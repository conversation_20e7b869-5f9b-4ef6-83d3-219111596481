/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        // Custom TravelAllinOne Colors
        'sky-blue': {
          DEFAULT: "hsl(var(--sky-blue))",
          dark: "hsl(var(--sky-blue-dark))",
        },
        'light-orange': {
          DEFAULT: "hsl(var(--light-orange))",
          dark: "hsl(var(--light-orange-dark))",
        },
        'travel-black': {
          DEFAULT: "hsl(var(--black))",
          light: "hsl(var(--black-light))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
      },
      animation: {
        'float': 'float 3s ease-in-out infinite',
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'slide-up': 'slideInUp 0.6s ease-out',
        'slide-left': 'slideInLeft 0.6s ease-out',
        'slide-right': 'slideInRight 0.6s ease-out',
      },
      backgroundImage: {
        'gradient-primary': 'var(--gradient-primary)',
        'gradient-secondary': 'var(--gradient-secondary)',
      },
      boxShadow: {
        'travel': '0 10px 25px rgba(0, 0, 0, 0.15)',
        'travel-lg': '0 20px 40px rgba(0, 0, 0, 0.1)',
        'sky-blue': '0 8px 20px rgba(135, 206, 235, 0.4)',
        'orange': '0 8px 20px rgba(255, 165, 0, 0.4)',
      },
    },
  },
  plugins: [],
};
