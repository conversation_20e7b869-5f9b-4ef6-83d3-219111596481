# TravelAllinOne Frontend

A modern, responsive web application built with Next.js 15, TypeScript, and Tailwind CSS for the TravelAllinOne travel booking platform.

## 🚀 Features

### Core Services
- **Bus Booking**: Search and book bus tickets across India (inspired by AbhiBus)
- **Car Pooling**: Share rides and split costs (inspired by BlaBlaCar)
- **Bike Rides**: Quick bike rides for short distances (inspired by Rapido)

### User Experience
- **Responsive Design**: Mobile-first approach with seamless desktop experience
- **Real-time Chat**: AI-powered chatbot for customer support
- **User Authentication**: Secure login/registration with JWT tokens
- **Profile Management**: Complete user profile and booking management
- **Payment Integration**: Razorpay payment gateway integration

### Technical Features
- **Modern Stack**: Next.js 15 with App Router, TypeScript, Tailwind CSS
- **Component Library**: shadcn/ui components for consistent design
- **State Management**: React hooks with local storage for auth
- **API Integration**: Axios-based API client with interceptors
- **Real-time Updates**: WebSocket support for live features

## 🛠️ Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS v4
- **UI Components**: shadcn/ui + Radix UI
- **Icons**: Lucide React
- **HTTP Client**: Axios
- **Form Handling**: React Hook Form + Zod validation
- **Notifications**: Sonner (toast notifications)

## 📦 Installation

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   ```

   Update `.env.local` with your configuration:
   ```env
   NEXT_PUBLIC_API_URL=http://localhost:8000
   ```

3. **Run the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3002](http://localhost:3002)

## 🏗️ Project Structure

```
frontend/
├── src/
│   ├── app/                    # Next.js App Router pages
│   │   ├── bus/               # Bus booking pages
│   │   ├── carpool/           # Car pooling pages
│   │   ├── bike/              # Bike ride pages
│   │   ├── login/             # Authentication pages
│   │   ├── register/
│   │   ├── profile/           # User profile
│   │   ├── layout.tsx         # Root layout
│   │   ├── page.tsx           # Home page
│   │   └── globals.css        # Global styles
│   ├── components/            # Reusable components
│   │   ├── ui/                # shadcn/ui components
│   │   ├── layout/            # Layout components
│   │   └── Chatbot.tsx        # AI chatbot component
│   └── lib/                   # Utilities and configurations
│       ├── api.ts             # API client and types
│       └── utils.ts           # Utility functions
├── public/                    # Static assets
├── components.json            # shadcn/ui configuration
├── tailwind.config.ts         # Tailwind CSS configuration
├── tsconfig.json             # TypeScript configuration
└── package.json              # Dependencies and scripts
```

## 🎨 Design System

### Color Palette
Our beautiful color scheme combines trust, energy, and elegance:

- **Sky Blue** (`#87CEEB` / `hsl(197, 71%, 73%)`) - Primary brand color representing trust and reliability
- **Light Orange** (`#FFD4B3` / `hsl(33, 100%, 85%)`) - Secondary color for energy and warmth
- **Travel Black** (`#1A1A1A` / `hsl(0, 0%, 10%)`) - Premium text and contrast
- **Gradients**: Beautiful combinations of sky blue and light orange for visual appeal

### Visual Features
- **Glass Morphism**: Frosted glass effects with backdrop blur
- **Smooth Animations**: Floating elements, slide-in effects, and hover transitions
- **Gradient Backgrounds**: Multi-layered gradients for depth and visual interest
- **Custom Shadows**: Sky blue and orange themed shadows for brand consistency

### Typography
- **Font**: Inter (primary), Geist Sans (fallback), system fonts
- **Scale**: Tailwind's default type scale with custom gradient text effects
- **Weights**: 400 (normal), 500 (medium), 600 (semibold), 700 (bold)

### Components
- **Cards**: Glass morphism effects with travel-themed shadows
- **Buttons**: Custom gradient buttons with hover lift effects
- **Forms**: Enhanced inputs with floating labels and focus states
- **Navigation**: Sticky header with backdrop blur and smooth transitions

### Custom CSS Classes
```css
.btn-primary          /* Sky blue gradient button */
.btn-secondary        /* Light orange gradient button */
.glass-effect         /* Frosted glass background */
.hover-lift           /* Smooth hover elevation */
.gradient-text        /* Multi-color gradient text */
.animate-float        /* Floating animation */
.service-card         /* Enhanced service cards */
.hero-section         /* Beautiful hero backgrounds */
```

## 🔌 API Integration

### Authentication
```typescript
// Login
const response = await authApi.login({ email, password });

// Register
const response = await authApi.register(userData);

// Get Profile
const response = await authApi.getProfile();
```

### Booking Services
```typescript
// Search buses
const response = await busApi.search(searchParams);

// Search carpool rides
const response = await carPoolApi.search(searchParams);

// Get bike ride estimate
const response = await bikeApi.getEstimate(rideData);
```

## 📱 Responsive Design

### Breakpoints
- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

### Mobile-First Approach
- Touch-friendly interface
- Optimized for mobile performance
- Progressive enhancement for larger screens

## 🚀 Performance Optimizations

- **Code Splitting**: Automatic route-based code splitting
- **Image Optimization**: Next.js Image component
- **Bundle Analysis**: Built-in bundle analyzer
- **Caching**: Efficient API response caching

## 📦 Build and Deployment

### Development Build
```bash
npm run dev
```

### Production Build
```bash
npm run build
npm run start
```

Built with ❤️ for travelers across India 🇮🇳
