import React from 'react';
import Link from 'next/link';
import { Bus, Car, Bike, Phone, Mail, MapPin } from 'lucide-react';

const Footer = () => {
  return (
    <footer className="footer text-white relative overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-br from-sky-blue/10 via-transparent to-light-orange/10"></div>
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center space-x-3 mb-4">
              <div className="v-logo">
                <span className="v-letter">V</span>
              </div>
              <span className="text-xl font-bold gradient-text">Vtravelallinone</span>
            </div>
            <p className="text-gray-300 mb-4 max-w-md">
              Your one-stop destination for all travel needs across India. 
              Book buses, share rides, and travel smart with TravelAllinOne.
            </p>
            <div className="flex space-x-4">
              <div className="flex items-center space-x-2 text-gray-300">
                <Phone className="w-4 h-4" />
                <span>+91 98765 43210</span>
              </div>
              <div className="flex items-center space-x-2 text-gray-300">
                <Mail className="w-4 h-4" />
                <span><EMAIL></span>
              </div>
            </div>
          </div>

          {/* Services */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Services</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/bus" className="flex items-center space-x-2 text-gray-300 hover:text-white transition-colors">
                  <Bus className="w-4 h-4" />
                  <span>Bus Booking</span>
                </Link>
              </li>
              <li>
                <Link href="/carpool" className="flex items-center space-x-2 text-gray-300 hover:text-white transition-colors">
                  <Car className="w-4 h-4" />
                  <span>Car Pooling</span>
                </Link>
              </li>
              <li>
                <Link href="/bike" className="flex items-center space-x-2 text-gray-300 hover:text-white transition-colors">
                  <Bike className="w-4 h-4" />
                  <span>Bike Rides</span>
                </Link>
              </li>
            </ul>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/about" className="text-gray-300 hover:text-white transition-colors">
                  About Us
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-gray-300 hover:text-white transition-colors">
                  Contact
                </Link>
              </li>
              <li>
                <Link href="/help" className="text-gray-300 hover:text-white transition-colors">
                  Help & Support
                </Link>
              </li>
              <li>
                <Link href="/privacy" className="text-gray-300 hover:text-white transition-colors">
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link href="/terms" className="text-gray-300 hover:text-white transition-colors">
                  Terms of Service
                </Link>
              </li>
            </ul>
          </div>
        </div>

        {/* Popular Routes */}
        <div className="mt-8 pt-8 border-t border-gray-800">
          <h3 className="text-lg font-semibold mb-4">Popular Routes</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div>
              <h4 className="font-medium mb-2">From Mumbai</h4>
              <ul className="space-y-1 text-sm text-gray-300">
                <li><Link href="/bus?from=mumbai&to=pune" className="hover:text-white">Mumbai to Pune</Link></li>
                <li><Link href="/bus?from=mumbai&to=nashik" className="hover:text-white">Mumbai to Nashik</Link></li>
                <li><Link href="/bus?from=mumbai&to=goa" className="hover:text-white">Mumbai to Goa</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">From Delhi</h4>
              <ul className="space-y-1 text-sm text-gray-300">
                <li><Link href="/bus?from=delhi&to=agra" className="hover:text-white">Delhi to Agra</Link></li>
                <li><Link href="/bus?from=delhi&to=jaipur" className="hover:text-white">Delhi to Jaipur</Link></li>
                <li><Link href="/bus?from=delhi&to=chandigarh" className="hover:text-white">Delhi to Chandigarh</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">From Bangalore</h4>
              <ul className="space-y-1 text-sm text-gray-300">
                <li><Link href="/bus?from=bangalore&to=mysore" className="hover:text-white">Bangalore to Mysore</Link></li>
                <li><Link href="/bus?from=bangalore&to=chennai" className="hover:text-white">Bangalore to Chennai</Link></li>
                <li><Link href="/bus?from=bangalore&to=hyderabad" className="hover:text-white">Bangalore to Hyderabad</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">From Chennai</h4>
              <ul className="space-y-1 text-sm text-gray-300">
                <li><Link href="/bus?from=chennai&to=pondicherry" className="hover:text-white">Chennai to Pondicherry</Link></li>
                <li><Link href="/bus?from=chennai&to=coimbatore" className="hover:text-white">Chennai to Coimbatore</Link></li>
                <li><Link href="/bus?from=chennai&to=madurai" className="hover:text-white">Chennai to Madurai</Link></li>
              </ul>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="mt-8 pt-8 border-t border-gray-800 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-300 text-sm">
            © 2024 TravelAllinOne. All rights reserved.
          </p>
          <div className="flex items-center space-x-4 mt-4 md:mt-0">
            <span className="text-gray-300 text-sm">Made with ❤️ in India</span>
            <div className="flex items-center space-x-1 text-gray-300">
              <MapPin className="w-4 h-4" />
              <span className="text-sm">🇮🇳</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
