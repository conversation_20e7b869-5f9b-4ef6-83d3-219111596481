import React from 'react';

interface AttractiveHighlightsProps {
  className?: string;
}

const AttractiveHighlights: React.FC<AttractiveHighlightsProps> = ({ className = '' }) => {
  return (
    <div className={`space-y-8 p-8 ${className}`}>
      {/* Hero Title Example */}
      <section className="text-center">
        <h1 className="title-hero">
          Welcome to Vtravelallinone
        </h1>
        <p className="text-lg text-white/80 max-w-2xl mx-auto">
          Experience the future of travel booking with our 
          <span className="highlight-important ml-2 mr-2">revolutionary platform</span>
          that combines all your travel needs in one place.
        </p>
      </section>

      {/* Section Titles */}
      <section>
        <h2 className="title-section">
          Our Amazing Services
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
          <div className="bg-card p-6 rounded-2xl">
            <h3 className="title-card attention-grabber">
              Bus Booking
              <span className="badge-new ml-2">New</span>
            </h3>
            <p className="text-white/70">
              Book comfortable intercity buses with 
              <span className="emphasis-strong">real-time tracking</span>
            </p>
          </div>
          
          <div className="bg-card p-6 rounded-2xl">
            <h3 className="title-card">
              Car Pool
              <span className="badge-hot ml-2">Hot</span>
            </h3>
            <p className="text-white/70">
              Share rides and 
              <span className="emphasis-success">save money</span>
              while making new friends
            </p>
          </div>
          
          <div className="bg-card p-6 rounded-2xl">
            <h3 className="title-card">
              Bike Rides
              <span className="badge-premium ml-2">Premium</span>
            </h3>
            <p className="text-white/70">
              Quick city commutes with our 
              <span className="emphasis-premium">premium bike service</span>
            </p>
          </div>
        </div>
      </section>

      {/* Important Highlights */}
      <section>
        <h2 className="title-section">
          Special Offers
        </h2>
        <div className="space-y-4">
          <div className="notification-highlight">
            <h4 className="font-bold mb-2">🎉 Limited Time Offer!</h4>
            <p>
              Get <span className="highlight-urgent">50% OFF</span> on your first booking 
              with code <span className="highlight-premium">WELCOME50</span>
            </p>
          </div>
          
          <div className="bg-card p-6 rounded-2xl">
            <p className="text-white/80">
              Join our <span className="highlight-success">Premium Membership</span> and enjoy:
            </p>
            <ul className="mt-4 space-y-2 text-white/70">
              <li className="flex items-center">
                <span className="emphasis-strong mr-2">✓</span>
                Priority booking access
              </li>
              <li className="flex items-center">
                <span className="emphasis-success mr-2">✓</span>
                Exclusive discounts up to 30%
              </li>
              <li className="flex items-center">
                <span className="emphasis-premium mr-2">✓</span>
                24/7 premium support
              </li>
            </ul>
          </div>
        </div>
      </section>

      {/* Gradient Text Examples */}
      <section>
        <h2 className="title-section">
          Experience the Future
        </h2>
        <div className="space-y-4 text-center">
          <p className="gradient-text-rainbow text-4xl font-bold">
            Travel in Style
          </p>
          <p className="gradient-text-electric text-2xl">
            Lightning Fast Bookings
          </p>
          <p className="gradient-text-laser text-xl">
            Eco-Friendly Options
          </p>
          <p className="gradient-text-plasma text-lg">
            Premium Comfort
          </p>
        </div>
      </section>

      {/* Interactive Elements */}
      <section>
        <h2 className="title-section">
          Take Action Now
        </h2>
        <div className="flex flex-wrap gap-4 justify-center">
          <button className="btn-primary pulse-glow">
            Book Your Trip
          </button>
          <button className="btn-secondary zoom-attention">
            Explore Services
          </button>
          <button className="btn-premium attention-grabber">
            Join Premium
          </button>
        </div>
      </section>

      {/* Status Badges */}
      <section>
        <h2 className="title-section">
          Live Updates
        </h2>
        <div className="flex flex-wrap gap-4 justify-center">
          <span className="badge-new">New Routes Available</span>
          <span className="badge-hot">Trending Destination</span>
          <span className="badge-premium">VIP Service</span>
          <span className="badge-limited">Limited Seats</span>
        </div>
      </section>

      {/* Emphasis Examples */}
      <section className="text-center">
        <h2 className="title-section">
          Why Choose Us?
        </h2>
        <div className="max-w-4xl mx-auto text-white/80 space-y-4">
          <p>
            We provide <span className="emphasis-strong">industry-leading</span> travel solutions 
            with <span className="emphasis-success">100% satisfaction guarantee</span>.
          </p>
          <p>
            Our <span className="emphasis-premium">premium platform</span> ensures 
            <span className="emphasis-urgent">instant confirmations</span> for all bookings.
          </p>
          <p className="highlight-important">
            Join over 1 million happy travelers who trust Vtravelallinone!
          </p>
        </div>
      </section>

      {/* Call to Action */}
      <section className="text-center">
        <div className="bg-gradient-rainbow p-8 rounded-3xl">
          <h2 className="text-4xl font-black text-black mb-4">
            Ready to Start Your Journey?
          </h2>
          <p className="text-xl text-black/80 mb-6">
            Download our app and get started in seconds!
          </p>
          <div className="flex flex-wrap gap-4 justify-center">
            <button className="btn-primary bounce-in">
              Download for iOS
            </button>
            <button className="btn-secondary bounce-in" style={{ animationDelay: '0.2s' }}>
              Download for Android
            </button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default AttractiveHighlights;
