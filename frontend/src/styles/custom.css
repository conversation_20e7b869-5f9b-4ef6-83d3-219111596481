/* TravelAllinOne Custom Styles */

/* Enhanced <PERSON><PERSON> */
.btn-travel {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.btn-travel::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-travel:hover::before {
  left: 100%;
}

/* Card Hover Effects */
.card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

/* Gradient Backgrounds */
.bg-travel-gradient {
  background: linear-gradient(135deg, 
    hsl(197, 71%, 73%) 0%, 
    hsl(33, 100%, 85%) 50%, 
    hsl(197, 71%, 83%) 100%);
}

.bg-travel-dark-gradient {
  background: linear-gradient(135deg, 
    hsl(0, 0%, 10%) 0%, 
    hsl(0, 0%, 20%) 50%, 
    hsl(0, 0%, 15%) 100%);
}

/* Text Animations */
.text-shimmer {
  background: linear-gradient(
    90deg,
    hsl(197, 71%, 73%) 0%,
    hsl(33, 100%, 85%) 50%,
    hsl(197, 71%, 73%) 100%
  );
  background-size: 200% 100%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% {
    background-position: 200% 0;
  }
  50% {
    background-position: -200% 0;
  }
}

/* Loading States */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(240, 5%, 96%);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, hsl(197, 71%, 73%), hsl(33, 100%, 85%));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, hsl(197, 71%, 63%), hsl(33, 100%, 75%));
}

/* Form Enhancements */
.form-group {
  position: relative;
}

.form-floating-label {
  position: absolute;
  top: 12px;
  left: 12px;
  color: hsl(0, 0%, 45%);
  transition: all 0.3s ease;
  pointer-events: none;
}

.form-input:focus + .form-floating-label,
.form-input:not(:placeholder-shown) + .form-floating-label {
  top: -8px;
  left: 8px;
  font-size: 12px;
  color: hsl(197, 71%, 73%);
  background: white;
  padding: 0 4px;
}

/* Service Icons */
.service-icon {
  background: linear-gradient(135deg, hsl(197, 71%, 73%), hsl(33, 100%, 85%));
  transition: all 0.3s ease;
}

.service-icon:hover {
  background: linear-gradient(135deg, hsl(197, 71%, 63%), hsl(33, 100%, 75%));
  transform: rotate(5deg) scale(1.1);
}

/* Navigation Enhancements */
.nav-link {
  position: relative;
  overflow: hidden;
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, hsl(197, 71%, 73%), hsl(33, 100%, 85%));
  transition: width 0.3s ease;
}

.nav-link:hover::after {
  width: 100%;
}

/* Modal and Dialog Enhancements */
.modal-backdrop {
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.modal-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Toast Notifications */
.toast-success {
  background: linear-gradient(135deg, hsl(142, 76%, 36%), hsl(142, 76%, 46%));
  color: white;
}

.toast-error {
  background: linear-gradient(135deg, hsl(0, 84%, 60%), hsl(0, 84%, 70%));
  color: white;
}

.toast-info {
  background: linear-gradient(135deg, hsl(197, 71%, 73%), hsl(197, 71%, 83%));
  color: white;
}

/* Search Results */
.search-result-item {
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
}

.search-result-item:hover {
  border-left-color: hsl(197, 71%, 73%);
  background: linear-gradient(90deg, rgba(197, 71%, 73%, 0.05), transparent);
  transform: translateX(4px);
}

/* Price Tags */
.price-tag {
  background: linear-gradient(135deg, hsl(142, 76%, 36%), hsl(142, 76%, 46%));
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 14px;
}

.price-tag-large {
  background: linear-gradient(135deg, hsl(197, 71%, 73%), hsl(33, 100%, 85%));
  color: hsl(0, 0%, 10%);
  padding: 8px 16px;
  border-radius: 25px;
  font-weight: 700;
  font-size: 18px;
}

/* Status Badges */
.status-confirmed {
  background: linear-gradient(135deg, hsl(142, 76%, 36%), hsl(142, 76%, 46%));
  color: white;
}

.status-pending {
  background: linear-gradient(135deg, hsl(33, 100%, 75%), hsl(33, 100%, 85%));
  color: hsl(0, 0%, 10%);
}

.status-cancelled {
  background: linear-gradient(135deg, hsl(0, 84%, 60%), hsl(0, 84%, 70%));
  color: white;
}

/* Mobile Optimizations */
@media (max-width: 768px) {
  .hero-section {
    padding: 3rem 1rem;
  }
  
  .service-card {
    margin-bottom: 1.5rem;
  }
  
  .btn-travel {
    padding: 12px 24px;
    font-size: 16px;
  }
  
  .text-shimmer {
    font-size: 2rem;
  }
}

/* Dark Mode Enhancements */
@media (prefers-color-scheme: dark) {
  .glass-effect {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .modal-content {
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  .hover-lift,
  .btn-travel,
  .service-icon,
  .card-hover,
  .search-result-item {
    transition: none;
  }
  
  .text-shimmer,
  .skeleton,
  .animate-float,
  .animate-pulse-slow {
    animation: none;
  }
}

/* Focus States */
.btn-travel:focus,
.form-input:focus {
  outline: 2px solid hsl(197, 71%, 73%);
  outline-offset: 2px;
}

/* Print Styles */
@media print {
  .hero-section,
  .bg-gradient-primary,
  .bg-gradient-secondary {
    background: white !important;
    color: black !important;
  }
  
  .shadow-travel,
  .shadow-travel-lg,
  .shadow-sky-blue,
  .shadow-orange {
    box-shadow: none !important;
  }
}
