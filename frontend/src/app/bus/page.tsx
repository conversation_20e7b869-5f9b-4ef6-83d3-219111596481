'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Bus, 
  MapPin, 
  Calendar, 
  Users, 
  Clock, 
  Star, 
  Wifi, 
  Zap, 
  Coffee,
  ArrowLeftRight,
  Search
} from 'lucide-react';
import { busApi } from '@/lib/api';
import { toast } from 'sonner';

const BusBookingPage = () => {
  const [searchData, setSearchData] = useState({
    source_city: '',
    destination_city: '',
    travel_date: '',
    passenger_count: 1,
    bus_type: ''
  });
  const [searchResults, setSearchResults] = useState([]);
  const [isSearching, setIsSearching] = useState(false);

  const popularRoutes = [
    { from: 'Mumbai', to: 'Pune', duration: '3h 30m', price: '₹450' },
    { from: 'Delhi', to: 'Agra', duration: '4h 15m', price: '₹380' },
    { from: 'Bangalore', to: 'Chennai', duration: '6h 45m', price: '₹650' },
    { from: 'Hyderabad', to: 'Vijayawada', duration: '4h 30m', price: '₹420' },
  ];

  const busTypes = [
    { value: 'regular', label: 'Regular' },
    { value: 'ac', label: 'AC' },
    { value: 'sleeper', label: 'Sleeper' },
    { value: 'luxury', label: 'Luxury' }
  ];

  const handleInputChange = (field: string, value: string | number) => {
    setSearchData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSearch = async () => {
    if (!searchData.source_city || !searchData.destination_city || !searchData.travel_date) {
      toast.error('Please fill in all required fields');
      return;
    }

    setIsSearching(true);
    try {
      const response = await busApi.search(searchData);
      if (response.data.success) {
        setSearchResults(response.data.data.buses || []);
        if (response.data.data.buses?.length === 0) {
          toast.info('No buses found for the selected route and date');
        }
      } else {
        toast.error('Search failed. Please try again.');
      }
    } catch (error: any) {
      console.error('Search error:', error);
      toast.error('Search failed. Please try again.');
    } finally {
      setIsSearching(false);
    }
  };

  const swapCities = () => {
    setSearchData(prev => ({
      ...prev,
      source_city: prev.destination_city,
      destination_city: prev.source_city
    }));
  };

  const BusCard = ({ bus }: { bus: any }) => {
    const amenityIcons: { [key: string]: any } = {
      wifi: Wifi,
      charging_point: Zap,
      water_bottle: Coffee,
    };

    return (
      <Card className="mb-4 hover:shadow-lg transition-shadow">
        <CardContent className="p-6">
          <div className="flex justify-between items-start mb-4">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <Bus className="w-5 h-5 text-blue-600" />
                <h3 className="text-lg font-semibold">{bus.bus.operator_name}</h3>
                <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                  {bus.bus.bus_type.toUpperCase()}
                </span>
              </div>
              <p className="text-gray-600 text-sm">{bus.bus.bus_number}</p>
              
              <div className="flex items-center gap-4 mt-3">
                <div className="flex items-center gap-1">
                  <Clock className="w-4 h-4 text-gray-500" />
                  <span className="text-sm">
                    {new Date(bus.route.departure_time).toLocaleTimeString('en-IN', { 
                      hour: '2-digit', 
                      minute: '2-digit' 
                    })}
                  </span>
                </div>
                <div className="text-gray-400">→</div>
                <div className="flex items-center gap-1">
                  <Clock className="w-4 h-4 text-gray-500" />
                  <span className="text-sm">
                    {new Date(bus.route.arrival_time).toLocaleTimeString('en-IN', { 
                      hour: '2-digit', 
                      minute: '2-digit' 
                    })}
                  </span>
                </div>
                <div className="text-sm text-gray-600">
                  ({Math.floor(bus.route.estimated_duration_minutes / 60)}h {bus.route.estimated_duration_minutes % 60}m)
                </div>
              </div>

              <div className="flex items-center gap-2 mt-2">
                {bus.bus.rating && (
                  <div className="flex items-center gap-1">
                    <Star className="w-4 h-4 text-yellow-500 fill-current" />
                    <span className="text-sm font-medium">{bus.bus.rating}</span>
                    <span className="text-xs text-gray-500">({bus.bus.reviews_count} reviews)</span>
                  </div>
                )}
              </div>

              <div className="flex items-center gap-2 mt-3">
                {bus.bus.amenities.map((amenity: string, index: number) => {
                  const IconComponent = amenityIcons[amenity] || Coffee;
                  return (
                    <div key={index} className="flex items-center gap-1 text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded">
                      <IconComponent className="w-3 h-3" />
                      <span>{amenity.replace('_', ' ')}</span>
                    </div>
                  );
                })}
              </div>
            </div>

            <div className="text-right">
              <div className="text-2xl font-bold text-green-600">
                ₹{Object.values(bus.pricing)[0]}
              </div>
              <div className="text-sm text-gray-500 mb-2">per person</div>
              <div className="text-sm text-gray-600 mb-3">
                {bus.bus.available_seats} seats left
              </div>
              <Button className="w-full">
                Select Seats
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50">
      {/* Hero Section with Search */}
      <section className="bg-travel-gradient text-white py-12 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-sky-blue/80 via-light-orange/60 to-sky-blue/80"></div>
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-8 animate-slide-up">
            <h1 className="text-3xl md:text-4xl font-bold mb-4 text-travel-black">
              🚌 Book Bus Tickets
            </h1>
            <p className="text-xl text-travel-black opacity-90">
              Travel comfortably across India with verified bus operators
            </p>
          </div>

          <Card className="max-w-4xl mx-auto shadow-travel-lg glass-effect animate-slide-up" style={{animationDelay: '0.2s'}}>
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                <div className="space-y-2">
                  <Label htmlFor="from" className="text-travel-black font-medium">From</Label>
                  <div className="relative">
                    <MapPin className="absolute left-3 top-3 h-4 w-4 text-sky-blue" />
                    <Input
                      id="from"
                      placeholder="Departure city"
                      value={searchData.source_city}
                      onChange={(e) => handleInputChange('source_city', e.target.value)}
                      className="pl-10 form-input"
                    />
                  </div>
                </div>

                <div className="flex justify-center md:col-span-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={swapCities}
                    className="p-2 hover:bg-gray-100 rounded-full mt-6"
                  >
                    <ArrowLeftRight className="w-4 h-4" />
                  </Button>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="to">To</Label>
                  <div className="relative">
                    <MapPin className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="to"
                      placeholder="Destination city"
                      value={searchData.destination_city}
                      onChange={(e) => handleInputChange('destination_city', e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="date">Travel Date</Label>
                  <div className="relative">
                    <Calendar className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="date"
                      type="date"
                      value={searchData.travel_date}
                      onChange={(e) => handleInputChange('travel_date', e.target.value)}
                      className="pl-10"
                      min={new Date().toISOString().split('T')[0]}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="passengers">Passengers</Label>
                  <div className="relative">
                    <Users className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Select 
                      value={searchData.passenger_count.toString()} 
                      onValueChange={(value) => handleInputChange('passenger_count', parseInt(value))}
                    >
                      <SelectTrigger className="pl-10">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {[1, 2, 3, 4, 5, 6].map(num => (
                          <SelectItem key={num} value={num.toString()}>
                            {num} {num === 1 ? 'Passenger' : 'Passengers'}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              <div className="flex justify-between items-center mt-6">
                <div className="flex gap-4">
                  <Select 
                    value={searchData.bus_type} 
                    onValueChange={(value) => handleInputChange('bus_type', value)}
                  >
                    <SelectTrigger className="w-40">
                      <SelectValue placeholder="Bus Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">All Types</SelectItem>
                      {busTypes.map(type => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <Button
                  onClick={handleSearch}
                  disabled={isSearching}
                  className="px-8 btn-primary hover-lift shadow-sky-blue"
                >
                  <Search className="w-4 h-4 mr-2" />
                  {isSearching ? (
                    <>
                      <div className="loading-spinner mr-2"></div>
                      Searching...
                    </>
                  ) : (
                    '🔍 Search Buses'
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Results Section */}
      <section className="py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {searchResults.length > 0 ? (
            <div>
              <h2 className="text-2xl font-bold mb-6">
                Available Buses ({searchResults.length} found)
              </h2>
              <div className="grid grid-cols-1 gap-4">
                {searchResults.map((bus: any, index: number) => (
                  <BusCard key={index} bus={bus} />
                ))}
              </div>
            </div>
          ) : (
            <div>
              <h2 className="text-2xl font-bold mb-6">Popular Routes</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {popularRoutes.map((route, index) => (
                  <Card key={index} className="hover:shadow-lg transition-shadow cursor-pointer">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium">{route.from}</span>
                        <ArrowLeftRight className="w-4 h-4 text-gray-400" />
                        <span className="font-medium">{route.to}</span>
                      </div>
                      <div className="flex justify-between text-sm text-gray-600">
                        <span>{route.duration}</span>
                        <span className="font-semibold text-green-600">{route.price}</span>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}
        </div>
      </section>
    </div>
  );
};

export default BusBookingPage;
