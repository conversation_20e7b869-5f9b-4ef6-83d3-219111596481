@import "tailwindcss";

/* Vtravelallinone Enhanced Color Palette - Premium Edition */
:root {
  /* Enhanced Sophisticated Color Scheme */
  --background: #fafbff;
  --foreground: #1e293b;
  --card: #ffffff;
  --card-foreground: #1e293b;
  --popover: #ffffff;
  --popover-foreground: #1e293b;
  --primary: #0a3d62; /* Enhanced Deep Ocean */
  --primary-foreground: #ffffff;
  --secondary: #2980b9; /* Enhanced Ocean Teal */
  --secondary-foreground: #ffffff;
  --muted: #f1f5f9;
  --muted-foreground: #64748b;
  --accent: #e74c3c; /* Enhanced Coral */
  --accent-foreground: #ffffff;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #e2e8f0;
  --input: #e2e8f0;
  --ring: #2980b9;
  --radius: 0.75rem;

  /* Premium Color Palette - Primary Layer */
  --deep-ocean: #0a3d62;        /* Enhanced Deep Ocean Blue */
  --ocean-teal: #2980b9;        /* Enhanced Ocean Teal */
  --coral-sunset: #e74c3c;      /* Enhanced Coral Sunset */
  --golden-hour: #f39c12;       /* Enhanced Golden Hour */
  --royal-purple: #8e44ad;      /* Enhanced Royal Purple */
  --emerald-green: #27ae60;     /* Enhanced Emerald */
  --sunset-orange: #e67e22;     /* Enhanced Sunset Orange */
  --midnight-blue: #2c3e50;     /* Enhanced Midnight */

  /* Secondary Layer Colors - Sophisticated Variations */
  --ocean-depth: #1e3a8a;       /* Deeper Ocean Variant */
  --teal-mist: #0891b2;         /* Teal Mist */
  --coral-bloom: #f87171;       /* Coral Bloom */
  --amber-glow: #fbbf24;        /* Amber Glow */
  --violet-dream: #a855f7;      /* Violet Dream */
  --mint-fresh: #10b981;        /* Mint Fresh */
  --peach-sunset: #fb923c;      /* Peach Sunset */
  --slate-storm: #475569;       /* Slate Storm */

  /* Tertiary Layer Colors - Accent Variations */
  --azure-sky: #3b82f6;         /* Azure Sky */
  --turquoise-wave: #06b6d4;    /* Turquoise Wave */
  --rose-garden: #f43f5e;       /* Rose Garden */
  --champagne-gold: #eab308;    /* Champagne Gold */
  --lavender-mist: #c084fc;     /* Lavender Mist */
  --seafoam-green: #34d399;     /* Seafoam Green */
  --apricot-blush: #fdba74;     /* Apricot Blush */
  --charcoal-grey: #6b7280;     /* Charcoal Grey */

  /* Gradient Enhancement Colors */
  --gradient-start: #0a3d62;    /* Enhanced gradient start */
  --gradient-mid: #2980b9;      /* Enhanced gradient middle */
  --gradient-end: #e74c3c;      /* Enhanced gradient end */
  --gradient-accent: #f39c12;   /* Enhanced gradient accent */

  /* Neutral Enhanced Palette */
  --neutral-50: #f8fafc;
  --neutral-100: #f1f5f9;
  --neutral-200: #e2e8f0;
  --neutral-300: #cbd5e1;
  --neutral-400: #94a3b8;
  --neutral-500: #64748b;
  --neutral-600: #475569;
  --neutral-700: #334155;
  --neutral-800: #1e293b;
  --neutral-900: #0f172a;
}

.dark {
  --background: #0f172a;
  --foreground: #f1f5f9;
  --card: #1e293b;
  --card-foreground: #f1f5f9;
  --popover: #1e293b;
  --popover-foreground: #f1f5f9;
  --primary: #2980b9;
  --primary-foreground: #ffffff;
  --secondary: #0a3d62;
  --secondary-foreground: #ffffff;
  --muted: #334155;
  --muted-foreground: #94a3b8;
  --accent: #e74c3c;
  --accent-foreground: #ffffff;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #475569;
  --input: #475569;
  --ring: #2980b9;

  /* Enhanced Dark Mode Colors */
  --deep-ocean: #2980b9;        /* Lighter for dark mode */
  --ocean-teal: #3498db;        /* Brighter teal for dark */
  --coral-sunset: #e74c3c;      /* Vibrant coral for dark */
  --golden-hour: #f39c12;       /* Warm gold for dark */
  --royal-purple: #9b59b6;      /* Rich purple for dark */
  --emerald-green: #2ecc71;     /* Bright emerald for dark */
}

* {
  border-color: var(--border);
}

body {
  background-color: var(--background);
  color: var(--foreground);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

html {
  scroll-behavior: smooth;
}

/* Enhanced Premium Gradient Utility Classes */
.bg-gradient-ocean {
  background: linear-gradient(135deg, var(--deep-ocean) 0%, var(--ocean-teal) 50%, var(--azure-sky) 100%);
}

.bg-gradient-sunset {
  background: linear-gradient(135deg, var(--coral-sunset) 0%, var(--sunset-orange) 50%, var(--golden-hour) 100%);
}

.bg-gradient-royal {
  background: linear-gradient(135deg, var(--royal-purple) 0%, var(--violet-dream) 50%, var(--lavender-mist) 100%);
}

.bg-gradient-emerald {
  background: linear-gradient(135deg, var(--emerald-green) 0%, var(--mint-fresh) 50%, var(--seafoam-green) 100%);
}

.bg-gradient-hero {
  background: linear-gradient(135deg, var(--deep-ocean) 0%, var(--ocean-teal) 25%, var(--coral-sunset) 75%, var(--golden-hour) 100%);
}

.bg-gradient-premium {
  background: linear-gradient(135deg, var(--midnight-blue) 0%, var(--royal-purple) 50%, var(--rose-garden) 100%);
}

.bg-gradient-warm {
  background: linear-gradient(135deg, var(--peach-sunset) 0%, var(--apricot-blush) 50%, var(--champagne-gold) 100%);
}

.bg-gradient-cool {
  background: linear-gradient(135deg, var(--teal-mist) 0%, var(--turquoise-wave) 50%, var(--azure-sky) 100%);
}

.bg-gradient-vibrant {
  background: linear-gradient(135deg, var(--coral-bloom) 0%, var(--rose-garden) 50%, var(--violet-dream) 100%);
}

.bg-gradient-nature {
  background: linear-gradient(135deg, var(--seafoam-green) 0%, var(--mint-fresh) 50%, var(--emerald-green) 100%);
}

/* Multi-layer Gradients */
.bg-gradient-aurora {
  background: linear-gradient(135deg,
    var(--deep-ocean) 0%,
    var(--royal-purple) 25%,
    var(--coral-sunset) 50%,
    var(--golden-hour) 75%,
    var(--emerald-green) 100%);
}

.bg-gradient-twilight {
  background: linear-gradient(135deg,
    var(--midnight-blue) 0%,
    var(--ocean-depth) 30%,
    var(--violet-dream) 70%,
    var(--lavender-mist) 100%);
}

.bg-gradient-sunrise {
  background: linear-gradient(135deg,
    var(--coral-sunset) 0%,
    var(--sunset-orange) 25%,
    var(--golden-hour) 50%,
    var(--champagne-gold) 75%,
    var(--amber-glow) 100%);
}

/* Enhanced Premium Color Utilities */

/* Primary Layer Text Colors */
.text-ocean {
  color: var(--deep-ocean);
}

.text-teal {
  color: var(--ocean-teal);
}

.text-coral {
  color: var(--coral-sunset);
}

.text-golden {
  color: var(--golden-hour);
}

.text-purple {
  color: var(--royal-purple);
}

.text-emerald {
  color: var(--emerald-green);
}

/* Secondary Layer Text Colors */
.text-azure {
  color: var(--azure-sky);
}

.text-turquoise {
  color: var(--turquoise-wave);
}

.text-rose {
  color: var(--rose-garden);
}

.text-champagne {
  color: var(--champagne-gold);
}

.text-lavender {
  color: var(--lavender-mist);
}

.text-seafoam {
  color: var(--seafoam-green);
}

/* Primary Layer Background Colors */
.bg-ocean {
  background-color: var(--deep-ocean);
}

.bg-teal {
  background-color: var(--ocean-teal);
}

.bg-coral {
  background-color: var(--coral-sunset);
}

.bg-golden {
  background-color: var(--golden-hour);
}

.bg-purple {
  background-color: var(--royal-purple);
}

.bg-emerald {
  background-color: var(--emerald-green);
}

/* Secondary Layer Background Colors */
.bg-azure {
  background-color: var(--azure-sky);
}

.bg-turquoise {
  background-color: var(--turquoise-wave);
}

.bg-rose {
  background-color: var(--rose-garden);
}

.bg-champagne {
  background-color: var(--champagne-gold);
}

.bg-lavender {
  background-color: var(--lavender-mist);
}

.bg-seafoam {
  background-color: var(--seafoam-green);
}

/* Enhanced Border Colors */
.border-ocean {
  border-color: var(--deep-ocean);
}

.border-teal {
  border-color: var(--ocean-teal);
}

.border-coral {
  border-color: var(--coral-sunset);
}

.border-golden {
  border-color: var(--golden-hour);
}

.border-purple {
  border-color: var(--royal-purple);
}

.border-emerald {
  border-color: var(--emerald-green);
}

.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Enhanced Premium Gradient Text Classes */
.gradient-text {
  background: linear-gradient(135deg, var(--deep-ocean) 0%, var(--ocean-teal) 30%, var(--coral-sunset) 70%, var(--golden-hour) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

.gradient-text-sunset {
  background: linear-gradient(135deg, var(--coral-sunset) 0%, var(--sunset-orange) 50%, var(--golden-hour) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

.gradient-text-ocean {
  background: linear-gradient(135deg, var(--deep-ocean) 0%, var(--ocean-teal) 50%, var(--azure-sky) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

.gradient-text-royal {
  background: linear-gradient(135deg, var(--royal-purple) 0%, var(--violet-dream) 50%, var(--lavender-mist) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

.gradient-text-emerald {
  background: linear-gradient(135deg, var(--emerald-green) 0%, var(--mint-fresh) 50%, var(--seafoam-green) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

.gradient-text-aurora {
  background: linear-gradient(135deg,
    var(--deep-ocean) 0%,
    var(--royal-purple) 25%,
    var(--coral-sunset) 50%,
    var(--golden-hour) 75%,
    var(--emerald-green) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800;
}

.gradient-text-premium {
  background: linear-gradient(135deg, var(--midnight-blue) 0%, var(--royal-purple) 50%, var(--rose-garden) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

/* Custom Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-slide-up {
  animation: slideInUp 0.6s ease-out;
}

.animate-slide-left {
  animation: slideInLeft 0.6s ease-out;
}

.animate-slide-right {
  animation: slideInRight 0.6s ease-out;
}

/* Modern Component Styles */
.hero-section {
  background: linear-gradient(135deg, var(--deep-ocean) 0%, var(--ocean-teal) 30%, var(--coral-sunset) 70%, var(--golden-hour) 100%);
  position: relative;
  overflow: hidden;
}

/* Enhanced Premium V Logo Styles */
.v-logo {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 56px;
  height: 56px;
  border-radius: 18px;
  background: linear-gradient(135deg,
    var(--deep-ocean) 0%,
    var(--ocean-teal) 25%,
    var(--coral-sunset) 75%,
    var(--golden-hour) 100%);
  box-shadow:
    0 8px 32px rgba(10, 61, 98, 0.25),
    0 4px 16px rgba(41, 128, 185, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.v-logo:hover {
  transform: translateY(-4px) scale(1.08) rotate(2deg);
  box-shadow:
    0 16px 48px rgba(10, 61, 98, 0.35),
    0 8px 24px rgba(41, 128, 185, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.v-logo::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    rgba(255, 231, 109, 0.2),
    transparent);
  transform: rotate(45deg);
  transition: all 1s ease;
  opacity: 0;
}

.v-logo:hover::before {
  animation: premiumShimmer 2.5s ease-in-out;
}

.v-logo-large {
  width: 72px;
  height: 72px;
  border-radius: 22px;
}

.v-logo-large .v-letter {
  font-size: 36px;
}

.v-logo-xl {
  width: 96px;
  height: 96px;
  border-radius: 28px;
}

.v-logo-xl .v-letter {
  font-size: 48px;
}

.v-letter {
  font-size: 28px;
  font-weight: 900;
  color: white;
  text-shadow:
    0 2px 4px rgba(0, 0, 0, 0.3),
    0 4px 8px rgba(0, 0, 0, 0.2),
    0 1px 0 rgba(255, 255, 255, 0.1);
  z-index: 2;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  letter-spacing: -0.02em;
}

/* Premium Logo Variants */
.v-logo-premium {
  background: linear-gradient(135deg,
    var(--midnight-blue) 0%,
    var(--royal-purple) 30%,
    var(--rose-garden) 70%,
    var(--champagne-gold) 100%);
  box-shadow:
    0 12px 40px rgba(44, 62, 80, 0.3),
    0 6px 20px rgba(142, 68, 173, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.25);
}

.v-logo-nature {
  background: linear-gradient(135deg,
    var(--emerald-green) 0%,
    var(--mint-fresh) 50%,
    var(--seafoam-green) 100%);
  box-shadow:
    0 8px 32px rgba(39, 174, 96, 0.25),
    0 4px 16px rgba(16, 185, 129, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.v-logo-sunset {
  background: linear-gradient(135deg,
    var(--coral-sunset) 0%,
    var(--sunset-orange) 50%,
    var(--golden-hour) 100%);
  box-shadow:
    0 8px 32px rgba(231, 76, 60, 0.25),
    0 4px 16px rgba(230, 126, 34, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

@keyframes modernShimmer {
  0% {
    transform: translateX(-100%) rotate(45deg);
    opacity: 0;
  }
  30% {
    opacity: 0.6;
  }
  70% {
    opacity: 0.8;
  }
  100% {
    transform: translateX(100%) rotate(45deg);
    opacity: 0;
  }
}

@keyframes premiumShimmer {
  0% {
    transform: translateX(-120%) rotate(45deg);
    opacity: 0;
  }
  20% {
    opacity: 0.4;
  }
  50% {
    opacity: 0.8;
    transform: translateX(0%) rotate(45deg);
  }
  80% {
    opacity: 0.6;
  }
  100% {
    transform: translateX(120%) rotate(45deg);
    opacity: 0;
  }
}

@keyframes pulseGlow {
  0%, 100% {
    box-shadow:
      0 8px 32px rgba(10, 61, 98, 0.25),
      0 4px 16px rgba(41, 128, 185, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }
  50% {
    box-shadow:
      0 12px 48px rgba(10, 61, 98, 0.4),
      0 6px 24px rgba(41, 128, 185, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }
}

@keyframes floatAnimation {
  0%, 100% {
    transform: translateY(0px) scale(1);
  }
  50% {
    transform: translateY(-6px) scale(1.02);
  }
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
  opacity: 0.3;
}

.service-card {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(24px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 24px;
  box-shadow:
    0 8px 32px rgba(10, 61, 98, 0.08),
    0 4px 16px rgba(41, 128, 185, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  position: relative;
  overflow: hidden;
}

.service-card:hover {
  transform: translateY(-16px) scale(1.03) rotate(1deg);
  box-shadow:
    0 32px 64px rgba(10, 61, 98, 0.15),
    0 16px 32px rgba(41, 128, 185, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  background: rgba(255, 255, 255, 1);
  border-color: var(--ocean-teal);
}

.service-card-bus {
  background: linear-gradient(135deg,
    var(--deep-ocean) 0%,
    var(--ocean-teal) 50%,
    var(--azure-sky) 100%);
  color: white;
  box-shadow:
    0 12px 40px rgba(10, 61, 98, 0.25),
    0 6px 20px rgba(41, 128, 185, 0.15);
}

.service-card-carpool {
  background: linear-gradient(135deg,
    var(--emerald-green) 0%,
    var(--mint-fresh) 50%,
    var(--seafoam-green) 100%);
  color: white;
  box-shadow:
    0 12px 40px rgba(39, 174, 96, 0.25),
    0 6px 20px rgba(16, 185, 129, 0.15);
}

.service-card-bike {
  background: linear-gradient(135deg,
    var(--coral-sunset) 0%,
    var(--sunset-orange) 50%,
    var(--golden-hour) 100%);
  color: white;
  box-shadow:
    0 12px 40px rgba(231, 76, 60, 0.25),
    0 6px 20px rgba(230, 126, 34, 0.15);
}

.service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent);
  transition: left 0.8s ease;
}

.service-card:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(135deg,
    var(--deep-ocean) 0%,
    var(--ocean-teal) 50%,
    var(--azure-sky) 100%);
  border: none;
  color: white;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 16px;
  padding: 16px 32px;
  font-weight: 600;
  font-size: 16px;
  box-shadow:
    0 6px 20px rgba(10, 61, 98, 0.25),
    0 3px 10px rgba(41, 128, 185, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.btn-primary:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow:
    0 12px 32px rgba(10, 61, 98, 0.35),
    0 6px 16px rgba(41, 128, 185, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  background: linear-gradient(135deg,
    var(--azure-sky) 0%,
    var(--ocean-teal) 50%,
    var(--deep-ocean) 100%);
}

.btn-secondary {
  background: linear-gradient(135deg,
    var(--coral-sunset) 0%,
    var(--sunset-orange) 50%,
    var(--golden-hour) 100%);
  border: none;
  color: white;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 16px;
  padding: 16px 32px;
  font-weight: 600;
  font-size: 16px;
  box-shadow:
    0 6px 20px rgba(231, 76, 60, 0.25),
    0 3px 10px rgba(230, 126, 34, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.btn-secondary:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow:
    0 12px 32px rgba(231, 76, 60, 0.35),
    0 6px 16px rgba(230, 126, 34, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  background: linear-gradient(135deg,
    var(--golden-hour) 0%,
    var(--sunset-orange) 50%,
    var(--coral-sunset) 100%);
}

.btn-premium {
  background: linear-gradient(135deg,
    var(--royal-purple) 0%,
    var(--violet-dream) 50%,
    var(--lavender-mist) 100%);
  border: none;
  color: white;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 16px;
  padding: 16px 32px;
  font-weight: 600;
  font-size: 16px;
  box-shadow:
    0 6px 20px rgba(142, 68, 173, 0.25),
    0 3px 10px rgba(168, 85, 247, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.btn-premium:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow:
    0 12px 32px rgba(142, 68, 173, 0.35),
    0 6px 16px rgba(168, 85, 247, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.btn-outline {
  background: transparent;
  border: 2px solid var(--ocean-teal);
  color: var(--ocean-teal);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 16px;
  padding: 14px 30px;
  font-weight: 600;
  font-size: 16px;
  position: relative;
  overflow: hidden;
}

.btn-outline:hover {
  background: linear-gradient(135deg, var(--ocean-teal) 0%, var(--azure-sky) 100%);
  color: white;
  transform: translateY(-3px) scale(1.02);
  box-shadow:
    0 8px 24px rgba(41, 128, 185, 0.3),
    0 4px 12px rgba(59, 130, 246, 0.2);
  border-color: var(--azure-sky);
}

/* Button shine effect */
.btn-primary::before,
.btn-secondary::before,
.btn-premium::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent);
  transition: left 0.6s ease;
}

.btn-primary:hover::before,
.btn-secondary:hover::before,
.btn-premium:hover::before {
  left: 100%;
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--sky-blue-dark) 0%, #4a9bc7 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(135, 206, 235, 0.4);
  color: white;
}

.btn-secondary {
  background: linear-gradient(135deg, var(--light-orange) 0%, var(--light-orange-dark) 100%);
  border: none;
  color: var(--travel-black);
  transition: all 0.3s ease;
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 600;
}

.btn-secondary:hover {
  background: linear-gradient(135deg, var(--light-orange-dark) 0%, #ff9f4d 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(255, 165, 0, 0.4);
  color: var(--travel-black);
}

.navbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(135, 206, 235, 0.2);
}

.footer {
  background: linear-gradient(135deg, var(--travel-black) 0%, var(--travel-black-light) 100%);
}

/* Form Styles */
.form-input {
  border: 2px solid var(--border);
  transition: all 0.3s ease;
  border-radius: 8px;
}

.form-input:focus {
  border-color: var(--sky-blue);
  box-shadow: 0 0 0 3px rgba(135, 206, 235, 0.1);
  outline: none;
}

/* Enhanced Card Styles */
.shadow-travel {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.shadow-travel-lg {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.shadow-sky-blue {
  box-shadow: 0 8px 20px rgba(135, 206, 235, 0.4);
}

.shadow-orange {
  box-shadow: 0 8px 20px rgba(255, 165, 0, 0.4);
}

/* Travel Gradient Backgrounds */
.bg-travel-gradient {
  background: linear-gradient(135deg, var(--sky-blue) 0%, var(--light-orange) 50%, var(--sky-blue) 100%);
}

/* Unique V Logo Styles */
.v-logo {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--sky-blue) 0%, var(--light-orange) 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: 0 8px 20px rgba(135, 206, 235, 0.4);
  transition: all 0.3s ease;
  overflow: hidden;
}

.v-logo::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s;
}

.v-logo:hover::before {
  left: 100%;
}

.v-logo:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 12px 30px rgba(135, 206, 235, 0.6);
}

.v-letter {
  font-size: 18px;
  font-weight: 900;
  color: white;
  font-family: 'Inter', sans-serif;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  position: relative;
  z-index: 1;
}

/* Large V Logo for Login/Register Pages */
.v-logo-large {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--sky-blue) 0%, var(--light-orange) 50%, var(--sky-blue) 100%);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: 0 15px 35px rgba(135, 206, 235, 0.4);
  transition: all 0.4s ease;
  overflow: hidden;
  animation: logoFloat 3s ease-in-out infinite;
}

.v-logo-large::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.8s;
}

.v-logo-large:hover::before {
  left: 100%;
}

.v-logo-large:hover {
  transform: translateY(-4px) scale(1.08);
  box-shadow: 0 20px 45px rgba(135, 206, 235, 0.6);
}

.v-letter-large {
  font-size: 36px;
  font-weight: 900;
  color: white;
  font-family: 'Inter', sans-serif;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  position: relative;
  z-index: 1;
}

@keyframes logoFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
}

/* V Logo Variants */
.v-logo-minimal {
  width: 32px;
  height: 32px;
  background: linear-gradient(45deg, var(--sky-blue), var(--light-orange));
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(135, 206, 235, 0.3);
}

.v-logo-minimal .v-letter {
  font-size: 14px;
  font-weight: 800;
}

/* V Logo with Glow Effect */
.v-logo-glow {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--sky-blue) 0%, var(--light-orange) 100%);
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow:
    0 0 20px rgba(135, 206, 235, 0.5),
    0 8px 20px rgba(135, 206, 235, 0.3);
  animation: logoGlow 2s ease-in-out infinite alternate;
}

.v-logo-glow .v-letter {
  font-size: 20px;
  font-weight: 900;
  color: white;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

@keyframes logoGlow {
  0% {
    box-shadow:
      0 0 20px rgba(135, 206, 235, 0.5),
      0 8px 20px rgba(135, 206, 235, 0.3);
  }
  100% {
    box-shadow:
      0 0 30px rgba(135, 206, 235, 0.8),
      0 12px 30px rgba(135, 206, 235, 0.5);
  }
}

/* Loading Animations */
.loading-spinner {
  border: 3px solid hsl(var(--muted));
  border-top: 3px solid hsl(var(--sky-blue));
  border-radius: 50%;
  width: 24px;
  height: 24px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design Enhancements */
@media (max-width: 768px) {
  .hero-section {
    padding: 2rem 1rem;
  }

  .service-card {
    margin-bottom: 1rem;
  }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  .hover-lift,
  .btn-primary,
  .btn-secondary,
  .service-card {
    transition: none;
  }

  .animate-float,
  .animate-pulse-slow,
  .animate-slide-up,
  .animate-slide-left,
  .animate-slide-right {
    animation: none;
  }
}
