@import "tailwindcss";

/* Vtravelallinone Bright Dark-Optimized Color Palette */
:root {
  /* Bright Dark-Optimized Color Scheme */
  --background: #0a0a0f;
  --foreground: #ffffff;
  --card: #1a1a2e;
  --card-foreground: #ffffff;
  --popover: #1a1a2e;
  --popover-foreground: #ffffff;
  --primary: #00d4ff; /* <PERSON> */
  --primary-foreground: #000000;
  --secondary: #ff3366; /* Bright Pink */
  --secondary-foreground: #ffffff;
  --muted: #2a2a3e;
  --muted-foreground: #a0a0b0;
  --accent: #ffff00; /* Bright Yellow */
  --accent-foreground: #000000;
  --destructive: #ff4757;
  --destructive-foreground: #ffffff;
  --border: #3a3a4e;
  --input: #2a2a3e;
  --ring: #00d4ff;
  --radius: 0.75rem;

  /* Bright Primary Layer - Neon & Electric Colors */
  --electric-cyan: #00d4ff;      /* Electric Cyan Blue */
  --neon-pink: #ff3366;          /* Neon Pink */
  --laser-green: #00ff88;        /* Laser Green */
  --solar-yellow: #ffff00;       /* Solar Yellow */
  --plasma-purple: #aa44ff;      /* Plasma Purple */
  --fire-orange: #ff6600;        /* Fire Orange */
  --ice-blue: #44ddff;           /* Ice Blue */
  --volt-lime: #88ff00;          /* Volt Lime */

  /* Bright Secondary Layer - Vibrant Variations */
  --cyber-blue: #0099ff;         /* Cyber Blue */
  --hot-magenta: #ff0066;        /* Hot Magenta */
  --toxic-green: #66ff00;        /* Toxic Green */
  --gold-rush: #ffcc00;          /* Gold Rush */
  --royal-violet: #6600ff;       /* Royal Violet */
  --sunset-red: #ff3300;         /* Sunset Red */
  --aqua-bright: #00ffcc;        /* Aqua Bright */
  --electric-lime: #ccff00;      /* Electric Lime */

  /* Bright Tertiary Layer - Accent Highlights */
  --neon-blue: #0066ff;          /* Neon Blue */
  --cherry-pop: #ff0033;         /* Cherry Pop */
  --mint-electric: #00ff99;      /* Mint Electric */
  --banana-bright: #ffff33;      /* Banana Bright */
  --grape-neon: #9933ff;         /* Grape Neon */
  --coral-bright: #ff6633;       /* Coral Bright */
  --turbo-teal: #00ccff;         /* Turbo Teal */
  --lime-shock: #99ff33;         /* Lime Shock */

  /* Gradient Enhancement Colors */
  --gradient-start: #0a3d62;    /* Enhanced gradient start */
  --gradient-mid: #2980b9;      /* Enhanced gradient middle */
  --gradient-end: #e74c3c;      /* Enhanced gradient end */
  --gradient-accent: #f39c12;   /* Enhanced gradient accent */

  /* Neutral Enhanced Palette */
  --neutral-50: #f8fafc;
  --neutral-100: #f1f5f9;
  --neutral-200: #e2e8f0;
  --neutral-300: #cbd5e1;
  --neutral-400: #94a3b8;
  --neutral-500: #64748b;
  --neutral-600: #475569;
  --neutral-700: #334155;
  --neutral-800: #1e293b;
  --neutral-900: #0f172a;
}

.dark {
  --background: #0a0a0f;
  --foreground: #ffffff;
  --card: #1a1a2e;
  --card-foreground: #ffffff;
  --popover: #1a1a2e;
  --popover-foreground: #ffffff;
  --primary: #00d4ff;
  --primary-foreground: #000000;
  --secondary: #ff3366;
  --secondary-foreground: #ffffff;
  --muted: #2a2a3e;
  --muted-foreground: #a0a0b0;
  --accent: #ffff00;
  --accent-foreground: #000000;
  --destructive: #ff4757;
  --destructive-foreground: #ffffff;
  --border: #3a3a4e;
  --input: #2a2a3e;
  --ring: #00d4ff;

  /* Bright Dark Mode Colors - Maximum Visibility */
  --electric-cyan: #00ffff;      /* Maximum cyan brightness */
  --neon-pink: #ff0080;          /* Maximum pink brightness */
  --laser-green: #00ff00;        /* Maximum green brightness */
  --solar-yellow: #ffff00;       /* Maximum yellow brightness */
  --plasma-purple: #8000ff;      /* Maximum purple brightness */
  --fire-orange: #ff4000;        /* Maximum orange brightness */
  --ice-blue: #40e0ff;           /* Maximum ice blue brightness */
  --volt-lime: #80ff00;          /* Maximum lime brightness */
}

* {
  border-color: var(--border);
}

body {
  background-color: var(--background);
  color: var(--foreground);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

html {
  scroll-behavior: smooth;
}

/* Bright Neon Gradient Utility Classes */
.bg-gradient-electric {
  background: linear-gradient(135deg, var(--electric-cyan) 0%, var(--ice-blue) 50%, var(--turbo-teal) 100%);
  box-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
}

.bg-gradient-neon {
  background: linear-gradient(135deg, var(--neon-pink) 0%, var(--hot-magenta) 50%, var(--cherry-pop) 100%);
  box-shadow: 0 0 30px rgba(255, 51, 102, 0.5);
}

.bg-gradient-laser {
  background: linear-gradient(135deg, var(--laser-green) 0%, var(--toxic-green) 50%, var(--volt-lime) 100%);
  box-shadow: 0 0 30px rgba(0, 255, 136, 0.5);
}

.bg-gradient-solar {
  background: linear-gradient(135deg, var(--solar-yellow) 0%, var(--gold-rush) 50%, var(--banana-bright) 100%);
  box-shadow: 0 0 30px rgba(255, 255, 0, 0.5);
}

.bg-gradient-plasma {
  background: linear-gradient(135deg, var(--plasma-purple) 0%, var(--royal-violet) 50%, var(--grape-neon) 100%);
  box-shadow: 0 0 30px rgba(170, 68, 255, 0.5);
}

.bg-gradient-fire {
  background: linear-gradient(135deg, var(--fire-orange) 0%, var(--sunset-red) 50%, var(--coral-bright) 100%);
  box-shadow: 0 0 30px rgba(255, 102, 0, 0.5);
}

.bg-gradient-hero {
  background: linear-gradient(135deg,
    var(--electric-cyan) 0%,
    var(--neon-pink) 25%,
    var(--laser-green) 50%,
    var(--solar-yellow) 75%,
    var(--plasma-purple) 100%);
  box-shadow: 0 0 50px rgba(0, 212, 255, 0.3);
}

.bg-gradient-rainbow {
  background: linear-gradient(135deg,
    var(--electric-cyan) 0%,
    var(--neon-blue) 16.66%,
    var(--laser-green) 33.33%,
    var(--solar-yellow) 50%,
    var(--fire-orange) 66.66%,
    var(--neon-pink) 83.33%,
    var(--plasma-purple) 100%);
  box-shadow: 0 0 40px rgba(255, 255, 255, 0.2);
}

.bg-gradient-cyber {
  background: linear-gradient(135deg,
    var(--cyber-blue) 0%,
    var(--electric-cyan) 50%,
    var(--aqua-bright) 100%);
  box-shadow: 0 0 35px rgba(0, 153, 255, 0.4);
}

.bg-gradient-toxic {
  background: linear-gradient(135deg,
    var(--toxic-green) 0%,
    var(--electric-lime) 50%,
    var(--lime-shock) 100%);
  box-shadow: 0 0 35px rgba(102, 255, 0, 0.4);
}

/* Multi-layer Gradients */
.bg-gradient-aurora {
  background: linear-gradient(135deg,
    var(--deep-ocean) 0%,
    var(--royal-purple) 25%,
    var(--coral-sunset) 50%,
    var(--golden-hour) 75%,
    var(--emerald-green) 100%);
}

.bg-gradient-twilight {
  background: linear-gradient(135deg,
    var(--midnight-blue) 0%,
    var(--ocean-depth) 30%,
    var(--violet-dream) 70%,
    var(--lavender-mist) 100%);
}

.bg-gradient-sunrise {
  background: linear-gradient(135deg,
    var(--coral-sunset) 0%,
    var(--sunset-orange) 25%,
    var(--golden-hour) 50%,
    var(--champagne-gold) 75%,
    var(--amber-glow) 100%);
}

/* Enhanced Premium Color Utilities */

/* Primary Layer Text Colors */
.text-ocean {
  color: var(--deep-ocean);
}

.text-teal {
  color: var(--ocean-teal);
}

.text-coral {
  color: var(--coral-sunset);
}

.text-golden {
  color: var(--golden-hour);
}

.text-purple {
  color: var(--royal-purple);
}

.text-emerald {
  color: var(--emerald-green);
}

/* Secondary Layer Text Colors */
.text-azure {
  color: var(--azure-sky);
}

.text-turquoise {
  color: var(--turquoise-wave);
}

.text-rose {
  color: var(--rose-garden);
}

.text-champagne {
  color: var(--champagne-gold);
}

.text-lavender {
  color: var(--lavender-mist);
}

.text-seafoam {
  color: var(--seafoam-green);
}

/* Primary Layer Background Colors */
.bg-ocean {
  background-color: var(--deep-ocean);
}

.bg-teal {
  background-color: var(--ocean-teal);
}

.bg-coral {
  background-color: var(--coral-sunset);
}

.bg-golden {
  background-color: var(--golden-hour);
}

.bg-purple {
  background-color: var(--royal-purple);
}

.bg-emerald {
  background-color: var(--emerald-green);
}

/* Secondary Layer Background Colors */
.bg-azure {
  background-color: var(--azure-sky);
}

.bg-turquoise {
  background-color: var(--turquoise-wave);
}

.bg-rose {
  background-color: var(--rose-garden);
}

.bg-champagne {
  background-color: var(--champagne-gold);
}

.bg-lavender {
  background-color: var(--lavender-mist);
}

.bg-seafoam {
  background-color: var(--seafoam-green);
}

/* Enhanced Border Colors */
.border-ocean {
  border-color: var(--deep-ocean);
}

.border-teal {
  border-color: var(--ocean-teal);
}

.border-coral {
  border-color: var(--coral-sunset);
}

.border-golden {
  border-color: var(--golden-hour);
}

.border-purple {
  border-color: var(--royal-purple);
}

.border-emerald {
  border-color: var(--emerald-green);
}

.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Enhanced Premium Gradient Text Classes */
.gradient-text {
  background: linear-gradient(135deg, var(--deep-ocean) 0%, var(--ocean-teal) 30%, var(--coral-sunset) 70%, var(--golden-hour) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

.gradient-text-sunset {
  background: linear-gradient(135deg, var(--coral-sunset) 0%, var(--sunset-orange) 50%, var(--golden-hour) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

.gradient-text-ocean {
  background: linear-gradient(135deg, var(--deep-ocean) 0%, var(--ocean-teal) 50%, var(--azure-sky) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

.gradient-text-royal {
  background: linear-gradient(135deg, var(--royal-purple) 0%, var(--violet-dream) 50%, var(--lavender-mist) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

.gradient-text-emerald {
  background: linear-gradient(135deg, var(--emerald-green) 0%, var(--mint-fresh) 50%, var(--seafoam-green) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

.gradient-text-aurora {
  background: linear-gradient(135deg,
    var(--deep-ocean) 0%,
    var(--royal-purple) 25%,
    var(--coral-sunset) 50%,
    var(--golden-hour) 75%,
    var(--emerald-green) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800;
}

.gradient-text-premium {
  background: linear-gradient(135deg, var(--midnight-blue) 0%, var(--royal-purple) 50%, var(--rose-garden) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

/* Custom Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-slide-up {
  animation: slideInUp 0.6s ease-out;
}

.animate-slide-left {
  animation: slideInLeft 0.6s ease-out;
}

.animate-slide-right {
  animation: slideInRight 0.6s ease-out;
}

/* Modern Component Styles */
.hero-section {
  background: linear-gradient(135deg, var(--deep-ocean) 0%, var(--ocean-teal) 30%, var(--coral-sunset) 70%, var(--golden-hour) 100%);
  position: relative;
  overflow: hidden;
}

/* Bright Neon V Logo Styles */
.v-logo {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  border-radius: 20px;
  background: linear-gradient(135deg,
    var(--electric-cyan) 0%,
    var(--neon-pink) 25%,
    var(--laser-green) 50%,
    var(--solar-yellow) 75%,
    var(--plasma-purple) 100%);
  box-shadow:
    0 0 30px rgba(0, 212, 255, 0.6),
    0 0 60px rgba(255, 51, 102, 0.4),
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 2px 0 rgba(255, 255, 255, 0.3);
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  border: 2px solid rgba(255, 255, 255, 0.2);
  animation: logoGlow 3s ease-in-out infinite alternate;
}

.v-logo:hover {
  transform: translateY(-6px) scale(1.12) rotate(5deg);
  box-shadow:
    0 0 50px rgba(0, 212, 255, 0.8),
    0 0 80px rgba(255, 51, 102, 0.6),
    0 0 100px rgba(0, 255, 136, 0.4),
    0 12px 48px rgba(0, 0, 0, 0.4),
    inset 0 2px 0 rgba(255, 255, 255, 0.4);
  animation: logoGlowIntense 1.5s ease-in-out infinite alternate;
}

.v-logo::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg,
    transparent,
    rgba(255, 255, 255, 0.6),
    rgba(0, 255, 255, 0.4),
    rgba(255, 255, 0, 0.3),
    transparent);
  transform: rotate(45deg);
  transition: all 1.2s ease;
  opacity: 0;
}

.v-logo:hover::before {
  animation: neonShimmer 2s ease-in-out infinite;
}

.v-logo-large {
  width: 80px;
  height: 80px;
  border-radius: 24px;
}

.v-logo-large .v-letter {
  font-size: 40px;
}

.v-logo-xl {
  width: 112px;
  height: 112px;
  border-radius: 32px;
}

.v-logo-xl .v-letter {
  font-size: 56px;
}

.v-letter {
  font-size: 32px;
  font-weight: 900;
  color: #000000;
  text-shadow:
    0 0 10px rgba(255, 255, 255, 0.8),
    0 0 20px rgba(0, 255, 255, 0.6),
    0 0 30px rgba(255, 255, 0, 0.4),
    0 2px 4px rgba(0, 0, 0, 0.8);
  z-index: 2;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  letter-spacing: -0.02em;
  background: linear-gradient(135deg,
    #ffffff 0%,
    #00ffff 50%,
    #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.8));
}

/* Bright Neon Logo Variants */
.v-logo-electric {
  background: linear-gradient(135deg,
    var(--electric-cyan) 0%,
    var(--ice-blue) 50%,
    var(--turbo-teal) 100%);
  box-shadow:
    0 0 40px rgba(0, 212, 255, 0.8),
    0 0 80px rgba(68, 221, 255, 0.6),
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 2px 0 rgba(255, 255, 255, 0.4);
  animation: electricPulse 2s ease-in-out infinite alternate;
}

.v-logo-neon {
  background: linear-gradient(135deg,
    var(--neon-pink) 0%,
    var(--hot-magenta) 50%,
    var(--cherry-pop) 100%);
  box-shadow:
    0 0 40px rgba(255, 51, 102, 0.8),
    0 0 80px rgba(255, 0, 102, 0.6),
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 2px 0 rgba(255, 255, 255, 0.4);
  animation: neonPulse 2.5s ease-in-out infinite alternate;
}

.v-logo-laser {
  background: linear-gradient(135deg,
    var(--laser-green) 0%,
    var(--toxic-green) 50%,
    var(--volt-lime) 100%);
  box-shadow:
    0 0 40px rgba(0, 255, 136, 0.8),
    0 0 80px rgba(102, 255, 0, 0.6),
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 2px 0 rgba(255, 255, 255, 0.4);
  animation: laserPulse 2.2s ease-in-out infinite alternate;
}

.v-logo-solar {
  background: linear-gradient(135deg,
    var(--solar-yellow) 0%,
    var(--gold-rush) 50%,
    var(--banana-bright) 100%);
  box-shadow:
    0 0 40px rgba(255, 255, 0, 0.8),
    0 0 80px rgba(255, 204, 0, 0.6),
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 2px 0 rgba(255, 255, 255, 0.4);
  animation: solarPulse 1.8s ease-in-out infinite alternate;
}

.v-logo-plasma {
  background: linear-gradient(135deg,
    var(--plasma-purple) 0%,
    var(--royal-violet) 50%,
    var(--grape-neon) 100%);
  box-shadow:
    0 0 40px rgba(170, 68, 255, 0.8),
    0 0 80px rgba(102, 0, 255, 0.6),
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 2px 0 rgba(255, 255, 255, 0.4);
  animation: plasmaPulse 2.8s ease-in-out infinite alternate;
}

.v-logo-fire {
  background: linear-gradient(135deg,
    var(--fire-orange) 0%,
    var(--sunset-red) 50%,
    var(--coral-bright) 100%);
  box-shadow:
    0 0 40px rgba(255, 102, 0, 0.8),
    0 0 80px rgba(255, 51, 0, 0.6),
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 2px 0 rgba(255, 255, 255, 0.4);
  animation: firePulse 1.5s ease-in-out infinite alternate;
}

@keyframes modernShimmer {
  0% {
    transform: translateX(-100%) rotate(45deg);
    opacity: 0;
  }
  30% {
    opacity: 0.6;
  }
  70% {
    opacity: 0.8;
  }
  100% {
    transform: translateX(100%) rotate(45deg);
    opacity: 0;
  }
}

@keyframes neonShimmer {
  0% {
    transform: translateX(-150%) rotate(45deg);
    opacity: 0;
  }
  25% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
    transform: translateX(0%) rotate(45deg);
  }
  75% {
    opacity: 0.8;
  }
  100% {
    transform: translateX(150%) rotate(45deg);
    opacity: 0;
  }
}

@keyframes logoGlow {
  0% {
    box-shadow:
      0 0 30px rgba(0, 212, 255, 0.6),
      0 0 60px rgba(255, 51, 102, 0.4),
      0 8px 32px rgba(0, 0, 0, 0.3);
  }
  100% {
    box-shadow:
      0 0 50px rgba(0, 212, 255, 0.8),
      0 0 80px rgba(255, 51, 102, 0.6),
      0 0 100px rgba(0, 255, 136, 0.4),
      0 8px 32px rgba(0, 0, 0, 0.3);
  }
}

@keyframes logoGlowIntense {
  0% {
    box-shadow:
      0 0 50px rgba(0, 212, 255, 0.8),
      0 0 80px rgba(255, 51, 102, 0.6),
      0 0 100px rgba(0, 255, 136, 0.4),
      0 12px 48px rgba(0, 0, 0, 0.4);
  }
  100% {
    box-shadow:
      0 0 70px rgba(0, 212, 255, 1),
      0 0 100px rgba(255, 51, 102, 0.8),
      0 0 120px rgba(0, 255, 136, 0.6),
      0 0 140px rgba(255, 255, 0, 0.4),
      0 12px 48px rgba(0, 0, 0, 0.4);
  }
}

@keyframes electricPulse {
  0% { box-shadow: 0 0 40px rgba(0, 212, 255, 0.8), 0 0 80px rgba(68, 221, 255, 0.6); }
  100% { box-shadow: 0 0 60px rgba(0, 212, 255, 1), 0 0 120px rgba(68, 221, 255, 0.8); }
}

@keyframes neonPulse {
  0% { box-shadow: 0 0 40px rgba(255, 51, 102, 0.8), 0 0 80px rgba(255, 0, 102, 0.6); }
  100% { box-shadow: 0 0 60px rgba(255, 51, 102, 1), 0 0 120px rgba(255, 0, 102, 0.8); }
}

@keyframes laserPulse {
  0% { box-shadow: 0 0 40px rgba(0, 255, 136, 0.8), 0 0 80px rgba(102, 255, 0, 0.6); }
  100% { box-shadow: 0 0 60px rgba(0, 255, 136, 1), 0 0 120px rgba(102, 255, 0, 0.8); }
}

@keyframes solarPulse {
  0% { box-shadow: 0 0 40px rgba(255, 255, 0, 0.8), 0 0 80px rgba(255, 204, 0, 0.6); }
  100% { box-shadow: 0 0 60px rgba(255, 255, 0, 1), 0 0 120px rgba(255, 204, 0, 0.8); }
}

@keyframes plasmaPulse {
  0% { box-shadow: 0 0 40px rgba(170, 68, 255, 0.8), 0 0 80px rgba(102, 0, 255, 0.6); }
  100% { box-shadow: 0 0 60px rgba(170, 68, 255, 1), 0 0 120px rgba(102, 0, 255, 0.8); }
}

@keyframes firePulse {
  0% { box-shadow: 0 0 40px rgba(255, 102, 0, 0.8), 0 0 80px rgba(255, 51, 0, 0.6); }
  100% { box-shadow: 0 0 60px rgba(255, 102, 0, 1), 0 0 120px rgba(255, 51, 0, 0.8); }
}

@keyframes pulseGlow {
  0%, 100% {
    box-shadow:
      0 8px 32px rgba(10, 61, 98, 0.25),
      0 4px 16px rgba(41, 128, 185, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }
  50% {
    box-shadow:
      0 12px 48px rgba(10, 61, 98, 0.4),
      0 6px 24px rgba(41, 128, 185, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }
}

@keyframes floatAnimation {
  0%, 100% {
    transform: translateY(0px) scale(1);
  }
  50% {
    transform: translateY(-6px) scale(1.02);
  }
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
  opacity: 0.3;
}

.service-card {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(24px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 24px;
  box-shadow:
    0 8px 32px rgba(10, 61, 98, 0.08),
    0 4px 16px rgba(41, 128, 185, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  position: relative;
  overflow: hidden;
}

.service-card:hover {
  transform: translateY(-16px) scale(1.03) rotate(1deg);
  box-shadow:
    0 32px 64px rgba(10, 61, 98, 0.15),
    0 16px 32px rgba(41, 128, 185, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  background: rgba(255, 255, 255, 1);
  border-color: var(--ocean-teal);
}

.service-card-bus {
  background: linear-gradient(135deg,
    var(--electric-cyan) 0%,
    var(--cyber-blue) 50%,
    var(--ice-blue) 100%);
  color: #000000;
  font-weight: 700;
  box-shadow:
    0 0 30px rgba(0, 212, 255, 0.6),
    0 0 60px rgba(0, 153, 255, 0.4),
    0 12px 40px rgba(0, 0, 0, 0.3);
  border: 2px solid rgba(0, 255, 255, 0.3);
}

.service-card-carpool {
  background: linear-gradient(135deg,
    var(--laser-green) 0%,
    var(--toxic-green) 50%,
    var(--mint-electric) 100%);
  color: #000000;
  font-weight: 700;
  box-shadow:
    0 0 30px rgba(0, 255, 136, 0.6),
    0 0 60px rgba(102, 255, 0, 0.4),
    0 12px 40px rgba(0, 0, 0, 0.3);
  border: 2px solid rgba(0, 255, 153, 0.3);
}

.service-card-bike {
  background: linear-gradient(135deg,
    var(--fire-orange) 0%,
    var(--sunset-red) 50%,
    var(--coral-bright) 100%);
  color: #000000;
  font-weight: 700;
  box-shadow:
    0 0 30px rgba(255, 102, 0, 0.6),
    0 0 60px rgba(255, 51, 0, 0.4),
    0 12px 40px rgba(0, 0, 0, 0.3);
  border: 2px solid rgba(255, 102, 51, 0.3);
}

.service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent);
  transition: left 0.8s ease;
}

.service-card:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(135deg,
    var(--electric-cyan) 0%,
    var(--cyber-blue) 50%,
    var(--neon-blue) 100%);
  border: 2px solid rgba(0, 255, 255, 0.3);
  color: #000000;
  font-weight: 700;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 20px;
  padding: 18px 36px;
  font-size: 16px;
  box-shadow:
    0 0 20px rgba(0, 212, 255, 0.5),
    0 0 40px rgba(0, 153, 255, 0.3),
    0 8px 24px rgba(0, 0, 0, 0.3),
    inset 0 2px 0 rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
}

.btn-primary:hover {
  transform: translateY(-4px) scale(1.05);
  box-shadow:
    0 0 30px rgba(0, 212, 255, 0.8),
    0 0 60px rgba(0, 153, 255, 0.5),
    0 0 80px rgba(0, 255, 255, 0.3),
    0 12px 36px rgba(0, 0, 0, 0.4),
    inset 0 2px 0 rgba(255, 255, 255, 0.4);
  background: linear-gradient(135deg,
    var(--neon-blue) 0%,
    var(--electric-cyan) 50%,
    var(--turbo-teal) 100%);
}

.btn-secondary {
  background: linear-gradient(135deg,
    var(--neon-pink) 0%,
    var(--hot-magenta) 50%,
    var(--cherry-pop) 100%);
  border: 2px solid rgba(255, 51, 102, 0.3);
  color: #ffffff;
  font-weight: 700;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 20px;
  padding: 18px 36px;
  font-size: 16px;
  box-shadow:
    0 0 20px rgba(255, 51, 102, 0.5),
    0 0 40px rgba(255, 0, 102, 0.3),
    0 8px 24px rgba(0, 0, 0, 0.3),
    inset 0 2px 0 rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
}

.btn-secondary:hover {
  transform: translateY(-4px) scale(1.05);
  box-shadow:
    0 0 30px rgba(255, 51, 102, 0.8),
    0 0 60px rgba(255, 0, 102, 0.5),
    0 0 80px rgba(255, 0, 128, 0.3),
    0 12px 36px rgba(0, 0, 0, 0.4),
    inset 0 2px 0 rgba(255, 255, 255, 0.4);
  background: linear-gradient(135deg,
    var(--cherry-pop) 0%,
    var(--neon-pink) 50%,
    var(--hot-magenta) 100%);
}

.btn-premium {
  background: linear-gradient(135deg,
    var(--plasma-purple) 0%,
    var(--royal-violet) 50%,
    var(--grape-neon) 100%);
  border: 2px solid rgba(170, 68, 255, 0.3);
  color: #ffffff;
  font-weight: 700;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 20px;
  padding: 18px 36px;
  font-size: 16px;
  box-shadow:
    0 0 20px rgba(170, 68, 255, 0.5),
    0 0 40px rgba(102, 0, 255, 0.3),
    0 8px 24px rgba(0, 0, 0, 0.3),
    inset 0 2px 0 rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
}

.btn-premium:hover {
  transform: translateY(-4px) scale(1.05);
  box-shadow:
    0 0 30px rgba(170, 68, 255, 0.8),
    0 0 60px rgba(102, 0, 255, 0.5),
    0 0 80px rgba(128, 0, 255, 0.3),
    0 12px 36px rgba(0, 0, 0, 0.4),
    inset 0 2px 0 rgba(255, 255, 255, 0.4);
}

.btn-outline {
  background: transparent;
  border: 2px solid var(--ocean-teal);
  color: var(--ocean-teal);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 16px;
  padding: 14px 30px;
  font-weight: 600;
  font-size: 16px;
  position: relative;
  overflow: hidden;
}

.btn-outline:hover {
  background: linear-gradient(135deg, var(--ocean-teal) 0%, var(--azure-sky) 100%);
  color: white;
  transform: translateY(-3px) scale(1.02);
  box-shadow:
    0 8px 24px rgba(41, 128, 185, 0.3),
    0 4px 12px rgba(59, 130, 246, 0.2);
  border-color: var(--azure-sky);
}

/* Button shine effect */
.btn-primary::before,
.btn-secondary::before,
.btn-premium::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent);
  transition: left 0.6s ease;
}

.btn-primary:hover::before,
.btn-secondary:hover::before,
.btn-premium:hover::before {
  left: 100%;
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--sky-blue-dark) 0%, #4a9bc7 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(135, 206, 235, 0.4);
  color: white;
}

.btn-secondary {
  background: linear-gradient(135deg, var(--light-orange) 0%, var(--light-orange-dark) 100%);
  border: none;
  color: var(--travel-black);
  transition: all 0.3s ease;
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 600;
}

.btn-secondary:hover {
  background: linear-gradient(135deg, var(--light-orange-dark) 0%, #ff9f4d 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(255, 165, 0, 0.4);
  color: var(--travel-black);
}

.navbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(135, 206, 235, 0.2);
}

.footer {
  background: linear-gradient(135deg, var(--travel-black) 0%, var(--travel-black-light) 100%);
}

/* Form Styles */
.form-input {
  border: 2px solid var(--border);
  transition: all 0.3s ease;
  border-radius: 8px;
}

.form-input:focus {
  border-color: var(--sky-blue);
  box-shadow: 0 0 0 3px rgba(135, 206, 235, 0.1);
  outline: none;
}

/* Enhanced Card Styles */
.shadow-travel {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.shadow-travel-lg {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.shadow-sky-blue {
  box-shadow: 0 8px 20px rgba(135, 206, 235, 0.4);
}

.shadow-orange {
  box-shadow: 0 8px 20px rgba(255, 165, 0, 0.4);
}

/* Travel Gradient Backgrounds */
.bg-travel-gradient {
  background: linear-gradient(135deg, var(--sky-blue) 0%, var(--light-orange) 50%, var(--sky-blue) 100%);
}

/* Unique V Logo Styles */
.v-logo {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--sky-blue) 0%, var(--light-orange) 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: 0 8px 20px rgba(135, 206, 235, 0.4);
  transition: all 0.3s ease;
  overflow: hidden;
}

.v-logo::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s;
}

.v-logo:hover::before {
  left: 100%;
}

.v-logo:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 12px 30px rgba(135, 206, 235, 0.6);
}

.v-letter {
  font-size: 18px;
  font-weight: 900;
  color: white;
  font-family: 'Inter', sans-serif;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  position: relative;
  z-index: 1;
}

/* Large V Logo for Login/Register Pages */
.v-logo-large {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--sky-blue) 0%, var(--light-orange) 50%, var(--sky-blue) 100%);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: 0 15px 35px rgba(135, 206, 235, 0.4);
  transition: all 0.4s ease;
  overflow: hidden;
  animation: logoFloat 3s ease-in-out infinite;
}

.v-logo-large::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.8s;
}

.v-logo-large:hover::before {
  left: 100%;
}

.v-logo-large:hover {
  transform: translateY(-4px) scale(1.08);
  box-shadow: 0 20px 45px rgba(135, 206, 235, 0.6);
}

.v-letter-large {
  font-size: 36px;
  font-weight: 900;
  color: white;
  font-family: 'Inter', sans-serif;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  position: relative;
  z-index: 1;
}

@keyframes logoFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
}

/* V Logo Variants */
.v-logo-minimal {
  width: 32px;
  height: 32px;
  background: linear-gradient(45deg, var(--sky-blue), var(--light-orange));
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(135, 206, 235, 0.3);
}

.v-logo-minimal .v-letter {
  font-size: 14px;
  font-weight: 800;
}

/* V Logo with Glow Effect */
.v-logo-glow {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--sky-blue) 0%, var(--light-orange) 100%);
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow:
    0 0 20px rgba(135, 206, 235, 0.5),
    0 8px 20px rgba(135, 206, 235, 0.3);
  animation: logoGlow 2s ease-in-out infinite alternate;
}

.v-logo-glow .v-letter {
  font-size: 20px;
  font-weight: 900;
  color: white;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

@keyframes logoGlow {
  0% {
    box-shadow:
      0 0 20px rgba(135, 206, 235, 0.5),
      0 8px 20px rgba(135, 206, 235, 0.3);
  }
  100% {
    box-shadow:
      0 0 30px rgba(135, 206, 235, 0.8),
      0 12px 30px rgba(135, 206, 235, 0.5);
  }
}

/* Loading Animations */
.loading-spinner {
  border: 3px solid hsl(var(--muted));
  border-top: 3px solid hsl(var(--sky-blue));
  border-radius: 50%;
  width: 24px;
  height: 24px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design Enhancements */
@media (max-width: 768px) {
  .hero-section {
    padding: 2rem 1rem;
  }

  .service-card {
    margin-bottom: 1rem;
  }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  .hover-lift,
  .btn-primary,
  .btn-secondary,
  .service-card {
    transition: none;
  }

  .animate-float,
  .animate-pulse-slow,
  .animate-slide-up,
  .animate-slide-left,
  .animate-slide-right {
    animation: none;
  }
}
