@import "tailwindcss";

/* Vtravelallinone Modern Color Palette */
:root {
  /* Modern Sophisticated Color Scheme */
  --background: #fafbff;
  --foreground: #1e293b;
  --card: #ffffff;
  --card-foreground: #1e293b;
  --popover: #ffffff;
  --popover-foreground: #1e293b;
  --primary: #0f4c75; /* Deep Ocean Blue */
  --primary-foreground: #ffffff;
  --secondary: #3282b8; /* Ocean Teal */
  --secondary-foreground: #ffffff;
  --muted: #f1f5f9;
  --muted-foreground: #64748b;
  --accent: #ff6b6b; /* Coral Sunset */
  --accent-foreground: #ffffff;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #e2e8f0;
  --input: #e2e8f0;
  --ring: #3282b8;
  --radius: 0.75rem;

  /* Beautiful Modern Color Palette */
  --deep-ocean: #0f4c75;        /* Primary Brand - Deep Ocean Blue */
  --ocean-teal: #3282b8;        /* Secondary - Ocean Teal */
  --coral-sunset: #ff6b6b;      /* Accent - Coral Sunset */
  --golden-hour: #ffe66d;       /* Highlight - Golden Hour */
  --royal-purple: #6c5ce7;      /* Premium - Royal Purple */
  --fresh-mint: #00dfa2;        /* Success - Fresh Mint */
  --soft-lavender: #a8e6cf;     /* Light Accent - Soft Lavender */
  --warm-peach: #ffb4a2;        /* Warm Tone - Peach */
  --clear-sky: #74b9ff;         /* Bright Blue - Clear Sky */
  --rose-pink: #fd79a8;         /* Vibrant Pink - Rose */

  /* Neutral Modern Grays */
  --neutral-50: #fafbfc;
  --neutral-100: #f4f6f8;
  --neutral-200: #e4e7eb;
  --neutral-300: #d1d5db;
  --neutral-400: #9ca3af;
  --neutral-500: #6b7280;
  --neutral-600: #4b5563;
  --neutral-700: #374151;
  --neutral-800: #1f2937;
  --neutral-900: #111827;
}

.dark {
  --background: #0f172a;
  --foreground: #f1f5f9;
  --card: #1e293b;
  --card-foreground: #f1f5f9;
  --popover: #1e293b;
  --popover-foreground: #f1f5f9;
  --primary: #3282b8;
  --primary-foreground: #ffffff;
  --secondary: #0f4c75;
  --secondary-foreground: #ffffff;
  --muted: #334155;
  --muted-foreground: #94a3b8;
  --accent: #ff6b6b;
  --accent-foreground: #ffffff;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #475569;
  --input: #475569;
  --ring: #3282b8;
}

* {
  border-color: var(--border);
}

body {
  background-color: var(--background);
  color: var(--foreground);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

html {
  scroll-behavior: smooth;
}

/* Modern Gradient Utility Classes */
.bg-gradient-ocean {
  background: linear-gradient(135deg, var(--deep-ocean) 0%, var(--ocean-teal) 100%);
}

.bg-gradient-sunset {
  background: linear-gradient(135deg, var(--coral-sunset) 0%, var(--golden-hour) 100%);
}

.bg-gradient-royal {
  background: linear-gradient(135deg, var(--royal-purple) 0%, var(--rose-pink) 100%);
}

.bg-gradient-fresh {
  background: linear-gradient(135deg, var(--fresh-mint) 0%, var(--clear-sky) 100%);
}

.bg-gradient-warm {
  background: linear-gradient(135deg, var(--warm-peach) 0%, var(--golden-hour) 100%);
}

.bg-gradient-hero {
  background: linear-gradient(135deg, var(--deep-ocean) 0%, var(--ocean-teal) 50%, var(--coral-sunset) 100%);
}

/* Modern Color Utilities */
.text-ocean {
  color: var(--deep-ocean);
}

.text-teal {
  color: var(--ocean-teal);
}

.text-coral {
  color: var(--coral-sunset);
}

.text-golden {
  color: var(--golden-hour);
}

.text-purple {
  color: var(--royal-purple);
}

.text-mint {
  color: var(--fresh-mint);
}

.bg-ocean {
  background-color: var(--deep-ocean);
}

.bg-teal {
  background-color: var(--ocean-teal);
}

.bg-coral {
  background-color: var(--coral-sunset);
}

.bg-golden {
  background-color: var(--golden-hour);
}

.bg-purple {
  background-color: var(--royal-purple);
}

.bg-mint {
  background-color: var(--fresh-mint);
}

.border-ocean {
  border-color: var(--deep-ocean);
}

.border-teal {
  border-color: var(--ocean-teal);
}

.border-coral {
  border-color: var(--coral-sunset);
}

.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.gradient-text {
  background: linear-gradient(135deg, var(--deep-ocean) 0%, var(--ocean-teal) 50%, var(--coral-sunset) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-text-sunset {
  background: linear-gradient(135deg, var(--coral-sunset) 0%, var(--golden-hour) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-text-ocean {
  background: linear-gradient(135deg, var(--deep-ocean) 0%, var(--ocean-teal) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-text-royal {
  background: linear-gradient(135deg, var(--royal-purple) 0%, var(--rose-pink) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Custom Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-slide-up {
  animation: slideInUp 0.6s ease-out;
}

.animate-slide-left {
  animation: slideInLeft 0.6s ease-out;
}

.animate-slide-right {
  animation: slideInRight 0.6s ease-out;
}

/* Modern Component Styles */
.hero-section {
  background: linear-gradient(135deg, var(--deep-ocean) 0%, var(--ocean-teal) 30%, var(--coral-sunset) 70%, var(--golden-hour) 100%);
  position: relative;
  overflow: hidden;
}

/* Modern V Logo Styles */
.v-logo {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 16px;
  background: linear-gradient(135deg, var(--deep-ocean) 0%, var(--ocean-teal) 50%, var(--coral-sunset) 100%);
  box-shadow: 0 8px 32px rgba(15, 76, 117, 0.3);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.v-logo:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 12px 40px rgba(15, 76, 117, 0.4);
}

.v-logo::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transform: rotate(45deg);
  transition: all 0.8s ease;
  opacity: 0;
}

.v-logo:hover::before {
  animation: modernShimmer 2s ease-in-out;
}

.v-logo-large {
  width: 64px;
  height: 64px;
  border-radius: 20px;
}

.v-logo-large .v-letter {
  font-size: 32px;
}

.v-letter {
  font-size: 24px;
  font-weight: 900;
  color: white;
  text-shadow: 0 3px 6px rgba(0, 0, 0, 0.4);
  z-index: 1;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

@keyframes modernShimmer {
  0% {
    transform: translateX(-100%) rotate(45deg);
    opacity: 0;
  }
  30% {
    opacity: 0.6;
  }
  70% {
    opacity: 0.8;
  }
  100% {
    transform: translateX(100%) rotate(45deg);
    opacity: 0;
  }
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
  opacity: 0.3;
}

.service-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(15, 76, 117, 0.1);
}

.service-card:hover {
  transform: translateY(-12px) scale(1.02);
  box-shadow: 0 24px 48px rgba(15, 76, 117, 0.15);
  background: rgba(255, 255, 255, 1);
  border-color: var(--ocean-teal);
}

.service-card-bus {
  background: linear-gradient(135deg, var(--deep-ocean) 0%, var(--ocean-teal) 100%);
  color: white;
}

.service-card-carpool {
  background: linear-gradient(135deg, var(--fresh-mint) 0%, var(--clear-sky) 100%);
  color: white;
}

.service-card-bike {
  background: linear-gradient(135deg, var(--coral-sunset) 0%, var(--golden-hour) 100%);
  color: white;
}

.btn-primary {
  background: linear-gradient(135deg, var(--deep-ocean) 0%, var(--ocean-teal) 100%);
  border: none;
  color: white;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 12px;
  padding: 14px 28px;
  font-weight: 600;
  box-shadow: 0 4px 16px rgba(15, 76, 117, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(15, 76, 117, 0.4);
  background: linear-gradient(135deg, var(--ocean-teal) 0%, var(--deep-ocean) 100%);
}

.btn-secondary {
  background: linear-gradient(135deg, var(--coral-sunset) 0%, var(--golden-hour) 100%);
  border: none;
  color: white;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 12px;
  padding: 14px 28px;
  font-weight: 600;
  box-shadow: 0 4px 16px rgba(255, 107, 107, 0.3);
}

.btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(255, 107, 107, 0.4);
  background: linear-gradient(135deg, var(--golden-hour) 0%, var(--coral-sunset) 100%);
}

.btn-outline {
  background: transparent;
  border: 2px solid var(--ocean-teal);
  color: var(--ocean-teal);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 12px;
  padding: 12px 26px;
  font-weight: 600;
}

.btn-outline:hover {
  background: var(--ocean-teal);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(50, 130, 184, 0.3);
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--sky-blue-dark) 0%, #4a9bc7 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(135, 206, 235, 0.4);
  color: white;
}

.btn-secondary {
  background: linear-gradient(135deg, var(--light-orange) 0%, var(--light-orange-dark) 100%);
  border: none;
  color: var(--travel-black);
  transition: all 0.3s ease;
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 600;
}

.btn-secondary:hover {
  background: linear-gradient(135deg, var(--light-orange-dark) 0%, #ff9f4d 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(255, 165, 0, 0.4);
  color: var(--travel-black);
}

.navbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(135, 206, 235, 0.2);
}

.footer {
  background: linear-gradient(135deg, var(--travel-black) 0%, var(--travel-black-light) 100%);
}

/* Form Styles */
.form-input {
  border: 2px solid var(--border);
  transition: all 0.3s ease;
  border-radius: 8px;
}

.form-input:focus {
  border-color: var(--sky-blue);
  box-shadow: 0 0 0 3px rgba(135, 206, 235, 0.1);
  outline: none;
}

/* Enhanced Card Styles */
.shadow-travel {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.shadow-travel-lg {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.shadow-sky-blue {
  box-shadow: 0 8px 20px rgba(135, 206, 235, 0.4);
}

.shadow-orange {
  box-shadow: 0 8px 20px rgba(255, 165, 0, 0.4);
}

/* Travel Gradient Backgrounds */
.bg-travel-gradient {
  background: linear-gradient(135deg, var(--sky-blue) 0%, var(--light-orange) 50%, var(--sky-blue) 100%);
}

/* Unique V Logo Styles */
.v-logo {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--sky-blue) 0%, var(--light-orange) 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: 0 8px 20px rgba(135, 206, 235, 0.4);
  transition: all 0.3s ease;
  overflow: hidden;
}

.v-logo::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s;
}

.v-logo:hover::before {
  left: 100%;
}

.v-logo:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 12px 30px rgba(135, 206, 235, 0.6);
}

.v-letter {
  font-size: 18px;
  font-weight: 900;
  color: white;
  font-family: 'Inter', sans-serif;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  position: relative;
  z-index: 1;
}

/* Large V Logo for Login/Register Pages */
.v-logo-large {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--sky-blue) 0%, var(--light-orange) 50%, var(--sky-blue) 100%);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: 0 15px 35px rgba(135, 206, 235, 0.4);
  transition: all 0.4s ease;
  overflow: hidden;
  animation: logoFloat 3s ease-in-out infinite;
}

.v-logo-large::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.8s;
}

.v-logo-large:hover::before {
  left: 100%;
}

.v-logo-large:hover {
  transform: translateY(-4px) scale(1.08);
  box-shadow: 0 20px 45px rgba(135, 206, 235, 0.6);
}

.v-letter-large {
  font-size: 36px;
  font-weight: 900;
  color: white;
  font-family: 'Inter', sans-serif;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  position: relative;
  z-index: 1;
}

@keyframes logoFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
}

/* V Logo Variants */
.v-logo-minimal {
  width: 32px;
  height: 32px;
  background: linear-gradient(45deg, var(--sky-blue), var(--light-orange));
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(135, 206, 235, 0.3);
}

.v-logo-minimal .v-letter {
  font-size: 14px;
  font-weight: 800;
}

/* V Logo with Glow Effect */
.v-logo-glow {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--sky-blue) 0%, var(--light-orange) 100%);
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow:
    0 0 20px rgba(135, 206, 235, 0.5),
    0 8px 20px rgba(135, 206, 235, 0.3);
  animation: logoGlow 2s ease-in-out infinite alternate;
}

.v-logo-glow .v-letter {
  font-size: 20px;
  font-weight: 900;
  color: white;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

@keyframes logoGlow {
  0% {
    box-shadow:
      0 0 20px rgba(135, 206, 235, 0.5),
      0 8px 20px rgba(135, 206, 235, 0.3);
  }
  100% {
    box-shadow:
      0 0 30px rgba(135, 206, 235, 0.8),
      0 12px 30px rgba(135, 206, 235, 0.5);
  }
}

/* Loading Animations */
.loading-spinner {
  border: 3px solid hsl(var(--muted));
  border-top: 3px solid hsl(var(--sky-blue));
  border-radius: 50%;
  width: 24px;
  height: 24px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design Enhancements */
@media (max-width: 768px) {
  .hero-section {
    padding: 2rem 1rem;
  }

  .service-card {
    margin-bottom: 1rem;
  }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  .hover-lift,
  .btn-primary,
  .btn-secondary,
  .service-card {
    transition: none;
  }

  .animate-float,
  .animate-pulse-slow,
  .animate-slide-up,
  .animate-slide-left,
  .animate-slide-right {
    animation: none;
  }
}
