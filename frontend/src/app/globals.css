@import "tailwindcss";

/* TravelAllinOne Custom Color Scheme */
:root {
  /* Sky Blue, Light Orange, Black Color Scheme */
  --background: #fafbff;
  --foreground: #1a1a1a;
  --card: #ffffff;
  --card-foreground: #1a1a1a;
  --popover: #ffffff;
  --popover-foreground: #1a1a1a;
  --primary: #87ceeb; /* Sky Blue */
  --primary-foreground: #ffffff;
  --secondary: #ffd4b3; /* Light Orange */
  --secondary-foreground: #1a1a1a;
  --muted: #f5f5f5;
  --muted-foreground: #737373;
  --accent: #ffd4b3; /* Light Orange */
  --accent-foreground: #1a1a1a;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #e5e5e5;
  --input: #e5e5e5;
  --ring: #87ceeb;
  --radius: 0.75rem;

  /* Custom TravelAllinOne Colors */
  --sky-blue: #87ceeb;
  --sky-blue-dark: #5fb3d4;
  --light-orange: #ffd4b3;
  --light-orange-dark: #ffb380;
  --travel-black: #1a1a1a;
  --travel-black-light: #333333;
}

.dark {
  --background: #0a0a0a;
  --foreground: #f5f5f5;
  --card: #1a1a1a;
  --card-foreground: #f5f5f5;
  --popover: #1a1a1a;
  --popover-foreground: #f5f5f5;
  --primary: #87ceeb;
  --primary-foreground: #1a1a1a;
  --secondary: #ffb380;
  --secondary-foreground: #1a1a1a;
  --muted: #262626;
  --muted-foreground: #a3a3a3;
  --accent: #ffb380;
  --accent-foreground: #1a1a1a;
  --destructive: #dc2626;
  --destructive-foreground: #ffffff;
  --border: #333333;
  --input: #333333;
  --ring: #87ceeb;
}

* {
  border-color: var(--border);
}

body {
  background-color: var(--background);
  color: var(--foreground);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

html {
  scroll-behavior: smooth;
}

/* Custom Utility Classes */
.bg-gradient-primary {
  background: linear-gradient(135deg, var(--sky-blue) 0%, var(--light-orange) 100%);
}

.bg-gradient-secondary {
  background: linear-gradient(135deg, var(--light-orange) 0%, var(--sky-blue) 100%);
}

.text-sky-blue {
  color: var(--sky-blue);
}

.text-light-orange {
  color: var(--light-orange-dark);
}

.text-travel-black {
  color: var(--travel-black);
}

.bg-sky-blue {
  background-color: var(--sky-blue);
}

.bg-light-orange {
  background-color: var(--light-orange);
}

.bg-travel-black {
  background-color: var(--travel-black);
}

.border-sky-blue {
  border-color: var(--sky-blue);
}

.border-light-orange {
  border-color: var(--light-orange-dark);
}

.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.gradient-text {
  background: linear-gradient(135deg, var(--sky-blue) 0%, var(--light-orange) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Custom Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-slide-up {
  animation: slideInUp 0.6s ease-out;
}

.animate-slide-left {
  animation: slideInLeft 0.6s ease-out;
}

.animate-slide-right {
  animation: slideInRight 0.6s ease-out;
}

/* Component Specific Styles */
.hero-section {
  background: linear-gradient(135deg, var(--sky-blue) 0%, var(--light-orange) 50%, var(--sky-blue) 100%);
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
  opacity: 0.3;
}

.service-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  border-radius: 12px;
}

.service-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 1);
}

.btn-primary {
  background: linear-gradient(135deg, var(--sky-blue) 0%, var(--sky-blue-dark) 100%);
  border: none;
  color: white;
  transition: all 0.3s ease;
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 600;
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--sky-blue-dark) 0%, #4a9bc7 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(135, 206, 235, 0.4);
  color: white;
}

.btn-secondary {
  background: linear-gradient(135deg, var(--light-orange) 0%, var(--light-orange-dark) 100%);
  border: none;
  color: var(--travel-black);
  transition: all 0.3s ease;
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 600;
}

.btn-secondary:hover {
  background: linear-gradient(135deg, var(--light-orange-dark) 0%, #ff9f4d 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(255, 165, 0, 0.4);
  color: var(--travel-black);
}

.navbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(135, 206, 235, 0.2);
}

.footer {
  background: linear-gradient(135deg, var(--travel-black) 0%, var(--travel-black-light) 100%);
}

/* Form Styles */
.form-input {
  border: 2px solid var(--border);
  transition: all 0.3s ease;
  border-radius: 8px;
}

.form-input:focus {
  border-color: var(--sky-blue);
  box-shadow: 0 0 0 3px rgba(135, 206, 235, 0.1);
  outline: none;
}

/* Enhanced Card Styles */
.shadow-travel {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.shadow-travel-lg {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.shadow-sky-blue {
  box-shadow: 0 8px 20px rgba(135, 206, 235, 0.4);
}

.shadow-orange {
  box-shadow: 0 8px 20px rgba(255, 165, 0, 0.4);
}

/* Travel Gradient Backgrounds */
.bg-travel-gradient {
  background: linear-gradient(135deg, var(--sky-blue) 0%, var(--light-orange) 50%, var(--sky-blue) 100%);
}

/* Loading Animations */
.loading-spinner {
  border: 3px solid hsl(var(--muted));
  border-top: 3px solid hsl(var(--sky-blue));
  border-radius: 50%;
  width: 24px;
  height: 24px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design Enhancements */
@media (max-width: 768px) {
  .hero-section {
    padding: 2rem 1rem;
  }

  .service-card {
    margin-bottom: 1rem;
  }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  .hover-lift,
  .btn-primary,
  .btn-secondary,
  .service-card {
    transition: none;
  }

  .animate-float,
  .animate-pulse-slow,
  .animate-slide-up,
  .animate-slide-left,
  .animate-slide-right {
    animation: none;
  }
}
