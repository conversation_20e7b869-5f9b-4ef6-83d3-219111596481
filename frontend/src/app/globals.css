@import "tailwindcss";

/* TravelAllinOne Custom Color Scheme */
@layer base {
  :root {
    /* Sky Blue, Light Orange, Black Color Scheme */
    --background: 240 100% 98%;
    --foreground: 0 0% 10%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 10%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 10%;
    --primary: 197 71% 73%; /* Sky Blue */
    --primary-foreground: 0 0% 100%;
    --secondary: 33 100% 85%; /* Light Orange */
    --secondary-foreground: 0 0% 10%;
    --muted: 240 5% 96%;
    --muted-foreground: 0 0% 45%;
    --accent: 33 100% 85%; /* Light Orange */
    --accent-foreground: 0 0% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 240 6% 90%;
    --input: 240 6% 90%;
    --ring: 197 71% 73%;
    --radius: 0.75rem;

    /* Custom TravelAllinOne Colors */
    --sky-blue: 197 71% 73%;
    --sky-blue-dark: 197 71% 63%;
    --light-orange: 33 100% 85%;
    --light-orange-dark: 33 100% 75%;
    --black: 0 0% 10%;
    --black-light: 0 0% 20%;
    --gradient-primary: linear-gradient(135deg, hsl(197 71% 73%) 0%, hsl(33 100% 85%) 100%);
    --gradient-secondary: linear-gradient(135deg, hsl(33 100% 85%) 0%, hsl(197 71% 73%) 100%);
  }

  .dark {
    --background: 0 0% 8%;
    --foreground: 0 0% 95%;
    --card: 0 0% 12%;
    --card-foreground: 0 0% 95%;
    --popover: 0 0% 12%;
    --popover-foreground: 0 0% 95%;
    --primary: 197 71% 73%;
    --primary-foreground: 0 0% 10%;
    --secondary: 33 100% 75%;
    --secondary-foreground: 0 0% 10%;
    --muted: 0 0% 15%;
    --muted-foreground: 0 0% 65%;
    --accent: 33 100% 75%;
    --accent-foreground: 0 0% 10%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 0 0% 20%;
    --input: 0 0% 20%;
    --ring: 197 71% 73%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  html {
    scroll-behavior: smooth;
  }
}

/* Custom Utility Classes */
@layer utilities {
  .bg-gradient-primary {
    background: var(--gradient-primary);
  }

  .bg-gradient-secondary {
    background: var(--gradient-secondary);
  }

  .text-sky-blue {
    color: hsl(var(--sky-blue));
  }

  .text-light-orange {
    color: hsl(var(--light-orange-dark));
  }

  .text-black {
    color: hsl(var(--black));
  }

  .bg-sky-blue {
    background-color: hsl(var(--sky-blue));
  }

  .bg-light-orange {
    background-color: hsl(var(--light-orange));
  }

  .bg-black {
    background-color: hsl(var(--black));
  }

  .border-sky-blue {
    border-color: hsl(var(--sky-blue));
  }

  .border-light-orange {
    border-color: hsl(var(--light-orange-dark));
  }

  .hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  }

  .glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .gradient-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

/* Custom Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-slide-up {
  animation: slideInUp 0.6s ease-out;
}

.animate-slide-left {
  animation: slideInLeft 0.6s ease-out;
}

.animate-slide-right {
  animation: slideInRight 0.6s ease-out;
}

/* Component Specific Styles */
.hero-section {
  background: linear-gradient(135deg, hsl(197 71% 73%) 0%, hsl(33 100% 85%) 50%, hsl(197 71% 83%) 100%);
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
  opacity: 0.3;
}

.service-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.service-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 1);
}

.btn-primary {
  background: linear-gradient(135deg, hsl(197 71% 73%) 0%, hsl(197 71% 63%) 100%);
  border: none;
  color: white;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background: linear-gradient(135deg, hsl(197 71% 63%) 0%, hsl(197 71% 53%) 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(135, 206, 235, 0.4);
}

.btn-secondary {
  background: linear-gradient(135deg, hsl(33 100% 85%) 0%, hsl(33 100% 75%) 100%);
  border: none;
  color: hsl(var(--black));
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background: linear-gradient(135deg, hsl(33 100% 75%) 0%, hsl(33 100% 65%) 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(255, 165, 0, 0.4);
}

.navbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(135, 206, 235, 0.2);
}

.footer {
  background: linear-gradient(135deg, hsl(var(--black)) 0%, hsl(var(--black-light)) 100%);
}

/* Form Styles */
.form-input {
  border: 2px solid hsl(var(--border));
  transition: all 0.3s ease;
}

.form-input:focus {
  border-color: hsl(var(--sky-blue));
  box-shadow: 0 0 0 3px rgba(135, 206, 235, 0.1);
}

/* Loading Animations */
.loading-spinner {
  border: 3px solid hsl(var(--muted));
  border-top: 3px solid hsl(var(--sky-blue));
  border-radius: 50%;
  width: 24px;
  height: 24px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design Enhancements */
@media (max-width: 768px) {
  .hero-section {
    padding: 2rem 1rem;
  }

  .service-card {
    margin-bottom: 1rem;
  }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  .hover-lift,
  .btn-primary,
  .btn-secondary,
  .service-card {
    transition: none;
  }

  .animate-float,
  .animate-pulse-slow,
  .animate-slide-up,
  .animate-slide-left,
  .animate-slide-right {
    animation: none;
  }
}
