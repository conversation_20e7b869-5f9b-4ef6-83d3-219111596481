import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Bus, Car, Bike, MapPin, Clock, Shield, Star } from "lucide-react";

export default function Home() {
  const services = [
    {
      icon: Bus,
      title: "Bus Booking",
      description: "Book bus tickets across India with real-time seat availability and best prices",
      href: "/bus",
      color: "bg-blue-500",
      features: ["1000+ Routes", "Live Tracking", "Easy Cancellation"]
    },
    {
      icon: Car,
      title: "Car Pooling",
      description: "Share rides with verified drivers and split costs for comfortable travel",
      href: "/carpool",
      color: "bg-green-500",
      features: ["Verified Drivers", "Cost Sharing", "Safe Travel"]
    },
    {
      icon: Bike,
      title: "Bike Rides",
      description: "Quick and affordable bike rides for short distances in your city",
      href: "/bike",
      color: "bg-orange-500",
      features: ["Quick Booking", "Real-time Tracking", "Affordable Rates"]
    }
  ];

  const stats = [
    { label: "Cities Covered", value: "500+" },
    { label: "Happy Customers", value: "1M+" },
    { label: "Daily Rides", value: "10K+" },
    { label: "Partner Operators", value: "2000+" }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="hero-section text-white py-20 relative overflow-hidden">
        <div className="absolute inset-0 bg-travel-gradient opacity-90"></div>
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6 text-travel-black">
              Travel Across India
              <span className="block gradient-text">All in One Place</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-travel-black max-w-3xl mx-auto opacity-90">
              Book buses, share rides, or grab a quick bike ride. Your complete travel solution for every journey across India.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="btn-primary hover-lift" asChild>
                <Link href="/bus">🚌 Book Bus Tickets</Link>
              </Button>
              <Button size="lg" className="btn-secondary hover-lift" asChild>
                <Link href="/carpool">🚗 Find Car Pool</Link>
              </Button>
            </div>
          </div>
        </div>

        {/* Floating Elements */}
        <div className="absolute top-20 left-10 w-20 h-20 bg-white/20 rounded-full animate-float"></div>
        <div className="absolute top-40 right-20 w-16 h-16 bg-white/15 rounded-full animate-float"></div>
        <div className="absolute bottom-20 left-1/4 w-12 h-12 bg-white/10 rounded-full animate-float"></div>
      </section>

      {/* Services Section */}
      <section className="py-20 bg-gradient-to-b from-white to-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-travel-black mb-4">
              Choose Your Travel Mode
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Whether you're planning a long journey or need a quick ride, we've got you covered with multiple travel options.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {services.map((service, index) => {
              const IconComponent = service.icon;
              return (
                <Card key={index} className="service-card hover-lift border-0 shadow-travel">
                  <CardHeader className="text-center">
                    <div className={`w-16 h-16 ${service.color} rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg`}>
                      <IconComponent className="w-8 h-8 text-white" />
                    </div>
                    <CardTitle className="text-2xl text-travel-black">{service.title}</CardTitle>
                    <CardDescription className="text-base text-gray-600">{service.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2 mb-6">
                      {service.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center text-sm text-gray-600">
                          <div className="w-2 h-2 bg-light-orange rounded-full mr-3"></div>
                          {feature}
                        </li>
                      ))}
                    </ul>
                    <Button className="w-full btn-primary hover-lift" asChild>
                      <Link href={service.href}>Get Started</Link>
                    </Button>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white relative">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl md:text-4xl font-bold gradient-text mb-2">{stat.value}</div>
                <div className="text-gray-600 font-medium">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gradient-to-b from-gray-50 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-travel-black mb-4">
              Why Choose TravelAllinOne?
            </h2>
            <div className="w-24 h-1 bg-gradient-primary mx-auto rounded-full"></div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-sky-blue rounded-full flex items-center justify-center mx-auto mb-4 shadow-sky-blue hover-lift">
                <Shield className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-2 text-travel-black">Safe & Secure</h3>
              <p className="text-gray-600">All our partners are verified and we ensure your safety throughout the journey.</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-light-orange rounded-full flex items-center justify-center mx-auto mb-4 shadow-orange hover-lift">
                <Clock className="w-8 h-8 text-travel-black" />
              </div>
              <h3 className="text-xl font-semibold mb-2 text-travel-black">Real-time Updates</h3>
              <p className="text-gray-600">Get live tracking and updates about your journey status and arrival times.</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4 shadow-travel hover-lift">
                <Star className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-2 text-travel-black">Best Prices</h3>
              <p className="text-gray-600">Compare prices across multiple operators and get the best deals for your travel.</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-travel-black text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-sky-blue/20 via-transparent to-light-orange/20"></div>
        <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div>
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Ready to Start Your Journey?
            </h2>
            <p className="text-xl mb-8 text-gray-300">
              Join millions of travelers who trust TravelAllinOne for their daily commute and long-distance travel.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="btn-primary hover-lift" asChild>
                <Link href="/register">🚀 Sign Up Now</Link>
              </Button>
              <Button size="lg" className="btn-secondary hover-lift" asChild>
                <Link href="/help">📚 Learn More</Link>
              </Button>
            </div>
          </div>
        </div>

        {/* Decorative Elements */}
        <div className="absolute top-10 left-10 w-32 h-32 bg-white/10 rounded-full animate-pulse"></div>
        <div className="absolute bottom-10 right-10 w-24 h-24 bg-white/10 rounded-full animate-pulse"></div>
      </section>
    </div>
  );
}
