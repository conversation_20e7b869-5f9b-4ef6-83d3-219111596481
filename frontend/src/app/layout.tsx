import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import "../styles/custom.css";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import Chatbot from "@/components/Chatbot";
import { Toaster } from "@/components/ui/sonner";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Vtravelallinone - Your Complete Travel Solution",
  description: "Book buses, share rides, or grab a quick bike ride. Your complete travel solution for every journey across India. Safe, affordable, and convenient.",
  keywords: "travel, bus booking, carpool, bike rides, India travel, transportation, AbhiBus, BlaBlaCar, Rapido",
  authors: [{ name: "Vtravelallinone Team" }],
  creator: "Vtravelallinone",
  publisher: "Vtravelallinone",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  openGraph: {
    title: "Vtravelallinone - Your Complete Travel Solution",
    description: "Book buses, share rides, or grab a quick bike ride. Your complete travel solution for every journey across India.",
    siteName: 'Vtravelallinone',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Vtravelallinone - Complete Travel Solution',
      },
    ],
    locale: 'en_IN',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: "Vtravelallinone - Your Complete Travel Solution",
    description: "Book buses, share rides, or grab a quick bike ride. Your complete travel solution for every journey across India.",
    images: ['/twitter-image.jpg'],
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen flex flex-col`}
      >
        <Header />
        <main className="flex-1">
          {children}
        </main>
        <Footer />
        <Chatbot />
        <Toaster />
      </body>
    </html>
  );
}
