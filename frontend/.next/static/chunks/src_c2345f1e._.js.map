{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/TravelAllinOne/frontend/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 41, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/TravelAllinOne/frontend/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 290, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/TravelAllinOne/frontend/src/app/bus/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\nimport { \n  Bus, \n  MapPin, \n  Calendar, \n  Users, \n  Clock, \n  Star, \n  Wifi, \n  Zap, \n  Coffee,\n  ArrowLeftRight,\n  Search\n} from 'lucide-react';\nimport { busApi } from '@/lib/api';\nimport { toast } from 'sonner';\n\nconst BusBookingPage = () => {\n  const [searchData, setSearchData] = useState({\n    source_city: '',\n    destination_city: '',\n    travel_date: '',\n    passenger_count: 1,\n    bus_type: ''\n  });\n  const [searchResults, setSearchResults] = useState([]);\n  const [isSearching, setIsSearching] = useState(false);\n\n  const popularRoutes = [\n    { from: 'Mumbai', to: 'Pune', duration: '3h 30m', price: '₹450' },\n    { from: 'Delhi', to: 'Agra', duration: '4h 15m', price: '₹380' },\n    { from: 'Bangalore', to: 'Chennai', duration: '6h 45m', price: '₹650' },\n    { from: 'Hyderabad', to: 'Vijayawada', duration: '4h 30m', price: '₹420' },\n  ];\n\n  const busTypes = [\n    { value: 'regular', label: 'Regular' },\n    { value: 'ac', label: 'AC' },\n    { value: 'sleeper', label: 'Sleeper' },\n    { value: 'luxury', label: 'Luxury' }\n  ];\n\n  const handleInputChange = (field: string, value: string | number) => {\n    setSearchData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  const handleSearch = async () => {\n    if (!searchData.source_city || !searchData.destination_city || !searchData.travel_date) {\n      toast.error('Please fill in all required fields');\n      return;\n    }\n\n    setIsSearching(true);\n    try {\n      const response = await busApi.search(searchData);\n      if (response.data.success) {\n        setSearchResults(response.data.data.buses || []);\n        if (response.data.data.buses?.length === 0) {\n          toast.info('No buses found for the selected route and date');\n        }\n      } else {\n        toast.error('Search failed. Please try again.');\n      }\n    } catch (error: any) {\n      console.error('Search error:', error);\n      toast.error('Search failed. Please try again.');\n    } finally {\n      setIsSearching(false);\n    }\n  };\n\n  const swapCities = () => {\n    setSearchData(prev => ({\n      ...prev,\n      source_city: prev.destination_city,\n      destination_city: prev.source_city\n    }));\n  };\n\n  const BusCard = ({ bus }: { bus: any }) => {\n    const amenityIcons: { [key: string]: any } = {\n      wifi: Wifi,\n      charging_point: Zap,\n      water_bottle: Coffee,\n    };\n\n    return (\n      <Card className=\"mb-4 hover:shadow-lg transition-shadow\">\n        <CardContent className=\"p-6\">\n          <div className=\"flex justify-between items-start mb-4\">\n            <div className=\"flex-1\">\n              <div className=\"flex items-center gap-2 mb-2\">\n                <Bus className=\"w-5 h-5 text-blue-600\" />\n                <h3 className=\"text-lg font-semibold\">{bus.bus.operator_name}</h3>\n                <span className=\"px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full\">\n                  {bus.bus.bus_type.toUpperCase()}\n                </span>\n              </div>\n              <p className=\"text-gray-600 text-sm\">{bus.bus.bus_number}</p>\n              \n              <div className=\"flex items-center gap-4 mt-3\">\n                <div className=\"flex items-center gap-1\">\n                  <Clock className=\"w-4 h-4 text-gray-500\" />\n                  <span className=\"text-sm\">\n                    {new Date(bus.route.departure_time).toLocaleTimeString('en-IN', { \n                      hour: '2-digit', \n                      minute: '2-digit' \n                    })}\n                  </span>\n                </div>\n                <div className=\"text-gray-400\">→</div>\n                <div className=\"flex items-center gap-1\">\n                  <Clock className=\"w-4 h-4 text-gray-500\" />\n                  <span className=\"text-sm\">\n                    {new Date(bus.route.arrival_time).toLocaleTimeString('en-IN', { \n                      hour: '2-digit', \n                      minute: '2-digit' \n                    })}\n                  </span>\n                </div>\n                <div className=\"text-sm text-gray-600\">\n                  ({Math.floor(bus.route.estimated_duration_minutes / 60)}h {bus.route.estimated_duration_minutes % 60}m)\n                </div>\n              </div>\n\n              <div className=\"flex items-center gap-2 mt-2\">\n                {bus.bus.rating && (\n                  <div className=\"flex items-center gap-1\">\n                    <Star className=\"w-4 h-4 text-yellow-500 fill-current\" />\n                    <span className=\"text-sm font-medium\">{bus.bus.rating}</span>\n                    <span className=\"text-xs text-gray-500\">({bus.bus.reviews_count} reviews)</span>\n                  </div>\n                )}\n              </div>\n\n              <div className=\"flex items-center gap-2 mt-3\">\n                {bus.bus.amenities.map((amenity: string, index: number) => {\n                  const IconComponent = amenityIcons[amenity] || Coffee;\n                  return (\n                    <div key={index} className=\"flex items-center gap-1 text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded\">\n                      <IconComponent className=\"w-3 h-3\" />\n                      <span>{amenity.replace('_', ' ')}</span>\n                    </div>\n                  );\n                })}\n              </div>\n            </div>\n\n            <div className=\"text-right\">\n              <div className=\"text-2xl font-bold text-green-600\">\n                ₹{Object.values(bus.pricing)[0]}\n              </div>\n              <div className=\"text-sm text-gray-500 mb-2\">per person</div>\n              <div className=\"text-sm text-gray-600 mb-3\">\n                {bus.bus.available_seats} seats left\n              </div>\n              <Button className=\"w-full\">\n                Select Seats\n              </Button>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    );\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Hero Section with Search */}\n      <section className=\"bg-gradient-to-r from-blue-600 to-blue-800 text-white py-12\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-8\">\n            <h1 className=\"text-3xl md:text-4xl font-bold mb-4\">\n              Book Bus Tickets\n            </h1>\n            <p className=\"text-xl text-blue-100\">\n              Travel comfortably across India with verified bus operators\n            </p>\n          </div>\n\n          <Card className=\"max-w-4xl mx-auto\">\n            <CardContent className=\"p-6\">\n              <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4 items-end\">\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"from\">From</Label>\n                  <div className=\"relative\">\n                    <MapPin className=\"absolute left-3 top-3 h-4 w-4 text-gray-400\" />\n                    <Input\n                      id=\"from\"\n                      placeholder=\"Departure city\"\n                      value={searchData.source_city}\n                      onChange={(e) => handleInputChange('source_city', e.target.value)}\n                      className=\"pl-10\"\n                    />\n                  </div>\n                </div>\n\n                <div className=\"flex justify-center md:col-span-1\">\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    onClick={swapCities}\n                    className=\"p-2 hover:bg-gray-100 rounded-full mt-6\"\n                  >\n                    <ArrowLeftRight className=\"w-4 h-4\" />\n                  </Button>\n                </div>\n\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"to\">To</Label>\n                  <div className=\"relative\">\n                    <MapPin className=\"absolute left-3 top-3 h-4 w-4 text-gray-400\" />\n                    <Input\n                      id=\"to\"\n                      placeholder=\"Destination city\"\n                      value={searchData.destination_city}\n                      onChange={(e) => handleInputChange('destination_city', e.target.value)}\n                      className=\"pl-10\"\n                    />\n                  </div>\n                </div>\n\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"date\">Travel Date</Label>\n                  <div className=\"relative\">\n                    <Calendar className=\"absolute left-3 top-3 h-4 w-4 text-gray-400\" />\n                    <Input\n                      id=\"date\"\n                      type=\"date\"\n                      value={searchData.travel_date}\n                      onChange={(e) => handleInputChange('travel_date', e.target.value)}\n                      className=\"pl-10\"\n                      min={new Date().toISOString().split('T')[0]}\n                    />\n                  </div>\n                </div>\n\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"passengers\">Passengers</Label>\n                  <div className=\"relative\">\n                    <Users className=\"absolute left-3 top-3 h-4 w-4 text-gray-400\" />\n                    <Select \n                      value={searchData.passenger_count.toString()} \n                      onValueChange={(value) => handleInputChange('passenger_count', parseInt(value))}\n                    >\n                      <SelectTrigger className=\"pl-10\">\n                        <SelectValue />\n                      </SelectTrigger>\n                      <SelectContent>\n                        {[1, 2, 3, 4, 5, 6].map(num => (\n                          <SelectItem key={num} value={num.toString()}>\n                            {num} {num === 1 ? 'Passenger' : 'Passengers'}\n                          </SelectItem>\n                        ))}\n                      </SelectContent>\n                    </Select>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"flex justify-between items-center mt-6\">\n                <div className=\"flex gap-4\">\n                  <Select \n                    value={searchData.bus_type} \n                    onValueChange={(value) => handleInputChange('bus_type', value)}\n                  >\n                    <SelectTrigger className=\"w-40\">\n                      <SelectValue placeholder=\"Bus Type\" />\n                    </SelectTrigger>\n                    <SelectContent>\n                      <SelectItem value=\"\">All Types</SelectItem>\n                      {busTypes.map(type => (\n                        <SelectItem key={type.value} value={type.value}>\n                          {type.label}\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                </div>\n\n                <Button \n                  onClick={handleSearch} \n                  disabled={isSearching}\n                  className=\"px-8\"\n                >\n                  <Search className=\"w-4 h-4 mr-2\" />\n                  {isSearching ? 'Searching...' : 'Search Buses'}\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </section>\n\n      {/* Results Section */}\n      <section className=\"py-8\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          {searchResults.length > 0 ? (\n            <div>\n              <h2 className=\"text-2xl font-bold mb-6\">\n                Available Buses ({searchResults.length} found)\n              </h2>\n              <div className=\"grid grid-cols-1 gap-4\">\n                {searchResults.map((bus: any, index: number) => (\n                  <BusCard key={index} bus={bus} />\n                ))}\n              </div>\n            </div>\n          ) : (\n            <div>\n              <h2 className=\"text-2xl font-bold mb-6\">Popular Routes</h2>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n                {popularRoutes.map((route, index) => (\n                  <Card key={index} className=\"hover:shadow-lg transition-shadow cursor-pointer\">\n                    <CardContent className=\"p-4\">\n                      <div className=\"flex items-center justify-between mb-2\">\n                        <span className=\"font-medium\">{route.from}</span>\n                        <ArrowLeftRight className=\"w-4 h-4 text-gray-400\" />\n                        <span className=\"font-medium\">{route.to}</span>\n                      </div>\n                      <div className=\"flex justify-between text-sm text-gray-600\">\n                        <span>{route.duration}</span>\n                        <span className=\"font-semibold text-green-600\">{route.price}</span>\n                      </div>\n                    </CardContent>\n                  </Card>\n                ))}\n              </div>\n            </div>\n          )}\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default BusBookingPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AACA;;;AAvBA;;;;;;;;;;AAyBA,MAAM,iBAAiB;;IACrB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,aAAa;QACb,kBAAkB;QAClB,aAAa;QACb,iBAAiB;QACjB,UAAU;IACZ;IACA,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,gBAAgB;QACpB;YAAE,MAAM;YAAU,IAAI;YAAQ,UAAU;YAAU,OAAO;QAAO;QAChE;YAAE,MAAM;YAAS,IAAI;YAAQ,UAAU;YAAU,OAAO;QAAO;QAC/D;YAAE,MAAM;YAAa,IAAI;YAAW,UAAU;YAAU,OAAO;QAAO;QACtE;YAAE,MAAM;YAAa,IAAI;YAAc,UAAU;YAAU,OAAO;QAAO;KAC1E;IAED,MAAM,WAAW;QACf;YAAE,OAAO;YAAW,OAAO;QAAU;QACrC;YAAE,OAAO;YAAM,OAAO;QAAK;QAC3B;YAAE,OAAO;YAAW,OAAO;QAAU;QACrC;YAAE,OAAO;YAAU,OAAO;QAAS;KACpC;IAED,MAAM,oBAAoB,CAAC,OAAe;QACxC,cAAc,CAAA,OAAQ,CAAC;gBACrB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,WAAW,WAAW,IAAI,CAAC,WAAW,gBAAgB,IAAI,CAAC,WAAW,WAAW,EAAE;YACtF,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,eAAe;QACf,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,SAAM,CAAC,MAAM,CAAC;YACrC,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gBACzB,iBAAiB,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE;gBAC/C,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,WAAW,GAAG;oBAC1C,2IAAA,CAAA,QAAK,CAAC,IAAI,CAAC;gBACb;YACF,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,aAAa;QACjB,cAAc,CAAA,OAAQ,CAAC;gBACrB,GAAG,IAAI;gBACP,aAAa,KAAK,gBAAgB;gBAClC,kBAAkB,KAAK,WAAW;YACpC,CAAC;IACH;IAEA,MAAM,UAAU,CAAC,EAAE,GAAG,EAAgB;QACpC,MAAM,eAAuC;YAC3C,MAAM,qMAAA,CAAA,OAAI;YACV,gBAAgB,mMAAA,CAAA,MAAG;YACnB,cAAc,yMAAA,CAAA,SAAM;QACtB;QAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;sBACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,6LAAC;4CAAG,WAAU;sDAAyB,IAAI,GAAG,CAAC,aAAa;;;;;;sDAC5D,6LAAC;4CAAK,WAAU;sDACb,IAAI,GAAG,CAAC,QAAQ,CAAC,WAAW;;;;;;;;;;;;8CAGjC,6LAAC;oCAAE,WAAU;8CAAyB,IAAI,GAAG,CAAC,UAAU;;;;;;8CAExD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;oDAAK,WAAU;8DACb,IAAI,KAAK,IAAI,KAAK,CAAC,cAAc,EAAE,kBAAkB,CAAC,SAAS;wDAC9D,MAAM;wDACN,QAAQ;oDACV;;;;;;;;;;;;sDAGJ,6LAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;oDAAK,WAAU;8DACb,IAAI,KAAK,IAAI,KAAK,CAAC,YAAY,EAAE,kBAAkB,CAAC,SAAS;wDAC5D,MAAM;wDACN,QAAQ;oDACV;;;;;;;;;;;;sDAGJ,6LAAC;4CAAI,WAAU;;gDAAwB;gDACnC,KAAK,KAAK,CAAC,IAAI,KAAK,CAAC,0BAA0B,GAAG;gDAAI;gDAAG,IAAI,KAAK,CAAC,0BAA0B,GAAG;gDAAG;;;;;;;;;;;;;8CAIzG,6LAAC;oCAAI,WAAU;8CACZ,IAAI,GAAG,CAAC,MAAM,kBACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;gDAAK,WAAU;0DAAuB,IAAI,GAAG,CAAC,MAAM;;;;;;0DACrD,6LAAC;gDAAK,WAAU;;oDAAwB;oDAAE,IAAI,GAAG,CAAC,aAAa;oDAAC;;;;;;;;;;;;;;;;;;8CAKtE,6LAAC;oCAAI,WAAU;8CACZ,IAAI,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,SAAiB;wCACvC,MAAM,gBAAgB,YAAY,CAAC,QAAQ,IAAI,yMAAA,CAAA,SAAM;wCACrD,qBACE,6LAAC;4CAAgB,WAAU;;8DACzB,6LAAC;oDAAc,WAAU;;;;;;8DACzB,6LAAC;8DAAM,QAAQ,OAAO,CAAC,KAAK;;;;;;;2CAFpB;;;;;oCAKd;;;;;;;;;;;;sCAIJ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;wCAAoC;wCAC/C,OAAO,MAAM,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE;;;;;;;8CAEjC,6LAAC;oCAAI,WAAU;8CAA6B;;;;;;8CAC5C,6LAAC;oCAAI,WAAU;;wCACZ,IAAI,GAAG,CAAC,eAAe;wCAAC;;;;;;;8CAE3B,6LAAC,qIAAA,CAAA,SAAM;oCAAC,WAAU;8CAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQvC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAsC;;;;;;8CAGpD,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAKvC,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAO;;;;;;kEACtB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,6LAAC,oIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,aAAY;gEACZ,OAAO,WAAW,WAAW;gEAC7B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;gEAChE,WAAU;;;;;;;;;;;;;;;;;;0DAKhB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;oDACT,WAAU;8DAEV,cAAA,6LAAC,iOAAA,CAAA,iBAAc;wDAAC,WAAU;;;;;;;;;;;;;;;;0DAI9B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAK;;;;;;kEACpB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,6LAAC,oIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,aAAY;gEACZ,OAAO,WAAW,gBAAgB;gEAClC,UAAU,CAAC,IAAM,kBAAkB,oBAAoB,EAAE,MAAM,CAAC,KAAK;gEACrE,WAAU;;;;;;;;;;;;;;;;;;0DAKhB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAO;;;;;;kEACtB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,6LAAC,oIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,MAAK;gEACL,OAAO,WAAW,WAAW;gEAC7B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;gEAChE,WAAU;gEACV,KAAK,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;;;;;;;;;;;;;;;;;;0DAKjD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAa;;;;;;kEAC5B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,6LAAC,qIAAA,CAAA,SAAM;gEACL,OAAO,WAAW,eAAe,CAAC,QAAQ;gEAC1C,eAAe,CAAC,QAAU,kBAAkB,mBAAmB,SAAS;;kFAExE,6LAAC,qIAAA,CAAA,gBAAa;wEAAC,WAAU;kFACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;kFAEd,6LAAC,qIAAA,CAAA,gBAAa;kFACX;4EAAC;4EAAG;4EAAG;4EAAG;4EAAG;4EAAG;yEAAE,CAAC,GAAG,CAAC,CAAA,oBACtB,6LAAC,qIAAA,CAAA,aAAU;gFAAW,OAAO,IAAI,QAAQ;;oFACtC;oFAAI;oFAAE,QAAQ,IAAI,cAAc;;+EADlB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAU7B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oDACL,OAAO,WAAW,QAAQ;oDAC1B,eAAe,CAAC,QAAU,kBAAkB,YAAY;;sEAExD,6LAAC,qIAAA,CAAA,gBAAa;4DAAC,WAAU;sEACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,6LAAC,qIAAA,CAAA,gBAAa;;8EACZ,6LAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAG;;;;;;gEACpB,SAAS,GAAG,CAAC,CAAA,qBACZ,6LAAC,qIAAA,CAAA,aAAU;wEAAkB,OAAO,KAAK,KAAK;kFAC3C,KAAK,KAAK;uEADI,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;0DAQnC,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAS;gDACT,UAAU;gDACV,WAAU;;kEAEV,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDACjB,cAAc,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS5C,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACZ,cAAc,MAAM,GAAG,kBACtB,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;;oCAA0B;oCACpB,cAAc,MAAM;oCAAC;;;;;;;0CAEzC,6LAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAC,KAAU,sBAC5B,6LAAC;wCAAoB,KAAK;uCAAZ;;;;;;;;;;;;;;;6CAKpB,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAA0B;;;;;;0CACxC,6LAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAC,OAAO,sBACzB,6LAAC,mIAAA,CAAA,OAAI;wCAAa,WAAU;kDAC1B,cAAA,6LAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAe,MAAM,IAAI;;;;;;sEACzC,6LAAC,iOAAA,CAAA,iBAAc;4DAAC,WAAU;;;;;;sEAC1B,6LAAC;4DAAK,WAAU;sEAAe,MAAM,EAAE;;;;;;;;;;;;8DAEzC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAM,MAAM,QAAQ;;;;;;sEACrB,6LAAC;4DAAK,WAAU;sEAAgC,MAAM,KAAK;;;;;;;;;;;;;;;;;;uCATtD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqB7B;GAhUM;KAAA;uCAkUS", "debugId": null}}]}