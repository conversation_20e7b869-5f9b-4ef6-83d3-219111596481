{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/TravelAllinOne/frontend/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 41, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/TravelAllinOne/frontend/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 290, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/TravelAllinOne/frontend/src/app/bike/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { \n  Bike, \n  MapPin, \n  Clock, \n  Star, \n  DollarSign,\n  Navigation,\n  User,\n  Phone,\n  Zap\n} from 'lucide-react';\nimport { bikeApi } from '@/lib/api';\nimport { toast } from 'sonner';\n\nconst BikePage = () => {\n  const [rideData, setRideData] = useState({\n    pickup_location: {\n      latitude: 0,\n      longitude: 0,\n      address: '',\n      city: '',\n      state: '',\n      pincode: ''\n    },\n    drop_location: {\n      latitude: 0,\n      longitude: 0,\n      address: '',\n      city: '',\n      state: '',\n      pincode: ''\n    },\n    ride_type: 'regular',\n    passenger_phone: ''\n  });\n  const [estimate, setEstimate] = useState<any>(null);\n  const [nearbyDrivers, setNearbyDrivers] = useState([]);\n  const [isGettingEstimate, setIsGettingEstimate] = useState(false);\n  const [isBooking, setIsBooking] = useState(false);\n  const [userLocation, setUserLocation] = useState<any>(null);\n\n  useEffect(() => {\n    // Get user's current location\n    if (navigator.geolocation) {\n      navigator.geolocation.getCurrentPosition(\n        (position) => {\n          setUserLocation({\n            latitude: position.coords.latitude,\n            longitude: position.coords.longitude\n          });\n          // Auto-fill pickup location\n          setRideData(prev => ({\n            ...prev,\n            pickup_location: {\n              ...prev.pickup_location,\n              latitude: position.coords.latitude,\n              longitude: position.coords.longitude,\n              address: 'Current Location',\n              city: 'Mumbai', // This would be determined by reverse geocoding\n              state: 'Maharashtra',\n              pincode: '400001'\n            }\n          }));\n        },\n        (error) => {\n          console.error('Error getting location:', error);\n          toast.error('Unable to get your location. Please enter manually.');\n        }\n      );\n    }\n  }, []);\n\n  const handleLocationChange = (field: string, subfield: string, value: string) => {\n    setRideData(prev => ({\n      ...prev,\n      [field]: {\n        ...prev[field as keyof typeof prev],\n        [subfield]: value\n      }\n    }));\n  };\n\n  const handleGetEstimate = async () => {\n    if (!rideData.pickup_location.address || !rideData.drop_location.address) {\n      toast.error('Please enter both pickup and drop locations');\n      return;\n    }\n\n    setIsGettingEstimate(true);\n    try {\n      const response = await bikeApi.getEstimate(rideData);\n      if (response.data.success) {\n        setEstimate(response.data.data.estimate);\n        // Also get nearby drivers\n        if (userLocation) {\n          const driversResponse = await bikeApi.getNearbyDrivers(\n            userLocation.latitude, \n            userLocation.longitude\n          );\n          if (driversResponse.data.success) {\n            setNearbyDrivers(driversResponse.data.data.drivers || []);\n          }\n        }\n      } else {\n        toast.error('Failed to get estimate. Please try again.');\n      }\n    } catch (error: any) {\n      console.error('Estimate error:', error);\n      toast.error('Failed to get estimate. Please try again.');\n    } finally {\n      setIsGettingEstimate(false);\n    }\n  };\n\n  const handleBookRide = async () => {\n    if (!rideData.passenger_phone) {\n      toast.error('Please enter your phone number');\n      return;\n    }\n\n    setIsBooking(true);\n    try {\n      const response = await bikeApi.book(rideData);\n      if (response.data.success) {\n        toast.success('Ride booked successfully! Finding nearby drivers...');\n        // Here you would typically redirect to a tracking page\n      } else {\n        toast.error('Booking failed. Please try again.');\n      }\n    } catch (error: any) {\n      console.error('Booking error:', error);\n      toast.error('Booking failed. Please try again.');\n    } finally {\n      setIsBooking(false);\n    }\n  };\n\n  const DriverCard = ({ driver }: { driver: any }) => (\n    <Card className=\"mb-3\">\n      <CardContent className=\"p-4\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center gap-3\">\n            <div className=\"w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center\">\n              <User className=\"w-5 h-5 text-orange-600\" />\n            </div>\n            <div>\n              <div className=\"font-medium\">Driver nearby</div>\n              <div className=\"text-sm text-gray-600\">{driver.vehicle_number}</div>\n              {driver.rating && (\n                <div className=\"flex items-center gap-1\">\n                  <Star className=\"w-3 h-3 text-yellow-500 fill-current\" />\n                  <span className=\"text-xs\">{driver.rating}</span>\n                </div>\n              )}\n            </div>\n          </div>\n          <div className=\"text-right\">\n            <div className=\"text-sm font-medium\">{driver.estimated_arrival_minutes} min</div>\n            <div className=\"text-xs text-gray-500\">away</div>\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  );\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-r from-orange-600 to-orange-800 text-white py-12\">\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-8\">\n            <h1 className=\"text-3xl md:text-4xl font-bold mb-4\">\n              Quick Bike Rides\n            </h1>\n            <p className=\"text-xl text-orange-100\">\n              Fast, affordable rides for short distances\n            </p>\n          </div>\n\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"space-y-4\">\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"pickup\">Pickup Location</Label>\n                    <div className=\"relative\">\n                      <MapPin className=\"absolute left-3 top-3 h-4 w-4 text-gray-400\" />\n                      <Input\n                        id=\"pickup\"\n                        placeholder=\"Enter pickup location\"\n                        value={rideData.pickup_location.address}\n                        onChange={(e) => handleLocationChange('pickup_location', 'address', e.target.value)}\n                        className=\"pl-10\"\n                      />\n                    </div>\n                  </div>\n\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"drop\">Drop Location</Label>\n                    <div className=\"relative\">\n                      <MapPin className=\"absolute left-3 top-3 h-4 w-4 text-gray-400\" />\n                      <Input\n                        id=\"drop\"\n                        placeholder=\"Enter drop location\"\n                        value={rideData.drop_location.address}\n                        onChange={(e) => handleLocationChange('drop_location', 'address', e.target.value)}\n                        className=\"pl-10\"\n                      />\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"ride_type\">Ride Type</Label>\n                    <Select \n                      value={rideData.ride_type} \n                      onValueChange={(value) => setRideData(prev => ({ ...prev, ride_type: value }))}\n                    >\n                      <SelectTrigger>\n                        <SelectValue />\n                      </SelectTrigger>\n                      <SelectContent>\n                        <SelectItem value=\"regular\">Regular</SelectItem>\n                        <SelectItem value=\"premium\">Premium</SelectItem>\n                        <SelectItem value=\"delivery\">Delivery</SelectItem>\n                      </SelectContent>\n                    </Select>\n                  </div>\n\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"phone\">Your Phone Number</Label>\n                    <div className=\"relative\">\n                      <Phone className=\"absolute left-3 top-3 h-4 w-4 text-gray-400\" />\n                      <Input\n                        id=\"phone\"\n                        placeholder=\"+919876543210\"\n                        value={rideData.passenger_phone}\n                        onChange={(e) => setRideData(prev => ({ ...prev, passenger_phone: e.target.value }))}\n                        className=\"pl-10\"\n                      />\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"flex gap-4\">\n                  <Button \n                    onClick={handleGetEstimate} \n                    disabled={isGettingEstimate}\n                    variant=\"outline\"\n                    className=\"flex-1\"\n                  >\n                    <DollarSign className=\"w-4 h-4 mr-2\" />\n                    {isGettingEstimate ? 'Getting Estimate...' : 'Get Estimate'}\n                  </Button>\n                  \n                  {estimate && (\n                    <Button \n                      onClick={handleBookRide} \n                      disabled={isBooking}\n                      className=\"flex-1 bg-orange-600 hover:bg-orange-700\"\n                    >\n                      <Bike className=\"w-4 h-4 mr-2\" />\n                      {isBooking ? 'Booking...' : 'Book Ride'}\n                    </Button>\n                  )}\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </section>\n\n      {/* Estimate and Drivers Section */}\n      {(estimate || nearbyDrivers.length > 0) && (\n        <section className=\"py-8\">\n          <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              {/* Estimate Card */}\n              {estimate && (\n                <Card>\n                  <CardHeader>\n                    <CardTitle className=\"flex items-center gap-2\">\n                      <DollarSign className=\"w-5 h-5 text-green-600\" />\n                      Ride Estimate\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"space-y-3\">\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-gray-600\">Distance:</span>\n                        <span className=\"font-medium\">{estimate.distance_km} km</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-gray-600\">Duration:</span>\n                        <span className=\"font-medium\">{estimate.estimated_duration_minutes} min</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-gray-600\">Base Fare:</span>\n                        <span className=\"font-medium\">₹{estimate.estimated_fare}</span>\n                      </div>\n                      {estimate.surge_multiplier > 1 && (\n                        <div className=\"flex justify-between\">\n                          <span className=\"text-gray-600\">Surge:</span>\n                          <span className=\"font-medium text-orange-600\">{estimate.surge_multiplier}x</span>\n                        </div>\n                      )}\n                      <hr />\n                      <div className=\"flex justify-between text-lg font-bold\">\n                        <span>Total Fare:</span>\n                        <span className=\"text-green-600\">₹{Math.round(estimate.estimated_fare * estimate.surge_multiplier)}</span>\n                      </div>\n                      <div className=\"text-sm text-gray-500\">\n                        {estimate.available_drivers} drivers nearby\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n              )}\n\n              {/* Nearby Drivers */}\n              {nearbyDrivers.length > 0 && (\n                <Card>\n                  <CardHeader>\n                    <CardTitle className=\"flex items-center gap-2\">\n                      <Navigation className=\"w-5 h-5 text-blue-600\" />\n                      Nearby Drivers\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"space-y-2 max-h-64 overflow-y-auto\">\n                      {nearbyDrivers.slice(0, 5).map((driver: any, index: number) => (\n                        <DriverCard key={index} driver={driver} />\n                      ))}\n                    </div>\n                  </CardContent>\n                </Card>\n              )}\n            </div>\n          </div>\n        </section>\n      )}\n\n      {/* Features Section */}\n      <section className=\"py-12 bg-white\">\n        <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <h2 className=\"text-2xl font-bold text-center mb-8\">Why Choose Our Bike Rides?</h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <Card>\n              <CardContent className=\"p-6 text-center\">\n                <Zap className=\"w-12 h-12 text-orange-600 mx-auto mb-4\" />\n                <h3 className=\"text-lg font-semibold mb-2\">Quick Booking</h3>\n                <p className=\"text-gray-600\">Book a ride in seconds and get picked up within minutes</p>\n              </CardContent>\n            </Card>\n            <Card>\n              <CardContent className=\"p-6 text-center\">\n                <DollarSign className=\"w-12 h-12 text-green-600 mx-auto mb-4\" />\n                <h3 className=\"text-lg font-semibold mb-2\">Affordable Rates</h3>\n                <p className=\"text-gray-600\">Best prices for short distance travel in the city</p>\n              </CardContent>\n            </Card>\n            <Card>\n              <CardContent className=\"p-6 text-center\">\n                <Navigation className=\"w-12 h-12 text-blue-600 mx-auto mb-4\" />\n                <h3 className=\"text-lg font-semibold mb-2\">Live Tracking</h3>\n                <p className=\"text-gray-600\">Track your ride in real-time and share with family</p>\n              </CardContent>\n            </Card>\n          </div>\n        </div>\n      </section>\n\n      {/* Popular Areas */}\n      <section className=\"py-12 bg-gray-50\">\n        <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <h2 className=\"text-2xl font-bold text-center mb-8\">Popular Areas</h2>\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n            {[\n              'Bandra West', 'Andheri East', 'Powai', 'Koregaon Park',\n              'Indiranagar', 'Whitefield', 'Gachibowli', 'Cyber City'\n            ].map((area, index) => (\n              <Card key={index} className=\"hover:shadow-md transition-shadow cursor-pointer\">\n                <CardContent className=\"p-4 text-center\">\n                  <MapPin className=\"w-6 h-6 text-orange-600 mx-auto mb-2\" />\n                  <span className=\"text-sm font-medium\">{area}</span>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default BikePage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;;;AApBA;;;;;;;;;;AAsBA,MAAM,WAAW;;IACf,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,iBAAiB;YACf,UAAU;YACV,WAAW;YACX,SAAS;YACT,MAAM;YACN,OAAO;YACP,SAAS;QACX;QACA,eAAe;YACb,UAAU;YACV,WAAW;YACX,SAAS;YACT,MAAM;YACN,OAAO;YACP,SAAS;QACX;QACA,WAAW;QACX,iBAAiB;IACnB;IACA,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAC9C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAEtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,8BAA8B;YAC9B,IAAI,UAAU,WAAW,EAAE;gBACzB,UAAU,WAAW,CAAC,kBAAkB;0CACtC,CAAC;wBACC,gBAAgB;4BACd,UAAU,SAAS,MAAM,CAAC,QAAQ;4BAClC,WAAW,SAAS,MAAM,CAAC,SAAS;wBACtC;wBACA,4BAA4B;wBAC5B;kDAAY,CAAA,OAAQ,CAAC;oCACnB,GAAG,IAAI;oCACP,iBAAiB;wCACf,GAAG,KAAK,eAAe;wCACvB,UAAU,SAAS,MAAM,CAAC,QAAQ;wCAClC,WAAW,SAAS,MAAM,CAAC,SAAS;wCACpC,SAAS;wCACT,MAAM;wCACN,OAAO;wCACP,SAAS;oCACX;gCACF,CAAC;;oBACH;;0CACA,CAAC;wBACC,QAAQ,KAAK,CAAC,2BAA2B;wBACzC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACd;;YAEJ;QACF;6BAAG,EAAE;IAEL,MAAM,uBAAuB,CAAC,OAAe,UAAkB;QAC7D,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;oBACP,GAAG,IAAI,CAAC,MAA2B;oBACnC,CAAC,SAAS,EAAE;gBACd;YACF,CAAC;IACH;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,SAAS,eAAe,CAAC,OAAO,IAAI,CAAC,SAAS,aAAa,CAAC,OAAO,EAAE;YACxE,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,qBAAqB;QACrB,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAO,CAAC,WAAW,CAAC;YAC3C,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gBACzB,YAAY,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ;gBACvC,0BAA0B;gBAC1B,IAAI,cAAc;oBAChB,MAAM,kBAAkB,MAAM,oHAAA,CAAA,UAAO,CAAC,gBAAgB,CACpD,aAAa,QAAQ,EACrB,aAAa,SAAS;oBAExB,IAAI,gBAAgB,IAAI,CAAC,OAAO,EAAE;wBAChC,iBAAiB,gBAAgB,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE;oBAC1D;gBACF;YACF,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,mBAAmB;YACjC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,qBAAqB;QACvB;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,SAAS,eAAe,EAAE;YAC7B,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,aAAa;QACb,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAO,CAAC,IAAI,CAAC;YACpC,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gBACzB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,uDAAuD;YACzD,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,kBAAkB;YAChC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,aAAa,CAAC,EAAE,MAAM,EAAmB,iBAC7C,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;sBACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAElB,6LAAC;;sDACC,6LAAC;4CAAI,WAAU;sDAAc;;;;;;sDAC7B,6LAAC;4CAAI,WAAU;sDAAyB,OAAO,cAAc;;;;;;wCAC5D,OAAO,MAAM,kBACZ,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;oDAAK,WAAU;8DAAW,OAAO,MAAM;;;;;;;;;;;;;;;;;;;;;;;;sCAKhD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;wCAAuB,OAAO,yBAAyB;wCAAC;;;;;;;8CACvE,6LAAC;oCAAI,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOjD,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAsC;;;;;;8CAGpD,6LAAC;oCAAE,WAAU;8CAA0B;;;;;;;;;;;;sCAKzC,6LAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAS;;;;;;sEACxB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,6MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAClB,6LAAC,oIAAA,CAAA,QAAK;oEACJ,IAAG;oEACH,aAAY;oEACZ,OAAO,SAAS,eAAe,CAAC,OAAO;oEACvC,UAAU,CAAC,IAAM,qBAAqB,mBAAmB,WAAW,EAAE,MAAM,CAAC,KAAK;oEAClF,WAAU;;;;;;;;;;;;;;;;;;8DAKhB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAO;;;;;;sEACtB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,6MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAClB,6LAAC,oIAAA,CAAA,QAAK;oEACJ,IAAG;oEACH,aAAY;oEACZ,OAAO,SAAS,aAAa,CAAC,OAAO;oEACrC,UAAU,CAAC,IAAM,qBAAqB,iBAAiB,WAAW,EAAE,MAAM,CAAC,KAAK;oEAChF,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sDAMlB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAY;;;;;;sEAC3B,6LAAC,qIAAA,CAAA,SAAM;4DACL,OAAO,SAAS,SAAS;4DACzB,eAAe,CAAC,QAAU,YAAY,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,WAAW;oEAAM,CAAC;;8EAE5E,6LAAC,qIAAA,CAAA,gBAAa;8EACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;8EAEd,6LAAC,qIAAA,CAAA,gBAAa;;sFACZ,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAU;;;;;;sFAC5B,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAU;;;;;;sFAC5B,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAW;;;;;;;;;;;;;;;;;;;;;;;;8DAKnC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAQ;;;;;;sEACvB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,6LAAC,oIAAA,CAAA,QAAK;oEACJ,IAAG;oEACH,aAAY;oEACZ,OAAO,SAAS,eAAe;oEAC/B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;gFAAE,GAAG,IAAI;gFAAE,iBAAiB,EAAE,MAAM,CAAC,KAAK;4EAAC,CAAC;oEAClF,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sDAMlB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAS;oDACT,UAAU;oDACV,SAAQ;oDACR,WAAU;;sEAEV,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;wDACrB,oBAAoB,wBAAwB;;;;;;;gDAG9C,0BACC,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAS;oDACT,UAAU;oDACV,WAAU;;sEAEV,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDACf,YAAY,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAW3C,CAAC,YAAY,cAAc,MAAM,GAAG,CAAC,mBACpC,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;4BAEZ,0BACC,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;gDAA2B;;;;;;;;;;;;kDAIrD,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;;gEAAe,SAAS,WAAW;gEAAC;;;;;;;;;;;;;8DAEtD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;;gEAAe,SAAS,0BAA0B;gEAAC;;;;;;;;;;;;;8DAErE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;;gEAAc;gEAAE,SAAS,cAAc;;;;;;;;;;;;;gDAExD,SAAS,gBAAgB,GAAG,mBAC3B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;;gEAA+B,SAAS,gBAAgB;gEAAC;;;;;;;;;;;;;8DAG7E,6LAAC;;;;;8DACD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAK;;;;;;sEACN,6LAAC;4DAAK,WAAU;;gEAAiB;gEAAE,KAAK,KAAK,CAAC,SAAS,cAAc,GAAG,SAAS,gBAAgB;;;;;;;;;;;;;8DAEnG,6LAAC;oDAAI,WAAU;;wDACZ,SAAS,iBAAiB;wDAAC;;;;;;;;;;;;;;;;;;;;;;;;4BAQrC,cAAc,MAAM,GAAG,mBACtB,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,iNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;gDAA0B;;;;;;;;;;;;kDAIpD,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;sDACZ,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,QAAa,sBAC3C,6LAAC;oDAAuB,QAAQ;mDAAf;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYnC,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;sCACpD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,mIAAA,CAAA,OAAI;8CACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;0DACf,6LAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;;8CAGjC,6LAAC,mIAAA,CAAA,OAAI;8CACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,6LAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;;8CAGjC,6LAAC,mIAAA,CAAA,OAAI;8CACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,6LAAC,iNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,6LAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQvC,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;sCACpD,6LAAC;4BAAI,WAAU;sCACZ;gCACC;gCAAe;gCAAgB;gCAAS;gCACxC;gCAAe;gCAAc;gCAAc;6BAC5C,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,6LAAC,mIAAA,CAAA,OAAI;oCAAa,WAAU;8CAC1B,cAAA,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;gDAAK,WAAU;0DAAuB;;;;;;;;;;;;mCAHhC;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYzB;GA5XM;KAAA;uCA8XS", "debugId": null}}]}