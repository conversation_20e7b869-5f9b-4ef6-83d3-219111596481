{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/custom.css"], "sourcesContent": ["/* TravelAllinOne Custom Styles */\n\n/* Enhanced <PERSON><PERSON> */\n.btn-travel {\n  position: relative;\n  overflow: hidden;\n  transition: all 0.3s ease;\n}\n\n.btn-travel::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n  transition: left 0.5s;\n}\n\n.btn-travel:hover::before {\n  left: 100%;\n}\n\n/* Card Hover Effects */\n.card-hover {\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.card-hover:hover {\n  transform: translateY(-8px) scale(1.02);\n  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);\n}\n\n/* Gradient Backgrounds */\n.bg-travel-gradient {\n  background: linear-gradient(135deg, \n    hsl(197, 71%, 73%) 0%, \n    hsl(33, 100%, 85%) 50%, \n    hsl(197, 71%, 83%) 100%);\n}\n\n.bg-travel-dark-gradient {\n  background: linear-gradient(135deg, \n    hsl(0, 0%, 10%) 0%, \n    hsl(0, 0%, 20%) 50%, \n    hsl(0, 0%, 15%) 100%);\n}\n\n/* Text Animations */\n.text-shimmer {\n  background: linear-gradient(\n    90deg,\n    hsl(197, 71%, 73%) 0%,\n    hsl(33, 100%, 85%) 50%,\n    hsl(197, 71%, 73%) 100%\n  );\n  background-size: 200% 100%;\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  animation: shimmer 3s ease-in-out infinite;\n}\n\n@keyframes shimmer {\n  0%, 100% {\n    background-position: 200% 0;\n  }\n  50% {\n    background-position: -200% 0;\n  }\n}\n\n/* Loading States */\n.skeleton {\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n  background-size: 200% 100%;\n  animation: loading 1.5s infinite;\n}\n\n@keyframes loading {\n  0% {\n    background-position: 200% 0;\n  }\n  100% {\n    background-position: -200% 0;\n  }\n}\n\n/* Custom Scrollbar */\n::-webkit-scrollbar {\n  width: 8px;\n}\n\n::-webkit-scrollbar-track {\n  background: hsl(240, 5%, 96%);\n}\n\n::-webkit-scrollbar-thumb {\n  background: linear-gradient(135deg, hsl(197, 71%, 73%), hsl(33, 100%, 85%));\n  border-radius: 4px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: linear-gradient(135deg, hsl(197, 71%, 63%), hsl(33, 100%, 75%));\n}\n\n/* Form Enhancements */\n.form-group {\n  position: relative;\n}\n\n.form-floating-label {\n  position: absolute;\n  top: 12px;\n  left: 12px;\n  color: hsl(0, 0%, 45%);\n  transition: all 0.3s ease;\n  pointer-events: none;\n}\n\n.form-input:focus + .form-floating-label,\n.form-input:not(:placeholder-shown) + .form-floating-label {\n  top: -8px;\n  left: 8px;\n  font-size: 12px;\n  color: hsl(197, 71%, 73%);\n  background: white;\n  padding: 0 4px;\n}\n\n/* Service Icons */\n.service-icon {\n  background: linear-gradient(135deg, hsl(197, 71%, 73%), hsl(33, 100%, 85%));\n  transition: all 0.3s ease;\n}\n\n.service-icon:hover {\n  background: linear-gradient(135deg, hsl(197, 71%, 63%), hsl(33, 100%, 75%));\n  transform: rotate(5deg) scale(1.1);\n}\n\n/* Navigation Enhancements */\n.nav-link {\n  position: relative;\n  overflow: hidden;\n}\n\n.nav-link::after {\n  content: '';\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  width: 0;\n  height: 2px;\n  background: linear-gradient(90deg, hsl(197, 71%, 73%), hsl(33, 100%, 85%));\n  transition: width 0.3s ease;\n}\n\n.nav-link:hover::after {\n  width: 100%;\n}\n\n/* Modal and Dialog Enhancements */\n.modal-backdrop {\n  background: rgba(0, 0, 0, 0.5);\n  backdrop-filter: blur(4px);\n}\n\n.modal-content {\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n/* Toast Notifications */\n.toast-success {\n  background: linear-gradient(135deg, hsl(142, 76%, 36%), hsl(142, 76%, 46%));\n  color: white;\n}\n\n.toast-error {\n  background: linear-gradient(135deg, hsl(0, 84%, 60%), hsl(0, 84%, 70%));\n  color: white;\n}\n\n.toast-info {\n  background: linear-gradient(135deg, hsl(197, 71%, 73%), hsl(197, 71%, 83%));\n  color: white;\n}\n\n/* Search Results */\n.search-result-item {\n  transition: all 0.3s ease;\n  border-left: 4px solid transparent;\n}\n\n.search-result-item:hover {\n  border-left-color: hsl(197, 71%, 73%);\n  background: linear-gradient(90deg, rgba(197, 71%, 73%, 0.05), transparent);\n  transform: translateX(4px);\n}\n\n/* Price Tags */\n.price-tag {\n  background: linear-gradient(135deg, hsl(142, 76%, 36%), hsl(142, 76%, 46%));\n  color: white;\n  padding: 4px 12px;\n  border-radius: 20px;\n  font-weight: 600;\n  font-size: 14px;\n}\n\n.price-tag-large {\n  background: linear-gradient(135deg, hsl(197, 71%, 73%), hsl(33, 100%, 85%));\n  color: hsl(0, 0%, 10%);\n  padding: 8px 16px;\n  border-radius: 25px;\n  font-weight: 700;\n  font-size: 18px;\n}\n\n/* Status Badges */\n.status-confirmed {\n  background: linear-gradient(135deg, hsl(142, 76%, 36%), hsl(142, 76%, 46%));\n  color: white;\n}\n\n.status-pending {\n  background: linear-gradient(135deg, hsl(33, 100%, 75%), hsl(33, 100%, 85%));\n  color: hsl(0, 0%, 10%);\n}\n\n.status-cancelled {\n  background: linear-gradient(135deg, hsl(0, 84%, 60%), hsl(0, 84%, 70%));\n  color: white;\n}\n\n/* Mobile Optimizations */\n@media (max-width: 768px) {\n  .hero-section {\n    padding: 3rem 1rem;\n  }\n  \n  .service-card {\n    margin-bottom: 1.5rem;\n  }\n  \n  .btn-travel {\n    padding: 12px 24px;\n    font-size: 16px;\n  }\n  \n  .text-shimmer {\n    font-size: 2rem;\n  }\n}\n\n/* Dark Mode Enhancements */\n@media (prefers-color-scheme: dark) {\n  .glass-effect {\n    background: rgba(0, 0, 0, 0.3);\n    border: 1px solid rgba(255, 255, 255, 0.1);\n  }\n  \n  .modal-content {\n    background: rgba(0, 0, 0, 0.8);\n    border: 1px solid rgba(255, 255, 255, 0.1);\n  }\n}\n\n/* Accessibility Improvements */\n@media (prefers-reduced-motion: reduce) {\n  .hover-lift,\n  .btn-travel,\n  .service-icon,\n  .card-hover,\n  .search-result-item {\n    transition: none;\n  }\n  \n  .text-shimmer,\n  .skeleton,\n  .animate-float,\n  .animate-pulse-slow {\n    animation: none;\n  }\n}\n\n/* Focus States */\n.btn-travel:focus,\n.form-input:focus {\n  outline: 2px solid hsl(197, 71%, 73%);\n  outline-offset: 2px;\n}\n\n/* Print Styles */\n@media print {\n  .hero-section,\n  .bg-gradient-primary,\n  .bg-gradient-secondary {\n    background: white !important;\n    color: black !important;\n  }\n  \n  .shadow-travel,\n  .shadow-travel-lg,\n  .shadow-sky-blue,\n  .shadow-orange {\n    box-shadow: none !important;\n  }\n}\n"], "names": [], "mappings": "AAGA;;;;;;AAMA;;;;;;;;;;;AAWA;;;;AAKA;;;;AAIA;;;;;AAMA;;;;AAOA;;;;AAQA;;;;;;;;AAcA;;;;;;;;;;AAUA;;;;;AAMA;;;;;;;;;;AAUA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAKA;;;;AAIA;;;;;;;;;AASA;;;;;;;;;AAWA;;;;;AAKA;;;;;AAMA;;;;;AAKA;;;;;;;;;;;AAWA;;;;AAKA;;;;;AAKA;;;;;;AAOA;;;;;AAKA;;;;;AAKA;;;;;AAMA;;;;;AAKA;;;;;;AAOA;;;;;;;;;AASA;;;;;;;;;AAUA;;;;;AAKA;;;;;AAKA;;;;;AAMA;EACE;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;AAMF;EACE;;;;;EAKA;;;;;;AAOF;EACE;;;;EAQA;;;;;AASF;;;;;AAOA;EACE;;;;;EAOA"}}]}