{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/TravelAllinOne/frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/TravelAllinOne/frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/TravelAllinOne/frontend/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,6LAAC,+KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MAlBS;AAoBT,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,6LAAC,+KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,6LAAC,+KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;MAxBS;AA0BT,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,6MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;MAtBS;AAwBT,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAlBS;AAoBT,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;OAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS;AAgBT,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,6LAAC,+KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;OAJS;AAMT,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,6NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;OAtBS;AAwBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS", "debugId": null}}, {"offset": {"line": 385, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/TravelAllinOne/frontend/src/lib/api.ts"], "sourcesContent": ["// API client for TravelAllinOne backend services\n\nimport axios, { AxiosInstance, AxiosResponse } from 'axios';\n\n// API Configuration\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';\n\n// Create axios instance\nconst apiClient: AxiosInstance = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 30000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Request interceptor to add auth token\napiClient.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('auth_token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor for error handling\napiClient.interceptors.response.use(\n  (response: AxiosResponse) => response,\n  (error) => {\n    if (error.response?.status === 401) {\n      // Clear token and redirect to login\n      localStorage.removeItem('auth_token');\n      localStorage.removeItem('user_data');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\n// Types\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  message: string;\n  data?: T;\n  errors?: string[];\n}\n\nexport interface User {\n  id: string;\n  email: string;\n  first_name: string;\n  last_name: string;\n  phone: string;\n  role: string;\n  is_active: boolean;\n  is_verified: boolean;\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface LoginRequest {\n  email: string;\n  password: string;\n}\n\nexport interface RegisterRequest {\n  email: string;\n  phone: string;\n  first_name: string;\n  last_name: string;\n  password: string;\n  role?: string;\n}\n\nexport interface BusSearchRequest {\n  source_city: string;\n  destination_city: string;\n  travel_date: string;\n  passenger_count: number;\n  bus_type?: string;\n  max_price?: number;\n}\n\nexport interface CarPoolSearchRequest {\n  source_city: string;\n  destination_city: string;\n  travel_date: string;\n  passenger_count: number;\n  max_price_per_seat?: number;\n}\n\nexport interface BikeRideRequest {\n  pickup_location: {\n    latitude: number;\n    longitude: number;\n    address: string;\n    city: string;\n    state: string;\n    pincode: string;\n  };\n  drop_location: {\n    latitude: number;\n    longitude: number;\n    address: string;\n    city: string;\n    state: string;\n    pincode: string;\n  };\n  ride_type: string;\n  passenger_phone: string;\n}\n\n// Auth API\nexport const authApi = {\n  login: (data: LoginRequest): Promise<AxiosResponse<ApiResponse>> =>\n    apiClient.post('/api/auth/login', data),\n  \n  register: (data: RegisterRequest): Promise<AxiosResponse<ApiResponse>> =>\n    apiClient.post('/api/auth/register', data),\n  \n  getProfile: (): Promise<AxiosResponse<ApiResponse<{ user: User }>>> =>\n    apiClient.get('/api/auth/profile'),\n  \n  updateProfile: (data: Partial<User>): Promise<AxiosResponse<ApiResponse>> =>\n    apiClient.put('/api/auth/profile', data),\n};\n\n// Bus API\nexport const busApi = {\n  search: (data: BusSearchRequest): Promise<AxiosResponse<ApiResponse>> =>\n    apiClient.post('/api/bus/search', data),\n  \n  getSeatLayout: (busId: string, travelDate: string): Promise<AxiosResponse<ApiResponse>> =>\n    apiClient.get(`/api/bus/seat-layout/${busId}?travel_date=${travelDate}`),\n  \n  book: (data: any): Promise<AxiosResponse<ApiResponse>> =>\n    apiClient.post('/api/bus/book', data),\n};\n\n// CarPool API\nexport const carPoolApi = {\n  search: (data: CarPoolSearchRequest): Promise<AxiosResponse<ApiResponse>> =>\n    apiClient.post('/api/carpool/search', data),\n  \n  createTrip: (data: any): Promise<AxiosResponse<ApiResponse>> =>\n    apiClient.post('/api/carpool/trips', data),\n  \n  book: (data: any): Promise<AxiosResponse<ApiResponse>> =>\n    apiClient.post('/api/carpool/book', data),\n};\n\n// Bike API\nexport const bikeApi = {\n  getEstimate: (data: BikeRideRequest): Promise<AxiosResponse<ApiResponse>> =>\n    apiClient.post('/api/bike/estimate', data),\n  \n  getNearbyDrivers: (lat: number, lng: number, radius?: number): Promise<AxiosResponse<ApiResponse>> =>\n    apiClient.get(`/api/bike/nearby-drivers?lat=${lat}&lng=${lng}&radius=${radius || 5}`),\n  \n  book: (data: BikeRideRequest): Promise<AxiosResponse<ApiResponse>> =>\n    apiClient.post('/api/bike/book', data),\n};\n\n// Payment API\nexport const paymentApi = {\n  initiate: (data: any): Promise<AxiosResponse<ApiResponse>> =>\n    apiClient.post('/api/payment/initiate', data),\n  \n  verify: (data: any): Promise<AxiosResponse<ApiResponse>> =>\n    apiClient.post('/api/payment/verify', data),\n  \n  getHistory: (params?: any): Promise<AxiosResponse<ApiResponse>> =>\n    apiClient.get('/api/payment/history', { params }),\n  \n  refund: (paymentId: string, data: any): Promise<AxiosResponse<ApiResponse>> =>\n    apiClient.post(`/api/payment/refund/${paymentId}`, data),\n};\n\n// Chatbot API\nexport const chatbotApi = {\n  sendMessage: (data: any): Promise<AxiosResponse<ApiResponse>> =>\n    apiClient.post('/api/chatbot/chat', data),\n  \n  addKnowledgeBase: (data: any): Promise<AxiosResponse<ApiResponse>> =>\n    apiClient.post('/api/chatbot/knowledge-base', data),\n};\n\n// Utility functions\nexport const setAuthToken = (token: string) => {\n  localStorage.setItem('auth_token', token);\n};\n\nexport const getAuthToken = (): string | null => {\n  return localStorage.getItem('auth_token');\n};\n\nexport const clearAuthToken = () => {\n  localStorage.removeItem('auth_token');\n  localStorage.removeItem('user_data');\n};\n\nexport const setUserData = (user: User) => {\n  localStorage.setItem('user_data', JSON.stringify(user));\n};\n\nexport const getUserData = (): User | null => {\n  const userData = localStorage.getItem('user_data');\n  return userData ? JSON.parse(userData) : null;\n};\n\nexport default apiClient;\n"], "names": [], "mappings": "AAAA,iDAAiD;;;;;;;;;;;;;;;AAK5B;AAHrB;;AAEA,oBAAoB;AACpB,MAAM,eAAe,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI;AAExD,wBAAwB;AACxB,MAAM,YAA2B,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC5C,SAAS;IACT,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,wCAAwC;AACxC,UAAU,YAAY,CAAC,OAAO,CAAC,GAAG,CAChC,CAAC;IACC,MAAM,QAAQ,aAAa,OAAO,CAAC;IACnC,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IACA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,0CAA0C;AAC1C,UAAU,YAAY,CAAC,QAAQ,CAAC,GAAG,CACjC,CAAC,WAA4B,UAC7B,CAAC;IACC,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;QAClC,oCAAoC;QACpC,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IACA,OAAO,QAAQ,MAAM,CAAC;AACxB;AA6EK,MAAM,UAAU;IACrB,OAAO,CAAC,OACN,UAAU,IAAI,CAAC,mBAAmB;IAEpC,UAAU,CAAC,OACT,UAAU,IAAI,CAAC,sBAAsB;IAEvC,YAAY,IACV,UAAU,GAAG,CAAC;IAEhB,eAAe,CAAC,OACd,UAAU,GAAG,CAAC,qBAAqB;AACvC;AAGO,MAAM,SAAS;IACpB,QAAQ,CAAC,OACP,UAAU,IAAI,CAAC,mBAAmB;IAEpC,eAAe,CAAC,OAAe,aAC7B,UAAU,GAAG,CAAC,CAAC,qBAAqB,EAAE,MAAM,aAAa,EAAE,YAAY;IAEzE,MAAM,CAAC,OACL,UAAU,IAAI,CAAC,iBAAiB;AACpC;AAGO,MAAM,aAAa;IACxB,QAAQ,CAAC,OACP,UAAU,IAAI,CAAC,uBAAuB;IAExC,YAAY,CAAC,OACX,UAAU,IAAI,CAAC,sBAAsB;IAEvC,MAAM,CAAC,OACL,UAAU,IAAI,CAAC,qBAAqB;AACxC;AAGO,MAAM,UAAU;IACrB,aAAa,CAAC,OACZ,UAAU,IAAI,CAAC,sBAAsB;IAEvC,kBAAkB,CAAC,KAAa,KAAa,SAC3C,UAAU,GAAG,CAAC,CAAC,6BAA6B,EAAE,IAAI,KAAK,EAAE,IAAI,QAAQ,EAAE,UAAU,GAAG;IAEtF,MAAM,CAAC,OACL,UAAU,IAAI,CAAC,kBAAkB;AACrC;AAGO,MAAM,aAAa;IACxB,UAAU,CAAC,OACT,UAAU,IAAI,CAAC,yBAAyB;IAE1C,QAAQ,CAAC,OACP,UAAU,IAAI,CAAC,uBAAuB;IAExC,YAAY,CAAC,SACX,UAAU,GAAG,CAAC,wBAAwB;YAAE;QAAO;IAEjD,QAAQ,CAAC,WAAmB,OAC1B,UAAU,IAAI,CAAC,CAAC,oBAAoB,EAAE,WAAW,EAAE;AACvD;AAGO,MAAM,aAAa;IACxB,aAAa,CAAC,OACZ,UAAU,IAAI,CAAC,qBAAqB;IAEtC,kBAAkB,CAAC,OACjB,UAAU,IAAI,CAAC,+BAA+B;AAClD;AAGO,MAAM,eAAe,CAAC;IAC3B,aAAa,OAAO,CAAC,cAAc;AACrC;AAEO,MAAM,eAAe;IAC1B,OAAO,aAAa,OAAO,CAAC;AAC9B;AAEO,MAAM,iBAAiB;IAC5B,aAAa,UAAU,CAAC;IACxB,aAAa,UAAU,CAAC;AAC1B;AAEO,MAAM,cAAc,CAAC;IAC1B,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;AACnD;AAEO,MAAM,cAAc;IACzB,MAAM,WAAW,aAAa,OAAO,CAAC;IACtC,OAAO,WAAW,KAAK,KAAK,CAAC,YAAY;AAC3C;uCAEe", "debugId": null}}, {"offset": {"line": 493, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/TravelAllinOne/frontend/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { useRouter } from 'next/navigation';\nimport { Button } from '@/components/ui/button';\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu';\nimport { \n  Bus, \n  Car, \n  Bike, \n  User, \n  LogOut, \n  Settings, \n  Menu,\n  X\n} from 'lucide-react';\nimport { getUserData, clearAuthToken } from '@/lib/api';\n\nconst Header = () => {\n  const [user, setUser] = useState<any>(null);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const router = useRouter();\n\n  useEffect(() => {\n    const userData = getUserData();\n    setUser(userData);\n  }, []);\n\n  const handleLogout = () => {\n    clearAuthToken();\n    setUser(null);\n    router.push('/');\n  };\n\n  const navigationItems = [\n    { href: '/bus', label: 'Bus Booking', icon: Bus },\n    { href: '/carpool', label: 'Car Pool', icon: Car },\n    { href: '/bike', label: 'Bike Rides', icon: Bike },\n  ];\n\n  return (\n    <header className=\"navbar shadow-travel border-b border-sky-blue/20 sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-3 hover-lift\">\n              <div className=\"v-logo\">\n                <span className=\"v-letter\">V</span>\n              </div>\n              <span className=\"text-xl font-bold gradient-text\">Vtravelallinone</span>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-8\">\n            {navigationItems.map((item) => {\n              const IconComponent = item.icon;\n              return (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className=\"flex items-center space-x-1 text-travel-black hover:text-sky-blue transition-all duration-300 hover-lift font-medium\"\n                >\n                  <IconComponent className=\"w-4 h-4\" />\n                  <span>{item.label}</span>\n                </Link>\n              );\n            })}\n          </nav>\n\n          {/* User Menu / Auth Buttons */}\n          <div className=\"flex items-center space-x-4\">\n            {user ? (\n              <DropdownMenu>\n                <DropdownMenuTrigger asChild>\n                  <Button variant=\"ghost\" className=\"flex items-center space-x-2 hover:bg-sky-blue/10 text-travel-black\">\n                    <div className=\"v-logo-minimal\">\n                      <User className=\"w-3 h-3 text-white\" />\n                    </div>\n                    <span className=\"hidden sm:inline font-medium\">{user.first_name}</span>\n                  </Button>\n                </DropdownMenuTrigger>\n                <DropdownMenuContent align=\"end\" className=\"w-56\">\n                  <DropdownMenuLabel>My Account</DropdownMenuLabel>\n                  <DropdownMenuSeparator />\n                  <DropdownMenuItem asChild>\n                    <Link href=\"/profile\" className=\"flex items-center\">\n                      <User className=\"w-4 h-4 mr-2\" />\n                      Profile\n                    </Link>\n                  </DropdownMenuItem>\n                  <DropdownMenuItem asChild>\n                    <Link href=\"/bookings\" className=\"flex items-center\">\n                      <Settings className=\"w-4 h-4 mr-2\" />\n                      My Bookings\n                    </Link>\n                  </DropdownMenuItem>\n                  <DropdownMenuSeparator />\n                  <DropdownMenuItem onClick={handleLogout} className=\"text-red-600\">\n                    <LogOut className=\"w-4 h-4 mr-2\" />\n                    Logout\n                  </DropdownMenuItem>\n                </DropdownMenuContent>\n              </DropdownMenu>\n            ) : (\n              <div className=\"flex items-center space-x-2\">\n                <Button variant=\"ghost\" className=\"text-travel-black hover:bg-sky-blue/10\" asChild>\n                  <Link href=\"/login\">Login</Link>\n                </Button>\n                <Button className=\"btn-primary hover-lift shadow-sky-blue\" asChild>\n                  <Link href=\"/register\">Sign Up</Link>\n                </Button>\n              </div>\n            )}\n\n            {/* Mobile Menu Button */}\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              className=\"md:hidden\"\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            >\n              {isMobileMenuOpen ? (\n                <X className=\"w-5 h-5\" />\n              ) : (\n                <Menu className=\"w-5 h-5\" />\n              )}\n            </Button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMobileMenuOpen && (\n          <div className=\"md:hidden py-4 border-t\">\n            <nav className=\"flex flex-col space-y-2\">\n              {navigationItems.map((item) => {\n                const IconComponent = item.icon;\n                return (\n                  <Link\n                    key={item.href}\n                    href={item.href}\n                    className=\"flex items-center space-x-2 px-3 py-2 text-gray-600 hover:text-blue-600 hover:bg-gray-50 rounded-md transition-colors\"\n                    onClick={() => setIsMobileMenuOpen(false)}\n                  >\n                    <IconComponent className=\"w-4 h-4\" />\n                    <span>{item.label}</span>\n                  </Link>\n                );\n              })}\n            </nav>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAQA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;;;AAxBA;;;;;;;;AA0BA,MAAM,SAAS;;IACb,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IACtC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM,WAAW,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD;YAC3B,QAAQ;QACV;2BAAG,EAAE;IAEL,MAAM,eAAe;QACnB,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD;QACb,QAAQ;QACR,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,kBAAkB;QACtB;YAAE,MAAM;YAAQ,OAAO;YAAe,MAAM,mMAAA,CAAA,MAAG;QAAC;QAChD;YAAE,MAAM;YAAY,OAAO;YAAY,MAAM,mMAAA,CAAA,MAAG;QAAC;QACjD;YAAE,MAAM;YAAS,OAAO;YAAc,MAAM,qMAAA,CAAA,OAAI;QAAC;KAClD;IAED,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAAW;;;;;;;;;;;kDAE7B,6LAAC;wCAAK,WAAU;kDAAkC;;;;;;;;;;;;;;;;;sCAKtD,6LAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC;gCACpB,MAAM,gBAAgB,KAAK,IAAI;gCAC/B,qBACE,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;;sDAEV,6LAAC;4CAAc,WAAU;;;;;;sDACzB,6LAAC;sDAAM,KAAK,KAAK;;;;;;;mCALZ,KAAK,IAAI;;;;;4BAQpB;;;;;;sCAIF,6LAAC;4BAAI,WAAU;;gCACZ,qBACC,6LAAC,+IAAA,CAAA,eAAY;;sDACX,6LAAC,+IAAA,CAAA,sBAAmB;4CAAC,OAAO;sDAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,WAAU;;kEAChC,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;kEAElB,6LAAC;wDAAK,WAAU;kEAAgC,KAAK,UAAU;;;;;;;;;;;;;;;;;sDAGnE,6LAAC,+IAAA,CAAA,sBAAmB;4CAAC,OAAM;4CAAM,WAAU;;8DACzC,6LAAC,+IAAA,CAAA,oBAAiB;8DAAC;;;;;;8DACnB,6LAAC,+IAAA,CAAA,wBAAqB;;;;;8DACtB,6LAAC,+IAAA,CAAA,mBAAgB;oDAAC,OAAO;8DACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;;0EAC9B,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;8DAIrC,6LAAC,+IAAA,CAAA,mBAAgB;oDAAC,OAAO;8DACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAY,WAAU;;0EAC/B,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;8DAIzC,6LAAC,+IAAA,CAAA,wBAAqB;;;;;8DACtB,6LAAC,+IAAA,CAAA,mBAAgB;oDAAC,SAAS;oDAAc,WAAU;;sEACjD,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;yDAMzC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,WAAU;4CAAyC,OAAO;sDAChF,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;0DAAS;;;;;;;;;;;sDAEtB,6LAAC,qIAAA,CAAA,SAAM;4CAAC,WAAU;4CAAyC,OAAO;sDAChE,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;0DAAY;;;;;;;;;;;;;;;;;8CAM7B,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,oBAAoB,CAAC;8CAEnC,iCACC,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;6DAEb,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gBAOvB,kCACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ,gBAAgB,GAAG,CAAC,CAAC;4BACpB,MAAM,gBAAgB,KAAK,IAAI;4BAC/B,qBACE,6LAAC,+JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAU;gCACV,SAAS,IAAM,oBAAoB;;kDAEnC,6LAAC;wCAAc,WAAU;;;;;;kDACzB,6LAAC;kDAAM,KAAK,KAAK;;;;;;;+BANZ,KAAK,IAAI;;;;;wBASpB;;;;;;;;;;;;;;;;;;;;;;AAOd;GA1IM;;QAGW,qIAAA,CAAA,YAAS;;;KAHpB;uCA4IS", "debugId": null}}, {"offset": {"line": 929, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/TravelAllinOne/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 1044, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/TravelAllinOne/frontend/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 1076, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/TravelAllinOne/frontend/src/components/Chatbot.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Input } from '@/components/ui/input';\nimport { MessageCircle, Send, X, Bot, User } from 'lucide-react';\nimport { chatbotApi } from '@/lib/api';\nimport { toast } from 'sonner';\n\ninterface Message {\n  id: string;\n  content: string;\n  isBot: boolean;\n  timestamp: Date;\n  suggestions?: string[];\n}\n\nconst Chatbot = () => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [messages, setMessages] = useState<Message[]>([\n    {\n      id: '1',\n      content: 'Hello! I\\'m your TravelAllinOne assistant. How can I help you today?',\n      isBot: true,\n      timestamp: new Date(),\n      suggestions: [\n        'How to book a bus ticket?',\n        'Tell me about car pooling',\n        'What are bike ride charges?',\n        'How to cancel my booking?'\n      ]\n    }\n  ]);\n  const [inputMessage, setInputMessage] = useState('');\n  const [isTyping, setIsTyping] = useState(false);\n  const [sessionId, setSessionId] = useState<string | null>(null);\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  const sendMessage = async (message: string) => {\n    if (!message.trim()) return;\n\n    // Add user message\n    const userMessage: Message = {\n      id: Date.now().toString(),\n      content: message,\n      isBot: false,\n      timestamp: new Date()\n    };\n\n    setMessages(prev => [...prev, userMessage]);\n    setInputMessage('');\n    setIsTyping(true);\n\n    try {\n      const response = await chatbotApi.sendMessage({\n        message,\n        session_id: sessionId,\n        context: {\n          current_page: window.location.pathname\n        }\n      });\n\n      if (response.data.success) {\n        const { message: botResponse, session_id: newSessionId, suggestions } = response.data.data;\n        \n        if (newSessionId && !sessionId) {\n          setSessionId(newSessionId);\n        }\n\n        // Add bot message\n        const botMessage: Message = {\n          id: (Date.now() + 1).toString(),\n          content: botResponse,\n          isBot: true,\n          timestamp: new Date(),\n          suggestions: suggestions || []\n        };\n\n        setMessages(prev => [...prev, botMessage]);\n      } else {\n        throw new Error('Failed to get response');\n      }\n    } catch (error) {\n      console.error('Chat error:', error);\n      const errorMessage: Message = {\n        id: (Date.now() + 1).toString(),\n        content: 'Sorry, I\\'m having trouble responding right now. Please try again later.',\n        isBot: true,\n        timestamp: new Date()\n      };\n      setMessages(prev => [...prev, errorMessage]);\n    } finally {\n      setIsTyping(false);\n    }\n  };\n\n  const handleSuggestionClick = (suggestion: string) => {\n    sendMessage(suggestion);\n  };\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    sendMessage(inputMessage);\n  };\n\n  const formatTime = (date: Date) => {\n    return date.toLocaleTimeString('en-IN', { \n      hour: '2-digit', \n      minute: '2-digit' \n    });\n  };\n\n  if (!isOpen) {\n    return (\n      <div className=\"fixed bottom-6 right-6 z-50\">\n        <Button\n          onClick={() => setIsOpen(true)}\n          className=\"w-14 h-14 rounded-full bg-blue-600 hover:bg-blue-700 shadow-lg\"\n        >\n          <MessageCircle className=\"w-6 h-6\" />\n        </Button>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"fixed bottom-6 right-6 z-50\">\n      <Card className=\"w-80 h-96 shadow-xl\">\n        <CardHeader className=\"bg-blue-600 text-white rounded-t-lg\">\n          <div className=\"flex items-center justify-between\">\n            <CardTitle className=\"text-lg flex items-center gap-2\">\n              <Bot className=\"w-5 h-5\" />\n              TravelBot\n            </CardTitle>\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => setIsOpen(false)}\n              className=\"text-white hover:bg-blue-700\"\n            >\n              <X className=\"w-4 h-4\" />\n            </Button>\n          </div>\n        </CardHeader>\n        \n        <CardContent className=\"p-0 flex flex-col h-80\">\n          {/* Messages Area */}\n          <div className=\"flex-1 overflow-y-auto p-4 space-y-3\">\n            {messages.map((message) => (\n              <div key={message.id} className=\"space-y-2\">\n                <div className={`flex ${message.isBot ? 'justify-start' : 'justify-end'}`}>\n                  <div className={`flex items-start gap-2 max-w-[80%] ${\n                    message.isBot ? 'flex-row' : 'flex-row-reverse'\n                  }`}>\n                    <div className={`w-6 h-6 rounded-full flex items-center justify-center ${\n                      message.isBot ? 'bg-blue-100' : 'bg-gray-100'\n                    }`}>\n                      {message.isBot ? (\n                        <Bot className=\"w-3 h-3 text-blue-600\" />\n                      ) : (\n                        <User className=\"w-3 h-3 text-gray-600\" />\n                      )}\n                    </div>\n                    <div className={`rounded-lg p-3 ${\n                      message.isBot \n                        ? 'bg-gray-100 text-gray-800' \n                        : 'bg-blue-600 text-white'\n                    }`}>\n                      <p className=\"text-sm\">{message.content}</p>\n                      <p className={`text-xs mt-1 ${\n                        message.isBot ? 'text-gray-500' : 'text-blue-100'\n                      }`}>\n                        {formatTime(message.timestamp)}\n                      </p>\n                    </div>\n                  </div>\n                </div>\n                \n                {/* Suggestions */}\n                {message.isBot && message.suggestions && message.suggestions.length > 0 && (\n                  <div className=\"flex flex-wrap gap-1 ml-8\">\n                    {message.suggestions.map((suggestion, index) => (\n                      <Button\n                        key={index}\n                        variant=\"outline\"\n                        size=\"sm\"\n                        onClick={() => handleSuggestionClick(suggestion)}\n                        className=\"text-xs h-7 px-2\"\n                      >\n                        {suggestion}\n                      </Button>\n                    ))}\n                  </div>\n                )}\n              </div>\n            ))}\n            \n            {/* Typing Indicator */}\n            {isTyping && (\n              <div className=\"flex justify-start\">\n                <div className=\"flex items-start gap-2\">\n                  <div className=\"w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center\">\n                    <Bot className=\"w-3 h-3 text-blue-600\" />\n                  </div>\n                  <div className=\"bg-gray-100 rounded-lg p-3\">\n                    <div className=\"flex space-x-1\">\n                      <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"></div>\n                      <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n                      <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n            \n            <div ref={messagesEndRef} />\n          </div>\n          \n          {/* Input Area */}\n          <div className=\"border-t p-3\">\n            <form onSubmit={handleSubmit} className=\"flex gap-2\">\n              <Input\n                value={inputMessage}\n                onChange={(e) => setInputMessage(e.target.value)}\n                placeholder=\"Type your message...\"\n                className=\"flex-1\"\n                disabled={isTyping}\n              />\n              <Button \n                type=\"submit\" \n                size=\"sm\"\n                disabled={!inputMessage.trim() || isTyping}\n              >\n                <Send className=\"w-4 h-4\" />\n              </Button>\n            </form>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  );\n};\n\nexport default Chatbot;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAPA;;;;;;;AAkBA,MAAM,UAAU;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;QAClD;YACE,IAAI;YACJ,SAAS;YACT,OAAO;YACP,WAAW,IAAI;YACf,aAAa;gBACX;gBACA;gBACA;gBACA;aACD;QACH;KACD;IACD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1D,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE9C,MAAM,iBAAiB;QACrB,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR;QACF;4BAAG;QAAC;KAAS;IAEb,MAAM,cAAc,OAAO;QACzB,IAAI,CAAC,QAAQ,IAAI,IAAI;QAErB,mBAAmB;QACnB,MAAM,cAAuB;YAC3B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,SAAS;YACT,OAAO;YACP,WAAW,IAAI;QACjB;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAC1C,gBAAgB;QAChB,YAAY;QAEZ,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,aAAU,CAAC,WAAW,CAAC;gBAC5C;gBACA,YAAY;gBACZ,SAAS;oBACP,cAAc,OAAO,QAAQ,CAAC,QAAQ;gBACxC;YACF;YAEA,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gBACzB,MAAM,EAAE,SAAS,WAAW,EAAE,YAAY,YAAY,EAAE,WAAW,EAAE,GAAG,SAAS,IAAI,CAAC,IAAI;gBAE1F,IAAI,gBAAgB,CAAC,WAAW;oBAC9B,aAAa;gBACf;gBAEA,kBAAkB;gBAClB,MAAM,aAAsB;oBAC1B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;oBAC7B,SAAS;oBACT,OAAO;oBACP,WAAW,IAAI;oBACf,aAAa,eAAe,EAAE;gBAChC;gBAEA,YAAY,CAAA,OAAQ;2BAAI;wBAAM;qBAAW;YAC3C,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,eAAe;YAC7B,MAAM,eAAwB;gBAC5B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,SAAS;gBACT,OAAO;gBACP,WAAW,IAAI;YACjB;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAa;QAC7C,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,YAAY;IACd;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,YAAY;IACd;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,QAAQ;QACV;IACF;IAEA,IAAI,CAAC,QAAQ;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gBACL,SAAS,IAAM,UAAU;gBACzB,WAAU;0BAEV,cAAA,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;;;;;;;;;;;IAIjC;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,6LAAC,mIAAA,CAAA,aAAU;oBAAC,WAAU;8BACpB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAG7B,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,UAAU;gCACzB,WAAU;0CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8BAKnB,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCAErB,6LAAC;4BAAI,WAAU;;gCACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;wCAAqB,WAAU;;0DAC9B,6LAAC;gDAAI,WAAW,CAAC,KAAK,EAAE,QAAQ,KAAK,GAAG,kBAAkB,eAAe;0DACvE,cAAA,6LAAC;oDAAI,WAAW,CAAC,mCAAmC,EAClD,QAAQ,KAAK,GAAG,aAAa,oBAC7B;;sEACA,6LAAC;4DAAI,WAAW,CAAC,sDAAsD,EACrE,QAAQ,KAAK,GAAG,gBAAgB,eAChC;sEACC,QAAQ,KAAK,iBACZ,6LAAC,mMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;qFAEf,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;sEAGpB,6LAAC;4DAAI,WAAW,CAAC,eAAe,EAC9B,QAAQ,KAAK,GACT,8BACA,0BACJ;;8EACA,6LAAC;oEAAE,WAAU;8EAAW,QAAQ,OAAO;;;;;;8EACvC,6LAAC;oEAAE,WAAW,CAAC,aAAa,EAC1B,QAAQ,KAAK,GAAG,kBAAkB,iBAClC;8EACC,WAAW,QAAQ,SAAS;;;;;;;;;;;;;;;;;;;;;;;4CAOpC,QAAQ,KAAK,IAAI,QAAQ,WAAW,IAAI,QAAQ,WAAW,CAAC,MAAM,GAAG,mBACpE,6LAAC;gDAAI,WAAU;0DACZ,QAAQ,WAAW,CAAC,GAAG,CAAC,CAAC,YAAY,sBACpC,6LAAC,qIAAA,CAAA,SAAM;wDAEL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,sBAAsB;wDACrC,WAAU;kEAET;uDANI;;;;;;;;;;;uCAlCL,QAAQ,EAAE;;;;;gCAiDrB,0BACC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;;;;;;0DAEjB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;4DAAkD,OAAO;gEAAE,gBAAgB;4DAAO;;;;;;sEACjG,6LAAC;4DAAI,WAAU;4DAAkD,OAAO;gEAAE,gBAAgB;4DAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAO3G,6LAAC;oCAAI,KAAK;;;;;;;;;;;;sCAIZ,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,UAAU;gCAAc,WAAU;;kDACtC,6LAAC,oIAAA,CAAA,QAAK;wCACJ,OAAO;wCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wCAC/C,aAAY;wCACZ,WAAU;wCACV,UAAU;;;;;;kDAEZ,6LAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,MAAK;wCACL,UAAU,CAAC,aAAa,IAAI,MAAM;kDAElC,cAAA,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhC;GAxOM;KAAA;uCA0OS", "debugId": null}}, {"offset": {"line": 1539, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/TravelAllinOne/frontend/src/components/ui/sonner.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useTheme } from \"next-themes\"\nimport { Toaster as Son<PERSON>, ToasterP<PERSON> } from \"sonner\"\n\nconst Toaster = ({ ...props }: ToasterProps) => {\n  const { theme = \"system\" } = useTheme()\n\n  return (\n    <Sonner\n      theme={theme as ToasterProps[\"theme\"]}\n      className=\"toaster group\"\n      style={\n        {\n          \"--normal-bg\": \"var(--popover)\",\n          \"--normal-text\": \"var(--popover-foreground)\",\n          \"--normal-border\": \"var(--border)\",\n        } as React.CSSProperties\n      }\n      {...props}\n    />\n  )\n}\n\nexport { Toaster }\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKA,MAAM,UAAU,CAAC,EAAE,GAAG,OAAqB;;IACzC,MAAM,EAAE,QAAQ,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAEpC,qBACE,6LAAC,2IAAA,CAAA,UAAM;QACL,OAAO;QACP,WAAU;QACV,OACE;YACE,eAAe;YACf,iBAAiB;YACjB,mBAAmB;QACrB;QAED,GAAG,KAAK;;;;;;AAGf;GAjBM;;QACyB,mJAAA,CAAA,WAAQ;;;KADjC", "debugId": null}}]}