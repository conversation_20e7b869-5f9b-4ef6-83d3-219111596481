{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/globals.css"], "sourcesContent": ["/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */\n@layer properties;\n@layer theme, base, components, utilities;\n@layer theme {\n  :root, :host {\n    --font-sans: ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\",\n      \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\",\n      \"Courier New\", monospace;\n    --color-red-100: oklch(93.6% 0.032 17.717);\n    --color-red-600: oklch(57.7% 0.245 27.325);\n    --color-red-700: oklch(50.5% 0.213 27.518);\n    --color-orange-100: oklch(95.4% 0.038 75.164);\n    --color-orange-500: oklch(70.5% 0.213 47.604);\n    --color-orange-600: oklch(64.6% 0.222 41.116);\n    --color-orange-700: oklch(55.3% 0.195 38.402);\n    --color-orange-800: oklch(47% 0.157 37.304);\n    --color-yellow-100: oklch(97.3% 0.071 103.193);\n    --color-yellow-500: oklch(79.5% 0.184 86.047);\n    --color-yellow-800: oklch(47.6% 0.114 61.907);\n    --color-green-100: oklch(96.2% 0.044 156.743);\n    --color-green-500: oklch(72.3% 0.219 149.579);\n    --color-green-600: oklch(62.7% 0.194 149.214);\n    --color-green-700: oklch(52.7% 0.154 150.069);\n    --color-green-800: oklch(44.8% 0.119 151.328);\n    --color-blue-100: oklch(93.2% 0.032 255.585);\n    --color-blue-500: oklch(62.3% 0.214 259.815);\n    --color-blue-600: oklch(54.6% 0.245 262.881);\n    --color-blue-700: oklch(48.8% 0.243 264.376);\n    --color-blue-800: oklch(42.4% 0.199 265.638);\n    --color-purple-600: oklch(55.8% 0.288 302.321);\n    --color-gray-50: oklch(98.5% 0.002 247.839);\n    --color-gray-100: oklch(96.7% 0.003 264.542);\n    --color-gray-300: oklch(87.2% 0.01 258.338);\n    --color-gray-400: oklch(70.7% 0.022 261.325);\n    --color-gray-500: oklch(55.1% 0.027 264.364);\n    --color-gray-600: oklch(44.6% 0.03 256.802);\n    --color-gray-700: oklch(37.3% 0.034 259.733);\n    --color-gray-800: oklch(27.8% 0.033 256.848);\n    --color-gray-900: oklch(21% 0.034 264.665);\n    --color-black: #000;\n    --color-white: #fff;\n    --spacing: 0.25rem;\n    --container-md: 28rem;\n    --container-lg: 32rem;\n    --container-2xl: 42rem;\n    --container-3xl: 48rem;\n    --container-4xl: 56rem;\n    --container-6xl: 72rem;\n    --container-7xl: 80rem;\n    --text-xs: 0.75rem;\n    --text-xs--line-height: calc(1 / 0.75);\n    --text-sm: 0.875rem;\n    --text-sm--line-height: calc(1.25 / 0.875);\n    --text-base: 1rem;\n    --text-base--line-height: calc(1.5 / 1);\n    --text-lg: 1.125rem;\n    --text-lg--line-height: calc(1.75 / 1.125);\n    --text-xl: 1.25rem;\n    --text-xl--line-height: calc(1.75 / 1.25);\n    --text-2xl: 1.5rem;\n    --text-2xl--line-height: calc(2 / 1.5);\n    --text-3xl: 1.875rem;\n    --text-3xl--line-height: calc(2.25 / 1.875);\n    --text-4xl: 2.25rem;\n    --text-4xl--line-height: calc(2.5 / 2.25);\n    --text-6xl: 3.75rem;\n    --text-6xl--line-height: 1;\n    --font-weight-medium: 500;\n    --font-weight-semibold: 600;\n    --font-weight-bold: 700;\n    --font-weight-black: 900;\n    --tracking-widest: 0.1em;\n    --radius-xs: 0.125rem;\n    --radius-sm: 0.25rem;\n    --radius-md: 0.375rem;\n    --radius-lg: 0.5rem;\n    --radius-xl: 0.75rem;\n    --radius-2xl: 1rem;\n    --radius-3xl: 1.5rem;\n    --animate-spin: spin 1s linear infinite;\n    --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n    --animate-bounce: bounce 1s infinite;\n    --default-transition-duration: 150ms;\n    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n    --default-font-family: var(--font-sans);\n    --default-mono-font-family: var(--font-mono);\n  }\n}\n@layer base {\n  *, ::after, ::before, ::backdrop, ::file-selector-button {\n    box-sizing: border-box;\n    margin: 0;\n    padding: 0;\n    border: 0 solid;\n  }\n  html, :host {\n    line-height: 1.5;\n    -webkit-text-size-adjust: 100%;\n    tab-size: 4;\n    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\");\n    font-feature-settings: var(--default-font-feature-settings, normal);\n    font-variation-settings: var(--default-font-variation-settings, normal);\n    -webkit-tap-highlight-color: transparent;\n  }\n  hr {\n    height: 0;\n    color: inherit;\n    border-top-width: 1px;\n  }\n  abbr:where([title]) {\n    -webkit-text-decoration: underline dotted;\n    text-decoration: underline dotted;\n  }\n  h1, h2, h3, h4, h5, h6 {\n    font-size: inherit;\n    font-weight: inherit;\n  }\n  a {\n    color: inherit;\n    -webkit-text-decoration: inherit;\n    text-decoration: inherit;\n  }\n  b, strong {\n    font-weight: bolder;\n  }\n  code, kbd, samp, pre {\n    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace);\n    font-feature-settings: var(--default-mono-font-feature-settings, normal);\n    font-variation-settings: var(--default-mono-font-variation-settings, normal);\n    font-size: 1em;\n  }\n  small {\n    font-size: 80%;\n  }\n  sub, sup {\n    font-size: 75%;\n    line-height: 0;\n    position: relative;\n    vertical-align: baseline;\n  }\n  sub {\n    bottom: -0.25em;\n  }\n  sup {\n    top: -0.5em;\n  }\n  table {\n    text-indent: 0;\n    border-color: inherit;\n    border-collapse: collapse;\n  }\n  :-moz-focusring {\n    outline: auto;\n  }\n  progress {\n    vertical-align: baseline;\n  }\n  summary {\n    display: list-item;\n  }\n  ol, ul, menu {\n    list-style: none;\n  }\n  img, svg, video, canvas, audio, iframe, embed, object {\n    display: block;\n    vertical-align: middle;\n  }\n  img, video {\n    max-width: 100%;\n    height: auto;\n  }\n  button, input, select, optgroup, textarea, ::file-selector-button {\n    font: inherit;\n    font-feature-settings: inherit;\n    font-variation-settings: inherit;\n    letter-spacing: inherit;\n    color: inherit;\n    border-radius: 0;\n    background-color: transparent;\n    opacity: 1;\n  }\n  :where(select:is([multiple], [size])) optgroup {\n    font-weight: bolder;\n  }\n  :where(select:is([multiple], [size])) optgroup option {\n    padding-inline-start: 20px;\n  }\n  ::file-selector-button {\n    margin-inline-end: 4px;\n  }\n  ::placeholder {\n    opacity: 1;\n  }\n  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {\n    ::placeholder {\n      color: currentcolor;\n      @supports (color: color-mix(in lab, red, red)) {\n        color: color-mix(in oklab, currentcolor 50%, transparent);\n      }\n    }\n  }\n  textarea {\n    resize: vertical;\n  }\n  ::-webkit-search-decoration {\n    -webkit-appearance: none;\n  }\n  ::-webkit-date-and-time-value {\n    min-height: 1lh;\n    text-align: inherit;\n  }\n  ::-webkit-datetime-edit {\n    display: inline-flex;\n  }\n  ::-webkit-datetime-edit-fields-wrapper {\n    padding: 0;\n  }\n  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {\n    padding-block: 0;\n  }\n  :-moz-ui-invalid {\n    box-shadow: none;\n  }\n  button, input:where([type=\"button\"], [type=\"reset\"], [type=\"submit\"]), ::file-selector-button {\n    appearance: button;\n  }\n  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {\n    height: auto;\n  }\n  [hidden]:where(:not([hidden=\"until-found\"])) {\n    display: none !important;\n  }\n}\n@layer utilities {\n  .\\@container\\/card-header {\n    container-type: inline-size;\n    container-name: card-header;\n  }\n  .pointer-events-none {\n    pointer-events: none;\n  }\n  .sr-only {\n    position: absolute;\n    width: 1px;\n    height: 1px;\n    padding: 0;\n    margin: -1px;\n    overflow: hidden;\n    clip: rect(0, 0, 0, 0);\n    white-space: nowrap;\n    border-width: 0;\n  }\n  .absolute {\n    position: absolute;\n  }\n  .fixed {\n    position: fixed;\n  }\n  .relative {\n    position: relative;\n  }\n  .sticky {\n    position: sticky;\n  }\n  .inset-0 {\n    inset: calc(var(--spacing) * 0);\n  }\n  .top-0 {\n    top: calc(var(--spacing) * 0);\n  }\n  .top-3 {\n    top: calc(var(--spacing) * 3);\n  }\n  .top-4 {\n    top: calc(var(--spacing) * 4);\n  }\n  .top-10 {\n    top: calc(var(--spacing) * 10);\n  }\n  .top-20 {\n    top: calc(var(--spacing) * 20);\n  }\n  .top-40 {\n    top: calc(var(--spacing) * 40);\n  }\n  .top-\\[50\\%\\] {\n    top: 50%;\n  }\n  .right-2 {\n    right: calc(var(--spacing) * 2);\n  }\n  .right-3 {\n    right: calc(var(--spacing) * 3);\n  }\n  .right-4 {\n    right: calc(var(--spacing) * 4);\n  }\n  .right-6 {\n    right: calc(var(--spacing) * 6);\n  }\n  .right-10 {\n    right: calc(var(--spacing) * 10);\n  }\n  .right-20 {\n    right: calc(var(--spacing) * 20);\n  }\n  .bottom-6 {\n    bottom: calc(var(--spacing) * 6);\n  }\n  .bottom-10 {\n    bottom: calc(var(--spacing) * 10);\n  }\n  .bottom-20 {\n    bottom: calc(var(--spacing) * 20);\n  }\n  .left-1\\/4 {\n    left: calc(1/4 * 100%);\n  }\n  .left-2 {\n    left: calc(var(--spacing) * 2);\n  }\n  .left-3 {\n    left: calc(var(--spacing) * 3);\n  }\n  .left-10 {\n    left: calc(var(--spacing) * 10);\n  }\n  .left-\\[50\\%\\] {\n    left: 50%;\n  }\n  .z-10 {\n    z-index: 10;\n  }\n  .z-50 {\n    z-index: 50;\n  }\n  .col-span-1 {\n    grid-column: span 1 / span 1;\n  }\n  .col-start-2 {\n    grid-column-start: 2;\n  }\n  .row-span-2 {\n    grid-row: span 2 / span 2;\n  }\n  .row-start-1 {\n    grid-row-start: 1;\n  }\n  .-mx-1 {\n    margin-inline: calc(var(--spacing) * -1);\n  }\n  .mx-auto {\n    margin-inline: auto;\n  }\n  .my-1 {\n    margin-block: calc(var(--spacing) * 1);\n  }\n  .mt-1 {\n    margin-top: calc(var(--spacing) * 1);\n  }\n  .mt-2 {\n    margin-top: calc(var(--spacing) * 2);\n  }\n  .mt-3 {\n    margin-top: calc(var(--spacing) * 3);\n  }\n  .mt-4 {\n    margin-top: calc(var(--spacing) * 4);\n  }\n  .mt-6 {\n    margin-top: calc(var(--spacing) * 6);\n  }\n  .mt-8 {\n    margin-top: calc(var(--spacing) * 8);\n  }\n  .mt-12 {\n    margin-top: calc(var(--spacing) * 12);\n  }\n  .mr-2 {\n    margin-right: calc(var(--spacing) * 2);\n  }\n  .mr-3 {\n    margin-right: calc(var(--spacing) * 3);\n  }\n  .mb-2 {\n    margin-bottom: calc(var(--spacing) * 2);\n  }\n  .mb-3 {\n    margin-bottom: calc(var(--spacing) * 3);\n  }\n  .mb-4 {\n    margin-bottom: calc(var(--spacing) * 4);\n  }\n  .mb-6 {\n    margin-bottom: calc(var(--spacing) * 6);\n  }\n  .mb-8 {\n    margin-bottom: calc(var(--spacing) * 8);\n  }\n  .mb-16 {\n    margin-bottom: calc(var(--spacing) * 16);\n  }\n  .ml-2 {\n    margin-left: calc(var(--spacing) * 2);\n  }\n  .ml-6 {\n    margin-left: calc(var(--spacing) * 6);\n  }\n  .ml-8 {\n    margin-left: calc(var(--spacing) * 8);\n  }\n  .ml-auto {\n    margin-left: auto;\n  }\n  .block {\n    display: block;\n  }\n  .flex {\n    display: flex;\n  }\n  .grid {\n    display: grid;\n  }\n  .hidden {\n    display: none;\n  }\n  .inline-block {\n    display: inline-block;\n  }\n  .inline-flex {\n    display: inline-flex;\n  }\n  .size-2 {\n    width: calc(var(--spacing) * 2);\n    height: calc(var(--spacing) * 2);\n  }\n  .size-3\\.5 {\n    width: calc(var(--spacing) * 3.5);\n    height: calc(var(--spacing) * 3.5);\n  }\n  .size-4 {\n    width: calc(var(--spacing) * 4);\n    height: calc(var(--spacing) * 4);\n  }\n  .size-9 {\n    width: calc(var(--spacing) * 9);\n    height: calc(var(--spacing) * 9);\n  }\n  .h-1 {\n    height: calc(var(--spacing) * 1);\n  }\n  .h-2 {\n    height: calc(var(--spacing) * 2);\n  }\n  .h-3 {\n    height: calc(var(--spacing) * 3);\n  }\n  .h-4 {\n    height: calc(var(--spacing) * 4);\n  }\n  .h-5 {\n    height: calc(var(--spacing) * 5);\n  }\n  .h-6 {\n    height: calc(var(--spacing) * 6);\n  }\n  .h-7 {\n    height: calc(var(--spacing) * 7);\n  }\n  .h-8 {\n    height: calc(var(--spacing) * 8);\n  }\n  .h-9 {\n    height: calc(var(--spacing) * 9);\n  }\n  .h-10 {\n    height: calc(var(--spacing) * 10);\n  }\n  .h-12 {\n    height: calc(var(--spacing) * 12);\n  }\n  .h-14 {\n    height: calc(var(--spacing) * 14);\n  }\n  .h-16 {\n    height: calc(var(--spacing) * 16);\n  }\n  .h-20 {\n    height: calc(var(--spacing) * 20);\n  }\n  .h-24 {\n    height: calc(var(--spacing) * 24);\n  }\n  .h-32 {\n    height: calc(var(--spacing) * 32);\n  }\n  .h-80 {\n    height: calc(var(--spacing) * 80);\n  }\n  .h-96 {\n    height: calc(var(--spacing) * 96);\n  }\n  .h-\\[calc\\(100\\%-1px\\)\\] {\n    height: calc(100% - 1px);\n  }\n  .h-\\[var\\(--radix-select-trigger-height\\)\\] {\n    height: var(--radix-select-trigger-height);\n  }\n  .h-px {\n    height: 1px;\n  }\n  .max-h-\\(--radix-dropdown-menu-content-available-height\\) {\n    max-height: var(--radix-dropdown-menu-content-available-height);\n  }\n  .max-h-\\(--radix-select-content-available-height\\) {\n    max-height: var(--radix-select-content-available-height);\n  }\n  .max-h-64 {\n    max-height: calc(var(--spacing) * 64);\n  }\n  .min-h-screen {\n    min-height: 100vh;\n  }\n  .w-2 {\n    width: calc(var(--spacing) * 2);\n  }\n  .w-3 {\n    width: calc(var(--spacing) * 3);\n  }\n  .w-4 {\n    width: calc(var(--spacing) * 4);\n  }\n  .w-5 {\n    width: calc(var(--spacing) * 5);\n  }\n  .w-6 {\n    width: calc(var(--spacing) * 6);\n  }\n  .w-8 {\n    width: calc(var(--spacing) * 8);\n  }\n  .w-10 {\n    width: calc(var(--spacing) * 10);\n  }\n  .w-12 {\n    width: calc(var(--spacing) * 12);\n  }\n  .w-14 {\n    width: calc(var(--spacing) * 14);\n  }\n  .w-16 {\n    width: calc(var(--spacing) * 16);\n  }\n  .w-20 {\n    width: calc(var(--spacing) * 20);\n  }\n  .w-24 {\n    width: calc(var(--spacing) * 24);\n  }\n  .w-32 {\n    width: calc(var(--spacing) * 32);\n  }\n  .w-40 {\n    width: calc(var(--spacing) * 40);\n  }\n  .w-56 {\n    width: calc(var(--spacing) * 56);\n  }\n  .w-80 {\n    width: calc(var(--spacing) * 80);\n  }\n  .w-fit {\n    width: fit-content;\n  }\n  .w-full {\n    width: 100%;\n  }\n  .max-w-2xl {\n    max-width: var(--container-2xl);\n  }\n  .max-w-3xl {\n    max-width: var(--container-3xl);\n  }\n  .max-w-4xl {\n    max-width: var(--container-4xl);\n  }\n  .max-w-6xl {\n    max-width: var(--container-6xl);\n  }\n  .max-w-7xl {\n    max-width: var(--container-7xl);\n  }\n  .max-w-\\[80\\%\\] {\n    max-width: 80%;\n  }\n  .max-w-\\[calc\\(100\\%-2rem\\)\\] {\n    max-width: calc(100% - 2rem);\n  }\n  .max-w-md {\n    max-width: var(--container-md);\n  }\n  .min-w-0 {\n    min-width: calc(var(--spacing) * 0);\n  }\n  .min-w-\\[8rem\\] {\n    min-width: 8rem;\n  }\n  .min-w-\\[var\\(--radix-select-trigger-width\\)\\] {\n    min-width: var(--radix-select-trigger-width);\n  }\n  .flex-1 {\n    flex: 1;\n  }\n  .shrink-0 {\n    flex-shrink: 0;\n  }\n  .origin-\\(--radix-dropdown-menu-content-transform-origin\\) {\n    transform-origin: var(--radix-dropdown-menu-content-transform-origin);\n  }\n  .origin-\\(--radix-select-content-transform-origin\\) {\n    transform-origin: var(--radix-select-content-transform-origin);\n  }\n  .translate-x-\\[-50\\%\\] {\n    --tw-translate-x: -50%;\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-y-\\[-50\\%\\] {\n    --tw-translate-y: -50%;\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .animate-bounce {\n    animation: var(--animate-bounce);\n  }\n  .animate-pulse {\n    animation: var(--animate-pulse);\n  }\n  .animate-spin {\n    animation: var(--animate-spin);\n  }\n  .cursor-default {\n    cursor: default;\n  }\n  .cursor-pointer {\n    cursor: pointer;\n  }\n  .scroll-my-1 {\n    scroll-margin-block: calc(var(--spacing) * 1);\n  }\n  .auto-rows-min {\n    grid-auto-rows: min-content;\n  }\n  .grid-cols-1 {\n    grid-template-columns: repeat(1, minmax(0, 1fr));\n  }\n  .grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n  .grid-cols-4 {\n    grid-template-columns: repeat(4, minmax(0, 1fr));\n  }\n  .grid-rows-\\[auto_auto\\] {\n    grid-template-rows: auto auto;\n  }\n  .flex-col {\n    flex-direction: column;\n  }\n  .flex-col-reverse {\n    flex-direction: column-reverse;\n  }\n  .flex-row {\n    flex-direction: row;\n  }\n  .flex-row-reverse {\n    flex-direction: row-reverse;\n  }\n  .flex-wrap {\n    flex-wrap: wrap;\n  }\n  .items-center {\n    align-items: center;\n  }\n  .items-end {\n    align-items: flex-end;\n  }\n  .items-start {\n    align-items: flex-start;\n  }\n  .justify-between {\n    justify-content: space-between;\n  }\n  .justify-center {\n    justify-content: center;\n  }\n  .justify-end {\n    justify-content: flex-end;\n  }\n  .justify-start {\n    justify-content: flex-start;\n  }\n  .gap-1 {\n    gap: calc(var(--spacing) * 1);\n  }\n  .gap-1\\.5 {\n    gap: calc(var(--spacing) * 1.5);\n  }\n  .gap-2 {\n    gap: calc(var(--spacing) * 2);\n  }\n  .gap-3 {\n    gap: calc(var(--spacing) * 3);\n  }\n  .gap-4 {\n    gap: calc(var(--spacing) * 4);\n  }\n  .gap-6 {\n    gap: calc(var(--spacing) * 6);\n  }\n  .gap-8 {\n    gap: calc(var(--spacing) * 8);\n  }\n  .space-y-1 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-2 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-3 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-4 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-6 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-8 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-x-1 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .space-x-2 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .space-x-3 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .space-x-4 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .space-x-8 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .self-start {\n    align-self: flex-start;\n  }\n  .justify-self-end {\n    justify-self: flex-end;\n  }\n  .overflow-hidden {\n    overflow: hidden;\n  }\n  .overflow-x-hidden {\n    overflow-x: hidden;\n  }\n  .overflow-y-auto {\n    overflow-y: auto;\n  }\n  .rounded {\n    border-radius: 0.25rem;\n  }\n  .rounded-2xl {\n    border-radius: var(--radius-2xl);\n  }\n  .rounded-3xl {\n    border-radius: var(--radius-3xl);\n  }\n  .rounded-full {\n    border-radius: calc(infinity * 1px);\n  }\n  .rounded-lg {\n    border-radius: var(--radius-lg);\n  }\n  .rounded-md {\n    border-radius: var(--radius-md);\n  }\n  .rounded-sm {\n    border-radius: var(--radius-sm);\n  }\n  .rounded-xl {\n    border-radius: var(--radius-xl);\n  }\n  .rounded-xs {\n    border-radius: var(--radius-xs);\n  }\n  .rounded-t-lg {\n    border-top-left-radius: var(--radius-lg);\n    border-top-right-radius: var(--radius-lg);\n  }\n  .border {\n    border-style: var(--tw-border-style);\n    border-width: 1px;\n  }\n  .border-0 {\n    border-style: var(--tw-border-style);\n    border-width: 0px;\n  }\n  .border-t {\n    border-top-style: var(--tw-border-style);\n    border-top-width: 1px;\n  }\n  .border-b {\n    border-bottom-style: var(--tw-border-style);\n    border-bottom-width: 1px;\n  }\n  .border-b-2 {\n    border-bottom-style: var(--tw-border-style);\n    border-bottom-width: 2px;\n  }\n  .border-blue-600 {\n    border-color: var(--color-blue-600);\n  }\n  .border-gray-300 {\n    border-color: var(--color-gray-300);\n  }\n  .border-gray-800 {\n    border-color: var(--color-gray-800);\n  }\n  .border-transparent {\n    border-color: transparent;\n  }\n  .bg-black\\/50 {\n    background-color: color-mix(in srgb, #000 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-black) 50%, transparent);\n    }\n  }\n  .bg-blue-100 {\n    background-color: var(--color-blue-100);\n  }\n  .bg-blue-500 {\n    background-color: var(--color-blue-500);\n  }\n  .bg-blue-600 {\n    background-color: var(--color-blue-600);\n  }\n  .bg-gray-50 {\n    background-color: var(--color-gray-50);\n  }\n  .bg-gray-100 {\n    background-color: var(--color-gray-100);\n  }\n  .bg-gray-400 {\n    background-color: var(--color-gray-400);\n  }\n  .bg-green-100 {\n    background-color: var(--color-green-100);\n  }\n  .bg-green-500 {\n    background-color: var(--color-green-500);\n  }\n  .bg-green-600 {\n    background-color: var(--color-green-600);\n  }\n  .bg-orange-100 {\n    background-color: var(--color-orange-100);\n  }\n  .bg-orange-500 {\n    background-color: var(--color-orange-500);\n  }\n  .bg-orange-600 {\n    background-color: var(--color-orange-600);\n  }\n  .bg-red-100 {\n    background-color: var(--color-red-100);\n  }\n  .bg-transparent {\n    background-color: transparent;\n  }\n  .bg-white {\n    background-color: var(--color-white);\n  }\n  .bg-white\\/10 {\n    background-color: color-mix(in srgb, #fff 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-white) 10%, transparent);\n    }\n  }\n  .bg-white\\/15 {\n    background-color: color-mix(in srgb, #fff 15%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-white) 15%, transparent);\n    }\n  }\n  .bg-white\\/20 {\n    background-color: color-mix(in srgb, #fff 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-white) 20%, transparent);\n    }\n  }\n  .bg-yellow-100 {\n    background-color: var(--color-yellow-100);\n  }\n  .bg-gradient-to-b {\n    --tw-gradient-position: to bottom in oklab;\n    background-image: linear-gradient(var(--tw-gradient-stops));\n  }\n  .bg-gradient-to-br {\n    --tw-gradient-position: to bottom right in oklab;\n    background-image: linear-gradient(var(--tw-gradient-stops));\n  }\n  .bg-gradient-to-r {\n    --tw-gradient-position: to right in oklab;\n    background-image: linear-gradient(var(--tw-gradient-stops));\n  }\n  .from-gray-50 {\n    --tw-gradient-from: var(--color-gray-50);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-green-600 {\n    --tw-gradient-from: var(--color-green-600);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-orange-600 {\n    --tw-gradient-from: var(--color-orange-600);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-white {\n    --tw-gradient-from: var(--color-white);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .via-transparent {\n    --tw-gradient-via: transparent;\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .via-white {\n    --tw-gradient-via: var(--color-white);\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .to-gray-50 {\n    --tw-gradient-to: var(--color-gray-50);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-green-800 {\n    --tw-gradient-to: var(--color-green-800);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-orange-800 {\n    --tw-gradient-to: var(--color-orange-800);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-white {\n    --tw-gradient-to: var(--color-white);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .fill-current {\n    fill: currentcolor;\n  }\n  .p-0 {\n    padding: calc(var(--spacing) * 0);\n  }\n  .p-1 {\n    padding: calc(var(--spacing) * 1);\n  }\n  .p-2 {\n    padding: calc(var(--spacing) * 2);\n  }\n  .p-3 {\n    padding: calc(var(--spacing) * 3);\n  }\n  .p-4 {\n    padding: calc(var(--spacing) * 4);\n  }\n  .p-6 {\n    padding: calc(var(--spacing) * 6);\n  }\n  .p-8 {\n    padding: calc(var(--spacing) * 8);\n  }\n  .p-\\[3px\\] {\n    padding: 3px;\n  }\n  .px-2 {\n    padding-inline: calc(var(--spacing) * 2);\n  }\n  .px-3 {\n    padding-inline: calc(var(--spacing) * 3);\n  }\n  .px-4 {\n    padding-inline: calc(var(--spacing) * 4);\n  }\n  .px-6 {\n    padding-inline: calc(var(--spacing) * 6);\n  }\n  .px-8 {\n    padding-inline: calc(var(--spacing) * 8);\n  }\n  .py-1 {\n    padding-block: calc(var(--spacing) * 1);\n  }\n  .py-1\\.5 {\n    padding-block: calc(var(--spacing) * 1.5);\n  }\n  .py-2 {\n    padding-block: calc(var(--spacing) * 2);\n  }\n  .py-4 {\n    padding-block: calc(var(--spacing) * 4);\n  }\n  .py-6 {\n    padding-block: calc(var(--spacing) * 6);\n  }\n  .py-8 {\n    padding-block: calc(var(--spacing) * 8);\n  }\n  .py-12 {\n    padding-block: calc(var(--spacing) * 12);\n  }\n  .py-16 {\n    padding-block: calc(var(--spacing) * 16);\n  }\n  .py-20 {\n    padding-block: calc(var(--spacing) * 20);\n  }\n  .pt-6 {\n    padding-top: calc(var(--spacing) * 6);\n  }\n  .pt-8 {\n    padding-top: calc(var(--spacing) * 8);\n  }\n  .pr-2 {\n    padding-right: calc(var(--spacing) * 2);\n  }\n  .pr-8 {\n    padding-right: calc(var(--spacing) * 8);\n  }\n  .pr-10 {\n    padding-right: calc(var(--spacing) * 10);\n  }\n  .pl-2 {\n    padding-left: calc(var(--spacing) * 2);\n  }\n  .pl-8 {\n    padding-left: calc(var(--spacing) * 8);\n  }\n  .pl-10 {\n    padding-left: calc(var(--spacing) * 10);\n  }\n  .text-center {\n    text-align: center;\n  }\n  .text-right {\n    text-align: right;\n  }\n  .text-2xl {\n    font-size: var(--text-2xl);\n    line-height: var(--tw-leading, var(--text-2xl--line-height));\n  }\n  .text-3xl {\n    font-size: var(--text-3xl);\n    line-height: var(--tw-leading, var(--text-3xl--line-height));\n  }\n  .text-4xl {\n    font-size: var(--text-4xl);\n    line-height: var(--tw-leading, var(--text-4xl--line-height));\n  }\n  .text-base {\n    font-size: var(--text-base);\n    line-height: var(--tw-leading, var(--text-base--line-height));\n  }\n  .text-lg {\n    font-size: var(--text-lg);\n    line-height: var(--tw-leading, var(--text-lg--line-height));\n  }\n  .text-sm {\n    font-size: var(--text-sm);\n    line-height: var(--tw-leading, var(--text-sm--line-height));\n  }\n  .text-xl {\n    font-size: var(--text-xl);\n    line-height: var(--tw-leading, var(--text-xl--line-height));\n  }\n  .text-xs {\n    font-size: var(--text-xs);\n    line-height: var(--tw-leading, var(--text-xs--line-height));\n  }\n  .leading-none {\n    --tw-leading: 1;\n    line-height: 1;\n  }\n  .font-black {\n    --tw-font-weight: var(--font-weight-black);\n    font-weight: var(--font-weight-black);\n  }\n  .font-bold {\n    --tw-font-weight: var(--font-weight-bold);\n    font-weight: var(--font-weight-bold);\n  }\n  .font-medium {\n    --tw-font-weight: var(--font-weight-medium);\n    font-weight: var(--font-weight-medium);\n  }\n  .font-semibold {\n    --tw-font-weight: var(--font-weight-semibold);\n    font-weight: var(--font-weight-semibold);\n  }\n  .tracking-widest {\n    --tw-tracking: var(--tracking-widest);\n    letter-spacing: var(--tracking-widest);\n  }\n  .whitespace-nowrap {\n    white-space: nowrap;\n  }\n  .text-black {\n    color: var(--color-black);\n  }\n  .text-black\\/80 {\n    color: color-mix(in srgb, #000 80%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--color-black) 80%, transparent);\n    }\n  }\n  .text-blue-100 {\n    color: var(--color-blue-100);\n  }\n  .text-blue-600 {\n    color: var(--color-blue-600);\n  }\n  .text-blue-800 {\n    color: var(--color-blue-800);\n  }\n  .text-gray-300 {\n    color: var(--color-gray-300);\n  }\n  .text-gray-400 {\n    color: var(--color-gray-400);\n  }\n  .text-gray-500 {\n    color: var(--color-gray-500);\n  }\n  .text-gray-600 {\n    color: var(--color-gray-600);\n  }\n  .text-gray-700 {\n    color: var(--color-gray-700);\n  }\n  .text-gray-800 {\n    color: var(--color-gray-800);\n  }\n  .text-gray-900 {\n    color: var(--color-gray-900);\n  }\n  .text-green-100 {\n    color: var(--color-green-100);\n  }\n  .text-green-600 {\n    color: var(--color-green-600);\n  }\n  .text-green-700 {\n    color: var(--color-green-700);\n  }\n  .text-green-800 {\n    color: var(--color-green-800);\n  }\n  .text-orange-100 {\n    color: var(--color-orange-100);\n  }\n  .text-orange-600 {\n    color: var(--color-orange-600);\n  }\n  .text-purple-600 {\n    color: var(--color-purple-600);\n  }\n  .text-red-600 {\n    color: var(--color-red-600);\n  }\n  .text-red-700 {\n    color: var(--color-red-700);\n  }\n  .text-white {\n    color: var(--color-white);\n  }\n  .text-white\\/70 {\n    color: color-mix(in srgb, #fff 70%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--color-white) 70%, transparent);\n    }\n  }\n  .text-white\\/80 {\n    color: color-mix(in srgb, #fff 80%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--color-white) 80%, transparent);\n    }\n  }\n  .text-yellow-500 {\n    color: var(--color-yellow-500);\n  }\n  .text-yellow-800 {\n    color: var(--color-yellow-800);\n  }\n  .capitalize {\n    text-transform: capitalize;\n  }\n  .underline-offset-4 {\n    text-underline-offset: 4px;\n  }\n  .antialiased {\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n  }\n  .opacity-50 {\n    opacity: 50%;\n  }\n  .opacity-70 {\n    opacity: 70%;\n  }\n  .opacity-90 {\n    opacity: 90%;\n  }\n  .shadow-lg {\n    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-md {\n    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-sm {\n    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-xl {\n    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-xs {\n    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .outline-hidden {\n    --tw-outline-style: none;\n    outline-style: none;\n    @media (forced-colors: active) {\n      outline: 2px solid transparent;\n      outline-offset: 2px;\n    }\n  }\n  .outline {\n    outline-style: var(--tw-outline-style);\n    outline-width: 1px;\n  }\n  .blur {\n    --tw-blur: blur(8px);\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n  }\n  .transition-\\[color\\,box-shadow\\] {\n    transition-property: color,box-shadow;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-all {\n    transition-property: all;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-colors {\n    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-opacity {\n    transition-property: opacity;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-shadow {\n    transition-property: box-shadow;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .duration-200 {\n    --tw-duration: 200ms;\n    transition-duration: 200ms;\n  }\n  .duration-300 {\n    --tw-duration: 300ms;\n    transition-duration: 300ms;\n  }\n  .outline-none {\n    --tw-outline-style: none;\n    outline-style: none;\n  }\n  .select-none {\n    -webkit-user-select: none;\n    user-select: none;\n  }\n  .group-data-\\[disabled\\=true\\]\\:pointer-events-none {\n    &:is(:where(.group)[data-disabled=\"true\"] *) {\n      pointer-events: none;\n    }\n  }\n  .group-data-\\[disabled\\=true\\]\\:opacity-50 {\n    &:is(:where(.group)[data-disabled=\"true\"] *) {\n      opacity: 50%;\n    }\n  }\n  .peer-disabled\\:cursor-not-allowed {\n    &:is(:where(.peer):disabled ~ *) {\n      cursor: not-allowed;\n    }\n  }\n  .peer-disabled\\:opacity-50 {\n    &:is(:where(.peer):disabled ~ *) {\n      opacity: 50%;\n    }\n  }\n  .file\\:inline-flex {\n    &::file-selector-button {\n      display: inline-flex;\n    }\n  }\n  .file\\:h-7 {\n    &::file-selector-button {\n      height: calc(var(--spacing) * 7);\n    }\n  }\n  .file\\:border-0 {\n    &::file-selector-button {\n      border-style: var(--tw-border-style);\n      border-width: 0px;\n    }\n  }\n  .file\\:bg-transparent {\n    &::file-selector-button {\n      background-color: transparent;\n    }\n  }\n  .file\\:text-sm {\n    &::file-selector-button {\n      font-size: var(--text-sm);\n      line-height: var(--tw-leading, var(--text-sm--line-height));\n    }\n  }\n  .file\\:font-medium {\n    &::file-selector-button {\n      --tw-font-weight: var(--font-weight-medium);\n      font-weight: var(--font-weight-medium);\n    }\n  }\n  .hover\\:bg-blue-700 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-blue-700);\n      }\n    }\n  }\n  .hover\\:bg-gray-50 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-gray-50);\n      }\n    }\n  }\n  .hover\\:bg-gray-100 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-gray-100);\n      }\n    }\n  }\n  .hover\\:bg-green-700 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-green-700);\n      }\n    }\n  }\n  .hover\\:bg-orange-700 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-orange-700);\n      }\n    }\n  }\n  .hover\\:text-blue-500 {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-blue-500);\n      }\n    }\n  }\n  .hover\\:text-blue-600 {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-blue-600);\n      }\n    }\n  }\n  .hover\\:text-gray-600 {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-gray-600);\n      }\n    }\n  }\n  .hover\\:text-white {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-white);\n      }\n    }\n  }\n  .hover\\:underline {\n    &:hover {\n      @media (hover: hover) {\n        text-decoration-line: underline;\n      }\n    }\n  }\n  .hover\\:opacity-100 {\n    &:hover {\n      @media (hover: hover) {\n        opacity: 100%;\n      }\n    }\n  }\n  .hover\\:shadow-lg {\n    &:hover {\n      @media (hover: hover) {\n        --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .hover\\:shadow-md {\n    &:hover {\n      @media (hover: hover) {\n        --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .focus\\:ring-2 {\n    &:focus {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .focus\\:ring-blue-500 {\n    &:focus {\n      --tw-ring-color: var(--color-blue-500);\n    }\n  }\n  .focus\\:ring-offset-2 {\n    &:focus {\n      --tw-ring-offset-width: 2px;\n      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n    }\n  }\n  .focus\\:outline-hidden {\n    &:focus {\n      --tw-outline-style: none;\n      outline-style: none;\n      @media (forced-colors: active) {\n        outline: 2px solid transparent;\n        outline-offset: 2px;\n      }\n    }\n  }\n  .focus\\:outline-none {\n    &:focus {\n      --tw-outline-style: none;\n      outline-style: none;\n    }\n  }\n  .focus-visible\\:ring-\\[3px\\] {\n    &:focus-visible {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .focus-visible\\:outline-1 {\n    &:focus-visible {\n      outline-style: var(--tw-outline-style);\n      outline-width: 1px;\n    }\n  }\n  .disabled\\:pointer-events-none {\n    &:disabled {\n      pointer-events: none;\n    }\n  }\n  .disabled\\:cursor-not-allowed {\n    &:disabled {\n      cursor: not-allowed;\n    }\n  }\n  .disabled\\:bg-gray-100 {\n    &:disabled {\n      background-color: var(--color-gray-100);\n    }\n  }\n  .disabled\\:opacity-50 {\n    &:disabled {\n      opacity: 50%;\n    }\n  }\n  .has-data-\\[slot\\=card-action\\]\\:grid-cols-\\[1fr_auto\\] {\n    &:has(*[data-slot=\"card-action\"]) {\n      grid-template-columns: 1fr auto;\n    }\n  }\n  .has-\\[\\>svg\\]\\:px-2\\.5 {\n    &:has(>svg) {\n      padding-inline: calc(var(--spacing) * 2.5);\n    }\n  }\n  .has-\\[\\>svg\\]\\:px-3 {\n    &:has(>svg) {\n      padding-inline: calc(var(--spacing) * 3);\n    }\n  }\n  .has-\\[\\>svg\\]\\:px-4 {\n    &:has(>svg) {\n      padding-inline: calc(var(--spacing) * 4);\n    }\n  }\n  .data-\\[disabled\\]\\:pointer-events-none {\n    &[data-disabled] {\n      pointer-events: none;\n    }\n  }\n  .data-\\[disabled\\]\\:opacity-50 {\n    &[data-disabled] {\n      opacity: 50%;\n    }\n  }\n  .data-\\[inset\\]\\:pl-8 {\n    &[data-inset] {\n      padding-left: calc(var(--spacing) * 8);\n    }\n  }\n  .data-\\[side\\=bottom\\]\\:translate-y-1 {\n    &[data-side=\"bottom\"] {\n      --tw-translate-y: calc(var(--spacing) * 1);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .data-\\[side\\=left\\]\\:-translate-x-1 {\n    &[data-side=\"left\"] {\n      --tw-translate-x: calc(var(--spacing) * -1);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .data-\\[side\\=right\\]\\:translate-x-1 {\n    &[data-side=\"right\"] {\n      --tw-translate-x: calc(var(--spacing) * 1);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .data-\\[side\\=top\\]\\:-translate-y-1 {\n    &[data-side=\"top\"] {\n      --tw-translate-y: calc(var(--spacing) * -1);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .data-\\[size\\=default\\]\\:h-9 {\n    &[data-size=\"default\"] {\n      height: calc(var(--spacing) * 9);\n    }\n  }\n  .data-\\[size\\=sm\\]\\:h-8 {\n    &[data-size=\"sm\"] {\n      height: calc(var(--spacing) * 8);\n    }\n  }\n  .\\*\\:data-\\[slot\\=select-value\\]\\:line-clamp-1 {\n    :is(& > *) {\n      &[data-slot=\"select-value\"] {\n        overflow: hidden;\n        display: -webkit-box;\n        -webkit-box-orient: vertical;\n        -webkit-line-clamp: 1;\n      }\n    }\n  }\n  .\\*\\:data-\\[slot\\=select-value\\]\\:flex {\n    :is(& > *) {\n      &[data-slot=\"select-value\"] {\n        display: flex;\n      }\n    }\n  }\n  .\\*\\:data-\\[slot\\=select-value\\]\\:items-center {\n    :is(& > *) {\n      &[data-slot=\"select-value\"] {\n        align-items: center;\n      }\n    }\n  }\n  .\\*\\:data-\\[slot\\=select-value\\]\\:gap-2 {\n    :is(& > *) {\n      &[data-slot=\"select-value\"] {\n        gap: calc(var(--spacing) * 2);\n      }\n    }\n  }\n  .data-\\[state\\=active\\]\\:shadow-sm {\n    &[data-state=\"active\"] {\n      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .sm\\:inline {\n    @media (width >= 40rem) {\n      display: inline;\n    }\n  }\n  .sm\\:max-w-lg {\n    @media (width >= 40rem) {\n      max-width: var(--container-lg);\n    }\n  }\n  .sm\\:flex-row {\n    @media (width >= 40rem) {\n      flex-direction: row;\n    }\n  }\n  .sm\\:justify-end {\n    @media (width >= 40rem) {\n      justify-content: flex-end;\n    }\n  }\n  .sm\\:px-6 {\n    @media (width >= 40rem) {\n      padding-inline: calc(var(--spacing) * 6);\n    }\n  }\n  .sm\\:text-left {\n    @media (width >= 40rem) {\n      text-align: left;\n    }\n  }\n  .md\\:col-span-1 {\n    @media (width >= 48rem) {\n      grid-column: span 1 / span 1;\n    }\n  }\n  .md\\:col-span-2 {\n    @media (width >= 48rem) {\n      grid-column: span 2 / span 2;\n    }\n  }\n  .md\\:mt-0 {\n    @media (width >= 48rem) {\n      margin-top: calc(var(--spacing) * 0);\n    }\n  }\n  .md\\:flex {\n    @media (width >= 48rem) {\n      display: flex;\n    }\n  }\n  .md\\:hidden {\n    @media (width >= 48rem) {\n      display: none;\n    }\n  }\n  .md\\:grid-cols-2 {\n    @media (width >= 48rem) {\n      grid-template-columns: repeat(2, minmax(0, 1fr));\n    }\n  }\n  .md\\:grid-cols-3 {\n    @media (width >= 48rem) {\n      grid-template-columns: repeat(3, minmax(0, 1fr));\n    }\n  }\n  .md\\:grid-cols-4 {\n    @media (width >= 48rem) {\n      grid-template-columns: repeat(4, minmax(0, 1fr));\n    }\n  }\n  .md\\:flex-row {\n    @media (width >= 48rem) {\n      flex-direction: row;\n    }\n  }\n  .md\\:text-2xl {\n    @media (width >= 48rem) {\n      font-size: var(--text-2xl);\n      line-height: var(--tw-leading, var(--text-2xl--line-height));\n    }\n  }\n  .md\\:text-4xl {\n    @media (width >= 48rem) {\n      font-size: var(--text-4xl);\n      line-height: var(--tw-leading, var(--text-4xl--line-height));\n    }\n  }\n  .md\\:text-6xl {\n    @media (width >= 48rem) {\n      font-size: var(--text-6xl);\n      line-height: var(--tw-leading, var(--text-6xl--line-height));\n    }\n  }\n  .md\\:text-sm {\n    @media (width >= 48rem) {\n      font-size: var(--text-sm);\n      line-height: var(--tw-leading, var(--text-sm--line-height));\n    }\n  }\n  .lg\\:grid-cols-4 {\n    @media (width >= 64rem) {\n      grid-template-columns: repeat(4, minmax(0, 1fr));\n    }\n  }\n  .lg\\:px-8 {\n    @media (width >= 64rem) {\n      padding-inline: calc(var(--spacing) * 8);\n    }\n  }\n  .\\[\\&_svg\\]\\:pointer-events-none {\n    & svg {\n      pointer-events: none;\n    }\n  }\n  .\\[\\&_svg\\]\\:shrink-0 {\n    & svg {\n      flex-shrink: 0;\n    }\n  }\n  .\\[\\&_svg\\:not\\(\\[class\\*\\=\\'size-\\'\\]\\)\\]\\:size-4 {\n    & svg:not([class*='size-']) {\n      width: calc(var(--spacing) * 4);\n      height: calc(var(--spacing) * 4);\n    }\n  }\n  .\\[\\.border-b\\]\\:pb-6 {\n    &:is(.border-b) {\n      padding-bottom: calc(var(--spacing) * 6);\n    }\n  }\n  .\\[\\.border-t\\]\\:pt-6 {\n    &:is(.border-t) {\n      padding-top: calc(var(--spacing) * 6);\n    }\n  }\n  .\\*\\:\\[span\\]\\:last\\:flex {\n    :is(& > *) {\n      &:is(span) {\n        &:last-child {\n          display: flex;\n        }\n      }\n    }\n  }\n  .\\*\\:\\[span\\]\\:last\\:items-center {\n    :is(& > *) {\n      &:is(span) {\n        &:last-child {\n          align-items: center;\n        }\n      }\n    }\n  }\n  .\\*\\:\\[span\\]\\:last\\:gap-2 {\n    :is(& > *) {\n      &:is(span) {\n        &:last-child {\n          gap: calc(var(--spacing) * 2);\n        }\n      }\n    }\n  }\n}\n:root {\n  --background: #0a0a0f;\n  --foreground: #ffffff;\n  --card: #1a1a2e;\n  --card-foreground: #ffffff;\n  --popover: #1a1a2e;\n  --popover-foreground: #ffffff;\n  --primary: #00d4ff;\n  --primary-foreground: #000000;\n  --secondary: #ff3366;\n  --secondary-foreground: #ffffff;\n  --muted: #2a2a3e;\n  --muted-foreground: #ffffff;\n  --accent: #ffff00;\n  --accent-foreground: #000000;\n  --destructive: #ff4757;\n  --destructive-foreground: #ffffff;\n  --border: #3a3a4e;\n  --input: #2a2a3e;\n  --input-foreground: #ffffff;\n  --ring: #00d4ff;\n  --radius: 0.75rem;\n  --electric-cyan: #00d4ff;\n  --neon-pink: #ff3366;\n  --laser-green: #00ff88;\n  --solar-yellow: #ffff00;\n  --plasma-purple: #aa44ff;\n  --fire-orange: #ff6600;\n  --ice-blue: #44ddff;\n  --volt-lime: #88ff00;\n  --cyber-blue: #0099ff;\n  --hot-magenta: #ff0066;\n  --toxic-green: #66ff00;\n  --gold-rush: #ffcc00;\n  --royal-violet: #6600ff;\n  --sunset-red: #ff3300;\n  --aqua-bright: #00ffcc;\n  --electric-lime: #ccff00;\n  --neon-blue: #0066ff;\n  --cherry-pop: #ff0033;\n  --mint-electric: #00ff99;\n  --banana-bright: #ffff33;\n  --grape-neon: #9933ff;\n  --coral-bright: #ff6633;\n  --turbo-teal: #00ccff;\n  --lime-shock: #99ff33;\n  --gradient-start: #0a3d62;\n  --gradient-mid: #2980b9;\n  --gradient-end: #e74c3c;\n  --gradient-accent: #f39c12;\n  --neutral-50: #f8fafc;\n  --neutral-100: #f1f5f9;\n  --neutral-200: #e2e8f0;\n  --neutral-300: #cbd5e1;\n  --neutral-400: #94a3b8;\n  --neutral-500: #64748b;\n  --neutral-600: #475569;\n  --neutral-700: #334155;\n  --neutral-800: #1e293b;\n  --neutral-900: #0f172a;\n}\n.dark {\n  --background: #0a0a0f;\n  --foreground: #ffffff;\n  --card: #1a1a2e;\n  --card-foreground: #ffffff;\n  --popover: #1a1a2e;\n  --popover-foreground: #ffffff;\n  --primary: #00d4ff;\n  --primary-foreground: #000000;\n  --secondary: #ff3366;\n  --secondary-foreground: #ffffff;\n  --muted: #2a2a3e;\n  --muted-foreground: #ffffff;\n  --accent: #ffff00;\n  --accent-foreground: #000000;\n  --destructive: #ff4757;\n  --destructive-foreground: #ffffff;\n  --border: #3a3a4e;\n  --input: #2a2a3e;\n  --input-foreground: #ffffff;\n  --ring: #00d4ff;\n  --electric-cyan: #00ffff;\n  --neon-pink: #ff0080;\n  --laser-green: #00ff00;\n  --solar-yellow: #ffff00;\n  --plasma-purple: #8000ff;\n  --fire-orange: #ff4000;\n  --ice-blue: #40e0ff;\n  --volt-lime: #80ff00;\n}\n* {\n  border-color: var(--border);\n}\nbody {\n  background-color: var(--background);\n  color: var(--foreground);\n  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n}\nhtml {\n  scroll-behavior: smooth;\n}\n.bg-gradient-electric {\n  background: linear-gradient(135deg, var(--electric-cyan) 0%, var(--ice-blue) 50%, var(--turbo-teal) 100%);\n  box-shadow: 0 0 30px rgba(0, 212, 255, 0.5);\n}\n.bg-gradient-neon {\n  background: linear-gradient(135deg, var(--neon-pink) 0%, var(--hot-magenta) 50%, var(--cherry-pop) 100%);\n  box-shadow: 0 0 30px rgba(255, 51, 102, 0.5);\n}\n.bg-gradient-laser {\n  background: linear-gradient(135deg, var(--laser-green) 0%, var(--toxic-green) 50%, var(--volt-lime) 100%);\n  box-shadow: 0 0 30px rgba(0, 255, 136, 0.5);\n}\n.bg-gradient-solar {\n  background: linear-gradient(135deg, var(--solar-yellow) 0%, var(--gold-rush) 50%, var(--banana-bright) 100%);\n  box-shadow: 0 0 30px rgba(255, 255, 0, 0.5);\n}\n.bg-gradient-plasma {\n  background: linear-gradient(135deg, var(--plasma-purple) 0%, var(--royal-violet) 50%, var(--grape-neon) 100%);\n  box-shadow: 0 0 30px rgba(170, 68, 255, 0.5);\n}\n.bg-gradient-fire {\n  background: linear-gradient(135deg, var(--fire-orange) 0%, var(--sunset-red) 50%, var(--coral-bright) 100%);\n  box-shadow: 0 0 30px rgba(255, 102, 0, 0.5);\n}\n.bg-gradient-hero {\n  background: linear-gradient(135deg,\n    var(--electric-cyan) 0%,\n    var(--neon-pink) 25%,\n    var(--laser-green) 50%,\n    var(--solar-yellow) 75%,\n    var(--plasma-purple) 100%);\n  box-shadow: 0 0 50px rgba(0, 212, 255, 0.3);\n}\n.bg-gradient-rainbow {\n  background: linear-gradient(135deg,\n    var(--electric-cyan) 0%,\n    var(--neon-blue) 16.66%,\n    var(--laser-green) 33.33%,\n    var(--solar-yellow) 50%,\n    var(--fire-orange) 66.66%,\n    var(--neon-pink) 83.33%,\n    var(--plasma-purple) 100%);\n  box-shadow: 0 0 40px rgba(255, 255, 255, 0.2);\n}\n.bg-gradient-cyber {\n  background: linear-gradient(135deg,\n    var(--cyber-blue) 0%,\n    var(--electric-cyan) 50%,\n    var(--aqua-bright) 100%);\n  box-shadow: 0 0 35px rgba(0, 153, 255, 0.4);\n}\n.bg-gradient-toxic {\n  background: linear-gradient(135deg,\n    var(--toxic-green) 0%,\n    var(--electric-lime) 50%,\n    var(--lime-shock) 100%);\n  box-shadow: 0 0 35px rgba(102, 255, 0, 0.4);\n}\n.bg-gradient-aurora {\n  background: linear-gradient(135deg,\n    var(--deep-ocean) 0%,\n    var(--royal-purple) 25%,\n    var(--coral-sunset) 50%,\n    var(--golden-hour) 75%,\n    var(--emerald-green) 100%);\n}\n.bg-gradient-twilight {\n  background: linear-gradient(135deg,\n    var(--midnight-blue) 0%,\n    var(--ocean-depth) 30%,\n    var(--violet-dream) 70%,\n    var(--lavender-mist) 100%);\n}\n.bg-gradient-sunrise {\n  background: linear-gradient(135deg,\n    var(--coral-sunset) 0%,\n    var(--sunset-orange) 25%,\n    var(--golden-hour) 50%,\n    var(--champagne-gold) 75%,\n    var(--amber-glow) 100%);\n}\n.text-electric {\n  color: var(--electric-cyan);\n}\n.text-neon {\n  color: var(--neon-pink);\n}\n.text-laser {\n  color: var(--laser-green);\n}\n.text-solar {\n  color: var(--solar-yellow);\n}\n.text-plasma {\n  color: var(--plasma-purple);\n}\n.text-fire {\n  color: var(--fire-orange);\n}\n.text-ice {\n  color: var(--ice-blue);\n}\n.text-volt {\n  color: var(--volt-lime);\n}\n.text-azure {\n  color: var(--azure-sky);\n}\n.text-turquoise {\n  color: var(--turquoise-wave);\n}\n.text-rose {\n  color: var(--rose-garden);\n}\n.text-champagne {\n  color: var(--champagne-gold);\n}\n.text-lavender {\n  color: var(--lavender-mist);\n}\n.text-seafoam {\n  color: var(--seafoam-green);\n}\n.bg-electric {\n  background-color: var(--electric-cyan);\n  color: #000000;\n}\n.bg-neon {\n  background-color: var(--neon-pink);\n  color: #ffffff;\n}\n.bg-laser {\n  background-color: var(--laser-green);\n  color: #000000;\n}\n.bg-solar {\n  background-color: var(--solar-yellow);\n  color: #000000;\n}\n.bg-plasma {\n  background-color: var(--plasma-purple);\n  color: #ffffff;\n}\n.bg-fire {\n  background-color: var(--fire-orange);\n  color: #ffffff;\n}\n.bg-ice {\n  background-color: var(--ice-blue);\n  color: #000000;\n}\n.bg-volt {\n  background-color: var(--volt-lime);\n  color: #000000;\n}\n.bg-azure {\n  background-color: var(--azure-sky);\n}\n.bg-turquoise {\n  background-color: var(--turquoise-wave);\n}\n.bg-rose {\n  background-color: var(--rose-garden);\n}\n.bg-champagne {\n  background-color: var(--champagne-gold);\n}\n.bg-lavender {\n  background-color: var(--lavender-mist);\n}\n.bg-seafoam {\n  background-color: var(--seafoam-green);\n}\n.border-ocean {\n  border-color: var(--deep-ocean);\n}\n.border-teal {\n  border-color: var(--ocean-teal);\n}\n.border-coral {\n  border-color: var(--coral-sunset);\n}\n.border-golden {\n  border-color: var(--golden-hour);\n}\n.border-purple {\n  border-color: var(--royal-purple);\n}\n.border-emerald {\n  border-color: var(--emerald-green);\n}\n.hover-lift {\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\n}\n.hover-lift:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);\n}\n.glass-effect {\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n.animate-float {\n  animation: float 3s ease-in-out infinite;\n}\n.animate-pulse-slow {\n  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n}\n.gradient-text {\n  background: linear-gradient(135deg,\n    var(--electric-cyan) 0%,\n    var(--neon-pink) 25%,\n    var(--laser-green) 50%,\n    var(--solar-yellow) 75%,\n    var(--plasma-purple) 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  font-weight: 900;\n  text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);\n  animation: textGlow 3s ease-in-out infinite alternate;\n}\n.gradient-text-electric {\n  background: linear-gradient(135deg, var(--electric-cyan) 0%, var(--ice-blue) 50%, var(--turbo-teal) 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  font-weight: 900;\n  text-shadow: 0 0 15px rgba(0, 212, 255, 0.6);\n  animation: electricTextPulse 2s ease-in-out infinite alternate;\n}\n.gradient-text-neon {\n  background: linear-gradient(135deg, var(--neon-pink) 0%, var(--hot-magenta) 50%, var(--cherry-pop) 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  font-weight: 900;\n  text-shadow: 0 0 15px rgba(255, 51, 102, 0.6);\n  animation: neonTextPulse 2.5s ease-in-out infinite alternate;\n}\n.gradient-text-laser {\n  background: linear-gradient(135deg, var(--laser-green) 0%, var(--toxic-green) 50%, var(--volt-lime) 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  font-weight: 900;\n  text-shadow: 0 0 15px rgba(0, 255, 136, 0.6);\n  animation: laserTextPulse 2.2s ease-in-out infinite alternate;\n}\n.gradient-text-solar {\n  background: linear-gradient(135deg, var(--solar-yellow) 0%, var(--gold-rush) 50%, var(--banana-bright) 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  font-weight: 900;\n  text-shadow: 0 0 15px rgba(255, 255, 0, 0.6);\n  animation: solarTextPulse 1.8s ease-in-out infinite alternate;\n}\n.gradient-text-plasma {\n  background: linear-gradient(135deg, var(--plasma-purple) 0%, var(--royal-violet) 50%, var(--grape-neon) 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  font-weight: 900;\n  text-shadow: 0 0 15px rgba(170, 68, 255, 0.6);\n  animation: plasmaTextPulse 2.8s ease-in-out infinite alternate;\n}\n.gradient-text-fire {\n  background: linear-gradient(135deg, var(--fire-orange) 0%, var(--sunset-red) 50%, var(--coral-bright) 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  font-weight: 900;\n  text-shadow: 0 0 15px rgba(255, 102, 0, 0.6);\n  animation: fireTextPulse 1.5s ease-in-out infinite alternate;\n}\n.gradient-text-rainbow {\n  background: linear-gradient(135deg,\n    var(--electric-cyan) 0%,\n    var(--neon-blue) 14%,\n    var(--laser-green) 28%,\n    var(--solar-yellow) 42%,\n    var(--fire-orange) 56%,\n    var(--neon-pink) 70%,\n    var(--plasma-purple) 84%,\n    var(--electric-cyan) 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  font-weight: 900;\n  background-size: 200% 200%;\n  animation: rainbowText 4s ease-in-out infinite;\n  text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);\n}\n@keyframes float {\n  0%, 100% {\n    transform: translateY(0px);\n  }\n  50% {\n    transform: translateY(-10px);\n  }\n}\n@keyframes slideInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n@keyframes slideInLeft {\n  from {\n    opacity: 0;\n    transform: translateX(-30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n@keyframes slideInRight {\n  from {\n    opacity: 0;\n    transform: translateX(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n.animate-slide-up {\n  animation: slideInUp 0.6s ease-out;\n}\n.animate-slide-left {\n  animation: slideInLeft 0.6s ease-out;\n}\n.animate-slide-right {\n  animation: slideInRight 0.6s ease-out;\n}\n.highlight-important {\n  background: linear-gradient(135deg, var(--electric-cyan) 0%, var(--neon-pink) 100%);\n  color: #000000;\n  padding: 8px 16px;\n  border-radius: 12px;\n  font-weight: 800;\n  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.9);\n  box-shadow: 0 0 20px rgba(0, 212, 255, 0.4),\n    0 4px 12px rgba(0, 0, 0, 0.2);\n  animation: highlightPulse 2s ease-in-out infinite alternate;\n  display: inline-block;\n  transform: scale(1);\n  transition: all 0.3s ease;\n}\n.highlight-important:hover {\n  transform: scale(1.05);\n  box-shadow: 0 0 30px rgba(0, 212, 255, 0.6),\n    0 6px 18px rgba(0, 0, 0, 0.3);\n}\n.highlight-urgent {\n  background: linear-gradient(135deg, var(--fire-orange) 0%, var(--sunset-red) 100%);\n  color: #ffffff;\n  padding: 8px 16px;\n  border-radius: 12px;\n  font-weight: 800;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);\n  box-shadow: 0 0 20px rgba(255, 102, 0, 0.5),\n    0 4px 12px rgba(0, 0, 0, 0.2);\n  animation: urgentBlink 1.5s ease-in-out infinite;\n  display: inline-block;\n}\n.highlight-success {\n  background: linear-gradient(135deg, var(--laser-green) 0%, var(--toxic-green) 100%);\n  color: #000000;\n  padding: 8px 16px;\n  border-radius: 12px;\n  font-weight: 800;\n  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.9);\n  box-shadow: 0 0 20px rgba(0, 255, 136, 0.4),\n    0 4px 12px rgba(0, 0, 0, 0.2);\n  animation: successGlow 2.5s ease-in-out infinite alternate;\n  display: inline-block;\n}\n.highlight-premium {\n  background: linear-gradient(135deg, var(--plasma-purple) 0%, var(--royal-violet) 100%);\n  color: #ffffff;\n  padding: 8px 16px;\n  border-radius: 12px;\n  font-weight: 800;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);\n  box-shadow: 0 0 20px rgba(170, 68, 255, 0.5),\n    0 4px 12px rgba(0, 0, 0, 0.2);\n  animation: premiumShine 3s ease-in-out infinite;\n  display: inline-block;\n}\n.title-hero {\n  font-size: 3.5rem;\n  font-weight: 900;\n  line-height: 1.1;\n  margin-bottom: 1rem;\n  background: linear-gradient(135deg,\n    var(--electric-cyan) 0%,\n    var(--neon-pink) 25%,\n    var(--laser-green) 50%,\n    var(--solar-yellow) 75%,\n    var(--plasma-purple) 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  background-size: 300% 300%;\n  animation: heroTitleFlow 6s ease-in-out infinite;\n  text-shadow: 0 0 30px rgba(255, 255, 255, 0.3);\n  position: relative;\n}\n.title-hero::before {\n  content: '';\n  position: absolute;\n  top: -10px;\n  left: -10px;\n  right: -10px;\n  bottom: -10px;\n  background: linear-gradient(135deg,\n    var(--electric-cyan) 0%,\n    var(--neon-pink) 25%,\n    var(--laser-green) 50%,\n    var(--solar-yellow) 75%,\n    var(--plasma-purple) 100%);\n  border-radius: 20px;\n  opacity: 0.1;\n  z-index: -1;\n  animation: titleGlow 4s ease-in-out infinite alternate;\n}\n.title-section {\n  font-size: 2.5rem;\n  font-weight: 800;\n  margin-bottom: 1rem;\n  background: linear-gradient(135deg, var(--electric-cyan) 0%, var(--neon-pink) 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  text-shadow: 0 0 20px rgba(0, 212, 255, 0.4);\n  animation: sectionTitlePulse 3s ease-in-out infinite alternate;\n}\n.title-card {\n  font-size: 1.5rem;\n  font-weight: 700;\n  margin-bottom: 0.5rem;\n  color: var(--electric-cyan);\n  text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);\n  animation: cardTitleGlow 2s ease-in-out infinite alternate;\n}\n.hero-section {\n  background: linear-gradient(135deg,\n    var(--background) 0%,\n    rgba(26, 26, 46, 0.95) 50%,\n    var(--background) 100%);\n  position: relative;\n  overflow: hidden;\n  padding: 4rem 2rem;\n}\n.hero-section::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(135deg,\n    var(--electric-cyan) 0%,\n    var(--neon-pink) 25%,\n    var(--laser-green) 50%,\n    var(--solar-yellow) 75%,\n    var(--plasma-purple) 100%);\n  opacity: 0.05;\n  z-index: 0;\n  animation: heroBackground 8s ease-in-out infinite;\n}\n.v-logo {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  width: 64px;\n  height: 64px;\n  border-radius: 20px;\n  background: linear-gradient(135deg,\n    var(--electric-cyan) 0%,\n    var(--neon-pink) 25%,\n    var(--laser-green) 50%,\n    var(--solar-yellow) 75%,\n    var(--plasma-purple) 100%);\n  box-shadow: 0 0 30px rgba(0, 212, 255, 0.6),\n    0 0 60px rgba(255, 51, 102, 0.4),\n    0 8px 32px rgba(0, 0, 0, 0.3),\n    inset 0 2px 0 rgba(255, 255, 255, 0.3);\n  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);\n  position: relative;\n  overflow: hidden;\n  border: 2px solid rgba(255, 255, 255, 0.2);\n  animation: logoGlow 3s ease-in-out infinite alternate;\n}\n.v-logo:hover {\n  transform: translateY(-6px) scale(1.12) rotate(5deg);\n  box-shadow: 0 0 50px rgba(0, 212, 255, 0.8),\n    0 0 80px rgba(255, 51, 102, 0.6),\n    0 0 100px rgba(0, 255, 136, 0.4),\n    0 12px 48px rgba(0, 0, 0, 0.4),\n    inset 0 2px 0 rgba(255, 255, 255, 0.4);\n  animation: logoGlowIntense 1.5s ease-in-out infinite alternate;\n}\n.v-logo::before {\n  content: '';\n  position: absolute;\n  top: -50%;\n  left: -50%;\n  width: 200%;\n  height: 200%;\n  background: linear-gradient(45deg,\n    transparent,\n    rgba(255, 255, 255, 0.6),\n    rgba(0, 255, 255, 0.4),\n    rgba(255, 255, 0, 0.3),\n    transparent);\n  transform: rotate(45deg);\n  transition: all 1.2s ease;\n  opacity: 0;\n}\n.v-logo:hover::before {\n  animation: neonShimmer 2s ease-in-out infinite;\n}\n.v-logo-large {\n  width: 80px;\n  height: 80px;\n  border-radius: 24px;\n}\n.v-logo-large .v-letter {\n  font-size: 40px;\n}\n.v-logo-xl {\n  width: 112px;\n  height: 112px;\n  border-radius: 32px;\n}\n.v-logo-xl .v-letter {\n  font-size: 56px;\n}\n.v-letter {\n  font-size: 32px;\n  font-weight: 900;\n  color: #000000;\n  text-shadow: 0 0 8px rgba(255, 255, 255, 1),\n    0 0 16px rgba(255, 255, 255, 0.8),\n    0 2px 4px rgba(0, 0, 0, 0.6);\n  z-index: 2;\n  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;\n  letter-spacing: -0.02em;\n  background: none;\n  -webkit-text-fill-color: #000000;\n  filter: drop-shadow(0 0 3px rgba(255, 255, 255, 0.9));\n}\n.v-letter-white {\n  font-size: 32px;\n  font-weight: 900;\n  color: #ffffff;\n  text-shadow: 0 0 8px rgba(0, 0, 0, 1),\n    0 0 16px rgba(0, 0, 0, 0.8),\n    0 2px 4px rgba(255, 255, 255, 0.3);\n  z-index: 2;\n  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;\n  letter-spacing: -0.02em;\n  background: none;\n  -webkit-text-fill-color: #ffffff;\n  filter: drop-shadow(0 0 3px rgba(0, 0, 0, 0.9));\n}\n.v-logo-electric {\n  background: linear-gradient(135deg,\n    var(--electric-cyan) 0%,\n    var(--ice-blue) 50%,\n    var(--turbo-teal) 100%);\n  box-shadow: 0 0 40px rgba(0, 212, 255, 0.8),\n    0 0 80px rgba(68, 221, 255, 0.6),\n    0 8px 32px rgba(0, 0, 0, 0.3),\n    inset 0 2px 0 rgba(255, 255, 255, 0.4);\n  animation: electricPulse 2s ease-in-out infinite alternate;\n}\n.v-logo-neon {\n  background: linear-gradient(135deg,\n    var(--neon-pink) 0%,\n    var(--hot-magenta) 50%,\n    var(--cherry-pop) 100%);\n  box-shadow: 0 0 40px rgba(255, 51, 102, 0.8),\n    0 0 80px rgba(255, 0, 102, 0.6),\n    0 8px 32px rgba(0, 0, 0, 0.3),\n    inset 0 2px 0 rgba(255, 255, 255, 0.4);\n  animation: neonPulse 2.5s ease-in-out infinite alternate;\n}\n.v-logo-laser {\n  background: linear-gradient(135deg,\n    var(--laser-green) 0%,\n    var(--toxic-green) 50%,\n    var(--volt-lime) 100%);\n  box-shadow: 0 0 40px rgba(0, 255, 136, 0.8),\n    0 0 80px rgba(102, 255, 0, 0.6),\n    0 8px 32px rgba(0, 0, 0, 0.3),\n    inset 0 2px 0 rgba(255, 255, 255, 0.4);\n  animation: laserPulse 2.2s ease-in-out infinite alternate;\n}\n.v-logo-solar {\n  background: linear-gradient(135deg,\n    var(--solar-yellow) 0%,\n    var(--gold-rush) 50%,\n    var(--banana-bright) 100%);\n  box-shadow: 0 0 40px rgba(255, 255, 0, 0.8),\n    0 0 80px rgba(255, 204, 0, 0.6),\n    0 8px 32px rgba(0, 0, 0, 0.3),\n    inset 0 2px 0 rgba(255, 255, 255, 0.4);\n  animation: solarPulse 1.8s ease-in-out infinite alternate;\n}\n.v-logo-plasma {\n  background: linear-gradient(135deg,\n    var(--plasma-purple) 0%,\n    var(--royal-violet) 50%,\n    var(--grape-neon) 100%);\n  box-shadow: 0 0 40px rgba(170, 68, 255, 0.8),\n    0 0 80px rgba(102, 0, 255, 0.6),\n    0 8px 32px rgba(0, 0, 0, 0.3),\n    inset 0 2px 0 rgba(255, 255, 255, 0.4);\n  animation: plasmaPulse 2.8s ease-in-out infinite alternate;\n}\n.v-logo-fire {\n  background: linear-gradient(135deg,\n    var(--fire-orange) 0%,\n    var(--sunset-red) 50%,\n    var(--coral-bright) 100%);\n  box-shadow: 0 0 40px rgba(255, 102, 0, 0.8),\n    0 0 80px rgba(255, 51, 0, 0.6),\n    0 8px 32px rgba(0, 0, 0, 0.3),\n    inset 0 2px 0 rgba(255, 255, 255, 0.4);\n  animation: firePulse 1.5s ease-in-out infinite alternate;\n}\n@keyframes modernShimmer {\n  0% {\n    transform: translateX(-100%) rotate(45deg);\n    opacity: 0;\n  }\n  30% {\n    opacity: 0.6;\n  }\n  70% {\n    opacity: 0.8;\n  }\n  100% {\n    transform: translateX(100%) rotate(45deg);\n    opacity: 0;\n  }\n}\n@keyframes neonShimmer {\n  0% {\n    transform: translateX(-150%) rotate(45deg);\n    opacity: 0;\n  }\n  25% {\n    opacity: 0.6;\n  }\n  50% {\n    opacity: 1;\n    transform: translateX(0%) rotate(45deg);\n  }\n  75% {\n    opacity: 0.8;\n  }\n  100% {\n    transform: translateX(150%) rotate(45deg);\n    opacity: 0;\n  }\n}\n@keyframes logoGlow {\n  0% {\n    box-shadow: 0 0 30px rgba(0, 212, 255, 0.6),\n      0 0 60px rgba(255, 51, 102, 0.4),\n      0 8px 32px rgba(0, 0, 0, 0.3);\n  }\n  100% {\n    box-shadow: 0 0 50px rgba(0, 212, 255, 0.8),\n      0 0 80px rgba(255, 51, 102, 0.6),\n      0 0 100px rgba(0, 255, 136, 0.4),\n      0 8px 32px rgba(0, 0, 0, 0.3);\n  }\n}\n@keyframes logoGlowIntense {\n  0% {\n    box-shadow: 0 0 50px rgba(0, 212, 255, 0.8),\n      0 0 80px rgba(255, 51, 102, 0.6),\n      0 0 100px rgba(0, 255, 136, 0.4),\n      0 12px 48px rgba(0, 0, 0, 0.4);\n  }\n  100% {\n    box-shadow: 0 0 70px rgba(0, 212, 255, 1),\n      0 0 100px rgba(255, 51, 102, 0.8),\n      0 0 120px rgba(0, 255, 136, 0.6),\n      0 0 140px rgba(255, 255, 0, 0.4),\n      0 12px 48px rgba(0, 0, 0, 0.4);\n  }\n}\n@keyframes electricPulse {\n  0% {\n    box-shadow: 0 0 40px rgba(0, 212, 255, 0.8), 0 0 80px rgba(68, 221, 255, 0.6);\n  }\n  100% {\n    box-shadow: 0 0 60px rgba(0, 212, 255, 1), 0 0 120px rgba(68, 221, 255, 0.8);\n  }\n}\n@keyframes neonPulse {\n  0% {\n    box-shadow: 0 0 40px rgba(255, 51, 102, 0.8), 0 0 80px rgba(255, 0, 102, 0.6);\n  }\n  100% {\n    box-shadow: 0 0 60px rgba(255, 51, 102, 1), 0 0 120px rgba(255, 0, 102, 0.8);\n  }\n}\n@keyframes laserPulse {\n  0% {\n    box-shadow: 0 0 40px rgba(0, 255, 136, 0.8), 0 0 80px rgba(102, 255, 0, 0.6);\n  }\n  100% {\n    box-shadow: 0 0 60px rgba(0, 255, 136, 1), 0 0 120px rgba(102, 255, 0, 0.8);\n  }\n}\n@keyframes solarPulse {\n  0% {\n    box-shadow: 0 0 40px rgba(255, 255, 0, 0.8), 0 0 80px rgba(255, 204, 0, 0.6);\n  }\n  100% {\n    box-shadow: 0 0 60px rgba(255, 255, 0, 1), 0 0 120px rgba(255, 204, 0, 0.8);\n  }\n}\n@keyframes plasmaPulse {\n  0% {\n    box-shadow: 0 0 40px rgba(170, 68, 255, 0.8), 0 0 80px rgba(102, 0, 255, 0.6);\n  }\n  100% {\n    box-shadow: 0 0 60px rgba(170, 68, 255, 1), 0 0 120px rgba(102, 0, 255, 0.8);\n  }\n}\n@keyframes firePulse {\n  0% {\n    box-shadow: 0 0 40px rgba(255, 102, 0, 0.8), 0 0 80px rgba(255, 51, 0, 0.6);\n  }\n  100% {\n    box-shadow: 0 0 60px rgba(255, 102, 0, 1), 0 0 120px rgba(255, 51, 0, 0.8);\n  }\n}\n@keyframes textGlow {\n  0% {\n    text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);\n  }\n  100% {\n    text-shadow: 0 0 30px rgba(0, 212, 255, 0.8), 0 0 40px rgba(255, 51, 102, 0.4);\n  }\n}\n@keyframes electricTextPulse {\n  0% {\n    text-shadow: 0 0 15px rgba(0, 212, 255, 0.6);\n  }\n  100% {\n    text-shadow: 0 0 25px rgba(0, 212, 255, 1), 0 0 35px rgba(68, 221, 255, 0.6);\n  }\n}\n@keyframes neonTextPulse {\n  0% {\n    text-shadow: 0 0 15px rgba(255, 51, 102, 0.6);\n  }\n  100% {\n    text-shadow: 0 0 25px rgba(255, 51, 102, 1), 0 0 35px rgba(255, 0, 102, 0.6);\n  }\n}\n@keyframes laserTextPulse {\n  0% {\n    text-shadow: 0 0 15px rgba(0, 255, 136, 0.6);\n  }\n  100% {\n    text-shadow: 0 0 25px rgba(0, 255, 136, 1), 0 0 35px rgba(102, 255, 0, 0.6);\n  }\n}\n@keyframes solarTextPulse {\n  0% {\n    text-shadow: 0 0 15px rgba(255, 255, 0, 0.6);\n  }\n  100% {\n    text-shadow: 0 0 25px rgba(255, 255, 0, 1), 0 0 35px rgba(255, 204, 0, 0.6);\n  }\n}\n@keyframes plasmaTextPulse {\n  0% {\n    text-shadow: 0 0 15px rgba(170, 68, 255, 0.6);\n  }\n  100% {\n    text-shadow: 0 0 25px rgba(170, 68, 255, 1), 0 0 35px rgba(102, 0, 255, 0.6);\n  }\n}\n@keyframes fireTextPulse {\n  0% {\n    text-shadow: 0 0 15px rgba(255, 102, 0, 0.6);\n  }\n  100% {\n    text-shadow: 0 0 25px rgba(255, 102, 0, 1), 0 0 35px rgba(255, 51, 0, 0.6);\n  }\n}\n@keyframes rainbowText {\n  0% {\n    background-position: 0% 50%;\n  }\n  50% {\n    background-position: 100% 50%;\n  }\n  100% {\n    background-position: 0% 50%;\n  }\n}\n@keyframes highlightPulse {\n  0% {\n    box-shadow: 0 0 20px rgba(0, 212, 255, 0.4), 0 4px 12px rgba(0, 0, 0, 0.2);\n    transform: scale(1);\n  }\n  100% {\n    box-shadow: 0 0 30px rgba(0, 212, 255, 0.7), 0 6px 18px rgba(0, 0, 0, 0.3);\n    transform: scale(1.02);\n  }\n}\n@keyframes urgentBlink {\n  0%, 50% {\n    box-shadow: 0 0 20px rgba(255, 102, 0, 0.5), 0 4px 12px rgba(0, 0, 0, 0.2);\n    opacity: 1;\n  }\n  25%, 75% {\n    box-shadow: 0 0 40px rgba(255, 102, 0, 0.8), 0 6px 18px rgba(0, 0, 0, 0.3);\n    opacity: 0.8;\n  }\n}\n@keyframes successGlow {\n  0% {\n    box-shadow: 0 0 20px rgba(0, 255, 136, 0.4), 0 4px 12px rgba(0, 0, 0, 0.2);\n  }\n  100% {\n    box-shadow: 0 0 35px rgba(0, 255, 136, 0.7), 0 6px 18px rgba(0, 0, 0, 0.3);\n  }\n}\n@keyframes premiumShine {\n  0% {\n    box-shadow: 0 0 20px rgba(170, 68, 255, 0.5), 0 4px 12px rgba(0, 0, 0, 0.2);\n  }\n  50% {\n    box-shadow: 0 0 40px rgba(170, 68, 255, 0.8), 0 6px 18px rgba(0, 0, 0, 0.3);\n  }\n  100% {\n    box-shadow: 0 0 20px rgba(170, 68, 255, 0.5), 0 4px 12px rgba(0, 0, 0, 0.2);\n  }\n}\n@keyframes heroTitleFlow {\n  0% {\n    background-position: 0% 50%;\n  }\n  50% {\n    background-position: 100% 50%;\n  }\n  100% {\n    background-position: 0% 50%;\n  }\n}\n@keyframes titleGlow {\n  0% {\n    opacity: 0.1;\n  }\n  100% {\n    opacity: 0.2;\n  }\n}\n@keyframes sectionTitlePulse {\n  0% {\n    text-shadow: 0 0 20px rgba(0, 212, 255, 0.4);\n  }\n  100% {\n    text-shadow: 0 0 30px rgba(0, 212, 255, 0.7), 0 0 40px rgba(255, 51, 102, 0.3);\n  }\n}\n@keyframes cardTitleGlow {\n  0% {\n    text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);\n  }\n  100% {\n    text-shadow: 0 0 20px rgba(0, 212, 255, 0.8);\n  }\n}\n@keyframes heroBackground {\n  0% {\n    opacity: 0.05;\n  }\n  50% {\n    opacity: 0.1;\n  }\n  100% {\n    opacity: 0.05;\n  }\n}\n@keyframes attentionBounce {\n  0%, 20%, 50%, 80%, 100% {\n    transform: translateY(0);\n  }\n  40% {\n    transform: translateY(-8px);\n  }\n  60% {\n    transform: translateY(-4px);\n  }\n}\n@keyframes sparkle {\n  0%, 100% {\n    opacity: 1;\n    transform: scale(1) rotate(0deg);\n  }\n  50% {\n    opacity: 0.7;\n    transform: scale(1.2) rotate(180deg);\n  }\n}\n@keyframes pulseGlowEffect {\n  0% {\n    box-shadow: 0 0 5px rgba(0, 212, 255, 0.3);\n    transform: scale(1);\n  }\n  100% {\n    box-shadow: 0 0 20px rgba(0, 212, 255, 0.6), 0 0 30px rgba(255, 51, 102, 0.3);\n    transform: scale(1.02);\n  }\n}\n@keyframes shakeAttention {\n  0%, 100% {\n    transform: translateX(0);\n  }\n  10%, 30%, 50%, 70%, 90% {\n    transform: translateX(-2px);\n  }\n  20%, 40%, 60%, 80% {\n    transform: translateX(2px);\n  }\n}\n@keyframes bounceIn {\n  0% {\n    opacity: 0;\n    transform: scale(0.3) translateY(-50px);\n  }\n  50% {\n    opacity: 1;\n    transform: scale(1.05) translateY(0);\n  }\n  70% {\n    transform: scale(0.95);\n  }\n  100% {\n    opacity: 1;\n    transform: scale(1);\n  }\n}\n@keyframes strongEmphasis {\n  0% {\n    text-shadow: 0 0 10px rgba(0, 212, 255, 0.6);\n  }\n  100% {\n    text-shadow: 0 0 20px rgba(0, 212, 255, 1), 0 0 30px rgba(68, 221, 255, 0.5);\n  }\n}\n@keyframes urgentEmphasis {\n  0%, 100% {\n    text-shadow: 0 0 10px rgba(255, 102, 0, 0.6);\n    transform: scale(1);\n  }\n  50% {\n    text-shadow: 0 0 20px rgba(255, 102, 0, 1), 0 0 30px rgba(255, 51, 0, 0.5);\n    transform: scale(1.05);\n  }\n}\n@keyframes successEmphasis {\n  0% {\n    text-shadow: 0 0 10px rgba(0, 255, 136, 0.6);\n  }\n  100% {\n    text-shadow: 0 0 20px rgba(0, 255, 136, 1), 0 0 30px rgba(102, 255, 0, 0.5);\n  }\n}\n@keyframes premiumEmphasis {\n  0% {\n    text-shadow: 0 0 10px rgba(170, 68, 255, 0.6);\n  }\n  33% {\n    text-shadow: 0 0 15px rgba(170, 68, 255, 0.8), 0 0 25px rgba(102, 0, 255, 0.4);\n  }\n  66% {\n    text-shadow: 0 0 20px rgba(170, 68, 255, 1), 0 0 30px rgba(102, 0, 255, 0.6);\n  }\n  100% {\n    text-shadow: 0 0 10px rgba(170, 68, 255, 0.6);\n  }\n}\n@keyframes newBadgePulse {\n  0% {\n    box-shadow: 0 0 15px rgba(0, 255, 136, 0.5);\n    transform: scale(1);\n  }\n  100% {\n    box-shadow: 0 0 25px rgba(0, 255, 136, 0.8), 0 0 35px rgba(102, 255, 0, 0.4);\n    transform: scale(1.05);\n  }\n}\n@keyframes hotBadgeFlicker {\n  0%, 100% {\n    box-shadow: 0 0 15px rgba(255, 102, 0, 0.5);\n    opacity: 1;\n  }\n  50% {\n    box-shadow: 0 0 25px rgba(255, 102, 0, 0.8), 0 0 35px rgba(255, 51, 0, 0.6);\n    opacity: 0.9;\n  }\n}\n@keyframes premiumBadgeShine {\n  0% {\n    box-shadow: 0 0 15px rgba(170, 68, 255, 0.5);\n  }\n  50% {\n    box-shadow: 0 0 25px rgba(170, 68, 255, 0.8), 0 0 35px rgba(102, 0, 255, 0.4);\n  }\n  100% {\n    box-shadow: 0 0 15px rgba(170, 68, 255, 0.5);\n  }\n}\n@keyframes limitedBadgeBlink {\n  0%, 50% {\n    box-shadow: 0 0 15px rgba(255, 255, 0, 0.5);\n    opacity: 1;\n  }\n  25%, 75% {\n    box-shadow: 0 0 25px rgba(255, 255, 0, 0.8), 0 0 35px rgba(255, 204, 0, 0.6);\n    opacity: 0.8;\n  }\n}\n@keyframes notificationGlow {\n  0% {\n    box-shadow: 0 0 25px rgba(0, 212, 255, 0.3), 0 8px 32px rgba(0, 0, 0, 0.1);\n  }\n  100% {\n    box-shadow: 0 0 35px rgba(0, 212, 255, 0.5), 0 0 45px rgba(255, 51, 102, 0.2), 0 8px 32px rgba(0, 0, 0, 0.1);\n  }\n}\n@keyframes iconBounce {\n  0%, 20%, 50%, 80%, 100% {\n    transform: translateY(-50%) scale(1);\n  }\n  40% {\n    transform: translateY(-60%) scale(1.1);\n  }\n  60% {\n    transform: translateY(-55%) scale(1.05);\n  }\n}\n@keyframes pulseGlow {\n  0%, 100% {\n    box-shadow: 0 8px 32px rgba(10, 61, 98, 0.25),\n      0 4px 16px rgba(41, 128, 185, 0.15),\n      inset 0 1px 0 rgba(255, 255, 255, 0.2);\n  }\n  50% {\n    box-shadow: 0 12px 48px rgba(10, 61, 98, 0.4),\n      0 6px 24px rgba(41, 128, 185, 0.3),\n      inset 0 1px 0 rgba(255, 255, 255, 0.3);\n  }\n}\n@keyframes floatAnimation {\n  0%, 100% {\n    transform: translateY(0px) scale(1);\n  }\n  50% {\n    transform: translateY(-6px) scale(1.02);\n  }\n}\n.hero-section::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\") repeat;\n  opacity: 0.3;\n}\n.service-card {\n  background: rgba(255, 255, 255, 0.98);\n  backdrop-filter: blur(24px);\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);\n  border-radius: 24px;\n  box-shadow: 0 8px 32px rgba(10, 61, 98, 0.08),\n    0 4px 16px rgba(41, 128, 185, 0.05),\n    inset 0 1px 0 rgba(255, 255, 255, 0.8);\n  position: relative;\n  overflow: hidden;\n}\n.service-card:hover {\n  transform: translateY(-16px) scale(1.03) rotate(1deg);\n  box-shadow: 0 32px 64px rgba(10, 61, 98, 0.15),\n    0 16px 32px rgba(41, 128, 185, 0.1),\n    inset 0 1px 0 rgba(255, 255, 255, 0.9);\n  background: rgba(255, 255, 255, 1);\n  border-color: var(--ocean-teal);\n}\n.service-card-bus {\n  background: linear-gradient(135deg,\n    var(--electric-cyan) 0%,\n    var(--cyber-blue) 50%,\n    var(--ice-blue) 100%);\n  color: #000000;\n  font-weight: 800;\n  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);\n  box-shadow: 0 0 30px rgba(0, 212, 255, 0.6),\n    0 0 60px rgba(0, 153, 255, 0.4),\n    0 12px 40px rgba(0, 0, 0, 0.3);\n  border: 2px solid rgba(0, 255, 255, 0.3);\n}\n.service-card-carpool {\n  background: linear-gradient(135deg,\n    var(--laser-green) 0%,\n    var(--toxic-green) 50%,\n    var(--mint-electric) 100%);\n  color: #000000;\n  font-weight: 800;\n  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);\n  box-shadow: 0 0 30px rgba(0, 255, 136, 0.6),\n    0 0 60px rgba(102, 255, 0, 0.4),\n    0 12px 40px rgba(0, 0, 0, 0.3);\n  border: 2px solid rgba(0, 255, 153, 0.3);\n}\n.service-card-bike {\n  background: linear-gradient(135deg,\n    var(--fire-orange) 0%,\n    var(--sunset-red) 50%,\n    var(--coral-bright) 100%);\n  color: #ffffff;\n  font-weight: 800;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);\n  box-shadow: 0 0 30px rgba(255, 102, 0, 0.6),\n    0 0 60px rgba(255, 51, 0, 0.4),\n    0 12px 40px rgba(0, 0, 0, 0.3);\n  border: 2px solid rgba(255, 102, 51, 0.3);\n}\n.service-card::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg,\n    transparent,\n    rgba(255, 255, 255, 0.2),\n    transparent);\n  transition: left 0.8s ease;\n}\n.service-card:hover::before {\n  left: 100%;\n}\n.btn-primary {\n  background: linear-gradient(135deg,\n    var(--electric-cyan) 0%,\n    var(--cyber-blue) 50%,\n    var(--neon-blue) 100%);\n  border: 2px solid rgba(0, 255, 255, 0.3);\n  color: #000000;\n  font-weight: 800;\n  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);\n  border-radius: 20px;\n  padding: 18px 36px;\n  font-size: 16px;\n  box-shadow: 0 0 20px rgba(0, 212, 255, 0.5),\n    0 0 40px rgba(0, 153, 255, 0.3),\n    0 8px 24px rgba(0, 0, 0, 0.3),\n    inset 0 2px 0 rgba(255, 255, 255, 0.3);\n  position: relative;\n  overflow: hidden;\n  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.9);\n}\n.btn-primary:hover {\n  transform: translateY(-4px) scale(1.05);\n  box-shadow: 0 0 30px rgba(0, 212, 255, 0.8),\n    0 0 60px rgba(0, 153, 255, 0.5),\n    0 0 80px rgba(0, 255, 255, 0.3),\n    0 12px 36px rgba(0, 0, 0, 0.4),\n    inset 0 2px 0 rgba(255, 255, 255, 0.4);\n  background: linear-gradient(135deg,\n    var(--neon-blue) 0%,\n    var(--electric-cyan) 50%,\n    var(--turbo-teal) 100%);\n}\n.btn-secondary {\n  background: linear-gradient(135deg,\n    var(--neon-pink) 0%,\n    var(--hot-magenta) 50%,\n    var(--cherry-pop) 100%);\n  border: 2px solid rgba(255, 51, 102, 0.3);\n  color: #ffffff;\n  font-weight: 800;\n  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);\n  border-radius: 20px;\n  padding: 18px 36px;\n  font-size: 16px;\n  box-shadow: 0 0 20px rgba(255, 51, 102, 0.5),\n    0 0 40px rgba(255, 0, 102, 0.3),\n    0 8px 24px rgba(0, 0, 0, 0.3),\n    inset 0 2px 0 rgba(255, 255, 255, 0.3);\n  position: relative;\n  overflow: hidden;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);\n}\n.btn-secondary:hover {\n  transform: translateY(-4px) scale(1.05);\n  box-shadow: 0 0 30px rgba(255, 51, 102, 0.8),\n    0 0 60px rgba(255, 0, 102, 0.5),\n    0 0 80px rgba(255, 0, 128, 0.3),\n    0 12px 36px rgba(0, 0, 0, 0.4),\n    inset 0 2px 0 rgba(255, 255, 255, 0.4);\n  background: linear-gradient(135deg,\n    var(--cherry-pop) 0%,\n    var(--neon-pink) 50%,\n    var(--hot-magenta) 100%);\n}\n.btn-premium {\n  background: linear-gradient(135deg,\n    var(--plasma-purple) 0%,\n    var(--royal-violet) 50%,\n    var(--grape-neon) 100%);\n  border: 2px solid rgba(170, 68, 255, 0.3);\n  color: #ffffff;\n  font-weight: 700;\n  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);\n  border-radius: 20px;\n  padding: 18px 36px;\n  font-size: 16px;\n  box-shadow: 0 0 20px rgba(170, 68, 255, 0.5),\n    0 0 40px rgba(102, 0, 255, 0.3),\n    0 8px 24px rgba(0, 0, 0, 0.3),\n    inset 0 2px 0 rgba(255, 255, 255, 0.3);\n  position: relative;\n  overflow: hidden;\n  text-shadow: 0 0 10px rgba(255, 255, 255, 0.8);\n}\n.btn-premium:hover {\n  transform: translateY(-4px) scale(1.05);\n  box-shadow: 0 0 30px rgba(170, 68, 255, 0.8),\n    0 0 60px rgba(102, 0, 255, 0.5),\n    0 0 80px rgba(128, 0, 255, 0.3),\n    0 12px 36px rgba(0, 0, 0, 0.4),\n    inset 0 2px 0 rgba(255, 255, 255, 0.4);\n}\n.btn-outline {\n  background: transparent;\n  border: 2px solid var(--ocean-teal);\n  color: var(--ocean-teal);\n  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);\n  border-radius: 16px;\n  padding: 14px 30px;\n  font-weight: 600;\n  font-size: 16px;\n  position: relative;\n  overflow: hidden;\n}\n.btn-outline:hover {\n  background: linear-gradient(135deg, var(--ocean-teal) 0%, var(--azure-sky) 100%);\n  color: white;\n  transform: translateY(-3px) scale(1.02);\n  box-shadow: 0 8px 24px rgba(41, 128, 185, 0.3),\n    0 4px 12px rgba(59, 130, 246, 0.2);\n  border-color: var(--azure-sky);\n}\n.btn-primary::before,\n.btn-secondary::before,\n.btn-premium::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg,\n    transparent,\n    rgba(255, 255, 255, 0.3),\n    transparent);\n  transition: left 0.6s ease;\n}\n.btn-primary:hover::before,\n.btn-secondary:hover::before,\n.btn-premium:hover::before {\n  left: 100%;\n}\n.attention-grabber {\n  position: relative;\n  display: inline-block;\n  animation: attentionBounce 2s ease-in-out infinite;\n}\n.attention-grabber::before {\n  content: '⚡';\n  position: absolute;\n  top: -10px;\n  right: -10px;\n  font-size: 1.2em;\n  animation: sparkle 1.5s ease-in-out infinite;\n}\n.pulse-glow {\n  animation: pulseGlowEffect 2s ease-in-out infinite alternate;\n}\n.shake-attention {\n  animation: shakeAttention 0.5s ease-in-out;\n}\n.bounce-in {\n  animation: bounceIn 0.8s ease-out;\n}\n.zoom-attention {\n  transition: all 0.3s ease;\n}\n.zoom-attention:hover {\n  transform: scale(1.1);\n  z-index: 10;\n}\n.emphasis-strong {\n  font-weight: 900;\n  color: var(--electric-cyan);\n  text-shadow: 0 0 10px rgba(0, 212, 255, 0.6);\n  animation: strongEmphasis 2s ease-in-out infinite alternate;\n}\n.emphasis-urgent {\n  font-weight: 800;\n  color: var(--fire-orange);\n  text-shadow: 0 0 10px rgba(255, 102, 0, 0.6);\n  animation: urgentEmphasis 1s ease-in-out infinite;\n}\n.emphasis-success {\n  font-weight: 800;\n  color: var(--laser-green);\n  text-shadow: 0 0 10px rgba(0, 255, 136, 0.6);\n  animation: successEmphasis 2.5s ease-in-out infinite alternate;\n}\n.emphasis-premium {\n  font-weight: 800;\n  color: var(--plasma-purple);\n  text-shadow: 0 0 10px rgba(170, 68, 255, 0.6);\n  animation: premiumEmphasis 3s ease-in-out infinite;\n}\n.badge-new {\n  background: linear-gradient(135deg, var(--laser-green) 0%, var(--toxic-green) 100%);\n  color: #000000;\n  padding: 4px 12px;\n  border-radius: 20px;\n  font-size: 0.75rem;\n  font-weight: 800;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  box-shadow: 0 0 15px rgba(0, 255, 136, 0.5);\n  animation: newBadgePulse 2s ease-in-out infinite alternate;\n}\n.badge-hot {\n  background: linear-gradient(135deg, var(--fire-orange) 0%, var(--sunset-red) 100%);\n  color: #ffffff;\n  padding: 4px 12px;\n  border-radius: 20px;\n  font-size: 0.75rem;\n  font-weight: 800;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  box-shadow: 0 0 15px rgba(255, 102, 0, 0.5);\n  animation: hotBadgeFlicker 1.5s ease-in-out infinite;\n}\n.badge-premium {\n  background: linear-gradient(135deg, var(--plasma-purple) 0%, var(--royal-violet) 100%);\n  color: #ffffff;\n  padding: 4px 12px;\n  border-radius: 20px;\n  font-size: 0.75rem;\n  font-weight: 800;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  box-shadow: 0 0 15px rgba(170, 68, 255, 0.5);\n  animation: premiumBadgeShine 3s ease-in-out infinite;\n}\n.badge-limited {\n  background: linear-gradient(135deg, var(--solar-yellow) 0%, var(--gold-rush) 100%);\n  color: #000000;\n  padding: 4px 12px;\n  border-radius: 20px;\n  font-size: 0.75rem;\n  font-weight: 800;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  box-shadow: 0 0 15px rgba(255, 255, 0, 0.5);\n  animation: limitedBadgeBlink 1s ease-in-out infinite;\n}\n.notification-highlight {\n  background: linear-gradient(135deg, var(--electric-cyan) 0%, var(--neon-pink) 100%);\n  color: #000000;\n  padding: 16px 24px;\n  border-radius: 16px;\n  border-left: 4px solid var(--solar-yellow);\n  font-weight: 700;\n  box-shadow: 0 0 25px rgba(0, 212, 255, 0.3),\n    0 8px 32px rgba(0, 0, 0, 0.1);\n  animation: notificationGlow 3s ease-in-out infinite alternate;\n  position: relative;\n}\n.notification-highlight::before {\n  content: '💡';\n  position: absolute;\n  left: -12px;\n  top: 50%;\n  transform: translateY(-50%);\n  font-size: 1.5em;\n  animation: iconBounce 2s ease-in-out infinite;\n}\n.btn-primary:hover {\n  background: linear-gradient(135deg, var(--sky-blue-dark) 0%, #4a9bc7 100%);\n  transform: translateY(-2px);\n  box-shadow: 0 8px 20px rgba(135, 206, 235, 0.4);\n  color: white;\n}\n.btn-secondary {\n  background: linear-gradient(135deg, var(--light-orange) 0%, var(--light-orange-dark) 100%);\n  border: none;\n  color: var(--travel-black);\n  transition: all 0.3s ease;\n  border-radius: 8px;\n  padding: 12px 24px;\n  font-weight: 600;\n}\n.btn-secondary:hover {\n  background: linear-gradient(135deg, var(--light-orange-dark) 0%, #ff9f4d 100%);\n  transform: translateY(-2px);\n  box-shadow: 0 8px 20px rgba(255, 165, 0, 0.4);\n  color: var(--travel-black);\n}\n.navbar {\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border-bottom: 1px solid rgba(135, 206, 235, 0.2);\n}\n.footer {\n  background: linear-gradient(135deg, var(--travel-black) 0%, var(--travel-black-light) 100%);\n}\n.form-input {\n  border: 2px solid var(--border);\n  transition: all 0.3s ease;\n  border-radius: 8px;\n}\n.form-input:focus {\n  border-color: var(--sky-blue);\n  box-shadow: 0 0 0 3px rgba(135, 206, 235, 0.1);\n  outline: none;\n}\n.shadow-travel {\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);\n}\n.shadow-travel-lg {\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n}\n.shadow-sky-blue {\n  box-shadow: 0 8px 20px rgba(135, 206, 235, 0.4);\n}\n.shadow-orange {\n  box-shadow: 0 8px 20px rgba(255, 165, 0, 0.4);\n}\n.bg-travel-gradient {\n  background: linear-gradient(135deg, var(--sky-blue) 0%, var(--light-orange) 50%, var(--sky-blue) 100%);\n}\n.v-logo {\n  width: 40px;\n  height: 40px;\n  background: linear-gradient(135deg, var(--sky-blue) 0%, var(--light-orange) 100%);\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  box-shadow: 0 8px 20px rgba(135, 206, 235, 0.4);\n  transition: all 0.3s ease;\n  overflow: hidden;\n}\n.v-logo::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\n  transition: left 0.6s;\n}\n.v-logo:hover::before {\n  left: 100%;\n}\n.v-logo:hover {\n  transform: translateY(-2px) scale(1.05);\n  box-shadow: 0 12px 30px rgba(135, 206, 235, 0.6);\n}\n.v-letter {\n  font-size: 18px;\n  font-weight: 900;\n  color: white;\n  font-family: 'Inter', sans-serif;\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n  position: relative;\n  z-index: 1;\n}\n.v-logo-large {\n  width: 80px;\n  height: 80px;\n  background: linear-gradient(135deg, var(--sky-blue) 0%, var(--light-orange) 50%, var(--sky-blue) 100%);\n  border-radius: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  box-shadow: 0 15px 35px rgba(135, 206, 235, 0.4);\n  transition: all 0.4s ease;\n  overflow: hidden;\n  animation: logoFloat 3s ease-in-out infinite;\n}\n.v-logo-large::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);\n  transition: left 0.8s;\n}\n.v-logo-large:hover::before {\n  left: 100%;\n}\n.v-logo-large:hover {\n  transform: translateY(-4px) scale(1.08);\n  box-shadow: 0 20px 45px rgba(135, 206, 235, 0.6);\n}\n.v-letter-large {\n  font-size: 36px;\n  font-weight: 900;\n  color: white;\n  font-family: 'Inter', sans-serif;\n  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);\n  position: relative;\n  z-index: 1;\n}\n@keyframes logoFloat {\n  0%, 100% {\n    transform: translateY(0px);\n  }\n  50% {\n    transform: translateY(-8px);\n  }\n}\n.v-logo-minimal {\n  width: 32px;\n  height: 32px;\n  background: linear-gradient(45deg, var(--sky-blue), var(--light-orange));\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 4px 12px rgba(135, 206, 235, 0.3);\n}\n.v-logo-minimal .v-letter {\n  font-size: 14px;\n  font-weight: 800;\n}\n.v-logo-glow {\n  width: 48px;\n  height: 48px;\n  background: linear-gradient(135deg, var(--sky-blue) 0%, var(--light-orange) 100%);\n  border-radius: 14px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  box-shadow: 0 0 20px rgba(135, 206, 235, 0.5),\n    0 8px 20px rgba(135, 206, 235, 0.3);\n  animation: logoGlow 2s ease-in-out infinite alternate;\n}\n.v-logo-glow .v-letter {\n  font-size: 20px;\n  font-weight: 900;\n  color: white;\n  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);\n}\n@keyframes logoGlow {\n  0% {\n    box-shadow: 0 0 20px rgba(135, 206, 235, 0.5),\n      0 8px 20px rgba(135, 206, 235, 0.3);\n  }\n  100% {\n    box-shadow: 0 0 30px rgba(135, 206, 235, 0.8),\n      0 12px 30px rgba(135, 206, 235, 0.5);\n  }\n}\n.loading-spinner {\n  border: 3px solid hsl(var(--muted));\n  border-top: 3px solid hsl(var(--sky-blue));\n  border-radius: 50%;\n  width: 24px;\n  height: 24px;\n  animation: spin 1s linear infinite;\n}\n@keyframes spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n@media (max-width: 768px) {\n  .hero-section {\n    padding: 2rem 1rem;\n  }\n  .service-card {\n    margin-bottom: 1rem;\n  }\n}\n@media (prefers-reduced-motion: reduce) {\n  .hover-lift,\n  .btn-primary,\n  .btn-secondary,\n  .service-card {\n    transition: none;\n  }\n  .animate-float,\n  .animate-pulse-slow,\n  .animate-slide-up,\n  .animate-slide-left,\n  .animate-slide-right {\n    animation: none;\n  }\n}\n@property --tw-translate-x {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-translate-y {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-translate-z {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-space-y-reverse {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-space-x-reverse {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-border-style {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: solid;\n}\n@property --tw-gradient-position {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-gradient-from {\n  syntax: \"<color>\";\n  inherits: false;\n  initial-value: #0000;\n}\n@property --tw-gradient-via {\n  syntax: \"<color>\";\n  inherits: false;\n  initial-value: #0000;\n}\n@property --tw-gradient-to {\n  syntax: \"<color>\";\n  inherits: false;\n  initial-value: #0000;\n}\n@property --tw-gradient-stops {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-gradient-via-stops {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-gradient-from-position {\n  syntax: \"<length-percentage>\";\n  inherits: false;\n  initial-value: 0%;\n}\n@property --tw-gradient-via-position {\n  syntax: \"<length-percentage>\";\n  inherits: false;\n  initial-value: 50%;\n}\n@property --tw-gradient-to-position {\n  syntax: \"<length-percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-leading {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-font-weight {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-tracking {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-inset-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-inset-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-inset-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-ring-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ring-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-inset-ring-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-inset-ring-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-ring-inset {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ring-offset-width {\n  syntax: \"<length>\";\n  inherits: false;\n  initial-value: 0px;\n}\n@property --tw-ring-offset-color {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: #fff;\n}\n@property --tw-ring-offset-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-outline-style {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: solid;\n}\n@property --tw-blur {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-brightness {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-contrast {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-grayscale {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-hue-rotate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-invert {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-opacity {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-saturate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-sepia {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-drop-shadow-size {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-duration {\n  syntax: \"*\";\n  inherits: false;\n}\n@keyframes spin {\n  to {\n    transform: rotate(360deg);\n  }\n}\n@keyframes pulse {\n  50% {\n    opacity: 0.5;\n  }\n}\n@keyframes bounce {\n  0%, 100% {\n    transform: translateY(-25%);\n    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);\n  }\n  50% {\n    transform: none;\n    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\n  }\n}\n@layer properties {\n  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {\n    *, ::before, ::after, ::backdrop {\n      --tw-translate-x: 0;\n      --tw-translate-y: 0;\n      --tw-translate-z: 0;\n      --tw-space-y-reverse: 0;\n      --tw-space-x-reverse: 0;\n      --tw-border-style: solid;\n      --tw-gradient-position: initial;\n      --tw-gradient-from: #0000;\n      --tw-gradient-via: #0000;\n      --tw-gradient-to: #0000;\n      --tw-gradient-stops: initial;\n      --tw-gradient-via-stops: initial;\n      --tw-gradient-from-position: 0%;\n      --tw-gradient-via-position: 50%;\n      --tw-gradient-to-position: 100%;\n      --tw-leading: initial;\n      --tw-font-weight: initial;\n      --tw-tracking: initial;\n      --tw-shadow: 0 0 #0000;\n      --tw-shadow-color: initial;\n      --tw-shadow-alpha: 100%;\n      --tw-inset-shadow: 0 0 #0000;\n      --tw-inset-shadow-color: initial;\n      --tw-inset-shadow-alpha: 100%;\n      --tw-ring-color: initial;\n      --tw-ring-shadow: 0 0 #0000;\n      --tw-inset-ring-color: initial;\n      --tw-inset-ring-shadow: 0 0 #0000;\n      --tw-ring-inset: initial;\n      --tw-ring-offset-width: 0px;\n      --tw-ring-offset-color: #fff;\n      --tw-ring-offset-shadow: 0 0 #0000;\n      --tw-outline-style: solid;\n      --tw-blur: initial;\n      --tw-brightness: initial;\n      --tw-contrast: initial;\n      --tw-grayscale: initial;\n      --tw-hue-rotate: initial;\n      --tw-invert: initial;\n      --tw-opacity: initial;\n      --tw-saturate: initial;\n      --tw-sepia: initial;\n      --tw-drop-shadow: initial;\n      --tw-drop-shadow-color: initial;\n      --tw-drop-shadow-alpha: 100%;\n      --tw-drop-shadow-size: initial;\n      --tw-duration: initial;\n    }\n  }\n}\n"], "names": [], "mappings": "AACA;EA0tHE;IACE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA1tHJ;EAEE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAFF;EAwFE;;;;;;;EAMA;;;;;;;;;;EASA;;;;;;EAKA;;;;EAIA;;;;;EAIA;;;;;;EAKA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;;;;;;;EAUA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;IACE;;;;IAEE;MAAgD;;;;;;EAKpD;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;AApOF;;AAAA;EAyOE;;;;EAIA;;;;EAGA;;;;;;;;;;;;EAWA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAIE;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAMF;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;;EAKA;;;;;;EAKA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAGE;IAAgC;;;;;;EAKlC;;;;;EAIA;;;;;EAIA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAKE;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;;EAOE;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAOzB;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;;EAGE;IAAgC;;;;;;EAOlC;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAME;;;;;;;EAUA;;;;EAOA;;;;EAOA;;;;EAMF;;;;;EAMA;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAOI;;;;EASA;;;;EASA;;;;;AAOR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6DA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AASA;;;;;AAWA;;;;;AAOA;;;;;AAOA;;;;AAQA;;;;AAOA;;;;AAQA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;;;;;;;AAcA;;;;;;;;;;AASA;;;;;;;;;;AASA;;;;;;;;;;AASA;;;;;;;;;;AASA;;;;;;;;;;AASA;;;;;;;;;;AASA;;;;;;;;;;;AAkBA;;;;;;;;;;AAQA;;;;;;;;;;;;AAUA;;;;;;;;;;;;AAUA;;;;;;;;;;;;AAUA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;;;;;;;;AAcA;;;;;AAKA;;;;;;;;;;;;AAYA;;;;;;;;;;;;AAYA;;;;;;;;;;;;AAYA;;;;;;;;;;;;;;;AAmBA;;;;;;;;;;;AAkBA;;;;;;;;;;;;AAWA;;;;;;;;;AAQA;;;;;;;AASA;;;;;;;;;;AAiBA;;;;;;;;;;;;;;;;AAuBA;;;;;;AASA;;;;;;;;;;;;;AAiBA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;;;;;;;;;;AAcA;;;;;;;;;;;;;AAcA;;;;;;AAWA;;;;;;AAWA;;;;;;AAWA;;;;;;AAWA;;;;;;AAWA;;;;;;AAWA;;;;;;;;;;;;;;;;;;;;AAgBA;;;;;;;;;;;;;;;;;;;;;;;;;AA46BA;;;;;;;;;;AA34BA;;;;;;;;;;AAeA;;;;;;;;;;AAQA;;;;;;;;;;AAQA;;;;;;;;;;AAQA;;;;;;;;;;AAQA;;;;;;;;;;AAQA;;;;;;;;;;AAQA;;;;;;;;;;AAQA;;;;;;;;;;AAQA;;;;;;;;;;AAQA;;;;;;;;;;AAQA;;;;;;;;;;AAQA;;;;;;;;;;AAQA;;;;;;;;;;AAQA;;;;;;;;;;;;;;AAWA;;;;;;;;;;;;AAUA;;;;;;;;;;;;AAUA;;;;;;;;;;AAQA;;;;;;;;;;;;;;AAWA;;;;;;;;;;;;;;AAWA;;;;;;;;;;AAQA;;;;;;;;;;AAQA;;;;;;;;;;AAQA;;;;;;;;;;;;;;AAWA;;;;;;;;;;;;;;AAWA;;;;;;;;;;;;AAUA;;;;;;;;;;;;AAUA;;;;;;;;;;;;;;AAWA;;;;;;;;;;;;;;;;;;;;;AAiBA;;;;;;;;;;AAQA;;;;;;;;;;;;AAUA;;;;;;;;;;AAQA;;;;;;;;;;;;;;;;;;AAcA;;;;;;;;;;;;AAUA;;;;;;;;;;;;AAUA;;;;;;;;;;;;;;AAWA;;;;;;;;;;;;AAUA;;;;;;;;;;AAQA;;;;;;;;;;;;;;AAWA;;;;;;;;;;AAYA;;;;;;;;;;AAQA;;;;;;;;AAUA;;;;;;;;;;;AAYA;;;;;;;AAQA;;;;;;;;;AAaA;;;;;;;;;AAaA;;;;;;;;;AAaA;;;;;;;;;;;AAaA;;;;AAGA;;;;;;;;;;;;;;;AAoBA;;;;;;AAYA;;;;;;;;;;;;;;;AAoBA;;;;;;AAYA;;;;;;;;;;;;;;;AAoBA;;;;;AAQA;;;;;;;;;;;;;AAYA;;;;;;;;AAQA;;;;;;;;;;;AAeA;;;;AAKA;;;;;;AAKA;;;;;;;;;AAQA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;;;;;;;AAYA;;;;;;;;;;;;;AAYA;;;;;;;;;;;;;AAYA;;;;;;;;;;;;;AAYA;;;;;;;;;;;;AAYA;;;;;;;;;;AASA;;;;;;;AAMA;;;;;;;;;;AASA;;;;;;;AAMA;;;;;;AAKA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;;;;;;;;AAaA;;;;;;;;;;;AAUA;;;;AAGA;;;;;AAIA;;;;;;;;;;AASA;;;;;;;;;;;;;;;AAcA;;;;;;;;;;;AAUA;;;;AAGA;;;;;AAIA;;;;;;;;;;AASA;;;;;;;;;;AAQA;;;;;;;;;;;AAUA;;;;;AAIA;;;;;;;;;;;;;AAaA;;;;;;;AAgBA;;;;;;;;;AA0PA;;;;;;AA1OA;EACE;;;;EAGA;;;;;AAIF;EACE;;;;EAMA;;;;;AAQF;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;AASA;;;;;;AAKA"}}]}