/* [next]/internal/font/google/geist_e531dabc.module.css [app-client] (css) */
@font-face {
  font-family: Geist;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/gyByhwUxId8gMEwYGFWNOITddY4-s.b7d310ad.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Geist;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/gyByhwUxId8gMEwSGFWNOITddY4-s.81df3a5b.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Geist;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/gyByhwUxId8gMEwcGFWNOITd-s.p.da1ebef7.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Geist Fallback;
  src: local(Arial);
  ascent-override: 95.94%;
  descent-override: 28.16%;
  line-gap-override: 0.0%;
  size-adjust: 104.76%;
}

.geist_e531dabc-module__QGiZLq__className {
  font-family: Geist, Geist Fallback;
  font-style: normal;
}

.geist_e531dabc-module__QGiZLq__variable {
  --font-geist-sans: "Geist", "Geist Fallback";
}


/* [next]/internal/font/google/geist_mono_68a01160.module.css [app-client] (css) */
@font-face {
  font-family: Geist Mono;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/or3nQ6H_1_WfwkMZI_qYFrMdmhHkjkotbA-s.cb6bbcb1.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Geist Mono;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/or3nQ6H_1_WfwkMZI_qYFrkdmhHkjkotbA-s.e32db976.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Geist Mono;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/or3nQ6H_1_WfwkMZI_qYFrcdmhHkjko-s.p.be19f591.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Geist Mono Fallback;
  src: local(Arial);
  ascent-override: 74.67%;
  descent-override: 21.92%;
  line-gap-override: 0.0%;
  size-adjust: 134.59%;
}

.geist_mono_68a01160-module__YLcDdW__className {
  font-family: Geist Mono, Geist Mono Fallback;
  font-style: normal;
}

.geist_mono_68a01160-module__YLcDdW__variable {
  --font-geist-mono: "Geist Mono", "Geist Mono Fallback";
}


/* [project]/src/app/globals.css [app-client] (css) */



/* [project]/src/styles/custom.css [app-client] (css) */
.btn-travel {
  transition: all .3s;
  position: relative;
  overflow: hidden;
}

.btn-travel:before {
  content: "";
  background: linear-gradient(90deg, #0000, #fff3, #0000);
  width: 100%;
  height: 100%;
  transition: left .5s;
  position: absolute;
  top: 0;
  left: -100%;
}

.btn-travel:hover:before {
  left: 100%;
}

.card-hover {
  transition: all .3s cubic-bezier(.4, 0, .2, 1);
}

.card-hover:hover {
  transform: translateY(-8px)scale(1.02);
  box-shadow: 0 25px 50px #00000026;
}

.bg-travel-gradient {
  background: linear-gradient(135deg, #89cfeb 0%, #ffddb3 50%, #b5e1f2 100%);
}

.bg-travel-dark-gradient {
  background: linear-gradient(135deg, #1a1a1a 0%, #333 50%, #262626 100%);
}

.text-shimmer {
  background: linear-gradient(90deg, #89cfeb 0%, #ffddb3 50%, #89cfeb 100%) 0 0 / 200% 100%;
  -webkit-text-fill-color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
  animation: 3s ease-in-out infinite shimmer;
}

@keyframes shimmer {
  0%, 100% {
    background-position: 200% 0;
  }

  50% {
    background-position: -200% 0;
  }
}

.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%) 0 0 / 200% 100%;
  animation: 1.5s infinite loading;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }

  100% {
    background-position: -200% 0;
  }
}

::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f4f4f5;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #89cfeb, #ffddb3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5ebee4, #ffc680);
}

.form-group {
  position: relative;
}

.form-floating-label {
  color: #737373;
  pointer-events: none;
  transition: all .3s;
  position: absolute;
  top: 12px;
  left: 12px;
}

.form-input:focus + .form-floating-label, .form-input:not(:placeholder-shown) + .form-floating-label {
  color: #89cfeb;
  background: #fff;
  padding: 0 4px;
  font-size: 12px;
  top: -8px;
  left: 8px;
}

.service-icon {
  background: linear-gradient(135deg, #89cfeb, #ffddb3);
  transition: all .3s;
}

.service-icon:hover {
  background: linear-gradient(135deg, #5ebee4, #ffc680);
  transform: rotate(5deg)scale(1.1);
}

.nav-link {
  position: relative;
  overflow: hidden;
}

.nav-link:after {
  content: "";
  background: linear-gradient(90deg, #89cfeb, #ffddb3);
  width: 0;
  height: 2px;
  transition: width .3s;
  position: absolute;
  bottom: 0;
  left: 0;
}

.nav-link:hover:after {
  width: 100%;
}

.modal-backdrop {
  backdrop-filter: blur(4px);
  background: #00000080;
}

.modal-content {
  backdrop-filter: blur(10px);
  background: #fffffff2;
  border: 1px solid #fff3;
}

.toast-success {
  color: #fff;
  background: linear-gradient(135deg, #16a249, #1cce5e);
}

.toast-error {
  color: #fff;
  background: linear-gradient(135deg, #ef4343, #f37272);
}

.toast-info {
  color: #fff;
  background: linear-gradient(135deg, #89cfeb, #b5e1f2);
}

.search-result-item {
  border-left: 4px solid #0000;
  transition: all .3s;
}

.search-result-item:hover {
  background: linear-gradient(90deg, rgba(197, 71%, 73%, .05), transparent);
  border-left-color: #89cfeb;
  transform: translateX(4px);
}

.price-tag {
  color: #fff;
  background: linear-gradient(135deg, #16a249, #1cce5e);
  border-radius: 20px;
  padding: 4px 12px;
  font-size: 14px;
  font-weight: 600;
}

.price-tag-large {
  color: #1a1a1a;
  background: linear-gradient(135deg, #89cfeb, #ffddb3);
  border-radius: 25px;
  padding: 8px 16px;
  font-size: 18px;
  font-weight: 700;
}

.status-confirmed {
  color: #fff;
  background: linear-gradient(135deg, #16a249, #1cce5e);
}

.status-pending {
  color: #1a1a1a;
  background: linear-gradient(135deg, #ffc680, #ffddb3);
}

.status-cancelled {
  color: #fff;
  background: linear-gradient(135deg, #ef4343, #f37272);
}

@media (width <= 768px) {
  .hero-section {
    padding: 3rem 1rem;
  }

  .service-card {
    margin-bottom: 1.5rem;
  }

  .btn-travel {
    padding: 12px 24px;
    font-size: 16px;
  }

  .text-shimmer {
    font-size: 2rem;
  }
}

@media (prefers-color-scheme: dark) {
  .glass-effect {
    background: #0000004d;
    border: 1px solid #ffffff1a;
  }

  .modal-content {
    background: #000c;
    border: 1px solid #ffffff1a;
  }
}

@media (prefers-reduced-motion: reduce) {
  .hover-lift, .btn-travel, .service-icon, .card-hover, .search-result-item {
    transition: none;
  }

  .text-shimmer, .skeleton, .animate-float, .animate-pulse-slow {
    animation: none;
  }
}

.btn-travel:focus, .form-input:focus {
  outline-offset: 2px;
  outline: 2px solid #89cfeb;
}

@media print {
  .hero-section, .bg-gradient-primary, .bg-gradient-secondary {
    color: #000 !important;
    background: #fff !important;
  }

  .shadow-travel, .shadow-travel-lg, .shadow-sky-blue, .shadow-orange {
    box-shadow: none !important;
  }
}


/*# sourceMappingURL=%5Broot-of-the-server%5D__1206895b._.css.map*/