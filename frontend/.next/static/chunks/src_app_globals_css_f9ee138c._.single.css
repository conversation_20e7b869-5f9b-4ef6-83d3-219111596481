/* [project]/src/app/globals.css [app-client] (css) */
@layer properties {
  @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
    *, :before, :after, ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: #0000;
      --tw-gradient-via: #0000;
      --tw-gradient-to: #0000;
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-outline-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-duration: initial;
    }
  }
}

@layer theme {
  :root, :host {
    --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    --color-red-100: oklch(93.6% .032 17.717);
    --color-red-600: oklch(57.7% .245 27.325);
    --color-red-700: oklch(50.5% .213 27.518);
    --color-orange-100: oklch(95.4% .038 75.164);
    --color-orange-500: oklch(70.5% .213 47.604);
    --color-orange-600: oklch(64.6% .222 41.116);
    --color-orange-700: oklch(55.3% .195 38.402);
    --color-orange-800: oklch(47% .157 37.304);
    --color-yellow-100: oklch(97.3% .071 103.193);
    --color-yellow-500: oklch(79.5% .184 86.047);
    --color-yellow-800: oklch(47.6% .114 61.907);
    --color-green-100: oklch(96.2% .044 156.743);
    --color-green-500: oklch(72.3% .219 149.579);
    --color-green-600: oklch(62.7% .194 149.214);
    --color-green-700: oklch(52.7% .154 150.069);
    --color-green-800: oklch(44.8% .119 151.328);
    --color-blue-100: oklch(93.2% .032 255.585);
    --color-blue-500: oklch(62.3% .214 259.815);
    --color-blue-600: oklch(54.6% .245 262.881);
    --color-blue-700: oklch(48.8% .243 264.376);
    --color-blue-800: oklch(42.4% .199 265.638);
    --color-purple-600: oklch(55.8% .288 302.321);
    --color-gray-50: oklch(98.5% .002 247.839);
    --color-gray-100: oklch(96.7% .003 264.542);
    --color-gray-300: oklch(87.2% .01 258.338);
    --color-gray-400: oklch(70.7% .022 261.325);
    --color-gray-500: oklch(55.1% .027 264.364);
    --color-gray-600: oklch(44.6% .03 256.802);
    --color-gray-700: oklch(37.3% .034 259.733);
    --color-gray-800: oklch(27.8% .033 256.848);
    --color-gray-900: oklch(21% .034 264.665);
    --color-black: #000;
    --color-white: #fff;
    --spacing: .25rem;
    --container-md: 28rem;
    --container-lg: 32rem;
    --container-2xl: 42rem;
    --container-3xl: 48rem;
    --container-4xl: 56rem;
    --container-6xl: 72rem;
    --container-7xl: 80rem;
    --text-xs: .75rem;
    --text-xs--line-height: calc(1 / .75);
    --text-sm: .875rem;
    --text-sm--line-height: calc(1.25 / .875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --text-6xl: 3.75rem;
    --text-6xl--line-height: 1;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --tracking-widest: .1em;
    --radius-xs: .125rem;
    --radius-sm: .25rem;
    --radius-md: .375rem;
    --radius-lg: .5rem;
    --radius-xl: .75rem;
    --animate-spin: spin 1s linear infinite;
    --animate-pulse: pulse 2s cubic-bezier(.4, 0, .6, 1) infinite;
    --animate-bounce: bounce 1s infinite;
    --default-transition-duration: .15s;
    --default-transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
  }
}

@layer base {
  *, :after, :before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  html, :host {
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    line-height: 1.5;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }

  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }

  abbr:where([title]) {
    text-decoration: underline dotted;
  }

  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }

  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }

  b, strong {
    font-weight: bolder;
  }

  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }

  small {
    font-size: 80%;
  }

  sub, sup {
    vertical-align: baseline;
    font-size: 75%;
    line-height: 0;
    position: relative;
  }

  sub {
    bottom: -.25em;
  }

  sup {
    top: -.5em;
  }

  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }

  :-moz-focusring {
    outline: auto;
  }

  progress {
    vertical-align: baseline;
  }

  summary {
    display: list-item;
  }

  ol, ul, menu {
    list-style: none;
  }

  img, svg, video, canvas, audio, iframe, embed, object {
    vertical-align: middle;
    display: block;
  }

  img, video {
    max-width: 100%;
    height: auto;
  }

  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: #0000;
    border-radius: 0;
  }

  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }

  ::file-selector-button {
    margin-inline-end: 4px;
  }

  ::placeholder {
    opacity: 1;
  }

  @supports (not ((-webkit-appearance: -apple-pay-button))) or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentColor;
    }

    @supports (color: color-mix(in lab, red, red)) {
      ::placeholder {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }

  textarea {
    resize: vertical;
  }

  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }

  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }

  ::-webkit-datetime-edit {
    display: inline-flex;
  }

  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }

  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }

  :-moz-ui-invalid {
    box-shadow: none;
  }

  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }

  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }

  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}

@layer components;

@layer utilities {
  .\@container\/card-header {
    container: card-header / inline-size;
  }

  .pointer-events-none {
    pointer-events: none;
  }

  .sr-only {
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    position: absolute;
    overflow: hidden;
  }

  .absolute {
    position: absolute;
  }

  .fixed {
    position: fixed;
  }

  .relative {
    position: relative;
  }

  .sticky {
    position: sticky;
  }

  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }

  .top-0 {
    top: calc(var(--spacing) * 0);
  }

  .top-3 {
    top: calc(var(--spacing) * 3);
  }

  .top-4 {
    top: calc(var(--spacing) * 4);
  }

  .top-10 {
    top: calc(var(--spacing) * 10);
  }

  .top-20 {
    top: calc(var(--spacing) * 20);
  }

  .top-40 {
    top: calc(var(--spacing) * 40);
  }

  .top-\[50\%\] {
    top: 50%;
  }

  .right-2 {
    right: calc(var(--spacing) * 2);
  }

  .right-3 {
    right: calc(var(--spacing) * 3);
  }

  .right-4 {
    right: calc(var(--spacing) * 4);
  }

  .right-6 {
    right: calc(var(--spacing) * 6);
  }

  .right-10 {
    right: calc(var(--spacing) * 10);
  }

  .right-20 {
    right: calc(var(--spacing) * 20);
  }

  .bottom-6 {
    bottom: calc(var(--spacing) * 6);
  }

  .bottom-10 {
    bottom: calc(var(--spacing) * 10);
  }

  .bottom-20 {
    bottom: calc(var(--spacing) * 20);
  }

  .left-1\/4 {
    left: 25%;
  }

  .left-2 {
    left: calc(var(--spacing) * 2);
  }

  .left-3 {
    left: calc(var(--spacing) * 3);
  }

  .left-10 {
    left: calc(var(--spacing) * 10);
  }

  .left-\[50\%\] {
    left: 50%;
  }

  .z-10 {
    z-index: 10;
  }

  .z-50 {
    z-index: 50;
  }

  .col-span-1 {
    grid-column: span 1 / span 1;
  }

  .col-start-2 {
    grid-column-start: 2;
  }

  .row-span-2 {
    grid-row: span 2 / span 2;
  }

  .row-start-1 {
    grid-row-start: 1;
  }

  .-mx-1 {
    margin-inline: calc(var(--spacing) * -1);
  }

  .mx-auto {
    margin-inline: auto;
  }

  .my-1 {
    margin-block: calc(var(--spacing) * 1);
  }

  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }

  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }

  .mt-3 {
    margin-top: calc(var(--spacing) * 3);
  }

  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }

  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }

  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }

  .mt-12 {
    margin-top: calc(var(--spacing) * 12);
  }

  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }

  .mr-3 {
    margin-right: calc(var(--spacing) * 3);
  }

  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }

  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }

  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }

  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }

  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }

  .mb-16 {
    margin-bottom: calc(var(--spacing) * 16);
  }

  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }

  .ml-6 {
    margin-left: calc(var(--spacing) * 6);
  }

  .ml-8 {
    margin-left: calc(var(--spacing) * 8);
  }

  .ml-auto {
    margin-left: auto;
  }

  .block {
    display: block;
  }

  .flex {
    display: flex;
  }

  .grid {
    display: grid;
  }

  .hidden {
    display: none;
  }

  .inline-block {
    display: inline-block;
  }

  .inline-flex {
    display: inline-flex;
  }

  .size-2 {
    width: calc(var(--spacing) * 2);
    height: calc(var(--spacing) * 2);
  }

  .size-3\.5 {
    width: calc(var(--spacing) * 3.5);
    height: calc(var(--spacing) * 3.5);
  }

  .size-4 {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }

  .size-9 {
    width: calc(var(--spacing) * 9);
    height: calc(var(--spacing) * 9);
  }

  .h-1 {
    height: calc(var(--spacing) * 1);
  }

  .h-2 {
    height: calc(var(--spacing) * 2);
  }

  .h-3 {
    height: calc(var(--spacing) * 3);
  }

  .h-4 {
    height: calc(var(--spacing) * 4);
  }

  .h-5 {
    height: calc(var(--spacing) * 5);
  }

  .h-6 {
    height: calc(var(--spacing) * 6);
  }

  .h-7 {
    height: calc(var(--spacing) * 7);
  }

  .h-8 {
    height: calc(var(--spacing) * 8);
  }

  .h-9 {
    height: calc(var(--spacing) * 9);
  }

  .h-10 {
    height: calc(var(--spacing) * 10);
  }

  .h-12 {
    height: calc(var(--spacing) * 12);
  }

  .h-14 {
    height: calc(var(--spacing) * 14);
  }

  .h-16 {
    height: calc(var(--spacing) * 16);
  }

  .h-20 {
    height: calc(var(--spacing) * 20);
  }

  .h-24 {
    height: calc(var(--spacing) * 24);
  }

  .h-32 {
    height: calc(var(--spacing) * 32);
  }

  .h-80 {
    height: calc(var(--spacing) * 80);
  }

  .h-96 {
    height: calc(var(--spacing) * 96);
  }

  .h-\[calc\(100\%-1px\)\] {
    height: calc(100% - 1px);
  }

  .h-\[var\(--radix-select-trigger-height\)\] {
    height: var(--radix-select-trigger-height);
  }

  .h-px {
    height: 1px;
  }

  .max-h-\(--radix-dropdown-menu-content-available-height\) {
    max-height: var(--radix-dropdown-menu-content-available-height);
  }

  .max-h-\(--radix-select-content-available-height\) {
    max-height: var(--radix-select-content-available-height);
  }

  .max-h-64 {
    max-height: calc(var(--spacing) * 64);
  }

  .min-h-screen {
    min-height: 100vh;
  }

  .w-2 {
    width: calc(var(--spacing) * 2);
  }

  .w-3 {
    width: calc(var(--spacing) * 3);
  }

  .w-4 {
    width: calc(var(--spacing) * 4);
  }

  .w-5 {
    width: calc(var(--spacing) * 5);
  }

  .w-6 {
    width: calc(var(--spacing) * 6);
  }

  .w-8 {
    width: calc(var(--spacing) * 8);
  }

  .w-10 {
    width: calc(var(--spacing) * 10);
  }

  .w-12 {
    width: calc(var(--spacing) * 12);
  }

  .w-14 {
    width: calc(var(--spacing) * 14);
  }

  .w-16 {
    width: calc(var(--spacing) * 16);
  }

  .w-20 {
    width: calc(var(--spacing) * 20);
  }

  .w-24 {
    width: calc(var(--spacing) * 24);
  }

  .w-32 {
    width: calc(var(--spacing) * 32);
  }

  .w-40 {
    width: calc(var(--spacing) * 40);
  }

  .w-56 {
    width: calc(var(--spacing) * 56);
  }

  .w-80 {
    width: calc(var(--spacing) * 80);
  }

  .w-fit {
    width: fit-content;
  }

  .w-full {
    width: 100%;
  }

  .max-w-2xl {
    max-width: var(--container-2xl);
  }

  .max-w-3xl {
    max-width: var(--container-3xl);
  }

  .max-w-4xl {
    max-width: var(--container-4xl);
  }

  .max-w-6xl {
    max-width: var(--container-6xl);
  }

  .max-w-7xl {
    max-width: var(--container-7xl);
  }

  .max-w-\[80\%\] {
    max-width: 80%;
  }

  .max-w-\[calc\(100\%-2rem\)\] {
    max-width: calc(100% - 2rem);
  }

  .max-w-md {
    max-width: var(--container-md);
  }

  .min-w-0 {
    min-width: calc(var(--spacing) * 0);
  }

  .min-w-\[8rem\] {
    min-width: 8rem;
  }

  .min-w-\[var\(--radix-select-trigger-width\)\] {
    min-width: var(--radix-select-trigger-width);
  }

  .flex-1 {
    flex: 1;
  }

  .shrink-0 {
    flex-shrink: 0;
  }

  .origin-\(--radix-dropdown-menu-content-transform-origin\) {
    transform-origin: var(--radix-dropdown-menu-content-transform-origin);
  }

  .origin-\(--radix-select-content-transform-origin\) {
    transform-origin: var(--radix-select-content-transform-origin);
  }

  .translate-x-\[-50\%\] {
    --tw-translate-x: -50%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-\[-50\%\] {
    --tw-translate-y: -50%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .animate-bounce {
    animation: var(--animate-bounce);
  }

  .animate-pulse {
    animation: var(--animate-pulse);
  }

  .animate-spin {
    animation: var(--animate-spin);
  }

  .cursor-default {
    cursor: default;
  }

  .cursor-pointer {
    cursor: pointer;
  }

  .scroll-my-1 {
    scroll-margin-block: calc(var(--spacing) * 1);
  }

  .auto-rows-min {
    grid-auto-rows: min-content;
  }

  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .grid-rows-\[auto_auto\] {
    grid-template-rows: auto auto;
  }

  .flex-col {
    flex-direction: column;
  }

  .flex-col-reverse {
    flex-direction: column-reverse;
  }

  .flex-row {
    flex-direction: row;
  }

  .flex-row-reverse {
    flex-direction: row-reverse;
  }

  .flex-wrap {
    flex-wrap: wrap;
  }

  .items-center {
    align-items: center;
  }

  .items-end {
    align-items: flex-end;
  }

  .items-start {
    align-items: flex-start;
  }

  .justify-between {
    justify-content: space-between;
  }

  .justify-center {
    justify-content: center;
  }

  .justify-end {
    justify-content: flex-end;
  }

  .justify-start {
    justify-content: flex-start;
  }

  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }

  .gap-1\.5 {
    gap: calc(var(--spacing) * 1.5);
  }

  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }

  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }

  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }

  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }

  .gap-8 {
    gap: calc(var(--spacing) * 8);
  }

  :where(.space-y-1 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-2 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-3 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-4 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-6 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-8 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-x-1 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-8 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));
  }

  .self-start {
    align-self: flex-start;
  }

  .justify-self-end {
    justify-self: flex-end;
  }

  .overflow-hidden {
    overflow: hidden;
  }

  .overflow-x-hidden {
    overflow-x: hidden;
  }

  .overflow-y-auto {
    overflow-y: auto;
  }

  .rounded {
    border-radius: .25rem;
  }

  .rounded-full {
    border-radius: 3.40282e38px;
  }

  .rounded-lg {
    border-radius: var(--radius-lg);
  }

  .rounded-md {
    border-radius: var(--radius-md);
  }

  .rounded-sm {
    border-radius: var(--radius-sm);
  }

  .rounded-xl {
    border-radius: var(--radius-xl);
  }

  .rounded-xs {
    border-radius: var(--radius-xs);
  }

  .rounded-t-lg {
    border-top-left-radius: var(--radius-lg);
    border-top-right-radius: var(--radius-lg);
  }

  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .border-0 {
    border-style: var(--tw-border-style);
    border-width: 0;
  }

  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }

  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }

  .border-b-2 {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 2px;
  }

  .border-blue-600 {
    border-color: var(--color-blue-600);
  }

  .border-gray-300 {
    border-color: var(--color-gray-300);
  }

  .border-gray-800 {
    border-color: var(--color-gray-800);
  }

  .border-transparent {
    border-color: #0000;
  }

  .bg-black\/50 {
    background-color: #00000080;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-black\/50 {
      background-color: color-mix(in oklab, var(--color-black) 50%, transparent);
    }
  }

  .bg-blue-100 {
    background-color: var(--color-blue-100);
  }

  .bg-blue-500 {
    background-color: var(--color-blue-500);
  }

  .bg-blue-600 {
    background-color: var(--color-blue-600);
  }

  .bg-gray-50 {
    background-color: var(--color-gray-50);
  }

  .bg-gray-100 {
    background-color: var(--color-gray-100);
  }

  .bg-gray-400 {
    background-color: var(--color-gray-400);
  }

  .bg-green-100 {
    background-color: var(--color-green-100);
  }

  .bg-green-500 {
    background-color: var(--color-green-500);
  }

  .bg-green-600 {
    background-color: var(--color-green-600);
  }

  .bg-orange-100 {
    background-color: var(--color-orange-100);
  }

  .bg-orange-500 {
    background-color: var(--color-orange-500);
  }

  .bg-orange-600 {
    background-color: var(--color-orange-600);
  }

  .bg-red-100 {
    background-color: var(--color-red-100);
  }

  .bg-transparent {
    background-color: #0000;
  }

  .bg-white {
    background-color: var(--color-white);
  }

  .bg-white\/10 {
    background-color: #ffffff1a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/10 {
      background-color: color-mix(in oklab, var(--color-white) 10%, transparent);
    }
  }

  .bg-white\/15 {
    background-color: #ffffff26;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/15 {
      background-color: color-mix(in oklab, var(--color-white) 15%, transparent);
    }
  }

  .bg-white\/20 {
    background-color: #fff3;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/20 {
      background-color: color-mix(in oklab, var(--color-white) 20%, transparent);
    }
  }

  .bg-yellow-100 {
    background-color: var(--color-yellow-100);
  }

  .bg-gradient-to-b {
    --tw-gradient-position: to bottom in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-br {
    --tw-gradient-position: to bottom right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-r {
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .from-gray-50 {
    --tw-gradient-from: var(--color-gray-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-green-600 {
    --tw-gradient-from: var(--color-green-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-orange-600 {
    --tw-gradient-from: var(--color-orange-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-white {
    --tw-gradient-from: var(--color-white);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .via-transparent {
    --tw-gradient-via: transparent;
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .via-white {
    --tw-gradient-via: var(--color-white);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .to-gray-50 {
    --tw-gradient-to: var(--color-gray-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-green-800 {
    --tw-gradient-to: var(--color-green-800);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-orange-800 {
    --tw-gradient-to: var(--color-orange-800);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-white {
    --tw-gradient-to: var(--color-white);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .fill-current {
    fill: currentColor;
  }

  .p-0 {
    padding: calc(var(--spacing) * 0);
  }

  .p-1 {
    padding: calc(var(--spacing) * 1);
  }

  .p-2 {
    padding: calc(var(--spacing) * 2);
  }

  .p-3 {
    padding: calc(var(--spacing) * 3);
  }

  .p-4 {
    padding: calc(var(--spacing) * 4);
  }

  .p-6 {
    padding: calc(var(--spacing) * 6);
  }

  .p-\[3px\] {
    padding: 3px;
  }

  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }

  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }

  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }

  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }

  .px-8 {
    padding-inline: calc(var(--spacing) * 8);
  }

  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }

  .py-1\.5 {
    padding-block: calc(var(--spacing) * 1.5);
  }

  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }

  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }

  .py-6 {
    padding-block: calc(var(--spacing) * 6);
  }

  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }

  .py-12 {
    padding-block: calc(var(--spacing) * 12);
  }

  .py-16 {
    padding-block: calc(var(--spacing) * 16);
  }

  .py-20 {
    padding-block: calc(var(--spacing) * 20);
  }

  .pt-6 {
    padding-top: calc(var(--spacing) * 6);
  }

  .pt-8 {
    padding-top: calc(var(--spacing) * 8);
  }

  .pr-2 {
    padding-right: calc(var(--spacing) * 2);
  }

  .pr-8 {
    padding-right: calc(var(--spacing) * 8);
  }

  .pr-10 {
    padding-right: calc(var(--spacing) * 10);
  }

  .pl-2 {
    padding-left: calc(var(--spacing) * 2);
  }

  .pl-8 {
    padding-left: calc(var(--spacing) * 8);
  }

  .pl-10 {
    padding-left: calc(var(--spacing) * 10);
  }

  .text-center {
    text-align: center;
  }

  .text-right {
    text-align: right;
  }

  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }

  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }

  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }

  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }

  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }

  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }

  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }

  .leading-none {
    --tw-leading: 1;
    line-height: 1;
  }

  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }

  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }

  .tracking-widest {
    --tw-tracking: var(--tracking-widest);
    letter-spacing: var(--tracking-widest);
  }

  .whitespace-nowrap {
    white-space: nowrap;
  }

  .text-blue-100 {
    color: var(--color-blue-100);
  }

  .text-blue-600 {
    color: var(--color-blue-600);
  }

  .text-blue-800 {
    color: var(--color-blue-800);
  }

  .text-gray-300 {
    color: var(--color-gray-300);
  }

  .text-gray-400 {
    color: var(--color-gray-400);
  }

  .text-gray-500 {
    color: var(--color-gray-500);
  }

  .text-gray-600 {
    color: var(--color-gray-600);
  }

  .text-gray-700 {
    color: var(--color-gray-700);
  }

  .text-gray-800 {
    color: var(--color-gray-800);
  }

  .text-gray-900 {
    color: var(--color-gray-900);
  }

  .text-green-100 {
    color: var(--color-green-100);
  }

  .text-green-600 {
    color: var(--color-green-600);
  }

  .text-green-700 {
    color: var(--color-green-700);
  }

  .text-green-800 {
    color: var(--color-green-800);
  }

  .text-orange-100 {
    color: var(--color-orange-100);
  }

  .text-orange-600 {
    color: var(--color-orange-600);
  }

  .text-purple-600 {
    color: var(--color-purple-600);
  }

  .text-red-600 {
    color: var(--color-red-600);
  }

  .text-red-700 {
    color: var(--color-red-700);
  }

  .text-white {
    color: var(--color-white);
  }

  .text-yellow-500 {
    color: var(--color-yellow-500);
  }

  .text-yellow-800 {
    color: var(--color-yellow-800);
  }

  .capitalize {
    text-transform: capitalize;
  }

  .underline-offset-4 {
    text-underline-offset: 4px;
  }

  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .opacity-50 {
    opacity: .5;
  }

  .opacity-70 {
    opacity: .7;
  }

  .opacity-90 {
    opacity: .9;
  }

  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-md {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, #0000001a), 0 2px 4px -2px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-xl {
    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, #0000001a), 0 8px 10px -6px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-xs {
    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, #0000000d);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .outline-hidden {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .outline-hidden {
      outline-offset: 2px;
      outline: 2px solid #0000;
    }
  }

  .outline {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }

  .blur {
    --tw-blur: blur(8px);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .transition-\[color\,box-shadow\] {
    transition-property: color, box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-opacity {
    transition-property: opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-shadow {
    transition-property: box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .duration-200 {
    --tw-duration: .2s;
    transition-duration: .2s;
  }

  .duration-300 {
    --tw-duration: .3s;
    transition-duration: .3s;
  }

  .outline-none {
    --tw-outline-style: none;
    outline-style: none;
  }

  .select-none {
    -webkit-user-select: none;
    user-select: none;
  }

  .group-data-\[disabled\=true\]\:pointer-events-none:is(:where(.group)[data-disabled="true"] *) {
    pointer-events: none;
  }

  .group-data-\[disabled\=true\]\:opacity-50:is(:where(.group)[data-disabled="true"] *) {
    opacity: .5;
  }

  .peer-disabled\:cursor-not-allowed:is(:where(.peer):disabled ~ *) {
    cursor: not-allowed;
  }

  .peer-disabled\:opacity-50:is(:where(.peer):disabled ~ *) {
    opacity: .5;
  }

  .file\:inline-flex::file-selector-button {
    display: inline-flex;
  }

  .file\:h-7::file-selector-button {
    height: calc(var(--spacing) * 7);
  }

  .file\:border-0::file-selector-button {
    border-style: var(--tw-border-style);
    border-width: 0;
  }

  .file\:bg-transparent::file-selector-button {
    background-color: #0000;
  }

  .file\:text-sm::file-selector-button {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .file\:font-medium::file-selector-button {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  @media (hover: hover) {
    .hover\:bg-blue-700:hover {
      background-color: var(--color-blue-700);
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-50:hover {
      background-color: var(--color-gray-50);
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-100:hover {
      background-color: var(--color-gray-100);
    }
  }

  @media (hover: hover) {
    .hover\:bg-green-700:hover {
      background-color: var(--color-green-700);
    }
  }

  @media (hover: hover) {
    .hover\:bg-orange-700:hover {
      background-color: var(--color-orange-700);
    }
  }

  @media (hover: hover) {
    .hover\:text-blue-500:hover {
      color: var(--color-blue-500);
    }
  }

  @media (hover: hover) {
    .hover\:text-blue-600:hover {
      color: var(--color-blue-600);
    }
  }

  @media (hover: hover) {
    .hover\:text-gray-600:hover {
      color: var(--color-gray-600);
    }
  }

  @media (hover: hover) {
    .hover\:text-white:hover {
      color: var(--color-white);
    }
  }

  @media (hover: hover) {
    .hover\:underline:hover {
      text-decoration-line: underline;
    }
  }

  @media (hover: hover) {
    .hover\:opacity-100:hover {
      opacity: 1;
    }
  }

  @media (hover: hover) {
    .hover\:shadow-lg:hover {
      --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-md:hover {
      --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, #0000001a), 0 2px 4px -2px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  .focus\:ring-2:focus {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus\:ring-blue-500:focus {
    --tw-ring-color: var(--color-blue-500);
  }

  .focus\:ring-offset-2:focus {
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }

  .focus\:outline-hidden:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .focus\:outline-hidden:focus {
      outline-offset: 2px;
      outline: 2px solid #0000;
    }
  }

  .focus\:outline-none:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  .focus-visible\:ring-\[3px\]:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-visible\:outline-1:focus-visible {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }

  .disabled\:pointer-events-none:disabled {
    pointer-events: none;
  }

  .disabled\:cursor-not-allowed:disabled {
    cursor: not-allowed;
  }

  .disabled\:bg-gray-100:disabled {
    background-color: var(--color-gray-100);
  }

  .disabled\:opacity-50:disabled {
    opacity: .5;
  }

  .has-data-\[slot\=card-action\]\:grid-cols-\[1fr_auto\]:has([data-slot="card-action"]) {
    grid-template-columns: 1fr auto;
  }

  .has-\[\>svg\]\:px-2\.5:has( > svg) {
    padding-inline: calc(var(--spacing) * 2.5);
  }

  .has-\[\>svg\]\:px-3:has( > svg) {
    padding-inline: calc(var(--spacing) * 3);
  }

  .has-\[\>svg\]\:px-4:has( > svg) {
    padding-inline: calc(var(--spacing) * 4);
  }

  .data-\[disabled\]\:pointer-events-none[data-disabled] {
    pointer-events: none;
  }

  .data-\[disabled\]\:opacity-50[data-disabled] {
    opacity: .5;
  }

  .data-\[inset\]\:pl-8[data-inset] {
    padding-left: calc(var(--spacing) * 8);
  }

  .data-\[side\=bottom\]\:translate-y-1[data-side="bottom"] {
    --tw-translate-y: calc(var(--spacing) * 1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[side\=left\]\:-translate-x-1[data-side="left"] {
    --tw-translate-x: calc(var(--spacing) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[side\=right\]\:translate-x-1[data-side="right"] {
    --tw-translate-x: calc(var(--spacing) * 1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[side\=top\]\:-translate-y-1[data-side="top"] {
    --tw-translate-y: calc(var(--spacing) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[size\=default\]\:h-9[data-size="default"] {
    height: calc(var(--spacing) * 9);
  }

  .data-\[size\=sm\]\:h-8[data-size="sm"] {
    height: calc(var(--spacing) * 8);
  }

  :is(.\*\:data-\[slot\=select-value\]\:line-clamp-1 > *)[data-slot="select-value"] {
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
  }

  :is(.\*\:data-\[slot\=select-value\]\:flex > *)[data-slot="select-value"] {
    display: flex;
  }

  :is(.\*\:data-\[slot\=select-value\]\:items-center > *)[data-slot="select-value"] {
    align-items: center;
  }

  :is(.\*\:data-\[slot\=select-value\]\:gap-2 > *)[data-slot="select-value"] {
    gap: calc(var(--spacing) * 2);
  }

  .data-\[state\=active\]\:shadow-sm[data-state="active"] {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  @media (width >= 40rem) {
    .sm\:inline {
      display: inline;
    }
  }

  @media (width >= 40rem) {
    .sm\:max-w-lg {
      max-width: var(--container-lg);
    }
  }

  @media (width >= 40rem) {
    .sm\:flex-row {
      flex-direction: row;
    }
  }

  @media (width >= 40rem) {
    .sm\:justify-end {
      justify-content: flex-end;
    }
  }

  @media (width >= 40rem) {
    .sm\:px-6 {
      padding-inline: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 40rem) {
    .sm\:text-left {
      text-align: left;
    }
  }

  @media (width >= 48rem) {
    .md\:col-span-1 {
      grid-column: span 1 / span 1;
    }
  }

  @media (width >= 48rem) {
    .md\:col-span-2 {
      grid-column: span 2 / span 2;
    }
  }

  @media (width >= 48rem) {
    .md\:mt-0 {
      margin-top: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 48rem) {
    .md\:flex {
      display: flex;
    }
  }

  @media (width >= 48rem) {
    .md\:hidden {
      display: none;
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:flex-row {
      flex-direction: row;
    }
  }

  @media (width >= 48rem) {
    .md\:text-2xl {
      font-size: var(--text-2xl);
      line-height: var(--tw-leading, var(--text-2xl--line-height));
    }
  }

  @media (width >= 48rem) {
    .md\:text-4xl {
      font-size: var(--text-4xl);
      line-height: var(--tw-leading, var(--text-4xl--line-height));
    }
  }

  @media (width >= 48rem) {
    .md\:text-6xl {
      font-size: var(--text-6xl);
      line-height: var(--tw-leading, var(--text-6xl--line-height));
    }
  }

  @media (width >= 48rem) {
    .md\:text-sm {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:px-8 {
      padding-inline: calc(var(--spacing) * 8);
    }
  }

  .\[\&_svg\]\:pointer-events-none svg {
    pointer-events: none;
  }

  .\[\&_svg\]\:shrink-0 svg {
    flex-shrink: 0;
  }

  .\[\&_svg\:not\(\[class\*\=\'size-\'\]\)\]\:size-4 svg:not([class*="size-"]) {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }

  .\[\.border-b\]\:pb-6.border-b {
    padding-bottom: calc(var(--spacing) * 6);
  }

  .\[\.border-t\]\:pt-6.border-t {
    padding-top: calc(var(--spacing) * 6);
  }

  :is(.\*\:\[span\]\:last\:flex > *):is(span):last-child {
    display: flex;
  }

  :is(.\*\:\[span\]\:last\:items-center > *):is(span):last-child {
    align-items: center;
  }

  :is(.\*\:\[span\]\:last\:gap-2 > *):is(span):last-child {
    gap: calc(var(--spacing) * 2);
  }
}

:root {
  --background: #0a0a0f;
  --foreground: #fff;
  --card: #1a1a2e;
  --card-foreground: #fff;
  --popover: #1a1a2e;
  --popover-foreground: #fff;
  --primary: #00d4ff;
  --primary-foreground: #000;
  --secondary: #f36;
  --secondary-foreground: #fff;
  --muted: #2a2a3e;
  --muted-foreground: #fff;
  --accent: #ff0;
  --accent-foreground: #000;
  --destructive: #ff4757;
  --destructive-foreground: #fff;
  --border: #3a3a4e;
  --input: #2a2a3e;
  --input-foreground: #fff;
  --ring: #00d4ff;
  --radius: .75rem;
  --electric-cyan: #00d4ff;
  --neon-pink: #f36;
  --laser-green: #0f8;
  --solar-yellow: #ff0;
  --plasma-purple: #a4f;
  --fire-orange: #f60;
  --ice-blue: #4df;
  --volt-lime: #8f0;
  --cyber-blue: #09f;
  --hot-magenta: #f06;
  --toxic-green: #6f0;
  --gold-rush: #fc0;
  --royal-violet: #60f;
  --sunset-red: #f30;
  --aqua-bright: #0fc;
  --electric-lime: #cf0;
  --neon-blue: #06f;
  --cherry-pop: #f03;
  --mint-electric: #0f9;
  --banana-bright: #ff3;
  --grape-neon: #93f;
  --coral-bright: #f63;
  --turbo-teal: #0cf;
  --lime-shock: #9f3;
  --gradient-start: #0a3d62;
  --gradient-mid: #2980b9;
  --gradient-end: #e74c3c;
  --gradient-accent: #f39c12;
  --neutral-50: #f8fafc;
  --neutral-100: #f1f5f9;
  --neutral-200: #e2e8f0;
  --neutral-300: #cbd5e1;
  --neutral-400: #94a3b8;
  --neutral-500: #64748b;
  --neutral-600: #475569;
  --neutral-700: #334155;
  --neutral-800: #1e293b;
  --neutral-900: #0f172a;
}

.dark {
  --background: #0a0a0f;
  --foreground: #fff;
  --card: #1a1a2e;
  --card-foreground: #fff;
  --popover: #1a1a2e;
  --popover-foreground: #fff;
  --primary: #00d4ff;
  --primary-foreground: #000;
  --secondary: #f36;
  --secondary-foreground: #fff;
  --muted: #2a2a3e;
  --muted-foreground: #fff;
  --accent: #ff0;
  --accent-foreground: #000;
  --destructive: #ff4757;
  --destructive-foreground: #fff;
  --border: #3a3a4e;
  --input: #2a2a3e;
  --input-foreground: #fff;
  --ring: #00d4ff;
  --electric-cyan: #0ff;
  --neon-pink: #ff0080;
  --laser-green: #0f0;
  --solar-yellow: #ff0;
  --plasma-purple: #8000ff;
  --fire-orange: #ff4000;
  --ice-blue: #40e0ff;
  --volt-lime: #80ff00;
}

* {
  border-color: var(--border);
}

body {
  background-color: var(--background);
  color: var(--foreground);
  font-family: Inter, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif;
}

html {
  scroll-behavior: smooth;
}

.bg-gradient-electric {
  background: linear-gradient(135deg, var(--electric-cyan) 0%, var(--ice-blue) 50%, var(--turbo-teal) 100%);
  box-shadow: 0 0 30px #00d4ff80;
}

.bg-gradient-neon {
  background: linear-gradient(135deg, var(--neon-pink) 0%, var(--hot-magenta) 50%, var(--cherry-pop) 100%);
  box-shadow: 0 0 30px #ff336680;
}

.bg-gradient-laser {
  background: linear-gradient(135deg, var(--laser-green) 0%, var(--toxic-green) 50%, var(--volt-lime) 100%);
  box-shadow: 0 0 30px #00ff8880;
}

.bg-gradient-solar {
  background: linear-gradient(135deg, var(--solar-yellow) 0%, var(--gold-rush) 50%, var(--banana-bright) 100%);
  box-shadow: 0 0 30px #ffff0080;
}

.bg-gradient-plasma {
  background: linear-gradient(135deg, var(--plasma-purple) 0%, var(--royal-violet) 50%, var(--grape-neon) 100%);
  box-shadow: 0 0 30px #aa44ff80;
}

.bg-gradient-fire {
  background: linear-gradient(135deg, var(--fire-orange) 0%, var(--sunset-red) 50%, var(--coral-bright) 100%);
  box-shadow: 0 0 30px #ff660080;
}

.bg-gradient-hero {
  background: linear-gradient(135deg, var(--electric-cyan) 0%, var(--neon-pink) 25%, var(--laser-green) 50%, var(--solar-yellow) 75%, var(--plasma-purple) 100%);
  box-shadow: 0 0 50px #00d4ff4d;
}

.bg-gradient-rainbow {
  background: linear-gradient(135deg, var(--electric-cyan) 0%, var(--neon-blue) 16.66%, var(--laser-green) 33.33%, var(--solar-yellow) 50%, var(--fire-orange) 66.66%, var(--neon-pink) 83.33%, var(--plasma-purple) 100%);
  box-shadow: 0 0 40px #fff3;
}

.bg-gradient-cyber {
  background: linear-gradient(135deg, var(--cyber-blue) 0%, var(--electric-cyan) 50%, var(--aqua-bright) 100%);
  box-shadow: 0 0 35px #09f6;
}

.bg-gradient-toxic {
  background: linear-gradient(135deg, var(--toxic-green) 0%, var(--electric-lime) 50%, var(--lime-shock) 100%);
  box-shadow: 0 0 35px #6f06;
}

.bg-gradient-aurora {
  background: linear-gradient(135deg, var(--deep-ocean) 0%, var(--royal-purple) 25%, var(--coral-sunset) 50%, var(--golden-hour) 75%, var(--emerald-green) 100%);
}

.bg-gradient-twilight {
  background: linear-gradient(135deg, var(--midnight-blue) 0%, var(--ocean-depth) 30%, var(--violet-dream) 70%, var(--lavender-mist) 100%);
}

.bg-gradient-sunrise {
  background: linear-gradient(135deg, var(--coral-sunset) 0%, var(--sunset-orange) 25%, var(--golden-hour) 50%, var(--champagne-gold) 75%, var(--amber-glow) 100%);
}

.text-electric {
  color: var(--electric-cyan);
}

.text-neon {
  color: var(--neon-pink);
}

.text-laser {
  color: var(--laser-green);
}

.text-solar {
  color: var(--solar-yellow);
}

.text-plasma {
  color: var(--plasma-purple);
}

.text-fire {
  color: var(--fire-orange);
}

.text-ice {
  color: var(--ice-blue);
}

.text-volt {
  color: var(--volt-lime);
}

.text-azure {
  color: var(--azure-sky);
}

.text-turquoise {
  color: var(--turquoise-wave);
}

.text-rose {
  color: var(--rose-garden);
}

.text-champagne {
  color: var(--champagne-gold);
}

.text-lavender {
  color: var(--lavender-mist);
}

.text-seafoam {
  color: var(--seafoam-green);
}

.bg-electric {
  background-color: var(--electric-cyan);
  color: #000;
}

.bg-neon {
  background-color: var(--neon-pink);
  color: #fff;
}

.bg-laser {
  background-color: var(--laser-green);
  color: #000;
}

.bg-solar {
  background-color: var(--solar-yellow);
  color: #000;
}

.bg-plasma {
  background-color: var(--plasma-purple);
  color: #fff;
}

.bg-fire {
  background-color: var(--fire-orange);
  color: #fff;
}

.bg-ice {
  background-color: var(--ice-blue);
  color: #000;
}

.bg-volt {
  background-color: var(--volt-lime);
  color: #000;
}

.bg-azure {
  background-color: var(--azure-sky);
}

.bg-turquoise {
  background-color: var(--turquoise-wave);
}

.bg-rose {
  background-color: var(--rose-garden);
}

.bg-champagne {
  background-color: var(--champagne-gold);
}

.bg-lavender {
  background-color: var(--lavender-mist);
}

.bg-seafoam {
  background-color: var(--seafoam-green);
}

.border-ocean {
  border-color: var(--deep-ocean);
}

.border-teal {
  border-color: var(--ocean-teal);
}

.border-coral {
  border-color: var(--coral-sunset);
}

.border-golden {
  border-color: var(--golden-hour);
}

.border-purple {
  border-color: var(--royal-purple);
}

.border-emerald {
  border-color: var(--emerald-green);
}

.hover-lift {
  transition: transform .3s, box-shadow .3s;
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px #00000026;
}

.glass-effect {
  backdrop-filter: blur(10px);
  background: #ffffff1a;
  border: 1px solid #fff3;
}

.animate-float {
  animation: 3s ease-in-out infinite float;
}

.animate-pulse-slow {
  animation: 3s cubic-bezier(.4, 0, .6, 1) infinite pulse;
}

.gradient-text {
  background: linear-gradient(135deg, var(--deep-ocean) 0%, var(--ocean-teal) 30%, var(--coral-sunset) 70%, var(--golden-hour) 100%);
  -webkit-text-fill-color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
  font-weight: 700;
}

.gradient-text-sunset {
  background: linear-gradient(135deg, var(--coral-sunset) 0%, var(--sunset-orange) 50%, var(--golden-hour) 100%);
  -webkit-text-fill-color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
  font-weight: 700;
}

.gradient-text-ocean {
  background: linear-gradient(135deg, var(--deep-ocean) 0%, var(--ocean-teal) 50%, var(--azure-sky) 100%);
  -webkit-text-fill-color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
  font-weight: 700;
}

.gradient-text-royal {
  background: linear-gradient(135deg, var(--royal-purple) 0%, var(--violet-dream) 50%, var(--lavender-mist) 100%);
  -webkit-text-fill-color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
  font-weight: 700;
}

.gradient-text-emerald {
  background: linear-gradient(135deg, var(--emerald-green) 0%, var(--mint-fresh) 50%, var(--seafoam-green) 100%);
  -webkit-text-fill-color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
  font-weight: 700;
}

.gradient-text-aurora {
  background: linear-gradient(135deg, var(--deep-ocean) 0%, var(--royal-purple) 25%, var(--coral-sunset) 50%, var(--golden-hour) 75%, var(--emerald-green) 100%);
  -webkit-text-fill-color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
  font-weight: 800;
}

.gradient-text-premium {
  background: linear-gradient(135deg, var(--midnight-blue) 0%, var(--royal-purple) 50%, var(--rose-garden) 100%);
  -webkit-text-fill-color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
  font-weight: 700;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-10px);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-slide-up {
  animation: .6s ease-out slideInUp;
}

.animate-slide-left {
  animation: .6s ease-out slideInLeft;
}

.animate-slide-right {
  animation: .6s ease-out slideInRight;
}

.hero-section {
  background: linear-gradient(135deg, var(--deep-ocean) 0%, var(--ocean-teal) 30%, var(--coral-sunset) 70%, var(--golden-hour) 100%);
  position: relative;
  overflow: hidden;
}

.v-logo {
  background: linear-gradient(135deg, var(--electric-cyan) 0%, var(--neon-pink) 25%, var(--laser-green) 50%, var(--solar-yellow) 75%, var(--plasma-purple) 100%);
  border: 2px solid #fff3;
  border-radius: 20px;
  justify-content: center;
  align-items: center;
  width: 64px;
  height: 64px;
  transition: all .6s cubic-bezier(.4, 0, .2, 1);
  animation: 3s ease-in-out infinite alternate logoGlow;
  display: inline-flex;
  position: relative;
  overflow: hidden;
  box-shadow: 0 0 30px #00d4ff99, 0 0 60px #f366, 0 8px 32px #0000004d, inset 0 2px #ffffff4d;
}

.v-logo:hover {
  animation: 1.5s ease-in-out infinite alternate logoGlowIntense;
  transform: translateY(-6px)scale(1.12)rotate(5deg);
  box-shadow: 0 0 50px #00d4ffcc, 0 0 80px #f369, 0 0 100px #0f86, 0 12px 48px #0006, inset 0 2px #fff6;
}

.v-logo:before {
  content: "";
  opacity: 0;
  background: linear-gradient(45deg, #0000, #fff9, #0ff6, #ffff004d, #0000);
  width: 200%;
  height: 200%;
  transition: all 1.2s;
  position: absolute;
  top: -50%;
  left: -50%;
  transform: rotate(45deg);
}

.v-logo:hover:before {
  animation: 2s ease-in-out infinite neonShimmer;
}

.v-logo-large {
  border-radius: 24px;
  width: 80px;
  height: 80px;
}

.v-logo-large .v-letter {
  font-size: 40px;
}

.v-logo-xl {
  border-radius: 32px;
  width: 112px;
  height: 112px;
}

.v-logo-xl .v-letter {
  font-size: 56px;
}

.v-letter {
  color: #000;
  text-shadow: 0 0 8px #fff, 0 0 16px #fffc, 0 2px 4px #0009;
  z-index: 2;
  letter-spacing: -.02em;
  -webkit-text-fill-color: #000;
  filter: drop-shadow(0 0 3px #ffffffe6);
  background: none;
  font-family: Inter, -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 32px;
  font-weight: 900;
}

.v-letter-white {
  color: #fff;
  text-shadow: 0 0 8px #000, 0 0 16px #000c, 0 2px 4px #ffffff4d;
  z-index: 2;
  letter-spacing: -.02em;
  -webkit-text-fill-color: #fff;
  filter: drop-shadow(0 0 3px #000000e6);
  background: none;
  font-family: Inter, -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 32px;
  font-weight: 900;
}

.v-logo-electric {
  background: linear-gradient(135deg, var(--electric-cyan) 0%, var(--ice-blue) 50%, var(--turbo-teal) 100%);
  animation: 2s ease-in-out infinite alternate electricPulse;
  box-shadow: 0 0 40px #00d4ffcc, 0 0 80px #4df9, 0 8px 32px #0000004d, inset 0 2px #fff6;
}

.v-logo-neon {
  background: linear-gradient(135deg, var(--neon-pink) 0%, var(--hot-magenta) 50%, var(--cherry-pop) 100%);
  animation: 2.5s ease-in-out infinite alternate neonPulse;
  box-shadow: 0 0 40px #f36c, 0 0 80px #f069, 0 8px 32px #0000004d, inset 0 2px #fff6;
}

.v-logo-laser {
  background: linear-gradient(135deg, var(--laser-green) 0%, var(--toxic-green) 50%, var(--volt-lime) 100%);
  animation: 2.2s ease-in-out infinite alternate laserPulse;
  box-shadow: 0 0 40px #0f8c, 0 0 80px #6f09, 0 8px 32px #0000004d, inset 0 2px #fff6;
}

.v-logo-solar {
  background: linear-gradient(135deg, var(--solar-yellow) 0%, var(--gold-rush) 50%, var(--banana-bright) 100%);
  animation: 1.8s ease-in-out infinite alternate solarPulse;
  box-shadow: 0 0 40px #ff0c, 0 0 80px #fc09, 0 8px 32px #0000004d, inset 0 2px #fff6;
}

.v-logo-plasma {
  background: linear-gradient(135deg, var(--plasma-purple) 0%, var(--royal-violet) 50%, var(--grape-neon) 100%);
  animation: 2.8s ease-in-out infinite alternate plasmaPulse;
  box-shadow: 0 0 40px #a4fc, 0 0 80px #60f9, 0 8px 32px #0000004d, inset 0 2px #fff6;
}

.v-logo-fire {
  background: linear-gradient(135deg, var(--fire-orange) 0%, var(--sunset-red) 50%, var(--coral-bright) 100%);
  animation: 1.5s ease-in-out infinite alternate firePulse;
  box-shadow: 0 0 40px #f60c, 0 0 80px #f309, 0 8px 32px #0000004d, inset 0 2px #fff6;
}

@keyframes modernShimmer {
  0% {
    opacity: 0;
    transform: translateX(-100%)rotate(45deg);
  }

  30% {
    opacity: .6;
  }

  70% {
    opacity: .8;
  }

  100% {
    opacity: 0;
    transform: translateX(100%)rotate(45deg);
  }
}

@keyframes neonShimmer {
  0% {
    opacity: 0;
    transform: translateX(-150%)rotate(45deg);
  }

  25% {
    opacity: .6;
  }

  50% {
    opacity: 1;
    transform: translateX(0%)rotate(45deg);
  }

  75% {
    opacity: .8;
  }

  100% {
    opacity: 0;
    transform: translateX(150%)rotate(45deg);
  }
}

@keyframes logoGlow {
  0% {
    box-shadow: 0 0 20px #87ceeb80, 0 8px 20px #87ceeb4d;
  }

  100% {
    box-shadow: 0 0 30px #87ceebcc, 0 12px 30px #87ceeb80;
  }
}

@keyframes logoGlowIntense {
  0% {
    box-shadow: 0 0 50px #00d4ffcc, 0 0 80px #f369, 0 0 100px #0f86, 0 12px 48px #0006;
  }

  100% {
    box-shadow: 0 0 70px #00d4ff, 0 0 100px #f36c, 0 0 120px #0f89, 0 0 140px #ff06, 0 12px 48px #0006;
  }
}

@keyframes electricPulse {
  0% {
    box-shadow: 0 0 40px #00d4ffcc, 0 0 80px #4df9;
  }

  100% {
    box-shadow: 0 0 60px #00d4ff, 0 0 120px #4dfc;
  }
}

@keyframes neonPulse {
  0% {
    box-shadow: 0 0 40px #f36c, 0 0 80px #f069;
  }

  100% {
    box-shadow: 0 0 60px #f36, 0 0 120px #f06c;
  }
}

@keyframes laserPulse {
  0% {
    box-shadow: 0 0 40px #0f8c, 0 0 80px #6f09;
  }

  100% {
    box-shadow: 0 0 60px #0f8, 0 0 120px #6f0c;
  }
}

@keyframes solarPulse {
  0% {
    box-shadow: 0 0 40px #ff0c, 0 0 80px #fc09;
  }

  100% {
    box-shadow: 0 0 60px #ff0, 0 0 120px #fc0c;
  }
}

@keyframes plasmaPulse {
  0% {
    box-shadow: 0 0 40px #a4fc, 0 0 80px #60f9;
  }

  100% {
    box-shadow: 0 0 60px #a4f, 0 0 120px #60fc;
  }
}

@keyframes firePulse {
  0% {
    box-shadow: 0 0 40px #f60c, 0 0 80px #f309;
  }

  100% {
    box-shadow: 0 0 60px #f60, 0 0 120px #f30c;
  }
}

@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 0 8px 32px #0a3d6240, 0 4px 16px #2980b926, inset 0 1px #fff3;
  }

  50% {
    box-shadow: 0 12px 48px #0a3d6266, 0 6px 24px #2980b94d, inset 0 1px #ffffff4d;
  }
}

@keyframes floatAnimation {
  0%, 100% {
    transform: translateY(0)scale(1);
  }

  50% {
    transform: translateY(-6px)scale(1.02);
  }
}

.hero-section:before {
  content: "";
  opacity: .3;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  position: absolute;
  inset: 0;
}

.service-card {
  backdrop-filter: blur(24px);
  background: #fffffffa;
  border: 1px solid #ffffff4d;
  border-radius: 24px;
  transition: all .5s cubic-bezier(.4, 0, .2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 32px #0a3d6214, 0 4px 16px #2980b90d, inset 0 1px #fffc;
}

.service-card:hover {
  border-color: var(--ocean-teal);
  background: #fff;
  transform: translateY(-16px)scale(1.03)rotate(1deg);
  box-shadow: 0 32px 64px #0a3d6226, 0 16px 32px #2980b91a, inset 0 1px #ffffffe6;
}

.service-card-bus {
  background: linear-gradient(135deg, var(--electric-cyan) 0%, var(--cyber-blue) 50%, var(--ice-blue) 100%);
  color: #000;
  text-shadow: 0 1px 2px #fffc;
  border: 2px solid #00ffff4d;
  font-weight: 800;
  box-shadow: 0 0 30px #00d4ff99, 0 0 60px #09f6, 0 12px 40px #0000004d;
}

.service-card-carpool {
  background: linear-gradient(135deg, var(--laser-green) 0%, var(--toxic-green) 50%, var(--mint-electric) 100%);
  color: #000;
  text-shadow: 0 1px 2px #fffc;
  border: 2px solid #00ff994d;
  font-weight: 800;
  box-shadow: 0 0 30px #0f89, 0 0 60px #6f06, 0 12px 40px #0000004d;
}

.service-card-bike {
  background: linear-gradient(135deg, var(--fire-orange) 0%, var(--sunset-red) 50%, var(--coral-bright) 100%);
  color: #fff;
  text-shadow: 0 1px 2px #000c;
  border: 2px solid #ff66334d;
  font-weight: 800;
  box-shadow: 0 0 30px #f609, 0 0 60px #f306, 0 12px 40px #0000004d;
}

.service-card:before {
  content: "";
  background: linear-gradient(90deg, #0000, #fff3, #0000);
  width: 100%;
  height: 100%;
  transition: left .8s;
  position: absolute;
  top: 0;
  left: -100%;
}

.service-card:hover:before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(135deg, var(--electric-cyan) 0%, var(--cyber-blue) 50%, var(--neon-blue) 100%);
  color: #000;
  text-shadow: 0 1px 2px #ffffffe6;
  border: 2px solid #00ffff4d;
  border-radius: 20px;
  padding: 18px 36px;
  font-size: 16px;
  font-weight: 800;
  transition: all .5s cubic-bezier(.4, 0, .2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 0 20px #00d4ff80, 0 0 40px #0099ff4d, 0 8px 24px #0000004d, inset 0 2px #ffffff4d;
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--neon-blue) 0%, var(--electric-cyan) 50%, var(--turbo-teal) 100%);
  transform: translateY(-4px)scale(1.05);
  box-shadow: 0 0 30px #00d4ffcc, 0 0 60px #0099ff80, 0 0 80px #00ffff4d, 0 12px 36px #0006, inset 0 2px #fff6;
}

.btn-secondary {
  background: linear-gradient(135deg, var(--neon-pink) 0%, var(--hot-magenta) 50%, var(--cherry-pop) 100%);
  color: #fff;
  text-shadow: 0 1px 2px #000c;
  border: 2px solid #ff33664d;
  border-radius: 20px;
  padding: 18px 36px;
  font-size: 16px;
  font-weight: 800;
  transition: all .5s cubic-bezier(.4, 0, .2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 0 20px #ff336680, 0 0 40px #ff00664d, 0 8px 24px #0000004d, inset 0 2px #ffffff4d;
}

.btn-secondary:hover {
  background: linear-gradient(135deg, var(--cherry-pop) 0%, var(--neon-pink) 50%, var(--hot-magenta) 100%);
  transform: translateY(-4px)scale(1.05);
  box-shadow: 0 0 30px #f36c, 0 0 60px #ff006680, 0 0 80px #ff00804d, 0 12px 36px #0006, inset 0 2px #fff6;
}

.btn-premium {
  background: linear-gradient(135deg, var(--plasma-purple) 0%, var(--royal-violet) 50%, var(--grape-neon) 100%);
  color: #fff;
  text-shadow: 0 0 10px #fffc;
  border: 2px solid #aa44ff4d;
  border-radius: 20px;
  padding: 18px 36px;
  font-size: 16px;
  font-weight: 700;
  transition: all .5s cubic-bezier(.4, 0, .2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 0 20px #aa44ff80, 0 0 40px #6600ff4d, 0 8px 24px #0000004d, inset 0 2px #ffffff4d;
}

.btn-premium:hover {
  transform: translateY(-4px)scale(1.05);
  box-shadow: 0 0 30px #a4fc, 0 0 60px #6600ff80, 0 0 80px #8000ff4d, 0 12px 36px #0006, inset 0 2px #fff6;
}

.btn-outline {
  border: 2px solid var(--ocean-teal);
  color: var(--ocean-teal);
  background: none;
  border-radius: 16px;
  padding: 14px 30px;
  font-size: 16px;
  font-weight: 600;
  transition: all .5s cubic-bezier(.4, 0, .2, 1);
  position: relative;
  overflow: hidden;
}

.btn-outline:hover {
  background: linear-gradient(135deg, var(--ocean-teal) 0%, var(--azure-sky) 100%);
  color: #fff;
  border-color: var(--azure-sky);
  transform: translateY(-3px)scale(1.02);
  box-shadow: 0 8px 24px #2980b94d, 0 4px 12px #3b82f633;
}

.btn-primary:before, .btn-secondary:before, .btn-premium:before {
  content: "";
  background: linear-gradient(90deg, #0000, #ffffff4d, #0000);
  width: 100%;
  height: 100%;
  transition: left .6s;
  position: absolute;
  top: 0;
  left: -100%;
}

.btn-primary:hover:before, .btn-secondary:hover:before, .btn-premium:hover:before {
  left: 100%;
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--sky-blue-dark) 0%, #4a9bc7 100%);
  color: #fff;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px #87ceeb66;
}

.btn-secondary {
  background: linear-gradient(135deg, var(--light-orange) 0%, var(--light-orange-dark) 100%);
  color: var(--travel-black);
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 600;
  transition: all .3s;
}

.btn-secondary:hover {
  background: linear-gradient(135deg, var(--light-orange-dark) 0%, #ff9f4d 100%);
  color: var(--travel-black);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px #ffa50066;
}

.navbar {
  backdrop-filter: blur(10px);
  background: #fffffff2;
  border-bottom: 1px solid #87ceeb33;
}

.footer {
  background: linear-gradient(135deg, var(--travel-black) 0%, var(--travel-black-light) 100%);
}

.form-input {
  border: 2px solid var(--border);
  border-radius: 8px;
  transition: all .3s;
}

.form-input:focus {
  border-color: var(--sky-blue);
  outline: none;
  box-shadow: 0 0 0 3px #87ceeb1a;
}

.shadow-travel {
  box-shadow: 0 10px 25px #00000026;
}

.shadow-travel-lg {
  box-shadow: 0 20px 40px #0000001a;
}

.shadow-sky-blue {
  box-shadow: 0 8px 20px #87ceeb66;
}

.shadow-orange {
  box-shadow: 0 8px 20px #ffa50066;
}

.bg-travel-gradient {
  background: linear-gradient(135deg, var(--sky-blue) 0%, var(--light-orange) 50%, var(--sky-blue) 100%);
}

.v-logo {
  background: linear-gradient(135deg, var(--sky-blue) 0%, var(--light-orange) 100%);
  border-radius: 12px;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  transition: all .3s;
  display: flex;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 20px #87ceeb66;
}

.v-logo:before {
  content: "";
  background: linear-gradient(90deg, #0000, #ffffff4d, #0000);
  width: 100%;
  height: 100%;
  transition: left .6s;
  position: absolute;
  top: 0;
  left: -100%;
}

.v-logo:hover:before {
  left: 100%;
}

.v-logo:hover {
  transform: translateY(-2px)scale(1.05);
  box-shadow: 0 12px 30px #87ceeb99;
}

.v-letter {
  color: #fff;
  text-shadow: 0 2px 4px #0003;
  z-index: 1;
  font-family: Inter, sans-serif;
  font-size: 18px;
  font-weight: 900;
  position: relative;
}

.v-logo-large {
  background: linear-gradient(135deg, var(--sky-blue) 0%, var(--light-orange) 50%, var(--sky-blue) 100%);
  border-radius: 20px;
  justify-content: center;
  align-items: center;
  width: 80px;
  height: 80px;
  transition: all .4s;
  animation: 3s ease-in-out infinite logoFloat;
  display: flex;
  position: relative;
  overflow: hidden;
  box-shadow: 0 15px 35px #87ceeb66;
}

.v-logo-large:before {
  content: "";
  background: linear-gradient(90deg, #0000, #fff6, #0000);
  width: 100%;
  height: 100%;
  transition: left .8s;
  position: absolute;
  top: 0;
  left: -100%;
}

.v-logo-large:hover:before {
  left: 100%;
}

.v-logo-large:hover {
  transform: translateY(-4px)scale(1.08);
  box-shadow: 0 20px 45px #87ceeb99;
}

.v-letter-large {
  color: #fff;
  text-shadow: 0 4px 8px #0000004d;
  z-index: 1;
  font-family: Inter, sans-serif;
  font-size: 36px;
  font-weight: 900;
  position: relative;
}

@keyframes logoFloat {
  0%, 100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-8px);
  }
}

.v-logo-minimal {
  background: linear-gradient(45deg, var(--sky-blue), var(--light-orange));
  border-radius: 8px;
  justify-content: center;
  align-items: center;
  width: 32px;
  height: 32px;
  display: flex;
  box-shadow: 0 4px 12px #87ceeb4d;
}

.v-logo-minimal .v-letter {
  font-size: 14px;
  font-weight: 800;
}

.v-logo-glow {
  background: linear-gradient(135deg, var(--sky-blue) 0%, var(--light-orange) 100%);
  border-radius: 14px;
  justify-content: center;
  align-items: center;
  width: 48px;
  height: 48px;
  animation: 2s ease-in-out infinite alternate logoGlow;
  display: flex;
  position: relative;
  box-shadow: 0 0 20px #87ceeb80, 0 8px 20px #87ceeb4d;
}

.v-logo-glow .v-letter {
  color: #fff;
  text-shadow: 0 0 10px #ffffff80;
  font-size: 20px;
  font-weight: 900;
}

.loading-spinner {
  border: 3px solid hsl(var(--muted));
  border-top: 3px solid hsl(var(--sky-blue));
  border-radius: 50%;
  width: 24px;
  height: 24px;
  animation: 1s linear infinite spin;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@media (width <= 768px) {
  .hero-section {
    padding: 2rem 1rem;
  }

  .service-card {
    margin-bottom: 1rem;
  }
}

@media (prefers-reduced-motion: reduce) {
  .hover-lift, .btn-primary, .btn-secondary, .service-card {
    transition: none;
  }

  .animate-float, .animate-pulse-slow, .animate-slide-up, .animate-slide-left, .animate-slide-right {
    animation: none;
  }
}

@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-gradient-position {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}

@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}

@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-leading {
  syntax: "*";
  inherits: false
}

@property --tw-font-weight {
  syntax: "*";
  inherits: false
}

@property --tw-tracking {
  syntax: "*";
  inherits: false
}

@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-ring-inset {
  syntax: "*";
  inherits: false
}

@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0;
}

@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}

@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-blur {
  syntax: "*";
  inherits: false
}

@property --tw-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-invert {
  syntax: "*";
  inherits: false
}

@property --tw-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false
}

@property --tw-duration {
  syntax: "*";
  inherits: false
}

@keyframes pulse {
  50% {
    opacity: .5;
  }
}

@keyframes bounce {
  0%, 100% {
    animation-timing-function: cubic-bezier(.8, 0, 1, 1);
    transform: translateY(-25%);
  }

  50% {
    animation-timing-function: cubic-bezier(0, 0, .2, 1);
    transform: none;
  }
}

/*# sourceMappingURL=src_app_globals_css_f9ee138c._.single.css.map*/