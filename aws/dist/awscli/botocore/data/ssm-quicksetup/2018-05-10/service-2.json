{"version": "2.0", "metadata": {"apiVersion": "2018-05-10", "endpointPrefix": "ssm-quicksetup", "jsonVersion": "1.1", "protocol": "rest-json", "protocols": ["rest-json"], "serviceFullName": "AWS Systems Manager QuickSetup", "serviceId": "SSM QuickSetup", "signatureVersion": "v4", "signingName": "ssm-quicksetup", "uid": "ssm-quicksetup-2018-05-10", "auth": ["aws.auth#sigv4"]}, "operations": {"CreateConfigurationManager": {"name": "CreateConfigurationManager", "http": {"method": "POST", "requestUri": "/configurationManager", "responseCode": 200}, "input": {"shape": "CreateConfigurationManagerInput"}, "output": {"shape": "CreateConfigurationManagerOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Creates a Quick Setup configuration manager resource. This object is a collection of desired state configurations for multiple configuration definitions and summaries describing the deployments of those definitions.</p>"}, "DeleteConfigurationManager": {"name": "DeleteConfigurationManager", "http": {"method": "DELETE", "requestUri": "/configurationManager/{ManagerArn}", "responseCode": 200}, "input": {"shape": "DeleteConfigurationManagerInput"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Deletes a configuration manager.</p>", "idempotent": true}, "GetConfiguration": {"name": "GetConfiguration", "http": {"method": "GET", "requestUri": "/getConfiguration/{ConfigurationId}", "responseCode": 200}, "input": {"shape": "GetConfigurationInput"}, "output": {"shape": "GetConfigurationOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns details about the specified configuration.</p>"}, "GetConfigurationManager": {"name": "GetConfigurationManager", "http": {"method": "GET", "requestUri": "/configurationManager/{ManagerArn}", "responseCode": 200}, "input": {"shape": "GetConfigurationManagerInput"}, "output": {"shape": "GetConfigurationManagerOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns a configuration manager.</p>"}, "GetServiceSettings": {"name": "GetServiceSettings", "http": {"method": "GET", "requestUri": "/serviceSettings", "responseCode": 200}, "output": {"shape": "GetServiceSettingsOutput"}, "errors": [{"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Returns settings configured for Quick Setup in the requesting Amazon Web Services account and Amazon Web Services Region.</p>"}, "ListConfigurationManagers": {"name": "ListConfigurationManagers", "http": {"method": "POST", "requestUri": "/listConfigurationManagers", "responseCode": 200}, "input": {"shape": "ListConfigurationManagersInput"}, "output": {"shape": "ListConfigurationManagersOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Returns Quick Setup configuration managers.</p>"}, "ListConfigurations": {"name": "ListConfigurations", "http": {"method": "POST", "requestUri": "/listConfigurations", "responseCode": 200}, "input": {"shape": "ListConfigurationsInput"}, "output": {"shape": "ListConfigurationsOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns configurations deployed by Quick Setup in the requesting Amazon Web Services account and Amazon Web Services Region.</p>"}, "ListQuickSetupTypes": {"name": "ListQuickSetupTypes", "http": {"method": "GET", "requestUri": "/listQuickSetupTypes", "responseCode": 200}, "output": {"shape": "ListQuickSetupTypesOutput"}, "errors": [{"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Returns the available Quick Setup types.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags/{ResourceArn}", "responseCode": 200}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns tags assigned to the resource.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "PUT", "requestUri": "/tags/{ResourceArn}", "responseCode": 200}, "input": {"shape": "TagResourceInput"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Assigns key-value pairs of metadata to Amazon Web Services resources.</p>", "idempotent": true}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags/{ResourceArn}", "responseCode": 200}, "input": {"shape": "UntagResourceInput"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Removes tags from the specified resource.</p>", "idempotent": true}, "UpdateConfigurationDefinition": {"name": "UpdateConfigurationDefinition", "http": {"method": "PUT", "requestUri": "/configurationDefinition/{ManagerArn}/{Id}", "responseCode": 200}, "input": {"shape": "UpdateConfigurationDefinitionInput"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Updates a Quick Setup configuration definition.</p>", "idempotent": true}, "UpdateConfigurationManager": {"name": "UpdateConfigurationManager", "http": {"method": "PUT", "requestUri": "/configurationManager/{ManagerArn}", "responseCode": 200}, "input": {"shape": "UpdateConfigurationManagerInput"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Updates a Quick Setup configuration manager.</p>", "idempotent": true}, "UpdateServiceSettings": {"name": "UpdateServiceSettings", "http": {"method": "PUT", "requestUri": "/serviceSettings", "responseCode": 200}, "input": {"shape": "UpdateServiceSettingsInput"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Updates settings configured for Quick Setup.</p>", "idempotent": true}}, "shapes": {"AccessDeniedException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>The requester has insufficient permissions to perform the operation.</p>", "error": {"httpStatusCode": 403, "senderFault": true}, "exception": true}, "ConfigurationDefinition": {"type": "structure", "required": ["Parameters", "Type"], "members": {"Id": {"shape": "String", "documentation": "<p>The ID of the configuration definition.</p>"}, "LocalDeploymentAdministrationRoleArn": {"shape": "IAMRoleArn", "documentation": "<p>The ARN of the IAM role used to administrate local configuration deployments.</p>"}, "LocalDeploymentExecutionRoleName": {"shape": "ConfigurationDefinitionLocalDeploymentExecutionRoleNameString", "documentation": "<p>The name of the IAM role used to deploy local configurations.</p>"}, "Parameters": {"shape": "ConfigurationParametersMap", "documentation": "<p>A list of key-value pairs containing the required parameters for the configuration type.</p>"}, "Type": {"shape": "ConfigurationDefinitionTypeString", "documentation": "<p>The type of the Quick Setup configuration.</p>"}, "TypeVersion": {"shape": "ConfigurationDefinitionTypeVersionString", "documentation": "<p>The version of the Quick Setup type used.</p>"}}, "documentation": "<p>The definition of a Quick Setup configuration.</p>"}, "ConfigurationDefinitionInput": {"type": "structure", "required": ["Parameters", "Type"], "members": {"LocalDeploymentAdministrationRoleArn": {"shape": "IAMRoleArn", "documentation": "<p>The ARN of the IAM role used to administrate local configuration deployments.</p>"}, "LocalDeploymentExecutionRoleName": {"shape": "ConfigurationDefinitionInputLocalDeploymentExecutionRoleNameString", "documentation": "<p>The name of the IAM role used to deploy local configurations.</p>"}, "Parameters": {"shape": "ConfigurationParametersMap", "documentation": "<p>The parameters for the configuration definition type. Parameters for configuration definitions vary based the configuration type. The following tables outline the parameters for each configuration type.</p> <dl> <dt>OpsCenter (Type: Amazon Web ServicesQuickSetupType-SSMOpsCenter)</dt> <dd> <ul> <li> <p> <code>DelegatedAccountId</code> </p> <ul> <li> <p>Description: (Required) The ID of the delegated administrator account.</p> </li> </ul> </li> <li> <p> <code>TargetOrganizationalUnits</code> </p> <ul> <li> <p>Description: (Required) A comma separated list of organizational units (OUs) you want to deploy the configuration to.</p> </li> </ul> </li> <li> <p> <code>TargetRegions</code> </p> <ul> <li> <p>Description: (Required) A comma separated list of Amazon Web Services Regions you want to deploy the configuration to.</p> </li> </ul> </li> </ul> </dd> <dt>Resource Scheduler (Type: Amazon Web ServicesQuickSetupType-Scheduler)</dt> <dd> <ul> <li> <p> <code>TargetTagKey</code> </p> <ul> <li> <p>Description: (Required) The tag key assigned to the instances you want to target.</p> </li> </ul> </li> <li> <p> <code>TargetTagValue</code> </p> <ul> <li> <p>Description: (Required) The value of the tag key assigned to the instances you want to target.</p> </li> </ul> </li> <li> <p> <code>ICalendarString</code> </p> <ul> <li> <p>Description: (Required) An iCalendar formatted string containing the schedule you want Change Manager to use.</p> </li> </ul> </li> <li> <p> <code>TargetAccounts</code> </p> <ul> <li> <p>Description: (Optional) The ID of the Amazon Web Services account initiating the configuration deployment. You only need to provide a value for this parameter if you want to deploy the configuration locally. A value must be provided for either <code>TargetAccounts</code> or <code>TargetOrganizationalUnits</code>.</p> </li> </ul> </li> <li> <p> <code>TargetOrganizationalUnits</code> </p> <ul> <li> <p>Description: (Optional) A comma separated list of organizational units (OUs) you want to deploy the configuration to.</p> </li> </ul> </li> <li> <p> <code>TargetRegions</code> </p> <ul> <li> <p>Description: (Required) A comma separated list of Amazon Web Services Regions you want to deploy the configuration to.</p> </li> </ul> </li> </ul> </dd> <dt>Default Host Management Configuration (Type: Amazon Web ServicesQuickSetupType-DHMC)</dt> <dd> <ul> <li> <p> <code>UpdateSSMAgent</code> </p> <ul> <li> <p>Description: (Optional) A boolean value that determines whether the SSM Agent is updated on the target instances every 2 weeks. The default value is \"<code>true</code>\".</p> </li> </ul> </li> <li> <p> <code>TargetOrganizationalUnits</code> </p> <ul> <li> <p>Description: (Required) A comma separated list of organizational units (OUs) you want to deploy the configuration to.</p> </li> </ul> </li> <li> <p> <code>TargetRegions</code> </p> <ul> <li> <p>Description: (Required) A comma separated list of Amazon Web Services Regions you want to deploy the configuration to.</p> </li> </ul> </li> </ul> </dd> <dt>Resource Explorer (Type: Amazon Web ServicesQuickSetupType-ResourceExplorer)</dt> <dd> <ul> <li> <p> <code>SelectedAggregatorRegion</code> </p> <ul> <li> <p>Description: (Required) The Amazon Web Services Region where you want to create the aggregator index.</p> </li> </ul> </li> <li> <p> <code>ReplaceExistingAggregator</code> </p> <ul> <li> <p>Description: (Required) A boolean value that determines whether to demote an existing aggregator if it is in a Region that differs from the value you specify for the <code>SelectedAggregatorRegion</code>.</p> </li> </ul> </li> <li> <p> <code>TargetOrganizationalUnits</code> </p> <ul> <li> <p>Description: (Required) A comma separated list of organizational units (OUs) you want to deploy the configuration to.</p> </li> </ul> </li> <li> <p> <code>TargetRegions</code> </p> <ul> <li> <p>Description: (Required) A comma separated list of Amazon Web Services Regions you want to deploy the configuration to.</p> </li> </ul> </li> </ul> </dd> <dt>Change Manager (Type: Amazon Web ServicesQuickSetupType-SSMChangeMgr)</dt> <dd> <ul> <li> <p> <code>DelegatedAccountId</code> </p> <ul> <li> <p>Description: (Required) The ID of the delegated administrator account.</p> </li> </ul> </li> <li> <p> <code>JobFunction</code> </p> <ul> <li> <p>Description: (Required) The name for the Change Manager job function.</p> </li> </ul> </li> <li> <p> <code>PermissionType</code> </p> <ul> <li> <p>Description: (Optional) Specifies whether you want to use default administrator permissions for the job function role, or provide a custom IAM policy. The valid values are <code>CustomPermissions</code> and <code>AdminPermissions</code>. The default value for the parameter is <code>CustomerPermissions</code>.</p> </li> </ul> </li> <li> <p> <code>CustomPermissions</code> </p> <ul> <li> <p>Description: (Optional) A JSON string containing the IAM policy you want your job function to use. You must provide a value for this parameter if you specify <code>CustomPermissions</code> for the <code>PermissionType</code> parameter.</p> </li> </ul> </li> <li> <p> <code>TargetOrganizationalUnits</code> </p> <ul> <li> <p>Description: (Required) A comma separated list of organizational units (OUs) you want to deploy the configuration to.</p> </li> </ul> </li> <li> <p> <code>TargetRegions</code> </p> <ul> <li> <p>Description: (Required) A comma separated list of Amazon Web Services Regions you want to deploy the configuration to.</p> </li> </ul> </li> </ul> </dd> <dt>DevOps Guru (Type: Amazon Web ServicesQuickSetupType-DevOpsGuru)</dt> <dd> <ul> <li> <p> <code>AnalyseAllResources</code> </p> <ul> <li> <p>Description: (Optional) A boolean value that determines whether DevOps Guru analyzes all CloudFormation stacks in the account. The default value is \"<code>false</code>\".</p> </li> </ul> </li> <li> <p> <code>EnableSnsNotifications</code> </p> <ul> <li> <p>Description: (Optional) A boolean value that determines whether DevOps Guru sends notifications when an insight is created. The default value is \"<code>true</code>\".</p> </li> </ul> </li> <li> <p> <code>EnableSsmOpsItems</code> </p> <ul> <li> <p>Description: (Optional) A boolean value that determines whether DevOps Guru creates an OpsCenter OpsItem when an insight is created. The default value is \"<code>true</code>\".</p> </li> </ul> </li> <li> <p> <code>EnableDriftRemediation</code> </p> <ul> <li> <p>Description: (Optional) A boolean value that determines whether a drift remediation schedule is used. The default value is \"<code>false</code>\".</p> </li> </ul> </li> <li> <p> <code>RemediationSchedule</code> </p> <ul> <li> <p>Description: (Optional) A rate expression that defines the schedule for drift remediation. The valid values are <code>rate(30 days)</code>, <code>rate(14 days)</code>, <code>rate(1 days)</code>, and <code>none</code>. The default value is \"<code>none</code>\".</p> </li> </ul> </li> <li> <p> <code>TargetAccounts</code> </p> <ul> <li> <p>Description: (Optional) The ID of the Amazon Web Services account initiating the configuration deployment. You only need to provide a value for this parameter if you want to deploy the configuration locally. A value must be provided for either <code>TargetAccounts</code> or <code>TargetOrganizationalUnits</code>.</p> </li> </ul> </li> <li> <p> <code>TargetOrganizationalUnits</code> </p> <ul> <li> <p>Description: (Optional) A comma separated list of organizational units (OUs) you want to deploy the configuration to.</p> </li> </ul> </li> <li> <p> <code>TargetRegions</code> </p> <ul> <li> <p>Description: (Required) A comma separated list of Amazon Web Services Regions you want to deploy the configuration to.</p> </li> </ul> </li> </ul> </dd> <dt>Conformance Packs (Type: Amazon Web ServicesQuickSetupType-CFGCPacks)</dt> <dd> <ul> <li> <p> <code>DelegatedAccountId</code> </p> <ul> <li> <p>Description: (Optional) The ID of the delegated administrator account. This parameter is required for Organization deployments.</p> </li> </ul> </li> <li> <p> <code>RemediationSchedule</code> </p> <ul> <li> <p>Description: (Optional) A rate expression that defines the schedule for drift remediation. The valid values are <code>rate(30 days)</code>, <code>rate(14 days)</code>, <code>rate(2 days)</code>, and <code>none</code>. The default value is \"<code>none</code>\".</p> </li> </ul> </li> <li> <p> <code>CPackNames</code> </p> <ul> <li> <p>Description: (Required) A comma separated list of Config conformance packs.</p> </li> </ul> </li> <li> <p> <code>TargetAccounts</code> </p> <ul> <li> <p>Description: (Optional) The ID of the Amazon Web Services account initiating the configuration deployment. You only need to provide a value for this parameter if you want to deploy the configuration locally. A value must be provided for either <code>TargetAccounts</code> or <code>TargetOrganizationalUnits</code>.</p> </li> </ul> </li> <li> <p> <code>TargetOrganizationalUnits</code> </p> <ul> <li> <p>Description: (Optional) The ID of the root of your Organization. This configuration type doesn't currently support choosing specific OUs. The configuration will be deployed to all the OUs in the Organization.</p> </li> </ul> </li> <li> <p> <code>TargetRegions</code> </p> <ul> <li> <p>Description: (Required) A comma separated list of Amazon Web Services Regions you want to deploy the configuration to.</p> </li> </ul> </li> </ul> </dd> <dt>Config Recording (Type: Amazon Web ServicesQuickSetupType-CFGRecording)</dt> <dd> <ul> <li> <p> <code>RecordAllResources</code> </p> <ul> <li> <p>Description: (Optional) A boolean value that determines whether all supported resources are recorded. The default value is \"<code>true</code>\".</p> </li> </ul> </li> <li> <p> <code>ResourceTypesToRecord</code> </p> <ul> <li> <p>Description: (Optional) A comma separated list of resource types you want to record.</p> </li> </ul> </li> <li> <p> <code>RecordGlobalResourceTypes</code> </p> <ul> <li> <p>Description: (Optional) A boolean value that determines whether global resources are recorded with all resource configurations. The default value is \"<code>false</code>\".</p> </li> </ul> </li> <li> <p> <code>GlobalResourceTypesRegion</code> </p> <ul> <li> <p>Description: (Optional) Determines the Amazon Web Services Region where global resources are recorded.</p> </li> </ul> </li> <li> <p> <code>UseCustomBucket</code> </p> <ul> <li> <p>Description: (Optional) A boolean value that determines whether a custom Amazon S3 bucket is used for delivery. The default value is \"<code>false</code>\".</p> </li> </ul> </li> <li> <p> <code>DeliveryBucketName</code> </p> <ul> <li> <p>Description: (Optional) The name of the Amazon S3 bucket you want Config to deliver configuration snapshots and configuration history files to.</p> </li> </ul> </li> <li> <p> <code>DeliveryBucketPrefix</code> </p> <ul> <li> <p>Description: (Optional) The key prefix you want to use in the custom Amazon S3 bucket.</p> </li> </ul> </li> <li> <p> <code>NotificationOptions</code> </p> <ul> <li> <p>Description: (Optional) Determines the notification configuration for the recorder. The valid values are <code>NoStreaming</code>, <code>UseExistingTopic</code>, and <code>CreateTopic</code>. The default value is <code>NoStreaming</code>.</p> </li> </ul> </li> <li> <p> <code>CustomDeliveryTopicAccountId</code> </p> <ul> <li> <p>Description: (Optional) The ID of the Amazon Web Services account where the Amazon SNS topic you want to use for notifications resides. You must specify a value for this parameter if you use the <code>UseExistingTopic</code> notification option.</p> </li> </ul> </li> <li> <p> <code>CustomDeliveryTopicName</code> </p> <ul> <li> <p>Description: (Optional) The name of the Amazon SNS topic you want to use for notifications. You must specify a value for this parameter if you use the <code>UseExistingTopic</code> notification option.</p> </li> </ul> </li> <li> <p> <code>RemediationSchedule</code> </p> <ul> <li> <p>Description: (Optional) A rate expression that defines the schedule for drift remediation. The valid values are <code>rate(30 days)</code>, <code>rate(7 days)</code>, <code>rate(1 days)</code>, and <code>none</code>. The default value is \"<code>none</code>\".</p> </li> </ul> </li> <li> <p> <code>TargetAccounts</code> </p> <ul> <li> <p>Description: (Optional) The ID of the Amazon Web Services account initiating the configuration deployment. You only need to provide a value for this parameter if you want to deploy the configuration locally. A value must be provided for either <code>TargetAccounts</code> or <code>TargetOrganizationalUnits</code>.</p> </li> </ul> </li> <li> <p> <code>TargetOrganizationalUnits</code> </p> <ul> <li> <p>Description: (Optional) The ID of the root of your Organization. This configuration type doesn't currently support choosing specific OUs. The configuration will be deployed to all the OUs in the Organization.</p> </li> </ul> </li> <li> <p> <code>TargetRegions</code> </p> <ul> <li> <p>Description: (Required) A comma separated list of Amazon Web Services Regions you want to deploy the configuration to.</p> </li> </ul> </li> </ul> </dd> <dt>Host Management (Type: Amazon Web ServicesQuickSetupType-SSMHostMgmt)</dt> <dd> <ul> <li> <p> <code>UpdateSSMAgent</code> </p> <ul> <li> <p>Description: (Optional) A boolean value that determines whether the SSM Agent is updated on the target instances every 2 weeks. The default value is \"<code>true</code>\".</p> </li> </ul> </li> <li> <p> <code>UpdateEc2LaunchAgent</code> </p> <ul> <li> <p>Description: (Optional) A boolean value that determines whether the EC2 Launch agent is updated on the target instances every month. The default value is \"<code>false</code>\".</p> </li> </ul> </li> <li> <p> <code>CollectInventory</code> </p> <ul> <li> <p>Description: (Optional) A boolean value that determines whether the EC2 Launch agent is updated on the target instances every month. The default value is \"<code>true</code>\".</p> </li> </ul> </li> <li> <p> <code>ScanInstances</code> </p> <ul> <li> <p>Description: (Optional) A boolean value that determines whether the target instances are scanned daily for available patches. The default value is \"<code>true</code>\".</p> </li> </ul> </li> <li> <p> <code>InstallCloudWatchAgent</code> </p> <ul> <li> <p>Description: (Optional) A boolean value that determines whether the Amazon CloudWatch agent is installed on the target instances. The default value is \"<code>false</code>\".</p> </li> </ul> </li> <li> <p> <code>UpdateCloudWatchAgent</code> </p> <ul> <li> <p>Description: (Optional) A boolean value that determines whether the Amazon CloudWatch agent is updated on the target instances every month. The default value is \"<code>false</code>\".</p> </li> </ul> </li> <li> <p> <code>IsPolicyAttachAllowed</code> </p> <ul> <li> <p>Description: (Optional) A boolean value that determines whether Quick Setup attaches policies to instances profiles already associated with the target instances. The default value is \"<code>false</code>\".</p> </li> </ul> </li> <li> <p> <code>TargetType</code> </p> <ul> <li> <p>Description: (Optional) Determines how instances are targeted for local account deployments. Don't specify a value for this parameter if you're deploying to OUs. The valid values are <code>*</code>, <code>InstanceIds</code>, <code>ResourceGroups</code>, and <code>Tags</code>. Use <code>*</code> to target all instances in the account.</p> </li> </ul> </li> <li> <p> <code>TargetInstances</code> </p> <ul> <li> <p>Description: (Optional) A comma separated list of instance IDs. You must provide a value for this parameter if you specify <code>InstanceIds</code> for the <code>TargetType</code> parameter.</p> </li> </ul> </li> <li> <p> <code>TargetTagKey</code> </p> <ul> <li> <p>Description: (Optional) The tag key assigned to the instances you want to target. You must provide a value for this parameter if you specify <code>Tags</code> for the <code>TargetType</code> parameter.</p> </li> </ul> </li> <li> <p> <code>TargetTagValue</code> </p> <ul> <li> <p>Description: (Optional) The value of the tag key assigned to the instances you want to target. You must provide a value for this parameter if you specify <code>Tags</code> for the <code>TargetType</code> parameter.</p> </li> </ul> </li> <li> <p> <code>ResourceGroupName</code> </p> <ul> <li> <p>Description: (Optional) The name of the resource group associated with the instances you want to target. You must provide a value for this parameter if you specify <code>ResourceGroups</code> for the <code>TargetType</code> parameter.</p> </li> </ul> </li> <li> <p> <code>TargetAccounts</code> </p> <ul> <li> <p>Description: (Optional) The ID of the Amazon Web Services account initiating the configuration deployment. You only need to provide a value for this parameter if you want to deploy the configuration locally. A value must be provided for either <code>TargetAccounts</code> or <code>TargetOrganizationalUnits</code>.</p> </li> </ul> </li> <li> <p> <code>TargetOrganizationalUnits</code> </p> <ul> <li> <p>Description: (Optional) A comma separated list of organizational units (OUs) you want to deploy the configuration to.</p> </li> </ul> </li> <li> <p> <code>TargetRegions</code> </p> <ul> <li> <p>Description: (Required) A comma separated list of Amazon Web Services Regions you want to deploy the configuration to.</p> </li> </ul> </li> </ul> </dd> <dt>Distributor (Type: Amazon Web ServicesQuickSetupType-Distributor)</dt> <dd> <ul> <li> <p> <code>PackagesToInstall</code> </p> <ul> <li> <p>Description: (Required) A comma separated list of packages you want to install on the target instances. The valid values are <code>AWSEFSTools</code>, <code>AWSCWAgent</code>, and <code>AWSEC2LaunchAgent</code>.</p> </li> </ul> </li> <li> <p> <code>RemediationSchedule</code> </p> <ul> <li> <p>Description: (Optional) A rate expression that defines the schedule for drift remediation. The valid values are <code>rate(30 days)</code>, <code>rate(14 days)</code>, <code>rate(2 days)</code>, and <code>none</code>. The default value is \"<code>rate(30 days)</code>\".</p> </li> </ul> </li> <li> <p> <code>IsPolicyAttachAllowed</code> </p> <ul> <li> <p>Description: (Optional) A boolean value that determines whether Quick Setup attaches policies to instances profiles already associated with the target instances. The default value is \"<code>false</code>\".</p> </li> </ul> </li> <li> <p> <code>TargetType</code> </p> <ul> <li> <p>Description: (Optional) Determines how instances are targeted for local account deployments. Don't specify a value for this parameter if you're deploying to OUs. The valid values are <code>*</code>, <code>InstanceIds</code>, <code>ResourceGroups</code>, and <code>Tags</code>. Use <code>*</code> to target all instances in the account.</p> </li> </ul> </li> <li> <p> <code>TargetInstances</code> </p> <ul> <li> <p>Description: (Optional) A comma separated list of instance IDs. You must provide a value for this parameter if you specify <code>InstanceIds</code> for the <code>TargetType</code> parameter.</p> </li> </ul> </li> <li> <p> <code>TargetTagKey</code> </p> <ul> <li> <p>Description: (Required) The tag key assigned to the instances you want to target. You must provide a value for this parameter if you specify <code>Tags</code> for the <code>TargetType</code> parameter.</p> </li> </ul> </li> <li> <p> <code>TargetTagValue</code> </p> <ul> <li> <p>Description: (Required) The value of the tag key assigned to the instances you want to target. You must provide a value for this parameter if you specify <code>Tags</code> for the <code>TargetType</code> parameter.</p> </li> </ul> </li> <li> <p> <code>ResourceGroupName</code> </p> <ul> <li> <p>Description: (Required) The name of the resource group associated with the instances you want to target. You must provide a value for this parameter if you specify <code>ResourceGroups</code> for the <code>TargetType</code> parameter.</p> </li> </ul> </li> <li> <p> <code>TargetAccounts</code> </p> <ul> <li> <p>Description: (Optional) The ID of the Amazon Web Services account initiating the configuration deployment. You only need to provide a value for this parameter if you want to deploy the configuration locally. A value must be provided for either <code>TargetAccounts</code> or <code>TargetOrganizationalUnits</code>.</p> </li> </ul> </li> <li> <p> <code>TargetOrganizationalUnits</code> </p> <ul> <li> <p>Description: (Optional) A comma separated list of organizational units (OUs) you want to deploy the configuration to.</p> </li> </ul> </li> <li> <p> <code>TargetRegions</code> </p> <ul> <li> <p>Description: (Required) A comma separated list of Amazon Web Services Regions you want to deploy the configuration to.</p> </li> </ul> </li> </ul> </dd> <dt>Patch Policy (Type: Amazon Web ServicesQuickSetupType-PatchPolicy)</dt> <dd> <ul> <li> <p> <code>PatchPolicyName</code> </p> <ul> <li> <p>Description: (Required) A name for the patch policy. The value you provide is applied to target Amazon EC2 instances as a tag.</p> </li> </ul> </li> <li> <p> <code>SelectedPatchBaselines</code> </p> <ul> <li> <p>Description: (Required) An array of JSON objects containing the information for the patch baselines to include in your patch policy.</p> </li> </ul> </li> <li> <p> <code>PatchBaselineUseDefault</code> </p> <ul> <li> <p>Description: (Optional) A boolean value that determines whether the selected patch baselines are all Amazon Web Services provided.</p> </li> </ul> </li> <li> <p> <code>ConfigurationOptionsPatchOperation</code> </p> <ul> <li> <p>Description: (Optional) Determines whether target instances scan for available patches, or scan and install available patches. The valid values are <code>Scan</code> and <code>ScanAndInstall</code>. The default value for the parameter is <code>Scan</code>.</p> </li> </ul> </li> <li> <p> <code>ConfigurationOptionsScanValue</code> </p> <ul> <li> <p>Description: (Optional) A cron expression that is used as the schedule for when instances scan for available patches.</p> </li> </ul> </li> <li> <p> <code>ConfigurationOptionsInstallValue</code> </p> <ul> <li> <p>Description: (Optional) A cron expression that is used as the schedule for when instances install available patches.</p> </li> </ul> </li> <li> <p> <code>ConfigurationOptionsScanNextInterval</code> </p> <ul> <li> <p>Description: (Optional) A boolean value that determines whether instances should scan for available patches at the next cron interval. The default value is \"<code>false</code>\".</p> </li> </ul> </li> <li> <p> <code>ConfigurationOptionsInstallNextInterval</code> </p> <ul> <li> <p>Description: (Optional) A boolean value that determines whether instances should scan for available patches at the next cron interval. The default value is \"<code>false</code>\".</p> </li> </ul> </li> <li> <p> <code>RebootOption</code> </p> <ul> <li> <p>Description: (Optional) Determines whether instances are rebooted after patches are installed. Valid values are <code>RebootIfNeeded</code> and <code>NoReboot</code>.</p> </li> </ul> </li> <li> <p> <code>IsPolicyAttachAllowed</code> </p> <ul> <li> <p>Description: (Optional) A boolean value that determines whether Quick Setup attaches policies to instances profiles already associated with the target instances. The default value is \"<code>false</code>\".</p> </li> </ul> </li> <li> <p> <code>OutputLogEnableS3</code> </p> <ul> <li> <p>Description: (Optional) A boolean value that determines whether command output logs are sent to Amazon S3.</p> </li> </ul> </li> <li> <p> <code>OutputS3Location</code> </p> <ul> <li> <p>Description: (Optional) A JSON string containing information about the Amazon S3 bucket where you want to store the output details of the request.</p> <ul> <li> <p> <code>OutputS3BucketRegion</code> </p> <ul> <li> <p>Description: (Optional) The Amazon Web Services Region where the Amazon S3 bucket you want Config to deliver command output to is located.</p> </li> </ul> </li> <li> <p> <code>OutputS3BucketName</code> </p> <ul> <li> <p>Description: (Optional) The name of the Amazon S3 bucket you want Config to deliver command output to.</p> </li> </ul> </li> <li> <p> <code>OutputS3KeyPrefix</code> </p> <ul> <li> <p>Description: (Optional) The key prefix you want to use in the custom Amazon S3 bucket.</p> </li> </ul> </li> </ul> </li> </ul> </li> <li> <p> <code>TargetType</code> </p> <ul> <li> <p>Description: (Optional) Determines how instances are targeted for local account deployments. Don't specify a value for this parameter if you're deploying to OUs. The valid values are <code>*</code>, <code>InstanceIds</code>, <code>ResourceGroups</code>, and <code>Tags</code>. Use <code>*</code> to target all instances in the account.</p> </li> </ul> </li> <li> <p> <code>TargetInstances</code> </p> <ul> <li> <p>Description: (Optional) A comma separated list of instance IDs. You must provide a value for this parameter if you specify <code>InstanceIds</code> for the <code>TargetType</code> parameter.</p> </li> </ul> </li> <li> <p> <code>TargetTagKey</code> </p> <ul> <li> <p>Description: (Required) The tag key assigned to the instances you want to target. You must provide a value for this parameter if you specify <code>Tags</code> for the <code>TargetType</code> parameter.</p> </li> </ul> </li> <li> <p> <code>TargetTagValue</code> </p> <ul> <li> <p>Description: (Required) The value of the tag key assigned to the instances you want to target. You must provide a value for this parameter if you specify <code>Tags</code> for the <code>TargetType</code> parameter.</p> </li> </ul> </li> <li> <p> <code>ResourceGroupName</code> </p> <ul> <li> <p>Description: (Required) The name of the resource group associated with the instances you want to target. You must provide a value for this parameter if you specify <code>ResourceGroups</code> for the <code>TargetType</code> parameter.</p> </li> </ul> </li> <li> <p> <code>TargetAccounts</code> </p> <ul> <li> <p>Description: (Optional) The ID of the Amazon Web Services account initiating the configuration deployment. You only need to provide a value for this parameter if you want to deploy the configuration locally. A value must be provided for either <code>TargetAccounts</code> or <code>TargetOrganizationalUnits</code>.</p> </li> </ul> </li> <li> <p> <code>TargetOrganizationalUnits</code> </p> <ul> <li> <p>Description: (Optional) A comma separated list of organizational units (OUs) you want to deploy the configuration to.</p> </li> </ul> </li> <li> <p> <code>TargetRegions</code> </p> <ul> <li> <p>Description: (Required) A comma separated list of Amazon Web Services Regions you want to deploy the configuration to.</p> </li> </ul> </li> </ul> </dd> </dl>"}, "Type": {"shape": "ConfigurationDefinitionInputTypeString", "documentation": "<p>The type of the Quick Setup configuration.</p>"}, "TypeVersion": {"shape": "ConfigurationDefinitionInputTypeVersionString", "documentation": "<p>The version of the Quick Setup type to use.</p>"}}, "documentation": "<p>Defines the preferences and options for a configuration definition.</p>"}, "ConfigurationDefinitionInputLocalDeploymentExecutionRoleNameString": {"type": "string", "pattern": "^[\\w+=,.@-]{1,64}$"}, "ConfigurationDefinitionInputTypeString": {"type": "string", "pattern": "^[a-zA-Z0-9_\\-.:/]{3,200}$"}, "ConfigurationDefinitionInputTypeVersionString": {"type": "string", "max": 128, "min": 1}, "ConfigurationDefinitionLocalDeploymentExecutionRoleNameString": {"type": "string", "pattern": "^[\\w+=,.@-]{1,64}$"}, "ConfigurationDefinitionSummariesList": {"type": "list", "member": {"shape": "ConfigurationDefinitionSummary"}}, "ConfigurationDefinitionSummary": {"type": "structure", "members": {"FirstClassParameters": {"shape": "ConfigurationParametersMap", "documentation": "<p>The common parameters and values for the configuration definition.</p>"}, "Id": {"shape": "String", "documentation": "<p>The ID of the configuration definition.</p>"}, "Type": {"shape": "String", "documentation": "<p>The type of the Quick Setup configuration used by the configuration definition.</p>"}, "TypeVersion": {"shape": "String", "documentation": "<p>The version of the Quick Setup type used by the configuration definition.</p>"}}, "documentation": "<p>A summarized definition of a Quick Setup configuration definition.</p>"}, "ConfigurationDefinitionTypeString": {"type": "string", "pattern": "^[a-zA-Z0-9_\\-.:/]{3,200}$"}, "ConfigurationDefinitionTypeVersionString": {"type": "string", "max": 128, "min": 1}, "ConfigurationDefinitionsInputList": {"type": "list", "member": {"shape": "ConfigurationDefinitionInput"}}, "ConfigurationDefinitionsList": {"type": "list", "member": {"shape": "ConfigurationDefinition"}}, "ConfigurationManagerList": {"type": "list", "member": {"shape": "ConfigurationManager<PERSON><PERSON><PERSON><PERSON>"}}, "ConfigurationManagerSummary": {"type": "structure", "required": ["ManagerArn"], "members": {"ConfigurationDefinitionSummaries": {"shape": "ConfigurationDefinitionSummariesList", "documentation": "<p>A summary of the Quick Setup configuration definition.</p>"}, "Description": {"shape": "String", "documentation": "<p>The description of the configuration.</p>"}, "ManagerArn": {"shape": "String", "documentation": "<p>The ARN of the Quick Setup configuration.</p>"}, "Name": {"shape": "String", "documentation": "<p>The name of the configuration</p>"}, "StatusSummaries": {"shape": "StatusSummariesList", "documentation": "<p>Summaries of the state of the configuration manager. These summaries include an aggregate of the statuses from the configuration definition associated with the configuration manager. This includes deployment statuses, association statuses, drift statuses, health checks, and more.</p>"}}, "documentation": "<p>A summary of a Quick Setup configuration manager.</p>"}, "ConfigurationParametersMap": {"type": "map", "key": {"shape": "ConfigurationParametersMapKeyString"}, "value": {"shape": "ConfigurationParametersMapValueString"}}, "ConfigurationParametersMapKeyString": {"type": "string", "max": 256, "min": 1, "pattern": "^[A-Za-z0-9+=@_\\/\\s-]+$"}, "ConfigurationParametersMapValueString": {"type": "string", "max": 40960, "min": 0}, "ConfigurationSummary": {"type": "structure", "members": {"Account": {"shape": "String", "documentation": "<p>The ID of the Amazon Web Services account where the configuration was deployed.</p>"}, "ConfigurationDefinitionId": {"shape": "String", "documentation": "<p>The ID of the configuration definition.</p>"}, "CreatedAt": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The datetime stamp when the configuration was created.</p>"}, "FirstClassParameters": {"shape": "ConfigurationParametersMap", "documentation": "<p>The common parameters and values for the configuration definition.</p>"}, "Id": {"shape": "String", "documentation": "<p>A service generated identifier for the configuration.</p>"}, "ManagerArn": {"shape": "String", "documentation": "<p>The ARN of the configuration manager.</p>"}, "Region": {"shape": "String", "documentation": "<p>The Amazon Web Services Region where the configuration was deployed.</p>"}, "StatusSummaries": {"shape": "StatusSummariesList", "documentation": "<p>A summary of the state of the configuration manager. This includes deployment statuses, association statuses, drift statuses, health checks, and more.</p>"}, "Type": {"shape": "String", "documentation": "<p>The type of the Quick Setup configuration.</p>"}, "TypeVersion": {"shape": "String", "documentation": "<p>The version of the Quick Setup type used.</p>"}}, "documentation": "<p>Details for a Quick Setup configuration.</p>"}, "ConfigurationsList": {"type": "list", "member": {"shape": "ConfigurationSummary"}}, "ConflictException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>Another request is being processed. Wait a few minutes and try again.</p>", "error": {"httpStatusCode": 409, "senderFault": true}, "exception": true}, "CreateConfigurationManagerInput": {"type": "structure", "required": ["ConfigurationDefinitions"], "members": {"ConfigurationDefinitions": {"shape": "ConfigurationDefinitionsInputList", "documentation": "<p>The definition of the Quick Setup configuration that the configuration manager deploys.</p>"}, "Description": {"shape": "CreateConfigurationManagerInputDescriptionString", "documentation": "<p>A description of the configuration manager.</p>"}, "Name": {"shape": "CreateConfigurationManagerInputNameString", "documentation": "<p>A name for the configuration manager.</p>"}, "Tags": {"shape": "TagsMap", "documentation": "<p>Key-value pairs of metadata to assign to the configuration manager.</p>"}}}, "CreateConfigurationManagerInputDescriptionString": {"type": "string", "pattern": "^.{0,512}$"}, "CreateConfigurationManagerInputNameString": {"type": "string", "pattern": "^[ A-Za-z0-9._-]{0,120}$"}, "CreateConfigurationManagerOutput": {"type": "structure", "required": ["ManagerArn"], "members": {"ManagerArn": {"shape": "String", "documentation": "<p>The ARN for the newly created configuration manager.</p>"}}}, "DeleteConfigurationManagerInput": {"type": "structure", "required": ["ManagerArn"], "members": {"ManagerArn": {"shape": "DeleteConfigurationManagerInputManagerArnString", "documentation": "<p>The ID of the configuration manager.</p>", "location": "uri", "locationName": "ManagerArn"}}}, "DeleteConfigurationManagerInputManagerArnString": {"type": "string", "pattern": "^arn:aws:ssm-quicksetup:([^:]+):(\\d{12}):configuration-manager/[0-9a-fA-F]{8}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{12}$"}, "Filter": {"type": "structure", "required": ["Key", "Values"], "members": {"Key": {"shape": "FilterKeyString", "documentation": "<p>The key for the filter.</p>"}, "Values": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The values for the filter keys.</p>"}}, "documentation": "<p>A key-value pair to filter results.</p>"}, "FilterKeyString": {"type": "string", "max": 128, "min": 0, "pattern": "^[A-Za-z0-9+=@_\\/\\s-]*$"}, "FilterValues": {"type": "list", "member": {"shape": "FilterValuesMemberString"}}, "FilterValuesMemberString": {"type": "string", "max": 256, "min": 0, "pattern": "^[A-Za-z0-9+=@_\\/\\s-]*$"}, "FiltersList": {"type": "list", "member": {"shape": "Filter"}}, "GetConfigurationInput": {"type": "structure", "required": ["ConfigurationId"], "members": {"ConfigurationId": {"shape": "GetConfigurationInputConfigurationIdString", "documentation": "<p>A service generated identifier for the configuration.</p>", "location": "uri", "locationName": "ConfigurationId"}}}, "GetConfigurationInputConfigurationIdString": {"type": "string", "pattern": "^[a-zA-Z0-9-_/:]{1,100}$"}, "GetConfigurationManagerInput": {"type": "structure", "required": ["ManagerArn"], "members": {"ManagerArn": {"shape": "GetConfigurationManagerInputManagerArnString", "documentation": "<p>The ARN of the configuration manager.</p>", "location": "uri", "locationName": "ManagerArn"}}}, "GetConfigurationManagerInputManagerArnString": {"type": "string", "min": 1, "pattern": "^arn:aws:ssm-quicksetup:([^:]+):(\\d{12}):configuration-manager/[0-9a-fA-F]{8}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{12}$"}, "GetConfigurationManagerOutput": {"type": "structure", "required": ["ManagerArn"], "members": {"ConfigurationDefinitions": {"shape": "ConfigurationDefinitionsList", "documentation": "<p>The configuration definitions association with the configuration manager.</p>"}, "CreatedAt": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The datetime stamp when the configuration manager was created.</p>"}, "Description": {"shape": "String", "documentation": "<p>The description of the configuration manager.</p>"}, "LastModifiedAt": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The datetime stamp when the configuration manager was last updated.</p>"}, "ManagerArn": {"shape": "String", "documentation": "<p>The ARN of the configuration manager.</p>"}, "Name": {"shape": "String", "documentation": "<p>The name of the configuration manager.</p>"}, "StatusSummaries": {"shape": "StatusSummariesList", "documentation": "<p>A summary of the state of the configuration manager. This includes deployment statuses, association statuses, drift statuses, health checks, and more.</p>"}, "Tags": {"shape": "TagsMap", "documentation": "<p>Key-value pairs of metadata to assign to the configuration manager.</p>"}}}, "GetConfigurationOutput": {"type": "structure", "members": {"Account": {"shape": "String", "documentation": "<p>The ID of the Amazon Web Services account where the configuration was deployed.</p>"}, "ConfigurationDefinitionId": {"shape": "String", "documentation": "<p>The ID of the configuration definition.</p>"}, "CreatedAt": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The datetime stamp when the configuration manager was created.</p>"}, "Id": {"shape": "String", "documentation": "<p>A service generated identifier for the configuration.</p>"}, "LastModifiedAt": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The datetime stamp when the configuration manager was last updated.</p>"}, "ManagerArn": {"shape": "String", "documentation": "<p>The ARN of the configuration manager.</p>"}, "Parameters": {"shape": "ConfigurationParametersMap", "documentation": "<p>The parameters for the configuration definition type.</p>"}, "Region": {"shape": "String", "documentation": "<p>The Amazon Web Services Region where the configuration was deployed.</p>"}, "StatusSummaries": {"shape": "StatusSummariesList", "documentation": "<p>A summary of the state of the configuration manager. This includes deployment statuses, association statuses, drift statuses, health checks, and more.</p>"}, "Type": {"shape": "String", "documentation": "<p>The type of the Quick Setup configuration.</p>"}, "TypeVersion": {"shape": "String", "documentation": "<p>The version of the Quick Setup type used.</p>"}}}, "GetServiceSettingsOutput": {"type": "structure", "members": {"ServiceSettings": {"shape": "ServiceSettings", "documentation": "<p>Returns details about the settings for Quick Setup in the requesting Amazon Web Services account and Amazon Web Services Region.</p>"}}}, "IAMRoleArn": {"type": "string"}, "InternalServerException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>An error occurred on the server side.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true, "retryable": {"throttling": false}}, "ListConfigurationManagersInput": {"type": "structure", "members": {"Filters": {"shape": "FiltersList", "documentation": "<p>Filters the results returned by the request.</p>"}, "MaxItems": {"shape": "ListConfigurationManagersInputMaxItemsInteger", "documentation": "<p>Specifies the maximum number of configuration managers that are returned by the request.</p>"}, "StartingToken": {"shape": "ListConfigurationManagersInputStartingTokenString", "documentation": "<p>The token to use when requesting a specific set of items from a list.</p>"}}}, "ListConfigurationManagersInputMaxItemsInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListConfigurationManagersInputStartingTokenString": {"type": "string", "max": 1024, "min": 0, "pattern": "^[A-Za-z0-9+=@_\\/\\s-]*$"}, "ListConfigurationManagersOutput": {"type": "structure", "members": {"ConfigurationManagersList": {"shape": "ConfigurationManagerList", "documentation": "<p>The configuration managers returned by the request.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The token to use when requesting the next set of configuration managers. If there are no additional operations to return, the string is empty.</p>"}}}, "ListConfigurationsInput": {"type": "structure", "members": {"ConfigurationDefinitionId": {"shape": "ListConfigurationsInputConfigurationDefinitionIdString", "documentation": "<p>The ID of the configuration definition.</p>"}, "Filters": {"shape": "FiltersList", "documentation": "<p>Filters the results returned by the request.</p>"}, "ManagerArn": {"shape": "ListConfigurationsInputManagerArnString", "documentation": "<p>The ARN of the configuration manager.</p>"}, "MaxItems": {"shape": "ListConfigurationsInputMaxItemsInteger", "documentation": "<p>Specifies the maximum number of configurations that are returned by the request.</p>"}, "StartingToken": {"shape": "ListConfigurationsInputStartingTokenString", "documentation": "<p>The token to use when requesting a specific set of items from a list.</p>"}}}, "ListConfigurationsInputConfigurationDefinitionIdString": {"type": "string", "pattern": "^[a-z0-9-]{1,20}$"}, "ListConfigurationsInputManagerArnString": {"type": "string", "pattern": "^arn:aws:ssm-quicksetup:([^:]+):(\\d{12}):configuration-manager/[0-9a-fA-F]{8}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{12}$"}, "ListConfigurationsInputMaxItemsInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListConfigurationsInputStartingTokenString": {"type": "string", "max": 1024, "min": 0, "pattern": "^[A-Za-z0-9+=@_|\\/\\s-]*$"}, "ListConfigurationsOutput": {"type": "structure", "members": {"ConfigurationsList": {"shape": "ConfigurationsList", "documentation": "<p>An array of configurations.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The token to use when requesting the next set of items. If there are no additional items to return, the string is empty.</p>"}}}, "ListQuickSetupTypesOutput": {"type": "structure", "members": {"QuickSetupTypeList": {"shape": "QuickSetupTypeList", "documentation": "<p>An array of Quick Setup types.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "String", "documentation": "<p>The ARN of the resource the tag is assigned to.</p>", "location": "uri", "locationName": "ResourceArn"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"Tags": {"shape": "Tags", "documentation": "<p>Key-value pairs of metadata assigned to the resource.</p>"}}}, "QuickSetupTypeList": {"type": "list", "member": {"shape": "QuickSetupTypeOutput"}}, "QuickSetupTypeOutput": {"type": "structure", "members": {"LatestVersion": {"shape": "String", "documentation": "<p>The latest version number of the configuration.</p>"}, "Type": {"shape": "String", "documentation": "<p>The type of the Quick Setup configuration.</p>"}}, "documentation": "<p>Information about the Quick Setup type.</p>"}, "ResourceNotFoundException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>The resource couldn't be found. Check the ID or name and try again.</p>", "error": {"httpStatusCode": 404, "senderFault": true}, "exception": true}, "ServiceSettings": {"type": "structure", "members": {"ExplorerEnablingRoleArn": {"shape": "IAMRoleArn", "documentation": "<p>The IAM role used to enable Explorer.</p>"}}, "documentation": "<p>Settings configured for Quick Setup.</p>"}, "Status": {"type": "string", "enum": ["INITIALIZING", "DEPLOYING", "SUCCEEDED", "DELETING", "STOPPING", "FAILED", "STOPPED", "DELETE_FAILED", "STOP_FAILED", "NONE"]}, "StatusDetails": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "String"}}, "StatusSummariesList": {"type": "list", "member": {"shape": "StatusSummary"}}, "StatusSummary": {"type": "structure", "required": ["LastUpdatedAt", "StatusType"], "members": {"LastUpdatedAt": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The datetime stamp when the status was last updated.</p>"}, "Status": {"shape": "Status", "documentation": "<p>The current status.</p>"}, "StatusDetails": {"shape": "StatusDetails", "documentation": "<p>Details about the status.</p>"}, "StatusMessage": {"shape": "String", "documentation": "<p>When applicable, returns an informational message relevant to the current status and status type of the status summary object. We don't recommend implementing parsing logic around this value since the messages returned can vary in format.</p>"}, "StatusType": {"shape": "StatusType", "documentation": "<p>The type of a status summary.</p>"}}, "documentation": "<p>A summarized description of the status.</p>"}, "StatusType": {"type": "string", "enum": ["Deployment", "AsyncExecutions"]}, "String": {"type": "string"}, "SyntheticTimestamp_date_time": {"type": "timestamp", "timestampFormat": "iso8601"}, "TagEntry": {"type": "structure", "members": {"Key": {"shape": "TagEntryKeyString", "documentation": "<p>The key for the tag.</p>"}, "Value": {"shape": "TagEntryValueString", "documentation": "<p>The value for the tag.</p>"}}, "documentation": "<p>Key-value pairs of metadata.</p>", "sensitive": true}, "TagEntryKeyString": {"type": "string", "max": 128, "min": 1, "pattern": "^[A-Za-z0-9 _=@:.+-/]+$"}, "TagEntryValueString": {"type": "string", "max": 256, "min": 0, "pattern": "^[A-Za-z0-9 _=@:.+-/]+$"}, "TagKeys": {"type": "list", "member": {"shape": "String"}}, "TagResourceInput": {"type": "structure", "required": ["ResourceArn", "Tags"], "members": {"ResourceArn": {"shape": "String", "documentation": "<p>The ARN of the resource to tag.</p>", "location": "uri", "locationName": "ResourceArn"}, "Tags": {"shape": "TagsMap", "documentation": "<p>Key-value pairs of metadata to assign to the resource.</p>"}}}, "Tags": {"type": "list", "member": {"shape": "TagEntry"}, "sensitive": true}, "TagsMap": {"type": "map", "key": {"shape": "TagsMapKeyString"}, "value": {"shape": "TagsMapValueString"}, "sensitive": true}, "TagsMapKeyString": {"type": "string", "max": 128, "min": 1, "pattern": "^[A-Za-z0-9 _=@:.+-/]+$"}, "TagsMapValueString": {"type": "string", "max": 256, "min": 0, "pattern": "^[A-Za-z0-9 _=@:.+-/]+$"}, "ThrottlingException": {"type": "structure", "required": ["Message"], "members": {"Message": {"shape": "String"}}, "documentation": "<p>The request or operation exceeds the maximum allowed request rate per Amazon Web Services account and Amazon Web Services Region.</p>", "error": {"httpStatusCode": 429, "senderFault": true}, "exception": true, "retryable": {"throttling": false}}, "UntagResourceInput": {"type": "structure", "required": ["ResourceArn", "TagKeys"], "members": {"ResourceArn": {"shape": "String", "documentation": "<p>The ARN of the resource to remove tags from.</p>", "location": "uri", "locationName": "ResourceArn"}, "TagKeys": {"shape": "TagKeys", "documentation": "<p>The keys of the tags to remove from the resource.</p>", "location": "querystring", "locationName": "tagKeys"}}}, "UpdateConfigurationDefinitionInput": {"type": "structure", "required": ["Id", "ManagerArn"], "members": {"Id": {"shape": "UpdateConfigurationDefinitionInputIdString", "documentation": "<p>The ID of the configuration definition you want to update.</p>", "location": "uri", "locationName": "Id"}, "LocalDeploymentAdministrationRoleArn": {"shape": "IAMRoleArn", "documentation": "<p>The ARN of the IAM role used to administrate local configuration deployments.</p>"}, "LocalDeploymentExecutionRoleName": {"shape": "UpdateConfigurationDefinitionInputLocalDeploymentExecutionRoleNameString", "documentation": "<p>The name of the IAM role used to deploy local configurations.</p>"}, "ManagerArn": {"shape": "UpdateConfigurationDefinitionInputManagerArnString", "documentation": "<p>The ARN of the configuration manager associated with the definition to update.</p>", "location": "uri", "locationName": "ManagerArn"}, "Parameters": {"shape": "ConfigurationParametersMap", "documentation": "<p>The parameters for the configuration definition type.</p>"}, "TypeVersion": {"shape": "UpdateConfigurationDefinitionInputTypeVersionString", "documentation": "<p>The version of the Quick Setup type to use.</p>"}}}, "UpdateConfigurationDefinitionInputIdString": {"type": "string", "pattern": "^[a-z0-9-]{1,20}$"}, "UpdateConfigurationDefinitionInputLocalDeploymentExecutionRoleNameString": {"type": "string", "pattern": "^[\\w+=,.@-]{1,64}$"}, "UpdateConfigurationDefinitionInputManagerArnString": {"type": "string", "pattern": "^arn:aws:ssm-quicksetup:([^:]+):(\\d{12}):configuration-manager/[0-9a-fA-F]{8}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{12}$"}, "UpdateConfigurationDefinitionInputTypeVersionString": {"type": "string", "pattern": "^\\d{1,3}(\\.\\d{1,3})?$|^LATEST$"}, "UpdateConfigurationManagerInput": {"type": "structure", "required": ["ManagerArn"], "members": {"Description": {"shape": "UpdateConfigurationManagerInputDescriptionString", "documentation": "<p>A description of the configuration manager.</p>"}, "ManagerArn": {"shape": "UpdateConfigurationManagerInputManagerArnString", "documentation": "<p>The ARN of the configuration manager.</p>", "location": "uri", "locationName": "ManagerArn"}, "Name": {"shape": "UpdateConfigurationManagerInputNameString", "documentation": "<p>A name for the configuration manager.</p>"}}}, "UpdateConfigurationManagerInputDescriptionString": {"type": "string", "pattern": "^.{0,512}$"}, "UpdateConfigurationManagerInputManagerArnString": {"type": "string", "pattern": "^arn:aws:ssm-quicksetup:([^:]+):(\\d{12}):configuration-manager/[0-9a-fA-F]{8}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{12}$"}, "UpdateConfigurationManagerInputNameString": {"type": "string", "pattern": "^[ A-Za-z0-9._-]{0,120}$"}, "UpdateServiceSettingsInput": {"type": "structure", "members": {"ExplorerEnablingRoleArn": {"shape": "IAMRoleArn", "documentation": "<p>The IAM role used to enable Explorer.</p>"}}}, "ValidationException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>The request is invalid. Verify the values provided for the request parameters are accurate.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}}, "documentation": "<p>Quick Setup helps you quickly configure frequently used services and features with recommended best practices. Quick Setup simplifies setting up services, including Systems Manager, by automating common or recommended tasks.</p>"}