{"version": "2.0", "metadata": {"apiVersion": "2023-01-25", "endpointPrefix": "deployment-marketplace", "jsonVersion": "1.1", "protocol": "rest-json", "protocols": ["rest-json"], "serviceFullName": "AWS Marketplace Deployment Service", "serviceId": "Marketplace Deployment", "signatureVersion": "v4", "signingName": "aws-marketplace", "uid": "marketplace-deployment-2023-01-25", "auth": ["aws.auth#sigv4"]}, "operations": {"ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists all tags that have been added to a deployment parameter resource.</p>"}, "PutDeploymentParameter": {"name": "PutDeploymentParameter", "http": {"method": "POST", "requestUri": "/catalogs/{catalog}/products/{productId}/deployment-parameters", "responseCode": 200}, "input": {"shape": "PutDeploymentParameterRequest"}, "output": {"shape": "PutDeploymentParameterResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates or updates a deployment parameter and is targeted by <code>catalog</code> and <code>agreementId</code>.</p>", "idempotent": true}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags/{resourceArn}", "responseCode": 204}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Tags a resource.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags/{resourceArn}", "responseCode": 204}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Removes a tag or list of tags from a resource.</p>", "idempotent": true}}, "shapes": {"AccessDeniedException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>You do not have sufficient access to perform this action.</p>", "error": {"httpStatusCode": 403, "senderFault": true}, "exception": true}, "Catalog": {"type": "string", "max": 64, "min": 1, "pattern": "^[a-zA-Z_-]+$"}, "ClientToken": {"type": "string", "max": 64, "min": 32, "pattern": "^[a-zA-Z0-9/_+=.:@-]+$"}, "ConflictException": {"type": "structure", "required": ["message", "resourceId"], "members": {"message": {"shape": "String"}, "resourceId": {"shape": "ResourceId", "documentation": "<p>The unique identifier for the resource associated with the error.</p>"}}, "documentation": "<p>The request configuration has conflicts. For details, see the accompanying error message.</p>", "error": {"httpStatusCode": 409, "senderFault": true}, "exception": true}, "DeploymentParameterInput": {"type": "structure", "required": ["name", "secretString"], "members": {"name": {"shape": "DeploymentParameterName", "documentation": "<p>The desired name of the deployment parameter. This is the identifier on which deployment parameters are keyed for a given buyer and product. If this name matches an existing deployment parameter, this request will update the existing resource.</p>"}, "secretString": {"shape": "SecretString", "documentation": "<p>The text to encrypt and store in the secret.</p>"}}, "documentation": "<p>The shape containing the requested deployment parameter name and secretString.</p> <note> <p>To support AWS CloudFormation dynamic references to this resource using Quick Launch, this value must match a parameter defined in the CloudFormation templated provided to buyers.</p> </note>"}, "DeploymentParameterName": {"type": "string", "max": 400, "min": 1, "pattern": "^[a-zA-Z0-9/_+=.@-]+$"}, "DeploymentParameterResourceIdentifier": {"type": "string", "max": 32, "min": 1, "pattern": "^dp-[a-zA-Z0-9]+$"}, "InternalServerException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>There was an internal service exception.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true, "retryable": {"throttling": false}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) associated with the deployment parameter resource you want to list tags on.</p>", "location": "uri", "locationName": "resourceArn"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"tags": {"shape": "Tags", "documentation": "<p>A map of key-value pairs, where each pair represents a tag present on the resource.</p>"}}}, "PutDeploymentParameterRequest": {"type": "structure", "required": ["agreementId", "catalog", "deploymentParameter", "productId"], "members": {"agreementId": {"shape": "ResourceId", "documentation": "<p>The unique identifier of the agreement.</p>"}, "catalog": {"shape": "Catalog", "documentation": "<p>The catalog related to the request. Fixed value: <code>AWSMarketplace</code> </p>", "location": "uri", "locationName": "catalog"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>The idempotency token for deployment parameters. A unique identifier for the new version.</p> <note> <p>This field is not required if you're calling using an AWS SDK. Otherwise, a <code>clientToken</code> must be provided with the request.</p> </note>", "idempotencyToken": true}, "deploymentParameter": {"shape": "DeploymentParameterInput", "documentation": "<p>The deployment parameter targeted to the acceptor of an agreement for which to create the AWS Secret Manager resource.</p>"}, "expirationDate": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The date when deployment parameters expire and are scheduled for deletion.</p>"}, "productId": {"shape": "ResourceId", "documentation": "<p>The product for which AWS Marketplace will save secrets for the buyer’s account.</p>", "location": "uri", "locationName": "productId"}, "tags": {"shape": "TagsMap", "documentation": "<p>A map of key-value pairs, where each pair represents a tag saved to the resource. Tags will only be applied for create operations, and they'll be ignored if the resource already exists.</p>"}}}, "PutDeploymentParameterResponse": {"type": "structure", "required": ["agreementId", "deploymentParameterId", "resourceArn"], "members": {"agreementId": {"shape": "ResourceId", "documentation": "<p>The unique identifier of the agreement.</p>"}, "deploymentParameterId": {"shape": "DeploymentParameterResourceIdentifier", "documentation": "<p>The unique identifier of the deployment parameter.</p>"}, "resourceArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) associated with the deployment parameter resource you want to create or update.</p>"}, "tags": {"shape": "TagsMap", "documentation": "<p>A map of key-value pairs, where each pair represents a tag saved to the resource. Tags will only be applied for create operations, and they'll be ignored if the resource already exists.</p>"}}}, "ResourceArn": {"type": "string", "max": 2048, "min": 1, "pattern": "^[a-zA-Z0-9:*/-]+$"}, "ResourceId": {"type": "string", "max": 64, "min": 1, "pattern": "^[A-Za-z0-9_/-]+$"}, "ResourceNotFoundException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The specified resource wasn't found.</p>", "error": {"httpStatusCode": 404, "senderFault": true}, "exception": true}, "SecretString": {"type": "string", "max": 15000, "min": 1, "sensitive": true}, "ServiceQuotaExceededException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The maximum number of requests per account has been exceeded.</p>", "error": {"httpStatusCode": 402, "senderFault": true}, "exception": true}, "String": {"type": "string"}, "StringList": {"type": "list", "member": {"shape": "String"}}, "SyntheticTimestamp_date_time": {"type": "timestamp", "timestampFormat": "iso8601"}, "TagKey": {"type": "string", "max": 128, "min": 1, "pattern": "^[a-zA-Z0-9/_+=.:@-]+$"}, "TagResourceRequest": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) associated with the resource you want to tag.</p>", "location": "uri", "locationName": "resourceArn"}, "tags": {"shape": "Tags", "documentation": "<p>A map of key-value pairs, where each pair represents a tag present on the resource.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 1, "pattern": "^[a-zA-Z0-9/_+=.:@-]+$"}, "Tags": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "String"}}, "TagsMap": {"type": "map", "key": {"shape": "TagKey"}, "value": {"shape": "TagValue"}, "max": 50, "min": 0}, "ThrottlingException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>Too many requests.</p>", "error": {"httpStatusCode": 429, "senderFault": true}, "exception": true, "retryable": {"throttling": true}}, "UntagResourceRequest": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) associated with the resource you want to remove the tag from.</p>", "location": "uri", "locationName": "resourceArn"}, "tagKeys": {"shape": "StringList", "documentation": "<p>A list of key names of tags to be removed.</p>", "location": "querystring", "locationName": "tagKeys"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "ValidationException": {"type": "structure", "required": ["fieldName", "message"], "members": {"fieldName": {"shape": "String", "documentation": "<p>The field name associated with the error.</p>"}, "message": {"shape": "String"}}, "documentation": "<p>An error occurred during validation.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}}, "documentation": "<p>The AWS Marketplace Deployment Service supports the Quick Launch experience, which is a deployment option for software as a service (SaaS) products. Quick Launch simplifies and reduces the time, resources, and steps required to configure, deploy, and launch a products. The AWS Marketplace Deployment Service provides sellers with a secure method for passing deployment parameters, such as API keys and external IDs, to buyers during the Quick Launch experience.</p>"}