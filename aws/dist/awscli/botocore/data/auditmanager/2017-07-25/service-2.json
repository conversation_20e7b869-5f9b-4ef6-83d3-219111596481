{"version": "2.0", "metadata": {"apiVersion": "2017-07-25", "endpointPrefix": "auditmanager", "jsonVersion": "1.1", "protocol": "rest-json", "protocols": ["rest-json"], "serviceFullName": "AWS Audit Manager", "serviceId": "AuditManager", "signatureVersion": "v4", "signingName": "auditmanager", "uid": "auditmanager-2017-07-25", "auth": ["aws.auth#sigv4"]}, "operations": {"AssociateAssessmentReportEvidenceFolder": {"name": "AssociateAssessmentReportEvidenceFolder", "http": {"method": "PUT", "requestUri": "/assessments/{assessmentId}/associateToAssessmentReport"}, "input": {"shape": "AssociateAssessmentReportEvidenceFolderRequest"}, "output": {"shape": "AssociateAssessmentReportEvidenceFolderResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p> Associates an evidence folder to an assessment report in an Audit Manager assessment. </p>"}, "BatchAssociateAssessmentReportEvidence": {"name": "BatchAssociateAssessmentReportEvidence", "http": {"method": "PUT", "requestUri": "/assessments/{assessmentId}/batchAssociateToAssessmentReport"}, "input": {"shape": "BatchAssociateAssessmentReportEvidenceRequest"}, "output": {"shape": "BatchAssociateAssessmentReportEvidenceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p> Associates a list of evidence to an assessment report in an Audit Manager assessment. </p>"}, "BatchCreateDelegationByAssessment": {"name": "BatchCreateDelegationByAssessment", "http": {"method": "POST", "requestUri": "/assessments/{assessmentId}/delegations"}, "input": {"shape": "BatchCreateDelegationByAssessmentRequest"}, "output": {"shape": "BatchCreateDelegationByAssessmentResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p> Creates a batch of delegations for an assessment in Audit Manager. </p>"}, "BatchDeleteDelegationByAssessment": {"name": "BatchDeleteDelegationByAssessment", "http": {"method": "PUT", "requestUri": "/assessments/{assessmentId}/delegations"}, "input": {"shape": "BatchDeleteDelegationByAssessmentRequest"}, "output": {"shape": "BatchDeleteDelegationByAssessmentResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p> Deletes a batch of delegations for an assessment in Audit Manager. </p>"}, "BatchDisassociateAssessmentReportEvidence": {"name": "BatchDisassociateAssessmentReportEvidence", "http": {"method": "PUT", "requestUri": "/assessments/{assessmentId}/batchDisassociateFromAssessmentReport"}, "input": {"shape": "BatchDisassociateAssessmentReportEvidenceRequest"}, "output": {"shape": "BatchDisassociateAssessmentReportEvidenceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p> Disassociates a list of evidence from an assessment report in Audit Manager. </p>"}, "BatchImportEvidenceToAssessmentControl": {"name": "BatchImportEvidenceToAssessmentControl", "http": {"method": "POST", "requestUri": "/assessments/{assessmentId}/controlSets/{controlSetId}/controls/{controlId}/evidence"}, "input": {"shape": "BatchImportEvidenceToAssessmentControlRequest"}, "output": {"shape": "BatchImportEvidenceToAssessmentControlResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Adds one or more pieces of evidence to a control in an Audit Manager assessment. </p> <p>You can import manual evidence from any S3 bucket by specifying the S3 URI of the object. You can also upload a file from your browser, or enter plain text in response to a risk assessment question. </p> <p>The following restrictions apply to this action:</p> <ul> <li> <p> <code>manualEvidence</code> can be only one of the following: <code>evidenceFileName</code>, <code>s3ResourcePath</code>, or <code>textResponse</code> </p> </li> <li> <p>Maximum size of an individual evidence file: 100 MB</p> </li> <li> <p>Number of daily manual evidence uploads per control: 100</p> </li> <li> <p>Supported file formats: See <a href=\"https://docs.aws.amazon.com/audit-manager/latest/userguide/upload-evidence.html#supported-manual-evidence-files\">Supported file types for manual evidence</a> in the <i>Audit Manager User Guide</i> </p> </li> </ul> <p>For more information about Audit Manager service restrictions, see <a href=\"https://docs.aws.amazon.com/audit-manager/latest/userguide/service-quotas.html\">Quotas and restrictions for Audit Manager</a>.</p>"}, "CreateAssessment": {"name": "CreateAssessment", "http": {"method": "POST", "requestUri": "/assessments"}, "input": {"shape": "CreateAssessmentRequest"}, "output": {"shape": "CreateAssessmentResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}], "documentation": "<p> Creates an assessment in Audit Manager. </p>"}, "CreateAssessmentFramework": {"name": "CreateAssessmentFramework", "http": {"method": "POST", "requestUri": "/assessmentFrameworks"}, "input": {"shape": "CreateAssessmentFrameworkRequest"}, "output": {"shape": "CreateAssessmentFrameworkResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p> Creates a custom framework in Audit Manager. </p>"}, "CreateAssessmentReport": {"name": "CreateAssessmentReport", "http": {"method": "POST", "requestUri": "/assessments/{assessmentId}/reports"}, "input": {"shape": "CreateAssessmentReportRequest"}, "output": {"shape": "CreateAssessmentReportResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p> Creates an assessment report for the specified assessment. </p>"}, "CreateControl": {"name": "CreateControl", "http": {"method": "POST", "requestUri": "/controls"}, "input": {"shape": "CreateControlRequest"}, "output": {"shape": "CreateControlResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p> Creates a new custom control in Audit Manager. </p>"}, "DeleteAssessment": {"name": "DeleteAssessment", "http": {"method": "DELETE", "requestUri": "/assessments/{assessmentId}"}, "input": {"shape": "DeleteAssessmentRequest"}, "output": {"shape": "DeleteAssessmentResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p> Deletes an assessment in Audit Manager. </p>"}, "DeleteAssessmentFramework": {"name": "DeleteAssessmentFramework", "http": {"method": "DELETE", "requestUri": "/assessmentFrameworks/{frameworkId}"}, "input": {"shape": "DeleteAssessmentFrameworkRequest"}, "output": {"shape": "DeleteAssessmentFrameworkResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p> Deletes a custom framework in Audit Manager. </p>"}, "DeleteAssessmentFrameworkShare": {"name": "DeleteAssessmentFrameworkShare", "http": {"method": "DELETE", "requestUri": "/assessmentFrameworkShareRequests/{requestId}"}, "input": {"shape": "DeleteAssessmentFrameworkShareRequest"}, "output": {"shape": "DeleteAssessmentFrameworkShareResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p> Deletes a share request for a custom framework in Audit Manager. </p>"}, "DeleteAssessmentReport": {"name": "DeleteAssessmentReport", "http": {"method": "DELETE", "requestUri": "/assessments/{assessmentId}/reports/{assessmentReportId}"}, "input": {"shape": "DeleteAssessmentReportRequest"}, "output": {"shape": "DeleteAssessmentReportResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Deletes an assessment report in Audit Manager. </p> <p>When you run the <code>DeleteAssessmentReport</code> operation, Audit Manager attempts to delete the following data:</p> <ol> <li> <p>The specified assessment report that’s stored in your S3 bucket</p> </li> <li> <p>The associated metadata that’s stored in Audit Manager</p> </li> </ol> <p>If Audit Manager can’t access the assessment report in your S3 bucket, the report isn’t deleted. In this event, the <code>DeleteAssessmentReport</code> operation doesn’t fail. Instead, it proceeds to delete the associated metadata only. You must then delete the assessment report from the S3 bucket yourself. </p> <p>This scenario happens when Audit Manager receives a <code>403 (Forbidden)</code> or <code>404 (Not Found)</code> error from Amazon S3. To avoid this, make sure that your S3 bucket is available, and that you configured the correct permissions for Audit Manager to delete resources in your S3 bucket. For an example permissions policy that you can use, see <a href=\"https://docs.aws.amazon.com/audit-manager/latest/userguide/security_iam_id-based-policy-examples.html#full-administrator-access-assessment-report-destination\">Assessment report destination permissions</a> in the <i>Audit Manager User Guide</i>. For information about the issues that could cause a <code>403 (Forbidden)</code> or <code>404 (Not Found</code>) error from Amazon S3, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/ErrorResponses.html#ErrorCodeList\">List of Error Codes</a> in the <i>Amazon Simple Storage Service API Reference</i>. </p>"}, "DeleteControl": {"name": "DeleteControl", "http": {"method": "DELETE", "requestUri": "/controls/{controlId}"}, "input": {"shape": "DeleteControlRequest"}, "output": {"shape": "DeleteControlResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p> Deletes a custom control in Audit Manager. </p> <important> <p>When you invoke this operation, the custom control is deleted from any frameworks or assessments that it’s currently part of. As a result, Audit Manager will stop collecting evidence for that custom control in all of your assessments. This includes assessments that you previously created before you deleted the custom control.</p> </important>"}, "DeregisterAccount": {"name": "DeregisterAccount", "http": {"method": "POST", "requestUri": "/account/deregisterAccount"}, "input": {"shape": "DeregisterAccountRequest"}, "output": {"shape": "DeregisterAccountResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p> Deregisters an account in Audit Manager. </p> <note> <p>Before you deregister, you can use the <a href=\"https://docs.aws.amazon.com/audit-manager/latest/APIReference/API_UpdateSettings.html\">UpdateSettings</a> API operation to set your preferred data retention policy. By default, Audit Manager retains your data. If you want to delete your data, you can use the <code>DeregistrationPolicy</code> attribute to request the deletion of your data. </p> <p>For more information about data retention, see <a href=\"https://docs.aws.amazon.com/audit-manager/latest/userguide/data-protection.html\">Data Protection</a> in the <i>Audit Manager User Guide</i>. </p> </note>"}, "DeregisterOrganizationAdminAccount": {"name": "DeregisterOrganizationAdminAccount", "http": {"method": "POST", "requestUri": "/account/deregisterOrganizationAdminAccount"}, "input": {"shape": "DeregisterOrganizationAdminAccountRequest"}, "output": {"shape": "DeregisterOrganizationAdminAccountResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Removes the specified Amazon Web Services account as a delegated administrator for Audit Manager. </p> <p>When you remove a delegated administrator from your Audit Manager settings, you continue to have access to the evidence that you previously collected under that account. This is also the case when you deregister a delegated administrator from Organizations. However, Audit Manager stops collecting and attaching evidence to that delegated administrator account moving forward.</p> <important> <p>Keep in mind the following cleanup task if you use evidence finder:</p> <p>Before you use your management account to remove a delegated administrator, make sure that the current delegated administrator account signs in to Audit Manager and disables evidence finder first. Disabling evidence finder automatically deletes the event data store that was created in their account when they enabled evidence finder. If this task isn’t completed, the event data store remains in their account. In this case, we recommend that the original delegated administrator goes to CloudTrail Lake and manually <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/query-eds-disable-termination.html\">deletes the event data store</a>.</p> <p>This cleanup task is necessary to ensure that you don't end up with multiple event data stores. Audit Manager ignores an unused event data store after you remove or change a delegated administrator account. However, the unused event data store continues to incur storage costs from CloudTrail Lake if you don't delete it.</p> </important> <p>When you deregister a delegated administrator account for Audit Manager, the data for that account isn’t deleted. If you want to delete resource data for a delegated administrator account, you must perform that task separately before you deregister the account. Either, you can do this in the Audit Manager console. Or, you can use one of the delete API operations that are provided by Audit Manager. </p> <p>To delete your Audit Manager resource data, see the following instructions: </p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/audit-manager/latest/APIReference/API_DeleteAssessment.html\">DeleteAssessment</a> (see also: <a href=\"https://docs.aws.amazon.com/audit-manager/latest/userguide/delete-assessment.html\">Deleting an assessment</a> in the <i>Audit Manager User Guide</i>)</p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/audit-manager/latest/APIReference/API_DeleteAssessmentFramework.html\">DeleteAssessmentFramework</a> (see also: <a href=\"https://docs.aws.amazon.com/audit-manager/latest/userguide/delete-custom-framework.html\">Deleting a custom framework</a> in the <i>Audit Manager User Guide</i>)</p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/audit-manager/latest/APIReference/API_DeleteAssessmentFrameworkShare.html\">DeleteAssessmentFrameworkShare</a> (see also: <a href=\"https://docs.aws.amazon.com/audit-manager/latest/userguide/deleting-shared-framework-requests.html\">Deleting a share request</a> in the <i>Audit Manager User Guide</i>)</p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/audit-manager/latest/APIReference/API_DeleteAssessmentReport.html\">DeleteAssessmentReport</a> (see also: <a href=\"https://docs.aws.amazon.com/audit-manager/latest/userguide/generate-assessment-report.html#delete-assessment-report-steps\">Deleting an assessment report</a> in the <i>Audit Manager User Guide</i>)</p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/audit-manager/latest/APIReference/API_DeleteControl.html\">DeleteControl</a> (see also: <a href=\"https://docs.aws.amazon.com/audit-manager/latest/userguide/delete-controls.html\">Deleting a custom control</a> in the <i>Audit Manager User Guide</i>)</p> </li> </ul> <p>At this time, Audit Manager doesn't provide an option to delete evidence for a specific delegated administrator. Instead, when your management account deregisters Audit Manager, we perform a cleanup for the current delegated administrator account at the time of deregistration.</p>"}, "DisassociateAssessmentReportEvidenceFolder": {"name": "DisassociateAssessmentReportEvidenceFolder", "http": {"method": "PUT", "requestUri": "/assessments/{assessmentId}/disassociateFromAssessmentReport"}, "input": {"shape": "DisassociateAssessmentReportEvidenceFolderRequest"}, "output": {"shape": "DisassociateAssessmentReportEvidenceFolderResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p> Disassociates an evidence folder from the specified assessment report in Audit Manager. </p>"}, "GetAccountStatus": {"name": "GetAccountStatus", "http": {"method": "GET", "requestUri": "/account/status"}, "input": {"shape": "GetAccountStatusRequest"}, "output": {"shape": "GetAccountStatusResponse"}, "errors": [{"shape": "InternalServerException"}], "documentation": "<p> Gets the registration status of an account in Audit Manager. </p>"}, "GetAssessment": {"name": "GetAssessment", "http": {"method": "GET", "requestUri": "/assessments/{assessmentId}"}, "input": {"shape": "GetAssessmentRequest"}, "output": {"shape": "GetAssessmentResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets information about a specified assessment. </p>"}, "GetAssessmentFramework": {"name": "GetAssessmentFramework", "http": {"method": "GET", "requestUri": "/assessmentFrameworks/{frameworkId}"}, "input": {"shape": "GetAssessmentFrameworkRequest"}, "output": {"shape": "GetAssessmentFrameworkResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets information about a specified framework.</p>"}, "GetAssessmentReportUrl": {"name": "GetAssessmentReportUrl", "http": {"method": "GET", "requestUri": "/assessments/{assessmentId}/reports/{assessmentReportId}/url"}, "input": {"shape": "GetAssessmentReportUrlRequest"}, "output": {"shape": "GetAssessmentReportUrlResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p> Gets the URL of an assessment report in Audit Manager. </p>"}, "GetChangeLogs": {"name": "GetChangeLogs", "http": {"method": "GET", "requestUri": "/assessments/{assessmentId}/changelogs"}, "input": {"shape": "GetChangeLogsRequest"}, "output": {"shape": "GetChangeLogsResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p> Gets a list of changelogs from Audit Manager. </p>"}, "GetControl": {"name": "GetControl", "http": {"method": "GET", "requestUri": "/controls/{controlId}"}, "input": {"shape": "GetControlRequest"}, "output": {"shape": "GetControlResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p> Gets information about a specified control.</p>"}, "GetDelegations": {"name": "GetDelegations", "http": {"method": "GET", "requestUri": "/delegations"}, "input": {"shape": "GetDelegationsRequest"}, "output": {"shape": "GetDelegationsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p> Gets a list of delegations from an audit owner to a delegate. </p>"}, "GetEvidence": {"name": "GetEvidence", "http": {"method": "GET", "requestUri": "/assessments/{assessmentId}/controlSets/{controlSetId}/evidenceFolders/{evidenceFolderId}/evidence/{evidenceId}"}, "input": {"shape": "GetEvidenceRequest"}, "output": {"shape": "GetEvidenceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p> Gets information about a specified evidence item.</p>"}, "GetEvidenceByEvidenceFolder": {"name": "GetEvidenceByEvidenceFolder", "http": {"method": "GET", "requestUri": "/assessments/{assessmentId}/controlSets/{controlSetId}/evidenceFolders/{evidenceFolderId}/evidence"}, "input": {"shape": "GetEvidenceByEvidenceFolderRequest"}, "output": {"shape": "GetEvidenceByEvidenceFolderResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p> Gets all evidence from a specified evidence folder in Audit Manager. </p>"}, "GetEvidenceFileUploadUrl": {"name": "GetEvidenceFileUploadUrl", "http": {"method": "GET", "requestUri": "/evidenceFileUploadUrl"}, "input": {"shape": "GetEvidenceFileUploadUrlRequest"}, "output": {"shape": "GetEvidenceFileUploadUrlResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Creates a presigned Amazon S3 URL that can be used to upload a file as manual evidence. For instructions on how to use this operation, see <a href=\"https://docs.aws.amazon.com/audit-manager/latest/userguide/upload-evidence.html#how-to-upload-manual-evidence-files\">Upload a file from your browser </a> in the <i>Audit Manager User Guide</i>.</p> <p>The following restrictions apply to this operation:</p> <ul> <li> <p>Maximum size of an individual evidence file: 100 MB</p> </li> <li> <p>Number of daily manual evidence uploads per control: 100</p> </li> <li> <p>Supported file formats: See <a href=\"https://docs.aws.amazon.com/audit-manager/latest/userguide/upload-evidence.html#supported-manual-evidence-files\">Supported file types for manual evidence</a> in the <i>Audit Manager User Guide</i> </p> </li> </ul> <p>For more information about Audit Manager service restrictions, see <a href=\"https://docs.aws.amazon.com/audit-manager/latest/userguide/service-quotas.html\">Quotas and restrictions for Audit Manager</a>.</p>"}, "GetEvidenceFolder": {"name": "GetEvidenceFolder", "http": {"method": "GET", "requestUri": "/assessments/{assessmentId}/controlSets/{controlSetId}/evidenceFolders/{evidenceFolderId}"}, "input": {"shape": "GetEvidenceFolderRequest"}, "output": {"shape": "GetEvidenceFolderResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p> Gets an evidence folder from a specified assessment in Audit Manager. </p>"}, "GetEvidenceFoldersByAssessment": {"name": "GetEvidenceFoldersByAssessment", "http": {"method": "GET", "requestUri": "/assessments/{assessmentId}/evidenceFolders"}, "input": {"shape": "GetEvidenceFoldersByAssessmentRequest"}, "output": {"shape": "GetEvidenceFoldersByAssessmentResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p> Gets the evidence folders from a specified assessment in Audit Manager. </p>"}, "GetEvidenceFoldersByAssessmentControl": {"name": "GetEvidenceFoldersByAssessmentControl", "http": {"method": "GET", "requestUri": "/assessments/{assessmentId}/evidenceFolders-by-assessment-control/{controlSetId}/{controlId}"}, "input": {"shape": "GetEvidenceFoldersByAssessmentControlRequest"}, "output": {"shape": "GetEvidenceFoldersByAssessmentControlResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p> Gets a list of evidence folders that are associated with a specified control in an Audit Manager assessment. </p>"}, "GetInsights": {"name": "GetInsights", "http": {"method": "GET", "requestUri": "/insights"}, "input": {"shape": "GetInsightsRequest"}, "output": {"shape": "GetInsightsResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets the latest analytics data for all your current active assessments. </p>"}, "GetInsightsByAssessment": {"name": "GetInsightsByAssessment", "http": {"method": "GET", "requestUri": "/insights/assessments/{assessmentId}"}, "input": {"shape": "GetInsightsByAssessmentRequest"}, "output": {"shape": "GetInsightsByAssessmentResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets the latest analytics data for a specific active assessment. </p>"}, "GetOrganizationAdminAccount": {"name": "GetOrganizationAdminAccount", "http": {"method": "GET", "requestUri": "/account/organizationAdminAccount"}, "input": {"shape": "GetOrganizationAdminAccountRequest"}, "output": {"shape": "GetOrganizationAdminAccountResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p> Gets the name of the delegated Amazon Web Services administrator account for a specified organization. </p>"}, "GetServicesInScope": {"name": "GetServicesInScope", "http": {"method": "GET", "requestUri": "/services"}, "input": {"shape": "GetServicesInScopeRequest"}, "output": {"shape": "GetServicesInScopeResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets a list of the Amazon Web Services services from which Audit Manager can collect evidence. </p> <p>Audit Manager defines which Amazon Web Services services are in scope for an assessment. Audit Manager infers this scope by examining the assessment’s controls and their data sources, and then mapping this information to one or more of the corresponding Amazon Web Services services that are in this list.</p> <note> <p>For information about why it's no longer possible to specify services in scope manually, see <a href=\"https://docs.aws.amazon.com/audit-manager/latest/userguide/evidence-collection-issues.html#unable-to-edit-services\">I can't edit the services in scope for my assessment</a> in the <i>Troubleshooting</i> section of the Audit Manager user guide.</p> </note>"}, "GetSettings": {"name": "GetSettings", "http": {"method": "GET", "requestUri": "/settings/{attribute}"}, "input": {"shape": "GetSettingsRequest"}, "output": {"shape": "GetSettingsResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p> Gets the settings for a specified Amazon Web Services account. </p>"}, "ListAssessmentControlInsightsByControlDomain": {"name": "ListAssessmentControlInsightsByControlDomain", "http": {"method": "GET", "requestUri": "/insights/controls-by-assessment"}, "input": {"shape": "ListAssessmentControlInsightsByControlDomainRequest"}, "output": {"shape": "ListAssessmentControlInsightsByControlDomainResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists the latest analytics data for controls within a specific control domain and a specific active assessment.</p> <note> <p>Control insights are listed only if the control belongs to the control domain and assessment that was specified. Moreover, the control must have collected evidence on the <code>lastUpdated</code> date of <code>controlInsightsByAssessment</code>. If neither of these conditions are met, no data is listed for that control. </p> </note>"}, "ListAssessmentFrameworkShareRequests": {"name": "ListAssessmentFrameworkShareRequests", "http": {"method": "GET", "requestUri": "/assessmentFrameworkShareRequests"}, "input": {"shape": "ListAssessmentFrameworkShareRequestsRequest"}, "output": {"shape": "ListAssessmentFrameworkShareRequestsResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p> Returns a list of sent or received share requests for custom frameworks in Audit Manager. </p>"}, "ListAssessmentFrameworks": {"name": "ListAssessmentFrameworks", "http": {"method": "GET", "requestUri": "/assessmentFrameworks"}, "input": {"shape": "ListAssessmentFrameworksRequest"}, "output": {"shape": "ListAssessmentFrameworksResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p> Returns a list of the frameworks that are available in the Audit Manager framework library. </p>"}, "ListAssessmentReports": {"name": "ListAssessmentReports", "http": {"method": "GET", "requestUri": "/assessmentReports"}, "input": {"shape": "ListAssessmentReportsRequest"}, "output": {"shape": "ListAssessmentReportsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p> Returns a list of assessment reports created in Audit Manager. </p>"}, "ListAssessments": {"name": "ListAssessments", "http": {"method": "GET", "requestUri": "/assessments"}, "input": {"shape": "ListAssessmentsRequest"}, "output": {"shape": "ListAssessmentsResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p> Returns a list of current and past assessments from Audit Manager. </p>"}, "ListControlDomainInsights": {"name": "ListControlDomainInsights", "http": {"method": "GET", "requestUri": "/insights/control-domains"}, "input": {"shape": "ListControlDomainInsightsRequest"}, "output": {"shape": "ListControlDomainInsightsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists the latest analytics data for control domains across all of your active assessments. </p> <p>Audit Manager supports the control domains that are provided by Amazon Web Services Control Catalog. For information about how to find a list of available control domains, see <a href=\"https://docs.aws.amazon.com/controlcatalog/latest/APIReference/API_ListDomains.html\"> <code>ListDomains</code> </a> in the Amazon Web Services Control Catalog API Reference.</p> <note> <p>A control domain is listed only if at least one of the controls within that domain collected evidence on the <code>lastUpdated</code> date of <code>controlDomainInsights</code>. If this condition isn’t met, no data is listed for that control domain.</p> </note>"}, "ListControlDomainInsightsByAssessment": {"name": "ListControlDomainInsightsByAssessment", "http": {"method": "GET", "requestUri": "/insights/control-domains-by-assessment"}, "input": {"shape": "ListControlDomainInsightsByAssessmentRequest"}, "output": {"shape": "ListControlDomainInsightsByAssessmentResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists analytics data for control domains within a specified active assessment.</p> <p>Audit Manager supports the control domains that are provided by Amazon Web Services Control Catalog. For information about how to find a list of available control domains, see <a href=\"https://docs.aws.amazon.com/controlcatalog/latest/APIReference/API_ListDomains.html\"> <code>ListDomains</code> </a> in the Amazon Web Services Control Catalog API Reference.</p> <note> <p>A control domain is listed only if at least one of the controls within that domain collected evidence on the <code>lastUpdated</code> date of <code>controlDomainInsights</code>. If this condition isn’t met, no data is listed for that domain.</p> </note>"}, "ListControlInsightsByControlDomain": {"name": "ListControlInsightsByControlDomain", "http": {"method": "GET", "requestUri": "/insights/controls"}, "input": {"shape": "ListControlInsightsByControlDomainRequest"}, "output": {"shape": "ListControlInsightsByControlDomainResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists the latest analytics data for controls within a specific control domain across all active assessments.</p> <note> <p>Control insights are listed only if the control belongs to the control domain that was specified and the control collected evidence on the <code>lastUpdated</code> date of <code>controlInsightsMetadata</code>. If neither of these conditions are met, no data is listed for that control. </p> </note>"}, "ListControls": {"name": "ListControls", "http": {"method": "GET", "requestUri": "/controls"}, "input": {"shape": "ListControlsRequest"}, "output": {"shape": "ListControlsResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p> Returns a list of controls from Audit Manager. </p>"}, "ListKeywordsForDataSource": {"name": "ListKeywordsForDataSource", "http": {"method": "GET", "requestUri": "/dataSourceKeywords"}, "input": {"shape": "ListKeywordsForDataSourceRequest"}, "output": {"shape": "ListKeywordsForDataSourceResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Returns a list of keywords that are pre-mapped to the specified control data source.</p>"}, "ListNotifications": {"name": "ListNotifications", "http": {"method": "GET", "requestUri": "/notifications"}, "input": {"shape": "ListNotificationsRequest"}, "output": {"shape": "ListNotificationsResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p> Returns a list of all Audit Manager notifications. </p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags/{resourceArn}"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p> Returns a list of tags for the specified resource in Audit Manager. </p>"}, "RegisterAccount": {"name": "RegisterAccount", "http": {"method": "POST", "requestUri": "/account/registerAccount"}, "input": {"shape": "RegisterAccountRequest"}, "output": {"shape": "RegisterAccountResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p> Enables Audit Manager for the specified Amazon Web Services account. </p>"}, "RegisterOrganizationAdminAccount": {"name": "RegisterOrganizationAdminAccount", "http": {"method": "POST", "requestUri": "/account/registerOrganizationAdminAccount"}, "input": {"shape": "RegisterOrganizationAdminAccountRequest"}, "output": {"shape": "RegisterOrganizationAdminAccountResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p> Enables an Amazon Web Services account within the organization as the delegated administrator for Audit Manager. </p>"}, "StartAssessmentFrameworkShare": {"name": "StartAssessmentFrameworkShare", "http": {"method": "POST", "requestUri": "/assessmentFrameworks/{frameworkId}/shareRequests"}, "input": {"shape": "StartAssessmentFrameworkShareRequest"}, "output": {"shape": "StartAssessmentFrameworkShareResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p> Creates a share request for a custom framework in Audit Manager. </p> <p>The share request specifies a recipient and notifies them that a custom framework is available. Recipients have 120 days to accept or decline the request. If no action is taken, the share request expires.</p> <p>When you create a share request, Audit Manager stores a snapshot of your custom framework in the US East (N. Virginia) Amazon Web Services Region. Audit Manager also stores a backup of the same snapshot in the US West (Oregon) Amazon Web Services Region.</p> <p>Audit Manager deletes the snapshot and the backup snapshot when one of the following events occurs:</p> <ul> <li> <p>The sender revokes the share request.</p> </li> <li> <p>The recipient declines the share request.</p> </li> <li> <p>The recipient encounters an error and doesn't successfully accept the share request.</p> </li> <li> <p>The share request expires before the recipient responds to the request.</p> </li> </ul> <p>When a sender <a href=\"https://docs.aws.amazon.com/audit-manager/latest/userguide/framework-sharing.html#framework-sharing-resend\">resends a share request</a>, the snapshot is replaced with an updated version that corresponds with the latest version of the custom framework. </p> <p>When a recipient accepts a share request, the snapshot is replicated into their Amazon Web Services account under the Amazon Web Services Region that was specified in the share request. </p> <important> <p>When you invoke the <code>StartAssessmentFrameworkShare</code> API, you are about to share a custom framework with another Amazon Web Services account. You may not share a custom framework that is derived from a standard framework if the standard framework is designated as not eligible for sharing by Amazon Web Services, unless you have obtained permission to do so from the owner of the standard framework. To learn more about which standard frameworks are eligible for sharing, see <a href=\"https://docs.aws.amazon.com/audit-manager/latest/userguide/share-custom-framework-concepts-and-terminology.html#eligibility\">Framework sharing eligibility</a> in the <i>Audit Manager User Guide</i>.</p> </important>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags/{resourceArn}"}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p> Tags the specified resource in Audit Manager. </p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags/{resourceArn}"}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p> Removes a tag from a resource in Audit Manager. </p>"}, "UpdateAssessment": {"name": "UpdateAssessment", "http": {"method": "PUT", "requestUri": "/assessments/{assessmentId}"}, "input": {"shape": "UpdateAssessmentRequest"}, "output": {"shape": "UpdateAssessmentResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p> Edits an Audit Manager assessment. </p>"}, "UpdateAssessmentControl": {"name": "UpdateAssessmentControl", "http": {"method": "PUT", "requestUri": "/assessments/{assessmentId}/controlSets/{controlSetId}/controls/{controlId}"}, "input": {"shape": "UpdateAssessmentControlRequest"}, "output": {"shape": "UpdateAssessmentControlResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p> Updates a control within an assessment in Audit Manager. </p>"}, "UpdateAssessmentControlSetStatus": {"name": "UpdateAssessmentControlSetStatus", "http": {"method": "PUT", "requestUri": "/assessments/{assessmentId}/controlSets/{controlSetId}/status"}, "input": {"shape": "UpdateAssessmentControlSetStatusRequest"}, "output": {"shape": "UpdateAssessmentControlSetStatusResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p> Updates the status of a control set in an Audit Manager assessment. </p>"}, "UpdateAssessmentFramework": {"name": "UpdateAssessmentFramework", "http": {"method": "PUT", "requestUri": "/assessmentFrameworks/{frameworkId}"}, "input": {"shape": "UpdateAssessmentFrameworkRequest"}, "output": {"shape": "UpdateAssessmentFrameworkResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p> Updates a custom framework in Audit Manager. </p>"}, "UpdateAssessmentFrameworkShare": {"name": "UpdateAssessmentFrameworkShare", "http": {"method": "PUT", "requestUri": "/assessmentFrameworkShareRequests/{requestId}"}, "input": {"shape": "UpdateAssessmentFrameworkShareRequest"}, "output": {"shape": "UpdateAssessmentFrameworkShareResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p> Updates a share request for a custom framework in Audit Manager. </p>"}, "UpdateAssessmentStatus": {"name": "UpdateAssessmentStatus", "http": {"method": "PUT", "requestUri": "/assessments/{assessmentId}/status"}, "input": {"shape": "UpdateAssessmentStatusRequest"}, "output": {"shape": "UpdateAssessmentStatusResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p> Updates the status of an assessment in Audit Manager. </p>"}, "UpdateControl": {"name": "UpdateControl", "http": {"method": "PUT", "requestUri": "/controls/{controlId}"}, "input": {"shape": "UpdateControlRequest"}, "output": {"shape": "UpdateControlResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p> Updates a custom control in Audit Manager. </p>"}, "UpdateSettings": {"name": "UpdateSettings", "http": {"method": "PUT", "requestUri": "/settings"}, "input": {"shape": "UpdateSettingsRequest"}, "output": {"shape": "UpdateSettingsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p> Updates Audit Manager settings for the current account. </p>"}, "ValidateAssessmentReportIntegrity": {"name": "ValidateAssessmentReportIntegrity", "http": {"method": "POST", "requestUri": "/assessmentReports/integrity"}, "input": {"shape": "ValidateAssessmentReportIntegrityRequest"}, "output": {"shape": "ValidateAssessmentReportIntegrityResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p> Validates the integrity of an assessment report in Audit Manager. </p>"}}, "shapes": {"AWSAccount": {"type": "structure", "members": {"id": {"shape": "AccountId", "documentation": "<p> The identifier for the Amazon Web Services account. </p>"}, "emailAddress": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p> The email address that's associated with the Amazon Web Services account. </p>"}, "name": {"shape": "Account<PERSON><PERSON>", "documentation": "<p> The name of the Amazon Web Services account. </p>"}}, "documentation": "<p> The wrapper of Amazon Web Services account details, such as account ID or email address. </p>"}, "AWSAccounts": {"type": "list", "member": {"shape": "AWSAccount"}, "max": 200, "min": 1, "sensitive": true}, "AWSService": {"type": "structure", "members": {"serviceName": {"shape": "AWSServiceName", "documentation": "<p> The name of the Amazon Web Services service. </p>"}}, "documentation": "<p> An Amazon Web Services service such as Amazon S3 or CloudTrail. </p> <p>For an example of how to find an Amazon Web Services service name and how to define it in your assessment scope, see the following:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/audit-manager/latest/APIReference/API_GetServicesInScope.html#API_GetServicesInScope_Example_2\">Finding an Amazon Web Services service name to use in your assessment scope</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/audit-manager/latest/APIReference/API_GetServicesInScope.html#API_GetServicesInScope_Example_3\">Defining an Amazon Web Services service name in your assessment scope</a> </p> </li> </ul>"}, "AWSServiceName": {"type": "string", "max": 40, "min": 1, "pattern": "^[a-zA-Z0-9-\\s().]+$"}, "AWSServices": {"type": "list", "member": {"shape": "AWSService"}}, "AccessDeniedException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p> Your account isn't registered with Audit Manager. Check the delegated administrator setup on the Audit Manager settings page, and try again. </p>", "error": {"httpStatusCode": 403}, "exception": true}, "AccountId": {"type": "string", "max": 12, "min": 12, "pattern": "^[0-9]{12}$"}, "AccountName": {"type": "string", "max": 50, "min": 1, "pattern": "^[\\u0020-\\u007E]+$"}, "AccountStatus": {"type": "string", "enum": ["ACTIVE", "INACTIVE", "PENDING_ACTIVATION"]}, "ActionEnum": {"type": "string", "enum": ["CREATE", "UPDATE_METADATA", "ACTIVE", "INACTIVE", "DELETE", "UNDER_REVIEW", "REVIEWED", "IMPORT_EVIDENCE"]}, "ActionPlanInstructions": {"type": "string", "max": 1000, "pattern": "^[\\w\\W\\s\\S]*$", "sensitive": true}, "ActionPlanTitle": {"type": "string", "max": 300, "pattern": "^[\\w\\W\\s\\S]*$", "sensitive": true}, "Assessment": {"type": "structure", "members": {"arn": {"shape": "AuditManagerArn", "documentation": "<p> The Amazon Resource Name (ARN) of the assessment. </p>"}, "awsAccount": {"shape": "AWSAccount", "documentation": "<p> The Amazon Web Services account that's associated with the assessment. </p>"}, "metadata": {"shape": "AssessmentMetadata", "documentation": "<p> The metadata for the assessment. </p>"}, "framework": {"shape": "AssessmentFramework", "documentation": "<p> The framework that the assessment was created from. </p>"}, "tags": {"shape": "TagMap", "documentation": "<p> The tags that are associated with the assessment. </p>"}}, "documentation": "<p> An entity that defines the scope of audit evidence collected by Audit Manager. An Audit Manager assessment is an implementation of an Audit Manager framework. </p>"}, "AssessmentControl": {"type": "structure", "members": {"id": {"shape": "UUID", "documentation": "<p> The identifier for the control. </p>"}, "name": {"shape": "ControlName", "documentation": "<p> The name of the control. </p>"}, "description": {"shape": "ControlDescription", "documentation": "<p> The description of the control. </p>", "deprecated": true, "deprecatedMessage": "This data type will be deprecated on May 19, 2025. To view the assessment control description, use GetControl.", "deprecatedSince": "2025-05-19"}, "status": {"shape": "ControlStatus", "documentation": "<p> The status of the control. </p>"}, "response": {"shape": "ControlResponse", "documentation": "<p> The response of the control. </p>"}, "comments": {"shape": "ControlComments", "documentation": "<p> The list of comments that's attached to the control. </p>"}, "evidenceSources": {"shape": "EvidenceSources", "documentation": "<p> The list of data sources for the evidence. </p>"}, "evidenceCount": {"shape": "Integer", "documentation": "<p> The amount of evidence that's collected for the control. </p>"}, "assessmentReportEvidenceCount": {"shape": "Integer", "documentation": "<p> The amount of evidence in the assessment report. </p>"}}, "documentation": "<p> The control entity that represents a standard control or a custom control in an Audit Manager assessment. </p>"}, "AssessmentControlSet": {"type": "structure", "members": {"id": {"shape": "ControlSetId", "documentation": "<p> The identifier of the control set in the assessment. This is the control set name in a plain string format. </p>"}, "description": {"shape": "NonEmptyString", "documentation": "<p> The description for the control set. </p>"}, "status": {"shape": "ControlSetStatus", "documentation": "<p> The current status of the control set. </p>"}, "roles": {"shape": "Roles", "documentation": "<p> The roles that are associated with the control set. </p>"}, "controls": {"shape": "AssessmentControls", "documentation": "<p> The list of controls that's contained with the control set. </p>"}, "delegations": {"shape": "Delegations", "documentation": "<p> The delegations that are associated with the control set. </p>"}, "systemEvidenceCount": {"shape": "Integer", "documentation": "<p> The total number of evidence objects that are retrieved automatically for the control set. </p>"}, "manualEvidenceCount": {"shape": "Integer", "documentation": "<p> The total number of evidence objects that are uploaded manually to the control set. </p>"}}, "documentation": "<p> Represents a set of controls in an Audit Manager assessment. </p>"}, "AssessmentControlSets": {"type": "list", "member": {"shape": "AssessmentControlSet"}}, "AssessmentControls": {"type": "list", "member": {"shape": "AssessmentControl"}}, "AssessmentDescription": {"type": "string", "max": 1000, "pattern": "^[\\w\\W\\s\\S]*$", "sensitive": true}, "AssessmentEvidenceFolder": {"type": "structure", "members": {"name": {"shape": "AssessmentEvidenceFolderName", "documentation": "<p> The name of the evidence folder. </p>"}, "date": {"shape": "Timestamp", "documentation": "<p> The date when the first evidence was added to the evidence folder. </p>"}, "assessmentId": {"shape": "UUID", "documentation": "<p> The identifier for the assessment. </p>"}, "controlSetId": {"shape": "ControlSetId", "documentation": "<p> The identifier for the control set. </p>"}, "controlId": {"shape": "UUID", "documentation": "<p> The unique identifier for the control. </p>"}, "id": {"shape": "UUID", "documentation": "<p> The identifier for the folder that the evidence is stored in. </p>"}, "dataSource": {"shape": "String", "documentation": "<p> The Amazon Web Services service that the evidence was collected from. </p>"}, "author": {"shape": "String", "documentation": "<p> The name of the user who created the evidence folder. </p>"}, "totalEvidence": {"shape": "Integer", "documentation": "<p> The total amount of evidence in the evidence folder. </p>"}, "assessmentReportSelectionCount": {"shape": "Integer", "documentation": "<p> The total count of evidence that's included in the assessment report. </p>"}, "controlName": {"shape": "ControlName", "documentation": "<p> The name of the control. </p>"}, "evidenceResourcesIncludedCount": {"shape": "Integer", "documentation": "<p> The amount of evidence that's included in the evidence folder. </p>"}, "evidenceByTypeConfigurationDataCount": {"shape": "Integer", "documentation": "<p> The number of evidence that falls under the configuration data category. This evidence is collected from configuration snapshots of other Amazon Web Services services such as Amazon EC2, Amazon S3, or IAM. </p>"}, "evidenceByTypeManualCount": {"shape": "Integer", "documentation": "<p> The number of evidence that falls under the manual category. This evidence is imported manually. </p>"}, "evidenceByTypeComplianceCheckCount": {"shape": "Integer", "documentation": "<p> The number of evidence that falls under the compliance check category. This evidence is collected from Config or Security Hub. </p>"}, "evidenceByTypeComplianceCheckIssuesCount": {"shape": "Integer", "documentation": "<p> The total number of issues that were reported directly from Security Hub, Config, or both. </p>"}, "evidenceByTypeUserActivityCount": {"shape": "Integer", "documentation": "<p> The number of evidence that falls under the user activity category. This evidence is collected from CloudTrail logs. </p>"}, "evidenceAwsServiceSourceCount": {"shape": "Integer", "documentation": "<p> The total number of Amazon Web Services resources that were assessed to generate the evidence. </p>"}}, "documentation": "<p> The folder where Audit Manager stores evidence for an assessment. </p>"}, "AssessmentEvidenceFolderName": {"type": "string", "max": 300, "min": 1, "pattern": "^[\\w\\W\\s\\S]*$"}, "AssessmentEvidenceFolders": {"type": "list", "member": {"shape": "AssessmentEvidenceFolder"}}, "AssessmentFramework": {"type": "structure", "members": {"id": {"shape": "UUID", "documentation": "<p> The unique identifier for the framework. </p>"}, "arn": {"shape": "AuditManagerArn", "documentation": "<p> The Amazon Resource Name (ARN) of the framework. </p>"}, "metadata": {"shape": "FrameworkMetadata"}, "controlSets": {"shape": "AssessmentControlSets", "documentation": "<p> The control sets that are associated with the framework. </p>"}}, "documentation": "<p> The file used to structure and automate Audit Manager assessments for a given compliance standard. </p>", "sensitive": true}, "AssessmentFrameworkDescription": {"type": "string", "max": 200, "min": 1, "pattern": "^[\\w\\W\\s\\S]*$"}, "AssessmentFrameworkMetadata": {"type": "structure", "members": {"arn": {"shape": "AuditManagerArn", "documentation": "<p> The Amazon Resource Name (ARN) of the framework. </p>"}, "id": {"shape": "UUID", "documentation": "<p> The unique identifier for the framework. </p>"}, "type": {"shape": "FrameworkType", "documentation": "<p> The framework type, such as a standard framework or a custom framework. </p>"}, "name": {"shape": "FrameworkName", "documentation": "<p> The name of the framework. </p>"}, "description": {"shape": "FrameworkDescription", "documentation": "<p> The description of the framework. </p>"}, "logo": {"shape": "Filename", "documentation": "<p> The logo that's associated with the framework. </p>"}, "complianceType": {"shape": "ComplianceType", "documentation": "<p> The compliance type that the new custom framework supports, such as CIS or HIPAA. </p>"}, "controlsCount": {"shape": "ControlsCount", "documentation": "<p> The number of controls that are associated with the framework. </p>"}, "controlSetsCount": {"shape": "ControlSetsCount", "documentation": "<p> The number of control sets that are associated with the framework. </p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p> The time when the framework was created. </p>"}, "lastUpdatedAt": {"shape": "Timestamp", "documentation": "<p> The time when the framework was most recently updated. </p>"}}, "documentation": "<p> The metadata that's associated with a standard framework or a custom framework. </p>"}, "AssessmentFrameworkShareRequest": {"type": "structure", "members": {"id": {"shape": "UUID", "documentation": "<p> The unique identifier for the share request. </p>"}, "frameworkId": {"shape": "UUID", "documentation": "<p>The unique identifier for the shared custom framework. </p>"}, "frameworkName": {"shape": "FrameworkName", "documentation": "<p> The name of the custom framework that the share request is for. </p>"}, "frameworkDescription": {"shape": "FrameworkDescription", "documentation": "<p>The description of the shared custom framework.</p>"}, "status": {"shape": "ShareRequestStatus", "documentation": "<p> The status of the share request. </p>"}, "sourceAccount": {"shape": "AccountId", "documentation": "<p> The Amazon Web Services account of the sender. </p>"}, "destinationAccount": {"shape": "AccountId", "documentation": "<p> The Amazon Web Services account of the recipient. </p>"}, "destinationRegion": {"shape": "Region", "documentation": "<p> The Amazon Web Services Region of the recipient. </p>"}, "expirationTime": {"shape": "Timestamp", "documentation": "<p> The time when the share request expires. </p>"}, "creationTime": {"shape": "Timestamp", "documentation": "<p> The time when the share request was created. </p>"}, "lastUpdated": {"shape": "Timestamp", "documentation": "<p> Specifies when the share request was last updated. </p>"}, "comment": {"shape": "ShareRequestComment", "documentation": "<p> An optional comment from the sender about the share request. </p>"}, "standardControlsCount": {"shape": "NullableInteger", "documentation": "<p>The number of standard controls that are part of the shared custom framework. </p>"}, "customControlsCount": {"shape": "NullableInteger", "documentation": "<p>The number of custom controls that are part of the shared custom framework.</p>"}, "complianceType": {"shape": "ComplianceType", "documentation": "<p>The compliance type that the shared custom framework supports, such as CIS or HIPAA.</p>"}}, "documentation": "<p> Represents a share request for a custom framework in Audit Manager. </p>"}, "AssessmentFrameworkShareRequestList": {"type": "list", "member": {"shape": "AssessmentFrameworkShareRequest"}}, "AssessmentMetadata": {"type": "structure", "members": {"name": {"shape": "AssessmentName", "documentation": "<p> The name of the assessment. </p>"}, "id": {"shape": "UUID", "documentation": "<p> The unique identifier for the assessment. </p>"}, "description": {"shape": "AssessmentDescription", "documentation": "<p> The description of the assessment. </p>"}, "complianceType": {"shape": "ComplianceType", "documentation": "<p> The name of the compliance standard that's related to the assessment, such as PCI-DSS. </p>"}, "status": {"shape": "AssessmentStatus", "documentation": "<p> The overall status of the assessment. </p>"}, "assessmentReportsDestination": {"shape": "AssessmentReportsDestination", "documentation": "<p> The destination that evidence reports are stored in for the assessment. </p>"}, "scope": {"shape": "<PERSON><PERSON>", "documentation": "<p> The wrapper of Amazon Web Services accounts and services that are in scope for the assessment. </p>"}, "roles": {"shape": "Roles", "documentation": "<p> The roles that are associated with the assessment. </p>"}, "delegations": {"shape": "Delegations", "documentation": "<p> The delegations that are associated with the assessment. </p>"}, "creationTime": {"shape": "Timestamp", "documentation": "<p> Specifies when the assessment was created. </p>"}, "lastUpdated": {"shape": "Timestamp", "documentation": "<p> The time of the most recent update. </p>"}}, "documentation": "<p> The metadata that's associated with the specified assessment. </p>"}, "AssessmentMetadataItem": {"type": "structure", "members": {"name": {"shape": "AssessmentName", "documentation": "<p> The name of the assessment. </p>"}, "id": {"shape": "UUID", "documentation": "<p> The unique identifier for the assessment. </p>"}, "complianceType": {"shape": "ComplianceType", "documentation": "<p> The name of the compliance standard that's related to the assessment, such as PCI-DSS. </p>"}, "status": {"shape": "AssessmentStatus", "documentation": "<p> The current status of the assessment. </p>"}, "roles": {"shape": "Roles", "documentation": "<p> The roles that are associated with the assessment. </p>"}, "delegations": {"shape": "Delegations", "documentation": "<p> The delegations that are associated with the assessment. </p>"}, "creationTime": {"shape": "Timestamp", "documentation": "<p> Specifies when the assessment was created. </p>"}, "lastUpdated": {"shape": "Timestamp", "documentation": "<p> The time of the most recent update. </p>"}}, "documentation": "<p> A metadata object that's associated with an assessment in Audit Manager. </p>"}, "AssessmentName": {"type": "string", "max": 300, "min": 1, "pattern": "^[^\\\\]*$", "sensitive": true}, "AssessmentReport": {"type": "structure", "members": {"id": {"shape": "UUID", "documentation": "<p> The unique identifier for the assessment report. </p>"}, "name": {"shape": "AssessmentReportName", "documentation": "<p> The name that's given to the assessment report. </p>"}, "description": {"shape": "AssessmentReportDescription", "documentation": "<p> The description of the specified assessment report. </p>"}, "awsAccountId": {"shape": "AccountId", "documentation": "<p> The identifier for the specified Amazon Web Services account. </p>"}, "assessmentId": {"shape": "UUID", "documentation": "<p> The identifier for the specified assessment. </p>"}, "assessmentName": {"shape": "AssessmentName", "documentation": "<p> The name of the associated assessment. </p>"}, "author": {"shape": "Username", "documentation": "<p> The name of the user who created the assessment report. </p>"}, "status": {"shape": "AssessmentReportStatus", "documentation": "<p> The current status of the specified assessment report. </p>"}, "creationTime": {"shape": "Timestamp", "documentation": "<p> Specifies when the assessment report was created. </p>"}}, "documentation": "<p> A finalized document that's generated from an Audit Manager assessment. These reports summarize the relevant evidence that was collected for your audit, and link to the relevant evidence folders. These evidence folders are named and organized according to the controls that are specified in your assessment. </p>"}, "AssessmentReportDescription": {"type": "string", "max": 1000, "pattern": "^[\\w\\W\\s\\S]*$", "sensitive": true}, "AssessmentReportDestinationType": {"type": "string", "enum": ["S3"]}, "AssessmentReportEvidenceError": {"type": "structure", "members": {"evidenceId": {"shape": "UUID", "documentation": "<p> The identifier for the evidence. </p>"}, "errorCode": {"shape": "ErrorCode", "documentation": "<p> The error code that was returned. </p>"}, "errorMessage": {"shape": "ErrorMessage", "documentation": "<p> The error message that was returned. </p>"}}, "documentation": "<p> An error entity for assessment report evidence errors. This is used to provide more meaningful errors than a simple string message. </p>"}, "AssessmentReportEvidenceErrors": {"type": "list", "member": {"shape": "AssessmentReportEvidenceError"}}, "AssessmentReportMetadata": {"type": "structure", "members": {"id": {"shape": "UUID", "documentation": "<p> The unique identifier for the assessment report. </p>"}, "name": {"shape": "AssessmentReportName", "documentation": "<p> The name of the assessment report. </p>"}, "description": {"shape": "AssessmentReportDescription", "documentation": "<p> The description of the assessment report. </p>"}, "assessmentId": {"shape": "UUID", "documentation": "<p> The unique identifier for the associated assessment. </p>"}, "assessmentName": {"shape": "AssessmentName", "documentation": "<p>The name of the associated assessment. </p>"}, "author": {"shape": "Username", "documentation": "<p> The name of the user who created the assessment report. </p>"}, "status": {"shape": "AssessmentReportStatus", "documentation": "<p> The current status of the assessment report. </p>"}, "creationTime": {"shape": "Timestamp", "documentation": "<p> Specifies when the assessment report was created. </p>"}}, "documentation": "<p> The metadata objects that are associated with the specified assessment report. </p>"}, "AssessmentReportName": {"type": "string", "max": 300, "min": 1, "pattern": "^[a-zA-Z0-9-_\\.]+$"}, "AssessmentReportStatus": {"type": "string", "enum": ["COMPLETE", "IN_PROGRESS", "FAILED"]}, "AssessmentReportsDestination": {"type": "structure", "members": {"destinationType": {"shape": "AssessmentReportDestinationType", "documentation": "<p> The destination type, such as Amazon S3. </p>"}, "destination": {"shape": "S3Url", "documentation": "<p> The destination bucket where Audit Manager stores assessment reports. </p>"}}, "documentation": "<p> The location where Audit Manager saves assessment reports for the given assessment. </p>", "sensitive": true}, "AssessmentReportsMetadata": {"type": "list", "member": {"shape": "AssessmentReportMetadata"}}, "AssessmentStatus": {"type": "string", "enum": ["ACTIVE", "INACTIVE"]}, "AssociateAssessmentReportEvidenceFolderRequest": {"type": "structure", "required": ["assessmentId", "evidenceFolderId"], "members": {"assessmentId": {"shape": "UUID", "documentation": "<p> The identifier for the assessment. </p>", "location": "uri", "locationName": "assessmentId"}, "evidenceFolderId": {"shape": "UUID", "documentation": "<p> The identifier for the folder that the evidence is stored in. </p>"}}}, "AssociateAssessmentReportEvidenceFolderResponse": {"type": "structure", "members": {}}, "AuditManagerArn": {"type": "string", "max": 2048, "min": 20, "pattern": "^arn:.*:auditmanager:.*"}, "BatchAssociateAssessmentReportEvidenceRequest": {"type": "structure", "required": ["assessmentId", "evidenceFolderId", "evidenceIds"], "members": {"assessmentId": {"shape": "UUID", "documentation": "<p> The identifier for the assessment. </p>", "location": "uri", "locationName": "assessmentId"}, "evidenceFolderId": {"shape": "UUID", "documentation": "<p> The identifier for the folder that the evidence is stored in. </p>"}, "evidenceIds": {"shape": "EvidenceIds", "documentation": "<p> The list of evidence identifiers. </p>"}}}, "BatchAssociateAssessmentReportEvidenceResponse": {"type": "structure", "members": {"evidenceIds": {"shape": "EvidenceIds", "documentation": "<p> The list of evidence identifiers. </p>"}, "errors": {"shape": "AssessmentReportEvidenceErrors", "documentation": "<p> A list of errors that the <code>BatchAssociateAssessmentReportEvidence</code> API returned. </p>"}}}, "BatchCreateDelegationByAssessmentError": {"type": "structure", "members": {"createDelegationRequest": {"shape": "CreateDelegationRequest", "documentation": "<p> The API request to batch create delegations in Audit Manager. </p>"}, "errorCode": {"shape": "ErrorCode", "documentation": "<p> The error code that the <code>BatchCreateDelegationByAssessment</code> API returned. </p>"}, "errorMessage": {"shape": "ErrorMessage", "documentation": "<p> The error message that the <code>BatchCreateDelegationByAssessment</code> API returned. </p>"}}, "documentation": "<p> An error entity for the <code>BatchCreateDelegationByAssessment</code> API. This is used to provide more meaningful errors than a simple string message. </p>"}, "BatchCreateDelegationByAssessmentErrors": {"type": "list", "member": {"shape": "BatchCreateDelegationByAssessmentError"}, "sensitive": true}, "BatchCreateDelegationByAssessmentRequest": {"type": "structure", "required": ["createDelegationRequests", "assessmentId"], "members": {"createDelegationRequests": {"shape": "CreateDelegationRequests", "documentation": "<p> The API request to batch create delegations in Audit Manager. </p>"}, "assessmentId": {"shape": "UUID", "documentation": "<p> The identifier for the assessment. </p>", "location": "uri", "locationName": "assessmentId"}}}, "BatchCreateDelegationByAssessmentResponse": {"type": "structure", "members": {"delegations": {"shape": "Delegations", "documentation": "<p> The delegations that are associated with the assessment. </p>"}, "errors": {"shape": "BatchCreateDelegationByAssessmentErrors", "documentation": "<p> A list of errors that the <code>BatchCreateDelegationByAssessment</code> API returned. </p>"}}}, "BatchDeleteDelegationByAssessmentError": {"type": "structure", "members": {"delegationId": {"shape": "UUID", "documentation": "<p> The identifier for the delegation. </p>"}, "errorCode": {"shape": "ErrorCode", "documentation": "<p> The error code that the <code>BatchDeleteDelegationByAssessment</code> API returned. </p>"}, "errorMessage": {"shape": "ErrorMessage", "documentation": "<p> The error message that the <code>BatchDeleteDelegationByAssessment</code> API returned. </p>"}}, "documentation": "<p> An error entity for the <code>BatchDeleteDelegationByAssessment</code> API. This is used to provide more meaningful errors than a simple string message. </p>"}, "BatchDeleteDelegationByAssessmentErrors": {"type": "list", "member": {"shape": "BatchDeleteDelegationByAssessmentError"}, "sensitive": true}, "BatchDeleteDelegationByAssessmentRequest": {"type": "structure", "required": ["delegationIds", "assessmentId"], "members": {"delegationIds": {"shape": "DelegationIds", "documentation": "<p> The identifiers for the delegations. </p>"}, "assessmentId": {"shape": "UUID", "documentation": "<p> The identifier for the assessment. </p>", "location": "uri", "locationName": "assessmentId"}}}, "BatchDeleteDelegationByAssessmentResponse": {"type": "structure", "members": {"errors": {"shape": "BatchDeleteDelegationByAssessmentErrors", "documentation": "<p> A list of errors that the <code>BatchDeleteDelegationByAssessment</code> API returned. </p>"}}}, "BatchDisassociateAssessmentReportEvidenceRequest": {"type": "structure", "required": ["assessmentId", "evidenceFolderId", "evidenceIds"], "members": {"assessmentId": {"shape": "UUID", "documentation": "<p> The identifier for the assessment. </p>", "location": "uri", "locationName": "assessmentId"}, "evidenceFolderId": {"shape": "UUID", "documentation": "<p> The identifier for the folder that the evidence is stored in. </p>"}, "evidenceIds": {"shape": "EvidenceIds", "documentation": "<p> The list of evidence identifiers. </p>"}}}, "BatchDisassociateAssessmentReportEvidenceResponse": {"type": "structure", "members": {"evidenceIds": {"shape": "EvidenceIds", "documentation": "<p> The identifier for the evidence. </p>"}, "errors": {"shape": "AssessmentReportEvidenceErrors", "documentation": "<p> A list of errors that the <code>BatchDisassociateAssessmentReportEvidence</code> API returned. </p>"}}}, "BatchImportEvidenceToAssessmentControlError": {"type": "structure", "members": {"manualEvidence": {"shape": "ManualEvidence", "documentation": "<p> Manual evidence that can't be collected automatically by Audit Manager. </p>"}, "errorCode": {"shape": "ErrorCode", "documentation": "<p> The error code that the <code>BatchImportEvidenceToAssessmentControl</code> API returned. </p>"}, "errorMessage": {"shape": "ErrorMessage", "documentation": "<p> The error message that the <code>BatchImportEvidenceToAssessmentControl</code> API returned. </p>"}}, "documentation": "<p> An error entity for the <code>BatchImportEvidenceToAssessmentControl</code> API. This is used to provide more meaningful errors than a simple string message. </p>"}, "BatchImportEvidenceToAssessmentControlErrors": {"type": "list", "member": {"shape": "BatchImportEvidenceToAssessmentControlError"}}, "BatchImportEvidenceToAssessmentControlRequest": {"type": "structure", "required": ["assessmentId", "controlSetId", "controlId", "manualEvidence"], "members": {"assessmentId": {"shape": "UUID", "documentation": "<p> The identifier for the assessment. </p>", "location": "uri", "locationName": "assessmentId"}, "controlSetId": {"shape": "ControlSetId", "documentation": "<p> The identifier for the control set. </p>", "location": "uri", "locationName": "controlSetId"}, "controlId": {"shape": "UUID", "documentation": "<p> The identifier for the control. </p>", "location": "uri", "locationName": "controlId"}, "manualEvidence": {"shape": "ManualEvidenceList", "documentation": "<p> The list of manual evidence objects. </p>"}}}, "BatchImportEvidenceToAssessmentControlResponse": {"type": "structure", "members": {"errors": {"shape": "BatchImportEvidenceToAssessmentControlErrors", "documentation": "<p> A list of errors that the <code>BatchImportEvidenceToAssessmentControl</code> API returned. </p>"}}}, "Boolean": {"type": "boolean"}, "ChangeLog": {"type": "structure", "members": {"objectType": {"shape": "ObjectTypeEnum", "documentation": "<p> The object that was changed, such as an assessment, control, or control set. </p>"}, "objectName": {"shape": "NonEmptyString", "documentation": "<p> The name of the object that changed. This could be the name of an assessment, control, or control set.</p>"}, "action": {"shape": "ActionEnum", "documentation": "<p> The action that was performed. </p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p> The time when the action was performed and the changelog record was created. </p>"}, "createdBy": {"shape": "IamArn", "documentation": "<p> The user or role that performed the action. </p>"}}, "documentation": "<p> The record of a change within Audit Manager. For example, this could be the status change of an assessment or the delegation of a control set. </p>"}, "ChangeLogs": {"type": "list", "member": {"shape": "ChangeLog"}}, "CloudTrailArn": {"type": "string", "max": 2048, "min": 20, "pattern": "^arn:.*:cloudtrail:.*"}, "ComplianceType": {"type": "string", "max": 100, "pattern": "^[\\w\\W\\s\\S]*$", "sensitive": true}, "Control": {"type": "structure", "members": {"arn": {"shape": "AuditManagerArn", "documentation": "<p> The Amazon Resource Name (ARN) of the control. </p>"}, "id": {"shape": "UUID", "documentation": "<p> The unique identifier for the control. </p>"}, "type": {"shape": "ControlType", "documentation": "<p> Specifies whether the control is a standard control or a custom control.</p>"}, "name": {"shape": "ControlName", "documentation": "<p> The name of the control. </p>"}, "description": {"shape": "ControlDescription", "documentation": "<p> The description of the control. </p>"}, "testingInformation": {"shape": "TestingInformation", "documentation": "<p> The steps that you should follow to determine if the control has been satisfied. </p>"}, "actionPlanTitle": {"shape": "ActionPlanTitle", "documentation": "<p> The title of the action plan for remediating the control. </p>"}, "actionPlanInstructions": {"shape": "ActionPlanInstructions", "documentation": "<p> The recommended actions to carry out if the control isn't fulfilled. </p>"}, "controlSources": {"shape": "ControlSources", "documentation": "<p> The data source types that determine where Audit Manager collects evidence from for the control. </p>"}, "controlMappingSources": {"shape": "ControlMappingSources", "documentation": "<p> The data mapping sources for the control. </p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p> The time when the control was created. </p>"}, "lastUpdatedAt": {"shape": "Timestamp", "documentation": "<p> The time when the control was most recently updated. </p>"}, "createdBy": {"shape": "CreatedBy", "documentation": "<p> The user or role that created the control. </p>"}, "lastUpdatedBy": {"shape": "LastUpdatedBy", "documentation": "<p> The user or role that most recently updated the control. </p>"}, "tags": {"shape": "TagMap", "documentation": "<p> The tags associated with the control. </p>"}, "state": {"shape": "ControlState", "documentation": "<p>The state of the control. The <code>END_OF_SUPPORT</code> state is applicable to standard controls only. This state indicates that the standard control can still be used to collect evidence, but Audit Manager is no longer updating or maintaining that control.</p>"}}, "documentation": "<p> A control in Audit Manager. </p>"}, "ControlCatalogId": {"type": "string", "max": 2048, "min": 13, "pattern": "^arn:.*:controlcatalog:.*|UNCATEGORIZED"}, "ControlComment": {"type": "structure", "members": {"authorName": {"shape": "Username", "documentation": "<p> The name of the user who authored the comment. </p>"}, "commentBody": {"shape": "ControlCommentBody", "documentation": "<p> The body text of a control comment. </p>"}, "postedDate": {"shape": "Timestamp", "documentation": "<p> The time when the comment was posted. </p>"}}, "documentation": "<p> A comment that's posted by a user on a control. This includes the author's name, the comment text, and a timestamp. </p>"}, "ControlCommentBody": {"type": "string", "max": 500, "pattern": "^[\\w\\W\\s\\S]*$", "sensitive": true}, "ControlComments": {"type": "list", "member": {"shape": "ControlComment"}}, "ControlDescription": {"type": "string", "max": 1000, "pattern": "^[\\w\\W\\s\\S]*$", "sensitive": true}, "ControlDomainId": {"type": "string", "max": 2048, "min": 13, "pattern": "^arn:.*:controlcatalog:.*:.*:domain/.*|UNCATEGORIZED|^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$"}, "ControlDomainInsights": {"type": "structure", "members": {"name": {"shape": "String", "documentation": "<p>The name of the control domain. </p>"}, "id": {"shape": "ControlDomainId", "documentation": "<p>The unique identifier for the control domain. Audit Manager supports the control domains that are provided by Amazon Web Services Control Catalog. For information about how to find a list of available control domains, see <a href=\"https://docs.aws.amazon.com/controlcatalog/latest/APIReference/API_ListDomains.html\"> <code>ListDomains</code> </a> in the Amazon Web Services Control Catalog API Reference.</p>"}, "controlsCountByNoncompliantEvidence": {"shape": "NullableInteger", "documentation": "<p>The number of controls in the control domain that collected non-compliant evidence on the <code>lastUpdated</code> date. </p>"}, "totalControlsCount": {"shape": "NullableInteger", "documentation": "<p>The total number of controls in the control domain. </p>"}, "evidenceInsights": {"shape": "EvidenceInsights", "documentation": "<p>A breakdown of the compliance check status for the evidence that’s associated with the control domain. </p>"}, "lastUpdated": {"shape": "Timestamp", "documentation": "<p>The time when the control domain insights were last updated. </p>"}}, "documentation": "<p>A summary of the latest analytics data for a specific control domain.</p> <p>Control domain insights are grouped by control domain, and ranked by the highest total count of non-compliant evidence.</p>"}, "ControlDomainInsightsList": {"type": "list", "member": {"shape": "ControlDomainInsights"}}, "ControlInsightsMetadata": {"type": "list", "member": {"shape": "ControlInsightsMetadataItem"}}, "ControlInsightsMetadataByAssessment": {"type": "list", "member": {"shape": "ControlInsightsMetadataByAssessmentItem"}}, "ControlInsightsMetadataByAssessmentItem": {"type": "structure", "members": {"name": {"shape": "String", "documentation": "<p>The name of the assessment control. </p>"}, "id": {"shape": "ControlDomainId", "documentation": "<p>The unique identifier for the assessment control. </p>"}, "evidenceInsights": {"shape": "EvidenceInsights", "documentation": "<p>A breakdown of the compliance check status for the evidence that’s associated with the assessment control. </p>"}, "controlSetName": {"shape": "NonEmptyString", "documentation": "<p>The name of the control set that the assessment control belongs to. </p>"}, "lastUpdated": {"shape": "Timestamp", "documentation": "<p>The time when the assessment control insights were last updated. </p>"}}, "documentation": "<p>A summary of the latest analytics data for a specific control in a specific active assessment.</p> <p>Control insights are grouped by control domain, and ranked by the highest total count of non-compliant evidence. </p>"}, "ControlInsightsMetadataItem": {"type": "structure", "members": {"name": {"shape": "String", "documentation": "<p>The name of the control. </p>"}, "id": {"shape": "ControlDomainId", "documentation": "<p>The unique identifier for the control. </p>"}, "evidenceInsights": {"shape": "EvidenceInsights", "documentation": "<p>A breakdown of the compliance check status for the evidence that’s associated with the control. </p>"}, "lastUpdated": {"shape": "Timestamp", "documentation": "<p>The time when the control insights were last updated. </p>"}}, "documentation": "<p>A summary of the latest analytics data for a specific control. </p> <p>This data reflects the total counts for the specified control across all active assessments. Control insights are grouped by control domain, and ranked by the highest total count of non-compliant evidence.</p>"}, "ControlMappingSource": {"type": "structure", "members": {"sourceId": {"shape": "UUID", "documentation": "<p> The unique identifier for the source. </p>"}, "sourceName": {"shape": "SourceName", "documentation": "<p> The name of the source. </p>"}, "sourceDescription": {"shape": "SourceDescription", "documentation": "<p> The description of the source. </p>"}, "sourceSetUpOption": {"shape": "SourceSetUpOption", "documentation": "<p>The setup option for the data source. This option reflects if the evidence collection method is automated or manual. If you don’t provide a value for <code>sourceSetUpOption</code>, Audit Manager automatically infers and populates the correct value based on the <code>sourceType</code> that you specify.</p>"}, "sourceType": {"shape": "SourceType", "documentation": "<p> Specifies which type of data source is used to collect evidence. </p> <ul> <li> <p>The source can be an individual data source type, such as <code>AWS_Cloudtrail</code>, <code>AWS_Config</code>, <code>AWS_Security_Hub</code>, <code>AWS_API_Call</code>, or <code>MANUAL</code>. </p> </li> <li> <p>The source can also be a managed grouping of data sources, such as a <code>Core_Control</code> or a <code>Common_Control</code>.</p> </li> </ul>"}, "sourceKeyword": {"shape": "SourceKeyword"}, "sourceFrequency": {"shape": "SourceFrequency", "documentation": "<p>Specifies how often evidence is collected from the control mapping source. </p>"}, "troubleshootingText": {"shape": "TroubleshootingText", "documentation": "<p> The instructions for troubleshooting the control. </p>"}}, "documentation": "<p> The data source that determines where Audit Manager collects evidence from for the control. </p>"}, "ControlMappingSources": {"type": "list", "member": {"shape": "ControlMappingSource"}, "min": 1}, "ControlMetadata": {"type": "structure", "members": {"arn": {"shape": "AuditManagerArn", "documentation": "<p> The Amazon Resource Name (ARN) of the control. </p>"}, "id": {"shape": "UUID", "documentation": "<p> The unique identifier for the control. </p>"}, "name": {"shape": "ControlName", "documentation": "<p> The name of the control. </p>"}, "controlSources": {"shape": "ControlSources", "documentation": "<p> The data source that determines where Audit Manager collects evidence from for the control. </p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p> The time when the control was created. </p>"}, "lastUpdatedAt": {"shape": "Timestamp", "documentation": "<p> The time when the control was most recently updated. </p>"}}, "documentation": "<p> The metadata that's associated with the standard control or custom control. </p>"}, "ControlMetadataList": {"type": "list", "member": {"shape": "ControlMetadata"}}, "ControlName": {"type": "string", "max": 300, "min": 1, "pattern": "^[^\\\\]*$"}, "ControlResponse": {"type": "string", "enum": ["MANUAL", "AUTOMATE", "DEFER", "IGNORE"]}, "ControlSet": {"type": "structure", "members": {"id": {"shape": "UUID", "documentation": "<p> The identifier of the control set in the assessment. This is the control set name in a plain string format. </p>"}, "name": {"shape": "ControlSetName", "documentation": "<p> The name of the control set. </p>"}, "controls": {"shape": "Controls", "documentation": "<p> The list of controls within the control set. </p>"}}, "documentation": "<p> A set of controls in Audit Manager. </p>"}, "ControlSetId": {"type": "string", "max": 300, "min": 1, "pattern": "^[\\w\\W\\s\\S]*$"}, "ControlSetName": {"type": "string", "max": 300, "min": 1, "pattern": "^[^\\\\\\_]*$"}, "ControlSetStatus": {"type": "string", "enum": ["ACTIVE", "UNDER_REVIEW", "REVIEWED"]}, "ControlSets": {"type": "list", "member": {"shape": "ControlSet"}, "min": 1, "sensitive": true}, "ControlSetsCount": {"type": "integer"}, "ControlSources": {"type": "string", "max": 100, "min": 1, "pattern": "^[a-zA-Z_0-9-\\s.,]+$"}, "ControlState": {"type": "string", "enum": ["ACTIVE", "END_OF_SUPPORT"]}, "ControlStatus": {"type": "string", "enum": ["UNDER_REVIEW", "REVIEWED", "INACTIVE"]}, "ControlType": {"type": "string", "enum": ["Standard", "Custom", "Core"]}, "Controls": {"type": "list", "member": {"shape": "Control"}, "min": 1}, "ControlsCount": {"type": "integer"}, "CreateAssessmentFrameworkControl": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "UUID", "documentation": "<p> The unique identifier of the control. </p>"}}, "documentation": "<p> The control entity attributes that uniquely identify an existing control to be added to a framework in Audit Manager. </p>"}, "CreateAssessmentFrameworkControlSet": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "ControlSetName", "documentation": "<p> The name of the control set. </p>"}, "controls": {"shape": "CreateAssessmentFrameworkControls", "documentation": "<p> The list of controls within the control set. This doesn't contain the control set ID. </p>"}}, "documentation": "<p> A <code>controlSet</code> entity that represents a collection of controls in Audit Manager. This doesn't contain the control set ID. </p>"}, "CreateAssessmentFrameworkControlSets": {"type": "list", "member": {"shape": "CreateAssessmentFrameworkControlSet"}, "min": 1}, "CreateAssessmentFrameworkControls": {"type": "list", "member": {"shape": "CreateAssessmentFrameworkControl"}, "min": 1}, "CreateAssessmentFrameworkRequest": {"type": "structure", "required": ["name", "controlSets"], "members": {"name": {"shape": "FrameworkName", "documentation": "<p> The name of the new custom framework. </p>"}, "description": {"shape": "FrameworkDescription", "documentation": "<p> An optional description for the new custom framework. </p>"}, "complianceType": {"shape": "ComplianceType", "documentation": "<p> The compliance type that the new custom framework supports, such as CIS or HIPAA. </p>"}, "controlSets": {"shape": "CreateAssessmentFrameworkControlSets", "documentation": "<p> The control sets that are associated with the framework. </p>"}, "tags": {"shape": "TagMap", "documentation": "<p> The tags that are associated with the framework. </p>"}}}, "CreateAssessmentFrameworkResponse": {"type": "structure", "members": {"framework": {"shape": "Framework", "documentation": "<p> The name of the new framework that the <code>CreateAssessmentFramework</code> API returned. </p>"}}}, "CreateAssessmentReportRequest": {"type": "structure", "required": ["name", "assessmentId"], "members": {"name": {"shape": "AssessmentReportName", "documentation": "<p> The name of the new assessment report. </p>"}, "description": {"shape": "AssessmentReportDescription", "documentation": "<p> The description of the assessment report. </p>"}, "assessmentId": {"shape": "UUID", "documentation": "<p> The identifier for the assessment. </p>", "location": "uri", "locationName": "assessmentId"}, "queryStatement": {"shape": "QueryStatement", "documentation": "<p>A SQL statement that represents an evidence finder query.</p> <p>Provide this parameter when you want to generate an assessment report from the results of an evidence finder search query. When you use this parameter, Audit Manager generates a one-time report using only the evidence from the query output. This report does not include any assessment evidence that was manually <a href=\"https://docs.aws.amazon.com/audit-manager/latest/userguide/generate-assessment-report.html#generate-assessment-report-include-evidence\">added to a report using the console</a>, or <a href=\"https://docs.aws.amazon.com/audit-manager/latest/APIReference/API_BatchAssociateAssessmentReportEvidence.html\">associated with a report using the API</a>. </p> <p>To use this parameter, the <a href=\"https://docs.aws.amazon.com/audit-manager/latest/APIReference/API_EvidenceFinderEnablement.html#auditmanager-Type-EvidenceFinderEnablement-enablementStatus\">enablementStatus</a> of evidence finder must be <code>ENABLED</code>. </p> <p> For examples and help resolving <code>queryStatement</code> validation exceptions, see <a href=\"https://docs.aws.amazon.com/audit-manager/latest/userguide/evidence-finder-issues.html#querystatement-exceptions\">Troubleshooting evidence finder issues</a> in the <i>Audit Manager User Guide.</i> </p>"}}}, "CreateAssessmentReportResponse": {"type": "structure", "members": {"assessmentReport": {"shape": "AssessmentReport", "documentation": "<p> The new assessment report that the <code>CreateAssessmentReport</code> API returned. </p>"}}}, "CreateAssessmentRequest": {"type": "structure", "required": ["name", "assessmentReportsDestination", "scope", "roles", "frameworkId"], "members": {"name": {"shape": "AssessmentName", "documentation": "<p> The name of the assessment to be created. </p>"}, "description": {"shape": "AssessmentDescription", "documentation": "<p> The optional description of the assessment to be created. </p>"}, "assessmentReportsDestination": {"shape": "AssessmentReportsDestination", "documentation": "<p> The assessment report storage destination for the assessment that's being created. </p>"}, "scope": {"shape": "<PERSON><PERSON>"}, "roles": {"shape": "Roles", "documentation": "<p> The list of roles for the assessment. </p>"}, "frameworkId": {"shape": "UUID", "documentation": "<p> The identifier for the framework that the assessment will be created from. </p>"}, "tags": {"shape": "TagMap", "documentation": "<p> The tags that are associated with the assessment. </p>"}}}, "CreateAssessmentResponse": {"type": "structure", "members": {"assessment": {"shape": "Assessment"}}}, "CreateControlMappingSource": {"type": "structure", "members": {"sourceName": {"shape": "SourceName", "documentation": "<p> The name of the control mapping data source. </p>"}, "sourceDescription": {"shape": "SourceDescription", "documentation": "<p> The description of the data source that determines where Audit Manager collects evidence from for the control. </p>"}, "sourceSetUpOption": {"shape": "SourceSetUpOption", "documentation": "<p>The setup option for the data source. This option reflects if the evidence collection method is automated or manual. If you don’t provide a value for <code>sourceSetUpOption</code>, Audit Manager automatically infers and populates the correct value based on the <code>sourceType</code> that you specify.</p>"}, "sourceType": {"shape": "SourceType", "documentation": "<p> Specifies which type of data source is used to collect evidence. </p> <ul> <li> <p>The source can be an individual data source type, such as <code>AWS_Cloudtrail</code>, <code>AWS_Config</code>, <code>AWS_Security_Hub</code>, <code>AWS_API_Call</code>, or <code>MANUAL</code>. </p> </li> <li> <p>The source can also be a managed grouping of data sources, such as a <code>Core_Control</code> or a <code>Common_Control</code>.</p> </li> </ul>"}, "sourceKeyword": {"shape": "SourceKeyword"}, "sourceFrequency": {"shape": "SourceFrequency", "documentation": "<p>Specifies how often evidence is collected from the control mapping source. </p>"}, "troubleshootingText": {"shape": "TroubleshootingText", "documentation": "<p> The instructions for troubleshooting the control. </p>"}}, "documentation": "<p>The mapping attributes that determine the evidence source for a given control, along with related parameters and metadata. This doesn't contain <code>mappingID</code>. </p>"}, "CreateControlMappingSources": {"type": "list", "member": {"shape": "CreateControlMappingSource"}, "min": 1}, "CreateControlRequest": {"type": "structure", "required": ["name", "controlMappingSources"], "members": {"name": {"shape": "ControlName", "documentation": "<p> The name of the control. </p>"}, "description": {"shape": "ControlDescription", "documentation": "<p> The description of the control. </p>"}, "testingInformation": {"shape": "TestingInformation", "documentation": "<p> The steps to follow to determine if the control is satisfied. </p>"}, "actionPlanTitle": {"shape": "ActionPlanTitle", "documentation": "<p> The title of the action plan for remediating the control. </p>"}, "actionPlanInstructions": {"shape": "ActionPlanInstructions", "documentation": "<p> The recommended actions to carry out if the control isn't fulfilled. </p>"}, "controlMappingSources": {"shape": "CreateControlMappingSources", "documentation": "<p> The data mapping sources for the control. </p>"}, "tags": {"shape": "TagMap", "documentation": "<p> The tags that are associated with the control. </p>"}}}, "CreateControlResponse": {"type": "structure", "members": {"control": {"shape": "Control", "documentation": "<p> The new control that the <code>CreateControl</code> API returned. </p>"}}}, "CreateDelegationRequest": {"type": "structure", "members": {"comment": {"shape": "DelegationComment", "documentation": "<p> A comment that's related to the delegation request. </p>"}, "controlSetId": {"shape": "ControlSetId", "documentation": "<p> The unique identifier for the control set. </p>"}, "roleArn": {"shape": "IamArn", "documentation": "<p> The Amazon Resource Name (ARN) of the IAM role. </p>"}, "roleType": {"shape": "RoleType", "documentation": "<p> The type of customer persona. </p> <note> <p>In <code>CreateAssessment</code>, <code>roleType</code> can only be <code>PROCESS_OWNER</code>. </p> <p>In <code>UpdateSettings</code>, <code>roleType</code> can only be <code>PROCESS_OWNER</code>.</p> <p>In <code>BatchCreateDelegationByAssessment</code>, <code>roleType</code> can only be <code>RESOURCE_OWNER</code>.</p> </note>"}}, "documentation": "<p> A collection of attributes that's used to create a delegation for an assessment in Audit Manager. </p>"}, "CreateDelegationRequests": {"type": "list", "member": {"shape": "CreateDelegationRequest"}, "max": 50, "min": 1, "sensitive": true}, "CreatedBy": {"type": "string", "max": 100, "min": 1, "pattern": "^[a-zA-Z0-9\\s-_()\\[\\]]+$", "sensitive": true}, "DataSourceType": {"type": "string", "enum": ["AWS_Cloudtrail", "AWS_Config", "AWS_Security_Hub", "AWS_API_Call", "MANUAL"]}, "DefaultExportDestination": {"type": "structure", "members": {"destinationType": {"shape": "ExportDestinationType", "documentation": "<p>The destination type, such as Amazon S3.</p>"}, "destination": {"shape": "S3Url", "documentation": "<p>The destination bucket where Audit Manager stores exported files.</p>"}}, "documentation": "<p>The default s3 bucket where Audit Manager saves the files that you export from evidence finder.</p>"}, "Delegation": {"type": "structure", "members": {"id": {"shape": "UUID", "documentation": "<p> The unique identifier for the delegation. </p>"}, "assessmentName": {"shape": "AssessmentName", "documentation": "<p> The name of the assessment that's associated with the delegation. </p>"}, "assessmentId": {"shape": "UUID", "documentation": "<p> The identifier for the assessment that's associated with the delegation. </p>"}, "status": {"shape": "DelegationStatus", "documentation": "<p> The status of the delegation. </p>"}, "roleArn": {"shape": "IamArn", "documentation": "<p> The Amazon Resource Name (ARN) of the IAM role. </p>"}, "roleType": {"shape": "RoleType", "documentation": "<p> The type of customer persona. </p> <note> <p>In <code>CreateAssessment</code>, <code>roleType</code> can only be <code>PROCESS_OWNER</code>. </p> <p>In <code>UpdateSettings</code>, <code>roleType</code> can only be <code>PROCESS_OWNER</code>.</p> <p>In <code>BatchCreateDelegationByAssessment</code>, <code>roleType</code> can only be <code>RESOURCE_OWNER</code>.</p> </note>"}, "creationTime": {"shape": "Timestamp", "documentation": "<p> Specifies when the delegation was created. </p>"}, "lastUpdated": {"shape": "Timestamp", "documentation": "<p> Specifies when the delegation was last updated. </p>"}, "controlSetId": {"shape": "ControlSetId", "documentation": "<p> The identifier for the control set that's associated with the delegation. </p>"}, "comment": {"shape": "DelegationComment", "documentation": "<p> The comment that's related to the delegation. </p>"}, "createdBy": {"shape": "CreatedBy", "documentation": "<p> The user or role that created the delegation. </p>"}}, "documentation": "<p> The assignment of a control set to a delegate for review. </p>", "sensitive": true}, "DelegationComment": {"type": "string", "max": 350, "pattern": "^[\\w\\W\\s\\S]*$", "sensitive": true}, "DelegationIds": {"type": "list", "member": {"shape": "UUID"}, "max": 50, "min": 1}, "DelegationMetadata": {"type": "structure", "members": {"id": {"shape": "UUID", "documentation": "<p> The unique identifier for the delegation. </p>"}, "assessmentName": {"shape": "AssessmentName", "documentation": "<p> The name of the associated assessment. </p>"}, "assessmentId": {"shape": "UUID", "documentation": "<p> The unique identifier for the assessment. </p>"}, "status": {"shape": "DelegationStatus", "documentation": "<p> The current status of the delegation. </p>"}, "roleArn": {"shape": "IamArn", "documentation": "<p> The Amazon Resource Name (ARN) of the IAM role. </p>"}, "creationTime": {"shape": "Timestamp", "documentation": "<p> Specifies when the delegation was created. </p>"}, "controlSetName": {"shape": "NonEmptyString", "documentation": "<p> Specifies the name of the control set that was delegated for review. </p>"}}, "documentation": "<p> The metadata that's associated with the delegation. </p>"}, "DelegationMetadataList": {"type": "list", "member": {"shape": "DelegationMetadata"}}, "DelegationStatus": {"type": "string", "enum": ["IN_PROGRESS", "UNDER_REVIEW", "COMPLETE"]}, "Delegations": {"type": "list", "member": {"shape": "Delegation"}}, "DeleteAssessmentFrameworkRequest": {"type": "structure", "required": ["frameworkId"], "members": {"frameworkId": {"shape": "UUID", "documentation": "<p> The identifier for the custom framework. </p>", "location": "uri", "locationName": "frameworkId"}}}, "DeleteAssessmentFrameworkResponse": {"type": "structure", "members": {}}, "DeleteAssessmentFrameworkShareRequest": {"type": "structure", "required": ["requestId", "requestType"], "members": {"requestId": {"shape": "UUID", "documentation": "<p>The unique identifier for the share request to be deleted.</p>", "location": "uri", "locationName": "requestId"}, "requestType": {"shape": "ShareRequestType", "documentation": "<p>Specifies whether the share request is a sent request or a received request.</p>", "location": "querystring", "locationName": "requestType"}}}, "DeleteAssessmentFrameworkShareResponse": {"type": "structure", "members": {}}, "DeleteAssessmentReportRequest": {"type": "structure", "required": ["assessmentId", "assessmentReportId"], "members": {"assessmentId": {"shape": "UUID", "documentation": "<p> The unique identifier for the assessment. </p>", "location": "uri", "locationName": "assessmentId"}, "assessmentReportId": {"shape": "UUID", "documentation": "<p> The unique identifier for the assessment report. </p>", "location": "uri", "locationName": "assessmentReportId"}}}, "DeleteAssessmentReportResponse": {"type": "structure", "members": {}}, "DeleteAssessmentRequest": {"type": "structure", "required": ["assessmentId"], "members": {"assessmentId": {"shape": "UUID", "documentation": "<p> The identifier for the assessment. </p>", "location": "uri", "locationName": "assessmentId"}}}, "DeleteAssessmentResponse": {"type": "structure", "members": {}}, "DeleteControlRequest": {"type": "structure", "required": ["controlId"], "members": {"controlId": {"shape": "UUID", "documentation": "<p> The unique identifier for the control. </p>", "location": "uri", "locationName": "controlId"}}}, "DeleteControlResponse": {"type": "structure", "members": {}}, "DeleteResources": {"type": "string", "enum": ["ALL", "DEFAULT"]}, "DeregisterAccountRequest": {"type": "structure", "members": {}}, "DeregisterAccountResponse": {"type": "structure", "members": {"status": {"shape": "Account<PERSON><PERSON><PERSON>", "documentation": "<p> The registration status of the account. </p>"}}}, "DeregisterOrganizationAdminAccountRequest": {"type": "structure", "members": {"adminAccountId": {"shape": "AccountId", "documentation": "<p> The identifier for the administrator account. </p>"}}}, "DeregisterOrganizationAdminAccountResponse": {"type": "structure", "members": {}}, "DeregistrationPolicy": {"type": "structure", "members": {"deleteResources": {"shape": "DeleteResources", "documentation": "<p>Specifies which Audit Manager data will be deleted when you deregister Audit Manager.</p> <ul> <li> <p>If you set the value to <code>ALL</code>, all of your data is deleted within seven days of deregistration.</p> </li> <li> <p>If you set the value to <code>DEFAULT</code>, none of your data is deleted at the time of deregistration. However, keep in mind that the Audit Manager data retention policy still applies. As a result, any evidence data will be deleted two years after its creation date. Your other Audit Manager resources will continue to exist indefinitely.</p> </li> </ul>"}}, "documentation": "<p>The deregistration policy for the data that's stored in Audit Manager. You can use this attribute to determine how your data is handled when you <a href=\"https://docs.aws.amazon.com/audit-manager/latest/APIReference/API_DeregisterAccount.html\">deregister Audit Manager</a>.</p> <p>By default, Audit Manager retains evidence data for two years from the time of its creation. Other Audit Manager resources (including assessments, custom controls, and custom frameworks) remain in Audit Manager indefinitely, and are available if you <a href=\"https://docs.aws.amazon.com/audit-manager/latest/APIReference/API_RegisterAccount.html\">re-register Audit Manager</a> in the future. For more information about data retention, see <a href=\"https://docs.aws.amazon.com/audit-manager/latest/userguide/data-protection.html\">Data Protection</a> in the <i>Audit Manager User Guide</i>.</p> <important> <p>If you choose to delete all data, this action permanently deletes all evidence data in your account within seven days. It also deletes all of the Audit Manager resources that you created, including assessments, custom controls, and custom frameworks. Your data will not be available if you re-register Audit Manager in the future.</p> </important>"}, "DisassociateAssessmentReportEvidenceFolderRequest": {"type": "structure", "required": ["assessmentId", "evidenceFolderId"], "members": {"assessmentId": {"shape": "UUID", "documentation": "<p> The unique identifier for the assessment. </p>", "location": "uri", "locationName": "assessmentId"}, "evidenceFolderId": {"shape": "UUID", "documentation": "<p> The unique identifier for the folder that the evidence is stored in. </p>"}}}, "DisassociateAssessmentReportEvidenceFolderResponse": {"type": "structure", "members": {}}, "EmailAddress": {"type": "string", "max": 320, "min": 1, "pattern": "^.*@.*$", "sensitive": true}, "ErrorCode": {"type": "string", "max": 3, "min": 3, "pattern": "[0-9]{3}"}, "ErrorMessage": {"type": "string", "max": 300, "pattern": "^[\\w\\W\\s\\S]*$"}, "EventName": {"type": "string", "max": 100, "pattern": "^[\\w\\W\\s\\S]*$"}, "Evidence": {"type": "structure", "members": {"dataSource": {"shape": "String", "documentation": "<p> The data source where the evidence was collected from. </p>"}, "evidenceAwsAccountId": {"shape": "AccountId", "documentation": "<p> The identifier for the Amazon Web Services account. </p>"}, "time": {"shape": "Timestamp", "documentation": "<p> The timestamp that represents when the evidence was collected. </p>"}, "eventSource": {"shape": "AWSServiceName", "documentation": "<p> The Amazon Web Services service that the evidence is collected from. </p>"}, "eventName": {"shape": "EventName", "documentation": "<p> The name of the evidence event. </p>"}, "evidenceByType": {"shape": "String", "documentation": "<p> The type of automated evidence. </p>"}, "resourcesIncluded": {"shape": "Resources", "documentation": "<p> The list of resources that are assessed to generate the evidence. </p>"}, "attributes": {"shape": "EvidenceAttributes", "documentation": "<p> The names and values that are used by the evidence event. This includes an attribute name (such as <code>allowUsersToChangePassword</code>) and value (such as <code>true</code> or <code>false</code>). </p>"}, "iamId": {"shape": "IamArn", "documentation": "<p> The unique identifier for the user or role that's associated with the evidence. </p>"}, "complianceCheck": {"shape": "String", "documentation": "<p>The evaluation status for automated evidence that falls under the compliance check category.</p> <ul> <li> <p>Audit Manager classes evidence as non-compliant if Security Hub reports a <i>Fail</i> result, or if Config reports a <i>Non-compliant</i> result.</p> </li> <li> <p>Audit Manager classes evidence as compliant if Security Hub reports a <i>Pass</i> result, or if Config reports a <i>Compliant</i> result.</p> </li> <li> <p>If a compliance check isn't available or applicable, then no compliance evaluation can be made for that evidence. This is the case if the evidence uses Config or Security Hub as the underlying data source type, but those services aren't enabled. This is also the case if the evidence uses an underlying data source type that doesn't support compliance checks (such as manual evidence, Amazon Web Services API calls, or CloudTrail). </p> </li> </ul>"}, "awsOrganization": {"shape": "String", "documentation": "<p> The Amazon Web Services account that the evidence is collected from, and its organization path. </p>"}, "awsAccountId": {"shape": "AccountId", "documentation": "<p> The identifier for the Amazon Web Services account. </p>"}, "evidenceFolderId": {"shape": "UUID", "documentation": "<p> The identifier for the folder that the evidence is stored in. </p>"}, "id": {"shape": "UUID", "documentation": "<p> The identifier for the evidence. </p>"}, "assessmentReportSelection": {"shape": "String", "documentation": "<p> Specifies whether the evidence is included in the assessment report. </p>"}}, "documentation": "<p> A record that contains the information needed to demonstrate compliance with the requirements specified by a control. Examples of evidence include change activity invoked by a user, or a system configuration snapshot. </p>"}, "EvidenceAttributeKey": {"type": "string", "max": 100, "pattern": "^[\\w\\W\\s\\S]*$"}, "EvidenceAttributeValue": {"type": "string", "max": 200, "pattern": "^[\\w\\W\\s\\S]*$"}, "EvidenceAttributes": {"type": "map", "key": {"shape": "EvidenceAttributeKey"}, "value": {"shape": "EvidenceAttributeValue"}}, "EvidenceFinderBackfillStatus": {"type": "string", "enum": ["NOT_STARTED", "IN_PROGRESS", "COMPLETED"]}, "EvidenceFinderEnablement": {"type": "structure", "members": {"eventDataStoreArn": {"shape": "CloudTrailArn", "documentation": "<p>The Amazon Resource Name (ARN) of the CloudTrail Lake event data store that’s used by evidence finder. The event data store is the lake of evidence data that evidence finder runs queries against.</p>"}, "enablementStatus": {"shape": "EvidenceFinderEnablementStatus", "documentation": "<p>The current status of the evidence finder feature and the related event data store. </p> <ul> <li> <p> <code>ENABLE_IN_PROGRESS</code> means that you requested to enable evidence finder. An event data store is currently being created to support evidence finder queries.</p> </li> <li> <p> <code>ENABLED</code> means that an event data store was successfully created and evidence finder is enabled. We recommend that you wait 7 days until the event data store is backfilled with your past two years’ worth of evidence data. You can use evidence finder in the meantime, but not all data might be available until the backfill is complete.</p> </li> <li> <p> <code>DISABLE_IN_PROGRESS</code> means that you requested to disable evidence finder, and your request is pending the deletion of the event data store.</p> </li> <li> <p> <code>DISABLED</code> means that you have permanently disabled evidence finder and the event data store has been deleted. You can't re-enable evidence finder after this point.</p> </li> </ul>"}, "backfillStatus": {"shape": "EvidenceFinderBackfillStatus", "documentation": "<p>The current status of the evidence data backfill process. </p> <p>The backfill starts after you enable evidence finder. During this task, Audit Manager populates an event data store with your past two years’ worth of evidence data so that your evidence can be queried.</p> <ul> <li> <p> <code>NOT_STARTED</code> means that the backfill hasn’t started yet. </p> </li> <li> <p> <code>IN_PROGRESS</code> means that the backfill is in progress. This can take up to 7 days to complete, depending on the amount of evidence data. </p> </li> <li> <p> <code>COMPLETED</code> means that the backfill is complete. All of your past evidence is now queryable. </p> </li> </ul>"}, "error": {"shape": "ErrorMessage", "documentation": "<p>Represents any errors that occurred when enabling or disabling evidence finder. </p>"}}, "documentation": "<p>The settings object that specifies whether evidence finder is enabled. This object also describes the related event data store, and the backfill status for populating the event data store with evidence data.</p>"}, "EvidenceFinderEnablementStatus": {"type": "string", "enum": ["ENABLED", "DISABLED", "ENABLE_IN_PROGRESS", "DISABLE_IN_PROGRESS"]}, "EvidenceIds": {"type": "list", "member": {"shape": "UUID"}, "max": 50, "min": 0}, "EvidenceInsights": {"type": "structure", "members": {"noncompliantEvidenceCount": {"shape": "NullableInteger", "documentation": "<p>The number of compliance check evidence that Audit Manager classified as non-compliant. This includes evidence that was collected from Security Hub with a <i>Fail</i> ruling, or collected from Config with a <i>Non-compliant</i> ruling. </p>"}, "compliantEvidenceCount": {"shape": "NullableInteger", "documentation": "<p>The number of compliance check evidence that Audit Manager classified as compliant. This includes evidence that was collected from Security Hub with a <i>Pass</i> ruling, or collected from Config with a <i>Compliant</i> ruling. </p>"}, "inconclusiveEvidenceCount": {"shape": "NullableInteger", "documentation": "<p>The number of evidence that a compliance check ruling isn't available for. Evidence is inconclusive when the associated control uses Security Hub or Config as a data source but you didn't enable those services. This is also the case when a control uses a data source that doesn’t support compliance checks (for example, manual evidence, API calls, or CloudTrail). </p> <note> <p>If evidence has a compliance check status of <i>not applicable</i> in the console, it's classified as <i>inconclusive</i> in <code>EvidenceInsights</code> data.</p> </note>"}}, "documentation": "<p>A breakdown of the latest compliance check status for the evidence in your Audit Manager assessments. </p>"}, "EvidenceList": {"type": "list", "member": {"shape": "Evidence"}}, "EvidenceSources": {"type": "list", "member": {"shape": "NonEmptyString"}}, "ExportDestinationType": {"type": "string", "enum": ["S3"]}, "Filename": {"type": "string", "max": 255, "min": 1, "pattern": "^[\\w,\\s-]+\\.[A-Za-z]+$"}, "Framework": {"type": "structure", "members": {"arn": {"shape": "AuditManagerArn", "documentation": "<p> The Amazon Resource Name (ARN) of the framework. </p>"}, "id": {"shape": "UUID", "documentation": "<p> The unique identifier for the framework. </p>"}, "name": {"shape": "FrameworkName", "documentation": "<p> The name of the framework. </p>"}, "type": {"shape": "FrameworkType", "documentation": "<p> Specifies whether the framework is a standard framework or a custom framework.</p>"}, "complianceType": {"shape": "ComplianceType", "documentation": "<p> The compliance type that the framework supports, such as CIS or HIPAA. </p>"}, "description": {"shape": "FrameworkDescription", "documentation": "<p> The description of the framework. </p>"}, "logo": {"shape": "Filename", "documentation": "<p> The logo that's associated with the framework. </p>"}, "controlSources": {"shape": "ControlSources", "documentation": "<p> The control data sources where Audit Manager collects evidence from.</p>"}, "controlSets": {"shape": "ControlSets", "documentation": "<p> The control sets that are associated with the framework. </p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p> The time when the framework was created. </p>"}, "lastUpdatedAt": {"shape": "Timestamp", "documentation": "<p> The time when the framework was most recently updated. </p>"}, "createdBy": {"shape": "CreatedBy", "documentation": "<p> The user or role that created the framework. </p>"}, "lastUpdatedBy": {"shape": "LastUpdatedBy", "documentation": "<p> The user or role that most recently updated the framework. </p>"}, "tags": {"shape": "TagMap", "documentation": "<p> The tags that are associated with the framework. </p>"}}, "documentation": "<p> The file that's used to structure and automate Audit Manager assessments for a given compliance standard. </p>"}, "FrameworkDescription": {"type": "string", "max": 1000, "min": 1, "pattern": "^[\\w\\W\\s\\S]*$"}, "FrameworkMetadata": {"type": "structure", "members": {"name": {"shape": "AssessmentName", "documentation": "<p> The name of the framework. </p>"}, "description": {"shape": "AssessmentFrameworkDescription", "documentation": "<p> The description of the framework. </p>"}, "logo": {"shape": "Filename", "documentation": "<p> The logo that's associated with the framework. </p>"}, "complianceType": {"shape": "ComplianceType", "documentation": "<p> The compliance standard that's associated with the framework. For example, this could be PCI DSS or HIPAA. </p>"}}, "documentation": "<p> The metadata of a framework, such as the name, ID, or description. </p>"}, "FrameworkMetadataList": {"type": "list", "member": {"shape": "AssessmentFrameworkMetadata"}}, "FrameworkName": {"type": "string", "max": 300, "min": 1, "pattern": "^[^\\\\]*$"}, "FrameworkType": {"type": "string", "enum": ["Standard", "Custom"]}, "GenericArn": {"type": "string", "max": 2048, "min": 20, "pattern": "^arn:.*"}, "GetAccountStatusRequest": {"type": "structure", "members": {}}, "GetAccountStatusResponse": {"type": "structure", "members": {"status": {"shape": "Account<PERSON><PERSON><PERSON>", "documentation": "<p> The status of the Amazon Web Services account. </p>"}}}, "GetAssessmentFrameworkRequest": {"type": "structure", "required": ["frameworkId"], "members": {"frameworkId": {"shape": "UUID", "documentation": "<p> The identifier for the framework. </p>", "location": "uri", "locationName": "frameworkId"}}}, "GetAssessmentFrameworkResponse": {"type": "structure", "members": {"framework": {"shape": "Framework", "documentation": "<p> The framework that the <code>GetAssessmentFramework</code> API returned. </p>"}}}, "GetAssessmentReportUrlRequest": {"type": "structure", "required": ["assessmentReportId", "assessmentId"], "members": {"assessmentReportId": {"shape": "UUID", "documentation": "<p> The unique identifier for the assessment report. </p>", "location": "uri", "locationName": "assessmentReportId"}, "assessmentId": {"shape": "UUID", "documentation": "<p> The unique identifier for the assessment. </p>", "location": "uri", "locationName": "assessmentId"}}}, "GetAssessmentReportUrlResponse": {"type": "structure", "members": {"preSignedUrl": {"shape": "URL"}}}, "GetAssessmentRequest": {"type": "structure", "required": ["assessmentId"], "members": {"assessmentId": {"shape": "UUID", "documentation": "<p>The unique identifier for the assessment. </p>", "location": "uri", "locationName": "assessmentId"}}}, "GetAssessmentResponse": {"type": "structure", "members": {"assessment": {"shape": "Assessment"}, "userRole": {"shape": "Role"}}}, "GetChangeLogsRequest": {"type": "structure", "required": ["assessmentId"], "members": {"assessmentId": {"shape": "UUID", "documentation": "<p>The unique identifier for the assessment. </p>", "location": "uri", "locationName": "assessmentId"}, "controlSetId": {"shape": "ControlSetId", "documentation": "<p> The unique identifier for the control set. </p>", "location": "querystring", "locationName": "controlSetId"}, "controlId": {"shape": "UUID", "documentation": "<p> The unique identifier for the control. </p>", "location": "querystring", "locationName": "controlId"}, "nextToken": {"shape": "Token", "documentation": "<p> The pagination token that's used to fetch the next set of results. </p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>Represents the maximum number of results on a page or for an API request call. </p>", "location": "querystring", "locationName": "maxResults"}}}, "GetChangeLogsResponse": {"type": "structure", "members": {"changeLogs": {"shape": "ChangeLogs", "documentation": "<p>The list of user activity for the control. </p>"}, "nextToken": {"shape": "Token", "documentation": "<p>The pagination token that's used to fetch the next set of results. </p>"}}}, "GetControlRequest": {"type": "structure", "required": ["controlId"], "members": {"controlId": {"shape": "UUID", "documentation": "<p> The identifier for the control. </p>", "location": "uri", "locationName": "controlId"}}}, "GetControlResponse": {"type": "structure", "members": {"control": {"shape": "Control", "documentation": "<p> The details of the control that the <code>GetControl</code> API returned. </p>"}}}, "GetDelegationsRequest": {"type": "structure", "members": {"nextToken": {"shape": "Token", "documentation": "<p> The pagination token that's used to fetch the next set of results. </p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p> Represents the maximum number of results on a page or for an API request call. </p>", "location": "querystring", "locationName": "maxResults"}}}, "GetDelegationsResponse": {"type": "structure", "members": {"delegations": {"shape": "DelegationMetadataList", "documentation": "<p> The list of delegations that the <code>GetDelegations</code> API returned. </p>"}, "nextToken": {"shape": "Token", "documentation": "<p> The pagination token that's used to fetch the next set of results. </p>"}}}, "GetEvidenceByEvidenceFolderRequest": {"type": "structure", "required": ["assessmentId", "controlSetId", "evidenceFolderId"], "members": {"assessmentId": {"shape": "UUID", "documentation": "<p> The identifier for the assessment. </p>", "location": "uri", "locationName": "assessmentId"}, "controlSetId": {"shape": "ControlSetId", "documentation": "<p> The identifier for the control set. </p>", "location": "uri", "locationName": "controlSetId"}, "evidenceFolderId": {"shape": "UUID", "documentation": "<p> The unique identifier for the folder that the evidence is stored in. </p>", "location": "uri", "locationName": "evidenceFolderId"}, "nextToken": {"shape": "Token", "documentation": "<p> The pagination token that's used to fetch the next set of results. </p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p> Represents the maximum number of results on a page or for an API request call. </p>", "location": "querystring", "locationName": "maxResults"}}}, "GetEvidenceByEvidenceFolderResponse": {"type": "structure", "members": {"evidence": {"shape": "EvidenceList", "documentation": "<p> The list of evidence that the <code>GetEvidenceByEvidenceFolder</code> API returned. </p>"}, "nextToken": {"shape": "Token", "documentation": "<p> The pagination token that's used to fetch the next set of results. </p>"}}}, "GetEvidenceFileUploadUrlRequest": {"type": "structure", "required": ["fileName"], "members": {"fileName": {"shape": "ManualEvidenceLocalFileName", "documentation": "<p>The file that you want to upload. For a list of supported file formats, see <a href=\"https://docs.aws.amazon.com/audit-manager/latest/userguide/upload-evidence.html#supported-manual-evidence-files\">Supported file types for manual evidence</a> in the <i>Audit Manager User Guide</i>.</p>", "location": "querystring", "locationName": "fileName"}}}, "GetEvidenceFileUploadUrlResponse": {"type": "structure", "members": {"evidenceFileName": {"shape": "NonEmptyString", "documentation": "<p>The name of the uploaded manual evidence file that the presigned URL was generated for.</p>"}, "uploadUrl": {"shape": "NonEmptyString", "documentation": "<p>The presigned URL that was generated.</p>"}}, "sensitive": true}, "GetEvidenceFolderRequest": {"type": "structure", "required": ["assessmentId", "controlSetId", "evidenceFolderId"], "members": {"assessmentId": {"shape": "UUID", "documentation": "<p> The unique identifier for the assessment. </p>", "location": "uri", "locationName": "assessmentId"}, "controlSetId": {"shape": "ControlSetId", "documentation": "<p> The unique identifier for the control set. </p>", "location": "uri", "locationName": "controlSetId"}, "evidenceFolderId": {"shape": "UUID", "documentation": "<p> The unique identifier for the folder that the evidence is stored in. </p>", "location": "uri", "locationName": "evidenceFolderId"}}}, "GetEvidenceFolderResponse": {"type": "structure", "members": {"evidenceFolder": {"shape": "AssessmentEvidenceFolder", "documentation": "<p> The folder that the evidence is stored in. </p>"}}}, "GetEvidenceFoldersByAssessmentControlRequest": {"type": "structure", "required": ["assessmentId", "controlSetId", "controlId"], "members": {"assessmentId": {"shape": "UUID", "documentation": "<p> The identifier for the assessment. </p>", "location": "uri", "locationName": "assessmentId"}, "controlSetId": {"shape": "ControlSetId", "documentation": "<p> The identifier for the control set. </p>", "location": "uri", "locationName": "controlSetId"}, "controlId": {"shape": "UUID", "documentation": "<p> The identifier for the control. </p>", "location": "uri", "locationName": "controlId"}, "nextToken": {"shape": "Token", "documentation": "<p> The pagination token that's used to fetch the next set of results. </p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p> Represents the maximum number of results on a page or for an API request call. </p>", "location": "querystring", "locationName": "maxResults"}}}, "GetEvidenceFoldersByAssessmentControlResponse": {"type": "structure", "members": {"evidenceFolders": {"shape": "AssessmentEvidenceFolders", "documentation": "<p> The list of evidence folders that the <code>GetEvidenceFoldersByAssessmentControl</code> API returned. </p>"}, "nextToken": {"shape": "Token", "documentation": "<p> The pagination token that's used to fetch the next set of results. </p>"}}}, "GetEvidenceFoldersByAssessmentRequest": {"type": "structure", "required": ["assessmentId"], "members": {"assessmentId": {"shape": "UUID", "documentation": "<p> The unique identifier for the assessment. </p>", "location": "uri", "locationName": "assessmentId"}, "nextToken": {"shape": "Token", "documentation": "<p> The pagination token that's used to fetch the next set of results. </p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p> Represents the maximum number of results on a page or for an API request call. </p>", "location": "querystring", "locationName": "maxResults"}}}, "GetEvidenceFoldersByAssessmentResponse": {"type": "structure", "members": {"evidenceFolders": {"shape": "AssessmentEvidenceFolders", "documentation": "<p> The list of evidence folders that the <code>GetEvidenceFoldersByAssessment</code> API returned. </p>"}, "nextToken": {"shape": "Token", "documentation": "<p> The pagination token that's used to fetch the next set of results. </p>"}}}, "GetEvidenceRequest": {"type": "structure", "required": ["assessmentId", "controlSetId", "evidenceFolderId", "evidenceId"], "members": {"assessmentId": {"shape": "UUID", "documentation": "<p> The unique identifier for the assessment. </p>", "location": "uri", "locationName": "assessmentId"}, "controlSetId": {"shape": "ControlSetId", "documentation": "<p> The unique identifier for the control set. </p>", "location": "uri", "locationName": "controlSetId"}, "evidenceFolderId": {"shape": "UUID", "documentation": "<p> The unique identifier for the folder that the evidence is stored in. </p>", "location": "uri", "locationName": "evidenceFolderId"}, "evidenceId": {"shape": "UUID", "documentation": "<p> The unique identifier for the evidence. </p>", "location": "uri", "locationName": "evidenceId"}}}, "GetEvidenceResponse": {"type": "structure", "members": {"evidence": {"shape": "Evidence", "documentation": "<p> The evidence that the <code>GetEvidence</code> API returned. </p>"}}}, "GetInsightsByAssessmentRequest": {"type": "structure", "required": ["assessmentId"], "members": {"assessmentId": {"shape": "UUID", "documentation": "<p>The unique identifier for the assessment. </p>", "location": "uri", "locationName": "assessmentId"}}}, "GetInsightsByAssessmentResponse": {"type": "structure", "members": {"insights": {"shape": "InsightsByAssessment", "documentation": "<p> The assessment analytics data that the <code>GetInsightsByAssessment</code> API returned. </p>"}}}, "GetInsightsRequest": {"type": "structure", "members": {}}, "GetInsightsResponse": {"type": "structure", "members": {"insights": {"shape": "Insights", "documentation": "<p>The analytics data that the <code>GetInsights</code> API returned. </p>"}}}, "GetOrganizationAdminAccountRequest": {"type": "structure", "members": {}}, "GetOrganizationAdminAccountResponse": {"type": "structure", "members": {"adminAccountId": {"shape": "AccountId", "documentation": "<p> The identifier for the administrator account. </p>"}, "organizationId": {"shape": "organizationId", "documentation": "<p> The identifier for the organization. </p>"}}}, "GetServicesInScopeRequest": {"type": "structure", "members": {}}, "GetServicesInScopeResponse": {"type": "structure", "members": {"serviceMetadata": {"shape": "ServiceMetadataList", "documentation": "<p> The metadata that's associated with the Amazon Web Services service. </p>"}}}, "GetSettingsRequest": {"type": "structure", "required": ["attribute"], "members": {"attribute": {"shape": "SettingAttribute", "documentation": "<p> The list of setting attribute enum values. </p>", "location": "uri", "locationName": "attribute"}}}, "GetSettingsResponse": {"type": "structure", "members": {"settings": {"shape": "Settings", "documentation": "<p> The settings object that holds all supported Audit Manager settings. </p>"}}}, "HyperlinkName": {"type": "string", "max": 200, "min": 1, "pattern": "^[\\w\\W\\s\\S]*$"}, "IamArn": {"type": "string", "max": 2048, "min": 20, "pattern": "^arn:.*:iam:.*"}, "Insights": {"type": "structure", "members": {"activeAssessmentsCount": {"shape": "NullableInteger", "documentation": "<p>The number of active assessments in Audit Manager. </p>"}, "noncompliantEvidenceCount": {"shape": "NullableInteger", "documentation": "<p>The number of compliance check evidence that Audit Manager classified as non-compliant on the <code>lastUpdated</code> date. This includes evidence that was collected from Security Hub with a <i>Fail</i> ruling, or collected from Config with a <i>Non-compliant</i> ruling. </p>"}, "compliantEvidenceCount": {"shape": "NullableInteger", "documentation": "<p>The number of compliance check evidence that Audit Manager classified as compliant on the <code>lastUpdated</code> date. This includes evidence that was collected from Security Hub with a <i>Pass</i> ruling, or collected from Config with a <i>Compliant</i> ruling. </p>"}, "inconclusiveEvidenceCount": {"shape": "NullableInteger", "documentation": "<p>The number of evidence without a compliance check ruling. Evidence is inconclusive when the associated control uses Security Hub or Config as a data source but you didn't enable those services. This is also the case when a control uses a data source that doesn’t support compliance checks (for example: manual evidence, API calls, or CloudTrail). </p> <note> <p>If evidence has a compliance check status of <i>not applicable</i>, it's classed as <i>inconclusive</i> in <code>Insights</code> data.</p> </note>"}, "assessmentControlsCountByNoncompliantEvidence": {"shape": "NullableInteger", "documentation": "<p>The number of assessment controls that collected non-compliant evidence on the <code>lastUpdated</code> date. </p>"}, "totalAssessmentControlsCount": {"shape": "NullableInteger", "documentation": "<p>The total number of controls across all active assessments. </p>"}, "lastUpdated": {"shape": "Timestamp", "documentation": "<p>The time when the cross-assessment insights were last updated. </p>"}}, "documentation": "<p>A summary of the latest analytics data for all your active assessments. </p> <p>This summary is a snapshot of the data that your active assessments collected on the <code>lastUpdated</code> date. It’s important to understand that the following totals are daily counts based on this date — they aren’t a total sum to date. </p> <p>The <code>Insights</code> data is eventually consistent. This means that, when you read data from <code>Insights</code>, the response might not instantly reflect the results of a recently completed write or update operation. If you repeat your read request after a few hours, the response should return the latest data.</p> <note> <p>If you delete an assessment or change its status to inactive, <code>InsightsByAssessment</code> includes data for that assessment as follows.</p> <ul> <li> <p> <b>Inactive assessments</b> - If Audit Manager collected evidence for your assessment before you changed it inactive, that evidence is included in the <code>InsightsByAssessment</code> counts for that day.</p> </li> <li> <p> <b>Deleted assessments</b> - If Audit Manager collected evidence for your assessment before you deleted it, that evidence isn't included in the <code>InsightsByAssessment</code> counts for that day.</p> </li> </ul> </note>"}, "InsightsByAssessment": {"type": "structure", "members": {"noncompliantEvidenceCount": {"shape": "NullableInteger", "documentation": "<p>The number of compliance check evidence that Audit Manager classified as non-compliant. This includes evidence that was collected from Security Hub with a <i>Fail</i> ruling, or collected from Config with a <i>Non-compliant</i> ruling. </p>"}, "compliantEvidenceCount": {"shape": "NullableInteger", "documentation": "<p>The number of compliance check evidence that Audit Manager classified as compliant. This includes evidence that was collected from Security Hub with a <i>Pass</i> ruling, or collected from Config with a <i>Compliant</i> ruling. </p>"}, "inconclusiveEvidenceCount": {"shape": "NullableInteger", "documentation": "<p>The amount of evidence without a compliance check ruling. Evidence is inconclusive if the associated control uses Security Hub or Config as a data source and you didn't enable those services. This is also the case if a control uses a data source that doesn’t support compliance checks (for example, manual evidence, API calls, or CloudTrail). </p> <note> <p>If evidence has a compliance check status of <i>not applicable</i>, it's classified as <i>inconclusive</i> in <code>InsightsByAssessment</code> data.</p> </note>"}, "assessmentControlsCountByNoncompliantEvidence": {"shape": "NullableInteger", "documentation": "<p>The number of assessment controls that collected non-compliant evidence on the <code>lastUpdated</code> date. </p>"}, "totalAssessmentControlsCount": {"shape": "NullableInteger", "documentation": "<p>The total number of controls in the assessment. </p>"}, "lastUpdated": {"shape": "Timestamp", "documentation": "<p>The time when the assessment insights were last updated.</p>"}}, "documentation": "<p>A summary of the latest analytics data for a specific active assessment.</p> <p>This summary is a snapshot of the data that was collected on the <code>lastUpdated</code> date. It’s important to understand that the totals in <code>InsightsByAssessment</code> are daily counts based on this date — they aren’t a total sum to date. </p> <p>The <code>InsightsByAssessment</code> data is eventually consistent. This means that when you read data from <code>InsightsByAssessment</code>, the response might not instantly reflect the results of a recently completed write or update operation. If you repeat your read request after a few hours, the response returns the latest data.</p> <note> <p>If you delete an assessment or change its status to inactive, <code>InsightsByAssessment</code> includes data for that assessment as follows.</p> <ul> <li> <p> <b>Inactive assessments</b> - If Audit Manager collected evidence for your assessment before you changed it inactive, that evidence is included in the <code>InsightsByAssessment</code> counts for that day.</p> </li> <li> <p> <b>Deleted assessments</b> - If Audit Manager collected evidence for your assessment before you deleted it, that evidence isn't included in the <code>InsightsByAssessment</code> counts for that day.</p> </li> </ul> </note>"}, "Integer": {"type": "integer"}, "InternalServerException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p> An internal service error occurred during the processing of your request. Try again later. </p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true}, "KeywordInputType": {"type": "string", "enum": ["SELECT_FROM_LIST", "UPLOAD_FILE", "INPUT_TEXT"]}, "KeywordValue": {"type": "string", "max": 100, "min": 1, "pattern": "^[a-zA-Z_0-9-\\s().:\\/]+$"}, "Keywords": {"type": "list", "member": {"shape": "KeywordValue"}}, "KmsKey": {"type": "string", "max": 2048, "min": 7, "pattern": "^arn:.*:kms:.*|DEFAULT"}, "LastUpdatedBy": {"type": "string", "max": 100, "min": 1, "pattern": "^[a-zA-Z0-9\\s-_()\\[\\]]+$", "sensitive": true}, "ListAssessmentControlInsightsByControlDomainRequest": {"type": "structure", "required": ["controlDomainId", "assessmentId"], "members": {"controlDomainId": {"shape": "ControlDomainId", "documentation": "<p>The unique identifier for the control domain. </p> <p>Audit Manager supports the control domains that are provided by Amazon Web Services Control Catalog. For information about how to find a list of available control domains, see <a href=\"https://docs.aws.amazon.com/controlcatalog/latest/APIReference/API_ListDomains.html\"> <code>ListDomains</code> </a> in the Amazon Web Services Control Catalog API Reference.</p>", "location": "querystring", "locationName": "controlDomainId"}, "assessmentId": {"shape": "UUID", "documentation": "<p>The unique identifier for the active assessment. </p>", "location": "querystring", "locationName": "assessmentId"}, "nextToken": {"shape": "Token", "documentation": "<p>The pagination token that's used to fetch the next set of results. </p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>Represents the maximum number of results on a page or for an API request call. </p>", "location": "querystring", "locationName": "maxResults"}}}, "ListAssessmentControlInsightsByControlDomainResponse": {"type": "structure", "members": {"controlInsightsByAssessment": {"shape": "ControlInsightsMetadataByAssessment", "documentation": "<p>The assessment control analytics data that the <code>ListAssessmentControlInsightsByControlDomain</code> API returned. </p>"}, "nextToken": {"shape": "Token", "documentation": "<p>The pagination token that's used to fetch the next set of results. </p>"}}}, "ListAssessmentFrameworkShareRequestsRequest": {"type": "structure", "required": ["requestType"], "members": {"requestType": {"shape": "ShareRequestType", "documentation": "<p> Specifies whether the share request is a sent request or a received request.</p>", "location": "querystring", "locationName": "requestType"}, "nextToken": {"shape": "Token", "documentation": "<p> The pagination token that's used to fetch the next set of results. </p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p> Represents the maximum number of results on a page or for an API request call. </p>", "location": "querystring", "locationName": "maxResults"}}}, "ListAssessmentFrameworkShareRequestsResponse": {"type": "structure", "members": {"assessmentFrameworkShareRequests": {"shape": "AssessmentFrameworkShareRequestList", "documentation": "<p> The list of share requests that the <code>ListAssessmentFrameworkShareRequests</code> API returned. </p>"}, "nextToken": {"shape": "Token", "documentation": "<p> The pagination token that's used to fetch the next set of results. </p>"}}}, "ListAssessmentFrameworksRequest": {"type": "structure", "required": ["frameworkType"], "members": {"frameworkType": {"shape": "FrameworkType", "documentation": "<p> The type of framework, such as a standard framework or a custom framework. </p>", "location": "querystring", "locationName": "frameworkType"}, "nextToken": {"shape": "Token", "documentation": "<p> The pagination token that's used to fetch the next set of results. </p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p> Represents the maximum number of results on a page or for an API request call. </p>", "location": "querystring", "locationName": "maxResults"}}}, "ListAssessmentFrameworksResponse": {"type": "structure", "members": {"frameworkMetadataList": {"shape": "FrameworkMetadataList", "documentation": "<p> A list of metadata that the <code>ListAssessmentFrameworks</code> API returns for each framework.</p>"}, "nextToken": {"shape": "Token", "documentation": "<p> The pagination token that's used to fetch the next set of results. </p>"}}}, "ListAssessmentMetadata": {"type": "list", "member": {"shape": "AssessmentMetadataItem"}}, "ListAssessmentReportsRequest": {"type": "structure", "members": {"nextToken": {"shape": "Token", "documentation": "<p> The pagination token that's used to fetch the next set of results. </p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p> Represents the maximum number of results on a page or for an API request call. </p>", "location": "querystring", "locationName": "maxResults"}}}, "ListAssessmentReportsResponse": {"type": "structure", "members": {"assessmentReports": {"shape": "AssessmentReportsMetadata", "documentation": "<p> The list of assessment reports that the <code>ListAssessmentReports</code> API returned. </p>"}, "nextToken": {"shape": "Token", "documentation": "<p> The pagination token that's used to fetch the next set of results. </p>"}}}, "ListAssessmentsRequest": {"type": "structure", "members": {"status": {"shape": "AssessmentStatus", "documentation": "<p> The current status of the assessment.</p>", "location": "querystring", "locationName": "status"}, "nextToken": {"shape": "Token", "documentation": "<p> The pagination token that's used to fetch the next set of results. </p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p> Represents the maximum number of results on a page or for an API request call. </p>", "location": "querystring", "locationName": "maxResults"}}}, "ListAssessmentsResponse": {"type": "structure", "members": {"assessmentMetadata": {"shape": "ListAssessmentMetadata", "documentation": "<p>The metadata that the <code>ListAssessments</code> API returns for each assessment.</p>"}, "nextToken": {"shape": "Token", "documentation": "<p> The pagination token that's used to fetch the next set of results. </p>"}}}, "ListControlDomainInsightsByAssessmentRequest": {"type": "structure", "required": ["assessmentId"], "members": {"assessmentId": {"shape": "UUID", "documentation": "<p>The unique identifier for the active assessment. </p>", "location": "querystring", "locationName": "assessmentId"}, "nextToken": {"shape": "Token", "documentation": "<p>The pagination token that's used to fetch the next set of results. </p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>Represents the maximum number of results on a page or for an API request call. </p>", "location": "querystring", "locationName": "maxResults"}}}, "ListControlDomainInsightsByAssessmentResponse": {"type": "structure", "members": {"controlDomainInsights": {"shape": "ControlDomainInsightsList", "documentation": "<p>The control domain analytics data that the <code>ListControlDomainInsightsByAssessment</code> API returned. </p>"}, "nextToken": {"shape": "Token", "documentation": "<p>The pagination token that's used to fetch the next set of results. </p>"}}}, "ListControlDomainInsightsRequest": {"type": "structure", "members": {"nextToken": {"shape": "Token", "documentation": "<p>The pagination token that's used to fetch the next set of results. </p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>Represents the maximum number of results on a page or for an API request call. </p>", "location": "querystring", "locationName": "maxResults"}}}, "ListControlDomainInsightsResponse": {"type": "structure", "members": {"controlDomainInsights": {"shape": "ControlDomainInsightsList", "documentation": "<p>The control domain analytics data that the <code>ListControlDomainInsights</code> API returned. </p>"}, "nextToken": {"shape": "Token", "documentation": "<p>The pagination token that's used to fetch the next set of results. </p>"}}}, "ListControlInsightsByControlDomainRequest": {"type": "structure", "required": ["controlDomainId"], "members": {"controlDomainId": {"shape": "ControlDomainId", "documentation": "<p>The unique identifier for the control domain. </p> <p>Audit Manager supports the control domains that are provided by Amazon Web Services Control Catalog. For information about how to find a list of available control domains, see <a href=\"https://docs.aws.amazon.com/controlcatalog/latest/APIReference/API_ListDomains.html\"> <code>ListDomains</code> </a> in the Amazon Web Services Control Catalog API Reference.</p>", "location": "querystring", "locationName": "controlDomainId"}, "nextToken": {"shape": "Token", "documentation": "<p>The pagination token that's used to fetch the next set of results. </p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>Represents the maximum number of results on a page or for an API request call. </p>", "location": "querystring", "locationName": "maxResults"}}}, "ListControlInsightsByControlDomainResponse": {"type": "structure", "members": {"controlInsightsMetadata": {"shape": "ControlInsightsMetadata", "documentation": "<p>The control analytics data that the <code>ListControlInsightsByControlDomain</code> API returned. </p>"}, "nextToken": {"shape": "Token", "documentation": "<p>The pagination token that's used to fetch the next set of results. </p>"}}}, "ListControlsRequest": {"type": "structure", "required": ["controlType"], "members": {"controlType": {"shape": "ControlType", "documentation": "<p>A filter that narrows the list of controls to a specific type. </p>", "location": "querystring", "locationName": "controlType"}, "nextToken": {"shape": "Token", "documentation": "<p>The pagination token that's used to fetch the next set of results. </p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results on a page or for an API request call. </p>", "location": "querystring", "locationName": "maxResults"}, "controlCatalogId": {"shape": "ControlCatalogId", "documentation": "<p>A filter that narrows the list of controls to a specific resource from the Amazon Web Services Control Catalog. </p> <p>To use this parameter, specify the ARN of the Control Catalog resource. You can specify either a control domain, a control objective, or a common control. For information about how to find the ARNs for these resources, see <a href=\"https://docs.aws.amazon.com/controlcatalog/latest/APIReference/API_ListDomains.html\"> <code>ListDomains</code> </a>, <a href=\"https://docs.aws.amazon.com/controlcatalog/latest/APIReference/API_ListObjectives.html\"> <code>ListObjectives</code> </a>, and <a href=\"https://docs.aws.amazon.com/controlcatalog/latest/APIReference/API_ListCommonControls.html\"> <code>ListCommonControls</code> </a>.</p> <note> <p>You can only filter by one Control Catalog resource at a time. Specifying multiple resource ARNs isn’t currently supported. If you want to filter by more than one ARN, we recommend that you run the <code>ListControls</code> operation separately for each ARN. </p> </note> <p>Alternatively, specify <code>UNCATEGORIZED</code> to list controls that aren't mapped to a Control Catalog resource. For example, this operation might return a list of custom controls that don't belong to any control domain or control objective.</p>", "location": "querystring", "locationName": "controlCatalogId"}}}, "ListControlsResponse": {"type": "structure", "members": {"controlMetadataList": {"shape": "ControlMetadataList", "documentation": "<p> A list of metadata that the <code>ListControls</code> API returns for each control.</p>"}, "nextToken": {"shape": "Token", "documentation": "<p>The pagination token that's used to fetch the next set of results. </p>"}}}, "ListKeywordsForDataSourceRequest": {"type": "structure", "required": ["source"], "members": {"source": {"shape": "DataSourceType", "documentation": "<p>The control mapping data source that the keywords apply to. </p>", "location": "querystring", "locationName": "source"}, "nextToken": {"shape": "Token", "documentation": "<p> The pagination token that's used to fetch the next set of results. </p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p> Represents the maximum number of results on a page or for an API request call. </p>", "location": "querystring", "locationName": "maxResults"}}}, "ListKeywordsForDataSourceResponse": {"type": "structure", "members": {"keywords": {"shape": "Keywords", "documentation": "<p>The list of keywords for the control mapping source.</p>"}, "nextToken": {"shape": "Token", "documentation": "<p> The pagination token that's used to fetch the next set of results. </p>"}}}, "ListNotificationsRequest": {"type": "structure", "members": {"nextToken": {"shape": "Token", "documentation": "<p> The pagination token that's used to fetch the next set of results. </p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p> Represents the maximum number of results on a page or for an API request call. </p>", "location": "querystring", "locationName": "maxResults"}}}, "ListNotificationsResponse": {"type": "structure", "members": {"notifications": {"shape": "Notifications", "documentation": "<p> The returned list of notifications. </p>"}, "nextToken": {"shape": "Token", "documentation": "<p> The pagination token that's used to fetch the next set of results. </p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "AuditManagerArn", "documentation": "<p> The Amazon Resource Name (ARN) of the resource. </p>", "location": "uri", "locationName": "resourceArn"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"tags": {"shape": "TagMap", "documentation": "<p> The list of tags that the <code>ListTagsForResource</code> API returned. </p>"}}}, "ManualEvidence": {"type": "structure", "members": {"s3ResourcePath": {"shape": "S3Url", "documentation": "<p>The S3 URL of the object that's imported as manual evidence. </p>"}, "textResponse": {"shape": "ManualEvidenceTextResponse", "documentation": "<p>The plain text response that's entered and saved as manual evidence.</p>"}, "evidenceFileName": {"shape": "ManualEvidenceLocalFileName", "documentation": "<p>The name of the file that's uploaded as manual evidence. This name is populated using the <code>evidenceFileName</code> value from the <a href=\"https://docs.aws.amazon.com/audit-manager/latest/APIReference/API_GetEvidenceFileUploadUrl.html\"> <code>GetEvidenceFileUploadUrl</code> </a> API response.</p>"}}, "documentation": "<p> Evidence that's manually added to a control in Audit Manager. <code>manualEvidence</code> can be one of the following: <code>evidenceFileName</code>, <code>s3ResourcePath</code>, or <code>textResponse</code>.</p>"}, "ManualEvidenceList": {"type": "list", "member": {"shape": "ManualEvidence"}, "max": 50, "min": 1}, "ManualEvidenceLocalFileName": {"type": "string", "max": 300, "min": 1, "pattern": "[^\\/]*", "sensitive": true}, "ManualEvidenceTextResponse": {"type": "string", "max": 1000, "min": 1, "pattern": "^[\\w\\W\\s\\S]*$", "sensitive": true}, "MaxResults": {"type": "integer", "documentation": "Max results in the page.", "max": 1000, "min": 1}, "NonEmptyString": {"type": "string", "max": 2048, "min": 1, "pattern": ".*\\S.*"}, "Notification": {"type": "structure", "members": {"id": {"shape": "TimestampUUID", "documentation": "<p> The unique identifier for the notification. </p>"}, "assessmentId": {"shape": "UUID", "documentation": "<p> The identifier for the assessment. </p>"}, "assessmentName": {"shape": "AssessmentName", "documentation": "<p> The name of the related assessment. </p>"}, "controlSetId": {"shape": "ControlSetId", "documentation": "<p> The identifier for the control set. </p>"}, "controlSetName": {"shape": "NonEmptyString", "documentation": "<p> Specifies the name of the control set that the notification is about. </p>"}, "description": {"shape": "NonEmptyString", "documentation": "<p> The description of the notification. </p>"}, "eventTime": {"shape": "Timestamp", "documentation": "<p> The time when the notification was sent. </p>"}, "source": {"shape": "NonEmptyString", "documentation": "<p> The sender of the notification. </p>"}}, "documentation": "<p> The notification that informs a user of an update in Audit Manager. For example, this includes the notification that's sent when a control set is delegated for review. </p>"}, "Notifications": {"type": "list", "member": {"shape": "Notification"}}, "NullableInteger": {"type": "integer"}, "ObjectTypeEnum": {"type": "string", "enum": ["ASSESSMENT", "CONTROL_SET", "CONTROL", "DELEGATION", "ASSESSMENT_REPORT"]}, "QueryStatement": {"type": "string", "max": 10000, "min": 1, "pattern": "(?s).*"}, "Region": {"type": "string", "pattern": "^[a-z]{2}-[a-z]+-[0-9]{1}$"}, "RegisterAccountRequest": {"type": "structure", "members": {"kmsKey": {"shape": "KmsKey", "documentation": "<p> The KMS key details. </p>"}, "delegatedAdminAccount": {"shape": "AccountId", "documentation": "<p> The delegated administrator account for Audit Manager. </p>"}}}, "RegisterAccountResponse": {"type": "structure", "members": {"status": {"shape": "Account<PERSON><PERSON><PERSON>", "documentation": "<p> The status of the account registration request. </p>"}}}, "RegisterOrganizationAdminAccountRequest": {"type": "structure", "required": ["adminAccountId"], "members": {"adminAccountId": {"shape": "AccountId", "documentation": "<p> The identifier for the delegated administrator account. </p>"}}}, "RegisterOrganizationAdminAccountResponse": {"type": "structure", "members": {"adminAccountId": {"shape": "AccountId", "documentation": "<p> The identifier for the delegated administrator account. </p>"}, "organizationId": {"shape": "organizationId", "documentation": "<p> The identifier for the organization. </p>"}}}, "Resource": {"type": "structure", "members": {"arn": {"shape": "GenericArn", "documentation": "<p> The Amazon Resource Name (ARN) for the resource. </p>"}, "value": {"shape": "String", "documentation": "<p> The value of the resource. </p>"}, "complianceCheck": {"shape": "String", "documentation": "<p> The evaluation status for a resource that was assessed when collecting compliance check evidence. </p> <ul> <li> <p>Audit Manager classes the resource as non-compliant if Security Hub reports a <i>Fail</i> result, or if Config reports a <i>Non-compliant</i> result.</p> </li> <li> <p>Audit Manager classes the resource as compliant if Security Hub reports a <i>Pass</i> result, or if Config reports a <i>Compliant</i> result.</p> </li> <li> <p>If a compliance check isn't available or applicable, then no compliance evaluation can be made for that resource. This is the case if a resource assessment uses Config or Security Hub as the underlying data source type, but those services aren't enabled. This is also the case if the resource assessment uses an underlying data source type that doesn't support compliance checks (such as manual evidence, Amazon Web Services API calls, or CloudTrail). </p> </li> </ul>"}}, "documentation": "<p> A system asset that's evaluated in an Audit Manager assessment. </p>"}, "ResourceNotFoundException": {"type": "structure", "required": ["message", "resourceId", "resourceType"], "members": {"message": {"shape": "String"}, "resourceId": {"shape": "String", "documentation": "<p> The unique identifier for the resource. </p>"}, "resourceType": {"shape": "String", "documentation": "<p> The type of resource that's affected by the error. </p>"}}, "documentation": "<p> The resource that's specified in the request can't be found. </p>", "error": {"httpStatusCode": 404}, "exception": true}, "Resources": {"type": "list", "member": {"shape": "Resource"}}, "Role": {"type": "structure", "required": ["roleType", "roleArn"], "members": {"roleType": {"shape": "RoleType", "documentation": "<p> The type of customer persona. </p> <note> <p>In <code>CreateAssessment</code>, <code>roleType</code> can only be <code>PROCESS_OWNER</code>. </p> <p>In <code>UpdateSettings</code>, <code>roleType</code> can only be <code>PROCESS_OWNER</code>.</p> <p>In <code>BatchCreateDelegationByAssessment</code>, <code>roleType</code> can only be <code>RESOURCE_OWNER</code>.</p> </note>"}, "roleArn": {"shape": "IamArn", "documentation": "<p> The Amazon Resource Name (ARN) of the IAM role. </p>"}}, "documentation": "<p> The wrapper that contains the Audit Manager role information of the current user. This includes the role type and IAM Amazon Resource Name (ARN). </p>"}, "RoleType": {"type": "string", "enum": ["PROCESS_OWNER", "RESOURCE_OWNER"]}, "Roles": {"type": "list", "member": {"shape": "Role"}, "sensitive": true}, "S3Url": {"type": "string", "max": 1024, "min": 1, "pattern": "^(S|s)3:\\/\\/[a-zA-Z0-9\\-\\.\\(\\)\\'\\*\\_\\!\\/]+$"}, "SNSTopic": {"type": "string", "max": 255, "min": 1, "pattern": "^[a-zA-Z0-9-_\\(\\)\\[\\]]+$", "sensitive": true}, "Scope": {"type": "structure", "members": {"awsAccounts": {"shape": "AWSAccounts", "documentation": "<p> The Amazon Web Services accounts that are included in the scope of the assessment. </p>"}, "awsServices": {"shape": "AWSServices", "documentation": "<p> The Amazon Web Services services that are included in the scope of the assessment. </p> <important> <p>This API parameter is no longer supported. If you use this parameter to specify one or more Amazon Web Services services, Audit Manager ignores this input. Instead, the value for <code>awsServices</code> will show as empty.</p> </important>", "deprecated": true, "deprecatedMessage": "You can't specify services in scope when creating/updating an assessment. If you use the parameter to specify one or more AWS services, Audit Manager ignores the input. Instead the value of the parameter will show as empty indicating that the services are defined and managed by Audit Manager."}}, "documentation": "<p> The wrapper that contains the Amazon Web Services accounts that are in scope for the assessment. </p> <note> <p>You no longer need to specify which Amazon Web Services services are in scope when you create or update an assessment. Audit Manager infers the services in scope by examining your assessment controls and their data sources, and then mapping this information to the relevant Amazon Web Services services. </p> <p>If an underlying data source changes for your assessment, we automatically update the services scope as needed to reflect the correct Amazon Web Services services. This ensures that your assessment collects accurate and comprehensive evidence about all of the relevant services in your AWS environment.</p> </note>", "sensitive": true}, "ServiceMetadata": {"type": "structure", "members": {"name": {"shape": "AWSServiceName", "documentation": "<p> The name of the Amazon Web Services service. </p>"}, "displayName": {"shape": "NonEmptyString", "documentation": "<p> The display name of the Amazon Web Services service. </p>"}, "description": {"shape": "NonEmptyString", "documentation": "<p> The description of the Amazon Web Services service. </p>"}, "category": {"shape": "NonEmptyString", "documentation": "<p> The category that the Amazon Web Services service belongs to, such as compute, storage, or database. </p>"}}, "documentation": "<p> The metadata that's associated with the Amazon Web Services service. </p>"}, "ServiceMetadataList": {"type": "list", "member": {"shape": "ServiceMetadata"}}, "ServiceQuotaExceededException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>You've reached your account quota for this resource type. To perform the requested action, delete some existing resources or <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws_service_limits.html\">request a quota increase</a> from the Service Quotas console. For a list of Audit Manager service quotas, see <a href=\"https://docs.aws.amazon.com/audit-manager/latest/userguide/service-quotas.html\">Quotas and restrictions for Audit Manager</a>.</p>", "error": {"httpStatusCode": 402}, "exception": true}, "SettingAttribute": {"type": "string", "enum": ["ALL", "IS_AWS_ORG_ENABLED", "SNS_TOPIC", "DEFAULT_ASSESSMENT_REPORTS_DESTINATION", "DEFAULT_PROCESS_OWNERS", "EVIDENCE_FINDER_ENABLEMENT", "DEREGISTRATION_POLICY", "DEFAULT_EXPORT_DESTINATION"]}, "Settings": {"type": "structure", "members": {"isAwsOrgEnabled": {"shape": "Boolean", "documentation": "<p> Specifies whether Organizations is enabled. </p>"}, "snsTopic": {"shape": "SNSTopic", "documentation": "<p> The designated Amazon Simple Notification Service (Amazon SNS) topic. </p>"}, "defaultAssessmentReportsDestination": {"shape": "AssessmentReportsDestination", "documentation": "<p>The default S3 destination bucket for storing assessment reports.</p>"}, "defaultProcessOwners": {"shape": "Roles", "documentation": "<p> The designated default audit owners. </p>"}, "kmsKey": {"shape": "KmsKey", "documentation": "<p> The KMS key details. </p>"}, "evidenceFinderEnablement": {"shape": "EvidenceFinderEnablement", "documentation": "<p>The current evidence finder status and event data store details.</p>"}, "deregistrationPolicy": {"shape": "DeregistrationPolicy", "documentation": "<p>The deregistration policy for your Audit Manager data. You can use this attribute to determine how your data is handled when you deregister Audit Manager.</p>"}, "defaultExportDestination": {"shape": "DefaultExportDestination", "documentation": "<p>The default S3 destination bucket for storing evidence finder exports.</p>"}}, "documentation": "<p> The settings object that holds all supported Audit Manager settings. </p>"}, "ShareRequestAction": {"type": "string", "enum": ["ACCEPT", "DECLINE", "REVOKE"]}, "ShareRequestComment": {"type": "string", "max": 500, "pattern": "^[\\w\\W\\s\\S]*$"}, "ShareRequestStatus": {"type": "string", "enum": ["ACTIVE", "REPLICATING", "SHARED", "EXPIRING", "FAILED", "EXPIRED", "DECLINED", "REVOKED"]}, "ShareRequestType": {"type": "string", "enum": ["SENT", "RECEIVED"]}, "SnsArn": {"type": "string", "max": 2048, "min": 4, "pattern": "^arn:.*:sns:.*|NONE"}, "SourceDescription": {"type": "string", "max": 1000, "pattern": "^[\\w\\W\\s\\S]*$"}, "SourceFrequency": {"type": "string", "enum": ["DAILY", "WEEKLY", "MONTHLY"]}, "SourceKeyword": {"type": "structure", "members": {"keywordInputType": {"shape": "KeywordInputType", "documentation": "<p> The input method for the keyword. </p> <ul> <li> <p> <code>SELECT_FROM_LIST</code> is used when mapping a data source for automated evidence.</p> <ul> <li> <p>When <code>keywordInputType</code> is <code>SELECT_FROM_LIST</code>, a keyword must be selected to collect automated evidence. For example, this keyword can be a CloudTrail event name, a rule name for Config, a Security Hub control, or the name of an Amazon Web Services API call.</p> </li> </ul> </li> <li> <p> <code>UPLOAD_FILE</code> and <code>INPUT_TEXT</code> are only used when mapping a data source for manual evidence.</p> <ul> <li> <p>When <code>keywordInputType</code> is <code>UPLOAD_FILE</code>, a file must be uploaded as manual evidence.</p> </li> <li> <p>When <code>keywordInputType</code> is <code>INPUT_TEXT</code>, text must be entered as manual evidence.</p> </li> </ul> </li> </ul>"}, "keywordValue": {"shape": "KeywordValue", "documentation": "<p> The value of the keyword that's used when mapping a control data source. For example, this can be a CloudTrail event name, a rule name for Config, a Security Hub control, or the name of an Amazon Web Services API call. </p> <p>If you’re mapping a data source to a rule in Config, the <code>keywordValue</code> that you specify depends on the type of rule:</p> <ul> <li> <p>For <a href=\"https://docs.aws.amazon.com/config/latest/developerguide/evaluate-config_use-managed-rules.html\">managed rules</a>, you can use the rule identifier as the <code>keywordValue</code>. You can find the rule identifier from the <a href=\"https://docs.aws.amazon.com/config/latest/developerguide/managed-rules-by-aws-config.html\">list of Config managed rules</a>. For some rules, the rule identifier is different from the rule name. For example, the rule name <code>restricted-ssh</code> has the following rule identifier: <code>INCOMING_SSH_DISABLED</code>. Make sure to use the rule identifier, not the rule name. </p> <p>Keyword example for managed rules:</p> <ul> <li> <p>Managed rule name: <a href=\"https://docs.aws.amazon.com/config/latest/developerguide/s3-bucket-acl-prohibited.html\">s3-bucket-acl-prohibited</a> </p> <p> <code>keywordValue</code>: <code>S3_BUCKET_ACL_PROHIBITED</code> </p> </li> </ul> </li> <li> <p>For <a href=\"https://docs.aws.amazon.com/config/latest/developerguide/evaluate-config_develop-rules.html\">custom rules</a>, you form the <code>keywordValue</code> by adding the <code>Custom_</code> prefix to the rule name. This prefix distinguishes the custom rule from a managed rule. </p> <p>Keyword example for custom rules:</p> <ul> <li> <p>Custom rule name: my-custom-config-rule</p> <p> <code>keywordValue</code>: <code>Custom_my-custom-config-rule</code> </p> </li> </ul> </li> <li> <p>For <a href=\"https://docs.aws.amazon.com/config/latest/developerguide/service-linked-awsconfig-rules.html\">service-linked rules</a>, you form the <code>keywordValue</code> by adding the <code>Custom_</code> prefix to the rule name. In addition, you remove the suffix ID that appears at the end of the rule name. </p> <p>Keyword examples for service-linked rules:</p> <ul> <li> <p>Service-linked rule name: CustomRuleForAccount-conformance-pack-szsm1uv0w</p> <p> <code>keywordValue</code>: <code>Custom_CustomRuleForAccount-conformance-pack</code> </p> </li> <li> <p>Service-linked rule name: OrgConfigRule-s3-bucket-versioning-enabled-dbgzf8ba</p> <p> <code>keywordValue</code>: <code>Custom_OrgConfigRule-s3-bucket-versioning-enabled</code> </p> </li> </ul> </li> </ul> <important> <p>The <code>keywordValue</code> is case sensitive. If you enter a value incorrectly, Audit Manager might not recognize the data source mapping. As a result, you might not successfully collect evidence from that data source as intended. </p> <p>Keep in mind the following requirements, depending on the data source type that you're using. </p> <ol> <li> <p>For Config: </p> <ul> <li> <p>For managed rules, make sure that the <code>keywordValue</code> is the rule identifier in <code>ALL_CAPS_WITH_UNDERSCORES</code>. For example, <code>CLOUDWATCH_LOG_GROUP_ENCRYPTED</code>. For accuracy, we recommend that you reference the list of <a href=\"https://docs.aws.amazon.com/audit-manager/latest/userguide/control-data-sources-config.html\">supported Config managed rules</a>.</p> </li> <li> <p>For custom rules, make sure that the <code>keywordValue</code> has the <code>Custom_</code> prefix followed by the custom rule name. The format of the custom rule name itself may vary. For accuracy, we recommend that you visit the <a href=\"https://console.aws.amazon.com/config/\">Config console</a> to verify your custom rule name.</p> </li> </ul> </li> <li> <p>For Security Hub: The format varies for Security Hub control names. For accuracy, we recommend that you reference the list of <a href=\"https://docs.aws.amazon.com/audit-manager/latest/userguide/control-data-sources-ash.html\">supported Security Hub controls</a>.</p> </li> <li> <p>For Amazon Web Services API calls: Make sure that the <code>keywordValue</code> is written as <code>serviceprefix_ActionName</code>. For example, <code>iam_ListGroups</code>. For accuracy, we recommend that you reference the list of <a href=\"https://docs.aws.amazon.com/audit-manager/latest/userguide/control-data-sources-api.html\">supported API calls</a>.</p> </li> <li> <p>For CloudTrail: Make sure that the <code>keywordValue</code> is written as <code>serviceprefix_ActionName</code>. For example, <code>cloudtrail_StartLogging</code>. For accuracy, we recommend that you review the Amazon Web Services service prefix and action names in the <a href=\"https://docs.aws.amazon.com/service-authorization/latest/reference/reference_policies_actions-resources-contextkeys.html\">Service Authorization Reference</a>.</p> </li> </ol> </important>"}}, "documentation": "<p>A keyword that relates to the control data source.</p> <p>For manual evidence, this keyword indicates if the manual evidence is a file or text.</p> <p>For automated evidence, this keyword identifies a specific CloudTrail event, Config rule, Security Hub control, or Amazon Web Services API name. </p> <p> To learn more about the supported keywords that you can use when mapping a control data source, see the following pages in the <i>Audit Manager User Guide</i>:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/audit-manager/latest/userguide/control-data-sources-config.html\">Config rules supported by Audit Manager</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/audit-manager/latest/userguide/control-data-sources-ash.html\">Security Hub controls supported by Audit Manager</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/audit-manager/latest/userguide/control-data-sources-api.html\">API calls supported by Audit Manager</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/audit-manager/latest/userguide/control-data-sources-cloudtrail.html\">CloudTrail event names supported by Audit Manager</a> </p> </li> </ul>"}, "SourceName": {"type": "string", "max": 300, "min": 1}, "SourceSetUpOption": {"type": "string", "enum": ["System_Controls_Mapping", "Procedural_Controls_Mapping"]}, "SourceType": {"type": "string", "enum": ["AWS_Cloudtrail", "AWS_Config", "AWS_Security_Hub", "AWS_API_Call", "MANUAL", "Common_Control", "Core_Control"]}, "StartAssessmentFrameworkShareRequest": {"type": "structure", "required": ["frameworkId", "destinationAccount", "destinationRegion"], "members": {"frameworkId": {"shape": "UUID", "documentation": "<p> The unique identifier for the custom framework to be shared. </p>", "location": "uri", "locationName": "frameworkId"}, "destinationAccount": {"shape": "AccountId", "documentation": "<p> The Amazon Web Services account of the recipient. </p>"}, "destinationRegion": {"shape": "Region", "documentation": "<p> The Amazon Web Services Region of the recipient. </p>"}, "comment": {"shape": "ShareRequestComment", "documentation": "<p> An optional comment from the sender about the share request. </p>"}}}, "StartAssessmentFrameworkShareResponse": {"type": "structure", "members": {"assessmentFrameworkShareRequest": {"shape": "AssessmentFrameworkShareRequest", "documentation": "<p> The share request that's created by the <code>StartAssessmentFrameworkShare</code> API. </p>"}}}, "String": {"type": "string", "max": 2048, "min": 0, "pattern": ".*"}, "TagKey": {"type": "string", "max": 128, "min": 1, "pattern": "^(?!aws:)[a-zA-Z+-=._:/]+$"}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 50, "min": 1}, "TagMap": {"type": "map", "key": {"shape": "TagKey"}, "value": {"shape": "TagValue"}, "max": 50, "min": 0}, "TagResourceRequest": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {"shape": "AuditManagerArn", "documentation": "<p> The Amazon Resource Name (ARN) of the resource. </p>", "location": "uri", "locationName": "resourceArn"}, "tags": {"shape": "TagMap", "documentation": "<p> The tags that are associated with the resource. </p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0, "pattern": ".{0,255}"}, "TestingInformation": {"type": "string", "max": 1000, "pattern": "^[\\w\\W\\s\\S]*$", "sensitive": true}, "ThrottlingException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The request was denied due to request throttling.</p>", "error": {"httpStatusCode": 429}, "exception": true}, "Timestamp": {"type": "timestamp"}, "TimestampUUID": {"type": "string", "max": 50, "min": 47, "pattern": "^[0-9]{10,13}_[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$"}, "Token": {"type": "string", "max": 1000, "min": 1, "pattern": "^[A-Za-z0-9+\\/=]*$"}, "TroubleshootingText": {"type": "string", "max": 1000, "pattern": "^[\\w\\W\\s\\S]*$", "sensitive": true}, "URL": {"type": "structure", "members": {"hyperlinkName": {"shape": "HyperlinkName", "documentation": "<p> The name or word that's used as a hyperlink to the URL. </p>"}, "link": {"shape": "UrlLink", "documentation": "<p> The unique identifier for the internet resource. </p>"}}, "documentation": "<p> Short for uniform resource locator. A URL is used as a unique identifier to locate a resource on the internet. </p>"}, "UUID": {"type": "string", "max": 36, "min": 36, "pattern": "^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$"}, "UntagResourceRequest": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {"shape": "AuditManagerArn", "documentation": "<p> The Amazon Resource Name (ARN) of the specified resource. </p>", "location": "uri", "locationName": "resourceArn"}, "tagKeys": {"shape": "TagKeyList", "documentation": "<p> The name or key of the tag. </p>", "location": "querystring", "locationName": "tagKeys"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateAssessmentControlRequest": {"type": "structure", "required": ["assessmentId", "controlSetId", "controlId"], "members": {"assessmentId": {"shape": "UUID", "documentation": "<p> The unique identifier for the assessment. </p>", "location": "uri", "locationName": "assessmentId"}, "controlSetId": {"shape": "ControlSetId", "documentation": "<p> The unique identifier for the control set. </p>", "location": "uri", "locationName": "controlSetId"}, "controlId": {"shape": "UUID", "documentation": "<p> The unique identifier for the control. </p>", "location": "uri", "locationName": "controlId"}, "controlStatus": {"shape": "ControlStatus", "documentation": "<p> The status of the control. </p>"}, "commentBody": {"shape": "ControlCommentBody", "documentation": "<p> The comment body text for the control. </p>"}}}, "UpdateAssessmentControlResponse": {"type": "structure", "members": {"control": {"shape": "AssessmentControl", "documentation": "<p> The name of the updated control set that the <code>UpdateAssessmentControl</code> API returned. </p>"}}}, "UpdateAssessmentControlSetStatusRequest": {"type": "structure", "required": ["assessmentId", "controlSetId", "status", "comment"], "members": {"assessmentId": {"shape": "UUID", "documentation": "<p> The unique identifier for the assessment. </p>", "location": "uri", "locationName": "assessmentId"}, "controlSetId": {"shape": "String", "documentation": "<p> The unique identifier for the control set. </p>", "location": "uri", "locationName": "controlSetId"}, "status": {"shape": "ControlSetStatus", "documentation": "<p> The status of the control set that's being updated. </p>"}, "comment": {"shape": "DelegationComment", "documentation": "<p> The comment that's related to the status update. </p>"}}}, "UpdateAssessmentControlSetStatusResponse": {"type": "structure", "members": {"controlSet": {"shape": "AssessmentControlSet", "documentation": "<p> The name of the updated control set that the <code>UpdateAssessmentControlSetStatus</code> API returned. </p>"}}}, "UpdateAssessmentFrameworkControlSet": {"type": "structure", "required": ["name", "controls"], "members": {"id": {"shape": "ControlSetName", "documentation": "<p> The unique identifier for the control set. </p>"}, "name": {"shape": "ControlSetName", "documentation": "<p> The name of the control set. </p>"}, "controls": {"shape": "CreateAssessmentFrameworkControls", "documentation": "<p> The list of controls that are contained within the control set. </p>"}}, "documentation": "<p> A <code>controlSet</code> entity that represents a collection of controls in Audit Manager. This doesn't contain the control set ID. </p>"}, "UpdateAssessmentFrameworkControlSets": {"type": "list", "member": {"shape": "UpdateAssessmentFrameworkControlSet"}, "min": 1}, "UpdateAssessmentFrameworkRequest": {"type": "structure", "required": ["frameworkId", "name", "controlSets"], "members": {"frameworkId": {"shape": "UUID", "documentation": "<p> The unique identifier for the framework. </p>", "location": "uri", "locationName": "frameworkId"}, "name": {"shape": "FrameworkName", "documentation": "<p> The name of the framework to be updated. </p>"}, "description": {"shape": "FrameworkDescription", "documentation": "<p> The description of the updated framework. </p>"}, "complianceType": {"shape": "ComplianceType", "documentation": "<p> The compliance type that the new custom framework supports, such as CIS or HIPAA. </p>"}, "controlSets": {"shape": "UpdateAssessmentFrameworkControlSets", "documentation": "<p> The control sets that are associated with the framework. </p>"}}}, "UpdateAssessmentFrameworkResponse": {"type": "structure", "members": {"framework": {"shape": "Framework", "documentation": "<p> The name of the framework. </p>"}}}, "UpdateAssessmentFrameworkShareRequest": {"type": "structure", "required": ["requestId", "requestType", "action"], "members": {"requestId": {"shape": "UUID", "documentation": "<p> The unique identifier for the share request. </p>", "location": "uri", "locationName": "requestId"}, "requestType": {"shape": "ShareRequestType", "documentation": "<p>Specifies whether the share request is a sent request or a received request.</p>"}, "action": {"shape": "ShareRequestAction", "documentation": "<p>Specifies the update action for the share request.</p>"}}}, "UpdateAssessmentFrameworkShareResponse": {"type": "structure", "members": {"assessmentFrameworkShareRequest": {"shape": "AssessmentFrameworkShareRequest", "documentation": "<p> The updated share request that's returned by the <code>UpdateAssessmentFrameworkShare</code> operation. </p>"}}}, "UpdateAssessmentRequest": {"type": "structure", "required": ["assessmentId", "scope"], "members": {"assessmentId": {"shape": "UUID", "documentation": "<p> The unique identifier for the assessment. </p>", "location": "uri", "locationName": "assessmentId"}, "assessmentName": {"shape": "AssessmentName", "documentation": "<p> The name of the assessment to be updated. </p>"}, "assessmentDescription": {"shape": "AssessmentDescription", "documentation": "<p> The description of the assessment. </p>"}, "scope": {"shape": "<PERSON><PERSON>", "documentation": "<p> The scope of the assessment. </p>"}, "assessmentReportsDestination": {"shape": "AssessmentReportsDestination", "documentation": "<p> The assessment report storage destination for the assessment that's being updated. </p>"}, "roles": {"shape": "Roles", "documentation": "<p> The list of roles for the assessment. </p>"}}}, "UpdateAssessmentResponse": {"type": "structure", "members": {"assessment": {"shape": "Assessment", "documentation": "<p> The response object for the <code>UpdateAssessment</code> API. This is the name of the updated assessment.</p>"}}}, "UpdateAssessmentStatusRequest": {"type": "structure", "required": ["assessmentId", "status"], "members": {"assessmentId": {"shape": "UUID", "documentation": "<p> The unique identifier for the assessment. </p>", "location": "uri", "locationName": "assessmentId"}, "status": {"shape": "AssessmentStatus", "documentation": "<p> The current status of the assessment. </p>"}}}, "UpdateAssessmentStatusResponse": {"type": "structure", "members": {"assessment": {"shape": "Assessment", "documentation": "<p> The name of the updated assessment that the <code>UpdateAssessmentStatus</code> API returned. </p>"}}}, "UpdateControlRequest": {"type": "structure", "required": ["controlId", "name", "controlMappingSources"], "members": {"controlId": {"shape": "UUID", "documentation": "<p> The identifier for the control. </p>", "location": "uri", "locationName": "controlId"}, "name": {"shape": "ControlName", "documentation": "<p> The name of the updated control. </p>"}, "description": {"shape": "ControlDescription", "documentation": "<p> The optional description of the control. </p>"}, "testingInformation": {"shape": "TestingInformation", "documentation": "<p> The steps that you should follow to determine if the control is met. </p>"}, "actionPlanTitle": {"shape": "ActionPlanTitle", "documentation": "<p> The title of the action plan for remediating the control. </p>"}, "actionPlanInstructions": {"shape": "ActionPlanInstructions", "documentation": "<p> The recommended actions to carry out if the control isn't fulfilled. </p>"}, "controlMappingSources": {"shape": "ControlMappingSources", "documentation": "<p> The data mapping sources for the control. </p>"}}}, "UpdateControlResponse": {"type": "structure", "members": {"control": {"shape": "Control", "documentation": "<p> The name of the updated control set that the <code>UpdateControl</code> API returned. </p>"}}}, "UpdateSettingsRequest": {"type": "structure", "members": {"snsTopic": {"shape": "SnsArn", "documentation": "<p> The Amazon Simple Notification Service (Amazon SNS) topic that Audit Manager sends notifications to. </p>"}, "defaultAssessmentReportsDestination": {"shape": "AssessmentReportsDestination", "documentation": "<p> The default S3 destination bucket for storing assessment reports. </p>"}, "defaultProcessOwners": {"shape": "Roles", "documentation": "<p> A list of the default audit owners. </p>"}, "kmsKey": {"shape": "KmsKey", "documentation": "<p> The KMS key details. </p>"}, "evidenceFinderEnabled": {"shape": "Boolean", "documentation": "<p>Specifies whether the evidence finder feature is enabled. Change this attribute to enable or disable evidence finder.</p> <important> <p>When you use this attribute to disable evidence finder, Audit Manager deletes the event data store that’s used to query your evidence data. As a result, you can’t re-enable evidence finder and use the feature again. Your only alternative is to <a href=\"https://docs.aws.amazon.com/audit-manager/latest/APIReference/API_DeregisterAccount.html\">deregister</a> and then <a href=\"https://docs.aws.amazon.com/audit-manager/latest/APIReference/API_RegisterAccount.html\">re-register</a> Audit Manager. </p> </important>"}, "deregistrationPolicy": {"shape": "DeregistrationPolicy", "documentation": "<p>The deregistration policy for your Audit Manager data. You can use this attribute to determine how your data is handled when you deregister Audit Manager.</p>"}, "defaultExportDestination": {"shape": "DefaultExportDestination", "documentation": "<p> The default S3 destination bucket for storing evidence finder exports. </p>"}}}, "UpdateSettingsResponse": {"type": "structure", "members": {"settings": {"shape": "Settings", "documentation": "<p> The current list of settings. </p>"}}}, "UrlLink": {"type": "string", "max": 8192, "min": 1, "pattern": "^(https?:\\/\\/)?(www\\.)?[a-zA-Z0-9-_]+([\\.]+[a-zA-Z]+)+[\\/\\w]*$"}, "Username": {"type": "string", "max": 128, "min": 1, "pattern": "^[a-zA-Z0-9-_()\\s\\+=,.@]+$", "sensitive": true}, "ValidateAssessmentReportIntegrityRequest": {"type": "structure", "required": ["s3RelativePath"], "members": {"s3RelativePath": {"shape": "S3Url", "documentation": "<p> The relative path of the Amazon S3 bucket that the assessment report is stored in. </p>"}}}, "ValidateAssessmentReportIntegrityResponse": {"type": "structure", "members": {"signatureValid": {"shape": "Boolean", "documentation": "<p> Specifies whether the signature key is valid. </p>"}, "signatureAlgorithm": {"shape": "String", "documentation": "<p> The signature algorithm that's used to code sign the assessment report file. </p>"}, "signatureDateTime": {"shape": "String", "documentation": "<p> The date and time signature that specifies when the assessment report was created. </p>"}, "signatureKeyId": {"shape": "String", "documentation": "<p> The unique identifier for the validation signature key. </p>"}, "validationErrors": {"shape": "ValidationErrors", "documentation": "<p> Represents any errors that occurred when validating the assessment report. </p>"}}}, "ValidationErrors": {"type": "list", "member": {"shape": "NonEmptyString"}}, "ValidationException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}, "reason": {"shape": "ValidationExceptionReason", "documentation": "<p> The reason the request failed validation. </p>"}, "fields": {"shape": "ValidationExceptionFieldList", "documentation": "<p> The fields that caused the error, if applicable. </p>"}}, "documentation": "<p> The request has invalid or missing parameters. </p>", "error": {"httpStatusCode": 400}, "exception": true}, "ValidationExceptionField": {"type": "structure", "required": ["name", "message"], "members": {"name": {"shape": "String", "documentation": "<p> The name of the validation error. </p>"}, "message": {"shape": "String", "documentation": "<p> The body of the error message. </p>"}}, "documentation": "<p> Indicates that the request has invalid or missing parameters for the field. </p>"}, "ValidationExceptionFieldList": {"type": "list", "member": {"shape": "ValidationExceptionField"}}, "ValidationExceptionReason": {"type": "string", "enum": ["unknownOperation", "<PERSON><PERSON><PERSON><PERSON>", "fieldValidationFailed", "other"]}, "organizationId": {"type": "string", "max": 34, "min": 12, "pattern": "o-[a-z0-9]{10,32}"}}, "documentation": "<p>Welcome to the Audit Manager API reference. This guide is for developers who need detailed information about the Audit Manager API operations, data types, and errors. </p> <p>Audit Manager is a service that provides automated evidence collection so that you can continually audit your Amazon Web Services usage. You can use it to assess the effectiveness of your controls, manage risk, and simplify compliance.</p> <p>Audit Manager provides prebuilt frameworks that structure and automate assessments for a given compliance standard. Frameworks include a prebuilt collection of controls with descriptions and testing procedures. These controls are grouped according to the requirements of the specified compliance standard or regulation. You can also customize frameworks and controls to support internal audits with specific requirements. </p> <p>Use the following links to get started with the Audit Manager API:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/audit-manager/latest/APIReference/API_Operations.html\">Actions</a>: An alphabetical list of all Audit Manager API operations.</p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/audit-manager/latest/APIReference/API_Types.html\">Data types</a>: An alphabetical list of all Audit Manager data types.</p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/audit-manager/latest/APIReference/CommonParameters.html\">Common parameters</a>: Parameters that all operations can use.</p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/audit-manager/latest/APIReference/CommonErrors.html\">Common errors</a>: Client and server errors that all operations can return.</p> </li> </ul> <p>If you're new to Audit Manager, we recommend that you review the <a href=\"https://docs.aws.amazon.com/audit-manager/latest/userguide/what-is.html\"> Audit Manager User Guide</a>.</p>"}