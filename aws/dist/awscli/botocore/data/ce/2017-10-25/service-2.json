{"version": "2.0", "metadata": {"apiVersion": "2017-10-25", "endpointPrefix": "ce", "jsonVersion": "1.1", "protocol": "json", "protocols": ["json"], "serviceAbbreviation": "AWS Cost Explorer", "serviceFullName": "AWS Cost Explorer Service", "serviceId": "Cost Explorer", "signatureVersion": "v4", "signingName": "ce", "targetPrefix": "AWSInsightsIndexService", "uid": "ce-2017-10-25", "auth": ["aws.auth#sigv4"]}, "operations": {"CreateAnomalyMonitor": {"name": "CreateAnomalyMonitor", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateAnomalyMonitorRequest"}, "output": {"shape": "CreateAnomalyMonitorResponse"}, "errors": [{"shape": "LimitExceededException"}], "documentation": "<p>Creates a new cost anomaly detection monitor with the requested type and monitor specification. </p>"}, "CreateAnomalySubscription": {"name": "CreateAnomalySubscription", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateAnomalySubscriptionRequest"}, "output": {"shape": "CreateAnomalySubscriptionResponse"}, "errors": [{"shape": "UnknownMonitorException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Adds an alert subscription to a cost anomaly detection monitor. You can use each subscription to define subscribers with email or SNS notifications. Email subscribers can set an absolute or percentage threshold and a time frequency for receiving notifications. </p>"}, "CreateCostCategoryDefinition": {"name": "CreateCostCategoryDefinition", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateCostCategoryDefinitionRequest"}, "output": {"shape": "CreateCostCategoryDefinitionResponse"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Creates a new Cost Category with the requested name and rules.</p>"}, "DeleteAnomalyMonitor": {"name": "DeleteAnomalyMonitor", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteAnomalyMonitorRequest"}, "output": {"shape": "DeleteAnomalyMonitorResponse"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "UnknownMonitorException"}], "documentation": "<p>Deletes a cost anomaly monitor. </p>"}, "DeleteAnomalySubscription": {"name": "DeleteAnomalySubscription", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteAnomalySubscriptionRequest"}, "output": {"shape": "DeleteAnomalySubscriptionResponse"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "UnknownSubscriptionException"}], "documentation": "<p>Deletes a cost anomaly subscription. </p>"}, "DeleteCostCategoryDefinition": {"name": "DeleteCostCategoryDefinition", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteCostCategoryDefinitionRequest"}, "output": {"shape": "DeleteCostCategoryDefinitionResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Deletes a Cost Category. Expenses from this month going forward will no longer be categorized with this Cost Category.</p>"}, "DescribeCostCategoryDefinition": {"name": "DescribeCostCategoryDefinition", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeCostCategoryDefinitionRequest"}, "output": {"shape": "DescribeCostCategoryDefinitionResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Returns the name, Amazon Resource Name (ARN), rules, definition, and effective dates of a Cost Category that's defined in the account.</p> <p>You have the option to use <code>EffectiveOn</code> to return a Cost Category that's active on a specific date. If there's no <code>EffectiveOn</code> specified, you see a Cost Category that's effective on the current date. If Cost Category is still effective, <code>EffectiveEnd</code> is omitted in the response. </p>"}, "GetAnomalies": {"name": "GetAnomalies", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetAnomaliesRequest"}, "output": {"shape": "GetAnomaliesResponse"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "InvalidNextTokenException"}], "documentation": "<p>Retrieves all of the cost anomalies detected on your account during the time period that's specified by the <code>DateInterval</code> object. Anomalies are available for up to 90 days.</p>"}, "GetAnomalyMonitors": {"name": "GetAnomalyMonitors", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetAnomalyMonitorsRequest"}, "output": {"shape": "GetAnomalyMonitorsResponse"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "UnknownMonitorException"}, {"shape": "InvalidNextTokenException"}], "documentation": "<p>Retrieves the cost anomaly monitor definitions for your account. You can filter using a list of cost anomaly monitor Amazon Resource Names (ARNs). </p>"}, "GetAnomalySubscriptions": {"name": "GetAnomalySubscriptions", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetAnomalySubscriptionsRequest"}, "output": {"shape": "GetAnomalySubscriptionsResponse"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "UnknownSubscriptionException"}, {"shape": "InvalidNextTokenException"}], "documentation": "<p>Retrieves the cost anomaly subscription objects for your account. You can filter using a list of cost anomaly monitor Amazon Resource Names (ARNs). </p>"}, "GetApproximateUsageRecords": {"name": "GetApproximateUsageRecords", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetApproximateUsageRecordsRequest"}, "output": {"shape": "GetApproximateUsageRecordsResponse"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "DataUnavailableException"}], "documentation": "<p>Retrieves estimated usage records for hourly granularity or resource-level data at daily granularity.</p>"}, "GetCommitmentPurchaseAnalysis": {"name": "GetCommitmentPurchaseAnalysis", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetCommitmentPurchaseAnalysisRequest"}, "output": {"shape": "GetCommitmentPurchaseAnalysisResponse"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "AnalysisNotFoundException"}, {"shape": "DataUnavailableException"}], "documentation": "<p>Retrieves a commitment purchase analysis result based on the <code>AnalysisId</code>.</p>"}, "GetCostAndUsage": {"name": "GetCostAndUsage", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetCostAndUsageRequest"}, "output": {"shape": "GetCostAndUsageResponse"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "BillExpirationException"}, {"shape": "DataUnavailableException"}, {"shape": "InvalidNextTokenException"}, {"shape": "RequestChangedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves cost and usage metrics for your account. You can specify which cost and usage-related metric that you want the request to return. For example, you can specify <code>BlendedCosts</code> or <code>UsageQuantity</code>. You can also filter and group your data by various dimensions, such as <code>SERVICE</code> or <code>AZ</code>, in a specific time range. For a complete list of valid dimensions, see the <a href=\"https://docs.aws.amazon.com/aws-cost-management/latest/APIReference/API_GetDimensionValues.html\">GetDimensionValues</a> operation. Management account in an organization in Organizations have access to all member accounts.</p> <p>For information about filter limitations, see <a href=\"https://docs.aws.amazon.com/awsaccountbilling/latest/aboutv2/billing-limits.html\">Quotas and restrictions</a> in the <i>Billing and Cost Management User Guide</i>.</p>"}, "GetCostAndUsageComparisons": {"name": "GetCostAndUsageComparisons", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetCostAndUsageComparisonsRequest"}, "output": {"shape": "GetCostAndUsageComparisonsResponse"}, "errors": [{"shape": "DataUnavailableException"}, {"shape": "InvalidNextTokenException"}, {"shape": "LimitExceededException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves cost and usage comparisons for your account between two periods within the last 13 months. If you have enabled multi-year data at monthly granularity, you can go back up to 38 months.</p>"}, "GetCostAndUsageWithResources": {"name": "GetCostAndUsageWithResources", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetCostAndUsageWithResourcesRequest"}, "output": {"shape": "GetCostAndUsageWithResourcesResponse"}, "errors": [{"shape": "DataUnavailableException"}, {"shape": "LimitExceededException"}, {"shape": "BillExpirationException"}, {"shape": "InvalidNextTokenException"}, {"shape": "RequestChangedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves cost and usage metrics with resources for your account. You can specify which cost and usage-related metric, such as <code>BlendedCosts</code> or <code>UsageQuantity</code>, that you want the request to return. You can also filter and group your data by various dimensions, such as <code>SERVICE</code> or <code>AZ</code>, in a specific time range. For a complete list of valid dimensions, see the <a href=\"https://docs.aws.amazon.com/aws-cost-management/latest/APIReference/API_GetDimensionValues.html\">GetDimensionValues</a> operation. Management account in an organization in Organizations have access to all member accounts.</p> <p>Hourly granularity is only available for EC2-Instances (Elastic Compute Cloud) resource-level data. All other resource-level data is available at daily granularity.</p> <note> <p>This is an opt-in only feature. You can enable this feature from the Cost Explorer Settings page. For information about how to access the Settings page, see <a href=\"https://docs.aws.amazon.com/awsaccountbilling/latest/aboutv2/ce-access.html\">Controlling Access for Cost Explorer</a> in the <i>Billing and Cost Management User Guide</i>.</p> </note>"}, "GetCostCategories": {"name": "GetCostCategories", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetCostCategoriesRequest"}, "output": {"shape": "GetCostCategoriesResponse"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "BillExpirationException"}, {"shape": "DataUnavailableException"}, {"shape": "InvalidNextTokenException"}, {"shape": "RequestChangedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves an array of Cost Category names and values incurred cost.</p> <note> <p>If some Cost Category names and values are not associated with any cost, they will not be returned by this API.</p> </note>"}, "GetCostComparisonDrivers": {"name": "GetCostComparisonDrivers", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetCostComparisonDriversRequest"}, "output": {"shape": "GetCostComparisonDriversResponse"}, "errors": [{"shape": "DataUnavailableException"}, {"shape": "InvalidNextTokenException"}, {"shape": "LimitExceededException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves key factors driving cost changes between two time periods within the last 13 months, such as usage changes, discount changes, and commitment-based savings. If you have enabled multi-year data at monthly granularity, you can go back up to 38 months.</p>"}, "GetCostForecast": {"name": "GetCostForecast", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetCostForecastRequest"}, "output": {"shape": "GetCostForecastResponse"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "DataUnavailableException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves a forecast for how much Amazon Web Services predicts that you will spend over the forecast time period that you select, based on your past costs. </p>"}, "GetDimensionValues": {"name": "GetDimensionValues", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetDimensionValuesRequest"}, "output": {"shape": "GetDimensionValuesResponse"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "BillExpirationException"}, {"shape": "DataUnavailableException"}, {"shape": "InvalidNextTokenException"}, {"shape": "RequestChangedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves all available filter values for a specified filter over a period of time. You can search the dimension values for an arbitrary string. </p>"}, "GetReservationCoverage": {"name": "GetReservationCoverage", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetReservationCoverageRequest"}, "output": {"shape": "GetReservationCoverageResponse"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "DataUnavailableException"}, {"shape": "InvalidNextTokenException"}], "documentation": "<p>Retrieves the reservation coverage for your account, which you can use to see how much of your Amazon Elastic Compute Cloud, Amazon ElastiCache, Amazon Relational Database Service, or Amazon Redshift usage is covered by a reservation. An organization's management account can see the coverage of the associated member accounts. This supports dimensions, Cost Categories, and nested expressions. For any time period, you can filter data about reservation usage by the following dimensions:</p> <ul> <li> <p>AZ</p> </li> <li> <p>CACHE_ENGINE</p> </li> <li> <p>DATABASE_ENGINE</p> </li> <li> <p>DEPLOYMENT_OPTION</p> </li> <li> <p>INSTANCE_TYPE</p> </li> <li> <p>LINKED_ACCOUNT</p> </li> <li> <p>OPERATING_SYSTEM</p> </li> <li> <p>PLATFORM</p> </li> <li> <p>REGION</p> </li> <li> <p>SERVICE</p> </li> <li> <p>TAG</p> </li> <li> <p>TENANCY</p> </li> </ul> <p>To determine valid values for a dimension, use the <code>GetDimensionValues</code> operation. </p>"}, "GetReservationPurchaseRecommendation": {"name": "GetReservationPurchaseRecommendation", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetReservationPurchaseRecommendationRequest"}, "output": {"shape": "GetReservationPurchaseRecommendationResponse"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "DataUnavailableException"}, {"shape": "InvalidNextTokenException"}], "documentation": "<p>Gets recommendations for reservation purchases. These recommendations might help you to reduce your costs. Reservations provide a discounted hourly rate (up to 75%) compared to On-Demand pricing.</p> <p>Amazon Web Services generates your recommendations by identifying your On-Demand usage during a specific time period and collecting your usage into categories that are eligible for a reservation. After Amazon Web Services has these categories, it simulates every combination of reservations in each category of usage to identify the best number of each type of Reserved Instance (RI) to purchase to maximize your estimated savings. </p> <p>For example, Amazon Web Services automatically aggregates your Amazon EC2 Linux, shared tenancy, and c4 family usage in the US West (Oregon) Region and recommends that you buy size-flexible regional reservations to apply to the c4 family usage. Amazon Web Services recommends the smallest size instance in an instance family. This makes it easier to purchase a size-flexible Reserved Instance (RI). Amazon Web Services also shows the equal number of normalized units. This way, you can purchase any instance size that you want. For this example, your RI recommendation is for <code>c4.large</code> because that is the smallest size instance in the c4 instance family.</p>"}, "GetReservationUtilization": {"name": "GetReservationUtilization", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetReservationUtilizationRequest"}, "output": {"shape": "GetReservationUtilizationResponse"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "DataUnavailableException"}, {"shape": "InvalidNextTokenException"}], "documentation": "<p>Retrieves the reservation utilization for your account. Management account in an organization have access to member accounts. You can filter data by dimensions in a time period. You can use <code>GetDimensionValues</code> to determine the possible dimension values. Currently, you can group only by <code>SUBSCRIPTION_ID</code>. </p>"}, "GetRightsizingRecommendation": {"name": "GetRightsizingRecommendation", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetRightsizingRecommendationRequest"}, "output": {"shape": "GetRightsizingRecommendationResponse"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "InvalidNextTokenException"}], "documentation": "<p>Creates recommendations that help you save cost by identifying idle and underutilized Amazon EC2 instances.</p> <p>Recommendations are generated to either downsize or terminate instances, along with providing savings detail and metrics. For more information about calculation and function, see <a href=\"https://docs.aws.amazon.com/awsaccountbilling/latest/aboutv2/ce-rightsizing.html\">Optimizing Your Cost with Rightsizing Recommendations</a> in the <i>Billing and Cost Management User Guide</i>.</p>"}, "GetSavingsPlanPurchaseRecommendationDetails": {"name": "GetSavingsPlanPurchaseRecommendationDetails", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetSavingsPlanPurchaseRecommendationDetailsRequest"}, "output": {"shape": "GetSavingsPlanPurchaseRecommendationDetailsResponse"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "DataUnavailableException"}], "documentation": "<p>Retrieves the details for a Savings Plan recommendation. These details include the hourly data-points that construct the cost, coverage, and utilization charts.</p>"}, "GetSavingsPlansCoverage": {"name": "GetSavingsPlansCoverage", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetSavingsPlansCoverageRequest"}, "output": {"shape": "GetSavingsPlansCoverageResponse"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "DataUnavailableException"}, {"shape": "InvalidNextTokenException"}], "documentation": "<p>Retrieves the Savings Plans covered for your account. This enables you to see how much of your cost is covered by a Savings Plan. An organization’s management account can see the coverage of the associated member accounts. This supports dimensions, Cost Categories, and nested expressions. For any time period, you can filter data for Savings Plans usage with the following dimensions:</p> <ul> <li> <p> <code>LINKED_ACCOUNT</code> </p> </li> <li> <p> <code>REGION</code> </p> </li> <li> <p> <code>SERVICE</code> </p> </li> <li> <p> <code>INSTANCE_FAMILY</code> </p> </li> </ul> <p>To determine valid values for a dimension, use the <code>GetDimensionValues</code> operation.</p>"}, "GetSavingsPlansPurchaseRecommendation": {"name": "GetSavingsPlansPurchaseRecommendation", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetSavingsPlansPurchaseRecommendationRequest"}, "output": {"shape": "GetSavingsPlansPurchaseRecommendationResponse"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "InvalidNextTokenException"}], "documentation": "<p>Retrieves the Savings Plans recommendations for your account. First use <code>StartSavingsPlansPurchaseRecommendationGeneration</code> to generate a new set of recommendations, and then use <code>GetSavingsPlansPurchaseRecommendation</code> to retrieve them.</p>"}, "GetSavingsPlansUtilization": {"name": "GetSavingsPlansUtilization", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetSavingsPlansUtilizationRequest"}, "output": {"shape": "GetSavingsPlansUtilizationResponse"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "DataUnavailableException"}], "documentation": "<p>Retrieves the Savings Plans utilization for your account across date ranges with daily or monthly granularity. Management account in an organization have access to member accounts. You can use <code>GetDimensionValues</code> in <code>SAVINGS_PLANS</code> to determine the possible dimension values.</p> <note> <p>You can't group by any dimension values for <code>GetSavingsPlansUtilization</code>.</p> </note>"}, "GetSavingsPlansUtilizationDetails": {"name": "GetSavingsPlansUtilizationDetails", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetSavingsPlansUtilizationDetailsRequest"}, "output": {"shape": "GetSavingsPlansUtilizationDetailsResponse"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "DataUnavailableException"}, {"shape": "InvalidNextTokenException"}], "documentation": "<p>Retrieves attribute data along with aggregate utilization and savings data for a given time period. This doesn't support granular or grouped data (daily/monthly) in response. You can't retrieve data by dates in a single response similar to <code>GetSavingsPlanUtilization</code>, but you have the option to make multiple calls to <code>GetSavingsPlanUtilizationDetails</code> by providing individual dates. You can use <code>GetDimensionValues</code> in <code>SAVINGS_PLANS</code> to determine the possible dimension values.</p> <note> <p> <code>GetSavingsPlanUtilizationDetails</code> internally groups data by <code>SavingsPlansArn</code>.</p> </note>"}, "GetTags": {"name": "GetTags", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetTagsRequest"}, "output": {"shape": "GetTagsResponse"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "BillExpirationException"}, {"shape": "DataUnavailableException"}, {"shape": "InvalidNextTokenException"}, {"shape": "RequestChangedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Queries for available tag keys and tag values for a specified period. You can search the tag values for an arbitrary string. </p>"}, "GetUsageForecast": {"name": "GetUsageForecast", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetUsageForecastRequest"}, "output": {"shape": "GetUsageForecastResponse"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "DataUnavailableException"}, {"shape": "UnresolvableUsageUnitException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves a forecast for how much Amazon Web Services predicts that you will use over the forecast time period that you select, based on your past usage. </p>"}, "ListCommitmentPurchaseAnalyses": {"name": "ListCommitmentPurchaseAnalyses", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListCommitmentPurchaseAnalysesRequest"}, "output": {"shape": "ListCommitmentPurchaseAnalysesResponse"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "InvalidNextTokenException"}, {"shape": "DataUnavailableException"}], "documentation": "<p>Lists the commitment purchase analyses for your account.</p>"}, "ListCostAllocationTagBackfillHistory": {"name": "ListCostAllocationTagBackfillHistory", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListCostAllocationTagBackfillHistoryRequest"}, "output": {"shape": "ListCostAllocationTagBackfillHistoryResponse"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "InvalidNextTokenException"}], "documentation": "<p> Retrieves a list of your historical cost allocation tag backfill requests. </p>"}, "ListCostAllocationTags": {"name": "ListCostAllocationTags", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListCostAllocationTagsRequest"}, "output": {"shape": "ListCostAllocationTagsResponse"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "InvalidNextTokenException"}], "documentation": "<p>Get a list of cost allocation tags. All inputs in the API are optional and serve as filters. By default, all cost allocation tags are returned. </p>"}, "ListCostCategoryDefinitions": {"name": "ListCostCategoryDefinitions", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListCostCategoryDefinitionsRequest"}, "output": {"shape": "ListCostCategoryDefinitionsResponse"}, "errors": [{"shape": "LimitExceededException"}], "documentation": "<p>Returns the name, Amazon Resource Name (ARN), <code>NumberOfRules</code> and effective dates of all Cost Categories defined in the account. You have the option to use <code>EffectiveOn</code> to return a list of Cost Categories that were active on a specific date. If there is no <code>EffectiveOn</code> specified, you’ll see Cost Categories that are effective on the current date. If Cost Category is still effective, <code>EffectiveEnd</code> is omitted in the response. <code>ListCostCategoryDefinitions</code> supports pagination. The request can have a <code>MaxResults</code> range up to 100.</p>"}, "ListSavingsPlansPurchaseRecommendationGeneration": {"name": "ListSavingsPlansPurchaseRecommendationGeneration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListSavingsPlansPurchaseRecommendationGenerationRequest"}, "output": {"shape": "ListSavingsPlansPurchaseRecommendationGenerationResponse"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "InvalidNextTokenException"}, {"shape": "DataUnavailableException"}], "documentation": "<p>Retrieves a list of your historical recommendation generations within the past 30 days.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Returns a list of resource tags associated with the resource specified by the Amazon Resource Name (ARN). </p>"}, "ProvideAnomalyFeedback": {"name": "ProvideAnomalyFeedback", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ProvideAnomalyFeedbackRequest"}, "output": {"shape": "ProvideAnomalyFeedbackResponse"}, "errors": [{"shape": "LimitExceededException"}], "documentation": "<p>Modifies the feedback property of a given cost anomaly. </p>"}, "StartCommitmentPurchaseAnalysis": {"name": "StartCommitmentPurchaseAnalysis", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StartCommitmentPurchaseAnalysisRequest"}, "output": {"shape": "StartCommitmentPurchaseAnalysisResponse"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "DataUnavailableException"}, {"shape": "GenerationExistsException"}], "documentation": "<p>Specifies the parameters of a planned commitment purchase and starts the generation of the analysis. This enables you to estimate the cost, coverage, and utilization impact of your planned commitment purchases.</p>"}, "StartCostAllocationTagBackfill": {"name": "StartCostAllocationTagBackfill", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StartCostAllocationTagBackfillRequest"}, "output": {"shape": "StartCostAllocationTagBackfillResponse"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "BackfillLimitExceededException"}], "documentation": "<p> Request a cost allocation tag backfill. This will backfill the activation status (either <code>active</code> or <code>inactive</code>) for all tag keys from <code>para:BackfillFrom</code> up to the time this request is made.</p> <p>You can request a backfill once every 24 hours. </p>"}, "StartSavingsPlansPurchaseRecommendationGeneration": {"name": "StartSavingsPlansPurchaseRecommendationGeneration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StartSavingsPlansPurchaseRecommendationGenerationRequest"}, "output": {"shape": "StartSavingsPlansPurchaseRecommendationGenerationResponse"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "GenerationExistsException"}, {"shape": "DataUnavailableException"}], "documentation": "<p>Requests a Savings Plans recommendation generation. This enables you to calculate a fresh set of Savings Plans recommendations that takes your latest usage data and current Savings Plans inventory into account. You can refresh Savings Plans recommendations up to three times daily for a consolidated billing family.</p> <note> <p> <code>StartSavingsPlansPurchaseRecommendationGeneration</code> has no request syntax because no input parameters are needed to support this operation.</p> </note>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "TooManyTagsException"}, {"shape": "LimitExceededException"}], "documentation": "<p>An API operation for adding one or more tags (key-value pairs) to a resource.</p> <p>You can use the <code>TagResource</code> operation with a resource that already has tags. If you specify a new tag key for the resource, this tag is appended to the list of tags associated with the resource. If you specify a tag key that is already associated with the resource, the new tag value you specify replaces the previous value for that tag.</p> <p>Although the maximum number of array members is 200, user-tag maximum is 50. The remaining are reserved for Amazon Web Services use.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Removes one or more tags from a resource. Specify only tag keys in your request. Don't specify the value. </p>"}, "UpdateAnomalyMonitor": {"name": "UpdateAnomalyMonitor", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateAnomalyMonitorRequest"}, "output": {"shape": "UpdateAnomalyMonitorResponse"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "UnknownMonitorException"}], "documentation": "<p>Updates an existing cost anomaly monitor. The changes made are applied going forward, and doesn't change anomalies detected in the past. </p>"}, "UpdateAnomalySubscription": {"name": "UpdateAnomalySubscription", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateAnomalySubscriptionRequest"}, "output": {"shape": "UpdateAnomalySubscriptionResponse"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "UnknownMonitorException"}, {"shape": "UnknownSubscriptionException"}], "documentation": "<p>Updates an existing cost anomaly subscription. Specify the fields that you want to update. Omitted fields are unchanged.</p> <note> <p>The JSON below describes the generic construct for each type. See <a href=\"https://docs.aws.amazon.com/aws-cost-management/latest/APIReference/API_UpdateAnomalySubscription.html#API_UpdateAnomalySubscription_RequestParameters\">Request Parameters</a> for possible values as they apply to <code>AnomalySubscription</code>.</p> </note>"}, "UpdateCostAllocationTagsStatus": {"name": "UpdateCostAllocationTagsStatus", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateCostAllocationTagsStatusRequest"}, "output": {"shape": "UpdateCostAllocationTagsStatusResponse"}, "errors": [{"shape": "LimitExceededException"}], "documentation": "<p>Updates status for cost allocation tags in bulk, with maximum batch size of 20. If the tag status that's updated is the same as the existing tag status, the request doesn't fail. Instead, it doesn't have any effect on the tag status (for example, activating the active tag). </p>"}, "UpdateCostCategoryDefinition": {"name": "UpdateCostCategoryDefinition", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateCostCategoryDefinitionRequest"}, "output": {"shape": "UpdateCostCategoryDefinitionResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Updates an existing Cost Category. Changes made to the Cost Category rules will be used to categorize the current month’s expenses and future expenses. This won’t change categorization for the previous months.</p>"}}, "shapes": {"AccountId": {"type": "string", "max": 12, "min": 12, "pattern": "[0-9]{12}"}, "AccountScope": {"type": "string", "enum": ["PAYER", "LINKED"]}, "AmortizedRecurringFee": {"type": "string"}, "AmortizedUpfrontFee": {"type": "string"}, "AnalysisDetails": {"type": "structure", "members": {"SavingsPlansPurchaseAnalysisDetails": {"shape": "SavingsPlansPurchaseAnalysisDetails", "documentation": "<p>Details about the Savings Plans purchase analysis.</p>"}}, "documentation": "<p>Details about the analysis.</p>"}, "AnalysisId": {"type": "string", "max": 36, "min": 36, "pattern": "^[\\S\\s]{8}-[\\S\\s]{4}-[\\S\\s]{4}-[\\S\\s]{4}-[\\S\\s]{12}$"}, "AnalysisIds": {"type": "list", "member": {"shape": "AnalysisId"}, "max": 600, "min": 0}, "AnalysisNotFoundException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The requested analysis can't be found.</p>", "exception": true}, "AnalysisStatus": {"type": "string", "enum": ["SUCCEEDED", "PROCESSING", "FAILED"]}, "AnalysisSummary": {"type": "structure", "members": {"EstimatedCompletionTime": {"shape": "ZonedDateTime", "documentation": "<p>The estimated time for when the analysis will complete.</p>"}, "AnalysisCompletionTime": {"shape": "ZonedDateTime", "documentation": "<p>The completion time of the analysis.</p>"}, "AnalysisStartedTime": {"shape": "ZonedDateTime", "documentation": "<p>The start time of the analysis.</p>"}, "AnalysisStatus": {"shape": "AnalysisStatus", "documentation": "<p>The status of the analysis.</p>"}, "ErrorCode": {"shape": "ErrorCode", "documentation": "<p>The error code used for the analysis.</p>"}, "AnalysisId": {"shape": "AnalysisId", "documentation": "<p>The analysis ID that's associated with the commitment purchase analysis.</p>"}, "CommitmentPurchaseAnalysisConfiguration": {"shape": "CommitmentPurchaseAnalysisConfiguration", "documentation": "<p>The configuration for the commitment purchase analysis.</p>"}}, "documentation": "<p>A summary of the analysis.</p>"}, "AnalysisSummaryList": {"type": "list", "member": {"shape": "AnalysisSummary"}}, "AnalysisType": {"type": "string", "enum": ["MAX_SAVINGS", "CUSTOM_COMMITMENT"]}, "Anomalies": {"type": "list", "member": {"shape": "Anomaly"}}, "Anomaly": {"type": "structure", "required": ["AnomalyId", "AnomalyScore", "Impact", "MonitorArn"], "members": {"AnomalyId": {"shape": "GenericString", "documentation": "<p>The unique identifier for the anomaly. </p>"}, "AnomalyStartDate": {"shape": "YearMonthDay", "documentation": "<p>The first day the anomaly is detected. </p>"}, "AnomalyEndDate": {"shape": "YearMonthDay", "documentation": "<p>The last day the anomaly is detected. </p>"}, "DimensionValue": {"shape": "GenericString", "documentation": "<p>The dimension for the anomaly (for example, an Amazon Web Services service in a service monitor). </p>"}, "RootCauses": {"shape": "RootCauses", "documentation": "<p>The list of identified root causes for the anomaly. </p>"}, "AnomalyScore": {"shape": "AnomalyScore", "documentation": "<p>The latest and maximum score for the anomaly. </p>"}, "Impact": {"shape": "Impact", "documentation": "<p>The dollar impact for the anomaly. </p>"}, "MonitorArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) for the cost monitor that generated this anomaly. </p>"}, "Feedback": {"shape": "AnomalyFeedbackType", "documentation": "<p>The feedback value. </p>"}}, "documentation": "<p>An unusual cost pattern. This consists of the detailed metadata and the current status of the anomaly object. </p>"}, "AnomalyDateInterval": {"type": "structure", "required": ["StartDate"], "members": {"StartDate": {"shape": "YearMonthDay", "documentation": "<p>The first date an anomaly was observed. </p>"}, "EndDate": {"shape": "YearMonthDay", "documentation": "<p>The last date an anomaly was observed. </p>"}}, "documentation": "<p>The time period for an anomaly. </p>"}, "AnomalyFeedbackType": {"type": "string", "enum": ["YES", "NO", "PLANNED_ACTIVITY"]}, "AnomalyMonitor": {"type": "structure", "required": ["MonitorName", "MonitorType"], "members": {"MonitorArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) value. </p>"}, "MonitorName": {"shape": "GenericString", "documentation": "<p>The name of the monitor. </p>"}, "CreationDate": {"shape": "YearMonthDay", "documentation": "<p>The date when the monitor was created. </p>"}, "LastUpdatedDate": {"shape": "YearMonthDay", "documentation": "<p>The date when the monitor was last updated. </p>"}, "LastEvaluatedDate": {"shape": "YearMonthDay", "documentation": "<p>The date when the monitor last evaluated for anomalies. </p>"}, "MonitorType": {"shape": "MonitorType", "documentation": "<p>The possible type values. </p>"}, "MonitorDimension": {"shape": "MonitorDimension", "documentation": "<p>The dimensions to evaluate. </p>"}, "MonitorSpecification": {"shape": "Expression"}, "DimensionalValueCount": {"shape": "NonNegativeInteger", "documentation": "<p>The value for evaluated dimensions. </p>"}}, "documentation": "<p>This object continuously inspects your account's cost data for anomalies. It's based on <code>MonitorType</code> and <code>MonitorSpecification</code>. The content consists of detailed metadata and the current status of the monitor object. </p>"}, "AnomalyMonitors": {"type": "list", "member": {"shape": "AnomalyMonitor"}}, "AnomalyScore": {"type": "structure", "required": ["MaxScore", "CurrentScore"], "members": {"MaxScore": {"shape": "GenericDouble", "documentation": "<p>The maximum score that's observed during the <code>AnomalyDateInterval</code>. </p>"}, "CurrentScore": {"shape": "GenericDouble", "documentation": "<p>The last observed score. </p>"}}, "documentation": "<p>Quantifies the anomaly. The higher score means that it's more anomalous. </p>"}, "AnomalySubscription": {"type": "structure", "required": ["MonitorArnList", "Subscribers", "Frequency", "SubscriptionName"], "members": {"SubscriptionArn": {"shape": "GenericString", "documentation": "<p>The <code>AnomalySubscription</code> Amazon Resource Name (ARN). </p>"}, "AccountId": {"shape": "GenericString", "documentation": "<p>Your unique account identifier. </p>"}, "MonitorArnList": {"shape": "MonitorArnList", "documentation": "<p>A list of cost anomaly monitors. </p>"}, "Subscribers": {"shape": "Subscribers", "documentation": "<p>A list of subscribers to notify. </p>"}, "Threshold": {"shape": "NullableNonNegativeDouble", "documentation": "<p>(deprecated)</p> <p>An absolute dollar value that must be exceeded by the anomaly's total impact (see <a href=\"https://docs.aws.amazon.com/aws-cost-management/latest/APIReference/API_Impact.html\">Impact</a> for more details) for an anomaly notification to be generated.</p> <p>This field has been deprecated. To specify a threshold, use ThresholdExpression. Continued use of Threshold will be treated as shorthand syntax for a ThresholdExpression.</p> <p>One of Threshold or ThresholdExpression is required for this resource. You cannot specify both.</p>", "deprecated": true, "deprecatedMessage": "Threshold has been deprecated in favor of ThresholdExpression"}, "Frequency": {"shape": "AnomalySubscriptionFrequency", "documentation": "<p>The frequency that anomaly notifications are sent. Notifications are sent either over email (for DAILY and WEEKLY frequencies) or SNS (for IMMEDIATE frequency). For more information, see <a href=\"https://docs.aws.amazon.com/cost-management/latest/userguide/ad-SNS.html\">Creating an Amazon SNS topic for anomaly notifications</a>.</p>"}, "SubscriptionName": {"shape": "GenericString", "documentation": "<p>The name for the subscription. </p>"}, "ThresholdExpression": {"shape": "Expression", "documentation": "<p>An <a href=\"https://docs.aws.amazon.com/aws-cost-management/latest/APIReference/API_Expression.html\">Expression</a> object used to specify the anomalies that you want to generate alerts for. This supports dimensions and nested expressions. The supported dimensions are <code>ANOMALY_TOTAL_IMPACT_ABSOLUTE</code> and <code>ANOMALY_TOTAL_IMPACT_PERCENTAGE</code>, corresponding to an anomaly’s TotalImpact and TotalImpactPercentage, respectively (see <a href=\"https://docs.aws.amazon.com/aws-cost-management/latest/APIReference/API_Impact.html\">Impact</a> for more details). The supported nested expression types are <code>AND</code> and <code>OR</code>. The match option <code>GREATER_THAN_OR_EQUAL</code> is required. Values must be numbers between 0 and 10,000,000,000 in string format.</p> <p>One of Threshold or ThresholdExpression is required for this resource. You cannot specify both.</p> <p>The following are examples of valid ThresholdExpressions:</p> <ul> <li> <p>Absolute threshold: <code>{ \"Dimensions\": { \"Key\": \"ANOMALY_TOTAL_IMPACT_ABSOLUTE\", \"MatchOptions\": [ \"GREATER_THAN_OR_EQUAL\" ], \"Values\": [ \"100\" ] } }</code> </p> </li> <li> <p>Percentage threshold: <code>{ \"Dimensions\": { \"Key\": \"ANOMALY_TOTAL_IMPACT_PERCENTAGE\", \"MatchOptions\": [ \"GREATER_THAN_OR_EQUAL\" ], \"Values\": [ \"100\" ] } }</code> </p> </li> <li> <p> <code>AND</code> two thresholds together: <code>{ \"And\": [ { \"Dimensions\": { \"Key\": \"ANOMALY_TOTAL_IMPACT_ABSOLUTE\", \"MatchOptions\": [ \"GREATER_THAN_OR_EQUAL\" ], \"Values\": [ \"100\" ] } }, { \"Dimensions\": { \"Key\": \"ANOMALY_TOTAL_IMPACT_PERCENTAGE\", \"MatchOptions\": [ \"GREATER_THAN_OR_EQUAL\" ], \"Values\": [ \"100\" ] } } ] }</code> </p> </li> <li> <p> <code>OR</code> two thresholds together: <code>{ \"Or\": [ { \"Dimensions\": { \"Key\": \"ANOMALY_TOTAL_IMPACT_ABSOLUTE\", \"MatchOptions\": [ \"GREATER_THAN_OR_EQUAL\" ], \"Values\": [ \"100\" ] } }, { \"Dimensions\": { \"Key\": \"ANOMALY_TOTAL_IMPACT_PERCENTAGE\", \"MatchOptions\": [ \"GREATER_THAN_OR_EQUAL\" ], \"Values\": [ \"100\" ] } } ] }</code> </p> </li> </ul>"}}, "documentation": "<p>An <code>AnomalySubscription</code> resource (also referred to as an alert subscription) sends notifications about specific anomalies that meet an alerting criteria defined by you.</p> <p>You can specify the frequency of the alerts and the subscribers to notify.</p> <p>Anomaly subscriptions can be associated with one or more <a href=\"https://docs.aws.amazon.com/aws-cost-management/latest/APIReference/API_AnomalyMonitor.html\"> <code>AnomalyMonitor</code> </a> resources, and they only send notifications about anomalies detected by those associated monitors. You can also configure a threshold to further control which anomalies are included in the notifications.</p> <p>Anomalies that don’t exceed the chosen threshold and therefore don’t trigger notifications from an anomaly subscription will still be available on the console and from the <a href=\"https://docs.aws.amazon.com/aws-cost-management/latest/APIReference/API_GetAnomalies.html\"> <code>GetAnomalies</code> </a> API.</p>"}, "AnomalySubscriptionFrequency": {"type": "string", "enum": ["DAILY", "IMMEDIATE", "WEEKLY"]}, "AnomalySubscriptions": {"type": "list", "member": {"shape": "AnomalySubscription"}}, "ApproximateUsageRecordsPerService": {"type": "map", "key": {"shape": "GenericString"}, "value": {"shape": "NonNegativeLong"}}, "ApproximationDimension": {"type": "string", "enum": ["SERVICE", "RESOURCE"]}, "Arn": {"type": "string", "max": 2048, "min": 20, "pattern": "arn:aws[-a-z0-9]*:[a-z0-9]+:[-a-z0-9]*:[0-9]{12}:[-a-zA-Z0-9/:_]+"}, "AttributeType": {"type": "string"}, "AttributeValue": {"type": "string"}, "Attributes": {"type": "map", "key": {"shape": "AttributeType"}, "value": {"shape": "AttributeValue"}}, "BackfillLimitExceededException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p> A request to backfill is already in progress. Once the previous request is complete, you can create another request. </p>", "exception": true}, "BillExpirationException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The requested report expired. Update the date interval and try again.</p>", "exception": true}, "BillingViewArn": {"type": "string", "max": 2048, "min": 20, "pattern": "^arn:aws[a-z-]*:(billing)::[0-9]{12}:billingview/[-a-zA-Z0-9/:_+=.-@]{1,43}$"}, "CommitmentPurchaseAnalysisConfiguration": {"type": "structure", "members": {"SavingsPlansPurchaseAnalysisConfiguration": {"shape": "SavingsPlansPurchaseAnalysisConfiguration", "documentation": "<p>The configuration for the Savings Plans purchase analysis.</p>"}}, "documentation": "<p>The configuration for the commitment purchase analysis.</p>"}, "ComparisonMetricValue": {"type": "structure", "members": {"BaselineTimePeriodAmount": {"shape": "GenericString", "documentation": "<p>The numeric value for the baseline time period measurement.</p>"}, "ComparisonTimePeriodAmount": {"shape": "GenericString", "documentation": "<p>The numeric value for the comparison time period measurement.</p>"}, "Difference": {"shape": "GenericString", "documentation": "<p>The calculated difference between <code>ComparisonTimePeriodAmount</code> and <code>BaselineTimePeriodAmount</code>.</p>"}, "Unit": {"shape": "GenericString", "documentation": "<p>The unit of measurement applicable to all numeric values in this comparison.</p>"}}, "documentation": "<p>Contains cost or usage metric values for comparing two time periods. Each value includes amounts for the baseline and comparison time periods, their difference, and the unit of measurement.</p>"}, "ComparisonMetrics": {"type": "map", "key": {"shape": "MetricName"}, "value": {"shape": "ComparisonMetricValue"}}, "Context": {"type": "string", "enum": ["COST_AND_USAGE", "RESERVATIONS", "SAVINGS_PLANS"]}, "CostAllocationTag": {"type": "structure", "required": ["TagKey", "Type", "Status"], "members": {"TagKey": {"shape": "TagKey", "documentation": "<p>The key for the cost allocation tag. </p>"}, "Type": {"shape": "CostAllocationTagType", "documentation": "<p>The type of cost allocation tag. You can use <code>AWSGenerated</code> or <code>UserDefined</code> type tags. <code>AWSGenerated</code> type tags are tags that Amazon Web Services defines and applies to support Amazon Web Services resources for cost allocation purposes. <code>UserDefined</code> type tags are tags that you define, create, and apply to resources. </p>"}, "Status": {"shape": "CostAllocationTagStatus", "documentation": "<p>The status of a cost allocation tag. </p>"}, "LastUpdatedDate": {"shape": "ZonedDateTime", "documentation": "<p>The last date that the tag was either activated or deactivated.</p>"}, "LastUsedDate": {"shape": "ZonedDateTime", "documentation": "<p>The last month that the tag was used on an Amazon Web Services resource.</p>"}}, "documentation": "<p>The cost allocation tag structure. This includes detailed metadata for the <code>CostAllocationTag</code> object. </p>"}, "CostAllocationTagBackfillRequest": {"type": "structure", "members": {"BackfillFrom": {"shape": "ZonedDateTime", "documentation": "<p> The date the backfill starts from. </p>"}, "RequestedAt": {"shape": "ZonedDateTime", "documentation": "<p> The time when the backfill was requested. </p>"}, "CompletedAt": {"shape": "ZonedDateTime", "documentation": "<p> The backfill completion time. </p>"}, "BackfillStatus": {"shape": "CostAllocationTagBackfillStatus", "documentation": "<p> The status of the cost allocation tag backfill request. </p>"}, "LastUpdatedAt": {"shape": "ZonedDateTime", "documentation": "<p> The time when the backfill status was last updated. </p>"}}, "documentation": "<p> The cost allocation tag backfill request structure that contains metadata and details of a certain backfill.</p>"}, "CostAllocationTagBackfillRequestList": {"type": "list", "member": {"shape": "CostAllocationTagBackfillRequest"}, "max": 1000, "min": 0}, "CostAllocationTagBackfillStatus": {"type": "string", "enum": ["SUCCEEDED", "PROCESSING", "FAILED"]}, "CostAllocationTagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 100, "min": 1}, "CostAllocationTagList": {"type": "list", "member": {"shape": "CostAllocationTag"}, "max": 100, "min": 0}, "CostAllocationTagStatus": {"type": "string", "enum": ["Active", "Inactive"]}, "CostAllocationTagStatusEntry": {"type": "structure", "required": ["TagKey", "Status"], "members": {"TagKey": {"shape": "TagKey", "documentation": "<p>The key for the cost allocation tag. </p>"}, "Status": {"shape": "CostAllocationTagStatus", "documentation": "<p>The status of a cost allocation tag. </p>"}}, "documentation": "<p>The cost allocation tag status. The status of a key can either be active or inactive. </p>"}, "CostAllocationTagStatusList": {"type": "list", "member": {"shape": "CostAllocationTagStatusEntry"}, "max": 20, "min": 1}, "CostAllocationTagType": {"type": "string", "enum": ["AWSGenerated", "UserDefined"]}, "CostAllocationTagsMaxResults": {"type": "integer", "max": 1000, "min": 1}, "CostAndUsageComparison": {"type": "structure", "members": {"CostAndUsageSelector": {"shape": "Expression"}, "Metrics": {"shape": "ComparisonMetrics", "documentation": "<p>A mapping of metric names to their comparison values.</p>"}}, "documentation": "<p>Represents a comparison of cost and usage metrics between two time periods.</p>"}, "CostAndUsageComparisons": {"type": "list", "member": {"shape": "CostAndUsageComparison"}}, "CostAndUsageComparisonsMaxResults": {"type": "integer", "box": true, "max": 2000, "min": 1}, "CostCategory": {"type": "structure", "required": ["CostCategoryArn", "EffectiveStart", "Name", "RuleVersion", "Rules"], "members": {"CostCategoryArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The unique identifier for your Cost Category. </p>"}, "EffectiveStart": {"shape": "ZonedDateTime", "documentation": "<p>The effective start date of your Cost Category.</p>"}, "EffectiveEnd": {"shape": "ZonedDateTime", "documentation": "<p>The effective end date of your Cost Category.</p>"}, "Name": {"shape": "CostCategoryName"}, "RuleVersion": {"shape": "CostCategoryRuleVersion"}, "Rules": {"shape": "CostCategoryRulesList", "documentation": "<p>The rules are processed in order. If there are multiple rules that match the line item, then the first rule to match is used to determine that Cost Category value. </p>"}, "SplitChargeRules": {"shape": "CostCategorySplitChargeRulesList", "documentation": "<p> The split charge rules that are used to allocate your charges between your Cost Category values. </p>"}, "ProcessingStatus": {"shape": "CostCategoryProcessingStatusList", "documentation": "<p>The list of processing statuses for Cost Management products for a specific cost category. </p>"}, "DefaultValue": {"shape": "CostCategoryValue"}}, "documentation": "<p>The structure of Cost Categories. This includes detailed metadata and the set of rules for the <code>CostCategory</code> object.</p>"}, "CostCategoryInheritedValueDimension": {"type": "structure", "members": {"DimensionName": {"shape": "CostCategoryInheritedValueDimensionName", "documentation": "<p>The name of the dimension that's used to group costs.</p> <p>If you specify <code>LINKED_ACCOUNT_NAME</code>, the cost category value is based on account name. If you specify <code>TAG</code>, the cost category value is based on the value of the specified tag key.</p>"}, "DimensionKey": {"shape": "GenericString", "documentation": "<p>The key to extract cost category values.</p>"}}, "documentation": "<p>When you create or update a cost category, you can define the <code>CostCategoryRule</code> rule type as <code>INHERITED_VALUE</code>. This rule type adds the flexibility to define a rule that dynamically inherits the cost category value from the dimension value that's defined by <code>CostCategoryInheritedValueDimension</code>. For example, suppose that you want to dynamically group costs that are based on the value of a specific tag key. First, choose an inherited value rule type, and then choose the tag dimension and specify the tag key to use.</p>"}, "CostCategoryInheritedValueDimensionName": {"type": "string", "enum": ["LINKED_ACCOUNT_NAME", "TAG"]}, "CostCategoryMaxResults": {"type": "integer", "max": 100, "min": 1}, "CostCategoryName": {"type": "string", "documentation": "<p>The unique name of the Cost Category.</p>", "max": 50, "min": 1, "pattern": "^(?! )[\\p{L}\\p{N}\\p{Z}-_]*(?<! )$"}, "CostCategoryNamesList": {"type": "list", "member": {"shape": "CostCategoryName"}}, "CostCategoryProcessingStatus": {"type": "structure", "members": {"Component": {"shape": "CostCategoryStatusComponent", "documentation": "<p>The Cost Management product name of the applied status. </p>"}, "Status": {"shape": "CostCategoryStatus", "documentation": "<p>The process status for a specific cost category. </p>"}}, "documentation": "<p>The list of processing statuses for Cost Management products for a specific cost category. </p>"}, "CostCategoryProcessingStatusList": {"type": "list", "member": {"shape": "CostCategoryProcessingStatus"}}, "CostCategoryReference": {"type": "structure", "members": {"CostCategoryArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The unique identifier for your Cost Category. </p>"}, "Name": {"shape": "CostCategoryName"}, "EffectiveStart": {"shape": "ZonedDateTime", "documentation": "<p>The Cost Category's effective start date.</p>"}, "EffectiveEnd": {"shape": "ZonedDateTime", "documentation": "<p>The Cost Category's effective end date.</p>"}, "NumberOfRules": {"shape": "NonNegativeInteger", "documentation": "<p>The number of rules that are associated with a specific Cost Category. </p>"}, "ProcessingStatus": {"shape": "CostCategoryProcessingStatusList", "documentation": "<p>The list of processing statuses for Cost Management products for a specific cost category. </p>"}, "Values": {"shape": "CostCategoryValuesList", "documentation": "<p>A list of unique cost category values in a specific cost category. </p>"}, "DefaultValue": {"shape": "CostCategoryValue"}}, "documentation": "<p>A reference to a Cost Category containing only enough information to identify the Cost Category.</p> <p>You can use this information to retrieve the full Cost Category information using <code>DescribeCostCategory</code>.</p>"}, "CostCategoryReferencesList": {"type": "list", "member": {"shape": "CostCategoryReference"}}, "CostCategoryRule": {"type": "structure", "members": {"Value": {"shape": "CostCategoryValue"}, "Rule": {"shape": "Expression", "documentation": "<p>An <a href=\"https://docs.aws.amazon.com/aws-cost-management/latest/APIReference/API_Expression.html\">Expression</a> object used to categorize costs. This supports dimensions, tags, and nested expressions. Currently the only dimensions supported are <code>LINKED_ACCOUNT</code>, <code>SERVICE_CODE</code>, <code>RECORD_TYPE</code>, <code>LINKED_ACCOUNT_NAME</code>, <code>REGION</code>, and <code>USAGE_TYPE</code>.</p> <p> <code>RECORD_TYPE</code> is a dimension used for Cost Explorer APIs, and is also supported for Cost Category expressions. This dimension uses different terms, depending on whether you're using the console or API/JSON editor. For a detailed comparison, see <a href=\"https://docs.aws.amazon.com/awsaccountbilling/latest/aboutv2/manage-cost-categories.html#cost-categories-terms\">Term Comparisons</a> in the <i>Billing and Cost Management User Guide</i>.</p>"}, "InheritedValue": {"shape": "CostCategoryInheritedValueDimension", "documentation": "<p>The value the line item is categorized as if the line item contains the matched dimension.</p>"}, "Type": {"shape": "CostCategoryRuleType", "documentation": "<p>You can define the <code>CostCategoryRule</code> rule type as either <code>REGULAR</code> or <code>INHERITED_VALUE</code>. The <code>INHERITED_VALUE</code> rule type adds the flexibility to define a rule that dynamically inherits the cost category value. This value is from the dimension value that's defined by <code>CostCategoryInheritedValueDimension</code>. For example, suppose that you want to costs to be dynamically grouped based on the value of a specific tag key. First, choose an inherited value rule type, and then choose the tag dimension and specify the tag key to use.</p>"}}, "documentation": "<p>Rules are processed in order. If there are multiple rules that match the line item, then the first rule to match is used to determine that Cost Category value.</p>"}, "CostCategoryRuleType": {"type": "string", "enum": ["REGULAR", "INHERITED_VALUE"]}, "CostCategoryRuleVersion": {"type": "string", "documentation": "<p>The rule schema version in this particular Cost Category.</p>", "enum": ["CostCategoryExpression.v1"]}, "CostCategoryRulesList": {"type": "list", "member": {"shape": "CostCategoryRule"}, "max": 500, "min": 1}, "CostCategorySplitChargeMethod": {"type": "string", "enum": ["FIXED", "PROPORTIONAL", "EVEN"]}, "CostCategorySplitChargeRule": {"type": "structure", "required": ["Source", "Targets", "Method"], "members": {"Source": {"shape": "GenericString", "documentation": "<p>The Cost Category value that you want to split. That value can't be used as a source or a target in other split charge rules. To indicate uncategorized costs, you can use an empty string as the source.</p>"}, "Targets": {"shape": "CostCategorySplitChargeRuleTargetsList", "documentation": "<p>The Cost Category values that you want to split costs across. These values can't be used as a source in other split charge rules. </p>"}, "Method": {"shape": "CostCategorySplitChargeMethod", "documentation": "<p>The method that's used to define how to split your source costs across your targets. </p> <p> <code>Proportional</code> - Allocates charges across your targets based on the proportional weighted cost of each target.</p> <p> <code>Fixed</code> - Allocates charges across your targets based on your defined allocation percentage.</p> <p>&gt;<code>Even</code> - Allocates costs evenly across all targets.</p>"}, "Parameters": {"shape": "CostCategorySplitChargeRuleParametersList", "documentation": "<p>The parameters for a split charge method. This is only required for the <code>FIXED</code> method. </p>"}}, "documentation": "<p>Use the split charge rule to split the cost of one Cost Category value across several other target values. </p>"}, "CostCategorySplitChargeRuleParameter": {"type": "structure", "required": ["Type", "Values"], "members": {"Type": {"shape": "CostCategorySplitChargeRuleParameterType", "documentation": "<p>The parameter type. </p>"}, "Values": {"shape": "CostCategorySplitChargeRuleParameterValuesList", "documentation": "<p>The parameter values. </p>"}}, "documentation": "<p>The parameters for a split charge method. </p>"}, "CostCategorySplitChargeRuleParameterType": {"type": "string", "enum": ["ALLOCATION_PERCENTAGES"]}, "CostCategorySplitChargeRuleParameterValuesList": {"type": "list", "member": {"shape": "GenericString"}, "max": 500, "min": 1}, "CostCategorySplitChargeRuleParametersList": {"type": "list", "member": {"shape": "CostCategorySplitChargeRuleParameter"}, "max": 10, "min": 1}, "CostCategorySplitChargeRuleTargetsList": {"type": "list", "member": {"shape": "GenericString"}, "max": 500, "min": 1}, "CostCategorySplitChargeRulesList": {"type": "list", "member": {"shape": "CostCategorySplitChargeRule"}, "max": 10, "min": 1}, "CostCategoryStatus": {"type": "string", "enum": ["PROCESSING", "APPLIED"]}, "CostCategoryStatusComponent": {"type": "string", "enum": ["COST_EXPLORER"]}, "CostCategoryValue": {"type": "string", "documentation": "<p>The default value for the cost category.</p>", "max": 50, "min": 1, "pattern": "^(?! )[\\p{L}\\p{N}\\p{Z}-_]*(?<! )$"}, "CostCategoryValues": {"type": "structure", "members": {"Key": {"shape": "CostCategoryName"}, "Values": {"shape": "Values", "documentation": "<p>The specific value of the Cost Category.</p>"}, "MatchOptions": {"shape": "MatchOptions", "documentation": "<p>The match options that you can use to filter your results. MatchOptions is only applicable for actions related to cost category. The default values for <code>MatchOptions</code> is <code>EQUALS</code> and <code>CASE_SENSITIVE</code>. </p>"}}, "documentation": "<p>The Cost Categories values used for filtering the costs.</p> <p>If <code>Values</code> and <code>Key</code> are not specified, the <code>ABSENT</code> <code>MatchOption</code> is applied to all Cost Categories. That is, it filters on resources that aren't mapped to any Cost Categories.</p> <p>If <code>Values</code> is provided and <code>Key</code> isn't specified, the <code>ABSENT</code> <code>MatchOption</code> is applied to the Cost Categories <code>Key</code> only. That is, it filters on resources without the given Cost Categories key.</p>"}, "CostCategoryValuesList": {"type": "list", "member": {"shape": "CostCategoryValue"}}, "CostComparisonDriver": {"type": "structure", "members": {"CostSelector": {"shape": "Expression"}, "Metrics": {"shape": "ComparisonMetrics", "documentation": "<p>A mapping of metric names to their comparison values.</p>"}, "CostDrivers": {"shape": "CostDrivers", "documentation": "<p>An array of cost drivers, each representing a cost difference between the baseline and comparison time periods. Each entry also includes a metric delta (for example, usage change) that contributed to the cost variance, along with the identifier and type of change.</p>"}}, "documentation": "<p>Represents a collection of cost drivers and their associated metrics for cost comparison analysis.</p>"}, "CostComparisonDrivers": {"type": "list", "member": {"shape": "CostComparisonDriver"}}, "CostComparisonDriversMaxResults": {"type": "integer", "box": true, "max": 10, "min": 1}, "CostDriver": {"type": "structure", "members": {"Type": {"shape": "GenericString", "documentation": "<p>The category or classification of the cost driver.</p> <p>Values include: BUNDLED_DISCOUNT, CREDIT, OUT_OF_CYCLE_CHARGE, REFUND, RECURRING_RESERVATION_FEE, RESERVATION_USAGE, RI_VOLUME_DISCOUNT, SAVINGS_PLAN_USAGE, SAVINGS_PLAN_NEGATION, SAVINGS_PLAN_RECURRING_FEE, SUPPORT_FEE, TAX, UPFRONT_RESERVATION_FEE, USAGE_CHANGE, COMMITMENT</p>"}, "Name": {"shape": "GenericString", "documentation": "<p>The specific identifier of the cost driver.</p>"}, "Metrics": {"shape": "ComparisonMetrics", "documentation": "<p>A mapping of metric names to their comparison values, measuring the impact of this cost driver.</p>"}}, "documentation": "<p>Represents factors that contribute to cost variations between the baseline and comparison time periods, including the type of driver, an identifier of the driver, and associated metrics.</p>"}, "CostDrivers": {"type": "list", "member": {"shape": "CostDriver"}}, "Coverage": {"type": "structure", "members": {"CoverageHours": {"shape": "CoverageHours", "documentation": "<p>The amount of instance usage that the reservation covered, in hours.</p>"}, "CoverageNormalizedUnits": {"shape": "CoverageNormalizedUnits", "documentation": "<p>The amount of instance usage that the reservation covered, in normalized units.</p>"}, "CoverageCost": {"shape": "CoverageCost", "documentation": "<p>The amount of cost that the reservation covered.</p>"}}, "documentation": "<p>The amount of instance usage that a reservation covered.</p>"}, "CoverageByTime": {"type": "structure", "members": {"TimePeriod": {"shape": "DateInterval", "documentation": "<p>The period that this coverage was used over.</p>"}, "Groups": {"shape": "ReservationCoverageGroups", "documentation": "<p>The groups of instances that the reservation covered.</p>"}, "Total": {"shape": "Coverage", "documentation": "<p>The total reservation coverage, in hours.</p>"}}, "documentation": "<p>Reservation coverage for a specified period, in hours.</p>"}, "CoverageCost": {"type": "structure", "members": {"OnDemandCost": {"shape": "OnDemandCost", "documentation": "<p>How much an On-Demand Instance costs.</p>"}}, "documentation": "<p>How much it costs to run an instance.</p>"}, "CoverageHours": {"type": "structure", "members": {"OnDemandHours": {"shape": "OnDemandHours", "documentation": "<p>The number of instance running hours that On-Demand Instances covered.</p>"}, "ReservedHours": {"shape": "ReservedHours", "documentation": "<p>The number of instance running hours that reservations covered.</p>"}, "TotalRunningHours": {"shape": "TotalRunningHours", "documentation": "<p>The total instance usage, in hours.</p>"}, "CoverageHoursPercentage": {"shape": "CoverageHoursPercentage", "documentation": "<p>The percentage of instance hours that a reservation covered.</p>"}}, "documentation": "<p>How long a running instance either used a reservation or was On-Demand.</p>"}, "CoverageHoursPercentage": {"type": "string"}, "CoverageNormalizedUnits": {"type": "structure", "members": {"OnDemandNormalizedUnits": {"shape": "OnDemandNormalizedUnits", "documentation": "<p>The number of normalized units that are covered by On-Demand Instances instead of a reservation.</p>"}, "ReservedNormalizedUnits": {"shape": "ReservedNormalizedUnits", "documentation": "<p>The number of normalized units that a reservation covers.</p>"}, "TotalRunningNormalizedUnits": {"shape": "TotalRunningNormalizedUnits", "documentation": "<p>The total number of normalized units that you used.</p>"}, "CoverageNormalizedUnitsPercentage": {"shape": "CoverageNormalizedUnitsPercentage", "documentation": "<p>The percentage of your used instance normalized units that a reservation covers.</p>"}}, "documentation": "<p>The amount of instance usage, in normalized units. You can use normalized units to see your EC2 usage for multiple sizes of instances in a uniform way. For example, suppose that you run an xlarge instance and a 2xlarge instance. If you run both instances for the same amount of time, the 2xlarge instance uses twice as much of your reservation as the xlarge instance, even though both instances show only one instance-hour. When you use normalized units instead of instance-hours, the xlarge instance used 8 normalized units, and the 2xlarge instance used 16 normalized units.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/ri-modifying.html\">Modifying Reserved Instances</a> in the <i>Amazon Elastic Compute Cloud User Guide for Linux Instances</i>.</p>"}, "CoverageNormalizedUnitsPercentage": {"type": "string"}, "CoveragesByTime": {"type": "list", "member": {"shape": "CoverageByTime"}}, "CreateAnomalyMonitorRequest": {"type": "structure", "required": ["AnomalyMonitor"], "members": {"AnomalyMonitor": {"shape": "AnomalyMonitor", "documentation": "<p>The cost anomaly detection monitor object that you want to create.</p>"}, "ResourceTags": {"shape": "ResourceTagList", "documentation": "<p>An optional list of tags to associate with the specified <a href=\"https://docs.aws.amazon.com/aws-cost-management/latest/APIReference/API_AnomalyMonitor.html\"> <code>AnomalyMonitor</code> </a>. You can use resource tags to control access to your <code>monitor</code> using IAM policies.</p> <p>Each tag consists of a key and a value, and each key must be unique for the resource. The following restrictions apply to resource tags:</p> <ul> <li> <p>Although the maximum number of array members is 200, you can assign a maximum of 50 user-tags to one resource. The remaining are reserved for Amazon Web Services use</p> </li> <li> <p>The maximum length of a key is 128 characters</p> </li> <li> <p>The maximum length of a value is 256 characters</p> </li> <li> <p>Keys and values can only contain alphanumeric characters, spaces, and any of the following: <code>_.:/=+@-</code> </p> </li> <li> <p>Keys and values are case sensitive</p> </li> <li> <p>Keys and values are trimmed for any leading or trailing whitespaces</p> </li> <li> <p>Don’t use <code>aws:</code> as a prefix for your keys. This prefix is reserved for Amazon Web Services use</p> </li> </ul>"}}}, "CreateAnomalyMonitorResponse": {"type": "structure", "required": ["MonitorArn"], "members": {"MonitorArn": {"shape": "GenericString", "documentation": "<p>The unique identifier of your newly created cost anomaly detection monitor.</p>"}}}, "CreateAnomalySubscriptionRequest": {"type": "structure", "required": ["AnomalySubscription"], "members": {"AnomalySubscription": {"shape": "AnomalySubscription", "documentation": "<p>The cost anomaly subscription object that you want to create. </p>"}, "ResourceTags": {"shape": "ResourceTagList", "documentation": "<p>An optional list of tags to associate with the specified <a href=\"https://docs.aws.amazon.com/aws-cost-management/latest/APIReference/API_AnomalySubscription.html\"> <code>AnomalySubscription</code> </a>. You can use resource tags to control access to your <code>subscription</code> using IAM policies.</p> <p>Each tag consists of a key and a value, and each key must be unique for the resource. The following restrictions apply to resource tags:</p> <ul> <li> <p>Although the maximum number of array members is 200, you can assign a maximum of 50 user-tags to one resource. The remaining are reserved for Amazon Web Services use</p> </li> <li> <p>The maximum length of a key is 128 characters</p> </li> <li> <p>The maximum length of a value is 256 characters</p> </li> <li> <p>Keys and values can only contain alphanumeric characters, spaces, and any of the following: <code>_.:/=+@-</code> </p> </li> <li> <p>Keys and values are case sensitive</p> </li> <li> <p>Keys and values are trimmed for any leading or trailing whitespaces</p> </li> <li> <p>Don’t use <code>aws:</code> as a prefix for your keys. This prefix is reserved for Amazon Web Services use</p> </li> </ul>"}}}, "CreateAnomalySubscriptionResponse": {"type": "structure", "required": ["SubscriptionArn"], "members": {"SubscriptionArn": {"shape": "GenericString", "documentation": "<p>The unique identifier of your newly created cost anomaly subscription. </p>"}}}, "CreateCostCategoryDefinitionRequest": {"type": "structure", "required": ["Name", "RuleVersion", "Rules"], "members": {"Name": {"shape": "CostCategoryName"}, "EffectiveStart": {"shape": "ZonedDateTime", "documentation": "<p>The Cost Category's effective start date. It can only be a billing start date (first day of the month). If the date isn't provided, it's the first day of the current month. Dates can't be before the previous twelve months, or in the future.</p>"}, "RuleVersion": {"shape": "CostCategoryRuleVersion"}, "Rules": {"shape": "CostCategoryRulesList", "documentation": "<p>The Cost Category rules used to categorize costs. For more information, see <a href=\"https://docs.aws.amazon.com/aws-cost-management/latest/APIReference/API_CostCategoryRule.html\">CostCategoryRule</a>.</p>"}, "DefaultValue": {"shape": "CostCategoryValue"}, "SplitChargeRules": {"shape": "CostCategorySplitChargeRulesList", "documentation": "<p> The split charge rules used to allocate your charges between your Cost Category values. </p>"}, "ResourceTags": {"shape": "ResourceTagList", "documentation": "<p>An optional list of tags to associate with the specified <a href=\"https://docs.aws.amazon.com/aws-cost-management/latest/APIReference/API_CostCategory.html\"> <code>CostCategory</code> </a>. You can use resource tags to control access to your <code>cost category</code> using IAM policies.</p> <p>Each tag consists of a key and a value, and each key must be unique for the resource. The following restrictions apply to resource tags:</p> <ul> <li> <p>Although the maximum number of array members is 200, you can assign a maximum of 50 user-tags to one resource. The remaining are reserved for Amazon Web Services use</p> </li> <li> <p>The maximum length of a key is 128 characters</p> </li> <li> <p>The maximum length of a value is 256 characters</p> </li> <li> <p>Keys and values can only contain alphanumeric characters, spaces, and any of the following: <code>_.:/=+@-</code> </p> </li> <li> <p>Keys and values are case sensitive</p> </li> <li> <p>Keys and values are trimmed for any leading or trailing whitespaces</p> </li> <li> <p>Don’t use <code>aws:</code> as a prefix for your keys. This prefix is reserved for Amazon Web Services use</p> </li> </ul>"}}}, "CreateCostCategoryDefinitionResponse": {"type": "structure", "members": {"CostCategoryArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The unique identifier for your newly created Cost Category. </p>"}, "EffectiveStart": {"shape": "ZonedDateTime", "documentation": "<p>The Cost Category's effective start date. It can only be a billing start date (first day of the month).</p>"}}}, "CurrentInstance": {"type": "structure", "members": {"ResourceId": {"shape": "GenericString", "documentation": "<p>Resource ID of the current instance.</p>"}, "InstanceName": {"shape": "GenericString", "documentation": "<p>The name that you given an instance. This field shows as blank if you haven't given the instance a name.</p>"}, "Tags": {"shape": "TagValuesList", "documentation": "<p>Cost allocation resource tags that are applied to the instance.</p>"}, "ResourceDetails": {"shape": "ResourceDetails", "documentation": "<p>Details about the resource and utilization.</p>"}, "ResourceUtilization": {"shape": "ResourceUtilization", "documentation": "<p>Utilization information of the current instance during the lookback period.</p>"}, "ReservationCoveredHoursInLookbackPeriod": {"shape": "GenericString", "documentation": "<p>The number of hours during the lookback period that's covered by reservations.</p>"}, "SavingsPlansCoveredHoursInLookbackPeriod": {"shape": "GenericString", "documentation": "<p>The number of hours during the lookback period that's covered by Savings Plans.</p>"}, "OnDemandHoursInLookbackPeriod": {"shape": "GenericString", "documentation": "<p>The number of hours during the lookback period that's billed at On-Demand rates.</p>"}, "TotalRunningHoursInLookbackPeriod": {"shape": "GenericString", "documentation": "<p>The total number of hours that the instance ran during the lookback period.</p>"}, "MonthlyCost": {"shape": "GenericString", "documentation": "<p>The current On-Demand cost of operating this instance on a monthly basis.</p>"}, "CurrencyCode": {"shape": "GenericString", "documentation": "<p>The currency code that Amazon Web Services used to calculate the costs for this instance.</p>"}}, "documentation": "<p>Context about the current instance.</p>"}, "DataUnavailableException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The requested data is unavailable.</p>", "exception": true}, "DateInterval": {"type": "structure", "required": ["Start", "End"], "members": {"Start": {"shape": "YearMonthDay", "documentation": "<p>The beginning of the time period. The start date is inclusive. For example, if <code>start</code> is <code>2017-01-01</code>, Amazon Web Services retrieves cost and usage data starting at <code>2017-01-01</code> up to the end date. The start date must be equal to or no later than the current date to avoid a validation error.</p>"}, "End": {"shape": "YearMonthDay", "documentation": "<p>The end of the time period. The end date is exclusive. For example, if <code>end</code> is <code>2017-05-01</code>, Amazon Web Services retrieves cost and usage data from the start date up to, but not including, <code>2017-05-01</code>.</p>"}}, "documentation": "<p>The time period of the request. </p>"}, "DeleteAnomalyMonitorRequest": {"type": "structure", "required": ["MonitorArn"], "members": {"MonitorArn": {"shape": "GenericString", "documentation": "<p>The unique identifier of the cost anomaly monitor that you want to delete. </p>"}}}, "DeleteAnomalyMonitorResponse": {"type": "structure", "members": {}}, "DeleteAnomalySubscriptionRequest": {"type": "structure", "required": ["SubscriptionArn"], "members": {"SubscriptionArn": {"shape": "GenericString", "documentation": "<p>The unique identifier of the cost anomaly subscription that you want to delete. </p>"}}}, "DeleteAnomalySubscriptionResponse": {"type": "structure", "members": {}}, "DeleteCostCategoryDefinitionRequest": {"type": "structure", "required": ["CostCategoryArn"], "members": {"CostCategoryArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The unique identifier for your Cost Category. </p>"}}}, "DeleteCostCategoryDefinitionResponse": {"type": "structure", "members": {"CostCategoryArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The unique identifier for your Cost Category. </p>"}, "EffectiveEnd": {"shape": "ZonedDateTime", "documentation": "<p>The effective end date of the Cost Category as a result of deleting it. No costs after this date is categorized by the deleted Cost Category. </p>"}}}, "DescribeCostCategoryDefinitionRequest": {"type": "structure", "required": ["CostCategoryArn"], "members": {"CostCategoryArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The unique identifier for your Cost Category. </p>"}, "EffectiveOn": {"shape": "ZonedDateTime", "documentation": "<p>The date when the Cost Category was effective. </p>"}}}, "DescribeCostCategoryDefinitionResponse": {"type": "structure", "members": {"CostCategory": {"shape": "CostCategory"}}}, "Dimension": {"type": "string", "enum": ["AZ", "INSTANCE_TYPE", "LINKED_ACCOUNT", "LINKED_ACCOUNT_NAME", "OPERATION", "PURCHASE_TYPE", "REGION", "SERVICE", "SERVICE_CODE", "USAGE_TYPE", "USAGE_TYPE_GROUP", "RECORD_TYPE", "OPERATING_SYSTEM", "TENANCY", "SCOPE", "PLATFORM", "SUBSCRIPTION_ID", "LEGAL_ENTITY_NAME", "DEPLOYMENT_OPTION", "DATABASE_ENGINE", "CACHE_ENGINE", "INSTANCE_TYPE_FAMILY", "BILLING_ENTITY", "RESERVATION_ID", "RESOURCE_ID", "RIGHTSIZING_TYPE", "SAVINGS_PLANS_TYPE", "SAVINGS_PLAN_ARN", "PAYMENT_OPTION", "AGREEMENT_END_DATE_TIME_AFTER", "AGREEMENT_END_DATE_TIME_BEFORE", "INVOICING_ENTITY", "ANOMALY_TOTAL_IMPACT_ABSOLUTE", "ANOMALY_TOTAL_IMPACT_PERCENTAGE"]}, "DimensionValues": {"type": "structure", "members": {"Key": {"shape": "Dimension", "documentation": "<p>The names of the metadata types that you can use to filter and group your results. For example, <code>AZ</code> returns a list of Availability Zones.</p> <p>Not all dimensions are supported in each API. Refer to the documentation for each specific API to see what is supported.</p> <p> <code>LINKED_ACCOUNT_NAME</code> and <code>SERVICE_CODE</code> can only be used in <a href=\"https://docs.aws.amazon.com/aws-cost-management/latest/APIReference/API_CostCategoryRule.html\">CostCategoryRule</a>.</p> <p> <code>ANOMALY_TOTAL_IMPACT_ABSOLUTE</code> and <code>ANOMALY_TOTAL_IMPACT_PERCENTAGE</code> can only be used in <a href=\"https://docs.aws.amazon.com/aws-cost-management/latest/APIReference/API_AnomalySubscription.html\">AnomalySubscriptions</a>.</p>"}, "Values": {"shape": "Values", "documentation": "<p>The metadata values that you can use to filter and group your results. You can use <code>GetDimensionValues</code> to find specific values.</p>"}, "MatchOptions": {"shape": "MatchOptions", "documentation": "<p>The match options that you can use to filter your results.</p> <p> <code>MatchOptions</code> is only applicable for actions related to Cost Category and Anomaly Subscriptions. Refer to the documentation for each specific API to see what is supported.</p> <p>The default values for <code>MatchOptions</code> are <code>EQUALS</code> and <code>CASE_SENSITIVE</code>.</p>"}}, "documentation": "<p>The metadata that you can use to filter and group your results. You can use <code>GetDimensionValues</code> to find specific values.</p>"}, "DimensionValuesWithAttributes": {"type": "structure", "members": {"Value": {"shape": "Value", "documentation": "<p>The value of a dimension with a specific attribute.</p>"}, "Attributes": {"shape": "Attributes", "documentation": "<p>The attribute that applies to a specific <code>Dimension</code>.</p>"}}, "documentation": "<p>The metadata of a specific type that you can use to filter and group your results. You can use <code>GetDimensionValues</code> to find specific values.</p>"}, "DimensionValuesWithAttributesList": {"type": "list", "member": {"shape": "DimensionValuesWithAttributes"}}, "DiskResourceUtilization": {"type": "structure", "members": {"DiskReadOpsPerSecond": {"shape": "GenericString", "documentation": "<p>The maximum number of read operations per second. </p>"}, "DiskWriteOpsPerSecond": {"shape": "GenericString", "documentation": "<p>The maximum number of write operations per second. </p>"}, "DiskReadBytesPerSecond": {"shape": "GenericString", "documentation": "<p>The maximum read throughput operations per second. </p>"}, "DiskWriteBytesPerSecond": {"shape": "GenericString", "documentation": "<p>The maximum write throughput operations per second. </p>"}}, "documentation": "<p>The field that contains a list of disk (local storage) metrics that are associated with the current instance. </p>"}, "DynamoDBCapacityDetails": {"type": "structure", "members": {"CapacityUnits": {"shape": "GenericString", "documentation": "<p>The capacity unit of the recommended reservation.</p>"}, "Region": {"shape": "GenericString", "documentation": "<p>The Amazon Web Services Region of the recommended reservation.</p>"}}, "documentation": "<p>The DynamoDB reservations that Amazon Web Services recommends that you purchase.</p>"}, "EBSResourceUtilization": {"type": "structure", "members": {"EbsReadOpsPerSecond": {"shape": "GenericString", "documentation": "<p>The maximum number of read operations per second. </p>"}, "EbsWriteOpsPerSecond": {"shape": "GenericString", "documentation": "<p>The maximum number of write operations per second. </p>"}, "EbsReadBytesPerSecond": {"shape": "GenericString", "documentation": "<p>The maximum size of read operations per second </p>"}, "EbsWriteBytesPerSecond": {"shape": "GenericString", "documentation": "<p>The maximum size of write operations per second. </p>"}}, "documentation": "<p>The EBS field that contains a list of EBS metrics that are associated with the current instance. </p>"}, "EC2InstanceDetails": {"type": "structure", "members": {"Family": {"shape": "GenericString", "documentation": "<p>The instance family of the recommended reservation.</p>"}, "InstanceType": {"shape": "GenericString", "documentation": "<p>The type of instance that Amazon Web Services recommends.</p>"}, "Region": {"shape": "GenericString", "documentation": "<p>The Amazon Web Services Region of the recommended reservation.</p>"}, "AvailabilityZone": {"shape": "GenericString", "documentation": "<p>The Availability Zone of the recommended reservation.</p>"}, "Platform": {"shape": "GenericString", "documentation": "<p>The platform of the recommended reservation. The platform is the specific combination of operating system, license model, and software on an instance.</p>"}, "Tenancy": {"shape": "GenericString", "documentation": "<p>Determines whether the recommended reservation is dedicated or shared.</p>"}, "CurrentGeneration": {"shape": "GenericBoolean", "documentation": "<p>Determines whether the recommendation is for a current-generation instance. </p>"}, "SizeFlexEligible": {"shape": "GenericBoolean", "documentation": "<p>Determines whether the recommended reservation is size flexible.</p>"}}, "documentation": "<p>Details about the Amazon EC2 reservations that Amazon Web Services recommends that you purchase.</p>"}, "EC2ResourceDetails": {"type": "structure", "members": {"HourlyOnDemandRate": {"shape": "GenericString", "documentation": "<p>The hourly public On-Demand rate for the instance type.</p>"}, "InstanceType": {"shape": "GenericString", "documentation": "<p>The type of Amazon Web Services instance.</p>"}, "Platform": {"shape": "GenericString", "documentation": "<p>The platform of the Amazon Web Services instance. The platform is the specific combination of operating system, license model, and software on an instance.</p>"}, "Region": {"shape": "GenericString", "documentation": "<p>The Amazon Web Services Region of the instance.</p>"}, "Sku": {"shape": "GenericString", "documentation": "<p>The SKU of the product.</p>"}, "Memory": {"shape": "GenericString", "documentation": "<p>The memory capacity of the Amazon Web Services instance.</p>"}, "NetworkPerformance": {"shape": "GenericString", "documentation": "<p>The network performance capacity of the Amazon Web Services instance.</p>"}, "Storage": {"shape": "GenericString", "documentation": "<p>The disk storage of the Amazon Web Services instance. This doesn't include EBS storage.</p>"}, "Vcpu": {"shape": "GenericString", "documentation": "<p>The number of VCPU cores in the Amazon Web Services instance type.</p>"}}, "documentation": "<p>Details on the Amazon EC2 Resource.</p>"}, "EC2ResourceUtilization": {"type": "structure", "members": {"MaxCpuUtilizationPercentage": {"shape": "GenericString", "documentation": "<p>The maximum observed or expected CPU utilization of the instance.</p>"}, "MaxMemoryUtilizationPercentage": {"shape": "GenericString", "documentation": "<p>The maximum observed or expected memory utilization of the instance.</p>"}, "MaxStorageUtilizationPercentage": {"shape": "GenericString", "documentation": "<p>The maximum observed or expected storage utilization of the instance. This doesn't include EBS storage.</p>"}, "EBSResourceUtilization": {"shape": "EBSResourceUtilization", "documentation": "<p>The EBS field that contains a list of EBS metrics that are associated with the current instance. </p>"}, "DiskResourceUtilization": {"shape": "DiskResourceUtilization", "documentation": "<p>The field that contains a list of disk (local storage) metrics that are associated with the current instance. </p>"}, "NetworkResourceUtilization": {"shape": "NetworkResourceUtilization", "documentation": "<p>The network field that contains a list of network metrics that are associated with the current instance. </p>"}}, "documentation": "<p>Utilization metrics for the instance. </p>"}, "EC2Specification": {"type": "structure", "members": {"OfferingClass": {"shape": "OfferingClass", "documentation": "<p>Indicates whether you want a recommendation for standard or convertible reservations.</p>"}}, "documentation": "<p>The Amazon EC2 hardware specifications that you want Amazon Web Services to provide recommendations for.</p>"}, "ESInstanceDetails": {"type": "structure", "members": {"InstanceClass": {"shape": "GenericString", "documentation": "<p>The class of instance that Amazon Web Services recommends.</p>"}, "InstanceSize": {"shape": "GenericString", "documentation": "<p>The size of instance that Amazon Web Services recommends.</p>"}, "Region": {"shape": "GenericString", "documentation": "<p>The Amazon Web Services Region of the recommended reservation.</p>"}, "CurrentGeneration": {"shape": "GenericBoolean", "documentation": "<p>Determines whether the recommendation is for a current-generation instance.</p>"}, "SizeFlexEligible": {"shape": "GenericBoolean", "documentation": "<p>Determines whether the recommended reservation is size flexible.</p>"}}, "documentation": "<p>Details about the Amazon OpenSearch Service reservations that Amazon Web Services recommends that you purchase.</p>"}, "ElastiCacheInstanceDetails": {"type": "structure", "members": {"Family": {"shape": "GenericString", "documentation": "<p>The instance family of the recommended reservation.</p>"}, "NodeType": {"shape": "GenericString", "documentation": "<p>The type of node that Amazon Web Services recommends.</p>"}, "Region": {"shape": "GenericString", "documentation": "<p>The Amazon Web Services Region of the recommended reservation.</p>"}, "ProductDescription": {"shape": "GenericString", "documentation": "<p>The description of the recommended reservation.</p>"}, "CurrentGeneration": {"shape": "GenericBoolean", "documentation": "<p>Determines whether the recommendation is for a current generation instance.</p>"}, "SizeFlexEligible": {"shape": "GenericBoolean", "documentation": "<p>Determines whether the recommended reservation is size flexible.</p>"}}, "documentation": "<p>Details about the Amazon ElastiCache reservations that Amazon Web Services recommends that you purchase.</p>"}, "Entity": {"type": "string"}, "ErrorCode": {"type": "string", "enum": ["NO_USAGE_FOUND", "INTERNAL_FAILURE", "INVALID_SAVINGS_PLANS_TO_ADD", "INVALID_SAVINGS_PLANS_TO_EXCLUDE", "INVALID_ACCOUNT_ID"]}, "ErrorMessage": {"type": "string"}, "Estimated": {"type": "boolean"}, "Expression": {"type": "structure", "members": {"Or": {"shape": "Expressions", "documentation": "<p>Return results that match either <code>Dimension</code> object.</p>"}, "And": {"shape": "Expressions", "documentation": "<p>Return results that match both <code>Dimension</code> objects.</p>"}, "Not": {"shape": "Expression", "documentation": "<p>Return results that don't match a <code>Dimension</code> object.</p>"}, "Dimensions": {"shape": "DimensionValues", "documentation": "<p>The specific <code>Dimension</code> to use for <code>Expression</code>.</p>"}, "Tags": {"shape": "TagValues", "documentation": "<p>The specific <code>Tag</code> to use for <code>Expression</code>.</p>"}, "CostCategories": {"shape": "CostCategoryValues", "documentation": "<p>The filter that's based on <code>CostCategory</code> values.</p>"}}, "documentation": "<p>Use <code>Expression</code> to filter in various Cost Explorer APIs.</p> <p>Not all <code>Expression</code> types are supported in each API. Refer to the documentation for each specific API to see what is supported.</p> <p>There are two patterns:</p> <ul> <li> <p>Simple dimension values.</p> <ul> <li> <p>There are three types of simple dimension values: <code>CostCategories</code>, <code>Tags</code>, and <code>Dimensions</code>.</p> <ul> <li> <p>Specify the <code>CostCategories</code> field to define a filter that acts on Cost Categories.</p> </li> <li> <p>Specify the <code>Tags</code> field to define a filter that acts on Cost Allocation Tags.</p> </li> <li> <p>Specify the <code>Dimensions</code> field to define a filter that acts on the <a href=\"https://docs.aws.amazon.com/aws-cost-management/latest/APIReference/API_DimensionValues.html\"> <code>DimensionValues</code> </a>.</p> </li> </ul> </li> <li> <p>For each filter type, you can set the dimension name and values for the filters that you plan to use.</p> <ul> <li> <p>For example, you can filter for <code>REGION==us-east-1 OR REGION==us-west-1</code>. For <code>GetRightsizingRecommendation</code>, the Region is a full name (for example, <code>REGION==US East (N. Virginia)</code>.</p> </li> <li> <p>The corresponding <code>Expression</code> for this example is as follows: <code>{ \"Dimensions\": { \"Key\": \"REGION\", \"Values\": [ \"us-east-1\", \"us-west-1\" ] } }</code> </p> </li> <li> <p>As shown in the previous example, lists of dimension values are combined with <code>OR</code> when applying the filter.</p> </li> </ul> </li> <li> <p>You can also set different match options to further control how the filter behaves. Not all APIs support match options. Refer to the documentation for each specific API to see what is supported.</p> <ul> <li> <p>For example, you can filter for linked account names that start with \"a\".</p> </li> <li> <p>The corresponding <code>Expression</code> for this example is as follows: <code>{ \"Dimensions\": { \"Key\": \"LINKED_ACCOUNT_NAME\", \"MatchOptions\": [ \"STARTS_WITH\" ], \"Values\": [ \"a\" ] } }</code> </p> </li> </ul> </li> </ul> </li> <li> <p>Compound <code>Expression</code> types with logical operations.</p> <ul> <li> <p>You can use multiple <code>Expression</code> types and the logical operators <code>AND/OR/NOT</code> to create a list of one or more <code>Expression</code> objects. By doing this, you can filter by more advanced options.</p> </li> <li> <p>For example, you can filter by <code>((REGION == us-east-1 OR REGION == us-west-1) OR (TAG.Type == Type1)) AND (USAGE_TYPE != DataTransfer)</code>.</p> </li> <li> <p>The corresponding <code>Expression</code> for this example is as follows: <code>{ \"And\": [ {\"Or\": [ {\"Dimensions\": { \"Key\": \"REGION\", \"Values\": [ \"us-east-1\", \"us-west-1\" ] }}, {\"Tags\": { \"Key\": \"TagName\", \"Values\": [\"Value1\"] } } ]}, {\"Not\": {\"Dimensions\": { \"Key\": \"USAGE_TYPE\", \"Values\": [\"DataTransfer\"] }}} ] } </code> </p> </li> </ul> <note> <p>Because each <code>Expression</code> can have only one operator, the service returns an error if more than one is specified. The following example shows an <code>Expression</code> object that creates an error: <code> { \"And\": [ ... ], \"Dimensions\": { \"Key\": \"USAGE_TYPE\", \"Values\": [ \"DataTransfer\" ] } } </code> </p> <p>The following is an example of the corresponding error message: <code>\"Expression has more than one roots. Only one root operator is allowed for each expression: And, Or, Not, Dimensions, Tags, CostCategories\"</code> </p> </note> </li> </ul> <note> <p>For the <code>GetRightsizingRecommendation</code> action, a combination of OR and NOT isn't supported. OR isn't supported between different dimensions, or dimensions and tags. NOT operators aren't supported. Dimensions are also limited to <code>LINKED_ACCOUNT</code>, <code>REGION</code>, or <code>RIGHTSIZING_TYPE</code>.</p> <p>For the <code>GetReservationPurchaseRecommendation</code> action, only NOT is supported. AND and OR aren't supported. Dimensions are limited to <code>LINKED_ACCOUNT</code>.</p> </note>"}, "Expressions": {"type": "list", "member": {"shape": "Expression"}}, "FindingReasonCode": {"type": "string", "enum": ["CPU_OVER_PROVISIONED", "CPU_UNDER_PROVISIONED", "MEMORY_OVER_PROVISIONED", "MEMORY_UNDER_PROVISIONED", "EBS_THROUGHPUT_OVER_PROVISIONED", "EBS_THROUGHPUT_UNDER_PROVISIONED", "EBS_IOPS_OVER_PROVISIONED", "EBS_IOPS_UNDER_PROVISIONED", "NETWORK_BANDWIDTH_OVER_PROVISIONED", "NETWORK_BANDWIDTH_UNDER_PROVISIONED", "NETWORK_PPS_OVER_PROVISIONED", "NETWORK_PPS_UNDER_PROVISIONED", "DISK_IOPS_OVER_PROVISIONED", "DISK_IOPS_UNDER_PROVISIONED", "DISK_THROUGHPUT_OVER_PROVISIONED", "DISK_THROUGHPUT_UNDER_PROVISIONED"]}, "FindingReasonCodes": {"type": "list", "member": {"shape": "FindingReasonCode"}}, "ForecastResult": {"type": "structure", "members": {"TimePeriod": {"shape": "DateInterval", "documentation": "<p>The period of time that the forecast covers.</p>"}, "MeanValue": {"shape": "GenericString", "documentation": "<p>The mean value of the forecast.</p>"}, "PredictionIntervalLowerBound": {"shape": "GenericString", "documentation": "<p>The lower limit for the prediction interval. </p>"}, "PredictionIntervalUpperBound": {"shape": "GenericString", "documentation": "<p>The upper limit for the prediction interval. </p>"}}, "documentation": "<p>The forecast that's created for your query.</p>"}, "ForecastResultsByTime": {"type": "list", "member": {"shape": "ForecastResult"}}, "GenerationExistsException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>A request to generate a recommendation or analysis is already in progress.</p>", "exception": true}, "GenerationStatus": {"type": "string", "enum": ["SUCCEEDED", "PROCESSING", "FAILED"]}, "GenerationSummary": {"type": "structure", "members": {"RecommendationId": {"shape": "RecommendationId", "documentation": "<p>Indicates the ID for this specific recommendation.</p>"}, "GenerationStatus": {"shape": "GenerationStatus", "documentation": "<p>Indicates whether the recommendation generation succeeded, is processing, or failed.</p>"}, "GenerationStartedTime": {"shape": "ZonedDateTime", "documentation": "<p>Indicates the start time of the recommendation generation.</p>"}, "GenerationCompletionTime": {"shape": "ZonedDateTime", "documentation": "<p>Indicates the completion time of the recommendation generation.</p>"}, "EstimatedCompletionTime": {"shape": "ZonedDateTime", "documentation": "<p>Indicates the estimated time for when the recommendation generation will complete.</p>"}}, "documentation": "<p>The summary of the Savings Plans recommendation generation.</p>"}, "GenerationSummaryList": {"type": "list", "member": {"shape": "GenerationSummary"}}, "GenericBoolean": {"type": "boolean"}, "GenericDouble": {"type": "double"}, "GenericString": {"type": "string", "max": 1024, "min": 0, "pattern": "[\\S\\s]*"}, "GetAnomaliesRequest": {"type": "structure", "required": ["DateInterval"], "members": {"MonitorArn": {"shape": "GenericString", "documentation": "<p>Retrieves all of the cost anomalies detected for a specific cost anomaly monitor Amazon Resource Name (ARN). </p>"}, "DateInterval": {"shape": "AnomalyDateInterval", "documentation": "<p>Assigns the start and end dates for retrieving cost anomalies. The returned anomaly object will have an <code>AnomalyEndDate</code> in the specified time range. </p>"}, "Feedback": {"shape": "AnomalyFeedbackType", "documentation": "<p>Filters anomaly results by the feedback field on the anomaly object. </p>"}, "TotalImpact": {"shape": "TotalImpactFilter", "documentation": "<p>Filters anomaly results by the total impact field on the anomaly object. For example, you can filter anomalies <code>GREATER_THAN 200.00</code> to retrieve anomalies, with an estimated dollar impact greater than 200. </p>"}, "NextPageToken": {"shape": "NextPageToken", "documentation": "<p>The token to retrieve the next set of results. Amazon Web Services provides the token when the response from a previous call has more results than the maximum page size. </p>"}, "MaxResults": {"shape": "PageSize", "documentation": "<p>The number of entries a paginated response contains. </p>", "box": true}}}, "GetAnomaliesResponse": {"type": "structure", "required": ["Anomalies"], "members": {"Anomalies": {"shape": "Anomalies", "documentation": "<p>A list of cost anomalies. </p>"}, "NextPageToken": {"shape": "NextPageToken", "documentation": "<p>The token to retrieve the next set of results. Amazon Web Services provides the token when the response from a previous call has more results than the maximum page size. </p>"}}}, "GetAnomalyMonitorsRequest": {"type": "structure", "members": {"MonitorArnList": {"shape": "Values", "documentation": "<p>A list of cost anomaly monitor ARNs. </p>"}, "NextPageToken": {"shape": "NextPageToken", "documentation": "<p>The token to retrieve the next set of results. Amazon Web Services provides the token when the response from a previous call has more results than the maximum page size. </p>"}, "MaxResults": {"shape": "PageSize", "documentation": "<p>The number of entries that a paginated response contains. </p>", "box": true}}}, "GetAnomalyMonitorsResponse": {"type": "structure", "required": ["AnomalyMonitors"], "members": {"AnomalyMonitors": {"shape": "AnomalyMonitors", "documentation": "<p>A list of cost anomaly monitors that includes the detailed metadata for each monitor. </p>"}, "NextPageToken": {"shape": "NextPageToken", "documentation": "<p>The token to retrieve the next set of results. Amazon Web Services provides the token when the response from a previous call has more results than the maximum page size. </p>"}}}, "GetAnomalySubscriptionsRequest": {"type": "structure", "members": {"SubscriptionArnList": {"shape": "Values", "documentation": "<p>A list of cost anomaly subscription ARNs. </p>"}, "MonitorArn": {"shape": "GenericString", "documentation": "<p>Cost anomaly monitor ARNs. </p>"}, "NextPageToken": {"shape": "NextPageToken", "documentation": "<p>The token to retrieve the next set of results. Amazon Web Services provides the token when the response from a previous call has more results than the maximum page size. </p>"}, "MaxResults": {"shape": "PageSize", "documentation": "<p>The number of entries a paginated response contains. </p>", "box": true}}}, "GetAnomalySubscriptionsResponse": {"type": "structure", "required": ["AnomalySubscriptions"], "members": {"AnomalySubscriptions": {"shape": "AnomalySubscriptions", "documentation": "<p>A list of cost anomaly subscriptions that includes the detailed metadata for each one. </p>"}, "NextPageToken": {"shape": "NextPageToken", "documentation": "<p>The token to retrieve the next set of results. Amazon Web Services provides the token when the response from a previous call has more results than the maximum page size. </p>"}}}, "GetApproximateUsageRecordsRequest": {"type": "structure", "required": ["Granularity", "ApproximationDimension"], "members": {"Granularity": {"shape": "Granularity", "documentation": "<p>How granular you want the data to be. You can enable data at hourly or daily granularity.</p>"}, "Services": {"shape": "UsageServices", "documentation": "<p>The service metadata for the service or services you want to query. If not specified, all elements are returned.</p>"}, "ApproximationDimension": {"shape": "ApproximationDimension", "documentation": "<p>The service to evaluate for the usage records. You can choose resource-level data at daily granularity, or hourly granularity with or without resource-level data.</p>"}}}, "GetApproximateUsageRecordsResponse": {"type": "structure", "members": {"Services": {"shape": "ApproximateUsageRecordsPerService", "documentation": "<p>The service metadata for the service or services in the response.</p>"}, "TotalRecords": {"shape": "NonNegativeLong", "documentation": "<p>The total number of usage records for all services in the services list.</p>"}, "LookbackPeriod": {"shape": "DateInterval", "documentation": "<p>The lookback period that's used for the estimation.</p>"}}}, "GetCommitmentPurchaseAnalysisRequest": {"type": "structure", "required": ["AnalysisId"], "members": {"AnalysisId": {"shape": "AnalysisId", "documentation": "<p>The analysis ID that's associated with the commitment purchase analysis.</p>"}}}, "GetCommitmentPurchaseAnalysisResponse": {"type": "structure", "required": ["EstimatedCompletionTime", "AnalysisStartedTime", "AnalysisId", "AnalysisStatus", "CommitmentPurchaseAnalysisConfiguration"], "members": {"EstimatedCompletionTime": {"shape": "ZonedDateTime", "documentation": "<p>The estimated time for when the analysis will complete.</p>"}, "AnalysisCompletionTime": {"shape": "ZonedDateTime", "documentation": "<p>The completion time of the analysis.</p>"}, "AnalysisStartedTime": {"shape": "ZonedDateTime", "documentation": "<p>The start time of the analysis.</p>"}, "AnalysisId": {"shape": "AnalysisId", "documentation": "<p>The analysis ID that's associated with the commitment purchase analysis.</p>"}, "AnalysisStatus": {"shape": "AnalysisStatus", "documentation": "<p>The status of the analysis.</p>"}, "ErrorCode": {"shape": "ErrorCode", "documentation": "<p>The error code used for the analysis.</p>"}, "AnalysisDetails": {"shape": "AnalysisDetails", "documentation": "<p>Details about the analysis.</p>"}, "CommitmentPurchaseAnalysisConfiguration": {"shape": "CommitmentPurchaseAnalysisConfiguration", "documentation": "<p>The configuration for the commitment purchase analysis.</p>"}}}, "GetCostAndUsageComparisonsRequest": {"type": "structure", "required": ["BaselineTimePeriod", "ComparisonTimePeriod", "MetricForComparison"], "members": {"BillingViewArn": {"shape": "BillingViewArn", "documentation": "<p>The Amazon Resource Name (ARN) that uniquely identifies a specific billing view. The ARN is used to specify which particular billing view you want to interact with or retrieve information from when making API calls related to Amazon Web Services Billing and Cost Management features. The BillingViewArn can be retrieved by calling the ListBillingViews API.</p>"}, "BaselineTimePeriod": {"shape": "DateInterval", "documentation": "<p>The reference time period for comparison. This time period serves as the baseline against which other cost and usage data will be compared. The interval must start and end on the first day of a month, with a duration of exactly one month.</p>"}, "ComparisonTimePeriod": {"shape": "DateInterval", "documentation": "<p>The comparison time period for analysis. This time period's cost and usage data will be compared against the baseline time period. The interval must start and end on the first day of a month, with a duration of exactly one month.</p>"}, "MetricForComparison": {"shape": "MetricName", "documentation": "<p>The cost and usage metric to compare. Valid values are <code>AmortizedCost</code>, <code>BlendedCost</code>, <code>NetAmortizedCost</code>, <code>NetUnblendedCost</code>, <code>NormalizedUsageAmount</code>, <code>UnblendedCost</code>, and <code>UsageQuantity</code>.</p>"}, "Filter": {"shape": "Expression"}, "GroupBy": {"shape": "GroupDefinitions", "documentation": "<p>You can group results using the attributes <code>DIMENSION</code>, <code>TAG</code>, and <code>COST_CATEGORY</code>. </p>"}, "MaxResults": {"shape": "CostAndUsageComparisonsMaxResults", "documentation": "<p>The maximum number of results that are returned for the request.</p>", "box": true}, "NextPageToken": {"shape": "NextPageToken", "documentation": "<p>The token to retrieve the next set of paginated results.</p>"}}}, "GetCostAndUsageComparisonsResponse": {"type": "structure", "members": {"CostAndUsageComparisons": {"shape": "CostAndUsageComparisons", "documentation": "<p>An array of comparison results showing cost and usage metrics between <code>BaselineTimePeriod</code> and <code>ComparisonTimePeriod</code>.</p>"}, "TotalCostAndUsage": {"shape": "ComparisonMetrics", "documentation": "<p>A summary of the total cost and usage, comparing amounts between <code>BaselineTimePeriod</code> and <code>ComparisonTimePeriod</code> and their differences. This total represents the aggregate total across all paginated results, if the response spans multiple pages.</p>"}, "NextPageToken": {"shape": "NextPageToken", "documentation": "<p>The token to retrieve the next set of paginated results.</p>"}}}, "GetCostAndUsageRequest": {"type": "structure", "required": ["TimePeriod", "Granularity", "Metrics"], "members": {"TimePeriod": {"shape": "DateInterval", "documentation": "<p>Sets the start date and end date for retrieving Amazon Web Services costs. The start date is inclusive, but the end date is exclusive. For example, if <code>start</code> is <code>2017-01-01</code> and <code>end</code> is <code>2017-05-01</code>, then the cost and usage data is retrieved from <code>2017-01-01</code> up to and including <code>2017-04-30</code> but not including <code>2017-05-01</code>.</p>"}, "Granularity": {"shape": "Granularity", "documentation": "<p>Sets the Amazon Web Services cost granularity to <code>MONTHLY</code> or <code>DAILY</code>, or <code>HOURLY</code>. If <code>Granularity</code> isn't set, the response object doesn't include the <code>Granularity</code>, either <code>MONTHLY</code> or <code>DAILY</code>, or <code>HOURLY</code>. </p>"}, "Filter": {"shape": "Expression", "documentation": "<p>Filters Amazon Web Services costs by different dimensions. For example, you can specify <code>SERVICE</code> and <code>LINKED_ACCOUNT</code> and get the costs that are associated with that account's usage of that service. You can nest <code>Expression</code> objects to define any combination of dimension filters. For more information, see <a href=\"https://docs.aws.amazon.com/aws-cost-management/latest/APIReference/API_Expression.html\">Expression</a>. </p> <p>Valid values for <code>MatchOptions</code> for <code>Dimensions</code> are <code>EQUALS</code> and <code>CASE_SENSITIVE</code>.</p> <p>Valid values for <code>MatchOptions</code> for <code>CostCategories</code> and <code>Tags</code> are <code>EQUALS</code>, <code>ABSENT</code>, and <code>CASE_SENSITIVE</code>. Default values are <code>EQUALS</code> and <code>CASE_SENSITIVE</code>.</p>"}, "Metrics": {"shape": "MetricNames", "documentation": "<p>Which metrics are returned in the query. For more information about blended and unblended rates, see <a href=\"http://aws.amazon.com/premiumsupport/knowledge-center/blended-rates-intro/\">Why does the \"blended\" annotation appear on some line items in my bill?</a>. </p> <p>Valid values are <code>AmortizedCost</code>, <code>BlendedCost</code>, <code>NetAmortizedCost</code>, <code>NetUnblendedCost</code>, <code>NormalizedUsageAmount</code>, <code>UnblendedCost</code>, and <code>UsageQuantity</code>. </p> <note> <p>If you return the <code>UsageQuantity</code> metric, the service aggregates all usage numbers without taking into account the units. For example, if you aggregate <code>usageQuantity</code> across all of Amazon EC2, the results aren't meaningful because Amazon EC2 compute hours and data transfer are measured in different units (for example, hours and GB). To get more meaningful <code>UsageQuantity</code> metrics, filter by <code>UsageType</code> or <code>UsageTypeGroups</code>. </p> </note> <p> <code>Metrics</code> is required for <code>GetCostAndUsage</code> requests.</p>"}, "GroupBy": {"shape": "GroupDefinitions", "documentation": "<p>You can group Amazon Web Services costs using up to two different groups, either dimensions, tag keys, cost categories, or any two group by types.</p> <p>Valid values for the <code>DIMENSION</code> type are <code>AZ</code>, <code>INSTANCE_TYPE</code>, <code>LEGAL_ENTITY_NAME</code>, <code>INVOICING_ENTITY</code>, <code>LINKED_ACCOUNT</code>, <code>OPERATION</code>, <code>PLATFORM</code>, <code>PURCHASE_TYPE</code>, <code>SERVICE</code>, <code>TENANCY</code>, <code>RECORD_TYPE</code>, and <code>USAGE_TYPE</code>.</p> <p>When you group by the <code>TAG</code> type and include a valid tag key, you get all tag values, including empty strings.</p>"}, "BillingViewArn": {"shape": "BillingViewArn", "documentation": "<p>The Amazon Resource Name (ARN) that uniquely identifies a specific billing view. The ARN is used to specify which particular billing view you want to interact with or retrieve information from when making API calls related to Amazon Web Services Billing and Cost Management features. The BillingViewArn can be retrieved by calling the ListBillingViews API.</p>"}, "NextPageToken": {"shape": "NextPageToken", "documentation": "<p>The token to retrieve the next set of results. Amazon Web Services provides the token when the response from a previous call has more results than the maximum page size.</p>"}}}, "GetCostAndUsageResponse": {"type": "structure", "members": {"NextPageToken": {"shape": "NextPageToken", "documentation": "<p>The token for the next set of retrievable results. Amazon Web Services provides the token when the response from a previous call has more results than the maximum page size.</p>"}, "GroupDefinitions": {"shape": "GroupDefinitions", "documentation": "<p>The groups that are specified by the <code>Filter</code> or <code>GroupBy</code> parameters in the request.</p>"}, "ResultsByTime": {"shape": "ResultsByTime", "documentation": "<p>The time period that's covered by the results in the response.</p>"}, "DimensionValueAttributes": {"shape": "DimensionValuesWithAttributesList", "documentation": "<p>The attributes that apply to a specific dimension value. For example, if the value is a linked account, the attribute is that account name.</p>"}}}, "GetCostAndUsageWithResourcesRequest": {"type": "structure", "required": ["TimePeriod", "Granularity", "Filter"], "members": {"TimePeriod": {"shape": "DateInterval", "documentation": "<p>Sets the start and end dates for retrieving Amazon Web Services costs. The range must be within the last 14 days (the start date cannot be earlier than 14 days ago). The start date is inclusive, but the end date is exclusive. For example, if <code>start</code> is <code>2017-01-01</code> and <code>end</code> is <code>2017-05-01</code>, then the cost and usage data is retrieved from <code>2017-01-01</code> up to and including <code>2017-04-30</code> but not including <code>2017-05-01</code>.</p>"}, "Granularity": {"shape": "Granularity", "documentation": "<p>Sets the Amazon Web Services cost granularity to <code>MONTHLY</code>, <code>DAILY</code>, or <code>HOURLY</code>. If <code>Granularity</code> isn't set, the response object doesn't include the <code>Granularity</code>, <code>MONTHLY</code>, <code>DAILY</code>, or <code>HOURLY</code>. </p>"}, "Filter": {"shape": "Expression", "documentation": "<p>Filters Amazon Web Services costs by different dimensions. For example, you can specify <code>SERVICE</code> and <code>LINKED_ACCOUNT</code> and get the costs that are associated with that account's usage of that service. You can nest <code>Expression</code> objects to define any combination of dimension filters. For more information, see <a href=\"https://docs.aws.amazon.com/aws-cost-management/latest/APIReference/API_Expression.html\">Expression</a>. </p> <p>Valid values for <code>MatchOptions</code> for <code>Dimensions</code> are <code>EQUALS</code> and <code>CASE_SENSITIVE</code>.</p> <p>Valid values for <code>MatchOptions</code> for <code>CostCategories</code> and <code>Tags</code> are <code>EQUALS</code>, <code>ABSENT</code>, and <code>CASE_SENSITIVE</code>. Default values are <code>EQUALS</code> and <code>CASE_SENSITIVE</code>.</p>"}, "Metrics": {"shape": "MetricNames", "documentation": "<p>Which metrics are returned in the query. For more information about blended and unblended rates, see <a href=\"http://aws.amazon.com/premiumsupport/knowledge-center/blended-rates-intro/\">Why does the \"blended\" annotation appear on some line items in my bill?</a>. </p> <p>Valid values are <code>AmortizedCost</code>, <code>BlendedCost</code>, <code>NetAmortizedCost</code>, <code>NetUnblendedCost</code>, <code>NormalizedUsageAmount</code>, <code>UnblendedCost</code>, and <code>UsageQuantity</code>. </p> <note> <p>If you return the <code>UsageQuantity</code> metric, the service aggregates all usage numbers without taking the units into account. For example, if you aggregate <code>usageQuantity</code> across all of Amazon EC2, the results aren't meaningful because Amazon EC2 compute hours and data transfer are measured in different units (for example, hour or GB). To get more meaningful <code>UsageQuantity</code> metrics, filter by <code>UsageType</code> or <code>UsageTypeGroups</code>. </p> </note> <p> <code>Metrics</code> is required for <code>GetCostAndUsageWithResources</code> requests.</p>"}, "GroupBy": {"shape": "GroupDefinitions", "documentation": "<p>You can group Amazon Web Services costs using up to two different groups: <code>DIMENSION</code>, <code>TAG</code>, <code>COST_CATEGORY</code>.</p>"}, "BillingViewArn": {"shape": "BillingViewArn", "documentation": "<p>The Amazon Resource Name (ARN) that uniquely identifies a specific billing view. The ARN is used to specify which particular billing view you want to interact with or retrieve information from when making API calls related to Amazon Web Services Billing and Cost Management features. The BillingViewArn can be retrieved by calling the ListBillingViews API.</p>"}, "NextPageToken": {"shape": "NextPageToken", "documentation": "<p>The token to retrieve the next set of results. Amazon Web Services provides the token when the response from a previous call has more results than the maximum page size.</p>"}}}, "GetCostAndUsageWithResourcesResponse": {"type": "structure", "members": {"NextPageToken": {"shape": "NextPageToken", "documentation": "<p>The token for the next set of retrievable results. Amazon Web Services provides the token when the response from a previous call has more results than the maximum page size.</p>"}, "GroupDefinitions": {"shape": "GroupDefinitions", "documentation": "<p>The groups that are specified by the <code>Filter</code> or <code>GroupBy</code> parameters in the request.</p>"}, "ResultsByTime": {"shape": "ResultsByTime", "documentation": "<p>The time period that's covered by the results in the response.</p>"}, "DimensionValueAttributes": {"shape": "DimensionValuesWithAttributesList", "documentation": "<p>The attributes that apply to a specific dimension value. For example, if the value is a linked account, the attribute is that account name.</p>"}}}, "GetCostCategoriesRequest": {"type": "structure", "required": ["TimePeriod"], "members": {"SearchString": {"shape": "SearchString", "documentation": "<p>The value that you want to search the filter values for.</p> <p>If you don't specify a <code>CostCategoryName</code>, <code>SearchString</code> is used to filter Cost Category names that match the <code>SearchString</code> pattern. If you specify a <code>CostCategoryName</code>, <code>SearchString</code> is used to filter Cost Category values that match the <code>SearchString</code> pattern.</p>"}, "TimePeriod": {"shape": "DateInterval"}, "CostCategoryName": {"shape": "CostCategoryName"}, "Filter": {"shape": "Expression"}, "SortBy": {"shape": "SortDefinitions", "documentation": "<p>The value that you sort the data by.</p> <p>The key represents the cost and usage metrics. The following values are supported:</p> <ul> <li> <p> <code>BlendedCost</code> </p> </li> <li> <p> <code>UnblendedCost</code> </p> </li> <li> <p> <code>AmortizedCost</code> </p> </li> <li> <p> <code>NetAmortizedCost</code> </p> </li> <li> <p> <code>NetUnblendedCost</code> </p> </li> <li> <p> <code>UsageQuantity</code> </p> </li> <li> <p> <code>NormalizedUsageAmount</code> </p> </li> </ul> <p>The supported key values for the <code>SortOrder</code> value are <code>ASCENDING</code> and <code>DESCENDING</code>.</p> <p>When you use the <code>SortBy</code> value, the <code>NextPageToken</code> and <code>SearchString</code> key values aren't supported.</p>"}, "BillingViewArn": {"shape": "BillingViewArn", "documentation": "<p>The Amazon Resource Name (ARN) that uniquely identifies a specific billing view. The ARN is used to specify which particular billing view you want to interact with or retrieve information from when making API calls related to Amazon Web Services Billing and Cost Management features. The BillingViewArn can be retrieved by calling the ListBillingViews API.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>This field is only used when the <code>SortBy</code> value is provided in the request.</p> <p>The maximum number of objects that are returned for this request. If <code>MaxResults</code> isn't specified with the <code>SortBy</code> value, the request returns 1000 results as the default value for this parameter.</p> <p>For <code>GetCostCategories</code>, MaxResults has an upper quota of 1000.</p>"}, "NextPageToken": {"shape": "NextPageToken", "documentation": "<p>If the number of objects that are still available for retrieval exceeds the quota, Amazon Web Services returns a NextPageToken value in the response. To retrieve the next batch of objects, provide the NextPageToken from the previous call in your next request.</p>"}}}, "GetCostCategoriesResponse": {"type": "structure", "required": ["ReturnSize", "TotalSize"], "members": {"NextPageToken": {"shape": "NextPageToken", "documentation": "<p>If the number of objects that are still available for retrieval exceeds the quota, Amazon Web Services returns a NextPageToken value in the response. To retrieve the next batch of objects, provide the marker from the prior call in your next request.</p>"}, "CostCategoryNames": {"shape": "CostCategoryNamesList", "documentation": "<p>The names of the Cost Categories.</p>"}, "CostCategoryValues": {"shape": "CostCategoryValuesList", "documentation": "<p>The Cost Category values.</p> <p>If the <code>CostCategoryName</code> key isn't specified in the request, the <code>CostCategoryValues</code> fields aren't returned. </p>"}, "ReturnSize": {"shape": "PageSize", "documentation": "<p>The number of objects that are returned.</p>"}, "TotalSize": {"shape": "PageSize", "documentation": "<p>The total number of objects.</p>"}}}, "GetCostComparisonDriversRequest": {"type": "structure", "required": ["BaselineTimePeriod", "ComparisonTimePeriod", "MetricForComparison"], "members": {"BillingViewArn": {"shape": "BillingViewArn", "documentation": "<p>The Amazon Resource Name (ARN) that uniquely identifies a specific billing view. The ARN is used to specify which particular billing view you want to interact with or retrieve information from when making API calls related to Amazon Web Services Billing and Cost Management features. The BillingViewArn can be retrieved by calling the ListBillingViews API.</p>"}, "BaselineTimePeriod": {"shape": "DateInterval", "documentation": "<p>The reference time period for comparison. This time period serves as the baseline against which other cost and usage data will be compared. The interval must start and end on the first day of a month, with a duration of exactly one month.</p>"}, "ComparisonTimePeriod": {"shape": "DateInterval", "documentation": "<p>The comparison time period for analysis. This time period's cost and usage data will be compared against the baseline time period. The interval must start and end on the first day of a month, with a duration of exactly one month.</p>"}, "MetricForComparison": {"shape": "MetricName", "documentation": "<p>The cost and usage metric to compare. Valid values are <code>AmortizedCost</code>, <code>BlendedCost</code>, <code>NetAmortizedCost</code>, <code>NetUnblendedCost</code>, <code>NormalizedUsageAmount</code>, <code>UnblendedCost</code>, and <code>UsageQuantity</code>.</p>"}, "Filter": {"shape": "Expression"}, "GroupBy": {"shape": "GroupDefinitions", "documentation": "<p>You can group results using the attributes <code>DIMENSION</code>, <code>TAG</code>, and <code>COST_CATEGORY</code>. Note that <code>SERVICE</code> and <code>USAGE_TYPE</code> dimensions are automatically included in the cost comparison drivers analysis.</p>"}, "MaxResults": {"shape": "CostComparisonDriversMaxResults", "documentation": "<p>The maximum number of results that are returned for the request.</p>", "box": true}, "NextPageToken": {"shape": "NextPageToken", "documentation": "<p>The token to retrieve the next set of paginated results.</p>"}}}, "GetCostComparisonDriversResponse": {"type": "structure", "members": {"CostComparisonDrivers": {"shape": "CostComparisonDrivers", "documentation": "<p>An array of comparison results showing factors that drive significant cost differences between <code>BaselineTimePeriod</code> and <code>ComparisonTimePeriod</code>.</p>"}, "NextPageToken": {"shape": "NextPageToken", "documentation": "<p>The token to retrieve the next set of paginated results.</p>"}}}, "GetCostForecastRequest": {"type": "structure", "required": ["TimePeriod", "Metric", "Granularity"], "members": {"TimePeriod": {"shape": "DateInterval", "documentation": "<p>The period of time that you want the forecast to cover. The start date must be equal to or no later than the current date to avoid a validation error.</p>"}, "Metric": {"shape": "Metric", "documentation": "<p>Which metric Cost Explorer uses to create your forecast. For more information about blended and unblended rates, see <a href=\"http://aws.amazon.com/premiumsupport/knowledge-center/blended-rates-intro/\">Why does the \"blended\" annotation appear on some line items in my bill?</a>. </p> <p>Valid values for a <code>GetCostForecast</code> call are the following:</p> <ul> <li> <p>AMORTIZED_COST</p> </li> <li> <p>BLENDED_COST</p> </li> <li> <p>NET_AMORTIZED_COST</p> </li> <li> <p>NET_UNBLENDED_COST</p> </li> <li> <p>UNBLENDED_COST</p> </li> </ul>"}, "Granularity": {"shape": "Granularity", "documentation": "<p>How granular you want the forecast to be. You can get 3 months of <code>DAILY</code> forecasts or 12 months of <code>MONTHLY</code> forecasts.</p> <p>The <code>GetCostForecast</code> operation supports only <code>DAILY</code> and <code>MONTHLY</code> granularities.</p>"}, "Filter": {"shape": "Expression", "documentation": "<p>The filters that you want to use to filter your forecast. The <code>GetCostForecast</code> API supports filtering by the following dimensions:</p> <ul> <li> <p> <code>AZ</code> </p> </li> <li> <p> <code>INSTANCE_TYPE</code> </p> </li> <li> <p> <code>LINKED_ACCOUNT</code> </p> </li> <li> <p> <code>OPERATION</code> </p> </li> <li> <p> <code>PURCHASE_TYPE</code> </p> </li> <li> <p> <code>REGION</code> </p> </li> <li> <p> <code>SERVICE</code> </p> </li> <li> <p> <code>USAGE_TYPE</code> </p> </li> <li> <p> <code>USAGE_TYPE_GROUP</code> </p> </li> <li> <p> <code>RECORD_TYPE</code> </p> </li> <li> <p> <code>OPERATING_SYSTEM</code> </p> </li> <li> <p> <code>TENANCY</code> </p> </li> <li> <p> <code>SCOPE</code> </p> </li> <li> <p> <code>PLATFORM</code> </p> </li> <li> <p> <code>SUBSCRIPTION_ID</code> </p> </li> <li> <p> <code>LEGAL_ENTITY_NAME</code> </p> </li> <li> <p> <code>DEPLOYMENT_OPTION</code> </p> </li> <li> <p> <code>DATABASE_ENGINE</code> </p> </li> <li> <p> <code>INSTANCE_TYPE_FAMILY</code> </p> </li> <li> <p> <code>BILLING_ENTITY</code> </p> </li> <li> <p> <code>RESERVATION_ID</code> </p> </li> <li> <p> <code>SAVINGS_PLAN_ARN</code> </p> </li> </ul>"}, "BillingViewArn": {"shape": "BillingViewArn", "documentation": "<p>The Amazon Resource Name (ARN) that uniquely identifies a specific billing view. The ARN is used to specify which particular billing view you want to interact with or retrieve information from when making API calls related to Amazon Web Services Billing and Cost Management features. The BillingViewArn can be retrieved by calling the ListBillingViews API.</p>"}, "PredictionIntervalLevel": {"shape": "PredictionIntervalLevel", "documentation": "<p>Cost Explorer always returns the mean forecast as a single point. You can request a prediction interval around the mean by specifying a confidence level. The higher the confidence level, the more confident Cost Explorer is about the actual value falling in the prediction interval. Higher confidence levels result in wider prediction intervals.</p>"}}}, "GetCostForecastResponse": {"type": "structure", "members": {"Total": {"shape": "MetricValue", "documentation": "<p>How much you are forecasted to spend over the forecast period, in <code>USD</code>.</p>"}, "ForecastResultsByTime": {"shape": "ForecastResultsByTime", "documentation": "<p>The forecasts for your query, in order. For <code>DAILY</code> forecasts, this is a list of days. For <code>MONTHLY</code> forecasts, this is a list of months.</p>"}}}, "GetDimensionValuesRequest": {"type": "structure", "required": ["TimePeriod", "Dimension"], "members": {"SearchString": {"shape": "SearchString", "documentation": "<p>The value that you want to search the filter values for.</p>"}, "TimePeriod": {"shape": "DateInterval", "documentation": "<p>The start date and end date for retrieving the dimension values. The start date is inclusive, but the end date is exclusive. For example, if <code>start</code> is <code>2017-01-01</code> and <code>end</code> is <code>2017-05-01</code>, then the cost and usage data is retrieved from <code>2017-01-01</code> up to and including <code>2017-04-30</code> but not including <code>2017-05-01</code>.</p>"}, "Dimension": {"shape": "Dimension", "documentation": "<p>The name of the dimension. Each <code>Dimension</code> is available for a different <code>Context</code>. For more information, see <code>Context</code>. <code>LINK_ACCOUNT_NAME</code> and <code>SERVICE_CODE</code> can only be used in <a href=\"https://docs.aws.amazon.com/aws-cost-management/latest/APIReference/AAPI_CostCategoryRule.html\">CostCategoryRule</a>. </p>"}, "Context": {"shape": "Context", "documentation": "<p>The context for the call to <code>GetDimensionValues</code>. This can be <code>RESERVATIONS</code> or <code>COST_AND_USAGE</code>. The default value is <code>COST_AND_USAGE</code>. If the context is set to <code>RESERVATIONS</code>, the resulting dimension values can be used in the <code>GetReservationUtilization</code> operation. If the context is set to <code>COST_AND_USAGE</code>, the resulting dimension values can be used in the <code>GetCostAndUsage</code> operation.</p> <p>If you set the context to <code>COST_AND_USAGE</code>, you can use the following dimensions for searching:</p> <ul> <li> <p>AZ - The Availability Zone. An example is <code>us-east-1a</code>.</p> </li> <li> <p>BILLING_ENTITY - The Amazon Web Services seller that your account is with. Possible values are the following:</p> <p>- Amazon Web Services(Amazon Web Services): The entity that sells Amazon Web Services services.</p> <p>- AISPL (Amazon Internet Services Pvt. Ltd.): The local Indian entity that's an acting reseller for Amazon Web Services services in India.</p> <p>- Amazon Web Services Marketplace: The entity that supports the sale of solutions that are built on Amazon Web Services by third-party software providers.</p> </li> <li> <p>CACHE_ENGINE - The Amazon ElastiCache operating system. Examples are Windows or Linux.</p> </li> <li> <p>DEPLOYMENT_OPTION - The scope of Amazon Relational Database Service deployments. Valid values are <code>SingleAZ</code> and <code>MultiAZ</code>.</p> </li> <li> <p>DATABASE_ENGINE - The Amazon Relational Database Service database. Examples are Aurora or MySQL.</p> </li> <li> <p>INSTANCE_TYPE - The type of Amazon EC2 instance. An example is <code>m4.xlarge</code>.</p> </li> <li> <p>INSTANCE_TYPE_FAMILY - A family of instance types optimized to fit different use cases. Examples are <code>Compute Optimized</code> (for example, <code>C4</code>, <code>C5</code>, <code>C6g</code>, and <code>C7g</code>), <code>Memory Optimization</code> (for example, <code>R4</code>, <code>R5n</code>, <code>R5b</code>, and <code>R6g</code>).</p> </li> <li> <p>INVOICING_ENTITY - The name of the entity that issues the Amazon Web Services invoice.</p> </li> <li> <p>LEGAL_ENTITY_NAME - The name of the organization that sells you Amazon Web Services services, such as Amazon Web Services.</p> </li> <li> <p>LINKED_ACCOUNT - The description in the attribute map that includes the full name of the member account. The value field contains the Amazon Web Services ID of the member account.</p> </li> <li> <p>OPERATING_SYSTEM - The operating system. Examples are Windows or Linux.</p> </li> <li> <p>OPERATION - The action performed. Examples include <code>RunInstance</code> and <code>CreateBucket</code>.</p> </li> <li> <p>PLATFORM - The Amazon EC2 operating system. Examples are Windows or Linux.</p> </li> <li> <p>PURCHASE_TYPE - The reservation type of the purchase that this usage is related to. Examples include On-Demand Instances and Standard Reserved Instances.</p> </li> <li> <p>RESERVATION_ID - The unique identifier for an Amazon Web Services Reservation Instance.</p> </li> <li> <p>SAVINGS_PLAN_ARN - The unique identifier for your Savings Plans.</p> </li> <li> <p>SAVINGS_PLANS_TYPE - Type of Savings Plans (EC2 Instance or Compute).</p> </li> <li> <p>SERVICE - The Amazon Web Services service such as Amazon DynamoDB.</p> </li> <li> <p>TENANCY - The tenancy of a resource. Examples are shared or dedicated.</p> </li> <li> <p>USAGE_TYPE - The type of usage. An example is DataTransfer-In-Bytes. The response for the <code>GetDimensionValues</code> operation includes a unit attribute. Examples include GB and Hrs.</p> </li> <li> <p>USAGE_TYPE_GROUP - The grouping of common usage types. An example is Amazon EC2: CloudWatch – Alarms. The response for this operation includes a unit attribute.</p> </li> <li> <p>REGION - The Amazon Web Services Region.</p> </li> <li> <p>RECORD_TYPE - The different types of charges such as Reserved Instance (RI) fees, usage costs, tax refunds, and credits.</p> </li> <li> <p>RESOURCE_ID - The unique identifier of the resource. ResourceId is an opt-in feature only available for last 14 days for EC2-Compute Service.</p> </li> </ul> <p>If you set the context to <code>RESERVATIONS</code>, you can use the following dimensions for searching:</p> <ul> <li> <p>AZ - The Availability Zone. An example is <code>us-east-1a</code>.</p> </li> <li> <p>CACHE_ENGINE - The Amazon ElastiCache operating system. Examples are Windows or Linux.</p> </li> <li> <p>DEPLOYMENT_OPTION - The scope of Amazon Relational Database Service deployments. Valid values are <code>SingleAZ</code> and <code>MultiAZ</code>.</p> </li> <li> <p>INSTANCE_TYPE - The type of Amazon EC2 instance. An example is <code>m4.xlarge</code>.</p> </li> <li> <p>LINKED_ACCOUNT - The description in the attribute map that includes the full name of the member account. The value field contains the Amazon Web Services ID of the member account.</p> </li> <li> <p>PLATFORM - The Amazon EC2 operating system. Examples are Windows or Linux.</p> </li> <li> <p>REGION - The Amazon Web Services Region.</p> </li> <li> <p>SCOPE (Utilization only) - The scope of a Reserved Instance (RI). Values are regional or a single Availability Zone.</p> </li> <li> <p>TAG (Coverage only) - The tags that are associated with a Reserved Instance (RI).</p> </li> <li> <p>TENANCY - The tenancy of a resource. Examples are shared or dedicated.</p> </li> </ul> <p>If you set the context to <code>SAVINGS_PLANS</code>, you can use the following dimensions for searching:</p> <ul> <li> <p>SAVINGS_PLANS_TYPE - Type of Savings Plans (EC2 Instance or Compute)</p> </li> <li> <p>PAYMENT_OPTION - The payment option for the given Savings Plans (for example, All Upfront)</p> </li> <li> <p>REGION - The Amazon Web Services Region.</p> </li> <li> <p>INSTANCE_TYPE_FAMILY - The family of instances (For example, <code>m5</code>)</p> </li> <li> <p>LINKED_ACCOUNT - The description in the attribute map that includes the full name of the member account. The value field contains the Amazon Web Services ID of the member account.</p> </li> <li> <p>SAVINGS_PLAN_ARN - The unique identifier for your Savings Plans.</p> </li> </ul>"}, "Filter": {"shape": "Expression"}, "SortBy": {"shape": "SortDefinitions", "documentation": "<p>The value that you want to sort the data by.</p> <p>The key represents cost and usage metrics. The following values are supported:</p> <ul> <li> <p> <code>BlendedCost</code> </p> </li> <li> <p> <code>UnblendedCost</code> </p> </li> <li> <p> <code>AmortizedCost</code> </p> </li> <li> <p> <code>NetAmortizedCost</code> </p> </li> <li> <p> <code>NetUnblendedCost</code> </p> </li> <li> <p> <code>UsageQuantity</code> </p> </li> <li> <p> <code>NormalizedUsageAmount</code> </p> </li> </ul> <p>The supported values for the <code>SortOrder</code> key are <code>ASCENDING</code> or <code>DESCENDING</code>.</p> <p>When you specify a <code>SortBy</code> paramater, the context must be <code>COST_AND_USAGE</code>. Further, when using <code>SortBy</code>, <code>NextPageToken</code> and <code>SearchString</code> aren't supported.</p>"}, "BillingViewArn": {"shape": "BillingViewArn", "documentation": "<p>The Amazon Resource Name (ARN) that uniquely identifies a specific billing view. The ARN is used to specify which particular billing view you want to interact with or retrieve information from when making API calls related to Amazon Web Services Billing and Cost Management features. The BillingViewArn can be retrieved by calling the ListBillingViews API.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>This field is only used when SortBy is provided in the request. The maximum number of objects that are returned for this request. If MaxResults isn't specified with SortBy, the request returns 1000 results as the default value for this parameter.</p> <p>For <code>GetDimensionValues</code>, MaxResults has an upper limit of 1000.</p>"}, "NextPageToken": {"shape": "NextPageToken", "documentation": "<p>The token to retrieve the next set of results. Amazon Web Services provides the token when the response from a previous call has more results than the maximum page size.</p>"}}}, "GetDimensionValuesResponse": {"type": "structure", "required": ["DimensionValues", "ReturnSize", "TotalSize"], "members": {"DimensionValues": {"shape": "DimensionValuesWithAttributesList", "documentation": "<p>The filters that you used to filter your request. Some dimensions are available only for a specific context.</p> <p>If you set the context to <code>COST_AND_USAGE</code>, you can use the following dimensions for searching:</p> <ul> <li> <p>AZ - The Availability Zone. An example is <code>us-east-1a</code>.</p> </li> <li> <p>DATABASE_ENGINE - The Amazon Relational Database Service database. Examples are Aurora or MySQL.</p> </li> <li> <p>INSTANCE_TYPE - The type of Amazon EC2 instance. An example is <code>m4.xlarge</code>.</p> </li> <li> <p>LEGAL_ENTITY_NAME - The name of the organization that sells you Amazon Web Services services, such as Amazon Web Services.</p> </li> <li> <p>LINKED_ACCOUNT - The description in the attribute map that includes the full name of the member account. The value field contains the Amazon Web Services ID of the member account.</p> </li> <li> <p>OPERATING_SYSTEM - The operating system. Examples are Windows or Linux.</p> </li> <li> <p>OPERATION - The action performed. Examples include <code>RunInstance</code> and <code>CreateBucket</code>.</p> </li> <li> <p>PLATFORM - The Amazon EC2 operating system. Examples are Windows or Linux.</p> </li> <li> <p>PURCHASE_TYPE - The reservation type of the purchase to which this usage is related. Examples include On-Demand Instances and Standard Reserved Instances.</p> </li> <li> <p>SERVICE - The Amazon Web Services service such as Amazon DynamoDB.</p> </li> <li> <p>USAGE_TYPE - The type of usage. An example is DataTransfer-In-Bytes. The response for the <code>GetDimensionValues</code> operation includes a unit attribute. Examples include GB and Hrs.</p> </li> <li> <p>USAGE_TYPE_GROUP - The grouping of common usage types. An example is Amazon EC2: CloudWatch – Alarms. The response for this operation includes a unit attribute.</p> </li> <li> <p>RECORD_TYPE - The different types of charges such as RI fees, usage costs, tax refunds, and credits.</p> </li> <li> <p>RESOURCE_ID - The unique identifier of the resource. ResourceId is an opt-in feature only available for last 14 days for EC2-Compute Service. You can opt-in by enabling <code>Hourly</code> and <code>Resource Level Data</code> in Cost Management Console preferences.</p> </li> </ul> <p>If you set the context to <code>RESERVATIONS</code>, you can use the following dimensions for searching:</p> <ul> <li> <p>AZ - The Availability Zone. An example is <code>us-east-1a</code>.</p> </li> <li> <p>CACHE_ENGINE - The Amazon ElastiCache operating system. Examples are Windows or Linux.</p> </li> <li> <p>DEPLOYMENT_OPTION - The scope of Amazon Relational Database Service deployments. Valid values are <code>SingleAZ</code> and <code>MultiAZ</code>.</p> </li> <li> <p>INSTANCE_TYPE - The type of Amazon EC2 instance. An example is <code>m4.xlarge</code>.</p> </li> <li> <p>LINKED_ACCOUNT - The description in the attribute map that includes the full name of the member account. The value field contains the Amazon Web Services ID of the member account.</p> </li> <li> <p>PLATFORM - The Amazon EC2 operating system. Examples are Windows or Linux.</p> </li> <li> <p>REGION - The Amazon Web Services Region.</p> </li> <li> <p>SCOPE (Utilization only) - The scope of a Reserved Instance (RI). Values are regional or a single Availability Zone.</p> </li> <li> <p>TAG (Coverage only) - The tags that are associated with a Reserved Instance (RI).</p> </li> <li> <p>TENANCY - The tenancy of a resource. Examples are shared or dedicated.</p> </li> </ul> <p>If you set the context to <code>SAVINGS_PLANS</code>, you can use the following dimensions for searching:</p> <ul> <li> <p>SAVINGS_PLANS_TYPE - Type of Savings Plans (EC2 Instance or Compute)</p> </li> <li> <p>PAYMENT_OPTION - Payment option for the given Savings Plans (for example, All Upfront)</p> </li> <li> <p>REGION - The Amazon Web Services Region.</p> </li> <li> <p>INSTANCE_TYPE_FAMILY - The family of instances (For example, <code>m5</code>)</p> </li> <li> <p>LINKED_ACCOUNT - The description in the attribute map that includes the full name of the member account. The value field contains the Amazon Web Services ID of the member account.</p> </li> <li> <p>SAVINGS_PLAN_ARN - The unique identifier for your Savings Plan</p> </li> </ul>"}, "ReturnSize": {"shape": "PageSize", "documentation": "<p>The number of results that Amazon Web Services returned at one time.</p>"}, "TotalSize": {"shape": "PageSize", "documentation": "<p>The total number of search results.</p>"}, "NextPageToken": {"shape": "NextPageToken", "documentation": "<p>The token for the next set of retrievable results. Amazon Web Services provides the token when the response from a previous call has more results than the maximum page size.</p>"}}}, "GetReservationCoverageRequest": {"type": "structure", "required": ["TimePeriod"], "members": {"TimePeriod": {"shape": "DateInterval", "documentation": "<p>The start and end dates of the period that you want to retrieve data about reservation coverage for. You can retrieve data for a maximum of 13 months: the last 12 months and the current month. The start date is inclusive, but the end date is exclusive. For example, if <code>start</code> is <code>2017-01-01</code> and <code>end</code> is <code>2017-05-01</code>, then the cost and usage data is retrieved from <code>2017-01-01</code> up to and including <code>2017-04-30</code> but not including <code>2017-05-01</code>. </p>"}, "GroupBy": {"shape": "GroupDefinitions", "documentation": "<p>You can group the data by the following attributes:</p> <ul> <li> <p>AZ</p> </li> <li> <p>CACHE_ENGINE</p> </li> <li> <p>DATABASE_ENGINE</p> </li> <li> <p>DEPLOYMENT_OPTION</p> </li> <li> <p>INSTANCE_TYPE</p> </li> <li> <p>INVOICING_ENTITY</p> </li> <li> <p>LINKED_ACCOUNT</p> </li> <li> <p>OPERATING_SYSTEM</p> </li> <li> <p>PLATFORM</p> </li> <li> <p>REGION</p> </li> <li> <p>TENANCY</p> </li> </ul>"}, "Granularity": {"shape": "Granularity", "documentation": "<p>The granularity of the Amazon Web Services cost data for the reservation. Valid values are <code>MONTHLY</code> and <code>DAILY</code>.</p> <p>If <code>GroupBy</code> is set, <code>Granularity</code> can't be set. If <code>Granularity</code> isn't set, the response object doesn't include <code>Granularity</code>, either <code>MONTHLY</code> or <code>DAILY</code>.</p> <p>The <code>GetReservationCoverage</code> operation supports only <code>DAILY</code> and <code>MONTHLY</code> granularities.</p>"}, "Filter": {"shape": "Expression", "documentation": "<p>Filters utilization data by dimensions. You can filter by the following dimensions:</p> <ul> <li> <p>AZ</p> </li> <li> <p>CACHE_ENGINE</p> </li> <li> <p>DATABASE_ENGINE</p> </li> <li> <p>DEPLOYMENT_OPTION</p> </li> <li> <p>INSTANCE_TYPE</p> </li> <li> <p>LINKED_ACCOUNT</p> </li> <li> <p>OPERATING_SYSTEM</p> </li> <li> <p>PLATFORM</p> </li> <li> <p>REGION</p> </li> <li> <p>SERVICE</p> </li> <li> <p>TAG</p> </li> <li> <p>TENANCY</p> </li> </ul> <p> <code>GetReservationCoverage</code> uses the same <a href=\"https://docs.aws.amazon.com/aws-cost-management/latest/APIReference/API_Expression.html\">Expression</a> object as the other operations, but only <code>AND</code> is supported among each dimension. You can nest only one level deep. If there are multiple values for a dimension, they are OR'd together.</p> <p>If you don't provide a <code>SERVICE</code> filter, Cost Explorer defaults to EC2.</p> <p>Cost category is also supported.</p>"}, "Metrics": {"shape": "MetricNames", "documentation": "<p>The measurement that you want your reservation coverage reported in.</p> <p>Valid values are <code>Hour</code>, <code>Unit</code>, and <code>Cost</code>. You can use multiple values in a request.</p>"}, "NextPageToken": {"shape": "NextPageToken", "documentation": "<p>The token to retrieve the next set of results. Amazon Web Services provides the token when the response from a previous call has more results than the maximum page size.</p>"}, "SortBy": {"shape": "SortDefinition", "documentation": "<p>The value by which you want to sort the data.</p> <p>The following values are supported for <code>Key</code>:</p> <ul> <li> <p> <code>OnDemandCost</code> </p> </li> <li> <p> <code>CoverageHoursPercentage</code> </p> </li> <li> <p> <code>OnDemandHours</code> </p> </li> <li> <p> <code>ReservedHours</code> </p> </li> <li> <p> <code>TotalRunningHours</code> </p> </li> <li> <p> <code>CoverageNormalizedUnitsPercentage</code> </p> </li> <li> <p> <code>OnDemandNormalizedUnits</code> </p> </li> <li> <p> <code>ReservedNormalizedUnits</code> </p> </li> <li> <p> <code>TotalRunningNormalizedUnits</code> </p> </li> <li> <p> <code>Time</code> </p> </li> </ul> <p>Supported values for <code>SortOrder</code> are <code>ASCENDING</code> or <code>DESCENDING</code>.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of objects that you returned for this request. If more objects are available, in the response, Amazon Web Services provides a NextPageToken value that you can use in a subsequent call to get the next batch of objects.</p>", "box": true}}, "documentation": "<p>You can use the following request parameters to query for how much of your instance usage a reservation covered.</p>"}, "GetReservationCoverageResponse": {"type": "structure", "required": ["CoveragesByTime"], "members": {"CoveragesByTime": {"shape": "CoveragesByTime", "documentation": "<p>The amount of time that your reservations covered.</p>"}, "Total": {"shape": "Coverage", "documentation": "<p>The total amount of instance usage that a reservation covered.</p>"}, "NextPageToken": {"shape": "NextPageToken", "documentation": "<p>The token for the next set of retrievable results. Amazon Web Services provides the token when the response from a previous call has more results than the maximum page size.</p>"}}}, "GetReservationPurchaseRecommendationRequest": {"type": "structure", "required": ["Service"], "members": {"AccountId": {"shape": "GenericString", "documentation": "<p>The account ID that's associated with the recommendation. </p>"}, "Service": {"shape": "GenericString", "documentation": "<p>The specific service that you want recommendations for.</p>"}, "Filter": {"shape": "Expression"}, "AccountScope": {"shape": "AccountScope", "documentation": "<p>The account scope that you want your recommendations for. Amazon Web Services calculates recommendations including the management account and member accounts if the value is set to <code>PAYER</code>. If the value is <code>LINKED</code>, recommendations are calculated for individual member accounts only.</p>"}, "LookbackPeriodInDays": {"shape": "LookbackPeriodInDays", "documentation": "<p>The number of previous days that you want Amazon Web Services to consider when it calculates your recommendations.</p>"}, "TermInYears": {"shape": "TermInYears", "documentation": "<p>The reservation term that you want recommendations for.</p>"}, "PaymentOption": {"shape": "PaymentOption", "documentation": "<p>The reservation purchase option that you want recommendations for.</p>"}, "ServiceSpecification": {"shape": "ServiceSpecification", "documentation": "<p>The hardware specifications for the service instances that you want recommendations for, such as standard or convertible Amazon EC2 instances.</p>"}, "PageSize": {"shape": "NonNegativeInteger", "documentation": "<p>The number of recommendations that you want returned in a single response object.</p>"}, "NextPageToken": {"shape": "NextPageToken", "documentation": "<p>The pagination token that indicates the next set of results that you want to retrieve.</p>"}}}, "GetReservationPurchaseRecommendationResponse": {"type": "structure", "members": {"Metadata": {"shape": "ReservationPurchaseRecommendationMetadata", "documentation": "<p>Information about this specific recommendation call, such as the time stamp for when Cost Explorer generated this recommendation.</p>"}, "Recommendations": {"shape": "ReservationPurchaseRecommendations", "documentation": "<p>Recommendations for reservations to purchase.</p>"}, "NextPageToken": {"shape": "NextPageToken", "documentation": "<p>The pagination token for the next set of retrievable results.</p>"}}}, "GetReservationUtilizationRequest": {"type": "structure", "required": ["TimePeriod"], "members": {"TimePeriod": {"shape": "DateInterval", "documentation": "<p>Sets the start and end dates for retrieving Reserved Instance (RI) utilization. The start date is inclusive, but the end date is exclusive. For example, if <code>start</code> is <code>2017-01-01</code> and <code>end</code> is <code>2017-05-01</code>, then the cost and usage data is retrieved from <code>2017-01-01</code> up to and including <code>2017-04-30</code> but not including <code>2017-05-01</code>. </p>"}, "GroupBy": {"shape": "GroupDefinitions", "documentation": "<p>Groups only by <code>SUBSCRIPTION_ID</code>. Metadata is included.</p>"}, "Granularity": {"shape": "Granularity", "documentation": "<p>If <code>GroupBy</code> is set, <code>Granularity</code> can't be set. If <code>Granularity</code> isn't set, the response object doesn't include <code>Granularity</code>, either <code>MONTHLY</code> or <code>DAILY</code>. If both <code>GroupBy</code> and <code>Granularity</code> aren't set, <code>GetReservationUtilization</code> defaults to <code>DAILY</code>.</p> <p>The <code>GetReservationUtilization</code> operation supports only <code>DAILY</code> and <code>MONTHLY</code> granularities.</p>"}, "Filter": {"shape": "Expression", "documentation": "<p>Filters utilization data by dimensions. You can filter by the following dimensions:</p> <ul> <li> <p>AZ</p> </li> <li> <p>CACHE_ENGINE</p> </li> <li> <p>DEPLOYMENT_OPTION</p> </li> <li> <p>INSTANCE_TYPE</p> </li> <li> <p>LINKED_ACCOUNT</p> </li> <li> <p>OPERATING_SYSTEM</p> </li> <li> <p>PLATFORM</p> </li> <li> <p>REGION</p> </li> <li> <p>SERVICE</p> <note> <p>If not specified, the <code>SERVICE</code> filter defaults to Amazon Elastic Compute Cloud - Compute. Supported values for <code>SERVICE</code> are Amazon Elastic Compute Cloud - Compute, Amazon Relational Database Service, Amazon ElastiCache, Amazon Redshift, and Amazon Elasticsearch Service. The value for the <code>SERVICE</code> filter should not exceed \"1\".</p> </note> </li> <li> <p>SCOPE</p> </li> <li> <p>TENANCY</p> </li> </ul> <p> <code>GetReservationUtilization</code> uses the same <a href=\"https://docs.aws.amazon.com/aws-cost-management/latest/APIReference/API_Expression.html\">Expression</a> object as the other operations, but only <code>AND</code> is supported among each dimension, and nesting is supported up to only one level deep. If there are multiple values for a dimension, they are OR'd together.</p>"}, "SortBy": {"shape": "SortDefinition", "documentation": "<p>The value that you want to sort the data by.</p> <p>The following values are supported for <code>Key</code>:</p> <ul> <li> <p> <code>UtilizationPercentage</code> </p> </li> <li> <p> <code>UtilizationPercentageInUnits</code> </p> </li> <li> <p> <code>PurchasedHours</code> </p> </li> <li> <p> <code>PurchasedUnits</code> </p> </li> <li> <p> <code>TotalActualHours</code> </p> </li> <li> <p> <code>TotalActualUnits</code> </p> </li> <li> <p> <code>UnusedHours</code> </p> </li> <li> <p> <code>UnusedUnits</code> </p> </li> <li> <p> <code>OnDemandCostOfRIHoursUsed</code> </p> </li> <li> <p> <code>NetRISavings</code> </p> </li> <li> <p> <code>TotalPotentialRISavings</code> </p> </li> <li> <p> <code>AmortizedUpfrontFee</code> </p> </li> <li> <p> <code>AmortizedRecurringFee</code> </p> </li> <li> <p> <code>TotalAmortizedFee</code> </p> </li> <li> <p> <code>RICostForUnusedHours</code> </p> </li> <li> <p> <code>RealizedSavings</code> </p> </li> <li> <p> <code>UnrealizedSavings</code> </p> </li> </ul> <p>The supported values for <code>SortOrder</code> are <code>ASCENDING</code> and <code>DESCENDING</code>.</p>"}, "NextPageToken": {"shape": "NextPageToken", "documentation": "<p>The token to retrieve the next set of results. Amazon Web Services provides the token when the response from a previous call has more results than the maximum page size.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of objects that you returned for this request. If more objects are available, in the response, Amazon Web Services provides a NextPageToken value that you can use in a subsequent call to get the next batch of objects.</p>", "box": true}}}, "GetReservationUtilizationResponse": {"type": "structure", "required": ["UtilizationsByTime"], "members": {"UtilizationsByTime": {"shape": "UtilizationsByTime", "documentation": "<p>The amount of time that you used your Reserved Instances (RIs).</p>"}, "Total": {"shape": "ReservationAggregates", "documentation": "<p>The total amount of time that you used your Reserved Instances (RIs).</p>"}, "NextPageToken": {"shape": "NextPageToken", "documentation": "<p>The token for the next set of retrievable results. Amazon Web Services provides the token when the response from a previous call has more results than the maximum page size.</p>"}}}, "GetRightsizingRecommendationRequest": {"type": "structure", "required": ["Service"], "members": {"Filter": {"shape": "Expression"}, "Configuration": {"shape": "RightsizingRecommendationConfiguration", "documentation": "<p>You can use Configuration to customize recommendations across two attributes. You can choose to view recommendations for instances within the same instance families or across different instance families. You can also choose to view your estimated savings that are associated with recommendations with consideration of existing Savings Plans or RI benefits, or neither. </p>"}, "Service": {"shape": "GenericString", "documentation": "<p>The specific service that you want recommendations for. The only valid value for <code>GetRightsizingRecommendation</code> is \"<code>AmazonEC2</code>\".</p>"}, "PageSize": {"shape": "NonNegativeInteger", "documentation": "<p>The number of recommendations that you want returned in a single response object.</p>"}, "NextPageToken": {"shape": "NextPageToken", "documentation": "<p>The pagination token that indicates the next set of results that you want to retrieve.</p>"}}}, "GetRightsizingRecommendationResponse": {"type": "structure", "members": {"Metadata": {"shape": "RightsizingRecommendationMetadata", "documentation": "<p>Information regarding this specific recommendation set.</p>"}, "Summary": {"shape": "RightsizingRecommendationSummary", "documentation": "<p>Summary of this recommendation set.</p>"}, "RightsizingRecommendations": {"shape": "RightsizingRecommendationList", "documentation": "<p>Recommendations to rightsize resources.</p>"}, "NextPageToken": {"shape": "NextPageToken", "documentation": "<p>The token to retrieve the next set of results.</p>"}, "Configuration": {"shape": "RightsizingRecommendationConfiguration", "documentation": "<p>You can use Configuration to customize recommendations across two attributes. You can choose to view recommendations for instances within the same instance families or across different instance families. You can also choose to view your estimated savings that are associated with recommendations with consideration of existing Savings Plans or RI benefits, or neither. </p>"}}}, "GetSavingsPlanPurchaseRecommendationDetailsRequest": {"type": "structure", "required": ["RecommendationDetailId"], "members": {"RecommendationDetailId": {"shape": "RecommendationDetailId", "documentation": "<p>The ID that is associated with the Savings Plan recommendation.</p>"}}}, "GetSavingsPlanPurchaseRecommendationDetailsResponse": {"type": "structure", "members": {"RecommendationDetailId": {"shape": "RecommendationDetailId", "documentation": "<p>The ID that is associated with the Savings Plan recommendation.</p>"}, "RecommendationDetailData": {"shape": "RecommendationDetailData", "documentation": "<p>Contains detailed information about a specific Savings Plan recommendation.</p>"}}}, "GetSavingsPlansCoverageRequest": {"type": "structure", "required": ["TimePeriod"], "members": {"TimePeriod": {"shape": "DateInterval", "documentation": "<p>The time period that you want the usage and costs for. The <code>Start</code> date must be within 13 months. The <code>End</code> date must be after the <code>Start</code> date, and before the current date. Future dates can't be used as an <code>End</code> date.</p>"}, "GroupBy": {"shape": "GroupDefinitions", "documentation": "<p>You can group the data using the attributes <code>INSTANCE_FAMILY</code>, <code>REGION</code>, or <code>SERVICE</code>.</p>"}, "Granularity": {"shape": "Granularity", "documentation": "<p>The granularity of the Amazon Web Services cost data for your Savings Plans. <code>Granularity</code> can't be set if <code>GroupBy</code> is set.</p> <p>The <code>GetSavingsPlansCoverage</code> operation supports only <code>DAILY</code> and <code>MONTHLY</code> granularities.</p>"}, "Filter": {"shape": "Expression", "documentation": "<p>Filters Savings Plans coverage data by dimensions. You can filter data for Savings Plans usage with the following dimensions:</p> <ul> <li> <p> <code>LINKED_ACCOUNT</code> </p> </li> <li> <p> <code>REGION</code> </p> </li> <li> <p> <code>SERVICE</code> </p> </li> <li> <p> <code>INSTANCE_FAMILY</code> </p> </li> </ul> <p> <code>GetSavingsPlansCoverage</code> uses the same <a href=\"https://docs.aws.amazon.com/aws-cost-management/latest/APIReference/API_Expression.html\">Expression</a> object as the other operations, but only <code>AND</code> is supported among each dimension. If there are multiple values for a dimension, they are OR'd together.</p> <p>Cost category is also supported.</p>"}, "Metrics": {"shape": "MetricNames", "documentation": "<p>The measurement that you want your Savings Plans coverage reported in. The only valid value is <code>SpendCoveredBySavingsPlans</code>.</p>"}, "NextToken": {"shape": "NextPageToken", "documentation": "<p>The token to retrieve the next set of results. Amazon Web Services provides the token when the response from a previous call has more results than the maximum page size.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The number of items to be returned in a response. The default is <code>20</code>, with a minimum value of <code>1</code>.</p>", "box": true}, "SortBy": {"shape": "SortDefinition", "documentation": "<p>The value that you want to sort the data by.</p> <p>The following values are supported for <code>Key</code>:</p> <ul> <li> <p> <code>SpendCoveredBySavingsPlan</code> </p> </li> <li> <p> <code>OnDemandCost</code> </p> </li> <li> <p> <code>CoveragePercentage</code> </p> </li> <li> <p> <code>TotalCost</code> </p> </li> <li> <p> <code>InstanceFamily</code> </p> </li> <li> <p> <code>Region</code> </p> </li> <li> <p> <code>Service</code> </p> </li> </ul> <p>The supported values for <code>SortOrder</code> are <code>ASCENDING</code> and <code>DESCENDING</code>.</p>"}}}, "GetSavingsPlansCoverageResponse": {"type": "structure", "required": ["SavingsPlansCoverages"], "members": {"SavingsPlansCoverages": {"shape": "SavingsPlansCoverages", "documentation": "<p>The amount of spend that your Savings Plans covered.</p>"}, "NextToken": {"shape": "NextPageToken", "documentation": "<p>The token to retrieve the next set of results. Amazon Web Services provides the token when the response from a previous call has more results than the maximum page size.</p>"}}}, "GetSavingsPlansPurchaseRecommendationRequest": {"type": "structure", "required": ["SavingsPlansType", "TermInYears", "PaymentOption", "LookbackPeriodInDays"], "members": {"SavingsPlansType": {"shape": "SupportedSavingsPlansType", "documentation": "<p>The Savings Plans recommendation type that's requested.</p>"}, "TermInYears": {"shape": "TermInYears", "documentation": "<p>The savings plan recommendation term that's used to generate these recommendations.</p>"}, "PaymentOption": {"shape": "PaymentOption", "documentation": "<p>The payment option that's used to generate these recommendations.</p>"}, "AccountScope": {"shape": "AccountScope", "documentation": "<p>The account scope that you want your recommendations for. Amazon Web Services calculates recommendations including the management account and member accounts if the value is set to <code>PAYER</code>. If the value is <code>LINKED</code>, recommendations are calculated for individual member accounts only.</p>"}, "NextPageToken": {"shape": "NextPageToken", "documentation": "<p>The token to retrieve the next set of results. Amazon Web Services provides the token when the response from a previous call has more results than the maximum page size.</p>"}, "PageSize": {"shape": "NonNegativeInteger", "documentation": "<p>The number of recommendations that you want returned in a single response object.</p>"}, "LookbackPeriodInDays": {"shape": "LookbackPeriodInDays", "documentation": "<p>The lookback period that's used to generate the recommendation.</p>"}, "Filter": {"shape": "Expression", "documentation": "<p>You can filter your recommendations by Account ID with the <code>LINKED_ACCOUNT</code> dimension. To filter your recommendations by Account ID, specify <code>Key</code> as <code>LINKED_ACCOUNT</code> and <code>Value</code> as the comma-separated Acount ID(s) that you want to see Savings Plans purchase recommendations for.</p> <p>For GetSavingsPlansPurchaseRecommendation, the <code>Filter</code> doesn't include <code>CostCategories</code> or <code>Tags</code>. It only includes <code>Dimensions</code>. With <code>Dimensions</code>, <code>Key</code> must be <code>LINKED_ACCOUNT</code> and <code>Value</code> can be a single Account ID or multiple comma-separated Account IDs that you want to see Savings Plans Purchase Recommendations for. <code>AND</code> and <code>OR</code> operators are not supported.</p>"}}}, "GetSavingsPlansPurchaseRecommendationResponse": {"type": "structure", "members": {"Metadata": {"shape": "SavingsPlansPurchaseRecommendationMetadata", "documentation": "<p>Information that regards this specific recommendation set.</p>"}, "SavingsPlansPurchaseRecommendation": {"shape": "SavingsPlansPurchaseRecommendation", "documentation": "<p>Contains your request parameters, Savings Plan Recommendations Summary, and Details.</p>"}, "NextPageToken": {"shape": "NextPageToken", "documentation": "<p>The token for the next set of retrievable results. Amazon Web Services provides the token when the response from a previous call has more results than the maximum page size.</p>"}}}, "GetSavingsPlansUtilizationDetailsRequest": {"type": "structure", "required": ["TimePeriod"], "members": {"TimePeriod": {"shape": "DateInterval", "documentation": "<p>The time period that you want the usage and costs for. The <code>Start</code> date must be within 13 months. The <code>End</code> date must be after the <code>Start</code> date, and before the current date. Future dates can't be used as an <code>End</code> date.</p>"}, "Filter": {"shape": "Expression", "documentation": "<p>Filters Savings Plans utilization coverage data for active Savings Plans dimensions. You can filter data with the following dimensions:</p> <ul> <li> <p> <code>LINKED_ACCOUNT</code> </p> </li> <li> <p> <code>SAVINGS_PLAN_ARN</code> </p> </li> <li> <p> <code>REGION</code> </p> </li> <li> <p> <code>PAYMENT_OPTION</code> </p> </li> <li> <p> <code>INSTANCE_TYPE_FAMILY</code> </p> </li> </ul> <p> <code>GetSavingsPlansUtilizationDetails</code> uses the same <a href=\"https://docs.aws.amazon.com/aws-cost-management/latest/APIReference/API_Expression.html\">Expression</a> object as the other operations, but only <code>AND</code> is supported among each dimension.</p>"}, "DataType": {"shape": "SavingsPlansDataTypes", "documentation": "<p>The data type.</p>"}, "NextToken": {"shape": "NextPageToken", "documentation": "<p>The token to retrieve the next set of results. Amazon Web Services provides the token when the response from a previous call has more results than the maximum page size.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The number of items to be returned in a response. The default is <code>20</code>, with a minimum value of <code>1</code>.</p>", "box": true}, "SortBy": {"shape": "SortDefinition", "documentation": "<p>The value that you want to sort the data by.</p> <p>The following values are supported for <code>Key</code>:</p> <ul> <li> <p> <code>UtilizationPercentage</code> </p> </li> <li> <p> <code>TotalCommitment</code> </p> </li> <li> <p> <code>UsedCommitment</code> </p> </li> <li> <p> <code>UnusedCommitment</code> </p> </li> <li> <p> <code>NetSavings</code> </p> </li> <li> <p> <code>AmortizedRecurringCommitment</code> </p> </li> <li> <p> <code>AmortizedUpfrontCommitment</code> </p> </li> </ul> <p>The supported values for <code>SortOrder</code> are <code>ASCENDING</code> and <code>DESCENDING</code>.</p>"}}}, "GetSavingsPlansUtilizationDetailsResponse": {"type": "structure", "required": ["SavingsPlansUtilizationDetails", "TimePeriod"], "members": {"SavingsPlansUtilizationDetails": {"shape": "SavingsPlansUtilizationDetails", "documentation": "<p>Retrieves a single daily or monthly Savings Plans utilization rate and details for your account.</p>"}, "Total": {"shape": "SavingsPlansUtilizationAggregates", "documentation": "<p>The total Savings Plans utilization, regardless of time period.</p>"}, "TimePeriod": {"shape": "DateInterval"}, "NextToken": {"shape": "NextPageToken", "documentation": "<p>The token to retrieve the next set of results. Amazon Web Services provides the token when the response from a previous call has more results than the maximum page size.</p>"}}}, "GetSavingsPlansUtilizationRequest": {"type": "structure", "required": ["TimePeriod"], "members": {"TimePeriod": {"shape": "DateInterval", "documentation": "<p>The time period that you want the usage and costs for. The <code>Start</code> date must be within 13 months. The <code>End</code> date must be after the <code>Start</code> date, and before the current date. Future dates can't be used as an <code>End</code> date.</p>"}, "Granularity": {"shape": "Granularity", "documentation": "<p>The granularity of the Amazon Web Services utillization data for your Savings Plans.</p> <p>The <code>GetSavingsPlansUtilization</code> operation supports only <code>DAILY</code> and <code>MONTHLY</code> granularities.</p>"}, "Filter": {"shape": "Expression", "documentation": "<p>Filters Savings Plans utilization coverage data for active Savings Plans dimensions. You can filter data with the following dimensions:</p> <ul> <li> <p> <code>LINKED_ACCOUNT</code> </p> </li> <li> <p> <code>SAVINGS_PLAN_ARN</code> </p> </li> <li> <p> <code>SAVINGS_PLANS_TYPE</code> </p> </li> <li> <p> <code>REGION</code> </p> </li> <li> <p> <code>PAYMENT_OPTION</code> </p> </li> <li> <p> <code>INSTANCE_TYPE_FAMILY</code> </p> </li> </ul> <p> <code>GetSavingsPlansUtilization</code> uses the same <a href=\"https://docs.aws.amazon.com/aws-cost-management/latest/APIReference/API_Expression.html\">Expression</a> object as the other operations, but only <code>AND</code> is supported among each dimension.</p>"}, "SortBy": {"shape": "SortDefinition", "documentation": "<p>The value that you want to sort the data by.</p> <p>The following values are supported for <code>Key</code>:</p> <ul> <li> <p> <code>UtilizationPercentage</code> </p> </li> <li> <p> <code>TotalCommitment</code> </p> </li> <li> <p> <code>UsedCommitment</code> </p> </li> <li> <p> <code>UnusedCommitment</code> </p> </li> <li> <p> <code>NetSavings</code> </p> </li> </ul> <p>The supported values for <code>SortOrder</code> are <code>ASCENDING</code> and <code>DESCENDING</code>.</p>"}}}, "GetSavingsPlansUtilizationResponse": {"type": "structure", "required": ["Total"], "members": {"SavingsPlansUtilizationsByTime": {"shape": "SavingsPlansUtilizationsByTime", "documentation": "<p>The amount of cost/commitment that you used your Savings Plans. You can use it to specify date ranges.</p>"}, "Total": {"shape": "SavingsPlansUtilizationAggregates", "documentation": "<p>The total amount of cost/commitment that you used your Savings Plans, regardless of date ranges.</p>"}}}, "GetTagsRequest": {"type": "structure", "required": ["TimePeriod"], "members": {"SearchString": {"shape": "SearchString", "documentation": "<p>The value that you want to search for.</p>"}, "TimePeriod": {"shape": "DateInterval", "documentation": "<p>The start and end dates for retrieving the dimension values. The start date is inclusive, but the end date is exclusive. For example, if <code>start</code> is <code>2017-01-01</code> and <code>end</code> is <code>2017-05-01</code>, then the cost and usage data is retrieved from <code>2017-01-01</code> up to and including <code>2017-04-30</code> but not including <code>2017-05-01</code>.</p>"}, "TagKey": {"shape": "TagKey", "documentation": "<p>The key of the tag that you want to return values for.</p>"}, "Filter": {"shape": "Expression"}, "SortBy": {"shape": "SortDefinitions", "documentation": "<p>The value that you want to sort the data by.</p> <p>The key represents cost and usage metrics. The following values are supported:</p> <ul> <li> <p> <code>BlendedCost</code> </p> </li> <li> <p> <code>UnblendedCost</code> </p> </li> <li> <p> <code>AmortizedCost</code> </p> </li> <li> <p> <code>NetAmortizedCost</code> </p> </li> <li> <p> <code>NetUnblendedCost</code> </p> </li> <li> <p> <code>UsageQuantity</code> </p> </li> <li> <p> <code>NormalizedUsageAmount</code> </p> </li> </ul> <p>The supported values for <code>SortOrder</code> are <code>ASCENDING</code> and <code>DESCENDING</code>.</p> <p>When you use <code>SortBy</code>, <code>NextPageToken</code> and <code>SearchString</code> aren't supported.</p>"}, "BillingViewArn": {"shape": "BillingViewArn", "documentation": "<p>The Amazon Resource Name (ARN) that uniquely identifies a specific billing view. The ARN is used to specify which particular billing view you want to interact with or retrieve information from when making API calls related to Amazon Web Services Billing and Cost Management features. The BillingViewArn can be retrieved by calling the ListBillingViews API.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>This field is only used when SortBy is provided in the request. The maximum number of objects that are returned for this request. If MaxResults isn't specified with SortBy, the request returns 1000 results as the default value for this parameter.</p> <p>For <code>GetTags</code>, MaxResults has an upper quota of 1000.</p>"}, "NextPageToken": {"shape": "NextPageToken", "documentation": "<p>The token to retrieve the next set of results. Amazon Web Services provides the token when the response from a previous call has more results than the maximum page size.</p>"}}}, "GetTagsResponse": {"type": "structure", "required": ["Tags", "ReturnSize", "TotalSize"], "members": {"NextPageToken": {"shape": "NextPageToken", "documentation": "<p>The token for the next set of retrievable results. Amazon Web Services provides the token when the response from a previous call has more results than the maximum page size.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The tags that match your request.</p>"}, "ReturnSize": {"shape": "PageSize", "documentation": "<p>The number of query results that Amazon Web Services returns at a time.</p>"}, "TotalSize": {"shape": "PageSize", "documentation": "<p>The total number of query results.</p>"}}}, "GetUsageForecastRequest": {"type": "structure", "required": ["TimePeriod", "Metric", "Granularity"], "members": {"TimePeriod": {"shape": "DateInterval", "documentation": "<p>The start and end dates of the period that you want to retrieve usage forecast for. The start date is included in the period, but the end date isn't included in the period. For example, if <code>start</code> is <code>2017-01-01</code> and <code>end</code> is <code>2017-05-01</code>, then the cost and usage data is retrieved from <code>2017-01-01</code> up to and including <code>2017-04-30</code> but not including <code>2017-05-01</code>. The start date must be equal to or later than the current date to avoid a validation error.</p>"}, "Metric": {"shape": "Metric", "documentation": "<p>Which metric Cost Explorer uses to create your forecast.</p> <p>Valid values for a <code>GetUsageForecast</code> call are the following:</p> <ul> <li> <p>USAGE_QUANTITY</p> </li> <li> <p>NORMALIZED_USAGE_AMOUNT</p> </li> </ul>"}, "Granularity": {"shape": "Granularity", "documentation": "<p>How granular you want the forecast to be. You can get 3 months of <code>DAILY</code> forecasts or 12 months of <code>MONTHLY</code> forecasts.</p> <p>The <code>GetUsageForecast</code> operation supports only <code>DAILY</code> and <code>MONTHLY</code> granularities.</p>"}, "Filter": {"shape": "Expression", "documentation": "<p>The filters that you want to use to filter your forecast. The <code>GetUsageForecast</code> API supports filtering by the following dimensions:</p> <ul> <li> <p> <code>AZ</code> </p> </li> <li> <p> <code>INSTANCE_TYPE</code> </p> </li> <li> <p> <code>LINKED_ACCOUNT</code> </p> </li> <li> <p> <code>LINKED_ACCOUNT_NAME</code> </p> </li> <li> <p> <code>OPERATION</code> </p> </li> <li> <p> <code>PURCHASE_TYPE</code> </p> </li> <li> <p> <code>REGION</code> </p> </li> <li> <p> <code>SERVICE</code> </p> </li> <li> <p> <code>USAGE_TYPE</code> </p> </li> <li> <p> <code>USAGE_TYPE_GROUP</code> </p> </li> <li> <p> <code>RECORD_TYPE</code> </p> </li> <li> <p> <code>OPERATING_SYSTEM</code> </p> </li> <li> <p> <code>TENANCY</code> </p> </li> <li> <p> <code>SCOPE</code> </p> </li> <li> <p> <code>PLATFORM</code> </p> </li> <li> <p> <code>SUBSCRIPTION_ID</code> </p> </li> <li> <p> <code>LEGAL_ENTITY_NAME</code> </p> </li> <li> <p> <code>DEPLOYMENT_OPTION</code> </p> </li> <li> <p> <code>DATABASE_ENGINE</code> </p> </li> <li> <p> <code>INSTANCE_TYPE_FAMILY</code> </p> </li> <li> <p> <code>BILLING_ENTITY</code> </p> </li> <li> <p> <code>RESERVATION_ID</code> </p> </li> <li> <p> <code>SAVINGS_PLAN_ARN</code> </p> </li> </ul>"}, "BillingViewArn": {"shape": "BillingViewArn", "documentation": "<p>The Amazon Resource Name (ARN) that uniquely identifies a specific billing view. The ARN is used to specify which particular billing view you want to interact with or retrieve information from when making API calls related to Amazon Web Services Billing and Cost Management features. The BillingViewArn can be retrieved by calling the ListBillingViews API.</p>"}, "PredictionIntervalLevel": {"shape": "PredictionIntervalLevel", "documentation": "<p>Amazon Web Services Cost Explorer always returns the mean forecast as a single point. You can request a prediction interval around the mean by specifying a confidence level. The higher the confidence level, the more confident Cost Explorer is about the actual value falling in the prediction interval. Higher confidence levels result in wider prediction intervals.</p>"}}}, "GetUsageForecastResponse": {"type": "structure", "members": {"Total": {"shape": "MetricValue", "documentation": "<p>How much you're forecasted to use over the forecast period.</p>"}, "ForecastResultsByTime": {"shape": "ForecastResultsByTime", "documentation": "<p>The forecasts for your query, in order. For <code>DAILY</code> forecasts, this is a list of days. For <code>MONTHLY</code> forecasts, this is a list of months.</p>"}}}, "Granularity": {"type": "string", "enum": ["DAILY", "MONTHLY", "HOURLY"]}, "Group": {"type": "structure", "members": {"Keys": {"shape": "Keys", "documentation": "<p>The keys that are included in this group.</p>"}, "Metrics": {"shape": "Metrics", "documentation": "<p>The metrics that are included in this group.</p>"}}, "documentation": "<p>One level of grouped data in the results.</p>"}, "GroupDefinition": {"type": "structure", "members": {"Type": {"shape": "GroupDefinitionType", "documentation": "<p>The string that represents the type of group.</p>"}, "Key": {"shape": "GroupDefinitionKey", "documentation": "<p>The string that represents a key for a specified group.</p>"}}, "documentation": "<p>Represents a group when you specify a group by criteria or in the response to a query with a specific grouping.</p>"}, "GroupDefinitionKey": {"type": "string", "max": 1024, "min": 0, "pattern": "[\\S\\s]*"}, "GroupDefinitionType": {"type": "string", "enum": ["DIMENSION", "TAG", "COST_CATEGORY"]}, "GroupDefinitions": {"type": "list", "member": {"shape": "GroupDefinition"}}, "Groups": {"type": "list", "member": {"shape": "Group"}}, "Impact": {"type": "structure", "required": ["MaxImpact"], "members": {"MaxImpact": {"shape": "GenericDouble", "documentation": "<p>The maximum dollar value that's observed for an anomaly.</p>"}, "TotalImpact": {"shape": "GenericDouble", "documentation": "<p>The cumulative dollar difference between the total actual spend and total expected spend. It is calculated as <code>TotalActualSpend - TotalExpectedSpend</code>.</p>"}, "TotalActualSpend": {"shape": "NullableNonNegativeDouble", "documentation": "<p>The cumulative dollar amount that was actually spent during the anomaly.</p>"}, "TotalExpectedSpend": {"shape": "NullableNonNegativeDouble", "documentation": "<p>The cumulative dollar amount that was expected to be spent during the anomaly. It is calculated using advanced machine learning models to determine the typical spending pattern based on historical data for a customer.</p>"}, "TotalImpactPercentage": {"shape": "NullableNonNegativeDouble", "documentation": "<p>The cumulative percentage difference between the total actual spend and total expected spend. It is calculated as <code>(TotalImpact / TotalExpectedSpend) * 100</code>. When <code>TotalExpectedSpend</code> is zero, this field is omitted. Expected spend can be zero in situations such as when you start to use a service for the first time.</p>"}}, "documentation": "<p>The dollar value of the anomaly. </p>"}, "InstanceDetails": {"type": "structure", "members": {"EC2InstanceDetails": {"shape": "EC2InstanceDetails", "documentation": "<p>The Amazon EC2 reservations that Amazon Web Services recommends that you purchase.</p>"}, "RDSInstanceDetails": {"shape": "RDSInstanceDetails", "documentation": "<p>The Amazon RDS reservations that Amazon Web Services recommends that you purchase.</p>"}, "RedshiftInstanceDetails": {"shape": "RedshiftInstanceDetails", "documentation": "<p>The Amazon Redshift reservations that Amazon Web Services recommends that you purchase.</p>"}, "ElastiCacheInstanceDetails": {"shape": "ElastiCacheInstanceDetails", "documentation": "<p>The ElastiCache reservations that Amazon Web Services recommends that you purchase.</p>"}, "ESInstanceDetails": {"shape": "ESInstanceDetails", "documentation": "<p>The Amazon OpenSearch Service reservations that Amazon Web Services recommends that you purchase.</p>"}, "MemoryDBInstanceDetails": {"shape": "MemoryDBInstanceDetails", "documentation": "<p>The MemoryDB reservations that Amazon Web Services recommends that you purchase.</p>"}}, "documentation": "<p>Details about the reservations that Amazon Web Services recommends that you purchase.</p>"}, "InvalidNextTokenException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The pagination token is invalid. Try again without a pagination token.</p>", "exception": true}, "Key": {"type": "string"}, "Keys": {"type": "list", "member": {"shape": "Key"}}, "LimitExceededException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>You made too many calls in a short period of time. Try again later.</p>", "exception": true}, "ListCommitmentPurchaseAnalysesRequest": {"type": "structure", "members": {"AnalysisStatus": {"shape": "AnalysisStatus", "documentation": "<p>The status of the analysis.</p>"}, "NextPageToken": {"shape": "NextPageToken", "documentation": "<p>The token to retrieve the next set of results.</p>"}, "PageSize": {"shape": "NonNegativeInteger", "documentation": "<p>The number of analyses that you want returned in a single response object.</p>"}, "AnalysisIds": {"shape": "AnalysisIds", "documentation": "<p>The analysis IDs associated with the commitment purchase analyses.</p>"}}}, "ListCommitmentPurchaseAnalysesResponse": {"type": "structure", "members": {"AnalysisSummaryList": {"shape": "AnalysisSummaryList", "documentation": "<p>The list of analyses.</p>"}, "NextPageToken": {"shape": "NextPageToken", "documentation": "<p>The token to retrieve the next set of results.</p>"}}}, "ListCostAllocationTagBackfillHistoryRequest": {"type": "structure", "members": {"NextToken": {"shape": "NextPageToken", "documentation": "<p> The token to retrieve the next set of results. Amazon Web Services provides the token when the response from a previous call has more results than the maximum page size. </p>"}, "MaxResults": {"shape": "CostAllocationTagsMaxResults", "documentation": "<p> The maximum number of objects that are returned for this request. </p>", "box": true}}}, "ListCostAllocationTagBackfillHistoryResponse": {"type": "structure", "members": {"BackfillRequests": {"shape": "CostAllocationTagBackfillRequestList", "documentation": "<p> The list of historical cost allocation tag backfill requests. </p>"}, "NextToken": {"shape": "NextPageToken", "documentation": "<p> The token to retrieve the next set of results. Amazon Web Services provides the token when the response from a previous call has more results than the maximum page size. </p>"}}}, "ListCostAllocationTagsRequest": {"type": "structure", "members": {"Status": {"shape": "CostAllocationTagStatus", "documentation": "<p>The status of cost allocation tag keys that are returned for this request. </p>"}, "TagKeys": {"shape": "CostAllocationTagKeyList", "documentation": "<p>The list of cost allocation tag keys that are returned for this request. </p>"}, "Type": {"shape": "CostAllocationTagType", "documentation": "<p>The type of <code>CostAllocationTag</code> object that are returned for this request. The <code>AWSGenerated</code> type tags are tags that Amazon Web Services defines and applies to support Amazon Web Services resources for cost allocation purposes. The <code>UserDefined</code> type tags are tags that you define, create, and apply to resources. </p>"}, "NextToken": {"shape": "NextPageToken", "documentation": "<p>The token to retrieve the next set of results. Amazon Web Services provides the token when the response from a previous call has more results than the maximum page size. </p>"}, "MaxResults": {"shape": "CostAllocationTagsMaxResults", "documentation": "<p>The maximum number of objects that are returned for this request. By default, the request returns 100 results. </p>", "box": true}}}, "ListCostAllocationTagsResponse": {"type": "structure", "members": {"CostAllocationTags": {"shape": "CostAllocationTagList", "documentation": "<p>A list of cost allocation tags that includes the detailed metadata for each one. </p>"}, "NextToken": {"shape": "NextPageToken", "documentation": "<p>The token to retrieve the next set of results. Amazon Web Services provides the token when the response from a previous call has more results than the maximum page size. </p>"}}}, "ListCostCategoryDefinitionsRequest": {"type": "structure", "members": {"EffectiveOn": {"shape": "ZonedDateTime", "documentation": "<p>The date when the Cost Category was effective. </p>"}, "NextToken": {"shape": "NextPageToken", "documentation": "<p>The token to retrieve the next set of results. Amazon Web Services provides the token when the response from a previous call has more results than the maximum page size. </p>"}, "MaxResults": {"shape": "CostCategoryMaxResults", "documentation": "<p>The number of entries a paginated response contains. </p>", "box": true}}}, "ListCostCategoryDefinitionsResponse": {"type": "structure", "members": {"CostCategoryReferences": {"shape": "CostCategoryReferencesList", "documentation": "<p>A reference to a Cost Category that contains enough information to identify the Cost Category. </p>"}, "NextToken": {"shape": "NextPageToken", "documentation": "<p>The token to retrieve the next set of results. Amazon Web Services provides the token when the response from a previous call has more results than the maximum page size. </p>"}}}, "ListSavingsPlansPurchaseRecommendationGenerationRequest": {"type": "structure", "members": {"GenerationStatus": {"shape": "GenerationStatus", "documentation": "<p>The status of the recommendation generation.</p>"}, "RecommendationIds": {"shape": "RecommendationIdList", "documentation": "<p>The IDs for each specific recommendation.</p>"}, "PageSize": {"shape": "NonNegativeInteger", "documentation": "<p>The number of recommendations that you want returned in a single response object.</p>"}, "NextPageToken": {"shape": "NextPageToken", "documentation": "<p>The token to retrieve the next set of results.</p>"}}}, "ListSavingsPlansPurchaseRecommendationGenerationResponse": {"type": "structure", "members": {"GenerationSummaryList": {"shape": "GenerationSummaryList", "documentation": "<p>The list of historical recommendation generations.</p>"}, "NextPageToken": {"shape": "NextPageToken", "documentation": "<p>The token to retrieve the next set of results.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the resource. For a list of supported resources, see <a href=\"https://docs.aws.amazon.com/aws-cost-management/latest/APIReference/API_ResourceTag.html\">ResourceTag</a>.</p>"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"ResourceTags": {"shape": "ResourceTagList", "documentation": "<p>A list of tag key value pairs that are associated with the resource. </p>"}}}, "LookbackPeriodInDays": {"type": "string", "enum": ["SEVEN_DAYS", "THIRTY_DAYS", "SIXTY_DAYS"]}, "MatchOption": {"type": "string", "enum": ["EQUALS", "ABSENT", "STARTS_WITH", "ENDS_WITH", "CONTAINS", "CASE_SENSITIVE", "CASE_INSENSITIVE", "GREATER_THAN_OR_EQUAL"]}, "MatchOptions": {"type": "list", "member": {"shape": "MatchOption"}}, "MaxResults": {"type": "integer", "min": 1}, "MemoryDBInstanceDetails": {"type": "structure", "members": {"Family": {"shape": "GenericString", "documentation": "<p>The instance family of the recommended reservation.</p>"}, "NodeType": {"shape": "GenericString", "documentation": "<p>The node type of the recommended reservation.</p>"}, "Region": {"shape": "GenericString", "documentation": "<p>The Amazon Web Services Region of the recommended reservation.</p>"}, "CurrentGeneration": {"shape": "GenericBoolean", "documentation": "<p>Determines whether the recommendation is for a current generation instance.</p>"}, "SizeFlexEligible": {"shape": "GenericBoolean", "documentation": "<p>Determines whether the recommended reservation is size flexible.</p>"}}, "documentation": "<p>Details about the MemoryDB reservations that Amazon Web Services recommends that you purchase.</p>"}, "Metric": {"type": "string", "enum": ["BLENDED_COST", "UNBLENDED_COST", "AMORTIZED_COST", "NET_UNBLENDED_COST", "NET_AMORTIZED_COST", "USAGE_QUANTITY", "NORMALIZED_USAGE_AMOUNT"]}, "MetricAmount": {"type": "string"}, "MetricName": {"type": "string", "max": 1024, "min": 0, "pattern": "[\\S\\s]*"}, "MetricNames": {"type": "list", "member": {"shape": "MetricName"}}, "MetricUnit": {"type": "string"}, "MetricValue": {"type": "structure", "members": {"Amount": {"shape": "MetricAmount", "documentation": "<p>The actual number that represents the metric.</p>"}, "Unit": {"shape": "MetricUnit", "documentation": "<p>The unit that the metric is given in.</p>"}}, "documentation": "<p>The aggregated value for a metric.</p>"}, "Metrics": {"type": "map", "key": {"shape": "MetricName"}, "value": {"shape": "MetricValue"}}, "MetricsOverLookbackPeriod": {"type": "list", "member": {"shape": "RecommendationDetailHourlyMetrics"}}, "ModifyRecommendationDetail": {"type": "structure", "members": {"TargetInstances": {"shape": "TargetInstancesList", "documentation": "<p>Determines whether this instance type is the Amazon Web Services default recommendation.</p>"}}, "documentation": "<p>Details for the modification recommendation.</p>"}, "MonitorArnList": {"type": "list", "member": {"shape": "<PERSON><PERSON>"}}, "MonitorDimension": {"type": "string", "enum": ["SERVICE"]}, "MonitorType": {"type": "string", "enum": ["DIMENSIONAL", "CUSTOM"]}, "NetRISavings": {"type": "string"}, "NetworkResourceUtilization": {"type": "structure", "members": {"NetworkInBytesPerSecond": {"shape": "GenericString", "documentation": "<p>The network inbound throughput utilization measured in Bytes per second (Bps). </p>"}, "NetworkOutBytesPerSecond": {"shape": "GenericString", "documentation": "<p>The network outbound throughput utilization measured in Bytes per second (Bps). </p>"}, "NetworkPacketsInPerSecond": {"shape": "GenericString", "documentation": "<p>The network inbound packets that are measured in packets per second. </p>"}, "NetworkPacketsOutPerSecond": {"shape": "GenericString", "documentation": "<p>The network outbound packets that are measured in packets per second. </p>"}}, "documentation": "<p>The network field that contains a list of network metrics that are associated with the current instance. </p>"}, "NextPageToken": {"type": "string", "max": 8192, "min": 0, "pattern": "[\\S\\s]*"}, "NonNegativeInteger": {"type": "integer", "min": 0}, "NonNegativeLong": {"type": "long", "min": 0}, "NullableNonNegativeDouble": {"type": "double", "min": 0.0}, "NumericOperator": {"type": "string", "enum": ["EQUAL", "GREATER_THAN_OR_EQUAL", "LESS_THAN_OR_EQUAL", "GREATER_THAN", "LESS_THAN", "BETWEEN"]}, "OfferingClass": {"type": "string", "enum": ["STANDARD", "CONVERTIBLE"]}, "OnDemandCost": {"type": "string"}, "OnDemandCostOfRIHoursUsed": {"type": "string"}, "OnDemandHours": {"type": "string"}, "OnDemandNormalizedUnits": {"type": "string"}, "PageSize": {"type": "integer"}, "PaymentOption": {"type": "string", "enum": ["NO_UPFRONT", "PARTIAL_UPFRONT", "ALL_UPFRONT", "LIGHT_UTILIZATION", "MEDIUM_UTILIZATION", "HEAVY_UTILIZATION"]}, "PlatformDifference": {"type": "string", "enum": ["HYPERVISOR", "NETWORK_INTERFACE", "STORAGE_INTERFACE", "INSTANCE_STORE_AVAILABILITY", "VIRTUALIZATION_TYPE"]}, "PlatformDifferences": {"type": "list", "member": {"shape": "PlatformDifference"}}, "PredictionIntervalLevel": {"type": "integer", "max": 99, "min": 51}, "ProvideAnomalyFeedbackRequest": {"type": "structure", "required": ["AnomalyId", "<PERSON><PERSON><PERSON>"], "members": {"AnomalyId": {"shape": "GenericString", "documentation": "<p>A cost anomaly ID. </p>"}, "Feedback": {"shape": "AnomalyFeedbackType", "documentation": "<p>Describes whether the cost anomaly was a planned activity or you considered it an anomaly. </p>"}}}, "ProvideAnomalyFeedbackResponse": {"type": "structure", "required": ["AnomalyId"], "members": {"AnomalyId": {"shape": "GenericString", "documentation": "<p>The ID of the modified cost anomaly. </p>"}}}, "PurchasedHours": {"type": "string"}, "PurchasedUnits": {"type": "string"}, "RDSInstanceDetails": {"type": "structure", "members": {"Family": {"shape": "GenericString", "documentation": "<p>The instance family of the recommended reservation.</p>"}, "InstanceType": {"shape": "GenericString", "documentation": "<p>The type of instance that Amazon Web Services recommends.</p>"}, "Region": {"shape": "GenericString", "documentation": "<p>The Amazon Web Services Region of the recommended reservation.</p>"}, "DatabaseEngine": {"shape": "GenericString", "documentation": "<p>The database engine that the recommended reservation supports.</p>"}, "DatabaseEdition": {"shape": "GenericString", "documentation": "<p>The database edition that the recommended reservation supports.</p>"}, "DeploymentOption": {"shape": "GenericString", "documentation": "<p>Determines whether the recommendation is for a reservation in a single Availability Zone or a reservation with a backup in a second Availability Zone.</p>"}, "LicenseModel": {"shape": "GenericString", "documentation": "<p>The license model that the recommended reservation supports.</p>"}, "CurrentGeneration": {"shape": "GenericBoolean", "documentation": "<p>Determines whether the recommendation is for a current-generation instance. </p>"}, "SizeFlexEligible": {"shape": "GenericBoolean", "documentation": "<p>Determines whether the recommended reservation is size flexible.</p>"}}, "documentation": "<p>Details about the Amazon RDS reservations that Amazon Web Services recommends that you purchase.</p>"}, "RICostForUnusedHours": {"type": "string"}, "RealizedSavings": {"type": "string"}, "RecommendationDetailData": {"type": "structure", "members": {"AccountScope": {"shape": "AccountScope", "documentation": "<p>The account scope that you want your recommendations for. Amazon Web Services calculates recommendations including the management account and member accounts if the value is set to PAYER. If the value is LINKED, recommendations are calculated for individual member accounts only.</p>"}, "LookbackPeriodInDays": {"shape": "LookbackPeriodInDays", "documentation": "<p>How many days of previous usage that Amazon Web Services considers when making this recommendation.</p>"}, "SavingsPlansType": {"shape": "SupportedSavingsPlansType", "documentation": "<p>The requested Savings Plan recommendation type.</p>"}, "TermInYears": {"shape": "TermInYears", "documentation": "<p>The term of the commitment in years.</p>"}, "PaymentOption": {"shape": "PaymentOption", "documentation": "<p>The payment option for the commitment (for example, All Upfront or No Upfront).</p>"}, "AccountId": {"shape": "GenericString", "documentation": "<p>The AccountID that the recommendation is generated for.</p>"}, "CurrencyCode": {"shape": "GenericString", "documentation": "<p>The currency code that Amazon Web Services used to generate the recommendation and present potential savings.</p>"}, "InstanceFamily": {"shape": "GenericString", "documentation": "<p>The instance family of the recommended Savings Plan.</p>"}, "Region": {"shape": "GenericString", "documentation": "<p>The region the recommendation is generated for.</p>"}, "OfferingId": {"shape": "GenericString", "documentation": "<p>The unique ID that's used to distinguish Savings Plans from one another.</p>"}, "GenerationTimestamp": {"shape": "ZonedDateTime"}, "LatestUsageTimestamp": {"shape": "ZonedDateTime"}, "CurrentAverageHourlyOnDemandSpend": {"shape": "GenericString", "documentation": "<p>The average value of hourly On-Demand spend over the lookback period of the applicable usage type.</p>"}, "CurrentMaximumHourlyOnDemandSpend": {"shape": "GenericString", "documentation": "<p>The highest value of hourly On-Demand spend over the lookback period of the applicable usage type.</p>"}, "CurrentMinimumHourlyOnDemandSpend": {"shape": "GenericString", "documentation": "<p>The lowest value of hourly On-Demand spend over the lookback period of the applicable usage type.</p>"}, "EstimatedAverageUtilization": {"shape": "GenericString", "documentation": "<p>The estimated utilization of the recommended Savings Plan.</p>"}, "EstimatedMonthlySavingsAmount": {"shape": "GenericString", "documentation": "<p>The estimated monthly savings amount based on the recommended Savings Plan.</p>"}, "EstimatedOnDemandCost": {"shape": "GenericString", "documentation": "<p>The remaining On-Demand cost estimated to not be covered by the recommended Savings Plan, over the length of the lookback period.</p>"}, "EstimatedOnDemandCostWithCurrentCommitment": {"shape": "GenericString", "documentation": "<p>The estimated On-Demand costs you expect with no additional commitment, based on your usage of the selected time period and the Savings Plan you own.</p>"}, "EstimatedROI": {"shape": "GenericString", "documentation": "<p>The estimated return on investment that's based on the recommended Savings Plan that you purchased. This is calculated as estimatedSavingsAmount/estimatedSPCost*100.</p>"}, "EstimatedSPCost": {"shape": "GenericString", "documentation": "<p>The cost of the recommended Savings Plan over the length of the lookback period.</p>"}, "EstimatedSavingsAmount": {"shape": "GenericString", "documentation": "<p>The estimated savings amount that's based on the recommended Savings Plan over the length of the lookback period.</p>"}, "EstimatedSavingsPercentage": {"shape": "GenericString", "documentation": "<p>The estimated savings percentage relative to the total cost of applicable On-Demand usage over the lookback period.</p>"}, "ExistingHourlyCommitment": {"shape": "GenericString", "documentation": "<p>The existing hourly commitment for the Savings Plan type.</p>"}, "HourlyCommitmentToPurchase": {"shape": "GenericString", "documentation": "<p>The recommended hourly commitment level for the Savings Plan type and the configuration that's based on the usage during the lookback period.</p>"}, "UpfrontCost": {"shape": "GenericString", "documentation": "<p>The upfront cost of the recommended Savings Plan, based on the selected payment option.</p>"}, "CurrentAverageCoverage": {"shape": "GenericString", "documentation": "<p>The average value of hourly coverage over the lookback period.</p>"}, "EstimatedAverageCoverage": {"shape": "GenericString", "documentation": "<p>The estimated coverage of the recommended Savings Plan.</p>"}, "MetricsOverLookbackPeriod": {"shape": "MetricsOverLookbackPeriod", "documentation": "<p>The related hourly cost, coverage, and utilization metrics over the lookback period.</p>"}}, "documentation": "<p>The details and metrics for the given recommendation.</p>"}, "RecommendationDetailHourlyMetrics": {"type": "structure", "members": {"StartTime": {"shape": "ZonedDateTime"}, "EstimatedOnDemandCost": {"shape": "GenericString", "documentation": "<p>The remaining On-Demand cost estimated to not be covered by the recommended Savings Plan, over the length of the lookback period.</p>"}, "CurrentCoverage": {"shape": "GenericString", "documentation": "<p>The current amount of Savings Plans eligible usage that the Savings Plan covered.</p>"}, "EstimatedCoverage": {"shape": "GenericString", "documentation": "<p>The estimated coverage amount based on the recommended Savings Plan.</p>"}, "EstimatedNewCommitmentUtilization": {"shape": "GenericString", "documentation": "<p>The estimated utilization for the recommended Savings Plan.</p>"}}, "documentation": "<p>Contains the hourly metrics for the given recommendation over the lookback period. </p>"}, "RecommendationDetailId": {"type": "string", "max": 36, "min": 36, "pattern": "^[\\S\\s]{8}-[\\S\\s]{4}-[\\S\\s]{4}-[\\S\\s]{4}-[\\S\\s]{12}$"}, "RecommendationId": {"type": "string", "max": 36, "min": 36, "pattern": "^[\\S\\s]{8}-[\\S\\s]{4}-[\\S\\s]{4}-[\\S\\s]{4}-[\\S\\s]{12}$"}, "RecommendationIdList": {"type": "list", "member": {"shape": "RecommendationId"}}, "RecommendationTarget": {"type": "string", "enum": ["SAME_INSTANCE_FAMILY", "CROSS_INSTANCE_FAMILY"]}, "RedshiftInstanceDetails": {"type": "structure", "members": {"Family": {"shape": "GenericString", "documentation": "<p>The instance family of the recommended reservation.</p>"}, "NodeType": {"shape": "GenericString", "documentation": "<p>The type of node that Amazon Web Services recommends.</p>"}, "Region": {"shape": "GenericString", "documentation": "<p>The Amazon Web Services Region of the recommended reservation.</p>"}, "CurrentGeneration": {"shape": "GenericBoolean", "documentation": "<p>Determines whether the recommendation is for a current-generation instance.</p>"}, "SizeFlexEligible": {"shape": "GenericBoolean", "documentation": "<p>Determines whether the recommended reservation is size flexible.</p>"}}, "documentation": "<p>Details about the Amazon Redshift reservations that Amazon Web Services recommends that you purchase.</p>"}, "RequestChangedException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>Your request parameters changed between pages. Try again with the old parameters or without a pagination token.</p>", "exception": true}, "ReservationAggregates": {"type": "structure", "members": {"UtilizationPercentage": {"shape": "UtilizationPercentage", "documentation": "<p>The percentage of reservation time that you used.</p>"}, "UtilizationPercentageInUnits": {"shape": "UtilizationPercentageInUnits", "documentation": "<p>The percentage of Amazon EC2 reservation time that you used. It's converted to normalized units. Normalized units are available only for Amazon EC2 usage after November 11, 2017.</p>"}, "PurchasedHours": {"shape": "PurchasedHours", "documentation": "<p>How many reservation hours that you purchased.</p>"}, "PurchasedUnits": {"shape": "Purchased<PERSON>nits", "documentation": "<p>The number of Amazon EC2 reservation hours that you purchased. It's converted to normalized units. Normalized units are available only for Amazon EC2 usage after November 11, 2017.</p>"}, "TotalActualHours": {"shape": "TotalActualHours", "documentation": "<p>The total number of reservation hours that you used.</p>"}, "TotalActualUnits": {"shape": "TotalActualUnits", "documentation": "<p>The total number of Amazon EC2 reservation hours that you used. It's converted to normalized units. Normalized units are available only for Amazon EC2 usage after November 11, 2017.</p>"}, "UnusedHours": {"shape": "UnusedHours", "documentation": "<p>The number of reservation hours that you didn't use.</p>"}, "UnusedUnits": {"shape": "UnusedUnits", "documentation": "<p>The number of Amazon EC2 reservation hours that you didn't use. It's converted to normalized units. Normalized units are available only for Amazon EC2 usage after November 11, 2017.</p>"}, "OnDemandCostOfRIHoursUsed": {"shape": "OnDemandCostOfRIHoursUsed", "documentation": "<p>How much your reservation costs if charged On-Demand rates.</p>"}, "NetRISavings": {"shape": "NetRISavings", "documentation": "<p>How much you saved due to purchasing and utilizing reservation. Amazon Web Services calculates this by subtracting <code>TotalAmortizedFee</code> from <code>OnDemandCostOfRIHoursUsed</code>.</p>"}, "TotalPotentialRISavings": {"shape": "TotalPotentialRISavings", "documentation": "<p>How much you might save if you use your entire reservation.</p>"}, "AmortizedUpfrontFee": {"shape": "AmortizedUpfrontFee", "documentation": "<p>The upfront cost of your reservation. It's amortized over the reservation period.</p>"}, "AmortizedRecurringFee": {"shape": "AmortizedRecurringFee", "documentation": "<p>The monthly cost of your reservation. It's amortized over the reservation period.</p>"}, "TotalAmortizedFee": {"shape": "TotalAmortizedFee", "documentation": "<p>The total cost of your reservation. It's amortized over the reservation period.</p>"}, "RICostForUnusedHours": {"shape": "RICostForUnusedHours", "documentation": "<p>The cost of unused hours for your reservation.</p>"}, "RealizedSavings": {"shape": "RealizedSavings", "documentation": "<p>The realized savings because of purchasing and using a reservation.</p>"}, "UnrealizedSavings": {"shape": "UnrealizedSavings", "documentation": "<p>The unrealized savings because of purchasing and using a reservation.</p>"}}, "documentation": "<p>The aggregated numbers for your reservation usage.</p>"}, "ReservationCoverageGroup": {"type": "structure", "members": {"Attributes": {"shape": "Attributes", "documentation": "<p>The attributes for this group of reservations.</p>"}, "Coverage": {"shape": "Coverage", "documentation": "<p>How much instance usage this group of reservations covered.</p>"}}, "documentation": "<p>A group of reservations that share a set of attributes.</p>"}, "ReservationCoverageGroups": {"type": "list", "member": {"shape": "ReservationCoverageGroup"}}, "ReservationGroupKey": {"type": "string"}, "ReservationGroupValue": {"type": "string"}, "ReservationPurchaseRecommendation": {"type": "structure", "members": {"AccountScope": {"shape": "AccountScope", "documentation": "<p>The account scope that Amazon Web Services recommends that you purchase this instance for. For example, you can purchase this reservation for an entire organization in Amazon Web Services Organizations.</p>"}, "LookbackPeriodInDays": {"shape": "LookbackPeriodInDays", "documentation": "<p>How many days of previous usage that Amazon Web Services considers when making this recommendation.</p>"}, "TermInYears": {"shape": "TermInYears", "documentation": "<p>The term of the reservation that you want recommendations for, in years.</p>"}, "PaymentOption": {"shape": "PaymentOption", "documentation": "<p>The payment option for the reservation (for example, <code>AllUpfront</code> or <code>NoUpfront</code>).</p>"}, "ServiceSpecification": {"shape": "ServiceSpecification", "documentation": "<p>Hardware specifications for the service that you want recommendations for.</p>"}, "RecommendationDetails": {"shape": "ReservationPurchaseRecommendationDetails", "documentation": "<p>Details about the recommended purchases.</p>"}, "RecommendationSummary": {"shape": "ReservationPurchaseRecommendationSummary", "documentation": "<p>A summary about the recommended purchase.</p>"}}, "documentation": "<p>A specific reservation that Amazon Web Services recommends for purchase.</p>"}, "ReservationPurchaseRecommendationDetail": {"type": "structure", "members": {"AccountId": {"shape": "GenericString", "documentation": "<p>The account that this Reserved Instance (RI) recommendation is for.</p>"}, "InstanceDetails": {"shape": "InstanceDetails", "documentation": "<p>Details about the reservations that Amazon Web Services recommends that you purchase.</p>"}, "RecommendedNumberOfInstancesToPurchase": {"shape": "GenericString", "documentation": "<p>The number of instances that Amazon Web Services recommends that you purchase.</p>"}, "RecommendedNormalizedUnitsToPurchase": {"shape": "GenericString", "documentation": "<p>The number of normalized units that Amazon Web Services recommends that you purchase.</p>"}, "MinimumNumberOfInstancesUsedPerHour": {"shape": "GenericString", "documentation": "<p>The minimum number of instances that you used in an hour during the historical period. Amazon Web Services uses this to calculate your recommended reservation purchases.</p>"}, "MinimumNormalizedUnitsUsedPerHour": {"shape": "GenericString", "documentation": "<p>The minimum number of normalized units that you used in an hour during the historical period. Amazon Web Services uses this to calculate your recommended reservation purchases.</p>"}, "MaximumNumberOfInstancesUsedPerHour": {"shape": "GenericString", "documentation": "<p>The maximum number of instances that you used in an hour during the historical period. Amazon Web Services uses this to calculate your recommended reservation purchases.</p>"}, "MaximumNormalizedUnitsUsedPerHour": {"shape": "GenericString", "documentation": "<p>The maximum number of normalized units that you used in an hour during the historical period. Amazon Web Services uses this to calculate your recommended reservation purchases.</p>"}, "AverageNumberOfInstancesUsedPerHour": {"shape": "GenericString", "documentation": "<p>The average number of instances that you used in an hour during the historical period. Amazon Web Services uses this to calculate your recommended reservation purchases.</p>"}, "AverageNormalizedUnitsUsedPerHour": {"shape": "GenericString", "documentation": "<p>The average number of normalized units that you used in an hour during the historical period. Amazon Web Services uses this to calculate your recommended reservation purchases.</p>"}, "AverageUtilization": {"shape": "GenericString", "documentation": "<p>The average utilization of your instances. Amazon Web Services uses this to calculate your recommended reservation purchases.</p>"}, "EstimatedBreakEvenInMonths": {"shape": "GenericString", "documentation": "<p>How long Amazon Web Services estimates that it takes for this instance to start saving you money, in months.</p>"}, "CurrencyCode": {"shape": "GenericString", "documentation": "<p>The currency code that Amazon Web Services used to calculate the costs for this instance.</p>"}, "EstimatedMonthlySavingsAmount": {"shape": "GenericString", "documentation": "<p>How much Amazon Web Services estimates that this specific recommendation might save you in a month.</p>"}, "EstimatedMonthlySavingsPercentage": {"shape": "GenericString", "documentation": "<p>How much Amazon Web Services estimates that this specific recommendation might save you in a month, as a percentage of your overall costs.</p>"}, "EstimatedMonthlyOnDemandCost": {"shape": "GenericString", "documentation": "<p>How much Amazon Web Services estimates that you spend on On-Demand Instances in a month.</p>"}, "EstimatedReservationCostForLookbackPeriod": {"shape": "GenericString", "documentation": "<p>How much Amazon Web Services estimates that you might spend for all usage during the specified historical period if you had a reservation.</p>"}, "UpfrontCost": {"shape": "GenericString", "documentation": "<p>How much purchasing this instance costs you upfront.</p>"}, "RecurringStandardMonthlyCost": {"shape": "GenericString", "documentation": "<p>How much purchasing this instance costs you on a monthly basis.</p>"}, "ReservedCapacityDetails": {"shape": "ReservedCapacityDetails", "documentation": "<p>Details about the reservations that Amazon Web Services recommends that you purchase.</p>"}, "RecommendedNumberOfCapacityUnitsToPurchase": {"shape": "GenericString", "documentation": "<p>The number of reserved capacity units that Amazon Web Services recommends that you purchase.</p>"}, "MinimumNumberOfCapacityUnitsUsedPerHour": {"shape": "GenericString", "documentation": "<p>The minimum number of provisioned capacity units that you used in an hour during the historical period. Amazon Web Services uses this to calculate your recommended reservation purchases.</p>"}, "MaximumNumberOfCapacityUnitsUsedPerHour": {"shape": "GenericString", "documentation": "<p>The maximum number of provisioned capacity units that you used in an hour during the historical period. Amazon Web Services uses this to calculate your recommended reservation purchases.</p>"}, "AverageNumberOfCapacityUnitsUsedPerHour": {"shape": "GenericString", "documentation": "<p>The average number of provisioned capacity units that you used in an hour during the historical period. Amazon Web Services uses this to calculate your recommended reservation purchases.</p>"}}, "documentation": "<p>Details about your recommended reservation purchase.</p>"}, "ReservationPurchaseRecommendationDetails": {"type": "list", "member": {"shape": "ReservationPurchaseRecommendationDetail"}}, "ReservationPurchaseRecommendationMetadata": {"type": "structure", "members": {"RecommendationId": {"shape": "GenericString", "documentation": "<p>The ID for the recommendation.</p>"}, "GenerationTimestamp": {"shape": "GenericString", "documentation": "<p>The timestamp for when Amazon Web Services made the recommendation.</p>"}, "AdditionalMetadata": {"shape": "GenericString", "documentation": "<p>Additional metadata that might be applicable to the recommendation.</p>"}}, "documentation": "<p>Information about a recommendation, such as the timestamp for when Amazon Web Services made a specific recommendation.</p>"}, "ReservationPurchaseRecommendationSummary": {"type": "structure", "members": {"TotalEstimatedMonthlySavingsAmount": {"shape": "GenericString", "documentation": "<p>The total amount that Amazon Web Services estimates that this recommendation could save you in a month.</p>"}, "TotalEstimatedMonthlySavingsPercentage": {"shape": "GenericString", "documentation": "<p>The total amount that Amazon Web Services estimates that this recommendation could save you in a month, as a percentage of your costs.</p>"}, "CurrencyCode": {"shape": "GenericString", "documentation": "<p>The currency code used for this recommendation.</p>"}}, "documentation": "<p>A summary about this recommendation, such as the currency code, the amount that Amazon Web Services estimates that you could save, and the total amount of reservation to purchase.</p>"}, "ReservationPurchaseRecommendations": {"type": "list", "member": {"shape": "ReservationPurchaseRecommendation"}}, "ReservationUtilizationGroup": {"type": "structure", "members": {"Key": {"shape": "ReservationGroupKey", "documentation": "<p>The key for a specific reservation attribute.</p>"}, "Value": {"shape": "ReservationGroupValue", "documentation": "<p>The value of a specific reservation attribute.</p>"}, "Attributes": {"shape": "Attributes", "documentation": "<p>The attributes for this group of reservations.</p>"}, "Utilization": {"shape": "ReservationAggregates", "documentation": "<p>How much you used this group of reservations.</p>"}}, "documentation": "<p>A group of reservations that share a set of attributes.</p>"}, "ReservationUtilizationGroups": {"type": "list", "member": {"shape": "ReservationUtilizationGroup"}}, "ReservedCapacityDetails": {"type": "structure", "members": {"DynamoDBCapacityDetails": {"shape": "DynamoDBCapacityDetails", "documentation": "<p>The DynamoDB reservations that Amazon Web Services recommends that you purchase.</p>"}}, "documentation": "<p>Details about the reservations that Amazon Web Services recommends that you purchase.</p>"}, "ReservedHours": {"type": "string"}, "ReservedNormalizedUnits": {"type": "string"}, "ResourceDetails": {"type": "structure", "members": {"EC2ResourceDetails": {"shape": "EC2ResourceDetails", "documentation": "<p>Details for the Amazon EC2 resource.</p>"}}, "documentation": "<p>Details for the resource.</p>"}, "ResourceNotFoundException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}, "ResourceName": {"shape": "<PERSON><PERSON>"}}, "documentation": "<p> The specified ARN in the request doesn't exist. </p>", "exception": true}, "ResourceTag": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {"shape": "ResourceTagKey", "documentation": "<p>The key that's associated with the tag. </p>"}, "Value": {"shape": "ResourceTagValue", "documentation": "<p>The value that's associated with the tag. </p>"}}, "documentation": "<p>The tag structure that contains a tag key and value. </p> <note> <p>Tagging is supported only for the following Cost Explorer resource types: <a href=\"https://docs.aws.amazon.com/aws-cost-management/latest/APIReference/API_AnomalyMonitor.html\"> <code>AnomalyMonitor</code> </a>, <a href=\"https://docs.aws.amazon.com/aws-cost-management/latest/APIReference/API_AnomalySubscription.html\"> <code>AnomalySubscription</code> </a>, <a href=\"https://docs.aws.amazon.com/aws-cost-management/latest/APIReference/API_CostCategory.html\"> <code>CostCategory</code> </a>.</p> </note>"}, "ResourceTagKey": {"type": "string", "max": 128, "min": 1, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "ResourceTagKeyList": {"type": "list", "member": {"shape": "ResourceTagKey"}, "max": 200, "min": 0}, "ResourceTagList": {"type": "list", "member": {"shape": "ResourceTag"}, "max": 200, "min": 0}, "ResourceTagValue": {"type": "string", "max": 256, "min": 0, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "ResourceUtilization": {"type": "structure", "members": {"EC2ResourceUtilization": {"shape": "EC2ResourceUtilization", "documentation": "<p>The utilization of current Amazon EC2 instance. </p>"}}, "documentation": "<p>Resource utilization of current resource. </p>"}, "ResultByTime": {"type": "structure", "members": {"TimePeriod": {"shape": "DateInterval", "documentation": "<p>The time period that the result covers.</p>"}, "Total": {"shape": "Metrics", "documentation": "<p>The total amount of cost or usage accrued during the time period.</p>"}, "Groups": {"shape": "Groups", "documentation": "<p>The groups that this time period includes.</p>"}, "Estimated": {"shape": "Estimated", "documentation": "<p>Determines whether the result is estimated.</p>"}}, "documentation": "<p>The result that's associated with a time period.</p>"}, "ResultsByTime": {"type": "list", "member": {"shape": "ResultByTime"}}, "RightsizingRecommendation": {"type": "structure", "members": {"AccountId": {"shape": "GenericString", "documentation": "<p>The account that this recommendation is for.</p>"}, "CurrentInstance": {"shape": "CurrentInstance", "documentation": "<p>Context regarding the current instance.</p>"}, "RightsizingType": {"shape": "RightsizingType", "documentation": "<p>A recommendation to either terminate or modify the resource.</p>"}, "ModifyRecommendationDetail": {"shape": "ModifyRecommendationDetail", "documentation": "<p>The details for the modification recommendations. </p>"}, "TerminateRecommendationDetail": {"shape": "TerminateRecommendationDetail", "documentation": "<p>The details for termination recommendations.</p>"}, "FindingReasonCodes": {"shape": "FindingReasonCodes", "documentation": "<p>The list of possible reasons why the recommendation is generated, such as under- or over-utilization of specific metrics (for example, CPU, Memory, Network). </p>"}}, "documentation": "<p>Recommendations to rightsize resources.</p>"}, "RightsizingRecommendationConfiguration": {"type": "structure", "required": ["RecommendationTarget", "BenefitsConsidered"], "members": {"RecommendationTarget": {"shape": "RecommendationTarget", "documentation": "<p>The option to see recommendations within the same instance family or recommendations for instances across other families. The default value is <code>SAME_INSTANCE_FAMILY</code>. </p>"}, "BenefitsConsidered": {"shape": "GenericBoolean", "documentation": "<p>The option to consider RI or Savings Plans discount benefits in your savings calculation. The default value is <code>TRUE</code>. </p>"}}, "documentation": "<p>You can use <code>RightsizingRecommendationConfiguration</code> to customize recommendations across two attributes. You can choose to view recommendations for instances within the same instance families or across different instance families. You can also choose to view your estimated savings that are associated with recommendations with consideration of existing Savings Plans or Reserved Instance (RI) benefits, or neither. </p>"}, "RightsizingRecommendationList": {"type": "list", "member": {"shape": "RightsizingRecommendation"}}, "RightsizingRecommendationMetadata": {"type": "structure", "members": {"RecommendationId": {"shape": "GenericString", "documentation": "<p>The ID for the recommendation.</p>"}, "GenerationTimestamp": {"shape": "GenericString", "documentation": "<p>The timestamp for when Amazon Web Services made the recommendation.</p>"}, "LookbackPeriodInDays": {"shape": "LookbackPeriodInDays", "documentation": "<p>The number of days of previous usage that Amazon Web Services considers when making the recommendation.</p>"}, "AdditionalMetadata": {"shape": "GenericString", "documentation": "<p>Additional metadata that might be applicable to the recommendation.</p>"}}, "documentation": "<p>Metadata for a recommendation set.</p>"}, "RightsizingRecommendationSummary": {"type": "structure", "members": {"TotalRecommendationCount": {"shape": "GenericString", "documentation": "<p>The total number of instance recommendations.</p>"}, "EstimatedTotalMonthlySavingsAmount": {"shape": "GenericString", "documentation": "<p>The estimated total savings resulting from modifications, on a monthly basis.</p>"}, "SavingsCurrencyCode": {"shape": "GenericString", "documentation": "<p>The currency code that Amazon Web Services used to calculate the savings.</p>"}, "SavingsPercentage": {"shape": "GenericString", "documentation": "<p> The savings percentage based on the recommended modifications. It's relative to the total On-Demand costs that are associated with these instances.</p>"}}, "documentation": "<p>The summary of rightsizing recommendations </p>"}, "RightsizingType": {"type": "string", "enum": ["TERMINATE", "MODIFY"]}, "RootCause": {"type": "structure", "members": {"Service": {"shape": "GenericString", "documentation": "<p>The Amazon Web Services service name that's associated with the cost anomaly. </p>"}, "Region": {"shape": "GenericString", "documentation": "<p>The Amazon Web Services Region that's associated with the cost anomaly. </p>"}, "LinkedAccount": {"shape": "GenericString", "documentation": "<p>The member account value that's associated with the cost anomaly. </p>"}, "LinkedAccountName": {"shape": "GenericString", "documentation": "<p>The member account name value that's associated with the cost anomaly.</p>"}, "UsageType": {"shape": "GenericString", "documentation": "<p>The <code>UsageType</code> value that's associated with the cost anomaly. </p>"}, "Impact": {"shape": "RootCauseImpact", "documentation": "<p>The dollar impact for the root cause.</p>"}}, "documentation": "<p>The combination of Amazon Web Services service, linked account, linked account name, Region, and usage type where a cost anomaly is observed, along with the dollar and percentage amount of the anomaly impact. The linked account name will only be available when the account name can be identified.</p>"}, "RootCauseImpact": {"type": "structure", "required": ["Contribution"], "members": {"Contribution": {"shape": "GenericDouble", "documentation": "<p>The dollar amount that this root cause contributed to the anomaly's TotalImpact.</p>"}}, "documentation": "<p>The dollar value of the root cause.</p>"}, "RootCauses": {"type": "list", "member": {"shape": "RootCause"}}, "SavingsPlanArn": {"type": "string"}, "SavingsPlans": {"type": "structure", "members": {"PaymentOption": {"shape": "PaymentOption", "documentation": "<p>The payment option for the Savings Plans commitment.</p>"}, "SavingsPlansType": {"shape": "SupportedSavingsPlansType", "documentation": "<p>The Savings Plans type.</p>"}, "Region": {"shape": "GenericString", "documentation": "<p>The Region associated with the Savings Plans commitment.</p>"}, "InstanceFamily": {"shape": "GenericString", "documentation": "<p>The instance family of the Savings Plans commitment.</p>"}, "TermInYears": {"shape": "TermInYears", "documentation": "<p>The term that you want the Savings Plans commitment for.</p>"}, "SavingsPlansCommitment": {"shape": "SavingsPlansCommitment", "documentation": "<p>The Savings Plans commitment.</p>"}, "OfferingId": {"shape": "GenericString", "documentation": "<p>The unique ID that's used to distinguish Savings Plans commitments from one another.</p>"}}, "documentation": "<p>The Savings Plans commitment details.</p>"}, "SavingsPlansAmortizedCommitment": {"type": "structure", "members": {"AmortizedRecurringCommitment": {"shape": "GenericString", "documentation": "<p>The amortized amount of your Savings Plans commitment that was purchased with either a <code>Partial</code> or a <code>NoUpfront</code>.</p>"}, "AmortizedUpfrontCommitment": {"shape": "GenericString", "documentation": "<p>The amortized amount of your Savings Plans commitment that was purchased with an <code>Upfront</code> or <code>PartialUpfront</code> Savings Plans.</p>"}, "TotalAmortizedCommitment": {"shape": "GenericString", "documentation": "<p>The total amortized amount of your Savings Plans commitment, regardless of your Savings Plans purchase method. </p>"}}, "documentation": "<p>The amortized amount of Savings Plans purchased in a specific account during a specific time interval.</p>"}, "SavingsPlansCommitment": {"type": "double", "max": 5000, "min": 0.001}, "SavingsPlansCoverage": {"type": "structure", "members": {"Attributes": {"shape": "Attributes", "documentation": "<p>The attribute that applies to a specific <code>Dimension</code>.</p>"}, "Coverage": {"shape": "SavingsPlansCoverageData", "documentation": "<p>The amount of Savings Plans eligible usage that the Savings Plans covered.</p>"}, "TimePeriod": {"shape": "DateInterval"}}, "documentation": "<p>The amount of Savings Plans eligible usage that's covered by Savings Plans. All calculations consider the On-Demand equivalent of your Savings Plans usage.</p>"}, "SavingsPlansCoverageData": {"type": "structure", "members": {"SpendCoveredBySavingsPlans": {"shape": "GenericString", "documentation": "<p>The amount of your Amazon Web Services usage that's covered by a Savings Plans.</p>"}, "OnDemandCost": {"shape": "GenericString", "documentation": "<p>The cost of your Amazon Web Services usage at the public On-Demand rate.</p>"}, "TotalCost": {"shape": "GenericString", "documentation": "<p>The total cost of your Amazon Web Services usage, regardless of your purchase option.</p>"}, "CoveragePercentage": {"shape": "GenericString", "documentation": "<p>The percentage of your existing Savings Plans covered usage, divided by all of your eligible Savings Plans usage in an account (or set of accounts).</p>"}}, "documentation": "<p>Specific coverage percentage, On-Demand costs, and spend covered by Savings Plans, and total Savings Plans costs for an account.</p>"}, "SavingsPlansCoverages": {"type": "list", "member": {"shape": "SavingsPlansCoverage"}}, "SavingsPlansDataType": {"type": "string", "enum": ["ATTRIBUTES", "UTILIZATION", "AMORTIZED_COMMITMENT", "SAVINGS"]}, "SavingsPlansDataTypes": {"type": "list", "member": {"shape": "SavingsPlansDataType"}}, "SavingsPlansDetails": {"type": "structure", "members": {"Region": {"shape": "GenericString", "documentation": "<p>A collection of Amazon Web Services resources in a geographic area. Each Amazon Web Services Region is isolated and independent of the other Regions.</p>"}, "InstanceFamily": {"shape": "GenericString", "documentation": "<p>A group of instance types that Savings Plans applies to.</p>"}, "OfferingId": {"shape": "GenericString", "documentation": "<p>The unique ID that's used to distinguish Savings Plans from one another.</p>"}}, "documentation": "<p>The attribute details on a specific Savings Plan.</p>"}, "SavingsPlansId": {"type": "string", "max": 36, "min": 36, "pattern": "^[\\S\\s]{8}-[\\S\\s]{4}-[\\S\\s]{4}-[\\S\\s]{4}-[\\S\\s]{12}$"}, "SavingsPlansPurchaseAnalysisConfiguration": {"type": "structure", "required": ["AnalysisType", "SavingsPlansToAdd", "LookBackTimePeriod"], "members": {"AccountScope": {"shape": "AccountScope", "documentation": "<p>The account scope that you want your analysis for.</p>"}, "AccountId": {"shape": "AccountId", "documentation": "<p>The account that the analysis is for.</p>"}, "AnalysisType": {"shape": "AnalysisType", "documentation": "<p>The type of analysis.</p>"}, "SavingsPlansToAdd": {"shape": "SavingsPlansToAdd", "documentation": "<p>Savings Plans to include in the analysis.</p>"}, "SavingsPlansToExclude": {"shape": "SavingsPlansToExclude", "documentation": "<p>Savings Plans to exclude from the analysis.</p>"}, "LookBackTimePeriod": {"shape": "DateInterval", "documentation": "<p>The time period associated with the analysis.</p>"}}, "documentation": "<p>The configuration for the Savings Plans purchase analysis.</p>"}, "SavingsPlansPurchaseAnalysisDetails": {"type": "structure", "members": {"CurrencyCode": {"shape": "GenericString", "documentation": "<p>The currency code used for the analysis.</p>"}, "LookbackPeriodInHours": {"shape": "GenericString", "documentation": "<p>The lookback period in hours that's used to generate the analysis.</p>"}, "CurrentAverageCoverage": {"shape": "GenericString", "documentation": "<p>The average value of hourly coverage over the lookback period.</p>"}, "CurrentAverageHourlyOnDemandSpend": {"shape": "GenericString", "documentation": "<p>The average value of hourly On-Demand spend over the lookback period.</p>"}, "CurrentMaximumHourlyOnDemandSpend": {"shape": "GenericString", "documentation": "<p>The highest value of hourly On-Demand spend over the lookback period.</p>"}, "CurrentMinimumHourlyOnDemandSpend": {"shape": "GenericString", "documentation": "<p>The lowest value of hourly On-Demand spend over the lookback period.</p>"}, "CurrentOnDemandSpend": {"shape": "GenericString", "documentation": "<p>The current total On-Demand spend over the lookback period.</p>"}, "ExistingHourlyCommitment": {"shape": "GenericString", "documentation": "<p>The existing hourly commitment for the Savings Plan type.</p>"}, "HourlyCommitmentToPurchase": {"shape": "GenericString", "documentation": "<p>The recommended or custom hourly commitment.</p>"}, "EstimatedAverageCoverage": {"shape": "GenericString", "documentation": "<p>The estimated coverage of the Savings Plan.</p>"}, "EstimatedAverageUtilization": {"shape": "GenericString", "documentation": "<p>The estimated utilization of the Savings Plan.</p>"}, "EstimatedMonthlySavingsAmount": {"shape": "GenericString", "documentation": "<p>The estimated monthly savings amount based on the Savings Plan.</p>"}, "EstimatedOnDemandCost": {"shape": "GenericString", "documentation": "<p>The remaining On-Demand cost estimated to not be covered by the Savings Plan over the length of the lookback period.</p>"}, "EstimatedOnDemandCostWithCurrentCommitment": {"shape": "GenericString", "documentation": "<p>The estimated On-Demand cost you expect with no additional commitment based on your usage of the selected time period and the Savings Plan you own.</p>"}, "EstimatedROI": {"shape": "GenericString", "documentation": "<p>The estimated return on investment that's based on the Savings Plan and estimated savings. This is calculated as estimatedSavingsAmount/estimatedSPCost*100.</p>"}, "EstimatedSavingsAmount": {"shape": "GenericString", "documentation": "<p>The estimated savings amount that's based on the Savings Plan over the length of the lookback period.</p>"}, "EstimatedSavingsPercentage": {"shape": "GenericString", "documentation": "<p>The estimated savings percentage relative to the total cost over the cost calculation lookback period.</p>"}, "EstimatedCommitmentCost": {"shape": "GenericString", "documentation": "<p>The estimated cost of the Savings Plan over the length of the lookback period.</p>"}, "LatestUsageTimestamp": {"shape": "GenericString", "documentation": "<p>The date and time of the last hour that went into the analysis.</p>"}, "UpfrontCost": {"shape": "GenericString", "documentation": "<p>The upfront cost of the Savings Plan based on the selected payment option.</p>"}, "AdditionalMetadata": {"shape": "GenericString", "documentation": "<p>Additional metadata that might be applicable to the commitment.</p>"}, "MetricsOverLookbackPeriod": {"shape": "MetricsOverLookbackPeriod", "documentation": "<p>The related hourly cost, coverage, and utilization metrics over the lookback period.</p>"}}, "documentation": "<p>Details about the Savings Plans purchase analysis.</p>"}, "SavingsPlansPurchaseRecommendation": {"type": "structure", "members": {"AccountScope": {"shape": "AccountScope", "documentation": "<p>The account scope that you want your recommendations for. Amazon Web Services calculates recommendations that include the management account and member accounts if the value is set to <code>PAYER</code>. If the value is <code>LINKED</code>, recommendations are calculated for individual member accounts only.</p>"}, "SavingsPlansType": {"shape": "SupportedSavingsPlansType", "documentation": "<p>The requested Savings Plans recommendation type.</p>"}, "TermInYears": {"shape": "TermInYears", "documentation": "<p>The Savings Plans recommendation term in years. It's used to generate the recommendation.</p>"}, "PaymentOption": {"shape": "PaymentOption", "documentation": "<p>The payment option that's used to generate the recommendation.</p>"}, "LookbackPeriodInDays": {"shape": "LookbackPeriodInDays", "documentation": "<p>The lookback period in days that's used to generate the recommendation.</p>"}, "SavingsPlansPurchaseRecommendationDetails": {"shape": "SavingsPlansPurchaseRecommendationDetailList", "documentation": "<p>Details for the Savings Plans that we recommend that you purchase to cover existing Savings Plans eligible workloads.</p>"}, "SavingsPlansPurchaseRecommendationSummary": {"shape": "SavingsPlansPurchaseRecommendationSummary", "documentation": "<p>Summary metrics for your Savings Plans Recommendations. </p>"}}, "documentation": "<p>Contains your request parameters, Savings Plan Recommendations Summary, and Details.</p>"}, "SavingsPlansPurchaseRecommendationDetail": {"type": "structure", "members": {"SavingsPlansDetails": {"shape": "SavingsPlansDetails", "documentation": "<p>Details for your recommended Savings Plans.</p>"}, "AccountId": {"shape": "GenericString", "documentation": "<p>The <code>AccountID</code> the recommendation is generated for.</p>"}, "UpfrontCost": {"shape": "GenericString", "documentation": "<p>The upfront cost of the recommended Savings Plans, based on the selected payment option.</p>"}, "EstimatedROI": {"shape": "GenericString", "documentation": "<p>The estimated return on investment that's based on the recommended Savings Plans that you purchased. This is calculated as <code>estimatedSavingsAmount</code>/ <code>estimatedSPCost</code>*100.</p>"}, "CurrencyCode": {"shape": "GenericString", "documentation": "<p>The currency code that Amazon Web Services used to generate the recommendations and present potential savings.</p>"}, "EstimatedSPCost": {"shape": "GenericString", "documentation": "<p>The cost of the recommended Savings Plans over the length of the lookback period.</p>"}, "EstimatedOnDemandCost": {"shape": "GenericString", "documentation": "<p>The remaining On-Demand cost estimated to not be covered by the recommended Savings Plans, over the length of the lookback period.</p>"}, "EstimatedOnDemandCostWithCurrentCommitment": {"shape": "GenericString", "documentation": "<p> The estimated On-Demand costs you expect with no additional commitment, based on your usage of the selected time period and the Savings Plans you own. </p>"}, "EstimatedSavingsAmount": {"shape": "GenericString", "documentation": "<p>The estimated savings amount that's based on the recommended Savings Plans over the length of the lookback period.</p>"}, "EstimatedSavingsPercentage": {"shape": "GenericString", "documentation": "<p>The estimated savings percentage relative to the total cost of applicable On-Demand usage over the lookback period.</p>"}, "HourlyCommitmentToPurchase": {"shape": "GenericString", "documentation": "<p>The recommended hourly commitment level for the Savings Plans type and the configuration that's based on the usage during the lookback period.</p>"}, "EstimatedAverageUtilization": {"shape": "GenericString", "documentation": "<p>The estimated utilization of the recommended Savings Plans.</p>"}, "EstimatedMonthlySavingsAmount": {"shape": "GenericString", "documentation": "<p>The estimated monthly savings amount based on the recommended Savings Plans.</p>"}, "CurrentMinimumHourlyOnDemandSpend": {"shape": "GenericString", "documentation": "<p>The lowest value of hourly On-Demand spend over the lookback period of the applicable usage type.</p>"}, "CurrentMaximumHourlyOnDemandSpend": {"shape": "GenericString", "documentation": "<p>The highest value of hourly On-Demand spend over the lookback period of the applicable usage type.</p>"}, "CurrentAverageHourlyOnDemandSpend": {"shape": "GenericString", "documentation": "<p>The average value of hourly On-Demand spend over the lookback period of the applicable usage type.</p>"}, "RecommendationDetailId": {"shape": "RecommendationDetailId", "documentation": "<p>Contains detailed information about a specific Savings Plan recommendation.</p>"}}, "documentation": "<p>Details for your recommended Savings Plans.</p>"}, "SavingsPlansPurchaseRecommendationDetailList": {"type": "list", "member": {"shape": "SavingsPlansPurchaseRecommendationDetail"}}, "SavingsPlansPurchaseRecommendationMetadata": {"type": "structure", "members": {"RecommendationId": {"shape": "GenericString", "documentation": "<p>The unique identifier for the recommendation set.</p>"}, "GenerationTimestamp": {"shape": "GenericString", "documentation": "<p>The timestamp that shows when the recommendations were generated.</p>"}, "AdditionalMetadata": {"shape": "GenericString", "documentation": "<p>Additional metadata that might be applicable to the recommendation.</p>"}}, "documentation": "<p>Metadata about your Savings Plans Purchase Recommendations.</p>"}, "SavingsPlansPurchaseRecommendationSummary": {"type": "structure", "members": {"EstimatedROI": {"shape": "GenericString", "documentation": "<p>The estimated return on investment that's based on the recommended Savings Plans and estimated savings.</p>"}, "CurrencyCode": {"shape": "GenericString", "documentation": "<p>The currency code that Amazon Web Services used to generate the recommendations and present potential savings.</p>"}, "EstimatedTotalCost": {"shape": "GenericString", "documentation": "<p>The estimated total cost of the usage after purchasing the recommended Savings Plans. This is a sum of the cost of Savings Plans during this term, and the remaining On-Demand usage.</p>"}, "CurrentOnDemandSpend": {"shape": "GenericString", "documentation": "<p>The current total on demand spend of the applicable usage types over the lookback period.</p>"}, "EstimatedSavingsAmount": {"shape": "GenericString", "documentation": "<p>The estimated total savings over the lookback period, based on the purchase of the recommended Savings Plans.</p>"}, "TotalRecommendationCount": {"shape": "GenericString", "documentation": "<p>The aggregate number of Savings Plans recommendations that exist for your account.</p>"}, "DailyCommitmentToPurchase": {"shape": "GenericString", "documentation": "<p>The recommended Savings Plans cost on a daily (24 hourly) basis.</p>"}, "HourlyCommitmentToPurchase": {"shape": "GenericString", "documentation": "<p>The recommended hourly commitment that's based on the recommendation parameters.</p>"}, "EstimatedSavingsPercentage": {"shape": "GenericString", "documentation": "<p>The estimated savings relative to the total cost of On-Demand usage, over the lookback period. This is calculated as <code>estimatedSavingsAmount</code>/ <code>CurrentOnDemandSpend</code>*100.</p>"}, "EstimatedMonthlySavingsAmount": {"shape": "GenericString", "documentation": "<p>The estimated monthly savings amount that's based on the recommended Savings Plans purchase.</p>"}, "EstimatedOnDemandCostWithCurrentCommitment": {"shape": "GenericString", "documentation": "<p>The estimated On-Demand costs you expect with no additional commitment. It's based on your usage of the selected time period and the Savings Plans you own. </p>"}}, "documentation": "<p>Summary metrics for your Savings Plans Purchase Recommendations.</p>"}, "SavingsPlansSavings": {"type": "structure", "members": {"NetSavings": {"shape": "GenericString", "documentation": "<p>The savings amount that you're accumulating for the usage that's covered by a Savings Plans, when compared to the On-Demand equivalent of the same usage.</p>"}, "OnDemandCostEquivalent": {"shape": "GenericString", "documentation": "<p>How much the amount that the usage would have cost if it was accrued at the On-Demand rate.</p>"}}, "documentation": "<p>The amount of savings that you're accumulating, against the public On-Demand rate of the usage accrued in an account.</p>"}, "SavingsPlansToAdd": {"type": "list", "member": {"shape": "SavingsPlans"}, "max": 1, "min": 1}, "SavingsPlansToExclude": {"type": "list", "member": {"shape": "SavingsPlansId"}, "max": 1000, "min": 0}, "SavingsPlansUtilization": {"type": "structure", "members": {"TotalCommitment": {"shape": "GenericString", "documentation": "<p>The total amount of Savings Plans commitment that's been purchased in an account (or set of accounts).</p>"}, "UsedCommitment": {"shape": "GenericString", "documentation": "<p>The amount of your Savings Plans commitment that was consumed from Savings Plans eligible usage in a specific period.</p>"}, "UnusedCommitment": {"shape": "GenericString", "documentation": "<p>The amount of your Savings Plans commitment that wasn't consumed from Savings Plans eligible usage in a specific period.</p>"}, "UtilizationPercentage": {"shape": "GenericString", "documentation": "<p>The amount of <code>UsedCommitment</code> divided by the <code>TotalCommitment</code> for your Savings Plans.</p>"}}, "documentation": "<p>The measurement of how well you're using your existing Savings Plans.</p>"}, "SavingsPlansUtilizationAggregates": {"type": "structure", "required": ["Utilization"], "members": {"Utilization": {"shape": "SavingsPlansUtilization", "documentation": "<p>A ratio of your effectiveness of using existing Savings Plans to apply to workloads that are Savings Plans eligible.</p>"}, "Savings": {"shape": "SavingsPlansSavings", "documentation": "<p>The amount that's saved by using existing Savings Plans. Savings returns both net savings from Savings Plans and also the <code>onDemandCostEquivalent</code> of the Savings Plans when considering the utilization rate.</p>"}, "AmortizedCommitment": {"shape": "SavingsPlansAmortizedCommitment", "documentation": "<p>The total amortized commitment for a Savings Plans. This includes the sum of the upfront and recurring Savings Plans fees.</p>"}}, "documentation": "<p>The aggregated utilization metrics for your Savings Plans usage.</p>"}, "SavingsPlansUtilizationByTime": {"type": "structure", "required": ["TimePeriod", "Utilization"], "members": {"TimePeriod": {"shape": "DateInterval"}, "Utilization": {"shape": "SavingsPlansUtilization", "documentation": "<p>A ratio of your effectiveness of using existing Savings Plans to apply to workloads that are Savings Plans eligible.</p>"}, "Savings": {"shape": "SavingsPlansSavings", "documentation": "<p>The amount that's saved by using existing Savings Plans. Savings returns both net savings from Savings Plans and also the <code>onDemandCostEquivalent</code> of the Savings Plans when considering the utilization rate.</p>"}, "AmortizedCommitment": {"shape": "SavingsPlansAmortizedCommitment", "documentation": "<p>The total amortized commitment for a Savings Plans. This includes the sum of the upfront and recurring Savings Plans fees.</p>"}}, "documentation": "<p>The amount of Savings Plans utilization (in hours).</p>"}, "SavingsPlansUtilizationDetail": {"type": "structure", "members": {"SavingsPlanArn": {"shape": "SavingsPlanArn", "documentation": "<p>The unique Amazon Resource Name (ARN) for a particular Savings Plan.</p>"}, "Attributes": {"shape": "Attributes", "documentation": "<p>The attribute that applies to a specific <code>Dimension</code>.</p>"}, "Utilization": {"shape": "SavingsPlansUtilization", "documentation": "<p>A ratio of your effectiveness of using existing Savings Plans to apply to workloads that are Savings Plans eligible.</p>"}, "Savings": {"shape": "SavingsPlansSavings", "documentation": "<p>The amount saved by using existing Savings Plans. Savings returns both net savings from savings plans and also the <code>onDemandCostEquivalent</code> of the Savings Plans when considering the utilization rate.</p>"}, "AmortizedCommitment": {"shape": "SavingsPlansAmortizedCommitment", "documentation": "<p>The total amortized commitment for a Savings Plans. Includes the sum of the upfront and recurring Savings Plans fees.</p>"}}, "documentation": "<p>A single daily or monthly Savings Plans utilization rate and details for your account. A management account in an organization have access to member accounts. You can use <code>GetDimensionValues</code> to determine the possible dimension values. </p>"}, "SavingsPlansUtilizationDetails": {"type": "list", "member": {"shape": "SavingsPlansUtilizationDetail"}}, "SavingsPlansUtilizationsByTime": {"type": "list", "member": {"shape": "SavingsPlansUtilizationByTime"}}, "SearchString": {"type": "string", "max": 1024, "min": 0, "pattern": "[\\S\\s]*"}, "ServiceQuotaExceededException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p> You've reached the limit on the number of resources you can create, or exceeded the size of an individual resource. </p>", "exception": true}, "ServiceSpecification": {"type": "structure", "members": {"EC2Specification": {"shape": "EC2Specification", "documentation": "<p>The Amazon EC2 hardware specifications that you want Amazon Web Services to provide recommendations for.</p>"}}, "documentation": "<p>Hardware specifications for the service that you want recommendations for.</p>"}, "SortDefinition": {"type": "structure", "required": ["Key"], "members": {"Key": {"shape": "SortDefinitionKey", "documentation": "<p>The key that's used to sort the data.</p>"}, "SortOrder": {"shape": "SortOrder", "documentation": "<p>The order that's used to sort the data.</p>"}}, "documentation": "<p>The details for how to sort the data.</p>"}, "SortDefinitionKey": {"type": "string", "max": 1024, "min": 0, "pattern": "[\\S\\s]*"}, "SortDefinitions": {"type": "list", "member": {"shape": "SortDefinition"}}, "SortOrder": {"type": "string", "enum": ["ASCENDING", "DESCENDING"]}, "StartCommitmentPurchaseAnalysisRequest": {"type": "structure", "required": ["CommitmentPurchaseAnalysisConfiguration"], "members": {"CommitmentPurchaseAnalysisConfiguration": {"shape": "CommitmentPurchaseAnalysisConfiguration", "documentation": "<p>The configuration for the commitment purchase analysis.</p>"}}}, "StartCommitmentPurchaseAnalysisResponse": {"type": "structure", "required": ["AnalysisId", "AnalysisStartedTime", "EstimatedCompletionTime"], "members": {"AnalysisId": {"shape": "AnalysisId", "documentation": "<p>The analysis ID that's associated with the commitment purchase analysis.</p>"}, "AnalysisStartedTime": {"shape": "ZonedDateTime", "documentation": "<p>The start time of the analysis.</p>"}, "EstimatedCompletionTime": {"shape": "ZonedDateTime", "documentation": "<p>The estimated time for when the analysis will complete.</p>"}}}, "StartCostAllocationTagBackfillRequest": {"type": "structure", "required": ["BackfillFrom"], "members": {"BackfillFrom": {"shape": "ZonedDateTime", "documentation": "<p> The date you want the backfill to start from. The date can only be a first day of the month (a billing start date). Dates can't precede the previous twelve months, or in the future.</p>"}}}, "StartCostAllocationTagBackfillResponse": {"type": "structure", "members": {"BackfillRequest": {"shape": "CostAllocationTagBackfillRequest", "documentation": "<p> An object containing detailed metadata of your new backfill request. </p>"}}}, "StartSavingsPlansPurchaseRecommendationGenerationRequest": {"type": "structure", "members": {}}, "StartSavingsPlansPurchaseRecommendationGenerationResponse": {"type": "structure", "members": {"RecommendationId": {"shape": "RecommendationId", "documentation": "<p>The ID for this specific recommendation.</p>"}, "GenerationStartedTime": {"shape": "ZonedDateTime", "documentation": "<p>The start time of the recommendation generation.</p>"}, "EstimatedCompletionTime": {"shape": "ZonedDateTime", "documentation": "<p>The estimated time for when the recommendation generation will complete.</p>"}}}, "Subscriber": {"type": "structure", "members": {"Address": {"shape": "Subscriber<PERSON><PERSON><PERSON>", "documentation": "<p>The email address or SNS Amazon Resource Name (ARN). This depends on the <code>Type</code>. </p>"}, "Type": {"shape": "SubscriberType", "documentation": "<p>The notification delivery channel. </p>"}, "Status": {"shape": "SubscriberStatus", "documentation": "<p>Indicates if the subscriber accepts the notifications. </p>"}}, "documentation": "<p>The recipient of <code>AnomalySubscription</code> notifications. </p>"}, "SubscriberAddress": {"type": "string", "max": 302, "min": 6, "pattern": "(^[a-zA-Z0-9.!#$%&'*+=?^_‘{|}~-]+@[a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)+$)|(^arn:(aws[a-zA-Z-]*):sns:[a-zA-Z0-9-]+:[0-9]{12}:[a-zA-Z0-9_-]+(\\.fifo)?$)"}, "SubscriberStatus": {"type": "string", "enum": ["CONFIRMED", "DECLINED"]}, "SubscriberType": {"type": "string", "enum": ["EMAIL", "SNS"]}, "Subscribers": {"type": "list", "member": {"shape": "Subscriber"}}, "SupportedSavingsPlansType": {"type": "string", "enum": ["COMPUTE_SP", "EC2_INSTANCE_SP", "SAGEMAKER_SP"]}, "TagKey": {"type": "string", "max": 1024, "min": 0, "pattern": "[\\S\\s]*"}, "TagList": {"type": "list", "member": {"shape": "Entity"}}, "TagResourceRequest": {"type": "structure", "required": ["ResourceArn", "ResourceTags"], "members": {"ResourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the resource. For a list of supported resources, see <a href=\"https://docs.aws.amazon.com/aws-cost-management/latest/APIReference/API_ResourceTag.html\">ResourceTag</a>. </p>"}, "ResourceTags": {"shape": "ResourceTagList", "documentation": "<p> A list of tag key-value pairs to be added to the resource.</p> <p>Each tag consists of a key and a value, and each key must be unique for the resource. The following restrictions apply to resource tags:</p> <ul> <li> <p>Although the maximum number of array members is 200, you can assign a maximum of 50 user-tags to one resource. The remaining are reserved for Amazon Web Services use</p> </li> <li> <p>The maximum length of a key is 128 characters</p> </li> <li> <p>The maximum length of a value is 256 characters</p> </li> <li> <p>Keys and values can only contain alphanumeric characters, spaces, and any of the following: <code>_.:/=+@-</code> </p> </li> <li> <p>Keys and values are case sensitive</p> </li> <li> <p>Keys and values are trimmed for any leading or trailing whitespaces</p> </li> <li> <p>Don’t use <code>aws:</code> as a prefix for your keys. This prefix is reserved for Amazon Web Services use</p> </li> </ul>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValues": {"type": "structure", "members": {"Key": {"shape": "TagKey", "documentation": "<p>The key for the tag.</p>"}, "Values": {"shape": "Values", "documentation": "<p>The specific value of the tag.</p>"}, "MatchOptions": {"shape": "MatchOptions", "documentation": "<p>The match options that you can use to filter your results. <code>MatchOptions</code> is only applicable for actions related to Cost Category. The default values for <code>MatchOptions</code> are <code>EQUALS</code> and <code>CASE_SENSITIVE</code>.</p>"}}, "documentation": "<p>The values that are available for a tag.</p> <p>If <code>Values</code> and <code>Key</code> aren't specified, the <code>ABSENT</code> <code>MatchOption</code> is applied to all tags. That is, it's filtered on resources with no tags.</p> <p>If <code>Key</code> is provided and <code>Values</code> isn't specified, the <code>ABSENT</code> <code>MatchOption</code> is applied to the tag <code>Key</code> only. That is, it's filtered on resources without the given tag key.</p>"}, "TagValuesList": {"type": "list", "member": {"shape": "TagValues"}}, "TargetInstance": {"type": "structure", "members": {"EstimatedMonthlyCost": {"shape": "GenericString", "documentation": "<p>The expected cost to operate this instance type on a monthly basis.</p>"}, "EstimatedMonthlySavings": {"shape": "GenericString", "documentation": "<p>The estimated savings that result from modification, on a monthly basis.</p>"}, "CurrencyCode": {"shape": "GenericString", "documentation": "<p>The currency code that Amazon Web Services used to calculate the costs for this instance.</p>"}, "DefaultTargetInstance": {"shape": "GenericBoolean", "documentation": "<p>Determines whether this recommendation is the defaulted Amazon Web Services recommendation.</p>"}, "ResourceDetails": {"shape": "ResourceDetails", "documentation": "<p>Details on the target instance type. </p>"}, "ExpectedResourceUtilization": {"shape": "ResourceUtilization", "documentation": "<p>The expected utilization metrics for target instance type.</p>"}, "PlatformDifferences": {"shape": "PlatformDifferences", "documentation": "<p>Explains the actions that you might need to take to successfully migrate your workloads from the current instance type to the recommended instance type. </p>"}}, "documentation": "<p>Details on recommended instance.</p>"}, "TargetInstancesList": {"type": "list", "member": {"shape": "TargetInstance"}}, "TermInYears": {"type": "string", "enum": ["ONE_YEAR", "THREE_YEARS"]}, "TerminateRecommendationDetail": {"type": "structure", "members": {"EstimatedMonthlySavings": {"shape": "GenericString", "documentation": "<p>The estimated savings that result from modification, on a monthly basis.</p>"}, "CurrencyCode": {"shape": "GenericString", "documentation": "<p>The currency code that Amazon Web Services used to calculate the costs for this instance.</p>"}}, "documentation": "<p>Details on termination recommendation. </p>"}, "TooManyTagsException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}, "ResourceName": {"shape": "<PERSON><PERSON>"}}, "documentation": "<p>Can occur if you specify a number of tags for a resource greater than the maximum 50 user tags per resource.</p>", "exception": true}, "TotalActualHours": {"type": "string"}, "TotalActualUnits": {"type": "string"}, "TotalAmortizedFee": {"type": "string"}, "TotalImpactFilter": {"type": "structure", "required": ["NumericOperator", "StartValue"], "members": {"NumericOperator": {"shape": "NumericOperator", "documentation": "<p>The comparing value that's used in the filter. </p>"}, "StartValue": {"shape": "GenericDouble", "documentation": "<p>The lower bound dollar value that's used in the filter. </p>"}, "EndValue": {"shape": "GenericDouble", "documentation": "<p>The upper bound dollar value that's used in the filter. </p>"}}, "documentation": "<p>Filters cost anomalies based on the total impact. </p>"}, "TotalPotentialRISavings": {"type": "string"}, "TotalRunningHours": {"type": "string"}, "TotalRunningNormalizedUnits": {"type": "string"}, "UnknownMonitorException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The cost anomaly monitor does not exist for the account. </p>", "exception": true}, "UnknownSubscriptionException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The cost anomaly subscription does not exist for the account. </p>", "exception": true}, "UnrealizedSavings": {"type": "string"}, "UnresolvableUsageUnitException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>Cost Explorer was unable to identify the usage unit. Provide <code>UsageType/UsageTypeGroup</code> filter selections that contain matching units, for example: <code>hours</code>.</p>", "exception": true}, "UntagResourceRequest": {"type": "structure", "required": ["ResourceArn", "ResourceTagKeys"], "members": {"ResourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the resource. For a list of supported resources, see <a href=\"https://docs.aws.amazon.com/aws-cost-management/latest/APIReference/API_ResourceTag.html\">ResourceTag</a>. </p>"}, "ResourceTagKeys": {"shape": "ResourceTagKeyList", "documentation": "<p>A list of tag keys associated with tags that need to be removed from the resource. If you specify a tag key that doesn't exist, it's ignored. Although the maximum number of array members is 200, user-tag maximum is 50. The remaining are reserved for Amazon Web Services use. </p>"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UnusedHours": {"type": "string"}, "UnusedUnits": {"type": "string"}, "UpdateAnomalyMonitorRequest": {"type": "structure", "required": ["MonitorArn"], "members": {"MonitorArn": {"shape": "GenericString", "documentation": "<p>Cost anomaly monitor Amazon Resource Names (ARNs). </p>"}, "MonitorName": {"shape": "GenericString", "documentation": "<p>The new name for the cost anomaly monitor. </p>"}}}, "UpdateAnomalyMonitorResponse": {"type": "structure", "required": ["MonitorArn"], "members": {"MonitorArn": {"shape": "GenericString", "documentation": "<p>A cost anomaly monitor ARN. </p>"}}}, "UpdateAnomalySubscriptionRequest": {"type": "structure", "required": ["SubscriptionArn"], "members": {"SubscriptionArn": {"shape": "GenericString", "documentation": "<p>A cost anomaly subscription Amazon Resource Name (ARN). </p>"}, "Threshold": {"shape": "NullableNonNegativeDouble", "documentation": "<p>(deprecated)</p> <p>The update to the threshold value for receiving notifications. </p> <p>This field has been deprecated. To update a threshold, use ThresholdExpression. Continued use of Threshold will be treated as shorthand syntax for a ThresholdExpression.</p> <p>You can specify either Threshold or ThresholdExpression, but not both.</p>", "deprecated": true, "deprecatedMessage": "Threshold has been deprecated in favor of ThresholdExpression"}, "Frequency": {"shape": "AnomalySubscriptionFrequency", "documentation": "<p>The update to the frequency value that subscribers receive notifications. </p>"}, "MonitorArnList": {"shape": "MonitorArnList", "documentation": "<p>A list of cost anomaly monitor ARNs. </p>"}, "Subscribers": {"shape": "Subscribers", "documentation": "<p>The update to the subscriber list. </p>"}, "SubscriptionName": {"shape": "GenericString", "documentation": "<p>The new name of the subscription. </p>"}, "ThresholdExpression": {"shape": "Expression", "documentation": "<p>The update to the <a href=\"https://docs.aws.amazon.com/aws-cost-management/latest/APIReference/API_Expression.html\">Expression</a> object used to specify the anomalies that you want to generate alerts for. This supports dimensions and nested expressions. The supported dimensions are <code>ANOMALY_TOTAL_IMPACT_ABSOLUTE</code> and <code>ANOMALY_TOTAL_IMPACT_PERCENTAGE</code>, corresponding to an anomaly’s TotalImpact and TotalImpactPercentage, respectively (see <a href=\"https://docs.aws.amazon.com/aws-cost-management/latest/APIReference/API_Impact.html\">Impact</a> for more details). The supported nested expression types are <code>AND</code> and <code>OR</code>. The match option <code>GREATER_THAN_OR_EQUAL</code> is required. Values must be numbers between 0 and 10,000,000,000 in string format.</p> <p>You can specify either Threshold or ThresholdExpression, but not both.</p> <p>The following are examples of valid ThresholdExpressions:</p> <ul> <li> <p>Absolute threshold: <code>{ \"Dimensions\": { \"Key\": \"ANOMALY_TOTAL_IMPACT_ABSOLUTE\", \"MatchOptions\": [ \"GREATER_THAN_OR_EQUAL\" ], \"Values\": [ \"100\" ] } }</code> </p> </li> <li> <p>Percentage threshold: <code>{ \"Dimensions\": { \"Key\": \"ANOMALY_TOTAL_IMPACT_PERCENTAGE\", \"MatchOptions\": [ \"GREATER_THAN_OR_EQUAL\" ], \"Values\": [ \"100\" ] } }</code> </p> </li> <li> <p> <code>AND</code> two thresholds together: <code>{ \"And\": [ { \"Dimensions\": { \"Key\": \"ANOMALY_TOTAL_IMPACT_ABSOLUTE\", \"MatchOptions\": [ \"GREATER_THAN_OR_EQUAL\" ], \"Values\": [ \"100\" ] } }, { \"Dimensions\": { \"Key\": \"ANOMALY_TOTAL_IMPACT_PERCENTAGE\", \"MatchOptions\": [ \"GREATER_THAN_OR_EQUAL\" ], \"Values\": [ \"100\" ] } } ] }</code> </p> </li> <li> <p> <code>OR</code> two thresholds together: <code>{ \"Or\": [ { \"Dimensions\": { \"Key\": \"ANOMALY_TOTAL_IMPACT_ABSOLUTE\", \"MatchOptions\": [ \"GREATER_THAN_OR_EQUAL\" ], \"Values\": [ \"100\" ] } }, { \"Dimensions\": { \"Key\": \"ANOMALY_TOTAL_IMPACT_PERCENTAGE\", \"MatchOptions\": [ \"GREATER_THAN_OR_EQUAL\" ], \"Values\": [ \"100\" ] } } ] }</code> </p> </li> </ul>"}}}, "UpdateAnomalySubscriptionResponse": {"type": "structure", "required": ["SubscriptionArn"], "members": {"SubscriptionArn": {"shape": "GenericString", "documentation": "<p>A cost anomaly subscription ARN. </p>"}}}, "UpdateCostAllocationTagsStatusError": {"type": "structure", "members": {"TagKey": {"shape": "TagKey", "documentation": "<p>The key for the cost allocation tag. </p>"}, "Code": {"shape": "GenericString", "documentation": "<p>An error code representing why the action failed on this entry. </p>"}, "Message": {"shape": "ErrorMessage", "documentation": "<p>A message explaining why the action failed on this entry. </p>"}}, "documentation": "<p>Gives a detailed description of the result of an action. It's on each cost allocation tag entry in the request. </p>"}, "UpdateCostAllocationTagsStatusErrors": {"type": "list", "member": {"shape": "UpdateCostAllocationTagsStatusError"}, "max": 20, "min": 0}, "UpdateCostAllocationTagsStatusRequest": {"type": "structure", "required": ["CostAllocationTagsStatus"], "members": {"CostAllocationTagsStatus": {"shape": "CostAllocationTagStatusList", "documentation": "<p>The list of <code>CostAllocationTagStatusEntry</code> objects that are used to update cost allocation tags status for this request. </p>"}}}, "UpdateCostAllocationTagsStatusResponse": {"type": "structure", "members": {"Errors": {"shape": "UpdateCostAllocationTagsStatusErrors", "documentation": "<p>A list of <code>UpdateCostAllocationTagsStatusError</code> objects with error details about each cost allocation tag that can't be updated. If there's no failure, an empty array returns. </p>"}}}, "UpdateCostCategoryDefinitionRequest": {"type": "structure", "required": ["CostCategoryArn", "RuleVersion", "Rules"], "members": {"CostCategoryArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The unique identifier for your Cost Category.</p>"}, "EffectiveStart": {"shape": "ZonedDateTime", "documentation": "<p>The Cost Category's effective start date. It can only be a billing start date (first day of the month). If the date isn't provided, it's the first day of the current month. Dates can't be before the previous twelve months, or in the future.</p>"}, "RuleVersion": {"shape": "CostCategoryRuleVersion"}, "Rules": {"shape": "CostCategoryRulesList", "documentation": "<p>The <code>Expression</code> object used to categorize costs. For more information, see <a href=\"https://docs.aws.amazon.com/aws-cost-management/latest/APIReference/API_CostCategoryRule.html\">CostCategoryRule </a>. </p>"}, "DefaultValue": {"shape": "CostCategoryValue"}, "SplitChargeRules": {"shape": "CostCategorySplitChargeRulesList", "documentation": "<p> The split charge rules used to allocate your charges between your Cost Category values. </p>"}}}, "UpdateCostCategoryDefinitionResponse": {"type": "structure", "members": {"CostCategoryArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The unique identifier for your Cost Category. </p>"}, "EffectiveStart": {"shape": "ZonedDateTime", "documentation": "<p>The Cost Category's effective start date. It can only be a billing start date (first day of the month).</p>"}}}, "UsageServices": {"type": "list", "member": {"shape": "GenericString"}}, "UtilizationByTime": {"type": "structure", "members": {"TimePeriod": {"shape": "DateInterval", "documentation": "<p>The period of time that this utilization was used for.</p>"}, "Groups": {"shape": "ReservationUtilizationGroups", "documentation": "<p>The groups that this utilization result uses.</p>"}, "Total": {"shape": "ReservationAggregates", "documentation": "<p>The total number of reservation hours that were used.</p>"}}, "documentation": "<p>The amount of utilization, in hours.</p>"}, "UtilizationPercentage": {"type": "string"}, "UtilizationPercentageInUnits": {"type": "string"}, "UtilizationsByTime": {"type": "list", "member": {"shape": "UtilizationByTime"}}, "Value": {"type": "string", "max": 1024, "min": 0, "pattern": "[\\S\\s]*"}, "Values": {"type": "list", "member": {"shape": "Value"}}, "YearMonthDay": {"type": "string", "max": 40, "min": 0, "pattern": "(\\d{4}-\\d{2}-\\d{2})(T\\d{2}:\\d{2}:\\d{2}Z)?"}, "ZonedDateTime": {"type": "string", "documentation": "<p>The period of time that you want the usage and costs for.</p>", "max": 25, "min": 20, "pattern": "^\\d{4}-\\d\\d-\\d\\dT\\d\\d:\\d\\d:\\d\\d(([+-]\\d\\d:\\d\\d)|Z)$"}}, "documentation": "<p>You can use the Cost Explorer API to programmatically query your cost and usage data. You can query for aggregated data such as total monthly costs or total daily usage. You can also query for granular data. This might include the number of daily write operations for Amazon DynamoDB database tables in your production environment. </p> <p>Service Endpoint</p> <p>The Cost Explorer API provides the following endpoint:</p> <ul> <li> <p> <code>https://ce.us-east-1.amazonaws.com</code> </p> </li> </ul> <p>For information about the costs that are associated with the Cost Explorer API, see <a href=\"http://aws.amazon.com/aws-cost-management/pricing/\">Amazon Web Services Cost Management Pricing</a>.</p>"}