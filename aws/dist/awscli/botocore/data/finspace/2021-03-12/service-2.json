{"version": "2.0", "metadata": {"apiVersion": "2021-03-12", "endpointPrefix": "finspace", "jsonVersion": "1.1", "protocol": "rest-json", "protocols": ["rest-json"], "serviceAbbreviation": "finspace", "serviceFullName": "FinSpace User Environment Management service", "serviceId": "finspace", "signatureVersion": "v4", "signingName": "finspace", "uid": "finspace-2021-03-12", "auth": ["aws.auth#sigv4"]}, "operations": {"CreateEnvironment": {"name": "CreateEnvironment", "http": {"method": "POST", "requestUri": "/environment"}, "input": {"shape": "CreateEnvironmentRequest"}, "output": {"shape": "CreateEnvironmentResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Create a new FinSpace environment.</p>", "deprecated": true, "deprecatedMessage": "This method will be discontinued."}, "CreateKxChangeset": {"name": "CreateKxChangeset", "http": {"method": "POST", "requestUri": "/kx/environments/{environmentId}/databases/{databaseName}/changesets"}, "input": {"shape": "CreateKxChangesetRequest"}, "output": {"shape": "CreateKxChangesetResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "LimitExceededException"}], "documentation": "<p> Creates a changeset for a kdb database. A changeset allows you to add and delete existing files by using an ordered list of change requests. </p>"}, "CreateKxCluster": {"name": "CreateKxCluster", "http": {"method": "POST", "requestUri": "/kx/environments/{environmentId}/clusters"}, "input": {"shape": "CreateKxClusterRequest"}, "output": {"shape": "CreateKxClusterResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "LimitExceededException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Creates a new kdb cluster.</p>"}, "CreateKxDatabase": {"name": "CreateKxDatabase", "http": {"method": "POST", "requestUri": "/kx/environments/{environmentId}/databases"}, "input": {"shape": "CreateKxDatabaseRequest"}, "output": {"shape": "CreateKxDatabaseResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "ResourceNotFoundException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Creates a new kdb database in the environment.</p>"}, "CreateKxDataview": {"name": "CreateKxDataview", "http": {"method": "POST", "requestUri": "/kx/environments/{environmentId}/databases/{databaseName}/dataviews"}, "input": {"shape": "CreateKxDataviewRequest"}, "output": {"shape": "CreateKxDataviewResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "LimitExceededException"}, {"shape": "ResourceAlreadyExistsException"}], "documentation": "<p> Creates a snapshot of kdb database with tiered storage capabilities and a pre-warmed cache, ready for mounting on kdb clusters. Dataviews are only available for clusters running on a scaling group. They are not supported on dedicated clusters. </p>"}, "CreateKxEnvironment": {"name": "CreateKxEnvironment", "http": {"method": "POST", "requestUri": "/kx/environments"}, "input": {"shape": "CreateKxEnvironmentRequest"}, "output": {"shape": "CreateKxEnvironmentResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "LimitExceededException"}, {"shape": "ConflictException"}], "documentation": "<p>Creates a managed kdb environment for the account.</p>"}, "CreateKxScalingGroup": {"name": "CreateKxScalingGroup", "http": {"method": "POST", "requestUri": "/kx/environments/{environmentId}/scalingGroups"}, "input": {"shape": "CreateKxScalingGroupRequest"}, "output": {"shape": "CreateKxScalingGroupResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "LimitExceededException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Creates a new scaling group. </p>"}, "CreateKxUser": {"name": "CreateKxUser", "http": {"method": "POST", "requestUri": "/kx/environments/{environmentId}/users"}, "input": {"shape": "CreateKxUserRequest"}, "output": {"shape": "CreateKxUserResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "LimitExceededException"}, {"shape": "ConflictException"}], "documentation": "<p>Creates a user in FinSpace kdb environment with an associated IAM role.</p>"}, "CreateKxVolume": {"name": "CreateKxVolume", "http": {"method": "POST", "requestUri": "/kx/environments/{environmentId}/kxvolumes"}, "input": {"shape": "CreateKxVolumeRequest"}, "output": {"shape": "CreateKxVolumeResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "LimitExceededException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Creates a new volume with a specific amount of throughput and storage capacity. </p>"}, "DeleteEnvironment": {"name": "DeleteEnvironment", "http": {"method": "DELETE", "requestUri": "/environment/{environmentId}"}, "input": {"shape": "DeleteEnvironmentRequest"}, "output": {"shape": "DeleteEnvironmentResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Delete an FinSpace environment.</p>", "deprecated": true, "deprecatedMessage": "This method will be discontinued."}, "DeleteKxCluster": {"name": "DeleteKxCluster", "http": {"method": "DELETE", "requestUri": "/kx/environments/{environmentId}/clusters/{clusterName}"}, "input": {"shape": "DeleteKxClusterRequest"}, "output": {"shape": "DeleteKxClusterResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "LimitExceededException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Deletes a kdb cluster.</p>"}, "DeleteKxClusterNode": {"name": "DeleteKxClusterNode", "http": {"method": "DELETE", "requestUri": "/kx/environments/{environmentId}/clusters/{clusterName}/nodes/{nodeId}"}, "input": {"shape": "DeleteKxClusterNodeRequest"}, "output": {"shape": "DeleteKxClusterNodeResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Deletes the specified nodes from a cluster. </p>"}, "DeleteKxDatabase": {"name": "DeleteKxDatabase", "http": {"method": "DELETE", "requestUri": "/kx/environments/{environmentId}/databases/{databaseName}"}, "input": {"shape": "DeleteKxDatabaseRequest"}, "output": {"shape": "DeleteKxDatabaseResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}], "documentation": "<p>Deletes the specified database and all of its associated data. This action is irreversible. You must copy any data out of the database before deleting it if the data is to be retained.</p>"}, "DeleteKxDataview": {"name": "DeleteKxDataview", "http": {"method": "DELETE", "requestUri": "/kx/environments/{environmentId}/databases/{databaseName}/dataviews/{dataviewName}"}, "input": {"shape": "DeleteKxDataviewRequest"}, "output": {"shape": "DeleteKxDataviewResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}], "documentation": "<p> Deletes the specified dataview. Before deleting a dataview, make sure that it is not in use by any cluster. </p>"}, "DeleteKxEnvironment": {"name": "DeleteKxEnvironment", "http": {"method": "DELETE", "requestUri": "/kx/environments/{environmentId}"}, "input": {"shape": "DeleteKxEnvironmentRequest"}, "output": {"shape": "DeleteKxEnvironmentResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Deletes the kdb environment. This action is irreversible. Deleting a kdb environment will remove all the associated data and any services running in it. </p>"}, "DeleteKxScalingGroup": {"name": "DeleteKxScalingGroup", "http": {"method": "DELETE", "requestUri": "/kx/environments/{environmentId}/scalingGroups/{scalingGroupName}"}, "input": {"shape": "DeleteKxScalingGroupRequest"}, "output": {"shape": "DeleteKxScalingGroupResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "LimitExceededException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p> Deletes the specified scaling group. This action is irreversible. You cannot delete a scaling group until all the clusters running on it have been deleted.</p>"}, "DeleteKxUser": {"name": "DeleteKxUser", "http": {"method": "DELETE", "requestUri": "/kx/environments/{environmentId}/users/{userName}"}, "input": {"shape": "DeleteKxUserRequest"}, "output": {"shape": "DeleteKxUserResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Deletes a user in the specified kdb environment.</p>"}, "DeleteKxVolume": {"name": "DeleteKxVolume", "http": {"method": "DELETE", "requestUri": "/kx/environments/{environmentId}/kxvolumes/{volumeName}"}, "input": {"shape": "DeleteKxVolumeRequest"}, "output": {"shape": "DeleteKxVolumeResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "LimitExceededException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p> Deletes a volume. You can only delete a volume if it's not attached to a cluster or a dataview. When a volume is deleted, any data on the volume is lost. This action is irreversible. </p>"}, "GetEnvironment": {"name": "GetEnvironment", "http": {"method": "GET", "requestUri": "/environment/{environmentId}"}, "input": {"shape": "GetEnvironmentRequest"}, "output": {"shape": "GetEnvironmentResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Returns the FinSpace environment object.</p>", "deprecated": true, "deprecatedMessage": "This method will be discontinued."}, "GetKxChangeset": {"name": "GetKxChangeset", "http": {"method": "GET", "requestUri": "/kx/environments/{environmentId}/databases/{databaseName}/changesets/{changesetId}"}, "input": {"shape": "GetKxChangesetRequest"}, "output": {"shape": "GetKxChangesetResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns information about a kdb changeset.</p>"}, "GetKxCluster": {"name": "GetKxCluster", "http": {"method": "GET", "requestUri": "/kx/environments/{environmentId}/clusters/{clusterName}"}, "input": {"shape": "GetKxClusterRequest"}, "output": {"shape": "GetKxClusterResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "LimitExceededException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves information about a kdb cluster.</p>"}, "GetKxConnectionString": {"name": "GetKxConnectionString", "http": {"method": "GET", "requestUri": "/kx/environments/{environmentId}/connectionString"}, "input": {"shape": "GetKxConnectionStringRequest"}, "output": {"shape": "GetKxConnectionStringResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}], "documentation": "<p>Retrieves a connection string for a user to connect to a kdb cluster. You must call this API using the same role that you have defined while creating a user. </p>"}, "GetKxDatabase": {"name": "GetKxDatabase", "http": {"method": "GET", "requestUri": "/kx/environments/{environmentId}/databases/{databaseName}"}, "input": {"shape": "GetKxDatabaseRequest"}, "output": {"shape": "GetKxDatabaseResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns database information for the specified environment ID.</p>"}, "GetKxDataview": {"name": "GetKxDataview", "http": {"method": "GET", "requestUri": "/kx/environments/{environmentId}/databases/{databaseName}/dataviews/{dataviewName}"}, "input": {"shape": "GetKxDataviewRequest"}, "output": {"shape": "GetKxDataviewResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p> Retrieves details of the dataview. </p>"}, "GetKxEnvironment": {"name": "GetKxEnvironment", "http": {"method": "GET", "requestUri": "/kx/environments/{environmentId}"}, "input": {"shape": "GetKxEnvironmentRequest"}, "output": {"shape": "GetKxEnvironmentResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}], "documentation": "<p>Retrieves all the information for the specified kdb environment.</p>"}, "GetKxScalingGroup": {"name": "GetKxScalingGroup", "http": {"method": "GET", "requestUri": "/kx/environments/{environmentId}/scalingGroups/{scalingGroupName}"}, "input": {"shape": "GetKxScalingGroupRequest"}, "output": {"shape": "GetKxScalingGroupResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "LimitExceededException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p> Retrieves details of a scaling group.</p>"}, "GetKxUser": {"name": "GetKxUser", "http": {"method": "GET", "requestUri": "/kx/environments/{environmentId}/users/{userName}"}, "input": {"shape": "GetKxUserRequest"}, "output": {"shape": "GetKxUserResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}], "documentation": "<p>Retrieves information about the specified kdb user.</p>"}, "GetKxVolume": {"name": "GetKxVolume", "http": {"method": "GET", "requestUri": "/kx/environments/{environmentId}/kxvolumes/{volumeName}"}, "input": {"shape": "GetKxVolumeRequest"}, "output": {"shape": "GetKxVolumeResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "LimitExceededException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p> Retrieves the information about the volume. </p>"}, "ListEnvironments": {"name": "ListEnvironments", "http": {"method": "GET", "requestUri": "/environment"}, "input": {"shape": "ListEnvironmentsRequest"}, "output": {"shape": "ListEnvironmentsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>A list of all of your FinSpace environments.</p>", "deprecated": true, "deprecatedMessage": "This method will be discontinued."}, "ListKxChangesets": {"name": "ListKxChangesets", "http": {"method": "GET", "requestUri": "/kx/environments/{environmentId}/databases/{databaseName}/changesets"}, "input": {"shape": "ListKxChangesetsRequest"}, "output": {"shape": "ListKxChangesetsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns a list of all the changesets for a database.</p>"}, "ListKxClusterNodes": {"name": "ListKxClusterNodes", "http": {"method": "GET", "requestUri": "/kx/environments/{environmentId}/clusters/{clusterName}/nodes"}, "input": {"shape": "ListKxClusterNodesRequest"}, "output": {"shape": "ListKxClusterNodesResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "LimitExceededException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Lists all the nodes in a kdb cluster.</p>"}, "ListKxClusters": {"name": "ListKxClusters", "http": {"method": "GET", "requestUri": "/kx/environments/{environmentId}/clusters"}, "input": {"shape": "ListKxClustersRequest"}, "output": {"shape": "ListKxClustersResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "LimitExceededException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns a list of clusters.</p>"}, "ListKxDatabases": {"name": "ListKxDatabases", "http": {"method": "GET", "requestUri": "/kx/environments/{environmentId}/databases"}, "input": {"shape": "ListKxDatabasesRequest"}, "output": {"shape": "ListKxDatabasesResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns a list of all the databases in the kdb environment.</p>"}, "ListKxDataviews": {"name": "ListKxDataviews", "http": {"method": "GET", "requestUri": "/kx/environments/{environmentId}/databases/{databaseName}/dataviews"}, "input": {"shape": "ListKxDataviewsRequest"}, "output": {"shape": "ListKxDataviewsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p> Returns a list of all the dataviews in the database.</p>"}, "ListKxEnvironments": {"name": "ListKxEnvironments", "http": {"method": "GET", "requestUri": "/kx/environments"}, "input": {"shape": "ListKxEnvironmentsRequest"}, "output": {"shape": "ListKxEnvironmentsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Returns a list of kdb environments created in an account.</p>"}, "ListKxScalingGroups": {"name": "ListKxScalingGroups", "http": {"method": "GET", "requestUri": "/kx/environments/{environmentId}/scalingGroups"}, "input": {"shape": "ListKxScalingGroupsRequest"}, "output": {"shape": "ListKxScalingGroupsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "LimitExceededException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p> Returns a list of scaling groups in a kdb environment.</p>"}, "ListKxUsers": {"name": "ListKxUsers", "http": {"method": "GET", "requestUri": "/kx/environments/{environmentId}/users"}, "input": {"shape": "ListKxUsersRequest"}, "output": {"shape": "ListKxUsersResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists all the users in a kdb environment.</p>"}, "ListKxVolumes": {"name": "ListKxVolumes", "http": {"method": "GET", "requestUri": "/kx/environments/{environmentId}/kxvolumes"}, "input": {"shape": "ListKxVolumesRequest"}, "output": {"shape": "ListKxVolumesResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "LimitExceededException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p> Lists all the volumes in a kdb environment. </p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags/{resourceArn}"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>A list of all tags for a resource.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags/{resourceArn}"}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Adds metadata tags to a FinSpace resource.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags/{resourceArn}"}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Removes metadata tags from a FinSpace resource.</p>"}, "UpdateEnvironment": {"name": "UpdateEnvironment", "http": {"method": "PUT", "requestUri": "/environment/{environmentId}"}, "input": {"shape": "UpdateEnvironmentRequest"}, "output": {"shape": "UpdateEnvironmentResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Update your FinSpace environment.</p>", "deprecated": true, "deprecatedMessage": "This method will be discontinued."}, "UpdateKxClusterCodeConfiguration": {"name": "UpdateKxClusterCodeConfiguration", "http": {"method": "PUT", "requestUri": "/kx/environments/{environmentId}/clusters/{clusterName}/configuration/code"}, "input": {"shape": "UpdateKxClusterCodeConfigurationRequest"}, "output": {"shape": "UpdateKxClusterCodeConfigurationResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "LimitExceededException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p> Allows you to update code configuration on a running cluster. By using this API you can update the code, the initialization script path, and the command line arguments for a specific cluster. The configuration that you want to update will override any existing configurations on the cluster. </p>"}, "UpdateKxClusterDatabases": {"name": "UpdateKxClusterDatabases", "http": {"method": "PUT", "requestUri": "/kx/environments/{environmentId}/clusters/{clusterName}/configuration/databases"}, "input": {"shape": "UpdateKxClusterDatabasesRequest"}, "output": {"shape": "UpdateKxClusterDatabasesResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "LimitExceededException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Updates the databases mounted on a kdb cluster, which includes the <code>changesetId</code> and all the dbPaths to be cached. This API does not allow you to change a database name or add a database if you created a cluster without one. </p> <p>Using this API you can point a cluster to a different changeset and modify a list of partitions being cached.</p>"}, "UpdateKxDatabase": {"name": "UpdateKxDatabase", "http": {"method": "PUT", "requestUri": "/kx/environments/{environmentId}/databases/{databaseName}"}, "input": {"shape": "UpdateKxDatabaseRequest"}, "output": {"shape": "UpdateKxDatabaseResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}], "documentation": "<p>Updates information for the given kdb database.</p>"}, "UpdateKxDataview": {"name": "UpdateKxDataview", "http": {"method": "PUT", "requestUri": "/kx/environments/{environmentId}/databases/{databaseName}/dataviews/{dataviewName}"}, "input": {"shape": "UpdateKxDataviewRequest"}, "output": {"shape": "UpdateKxDataviewResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ResourceAlreadyExistsException"}], "documentation": "<p> Updates the specified dataview. The dataviews get automatically updated when any new changesets are ingested. Each update of the dataview creates a new version, including changeset details and cache configurations</p>"}, "UpdateKxEnvironment": {"name": "UpdateKxEnvironment", "http": {"method": "PUT", "requestUri": "/kx/environments/{environmentId}"}, "input": {"shape": "UpdateKxEnvironmentRequest"}, "output": {"shape": "UpdateKxEnvironmentResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Updates information for the given kdb environment.</p>"}, "UpdateKxEnvironmentNetwork": {"name": "UpdateKxEnvironmentNetwork", "http": {"method": "PUT", "requestUri": "/kx/environments/{environmentId}/network"}, "input": {"shape": "UpdateKxEnvironmentNetworkRequest"}, "output": {"shape": "UpdateKxEnvironmentNetworkResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Updates environment network to connect to your internal network by using a transit gateway. This API supports request to create a transit gateway attachment from FinSpace VPC to your transit gateway ID and create a custom Route-53 outbound resolvers.</p> <p>Once you send a request to update a network, you cannot change it again. Network update might require termination of any clusters that are running in the existing network.</p>"}, "UpdateKxUser": {"name": "UpdateKxUser", "http": {"method": "PUT", "requestUri": "/kx/environments/{environmentId}/users/{userName}"}, "input": {"shape": "UpdateKxUserRequest"}, "output": {"shape": "UpdateKxUserResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "LimitExceededException"}, {"shape": "ConflictException"}], "documentation": "<p>Updates the user details. You can only update the IAM role associated with a user.</p>"}, "UpdateKxVolume": {"name": "UpdateKxVolume", "http": {"method": "PATCH", "requestUri": "/kx/environments/{environmentId}/kxvolumes/{volumeName}"}, "input": {"shape": "UpdateKxVolumeRequest"}, "output": {"shape": "UpdateKxVolumeResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "LimitExceededException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p> Updates the throughput or capacity of a volume. During the update process, the filesystem might be unavailable for a few minutes. You can retry any operations after the update is complete. </p>"}}, "shapes": {"AccessDeniedException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}}, "documentation": "<p>You do not have sufficient access to perform this action.</p>", "error": {"httpStatusCode": 403}, "exception": true}, "AttachedClusterList": {"type": "list", "member": {"shape": "KxClusterName"}}, "AttributeMap": {"type": "map", "key": {"shape": "FederationAttributeKey"}, "value": {"shape": "FederationAttributeValue"}}, "AutoScalingConfiguration": {"type": "structure", "members": {"minNodeCount": {"shape": "NodeCount", "documentation": "<p>The lowest number of nodes to scale. This value must be at least 1 and less than the <code>maxNodeCount</code>. If the nodes in a cluster belong to multiple availability zones, then <code>minNodeCount</code> must be at least 3.</p>"}, "maxNodeCount": {"shape": "NodeCount", "documentation": "<p>The highest number of nodes to scale. This value cannot be greater than 5.</p>"}, "autoScalingMetric": {"shape": "AutoScalingMetric", "documentation": "<p> The metric your cluster will track in order to scale in and out. For example, <code>CPU_UTILIZATION_PERCENTAGE</code> is the average CPU usage across all the nodes in a cluster.</p>"}, "metricTarget": {"shape": "AutoScalingMetricTarget", "documentation": "<p>The desired value of the chosen <code>autoScalingMetric</code>. When the metric drops below this value, the cluster will scale in. When the metric goes above this value, the cluster will scale out. You can set the target value between 1 and 100 percent.</p>"}, "scaleInCooldownSeconds": {"shape": "CooldownTime", "documentation": "<p>The duration in seconds that FinSpace will wait after a scale in event before initiating another scaling event.</p>"}, "scaleOutCooldownSeconds": {"shape": "CooldownTime", "documentation": "<p>The duration in seconds that FinSpace will wait after a scale out event before initiating another scaling event.</p>"}}, "documentation": "<p>The configuration based on which FinSpace will scale in or scale out nodes in your cluster. </p>"}, "AutoScalingMetric": {"type": "string", "enum": ["CPU_UTILIZATION_PERCENTAGE"]}, "AutoScalingMetricTarget": {"type": "double", "max": 100, "min": 1}, "AvailabilityZoneId": {"type": "string", "max": 12, "min": 8, "pattern": "^[a-zA-Z0-9-]+$"}, "AvailabilityZoneIds": {"type": "list", "member": {"shape": "AvailabilityZoneId"}}, "BoxedInteger": {"type": "integer", "box": true}, "CapacityConfiguration": {"type": "structure", "members": {"nodeType": {"shape": "NodeType", "documentation": "<p>The type that determines the hardware of the host computer used for your cluster instance. Each node type offers different memory and storage capabilities. Choose a node type based on the requirements of the application or software that you plan to run on your instance.</p> <p>You can only specify one of the following values:</p> <ul> <li> <p> <code>kx.s.large</code> – The node type with a configuration of 12 GiB memory and 2 vCPUs.</p> </li> <li> <p> <code>kx.s.xlarge</code> – The node type with a configuration of 27 GiB memory and 4 vCPUs.</p> </li> <li> <p> <code>kx.s.2xlarge</code> – The node type with a configuration of 54 GiB memory and 8 vCPUs.</p> </li> <li> <p> <code>kx.s.4xlarge</code> – The node type with a configuration of 108 GiB memory and 16 vCPUs.</p> </li> <li> <p> <code>kx.s.8xlarge</code> – The node type with a configuration of 216 GiB memory and 32 vCPUs.</p> </li> <li> <p> <code>kx.s.16xlarge</code> – The node type with a configuration of 432 GiB memory and 64 vCPUs.</p> </li> <li> <p> <code>kx.s.32xlarge</code> – The node type with a configuration of 864 GiB memory and 128 vCPUs.</p> </li> </ul>"}, "nodeCount": {"shape": "NodeCount", "documentation": "<p>The number of instances running in a cluster.</p>"}}, "documentation": "<p>A structure for the metadata of a cluster. It includes information like the CPUs needed, memory of instances, and number of instances.</p>"}, "ChangeRequest": {"type": "structure", "required": ["changeType", "db<PERSON><PERSON>"], "members": {"changeType": {"shape": "ChangeType", "documentation": "<p>Defines the type of change request. A <code>changeType</code> can have the following values:</p> <ul> <li> <p>PUT – Adds or updates files in a database.</p> </li> <li> <p>DELETE – Deletes files in a database.</p> </li> </ul>"}, "s3Path": {"shape": "S3Path", "documentation": "<p>Defines the S3 path of the source file that is required to add or update files in a database.</p>"}, "dbPath": {"shape": "DbPath", "documentation": "<p>Defines the path within the database directory. </p>"}}, "documentation": "<p>A list of change request objects.</p>"}, "ChangeRequests": {"type": "list", "member": {"shape": "ChangeRequest"}, "max": 32, "min": 1}, "ChangeType": {"type": "string", "enum": ["PUT", "DELETE"]}, "ChangesetId": {"type": "string", "max": 26, "min": 1, "pattern": "^[a-zA-Z0-9]+$"}, "ChangesetStatus": {"type": "string", "enum": ["PENDING", "PROCESSING", "FAILED", "COMPLETED"]}, "ClientToken": {"type": "string", "max": 36, "min": 1, "pattern": ".*\\S.*"}, "ClientTokenString": {"type": "string", "max": 64, "min": 1, "pattern": "^[a-zA-Z0-9-]+$"}, "ClusterNodeCount": {"type": "integer", "min": 1}, "CodeConfiguration": {"type": "structure", "members": {"s3Bucket": {"shape": "S3Bucket", "documentation": "<p>A unique name for the S3 bucket.</p>"}, "s3Key": {"shape": "S3Key", "documentation": "<p>The full S3 path (excluding bucket) to the .zip file. This file contains the code that is loaded onto the cluster when it's started.</p>"}, "s3ObjectVersion": {"shape": "S3ObjectVersion", "documentation": "<p>The version of an S3 object.</p>"}}, "documentation": "<p>The structure of the customer code available within the running cluster.</p>"}, "ConflictException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}, "reason": {"shape": "errorMessage", "documentation": "<p>The reason for the conflict exception.</p>"}}, "documentation": "<p>There was a conflict with this action, and it could not be completed.</p>", "error": {"httpStatusCode": 409}, "exception": true}, "CooldownTime": {"type": "double", "max": 100000, "min": 0}, "CpuCount": {"type": "double", "min": 0.1}, "CreateEnvironmentRequest": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "EnvironmentName", "documentation": "<p>The name of the FinSpace environment to be created.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description of the FinSpace environment to be created.</p>"}, "kmsKeyId": {"shape": "KmsKeyId", "documentation": "<p>The KMS key id to encrypt your data in the FinSpace environment.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>Add tags to your FinSpace environment.</p>"}, "federationMode": {"shape": "FederationMode", "documentation": "<p>Authentication mode for the environment.</p> <ul> <li> <p> <code>FEDERATED</code> - Users access FinSpace through Single Sign On (SSO) via your Identity provider.</p> </li> <li> <p> <code>LOCAL</code> - Users access FinSpace via email and password managed within the FinSpace environment.</p> </li> </ul>"}, "federationParameters": {"shape": "FederationParameters", "documentation": "<p>Configuration information when authentication mode is FEDERATED.</p>"}, "superuserParameters": {"shape": "SuperuserParameters", "documentation": "<p>Configuration information for the superuser.</p>"}, "dataBundles": {"shape": "DataBundleArns", "documentation": "<p>The list of Amazon Resource Names (ARN) of the data bundles to install. Currently supported data bundle ARNs:</p> <ul> <li> <p> <code>arn:aws:finspace:${Region}::data-bundle/capital-markets-sample</code> - Contains sample Capital Markets datasets, categories and controlled vocabularies.</p> </li> <li> <p> <code>arn:aws:finspace:${Region}::data-bundle/taq</code> (default) - Contains trades and quotes data in addition to sample Capital Markets data.</p> </li> </ul>"}}}, "CreateEnvironmentResponse": {"type": "structure", "members": {"environmentId": {"shape": "IdType", "documentation": "<p>The unique identifier for FinSpace environment that you created.</p>"}, "environmentArn": {"shape": "EnvironmentArn", "documentation": "<p>The Amazon Resource Name (ARN) of the FinSpace environment that you created.</p>"}, "environmentUrl": {"shape": "url", "documentation": "<p>The sign-in URL for the web application of the FinSpace environment you created.</p>"}}}, "CreateKxChangesetRequest": {"type": "structure", "required": ["environmentId", "databaseName", "changeRequests", "clientToken"], "members": {"environmentId": {"shape": "EnvironmentId", "documentation": "<p>A unique identifier of the kdb environment.</p>", "location": "uri", "locationName": "environmentId"}, "databaseName": {"shape": "DatabaseName", "documentation": "<p>The name of the kdb database.</p>", "location": "uri", "locationName": "databaseName"}, "changeRequests": {"shape": "ChangeRequests", "documentation": "<p>A list of change request objects that are run in order. A change request object consists of <code>changeType</code> , <code>s3Path</code>, and <code>dbPath</code>. A changeType can have the following values: </p> <ul> <li> <p>PUT – Adds or updates files in a database.</p> </li> <li> <p>DELETE – Deletes files in a database.</p> </li> </ul> <p>All the change requests require a mandatory <code>dbPath</code> attribute that defines the path within the database directory. All database paths must start with a leading / and end with a trailing /. The <code>s3Path</code> attribute defines the s3 source file path and is required for a PUT change type. The <code>s3path</code> must end with a trailing / if it is a directory and must end without a trailing / if it is a file. </p> <p>Here are few examples of how you can use the change request object:</p> <ol> <li> <p>This request adds a single sym file at database root location. </p> <p> <code>{ \"changeType\": \"PUT\", \"s3Path\":\"s3://bucket/db/sym\", \"dbPath\":\"/\"}</code> </p> </li> <li> <p>This request adds files in the given <code>s3Path</code> under the 2020.01.02 partition of the database.</p> <p> <code>{ \"changeType\": \"PUT\", \"s3Path\":\"s3://bucket/db/2020.01.02/\", \"dbPath\":\"/2020.01.02/\"}</code> </p> </li> <li> <p>This request adds files in the given <code>s3Path</code> under the <i>taq</i> table partition of the database.</p> <p> <code>[ { \"changeType\": \"PUT\", \"s3Path\":\"s3://bucket/db/2020.01.02/taq/\", \"dbPath\":\"/2020.01.02/taq/\"}]</code> </p> </li> <li> <p>This request deletes the 2020.01.02 partition of the database.</p> <p> <code>[{ \"changeType\": \"DELETE\", \"dbPath\": \"/2020.01.02/\"} ]</code> </p> </li> <li> <p>The <i>DELETE</i> request allows you to delete the existing files under the 2020.01.02 partition of the database, and the <i>PUT</i> request adds a new taq table under it.</p> <p> <code>[ {\"changeType\": \"DELETE\", \"dbPath\":\"/2020.01.02/\"}, {\"changeType\": \"PUT\", \"s3Path\":\"s3://bucket/db/2020.01.02/taq/\", \"dbPath\":\"/2020.01.02/taq/\"}]</code> </p> </li> </ol>"}, "clientToken": {"shape": "ClientTokenString", "documentation": "<p>A token that ensures idempotency. This token expires in 10 minutes.</p>", "idempotencyToken": true}}}, "CreateKxChangesetResponse": {"type": "structure", "members": {"changesetId": {"shape": "ChangesetId", "documentation": "<p>A unique identifier for the changeset.</p>"}, "databaseName": {"shape": "DatabaseName", "documentation": "<p>The name of the kdb database.</p>"}, "environmentId": {"shape": "EnvironmentId", "documentation": "<p>A unique identifier for the kdb environment.</p>"}, "changeRequests": {"shape": "ChangeRequests", "documentation": "<p>A list of change requests.</p>"}, "createdTimestamp": {"shape": "Timestamp", "documentation": "<p>The timestamp at which the changeset was created in FinSpace. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as *************.</p>"}, "lastModifiedTimestamp": {"shape": "Timestamp", "documentation": "<p>The timestamp at which the changeset was updated in FinSpace. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as *************.</p>"}, "status": {"shape": "ChangesetStatus", "documentation": "<p>Status of the changeset creation process.</p> <ul> <li> <p>Pending – Changeset creation is pending.</p> </li> <li> <p>Processing – Changeset creation is running.</p> </li> <li> <p>Failed – Changeset creation has failed.</p> </li> <li> <p>Complete – Changeset creation has succeeded.</p> </li> </ul>"}, "errorInfo": {"shape": "ErrorInfo", "documentation": "<p>The details of the error that you receive when creating a changeset. It consists of the type of error and the error message.</p>"}}}, "CreateKxClusterRequest": {"type": "structure", "required": ["environmentId", "clusterName", "clusterType", "releaseLabel", "vpcConfiguration", "azMode"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>A token that ensures idempotency. This token expires in 10 minutes.</p>", "idempotencyToken": true}, "environmentId": {"shape": "KxEnvironmentId", "documentation": "<p>A unique identifier for the kdb environment.</p>", "location": "uri", "locationName": "environmentId"}, "clusterName": {"shape": "KxClusterName", "documentation": "<p>A unique name for the cluster that you want to create.</p>"}, "clusterType": {"shape": "KxClusterType", "documentation": "<p>Specifies the type of KDB database that is being created. The following types are available: </p> <ul> <li> <p>HDB – A Historical Database. The data is only accessible with read-only permissions from one of the FinSpace managed kdb databases mounted to the cluster.</p> </li> <li> <p>RDB – A Realtime Database. This type of database captures all the data from a ticker plant and stores it in memory until the end of day, after which it writes all of its data to a disk and reloads the HDB. This cluster type requires local storage for temporary storage of data during the savedown process. If you specify this field in your request, you must provide the <code>savedownStorageConfiguration</code> parameter.</p> </li> <li> <p>GATEWAY – A gateway cluster allows you to access data across processes in kdb systems. It allows you to create your own routing logic using the initialization scripts and custom code. This type of cluster does not require a writable local storage.</p> </li> <li> <p>GP – A general purpose cluster allows you to quickly iterate on code during development by granting greater access to system commands and enabling a fast reload of custom code. This cluster type can optionally mount databases including cache and savedown storage. For this cluster type, the node count is fixed at 1. It does not support autoscaling and supports only <code>SINGLE</code> AZ mode.</p> </li> <li> <p>Tickerplant – A tickerplant cluster allows you to subscribe to feed handlers based on IAM permissions. It can publish to RDBs, other Tickerplants, and real-time subscribers (RTS). Tickerplants can persist messages to log, which is readable by any RDB environment. It supports only single-node that is only one kdb process.</p> </li> </ul>"}, "tickerplantLogConfiguration": {"shape": "TickerplantLogConfiguration", "documentation": "<p> A configuration to store Tickerplant logs. It consists of a list of volumes that will be mounted to your cluster. For the cluster type <code>Tickerplant</code>, the location of the TP volume on the cluster will be available by using the global variable <code>.aws.tp_log_path</code>. </p>"}, "databases": {"shape": "KxDatabaseConfigurations", "documentation": "<p>A list of databases that will be available for querying.</p>"}, "cacheStorageConfigurations": {"shape": "KxCacheStorageConfigurations", "documentation": "<p>The configurations for a read only cache storage associated with a cluster. This cache will be stored as an FSx Lustre that reads from the S3 store. </p>"}, "autoScalingConfiguration": {"shape": "AutoScalingConfiguration", "documentation": "<p>The configuration based on which FinSpace will scale in or scale out nodes in your cluster.</p>"}, "clusterDescription": {"shape": "KxClusterDescription", "documentation": "<p>A description of the cluster.</p>"}, "capacityConfiguration": {"shape": "CapacityConfiguration", "documentation": "<p>A structure for the metadata of a cluster. It includes information like the CPUs needed, memory of instances, and number of instances.</p>"}, "releaseLabel": {"shape": "ReleaseLabel", "documentation": "<p>The version of FinSpace managed kdb to run.</p>"}, "vpcConfiguration": {"shape": "VpcConfiguration", "documentation": "<p>Configuration details about the network where the Privatelink endpoint of the cluster resides.</p>"}, "initializationScript": {"shape": "InitializationScriptFilePath", "documentation": "<p>Specifies a Q program that will be run at launch of a cluster. It is a relative path within <i>.zip</i> file that contains the custom code, which will be loaded on the cluster. It must include the file name itself. For example, <code>somedir/init.q</code>.</p>"}, "commandLineArguments": {"shape": "KxCommandLineArguments", "documentation": "<p>Defines the key-value pairs to make them available inside the cluster.</p>"}, "code": {"shape": "CodeConfiguration", "documentation": "<p>The details of the custom code that you want to use inside a cluster when analyzing a data. It consists of the S3 source bucket, location, S3 object version, and the relative path from where the custom code is loaded into the cluster. </p>"}, "executionRole": {"shape": "ExecutionRoleArn", "documentation": "<p>An IAM role that defines a set of permissions associated with a cluster. These permissions are assumed when a cluster attempts to access another cluster.</p>"}, "savedownStorageConfiguration": {"shape": "KxSavedownStorageConfiguration", "documentation": "<p>The size and type of the temporary storage that is used to hold data during the savedown process. This parameter is required when you choose <code>clusterType</code> as RDB. All the data written to this storage space is lost when the cluster node is restarted.</p>"}, "azMode": {"shape": "KxAzMode", "documentation": "<p>The number of availability zones you want to assign per cluster. This can be one of the following </p> <ul> <li> <p> <code>SINGLE</code> – Assigns one availability zone per cluster.</p> </li> <li> <p> <code>MULTI</code> – Assigns all the availability zones per cluster.</p> </li> </ul>"}, "availabilityZoneId": {"shape": "AvailabilityZoneId", "documentation": "<p>The availability zone identifiers for the requested regions.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>A list of key-value pairs to label the cluster. You can add up to 50 tags to a cluster.</p>"}, "scalingGroupConfiguration": {"shape": "KxScalingGroupConfiguration", "documentation": "<p>The structure that stores the configuration details of a scaling group.</p>"}}}, "CreateKxClusterResponse": {"type": "structure", "members": {"environmentId": {"shape": "KxEnvironmentId", "documentation": "<p>A unique identifier for the kdb environment.</p>"}, "status": {"shape": "KxClusterStatus", "documentation": "<p>The status of cluster creation.</p> <ul> <li> <p>PENDING – The cluster is pending creation.</p> </li> <li> <p>CREATING – The cluster creation process is in progress.</p> </li> <li> <p>CREATE_FAILED – The cluster creation process has failed.</p> </li> <li> <p>RUNNING – The cluster creation process is running.</p> </li> <li> <p>UPDATING – The cluster is in the process of being updated.</p> </li> <li> <p>DELETING – The cluster is in the process of being deleted.</p> </li> <li> <p>DELETED – The cluster has been deleted.</p> </li> <li> <p>DELETE_FAILED – The cluster failed to delete.</p> </li> </ul>"}, "statusReason": {"shape": "KxClusterStatusReason", "documentation": "<p>The error message when a failed state occurs. </p>"}, "clusterName": {"shape": "KxClusterName", "documentation": "<p>A unique name for the cluster.</p>"}, "clusterType": {"shape": "KxClusterType", "documentation": "<p>Specifies the type of KDB database that is being created. The following types are available: </p> <ul> <li> <p>HDB – A Historical Database. The data is only accessible with read-only permissions from one of the FinSpace managed kdb databases mounted to the cluster.</p> </li> <li> <p>RDB – A Realtime Database. This type of database captures all the data from a ticker plant and stores it in memory until the end of day, after which it writes all of its data to a disk and reloads the HDB. This cluster type requires local storage for temporary storage of data during the savedown process. If you specify this field in your request, you must provide the <code>savedownStorageConfiguration</code> parameter.</p> </li> <li> <p>GATEWAY – A gateway cluster allows you to access data across processes in kdb systems. It allows you to create your own routing logic using the initialization scripts and custom code. This type of cluster does not require a writable local storage.</p> </li> <li> <p>GP – A general purpose cluster allows you to quickly iterate on code during development by granting greater access to system commands and enabling a fast reload of custom code. This cluster type can optionally mount databases including cache and savedown storage. For this cluster type, the node count is fixed at 1. It does not support autoscaling and supports only <code>SINGLE</code> AZ mode.</p> </li> <li> <p>Tickerplant – A tickerplant cluster allows you to subscribe to feed handlers based on IAM permissions. It can publish to RDBs, other Tickerplants, and real-time subscribers (RTS). Tickerplants can persist messages to log, which is readable by any RDB environment. It supports only single-node that is only one kdb process.</p> </li> </ul>"}, "tickerplantLogConfiguration": {"shape": "TickerplantLogConfiguration"}, "volumes": {"shape": "Volumes", "documentation": "<p> A list of volumes mounted on the cluster. </p>"}, "databases": {"shape": "KxDatabaseConfigurations", "documentation": "<p>A list of databases that will be available for querying.</p>"}, "cacheStorageConfigurations": {"shape": "KxCacheStorageConfigurations", "documentation": "<p>The configurations for a read only cache storage associated with a cluster. This cache will be stored as an FSx Lustre that reads from the S3 store. </p>"}, "autoScalingConfiguration": {"shape": "AutoScalingConfiguration", "documentation": "<p>The configuration based on which FinSpace will scale in or scale out nodes in your cluster.</p>"}, "clusterDescription": {"shape": "KxClusterDescription", "documentation": "<p>A description of the cluster.</p>"}, "capacityConfiguration": {"shape": "CapacityConfiguration", "documentation": "<p>A structure for the metadata of a cluster. It includes information like the CPUs needed, memory of instances, and number of instances.</p>"}, "releaseLabel": {"shape": "ReleaseLabel", "documentation": "<p>A version of the FinSpace managed kdb to run.</p>"}, "vpcConfiguration": {"shape": "VpcConfiguration", "documentation": "<p>Configuration details about the network where the Privatelink endpoint of the cluster resides.</p>"}, "initializationScript": {"shape": "InitializationScriptFilePath", "documentation": "<p>Specifies a Q program that will be run at launch of a cluster. It is a relative path within <i>.zip</i> file that contains the custom code, which will be loaded on the cluster. It must include the file name itself. For example, <code>somedir/init.q</code>.</p>"}, "commandLineArguments": {"shape": "KxCommandLineArguments", "documentation": "<p>Defines the key-value pairs to make them available inside the cluster.</p>"}, "code": {"shape": "CodeConfiguration", "documentation": "<p>The details of the custom code that you want to use inside a cluster when analyzing a data. It consists of the S3 source bucket, location, S3 object version, and the relative path from where the custom code is loaded into the cluster. </p>"}, "executionRole": {"shape": "ExecutionRoleArn", "documentation": "<p> An IAM role that defines a set of permissions associated with a cluster. These permissions are assumed when a cluster attempts to access another cluster. </p>"}, "lastModifiedTimestamp": {"shape": "Timestamp", "documentation": "<p>The last time that the cluster was modified. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as *************.</p>"}, "savedownStorageConfiguration": {"shape": "KxSavedownStorageConfiguration", "documentation": "<p>The size and type of the temporary storage that is used to hold data during the savedown process. This parameter is required when you choose <code>clusterType</code> as RDB. All the data written to this storage space is lost when the cluster node is restarted.</p>"}, "azMode": {"shape": "KxAzMode", "documentation": "<p>The number of availability zones you want to assign per cluster. This can be one of the following </p> <ul> <li> <p> <code>SINGLE</code> – Assigns one availability zone per cluster.</p> </li> <li> <p> <code>MULTI</code> – Assigns all the availability zones per cluster.</p> </li> </ul>"}, "availabilityZoneId": {"shape": "AvailabilityZoneId", "documentation": "<p> The availability zone identifiers for the requested regions. </p>"}, "createdTimestamp": {"shape": "Timestamp", "documentation": "<p>The timestamp at which the cluster was created in FinSpace. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as *************.</p>"}, "scalingGroupConfiguration": {"shape": "KxScalingGroupConfiguration", "documentation": "<p>The structure that stores the configuration details of a scaling group.</p>"}}}, "CreateKxDatabaseRequest": {"type": "structure", "required": ["environmentId", "databaseName", "clientToken"], "members": {"environmentId": {"shape": "EnvironmentId", "documentation": "<p>A unique identifier for the kdb environment.</p>", "location": "uri", "locationName": "environmentId"}, "databaseName": {"shape": "DatabaseName", "documentation": "<p>The name of the kdb database.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description of the database.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>A list of key-value pairs to label the kdb database. You can add up to 50 tags to your kdb database</p>"}, "clientToken": {"shape": "ClientTokenString", "documentation": "<p>A token that ensures idempotency. This token expires in 10 minutes.</p>", "idempotencyToken": true}}}, "CreateKxDatabaseResponse": {"type": "structure", "members": {"databaseName": {"shape": "DatabaseName", "documentation": "<p>The name of the kdb database.</p>"}, "databaseArn": {"shape": "DatabaseArn", "documentation": "<p>The ARN identifier of the database.</p>"}, "environmentId": {"shape": "EnvironmentId", "documentation": "<p>A unique identifier for the kdb environment.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description of the database.</p>"}, "createdTimestamp": {"shape": "Timestamp", "documentation": "<p>The timestamp at which the database is created in FinSpace. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as *************.</p>"}, "lastModifiedTimestamp": {"shape": "Timestamp", "documentation": "<p>The last time that the database was updated in FinSpace. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as *************.</p>"}}}, "CreateKxDataviewRequest": {"type": "structure", "required": ["environmentId", "databaseName", "dataviewName", "azMode", "clientToken"], "members": {"environmentId": {"shape": "EnvironmentId", "documentation": "<p>A unique identifier for the kdb environment, where you want to create the dataview. </p>", "location": "uri", "locationName": "environmentId"}, "databaseName": {"shape": "DatabaseName", "documentation": "<p> The name of the database where you want to create a dataview. </p>", "location": "uri", "locationName": "databaseName"}, "dataviewName": {"shape": "KxDataviewName", "documentation": "<p>A unique identifier for the dataview.</p>"}, "azMode": {"shape": "KxAzMode", "documentation": "<p>The number of availability zones you want to assign per volume. Currently, FinSpace only supports <code>SINGLE</code> for volumes. This places dataview in a single AZ.</p>"}, "availabilityZoneId": {"shape": "AvailabilityZoneId", "documentation": "<p> The identifier of the availability zones. </p>"}, "changesetId": {"shape": "ChangesetId", "documentation": "<p> A unique identifier of the changeset that you want to use to ingest data. </p>"}, "segmentConfigurations": {"shape": "KxDataviewSegmentConfigurationList", "documentation": "<p> The configuration that contains the database path of the data that you want to place on each selected volume. Each segment must have a unique database path for each volume. If you do not explicitly specify any database path for a volume, they are accessible from the cluster through the default S3/object store segment. </p>"}, "autoUpdate": {"shape": "booleanValue", "documentation": "<p>The option to specify whether you want to apply all the future additions and corrections automatically to the dataview, when you ingest new changesets. The default value is false.</p>"}, "readWrite": {"shape": "booleanValue", "documentation": "<p> The option to specify whether you want to make the dataview writable to perform database maintenance. The following are some considerations related to writable dataviews.&#x2028;&#x2028;</p> <ul> <li> <p>You cannot create partial writable dataviews. When you create writeable dataviews you must provide the entire database path.</p> </li> <li> <p>You cannot perform updates on a writeable dataview. Hence, <code>autoUpdate</code> must be set as <b>False</b> if <code>readWrite</code> is <b>True</b> for a dataview.</p> </li> <li> <p>You must also use a unique volume for creating a writeable dataview. So, if you choose a volume that is already in use by another dataview, the dataview creation fails.</p> </li> <li> <p>Once you create a dataview as writeable, you cannot change it to read-only. So, you cannot update the <code>readWrite</code> parameter later.</p> </li> </ul>"}, "description": {"shape": "Description", "documentation": "<p>A description of the dataview.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p> A list of key-value pairs to label the dataview. You can add up to 50 tags to a dataview. </p>"}, "clientToken": {"shape": "ClientTokenString", "documentation": "<p>A token that ensures idempotency. This token expires in 10 minutes.</p>", "idempotencyToken": true}}}, "CreateKxDataviewResponse": {"type": "structure", "members": {"dataviewName": {"shape": "KxDataviewName", "documentation": "<p>A unique identifier for the dataview.</p>"}, "databaseName": {"shape": "DatabaseName", "documentation": "<p>The name of the database where you want to create a dataview.</p>"}, "environmentId": {"shape": "EnvironmentId", "documentation": "<p>A unique identifier for the kdb environment, where you want to create the dataview. </p>"}, "azMode": {"shape": "KxAzMode", "documentation": "<p>The number of availability zones you want to assign per volume. Currently, FinSpace only supports <code>SINGLE</code> for volumes. This places dataview in a single AZ.</p>"}, "availabilityZoneId": {"shape": "AvailabilityZoneId", "documentation": "<p> The identifier of the availability zones. </p>"}, "changesetId": {"shape": "ChangesetId", "documentation": "<p>A unique identifier for the changeset.</p>"}, "segmentConfigurations": {"shape": "KxDataviewSegmentConfigurationList", "documentation": "<p> The configuration that contains the database path of the data that you want to place on each selected volume. Each segment must have a unique database path for each volume. If you do not explicitly specify any database path for a volume, they are accessible from the cluster through the default S3/object store segment. </p>"}, "description": {"shape": "Description", "documentation": "<p>A description of the dataview.</p>"}, "autoUpdate": {"shape": "booleanValue", "documentation": "<p>The option to select whether you want to apply all the future additions and corrections automatically to the dataview when you ingest new changesets. The default value is false.</p>"}, "readWrite": {"shape": "booleanValue", "documentation": "<p>Returns True if the dataview is created as writeable and False otherwise. </p>"}, "createdTimestamp": {"shape": "Timestamp", "documentation": "<p> The timestamp at which the dataview was created in FinSpace. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as *************.</p>"}, "lastModifiedTimestamp": {"shape": "Timestamp", "documentation": "<p> The last time that the dataview was updated in FinSpace. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as *************. </p>"}, "status": {"shape": "KxDataviewStatus", "documentation": "<p> The status of dataview creation.</p> <ul> <li> <p> <code>CREATING</code> – The dataview creation is in progress.</p> </li> <li> <p> <code>UPDATING</code> – The dataview is in the process of being updated.</p> </li> <li> <p> <code>ACTIVE</code> – The dataview is active.</p> </li> </ul>"}}}, "CreateKxEnvironmentRequest": {"type": "structure", "required": ["name", "kmsKeyId"], "members": {"name": {"shape": "KxEnvironmentName", "documentation": "<p>The name of the kdb environment that you want to create.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description for the kdb environment.</p>"}, "kmsKeyId": {"shape": "KmsKeyARN", "documentation": "<p>The KMS key ID to encrypt your data in the FinSpace environment.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>A list of key-value pairs to label the kdb environment. You can add up to 50 tags to your kdb environment.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A token that ensures idempotency. This token expires in 10 minutes.</p>", "idempotencyToken": true}}}, "CreateKxEnvironmentResponse": {"type": "structure", "members": {"name": {"shape": "KxEnvironmentName", "documentation": "<p>The name of the kdb environment.</p>"}, "status": {"shape": "EnvironmentStatus", "documentation": "<p>The status of the kdb environment.</p>"}, "environmentId": {"shape": "IdType", "documentation": "<p>A unique identifier for the kdb environment.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description for the kdb environment.</p>"}, "environmentArn": {"shape": "EnvironmentArn", "documentation": "<p>The ARN identifier of the environment.</p>"}, "kmsKeyId": {"shape": "KmsKeyId", "documentation": "<p>The KMS key ID to encrypt your data in the FinSpace environment.</p>"}, "creationTimestamp": {"shape": "Timestamp", "documentation": "<p>The timestamp at which the kdb environment was created in FinSpace.</p>"}}}, "CreateKxScalingGroupRequest": {"type": "structure", "required": ["clientToken", "environmentId", "scalingGroupName", "hostType", "availabilityZoneId"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>A token that ensures idempotency. This token expires in 10 minutes.</p>", "idempotencyToken": true}, "environmentId": {"shape": "KxEnvironmentId", "documentation": "<p>A unique identifier for the kdb environment, where you want to create the scaling group. </p>", "location": "uri", "locationName": "environmentId"}, "scalingGroupName": {"shape": "KxScalingGroupName", "documentation": "<p>A unique identifier for the kdb scaling group. </p>"}, "hostType": {"shape": "KxHostType", "documentation": "<p> The memory and CPU capabilities of the scaling group host on which FinSpace Managed kdb clusters will be placed.</p> <p>You can add one of the following values:</p> <ul> <li> <p> <code>kx.sg.large</code> – The host type with a configuration of 16 GiB memory and 2 vCPUs.</p> </li> <li> <p> <code>kx.sg.xlarge</code> – The host type with a configuration of 32 GiB memory and 4 vCPUs.</p> </li> <li> <p> <code>kx.sg.2xlarge</code> – The host type with a configuration of 64 GiB memory and 8 vCPUs.</p> </li> <li> <p> <code>kx.sg.4xlarge</code> – The host type with a configuration of 108 GiB memory and 16 vCPUs.</p> </li> <li> <p> <code>kx.sg.8xlarge</code> – The host type with a configuration of 216 GiB memory and 32 vCPUs.</p> </li> <li> <p> <code>kx.sg.16xlarge</code> – The host type with a configuration of 432 GiB memory and 64 vCPUs.</p> </li> <li> <p> <code>kx.sg.32xlarge</code> – The host type with a configuration of 864 GiB memory and 128 vCPUs.</p> </li> <li> <p> <code>kx.sg1.16xlarge</code> – The host type with a configuration of 1949 GiB memory and 64 vCPUs.</p> </li> <li> <p> <code>kx.sg1.24xlarge</code> – The host type with a configuration of 2948 GiB memory and 96 vCPUs.</p> </li> </ul>"}, "availabilityZoneId": {"shape": "AvailabilityZoneId", "documentation": "<p>The identifier of the availability zones.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p> A list of key-value pairs to label the scaling group. You can add up to 50 tags to a scaling group. </p>"}}}, "CreateKxScalingGroupResponse": {"type": "structure", "members": {"environmentId": {"shape": "KxEnvironmentId", "documentation": "<p>A unique identifier for the kdb environment, where you create the scaling group. </p>"}, "scalingGroupName": {"shape": "KxScalingGroupName", "documentation": "<p>A unique identifier for the kdb scaling group. </p>"}, "hostType": {"shape": "KxHostType", "documentation": "<p> The memory and CPU capabilities of the scaling group host on which FinSpace Managed kdb clusters will be placed. </p>"}, "availabilityZoneId": {"shape": "AvailabilityZoneId", "documentation": "<p>The identifier of the availability zones.</p>"}, "status": {"shape": "KxScalingGroupStatus", "documentation": "<p>The status of scaling group.</p> <ul> <li> <p>CREATING – The scaling group creation is in progress.</p> </li> <li> <p>CREATE_FAILED – The scaling group creation has failed.</p> </li> <li> <p>ACTIVE – The scaling group is active.</p> </li> <li> <p>UPDATING – The scaling group is in the process of being updated.</p> </li> <li> <p>UPDATE_FAILED – The update action failed.</p> </li> <li> <p>DELETING – The scaling group is in the process of being deleted.</p> </li> <li> <p>DELETE_FAILED – The system failed to delete the scaling group.</p> </li> <li> <p>DELETED – The scaling group is successfully deleted.</p> </li> </ul>"}, "lastModifiedTimestamp": {"shape": "Timestamp", "documentation": "<p> The last time that the scaling group was updated in FinSpace. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as *************. </p>"}, "createdTimestamp": {"shape": "Timestamp", "documentation": "<p> The timestamp at which the scaling group was created in FinSpace. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as *************.</p>"}}}, "CreateKxUserRequest": {"type": "structure", "required": ["environmentId", "userName", "iamRole"], "members": {"environmentId": {"shape": "IdType", "documentation": "<p>A unique identifier for the kdb environment where you want to create a user.</p>", "location": "uri", "locationName": "environmentId"}, "userName": {"shape": "KxUserNameString", "documentation": "<p>A unique identifier for the user.</p>"}, "iamRole": {"shape": "RoleArn", "documentation": "<p>The IAM role ARN that will be associated with the user.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>A list of key-value pairs to label the user. You can add up to 50 tags to a user.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A token that ensures idempotency. This token expires in 10 minutes.</p>", "idempotencyToken": true}}}, "CreateKxUserResponse": {"type": "structure", "members": {"userName": {"shape": "KxUserNameString", "documentation": "<p>A unique identifier for the user.</p>"}, "userArn": {"shape": "KxUserArn", "documentation": "<p> The Amazon Resource Name (ARN) that identifies the user. For more information about ARNs and how to use ARNs in policies, see <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/reference_identifiers.html\">IAM Identifiers</a> in the <i>IAM User Guide</i>. </p>"}, "environmentId": {"shape": "IdType", "documentation": "<p>A unique identifier for the kdb environment.</p>"}, "iamRole": {"shape": "RoleArn", "documentation": "<p>The IAM role ARN that will be associated with the user.</p>"}}}, "CreateKxVolumeRequest": {"type": "structure", "required": ["environmentId", "volumeType", "volumeName", "azMode", "availabilityZoneIds"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>A token that ensures idempotency. This token expires in 10 minutes.</p>", "idempotencyToken": true}, "environmentId": {"shape": "KxEnvironmentId", "documentation": "<p>A unique identifier for the kdb environment, whose clusters can attach to the volume. </p>", "location": "uri", "locationName": "environmentId"}, "volumeType": {"shape": "KxVolumeType", "documentation": "<p> The type of file system volume. Currently, FinSpace only supports <code>NAS_1</code> volume type. When you select <code>NAS_1</code> volume type, you must also provide <code>nas1Configuration</code>. </p>"}, "volumeName": {"shape": "KxVolumeName", "documentation": "<p>A unique identifier for the volume.</p>"}, "description": {"shape": "Description", "documentation": "<p> A description of the volume. </p>"}, "nas1Configuration": {"shape": "KxNAS1Configuration", "documentation": "<p> Specifies the configuration for the Network attached storage (NAS_1) file system volume. This parameter is required when you choose <code>volumeType</code> as <i>NAS_1</i>.</p>"}, "azMode": {"shape": "KxAzMode", "documentation": "<p>The number of availability zones you want to assign per volume. Currently, FinSpace only supports <code>SINGLE</code> for volumes. This places dataview in a single AZ.</p>"}, "availabilityZoneIds": {"shape": "AvailabilityZoneIds", "documentation": "<p>The identifier of the availability zones.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p> A list of key-value pairs to label the volume. You can add up to 50 tags to a volume. </p>"}}}, "CreateKxVolumeResponse": {"type": "structure", "members": {"environmentId": {"shape": "KxEnvironmentId", "documentation": "<p>A unique identifier for the kdb environment, whose clusters can attach to the volume. </p>"}, "volumeName": {"shape": "KxVolumeName", "documentation": "<p>A unique identifier for the volume.</p>"}, "volumeType": {"shape": "KxVolumeType", "documentation": "<p> The type of file system volume. Currently, FinSpace only supports <code>NAS_1</code> volume type. </p>"}, "volumeArn": {"shape": "KxVolumeArn", "documentation": "<p> The ARN identifier of the volume. </p>"}, "nas1Configuration": {"shape": "KxNAS1Configuration", "documentation": "<p> Specifies the configuration for the Network attached storage (NAS_1) file system volume.</p>"}, "status": {"shape": "KxVolumeStatus", "documentation": "<p>The status of volume creation.</p> <ul> <li> <p>CREATING – The volume creation is in progress.</p> </li> <li> <p>CREATE_FAILED – The volume creation has failed.</p> </li> <li> <p>ACTIVE – The volume is active.</p> </li> <li> <p>UPDATING – The volume is in the process of being updated.</p> </li> <li> <p>UPDATE_FAILED – The update action failed.</p> </li> <li> <p>UPDATED – The volume is successfully updated.</p> </li> <li> <p>DELETING – The volume is in the process of being deleted.</p> </li> <li> <p>DELETE_FAILED – The system failed to delete the volume.</p> </li> <li> <p>DELETED – The volume is successfully deleted.</p> </li> </ul>"}, "statusReason": {"shape": "KxVolumeStatusReason", "documentation": "<p>The error message when a failed state occurs. </p>"}, "azMode": {"shape": "KxAzMode", "documentation": "<p>The number of availability zones you want to assign per volume. Currently, FinSpace only supports <code>SINGLE</code> for volumes. This places dataview in a single AZ.</p>"}, "description": {"shape": "Description", "documentation": "<p> A description of the volume. </p>"}, "availabilityZoneIds": {"shape": "AvailabilityZoneIds", "documentation": "<p>The identifier of the availability zones.</p>"}, "createdTimestamp": {"shape": "Timestamp", "documentation": "<p>The timestamp at which the volume was created in FinSpace. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as *************.</p>"}}}, "CustomDNSConfiguration": {"type": "list", "member": {"shape": "CustomDNSServer"}}, "CustomDNSServer": {"type": "structure", "required": ["customDNSServerName", "customDNSServerIP"], "members": {"customDNSServerName": {"shape": "ValidHostname", "documentation": "<p>The name of the DNS server.</p>"}, "customDNSServerIP": {"shape": "ValidIPAddress", "documentation": "<p>The IP address of the DNS server.</p>"}}, "documentation": "<p>A list of DNS server name and server IP. This is used to set up Route-53 outbound resolvers.</p>"}, "DataBundleArn": {"type": "string", "documentation": "<p>The Amazon Resource Name (ARN) of the data bundle.</p>", "max": 2048, "min": 20, "pattern": "^arn:aws:finspace:[A-Za-z0-9_/.-]{0,63}:\\d*:data-bundle/[0-9A-Za-z_-]{1,128}$"}, "DataBundleArns": {"type": "list", "member": {"shape": "DataBundleArn"}}, "DatabaseArn": {"type": "string"}, "DatabaseName": {"type": "string", "max": 63, "min": 3, "pattern": "^[a-zA-Z0-9][a-zA-Z0-9-_]*[a-zA-Z0-9]$"}, "DbPath": {"type": "string", "max": 1025, "min": 1, "pattern": "^(\\*)*[\\/\\?\\*]([^\\/]+\\/){0,2}[^\\/]*$"}, "DbPaths": {"type": "list", "member": {"shape": "DbPath"}}, "DeleteEnvironmentRequest": {"type": "structure", "required": ["environmentId"], "members": {"environmentId": {"shape": "IdType", "documentation": "<p>The identifier for the FinSpace environment.</p>", "location": "uri", "locationName": "environmentId"}}}, "DeleteEnvironmentResponse": {"type": "structure", "members": {}}, "DeleteKxClusterNodeRequest": {"type": "structure", "required": ["clusterName", "nodeId", "environmentId"], "members": {"environmentId": {"shape": "KxEnvironmentId", "documentation": "<p>A unique identifier for the kdb environment.</p>", "location": "uri", "locationName": "environmentId"}, "clusterName": {"shape": "KxClusterName", "documentation": "<p>The name of the cluster, for which you want to delete the nodes.</p>", "location": "uri", "locationName": "clusterName"}, "nodeId": {"shape": "KxClusterNodeIdString", "documentation": "<p>A unique identifier for the node that you want to delete.</p>", "location": "uri", "locationName": "nodeId"}}}, "DeleteKxClusterNodeResponse": {"type": "structure", "members": {}}, "DeleteKxClusterRequest": {"type": "structure", "required": ["environmentId", "clusterName"], "members": {"environmentId": {"shape": "KxEnvironmentId", "documentation": "<p>A unique identifier for the kdb environment.</p>", "location": "uri", "locationName": "environmentId"}, "clusterName": {"shape": "KxClusterName", "documentation": "<p>The name of the cluster that you want to delete.</p>", "location": "uri", "locationName": "clusterName"}, "clientToken": {"shape": "ClientTokenString", "documentation": "<p>A token that ensures idempotency. This token expires in 10 minutes.</p>", "idempotencyToken": true, "location": "querystring", "locationName": "clientToken"}}}, "DeleteKxClusterResponse": {"type": "structure", "members": {}}, "DeleteKxDatabaseRequest": {"type": "structure", "required": ["environmentId", "databaseName", "clientToken"], "members": {"environmentId": {"shape": "EnvironmentId", "documentation": "<p>A unique identifier for the kdb environment.</p>", "location": "uri", "locationName": "environmentId"}, "databaseName": {"shape": "DatabaseName", "documentation": "<p>The name of the kdb database that you want to delete.</p>", "location": "uri", "locationName": "databaseName"}, "clientToken": {"shape": "ClientTokenString", "documentation": "<p>A token that ensures idempotency. This token expires in 10 minutes.</p>", "idempotencyToken": true, "location": "querystring", "locationName": "clientToken"}}}, "DeleteKxDatabaseResponse": {"type": "structure", "members": {}}, "DeleteKxDataviewRequest": {"type": "structure", "required": ["environmentId", "databaseName", "dataviewName", "clientToken"], "members": {"environmentId": {"shape": "EnvironmentId", "documentation": "<p>A unique identifier for the kdb environment, from where you want to delete the dataview. </p>", "location": "uri", "locationName": "environmentId"}, "databaseName": {"shape": "DatabaseName", "documentation": "<p>The name of the database whose dataview you want to delete.</p>", "location": "uri", "locationName": "databaseName"}, "dataviewName": {"shape": "KxDataviewName", "documentation": "<p>The name of the dataview that you want to delete.</p>", "location": "uri", "locationName": "dataviewName"}, "clientToken": {"shape": "ClientTokenString", "documentation": "<p>A token that ensures idempotency. This token expires in 10 minutes.</p>", "idempotencyToken": true, "location": "querystring", "locationName": "clientToken"}}}, "DeleteKxDataviewResponse": {"type": "structure", "members": {}}, "DeleteKxEnvironmentRequest": {"type": "structure", "required": ["environmentId"], "members": {"environmentId": {"shape": "IdType", "documentation": "<p>A unique identifier for the kdb environment.</p>", "location": "uri", "locationName": "environmentId"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A token that ensures idempotency. This token expires in 10 minutes.</p>", "idempotencyToken": true, "location": "querystring", "locationName": "clientToken"}}}, "DeleteKxEnvironmentResponse": {"type": "structure", "members": {}}, "DeleteKxScalingGroupRequest": {"type": "structure", "required": ["environmentId", "scalingGroupName"], "members": {"environmentId": {"shape": "KxEnvironmentId", "documentation": "<p>A unique identifier for the kdb environment, from where you want to delete the dataview. </p>", "location": "uri", "locationName": "environmentId"}, "scalingGroupName": {"shape": "KxScalingGroupName", "documentation": "<p>A unique identifier for the kdb scaling group. </p>", "location": "uri", "locationName": "scalingGroupName"}, "clientToken": {"shape": "ClientTokenString", "documentation": "<p>A token that ensures idempotency. This token expires in 10 minutes.</p>", "idempotencyToken": true, "location": "querystring", "locationName": "clientToken"}}}, "DeleteKxScalingGroupResponse": {"type": "structure", "members": {}}, "DeleteKxUserRequest": {"type": "structure", "required": ["userName", "environmentId"], "members": {"userName": {"shape": "KxUserNameString", "documentation": "<p>A unique identifier for the user that you want to delete.</p>", "location": "uri", "locationName": "userName"}, "environmentId": {"shape": "IdType", "documentation": "<p>A unique identifier for the kdb environment.</p>", "location": "uri", "locationName": "environmentId"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A token that ensures idempotency. This token expires in 10 minutes.</p>", "idempotencyToken": true, "location": "querystring", "locationName": "clientToken"}}}, "DeleteKxUserResponse": {"type": "structure", "members": {}}, "DeleteKxVolumeRequest": {"type": "structure", "required": ["environmentId", "volumeName"], "members": {"environmentId": {"shape": "KxEnvironmentId", "documentation": "<p>A unique identifier for the kdb environment, whose clusters can attach to the volume. </p>", "location": "uri", "locationName": "environmentId"}, "volumeName": {"shape": "KxVolumeName", "documentation": "<p> The name of the volume that you want to delete. </p>", "location": "uri", "locationName": "volumeName"}, "clientToken": {"shape": "ClientTokenString", "documentation": "<p>A token that ensures idempotency. This token expires in 10 minutes.</p>", "idempotencyToken": true, "location": "querystring", "locationName": "clientToken"}}}, "DeleteKxVolumeResponse": {"type": "structure", "members": {}}, "Description": {"type": "string", "max": 1000, "min": 1, "pattern": "^[a-zA-Z0-9. ]{1,1000}$"}, "EmailId": {"type": "string", "max": 128, "min": 1, "pattern": "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+[.]+[A-Za-z]+", "sensitive": true}, "Environment": {"type": "structure", "members": {"name": {"shape": "EnvironmentName", "documentation": "<p>The name of the FinSpace environment.</p>"}, "environmentId": {"shape": "IdType", "documentation": "<p>The identifier of the FinSpace environment.</p>"}, "awsAccountId": {"shape": "IdType", "documentation": "<p>The ID of the AWS account in which the FinSpace environment is created.</p>"}, "status": {"shape": "EnvironmentStatus", "documentation": "<p>The current status of creation of the FinSpace environment.</p>"}, "environmentUrl": {"shape": "url", "documentation": "<p>The sign-in URL for the web application of your FinSpace environment.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description of the FinSpace environment.</p>"}, "environmentArn": {"shape": "EnvironmentArn", "documentation": "<p>The Amazon Resource Name (ARN) of your FinSpace environment.</p>"}, "sageMakerStudioDomainUrl": {"shape": "SmsDomainUrl", "documentation": "<p>The URL of the integrated FinSpace notebook environment in your web application.</p>"}, "kmsKeyId": {"shape": "KmsKeyId", "documentation": "<p>The KMS key id used to encrypt in the FinSpace environment.</p>"}, "dedicatedServiceAccountId": {"shape": "IdType", "documentation": "<p>The AWS account ID of the dedicated service account associated with your FinSpace environment.</p>"}, "federationMode": {"shape": "FederationMode", "documentation": "<p>The authentication mode for the environment.</p>"}, "federationParameters": {"shape": "FederationParameters", "documentation": "<p>Configuration information when authentication mode is FEDERATED.</p>"}}, "documentation": "<p>Represents an FinSpace environment.</p>"}, "EnvironmentArn": {"type": "string", "max": 2048, "min": 20, "pattern": "^arn:aws:finspace:[A-Za-z0-9_/.-]{0,63}:\\d+:environment/[0-9A-Za-z_-]{1,128}$"}, "EnvironmentErrorMessage": {"type": "string", "max": 1000, "min": 0, "pattern": "^[a-zA-Z0-9. ]{1,1000}$"}, "EnvironmentId": {"type": "string", "max": 32, "min": 1, "pattern": ".*\\S.*"}, "EnvironmentList": {"type": "list", "member": {"shape": "Environment"}}, "EnvironmentName": {"type": "string", "max": 255, "min": 1, "pattern": "^[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]$"}, "EnvironmentStatus": {"type": "string", "enum": ["CREATE_REQUESTED", "CREATING", "CREATED", "DELETE_REQUESTED", "DELETING", "DELETED", "FAILED_CREATION", "RETRY_DELETION", "FAILED_DELETION", "UPDATE_NETWORK_REQUESTED", "UPDATING_NETWORK", "FAILED_UPDATING_NETWORK", "SUSPENDED"]}, "ErrorDetails": {"type": "string", "enum": ["The inputs to this request are invalid.", "Service limits have been exceeded.", "Missing required permission to perform this request.", "One or more inputs to this request were not found.", "The system temporarily lacks sufficient resources to process the request.", "An internal error has occurred.", "Cancelled", "A user recoverable error has occurred"]}, "ErrorInfo": {"type": "structure", "members": {"errorMessage": {"shape": "ErrorMessage", "documentation": "<p>Specifies the error message that appears if a flow fails. </p>"}, "errorType": {"shape": "ErrorDetails", "documentation": "<p>Specifies the type of error.</p>"}}, "documentation": "<p>Provides details in the event of a failed flow, including the error type and the related error message.</p>"}, "ErrorMessage": {"type": "string", "max": 1000}, "ExecutionRoleArn": {"type": "string", "max": 1024, "min": 1, "pattern": "^arn:aws[a-z0-9-]*:iam::\\d{12}:role\\/[\\w-\\/.@+=,]{1,1017}$"}, "FederationAttributeKey": {"type": "string", "max": 32, "min": 1, "pattern": ".*"}, "FederationAttributeValue": {"type": "string", "max": 1000, "min": 1, "pattern": ".*"}, "FederationMode": {"type": "string", "enum": ["FEDERATED", "LOCAL"]}, "FederationParameters": {"type": "structure", "members": {"samlMetadataDocument": {"shape": "SamlMetadataDocument", "documentation": "<p>SAML 2.0 Metadata document from identity provider (IdP).</p>"}, "samlMetadataURL": {"shape": "url", "documentation": "<p>Provide the metadata URL from your SAML 2.0 compliant identity provider (IdP).</p>"}, "applicationCallBackURL": {"shape": "url", "documentation": "<p>The redirect or sign-in URL that should be entered into the SAML 2.0 compliant identity provider configuration (IdP).</p>"}, "federationURN": {"shape": "urn", "documentation": "<p>The Uniform Resource Name (URN). Also referred as Service Provider URN or Audience URI or Service Provider Entity ID.</p>"}, "federationProviderName": {"shape": "FederationProviderName", "documentation": "<p>Name of the identity provider (IdP).</p>"}, "attributeMap": {"shape": "AttributeMap", "documentation": "<p>SAML attribute name and value. The name must always be <code>Email</code> and the value should be set to the attribute definition in which user email is set. For example, name would be <code>Email</code> and value <code>http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress</code>. Please check your SAML 2.0 compliant identity provider (IdP) documentation for details.</p>"}}, "documentation": "<p>Configuration information when authentication mode is FEDERATED.</p>"}, "FederationProviderName": {"type": "string", "max": 32, "min": 1, "pattern": "[^_\\p{Z}][\\p{L}\\p{M}\\p{S}\\p{N}\\p{P}][^_\\p{Z}]+"}, "FinSpaceTaggableArn": {"type": "string", "max": 2048, "min": 20, "pattern": "^arn:aws:finspace:[A-Za-z0-9_/.-]{0,63}:\\d+:(environment|kxEnvironment)/[0-9A-Za-z_-]{1,128}(/(kxCluster|kxUser|kxVolume|kxScalingGroup)/[a-zA-Z0-9_-]{1,255}|/(kxDatabase/[a-zA-Z0-9_-]{1,255}(/kxDataview/[a-zA-Z0-9_-]{1,255})?))?$"}, "GetEnvironmentRequest": {"type": "structure", "required": ["environmentId"], "members": {"environmentId": {"shape": "IdType", "documentation": "<p>The identifier of the FinSpace environment.</p>", "location": "uri", "locationName": "environmentId"}}}, "GetEnvironmentResponse": {"type": "structure", "members": {"environment": {"shape": "Environment", "documentation": "<p>The name of the FinSpace environment.</p>"}}}, "GetKxChangesetRequest": {"type": "structure", "required": ["environmentId", "databaseName", "changesetId"], "members": {"environmentId": {"shape": "EnvironmentId", "documentation": "<p>A unique identifier for the kdb environment.</p>", "location": "uri", "locationName": "environmentId"}, "databaseName": {"shape": "DatabaseName", "documentation": "<p>The name of the kdb database.</p>", "location": "uri", "locationName": "databaseName"}, "changesetId": {"shape": "ChangesetId", "documentation": "<p>A unique identifier of the changeset for which you want to retrieve data.</p>", "location": "uri", "locationName": "changesetId"}}}, "GetKxChangesetResponse": {"type": "structure", "members": {"changesetId": {"shape": "ChangesetId", "documentation": "<p>A unique identifier for the changeset.</p>"}, "databaseName": {"shape": "DatabaseName", "documentation": "<p>The name of the kdb database.</p>"}, "environmentId": {"shape": "EnvironmentId", "documentation": "<p>A unique identifier for the kdb environment.</p>"}, "changeRequests": {"shape": "ChangeRequests", "documentation": "<p>A list of change request objects that are run in order.</p>"}, "createdTimestamp": {"shape": "Timestamp", "documentation": "<p>The timestamp at which the changeset was created in FinSpace. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as *************.</p>"}, "activeFromTimestamp": {"shape": "Timestamp", "documentation": "<p>Beginning time from which the changeset is active. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as *************.</p>"}, "lastModifiedTimestamp": {"shape": "Timestamp", "documentation": "<p>The timestamp at which the changeset was updated in FinSpace. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as *************.</p>"}, "status": {"shape": "ChangesetStatus", "documentation": "<p>Status of the changeset creation process.</p> <ul> <li> <p>Pending – Changeset creation is pending.</p> </li> <li> <p>Processing – Changeset creation is running.</p> </li> <li> <p>Failed – Changeset creation has failed.</p> </li> <li> <p>Complete – Changeset creation has succeeded.</p> </li> </ul>"}, "errorInfo": {"shape": "ErrorInfo", "documentation": "<p>Provides details in the event of a failed flow, including the error type and the related error message.</p>"}}}, "GetKxClusterRequest": {"type": "structure", "required": ["environmentId", "clusterName"], "members": {"environmentId": {"shape": "KxEnvironmentId", "documentation": "<p>A unique identifier for the kdb environment.</p>", "location": "uri", "locationName": "environmentId"}, "clusterName": {"shape": "KxClusterName", "documentation": "<p>The name of the cluster that you want to retrieve.</p>", "location": "uri", "locationName": "clusterName"}}}, "GetKxClusterResponse": {"type": "structure", "members": {"status": {"shape": "KxClusterStatus", "documentation": "<p>The status of cluster creation.</p> <ul> <li> <p>PENDING – The cluster is pending creation.</p> </li> <li> <p>CREATING – The cluster creation process is in progress.</p> </li> <li> <p>CREATE_FAILED – The cluster creation process has failed.</p> </li> <li> <p>RUNNING – The cluster creation process is running.</p> </li> <li> <p>UPDATING – The cluster is in the process of being updated.</p> </li> <li> <p>DELETING – The cluster is in the process of being deleted.</p> </li> <li> <p>DELETED – The cluster has been deleted.</p> </li> <li> <p>DELETE_FAILED – The cluster failed to delete.</p> </li> </ul>"}, "statusReason": {"shape": "KxClusterStatusReason", "documentation": "<p>The error message when a failed state occurs. </p>"}, "clusterName": {"shape": "KxClusterName", "documentation": "<p>A unique name for the cluster.</p>"}, "clusterType": {"shape": "KxClusterType", "documentation": "<p>Specifies the type of KDB database that is being created. The following types are available: </p> <ul> <li> <p>HDB – A Historical Database. The data is only accessible with read-only permissions from one of the FinSpace managed kdb databases mounted to the cluster.</p> </li> <li> <p>RDB – A Realtime Database. This type of database captures all the data from a ticker plant and stores it in memory until the end of day, after which it writes all of its data to a disk and reloads the HDB. This cluster type requires local storage for temporary storage of data during the savedown process. If you specify this field in your request, you must provide the <code>savedownStorageConfiguration</code> parameter.</p> </li> <li> <p>GATEWAY – A gateway cluster allows you to access data across processes in kdb systems. It allows you to create your own routing logic using the initialization scripts and custom code. This type of cluster does not require a writable local storage.</p> </li> <li> <p>GP – A general purpose cluster allows you to quickly iterate on code during development by granting greater access to system commands and enabling a fast reload of custom code. This cluster type can optionally mount databases including cache and savedown storage. For this cluster type, the node count is fixed at 1. It does not support autoscaling and supports only <code>SINGLE</code> AZ mode.</p> </li> <li> <p>Tickerplant – A tickerplant cluster allows you to subscribe to feed handlers based on IAM permissions. It can publish to RDBs, other Tickerplants, and real-time subscribers (RTS). Tickerplants can persist messages to log, which is readable by any RDB environment. It supports only single-node that is only one kdb process.</p> </li> </ul>"}, "tickerplantLogConfiguration": {"shape": "TickerplantLogConfiguration"}, "volumes": {"shape": "Volumes", "documentation": "<p> A list of volumes attached to the cluster. </p>"}, "databases": {"shape": "KxDatabaseConfigurations", "documentation": "<p> A list of databases mounted on the cluster.</p>"}, "cacheStorageConfigurations": {"shape": "KxCacheStorageConfigurations", "documentation": "<p>The configurations for a read only cache storage associated with a cluster. This cache will be stored as an FSx Lustre that reads from the S3 store. </p>"}, "autoScalingConfiguration": {"shape": "AutoScalingConfiguration", "documentation": "<p>The configuration based on which FinSpace will scale in or scale out nodes in your cluster.</p>"}, "clusterDescription": {"shape": "KxClusterDescription", "documentation": "<p>A description of the cluster.</p>"}, "capacityConfiguration": {"shape": "CapacityConfiguration", "documentation": "<p>A structure for the metadata of a cluster. It includes information like the CPUs needed, memory of instances, and number of instances.</p>"}, "releaseLabel": {"shape": "ReleaseLabel", "documentation": "<p>The version of FinSpace managed kdb to run.</p>"}, "vpcConfiguration": {"shape": "VpcConfiguration", "documentation": "<p>Configuration details about the network where the Privatelink endpoint of the cluster resides.</p>"}, "initializationScript": {"shape": "InitializationScriptFilePath", "documentation": "<p>Specifies a Q program that will be run at launch of a cluster. It is a relative path within <i>.zip</i> file that contains the custom code, which will be loaded on the cluster. It must include the file name itself. For example, <code>somedir/init.q</code>.</p>"}, "commandLineArguments": {"shape": "KxCommandLineArguments", "documentation": "<p>Defines key-value pairs to make them available inside the cluster.</p>"}, "code": {"shape": "CodeConfiguration", "documentation": "<p>The details of the custom code that you want to use inside a cluster when analyzing a data. It consists of the S3 source bucket, location, S3 object version, and the relative path from where the custom code is loaded into the cluster. </p>"}, "executionRole": {"shape": "ExecutionRoleArn", "documentation": "<p> An IAM role that defines a set of permissions associated with a cluster. These permissions are assumed when a cluster attempts to access another cluster. </p>"}, "lastModifiedTimestamp": {"shape": "Timestamp", "documentation": "<p>The last time that the cluster was modified. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as *************.</p>"}, "savedownStorageConfiguration": {"shape": "KxSavedownStorageConfiguration", "documentation": "<p>The size and type of the temporary storage that is used to hold data during the savedown process. This parameter is required when you choose <code>clusterType</code> as RDB. All the data written to this storage space is lost when the cluster node is restarted.</p>"}, "azMode": {"shape": "KxAzMode", "documentation": "<p>The number of availability zones you want to assign per cluster. This can be one of the following </p> <ul> <li> <p> <code>SINGLE</code> – Assigns one availability zone per cluster.</p> </li> <li> <p> <code>MULTI</code> – Assigns all the availability zones per cluster.</p> </li> </ul>"}, "availabilityZoneId": {"shape": "AvailabilityZoneId", "documentation": "<p> The availability zone identifiers for the requested regions. </p>"}, "createdTimestamp": {"shape": "Timestamp", "documentation": "<p>The timestamp at which the cluster was created in FinSpace. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as *************.</p>"}, "scalingGroupConfiguration": {"shape": "KxScalingGroupConfiguration"}}}, "GetKxConnectionStringRequest": {"type": "structure", "required": ["userArn", "environmentId", "clusterName"], "members": {"userArn": {"shape": "KxUserArn", "documentation": "<p> The Amazon Resource Name (ARN) that identifies the user. For more information about ARNs and how to use ARNs in policies, see <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/reference_identifiers.html\">IAM Identifiers</a> in the <i>IAM User Guide</i>. </p>", "location": "querystring", "locationName": "userArn"}, "environmentId": {"shape": "IdType", "documentation": "<p>A unique identifier for the kdb environment.</p>", "location": "uri", "locationName": "environmentId"}, "clusterName": {"shape": "KxClusterName", "documentation": "<p>A name of the kdb cluster.</p>", "location": "querystring", "locationName": "clusterName"}}}, "GetKxConnectionStringResponse": {"type": "structure", "members": {"signedConnectionString": {"shape": "SignedKxConnectionString", "documentation": "<p>The signed connection string that you can use to connect to clusters.</p>"}}}, "GetKxDatabaseRequest": {"type": "structure", "required": ["environmentId", "databaseName"], "members": {"environmentId": {"shape": "EnvironmentId", "documentation": "<p>A unique identifier for the kdb environment.</p>", "location": "uri", "locationName": "environmentId"}, "databaseName": {"shape": "DatabaseName", "documentation": "<p>The name of the kdb database.</p>", "location": "uri", "locationName": "databaseName"}}}, "GetKxDatabaseResponse": {"type": "structure", "members": {"databaseName": {"shape": "DatabaseName", "documentation": "<p>The name of the kdb database for which the information is retrieved.</p>"}, "databaseArn": {"shape": "DatabaseArn", "documentation": "<p>The ARN identifier of the database.</p>"}, "environmentId": {"shape": "EnvironmentId", "documentation": "<p>A unique identifier for the kdb environment.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description of the database.</p>"}, "createdTimestamp": {"shape": "Timestamp", "documentation": "<p>The timestamp at which the database is created in FinSpace. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as *************.</p>"}, "lastModifiedTimestamp": {"shape": "Timestamp", "documentation": "<p>The last time that the database was modified. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as *************.</p>"}, "lastCompletedChangesetId": {"shape": "ChangesetId", "documentation": "<p>A unique identifier for the changeset.</p>"}, "numBytes": {"shape": "numBytes", "documentation": "<p>The total number of bytes in the database.</p>"}, "numChangesets": {"shape": "numChangesets", "documentation": "<p>The total number of changesets in the database.</p>"}, "numFiles": {"shape": "numFiles", "documentation": "<p>The total number of files in the database.</p>"}}}, "GetKxDataviewRequest": {"type": "structure", "required": ["environmentId", "databaseName", "dataviewName"], "members": {"environmentId": {"shape": "EnvironmentId", "documentation": "<p>A unique identifier for the kdb environment, from where you want to retrieve the dataview details.</p>", "location": "uri", "locationName": "environmentId"}, "databaseName": {"shape": "DatabaseName", "documentation": "<p> The name of the database where you created the dataview.</p>", "location": "uri", "locationName": "databaseName"}, "dataviewName": {"shape": "KxDataviewName", "documentation": "<p>A unique identifier for the dataview.</p>", "location": "uri", "locationName": "dataviewName"}}}, "GetKxDataviewResponse": {"type": "structure", "members": {"databaseName": {"shape": "DatabaseName", "documentation": "<p> The name of the database where you created the dataview.</p>"}, "dataviewName": {"shape": "KxDataviewName", "documentation": "<p>A unique identifier for the dataview.</p>"}, "azMode": {"shape": "KxAzMode", "documentation": "<p>The number of availability zones you want to assign per volume. Currently, FinSpace only supports <code>SINGLE</code> for volumes. This places dataview in a single AZ.</p>"}, "availabilityZoneId": {"shape": "AvailabilityZoneId", "documentation": "<p> The identifier of the availability zones. </p>"}, "changesetId": {"shape": "ChangesetId", "documentation": "<p> A unique identifier of the changeset that you want to use to ingest data. </p>"}, "segmentConfigurations": {"shape": "KxDataviewSegmentConfigurationList", "documentation": "<p> The configuration that contains the database path of the data that you want to place on each selected volume. Each segment must have a unique database path for each volume. If you do not explicitly specify any database path for a volume, they are accessible from the cluster through the default S3/object store segment. </p>"}, "activeVersions": {"shape": "KxDataviewActiveVersionList", "documentation": "<p> The current active changeset versions of the database on the given dataview. </p>"}, "description": {"shape": "Description", "documentation": "<p>A description of the dataview.</p>"}, "autoUpdate": {"shape": "booleanValue", "documentation": "<p>The option to specify whether you want to apply all the future additions and corrections automatically to the dataview when new changesets are ingested. The default value is false.</p>"}, "readWrite": {"shape": "booleanValue", "documentation": "<p>Returns True if the dataview is created as writeable and False otherwise. </p>"}, "environmentId": {"shape": "EnvironmentId", "documentation": "<p>A unique identifier for the kdb environment, from where you want to retrieve the dataview details.</p>"}, "createdTimestamp": {"shape": "Timestamp", "documentation": "<p>The timestamp at which the dataview was created in FinSpace. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as *************.</p>"}, "lastModifiedTimestamp": {"shape": "Timestamp", "documentation": "<p> The last time that the dataview was updated in FinSpace. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as *************. </p>"}, "status": {"shape": "KxDataviewStatus", "documentation": "<p> The status of dataview creation.</p> <ul> <li> <p> <code>CREATING</code> – The dataview creation is in progress.</p> </li> <li> <p> <code>UPDATING</code> – The dataview is in the process of being updated.</p> </li> <li> <p> <code>ACTIVE</code> – The dataview is active.</p> </li> </ul>"}, "statusReason": {"shape": "KxDataviewStatusReason", "documentation": "<p> The error message when a failed state occurs. </p>"}}}, "GetKxEnvironmentRequest": {"type": "structure", "required": ["environmentId"], "members": {"environmentId": {"shape": "IdType", "documentation": "<p>A unique identifier for the kdb environment.</p>", "location": "uri", "locationName": "environmentId"}}}, "GetKxEnvironmentResponse": {"type": "structure", "members": {"name": {"shape": "KxEnvironmentName", "documentation": "<p>The name of the kdb environment.</p>"}, "environmentId": {"shape": "IdType", "documentation": "<p>A unique identifier for the kdb environment.</p>"}, "awsAccountId": {"shape": "IdType", "documentation": "<p>The unique identifier of the AWS account that is used to create the kdb environment.</p>"}, "status": {"shape": "EnvironmentStatus", "documentation": "<p>The status of the kdb environment.</p>"}, "tgwStatus": {"shape": "tgwStatus", "documentation": "<p>The status of the network configuration.</p>"}, "dnsStatus": {"shape": "dnsStatus", "documentation": "<p>The status of DNS configuration.</p>"}, "errorMessage": {"shape": "EnvironmentErrorMessage", "documentation": "<p>Specifies the error message that appears if a flow fails.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description for the kdb environment.</p>"}, "environmentArn": {"shape": "EnvironmentArn", "documentation": "<p>The ARN identifier of the environment.</p>"}, "kmsKeyId": {"shape": "KmsKeyId", "documentation": "<p>The KMS key ID to encrypt your data in the FinSpace environment.</p>"}, "dedicatedServiceAccountId": {"shape": "IdType", "documentation": "<p>A unique identifier for the AWS environment infrastructure account.</p>"}, "transitGatewayConfiguration": {"shape": "TransitGatewayConfiguration"}, "customDNSConfiguration": {"shape": "CustomDNSConfiguration", "documentation": "<p>A list of DNS server name and server IP. This is used to set up Route-53 outbound resolvers.</p>"}, "creationTimestamp": {"shape": "Timestamp", "documentation": "<p>The timestamp at which the kdb environment was created in FinSpace. </p>"}, "updateTimestamp": {"shape": "Timestamp", "documentation": "<p>The timestamp at which the kdb environment was updated. </p>"}, "availabilityZoneIds": {"shape": "AvailabilityZoneIds", "documentation": "<p>The identifier of the availability zones where subnets for the environment are created.</p>"}, "certificateAuthorityArn": {"shape": "stringValueLength1to255", "documentation": "<p>The Amazon Resource Name (ARN) of the certificate authority of the kdb environment.</p>"}}}, "GetKxScalingGroupRequest": {"type": "structure", "required": ["environmentId", "scalingGroupName"], "members": {"environmentId": {"shape": "KxEnvironmentId", "documentation": "<p>A unique identifier for the kdb environment. </p>", "location": "uri", "locationName": "environmentId"}, "scalingGroupName": {"shape": "KxScalingGroupName", "documentation": "<p>A unique identifier for the kdb scaling group. </p>", "location": "uri", "locationName": "scalingGroupName"}}}, "GetKxScalingGroupResponse": {"type": "structure", "members": {"scalingGroupName": {"shape": "KxScalingGroupName", "documentation": "<p>A unique identifier for the kdb scaling group. </p>"}, "scalingGroupArn": {"shape": "arn", "documentation": "<p> The ARN identifier for the scaling group. </p>"}, "hostType": {"shape": "KxHostType", "documentation": "<p> The memory and CPU capabilities of the scaling group host on which FinSpace Managed kdb clusters will be placed.</p> <p>It can have one of the following values:</p> <ul> <li> <p> <code>kx.sg.large</code> – The host type with a configuration of 16 GiB memory and 2 vCPUs.</p> </li> <li> <p> <code>kx.sg.xlarge</code> – The host type with a configuration of 32 GiB memory and 4 vCPUs.</p> </li> <li> <p> <code>kx.sg.2xlarge</code> – The host type with a configuration of 64 GiB memory and 8 vCPUs.</p> </li> <li> <p> <code>kx.sg.4xlarge</code> – The host type with a configuration of 108 GiB memory and 16 vCPUs.</p> </li> <li> <p> <code>kx.sg.8xlarge</code> – The host type with a configuration of 216 GiB memory and 32 vCPUs.</p> </li> <li> <p> <code>kx.sg.16xlarge</code> – The host type with a configuration of 432 GiB memory and 64 vCPUs.</p> </li> <li> <p> <code>kx.sg.32xlarge</code> – The host type with a configuration of 864 GiB memory and 128 vCPUs.</p> </li> <li> <p> <code>kx.sg1.16xlarge</code> – The host type with a configuration of 1949 GiB memory and 64 vCPUs.</p> </li> <li> <p> <code>kx.sg1.24xlarge</code> – The host type with a configuration of 2948 GiB memory and 96 vCPUs.</p> </li> </ul>"}, "clusters": {"shape": "KxClusterNameList", "documentation": "<p> The list of Managed kdb clusters that are currently active in the given scaling group. </p>"}, "availabilityZoneId": {"shape": "AvailabilityZoneId", "documentation": "<p>The identifier of the availability zones.</p>"}, "status": {"shape": "KxScalingGroupStatus", "documentation": "<p>The status of scaling group.</p> <ul> <li> <p>CREATING – The scaling group creation is in progress.</p> </li> <li> <p>CREATE_FAILED – The scaling group creation has failed.</p> </li> <li> <p>ACTIVE – The scaling group is active.</p> </li> <li> <p>UPDATING – The scaling group is in the process of being updated.</p> </li> <li> <p>UPDATE_FAILED – The update action failed.</p> </li> <li> <p>DELETING – The scaling group is in the process of being deleted.</p> </li> <li> <p>DELETE_FAILED – The system failed to delete the scaling group.</p> </li> <li> <p>DELETED – The scaling group is successfully deleted.</p> </li> </ul>"}, "statusReason": {"shape": "KxClusterStatusReason", "documentation": "<p> The error message when a failed state occurs. </p>"}, "lastModifiedTimestamp": {"shape": "Timestamp", "documentation": "<p> The last time that the scaling group was updated in FinSpace. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as *************. </p>"}, "createdTimestamp": {"shape": "Timestamp", "documentation": "<p> The timestamp at which the scaling group was created in FinSpace. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as *************.</p>"}}}, "GetKxUserRequest": {"type": "structure", "required": ["userName", "environmentId"], "members": {"userName": {"shape": "KxUserNameString", "documentation": "<p>A unique identifier for the user.</p>", "location": "uri", "locationName": "userName"}, "environmentId": {"shape": "IdType", "documentation": "<p>A unique identifier for the kdb environment.</p>", "location": "uri", "locationName": "environmentId"}}}, "GetKxUserResponse": {"type": "structure", "members": {"userName": {"shape": "IdType", "documentation": "<p>A unique identifier for the user.</p>"}, "userArn": {"shape": "KxUserArn", "documentation": "<p> The Amazon Resource Name (ARN) that identifies the user. For more information about ARNs and how to use ARNs in policies, see <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/reference_identifiers.html\">IAM Identifiers</a> in the <i>IAM User Guide</i>. </p>"}, "environmentId": {"shape": "IdType", "documentation": "<p>A unique identifier for the kdb environment.</p>"}, "iamRole": {"shape": "RoleArn", "documentation": "<p>The IAM role ARN that is associated with the user.</p>"}}}, "GetKxVolumeRequest": {"type": "structure", "required": ["environmentId", "volumeName"], "members": {"environmentId": {"shape": "KxEnvironmentId", "documentation": "<p>A unique identifier for the kdb environment, whose clusters can attach to the volume. </p>", "location": "uri", "locationName": "environmentId"}, "volumeName": {"shape": "KxVolumeName", "documentation": "<p>A unique identifier for the volume.</p>", "location": "uri", "locationName": "volumeName"}}}, "GetKxVolumeResponse": {"type": "structure", "members": {"environmentId": {"shape": "KxEnvironmentId", "documentation": "<p>A unique identifier for the kdb environment, whose clusters can attach to the volume. </p>"}, "volumeName": {"shape": "KxVolumeName", "documentation": "<p> A unique identifier for the volume.</p>"}, "volumeType": {"shape": "KxVolumeType", "documentation": "<p> The type of file system volume. Currently, FinSpace only supports <code>NAS_1</code> volume type. </p>"}, "volumeArn": {"shape": "KxVolumeArn", "documentation": "<p> The ARN identifier of the volume. </p>"}, "nas1Configuration": {"shape": "KxNAS1Configuration", "documentation": "<p> Specifies the configuration for the Network attached storage (NAS_1) file system volume.</p>"}, "status": {"shape": "KxVolumeStatus", "documentation": "<p>The status of volume creation.</p> <ul> <li> <p>CREATING – The volume creation is in progress.</p> </li> <li> <p>CREATE_FAILED – The volume creation has failed.</p> </li> <li> <p>ACTIVE – The volume is active.</p> </li> <li> <p>UPDATING – The volume is in the process of being updated.</p> </li> <li> <p>UPDATE_FAILED – The update action failed.</p> </li> <li> <p>UPDATED – The volume is successfully updated.</p> </li> <li> <p>DELETING – The volume is in the process of being deleted.</p> </li> <li> <p>DELETE_FAILED – The system failed to delete the volume.</p> </li> <li> <p>DELETED – The volume is successfully deleted.</p> </li> </ul>"}, "statusReason": {"shape": "KxVolumeStatusReason", "documentation": "<p>The error message when a failed state occurs. </p>"}, "createdTimestamp": {"shape": "Timestamp", "documentation": "<p> The timestamp at which the volume was created in FinSpace. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as *************. </p>"}, "description": {"shape": "Description", "documentation": "<p> A description of the volume. </p>"}, "azMode": {"shape": "KxAzMode", "documentation": "<p>The number of availability zones you want to assign per volume. Currently, FinSpace only supports <code>SINGLE</code> for volumes. This places dataview in a single AZ.</p>"}, "availabilityZoneIds": {"shape": "AvailabilityZoneIds", "documentation": "<p>The identifier of the availability zones.</p>"}, "lastModifiedTimestamp": {"shape": "Timestamp", "documentation": "<p>The last time that the volume was updated in FinSpace. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as *************.</p>"}, "attachedClusters": {"shape": "KxAttachedClusters", "documentation": "<p> A list of cluster identifiers that a volume is attached to. </p>"}}}, "IPAddressType": {"type": "string", "enum": ["IP_V4"]}, "IcmpTypeCode": {"type": "structure", "required": ["type", "code"], "members": {"type": {"shape": "IcmpTypeOrCode", "documentation": "<p>The ICMP type. A value of <i>-1</i> means all types. </p>"}, "code": {"shape": "IcmpTypeOrCode", "documentation": "<p> The ICMP code. A value of <i>-1</i> means all codes for the specified ICMP type. </p>"}}, "documentation": "<p> Defines the ICMP protocol that consists of the ICMP type and code. </p>"}, "IcmpTypeOrCode": {"type": "integer"}, "IdType": {"type": "string", "max": 26, "min": 1, "pattern": "^[a-zA-Z0-9]{1,26}$"}, "InitializationScriptFilePath": {"type": "string", "max": 255, "min": 1, "pattern": "^[a-zA-Z0-9\\_\\-\\.\\/\\\\]+$"}, "InternalServerException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}}, "documentation": "<p>The request processing has failed because of an unknown error, exception or failure.</p>", "error": {"httpStatusCode": 500}, "exception": true}, "InvalidRequestException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}}, "documentation": "<p>The request is invalid. Something is wrong with the input to the request.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "KmsKeyARN": {"type": "string", "max": 1000, "min": 1, "pattern": "^arn:aws:kms:.*:\\d+:.*$"}, "KmsKeyId": {"type": "string", "max": 1000, "min": 1, "pattern": "^[a-zA-Z-0-9-:\\/]*$"}, "KxAttachedCluster": {"type": "structure", "members": {"clusterName": {"shape": "KxClusterName", "documentation": "<p>A unique name for the attached cluster.</p>"}, "clusterType": {"shape": "KxClusterType", "documentation": "<p>Specifies the type of cluster. The volume for TP and RDB cluster types will be used for TP logs.</p>"}, "clusterStatus": {"shape": "KxClusterStatus", "documentation": "<p>The status of the attached cluster.</p> <ul> <li> <p>PENDING – The cluster is pending creation.</p> </li> <li> <p>CREATING – The cluster creation process is in progress.</p> </li> <li> <p>CREATE_FAILED – The cluster creation process has failed.</p> </li> <li> <p>RUNNING – The cluster creation process is running.</p> </li> <li> <p>UPDATING – The cluster is in the process of being updated.</p> </li> <li> <p>DELETING – The cluster is in the process of being deleted.</p> </li> <li> <p>DELETED – The cluster has been deleted.</p> </li> <li> <p>DELETE_FAILED – The cluster failed to delete.</p> </li> </ul>"}}, "documentation": "<p>The structure containing the metadata of the attached clusters.</p>"}, "KxAttachedClusters": {"type": "list", "member": {"shape": "KxAttachedCluster"}}, "KxAzMode": {"type": "string", "enum": ["SINGLE", "MULTI"]}, "KxCacheStorageConfiguration": {"type": "structure", "required": ["type", "size"], "members": {"type": {"shape": "KxCacheStorageType", "documentation": "<p>The type of cache storage. The valid values are: </p> <ul> <li> <p>CACHE_1000 – This type provides at least 1000 MB/s disk access throughput. </p> </li> <li> <p>CACHE_250 – This type provides at least 250 MB/s disk access throughput. </p> </li> <li> <p>CACHE_12 – This type provides at least 12 MB/s disk access throughput. </p> </li> </ul> <p>For cache type <code>CACHE_1000</code> and <code>CACHE_250</code> you can select cache size as 1200 GB or increments of 2400 GB. For cache type <code>CACHE_12</code> you can select the cache size in increments of 6000 GB.</p>"}, "size": {"shape": "KxCacheStorageSize", "documentation": "<p>The size of cache in Gigabytes.</p>"}}, "documentation": "<p>The configuration for read only disk cache associated with a cluster.</p>"}, "KxCacheStorageConfigurations": {"type": "list", "member": {"shape": "KxCacheStorageConfiguration"}}, "KxCacheStorageSize": {"type": "integer"}, "KxCacheStorageType": {"type": "string", "max": 10, "min": 8}, "KxChangesetListEntry": {"type": "structure", "members": {"changesetId": {"shape": "ChangesetId", "documentation": "<p>A unique identifier for the changeset.</p>"}, "createdTimestamp": {"shape": "Timestamp", "documentation": "<p>The timestamp at which the changeset was created in FinSpace. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as *************.</p>"}, "activeFromTimestamp": {"shape": "Timestamp", "documentation": "<p>Beginning time from which the changeset is active. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as *************.</p>"}, "lastModifiedTimestamp": {"shape": "Timestamp", "documentation": "<p>The timestamp at which the changeset was modified. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as *************.</p>"}, "status": {"shape": "ChangesetStatus", "documentation": "<p> Status of the changeset.</p> <ul> <li> <p>Pending – Changeset creation is pending.</p> </li> <li> <p>Processing – Changeset creation is running.</p> </li> <li> <p>Failed – Changeset creation has failed.</p> </li> <li> <p>Complete – Changeset creation has succeeded.</p> </li> </ul>"}}, "documentation": "<p>Details of changeset.</p>"}, "KxChangesets": {"type": "list", "member": {"shape": "KxChangesetListEntry"}}, "KxCluster": {"type": "structure", "members": {"status": {"shape": "KxClusterStatus", "documentation": "<p>The status of a cluster.</p> <ul> <li> <p>PENDING – The cluster is pending creation.</p> </li> <li> <p>CREATING –The cluster creation process is in progress.</p> </li> <li> <p>CREATE_FAILED– The cluster creation process has failed.</p> </li> <li> <p>RUNNING – The cluster creation process is running.</p> </li> <li> <p>UPDATING – The cluster is in the process of being updated.</p> </li> <li> <p> DELETING – The cluster is in the process of being deleted.</p> </li> <li> <p>DELETED – The cluster has been deleted.</p> </li> <li> <p>DELETE_FAILED – The cluster failed to delete.</p> </li> </ul>"}, "statusReason": {"shape": "KxClusterStatusReason", "documentation": "<p>The error message when a failed state occurs. </p>"}, "clusterName": {"shape": "KxClusterName", "documentation": "<p>A unique name for the cluster.</p>"}, "clusterType": {"shape": "KxClusterType", "documentation": "<p>Specifies the type of KDB database that is being created. The following types are available: </p> <ul> <li> <p>HDB – A Historical Database. The data is only accessible with read-only permissions from one of the FinSpace managed kdb databases mounted to the cluster.</p> </li> <li> <p>RDB – A Realtime Database. This type of database captures all the data from a ticker plant and stores it in memory until the end of day, after which it writes all of its data to a disk and reloads the HDB. This cluster type requires local storage for temporary storage of data during the savedown process. If you specify this field in your request, you must provide the <code>savedownStorageConfiguration</code> parameter.</p> </li> <li> <p>GATEWAY – A gateway cluster allows you to access data across processes in kdb systems. It allows you to create your own routing logic using the initialization scripts and custom code. This type of cluster does not require a writable local storage.</p> </li> <li> <p>GP – A general purpose cluster allows you to quickly iterate on code during development by granting greater access to system commands and enabling a fast reload of custom code. This cluster type can optionally mount databases including cache and savedown storage. For this cluster type, the node count is fixed at 1. It does not support autoscaling and supports only <code>SINGLE</code> AZ mode.</p> </li> <li> <p>Tickerplant – A tickerplant cluster allows you to subscribe to feed handlers based on IAM permissions. It can publish to RDBs, other Tickerplants, and real-time subscribers (RTS). Tickerplants can persist messages to log, which is readable by any RDB environment. It supports only single-node that is only one kdb process.</p> </li> </ul>"}, "clusterDescription": {"shape": "KxClusterDescription", "documentation": "<p>A description of the cluster.</p>"}, "releaseLabel": {"shape": "ReleaseLabel", "documentation": "<p>A version of the FinSpace managed kdb to run.</p>"}, "volumes": {"shape": "Volumes", "documentation": "<p> A list of volumes attached to the cluster. </p>"}, "initializationScript": {"shape": "InitializationScriptFilePath", "documentation": "<p>Specifies a Q program that will be run at launch of a cluster. It is a relative path within <i>.zip</i> file that contains the custom code, which will be loaded on the cluster. It must include the file name itself. For example, <code>somedir/init.q</code>.</p>"}, "executionRole": {"shape": "ExecutionRoleArn", "documentation": "<p> An IAM role that defines a set of permissions associated with a cluster. These permissions are assumed when a cluster attempts to access another cluster. </p>"}, "azMode": {"shape": "KxAzMode", "documentation": "<p>The number of availability zones assigned per cluster. This can be one of the following:</p> <ul> <li> <p> <code>SINGLE</code> – Assigns one availability zone per cluster.</p> </li> <li> <p> <code>MULTI</code> – Assigns all the availability zones per cluster.</p> </li> </ul>"}, "availabilityZoneId": {"shape": "AvailabilityZoneId", "documentation": "<p> The availability zone identifiers for the requested regions. </p>"}, "lastModifiedTimestamp": {"shape": "Timestamp", "documentation": "<p>The last time that the cluster was modified. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as *************.</p>"}, "createdTimestamp": {"shape": "Timestamp", "documentation": "<p>The timestamp at which the cluster was created in FinSpace. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as *************.</p>"}}, "documentation": "<p>The details of a kdb cluster.</p>"}, "KxClusterCodeDeploymentConfiguration": {"type": "structure", "required": ["deploymentStrategy"], "members": {"deploymentStrategy": {"shape": "KxClusterCodeDeploymentStrategy", "documentation": "<p> The type of deployment that you want on a cluster. </p> <ul> <li> <p>ROLLING – This options updates the cluster by stopping the exiting q process and starting a new q process with updated configuration.</p> </li> <li> <p>NO_RESTART – This option updates the cluster without stopping the running q process. It is only available for <code>GP</code> type cluster. This option is quicker as it reduces the turn around time to update configuration on a cluster. </p> <p>With this deployment mode, you cannot update the <code>initializationScript</code> and <code>commandLineArguments</code> parameters.</p> </li> <li> <p>FORCE – This option updates the cluster by immediately stopping all the running processes before starting up new ones with the updated configuration. </p> </li> </ul>"}}, "documentation": "<p> The configuration that allows you to choose how you want to update code on a cluster. Depending on the option you choose, you can reduce the time it takes to update the cluster. </p>"}, "KxClusterCodeDeploymentStrategy": {"type": "string", "enum": ["NO_RESTART", "ROLLING", "FORCE"]}, "KxClusterDescription": {"type": "string", "max": 1000, "min": 1, "pattern": "^[a-zA-Z0-9\\_\\-\\.\\s]+$"}, "KxClusterName": {"type": "string", "max": 63, "min": 3, "pattern": "^[a-zA-Z0-9][a-zA-Z0-9-_]*[a-zA-Z0-9]$"}, "KxClusterNameList": {"type": "list", "member": {"shape": "KxClusterName"}}, "KxClusterNodeIdString": {"type": "string", "max": 40, "min": 1}, "KxClusterStatus": {"type": "string", "enum": ["PENDING", "CREATING", "CREATE_FAILED", "RUNNING", "UPDATING", "DELETING", "DELETED", "DELETE_FAILED"]}, "KxClusterStatusReason": {"type": "string", "max": 250, "min": 1, "pattern": "^[a-zA-Z0-9\\_\\-\\.\\s]+$"}, "KxClusterType": {"type": "string", "enum": ["HDB", "RDB", "GATEWAY", "GP", "TICKERPLANT"]}, "KxClusters": {"type": "list", "member": {"shape": "KxCluster"}}, "KxCommandLineArgument": {"type": "structure", "members": {"key": {"shape": "KxCommandLineArgumentKey", "documentation": "<p>The name of the key.</p>"}, "value": {"shape": "KxCommandLineArgumentValue", "documentation": "<p>The value of the key.</p>"}}, "documentation": "<p>Defines the key-value pairs to make them available inside the cluster.</p>"}, "KxCommandLineArgumentKey": {"type": "string", "max": 1024, "min": 1, "pattern": "^(?![Aa][Ww][Ss])(s|([a-zA-Z][a-zA-Z0-9_]+))|(AWS_ZIP_DEFAULT)"}, "KxCommandLineArgumentValue": {"type": "string", "max": 1024, "min": 1, "pattern": "^[a-zA-Z0-9_:./,; ]+$"}, "KxCommandLineArguments": {"type": "list", "member": {"shape": "KxCommandLineArgument"}}, "KxDatabaseCacheConfiguration": {"type": "structure", "required": ["cacheType", "dbPaths"], "members": {"cacheType": {"shape": "KxCacheStorageType", "documentation": "<p>The type of disk cache. This parameter is used to map the database path to cache storage. The valid values are:</p> <ul> <li> <p>CACHE_1000 – This type provides at least 1000 MB/s disk access throughput. </p> </li> </ul>"}, "dbPaths": {"shape": "DbPaths", "documentation": "<p>Specifies the portions of database that will be loaded into the cache for access.</p>"}, "dataviewName": {"shape": "KxDataviewName", "documentation": "<p> The name of the dataview to be used for caching historical data on disk. </p>"}}, "documentation": "<p>The structure of database cache configuration that is used for mapping database paths to cache types in clusters.</p>"}, "KxDatabaseCacheConfigurations": {"type": "list", "member": {"shape": "KxDatabaseCacheConfiguration"}}, "KxDatabaseConfiguration": {"type": "structure", "required": ["databaseName"], "members": {"databaseName": {"shape": "DatabaseName", "documentation": "<p>The name of the kdb database. When this parameter is specified in the structure, S3 with the whole database is included by default.</p>"}, "cacheConfigurations": {"shape": "KxDatabaseCacheConfigurations", "documentation": "<p>Configuration details for the disk cache used to increase performance reading from a kdb database mounted to the cluster.</p>"}, "changesetId": {"shape": "ChangesetId", "documentation": "<p>A unique identifier of the changeset that is associated with the cluster.</p>"}, "dataviewName": {"shape": "KxDataviewName", "documentation": "<p> The name of the dataview to be used for caching historical data on disk. </p>"}, "dataviewConfiguration": {"shape": "KxDataviewConfiguration", "documentation": "<p> The configuration of the dataview to be used with specified cluster. </p>"}}, "documentation": "<p>The configuration of data that is available for querying from this database.</p>"}, "KxDatabaseConfigurations": {"type": "list", "member": {"shape": "KxDatabaseConfiguration"}}, "KxDatabaseListEntry": {"type": "structure", "members": {"databaseName": {"shape": "DatabaseName", "documentation": "<p>The name of the kdb database.</p>"}, "createdTimestamp": {"shape": "Timestamp", "documentation": "<p>The timestamp at which the database was created in FinSpace. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as *************.</p>"}, "lastModifiedTimestamp": {"shape": "Timestamp", "documentation": "<p>The last time that the database was modified. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as *************.</p>"}}, "documentation": "<p>Details about a FinSpace managed kdb database</p>"}, "KxDatabases": {"type": "list", "member": {"shape": "KxDatabaseListEntry"}}, "KxDataviewActiveVersion": {"type": "structure", "members": {"changesetId": {"shape": "ChangesetId", "documentation": "<p>A unique identifier for the changeset.</p>"}, "segmentConfigurations": {"shape": "KxDataviewSegmentConfigurationList", "documentation": "<p> The configuration that contains the database path of the data that you want to place on each selected volume. Each segment must have a unique database path for each volume. If you do not explicitly specify any database path for a volume, they are accessible from the cluster through the default S3/object store segment. </p>"}, "attachedClusters": {"shape": "AttachedClusterList", "documentation": "<p> The list of clusters that are currently using this dataview. </p>"}, "createdTimestamp": {"shape": "Timestamp", "documentation": "<p> The timestamp at which the dataview version was active. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as *************.</p>"}, "versionId": {"shape": "VersionId", "documentation": "<p> A unique identifier of the active version.</p>"}}, "documentation": "<p> The active version of the dataview that is currently in use by this cluster. </p>"}, "KxDataviewActiveVersionList": {"type": "list", "member": {"shape": "KxDataviewActiveVersion"}}, "KxDataviewConfiguration": {"type": "structure", "members": {"dataviewName": {"shape": "KxDataviewName", "documentation": "<p> The unique identifier of the dataview.</p>"}, "dataviewVersionId": {"shape": "VersionId", "documentation": "<p> The version of the dataview corresponding to a given changeset. </p>"}, "changesetId": {"shape": "ChangesetId", "documentation": "<p>A unique identifier for the changeset.</p>"}, "segmentConfigurations": {"shape": "KxDataviewSegmentConfigurationList", "documentation": "<p> The db path and volume configuration for the segmented database.</p>"}}, "documentation": "<p> The structure that stores the configuration details of a dataview.</p>"}, "KxDataviewListEntry": {"type": "structure", "members": {"environmentId": {"shape": "EnvironmentId", "documentation": "<p>A unique identifier for the kdb environment.</p>"}, "databaseName": {"shape": "DatabaseName", "documentation": "<p> A unique identifier of the database.</p>"}, "dataviewName": {"shape": "KxDataviewName", "documentation": "<p> A unique identifier of the dataview.</p>"}, "azMode": {"shape": "KxAzMode", "documentation": "<p>The number of availability zones you want to assign per volume. Currently, FinSpace only supports <code>SINGLE</code> for volumes. This places dataview in a single AZ.</p>"}, "availabilityZoneId": {"shape": "AvailabilityZoneId", "documentation": "<p> The identifier of the availability zones. </p>"}, "changesetId": {"shape": "ChangesetId", "documentation": "<p>A unique identifier for the changeset.</p>"}, "segmentConfigurations": {"shape": "KxDataviewSegmentConfigurationList", "documentation": "<p> The configuration that contains the database path of the data that you want to place on each selected volume. Each segment must have a unique database path for each volume. If you do not explicitly specify any database path for a volume, they are accessible from the cluster through the default S3/object store segment. </p>"}, "activeVersions": {"shape": "KxDataviewActiveVersionList", "documentation": "<p> The active changeset versions for the given dataview entry. </p>"}, "status": {"shape": "KxDataviewStatus", "documentation": "<p> The status of a given dataview entry. </p>"}, "description": {"shape": "Description", "documentation": "<p> A description for the dataview list entry.</p>"}, "autoUpdate": {"shape": "booleanValue", "documentation": "<p> The option to specify whether you want to apply all the future additions and corrections automatically to the dataview when you ingest new changesets. The default value is false. </p>"}, "readWrite": {"shape": "booleanValue", "documentation": "<p> Returns True if the dataview is created as writeable and False otherwise. </p>"}, "createdTimestamp": {"shape": "Timestamp", "documentation": "<p> The timestamp at which the dataview list entry was created in FinSpace. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as *************.</p>"}, "lastModifiedTimestamp": {"shape": "Timestamp", "documentation": "<p>The last time that the dataview list was updated in FinSpace. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as *************.</p>"}, "statusReason": {"shape": "KxDataviewStatusReason", "documentation": "<p> The error message when a failed state occurs. </p>"}}, "documentation": "<p> A collection of kdb dataview entries. </p>"}, "KxDataviewName": {"type": "string", "max": 63, "min": 3, "pattern": "^[a-zA-Z0-9][a-zA-Z0-9-_]*[a-zA-Z0-9]$"}, "KxDataviewSegmentConfiguration": {"type": "structure", "required": ["dbPaths", "volumeName"], "members": {"dbPaths": {"shape": "SegmentConfigurationDbPathList", "documentation": "<p> The database path of the data that you want to place on each selected volume for the segment. Each segment must have a unique database path for each volume.</p>"}, "volumeName": {"shape": "KxVolumeName", "documentation": "<p> The name of the volume where you want to add data. </p>"}, "onDemand": {"shape": "booleanValue", "documentation": "<p>Enables on-demand caching on the selected database path when a particular file or a column of the database is accessed. When on demand caching is <b>True</b>, dataviews perform minimal loading of files on the filesystem as needed. When it is set to <b>False</b>, everything is cached. The default value is <b>False</b>. </p>"}}, "documentation": "<p> The configuration that contains the database path of the data that you want to place on each selected volume. Each segment must have a unique database path for each volume. If you do not explicitly specify any database path for a volume, they are accessible from the cluster through the default S3/object store segment. </p>"}, "KxDataviewSegmentConfigurationList": {"type": "list", "member": {"shape": "KxDataviewSegmentConfiguration"}, "max": 50, "min": 0}, "KxDataviewStatus": {"type": "string", "enum": ["CREATING", "ACTIVE", "UPDATING", "FAILED", "DELETING"]}, "KxDataviewStatusReason": {"type": "string", "max": 250, "min": 1, "pattern": "^[a-zA-Z0-9\\_\\-\\.\\s]+$"}, "KxDataviews": {"type": "list", "member": {"shape": "KxDataviewListEntry"}}, "KxDeploymentConfiguration": {"type": "structure", "required": ["deploymentStrategy"], "members": {"deploymentStrategy": {"shape": "KxDeploymentStrategy", "documentation": "<p> The type of deployment that you want on a cluster. </p> <ul> <li> <p>ROLLING – This options updates the cluster by stopping the exiting q process and starting a new q process with updated configuration.</p> </li> <li> <p>NO_RESTART – This option updates the cluster without stopping the running q process. It is only available for <code>HDB</code> type cluster. This option is quicker as it reduces the turn around time to update configuration on a cluster. </p> <p>With this deployment mode, you cannot update the <code>initializationScript</code> and <code>commandLineArguments</code> parameters.</p> </li> </ul>"}}, "documentation": "<p> The configuration that allows you to choose how you want to update the databases on a cluster. Depending on the option you choose, you can reduce the time it takes to update the cluster. </p>"}, "KxDeploymentStrategy": {"type": "string", "enum": ["NO_RESTART", "ROLLING"]}, "KxEnvironment": {"type": "structure", "members": {"name": {"shape": "KxEnvironmentName", "documentation": "<p>The name of the kdb environment.</p>"}, "environmentId": {"shape": "IdType", "documentation": "<p>A unique identifier for the kdb environment.</p>"}, "awsAccountId": {"shape": "IdType", "documentation": "<p>The unique identifier of the AWS account in which you create the kdb environment.</p>"}, "status": {"shape": "EnvironmentStatus", "documentation": "<p>The status of the environment creation. </p> <ul> <li> <p>CREATE_REQUESTED – Environment creation has been requested.</p> </li> <li> <p>CREATING – Environment is in the process of being created.</p> </li> <li> <p>FAILED_CREATION – Environment creation has failed.</p> </li> <li> <p>CREATED – Environment is successfully created and is currently active.</p> </li> <li> <p>DELETE REQUESTED – Environment deletion has been requested.</p> </li> <li> <p>DELETING – Environment is in the process of being deleted.</p> </li> <li> <p>RETRY_DELETION – Initial environment deletion failed, system is reattempting delete.</p> </li> <li> <p>DELETED – Environment has been deleted.</p> </li> <li> <p>FAILED_DELETION – Environment deletion has failed.</p> </li> </ul>"}, "tgwStatus": {"shape": "tgwStatus", "documentation": "<p>The status of the network configuration.</p>"}, "dnsStatus": {"shape": "dnsStatus", "documentation": "<p>The status of DNS configuration.</p>"}, "errorMessage": {"shape": "EnvironmentErrorMessage", "documentation": "<p>Specifies the error message that appears if a flow fails. </p>"}, "description": {"shape": "Description", "documentation": "<p>A description of the kdb environment.</p>"}, "environmentArn": {"shape": "EnvironmentArn", "documentation": "<p>The Amazon Resource Name (ARN) of your kdb environment.</p>"}, "kmsKeyId": {"shape": "KmsKeyId", "documentation": "<p>The unique identifier of the KMS key.</p>"}, "dedicatedServiceAccountId": {"shape": "IdType", "documentation": "<p>A unique identifier for the AWS environment infrastructure account.</p>"}, "transitGatewayConfiguration": {"shape": "TransitGatewayConfiguration", "documentation": "<p>Specifies the transit gateway and network configuration to connect the kdb environment to an internal network.</p>"}, "customDNSConfiguration": {"shape": "CustomDNSConfiguration", "documentation": "<p>A list of DNS server name and server IP. This is used to set up Route-53 outbound resolvers.</p>"}, "creationTimestamp": {"shape": "Timestamp", "documentation": "<p>The timestamp at which the kdb environment was created in FinSpace. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as *************.</p>"}, "updateTimestamp": {"shape": "Timestamp", "documentation": "<p>The timestamp at which the kdb environment was modified in FinSpace. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as *************.</p>"}, "availabilityZoneIds": {"shape": "AvailabilityZoneIds", "documentation": "<p>The identifier of the availability zones where subnets for the environment are created.</p>"}, "certificateAuthorityArn": {"shape": "stringValueLength1to255", "documentation": "<p>The Amazon Resource Name (ARN) of the certificate authority:</p>"}}, "documentation": "<p>The details of a kdb environment.</p>"}, "KxEnvironmentId": {"type": "string", "max": 32, "min": 1, "pattern": "^[a-z0-9]+$"}, "KxEnvironmentList": {"type": "list", "member": {"shape": "KxEnvironment"}}, "KxEnvironmentName": {"type": "string", "max": 63, "min": 3, "pattern": "^[a-zA-Z0-9][a-zA-Z0-9-_]*[a-zA-Z0-9]$"}, "KxHostType": {"type": "string", "max": 32, "min": 1, "pattern": "^[a-zA-Z0-9._]+"}, "KxNAS1Configuration": {"type": "structure", "members": {"type": {"shape": "KxNAS1Type", "documentation": "<p> The type of the network attached storage. </p>"}, "size": {"shape": "KxNAS1Size", "documentation": "<p> The size of the network attached storage. For storage type <code>SSD_1000</code> and <code>SSD_250</code> you can select the minimum size as 1200 GB or increments of 2400 GB. For storage type <code>HDD_12</code> you can select the minimum size as 6000 GB or increments of 6000 GB.</p>"}}, "documentation": "<p> The structure containing the size and type of the network attached storage (NAS_1) file system volume. </p>"}, "KxNAS1Size": {"type": "integer", "min": 1200}, "KxNAS1Type": {"type": "string", "enum": ["SSD_1000", "SSD_250", "HDD_12"]}, "KxNode": {"type": "structure", "members": {"nodeId": {"shape": "KxClusterNodeIdString", "documentation": "<p>A unique identifier for the node.</p>"}, "availabilityZoneId": {"shape": "AvailabilityZoneId", "documentation": "<p>The identifier of the availability zones where subnets for the environment are created.</p>"}, "launchTime": {"shape": "Timestamp", "documentation": "<p>The time when a particular node is started. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as *************.</p>"}, "status": {"shape": "KxNodeStatus", "documentation": "<p> Specifies the status of the cluster nodes. </p> <ul> <li> <p> <code>RUNNING</code> – The node is actively serving.</p> </li> <li> <p> <code>PROVISIONING</code> – The node is being prepared.</p> </li> </ul>"}}, "documentation": "<p>A structure that stores metadata for a kdb node.</p>"}, "KxNodeStatus": {"type": "string", "enum": ["RUNNING", "PROVISIONING"]}, "KxNodeSummaries": {"type": "list", "member": {"shape": "KxNode"}}, "KxSavedownStorageConfiguration": {"type": "structure", "members": {"type": {"shape": "KxSavedownStorageType", "documentation": "<p>The type of writeable storage space for temporarily storing your savedown data. The valid values are:</p> <ul> <li> <p>SDS01 – This type represents 3000 IOPS and io2 ebs volume type.</p> </li> </ul>"}, "size": {"shape": "KxSavedownStorageSize", "documentation": "<p>The size of temporary storage in gibibytes.</p>"}, "volumeName": {"shape": "KxVolumeName", "documentation": "<p> The name of the kdb volume that you want to use as writeable save-down storage for clusters. </p>"}}, "documentation": "<p>The size and type of temporary storage that is used to hold data during the savedown process. All the data written to this storage space is lost when the cluster node is restarted.</p>"}, "KxSavedownStorageSize": {"type": "integer", "max": 16000, "min": 10}, "KxSavedownStorageType": {"type": "string", "enum": ["SDS01"]}, "KxScalingGroup": {"type": "structure", "members": {"scalingGroupName": {"shape": "KxScalingGroupName", "documentation": "<p>A unique identifier for the kdb scaling group. </p>"}, "hostType": {"shape": "KxHostType", "documentation": "<p> The memory and CPU capabilities of the scaling group host on which FinSpace Managed kdb clusters will be placed.</p> <p>You can add one of the following values:</p> <ul> <li> <p> <code>kx.sg.large</code> – The host type with a configuration of 16 GiB memory and 2 vCPUs.</p> </li> <li> <p> <code>kx.sg.xlarge</code> – The host type with a configuration of 32 GiB memory and 4 vCPUs.</p> </li> <li> <p> <code>kx.sg.2xlarge</code> – The host type with a configuration of 64 GiB memory and 8 vCPUs.</p> </li> <li> <p> <code>kx.sg.4xlarge</code> – The host type with a configuration of 108 GiB memory and 16 vCPUs.</p> </li> <li> <p> <code>kx.sg.8xlarge</code> – The host type with a configuration of 216 GiB memory and 32 vCPUs.</p> </li> <li> <p> <code>kx.sg.16xlarge</code> – The host type with a configuration of 432 GiB memory and 64 vCPUs.</p> </li> <li> <p> <code>kx.sg.32xlarge</code> – The host type with a configuration of 864 GiB memory and 128 vCPUs.</p> </li> <li> <p> <code>kx.sg1.16xlarge</code> – The host type with a configuration of 1949 GiB memory and 64 vCPUs.</p> </li> <li> <p> <code>kx.sg1.24xlarge</code> – The host type with a configuration of 2948 GiB memory and 96 vCPUs.</p> </li> </ul>"}, "clusters": {"shape": "KxClusterNameList", "documentation": "<p> The list of clusters currently active in a given scaling group. </p>"}, "availabilityZoneId": {"shape": "AvailabilityZoneId", "documentation": "<p>The identifier of the availability zones.</p>"}, "status": {"shape": "KxScalingGroupStatus", "documentation": "<p> The status of scaling groups. </p>"}, "statusReason": {"shape": "KxClusterStatusReason", "documentation": "<p> The error message when a failed state occurs. </p>"}, "lastModifiedTimestamp": {"shape": "Timestamp", "documentation": "<p> The last time that the scaling group was updated in FinSpace. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as *************. </p>"}, "createdTimestamp": {"shape": "Timestamp", "documentation": "<p> The timestamp at which the scaling group was created in FinSpace. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as *************. </p>"}}, "documentation": "<p> A structure for storing metadata of scaling group.</p>"}, "KxScalingGroupConfiguration": {"type": "structure", "required": ["scalingGroupName", "memoryReservation", "nodeCount"], "members": {"scalingGroupName": {"shape": "KxScalingGroupName", "documentation": "<p>A unique identifier for the kdb scaling group. </p>"}, "memoryLimit": {"shape": "MemoryMib", "documentation": "<p> An optional hard limit on the amount of memory a kdb cluster can use. </p>"}, "memoryReservation": {"shape": "MemoryMib", "documentation": "<p> A reservation of the minimum amount of memory that should be available on the scaling group for a kdb cluster to be successfully placed in a scaling group. </p>"}, "nodeCount": {"shape": "ClusterNodeCount", "documentation": "<p> The number of kdb cluster nodes. </p>"}, "cpu": {"shape": "CpuCount", "documentation": "<p> The number of vCPUs that you want to reserve for each node of this kdb cluster on the scaling group host. </p>"}}, "documentation": "<p>The structure that stores the capacity configuration details of a scaling group.</p>"}, "KxScalingGroupList": {"type": "list", "member": {"shape": "KxScalingGroup"}}, "KxScalingGroupName": {"type": "string", "max": 63, "min": 3, "pattern": "^[a-zA-Z0-9][a-zA-Z0-9-_]*[a-zA-Z0-9]$"}, "KxScalingGroupStatus": {"type": "string", "enum": ["CREATING", "CREATE_FAILED", "ACTIVE", "DELETING", "DELETED", "DELETE_FAILED"]}, "KxUser": {"type": "structure", "members": {"userArn": {"shape": "KxUserArn", "documentation": "<p> The Amazon Resource Name (ARN) that identifies the user. For more information about ARNs and how to use ARNs in policies, see <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/reference_identifiers.html\">IAM Identifiers</a> in the <i>IAM User Guide</i>. </p>"}, "userName": {"shape": "KxUserNameString", "documentation": "<p>A unique identifier for the user.</p>"}, "iamRole": {"shape": "RoleArn", "documentation": "<p>The IAM role ARN that is associated with the user.</p>"}, "createTimestamp": {"shape": "Timestamp", "documentation": "<p>The timestamp at which the kdb user was created. </p>"}, "updateTimestamp": {"shape": "Timestamp", "documentation": "<p>The timestamp at which the kdb user was updated. </p>"}}, "documentation": "<p>A structure that stores metadata for a kdb user.</p>"}, "KxUserArn": {"type": "string", "max": 2048, "min": 20, "pattern": "^arn:aws:finspace:[A-Za-z0-9_/.-]{0,63}:\\d+:kxEnvironment/[0-9A-Za-z_-]{1,128}/kxUser/[0-9A-Za-z_-]{1,128}$"}, "KxUserList": {"type": "list", "member": {"shape": "KxUser"}}, "KxUserNameString": {"type": "string", "max": 50, "min": 1, "pattern": "^[0-9A-Za-z_-]{1,50}$"}, "KxVolume": {"type": "structure", "members": {"volumeName": {"shape": "KxVolumeName", "documentation": "<p>A unique identifier for the volume.</p>"}, "volumeType": {"shape": "KxVolumeType", "documentation": "<p> The type of file system volume. Currently, FinSpace only supports <code>NAS_1</code> volume type. </p>"}, "status": {"shape": "KxVolumeStatus", "documentation": "<p>The status of volume.</p> <ul> <li> <p>CREATING – The volume creation is in progress.</p> </li> <li> <p>CREATE_FAILED – The volume creation has failed.</p> </li> <li> <p>ACTIVE – The volume is active.</p> </li> <li> <p>UPDATING – The volume is in the process of being updated.</p> </li> <li> <p>UPDATE_FAILED – The update action failed.</p> </li> <li> <p>UPDATED – The volume is successfully updated.</p> </li> <li> <p>DELETING – The volume is in the process of being deleted.</p> </li> <li> <p>DELETE_FAILED – The system failed to delete the volume.</p> </li> <li> <p>DELETED – The volume is successfully deleted.</p> </li> </ul>"}, "description": {"shape": "Description", "documentation": "<p> A description of the volume. </p>"}, "statusReason": {"shape": "KxVolumeStatusReason", "documentation": "<p>The error message when a failed state occurs. </p>"}, "azMode": {"shape": "KxAzMode", "documentation": "<p>The number of availability zones you want to assign per volume. Currently, FinSpace only supports <code>SINGLE</code> for volumes. This places dataview in a single AZ.</p>"}, "availabilityZoneIds": {"shape": "AvailabilityZoneIds", "documentation": "<p>The identifier of the availability zones.</p>"}, "createdTimestamp": {"shape": "Timestamp", "documentation": "<p> The timestamp at which the volume was created in FinSpace. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as *************.</p>"}, "lastModifiedTimestamp": {"shape": "Timestamp", "documentation": "<p>The last time that the volume was updated in FinSpace. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as *************.</p>"}}, "documentation": "<p> The structure that contains the metadata of the volume. </p>"}, "KxVolumeArn": {"type": "string", "max": 2048, "min": 20, "pattern": "^arn:aws:finspace:[A-Za-z0-9_/.-]{0,63}:\\d+:kxEnvironment/[0-9A-Za-z_-]{1,128}(/kxSharedVolume/[a-zA-Z0-9_-]{1,255})?$"}, "KxVolumeName": {"type": "string", "max": 63, "min": 3, "pattern": "^[a-zA-Z0-9][a-zA-Z0-9-_]*[a-zA-Z0-9]$"}, "KxVolumeStatus": {"type": "string", "enum": ["CREATING", "CREATE_FAILED", "ACTIVE", "UPDATING", "UPDATED", "UPDATE_FAILED", "DELETING", "DELETED", "DELETE_FAILED"]}, "KxVolumeStatusReason": {"type": "string", "max": 250, "min": 1, "pattern": "^[a-zA-Z0-9\\_\\-\\.\\s]+$"}, "KxVolumeType": {"type": "string", "enum": ["NAS_1"]}, "KxVolumes": {"type": "list", "member": {"shape": "KxVolume"}}, "LimitExceededException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}}, "documentation": "<p>A service limit or quota is exceeded.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "ListEnvironmentsRequest": {"type": "structure", "members": {"nextToken": {"shape": "PaginationToken", "documentation": "<p>A token generated by FinSpace that specifies where to continue pagination if a previous request was truncated. To get the next set of pages, pass in the <code>nextToken</code>nextToken value from the response object of the previous page call.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "ResultLimit", "documentation": "<p>The maximum number of results to return in this request.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListEnvironmentsResponse": {"type": "structure", "members": {"environments": {"shape": "EnvironmentList", "documentation": "<p>A list of all of your FinSpace environments.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>A token that you can use in a subsequent call to retrieve the next set of results.</p>"}}}, "ListKxChangesetsRequest": {"type": "structure", "required": ["environmentId", "databaseName"], "members": {"environmentId": {"shape": "EnvironmentId", "documentation": "<p>A unique identifier for the kdb environment.</p>", "location": "uri", "locationName": "environmentId"}, "databaseName": {"shape": "DatabaseName", "documentation": "<p>The name of the kdb database.</p>", "location": "uri", "locationName": "databaseName"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>A token that indicates where a results page should begin.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in this request.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListKxChangesetsResponse": {"type": "structure", "members": {"kxChangesets": {"shape": "KxChangesets", "documentation": "<p>A list of changesets for a database.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>A token that indicates where a results page should begin.</p>"}}}, "ListKxClusterNodesRequest": {"type": "structure", "required": ["clusterName", "environmentId"], "members": {"environmentId": {"shape": "KxEnvironmentId", "documentation": "<p>A unique identifier for the kdb environment.</p>", "location": "uri", "locationName": "environmentId"}, "clusterName": {"shape": "KxClusterName", "documentation": "<p>A unique name for the cluster.</p>", "location": "uri", "locationName": "clusterName"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>A token that indicates where a results page should begin.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "ResultLimit", "documentation": "<p>The maximum number of results to return in this request.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListKxClusterNodesResponse": {"type": "structure", "members": {"nodes": {"shape": "KxNodeSummaries", "documentation": "<p>A list of nodes associated with the cluster.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>A token that indicates where a results page should begin.</p>"}}}, "ListKxClustersRequest": {"type": "structure", "required": ["environmentId"], "members": {"environmentId": {"shape": "KxEnvironmentId", "documentation": "<p>A unique identifier for the kdb environment.</p>", "location": "uri", "locationName": "environmentId"}, "clusterType": {"shape": "KxClusterType", "documentation": "<p>Specifies the type of KDB database that is being created. The following types are available: </p> <ul> <li> <p>HDB – A Historical Database. The data is only accessible with read-only permissions from one of the FinSpace managed kdb databases mounted to the cluster.</p> </li> <li> <p>RDB – A Realtime Database. This type of database captures all the data from a ticker plant and stores it in memory until the end of day, after which it writes all of its data to a disk and reloads the HDB. This cluster type requires local storage for temporary storage of data during the savedown process. If you specify this field in your request, you must provide the <code>savedownStorageConfiguration</code> parameter.</p> </li> <li> <p>GATEWAY – A gateway cluster allows you to access data across processes in kdb systems. It allows you to create your own routing logic using the initialization scripts and custom code. This type of cluster does not require a writable local storage.</p> </li> <li> <p>GP – A general purpose cluster allows you to quickly iterate on code during development by granting greater access to system commands and enabling a fast reload of custom code. This cluster type can optionally mount databases including cache and savedown storage. For this cluster type, the node count is fixed at 1. It does not support autoscaling and supports only <code>SINGLE</code> AZ mode.</p> </li> <li> <p>Tickerplant – A tickerplant cluster allows you to subscribe to feed handlers based on IAM permissions. It can publish to RDBs, other Tickerplants, and real-time subscribers (RTS). Tickerplants can persist messages to log, which is readable by any RDB environment. It supports only single-node that is only one kdb process.</p> </li> </ul>", "location": "querystring", "locationName": "clusterType"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in this request.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>A token that indicates where a results page should begin.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListKxClustersResponse": {"type": "structure", "members": {"kxClusterSummaries": {"shape": "KxClusters", "documentation": "<p>Lists the cluster details.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>A token that indicates where a results page should begin.</p>"}}}, "ListKxDatabasesRequest": {"type": "structure", "required": ["environmentId"], "members": {"environmentId": {"shape": "EnvironmentId", "documentation": "<p>A unique identifier for the kdb environment.</p>", "location": "uri", "locationName": "environmentId"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>A token that indicates where a results page should begin.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in this request.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListKxDatabasesResponse": {"type": "structure", "members": {"kxDatabases": {"shape": "KxDatabases", "documentation": "<p>A list of databases in the kdb environment.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>A token that indicates where a results page should begin.</p>"}}}, "ListKxDataviewsRequest": {"type": "structure", "required": ["environmentId", "databaseName"], "members": {"environmentId": {"shape": "EnvironmentId", "documentation": "<p>A unique identifier for the kdb environment, for which you want to retrieve a list of dataviews.</p>", "location": "uri", "locationName": "environmentId"}, "databaseName": {"shape": "DatabaseName", "documentation": "<p> The name of the database where the dataviews were created.</p>", "location": "uri", "locationName": "databaseName"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p> A token that indicates where a results page should begin. </p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in this request.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListKxDataviewsResponse": {"type": "structure", "members": {"kxDataviews": {"shape": "KxDataviews", "documentation": "<p> The list of kdb dataviews that are currently active for the given database. </p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p> A token that indicates where a results page should begin. </p>"}}}, "ListKxEnvironmentsRequest": {"type": "structure", "members": {"nextToken": {"shape": "PaginationToken", "documentation": "<p>A token that indicates where a results page should begin.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "BoxedInteger", "documentation": "<p>The maximum number of results to return in this request.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListKxEnvironmentsResponse": {"type": "structure", "members": {"environments": {"shape": "KxEnvironmentList", "documentation": "<p>A list of environments in an account.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>A token that indicates where a results page should begin.</p>"}}}, "ListKxScalingGroupsRequest": {"type": "structure", "required": ["environmentId"], "members": {"environmentId": {"shape": "KxEnvironmentId", "documentation": "<p>A unique identifier for the kdb environment, for which you want to retrieve a list of scaling groups.</p>", "location": "uri", "locationName": "environmentId"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in this request.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p> A token that indicates where a results page should begin. </p>", "location": "querystring", "locationName": "nextToken"}}}, "ListKxScalingGroupsResponse": {"type": "structure", "members": {"scalingGroups": {"shape": "KxScalingGroupList", "documentation": "<p> A list of scaling groups available in a kdb environment.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p> A token that indicates where a results page should begin. </p>"}}}, "ListKxUsersRequest": {"type": "structure", "required": ["environmentId"], "members": {"environmentId": {"shape": "IdType", "documentation": "<p>A unique identifier for the kdb environment.</p>", "location": "uri", "locationName": "environmentId"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>A token that indicates where a results page should begin.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "ResultLimit", "documentation": "<p>The maximum number of results to return in this request.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListKxUsersResponse": {"type": "structure", "members": {"users": {"shape": "KxUserList", "documentation": "<p>A list of users in a kdb environment.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>A token that indicates where a results page should begin.</p>"}}}, "ListKxVolumesRequest": {"type": "structure", "required": ["environmentId"], "members": {"environmentId": {"shape": "KxEnvironmentId", "documentation": "<p>A unique identifier for the kdb environment, whose clusters can attach to the volume. </p>", "location": "uri", "locationName": "environmentId"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in this request.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>A token that indicates where a results page should begin.</p>", "location": "querystring", "locationName": "nextToken"}, "volumeType": {"shape": "KxVolumeType", "documentation": "<p> The type of file system volume. Currently, FinSpace only supports <code>NAS_1</code> volume type. </p>", "location": "querystring", "locationName": "volumeType"}}}, "ListKxVolumesResponse": {"type": "structure", "members": {"kxVolumeSummaries": {"shape": "KxVolumes", "documentation": "<p> A summary of volumes. </p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>A token that indicates where a results page should begin.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "FinSpaceTaggableArn", "documentation": "<p>The Amazon Resource Name of the resource.</p>", "location": "uri", "locationName": "resourceArn"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"tags": {"shape": "TagMap", "documentation": "<p>A list of all tags for a resource.</p>"}}}, "MaxResults": {"type": "integer", "max": 100, "min": 0}, "MemoryMib": {"type": "integer", "min": 6}, "NameString": {"type": "string", "max": 50, "min": 1, "pattern": "^[a-zA-Z0-9]{1,50}$"}, "NetworkACLConfiguration": {"type": "list", "member": {"shape": "NetworkACLEntry"}, "max": 100, "min": 1}, "NetworkACLEntry": {"type": "structure", "required": ["ruleNumber", "protocol", "ruleAction", "cidrBlock"], "members": {"ruleNumber": {"shape": "RuleNumber", "documentation": "<p> The rule number for the entry. For example <i>100</i>. All the network ACL entries are processed in ascending order by rule number. </p>"}, "protocol": {"shape": "Protocol", "documentation": "<p> The protocol number. A value of <i>-1</i> means all the protocols. </p>"}, "ruleAction": {"shape": "RuleAction", "documentation": "<p> Indicates whether to allow or deny the traffic that matches the rule. </p>"}, "portRange": {"shape": "PortRange", "documentation": "<p> The range of ports the rule applies to. </p>"}, "icmpTypeCode": {"shape": "IcmpTypeCode", "documentation": "<p> Defines the ICMP protocol that consists of the ICMP type and code. </p>"}, "cidrBlock": {"shape": "ValidCIDRBlock", "documentation": "<p> The IPv4 network range to allow or deny, in CIDR notation. For example, <code>**********/24</code>. We modify the specified CIDR block to its canonical form. For example, if you specify <code>***********/18</code>, we modify it to <code>**********/18</code>. </p>"}}, "documentation": "<p> The network access control list (ACL) is an optional layer of security for your VPC that acts as a firewall for controlling traffic in and out of one or more subnets. The entry is a set of numbered ingress and egress rules that determine whether a packet should be allowed in or out of a subnet associated with the ACL. We process the entries in the ACL according to the rule numbers, in ascending order. </p>"}, "NodeCount": {"type": "integer", "min": 1}, "NodeType": {"type": "string", "max": 32, "min": 1, "pattern": "^[a-zA-Z0-9._]+$"}, "PaginationToken": {"type": "string", "max": 1000, "min": 1, "pattern": ".*"}, "Port": {"type": "integer", "max": 65535, "min": 0}, "PortRange": {"type": "structure", "required": ["from", "to"], "members": {"from": {"shape": "Port", "documentation": "<p> The first port in the range. </p>"}, "to": {"shape": "Port", "documentation": "<p> The last port in the range. </p>"}}, "documentation": "<p> The range of ports the rule applies to. </p>"}, "Protocol": {"type": "string", "max": 5, "min": 1, "pattern": "^-1|[0-9]+$"}, "ReleaseLabel": {"type": "string", "max": 16, "min": 1, "pattern": "^[a-zA-Z0-9._-]+$"}, "ResourceAlreadyExistsException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}}, "documentation": "<p>The specified resource group already exists.</p>", "error": {"httpStatusCode": 409}, "exception": true}, "ResourceNotFoundException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}}, "documentation": "<p>One or more resources can't be found.</p>", "error": {"httpStatusCode": 404}, "exception": true}, "ResultLimit": {"type": "integer", "max": 100, "min": 0}, "RoleArn": {"type": "string", "max": 2048, "min": 20, "pattern": "^arn:aws[a-z\\-]*:iam::\\d{12}:role/?[a-zA-Z_0-9+=,.@\\-_/]+$"}, "RuleAction": {"type": "string", "enum": ["allow", "deny"]}, "RuleNumber": {"type": "integer", "max": 32766, "min": 1}, "S3Bucket": {"type": "string", "max": 255, "min": 3, "pattern": "^[a-z0-9][a-z0-9\\.\\-]*[a-z0-9]$"}, "S3Key": {"type": "string", "max": 1024, "min": 1, "pattern": "^[a-zA-Z0-9\\/\\!\\-_\\.\\*'\\(\\)]+$"}, "S3ObjectVersion": {"type": "string", "max": 1000, "min": 1}, "S3Path": {"type": "string", "max": 1093, "min": 9, "pattern": "^s3:\\/\\/[a-z0-9][a-z0-9-.]{1,61}[a-z0-9]\\/([^\\/]+\\/)*[^\\/]*$"}, "SamlMetadataDocument": {"type": "string", "max": 10000000, "min": 1000, "pattern": ".*"}, "SecurityGroupIdList": {"type": "list", "member": {"shape": "SecurityGroupIdString"}}, "SecurityGroupIdString": {"type": "string", "max": 1024, "min": 1, "pattern": "^sg-([a-z0-9]{8}$|[a-z0-9]{17}$)"}, "SegmentConfigurationDbPathList": {"type": "list", "member": {"shape": "DbPath"}, "max": 30, "min": 1}, "ServiceQuotaExceededException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}}, "documentation": "<p> You have exceeded your service quota. To perform the requested action, remove some of the relevant resources, or use Service Quotas to request a service quota increase.</p>", "error": {"httpStatusCode": 402}, "exception": true}, "SignedKxConnectionString": {"type": "string", "max": 2048, "min": 1, "pattern": "^(:|:tcps:\\/\\/)[a-zA-Z0-9-\\.\\_]+:\\d+:[a-zA-Z0-9-\\.\\_]+:\\S+$", "sensitive": true}, "SmsDomainUrl": {"type": "string", "max": 1000, "min": 1, "pattern": "^[a-zA-Z-0-9-:\\/.]*$"}, "SubnetIdList": {"type": "list", "member": {"shape": "SubnetIdString"}}, "SubnetIdString": {"type": "string", "max": 1024, "min": 1, "pattern": "^subnet-([a-z0-9]{8}$|[a-z0-9]{17}$)"}, "SuperuserParameters": {"type": "structure", "required": ["emailAddress", "firstName", "lastName"], "members": {"emailAddress": {"shape": "EmailId", "documentation": "<p>The email address of the superuser.</p>"}, "firstName": {"shape": "NameString", "documentation": "<p>The first name of the superuser.</p>"}, "lastName": {"shape": "NameString", "documentation": "<p>The last name of the superuser.</p>"}}, "documentation": "<p>Configuration information for the superuser.</p>"}, "TagKey": {"type": "string", "max": 128, "min": 1, "pattern": "^(?!aws:)[a-zA-Z+-=._:/]+$"}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 50, "min": 1}, "TagMap": {"type": "map", "key": {"shape": "TagKey"}, "value": {"shape": "TagValue"}, "max": 50, "min": 1}, "TagResourceRequest": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {"shape": "FinSpaceTaggableArn", "documentation": "<p>The Amazon Resource Name (ARN) for the resource.</p>", "location": "uri", "locationName": "resourceArn"}, "tags": {"shape": "TagMap", "documentation": "<p>One or more tags to be assigned to the resource.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 1, "pattern": "^[a-zA-Z0-9+-=._:@ ]+$"}, "ThrottlingException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}}, "documentation": "<p>The request was denied due to request throttling.</p>", "error": {"httpStatusCode": 429}, "exception": true}, "TickerplantLogConfiguration": {"type": "structure", "members": {"tickerplantLogVolumes": {"shape": "TickerplantLogVolumes", "documentation": "<p> The name of the volumes for tickerplant logs. </p>"}}, "documentation": "<p> A configuration to store the Tickerplant logs. It consists of a list of volumes that will be mounted to your cluster. For the cluster type <code>Tickerplant</code>, the location of the TP volume on the cluster will be available by using the global variable <code>.aws.tp_log_path</code>. </p>"}, "TickerplantLogVolumes": {"type": "list", "member": {"shape": "VolumeName"}}, "Timestamp": {"type": "timestamp"}, "TransitGatewayConfiguration": {"type": "structure", "required": ["transitGatewayID", "routableCIDRSpace"], "members": {"transitGatewayID": {"shape": "TransitGatewayID", "documentation": "<p>The identifier of the transit gateway created by the customer to connect outbound traffics from kdb network to your internal network.</p>"}, "routableCIDRSpace": {"shape": "ValidCIDRSpace", "documentation": "<p>The routing CIDR on behalf of kdb environment. It could be any \"/26 range in the 100.64.0.0 CIDR space. After providing, it will be added to the customer's transit gateway routing table so that the traffics could be routed to kdb network.</p>"}, "attachmentNetworkAclConfiguration": {"shape": "NetworkACLConfiguration", "documentation": "<p> The rules that define how you manage the outbound traffic from kdb network to your internal network. </p>"}}, "documentation": "<p>The structure of the transit gateway and network configuration that is used to connect the kdb environment to an internal network.</p>"}, "TransitGatewayID": {"type": "string", "max": 32, "min": 1}, "UntagResourceRequest": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {"shape": "FinSpaceTaggableArn", "documentation": "<p>A FinSpace resource from which you want to remove a tag or tags. The value for this parameter is an Amazon Resource Name (ARN).</p>", "location": "uri", "locationName": "resourceArn"}, "tagKeys": {"shape": "TagKeyList", "documentation": "<p>The tag keys (names) of one or more tags to be removed.</p>", "location": "querystring", "locationName": "tagKeys"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateEnvironmentRequest": {"type": "structure", "required": ["environmentId"], "members": {"environmentId": {"shape": "IdType", "documentation": "<p>The identifier of the FinSpace environment.</p>", "location": "uri", "locationName": "environmentId"}, "name": {"shape": "EnvironmentName", "documentation": "<p>The name of the environment.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description of the environment.</p>"}, "federationMode": {"shape": "FederationMode", "documentation": "<p>Authentication mode for the environment.</p> <ul> <li> <p> <code>FEDERATED</code> - Users access FinSpace through Single Sign On (SSO) via your Identity provider.</p> </li> <li> <p> <code>LOCAL</code> - Users access FinSpace via email and password managed within the FinSpace environment.</p> </li> </ul>"}, "federationParameters": {"shape": "FederationParameters"}}}, "UpdateEnvironmentResponse": {"type": "structure", "members": {"environment": {"shape": "Environment", "documentation": "<p>Returns the FinSpace environment object.</p>"}}}, "UpdateKxClusterCodeConfigurationRequest": {"type": "structure", "required": ["environmentId", "clusterName", "code"], "members": {"environmentId": {"shape": "KxEnvironmentId", "documentation": "<p> A unique identifier of the kdb environment. </p>", "location": "uri", "locationName": "environmentId"}, "clusterName": {"shape": "KxClusterName", "documentation": "<p>The name of the cluster.</p>", "location": "uri", "locationName": "clusterName"}, "clientToken": {"shape": "ClientTokenString", "documentation": "<p>A token that ensures idempotency. This token expires in 10 minutes.</p>", "idempotencyToken": true}, "code": {"shape": "CodeConfiguration"}, "initializationScript": {"shape": "InitializationScriptFilePath", "documentation": "<p>Specifies a Q program that will be run at launch of a cluster. It is a relative path within <i>.zip</i> file that contains the custom code, which will be loaded on the cluster. It must include the file name itself. For example, <code>somedir/init.q</code>.</p> <p>You cannot update this parameter for a <code>NO_RESTART</code> deployment.</p>"}, "commandLineArguments": {"shape": "KxCommandLineArguments", "documentation": "<p>Specifies the key-value pairs to make them available inside the cluster.</p> <p>You cannot update this parameter for a <code>NO_RESTART</code> deployment.</p>"}, "deploymentConfiguration": {"shape": "KxClusterCodeDeploymentConfiguration", "documentation": "<p> The configuration that allows you to choose how you want to update the code on a cluster. </p>"}}}, "UpdateKxClusterCodeConfigurationResponse": {"type": "structure", "members": {}}, "UpdateKxClusterDatabasesRequest": {"type": "structure", "required": ["environmentId", "clusterName", "databases"], "members": {"environmentId": {"shape": "KxEnvironmentId", "documentation": "<p>The unique identifier of a kdb environment.</p>", "location": "uri", "locationName": "environmentId"}, "clusterName": {"shape": "KxClusterName", "documentation": "<p>A unique name for the cluster that you want to modify.</p>", "location": "uri", "locationName": "clusterName"}, "clientToken": {"shape": "ClientTokenString", "documentation": "<p>A token that ensures idempotency. This token expires in 10 minutes.</p>", "idempotencyToken": true}, "databases": {"shape": "KxDatabaseConfigurations", "documentation": "<p> The structure of databases mounted on the cluster.</p>"}, "deploymentConfiguration": {"shape": "KxDeploymentConfiguration", "documentation": "<p> The configuration that allows you to choose how you want to update the databases on a cluster. </p>"}}}, "UpdateKxClusterDatabasesResponse": {"type": "structure", "members": {}}, "UpdateKxDatabaseRequest": {"type": "structure", "required": ["environmentId", "databaseName", "clientToken"], "members": {"environmentId": {"shape": "EnvironmentId", "documentation": "<p>A unique identifier for the kdb environment.</p>", "location": "uri", "locationName": "environmentId"}, "databaseName": {"shape": "DatabaseName", "documentation": "<p>The name of the kdb database.</p>", "location": "uri", "locationName": "databaseName"}, "description": {"shape": "Description", "documentation": "<p>A description of the database.</p>"}, "clientToken": {"shape": "ClientTokenString", "documentation": "<p>A token that ensures idempotency. This token expires in 10 minutes.</p>", "idempotencyToken": true}}}, "UpdateKxDatabaseResponse": {"type": "structure", "members": {"databaseName": {"shape": "DatabaseName", "documentation": "<p>The name of the kdb database.</p>"}, "environmentId": {"shape": "EnvironmentId", "documentation": "<p>A unique identifier for the kdb environment.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description of the database.</p>"}, "lastModifiedTimestamp": {"shape": "Timestamp", "documentation": "<p>The last time that the database was modified. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as *************.</p>"}}}, "UpdateKxDataviewRequest": {"type": "structure", "required": ["environmentId", "databaseName", "dataviewName", "clientToken"], "members": {"environmentId": {"shape": "EnvironmentId", "documentation": "<p>A unique identifier for the kdb environment, where you want to update the dataview.</p>", "location": "uri", "locationName": "environmentId"}, "databaseName": {"shape": "DatabaseName", "documentation": "<p> The name of the database.</p>", "location": "uri", "locationName": "databaseName"}, "dataviewName": {"shape": "KxDataviewName", "documentation": "<p>The name of the dataview that you want to update.</p>", "location": "uri", "locationName": "dataviewName"}, "description": {"shape": "Description", "documentation": "<p> The description for a dataview. </p>"}, "changesetId": {"shape": "ChangesetId", "documentation": "<p>A unique identifier for the changeset.</p>"}, "segmentConfigurations": {"shape": "KxDataviewSegmentConfigurationList", "documentation": "<p> The configuration that contains the database path of the data that you want to place on each selected volume. Each segment must have a unique database path for each volume. If you do not explicitly specify any database path for a volume, they are accessible from the cluster through the default S3/object store segment. </p>"}, "clientToken": {"shape": "ClientTokenString", "documentation": "<p>A token that ensures idempotency. This token expires in 10 minutes.</p>", "idempotencyToken": true}}}, "UpdateKxDataviewResponse": {"type": "structure", "members": {"environmentId": {"shape": "EnvironmentId", "documentation": "<p>A unique identifier for the kdb environment, where you want to update the dataview.</p>"}, "databaseName": {"shape": "DatabaseName", "documentation": "<p> The name of the database. </p>"}, "dataviewName": {"shape": "KxDataviewName", "documentation": "<p> The name of the database under which the dataview was created. </p>"}, "azMode": {"shape": "KxAzMode", "documentation": "<p>The number of availability zones you want to assign per volume. Currently, FinSpace only supports <code>SINGLE</code> for volumes. This places dataview in a single AZ.</p>"}, "availabilityZoneId": {"shape": "AvailabilityZoneId", "documentation": "<p> The identifier of the availability zones. </p>"}, "changesetId": {"shape": "ChangesetId", "documentation": "<p>A unique identifier for the changeset.</p>"}, "segmentConfigurations": {"shape": "KxDataviewSegmentConfigurationList", "documentation": "<p> The configuration that contains the database path of the data that you want to place on each selected volume. Each segment must have a unique database path for each volume. If you do not explicitly specify any database path for a volume, they are accessible from the cluster through the default S3/object store segment. </p>"}, "activeVersions": {"shape": "KxDataviewActiveVersionList", "documentation": "<p> The current active changeset versions of the database on the given dataview. </p>"}, "status": {"shape": "KxDataviewStatus", "documentation": "<p> The status of dataview creation.</p> <ul> <li> <p> <code>CREATING</code> – The dataview creation is in progress.</p> </li> <li> <p> <code>UPDATING</code> – The dataview is in the process of being updated.</p> </li> <li> <p> <code>ACTIVE</code> – The dataview is active.</p> </li> </ul>"}, "autoUpdate": {"shape": "booleanValue", "documentation": "<p>The option to specify whether you want to apply all the future additions and corrections automatically to the dataview when new changesets are ingested. The default value is false.</p>"}, "readWrite": {"shape": "booleanValue", "documentation": "<p>Returns True if the dataview is created as writeable and False otherwise. </p>"}, "description": {"shape": "Description", "documentation": "<p>A description of the dataview.</p>"}, "createdTimestamp": {"shape": "Timestamp", "documentation": "<p> The timestamp at which the dataview was created in FinSpace. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as *************. </p>"}, "lastModifiedTimestamp": {"shape": "Timestamp", "documentation": "<p> The last time that the dataview was updated in FinSpace. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as *************. </p>"}}}, "UpdateKxEnvironmentNetworkRequest": {"type": "structure", "required": ["environmentId"], "members": {"environmentId": {"shape": "IdType", "documentation": "<p>A unique identifier for the kdb environment.</p>", "location": "uri", "locationName": "environmentId"}, "transitGatewayConfiguration": {"shape": "TransitGatewayConfiguration", "documentation": "<p>Specifies the transit gateway and network configuration to connect the kdb environment to an internal network.</p>"}, "customDNSConfiguration": {"shape": "CustomDNSConfiguration", "documentation": "<p>A list of DNS server name and server IP. This is used to set up Route-53 outbound resolvers.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A token that ensures idempotency. This token expires in 10 minutes.</p>", "idempotencyToken": true}}}, "UpdateKxEnvironmentNetworkResponse": {"type": "structure", "members": {"name": {"shape": "KxEnvironmentName", "documentation": "<p>The name of the kdb environment.</p>"}, "environmentId": {"shape": "IdType", "documentation": "<p>A unique identifier for the kdb environment.</p>"}, "awsAccountId": {"shape": "IdType", "documentation": "<p>The unique identifier of the AWS account that is used to create the kdb environment.</p>"}, "status": {"shape": "EnvironmentStatus", "documentation": "<p>The status of the kdb environment.</p>"}, "tgwStatus": {"shape": "tgwStatus", "documentation": "<p>The status of the network configuration.</p>"}, "dnsStatus": {"shape": "dnsStatus", "documentation": "<p>The status of DNS configuration.</p>"}, "errorMessage": {"shape": "EnvironmentErrorMessage", "documentation": "<p>Specifies the error message that appears if a flow fails.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description of the environment.</p>"}, "environmentArn": {"shape": "EnvironmentArn", "documentation": "<p>The ARN identifier of the environment.</p>"}, "kmsKeyId": {"shape": "KmsKeyId", "documentation": "<p>The KMS key ID to encrypt your data in the FinSpace environment.</p>"}, "dedicatedServiceAccountId": {"shape": "IdType", "documentation": "<p>A unique identifier for the AWS environment infrastructure account.</p>"}, "transitGatewayConfiguration": {"shape": "TransitGatewayConfiguration"}, "customDNSConfiguration": {"shape": "CustomDNSConfiguration", "documentation": "<p>A list of DNS server name and server IP. This is used to set up Route-53 outbound resolvers.</p>"}, "creationTimestamp": {"shape": "Timestamp", "documentation": "<p>The timestamp at which the kdb environment was created in FinSpace. </p>"}, "updateTimestamp": {"shape": "Timestamp", "documentation": "<p>The timestamp at which the kdb environment was updated. </p>"}, "availabilityZoneIds": {"shape": "AvailabilityZoneIds", "documentation": "<p>The identifier of the availability zones where subnets for the environment are created.</p>"}}}, "UpdateKxEnvironmentRequest": {"type": "structure", "required": ["environmentId"], "members": {"environmentId": {"shape": "IdType", "documentation": "<p>A unique identifier for the kdb environment.</p>", "location": "uri", "locationName": "environmentId"}, "name": {"shape": "KxEnvironmentName", "documentation": "<p>The name of the kdb environment.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description of the kdb environment.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A token that ensures idempotency. This token expires in 10 minutes.</p>", "idempotencyToken": true}}}, "UpdateKxEnvironmentResponse": {"type": "structure", "members": {"name": {"shape": "KxEnvironmentName", "documentation": "<p>The name of the kdb environment.</p>"}, "environmentId": {"shape": "IdType", "documentation": "<p>A unique identifier for the kdb environment.</p>"}, "awsAccountId": {"shape": "IdType", "documentation": "<p>The unique identifier of the AWS account that is used to create the kdb environment.</p>"}, "status": {"shape": "EnvironmentStatus", "documentation": "<p>The status of the kdb environment.</p>"}, "tgwStatus": {"shape": "tgwStatus", "documentation": "<p>The status of the network configuration.</p>"}, "dnsStatus": {"shape": "dnsStatus", "documentation": "<p>The status of DNS configuration.</p>"}, "errorMessage": {"shape": "EnvironmentErrorMessage", "documentation": "<p>Specifies the error message that appears if a flow fails.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description of the environment.</p>"}, "environmentArn": {"shape": "EnvironmentArn", "documentation": "<p>The ARN identifier of the environment.</p>"}, "kmsKeyId": {"shape": "KmsKeyId", "documentation": "<p>The KMS key ID to encrypt your data in the FinSpace environment.</p>"}, "dedicatedServiceAccountId": {"shape": "IdType", "documentation": "<p>A unique identifier for the AWS environment infrastructure account.</p>"}, "transitGatewayConfiguration": {"shape": "TransitGatewayConfiguration"}, "customDNSConfiguration": {"shape": "CustomDNSConfiguration", "documentation": "<p>A list of DNS server name and server IP. This is used to set up Route-53 outbound resolvers.</p>"}, "creationTimestamp": {"shape": "Timestamp", "documentation": "<p>The timestamp at which the kdb environment was created in FinSpace. </p>"}, "updateTimestamp": {"shape": "Timestamp", "documentation": "<p>The timestamp at which the kdb environment was updated. </p>"}, "availabilityZoneIds": {"shape": "AvailabilityZoneIds", "documentation": "<p>The identifier of the availability zones where subnets for the environment are created.</p>"}}}, "UpdateKxUserRequest": {"type": "structure", "required": ["environmentId", "userName", "iamRole"], "members": {"environmentId": {"shape": "IdType", "documentation": "<p>A unique identifier for the kdb environment.</p>", "location": "uri", "locationName": "environmentId"}, "userName": {"shape": "KxUserNameString", "documentation": "<p>A unique identifier for the user.</p>", "location": "uri", "locationName": "userName"}, "iamRole": {"shape": "RoleArn", "documentation": "<p>The IAM role ARN that is associated with the user.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A token that ensures idempotency. This token expires in 10 minutes.</p>", "idempotencyToken": true}}}, "UpdateKxUserResponse": {"type": "structure", "members": {"userName": {"shape": "KxUserNameString", "documentation": "<p>A unique identifier for the user.</p>"}, "userArn": {"shape": "KxUserArn", "documentation": "<p> The Amazon Resource Name (ARN) that identifies the user. For more information about ARNs and how to use ARNs in policies, see <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/reference_identifiers.html\">IAM Identifiers</a> in the <i>IAM User Guide</i>. </p>"}, "environmentId": {"shape": "IdType", "documentation": "<p>A unique identifier for the kdb environment.</p>"}, "iamRole": {"shape": "RoleArn", "documentation": "<p>The IAM role ARN that is associated with the user.</p>"}}}, "UpdateKxVolumeRequest": {"type": "structure", "required": ["environmentId", "volumeName"], "members": {"environmentId": {"shape": "KxEnvironmentId", "documentation": "<p>A unique identifier for the kdb environment where you created the storage volume. </p>", "location": "uri", "locationName": "environmentId"}, "volumeName": {"shape": "KxVolumeName", "documentation": "<p> A unique identifier for the volume.</p>", "location": "uri", "locationName": "volumeName"}, "description": {"shape": "Description", "documentation": "<p> A description of the volume. </p>"}, "clientToken": {"shape": "ClientTokenString", "documentation": "<p>A token that ensures idempotency. This token expires in 10 minutes.</p>", "idempotencyToken": true}, "nas1Configuration": {"shape": "KxNAS1Configuration", "documentation": "<p> Specifies the configuration for the Network attached storage (NAS_1) file system volume.</p>"}}}, "UpdateKxVolumeResponse": {"type": "structure", "members": {"environmentId": {"shape": "KxEnvironmentId", "documentation": "<p>A unique identifier for the kdb environment where you want to update the volume. </p>"}, "volumeName": {"shape": "KxVolumeName", "documentation": "<p>A unique identifier for the volume that you want to update.</p>"}, "volumeType": {"shape": "KxVolumeType", "documentation": "<p> The type of file system volume. Currently, FinSpace only supports <code>NAS_1</code> volume type. </p>"}, "volumeArn": {"shape": "KxVolumeArn", "documentation": "<p> The ARN identifier of the volume. </p>"}, "nas1Configuration": {"shape": "KxNAS1Configuration", "documentation": "<p> Specifies the configuration for the Network attached storage (NAS_1) file system volume.</p>"}, "status": {"shape": "KxVolumeStatus", "documentation": "<p>The status of the volume.</p> <ul> <li> <p>CREATING – The volume creation is in progress.</p> </li> <li> <p>CREATE_FAILED – The volume creation has failed.</p> </li> <li> <p>ACTIVE – The volume is active.</p> </li> <li> <p>UPDATING – The volume is in the process of being updated.</p> </li> <li> <p>UPDATE_FAILED – The update action failed.</p> </li> <li> <p>UPDATED – The volume is successfully updated.</p> </li> <li> <p>DELETING – The volume is in the process of being deleted.</p> </li> <li> <p>DELETE_FAILED – The system failed to delete the volume.</p> </li> <li> <p>DELETED – The volume is successfully deleted.</p> </li> </ul>"}, "description": {"shape": "Description", "documentation": "<p> The description for the volume. </p>"}, "statusReason": {"shape": "KxVolumeStatusReason", "documentation": "<p>The error message when a failed state occurs. </p>"}, "createdTimestamp": {"shape": "Timestamp", "documentation": "<p> The timestamp at which the volume was created in FinSpace. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as *************.</p>"}, "azMode": {"shape": "KxAzMode", "documentation": "<p>The number of availability zones you want to assign per volume. Currently, FinSpace only supports <code>SINGLE</code> for volumes. This places dataview in a single AZ.</p>"}, "availabilityZoneIds": {"shape": "AvailabilityZoneIds", "documentation": "<p>The identifier of the availability zones.</p>"}, "lastModifiedTimestamp": {"shape": "Timestamp", "documentation": "<p>The last time that the volume was updated in FinSpace. The value is determined as epoch time in milliseconds. For example, the value for Monday, November 1, 2021 12:00:00 PM UTC is specified as *************.</p>"}, "attachedClusters": {"shape": "KxAttachedClusters", "documentation": "<p> Specifies the clusters that a volume is attached to. </p>"}}}, "ValidCIDRBlock": {"type": "string", "max": 18, "min": 1, "pattern": "^(?:\\d{1,3}\\.){3}\\d{1,3}(?:\\/(?:3[0-2]|[12]\\d|\\d))$"}, "ValidCIDRSpace": {"type": "string"}, "ValidHostname": {"type": "string", "max": 255, "min": 3, "pattern": "^([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9])(\\.([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9]))*$"}, "ValidIPAddress": {"type": "string", "pattern": "^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"}, "ValidationException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}}, "documentation": "<p>The input fails to satisfy the constraints specified by an AWS service.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "VersionId": {"type": "string", "max": 26, "min": 1}, "Volume": {"type": "structure", "members": {"volumeName": {"shape": "VolumeName", "documentation": "<p>A unique identifier for the volume.</p>"}, "volumeType": {"shape": "VolumeType", "documentation": "<p> The type of file system volume. Currently, FinSpace only supports <code>NAS_1</code> volume type. </p>"}}, "documentation": "<p> The structure that consists of name and type of volume.</p>"}, "VolumeName": {"type": "string", "max": 63, "min": 3, "pattern": "^[a-zA-Z0-9][a-zA-Z0-9-_]*[a-zA-Z0-9]$"}, "VolumeType": {"type": "string", "enum": ["NAS_1"]}, "Volumes": {"type": "list", "member": {"shape": "Volume"}, "max": 5, "min": 0}, "VpcConfiguration": {"type": "structure", "members": {"vpcId": {"shape": "VpcIdString", "documentation": "<p>The identifier of the VPC endpoint.</p>"}, "securityGroupIds": {"shape": "SecurityGroupIdList", "documentation": "<p>The unique identifier of the VPC security group applied to the VPC endpoint ENI for the cluster.</p>"}, "subnetIds": {"shape": "SubnetIdList", "documentation": "<p>The identifier of the subnet that the Privatelink VPC endpoint uses to connect to the cluster.</p>"}, "ipAddressType": {"shape": "IPAddressType", "documentation": "<p>The IP address type for cluster network configuration parameters. The following type is available:</p> <ul> <li> <p>IP_V4 – IP address version 4</p> </li> </ul>"}}, "documentation": "<p>Configuration details about the network where the Privatelink endpoint of the cluster resides.</p>"}, "VpcIdString": {"type": "string", "max": 1024, "min": 1, "pattern": "^vpc-([a-z0-9]{8}$|[a-z0-9]{17}$)"}, "arn": {"type": "string", "max": 2048, "min": 20, "pattern": "^arn:*:*:*:*:*"}, "booleanValue": {"type": "boolean"}, "dnsStatus": {"type": "string", "enum": ["NONE", "UPDATE_REQUESTED", "UPDATING", "FAILED_UPDATE", "SUCCESSFULLY_UPDATED"]}, "errorMessage": {"type": "string"}, "numBytes": {"type": "long"}, "numChangesets": {"type": "integer"}, "numFiles": {"type": "integer"}, "stringValueLength1to255": {"type": "string", "max": 255, "min": 1}, "tgwStatus": {"type": "string", "enum": ["NONE", "UPDATE_REQUESTED", "UPDATING", "FAILED_UPDATE", "SUCCESSFULLY_UPDATED"]}, "url": {"type": "string", "max": 1000, "min": 1, "pattern": "^https?://[-a-zA-Z0-9+&@#/%?=~_|!:,.;]*[-a-zA-Z0-9+&@#/%=~_|]"}, "urn": {"type": "string", "max": 255, "min": 1, "pattern": "^[A-Za-z0-9._\\-:\\/#\\+]+$"}}, "documentation": "<p>The FinSpace management service provides the APIs for managing FinSpace environments.</p>"}