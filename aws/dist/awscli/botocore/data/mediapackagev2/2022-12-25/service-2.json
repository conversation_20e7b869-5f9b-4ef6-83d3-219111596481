{"version": "2.0", "metadata": {"apiVersion": "2022-12-25", "auth": ["aws.auth#sigv4"], "endpointPrefix": "mediapackagev2", "protocol": "rest-json", "protocols": ["rest-json"], "serviceAbbreviation": "mediapackagev2", "serviceFullName": "AWS Elemental MediaPackage v2", "serviceId": "MediaPackageV2", "signatureVersion": "v4", "signingName": "mediapackagev2", "uid": "mediapackagev2-2022-12-25"}, "operations": {"CancelHarvestJob": {"name": "CancelHarvestJob", "http": {"method": "PUT", "requestUri": "/channelGroup/{ChannelGroupName}/channel/{ChannelName}/originEndpoint/{OriginEndpointName}/harvestJob/{HarvestJobName}", "responseCode": 200}, "input": {"shape": "CancelHarvestJobRequest"}, "output": {"shape": "CancelHarvestJobResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Cancels an in-progress harvest job.</p>", "idempotent": true}, "CreateChannel": {"name": "CreateChannel", "http": {"method": "POST", "requestUri": "/channelGroup/{ChannelGroupName}/channel", "responseCode": 200}, "input": {"shape": "CreateChannelRequest"}, "output": {"shape": "CreateChannelResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Create a channel to start receiving content streams. The channel represents the input to MediaPackage for incoming live content from an encoder such as AWS Elemental MediaLive. The channel receives content, and after packaging it, outputs it through an origin endpoint to downstream devices (such as video players or CDNs) that request the content. You can create only one channel with each request. We recommend that you spread out channels between channel groups, such as putting redundant channels in the same AWS Region in different channel groups.</p>", "idempotent": true}, "CreateChannelGroup": {"name": "CreateChannelGroup", "http": {"method": "POST", "requestUri": "/channelGroup", "responseCode": 200}, "input": {"shape": "CreateChannelGroupRequest"}, "output": {"shape": "CreateChannelGroupResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Create a channel group to group your channels and origin endpoints. A channel group is the top-level resource that consists of channels and origin endpoints that are associated with it and that provides predictable URLs for stream delivery. All channels and origin endpoints within the channel group are guaranteed to share the DNS. You can create only one channel group with each request. </p>", "idempotent": true}, "CreateHarvestJob": {"name": "CreateHarvestJob", "http": {"method": "POST", "requestUri": "/channelGroup/{ChannelGroupName}/channel/{ChannelName}/originEndpoint/{OriginEndpointName}/harvestJob", "responseCode": 200}, "input": {"shape": "CreateHarvestJobRequest"}, "output": {"shape": "CreateHarvestJobResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates a new harvest job to export content from a MediaPackage v2 channel to an S3 bucket.</p>", "idempotent": true}, "CreateOriginEndpoint": {"name": "CreateOriginEndpoint", "http": {"method": "POST", "requestUri": "/channelGroup/{ChannelGroupName}/channel/{ChannelName}/originEndpoint", "responseCode": 200}, "input": {"shape": "CreateOriginEndpointRequest"}, "output": {"shape": "CreateOriginEndpointResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>The endpoint is attached to a channel, and represents the output of the live content. You can associate multiple endpoints to a single channel. Each endpoint gives players and downstream CDNs (such as Amazon CloudFront) access to the content for playback. Content can't be served from a channel until it has an endpoint. You can create only one endpoint with each request. </p>", "idempotent": true}, "DeleteChannel": {"name": "DeleteChannel", "http": {"method": "DELETE", "requestUri": "/channelGroup/{ChannelGroupName}/channel/{ChannelName}/", "responseCode": 200}, "input": {"shape": "DeleteChannelRequest"}, "output": {"shape": "DeleteChannelResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Delete a channel to stop AWS Elemental MediaPackage from receiving further content. You must delete the channel's origin endpoints before you can delete the channel.</p>", "idempotent": true}, "DeleteChannelGroup": {"name": "DeleteChannelGroup", "http": {"method": "DELETE", "requestUri": "/channelGroup/{ChannelGroupName}", "responseCode": 200}, "input": {"shape": "DeleteChannelGroupRequest"}, "output": {"shape": "DeleteChannelGroupResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Delete a channel group. You must delete the channel group's channels and origin endpoints before you can delete the channel group. If you delete a channel group, you'll lose access to the egress domain and will have to create a new channel group to replace it.</p>", "idempotent": true}, "DeleteChannelPolicy": {"name": "DeleteChannelPolicy", "http": {"method": "DELETE", "requestUri": "/channelGroup/{ChannelGroupName}/channel/{ChannelName}/policy", "responseCode": 200}, "input": {"shape": "DeleteChannelPolicyRequest"}, "output": {"shape": "DeleteChannelPolicyResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Delete a channel policy.</p>", "idempotent": true}, "DeleteOriginEndpoint": {"name": "DeleteOriginEndpoint", "http": {"method": "DELETE", "requestUri": "/channelGroup/{ChannelGroupName}/channel/{ChannelName}/originEndpoint/{OriginEndpointName}", "responseCode": 200}, "input": {"shape": "DeleteOriginEndpointRequest"}, "output": {"shape": "DeleteOriginEndpointResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Origin endpoints can serve content until they're deleted. Delete the endpoint if it should no longer respond to playback requests. You must delete all endpoints from a channel before you can delete the channel.</p>", "idempotent": true}, "DeleteOriginEndpointPolicy": {"name": "DeleteOriginEndpointPolicy", "http": {"method": "DELETE", "requestUri": "/channelGroup/{ChannelGroupName}/channel/{ChannelName}/originEndpoint/{OriginEndpointName}/policy", "responseCode": 200}, "input": {"shape": "DeleteOriginEndpointPolicyRequest"}, "output": {"shape": "DeleteOriginEndpointPolicyResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Delete an origin endpoint policy.</p>", "idempotent": true}, "GetChannel": {"name": "GetChannel", "http": {"method": "GET", "requestUri": "/channelGroup/{ChannelGroupName}/channel/{ChannelName}/", "responseCode": 200}, "input": {"shape": "GetChannelRequest"}, "output": {"shape": "GetChannelResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves the specified channel that's configured in AWS Elemental MediaPackage, including the origin endpoints that are associated with it.</p>"}, "GetChannelGroup": {"name": "GetChannelGroup", "http": {"method": "GET", "requestUri": "/channelGroup/{ChannelGroupName}", "responseCode": 200}, "input": {"shape": "GetChannelGroupRequest"}, "output": {"shape": "GetChannelGroupResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves the specified channel group that's configured in AWS Elemental MediaPackage, including the channels and origin endpoints that are associated with it.</p>"}, "GetChannelPolicy": {"name": "GetChannelPolicy", "http": {"method": "GET", "requestUri": "/channelGroup/{ChannelGroupName}/channel/{ChannelName}/policy", "responseCode": 200}, "input": {"shape": "GetChannelPolicyRequest"}, "output": {"shape": "GetChannelPolicyResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves the specified channel policy that's configured in AWS Elemental MediaPackage. With policies, you can specify who has access to AWS resources and what actions they can perform on those resources.</p>"}, "GetHarvestJob": {"name": "GetHarvestJob", "http": {"method": "GET", "requestUri": "/channelGroup/{ChannelGroupName}/channel/{ChannelName}/originEndpoint/{OriginEndpointName}/harvestJob/{HarvestJobName}", "responseCode": 200}, "input": {"shape": "GetHarvestJobRequest"}, "output": {"shape": "GetHarvestJobResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves the details of a specific harvest job.</p>"}, "GetOriginEndpoint": {"name": "GetOriginEndpoint", "http": {"method": "GET", "requestUri": "/channelGroup/{ChannelGroupName}/channel/{ChannelName}/originEndpoint/{OriginEndpointName}", "responseCode": 200}, "input": {"shape": "GetOriginEndpointRequest"}, "output": {"shape": "GetOriginEndpointResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves the specified origin endpoint that's configured in AWS Elemental MediaPackage to obtain its playback URL and to view the packaging settings that it's currently using.</p>"}, "GetOriginEndpointPolicy": {"name": "GetOriginEndpointPolicy", "http": {"method": "GET", "requestUri": "/channelGroup/{ChannelGroupName}/channel/{ChannelName}/originEndpoint/{OriginEndpointName}/policy", "responseCode": 200}, "input": {"shape": "GetOriginEndpointPolicyRequest"}, "output": {"shape": "GetOriginEndpointPolicyResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves the specified origin endpoint policy that's configured in AWS Elemental MediaPackage.</p>"}, "ListChannelGroups": {"name": "ListChannelGroups", "http": {"method": "GET", "requestUri": "/channelGroup", "responseCode": 200}, "input": {"shape": "ListChannelGroupsRequest"}, "output": {"shape": "ListChannelGroupsResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Retrieves all channel groups that are configured in Elemental MediaPackage.</p>"}, "ListChannels": {"name": "ListChannels", "http": {"method": "GET", "requestUri": "/channelGroup/{ChannelGroupName}/channel", "responseCode": 200}, "input": {"shape": "ListChannelsRequest"}, "output": {"shape": "ListChannelsResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves all channels in a specific channel group that are configured in AWS Elemental MediaPackage, including the origin endpoints that are associated with it.</p>"}, "ListHarvestJobs": {"name": "ListHarvestJobs", "http": {"method": "GET", "requestUri": "/channelGroup/{ChannelGroupName}/harvestJob", "responseCode": 200}, "input": {"shape": "ListHarvestJobsRequest"}, "output": {"shape": "ListHarvestJobsResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves a list of harvest jobs that match the specified criteria.</p>"}, "ListOriginEndpoints": {"name": "ListOriginEndpoints", "http": {"method": "GET", "requestUri": "/channelGroup/{ChannelGroupName}/channel/{ChannelName}/originEndpoint", "responseCode": 200}, "input": {"shape": "ListOriginEndpointsRequest"}, "output": {"shape": "ListOriginEndpointsResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves all origin endpoints in a specific channel that are configured in AWS Elemental MediaPackage.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags/{ResourceArn}", "responseCode": 200}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "ValidationException"}], "documentation": "<p>Lists the tags assigned to a resource.</p>"}, "PutChannelPolicy": {"name": "PutChannelPolicy", "http": {"method": "PUT", "requestUri": "/channelGroup/{ChannelGroupName}/channel/{ChannelName}/policy", "responseCode": 200}, "input": {"shape": "PutChannelPolicyRequest"}, "output": {"shape": "PutChannelPolicyResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Attaches an IAM policy to the specified channel. With policies, you can specify who has access to AWS resources and what actions they can perform on those resources. You can attach only one policy with each request.</p>", "idempotent": true}, "PutOriginEndpointPolicy": {"name": "PutOriginEndpointPolicy", "http": {"method": "POST", "requestUri": "/channelGroup/{ChannelGroupName}/channel/{ChannelName}/originEndpoint/{OriginEndpointName}/policy", "responseCode": 200}, "input": {"shape": "PutOriginEndpointPolicyRequest"}, "output": {"shape": "PutOriginEndpointPolicyResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Attaches an IAM policy to the specified origin endpoint. You can attach only one policy with each request.</p>", "idempotent": true}, "ResetChannelState": {"name": "ResetChannelState", "http": {"method": "POST", "requestUri": "/channelGroup/{ChannelGroupName}/channel/{ChannelName}/reset", "responseCode": 200}, "input": {"shape": "ResetChannelStateRequest"}, "output": {"shape": "ResetChannelStateResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Resetting the channel can help to clear errors from misconfigurations in the encoder. A reset refreshes the ingest stream and removes previous content. </p> <p> Be sure to stop the encoder before you reset the channel, and wait at least 30 seconds before you restart the encoder. </p>", "idempotent": true}, "ResetOriginEndpointState": {"name": "ResetOriginEndpointState", "http": {"method": "POST", "requestUri": "/channelGroup/{ChannelGroupName}/channel/{ChannelName}/originEndpoint/{OriginEndpointName}/reset", "responseCode": 200}, "input": {"shape": "ResetOriginEndpointStateRequest"}, "output": {"shape": "ResetOriginEndpointStateResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Resetting the origin endpoint can help to resolve unexpected behavior and other content packaging issues. It also helps to preserve special events when you don't want the previous content to be available for viewing. A reset clears out all previous content from the origin endpoint.</p> <p>MediaPackage might return old content from this endpoint in the first 30 seconds after the endpoint reset. For best results, when possible, wait 30 seconds from endpoint reset to send playback requests to this endpoint. </p>", "idempotent": true}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags/{ResourceArn}", "responseCode": 204}, "input": {"shape": "TagResourceRequest"}, "errors": [{"shape": "ValidationException"}], "documentation": "<p>Assigns one of more tags (key-value pairs) to the specified MediaPackage resource.</p> <p>Tags can help you organize and categorize your resources. You can also use them to scope user permissions, by granting a user permission to access or change only resources with certain tag values. You can use the TagResource operation with a resource that already has tags. If you specify a new tag key for the resource, this tag is appended to the list of tags associated with the resource. If you specify a tag key that is already associated with the resource, the new tag value that you specify replaces the previous value for that tag.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags/{ResourceArn}", "responseCode": 204}, "input": {"shape": "UntagResourceRequest"}, "errors": [{"shape": "ValidationException"}], "documentation": "<p>Removes one or more tags from the specified resource.</p>", "idempotent": true}, "UpdateChannel": {"name": "UpdateChannel", "http": {"method": "PUT", "requestUri": "/channelGroup/{ChannelGroupName}/channel/{ChannelName}/", "responseCode": 200}, "input": {"shape": "UpdateChannelRequest"}, "output": {"shape": "UpdateChannelResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Update the specified channel. You can edit if MediaPackage sends ingest or egress access logs to the CloudWatch log group, if content will be encrypted, the description on a channel, and your channel's policy settings. You can't edit the name of the channel or CloudFront distribution details.</p> <p>Any edits you make that impact the video output may not be reflected for a few minutes.</p>", "idempotent": true}, "UpdateChannelGroup": {"name": "UpdateChannelGroup", "http": {"method": "PUT", "requestUri": "/channelGroup/{ChannelGroupName}", "responseCode": 200}, "input": {"shape": "UpdateChannelGroupRequest"}, "output": {"shape": "UpdateChannelGroupResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Update the specified channel group. You can edit the description on a channel group for easier identification later from the AWS Elemental MediaPackage console. You can't edit the name of the channel group.</p> <p>Any edits you make that impact the video output may not be reflected for a few minutes.</p>", "idempotent": true}, "UpdateOriginEndpoint": {"name": "UpdateOriginEndpoint", "http": {"method": "PUT", "requestUri": "/channelGroup/{ChannelGroupName}/channel/{ChannelName}/originEndpoint/{OriginEndpointName}", "responseCode": 200}, "input": {"shape": "UpdateOriginEndpointRequest"}, "output": {"shape": "UpdateOriginEndpointResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Update the specified origin endpoint. Edit the packaging preferences on an endpoint to optimize the viewing experience. You can't edit the name of the endpoint.</p> <p>Any edits you make that impact the video output may not be reflected for a few minutes.</p>", "idempotent": true}}, "shapes": {"AccessDeniedException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>You don't have permissions to perform the requested operation. The user or role that is making the request must have at least one IAM permissions policy attached that grants the required permissions. For more information, see Access Management in the IAM User Guide.</p>", "error": {"httpStatusCode": 403, "senderFault": true}, "exception": true}, "AdMarkerDash": {"type": "string", "enum": ["BINARY", "XML"]}, "AdMarkerHls": {"type": "string", "enum": ["DATERANGE"]}, "Boolean": {"type": "boolean", "box": true}, "CancelHarvestJobRequest": {"type": "structure", "required": ["ChannelGroupName", "ChannelName", "OriginEndpointName", "HarvestJobName"], "members": {"ChannelGroupName": {"shape": "ResourceName", "documentation": "<p>The name of the channel group containing the channel from which the harvest job is running.</p>", "location": "uri", "locationName": "ChannelGroupName"}, "ChannelName": {"shape": "ResourceName", "documentation": "<p>The name of the channel from which the harvest job is running.</p>", "location": "uri", "locationName": "ChannelName"}, "OriginEndpointName": {"shape": "ResourceName", "documentation": "<p>The name of the origin endpoint that the harvest job is harvesting from. This cannot be changed after the harvest job is submitted.</p>", "location": "uri", "locationName": "OriginEndpointName"}, "HarvestJobName": {"shape": "ResourceName", "documentation": "<p>The name of the harvest job to cancel. This name must be unique within the channel and cannot be changed after the harvest job is submitted.</p>", "location": "uri", "locationName": "HarvestJobName"}, "ETag": {"shape": "EntityTag", "documentation": "<p>The current Entity Tag (ETag) associated with the harvest job. Used for concurrency control.</p>", "location": "header", "locationName": "x-amzn-update-if-match"}}}, "CancelHarvestJobResponse": {"type": "structure", "members": {}}, "ChannelGroupListConfiguration": {"type": "structure", "required": ["ChannelGroupName", "<PERSON><PERSON>", "CreatedAt", "ModifiedAt"], "members": {"ChannelGroupName": {"shape": "String", "documentation": "<p>The name that describes the channel group. The name is the primary identifier for the channel group, and must be unique for your account in the AWS Region.</p>"}, "Arn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) associated with the resource.</p>"}, "CreatedAt": {"shape": "Timestamp", "documentation": "<p>The date and time the channel group was created.</p>"}, "ModifiedAt": {"shape": "Timestamp", "documentation": "<p>The date and time the channel group was modified.</p>"}, "Description": {"shape": "ResourceDescription", "documentation": "<p>Any descriptive information that you want to add to the channel group for future identification purposes.</p>"}}, "documentation": "<p>The configuration of the channel group.</p>"}, "ChannelGroupsList": {"type": "list", "member": {"shape": "ChannelGroupListConfiguration"}}, "ChannelList": {"type": "list", "member": {"shape": "ChannelListConfiguration"}}, "ChannelListConfiguration": {"type": "structure", "required": ["<PERSON><PERSON>", "ChannelName", "ChannelGroupName", "CreatedAt", "ModifiedAt"], "members": {"Arn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) associated with the resource.</p>"}, "ChannelName": {"shape": "String", "documentation": "<p>The name that describes the channel. The name is the primary identifier for the channel, and must be unique for your account in the AWS Region and channel group. </p>"}, "ChannelGroupName": {"shape": "String", "documentation": "<p>The name that describes the channel group. The name is the primary identifier for the channel group, and must be unique for your account in the AWS Region.</p>"}, "CreatedAt": {"shape": "Timestamp", "documentation": "<p>The date and time the channel was created.</p>"}, "ModifiedAt": {"shape": "Timestamp", "documentation": "<p>The date and time the channel was modified.</p>"}, "Description": {"shape": "ResourceDescription", "documentation": "<p>Any descriptive information that you want to add to the channel for future identification purposes.</p>"}, "InputType": {"shape": "InputType", "documentation": "<p>The input type will be an immutable field which will be used to define whether the channel will allow CMAF ingest or HLS ingest. If unprovided, it will default to HLS to preserve current behavior.</p> <p>The allowed values are:</p> <ul> <li> <p> <code>HLS</code> - The HLS streaming specification (which defines M3U8 manifests and TS segments).</p> </li> <li> <p> <code>CMAF</code> - The DASH-IF CMAF Ingest specification (which defines CMAF segments with optional DASH manifests).</p> </li> </ul>"}}, "documentation": "<p>The configuration of the channel.</p>"}, "CmafEncryptionMethod": {"type": "string", "enum": ["CENC", "CBCS"]}, "ConflictException": {"type": "structure", "members": {"Message": {"shape": "String"}, "ConflictExceptionType": {"shape": "ConflictExceptionType", "documentation": "<p>The type of ConflictException.</p>"}}, "documentation": "<p>Updating or deleting this resource can cause an inconsistent state.</p>", "error": {"httpStatusCode": 409, "senderFault": true}, "exception": true}, "ConflictExceptionType": {"type": "string", "enum": ["RESOURCE_IN_USE", "RESOURCE_ALREADY_EXISTS", "IDEMPOTENT_PARAMETER_MISMATCH", "CONFLICTING_OPERATION"]}, "ContainerType": {"type": "string", "enum": ["TS", "CMAF", "ISM"]}, "CreateChannelGroupRequest": {"type": "structure", "required": ["ChannelGroupName"], "members": {"ChannelGroupName": {"shape": "ResourceName", "documentation": "<p>The name that describes the channel group. The name is the primary identifier for the channel group, and must be unique for your account in the AWS Region. You can't use spaces in the name. You can't change the name after you create the channel group.</p>"}, "ClientToken": {"shape": "IdempotencyToken", "documentation": "<p>A unique, case-sensitive token that you provide to ensure the idempotency of the request.</p>", "idempotencyToken": true, "location": "header", "locationName": "x-amzn-client-token"}, "Description": {"shape": "ResourceDescription", "documentation": "<p>Enter any descriptive text that helps you to identify the channel group.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>A comma-separated list of tag key:value pairs that you define. For example:</p> <p> <code>\"Key1\": \"Value1\",</code> </p> <p> <code>\"Key2\": \"Value2\"</code> </p>", "locationName": "tags"}}}, "CreateChannelGroupResponse": {"type": "structure", "required": ["ChannelGroupName", "<PERSON><PERSON>", "EgressDomain", "CreatedAt", "ModifiedAt"], "members": {"ChannelGroupName": {"shape": "String", "documentation": "<p>The name that describes the channel group. The name is the primary identifier for the channel group, and must be unique for your account in the AWS Region.</p>"}, "Arn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) associated with the resource.</p>"}, "EgressDomain": {"shape": "String", "documentation": "<p>The output domain where the source stream should be sent. Integrate the egress domain with a downstream CDN (such as Amazon CloudFront) or playback device.</p>"}, "CreatedAt": {"shape": "Timestamp", "documentation": "<p>The date and time the channel group was created.</p>"}, "ModifiedAt": {"shape": "Timestamp", "documentation": "<p>The date and time the channel group was modified.</p>"}, "ETag": {"shape": "EntityTag", "documentation": "<p>The current Entity Tag (ETag) associated with this resource. The entity tag can be used to safely make concurrent updates to the resource.</p>"}, "Description": {"shape": "ResourceDescription", "documentation": "<p>The description for your channel group.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>The comma-separated list of tag key:value pairs assigned to the channel group.</p>"}}}, "CreateChannelRequest": {"type": "structure", "required": ["ChannelGroupName", "ChannelName"], "members": {"ChannelGroupName": {"shape": "ResourceName", "documentation": "<p>The name that describes the channel group. The name is the primary identifier for the channel group, and must be unique for your account in the AWS Region.</p>", "location": "uri", "locationName": "ChannelGroupName"}, "ChannelName": {"shape": "ResourceName", "documentation": "<p>The name that describes the channel. The name is the primary identifier for the channel, and must be unique for your account in the AWS Region and channel group. You can't change the name after you create the channel.</p>"}, "ClientToken": {"shape": "IdempotencyToken", "documentation": "<p>A unique, case-sensitive token that you provide to ensure the idempotency of the request.</p>", "idempotencyToken": true, "location": "header", "locationName": "x-amzn-client-token"}, "InputType": {"shape": "InputType", "documentation": "<p>The input type will be an immutable field which will be used to define whether the channel will allow CMAF ingest or HLS ingest. If unprovided, it will default to HLS to preserve current behavior.</p> <p>The allowed values are:</p> <ul> <li> <p> <code>HLS</code> - The HLS streaming specification (which defines M3U8 manifests and TS segments).</p> </li> <li> <p> <code>CMAF</code> - The DASH-IF CMAF Ingest specification (which defines CMAF segments with optional DASH manifests).</p> </li> </ul>"}, "Description": {"shape": "ResourceDescription", "documentation": "<p>Enter any descriptive text that helps you to identify the channel.</p>"}, "InputSwitchConfiguration": {"shape": "InputSwitchConfiguration", "documentation": "<p>The configuration for input switching based on the media quality confidence score (MQCS) as provided from AWS Elemental MediaLive. This setting is valid only when <code>InputType</code> is <code>CMAF</code>.</p>"}, "OutputHeaderConfiguration": {"shape": "OutputHeaderConfiguration", "documentation": "<p>The settings for what common media server data (CMSD) headers AWS Elemental MediaPackage includes in responses to the CDN. This setting is valid only when <code>InputType</code> is <code>CMAF</code>.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>A comma-separated list of tag key:value pairs that you define. For example:</p> <p> <code>\"Key1\": \"Value1\",</code> </p> <p> <code>\"Key2\": \"Value2\"</code> </p>", "locationName": "tags"}}}, "CreateChannelResponse": {"type": "structure", "required": ["<PERSON><PERSON>", "ChannelName", "ChannelGroupName", "CreatedAt", "ModifiedAt"], "members": {"Arn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) associated with the resource.</p>"}, "ChannelName": {"shape": "String", "documentation": "<p>The name that describes the channel. The name is the primary identifier for the channel, and must be unique for your account in the AWS Region and channel group.</p>"}, "ChannelGroupName": {"shape": "String", "documentation": "<p>The name that describes the channel group. The name is the primary identifier for the channel group, and must be unique for your account in the AWS Region.</p>"}, "CreatedAt": {"shape": "Timestamp", "documentation": "<p>The date and time the channel was created.</p>"}, "ModifiedAt": {"shape": "Timestamp", "documentation": "<p>The date and time the channel was modified.</p>"}, "Description": {"shape": "ResourceDescription", "documentation": "<p>The description for your channel.</p>"}, "IngestEndpoints": {"shape": "IngestEndpointList"}, "InputType": {"shape": "InputType", "documentation": "<p>The input type will be an immutable field which will be used to define whether the channel will allow CMAF ingest or HLS ingest. If unprovided, it will default to HLS to preserve current behavior.</p> <p>The allowed values are:</p> <ul> <li> <p> <code>HLS</code> - The HLS streaming specification (which defines M3U8 manifests and TS segments).</p> </li> <li> <p> <code>CMAF</code> - The DASH-IF CMAF Ingest specification (which defines CMAF segments with optional DASH manifests).</p> </li> </ul>"}, "ETag": {"shape": "EntityTag", "documentation": "<p>The current Entity Tag (ETag) associated with this resource. The entity tag can be used to safely make concurrent updates to the resource.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>The comma-separated list of tag key:value pairs assigned to the channel.</p>"}, "InputSwitchConfiguration": {"shape": "InputSwitchConfiguration", "documentation": "<p>The configuration for input switching based on the media quality confidence score (MQCS) as provided from AWS Elemental MediaLive. This setting is valid only when <code>InputType</code> is <code>CMAF</code>.</p>"}, "OutputHeaderConfiguration": {"shape": "OutputHeaderConfiguration", "documentation": "<p>The settings for what common media server data (CMSD) headers AWS Elemental MediaPackage includes in responses to the CDN. This setting is valid only when <code>InputType</code> is <code>CMAF</code>.</p>"}}}, "CreateDashManifestConfiguration": {"type": "structure", "required": ["ManifestName"], "members": {"ManifestName": {"shape": "ManifestName", "documentation": "<p>A short string that's appended to the endpoint URL. The child manifest name creates a unique path to this endpoint.</p>"}, "ManifestWindowSeconds": {"shape": "CreateDashManifestConfigurationManifestWindowSecondsInteger", "documentation": "<p>The total duration (in seconds) of the manifest's content.</p>"}, "FilterConfiguration": {"shape": "FilterConfiguration"}, "MinUpdatePeriodSeconds": {"shape": "CreateDashManifestConfigurationMinUpdatePeriodSecondsInteger", "documentation": "<p>Minimum amount of time (in seconds) that the player should wait before requesting updates to the manifest.</p>"}, "MinBufferTimeSeconds": {"shape": "CreateDashManifestConfigurationMinBufferTimeSecondsInteger", "documentation": "<p>Minimum amount of content (in seconds) that a player must keep available in the buffer.</p>"}, "SuggestedPresentationDelaySeconds": {"shape": "CreateDashManifestConfigurationSuggestedPresentationDelaySecondsInteger", "documentation": "<p>The amount of time (in seconds) that the player should be from the end of the manifest.</p>"}, "SegmentTemplateFormat": {"shape": "DashSegmentTemplateFormat", "documentation": "<p>Determines the type of variable used in the <code>media</code> URL of the <code>SegmentTemplate</code> tag in the manifest. Also specifies if segment timeline information is included in <code>SegmentTimeline</code> or <code>SegmentTemplate</code>.</p> <p>Value description:</p> <ul> <li> <p> <code>NUMBER_WITH_TIMELINE</code> - The <code>$Number$</code> variable is used in the <code>media</code> URL. The value of this variable is the sequential number of the segment. A full <code>SegmentTimeline</code> object is presented in each <code>SegmentTemplate</code>.</p> </li> </ul>"}, "PeriodTriggers": {"shape": "DashPeriodTriggers", "documentation": "<p>A list of triggers that controls when AWS Elemental MediaPackage separates the MPEG-DASH manifest into multiple periods. Type <code>ADS</code> to indicate that AWS Elemental MediaPackage must create periods in the output manifest that correspond to SCTE-35 ad markers in the input source. Leave this value empty to indicate that the manifest is contained all in one period. For more information about periods in the DASH manifest, see <a href=\"https://docs.aws.amazon.com/mediapackage/latest/userguide/multi-period.html\">Multi-period DASH in AWS Elemental MediaPackage</a>.</p>"}, "ScteDash": {"shape": "ScteDash", "documentation": "<p>The SCTE configuration.</p>"}, "DrmSignaling": {"shape": "DashDrmSignaling", "documentation": "<p>Determines how the DASH manifest signals the DRM content.</p>"}, "UtcTiming": {"shape": "DashUtcTiming", "documentation": "<p>Determines the type of UTC timing included in the DASH Media Presentation Description (MPD).</p>"}, "Profiles": {"shape": "DashProfiles", "documentation": "<p>The profile that the output is compliant with.</p>"}, "BaseUrls": {"shape": "DashBaseUrls", "documentation": "<p>The base URLs to use for retrieving segments.</p>"}, "ProgramInformation": {"shape": "DashProgramInformation", "documentation": "<p>Details about the content that you want MediaPackage to pass through in the manifest to the playback device.</p>"}, "DvbSettings": {"shape": "DashDvbSettings", "documentation": "<p>For endpoints that use the DVB-DASH profile only. The font download and error reporting information that you want MediaPackage to pass through to the manifest.</p>"}, "Compactness": {"shape": "DashCompactness", "documentation": "<p>The layout of the DASH manifest that MediaPackage produces. <code>STANDARD</code> indicates a default manifest, which is compacted. <code>NONE</code> indicates a full manifest.</p> <p>For information about compactness, see <a href=\"https://docs.aws.amazon.com/mediapackage/latest/userguide/compacted.html\">DASH manifest compactness</a> in the <i>Elemental MediaPackage v2 User Guide</i>.</p>"}, "SubtitleConfiguration": {"shape": "DashSubtitleConfiguration", "documentation": "<p>The configuration for DASH subtitles.</p>"}}, "documentation": "<p>Create a DASH manifest configuration.</p>"}, "CreateDashManifestConfigurationManifestWindowSecondsInteger": {"type": "integer", "box": true, "min": 30}, "CreateDashManifestConfigurationMinBufferTimeSecondsInteger": {"type": "integer", "box": true, "max": 3600, "min": 0}, "CreateDashManifestConfigurationMinUpdatePeriodSecondsInteger": {"type": "integer", "box": true, "max": 3600, "min": 1}, "CreateDashManifestConfigurationSuggestedPresentationDelaySecondsInteger": {"type": "integer", "box": true, "max": 3600, "min": 0}, "CreateDashManifests": {"type": "list", "member": {"shape": "CreateDashManifestConfiguration"}}, "CreateHarvestJobRequest": {"type": "structure", "required": ["ChannelGroupName", "ChannelName", "OriginEndpointName", "HarvestedManifests", "ScheduleConfiguration", "Destination"], "members": {"ChannelGroupName": {"shape": "ResourceName", "documentation": "<p>The name of the channel group containing the channel from which to harvest content.</p>", "location": "uri", "locationName": "ChannelGroupName"}, "ChannelName": {"shape": "ResourceName", "documentation": "<p>The name of the channel from which to harvest content.</p>", "location": "uri", "locationName": "ChannelName"}, "OriginEndpointName": {"shape": "ResourceName", "documentation": "<p>The name of the origin endpoint from which to harvest content.</p>", "location": "uri", "locationName": "OriginEndpointName"}, "Description": {"shape": "ResourceDescription", "documentation": "<p>An optional description for the harvest job.</p>"}, "HarvestedManifests": {"shape": "HarvestedManifests", "documentation": "<p>A list of manifests to be harvested.</p>"}, "ScheduleConfiguration": {"shape": "HarvesterScheduleConfiguration", "documentation": "<p>The configuration for when the harvest job should run, including start and end times.</p>"}, "Destination": {"shape": "Destination", "documentation": "<p>The S3 destination where the harvested content will be placed.</p>"}, "ClientToken": {"shape": "IdempotencyToken", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency of the request.</p>", "idempotencyToken": true, "location": "header", "locationName": "x-amzn-client-token"}, "HarvestJobName": {"shape": "ResourceName", "documentation": "<p>A name for the harvest job. This name must be unique within the channel.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>A collection of tags associated with the harvest job.</p>"}}, "documentation": "<p>The request object for creating a new harvest job.</p>"}, "CreateHarvestJobResponse": {"type": "structure", "required": ["ChannelGroupName", "ChannelName", "OriginEndpointName", "Destination", "HarvestJobName", "HarvestedManifests", "ScheduleConfiguration", "<PERSON><PERSON>", "CreatedAt", "ModifiedAt", "Status"], "members": {"ChannelGroupName": {"shape": "ResourceName", "documentation": "<p>The name of the channel group containing the channel from which content is being harvested.</p>"}, "ChannelName": {"shape": "ResourceName", "documentation": "<p>The name of the channel from which content is being harvested.</p>"}, "OriginEndpointName": {"shape": "ResourceName", "documentation": "<p>The name of the origin endpoint from which content is being harvested.</p>"}, "Destination": {"shape": "Destination", "documentation": "<p>The S3 destination where the harvested content will be placed.</p>"}, "HarvestJobName": {"shape": "ResourceName", "documentation": "<p>The name of the created harvest job.</p>"}, "HarvestedManifests": {"shape": "HarvestedManifests", "documentation": "<p>A list of manifests that will be harvested.</p>"}, "Description": {"shape": "ResourceDescription", "documentation": "<p>The description of the harvest job, if provided.</p>"}, "ScheduleConfiguration": {"shape": "HarvesterScheduleConfiguration", "documentation": "<p>The configuration for when the harvest job will run, including start and end times.</p>"}, "Arn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the created harvest job.</p>"}, "CreatedAt": {"shape": "Timestamp", "documentation": "<p>The date and time the harvest job was created.</p>"}, "ModifiedAt": {"shape": "Timestamp", "documentation": "<p>The date and time the harvest job was last modified.</p>"}, "Status": {"shape": "HarvestJobStatus", "documentation": "<p>The current status of the harvest job (e.g., CREATED, IN_PROGRESS, ABORTED, COMPLETED, FAILED).</p>"}, "ErrorMessage": {"shape": "String", "documentation": "<p>An error message if the harvest job creation failed.</p>"}, "ETag": {"shape": "EntityTag", "documentation": "<p>The current version of the harvest job. Used for concurrency control.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>A collection of tags associated with the harvest job.</p>"}}, "documentation": "<p>The response object returned after creating a harvest job.</p>"}, "CreateHlsManifestConfiguration": {"type": "structure", "required": ["ManifestName"], "members": {"ManifestName": {"shape": "ManifestName", "documentation": "<p>A short short string that's appended to the endpoint URL. The manifest name creates a unique path to this endpoint. If you don't enter a value, MediaPackage uses the default manifest name, index. MediaPackage automatically inserts the format extension, such as .m3u8. You can't use the same manifest name if you use HLS manifest and low-latency HLS manifest. The manifestName on the HLSManifest object overrides the manifestName you provided on the originEndpoint object.</p>"}, "ChildManifestName": {"shape": "ManifestName", "documentation": "<p>A short string that's appended to the endpoint URL. The child manifest name creates a unique path to this endpoint. If you don't enter a value, MediaPackage uses the default manifest name, index, with an added suffix to distinguish it from the manifest name. The manifestName on the HLSManifest object overrides the manifestName you provided on the originEndpoint object.</p>"}, "ScteHls": {"shape": "ScteHls"}, "StartTag": {"shape": "StartTag"}, "ManifestWindowSeconds": {"shape": "CreateHlsManifestConfigurationManifestWindowSecondsInteger", "documentation": "<p>The total duration (in seconds) of the manifest's content.</p>"}, "ProgramDateTimeIntervalSeconds": {"shape": "CreateHlsManifestConfigurationProgramDateTimeIntervalSecondsInteger", "documentation": "<p>Inserts EXT-X-PROGRAM-DATE-TIME tags in the output manifest at the interval that you specify. If you don't enter an interval, EXT-X-PROGRAM-DATE-TIME tags aren't included in the manifest. The tags sync the stream to the wall clock so that viewers can seek to a specific time in the playback timeline on the player.</p> <p>Irrespective of this parameter, if any ID3Timed metadata is in the HLS input, it is passed through to the HLS output.</p>"}, "FilterConfiguration": {"shape": "FilterConfiguration"}, "UrlEncodeChildManifest": {"shape": "Boolean", "documentation": "<p>When enabled, MediaPackage URL-encodes the query string for API requests for HLS child manifests to comply with Amazon Web Services Signature Version 4 (SigV4) signature signing protocol. For more information, see <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/reference_sigv.html\">Amazon Web Services Signature Version 4 for API requests</a> in <i>Identity and Access Management User Guide</i>.</p>"}}, "documentation": "<p>Create an HTTP live streaming (HLS) manifest configuration.</p>"}, "CreateHlsManifestConfigurationManifestWindowSecondsInteger": {"type": "integer", "box": true, "min": 30}, "CreateHlsManifestConfigurationProgramDateTimeIntervalSecondsInteger": {"type": "integer", "box": true, "max": 1209600, "min": 1}, "CreateHlsManifests": {"type": "list", "member": {"shape": "CreateHlsManifestConfiguration"}}, "CreateLowLatencyHlsManifestConfiguration": {"type": "structure", "required": ["ManifestName"], "members": {"ManifestName": {"shape": "ManifestName", "documentation": "<p>A short short string that's appended to the endpoint URL. The manifest name creates a unique path to this endpoint. If you don't enter a value, MediaPackage uses the default manifest name, index. MediaPackage automatically inserts the format extension, such as .m3u8. You can't use the same manifest name if you use HLS manifest and low-latency HLS manifest. The manifestName on the HLSManifest object overrides the manifestName you provided on the originEndpoint object.</p>"}, "ChildManifestName": {"shape": "ManifestName", "documentation": "<p>A short string that's appended to the endpoint URL. The child manifest name creates a unique path to this endpoint. If you don't enter a value, MediaPackage uses the default manifest name, index, with an added suffix to distinguish it from the manifest name. The manifestName on the HLSManifest object overrides the manifestName you provided on the originEndpoint object.</p>"}, "ScteHls": {"shape": "ScteHls"}, "StartTag": {"shape": "StartTag"}, "ManifestWindowSeconds": {"shape": "CreateLowLatencyHlsManifestConfigurationManifestWindowSecondsInteger", "documentation": "<p>The total duration (in seconds) of the manifest's content.</p>"}, "ProgramDateTimeIntervalSeconds": {"shape": "CreateLowLatencyHlsManifestConfigurationProgramDateTimeIntervalSecondsInteger", "documentation": "<p>Inserts EXT-X-PROGRAM-DATE-TIME tags in the output manifest at the interval that you specify. If you don't enter an interval, EXT-X-PROGRAM-DATE-TIME tags aren't included in the manifest. The tags sync the stream to the wall clock so that viewers can seek to a specific time in the playback timeline on the player.</p> <p>Irrespective of this parameter, if any ID3Timed metadata is in the HLS input, it is passed through to the HLS output.</p>"}, "FilterConfiguration": {"shape": "FilterConfiguration"}, "UrlEncodeChildManifest": {"shape": "Boolean", "documentation": "<p>When enabled, MediaPackage URL-encodes the query string for API requests for LL-HLS child manifests to comply with Amazon Web Services Signature Version 4 (SigV4) signature signing protocol. For more information, see <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/reference_sigv.html\">Amazon Web Services Signature Version 4 for API requests</a> in <i>Identity and Access Management User Guide</i>.</p>"}}, "documentation": "<p>Create a low-latency HTTP live streaming (HLS) manifest configuration.</p>"}, "CreateLowLatencyHlsManifestConfigurationManifestWindowSecondsInteger": {"type": "integer", "box": true, "min": 30}, "CreateLowLatencyHlsManifestConfigurationProgramDateTimeIntervalSecondsInteger": {"type": "integer", "box": true, "max": 1209600, "min": 1}, "CreateLowLatencyHlsManifests": {"type": "list", "member": {"shape": "CreateLowLatencyHlsManifestConfiguration"}}, "CreateMssManifestConfiguration": {"type": "structure", "required": ["ManifestName"], "members": {"ManifestName": {"shape": "ManifestName", "documentation": "<p>A short string that's appended to the endpoint URL to create a unique path to this MSS manifest. The manifest name must be unique within the origin endpoint and can contain letters, numbers, hyphens, and underscores.</p>"}, "ManifestWindowSeconds": {"shape": "CreateMssManifestConfigurationManifestWindowSecondsInteger", "documentation": "<p>The total duration (in seconds) of the manifest window. This determines how much content is available in the manifest at any given time. The manifest window slides forward as new segments become available, maintaining a consistent duration of content. The minimum value is 30 seconds.</p>"}, "FilterConfiguration": {"shape": "FilterConfiguration"}, "ManifestLayout": {"shape": "MssManifestLayout", "documentation": "<p>Determines the layout format of the MSS manifest. This controls how the manifest is structured and presented to client players, affecting compatibility with different MSS-compatible devices and applications.</p>"}}, "documentation": "<p>Configuration parameters for creating a Microsoft Smooth Streaming (MSS) manifest. MSS is a streaming media format developed by Microsoft that delivers adaptive bitrate streaming content to compatible players and devices.</p>"}, "CreateMssManifestConfigurationManifestWindowSecondsInteger": {"type": "integer", "box": true, "min": 30}, "CreateMssManifests": {"type": "list", "member": {"shape": "CreateMssManifestConfiguration"}}, "CreateOriginEndpointRequest": {"type": "structure", "required": ["ChannelGroupName", "ChannelName", "OriginEndpointName", "ContainerType"], "members": {"ChannelGroupName": {"shape": "ResourceName", "documentation": "<p>The name that describes the channel group. The name is the primary identifier for the channel group, and must be unique for your account in the AWS Region.</p>", "location": "uri", "locationName": "ChannelGroupName"}, "ChannelName": {"shape": "ResourceName", "documentation": "<p>The name that describes the channel. The name is the primary identifier for the channel, and must be unique for your account in the AWS Region and channel group. </p>", "location": "uri", "locationName": "ChannelName"}, "OriginEndpointName": {"shape": "ResourceName", "documentation": "<p>The name that describes the origin endpoint. The name is the primary identifier for the origin endpoint, and must be unique for your account in the AWS Region and channel. You can't use spaces in the name. You can't change the name after you create the endpoint.</p>"}, "ContainerType": {"shape": "ContainerType", "documentation": "<p>The type of container to attach to this origin endpoint. A container type is a file format that encapsulates one or more media streams, such as audio and video, into a single file. You can't change the container type after you create the endpoint.</p>"}, "Segment": {"shape": "Segment", "documentation": "<p>The segment configuration, including the segment name, duration, and other configuration values.</p>"}, "ClientToken": {"shape": "IdempotencyToken", "documentation": "<p>A unique, case-sensitive token that you provide to ensure the idempotency of the request.</p>", "idempotencyToken": true, "location": "header", "locationName": "x-amzn-client-token"}, "Description": {"shape": "ResourceDescription", "documentation": "<p>Enter any descriptive text that helps you to identify the origin endpoint.</p>"}, "StartoverWindowSeconds": {"shape": "CreateOriginEndpointRequestStartoverWindowSecondsInteger", "documentation": "<p>The size of the window (in seconds) to create a window of the live stream that's available for on-demand viewing. Viewers can start-over or catch-up on content that falls within the window. The maximum startover window is 1,209,600 seconds (14 days).</p>"}, "HlsManifests": {"shape": "CreateHlsManifests", "documentation": "<p>An HTTP live streaming (HLS) manifest configuration.</p>"}, "LowLatencyHlsManifests": {"shape": "CreateLowLatencyHlsManifests", "documentation": "<p>A low-latency HLS manifest configuration.</p>"}, "DashManifests": {"shape": "CreateDashManifests", "documentation": "<p>A DASH manifest configuration.</p>"}, "MssManifests": {"shape": "CreateMssManifests", "documentation": "<p>A list of Microsoft Smooth Streaming (MSS) manifest configurations for the origin endpoint. You can configure multiple MSS manifests to provide different streaming experiences or to support different client requirements.</p>"}, "ForceEndpointErrorConfiguration": {"shape": "ForceEndpointErrorConfiguration", "documentation": "<p>The failover settings for the endpoint.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>A comma-separated list of tag key:value pairs that you define. For example:</p> <p> <code>\"Key1\": \"Value1\",</code> </p> <p> <code>\"Key2\": \"Value2\"</code> </p>"}}}, "CreateOriginEndpointRequestStartoverWindowSecondsInteger": {"type": "integer", "box": true, "max": 1209600, "min": 60}, "CreateOriginEndpointResponse": {"type": "structure", "required": ["<PERSON><PERSON>", "ChannelGroupName", "ChannelName", "OriginEndpointName", "ContainerType", "Segment", "CreatedAt", "ModifiedAt"], "members": {"Arn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) associated with the resource.</p>"}, "ChannelGroupName": {"shape": "ResourceName", "documentation": "<p>The name that describes the channel group. The name is the primary identifier for the channel group, and must be unique for your account in the AWS Region.</p>"}, "ChannelName": {"shape": "ResourceName", "documentation": "<p>The name that describes the channel. The name is the primary identifier for the channel, and must be unique for your account in the AWS Region and channel group.</p>"}, "OriginEndpointName": {"shape": "ResourceName", "documentation": "<p>The name that describes the origin endpoint. The name is the primary identifier for the origin endpoint, and and must be unique for your account in the AWS Region and channel.</p>"}, "ContainerType": {"shape": "ContainerType", "documentation": "<p>The type of container attached to this origin endpoint.</p>"}, "Segment": {"shape": "Segment", "documentation": "<p>The segment configuration, including the segment name, duration, and other configuration values.</p>"}, "CreatedAt": {"shape": "Timestamp", "documentation": "<p>The date and time the origin endpoint was created.</p>"}, "ModifiedAt": {"shape": "Timestamp", "documentation": "<p>The date and time the origin endpoint was modified.</p>"}, "Description": {"shape": "ResourceDescription", "documentation": "<p>The description for your origin endpoint.</p>"}, "StartoverWindowSeconds": {"shape": "Integer", "documentation": "<p>The size of the window (in seconds) to create a window of the live stream that's available for on-demand viewing. Viewers can start-over or catch-up on content that falls within the window.</p>"}, "HlsManifests": {"shape": "GetHlsManifests", "documentation": "<p>An HTTP live streaming (HLS) manifest configuration.</p>"}, "LowLatencyHlsManifests": {"shape": "GetLowLatencyHlsManifests", "documentation": "<p>A low-latency HLS manifest configuration.</p>"}, "DashManifests": {"shape": "GetDashManifests", "documentation": "<p>A DASH manifest configuration.</p>"}, "MssManifests": {"shape": "GetMssManifests", "documentation": "<p>The Microsoft Smooth Streaming (MSS) manifest configurations that were created for this origin endpoint.</p>"}, "ForceEndpointErrorConfiguration": {"shape": "ForceEndpointErrorConfiguration", "documentation": "<p>The failover settings for the endpoint.</p>"}, "ETag": {"shape": "EntityTag", "documentation": "<p>The current Entity Tag (ETag) associated with this resource. The entity tag can be used to safely make concurrent updates to the resource.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>The comma-separated list of tag key:value pairs assigned to the origin endpoint.</p>"}}}, "DashBaseUrl": {"type": "structure", "required": ["Url"], "members": {"Url": {"shape": "DashBaseUrlUrlString", "documentation": "<p>A source location for segments.</p>"}, "ServiceLocation": {"shape": "DashBaseUrlServiceLocationString", "documentation": "<p>The name of the source location.</p>"}, "DvbPriority": {"shape": "DashBaseUrlDvbPriorityInteger", "documentation": "<p>For use with DVB-DASH profiles only. The priority of this location for servings segments. The lower the number, the higher the priority.</p>"}, "DvbWeight": {"shape": "DashBaseUrlDvbWeightInteger", "documentation": "<p>For use with DVB-DASH profiles only. The weighting for source locations that have the same priority. </p>"}}, "documentation": "<p>The base URLs to use for retrieving segments. You can specify multiple locations and indicate the priority and weight for when each should be used, for use in mutli-CDN workflows.</p>"}, "DashBaseUrlDvbPriorityInteger": {"type": "integer", "box": true, "max": 15000, "min": 1}, "DashBaseUrlDvbWeightInteger": {"type": "integer", "box": true, "max": 15000, "min": 1}, "DashBaseUrlServiceLocationString": {"type": "string", "max": 2048, "min": 1}, "DashBaseUrlUrlString": {"type": "string", "max": 2048, "min": 1}, "DashBaseUrls": {"type": "list", "member": {"shape": "DashBaseUrl"}, "max": 20, "min": 0}, "DashCompactness": {"type": "string", "enum": ["STANDARD", "NONE"]}, "DashDrmSignaling": {"type": "string", "enum": ["INDIVIDUAL", "REFERENCED"]}, "DashDvbErrorMetrics": {"type": "list", "member": {"shape": "DashDvbMetricsReporting"}, "max": 20, "min": 0}, "DashDvbFontDownload": {"type": "structure", "members": {"Url": {"shape": "DashDvbFontDownloadUrlString", "documentation": "<p>The URL for downloading fonts for subtitles.</p>"}, "MimeType": {"shape": "DashDvbFontDownloadMimeTypeString", "documentation": "<p>The <code>mimeType</code> of the resource that's at the font download URL.</p> <p>For information about font MIME types, see the <a href=\"https://dvb.org/wp-content/uploads/2021/06/A168r4_MPEG-DASH-Profile-for-Transport-of-ISO-BMFF-Based-DVB-Services_Draft-ts_103-285-v140_November_2021.pdf\">MPEG-DASH Profile for Transport of ISO BMFF Based DVB Services over IP Based Networks</a> document. </p>"}, "FontFamily": {"shape": "DashDvbFontDownloadFontFamilyString", "documentation": "<p>The <code>fontFamily</code> name for subtitles, as described in <a href=\"https://tech.ebu.ch/publications/tech3380\">EBU-TT-D Subtitling Distribution Format</a>. </p>"}}, "documentation": "<p>For use with DVB-DASH profiles only. The settings for font downloads that you want Elemental MediaPackage to pass through to the manifest.</p>"}, "DashDvbFontDownloadFontFamilyString": {"type": "string", "max": 256, "min": 1}, "DashDvbFontDownloadMimeTypeString": {"type": "string", "max": 256, "min": 1, "pattern": "[a-zA-Z0-9][a-zA-Z0-9_/-]*[a-zA-Z0-9]"}, "DashDvbFontDownloadUrlString": {"type": "string", "max": 2048, "min": 1}, "DashDvbMetricsReporting": {"type": "structure", "required": ["ReportingUrl"], "members": {"ReportingUrl": {"shape": "DashDvbMetricsReportingReportingUrlString", "documentation": "<p>The URL where playback devices send error reports.</p>"}, "Probability": {"shape": "DashDvbMetricsReportingProbabilityInteger", "documentation": "<p>The number of playback devices per 1000 that will send error reports to the reporting URL. This represents the probability that a playback device will be a reporting player for this session.</p>"}}, "documentation": "<p>For use with DVB-DASH profiles only. The settings for error reporting from the playback device that you want Elemental MediaPackage to pass through to the manifest.</p>"}, "DashDvbMetricsReportingProbabilityInteger": {"type": "integer", "box": true, "max": 1000, "min": 1}, "DashDvbMetricsReportingReportingUrlString": {"type": "string", "max": 2048, "min": 1}, "DashDvbSettings": {"type": "structure", "members": {"FontDownload": {"shape": "DashDvbFontDownload", "documentation": "<p>Subtitle font settings.</p>"}, "ErrorMetrics": {"shape": "DashDvbErrorMetrics", "documentation": "<p>Playback device error reporting settings.</p>"}}, "documentation": "<p>For endpoints that use the DVB-DASH profile only. The font download and error reporting information that you want MediaPackage to pass through to the manifest.</p>"}, "DashPeriodTrigger": {"type": "string", "enum": ["AVAILS", "DRM_KEY_ROTATION", "SOURCE_CHANGES", "SOURCE_DISRUPTIONS", "NONE"]}, "DashPeriodTriggers": {"type": "list", "member": {"shape": "DashPeriodTrigger"}, "max": 100, "min": 0}, "DashProfile": {"type": "string", "enum": ["DVB_DASH"]}, "DashProfiles": {"type": "list", "member": {"shape": "DashProfile"}, "max": 5, "min": 0}, "DashProgramInformation": {"type": "structure", "members": {"Title": {"shape": "DashProgramInformationTitleString", "documentation": "<p>The title for the manifest.</p>"}, "Source": {"shape": "DashProgramInformationSourceString", "documentation": "<p>Information about the content provider.</p>"}, "Copyright": {"shape": "DashProgramInformationCopyrightString", "documentation": "<p>A copyright statement about the content.</p>"}, "LanguageCode": {"shape": "DashProgramInformationLanguageCodeString", "documentation": "<p>The language code for this manifest.</p>"}, "MoreInformationUrl": {"shape": "DashProgramInformationMoreInformationUrlString", "documentation": "<p>An absolute URL that contains more information about this content.</p>"}}, "documentation": "<p>Details about the content that you want MediaPackage to pass through in the manifest to the playback device.</p>"}, "DashProgramInformationCopyrightString": {"type": "string", "max": 2048, "min": 1}, "DashProgramInformationLanguageCodeString": {"type": "string", "max": 5, "min": 2, "pattern": "[a-zA-Z0-9][a-zA-Z0-9_-]*[a-zA-Z0-9]"}, "DashProgramInformationMoreInformationUrlString": {"type": "string", "max": 2048, "min": 1}, "DashProgramInformationSourceString": {"type": "string", "max": 2048, "min": 1}, "DashProgramInformationTitleString": {"type": "string", "max": 2048, "min": 1}, "DashSegmentTemplateFormat": {"type": "string", "enum": ["NUMBER_WITH_TIMELINE"]}, "DashSubtitleConfiguration": {"type": "structure", "members": {"TtmlConfiguration": {"shape": "DashTtmlConfiguration", "documentation": "<p>Settings for TTML subtitles.</p>"}}, "documentation": "<p>The configuration for DASH subtitles.</p>"}, "DashTtmlConfiguration": {"type": "structure", "required": ["TtmlProfile"], "members": {"TtmlProfile": {"shape": "DashTtmlProfile", "documentation": "<p>The profile that MediaPackage uses when signaling subtitles in the manifest. <code>IMSC</code> is the default profile. <code>EBU-TT-D</code> produces subtitles that are compliant with the EBU-TT-D TTML profile. MediaPackage passes through subtitle styles to the manifest. For more information about EBU-TT-D subtitles, see <a href=\"https://tech.ebu.ch/publications/tech3380\">EBU-TT-D Subtitling Distribution Format</a>.</p>"}}, "documentation": "<p>The settings for TTML subtitles.</p>"}, "DashTtmlProfile": {"type": "string", "enum": ["IMSC_1", "EBU_TT_D_101"]}, "DashUtcTiming": {"type": "structure", "members": {"TimingMode": {"shape": "DashUtcTimingMode", "documentation": "<p>The UTC timing mode.</p>"}, "TimingSource": {"shape": "DashUtcTimingTimingSourceString", "documentation": "<p>The the method that the player uses to synchronize to coordinated universal time (UTC) wall clock time.</p>"}}, "documentation": "<p>Determines the type of UTC timing included in the DASH Media Presentation Description (MPD).</p>"}, "DashUtcTimingMode": {"type": "string", "enum": ["HTTP_HEAD", "HTTP_ISO", "HTTP_XSDATE", "UTC_DIRECT"]}, "DashUtcTimingTimingSourceString": {"type": "string", "max": 1024, "min": 1}, "DeleteChannelGroupRequest": {"type": "structure", "required": ["ChannelGroupName"], "members": {"ChannelGroupName": {"shape": "ResourceName", "documentation": "<p>The name that describes the channel group. The name is the primary identifier for the channel group, and must be unique for your account in the AWS Region.</p>", "location": "uri", "locationName": "ChannelGroupName"}}}, "DeleteChannelGroupResponse": {"type": "structure", "members": {}}, "DeleteChannelPolicyRequest": {"type": "structure", "required": ["ChannelGroupName", "ChannelName"], "members": {"ChannelGroupName": {"shape": "ResourceName", "documentation": "<p>The name that describes the channel group. The name is the primary identifier for the channel group, and must be unique for your account in the AWS Region.</p>", "location": "uri", "locationName": "ChannelGroupName"}, "ChannelName": {"shape": "ResourceName", "documentation": "<p>The name that describes the channel. The name is the primary identifier for the channel, and must be unique for your account in the AWS Region and channel group.</p>", "location": "uri", "locationName": "ChannelName"}}}, "DeleteChannelPolicyResponse": {"type": "structure", "members": {}}, "DeleteChannelRequest": {"type": "structure", "required": ["ChannelGroupName", "ChannelName"], "members": {"ChannelGroupName": {"shape": "ResourceName", "documentation": "<p>The name that describes the channel group. The name is the primary identifier for the channel group, and must be unique for your account in the AWS Region.</p>", "location": "uri", "locationName": "ChannelGroupName"}, "ChannelName": {"shape": "ResourceName", "documentation": "<p>The name that describes the channel. The name is the primary identifier for the channel, and must be unique for your account in the AWS Region and channel group.</p>", "location": "uri", "locationName": "ChannelName"}}}, "DeleteChannelResponse": {"type": "structure", "members": {}}, "DeleteOriginEndpointPolicyRequest": {"type": "structure", "required": ["ChannelGroupName", "ChannelName", "OriginEndpointName"], "members": {"ChannelGroupName": {"shape": "ResourceName", "documentation": "<p>The name that describes the channel group. The name is the primary identifier for the channel group, and must be unique for your account in the AWS Region.</p>", "location": "uri", "locationName": "ChannelGroupName"}, "ChannelName": {"shape": "ResourceName", "documentation": "<p>The name that describes the channel. The name is the primary identifier for the channel, and must be unique for your account in the AWS Region and channel group. </p>", "location": "uri", "locationName": "ChannelName"}, "OriginEndpointName": {"shape": "ResourceName", "documentation": "<p>The name that describes the origin endpoint. The name is the primary identifier for the origin endpoint, and and must be unique for your account in the AWS Region and channel. </p>", "location": "uri", "locationName": "OriginEndpointName"}}}, "DeleteOriginEndpointPolicyResponse": {"type": "structure", "members": {}}, "DeleteOriginEndpointRequest": {"type": "structure", "required": ["ChannelGroupName", "ChannelName", "OriginEndpointName"], "members": {"ChannelGroupName": {"shape": "ResourceName", "documentation": "<p>The name that describes the channel group. The name is the primary identifier for the channel group, and must be unique for your account in the AWS Region.</p>", "location": "uri", "locationName": "ChannelGroupName"}, "ChannelName": {"shape": "ResourceName", "documentation": "<p>The name that describes the channel. The name is the primary identifier for the channel, and must be unique for your account in the AWS Region and channel group. </p>", "location": "uri", "locationName": "ChannelName"}, "OriginEndpointName": {"shape": "ResourceName", "documentation": "<p>The name that describes the origin endpoint. The name is the primary identifier for the origin endpoint, and and must be unique for your account in the AWS Region and channel. </p>", "location": "uri", "locationName": "OriginEndpointName"}}}, "DeleteOriginEndpointResponse": {"type": "structure", "members": {}}, "Destination": {"type": "structure", "required": ["S3Destination"], "members": {"S3Destination": {"shape": "S3DestinationConfig", "documentation": "<p>The configuration for exporting harvested content to an S3 bucket. This includes details such as the bucket name and destination path within the bucket.</p>"}}, "documentation": "<p>The configuration for the destination where the harvested content will be exported.</p>"}, "DrmSystem": {"type": "string", "enum": ["CLEAR_KEY_AES_128", "FAIRPLAY", "PLAYREADY", "WIDEVINE", "IRDETO"]}, "Encryption": {"type": "structure", "required": ["EncryptionMethod", "SpekeKeyProvider"], "members": {"ConstantInitializationVector": {"shape": "EncryptionConstantInitializationVectorString", "documentation": "<p>A 128-bit, 16-byte hex value represented by a 32-character string, used in conjunction with the key for encrypting content. If you don't specify a value, then MediaPackage creates the constant initialization vector (IV).</p>"}, "EncryptionMethod": {"shape": "EncryptionMethod", "documentation": "<p>The encryption method to use.</p>"}, "KeyRotationIntervalSeconds": {"shape": "EncryptionKeyRotationIntervalSecondsInteger", "documentation": "<p>The frequency (in seconds) of key changes for live workflows, in which content is streamed real time. The service retrieves content keys before the live content begins streaming, and then retrieves them as needed over the lifetime of the workflow. By default, key rotation is set to 300 seconds (5 minutes), the minimum rotation interval, which is equivalent to setting it to 300. If you don't enter an interval, content keys aren't rotated.</p> <p>The following example setting causes the service to rotate keys every thirty minutes: <code>1800</code> </p>"}, "CmafExcludeSegmentDrmMetadata": {"shape": "Boolean", "documentation": "<p>Excludes SEIG and SGPD boxes from segment metadata in CMAF containers.</p> <p>When set to <code>true</code>, MediaPackage omits these DRM metadata boxes from CMAF segments, which can improve compatibility with certain devices and players that don't support these boxes.</p> <p>Important considerations:</p> <ul> <li> <p>This setting only affects CMAF container formats</p> </li> <li> <p>Key rotation can still be handled through media playlist signaling</p> </li> <li> <p>PSSH and TENC boxes remain unaffected</p> </li> <li> <p>Default behavior is preserved when this setting is disabled</p> </li> </ul> <p>Valid values: <code>true</code> | <code>false</code> </p> <p>Default: <code>false</code> </p>"}, "SpekeKeyProvider": {"shape": "SpekeKeyProvider", "documentation": "<p>The parameters for the SPEKE key provider.</p>"}}, "documentation": "<p>The parameters for encrypting content.</p>"}, "EncryptionConstantInitializationVectorString": {"type": "string", "max": 32, "min": 32, "pattern": "[0-9a-fA-F]+"}, "EncryptionContractConfiguration": {"type": "structure", "required": ["PresetSpeke20Audio", "PresetSpeke20Video"], "members": {"PresetSpeke20Audio": {"shape": "PresetSpeke20Audio", "documentation": "<p>A collection of audio encryption presets.</p> <p>Value description: </p> <ul> <li> <p>PRESET-AUDIO-1 - Use one content key to encrypt all of the audio tracks in your stream.</p> </li> <li> <p>PRESET-AUDIO-2 - Use one content key to encrypt all of the stereo audio tracks and one content key to encrypt all of the multichannel audio tracks.</p> </li> <li> <p>PRESET-AUDIO-3 - Use one content key to encrypt all of the stereo audio tracks, one content key to encrypt all of the multichannel audio tracks with 3 to 6 channels, and one content key to encrypt all of the multichannel audio tracks with more than 6 channels.</p> </li> <li> <p>SHARED - Use the same content key for all of the audio and video tracks in your stream.</p> </li> <li> <p>UNENCRYPTED - Don't encrypt any of the audio tracks in your stream.</p> </li> </ul>"}, "PresetSpeke20Video": {"shape": "PresetSpeke20Video", "documentation": "<p>A collection of video encryption presets.</p> <p>Value description: </p> <ul> <li> <p>PRESET-VIDEO-1 - Use one content key to encrypt all of the video tracks in your stream.</p> </li> <li> <p>PRESET-VIDEO-2 - Use one content key to encrypt all of the SD video tracks and one content key for all HD and higher resolutions video tracks.</p> </li> <li> <p>PRESET-VIDEO-3 - Use one content key to encrypt all of the SD video tracks, one content key for HD video tracks and one content key for all UHD video tracks.</p> </li> <li> <p>PRESET-VIDEO-4 - Use one content key to encrypt all of the SD video tracks, one content key for HD video tracks, one content key for all UHD1 video tracks and one content key for all UHD2 video tracks.</p> </li> <li> <p>PRESET-VIDEO-5 - Use one content key to encrypt all of the SD video tracks, one content key for HD1 video tracks, one content key for HD2 video tracks, one content key for all UHD1 video tracks and one content key for all UHD2 video tracks.</p> </li> <li> <p>PRESET-VIDEO-6 - Use one content key to encrypt all of the SD video tracks, one content key for HD1 video tracks, one content key for HD2 video tracks and one content key for all UHD video tracks.</p> </li> <li> <p>PRESET-VIDEO-7 - Use one content key to encrypt all of the SD+HD1 video tracks, one content key for HD2 video tracks and one content key for all UHD video tracks.</p> </li> <li> <p>PRESET-VIDEO-8 - Use one content key to encrypt all of the SD+HD1 video tracks, one content key for HD2 video tracks, one content key for all UHD1 video tracks and one content key for all UHD2 video tracks.</p> </li> <li> <p>SHARED - Use the same content key for all of the video and audio tracks in your stream.</p> </li> <li> <p>UNENCRYPTED - Don't encrypt any of the video tracks in your stream.</p> </li> </ul>"}}, "documentation": "<p>Configure one or more content encryption keys for your endpoints that use SPEKE Version 2.0. The encryption contract defines which content keys are used to encrypt the audio and video tracks in your stream. To configure the encryption contract, specify which audio and video encryption presets to use.</p>"}, "EncryptionKeyRotationIntervalSecondsInteger": {"type": "integer", "box": true, "max": 31536000, "min": 300}, "EncryptionMethod": {"type": "structure", "members": {"TsEncryptionMethod": {"shape": "TsEncryptionMethod", "documentation": "<p>The encryption method to use.</p>"}, "CmafEncryptionMethod": {"shape": "CmafEncryptionMethod", "documentation": "<p>The encryption method to use.</p>"}, "IsmEncryptionMethod": {"shape": "IsmEncryptionMethod", "documentation": "<p>The encryption method used for Microsoft Smooth Streaming (MSS) content. This specifies how the MSS segments are encrypted to protect the content during delivery to client players.</p>"}}, "documentation": "<p>The encryption type.</p>"}, "EndpointErrorCondition": {"type": "string", "enum": ["STALE_MANIFEST", "INCOMPLETE_MANIFEST", "MISSING_DRM_KEY", "SLATE_INPUT"]}, "EndpointErrorConditions": {"type": "list", "member": {"shape": "EndpointErrorCondition"}}, "EntityTag": {"type": "string", "max": 256, "min": 1, "pattern": "[\\S]+"}, "FilterConfiguration": {"type": "structure", "members": {"ManifestFilter": {"shape": "FilterConfigurationManifestFilterString", "documentation": "<p>Optionally specify one or more manifest filters for all of your manifest egress requests. When you include a manifest filter, note that you cannot use an identical manifest filter query parameter for this manifest's endpoint URL.</p>"}, "Start": {"shape": "Timestamp", "documentation": "<p>Optionally specify the start time for all of your manifest egress requests. When you include start time, note that you cannot use start time query parameters for this manifest's endpoint URL.</p>"}, "End": {"shape": "Timestamp", "documentation": "<p>Optionally specify the end time for all of your manifest egress requests. When you include end time, note that you cannot use end time query parameters for this manifest's endpoint URL.</p>"}, "TimeDelaySeconds": {"shape": "FilterConfigurationTimeDelaySecondsInteger", "documentation": "<p>Optionally specify the time delay for all of your manifest egress requests. Enter a value that is smaller than your endpoint's startover window. When you include time delay, note that you cannot use time delay query parameters for this manifest's endpoint URL.</p>"}, "ClipStartTime": {"shape": "Timestamp", "documentation": "<p>Optionally specify the clip start time for all of your manifest egress requests. When you include clip start time, note that you cannot use clip start time query parameters for this manifest's endpoint URL.</p>"}}, "documentation": "<p>Filter configuration includes settings for manifest filtering, start and end times, and time delay that apply to all of your egress requests for this manifest. </p>"}, "FilterConfigurationManifestFilterString": {"type": "string", "max": 1024, "min": 1}, "FilterConfigurationTimeDelaySecondsInteger": {"type": "integer", "box": true, "max": 1209600, "min": 0}, "Float": {"type": "float", "box": true}, "ForceEndpointErrorConfiguration": {"type": "structure", "members": {"EndpointErrorConditions": {"shape": "EndpointErrorConditions", "documentation": "<p>The failover conditions for the endpoint. The options are:</p> <ul> <li> <p> <code>STALE_MANIFEST</code> - The manifest stalled and there are no new segments or parts.</p> </li> <li> <p> <code>INCOMPLETE_MANIFEST</code> - There is a gap in the manifest.</p> </li> <li> <p> <code>MISSING_DRM_KEY</code> - Key rotation is enabled but we're unable to fetch the key for the current key period.</p> </li> <li> <p> <code>SLATE_INPUT</code> - The segments which contain slate content are considered to be missing content.</p> </li> </ul>"}}, "documentation": "<p>The failover settings for the endpoint.</p>"}, "GetChannelGroupRequest": {"type": "structure", "required": ["ChannelGroupName"], "members": {"ChannelGroupName": {"shape": "ResourceName", "documentation": "<p>The name that describes the channel group. The name is the primary identifier for the channel group, and must be unique for your account in the AWS Region.</p>", "location": "uri", "locationName": "ChannelGroupName"}}}, "GetChannelGroupResponse": {"type": "structure", "required": ["ChannelGroupName", "<PERSON><PERSON>", "EgressDomain", "CreatedAt", "ModifiedAt"], "members": {"ChannelGroupName": {"shape": "String", "documentation": "<p>The name that describes the channel group. The name is the primary identifier for the channel group, and must be unique for your account in the AWS Region.</p>"}, "Arn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) associated with the resource.</p>"}, "EgressDomain": {"shape": "String", "documentation": "<p>The output domain where the source stream should be sent. Integrate the domain with a downstream CDN (such as Amazon CloudFront) or playback device.</p>"}, "CreatedAt": {"shape": "Timestamp", "documentation": "<p>The date and time the channel group was created.</p>"}, "ModifiedAt": {"shape": "Timestamp", "documentation": "<p>The date and time the channel group was modified.</p>"}, "Description": {"shape": "ResourceDescription", "documentation": "<p>The description for your channel group.</p>"}, "ETag": {"shape": "EntityTag", "documentation": "<p>The current Entity Tag (ETag) associated with this resource. The entity tag can be used to safely make concurrent updates to the resource.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>The comma-separated list of tag key:value pairs assigned to the channel group.</p>", "locationName": "tags"}}}, "GetChannelPolicyRequest": {"type": "structure", "required": ["ChannelGroupName", "ChannelName"], "members": {"ChannelGroupName": {"shape": "ResourceName", "documentation": "<p>The name that describes the channel group. The name is the primary identifier for the channel group, and must be unique for your account in the AWS Region.</p>", "location": "uri", "locationName": "ChannelGroupName"}, "ChannelName": {"shape": "ResourceName", "documentation": "<p>The name that describes the channel. The name is the primary identifier for the channel, and must be unique for your account in the AWS Region and channel group. </p>", "location": "uri", "locationName": "ChannelName"}}}, "GetChannelPolicyResponse": {"type": "structure", "required": ["ChannelGroupName", "ChannelName", "Policy"], "members": {"ChannelGroupName": {"shape": "String", "documentation": "<p>The name that describes the channel group. The name is the primary identifier for the channel group, and must be unique for your account in the AWS Region.</p>"}, "ChannelName": {"shape": "String", "documentation": "<p>The name that describes the channel. The name is the primary identifier for the channel, and must be unique for your account in the AWS Region and channel group.</p>"}, "Policy": {"shape": "PolicyText", "documentation": "<p>The policy assigned to the channel.</p>"}}}, "GetChannelRequest": {"type": "structure", "required": ["ChannelGroupName", "ChannelName"], "members": {"ChannelGroupName": {"shape": "ResourceName", "documentation": "<p>The name that describes the channel group. The name is the primary identifier for the channel group, and must be unique for your account in the AWS Region.</p>", "location": "uri", "locationName": "ChannelGroupName"}, "ChannelName": {"shape": "ResourceName", "documentation": "<p>The name that describes the channel. The name is the primary identifier for the channel, and must be unique for your account in the AWS Region and channel group. </p>", "location": "uri", "locationName": "ChannelName"}}}, "GetChannelResponse": {"type": "structure", "required": ["<PERSON><PERSON>", "ChannelName", "ChannelGroupName", "CreatedAt", "ModifiedAt"], "members": {"Arn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) associated with the resource.</p>"}, "ChannelName": {"shape": "String", "documentation": "<p>The name that describes the channel. The name is the primary identifier for the channel, and must be unique for your account in the AWS Region and channel group.</p>"}, "ChannelGroupName": {"shape": "String", "documentation": "<p>The name that describes the channel group. The name is the primary identifier for the channel group, and must be unique for your account in the AWS Region.</p>"}, "CreatedAt": {"shape": "Timestamp", "documentation": "<p>The date and time the channel was created.</p>"}, "ModifiedAt": {"shape": "Timestamp", "documentation": "<p>The date and time the channel was modified.</p>"}, "ResetAt": {"shape": "Timestamp", "documentation": "<p>The time that the channel was last reset.</p>"}, "Description": {"shape": "ResourceDescription", "documentation": "<p>The description for your channel.</p>"}, "IngestEndpoints": {"shape": "IngestEndpointList"}, "InputType": {"shape": "InputType", "documentation": "<p>The input type will be an immutable field which will be used to define whether the channel will allow CMAF ingest or HLS ingest. If unprovided, it will default to HLS to preserve current behavior.</p> <p>The allowed values are:</p> <ul> <li> <p> <code>HLS</code> - The HLS streaming specification (which defines M3U8 manifests and TS segments).</p> </li> <li> <p> <code>CMAF</code> - The DASH-IF CMAF Ingest specification (which defines CMAF segments with optional DASH manifests).</p> </li> </ul>"}, "ETag": {"shape": "EntityTag", "documentation": "<p>The current Entity Tag (ETag) associated with this resource. The entity tag can be used to safely make concurrent updates to the resource.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>The comma-separated list of tag key:value pairs assigned to the channel.</p>"}, "InputSwitchConfiguration": {"shape": "InputSwitchConfiguration", "documentation": "<p>The configuration for input switching based on the media quality confidence score (MQCS) as provided from AWS Elemental MediaLive. This setting is valid only when <code>InputType</code> is <code>CMAF</code>.</p>"}, "OutputHeaderConfiguration": {"shape": "OutputHeaderConfiguration", "documentation": "<p>The settings for what common media server data (CMSD) headers AWS Elemental MediaPackage includes in responses to the CDN. This setting is valid only when <code>InputType</code> is <code>CMAF</code>.</p>"}}}, "GetDashManifestConfiguration": {"type": "structure", "required": ["ManifestName", "Url"], "members": {"ManifestName": {"shape": "ResourceName", "documentation": "<p>A short string that's appended to the endpoint URL. The manifest name creates a unique path to this endpoint. If you don't enter a value, MediaPackage uses the default manifest name, index. </p>"}, "Url": {"shape": "String", "documentation": "<p>The egress domain URL for stream delivery from MediaPackage.</p>"}, "ManifestWindowSeconds": {"shape": "Integer", "documentation": "<p>The total duration (in seconds) of the manifest's content.</p>"}, "FilterConfiguration": {"shape": "FilterConfiguration"}, "MinUpdatePeriodSeconds": {"shape": "Integer", "documentation": "<p>Minimum amount of time (in seconds) that the player should wait before requesting updates to the manifest.</p>"}, "MinBufferTimeSeconds": {"shape": "Integer", "documentation": "<p>Minimum amount of content (in seconds) that a player must keep available in the buffer.</p>"}, "SuggestedPresentationDelaySeconds": {"shape": "Integer", "documentation": "<p>The amount of time (in seconds) that the player should be from the end of the manifest.</p>"}, "SegmentTemplateFormat": {"shape": "DashSegmentTemplateFormat", "documentation": "<p>Determines the type of variable used in the <code>media</code> URL of the <code>SegmentTemplate</code> tag in the manifest. Also specifies if segment timeline information is included in <code>SegmentTimeline</code> or <code>SegmentTemplate</code>.</p> <p>Value description:</p> <ul> <li> <p> <code>NUMBER_WITH_TIMELINE</code> - The <code>$Number$</code> variable is used in the <code>media</code> URL. The value of this variable is the sequential number of the segment. A full <code>SegmentTimeline</code> object is presented in each <code>SegmentTemplate</code>.</p> </li> </ul>"}, "PeriodTriggers": {"shape": "DashPeriodTriggers", "documentation": "<p>A list of triggers that controls when AWS Elemental MediaPackage separates the MPEG-DASH manifest into multiple periods. Leave this value empty to indicate that the manifest is contained all in one period. For more information about periods in the DASH manifest, see <a href=\"https://docs.aws.amazon.com/mediapackage/latest/userguide/multi-period.html\">Multi-period DASH in AWS Elemental MediaPackage</a>.</p>"}, "ScteDash": {"shape": "ScteDash", "documentation": "<p>The SCTE configuration.</p>"}, "DrmSignaling": {"shape": "DashDrmSignaling", "documentation": "<p>Determines how the DASH manifest signals the DRM content.</p>"}, "UtcTiming": {"shape": "DashUtcTiming", "documentation": "<p>Determines the type of UTC timing included in the DASH Media Presentation Description (MPD).</p>"}, "Profiles": {"shape": "DashProfiles", "documentation": "<p>The profile that the output is compliant with.</p>"}, "BaseUrls": {"shape": "DashBaseUrls", "documentation": "<p>The base URL to use for retrieving segments.</p>"}, "ProgramInformation": {"shape": "DashProgramInformation", "documentation": "<p>Details about the content that you want MediaPackage to pass through in the manifest to the playback device.</p>"}, "DvbSettings": {"shape": "DashDvbSettings", "documentation": "<p>For endpoints that use the DVB-DASH profile only. The font download and error reporting information that you want MediaPackage to pass through to the manifest.</p>"}, "Compactness": {"shape": "DashCompactness", "documentation": "<p>The layout of the DASH manifest that MediaPackage produces. <code>STANDARD</code> indicates a default manifest, which is compacted. <code>NONE</code> indicates a full manifest.</p>"}, "SubtitleConfiguration": {"shape": "DashSubtitleConfiguration", "documentation": "<p>The configuration for DASH subtitles.</p>"}}, "documentation": "<p>Retrieve the DASH manifest configuration.</p>"}, "GetDashManifests": {"type": "list", "member": {"shape": "GetDashManifestConfiguration"}}, "GetHarvestJobRequest": {"type": "structure", "required": ["ChannelGroupName", "ChannelName", "OriginEndpointName", "HarvestJobName"], "members": {"ChannelGroupName": {"shape": "ResourceName", "documentation": "<p>The name of the channel group containing the channel associated with the harvest job.</p>", "location": "uri", "locationName": "ChannelGroupName"}, "ChannelName": {"shape": "ResourceName", "documentation": "<p>The name of the channel associated with the harvest job.</p>", "location": "uri", "locationName": "ChannelName"}, "OriginEndpointName": {"shape": "ResourceName", "documentation": "<p>The name of the origin endpoint associated with the harvest job.</p>", "location": "uri", "locationName": "OriginEndpointName"}, "HarvestJobName": {"shape": "ResourceName", "documentation": "<p>The name of the harvest job to retrieve.</p>", "location": "uri", "locationName": "HarvestJobName"}}, "documentation": "<p>The request object for retrieving a specific harvest job.</p>"}, "GetHarvestJobResponse": {"type": "structure", "required": ["ChannelGroupName", "ChannelName", "OriginEndpointName", "Destination", "HarvestJobName", "HarvestedManifests", "ScheduleConfiguration", "<PERSON><PERSON>", "CreatedAt", "ModifiedAt", "Status"], "members": {"ChannelGroupName": {"shape": "ResourceName", "documentation": "<p>The name of the channel group containing the channel associated with the harvest job.</p>"}, "ChannelName": {"shape": "ResourceName", "documentation": "<p>The name of the channel associated with the harvest job.</p>"}, "OriginEndpointName": {"shape": "ResourceName", "documentation": "<p>The name of the origin endpoint associated with the harvest job.</p>"}, "Destination": {"shape": "Destination", "documentation": "<p>The S3 destination where the harvested content is being placed.</p>"}, "HarvestJobName": {"shape": "ResourceName", "documentation": "<p>The name of the harvest job.</p>"}, "HarvestedManifests": {"shape": "HarvestedManifests", "documentation": "<p>A list of manifests that are being or have been harvested.</p>"}, "Description": {"shape": "ResourceDescription", "documentation": "<p>The description of the harvest job, if provided.</p>"}, "ScheduleConfiguration": {"shape": "HarvesterScheduleConfiguration", "documentation": "<p>The configuration for when the harvest job is scheduled to run, including start and end times.</p>"}, "Arn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the harvest job.</p>"}, "CreatedAt": {"shape": "Timestamp", "documentation": "<p>The date and time when the harvest job was created.</p>"}, "ModifiedAt": {"shape": "Timestamp", "documentation": "<p>The date and time when the harvest job was last modified.</p>"}, "Status": {"shape": "HarvestJobStatus", "documentation": "<p>The current status of the harvest job (e.g., QUEUED, IN_PROGRESS, CANCELLED, COMPLETED, FAILED).</p>"}, "ErrorMessage": {"shape": "String", "documentation": "<p>An error message if the harvest job encountered any issues.</p>"}, "ETag": {"shape": "EntityTag", "documentation": "<p>The current version of the harvest job. Used for concurrency control.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>A collection of tags associated with the harvest job.</p>"}}, "documentation": "<p>The response object containing the details of the requested harvest job.</p>"}, "GetHlsManifestConfiguration": {"type": "structure", "required": ["ManifestName", "Url"], "members": {"ManifestName": {"shape": "ResourceName", "documentation": "<p>A short short string that's appended to the endpoint URL. The manifest name creates a unique path to this endpoint. If you don't enter a value, MediaPackage uses the default manifest name, index. MediaPackage automatically inserts the format extension, such as .m3u8. You can't use the same manifest name if you use HLS manifest and low-latency HLS manifest. The manifestName on the HLSManifest object overrides the manifestName you provided on the originEndpoint object.</p>"}, "Url": {"shape": "String", "documentation": "<p>The egress domain URL for stream delivery from MediaPackage.</p>"}, "ChildManifestName": {"shape": "ResourceName", "documentation": "<p>A short string that's appended to the endpoint URL. The child manifest name creates a unique path to this endpoint. If you don't enter a value, MediaPackage uses the default child manifest name, index_1. The manifestName on the HLSManifest object overrides the manifestName you provided on the originEndpoint object.</p>"}, "ManifestWindowSeconds": {"shape": "Integer", "documentation": "<p>The total duration (in seconds) of the manifest's content.</p>"}, "ProgramDateTimeIntervalSeconds": {"shape": "Integer", "documentation": "<p>Inserts EXT-X-PROGRAM-DATE-TIME tags in the output manifest at the interval that you specify. If you don't enter an interval, EXT-X-PROGRAM-DATE-TIME tags aren't included in the manifest. The tags sync the stream to the wall clock so that viewers can seek to a specific time in the playback timeline on the player.</p> <p>Irrespective of this parameter, if any ID3Timed metadata is in the HLS input, it is passed through to the HLS output.</p>"}, "ScteHls": {"shape": "ScteHls"}, "FilterConfiguration": {"shape": "FilterConfiguration"}, "StartTag": {"shape": "StartTag"}, "UrlEncodeChildManifest": {"shape": "Boolean", "documentation": "<p>When enabled, MediaPackage URL-encodes the query string for API requests for HLS child manifests to comply with Amazon Web Services Signature Version 4 (SigV4) signature signing protocol. For more information, see <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/reference_sigv.html\">Amazon Web Services Signature Version 4 for API requests</a> in <i>Identity and Access Management User Guide</i>.</p>"}}, "documentation": "<p>Retrieve the HTTP live streaming (HLS) manifest configuration.</p>"}, "GetHlsManifests": {"type": "list", "member": {"shape": "GetHlsManifestConfiguration"}}, "GetLowLatencyHlsManifestConfiguration": {"type": "structure", "required": ["ManifestName", "Url"], "members": {"ManifestName": {"shape": "ResourceName", "documentation": "<p>A short short string that's appended to the endpoint URL. The manifest name creates a unique path to this endpoint. If you don't enter a value, MediaPackage uses the default manifest name, index. MediaPackage automatically inserts the format extension, such as .m3u8. You can't use the same manifest name if you use HLS manifest and low-latency HLS manifest. The manifestName on the HLSManifest object overrides the manifestName you provided on the originEndpoint object.</p>"}, "Url": {"shape": "String", "documentation": "<p>The egress domain URL for stream delivery from MediaPackage.</p>"}, "ChildManifestName": {"shape": "ResourceName", "documentation": "<p>A short string that's appended to the endpoint URL. The child manifest name creates a unique path to this endpoint. If you don't enter a value, MediaPackage uses the default child manifest name, index_1. The manifestName on the HLSManifest object overrides the manifestName you provided on the originEndpoint object.</p>"}, "ManifestWindowSeconds": {"shape": "Integer", "documentation": "<p>The total duration (in seconds) of the manifest's content.</p>"}, "ProgramDateTimeIntervalSeconds": {"shape": "Integer", "documentation": "<p>Inserts EXT-X-PROGRAM-DATE-TIME tags in the output manifest at the interval that you specify. If you don't enter an interval, EXT-X-PROGRAM-DATE-TIME tags aren't included in the manifest. The tags sync the stream to the wall clock so that viewers can seek to a specific time in the playback timeline on the player.</p> <p>Irrespective of this parameter, if any ID3Timed metadata is in the HLS input, it is passed through to the HLS output.</p>"}, "ScteHls": {"shape": "ScteHls"}, "FilterConfiguration": {"shape": "FilterConfiguration"}, "StartTag": {"shape": "StartTag"}, "UrlEncodeChildManifest": {"shape": "Boolean", "documentation": "<p>When enabled, MediaPackage URL-encodes the query string for API requests for LL-HLS child manifests to comply with Amazon Web Services Signature Version 4 (SigV4) signature signing protocol. For more information, see <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/reference_sigv.html\">Amazon Web Services Signature Version 4 for API requests</a> in <i>Identity and Access Management User Guide</i>.</p>"}}, "documentation": "<p>Retrieve the low-latency HTTP live streaming (HLS) manifest configuration.</p>"}, "GetLowLatencyHlsManifests": {"type": "list", "member": {"shape": "GetLowLatencyHlsManifestConfiguration"}}, "GetMssManifestConfiguration": {"type": "structure", "required": ["ManifestName", "Url"], "members": {"ManifestName": {"shape": "ManifestName", "documentation": "<p>The name of the MSS manifest. This name is appended to the origin endpoint URL to create the unique path for accessing this specific MSS manifest.</p>"}, "Url": {"shape": "String", "documentation": "<p>The complete URL for accessing the MSS manifest. Client players use this URL to retrieve the manifest and begin streaming the Microsoft Smooth Streaming content.</p>"}, "FilterConfiguration": {"shape": "FilterConfiguration"}, "ManifestWindowSeconds": {"shape": "Integer", "documentation": "<p>The duration (in seconds) of the manifest window. This represents the total amount of content available in the manifest at any given time.</p>"}, "ManifestLayout": {"shape": "MssManifestLayout", "documentation": "<p>The layout format of the MSS manifest, which determines how the manifest is structured for client compatibility.</p>"}}, "documentation": "<p>Configuration details for a Microsoft Smooth Streaming (MSS) manifest associated with an origin endpoint. This includes all the settings and properties that define how the MSS content is packaged and delivered.</p>"}, "GetMssManifests": {"type": "list", "member": {"shape": "GetMssManifestConfiguration"}}, "GetOriginEndpointPolicyRequest": {"type": "structure", "required": ["ChannelGroupName", "ChannelName", "OriginEndpointName"], "members": {"ChannelGroupName": {"shape": "ResourceName", "documentation": "<p>The name that describes the channel group. The name is the primary identifier for the channel group, and must be unique for your account in the AWS Region.</p>", "location": "uri", "locationName": "ChannelGroupName"}, "ChannelName": {"shape": "ResourceName", "documentation": "<p>The name that describes the channel. The name is the primary identifier for the channel, and must be unique for your account in the AWS Region and channel group. </p>", "location": "uri", "locationName": "ChannelName"}, "OriginEndpointName": {"shape": "ResourceName", "documentation": "<p>The name that describes the origin endpoint. The name is the primary identifier for the origin endpoint, and and must be unique for your account in the AWS Region and channel. </p>", "location": "uri", "locationName": "OriginEndpointName"}}}, "GetOriginEndpointPolicyResponse": {"type": "structure", "required": ["ChannelGroupName", "ChannelName", "OriginEndpointName", "Policy"], "members": {"ChannelGroupName": {"shape": "ResourceName", "documentation": "<p>The name that describes the channel group. The name is the primary identifier for the channel group, and must be unique for your account in the AWS Region.</p>"}, "ChannelName": {"shape": "ResourceName", "documentation": "<p>The name that describes the channel. The name is the primary identifier for the channel, and must be unique for your account in the AWS Region and channel group.</p>"}, "OriginEndpointName": {"shape": "ResourceName", "documentation": "<p>The name that describes the origin endpoint. The name is the primary identifier for the origin endpoint, and and must be unique for your account in the AWS Region and channel.</p>"}, "Policy": {"shape": "PolicyText", "documentation": "<p>The policy assigned to the origin endpoint.</p>"}}}, "GetOriginEndpointRequest": {"type": "structure", "required": ["ChannelGroupName", "ChannelName", "OriginEndpointName"], "members": {"ChannelGroupName": {"shape": "ResourceName", "documentation": "<p>The name that describes the channel group. The name is the primary identifier for the channel group, and must be unique for your account in the AWS Region.</p>", "location": "uri", "locationName": "ChannelGroupName"}, "ChannelName": {"shape": "ResourceName", "documentation": "<p>The name that describes the channel. The name is the primary identifier for the channel, and must be unique for your account in the AWS Region and channel group. </p>", "location": "uri", "locationName": "ChannelName"}, "OriginEndpointName": {"shape": "ResourceName", "documentation": "<p>The name that describes the origin endpoint. The name is the primary identifier for the origin endpoint, and and must be unique for your account in the AWS Region and channel. </p>", "location": "uri", "locationName": "OriginEndpointName"}}}, "GetOriginEndpointResponse": {"type": "structure", "required": ["<PERSON><PERSON>", "ChannelGroupName", "ChannelName", "OriginEndpointName", "ContainerType", "Segment", "CreatedAt", "ModifiedAt"], "members": {"Arn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) associated with the resource.</p>"}, "ChannelGroupName": {"shape": "ResourceName", "documentation": "<p>The name that describes the channel group. The name is the primary identifier for the channel group, and must be unique for your account in the AWS Region.</p>"}, "ChannelName": {"shape": "ResourceName", "documentation": "<p>The name that describes the channel. The name is the primary identifier for the channel, and must be unique for your account in the AWS Region and channel group.</p>"}, "OriginEndpointName": {"shape": "ResourceName", "documentation": "<p>The name that describes the origin endpoint. The name is the primary identifier for the origin endpoint, and and must be unique for your account in the AWS Region and channel.</p>"}, "ContainerType": {"shape": "ContainerType", "documentation": "<p>The type of container attached to this origin endpoint.</p>"}, "Segment": {"shape": "Segment"}, "CreatedAt": {"shape": "Timestamp", "documentation": "<p>The date and time the origin endpoint was created.</p>"}, "ModifiedAt": {"shape": "Timestamp", "documentation": "<p>The date and time the origin endpoint was modified.</p>"}, "ResetAt": {"shape": "Timestamp", "documentation": "<p>The time that the origin endpoint was last reset.</p>"}, "Description": {"shape": "ResourceDescription", "documentation": "<p>The description for your origin endpoint.</p>"}, "StartoverWindowSeconds": {"shape": "Integer", "documentation": "<p>The size of the window (in seconds) to create a window of the live stream that's available for on-demand viewing. Viewers can start-over or catch-up on content that falls within the window.</p>"}, "HlsManifests": {"shape": "GetHlsManifests", "documentation": "<p>An HTTP live streaming (HLS) manifest configuration.</p>"}, "LowLatencyHlsManifests": {"shape": "GetLowLatencyHlsManifests", "documentation": "<p>A low-latency HLS manifest configuration.</p>"}, "DashManifests": {"shape": "GetDashManifests", "documentation": "<p>A DASH manifest configuration.</p>"}, "MssManifests": {"shape": "GetMssManifests", "documentation": "<p>The Microsoft Smooth Streaming (MSS) manifest configurations associated with this origin endpoint.</p>"}, "ForceEndpointErrorConfiguration": {"shape": "ForceEndpointErrorConfiguration", "documentation": "<p>The failover settings for the endpoint.</p>"}, "ETag": {"shape": "EntityTag", "documentation": "<p>The current Entity Tag (ETag) associated with this resource. The entity tag can be used to safely make concurrent updates to the resource.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>The comma-separated list of tag key:value pairs assigned to the origin endpoint.</p>"}}}, "HarvestJob": {"type": "structure", "required": ["ChannelGroupName", "ChannelName", "OriginEndpointName", "Destination", "HarvestJobName", "HarvestedManifests", "ScheduleConfiguration", "<PERSON><PERSON>", "CreatedAt", "ModifiedAt", "Status"], "members": {"ChannelGroupName": {"shape": "ResourceName", "documentation": "<p>The name of the channel group containing the channel associated with this harvest job.</p>"}, "ChannelName": {"shape": "ResourceName", "documentation": "<p>The name of the channel associated with this harvest job.</p>"}, "OriginEndpointName": {"shape": "ResourceName", "documentation": "<p>The name of the origin endpoint associated with this harvest job.</p>"}, "Destination": {"shape": "Destination", "documentation": "<p>The S3 destination where the harvested content will be placed.</p>"}, "HarvestJobName": {"shape": "ResourceName", "documentation": "<p>The name of the harvest job.</p>"}, "HarvestedManifests": {"shape": "HarvestedManifests", "documentation": "<p>A list of manifests that are being or have been harvested.</p>"}, "Description": {"shape": "ResourceDescription", "documentation": "<p>An optional description of the harvest job.</p>"}, "ScheduleConfiguration": {"shape": "HarvesterScheduleConfiguration", "documentation": "<p>The configuration for when the harvest job is scheduled to run.</p>"}, "Arn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the harvest job.</p>"}, "CreatedAt": {"shape": "Timestamp", "documentation": "<p>The date and time when the harvest job was created.</p>"}, "ModifiedAt": {"shape": "Timestamp", "documentation": "<p>The date and time when the harvest job was last modified.</p>"}, "Status": {"shape": "HarvestJobStatus", "documentation": "<p>The current status of the harvest job (e.g., QUEUED, IN_PROGRESS, CANCELLED, COMPLETED, FAILED).</p>"}, "ErrorMessage": {"shape": "String", "documentation": "<p>An error message if the harvest job encountered any issues.</p>"}, "ETag": {"shape": "EntityTag", "documentation": "<p>The current version of the harvest job. Used for concurrency control.</p>"}}, "documentation": "<p>Represents a harvest job resource in MediaPackage v2, which is used to export content from an origin endpoint to an S3 bucket.</p>"}, "HarvestJobStatus": {"type": "string", "enum": ["QUEUED", "IN_PROGRESS", "CANCELLED", "COMPLETED", "FAILED"]}, "HarvestJobsList": {"type": "list", "member": {"shape": "HarvestJob"}}, "HarvestedDashManifest": {"type": "structure", "required": ["ManifestName"], "members": {"ManifestName": {"shape": "ResourceName", "documentation": "<p>The name of the harvested DASH manifest.</p>"}}, "documentation": "<p>Information about a harvested DASH manifest.</p>"}, "HarvestedDashManifestsList": {"type": "list", "member": {"shape": "HarvestedDashManifest"}}, "HarvestedHlsManifest": {"type": "structure", "required": ["ManifestName"], "members": {"ManifestName": {"shape": "ResourceName", "documentation": "<p>The name of the harvested HLS manifest.</p>"}}, "documentation": "<p>Information about a harvested HLS manifest.</p>"}, "HarvestedHlsManifestsList": {"type": "list", "member": {"shape": "HarvestedHlsManifest"}}, "HarvestedLowLatencyHlsManifest": {"type": "structure", "required": ["ManifestName"], "members": {"ManifestName": {"shape": "ResourceName", "documentation": "<p>The name of the harvested Low-Latency HLS manifest.</p>"}}, "documentation": "<p>Information about a harvested Low-Latency HLS manifest.</p>"}, "HarvestedLowLatencyHlsManifestsList": {"type": "list", "member": {"shape": "HarvestedLowLatencyHlsManifest"}}, "HarvestedManifests": {"type": "structure", "members": {"HlsManifests": {"shape": "HarvestedHlsManifestsList", "documentation": "<p>A list of harvested HLS manifests.</p>"}, "DashManifests": {"shape": "HarvestedDashManifestsList", "documentation": "<p>A list of harvested DASH manifests.</p>"}, "LowLatencyHlsManifests": {"shape": "HarvestedLowLatencyHlsManifestsList", "documentation": "<p>A list of harvested Low-Latency HLS manifests.</p>"}}, "documentation": "<p>A collection of harvested manifests of different types.</p>"}, "HarvesterScheduleConfiguration": {"type": "structure", "required": ["StartTime", "EndTime"], "members": {"StartTime": {"shape": "Timestamp", "documentation": "<p>The start time for the harvest job.</p>"}, "EndTime": {"shape": "Timestamp", "documentation": "<p>The end time for the harvest job.</p>"}}, "documentation": "<p>Defines the schedule configuration for a harvest job.</p>"}, "IdempotencyToken": {"type": "string", "max": 256, "min": 1, "pattern": "[\\S]+"}, "IngestEndpoint": {"type": "structure", "members": {"Id": {"shape": "String", "documentation": "<p>The system-generated unique identifier for the IngestEndpoint.</p>"}, "Url": {"shape": "String", "documentation": "<p>The ingest domain URL where the source stream should be sent.</p>"}}, "documentation": "<p>The ingest domain URL where the source stream should be sent.</p>"}, "IngestEndpointList": {"type": "list", "member": {"shape": "IngestEndpoint"}, "documentation": "<p>The list of ingest endpoints.</p>"}, "InputSwitchConfiguration": {"type": "structure", "members": {"MQCSInputSwitching": {"shape": "Boolean", "documentation": "<p>When true, AWS Elemental MediaPackage performs input switching based on the MQCS. Default is true. This setting is valid only when <code>InputType</code> is <code>CMAF</code>.</p>"}}, "documentation": "<p>The configuration for input switching based on the media quality confidence score (MQCS) as provided from AWS Elemental MediaLive.</p>"}, "InputType": {"type": "string", "enum": ["HLS", "CMAF"]}, "Integer": {"type": "integer", "box": true}, "InternalServerException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>Indicates that an error from the service occurred while trying to process a request.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true}, "IsmEncryptionMethod": {"type": "string", "enum": ["CENC"]}, "ListChannelGroupsRequest": {"type": "structure", "members": {"MaxResults": {"shape": "ListResourceMaxResults", "documentation": "<p>The maximum number of results to return in the response.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination token from the GET list request. Use the token to fetch the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListChannelGroupsResponse": {"type": "structure", "members": {"Items": {"shape": "ChannelGroupsList", "documentation": "<p>The objects being returned.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination token from the GET list request. Use the token to fetch the next page of results.</p>"}}}, "ListChannelsRequest": {"type": "structure", "required": ["ChannelGroupName"], "members": {"ChannelGroupName": {"shape": "ResourceName", "documentation": "<p>The name that describes the channel group. The name is the primary identifier for the channel group, and must be unique for your account in the AWS Region.</p>", "location": "uri", "locationName": "ChannelGroupName"}, "MaxResults": {"shape": "ListResourceMaxResults", "documentation": "<p>The maximum number of results to return in the response.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination token from the GET list request. Use the token to fetch the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListChannelsResponse": {"type": "structure", "members": {"Items": {"shape": "ChannelList", "documentation": "<p>The objects being returned.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination token from the GET list request.</p>"}}}, "ListDashManifestConfiguration": {"type": "structure", "required": ["ManifestName"], "members": {"ManifestName": {"shape": "ResourceName", "documentation": "<p>A short string that's appended to the endpoint URL. The manifest name creates a unique path to this endpoint. If you don't enter a value, MediaPackage uses the default manifest name, index. </p>"}, "Url": {"shape": "String", "documentation": "<p>The egress domain URL for stream delivery from MediaPackage.</p>"}}, "documentation": "<p>List the DASH manifest configuration.</p>"}, "ListDashManifests": {"type": "list", "member": {"shape": "ListDashManifestConfiguration"}}, "ListHarvestJobsRequest": {"type": "structure", "required": ["ChannelGroupName"], "members": {"ChannelGroupName": {"shape": "ResourceName", "documentation": "<p>The name of the channel group to filter the harvest jobs by. If specified, only harvest jobs associated with channels in this group will be returned.</p>", "location": "uri", "locationName": "ChannelGroupName"}, "ChannelName": {"shape": "ListHarvestJobsRequestChannelNameString", "documentation": "<p>The name of the channel to filter the harvest jobs by. If specified, only harvest jobs associated with this channel will be returned.</p>", "location": "querystring", "locationName": "channelName"}, "OriginEndpointName": {"shape": "ListHarvestJobsRequestOriginEndpointNameString", "documentation": "<p>The name of the origin endpoint to filter the harvest jobs by. If specified, only harvest jobs associated with this origin endpoint will be returned.</p>", "location": "querystring", "locationName": "originEndpointName"}, "Status": {"shape": "HarvestJobStatus", "documentation": "<p>The status to filter the harvest jobs by. If specified, only harvest jobs with this status will be returned.</p>", "location": "querystring", "locationName": "includeStatus"}, "MaxResults": {"shape": "ListHarvestJobsRequestMaxResultsInteger", "documentation": "<p>The maximum number of harvest jobs to return in a single request. If not specified, a default value will be used.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "String", "documentation": "<p>A token used for pagination. Provide this value in subsequent requests to retrieve the next set of results.</p>", "location": "querystring", "locationName": "nextToken"}}, "documentation": "<p>The request object for listing harvest jobs.</p>"}, "ListHarvestJobsRequestChannelNameString": {"type": "string", "max": 256, "min": 1, "pattern": "[a-zA-Z0-9_-]+"}, "ListHarvestJobsRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListHarvestJobsRequestOriginEndpointNameString": {"type": "string", "max": 256, "min": 1, "pattern": "[a-zA-Z0-9_-]+"}, "ListHarvestJobsResponse": {"type": "structure", "members": {"Items": {"shape": "HarvestJobsList", "documentation": "<p>An array of harvest job objects that match the specified criteria.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>A token used for pagination. Include this value in subsequent requests to retrieve the next set of results. If null, there are no more results to retrieve.</p>"}}, "documentation": "<p>The response object containing the list of harvest jobs that match the specified criteria.</p>"}, "ListHlsManifestConfiguration": {"type": "structure", "required": ["ManifestName"], "members": {"ManifestName": {"shape": "ResourceName", "documentation": "<p>A short short string that's appended to the endpoint URL. The manifest name creates a unique path to this endpoint. If you don't enter a value, MediaPackage uses the default manifest name, index. MediaPackage automatically inserts the format extension, such as .m3u8. You can't use the same manifest name if you use HLS manifest and low-latency HLS manifest. The manifestName on the HLSManifest object overrides the manifestName you provided on the originEndpoint object.</p>"}, "ChildManifestName": {"shape": "ResourceName", "documentation": "<p>A short string that's appended to the endpoint URL. The child manifest name creates a unique path to this endpoint. If you don't enter a value, MediaPackage uses the default child manifest name, index_1. The manifestName on the HLSManifest object overrides the manifestName you provided on the originEndpoint object.</p>"}, "Url": {"shape": "String", "documentation": "<p>The egress domain URL for stream delivery from MediaPackage.</p>"}}, "documentation": "<p>List the HTTP live streaming (HLS) manifest configuration.</p>"}, "ListHlsManifests": {"type": "list", "member": {"shape": "ListHlsManifestConfiguration"}}, "ListLowLatencyHlsManifestConfiguration": {"type": "structure", "required": ["ManifestName"], "members": {"ManifestName": {"shape": "ResourceName", "documentation": "<p>A short short string that's appended to the endpoint URL. The manifest name creates a unique path to this endpoint. If you don't enter a value, MediaPackage uses the default manifest name, index. MediaPackage automatically inserts the format extension, such as .m3u8. You can't use the same manifest name if you use HLS manifest and low-latency HLS manifest. The manifestName on the HLSManifest object overrides the manifestName you provided on the originEndpoint object.</p>"}, "ChildManifestName": {"shape": "ResourceName", "documentation": "<p>A short string that's appended to the endpoint URL. The child manifest name creates a unique path to this endpoint. If you don't enter a value, MediaPackage uses the default child manifest name, index_1. The manifestName on the HLSManifest object overrides the manifestName you provided on the originEndpoint object.</p>"}, "Url": {"shape": "String", "documentation": "<p>The egress domain URL for stream delivery from MediaPackage.</p>"}}, "documentation": "<p>List the low-latency HTTP live streaming (HLS) manifest configuration.</p>"}, "ListLowLatencyHlsManifests": {"type": "list", "member": {"shape": "ListLowLatencyHlsManifestConfiguration"}}, "ListMssManifestConfiguration": {"type": "structure", "required": ["ManifestName"], "members": {"ManifestName": {"shape": "ResourceName", "documentation": "<p>The name of the MSS manifest configuration.</p>"}, "Url": {"shape": "String", "documentation": "<p>The URL for accessing the MSS manifest.</p>"}}, "documentation": "<p>Summary information about a Microsoft Smooth Streaming (MSS) manifest configuration. This provides key details about the MSS manifest without including all configuration parameters.</p>"}, "ListMssManifests": {"type": "list", "member": {"shape": "ListMssManifestConfiguration"}}, "ListOriginEndpointsRequest": {"type": "structure", "required": ["ChannelGroupName", "ChannelName"], "members": {"ChannelGroupName": {"shape": "ResourceName", "documentation": "<p>The name that describes the channel group. The name is the primary identifier for the channel group, and must be unique for your account in the AWS Region.</p>", "location": "uri", "locationName": "ChannelGroupName"}, "ChannelName": {"shape": "ResourceName", "documentation": "<p>The name that describes the channel. The name is the primary identifier for the channel, and must be unique for your account in the AWS Region and channel group. </p>", "location": "uri", "locationName": "ChannelName"}, "MaxResults": {"shape": "ListResourceMaxResults", "documentation": "<p>The maximum number of results to return in the response.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination token from the GET list request. Use the token to fetch the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListOriginEndpointsResponse": {"type": "structure", "members": {"Items": {"shape": "OriginEndpointsList", "documentation": "<p>The objects being returned.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination token from the GET list request. Use the token to fetch the next page of results.</p>"}}}, "ListResourceMaxResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListTagsForResourceRequest": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "TagArn", "documentation": "<p>The ARN of the CloudWatch resource that you want to view tags for.</p>", "location": "uri", "locationName": "ResourceArn"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"Tags": {"shape": "TagMap", "documentation": "<p>Contains a map of the key-value pairs for the resource tag or tags assigned to the resource.</p>", "locationName": "tags"}}}, "ManifestName": {"type": "string", "max": 256, "min": 1, "pattern": "[a-zA-Z0-9-]+"}, "MssManifestLayout": {"type": "string", "enum": ["FULL", "COMPACT"]}, "OriginEndpointListConfiguration": {"type": "structure", "required": ["<PERSON><PERSON>", "ChannelGroupName", "ChannelName", "OriginEndpointName", "ContainerType"], "members": {"Arn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) associated with the resource.</p>"}, "ChannelGroupName": {"shape": "ResourceName", "documentation": "<p>The name that describes the channel group. The name is the primary identifier for the channel group, and must be unique for your account in the AWS Region.</p>"}, "ChannelName": {"shape": "ResourceName", "documentation": "<p>The name that describes the channel. The name is the primary identifier for the channel, and must be unique for your account in the AWS Region and channel group. </p>"}, "OriginEndpointName": {"shape": "ResourceName", "documentation": "<p>The name that describes the origin endpoint. The name is the primary identifier for the origin endpoint, and and must be unique for your account in the AWS Region and channel. </p>"}, "ContainerType": {"shape": "ContainerType", "documentation": "<p>The type of container attached to this origin endpoint. A container type is a file format that encapsulates one or more media streams, such as audio and video, into a single file. </p>"}, "Description": {"shape": "ResourceDescription", "documentation": "<p>Any descriptive information that you want to add to the origin endpoint for future identification purposes.</p>"}, "CreatedAt": {"shape": "Timestamp", "documentation": "<p>The date and time the origin endpoint was created.</p>"}, "ModifiedAt": {"shape": "Timestamp", "documentation": "<p>The date and time the origin endpoint was modified.</p>"}, "HlsManifests": {"shape": "ListHlsManifests", "documentation": "<p>An HTTP live streaming (HLS) manifest configuration.</p>"}, "LowLatencyHlsManifests": {"shape": "ListLowLatencyHlsManifests", "documentation": "<p>A low-latency HLS manifest configuration.</p>"}, "DashManifests": {"shape": "ListDashManifests", "documentation": "<p>A DASH manifest configuration.</p>"}, "MssManifests": {"shape": "ListMssManifests", "documentation": "<p>A list of Microsoft Smooth Streaming (MSS) manifest configurations associated with the origin endpoint. Each configuration represents a different MSS streaming option available from this endpoint.</p>"}, "ForceEndpointErrorConfiguration": {"shape": "ForceEndpointErrorConfiguration", "documentation": "<p>The failover settings for the endpoint.</p>"}}, "documentation": "<p>The configuration of the origin endpoint.</p>"}, "OriginEndpointsList": {"type": "list", "member": {"shape": "OriginEndpointListConfiguration"}}, "OutputHeaderConfiguration": {"type": "structure", "members": {"PublishMQCS": {"shape": "Boolean", "documentation": "<p>When true, AWS Elemental MediaPackage includes the MQCS in responses to the CDN. This setting is valid only when <code>InputType</code> is <code>CMAF</code>.</p>"}}, "documentation": "<p>The settings for what common media server data (CMSD) headers AWS Elemental MediaPackage includes in responses to the CDN.</p>"}, "PolicyText": {"type": "string", "max": 6144, "min": 0}, "PresetSpeke20Audio": {"type": "string", "enum": ["PRESET_AUDIO_1", "PRESET_AUDIO_2", "PRESET_AUDIO_3", "SHARED", "UNENCRYPTED"]}, "PresetSpeke20Video": {"type": "string", "enum": ["PRESET_VIDEO_1", "PRESET_VIDEO_2", "PRESET_VIDEO_3", "PRESET_VIDEO_4", "PRESET_VIDEO_5", "PRESET_VIDEO_6", "PRESET_VIDEO_7", "PRESET_VIDEO_8", "SHARED", "UNENCRYPTED"]}, "PutChannelPolicyRequest": {"type": "structure", "required": ["ChannelGroupName", "ChannelName", "Policy"], "members": {"ChannelGroupName": {"shape": "ResourceName", "documentation": "<p>The name that describes the channel group. The name is the primary identifier for the channel group, and must be unique for your account in the AWS Region.</p>", "location": "uri", "locationName": "ChannelGroupName"}, "ChannelName": {"shape": "ResourceName", "documentation": "<p>The name that describes the channel. The name is the primary identifier for the channel, and must be unique for your account in the AWS Region and channel group. </p>", "location": "uri", "locationName": "ChannelName"}, "Policy": {"shape": "PolicyText", "documentation": "<p>The policy to attach to the specified channel.</p>"}}}, "PutChannelPolicyResponse": {"type": "structure", "members": {}}, "PutOriginEndpointPolicyRequest": {"type": "structure", "required": ["ChannelGroupName", "ChannelName", "OriginEndpointName", "Policy"], "members": {"ChannelGroupName": {"shape": "ResourceName", "documentation": "<p>The name that describes the channel group. The name is the primary identifier for the channel group, and must be unique for your account in the AWS Region.</p>", "location": "uri", "locationName": "ChannelGroupName"}, "ChannelName": {"shape": "ResourceName", "documentation": "<p>The name that describes the channel. The name is the primary identifier for the channel, and must be unique for your account in the AWS Region and channel group. </p>", "location": "uri", "locationName": "ChannelName"}, "OriginEndpointName": {"shape": "ResourceName", "documentation": "<p>The name that describes the origin endpoint. The name is the primary identifier for the origin endpoint, and and must be unique for your account in the AWS Region and channel. </p>", "location": "uri", "locationName": "OriginEndpointName"}, "Policy": {"shape": "PolicyText", "documentation": "<p>The policy to attach to the specified origin endpoint.</p>"}}}, "PutOriginEndpointPolicyResponse": {"type": "structure", "members": {}}, "ResetChannelStateRequest": {"type": "structure", "required": ["ChannelGroupName", "ChannelName"], "members": {"ChannelGroupName": {"shape": "ResourceName", "documentation": "<p>The name of the channel group that contains the channel that you are resetting.</p>", "location": "uri", "locationName": "ChannelGroupName"}, "ChannelName": {"shape": "ResourceName", "documentation": "<p>The name of the channel that you are resetting.</p>", "location": "uri", "locationName": "ChannelName"}}}, "ResetChannelStateResponse": {"type": "structure", "required": ["ChannelGroupName", "ChannelName", "<PERSON><PERSON>", "ResetAt"], "members": {"ChannelGroupName": {"shape": "String", "documentation": "<p>The name of the channel group that contains the channel that you just reset.</p>"}, "ChannelName": {"shape": "String", "documentation": "<p>The name of the channel that you just reset.</p>"}, "Arn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) associated with the channel that you just reset.</p>"}, "ResetAt": {"shape": "Timestamp", "documentation": "<p>The time that the channel was last reset.</p>"}}}, "ResetOriginEndpointStateRequest": {"type": "structure", "required": ["ChannelGroupName", "ChannelName", "OriginEndpointName"], "members": {"ChannelGroupName": {"shape": "ResourceName", "documentation": "<p>The name of the channel group that contains the channel with the origin endpoint that you are resetting.</p>", "location": "uri", "locationName": "ChannelGroupName"}, "ChannelName": {"shape": "ResourceName", "documentation": "<p>The name of the channel with the origin endpoint that you are resetting.</p>", "location": "uri", "locationName": "ChannelName"}, "OriginEndpointName": {"shape": "ResourceName", "documentation": "<p>The name of the origin endpoint that you are resetting.</p>", "location": "uri", "locationName": "OriginEndpointName"}}}, "ResetOriginEndpointStateResponse": {"type": "structure", "required": ["ChannelGroupName", "ChannelName", "OriginEndpointName", "<PERSON><PERSON>", "ResetAt"], "members": {"ChannelGroupName": {"shape": "ResourceName", "documentation": "<p>The name of the channel group that contains the channel with the origin endpoint that you just reset.</p>"}, "ChannelName": {"shape": "ResourceName", "documentation": "<p>The name of the channel with the origin endpoint that you just reset.</p>"}, "OriginEndpointName": {"shape": "ResourceName", "documentation": "<p>The name of the origin endpoint that you just reset.</p>"}, "Arn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) associated with the endpoint that you just reset.</p>"}, "ResetAt": {"shape": "Timestamp", "documentation": "<p>The time that the origin endpoint was last reset.</p>"}}}, "ResourceDescription": {"type": "string", "max": 1024, "min": 0}, "ResourceName": {"type": "string", "max": 256, "min": 1, "pattern": "[a-zA-Z0-9_-]+"}, "ResourceNotFoundException": {"type": "structure", "members": {"Message": {"shape": "String"}, "ResourceTypeNotFound": {"shape": "ResourceTypeNotFound", "documentation": "<p>The specified resource type wasn't found.</p>"}}, "documentation": "<p>The specified resource doesn't exist.</p>", "error": {"httpStatusCode": 404, "senderFault": true}, "exception": true}, "ResourceTypeNotFound": {"type": "string", "enum": ["CHANNEL_GROUP", "CHANNEL", "ORIGIN_ENDPOINT", "HARVEST_JOB"]}, "S3BucketName": {"type": "string", "max": 63, "min": 3}, "S3DestinationConfig": {"type": "structure", "required": ["BucketName", "DestinationPath"], "members": {"BucketName": {"shape": "S3BucketName", "documentation": "<p>The name of an S3 bucket within which harvested content will be exported.</p>"}, "DestinationPath": {"shape": "S3DestinationPath", "documentation": "<p>The path within the specified S3 bucket where the harvested content will be placed.</p>"}}, "documentation": "<p>Configuration parameters for where in an S3 bucket to place the harvested content.</p>"}, "S3DestinationPath": {"type": "string", "max": 1024, "min": 1, "pattern": "[\\S]+"}, "Scte": {"type": "structure", "members": {"ScteFilter": {"shape": "ScteFilterList", "documentation": "<p>The SCTE-35 message types that you want to be treated as ad markers in the output.</p>"}}, "documentation": "<p>The SCTE configuration.</p>"}, "ScteDash": {"type": "structure", "members": {"AdMarkerDash": {"shape": "AdMarkerDash", "documentation": "<p>Choose how ad markers are included in the packaged content. If you include ad markers in the content stream in your upstream encoders, then you need to inform MediaPackage what to do with the ad markers in the output.</p> <p>Value description:</p> <ul> <li> <p> <code>Binary</code> - The SCTE-35 marker is expressed as a hex-string (Base64 string) rather than full XML.</p> </li> <li> <p> <code>XML</code> - The SCTE marker is expressed fully in XML.</p> </li> </ul>"}}, "documentation": "<p>The SCTE configuration.</p>"}, "ScteFilter": {"type": "string", "enum": ["SPLICE_INSERT", "BREAK", "PROVIDER_ADVERTISEMENT", "DISTRIBUTOR_ADVERTISEMENT", "PROVIDER_PLACEMENT_OPPORTUNITY", "DISTRIBUTOR_PLACEMENT_OPPORTUNITY", "PROVIDER_OVERLAY_PLACEMENT_OPPORTUNITY", "DISTRIBUTOR_OVERLAY_PLACEMENT_OPPORTUNITY", "PROGRAM"]}, "ScteFilterList": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "max": 100, "min": 0}, "ScteHls": {"type": "structure", "members": {"AdMarkerHls": {"shape": "AdMarkerHls", "documentation": "<p>Ad markers indicate when ads should be inserted during playback. If you include ad markers in the content stream in your upstream encoders, then you need to inform MediaPackage what to do with the ad markers in the output. Choose what you want MediaPackage to do with the ad markers.</p> <p>Value description: </p> <ul> <li> <p>DATERANGE - Insert EXT-X-DATERANGE tags to signal ad and program transition events in TS and CMAF manifests. If you use DATERANGE, you must set a programDateTimeIntervalSeconds value of 1 or higher. To learn more about DATERANGE, see <a href=\"http://docs.aws.amazon.com/mediapackage/latest/ug/scte-35-ad-marker-ext-x-daterange.html\">SCTE-35 Ad Marker EXT-X-DATERANGE</a>.</p> </li> </ul>"}}, "documentation": "<p>The SCTE configuration.</p>"}, "Segment": {"type": "structure", "members": {"SegmentDurationSeconds": {"shape": "SegmentSegmentDurationSecondsInteger", "documentation": "<p>The duration (in seconds) of each segment. Enter a value equal to, or a multiple of, the input segment duration. If the value that you enter is different from the input segment duration, MediaPackage rounds segments to the nearest multiple of the input segment duration.</p>"}, "SegmentName": {"shape": "SegmentSegmentNameString", "documentation": "<p>The name that describes the segment. The name is the base name of the segment used in all content manifests inside of the endpoint. You can't use spaces in the name.</p>"}, "TsUseAudioRenditionGroup": {"shape": "Boolean", "documentation": "<p>When selected, MediaPackage bundles all audio tracks in a rendition group. All other tracks in the stream can be used with any audio rendition from the group.</p>"}, "IncludeIframeOnlyStreams": {"shape": "Boolean", "documentation": "<p>When selected, the stream set includes an additional I-frame only stream, along with the other tracks. If false, this extra stream is not included. MediaPackage generates an I-frame only stream from the first rendition in the manifest. The service inserts EXT-I-FRAMES-ONLY tags in the output manifest, and then generates and includes an I-frames only playlist in the stream. This playlist permits player functionality like fast forward and rewind.</p>"}, "TsIncludeDvbSubtitles": {"shape": "Boolean", "documentation": "<p>By default, MediaPackage excludes all digital video broadcasting (DVB) subtitles from the output. When selected, MediaPackage passes through DVB subtitles into the output.</p>"}, "Scte": {"shape": "Scte", "documentation": "<p>The SCTE configuration options in the segment settings.</p>"}, "Encryption": {"shape": "Encryption"}}, "documentation": "<p>The segment configuration, including the segment name, duration, and other configuration values.</p>"}, "SegmentSegmentDurationSecondsInteger": {"type": "integer", "box": true, "max": 30, "min": 1}, "SegmentSegmentNameString": {"type": "string", "max": 256, "min": 1, "pattern": "[a-zA-Z0-9_-]+"}, "ServiceQuotaExceededException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>The request would cause a service quota to be exceeded.</p>", "error": {"httpStatusCode": 402, "senderFault": true}, "exception": true}, "SpekeKeyProvider": {"type": "structure", "required": ["EncryptionContractConfiguration", "ResourceId", "DrmSystems", "RoleArn", "Url"], "members": {"EncryptionContractConfiguration": {"shape": "EncryptionContractConfiguration", "documentation": "<p>Configure one or more content encryption keys for your endpoints that use SPEKE Version 2.0. The encryption contract defines which content keys are used to encrypt the audio and video tracks in your stream. To configure the encryption contract, specify which audio and video encryption presets to use.</p>"}, "ResourceId": {"shape": "SpekeKeyProviderResourceIdString", "documentation": "<p>The unique identifier for the content. The service sends this to the key server to identify the current endpoint. How unique you make this depends on how fine-grained you want access controls to be. The service does not permit you to use the same ID for two simultaneous encryption processes. The resource ID is also known as the content ID.</p> <p>The following example shows a resource ID: <code>MovieNight20171126093045</code> </p>"}, "DrmSystems": {"shape": "SpekeKeyProviderDrmSystemsList", "documentation": "<p>The DRM solution provider you're using to protect your content during distribution.</p>"}, "RoleArn": {"shape": "SpekeKeyProviderRoleArnString", "documentation": "<p>The ARN for the IAM role granted by the key provider that provides access to the key provider API. This role must have a trust policy that allows MediaPackage to assume the role, and it must have a sufficient permissions policy to allow access to the specific key retrieval URL. Get this from your DRM solution provider.</p> <p>Valid format: <code>arn:aws:iam::{accountID}:role/{name}</code>. The following example shows a role ARN: <code>arn:aws:iam::************:role/SpekeAccess</code> </p>"}, "Url": {"shape": "SpekeKeyProviderUrlString", "documentation": "<p>The URL of the API Gateway proxy that you set up to talk to your key server. The API Gateway proxy must reside in the same AWS Region as MediaPackage and must start with https://.</p> <p>The following example shows a URL: <code>https://1wm2dx1f33.execute-api.us-west-2.amazonaws.com/SpekeSample/copyProtection</code> </p>"}}, "documentation": "<p>The parameters for the SPEKE key provider.</p>"}, "SpekeKeyProviderDrmSystemsList": {"type": "list", "member": {"shape": "DrmSystem"}, "max": 4, "min": 1}, "SpekeKeyProviderResourceIdString": {"type": "string", "max": 256, "min": 1, "pattern": "[0-9a-zA-Z_-]+"}, "SpekeKeyProviderRoleArnString": {"type": "string", "max": 2048, "min": 1}, "SpekeKeyProviderUrlString": {"type": "string", "max": 1024, "min": 1}, "StartTag": {"type": "structure", "required": ["TimeOffset"], "members": {"TimeOffset": {"shape": "Float", "documentation": "<p>Specify the value for TIME-OFFSET within your EXT-X-START tag. Enter a signed floating point value which, if positive, must be less than the configured manifest duration minus three times the configured segment target duration. If negative, the absolute value must be larger than three times the configured segment target duration, and the absolute value must be smaller than the configured manifest duration.</p>"}, "Precise": {"shape": "Boolean", "documentation": "<p>Specify the value for PRECISE within your EXT-X-START tag. Leave blank, or choose false, to use the default value NO. Choose yes to use the value YES.</p>"}}, "documentation": "<p>To insert an EXT-X-START tag in your HLS playlist, specify a StartTag configuration object with a valid TimeOffset. When you do, you can also optionally specify whether to include a PRECISE value in the EXT-X-START tag.</p>"}, "String": {"type": "string"}, "TagArn": {"type": "string"}, "TagKey": {"type": "string"}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}}, "TagMap": {"type": "map", "key": {"shape": "TagKey"}, "value": {"shape": "TagValue"}}, "TagResourceRequest": {"type": "structure", "required": ["ResourceArn", "Tags"], "members": {"ResourceArn": {"shape": "TagArn", "documentation": "<p>The ARN of the MediaPackage resource that you're adding tags to.</p>", "location": "uri", "locationName": "ResourceArn"}, "Tags": {"shape": "TagMap", "documentation": "<p>Contains a map of the key-value pairs for the resource tag or tags assigned to the resource.</p>", "locationName": "tags"}}}, "TagValue": {"type": "string"}, "ThrottlingException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>The request throughput limit was exceeded.</p>", "error": {"httpStatusCode": 429, "senderFault": true}, "exception": true}, "Timestamp": {"type": "timestamp"}, "TsEncryptionMethod": {"type": "string", "enum": ["AES_128", "SAMPLE_AES"]}, "UntagResourceRequest": {"type": "structure", "required": ["ResourceArn", "TagKeys"], "members": {"ResourceArn": {"shape": "TagArn", "documentation": "<p>The ARN of the MediaPackage resource that you're removing tags from.</p>", "location": "uri", "locationName": "ResourceArn"}, "TagKeys": {"shape": "TagKeyList", "documentation": "<p>The list of tag keys to remove from the resource.</p>", "location": "querystring", "locationName": "tagKeys"}}}, "UpdateChannelGroupRequest": {"type": "structure", "required": ["ChannelGroupName"], "members": {"ChannelGroupName": {"shape": "ResourceName", "documentation": "<p>The name that describes the channel group. The name is the primary identifier for the channel group, and must be unique for your account in the AWS Region.</p>", "location": "uri", "locationName": "ChannelGroupName"}, "ETag": {"shape": "EntityTag", "documentation": "<p>The expected current Entity Tag (ETag) for the resource. If the specified ETag does not match the resource's current entity tag, the update request will be rejected.</p>", "location": "header", "locationName": "x-amzn-update-if-match"}, "Description": {"shape": "ResourceDescription", "documentation": "<p>Any descriptive information that you want to add to the channel group for future identification purposes.</p>"}}}, "UpdateChannelGroupResponse": {"type": "structure", "required": ["ChannelGroupName", "<PERSON><PERSON>", "EgressDomain", "CreatedAt", "ModifiedAt"], "members": {"ChannelGroupName": {"shape": "String", "documentation": "<p>The name that describes the channel group. The name is the primary identifier for the channel group, and must be unique for your account in the AWS Region.</p>"}, "Arn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) associated with the resource.</p>"}, "EgressDomain": {"shape": "String", "documentation": "<p>The output domain where the source stream is sent. Integrate the domain with a downstream CDN (such as Amazon CloudFront) or playback device.</p>"}, "CreatedAt": {"shape": "Timestamp", "documentation": "<p>The date and time the channel group was created.</p>"}, "ModifiedAt": {"shape": "Timestamp", "documentation": "<p>The date and time the channel group was modified.</p>"}, "Description": {"shape": "ResourceDescription", "documentation": "<p>The description for your channel group.</p>"}, "ETag": {"shape": "EntityTag", "documentation": "<p>The current Entity Tag (ETag) associated with this resource. The entity tag can be used to safely make concurrent updates to the resource.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>The comma-separated list of tag key:value pairs assigned to the channel group.</p>", "locationName": "tags"}}}, "UpdateChannelRequest": {"type": "structure", "required": ["ChannelGroupName", "ChannelName"], "members": {"ChannelGroupName": {"shape": "ResourceName", "documentation": "<p>The name that describes the channel group. The name is the primary identifier for the channel group, and must be unique for your account in the AWS Region.</p>", "location": "uri", "locationName": "ChannelGroupName"}, "ChannelName": {"shape": "ResourceName", "documentation": "<p>The name that describes the channel. The name is the primary identifier for the channel, and must be unique for your account in the AWS Region and channel group. </p>", "location": "uri", "locationName": "ChannelName"}, "ETag": {"shape": "EntityTag", "documentation": "<p>The expected current Entity Tag (ETag) for the resource. If the specified ETag does not match the resource's current entity tag, the update request will be rejected.</p>", "location": "header", "locationName": "x-amzn-update-if-match"}, "Description": {"shape": "ResourceDescription", "documentation": "<p>Any descriptive information that you want to add to the channel for future identification purposes.</p>"}, "InputSwitchConfiguration": {"shape": "InputSwitchConfiguration", "documentation": "<p>The configuration for input switching based on the media quality confidence score (MQCS) as provided from AWS Elemental MediaLive. This setting is valid only when <code>InputType</code> is <code>CMAF</code>.</p>"}, "OutputHeaderConfiguration": {"shape": "OutputHeaderConfiguration", "documentation": "<p>The settings for what common media server data (CMSD) headers AWS Elemental MediaPackage includes in responses to the CDN. This setting is valid only when <code>InputType</code> is <code>CMAF</code>.</p>"}}}, "UpdateChannelResponse": {"type": "structure", "required": ["<PERSON><PERSON>", "ChannelName", "ChannelGroupName", "CreatedAt", "ModifiedAt"], "members": {"Arn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) associated with the resource.</p>"}, "ChannelName": {"shape": "String", "documentation": "<p>The name that describes the channel. The name is the primary identifier for the channel, and must be unique for your account in the AWS Region and channel group.</p>"}, "ChannelGroupName": {"shape": "String", "documentation": "<p>The name that describes the channel group. The name is the primary identifier for the channel group, and must be unique for your account in the AWS Region.</p>"}, "CreatedAt": {"shape": "Timestamp", "documentation": "<p>The date and time the channel was created.</p>"}, "ModifiedAt": {"shape": "Timestamp", "documentation": "<p>The date and time the channel was modified.</p>"}, "Description": {"shape": "ResourceDescription", "documentation": "<p>The description for your channel.</p>"}, "IngestEndpoints": {"shape": "IngestEndpointList"}, "InputType": {"shape": "InputType", "documentation": "<p>The input type will be an immutable field which will be used to define whether the channel will allow CMAF ingest or HLS ingest. If unprovided, it will default to HLS to preserve current behavior.</p> <p>The allowed values are:</p> <ul> <li> <p> <code>HLS</code> - The HLS streaming specification (which defines M3U8 manifests and TS segments).</p> </li> <li> <p> <code>CMAF</code> - The DASH-IF CMAF Ingest specification (which defines CMAF segments with optional DASH manifests).</p> </li> </ul>"}, "ETag": {"shape": "EntityTag", "documentation": "<p>The current Entity Tag (ETag) associated with this resource. The entity tag can be used to safely make concurrent updates to the resource.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>The comma-separated list of tag key:value pairs assigned to the channel.</p>", "locationName": "tags"}, "InputSwitchConfiguration": {"shape": "InputSwitchConfiguration", "documentation": "<p>The configuration for input switching based on the media quality confidence score (MQCS) as provided from AWS Elemental MediaLive. This setting is valid only when <code>InputType</code> is <code>CMAF</code>.</p>"}, "OutputHeaderConfiguration": {"shape": "OutputHeaderConfiguration", "documentation": "<p>The settings for what common media server data (CMSD) headers AWS Elemental MediaPackage includes in responses to the CDN. This setting is valid only when <code>InputType</code> is <code>CMAF</code>.</p>"}}}, "UpdateOriginEndpointRequest": {"type": "structure", "required": ["ChannelGroupName", "ChannelName", "OriginEndpointName", "ContainerType"], "members": {"ChannelGroupName": {"shape": "ResourceName", "documentation": "<p>The name that describes the channel group. The name is the primary identifier for the channel group, and must be unique for your account in the AWS Region.</p>", "location": "uri", "locationName": "ChannelGroupName"}, "ChannelName": {"shape": "ResourceName", "documentation": "<p>The name that describes the channel. The name is the primary identifier for the channel, and must be unique for your account in the AWS Region and channel group. </p>", "location": "uri", "locationName": "ChannelName"}, "OriginEndpointName": {"shape": "ResourceName", "documentation": "<p>The name that describes the origin endpoint. The name is the primary identifier for the origin endpoint, and and must be unique for your account in the AWS Region and channel. </p>", "location": "uri", "locationName": "OriginEndpointName"}, "ContainerType": {"shape": "ContainerType", "documentation": "<p>The type of container attached to this origin endpoint. A container type is a file format that encapsulates one or more media streams, such as audio and video, into a single file. </p>"}, "Segment": {"shape": "Segment", "documentation": "<p>The segment configuration, including the segment name, duration, and other configuration values.</p>"}, "Description": {"shape": "ResourceDescription", "documentation": "<p>Any descriptive information that you want to add to the origin endpoint for future identification purposes.</p>"}, "StartoverWindowSeconds": {"shape": "UpdateOriginEndpointRequestStartoverWindowSecondsInteger", "documentation": "<p>The size of the window (in seconds) to create a window of the live stream that's available for on-demand viewing. Viewers can start-over or catch-up on content that falls within the window. The maximum startover window is 1,209,600 seconds (14 days).</p>"}, "HlsManifests": {"shape": "CreateHlsManifests", "documentation": "<p>An HTTP live streaming (HLS) manifest configuration.</p>"}, "LowLatencyHlsManifests": {"shape": "CreateLowLatencyHlsManifests", "documentation": "<p>A low-latency HLS manifest configuration.</p>"}, "DashManifests": {"shape": "CreateDashManifests", "documentation": "<p>A DASH manifest configuration.</p>"}, "MssManifests": {"shape": "CreateMssManifests", "documentation": "<p>A list of Microsoft Smooth Streaming (MSS) manifest configurations to update for the origin endpoint. This replaces the existing MSS manifest configurations.</p>"}, "ForceEndpointErrorConfiguration": {"shape": "ForceEndpointErrorConfiguration", "documentation": "<p>The failover settings for the endpoint.</p>"}, "ETag": {"shape": "EntityTag", "documentation": "<p>The expected current Entity Tag (ETag) for the resource. If the specified ETag does not match the resource's current entity tag, the update request will be rejected.</p>", "location": "header", "locationName": "x-amzn-update-if-match"}}}, "UpdateOriginEndpointRequestStartoverWindowSecondsInteger": {"type": "integer", "box": true, "max": 1209600, "min": 60}, "UpdateOriginEndpointResponse": {"type": "structure", "required": ["<PERSON><PERSON>", "ChannelGroupName", "ChannelName", "OriginEndpointName", "ContainerType", "Segment", "CreatedAt", "ModifiedAt"], "members": {"Arn": {"shape": "String", "documentation": "<p>The ARN associated with the resource.</p>"}, "ChannelGroupName": {"shape": "ResourceName", "documentation": "<p>The name that describes the channel group. The name is the primary identifier for the channel group, and must be unique for your account in the AWS Region.</p>"}, "ChannelName": {"shape": "ResourceName", "documentation": "<p>The name that describes the channel. The name is the primary identifier for the channel, and must be unique for your account in the AWS Region and channel group.</p>"}, "OriginEndpointName": {"shape": "ResourceName", "documentation": "<p>The name that describes the origin endpoint. The name is the primary identifier for the origin endpoint, and and must be unique for your account in the AWS Region and channel.</p>"}, "ContainerType": {"shape": "ContainerType", "documentation": "<p>The type of container attached to this origin endpoint.</p>"}, "Segment": {"shape": "Segment", "documentation": "<p>The segment configuration, including the segment name, duration, and other configuration values.</p>"}, "CreatedAt": {"shape": "Timestamp", "documentation": "<p>The date and time the origin endpoint was created.</p>"}, "ModifiedAt": {"shape": "Timestamp", "documentation": "<p>The date and time the origin endpoint was modified.</p>"}, "Description": {"shape": "ResourceDescription", "documentation": "<p>The description of the origin endpoint.</p>"}, "StartoverWindowSeconds": {"shape": "Integer", "documentation": "<p>The size of the window (in seconds) to create a window of the live stream that's available for on-demand viewing. Viewers can start-over or catch-up on content that falls within the window.</p>"}, "HlsManifests": {"shape": "GetHlsManifests", "documentation": "<p>An HTTP live streaming (HLS) manifest configuration.</p>"}, "LowLatencyHlsManifests": {"shape": "GetLowLatencyHlsManifests", "documentation": "<p>A low-latency HLS manifest configuration.</p>"}, "MssManifests": {"shape": "GetMssManifests", "documentation": "<p>The updated Microsoft Smooth Streaming (MSS) manifest configurations for this origin endpoint.</p>"}, "ForceEndpointErrorConfiguration": {"shape": "ForceEndpointErrorConfiguration", "documentation": "<p>The failover settings for the endpoint.</p>"}, "ETag": {"shape": "EntityTag", "documentation": "<p>The current Entity Tag (ETag) associated with this resource. The entity tag can be used to safely make concurrent updates to the resource.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>The comma-separated list of tag key:value pairs assigned to the origin endpoint.</p>", "locationName": "tags"}, "DashManifests": {"shape": "GetDashManifests", "documentation": "<p>A DASH manifest configuration.</p>"}}}, "ValidationException": {"type": "structure", "members": {"Message": {"shape": "String"}, "ValidationExceptionType": {"shape": "ValidationExceptionType", "documentation": "<p>The type of ValidationException.</p>"}}, "documentation": "<p>The input failed to meet the constraints specified by the AWS service.</p>", "exception": true}, "ValidationExceptionType": {"type": "string", "enum": ["CONTAINER_TYPE_IMMUTABLE", "INVALID_PAGINATION_TOKEN", "INVALID_PAGINATION_MAX_RESULTS", "INVALID_POLICY", "INVALID_ROLE_ARN", "MANIFEST_NAME_COLLISION", "ENCRYPTION_METHOD_CONTAINER_TYPE_MISMATCH", "CENC_IV_INCOMPATIBLE", "ENCRYPTION_CONTRACT_WITHOUT_AUDIO_RENDITION_INCOMPATIBLE", "ENCRYPTION_CONTRACT_WITH_ISM_CONTAINER_INCOMPATIBLE", "ENCRYPTION_CONTRACT_UNENCRYPTED", "ENCRYPTION_CONTRACT_SHARED", "NUM_MANIFESTS_LOW", "NUM_MANIFESTS_HIGH", "MANIFEST_DRM_SYSTEMS_INCOMPATIBLE", "DRM_SYSTEMS_ENCRYPTION_METHOD_INCOMPATIBLE", "ROLE_ARN_NOT_ASSUMABLE", "ROLE_ARN_LENGTH_OUT_OF_RANGE", "ROLE_ARN_INVALID_FORMAT", "URL_INVALID", "URL_SCHEME", "URL_USER_INFO", "URL_PORT", "URL_UNKNOWN_HOST", "URL_LOCAL_ADDRESS", "URL_LOOPBACK_ADDRESS", "URL_LINK_LOCAL_ADDRESS", "URL_MULTICAST_ADDRESS", "MEMBER_INVALID", "MEMBER_MISSING", "MEMBER_MIN_VALUE", "MEMBER_MAX_VALUE", "MEMBER_MIN_LENGTH", "MEMBER_MAX_LENGTH", "MEMBER_INVALID_ENUM_VALUE", "MEMBER_DOES_NOT_MATCH_PATTERN", "INVALID_MANIFEST_FILTER", "INVALID_TIME_DELAY_SECONDS", "END_TIME_EARLIER_THAN_START_TIME", "TS_CONTAINER_TYPE_WITH_DASH_MANIFEST", "DIRECT_MODE_WITH_TIMING_SOURCE", "NONE_MODE_WITH_TIMING_SOURCE", "TIMING_SOURCE_MISSING", "UPDATE_PERIOD_SMALLER_THAN_SEGMENT_DURATION", "PERIOD_TRIGGERS_NONE_SPECIFIED_WITH_ADDITIONAL_VALUES", "DRM_SIGNALING_MISMATCH_SEGMENT_ENCRYPTION_STATUS", "ONLY_CMAF_INPUT_TYPE_ALLOW_FORCE_ENDPOINT_ERROR_CONFIGURATION", "SOURCE_DISRUPTIONS_ENABLED_INCORRECTLY", "HARVESTED_MANIFEST_HAS_START_END_FILTER_CONFIGURATION", "HARVESTED_MANIFEST_NOT_FOUND_ON_ENDPOINT", "TOO_MANY_IN_PROGRESS_HARVEST_JOBS", "HARVEST_JOB_INELIGIBLE_FOR_CANCELLATION", "INVALID_HARVEST_JOB_DURATION", "HARVEST_JOB_S3_DESTINATION_MISSING_OR_INCOMPLETE", "HARVEST_JOB_UNABLE_TO_WRITE_TO_S3_DESTINATION", "HARVEST_JOB_CUSTOMER_ENDPOINT_READ_ACCESS_DENIED", "CLIP_START_TIME_WITH_START_OR_END", "START_TAG_TIME_OFFSET_INVALID", "INCOMPATIBLE_DASH_PROFILE_DVB_DASH_CONFIGURATION", "DASH_DVB_ATTRIBUTES_WITHOUT_DVB_DASH_PROFILE", "INCOMPATIBLE_DASH_COMPACTNESS_CONFIGURATION", "INCOMPATIBLE_XML_ENCODING", "CMAF_EXCLUDE_SEGMENT_DRM_METADATA_INCOMPATIBLE_CONTAINER_TYPE", "ONLY_CMAF_INPUT_TYPE_ALLOW_MQCS_INPUT_SWITCHING", "ONLY_CMAF_INPUT_TYPE_ALLOW_MQCS_OUTPUT_CONFIGURATION", "TS_CONTAINER_TYPE_WITH_MSS_MANIFEST", "CMAF_CONTAINER_TYPE_WITH_MSS_MANIFEST", "ISM_CONTAINER_TYPE_WITH_HLS_MANIFEST", "ISM_CONTAINER_TYPE_WITH_LL_HLS_MANIFEST", "ISM_CONTAINER_TYPE_WITH_DASH_MANIFEST", "ISM_CONTAINER_TYPE_WITH_SCTE", "ISM_CONTAINER_WITH_KEY_ROTATION"]}}, "documentation": "<note> <p>This guide is intended for creating AWS Elemental MediaPackage resources in MediaPackage Version 2 (v2) starting from May 2023. To get started with MediaPackage v2, create your MediaPackage resources. There isn't an automated process to migrate your resources from MediaPackage v1 to MediaPackage v2. </p> <p>The names of the entities that you use to access this API, like URLs and ARNs, all have the versioning information added, like \"v2\", to distinguish from the prior version. If you used MediaPackage prior to this release, you can't use the MediaPackage v2 CLI or the MediaPackage v2 API to access any MediaPackage v1 resources.</p> <p>If you created resources in MediaPackage v1, use video on demand (VOD) workflows, and aren't looking to migrate to MediaPackage v2 yet, see the <a href=\"https://docs.aws.amazon.com/mediapackage/latest/apireference/what-is.html\">MediaPackage v1 Live API Reference</a>.</p> </note> <p>This is the AWS Elemental MediaPackage v2 Live REST API Reference. It describes all the MediaPackage API operations for live content in detail, and provides sample requests, responses, and errors for the supported web services protocols.</p> <p>We assume that you have the IAM permissions that you need to use MediaPackage via the REST API. We also assume that you are familiar with the features and operations of MediaPackage, as described in the AWS Elemental MediaPackage User Guide.</p>"}