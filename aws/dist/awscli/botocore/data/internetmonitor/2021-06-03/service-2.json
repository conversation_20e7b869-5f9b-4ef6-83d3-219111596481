{"version": "2.0", "metadata": {"apiVersion": "2021-06-03", "auth": ["aws.auth#sigv4"], "endpointPrefix": "internetmonitor", "protocol": "rest-json", "protocols": ["rest-json"], "serviceFullName": "Amazon CloudWatch Internet Monitor", "serviceId": "InternetMonitor", "signatureVersion": "v4", "signingName": "internetmonitor", "uid": "internetmonitor-2021-06-03"}, "operations": {"CreateMonitor": {"name": "CreateMonitor", "http": {"method": "POST", "requestUri": "/v20210603/Monitors", "responseCode": 200}, "input": {"shape": "CreateMonitorInput"}, "output": {"shape": "CreateMonitorOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "LimitExceededException"}, {"shape": "ValidationException"}], "documentation": "<p>Creates a monitor in Amazon CloudWatch Internet Monitor. A monitor is built based on information from the application resources that you add: VPCs, Network Load Balancers (NLBs), Amazon CloudFront distributions, and Amazon WorkSpaces directories. Internet Monitor then publishes internet measurements from Amazon Web Services that are specific to the <i>city-networks</i>. That is, the locations and ASNs (typically internet service providers or ISPs), where clients access your application. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/CloudWatch-InternetMonitor.html\">Using Amazon CloudWatch Internet Monitor</a> in the <i>Amazon CloudWatch User Guide</i>.</p> <p>When you create a monitor, you choose the percentage of traffic that you want to monitor. You can also set a maximum limit for the number of city-networks where client traffic is monitored, that caps the total traffic that Internet Monitor monitors. A city-network maximum is the limit of city-networks, but you only pay for the number of city-networks that are actually monitored. You can update your monitor at any time to change the percentage of traffic to monitor or the city-networks maximum. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/IMCityNetworksMaximum.html\">Choosing a city-network maximum value</a> in the <i>Amazon CloudWatch User Guide</i>.</p>", "idempotent": true}, "DeleteMonitor": {"name": "DeleteMonitor", "http": {"method": "DELETE", "requestUri": "/v20210603/Monitors/{MonitorName}", "responseCode": 200}, "input": {"shape": "DeleteMonitorInput"}, "output": {"shape": "DeleteMonitorOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Deletes a monitor in Amazon CloudWatch Internet Monitor. </p>", "idempotent": true}, "GetHealthEvent": {"name": "GetHealthEvent", "http": {"method": "GET", "requestUri": "/v20210603/Monitors/{MonitorName}/HealthEvents/{EventId}", "responseCode": 200}, "input": {"shape": "GetHealthEventInput"}, "output": {"shape": "GetHealthEventOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Gets information that Amazon CloudWatch Internet Monitor has created and stored about a health event for a specified monitor. This information includes the impacted locations, and all the information related to the event, by location.</p> <p>The information returned includes the impact on performance, availability, and round-trip time, information about the network providers (ASNs), the event type, and so on.</p> <p>Information rolled up at the global traffic level is also returned, including the impact type and total traffic impact.</p>"}, "GetInternetEvent": {"name": "GetInternetEvent", "http": {"method": "GET", "requestUri": "/v20210603/InternetEvents/{EventId}", "responseCode": 200}, "input": {"shape": "GetInternetEventInput"}, "output": {"shape": "GetInternetEventOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Gets information that Amazon CloudWatch Internet Monitor has generated about an internet event. Internet Monitor displays information about recent global health events, called internet events, on a global outages map that is available to all Amazon Web Services customers. </p> <p>The information returned here includes the impacted location, when the event started and (if the event is over) ended, the type of event (<code>PERFORMANCE</code> or <code>AVAILABILITY</code>), and the status (<code>ACTIVE</code> or <code>RESOLVED</code>).</p>"}, "GetMonitor": {"name": "GetMonitor", "http": {"method": "GET", "requestUri": "/v20210603/Monitors/{MonitorName}", "responseCode": 200}, "input": {"shape": "GetMonitorInput"}, "output": {"shape": "GetMonitorOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Gets information about a monitor in Amazon CloudWatch Internet Monitor based on a monitor name. The information returned includes the Amazon Resource Name (ARN), create time, modified time, resources included in the monitor, and status information.</p>"}, "GetQueryResults": {"name": "GetQueryResults", "http": {"method": "GET", "requestUri": "/v20210603/Monitors/{MonitorName}/Queries/{QueryId}/Results", "responseCode": 200}, "input": {"shape": "GetQueryResultsInput"}, "output": {"shape": "GetQueryResultsOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "LimitExceededException"}, {"shape": "ValidationException"}], "documentation": "<p>Return the data for a query with the Amazon CloudWatch Internet Monitor query interface. Specify the query that you want to return results for by providing a <code>QueryId</code> and a monitor name.</p> <p>For more information about using the query interface, including examples, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/CloudWatch-IM-view-cw-tools-cwim-query.html\">Using the Amazon CloudWatch Internet Monitor query interface</a> in the Amazon CloudWatch Internet Monitor User Guide.</p>"}, "GetQueryStatus": {"name": "GetQueryStatus", "http": {"method": "GET", "requestUri": "/v20210603/Monitors/{MonitorName}/Queries/{QueryId}/Status", "responseCode": 200}, "input": {"shape": "GetQueryStatusInput"}, "output": {"shape": "GetQueryStatusOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "LimitExceededException"}, {"shape": "ValidationException"}], "documentation": "<p>Returns the current status of a query for the Amazon CloudWatch Internet Monitor query interface, for a specified query ID and monitor. When you run a query, check the status to make sure that the query has <code>SUCCEEDED</code> before you review the results.</p> <ul> <li> <p> <code>QUEUED</code>: The query is scheduled to run.</p> </li> <li> <p> <code>RUNNING</code>: The query is in progress but not complete.</p> </li> <li> <p> <code>SUCCEEDED</code>: The query completed sucessfully.</p> </li> <li> <p> <code>FAILED</code>: The query failed due to an error.</p> </li> <li> <p> <code>CANCELED</code>: The query was canceled.</p> </li> </ul>"}, "ListHealthEvents": {"name": "ListHealthEvents", "http": {"method": "GET", "requestUri": "/v20210603/Monitors/{MonitorName}/HealthEvents", "responseCode": 200}, "input": {"shape": "ListHealthEventsInput"}, "output": {"shape": "ListHealthEventsOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists all health events for a monitor in Amazon CloudWatch Internet Monitor. Returns information for health events including the event start and end times, and the status.</p> <note> <p>Health events that have start times during the time frame that is requested are not included in the list of health events.</p> </note>"}, "ListInternetEvents": {"name": "ListInternetEvents", "http": {"method": "GET", "requestUri": "/v20210603/InternetEvents", "responseCode": 200}, "input": {"shape": "ListInternetEventsInput"}, "output": {"shape": "ListInternetEventsOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists internet events that cause performance or availability issues for client locations. Amazon CloudWatch Internet Monitor displays information about recent global health events, called internet events, on a global outages map that is available to all Amazon Web Services customers. </p> <p>You can constrain the list of internet events returned by providing a start time and end time to define a total time frame for events you want to list. Both start time and end time specify the time when an event started. End time is optional. If you don't include it, the default end time is the current time.</p> <p>You can also limit the events returned to a specific status (<code>ACTIVE</code> or <code>RESOLVED</code>) or type (<code>PERFORMANCE</code> or <code>AVAILABILITY</code>).</p>"}, "ListMonitors": {"name": "ListMonitors", "http": {"method": "GET", "requestUri": "/v20210603/Monitors", "responseCode": 200}, "input": {"shape": "ListMonitorsInput"}, "output": {"shape": "ListMonitorsOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists all of your monitors for Amazon CloudWatch Internet Monitor and their statuses, along with the Amazon Resource Name (ARN) and name of each monitor.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags/{ResourceArn}", "responseCode": 200}, "input": {"shape": "ListTagsForResourceInput"}, "output": {"shape": "ListTagsForResourceOutput"}, "errors": [{"shape": "TooManyRequestsException"}, {"shape": "AccessDeniedException"}, {"shape": "NotFoundException"}, {"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Lists the tags for a resource. Tags are supported only for monitors in Amazon CloudWatch Internet Monitor.</p>"}, "StartQuery": {"name": "Start<PERSON>uery", "http": {"method": "POST", "requestUri": "/v20210603/Monitors/{MonitorName}/Queries", "responseCode": 200}, "input": {"shape": "StartQueryInput"}, "output": {"shape": "StartQueryOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "LimitExceededException"}, {"shape": "ValidationException"}], "documentation": "<p>Start a query to return data for a specific query type for the Amazon CloudWatch Internet Monitor query interface. Specify a time period for the data that you want returned by using <code>StartTime</code> and <code>EndTime</code>. You filter the query results to return by providing parameters that you specify with <code>FilterParameters</code>.</p> <p>For more information about using the query interface, including examples, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/CloudWatch-IM-view-cw-tools-cwim-query.html\">Using the Amazon CloudWatch Internet Monitor query interface</a> in the Amazon CloudWatch Internet Monitor User Guide.</p>"}, "StopQuery": {"name": "StopQuery", "http": {"method": "DELETE", "requestUri": "/v20210603/Monitors/{MonitorName}/Queries/{QueryId}", "responseCode": 200}, "input": {"shape": "StopQueryInput"}, "output": {"shape": "StopQueryOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "LimitExceededException"}, {"shape": "ValidationException"}], "documentation": "<p>Stop a query that is progress for a specific monitor.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags/{ResourceArn}", "responseCode": 204}, "input": {"shape": "TagResourceInput"}, "output": {"shape": "TagResourceOutput"}, "errors": [{"shape": "TooManyRequestsException"}, {"shape": "AccessDeniedException"}, {"shape": "NotFoundException"}, {"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Adds a tag to a resource. Tags are supported only for monitors in Amazon CloudWatch Internet Monitor. You can add a maximum of 50 tags in Internet Monitor.</p> <p>A minimum of one tag is required for this call. It returns an error if you use the <code>TagResource</code> request with 0 tags.</p>", "idempotent": true}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags/{ResourceArn}", "responseCode": 204}, "input": {"shape": "UntagResourceInput"}, "output": {"shape": "UntagResourceOutput"}, "errors": [{"shape": "TooManyRequestsException"}, {"shape": "AccessDeniedException"}, {"shape": "NotFoundException"}, {"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Removes a tag from a resource.</p>", "idempotent": true}, "UpdateMonitor": {"name": "UpdateMonitor", "http": {"method": "PATCH", "requestUri": "/v20210603/Monitors/{MonitorName}", "responseCode": 200}, "input": {"shape": "UpdateMonitorInput"}, "output": {"shape": "UpdateMonitorOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "LimitExceededException"}, {"shape": "ValidationException"}], "documentation": "<p>Updates a monitor. You can update a monitor to change the percentage of traffic to monitor or the maximum number of city-networks (locations and ASNs), to add or remove resources, or to change the status of the monitor. Note that you can't change the name of a monitor.</p> <p>The city-network maximum that you choose is the limit, but you only pay for the number of city-networks that are actually monitored. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/IMCityNetworksMaximum.html\">Choosing a city-network maximum value</a> in the <i>Amazon CloudWatch User Guide</i>.</p>", "idempotent": true}}, "shapes": {"AccessDeniedException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>You don't have sufficient permission to perform this action.</p>", "error": {"httpStatusCode": 403, "senderFault": true}, "exception": true}, "AccountId": {"type": "string", "max": 12, "min": 12}, "Arn": {"type": "string", "max": 2048, "min": 20, "pattern": "arn:.*"}, "AvailabilityMeasurement": {"type": "structure", "members": {"ExperienceScore": {"shape": "Double", "documentation": "<p>Experience scores, or health scores are calculated for different geographic and network provider combinations (that is, different granularities) and also summed into global scores. If you view performance or availability scores without filtering for any specific geography or service provider, Amazon CloudWatch Internet Monitor provides global health scores.</p> <p>The Amazon CloudWatch Internet Monitor chapter in the <i>CloudWatch User Guide</i> includes detailed information about how Internet Monitor calculates health scores, including performance and availability scores, and when it creates and resolves health events. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/CloudWatch-IM-inside-internet-monitor.html#IMExperienceScores\">How Amazon Web Services calculates performance and availability scores</a> in the Amazon CloudWatch Internet Monitor section of the <i>CloudWatch User Guide</i>.</p>"}, "PercentOfTotalTrafficImpacted": {"shape": "Double", "documentation": "<p>The impact on total traffic that a health event has, in increased latency or reduced availability. This is the percentage of how much latency has increased or availability has decreased during the event, compared to what is typical for traffic from this client location to the Amazon Web Services location using this client network.</p> <p>For information about how Internet Monitor calculates impact, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/CloudWatch-IM-inside-internet-monitor.html\">How Internet Monitor works</a> in the Amazon CloudWatch Internet Monitor section of the Amazon CloudWatch User Guide.</p>"}, "PercentOfClientLocationImpacted": {"shape": "Double", "documentation": "<p>The percentage of impact caused by a health event for client location traffic globally.</p> <p>For information about how Internet Monitor calculates impact, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/CloudWatch-IM-inside-internet-monitor.html\">Inside Internet Monitor</a> in the Amazon CloudWatch Internet Monitor section of the Amazon CloudWatch User Guide.</p>"}}, "documentation": "<p>Amazon CloudWatch Internet Monitor calculates measurements about the availability for your application's internet traffic between client locations and Amazon Web Services. Amazon Web Services has substantial historical data about internet performance and availability between Amazon Web Services services and different network providers and geographies. By applying statistical analysis to the data, Internet Monitor can detect when the performance and availability for your application has dropped, compared to an estimated baseline that's already calculated. To make it easier to see those drops, we report that information to you in the form of health scores: a performance score and an availability score.</p> <p>Availability in Internet Monitor represents the estimated percentage of traffic that is not seeing an availability drop. For example, an availability score of 99% for an end user and service location pair is equivalent to 1% of the traffic experiencing an availability drop for that pair.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/CloudWatch-IM-inside-internet-monitor.html#IMExperienceScores\">How Internet Monitor calculates performance and availability scores</a> in the Amazon CloudWatch Internet Monitor section of the <i>Amazon CloudWatch User Guide</i>.</p>"}, "BadRequestException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>A bad request was received.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "Boolean": {"type": "boolean", "box": true}, "ClientLocation": {"type": "structure", "required": ["ASName", "ASNumber", "Country", "City", "Latitude", "Longitude"], "members": {"ASName": {"shape": "String", "documentation": "<p>The name of the internet service provider (ISP) or network (ASN).</p>"}, "ASNumber": {"shape": "<PERSON>", "documentation": "<p>The Autonomous System Number (ASN) of the network at an impacted location.</p>"}, "Country": {"shape": "String", "documentation": "<p>The name of the country where the internet event is located.</p>"}, "Subdivision": {"shape": "String", "documentation": "<p>The subdivision location where the health event is located. The subdivision usually maps to states in most countries (including the United States). For United Kingdom, it maps to a country (England, Scotland, Wales) or province (Northern Ireland).</p>"}, "Metro": {"shape": "String", "documentation": "<p>The metro area where the health event is located.</p> <p>Metro indicates a metropolitan region in the United States, such as the region around New York City. In non-US countries, this is a second-level subdivision. For example, in the United Kingdom, it could be a county, a London borough, a unitary authority, council area, and so on.</p>"}, "City": {"shape": "String", "documentation": "<p>The name of the city where the internet event is located.</p>"}, "Latitude": {"shape": "Double", "documentation": "<p>The latitude where the internet event is located.</p>"}, "Longitude": {"shape": "Double", "documentation": "<p>The longitude where the internet event is located.</p>"}}, "documentation": "<p>The impacted location, such as a city, that Amazon Web Services clients access application resources from.</p>"}, "ConflictException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>The requested resource is in use.</p>", "error": {"httpStatusCode": 409, "senderFault": true}, "exception": true}, "CreateMonitorInput": {"type": "structure", "required": ["MonitorName"], "members": {"MonitorName": {"shape": "ResourceName", "documentation": "<p>The name of the monitor. </p>"}, "Resources": {"shape": "SetOfARNs", "documentation": "<p>The resources to include in a monitor, which you provide as a set of Amazon Resource Names (ARNs). Resources can be VPCs, NLBs, Amazon CloudFront distributions, or Amazon WorkSpaces directories.</p> <p>You can add a combination of VPCs and CloudFront distributions, or you can add WorkSpaces directories, or you can add NLBs. You can't add NLBs or WorkSpaces directories together with any other resources.</p> <note> <p>If you add only Amazon VPC resources, at least one VPC must have an Internet Gateway attached to it, to make sure that it has internet connectivity.</p> </note>"}, "ClientToken": {"shape": "String", "documentation": "<p>A unique, case-sensitive string of up to 64 ASCII characters that you specify to make an idempotent API request. Don't reuse the same client token for other API requests.</p>", "idempotencyToken": true}, "Tags": {"shape": "TagMap", "documentation": "<p>The tags for a monitor. You can add a maximum of 50 tags in Internet Monitor.</p>"}, "MaxCityNetworksToMonitor": {"shape": "MaxCityNetworksToMonitor", "documentation": "<p>The maximum number of city-networks to monitor for your resources. A city-network is the location (city) where clients access your application resources from and the ASN or network provider, such as an internet service provider (ISP), that clients access the resources through. Setting this limit can help control billing costs.</p> <p>To learn more, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/IMCityNetworksMaximum.html\">Choosing a city-network maximum value </a> in the Amazon CloudWatch Internet Monitor section of the <i>CloudWatch User Guide</i>.</p>"}, "InternetMeasurementsLogDelivery": {"shape": "InternetMeasurementsLogDelivery", "documentation": "<p>Publish internet measurements for Internet Monitor to an Amazon S3 bucket in addition to CloudWatch Logs.</p>"}, "TrafficPercentageToMonitor": {"shape": "TrafficPercentageToMonitor", "documentation": "<p>The percentage of the internet-facing traffic for your application that you want to monitor with this monitor. If you set a city-networks maximum, that limit overrides the traffic percentage that you set.</p> <p>To learn more, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/IMTrafficPercentage.html\">Choosing an application traffic percentage to monitor </a> in the Amazon CloudWatch Internet Monitor section of the <i>CloudWatch User Guide</i>.</p>"}, "HealthEventsConfig": {"shape": "HealthEventsConfig", "documentation": "<p>Defines the threshold percentages and other configuration information for when Amazon CloudWatch Internet Monitor creates a health event. Internet Monitor creates a health event when an internet issue that affects your application end users has a health score percentage that is at or below a specific threshold, and, sometimes, when other criteria are met.</p> <p>If you don't set a health event threshold, the default value is 95%.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/CloudWatch-IM-overview.html#IMUpdateThresholdFromOverview\"> Change health event thresholds</a> in the Internet Monitor section of the <i>CloudWatch User Guide</i>.</p>"}}}, "CreateMonitorOutput": {"type": "structure", "required": ["<PERSON><PERSON>", "Status"], "members": {"Arn": {"shape": "MonitorArn", "documentation": "<p>The Amazon Resource Name (ARN) of the monitor.</p>"}, "Status": {"shape": "MonitorConfigState", "documentation": "<p>The status of a monitor.</p>"}}}, "DeleteMonitorInput": {"type": "structure", "required": ["MonitorName"], "members": {"MonitorName": {"shape": "ResourceName", "documentation": "<p>The name of the monitor to delete.</p>", "location": "uri", "locationName": "MonitorName"}}}, "DeleteMonitorOutput": {"type": "structure", "members": {}}, "Double": {"type": "double", "box": true}, "FilterList": {"type": "list", "member": {"shape": "String"}}, "FilterParameter": {"type": "structure", "members": {"Field": {"shape": "String", "documentation": "<p>A data field that you want to filter, to further scope your application's Internet Monitor data in a repository that you created by running a query. A field might be <code>city</code>, for example. The field must be one of the fields that was returned by the specific query that you used to create the repository.</p>"}, "Operator": {"shape": "Operator", "documentation": "<p>The operator to use with the filter field and a value, such as <code>not_equals</code>.</p>"}, "Values": {"shape": "FilterList", "documentation": "<p>One or more values to be used, together with the specified operator, to filter data for a query. For example, you could specify an array of values such as <code>[\"Seattle\", \"Redmond\"]</code>. Values in the array are separated by commas.</p>"}}, "documentation": "<p>A filter that you use with the results of a Amazon CloudWatch Internet Monitor query that you created and ran. The query sets up a repository of data that is a subset of your application's Internet Monitor data. <code>FilterParameter</code> is a string that defines how you want to filter the repository of data to return a set of results, based on your criteria.</p> <p>The filter parameters that you can specify depend on the query type that you used to create the repository, since each query type returns a different set of Internet Monitor data.</p> <p>For each filter, you specify a field (such as <code>city</code>), an operator (such as <code>not_equals</code>, and a value or array of values (such as <code>[\"Seattle\", \"Redmond\"]</code>). Separate values in the array with commas.</p> <p>For more information about specifying filter parameters, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/CloudWatch-IM-view-cw-tools-cwim-query.html\">Using the Amazon CloudWatch Internet Monitor query interface</a> in the Amazon CloudWatch Internet Monitor User Guide.</p>"}, "FilterParameters": {"type": "list", "member": {"shape": "FilterParameter"}}, "GetHealthEventInput": {"type": "structure", "required": ["MonitorName", "EventId"], "members": {"MonitorName": {"shape": "ResourceName", "documentation": "<p>The name of the monitor.</p>", "location": "uri", "locationName": "MonitorName"}, "EventId": {"shape": "HealthEventName", "documentation": "<p>The internally-generated identifier of a health event. Because <code>EventID</code> contains the forward slash (“/”) character, you must URL-encode the <code>EventID</code> field in the request URL.</p>", "location": "uri", "locationName": "EventId"}, "LinkedAccountId": {"shape": "AccountId", "documentation": "<p>The account ID for an account that you've set up cross-account sharing for in Amazon CloudWatch Internet Monitor. You configure cross-account sharing by using Amazon CloudWatch Observability Access Manager. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/cwim-cross-account.html\">Internet Monitor cross-account observability</a> in the Amazon CloudWatch Internet Monitor User Guide.</p>", "location": "querystring", "locationName": "LinkedAccountId"}}}, "GetHealthEventOutput": {"type": "structure", "required": ["EventArn", "EventId", "StartedAt", "LastUpdatedAt", "ImpactedLocations", "Status", "ImpactType"], "members": {"EventArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the event.</p>"}, "EventId": {"shape": "HealthEventName", "documentation": "<p>The internally-generated identifier of a health event.</p>"}, "StartedAt": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time when a health event started.</p>"}, "EndedAt": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time when a health event was resolved. If the health event is still active, the end time is not set.</p>"}, "CreatedAt": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time when a health event was created.</p>"}, "LastUpdatedAt": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time when a health event was last updated or recalculated.</p>"}, "ImpactedLocations": {"shape": "ImpactedLocationsList", "documentation": "<p>The locations affected by a health event.</p>"}, "Status": {"shape": "HealthEventStatus", "documentation": "<p>The status of a health event.</p>"}, "PercentOfTotalTrafficImpacted": {"shape": "Double", "documentation": "<p>The impact on total traffic that a health event has, in increased latency or reduced availability. This is the percentage of how much latency has increased or availability has decreased during the event, compared to what is typical for traffic from this client location to the Amazon Web Services location using this client network.</p>"}, "ImpactType": {"shape": "HealthEventImpactType", "documentation": "<p>The type of impairment of a specific health event.</p>"}, "HealthScoreThreshold": {"shape": "Percentage", "documentation": "<p>The threshold percentage for a health score that determines, along with other configuration information, when Internet Monitor creates a health event when there's an internet issue that affects your application end users.</p>"}}}, "GetInternetEventInput": {"type": "structure", "required": ["EventId"], "members": {"EventId": {"shape": "InternetEventId", "documentation": "<p>The <code>EventId</code> of the internet event to return information for. </p>", "location": "uri", "locationName": "EventId"}}}, "GetInternetEventOutput": {"type": "structure", "required": ["EventId", "EventArn", "StartedAt", "ClientLocation", "EventType", "EventStatus"], "members": {"EventId": {"shape": "InternetEventId", "documentation": "<p>The internally-generated identifier of an internet event.</p>"}, "EventArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the internet event.</p>"}, "StartedAt": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time when the internet event started.</p>"}, "EndedAt": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time when the internet event ended. If the event hasn't ended yet, this value is empty.</p>"}, "ClientLocation": {"shape": "ClientLocation", "documentation": "<p>The impacted location, such as a city, where clients access Amazon Web Services application resources.</p>"}, "EventType": {"shape": "InternetEventType", "documentation": "<p>The type of network impairment.</p>"}, "EventStatus": {"shape": "InternetEventStatus", "documentation": "<p>The status of the internet event.</p>"}}}, "GetMonitorInput": {"type": "structure", "required": ["MonitorName"], "members": {"MonitorName": {"shape": "ResourceName", "documentation": "<p>The name of the monitor.</p>", "location": "uri", "locationName": "MonitorName"}, "LinkedAccountId": {"shape": "AccountId", "documentation": "<p>The account ID for an account that you've set up cross-account sharing for in Amazon CloudWatch Internet Monitor. You configure cross-account sharing by using Amazon CloudWatch Observability Access Manager. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/cwim-cross-account.html\">Internet Monitor cross-account observability</a> in the Amazon CloudWatch Internet Monitor User Guide.</p>", "location": "querystring", "locationName": "LinkedAccountId"}}}, "GetMonitorOutput": {"type": "structure", "required": ["MonitorName", "MonitorArn", "Resources", "Status", "CreatedAt", "ModifiedAt"], "members": {"MonitorName": {"shape": "ResourceName", "documentation": "<p>The name of the monitor.</p>"}, "MonitorArn": {"shape": "MonitorArn", "documentation": "<p>The Amazon Resource Name (ARN) of the monitor.</p>"}, "Resources": {"shape": "SetOfARNs", "documentation": "<p>The resources monitored by the monitor. Resources are listed by their Amazon Resource Names (ARNs).</p>"}, "Status": {"shape": "MonitorConfigState", "documentation": "<p>The status of the monitor.</p>"}, "CreatedAt": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time when the monitor was created.</p>"}, "ModifiedAt": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The last time that the monitor was modified.</p>"}, "ProcessingStatus": {"shape": "MonitorProcessingStatusCode", "documentation": "<p>The health of the data processing for the monitor.</p>"}, "ProcessingStatusInfo": {"shape": "String", "documentation": "<p>Additional information about the health of the data processing for the monitor.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>The tags that have been added to monitor.</p>"}, "MaxCityNetworksToMonitor": {"shape": "MaxCityNetworksToMonitor", "documentation": "<p>The maximum number of city-networks to monitor for your resources. A city-network is the location (city) where clients access your application resources from and the ASN or network provider, such as an internet service provider (ISP), that clients access the resources through. This limit can help control billing costs.</p> <p>To learn more, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/IMCityNetworksMaximum.html\">Choosing a city-network maximum value </a> in the Amazon CloudWatch Internet Monitor section of the <i>CloudWatch User Guide</i>.</p>"}, "InternetMeasurementsLogDelivery": {"shape": "InternetMeasurementsLogDelivery", "documentation": "<p>Publish internet measurements for Internet Monitor to another location, such as an Amazon S3 bucket. The measurements are also published to Amazon CloudWatch Logs.</p>"}, "TrafficPercentageToMonitor": {"shape": "TrafficPercentageToMonitor", "documentation": "<p>The percentage of the internet-facing traffic for your application to monitor with this monitor. If you set a city-networks maximum, that limit overrides the traffic percentage that you set.</p> <p>To learn more, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/IMTrafficPercentage.html\">Choosing an application traffic percentage to monitor </a> in the Amazon CloudWatch Internet Monitor section of the <i>CloudWatch User Guide</i>.</p>"}, "HealthEventsConfig": {"shape": "HealthEventsConfig", "documentation": "<p>The list of health event threshold configurations. The threshold percentage for a health score determines, along with other configuration information, when Internet Monitor creates a health event when there's an internet issue that affects your application end users.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/CloudWatch-IM-overview.html#IMUpdateThresholdFromOverview\"> Change health event thresholds</a> in the Internet Monitor section of the <i>CloudWatch User Guide</i>.</p>"}}}, "GetQueryResultsInput": {"type": "structure", "required": ["MonitorName", "QueryId"], "members": {"MonitorName": {"shape": "ResourceName", "documentation": "<p>The name of the monitor to return data for.</p>", "location": "uri", "locationName": "MonitorName"}, "QueryId": {"shape": "String", "documentation": "<p>The ID of the query that you want to return data results for. A <code>QueryId</code> is an internally-generated identifier for a specific query.</p>", "location": "uri", "locationName": "QueryId"}, "NextToken": {"shape": "String", "documentation": "<p>The token for the next set of results. You receive this token from a previous call.</p>", "location": "querystring", "locationName": "NextToken"}, "MaxResults": {"shape": "QueryMaxResults", "documentation": "<p>The number of query results that you want to return with this call.</p>", "location": "querystring", "locationName": "MaxResults"}}}, "GetQueryResultsOutput": {"type": "structure", "required": ["Fields", "Data"], "members": {"Fields": {"shape": "QueryFields", "documentation": "<p>The fields that the query returns data for. Fields are name-data type pairs, such as <code>availability_score</code>-<code>float</code>.</p>"}, "Data": {"shape": "QueryData", "documentation": "<p>The data results that the query returns. Data is returned in arrays, aligned with the <code>Fields</code> for the query, which creates a repository of Amazon CloudWatch Internet Monitor information for your application. Then, you can filter the information in the repository by using <code>FilterParameters</code> that you define.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The token for the next set of results. You receive this token from a previous call.</p>"}}}, "GetQueryStatusInput": {"type": "structure", "required": ["MonitorName", "QueryId"], "members": {"MonitorName": {"shape": "ResourceName", "documentation": "<p>The name of the monitor.</p>", "location": "uri", "locationName": "MonitorName"}, "QueryId": {"shape": "String", "documentation": "<p>The ID of the query that you want to return the status for. A <code>QueryId</code> is an internally-generated dentifier for a specific query.</p>", "location": "uri", "locationName": "QueryId"}}}, "GetQueryStatusOutput": {"type": "structure", "required": ["Status"], "members": {"Status": {"shape": "QueryStatus", "documentation": "<p>The current status for a query.</p>"}}}, "HealthEvent": {"type": "structure", "required": ["EventArn", "EventId", "StartedAt", "LastUpdatedAt", "ImpactedLocations", "Status", "ImpactType"], "members": {"EventArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the event.</p>"}, "EventId": {"shape": "HealthEventName", "documentation": "<p>The internally-generated identifier of a specific network traffic impairment health event.</p>"}, "StartedAt": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>When a health event started.</p>"}, "EndedAt": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time when a health event ended. If the health event is still active, then the end time is not set.</p>"}, "CreatedAt": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>When the health event was created.</p>"}, "LastUpdatedAt": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>When the health event was last updated.</p>"}, "ImpactedLocations": {"shape": "ImpactedLocationsList", "documentation": "<p>The locations impacted by the health event.</p>"}, "Status": {"shape": "HealthEventStatus", "documentation": "<p>The status of a health event.</p>"}, "PercentOfTotalTrafficImpacted": {"shape": "Double", "documentation": "<p>The impact on total traffic that a health event has, in increased latency or reduced availability. This is the percentage of how much latency has increased or availability has decreased during the event, compared to what is typical for traffic from this client location to the Amazon Web Services location using this client network.</p>"}, "ImpactType": {"shape": "HealthEventImpactType", "documentation": "<p>The type of impairment for a health event.</p>"}, "HealthScoreThreshold": {"shape": "Percentage", "documentation": "<p>The value of the threshold percentage for performance or availability that was configured when Amazon CloudWatch Internet Monitor created the health event.</p>"}}, "documentation": "<p>Information about a health event created in a monitor in Amazon CloudWatch Internet Monitor.</p>"}, "HealthEventImpactType": {"type": "string", "enum": ["AVAILABILITY", "PERFORMANCE", "LOCAL_AVAILABILITY", "LOCAL_PERFORMANCE"]}, "HealthEventList": {"type": "list", "member": {"shape": "HealthEvent"}}, "HealthEventName": {"type": "string", "max": 255, "min": 1, "pattern": "[a-zA-Z0-9/_.-]+"}, "HealthEventStatus": {"type": "string", "enum": ["ACTIVE", "RESOLVED"]}, "HealthEventsConfig": {"type": "structure", "members": {"AvailabilityScoreThreshold": {"shape": "Percentage", "documentation": "<p>The health event threshold percentage set for availability scores.</p>"}, "PerformanceScoreThreshold": {"shape": "Percentage", "documentation": "<p>The health event threshold percentage set for performance scores.</p>"}, "AvailabilityLocalHealthEventsConfig": {"shape": "LocalHealthEventsConfig", "documentation": "<p>The configuration that determines the threshold and other conditions for when Internet Monitor creates a health event for a local availability issue.</p>"}, "PerformanceLocalHealthEventsConfig": {"shape": "LocalHealthEventsConfig", "documentation": "<p>The configuration that determines the threshold and other conditions for when Internet Monitor creates a health event for a local performance issue.</p>"}}, "documentation": "<p>A complex type with the configuration information that determines the threshold and other conditions for when Internet Monitor creates a health event for an overall performance or availability issue, across an application's geographies.</p> <p>Defines the percentages, for overall performance scores and availability scores for an application, that are the thresholds for when Amazon CloudWatch Internet Monitor creates a health event. You can override the defaults to set a custom threshold for overall performance or availability scores, or both.</p> <p>You can also set thresholds for local health scores,, where Internet Monitor creates a health event when scores cross a threshold for one or more city-networks, in addition to creating an event when an overall score crosses a threshold.</p> <p>If you don't set a health event threshold, the default value is 95%.</p> <p>For local thresholds, you also set a minimum percentage of overall traffic that is impacted by an issue before Internet Monitor creates an event. In addition, you can disable local thresholds, for performance scores, availability scores, or both.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/CloudWatch-IM-overview.html#IMUpdateThresholdFromOverview\"> Change health event thresholds</a> in the Internet Monitor section of the <i>CloudWatch User Guide</i>.</p>"}, "ImpactedLocation": {"type": "structure", "required": ["ASName", "ASNumber", "Country", "Status"], "members": {"ASName": {"shape": "String", "documentation": "<p>The name of the internet service provider (ISP) or network (ASN).</p>"}, "ASNumber": {"shape": "<PERSON>", "documentation": "<p>The Autonomous System Number (ASN) of the network at an impacted location.</p>"}, "Country": {"shape": "String", "documentation": "<p>The name of the country where the health event is located.</p>"}, "Subdivision": {"shape": "String", "documentation": "<p>The subdivision location where the health event is located. The subdivision usually maps to states in most countries (including the United States). For United Kingdom, it maps to a country (England, Scotland, Wales) or province (Northern Ireland).</p>"}, "Metro": {"shape": "String", "documentation": "<p>The metro area where the health event is located.</p> <p>Metro indicates a metropolitan region in the United States, such as the region around New York City. In non-US countries, this is a second-level subdivision. For example, in the United Kingdom, it could be a county, a London borough, a unitary authority, council area, and so on.</p>"}, "City": {"shape": "String", "documentation": "<p>The name of the city where the health event is located.</p>"}, "Latitude": {"shape": "Double", "documentation": "<p>The latitude where the health event is located.</p>"}, "Longitude": {"shape": "Double", "documentation": "<p>The longitude where the health event is located.</p>"}, "CountryCode": {"shape": "String", "documentation": "<p>The country code where the health event is located. The ISO 3166-2 codes for the country is provided, when available. </p>"}, "SubdivisionCode": {"shape": "String", "documentation": "<p>The subdivision code where the health event is located. The ISO 3166-2 codes for country subdivisions is provided, when available. </p>"}, "ServiceLocation": {"shape": "String", "documentation": "<p>The service location where the health event is located.</p>"}, "Status": {"shape": "HealthEventStatus", "documentation": "<p>The status of the health event at an impacted location.</p>"}, "CausedBy": {"shape": "NetworkImpairment", "documentation": "<p>The cause of the impairment. There are two types of network impairments: Amazon Web Services network issues or internet issues. Internet issues are typically a problem with a network provider, like an internet service provider (ISP).</p>"}, "InternetHealth": {"shape": "InternetHealth", "documentation": "<p>The calculated health at a specific location.</p>"}, "Ipv4Prefixes": {"shape": "Ipv4PrefixList", "documentation": "<p>The IPv4 prefixes at the client location that was impacted by the health event.</p>"}}, "documentation": "<p>Information about a location impacted by a health event in Amazon CloudWatch Internet Monitor.</p> <p>Geographic regions are hierarchically categorized into country, subdivision, metro and city geographic granularities. The geographic region is identified based on the IP address used at the client locations.</p>"}, "ImpactedLocationsList": {"type": "list", "member": {"shape": "ImpactedLocation"}}, "InternalServerErrorException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>There was an internal server error.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true, "retryable": {"throttling": false}}, "InternalServerException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>An internal error occurred.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true, "retryable": {"throttling": false}}, "InternetEventId": {"type": "string", "max": 255, "min": 1, "pattern": "[a-zA-Z0-9-]+"}, "InternetEventMaxResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "InternetEventStatus": {"type": "string", "enum": ["ACTIVE", "RESOLVED"]}, "InternetEventSummary": {"type": "structure", "required": ["EventId", "EventArn", "StartedAt", "ClientLocation", "EventType", "EventStatus"], "members": {"EventId": {"shape": "InternetEventId", "documentation": "<p>The internally-generated identifier of an internet event.</p>"}, "EventArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the internet event.</p>"}, "StartedAt": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time when an internet event started.</p>"}, "EndedAt": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time when an internet event ended. If the event hasn't ended yet, this value is empty.</p>"}, "ClientLocation": {"shape": "ClientLocation", "documentation": "<p>The impacted location, such as a city, that Amazon Web Services clients access application resources from.</p>"}, "EventType": {"shape": "InternetEventType", "documentation": "<p>The type of network impairment.</p>"}, "EventStatus": {"shape": "InternetEventStatus", "documentation": "<p>The status of an internet event.</p>"}}, "documentation": "<p>A summary of information about an internet event in Amazon CloudWatch Internet Monitor. Internet events are issues that cause performance degradation or availability problems for impacted Amazon Web Services client locations. Internet Monitor displays information about recent global health events, called internet events, on a global outages map that is available to all Amazon Web Services customers. </p>"}, "InternetEventType": {"type": "string", "enum": ["AVAILABILITY", "PERFORMANCE"]}, "InternetEventsList": {"type": "list", "member": {"shape": "InternetEventSummary"}}, "InternetHealth": {"type": "structure", "members": {"Availability": {"shape": "AvailabilityMeasurement", "documentation": "<p>Availability in Internet Monitor represents the estimated percentage of traffic that is not seeing an availability drop. For example, an availability score of 99% for an end user and service location pair is equivalent to 1% of the traffic experiencing an availability drop for that pair.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/CloudWatch-IM-inside-internet-monitor.html#IMExperienceScores\">How Internet Monitor calculates performance and availability scores</a> in the Amazon CloudWatch Internet Monitor section of the <i>CloudWatch User Guide</i>.</p>"}, "Performance": {"shape": "PerformanceMeasurement", "documentation": "<p>Performance in Internet Monitor represents the estimated percentage of traffic that is not seeing a performance drop. For example, a performance score of 99% for an end user and service location pair is equivalent to 1% of the traffic experiencing a performance drop for that pair.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/CloudWatch-IM-inside-internet-monitor.html#IMExperienceScores\">How Internet Monitor calculates performance and availability scores</a> in the Amazon CloudWatch Internet Monitor section of the <i>CloudWatch User Guide</i>.</p>"}}, "documentation": "<p>Internet health includes measurements calculated by Amazon CloudWatch Internet Monitor about the performance and availability for your application on the internet. Amazon Web Services has substantial historical data about internet performance and availability between Amazon Web Services services and different network providers and geographies. By applying statistical analysis to the data, Internet Monitor can detect when the performance and availability for your application has dropped, compared to an estimated baseline that's already calculated. To make it easier to see those drops, Internet Monitor reports the information to you in the form of health scores: a performance score and an availability score.</p>"}, "InternetMeasurementsLogDelivery": {"type": "structure", "members": {"S3Config": {"shape": "S3Config", "documentation": "<p>The configuration information for publishing Internet Monitor internet measurements to Amazon S3. The configuration includes the bucket name and (optionally) prefix for the S3 bucket to store the measurements, and the delivery status. The delivery status is <code>ENABLED</code> or <code>DISABLED</code>, depending on whether you choose to deliver internet measurements to S3 logs.</p>"}}, "documentation": "<p>Publish internet measurements to an Amazon S3 bucket in addition to CloudWatch Logs.</p>"}, "Ipv4PrefixList": {"type": "list", "member": {"shape": "String"}}, "LimitExceededException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>The request exceeded a service quota.</p>", "error": {"httpStatusCode": 403, "senderFault": true}, "exception": true}, "ListHealthEventsInput": {"type": "structure", "required": ["MonitorName"], "members": {"MonitorName": {"shape": "ResourceName", "documentation": "<p>The name of the monitor.</p>", "location": "uri", "locationName": "MonitorName"}, "StartTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time when a health event started.</p>", "location": "querystring", "locationName": "StartTime"}, "EndTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time when a health event ended. If the health event is still ongoing, then the end time is not set.</p>", "location": "querystring", "locationName": "EndTime"}, "NextToken": {"shape": "String", "documentation": "<p>The token for the next set of results. You receive this token from a previous call.</p>", "location": "querystring", "locationName": "NextToken"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The number of health event objects that you want to return with this call. </p>", "location": "querystring", "locationName": "MaxResults"}, "EventStatus": {"shape": "HealthEventStatus", "documentation": "<p>The status of a health event.</p>", "location": "querystring", "locationName": "EventStatus"}, "LinkedAccountId": {"shape": "AccountId", "documentation": "<p>The account ID for an account that you've set up cross-account sharing for in Amazon CloudWatch Internet Monitor. You configure cross-account sharing by using Amazon CloudWatch Observability Access Manager. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/cwim-cross-account.html\">Internet Monitor cross-account observability</a> in the Amazon CloudWatch Internet Monitor User Guide.</p>", "location": "querystring", "locationName": "LinkedAccountId"}}}, "ListHealthEventsOutput": {"type": "structure", "required": ["HealthEvents"], "members": {"HealthEvents": {"shape": "HealthEventList", "documentation": "<p>A list of health events.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The token for the next set of results. You receive this token from a previous call.</p>"}}}, "ListInternetEventsInput": {"type": "structure", "members": {"NextToken": {"shape": "String", "documentation": "<p>The token for the next set of results. You receive this token from a previous call.</p>", "location": "querystring", "locationName": "NextToken"}, "MaxResults": {"shape": "InternetEventMaxResults", "documentation": "<p>The number of query results that you want to return with this call.</p>", "location": "querystring", "locationName": "InternetEventMaxResults"}, "StartTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The start time of the time window that you want to get a list of internet events for.</p>", "location": "querystring", "locationName": "StartTime"}, "EndTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The end time of the time window that you want to get a list of internet events for.</p>", "location": "querystring", "locationName": "EndTime"}, "EventStatus": {"shape": "String", "documentation": "<p>The status of an internet event.</p>", "location": "querystring", "locationName": "EventStatus"}, "EventType": {"shape": "String", "documentation": "<p>The type of network impairment.</p>", "location": "querystring", "locationName": "EventType"}}}, "ListInternetEventsOutput": {"type": "structure", "required": ["InternetEvents"], "members": {"InternetEvents": {"shape": "InternetEventsList", "documentation": "<p>A set of internet events returned for the list operation.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The token for the next set of results. You receive this token from a previous call.</p>"}}}, "ListMonitorsInput": {"type": "structure", "members": {"NextToken": {"shape": "String", "documentation": "<p>The token for the next set of results. You receive this token from a previous call.</p>", "location": "querystring", "locationName": "NextToken"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The number of monitor objects that you want to return with this call.</p>", "location": "querystring", "locationName": "MaxResults"}, "MonitorStatus": {"shape": "String", "documentation": "<p>The status of a monitor. This includes the status of the data processing for the monitor and the status of the monitor itself.</p> <p>For information about the statuses for a monitor, see <a href=\"https://docs.aws.amazon.com/internet-monitor/latest/api/API_Monitor.html\"> Monitor</a>.</p>", "location": "querystring", "locationName": "MonitorStatus"}, "IncludeLinkedAccounts": {"shape": "Boolean", "documentation": "<p>A boolean option that you can set to <code>TRUE</code> to include monitors for linked accounts in a list of monitors, when you've set up cross-account sharing in Amazon CloudWatch Internet Monitor. You configure cross-account sharing by using Amazon CloudWatch Observability Access Manager. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/cwim-cross-account.html\">Internet Monitor cross-account observability</a> in the Amazon CloudWatch Internet Monitor User Guide.</p>", "location": "querystring", "locationName": "IncludeLinkedAccounts"}}}, "ListMonitorsOutput": {"type": "structure", "required": ["Monitors"], "members": {"Monitors": {"shape": "MonitorList", "documentation": "<p>A list of monitors.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The token for the next set of results. You receive this token from a previous call.</p>"}}}, "ListTagsForResourceInput": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "MonitorArn", "documentation": "<p>The Amazon Resource Name (ARN) for a resource.</p>", "location": "uri", "locationName": "ResourceArn"}}}, "ListTagsForResourceOutput": {"type": "structure", "members": {"Tags": {"shape": "TagMap", "documentation": "<p>Tags for a resource.</p>"}}}, "LocalHealthEventsConfig": {"type": "structure", "members": {"Status": {"shape": "LocalHealthEventsConfigStatus", "documentation": "<p>The status of whether Internet Monitor creates a health event based on a threshold percentage set for a local health score. The status can be <code>ENABLED</code> or <code>DISABLED</code>.</p>"}, "HealthScoreThreshold": {"shape": "Percentage", "documentation": "<p>The health event threshold percentage set for a local health score.</p>"}, "MinTrafficImpact": {"shape": "Percentage", "documentation": "<p>The minimum percentage of overall traffic for an application that must be impacted by an issue before Internet Monitor creates an event when a threshold is crossed for a local health score.</p> <p>If you don't set a minimum traffic impact threshold, the default value is 0.1%.</p>"}}, "documentation": "<p>A complex type with the configuration information that determines the threshold and other conditions for when Internet Monitor creates a health event for a local performance or availability issue, when scores cross a threshold for one or more city-networks.</p> <p>Defines the percentages, for performance scores or availability scores, that are the local thresholds for when Amazon CloudWatch Internet Monitor creates a health event. Also defines whether a local threshold is enabled or disabled, and the minimum percentage of overall traffic that must be impacted by an issue before Internet Monitor creates an event when a threshold is crossed for a local health score.</p> <p>If you don't set a local health event threshold, the default value is 60%.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/CloudWatch-IM-overview.html#IMUpdateThresholdFromOverview\"> Change health event thresholds</a> in the Internet Monitor section of the <i>CloudWatch User Guide</i>.</p>"}, "LocalHealthEventsConfigStatus": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "LogDeliveryStatus": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "Long": {"type": "long", "box": true}, "MaxCityNetworksToMonitor": {"type": "integer", "box": true, "max": 500000, "min": 1}, "MaxResults": {"type": "integer", "box": true, "max": 25, "min": 1}, "Monitor": {"type": "structure", "required": ["MonitorName", "MonitorArn", "Status"], "members": {"MonitorName": {"shape": "ResourceName", "documentation": "<p>The name of the monitor.</p>"}, "MonitorArn": {"shape": "MonitorArn", "documentation": "<p>The Amazon Resource Name (ARN) of the monitor.</p>"}, "Status": {"shape": "MonitorConfigState", "documentation": "<p>The status of a monitor.</p>"}, "ProcessingStatus": {"shape": "MonitorProcessingStatusCode", "documentation": "<p>The health of data processing for the monitor.</p>"}}, "documentation": "<p>The description of and information about a monitor in Amazon CloudWatch Internet Monitor. </p>"}, "MonitorArn": {"type": "string", "max": 512, "min": 20, "pattern": "arn:.*"}, "MonitorConfigState": {"type": "string", "enum": ["PENDING", "ACTIVE", "INACTIVE", "ERROR"]}, "MonitorList": {"type": "list", "member": {"shape": "Monitor"}}, "MonitorProcessingStatusCode": {"type": "string", "enum": ["OK", "INACTIVE", "COLLECTING_DATA", "INSUFFICIENT_DATA", "FAULT_SERVICE", "FAULT_ACCESS_CLOUDWATCH"]}, "Network": {"type": "structure", "required": ["ASName", "ASNumber"], "members": {"ASName": {"shape": "String", "documentation": "<p>The name of the internet service provider (ISP) or network (ASN).</p>"}, "ASNumber": {"shape": "<PERSON>", "documentation": "<p>The Autonomous System Number (ASN) of the internet provider or network.</p>"}}, "documentation": "<p>An internet service provider (ISP) or network (ASN) in Amazon CloudWatch Internet Monitor.</p>"}, "NetworkImpairment": {"type": "structure", "required": ["Networks", "<PERSON><PERSON><PERSON>", "NetworkEventType"], "members": {"Networks": {"shape": "NetworkList", "documentation": "<p>The networks that could be impacted by a network impairment event.</p>"}, "AsPath": {"shape": "NetworkList", "documentation": "<p>The combination of the Autonomous System Number (ASN) of the network and the name of the network.</p>"}, "NetworkEventType": {"shape": "TriangulationEventType", "documentation": "<p>The type of network impairment.</p>"}}, "documentation": "<p>Information about the network impairment for a specific network measured by Amazon CloudWatch Internet Monitor.</p>"}, "NetworkList": {"type": "list", "member": {"shape": "Network"}}, "NotFoundException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>The request specifies something that doesn't exist.</p>", "error": {"httpStatusCode": 404, "senderFault": true}, "exception": true}, "Operator": {"type": "string", "enum": ["EQUALS", "NOT_EQUALS"]}, "Percentage": {"type": "double", "max": 100, "min": 0}, "PerformanceMeasurement": {"type": "structure", "members": {"ExperienceScore": {"shape": "Double", "documentation": "<p>Experience scores, or health scores, are calculated for different geographic and network provider combinations (that is, different granularities) and also totaled into global scores. If you view performance or availability scores without filtering for any specific geography or service provider, Amazon CloudWatch Internet Monitor provides global health scores.</p> <p>The Amazon CloudWatch Internet Monitor chapter in the CloudWatch User Guide includes detailed information about how Internet Monitor calculates health scores, including performance and availability scores, and when it creates and resolves health events. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/CloudWatch-IM-inside-internet-monitor.html#IMExperienceScores\">How Amazon Web Services calculates performance and availability scores</a> in the Amazon CloudWatch Internet Monitor section of the <i>CloudWatch User Guide</i>.</p>"}, "PercentOfTotalTrafficImpacted": {"shape": "Double", "documentation": "<p>The impact on total traffic that a health event has, in increased latency or reduced availability. This is the percentage of how much latency has increased or availability has decreased during the event, compared to what is typical for traffic from this client location to the Amazon Web Services location using this client network.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/CloudWatch-IM-inside-internet-monitor.html#IMHealthEventStartStop\">When Amazon Web Services creates and resolves health events</a> in the Amazon CloudWatch Internet Monitor section of the <i>CloudWatch User Guide</i>.</p>"}, "PercentOfClientLocationImpacted": {"shape": "Double", "documentation": "<p>How much performance impact was caused by a health event at a client location. For performance, this is the percentage of how much latency increased during the event compared to typical performance for traffic, from this client location to an Amazon Web Services location, using a specific client network. </p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/CloudWatch-IM-inside-internet-monitor.html#IMHealthEventStartStop\">When Amazon Web Services creates and resolves health events</a> in the Amazon CloudWatch Internet Monitor section of the <i>CloudWatch User Guide</i>.</p>"}, "RoundTripTime": {"shape": "RoundTripTime", "documentation": "<p>This is the percentage of how much round-trip time increased during the event compared to typical round-trip time for your application for traffic. </p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/CloudWatch-IM-inside-internet-monitor.html#IMHealthEventStartStop\">When Amazon Web Services creates and resolves health events</a> in the Amazon CloudWatch Internet Monitor section of the <i>CloudWatch User Guide</i>.</p>"}}, "documentation": "<p>Amazon CloudWatch Internet Monitor calculates measurements about the performance for your application's internet traffic between client locations and Amazon Web Services. Amazon Web Services has substantial historical data about internet performance and availability between Amazon Web Services services and different network providers and geographies. By applying statistical analysis to the data, Internet Monitor can detect when the performance and availability for your application has dropped, compared to an estimated baseline that's already calculated. To make it easier to see those drops, we report that information to you in the form of health scores: a performance score and an availability score.</p> <p>Performance in Internet Monitor represents the estimated percentage of traffic that is not seeing a performance drop. For example, a performance score of 99% for an end user and service location pair is equivalent to 1% of the traffic experiencing a performance drop for that pair.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/CloudWatch-IM-inside-internet-monitor.html#IMExperienceScores\">How Internet Monitor calculates performance and availability scores</a> in the Amazon CloudWatch Internet Monitor section of the <i>CloudWatch User Guide</i>.</p>"}, "QueryData": {"type": "list", "member": {"shape": "QueryRow"}}, "QueryField": {"type": "structure", "members": {"Name": {"shape": "String", "documentation": "<p>The name of a field to query your application's Amazon CloudWatch Internet Monitor data for, such as <code>availability_score</code>.</p>"}, "Type": {"shape": "String", "documentation": "<p>The data type for a query field, which must correspond to the field you're defining for <code>QueryField</code>. For example, if the query field name is <code>availability_score</code>, the data type is <code>float</code>.</p>"}}, "documentation": "<p>Defines a field to query for your application's Amazon CloudWatch Internet Monitor data. You create a data repository by running a query of a specific type. Each <code>QueryType</code> includes a specific set of fields and datatypes to retrieve data for. </p>"}, "QueryFields": {"type": "list", "member": {"shape": "QueryField"}}, "QueryMaxResults": {"type": "integer", "box": true, "max": 1000, "min": 1}, "QueryRow": {"type": "list", "member": {"shape": "String"}}, "QueryStatus": {"type": "string", "enum": ["QUEUED", "RUNNING", "SUCCEEDED", "FAILED", "CANCELED"]}, "QueryType": {"type": "string", "enum": ["MEASUREMENTS", "TOP_LOCATIONS", "TOP_LOCATION_DETAILS", "OVERALL_TRAFFIC_SUGGESTIONS", "OVERALL_TRAFFIC_SUGGESTIONS_DETAILS", "ROUTING_SUGGESTIONS"]}, "ResourceName": {"type": "string", "max": 255, "min": 1, "pattern": "[a-zA-Z0-9_.-]+"}, "ResourceNotFoundException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>The request specifies a resource that doesn't exist.</p>", "error": {"httpStatusCode": 404, "senderFault": true}, "exception": true}, "RoundTripTime": {"type": "structure", "members": {"P50": {"shape": "Double", "documentation": "<p>RTT at the 50th percentile (p50).</p>"}, "P90": {"shape": "Double", "documentation": "<p>RTT at the 90th percentile (p90). </p>"}, "P95": {"shape": "Double", "documentation": "<p>RTT at the 95th percentile (p95). </p>"}}, "documentation": "<p>Round-trip time (RTT) is how long it takes for a request from the user to return a response to the user. Amazon CloudWatch Internet Monitor calculates RTT at different percentiles: p50, p90, and p95.</p>"}, "S3Config": {"type": "structure", "members": {"BucketName": {"shape": "S3ConfigBucketNameString", "documentation": "<p>The Amazon S3 bucket name.</p>"}, "BucketPrefix": {"shape": "String", "documentation": "<p>The Amazon S3 bucket prefix.</p>"}, "LogDeliveryStatus": {"shape": "LogDeliveryStatus", "documentation": "<p>The status of publishing Internet Monitor internet measurements to an Amazon S3 bucket.</p>"}}, "documentation": "<p>The configuration for publishing Amazon CloudWatch Internet Monitor internet measurements to Amazon S3. The configuration includes the bucket name and (optionally) prefix for the S3 bucket to store the measurements, and the delivery status. The delivery status is <code>ENABLED</code> or <code>DISABLED</code>, depending on whether you choose to deliver internet measurements to S3 logs.</p>"}, "S3ConfigBucketNameString": {"type": "string", "min": 3}, "SetOfARNs": {"type": "list", "member": {"shape": "<PERSON><PERSON>"}}, "StartQueryInput": {"type": "structure", "required": ["MonitorName", "StartTime", "EndTime", "QueryType"], "members": {"MonitorName": {"shape": "ResourceName", "documentation": "<p>The name of the monitor to query.</p>", "location": "uri", "locationName": "MonitorName"}, "StartTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The timestamp that is the beginning of the period that you want to retrieve data for with your query.</p>"}, "EndTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The timestamp that is the end of the period that you want to retrieve data for with your query.</p>"}, "QueryType": {"shape": "QueryType", "documentation": "<p>The type of query to run. The following are the three types of queries that you can run using the Internet Monitor query interface:</p> <ul> <li> <p> <code>MEASUREMENTS</code>: Provides availability score, performance score, total traffic, and round-trip times, at 5 minute intervals.</p> </li> <li> <p> <code>TOP_LOCATIONS</code>: Provides availability score, performance score, total traffic, and time to first byte (TTFB) information, for the top location and ASN combinations that you're monitoring, by traffic volume.</p> </li> <li> <p> <code>TOP_LOCATION_DETAILS</code>: Provides TTFB for Amazon CloudFront, your current configuration, and the best performing EC2 configuration, at 1 hour intervals.</p> </li> <li> <p> <code>OVERALL_TRAFFIC_SUGGESTIONS</code>: Provides TTFB, using a 30-day weighted average, for all traffic in each Amazon Web Services location that is monitored.</p> </li> <li> <p> <code>OVERALL_TRAFFIC_SUGGESTIONS_DETAILS</code>: Provides TTFB, using a 30-day weighted average, for each top location, for a proposed Amazon Web Services location. Must provide an Amazon Web Services location to search.</p> </li> <li> <p> <code>ROUTING_SUGGESTIONS</code>: Provides the predicted average round-trip time (RTT) from an IP prefix toward an Amazon Web Services location for a DNS resolver. The RTT is calculated at one hour intervals, over a one hour period.</p> </li> </ul> <p>For lists of the fields returned with each query type and more information about how each type of query is performed, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/CloudWatch-IM-view-cw-tools-cwim-query.html\"> Using the Amazon CloudWatch Internet Monitor query interface</a> in the Amazon CloudWatch Internet Monitor User Guide.</p>"}, "FilterParameters": {"shape": "FilterParameters", "documentation": "<p>The <code>FilterParameters</code> field that you use with Amazon CloudWatch Internet Monitor queries is a string the defines how you want a query to be filtered. The filter parameters that you can specify depend on the query type, since each query type returns a different set of Internet Monitor data.</p> <p>For more information about specifying filter parameters, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/CloudWatch-IM-view-cw-tools-cwim-query.html\">Using the Amazon CloudWatch Internet Monitor query interface</a> in the Amazon CloudWatch Internet Monitor User Guide.</p>"}, "LinkedAccountId": {"shape": "AccountId", "documentation": "<p>The account ID for an account that you've set up cross-account sharing for in Amazon CloudWatch Internet Monitor. You configure cross-account sharing by using Amazon CloudWatch Observability Access Manager. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/cwim-cross-account.html\">Internet Monitor cross-account observability</a> in the Amazon CloudWatch Internet Monitor User Guide.</p>"}}}, "StartQueryOutput": {"type": "structure", "required": ["QueryId"], "members": {"QueryId": {"shape": "String", "documentation": "<p>The internally-generated identifier of a specific query.</p>"}}}, "StopQueryInput": {"type": "structure", "required": ["MonitorName", "QueryId"], "members": {"MonitorName": {"shape": "ResourceName", "documentation": "<p>The name of the monitor.</p>", "location": "uri", "locationName": "MonitorName"}, "QueryId": {"shape": "String", "documentation": "<p>The ID of the query that you want to stop. A <code>QueryId</code> is an internally-generated identifier for a specific query.</p>", "location": "uri", "locationName": "QueryId"}}}, "StopQueryOutput": {"type": "structure", "members": {}}, "String": {"type": "string"}, "SyntheticTimestamp_date_time": {"type": "timestamp", "timestampFormat": "iso8601"}, "TagKey": {"type": "string", "max": 128, "min": 1}, "TagKeys": {"type": "list", "member": {"shape": "TagKey"}, "max": 200, "min": 0}, "TagMap": {"type": "map", "key": {"shape": "TagKey"}, "value": {"shape": "TagValue"}, "max": 200, "min": 0}, "TagResourceInput": {"type": "structure", "required": ["ResourceArn", "Tags"], "members": {"ResourceArn": {"shape": "MonitorArn", "documentation": "<p>The Amazon Resource Name (ARN) for a tag that you add to a resource. Tags are supported only for monitors in Amazon CloudWatch Internet Monitor.</p>", "location": "uri", "locationName": "ResourceArn"}, "Tags": {"shape": "TagMap", "documentation": "<p>Tags that you add to a resource. You can add a maximum of 50 tags in Internet Monitor.</p>"}}}, "TagResourceOutput": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0}, "ThrottlingException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>The request was denied due to request throttling.</p>", "error": {"httpStatusCode": 429, "senderFault": true}, "exception": true, "retryable": {"throttling": true}}, "TooManyRequestsException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>There were too many requests.</p>", "error": {"httpStatusCode": 429, "senderFault": true}, "exception": true, "retryable": {"throttling": true}}, "TrafficPercentageToMonitor": {"type": "integer", "box": true, "max": 100, "min": 1}, "TriangulationEventType": {"type": "string", "enum": ["AWS", "Internet"]}, "UntagResourceInput": {"type": "structure", "required": ["ResourceArn", "TagKeys"], "members": {"ResourceArn": {"shape": "MonitorArn", "documentation": "<p>The Amazon Resource Name (ARN) for a tag you remove a resource from.</p>", "location": "uri", "locationName": "ResourceArn"}, "TagKeys": {"shape": "TagKeys", "documentation": "<p>Tag keys that you remove from a resource.</p>", "location": "querystring", "locationName": "tagKeys"}}}, "UntagResourceOutput": {"type": "structure", "members": {}}, "UpdateMonitorInput": {"type": "structure", "required": ["MonitorName"], "members": {"MonitorName": {"shape": "ResourceName", "documentation": "<p>The name of the monitor. </p>", "location": "uri", "locationName": "MonitorName"}, "ResourcesToAdd": {"shape": "SetOfARNs", "documentation": "<p>The resources to include in a monitor, which you provide as a set of Amazon Resource Names (ARNs). Resources can be VPCs, NLBs, Amazon CloudFront distributions, or Amazon WorkSpaces directories.</p> <p>You can add a combination of VPCs and CloudFront distributions, or you can add WorkSpaces directories, or you can add NLBs. You can't add NLBs or WorkSpaces directories together with any other resources.</p> <note> <p>If you add only Amazon Virtual Private Clouds resources, at least one VPC must have an Internet Gateway attached to it, to make sure that it has internet connectivity.</p> </note>"}, "ResourcesToRemove": {"shape": "SetOfARNs", "documentation": "<p>The resources to remove from a monitor, which you provide as a set of Amazon Resource Names (ARNs).</p>"}, "Status": {"shape": "MonitorConfigState", "documentation": "<p>The status for a monitor. The accepted values for <code>Status</code> with the <code>UpdateMonitor</code> API call are the following: <code>ACTIVE</code> and <code>INACTIVE</code>. The following values are <i>not</i> accepted: <code>PENDING</code>, and <code>ERROR</code>.</p>"}, "ClientToken": {"shape": "String", "documentation": "<p>A unique, case-sensitive string of up to 64 ASCII characters that you specify to make an idempotent API request. You should not reuse the same client token for other API requests.</p>", "idempotencyToken": true}, "MaxCityNetworksToMonitor": {"shape": "MaxCityNetworksToMonitor", "documentation": "<p>The maximum number of city-networks to monitor for your application. A city-network is the location (city) where clients access your application resources from and the ASN or network provider, such as an internet service provider (ISP), that clients access the resources through. Setting this limit can help control billing costs.</p>"}, "InternetMeasurementsLogDelivery": {"shape": "InternetMeasurementsLogDelivery", "documentation": "<p>Publish internet measurements for Internet Monitor to another location, such as an Amazon S3 bucket. The measurements are also published to Amazon CloudWatch Logs.</p>"}, "TrafficPercentageToMonitor": {"shape": "TrafficPercentageToMonitor", "documentation": "<p>The percentage of the internet-facing traffic for your application that you want to monitor with this monitor. If you set a city-networks maximum, that limit overrides the traffic percentage that you set.</p> <p>To learn more, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/IMTrafficPercentage.html\">Choosing an application traffic percentage to monitor </a> in the Amazon CloudWatch Internet Monitor section of the <i>CloudWatch User Guide</i>.</p>"}, "HealthEventsConfig": {"shape": "HealthEventsConfig", "documentation": "<p>The list of health score thresholds. A threshold percentage for health scores, along with other configuration information, determines when Internet Monitor creates a health event when there's an internet issue that affects your application end users.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/CloudWatch-IM-overview.html#IMUpdateThresholdFromOverview\"> Change health event thresholds</a> in the Internet Monitor section of the <i>CloudWatch User Guide</i>.</p>"}}}, "UpdateMonitorOutput": {"type": "structure", "required": ["MonitorArn", "Status"], "members": {"MonitorArn": {"shape": "MonitorArn", "documentation": "<p>The Amazon Resource Name (ARN) of the monitor.</p>"}, "Status": {"shape": "MonitorConfigState", "documentation": "<p>The status of a monitor.</p>"}}}, "ValidationException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>Invalid request.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}}, "documentation": "<p>Amazon CloudWatch Internet Monitor provides visibility into how internet issues impact the performance and availability between your applications hosted on Amazon Web Services and your end users. It can reduce the time it takes for you to diagnose internet issues from days to minutes. Internet Monitor uses the connectivity data that Amazon Web Services captures from its global networking footprint to calculate a baseline of performance and availability for internet traffic. This is the same data that Amazon Web Services uses to monitor internet uptime and availability. With those measurements as a baseline, Internet Monitor raises awareness for you when there are significant problems for your end users in the different geographic locations where your application runs.</p> <p>Internet Monitor publishes internet measurements to CloudWatch Logs and CloudWatch Metrics, to easily support using CloudWatch tools with health information for geographies and networks specific to your application. Internet Monitor sends health events to Amazon EventBridge so that you can set up notifications. If an issue is caused by the Amazon Web Services network, you also automatically receive an Amazon Web Services Health Dashboard notification with the steps that Amazon Web Services is taking to mitigate the problem.</p> <p>To use Internet Monitor, you create a <i>monitor</i> and associate your application's resources with it - VPCs, NLBs, CloudFront distributions, or WorkSpaces directories - so Internet Monitor can determine where your application's internet traffic is. Internet Monitor then provides internet measurements from Amazon Web Services that are specific to the locations and ASNs (typically, internet service providers or ISPs) that communicate with your application.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/CloudWatch-InternetMonitor.html\">Using Amazon CloudWatch Internet Monitor</a> in the <i>Amazon CloudWatch User Guide</i>.</p>"}