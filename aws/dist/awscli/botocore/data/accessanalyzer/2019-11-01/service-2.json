{"version": "2.0", "metadata": {"apiVersion": "2019-11-01", "auth": ["aws.auth#sigv4"], "endpointPrefix": "access-analyzer", "protocol": "rest-json", "protocols": ["rest-json"], "serviceFullName": "Access Analyzer", "serviceId": "AccessAnalyzer", "signatureVersion": "v4", "signingName": "access-analyzer", "uid": "accessanalyzer-2019-11-01"}, "operations": {"ApplyArchiveRule": {"name": "ApplyArchiveRule", "http": {"method": "PUT", "requestUri": "/archive-rule", "responseCode": 200}, "input": {"shape": "ApplyArchiveRuleRequest"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retroactively applies the archive rule to existing findings that meet the archive rule criteria.</p>", "idempotent": true}, "CancelPolicyGeneration": {"name": "CancelPolicyGeneration", "http": {"method": "PUT", "requestUri": "/policy/generation/{jobId}", "responseCode": 200}, "input": {"shape": "CancelPolicyGenerationRequest"}, "output": {"shape": "CancelPolicyGenerationResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Cancels the requested policy generation.</p>", "idempotent": true}, "CheckAccessNotGranted": {"name": "CheckAccessNotGranted", "http": {"method": "POST", "requestUri": "/policy/check-access-not-granted", "responseCode": 200}, "input": {"shape": "CheckAccessNotGrantedRequest"}, "output": {"shape": "CheckAccessNotGrantedResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "InvalidParameterException"}, {"shape": "UnprocessableEntityException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Checks whether the specified access isn't allowed by a policy.</p>"}, "CheckNoNewAccess": {"name": "CheckNoNewAccess", "http": {"method": "POST", "requestUri": "/policy/check-no-new-access", "responseCode": 200}, "input": {"shape": "CheckNoNewAccessRequest"}, "output": {"shape": "CheckNoNewAccessResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "InvalidParameterException"}, {"shape": "UnprocessableEntityException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Checks whether new access is allowed for an updated policy when compared to the existing policy.</p> <p>You can find examples for reference policies and learn how to set up and run a custom policy check for new access in the <a href=\"https://github.com/aws-samples/iam-access-analyzer-custom-policy-check-samples\">IAM Access Analyzer custom policy checks samples</a> repository on GitHub. The reference policies in this repository are meant to be passed to the <code>existingPolicyDocument</code> request parameter.</p>"}, "CheckNoPublicAccess": {"name": "CheckNoPublicAccess", "http": {"method": "POST", "requestUri": "/policy/check-no-public-access", "responseCode": 200}, "input": {"shape": "CheckNoPublicAccessRequest"}, "output": {"shape": "CheckNoPublicAccessResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "InvalidParameterException"}, {"shape": "UnprocessableEntityException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Checks whether a resource policy can grant public access to the specified resource type.</p>"}, "CreateAccessPreview": {"name": "CreateAccessPreview", "http": {"method": "PUT", "requestUri": "/access-preview", "responseCode": 200}, "input": {"shape": "CreateAccessPreviewRequest"}, "output": {"shape": "CreateAccessPreviewResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Creates an access preview that allows you to preview IAM Access Analyzer findings for your resource before deploying resource permissions.</p>", "idempotent": true}, "CreateAnalyzer": {"name": "CreateAnalyzer", "http": {"method": "PUT", "requestUri": "/analyzer", "responseCode": 200}, "input": {"shape": "CreateAnalyzerRequest"}, "output": {"shape": "CreateAnalyzerResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Creates an analyzer for your account.</p>", "idempotent": true}, "CreateArchiveRule": {"name": "CreateArchiveRule", "http": {"method": "PUT", "requestUri": "/analyzer/{analyzerName}/archive-rule", "responseCode": 200}, "input": {"shape": "CreateArchiveRuleRequest"}, "errors": [{"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Creates an archive rule for the specified analyzer. Archive rules automatically archive new findings that meet the criteria you define when you create the rule.</p> <p>To learn about filter keys that you can use to create an archive rule, see <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/access-analyzer-reference-filter-keys.html\">IAM Access Analyzer filter keys</a> in the <b>IAM User Guide</b>.</p>", "idempotent": true}, "DeleteAnalyzer": {"name": "DeleteAnalyzer", "http": {"method": "DELETE", "requestUri": "/analyzer/{analyzerName}", "responseCode": 200}, "input": {"shape": "DeleteAnalyzerRequest"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Deletes the specified analyzer. When you delete an analyzer, IAM Access Analyzer is disabled for the account or organization in the current or specific Region. All findings that were generated by the analyzer are deleted. You cannot undo this action.</p>", "idempotent": true}, "DeleteArchiveRule": {"name": "DeleteArchiveRule", "http": {"method": "DELETE", "requestUri": "/analyzer/{analyzerName}/archive-rule/{ruleName}", "responseCode": 200}, "input": {"shape": "DeleteArchiveRuleRequest"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Deletes the specified archive rule.</p>", "idempotent": true}, "GenerateFindingRecommendation": {"name": "GenerateFindingRecommendation", "http": {"method": "POST", "requestUri": "/recommendation/{id}", "responseCode": 200}, "input": {"shape": "GenerateFindingRecommendationRequest"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Creates a recommendation for an unused permissions finding.</p>"}, "GetAccessPreview": {"name": "GetAccessPreview", "http": {"method": "GET", "requestUri": "/access-preview/{accessPreviewId}", "responseCode": 200}, "input": {"shape": "GetAccessPreviewRequest"}, "output": {"shape": "GetAccessPreviewResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves information about an access preview for the specified analyzer.</p>"}, "GetAnalyzedResource": {"name": "GetAnalyzedResource", "http": {"method": "GET", "requestUri": "/analyzed-resource", "responseCode": 200}, "input": {"shape": "GetAnalyzedResourceRequest"}, "output": {"shape": "GetAnalyzedResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves information about a resource that was analyzed.</p>"}, "GetAnalyzer": {"name": "GetAnalyzer", "http": {"method": "GET", "requestUri": "/analyzer/{analyzerName}", "responseCode": 200}, "input": {"shape": "GetAnalyzerRequest"}, "output": {"shape": "GetAnalyzerResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves information about the specified analyzer.</p>"}, "GetArchiveRule": {"name": "GetArchiveRule", "http": {"method": "GET", "requestUri": "/analyzer/{analyzerName}/archive-rule/{ruleName}", "responseCode": 200}, "input": {"shape": "GetArchiveRuleRequest"}, "output": {"shape": "GetArchiveRuleResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves information about an archive rule.</p> <p>To learn about filter keys that you can use to create an archive rule, see <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/access-analyzer-reference-filter-keys.html\">IAM Access Analyzer filter keys</a> in the <b>IAM User Guide</b>.</p>"}, "GetFinding": {"name": "GetFinding", "http": {"method": "GET", "requestUri": "/finding/{id}", "responseCode": 200}, "input": {"shape": "GetFindingRequest"}, "output": {"shape": "GetFindingResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves information about the specified finding. GetFinding and GetFindingV2 both use <code>access-analyzer:GetFinding</code> in the <code>Action</code> element of an IAM policy statement. You must have permission to perform the <code>access-analyzer:GetFinding</code> action.</p>"}, "GetFindingRecommendation": {"name": "GetFindingRecommendation", "http": {"method": "GET", "requestUri": "/recommendation/{id}", "responseCode": 200}, "input": {"shape": "GetFindingRecommendationRequest"}, "output": {"shape": "GetFindingRecommendationResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves information about a finding recommendation for the specified analyzer.</p>"}, "GetFindingV2": {"name": "GetFindingV2", "http": {"method": "GET", "requestUri": "/findingv2/{id}", "responseCode": 200}, "input": {"shape": "GetFindingV2Request"}, "output": {"shape": "GetFindingV2Response"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves information about the specified finding. GetFinding and GetFindingV2 both use <code>access-analyzer:GetFinding</code> in the <code>Action</code> element of an IAM policy statement. You must have permission to perform the <code>access-analyzer:GetFinding</code> action.</p>"}, "GetFindingsStatistics": {"name": "GetFindingsStatistics", "http": {"method": "POST", "requestUri": "/analyzer/findings/statistics", "responseCode": 200}, "input": {"shape": "GetFindingsStatisticsRequest"}, "output": {"shape": "GetFindingsStatisticsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves a list of aggregated finding statistics for an external access or unused access analyzer.</p>"}, "GetGeneratedPolicy": {"name": "GetGeneratedPolicy", "http": {"method": "GET", "requestUri": "/policy/generation/{jobId}", "responseCode": 200}, "input": {"shape": "GetGeneratedPolicyRequest"}, "output": {"shape": "GetGeneratedPolicyResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves the policy that was generated using <code>StartPolicyGeneration</code>. </p>"}, "ListAccessPreviewFindings": {"name": "ListAccessPreviewFindings", "http": {"method": "POST", "requestUri": "/access-preview/{accessPreviewId}", "responseCode": 200}, "input": {"shape": "ListAccessPreviewFindingsRequest"}, "output": {"shape": "ListAccessPreviewFindingsResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves a list of access preview findings generated by the specified access preview.</p>"}, "ListAccessPreviews": {"name": "ListAccessPreviews", "http": {"method": "GET", "requestUri": "/access-preview", "responseCode": 200}, "input": {"shape": "ListAccessPreviewsRequest"}, "output": {"shape": "ListAccessPreviewsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves a list of access previews for the specified analyzer.</p>"}, "ListAnalyzedResources": {"name": "ListAnalyzedResources", "http": {"method": "POST", "requestUri": "/analyzed-resource", "responseCode": 200}, "input": {"shape": "ListAnalyzedResourcesRequest"}, "output": {"shape": "ListAnalyzedResourcesResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves a list of resources of the specified type that have been analyzed by the specified analyzer.</p>"}, "ListAnalyzers": {"name": "ListAnalyzers", "http": {"method": "GET", "requestUri": "/analyzer", "responseCode": 200}, "input": {"shape": "ListAnalyzersRequest"}, "output": {"shape": "ListAnalyzersResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves a list of analyzers.</p>"}, "ListArchiveRules": {"name": "ListArchiveRules", "http": {"method": "GET", "requestUri": "/analyzer/{analyzerName}/archive-rule", "responseCode": 200}, "input": {"shape": "ListArchiveRulesRequest"}, "output": {"shape": "ListArchiveRulesResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves a list of archive rules created for the specified analyzer.</p>"}, "ListFindings": {"name": "ListFindings", "http": {"method": "POST", "requestUri": "/finding", "responseCode": 200}, "input": {"shape": "ListFindingsRequest"}, "output": {"shape": "ListFindingsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves a list of findings generated by the specified analyzer. ListFindings and ListFindingsV2 both use <code>access-analyzer:ListFindings</code> in the <code>Action</code> element of an IAM policy statement. You must have permission to perform the <code>access-analyzer:ListFindings</code> action.</p> <p>To learn about filter keys that you can use to retrieve a list of findings, see <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/access-analyzer-reference-filter-keys.html\">IAM Access Analyzer filter keys</a> in the <b>IAM User Guide</b>.</p>"}, "ListFindingsV2": {"name": "ListFindingsV2", "http": {"method": "POST", "requestUri": "/findingv2", "responseCode": 200}, "input": {"shape": "ListFindingsV2Request"}, "output": {"shape": "ListFindingsV2Response"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves a list of findings generated by the specified analyzer. ListFindings and ListFindingsV2 both use <code>access-analyzer:ListFindings</code> in the <code>Action</code> element of an IAM policy statement. You must have permission to perform the <code>access-analyzer:ListFindings</code> action.</p> <p>To learn about filter keys that you can use to retrieve a list of findings, see <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/access-analyzer-reference-filter-keys.html\">IAM Access Analyzer filter keys</a> in the <b>IAM User Guide</b>.</p>"}, "ListPolicyGenerations": {"name": "ListPolicyGenerations", "http": {"method": "GET", "requestUri": "/policy/generation", "responseCode": 200}, "input": {"shape": "ListPolicyGenerationsRequest"}, "output": {"shape": "ListPolicyGenerationsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists all of the policy generations requested in the last seven days.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves a list of tags applied to the specified resource.</p>"}, "StartPolicyGeneration": {"name": "StartPolicyGeneration", "http": {"method": "PUT", "requestUri": "/policy/generation", "responseCode": 200}, "input": {"shape": "StartPolicyGenerationRequest"}, "output": {"shape": "StartPolicyGenerationResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Starts the policy generation request.</p>", "idempotent": true}, "StartResourceScan": {"name": "StartResourceScan", "http": {"method": "POST", "requestUri": "/resource/scan", "responseCode": 200}, "input": {"shape": "StartResourceScanRequest"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Immediately starts a scan of the policies applied to the specified resource.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Adds a tag to the specified resource.</p>", "idempotent": true}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Removes a tag from the specified resource.</p>", "idempotent": true}, "UpdateAnalyzer": {"name": "UpdateAnalyzer", "http": {"method": "PUT", "requestUri": "/analyzer/{analyzerName}", "responseCode": 200}, "input": {"shape": "UpdateAnalyzerRequest"}, "output": {"shape": "UpdateAnalyzerResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Modifies the configuration of an existing analyzer.</p>", "idempotent": true}, "UpdateArchiveRule": {"name": "UpdateArchiveRule", "http": {"method": "PUT", "requestUri": "/analyzer/{analyzerName}/archive-rule/{ruleName}", "responseCode": 200}, "input": {"shape": "UpdateArchiveRuleRequest"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Updates the criteria and values for the specified archive rule.</p>", "idempotent": true}, "UpdateFindings": {"name": "UpdateF<PERSON>ings", "http": {"method": "PUT", "requestUri": "/finding", "responseCode": 200}, "input": {"shape": "UpdateFindingsRequest"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Updates the status for the specified findings.</p>", "idempotent": true}, "ValidatePolicy": {"name": "ValidatePolicy", "http": {"method": "POST", "requestUri": "/policy/validation", "responseCode": 200}, "input": {"shape": "ValidatePolicyRequest"}, "output": {"shape": "ValidatePolicyResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Requests the validation of a policy and returns a list of findings. The findings help you identify issues and provide actionable recommendations to resolve the issue and enable you to author functional policies that meet security best practices. </p>"}}, "shapes": {"Access": {"type": "structure", "members": {"actions": {"shape": "AccessActionsList", "documentation": "<p>A list of actions for the access permissions. Any strings that can be used as an action in an IAM policy can be used in the list of actions to check.</p>"}, "resources": {"shape": "AccessResourcesList", "documentation": "<p>A list of resources for the access permissions. Any strings that can be used as an Amazon Resource Name (ARN) in an IAM policy can be used in the list of resources to check. You can only use a wildcard in the portion of the ARN that specifies the resource ID.</p>"}}, "documentation": "<p>Contains information about actions and resources that define permissions to check against a policy.</p>"}, "AccessActionsList": {"type": "list", "member": {"shape": "Action"}, "max": 100, "min": 0}, "AccessCheckPolicyDocument": {"type": "string", "sensitive": true}, "AccessCheckPolicyType": {"type": "string", "enum": ["IDENTITY_POLICY", "RESOURCE_POLICY"]}, "AccessCheckResourceType": {"type": "string", "enum": ["AWS::DynamoDB::Table", "AWS::DynamoDB::Stream", "AWS::EFS::FileSystem", "AWS::OpenSearchService::Domain", "AWS::Kinesis::Stream", "AWS::Kinesis::StreamConsumer", "AWS::KMS::Key", "AWS::Lambda::Function", "AWS::S3::<PERSON><PERSON>", "AWS::S3::AccessPoint", "AWS::S3Express::DirectoryBucket", "AWS::S3::Glacier", "AWS::S3Outposts::Bucket", "AWS::S3Outposts::AccessPoint", "AWS::<PERSON>Manager::Secret", "AWS::SNS::Topic", "AWS::SQS::Queue", "AWS::IAM::AssumeRolePolicyDocument", "AWS::S3Tables::TableBucket", "AWS::ApiGateway::RestApi", "AWS::CodeArtifact::Domain", "AWS::Backup::<PERSON>upVault", "AWS::CloudTrail::Dashboard", "AWS::CloudTrail::EventDataStore", "AWS::S3Tables::Table", "AWS::S3Express::AccessPoint"]}, "AccessDeniedException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>You do not have sufficient access to perform this action.</p>", "error": {"httpStatusCode": 403, "senderFault": true}, "exception": true}, "AccessPointArn": {"type": "string", "pattern": "arn:[^:]*:s3:[^:]*:[^:]*:accesspoint/.*"}, "AccessPointPolicy": {"type": "string"}, "AccessPreview": {"type": "structure", "required": ["id", "analyzerArn", "configurations", "createdAt", "status"], "members": {"id": {"shape": "AccessPreviewId", "documentation": "<p>The unique ID for the access preview.</p>"}, "analyzerArn": {"shape": "AnalyzerArn", "documentation": "<p>The ARN of the analyzer used to generate the access preview.</p>"}, "configurations": {"shape": "ConfigurationsMap", "documentation": "<p>A map of resource ARNs for the proposed resource configuration.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The time at which the access preview was created.</p>"}, "status": {"shape": "AccessPreviewStatus", "documentation": "<p>The status of the access preview.</p> <ul> <li> <p> <code>Creating</code> - The access preview creation is in progress.</p> </li> <li> <p> <code>Completed</code> - The access preview is complete. You can preview findings for external access to the resource.</p> </li> <li> <p> <code>Failed</code> - The access preview creation has failed.</p> </li> </ul>"}, "statusReason": {"shape": "AccessPreviewStatusReason", "documentation": "<p>Provides more details about the current status of the access preview.</p> <p>For example, if the creation of the access preview fails, a <code>Failed</code> status is returned. This failure can be due to an internal issue with the analysis or due to an invalid resource configuration.</p>"}}, "documentation": "<p>Contains information about an access preview.</p>"}, "AccessPreviewFinding": {"type": "structure", "required": ["id", "resourceType", "createdAt", "changeType", "status", "resourceOwnerAccount"], "members": {"id": {"shape": "AccessPreviewFindingId", "documentation": "<p>The ID of the access preview finding. This ID uniquely identifies the element in the list of access preview findings and is not related to the finding ID in Access Analyzer.</p>"}, "existingFindingId": {"shape": "FindingId", "documentation": "<p>The existing ID of the finding in IAM Access Analyzer, provided only for existing findings.</p>"}, "existingFindingStatus": {"shape": "FindingStatus", "documentation": "<p>The existing status of the finding, provided only for existing findings.</p>"}, "principal": {"shape": "PrincipalMap", "documentation": "<p>The external principal that has access to a resource within the zone of trust.</p>"}, "action": {"shape": "ActionList", "documentation": "<p>The action in the analyzed policy statement that an external principal has permission to perform.</p>"}, "condition": {"shape": "ConditionKeyMap", "documentation": "<p>The condition in the analyzed policy statement that resulted in a finding.</p>"}, "resource": {"shape": "String", "documentation": "<p>The resource that an external principal has access to. This is the resource associated with the access preview.</p>"}, "isPublic": {"shape": "Boolean", "documentation": "<p>Indicates whether the policy that generated the finding allows public access to the resource.</p>"}, "resourceType": {"shape": "ResourceType", "documentation": "<p>The type of the resource that can be accessed in the finding.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The time at which the access preview finding was created.</p>"}, "changeType": {"shape": "FindingChangeType", "documentation": "<p>Provides context on how the access preview finding compares to existing access identified in IAM Access Analyzer.</p> <ul> <li> <p> <code>New</code> - The finding is for newly-introduced access.</p> </li> <li> <p> <code>Unchanged</code> - The preview finding is an existing finding that would remain unchanged.</p> </li> <li> <p> <code>Changed</code> - The preview finding is an existing finding with a change in status.</p> </li> </ul> <p>For example, a <code>Changed</code> finding with preview status <code>Resolved</code> and existing status <code>Active</code> indicates the existing <code>Active</code> finding would become <code>Resolved</code> as a result of the proposed permissions change.</p>"}, "status": {"shape": "FindingStatus", "documentation": "<p>The preview status of the finding. This is what the status of the finding would be after permissions deployment. For example, a <code>Changed</code> finding with preview status <code>Resolved</code> and existing status <code>Active</code> indicates the existing <code>Active</code> finding would become <code>Resolved</code> as a result of the proposed permissions change.</p>"}, "resourceOwnerAccount": {"shape": "String", "documentation": "<p>The Amazon Web Services account ID that owns the resource. For most Amazon Web Services resources, the owning account is the account in which the resource was created.</p>"}, "error": {"shape": "String", "documentation": "<p>An error.</p>"}, "sources": {"shape": "FindingSourceList", "documentation": "<p>The sources of the finding. This indicates how the access that generated the finding is granted. It is populated for Amazon S3 bucket findings.</p>"}, "resourceControlPolicyRestriction": {"shape": "ResourceControlPolicyRestriction", "documentation": "<p>The type of restriction applied to the finding by the resource owner with an Organizations resource control policy (RCP).</p>"}}, "documentation": "<p>An access preview finding generated by the access preview.</p>"}, "AccessPreviewFindingId": {"type": "string"}, "AccessPreviewFindingsList": {"type": "list", "member": {"shape": "AccessPreviewFinding"}}, "AccessPreviewId": {"type": "string", "pattern": "[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}"}, "AccessPreviewStatus": {"type": "string", "enum": ["COMPLETED", "CREATING", "FAILED"]}, "AccessPreviewStatusReason": {"type": "structure", "required": ["code"], "members": {"code": {"shape": "AccessPreviewStatusReasonCode", "documentation": "<p>The reason code for the current status of the access preview.</p>"}}, "documentation": "<p>Provides more details about the current status of the access preview. For example, if the creation of the access preview fails, a <code>Failed</code> status is returned. This failure can be due to an internal issue with the analysis or due to an invalid proposed resource configuration.</p>"}, "AccessPreviewStatusReasonCode": {"type": "string", "enum": ["INTERNAL_ERROR", "INVALID_CONFIGURATION"]}, "AccessPreviewSummary": {"type": "structure", "required": ["id", "analyzerArn", "createdAt", "status"], "members": {"id": {"shape": "AccessPreviewId", "documentation": "<p>The unique ID for the access preview.</p>"}, "analyzerArn": {"shape": "AnalyzerArn", "documentation": "<p>The ARN of the analyzer used to generate the access preview.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The time at which the access preview was created.</p>"}, "status": {"shape": "AccessPreviewStatus", "documentation": "<p>The status of the access preview.</p> <ul> <li> <p> <code>Creating</code> - The access preview creation is in progress.</p> </li> <li> <p> <code>Completed</code> - The access preview is complete and previews the findings for external access to the resource.</p> </li> <li> <p> <code>Failed</code> - The access preview creation has failed.</p> </li> </ul>"}, "statusReason": {"shape": "AccessPreviewStatusReason"}}, "documentation": "<p>Contains a summary of information about an access preview.</p>"}, "AccessPreviewsList": {"type": "list", "member": {"shape": "AccessPreviewSummary"}}, "AccessResourcesList": {"type": "list", "member": {"shape": "Resource"}, "max": 100, "min": 0}, "AccountAggregations": {"type": "list", "member": {"shape": "FindingAggregationAccountDetails"}, "max": 10, "min": 1}, "AccountIdsList": {"type": "list", "member": {"shape": "String"}}, "AclCanonicalId": {"type": "string"}, "AclGrantee": {"type": "structure", "members": {"id": {"shape": "AclCanonicalId", "documentation": "<p>The value specified is the canonical user ID of an Amazon Web Services account.</p>"}, "uri": {"shape": "AclUri", "documentation": "<p>Used for granting permissions to a predefined group.</p>"}}, "documentation": "<p>You specify each grantee as a type-value pair using one of these types. You can specify only one type of grantee. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_PutBucketAcl.html\">PutBucketAcl</a>.</p>", "union": true}, "AclPermission": {"type": "string", "enum": ["READ", "WRITE", "READ_ACP", "WRITE_ACP", "FULL_CONTROL"]}, "AclUri": {"type": "string"}, "Action": {"type": "string"}, "ActionList": {"type": "list", "member": {"shape": "String"}}, "AnalysisRule": {"type": "structure", "members": {"exclusions": {"shape": "AnalysisRuleCriteriaList", "documentation": "<p>A list of rules for the analyzer containing criteria to exclude from analysis. Entities that meet the rule criteria will not generate findings.</p>"}}, "documentation": "<p>Contains information about analysis rules for the analyzer. Analysis rules determine which entities will generate findings based on the criteria you define when you create the rule.</p>"}, "AnalysisRuleCriteria": {"type": "structure", "members": {"accountIds": {"shape": "AccountIdsList", "documentation": "<p>A list of Amazon Web Services account IDs to apply to the analysis rule criteria. The accounts cannot include the organization analyzer owner account. Account IDs can only be applied to the analysis rule criteria for organization-level analyzers. The list cannot include more than 2,000 account IDs.</p>"}, "resourceTags": {"shape": "TagsList", "documentation": "<p>An array of key-value pairs to match for your resources. You can use the set of Unicode letters, digits, whitespace, <code>_</code>, <code>.</code>, <code>/</code>, <code>=</code>, <code>+</code>, and <code>-</code>.</p> <p>For the tag key, you can specify a value that is 1 to 128 characters in length and cannot be prefixed with <code>aws:</code>.</p> <p>For the tag value, you can specify a value that is 0 to 256 characters in length. If the specified tag value is 0 characters, the rule is applied to all principals with the specified tag key.</p>"}}, "documentation": "<p>The criteria for an analysis rule for an analyzer. The criteria determine which entities will generate findings.</p>"}, "AnalysisRuleCriteriaList": {"type": "list", "member": {"shape": "AnalysisRuleCriteria"}}, "AnalyzedResource": {"type": "structure", "required": ["resourceArn", "resourceType", "createdAt", "analyzedAt", "updatedAt", "isPublic", "resourceOwnerAccount"], "members": {"resourceArn": {"shape": "ResourceArn", "documentation": "<p>The ARN of the resource that was analyzed.</p>"}, "resourceType": {"shape": "ResourceType", "documentation": "<p>The type of the resource that was analyzed.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The time at which the finding was created.</p>"}, "analyzedAt": {"shape": "Timestamp", "documentation": "<p>The time at which the resource was analyzed.</p>"}, "updatedAt": {"shape": "Timestamp", "documentation": "<p>The time at which the finding was updated.</p>"}, "isPublic": {"shape": "Boolean", "documentation": "<p>Indicates whether the policy that generated the finding grants public access to the resource.</p>"}, "actions": {"shape": "ActionList", "documentation": "<p>The actions that an external principal is granted permission to use by the policy that generated the finding.</p>"}, "sharedVia": {"shape": "SharedViaList", "documentation": "<p>Indicates how the access that generated the finding is granted. This is populated for Amazon S3 bucket findings.</p>"}, "status": {"shape": "FindingStatus", "documentation": "<p>The current status of the finding generated from the analyzed resource.</p>"}, "resourceOwnerAccount": {"shape": "String", "documentation": "<p>The Amazon Web Services account ID that owns the resource.</p>"}, "error": {"shape": "String", "documentation": "<p>An error message.</p>"}}, "documentation": "<p>Contains details about the analyzed resource.</p>"}, "AnalyzedResourceSummary": {"type": "structure", "required": ["resourceArn", "resourceOwnerAccount", "resourceType"], "members": {"resourceArn": {"shape": "ResourceArn", "documentation": "<p>The ARN of the analyzed resource.</p>"}, "resourceOwnerAccount": {"shape": "String", "documentation": "<p>The Amazon Web Services account ID that owns the resource.</p>"}, "resourceType": {"shape": "ResourceType", "documentation": "<p>The type of resource that was analyzed.</p>"}}, "documentation": "<p>Contains the ARN of the analyzed resource.</p>"}, "AnalyzedResourcesList": {"type": "list", "member": {"shape": "AnalyzedResourceSummary"}}, "AnalyzerArn": {"type": "string", "pattern": "[^:]*:[^:]*:[^:]*:[^:]*:[^:]*:analyzer/.{1,255}"}, "AnalyzerConfiguration": {"type": "structure", "members": {"unusedAccess": {"shape": "UnusedAccessConfiguration", "documentation": "<p>Specifies the configuration of an unused access analyzer for an Amazon Web Services organization or account.</p>"}, "internalAccess": {"shape": "InternalAccessConfiguration", "documentation": "<p>Specifies the configuration of an internal access analyzer for an Amazon Web Services organization or account. This configuration determines how the analyzer evaluates access within your Amazon Web Services environment.</p>"}}, "documentation": "<p>Contains information about the configuration of an analyzer for an Amazon Web Services organization or account.</p>", "union": true}, "AnalyzerStatus": {"type": "string", "enum": ["ACTIVE", "CREATING", "DISABLED", "FAILED"]}, "AnalyzerSummary": {"type": "structure", "required": ["arn", "name", "type", "createdAt", "status"], "members": {"arn": {"shape": "AnalyzerArn", "documentation": "<p>The ARN of the analyzer.</p>"}, "name": {"shape": "Name", "documentation": "<p>The name of the analyzer.</p>"}, "type": {"shape": "Type", "documentation": "<p>The type of analyzer, which corresponds to the zone of trust chosen for the analyzer.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>A timestamp for the time at which the analyzer was created.</p>"}, "lastResourceAnalyzed": {"shape": "String", "documentation": "<p>The resource that was most recently analyzed by the analyzer.</p>"}, "lastResourceAnalyzedAt": {"shape": "Timestamp", "documentation": "<p>The time at which the most recently analyzed resource was analyzed.</p>"}, "tags": {"shape": "TagsMap", "documentation": "<p>The tags added to the analyzer.</p>"}, "status": {"shape": "AnalyzerStatus", "documentation": "<p>The status of the analyzer. An <code>Active</code> analyzer successfully monitors supported resources and generates new findings. The analyzer is <code>Disabled</code> when a user action, such as removing trusted access for Identity and Access Management Access Analyzer from Organizations, causes the analyzer to stop generating new findings. The status is <code>Creating</code> when the analyzer creation is in progress and <code>Failed</code> when the analyzer creation has failed. </p>"}, "statusReason": {"shape": "StatusReason", "documentation": "<p>The <code>statusReason</code> provides more details about the current status of the analyzer. For example, if the creation for the analyzer fails, a <code>Failed</code> status is returned. For an analyzer with organization as the type, this failure can be due to an issue with creating the service-linked roles required in the member accounts of the Amazon Web Services organization.</p>"}, "configuration": {"shape": "AnalyzerConfiguration", "documentation": "<p>Specifies if the analyzer is an external access, unused access, or internal access analyzer.</p>"}}, "documentation": "<p>Contains information about the analyzer.</p>"}, "AnalyzersList": {"type": "list", "member": {"shape": "AnalyzerSummary"}}, "ApplyArchiveRuleRequest": {"type": "structure", "required": ["analyzerArn", "ruleName"], "members": {"analyzerArn": {"shape": "AnalyzerArn", "documentation": "<p>The Amazon resource name (ARN) of the analyzer.</p>"}, "ruleName": {"shape": "Name", "documentation": "<p>The name of the rule to apply.</p>"}, "clientToken": {"shape": "String", "documentation": "<p>A client token.</p>", "idempotencyToken": true}}, "documentation": "<p>Retroactively applies an archive rule.</p>"}, "ArchiveRuleSummary": {"type": "structure", "required": ["ruleName", "filter", "createdAt", "updatedAt"], "members": {"ruleName": {"shape": "Name", "documentation": "<p>The name of the archive rule.</p>"}, "filter": {"shape": "FilterCriteriaMap", "documentation": "<p>A filter used to define the archive rule.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The time at which the archive rule was created.</p>"}, "updatedAt": {"shape": "Timestamp", "documentation": "<p>The time at which the archive rule was last updated.</p>"}}, "documentation": "<p>Contains information about an archive rule. Archive rules automatically archive new findings that meet the criteria you define when you create the rule.</p>"}, "ArchiveRulesList": {"type": "list", "member": {"shape": "ArchiveRuleSummary"}}, "Boolean": {"type": "boolean", "box": true}, "CancelPolicyGenerationRequest": {"type": "structure", "required": ["jobId"], "members": {"jobId": {"shape": "JobId", "documentation": "<p>The <code>JobId</code> that is returned by the <code>StartPolicyGeneration</code> operation. The <code>JobId</code> can be used with <code>GetGeneratedPolicy</code> to retrieve the generated policies or used with <code>CancelPolicyGeneration</code> to cancel the policy generation request.</p>", "location": "uri", "locationName": "jobId"}}}, "CancelPolicyGenerationResponse": {"type": "structure", "members": {}}, "CheckAccessNotGrantedRequest": {"type": "structure", "required": ["policyDocument", "access", "policyType"], "members": {"policyDocument": {"shape": "AccessCheckPolicyDocument", "documentation": "<p>The JSON policy document to use as the content for the policy.</p>"}, "access": {"shape": "CheckAccessNotGrantedRequestAccessList", "documentation": "<p>An access object containing the permissions that shouldn't be granted by the specified policy. If only actions are specified, IAM Access Analyzer checks for access to peform at least one of the actions on any resource in the policy. If only resources are specified, then IAM Access Analyzer checks for access to perform any action on at least one of the resources. If both actions and resources are specified, IAM Access Analyzer checks for access to perform at least one of the specified actions on at least one of the specified resources.</p>"}, "policyType": {"shape": "AccessCheckPolicyType", "documentation": "<p>The type of policy. Identity policies grant permissions to IAM principals. Identity policies include managed and inline policies for IAM roles, users, and groups.</p> <p>Resource policies grant permissions on Amazon Web Services resources. Resource policies include trust policies for IAM roles and bucket policies for Amazon S3 buckets.</p>"}}}, "CheckAccessNotGrantedRequestAccessList": {"type": "list", "member": {"shape": "Access"}, "max": 1, "min": 0}, "CheckAccessNotGrantedResponse": {"type": "structure", "members": {"result": {"shape": "CheckAccessNotGrantedResult", "documentation": "<p>The result of the check for whether the access is allowed. If the result is <code>PASS</code>, the specified policy doesn't allow any of the specified permissions in the access object. If the result is <code>FAIL</code>, the specified policy might allow some or all of the permissions in the access object.</p>"}, "message": {"shape": "String", "documentation": "<p>The message indicating whether the specified access is allowed.</p>"}, "reasons": {"shape": "ReasonSummaryList", "documentation": "<p>A description of the reasoning of the result.</p>"}}}, "CheckAccessNotGrantedResult": {"type": "string", "enum": ["PASS", "FAIL"]}, "CheckNoNewAccessRequest": {"type": "structure", "required": ["newPolicyDocument", "existingPolicyDocument", "policyType"], "members": {"newPolicyDocument": {"shape": "AccessCheckPolicyDocument", "documentation": "<p>The JSON policy document to use as the content for the updated policy.</p>"}, "existingPolicyDocument": {"shape": "AccessCheckPolicyDocument", "documentation": "<p>The JSON policy document to use as the content for the existing policy.</p>"}, "policyType": {"shape": "AccessCheckPolicyType", "documentation": "<p>The type of policy to compare. Identity policies grant permissions to IAM principals. Identity policies include managed and inline policies for IAM roles, users, and groups.</p> <p>Resource policies grant permissions on Amazon Web Services resources. Resource policies include trust policies for IAM roles and bucket policies for Amazon S3 buckets. You can provide a generic input such as identity policy or resource policy or a specific input such as managed policy or Amazon S3 bucket policy.</p>"}}}, "CheckNoNewAccessResponse": {"type": "structure", "members": {"result": {"shape": "CheckNoNewAccessResult", "documentation": "<p>The result of the check for new access. If the result is <code>PASS</code>, no new access is allowed by the updated policy. If the result is <code>FAIL</code>, the updated policy might allow new access.</p>"}, "message": {"shape": "String", "documentation": "<p>The message indicating whether the updated policy allows new access.</p>"}, "reasons": {"shape": "ReasonSummaryList", "documentation": "<p>A description of the reasoning of the result.</p>"}}}, "CheckNoNewAccessResult": {"type": "string", "enum": ["PASS", "FAIL"]}, "CheckNoPublicAccessRequest": {"type": "structure", "required": ["policyDocument", "resourceType"], "members": {"policyDocument": {"shape": "AccessCheckPolicyDocument", "documentation": "<p>The JSON policy document to evaluate for public access.</p>"}, "resourceType": {"shape": "AccessCheckResourceType", "documentation": "<p>The type of resource to evaluate for public access. For example, to check for public access to Amazon S3 buckets, you can choose <code>AWS::S3::Bucket</code> for the resource type.</p> <p>For resource types not supported as valid values, IAM Access Analyzer will return an error.</p>"}}}, "CheckNoPublicAccessResponse": {"type": "structure", "members": {"result": {"shape": "CheckNoPublicAccessResult", "documentation": "<p>The result of the check for public access to the specified resource type. If the result is <code>PASS</code>, the policy doesn't allow public access to the specified resource type. If the result is <code>FAIL</code>, the policy might allow public access to the specified resource type.</p>"}, "message": {"shape": "String", "documentation": "<p>The message indicating whether the specified policy allows public access to resources.</p>"}, "reasons": {"shape": "ReasonSummaryList", "documentation": "<p>A list of reasons why the specified resource policy grants public access for the resource type.</p>"}}}, "CheckNoPublicAccessResult": {"type": "string", "enum": ["PASS", "FAIL"]}, "CloudTrailArn": {"type": "string", "pattern": "arn:[^:]*:cloudtrail:[^:]*:[^:]*:trail/.{1,576}"}, "CloudTrailDetails": {"type": "structure", "required": ["trails", "accessRole", "startTime"], "members": {"trails": {"shape": "TrailList", "documentation": "<p>A <code>Trail</code> object that contains settings for a trail.</p>"}, "accessRole": {"shape": "RoleArn", "documentation": "<p>The ARN of the service role that IAM Access Analyzer uses to access your CloudTrail trail and service last accessed information.</p>"}, "startTime": {"shape": "Timestamp", "documentation": "<p>The start of the time range for which IAM Access Analyzer reviews your CloudTrail events. Events with a timestamp before this time are not considered to generate a policy.</p>"}, "endTime": {"shape": "Timestamp", "documentation": "<p>The end of the time range for which IAM Access Analyzer reviews your CloudTrail events. Events with a timestamp after this time are not considered to generate a policy. If this is not included in the request, the default value is the current time.</p>"}}, "documentation": "<p>Contains information about CloudTrail access.</p>"}, "CloudTrailProperties": {"type": "structure", "required": ["trailProperties", "startTime", "endTime"], "members": {"trailProperties": {"shape": "TrailPropertiesList", "documentation": "<p>A <code>TrailProperties</code> object that contains settings for trail properties.</p>"}, "startTime": {"shape": "Timestamp", "documentation": "<p>The start of the time range for which IAM Access Analyzer reviews your CloudTrail events. Events with a timestamp before this time are not considered to generate a policy.</p>"}, "endTime": {"shape": "Timestamp", "documentation": "<p>The end of the time range for which IAM Access Analyzer reviews your CloudTrail events. Events with a timestamp after this time are not considered to generate a policy. If this is not included in the request, the default value is the current time.</p>"}}, "documentation": "<p>Contains information about CloudTrail access.</p>"}, "ConditionKeyMap": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "String"}}, "Configuration": {"type": "structure", "members": {"ebsSnapshot": {"shape": "EbsSnapshotConfiguration", "documentation": "<p>The access control configuration is for an Amazon EBS volume snapshot.</p>"}, "ecrRepository": {"shape": "EcrRepositoryConfiguration", "documentation": "<p>The access control configuration is for an Amazon ECR repository.</p>"}, "iamRole": {"shape": "IamRoleConfiguration", "documentation": "<p>The access control configuration is for an IAM role. </p>"}, "efsFileSystem": {"shape": "EfsFileSystemConfiguration", "documentation": "<p>The access control configuration is for an Amazon EFS file system.</p>"}, "kmsKey": {"shape": "KmsKeyConfiguration", "documentation": "<p>The access control configuration is for a KMS key. </p>"}, "rdsDbClusterSnapshot": {"shape": "RdsDbClusterSnapshotConfiguration", "documentation": "<p>The access control configuration is for an Amazon RDS DB cluster snapshot.</p>"}, "rdsDbSnapshot": {"shape": "RdsDbSnapshotConfiguration", "documentation": "<p>The access control configuration is for an Amazon RDS DB snapshot.</p>"}, "secretsManagerSecret": {"shape": "SecretsManagerSecretConfiguration", "documentation": "<p>The access control configuration is for a Secrets Manager secret.</p>"}, "s3Bucket": {"shape": "S3BucketConfiguration", "documentation": "<p>The access control configuration is for an Amazon S3 bucket. </p>"}, "snsTopic": {"shape": "SnsTopicConfiguration", "documentation": "<p>The access control configuration is for an Amazon SNS topic</p>"}, "sqsQueue": {"shape": "SqsQueueConfiguration", "documentation": "<p>The access control configuration is for an Amazon SQS queue. </p>"}, "s3ExpressDirectoryBucket": {"shape": "S3ExpressDirectoryBucketConfiguration", "documentation": "<p>The access control configuration is for an Amazon S3 directory bucket.</p>"}, "dynamodbStream": {"shape": "DynamodbStreamConfiguration", "documentation": "<p>The access control configuration is for a DynamoDB stream.</p>"}, "dynamodbTable": {"shape": "DynamodbTableConfiguration", "documentation": "<p>The access control configuration is for a DynamoDB table or index.</p>"}}, "documentation": "<p>Access control configuration structures for your resource. You specify the configuration as a type-value pair. You can specify only one type of access control configuration.</p>", "union": true}, "ConfigurationsMap": {"type": "map", "key": {"shape": "ConfigurationsMapKey"}, "value": {"shape": "Configuration"}}, "ConfigurationsMapKey": {"type": "string"}, "ConflictException": {"type": "structure", "required": ["message", "resourceId", "resourceType"], "members": {"message": {"shape": "String"}, "resourceId": {"shape": "String", "documentation": "<p>The ID of the resource.</p>"}, "resourceType": {"shape": "String", "documentation": "<p>The resource type.</p>"}}, "documentation": "<p>A conflict exception error.</p>", "error": {"httpStatusCode": 409, "senderFault": true}, "exception": true}, "CreateAccessPreviewRequest": {"type": "structure", "required": ["analyzerArn", "configurations"], "members": {"analyzerArn": {"shape": "AnalyzerArn", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/access-analyzer-getting-started.html#permission-resources\">ARN of the account analyzer</a> used to generate the access preview. You can only create an access preview for analyzers with an <code>Account</code> type and <code>Active</code> status.</p>"}, "configurations": {"shape": "ConfigurationsMap", "documentation": "<p>Access control configuration for your resource that is used to generate the access preview. The access preview includes findings for external access allowed to the resource with the proposed access control configuration. The configuration must contain exactly one element.</p>"}, "clientToken": {"shape": "String", "documentation": "<p>A client token.</p>", "idempotencyToken": true}}}, "CreateAccessPreviewResponse": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "AccessPreviewId", "documentation": "<p>The unique ID for the access preview.</p>"}}}, "CreateAnalyzerRequest": {"type": "structure", "required": ["analyzerName", "type"], "members": {"analyzerName": {"shape": "Name", "documentation": "<p>The name of the analyzer to create.</p>"}, "type": {"shape": "Type", "documentation": "<p>The type of analyzer to create. You can create only one analyzer per account per Region. You can create up to 5 analyzers per organization per Region.</p>"}, "archiveRules": {"shape": "InlineArchiveRulesList", "documentation": "<p>Specifies the archive rules to add for the analyzer. Archive rules automatically archive findings that meet the criteria you define for the rule.</p>"}, "tags": {"shape": "TagsMap", "documentation": "<p>An array of key-value pairs to apply to the analyzer. You can use the set of Unicode letters, digits, whitespace, <code>_</code>, <code>.</code>, <code>/</code>, <code>=</code>, <code>+</code>, and <code>-</code>.</p> <p>For the tag key, you can specify a value that is 1 to 128 characters in length and cannot be prefixed with <code>aws:</code>.</p> <p>For the tag value, you can specify a value that is 0 to 256 characters in length.</p>"}, "clientToken": {"shape": "String", "documentation": "<p>A client token.</p>", "idempotencyToken": true}, "configuration": {"shape": "AnalyzerConfiguration", "documentation": "<p>Specifies the configuration of the analyzer. If the analyzer is an unused access analyzer, the specified scope of unused access is used for the configuration. If the analyzer is an internal access analyzer, the specified internal access analysis rules are used for the configuration.</p>"}}, "documentation": "<p>Creates an analyzer.</p>"}, "CreateAnalyzerResponse": {"type": "structure", "members": {"arn": {"shape": "AnalyzerArn", "documentation": "<p>The ARN of the analyzer that was created by the request.</p>"}}, "documentation": "<p>The response to the request to create an analyzer.</p>"}, "CreateArchiveRuleRequest": {"type": "structure", "required": ["analyzerName", "ruleName", "filter"], "members": {"analyzerName": {"shape": "Name", "documentation": "<p>The name of the created analyzer.</p>", "location": "uri", "locationName": "analyzerName"}, "ruleName": {"shape": "Name", "documentation": "<p>The name of the rule to create.</p>"}, "filter": {"shape": "FilterCriteriaMap", "documentation": "<p>The criteria for the rule.</p>"}, "clientToken": {"shape": "String", "documentation": "<p>A client token.</p>", "idempotencyToken": true}}, "documentation": "<p>Creates an archive rule.</p>"}, "Criterion": {"type": "structure", "members": {"eq": {"shape": "ValueList", "documentation": "<p>An \"equals\" operator to match for the filter used to create the rule.</p>"}, "neq": {"shape": "ValueList", "documentation": "<p>A \"not equals\" operator to match for the filter used to create the rule.</p>"}, "contains": {"shape": "ValueList", "documentation": "<p>A \"contains\" operator to match for the filter used to create the rule.</p>"}, "exists": {"shape": "Boolean", "documentation": "<p>An \"exists\" operator to match for the filter used to create the rule. </p>"}}, "documentation": "<p>The criteria to use in the filter that defines the archive rule. For more information on available filter keys, see <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/access-analyzer-reference-filter-keys.html\">IAM Access Analyzer filter keys</a>.</p>"}, "DeleteAnalyzerRequest": {"type": "structure", "required": ["analyzerName"], "members": {"analyzerName": {"shape": "Name", "documentation": "<p>The name of the analyzer to delete.</p>", "location": "uri", "locationName": "analyzerName"}, "clientToken": {"shape": "String", "documentation": "<p>A client token.</p>", "idempotencyToken": true, "location": "querystring", "locationName": "clientToken"}}, "documentation": "<p>Deletes an analyzer.</p>"}, "DeleteArchiveRuleRequest": {"type": "structure", "required": ["analyzerName", "ruleName"], "members": {"analyzerName": {"shape": "Name", "documentation": "<p>The name of the analyzer that associated with the archive rule to delete.</p>", "location": "uri", "locationName": "analyzerName"}, "ruleName": {"shape": "Name", "documentation": "<p>The name of the rule to delete.</p>", "location": "uri", "locationName": "ruleName"}, "clientToken": {"shape": "String", "documentation": "<p>A client token.</p>", "idempotencyToken": true, "location": "querystring", "locationName": "clientToken"}}, "documentation": "<p>Deletes an archive rule.</p>"}, "DynamodbStreamConfiguration": {"type": "structure", "members": {"streamPolicy": {"shape": "DynamodbStreamPolicy", "documentation": "<p>The proposed resource policy defining who can access or manage the DynamoDB stream.</p>"}}, "documentation": "<p>The proposed access control configuration for a DynamoDB stream. You can propose a configuration for a new DynamoDB stream or an existing DynamoDB stream that you own by specifying the policy for the DynamoDB stream. For more information, see <a href=\"https://docs.aws.amazon.com/amazondynamodb/latest/APIReference/API_PutResourcePolicy.html\">PutResourcePolicy</a>.</p> <ul> <li> <p>If the configuration is for an existing DynamoDB stream and you do not specify the DynamoDB policy, then the access preview uses the existing DynamoDB policy for the stream.</p> </li> <li> <p>If the access preview is for a new resource and you do not specify the policy, then the access preview assumes a DynamoDB stream without a policy.</p> </li> <li> <p>To propose deletion of an existing DynamoDB stream policy, you can specify an empty string for the DynamoDB policy.</p> </li> </ul>"}, "DynamodbStreamPolicy": {"type": "string"}, "DynamodbTableConfiguration": {"type": "structure", "members": {"tablePolicy": {"shape": "DynamodbTablePolicy", "documentation": "<p>The proposed resource policy defining who can access or manage the DynamoDB table.</p>"}}, "documentation": "<p>The proposed access control configuration for a DynamoDB table or index. You can propose a configuration for a new DynamoDB table or index or an existing DynamoDB table or index that you own by specifying the policy for the DynamoDB table or index. For more information, see <a href=\"https://docs.aws.amazon.com/amazondynamodb/latest/APIReference/API_PutResourcePolicy.html\">PutResourcePolicy</a>.</p> <ul> <li> <p>If the configuration is for an existing DynamoDB table or index and you do not specify the DynamoDB policy, then the access preview uses the existing DynamoDB policy for the table or index.</p> </li> <li> <p>If the access preview is for a new resource and you do not specify the policy, then the access preview assumes a DynamoDB table without a policy.</p> </li> <li> <p>To propose deletion of an existing DynamoDB table or index policy, you can specify an empty string for the DynamoDB policy.</p> </li> </ul>"}, "DynamodbTablePolicy": {"type": "string"}, "EbsGroup": {"type": "string"}, "EbsGroupList": {"type": "list", "member": {"shape": "EbsGroup"}}, "EbsSnapshotConfiguration": {"type": "structure", "members": {"userIds": {"shape": "EbsUserIdList", "documentation": "<p>The IDs of the Amazon Web Services accounts that have access to the Amazon EBS volume snapshot.</p> <ul> <li> <p>If the configuration is for an existing Amazon EBS volume snapshot and you do not specify the <code>userIds</code>, then the access preview uses the existing shared <code>userIds</code> for the snapshot.</p> </li> <li> <p>If the access preview is for a new resource and you do not specify the <code>userIds</code>, then the access preview considers the snapshot without any <code>userIds</code>.</p> </li> <li> <p>To propose deletion of existing shared <code>accountIds</code>, you can specify an empty list for <code>userIds</code>.</p> </li> </ul>"}, "groups": {"shape": "EbsGroupList", "documentation": "<p>The groups that have access to the Amazon EBS volume snapshot. If the value <code>all</code> is specified, then the Amazon EBS volume snapshot is public.</p> <ul> <li> <p>If the configuration is for an existing Amazon EBS volume snapshot and you do not specify the <code>groups</code>, then the access preview uses the existing shared <code>groups</code> for the snapshot.</p> </li> <li> <p>If the access preview is for a new resource and you do not specify the <code>groups</code>, then the access preview considers the snapshot without any <code>groups</code>.</p> </li> <li> <p>To propose deletion of existing shared <code>groups</code>, you can specify an empty list for <code>groups</code>.</p> </li> </ul>"}, "kmsKeyId": {"shape": "EbsSnapshotDataEncryptionKeyId", "documentation": "<p>The KMS key identifier for an encrypted Amazon EBS volume snapshot. The KMS key identifier is the key ARN, key ID, alias <PERSON>N, or alias name for the KMS key.</p> <ul> <li> <p>If the configuration is for an existing Amazon EBS volume snapshot and you do not specify the <code>kmsKeyId</code>, or you specify an empty string, then the access preview uses the existing <code>kmsKeyId</code> of the snapshot.</p> </li> <li> <p>If the access preview is for a new resource and you do not specify the <code>kmsKeyId</code>, the access preview considers the snapshot as unencrypted.</p> </li> </ul>"}}, "documentation": "<p>The proposed access control configuration for an Amazon EBS volume snapshot. You can propose a configuration for a new Amazon EBS volume snapshot or an Amazon EBS volume snapshot that you own by specifying the user IDs, groups, and optional KMS encryption key. For more information, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/APIReference/API_ModifySnapshotAttribute.html\">ModifySnapshotAttribute</a>.</p>"}, "EbsSnapshotDataEncryptionKeyId": {"type": "string"}, "EbsUserId": {"type": "string"}, "EbsUserIdList": {"type": "list", "member": {"shape": "EbsUserId"}}, "EcrRepositoryConfiguration": {"type": "structure", "members": {"repositoryPolicy": {"shape": "EcrRepositoryPolicy", "documentation": "<p>The JSON repository policy text to apply to the Amazon ECR repository. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonECR/latest/userguide/repository-policy-examples.html\">Private repository policy examples</a> in the <i>Amazon ECR User Guide</i>.</p>"}}, "documentation": "<p>The proposed access control configuration for an Amazon ECR repository. You can propose a configuration for a new Amazon ECR repository or an existing Amazon ECR repository that you own by specifying the Amazon ECR policy. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonECR/latest/APIReference/API_Repository.html\">Repository</a>.</p> <ul> <li> <p>If the configuration is for an existing Amazon ECR repository and you do not specify the Amazon ECR policy, then the access preview uses the existing Amazon ECR policy for the repository.</p> </li> <li> <p>If the access preview is for a new resource and you do not specify the policy, then the access preview assumes an Amazon ECR repository without a policy.</p> </li> <li> <p>To propose deletion of an existing Amazon ECR repository policy, you can specify an empty string for the Amazon ECR policy.</p> </li> </ul>"}, "EcrRepositoryPolicy": {"type": "string"}, "EfsFileSystemConfiguration": {"type": "structure", "members": {"fileSystemPolicy": {"shape": "EfsFileSystemPolicy", "documentation": "<p>The JSON policy definition to apply to the Amazon EFS file system. For more information on the elements that make up a file system policy, see <a href=\"https://docs.aws.amazon.com/efs/latest/ug/access-control-overview.html#access-control-manage-access-intro-resource-policies\">Amazon EFS Resource-based policies</a>.</p>"}}, "documentation": "<p>The proposed access control configuration for an Amazon EFS file system. You can propose a configuration for a new Amazon EFS file system or an existing Amazon EFS file system that you own by specifying the Amazon EFS policy. For more information, see <a href=\"https://docs.aws.amazon.com/efs/latest/ug/using-fs.html\">Using file systems in Amazon EFS</a>.</p> <ul> <li> <p>If the configuration is for an existing Amazon EFS file system and you do not specify the Amazon EFS policy, then the access preview uses the existing Amazon EFS policy for the file system.</p> </li> <li> <p>If the access preview is for a new resource and you do not specify the policy, then the access preview assumes an Amazon EFS file system without a policy.</p> </li> <li> <p>To propose deletion of an existing Amazon EFS file system policy, you can specify an empty string for the Amazon EFS policy.</p> </li> </ul>"}, "EfsFileSystemPolicy": {"type": "string"}, "ExternalAccessDetails": {"type": "structure", "required": ["condition"], "members": {"action": {"shape": "ActionList", "documentation": "<p>The action in the analyzed policy statement that an external principal has permission to use.</p>"}, "condition": {"shape": "ConditionKeyMap", "documentation": "<p>The condition in the analyzed policy statement that resulted in an external access finding.</p>"}, "isPublic": {"shape": "Boolean", "documentation": "<p>Specifies whether the external access finding is public.</p>"}, "principal": {"shape": "PrincipalMap", "documentation": "<p>The external principal that has access to a resource within the zone of trust.</p>"}, "sources": {"shape": "FindingSourceList", "documentation": "<p>The sources of the external access finding. This indicates how the access that generated the finding is granted. It is populated for Amazon S3 bucket findings.</p>"}, "resourceControlPolicyRestriction": {"shape": "ResourceControlPolicyRestriction", "documentation": "<p>The type of restriction applied to the finding by the resource owner with an Organizations resource control policy (RCP).</p> <ul> <li> <p> <code>APPLICABLE</code>: There is an RCP present in the organization but IAM Access Analyzer does not include it in the evaluation of effective permissions. For example, if <code>s3:DeleteObject</code> is blocked by the RCP and the restriction is <code>APPLICABLE</code>, then <code>s3:DeleteObject</code> would still be included in the list of actions for the finding.</p> </li> <li> <p> <code>FAILED_TO_EVALUATE_RCP</code>: There was an error evaluating the RCP.</p> </li> <li> <p> <code>NOT_APPLICABLE</code>: There was no RCP present in the organization, or there was no RCP applicable to the resource. For example, the resource being analyzed is an Amazon RDS snapshot and there is an RCP in the organization, but the RCP only impacts Amazon S3 buckets.</p> </li> <li> <p> <code>APPLIED</code>: This restriction is not currently available for external access findings. </p> </li> </ul>"}}, "documentation": "<p>Contains information about an external access finding.</p>"}, "ExternalAccessFindingsStatistics": {"type": "structure", "members": {"resourceTypeStatistics": {"shape": "ResourceTypeStatisticsMap", "documentation": "<p>The total number of active cross-account and public findings for each resource type of the specified external access analyzer.</p>"}, "totalActiveFindings": {"shape": "Integer", "documentation": "<p>The number of active findings for the specified external access analyzer.</p>"}, "totalArchivedFindings": {"shape": "Integer", "documentation": "<p>The number of archived findings for the specified external access analyzer.</p>"}, "totalResolvedFindings": {"shape": "Integer", "documentation": "<p>The number of resolved findings for the specified external access analyzer.</p>"}}, "documentation": "<p>Provides aggregate statistics about the findings for the specified external access analyzer.</p>"}, "FilterCriteriaMap": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "Criterion"}}, "Finding": {"type": "structure", "required": ["id", "resourceType", "condition", "createdAt", "analyzedAt", "updatedAt", "status", "resourceOwnerAccount"], "members": {"id": {"shape": "FindingId", "documentation": "<p>The ID of the finding.</p>"}, "principal": {"shape": "PrincipalMap", "documentation": "<p>The external principal that has access to a resource within the zone of trust.</p>"}, "action": {"shape": "ActionList", "documentation": "<p>The action in the analyzed policy statement that an external principal has permission to use.</p>"}, "resource": {"shape": "String", "documentation": "<p>The resource that an external principal has access to.</p>"}, "isPublic": {"shape": "Boolean", "documentation": "<p>Indicates whether the policy that generated the finding allows public access to the resource.</p>"}, "resourceType": {"shape": "ResourceType", "documentation": "<p>The type of the resource identified in the finding.</p>"}, "condition": {"shape": "ConditionKeyMap", "documentation": "<p>The condition in the analyzed policy statement that resulted in a finding.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The time at which the finding was generated.</p>"}, "analyzedAt": {"shape": "Timestamp", "documentation": "<p>The time at which the resource was analyzed.</p>"}, "updatedAt": {"shape": "Timestamp", "documentation": "<p>The time at which the finding was updated.</p>"}, "status": {"shape": "FindingStatus", "documentation": "<p>The current status of the finding.</p>"}, "resourceOwnerAccount": {"shape": "String", "documentation": "<p>The Amazon Web Services account ID that owns the resource.</p>"}, "error": {"shape": "String", "documentation": "<p>An error.</p>"}, "sources": {"shape": "FindingSourceList", "documentation": "<p>The sources of the finding. This indicates how the access that generated the finding is granted. It is populated for Amazon S3 bucket findings.</p>"}, "resourceControlPolicyRestriction": {"shape": "ResourceControlPolicyRestriction", "documentation": "<p>The type of restriction applied to the finding by the resource owner with an Organizations resource control policy (RCP).</p>"}}, "documentation": "<p>Contains information about a finding.</p>"}, "FindingAggregationAccountDetails": {"type": "structure", "members": {"account": {"shape": "String", "documentation": "<p>The ID of the Amazon Web Services account for which unused access finding details are provided.</p>"}, "numberOfActiveFindings": {"shape": "Integer", "documentation": "<p>The number of active unused access findings for the specified Amazon Web Services account.</p>"}, "details": {"shape": "FindingAggregationAccountDetailsMap", "documentation": "<p>Provides the number of active findings for each type of unused access for the specified Amazon Web Services account.</p>"}}, "documentation": "<p>Contains information about the findings for an Amazon Web Services account in an organization unused access analyzer.</p>"}, "FindingAggregationAccountDetailsMap": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "Integer"}}, "FindingChangeType": {"type": "string", "enum": ["CHANGED", "NEW", "UNCHANGED"]}, "FindingDetails": {"type": "structure", "members": {"internalAccessDetails": {"shape": "InternalAccessDetails", "documentation": "<p>The details for an internal access analyzer finding. This contains information about access patterns identified within your Amazon Web Services organization or account.</p>"}, "externalAccessDetails": {"shape": "ExternalAccessDetails", "documentation": "<p>The details for an external access analyzer finding.</p>"}, "unusedPermissionDetails": {"shape": "UnusedPermissionDetails", "documentation": "<p>The details for an unused access analyzer finding with an unused permission finding type.</p>"}, "unusedIamUserAccessKeyDetails": {"shape": "UnusedIamUserAccessKeyDetails", "documentation": "<p>The details for an unused access analyzer finding with an unused IAM user access key finding type.</p>"}, "unusedIamRoleDetails": {"shape": "UnusedIamRoleDetails", "documentation": "<p>The details for an unused access analyzer finding with an unused IAM role finding type.</p>"}, "unusedIamUserPasswordDetails": {"shape": "UnusedIamUserPasswordDetails", "documentation": "<p>The details for an unused access analyzer finding with an unused IAM user password finding type.</p>"}}, "documentation": "<p>Contains information about an external access or unused access finding. Only one parameter can be used in a <code>FindingDetails</code> object.</p>", "union": true}, "FindingDetailsList": {"type": "list", "member": {"shape": "FindingDetails"}}, "FindingId": {"type": "string"}, "FindingIdList": {"type": "list", "member": {"shape": "FindingId"}}, "FindingSource": {"type": "structure", "required": ["type"], "members": {"type": {"shape": "FindingSourceType", "documentation": "<p>Indicates the type of access that generated the finding.</p>"}, "detail": {"shape": "FindingSourceDetail", "documentation": "<p>Includes details about how the access that generated the finding is granted. This is populated for Amazon S3 bucket findings.</p>"}}, "documentation": "<p>The source of the finding. This indicates how the access that generated the finding is granted. It is populated for Amazon S3 bucket findings.</p>"}, "FindingSourceDetail": {"type": "structure", "members": {"accessPointArn": {"shape": "String", "documentation": "<p>The ARN of the access point that generated the finding. The ARN format depends on whether the ARN represents an access point or a multi-region access point.</p>"}, "accessPointAccount": {"shape": "String", "documentation": "<p>The account of the cross-account access point that generated the finding.</p>"}}, "documentation": "<p>Includes details about how the access that generated the finding is granted. This is populated for Amazon S3 bucket findings.</p>"}, "FindingSourceList": {"type": "list", "member": {"shape": "FindingSource"}}, "FindingSourceType": {"type": "string", "enum": ["POLICY", "BUCKET_ACL", "S3_ACCESS_POINT", "S3_ACCESS_POINT_ACCOUNT"]}, "FindingStatus": {"type": "string", "enum": ["ACTIVE", "ARCHIVED", "RESOLVED"]}, "FindingStatusUpdate": {"type": "string", "enum": ["ACTIVE", "ARCHIVED"]}, "FindingSummary": {"type": "structure", "required": ["id", "resourceType", "condition", "createdAt", "analyzedAt", "updatedAt", "status", "resourceOwnerAccount"], "members": {"id": {"shape": "FindingId", "documentation": "<p>The ID of the finding.</p>"}, "principal": {"shape": "PrincipalMap", "documentation": "<p>The external principal that has access to a resource within the zone of trust.</p>"}, "action": {"shape": "ActionList", "documentation": "<p>The action in the analyzed policy statement that an external principal has permission to use.</p>"}, "resource": {"shape": "String", "documentation": "<p>The resource that the external principal has access to.</p>"}, "isPublic": {"shape": "Boolean", "documentation": "<p>Indicates whether the finding reports a resource that has a policy that allows public access.</p>"}, "resourceType": {"shape": "ResourceType", "documentation": "<p>The type of the resource that the external principal has access to.</p>"}, "condition": {"shape": "ConditionKeyMap", "documentation": "<p>The condition in the analyzed policy statement that resulted in a finding.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The time at which the finding was created.</p>"}, "analyzedAt": {"shape": "Timestamp", "documentation": "<p>The time at which the resource-based policy that generated the finding was analyzed.</p>"}, "updatedAt": {"shape": "Timestamp", "documentation": "<p>The time at which the finding was most recently updated.</p>"}, "status": {"shape": "FindingStatus", "documentation": "<p>The status of the finding.</p>"}, "resourceOwnerAccount": {"shape": "String", "documentation": "<p>The Amazon Web Services account ID that owns the resource.</p>"}, "error": {"shape": "String", "documentation": "<p>The error that resulted in an Error finding.</p>"}, "sources": {"shape": "FindingSourceList", "documentation": "<p>The sources of the finding. This indicates how the access that generated the finding is granted. It is populated for Amazon S3 bucket findings.</p>"}, "resourceControlPolicyRestriction": {"shape": "ResourceControlPolicyRestriction", "documentation": "<p>The type of restriction applied to the finding by the resource owner with an Organizations resource control policy (RCP).</p>"}}, "documentation": "<p>Contains information about a finding.</p>"}, "FindingSummaryV2": {"type": "structure", "required": ["analyzedAt", "createdAt", "id", "resourceType", "resourceOwnerAccount", "status", "updatedAt"], "members": {"analyzedAt": {"shape": "Timestamp", "documentation": "<p>The time at which the resource-based policy or IAM entity that generated the finding was analyzed.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The time at which the finding was created.</p>"}, "error": {"shape": "String", "documentation": "<p>The error that resulted in an Error finding.</p>"}, "id": {"shape": "FindingId", "documentation": "<p>The ID of the finding.</p>"}, "resource": {"shape": "String", "documentation": "<p>The resource that the external principal has access to.</p>"}, "resourceType": {"shape": "ResourceType", "documentation": "<p>The type of the resource that the external principal has access to.</p>"}, "resourceOwnerAccount": {"shape": "String", "documentation": "<p>The Amazon Web Services account ID that owns the resource.</p>"}, "status": {"shape": "FindingStatus", "documentation": "<p>The status of the finding.</p>"}, "updatedAt": {"shape": "Timestamp", "documentation": "<p>The time at which the finding was most recently updated.</p>"}, "findingType": {"shape": "FindingType", "documentation": "<p>The type of the access finding. For external access analyzers, the type is <code>ExternalAccess</code>. For unused access analyzers, the type can be <code>UnusedIAMRole</code>, <code>UnusedIAMUserAccessKey</code>, <code>UnusedIAMUserPassword</code>, or <code>UnusedPermission</code>. For internal access analyzers, the type is <code>InternalAccess</code>.</p>"}}, "documentation": "<p>Contains information about a finding.</p>"}, "FindingType": {"type": "string", "enum": ["ExternalAccess", "UnusedIAMRole", "UnusedIAMUserAccessKey", "UnusedIAMUserPassword", "UnusedPermission", "InternalAccess"]}, "FindingsList": {"type": "list", "member": {"shape": "FindingSummary"}}, "FindingsListV2": {"type": "list", "member": {"shape": "FindingSummaryV2"}}, "FindingsStatistics": {"type": "structure", "members": {"externalAccessFindingsStatistics": {"shape": "ExternalAccessFindingsStatistics", "documentation": "<p>The aggregate statistics for an external access analyzer.</p>"}, "internalAccessFindingsStatistics": {"shape": "InternalAccessFindingsStatistics", "documentation": "<p>The aggregate statistics for an internal access analyzer. This includes information about active, archived, and resolved findings related to internal access within your Amazon Web Services organization or account.</p>"}, "unusedAccessFindingsStatistics": {"shape": "UnusedAccessFindingsStatistics", "documentation": "<p>The aggregate statistics for an unused access analyzer.</p>"}}, "documentation": "<p>Contains information about the aggregate statistics for an external or unused access analyzer. Only one parameter can be used in a <code>FindingsStatistics</code> object.</p>", "union": true}, "FindingsStatisticsList": {"type": "list", "member": {"shape": "FindingsStatistics"}}, "GenerateFindingRecommendationRequest": {"type": "structure", "required": ["analyzerArn", "id"], "members": {"analyzerArn": {"shape": "AnalyzerArn", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/access-analyzer-getting-started.html#permission-resources\">ARN of the analyzer</a> used to generate the finding recommendation.</p>", "location": "querystring", "locationName": "analyzerArn"}, "id": {"shape": "GenerateFindingRecommendationRequestIdString", "documentation": "<p>The unique ID for the finding recommendation.</p>", "location": "uri", "locationName": "id"}}}, "GenerateFindingRecommendationRequestIdString": {"type": "string", "max": 2048, "min": 1}, "GeneratedPolicy": {"type": "structure", "required": ["policy"], "members": {"policy": {"shape": "String", "documentation": "<p>The text to use as the content for the new policy. The policy is created using the <a href=\"https://docs.aws.amazon.com/IAM/latest/APIReference/API_CreatePolicy.html\">CreatePolicy</a> action.</p>"}}, "documentation": "<p>Contains the text for the generated policy.</p>"}, "GeneratedPolicyList": {"type": "list", "member": {"shape": "GeneratedPolicy"}}, "GeneratedPolicyProperties": {"type": "structure", "required": ["principalArn"], "members": {"isComplete": {"shape": "Boolean", "documentation": "<p>This value is set to <code>true</code> if the generated policy contains all possible actions for a service that IAM Access Analyzer identified from the CloudTrail trail that you specified, and <code>false</code> otherwise.</p>"}, "principalArn": {"shape": "PrincipalArn", "documentation": "<p>The ARN of the IAM entity (user or role) for which you are generating a policy.</p>"}, "cloudTrailProperties": {"shape": "CloudTrailProperties", "documentation": "<p>Lists details about the <code>Trail</code> used to generated policy.</p>"}}, "documentation": "<p>Contains the generated policy details.</p>"}, "GeneratedPolicyResult": {"type": "structure", "required": ["properties"], "members": {"properties": {"shape": "GeneratedPolicyProperties", "documentation": "<p>A <code>GeneratedPolicyProperties</code> object that contains properties of the generated policy.</p>"}, "generatedPolicies": {"shape": "GeneratedPolicyList", "documentation": "<p>The text to use as the content for the new policy. The policy is created using the <a href=\"https://docs.aws.amazon.com/IAM/latest/APIReference/API_CreatePolicy.html\">CreatePolicy</a> action.</p>"}}, "documentation": "<p>Contains the text for the generated policy and its details.</p>"}, "GetAccessPreviewRequest": {"type": "structure", "required": ["accessPreviewId", "analyzerArn"], "members": {"accessPreviewId": {"shape": "AccessPreviewId", "documentation": "<p>The unique ID for the access preview.</p>", "location": "uri", "locationName": "accessPreviewId"}, "analyzerArn": {"shape": "AnalyzerArn", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/access-analyzer-getting-started.html#permission-resources\">ARN of the analyzer</a> used to generate the access preview.</p>", "location": "querystring", "locationName": "analyzerArn"}}}, "GetAccessPreviewResponse": {"type": "structure", "required": ["accessPreview"], "members": {"accessPreview": {"shape": "AccessPreview", "documentation": "<p>An object that contains information about the access preview.</p>"}}}, "GetAnalyzedResourceRequest": {"type": "structure", "required": ["analyzerArn", "resourceArn"], "members": {"analyzerArn": {"shape": "AnalyzerArn", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/access-analyzer-getting-started.html#permission-resources\">ARN of the analyzer</a> to retrieve information from.</p>", "location": "querystring", "locationName": "analyzerArn"}, "resourceArn": {"shape": "ResourceArn", "documentation": "<p>The ARN of the resource to retrieve information about.</p>", "location": "querystring", "locationName": "resourceArn"}}, "documentation": "<p>Retrieves an analyzed resource.</p>"}, "GetAnalyzedResourceResponse": {"type": "structure", "members": {"resource": {"shape": "AnalyzedResource", "documentation": "<p>An <code>AnalyzedResource</code> object that contains information that IAM Access Analyzer found when it analyzed the resource.</p>"}}, "documentation": "<p>The response to the request.</p>"}, "GetAnalyzerRequest": {"type": "structure", "required": ["analyzerName"], "members": {"analyzerName": {"shape": "Name", "documentation": "<p>The name of the analyzer retrieved.</p>", "location": "uri", "locationName": "analyzerName"}}, "documentation": "<p>Retrieves an analyzer.</p>"}, "GetAnalyzerResponse": {"type": "structure", "required": ["analyzer"], "members": {"analyzer": {"shape": "AnalyzerSummary", "documentation": "<p>An <code>AnalyzerSummary</code> object that contains information about the analyzer.</p>"}}, "documentation": "<p>The response to the request.</p>"}, "GetArchiveRuleRequest": {"type": "structure", "required": ["analyzerName", "ruleName"], "members": {"analyzerName": {"shape": "Name", "documentation": "<p>The name of the analyzer to retrieve rules from.</p>", "location": "uri", "locationName": "analyzerName"}, "ruleName": {"shape": "Name", "documentation": "<p>The name of the rule to retrieve.</p>", "location": "uri", "locationName": "ruleName"}}, "documentation": "<p>Retrieves an archive rule.</p>"}, "GetArchiveRuleResponse": {"type": "structure", "required": ["archiveRule"], "members": {"archiveRule": {"shape": "ArchiveRuleSummary"}}, "documentation": "<p>The response to the request.</p>"}, "GetFindingRecommendationRequest": {"type": "structure", "required": ["analyzerArn", "id"], "members": {"analyzerArn": {"shape": "AnalyzerArn", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/access-analyzer-getting-started.html#permission-resources\">ARN of the analyzer</a> used to generate the finding recommendation.</p>", "location": "querystring", "locationName": "analyzerArn"}, "id": {"shape": "GetFindingRecommendationRequestIdString", "documentation": "<p>The unique ID for the finding recommendation.</p>", "location": "uri", "locationName": "id"}, "maxResults": {"shape": "GetFindingRecommendationRequestMaxResultsInteger", "documentation": "<p>The maximum number of results to return in the response.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "Token", "documentation": "<p>A token used for pagination of results returned.</p>", "location": "querystring", "locationName": "nextToken"}}}, "GetFindingRecommendationRequestIdString": {"type": "string", "max": 2048, "min": 1}, "GetFindingRecommendationRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 1000, "min": 1}, "GetFindingRecommendationResponse": {"type": "structure", "required": ["startedAt", "resourceArn", "recommendationType", "status"], "members": {"startedAt": {"shape": "Timestamp", "documentation": "<p>The time at which the retrieval of the finding recommendation was started.</p>"}, "completedAt": {"shape": "Timestamp", "documentation": "<p>The time at which the retrieval of the finding recommendation was completed.</p>"}, "nextToken": {"shape": "Token", "documentation": "<p>A token used for pagination of results returned.</p>"}, "error": {"shape": "RecommendationError", "documentation": "<p>Detailed information about the reason that the retrieval of a recommendation for the finding failed.</p>"}, "resourceArn": {"shape": "ResourceArn", "documentation": "<p>The ARN of the resource of the finding.</p>"}, "recommendedSteps": {"shape": "RecommendedStepList", "documentation": "<p>A group of recommended steps for the finding.</p>"}, "recommendationType": {"shape": "RecommendationType", "documentation": "<p>The type of recommendation for the finding.</p>"}, "status": {"shape": "Status", "documentation": "<p>The status of the retrieval of the finding recommendation.</p>"}}}, "GetFindingRequest": {"type": "structure", "required": ["analyzerArn", "id"], "members": {"analyzerArn": {"shape": "AnalyzerArn", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/access-analyzer-getting-started.html#permission-resources\">ARN of the analyzer</a> that generated the finding.</p>", "location": "querystring", "locationName": "analyzerArn"}, "id": {"shape": "FindingId", "documentation": "<p>The ID of the finding to retrieve.</p>", "location": "uri", "locationName": "id"}}, "documentation": "<p>Retrieves a finding.</p>"}, "GetFindingResponse": {"type": "structure", "members": {"finding": {"shape": "Finding", "documentation": "<p>A <code>finding</code> object that contains finding details.</p>"}}, "documentation": "<p>The response to the request.</p>"}, "GetFindingV2Request": {"type": "structure", "required": ["analyzerArn", "id"], "members": {"analyzerArn": {"shape": "AnalyzerArn", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/access-analyzer-getting-started.html#permission-resources\">ARN of the analyzer</a> that generated the finding.</p>", "location": "querystring", "locationName": "analyzerArn"}, "id": {"shape": "FindingId", "documentation": "<p>The ID of the finding to retrieve.</p>", "location": "uri", "locationName": "id"}, "maxResults": {"shape": "Integer", "documentation": "<p>The maximum number of results to return in the response.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "Token", "documentation": "<p>A token used for pagination of results returned.</p>", "location": "querystring", "locationName": "nextToken"}}}, "GetFindingV2Response": {"type": "structure", "required": ["analyzedAt", "createdAt", "id", "resourceType", "resourceOwnerAccount", "status", "updatedAt", "findingDetails"], "members": {"analyzedAt": {"shape": "Timestamp", "documentation": "<p>The time at which the resource-based policy or IAM entity that generated the finding was analyzed.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The time at which the finding was created.</p>"}, "error": {"shape": "String", "documentation": "<p>An error.</p>"}, "id": {"shape": "FindingId", "documentation": "<p>The ID of the finding to retrieve.</p>"}, "nextToken": {"shape": "Token", "documentation": "<p>A token used for pagination of results returned.</p>"}, "resource": {"shape": "String", "documentation": "<p>The resource that generated the finding.</p>"}, "resourceType": {"shape": "ResourceType", "documentation": "<p>The type of the resource identified in the finding.</p>"}, "resourceOwnerAccount": {"shape": "String", "documentation": "<p>Tye Amazon Web Services account ID that owns the resource.</p>"}, "status": {"shape": "FindingStatus", "documentation": "<p>The status of the finding.</p>"}, "updatedAt": {"shape": "Timestamp", "documentation": "<p>The time at which the finding was updated.</p>"}, "findingDetails": {"shape": "FindingDetailsList", "documentation": "<p>A localized message that explains the finding and provides guidance on how to address it.</p>"}, "findingType": {"shape": "FindingType", "documentation": "<p>The type of the finding. For external access analyzers, the type is <code>ExternalAccess</code>. For unused access analyzers, the type can be <code>UnusedIAMRole</code>, <code>UnusedIAMUserAccessKey</code>, <code>UnusedIAMUserPassword</code>, or <code>UnusedPermission</code>. For internal access analyzers, the type is <code>InternalAccess</code>.</p>"}}}, "GetFindingsStatisticsRequest": {"type": "structure", "required": ["analyzerArn"], "members": {"analyzerArn": {"shape": "AnalyzerArn", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/access-analyzer-getting-started.html#permission-resources\">ARN of the analyzer</a> used to generate the statistics.</p>"}}}, "GetFindingsStatisticsResponse": {"type": "structure", "members": {"findingsStatistics": {"shape": "FindingsStatisticsList", "documentation": "<p>A group of external access or unused access findings statistics.</p>"}, "lastUpdatedAt": {"shape": "Timestamp", "documentation": "<p>The time at which the retrieval of the findings statistics was last updated. If the findings statistics have not been previously retrieved for the specified analyzer, this field will not be populated.</p>"}}}, "GetGeneratedPolicyRequest": {"type": "structure", "required": ["jobId"], "members": {"jobId": {"shape": "JobId", "documentation": "<p>The <code>JobId</code> that is returned by the <code>StartPolicyGeneration</code> operation. The <code>JobId</code> can be used with <code>GetGeneratedPolicy</code> to retrieve the generated policies or used with <code>CancelPolicyGeneration</code> to cancel the policy generation request.</p>", "location": "uri", "locationName": "jobId"}, "includeResourcePlaceholders": {"shape": "Boolean", "documentation": "<p>The level of detail that you want to generate. You can specify whether to generate policies with placeholders for resource ARNs for actions that support resource level granularity in policies.</p> <p>For example, in the resource section of a policy, you can receive a placeholder such as <code>\"Resource\":\"arn:aws:s3:::${BucketName}\"</code> instead of <code>\"*\"</code>.</p>", "location": "querystring", "locationName": "includeResourcePlaceholders"}, "includeServiceLevelTemplate": {"shape": "Boolean", "documentation": "<p>The level of detail that you want to generate. You can specify whether to generate service-level policies. </p> <p>IAM Access Analyzer uses <code>iam:servicelastaccessed</code> to identify services that have been used recently to create this service-level template.</p>", "location": "querystring", "locationName": "includeServiceLevelTemplate"}}}, "GetGeneratedPolicyResponse": {"type": "structure", "required": ["jobDetails", "generatedPolicyResult"], "members": {"jobDetails": {"shape": "JobDetails", "documentation": "<p>A <code>GeneratedPolicyDetails</code> object that contains details about the generated policy.</p>"}, "generatedPolicyResult": {"shape": "GeneratedPolicyResult", "documentation": "<p>A <code>GeneratedPolicyResult</code> object that contains the generated policies and associated details.</p>"}}}, "GranteePrincipal": {"type": "string"}, "IamRoleConfiguration": {"type": "structure", "members": {"trustPolicy": {"shape": "IamTrustPolicy", "documentation": "<p>The proposed trust policy for the IAM role.</p>"}}, "documentation": "<p>The proposed access control configuration for an IAM role. You can propose a configuration for a new IAM role or an existing IAM role that you own by specifying the trust policy. If the configuration is for a new IAM role, you must specify the trust policy. If the configuration is for an existing IAM role that you own and you do not propose the trust policy, the access preview uses the existing trust policy for the role. The proposed trust policy cannot be an empty string. For more information about role trust policy limits, see <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/reference_iam-quotas.html\">IAM and STS quotas</a>.</p>"}, "IamTrustPolicy": {"type": "string"}, "InlineArchiveRule": {"type": "structure", "required": ["ruleName", "filter"], "members": {"ruleName": {"shape": "Name", "documentation": "<p>The name of the rule.</p>"}, "filter": {"shape": "FilterCriteriaMap", "documentation": "<p>The condition and values for a criterion.</p>"}}, "documentation": "<p>An criterion statement in an archive rule. Each archive rule may have multiple criteria.</p>"}, "InlineArchiveRulesList": {"type": "list", "member": {"shape": "InlineArchiveRule"}}, "Integer": {"type": "integer", "box": true}, "InternalAccessAnalysisRule": {"type": "structure", "members": {"inclusions": {"shape": "InternalAccessAnalysisRuleCriteriaList", "documentation": "<p>A list of rules for the internal access analyzer containing criteria to include in analysis. Only resources that meet the rule criteria will generate findings.</p>"}}, "documentation": "<p>Contains information about analysis rules for the internal access analyzer. Analysis rules determine which entities will generate findings based on the criteria you define when you create the rule.</p>"}, "InternalAccessAnalysisRuleCriteria": {"type": "structure", "members": {"accountIds": {"shape": "AccountIdsList", "documentation": "<p>A list of Amazon Web Services account IDs to apply to the internal access analysis rule criteria. Account IDs can only be applied to the analysis rule criteria for organization-level analyzers.</p>"}, "resourceTypes": {"shape": "ResourceTypeList", "documentation": "<p>A list of resource types to apply to the internal access analysis rule criteria. The analyzer will only generate findings for resources of these types. These resource types are currently supported for internal access analyzers:</p> <ul> <li> <p> <code>AWS::S3::Bucket</code> </p> </li> <li> <p> <code>AWS::RDS::DBSnapshot</code> </p> </li> <li> <p> <code>AWS::RDS::DBClusterSnapshot</code> </p> </li> <li> <p> <code>AWS::S3Express::DirectoryBucket</code> </p> </li> <li> <p> <code>AWS::DynamoDB::Table</code> </p> </li> <li> <p> <code>AWS::DynamoDB::Stream</code> </p> </li> </ul>"}, "resourceArns": {"shape": "ResourceArnsList", "documentation": "<p>A list of resource ARNs to apply to the internal access analysis rule criteria. The analyzer will only generate findings for resources that match these ARNs.</p>"}}, "documentation": "<p>The criteria for an analysis rule for an internal access analyzer.</p>"}, "InternalAccessAnalysisRuleCriteriaList": {"type": "list", "member": {"shape": "InternalAccessAnalysisRuleCriteria"}}, "InternalAccessConfiguration": {"type": "structure", "members": {"analysisRule": {"shape": "InternalAccessAnalysisRule", "documentation": "<p>Contains information about analysis rules for the internal access analyzer. These rules determine which resources and access patterns will be analyzed.</p>"}}, "documentation": "<p>Specifies the configuration of an internal access analyzer for an Amazon Web Services organization or account. This configuration determines how the analyzer evaluates internal access within your Amazon Web Services environment.</p>"}, "InternalAccessDetails": {"type": "structure", "members": {"action": {"shape": "ActionList", "documentation": "<p>The action in the analyzed policy statement that has internal access permission to use.</p>"}, "condition": {"shape": "ConditionKeyMap", "documentation": "<p>The condition in the analyzed policy statement that resulted in an internal access finding.</p>"}, "principal": {"shape": "PrincipalMap", "documentation": "<p>The principal that has access to a resource within the internal environment.</p>"}, "principalOwnerAccount": {"shape": "String", "documentation": "<p>The Amazon Web Services account ID that owns the principal identified in the internal access finding.</p>"}, "accessType": {"shape": "InternalAccessType", "documentation": "<p>The type of internal access identified in the finding. This indicates how the access is granted within your Amazon Web Services environment.</p>"}, "principalType": {"shape": "PrincipalType", "documentation": "<p>The type of principal identified in the internal access finding, such as IAM role or IAM user.</p>"}, "sources": {"shape": "FindingSourceList", "documentation": "<p>The sources of the internal access finding. This indicates how the access that generated the finding is granted within your Amazon Web Services environment.</p>"}, "resourceControlPolicyRestriction": {"shape": "ResourceControlPolicyRestriction", "documentation": "<p>The type of restriction applied to the finding by the resource owner with an Organizations resource control policy (RCP).</p> <ul> <li> <p> <code>APPLICABLE</code>: There is an RCP present in the organization but IAM Access Analyzer does not include it in the evaluation of effective permissions. For example, if <code>s3:DeleteObject</code> is blocked by the RCP and the restriction is <code>APPLICABLE</code>, then <code>s3:DeleteObject</code> would still be included in the list of actions for the finding. Only applicable to internal access findings with the account as the zone of trust. </p> </li> <li> <p> <code>FAILED_TO_EVALUATE_RCP</code>: There was an error evaluating the RCP.</p> </li> <li> <p> <code>NOT_APPLICABLE</code>: There was no RCP present in the organization. For internal access findings with the account as the zone of trust, <code>NOT_APPLICABLE</code> could also indicate that there was no RCP applicable to the resource.</p> </li> <li> <p> <code>APPLIED</code>: An RCP is present in the organization and IAM Access Analyzer included it in the evaluation of effective permissions. For example, if <code>s3:DeleteObject</code> is blocked by the RCP and the restriction is <code>APPLIED</code>, then <code>s3:DeleteObject</code> would not be included in the list of actions for the finding. Only applicable to internal access findings with the organization as the zone of trust. </p> </li> </ul>"}, "serviceControlPolicyRestriction": {"shape": "ServiceControlPolicyRestriction", "documentation": "<p>The type of restriction applied to the finding by an Organizations service control policy (SCP).</p> <ul> <li> <p> <code>APPLICABLE</code>: There is an SCP present in the organization but IAM Access Analyzer does not include it in the evaluation of effective permissions. Only applicable to internal access findings with the account as the zone of trust. </p> </li> <li> <p> <code>FAILED_TO_EVALUATE_SCP</code>: There was an error evaluating the SCP.</p> </li> <li> <p> <code>NOT_APPLICABLE</code>: There was no SCP present in the organization. For internal access findings with the account as the zone of trust, <code>NOT_APPLICABLE</code> could also indicate that there was no SCP applicable to the principal.</p> </li> <li> <p> <code>APPLIED</code>: An SCP is present in the organization and IAM Access Analyzer included it in the evaluation of effective permissions. Only applicable to internal access findings with the organization as the zone of trust. </p> </li> </ul>"}}, "documentation": "<p>Contains information about an internal access finding. This includes details about the access that was identified within your Amazon Web Services organization or account.</p>"}, "InternalAccessFindingsStatistics": {"type": "structure", "members": {"resourceTypeStatistics": {"shape": "InternalAccessResourceTypeStatisticsMap", "documentation": "<p>The total number of active findings for each resource type of the specified internal access analyzer.</p>"}, "totalActiveFindings": {"shape": "Integer", "documentation": "<p>The number of active findings for the specified internal access analyzer.</p>"}, "totalArchivedFindings": {"shape": "Integer", "documentation": "<p>The number of archived findings for the specified internal access analyzer.</p>"}, "totalResolvedFindings": {"shape": "Integer", "documentation": "<p>The number of resolved findings for the specified internal access analyzer.</p>"}}, "documentation": "<p>Provides aggregate statistics about the findings for the specified internal access analyzer. This includes counts of active, archived, and resolved findings.</p>"}, "InternalAccessResourceTypeDetails": {"type": "structure", "members": {"totalActiveFindings": {"shape": "Integer", "documentation": "<p>The total number of active findings for the resource type in the internal access analyzer.</p>"}, "totalResolvedFindings": {"shape": "Integer", "documentation": "<p>The total number of resolved findings for the resource type in the internal access analyzer.</p>"}, "totalArchivedFindings": {"shape": "Integer", "documentation": "<p>The total number of archived findings for the resource type in the internal access analyzer.</p>"}}, "documentation": "<p>Contains information about the total number of active, archived, and resolved findings for a resource type of an internal access analyzer.</p>"}, "InternalAccessResourceTypeStatisticsMap": {"type": "map", "key": {"shape": "ResourceType"}, "value": {"shape": "InternalAccessResourceTypeDetails"}}, "InternalAccessType": {"type": "string", "enum": ["INTRA_ACCOUNT", "INTRA_ORG"]}, "InternalServerException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}, "retryAfterSeconds": {"shape": "Integer", "documentation": "<p>The seconds to wait to retry.</p>", "location": "header", "locationName": "Retry-After"}}, "documentation": "<p>Internal server error.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true, "retryable": {"throttling": false}}, "InternetConfiguration": {"type": "structure", "members": {}, "documentation": "<p>This configuration sets the network origin for the Amazon S3 access point or multi-region access point to <code>Internet</code>.</p>"}, "InvalidParameterException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The specified parameter is invalid.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "IssueCode": {"type": "string"}, "IssuingAccount": {"type": "string"}, "JobDetails": {"type": "structure", "required": ["jobId", "status", "startedOn"], "members": {"jobId": {"shape": "JobId", "documentation": "<p>The <code>JobId</code> that is returned by the <code>StartPolicyGeneration</code> operation. The <code>JobId</code> can be used with <code>GetGeneratedPolicy</code> to retrieve the generated policies or used with <code>CancelPolicyGeneration</code> to cancel the policy generation request.</p>"}, "status": {"shape": "JobStatus", "documentation": "<p>The status of the job request.</p>"}, "startedOn": {"shape": "Timestamp", "documentation": "<p>A timestamp of when the job was started.</p>"}, "completedOn": {"shape": "Timestamp", "documentation": "<p>A timestamp of when the job was completed.</p>"}, "jobError": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The job error for the policy generation request.</p>"}}, "documentation": "<p>Contains details about the policy generation request.</p>"}, "JobError": {"type": "structure", "required": ["code", "message"], "members": {"code": {"shape": "JobErrorCode", "documentation": "<p>The job error code.</p>"}, "message": {"shape": "String", "documentation": "<p>Specific information about the error. For example, which service quota was exceeded or which resource was not found.</p>"}}, "documentation": "<p>Contains the details about the policy generation error.</p>"}, "JobErrorCode": {"type": "string", "enum": ["AUTHORIZATION_ERROR", "RESOURCE_NOT_FOUND_ERROR", "SERVICE_QUOTA_EXCEEDED_ERROR", "SERVICE_ERROR"]}, "JobId": {"type": "string"}, "JobStatus": {"type": "string", "enum": ["IN_PROGRESS", "SUCCEEDED", "FAILED", "CANCELED"]}, "KmsConstraintsKey": {"type": "string"}, "KmsConstraintsMap": {"type": "map", "key": {"shape": "KmsConstraintsKey"}, "value": {"shape": "KmsConstraintsValue"}}, "KmsConstraintsValue": {"type": "string"}, "KmsGrantConfiguration": {"type": "structure", "required": ["operations", "grantee<PERSON><PERSON><PERSON><PERSON>", "issuingAccount"], "members": {"operations": {"shape": "KmsGrantOperationsList", "documentation": "<p>A list of operations that the grant permits.</p>"}, "granteePrincipal": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The principal that is given permission to perform the operations that the grant permits.</p>"}, "retiringPrincipal": {"shape": "RetiringPrincipal", "documentation": "<p>The principal that is given permission to retire the grant by using <a href=\"https://docs.aws.amazon.com/kms/latest/APIReference/API_RetireGrant.html\">RetireGrant</a> operation.</p>"}, "constraints": {"shape": "KmsGrantConstraints", "documentation": "<p>Use this structure to propose allowing <a href=\"https://docs.aws.amazon.com/kms/latest/developerguide/concepts.html#cryptographic-operations\">cryptographic operations</a> in the grant only when the operation request includes the specified <a href=\"https://docs.aws.amazon.com/kms/latest/developerguide/concepts.html#encrypt_context\">encryption context</a>.</p>"}, "issuingAccount": {"shape": "IssuingAccount", "documentation": "<p> The Amazon Web Services account under which the grant was issued. The account is used to propose KMS grants issued by accounts other than the owner of the key.</p>"}}, "documentation": "<p>A proposed grant configuration for a KMS key. For more information, see <a href=\"https://docs.aws.amazon.com/kms/latest/APIReference/API_CreateGrant.html\">CreateGrant</a>.</p>"}, "KmsGrantConfigurationsList": {"type": "list", "member": {"shape": "KmsGrantConfiguration"}}, "KmsGrantConstraints": {"type": "structure", "members": {"encryptionContextEquals": {"shape": "KmsConstraintsMap", "documentation": "<p>A list of key-value pairs that must match the encryption context in the <a href=\"https://docs.aws.amazon.com/kms/latest/developerguide/concepts.html#cryptographic-operations\">cryptographic operation</a> request. The grant allows the operation only when the encryption context in the request is the same as the encryption context specified in this constraint.</p>"}, "encryptionContextSubset": {"shape": "KmsConstraintsMap", "documentation": "<p>A list of key-value pairs that must be included in the encryption context of the <a href=\"https://docs.aws.amazon.com/kms/latest/developerguide/concepts.html#cryptographic-operations\">cryptographic operation</a> request. The grant allows the cryptographic operation only when the encryption context in the request includes the key-value pairs specified in this constraint, although it can include additional key-value pairs.</p>"}}, "documentation": "<p>Use this structure to propose allowing <a href=\"https://docs.aws.amazon.com/kms/latest/developerguide/concepts.html#cryptographic-operations\">cryptographic operations</a> in the grant only when the operation request includes the specified <a href=\"https://docs.aws.amazon.com/kms/latest/developerguide/concepts.html#encrypt_context\">encryption context</a>. You can specify only one type of encryption context. An empty map is treated as not specified. For more information, see <a href=\"https://docs.aws.amazon.com/kms/latest/APIReference/API_GrantConstraints.html\">GrantConstraints</a>.</p>"}, "KmsGrantOperation": {"type": "string", "enum": ["CreateGrant", "Decrypt", "Describe<PERSON><PERSON>", "Encrypt", "GenerateDataKey", "GenerateDataKeyPair", "GenerateDataKeyPairWithoutPlaintext", "GenerateDataKeyWithoutPlaintext", "GetPublicKey", "ReEncryptFrom", "ReEncryptTo", "RetireGrant", "Sign", "Verify"]}, "KmsGrantOperationsList": {"type": "list", "member": {"shape": "KmsGrantOperation"}}, "KmsKeyConfiguration": {"type": "structure", "members": {"keyPolicies": {"shape": "KmsKeyPoliciesMap", "documentation": "<p>Resource policy configuration for the KMS key. The only valid value for the name of the key policy is <code>default</code>. For more information, see <a href=\"https://docs.aws.amazon.com/kms/latest/developerguide/key-policies.html#key-policy-default\">Default key policy</a>.</p>"}, "grants": {"shape": "KmsGrantConfigurationsList", "documentation": "<p>A list of proposed grant configurations for the KMS key. If the proposed grant configuration is for an existing key, the access preview uses the proposed list of grant configurations in place of the existing grants. Otherwise, the access preview uses the existing grants for the key.</p>"}}, "documentation": "<p>Proposed access control configuration for a KMS key. You can propose a configuration for a new KMS key or an existing KMS key that you own by specifying the key policy and KMS grant configuration. If the configuration is for an existing key and you do not specify the key policy, the access preview uses the existing policy for the key. If the access preview is for a new resource and you do not specify the key policy, then the access preview uses the default key policy. The proposed key policy cannot be an empty string. For more information, see <a href=\"https://docs.aws.amazon.com/kms/latest/developerguide/key-policies.html#key-policy-default\">Default key policy</a>. For more information about key policy limits, see <a href=\"https://docs.aws.amazon.com/kms/latest/developerguide/resource-limits.html\">Resource quotas</a>.</p> <p/>"}, "KmsKeyPoliciesMap": {"type": "map", "key": {"shape": "PolicyName"}, "value": {"shape": "KmsKeyPolicy"}}, "KmsKeyPolicy": {"type": "string"}, "LearnMoreLink": {"type": "string"}, "ListAccessPreviewFindingsRequest": {"type": "structure", "required": ["accessPreviewId", "analyzerArn"], "members": {"accessPreviewId": {"shape": "AccessPreviewId", "documentation": "<p>The unique ID for the access preview.</p>", "location": "uri", "locationName": "accessPreviewId"}, "analyzerArn": {"shape": "AnalyzerArn", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/access-analyzer-getting-started.html#permission-resources\">ARN of the analyzer</a> used to generate the access.</p>"}, "filter": {"shape": "FilterCriteriaMap", "documentation": "<p>Criteria to filter the returned findings.</p>"}, "nextToken": {"shape": "Token", "documentation": "<p>A token used for pagination of results returned.</p>"}, "maxResults": {"shape": "Integer", "documentation": "<p>The maximum number of results to return in the response.</p>"}}}, "ListAccessPreviewFindingsResponse": {"type": "structure", "required": ["findings"], "members": {"findings": {"shape": "AccessPreviewFindingsList", "documentation": "<p>A list of access preview findings that match the specified filter criteria.</p>"}, "nextToken": {"shape": "Token", "documentation": "<p>A token used for pagination of results returned.</p>"}}}, "ListAccessPreviewsRequest": {"type": "structure", "required": ["analyzerArn"], "members": {"analyzerArn": {"shape": "AnalyzerArn", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/access-analyzer-getting-started.html#permission-resources\">ARN of the analyzer</a> used to generate the access preview.</p>", "location": "querystring", "locationName": "analyzerArn"}, "nextToken": {"shape": "Token", "documentation": "<p>A token used for pagination of results returned.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "Integer", "documentation": "<p>The maximum number of results to return in the response.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListAccessPreviewsResponse": {"type": "structure", "required": ["accessPreviews"], "members": {"accessPreviews": {"shape": "AccessPreviewsList", "documentation": "<p>A list of access previews retrieved for the analyzer.</p>"}, "nextToken": {"shape": "Token", "documentation": "<p>A token used for pagination of results returned.</p>"}}}, "ListAnalyzedResourcesRequest": {"type": "structure", "required": ["analyzerArn"], "members": {"analyzerArn": {"shape": "AnalyzerArn", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/access-analyzer-getting-started.html#permission-resources\">ARN of the analyzer</a> to retrieve a list of analyzed resources from.</p>"}, "resourceType": {"shape": "ResourceType", "documentation": "<p>The type of resource.</p>"}, "nextToken": {"shape": "Token", "documentation": "<p>A token used for pagination of results returned.</p>"}, "maxResults": {"shape": "Integer", "documentation": "<p>The maximum number of results to return in the response.</p>"}}, "documentation": "<p>Retrieves a list of resources that have been analyzed.</p>"}, "ListAnalyzedResourcesResponse": {"type": "structure", "required": ["analyzedResources"], "members": {"analyzedResources": {"shape": "AnalyzedResourcesList", "documentation": "<p>A list of resources that were analyzed.</p>"}, "nextToken": {"shape": "Token", "documentation": "<p>A token used for pagination of results returned.</p>"}}, "documentation": "<p>The response to the request.</p>"}, "ListAnalyzersRequest": {"type": "structure", "members": {"nextToken": {"shape": "Token", "documentation": "<p>A token used for pagination of results returned.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "Integer", "documentation": "<p>The maximum number of results to return in the response.</p>", "location": "querystring", "locationName": "maxResults"}, "type": {"shape": "Type", "documentation": "<p>The type of analyzer.</p>", "location": "querystring", "locationName": "type"}}, "documentation": "<p>Retrieves a list of analyzers.</p>"}, "ListAnalyzersResponse": {"type": "structure", "required": ["analyzers"], "members": {"analyzers": {"shape": "AnalyzersList", "documentation": "<p>The analyzers retrieved.</p>"}, "nextToken": {"shape": "Token", "documentation": "<p>A token used for pagination of results returned.</p>"}}, "documentation": "<p>The response to the request.</p>"}, "ListArchiveRulesRequest": {"type": "structure", "required": ["analyzerName"], "members": {"analyzerName": {"shape": "Name", "documentation": "<p>The name of the analyzer to retrieve rules from.</p>", "location": "uri", "locationName": "analyzerName"}, "nextToken": {"shape": "Token", "documentation": "<p>A token used for pagination of results returned.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "Integer", "documentation": "<p>The maximum number of results to return in the request.</p>", "location": "querystring", "locationName": "maxResults"}}, "documentation": "<p>Retrieves a list of archive rules created for the specified analyzer.</p>"}, "ListArchiveRulesResponse": {"type": "structure", "required": ["archiveRules"], "members": {"archiveRules": {"shape": "ArchiveRulesList", "documentation": "<p>A list of archive rules created for the specified analyzer.</p>"}, "nextToken": {"shape": "Token", "documentation": "<p>A token used for pagination of results returned.</p>"}}, "documentation": "<p>The response to the request.</p>"}, "ListFindingsRequest": {"type": "structure", "required": ["analyzerArn"], "members": {"analyzerArn": {"shape": "AnalyzerArn", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/access-analyzer-getting-started.html#permission-resources\">ARN of the analyzer</a> to retrieve findings from.</p>"}, "filter": {"shape": "FilterCriteriaMap", "documentation": "<p>A filter to match for the findings to return.</p>"}, "sort": {"shape": "SortCriteria", "documentation": "<p>The sort order for the findings returned.</p>"}, "nextToken": {"shape": "Token", "documentation": "<p>A token used for pagination of results returned.</p>"}, "maxResults": {"shape": "Integer", "documentation": "<p>The maximum number of results to return in the response.</p>"}}, "documentation": "<p>Retrieves a list of findings generated by the specified analyzer.</p>"}, "ListFindingsResponse": {"type": "structure", "required": ["findings"], "members": {"findings": {"shape": "FindingsList", "documentation": "<p>A list of findings retrieved from the analyzer that match the filter criteria specified, if any.</p>"}, "nextToken": {"shape": "Token", "documentation": "<p>A token used for pagination of results returned.</p>"}}, "documentation": "<p>The response to the request.</p>"}, "ListFindingsV2Request": {"type": "structure", "required": ["analyzerArn"], "members": {"analyzerArn": {"shape": "AnalyzerArn", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/access-analyzer-getting-started.html#permission-resources\">ARN of the analyzer</a> to retrieve findings from.</p>"}, "filter": {"shape": "FilterCriteriaMap", "documentation": "<p>A filter to match for the findings to return.</p>"}, "maxResults": {"shape": "Integer", "documentation": "<p>The maximum number of results to return in the response.</p>"}, "nextToken": {"shape": "Token", "documentation": "<p>A token used for pagination of results returned.</p>"}, "sort": {"shape": "SortCriteria"}}}, "ListFindingsV2Response": {"type": "structure", "required": ["findings"], "members": {"findings": {"shape": "FindingsListV2", "documentation": "<p>A list of findings retrieved from the analyzer that match the filter criteria specified, if any.</p>"}, "nextToken": {"shape": "Token", "documentation": "<p>A token used for pagination of results returned.</p>"}}}, "ListPolicyGenerationsRequest": {"type": "structure", "members": {"principalArn": {"shape": "PrincipalArn", "documentation": "<p>The ARN of the IAM entity (user or role) for which you are generating a policy. Use this with <code>ListGeneratedPolicies</code> to filter the results to only include results for a specific principal.</p>", "location": "querystring", "locationName": "principalArn"}, "maxResults": {"shape": "ListPolicyGenerationsRequestMaxResultsInteger", "documentation": "<p>The maximum number of results to return in the response.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "Token", "documentation": "<p>A token used for pagination of results returned.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListPolicyGenerationsRequestMaxResultsInteger": {"type": "integer", "box": true, "min": 1}, "ListPolicyGenerationsResponse": {"type": "structure", "required": ["policyGenerations"], "members": {"policyGenerations": {"shape": "PolicyGenerationList", "documentation": "<p>A <code>PolicyGeneration</code> object that contains details about the generated policy.</p>"}, "nextToken": {"shape": "Token", "documentation": "<p>A token used for pagination of results returned.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "String", "documentation": "<p>The ARN of the resource to retrieve tags from.</p>", "location": "uri", "locationName": "resourceArn"}}, "documentation": "<p>Retrieves a list of tags applied to the specified resource.</p>"}, "ListTagsForResourceResponse": {"type": "structure", "members": {"tags": {"shape": "TagsMap", "documentation": "<p>The tags that are applied to the specified resource.</p>"}}, "documentation": "<p>The response to the request.</p>"}, "Locale": {"type": "string", "enum": ["DE", "EN", "ES", "FR", "IT", "JA", "KO", "PT_BR", "ZH_CN", "ZH_TW"]}, "Location": {"type": "structure", "required": ["path", "span"], "members": {"path": {"shape": "PathElementList", "documentation": "<p>A path in a policy, represented as a sequence of path elements.</p>"}, "span": {"shape": "Span", "documentation": "<p>A span in a policy.</p>"}}, "documentation": "<p>A location in a policy that is represented as a path through the JSON representation and a corresponding span.</p>"}, "LocationList": {"type": "list", "member": {"shape": "Location"}}, "Name": {"type": "string", "max": 255, "min": 1, "pattern": "[A-Za-z][A-Za-z0-9_.-]*"}, "NetworkOriginConfiguration": {"type": "structure", "members": {"vpcConfiguration": {"shape": "VpcConfiguration"}, "internetConfiguration": {"shape": "InternetConfiguration", "documentation": "<p>The configuration for the Amazon S3 access point or multi-region access point with an <code>Internet</code> origin.</p>"}}, "documentation": "<p>The proposed <code>InternetConfiguration</code> or <code>VpcConfiguration</code> to apply to the Amazon S3 access point. You can make the access point accessible from the internet, or you can specify that all requests made through that access point must originate from a specific virtual private cloud (VPC). You can specify only one type of network configuration. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/creating-access-points.html\">Creating access points</a>.</p>", "union": true}, "OrderBy": {"type": "string", "enum": ["ASC", "DESC"]}, "PathElement": {"type": "structure", "members": {"index": {"shape": "Integer", "documentation": "<p>Refers to an index in a JSON array.</p>"}, "key": {"shape": "String", "documentation": "<p>Refers to a key in a JSON object.</p>"}, "substring": {"shape": "Substring", "documentation": "<p>Refers to a substring of a literal string in a JSON object.</p>"}, "value": {"shape": "String", "documentation": "<p>Refers to the value associated with a given key in a JSON object.</p>"}}, "documentation": "<p>A single element in a path through the JSON representation of a policy.</p>", "union": true}, "PathElementList": {"type": "list", "member": {"shape": "PathElement"}}, "PolicyDocument": {"type": "string"}, "PolicyGeneration": {"type": "structure", "required": ["jobId", "principalArn", "status", "startedOn"], "members": {"jobId": {"shape": "JobId", "documentation": "<p>The <code>JobId</code> that is returned by the <code>StartPolicyGeneration</code> operation. The <code>JobId</code> can be used with <code>GetGeneratedPolicy</code> to retrieve the generated policies or used with <code>CancelPolicyGeneration</code> to cancel the policy generation request.</p>"}, "principalArn": {"shape": "PrincipalArn", "documentation": "<p>The ARN of the IAM entity (user or role) for which you are generating a policy.</p>"}, "status": {"shape": "JobStatus", "documentation": "<p>The status of the policy generation request.</p>"}, "startedOn": {"shape": "Timestamp", "documentation": "<p>A timestamp of when the policy generation started.</p>"}, "completedOn": {"shape": "Timestamp", "documentation": "<p>A timestamp of when the policy generation was completed.</p>"}}, "documentation": "<p>Contains details about the policy generation status and properties.</p>"}, "PolicyGenerationDetails": {"type": "structure", "required": ["principalArn"], "members": {"principalArn": {"shape": "PrincipalArn", "documentation": "<p>The ARN of the IAM entity (user or role) for which you are generating a policy.</p>"}}, "documentation": "<p>Contains the ARN details about the IAM entity for which the policy is generated.</p>"}, "PolicyGenerationList": {"type": "list", "member": {"shape": "PolicyGeneration"}}, "PolicyName": {"type": "string"}, "PolicyType": {"type": "string", "enum": ["IDENTITY_POLICY", "RESOURCE_POLICY", "SERVICE_CONTROL_POLICY", "RESOURCE_CONTROL_POLICY"]}, "Position": {"type": "structure", "required": ["line", "column", "offset"], "members": {"line": {"shape": "Integer", "documentation": "<p>The line of the position, starting from 1.</p>"}, "column": {"shape": "Integer", "documentation": "<p>The column of the position, starting from 0.</p>"}, "offset": {"shape": "Integer", "documentation": "<p>The offset within the policy that corresponds to the position, starting from 0.</p>"}}, "documentation": "<p>A position in a policy.</p>"}, "PrincipalArn": {"type": "string", "pattern": "arn:[^:]*:iam::[^:]*:(role|user)/.{1,576}"}, "PrincipalMap": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "String"}}, "PrincipalType": {"type": "string", "enum": ["IAM_ROLE", "IAM_USER"]}, "RdsDbClusterSnapshotAccountId": {"type": "string"}, "RdsDbClusterSnapshotAccountIdsList": {"type": "list", "member": {"shape": "RdsDbClusterSnapshotAccountId"}}, "RdsDbClusterSnapshotAttributeName": {"type": "string"}, "RdsDbClusterSnapshotAttributeValue": {"type": "structure", "members": {"accountIds": {"shape": "RdsDbClusterSnapshotAccountIdsList", "documentation": "<p>The Amazon Web Services account IDs that have access to the manual Amazon RDS DB cluster snapshot. If the value <code>all</code> is specified, then the Amazon RDS DB cluster snapshot is public and can be copied or restored by all Amazon Web Services accounts.</p> <ul> <li> <p>If the configuration is for an existing Amazon RDS DB cluster snapshot and you do not specify the <code>accountIds</code> in <code>RdsDbClusterSnapshotAttributeValue</code>, then the access preview uses the existing shared <code>accountIds</code> for the snapshot.</p> </li> <li> <p>If the access preview is for a new resource and you do not specify the specify the <code>accountIds</code> in <code>RdsDbClusterSnapshotAttributeValue</code>, then the access preview considers the snapshot without any attributes.</p> </li> <li> <p>To propose deletion of existing shared <code>accountIds</code>, you can specify an empty list for <code>accountIds</code> in the <code>RdsDbClusterSnapshotAttributeValue</code>.</p> </li> </ul>"}}, "documentation": "<p>The values for a manual Amazon RDS DB cluster snapshot attribute.</p>", "union": true}, "RdsDbClusterSnapshotAttributesMap": {"type": "map", "key": {"shape": "RdsDbClusterSnapshotAttributeName"}, "value": {"shape": "RdsDbClusterSnapshotAttributeValue"}}, "RdsDbClusterSnapshotConfiguration": {"type": "structure", "members": {"attributes": {"shape": "RdsDbClusterSnapshotAttributesMap", "documentation": "<p>The names and values of manual DB cluster snapshot attributes. Manual DB cluster snapshot attributes are used to authorize other Amazon Web Services accounts to restore a manual DB cluster snapshot. The only valid value for <code>AttributeName</code> for the attribute map is <code>restore</code> </p>"}, "kmsKeyId": {"shape": "RdsDbClusterSnapshotKmsKeyId", "documentation": "<p>The KMS key identifier for an encrypted Amazon RDS DB cluster snapshot. The KMS key identifier is the key ARN, key ID, alias <PERSON>N, or alias name for the KMS key.</p> <ul> <li> <p>If the configuration is for an existing Amazon RDS DB cluster snapshot and you do not specify the <code>kmsKeyId</code>, or you specify an empty string, then the access preview uses the existing <code>kmsKeyId</code> of the snapshot.</p> </li> <li> <p>If the access preview is for a new resource and you do not specify the specify the <code>kmsKeyId</code>, then the access preview considers the snapshot as unencrypted.</p> </li> </ul>"}}, "documentation": "<p>The proposed access control configuration for an Amazon RDS DB cluster snapshot. You can propose a configuration for a new Amazon RDS DB cluster snapshot or an Amazon RDS DB cluster snapshot that you own by specifying the <code>RdsDbClusterSnapshotAttributeValue</code> and optional KMS encryption key. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonRDS/latest/APIReference/API_ModifyDBClusterSnapshotAttribute.html\">ModifyDBClusterSnapshotAttribute</a>.</p>"}, "RdsDbClusterSnapshotKmsKeyId": {"type": "string"}, "RdsDbSnapshotAccountId": {"type": "string"}, "RdsDbSnapshotAccountIdsList": {"type": "list", "member": {"shape": "RdsDbSnapshotAccountId"}}, "RdsDbSnapshotAttributeName": {"type": "string"}, "RdsDbSnapshotAttributeValue": {"type": "structure", "members": {"accountIds": {"shape": "RdsDbSnapshotAccountIdsList", "documentation": "<p>The Amazon Web Services account IDs that have access to the manual Amazon RDS DB snapshot. If the value <code>all</code> is specified, then the Amazon RDS DB snapshot is public and can be copied or restored by all Amazon Web Services accounts.</p> <ul> <li> <p>If the configuration is for an existing Amazon RDS DB snapshot and you do not specify the <code>accountIds</code> in <code>RdsDbSnapshotAttributeValue</code>, then the access preview uses the existing shared <code>accountIds</code> for the snapshot.</p> </li> <li> <p>If the access preview is for a new resource and you do not specify the specify the <code>accountIds</code> in <code>RdsDbSnapshotAttributeValue</code>, then the access preview considers the snapshot without any attributes.</p> </li> <li> <p>To propose deletion of an existing shared <code>accountIds</code>, you can specify an empty list for <code>accountIds</code> in the <code>RdsDbSnapshotAttributeValue</code>.</p> </li> </ul>"}}, "documentation": "<p>The name and values of a manual Amazon RDS DB snapshot attribute. Manual DB snapshot attributes are used to authorize other Amazon Web Services accounts to restore a manual DB snapshot.</p>", "union": true}, "RdsDbSnapshotAttributesMap": {"type": "map", "key": {"shape": "RdsDbSnapshotAttributeName"}, "value": {"shape": "RdsDbSnapshotAttributeValue"}}, "RdsDbSnapshotConfiguration": {"type": "structure", "members": {"attributes": {"shape": "RdsDbSnapshotAttributesMap", "documentation": "<p>The names and values of manual DB snapshot attributes. Manual DB snapshot attributes are used to authorize other Amazon Web Services accounts to restore a manual DB snapshot. The only valid value for <code>attributeName</code> for the attribute map is restore.</p>"}, "kmsKeyId": {"shape": "RdsDbSnapshotKmsKeyId", "documentation": "<p>The KMS key identifier for an encrypted Amazon RDS DB snapshot. The KMS key identifier is the key ARN, key ID, alias <PERSON>N, or alias name for the KMS key.</p> <ul> <li> <p>If the configuration is for an existing Amazon RDS DB snapshot and you do not specify the <code>kmsKeyId</code>, or you specify an empty string, then the access preview uses the existing <code>kmsKeyId</code> of the snapshot.</p> </li> <li> <p>If the access preview is for a new resource and you do not specify the specify the <code>kmsKeyId</code>, then the access preview considers the snapshot as unencrypted.</p> </li> </ul>"}}, "documentation": "<p>The proposed access control configuration for an Amazon RDS DB snapshot. You can propose a configuration for a new Amazon RDS DB snapshot or an Amazon RDS DB snapshot that you own by specifying the <code>RdsDbSnapshotAttributeValue</code> and optional KMS encryption key. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonRDS/latest/APIReference/API_ModifyDBSnapshotAttribute.html\">ModifyDBSnapshotAttribute</a>.</p>"}, "RdsDbSnapshotKmsKeyId": {"type": "string"}, "ReasonCode": {"type": "string", "enum": ["AWS_SERVICE_ACCESS_DISABLED", "DELEGATED_ADMINISTRATOR_DEREGISTERED", "ORGANIZATION_DELETED", "SERVICE_LINKED_ROLE_CREATION_FAILED"]}, "ReasonSummary": {"type": "structure", "members": {"description": {"shape": "String", "documentation": "<p>A description of the reasoning of a result of checking for access.</p>"}, "statementIndex": {"shape": "Integer", "documentation": "<p>The index number of the reason statement.</p>"}, "statementId": {"shape": "String", "documentation": "<p>The identifier for the reason statement.</p>"}}, "documentation": "<p>Contains information about the reasoning why a check for access passed or failed.</p>"}, "ReasonSummaryList": {"type": "list", "member": {"shape": "ReasonSummary"}}, "RecommendationError": {"type": "structure", "required": ["code", "message"], "members": {"code": {"shape": "String", "documentation": "<p>The error code for a failed retrieval of a recommendation for a finding.</p>"}, "message": {"shape": "String", "documentation": "<p>The error message for a failed retrieval of a recommendation for a finding.</p>"}}, "documentation": "<p>Contains information about the reason that the retrieval of a recommendation for a finding failed.</p>"}, "RecommendationType": {"type": "string", "enum": ["UnusedPermissionRecommendation"]}, "RecommendedRemediationAction": {"type": "string", "enum": ["CREATE_POLICY", "DETACH_POLICY"]}, "RecommendedStep": {"type": "structure", "members": {"unusedPermissionsRecommendedStep": {"shape": "UnusedPermissionsRecommendedStep", "documentation": "<p>A recommended step for an unused permissions finding.</p>"}}, "documentation": "<p>Contains information about a recommended step for an unused access analyzer finding.</p>", "union": true}, "RecommendedStepList": {"type": "list", "member": {"shape": "RecommendedStep"}}, "RegionList": {"type": "list", "member": {"shape": "String"}}, "Resource": {"type": "string", "max": 2048, "min": 0}, "ResourceArn": {"type": "string", "pattern": "arn:[^:]*:[^:]*:[^:]*:[^:]*:.*"}, "ResourceArnsList": {"type": "list", "member": {"shape": "String"}}, "ResourceControlPolicyRestriction": {"type": "string", "enum": ["APPLICABLE", "FAILED_TO_EVALUATE_RCP", "NOT_APPLICABLE", "APPLIED"]}, "ResourceNotFoundException": {"type": "structure", "required": ["message", "resourceId", "resourceType"], "members": {"message": {"shape": "String"}, "resourceId": {"shape": "String", "documentation": "<p>The ID of the resource.</p>"}, "resourceType": {"shape": "String", "documentation": "<p>The type of the resource.</p>"}}, "documentation": "<p>The specified resource could not be found.</p>", "error": {"httpStatusCode": 404, "senderFault": true}, "exception": true}, "ResourceType": {"type": "string", "enum": ["AWS::S3::<PERSON><PERSON>", "AWS::IAM::Role", "AWS::SQS::Queue", "AWS::Lambda::Function", "AWS::Lambda::LayerVersion", "AWS::KMS::Key", "AWS::<PERSON>Manager::Secret", "AWS::EFS::FileSystem", "AWS::EC2::Snapshot", "AWS::ECR::Repository", "AWS::RDS::DBSnapshot", "AWS::RDS::DBClusterSnapshot", "AWS::SNS::Topic", "AWS::S3Express::DirectoryBucket", "AWS::DynamoDB::Table", "AWS::DynamoDB::Stream", "AWS::IAM::User"]}, "ResourceTypeDetails": {"type": "structure", "members": {"totalActivePublic": {"shape": "Integer", "documentation": "<p>The total number of active public findings for the resource type.</p>"}, "totalActiveCrossAccount": {"shape": "Integer", "documentation": "<p>The total number of active cross-account findings for the resource type.</p>"}}, "documentation": "<p>Contains information about the total number of active cross-account and public findings for a resource type of an external access analyzer.</p>"}, "ResourceTypeList": {"type": "list", "member": {"shape": "ResourceType"}}, "ResourceTypeStatisticsMap": {"type": "map", "key": {"shape": "ResourceType"}, "value": {"shape": "ResourceTypeDetails"}}, "RetiringPrincipal": {"type": "string"}, "RoleArn": {"type": "string", "pattern": "arn:[^:]*:iam::[^:]*:role/.{1,576}"}, "S3AccessPointConfiguration": {"type": "structure", "members": {"accessPointPolicy": {"shape": "AccessPointPolicy", "documentation": "<p>The access point or multi-region access point policy.</p>"}, "publicAccessBlock": {"shape": "S3PublicAccessBlockConfiguration", "documentation": "<p>The proposed <code>S3PublicAccessBlock</code> configuration to apply to this Amazon S3 access point or multi-region access point.</p>"}, "networkOrigin": {"shape": "NetworkOriginConfiguration", "documentation": "<p>The proposed <code>Internet</code> and <code>VpcConfiguration</code> to apply to this Amazon S3 access point. <code>VpcConfiguration</code> does not apply to multi-region access points. If the access preview is for a new resource and neither is specified, the access preview uses <code>Internet</code> for the network origin. If the access preview is for an existing resource and neither is specified, the access preview uses the existing network origin.</p>"}}, "documentation": "<p>The configuration for an Amazon S3 access point or multi-region access point for the bucket. You can propose up to 10 access points or multi-region access points per bucket. If the proposed Amazon S3 access point configuration is for an existing bucket, the access preview uses the proposed access point configuration in place of the existing access points. To propose an access point without a policy, you can provide an empty string as the access point policy. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/creating-access-points.html\">Creating access points</a>. For more information about access point policy limits, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/access-points-restrictions-limitations.html\">Access points restrictions and limitations</a>.</p>"}, "S3AccessPointConfigurationsMap": {"type": "map", "key": {"shape": "AccessPointArn"}, "value": {"shape": "S3AccessPointConfiguration"}}, "S3BucketAclGrantConfiguration": {"type": "structure", "required": ["permission", "grantee"], "members": {"permission": {"shape": "AclPermission", "documentation": "<p>The permissions being granted.</p>"}, "grantee": {"shape": "AclGrantee", "documentation": "<p>The grantee to whom you’re assigning access rights.</p>"}}, "documentation": "<p>A proposed access control list grant configuration for an Amazon S3 bucket. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/acl-overview.html#setting-acls\">How to Specify an ACL</a>.</p>"}, "S3BucketAclGrantConfigurationsList": {"type": "list", "member": {"shape": "S3BucketAclGrantConfiguration"}}, "S3BucketConfiguration": {"type": "structure", "members": {"bucketPolicy": {"shape": "S3BucketPolicy", "documentation": "<p>The proposed bucket policy for the Amazon S3 bucket.</p>"}, "bucketAclGrants": {"shape": "S3BucketAclGrantConfigurationsList", "documentation": "<p>The proposed list of ACL grants for the Amazon S3 bucket. You can propose up to 100 ACL grants per bucket. If the proposed grant configuration is for an existing bucket, the access preview uses the proposed list of grant configurations in place of the existing grants. Otherwise, the access preview uses the existing grants for the bucket.</p>"}, "bucketPublicAccessBlock": {"shape": "S3PublicAccessBlockConfiguration", "documentation": "<p>The proposed block public access configuration for the Amazon S3 bucket.</p>"}, "accessPoints": {"shape": "S3AccessPointConfigurationsMap", "documentation": "<p>The configuration of Amazon S3 access points or multi-region access points for the bucket. You can propose up to 10 new access points per bucket.</p>"}}, "documentation": "<p>Proposed access control configuration for an Amazon S3 bucket. You can propose a configuration for a new Amazon S3 bucket or an existing Amazon S3 bucket that you own by specifying the Amazon S3 bucket policy, bucket ACLs, bucket BPA settings, Amazon S3 access points, and multi-region access points attached to the bucket. If the configuration is for an existing Amazon S3 bucket and you do not specify the Amazon S3 bucket policy, the access preview uses the existing policy attached to the bucket. If the access preview is for a new resource and you do not specify the Amazon S3 bucket policy, the access preview assumes a bucket without a policy. To propose deletion of an existing bucket policy, you can specify an empty string. For more information about bucket policy limits, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/example-bucket-policies.html\">Bucket Policy Examples</a>.</p>"}, "S3BucketPolicy": {"type": "string"}, "S3ExpressDirectoryAccessPointArn": {"type": "string", "pattern": "arn:[^:]*:s3express:[^:]*:[^:]*:accesspoint/.*"}, "S3ExpressDirectoryAccessPointConfiguration": {"type": "structure", "members": {"accessPointPolicy": {"shape": "AccessPointPolicy", "documentation": "<p>The proposed access point policy for an Amazon S3 directory bucket access point.</p>"}, "networkOrigin": {"shape": "NetworkOriginConfiguration"}}, "documentation": "<p>Proposed configuration for an access point attached to an Amazon S3 directory bucket. You can propose up to 10 access points per bucket. If the proposed access point configuration is for an existing Amazon S3 directory bucket, the access preview uses the proposed access point configuration in place of the existing access points. To propose an access point without a policy, you can provide an empty string as the access point policy. For more information about access points for Amazon S3 directory buckets, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/access-points-directory-buckets.html\">Managing access to directory buckets with access points</a> in the Amazon Simple Storage Service User Guide.</p>"}, "S3ExpressDirectoryAccessPointConfigurationsMap": {"type": "map", "key": {"shape": "S3ExpressDirectoryAccessPointArn"}, "value": {"shape": "S3ExpressDirectoryAccessPointConfiguration"}}, "S3ExpressDirectoryBucketConfiguration": {"type": "structure", "members": {"bucketPolicy": {"shape": "S3ExpressDirectoryBucketPolicy", "documentation": "<p>The proposed bucket policy for the Amazon S3 directory bucket.</p>"}, "accessPoints": {"shape": "S3ExpressDirectoryAccessPointConfigurationsMap", "documentation": "<p>The proposed access points for the Amazon S3 directory bucket.</p>"}}, "documentation": "<p>Proposed access control configuration for an Amazon S3 directory bucket. You can propose a configuration for a new Amazon S3 directory bucket or an existing Amazon S3 directory bucket that you own by specifying the Amazon S3 bucket policy. If the configuration is for an existing Amazon S3 directory bucket and you do not specify the Amazon S3 bucket policy, the access preview uses the existing policy attached to the directory bucket. If the access preview is for a new resource and you do not specify the Amazon S3 bucket policy, the access preview assumes an directory bucket without a policy. To propose deletion of an existing bucket policy, you can specify an empty string. For more information about Amazon S3 directory bucket policies, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/s3-express-security-iam-example-bucket-policies.html\">Example bucket policies for directory buckets</a> in the Amazon Simple Storage Service User Guide.</p>"}, "S3ExpressDirectoryBucketPolicy": {"type": "string"}, "S3PublicAccessBlockConfiguration": {"type": "structure", "required": ["ignorePublicAcls", "restrictPublicBuckets"], "members": {"ignorePublicAcls": {"shape": "Boolean", "documentation": "<p> Specifies whether Amazon S3 should ignore public ACLs for this bucket and objects in this bucket. </p>"}, "restrictPublicBuckets": {"shape": "Boolean", "documentation": "<p> Specifies whether Amazon S3 should restrict public bucket policies for this bucket. </p>"}}, "documentation": "<p>The <code>PublicAccessBlock</code> configuration to apply to this Amazon S3 bucket. If the proposed configuration is for an existing Amazon S3 bucket and the configuration is not specified, the access preview uses the existing setting. If the proposed configuration is for a new bucket and the configuration is not specified, the access preview uses <code>false</code>. If the proposed configuration is for a new access point or multi-region access point and the access point BPA configuration is not specified, the access preview uses <code>true</code>. For more information, see <a href=\"https://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-s3-bucket-publicaccessblockconfiguration.html\">PublicAccessBlockConfiguration</a>. </p>"}, "SecretsManagerSecretConfiguration": {"type": "structure", "members": {"kmsKeyId": {"shape": "SecretsManagerSecretKmsId", "documentation": "<p>The proposed ARN, key ID, or alias of the KMS key.</p>"}, "secretPolicy": {"shape": "SecretsManagerSecretPolicy", "documentation": "<p>The proposed resource policy defining who can access or manage the secret.</p>"}}, "documentation": "<p>The configuration for a Secrets Manager secret. For more information, see <a href=\"https://docs.aws.amazon.com/secretsmanager/latest/apireference/API_CreateSecret.html\">CreateSecret</a>.</p> <p>You can propose a configuration for a new secret or an existing secret that you own by specifying the secret policy and optional KMS encryption key. If the configuration is for an existing secret and you do not specify the secret policy, the access preview uses the existing policy for the secret. If the access preview is for a new resource and you do not specify the policy, the access preview assumes a secret without a policy. To propose deletion of an existing policy, you can specify an empty string. If the proposed configuration is for a new secret and you do not specify the KMS key ID, the access preview uses the Amazon Web Services managed key <code>aws/secretsmanager</code>. If you specify an empty string for the KMS key ID, the access preview uses the Amazon Web Services managed key of the Amazon Web Services account. For more information about secret policy limits, see <a href=\"https://docs.aws.amazon.com/secretsmanager/latest/userguide/reference_limits.html\">Quotas for Secrets Manager.</a>.</p>"}, "SecretsManagerSecretKmsId": {"type": "string"}, "SecretsManagerSecretPolicy": {"type": "string"}, "ServiceControlPolicyRestriction": {"type": "string", "enum": ["APPLICABLE", "FAILED_TO_EVALUATE_SCP", "NOT_APPLICABLE", "APPLIED"]}, "ServiceQuotaExceededException": {"type": "structure", "required": ["message", "resourceId", "resourceType"], "members": {"message": {"shape": "String"}, "resourceId": {"shape": "String", "documentation": "<p>The resource ID.</p>"}, "resourceType": {"shape": "String", "documentation": "<p>The resource type.</p>"}}, "documentation": "<p>Service quote met error.</p>", "error": {"httpStatusCode": 402, "senderFault": true}, "exception": true}, "SharedViaList": {"type": "list", "member": {"shape": "String"}}, "SnsTopicConfiguration": {"type": "structure", "members": {"topicPolicy": {"shape": "SnsTopicPolicy", "documentation": "<p>The JSON policy text that defines who can access an Amazon SNS topic. For more information, see <a href=\"https://docs.aws.amazon.com/sns/latest/dg/sns-access-policy-use-cases.html\">Example cases for Amazon SNS access control</a> in the <i>Amazon SNS Developer Guide</i>.</p>"}}, "documentation": "<p>The proposed access control configuration for an Amazon SNS topic. You can propose a configuration for a new Amazon SNS topic or an existing Amazon SNS topic that you own by specifying the policy. If the configuration is for an existing Amazon SNS topic and you do not specify the Amazon SNS policy, then the access preview uses the existing Amazon SNS policy for the topic. If the access preview is for a new resource and you do not specify the policy, then the access preview assumes an Amazon SNS topic without a policy. To propose deletion of an existing Amazon SNS topic policy, you can specify an empty string for the Amazon SNS policy. For more information, see <a href=\"https://docs.aws.amazon.com/sns/latest/api/API_Topic.html\">Topic</a>.</p>"}, "SnsTopicPolicy": {"type": "string", "max": 30720, "min": 0}, "SortCriteria": {"type": "structure", "members": {"attributeName": {"shape": "String", "documentation": "<p>The name of the attribute to sort on.</p>"}, "orderBy": {"shape": "OrderBy", "documentation": "<p>The sort order, ascending or descending.</p>"}}, "documentation": "<p>The criteria used to sort.</p>"}, "Span": {"type": "structure", "required": ["start", "end"], "members": {"start": {"shape": "Position", "documentation": "<p>The start position of the span (inclusive).</p>"}, "end": {"shape": "Position", "documentation": "<p>The end position of the span (exclusive).</p>"}}, "documentation": "<p>A span in a policy. The span consists of a start position (inclusive) and end position (exclusive).</p>"}, "SqsQueueConfiguration": {"type": "structure", "members": {"queuePolicy": {"shape": "SqsQueuePolicy", "documentation": "<p> The proposed resource policy for the Amazon SQS queue. </p>"}}, "documentation": "<p>The proposed access control configuration for an Amazon SQS queue. You can propose a configuration for a new Amazon SQS queue or an existing Amazon SQS queue that you own by specifying the Amazon SQS policy. If the configuration is for an existing Amazon SQS queue and you do not specify the Amazon SQS policy, the access preview uses the existing Amazon SQS policy for the queue. If the access preview is for a new resource and you do not specify the policy, the access preview assumes an Amazon SQS queue without a policy. To propose deletion of an existing Amazon SQS queue policy, you can specify an empty string for the Amazon SQS policy. For more information about Amazon SQS policy limits, see <a href=\"https://docs.aws.amazon.com/AWSSimpleQueueService/latest/SQSDeveloperGuide/quotas-policies.html\">Quotas related to policies</a>.</p>"}, "SqsQueuePolicy": {"type": "string"}, "StartPolicyGenerationRequest": {"type": "structure", "required": ["policyGenerationDetails"], "members": {"policyGenerationDetails": {"shape": "PolicyGenerationDetails", "documentation": "<p>Contains the ARN of the IAM entity (user or role) for which you are generating a policy.</p>"}, "cloudTrailDetails": {"shape": "CloudTrailDetails", "documentation": "<p>A <code>CloudTrailDetails</code> object that contains details about a <code>Trail</code> that you want to analyze to generate policies.</p>"}, "clientToken": {"shape": "String", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency of the request. Idempotency ensures that an API request completes only once. With an idempotent request, if the original request completes successfully, the subsequent retries with the same client token return the result from the original successful request and they have no additional effect.</p> <p>If you do not specify a client token, one is automatically generated by the Amazon Web Services SDK.</p>", "idempotencyToken": true}}}, "StartPolicyGenerationResponse": {"type": "structure", "required": ["jobId"], "members": {"jobId": {"shape": "JobId", "documentation": "<p>The <code>JobId</code> that is returned by the <code>StartPolicyGeneration</code> operation. The <code>JobId</code> can be used with <code>GetGeneratedPolicy</code> to retrieve the generated policies or used with <code>CancelPolicyGeneration</code> to cancel the policy generation request.</p>"}}}, "StartResourceScanRequest": {"type": "structure", "required": ["analyzerArn", "resourceArn"], "members": {"analyzerArn": {"shape": "AnalyzerArn", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/access-analyzer-getting-started.html#permission-resources\">ARN of the analyzer</a> to use to scan the policies applied to the specified resource.</p>"}, "resourceArn": {"shape": "ResourceArn", "documentation": "<p>The ARN of the resource to scan.</p>"}, "resourceOwnerAccount": {"shape": "String", "documentation": "<p>The Amazon Web Services account ID that owns the resource. For most Amazon Web Services resources, the owning account is the account in which the resource was created.</p>"}}, "documentation": "<p>Starts a scan of the policies applied to the specified resource.</p>"}, "Status": {"type": "string", "enum": ["SUCCEEDED", "FAILED", "IN_PROGRESS"]}, "StatusReason": {"type": "structure", "required": ["code"], "members": {"code": {"shape": "ReasonCode", "documentation": "<p>The reason code for the current status of the analyzer.</p>"}}, "documentation": "<p>Provides more details about the current status of the analyzer. For example, if the creation for the analyzer fails, a <code>Failed</code> status is returned. For an analyzer with organization as the type, this failure can be due to an issue with creating the service-linked roles required in the member accounts of the Amazon Web Services organization.</p>"}, "String": {"type": "string"}, "Substring": {"type": "structure", "required": ["start", "length"], "members": {"start": {"shape": "Integer", "documentation": "<p>The start index of the substring, starting from 0.</p>"}, "length": {"shape": "Integer", "documentation": "<p>The length of the substring.</p>"}}, "documentation": "<p>A reference to a substring of a literal string in a JSON document.</p>"}, "TagKeys": {"type": "list", "member": {"shape": "String"}}, "TagResourceRequest": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {"shape": "String", "documentation": "<p>The ARN of the resource to add the tag to.</p>", "location": "uri", "locationName": "resourceArn"}, "tags": {"shape": "TagsMap", "documentation": "<p>The tags to add to the resource.</p>"}}, "documentation": "<p>Adds a tag to the specified resource.</p>"}, "TagResourceResponse": {"type": "structure", "members": {}, "documentation": "<p>The response to the request.</p>"}, "TagsList": {"type": "list", "member": {"shape": "TagsMap"}}, "TagsMap": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "String"}}, "ThrottlingException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}, "retryAfterSeconds": {"shape": "Integer", "documentation": "<p>The seconds to wait to retry.</p>", "location": "header", "locationName": "Retry-After"}}, "documentation": "<p>Throttling limit exceeded error.</p>", "error": {"httpStatusCode": 429, "senderFault": true}, "exception": true, "retryable": {"throttling": true}}, "Timestamp": {"type": "timestamp", "timestampFormat": "iso8601"}, "Token": {"type": "string"}, "Trail": {"type": "structure", "required": ["cloudTrailArn"], "members": {"cloudTrailArn": {"shape": "CloudTrailArn", "documentation": "<p>Specifies the ARN of the trail. The format of a trail ARN is <code>arn:aws:cloudtrail:us-east-2:123456789012:trail/MyTrail</code>.</p>"}, "regions": {"shape": "RegionList", "documentation": "<p>A list of regions to get CloudTrail data from and analyze to generate a policy.</p>"}, "allRegions": {"shape": "Boolean", "documentation": "<p>Possible values are <code>true</code> or <code>false</code>. If set to <code>true</code>, IAM Access Analyzer retrieves CloudTrail data from all regions to analyze and generate a policy.</p>"}}, "documentation": "<p>Contains details about the CloudTrail trail being analyzed to generate a policy.</p>"}, "TrailList": {"type": "list", "member": {"shape": "Trail"}}, "TrailProperties": {"type": "structure", "required": ["cloudTrailArn"], "members": {"cloudTrailArn": {"shape": "CloudTrailArn", "documentation": "<p>Specifies the ARN of the trail. The format of a trail ARN is <code>arn:aws:cloudtrail:us-east-2:123456789012:trail/MyTrail</code>.</p>"}, "regions": {"shape": "RegionList", "documentation": "<p>A list of regions to get CloudTrail data from and analyze to generate a policy.</p>"}, "allRegions": {"shape": "Boolean", "documentation": "<p>Possible values are <code>true</code> or <code>false</code>. If set to <code>true</code>, IAM Access Analyzer retrieves CloudTrail data from all regions to analyze and generate a policy.</p>"}}, "documentation": "<p>Contains details about the CloudTrail trail being analyzed to generate a policy.</p>"}, "TrailPropertiesList": {"type": "list", "member": {"shape": "TrailProperties"}}, "Type": {"type": "string", "enum": ["ACCOUNT", "ORGANIZATION", "ACCOUNT_UNUSED_ACCESS", "ORGANIZATION_UNUSED_ACCESS", "ACCOUNT_INTERNAL_ACCESS", "ORGANIZATION_INTERNAL_ACCESS"]}, "UnprocessableEntityException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The specified entity could not be processed.</p>", "error": {"httpStatusCode": 422, "senderFault": true}, "exception": true, "retryable": {"throttling": false}}, "UntagResourceRequest": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {"shape": "String", "documentation": "<p>The ARN of the resource to remove the tag from.</p>", "location": "uri", "locationName": "resourceArn"}, "tagKeys": {"shape": "TagKeys", "documentation": "<p>The key for the tag to add.</p>", "location": "querystring", "locationName": "tagKeys"}}, "documentation": "<p>Removes a tag from the specified resource.</p>"}, "UntagResourceResponse": {"type": "structure", "members": {}, "documentation": "<p>The response to the request.</p>"}, "UnusedAccessConfiguration": {"type": "structure", "members": {"unusedAccessAge": {"shape": "Integer", "documentation": "<p>The specified access age in days for which to generate findings for unused access. For example, if you specify 90 days, the analyzer will generate findings for IAM entities within the accounts of the selected organization for any access that hasn't been used in 90 or more days since the analyzer's last scan. You can choose a value between 1 and 365 days.</p>"}, "analysisRule": {"shape": "AnalysisRule"}}, "documentation": "<p>Contains information about an unused access analyzer.</p>"}, "UnusedAccessFindingsStatistics": {"type": "structure", "members": {"unusedAccessTypeStatistics": {"shape": "UnusedAccessTypeStatisticsList", "documentation": "<p>A list of details about the total number of findings for each type of unused access for the analyzer. </p>"}, "topAccounts": {"shape": "AccountAggregations", "documentation": "<p>A list of one to ten Amazon Web Services accounts that have the most active findings for the unused access analyzer.</p>"}, "totalActiveFindings": {"shape": "Integer", "documentation": "<p>The total number of active findings for the unused access analyzer.</p>"}, "totalArchivedFindings": {"shape": "Integer", "documentation": "<p>The total number of archived findings for the unused access analyzer.</p>"}, "totalResolvedFindings": {"shape": "Integer", "documentation": "<p>The total number of resolved findings for the unused access analyzer.</p>"}}, "documentation": "<p>Provides aggregate statistics about the findings for the specified unused access analyzer.</p>"}, "UnusedAccessTypeStatistics": {"type": "structure", "members": {"unusedAccessType": {"shape": "String", "documentation": "<p>The type of unused access.</p>"}, "total": {"shape": "Integer", "documentation": "<p>The total number of findings for the specified unused access type.</p>"}}, "documentation": "<p>Contains information about the total number of findings for a type of unused access.</p>"}, "UnusedAccessTypeStatisticsList": {"type": "list", "member": {"shape": "UnusedAccessTypeStatistics"}}, "UnusedAction": {"type": "structure", "required": ["action"], "members": {"action": {"shape": "String", "documentation": "<p>The action for which the unused access finding was generated.</p>"}, "lastAccessed": {"shape": "Timestamp", "documentation": "<p>The time at which the action was last accessed.</p>"}}, "documentation": "<p>Contains information about an unused access finding for an action. IAM Access Analyzer charges for unused access analysis based on the number of IAM roles and users analyzed per month. For more details on pricing, see <a href=\"https://aws.amazon.com/iam/access-analyzer/pricing\">IAM Access Analyzer pricing</a>.</p>"}, "UnusedActionList": {"type": "list", "member": {"shape": "UnusedAction"}}, "UnusedIamRoleDetails": {"type": "structure", "members": {"lastAccessed": {"shape": "Timestamp", "documentation": "<p>The time at which the role was last accessed.</p>"}}, "documentation": "<p>Contains information about an unused access finding for an IAM role. IAM Access Analyzer charges for unused access analysis based on the number of IAM roles and users analyzed per month. For more details on pricing, see <a href=\"https://aws.amazon.com/iam/access-analyzer/pricing\">IAM Access Analyzer pricing</a>.</p>"}, "UnusedIamUserAccessKeyDetails": {"type": "structure", "required": ["accessKeyId"], "members": {"accessKeyId": {"shape": "String", "documentation": "<p>The ID of the access key for which the unused access finding was generated.</p>"}, "lastAccessed": {"shape": "Timestamp", "documentation": "<p>The time at which the access key was last accessed.</p>"}}, "documentation": "<p>Contains information about an unused access finding for an IAM user access key. IAM Access Analyzer charges for unused access analysis based on the number of IAM roles and users analyzed per month. For more details on pricing, see <a href=\"https://aws.amazon.com/iam/access-analyzer/pricing\">IAM Access Analyzer pricing</a>.</p>"}, "UnusedIamUserPasswordDetails": {"type": "structure", "members": {"lastAccessed": {"shape": "Timestamp", "documentation": "<p>The time at which the password was last accessed.</p>"}}, "documentation": "<p>Contains information about an unused access finding for an IAM user password. IAM Access Analyzer charges for unused access analysis based on the number of IAM roles and users analyzed per month. For more details on pricing, see <a href=\"https://aws.amazon.com/iam/access-analyzer/pricing\">IAM Access Analyzer pricing</a>.</p>"}, "UnusedPermissionDetails": {"type": "structure", "required": ["serviceNamespace"], "members": {"actions": {"shape": "UnusedActionList", "documentation": "<p>A list of unused actions for which the unused access finding was generated.</p>"}, "serviceNamespace": {"shape": "String", "documentation": "<p>The namespace of the Amazon Web Services service that contains the unused actions.</p>"}, "lastAccessed": {"shape": "Timestamp", "documentation": "<p>The time at which the permission was last accessed.</p>"}}, "documentation": "<p>Contains information about an unused access finding for a permission. IAM Access Analyzer charges for unused access analysis based on the number of IAM roles and users analyzed per month. For more details on pricing, see <a href=\"https://aws.amazon.com/iam/access-analyzer/pricing\">IAM Access Analyzer pricing</a>.</p>"}, "UnusedPermissionsRecommendedStep": {"type": "structure", "required": ["recommendedAction"], "members": {"policyUpdatedAt": {"shape": "Timestamp", "documentation": "<p>The time at which the existing policy for the unused permissions finding was last updated.</p>"}, "recommendedAction": {"shape": "RecommendedRemediationAction", "documentation": "<p>A recommendation of whether to create or detach a policy for an unused permissions finding.</p>"}, "recommendedPolicy": {"shape": "String", "documentation": "<p>If the recommended action for the unused permissions finding is to replace the existing policy, the contents of the recommended policy to replace the policy specified in the <code>existingPolicyId</code> field.</p>"}, "existingPolicyId": {"shape": "String", "documentation": "<p>If the recommended action for the unused permissions finding is to detach a policy, the ID of an existing policy to be detached.</p>"}}, "documentation": "<p>Contains information about the action to take for a policy in an unused permissions finding.</p>"}, "UpdateAnalyzerRequest": {"type": "structure", "required": ["analyzerName"], "members": {"analyzerName": {"shape": "Name", "documentation": "<p>The name of the analyzer to modify.</p>", "location": "uri", "locationName": "analyzerName"}, "configuration": {"shape": "AnalyzerConfiguration"}}}, "UpdateAnalyzerResponse": {"type": "structure", "members": {"configuration": {"shape": "AnalyzerConfiguration"}}}, "UpdateArchiveRuleRequest": {"type": "structure", "required": ["analyzerName", "ruleName", "filter"], "members": {"analyzerName": {"shape": "Name", "documentation": "<p>The name of the analyzer to update the archive rules for.</p>", "location": "uri", "locationName": "analyzerName"}, "ruleName": {"shape": "Name", "documentation": "<p>The name of the rule to update.</p>", "location": "uri", "locationName": "ruleName"}, "filter": {"shape": "FilterCriteriaMap", "documentation": "<p>A filter to match for the rules to update. Only rules that match the filter are updated.</p>"}, "clientToken": {"shape": "String", "documentation": "<p>A client token.</p>", "idempotencyToken": true}}, "documentation": "<p>Updates the specified archive rule.</p>"}, "UpdateFindingsRequest": {"type": "structure", "required": ["analyzerArn", "status"], "members": {"analyzerArn": {"shape": "AnalyzerArn", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/access-analyzer-getting-started.html#permission-resources\">ARN of the analyzer</a> that generated the findings to update.</p>"}, "status": {"shape": "FindingStatusUpdate", "documentation": "<p>The state represents the action to take to update the finding Status. Use <code>ARCHIVE</code> to change an Active finding to an Archived finding. Use <code>ACTIVE</code> to change an Archived finding to an Active finding.</p>"}, "ids": {"shape": "FindingIdList", "documentation": "<p>The IDs of the findings to update.</p>"}, "resourceArn": {"shape": "ResourceArn", "documentation": "<p>The ARN of the resource identified in the finding.</p>"}, "clientToken": {"shape": "String", "documentation": "<p>A client token.</p>", "idempotencyToken": true}}, "documentation": "<p>Updates findings with the new values provided in the request.</p>"}, "ValidatePolicyFinding": {"type": "structure", "required": ["findingDetails", "findingType", "issueCode", "learnMoreLink", "locations"], "members": {"findingDetails": {"shape": "String", "documentation": "<p>A localized message that explains the finding and provides guidance on how to address it.</p>"}, "findingType": {"shape": "ValidatePolicyFindingType", "documentation": "<p>The impact of the finding.</p> <p>Security warnings report when the policy allows access that we consider overly permissive.</p> <p>Errors report when a part of the policy is not functional.</p> <p>Warnings report non-security issues when a policy does not conform to policy writing best practices.</p> <p>Suggestions recommend stylistic improvements in the policy that do not impact access.</p>"}, "issueCode": {"shape": "IssueCode", "documentation": "<p>The issue code provides an identifier of the issue associated with this finding.</p>"}, "learnMoreLink": {"shape": "LearnMoreLink", "documentation": "<p>A link to additional documentation about the type of finding.</p>"}, "locations": {"shape": "LocationList", "documentation": "<p>The list of locations in the policy document that are related to the finding. The issue code provides a summary of an issue identified by the finding.</p>"}}, "documentation": "<p>A finding in a policy. Each finding is an actionable recommendation that can be used to improve the policy.</p>"}, "ValidatePolicyFindingList": {"type": "list", "member": {"shape": "ValidatePolicyFinding"}}, "ValidatePolicyFindingType": {"type": "string", "enum": ["ERROR", "SECURITY_WARNING", "SUGGESTION", "WARNING"]}, "ValidatePolicyRequest": {"type": "structure", "required": ["policyDocument", "policyType"], "members": {"locale": {"shape": "Locale", "documentation": "<p>The locale to use for localizing the findings.</p>"}, "maxResults": {"shape": "Integer", "documentation": "<p>The maximum number of results to return in the response.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "Token", "documentation": "<p>A token used for pagination of results returned.</p>", "location": "querystring", "locationName": "nextToken"}, "policyDocument": {"shape": "PolicyDocument", "documentation": "<p>The JSON policy document to use as the content for the policy.</p>"}, "policyType": {"shape": "PolicyType", "documentation": "<p>The type of policy to validate. Identity policies grant permissions to IAM principals. Identity policies include managed and inline policies for IAM roles, users, and groups.</p> <p>Resource policies grant permissions on Amazon Web Services resources. Resource policies include trust policies for IAM roles and bucket policies for Amazon S3 buckets. You can provide a generic input such as identity policy or resource policy or a specific input such as managed policy or Amazon S3 bucket policy. </p> <p>Service control policies (SCPs) are a type of organization policy attached to an Amazon Web Services organization, organizational unit (OU), or an account.</p>"}, "validatePolicyResourceType": {"shape": "ValidatePolicyResourceType", "documentation": "<p>The type of resource to attach to your resource policy. Specify a value for the policy validation resource type only if the policy type is <code>RESOURCE_POLICY</code>. For example, to validate a resource policy to attach to an Amazon S3 bucket, you can choose <code>AWS::S3::Bucket</code> for the policy validation resource type.</p> <p>For resource types not supported as valid values, IAM Access Analyzer runs policy checks that apply to all resource policies. For example, to validate a resource policy to attach to a KMS key, do not specify a value for the policy validation resource type and IAM Access Analyzer will run policy checks that apply to all resource policies.</p>"}}}, "ValidatePolicyResourceType": {"type": "string", "enum": ["AWS::S3::<PERSON><PERSON>", "AWS::S3::AccessPoint", "AWS::S3::MultiRegionAccessPoint", "AWS::S3ObjectLambda::AccessPoint", "AWS::IAM::AssumeRolePolicyDocument", "AWS::DynamoDB::Table"]}, "ValidatePolicyResponse": {"type": "structure", "required": ["findings"], "members": {"findings": {"shape": "ValidatePolicyFindingList", "documentation": "<p>The list of findings in a policy returned by IAM Access Analyzer based on its suite of policy checks.</p>"}, "nextToken": {"shape": "Token", "documentation": "<p>A token used for pagination of results returned.</p>"}}}, "ValidationException": {"type": "structure", "required": ["message", "reason"], "members": {"message": {"shape": "String"}, "reason": {"shape": "ValidationExceptionReason", "documentation": "<p>The reason for the exception.</p>"}, "fieldList": {"shape": "ValidationExceptionFieldList", "documentation": "<p>A list of fields that didn't validate.</p>"}}, "documentation": "<p>Validation exception error.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "ValidationExceptionField": {"type": "structure", "required": ["name", "message"], "members": {"name": {"shape": "String", "documentation": "<p>The name of the validation exception.</p>"}, "message": {"shape": "String", "documentation": "<p>A message about the validation exception.</p>"}}, "documentation": "<p>Contains information about a validation exception.</p>"}, "ValidationExceptionFieldList": {"type": "list", "member": {"shape": "ValidationExceptionField"}}, "ValidationExceptionReason": {"type": "string", "enum": ["unknownOperation", "<PERSON><PERSON><PERSON><PERSON>", "fieldValidationFailed", "other", "notSupported"]}, "ValueList": {"type": "list", "member": {"shape": "String"}, "max": 20, "min": 1}, "VpcConfiguration": {"type": "structure", "required": ["vpcId"], "members": {"vpcId": {"shape": "VpcId", "documentation": "<p> If this field is specified, this access point will only allow connections from the specified VPC ID. </p>"}}, "documentation": "<p>The proposed virtual private cloud (VPC) configuration for the Amazon S3 access point. VPC configuration does not apply to multi-region access points. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/API_control_VpcConfiguration.html\">VpcConfiguration</a>. </p>"}, "VpcId": {"type": "string", "pattern": "vpc-([0-9a-f]){8}(([0-9a-f]){9})?"}}, "documentation": "<p>Identity and Access Management Access Analyzer helps you to set, verify, and refine your IAM policies by providing a suite of capabilities. Its features include findings for external and unused access, basic and custom policy checks for validating policies, and policy generation to generate fine-grained policies. To start using IAM Access Analyzer to identify external or unused access, you first need to create an analyzer.</p> <p> <b>External access analyzers</b> help identify potential risks of accessing resources by enabling you to identify any resource policies that grant access to an external principal. It does this by using logic-based reasoning to analyze resource-based policies in your Amazon Web Services environment. An external principal can be another Amazon Web Services account, a root user, an IAM user or role, a federated user, an Amazon Web Services service, or an anonymous user. You can also use IAM Access Analyzer to preview public and cross-account access to your resources before deploying permissions changes.</p> <p> <b>Unused access analyzers</b> help identify potential identity access risks by enabling you to identify unused IAM roles, unused access keys, unused console passwords, and IAM principals with unused service and action-level permissions.</p> <p>Beyond findings, IAM Access Analyzer provides basic and custom policy checks to validate IAM policies before deploying permissions changes. You can use policy generation to refine permissions by attaching a policy generated using access activity logged in CloudTrail logs. </p> <p>This guide describes the IAM Access Analyzer operations that you can call programmatically. For general information about IAM Access Analyzer, see <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/what-is-access-analyzer.html\">Identity and Access Management Access Analyzer</a> in the <b>IAM User Guide</b>.</p>"}