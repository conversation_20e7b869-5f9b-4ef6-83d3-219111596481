{"version": "2.0", "metadata": {"apiVersion": "2020-04-30", "endpointPrefix": "resiliencehub", "jsonVersion": "1.1", "protocol": "rest-json", "protocols": ["rest-json"], "serviceFullName": "AWS Resilience Hub", "serviceId": "resiliencehub", "signatureVersion": "v4", "signingName": "resiliencehub", "uid": "resiliencehub-2020-04-30", "auth": ["aws.auth#sigv4"]}, "operations": {"AcceptResourceGroupingRecommendations": {"name": "AcceptResourceGroupingRecommendations", "http": {"method": "POST", "requestUri": "/accept-resource-grouping-recommendations", "responseCode": 200}, "input": {"shape": "AcceptResourceGroupingRecommendationsRequest"}, "output": {"shape": "AcceptResourceGroupingRecommendationsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Accepts the resource grouping recommendations suggested by Resilience Hub for your application.</p>"}, "AddDraftAppVersionResourceMappings": {"name": "AddDraftAppVersionResourceMappings", "http": {"method": "POST", "requestUri": "/add-draft-app-version-resource-mappings", "responseCode": 200}, "input": {"shape": "AddDraftAppVersionResourceMappingsRequest"}, "output": {"shape": "AddDraftAppVersionResourceMappingsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Adds the source of resource-maps to the draft version of an application. During assessment, Resilience Hub will use these resource-maps to resolve the latest physical ID for each resource in the application template. For more information about different types of resources supported by Resilience Hub and how to add them in your application, see <a href=\"https://docs.aws.amazon.com/resilience-hub/latest/userguide/how-app-manage.html\">Step 2: How is your application managed?</a> in the Resilience Hub User Guide.</p>"}, "BatchUpdateRecommendationStatus": {"name": "BatchUpdateRecommendationStatus", "http": {"method": "POST", "requestUri": "/batch-update-recommendation-status", "responseCode": 200}, "input": {"shape": "BatchUpdateRecommendationStatusRequest"}, "output": {"shape": "BatchUpdateRecommendationStatusResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Enables you to include or exclude one or more operational recommendations.</p>"}, "CreateApp": {"name": "CreateApp", "http": {"method": "POST", "requestUri": "/create-app", "responseCode": 200}, "input": {"shape": "CreateAppRequest"}, "output": {"shape": "CreateAppResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Creates an Resilience Hub application. An Resilience Hub application is a collection of Amazon Web Services resources structured to prevent and recover Amazon Web Services application disruptions. To describe a Resilience Hub application, you provide an application name, resources from one or more CloudFormation stacks, Resource Groups, Terraform state files, AppRegistry applications, and an appropriate resiliency policy. In addition, you can also add resources that are located on Amazon Elastic Kubernetes Service (Amazon EKS) clusters as optional resources. For more information about the number of resources supported per application, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/resiliencehub.html#limits_resiliencehub\">Service quotas</a>.</p> <p>After you create an Resilience Hub application, you publish it so that you can run a resiliency assessment on it. You can then use recommendations from the assessment to improve resiliency by running another assessment, comparing results, and then iterating the process until you achieve your goals for recovery time objective (RTO) and recovery point objective (RPO).</p>"}, "CreateAppVersionAppComponent": {"name": "CreateAppVersionAppComponent", "http": {"method": "POST", "requestUri": "/create-app-version-app-component", "responseCode": 200}, "input": {"shape": "CreateAppVersionAppComponentRequest"}, "output": {"shape": "CreateAppVersionAppComponentResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Creates a new Application Component in the Resilience Hub application.</p> <note> <p>This API updates the Resilience Hub application draft version. To use this Application Component for running assessments, you must publish the Resilience Hub application using the <code>PublishAppVersion</code> API.</p> </note>"}, "CreateAppVersionResource": {"name": "CreateAppVersionResource", "http": {"method": "POST", "requestUri": "/create-app-version-resource", "responseCode": 200}, "input": {"shape": "CreateAppVersionResourceRequest"}, "output": {"shape": "CreateAppVersionResourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Adds a resource to the Resilience Hub application and assigns it to the specified Application Components. If you specify a new Application Component, Resilience Hub will automatically create the Application Component.</p> <note> <ul> <li> <p>This action has no effect outside Resilience Hub.</p> </li> <li> <p>This API updates the Resilience Hub application draft version. To use this resource for running resiliency assessments, you must publish the Resilience Hub application using the <code>PublishAppVersion</code> API.</p> </li> <li> <p>To update application version with new <code>physicalResourceID</code>, you must call <code>ResolveAppVersionResources</code> API.</p> </li> </ul> </note>"}, "CreateRecommendationTemplate": {"name": "CreateRecommendationTemplate", "http": {"method": "POST", "requestUri": "/create-recommendation-template", "responseCode": 200}, "input": {"shape": "CreateRecommendationTemplateRequest"}, "output": {"shape": "CreateRecommendationTemplateResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Creates a new recommendation template for the Resilience Hub application.</p>"}, "CreateResiliencyPolicy": {"name": "CreateResiliencyPolicy", "http": {"method": "POST", "requestUri": "/create-resiliency-policy", "responseCode": 200}, "input": {"shape": "CreateResiliencyPolicyRequest"}, "output": {"shape": "CreateResiliencyPolicyResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Creates a resiliency policy for an application.</p> <note> <p>Resilience Hub allows you to provide a value of zero for <code>rtoInSecs</code> and <code>rpoInSecs</code> of your resiliency policy. But, while assessing your application, the lowest possible assessment result is near zero. Hence, if you provide value zero for <code>rtoInSecs</code> and <code>rpoInSecs</code>, the estimated workload RTO and estimated workload RPO result will be near zero and the <b>Compliance status</b> for your application will be set to <b>Policy breached</b>.</p> </note>"}, "DeleteApp": {"name": "DeleteApp", "http": {"method": "POST", "requestUri": "/delete-app", "responseCode": 200}, "input": {"shape": "DeleteAppRequest"}, "output": {"shape": "DeleteAppResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Deletes an Resilience Hub application. This is a destructive action that can't be undone.</p>"}, "DeleteAppAssessment": {"name": "DeleteAppAssessment", "http": {"method": "POST", "requestUri": "/delete-app-assessment", "responseCode": 200}, "input": {"shape": "DeleteAppAssessmentRequest"}, "output": {"shape": "DeleteAppAssessmentResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Deletes an Resilience Hub application assessment. This is a destructive action that can't be undone.</p>"}, "DeleteAppInputSource": {"name": "DeleteAppInputSource", "http": {"method": "POST", "requestUri": "/delete-app-input-source", "responseCode": 200}, "input": {"shape": "DeleteAppInputSourceRequest"}, "output": {"shape": "DeleteAppInputSourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Deletes the input source and all of its imported resources from the Resilience Hub application.</p>"}, "DeleteAppVersionAppComponent": {"name": "DeleteAppVersionAppComponent", "http": {"method": "POST", "requestUri": "/delete-app-version-app-component", "responseCode": 200}, "input": {"shape": "DeleteAppVersionAppComponentRequest"}, "output": {"shape": "DeleteAppVersionAppComponentResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Deletes an Application Component from the Resilience Hub application.</p> <note> <ul> <li> <p>This API updates the Resilience Hub application draft version. To use this Application Component for running assessments, you must publish the Resilience Hub application using the <code>PublishAppVersion</code> API.</p> </li> <li> <p>You will not be able to delete an Application Component if it has resources associated with it.</p> </li> </ul> </note>"}, "DeleteAppVersionResource": {"name": "DeleteAppVersionResource", "http": {"method": "POST", "requestUri": "/delete-app-version-resource", "responseCode": 200}, "input": {"shape": "DeleteAppVersionResourceRequest"}, "output": {"shape": "DeleteAppVersionResourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Deletes a resource from the Resilience Hub application.</p> <note> <ul> <li> <p>You can only delete a manually added resource. To exclude non-manually added resources, use the <code>UpdateAppVersionResource</code> API.</p> </li> <li> <p>This action has no effect outside Resilience Hub.</p> </li> <li> <p>This API updates the Resilience Hub application draft version. To use this resource for running resiliency assessments, you must publish the Resilience Hub application using the <code>PublishAppVersion</code> API.</p> </li> </ul> </note>"}, "DeleteRecommendationTemplate": {"name": "DeleteRecommendationTemplate", "http": {"method": "POST", "requestUri": "/delete-recommendation-template", "responseCode": 200}, "input": {"shape": "DeleteRecommendationTemplateRequest"}, "output": {"shape": "DeleteRecommendationTemplateResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Deletes a recommendation template. This is a destructive action that can't be undone.</p>"}, "DeleteResiliencyPolicy": {"name": "DeleteResiliencyPolicy", "http": {"method": "POST", "requestUri": "/delete-resiliency-policy", "responseCode": 200}, "input": {"shape": "DeleteResiliencyPolicyRequest"}, "output": {"shape": "DeleteResiliencyPolicyResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Deletes a resiliency policy. This is a destructive action that can't be undone.</p>"}, "DescribeApp": {"name": "DescribeApp", "http": {"method": "POST", "requestUri": "/describe-app", "responseCode": 200}, "input": {"shape": "DescribeAppRequest"}, "output": {"shape": "DescribeAppResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Describes an Resilience Hub application.</p>"}, "DescribeAppAssessment": {"name": "DescribeAppAssessment", "http": {"method": "POST", "requestUri": "/describe-app-assessment", "responseCode": 200}, "input": {"shape": "DescribeAppAssessmentRequest"}, "output": {"shape": "DescribeAppAssessmentResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Describes an assessment for an Resilience Hub application.</p>"}, "DescribeAppVersion": {"name": "DescribeAppVersion", "http": {"method": "POST", "requestUri": "/describe-app-version", "responseCode": 200}, "input": {"shape": "DescribeAppVersionRequest"}, "output": {"shape": "DescribeAppVersionResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Describes the Resilience Hub application version.</p>"}, "DescribeAppVersionAppComponent": {"name": "DescribeAppVersionAppComponent", "http": {"method": "POST", "requestUri": "/describe-app-version-app-component", "responseCode": 200}, "input": {"shape": "DescribeAppVersionAppComponentRequest"}, "output": {"shape": "DescribeAppVersionAppComponentResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Describes an Application Component in the Resilience Hub application.</p>"}, "DescribeAppVersionResource": {"name": "DescribeAppVersionResource", "http": {"method": "POST", "requestUri": "/describe-app-version-resource", "responseCode": 200}, "input": {"shape": "DescribeAppVersionResourceRequest"}, "output": {"shape": "DescribeAppVersionResourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Describes a resource of the Resilience Hub application.</p> <note> <p>This API accepts only one of the following parameters to describe the resource:</p> <ul> <li> <p> <code>resourceName</code> </p> </li> <li> <p> <code>logicalResourceId</code> </p> </li> <li> <p> <code>physicalResourceId</code> (Along with <code>physicalResourceId</code>, you can also provide <code>awsAccountId</code>, and <code>awsRegion</code>)</p> </li> </ul> </note>"}, "DescribeAppVersionResourcesResolutionStatus": {"name": "DescribeAppVersionResourcesResolutionStatus", "http": {"method": "POST", "requestUri": "/describe-app-version-resources-resolution-status", "responseCode": 200}, "input": {"shape": "DescribeAppVersionResourcesResolutionStatusRequest"}, "output": {"shape": "DescribeAppVersionResourcesResolutionStatusResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Returns the resolution status for the specified resolution identifier for an application version. If <code>resolutionId</code> is not specified, the current resolution status is returned.</p>"}, "DescribeAppVersionTemplate": {"name": "DescribeAppVersionTemplate", "http": {"method": "POST", "requestUri": "/describe-app-version-template", "responseCode": 200}, "input": {"shape": "DescribeAppVersionTemplateRequest"}, "output": {"shape": "DescribeAppVersionTemplateResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Describes details about an Resilience Hub application.</p>"}, "DescribeDraftAppVersionResourcesImportStatus": {"name": "DescribeDraftAppVersionResourcesImportStatus", "http": {"method": "POST", "requestUri": "/describe-draft-app-version-resources-import-status", "responseCode": 200}, "input": {"shape": "DescribeDraftAppVersionResourcesImportStatusRequest"}, "output": {"shape": "DescribeDraftAppVersionResourcesImportStatusResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Describes the status of importing resources to an application version.</p> <note> <p>If you get a 404 error with <code>ResourceImportStatusNotFoundAppMetadataException</code>, you must call <code>importResourcesToDraftAppVersion</code> after creating the application and before calling <code>describeDraftAppVersionResourcesImportStatus</code> to obtain the status.</p> </note>"}, "DescribeMetricsExport": {"name": "DescribeMetricsExport", "http": {"method": "POST", "requestUri": "/describe-metrics-export", "responseCode": 200}, "input": {"shape": "DescribeMetricsExportRequest"}, "output": {"shape": "DescribeMetricsExportResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Describes the metrics of the application configuration being exported.</p>"}, "DescribeResiliencyPolicy": {"name": "DescribeResiliencyPolicy", "http": {"method": "POST", "requestUri": "/describe-resiliency-policy", "responseCode": 200}, "input": {"shape": "DescribeResiliencyPolicyRequest"}, "output": {"shape": "DescribeResiliencyPolicyResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Describes a specified resiliency policy for an Resilience Hub application. The returned policy object includes creation time, data location constraints, the Amazon Resource Name (ARN) for the policy, tags, tier, and more.</p>"}, "DescribeResourceGroupingRecommendationTask": {"name": "DescribeResourceGroupingRecommendationTask", "http": {"method": "POST", "requestUri": "/describe-resource-grouping-recommendation-task", "responseCode": 200}, "input": {"shape": "DescribeResourceGroupingRecommendationTaskRequest"}, "output": {"shape": "DescribeResourceGroupingRecommendationTaskResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Describes the resource grouping recommendation tasks run by Resilience Hub for your application.</p>"}, "ImportResourcesToDraftAppVersion": {"name": "ImportResourcesToDraftAppVersion", "http": {"method": "POST", "requestUri": "/import-resources-to-draft-app-version", "responseCode": 200}, "input": {"shape": "ImportResourcesToDraftAppVersionRequest"}, "output": {"shape": "ImportResourcesToDraftAppVersionResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Imports resources to Resilience Hub application draft version from different input sources. For more information about the input sources supported by Resilience Hub, see <a href=\"https://docs.aws.amazon.com/resilience-hub/latest/userguide/discover-structure.html\">Discover the structure and describe your Resilience Hub application</a>.</p>"}, "ListAlarmRecommendations": {"name": "ListAlarmRecommendations", "http": {"method": "POST", "requestUri": "/list-alarm-recommendations", "responseCode": 200}, "input": {"shape": "ListAlarmRecommendationsRequest"}, "output": {"shape": "ListAlarmRecommendationsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists the alarm recommendations for an Resilience Hub application.</p>"}, "ListAppAssessmentComplianceDrifts": {"name": "ListAppAssessmentComplianceDrifts", "http": {"method": "POST", "requestUri": "/list-app-assessment-compliance-drifts", "responseCode": 200}, "input": {"shape": "ListAppAssessmentComplianceDriftsRequest"}, "output": {"shape": "ListAppAssessmentComplianceDriftsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>List of compliance drifts that were detected while running an assessment.</p>"}, "ListAppAssessmentResourceDrifts": {"name": "ListAppAssessmentResourceDrifts", "http": {"method": "POST", "requestUri": "/list-app-assessment-resource-drifts", "responseCode": 200}, "input": {"shape": "ListAppAssessmentResourceDriftsRequest"}, "output": {"shape": "ListAppAssessmentResourceDriftsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>List of resource drifts that were detected while running an assessment.</p>"}, "ListAppAssessments": {"name": "ListAppAssessments", "http": {"method": "GET", "requestUri": "/list-app-assessments", "responseCode": 200}, "input": {"shape": "ListAppAssessmentsRequest"}, "output": {"shape": "ListAppAssessmentsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists the assessments for an Resilience Hub application. You can use request parameters to refine the results for the response object.</p>"}, "ListAppComponentCompliances": {"name": "ListAppComponentCompliances", "http": {"method": "POST", "requestUri": "/list-app-component-compliances", "responseCode": 200}, "input": {"shape": "ListAppComponentCompliancesRequest"}, "output": {"shape": "ListAppComponentCompliancesResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists the compliances for an Resilience Hub Application Component.</p>"}, "ListAppComponentRecommendations": {"name": "ListAppComponentRecommendations", "http": {"method": "POST", "requestUri": "/list-app-component-recommendations", "responseCode": 200}, "input": {"shape": "ListAppComponentRecommendationsRequest"}, "output": {"shape": "ListAppComponentRecommendationsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists the recommendations for an Resilience Hub Application Component.</p>"}, "ListAppInputSources": {"name": "ListAppInputSources", "http": {"method": "POST", "requestUri": "/list-app-input-sources", "responseCode": 200}, "input": {"shape": "ListAppInputSourcesRequest"}, "output": {"shape": "ListAppInputSourcesResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists all the input sources of the Resilience Hub application. For more information about the input sources supported by Resilience Hub, see <a href=\"https://docs.aws.amazon.com/resilience-hub/latest/userguide/discover-structure.html\">Discover the structure and describe your Resilience Hub application</a>.</p>"}, "ListAppVersionAppComponents": {"name": "ListAppVersionAppComponents", "http": {"method": "POST", "requestUri": "/list-app-version-app-components", "responseCode": 200}, "input": {"shape": "ListAppVersionAppComponentsRequest"}, "output": {"shape": "ListAppVersionAppComponentsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists all the Application Components in the Resilience Hub application.</p>"}, "ListAppVersionResourceMappings": {"name": "ListAppVersionResourceMappings", "http": {"method": "POST", "requestUri": "/list-app-version-resource-mappings", "responseCode": 200}, "input": {"shape": "ListAppVersionResourceMappingsRequest"}, "output": {"shape": "ListAppVersionResourceMappingsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists how the resources in an application version are mapped/sourced from. Mappings can be physical resource identifiers, CloudFormation stacks, resource-groups, or an application registry app.</p>"}, "ListAppVersionResources": {"name": "ListAppVersionResources", "http": {"method": "POST", "requestUri": "/list-app-version-resources", "responseCode": 200}, "input": {"shape": "ListAppVersionResourcesRequest"}, "output": {"shape": "ListAppVersionResourcesResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists all the resources in an Resilience Hub application.</p>"}, "ListAppVersions": {"name": "ListAppVersions", "http": {"method": "POST", "requestUri": "/list-app-versions", "responseCode": 200}, "input": {"shape": "ListAppVersionsRequest"}, "output": {"shape": "ListAppVersionsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists the different versions for the Resilience Hub applications.</p>"}, "ListApps": {"name": "ListApps", "http": {"method": "GET", "requestUri": "/list-apps", "responseCode": 200}, "input": {"shape": "ListAppsRequest"}, "output": {"shape": "ListAppsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists your Resilience Hub applications.</p> <note> <p>You can filter applications using only one filter at a time or without using any filter. If you try to filter applications using multiple filters, you will get the following error:</p> <p> <code>An error occurred (ValidationException) when calling the ListApps operation: Only one filter is supported for this operation.</code> </p> </note>"}, "ListMetrics": {"name": "ListMetrics", "http": {"method": "POST", "requestUri": "/list-metrics", "responseCode": 200}, "input": {"shape": "ListMetricsRequest"}, "output": {"shape": "ListMetricsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists the metrics that can be exported.</p>"}, "ListRecommendationTemplates": {"name": "ListRecommendationTemplates", "http": {"method": "GET", "requestUri": "/list-recommendation-templates", "responseCode": 200}, "input": {"shape": "ListRecommendationTemplatesRequest"}, "output": {"shape": "ListRecommendationTemplatesResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists the recommendation templates for the Resilience Hub applications.</p>"}, "ListResiliencyPolicies": {"name": "ListResiliencyPolicies", "http": {"method": "GET", "requestUri": "/list-resiliency-policies", "responseCode": 200}, "input": {"shape": "ListResiliencyPoliciesRequest"}, "output": {"shape": "ListResiliencyPoliciesResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists the resiliency policies for the Resilience Hub applications.</p>"}, "ListResourceGroupingRecommendations": {"name": "ListResourceGroupingRecommendations", "http": {"method": "GET", "requestUri": "/list-resource-grouping-recommendations", "responseCode": 200}, "input": {"shape": "ListResourceGroupingRecommendationsRequest"}, "output": {"shape": "ListResourceGroupingRecommendationsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists the resource grouping recommendations suggested by Resilience Hub for your application.</p>"}, "ListSopRecommendations": {"name": "ListSopRecommendations", "http": {"method": "POST", "requestUri": "/list-sop-recommendations", "responseCode": 200}, "input": {"shape": "ListSopRecommendationsRequest"}, "output": {"shape": "ListSopRecommendationsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists the standard operating procedure (SOP) recommendations for the Resilience Hub applications.</p>"}, "ListSuggestedResiliencyPolicies": {"name": "ListSuggestedResiliencyPolicies", "http": {"method": "GET", "requestUri": "/list-suggested-resiliency-policies", "responseCode": 200}, "input": {"shape": "ListSuggestedResiliencyPoliciesRequest"}, "output": {"shape": "ListSuggestedResiliencyPoliciesResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists the suggested resiliency policies for the Resilience Hub applications.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists the tags for your resources in your Resilience Hub applications.</p>"}, "ListTestRecommendations": {"name": "ListTestRecommendations", "http": {"method": "POST", "requestUri": "/list-test-recommendations", "responseCode": 200}, "input": {"shape": "ListTestRecommendationsRequest"}, "output": {"shape": "ListTestRecommendationsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists the test recommendations for the Resilience Hub application.</p>"}, "ListUnsupportedAppVersionResources": {"name": "ListUnsupportedAppVersionResources", "http": {"method": "POST", "requestUri": "/list-unsupported-app-version-resources", "responseCode": 200}, "input": {"shape": "ListUnsupportedAppVersionResourcesRequest"}, "output": {"shape": "ListUnsupportedAppVersionResourcesResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists the resources that are not currently supported in Resilience Hub. An unsupported resource is a resource that exists in the object that was used to create an app, but is not supported by Resilience Hub.</p>"}, "PublishAppVersion": {"name": "PublishAppVersion", "http": {"method": "POST", "requestUri": "/publish-app-version", "responseCode": 200}, "input": {"shape": "PublishAppVersionRequest"}, "output": {"shape": "PublishAppVersionResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Publishes a new version of a specific Resilience Hub application.</p>"}, "PutDraftAppVersionTemplate": {"name": "PutDraftAppVersionTemplate", "http": {"method": "POST", "requestUri": "/put-draft-app-version-template", "responseCode": 200}, "input": {"shape": "PutDraftAppVersionTemplateRequest"}, "output": {"shape": "PutDraftAppVersionTemplateResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Adds or updates the app template for an Resilience Hub application draft version.</p>"}, "RejectResourceGroupingRecommendations": {"name": "RejectResourceGroupingRecommendations", "http": {"method": "POST", "requestUri": "/reject-resource-grouping-recommendations", "responseCode": 200}, "input": {"shape": "RejectResourceGroupingRecommendationsRequest"}, "output": {"shape": "RejectResourceGroupingRecommendationsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Rejects resource grouping recommendations.</p>"}, "RemoveDraftAppVersionResourceMappings": {"name": "RemoveDraftAppVersionResourceMappings", "http": {"method": "POST", "requestUri": "/remove-draft-app-version-resource-mappings", "responseCode": 200}, "input": {"shape": "RemoveDraftAppVersionResourceMappingsRequest"}, "output": {"shape": "RemoveDraftAppVersionResourceMappingsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Removes resource mappings from a draft application version.</p>"}, "ResolveAppVersionResources": {"name": "ResolveAppVersionResources", "http": {"method": "POST", "requestUri": "/resolve-app-version-resources", "responseCode": 200}, "input": {"shape": "ResolveAppVersionResourcesRequest"}, "output": {"shape": "ResolveAppVersionResourcesResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Resolves the resources for an application version.</p>"}, "StartAppAssessment": {"name": "StartAppAssessment", "http": {"method": "POST", "requestUri": "/start-app-assessment", "responseCode": 200}, "input": {"shape": "StartAppAssessmentRequest"}, "output": {"shape": "StartAppAssessmentResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Creates a new application assessment for an application.</p>"}, "StartMetricsExport": {"name": "StartMetricsExport", "http": {"method": "POST", "requestUri": "/start-metrics-export", "responseCode": 200}, "input": {"shape": "StartMetricsExportRequest"}, "output": {"shape": "StartMetricsExportResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Initiates the export task of metrics.</p>"}, "StartResourceGroupingRecommendationTask": {"name": "StartResourceGroupingRecommendationTask", "http": {"method": "POST", "requestUri": "/start-resource-grouping-recommendation-task", "responseCode": 200}, "input": {"shape": "StartResourceGroupingRecommendationTaskRequest"}, "output": {"shape": "StartResourceGroupingRecommendationTaskResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Starts grouping recommendation task.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Applies one or more tags to a resource.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Removes one or more tags from a resource.</p>"}, "UpdateApp": {"name": "UpdateApp", "http": {"method": "POST", "requestUri": "/update-app", "responseCode": 200}, "input": {"shape": "UpdateAppRequest"}, "output": {"shape": "UpdateAppResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Updates an application.</p>"}, "UpdateAppVersion": {"name": "UpdateAppVersion", "http": {"method": "POST", "requestUri": "/update-app-version", "responseCode": 200}, "input": {"shape": "UpdateAppVersionRequest"}, "output": {"shape": "UpdateAppVersionResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Updates the Resilience Hub application version.</p> <note> <p>This API updates the Resilience Hub application draft version. To use this information for running resiliency assessments, you must publish the Resilience Hub application using the <code>PublishAppVersion</code> API.</p> </note>"}, "UpdateAppVersionAppComponent": {"name": "UpdateAppVersionAppComponent", "http": {"method": "POST", "requestUri": "/update-app-version-app-component", "responseCode": 200}, "input": {"shape": "UpdateAppVersionAppComponentRequest"}, "output": {"shape": "UpdateAppVersionAppComponentResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Updates an existing Application Component in the Resilience Hub application.</p> <note> <p>This API updates the Resilience Hub application draft version. To use this Application Component for running assessments, you must publish the Resilience Hub application using the <code>PublishAppVersion</code> API.</p> </note>"}, "UpdateAppVersionResource": {"name": "UpdateAppVersionResource", "http": {"method": "POST", "requestUri": "/update-app-version-resource", "responseCode": 200}, "input": {"shape": "UpdateAppVersionResourceRequest"}, "output": {"shape": "UpdateAppVersionResourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Updates the resource details in the Resilience Hub application.</p> <note> <ul> <li> <p>This action has no effect outside Resilience Hub.</p> </li> <li> <p>This API updates the Resilience Hub application draft version. To use this resource for running resiliency assessments, you must publish the Resilience Hub application using the <code>PublishAppVersion</code> API.</p> </li> <li> <p>To update application version with new <code>physicalResourceID</code>, you must call <code>ResolveAppVersionResources</code> API.</p> </li> </ul> </note>"}, "UpdateResiliencyPolicy": {"name": "UpdateResiliencyPolicy", "http": {"method": "POST", "requestUri": "/update-resiliency-policy", "responseCode": 200}, "input": {"shape": "UpdateResiliencyPolicyRequest"}, "output": {"shape": "UpdateResiliencyPolicyResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Updates a resiliency policy.</p> <note> <p>Resilience Hub allows you to provide a value of zero for <code>rtoInSecs</code> and <code>rpoInSecs</code> of your resiliency policy. But, while assessing your application, the lowest possible assessment result is near zero. Hence, if you provide value zero for <code>rtoInSecs</code> and <code>rpoInSecs</code>, the estimated workload RTO and estimated workload RPO result will be near zero and the <b>Compliance status</b> for your application will be set to <b>Policy breached</b>.</p> </note>"}}, "shapes": {"AcceptGroupingRecommendationEntries": {"type": "list", "member": {"shape": "AcceptGroupingRecommendationEntry"}, "max": 30, "min": 1}, "AcceptGroupingRecommendationEntry": {"type": "structure", "required": ["groupingRecommendationId"], "members": {"groupingRecommendationId": {"shape": "String255", "documentation": "<p>Indicates the identifier of the grouping recommendation.</p>"}}, "documentation": "<p>Indicates the grouping recommendation you have accepted to include in your application.</p>"}, "AcceptResourceGroupingRecommendationsRequest": {"type": "structure", "required": ["appArn", "entries"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "entries": {"shape": "AcceptGroupingRecommendationEntries", "documentation": "<p>List of resource grouping recommendations you want to include in your application.</p>"}}}, "AcceptResourceGroupingRecommendationsResponse": {"type": "structure", "required": ["appArn", "failedEntries"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "failedEntries": {"shape": "FailedGroupingRecommendationEntries", "documentation": "<p>List of resource grouping recommendations that could not be included in your application.</p>"}}}, "AccessDeniedException": {"type": "structure", "members": {"message": {"shape": "String500"}}, "documentation": "<p>You don't have permissions to perform the requested operation. The user or role that is making the request must have at least one IAM permissions policy attached that grants the required permissions.</p>", "error": {"httpStatusCode": 403, "senderFault": true}, "exception": true}, "AddDraftAppVersionResourceMappingsRequest": {"type": "structure", "required": ["appArn", "resourceMappings"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "resourceMappings": {"shape": "ResourceMappingList", "documentation": "<p>Mappings used to map logical resources from the template to physical resources. You can use the mapping type <code>CFN_STACK</code> if the application template uses a logical stack name. Or you can map individual resources by using the mapping type <code>RESOURCE</code>. We recommend using the mapping type <code>CFN_STACK</code> if the application is backed by a CloudFormation stack.</p>"}}}, "AddDraftAppVersionResourceMappingsResponse": {"type": "structure", "required": ["appArn", "appVersion", "resourceMappings"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "appVersion": {"shape": "EntityVersion", "documentation": "<p>The version of the application.</p>"}, "resourceMappings": {"shape": "ResourceMappingList", "documentation": "<p>List of sources that are used to map a logical resource from the template to a physical resource. You can use sources such as CloudFormation, Terraform state files, AppRegistry applications, or Amazon EKS.</p>"}}}, "AdditionalInfoMap": {"type": "map", "key": {"shape": "String128WithoutWhitespace"}, "value": {"shape": "AdditionalInfoValueList"}}, "AdditionalInfoValueList": {"type": "list", "member": {"shape": "String1024"}, "max": 10, "min": 1}, "Alarm": {"type": "structure", "members": {"alarmArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Amazon CloudWatch alarm.</p>"}, "source": {"shape": "String255", "documentation": "<p>Indicates the source of the Amazon CloudWatch alarm. That is, it indicates if the alarm was created using Resilience Hub recommendation (<code>AwsResilienceHub</code>), or if you had created the alarm in Amazon CloudWatch (<code>Customer</code>).</p>"}}, "documentation": "<p>Indicates the Amazon CloudWatch alarm detected while running an assessment.</p>"}, "AlarmRecommendation": {"type": "structure", "required": ["name", "recommendationId", "referenceId", "type"], "members": {"appComponentName": {"shape": "EntityId", "documentation": "<p>Application Component name for the CloudWatch alarm recommendation. This name is saved as the first item in the <code>appComponentNames</code> list.</p>", "deprecated": true, "deprecatedMessage": "An alarm recommendation can be attached to multiple Application Components, hence this property will be replaced by the new property 'appComponentNames'."}, "appComponentNames": {"shape": "AppComponentNameList", "documentation": "<p>List of Application Component names for the CloudWatch alarm recommendation.</p>"}, "description": {"shape": "EntityDescription", "documentation": "<p>Description of the alarm recommendation.</p>"}, "items": {"shape": "RecommendationItemList", "documentation": "<p>List of CloudWatch alarm recommendations.</p>"}, "name": {"shape": "String500", "documentation": "<p>Name of the alarm recommendation.</p>"}, "prerequisite": {"shape": "String500", "documentation": "<p>The prerequisite for the alarm recommendation.</p>"}, "recommendationId": {"shape": "<PERSON><PERSON>", "documentation": "<p>Identifier of the alarm recommendation.</p>"}, "recommendationStatus": {"shape": "RecommendationStatus", "documentation": "<p>Status of the recommended Amazon CloudWatch alarm.</p>"}, "referenceId": {"shape": "SpecReferenceId", "documentation": "<p>Reference identifier of the alarm recommendation.</p>"}, "type": {"shape": "AlarmType", "documentation": "<p>Type of alarm recommendation.</p>"}}, "documentation": "<p>Defines a recommendation for a CloudWatch alarm.</p>"}, "AlarmRecommendationList": {"type": "list", "member": {"shape": "AlarmRecommendation"}}, "AlarmReferenceIdList": {"type": "list", "member": {"shape": "String500"}, "max": 200, "min": 1}, "AlarmType": {"type": "string", "enum": ["Metric", "Composite", "Canary", "Logs", "Event"]}, "App": {"type": "structure", "required": ["appArn", "creationTime", "name"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "assessmentSchedule": {"shape": "AppAssessmentScheduleType", "documentation": "<p>Assessment execution schedule with 'Daily' or 'Disabled' values. </p>"}, "awsApplicationArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of Resource Groups group that is integrated with an AppRegistry application. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "complianceStatus": {"shape": "AppComplianceStatusType", "documentation": "<p>Current status of compliance for the resiliency policy.</p>"}, "creationTime": {"shape": "TimeStamp", "documentation": "<p>Date and time when the application was created.</p>"}, "description": {"shape": "EntityDescription", "documentation": "<p>Optional description for an application.</p>"}, "driftStatus": {"shape": "AppDriftStatusType", "documentation": "<p>Indicates if compliance drifts (deviations) were detected while running an assessment for your application.</p>"}, "eventSubscriptions": {"shape": "EventSubscriptionList", "documentation": "<p>The list of events you would like to subscribe and get notification for. Currently, Resilience Hub supports notifications only for <b>Drift detected</b> and <b>Scheduled assessment failure</b> events.</p>"}, "lastAppComplianceEvaluationTime": {"shape": "TimeStamp", "documentation": "<p>Date and time the most recent compliance evaluation.</p>"}, "lastDriftEvaluationTime": {"shape": "TimeStamp", "documentation": "<p>Indicates the last time that a drift was evaluated.</p>"}, "lastResiliencyScoreEvaluationTime": {"shape": "TimeStamp", "documentation": "<p>Date and time the most recent resiliency score evaluation.</p>"}, "name": {"shape": "EntityName", "documentation": "<p>Name for the application.</p>"}, "permissionModel": {"shape": "PermissionModel", "documentation": "<p>Defines the roles and credentials that Resilience Hub would use while creating the application, importing its resources, and running an assessment.</p>"}, "policyArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the resiliency policy. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:resiliency-policy/<code>policy-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "resiliencyScore": {"shape": "Double", "documentation": "<p>Current resiliency score for the application.</p>"}, "rpoInSecs": {"shape": "IntegerOptional", "documentation": "<p>Recovery Point Objective (RPO) in seconds.</p>"}, "rtoInSecs": {"shape": "IntegerOptional", "documentation": "<p>Recovery Time Objective (RTO) in seconds.</p>"}, "status": {"shape": "AppStatusType", "documentation": "<p>Status of the application.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>Tags assigned to the resource. A tag is a label that you assign to an Amazon Web Services resource. Each tag consists of a key/value pair.</p>"}}, "documentation": "<p>Defines an Resilience Hub application.</p>"}, "AppAssessment": {"type": "structure", "required": ["assessmentArn", "assessmentStatus", "invoker"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "appVersion": {"shape": "EntityVersion", "documentation": "<p>Version of an application.</p>"}, "assessmentArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the assessment. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app-assessment/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "assessmentName": {"shape": "EntityName", "documentation": "<p>Name of the assessment.</p>"}, "assessmentStatus": {"shape": "AssessmentStatus", "documentation": "<p>Current status of the assessment for the resiliency policy.</p>"}, "compliance": {"shape": "AssessmentCompliance", "documentation": "<p>Application compliance against the resiliency policy.</p>"}, "complianceStatus": {"shape": "ComplianceStatus", "documentation": "<p>Current status of the compliance for the resiliency policy.</p>"}, "cost": {"shape": "Cost", "documentation": "<p>Cost for the application.</p>"}, "driftStatus": {"shape": "DriftStatus", "documentation": "<p>Indicates if compliance drifts (deviations) were detected while running an assessment for your application.</p>"}, "endTime": {"shape": "TimeStamp", "documentation": "<p>End time for the action.</p>"}, "invoker": {"shape": "AssessmentInvoker", "documentation": "<p>The entity that invoked the assessment.</p>"}, "message": {"shape": "String500", "documentation": "<p>Error or warning message from the assessment execution</p>"}, "policy": {"shape": "ResiliencyPolicy", "documentation": "<p>Resiliency policy of an application.</p>"}, "resiliencyScore": {"shape": "ResiliencyScore", "documentation": "<p>Current resiliency score for an application.</p>"}, "resourceErrorsDetails": {"shape": "ResourceErrorsDetails", "documentation": "<p> A resource error object containing a list of errors retrieving an application's resources. </p>"}, "startTime": {"shape": "TimeStamp", "documentation": "<p>Starting time for the action.</p>"}, "summary": {"shape": "AssessmentSummary", "documentation": "<p>Indicates the AI-generated summary for the Resilience Hub assessment, providing a concise overview that highlights the top risks and recommendations.</p> <note> <p>This property is available only in the US East (N. Virginia) Region.</p> </note>"}, "tags": {"shape": "TagMap", "documentation": "<p>Tags assigned to the resource. A tag is a label that you assign to an Amazon Web Services resource. Each tag consists of a key/value pair.</p>"}, "versionName": {"shape": "EntityVersion", "documentation": "<p>Version name of the published application.</p>"}}, "documentation": "<p>Defines an application assessment.</p>"}, "AppAssessmentScheduleType": {"type": "string", "enum": ["Disabled", "Daily"]}, "AppAssessmentSummary": {"type": "structure", "required": ["assessmentArn", "assessmentStatus"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "appVersion": {"shape": "EntityVersion", "documentation": "<p>Version of an application.</p>"}, "assessmentArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the assessment. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app-assessment/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "assessmentName": {"shape": "EntityName", "documentation": "<p>Name of the assessment.</p>"}, "assessmentStatus": {"shape": "AssessmentStatus", "documentation": "<p>Current status of the assessment for the resiliency policy.</p>"}, "complianceStatus": {"shape": "ComplianceStatus", "documentation": "<p>Current status of compliance for the resiliency policy.</p>"}, "cost": {"shape": "Cost", "documentation": "<p>Cost for an application.</p>"}, "driftStatus": {"shape": "DriftStatus", "documentation": "<p>Indicates if compliance drifts (deviations) were detected while running an assessment for your application.</p>"}, "endTime": {"shape": "TimeStamp", "documentation": "<p>End time for the action.</p>"}, "invoker": {"shape": "AssessmentInvoker", "documentation": "<p>Entity that invoked the assessment.</p>"}, "message": {"shape": "String500", "documentation": "<p>Message from the assessment run.</p>"}, "resiliencyScore": {"shape": "Double", "documentation": "<p>Current resiliency score for the application.</p>"}, "startTime": {"shape": "TimeStamp", "documentation": "<p>Starting time for the action.</p>"}, "versionName": {"shape": "EntityVersion", "documentation": "<p>Name of an application version.</p>"}}, "documentation": "<p>Defines an application assessment summary.</p>"}, "AppAssessmentSummaryList": {"type": "list", "member": {"shape": "AppAssessmentSummary"}}, "AppComplianceStatusType": {"type": "string", "enum": ["PolicyBreached", "PolicyMet", "NotAssessed", "ChangesDetected", "NotApplicable", "MissingPolicy"]}, "AppComponent": {"type": "structure", "required": ["name", "type"], "members": {"additionalInfo": {"shape": "AdditionalInfoMap", "documentation": "<p>Additional configuration parameters for an Resilience Hub application. If you want to implement <code>additionalInfo</code> through the Resilience Hub console rather than using an API call, see <a href=\"https://docs.aws.amazon.com/resilience-hub/latest/userguide/app-config-param.html\">Configure the application configuration parameters</a>.</p> <note> <p>Currently, this parameter accepts a key-value mapping (in a string format) of only one failover region and one associated account.</p> <p>Key: <code>\"failover-regions\"</code> </p> <p>Value: <code>\"[{\"region\":\"&lt;REGION&gt;\", \"accounts\":[{\"id\":\"&lt;ACCOUNT_ID&gt;\"}]}]\"</code> </p> </note>"}, "id": {"shape": "EntityName255", "documentation": "<p>Identifier of the Application Component.</p>"}, "name": {"shape": "EntityName255", "documentation": "<p>Name of the Application Component.</p>"}, "type": {"shape": "String255", "documentation": "<p>The type of Application Component.</p>"}}, "documentation": "<p>Defines an Application Component.</p>"}, "AppComponentCompliance": {"type": "structure", "members": {"appComponentName": {"shape": "EntityId", "documentation": "<p>Name of the Application Component.</p>"}, "compliance": {"shape": "AssessmentCompliance", "documentation": "<p>The compliance of the Application Component against the resiliency policy.</p>"}, "cost": {"shape": "Cost", "documentation": "<p>The cost for the application.</p>"}, "message": {"shape": "String500", "documentation": "<p>The compliance message.</p>"}, "resiliencyScore": {"shape": "ResiliencyScore", "documentation": "<p>The current resiliency score for the application.</p>"}, "status": {"shape": "ComplianceStatus", "documentation": "<p>Status of the action.</p>"}}, "documentation": "<p>Defines the compliance of an Application Component against the resiliency policy.</p>"}, "AppComponentList": {"type": "list", "member": {"shape": "AppComponent"}}, "AppComponentNameList": {"type": "list", "member": {"shape": "String255"}}, "AppDriftStatusType": {"type": "string", "enum": ["NotChecked", "NotDetected", "Detected"]}, "AppInputSource": {"type": "structure", "required": ["importType"], "members": {"eksSourceClusterNamespace": {"shape": "EksSourceClusterNamespace", "documentation": "<p>The namespace on your Amazon Elastic Kubernetes Service cluster.</p>"}, "importType": {"shape": "ResourceMappingType", "documentation": "<p>The resource type of the input source.</p>"}, "resourceCount": {"shape": "Integer", "documentation": "<p>The number of resources.</p>"}, "sourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the input source. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "sourceName": {"shape": "String255", "documentation": "<p>The name of the input source.</p>"}, "terraformSource": {"shape": "TerraformSource", "documentation": "<p>The name of the Terraform s3 state ﬁle.</p>"}}, "documentation": "<p>The list of Resilience Hub application input sources.</p>"}, "AppInputSourceList": {"type": "list", "member": {"shape": "AppInputSource"}}, "AppStatusType": {"type": "string", "enum": ["Active", "Deleting"]}, "AppSummary": {"type": "structure", "required": ["appArn", "creationTime", "name"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "assessmentSchedule": {"shape": "AppAssessmentScheduleType", "documentation": "<p> Assessment execution schedule with 'Daily' or 'Disabled' values. </p>"}, "awsApplicationArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of Resource Groups group that is integrated with an AppRegistry application. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "complianceStatus": {"shape": "AppComplianceStatusType", "documentation": "<p>The current status of compliance for the resiliency policy.</p>"}, "creationTime": {"shape": "TimeStamp", "documentation": "<p>Date and time when the app was created.</p>"}, "description": {"shape": "EntityDescription", "documentation": "<p>The optional description for an app.</p>"}, "driftStatus": {"shape": "AppDriftStatusType", "documentation": "<p>Indicates if compliance drifts (deviations) were detected while running an assessment for your application.</p>"}, "lastAppComplianceEvaluationTime": {"shape": "TimeStamp", "documentation": "<p>Date and time of the most recent compliance evaluation.</p>"}, "name": {"shape": "EntityName", "documentation": "<p>The name of the application.</p>"}, "resiliencyScore": {"shape": "Double", "documentation": "<p>The current resiliency score for the application.</p>"}, "rpoInSecs": {"shape": "IntegerOptional", "documentation": "<p>Recovery Point Objective (RPO) in seconds.</p>"}, "rtoInSecs": {"shape": "IntegerOptional", "documentation": "<p>Recovery Time Objective (RTO) in seconds.</p>"}, "status": {"shape": "AppStatusType", "documentation": "<p>Status of the application.</p>"}}, "documentation": "<p>Defines an application summary.</p>"}, "AppSummaryList": {"type": "list", "member": {"shape": "AppSummary"}}, "AppTemplateBody": {"type": "string", "max": 409600, "min": 0, "pattern": "^[\\w\\s:,-\\.'\\/{}\\[\\]:\"\\\\]+$"}, "AppVersionList": {"type": "list", "member": {"shape": "AppVersionSummary"}}, "AppVersionSummary": {"type": "structure", "required": ["appVersion"], "members": {"appVersion": {"shape": "EntityVersion", "documentation": "<p>Version of an application.</p>"}, "creationTime": {"shape": "TimeStamp", "documentation": "<p>Creation time of the application version.</p>"}, "identifier": {"shape": "LongOptional", "documentation": "<p>Identifier of the application version.</p>"}, "versionName": {"shape": "EntityVersion", "documentation": "<p>Name of the application version.</p>"}}, "documentation": "<p>Version of an application.</p>"}, "Arn": {"type": "string", "pattern": "^arn:(aws|aws-cn|aws-iso|aws-iso-[a-z]{1}|aws-us-gov):[A-Za-z0-9][A-Za-z0-9_/.-]{0,62}:([a-z]{2}-((iso[a-z]{0,1}-)|(gov-)){0,1}[a-z]+-[0-9]):[0-9]{12}:[A-Za-z0-9/][A-Za-z0-9:_/+.-]{0,1023}$"}, "ArnList": {"type": "list", "member": {"shape": "<PERSON><PERSON>"}}, "AssessmentCompliance": {"type": "map", "key": {"shape": "DisruptionType"}, "value": {"shape": "DisruptionCompliance"}}, "AssessmentInvoker": {"type": "string", "enum": ["User", "System"]}, "AssessmentRiskRecommendation": {"type": "structure", "members": {"appComponents": {"shape": "AppComponentNameList", "documentation": "<p>Indicates the Application Components (AppComponents) that were assessed as part of the assessment and are associated with the identified risk and recommendation.</p> <note> <p>This property is available only in the US East (N. Virginia) Region.</p> </note>"}, "recommendation": {"shape": "String255", "documentation": "<p>Indicates the recommendation provided by the Resilience Hub to address the identified risks in the application.</p> <note> <p>This property is available only in the US East (N. Virginia) Region.</p> </note>"}, "risk": {"shape": "String255", "documentation": "<p>Indicates the description of the potential risk identified in the application as part of the Resilience Hub assessment.</p> <note> <p>This property is available only in the US East (N. Virginia) Region.</p> </note>"}}, "documentation": "<p>Indicates a specific risk identified in the Resilience Hub assessment and the corresponding recommendation provided to address that risk.</p> <note> <p>The assessment summary generated by large language models (LLMs) on Amazon Bedrock are only suggestions. The current level of generative AI technology is not perfect and LLMs are not infallible. Bias and incorrect answers, although rare, should be expected. Review each recommendation in the assessment summary before you use the output from an LLM. </p> </note> <note> <p>This property is available only in the US East (N. Virginia) Region.</p> </note>"}, "AssessmentRiskRecommendationList": {"type": "list", "member": {"shape": "AssessmentRiskRecommendation"}}, "AssessmentStatus": {"type": "string", "enum": ["Pending", "InProgress", "Failed", "Success"]}, "AssessmentStatusList": {"type": "list", "member": {"shape": "AssessmentStatus"}, "max": 10, "min": 1}, "AssessmentSummary": {"type": "structure", "members": {"riskRecommendations": {"shape": "AssessmentRiskRecommendationList", "documentation": "<p>Indicates the top risks and recommendations identified by the Resilience Hub assessment, each representing a specific risk and the corresponding recommendation to address it.</p> <note> <p>This property is available only in the US East (N. Virginia) Region.</p> </note>"}, "summary": {"shape": "String500", "documentation": "<p>Indicates a concise summary that provides an overview of the Resilience Hub assessment.</p> <note> <p>This property is available only in the US East (N. Virginia) Region.</p> </note>"}}, "documentation": "<p>Indicates the AI-generated summary for the Resilience Hub assessment, providing a concise overview that highlights the top risks and recommendations.</p> <note> <p>This property is available only in the US East (N. Virginia) Region.</p> </note>"}, "AwsRegion": {"type": "string", "pattern": "^[a-z]{2}-((iso[a-z]{0,1}-)|(gov-)){0,1}[a-z]+-[0-9]$"}, "BatchUpdateRecommendationStatusFailedEntries": {"type": "list", "member": {"shape": "BatchUpdateRecommendationStatusFailedEntry"}}, "BatchUpdateRecommendationStatusFailedEntry": {"type": "structure", "required": ["entryId", "errorMessage"], "members": {"entryId": {"shape": "String255", "documentation": "<p>An identifier of an entry in this batch that is used to communicate the result.</p> <note> <p>The <code>entryId</code>s of a batch request need to be unique within a request.</p> </note>"}, "errorMessage": {"shape": "ErrorMessage", "documentation": "<p>Indicates the error that occurred while excluding an operational recommendation.</p>"}}, "documentation": "<p>List of operational recommendations that did not get included or excluded.</p>"}, "BatchUpdateRecommendationStatusRequest": {"type": "structure", "required": ["appArn", "requestEntries"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "requestEntries": {"shape": "UpdateRecommendationStatusRequestEntries", "documentation": "<p>Defines the list of operational recommendations that need to be included or excluded.</p>"}}}, "BatchUpdateRecommendationStatusResponse": {"type": "structure", "required": ["appArn", "failedEntries", "successfulEntries"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "failedEntries": {"shape": "BatchUpdateRecommendationStatusFailedEntries", "documentation": "<p>A list of items with error details about each item, which could not be included or excluded.</p>"}, "successfulEntries": {"shape": "BatchUpdateRecommendationStatusSuccessfulEntries", "documentation": "<p>A list of items that were included or excluded.</p>"}}}, "BatchUpdateRecommendationStatusSuccessfulEntries": {"type": "list", "member": {"shape": "BatchUpdateRecommendationStatusSuccessfulEntry"}}, "BatchUpdateRecommendationStatusSuccessfulEntry": {"type": "structure", "required": ["entryId", "excluded", "referenceId"], "members": {"appComponentId": {"shape": "EntityName255", "documentation": "<p>Indicates the identifier of an AppComponent.</p>"}, "entryId": {"shape": "String255", "documentation": "<p>An identifier for an entry in this batch that is used to communicate the result.</p> <note> <p>The <code>entryId</code>s of a batch request need to be unique within a request.</p> </note>"}, "excludeReason": {"shape": "ExcludeRecommendationReason", "documentation": "<p>Indicates the reason for excluding an operational recommendation.</p>"}, "excluded": {"shape": "BooleanOptional", "documentation": "<p>Indicates if the operational recommendation was successfully excluded.</p>"}, "item": {"shape": "UpdateRecommendationStatusItem", "documentation": "<p>The operational recommendation item.</p>"}, "referenceId": {"shape": "SpecReferenceId", "documentation": "<p>Reference identifier of the operational recommendation.</p>"}}, "documentation": "<p>List of operational recommendations that were successfully included or excluded.</p>"}, "BooleanOptional": {"type": "boolean", "box": true}, "ClientToken": {"type": "string", "max": 63, "min": 1, "pattern": "^[A-Za-z0-9_.-]{0,63}$"}, "ComplianceDrift": {"type": "structure", "members": {"actualReferenceId": {"shape": "String255", "documentation": "<p>Assessment identifier that is associated with this drift item.</p>"}, "actualValue": {"shape": "AssessmentCompliance", "documentation": "<p>Actual compliance value of the entity.</p>"}, "appId": {"shape": "String255", "documentation": "<p>Identifier of your application.</p>"}, "appVersion": {"shape": "String255", "documentation": "<p>Published version of your application on which drift was detected.</p>"}, "diffType": {"shape": "DifferenceType", "documentation": "<p>Difference type between actual and expected recovery point objective (RPO) and recovery time objective (RTO) values. Currently, Resilience Hub supports only <code>NotEqual</code> difference type.</p>"}, "driftType": {"shape": "DriftType", "documentation": "<p>The type of drift detected. Currently, Resilience Hub supports only <b>ApplicationCompliance</b> drift type.</p>"}, "entityId": {"shape": "String255", "documentation": "<p>Identifier of an entity in which drift was detected. For compliance drift, the entity ID can be either application ID or the AppComponent ID.</p>"}, "entityType": {"shape": "String255", "documentation": "<p>The type of entity in which drift was detected. For compliance drifts, Resilience Hub supports <code>AWS::ResilienceHub::AppComponent</code> and <code>AWS::ResilienceHub::Application</code>.</p>"}, "expectedReferenceId": {"shape": "String255", "documentation": "<p>Assessment identifier of a previous assessment of the same application version. Resilience Hub uses the previous assessment (associated with the reference identifier) to compare the compliance with the current assessment to identify drifts.</p>"}, "expectedValue": {"shape": "AssessmentCompliance", "documentation": "<p>The expected compliance value of an entity.</p>"}}, "documentation": "<p>Indicates the compliance drifts (recovery time objective (RTO) and recovery point objective (RPO)) that were detected for an assessed entity.</p>"}, "ComplianceDriftList": {"type": "list", "member": {"shape": "ComplianceDrift"}}, "ComplianceStatus": {"type": "string", "enum": ["PolicyBreached", "PolicyMet", "NotApplicable", "MissingPolicy"]}, "ComponentCompliancesList": {"type": "list", "member": {"shape": "AppComponentCompliance"}}, "ComponentRecommendation": {"type": "structure", "required": ["appComponentName", "configRecommendations", "recommendationStatus"], "members": {"appComponentName": {"shape": "EntityId", "documentation": "<p>Name of the Application Component.</p>"}, "configRecommendations": {"shape": "ConfigRecommendationList", "documentation": "<p>List of recommendations.</p>"}, "recommendationStatus": {"shape": "RecommendationComplianceStatus", "documentation": "<p>Status of the recommendation.</p>"}}, "documentation": "<p>Defines recommendations for an Resilience Hub Application Component, returned as an object. This object contains component names, configuration recommendations, and recommendation statuses.</p>"}, "ComponentRecommendationList": {"type": "list", "member": {"shape": "ComponentRecommendation"}}, "Condition": {"type": "structure", "required": ["field", "operator"], "members": {"field": {"shape": "String255", "documentation": "<p>Indicates the field in the metric.</p>"}, "operator": {"shape": "ConditionOperatorType", "documentation": "<p>Indicates the type of operator or comparison to be used when evaluating a condition against the specified field. </p>"}, "value": {"shape": "String255", "documentation": "<p>Indicates the value or data against which a condition is evaluated.</p>"}}, "documentation": "<p>Indicates the condition based on which you want to filter the metrics.</p>"}, "ConditionList": {"type": "list", "member": {"shape": "Condition"}, "max": 50, "min": 0}, "ConditionOperatorType": {"type": "string", "enum": ["Equals", "NotEquals", "GreaterThen", "GreaterOrEquals", "<PERSON><PERSON><PERSON>", "LessOrEquals"]}, "ConfigRecommendation": {"type": "structure", "required": ["name", "optimizationType", "referenceId"], "members": {"appComponentName": {"shape": "EntityId", "documentation": "<p>Name of the Application Component.</p>"}, "compliance": {"shape": "AssessmentCompliance", "documentation": "<p>The current compliance against the resiliency policy before applying the configuration change.</p>"}, "cost": {"shape": "Cost", "documentation": "<p>The cost for the application.</p>"}, "description": {"shape": "EntityDescription", "documentation": "<p>The optional description for an app.</p>"}, "haArchitecture": {"shape": "HaArchitecture", "documentation": "<p>The architecture type.</p>"}, "name": {"shape": "EntityName", "documentation": "<p>The name of the recommendation configuration.</p>"}, "optimizationType": {"shape": "ConfigRecommendationOptimizationType", "documentation": "<p>The type of optimization.</p>"}, "recommendationCompliance": {"shape": "RecommendationCompliance", "documentation": "<p>The expected compliance against the resiliency policy after applying the configuration change.</p>"}, "referenceId": {"shape": "SpecReferenceId", "documentation": "<p>Reference identifier for the recommendation configuration.</p>"}, "suggestedChanges": {"shape": "SuggestedChangesList", "documentation": "<p>List of the suggested configuration changes.</p>"}}, "documentation": "<p>Defines a recommendation configuration.</p>"}, "ConfigRecommendationList": {"type": "list", "member": {"shape": "ConfigRecommendation"}}, "ConfigRecommendationOptimizationType": {"type": "string", "enum": ["LeastCost", "LeastChange", "BestAZRecovery", "LeastErrors", "BestAttainable", "BestRegionRecovery"]}, "ConflictException": {"type": "structure", "members": {"message": {"shape": "String500"}, "resourceId": {"shape": "ResourceId", "documentation": "<p>The identifier of the resource that the exception applies to.</p>"}, "resourceType": {"shape": "ResourceType", "documentation": "<p>The type of the resource that the exception applies to.</p>"}}, "documentation": "<p>This exception occurs when a conflict with a previous successful write is detected. This generally occurs when the previous write did not have time to propagate to the host serving the current request. A retry (with appropriate backoff logic) is the recommended response to this exception.</p>", "error": {"httpStatusCode": 409, "senderFault": true}, "exception": true}, "Cost": {"type": "structure", "required": ["amount", "currency", "frequency"], "members": {"amount": {"shape": "Double", "documentation": "<p>The cost amount.</p>"}, "currency": {"shape": "CurrencyCode", "documentation": "<p>The cost currency, for example <code>USD</code>.</p>"}, "frequency": {"shape": "CostFrequency", "documentation": "<p>The cost frequency.</p>"}}, "documentation": "<p>Defines a cost object.</p>"}, "CostFrequency": {"type": "string", "enum": ["Hourly", "Daily", "Monthly", "Yearly"]}, "CreateAppRequest": {"type": "structure", "required": ["name"], "members": {"assessmentSchedule": {"shape": "AppAssessmentScheduleType", "documentation": "<p> Assessment execution schedule with 'Daily' or 'Disabled' values. </p>"}, "awsApplicationArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of Resource Groups group that is integrated with an AppRegistry application. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>Used for an idempotency token. A client token is a unique, case-sensitive string of up to 64 ASCII characters. You should not reuse the same client token for other API requests.</p>", "idempotencyToken": true}, "description": {"shape": "EntityDescription", "documentation": "<p>The optional description for an app.</p>"}, "eventSubscriptions": {"shape": "EventSubscriptionList", "documentation": "<p>The list of events you would like to subscribe and get notification for. Currently, Resilience Hub supports only <b>Drift detected</b> and <b>Scheduled assessment failure</b> events notification.</p>"}, "name": {"shape": "EntityName", "documentation": "<p>Name of the application.</p>"}, "permissionModel": {"shape": "PermissionModel", "documentation": "<p>Defines the roles and credentials that Resilience Hub would use while creating the application, importing its resources, and running an assessment.</p>"}, "policyArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the resiliency policy. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:resiliency-policy/<code>policy-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>Tags assigned to the resource. A tag is a label that you assign to an Amazon Web Services resource. Each tag consists of a key/value pair.</p>"}}}, "CreateAppResponse": {"type": "structure", "required": ["app"], "members": {"app": {"shape": "App", "documentation": "<p>The created application returned as an object with details including compliance status, creation time, description, resiliency score, and more.</p>"}}}, "CreateAppVersionAppComponentRequest": {"type": "structure", "required": ["appArn", "name", "type"], "members": {"additionalInfo": {"shape": "AdditionalInfoMap", "documentation": "<p>Currently, there is no supported additional information for Application Components.</p>"}, "appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>Used for an idempotency token. A client token is a unique, case-sensitive string of up to 64 ASCII characters. You should not reuse the same client token for other API requests.</p>", "idempotencyToken": true}, "id": {"shape": "String255", "documentation": "<p>Identifier of the Application Component.</p>"}, "name": {"shape": "String255", "documentation": "<p>Name of the Application Component.</p>"}, "type": {"shape": "String255", "documentation": "<p>Type of Application Component. For more information about the types of Application Component, see <a href=\"https://docs.aws.amazon.com/resilience-hub/latest/userguide/AppComponent.grouping.html\">Grouping resources in an AppComponent</a>.</p>"}}}, "CreateAppVersionAppComponentResponse": {"type": "structure", "required": ["appArn", "appVersion"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "appComponent": {"shape": "AppComponent", "documentation": "<p>List of Application Components that belong to this resource.</p>"}, "appVersion": {"shape": "EntityVersion", "documentation": "<p>Resilience Hub application version.</p>"}}}, "CreateAppVersionResourceRequest": {"type": "structure", "required": ["appArn", "appComponents", "logicalResourceId", "physicalResourceId", "resourceType"], "members": {"additionalInfo": {"shape": "AdditionalInfoMap", "documentation": "<p>Currently, there is no supported additional information for resources.</p>"}, "appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "appComponents": {"shape": "AppComponentNameList", "documentation": "<p>List of Application Components that this resource belongs to. If an Application Component is not part of the Resilience Hub application, it will be added.</p>"}, "awsAccountId": {"shape": "CustomerId", "documentation": "<p>Amazon Web Services account that owns the physical resource.</p>"}, "awsRegion": {"shape": "AwsRegion", "documentation": "<p>Amazon Web Services region that owns the physical resource.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>Used for an idempotency token. A client token is a unique, case-sensitive string of up to 64 ASCII characters. You should not reuse the same client token for other API requests.</p>", "idempotencyToken": true}, "logicalResourceId": {"shape": "LogicalResourceId", "documentation": "<p>Logical identifier of the resource.</p>"}, "physicalResourceId": {"shape": "String2048", "documentation": "<p>Physical identifier of the resource.</p>"}, "resourceName": {"shape": "EntityName", "documentation": "<p>Name of the resource.</p>"}, "resourceType": {"shape": "String255", "documentation": "<p>Type of resource.</p>"}}}, "CreateAppVersionResourceResponse": {"type": "structure", "required": ["appArn", "appVersion"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "appVersion": {"shape": "EntityVersion", "documentation": "<p>Resilience Hub application version.</p>"}, "physicalResource": {"shape": "PhysicalResource", "documentation": "<p>Defines a physical resource. A physical resource is a resource that exists in your account. It can be identified using an Amazon Resource Name (ARN) or a Resilience Hub-native identifier.</p>"}}}, "CreateRecommendationTemplateRequest": {"type": "structure", "required": ["assessmentArn", "name"], "members": {"assessmentArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the assessment. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app-assessment/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "bucketName": {"shape": "EntityName", "documentation": "<p>The name of the Amazon S3 bucket that will contain the recommendation template.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>Used for an idempotency token. A client token is a unique, case-sensitive string of up to 64 ASCII characters. You should not reuse the same client token for other API requests.</p>", "idempotencyToken": true}, "format": {"shape": "TemplateFormat", "documentation": "<p>The format for the recommendation template.</p> <dl> <dt>Cfn<PERSON>son</dt> <dd> <p>The template is CloudFormation JSON.</p> </dd> <dt>CfnYaml</dt> <dd> <p>The template is CloudFormation YAML.</p> </dd> </dl>"}, "name": {"shape": "EntityName", "documentation": "<p>The name for the recommendation template.</p>"}, "recommendationIds": {"shape": "RecommendationIdList", "documentation": "<p>Identifiers for the recommendations used to create a recommendation template.</p>"}, "recommendationTypes": {"shape": "RenderRecommendationTypeList", "documentation": "<p>An array of strings that specify the recommendation template type or types.</p> <dl> <dt>Alarm</dt> <dd> <p>The template is an <a>AlarmRecommendation</a> template.</p> </dd> <dt>Sop</dt> <dd> <p>The template is a <a>SopRecommendation</a> template.</p> </dd> <dt>Test</dt> <dd> <p>The template is a <a>TestRecommendation</a> template.</p> </dd> </dl>"}, "tags": {"shape": "TagMap", "documentation": "<p>Tags assigned to the resource. A tag is a label that you assign to an Amazon Web Services resource. Each tag consists of a key/value pair.</p>"}}}, "CreateRecommendationTemplateResponse": {"type": "structure", "members": {"recommendationTemplate": {"shape": "RecommendationTemplate", "documentation": "<p>The newly created recommendation template, returned as an object. This object includes the template's name, format, status, tags, Amazon S3 bucket location, and more.</p>"}}}, "CreateResiliencyPolicyRequest": {"type": "structure", "required": ["policy", "policyName", "tier"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>Used for an idempotency token. A client token is a unique, case-sensitive string of up to 64 ASCII characters. You should not reuse the same client token for other API requests.</p>", "idempotencyToken": true}, "dataLocationConstraint": {"shape": "DataLocationConstraint", "documentation": "<p>Specifies a high-level geographical location constraint for where your resilience policy data can be stored.</p>"}, "policy": {"shape": "DisruptionPolicy", "documentation": "<p>The type of resiliency policy to be created, including the recovery time objective (RTO) and recovery point objective (RPO) in seconds.</p>"}, "policyDescription": {"shape": "EntityDescription", "documentation": "<p>Description of the resiliency policy.</p>"}, "policyName": {"shape": "EntityName", "documentation": "<p>Name of the resiliency policy.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>Tags assigned to the resource. A tag is a label that you assign to an Amazon Web Services resource. Each tag consists of a key/value pair.</p>"}, "tier": {"shape": "ResiliencyPolicyTier", "documentation": "<p>The tier for this resiliency policy, ranging from the highest severity (<code>MissionCritical</code>) to lowest (<code>NonCritical</code>).</p>"}}}, "CreateResiliencyPolicyResponse": {"type": "structure", "required": ["policy"], "members": {"policy": {"shape": "ResiliencyPolicy", "documentation": "<p>The type of resiliency policy that was created, including the recovery time objective (RTO) and recovery point objective (RPO) in seconds.</p>"}}}, "CurrencyCode": {"type": "string", "max": 3, "min": 0}, "CustomerId": {"type": "string", "pattern": "^[0-9]{12}$"}, "DataLocationConstraint": {"type": "string", "enum": ["AnyLocation", "SameContinent", "SameCountry"]}, "DeleteAppAssessmentRequest": {"type": "structure", "required": ["assessmentArn"], "members": {"assessmentArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the assessment. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app-assessment/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>Used for an idempotency token. A client token is a unique, case-sensitive string of up to 64 ASCII characters. You should not reuse the same client token for other API requests.</p>", "idempotencyToken": true}}}, "DeleteAppAssessmentResponse": {"type": "structure", "required": ["assessmentArn", "assessmentStatus"], "members": {"assessmentArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the assessment. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app-assessment/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "assessmentStatus": {"shape": "AssessmentStatus", "documentation": "<p>The current status of the assessment for the resiliency policy.</p>"}}}, "DeleteAppInputSourceRequest": {"type": "structure", "required": ["appArn"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>Used for an idempotency token. A client token is a unique, case-sensitive string of up to 64 ASCII characters. You should not reuse the same client token for other API requests.</p>", "idempotencyToken": true}, "eksSourceClusterNamespace": {"shape": "EksSourceClusterNamespace", "documentation": "<p>The namespace on your Amazon Elastic Kubernetes Service cluster that you want to delete from the Resilience Hub application.</p>"}, "sourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the imported resource you want to remove from the Resilience Hub application. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "terraformSource": {"shape": "TerraformSource", "documentation": "<p>The imported Terraform s3 state ﬁle you want to remove from the Resilience Hub application.</p>"}}}, "DeleteAppInputSourceResponse": {"type": "structure", "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "appInputSource": {"shape": "AppInputSource", "documentation": "<p>Name of the input source from where the application resource is imported from.</p>"}}}, "DeleteAppRequest": {"type": "structure", "required": ["appArn"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>Used for an idempotency token. A client token is a unique, case-sensitive string of up to 64 ASCII characters. You should not reuse the same client token for other API requests.</p>", "idempotencyToken": true}, "forceDelete": {"shape": "BooleanOptional", "documentation": "<p>A boolean option to force the deletion of an Resilience Hub application. </p>"}}}, "DeleteAppResponse": {"type": "structure", "required": ["appArn"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}}}, "DeleteAppVersionAppComponentRequest": {"type": "structure", "required": ["appArn", "id"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>Used for an idempotency token. A client token is a unique, case-sensitive string of up to 64 ASCII characters. You should not reuse the same client token for other API requests.</p>", "idempotencyToken": true}, "id": {"shape": "String255", "documentation": "<p>Identifier of the Application Component.</p>"}}}, "DeleteAppVersionAppComponentResponse": {"type": "structure", "required": ["appArn", "appVersion"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "appComponent": {"shape": "AppComponent", "documentation": "<p>List of Application Components that belong to this resource.</p>"}, "appVersion": {"shape": "EntityVersion", "documentation": "<p>Resilience Hub application version.</p>"}}}, "DeleteAppVersionResourceRequest": {"type": "structure", "required": ["appArn"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "awsAccountId": {"shape": "CustomerId", "documentation": "<p>Amazon Web Services account that owns the physical resource.</p>"}, "awsRegion": {"shape": "AwsRegion", "documentation": "<p>Amazon Web Services region that owns the physical resource.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>Used for an idempotency token. A client token is a unique, case-sensitive string of up to 64 ASCII characters. You should not reuse the same client token for other API requests.</p>", "idempotencyToken": true}, "logicalResourceId": {"shape": "LogicalResourceId", "documentation": "<p>Logical identifier of the resource.</p>"}, "physicalResourceId": {"shape": "String2048", "documentation": "<p>Physical identifier of the resource.</p>"}, "resourceName": {"shape": "EntityName", "documentation": "<p>Name of the resource.</p>"}}}, "DeleteAppVersionResourceResponse": {"type": "structure", "required": ["appArn", "appVersion"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "appVersion": {"shape": "EntityVersion", "documentation": "<p>Resilience Hub application version.</p>"}, "physicalResource": {"shape": "PhysicalResource", "documentation": "<p>Defines a physical resource. A physical resource is a resource that exists in your account. It can be identified using an Amazon Resource Name (ARN) or a Resilience Hub-native identifier.</p>"}}}, "DeleteRecommendationTemplateRequest": {"type": "structure", "required": ["recommendationTemplateArn"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>Used for an idempotency token. A client token is a unique, case-sensitive string of up to 64 ASCII characters. You should not reuse the same client token for other API requests.</p>", "idempotencyToken": true}, "recommendationTemplateArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) for a recommendation template.</p>"}}}, "DeleteRecommendationTemplateResponse": {"type": "structure", "required": ["recommendationTemplateArn", "status"], "members": {"recommendationTemplateArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) for a recommendation template.</p>"}, "status": {"shape": "RecommendationTemplateStatus", "documentation": "<p>Status of the action.</p>"}}}, "DeleteResiliencyPolicyRequest": {"type": "structure", "required": ["policyArn"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>Used for an idempotency token. A client token is a unique, case-sensitive string of up to 64 ASCII characters. You should not reuse the same client token for other API requests.</p>", "idempotencyToken": true}, "policyArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the resiliency policy. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:resiliency-policy/<code>policy-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}}}, "DeleteResiliencyPolicyResponse": {"type": "structure", "required": ["policyArn"], "members": {"policyArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the resiliency policy. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:resiliency-policy/<code>policy-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}}}, "DescribeAppAssessmentRequest": {"type": "structure", "required": ["assessmentArn"], "members": {"assessmentArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the assessment. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app-assessment/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}}}, "DescribeAppAssessmentResponse": {"type": "structure", "required": ["assessment"], "members": {"assessment": {"shape": "AppAssessment", "documentation": "<p>The assessment for an Resilience Hub application, returned as an object. This object includes Amazon Resource Names (ARNs), compliance information, compliance status, cost, messages, resiliency scores, and more.</p>"}}}, "DescribeAppRequest": {"type": "structure", "required": ["appArn"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}}}, "DescribeAppResponse": {"type": "structure", "required": ["app"], "members": {"app": {"shape": "App", "documentation": "<p>The specified application, returned as an object with details including compliance status, creation time, description, resiliency score, and more.</p>"}}}, "DescribeAppVersionAppComponentRequest": {"type": "structure", "required": ["appArn", "appVersion", "id"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "appVersion": {"shape": "EntityVersion", "documentation": "<p>Resilience Hub application version.</p>"}, "id": {"shape": "String255", "documentation": "<p>Identifier of the Application Component.</p>"}}}, "DescribeAppVersionAppComponentResponse": {"type": "structure", "required": ["appArn", "appVersion"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "appComponent": {"shape": "AppComponent", "documentation": "<p>List of Application Components that belong to this resource.</p>"}, "appVersion": {"shape": "EntityVersion", "documentation": "<p>Resilience Hub application version.</p>"}}}, "DescribeAppVersionRequest": {"type": "structure", "required": ["appArn", "appVersion"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "appVersion": {"shape": "EntityVersion", "documentation": "<p>Resilience Hub application version.</p>"}}}, "DescribeAppVersionResourceRequest": {"type": "structure", "required": ["appArn", "appVersion"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "appVersion": {"shape": "EntityVersion", "documentation": "<p>Resilience Hub application version.</p>"}, "awsAccountId": {"shape": "CustomerId", "documentation": "<p>Amazon Web Services account that owns the physical resource.</p>"}, "awsRegion": {"shape": "AwsRegion", "documentation": "<p>Amazon Web Services region that owns the physical resource.</p>"}, "logicalResourceId": {"shape": "LogicalResourceId", "documentation": "<p>Logical identifier of the resource.</p>"}, "physicalResourceId": {"shape": "String2048", "documentation": "<p>Physical identifier of the resource.</p>"}, "resourceName": {"shape": "EntityName", "documentation": "<p>Name of the resource.</p>"}}}, "DescribeAppVersionResourceResponse": {"type": "structure", "required": ["appArn", "appVersion"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "appVersion": {"shape": "EntityVersion", "documentation": "<p>Resilience Hub application version.</p>"}, "physicalResource": {"shape": "PhysicalResource", "documentation": "<p>Defines a physical resource. A physical resource is a resource that exists in your account. It can be identified using an Amazon Resource Name (ARN) or a Resilience Hub-native identifier.</p>"}}}, "DescribeAppVersionResourcesResolutionStatusRequest": {"type": "structure", "required": ["appArn", "appVersion"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "appVersion": {"shape": "EntityVersion", "documentation": "<p>The version of the application.</p>"}, "resolutionId": {"shape": "String255", "documentation": "<p>The identifier for a specific resolution.</p>"}}}, "DescribeAppVersionResourcesResolutionStatusResponse": {"type": "structure", "required": ["appArn", "appVersion", "resolutionId", "status"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "appVersion": {"shape": "EntityVersion", "documentation": "<p>The version of the application.</p>"}, "errorMessage": {"shape": "String500", "documentation": "<p>The returned error message for the request.</p>"}, "resolutionId": {"shape": "String255", "documentation": "<p>The identifier for a specific resolution.</p>"}, "status": {"shape": "ResourceResolutionStatusType", "documentation": "<p>Status of the action.</p>"}}}, "DescribeAppVersionResponse": {"type": "structure", "required": ["appArn", "appVersion"], "members": {"additionalInfo": {"shape": "AdditionalInfoMap", "documentation": "<p>Additional configuration parameters for an Resilience Hub application. If you want to implement <code>additionalInfo</code> through the Resilience Hub console rather than using an API call, see <a href=\"https://docs.aws.amazon.com/resilience-hub/latest/userguide/app-config-param.html\">Configure the application configuration parameters</a>.</p> <note> <p>Currently, this parameter supports only failover region and account.</p> </note>"}, "appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "appVersion": {"shape": "EntityVersion", "documentation": "<p>Resilience Hub application version.</p>"}}}, "DescribeAppVersionTemplateRequest": {"type": "structure", "required": ["appArn", "appVersion"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "appVersion": {"shape": "EntityVersion", "documentation": "<p>The version of the application.</p>"}}}, "DescribeAppVersionTemplateResponse": {"type": "structure", "required": ["appArn", "appTemplateBody", "appVersion"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "appTemplateBody": {"shape": "AppTemplateBody", "documentation": "<p>A JSON string that provides information about your application structure. To learn more about the <code>appTemplateBody</code> template, see the sample template provided in the <i>Examples</i> section.</p> <p>The <code>appTemplateBody</code> JSON string has the following structure:</p> <ul> <li> <p> <b> <code>resources</code> </b> </p> <p>The list of logical resources that must be included in the Resilience Hub application.</p> <p>Type: Array</p> <note> <p>Don't add the resources that you want to exclude.</p> </note> <p>Each <code>resources</code> array item includes the following fields:</p> <ul> <li> <p> <i> <code>logicalResourceId</code> </i> </p> <p>Logical identifier of the resource.</p> <p>Type: Object</p> <p>Each <code>logicalResourceId</code> object includes the following fields:</p> <ul> <li> <p> <code>identifier</code> </p> <p>Identifier of the resource.</p> <p>Type: String</p> </li> <li> <p> <code>logicalStackName</code> </p> <p>The name of the CloudFormation stack this resource belongs to.</p> <p>Type: String</p> </li> <li> <p> <code>resourceGroupName</code> </p> <p>The name of the resource group this resource belongs to.</p> <p>Type: String</p> </li> <li> <p> <code>terraformSourceName</code> </p> <p>The name of the Terraform S3 state file this resource belongs to.</p> <p>Type: String</p> </li> <li> <p> <code>eksSourceName</code> </p> <p>Name of the Amazon Elastic Kubernetes Service cluster and namespace this resource belongs to.</p> <note> <p>This parameter accepts values in \"eks-cluster/namespace\" format.</p> </note> <p>Type: String</p> </li> </ul> </li> <li> <p> <i> <code>type</code> </i> </p> <p>The type of resource.</p> <p>Type: string</p> </li> <li> <p> <i> <code>name</code> </i> </p> <p>The name of the resource.</p> <p>Type: String</p> </li> <li> <p> <code>additionalInfo</code> </p> <p>Additional configuration parameters for an Resilience Hub application. If you want to implement <code>additionalInfo</code> through the Resilience Hub console rather than using an API call, see <a href=\"https://docs.aws.amazon.com/resilience-hub/latest/userguide/app-config-param.html\">Configure the application configuration parameters</a>.</p> <note> <p>Currently, this parameter accepts a key-value mapping (in a string format) of only one failover region and one associated account.</p> <p>Key: <code>\"failover-regions\"</code> </p> <p>Value: <code>\"[{\"region\":\"&lt;REGION&gt;\", \"accounts\":[{\"id\":\"&lt;ACCOUNT_ID&gt;\"}]}]\"</code> </p> </note> </li> </ul> </li> <li> <p> <b> <code>appComponents</code> </b> </p> <p>List of Application Components that this resource belongs to. If an Application Component is not part of the Resilience Hub application, it will be added.</p> <p>Type: Array</p> <p>Each <code>appComponents</code> array item includes the following fields:</p> <ul> <li> <p> <code>name</code> </p> <p>Name of the Application Component.</p> <p>Type: String</p> </li> <li> <p> <code>type</code> </p> <p>Type of Application Component. For more information about the types of Application Component, see <a href=\"https://docs.aws.amazon.com/resilience-hub/latest/userguide/AppComponent.grouping.html\">Grouping resources in an AppComponent</a>.</p> <p>Type: String</p> </li> <li> <p> <code>resourceNames</code> </p> <p>The list of included resources that are assigned to the Application Component.</p> <p>Type: Array of strings</p> </li> <li> <p> <code>additionalInfo</code> </p> <p>Additional configuration parameters for an Resilience Hub application. If you want to implement <code>additionalInfo</code> through the Resilience Hub console rather than using an API call, see <a href=\"https://docs.aws.amazon.com/resilience-hub/latest/userguide/app-config-param.html\">Configure the application configuration parameters</a>.</p> <note> <p>Currently, this parameter accepts a key-value mapping (in a string format) of only one failover region and one associated account.</p> <p>Key: <code>\"failover-regions\"</code> </p> <p>Value: <code>\"[{\"region\":\"&lt;REGION&gt;\", \"accounts\":[{\"id\":\"&lt;ACCOUNT_ID&gt;\"}]}]\"</code> </p> </note> </li> </ul> </li> <li> <p> <b> <code>excludedResources</code> </b> </p> <p>The list of logical resource identifiers to be excluded from the application.</p> <p>Type: Array</p> <note> <p>Don't add the resources that you want to include.</p> </note> <p>Each <code>excludedResources</code> array item includes the following fields:</p> <ul> <li> <p> <i> <code>logicalResourceIds</code> </i> </p> <p>Logical identifier of the resource.</p> <p>Type: Object</p> <note> <p>You can configure only one of the following fields:</p> <ul> <li> <p> <code>logicalStackName</code> </p> </li> <li> <p> <code>resourceGroupName</code> </p> </li> <li> <p> <code>terraformSourceName</code> </p> </li> <li> <p> <code>eksSourceName</code> </p> </li> </ul> </note> <p>Each <code>logicalResourceIds</code> object includes the following fields:</p> <ul> <li> <p> <code>identifier</code> </p> <p>Identifier of the resource.</p> <p>Type: String</p> </li> <li> <p> <code>logicalStackName</code> </p> <p>The name of the CloudFormation stack this resource belongs to.</p> <p>Type: String</p> </li> <li> <p> <code>resourceGroupName</code> </p> <p>The name of the resource group this resource belongs to.</p> <p>Type: String</p> </li> <li> <p> <code>terraformSourceName</code> </p> <p>The name of the Terraform S3 state file this resource belongs to.</p> <p>Type: String</p> </li> <li> <p> <code>eksSourceName</code> </p> <p>Name of the Amazon Elastic Kubernetes Service cluster and namespace this resource belongs to.</p> <note> <p>This parameter accepts values in \"eks-cluster/namespace\" format.</p> </note> <p>Type: String</p> </li> </ul> </li> </ul> </li> <li> <p> <b> <code>version</code> </b> </p> <p>Resilience Hub application version.</p> </li> <li> <p> <code>additionalInfo</code> </p> <p>Additional configuration parameters for an Resilience Hub application. If you want to implement <code>additionalInfo</code> through the Resilience Hub console rather than using an API call, see <a href=\"https://docs.aws.amazon.com/resilience-hub/latest/userguide/app-config-param.html\">Configure the application configuration parameters</a>.</p> <note> <p>Currently, this parameter accepts a key-value mapping (in a string format) of only one failover region and one associated account.</p> <p>Key: <code>\"failover-regions\"</code> </p> <p>Value: <code>\"[{\"region\":\"&lt;REGION&gt;\", \"accounts\":[{\"id\":\"&lt;ACCOUNT_ID&gt;\"}]}]\"</code> </p> </note> </li> </ul>"}, "appVersion": {"shape": "EntityVersion", "documentation": "<p>The version of the application.</p>"}}}, "DescribeDraftAppVersionResourcesImportStatusRequest": {"type": "structure", "required": ["appArn"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}}}, "DescribeDraftAppVersionResourcesImportStatusResponse": {"type": "structure", "required": ["appArn", "appVersion", "status", "statusChangeTime"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "appVersion": {"shape": "EntityVersion", "documentation": "<p>The version of the application.</p>"}, "errorDetails": {"shape": "ErrorDetailList", "documentation": "<p>List of errors that were encountered while importing resources.</p>"}, "errorMessage": {"shape": "String500", "documentation": "<p>The error message returned for the resource request.</p>"}, "status": {"shape": "ResourceImportStatusType", "documentation": "<p>Status of the action.</p>"}, "statusChangeTime": {"shape": "TimeStamp", "documentation": "<p>The time when the status last changed.</p>"}}}, "DescribeMetricsExportRequest": {"type": "structure", "required": ["metricsExportId"], "members": {"metricsExportId": {"shape": "String255", "documentation": "<p>Identifier of the metrics export task.</p>"}}}, "DescribeMetricsExportResponse": {"type": "structure", "required": ["metricsExportId", "status"], "members": {"errorMessage": {"shape": "String500", "documentation": "<p>Explains the error that occurred while exporting the metrics.</p>"}, "exportLocation": {"shape": "S3Location", "documentation": "<p>Specifies the name of the Amazon S3 bucket where the exported metrics is stored.</p>"}, "metricsExportId": {"shape": "String255", "documentation": "<p>Identifier for the metrics export task.</p>"}, "status": {"shape": "MetricsExportStatusType", "documentation": "<p>Indicates the status of the metrics export task.</p>"}}}, "DescribeResiliencyPolicyRequest": {"type": "structure", "required": ["policyArn"], "members": {"policyArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the resiliency policy. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:resiliency-policy/<code>policy-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}}}, "DescribeResiliencyPolicyResponse": {"type": "structure", "required": ["policy"], "members": {"policy": {"shape": "ResiliencyPolicy", "documentation": "<p>Information about the specific resiliency policy, returned as an object. This object includes creation time, data location constraints, its name, description, tags, the recovery time objective (RTO) and recovery point objective (RPO) in seconds, and more.</p>"}}}, "DescribeResourceGroupingRecommendationTaskRequest": {"type": "structure", "required": ["appArn"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "groupingId": {"shape": "String255", "documentation": "<p>Identifier of the grouping recommendation task.</p>"}}}, "DescribeResourceGroupingRecommendationTaskResponse": {"type": "structure", "required": ["groupingId", "status"], "members": {"errorMessage": {"shape": "String500", "documentation": "<p>Error that occurred while generating a grouping recommendation.</p>"}, "groupingId": {"shape": "String255", "documentation": "<p>Identifier of the grouping recommendation task.</p>"}, "status": {"shape": "ResourcesGroupingRecGenStatusType", "documentation": "<p>Status of the action.</p>"}}}, "DifferenceType": {"type": "string", "enum": ["NotEqual", "Added", "Removed"]}, "DisruptionCompliance": {"type": "structure", "required": ["complianceStatus"], "members": {"achievableRpoInSecs": {"shape": "Seconds", "documentation": "<p>The Recovery Point Objective (RPO) that is achievable, in seconds.</p>"}, "achievableRtoInSecs": {"shape": "Seconds", "documentation": "<p>The Recovery Time Objective (RTO) that is achievable, in seconds</p>"}, "complianceStatus": {"shape": "ComplianceStatus", "documentation": "<p>The current status of compliance for the resiliency policy.</p>"}, "currentRpoInSecs": {"shape": "Seconds", "documentation": "<p>The current RPO, in seconds.</p>"}, "currentRtoInSecs": {"shape": "Seconds", "documentation": "<p>The current RTO, in seconds.</p>"}, "message": {"shape": "String500", "documentation": "<p>The disruption compliance message.</p>"}, "rpoDescription": {"shape": "String500", "documentation": "<p>The RPO description.</p>"}, "rpoReferenceId": {"shape": "String500", "documentation": "<p>Reference identifier of the RPO .</p>"}, "rtoDescription": {"shape": "String500", "documentation": "<p>The RTO description.</p>"}, "rtoReferenceId": {"shape": "String500", "documentation": "<p>Reference identifier of the RTO.</p>"}}, "documentation": "<p>Defines the compliance against the resiliency policy for a disruption.</p>"}, "DisruptionPolicy": {"type": "map", "key": {"shape": "DisruptionType"}, "value": {"shape": "FailurePolicy"}}, "DisruptionResiliencyScore": {"type": "map", "key": {"shape": "DisruptionType"}, "value": {"shape": "Double"}}, "DisruptionType": {"type": "string", "enum": ["Software", "Hardware", "AZ", "Region"]}, "DocumentName": {"type": "string", "max": 500, "min": 1}, "Double": {"type": "double"}, "DriftStatus": {"type": "string", "enum": ["NotChecked", "NotDetected", "Detected"]}, "DriftType": {"type": "string", "enum": ["ApplicationCompliance", "AppComponentResiliencyComplianceStatus"]}, "EksNamespace": {"type": "string", "max": 63, "min": 1, "pattern": "^[a-z0-9]([-a-z0-9]*[a-z0-9])?$"}, "EksNamespaceList": {"type": "list", "member": {"shape": "EksNamespace"}}, "EksSource": {"type": "structure", "required": ["eksClusterArn", "namespaces"], "members": {"eksClusterArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Amazon Elastic Kubernetes Service cluster. The format for this ARN is: arn:<code>aws</code>:eks:<code>region</code>:<code>account-id</code>:cluster/<code>cluster-name</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "namespaces": {"shape": "EksNamespaceList", "documentation": "<p>The list of namespaces located on your Amazon Elastic Kubernetes Service cluster.</p>"}}, "documentation": "<p>The input source of the Amazon Elastic Kubernetes Service cluster.</p>"}, "EksSourceClusterNamespace": {"type": "structure", "required": ["eksClusterArn", "namespace"], "members": {"eksClusterArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Amazon Elastic Kubernetes Service cluster. The format for this ARN is: arn:<code>aws</code>:eks:<code>region</code>:<code>account-id</code>:cluster/<code>cluster-name</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "namespace": {"shape": "EksNamespace", "documentation": "<p>Name of the namespace that is located on your Amazon Elastic Kubernetes Service cluster.</p>"}}, "documentation": "<p>The input source of the namespace that is located on your Amazon Elastic Kubernetes Service cluster.</p>"}, "EksSourceList": {"type": "list", "member": {"shape": "EksSource"}}, "EntityDescription": {"type": "string", "max": 500, "min": 0}, "EntityId": {"type": "string", "pattern": "^\\S{1,255}$"}, "EntityName": {"type": "string", "pattern": "^[A-Za-z0-9][A-Za-z0-9_\\-]{1,59}$"}, "EntityName255": {"type": "string", "pattern": "^[A-Za-z0-9][A-Za-z0-9_\\-]{0,254}$"}, "EntityNameList": {"type": "list", "member": {"shape": "EntityName"}}, "EntityVersion": {"type": "string", "pattern": "^\\S{1,50}$"}, "ErrorDetail": {"type": "structure", "members": {"errorMessage": {"shape": "ErrorMessage", "documentation": "<p>Provides additional information about the error.</p>"}}, "documentation": "<p>Indicates the error that was encountered while importing a resource.</p>"}, "ErrorDetailList": {"type": "list", "member": {"shape": "ErrorDetail"}}, "ErrorMessage": {"type": "string", "max": 500, "min": 0}, "EstimatedCostTier": {"type": "string", "enum": ["L1", "L2", "L3", "L4"]}, "EventSubscription": {"type": "structure", "required": ["eventType", "name"], "members": {"eventType": {"shape": "EventType", "documentation": "<p>The type of event you would like to subscribe and get notification for. Currently, Resilience Hub supports notifications only for <b>Drift detected</b> (<code>DriftDetected</code>) and <b>Scheduled assessment failure</b> (<code>ScheduledAssessmentFailure</code>) events.</p>"}, "name": {"shape": "String255", "documentation": "<p>Unique name to identify an event subscription.</p>"}, "snsTopicArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Amazon Simple Notification Service topic. The format for this ARN is: <code>arn:partition:sns:region:account:topic-name</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}}, "documentation": "<p>Indicates an event you would like to subscribe and get notification for. Currently, Resilience Hub supports notifications only for <b>Drift detected</b> and <b>Scheduled assessment failure</b> events.</p>"}, "EventSubscriptionList": {"type": "list", "member": {"shape": "EventSubscription"}, "max": 10, "min": 0}, "EventType": {"type": "string", "enum": ["ScheduledAssessmentFailure", "DriftDetected"]}, "ExcludeRecommendationReason": {"type": "string", "enum": ["AlreadyImplemented", "NotRelevant", "ComplexityOfImplementation"]}, "Experiment": {"type": "structure", "members": {"experimentArn": {"shape": "String255", "documentation": "<p>Amazon Resource Name (ARN) of the FIS experiment.</p>"}, "experimentTemplateId": {"shape": "String255", "documentation": "<p>Identifier of the FIS experiment template.</p>"}}, "documentation": "<p>Indicates the FIS experiment detected while running an assessment.</p>"}, "FailedGroupingRecommendationEntries": {"type": "list", "member": {"shape": "FailedGroupingRecommendationEntry"}}, "FailedGroupingRecommendationEntry": {"type": "structure", "required": ["errorMessage", "groupingRecommendationId"], "members": {"errorMessage": {"shape": "ErrorMessage", "documentation": "<p>Indicates the error that occurred while implementing a grouping recommendation.</p>"}, "groupingRecommendationId": {"shape": "String255", "documentation": "<p>Indicates the identifier of the grouping recommendation.</p>"}}, "documentation": "<p>Indicates the accepted grouping recommendation whose implementation failed.</p>"}, "FailurePolicy": {"type": "structure", "required": ["rpoInSecs", "rtoInSecs"], "members": {"rpoInSecs": {"shape": "Seconds", "documentation": "<p>Recovery Point Objective (RPO) in seconds.</p>"}, "rtoInSecs": {"shape": "Seconds", "documentation": "<p>Recovery Time Objective (RTO) in seconds.</p>"}}, "documentation": "<p>Defines a failure policy.</p>"}, "Field": {"type": "structure", "required": ["name"], "members": {"aggregation": {"shape": "FieldAggregationType", "documentation": "<p>(Optional) Indicates the type of aggregation or summary operation (such as Sum, Average, and so on) to be performed on a particular field or set of data.</p>"}, "name": {"shape": "String255", "documentation": "<p>Name of the field.</p>"}}, "documentation": "<p>Indicates the field or attribute of a resource or data structure on which a condition is being applied or evaluated.</p>"}, "FieldAggregationType": {"type": "string", "enum": ["Min", "Max", "Sum", "Avg", "Count"]}, "FieldList": {"type": "list", "member": {"shape": "Field"}, "max": 50, "min": 0}, "GroupingAppComponent": {"type": "structure", "required": ["appComponentId", "appComponentName", "appComponentType"], "members": {"appComponentId": {"shape": "EntityName255", "documentation": "<p>Indicates the identifier of an AppComponent.</p>"}, "appComponentName": {"shape": "EntityName255", "documentation": "<p>Indicates the name of an AppComponent.</p>"}, "appComponentType": {"shape": "String255", "documentation": "<p>Indicates the type of an AppComponent.</p>"}}, "documentation": "<p>Creates a new recommended Application Component (AppComponent).</p>"}, "GroupingRecommendation": {"type": "structure", "required": ["confidenceLevel", "creationTime", "groupingAppComponent", "groupingRecommendationId", "recommendationReasons", "resources", "score", "status"], "members": {"confidenceLevel": {"shape": "GroupingRecommendationConfidenceLevel", "documentation": "<p>Indicates the confidence level of Resilience Hub on the grouping recommendation.</p>"}, "creationTime": {"shape": "TimeStamp", "documentation": "<p>Indicates the creation time of the grouping recommendation.</p>"}, "groupingAppComponent": {"shape": "GroupingAppComponent", "documentation": "<p>Indicates the name of the recommended Application Component (AppComponent).</p>"}, "groupingRecommendationId": {"shape": "String255", "documentation": "<p>Indicates all the reasons available for rejecting a grouping recommendation.</p>"}, "recommendationReasons": {"shape": "String255List", "documentation": "<p>Indicates all the reasons available for rejecting a grouping recommendation.</p>"}, "rejectionReason": {"shape": "GroupingRecommendationRejectionReason", "documentation": "<p>Indicates the reason you had selected while rejecting a grouping recommendation.</p>"}, "resources": {"shape": "GroupingResourceList", "documentation": "<p>Indicates the resources that are grouped in a recommended AppComponent.</p>"}, "score": {"shape": "Double", "documentation": "<p>Indicates the confidence level of the grouping recommendation.</p>"}, "status": {"shape": "GroupingRecommendationStatusType", "documentation": "<p>Indicates the status of grouping resources into AppComponents.</p>"}}, "documentation": "<p>Creates a new grouping recommendation.</p>"}, "GroupingRecommendationConfidenceLevel": {"type": "string", "enum": ["High", "Medium"]}, "GroupingRecommendationList": {"type": "list", "member": {"shape": "GroupingRecommendation"}}, "GroupingRecommendationRejectionReason": {"type": "string", "enum": ["DistinctBusinessPurpose", "SeparateDataConcern", "DistinctUserGroupHandling", "Other"]}, "GroupingRecommendationStatusType": {"type": "string", "enum": ["Accepted", "Rejected", "PendingDecision"]}, "GroupingResource": {"type": "structure", "required": ["logicalResourceId", "physicalResourceId", "resourceName", "resourceType", "sourceAppComponentIds"], "members": {"logicalResourceId": {"shape": "LogicalResourceId", "documentation": "<p>Indicates the logical identifier of the resource.</p>"}, "physicalResourceId": {"shape": "PhysicalResourceId", "documentation": "<p>Indicates the physical identifier of the resource.</p>"}, "resourceName": {"shape": "String255", "documentation": "<p>Indicates the resource name.</p>"}, "resourceType": {"shape": "String255", "documentation": "<p>Indicates the resource type.</p>"}, "sourceAppComponentIds": {"shape": "String255List", "documentation": "<p>Indicates the identifier of the source AppComponents in which the resources were previously grouped into.</p>"}}, "documentation": "<p>Indicates the resource that will be grouped in the recommended Application Component (AppComponent).</p>"}, "GroupingResourceList": {"type": "list", "member": {"shape": "GroupingResource"}}, "HaArchitecture": {"type": "string", "enum": ["MultiSite", "WarmStandby", "PilotLight", "BackupAndRestore", "NoRecoveryPlan"]}, "IamRoleArn": {"type": "string", "pattern": "^arn:(aws|aws-cn|aws-iso|aws-iso-[a-z]{1}|aws-us-gov):iam::[0-9]{12}:role/(([^/][!-~]+/){1,511})?[A-Za-z0-9_+=,.@-]{1,64}$"}, "IamRoleArnList": {"type": "list", "member": {"shape": "IamRoleArn"}, "max": 10, "min": 0}, "IamRoleName": {"type": "string", "pattern": "^([^/]([!-~]+/){1,511})?[A-Za-z0-9_+=,.@-]{1,64}$"}, "ImportResourcesToDraftAppVersionRequest": {"type": "structure", "required": ["appArn"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "eksSources": {"shape": "EksSourceList", "documentation": "<p>The input sources of the Amazon Elastic Kubernetes Service resources you need to import.</p>"}, "importStrategy": {"shape": "ResourceImportStrategyType", "documentation": "<p>The import strategy you would like to set to import resources into Resilience Hub application.</p>"}, "sourceArns": {"shape": "ArnList", "documentation": "<p>The Amazon Resource Names (ARNs) for the resources.</p>"}, "terraformSources": {"shape": "TerraformSourceList", "documentation": "<p> A list of terraform file s3 URLs you need to import. </p>"}}}, "ImportResourcesToDraftAppVersionResponse": {"type": "structure", "required": ["appArn", "appVersion", "status"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "appVersion": {"shape": "EntityVersion", "documentation": "<p>The version of the application.</p>"}, "eksSources": {"shape": "EksSourceList", "documentation": "<p>The input sources of the Amazon Elastic Kubernetes Service resources you have imported.</p>"}, "sourceArns": {"shape": "ArnList", "documentation": "<p>The Amazon Resource Names (ARNs) for the resources you have imported.</p>"}, "status": {"shape": "ResourceImportStatusType", "documentation": "<p>Status of the action.</p>"}, "terraformSources": {"shape": "TerraformSourceList", "documentation": "<p> A list of terraform file s3 URLs you have imported. </p>"}}}, "Integer": {"type": "integer"}, "IntegerOptional": {"type": "integer", "box": true}, "InternalServerException": {"type": "structure", "members": {"message": {"shape": "String500"}}, "documentation": "<p>This exception occurs when there is an internal failure in the Resilience Hub service.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true}, "ListAlarmRecommendationsRequest": {"type": "structure", "required": ["assessmentArn"], "members": {"assessmentArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the assessment. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app-assessment/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>Maximum number of results to include in the response. If more results exist than the specified <code>MaxResults</code> value, a token is included in the response so that the remaining results can be retrieved.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p><PERSON>ull, or the token from a previous call to get the next set of results.</p>"}}}, "ListAlarmRecommendationsResponse": {"type": "structure", "required": ["alarmRecommendations"], "members": {"alarmRecommendations": {"shape": "AlarmRecommendationList", "documentation": "<p>The alarm recommendations for an Resilience Hub application, returned as an object. This object includes Application Component names, descriptions, information about whether a recommendation has already been implemented or not, prerequisites, and more.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>Token for the next set of results, or null if there are no more results.</p>"}}}, "ListAppAssessmentComplianceDriftsRequest": {"type": "structure", "required": ["assessmentArn"], "members": {"assessmentArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the assessment. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app-assessment/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>Maximum number of compliance drifts requested.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p><PERSON>ull, or the token from a previous call to get the next set of results.</p>"}}}, "ListAppAssessmentComplianceDriftsResponse": {"type": "structure", "required": ["complianceDrifts"], "members": {"complianceDrifts": {"shape": "ComplianceDriftList", "documentation": "<p>Indicates compliance drifts (recovery time objective (RTO) and recovery point objective (RPO)) detected for an assessed entity.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p><PERSON>ull, or the token from a previous call to get the next set of results.</p>"}}}, "ListAppAssessmentResourceDriftsRequest": {"type": "structure", "required": ["assessmentArn"], "members": {"assessmentArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the assessment. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app-assessment/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>Maximum number of drift results to include in the response. If more results exist than the specified <code>MaxResults</code> value, a token is included in the response so that the remaining results can be retrieved.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p><PERSON>ull, or the token from a previous call to get the next set of results.</p>"}}}, "ListAppAssessmentResourceDriftsResponse": {"type": "structure", "required": ["resourceDrifts"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p><PERSON>ull, or the token from a previous call to get the next set of results.</p>"}, "resourceDrifts": {"shape": "ResourceDriftList", "documentation": "<p>Indicates all the resource drifts detected for an assessed entity.</p>"}}}, "ListAppAssessmentsRequest": {"type": "structure", "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>", "location": "querystring", "locationName": "appArn"}, "assessmentName": {"shape": "EntityName", "documentation": "<p>The name for the assessment.</p>", "location": "querystring", "locationName": "assessmentName"}, "assessmentStatus": {"shape": "AssessmentStatusList", "documentation": "<p>The current status of the assessment for the resiliency policy.</p>", "location": "querystring", "locationName": "assessmentStatus"}, "complianceStatus": {"shape": "ComplianceStatus", "documentation": "<p>The current status of compliance for the resiliency policy.</p>", "location": "querystring", "locationName": "complianceStatus"}, "invoker": {"shape": "AssessmentInvoker", "documentation": "<p>Specifies the entity that invoked a specific assessment, either a <code>User</code> or the <code>System</code>.</p>", "location": "querystring", "locationName": "invoker"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>Maximum number of results to include in the response. If more results exist than the specified <code>MaxResults</code> value, a token is included in the response so that the remaining results can be retrieved.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "NextToken", "documentation": "<p><PERSON>ull, or the token from a previous call to get the next set of results.</p>", "location": "querystring", "locationName": "nextToken"}, "reverseOrder": {"shape": "BooleanOptional", "documentation": "<p>The default is to sort by ascending <b>startTime</b>. To sort by descending <b>startTime</b>, set reverseOrder to <code>true</code>.</p>", "location": "querystring", "locationName": "reverseOrder"}}}, "ListAppAssessmentsResponse": {"type": "structure", "required": ["assessmentSummaries"], "members": {"assessmentSummaries": {"shape": "AppAssessmentSummaryList", "documentation": "<p>The summaries for the specified assessments, returned as an object. This object includes application versions, associated Amazon Resource Numbers (ARNs), cost, messages, resiliency scores, and more.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>Token for the next set of results, or null if there are no more results.</p>"}}}, "ListAppComponentCompliancesRequest": {"type": "structure", "required": ["assessmentArn"], "members": {"assessmentArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the assessment. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app-assessment/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>Maximum number of results to include in the response. If more results exist than the specified <code>MaxResults</code> value, a token is included in the response so that the remaining results can be retrieved.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p><PERSON>ull, or the token from a previous call to get the next set of results.</p>"}}}, "ListAppComponentCompliancesResponse": {"type": "structure", "required": ["componentCompliances"], "members": {"componentCompliances": {"shape": "ComponentCompliancesList", "documentation": "<p>The compliances for an Resilience Hub Application Component, returned as an object. This object contains the names of the Application Components, compliances, costs, resiliency scores, outage scores, and more.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>Token for the next set of results, or null if there are no more results.</p>"}}}, "ListAppComponentRecommendationsRequest": {"type": "structure", "required": ["assessmentArn"], "members": {"assessmentArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the assessment. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app-assessment/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>Maximum number of results to include in the response. If more results exist than the specified <code>MaxResults</code> value, a token is included in the response so that the remaining results can be retrieved.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p><PERSON>ull, or the token from a previous call to get the next set of results.</p>"}}}, "ListAppComponentRecommendationsResponse": {"type": "structure", "required": ["componentRecommendations"], "members": {"componentRecommendations": {"shape": "ComponentRecommendationList", "documentation": "<p>The recommendations for an Resilience Hub Application Component, returned as an object. This object contains the names of the Application Components, configuration recommendations, and recommendation statuses.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>Token for the next set of results, or null if there are no more results.</p>"}}}, "ListAppInputSourcesRequest": {"type": "structure", "required": ["appArn", "appVersion"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "appVersion": {"shape": "EntityVersion", "documentation": "<p>Resilience Hub application version.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>Maximum number of input sources to be displayed per Resilience Hub application.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p><PERSON>ull, or the token from a previous call to get the next set of results.</p>"}}}, "ListAppInputSourcesResponse": {"type": "structure", "required": ["appInputSources"], "members": {"appInputSources": {"shape": "AppInputSourceList", "documentation": "<p>The list of Resilience Hub application input sources.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>Token for the next set of results, or null if there are no more results.</p>"}}}, "ListAppVersionAppComponentsRequest": {"type": "structure", "required": ["appArn", "appVersion"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "appVersion": {"shape": "EntityVersion", "documentation": "<p>Version of the Application Component.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>Maximum number of Application Components to be displayed per Resilience Hub application version.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p><PERSON>ull, or the token from a previous call to get the next set of results.</p>"}}}, "ListAppVersionAppComponentsResponse": {"type": "structure", "required": ["appArn", "appVersion"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "appComponents": {"shape": "AppComponentList", "documentation": "<p>Defines an Application Component.</p>"}, "appVersion": {"shape": "EntityVersion", "documentation": "<p>Resilience Hub application version.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>Token for the next set of results, or null if there are no more results.</p>"}}}, "ListAppVersionResourceMappingsRequest": {"type": "structure", "required": ["appArn", "appVersion"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "appVersion": {"shape": "EntityVersion", "documentation": "<p>The version of the application.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>Maximum number of results to include in the response. If more results exist than the specified <code>MaxResults</code> value, a token is included in the response so that the remaining results can be retrieved.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p><PERSON>ull, or the token from a previous call to get the next set of results.</p>"}}}, "ListAppVersionResourceMappingsResponse": {"type": "structure", "required": ["resourceMappings"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>Token for the next set of results, or null if there are no more results.</p>"}, "resourceMappings": {"shape": "ResourceMappingList", "documentation": "<p>Mappings used to map logical resources from the template to physical resources. You can use the mapping type <code>CFN_STACK</code> if the application template uses a logical stack name. Or you can map individual resources by using the mapping type <code>RESOURCE</code>. We recommend using the mapping type <code>CFN_STACK</code> if the application is backed by a CloudFormation stack.</p>"}}}, "ListAppVersionResourcesRequest": {"type": "structure", "required": ["appArn", "appVersion"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "appVersion": {"shape": "EntityVersion", "documentation": "<p>The version of the application.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>Maximum number of results to include in the response. If more results exist than the specified <code>MaxResults</code> value, a token is included in the response so that the remaining results can be retrieved.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p><PERSON>ull, or the token from a previous call to get the next set of results.</p>"}, "resolutionId": {"shape": "String255", "documentation": "<p>The identifier for a specific resolution.</p>"}}}, "ListAppVersionResourcesResponse": {"type": "structure", "required": ["physicalResources", "resolutionId"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>Token for the next set of results, or null if there are no more results.</p>"}, "physicalResources": {"shape": "PhysicalResourceList", "documentation": "<p>The physical resources in the application version.</p>"}, "resolutionId": {"shape": "String255", "documentation": "<p>The ID for a specific resolution.</p>"}}}, "ListAppVersionsRequest": {"type": "structure", "required": ["appArn"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "endTime": {"shape": "TimeStamp", "documentation": "<p>Upper limit of the time range to filter the application versions.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>Maximum number of results to include in the response. If more results exist than the specified <code>MaxResults</code> value, a token is included in the response so that the remaining results can be retrieved.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p><PERSON>ull, or the token from a previous call to get the next set of results.</p>"}, "startTime": {"shape": "TimeStamp", "documentation": "<p>Lower limit of the time range to filter the application versions.</p>"}}}, "ListAppVersionsResponse": {"type": "structure", "required": ["appVersions"], "members": {"appVersions": {"shape": "AppVersionList", "documentation": "<p>The version of the application.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>Token for the next set of results, or null if there are no more results.</p>"}}}, "ListAppsRequest": {"type": "structure", "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>", "location": "querystring", "locationName": "appArn"}, "awsApplicationArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of Resource Groups group that is integrated with an AppRegistry application. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>", "location": "querystring", "locationName": "awsApplicationArn"}, "fromLastAssessmentTime": {"shape": "TimeStamp", "documentation": "<p>Lower limit of the range that is used to filter applications based on their last assessment times.</p>", "location": "querystring", "locationName": "fromLastAssessmentTime"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>Maximum number of results to include in the response. If more results exist than the specified <code>MaxResults</code> value, a token is included in the response so that the remaining results can be retrieved.</p>", "location": "querystring", "locationName": "maxResults"}, "name": {"shape": "EntityName", "documentation": "<p>The name for the one of the listed applications.</p>", "location": "querystring", "locationName": "name"}, "nextToken": {"shape": "NextToken", "documentation": "<p><PERSON>ull, or the token from a previous call to get the next set of results.</p>", "location": "querystring", "locationName": "nextToken"}, "reverseOrder": {"shape": "BooleanOptional", "documentation": "<p>The application list is sorted based on the values of <code>lastAppComplianceEvaluationTime</code> field. By default, application list is sorted in ascending order. To sort the application list in descending order, set this field to <code>True</code>.</p>", "location": "querystring", "locationName": "reverseOrder"}, "toLastAssessmentTime": {"shape": "TimeStamp", "documentation": "<p>Upper limit of the range that is used to filter the applications based on their last assessment times.</p>", "location": "querystring", "locationName": "toLastAssessmentTime"}}}, "ListAppsResponse": {"type": "structure", "required": ["appSummaries"], "members": {"appSummaries": {"shape": "AppSummaryList", "documentation": "<p>Summaries for the Resilience Hub application.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>Token for the next set of results, or null if there are no more results.</p>"}}}, "ListMetricsRequest": {"type": "structure", "members": {"conditions": {"shape": "ConditionList", "documentation": "<p>Indicates the list of all the conditions that were applied on the metrics.</p>"}, "dataSource": {"shape": "String255", "documentation": "<p>Indicates the data source of the metrics.</p>"}, "fields": {"shape": "FieldList", "documentation": "<p>Indicates the list of fields in the data source.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>Maximum number of results to include in the response. If more results exist than the specified <code>MaxResults</code> value, a token is included in the response so that the remaining results can be retrieved.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p><PERSON>ull, or the token from a previous call to get the next set of results.</p>"}, "sorts": {"shape": "SortList", "documentation": "<p>(Optional) Indicates the order in which you want to sort the fields in the metrics. By default, the fields are sorted in the ascending order.</p>"}}}, "ListMetricsResponse": {"type": "structure", "required": ["rows"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>Token for the next set of results, or null if there are no more results.</p>"}, "rows": {"shape": "RowList", "documentation": "<p>Specifies all the list of metric values for each row of metrics.</p>"}}}, "ListRecommendationTemplatesRequest": {"type": "structure", "members": {"assessmentArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the assessment. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app-assessment/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>", "location": "querystring", "locationName": "assessmentArn"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>Maximum number of results to include in the response. If more results exist than the specified <code>MaxResults</code> value, a token is included in the response so that the remaining results can be retrieved.</p>", "location": "querystring", "locationName": "maxResults"}, "name": {"shape": "EntityName", "documentation": "<p>The name for one of the listed recommendation templates.</p>", "location": "querystring", "locationName": "name"}, "nextToken": {"shape": "NextToken", "documentation": "<p><PERSON>ull, or the token from a previous call to get the next set of results.</p>", "location": "querystring", "locationName": "nextToken"}, "recommendationTemplateArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) for a recommendation template.</p>", "location": "querystring", "locationName": "recommendationTemplateArn"}, "reverseOrder": {"shape": "BooleanOptional", "documentation": "<p>The default is to sort by ascending <b>startTime</b>. To sort by descending <b>startTime</b>, set reverseOrder to <code>true</code>.</p>", "location": "querystring", "locationName": "reverseOrder"}, "status": {"shape": "RecommendationTemplateStatusList", "documentation": "<p>Status of the action.</p>", "location": "querystring", "locationName": "status"}}}, "ListRecommendationTemplatesResponse": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>Token for the next set of results, or null if there are no more results.</p>"}, "recommendationTemplates": {"shape": "RecommendationTemplateList", "documentation": "<p>The recommendation templates for the Resilience Hub applications.</p>"}}}, "ListResiliencyPoliciesRequest": {"type": "structure", "members": {"maxResults": {"shape": "MaxResults", "documentation": "<p>Maximum number of results to include in the response. If more results exist than the specified <code>MaxResults</code> value, a token is included in the response so that the remaining results can be retrieved.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "NextToken", "documentation": "<p><PERSON>ull, or the token from a previous call to get the next set of results.</p>", "location": "querystring", "locationName": "nextToken"}, "policyName": {"shape": "EntityName", "documentation": "<p>Name of the resiliency policy.</p>", "location": "querystring", "locationName": "policyName"}}}, "ListResiliencyPoliciesResponse": {"type": "structure", "required": ["resiliencyPolicies"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>Token for the next set of results, or null if there are no more results.</p>"}, "resiliencyPolicies": {"shape": "ResiliencyPolicies", "documentation": "<p>The resiliency policies for the Resilience Hub applications.</p>"}}}, "ListResourceGroupingRecommendationsRequest": {"type": "structure", "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>", "location": "querystring", "locationName": "appArn"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>Maximum number of grouping recommendations to be displayed per Resilience Hub application.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "NextToken", "documentation": "<p><PERSON>ull, or the token from a previous call to get the next set of results.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListResourceGroupingRecommendationsResponse": {"type": "structure", "required": ["groupingRecommendations"], "members": {"groupingRecommendations": {"shape": "GroupingRecommendationList", "documentation": "<p>List of resource grouping recommendations generated by Resilience Hub.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p><PERSON>ull, or the token from a previous call to get the next set of results.</p>"}}}, "ListSopRecommendationsRequest": {"type": "structure", "required": ["assessmentArn"], "members": {"assessmentArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the assessment. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app-assessment/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>Maximum number of results to include in the response. If more results exist than the specified <code>MaxResults</code> value, a token is included in the response so that the remaining results can be retrieved.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p><PERSON>ull, or the token from a previous call to get the next set of results.</p>"}}}, "ListSopRecommendationsResponse": {"type": "structure", "required": ["sopRecommendations"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>Token for the next set of results, or null if there are no more results.</p>"}, "sopRecommendations": {"shape": "SopRecommendationList", "documentation": "<p>The standard operating procedure (SOP) recommendations for the Resilience Hub applications.</p>"}}}, "ListSuggestedResiliencyPoliciesRequest": {"type": "structure", "members": {"maxResults": {"shape": "MaxResults", "documentation": "<p>Maximum number of results to include in the response. If more results exist than the specified <code>MaxResults</code> value, a token is included in the response so that the remaining results can be retrieved.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "NextToken", "documentation": "<p><PERSON>ull, or the token from a previous call to get the next set of results.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListSuggestedResiliencyPoliciesResponse": {"type": "structure", "required": ["resiliencyPolicies"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>Token for the next set of results, or null if there are no more results.</p>"}, "resiliencyPolicies": {"shape": "ResiliencyPolicies", "documentation": "<p>The suggested resiliency policies for the Resilience Hub applications.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) for a specific resource in your Resilience Hub application.</p>", "location": "uri", "locationName": "resourceArn"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"tags": {"shape": "TagMap", "documentation": "<p>Tags assigned to the resource. A tag is a label that you assign to an Amazon Web Services resource. Each tag consists of a key/value pair.</p>"}}}, "ListTestRecommendationsRequest": {"type": "structure", "required": ["assessmentArn"], "members": {"assessmentArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the assessment. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app-assessment/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>Maximum number of results to include in the response. If more results exist than the specified <code>MaxResults</code> value, a token is included in the response so that the remaining results can be retrieved.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p><PERSON>ull, or the token from a previous call to get the next set of results.</p>"}}}, "ListTestRecommendationsResponse": {"type": "structure", "required": ["testRecommendations"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>Token for the next set of results, or null if there are no more results.</p>"}, "testRecommendations": {"shape": "TestRecommendationList", "documentation": "<p>The test recommendations for the Resilience Hub application.</p>"}}}, "ListUnsupportedAppVersionResourcesRequest": {"type": "structure", "required": ["appArn", "appVersion"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "appVersion": {"shape": "EntityVersion", "documentation": "<p>The version of the application.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>Maximum number of results to include in the response. If more results exist than the specified <code>MaxResults</code> value, a token is included in the response so that the remaining results can be retrieved.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p><PERSON>ull, or the token from a previous call to get the next set of results.</p>"}, "resolutionId": {"shape": "String255", "documentation": "<p>The identifier for a specific resolution.</p>"}}}, "ListUnsupportedAppVersionResourcesResponse": {"type": "structure", "required": ["resolutionId", "unsupportedResources"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>Token for the next set of results, or null if there are no more results.</p>"}, "resolutionId": {"shape": "String255", "documentation": "<p>The identifier for a specific resolution.</p>"}, "unsupportedResources": {"shape": "UnsupportedResourceList", "documentation": "<p>The unsupported resources for the application.</p>"}}}, "LogicalResourceId": {"type": "structure", "required": ["identifier"], "members": {"eksSourceName": {"shape": "String255", "documentation": "<p>Name of the Amazon Elastic Kubernetes Service cluster and namespace this resource belongs to.</p> <note> <p>This parameter accepts values in \"eks-cluster/namespace\" format.</p> </note>"}, "identifier": {"shape": "String255", "documentation": "<p>Identifier of the resource.</p>"}, "logicalStackName": {"shape": "String255", "documentation": "<p>The name of the CloudFormation stack this resource belongs to.</p>"}, "resourceGroupName": {"shape": "EntityName", "documentation": "<p>The name of the resource group that this resource belongs to.</p>"}, "terraformSourceName": {"shape": "String255", "documentation": "<p> The name of the Terraform S3 state file this resource belongs to. </p>"}}, "documentation": "<p>Defines a logical resource identifier.</p>"}, "Long": {"type": "long"}, "LongOptional": {"type": "long", "box": true}, "MaxResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "MetricsExportStatusType": {"type": "string", "enum": ["Pending", "InProgress", "Failed", "Success"]}, "NextToken": {"type": "string", "pattern": "^\\S{1,2000}$"}, "PermissionModel": {"type": "structure", "required": ["type"], "members": {"crossAccountRoleArns": {"shape": "IamRoleArnList", "documentation": "<p>Defines a list of role Amazon Resource Names (ARNs) to be used in other accounts. These ARNs are used for querying purposes while importing resources and assessing your application.</p> <note> <ul> <li> <p>These ARNs are required only when your resources are in other accounts and you have different role name in these accounts. Else, the invoker role name will be used in the other accounts.</p> </li> <li> <p>These roles must have a trust policy with <code>iam:AssumeRole</code> permission to the invoker role in the primary account.</p> </li> </ul> </note>"}, "invokerRoleName": {"shape": "IamRoleName", "documentation": "<p>Existing Amazon Web Services IAM role name in the primary Amazon Web Services account that will be assumed by Resilience Hub Service Principle to obtain a read-only access to your application resources while running an assessment. </p> <p>If your IAM role includes a path, you must include the path in the <code>invokerRoleName</code> parameter. For example, if your IAM role's ARN is <code>arn:aws:iam:************:role/my-path/role-name</code>, you should pass <code>my-path/role-name</code>. </p> <note> <ul> <li> <p>You must have <code>iam:passRole</code> permission for this role while creating or updating the application.</p> </li> <li> <p>Currently, <code>invokerRoleName</code> accepts only <code>[A-Za-z0-9_+=,.@-]</code> characters.</p> </li> </ul> </note>"}, "type": {"shape": "PermissionModelType", "documentation": "<p>Defines how Resilience Hub scans your resources. It can scan for the resources by using a pre-existing role in your Amazon Web Services account, or by using the credentials of the current IAM user.</p>"}}, "documentation": "<p>Defines the roles and credentials that Resilience Hub would use while creating the application, importing its resources, and running an assessment.</p>"}, "PermissionModelType": {"type": "string", "enum": ["LegacyIAMUser", "RoleBased"]}, "PhysicalIdentifierType": {"type": "string", "enum": ["<PERSON><PERSON>", "Native"]}, "PhysicalResource": {"type": "structure", "required": ["logicalResourceId", "physicalResourceId", "resourceType"], "members": {"additionalInfo": {"shape": "AdditionalInfoMap", "documentation": "<p>Additional configuration parameters for an Resilience Hub application. If you want to implement <code>additionalInfo</code> through the Resilience Hub console rather than using an API call, see <a href=\"https://docs.aws.amazon.com/resilience-hub/latest/userguide/app-config-param.html\">Configure the application configuration parameters</a>.</p> <note> <p>Currently, this parameter accepts a key-value mapping (in a string format) of only one failover region and one associated account.</p> <p>Key: <code>\"failover-regions\"</code> </p> <p>Value: <code>\"[{\"region\":\"&lt;REGION&gt;\", \"accounts\":[{\"id\":\"&lt;ACCOUNT_ID&gt;\"}]}]\"</code> </p> </note>"}, "appComponents": {"shape": "AppComponentList", "documentation": "<p>The application components that belong to this resource.</p>"}, "excluded": {"shape": "BooleanOptional", "documentation": "<p>Indicates if a resource is included or excluded from the assessment.</p>"}, "logicalResourceId": {"shape": "LogicalResourceId", "documentation": "<p>Logical identifier of the resource.</p>"}, "parentResourceName": {"shape": "EntityName", "documentation": "<p>Name of the parent resource.</p>"}, "physicalResourceId": {"shape": "PhysicalResourceId", "documentation": "<p>Identifier of the physical resource.</p>"}, "resourceName": {"shape": "EntityName", "documentation": "<p>The name of the resource.</p>"}, "resourceType": {"shape": "String255", "documentation": "<p>Type of resource.</p>"}, "sourceType": {"shape": "ResourceSourceType", "documentation": "<p>Type of input source.</p>"}}, "documentation": "<p>Defines a physical resource. A physical resource is a resource that exists in your account. It can be identified using an Amazon Resource Name (ARN) or an Resilience Hub-native identifier. </p>"}, "PhysicalResourceId": {"type": "structure", "required": ["identifier", "type"], "members": {"awsAccountId": {"shape": "CustomerId", "documentation": "<p>The Amazon Web Services account that owns the physical resource.</p>"}, "awsRegion": {"shape": "AwsRegion", "documentation": "<p>The Amazon Web Services Region that the physical resource is located in.</p>"}, "identifier": {"shape": "String255", "documentation": "<p>Identifier of the physical resource.</p>"}, "type": {"shape": "PhysicalIdentifierType", "documentation": "<p>Specifies the type of physical resource identifier.</p> <dl> <dt>Arn</dt> <dd> <p>The resource identifier is an Amazon Resource Name (ARN) and it can identify the following list of resources:</p> <ul> <li> <p> <code>AWS::ECS::Service</code> </p> </li> <li> <p> <code>AWS::EFS::FileSystem</code> </p> </li> <li> <p> <code>AWS::ElasticLoadBalancingV2::LoadBalancer</code> </p> </li> <li> <p> <code>AWS::Lambda::Function</code> </p> </li> <li> <p> <code>AWS::SNS::Topic</code> </p> </li> </ul> </dd> <dt>Native</dt> <dd> <p>The resource identifier is an Resilience Hub-native identifier and it can identify the following list of resources:</p> <ul> <li> <p> <code>AWS::ApiGateway::RestApi</code> </p> </li> <li> <p> <code>AWS::ApiGatewayV2::Api</code> </p> </li> <li> <p> <code>AWS::AutoScaling::AutoScalingGroup</code> </p> </li> <li> <p> <code>AWS::DocDB::DBCluster</code> </p> </li> <li> <p> <code>AWS::DocDB::DBGlobalCluster</code> </p> </li> <li> <p> <code>AWS::DocDB::DBInstance</code> </p> </li> <li> <p> <code>AWS::DynamoDB::GlobalTable</code> </p> </li> <li> <p> <code>AWS::DynamoDB::Table</code> </p> </li> <li> <p> <code>AWS::EC2::EC2Fleet</code> </p> </li> <li> <p> <code>AWS::EC2::Instance</code> </p> </li> <li> <p> <code>AWS::EC2::NatGateway</code> </p> </li> <li> <p> <code>AWS::EC2::Volume</code> </p> </li> <li> <p> <code>AWS::ElasticLoadBalancing::LoadBalancer</code> </p> </li> <li> <p> <code>AWS::RDS::DBCluster</code> </p> </li> <li> <p> <code>AWS::RDS::DBInstance</code> </p> </li> <li> <p> <code>AWS::RDS::GlobalCluster</code> </p> </li> <li> <p> <code>AWS::Route53::RecordSet</code> </p> </li> <li> <p> <code>AWS::S3::Bucket</code> </p> </li> <li> <p> <code>AWS::SQS::Queue</code> </p> </li> </ul> </dd> </dl>"}}, "documentation": "<p>Defines a physical resource identifier.</p>"}, "PhysicalResourceList": {"type": "list", "member": {"shape": "PhysicalResource"}}, "PublishAppVersionRequest": {"type": "structure", "required": ["appArn"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "versionName": {"shape": "EntityVersion", "documentation": "<p>Name of the application version.</p>"}}}, "PublishAppVersionResponse": {"type": "structure", "required": ["appArn"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "appVersion": {"shape": "EntityVersion", "documentation": "<p>The version of the application.</p>"}, "identifier": {"shape": "LongOptional", "documentation": "<p>Identifier of the application version.</p>"}, "versionName": {"shape": "EntityVersion", "documentation": "<p>Name of the application version.</p>"}}}, "PutDraftAppVersionTemplateRequest": {"type": "structure", "required": ["appArn", "appTemplateBody"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "appTemplateBody": {"shape": "AppTemplateBody", "documentation": "<p>A JSON string that provides information about your application structure. To learn more about the <code>appTemplateBody</code> template, see the sample template provided in the <i>Examples</i> section.</p> <p>The <code>appTemplateBody</code> JSON string has the following structure:</p> <ul> <li> <p> <b> <code>resources</code> </b> </p> <p>The list of logical resources that must be included in the Resilience Hub application.</p> <p>Type: Array</p> <note> <p>Don't add the resources that you want to exclude.</p> </note> <p>Each <code>resources</code> array item includes the following fields:</p> <ul> <li> <p> <i> <code>logicalResourceId</code> </i> </p> <p>Logical identifier of the resource.</p> <p>Type: Object</p> <p>Each <code>logicalResourceId</code> object includes the following fields:</p> <ul> <li> <p> <code>identifier</code> </p> <p>Identifier of the resource.</p> <p>Type: String</p> </li> <li> <p> <code>logicalStackName</code> </p> <p>The name of the CloudFormation stack this resource belongs to.</p> <p>Type: String</p> </li> <li> <p> <code>resourceGroupName</code> </p> <p>The name of the resource group this resource belongs to.</p> <p>Type: String</p> </li> <li> <p> <code>terraformSourceName</code> </p> <p>The name of the Terraform S3 state file this resource belongs to.</p> <p>Type: String</p> </li> <li> <p> <code>eksSourceName</code> </p> <p>Name of the Amazon Elastic Kubernetes Service cluster and namespace this resource belongs to.</p> <note> <p>This parameter accepts values in \"eks-cluster/namespace\" format.</p> </note> <p>Type: String</p> </li> </ul> </li> <li> <p> <i> <code>type</code> </i> </p> <p>The type of resource.</p> <p>Type: string</p> </li> <li> <p> <i> <code>name</code> </i> </p> <p>The name of the resource.</p> <p>Type: String</p> </li> <li> <p> <code>additionalInfo</code> </p> <p>Additional configuration parameters for an Resilience Hub application. If you want to implement <code>additionalInfo</code> through the Resilience Hub console rather than using an API call, see <a href=\"https://docs.aws.amazon.com/resilience-hub/latest/userguide/app-config-param.html\">Configure the application configuration parameters</a>.</p> <note> <p>Currently, this parameter accepts a key-value mapping (in a string format) of only one failover region and one associated account.</p> <p>Key: <code>\"failover-regions\"</code> </p> <p>Value: <code>\"[{\"region\":\"&lt;REGION&gt;\", \"accounts\":[{\"id\":\"&lt;ACCOUNT_ID&gt;\"}]}]\"</code> </p> </note> </li> </ul> </li> <li> <p> <b> <code>appComponents</code> </b> </p> <p>List of Application Components that this resource belongs to. If an Application Component is not part of the Resilience Hub application, it will be added.</p> <p>Type: Array</p> <p>Each <code>appComponents</code> array item includes the following fields:</p> <ul> <li> <p> <code>name</code> </p> <p>Name of the Application Component.</p> <p>Type: String</p> </li> <li> <p> <code>type</code> </p> <p>Type of Application Component. For more information about the types of Application Component, see <a href=\"https://docs.aws.amazon.com/resilience-hub/latest/userguide/AppComponent.grouping.html\">Grouping resources in an AppComponent</a>.</p> <p>Type: String</p> </li> <li> <p> <code>resourceNames</code> </p> <p>The list of included resources that are assigned to the Application Component.</p> <p>Type: Array of strings</p> </li> <li> <p> <code>additionalInfo</code> </p> <p>Additional configuration parameters for an Resilience Hub application. If you want to implement <code>additionalInfo</code> through the Resilience Hub console rather than using an API call, see <a href=\"https://docs.aws.amazon.com/resilience-hub/latest/userguide/app-config-param.html\">Configure the application configuration parameters</a>.</p> <note> <p>Currently, this parameter accepts a key-value mapping (in a string format) of only one failover region and one associated account.</p> <p>Key: <code>\"failover-regions\"</code> </p> <p>Value: <code>\"[{\"region\":\"&lt;REGION&gt;\", \"accounts\":[{\"id\":\"&lt;ACCOUNT_ID&gt;\"}]}]\"</code> </p> </note> </li> </ul> </li> <li> <p> <b> <code>excludedResources</code> </b> </p> <p>The list of logical resource identifiers to be excluded from the application.</p> <p>Type: Array</p> <note> <p>Don't add the resources that you want to include.</p> </note> <p>Each <code>excludedResources</code> array item includes the following fields:</p> <ul> <li> <p> <i> <code>logicalResourceIds</code> </i> </p> <p>Logical identifier of the resource.</p> <p>Type: Object</p> <note> <p>You can configure only one of the following fields:</p> <ul> <li> <p> <code>logicalStackName</code> </p> </li> <li> <p> <code>resourceGroupName</code> </p> </li> <li> <p> <code>terraformSourceName</code> </p> </li> <li> <p> <code>eksSourceName</code> </p> </li> </ul> </note> <p>Each <code>logicalResourceIds</code> object includes the following fields:</p> <ul> <li> <p> <code>identifier</code> </p> <p>Identifier of the resource.</p> <p>Type: String</p> </li> <li> <p> <code>logicalStackName</code> </p> <p>The name of the CloudFormation stack this resource belongs to.</p> <p>Type: String</p> </li> <li> <p> <code>resourceGroupName</code> </p> <p>The name of the resource group this resource belongs to.</p> <p>Type: String</p> </li> <li> <p> <code>terraformSourceName</code> </p> <p>The name of the Terraform S3 state file this resource belongs to.</p> <p>Type: String</p> </li> <li> <p> <code>eksSourceName</code> </p> <p>Name of the Amazon Elastic Kubernetes Service cluster and namespace this resource belongs to.</p> <note> <p>This parameter accepts values in \"eks-cluster/namespace\" format.</p> </note> <p>Type: String</p> </li> </ul> </li> </ul> </li> <li> <p> <b> <code>version</code> </b> </p> <p>Resilience Hub application version.</p> </li> <li> <p> <code>additionalInfo</code> </p> <p>Additional configuration parameters for an Resilience Hub application. If you want to implement <code>additionalInfo</code> through the Resilience Hub console rather than using an API call, see <a href=\"https://docs.aws.amazon.com/resilience-hub/latest/userguide/app-config-param.html\">Configure the application configuration parameters</a>.</p> <note> <p>Currently, this parameter accepts a key-value mapping (in a string format) of only one failover region and one associated account.</p> <p>Key: <code>\"failover-regions\"</code> </p> <p>Value: <code>\"[{\"region\":\"&lt;REGION&gt;\", \"accounts\":[{\"id\":\"&lt;ACCOUNT_ID&gt;\"}]}]\"</code> </p> </note> </li> </ul>"}}}, "PutDraftAppVersionTemplateResponse": {"type": "structure", "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "appVersion": {"shape": "EntityVersion", "documentation": "<p>The version of the application.</p>"}}}, "RecommendationCompliance": {"type": "map", "key": {"shape": "DisruptionType"}, "value": {"shape": "RecommendationDisruptionCompliance"}}, "RecommendationComplianceStatus": {"type": "string", "enum": ["BreachedUnattainable", "BreachedCanMeet", "MetCanImprove", "MissingPolicy"]}, "RecommendationDisruptionCompliance": {"type": "structure", "required": ["expectedComplianceStatus"], "members": {"expectedComplianceStatus": {"shape": "ComplianceStatus", "documentation": "<p>The expected compliance status after applying the recommended configuration change.</p>"}, "expectedRpoDescription": {"shape": "String500", "documentation": "<p>The expected Recovery Point Objective (RPO) description after applying the recommended configuration change.</p>"}, "expectedRpoInSecs": {"shape": "Seconds", "documentation": "<p>The expected RPO after applying the recommended configuration change.</p>"}, "expectedRtoDescription": {"shape": "String500", "documentation": "<p>The expected Recovery Time Objective (RTO) description after applying the recommended configuration change.</p>"}, "expectedRtoInSecs": {"shape": "Seconds", "documentation": "<p>The expected RTO after applying the recommended configuration change.</p>"}}, "documentation": "<p>Defines a disruption compliance recommendation.</p>"}, "RecommendationIdList": {"type": "list", "member": {"shape": "<PERSON><PERSON>"}, "max": 200, "min": 1}, "RecommendationItem": {"type": "structure", "members": {"alreadyImplemented": {"shape": "BooleanOptional", "documentation": "<p>Specifies if the recommendation has already been implemented.</p>"}, "discoveredAlarm": {"shape": "Alarm", "documentation": "<p>Indicates the previously implemented Amazon CloudWatch alarm discovered by Resilience Hub.</p>"}, "excludeReason": {"shape": "ExcludeRecommendationReason", "documentation": "<p>Indicates the reason for excluding an operational recommendation.</p>"}, "excluded": {"shape": "BooleanOptional", "documentation": "<p>Indicates if an operational recommendation item is excluded.</p>"}, "latestDiscoveredExperiment": {"shape": "Experiment", "documentation": "<p>Indicates the experiment created in FIS that was discovered by Resilience Hub, which matches the recommendation.</p>"}, "resourceId": {"shape": "String500", "documentation": "<p>Identifier of the resource.</p>"}, "targetAccountId": {"shape": "CustomerId", "documentation": "<p>Identifier of the target account.</p>"}, "targetRegion": {"shape": "AwsRegion", "documentation": "<p>The target region.</p>"}}, "documentation": "<p>Defines a recommendation.</p>"}, "RecommendationItemList": {"type": "list", "member": {"shape": "RecommendationItem"}}, "RecommendationStatus": {"type": "string", "enum": ["Implemented", "Inactive", "NotImplemented", "Excluded"]}, "RecommendationTemplate": {"type": "structure", "required": ["assessmentArn", "format", "name", "recommendationTemplateArn", "recommendationTypes", "status"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "assessmentArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the assessment. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app-assessment/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "endTime": {"shape": "TimeStamp", "documentation": "<p>The end time for the action.</p>"}, "format": {"shape": "TemplateFormat", "documentation": "<p>Format of the recommendation template.</p> <dl> <dt><PERSON>fn<PERSON>son</dt> <dd> <p>The template is CloudFormation JSON.</p> </dd> <dt>CfnYaml</dt> <dd> <p>The template is CloudFormation YAML.</p> </dd> </dl>"}, "message": {"shape": "String500", "documentation": "<p>Message for the recommendation template.</p>"}, "name": {"shape": "EntityName", "documentation": "<p>Name for the recommendation template.</p>"}, "needsReplacements": {"shape": "BooleanOptional", "documentation": "<p>Indicates if replacements are needed.</p>"}, "recommendationIds": {"shape": "RecommendationIdList", "documentation": "<p>Identifiers for the recommendations used in the recommendation template.</p>"}, "recommendationTemplateArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) for the recommendation template.</p>"}, "recommendationTypes": {"shape": "RenderRecommendationTypeList", "documentation": "<p>An array of strings that specify the recommendation template type or types.</p> <dl> <dt>Alarm</dt> <dd> <p>The template is an <a>AlarmRecommendation</a> template.</p> </dd> <dt>Sop</dt> <dd> <p>The template is a <a>SopRecommendation</a> template.</p> </dd> <dt>Test</dt> <dd> <p>The template is a <a>TestRecommendation</a> template.</p> </dd> </dl>"}, "startTime": {"shape": "TimeStamp", "documentation": "<p>The start time for the action.</p>"}, "status": {"shape": "RecommendationTemplateStatus", "documentation": "<p>Status of the action.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>Tags assigned to the resource. A tag is a label that you assign to an Amazon Web Services resource. Each tag consists of a key/value pair.</p>"}, "templatesLocation": {"shape": "S3Location", "documentation": "<p>The file location of the template.</p>"}}, "documentation": "<p>Defines a recommendation template created with the <a>CreateRecommendationTemplate</a> action.</p>"}, "RecommendationTemplateList": {"type": "list", "member": {"shape": "RecommendationTemplate"}}, "RecommendationTemplateStatus": {"type": "string", "enum": ["Pending", "InProgress", "Failed", "Success"]}, "RecommendationTemplateStatusList": {"type": "list", "member": {"shape": "RecommendationTemplateStatus"}, "max": 4, "min": 1}, "RejectGroupingRecommendationEntries": {"type": "list", "member": {"shape": "RejectGroupingRecommendationEntry"}, "max": 30, "min": 1}, "RejectGroupingRecommendationEntry": {"type": "structure", "required": ["groupingRecommendationId"], "members": {"groupingRecommendationId": {"shape": "String255", "documentation": "<p>Indicates the identifier of the grouping recommendation.</p>"}, "rejectionReason": {"shape": "GroupingRecommendationRejectionReason", "documentation": "<p>Indicates the reason you had selected while rejecting a grouping recommendation.</p>"}}, "documentation": "<p>Indicates the rejected grouping recommendation.</p>"}, "RejectResourceGroupingRecommendationsRequest": {"type": "structure", "required": ["appArn", "entries"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "entries": {"shape": "RejectGroupingRecommendationEntries", "documentation": "<p>List of resource grouping recommendations you have selected to exclude from your application.</p>"}}}, "RejectResourceGroupingRecommendationsResponse": {"type": "structure", "required": ["appArn", "failedEntries"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "failedEntries": {"shape": "FailedGroupingRecommendationEntries", "documentation": "<p>List of resource grouping recommendations that failed to get excluded in your application.</p>"}}}, "RemoveDraftAppVersionResourceMappingsRequest": {"type": "structure", "required": ["appArn"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "appRegistryAppNames": {"shape": "EntityNameList", "documentation": "<p>The names of the registered applications you want to remove from the resource mappings.</p>"}, "eksSourceNames": {"shape": "String255List", "documentation": "<p>The names of the Amazon Elastic Kubernetes Service clusters and namespaces you want to remove from the resource mappings.</p> <note> <p>This parameter accepts values in \"eks-cluster/namespace\" format.</p> </note>"}, "logicalStackNames": {"shape": "String255List", "documentation": "<p>The names of the CloudFormation stacks you want to remove from the resource mappings.</p>"}, "resourceGroupNames": {"shape": "EntityNameList", "documentation": "<p>The names of the resource groups you want to remove from the resource mappings.</p>"}, "resourceNames": {"shape": "EntityNameList", "documentation": "<p>The names of the resources you want to remove from the resource mappings.</p>"}, "terraformSourceNames": {"shape": "String255List", "documentation": "<p>The names of the Terraform sources you want to remove from the resource mappings.</p>"}}}, "RemoveDraftAppVersionResourceMappingsResponse": {"type": "structure", "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "appVersion": {"shape": "EntityVersion", "documentation": "<p>The version of the application.</p>"}}}, "RenderRecommendationType": {"type": "string", "enum": ["Alarm", "Sop", "Test"]}, "RenderRecommendationTypeList": {"type": "list", "member": {"shape": "RenderRecommendationType"}, "max": 4, "min": 1}, "ResiliencyPolicies": {"type": "list", "member": {"shape": "ResiliencyPolicy"}}, "ResiliencyPolicy": {"type": "structure", "members": {"creationTime": {"shape": "TimeStamp", "documentation": "<p>Date and time when the resiliency policy was created.</p>"}, "dataLocationConstraint": {"shape": "DataLocationConstraint", "documentation": "<p>Specifies a high-level geographical location constraint for where your resilience policy data can be stored.</p>"}, "estimatedCostTier": {"shape": "EstimatedCostTier", "documentation": "<p>Specifies the estimated cost tier of the resiliency policy.</p>"}, "policy": {"shape": "DisruptionPolicy", "documentation": "<p>The resiliency policy.</p>"}, "policyArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the resiliency policy. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:resiliency-policy/<code>policy-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "policyDescription": {"shape": "EntityDescription", "documentation": "<p>Description of the resiliency policy.</p>"}, "policyName": {"shape": "EntityName", "documentation": "<p>The name of the policy</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>Tags assigned to the resource. A tag is a label that you assign to an Amazon Web Services resource. Each tag consists of a key/value pair.</p>"}, "tier": {"shape": "ResiliencyPolicyTier", "documentation": "<p>The tier for this resiliency policy, ranging from the highest severity (<code>MissionCritical</code>) to lowest (<code>NonCritical</code>).</p>"}}, "documentation": "<p>Defines a resiliency policy.</p> <note> <p>Resilience Hub allows you to provide a value of zero for <code>rtoInSecs</code> and <code>rpoInSecs</code> of your resiliency policy. But, while assessing your application, the lowest possible assessment result is near zero. Hence, if you provide value zero for <code>rtoInSecs</code> and <code>rpoInSecs</code>, the estimated workload RTO and estimated workload RPO result will be near zero and the <b>Compliance status</b> for your application will be set to <b>Policy breached</b>.</p> </note>"}, "ResiliencyPolicyTier": {"type": "string", "enum": ["MissionCritical", "Critical", "Important", "CoreServices", "NonCritical", "NotApplicable"]}, "ResiliencyScore": {"type": "structure", "required": ["disruptionScore", "score"], "members": {"componentScore": {"shape": "ScoringComponentResiliencyScores", "documentation": "<p>The score generated by Resilience Hub for the scoring component after running an assessment.</p> <p>For example, if the <code>score</code> is 25 points, it indicates the overall score of your application generated by Resilience Hub after running an assessment.</p>"}, "disruptionScore": {"shape": "DisruptionResiliencyScore", "documentation": "<p>The disruption score for a valid key.</p>"}, "score": {"shape": "Double", "documentation": "<p>The outage score for a valid key.</p>"}}, "documentation": "<p>The overall resiliency score, returned as an object that includes the disruption score and outage score.</p>"}, "ResiliencyScoreType": {"type": "string", "enum": ["Compliance", "Test", "Alarm", "Sop"]}, "ResolveAppVersionResourcesRequest": {"type": "structure", "required": ["appArn", "appVersion"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "appVersion": {"shape": "EntityVersion", "documentation": "<p>The version of the application.</p>"}}}, "ResolveAppVersionResourcesResponse": {"type": "structure", "required": ["appArn", "appVersion", "resolutionId", "status"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "appVersion": {"shape": "EntityVersion", "documentation": "<p>The version of the application.</p>"}, "resolutionId": {"shape": "String255", "documentation": "<p>The identifier for a specific resolution.</p>"}, "status": {"shape": "ResourceResolutionStatusType", "documentation": "<p>Status of the action.</p>"}}}, "ResourceDrift": {"type": "structure", "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the application whose resources have drifted. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app-assessment/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "appVersion": {"shape": "EntityVersion", "documentation": "<p>Version of the application whose resources have drifted.</p>"}, "diffType": {"shape": "DifferenceType", "documentation": "<p>Indicates if the resource was added or removed.</p>"}, "referenceId": {"shape": "EntityId", "documentation": "<p>Reference identifier of the resource drift.</p>"}, "resourceIdentifier": {"shape": "ResourceIdentifier", "documentation": "<p>Identifier of the drifted resource.</p>"}}, "documentation": "<p>Indicates the resources that have drifted in the current application version.</p>"}, "ResourceDriftList": {"type": "list", "member": {"shape": "ResourceDrift"}}, "ResourceError": {"type": "structure", "members": {"logicalResourceId": {"shape": "String255", "documentation": "<p>Identifier of the logical resource. </p>"}, "physicalResourceId": {"shape": "String255", "documentation": "<p>Identifier of the physical resource. </p>"}, "reason": {"shape": "ErrorMessage", "documentation": "<p> This is the error message. </p>"}}, "documentation": "<p> Defines application resource errors. </p>"}, "ResourceErrorList": {"type": "list", "member": {"shape": "ResourceError"}}, "ResourceErrorsDetails": {"type": "structure", "members": {"hasMoreErrors": {"shape": "BooleanOptional", "documentation": "<p> This indicates if there are more errors not listed in the <code>resourceErrors</code> list. </p>"}, "resourceErrors": {"shape": "ResourceErrorList", "documentation": "<p> A list of errors retrieving an application's resources. </p>"}}, "documentation": "<p> A list of errors retrieving an application's resources. </p>"}, "ResourceId": {"type": "string", "pattern": ".*"}, "ResourceIdentifier": {"type": "structure", "members": {"logicalResourceId": {"shape": "LogicalResourceId", "documentation": "<p>Logical identifier of the drifted resource.</p>"}, "resourceType": {"shape": "String255", "documentation": "<p>Type of the drifted resource.</p>"}}, "documentation": "<p>Defines a resource identifier for the drifted resource.</p>"}, "ResourceImportStatusType": {"type": "string", "enum": ["Pending", "InProgress", "Failed", "Success"]}, "ResourceImportStrategyType": {"type": "string", "enum": ["AddOnly", "ReplaceAll"]}, "ResourceMapping": {"type": "structure", "required": ["mappingType", "physicalResourceId"], "members": {"appRegistryAppName": {"shape": "EntityName", "documentation": "<p>Name of the application this resource is mapped to when the <code>mappingType</code> is <code>AppRegistryApp</code>.</p>"}, "eksSourceName": {"shape": "String255", "documentation": "<p>Name of the Amazon Elastic Kubernetes Service cluster and namespace that this resource is mapped to when the <code>mappingType</code> is <code>EKS</code>.</p> <note> <p>This parameter accepts values in \"eks-cluster/namespace\" format.</p> </note>"}, "logicalStackName": {"shape": "String255", "documentation": "<p>Name of the CloudFormation stack this resource is mapped to when the <code>mappingType</code> is <code>CfnStack</code>.</p>"}, "mappingType": {"shape": "ResourceMappingType", "documentation": "<p>Specifies the type of resource mapping.</p>"}, "physicalResourceId": {"shape": "PhysicalResourceId", "documentation": "<p>Identifier of the physical resource.</p>"}, "resourceGroupName": {"shape": "EntityName", "documentation": "<p>Name of the Resource Groups that this resource is mapped to when the <code>mappingType</code> is <code>ResourceGroup</code>.</p>"}, "resourceName": {"shape": "EntityName", "documentation": "<p>Name of the resource that this resource is mapped to when the <code>mappingType</code> is <code>Resource</code>.</p>"}, "terraformSourceName": {"shape": "String255", "documentation": "<p>Name of the Terraform source that this resource is mapped to when the <code>mappingType</code> is <code>Terraform</code>.</p>"}}, "documentation": "<p>Defines a resource mapping.</p>"}, "ResourceMappingList": {"type": "list", "member": {"shape": "ResourceMapping"}}, "ResourceMappingType": {"type": "string", "enum": ["CfnStack", "Resource", "AppRegistryApp", "ResourceGroup", "Terraform", "EKS"]}, "ResourceNotFoundException": {"type": "structure", "members": {"message": {"shape": "String500"}, "resourceId": {"shape": "ResourceId", "documentation": "<p>The identifier of the resource that the exception applies to.</p>"}, "resourceType": {"shape": "ResourceType", "documentation": "<p>The type of the resource that the exception applies to.</p>"}}, "documentation": "<p>This exception occurs when the specified resource could not be found.</p>", "error": {"httpStatusCode": 404, "senderFault": true}, "exception": true}, "ResourceResolutionStatusType": {"type": "string", "enum": ["Pending", "InProgress", "Failed", "Success"]}, "ResourceSourceType": {"type": "string", "enum": ["AppTemplate", "Discovered"]}, "ResourceType": {"type": "string", "pattern": ".*"}, "ResourcesGroupingRecGenStatusType": {"type": "string", "enum": ["Pending", "InProgress", "Failed", "Success"]}, "RetryAfterSeconds": {"type": "integer", "box": true}, "Row": {"type": "list", "member": {"shape": "String255"}}, "RowList": {"type": "list", "member": {"shape": "Row"}}, "S3Location": {"type": "structure", "members": {"bucket": {"shape": "String500", "documentation": "<p>The name of the Amazon S3 bucket.</p>"}, "prefix": {"shape": "String500", "documentation": "<p>The prefix for the Amazon S3 bucket.</p>"}}, "documentation": "<p>The location of the Amazon S3 bucket.</p>"}, "S3Url": {"type": "string", "max": 2000, "min": 0, "pattern": "^((https://([^/]+)\\.s3((-|\\.)[^/]+)?\\.amazonaws\\.com(.cn)?)|(s3://([^/]+)))/\\S{1,2000}$"}, "ScoringComponentResiliencyScore": {"type": "structure", "members": {"excludedCount": {"shape": "<PERSON>", "documentation": "<p>Number of recommendations that were excluded from the assessment.</p> <p>For example, if the <code>excludedCount</code> for Alarms coverage scoring component is 7, it indicates that 7 Amazon CloudWatch alarms are excluded from the assessment.</p>"}, "outstandingCount": {"shape": "<PERSON>", "documentation": "<p>Number of recommendations that must be implemented to obtain the maximum possible score for the scoring component. For SOPs, alarms, and tests, these are the number of recommendations that must be implemented. For compliance, these are the number of Application Components that have breached the resiliency policy.</p> <p>For example, if the <code>outstandingCount</code> for Alarms coverage scoring component is 5, it indicates that 5 Amazon CloudWatch alarms need to be implemented to achieve the maximum possible score.</p>"}, "possibleScore": {"shape": "Double", "documentation": "<p>Maximum possible score that can be obtained for the scoring component. </p> <p>For example, if the <code>possibleScore</code> is 20 points, it indicates the maximum possible score you can achieve for the scoring component when you run a new assessment after implementing all the Resilience Hub recommendations.</p>"}, "score": {"shape": "Double", "documentation": "<p>Resiliency score points given for the scoring component. The score is always less than or equal to the <code>possibleScore</code>.</p>"}}, "documentation": "<p>Resiliency score of each scoring component. For more information about scoring component, see <a href=\"https://docs.aws.amazon.com/resilience-hub/latest/userguide/calculate-score.html\">Calculating resiliency score</a>.</p>"}, "ScoringComponentResiliencyScores": {"type": "map", "key": {"shape": "ResiliencyScoreType"}, "value": {"shape": "ScoringComponentResiliencyScore"}}, "Seconds": {"type": "integer", "min": 0}, "ServiceQuotaExceededException": {"type": "structure", "members": {"message": {"shape": "String500"}}, "documentation": "<p>This exception occurs when you have exceeded your service quota. To perform the requested action, remove some of the relevant resources, or use Service Quotas to request a service quota increase.</p>", "error": {"httpStatusCode": 402, "senderFault": true}, "exception": true}, "SopRecommendation": {"type": "structure", "required": ["recommendationId", "referenceId", "serviceType"], "members": {"appComponentName": {"shape": "EntityId", "documentation": "<p>Name of the Application Component.</p>"}, "description": {"shape": "String500", "documentation": "<p>Description of the SOP recommendation.</p>"}, "items": {"shape": "RecommendationItemList", "documentation": "<p>The recommendation items.</p>"}, "name": {"shape": "DocumentName", "documentation": "<p>Name of the SOP recommendation.</p>"}, "prerequisite": {"shape": "String500", "documentation": "<p>Prerequisite for the SOP recommendation.</p>"}, "recommendationId": {"shape": "<PERSON><PERSON>", "documentation": "<p>Identifier for the SOP recommendation.</p>"}, "recommendationStatus": {"shape": "RecommendationStatus", "documentation": "<p>Status of the recommended standard operating procedure.</p>"}, "referenceId": {"shape": "SpecReferenceId", "documentation": "<p>Reference identifier for the SOP recommendation.</p>"}, "serviceType": {"shape": "SopServiceType", "documentation": "<p>The service type.</p>"}}, "documentation": "<p>Defines a standard operating procedure (SOP) recommendation.</p>"}, "SopRecommendationList": {"type": "list", "member": {"shape": "SopRecommendation"}}, "SopServiceType": {"type": "string", "enum": ["SSM"]}, "Sort": {"type": "structure", "required": ["field"], "members": {"ascending": {"shape": "BooleanOptional", "documentation": "<p>Indicates the name or identifier of the field or attribute that should be used as the basis for sorting the metrics.</p>"}, "field": {"shape": "String255", "documentation": "<p>Indicates the order in which you want to sort the metrics. By default, the list is sorted in ascending order. To sort the list in descending order, set this field to False.</p>"}}, "documentation": "<p>Indicates the sorting order of the fields in the metrics.</p>"}, "SortList": {"type": "list", "member": {"shape": "Sort"}, "max": 50, "min": 0}, "SpecReferenceId": {"type": "string", "max": 500, "min": 1}, "StartAppAssessmentRequest": {"type": "structure", "required": ["appArn", "appVersion", "assessmentName"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "appVersion": {"shape": "EntityVersion", "documentation": "<p>The version of the application.</p>"}, "assessmentName": {"shape": "EntityName", "documentation": "<p>The name for the assessment.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>Used for an idempotency token. A client token is a unique, case-sensitive string of up to 64 ASCII characters. You should not reuse the same client token for other API requests.</p>", "idempotencyToken": true}, "tags": {"shape": "TagMap", "documentation": "<p>Tags assigned to the resource. A tag is a label that you assign to an Amazon Web Services resource. Each tag consists of a key/value pair.</p>"}}}, "StartAppAssessmentResponse": {"type": "structure", "required": ["assessment"], "members": {"assessment": {"shape": "AppAssessment", "documentation": "<p>The assessment created.</p>"}}}, "StartMetricsExportRequest": {"type": "structure", "members": {"bucketName": {"shape": "EntityName", "documentation": "<p>(Optional) Specifies the name of the Amazon Simple Storage Service bucket where the exported metrics will be stored.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>Used for an idempotency token. A client token is a unique, case-sensitive string of up to 64 ASCII characters. You should not reuse the same client token for other API requests.</p>", "idempotencyToken": true}}}, "StartMetricsExportResponse": {"type": "structure", "required": ["metricsExportId", "status"], "members": {"metricsExportId": {"shape": "String255", "documentation": "<p>Identifier of the metrics export task.</p>"}, "status": {"shape": "MetricsExportStatusType", "documentation": "<p>Indicates the status of the metrics export task.</p>"}}}, "StartResourceGroupingRecommendationTaskRequest": {"type": "structure", "required": ["appArn"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}}}, "StartResourceGroupingRecommendationTaskResponse": {"type": "structure", "required": ["appArn", "groupingId", "status"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "errorMessage": {"shape": "String500", "documentation": "<p>Error that occurred while executing a grouping recommendation task.</p>"}, "groupingId": {"shape": "String255", "documentation": "<p>Identifier of the grouping recommendation task.</p>"}, "status": {"shape": "ResourcesGroupingRecGenStatusType", "documentation": "<p>Status of the action.</p>"}}}, "String1024": {"type": "string", "max": 1024, "min": 1}, "String128WithoutWhitespace": {"type": "string", "pattern": "^\\S{1,128}$"}, "String2048": {"type": "string", "max": 2048, "min": 1}, "String255": {"type": "string", "max": 255, "min": 1}, "String255List": {"type": "list", "member": {"shape": "String255"}}, "String500": {"type": "string", "max": 500, "min": 1}, "SuggestedChangesList": {"type": "list", "member": {"shape": "EntityDescription"}}, "TagKey": {"type": "string", "max": 128, "min": 1, "pattern": "^[^\\x00-\\x1f\\x22]+$"}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 50, "min": 1, "sensitive": true}, "TagMap": {"type": "map", "key": {"shape": "TagKey"}, "value": {"shape": "TagValue"}, "max": 50, "min": 1, "sensitive": true}, "TagResourceRequest": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the resource. </p>", "location": "uri", "locationName": "resourceArn"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags to assign to the resource. Each tag consists of a key/value pair.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0, "pattern": "^[^\\x00-\\x1f\\x22]*$"}, "TemplateFormat": {"type": "string", "enum": ["CfnYaml", "CfnJson"]}, "TerraformSource": {"type": "structure", "required": ["s3StateFileUrl"], "members": {"s3StateFileUrl": {"shape": "S3Url", "documentation": "<p> The URL of the Terraform s3 state file you need to import. </p>"}}, "documentation": "<p> The Terraform s3 state file you need to import. </p>"}, "TerraformSourceList": {"type": "list", "member": {"shape": "TerraformSource"}}, "TestRecommendation": {"type": "structure", "required": ["referenceId"], "members": {"appComponentId": {"shape": "EntityName255", "documentation": "<p>Indicates the identifier of the AppComponent.</p>"}, "appComponentName": {"shape": "EntityId", "documentation": "<p>Name of the Application Component.</p>"}, "dependsOnAlarms": {"shape": "AlarmReferenceIdList", "documentation": "<p> A list of recommended alarms that are used in the test and must be exported before or with the test. </p>"}, "description": {"shape": "String500", "documentation": "<p>Description for the test recommendation.</p>"}, "intent": {"shape": "EntityDescription", "documentation": "<p>Intent of the test recommendation.</p>"}, "items": {"shape": "RecommendationItemList", "documentation": "<p>The test recommendation items.</p>"}, "name": {"shape": "DocumentName", "documentation": "<p>Name of the test recommendation.</p>"}, "prerequisite": {"shape": "String500", "documentation": "<p>Prerequisite of the test recommendation.</p>"}, "recommendationId": {"shape": "<PERSON><PERSON>", "documentation": "<p>Identifier for the test recommendation.</p>"}, "recommendationStatus": {"shape": "RecommendationStatus", "documentation": "<p>Status of the recommended test.</p>"}, "referenceId": {"shape": "SpecReferenceId", "documentation": "<p>Reference identifier for the test recommendation.</p>"}, "risk": {"shape": "TestRisk", "documentation": "<p>Level of risk for this test recommendation.</p>"}, "type": {"shape": "TestType", "documentation": "<p>Type of test recommendation.</p>"}}, "documentation": "<p>Defines a test recommendation.</p>"}, "TestRecommendationList": {"type": "list", "member": {"shape": "TestRecommendation"}}, "TestRisk": {"type": "string", "enum": ["Small", "Medium", "High"]}, "TestType": {"type": "string", "enum": ["Software", "Hardware", "AZ", "Region"]}, "ThrottlingException": {"type": "structure", "members": {"message": {"shape": "String500"}, "retryAfterSeconds": {"shape": "RetryAfterSeconds", "documentation": "<p>The number of seconds to wait before retrying the operation.</p>"}}, "documentation": "<p>This exception occurs when you have exceeded the limit on the number of requests per second.</p>", "error": {"httpStatusCode": 429, "senderFault": true}, "exception": true}, "TimeStamp": {"type": "timestamp"}, "UnsupportedResource": {"type": "structure", "required": ["logicalResourceId", "physicalResourceId", "resourceType"], "members": {"logicalResourceId": {"shape": "LogicalResourceId", "documentation": "<p>Logical resource identifier for the unsupported resource.</p>"}, "physicalResourceId": {"shape": "PhysicalResourceId", "documentation": "<p>Physical resource identifier for the unsupported resource.</p>"}, "resourceType": {"shape": "String255", "documentation": "<p>The type of resource.</p>"}, "unsupportedResourceStatus": {"shape": "String255", "documentation": "<p>The status of the unsupported resource.</p>"}}, "documentation": "<p>Defines a resource that is not supported by Resilience Hub.</p>"}, "UnsupportedResourceList": {"type": "list", "member": {"shape": "UnsupportedResource"}}, "UntagResourceRequest": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the resource. </p>", "location": "uri", "locationName": "resourceArn"}, "tagKeys": {"shape": "TagKeyList", "documentation": "<p>The keys of the tags you want to remove.</p>", "location": "querystring", "locationName": "tagKeys"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateAppRequest": {"type": "structure", "required": ["appArn"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "assessmentSchedule": {"shape": "AppAssessmentScheduleType", "documentation": "<p> Assessment execution schedule with 'Daily' or 'Disabled' values. </p>"}, "clearResiliencyPolicyArn": {"shape": "BooleanOptional", "documentation": "<p>Specifies if the resiliency policy ARN should be cleared.</p>"}, "description": {"shape": "EntityDescription", "documentation": "<p>The optional description for an app.</p>"}, "eventSubscriptions": {"shape": "EventSubscriptionList", "documentation": "<p>The list of events you would like to subscribe and get notification for. Currently, Resilience Hub supports notifications only for <b>Drift detected</b> and <b>Scheduled assessment failure</b> events.</p>"}, "permissionModel": {"shape": "PermissionModel", "documentation": "<p>Defines the roles and credentials that Resilience Hub would use while creating an application, importing its resources, and running an assessment.</p>"}, "policyArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the resiliency policy. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:resiliency-policy/<code>policy-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}}}, "UpdateAppResponse": {"type": "structure", "required": ["app"], "members": {"app": {"shape": "App", "documentation": "<p>The specified application, returned as an object with details including compliance status, creation time, description, resiliency score, and more.</p>"}}}, "UpdateAppVersionAppComponentRequest": {"type": "structure", "required": ["appArn", "id"], "members": {"additionalInfo": {"shape": "AdditionalInfoMap", "documentation": "<p>Currently, there is no supported additional information for Application Components.</p>"}, "appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "id": {"shape": "String255", "documentation": "<p>Identifier of the Application Component.</p>"}, "name": {"shape": "String255", "documentation": "<p>Name of the Application Component.</p>"}, "type": {"shape": "String255", "documentation": "<p>Type of Application Component. For more information about the types of Application Component, see <a href=\"https://docs.aws.amazon.com/resilience-hub/latest/userguide/AppComponent.grouping.html\">Grouping resources in an AppComponent</a>.</p>"}}}, "UpdateAppVersionAppComponentResponse": {"type": "structure", "required": ["appArn", "appVersion"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "appComponent": {"shape": "AppComponent", "documentation": "<p>List of Application Components that belong to this resource.</p>"}, "appVersion": {"shape": "EntityVersion", "documentation": "<p>Resilience Hub application version.</p>"}}}, "UpdateAppVersionRequest": {"type": "structure", "required": ["appArn"], "members": {"additionalInfo": {"shape": "AdditionalInfoMap", "documentation": "<p>Additional configuration parameters for an Resilience Hub application. If you want to implement <code>additionalInfo</code> through the Resilience Hub console rather than using an API call, see <a href=\"https://docs.aws.amazon.com/resilience-hub/latest/userguide/app-config-param.html\">Configure the application configuration parameters</a>.</p> <note> <p>Currently, this parameter accepts a key-value mapping (in a string format) of only one failover region and one associated account.</p> <p>Key: <code>\"failover-regions\"</code> </p> <p>Value: <code>\"[{\"region\":\"&lt;REGION&gt;\", \"accounts\":[{\"id\":\"&lt;ACCOUNT_ID&gt;\"}]}]\"</code> </p> </note>"}, "appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}}}, "UpdateAppVersionResourceRequest": {"type": "structure", "required": ["appArn"], "members": {"additionalInfo": {"shape": "AdditionalInfoMap", "documentation": "<p>Currently, there is no supported additional information for resources.</p>"}, "appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "appComponents": {"shape": "AppComponentNameList", "documentation": "<p>List of Application Components that this resource belongs to. If an Application Component is not part of the Resilience Hub application, it will be added.</p>"}, "awsAccountId": {"shape": "CustomerId", "documentation": "<p>Amazon Web Services account that owns the physical resource.</p>"}, "awsRegion": {"shape": "AwsRegion", "documentation": "<p>Amazon Web Services region that owns the physical resource.</p>"}, "excluded": {"shape": "BooleanOptional", "documentation": "<p>Indicates if a resource is excluded from an Resilience Hub application.</p> <note> <p>You can exclude only imported resources from an Resilience Hub application.</p> </note>"}, "logicalResourceId": {"shape": "LogicalResourceId", "documentation": "<p>Logical identifier of the resource.</p>"}, "physicalResourceId": {"shape": "String2048", "documentation": "<p>Physical identifier of the resource.</p>"}, "resourceName": {"shape": "EntityName", "documentation": "<p>Name of the resource.</p>"}, "resourceType": {"shape": "String255", "documentation": "<p>Type of resource.</p>"}}}, "UpdateAppVersionResourceResponse": {"type": "structure", "required": ["appArn", "appVersion"], "members": {"appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "appVersion": {"shape": "EntityVersion", "documentation": "<p>Resilience Hub application version.</p>"}, "physicalResource": {"shape": "PhysicalResource", "documentation": "<p>Defines a physical resource. A physical resource is a resource that exists in your account. It can be identified using an Amazon Resource Name (ARN) or a Resilience Hub-native identifier.</p>"}}}, "UpdateAppVersionResponse": {"type": "structure", "required": ["appArn", "appVersion"], "members": {"additionalInfo": {"shape": "AdditionalInfoMap", "documentation": "<p>Additional configuration parameters for an Resilience Hub application. If you want to implement <code>additionalInfo</code> through the Resilience Hub console rather than using an API call, see <a href=\"https://docs.aws.amazon.com/resilience-hub/latest/userguide/app-config-param.html\">Configure the application configuration parameters</a>.</p> <note> <p>Currently, this parameter supports only failover region and account.</p> </note>"}, "appArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the Resilience Hub application. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:app/<code>app-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "appVersion": {"shape": "EntityVersion", "documentation": "<p>Resilience Hub application version.</p>"}}}, "UpdateRecommendationStatusItem": {"type": "structure", "members": {"resourceId": {"shape": "String500", "documentation": "<p>Resource identifier of the operational recommendation item.</p>"}, "targetAccountId": {"shape": "CustomerId", "documentation": "<p>Identifier of the target Amazon Web Services account.</p>"}, "targetRegion": {"shape": "AwsRegion", "documentation": "<p>Identifier of the target Amazon Web Services Region.</p>"}}, "documentation": "<p>Defines the operational recommendation item that needs a status update.</p>"}, "UpdateRecommendationStatusRequestEntries": {"type": "list", "member": {"shape": "UpdateRecommendationStatusRequestEntry"}, "max": 50, "min": 1}, "UpdateRecommendationStatusRequestEntry": {"type": "structure", "required": ["entryId", "excluded", "referenceId"], "members": {"appComponentId": {"shape": "EntityName255", "documentation": "<p>Indicates the identifier of the AppComponent.</p>"}, "entryId": {"shape": "String255", "documentation": "<p>An identifier for an entry in this batch that is used to communicate the result.</p> <note> <p>The <code>entryId</code>s of a batch request need to be unique within a request.</p> </note>"}, "excludeReason": {"shape": "ExcludeRecommendationReason", "documentation": "<p>Indicates the reason for excluding an operational recommendation.</p>"}, "excluded": {"shape": "BooleanOptional", "documentation": "<p>Indicates if the operational recommendation needs to be excluded. If set to True, the operational recommendation will be excluded.</p>"}, "item": {"shape": "UpdateRecommendationStatusItem", "documentation": "<p>The operational recommendation item.</p>"}, "referenceId": {"shape": "SpecReferenceId", "documentation": "<p>Reference identifier of the operational recommendation item.</p>"}}, "documentation": "<p>Defines the operational recommendation item that is to be included or excluded.</p>"}, "UpdateResiliencyPolicyRequest": {"type": "structure", "required": ["policyArn"], "members": {"dataLocationConstraint": {"shape": "DataLocationConstraint", "documentation": "<p>Specifies a high-level geographical location constraint for where your resilience policy data can be stored.</p>"}, "policy": {"shape": "DisruptionPolicy", "documentation": "<p>Resiliency policy to be created, including the recovery time objective (RTO) and recovery point objective (RPO) in seconds.</p>"}, "policyArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the resiliency policy. The format for this ARN is: arn:<code>partition</code>:resiliencehub:<code>region</code>:<code>account</code>:resiliency-policy/<code>policy-id</code>. For more information about ARNs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\"> Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i> guide.</p>"}, "policyDescription": {"shape": "EntityDescription", "documentation": "<p>Description of the resiliency policy.</p>"}, "policyName": {"shape": "EntityName", "documentation": "<p>Name of the resiliency policy.</p>"}, "tier": {"shape": "ResiliencyPolicyTier", "documentation": "<p>The tier for this resiliency policy, ranging from the highest severity (<code>MissionCritical</code>) to lowest (<code>NonCritical</code>).</p>"}}}, "UpdateResiliencyPolicyResponse": {"type": "structure", "required": ["policy"], "members": {"policy": {"shape": "ResiliencyPolicy", "documentation": "<p>The resiliency policy that was updated, including the recovery time objective (RTO) and recovery point objective (RPO) in seconds.</p>"}}}, "Uuid": {"type": "string", "pattern": "^[0-9a-f]{8}-[0-9a-f]{4}-[0-5][0-9a-f]{3}-[089ab][0-9a-f]{3}-[0-9a-f]{12}$"}, "ValidationException": {"type": "structure", "members": {"message": {"shape": "String500"}}, "documentation": "<p>This exception occurs when a request is not valid.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}}, "documentation": "<p>Resilience Hub helps you proactively prepare and protect your Amazon Web Services applications from disruptions. It offers continual resiliency assessment and validation that integrates into your software development lifecycle. This enables you to uncover resiliency weaknesses, ensure recovery time objective (RTO) and recovery point objective (RPO) targets for your applications are met, and resolve issues before they are released into production. </p>"}