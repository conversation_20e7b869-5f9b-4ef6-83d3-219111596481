{"version": "1.0", "resources": {"Lexicon": {"operation": "ListLexicons", "resourceIdentifier": {"Name": "Lexicons[].Name"}}, "SpeechSynthesisTask": {"operation": "ListSpeechSynthesisTasks", "resourceIdentifier": {"TaskId": "SynthesisTasks[].TaskId", "SnsTopicArn": "SynthesisTasks[].SnsTopicArn", "LexiconNames": "SynthesisTasks[].LexiconNames", "OutputFormat": "SynthesisTasks[].OutputFormat", "SampleRate": "SynthesisTasks[].SampleRate", "SpeechMarkTypes": "SynthesisTasks[].SpeechMarkTypes", "TextType": "SynthesisTasks[].TextType", "VoiceId": "SynthesisTasks[].VoiceId", "LanguageCode": "SynthesisTasks[].LanguageCode"}}}, "operations": {"DeleteLexicon": {"Name": {"completions": [{"parameters": {}, "resourceName": "Lexicon", "resourceIdentifier": "Name"}]}}, "DescribeVoices": {"LanguageCode": {"completions": [{"parameters": {}, "resourceName": "SpeechSynthesisTask", "resourceIdentifier": "LanguageCode"}]}}, "GetLexicon": {"Name": {"completions": [{"parameters": {}, "resourceName": "Lexicon", "resourceIdentifier": "Name"}]}}, "GetSpeechSynthesisTask": {"TaskId": {"completions": [{"parameters": {}, "resourceName": "SpeechSynthesisTask", "resourceIdentifier": "TaskId"}]}}, "PutLexicon": {"Name": {"completions": [{"parameters": {}, "resourceName": "Lexicon", "resourceIdentifier": "Name"}]}}, "StartSpeechSynthesisTask": {"LexiconNames": {"completions": [{"parameters": {}, "resourceName": "SpeechSynthesisTask", "resourceIdentifier": "LexiconNames"}]}, "OutputFormat": {"completions": [{"parameters": {}, "resourceName": "SpeechSynthesisTask", "resourceIdentifier": "OutputFormat"}]}, "SampleRate": {"completions": [{"parameters": {}, "resourceName": "SpeechSynthesisTask", "resourceIdentifier": "SampleRate"}]}, "SnsTopicArn": {"completions": [{"parameters": {}, "resourceName": "SpeechSynthesisTask", "resourceIdentifier": "SnsTopicArn"}]}, "SpeechMarkTypes": {"completions": [{"parameters": {}, "resourceName": "SpeechSynthesisTask", "resourceIdentifier": "SpeechMarkTypes"}]}, "TextType": {"completions": [{"parameters": {}, "resourceName": "SpeechSynthesisTask", "resourceIdentifier": "TextType"}]}, "VoiceId": {"completions": [{"parameters": {}, "resourceName": "SpeechSynthesisTask", "resourceIdentifier": "VoiceId"}]}, "LanguageCode": {"completions": [{"parameters": {}, "resourceName": "SpeechSynthesisTask", "resourceIdentifier": "LanguageCode"}]}}, "SynthesizeSpeech": {"LexiconNames": {"completions": [{"parameters": {}, "resourceName": "SpeechSynthesisTask", "resourceIdentifier": "LexiconNames"}]}, "OutputFormat": {"completions": [{"parameters": {}, "resourceName": "SpeechSynthesisTask", "resourceIdentifier": "OutputFormat"}]}, "SampleRate": {"completions": [{"parameters": {}, "resourceName": "SpeechSynthesisTask", "resourceIdentifier": "SampleRate"}]}, "SpeechMarkTypes": {"completions": [{"parameters": {}, "resourceName": "SpeechSynthesisTask", "resourceIdentifier": "SpeechMarkTypes"}]}, "TextType": {"completions": [{"parameters": {}, "resourceName": "SpeechSynthesisTask", "resourceIdentifier": "TextType"}]}, "VoiceId": {"completions": [{"parameters": {}, "resourceName": "SpeechSynthesisTask", "resourceIdentifier": "VoiceId"}]}, "LanguageCode": {"completions": [{"parameters": {}, "resourceName": "SpeechSynthesisTask", "resourceIdentifier": "LanguageCode"}]}}}}