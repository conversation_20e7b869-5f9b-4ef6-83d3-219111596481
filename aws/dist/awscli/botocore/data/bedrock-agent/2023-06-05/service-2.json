{"version": "2.0", "metadata": {"apiVersion": "2023-06-05", "endpointPrefix": "bedrock-agent", "jsonVersion": "1.1", "protocol": "rest-json", "protocols": ["rest-json"], "serviceFullName": "Agents for Amazon Bedrock", "serviceId": "Bedrock Agent", "signatureVersion": "v4", "signingName": "bedrock", "uid": "bedrock-agent-2023-06-05", "auth": ["aws.auth#sigv4"]}, "operations": {"AssociateAgentCollaborator": {"name": "AssociateAgentCollaborator", "http": {"method": "PUT", "requestUri": "/agents/{agentId}/agentversions/{agentVersion}/agentcollaborators/", "responseCode": 200}, "input": {"shape": "AssociateAgentCollaboratorRequest"}, "output": {"shape": "AssociateAgentCollaboratorResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Makes an agent a collaborator for another agent.</p>", "idempotent": true}, "AssociateAgentKnowledgeBase": {"name": "AssociateAgentKnowledgeBase", "http": {"method": "PUT", "requestUri": "/agents/{agentId}/agentversions/{agentVersion}/knowledgebases/", "responseCode": 200}, "input": {"shape": "AssociateAgentKnowledgeBaseRequest"}, "output": {"shape": "AssociateAgentKnowledgeBaseResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Associates a knowledge base with an agent. If a knowledge base is associated and its <code>indexState</code> is set to <code>Enabled</code>, the agent queries the knowledge base for information to augment its response to the user.</p>", "idempotent": true}, "CreateAgent": {"name": "CreateAgent", "http": {"method": "PUT", "requestUri": "/agents/", "responseCode": 202}, "input": {"shape": "CreateAgentRequest"}, "output": {"shape": "CreateAgentResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates an agent that orchestrates interactions between foundation models, data sources, software applications, user conversations, and APIs to carry out tasks to help customers.</p> <ul> <li> <p>Specify the following fields for security purposes.</p> <ul> <li> <p> <code>agentResourceRoleArn</code> – The Amazon Resource Name (ARN) of the role with permissions to invoke API operations on an agent.</p> </li> <li> <p>(Optional) <code>customerEncryptionKeyArn</code> – The Amazon Resource Name (ARN) of a KMS key to encrypt the creation of the agent.</p> </li> <li> <p>(Optional) <code>idleSessionTTLinSeconds</code> – Specify the number of seconds for which the agent should maintain session information. After this time expires, the subsequent <code>InvokeAgent</code> request begins a new session.</p> </li> </ul> </li> <li> <p>To enable your agent to retain conversational context across multiple sessions, include a <code>memoryConfiguration</code> object. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/agents-configure-memory.html\">Configure memory</a>.</p> </li> <li> <p>To override the default prompt behavior for agent orchestration and to use advanced prompts, include a <code>promptOverrideConfiguration</code> object. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/advanced-prompts.html\">Advanced prompts</a>.</p> </li> <li> <p>If your agent fails to be created, the response returns a list of <code>failureReasons</code> alongside a list of <code>recommendedActions</code> for you to troubleshoot.</p> </li> <li> <p>The agent instructions will not be honored if your agent has only one knowledge base, uses default prompts, has no action group, and user input is disabled.</p> </li> </ul>", "idempotent": true}, "CreateAgentActionGroup": {"name": "CreateAgentActionGroup", "http": {"method": "PUT", "requestUri": "/agents/{agentId}/agentversions/{agentVersion}/actiongroups/", "responseCode": 200}, "input": {"shape": "CreateAgentActionGroupRequest"}, "output": {"shape": "CreateAgentActionGroupResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates an action group for an agent. An action group represents the actions that an agent can carry out for the customer by defining the APIs that an agent can call and the logic for calling them.</p> <p>To allow your agent to request the user for additional information when trying to complete a task, add an action group with the <code>parentActionGroupSignature</code> field set to <code>AMAZON.UserInput</code>. </p> <p>To allow your agent to generate, run, and troubleshoot code when trying to complete a task, add an action group with the <code>parentActionGroupSignature</code> field set to <code>AMAZON.CodeInterpreter</code>. </p> <p>You must leave the <code>description</code>, <code>apiSchema</code>, and <code>actionGroupExecutor</code> fields blank for this action group. During orchestration, if your agent determines that it needs to invoke an API in an action group, but doesn't have enough information to complete the API request, it will invoke this action group instead and return an <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_Observation.html\">Observation</a> reprompting the user for more information.</p>", "idempotent": true}, "CreateAgentAlias": {"name": "CreateAgentAlias", "http": {"method": "PUT", "requestUri": "/agents/{agentId}/agentaliases/", "responseCode": 202}, "input": {"shape": "CreateAgentAliasRequest"}, "output": {"shape": "CreateAgentAliasResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates an alias of an agent that can be used to deploy the agent.</p>", "idempotent": true}, "CreateDataSource": {"name": "CreateDataSource", "http": {"method": "PUT", "requestUri": "/knowledgebases/{knowledgeBaseId}/datasources/", "responseCode": 200}, "input": {"shape": "CreateDataSourceRequest"}, "output": {"shape": "CreateDataSourceResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Connects a knowledge base to a data source. You specify the configuration for the specific data source service in the <code>dataSourceConfiguration</code> field.</p> <important> <p>You can't change the <code>chunkingConfiguration</code> after you create the data source connector.</p> </important>", "idempotent": true}, "CreateFlow": {"name": "CreateFlow", "http": {"method": "POST", "requestUri": "/flows/", "responseCode": 201}, "input": {"shape": "CreateFlowRequest"}, "output": {"shape": "CreateFlowResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates a prompt flow that you can use to send an input through various steps to yield an output. Configure nodes, each of which corresponds to a step of the flow, and create connections between the nodes to create paths to different outputs. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/flows-how-it-works.html\">How it works</a> and <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/flows-create.html\">Create a flow in Amazon Bedrock</a> in the Amazon Bedrock User Guide.</p>", "idempotent": true}, "CreateFlowAlias": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "http": {"method": "POST", "requestUri": "/flows/{flowIdentifier}/aliases", "responseCode": 201}, "input": {"shape": "CreateFlowAliasRequest"}, "output": {"shape": "CreateFlowAliasResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates an alias of a flow for deployment. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/flows-deploy.html\">Deploy a flow in Amazon Bedrock</a> in the Amazon Bedrock User Guide.</p>", "idempotent": true}, "CreateFlowVersion": {"name": "CreateFlowVersion", "http": {"method": "POST", "requestUri": "/flows/{flowIdentifier}/versions", "responseCode": 201}, "input": {"shape": "CreateFlowVersionRequest"}, "output": {"shape": "CreateFlowVersionResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates a version of the flow that you can deploy. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/flows-deploy.html\">Deploy a flow in Amazon Bedrock</a> in the Amazon Bedrock User Guide.</p>", "idempotent": true}, "CreateKnowledgeBase": {"name": "CreateKnowledgeBase", "http": {"method": "PUT", "requestUri": "/knowledgebases/", "responseCode": 202}, "input": {"shape": "CreateKnowledgeBaseRequest"}, "output": {"shape": "CreateKnowledgeBaseResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates a knowledge base. A knowledge base contains your data sources so that Large Language Models (LLMs) can use your data. To create a knowledge base, you must first set up your data sources and configure a supported vector store. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/knowlege-base-prereq.html\">Set up a knowledge base</a>.</p> <note> <p>If you prefer to let Amazon Bedrock create and manage a vector store for you in Amazon OpenSearch Service, use the console. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/knowledge-base-create\">Create a knowledge base</a>.</p> </note> <ul> <li> <p>Provide the <code>name</code> and an optional <code>description</code>.</p> </li> <li> <p>Provide the Amazon Resource Name (ARN) with permissions to create a knowledge base in the <code>roleArn</code> field.</p> </li> <li> <p>Provide the embedding model to use in the <code>embeddingModelArn</code> field in the <code>knowledgeBaseConfiguration</code> object.</p> </li> <li> <p>Provide the configuration for your vector store in the <code>storageConfiguration</code> object.</p> <ul> <li> <p>For an Amazon OpenSearch Service database, use the <code>opensearchServerlessConfiguration</code> object. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/knowledge-base-setup-oss.html\">Create a vector store in Amazon OpenSearch Service</a>.</p> </li> <li> <p>For an Amazon Aurora database, use the <code>RdsConfiguration</code> object. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/knowledge-base-setup-rds.html\">Create a vector store in Amazon Aurora</a>.</p> </li> <li> <p>For a Pinecone database, use the <code>pineconeConfiguration</code> object. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/knowledge-base-setup-pinecone.html\">Create a vector store in Pinecone</a>.</p> </li> <li> <p>For a Redis Enterprise Cloud database, use the <code>redisEnterpriseCloudConfiguration</code> object. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/knowledge-base-setup-redis.html\">Create a vector store in Redis Enterprise Cloud</a>.</p> </li> </ul> </li> </ul>", "idempotent": true}, "CreatePrompt": {"name": "Create<PERSON>rompt", "http": {"method": "POST", "requestUri": "/prompts/", "responseCode": 201}, "input": {"shape": "CreatePromptRequest"}, "output": {"shape": "CreatePromptResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates a prompt in your prompt library that you can add to a flow. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/prompt-management.html\">Prompt management in Amazon Bedrock</a>, <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/prompt-management-create.html\">Create a prompt using Prompt management</a> and <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/flows.html\">Prompt flows in Amazon Bedrock</a> in the Amazon Bedrock User Guide.</p>", "idempotent": true}, "CreatePromptVersion": {"name": "CreatePromptVersion", "http": {"method": "POST", "requestUri": "/prompts/{promptIdentifier}/versions", "responseCode": 201}, "input": {"shape": "CreatePromptVersionRequest"}, "output": {"shape": "CreatePromptVersionResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates a static snapshot of your prompt that can be deployed to production. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/prompt-management-deploy.html\">Deploy prompts using Prompt management by creating versions</a> in the Amazon Bedrock User Guide.</p>", "idempotent": true}, "DeleteAgent": {"name": "DeleteAgent", "http": {"method": "DELETE", "requestUri": "/agents/{agentId}/", "responseCode": 202}, "input": {"shape": "DeleteAgentRequest"}, "output": {"shape": "DeleteAgentResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}], "documentation": "<p>Deletes an agent.</p>", "idempotent": true}, "DeleteAgentActionGroup": {"name": "DeleteAgentActionGroup", "http": {"method": "DELETE", "requestUri": "/agents/{agentId}/agentversions/{agentVersion}/actiongroups/{actionGroupId}/", "responseCode": 204}, "input": {"shape": "DeleteAgentActionGroupRequest"}, "output": {"shape": "DeleteAgentActionGroupResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}], "documentation": "<p>Deletes an action group in an agent.</p>", "idempotent": true}, "DeleteAgentAlias": {"name": "DeleteAgentAlias", "http": {"method": "DELETE", "requestUri": "/agents/{agentId}/agentaliases/{agentAliasId}/", "responseCode": 202}, "input": {"shape": "DeleteAgentAliasRequest"}, "output": {"shape": "DeleteAgentAliasResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Deletes an alias of an agent.</p>", "idempotent": true}, "DeleteAgentVersion": {"name": "DeleteAgentVersion", "http": {"method": "DELETE", "requestUri": "/agents/{agentId}/agentversions/{agentVersion}/", "responseCode": 202}, "input": {"shape": "DeleteAgentVersionRequest"}, "output": {"shape": "DeleteAgentVersionResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}], "documentation": "<p>Deletes a version of an agent.</p>", "idempotent": true}, "DeleteDataSource": {"name": "DeleteDataSource", "http": {"method": "DELETE", "requestUri": "/knowledgebases/{knowledgeBaseId}/datasources/{dataSourceId}", "responseCode": 202}, "input": {"shape": "DeleteDataSourceRequest"}, "output": {"shape": "DeleteDataSourceResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}], "documentation": "<p>Deletes a data source from a knowledge base.</p>", "idempotent": true}, "DeleteFlow": {"name": "DeleteFlow", "http": {"method": "DELETE", "requestUri": "/flows/{flowIdentifier}/", "responseCode": 200}, "input": {"shape": "DeleteFlowRequest"}, "output": {"shape": "DeleteFlowResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}], "documentation": "<p>Deletes a flow.</p>", "idempotent": true}, "DeleteFlowAlias": {"name": "DeleteFlowAlias", "http": {"method": "DELETE", "requestUri": "/flows/{flowIdentifier}/aliases/{aliasIdentifier}", "responseCode": 200}, "input": {"shape": "DeleteFlowAliasRequest"}, "output": {"shape": "DeleteFlowAliasResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}], "documentation": "<p>Deletes an alias of a flow.</p>", "idempotent": true}, "DeleteFlowVersion": {"name": "DeleteFlowVersion", "http": {"method": "DELETE", "requestUri": "/flows/{flowIdentifier}/versions/{flowVersion}/", "responseCode": 200}, "input": {"shape": "DeleteFlowVersionRequest"}, "output": {"shape": "DeleteFlowVersionResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}], "documentation": "<p>Deletes a version of a flow.</p>", "idempotent": true}, "DeleteKnowledgeBase": {"name": "DeleteKnowledgeBase", "http": {"method": "DELETE", "requestUri": "/knowledgebases/{knowledgeBaseId}", "responseCode": 202}, "input": {"shape": "DeleteKnowledgeBaseRequest"}, "output": {"shape": "DeleteKnowledgeBaseResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}], "documentation": "<p>Deletes a knowledge base. Before deleting a knowledge base, you should disassociate the knowledge base from any agents that it is associated with by making a <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent_DisassociateAgentKnowledgeBase.html\">DisassociateAgentKnowledgeBase</a> request.</p>", "idempotent": true}, "DeleteKnowledgeBaseDocuments": {"name": "DeleteKnowledgeBaseDocuments", "http": {"method": "POST", "requestUri": "/knowledgebases/{knowledgeBaseId}/datasources/{dataSourceId}/documents/deleteDocuments", "responseCode": 202}, "input": {"shape": "DeleteKnowledgeBaseDocumentsRequest"}, "output": {"shape": "DeleteKnowledgeBaseDocumentsResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Deletes documents from a data source and syncs the changes to the knowledge base that is connected to it. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/kb-direct-ingestion.html\">Ingest changes directly into a knowledge base</a> in the Amazon Bedrock User Guide.</p>", "idempotent": true}, "DeletePrompt": {"name": "DeletePrompt", "http": {"method": "DELETE", "requestUri": "/prompts/{promptIdentifier}/", "responseCode": 200}, "input": {"shape": "DeletePromptRequest"}, "output": {"shape": "DeletePromptResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}], "documentation": "<p>Deletes a prompt or a version of it, depending on whether you include the <code>promptVersion</code> field or not. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/prompt-management-manage.html#prompt-management-delete.html\">Delete prompts from the Prompt management tool</a> and <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/prompt-management-deploy.html#prompt-management-versions-delete.html\">Delete a version of a prompt from the Prompt management tool</a> in the Amazon Bedrock User Guide.</p>", "idempotent": true}, "DisassociateAgentCollaborator": {"name": "DisassociateAgentCollaborator", "http": {"method": "DELETE", "requestUri": "/agents/{agentId}/agentversions/{agentVersion}/agentcollaborators/{collaboratorId}/", "responseCode": 204}, "input": {"shape": "DisassociateAgentCollaboratorRequest"}, "output": {"shape": "DisassociateAgentCollaboratorResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}], "documentation": "<p>Disassociates an agent collaborator.</p>", "idempotent": true}, "DisassociateAgentKnowledgeBase": {"name": "DisassociateAgentKnowledgeBase", "http": {"method": "DELETE", "requestUri": "/agents/{agentId}/agentversions/{agentVersion}/knowledgebases/{knowledgeBaseId}/", "responseCode": 204}, "input": {"shape": "DisassociateAgentKnowledgeBaseRequest"}, "output": {"shape": "DisassociateAgentKnowledgeBaseResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}], "documentation": "<p>Disassociates a knowledge base from an agent.</p>", "idempotent": true}, "GetAgent": {"name": "GetAgent", "http": {"method": "GET", "requestUri": "/agents/{agentId}/", "responseCode": 200}, "input": {"shape": "GetAgentRequest"}, "output": {"shape": "GetAgentResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Gets information about an agent.</p>"}, "GetAgentActionGroup": {"name": "GetAgentActionGroup", "http": {"method": "GET", "requestUri": "/agents/{agentId}/agentversions/{agentVersion}/actiongroups/{actionGroupId}/", "responseCode": 200}, "input": {"shape": "GetAgentActionGroupRequest"}, "output": {"shape": "GetAgentActionGroupResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Gets information about an action group for an agent.</p>"}, "GetAgentAlias": {"name": "GetAgentAlias", "http": {"method": "GET", "requestUri": "/agents/{agentId}/agentaliases/{agentAliasId}/", "responseCode": 200}, "input": {"shape": "GetAgentAliasRequest"}, "output": {"shape": "GetAgentAliasResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Gets information about an alias of an agent.</p>"}, "GetAgentCollaborator": {"name": "GetAgentCollaborator", "http": {"method": "GET", "requestUri": "/agents/{agentId}/agentversions/{agentVersion}/agentcollaborators/{collaboratorId}/", "responseCode": 200}, "input": {"shape": "GetAgentCollaboratorRequest"}, "output": {"shape": "GetAgentCollaboratorResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves information about an agent's collaborator.</p>"}, "GetAgentKnowledgeBase": {"name": "GetAgentKnowledgeBase", "http": {"method": "GET", "requestUri": "/agents/{agentId}/agentversions/{agentVersion}/knowledgebases/{knowledgeBaseId}/", "responseCode": 200}, "input": {"shape": "GetAgentKnowledgeBaseRequest"}, "output": {"shape": "GetAgentKnowledgeBaseResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Gets information about a knowledge base associated with an agent.</p>"}, "GetAgentVersion": {"name": "GetAgentVersion", "http": {"method": "GET", "requestUri": "/agents/{agentId}/agentversions/{agentVersion}/", "responseCode": 200}, "input": {"shape": "GetAgentVersionRequest"}, "output": {"shape": "GetAgentVersionResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Gets details about a version of an agent.</p>"}, "GetDataSource": {"name": "GetDataSource", "http": {"method": "GET", "requestUri": "/knowledgebases/{knowledgeBaseId}/datasources/{dataSourceId}", "responseCode": 200}, "input": {"shape": "GetDataSourceRequest"}, "output": {"shape": "GetDataSourceResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Gets information about a data source.</p>"}, "GetFlow": {"name": "GetFlow", "http": {"method": "GET", "requestUri": "/flows/{flowIdentifier}/", "responseCode": 200}, "input": {"shape": "GetFlowRequest"}, "output": {"shape": "GetFlowResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves information about a flow. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/flows-manage.html\">Manage a flow in Amazon Bedrock</a> in the Amazon Bedrock User Guide.</p>"}, "GetFlowAlias": {"name": "GetFlowAlias", "http": {"method": "GET", "requestUri": "/flows/{flowIdentifier}/aliases/{aliasIdentifier}", "responseCode": 200}, "input": {"shape": "GetFlowAliasRequest"}, "output": {"shape": "GetFlowAliasResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves information about a flow. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/flows-deploy.html\">Deploy a flow in Amazon Bedrock</a> in the Amazon Bedrock User Guide.</p>"}, "GetFlowVersion": {"name": "GetFlowVersion", "http": {"method": "GET", "requestUri": "/flows/{flowIdentifier}/versions/{flowVersion}/", "responseCode": 200}, "input": {"shape": "GetFlowVersionRequest"}, "output": {"shape": "GetFlowVersionResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves information about a version of a flow. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/flows-deploy.html\">Deploy a flow in Amazon Bedrock</a> in the Amazon Bedrock User Guide.</p>"}, "GetIngestionJob": {"name": "GetIngestionJob", "http": {"method": "GET", "requestUri": "/knowledgebases/{knowledgeBaseId}/datasources/{dataSourceId}/ingestionjobs/{ingestionJobId}", "responseCode": 200}, "input": {"shape": "GetIngestionJobRequest"}, "output": {"shape": "GetIngestionJobResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Gets information about a data ingestion job. Data sources are ingested into your knowledge base so that Large Language Models (LLMs) can use your data.</p>"}, "GetKnowledgeBase": {"name": "GetKnowledgeBase", "http": {"method": "GET", "requestUri": "/knowledgebases/{knowledgeBaseId}", "responseCode": 200}, "input": {"shape": "GetKnowledgeBaseRequest"}, "output": {"shape": "GetKnowledgeBaseResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Gets information about a knoweldge base.</p>"}, "GetKnowledgeBaseDocuments": {"name": "GetKnowledgeBaseDocuments", "http": {"method": "POST", "requestUri": "/knowledgebases/{knowledgeBaseId}/datasources/{dataSourceId}/documents/getDocuments", "responseCode": 200}, "input": {"shape": "GetKnowledgeBaseDocumentsRequest"}, "output": {"shape": "GetKnowledgeBaseDocumentsResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Retrieves specific documents from a data source that is connected to a knowledge base. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/kb-direct-ingestion.html\">Ingest changes directly into a knowledge base</a> in the Amazon Bedrock User Guide.</p>"}, "GetPrompt": {"name": "GetPrompt", "http": {"method": "GET", "requestUri": "/prompts/{promptIdentifier}/", "responseCode": 200}, "input": {"shape": "GetPromptRequest"}, "output": {"shape": "GetPromptResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves information about the working draft (<code>DRAFT</code> version) of a prompt or a version of it, depending on whether you include the <code>promptVersion</code> field or not. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/prompt-management-manage.html#prompt-management-view.html\">View information about prompts using Prompt management</a> and <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/prompt-management-deploy.html#prompt-management-versions-view.html\">View information about a version of your prompt</a> in the Amazon Bedrock User Guide.</p>"}, "IngestKnowledgeBaseDocuments": {"name": "IngestKnowledgeBaseDocuments", "http": {"method": "PUT", "requestUri": "/knowledgebases/{knowledgeBaseId}/datasources/{dataSourceId}/documents", "responseCode": 202}, "input": {"shape": "IngestKnowledgeBaseDocumentsRequest"}, "output": {"shape": "IngestKnowledgeBaseDocumentsResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Ingests documents directly into the knowledge base that is connected to the data source. The <code>dataSourceType</code> specified in the content for each document must match the type of the data source that you specify in the header. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/kb-direct-ingestion.html\">Ingest changes directly into a knowledge base</a> in the Amazon Bedrock User Guide.</p>", "idempotent": true}, "ListAgentActionGroups": {"name": "ListAgentActionGroups", "http": {"method": "POST", "requestUri": "/agents/{agentId}/agentversions/{agentVersion}/actiongroups/", "responseCode": 200}, "input": {"shape": "ListAgentActionGroupsRequest"}, "output": {"shape": "ListAgentActionGroupsResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Lists the action groups for an agent and information about each one.</p>"}, "ListAgentAliases": {"name": "ListAgentAliases", "http": {"method": "POST", "requestUri": "/agents/{agentId}/agentaliases/", "responseCode": 200}, "input": {"shape": "ListAgentAliasesRequest"}, "output": {"shape": "ListAgentAliasesResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Lists the aliases of an agent and information about each one.</p>"}, "ListAgentCollaborators": {"name": "ListAgentCollaborators", "http": {"method": "POST", "requestUri": "/agents/{agentId}/agentversions/{agentVersion}/agentcollaborators/", "responseCode": 200}, "input": {"shape": "ListAgentCollaboratorsRequest"}, "output": {"shape": "ListAgentCollaboratorsResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieve a list of an agent's collaborators.</p>"}, "ListAgentKnowledgeBases": {"name": "ListAgentKnowledgeBases", "http": {"method": "POST", "requestUri": "/agents/{agentId}/agentversions/{agentVersion}/knowledgebases/", "responseCode": 200}, "input": {"shape": "ListAgentKnowledgeBasesRequest"}, "output": {"shape": "ListAgentKnowledgeBasesResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Lists knowledge bases associated with an agent and information about each one.</p>"}, "ListAgentVersions": {"name": "ListAgentVersions", "http": {"method": "POST", "requestUri": "/agents/{agentId}/agentversions/", "responseCode": 200}, "input": {"shape": "ListAgentVersionsRequest"}, "output": {"shape": "ListAgentVersionsResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Lists the versions of an agent and information about each version.</p>"}, "ListAgents": {"name": "ListAgents", "http": {"method": "POST", "requestUri": "/agents/", "responseCode": 200}, "input": {"shape": "ListAgentsRequest"}, "output": {"shape": "ListAgentsResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists the agents belonging to an account and information about each agent.</p>"}, "ListDataSources": {"name": "ListDataSources", "http": {"method": "POST", "requestUri": "/knowledgebases/{knowledgeBaseId}/datasources/", "responseCode": 200}, "input": {"shape": "ListDataSourcesRequest"}, "output": {"shape": "ListDataSourcesResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Lists the data sources in a knowledge base and information about each one.</p>"}, "ListFlowAliases": {"name": "ListFlowAliases", "http": {"method": "GET", "requestUri": "/flows/{flowIdentifier}/aliases", "responseCode": 200}, "input": {"shape": "ListFlowAliasesRequest"}, "output": {"shape": "ListFlowAliasesResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns a list of aliases for a flow.</p>"}, "ListFlowVersions": {"name": "ListFlowVersions", "http": {"method": "GET", "requestUri": "/flows/{flowIdentifier}/versions", "responseCode": 200}, "input": {"shape": "ListFlowVersionsRequest"}, "output": {"shape": "ListFlowVersionsResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns a list of information about each flow. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/flows-deploy.html\">Deploy a flow in Amazon Bedrock</a> in the Amazon Bedrock User Guide.</p>"}, "ListFlows": {"name": "ListFlows", "http": {"method": "GET", "requestUri": "/flows/", "responseCode": 200}, "input": {"shape": "ListFlowsRequest"}, "output": {"shape": "ListFlowsResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Returns a list of flows and information about each flow. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/flows-manage.html\">Manage a flow in Amazon Bedrock</a> in the Amazon Bedrock User Guide.</p>"}, "ListIngestionJobs": {"name": "ListIngestionJobs", "http": {"method": "POST", "requestUri": "/knowledgebases/{knowledgeBaseId}/datasources/{dataSourceId}/ingestionjobs/", "responseCode": 200}, "input": {"shape": "ListIngestionJobsRequest"}, "output": {"shape": "ListIngestionJobsResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Lists the data ingestion jobs for a data source. The list also includes information about each job.</p>"}, "ListKnowledgeBaseDocuments": {"name": "ListKnowledgeBaseDocuments", "http": {"method": "POST", "requestUri": "/knowledgebases/{knowledgeBaseId}/datasources/{dataSourceId}/documents", "responseCode": 200}, "input": {"shape": "ListKnowledgeBaseDocumentsRequest"}, "output": {"shape": "ListKnowledgeBaseDocumentsResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Retrieves all the documents contained in a data source that is connected to a knowledge base. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/kb-direct-ingestion.html\">Ingest changes directly into a knowledge base</a> in the Amazon Bedrock User Guide.</p>"}, "ListKnowledgeBases": {"name": "ListKnowledgeBases", "http": {"method": "POST", "requestUri": "/knowledgebases/", "responseCode": 200}, "input": {"shape": "ListKnowledgeBasesRequest"}, "output": {"shape": "ListKnowledgeBasesResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists the knowledge bases in an account. The list also includesinformation about each knowledge base.</p>"}, "ListPrompts": {"name": "ListPrompts", "http": {"method": "GET", "requestUri": "/prompts/", "responseCode": 200}, "input": {"shape": "ListPromptsRequest"}, "output": {"shape": "ListPromptsResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns either information about the working draft (<code>DRAFT</code> version) of each prompt in an account, or information about of all versions of a prompt, depending on whether you include the <code>promptIdentifier</code> field or not. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/prompt-management-manage.html#prompt-management-view.html\">View information about prompts using Prompt management</a> in the Amazon Bedrock User Guide.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>List all the tags for the resource you specify.</p>"}, "PrepareAgent": {"name": "PrepareAgent", "http": {"method": "POST", "requestUri": "/agents/{agentId}/", "responseCode": 202}, "input": {"shape": "PrepareAgentRequest"}, "output": {"shape": "PrepareAgentResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates a <code>DRAFT</code> version of the agent that can be used for internal testing.</p>"}, "PrepareFlow": {"name": "PrepareFlow", "http": {"method": "POST", "requestUri": "/flows/{flowIdentifier}/", "responseCode": 202}, "input": {"shape": "PrepareFlowRequest"}, "output": {"shape": "PrepareFlowResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Prepares the <code>DRAFT</code> version of a flow so that it can be invoked. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/flows-test.html\">Test a flow in Amazon Bedrock</a> in the Amazon Bedrock User Guide.</p>"}, "StartIngestionJob": {"name": "StartIngestionJob", "http": {"method": "PUT", "requestUri": "/knowledgebases/{knowledgeBaseId}/datasources/{dataSourceId}/ingestionjobs/", "responseCode": 202}, "input": {"shape": "StartIngestionJobRequest"}, "output": {"shape": "StartIngestionJobResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Begins a data ingestion job. Data sources are ingested into your knowledge base so that Large Language Models (LLMs) can use your data.</p>", "idempotent": true}, "StopIngestionJob": {"name": "StopIngestionJob", "http": {"method": "POST", "requestUri": "/knowledgebases/{knowledgeBaseId}/datasources/{dataSourceId}/ingestionjobs/{ingestionJobId}/stop", "responseCode": 202}, "input": {"shape": "StopIngestionJobRequest"}, "output": {"shape": "StopIngestionJobResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}], "documentation": "<p>Stops a currently running data ingestion job. You can send a <code>StartIngestionJob</code> request again to ingest the rest of your data when you are ready.</p>", "idempotent": true}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Associate tags with a resource. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/what-is-service.html\">Tagging resources</a> in the Amazon Bedrock User Guide.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Remove tags from a resource.</p>", "idempotent": true}, "UpdateAgent": {"name": "UpdateAgent", "http": {"method": "PUT", "requestUri": "/agents/{agentId}/", "responseCode": 202}, "input": {"shape": "UpdateAgentRequest"}, "output": {"shape": "UpdateAgentResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Updates the configuration of an agent.</p>", "idempotent": true}, "UpdateAgentActionGroup": {"name": "UpdateAgentActionGroup", "http": {"method": "PUT", "requestUri": "/agents/{agentId}/agentversions/{agentVersion}/actiongroups/{actionGroupId}/", "responseCode": 200}, "input": {"shape": "UpdateAgentActionGroupRequest"}, "output": {"shape": "UpdateAgentActionGroupResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Updates the configuration for an action group for an agent.</p>", "idempotent": true}, "UpdateAgentAlias": {"name": "UpdateAgentAlias", "http": {"method": "PUT", "requestUri": "/agents/{agentId}/agentaliases/{agentAliasId}/", "responseCode": 202}, "input": {"shape": "UpdateAgentAliasRequest"}, "output": {"shape": "UpdateAgentAliasResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Updates configurations for an alias of an agent.</p>", "idempotent": true}, "UpdateAgentCollaborator": {"name": "UpdateAgentCollaborator", "http": {"method": "PUT", "requestUri": "/agents/{agentId}/agentversions/{agentVersion}/agentcollaborators/{collaboratorId}/", "responseCode": 200}, "input": {"shape": "UpdateAgentCollaboratorRequest"}, "output": {"shape": "UpdateAgentCollaboratorResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Updates an agent's collaborator.</p>", "idempotent": true}, "UpdateAgentKnowledgeBase": {"name": "UpdateAgentKnowledgeBase", "http": {"method": "PUT", "requestUri": "/agents/{agentId}/agentversions/{agentVersion}/knowledgebases/{knowledgeBaseId}/", "responseCode": 200}, "input": {"shape": "UpdateAgentKnowledgeBaseRequest"}, "output": {"shape": "UpdateAgentKnowledgeBaseResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}], "documentation": "<p>Updates the configuration for a knowledge base that has been associated with an agent.</p>", "idempotent": true}, "UpdateDataSource": {"name": "UpdateDataSource", "http": {"method": "PUT", "requestUri": "/knowledgebases/{knowledgeBaseId}/datasources/{dataSourceId}", "responseCode": 200}, "input": {"shape": "UpdateDataSourceRequest"}, "output": {"shape": "UpdateDataSourceResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}], "documentation": "<p>Updates the configurations for a data source connector.</p> <important> <p>You can't change the <code>chunkingConfiguration</code> after you create the data source connector. Specify the existing <code>chunkingConfiguration</code>.</p> </important>", "idempotent": true}, "UpdateFlow": {"name": "UpdateFlow", "http": {"method": "PUT", "requestUri": "/flows/{flowIdentifier}/", "responseCode": 200}, "input": {"shape": "UpdateFlowRequest"}, "output": {"shape": "UpdateFlowResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Modifies a flow. Include both fields that you want to keep and fields that you want to change. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/flows-how-it-works.html\">How it works</a> and <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/flows-create.html\">Create a flow in Amazon Bedrock</a> in the Amazon Bedrock User Guide.</p>", "idempotent": true}, "UpdateFlowAlias": {"name": "UpdateFlowAlias", "http": {"method": "PUT", "requestUri": "/flows/{flowIdentifier}/aliases/{aliasIdentifier}", "responseCode": 200}, "input": {"shape": "UpdateFlowAliasRequest"}, "output": {"shape": "UpdateFlowAliasResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Modifies the alias of a flow. Include both fields that you want to keep and ones that you want to change. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/flows-deploy.html\">Deploy a flow in Amazon Bedrock</a> in the Amazon Bedrock User Guide.</p>", "idempotent": true}, "UpdateKnowledgeBase": {"name": "UpdateKnowledgeBase", "http": {"method": "PUT", "requestUri": "/knowledgebases/{knowledgeBaseId}", "responseCode": 202}, "input": {"shape": "UpdateKnowledgeBaseRequest"}, "output": {"shape": "UpdateKnowledgeBaseResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}], "documentation": "<p>Updates the configuration of a knowledge base with the fields that you specify. Because all fields will be overwritten, you must include the same values for fields that you want to keep the same.</p> <p>You can change the following fields:</p> <ul> <li> <p> <code>name</code> </p> </li> <li> <p> <code>description</code> </p> </li> <li> <p> <code>roleArn</code> </p> </li> </ul> <p>You can't change the <code>knowledgeBaseConfiguration</code> or <code>storageConfiguration</code> fields, so you must specify the same configurations as when you created the knowledge base. You can send a <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent_GetKnowledgeBase.html\">GetKnowledgeBase</a> request and copy the same configurations.</p>", "idempotent": true}, "UpdatePrompt": {"name": "UpdatePrompt", "http": {"method": "PUT", "requestUri": "/prompts/{promptIdentifier}/", "responseCode": 200}, "input": {"shape": "UpdatePromptRequest"}, "output": {"shape": "UpdatePromptResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Modifies a prompt in your prompt library. Include both fields that you want to keep and fields that you want to replace. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/prompt-management.html\">Prompt management in Amazon Bedrock</a> and <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/prompt-management-manage.html#prompt-management-edit\">Edit prompts in your prompt library</a> in the Amazon Bedrock User Guide.</p>", "idempotent": true}, "ValidateFlowDefinition": {"name": "ValidateFlowDefinition", "http": {"method": "POST", "requestUri": "/flows/validate-definition", "responseCode": 200}, "input": {"shape": "ValidateFlowDefinitionRequest"}, "output": {"shape": "ValidateFlowDefinitionResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Validates the definition of a flow.</p>"}}, "shapes": {"APISchema": {"type": "structure", "members": {"payload": {"shape": "Payload", "documentation": "<p>The JSON or YAML-formatted payload defining the OpenAPI schema for the action group. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/agents-api-schema.html\">Action group OpenAPI schemas</a>.</p>"}, "s3": {"shape": "S3Identifier", "documentation": "<p>Contains details about the S3 object containing the OpenAPI schema for the action group. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/agents-api-schema.html\">Action group OpenAPI schemas</a>.</p>"}}, "documentation": "<p>Contains details about the OpenAPI schema for the action group. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/agents-api-schema.html\">Action group OpenAPI schemas</a>. You can either include the schema directly in the <code>payload</code> field or you can upload it to an S3 bucket and specify the S3 bucket location in the <code>s3</code> field.</p>", "union": true}, "AccessDeniedException": {"type": "structure", "members": {"message": {"shape": "NonBlankString"}}, "documentation": "<p>The request is denied because of missing access permissions.</p>", "error": {"httpStatusCode": 403, "senderFault": true}, "exception": true}, "ActionGroupExecutor": {"type": "structure", "members": {"customControl": {"shape": "CustomControlMethod", "documentation": "<p>To return the action group invocation results directly in the <code>InvokeAgent</code> response, specify <code>RETURN_CONTROL</code>.</p>"}, "lambda": {"shape": "LambdaArn", "documentation": "<p>The Amazon Resource Name (ARN) of the Lambda function containing the business logic that is carried out upon invoking the action.</p>"}}, "documentation": "<p>Contains details about the Lambda function containing the business logic that is carried out upon invoking the action or the custom control method for handling the information elicited from the user.</p>", "union": true}, "ActionGroupSignature": {"type": "string", "enum": ["AMAZON.UserInput", "AMAZON.CodeInterpreter", "ANTHROPIC.Computer", "ANTHROPIC.Bash", "ANTHROPIC.TextEditor"]}, "ActionGroupSignatureParams": {"type": "map", "key": {"shape": "ActionGroupSignatureParamsKeyString"}, "value": {"shape": "ActionGroupSignatureParamsValueString"}}, "ActionGroupSignatureParamsKeyString": {"type": "string", "max": 100, "min": 0}, "ActionGroupSignatureParamsValueString": {"type": "string", "max": 100, "min": 0}, "ActionGroupState": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "ActionGroupSummaries": {"type": "list", "member": {"shape": "ActionGroupSummary"}, "max": 10, "min": 0}, "ActionGroupSummary": {"type": "structure", "required": ["actionGroupId", "actionGroupName", "actionGroupState", "updatedAt"], "members": {"actionGroupId": {"shape": "Id", "documentation": "<p>The unique identifier of the action group.</p>"}, "actionGroupName": {"shape": "Name", "documentation": "<p>The name of the action group.</p>"}, "actionGroupState": {"shape": "ActionGroupState", "documentation": "<p>Specifies whether the action group is available for the agent to invoke or not when sending an <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_InvokeAgent.html\">InvokeAgent</a> request.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description of the action group.</p>"}, "updatedAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the action group was last updated.</p>"}}, "documentation": "<p>Contains details about an action group.</p>"}, "AdditionalModelRequestFields": {"type": "map", "key": {"shape": "AdditionalModelRequestFieldsKey"}, "value": {"shape": "AdditionalModelRequestFieldsValue"}}, "AdditionalModelRequestFieldsKey": {"type": "string", "max": 100, "min": 1}, "AdditionalModelRequestFieldsValue": {"type": "structure", "members": {}, "document": true}, "Agent": {"type": "structure", "required": ["agentArn", "agentId", "<PERSON><PERSON><PERSON>", "agentResourceRoleArn", "agentStatus", "agentVersion", "createdAt", "idleSessionTTLInSeconds", "updatedAt"], "members": {"agentArn": {"shape": "AgentArn", "documentation": "<p>The Amazon Resource Name (ARN) of the agent.</p>"}, "agentCollaboration": {"shape": "AgentCollaboration", "documentation": "<p>The agent's collaboration settings.</p>"}, "agentId": {"shape": "Id", "documentation": "<p>The unique identifier of the agent.</p>"}, "agentName": {"shape": "Name", "documentation": "<p>The name of the agent.</p>"}, "agentResourceRoleArn": {"shape": "AgentRoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role with permissions to invoke API operations on the agent.</p>"}, "agentStatus": {"shape": "AgentStatus", "documentation": "<p>The status of the agent and whether it is ready for use. The following statuses are possible:</p> <ul> <li> <p>CREATING – The agent is being created.</p> </li> <li> <p>PREPARING – The agent is being prepared.</p> </li> <li> <p>PREPARED – The agent is prepared and ready to be invoked.</p> </li> <li> <p>NOT_PREPARED – The agent has been created but not yet prepared.</p> </li> <li> <p>FAILED – The agent API operation failed.</p> </li> <li> <p>UPDATING – The agent is being updated.</p> </li> <li> <p>DELETING – The agent is being deleted.</p> </li> </ul>"}, "agentVersion": {"shape": "DraftVersion", "documentation": "<p>The version of the agent.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A unique, case-sensitive identifier to ensure that the API request completes no more than one time. If this token matches a previous request, Amazon Bedrock ignores the request, but does not return an error. For more information, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/APIReference/Run_Instance_Idempotency.html\">Ensuring idempotency</a>.</p>"}, "createdAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the agent was created.</p>"}, "customOrchestration": {"shape": "CustomOrchestration", "documentation": "<p> Contains custom orchestration configurations for the agent. </p>"}, "customerEncryptionKeyArn": {"shape": "KmsKeyArn", "documentation": "<p>The Amazon Resource Name (ARN) of the KMS key that encrypts the agent.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description of the agent.</p>"}, "failureReasons": {"shape": "FailureReasons", "documentation": "<p>Contains reasons that the agent-related API that you invoked failed.</p>"}, "foundationModel": {"shape": "ModelIdentifier", "documentation": "<p>The foundation model used for orchestration by the agent.</p>"}, "guardrailConfiguration": {"shape": "GuardrailConfiguration", "documentation": "<p>Details about the guardrail associated with the agent.</p>"}, "idleSessionTTLInSeconds": {"shape": "SessionTTL", "documentation": "<p>The number of seconds for which Amazon Bedrock keeps information about a user's conversation with the agent.</p> <p>A user interaction remains active for the amount of time specified. If no conversation occurs during this time, the session expires and Amazon Bedrock deletes any data provided before the timeout.</p>"}, "instruction": {"shape": "Instruction", "documentation": "<p>Instructions that tell the agent what it should do and how it should interact with users.</p>"}, "memoryConfiguration": {"shape": "MemoryConfiguration", "documentation": "<p>Contains memory configuration for the agent.</p>"}, "orchestrationType": {"shape": "OrchestrationType", "documentation": "<p> Specifies the orchestration strategy for the agent. </p>"}, "preparedAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the agent was last prepared.</p>"}, "promptOverrideConfiguration": {"shape": "PromptOverrideConfiguration", "documentation": "<p>Contains configurations to override prompt templates in different parts of an agent sequence. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/advanced-prompts.html\">Advanced prompts</a>.</p>"}, "recommendedActions": {"shape": "RecommendedActions", "documentation": "<p>Contains recommended actions to take for the agent-related API that you invoked to succeed.</p>"}, "updatedAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the agent was last updated.</p>"}}, "documentation": "<p>Contains details about an agent.</p>"}, "AgentActionGroup": {"type": "structure", "required": ["actionGroupId", "actionGroupName", "actionGroupState", "agentId", "agentVersion", "createdAt", "updatedAt"], "members": {"actionGroupExecutor": {"shape": "ActionGroupExecutor", "documentation": "<p>The Amazon Resource Name (ARN) of the Lambda function containing the business logic that is carried out upon invoking the action or the custom control method for handling the information elicited from the user.</p>"}, "actionGroupId": {"shape": "Id", "documentation": "<p>The unique identifier of the action group.</p>"}, "actionGroupName": {"shape": "Name", "documentation": "<p>The name of the action group.</p>"}, "actionGroupState": {"shape": "ActionGroupState", "documentation": "<p>Specifies whether the action group is available for the agent to invoke or not when sending an <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_InvokeAgent.html\">InvokeAgent</a> request.</p>"}, "agentId": {"shape": "Id", "documentation": "<p>The unique identifier of the agent to which the action group belongs.</p>"}, "agentVersion": {"shape": "Version", "documentation": "<p>The version of the agent to which the action group belongs.</p>"}, "apiSchema": {"shape": "APISchema", "documentation": "<p>Contains either details about the S3 object containing the OpenAPI schema for the action group or the JSON or YAML-formatted payload defining the schema. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/agents-api-schema.html\">Action group OpenAPI schemas</a>.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A unique, case-sensitive identifier to ensure that the API request completes no more than one time. If this token matches a previous request, Amazon Bedrock ignores the request, but does not return an error. For more information, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/APIReference/Run_Instance_Idempotency.html\">Ensuring idempotency</a>.</p>"}, "createdAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the action group was created.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description of the action group.</p>"}, "functionSchema": {"shape": "FunctionSchema", "documentation": "<p>Defines functions that each define parameters that the agent needs to invoke from the user. Each function represents an action in an action group.</p>"}, "parentActionGroupSignatureParams": {"shape": "ActionGroupSignatureParams", "documentation": "<p>The configuration settings for a computer use action.</p> <important> <p> Computer use is a new Anthropic Claude model capability (in beta) available with Claude 3.7 Sonnet and Claude 3.5 Sonnet v2 only. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/agents-computer-use.html\">Configure an Amazon Bedrock Agent to complete tasks with computer use tools</a>. </p> </important>"}, "parentActionSignature": {"shape": "ActionGroupSignature", "documentation": "<p>If this field is set as <code>AMAZON.UserInput</code>, the agent can request the user for additional information when trying to complete a task. The <code>description</code>, <code>apiSchema</code>, and <code>actionGroupExecutor</code> fields must be blank for this action group.</p> <p>During orchestration, if the agent determines that it needs to invoke an API in an action group, but doesn't have enough information to complete the API request, it will invoke this action group instead and return an <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_Observation.html\">Observation</a> reprompting the user for more information.</p>"}, "updatedAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the action group was last updated.</p>"}}, "documentation": "<p>Contains details about an action group.</p>"}, "AgentAlias": {"type": "structure", "required": ["agentAliasArn", "agentAliasId", "agentAliasName", "agentAliasStatus", "agentId", "createdAt", "routingConfiguration", "updatedAt"], "members": {"agentAliasArn": {"shape": "AgentAliasArn", "documentation": "<p>The Amazon Resource Name (ARN) of the alias of the agent.</p>"}, "agentAliasHistoryEvents": {"shape": "AgentAliasHistoryEvents", "documentation": "<p>Contains details about the history of the alias.</p>"}, "agentAliasId": {"shape": "AgentAliasId", "documentation": "<p>The unique identifier of the alias of the agent.</p>"}, "agentAliasName": {"shape": "Name", "documentation": "<p>The name of the alias of the agent.</p>"}, "agentAliasStatus": {"shape": "AgentAliasStatus", "documentation": "<p>The status of the alias of the agent and whether it is ready for use. The following statuses are possible:</p> <ul> <li> <p>CREATING – The agent alias is being created.</p> </li> <li> <p>PREPARED – The agent alias is finished being created or updated and is ready to be invoked.</p> </li> <li> <p>FAILED – The agent alias API operation failed.</p> </li> <li> <p>UPDATING – The agent alias is being updated.</p> </li> <li> <p>DELETING – The agent alias is being deleted.</p> </li> <li> <p>DISSOCIATED - The agent alias has no version associated with it.</p> </li> </ul>"}, "agentId": {"shape": "Id", "documentation": "<p>The unique identifier of the agent.</p>"}, "aliasInvocationState": {"shape": "AliasInvocationState", "documentation": "<p>The invocation state for the agent alias. If the agent alias is running, the value is <code>ACCEPT_INVOCATIONS</code>. If the agent alias is paused, the value is <code>REJECT_INVOCATIONS</code>. Use the <code>UpdateAgentAlias</code> operation to change the invocation state.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A unique, case-sensitive identifier to ensure that the API request completes no more than one time. If this token matches a previous request, Amazon Bedrock ignores the request, but does not return an error. For more information, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/APIReference/Run_Instance_Idempotency.html\">Ensuring idempotency</a>.</p>"}, "createdAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the alias of the agent was created.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description of the alias of the agent.</p>"}, "failureReasons": {"shape": "FailureReasons", "documentation": "<p>Information on the failure of Provisioned Throughput assigned to an agent alias.</p>"}, "routingConfiguration": {"shape": "AgentAliasRoutingConfiguration", "documentation": "<p>Contains details about the routing configuration of the alias.</p>"}, "updatedAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the alias was last updated.</p>"}}, "documentation": "<p>Contains details about an alias of an agent.</p>"}, "AgentAliasArn": {"type": "string", "max": 2048, "min": 0, "pattern": "^arn:aws:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:agent-alias/[0-9a-zA-Z]{10}/[0-9a-zA-Z]{10}$"}, "AgentAliasHistoryEvent": {"type": "structure", "members": {"endDate": {"shape": "DateTimestamp", "documentation": "<p>The date that the alias stopped being associated to the version in the <code>routingConfiguration</code> object</p>"}, "routingConfiguration": {"shape": "AgentAliasRoutingConfiguration", "documentation": "<p>Contains details about the version of the agent with which the alias is associated.</p>"}, "startDate": {"shape": "DateTimestamp", "documentation": "<p>The date that the alias began being associated to the version in the <code>routingConfiguration</code> object.</p>"}}, "documentation": "<p>Contains details about the history of the alias.</p>"}, "AgentAliasHistoryEvents": {"type": "list", "member": {"shape": "AgentAliasHistoryEvent"}, "max": 10, "min": 0}, "AgentAliasId": {"type": "string", "max": 10, "min": 10, "pattern": "^(\\bTSTALIASID\\b|[0-9a-zA-Z]+)$"}, "AgentAliasRoutingConfiguration": {"type": "list", "member": {"shape": "AgentAliasRoutingConfigurationListItem"}, "max": 1, "min": 0}, "AgentAliasRoutingConfigurationListItem": {"type": "structure", "members": {"agentVersion": {"shape": "Version", "documentation": "<p>The version of the agent with which the alias is associated.</p>"}, "provisionedThroughput": {"shape": "ProvisionedModelIdentifier", "documentation": "<p>Information on the Provisioned Throughput assigned to an agent alias.</p>"}}, "documentation": "<p>Contains details about the routing configuration of the alias.</p>"}, "AgentAliasStatus": {"type": "string", "enum": ["CREATING", "PREPARED", "FAILED", "UPDATING", "DELETING", "DISSOCIATED"]}, "AgentAliasSummaries": {"type": "list", "member": {"shape": "Agent<PERSON>lias<PERSON><PERSON><PERSON><PERSON>"}, "max": 10, "min": 0}, "AgentAliasSummary": {"type": "structure", "required": ["agentAliasId", "agentAliasName", "agentAliasStatus", "createdAt", "updatedAt"], "members": {"agentAliasId": {"shape": "AgentAliasId", "documentation": "<p>Contains details about </p>"}, "agentAliasName": {"shape": "Name", "documentation": "<p>The name of the alias.</p>"}, "agentAliasStatus": {"shape": "AgentAliasStatus", "documentation": "<p>The status of the alias.</p>"}, "aliasInvocationState": {"shape": "AliasInvocationState", "documentation": "<p>The invocation state for the agent alias. If the agent alias is running, the value is <code>ACCEPT_INVOCATIONS</code>. If the agent alias is paused, the value is <code>REJECT_INVOCATIONS</code>. Use the <code>UpdateAgentAlias</code> operation to change the invocation state.</p>"}, "createdAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the alias of the agent was created.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description of the alias.</p>"}, "routingConfiguration": {"shape": "AgentAliasRoutingConfiguration", "documentation": "<p>Contains details about the version of the agent with which the alias is associated.</p>"}, "updatedAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the alias was last updated.</p>"}}, "documentation": "<p>Contains details about an alias of an agent.</p>"}, "AgentArn": {"type": "string", "max": 2048, "min": 0, "pattern": "^arn:aws:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:agent/[0-9a-zA-Z]{10}$"}, "AgentCollaboration": {"type": "string", "enum": ["SUPERVISOR", "SUPERVISOR_ROUTER", "DISABLED"]}, "AgentCollaborator": {"type": "structure", "required": ["agentDescriptor", "agentId", "agentVersion", "collaborationInstruction", "collaboratorId", "<PERSON><PERSON><PERSON>", "createdAt", "lastUpdatedAt"], "members": {"agentDescriptor": {"shape": "AgentDescriptor", "documentation": "<p>The collaborator's agent descriptor.</p>"}, "agentId": {"shape": "Id", "documentation": "<p>The collaborator's agent ID.</p>"}, "agentVersion": {"shape": "Version", "documentation": "<p>The collaborator's agent version.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>The collaborator's client token.</p>"}, "collaborationInstruction": {"shape": "CollaborationInstruction", "documentation": "<p>The collaborator's instructions.</p>"}, "collaboratorId": {"shape": "Id", "documentation": "<p>The collaborator's collaborator ID.</p>"}, "collaboratorName": {"shape": "Name", "documentation": "<p>The collaborator's collaborator name.</p>"}, "createdAt": {"shape": "DateTimestamp", "documentation": "<p>When the collaborator was created.</p>"}, "lastUpdatedAt": {"shape": "DateTimestamp", "documentation": "<p>When the collaborator was updated.</p>"}, "relayConversationHistory": {"shape": "RelayConversationHistory", "documentation": "<p>The collaborator's relay conversation history.</p>"}}, "documentation": "<p>An agent collaborator.</p>"}, "AgentCollaboratorSummaries": {"type": "list", "member": {"shape": "AgentCollaboratorSummary"}, "max": 10, "min": 0}, "AgentCollaboratorSummary": {"type": "structure", "required": ["agentDescriptor", "agentId", "agentVersion", "collaborationInstruction", "collaboratorId", "<PERSON><PERSON><PERSON>", "createdAt", "lastUpdatedAt", "relayConversationHistory"], "members": {"agentDescriptor": {"shape": "AgentDescriptor", "documentation": "<p>The collaborator's agent descriptor.</p>"}, "agentId": {"shape": "Id", "documentation": "<p>The collaborator's agent ID.</p>"}, "agentVersion": {"shape": "Version", "documentation": "<p>The collaborator's agent version.</p>"}, "collaborationInstruction": {"shape": "CollaborationInstruction", "documentation": "<p>The collaborator's collaboration instruction.</p>"}, "collaboratorId": {"shape": "Id", "documentation": "<p>The collaborator's ID.</p>"}, "collaboratorName": {"shape": "Name", "documentation": "<p>The collaborator's name.</p>"}, "createdAt": {"shape": "DateTimestamp", "documentation": "<p>When the collaborator was created.</p>"}, "lastUpdatedAt": {"shape": "DateTimestamp", "documentation": "<p>When the collaborator was last updated.</p>"}, "relayConversationHistory": {"shape": "RelayConversationHistory", "documentation": "<p>The collaborator's relay conversation history.</p>"}}, "documentation": "<p>An agent collaborator summary.</p>"}, "AgentDescriptor": {"type": "structure", "members": {"aliasArn": {"shape": "AgentAliasArn", "documentation": "<p>The agent's alias <PERSON><PERSON>.</p>"}}, "documentation": "<p>An agent descriptor.</p>"}, "AgentFlowNodeConfiguration": {"type": "structure", "required": ["agentAliasArn"], "members": {"agentAliasArn": {"shape": "AgentAliasArn", "documentation": "<p>The Amazon Resource Name (ARN) of the alias of the agent to invoke.</p>"}}, "documentation": "<p>Defines an agent node in your flow. You specify the agent to invoke at this point in the flow. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/flows-nodes.html\"><PERSON><PERSON> types in a flow</a> in the Amazon Bedrock User Guide.</p>"}, "AgentKnowledgeBase": {"type": "structure", "required": ["agentId", "agentVersion", "createdAt", "description", "knowledgeBaseId", "knowledgeBaseState", "updatedAt"], "members": {"agentId": {"shape": "Id", "documentation": "<p>The unique identifier of the agent with which the knowledge base is associated.</p>"}, "agentVersion": {"shape": "Version", "documentation": "<p>The version of the agent with which the knowledge base is associated.</p>"}, "createdAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the association between the agent and the knowledge base was created.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description of the association between the agent and the knowledge base.</p>"}, "knowledgeBaseId": {"shape": "Id", "documentation": "<p>The unique identifier of the association between the agent and the knowledge base.</p>"}, "knowledgeBaseState": {"shape": "KnowledgeBaseState", "documentation": "<p>Specifies whether to use the knowledge base or not when sending an <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_InvokeAgent.html\">InvokeAgent</a> request.</p>"}, "updatedAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the association between the agent and the knowledge base was last updated.</p>"}}, "documentation": "<p>Contains details about a knowledge base that is associated with an agent.</p>"}, "AgentKnowledgeBaseSummaries": {"type": "list", "member": {"shape": "AgentKnowledgeBaseSummary"}, "max": 10, "min": 0}, "AgentKnowledgeBaseSummary": {"type": "structure", "required": ["knowledgeBaseId", "knowledgeBaseState", "updatedAt"], "members": {"description": {"shape": "Description", "documentation": "<p>The description of the knowledge base associated with an agent.</p>"}, "knowledgeBaseId": {"shape": "Id", "documentation": "<p>The unique identifier of the knowledge base associated with an agent.</p>"}, "knowledgeBaseState": {"shape": "KnowledgeBaseState", "documentation": "<p>Specifies whether the agent uses the knowledge base or not when sending an <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_InvokeAgent.html\">InvokeAgent</a> request.</p>"}, "updatedAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the knowledge base associated with an agent was last updated.</p>"}}, "documentation": "<p>Contains details about a knowledge base associated with an agent.</p>"}, "AgentRoleArn": {"type": "string", "max": 2048, "min": 0, "pattern": "^arn:aws(-[^:]+)?:iam::([0-9]{12})?:role/.+$"}, "AgentStatus": {"type": "string", "enum": ["CREATING", "PREPARING", "PREPARED", "NOT_PREPARED", "DELETING", "FAILED", "VERSIONING", "UPDATING"]}, "AgentSummaries": {"type": "list", "member": {"shape": "Agent<PERSON><PERSON><PERSON><PERSON>"}, "max": 10, "min": 0}, "AgentSummary": {"type": "structure", "required": ["agentId", "<PERSON><PERSON><PERSON>", "agentStatus", "updatedAt"], "members": {"agentId": {"shape": "Id", "documentation": "<p>The unique identifier of the agent.</p>"}, "agentName": {"shape": "Name", "documentation": "<p>The name of the agent.</p>"}, "agentStatus": {"shape": "AgentStatus", "documentation": "<p>The status of the agent.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description of the agent.</p>"}, "guardrailConfiguration": {"shape": "GuardrailConfiguration", "documentation": "<p>Details about the guardrail associated with the agent.</p>"}, "latestAgentVersion": {"shape": "Version", "documentation": "<p>The latest version of the agent.</p>"}, "updatedAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the agent was last updated.</p>"}}, "documentation": "<p>Contains details about an agent.</p>"}, "AgentVersion": {"type": "structure", "required": ["agentArn", "agentId", "<PERSON><PERSON><PERSON>", "agentResourceRoleArn", "agentStatus", "createdAt", "idleSessionTTLInSeconds", "updatedAt", "version"], "members": {"agentArn": {"shape": "AgentArn", "documentation": "<p>The Amazon Resource Name (ARN) of the agent that the version belongs to.</p>"}, "agentCollaboration": {"shape": "AgentCollaboration", "documentation": "<p>The agent's collaboration settings.</p>"}, "agentId": {"shape": "Id", "documentation": "<p>The unique identifier of the agent that the version belongs to.</p>"}, "agentName": {"shape": "Name", "documentation": "<p>The name of the agent that the version belongs to.</p>"}, "agentResourceRoleArn": {"shape": "AgentRoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role with permissions to invoke API operations on the agent.</p>"}, "agentStatus": {"shape": "AgentStatus", "documentation": "<p>The status of the agent that the version belongs to.</p>"}, "createdAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the version was created.</p>"}, "customerEncryptionKeyArn": {"shape": "KmsKeyArn", "documentation": "<p>The Amazon Resource Name (ARN) of the KMS key that encrypts the agent.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description of the version.</p>"}, "failureReasons": {"shape": "FailureReasons", "documentation": "<p>A list of reasons that the API operation on the version failed.</p>"}, "foundationModel": {"shape": "ModelIdentifier", "documentation": "<p>The foundation model that the version invokes.</p>"}, "guardrailConfiguration": {"shape": "GuardrailConfiguration", "documentation": "<p>Details about the guardrail associated with the agent.</p>"}, "idleSessionTTLInSeconds": {"shape": "SessionTTL", "documentation": "<p>The number of seconds for which Amazon Bedrock keeps information about a user's conversation with the agent.</p> <p>A user interaction remains active for the amount of time specified. If no conversation occurs during this time, the session expires and Amazon Bedrock deletes any data provided before the timeout.</p>"}, "instruction": {"shape": "Instruction", "documentation": "<p>The instructions provided to the agent.</p>"}, "memoryConfiguration": {"shape": "MemoryConfiguration", "documentation": "<p> Contains details of the memory configuration on the version of the agent. </p>"}, "promptOverrideConfiguration": {"shape": "PromptOverrideConfiguration", "documentation": "<p>Contains configurations to override prompt templates in different parts of an agent sequence. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/advanced-prompts.html\">Advanced prompts</a>.</p>"}, "recommendedActions": {"shape": "RecommendedActions", "documentation": "<p>A list of recommended actions to take for the failed API operation on the version to succeed.</p>"}, "updatedAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the version was last updated.</p>"}, "version": {"shape": "NumericalVersion", "documentation": "<p>The version number.</p>"}}, "documentation": "<p>Contains details about a version of an agent.</p>"}, "AgentVersionSummaries": {"type": "list", "member": {"shape": "AgentVersionSummary"}, "max": 10, "min": 0}, "AgentVersionSummary": {"type": "structure", "required": ["<PERSON><PERSON><PERSON>", "agentStatus", "agentVersion", "createdAt", "updatedAt"], "members": {"agentName": {"shape": "Name", "documentation": "<p>The name of the agent to which the version belongs.</p>"}, "agentStatus": {"shape": "AgentStatus", "documentation": "<p>The status of the agent to which the version belongs.</p>"}, "agentVersion": {"shape": "Version", "documentation": "<p>The version of the agent.</p>"}, "createdAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the version was created.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description of the version of the agent.</p>"}, "guardrailConfiguration": {"shape": "GuardrailConfiguration", "documentation": "<p>Details about the guardrail associated with the agent.</p>"}, "updatedAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the version was last updated.</p>"}}, "documentation": "<p>Contains details about a version of an agent.</p>"}, "AliasInvocationState": {"type": "string", "documentation": "<p>Enum representing the invocation state of an agent alias</p>", "enum": ["ACCEPT_INVOCATIONS", "REJECT_INVOCATIONS"]}, "AnyToolChoice": {"type": "structure", "members": {}, "documentation": "<p>Defines tools, at least one of which must be requested by the model. No text is generated but the results of tool use are sent back to the model to help generate a response. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/tool-use.html\">Use a tool to complete an Amazon Bedrock model response</a>.</p>"}, "AssociateAgentCollaboratorRequest": {"type": "structure", "required": ["agentDescriptor", "agentId", "agentVersion", "collaborationInstruction", "<PERSON><PERSON><PERSON>"], "members": {"agentDescriptor": {"shape": "AgentDescriptor", "documentation": "<p>The alias of the collaborator agent.</p>"}, "agentId": {"shape": "Id", "documentation": "<p>The agent's ID.</p>", "location": "uri", "locationName": "agentId"}, "agentVersion": {"shape": "DraftVersion", "documentation": "<p>An agent version.</p>", "location": "uri", "locationName": "agentVersion"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A client token.</p>", "idempotencyToken": true}, "collaborationInstruction": {"shape": "CollaborationInstruction", "documentation": "<p>Instruction for the collaborator.</p>"}, "collaboratorName": {"shape": "Name", "documentation": "<p>A name for the collaborator.</p>"}, "relayConversationHistory": {"shape": "RelayConversationHistory", "documentation": "<p>A relay conversation history for the collaborator.</p>"}}}, "AssociateAgentCollaboratorResponse": {"type": "structure", "required": ["agentCollaborator"], "members": {"agentCollaborator": {"shape": "AgentCollaborator", "documentation": "<p>Details about the collaborator.</p>"}}}, "AssociateAgentKnowledgeBaseRequest": {"type": "structure", "required": ["agentId", "agentVersion", "description", "knowledgeBaseId"], "members": {"agentId": {"shape": "Id", "documentation": "<p>The unique identifier of the agent with which you want to associate the knowledge base.</p>", "location": "uri", "locationName": "agentId"}, "agentVersion": {"shape": "DraftVersion", "documentation": "<p>The version of the agent with which you want to associate the knowledge base.</p>", "location": "uri", "locationName": "agentVersion"}, "description": {"shape": "Description", "documentation": "<p>A description of what the agent should use the knowledge base for.</p>"}, "knowledgeBaseId": {"shape": "Id", "documentation": "<p>The unique identifier of the knowledge base to associate with the agent.</p>"}, "knowledgeBaseState": {"shape": "KnowledgeBaseState", "documentation": "<p>Specifies whether to use the knowledge base or not when sending an <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_InvokeAgent.html\">InvokeAgent</a> request.</p>"}}}, "AssociateAgentKnowledgeBaseResponse": {"type": "structure", "required": ["agentKnowledgeBase"], "members": {"agentKnowledgeBase": {"shape": "AgentKnowledgeBase", "documentation": "<p>Contains details about the knowledge base that has been associated with the agent.</p>"}}}, "AutoToolChoice": {"type": "structure", "members": {}, "documentation": "<p>Defines tools. The model automatically decides whether to call a tool or to generate text instead. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/tool-use.html\">Use a tool to complete an Amazon Bedrock model response</a>.</p>"}, "AwsDataCatalogTableName": {"type": "string", "max": 200, "min": 1, "pattern": "^.*\\.*$"}, "AwsDataCatalogTableNames": {"type": "list", "member": {"shape": "AwsDataCatalogTableName"}, "max": 1000, "min": 1}, "BasePromptTemplate": {"type": "string", "max": 100000, "min": 1, "sensitive": true}, "BedrockDataAutomationConfiguration": {"type": "structure", "members": {"parsingModality": {"shape": "ParsingModality", "documentation": "<p>Specifies whether to enable parsing of multimodal data, including both text and/or images.</p>"}}, "documentation": "<p>Contains configurations for using Amazon Bedrock Data Automation as the parser for ingesting your data sources.</p>"}, "BedrockEmbeddingModelArn": {"type": "string", "max": 2048, "min": 20, "pattern": "^(arn:aws(-[^:]{1,12})?:(bedrock|sagemaker):[a-z0-9-]{1,20}:([0-9]{12})?:([a-z-]+/)?)?([a-zA-Z0-9.-]{1,63}){0,2}(([:][a-z0-9-]{1,63}){0,2})?(/[a-z0-9]{1,12})?$"}, "BedrockEmbeddingModelConfiguration": {"type": "structure", "members": {"dimensions": {"shape": "Dimensions", "documentation": "<p>The dimensions details for the vector configuration used on the Bedrock embeddings model.</p>"}, "embeddingDataType": {"shape": "EmbeddingDataType", "documentation": "<p>The data type for the vectors when using a model to convert text into vector embeddings. The model must support the specified data type for vector embeddings. Floating-point (float32) is the default data type, and is supported by most models for vector embeddings. See <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/knowledge-base-supported.html\">Supported embeddings models</a> for information on the available models and their vector data types.</p>"}}, "documentation": "<p>The vector configuration details for the Bedrock embeddings model.</p>"}, "BedrockFoundationModelConfiguration": {"type": "structure", "required": ["modelArn"], "members": {"modelArn": {"shape": "BedrockModelArn", "documentation": "<p>The ARN of the foundation model to use for parsing.</p>"}, "parsingModality": {"shape": "ParsingModality", "documentation": "<p>Specifies whether to enable parsing of multimodal data, including both text and/or images.</p>"}, "parsingPrompt": {"shape": "ParsingPrompt", "documentation": "<p>Instructions for interpreting the contents of a document.</p>"}}, "documentation": "<p>Settings for a foundation model used to parse documents for a data source.</p>"}, "BedrockFoundationModelContextEnrichmentConfiguration": {"type": "structure", "required": ["enrichmentStrategyConfiguration", "modelArn"], "members": {"enrichmentStrategyConfiguration": {"shape": "EnrichmentStrategyConfiguration", "documentation": "<p>The enrichment stategy used to provide additional context. For example, Neptune GraphRAG uses Amazon Bedrock foundation models to perform chunk entity extraction.</p>"}, "modelArn": {"shape": "BedrockModelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the model used to create vector embeddings for the knowledge base.</p>"}}, "documentation": "<p>Context enrichment configuration is used to provide additional context to the RAG application using Amazon Bedrock foundation models.</p>"}, "BedrockModelArn": {"type": "string", "max": 2048, "min": 1, "pattern": "^(arn:aws(-[^:]{1,12})?:(bedrock):[a-z0-9-]{1,20}:([0-9]{12})?:([a-z-]+/)?)?([a-zA-Z0-9.-]{1,63}){0,2}(([:][a-z0-9-]{1,63}){0,2})?(/[a-z0-9]{1,12})?$"}, "BedrockRerankingModelArn": {"type": "string", "max": 2048, "min": 1, "pattern": "^(arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}::foundation-model/(.*))?$"}, "Boolean": {"type": "boolean", "box": true}, "BucketOwnerAccountId": {"type": "string", "max": 12, "min": 12, "pattern": "^[0-9]{12}$"}, "ByteContentBlob": {"type": "blob", "max": 5242880, "min": 1, "sensitive": true}, "ByteContentDoc": {"type": "structure", "required": ["data", "mimeType"], "members": {"data": {"shape": "ByteContentBlob", "documentation": "<p>The base64-encoded string of the content.</p>"}, "mimeType": {"shape": "ByteContentDocMimeTypeString", "documentation": "<p>The MIME type of the content. For a list of MIME types, see <a href=\"https://www.iana.org/assignments/media-types/media-types.xhtml\">Media Types</a>. The following MIME types are supported:</p> <ul> <li> <p>text/plain</p> </li> <li> <p>text/html</p> </li> <li> <p>text/csv</p> </li> <li> <p>text/vtt</p> </li> <li> <p>message/rfc822</p> </li> <li> <p>application/xhtml+xml</p> </li> <li> <p>application/pdf</p> </li> <li> <p>application/msword</p> </li> <li> <p>application/vnd.ms-word.document.macroenabled.12</p> </li> <li> <p>application/vnd.ms-word.template.macroenabled.12</p> </li> <li> <p>application/vnd.ms-excel</p> </li> <li> <p>application/vnd.ms-excel.addin.macroenabled.12</p> </li> <li> <p>application/vnd.ms-excel.sheet.macroenabled.12</p> </li> <li> <p>application/vnd.ms-excel.template.macroenabled.12</p> </li> <li> <p>application/vnd.ms-excel.sheet.binary.macroenabled.12</p> </li> <li> <p>application/vnd.ms-spreadsheetml</p> </li> <li> <p>application/vnd.openxmlformats-officedocument.spreadsheetml.sheet</p> </li> <li> <p>application/vnd.openxmlformats-officedocument.spreadsheetml.template</p> </li> <li> <p>application/vnd.openxmlformats-officedocument.wordprocessingml.document</p> </li> <li> <p>application/vnd.openxmlformats-officedocument.wordprocessingml.template</p> </li> </ul>"}}, "documentation": "<p>Contains information about content defined inline in bytes.</p>"}, "ByteContentDocMimeTypeString": {"type": "string", "pattern": "[a-z]{1,20}/.{1,20}"}, "CachePointBlock": {"type": "structure", "required": ["type"], "members": {"type": {"shape": "CachePointType", "documentation": "<p>Indicates that the CachePointBlock is of the default type</p>"}}, "documentation": "<p>Indicates where a cache checkpoint is located. All information before this checkpoint is cached to be accessed on subsequent requests.</p>"}, "CachePointType": {"type": "string", "enum": ["default"]}, "ChatPromptTemplateConfiguration": {"type": "structure", "required": ["messages"], "members": {"inputVariables": {"shape": "PromptInputVariablesList", "documentation": "<p>An array of the variables in the prompt template.</p>"}, "messages": {"shape": "Messages", "documentation": "<p>Contains messages in the chat for the prompt.</p>"}, "system": {"shape": "SystemContentBlocks", "documentation": "<p>Contains system prompts to provide context to the model or to describe how it should behave.</p>"}, "toolConfiguration": {"shape": "ToolConfiguration", "documentation": "<p>Configuration information for the tools that the model can use when generating a response.</p>"}}, "documentation": "<p>Contains configurations to use a prompt in a conversational format. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/prompt-management-create.html\">Create a prompt using Prompt management</a>.</p>", "sensitive": true}, "ChunkingConfiguration": {"type": "structure", "required": ["chunkingStrategy"], "members": {"chunkingStrategy": {"shape": "ChunkingStrategy", "documentation": "<p>Knowledge base can split your source data into chunks. A <i>chunk</i> refers to an excerpt from a data source that is returned when the knowledge base that it belongs to is queried. You have the following options for chunking your data. If you opt for <code>NONE</code>, then you may want to pre-process your files by splitting them up such that each file corresponds to a chunk.</p> <ul> <li> <p> <code>FIXED_SIZE</code> – Amazon Bedrock splits your source data into chunks of the approximate size that you set in the <code>fixedSizeChunkingConfiguration</code>.</p> </li> <li> <p> <code>HIERARCHICAL</code> – Split documents into layers of chunks where the first layer contains large chunks, and the second layer contains smaller chunks derived from the first layer.</p> </li> <li> <p> <code>SEMANTIC</code> – Split documents into chunks based on groups of similar content derived with natural language processing.</p> </li> <li> <p> <code>NONE</code> – Amazon Bedrock treats each file as one chunk. If you choose this option, you may want to pre-process your documents by splitting them into separate files.</p> </li> </ul>"}, "fixedSizeChunkingConfiguration": {"shape": "FixedSizeChunkingConfiguration", "documentation": "<p>Configurations for when you choose fixed-size chunking. If you set the <code>chunkingStrategy</code> as <code>NONE</code>, exclude this field.</p>"}, "hierarchicalChunkingConfiguration": {"shape": "HierarchicalChunkingConfiguration", "documentation": "<p>Settings for hierarchical document chunking for a data source. Hierarchical chunking splits documents into layers of chunks where the first layer contains large chunks, and the second layer contains smaller chunks derived from the first layer.</p>"}, "semanticChunkingConfiguration": {"shape": "SemanticChunkingConfiguration", "documentation": "<p>Settings for semantic document chunking for a data source. Semantic chunking splits a document into into smaller documents based on groups of similar content derived from the text with natural language processing.</p>"}}, "documentation": "<p>Details about how to chunk the documents in the data source. A <i>chunk</i> refers to an excerpt from a data source that is returned when the knowledge base that it belongs to is queried.</p>"}, "ChunkingStrategy": {"type": "string", "enum": ["FIXED_SIZE", "NONE", "HIERARCHICAL", "SEMANTIC"]}, "ClientToken": {"type": "string", "max": 256, "min": 33, "pattern": "^[a-zA-Z0-9](-*[a-zA-Z0-9]){0,256}$"}, "CollaborationInstruction": {"type": "string", "max": 4000, "min": 1, "sensitive": true}, "CollectorFlowNodeConfiguration": {"type": "structure", "members": {}, "documentation": "<p>Defines a collector node in your flow. This node takes an iteration of inputs and consolidates them into an array in the output. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/flows-nodes.html\">Node types in a flow</a> in the Amazon Bedrock User Guide.</p>"}, "ColumnName": {"type": "string", "max": 63, "min": 0, "pattern": "^[a-zA-Z0-9_\\-]+$"}, "ConcurrencyType": {"type": "string", "enum": ["Automatic", "Manual"]}, "ConditionFlowNodeConfiguration": {"type": "structure", "required": ["conditions"], "members": {"conditions": {"shape": "FlowConditions", "documentation": "<p>An array of conditions. Each member contains the name of a condition and an expression that defines the condition.</p>"}}, "documentation": "<p>Defines a condition node in your flow. You can specify conditions that determine which node comes next in the flow. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/flows-nodes.html\"><PERSON><PERSON> types in a flow</a> in the Amazon Bedrock User Guide.</p>"}, "ConflictException": {"type": "structure", "members": {"message": {"shape": "NonBlankString"}}, "documentation": "<p>There was a conflict performing an operation.</p>", "error": {"httpStatusCode": 409, "senderFault": true}, "exception": true}, "ConfluenceAuthType": {"type": "string", "enum": ["BASIC", "OAUTH2_CLIENT_CREDENTIALS"]}, "ConfluenceCrawlerConfiguration": {"type": "structure", "members": {"filterConfiguration": {"shape": "CrawlFilterConfiguration", "documentation": "<p>The configuration of filtering the Confluence content. For example, configuring regular expression patterns to include or exclude certain content.</p>"}}, "documentation": "<p>The configuration of the Confluence content. For example, configuring specific types of Confluence content.</p>"}, "ConfluenceDataSourceConfiguration": {"type": "structure", "required": ["sourceConfiguration"], "members": {"crawlerConfiguration": {"shape": "ConfluenceCrawlerConfiguration", "documentation": "<p>The configuration of the Confluence content. For example, configuring specific types of Confluence content.</p>"}, "sourceConfiguration": {"shape": "ConfluenceSourceConfiguration", "documentation": "<p>The endpoint information to connect to your Confluence data source.</p>"}}, "documentation": "<p>The configuration information to connect to Confluence as your data source.</p>"}, "ConfluenceHostType": {"type": "string", "enum": ["SAAS"]}, "ConfluenceSourceConfiguration": {"type": "structure", "required": ["authType", "credentialsSecretArn", "hostType", "hostUrl"], "members": {"authType": {"shape": "ConfluenceAuthType", "documentation": "<p>The supported authentication type to authenticate and connect to your Confluence instance.</p>"}, "credentialsSecretArn": {"shape": "SecretArn", "documentation": "<p>The Amazon Resource Name of an Secrets Manager secret that stores your authentication credentials for your Confluence instance URL. For more information on the key-value pairs that must be included in your secret, depending on your authentication type, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/confluence-data-source-connector.html#configuration-confluence-connector\">Confluence connection configuration</a>.</p>"}, "hostType": {"shape": "ConfluenceHostType", "documentation": "<p>The supported host type, whether online/cloud or server/on-premises.</p>"}, "hostUrl": {"shape": "HttpsUrl", "documentation": "<p>The Confluence host URL or instance URL.</p>"}}, "documentation": "<p>The endpoint information to connect to your Confluence data source.</p>"}, "ContentBlock": {"type": "structure", "members": {"cachePoint": {"shape": "CachePointBlock", "documentation": "<p>Creates a cache checkpoint within a message.</p>"}, "text": {"shape": "String", "documentation": "<p>The text in the message.</p>"}}, "documentation": "<p>Contains the content for the message you pass to, or receive from a model. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/prompt-management-create.html\">Create a prompt using Prompt management</a>.</p>", "sensitive": true, "union": true}, "ContentBlocks": {"type": "list", "member": {"shape": "ContentBlock"}}, "ContentDataSourceType": {"type": "string", "enum": ["CUSTOM", "S3"]}, "ContextEnrichmentConfiguration": {"type": "structure", "required": ["type"], "members": {"bedrockFoundationModelConfiguration": {"shape": "BedrockFoundationModelContextEnrichmentConfiguration", "documentation": "<p>The configuration of the Amazon Bedrock foundation model used for context enrichment.</p>"}, "type": {"shape": "ContextEnrichmentType", "documentation": "<p>The method used for context enrichment. It must be Amazon Bedrock foundation models.</p>"}}, "documentation": "<p>Context enrichment configuration is used to provide additional context to the RAG application.</p>"}, "ContextEnrichmentType": {"type": "string", "enum": ["BEDROCK_FOUNDATION_MODEL"]}, "ConversationRole": {"type": "string", "enum": ["user", "assistant"]}, "CrawlFilterConfiguration": {"type": "structure", "required": ["type"], "members": {"patternObjectFilter": {"shape": "PatternObjectFilterConfiguration", "documentation": "<p>The configuration of filtering certain objects or content types of the data source.</p>"}, "type": {"shape": "CrawlFilterConfigurationType", "documentation": "<p>The type of filtering that you want to apply to certain objects or content of the data source. For example, the <code>PATTERN</code> type is regular expression patterns you can apply to filter your content.</p>"}}, "documentation": "<p>The configuration of filtering the data source content. For example, configuring regular expression patterns to include or exclude certain content.</p>"}, "CrawlFilterConfigurationType": {"type": "string", "enum": ["PATTERN"]}, "CreateAgentActionGroupRequest": {"type": "structure", "required": ["actionGroupName", "agentId", "agentVersion"], "members": {"actionGroupExecutor": {"shape": "ActionGroupExecutor", "documentation": "<p>The Amazon Resource Name (ARN) of the Lambda function containing the business logic that is carried out upon invoking the action or the custom control method for handling the information elicited from the user.</p>"}, "actionGroupName": {"shape": "Name", "documentation": "<p>The name to give the action group.</p>"}, "actionGroupState": {"shape": "ActionGroupState", "documentation": "<p>Specifies whether the action group is available for the agent to invoke or not when sending an <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_InvokeAgent.html\">InvokeAgent</a> request.</p>"}, "agentId": {"shape": "Id", "documentation": "<p>The unique identifier of the agent for which to create the action group.</p>", "location": "uri", "locationName": "agentId"}, "agentVersion": {"shape": "DraftVersion", "documentation": "<p>The version of the agent for which to create the action group.</p>", "location": "uri", "locationName": "agentVersion"}, "apiSchema": {"shape": "APISchema", "documentation": "<p>Contains either details about the S3 object containing the OpenAPI schema for the action group or the JSON or YAML-formatted payload defining the schema. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/agents-api-schema.html\">Action group OpenAPI schemas</a>.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A unique, case-sensitive identifier to ensure that the API request completes no more than one time. If this token matches a previous request, Amazon Bedrock ignores the request, but does not return an error. For more information, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/APIReference/Run_Instance_Idempotency.html\">Ensuring idempotency</a>.</p>", "idempotencyToken": true}, "description": {"shape": "Description", "documentation": "<p>A description of the action group.</p>"}, "functionSchema": {"shape": "FunctionSchema", "documentation": "<p>Contains details about the function schema for the action group or the JSON or YAML-formatted payload defining the schema.</p>"}, "parentActionGroupSignature": {"shape": "ActionGroupSignature", "documentation": "<p>Specify a built-in or computer use action for this action group. If you specify a value, you must leave the <code>description</code>, <code>apiSchema</code>, and <code>actionGroupExecutor</code> fields empty for this action group. </p> <ul> <li> <p>To allow your agent to request the user for additional information when trying to complete a task, set this field to <code>AMAZON.UserInput</code>. </p> </li> <li> <p>To allow your agent to generate, run, and troubleshoot code when trying to complete a task, set this field to <code>AMAZON.CodeInterpreter</code>.</p> </li> <li> <p>To allow your agent to use an Anthropic computer use tool, specify one of the following values. </p> <important> <p> Computer use is a new Anthropic Claude model capability (in beta) available with Anthropic Claude 3.7 Sonnet and Claude 3.5 Sonnet v2 only. When operating computer use functionality, we recommend taking additional security precautions, such as executing computer actions in virtual environments with restricted data access and limited internet connectivity. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/agents-computer-use.html\">Configure an Amazon Bedrock Agent to complete tasks with computer use tools</a>. </p> </important> <ul> <li> <p> <code>ANTHROPIC.Computer</code> - Gives the agent permission to use the mouse and keyboard and take screenshots.</p> </li> <li> <p> <code>ANTHROPIC.TextEditor</code> - Gives the agent permission to view, create and edit files.</p> </li> <li> <p> <code>ANTHROPIC.Bash</code> - Gives the agent permission to run commands in a bash shell.</p> </li> </ul> </li> </ul>"}, "parentActionGroupSignatureParams": {"shape": "ActionGroupSignatureParams", "documentation": "<p>The configuration settings for a computer use action.</p> <important> <p> Computer use is a new Anthropic Claude model capability (in beta) available with Anthropic Claude 3.7 Sonnet and Claude 3.5 Sonnet v2 only. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/agents-computer-use.html\">Configure an Amazon Bedrock Agent to complete tasks with computer use tools</a>. </p> </important>"}}}, "CreateAgentActionGroupResponse": {"type": "structure", "required": ["agentActionGroup"], "members": {"agentActionGroup": {"shape": "AgentActionGroup", "documentation": "<p>Contains details about the action group that was created.</p>"}}}, "CreateAgentAliasRequest": {"type": "structure", "required": ["agentAliasName", "agentId"], "members": {"agentAliasName": {"shape": "Name", "documentation": "<p>The name of the alias.</p>"}, "agentId": {"shape": "Id", "documentation": "<p>The unique identifier of the agent.</p>", "location": "uri", "locationName": "agentId"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A unique, case-sensitive identifier to ensure that the API request completes no more than one time. If this token matches a previous request, Amazon Bedrock ignores the request, but does not return an error. For more information, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/APIReference/Run_Instance_Idempotency.html\">Ensuring idempotency</a>.</p>", "idempotencyToken": true}, "description": {"shape": "Description", "documentation": "<p>A description of the alias of the agent.</p>"}, "routingConfiguration": {"shape": "AgentAliasRoutingConfiguration", "documentation": "<p>Contains details about the routing configuration of the alias.</p>"}, "tags": {"shape": "TagsMap", "documentation": "<p>Any tags that you want to attach to the alias of the agent.</p>"}}}, "CreateAgentAliasResponse": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON>"], "members": {"agentAlias": {"shape": "Agent<PERSON><PERSON><PERSON>", "documentation": "<p>Contains details about the alias that was created.</p>"}}}, "CreateAgentRequest": {"type": "structure", "required": ["<PERSON><PERSON><PERSON>"], "members": {"agentCollaboration": {"shape": "AgentCollaboration", "documentation": "<p>The agent's collaboration role.</p>"}, "agentName": {"shape": "Name", "documentation": "<p>A name for the agent that you create.</p>"}, "agentResourceRoleArn": {"shape": "AgentRoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role with permissions to invoke API operations on the agent.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A unique, case-sensitive identifier to ensure that the API request completes no more than one time. If this token matches a previous request, Amazon Bedrock ignores the request, but does not return an error. For more information, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/APIReference/Run_Instance_Idempotency.html\">Ensuring idempotency</a>.</p>", "idempotencyToken": true}, "customOrchestration": {"shape": "CustomOrchestration", "documentation": "<p> Contains details of the custom orchestration configured for the agent. </p>"}, "customerEncryptionKeyArn": {"shape": "KmsKeyArn", "documentation": "<p>The Amazon Resource Name (ARN) of the KMS key with which to encrypt the agent.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description of the agent.</p>"}, "foundationModel": {"shape": "ModelIdentifier", "documentation": "<p>The identifier for the model that you want to be used for orchestration by the agent you create.</p> <p>The <code>modelId</code> to provide depends on the type of model or throughput that you use:</p> <ul> <li> <p>If you use a base model, specify the model ID or its ARN. For a list of model IDs for base models, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/model-ids.html#model-ids-arns\">Amazon Bedrock base model IDs (on-demand throughput)</a> in the Amazon Bedrock User Guide.</p> </li> <li> <p>If you use an inference profile, specify the inference profile ID or its ARN. For a list of inference profile IDs, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/cross-region-inference-support.html\">Supported Regions and models for cross-region inference</a> in the Amazon Bedrock User Guide.</p> </li> <li> <p>If you use a provisioned model, specify the ARN of the Provisioned Throughput. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/prov-thru-use.html\">Run inference using a Provisioned Throughput</a> in the Amazon Bedrock User Guide.</p> </li> <li> <p>If you use a custom model, first purchase Provisioned Throughput for it. Then specify the ARN of the resulting provisioned model. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/model-customization-use.html\">Use a custom model in Amazon Bedrock</a> in the Amazon Bedrock User Guide.</p> </li> <li> <p>If you use an <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/model-customization-import-model.html\">imported model</a>, specify the ARN of the imported model. You can get the model ARN from a successful call to <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_CreateModelImportJob.html\">CreateModelImportJob</a> or from the Imported models page in the Amazon Bedrock console.</p> </li> </ul>"}, "guardrailConfiguration": {"shape": "GuardrailConfiguration", "documentation": "<p>The unique Guardrail configuration assigned to the agent when it is created.</p>"}, "idleSessionTTLInSeconds": {"shape": "SessionTTL", "documentation": "<p>The number of seconds for which Amazon Bedrock keeps information about a user's conversation with the agent.</p> <p>A user interaction remains active for the amount of time specified. If no conversation occurs during this time, the session expires and Amazon Bedrock deletes any data provided before the timeout.</p>"}, "instruction": {"shape": "Instruction", "documentation": "<p>Instructions that tell the agent what it should do and how it should interact with users.</p>"}, "memoryConfiguration": {"shape": "MemoryConfiguration", "documentation": "<p> Contains the details of the memory configured for the agent.</p>"}, "orchestrationType": {"shape": "OrchestrationType", "documentation": "<p> Specifies the type of orchestration strategy for the agent. This is set to <code>DEFAULT</code> orchestration type, by default. </p>"}, "promptOverrideConfiguration": {"shape": "PromptOverrideConfiguration", "documentation": "<p>Contains configurations to override prompts in different parts of an agent sequence. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/advanced-prompts.html\">Advanced prompts</a>.</p>"}, "tags": {"shape": "TagsMap", "documentation": "<p>Any tags that you want to attach to the agent.</p>"}}}, "CreateAgentResponse": {"type": "structure", "required": ["agent"], "members": {"agent": {"shape": "Agent", "documentation": "<p>Contains details about the agent created.</p>"}}}, "CreateDataSourceRequest": {"type": "structure", "required": ["dataSourceConfiguration", "knowledgeBaseId", "name"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>A unique, case-sensitive identifier to ensure that the API request completes no more than one time. If this token matches a previous request, Amazon Bedrock ignores the request, but does not return an error. For more information, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/APIReference/Run_Instance_Idempotency.html\">Ensuring idempotency</a>.</p>", "idempotencyToken": true}, "dataDeletionPolicy": {"shape": "DataDeletionPolicy", "documentation": "<p>The data deletion policy for the data source.</p> <p>You can set the data deletion policy to:</p> <ul> <li> <p>DELETE: Deletes all data from your data source that’s converted into vector embeddings upon deletion of a knowledge base or data source resource. Note that the <b>vector store itself is not deleted</b>, only the data. This flag is ignored if an Amazon Web Services account is deleted.</p> </li> <li> <p>RETAIN: Retains all data from your data source that’s converted into vector embeddings upon deletion of a knowledge base or data source resource. Note that the <b>vector store itself is not deleted</b> if you delete a knowledge base or data source resource.</p> </li> </ul>"}, "dataSourceConfiguration": {"shape": "DataSourceConfiguration", "documentation": "<p>The connection configuration for the data source.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description of the data source.</p>"}, "knowledgeBaseId": {"shape": "Id", "documentation": "<p>The unique identifier of the knowledge base to which to add the data source.</p>", "location": "uri", "locationName": "knowledgeBaseId"}, "name": {"shape": "Name", "documentation": "<p>The name of the data source.</p>"}, "serverSideEncryptionConfiguration": {"shape": "ServerSideEncryptionConfiguration", "documentation": "<p>Contains details about the server-side encryption for the data source.</p>"}, "vectorIngestionConfiguration": {"shape": "VectorIngestionConfiguration", "documentation": "<p>Contains details about how to ingest the documents in the data source.</p>"}}}, "CreateDataSourceResponse": {"type": "structure", "required": ["dataSource"], "members": {"dataSource": {"shape": "DataSource", "documentation": "<p>Contains details about the data source.</p>"}}}, "CreateFlowAliasRequest": {"type": "structure", "required": ["flowIdentifier", "name", "routingConfiguration"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>A unique, case-sensitive identifier to ensure that the API request completes no more than one time. If this token matches a previous request, Amazon Bedrock ignores the request, but does not return an error. For more information, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/APIReference/Run_Instance_Idempotency.html\">Ensuring idempotency</a>.</p>", "idempotencyToken": true}, "concurrencyConfiguration": {"shape": "FlowAliasConcurrencyConfiguration", "documentation": "<p>The configuration that specifies how nodes in the flow are executed in parallel.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description for the alias.</p>"}, "flowIdentifier": {"shape": "FlowIdentifier", "documentation": "<p>The unique identifier of the flow for which to create an alias.</p>", "location": "uri", "locationName": "flowIdentifier"}, "name": {"shape": "Name", "documentation": "<p>A name for the alias.</p>"}, "routingConfiguration": {"shape": "FlowAliasRoutingConfiguration", "documentation": "<p>Contains information about the version to which to map the alias.</p>"}, "tags": {"shape": "TagsMap", "documentation": "<p>Any tags that you want to attach to the alias of the flow. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/tagging.html\">Tagging resources in Amazon Bedrock</a>.</p>"}}}, "CreateFlowAliasResponse": {"type": "structure", "required": ["arn", "createdAt", "flowId", "id", "name", "routingConfiguration", "updatedAt"], "members": {"arn": {"shape": "FlowAliasArn", "documentation": "<p>The Amazon Resource Name (ARN) of the alias.</p>"}, "concurrencyConfiguration": {"shape": "FlowAliasConcurrencyConfiguration", "documentation": "<p>The configuration that specifies how nodes in the flow are executed in parallel.</p>"}, "createdAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the alias was created.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description of the alias.</p>"}, "flowId": {"shape": "FlowId", "documentation": "<p>The unique identifier of the flow that the alias belongs to.</p>"}, "id": {"shape": "FlowAliasId", "documentation": "<p>The unique identifier of the alias.</p>"}, "name": {"shape": "Name", "documentation": "<p>The name of the alias.</p>"}, "routingConfiguration": {"shape": "FlowAliasRoutingConfiguration", "documentation": "<p>Contains information about the version that the alias is mapped to.</p>"}, "updatedAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the alias of the flow was last updated.</p>"}}}, "CreateFlowRequest": {"type": "structure", "required": ["executionRoleArn", "name"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>A unique, case-sensitive identifier to ensure that the API request completes no more than one time. If this token matches a previous request, Amazon Bedrock ignores the request, but does not return an error. For more information, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/APIReference/Run_Instance_Idempotency.html\">Ensuring idempotency</a>.</p>", "idempotencyToken": true}, "customerEncryptionKeyArn": {"shape": "KmsKeyArn", "documentation": "<p>The Amazon Resource Name (ARN) of the KMS key to encrypt the flow.</p>"}, "definition": {"shape": "FlowDefinition", "documentation": "<p>A definition of the nodes and connections between nodes in the flow.</p>"}, "description": {"shape": "FlowDescription", "documentation": "<p>A description for the flow.</p>"}, "executionRoleArn": {"shape": "FlowExecutionRoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the service role with permissions to create and manage a flow. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/flows-permissions.html\">Create a service role for flows in Amazon Bedrock</a> in the Amazon Bedrock User Guide.</p>"}, "name": {"shape": "FlowName", "documentation": "<p>A name for the flow.</p>"}, "tags": {"shape": "TagsMap", "documentation": "<p>Any tags that you want to attach to the flow. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/tagging.html\">Tagging resources in Amazon Bedrock</a>.</p>"}}}, "CreateFlowResponse": {"type": "structure", "required": ["arn", "createdAt", "executionRoleArn", "id", "name", "status", "updatedAt", "version"], "members": {"arn": {"shape": "FlowArn", "documentation": "<p>The Amazon Resource Name (ARN) of the flow.</p>"}, "createdAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the flow was created.</p>"}, "customerEncryptionKeyArn": {"shape": "KmsKeyArn", "documentation": "<p>The Amazon Resource Name (ARN) of the KMS key that you encrypted the flow with.</p>"}, "definition": {"shape": "FlowDefinition", "documentation": "<p>A definition of the nodes and connections between nodes in the flow.</p>"}, "description": {"shape": "FlowDescription", "documentation": "<p>The description of the flow.</p>"}, "executionRoleArn": {"shape": "FlowExecutionRoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the service role with permissions to create a flow. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/flows-permissions.html\">Create a service role for flows in Amazon Bedrock</a> in the Amazon Bedrock User Guide.</p>"}, "id": {"shape": "FlowId", "documentation": "<p>The unique identifier of the flow.</p>"}, "name": {"shape": "FlowName", "documentation": "<p>The name of the flow.</p>"}, "status": {"shape": "FlowStatus", "documentation": "<p>The status of the flow. When you submit this request, the status will be <code>NotPrepared</code>. If creation fails, the status becomes <code>Failed</code>.</p>"}, "updatedAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the flow was last updated.</p>"}, "version": {"shape": "DraftVersion", "documentation": "<p>The version of the flow. When you create a flow, the version created is the <code>DRAFT</code> version.</p>"}}}, "CreateFlowVersionRequest": {"type": "structure", "required": ["flowIdentifier"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>A unique, case-sensitive identifier to ensure that the API request completes no more than one time. If this token matches a previous request, Amazon Bedrock ignores the request, but does not return an error. For more information, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/APIReference/Run_Instance_Idempotency.html\">Ensuring idempotency</a>.</p>", "idempotencyToken": true}, "description": {"shape": "FlowDescription", "documentation": "<p>A description of the version of the flow.</p>"}, "flowIdentifier": {"shape": "FlowIdentifier", "documentation": "<p>The unique identifier of the flow that you want to create a version of.</p>", "location": "uri", "locationName": "flowIdentifier"}}}, "CreateFlowVersionResponse": {"type": "structure", "required": ["arn", "createdAt", "executionRoleArn", "id", "name", "status", "version"], "members": {"arn": {"shape": "FlowArn", "documentation": "<p>The Amazon Resource Name (ARN) of the flow.</p>"}, "createdAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the flow was created.</p>"}, "customerEncryptionKeyArn": {"shape": "KmsKeyArn", "documentation": "<p>The KMS key that the flow is encrypted with.</p>"}, "definition": {"shape": "FlowDefinition", "documentation": "<p>A definition of the nodes and connections in the flow.</p>"}, "description": {"shape": "FlowDescription", "documentation": "<p>The description of the version.</p>"}, "executionRoleArn": {"shape": "FlowExecutionRoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the service role with permissions to create a flow. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/flows-permissions.html\">Create a service role for flows in Amazon Bedrock</a> in the Amazon Bedrock User Guide.</p>"}, "id": {"shape": "FlowId", "documentation": "<p>The unique identifier of the flow.</p>"}, "name": {"shape": "FlowName", "documentation": "<p>The name of the version.</p>"}, "status": {"shape": "FlowStatus", "documentation": "<p>The status of the flow.</p>"}, "version": {"shape": "NumericalVersion", "documentation": "<p>The version of the flow that was created. Versions are numbered incrementally, starting from 1.</p>"}}}, "CreateKnowledgeBaseRequest": {"type": "structure", "required": ["knowledgeBaseConfiguration", "name", "roleArn"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>A unique, case-sensitive identifier to ensure that the API request completes no more than one time. If this token matches a previous request, Amazon Bedrock ignores the request, but does not return an error. For more information, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/APIReference/Run_Instance_Idempotency.html\">Ensuring idempotency</a>.</p>", "idempotencyToken": true}, "description": {"shape": "Description", "documentation": "<p>A description of the knowledge base.</p>"}, "knowledgeBaseConfiguration": {"shape": "KnowledgeBaseConfiguration", "documentation": "<p>Contains details about the embeddings model used for the knowledge base.</p>"}, "name": {"shape": "Name", "documentation": "<p>A name for the knowledge base.</p>"}, "roleArn": {"shape": "KnowledgeBaseRoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role with permissions to invoke API operations on the knowledge base.</p>"}, "storageConfiguration": {"shape": "StorageConfiguration", "documentation": "<p>Contains details about the configuration of the vector database used for the knowledge base.</p>"}, "tags": {"shape": "TagsMap", "documentation": "<p>Specify the key-value pairs for the tags that you want to attach to your knowledge base in this object.</p>"}}}, "CreateKnowledgeBaseResponse": {"type": "structure", "required": ["knowledgeBase"], "members": {"knowledgeBase": {"shape": "KnowledgeBase", "documentation": "<p>Contains details about the knowledge base.</p>"}}}, "CreatePromptRequest": {"type": "structure", "required": ["name"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>A unique, case-sensitive identifier to ensure that the API request completes no more than one time. If this token matches a previous request, Amazon Bedrock ignores the request, but does not return an error. For more information, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/APIReference/Run_Instance_Idempotency.html\">Ensuring idempotency</a>.</p>", "idempotencyToken": true}, "customerEncryptionKeyArn": {"shape": "KmsKeyArn", "documentation": "<p>The Amazon Resource Name (ARN) of the KMS key to encrypt the prompt.</p>"}, "defaultVariant": {"shape": "PromptVariantName", "documentation": "<p>The name of the default variant for the prompt. This value must match the <code>name</code> field in the relevant <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent_PromptVariant.html\">PromptVariant</a> object.</p>"}, "description": {"shape": "PromptDescription", "documentation": "<p>A description for the prompt.</p>"}, "name": {"shape": "PromptName", "documentation": "<p>A name for the prompt.</p>"}, "tags": {"shape": "TagsMap", "documentation": "<p>Any tags that you want to attach to the prompt. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/tagging.html\">Tagging resources in Amazon Bedrock</a>.</p>"}, "variants": {"shape": "PromptVariantList", "documentation": "<p>A list of objects, each containing details about a variant of the prompt.</p>"}}}, "CreatePromptResponse": {"type": "structure", "required": ["arn", "createdAt", "id", "name", "updatedAt", "version"], "members": {"arn": {"shape": "PromptArn", "documentation": "<p>The Amazon Resource Name (ARN) of the prompt.</p>"}, "createdAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the prompt was created.</p>"}, "customerEncryptionKeyArn": {"shape": "KmsKeyArn", "documentation": "<p>The Amazon Resource Name (ARN) of the KMS key that you encrypted the prompt with.</p>"}, "defaultVariant": {"shape": "PromptVariantName", "documentation": "<p>The name of the default variant for your prompt.</p>"}, "description": {"shape": "PromptDescription", "documentation": "<p>The description of the prompt.</p>"}, "id": {"shape": "PromptId", "documentation": "<p>The unique identifier of the prompt.</p>"}, "name": {"shape": "PromptName", "documentation": "<p>The name of the prompt.</p>"}, "updatedAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the prompt was last updated.</p>"}, "variants": {"shape": "PromptVariantList", "documentation": "<p>A list of objects, each containing details about a variant of the prompt.</p>"}, "version": {"shape": "Version", "documentation": "<p>The version of the prompt. When you create a prompt, the version created is the <code>DRAFT</code> version.</p>"}}}, "CreatePromptVersionRequest": {"type": "structure", "required": ["promptIdentifier"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>A unique, case-sensitive identifier to ensure that the API request completes no more than one time. If this token matches a previous request, Amazon Bedrock ignores the request, but does not return an error. For more information, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/APIReference/Run_Instance_Idempotency.html\">Ensuring idempotency</a>.</p>", "idempotencyToken": true}, "description": {"shape": "PromptDescription", "documentation": "<p>A description for the version of the prompt.</p>"}, "promptIdentifier": {"shape": "PromptIdentifier", "documentation": "<p>The unique identifier of the prompt that you want to create a version of.</p>", "location": "uri", "locationName": "promptIdentifier"}, "tags": {"shape": "TagsMap", "documentation": "<p>Any tags that you want to attach to the version of the prompt. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/tagging.html\">Tagging resources in Amazon Bedrock</a>.</p>"}}}, "CreatePromptVersionResponse": {"type": "structure", "required": ["arn", "createdAt", "id", "name", "updatedAt", "version"], "members": {"arn": {"shape": "PromptArn", "documentation": "<p>The Amazon Resource Name (ARN) of the version of the prompt.</p>"}, "createdAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the prompt was created.</p>"}, "customerEncryptionKeyArn": {"shape": "KmsKeyArn", "documentation": "<p>The Amazon Resource Name (ARN) of the KMS key to encrypt the version of the prompt.</p>"}, "defaultVariant": {"shape": "PromptVariantName", "documentation": "<p>The name of the default variant for the prompt. This value must match the <code>name</code> field in the relevant <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent_PromptVariant.html\">PromptVariant</a> object.</p>"}, "description": {"shape": "PromptDescription", "documentation": "<p>A description for the version.</p>"}, "id": {"shape": "PromptId", "documentation": "<p>The unique identifier of the prompt.</p>"}, "name": {"shape": "PromptName", "documentation": "<p>The name of the prompt.</p>"}, "updatedAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the prompt was last updated.</p>"}, "variants": {"shape": "PromptVariantList", "documentation": "<p>A list of objects, each containing details about a variant of the prompt.</p>"}, "version": {"shape": "Version", "documentation": "<p>The version of the prompt that was created. Versions are numbered incrementally, starting from 1.</p>"}}}, "CreationMode": {"type": "string", "enum": ["DEFAULT", "OVERRIDDEN"]}, "CuratedQueries": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "max": 10, "min": 0}, "CuratedQuery": {"type": "structure", "required": ["naturalLanguage", "sql"], "members": {"naturalLanguage": {"shape": "NaturalLanguageString", "documentation": "<p>An example natural language query.</p>"}, "sql": {"shape": "SqlString", "documentation": "<p>The SQL equivalent of the natural language query.</p>"}}, "documentation": "<p>Contains configurations for a query, each of which defines information about example queries to help the query engine generate appropriate SQL queries.</p>"}, "CustomContent": {"type": "structure", "required": ["customDocumentIdentifier", "sourceType"], "members": {"customDocumentIdentifier": {"shape": "CustomDocumentIdentifier", "documentation": "<p>A unique identifier for the document.</p>"}, "inlineContent": {"shape": "InlineContent", "documentation": "<p>Contains information about content defined inline to ingest into a knowledge base.</p>"}, "s3Location": {"shape": "CustomS3Location", "documentation": "<p>Contains information about the Amazon S3 location of the file from which to ingest data.</p>"}, "sourceType": {"shape": "CustomSourceType", "documentation": "<p>The source of the data to ingest.</p>"}}, "documentation": "<p>Contains information about the content to ingest into a knowledge base connected to a custom data source. Choose a <code>sourceType</code> and include the field that corresponds to it.</p>"}, "CustomControlMethod": {"type": "string", "enum": ["RETURN_CONTROL"]}, "CustomDocumentIdentifier": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "CustomDocumentIdentifierIdString", "documentation": "<p>The identifier of the document to ingest into a custom data source.</p>"}}, "documentation": "<p>Contains information about the identifier of the document to ingest into a custom data source.</p>"}, "CustomDocumentIdentifierIdString": {"type": "string", "max": 2048, "min": 1}, "CustomOrchestration": {"type": "structure", "members": {"executor": {"shape": "OrchestrationExecutor", "documentation": "<p> The structure of the executor invoking the actions in custom orchestration. </p>"}}, "documentation": "<p> Details of custom orchestration. </p>"}, "CustomS3Location": {"type": "structure", "required": ["uri"], "members": {"bucketOwnerAccountId": {"shape": "BucketOwnerAccountId", "documentation": "<p>The identifier of the Amazon Web Services account that owns the S3 bucket containing the content to ingest.</p>"}, "uri": {"shape": "S3ObjectUri", "documentation": "<p>The S3 URI of the file containing the content to ingest.</p>"}}, "documentation": "<p>Contains information about the Amazon S3 location of the file containing the content to ingest into a knowledge base connected to a custom data source.</p>"}, "CustomSourceType": {"type": "string", "enum": ["IN_LINE", "S3_LOCATION"]}, "CustomTransformationConfiguration": {"type": "structure", "required": ["intermediateStorage", "transformations"], "members": {"intermediateStorage": {"shape": "IntermediateStorage", "documentation": "<p>An S3 bucket path for input and output objects.</p>"}, "transformations": {"shape": "Transformations", "documentation": "<p>A Lambda function that processes documents.</p>"}}, "documentation": "<p>Settings for customizing steps in the data source content ingestion pipeline.</p> <p>You can configure the data source to process documents with a Lambda function after they are parsed and converted into chunks. When you add a post-chunking transformation, the service stores chunked documents in an S3 bucket and invokes a Lambda function to process them.</p> <p>To process chunked documents with a Lambda function, define an S3 bucket path for input and output objects, and a transformation that specifies the Lambda function to invoke. You can use the Lambda function to customize how chunks are split, and the metadata for each chunk. </p>"}, "CyclicConnectionFlowValidationDetails": {"type": "structure", "required": ["connection"], "members": {"connection": {"shape": "FlowConnectionName", "documentation": "<p>The name of the connection that causes the cycle in the flow.</p>"}}, "documentation": "<p>Details about a cyclic connection detected in the flow.</p>"}, "Data": {"type": "string", "max": 5242880, "min": 1, "sensitive": true}, "DataDeletionPolicy": {"type": "string", "enum": ["RETAIN", "DELETE"]}, "DataSource": {"type": "structure", "required": ["createdAt", "dataSourceConfiguration", "dataSourceId", "knowledgeBaseId", "name", "status", "updatedAt"], "members": {"createdAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the data source was created.</p>"}, "dataDeletionPolicy": {"shape": "DataDeletionPolicy", "documentation": "<p>The data deletion policy for the data source.</p>"}, "dataSourceConfiguration": {"shape": "DataSourceConfiguration", "documentation": "<p>The connection configuration for the data source.</p>"}, "dataSourceId": {"shape": "Id", "documentation": "<p>The unique identifier of the data source.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description of the data source.</p>"}, "failureReasons": {"shape": "FailureReasons", "documentation": "<p>The detailed reasons on the failure to delete a data source.</p>"}, "knowledgeBaseId": {"shape": "Id", "documentation": "<p>The unique identifier of the knowledge base to which the data source belongs.</p>"}, "name": {"shape": "Name", "documentation": "<p>The name of the data source.</p>"}, "serverSideEncryptionConfiguration": {"shape": "ServerSideEncryptionConfiguration", "documentation": "<p>Contains details about the configuration of the server-side encryption.</p>"}, "status": {"shape": "DataSourceStatus", "documentation": "<p>The status of the data source. The following statuses are possible:</p> <ul> <li> <p>Available – The data source has been created and is ready for ingestion into the knowledge base.</p> </li> <li> <p>Deleting – The data source is being deleted.</p> </li> </ul>"}, "updatedAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the data source was last updated.</p>"}, "vectorIngestionConfiguration": {"shape": "VectorIngestionConfiguration", "documentation": "<p>Contains details about how to ingest the documents in the data source.</p>"}}, "documentation": "<p>Contains details about a data source.</p>"}, "DataSourceConfiguration": {"type": "structure", "required": ["type"], "members": {"confluenceConfiguration": {"shape": "ConfluenceDataSourceConfiguration", "documentation": "<p>The configuration information to connect to Confluence as your data source.</p> <note> <p>Confluence data source connector is in preview release and is subject to change.</p> </note>"}, "s3Configuration": {"shape": "S3DataSourceConfiguration", "documentation": "<p>The configuration information to connect to Amazon S3 as your data source.</p>"}, "salesforceConfiguration": {"shape": "SalesforceDataSourceConfiguration", "documentation": "<p>The configuration information to connect to Salesforce as your data source.</p> <note> <p>Salesforce data source connector is in preview release and is subject to change.</p> </note>"}, "sharePointConfiguration": {"shape": "SharePointDataSourceConfiguration", "documentation": "<p>The configuration information to connect to SharePoint as your data source.</p> <note> <p>SharePoint data source connector is in preview release and is subject to change.</p> </note>"}, "type": {"shape": "DataSourceType", "documentation": "<p>The type of data source.</p>"}, "webConfiguration": {"shape": "WebDataSourceConfiguration", "documentation": "<p>The configuration of web URLs to crawl for your data source. You should be authorized to crawl the URLs.</p> <note> <p>Crawling web URLs as your data source is in preview release and is subject to change.</p> </note>"}}, "documentation": "<p>The connection configuration for the data source.</p>"}, "DataSourceStatus": {"type": "string", "enum": ["AVAILABLE", "DELETING", "DELETE_UNSUCCESSFUL"]}, "DataSourceSummaries": {"type": "list", "member": {"shape": "DataSourceSummary"}}, "DataSourceSummary": {"type": "structure", "required": ["dataSourceId", "knowledgeBaseId", "name", "status", "updatedAt"], "members": {"dataSourceId": {"shape": "Id", "documentation": "<p>The unique identifier of the data source.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description of the data source.</p>"}, "knowledgeBaseId": {"shape": "Id", "documentation": "<p>The unique identifier of the knowledge base to which the data source belongs.</p>"}, "name": {"shape": "Name", "documentation": "<p>The name of the data source.</p>"}, "status": {"shape": "DataSourceStatus", "documentation": "<p>The status of the data source.</p>"}, "updatedAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the data source was last updated.</p>"}}, "documentation": "<p>Contains details about a data source.</p>"}, "DataSourceType": {"type": "string", "enum": ["S3", "WEB", "CONFLUENCE", "SALESFORCE", "SHAREPOINT", "CUSTOM", "REDSHIFT_METADATA"]}, "DateTimestamp": {"type": "timestamp", "timestampFormat": "iso8601"}, "DeleteAgentActionGroupRequest": {"type": "structure", "required": ["actionGroupId", "agentId", "agentVersion"], "members": {"actionGroupId": {"shape": "Id", "documentation": "<p>The unique identifier of the action group to delete.</p>", "location": "uri", "locationName": "actionGroupId"}, "agentId": {"shape": "Id", "documentation": "<p>The unique identifier of the agent that the action group belongs to.</p>", "location": "uri", "locationName": "agentId"}, "agentVersion": {"shape": "DraftVersion", "documentation": "<p>The version of the agent that the action group belongs to.</p>", "location": "uri", "locationName": "agentVersion"}, "skipResourceInUseCheck": {"shape": "Boolean", "documentation": "<p>By default, this value is <code>false</code> and deletion is stopped if the resource is in use. If you set it to <code>true</code>, the resource will be deleted even if the resource is in use.</p>", "location": "querystring", "locationName": "skipResourceInUseCheck"}}}, "DeleteAgentActionGroupResponse": {"type": "structure", "members": {}}, "DeleteAgentAliasRequest": {"type": "structure", "required": ["agentAliasId", "agentId"], "members": {"agentAliasId": {"shape": "AgentAliasId", "documentation": "<p>The unique identifier of the alias to delete.</p>", "location": "uri", "locationName": "agentAliasId"}, "agentId": {"shape": "Id", "documentation": "<p>The unique identifier of the agent that the alias belongs to.</p>", "location": "uri", "locationName": "agentId"}}}, "DeleteAgentAliasResponse": {"type": "structure", "required": ["agentAliasId", "agentAliasStatus", "agentId"], "members": {"agentAliasId": {"shape": "AgentAliasId", "documentation": "<p>The unique identifier of the alias that was deleted.</p>"}, "agentAliasStatus": {"shape": "AgentAliasStatus", "documentation": "<p>The status of the alias.</p>"}, "agentId": {"shape": "Id", "documentation": "<p>The unique identifier of the agent that the alias belongs to.</p>"}}}, "DeleteAgentRequest": {"type": "structure", "required": ["agentId"], "members": {"agentId": {"shape": "Id", "documentation": "<p>The unique identifier of the agent to delete.</p>", "location": "uri", "locationName": "agentId"}, "skipResourceInUseCheck": {"shape": "Boolean", "documentation": "<p>By default, this value is <code>false</code> and deletion is stopped if the resource is in use. If you set it to <code>true</code>, the resource will be deleted even if the resource is in use.</p>", "location": "querystring", "locationName": "skipResourceInUseCheck"}}}, "DeleteAgentResponse": {"type": "structure", "required": ["agentId", "agentStatus"], "members": {"agentId": {"shape": "Id", "documentation": "<p>The unique identifier of the agent that was deleted.</p>"}, "agentStatus": {"shape": "AgentStatus", "documentation": "<p>The status of the agent.</p>"}}}, "DeleteAgentVersionRequest": {"type": "structure", "required": ["agentId", "agentVersion"], "members": {"agentId": {"shape": "Id", "documentation": "<p>The unique identifier of the agent that the version belongs to.</p>", "location": "uri", "locationName": "agentId"}, "agentVersion": {"shape": "NumericalVersion", "documentation": "<p>The version of the agent to delete.</p>", "location": "uri", "locationName": "agentVersion"}, "skipResourceInUseCheck": {"shape": "Boolean", "documentation": "<p>By default, this value is <code>false</code> and deletion is stopped if the resource is in use. If you set it to <code>true</code>, the resource will be deleted even if the resource is in use.</p>", "location": "querystring", "locationName": "skipResourceInUseCheck"}}}, "DeleteAgentVersionResponse": {"type": "structure", "required": ["agentId", "agentStatus", "agentVersion"], "members": {"agentId": {"shape": "Id", "documentation": "<p>The unique identifier of the agent that the version belongs to.</p>"}, "agentStatus": {"shape": "AgentStatus", "documentation": "<p>The status of the agent version.</p>"}, "agentVersion": {"shape": "NumericalVersion", "documentation": "<p>The version that was deleted.</p>"}}}, "DeleteDataSourceRequest": {"type": "structure", "required": ["dataSourceId", "knowledgeBaseId"], "members": {"dataSourceId": {"shape": "Id", "documentation": "<p>The unique identifier of the data source to delete.</p>", "location": "uri", "locationName": "dataSourceId"}, "knowledgeBaseId": {"shape": "Id", "documentation": "<p>The unique identifier of the knowledge base from which to delete the data source.</p>", "location": "uri", "locationName": "knowledgeBaseId"}}}, "DeleteDataSourceResponse": {"type": "structure", "required": ["dataSourceId", "knowledgeBaseId", "status"], "members": {"dataSourceId": {"shape": "Id", "documentation": "<p>The unique identifier of the data source that was deleted.</p>"}, "knowledgeBaseId": {"shape": "Id", "documentation": "<p>The unique identifier of the knowledge base to which the data source that was deleted belonged.</p>"}, "status": {"shape": "DataSourceStatus", "documentation": "<p>The status of the data source.</p>"}}}, "DeleteFlowAliasRequest": {"type": "structure", "required": ["aliasIdentifier", "flowIdentifier"], "members": {"aliasIdentifier": {"shape": "FlowAliasIdentifier", "documentation": "<p>The unique identifier of the alias to be deleted.</p>", "location": "uri", "locationName": "aliasIdentifier"}, "flowIdentifier": {"shape": "FlowIdentifier", "documentation": "<p>The unique identifier of the flow that the alias belongs to.</p>", "location": "uri", "locationName": "flowIdentifier"}}}, "DeleteFlowAliasResponse": {"type": "structure", "required": ["flowId", "id"], "members": {"flowId": {"shape": "FlowId", "documentation": "<p>The unique identifier of the flow that the alias belongs to.</p>"}, "id": {"shape": "FlowAliasId", "documentation": "<p>The unique identifier of the flow.</p>"}}}, "DeleteFlowRequest": {"type": "structure", "required": ["flowIdentifier"], "members": {"flowIdentifier": {"shape": "FlowIdentifier", "documentation": "<p>The unique identifier of the flow.</p>", "location": "uri", "locationName": "flowIdentifier"}, "skipResourceInUseCheck": {"shape": "Boolean", "documentation": "<p>By default, this value is <code>false</code> and deletion is stopped if the resource is in use. If you set it to <code>true</code>, the resource will be deleted even if the resource is in use.</p>", "location": "querystring", "locationName": "skipResourceInUseCheck"}}}, "DeleteFlowResponse": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "FlowId", "documentation": "<p>The unique identifier of the flow.</p>"}}}, "DeleteFlowVersionRequest": {"type": "structure", "required": ["flowIdentifier", "flowVersion"], "members": {"flowIdentifier": {"shape": "FlowIdentifier", "documentation": "<p>The unique identifier of the flow whose version that you want to delete</p>", "location": "uri", "locationName": "flowIdentifier"}, "flowVersion": {"shape": "NumericalVersion", "documentation": "<p>The version of the flow that you want to delete.</p>", "location": "uri", "locationName": "flowVersion"}, "skipResourceInUseCheck": {"shape": "Boolean", "documentation": "<p>By default, this value is <code>false</code> and deletion is stopped if the resource is in use. If you set it to <code>true</code>, the resource will be deleted even if the resource is in use.</p>", "location": "querystring", "locationName": "skipResourceInUseCheck"}}}, "DeleteFlowVersionResponse": {"type": "structure", "required": ["id", "version"], "members": {"id": {"shape": "Id", "documentation": "<p>The unique identifier of the flow.</p>"}, "version": {"shape": "NumericalVersion", "documentation": "<p>The version of the flow being deleted.</p>"}}}, "DeleteKnowledgeBaseDocumentsRequest": {"type": "structure", "required": ["dataSourceId", "documentIdentifiers", "knowledgeBaseId"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>A unique, case-sensitive identifier to ensure that the API request completes no more than one time. If this token matches a previous request, Amazon Bedrock ignores the request, but does not return an error. For more information, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/APIReference/Run_Instance_Idempotency.html\">Ensuring idempotency</a>.</p>", "idempotencyToken": true}, "dataSourceId": {"shape": "Id", "documentation": "<p>The unique identifier of the data source that contains the documents.</p>", "location": "uri", "locationName": "dataSourceId"}, "documentIdentifiers": {"shape": "DocumentIdentifiers", "documentation": "<p>A list of objects, each of which contains information to identify a document to delete.</p>"}, "knowledgeBaseId": {"shape": "Id", "documentation": "<p>The unique identifier of the knowledge base that is connected to the data source.</p>", "location": "uri", "locationName": "knowledgeBaseId"}}}, "DeleteKnowledgeBaseDocumentsResponse": {"type": "structure", "members": {"documentDetails": {"shape": "KnowledgeBaseDocumentDetails", "documentation": "<p>A list of objects, each of which contains information about the documents that were deleted.</p>"}}}, "DeleteKnowledgeBaseRequest": {"type": "structure", "required": ["knowledgeBaseId"], "members": {"knowledgeBaseId": {"shape": "Id", "documentation": "<p>The unique identifier of the knowledge base to delete.</p>", "location": "uri", "locationName": "knowledgeBaseId"}}}, "DeleteKnowledgeBaseResponse": {"type": "structure", "required": ["knowledgeBaseId", "status"], "members": {"knowledgeBaseId": {"shape": "Id", "documentation": "<p>The unique identifier of the knowledge base that was deleted.</p>"}, "status": {"shape": "KnowledgeBaseStatus", "documentation": "<p>The status of the knowledge base and whether it has been successfully deleted.</p>"}}}, "DeletePromptRequest": {"type": "structure", "required": ["promptIdentifier"], "members": {"promptIdentifier": {"shape": "PromptIdentifier", "documentation": "<p>The unique identifier of the prompt.</p>", "location": "uri", "locationName": "promptIdentifier"}, "promptVersion": {"shape": "NumericalVersion", "documentation": "<p>The version of the prompt to delete. To delete the prompt, omit this field.</p>", "location": "querystring", "locationName": "promptVersion"}}}, "DeletePromptResponse": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "PromptId", "documentation": "<p>The unique identifier of the prompt that was deleted.</p>"}, "version": {"shape": "NumericalVersion", "documentation": "<p>The version of the prompt that was deleted.</p>"}}}, "Description": {"type": "string", "max": 200, "min": 1}, "DescriptionString": {"type": "string", "max": 200, "min": 1}, "Dimensions": {"type": "integer", "box": true, "max": 4096, "min": 0}, "DisassociateAgentCollaboratorRequest": {"type": "structure", "required": ["agentId", "agentVersion", "collaboratorId"], "members": {"agentId": {"shape": "Id", "documentation": "<p>An agent ID.</p>", "location": "uri", "locationName": "agentId"}, "agentVersion": {"shape": "DraftVersion", "documentation": "<p>The agent's version.</p>", "location": "uri", "locationName": "agentVersion"}, "collaboratorId": {"shape": "Id", "documentation": "<p>The collaborator's ID.</p>", "location": "uri", "locationName": "collaboratorId"}}}, "DisassociateAgentCollaboratorResponse": {"type": "structure", "members": {}}, "DisassociateAgentKnowledgeBaseRequest": {"type": "structure", "required": ["agentId", "agentVersion", "knowledgeBaseId"], "members": {"agentId": {"shape": "Id", "documentation": "<p>The unique identifier of the agent from which to disassociate the knowledge base.</p>", "location": "uri", "locationName": "agentId"}, "agentVersion": {"shape": "DraftVersion", "documentation": "<p>The version of the agent from which to disassociate the knowledge base.</p>", "location": "uri", "locationName": "agentVersion"}, "knowledgeBaseId": {"shape": "Id", "documentation": "<p>The unique identifier of the knowledge base to disassociate.</p>", "location": "uri", "locationName": "knowledgeBaseId"}}}, "DisassociateAgentKnowledgeBaseResponse": {"type": "structure", "members": {}}, "Document": {"type": "structure", "members": {}, "document": true}, "DocumentContent": {"type": "structure", "required": ["dataSourceType"], "members": {"custom": {"shape": "CustomContent", "documentation": "<p>Contains information about the content to ingest into a knowledge base connected to a custom data source.</p>"}, "dataSourceType": {"shape": "ContentDataSourceType", "documentation": "<p>The type of data source that is connected to the knowledge base to which to ingest this document.</p>"}, "s3": {"shape": "S3Content", "documentation": "<p>Contains information about the content to ingest into a knowledge base connected to an Amazon S3 data source</p>"}}, "documentation": "<p>Contains information about the content of a document. Choose a <code>dataSourceType</code> and include the field that corresponds to it.</p>"}, "DocumentIdentifier": {"type": "structure", "required": ["dataSourceType"], "members": {"custom": {"shape": "CustomDocumentIdentifier", "documentation": "<p>Contains information that identifies the document in a custom data source.</p>"}, "dataSourceType": {"shape": "ContentDataSourceType", "documentation": "<p>The type of data source connected to the knowledge base that contains the document.</p>"}, "s3": {"shape": "S3Location", "documentation": "<p>Contains information that identifies the document in an S3 data source.</p>"}}, "documentation": "<p>Contains information that identifies the document.</p>"}, "DocumentIdentifiers": {"type": "list", "member": {"shape": "DocumentIdentifier"}, "max": 10, "min": 1}, "DocumentMetadata": {"type": "structure", "required": ["type"], "members": {"inlineAttributes": {"shape": "DocumentMetadataInlineAttributesList", "documentation": "<p>An array of objects, each of which defines a metadata attribute to associate with the content to ingest. You define the attributes inline.</p>"}, "s3Location": {"shape": "CustomS3Location", "documentation": "<p>The Amazon S3 location of the file containing metadata to associate with the content to ingest.</p>"}, "type": {"shape": "MetadataSourceType", "documentation": "<p>The type of the source source from which to add metadata.</p>"}}, "documentation": "<p>Contains information about the metadata associate with the content to ingest into a knowledge base. Choose a <code>type</code> and include the field that corresponds to it.</p>"}, "DocumentMetadataInlineAttributesList": {"type": "list", "member": {"shape": "MetadataAttribute"}, "max": 50, "min": 1}, "DocumentStatus": {"type": "string", "enum": ["INDEXED", "PARTIALLY_INDEXED", "PENDING", "FAILED", "METADATA_PARTIALLY_INDEXED", "METADATA_UPDATE_FAILED", "IGNORED", "NOT_FOUND", "STARTING", "IN_PROGRESS", "DELETING", "DELETE_IN_PROGRESS"]}, "DraftVersion": {"type": "string", "max": 5, "min": 5, "pattern": "^DRAFT$"}, "DuplicateConditionExpressionFlowValidationDetails": {"type": "structure", "required": ["expression", "node"], "members": {"expression": {"shape": "FlowConditionExpression", "documentation": "<p>The duplicated condition expression.</p>"}, "node": {"shape": "FlowNodeName", "documentation": "<p>The name of the node containing the duplicate condition expressions.</p>"}}, "documentation": "<p>Details about duplicate condition expressions found in a condition node.</p>"}, "DuplicateConnectionsFlowValidationDetails": {"type": "structure", "required": ["source", "target"], "members": {"source": {"shape": "FlowNodeName", "documentation": "<p>The name of the source node where the duplicate connection starts.</p>"}, "target": {"shape": "FlowNodeName", "documentation": "<p>The name of the target node where the duplicate connection ends.</p>"}}, "documentation": "<p>Details about duplicate connections found between two nodes in the flow.</p>"}, "EmbeddingDataType": {"type": "string", "documentation": "<p>Bedrock models embedding data type. Can be either float32 or binary.</p>", "enum": ["FLOAT32", "BINARY"]}, "EmbeddingModelConfiguration": {"type": "structure", "members": {"bedrockEmbeddingModelConfiguration": {"shape": "BedrockEmbeddingModelConfiguration", "documentation": "<p>The vector configuration details on the Bedrock embeddings model.</p>"}}, "documentation": "<p>The configuration details for the embeddings model.</p>"}, "EnabledMemoryTypes": {"type": "list", "member": {"shape": "MemoryType"}, "max": 1, "min": 1}, "EnrichmentStrategyConfiguration": {"type": "structure", "required": ["method"], "members": {"method": {"shape": "EnrichmentStrategyMethod", "documentation": "<p>The method used for the context enrichment strategy.</p>"}}, "documentation": "<p>The strategy used for performing context enrichment.</p>"}, "EnrichmentStrategyMethod": {"type": "string", "enum": ["CHUNK_ENTITY_EXTRACTION"]}, "ErrorMessage": {"type": "string", "max": 2048, "min": 0}, "FailureReason": {"type": "string", "max": 2048, "min": 0}, "FailureReasons": {"type": "list", "member": {"shape": "FailureReason"}, "max": 2048, "min": 0}, "FieldForReranking": {"type": "structure", "required": ["fieldName"], "members": {"fieldName": {"shape": "FieldForRerankingFieldNameString", "documentation": "<p>The name of the metadata field to include or exclude during reranking.</p>"}}, "documentation": "<p>Specifies a metadata field to include or exclude during the reranking process.</p>"}, "FieldForRerankingFieldNameString": {"type": "string", "max": 2000, "min": 1}, "FieldName": {"type": "string", "max": 2048, "min": 0, "pattern": "^.*$"}, "FieldsForReranking": {"type": "list", "member": {"shape": "FieldForReranking"}, "max": 100, "min": 1, "sensitive": true}, "FilterList": {"type": "list", "member": {"shape": "FilterPattern"}, "max": 25, "min": 1, "sensitive": true}, "FilterPattern": {"type": "string", "max": 1000, "min": 1, "sensitive": true}, "FilteredObjectType": {"type": "string", "max": 50, "min": 1, "sensitive": true}, "FixedSizeChunkingConfiguration": {"type": "structure", "required": ["maxTokens", "overlapPercentage"], "members": {"maxTokens": {"shape": "FixedSizeChunkingConfigurationMaxTokensInteger", "documentation": "<p>The maximum number of tokens to include in a chunk.</p>"}, "overlapPercentage": {"shape": "FixedSizeChunkingConfigurationOverlapPercentageInteger", "documentation": "<p>The percentage of overlap between adjacent chunks of a data source.</p>"}}, "documentation": "<p>Configurations for when you choose fixed-size chunking. If you set the <code>chunkingStrategy</code> as <code>NONE</code>, exclude this field.</p>"}, "FixedSizeChunkingConfigurationMaxTokensInteger": {"type": "integer", "box": true, "min": 1}, "FixedSizeChunkingConfigurationOverlapPercentageInteger": {"type": "integer", "box": true, "max": 99, "min": 1}, "FlowAliasArn": {"type": "string", "pattern": "^arn:aws:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:flow/[0-9a-zA-Z]{10}/alias/(TSTALIASID|[0-9a-zA-Z]{10})$"}, "FlowAliasConcurrencyConfiguration": {"type": "structure", "required": ["type"], "members": {"maxConcurrency": {"shape": "FlowAliasConcurrencyConfigurationMaxConcurrencyInteger", "documentation": "<p>The maximum number of nodes that can be executed concurrently in the flow.</p>"}, "type": {"shape": "ConcurrencyType", "documentation": "<p>The type of concurrency to use for parallel node execution. Specify one of the following options:</p> <ul> <li> <p> <code>Automatic</code> - Amazon Bedrock determines which nodes can be executed in parallel based on the flow definition and its dependencies.</p> </li> <li> <p> <code>Manual</code> - You specify which nodes can be executed in parallel.</p> </li> </ul>"}}, "documentation": "<p>Determines how multiple nodes in a flow can run in parallel. Running nodes concurrently can improve your flow's performance.</p>"}, "FlowAliasConcurrencyConfigurationMaxConcurrencyInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "FlowAliasId": {"type": "string", "pattern": "^(TSTALIASID|[0-9a-zA-Z]{10})$"}, "FlowAliasIdentifier": {"type": "string", "pattern": "^(arn:aws:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:flow/[0-9a-zA-Z]{10}/alias/[0-9a-zA-Z]{10})|(TSTALIASID|[0-9a-zA-Z]{10})$"}, "FlowAliasRoutingConfiguration": {"type": "list", "member": {"shape": "FlowAliasRoutingConfigurationListItem"}, "max": 1, "min": 1}, "FlowAliasRoutingConfigurationListItem": {"type": "structure", "members": {"flowVersion": {"shape": "Version", "documentation": "<p>The version that the alias maps to.</p>"}}, "documentation": "<p>Contains information about a version that the alias maps to.</p>"}, "FlowAliasSummaries": {"type": "list", "member": {"shape": "FlowAliasSummary"}, "max": 10, "min": 0}, "FlowAliasSummary": {"type": "structure", "required": ["arn", "createdAt", "flowId", "id", "name", "routingConfiguration", "updatedAt"], "members": {"arn": {"shape": "FlowAliasArn", "documentation": "<p>The Amazon Resource Name (ARN) of the alias.</p>"}, "concurrencyConfiguration": {"shape": "FlowAliasConcurrencyConfiguration", "documentation": "<p>The configuration that specifies how nodes in the flow are executed concurrently.</p>"}, "createdAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the alias was created.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description of the alias.</p>"}, "flowId": {"shape": "FlowId", "documentation": "<p>The unique identifier of the flow.</p>"}, "id": {"shape": "FlowAliasId", "documentation": "<p>The unique identifier of the alias of the flow.</p>"}, "name": {"shape": "Name", "documentation": "<p>The name of the alias.</p>"}, "routingConfiguration": {"shape": "FlowAliasRoutingConfiguration", "documentation": "<p>A list of configurations about the versions that the alias maps to. Currently, you can only specify one.</p>"}, "updatedAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the alias was last updated.</p>"}}, "documentation": "<p>Contains information about an alias of a flow.</p> <p>This data type is used in the following API operations:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent_ListFlowAliases.html#API_agent_ListFlowAliases_ResponseSyntax\">ListFlowAliases response</a> </p> </li> </ul>"}, "FlowArn": {"type": "string", "pattern": "^arn:aws:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:flow/[0-9a-zA-Z]{10}$"}, "FlowCondition": {"type": "structure", "required": ["name"], "members": {"expression": {"shape": "FlowConditionExpression", "documentation": "<p>Defines the condition. You must refer to at least one of the inputs in the condition. For more information, expand the Condition node section in <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/flows-how-it-works.html#flows-nodes\">Node types in prompt flows</a>.</p>"}, "name": {"shape": "FlowConditionName", "documentation": "<p>A name for the condition that you can reference.</p>"}}, "documentation": "<p>Defines a condition in the condition node.</p>"}, "FlowConditionExpression": {"type": "string", "max": 64, "min": 1, "sensitive": true}, "FlowConditionName": {"type": "string", "pattern": "^[a-zA-Z]([_]?[0-9a-zA-Z]){1,50}$"}, "FlowConditionalConnectionConfiguration": {"type": "structure", "required": ["condition"], "members": {"condition": {"shape": "FlowConditionName", "documentation": "<p>The condition that triggers this connection. For more information about how to write conditions, see the <b>Condition</b> node type in the <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/node-types.html\">Node types</a> topic in the Amazon Bedrock User Guide.</p>"}}, "documentation": "<p>The configuration of a connection between a condition node and another node.</p>"}, "FlowConditions": {"type": "list", "member": {"shape": "FlowCondition"}, "max": 5, "min": 1}, "FlowConnection": {"type": "structure", "required": ["name", "source", "target", "type"], "members": {"configuration": {"shape": "FlowConnectionConfiguration", "documentation": "<p>The configuration of the connection.</p>"}, "name": {"shape": "FlowConnectionName", "documentation": "<p>A name for the connection that you can reference.</p>"}, "source": {"shape": "FlowNodeName", "documentation": "<p>The node that the connection starts at.</p>"}, "target": {"shape": "FlowNodeName", "documentation": "<p>The node that the connection ends at.</p>"}, "type": {"shape": "FlowConnectionType", "documentation": "<p>Whether the source node that the connection begins from is a condition node (<code>Conditional</code>) or not (<code>Data</code>).</p>"}}, "documentation": "<p>Contains information about a connection between two nodes in the flow.</p>"}, "FlowConnectionConfiguration": {"type": "structure", "members": {"conditional": {"shape": "FlowConditionalConnectionConfiguration", "documentation": "<p>The configuration of a connection originating from a Condition node.</p>"}, "data": {"shape": "FlowDataConnectionConfiguration", "documentation": "<p>The configuration of a connection originating from a node that isn't a Condition node.</p>"}}, "documentation": "<p>The configuration of the connection.</p>", "union": true}, "FlowConnectionName": {"type": "string", "pattern": "^[a-zA-Z]([_]?[0-9a-zA-Z]){1,100}$"}, "FlowConnectionType": {"type": "string", "enum": ["Data", "Conditional"]}, "FlowConnections": {"type": "list", "member": {"shape": "FlowConnection"}, "max": 20, "min": 0}, "FlowDataConnectionConfiguration": {"type": "structure", "required": ["sourceOutput", "targetInput"], "members": {"sourceOutput": {"shape": "FlowNodeOutputName", "documentation": "<p>The name of the output in the source node that the connection begins from.</p>"}, "targetInput": {"shape": "FlowNodeInputName", "documentation": "<p>The name of the input in the target node that the connection ends at.</p>"}}, "documentation": "<p>The configuration of a connection originating from a node that isn't a Condition node.</p>"}, "FlowDefinition": {"type": "structure", "members": {"connections": {"shape": "FlowConnections", "documentation": "<p>An array of connection definitions in the flow.</p>"}, "nodes": {"shape": "FlowNodes", "documentation": "<p>An array of node definitions in the flow.</p>"}}, "documentation": "<p>The definition of the nodes and connections between nodes in the flow.</p>", "sensitive": true}, "FlowDescription": {"type": "string", "max": 200, "min": 1}, "FlowExecutionRoleArn": {"type": "string", "max": 2048, "min": 0, "pattern": "^arn:aws(-[^:]+)?:iam::([0-9]{12})?:role/(service-role/)?.+$"}, "FlowId": {"type": "string", "pattern": "^[0-9a-zA-Z]{10}$"}, "FlowIdentifier": {"type": "string", "pattern": "^(arn:aws:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:flow/[0-9a-zA-Z]{10})|([0-9a-zA-Z]{10})$"}, "FlowName": {"type": "string", "pattern": "^([0-9a-zA-Z][_-]?){1,100}$"}, "FlowNode": {"type": "structure", "required": ["name", "type"], "members": {"configuration": {"shape": "FlowNodeConfiguration", "documentation": "<p>Contains configurations for the node.</p>"}, "inputs": {"shape": "FlowNodeInputs", "documentation": "<p>An array of objects, each of which contains information about an input into the node.</p>"}, "name": {"shape": "FlowNodeName", "documentation": "<p>A name for the node.</p>"}, "outputs": {"shape": "FlowNodeOutputs", "documentation": "<p>A list of objects, each of which contains information about an output from the node.</p>"}, "type": {"shape": "FlowNodeType", "documentation": "<p>The type of node. This value must match the name of the key that you provide in the configuration you provide in the <code>FlowNodeConfiguration</code> field.</p>"}}, "documentation": "<p>Contains configurations about a node in the flow.</p>"}, "FlowNodeConfiguration": {"type": "structure", "members": {"agent": {"shape": "AgentFlowNodeConfiguration", "documentation": "<p>Contains configurations for an agent node in your flow. Invokes an alias of an agent and returns the response.</p>"}, "collector": {"shape": "CollectorFlowNodeConfiguration", "documentation": "<p>Contains configurations for a collector node in your flow. Collects an iteration of inputs and consolidates them into an array of outputs.</p>"}, "condition": {"shape": "ConditionFlowNodeConfiguration", "documentation": "<p>Contains configurations for a condition node in your flow. Defines conditions that lead to different branches of the flow.</p>"}, "inlineCode": {"shape": "InlineCodeFlowNodeConfiguration", "documentation": "<p>Contains configurations for an inline code node in your flow. Inline code nodes let you write and execute code directly within your flow, enabling data transformations, custom logic, and integrations without needing an external Lambda function.</p>"}, "input": {"shape": "InputFlowNodeConfiguration", "documentation": "<p>Contains configurations for an input flow node in your flow. The first node in the flow. <code>inputs</code> can't be specified for this node.</p>"}, "iterator": {"shape": "IteratorFlowNodeConfiguration", "documentation": "<p>Contains configurations for an iterator node in your flow. Takes an input that is an array and iteratively sends each item of the array as an output to the following node. The size of the array is also returned in the output.</p> <p>The output flow node at the end of the flow iteration will return a response for each member of the array. To return only one response, you can include a collector node downstream from the iterator node.</p>"}, "knowledgeBase": {"shape": "KnowledgeBaseFlowNodeConfiguration", "documentation": "<p>Contains configurations for a knowledge base node in your flow. Queries a knowledge base and returns the retrieved results or generated response.</p>"}, "lambdaFunction": {"shape": "LambdaFunctionFlowNodeConfiguration", "documentation": "<p>Contains configurations for a Lambda function node in your flow. Invokes an Lambda function.</p>"}, "lex": {"shape": "LexFlowNodeConfiguration", "documentation": "<p>Contains configurations for a Lex node in your flow. Invokes an Amazon Lex bot to identify the intent of the input and return the intent as the output.</p>"}, "loop": {"shape": "LoopFlowNodeConfiguration", "documentation": "<p>Contains configurations for a DoWhile loop in your flow.</p>"}, "loopController": {"shape": "LoopControllerFlowNodeConfiguration", "documentation": "<p>Contains controller node configurations for a DoWhile loop in your flow.</p>"}, "loopInput": {"shape": "LoopInputFlowNodeConfiguration", "documentation": "<p>Contains input node configurations for a DoWhile loop in your flow.</p>"}, "output": {"shape": "OutputFlowNodeConfiguration", "documentation": "<p>Contains configurations for an output flow node in your flow. The last node in the flow. <code>outputs</code> can't be specified for this node.</p>"}, "prompt": {"shape": "PromptFlowNodeConfiguration", "documentation": "<p>Contains configurations for a prompt node in your flow. Runs a prompt and generates the model response as the output. You can use a prompt from Prompt management or you can configure one in this node.</p>"}, "retrieval": {"shape": "RetrievalFlowNodeConfiguration", "documentation": "<p>Contains configurations for a retrieval node in your flow. Retrieves data from an Amazon S3 location and returns it as the output.</p>"}, "storage": {"shape": "StorageFlowNodeConfiguration", "documentation": "<p>Contains configurations for a storage node in your flow. Stores an input in an Amazon S3 location.</p>"}}, "documentation": "<p>Contains configurations for a node in your flow. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/flows-nodes.html\">Node types in a flow</a> in the Amazon Bedrock User Guide.</p>", "union": true}, "FlowNodeIODataType": {"type": "string", "enum": ["String", "Number", "Boolean", "Object", "Array"]}, "FlowNodeInput": {"type": "structure", "required": ["expression", "name", "type"], "members": {"category": {"shape": "FlowNodeInputCategory", "documentation": "<p>Specifies how input data flows between iterations in a DoWhile loop.</p> <ul> <li> <p> <code>LoopCondition</code> - Controls whether the loop continues by evaluating condition expressions against the input data. Use this category to define the condition that determines if the loop should continue. </p> </li> <li> <p> <code>ReturnValueToLoopStart</code> - Defines data to pass back to the start of the loop's next iteration. Use this category for variables that you want to update for each loop iteration.</p> </li> <li> <p> <code>ExitLoop</code> - Defines the value that's available once the loop ends. Use this category to expose loop results to nodes outside the loop.</p> </li> </ul>"}, "expression": {"shape": "FlowNodeInputExpression", "documentation": "<p>An expression that formats the input for the node. For an explanation of how to create expressions, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/flows-expressions.html\">Expressions in Prompt flows in Amazon Bedrock</a>.</p>"}, "name": {"shape": "FlowNodeInputName", "documentation": "<p>Specifies a name for the input that you can reference.</p>"}, "type": {"shape": "FlowNodeIODataType", "documentation": "<p>Specifies the data type of the input. If the input doesn't match this type at runtime, a validation error will be thrown.</p>"}}, "documentation": "<p>Contains configurations for an input in an Amazon Bedrock Flows node.</p>"}, "FlowNodeInputCategory": {"type": "string", "enum": ["LoopCondition", "ReturnValueToLoopStart", "ExitLoop"]}, "FlowNodeInputExpression": {"type": "string", "max": 64, "min": 1, "sensitive": true}, "FlowNodeInputName": {"type": "string", "pattern": "^[a-zA-Z]([_]?[0-9a-zA-Z]){1,50}$"}, "FlowNodeInputs": {"type": "list", "member": {"shape": "FlowNodeInput"}, "max": 20, "min": 0}, "FlowNodeName": {"type": "string", "pattern": "^[a-zA-Z]([_]?[0-9a-zA-Z]){1,50}$"}, "FlowNodeOutput": {"type": "structure", "required": ["name", "type"], "members": {"name": {"shape": "FlowNodeOutputName", "documentation": "<p>A name for the output that you can reference.</p>"}, "type": {"shape": "FlowNodeIODataType", "documentation": "<p>The data type of the output. If the output doesn't match this type at runtime, a validation error will be thrown.</p>"}}, "documentation": "<p>Contains configurations for an output from a node.</p>"}, "FlowNodeOutputName": {"type": "string", "pattern": "^[a-zA-Z]([_]?[0-9a-zA-Z]){1,50}$"}, "FlowNodeOutputs": {"type": "list", "member": {"shape": "FlowNodeOutput"}, "max": 5, "min": 0}, "FlowNodeType": {"type": "string", "enum": ["Input", "Output", "KnowledgeBase", "Condition", "Lex", "Prompt", "LambdaFunction", "Storage", "Agent", "Retrieval", "Iterator", "Collector", "InlineCode", "Loop", "LoopInput", "LoopController"]}, "FlowNodes": {"type": "list", "member": {"shape": "FlowNode"}, "max": 40, "min": 0}, "FlowStatus": {"type": "string", "enum": ["Failed", "Prepared", "Preparing", "NotPrepared"]}, "FlowSummaries": {"type": "list", "member": {"shape": "FlowSummary"}, "max": 10, "min": 0}, "FlowSummary": {"type": "structure", "required": ["arn", "createdAt", "id", "name", "status", "updatedAt", "version"], "members": {"arn": {"shape": "FlowArn", "documentation": "<p>The Amazon Resource Name (ARN) of the flow.</p>"}, "createdAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the flow was created.</p>"}, "description": {"shape": "FlowDescription", "documentation": "<p>A description of the flow.</p>"}, "id": {"shape": "FlowId", "documentation": "<p>The unique identifier of the flow.</p>"}, "name": {"shape": "FlowName", "documentation": "<p>The name of the flow.</p>"}, "status": {"shape": "FlowStatus", "documentation": "<p>The status of the flow. The following statuses are possible:</p> <ul> <li> <p>NotPrepared – The flow has been created or updated, but hasn't been prepared. If you just created the flow, you can't test it. If you updated the flow, the <code>DRAFT</code> version won't contain the latest changes for testing. Send a <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent_PrepareFlow.html\">PrepareFlow</a> request to package the latest changes into the <code>DRAFT</code> version.</p> </li> <li> <p>Preparing – The flow is being prepared so that the <code>DRAFT</code> version contains the latest changes for testing.</p> </li> <li> <p>Prepared – The flow is prepared and the <code>DRAFT</code> version contains the latest changes for testing.</p> </li> <li> <p>Failed – The last API operation that you invoked on the flow failed. Send a <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent_GetFlow.html\">GetFlow</a> request and check the error message in the <code>validations</code> field.</p> </li> </ul>"}, "updatedAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the flow was last updated.</p>"}, "version": {"shape": "DraftVersion", "documentation": "<p>The latest version of the flow.</p>"}}, "documentation": "<p>Contains the definition of a flow.</p>"}, "FlowValidation": {"type": "structure", "required": ["message", "severity"], "members": {"details": {"shape": "FlowValidationDetails", "documentation": "<p>Specific details about the validation issue encountered in the flow.</p>"}, "message": {"shape": "NonBlankString", "documentation": "<p>A message describing the validation error.</p>"}, "severity": {"shape": "FlowValidationSeverity", "documentation": "<p>The severity of the issue described in the message.</p>"}, "type": {"shape": "FlowValidationType", "documentation": "<p>The type of validation issue encountered in the flow.</p>"}}, "documentation": "<p>Contains information about validation of the flow.</p> <p>This data type is used in the following API operations:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent_GetFlow.html#API_agent_GetFlow_ResponseSyntax\">GetFlow response</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent_GetFlowVersion.html#API_agent_GetFlowVersion_ResponseSyntax\">GetFlowVersion response</a> </p> </li> </ul>"}, "FlowValidationDetails": {"type": "structure", "members": {"cyclicConnection": {"shape": "CyclicConnectionFlowValidationDetails", "documentation": "<p>Details about a cyclic connection in the flow.</p>"}, "duplicateConditionExpression": {"shape": "DuplicateConditionExpressionFlowValidationDetails", "documentation": "<p>Details about duplicate condition expressions in a node.</p>"}, "duplicateConnections": {"shape": "DuplicateConnectionsFlowValidationDetails", "documentation": "<p>Details about duplicate connections between nodes.</p>"}, "incompatibleConnectionDataType": {"shape": "IncompatibleConnectionDataTypeFlowValidationDetails", "documentation": "<p>Details about incompatible data types in a connection.</p>"}, "invalidLoopBoundary": {"shape": "InvalidLoopBoundaryFlowValidationDetails", "documentation": "<p>Details about a flow that includes connections that violate loop boundary rules.</p>"}, "loopIncompatibleNodeType": {"shape": "LoopIncompatibleNodeTypeFlowValidationDetails", "documentation": "<p>Details about a flow that includes incompatible node types in a DoWhile loop.</p>"}, "malformedConditionExpression": {"shape": "MalformedConditionExpressionFlowValidationDetails", "documentation": "<p>Details about a malformed condition expression in a node.</p>"}, "malformedNodeInputExpression": {"shape": "MalformedNodeInputExpressionFlowValidationDetails", "documentation": "<p>Details about a malformed input expression in a node.</p>"}, "mismatchedNodeInputType": {"shape": "MismatchedNodeInputTypeFlowValidationDetails", "documentation": "<p>Details about mismatched input data types in a node.</p>"}, "mismatchedNodeOutputType": {"shape": "MismatchedNodeOutputTypeFlowValidationDetails", "documentation": "<p>Details about mismatched output data types in a node.</p>"}, "missingConnectionConfiguration": {"shape": "MissingConnectionConfigurationFlowValidationDetails", "documentation": "<p>Details about missing configuration for a connection.</p>"}, "missingDefaultCondition": {"shape": "MissingDefaultConditionFlowValidationDetails", "documentation": "<p>Details about a missing default condition in a conditional node.</p>"}, "missingEndingNodes": {"shape": "MissingEndingNodesFlowValidationDetails", "documentation": "<p>Details about missing ending nodes in the flow.</p>"}, "missingLoopControllerNode": {"shape": "MissingLoopControllerNodeFlowValidationDetails", "documentation": "<p>Details about a flow that's missing a required <code>LoopController</code> node in a DoWhile loop.</p>"}, "missingLoopInputNode": {"shape": "MissingLoopInputNodeFlowValidationDetails", "documentation": "<p>Details about a flow that's missing a required <code>LoopInput</code> node in a DoWhile loop.</p>"}, "missingNodeConfiguration": {"shape": "MissingNodeConfigurationFlowValidationDetails", "documentation": "<p>Details about missing configuration for a node.</p>"}, "missingNodeInput": {"shape": "MissingNodeInputFlowValidationDetails", "documentation": "<p>Details about a missing required input in a node.</p>"}, "missingNodeOutput": {"shape": "MissingNodeOutputFlowValidationDetails", "documentation": "<p>Details about a missing required output in a node.</p>"}, "missingStartingNodes": {"shape": "MissingStartingNodesFlowValidationDetails", "documentation": "<p>Details about missing starting nodes in the flow.</p>"}, "multipleLoopControllerNodes": {"shape": "MultipleLoopControllerNodesFlowValidationDetails", "documentation": "<p>Details about a flow that contains multiple <code>LoopController</code> nodes in a DoWhile loop.</p>"}, "multipleLoopInputNodes": {"shape": "MultipleLoopInputNodesFlowValidationDetails", "documentation": "<p>Details about a flow that contains multiple <code>LoopInput</code> nodes in a DoWhile loop.</p>"}, "multipleNodeInputConnections": {"shape": "MultipleNodeInputConnectionsFlowValidationDetails", "documentation": "<p>Details about multiple connections to a single node input.</p>"}, "unfulfilledNodeInput": {"shape": "UnfulfilledNodeInputFlowValidationDetails", "documentation": "<p>Details about an unfulfilled node input with no valid connections.</p>"}, "unknownConnectionCondition": {"shape": "UnknownConnectionConditionFlowValidationDetails", "documentation": "<p>Details about an unknown condition for a connection.</p>"}, "unknownConnectionSource": {"shape": "UnknownConnectionSourceFlowValidationDetails", "documentation": "<p>Details about an unknown source node for a connection.</p>"}, "unknownConnectionSourceOutput": {"shape": "UnknownConnectionSourceOutputFlowValidationDetails", "documentation": "<p>Details about an unknown source output for a connection.</p>"}, "unknownConnectionTarget": {"shape": "UnknownConnectionTargetFlowValidationDetails", "documentation": "<p>Details about an unknown target node for a connection.</p>"}, "unknownConnectionTargetInput": {"shape": "UnknownConnectionTargetInputFlowValidationDetails", "documentation": "<p>Details about an unknown target input for a connection.</p>"}, "unknownNodeInput": {"shape": "UnknownNodeInputFlowValidationDetails", "documentation": "<p>Details about an unknown input for a node.</p>"}, "unknownNodeOutput": {"shape": "UnknownNodeOutputFlowValidationDetails", "documentation": "<p>Details about an unknown output for a node.</p>"}, "unreachableNode": {"shape": "UnreachableNodeFlowValidationDetails", "documentation": "<p>Details about an unreachable node in the flow.</p>"}, "unsatisfiedConnectionConditions": {"shape": "UnsatisfiedConnectionConditionsFlowValidationDetails", "documentation": "<p>Details about unsatisfied conditions for a connection.</p>"}, "unspecified": {"shape": "UnspecifiedFlowValidationDetails", "documentation": "<p>Details about an unspecified validation.</p>"}}, "documentation": "<p>A union type containing various possible validation issues in the flow.</p>", "union": true}, "FlowValidationSeverity": {"type": "string", "enum": ["Warning", "Error"]}, "FlowValidationType": {"type": "string", "enum": ["CyclicConnection", "DuplicateConnections", "DuplicateConditionExpression", "UnreachableNode", "UnknownConnectionSource", "UnknownConnectionSourceOutput", "UnknownConnectionTarget", "UnknownConnectionTargetInput", "UnknownConnectionCondition", "MalformedConditionExpression", "MalformedNodeInputExpression", "MismatchedNodeInputType", "MismatchedNodeOutputType", "IncompatibleConnectionDataType", "MissingConnectionConfiguration", "MissingDefaultCondition", "MissingEndingNodes", "MissingNodeConfiguration", "MissingNodeInput", "MissingNodeOutput", "MissingStartingNodes", "MultipleNodeInputConnections", "UnfulfilledNodeInput", "UnsatisfiedConnectionConditions", "Unspecified", "UnknownNodeInput", "UnknownNodeOutput", "MissingLoopInputNode", "MissingLoopControllerNode", "MultipleLoopInputNodes", "MultipleLoopControllerNodes", "LoopIncompatibleNodeType", "InvalidLoopBoundary"]}, "FlowValidations": {"type": "list", "member": {"shape": "FlowValidation"}, "max": 100, "min": 0}, "FlowVersionSummaries": {"type": "list", "member": {"shape": "FlowVersionSummary"}, "max": 10, "min": 0}, "FlowVersionSummary": {"type": "structure", "required": ["arn", "createdAt", "id", "status", "version"], "members": {"arn": {"shape": "FlowArn", "documentation": "<p>The Amazon Resource Name (ARN) of the flow that the version belongs to.</p>"}, "createdAt": {"shape": "DateTimestamp", "documentation": "<p>The time at the version was created.</p>"}, "id": {"shape": "FlowId", "documentation": "<p>The unique identifier of the flow.</p>"}, "status": {"shape": "FlowStatus", "documentation": "<p>The status of the flow.</p>"}, "version": {"shape": "NumericalVersion", "documentation": "<p>The version of the flow.</p>"}}, "documentation": "<p>Contains information about a version of a flow.</p> <p>This data type is used in the following API operations:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent_ListFlowVersions.html#API_agent_ListFlowVersions_ResponseSyntax\">ListFlowVersions response</a> </p> </li> </ul>"}, "Function": {"type": "structure", "required": ["name"], "members": {"description": {"shape": "FunctionDescription", "documentation": "<p>A description of the function and its purpose.</p>"}, "name": {"shape": "Name", "documentation": "<p>A name for the function.</p>"}, "parameters": {"shape": "ParameterMap", "documentation": "<p>The parameters that the agent elicits from the user to fulfill the function.</p>"}, "requireConfirmation": {"shape": "RequireConfirmation", "documentation": "<p>Contains information if user confirmation is required to invoke the function.</p>"}}, "documentation": "<p>Defines parameters that the agent needs to invoke from the user to complete the function. Corresponds to an action in an action group.</p> <p>This data type is used in the following API operations:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent_CreateAgentActionGroup.html#API_agent_CreateAgentActionGroup_RequestSyntax\">CreateAgentActionGroup request</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent_CreateAgentActionGroup.html#API_agent_CreateAgentActionGroup_ResponseSyntax\">CreateAgentActionGroup response</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent_UpdateAgentActionGroup.html#API_agent_UpdateAgentActionGroup_RequestSyntax\">UpdateAgentActionGroup request</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent_UpdateAgentActionGroup.html#API_agent_UpdateAgentActionGroup_ResponseSyntax\">UpdateAgentActionGroup response</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent_GetAgentActionGroup.html#API_agent_GetAgentActionGroup_ResponseSyntax\">GetAgentActionGroup response</a> </p> </li> </ul>"}, "FunctionDescription": {"type": "string", "max": 1200, "min": 1}, "FunctionSchema": {"type": "structure", "members": {"functions": {"shape": "Functions", "documentation": "<p>A list of functions that each define an action in the action group.</p>"}}, "documentation": "<p>Defines functions that each define parameters that the agent needs to invoke from the user. Each function represents an action in an action group.</p> <p>This data type is used in the following API operations:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent_CreateAgentActionGroup.html#API_agent_CreateAgentActionGroup_RequestSyntax\">CreateAgentActionGroup request</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent_CreateAgentActionGroup.html#API_agent_CreateAgentActionGroup_ResponseSyntax\">CreateAgentActionGroup response</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent_UpdateAgentActionGroup.html#API_agent_UpdateAgentActionGroup_RequestSyntax\">UpdateAgentActionGroup request</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent_UpdateAgentActionGroup.html#API_agent_UpdateAgentActionGroup_ResponseSyntax\">UpdateAgentActionGroup response</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent_GetAgentActionGroup.html#API_agent_GetAgentActionGroup_ResponseSyntax\">GetAgentActionGroup response</a> </p> </li> </ul>", "union": true}, "Functions": {"type": "list", "member": {"shape": "Function"}}, "GetAgentActionGroupRequest": {"type": "structure", "required": ["actionGroupId", "agentId", "agentVersion"], "members": {"actionGroupId": {"shape": "Id", "documentation": "<p>The unique identifier of the action group for which to get information.</p>", "location": "uri", "locationName": "actionGroupId"}, "agentId": {"shape": "Id", "documentation": "<p>The unique identifier of the agent that the action group belongs to.</p>", "location": "uri", "locationName": "agentId"}, "agentVersion": {"shape": "Version", "documentation": "<p>The version of the agent that the action group belongs to.</p>", "location": "uri", "locationName": "agentVersion"}}}, "GetAgentActionGroupResponse": {"type": "structure", "required": ["agentActionGroup"], "members": {"agentActionGroup": {"shape": "AgentActionGroup", "documentation": "<p>Contains details about the action group.</p>"}}}, "GetAgentAliasRequest": {"type": "structure", "required": ["agentAliasId", "agentId"], "members": {"agentAliasId": {"shape": "AgentAliasId", "documentation": "<p>The unique identifier of the alias for which to get information.</p>", "location": "uri", "locationName": "agentAliasId"}, "agentId": {"shape": "Id", "documentation": "<p>The unique identifier of the agent to which the alias to get information belongs.</p>", "location": "uri", "locationName": "agentId"}}}, "GetAgentAliasResponse": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON>"], "members": {"agentAlias": {"shape": "Agent<PERSON><PERSON><PERSON>", "documentation": "<p>Contains information about the alias.</p>"}}}, "GetAgentCollaboratorRequest": {"type": "structure", "required": ["agentId", "agentVersion", "collaboratorId"], "members": {"agentId": {"shape": "Id", "documentation": "<p>The agent's ID.</p>", "location": "uri", "locationName": "agentId"}, "agentVersion": {"shape": "Version", "documentation": "<p>The agent's version.</p>", "location": "uri", "locationName": "agentVersion"}, "collaboratorId": {"shape": "Id", "documentation": "<p>The collaborator's ID.</p>", "location": "uri", "locationName": "collaboratorId"}}}, "GetAgentCollaboratorResponse": {"type": "structure", "required": ["agentCollaborator"], "members": {"agentCollaborator": {"shape": "AgentCollaborator", "documentation": "<p>Details about the collaborator.</p>"}}}, "GetAgentKnowledgeBaseRequest": {"type": "structure", "required": ["agentId", "agentVersion", "knowledgeBaseId"], "members": {"agentId": {"shape": "Id", "documentation": "<p>The unique identifier of the agent with which the knowledge base is associated.</p>", "location": "uri", "locationName": "agentId"}, "agentVersion": {"shape": "Version", "documentation": "<p>The version of the agent with which the knowledge base is associated.</p>", "location": "uri", "locationName": "agentVersion"}, "knowledgeBaseId": {"shape": "Id", "documentation": "<p>The unique identifier of the knowledge base associated with the agent.</p>", "location": "uri", "locationName": "knowledgeBaseId"}}}, "GetAgentKnowledgeBaseResponse": {"type": "structure", "required": ["agentKnowledgeBase"], "members": {"agentKnowledgeBase": {"shape": "AgentKnowledgeBase", "documentation": "<p>Contains details about a knowledge base attached to an agent.</p>"}}}, "GetAgentRequest": {"type": "structure", "required": ["agentId"], "members": {"agentId": {"shape": "Id", "documentation": "<p>The unique identifier of the agent.</p>", "location": "uri", "locationName": "agentId"}}}, "GetAgentResponse": {"type": "structure", "required": ["agent"], "members": {"agent": {"shape": "Agent", "documentation": "<p>Contains details about the agent.</p>"}}}, "GetAgentVersionRequest": {"type": "structure", "required": ["agentId", "agentVersion"], "members": {"agentId": {"shape": "Id", "documentation": "<p>The unique identifier of the agent.</p>", "location": "uri", "locationName": "agentId"}, "agentVersion": {"shape": "NumericalVersion", "documentation": "<p>The version of the agent.</p>", "location": "uri", "locationName": "agentVersion"}}}, "GetAgentVersionResponse": {"type": "structure", "required": ["agentVersion"], "members": {"agentVersion": {"shape": "AgentVersion", "documentation": "<p>Contains details about the version of the agent.</p>"}}}, "GetDataSourceRequest": {"type": "structure", "required": ["dataSourceId", "knowledgeBaseId"], "members": {"dataSourceId": {"shape": "Id", "documentation": "<p>The unique identifier of the data source.</p>", "location": "uri", "locationName": "dataSourceId"}, "knowledgeBaseId": {"shape": "Id", "documentation": "<p>The unique identifier of the knowledge base for the data source.</p>", "location": "uri", "locationName": "knowledgeBaseId"}}}, "GetDataSourceResponse": {"type": "structure", "required": ["dataSource"], "members": {"dataSource": {"shape": "DataSource", "documentation": "<p>Contains details about the data source.</p>"}}}, "GetFlowAliasRequest": {"type": "structure", "required": ["aliasIdentifier", "flowIdentifier"], "members": {"aliasIdentifier": {"shape": "FlowAliasIdentifier", "documentation": "<p>The unique identifier of the alias for which to retrieve information.</p>", "location": "uri", "locationName": "aliasIdentifier"}, "flowIdentifier": {"shape": "FlowIdentifier", "documentation": "<p>The unique identifier of the flow that the alias belongs to.</p>", "location": "uri", "locationName": "flowIdentifier"}}}, "GetFlowAliasResponse": {"type": "structure", "required": ["arn", "createdAt", "flowId", "id", "name", "routingConfiguration", "updatedAt"], "members": {"arn": {"shape": "FlowAliasArn", "documentation": "<p>The Amazon Resource Name (ARN) of the flow.</p>"}, "concurrencyConfiguration": {"shape": "FlowAliasConcurrencyConfiguration", "documentation": "<p>The configuration that specifies how nodes in the flow are executed in parallel.</p>"}, "createdAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the flow was created.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description of the flow.</p>"}, "flowId": {"shape": "FlowId", "documentation": "<p>The unique identifier of the flow that the alias belongs to.</p>"}, "id": {"shape": "FlowAliasId", "documentation": "<p>The unique identifier of the alias of the flow.</p>"}, "name": {"shape": "Name", "documentation": "<p>The name of the alias.</p>"}, "routingConfiguration": {"shape": "FlowAliasRoutingConfiguration", "documentation": "<p>Contains information about the version that the alias is mapped to.</p>"}, "updatedAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the alias was last updated.</p>"}}}, "GetFlowRequest": {"type": "structure", "required": ["flowIdentifier"], "members": {"flowIdentifier": {"shape": "FlowIdentifier", "documentation": "<p>The unique identifier of the flow.</p>", "location": "uri", "locationName": "flowIdentifier"}}}, "GetFlowResponse": {"type": "structure", "required": ["arn", "createdAt", "executionRoleArn", "id", "name", "status", "updatedAt", "version"], "members": {"arn": {"shape": "FlowArn", "documentation": "<p>The Amazon Resource Name (ARN) of the flow.</p>"}, "createdAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the flow was created.</p>"}, "customerEncryptionKeyArn": {"shape": "KmsKeyArn", "documentation": "<p>The Amazon Resource Name (ARN) of the KMS key that the flow is encrypted with.</p>"}, "definition": {"shape": "FlowDefinition", "documentation": "<p>The definition of the nodes and connections between the nodes in the flow.</p>"}, "description": {"shape": "FlowDescription", "documentation": "<p>The description of the flow.</p>"}, "executionRoleArn": {"shape": "FlowExecutionRoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the service role with permissions to create a flow. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/flows-permissions.html\">Create a service row for flows</a> in the Amazon Bedrock User Guide.</p>"}, "id": {"shape": "FlowId", "documentation": "<p>The unique identifier of the flow.</p>"}, "name": {"shape": "FlowName", "documentation": "<p>The name of the flow.</p>"}, "status": {"shape": "FlowStatus", "documentation": "<p>The status of the flow. The following statuses are possible:</p> <ul> <li> <p>NotPrepared – The flow has been created or updated, but hasn't been prepared. If you just created the flow, you can't test it. If you updated the flow, the <code>DRAFT</code> version won't contain the latest changes for testing. Send a <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent_PrepareFlow.html\">PrepareFlow</a> request to package the latest changes into the <code>DRAFT</code> version.</p> </li> <li> <p>Preparing – The flow is being prepared so that the <code>DRAFT</code> version contains the latest changes for testing.</p> </li> <li> <p>Prepared – The flow is prepared and the <code>DRAFT</code> version contains the latest changes for testing.</p> </li> <li> <p>Failed – The last API operation that you invoked on the flow failed. Send a <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent_GetFlow.html\">GetFlow</a> request and check the error message in the <code>validations</code> field.</p> </li> </ul>"}, "updatedAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the flow was last updated.</p>"}, "validations": {"shape": "FlowValidations", "documentation": "<p>A list of validation error messages related to the last failed operation on the flow.</p>"}, "version": {"shape": "DraftVersion", "documentation": "<p>The version of the flow for which information was retrieved.</p>"}}}, "GetFlowVersionRequest": {"type": "structure", "required": ["flowIdentifier", "flowVersion"], "members": {"flowIdentifier": {"shape": "FlowIdentifier", "documentation": "<p>The unique identifier of the flow for which to get information.</p>", "location": "uri", "locationName": "flowIdentifier"}, "flowVersion": {"shape": "NumericalVersion", "documentation": "<p>The version of the flow for which to get information.</p>", "location": "uri", "locationName": "flowVersion"}}}, "GetFlowVersionResponse": {"type": "structure", "required": ["arn", "createdAt", "executionRoleArn", "id", "name", "status", "version"], "members": {"arn": {"shape": "FlowArn", "documentation": "<p>The Amazon Resource Name (ARN) of the flow.</p>"}, "createdAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the flow was created.</p>"}, "customerEncryptionKeyArn": {"shape": "KmsKeyArn", "documentation": "<p>The Amazon Resource Name (ARN) of the KMS key that the version of the flow is encrypted with.</p>"}, "definition": {"shape": "FlowDefinition", "documentation": "<p>The definition of the nodes and connections between nodes in the flow.</p>"}, "description": {"shape": "FlowDescription", "documentation": "<p>The description of the flow.</p>"}, "executionRoleArn": {"shape": "FlowExecutionRoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the service role with permissions to create a flow. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/flows-permissions.html\">Create a service role for flows in Amazon Bedrock</a> in the Amazon Bedrock User Guide.</p>"}, "id": {"shape": "FlowId", "documentation": "<p>The unique identifier of the flow.</p>"}, "name": {"shape": "FlowName", "documentation": "<p>The name of the version.</p>"}, "status": {"shape": "FlowStatus", "documentation": "<p>The status of the flow.</p>"}, "version": {"shape": "NumericalVersion", "documentation": "<p>The version of the flow for which information was retrieved.</p>"}}}, "GetIngestionJobRequest": {"type": "structure", "required": ["dataSourceId", "ingestionJobId", "knowledgeBaseId"], "members": {"dataSourceId": {"shape": "Id", "documentation": "<p>The unique identifier of the data source for the data ingestion job you want to get information on.</p>", "location": "uri", "locationName": "dataSourceId"}, "ingestionJobId": {"shape": "Id", "documentation": "<p>The unique identifier of the data ingestion job you want to get information on.</p>", "location": "uri", "locationName": "ingestionJobId"}, "knowledgeBaseId": {"shape": "Id", "documentation": "<p>The unique identifier of the knowledge base for the data ingestion job you want to get information on.</p>", "location": "uri", "locationName": "knowledgeBaseId"}}}, "GetIngestionJobResponse": {"type": "structure", "required": ["ingestionJob"], "members": {"ingestionJob": {"shape": "IngestionJob", "documentation": "<p>Contains details about the data ingestion job.</p>"}}}, "GetKnowledgeBaseDocumentsRequest": {"type": "structure", "required": ["dataSourceId", "documentIdentifiers", "knowledgeBaseId"], "members": {"dataSourceId": {"shape": "Id", "documentation": "<p>The unique identifier of the data source that contains the documents.</p>", "location": "uri", "locationName": "dataSourceId"}, "documentIdentifiers": {"shape": "DocumentIdentifiers", "documentation": "<p>A list of objects, each of which contains information to identify a document for which to retrieve information.</p>"}, "knowledgeBaseId": {"shape": "Id", "documentation": "<p>The unique identifier of the knowledge base that is connected to the data source.</p>", "location": "uri", "locationName": "knowledgeBaseId"}}}, "GetKnowledgeBaseDocumentsResponse": {"type": "structure", "members": {"documentDetails": {"shape": "KnowledgeBaseDocumentDetails", "documentation": "<p>A list of objects, each of which contains information about the documents that were retrieved.</p>"}}}, "GetKnowledgeBaseRequest": {"type": "structure", "required": ["knowledgeBaseId"], "members": {"knowledgeBaseId": {"shape": "Id", "documentation": "<p>The unique identifier of the knowledge base you want to get information on.</p>", "location": "uri", "locationName": "knowledgeBaseId"}}}, "GetKnowledgeBaseResponse": {"type": "structure", "required": ["knowledgeBase"], "members": {"knowledgeBase": {"shape": "KnowledgeBase", "documentation": "<p>Contains details about the knowledge base.</p>"}}}, "GetPromptRequest": {"type": "structure", "required": ["promptIdentifier"], "members": {"promptIdentifier": {"shape": "PromptIdentifier", "documentation": "<p>The unique identifier of the prompt.</p>", "location": "uri", "locationName": "promptIdentifier"}, "promptVersion": {"shape": "Version", "documentation": "<p>The version of the prompt about which you want to retrieve information. Omit this field to return information about the working draft of the prompt.</p>", "location": "querystring", "locationName": "promptVersion"}}}, "GetPromptResponse": {"type": "structure", "required": ["arn", "createdAt", "id", "name", "updatedAt", "version"], "members": {"arn": {"shape": "PromptArn", "documentation": "<p>The Amazon Resource Name (ARN) of the prompt or the prompt version (if you specified a version in the request).</p>"}, "createdAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the prompt was created.</p>"}, "customerEncryptionKeyArn": {"shape": "KmsKeyArn", "documentation": "<p>The Amazon Resource Name (ARN) of the KMS key that the prompt is encrypted with.</p>"}, "defaultVariant": {"shape": "PromptVariantName", "documentation": "<p>The name of the default variant for the prompt. This value must match the <code>name</code> field in the relevant <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent_PromptVariant.html\">PromptVariant</a> object.</p>"}, "description": {"shape": "PromptDescription", "documentation": "<p>The descriptino of the prompt.</p>"}, "id": {"shape": "PromptId", "documentation": "<p>The unique identifier of the prompt.</p>"}, "name": {"shape": "PromptName", "documentation": "<p>The name of the prompt.</p>"}, "updatedAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the prompt was last updated.</p>"}, "variants": {"shape": "PromptVariantList", "documentation": "<p>A list of objects, each containing details about a variant of the prompt.</p>"}, "version": {"shape": "Version", "documentation": "<p>The version of the prompt.</p>"}}}, "GraphArn": {"type": "string", "documentation": "<p>ARN for Neptune Analytics graph database</p>", "max": 255, "min": 1, "pattern": "^arn:aws(|-cn|-us-gov):neptune-graph:[a-zA-Z0-9-]*:[0-9]{12}:graph/g-[a-zA-Z0-9]{10}$", "sensitive": true}, "GuardrailConfiguration": {"type": "structure", "members": {"guardrailIdentifier": {"shape": "GuardrailIdentifier", "documentation": "<p>The unique identifier of the guardrail.</p>"}, "guardrailVersion": {"shape": "GuardrailVersion", "documentation": "<p>The version of the guardrail.</p>"}}, "documentation": "<p>Details about a guardrail associated with a resource.</p>"}, "GuardrailIdentifier": {"type": "string", "max": 2048, "min": 0, "pattern": "^(([a-z0-9]+)|(arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:guardrail/[a-z0-9]+))$"}, "GuardrailVersion": {"type": "string", "pattern": "^(([0-9]{1,8})|(DRAFT))$"}, "HierarchicalChunkingConfiguration": {"type": "structure", "required": ["levelConfigurations", "overlapTokens"], "members": {"levelConfigurations": {"shape": "HierarchicalChunkingLevelConfigurations", "documentation": "<p>Token settings for each layer.</p>"}, "overlapTokens": {"shape": "HierarchicalChunkingConfigurationOverlapTokensInteger", "documentation": "<p>The number of tokens to repeat across chunks in the same layer.</p>"}}, "documentation": "<p>Settings for hierarchical document chunking for a data source. Hierarchical chunking splits documents into layers of chunks where the first layer contains large chunks, and the second layer contains smaller chunks derived from the first layer.</p> <p>You configure the number of tokens to overlap, or repeat across adjacent chunks. For example, if you set overlap tokens to 60, the last 60 tokens in the first chunk are also included at the beginning of the second chunk. For each layer, you must also configure the maximum number of tokens in a chunk.</p>"}, "HierarchicalChunkingConfigurationOverlapTokensInteger": {"type": "integer", "box": true, "min": 1}, "HierarchicalChunkingLevelConfiguration": {"type": "structure", "required": ["maxTokens"], "members": {"maxTokens": {"shape": "HierarchicalChunkingLevelConfigurationMaxTokensInteger", "documentation": "<p>The maximum number of tokens that a chunk can contain in this layer.</p>"}}, "documentation": "<p>Token settings for a layer in a hierarchical chunking configuration.</p>"}, "HierarchicalChunkingLevelConfigurationMaxTokensInteger": {"type": "integer", "box": true, "max": 8192, "min": 1}, "HierarchicalChunkingLevelConfigurations": {"type": "list", "member": {"shape": "HierarchicalChunkingLevelConfiguration"}, "max": 2, "min": 2}, "HttpsUrl": {"type": "string", "pattern": "^https://[A-Za-z0-9][^\\s]*$"}, "Id": {"type": "string", "pattern": "^[0-9a-zA-Z]{10}$"}, "IncludeExclude": {"type": "string", "enum": ["INCLUDE", "EXCLUDE"]}, "IncompatibleConnectionDataTypeFlowValidationDetails": {"type": "structure", "required": ["connection"], "members": {"connection": {"shape": "FlowConnectionName", "documentation": "<p>The name of the connection with incompatible data types.</p>"}}, "documentation": "<p>Details about incompatible data types in a connection between nodes.</p>"}, "IncompatibleLoopNodeType": {"type": "string", "enum": ["Input", "Condition", "Iterator", "Collector"]}, "InferenceConfiguration": {"type": "structure", "members": {"maximumLength": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The maximum number of tokens to allow in the generated response.</p>"}, "stopSequences": {"shape": "StopSequences", "documentation": "<p>A list of stop sequences. A stop sequence is a sequence of characters that causes the model to stop generating the response.</p>"}, "temperature": {"shape": "Temperature", "documentation": "<p>The likelihood of the model selecting higher-probability options while generating a response. A lower value makes the model more likely to choose higher-probability options, while a higher value makes the model more likely to choose lower-probability options.</p>"}, "topK": {"shape": "TopK", "documentation": "<p>While generating a response, the model determines the probability of the following token at each point of generation. The value that you set for <code>topK</code> is the number of most-likely candidates from which the model chooses the next token in the sequence. For example, if you set <code>topK</code> to 50, the model selects the next token from among the top 50 most likely choices.</p>"}, "topP": {"shape": "TopP", "documentation": "<p>While generating a response, the model determines the probability of the following token at each point of generation. The value that you set for <code>Top P</code> determines the number of most-likely candidates from which the model chooses the next token in the sequence. For example, if you set <code>topP</code> to 0.8, the model only selects the next token from the top 80% of the probability distribution of next tokens.</p>"}}, "documentation": "<p>Contains inference parameters to use when the agent invokes a foundation model in the part of the agent sequence defined by the <code>promptType</code>. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/model-parameters.html\">Inference parameters for foundation models</a>.</p>"}, "IngestKnowledgeBaseDocumentsRequest": {"type": "structure", "required": ["dataSourceId", "documents", "knowledgeBaseId"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>A unique, case-sensitive identifier to ensure that the API request completes no more than one time. If this token matches a previous request, Amazon Bedrock ignores the request, but does not return an error. For more information, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/APIReference/Run_Instance_Idempotency.html\">Ensuring idempotency</a>.</p>", "idempotencyToken": true}, "dataSourceId": {"shape": "Id", "documentation": "<p>The unique identifier of the data source connected to the knowledge base that you're adding documents to.</p>", "location": "uri", "locationName": "dataSourceId"}, "documents": {"shape": "KnowledgeBaseDocuments", "documentation": "<p>A list of objects, each of which contains information about the documents to add.</p>"}, "knowledgeBaseId": {"shape": "Id", "documentation": "<p>The unique identifier of the knowledge base to ingest the documents into.</p>", "location": "uri", "locationName": "knowledgeBaseId"}}}, "IngestKnowledgeBaseDocumentsResponse": {"type": "structure", "members": {"documentDetails": {"shape": "KnowledgeBaseDocumentDetails", "documentation": "<p>A list of objects, each of which contains information about the documents that were ingested.</p>"}}}, "IngestionJob": {"type": "structure", "required": ["dataSourceId", "ingestionJobId", "knowledgeBaseId", "startedAt", "status", "updatedAt"], "members": {"dataSourceId": {"shape": "Id", "documentation": "<p>The unique identifier of the data source for the data ingestion job.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description of the data ingestion job.</p>"}, "failureReasons": {"shape": "FailureReasons", "documentation": "<p>A list of reasons that the data ingestion job failed.</p>"}, "ingestionJobId": {"shape": "Id", "documentation": "<p>The unique identifier of the data ingestion job.</p>"}, "knowledgeBaseId": {"shape": "Id", "documentation": "<p>The unique identifier of the knowledge for the data ingestion job.</p>"}, "startedAt": {"shape": "DateTimestamp", "documentation": "<p>The time the data ingestion job started.</p> <p>If you stop a data ingestion job, the <code>startedAt</code> time is the time the job was started before the job was stopped.</p>"}, "statistics": {"shape": "IngestionJobStatistics", "documentation": "<p>Contains statistics about the data ingestion job.</p>"}, "status": {"shape": "IngestionJobStatus", "documentation": "<p>The status of the data ingestion job.</p>"}, "updatedAt": {"shape": "DateTimestamp", "documentation": "<p>The time the data ingestion job was last updated.</p> <p>If you stop a data ingestion job, the <code>updatedAt</code> time is the time the job was stopped.</p>"}}, "documentation": "<p>Contains details about a data ingestion job. Data sources are ingested into a knowledge base so that Large Language Models (LLMs) can use your data.</p> <p>This data type is used in the following API operations:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent_StartIngestionJob.html#API_agent_StartIngestionJob_ResponseSyntax\">StartIngestionJob response</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent_GetIngestionJob.html#API_agent_GetIngestionJob_ResponseSyntax\">GetIngestionJob response</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent_ListIngestionJobs.html#API_agent_ListIngestionJobs_ResponseSyntax\">ListIngestionJob response</a> </p> </li> </ul>"}, "IngestionJobFilter": {"type": "structure", "required": ["attribute", "operator", "values"], "members": {"attribute": {"shape": "IngestionJobFilterAttribute", "documentation": "<p>The name of field or attribute to apply the filter.</p>"}, "operator": {"shape": "IngestionJobFilterOperator", "documentation": "<p>The operation to apply to the field or attribute.</p>"}, "values": {"shape": "IngestionJobFilterValues", "documentation": "<p>A list of values that belong to the field or attribute.</p>"}}, "documentation": "<p>The definition of a filter to filter the data.</p>"}, "IngestionJobFilterAttribute": {"type": "string", "enum": ["STATUS"]}, "IngestionJobFilterOperator": {"type": "string", "enum": ["EQ"]}, "IngestionJobFilterValue": {"type": "string", "max": 100, "min": 0, "pattern": "^.*$"}, "IngestionJobFilterValues": {"type": "list", "member": {"shape": "IngestionJobFilterValue"}, "max": 10, "min": 0}, "IngestionJobFilters": {"type": "list", "member": {"shape": "IngestionJobFilter"}, "max": 1, "min": 1}, "IngestionJobSortBy": {"type": "structure", "required": ["attribute", "order"], "members": {"attribute": {"shape": "IngestionJobSortByAttribute", "documentation": "<p>The name of field or attribute to apply sorting of data.</p>"}, "order": {"shape": "SortOrder", "documentation": "<p>The order for sorting the data.</p>"}}, "documentation": "<p>The parameters of sorting the data.</p>"}, "IngestionJobSortByAttribute": {"type": "string", "enum": ["STATUS", "STARTED_AT"]}, "IngestionJobStatistics": {"type": "structure", "members": {"numberOfDocumentsDeleted": {"shape": "PrimitiveLong", "documentation": "<p>The number of source documents that were deleted.</p>"}, "numberOfDocumentsFailed": {"shape": "PrimitiveLong", "documentation": "<p>The number of source documents that failed to be ingested.</p>"}, "numberOfDocumentsScanned": {"shape": "PrimitiveLong", "documentation": "<p>The total number of source documents that were scanned. Includes new, updated, and unchanged documents.</p>"}, "numberOfMetadataDocumentsModified": {"shape": "PrimitiveLong", "documentation": "<p>The number of metadata files that were updated or deleted.</p>"}, "numberOfMetadataDocumentsScanned": {"shape": "PrimitiveLong", "documentation": "<p>The total number of metadata files that were scanned. Includes new, updated, and unchanged files.</p>"}, "numberOfModifiedDocumentsIndexed": {"shape": "PrimitiveLong", "documentation": "<p>The number of modified source documents in the data source that were successfully indexed.</p>"}, "numberOfNewDocumentsIndexed": {"shape": "PrimitiveLong", "documentation": "<p>The number of new source documents in the data source that were successfully indexed.</p>"}}, "documentation": "<p>Contains the statistics for the data ingestion job.</p>"}, "IngestionJobStatus": {"type": "string", "enum": ["STARTING", "IN_PROGRESS", "COMPLETE", "FAILED", "STOPPING", "STOPPED"]}, "IngestionJobSummaries": {"type": "list", "member": {"shape": "IngestionJobSummary"}}, "IngestionJobSummary": {"type": "structure", "required": ["dataSourceId", "ingestionJobId", "knowledgeBaseId", "startedAt", "status", "updatedAt"], "members": {"dataSourceId": {"shape": "Id", "documentation": "<p>The unique identifier of the data source for the data ingestion job.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description of the data ingestion job.</p>"}, "ingestionJobId": {"shape": "Id", "documentation": "<p>The unique identifier of the data ingestion job.</p>"}, "knowledgeBaseId": {"shape": "Id", "documentation": "<p>The unique identifier of the knowledge base for the data ingestion job.</p>"}, "startedAt": {"shape": "DateTimestamp", "documentation": "<p>The time the data ingestion job started.</p>"}, "statistics": {"shape": "IngestionJobStatistics", "documentation": "<p>Contains statistics for the data ingestion job.</p>"}, "status": {"shape": "IngestionJobStatus", "documentation": "<p>The status of the data ingestion job.</p>"}, "updatedAt": {"shape": "DateTimestamp", "documentation": "<p>The time the data ingestion job was last updated.</p>"}}, "documentation": "<p>Contains details about a data ingestion job.</p>"}, "InlineCode": {"type": "string", "max": 5000000, "min": 1, "sensitive": true}, "InlineCodeFlowNodeConfiguration": {"type": "structure", "required": ["code", "language"], "members": {"code": {"shape": "InlineCode", "documentation": "<p>The code that's executed in your inline code node. The code can access input data from previous nodes in the flow, perform operations on that data, and produce output that can be used by other nodes in your flow.</p> <p>The code must be valid in the programming <code>language</code> that you specify.</p>"}, "language": {"shape": "SupportedLanguages", "documentation": "<p>The programming language used by your inline code node.</p> <p>The code must be valid in the programming <code>language</code> that you specify. Currently, only Python 3 (<code>Python_3</code>) is supported.</p>"}}, "documentation": "<p>Contains configurations for an inline code node in your flow. Inline code nodes let you write and execute code directly within your flow, enabling data transformations, custom logic, and integrations without needing an external Lambda function.</p>"}, "InlineContent": {"type": "structure", "required": ["type"], "members": {"byteContent": {"shape": "ByteContentDoc", "documentation": "<p>Contains information about content defined inline in bytes.</p>"}, "textContent": {"shape": "TextContentDoc", "documentation": "<p>Contains information about content defined inline in text.</p>"}, "type": {"shape": "InlineContentType", "documentation": "<p>The type of inline content to define.</p>"}}, "documentation": "<p>Contains information about content defined inline to ingest into a data source. Choose a <code>type</code> and include the field that corresponds to it.</p>"}, "InlineContentType": {"type": "string", "enum": ["BYTE", "TEXT"]}, "InputFlowNodeConfiguration": {"type": "structure", "members": {}, "documentation": "<p>Contains configurations for the input flow node for a flow. This node takes the input from flow invocation and passes it to the next node in the data type that you specify.</p>"}, "Instruction": {"type": "string", "max": 4000, "min": 40, "sensitive": true}, "IntermediateStorage": {"type": "structure", "required": ["s3Location"], "members": {"s3Location": {"shape": "S3Location", "documentation": "<p>An S3 bucket path.</p>"}}, "documentation": "<p>A location for storing content from data sources temporarily as it is processed by custom components in the ingestion pipeline.</p>"}, "InternalServerException": {"type": "structure", "members": {"message": {"shape": "NonBlankString"}}, "documentation": "<p>An internal server error occurred. Retry your request.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true}, "InvalidLoopBoundaryFlowValidationDetails": {"type": "structure", "required": ["connection", "source", "target"], "members": {"connection": {"shape": "FlowConnectionName", "documentation": "<p>The name of the connection that violates loop boundary rules.</p>"}, "source": {"shape": "FlowNodeName", "documentation": "<p>The source node of the connection that violates DoWhile loop boundary rules.</p>"}, "target": {"shape": "FlowNodeName", "documentation": "<p>The target node of the connection that violates DoWhile loop boundary rules.</p>"}}, "documentation": "<p>Details about a flow that contains connections that violate loop boundary rules.</p>"}, "IteratorFlowNodeConfiguration": {"type": "structure", "members": {}, "documentation": "<p>Contains configurations for an iterator node in a flow. Takes an input that is an array and iteratively sends each item of the array as an output to the following node. The size of the array is also returned in the output.</p> <p>The output flow node at the end of the flow iteration will return a response for each member of the array. To return only one response, you can include a collector node downstream from the iterator node.</p>"}, "KendraIndexArn": {"type": "string", "pattern": "^arn:aws(|-cn|-us-gov):kendra:[a-z0-9-]{1,20}:([0-9]{12}|):index/([a-zA-Z0-9][a-zA-Z0-9-]{35}|[a-zA-Z0-9][a-zA-Z0-9-]{35}-[a-zA-Z0-9][a-zA-Z0-9-]{35})$"}, "KendraKnowledgeBaseConfiguration": {"type": "structure", "required": ["kendraIndexArn"], "members": {"kendraIndexArn": {"shape": "KendraIndexArn", "documentation": "<p>The ARN of the Amazon Kendra index.</p>"}}, "documentation": "<p>Settings for an Amazon Kendra knowledge base.</p>"}, "Key": {"type": "string", "max": 200, "min": 1, "sensitive": true}, "KmsKeyArn": {"type": "string", "max": 2048, "min": 1, "pattern": "^arn:aws(|-cn|-us-gov):kms:[a-zA-Z0-9-]*:[0-9]{12}:key/[a-zA-Z0-9-]{36}$"}, "KnowledgeBase": {"type": "structure", "required": ["createdAt", "knowledgeBaseArn", "knowledgeBaseConfiguration", "knowledgeBaseId", "name", "roleArn", "status", "updatedAt"], "members": {"createdAt": {"shape": "DateTimestamp", "documentation": "<p>The time the knowledge base was created.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description of the knowledge base.</p>"}, "failureReasons": {"shape": "FailureReasons", "documentation": "<p>A list of reasons that the API operation on the knowledge base failed.</p>"}, "knowledgeBaseArn": {"shape": "KnowledgeBaseArn", "documentation": "<p>The Amazon Resource Name (ARN) of the knowledge base.</p>"}, "knowledgeBaseConfiguration": {"shape": "KnowledgeBaseConfiguration", "documentation": "<p>Contains details about the embeddings configuration of the knowledge base.</p>"}, "knowledgeBaseId": {"shape": "Id", "documentation": "<p>The unique identifier of the knowledge base.</p>"}, "name": {"shape": "Name", "documentation": "<p>The name of the knowledge base.</p>"}, "roleArn": {"shape": "KnowledgeBaseRoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role with permissions to invoke API operations on the knowledge base.</p>"}, "status": {"shape": "KnowledgeBaseStatus", "documentation": "<p>The status of the knowledge base. The following statuses are possible:</p> <ul> <li> <p>CREATING – The knowledge base is being created.</p> </li> <li> <p>ACTIVE – The knowledge base is ready to be queried.</p> </li> <li> <p>DELETING – The knowledge base is being deleted.</p> </li> <li> <p>UPDATING – The knowledge base is being updated.</p> </li> <li> <p>FAILED – The knowledge base API operation failed.</p> </li> </ul>"}, "storageConfiguration": {"shape": "StorageConfiguration", "documentation": "<p>Contains details about the storage configuration of the knowledge base.</p>"}, "updatedAt": {"shape": "DateTimestamp", "documentation": "<p>The time the knowledge base was last updated.</p>"}}, "documentation": "<p>Contains information about a knowledge base.</p>"}, "KnowledgeBaseArn": {"type": "string", "max": 128, "min": 0, "pattern": "^arn:aws(|-cn|-us-gov):bedrock:[a-zA-Z0-9-]*:[0-9]{12}:knowledge-base/[0-9a-zA-Z]+$"}, "KnowledgeBaseConfiguration": {"type": "structure", "required": ["type"], "members": {"kendraKnowledgeBaseConfiguration": {"shape": "KendraKnowledgeBaseConfiguration", "documentation": "<p>Settings for an Amazon Kendra knowledge base.</p>"}, "sqlKnowledgeBaseConfiguration": {"shape": "SqlKnowledgeBaseConfiguration", "documentation": "<p>Specifies configurations for a knowledge base connected to an SQL database.</p>"}, "type": {"shape": "KnowledgeBaseType", "documentation": "<p>The type of data that the data source is converted into for the knowledge base.</p>"}, "vectorKnowledgeBaseConfiguration": {"shape": "VectorKnowledgeBaseConfiguration", "documentation": "<p>Contains details about the model that's used to convert the data source into vector embeddings.</p>"}}, "documentation": "<p>Contains details about the vector embeddings configuration of the knowledge base.</p>"}, "KnowledgeBaseDocument": {"type": "structure", "required": ["content"], "members": {"content": {"shape": "DocumentContent", "documentation": "<p>Contains the content of the document.</p>"}, "metadata": {"shape": "DocumentMetadata", "documentation": "<p>Contains the metadata to associate with the document.</p>"}}, "documentation": "<p>Contains information about a document to ingest into a knowledge base and metadata to associate with it.</p>"}, "KnowledgeBaseDocumentDetail": {"type": "structure", "required": ["dataSourceId", "identifier", "knowledgeBaseId", "status"], "members": {"dataSourceId": {"shape": "Id", "documentation": "<p>The identifier of the data source connected to the knowledge base that the document was ingested into or deleted from.</p>"}, "identifier": {"shape": "DocumentIdentifier", "documentation": "<p>Contains information that identifies the document.</p>"}, "knowledgeBaseId": {"shape": "Id", "documentation": "<p>The identifier of the knowledge base that the document was ingested into or deleted from.</p>"}, "status": {"shape": "DocumentStatus", "documentation": "<p>The ingestion status of the document. The following statuses are possible:</p> <ul> <li> <p>STARTED – You submitted the ingestion job containing the document.</p> </li> <li> <p>PENDING – The document is waiting to be ingested.</p> </li> <li> <p>IN_PROGRESS – The document is being ingested.</p> </li> <li> <p>INDEXED – The document was successfully indexed.</p> </li> <li> <p>PARTIALLY_INDEXED – The document was partially indexed.</p> </li> <li> <p>METADATA_PARTIALLY_INDEXED – You submitted metadata for an existing document and it was partially indexed.</p> </li> <li> <p>METADATA_UPDATE_FAILED – You submitted a metadata update for an existing document but it failed.</p> </li> <li> <p>FAILED – The document failed to be ingested.</p> </li> <li> <p>NOT_FOUND – The document wasn't found.</p> </li> <li> <p>IGNORED – The document was ignored during ingestion.</p> </li> <li> <p>DELETING – You submitted the delete job containing the document.</p> </li> <li> <p>DELETE_IN_PROGRESS – The document is being deleted.</p> </li> </ul>"}, "statusReason": {"shape": "String", "documentation": "<p>The reason for the status. Appears alongside the status <code>IGNORED</code>.</p>"}, "updatedAt": {"shape": "DateTimestamp", "documentation": "<p>The date and time at which the document was last updated.</p>"}}, "documentation": "<p>Contains the details for a document that was ingested or deleted.</p>"}, "KnowledgeBaseDocumentDetails": {"type": "list", "member": {"shape": "KnowledgeBaseDocumentDetail"}}, "KnowledgeBaseDocuments": {"type": "list", "member": {"shape": "KnowledgeBaseDocument"}, "max": 10, "min": 1}, "KnowledgeBaseFlowNodeConfiguration": {"type": "structure", "required": ["knowledgeBaseId"], "members": {"guardrailConfiguration": {"shape": "GuardrailConfiguration", "documentation": "<p>Contains configurations for a guardrail to apply during query and response generation for the knowledge base in this configuration.</p>"}, "inferenceConfiguration": {"shape": "PromptInferenceConfiguration", "documentation": "<p>Contains inference configurations for the prompt.</p>"}, "knowledgeBaseId": {"shape": "KnowledgeBaseId", "documentation": "<p>The unique identifier of the knowledge base to query.</p>"}, "modelId": {"shape": "KnowledgeBaseModelIdentifier", "documentation": "<p>The unique identifier of the model or <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/cross-region-inference.html\">inference profile</a> to use to generate a response from the query results. Omit this field if you want to return the retrieved results as an array.</p>"}, "numberOfResults": {"shape": "KnowledgeBaseFlowNodeConfigurationNumberOfResultsInteger", "documentation": "<p>The number of results to retrieve from the knowledge base.</p>"}, "orchestrationConfiguration": {"shape": "KnowledgeBaseOrchestrationConfiguration", "documentation": "<p>The configuration for orchestrating the retrieval and generation process in the knowledge base node.</p>"}, "promptTemplate": {"shape": "KnowledgeBasePromptTemplate", "documentation": "<p>A custom prompt template to use with the knowledge base for generating responses.</p>"}, "rerankingConfiguration": {"shape": "VectorSearchRerankingConfiguration", "documentation": "<p>The configuration for reranking the retrieved results from the knowledge base to improve relevance.</p>"}}, "documentation": "<p>Contains configurations for a knowledge base node in a flow. This node takes a query as the input and returns, as the output, the retrieved responses directly (as an array) or a response generated based on the retrieved responses. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/flows-nodes.html\">Node types in a flow</a> in the Amazon Bedrock User Guide.</p>"}, "KnowledgeBaseFlowNodeConfigurationNumberOfResultsInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "KnowledgeBaseId": {"type": "string", "max": 10, "min": 0, "pattern": "^[0-9a-zA-Z]+$"}, "KnowledgeBaseModelIdentifier": {"type": "string", "max": 2048, "min": 1, "pattern": "^(arn:aws(-[^:]{1,12})?:(bedrock|sagemaker):[a-z0-9-]{1,20}:([0-9]{12})?:([a-z-]+/)?)?([a-zA-Z0-9.-]{1,63}){0,2}(([:][a-z0-9-]{1,63}){0,2})?(/[a-z0-9]{1,12})?$"}, "KnowledgeBaseOrchestrationConfiguration": {"type": "structure", "members": {"additionalModelRequestFields": {"shape": "AdditionalModelRequestFields", "documentation": "<p>The additional model-specific request parameters as key-value pairs to be included in the request to the foundation model.</p>"}, "inferenceConfig": {"shape": "PromptInferenceConfiguration", "documentation": "<p>Contains inference configurations for the prompt.</p>"}, "performanceConfig": {"shape": "PerformanceConfiguration", "documentation": "<p>The performance configuration options for the knowledge base retrieval and generation process.</p>"}, "promptTemplate": {"shape": "KnowledgeBasePromptTemplate", "documentation": "<p>A custom prompt template for orchestrating the retrieval and generation process.</p>"}}, "documentation": "<p>Configures how the knowledge base orchestrates the retrieval and generation process, allowing for customization of prompts, inference parameters, and performance settings.</p>"}, "KnowledgeBasePromptTemplate": {"type": "structure", "members": {"textPromptTemplate": {"shape": "KnowledgeBaseTextPrompt", "documentation": "<p>The text of the prompt template.</p>"}}, "documentation": "<p>Defines a custom prompt template for orchestrating the retrieval and generation process.</p>"}, "KnowledgeBaseRoleArn": {"type": "string", "max": 2048, "min": 0, "pattern": "^arn:aws(-[^:]+)?:iam::([0-9]{12})?:role/.+$"}, "KnowledgeBaseState": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "KnowledgeBaseStatus": {"type": "string", "enum": ["CREATING", "ACTIVE", "DELETING", "UPDATING", "FAILED", "DELETE_UNSUCCESSFUL"]}, "KnowledgeBaseStorageType": {"type": "string", "enum": ["OPENSEARCH_SERVERLESS", "PINECONE", "REDIS_ENTERPRISE_CLOUD", "RDS", "MONGO_DB_ATLAS", "NEPTUNE_ANALYTICS", "OPENSEARCH_MANAGED_CLUSTER"]}, "KnowledgeBaseSummaries": {"type": "list", "member": {"shape": "KnowledgeBaseSummary"}}, "KnowledgeBaseSummary": {"type": "structure", "required": ["knowledgeBaseId", "name", "status", "updatedAt"], "members": {"description": {"shape": "Description", "documentation": "<p>The description of the knowledge base.</p>"}, "knowledgeBaseId": {"shape": "Id", "documentation": "<p>The unique identifier of the knowledge base.</p>"}, "name": {"shape": "Name", "documentation": "<p>The name of the knowledge base.</p>"}, "status": {"shape": "KnowledgeBaseStatus", "documentation": "<p>The status of the knowledge base.</p>"}, "updatedAt": {"shape": "DateTimestamp", "documentation": "<p>The time the knowledge base was last updated.</p>"}}, "documentation": "<p>Contains details about a knowledge base.</p>"}, "KnowledgeBaseTextPrompt": {"type": "string", "max": 100000, "min": 1, "sensitive": true}, "KnowledgeBaseType": {"type": "string", "enum": ["VECTOR", "KENDRA", "SQL"]}, "LambdaArn": {"type": "string", "max": 2048, "min": 0, "pattern": "^arn:(aws[a-zA-Z-]*)?:lambda:[a-z]{2}(-gov)?-[a-z]+-\\d{1}:\\d{12}:function:[a-zA-Z0-9-_\\.]+(:(\\$LATEST|[a-zA-Z0-9-_]+))?$"}, "LambdaFunctionFlowNodeConfiguration": {"type": "structure", "required": ["lambdaArn"], "members": {"lambdaArn": {"shape": "LambdaArn", "documentation": "<p>The Amazon Resource Name (ARN) of the Lambda function to invoke.</p>"}}, "documentation": "<p>Contains configurations for a Lambda function node in the flow. You specify the Lambda function to invoke and the inputs into the function. The output is the response that is defined in the Lambda function. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/flows-nodes.html\">Node types in a flow</a> in the Amazon Bedrock User Guide.</p>"}, "LexBotAliasArn": {"type": "string", "max": 78, "min": 0, "pattern": "^arn:aws(|-us-gov):lex:[a-z]{2}(-gov)?-[a-z]+-\\d{1}:\\d{12}:bot-alias/[0-9a-zA-Z]+/[0-9a-zA-Z]+$"}, "LexBotLocaleId": {"type": "string", "max": 10, "min": 1}, "LexFlowNodeConfiguration": {"type": "structure", "required": ["botAliasArn", "localeId"], "members": {"botAliasArn": {"shape": "LexBotAliasArn", "documentation": "<p>The Amazon Resource Name (ARN) of the Amazon Lex bot alias to invoke.</p>"}, "localeId": {"shape": "LexBotLocaleId", "documentation": "<p>The Region to invoke the Amazon Lex bot in.</p>"}}, "documentation": "<p>Contains configurations for a Lex node in the flow. You specify a Amazon Lex bot to invoke. This node takes an utterance as the input and returns as the output the intent identified by the Amazon Lex bot. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/flows-nodes.html\">Node types in a flow</a> in the Amazon Bedrock User Guide.</p>"}, "ListAgentActionGroupsRequest": {"type": "structure", "required": ["agentId", "agentVersion"], "members": {"agentId": {"shape": "Id", "documentation": "<p>The unique identifier of the agent.</p>", "location": "uri", "locationName": "agentId"}, "agentVersion": {"shape": "Version", "documentation": "<p>The version of the agent.</p>", "location": "uri", "locationName": "agentVersion"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in the response. If the total number of results is greater than this value, use the token returned in the response in the <code>nextToken</code> field when making another request to return the next batch of results.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the total number of results is greater than the <code>maxResults</code> value provided in the request, enter the token returned in the <code>nextToken</code> field in the response in this field to return the next batch of results.</p>"}}}, "ListAgentActionGroupsResponse": {"type": "structure", "required": ["actionGroupSummaries"], "members": {"actionGroupSummaries": {"shape": "ActionGroupSummaries", "documentation": "<p>A list of objects, each of which contains information about an action group.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the total number of results is greater than the <code>maxResults</code> value provided in the request, use this token when making another request in the <code>nextToken</code> field to return the next batch of results.</p>"}}}, "ListAgentAliasesRequest": {"type": "structure", "required": ["agentId"], "members": {"agentId": {"shape": "Id", "documentation": "<p>The unique identifier of the agent.</p>", "location": "uri", "locationName": "agentId"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in the response. If the total number of results is greater than this value, use the token returned in the response in the <code>nextToken</code> field when making another request to return the next batch of results.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the total number of results is greater than the <code>maxResults</code> value provided in the request, enter the token returned in the <code>nextToken</code> field in the response in this field to return the next batch of results.</p>"}}}, "ListAgentAliasesResponse": {"type": "structure", "required": ["agentAliasSummaries"], "members": {"agentAliasSummaries": {"shape": "AgentAliasSummaries", "documentation": "<p>A list of objects, each of which contains information about an alias of the agent.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the total number of results is greater than the <code>maxResults</code> value provided in the request, use this token when making another request in the <code>nextToken</code> field to return the next batch of results.</p>"}}}, "ListAgentCollaboratorsRequest": {"type": "structure", "required": ["agentId", "agentVersion"], "members": {"agentId": {"shape": "Id", "documentation": "<p>The agent's ID.</p>", "location": "uri", "locationName": "agentId"}, "agentVersion": {"shape": "Version", "documentation": "<p>The agent's version.</p>", "location": "uri", "locationName": "agentVersion"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of agent collaborators to return in one page of results.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>Specify the pagination token from a previous request to retrieve the next page of results.</p>"}}}, "ListAgentCollaboratorsResponse": {"type": "structure", "required": ["agentCollaboratorSummaries"], "members": {"agentCollaboratorSummaries": {"shape": "AgentCollaboratorSummaries", "documentation": "<p>A list of collaborator summaries.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>Specify the pagination token from a previous request to retrieve the next page of results.</p>"}}}, "ListAgentKnowledgeBasesRequest": {"type": "structure", "required": ["agentId", "agentVersion"], "members": {"agentId": {"shape": "Id", "documentation": "<p>The unique identifier of the agent for which to return information about knowledge bases associated with it.</p>", "location": "uri", "locationName": "agentId"}, "agentVersion": {"shape": "Version", "documentation": "<p>The version of the agent for which to return information about knowledge bases associated with it.</p>", "location": "uri", "locationName": "agentVersion"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in the response. If the total number of results is greater than this value, use the token returned in the response in the <code>nextToken</code> field when making another request to return the next batch of results.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the total number of results is greater than the <code>maxResults</code> value provided in the request, enter the token returned in the <code>nextToken</code> field in the response in this field to return the next batch of results.</p>"}}}, "ListAgentKnowledgeBasesResponse": {"type": "structure", "required": ["agentKnowledgeBaseSummaries"], "members": {"agentKnowledgeBaseSummaries": {"shape": "AgentKnowledgeBaseSummaries", "documentation": "<p>A list of objects, each of which contains information about a knowledge base associated with the agent.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the total number of results is greater than the <code>maxResults</code> value provided in the request, use this token when making another request in the <code>nextToken</code> field to return the next batch of results.</p>"}}}, "ListAgentVersionsRequest": {"type": "structure", "required": ["agentId"], "members": {"agentId": {"shape": "Id", "documentation": "<p>The unique identifier of the agent.</p>", "location": "uri", "locationName": "agentId"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in the response. If the total number of results is greater than this value, use the token returned in the response in the <code>nextToken</code> field when making another request to return the next batch of results.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the total number of results is greater than the <code>maxResults</code> value provided in the request, enter the token returned in the <code>nextToken</code> field in the response in this field to return the next batch of results.</p>"}}}, "ListAgentVersionsResponse": {"type": "structure", "required": ["agentVersionSummaries"], "members": {"agentVersionSummaries": {"shape": "AgentVersionSummaries", "documentation": "<p>A list of objects, each of which contains information about a version of the agent.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the total number of results is greater than the <code>maxResults</code> value provided in the request, use this token when making another request in the <code>nextToken</code> field to return the next batch of results.</p>"}}}, "ListAgentsRequest": {"type": "structure", "members": {"maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in the response. If the total number of results is greater than this value, use the token returned in the response in the <code>nextToken</code> field when making another request to return the next batch of results.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the total number of results is greater than the <code>maxResults</code> value provided in the request, enter the token returned in the <code>nextToken</code> field in the response in this field to return the next batch of results.</p>"}}}, "ListAgentsResponse": {"type": "structure", "required": ["agentSummaries"], "members": {"agentSummaries": {"shape": "AgentSummaries", "documentation": "<p>A list of objects, each of which contains information about an agent.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the total number of results is greater than the <code>maxResults</code> value provided in the request, use this token when making another request in the <code>nextToken</code> field to return the next batch of results.</p>"}}}, "ListDataSourcesRequest": {"type": "structure", "required": ["knowledgeBaseId"], "members": {"knowledgeBaseId": {"shape": "Id", "documentation": "<p>The unique identifier of the knowledge base for which to return a list of information.</p>", "location": "uri", "locationName": "knowledgeBaseId"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in the response. If the total number of results is greater than this value, use the token returned in the response in the <code>nextToken</code> field when making another request to return the next batch of results.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the total number of results is greater than the <code>maxResults</code> value provided in the request, enter the token returned in the <code>nextToken</code> field in the response in this field to return the next batch of results.</p>"}}}, "ListDataSourcesResponse": {"type": "structure", "required": ["dataSourceSummaries"], "members": {"dataSourceSummaries": {"shape": "DataSourceSummaries", "documentation": "<p>A list of objects, each of which contains information about a data source.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the total number of results is greater than the <code>maxResults</code> value provided in the request, use this token when making another request in the <code>nextToken</code> field to return the next batch of results.</p>"}}}, "ListFlowAliasesRequest": {"type": "structure", "required": ["flowIdentifier"], "members": {"flowIdentifier": {"shape": "FlowIdentifier", "documentation": "<p>The unique identifier of the flow for which aliases are being returned.</p>", "location": "uri", "locationName": "flowIdentifier"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in the response. If the total number of results is greater than this value, use the token returned in the response in the <code>nextToken</code> field when making another request to return the next batch of results.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the total number of results is greater than the <code>maxResults</code> value provided in the request, enter the token returned in the <code>nextToken</code> field in the response in this field to return the next batch of results.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListFlowAliasesResponse": {"type": "structure", "required": ["flowAliasSummaries"], "members": {"flowAliasSummaries": {"shape": "FlowAliasSummaries", "documentation": "<p>A list, each member of which contains information about an alias.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the total number of results is greater than the <code>maxResults</code> value provided in the request, use this token when making another request in the <code>nextToken</code> field to return the next batch of results.</p>"}}}, "ListFlowVersionsRequest": {"type": "structure", "required": ["flowIdentifier"], "members": {"flowIdentifier": {"shape": "FlowIdentifier", "documentation": "<p>The unique identifier of the flow.</p>", "location": "uri", "locationName": "flowIdentifier"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in the response. If the total number of results is greater than this value, use the token returned in the response in the <code>nextToken</code> field when making another request to return the next batch of results.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the total number of results is greater than the <code>maxResults</code> value provided in the request, enter the token returned in the <code>nextToken</code> field in the response in this field to return the next batch of results.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListFlowVersionsResponse": {"type": "structure", "required": ["flowVersionSummaries"], "members": {"flowVersionSummaries": {"shape": "FlowVersionSummaries", "documentation": "<p>A list, each member of which contains information about a flow.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the total number of results is greater than the <code>maxResults</code> value provided in the request, use this token when making another request in the <code>nextToken</code> field to return the next batch of results.</p>"}}}, "ListFlowsRequest": {"type": "structure", "members": {"maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in the response. If the total number of results is greater than this value, use the token returned in the response in the <code>nextToken</code> field when making another request to return the next batch of results.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the total number of results is greater than the <code>maxResults</code> value provided in the request, enter the token returned in the <code>nextToken</code> field in the response in this field to return the next batch of results.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListFlowsResponse": {"type": "structure", "required": ["flowSummaries"], "members": {"flowSummaries": {"shape": "FlowSummaries", "documentation": "<p>A list, each member of which contains information about a flow.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the total number of results is greater than the <code>maxResults</code> value provided in the request, use this token when making another request in the <code>nextToken</code> field to return the next batch of results.</p>"}}}, "ListIngestionJobsRequest": {"type": "structure", "required": ["dataSourceId", "knowledgeBaseId"], "members": {"dataSourceId": {"shape": "Id", "documentation": "<p>The unique identifier of the data source for the list of data ingestion jobs.</p>", "location": "uri", "locationName": "dataSourceId"}, "filters": {"shape": "IngestionJobFilters", "documentation": "<p>Contains information about the filters for filtering the data.</p>"}, "knowledgeBaseId": {"shape": "Id", "documentation": "<p>The unique identifier of the knowledge base for the list of data ingestion jobs.</p>", "location": "uri", "locationName": "knowledgeBaseId"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in the response. If the total number of results is greater than this value, use the token returned in the response in the <code>nextToken</code> field when making another request to return the next batch of results.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the total number of results is greater than the <code>maxResults</code> value provided in the request, enter the token returned in the <code>nextToken</code> field in the response in this field to return the next batch of results.</p>"}, "sortBy": {"shape": "IngestionJobSortBy", "documentation": "<p>Contains details about how to sort the data.</p>"}}}, "ListIngestionJobsResponse": {"type": "structure", "required": ["ingestionJobSummaries"], "members": {"ingestionJobSummaries": {"shape": "IngestionJobSummaries", "documentation": "<p>A list of data ingestion jobs with information about each job.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the total number of results is greater than the <code>maxResults</code> value provided in the request, use this token when making another request in the <code>nextToken</code> field to return the next batch of results.</p>"}}}, "ListKnowledgeBaseDocumentsRequest": {"type": "structure", "required": ["dataSourceId", "knowledgeBaseId"], "members": {"dataSourceId": {"shape": "Id", "documentation": "<p>The unique identifier of the data source that contains the documents.</p>", "location": "uri", "locationName": "dataSourceId"}, "knowledgeBaseId": {"shape": "Id", "documentation": "<p>The unique identifier of the knowledge base that is connected to the data source.</p>", "location": "uri", "locationName": "knowledgeBaseId"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in the response. If the total number of results is greater than this value, use the token returned in the response in the <code>nextToken</code> field when making another request to return the next batch of results.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the total number of results is greater than the <code>maxResults</code> value provided in the request, enter the token returned in the <code>nextToken</code> field in the response in this field to return the next batch of results.</p>"}}}, "ListKnowledgeBaseDocumentsResponse": {"type": "structure", "required": ["documentDetails"], "members": {"documentDetails": {"shape": "KnowledgeBaseDocumentDetails", "documentation": "<p>A list of objects, each of which contains information about the documents that were retrieved.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>If the total number of results is greater than the <code>maxResults</code> value provided in the request, use this token when making another request in the <code>nextToken</code> field to return the next batch of results.</p>"}}}, "ListKnowledgeBasesRequest": {"type": "structure", "members": {"maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in the response. If the total number of results is greater than this value, use the token returned in the response in the <code>nextToken</code> field when making another request to return the next batch of results.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the total number of results is greater than the <code>maxResults</code> value provided in the request, enter the token returned in the <code>nextToken</code> field in the response in this field to return the next batch of results.</p>"}}}, "ListKnowledgeBasesResponse": {"type": "structure", "required": ["knowledgeBaseSummaries"], "members": {"knowledgeBaseSummaries": {"shape": "KnowledgeBaseSummaries", "documentation": "<p>A list of knowledge bases with information about each knowledge base.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the total number of results is greater than the <code>maxResults</code> value provided in the request, use this token when making another request in the <code>nextToken</code> field to return the next batch of results.</p>"}}}, "ListPromptsRequest": {"type": "structure", "members": {"maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in the response. If the total number of results is greater than this value, use the token returned in the response in the <code>nextToken</code> field when making another request to return the next batch of results.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the total number of results is greater than the <code>maxResults</code> value provided in the request, enter the token returned in the <code>nextToken</code> field in the response in this field to return the next batch of results.</p>", "location": "querystring", "locationName": "nextToken"}, "promptIdentifier": {"shape": "PromptIdentifier", "documentation": "<p>The unique identifier of the prompt for whose versions you want to return information. Omit this field to list information about all prompts in an account.</p>", "location": "querystring", "locationName": "promptIdentifier"}}}, "ListPromptsResponse": {"type": "structure", "required": ["promptSummaries"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>If the total number of results is greater than the <code>maxResults</code> value provided in the request, use this token when making another request in the <code>nextToken</code> field to return the next batch of results.</p>"}, "promptSummaries": {"shape": "PromptSummaries", "documentation": "<p>A list, each member of which contains information about a prompt using Prompt management.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "TaggableResourcesArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource for which to list tags.</p>", "location": "uri", "locationName": "resourceArn"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"tags": {"shape": "TagsMap", "documentation": "<p>The key-value pairs for the tags associated with the resource.</p>"}}}, "LoopControllerFlowNodeConfiguration": {"type": "structure", "required": ["continueCondition"], "members": {"continueCondition": {"shape": "FlowCondition", "documentation": "<p>Specifies the condition that determines when the flow exits the DoWhile loop. The loop executes until this condition evaluates to true.</p>"}, "maxIterations": {"shape": "LoopControllerFlowNodeConfigurationMaxIterationsInteger", "documentation": "<p>Specifies the maximum number of times the DoWhile loop can iterate before the flow exits the loop.</p>"}}, "documentation": "<p>Contains configurations for the controller node of a DoWhile loop in the flow.</p>"}, "LoopControllerFlowNodeConfigurationMaxIterationsInteger": {"type": "integer", "box": true, "max": 1000, "min": 1}, "LoopFlowNodeConfiguration": {"type": "structure", "required": ["definition"], "members": {"definition": {"shape": "FlowDefinition", "documentation": "<p>The definition of the Do<PERSON>hile loop nodes and connections between nodes in the flow.</p>"}}, "documentation": "<p>Contains configurations for the nodes of a DoWhile loop in your flow.</p> <p>A DoWhile loop is made up of the following nodes:</p> <ul> <li> <p> <code>Loop</code> - The container node that holds the loop's flow definition. This node encompasses the entire loop structure.</p> </li> <li> <p> <code>LoopInput</code> - The entry point node for the loop. This node receives inputs from nodes outside the loop and from previous loop iterations.</p> </li> <li> <p>Body nodes - The processing nodes that execute within each loop iteration. These can be nodes for handling data in your flow, such as a prompt or Lambda function nodes. Some node types aren't supported inside a DoWhile loop body. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent_LoopIncompatibleNodeTypeFlowValidationDetails.html\">LoopIncompatibleNodeTypeFlowValidationDetails</a>.</p> </li> <li> <p> <code>LoopController</code> - The node that evaluates whether the loop should continue or exit based on a condition.</p> </li> </ul> <p>These nodes work together to create a loop that runs at least once and continues until a specified condition is met or a maximum number of iterations is reached.</p>"}, "LoopIncompatibleNodeTypeFlowValidationDetails": {"type": "structure", "required": ["incompatibleNodeName", "incompatibleNodeType", "node"], "members": {"incompatibleNodeName": {"shape": "FlowNodeName", "documentation": "<p>The node that's incompatible in the DoWhile loop.</p>"}, "incompatibleNodeType": {"shape": "IncompatibleLoopNodeType", "documentation": "<p>The node type of the incompatible node in the DoWhile loop. Some node types, like a condition node, aren't allowed in a DoWhile loop.</p>"}, "node": {"shape": "FlowNodeName", "documentation": "<p>The <code>Loop</code> container node that contains an incompatible node.</p>"}}, "documentation": "<p>Details about a flow that contains an incompatible node in a DoWhile loop.</p>"}, "LoopInputFlowNodeConfiguration": {"type": "structure", "members": {}, "documentation": "<p>Contains configurations for the input node of a DoWhile loop in the flow.</p>"}, "MalformedConditionExpressionFlowValidationDetails": {"type": "structure", "required": ["cause", "condition", "node"], "members": {"cause": {"shape": "ErrorMessage", "documentation": "<p>The error message describing why the condition expression is malformed.</p>"}, "condition": {"shape": "FlowConditionName", "documentation": "<p>The name of the malformed condition.</p>"}, "node": {"shape": "FlowNodeName", "documentation": "<p>The name of the node containing the malformed condition expression.</p>"}}, "documentation": "<p>Details about a malformed condition expression in a node.</p>"}, "MalformedNodeInputExpressionFlowValidationDetails": {"type": "structure", "required": ["cause", "input", "node"], "members": {"cause": {"shape": "ErrorMessage", "documentation": "<p>The error message describing why the input expression is malformed.</p>"}, "input": {"shape": "FlowNodeInputName", "documentation": "<p>The name of the input with the malformed expression.</p>"}, "node": {"shape": "FlowNodeName", "documentation": "<p>The name of the node containing the malformed input expression.</p>"}}, "documentation": "<p>Details about a malformed input expression in a node.</p>"}, "MaxRecentSessions": {"type": "integer", "box": true, "min": 1}, "MaxResults": {"type": "integer", "box": true, "max": 1000, "min": 1}, "MaximumLength": {"type": "integer", "box": true, "min": 0}, "MemoryConfiguration": {"type": "structure", "required": ["enabledMemoryTypes"], "members": {"enabledMemoryTypes": {"shape": "EnabledMemoryTypes", "documentation": "<p>The type of memory that is stored. </p>"}, "sessionSummaryConfiguration": {"shape": "SessionSummaryConfiguration", "documentation": "<p>Contains the configuration for SESSION_SUMMARY memory type enabled for the agent. </p>"}, "storageDays": {"shape": "StorageDays", "documentation": "<p>The number of days the agent is configured to retain the conversational context.</p>"}}, "documentation": "<p>Details of the memory configuration.</p>"}, "MemoryType": {"type": "string", "enum": ["SESSION_SUMMARY"]}, "Message": {"type": "structure", "required": ["content", "role"], "members": {"content": {"shape": "ContentBlocks", "documentation": "<p>The content in the message.</p>"}, "role": {"shape": "ConversationRole", "documentation": "<p>The role that the message belongs to.</p>"}}, "documentation": "<p>A message input or response from a model. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/prompt-management-create.html\">Create a prompt using Prompt management</a>.</p>"}, "Messages": {"type": "list", "member": {"shape": "Message"}}, "MetadataAttribute": {"type": "structure", "required": ["key", "value"], "members": {"key": {"shape": "Key", "documentation": "<p>The key of the metadata attribute.</p>"}, "value": {"shape": "MetadataAttributeValue", "documentation": "<p>Contains the value of the metadata attribute.</p>"}}, "documentation": "<p>Contains information about a metadata attribute.</p>"}, "MetadataAttributeValue": {"type": "structure", "required": ["type"], "members": {"booleanValue": {"shape": "Boolean", "documentation": "<p>The value of the Boolean metadata attribute.</p>"}, "numberValue": {"shape": "NumberValue", "documentation": "<p>The value of the numeric metadata attribute.</p>"}, "stringListValue": {"shape": "MetadataAttributeValueStringListValueList", "documentation": "<p>An array of strings that define the value of the metadata attribute.</p>"}, "stringValue": {"shape": "StringValue", "documentation": "<p>The value of the string metadata attribute.</p>"}, "type": {"shape": "MetadataValueType", "documentation": "<p>The type of the metadata attribute.</p>"}}, "documentation": "<p>Contains the value of the metadata attribute. Choose a <code>type</code> and include the field that corresponds to it.</p>"}, "MetadataAttributeValueStringListValueList": {"type": "list", "member": {"shape": "StringValue"}, "max": 10, "min": 1}, "MetadataConfigurationForReranking": {"type": "structure", "required": ["selectionMode"], "members": {"selectionMode": {"shape": "RerankingMetadataSelectionMode", "documentation": "<p>The mode for selecting metadata fields for reranking.</p>"}, "selectiveModeConfiguration": {"shape": "RerankingMetadataSelectiveModeConfiguration", "documentation": "<p>The configuration for selective metadata field inclusion or exclusion during reranking.</p>"}}, "documentation": "<p>Specifies how metadata fields should be handled during the reranking process.</p>"}, "MetadataSourceType": {"type": "string", "enum": ["IN_LINE_ATTRIBUTE", "S3_LOCATION"]}, "MetadataValueType": {"type": "string", "enum": ["BOOLEAN", "NUMBER", "STRING", "STRING_LIST"]}, "Microsoft365TenantId": {"type": "string", "max": 36, "min": 36, "pattern": "^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$"}, "MismatchedNodeInputTypeFlowValidationDetails": {"type": "structure", "required": ["expectedType", "input", "node"], "members": {"expectedType": {"shape": "FlowNodeIODataType", "documentation": "<p>The expected data type for the node input.</p>"}, "input": {"shape": "FlowNodeInputName", "documentation": "<p>The name of the input with the mismatched data type.</p>"}, "node": {"shape": "FlowNodeName", "documentation": "<p>The name of the node containing the input with the mismatched data type.</p>"}}, "documentation": "<p>Details about mismatched input data types in a node.</p>"}, "MismatchedNodeOutputTypeFlowValidationDetails": {"type": "structure", "required": ["expectedType", "node", "output"], "members": {"expectedType": {"shape": "FlowNodeIODataType", "documentation": "<p>The expected data type for the node output.</p>"}, "node": {"shape": "FlowNodeName", "documentation": "<p>The name of the node containing the output with the mismatched data type.</p>"}, "output": {"shape": "FlowNodeOutputName", "documentation": "<p>The name of the output with the mismatched data type.</p>"}}, "documentation": "<p>Details about mismatched output data types in a node.</p>"}, "MissingConnectionConfigurationFlowValidationDetails": {"type": "structure", "required": ["connection"], "members": {"connection": {"shape": "FlowConnectionName", "documentation": "<p>The name of the connection missing configuration.</p>"}}, "documentation": "<p>Details about a connection missing required configuration.</p>"}, "MissingDefaultConditionFlowValidationDetails": {"type": "structure", "required": ["node"], "members": {"node": {"shape": "FlowNodeName", "documentation": "<p>The name of the node missing the default condition.</p>"}}, "documentation": "<p>Details about a missing default condition in a conditional node.</p>"}, "MissingEndingNodesFlowValidationDetails": {"type": "structure", "members": {}, "documentation": "<p>Details about missing ending nodes (such as FlowOutputNode) in the flow.</p>"}, "MissingLoopControllerNodeFlowValidationDetails": {"type": "structure", "required": ["loopNode"], "members": {"loopNode": {"shape": "FlowNodeName", "documentation": "<p>The DoWhile loop in a flow that's missing a required <code>LoopController</code> node.</p>"}}, "documentation": "<p>Details about a flow that's missing a required <code>LoopController</code> node in a DoWhile loop.</p>"}, "MissingLoopInputNodeFlowValidationDetails": {"type": "structure", "required": ["loopNode"], "members": {"loopNode": {"shape": "FlowNodeName", "documentation": "<p>The DoWhile loop in a flow that's missing a required <code>LoopInput</code> node.</p>"}}, "documentation": "<p>Details about a flow that's missing a required <code>LoopInput</code> node in a DoWhile loop.</p>"}, "MissingNodeConfigurationFlowValidationDetails": {"type": "structure", "required": ["node"], "members": {"node": {"shape": "FlowNodeName", "documentation": "<p>The name of the node missing a required configuration.</p>"}}, "documentation": "<p>Details about a node missing a required configuration.</p>"}, "MissingNodeInputFlowValidationDetails": {"type": "structure", "required": ["input", "node"], "members": {"input": {"shape": "FlowNodeInputName", "documentation": "<p>The name of the missing input.</p>"}, "node": {"shape": "FlowNodeName", "documentation": "<p>The name of the node missing the required input.</p>"}}, "documentation": "<p>Details about a missing required input in a node.</p>"}, "MissingNodeOutputFlowValidationDetails": {"type": "structure", "required": ["node", "output"], "members": {"node": {"shape": "FlowNodeName", "documentation": "<p>The name of the node missing the required output.</p>"}, "output": {"shape": "FlowNodeOutputName", "documentation": "<p>The name of the missing output.</p>"}}, "documentation": "<p>Details about a missing required output in a node.</p>"}, "MissingStartingNodesFlowValidationDetails": {"type": "structure", "members": {}, "documentation": "<p>Details about missing starting nodes (such as FlowInputNode) in the flow.</p>"}, "ModelIdentifier": {"type": "string", "max": 2048, "min": 1, "pattern": "^(arn:aws(-[^:]{1,12})?:(bedrock|sagemaker):[a-z0-9-]{1,20}:([0-9]{12})?:([a-z-]+/)?)?([a-zA-Z0-9.-]{1,63}){0,2}(([:][a-z0-9-]{1,63}){0,2})?(/[a-z0-9]{1,12})?$"}, "MongoDbAtlasCollectionName": {"type": "string", "max": 63, "min": 0, "pattern": "^.*$"}, "MongoDbAtlasConfiguration": {"type": "structure", "required": ["collectionName", "credentialsSecretArn", "databaseName", "endpoint", "fieldMapping", "vectorIndexName"], "members": {"collectionName": {"shape": "MongoDbAtlasCollectionName", "documentation": "<p>The collection name of the knowledge base in MongoDB Atlas.</p>"}, "credentialsSecretArn": {"shape": "SecretArn", "documentation": "<p>The Amazon Resource Name (ARN) of the secret that you created in Secrets Manager that contains user credentials for your MongoDB Atlas cluster.</p>"}, "databaseName": {"shape": "MongoDbAtlasDatabaseName", "documentation": "<p>The database name in your MongoDB Atlas cluster for your knowledge base.</p>"}, "endpoint": {"shape": "MongoDbAtlasEndpoint", "documentation": "<p>The endpoint URL of your MongoDB Atlas cluster for your knowledge base.</p>"}, "endpointServiceName": {"shape": "MongoDbAtlasEndpointServiceName", "documentation": "<p>The name of the VPC endpoint service in your account that is connected to your MongoDB Atlas cluster.</p>"}, "fieldMapping": {"shape": "MongoDbAtlasFieldMapping", "documentation": "<p>Contains the names of the fields to which to map information about the vector store.</p>"}, "textIndexName": {"shape": "MongoDbAtlasIndexName", "documentation": "<p>The name of the text search index in the MongoDB collection. This is required for using the hybrid search feature.</p>"}, "vectorIndexName": {"shape": "MongoDbAtlasIndexName", "documentation": "<p>The name of the MongoDB Atlas vector search index.</p>"}}, "documentation": "<p>Contains details about the storage configuration of the knowledge base in MongoDB Atlas. </p>"}, "MongoDbAtlasDatabaseName": {"type": "string", "max": 63, "min": 0, "pattern": "^.*$"}, "MongoDbAtlasEndpoint": {"type": "string", "max": 2048, "min": 0, "pattern": "^.*$"}, "MongoDbAtlasEndpointServiceName": {"type": "string", "max": 255, "min": 1, "pattern": "^(?:arn:aws(?:-us-gov|-cn|-iso|-iso-[a-z])*:.+:.*:\\d+:.+/.+$|[a-zA-Z0-9*]+[a-zA-Z0-9._-]*)$"}, "MongoDbAtlasFieldMapping": {"type": "structure", "required": ["metadataField", "textField", "vectorField"], "members": {"metadataField": {"shape": "FieldName", "documentation": "<p>The name of the field in which Amazon Bedrock stores metadata about the vector store.</p>"}, "textField": {"shape": "FieldName", "documentation": "<p>The name of the field in which Amazon Bedrock stores the raw text from your data. The text is split according to the chunking strategy you choose.</p>"}, "vectorField": {"shape": "FieldName", "documentation": "<p>The name of the field in which Amazon Bedrock stores the vector embeddings for your data sources.</p>"}}, "documentation": "<p>Contains the names of the fields to which to map information about the vector store.</p>"}, "MongoDbAtlasIndexName": {"type": "string", "max": 2048, "min": 0, "pattern": "^.*$"}, "MultipleLoopControllerNodesFlowValidationDetails": {"type": "structure", "required": ["loopNode"], "members": {"loopNode": {"shape": "FlowNodeName", "documentation": "<p>The DoWhile loop in a flow that contains multiple <code>LoopController</code> nodes.</p>"}}, "documentation": "<p>Details about a flow that contains multiple <code>LoopController</code> nodes in a DoWhile loop.</p>"}, "MultipleLoopInputNodesFlowValidationDetails": {"type": "structure", "required": ["loopNode"], "members": {"loopNode": {"shape": "FlowNodeName", "documentation": "<p>The DoWhile loop in a flow that contains multiple <code>LoopInput</code> nodes.</p>"}}, "documentation": "<p>Details about a flow that contains multiple <code>LoopInput</code> nodes in a DoWhile loop.</p>"}, "MultipleNodeInputConnectionsFlowValidationDetails": {"type": "structure", "required": ["input", "node"], "members": {"input": {"shape": "FlowNodeInputName", "documentation": "<p>The name of the input with multiple connections to it.</p>"}, "node": {"shape": "FlowNodeName", "documentation": "<p>The name of the node containing the input with multiple connections.</p>"}}, "documentation": "<p>Details about multiple connections to a single node input.</p>"}, "Name": {"type": "string", "pattern": "^([0-9a-zA-Z][_-]?){1,100}$"}, "NaturalLanguageString": {"type": "string", "max": 1000, "min": 1}, "NeptuneAnalyticsConfiguration": {"type": "structure", "required": ["fieldMapping", "graphArn"], "members": {"fieldMapping": {"shape": "NeptuneAnalyticsFieldMapping", "documentation": "<p>Contains the names of the fields to which to map information about the vector store.</p>"}, "graphArn": {"shape": "GraphArn", "documentation": "<p>The Amazon Resource Name (ARN) of the Neptune Analytics vector store.</p>"}}, "documentation": "<p>Contains details about the storage configuration of the knowledge base in Amazon Neptune Analytics. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/knowledge-base-setup-neptune.html\">Create a vector index in Amazon Neptune Analytics</a>.</p>"}, "NeptuneAnalyticsFieldMapping": {"type": "structure", "required": ["metadataField", "textField"], "members": {"metadataField": {"shape": "FieldName", "documentation": "<p>The name of the field in which Amazon Bedrock stores metadata about the vector store.</p>"}, "textField": {"shape": "FieldName", "documentation": "<p>The name of the field in which Amazon Bedrock stores the raw text from your data. The text is split according to the chunking strategy you choose.</p>"}}, "documentation": "<p>Contains the names of the fields to which to map information about the vector store.</p>"}, "NextToken": {"type": "string", "max": 2048, "min": 1, "pattern": "^\\S*$"}, "NonBlankString": {"type": "string", "pattern": "^[\\s\\S]+$"}, "NonEmptyString": {"type": "string", "min": 1}, "NumberValue": {"type": "double", "box": true, "sensitive": true}, "NumericalVersion": {"type": "string", "pattern": "^[0-9]{1,5}$"}, "OpenSearchManagedClusterConfiguration": {"type": "structure", "required": ["domainArn", "domainEndpoint", "fieldMapping", "vectorIndexName"], "members": {"domainArn": {"shape": "OpenSearchManagedClusterDomainArn", "documentation": "<p>The Amazon Resource Name (ARN) of the OpenSearch domain.</p>"}, "domainEndpoint": {"shape": "OpenSearchManagedClusterDomainEndpoint", "documentation": "<p>The endpoint URL the OpenSearch domain.</p>"}, "fieldMapping": {"shape": "OpenSearchManagedClusterFieldMapping", "documentation": "<p>Contains the names of the fields to which to map information about the vector store.</p>"}, "vectorIndexName": {"shape": "OpenSearchManagedClusterIndexName", "documentation": "<p>The name of the vector store.</p>"}}, "documentation": "<p>Contains details about the Managed Cluster configuration of the knowledge base in Amazon OpenSearch Service. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/knowledge-base-setup-osm.html\">Create a vector index in OpenSearch Managed Cluster</a>.</p>"}, "OpenSearchManagedClusterDomainArn": {"type": "string", "max": 2048, "min": 0, "pattern": "^arn:aws(|-cn|-us-gov|-iso):es:[a-z]{2}(-gov)?-[a-z]+-\\d{1}:\\d{12}:domain/[a-z][a-z0-9-]{3,28}$"}, "OpenSearchManagedClusterDomainEndpoint": {"type": "string", "max": 2048, "min": 0, "pattern": "^https://.*$"}, "OpenSearchManagedClusterFieldMapping": {"type": "structure", "required": ["metadataField", "textField", "vectorField"], "members": {"metadataField": {"shape": "FieldName", "documentation": "<p>The name of the field in which Amazon Bedrock stores metadata about the vector store.</p>"}, "textField": {"shape": "FieldName", "documentation": "<p>The name of the field in which Amazon Bedrock stores the raw text from your data. The text is split according to the chunking strategy you choose.</p>"}, "vectorField": {"shape": "FieldName", "documentation": "<p>The name of the field in which Amazon Bedrock stores the vector embeddings for your data sources.</p>"}}, "documentation": "<p>Contains the names of the fields to which to map information about the vector store.</p>"}, "OpenSearchManagedClusterIndexName": {"type": "string", "max": 2048, "min": 1, "pattern": "^(?![\\-_+.])[a-z0-9][a-z0-9\\-_\\.]*$", "sensitive": true}, "OpenSearchServerlessCollectionArn": {"type": "string", "max": 2048, "min": 0, "pattern": "^arn:aws:aoss:[a-z]{2}(-gov)?-[a-z]+-\\d{1}:\\d{12}:collection/[a-z0-9-]{3,32}$"}, "OpenSearchServerlessConfiguration": {"type": "structure", "required": ["collectionArn", "fieldMapping", "vectorIndexName"], "members": {"collectionArn": {"shape": "OpenSearchServerlessCollectionArn", "documentation": "<p>The Amazon Resource Name (ARN) of the OpenSearch Service vector store.</p>"}, "fieldMapping": {"shape": "OpenSearchServerlessFieldMapping", "documentation": "<p>Contains the names of the fields to which to map information about the vector store.</p>"}, "vectorIndexName": {"shape": "OpenSearchServerlessIndexName", "documentation": "<p>The name of the vector store.</p>"}}, "documentation": "<p>Contains details about the storage configuration of the knowledge base in Amazon OpenSearch Service. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/knowledge-base-setup-oss.html\">Create a vector index in Amazon OpenSearch Service</a>.</p>"}, "OpenSearchServerlessFieldMapping": {"type": "structure", "required": ["metadataField", "textField", "vectorField"], "members": {"metadataField": {"shape": "FieldName", "documentation": "<p>The name of the field in which Amazon Bedrock stores metadata about the vector store.</p>"}, "textField": {"shape": "FieldName", "documentation": "<p>The name of the field in which Amazon Bedrock stores the raw text from your data. The text is split according to the chunking strategy you choose.</p>"}, "vectorField": {"shape": "FieldName", "documentation": "<p>The name of the field in which Amazon Bedrock stores the vector embeddings for your data sources.</p>"}}, "documentation": "<p>Contains the names of the fields to which to map information about the vector store.</p>"}, "OpenSearchServerlessIndexName": {"type": "string", "max": 2048, "min": 0, "pattern": "^.*$"}, "OrchestrationExecutor": {"type": "structure", "members": {"lambda": {"shape": "LambdaArn", "documentation": "<p> The Amazon Resource Name (ARN) of the Lambda function containing the business logic that is carried out upon invoking the action. </p>"}}, "documentation": "<p> Contains details about the Lambda function containing the orchestration logic carried out upon invoking the custom orchestration. </p>", "union": true}, "OrchestrationType": {"type": "string", "enum": ["DEFAULT", "CUSTOM_ORCHESTRATION"]}, "OutputFlowNodeConfiguration": {"type": "structure", "members": {}, "documentation": "<p>Contains configurations for an output flow node in the flow. You specify the data type expected for the input into the node in the <code>type</code> field and how to return the final output in the <code>expression</code> field.</p>"}, "ParameterDescription": {"type": "string", "max": 500, "min": 1}, "ParameterDetail": {"type": "structure", "required": ["type"], "members": {"description": {"shape": "ParameterDescription", "documentation": "<p>A description of the parameter. Helps the foundation model determine how to elicit the parameters from the user.</p>"}, "required": {"shape": "Boolean", "documentation": "<p>Whether the parameter is required for the agent to complete the function for action group invocation.</p>"}, "type": {"shape": "Type", "documentation": "<p>The data type of the parameter.</p>"}}, "documentation": "<p>Contains details about a parameter in a function for an action group.</p> <p>This data type is used in the following API operations:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent_CreateAgentActionGroup.html#API_agent_CreateAgentActionGroup_RequestSyntax\">CreateAgentActionGroup request</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent_CreateAgentActionGroup.html#API_agent_CreateAgentActionGroup_ResponseSyntax\">CreateAgentActionGroup response</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent_UpdateAgentActionGroup.html#API_agent_UpdateAgentActionGroup_RequestSyntax\">UpdateAgentActionGroup request</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent_UpdateAgentActionGroup.html#API_agent_UpdateAgentActionGroup_ResponseSyntax\">UpdateAgentActionGroup response</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent_GetAgentActionGroup.html#API_agent_GetAgentActionGroup_ResponseSyntax\">GetAgentActionGroup response</a> </p> </li> </ul>"}, "ParameterMap": {"type": "map", "key": {"shape": "Name"}, "value": {"shape": "ParameterDetail"}}, "ParsingConfiguration": {"type": "structure", "required": ["parsingStrategy"], "members": {"bedrockDataAutomationConfiguration": {"shape": "BedrockDataAutomationConfiguration", "documentation": "<p>If you specify <code>BEDROCK_DATA_AUTOMATION</code> as the parsing strategy for ingesting your data source, use this object to modify configurations for using the Amazon Bedrock Data Automation parser.</p>"}, "bedrockFoundationModelConfiguration": {"shape": "BedrockFoundationModelConfiguration", "documentation": "<p>If you specify <code>BEDROCK_FOUNDATION_MODEL</code> as the parsing strategy for ingesting your data source, use this object to modify configurations for using a foundation model to parse documents.</p>"}, "parsingStrategy": {"shape": "ParsingStrategy", "documentation": "<p>The parsing strategy for the data source.</p>"}}, "documentation": "<p>Settings for parsing document contents. If you exclude this field, the default parser converts the contents of each document into text before splitting it into chunks. Specify the parsing strategy to use in the <code>parsingStrategy</code> field and include the relevant configuration, or omit it to use the Amazon Bedrock default parser. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/kb-advanced-parsing.html\">Parsing options for your data source</a>.</p> <note> <p>If you specify <code>BEDROCK_DATA_AUTOMATION</code> or <code>BEDROCK_FOUNDATION_MODEL</code> and it fails to parse a file, the Amazon Bedrock default parser will be used instead.</p> </note>"}, "ParsingModality": {"type": "string", "enum": ["MULTIMODAL"]}, "ParsingPrompt": {"type": "structure", "required": ["parsingPromptText"], "members": {"parsingPromptText": {"shape": "ParsingPromptText", "documentation": "<p>Instructions for interpreting the contents of a document.</p>"}}, "documentation": "<p>Instructions for interpreting the contents of a document.</p>"}, "ParsingPromptText": {"type": "string", "max": 10000, "min": 1}, "ParsingStrategy": {"type": "string", "enum": ["BEDROCK_FOUNDATION_MODEL", "BEDROCK_DATA_AUTOMATION"]}, "PatternObjectFilter": {"type": "structure", "required": ["objectType"], "members": {"exclusionFilters": {"shape": "FilterList", "documentation": "<p>A list of one or more exclusion regular expression patterns to exclude certain object types that adhere to the pattern. If you specify an inclusion and exclusion filter/pattern and both match a document, the exclusion filter takes precedence and the document isn’t crawled.</p>"}, "inclusionFilters": {"shape": "FilterList", "documentation": "<p>A list of one or more inclusion regular expression patterns to include certain object types that adhere to the pattern. If you specify an inclusion and exclusion filter/pattern and both match a document, the exclusion filter takes precedence and the document isn’t crawled.</p>"}, "objectType": {"shape": "FilteredObjectType", "documentation": "<p>The supported object type or content type of the data source.</p>"}}, "documentation": "<p>The specific filters applied to your data source content. You can filter out or include certain content.</p>"}, "PatternObjectFilterConfiguration": {"type": "structure", "required": ["filters"], "members": {"filters": {"shape": "PatternObjectFilterList", "documentation": "<p>The configuration of specific filters applied to your data source content. You can filter out or include certain content.</p>"}}, "documentation": "<p>The configuration of filtering certain objects or content types of the data source.</p>"}, "PatternObjectFilterList": {"type": "list", "member": {"shape": "PatternObjectFilter"}, "max": 25, "min": 1, "sensitive": true}, "Payload": {"type": "string", "sensitive": true}, "PerformanceConfigLatency": {"type": "string", "enum": ["standard", "optimized"]}, "PerformanceConfiguration": {"type": "structure", "members": {"latency": {"shape": "PerformanceConfigLatency", "documentation": "<p>The latency optimization setting.</p>"}}, "documentation": "<p>The performance-related configuration options for the knowledge base retrieval and generation process.</p>"}, "PineconeConfiguration": {"type": "structure", "required": ["connectionString", "credentialsSecretArn", "fieldMapping"], "members": {"connectionString": {"shape": "PineconeConnectionString", "documentation": "<p>The endpoint URL for your index management page.</p>"}, "credentialsSecretArn": {"shape": "SecretArn", "documentation": "<p>The Amazon Resource Name (ARN) of the secret that you created in Secrets Manager that is linked to your Pinecone API key.</p>"}, "fieldMapping": {"shape": "PineconeFieldMapping", "documentation": "<p>Contains the names of the fields to which to map information about the vector store.</p>"}, "namespace": {"shape": "PineconeNamespace", "documentation": "<p>The namespace to be used to write new data to your database.</p>"}}, "documentation": "<p>Contains details about the storage configuration of the knowledge base in Pinecone. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/knowledge-base-setup-pinecone.html\">Create a vector index in Pinecone</a>.</p>"}, "PineconeConnectionString": {"type": "string", "max": 2048, "min": 0, "pattern": "^.*$"}, "PineconeFieldMapping": {"type": "structure", "required": ["metadataField", "textField"], "members": {"metadataField": {"shape": "FieldName", "documentation": "<p>The name of the field in which Amazon Bedrock stores metadata about the vector store.</p>"}, "textField": {"shape": "FieldName", "documentation": "<p>The name of the field in which Amazon Bedrock stores the raw text from your data. The text is split according to the chunking strategy you choose.</p>"}}, "documentation": "<p>Contains the names of the fields to which to map information about the vector store.</p>"}, "PineconeNamespace": {"type": "string", "max": 2048, "min": 0, "pattern": "^.*$"}, "PrepareAgentRequest": {"type": "structure", "required": ["agentId"], "members": {"agentId": {"shape": "Id", "documentation": "<p>The unique identifier of the agent for which to create a <code>DRAFT</code> version.</p>", "location": "uri", "locationName": "agentId"}}}, "PrepareAgentResponse": {"type": "structure", "required": ["agentId", "agentStatus", "agentVersion", "preparedAt"], "members": {"agentId": {"shape": "Id", "documentation": "<p>The unique identifier of the agent for which the <code>DRAFT</code> version was created.</p>"}, "agentStatus": {"shape": "AgentStatus", "documentation": "<p>The status of the <code>DRAFT</code> version and whether it is ready for use.</p>"}, "agentVersion": {"shape": "Version", "documentation": "<p>The version of the agent.</p>"}, "preparedAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the <code>DRAFT</code> version of the agent was last prepared.</p>"}}}, "PrepareFlowRequest": {"type": "structure", "required": ["flowIdentifier"], "members": {"flowIdentifier": {"shape": "FlowIdentifier", "documentation": "<p>The unique identifier of the flow.</p>", "location": "uri", "locationName": "flowIdentifier"}}}, "PrepareFlowResponse": {"type": "structure", "required": ["id", "status"], "members": {"id": {"shape": "FlowId", "documentation": "<p>The unique identifier of the flow.</p>"}, "status": {"shape": "FlowStatus", "documentation": "<p>The status of the flow. When you submit this request, the status will be <code>NotPrepared</code>. If preparation succeeds, the status becomes <code>Prepared</code>. If it fails, the status becomes <code>FAILED</code>.</p>"}}}, "PrimitiveLong": {"type": "long"}, "PromptAgentResource": {"type": "structure", "required": ["agentIdentifier"], "members": {"agentIdentifier": {"shape": "AgentAliasArn", "documentation": "<p>The ARN of the agent with which to use the prompt.</p>"}}, "documentation": "<p>Contains specifications for an Amazon Bedrock agent with which to use the prompt. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/prompt-management-create.html\">Create a prompt using Prompt management</a> and <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/agents.html\">Automate tasks in your application using conversational agents</a>.</p>", "sensitive": true}, "PromptArn": {"type": "string", "pattern": "^(arn:aws:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:prompt/[0-9a-zA-Z]{10}(?::[0-9]{1,5})?)$"}, "PromptConfiguration": {"type": "structure", "members": {"additionalModelRequestFields": {"shape": "Document", "documentation": "<p>If the Converse or ConverseStream operations support the model, <code>additionalModelRequestFields</code> contains additional inference parameters, beyond the base set of inference parameters in the <code>inferenceConfiguration</code> field. </p> <p>For more information, see <i>Inference request parameters and response fields for foundation models</i> in the Amazon Bedrock user guide.</p>"}, "basePromptTemplate": {"shape": "BasePromptTemplate", "documentation": "<p>Defines the prompt template with which to replace the default prompt template. You can use placeholder variables in the base prompt template to customize the prompt. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/prompt-placeholders.html\">Prompt template placeholder variables</a>. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/advanced-prompts-configure.html\">Configure the prompt templates</a>.</p>"}, "foundationModel": {"shape": "ModelIdentifier", "documentation": "<p>The agent's foundation model.</p>"}, "inferenceConfiguration": {"shape": "InferenceConfiguration", "documentation": "<p>Contains inference parameters to use when the agent invokes a foundation model in the part of the agent sequence defined by the <code>promptType</code>. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/model-parameters.html\">Inference parameters for foundation models</a>.</p>"}, "parserMode": {"shape": "CreationMode", "documentation": "<p>Specifies whether to override the default parser Lambda function when parsing the raw foundation model output in the part of the agent sequence defined by the <code>promptType</code>. If you set the field as <code>OVERRIDDEN</code>, the <code>overrideLambda</code> field in the <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent_PromptOverrideConfiguration.html\">PromptOverrideConfiguration</a> must be specified with the ARN of a Lambda function.</p>"}, "promptCreationMode": {"shape": "CreationMode", "documentation": "<p>Specifies whether to override the default prompt template for this <code>promptType</code>. Set this value to <code>OVERRIDDEN</code> to use the prompt that you provide in the <code>basePromptTemplate</code>. If you leave it as <code>DEFAULT</code>, the agent uses a default prompt template.</p>"}, "promptState": {"shape": "PromptState", "documentation": "<p>Specifies whether to allow the agent to carry out the step specified in the <code>promptType</code>. If you set this value to <code>DISABLED</code>, the agent skips that step. The default state for each <code>promptType</code> is as follows.</p> <ul> <li> <p> <code>PRE_PROCESSING</code> – <code>DISABLED</code> </p> </li> <li> <p> <code>ORCHESTRATION</code> – <code>ENABLED</code> </p> </li> <li> <p> <code>KNOWLEDGE_BASE_RESPONSE_GENERATION</code> – <code>ENABLED</code> </p> </li> <li> <p> <code>POST_PROCESSING</code> – <code>DISABLED</code> </p> </li> </ul>"}, "promptType": {"shape": "PromptType", "documentation": "<p>The step in the agent sequence that this prompt configuration applies to.</p>"}}, "documentation": "<p>Contains configurations to override a prompt template in one part of an agent sequence. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/advanced-prompts.html\">Advanced prompts</a>.</p>"}, "PromptConfigurations": {"type": "list", "member": {"shape": "PromptConfiguration"}, "max": 10, "min": 0}, "PromptDescription": {"type": "string", "max": 200, "min": 1}, "PromptFlowNodeConfiguration": {"type": "structure", "required": ["sourceConfiguration"], "members": {"guardrailConfiguration": {"shape": "GuardrailConfiguration", "documentation": "<p>Contains configurations for a guardrail to apply to the prompt in this node and the response generated from it.</p>"}, "sourceConfiguration": {"shape": "PromptFlowNodeSourceConfiguration", "documentation": "<p>Specifies whether the prompt is from Prompt management or defined inline.</p>"}}, "documentation": "<p>Contains configurations for a prompt node in the flow. You can use a prompt from Prompt management or you can define one in this node. If the prompt contains variables, the inputs into this node will fill in the variables. The output from this node is the response generated by the model. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/flows-nodes.html\">Node types in a flow</a> in the Amazon Bedrock User Guide.</p>"}, "PromptFlowNodeInlineConfiguration": {"type": "structure", "required": ["modelId", "templateConfiguration", "templateType"], "members": {"additionalModelRequestFields": {"shape": "Document", "documentation": "<p>Additional fields to be included in the model request for the Prompt node.</p>"}, "inferenceConfiguration": {"shape": "PromptInferenceConfiguration", "documentation": "<p>Contains inference configurations for the prompt.</p>"}, "modelId": {"shape": "PromptModelIdentifier", "documentation": "<p>The unique identifier of the model or <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/cross-region-inference.html\">inference profile</a> to run inference with.</p>"}, "templateConfiguration": {"shape": "PromptTemplateConfiguration", "documentation": "<p>Contains a prompt and variables in the prompt that can be replaced with values at runtime.</p>"}, "templateType": {"shape": "PromptTemplateType", "documentation": "<p>The type of prompt template.</p>"}}, "documentation": "<p>Contains configurations for a prompt defined inline in the node.</p>"}, "PromptFlowNodeResourceConfiguration": {"type": "structure", "required": ["promptArn"], "members": {"promptArn": {"shape": "PromptArn", "documentation": "<p>The Amazon Resource Name (ARN) of the prompt from Prompt management.</p>"}}, "documentation": "<p>Contains configurations for a prompt from Prompt management to use in a node.</p>"}, "PromptFlowNodeSourceConfiguration": {"type": "structure", "members": {"inline": {"shape": "PromptFlowNodeInlineConfiguration", "documentation": "<p>Contains configurations for a prompt that is defined inline</p>"}, "resource": {"shape": "PromptFlowNodeResourceConfiguration", "documentation": "<p>Contains configurations for a prompt from Prompt management.</p>"}}, "documentation": "<p>Contains configurations for a prompt and whether it is from Prompt management or defined inline.</p>", "union": true}, "PromptGenAiResource": {"type": "structure", "members": {"agent": {"shape": "PromptAgentResource", "documentation": "<p>Specifies an Amazon Bedrock agent with which to use the prompt.</p>"}}, "documentation": "<p>Contains specifications for a generative AI resource with which to use the prompt. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/prompt-management-create.html\">Create a prompt using Prompt management</a>.</p>", "sensitive": true, "union": true}, "PromptId": {"type": "string", "pattern": "^[0-9a-zA-Z]{10}$"}, "PromptIdentifier": {"type": "string", "pattern": "^([0-9a-zA-Z]{10})|(arn:aws:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:prompt/[0-9a-zA-Z]{10})(?::[0-9]{1,5})?$"}, "PromptInferenceConfiguration": {"type": "structure", "members": {"text": {"shape": "PromptModelInferenceConfiguration", "documentation": "<p>Contains inference configurations for a text prompt.</p>"}}, "documentation": "<p>Contains inference configurations for the prompt.</p>", "union": true}, "PromptInputVariable": {"type": "structure", "members": {"name": {"shape": "PromptInputVariableName", "documentation": "<p>The name of the variable.</p>"}}, "documentation": "<p>Contains information about a variable in the prompt.</p>"}, "PromptInputVariableName": {"type": "string", "pattern": "^([0-9a-zA-Z][_-]?){1,100}$"}, "PromptInputVariablesList": {"type": "list", "member": {"shape": "PromptInputVariable"}, "max": 20, "min": 0, "sensitive": true}, "PromptMetadataEntry": {"type": "structure", "required": ["key", "value"], "members": {"key": {"shape": "PromptMetadataKey", "documentation": "<p>The key of a metadata tag for a prompt variant.</p>"}, "value": {"shape": "PromptMetadataValue", "documentation": "<p>The value of a metadata tag for a prompt variant.</p>"}}, "documentation": "<p>Contains a key-value pair that defines a metadata tag and value to attach to a prompt variant. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/prompt-management-create.html\">Create a prompt using Prompt management</a>.</p>", "sensitive": true}, "PromptMetadataKey": {"type": "string", "max": 128, "min": 1, "pattern": "^[a-zA-Z0-9\\s._:/=+@-]*$", "sensitive": true}, "PromptMetadataList": {"type": "list", "member": {"shape": "PromptMetadataEntry"}, "max": 50, "min": 0, "sensitive": true}, "PromptMetadataValue": {"type": "string", "max": 1024, "min": 0, "pattern": "^[a-zA-Z0-9\\s._:/=+@-]*$", "sensitive": true}, "PromptModelIdentifier": {"type": "string", "max": 2048, "min": 1, "pattern": "^(arn:aws(-[^:]{1,12})?:(bedrock|sagemaker):[a-z0-9-]{1,20}:([0-9]{12})?:([a-z-]+/)?)?([a-zA-Z0-9.-]{1,63}){0,2}(([:][a-z0-9-]{1,63}){0,2})?(/[a-z0-9]{1,12})?$"}, "PromptModelInferenceConfiguration": {"type": "structure", "members": {"maxTokens": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The maximum number of tokens to return in the response.</p>"}, "stopSequences": {"shape": "StopSequences", "documentation": "<p>A list of strings that define sequences after which the model will stop generating.</p>"}, "temperature": {"shape": "Temperature", "documentation": "<p>Controls the randomness of the response. Choose a lower value for more predictable outputs and a higher value for more surprising outputs.</p>"}, "topP": {"shape": "TopP", "documentation": "<p>The percentage of most-likely candidates that the model considers for the next token.</p>"}}, "documentation": "<p>Contains inference configurations related to model inference for a prompt. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/inference-parameters.html\">Inference parameters</a>.</p>"}, "PromptName": {"type": "string", "pattern": "^([0-9a-zA-Z][_-]?){1,100}$"}, "PromptOverrideConfiguration": {"type": "structure", "required": ["promptConfigurations"], "members": {"overrideLambda": {"shape": "LambdaArn", "documentation": "<p>The ARN of the Lambda function to use when parsing the raw foundation model output in parts of the agent sequence. If you specify this field, at least one of the <code>promptConfigurations</code> must contain a <code>parserMode</code> value that is set to <code>OVERRIDDEN</code>. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/lambda-parser.html\">Parser Lambda function in Amazon Bedrock Agents</a>.</p>"}, "promptConfigurations": {"shape": "PromptConfigurations", "documentation": "<p>Contains configurations to override a prompt template in one part of an agent sequence. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/advanced-prompts.html\">Advanced prompts</a>.</p>"}}, "documentation": "<p>Contains configurations to override prompts in different parts of an agent sequence. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/advanced-prompts.html\">Advanced prompts</a>.</p>", "sensitive": true}, "PromptState": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "PromptSummaries": {"type": "list", "member": {"shape": "PromptSummary"}, "max": 10, "min": 0}, "PromptSummary": {"type": "structure", "required": ["arn", "createdAt", "id", "name", "updatedAt", "version"], "members": {"arn": {"shape": "PromptArn", "documentation": "<p>The Amazon Resource Name (ARN) of the prompt or the prompt version (if you specified a version in the request).</p>"}, "createdAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the prompt was created.</p>"}, "description": {"shape": "PromptDescription", "documentation": "<p>The description of the prompt.</p>"}, "id": {"shape": "PromptId", "documentation": "<p>The unique identifier of the prompt.</p>"}, "name": {"shape": "PromptName", "documentation": "<p>The name of the prompt.</p>"}, "updatedAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the prompt was last updated.</p>"}, "version": {"shape": "Version", "documentation": "<p>The version of the prompt that this summary applies to.</p>"}}, "documentation": "<p>Contains information about a prompt in your Prompt management tool.</p> <p>This data type is used in the following API operations:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent_ListPrompts.html#API_agent_ListPrompts_ResponseSyntax\">ListPrompts response</a> </p> </li> </ul>"}, "PromptTemplateConfiguration": {"type": "structure", "members": {"chat": {"shape": "ChatPromptTemplateConfiguration", "documentation": "<p>Contains configurations to use the prompt in a conversational format.</p>"}, "text": {"shape": "TextPromptTemplateConfiguration", "documentation": "<p>Contains configurations for the text in a message for a prompt.</p>"}}, "documentation": "<p>Contains the message for a prompt. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/prompt-management.html\">Construct and store reusable prompts with Prompt management in Amazon Bedrock</a>.</p>", "union": true}, "PromptTemplateType": {"type": "string", "enum": ["TEXT", "CHAT"]}, "PromptType": {"type": "string", "enum": ["PRE_PROCESSING", "ORCHESTRATION", "POST_PROCESSING", "KNOWLEDGE_BASE_RESPONSE_GENERATION", "MEMORY_SUMMARIZATION"]}, "PromptVariant": {"type": "structure", "required": ["name", "templateConfiguration", "templateType"], "members": {"additionalModelRequestFields": {"shape": "Document", "documentation": "<p>Contains model-specific inference configurations that aren't in the <code>inferenceConfiguration</code> field. To see model-specific inference parameters, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/model-parameters.html\">Inference request parameters and response fields for foundation models</a>.</p>"}, "genAiResource": {"shape": "PromptGenAiResource", "documentation": "<p>Specifies a generative AI resource with which to use the prompt.</p>"}, "inferenceConfiguration": {"shape": "PromptInferenceConfiguration", "documentation": "<p>Contains inference configurations for the prompt variant.</p>"}, "metadata": {"shape": "PromptMetadataList", "documentation": "<p>An array of objects, each containing a key-value pair that defines a metadata tag and value to attach to a prompt variant.</p>"}, "modelId": {"shape": "PromptModelIdentifier", "documentation": "<p>The unique identifier of the model or <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/cross-region-inference.html\">inference profile</a> with which to run inference on the prompt.</p>"}, "name": {"shape": "PromptVariantName", "documentation": "<p>The name of the prompt variant.</p>"}, "templateConfiguration": {"shape": "PromptTemplateConfiguration", "documentation": "<p>Contains configurations for the prompt template.</p>"}, "templateType": {"shape": "PromptTemplateType", "documentation": "<p>The type of prompt template to use.</p>"}}, "documentation": "<p>Contains details about a variant of the prompt.</p>", "sensitive": true}, "PromptVariantList": {"type": "list", "member": {"shape": "Prompt<PERSON><PERSON><PERSON>"}, "max": 1, "min": 0, "sensitive": true}, "PromptVariantName": {"type": "string", "pattern": "^([0-9a-zA-Z][_-]?){1,100}$"}, "ProvisionedModelIdentifier": {"type": "string", "max": 2048, "min": 1, "pattern": "^((([0-9a-zA-Z][_-]?){1,63})|(arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:provisioned-model/[a-z0-9]{12}))$"}, "QueryEngineType": {"type": "string", "enum": ["REDSHIFT"]}, "QueryExecutionTimeoutSeconds": {"type": "integer", "box": true, "max": 200, "min": 1}, "QueryGenerationColumn": {"type": "structure", "members": {"description": {"shape": "DescriptionString", "documentation": "<p>A description of the column that helps the query engine understand the contents of the column.</p>"}, "inclusion": {"shape": "IncludeExclude", "documentation": "<p>Specifies whether to include or exclude the column during query generation. If you specify <code>EXCLUDE</code>, the column will be ignored. If you specify <code>INCLUDE</code>, all other columns in the table will be ignored.</p>"}, "name": {"shape": "QueryGenerationColumnName", "documentation": "<p>The name of the column for which the other fields in this object apply.</p>"}}, "documentation": "<p>Contains information about a column in the current table for the query engine to consider.</p>"}, "QueryGenerationColumnName": {"type": "string", "max": 127, "min": 1}, "QueryGenerationColumns": {"type": "list", "member": {"shape": "QueryGenerationColumn"}}, "QueryGenerationConfiguration": {"type": "structure", "members": {"executionTimeoutSeconds": {"shape": "QueryExecutionTimeoutSeconds", "documentation": "<p>The time after which query generation will time out.</p>"}, "generationContext": {"shape": "QueryGenerationContext", "documentation": "<p>Specifies configurations for context to use during query generation.</p>"}}, "documentation": "<p>Contains configurations for query generation. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/knowledge-base-build-structured.html\">Build a knowledge base by connecting to a structured data source</a> in the Amazon Bedrock User Guide..</p>"}, "QueryGenerationContext": {"type": "structure", "members": {"curatedQueries": {"shape": "CuratedQueries", "documentation": "<p>An array of objects, each of which defines information about example queries to help the query engine generate appropriate SQL queries.</p>"}, "tables": {"shape": "QueryGenerationTables", "documentation": "<p>An array of objects, each of which defines information about a table in the database.</p>"}}, "documentation": "<p>&gt;Contains configurations for context to use during query generation.</p>", "sensitive": true}, "QueryGenerationTable": {"type": "structure", "required": ["name"], "members": {"columns": {"shape": "QueryGenerationColumns", "documentation": "<p>An array of objects, each of which defines information about a column in the table.</p>"}, "description": {"shape": "DescriptionString", "documentation": "<p>A description of the table that helps the query engine understand the contents of the table.</p>"}, "inclusion": {"shape": "IncludeExclude", "documentation": "<p>Specifies whether to include or exclude the table during query generation. If you specify <code>EXCLUDE</code>, the table will be ignored. If you specify <code>INCLUDE</code>, all other tables will be ignored.</p>"}, "name": {"shape": "QueryGenerationTableName", "documentation": "<p>The name of the table for which the other fields in this object apply.</p>"}}, "documentation": "<p>Contains information about a table for the query engine to consider.</p>"}, "QueryGenerationTableName": {"type": "string", "pattern": "^.*\\..*\\..*$"}, "QueryGenerationTables": {"type": "list", "member": {"shape": "QueryGenerationTable"}, "max": 50, "min": 0}, "RdsArn": {"type": "string", "pattern": "^arn:aws(|-cn|-us-gov):rds:[a-zA-Z0-9-]*:[0-9]{12}:cluster:[a-zA-Z0-9-]{1,63}$"}, "RdsConfiguration": {"type": "structure", "required": ["credentialsSecretArn", "databaseName", "fieldMapping", "resourceArn", "tableName"], "members": {"credentialsSecretArn": {"shape": "SecretArn", "documentation": "<p>The Amazon Resource Name (ARN) of the secret that you created in Secrets Manager that is linked to your Amazon RDS database.</p>"}, "databaseName": {"shape": "RdsDatabaseName", "documentation": "<p>The name of your Amazon RDS database.</p>"}, "fieldMapping": {"shape": "RdsFieldMapping", "documentation": "<p>Contains the names of the fields to which to map information about the vector store.</p>"}, "resourceArn": {"shape": "RdsArn", "documentation": "<p>The Amazon Resource Name (ARN) of the vector store.</p>"}, "tableName": {"shape": "RdsTableName", "documentation": "<p>The name of the table in the database.</p>"}}, "documentation": "<p>Contains details about the storage configuration of the knowledge base in Amazon RDS. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/knowledge-base-setup-rds.html\">Create a vector index in Amazon RDS</a>.</p>"}, "RdsDatabaseName": {"type": "string", "max": 63, "min": 0, "pattern": "^[a-zA-Z0-9_\\-]+$"}, "RdsFieldMapping": {"type": "structure", "required": ["metadataField", "primaryKeyField", "textField", "vectorField"], "members": {"customMetadataField": {"shape": "ColumnName", "documentation": "<p>Provide a name for the universal metadata field where Amazon Bedrock will store any custom metadata from your data source.</p>"}, "metadataField": {"shape": "ColumnName", "documentation": "<p>The name of the field in which Amazon Bedrock stores metadata about the vector store.</p>"}, "primaryKeyField": {"shape": "ColumnName", "documentation": "<p>The name of the field in which Amazon Bedrock stores the ID for each entry.</p>"}, "textField": {"shape": "ColumnName", "documentation": "<p>The name of the field in which Amazon Bedrock stores the raw text from your data. The text is split according to the chunking strategy you choose.</p>"}, "vectorField": {"shape": "ColumnName", "documentation": "<p>The name of the field in which Amazon Bedrock stores the vector embeddings for your data sources.</p>"}}, "documentation": "<p>Contains the names of the fields to which to map information about the vector store.</p>"}, "RdsTableName": {"type": "string", "max": 63, "min": 0, "pattern": "^[a-zA-Z0-9_\\.\\-]+$"}, "RecommendedAction": {"type": "string", "max": 2048, "min": 0}, "RecommendedActions": {"type": "list", "member": {"shape": "RecommendedAction"}, "max": 2048, "min": 0}, "RedisEnterpriseCloudConfiguration": {"type": "structure", "required": ["credentialsSecretArn", "endpoint", "fieldMapping", "vectorIndexName"], "members": {"credentialsSecretArn": {"shape": "SecretArn", "documentation": "<p>The Amazon Resource Name (ARN) of the secret that you created in Secrets Manager that is linked to your Redis Enterprise Cloud database.</p>"}, "endpoint": {"shape": "RedisEnterpriseCloudEndpoint", "documentation": "<p>The endpoint URL of the Redis Enterprise Cloud database.</p>"}, "fieldMapping": {"shape": "RedisEnterpriseCloudFieldMapping", "documentation": "<p>Contains the names of the fields to which to map information about the vector store.</p>"}, "vectorIndexName": {"shape": "RedisEnterpriseCloudIndexName", "documentation": "<p>The name of the vector index.</p>"}}, "documentation": "<p>Contains details about the storage configuration of the knowledge base in Redis Enterprise Cloud. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/knowledge-base-setup-oss.html\">Create a vector index in Redis Enterprise Cloud</a>.</p>"}, "RedisEnterpriseCloudEndpoint": {"type": "string", "max": 2048, "min": 0, "pattern": "^.*$"}, "RedisEnterpriseCloudFieldMapping": {"type": "structure", "required": ["metadataField", "textField", "vectorField"], "members": {"metadataField": {"shape": "FieldName", "documentation": "<p>The name of the field in which Amazon Bedrock stores metadata about the vector store.</p>"}, "textField": {"shape": "FieldName", "documentation": "<p>The name of the field in which Amazon Bedrock stores the raw text from your data. The text is split according to the chunking strategy you choose.</p>"}, "vectorField": {"shape": "FieldName", "documentation": "<p>The name of the field in which Amazon Bedrock stores the vector embeddings for your data sources.</p>"}}, "documentation": "<p>Contains the names of the fields to which to map information about the vector store.</p>"}, "RedisEnterpriseCloudIndexName": {"type": "string", "max": 2048, "min": 0, "pattern": "^.*$"}, "RedshiftClusterIdentifier": {"type": "string", "max": 63, "min": 1}, "RedshiftConfiguration": {"type": "structure", "required": ["queryEngineConfiguration", "storageConfigurations"], "members": {"queryEngineConfiguration": {"shape": "RedshiftQueryEngineConfiguration", "documentation": "<p>Specifies configurations for an Amazon Redshift query engine.</p>"}, "queryGenerationConfiguration": {"shape": "QueryGenerationConfiguration", "documentation": "<p>Specifies configurations for generating queries.</p>"}, "storageConfigurations": {"shape": "RedshiftQueryEngineStorageConfigurations", "documentation": "<p>Specifies configurations for Amazon Redshift database storage.</p>"}}, "documentation": "<p>Contains configurations for an Amazon Redshift database. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/knowledge-base-build-structured.html\">Build a knowledge base by connecting to a structured data source</a> in the Amazon Bedrock User Guide.</p>"}, "RedshiftDatabase": {"type": "string", "max": 200, "min": 1}, "RedshiftProvisionedAuthConfiguration": {"type": "structure", "required": ["type"], "members": {"databaseUser": {"shape": "String", "documentation": "<p>The database username for authentication to an Amazon Redshift provisioned data warehouse.</p>"}, "type": {"shape": "RedshiftProvisionedAuthType", "documentation": "<p>The type of authentication to use.</p>"}, "usernamePasswordSecretArn": {"shape": "SecretArn", "documentation": "<p>The ARN of an Secrets Manager secret for authentication.</p>"}}, "documentation": "<p>Contains configurations for authentication to an Amazon Redshift provisioned data warehouse. Specify the type of authentication to use in the <code>type</code> field and include the corresponding field. If you specify IAM authentication, you don't need to include another field.</p>"}, "RedshiftProvisionedAuthType": {"type": "string", "enum": ["IAM", "USERNAME_PASSWORD", "USERNAME"]}, "RedshiftProvisionedConfiguration": {"type": "structure", "required": ["authConfiguration", "clusterIdentifier"], "members": {"authConfiguration": {"shape": "RedshiftProvisionedAuthConfiguration", "documentation": "<p>Specifies configurations for authentication to Amazon Redshift.</p>"}, "clusterIdentifier": {"shape": "RedshiftClusterIdentifier", "documentation": "<p>The ID of the Amazon Redshift cluster.</p>"}}, "documentation": "<p>Contains configurations for a provisioned Amazon Redshift query engine.</p>"}, "RedshiftQueryEngineAwsDataCatalogStorageConfiguration": {"type": "structure", "required": ["tableNames"], "members": {"tableNames": {"shape": "AwsDataCatalogTableNames", "documentation": "<p>A list of names of the tables to use.</p>"}}, "documentation": "<p>Contains configurations for storage in Glue Data Catalog.</p>"}, "RedshiftQueryEngineConfiguration": {"type": "structure", "required": ["type"], "members": {"provisionedConfiguration": {"shape": "RedshiftProvisionedConfiguration", "documentation": "<p>Specifies configurations for a provisioned Amazon Redshift query engine.</p>"}, "serverlessConfiguration": {"shape": "RedshiftServerlessConfiguration", "documentation": "<p>Specifies configurations for a serverless Amazon Redshift query engine.</p>"}, "type": {"shape": "RedshiftQueryEngineType", "documentation": "<p>The type of query engine.</p>"}}, "documentation": "<p>Contains configurations for an Amazon Redshift query engine. Specify the type of query engine in <code>type</code> and include the corresponding field. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/knowledge-base-build-structured.html\">Build a knowledge base by connecting to a structured data source</a> in the Amazon Bedrock User Guide.</p>"}, "RedshiftQueryEngineRedshiftStorageConfiguration": {"type": "structure", "required": ["databaseName"], "members": {"databaseName": {"shape": "RedshiftDatabase", "documentation": "<p>The name of the Amazon Redshift database.</p>"}}, "documentation": "<p>Contains configurations for storage in Amazon Redshift.</p>"}, "RedshiftQueryEngineStorageConfiguration": {"type": "structure", "required": ["type"], "members": {"awsDataCatalogConfiguration": {"shape": "RedshiftQueryEngineAwsDataCatalogStorageConfiguration", "documentation": "<p>Specifies configurations for storage in Glue Data Catalog.</p>"}, "redshiftConfiguration": {"shape": "RedshiftQueryEngineRedshiftStorageConfiguration", "documentation": "<p>Specifies configurations for storage in Amazon Redshift.</p>"}, "type": {"shape": "RedshiftQueryEngineStorageType", "documentation": "<p>The data storage service to use.</p>"}}, "documentation": "<p>Contains configurations for Amazon Redshift data storage. Specify the data storage service to use in the <code>type</code> field and include the corresponding field. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/knowledge-base-build-structured.html\">Build a knowledge base by connecting to a structured data source</a> in the Amazon Bedrock User Guide.</p>"}, "RedshiftQueryEngineStorageConfigurations": {"type": "list", "member": {"shape": "RedshiftQueryEngineStorageConfiguration"}, "max": 1, "min": 1}, "RedshiftQueryEngineStorageType": {"type": "string", "enum": ["REDSHIFT", "AWS_DATA_CATALOG"]}, "RedshiftQueryEngineType": {"type": "string", "enum": ["SERVERLESS", "PROVISIONED"]}, "RedshiftServerlessAuthConfiguration": {"type": "structure", "required": ["type"], "members": {"type": {"shape": "RedshiftServerlessAuthType", "documentation": "<p>The type of authentication to use.</p>"}, "usernamePasswordSecretArn": {"shape": "SecretArn", "documentation": "<p>The ARN of an Secrets Manager secret for authentication.</p>"}}, "documentation": "<p>Specifies configurations for authentication to a Redshift Serverless. Specify the type of authentication to use in the <code>type</code> field and include the corresponding field. If you specify IAM authentication, you don't need to include another field.</p>"}, "RedshiftServerlessAuthType": {"type": "string", "enum": ["IAM", "USERNAME_PASSWORD"]}, "RedshiftServerlessConfiguration": {"type": "structure", "required": ["authConfiguration", "workgroupArn"], "members": {"authConfiguration": {"shape": "RedshiftServerlessAuthConfiguration", "documentation": "<p>Specifies configurations for authentication to an Amazon Redshift provisioned data warehouse.</p>"}, "workgroupArn": {"shape": "WorkgroupArn", "documentation": "<p>The ARN of the Amazon Redshift workgroup.</p>"}}, "documentation": "<p>Contains configurations for authentication to Amazon Redshift Serverless.</p>"}, "RelayConversationHistory": {"type": "string", "enum": ["TO_COLLABORATOR", "DISABLED"]}, "RequireConfirmation": {"type": "string", "documentation": "<p>Whether the action requires user confirmation.</p>", "enum": ["ENABLED", "DISABLED"]}, "RerankingMetadataSelectionMode": {"type": "string", "enum": ["SELECTIVE", "ALL"]}, "RerankingMetadataSelectiveModeConfiguration": {"type": "structure", "members": {"fieldsToExclude": {"shape": "FieldsForReranking", "documentation": "<p>Specifies the metadata fields to exclude from the reranking process.</p>"}, "fieldsToInclude": {"shape": "FieldsForReranking", "documentation": "<p>Specifies the metadata fields to include in the reranking process.</p>"}}, "documentation": "<p>Configures the metadata fields to include or exclude during the reranking process when using selective mode.</p>", "union": true}, "ResourceNotFoundException": {"type": "structure", "members": {"message": {"shape": "NonBlankString"}}, "documentation": "<p>The specified resource Amazon Resource Name (ARN) was not found. Check the Amazon Resource Name (ARN) and try your request again.</p>", "error": {"httpStatusCode": 404, "senderFault": true}, "exception": true}, "RetrievalFlowNodeConfiguration": {"type": "structure", "required": ["serviceConfiguration"], "members": {"serviceConfiguration": {"shape": "RetrievalFlowNodeServiceConfiguration", "documentation": "<p>Contains configurations for the service to use for retrieving data to return as the output from the node.</p>"}}, "documentation": "<p>Contains configurations for a Retrieval node in a flow. This node retrieves data from the Amazon S3 location that you specify and returns it as the output.</p>"}, "RetrievalFlowNodeS3Configuration": {"type": "structure", "required": ["bucketName"], "members": {"bucketName": {"shape": "S3BucketName", "documentation": "<p>The name of the Amazon S3 bucket from which to retrieve data.</p>"}}, "documentation": "<p>Contains configurations for the Amazon S3 location from which to retrieve data to return as the output from the node.</p>"}, "RetrievalFlowNodeServiceConfiguration": {"type": "structure", "members": {"s3": {"shape": "RetrievalFlowNodeS3Configuration", "documentation": "<p>Contains configurations for the Amazon S3 location from which to retrieve data to return as the output from the node.</p>"}}, "documentation": "<p>Contains configurations for the service to use for retrieving data to return as the output from the node.</p>", "union": true}, "S3BucketArn": {"type": "string", "max": 2048, "min": 1, "pattern": "^arn:aws(|-cn|-us-gov):s3:::[a-z0-9][a-z0-9.-]{1,61}[a-z0-9]$"}, "S3BucketName": {"type": "string", "max": 63, "min": 3, "pattern": "^[a-z0-9][\\.\\-a-z0-9]{1,61}[a-z0-9]$"}, "S3BucketUri": {"type": "string", "max": 2048, "min": 1, "pattern": "^s3://.{1,128}$"}, "S3Content": {"type": "structure", "required": ["s3Location"], "members": {"s3Location": {"shape": "S3Location", "documentation": "<p>The S3 location of the file containing the content to ingest.</p>"}}, "documentation": "<p>Contains information about the content to ingest into a knowledge base connected to an Amazon S3 data source.</p>"}, "S3DataSourceConfiguration": {"type": "structure", "required": ["bucketArn"], "members": {"bucketArn": {"shape": "S3BucketArn", "documentation": "<p>The Amazon Resource Name (ARN) of the S3 bucket that contains your data.</p>"}, "bucketOwnerAccountId": {"shape": "BucketOwnerAccountId", "documentation": "<p>The account ID for the owner of the S3 bucket.</p>"}, "inclusionPrefixes": {"shape": "S3Prefixes", "documentation": "<p>A list of S3 prefixes to include certain files or content. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/using-prefixes.html\">Organizing objects using prefixes</a>.</p>"}}, "documentation": "<p>The configuration information to connect to Amazon S3 as your data source.</p>"}, "S3Identifier": {"type": "structure", "members": {"s3BucketName": {"shape": "S3BucketName", "documentation": "<p>The name of the S3 bucket.</p>"}, "s3ObjectKey": {"shape": "S3ObjectKey", "documentation": "<p>The S3 object key for the S3 resource.</p>"}}, "documentation": "<p>The identifier information for an Amazon S3 bucket.</p>"}, "S3Location": {"type": "structure", "required": ["uri"], "members": {"uri": {"shape": "S3BucketUri", "documentation": "<p>The location's URI. For example, <code>s3://my-bucket/chunk-processor/</code>.</p>"}}, "documentation": "<p>An Amazon S3 location.</p>"}, "S3ObjectKey": {"type": "string", "max": 1024, "min": 1, "pattern": "^[\\.\\-\\!\\*\\_\\'\\(\\)a-zA-Z0-9][\\.\\-\\!\\*\\_\\'\\(\\)\\/a-zA-Z0-9]*$"}, "S3ObjectUri": {"type": "string", "max": 1024, "min": 1, "pattern": "^s3://[a-z0-9][a-z0-9.-]{1,61}[a-z0-9]/.{1,1024}$"}, "S3Prefix": {"type": "string", "max": 300, "min": 1, "sensitive": true}, "S3Prefixes": {"type": "list", "member": {"shape": "S3Prefix"}, "max": 1, "min": 1}, "SalesforceAuthType": {"type": "string", "enum": ["OAUTH2_CLIENT_CREDENTIALS"]}, "SalesforceCrawlerConfiguration": {"type": "structure", "members": {"filterConfiguration": {"shape": "CrawlFilterConfiguration", "documentation": "<p>The configuration of filtering the Salesforce content. For example, configuring regular expression patterns to include or exclude certain content.</p>"}}, "documentation": "<p>The configuration of the Salesforce content. For example, configuring specific types of Salesforce content.</p>"}, "SalesforceDataSourceConfiguration": {"type": "structure", "required": ["sourceConfiguration"], "members": {"crawlerConfiguration": {"shape": "SalesforceCrawlerConfiguration", "documentation": "<p>The configuration of the Salesforce content. For example, configuring specific types of Salesforce content.</p>"}, "sourceConfiguration": {"shape": "SalesforceSourceConfiguration", "documentation": "<p>The endpoint information to connect to your Salesforce data source.</p>"}}, "documentation": "<p>The configuration information to connect to Salesforce as your data source.</p>"}, "SalesforceSourceConfiguration": {"type": "structure", "required": ["authType", "credentialsSecretArn", "hostUrl"], "members": {"authType": {"shape": "SalesforceAuthType", "documentation": "<p>The supported authentication type to authenticate and connect to your Salesforce instance.</p>"}, "credentialsSecretArn": {"shape": "SecretArn", "documentation": "<p>The Amazon Resource Name of an Secrets Manager secret that stores your authentication credentials for your Salesforce instance URL. For more information on the key-value pairs that must be included in your secret, depending on your authentication type, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/salesforce-data-source-connector.html#configuration-salesforce-connector\">Salesforce connection configuration</a>.</p>"}, "hostUrl": {"shape": "HttpsUrl", "documentation": "<p>The Salesforce host URL or instance URL.</p>"}}, "documentation": "<p>The endpoint information to connect to your Salesforce data source.</p>"}, "SecretArn": {"type": "string", "pattern": "^arn:aws(|-cn|-us-gov):secretsmanager:[a-z0-9-]{1,20}:([0-9]{12}|):secret:[a-zA-Z0-9!/_+=.@-]{1,512}$"}, "SeedUrl": {"type": "structure", "members": {"url": {"shape": "Url", "documentation": "<p>A seed or starting point URL.</p>"}}, "documentation": "<p>The seed or starting point URL. You should be authorized to crawl the URL.</p>"}, "SeedUrls": {"type": "list", "member": {"shape": "SeedUrl"}, "max": 100, "min": 1}, "SemanticChunkingConfiguration": {"type": "structure", "required": ["breakpointPercentileThreshold", "bufferSize", "maxTokens"], "members": {"breakpointPercentileThreshold": {"shape": "SemanticChunkingConfigurationBreakpointPercentileThresholdInteger", "documentation": "<p>The dissimilarity threshold for splitting chunks.</p>"}, "bufferSize": {"shape": "SemanticChunkingConfigurationBufferSizeInteger", "documentation": "<p>The buffer size.</p>"}, "maxTokens": {"shape": "SemanticChunkingConfigurationMaxTokensInteger", "documentation": "<p>The maximum number of tokens that a chunk can contain.</p>"}}, "documentation": "<p>Settings for semantic document chunking for a data source. Semantic chunking splits a document into into smaller documents based on groups of similar content derived from the text with natural language processing.</p> <p>With semantic chunking, each sentence is compared to the next to determine how similar they are. You specify a threshold in the form of a percentile, where adjacent sentences that are less similar than that percentage of sentence pairs are divided into separate chunks. For example, if you set the threshold to 90, then the 10 percent of sentence pairs that are least similar are split. So if you have 101 sentences, 100 sentence pairs are compared, and the 10 with the least similarity are split, creating 11 chunks. These chunks are further split if they exceed the max token size.</p> <p>You must also specify a buffer size, which determines whether sentences are compared in isolation, or within a moving context window that includes the previous and following sentence. For example, if you set the buffer size to <code>1</code>, the embedding for sentence 10 is derived from sentences 9, 10, and 11 combined.</p>"}, "SemanticChunkingConfigurationBreakpointPercentileThresholdInteger": {"type": "integer", "box": true, "max": 99, "min": 50}, "SemanticChunkingConfigurationBufferSizeInteger": {"type": "integer", "box": true, "max": 1, "min": 0}, "SemanticChunkingConfigurationMaxTokensInteger": {"type": "integer", "box": true, "min": 1}, "ServerSideEncryptionConfiguration": {"type": "structure", "members": {"kmsKeyArn": {"shape": "KmsKeyArn", "documentation": "<p>The Amazon Resource Name (ARN) of the KMS key used to encrypt the resource.</p>"}}, "documentation": "<p>Contains the configuration for server-side encryption.</p>"}, "ServiceQuotaExceededException": {"type": "structure", "members": {"message": {"shape": "NonBlankString"}}, "documentation": "<p>The number of requests exceeds the service quota. Resubmit your request later.</p>", "error": {"httpStatusCode": 402, "senderFault": true}, "exception": true}, "SessionSummaryConfiguration": {"type": "structure", "members": {"maxRecentSessions": {"shape": "MaxRecentSessions", "documentation": "<p>Maximum number of recent session summaries to include in the agent's prompt context.</p>"}}, "documentation": "<p>Configuration for SESSION_SUMMARY memory type enabled for the agent.</p>"}, "SessionTTL": {"type": "integer", "box": true, "max": 5400, "min": 60}, "SharePointAuthType": {"type": "string", "enum": ["OAUTH2_CLIENT_CREDENTIALS", "OAUTH2_SHAREPOINT_APP_ONLY_CLIENT_CREDENTIALS"]}, "SharePointCrawlerConfiguration": {"type": "structure", "members": {"filterConfiguration": {"shape": "CrawlFilterConfiguration", "documentation": "<p>The configuration of filtering the SharePoint content. For example, configuring regular expression patterns to include or exclude certain content.</p>"}}, "documentation": "<p>The configuration of the SharePoint content. For example, configuring specific types of SharePoint content.</p>"}, "SharePointDataSourceConfiguration": {"type": "structure", "required": ["sourceConfiguration"], "members": {"crawlerConfiguration": {"shape": "SharePointCrawlerConfiguration", "documentation": "<p>The configuration of the SharePoint content. For example, configuring specific types of SharePoint content.</p>"}, "sourceConfiguration": {"shape": "SharePointSourceConfiguration", "documentation": "<p>The endpoint information to connect to your SharePoint data source.</p>"}}, "documentation": "<p>The configuration information to connect to SharePoint as your data source.</p>"}, "SharePointDomain": {"type": "string", "max": 50, "min": 1}, "SharePointHostType": {"type": "string", "enum": ["ONLINE"]}, "SharePointSiteUrls": {"type": "list", "member": {"shape": "HttpsUrl"}, "max": 100, "min": 1}, "SharePointSourceConfiguration": {"type": "structure", "required": ["authType", "credentialsSecretArn", "domain", "hostType", "siteUrls"], "members": {"authType": {"shape": "SharePointAuthType", "documentation": "<p>The supported authentication type to authenticate and connect to your SharePoint site/sites.</p>"}, "credentialsSecretArn": {"shape": "SecretArn", "documentation": "<p>The Amazon Resource Name of an Secrets Manager secret that stores your authentication credentials for your SharePoint site/sites. For more information on the key-value pairs that must be included in your secret, depending on your authentication type, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/sharepoint-data-source-connector.html#configuration-sharepoint-connector\">SharePoint connection configuration</a>.</p>"}, "domain": {"shape": "SharePointDomain", "documentation": "<p>The domain of your SharePoint instance or site URL/URLs.</p>"}, "hostType": {"shape": "SharePointHostType", "documentation": "<p>The supported host type, whether online/cloud or server/on-premises.</p>"}, "siteUrls": {"shape": "SharePointSiteUrls", "documentation": "<p>A list of one or more SharePoint site URLs.</p>"}, "tenantId": {"shape": "Microsoft365TenantId", "documentation": "<p>The identifier of your Microsoft 365 tenant.</p>"}}, "documentation": "<p>The endpoint information to connect to your SharePoint data source.</p>"}, "SortOrder": {"type": "string", "enum": ["ASCENDING", "DESCENDING"]}, "SpecificToolChoice": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "ToolName", "documentation": "<p>The name of the tool.</p>"}}, "documentation": "<p>Defines a specific tool that the model must request. No text is generated but the results of tool use are sent back to the model to help generate a response. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/tool-use.html\">Use a tool to complete an Amazon Bedrock model response</a>.</p>"}, "SqlKnowledgeBaseConfiguration": {"type": "structure", "required": ["type"], "members": {"redshiftConfiguration": {"shape": "RedshiftConfiguration", "documentation": "<p>Specifies configurations for a knowledge base connected to an Amazon Redshift database.</p>"}, "type": {"shape": "QueryEngineType", "documentation": "<p>The type of SQL database to connect to the knowledge base.</p>"}}, "documentation": "<p>Contains configurations for a knowledge base connected to an SQL database. Specify the SQL database type in the <code>type</code> field and include the corresponding field. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/knowledge-base-build-structured.html\">Build a knowledge base by connecting to a structured data source</a> in the Amazon Bedrock User Guide.</p>"}, "SqlString": {"type": "string", "max": 1000, "min": 1}, "StartIngestionJobRequest": {"type": "structure", "required": ["dataSourceId", "knowledgeBaseId"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>A unique, case-sensitive identifier to ensure that the API request completes no more than one time. If this token matches a previous request, Amazon Bedrock ignores the request, but does not return an error. For more information, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/APIReference/Run_Instance_Idempotency.html\">Ensuring idempotency</a>.</p>", "idempotencyToken": true}, "dataSourceId": {"shape": "Id", "documentation": "<p>The unique identifier of the data source you want to ingest into your knowledge base.</p>", "location": "uri", "locationName": "dataSourceId"}, "description": {"shape": "Description", "documentation": "<p>A description of the data ingestion job.</p>"}, "knowledgeBaseId": {"shape": "Id", "documentation": "<p>The unique identifier of the knowledge base for the data ingestion job.</p>", "location": "uri", "locationName": "knowledgeBaseId"}}}, "StartIngestionJobResponse": {"type": "structure", "required": ["ingestionJob"], "members": {"ingestionJob": {"shape": "IngestionJob", "documentation": "<p>Contains information about the data ingestion job.</p>"}}}, "StepType": {"type": "string", "enum": ["POST_CHUNKING"]}, "StopIngestionJobRequest": {"type": "structure", "required": ["dataSourceId", "ingestionJobId", "knowledgeBaseId"], "members": {"dataSourceId": {"shape": "Id", "documentation": "<p>The unique identifier of the data source for the data ingestion job you want to stop.</p>", "location": "uri", "locationName": "dataSourceId"}, "ingestionJobId": {"shape": "Id", "documentation": "<p>The unique identifier of the data ingestion job you want to stop.</p>", "location": "uri", "locationName": "ingestionJobId"}, "knowledgeBaseId": {"shape": "Id", "documentation": "<p>The unique identifier of the knowledge base for the data ingestion job you want to stop.</p>", "location": "uri", "locationName": "knowledgeBaseId"}}}, "StopIngestionJobResponse": {"type": "structure", "required": ["ingestionJob"], "members": {"ingestionJob": {"shape": "IngestionJob", "documentation": "<p>Contains information about the stopped data ingestion job.</p>"}}}, "StopSequences": {"type": "list", "member": {"shape": "String"}, "max": 4, "min": 0}, "StorageConfiguration": {"type": "structure", "required": ["type"], "members": {"mongoDbAtlasConfiguration": {"shape": "MongoDbAtlasConfiguration", "documentation": "<p>Contains the storage configuration of the knowledge base in MongoDB Atlas.</p>"}, "neptuneAnalyticsConfiguration": {"shape": "NeptuneAnalyticsConfiguration", "documentation": "<p>Contains details about the Neptune Analytics configuration of the knowledge base in Amazon Neptune. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/knowledge-base-setup-neptune.html\">Create a vector index in Amazon Neptune Analytics.</a>.</p>"}, "opensearchManagedClusterConfiguration": {"shape": "OpenSearchManagedClusterConfiguration", "documentation": "<p>Contains details about the storage configuration of the knowledge base in OpenSearch Managed Cluster. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/knowledge-base-setup-osm.html\">Create a vector index in Amazon OpenSearch Service</a>.</p>"}, "opensearchServerlessConfiguration": {"shape": "OpenSearchServerlessConfiguration", "documentation": "<p>Contains the storage configuration of the knowledge base in Amazon OpenSearch Service.</p>"}, "pineconeConfiguration": {"shape": "PineconeConfiguration", "documentation": "<p>Contains the storage configuration of the knowledge base in Pinecone.</p>"}, "rdsConfiguration": {"shape": "RdsConfiguration", "documentation": "<p>Contains details about the storage configuration of the knowledge base in Amazon RDS. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/knowledge-base-setup-rds.html\">Create a vector index in Amazon RDS</a>.</p>"}, "redisEnterpriseCloudConfiguration": {"shape": "RedisEnterpriseCloudConfiguration", "documentation": "<p>Contains the storage configuration of the knowledge base in Redis Enterprise Cloud.</p>"}, "type": {"shape": "KnowledgeBaseStorageType", "documentation": "<p>The vector store service in which the knowledge base is stored.</p>"}}, "documentation": "<p>Contains the storage configuration of the knowledge base.</p>"}, "StorageDays": {"type": "integer", "box": true, "max": 365, "min": 0}, "StorageFlowNodeConfiguration": {"type": "structure", "required": ["serviceConfiguration"], "members": {"serviceConfiguration": {"shape": "StorageFlowNodeServiceConfiguration", "documentation": "<p>Contains configurations for the service to use for storing the input into the node.</p>"}}, "documentation": "<p>Contains configurations for a Storage node in a flow. This node stores the input in an Amazon S3 location that you specify.</p>"}, "StorageFlowNodeS3Configuration": {"type": "structure", "required": ["bucketName"], "members": {"bucketName": {"shape": "S3BucketName", "documentation": "<p>The name of the Amazon S3 bucket in which to store the input into the node.</p>"}}, "documentation": "<p>Contains configurations for the Amazon S3 location in which to store the input into the node.</p>"}, "StorageFlowNodeServiceConfiguration": {"type": "structure", "members": {"s3": {"shape": "StorageFlowNodeS3Configuration", "documentation": "<p>Contains configurations for the Amazon S3 location in which to store the input into the node.</p>"}}, "documentation": "<p>Contains configurations for the service to use for storing the input into the node.</p>", "union": true}, "String": {"type": "string"}, "StringValue": {"type": "string", "max": 2048, "min": 1, "sensitive": true}, "SupplementalDataStorageConfiguration": {"type": "structure", "required": ["storageLocations"], "members": {"storageLocations": {"shape": "SupplementalDataStorageLocations", "documentation": "<p>A list of objects specifying storage locations for images extracted from multimodal documents in your data source.</p>"}}, "documentation": "<p>Specifies configurations for the storage location of the images extracted from multimodal documents in your data source. These images can be retrieved and returned to the end user.</p>"}, "SupplementalDataStorageLocation": {"type": "structure", "required": ["type"], "members": {"s3Location": {"shape": "S3Location", "documentation": "<p>Contains information about the Amazon S3 location for the extracted images.</p>"}, "type": {"shape": "SupplementalDataStorageLocationType", "documentation": "<p>Specifies the storage service used for this location.</p>"}}, "documentation": "<p>Contains information about a storage location for images extracted from multimodal documents in your data source.</p>"}, "SupplementalDataStorageLocationType": {"type": "string", "enum": ["S3"]}, "SupplementalDataStorageLocations": {"type": "list", "member": {"shape": "SupplementalDataStorageLocation"}, "max": 1, "min": 1}, "SupportedLanguages": {"type": "string", "enum": ["Python_3"]}, "SystemContentBlock": {"type": "structure", "members": {"cachePoint": {"shape": "CachePointBlock", "documentation": "<p>Creates a cache checkpoint within a tool designation</p>"}, "text": {"shape": "NonEmptyString", "documentation": "<p>The text in the system prompt.</p>"}}, "documentation": "<p>Contains a system prompt to provide context to the model or to describe how it should behave. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/prompt-management-create.html\">Create a prompt using Prompt management</a>.</p>", "sensitive": true, "union": true}, "SystemContentBlocks": {"type": "list", "member": {"shape": "SystemContentBlock"}}, "TagKey": {"type": "string", "max": 128, "min": 1, "pattern": "^[a-zA-Z0-9\\s._:/=+@-]*$"}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 200, "min": 0}, "TagResourceRequest": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {"shape": "TaggableResourcesArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource to tag.</p>", "location": "uri", "locationName": "resourceArn"}, "tags": {"shape": "TagsMap", "documentation": "<p>An object containing key-value pairs that define the tags to attach to the resource.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0, "pattern": "^[a-zA-Z0-9\\s._:/=+@-]*$"}, "TaggableResourcesArn": {"type": "string", "max": 1011, "min": 20, "pattern": "(^arn:aws:bedrock:[a-zA-Z0-9-]+:/d{12}:(agent|agent-alias|knowledge-base|flow|prompt)/[A-Z0-9]{10}(?:/[A-Z0-9]{10})?$|^arn:aws:bedrock:[a-zA-Z0-9-]+:/d{12}:flow/([A-Z0-9]{10})/alias/([A-Z0-9]{10})$|^arn:aws:bedrock:[a-zA-Z0-9-]+:/d{12}:prompt/([A-Z0-9]{10})?(?::/d+)?$)"}, "TagsMap": {"type": "map", "key": {"shape": "TagKey"}, "value": {"shape": "TagValue"}}, "Temperature": {"type": "float", "box": true, "max": 1, "min": 0}, "TextContentDoc": {"type": "structure", "required": ["data"], "members": {"data": {"shape": "Data", "documentation": "<p>The text of the content.</p>"}}, "documentation": "<p>Contains information about content defined inline in text.</p>"}, "TextPrompt": {"type": "string", "min": 1, "sensitive": true}, "TextPromptTemplateConfiguration": {"type": "structure", "required": ["text"], "members": {"cachePoint": {"shape": "CachePointBlock", "documentation": "<p>A cache checkpoint within a template configuration.</p>"}, "inputVariables": {"shape": "PromptInputVariablesList", "documentation": "<p>An array of the variables in the prompt template.</p>"}, "text": {"shape": "TextPrompt", "documentation": "<p>The message for the prompt.</p>"}}, "documentation": "<p>Contains configurations for a text prompt template. To include a variable, enclose a word in double curly braces as in <code>{{variable}}</code>.</p>", "sensitive": true}, "ThrottlingException": {"type": "structure", "members": {"message": {"shape": "NonBlankString"}}, "documentation": "<p>The number of requests exceeds the limit. Resubmit your request later.</p>", "error": {"httpStatusCode": 429, "senderFault": true}, "exception": true}, "Tool": {"type": "structure", "members": {"cachePoint": {"shape": "CachePointBlock", "documentation": "<p>Creates a cache checkpoint within a tool designation</p>"}, "toolSpec": {"shape": "ToolSpecification", "documentation": "<p>The specification for the tool.</p>"}}, "documentation": "<p>Contains configurations for a tool that a model can use when generating a response. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/tool-use.html\">Use a tool to complete an Amazon Bedrock model response</a>.</p>", "union": true}, "ToolChoice": {"type": "structure", "members": {"any": {"shape": "AnyToolChoice", "documentation": "<p>Defines tools, at least one of which must be requested by the model. No text is generated but the results of tool use are sent back to the model to help generate a response.</p>"}, "auto": {"shape": "AutoToolChoice", "documentation": "<p>Defines tools. The model automatically decides whether to call a tool or to generate text instead.</p>"}, "tool": {"shape": "SpecificToolChoice", "documentation": "<p>Defines a specific tool that the model must request. No text is generated but the results of tool use are sent back to the model to help generate a response.</p>"}}, "documentation": "<p>Defines which tools the model should request when invoked. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/tool-use.html\">Use a tool to complete an Amazon Bedrock model response</a>.</p>", "sensitive": true, "union": true}, "ToolConfiguration": {"type": "structure", "required": ["tools"], "members": {"toolChoice": {"shape": "ToolChoice", "documentation": "<p>Defines which tools the model should request when invoked.</p>"}, "tools": {"shape": "ToolConfigurationToolsList", "documentation": "<p>An array of tools to pass to a model.</p>"}}, "documentation": "<p>Configuration information for the tools that the model can use when generating a response. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/tool-use.html\">Use a tool to complete an Amazon Bedrock model response</a>.</p>"}, "ToolConfigurationToolsList": {"type": "list", "member": {"shape": "Tool"}, "min": 1, "sensitive": true}, "ToolInputSchema": {"type": "structure", "members": {"json": {"shape": "Document", "documentation": "<p>A JSON object defining the input schema for the tool.</p>"}}, "documentation": "<p>The input schema for the tool. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/tool-use.html\">Use a tool to complete an Amazon Bedrock model response</a>.</p>", "union": true}, "ToolName": {"type": "string", "max": 64, "min": 1, "pattern": "^[a-zA-Z][a-zA-Z0-9_]*$"}, "ToolSpecification": {"type": "structure", "required": ["inputSchema", "name"], "members": {"description": {"shape": "NonEmptyString", "documentation": "<p>The description of the tool.</p>"}, "inputSchema": {"shape": "ToolInputSchema", "documentation": "<p>The input schema for the tool.</p>"}, "name": {"shape": "ToolName", "documentation": "<p>The name of the tool.</p>"}}, "documentation": "<p>Contains a specification for a tool. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/tool-use.html\">Use a tool to complete an Amazon Bedrock model response</a>.</p>"}, "TopK": {"type": "integer", "box": true, "max": 500, "min": 0}, "TopP": {"type": "float", "box": true, "max": 1, "min": 0}, "Transformation": {"type": "structure", "required": ["stepToApply", "transformationFunction"], "members": {"stepToApply": {"shape": "StepType", "documentation": "<p>When the service applies the transformation.</p>"}, "transformationFunction": {"shape": "TransformationFunction", "documentation": "<p>A Lambda function that processes documents.</p>"}}, "documentation": "<p>A custom processing step for documents moving through a data source ingestion pipeline. To process documents after they have been converted into chunks, set the step to apply to <code>POST_CHUNKING</code>.</p>"}, "TransformationFunction": {"type": "structure", "required": ["transformationLambdaConfiguration"], "members": {"transformationLambdaConfiguration": {"shape": "TransformationLambdaConfiguration", "documentation": "<p>The Lambda function.</p>"}}, "documentation": "<p>A Lambda function that processes documents.</p>"}, "TransformationLambdaConfiguration": {"type": "structure", "required": ["lambdaArn"], "members": {"lambdaArn": {"shape": "LambdaArn", "documentation": "<p>The function's ARN identifier.</p>"}}, "documentation": "<p>A Lambda function that processes documents.</p>"}, "Transformations": {"type": "list", "member": {"shape": "Transformation"}, "max": 1, "min": 1}, "Type": {"type": "string", "enum": ["string", "number", "integer", "boolean", "array"]}, "UnfulfilledNodeInputFlowValidationDetails": {"type": "structure", "required": ["input", "node"], "members": {"input": {"shape": "FlowNodeInputName", "documentation": "<p>The name of the unfulfilled input. An input is unfulfilled if there are no data connections to it.</p>"}, "node": {"shape": "FlowNodeName", "documentation": "<p>The name of the node containing the unfulfilled input.</p>"}}, "documentation": "<p>Details about an unfulfilled node input with no valid connections.</p>"}, "UnknownConnectionConditionFlowValidationDetails": {"type": "structure", "required": ["connection"], "members": {"connection": {"shape": "FlowConnectionName", "documentation": "<p>The name of the connection with the unknown condition.</p>"}}, "documentation": "<p>Details about an unknown condition for a connection.</p>"}, "UnknownConnectionSourceFlowValidationDetails": {"type": "structure", "required": ["connection"], "members": {"connection": {"shape": "FlowConnectionName", "documentation": "<p>The name of the connection with the unknown source.</p>"}}, "documentation": "<p>Details about an unknown source node for a connection.</p>"}, "UnknownConnectionSourceOutputFlowValidationDetails": {"type": "structure", "required": ["connection"], "members": {"connection": {"shape": "FlowConnectionName", "documentation": "<p>The name of the connection with the unknown source output.</p>"}}, "documentation": "<p>Details about an unknown source output for a connection.</p>"}, "UnknownConnectionTargetFlowValidationDetails": {"type": "structure", "required": ["connection"], "members": {"connection": {"shape": "FlowConnectionName", "documentation": "<p>The name of the connection with the unknown target.</p>"}}, "documentation": "<p>Details about an unknown target node for a connection.</p>"}, "UnknownConnectionTargetInputFlowValidationDetails": {"type": "structure", "required": ["connection"], "members": {"connection": {"shape": "FlowConnectionName", "documentation": "<p>The name of the connection with the unknown target input.</p>"}}, "documentation": "<p>Details about an unknown target input for a connection.</p>"}, "UnknownNodeInputFlowValidationDetails": {"type": "structure", "required": ["input", "node"], "members": {"input": {"shape": "FlowNodeInputName", "documentation": "<p>The name of the node with the unknown input.</p>"}, "node": {"shape": "FlowNodeName", "documentation": "<p>The name of the unknown input.</p>"}}, "documentation": "<p>Details about an unknown input for a node.</p>"}, "UnknownNodeOutputFlowValidationDetails": {"type": "structure", "required": ["node", "output"], "members": {"node": {"shape": "FlowNodeName", "documentation": "<p>The name of the node with the unknown output.</p>"}, "output": {"shape": "FlowNodeOutputName", "documentation": "<p>The name of the unknown output.</p>"}}, "documentation": "<p>Details about an unknown output for a node.</p>"}, "UnreachableNodeFlowValidationDetails": {"type": "structure", "required": ["node"], "members": {"node": {"shape": "FlowNodeName", "documentation": "<p>The name of the unreachable node.</p>"}}, "documentation": "<p>Details about an unreachable node in the flow. A node is unreachable when there are no paths to it from any starting node.</p>"}, "UnsatisfiedConnectionConditionsFlowValidationDetails": {"type": "structure", "required": ["connection"], "members": {"connection": {"shape": "FlowConnectionName", "documentation": "<p>The name of the connection with unsatisfied conditions.</p>"}}, "documentation": "<p>Details about unsatisfied conditions for a connection. A condition is unsatisfied if it can never be true, for example two branches of condition node cannot be simultaneously true.</p>"}, "UnspecifiedFlowValidationDetails": {"type": "structure", "members": {}, "documentation": "<p>Details about an unspecified validation that doesn't fit other categories.</p>"}, "UntagResourceRequest": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {"shape": "TaggableResourcesArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource from which to remove tags.</p>", "location": "uri", "locationName": "resourceArn"}, "tagKeys": {"shape": "TagKeyList", "documentation": "<p>A list of keys of the tags to remove from the resource.</p>", "location": "querystring", "locationName": "tagKeys"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateAgentActionGroupRequest": {"type": "structure", "required": ["actionGroupId", "actionGroupName", "agentId", "agentVersion"], "members": {"actionGroupExecutor": {"shape": "ActionGroupExecutor", "documentation": "<p>The Amazon Resource Name (ARN) of the Lambda function containing the business logic that is carried out upon invoking the action.</p>"}, "actionGroupId": {"shape": "Id", "documentation": "<p>The unique identifier of the action group.</p>", "location": "uri", "locationName": "actionGroupId"}, "actionGroupName": {"shape": "Name", "documentation": "<p>Specifies a new name for the action group.</p>"}, "actionGroupState": {"shape": "ActionGroupState", "documentation": "<p>Specifies whether the action group is available for the agent to invoke or not when sending an <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_InvokeAgent.html\">InvokeAgent</a> request.</p>"}, "agentId": {"shape": "Id", "documentation": "<p>The unique identifier of the agent for which to update the action group.</p>", "location": "uri", "locationName": "agentId"}, "agentVersion": {"shape": "DraftVersion", "documentation": "<p>The unique identifier of the agent version for which to update the action group.</p>", "location": "uri", "locationName": "agentVersion"}, "apiSchema": {"shape": "APISchema", "documentation": "<p>Contains either details about the S3 object containing the OpenAPI schema for the action group or the JSON or YAML-formatted payload defining the schema. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/agents-api-schema.html\">Action group OpenAPI schemas</a>.</p>"}, "description": {"shape": "Description", "documentation": "<p>Specifies a new name for the action group.</p>"}, "functionSchema": {"shape": "FunctionSchema", "documentation": "<p>Contains details about the function schema for the action group or the JSON or YAML-formatted payload defining the schema.</p>"}, "parentActionGroupSignature": {"shape": "ActionGroupSignature", "documentation": "<p>Update the built-in or computer use action for this action group. If you specify a value, you must leave the <code>description</code>, <code>apiSchema</code>, and <code>actionGroupExecutor</code> fields empty for this action group. </p> <ul> <li> <p>To allow your agent to request the user for additional information when trying to complete a task, set this field to <code>AMAZON.UserInput</code>. </p> </li> <li> <p>To allow your agent to generate, run, and troubleshoot code when trying to complete a task, set this field to <code>AMAZON.CodeInterpreter</code>.</p> </li> <li> <p>To allow your agent to use an Anthropic computer use tool, specify one of the following values. </p> <important> <p> Computer use is a new Anthropic Claude model capability (in beta) available with Anthropic Claude 3.7 Sonnet and Claude 3.5 Sonnet v2 only. When operating computer use functionality, we recommend taking additional security precautions, such as executing computer actions in virtual environments with restricted data access and limited internet connectivity. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/agents-computer-use.html\">Configure an Amazon Bedrock Agent to complete tasks with computer use tools</a>. </p> </important> <ul> <li> <p> <code>ANTHROPIC.Computer</code> - Gives the agent permission to use the mouse and keyboard and take screenshots.</p> </li> <li> <p> <code>ANTHROPIC.TextEditor</code> - Gives the agent permission to view, create and edit files.</p> </li> <li> <p> <code>ANTHROPIC.Bash</code> - Gives the agent permission to run commands in a bash shell.</p> </li> </ul> </li> </ul> <p>During orchestration, if your agent determines that it needs to invoke an API in an action group, but doesn't have enough information to complete the API request, it will invoke this action group instead and return an <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_Observation.html\">Observation</a> reprompting the user for more information.</p>"}, "parentActionGroupSignatureParams": {"shape": "ActionGroupSignatureParams", "documentation": "<p>The configuration settings for a computer use action.</p> <important> <p> Computer use is a new Anthropic Claude model capability (in beta) available with Claude 3.7 Sonnet and Claude 3.5 Sonnet v2 only. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/agents-computer-use.html\">Configure an Amazon Bedrock Agent to complete tasks with computer use tools</a>. </p> </important>"}}}, "UpdateAgentActionGroupResponse": {"type": "structure", "required": ["agentActionGroup"], "members": {"agentActionGroup": {"shape": "AgentActionGroup", "documentation": "<p>Contains details about the action group that was updated.</p>"}}}, "UpdateAgentAliasRequest": {"type": "structure", "required": ["agentAliasId", "agentAliasName", "agentId"], "members": {"agentAliasId": {"shape": "AgentAliasId", "documentation": "<p>The unique identifier of the alias.</p>", "location": "uri", "locationName": "agentAliasId"}, "agentAliasName": {"shape": "Name", "documentation": "<p>Specifies a new name for the alias.</p>"}, "agentId": {"shape": "Id", "documentation": "<p>The unique identifier of the agent.</p>", "location": "uri", "locationName": "agentId"}, "aliasInvocationState": {"shape": "AliasInvocationState", "documentation": "<p>The invocation state for the agent alias. To pause the agent alias, set the value to <code>REJECT_INVOCATIONS</code>. To start the agent alias running again, set the value to <code>ACCEPT_INVOCATIONS</code>. Use the <code>GetAgentAlias</code>, or <code>ListAgentAliases</code>, operation to get the invocation state of an agent alias.</p>"}, "description": {"shape": "Description", "documentation": "<p>Specifies a new description for the alias.</p>"}, "routingConfiguration": {"shape": "AgentAliasRoutingConfiguration", "documentation": "<p>Contains details about the routing configuration of the alias.</p>"}}}, "UpdateAgentAliasResponse": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON>"], "members": {"agentAlias": {"shape": "Agent<PERSON><PERSON><PERSON>", "documentation": "<p>Contains details about the alias that was updated.</p>"}}}, "UpdateAgentCollaboratorRequest": {"type": "structure", "required": ["agentDescriptor", "agentId", "agentVersion", "collaborationInstruction", "collaboratorId", "<PERSON><PERSON><PERSON>"], "members": {"agentDescriptor": {"shape": "AgentDescriptor", "documentation": "<p>An agent descriptor for the agent collaborator.</p>"}, "agentId": {"shape": "Id", "documentation": "<p>The agent's ID.</p>", "location": "uri", "locationName": "agentId"}, "agentVersion": {"shape": "DraftVersion", "documentation": "<p>The agent's version.</p>", "location": "uri", "locationName": "agentVersion"}, "collaborationInstruction": {"shape": "CollaborationInstruction", "documentation": "<p>Instruction for the collaborator.</p>"}, "collaboratorId": {"shape": "Id", "documentation": "<p>The collaborator's ID.</p>", "location": "uri", "locationName": "collaboratorId"}, "collaboratorName": {"shape": "Name", "documentation": "<p>The collaborator's name.</p>"}, "relayConversationHistory": {"shape": "RelayConversationHistory", "documentation": "<p>A relay conversation history for the collaborator.</p>"}}}, "UpdateAgentCollaboratorResponse": {"type": "structure", "required": ["agentCollaborator"], "members": {"agentCollaborator": {"shape": "AgentCollaborator", "documentation": "<p>Details about the collaborator.</p>"}}}, "UpdateAgentKnowledgeBaseRequest": {"type": "structure", "required": ["agentId", "agentVersion", "knowledgeBaseId"], "members": {"agentId": {"shape": "Id", "documentation": "<p>The unique identifier of the agent associated with the knowledge base that you want to update.</p>", "location": "uri", "locationName": "agentId"}, "agentVersion": {"shape": "DraftVersion", "documentation": "<p>The version of the agent associated with the knowledge base that you want to update.</p>", "location": "uri", "locationName": "agentVersion"}, "description": {"shape": "Description", "documentation": "<p>Specifies a new description for the knowledge base associated with an agent.</p>"}, "knowledgeBaseId": {"shape": "Id", "documentation": "<p>The unique identifier of the knowledge base that has been associated with an agent.</p>", "location": "uri", "locationName": "knowledgeBaseId"}, "knowledgeBaseState": {"shape": "KnowledgeBaseState", "documentation": "<p>Specifies whether the agent uses the knowledge base or not when sending an <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_InvokeAgent.html\">InvokeAgent</a> request.</p>"}}}, "UpdateAgentKnowledgeBaseResponse": {"type": "structure", "required": ["agentKnowledgeBase"], "members": {"agentKnowledgeBase": {"shape": "AgentKnowledgeBase", "documentation": "<p>Contains details about the knowledge base that has been associated with an agent.</p>"}}}, "UpdateAgentRequest": {"type": "structure", "required": ["agentId", "<PERSON><PERSON><PERSON>", "agentResourceRoleArn", "foundationModel"], "members": {"agentCollaboration": {"shape": "AgentCollaboration", "documentation": "<p>The agent's collaboration role.</p>"}, "agentId": {"shape": "Id", "documentation": "<p>The unique identifier of the agent.</p>", "location": "uri", "locationName": "agentId"}, "agentName": {"shape": "Name", "documentation": "<p>Specifies a new name for the agent.</p>"}, "agentResourceRoleArn": {"shape": "AgentRoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role with permissions to invoke API operations on the agent.</p>"}, "customOrchestration": {"shape": "CustomOrchestration", "documentation": "<p> Contains details of the custom orchestration configured for the agent. </p>"}, "customerEncryptionKeyArn": {"shape": "KmsKeyArn", "documentation": "<p>The Amazon Resource Name (ARN) of the KMS key with which to encrypt the agent.</p>"}, "description": {"shape": "Description", "documentation": "<p>Specifies a new description of the agent.</p>"}, "foundationModel": {"shape": "ModelIdentifier", "documentation": "<p>The identifier for the model that you want to be used for orchestration by the agent you create.</p> <p>The <code>modelId</code> to provide depends on the type of model or throughput that you use:</p> <ul> <li> <p>If you use a base model, specify the model ID or its ARN. For a list of model IDs for base models, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/model-ids.html#model-ids-arns\">Amazon Bedrock base model IDs (on-demand throughput)</a> in the Amazon Bedrock User Guide.</p> </li> <li> <p>If you use an inference profile, specify the inference profile ID or its ARN. For a list of inference profile IDs, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/cross-region-inference-support.html\">Supported Regions and models for cross-region inference</a> in the Amazon Bedrock User Guide.</p> </li> <li> <p>If you use a provisioned model, specify the ARN of the Provisioned Throughput. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/prov-thru-use.html\">Run inference using a Provisioned Throughput</a> in the Amazon Bedrock User Guide.</p> </li> <li> <p>If you use a custom model, first purchase Provisioned Throughput for it. Then specify the ARN of the resulting provisioned model. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/model-customization-use.html\">Use a custom model in Amazon Bedrock</a> in the Amazon Bedrock User Guide.</p> </li> <li> <p>If you use an <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/model-customization-import-model.html\">imported model</a>, specify the ARN of the imported model. You can get the model ARN from a successful call to <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_CreateModelImportJob.html\">CreateModelImportJob</a> or from the Imported models page in the Amazon Bedrock console.</p> </li> </ul>"}, "guardrailConfiguration": {"shape": "GuardrailConfiguration", "documentation": "<p>The unique Guardrail configuration assigned to the agent when it is updated.</p>"}, "idleSessionTTLInSeconds": {"shape": "SessionTTL", "documentation": "<p>The number of seconds for which Amazon Bedrock keeps information about a user's conversation with the agent.</p> <p>A user interaction remains active for the amount of time specified. If no conversation occurs during this time, the session expires and Amazon Bedrock deletes any data provided before the timeout.</p>"}, "instruction": {"shape": "Instruction", "documentation": "<p>Specifies new instructions that tell the agent what it should do and how it should interact with users.</p>"}, "memoryConfiguration": {"shape": "MemoryConfiguration", "documentation": "<p>Specifies the new memory configuration for the agent. </p>"}, "orchestrationType": {"shape": "OrchestrationType", "documentation": "<p> Specifies the type of orchestration strategy for the agent. This is set to <code>DEFAULT</code> orchestration type, by default. </p>"}, "promptOverrideConfiguration": {"shape": "PromptOverrideConfiguration", "documentation": "<p>Contains configurations to override prompts in different parts of an agent sequence. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/advanced-prompts.html\">Advanced prompts</a>.</p>"}}}, "UpdateAgentResponse": {"type": "structure", "required": ["agent"], "members": {"agent": {"shape": "Agent", "documentation": "<p>Contains details about the agent that was updated.</p>"}}}, "UpdateDataSourceRequest": {"type": "structure", "required": ["dataSourceConfiguration", "dataSourceId", "knowledgeBaseId", "name"], "members": {"dataDeletionPolicy": {"shape": "DataDeletionPolicy", "documentation": "<p>The data deletion policy for the data source that you want to update.</p>"}, "dataSourceConfiguration": {"shape": "DataSourceConfiguration", "documentation": "<p>The connection configuration for the data source that you want to update.</p>"}, "dataSourceId": {"shape": "Id", "documentation": "<p>The unique identifier of the data source.</p>", "location": "uri", "locationName": "dataSourceId"}, "description": {"shape": "Description", "documentation": "<p>Specifies a new description for the data source.</p>"}, "knowledgeBaseId": {"shape": "Id", "documentation": "<p>The unique identifier of the knowledge base for the data source.</p>", "location": "uri", "locationName": "knowledgeBaseId"}, "name": {"shape": "Name", "documentation": "<p>Specifies a new name for the data source.</p>"}, "serverSideEncryptionConfiguration": {"shape": "ServerSideEncryptionConfiguration", "documentation": "<p>Contains details about server-side encryption of the data source.</p>"}, "vectorIngestionConfiguration": {"shape": "VectorIngestionConfiguration", "documentation": "<p>Contains details about how to ingest the documents in the data source.</p>"}}}, "UpdateDataSourceResponse": {"type": "structure", "required": ["dataSource"], "members": {"dataSource": {"shape": "DataSource", "documentation": "<p>Contains details about the data source.</p>"}}}, "UpdateFlowAliasRequest": {"type": "structure", "required": ["aliasIdentifier", "flowIdentifier", "name", "routingConfiguration"], "members": {"aliasIdentifier": {"shape": "FlowAliasIdentifier", "documentation": "<p>The unique identifier of the alias.</p>", "location": "uri", "locationName": "aliasIdentifier"}, "concurrencyConfiguration": {"shape": "FlowAliasConcurrencyConfiguration", "documentation": "<p>The configuration that specifies how nodes in the flow are executed in parallel.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description for the alias.</p>"}, "flowIdentifier": {"shape": "FlowIdentifier", "documentation": "<p>The unique identifier of the flow.</p>", "location": "uri", "locationName": "flowIdentifier"}, "name": {"shape": "Name", "documentation": "<p>The name of the alias.</p>"}, "routingConfiguration": {"shape": "FlowAliasRoutingConfiguration", "documentation": "<p>Contains information about the version to which to map the alias.</p>"}}}, "UpdateFlowAliasResponse": {"type": "structure", "required": ["arn", "createdAt", "flowId", "id", "name", "routingConfiguration", "updatedAt"], "members": {"arn": {"shape": "FlowAliasArn", "documentation": "<p>The Amazon Resource Name (ARN) of the flow.</p>"}, "concurrencyConfiguration": {"shape": "FlowAliasConcurrencyConfiguration", "documentation": "<p>The configuration that specifies how nodes in the flow are executed in parallel.</p>"}, "createdAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the flow was created.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description of the flow.</p>"}, "flowId": {"shape": "FlowId", "documentation": "<p>The unique identifier of the flow.</p>"}, "id": {"shape": "FlowAliasId", "documentation": "<p>The unique identifier of the alias.</p>"}, "name": {"shape": "Name", "documentation": "<p>The name of the alias.</p>"}, "routingConfiguration": {"shape": "FlowAliasRoutingConfiguration", "documentation": "<p>Contains information about the version that the alias is mapped to.</p>"}, "updatedAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the alias was last updated.</p>"}}}, "UpdateFlowRequest": {"type": "structure", "required": ["executionRoleArn", "flowIdentifier", "name"], "members": {"customerEncryptionKeyArn": {"shape": "KmsKeyArn", "documentation": "<p>The Amazon Resource Name (ARN) of the KMS key to encrypt the flow.</p>"}, "definition": {"shape": "FlowDefinition", "documentation": "<p>A definition of the nodes and the connections between the nodes in the flow.</p>"}, "description": {"shape": "FlowDescription", "documentation": "<p>A description for the flow.</p>"}, "executionRoleArn": {"shape": "FlowExecutionRoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the service role with permissions to create and manage a flow. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/flows-permissions.html\">Create a service role for flows in Amazon Bedrock</a> in the Amazon Bedrock User Guide.</p>"}, "flowIdentifier": {"shape": "FlowIdentifier", "documentation": "<p>The unique identifier of the flow.</p>", "location": "uri", "locationName": "flowIdentifier"}, "name": {"shape": "FlowName", "documentation": "<p>A name for the flow.</p>"}}}, "UpdateFlowResponse": {"type": "structure", "required": ["arn", "createdAt", "executionRoleArn", "id", "name", "status", "updatedAt", "version"], "members": {"arn": {"shape": "FlowArn", "documentation": "<p>The Amazon Resource Name (ARN) of the flow.</p>"}, "createdAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the flow was created.</p>"}, "customerEncryptionKeyArn": {"shape": "KmsKeyArn", "documentation": "<p>The Amazon Resource Name (ARN) of the KMS key that the flow was encrypted with.</p>"}, "definition": {"shape": "FlowDefinition", "documentation": "<p>A definition of the nodes and the connections between nodes in the flow.</p>"}, "description": {"shape": "FlowDescription", "documentation": "<p>The description of the flow.</p>"}, "executionRoleArn": {"shape": "FlowExecutionRoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the service role with permissions to create a flow. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/flows-permissions.html\">Create a service role for flows in Amazon Bedrock</a> in the Amazon Bedrock User Guide.</p>"}, "id": {"shape": "FlowId", "documentation": "<p>The unique identifier of the flow.</p>"}, "name": {"shape": "FlowName", "documentation": "<p>The name of the flow.</p>"}, "status": {"shape": "FlowStatus", "documentation": "<p>The status of the flow. When you submit this request, the status will be <code>NotPrepared</code>. If updating fails, the status becomes <code>Failed</code>.</p>"}, "updatedAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the flow was last updated.</p>"}, "version": {"shape": "DraftVersion", "documentation": "<p>The version of the flow. When you update a flow, the version updated is the <code>DRAFT</code> version.</p>"}}}, "UpdateKnowledgeBaseRequest": {"type": "structure", "required": ["knowledgeBaseConfiguration", "knowledgeBaseId", "name", "roleArn"], "members": {"description": {"shape": "Description", "documentation": "<p>Specifies a new description for the knowledge base.</p>"}, "knowledgeBaseConfiguration": {"shape": "KnowledgeBaseConfiguration", "documentation": "<p>Specifies the configuration for the embeddings model used for the knowledge base. You must use the same configuration as when the knowledge base was created.</p>"}, "knowledgeBaseId": {"shape": "Id", "documentation": "<p>The unique identifier of the knowledge base to update.</p>", "location": "uri", "locationName": "knowledgeBaseId"}, "name": {"shape": "Name", "documentation": "<p>Specifies a new name for the knowledge base.</p>"}, "roleArn": {"shape": "KnowledgeBaseRoleArn", "documentation": "<p>Specifies a different Amazon Resource Name (ARN) of the IAM role with permissions to invoke API operations on the knowledge base.</p>"}, "storageConfiguration": {"shape": "StorageConfiguration", "documentation": "<p>Specifies the configuration for the vector store used for the knowledge base. You must use the same configuration as when the knowledge base was created.</p>"}}}, "UpdateKnowledgeBaseResponse": {"type": "structure", "required": ["knowledgeBase"], "members": {"knowledgeBase": {"shape": "KnowledgeBase", "documentation": "<p>Contains details about the knowledge base.</p>"}}}, "UpdatePromptRequest": {"type": "structure", "required": ["name", "promptIdentifier"], "members": {"customerEncryptionKeyArn": {"shape": "KmsKeyArn", "documentation": "<p>The Amazon Resource Name (ARN) of the KMS key to encrypt the prompt.</p>"}, "defaultVariant": {"shape": "PromptVariantName", "documentation": "<p>The name of the default variant for the prompt. This value must match the <code>name</code> field in the relevant <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent_PromptVariant.html\">PromptVariant</a> object.</p>"}, "description": {"shape": "PromptDescription", "documentation": "<p>A description for the prompt.</p>"}, "name": {"shape": "PromptName", "documentation": "<p>A name for the prompt.</p>"}, "promptIdentifier": {"shape": "PromptIdentifier", "documentation": "<p>The unique identifier of the prompt.</p>", "location": "uri", "locationName": "promptIdentifier"}, "variants": {"shape": "PromptVariantList", "documentation": "<p>A list of objects, each containing details about a variant of the prompt.</p>"}}}, "UpdatePromptResponse": {"type": "structure", "required": ["arn", "createdAt", "id", "name", "updatedAt", "version"], "members": {"arn": {"shape": "PromptArn", "documentation": "<p>The Amazon Resource Name (ARN) of the prompt.</p>"}, "createdAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the prompt was created.</p>"}, "customerEncryptionKeyArn": {"shape": "KmsKeyArn", "documentation": "<p>The Amazon Resource Name (ARN) of the KMS key to encrypt the prompt.</p>"}, "defaultVariant": {"shape": "PromptVariantName", "documentation": "<p>The name of the default variant for the prompt. This value must match the <code>name</code> field in the relevant <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent_PromptVariant.html\">PromptVariant</a> object.</p>"}, "description": {"shape": "PromptDescription", "documentation": "<p>The description of the prompt.</p>"}, "id": {"shape": "PromptId", "documentation": "<p>The unique identifier of the prompt.</p>"}, "name": {"shape": "PromptName", "documentation": "<p>The name of the prompt.</p>"}, "updatedAt": {"shape": "DateTimestamp", "documentation": "<p>The time at which the prompt was last updated.</p>"}, "variants": {"shape": "PromptVariantList", "documentation": "<p>A list of objects, each containing details about a variant of the prompt.</p>"}, "version": {"shape": "Version", "documentation": "<p>The version of the prompt. When you update a prompt, the version updated is the <code>DRAFT</code> version.</p>"}}}, "Url": {"type": "string", "pattern": "^https?://[A-Za-z0-9][^\\s]*$"}, "UrlConfiguration": {"type": "structure", "members": {"seedUrls": {"shape": "SeedUrls", "documentation": "<p>One or more seed or starting point URLs.</p>"}}, "documentation": "<p>The configuration of web URLs that you want to crawl. You should be authorized to crawl the URLs.</p>"}, "UserAgent": {"type": "string", "max": 40, "min": 15, "sensitive": true}, "UserAgentHeader": {"type": "string", "max": 86, "min": 61, "sensitive": true}, "ValidateFlowDefinitionRequest": {"type": "structure", "required": ["definition"], "members": {"definition": {"shape": "FlowDefinition", "documentation": "<p>The definition of a flow to validate.</p>"}}}, "ValidateFlowDefinitionResponse": {"type": "structure", "required": ["validations"], "members": {"validations": {"shape": "FlowValidations", "documentation": "<p>Contains an array of objects, each of which contains an error identified by validation.</p>"}}}, "ValidationException": {"type": "structure", "members": {"fieldList": {"shape": "ValidationExceptionFieldList", "documentation": "<p>A list of objects containing fields that caused validation errors and their corresponding validation error messages.</p>"}, "message": {"shape": "NonBlankString"}}, "documentation": "<p>Input validation failed. Check your request parameters and retry the request.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "ValidationExceptionField": {"type": "structure", "required": ["message", "name"], "members": {"message": {"shape": "NonBlankString", "documentation": "<p>A message describing why this field failed validation.</p>"}, "name": {"shape": "NonBlankString", "documentation": "<p>The name of the field.</p>"}}, "documentation": "<p>Stores information about a field passed inside a request that resulted in an validation error.</p>"}, "ValidationExceptionFieldList": {"type": "list", "member": {"shape": "ValidationExceptionField"}}, "VectorIngestionConfiguration": {"type": "structure", "members": {"chunkingConfiguration": {"shape": "ChunkingConfiguration", "documentation": "<p>Details about how to chunk the documents in the data source. A <i>chunk</i> refers to an excerpt from a data source that is returned when the knowledge base that it belongs to is queried.</p>"}, "contextEnrichmentConfiguration": {"shape": "ContextEnrichmentConfiguration", "documentation": "<p>The context enrichment configuration used for ingestion of the data into the vector store.</p>"}, "customTransformationConfiguration": {"shape": "CustomTransformationConfiguration", "documentation": "<p>A custom document transformer for parsed data source documents.</p>"}, "parsingConfiguration": {"shape": "ParsingConfiguration", "documentation": "<p>Configurations for a parser to use for parsing documents in your data source. If you exclude this field, the default parser will be used.</p>"}}, "documentation": "<p>Contains details about how to ingest the documents in a data source.</p>"}, "VectorKnowledgeBaseConfiguration": {"type": "structure", "required": ["embeddingModelArn"], "members": {"embeddingModelArn": {"shape": "BedrockEmbeddingModelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the model used to create vector embeddings for the knowledge base.</p>"}, "embeddingModelConfiguration": {"shape": "EmbeddingModelConfiguration", "documentation": "<p>The embeddings model configuration details for the vector model used in Knowledge Base.</p>"}, "supplementalDataStorageConfiguration": {"shape": "SupplementalDataStorageConfiguration", "documentation": "<p>If you include multimodal data from your data source, use this object to specify configurations for the storage location of the images extracted from your documents. These images can be retrieved and returned to the end user. They can also be used in generation when using <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_RetrieveAndGenerate.html\">RetrieveAndGenerate</a>.</p>"}}, "documentation": "<p>Contains details about the model used to create vector embeddings for the knowledge base.</p>"}, "VectorSearchBedrockRerankingConfiguration": {"type": "structure", "required": ["modelConfiguration"], "members": {"metadataConfiguration": {"shape": "MetadataConfigurationForReranking", "documentation": "<p>Specifies how metadata fields should be handled during the reranking process.</p>"}, "modelConfiguration": {"shape": "VectorSearchBedrockRerankingModelConfiguration", "documentation": "<p>Specifies the configuration for the Amazon Bedrock reranker model.</p>"}, "numberOfRerankedResults": {"shape": "VectorSearchBedrockRerankingConfigurationNumberOfRerankedResultsInteger", "documentation": "<p>Specifies the number of results to return after reranking.</p>"}}, "documentation": "<p>Configures the Amazon Bedrock reranker model to improve the relevance of retrieved results.</p>"}, "VectorSearchBedrockRerankingConfigurationNumberOfRerankedResultsInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "VectorSearchBedrockRerankingModelConfiguration": {"type": "structure", "required": ["modelArn"], "members": {"additionalModelRequestFields": {"shape": "AdditionalModelRequestFields", "documentation": "<p>Specifies additional model-specific request parameters as key-value pairs that are included in the request to the Amazon Bedrock reranker model.</p>"}, "modelArn": {"shape": "BedrockRerankingModelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the Amazon Bedrock reranker model.</p>"}}, "documentation": "<p>Configures the Amazon Bedrock model used for reranking retrieved results.</p>"}, "VectorSearchRerankingConfiguration": {"type": "structure", "required": ["type"], "members": {"bedrockRerankingConfiguration": {"shape": "VectorSearchBedrockRerankingConfiguration", "documentation": "<p>Specifies the configuration for using an Amazon Bedrock reranker model to rerank retrieved results.</p>"}, "type": {"shape": "VectorSearchRerankingConfigurationType", "documentation": "<p>Specifies the type of reranking model to use. Currently, the only supported value is <code>BEDROCK_RERANKING_MODEL</code>.</p>"}}, "documentation": "<p>Specifies how retrieved results from a knowledge base are reranked to improve relevance.</p>"}, "VectorSearchRerankingConfigurationType": {"type": "string", "enum": ["BEDROCK_RERANKING_MODEL"]}, "Version": {"type": "string", "max": 5, "min": 1, "pattern": "^(DRAFT|[0-9]{0,4}[1-9][0-9]{0,4})$"}, "WebCrawlerConfiguration": {"type": "structure", "members": {"crawlerLimits": {"shape": "WebCrawlerLimits", "documentation": "<p>The configuration of crawl limits for the web URLs.</p>"}, "exclusionFilters": {"shape": "FilterList", "documentation": "<p>A list of one or more exclusion regular expression patterns to exclude certain URLs. If you specify an inclusion and exclusion filter/pattern and both match a URL, the exclusion filter takes precedence and the web content of the URL isn’t crawled.</p>"}, "inclusionFilters": {"shape": "FilterList", "documentation": "<p>A list of one or more inclusion regular expression patterns to include certain URLs. If you specify an inclusion and exclusion filter/pattern and both match a URL, the exclusion filter takes precedence and the web content of the URL isn’t crawled.</p>"}, "scope": {"shape": "WebScopeType", "documentation": "<p>The scope of what is crawled for your URLs.</p> <p>You can choose to crawl only web pages that belong to the same host or primary domain. For example, only web pages that contain the seed URL \"https://docs.aws.amazon.com/bedrock/latest/userguide/\" and no other domains. You can choose to include sub domains in addition to the host or primary domain. For example, web pages that contain \"aws.amazon.com\" can also include sub domain \"docs.aws.amazon.com\".</p>"}, "userAgent": {"shape": "UserAgent", "documentation": "<p>Returns the user agent suffix for your web crawler.</p>"}, "userAgentHeader": {"shape": "UserAgentHeader", "documentation": "<p>A string used for identifying the crawler or bot when it accesses a web server. The user agent header value consists of the <code>bedrockbot</code>, UUID, and a user agent suffix for your crawler (if one is provided). By default, it is set to <code>bedrockbot_UUID</code>. You can optionally append a custom suffix to <code>bedrockbot_UUID</code> to allowlist a specific user agent permitted to access your source URLs. </p>"}}, "documentation": "<p>The configuration of web URLs that you want to crawl. You should be authorized to crawl the URLs.</p>"}, "WebCrawlerLimits": {"type": "structure", "members": {"maxPages": {"shape": "WebCrawlerLimitsMaxPagesInteger", "documentation": "<p> The max number of web pages crawled from your source URLs, up to 25,000 pages. If the web pages exceed this limit, the data source sync will fail and no web pages will be ingested. </p>"}, "rateLimit": {"shape": "WebCrawlerLimitsRateLimitInteger", "documentation": "<p>The max rate at which pages are crawled, up to 300 per minute per host.</p>"}}, "documentation": "<p>The rate limits for the URLs that you want to crawl. You should be authorized to crawl the URLs.</p>"}, "WebCrawlerLimitsMaxPagesInteger": {"type": "integer", "box": true, "min": 1}, "WebCrawlerLimitsRateLimitInteger": {"type": "integer", "box": true, "max": 300, "min": 1}, "WebDataSourceConfiguration": {"type": "structure", "required": ["sourceConfiguration"], "members": {"crawlerConfiguration": {"shape": "WebCrawlerConfiguration", "documentation": "<p>The Web Crawler configuration details for the web data source.</p>"}, "sourceConfiguration": {"shape": "WebSourceConfiguration", "documentation": "<p>The source configuration details for the web data source.</p>"}}, "documentation": "<p>The configuration details for the web data source.</p>"}, "WebScopeType": {"type": "string", "enum": ["HOST_ONLY", "SUBDOMAINS"]}, "WebSourceConfiguration": {"type": "structure", "required": ["urlConfiguration"], "members": {"urlConfiguration": {"shape": "UrlConfiguration", "documentation": "<p>The configuration of the URL/URLs.</p>"}}, "documentation": "<p>The configuration of the URL/URLs for the web content that you want to crawl. You should be authorized to crawl the URLs.</p>"}, "WorkgroupArn": {"type": "string", "pattern": "^(arn:(aws(-[a-z]+)*):redshift-serverless:[a-z]{2}(-gov)?-[a-z]+-\\d{1}:\\d{12}:workgroup/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})$"}}, "documentation": "<p>Describes the API operations for creating and managing Amazon Bedrock agents.</p>"}