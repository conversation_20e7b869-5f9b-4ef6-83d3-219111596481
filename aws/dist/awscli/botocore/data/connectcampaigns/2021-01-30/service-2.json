{"version": "2.0", "metadata": {"apiVersion": "2021-01-30", "endpointPrefix": "connect-campaigns", "jsonVersion": "1.1", "protocol": "rest-json", "protocols": ["rest-json"], "serviceFullName": "AmazonConnectCampaignService", "serviceId": "ConnectCampaigns", "signatureVersion": "v4", "signingName": "connect-campaigns", "uid": "connectcampaigns-2021-01-30"}, "operations": {"CreateCampaign": {"name": "CreateCampaign", "http": {"method": "PUT", "requestUri": "/campaigns", "responseCode": 200}, "input": {"shape": "CreateCampaignRequest"}, "output": {"shape": "CreateCampaignResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Creates a campaign for the specified Amazon Connect account. This API is idempotent.</p>", "idempotent": true}, "DeleteCampaign": {"name": "DeleteCampaign", "http": {"method": "DELETE", "requestUri": "/campaigns/{id}", "responseCode": 200}, "input": {"shape": "DeleteCampaignRequest"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Deletes a campaign from the specified Amazon Connect account.</p>", "idempotent": true}, "DeleteConnectInstanceConfig": {"name": "DeleteConnectInstanceConfig", "http": {"method": "DELETE", "requestUri": "/connect-instance/{connectInstanceId}/config", "responseCode": 200}, "input": {"shape": "DeleteConnectInstanceConfigRequest"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InvalidStateException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Deletes a connect instance config from the specified AWS account.</p>", "idempotent": true}, "DeleteInstanceOnboardingJob": {"name": "DeleteInstanceOnboardingJob", "http": {"method": "DELETE", "requestUri": "/connect-instance/{connectInstanceId}/onboarding", "responseCode": 200}, "input": {"shape": "DeleteInstanceOnboardingJobRequest"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InvalidStateException"}], "documentation": "<p>Delete the Connect Campaigns onboarding job for the specified Amazon Connect instance.</p>", "idempotent": true}, "DescribeCampaign": {"name": "DescribeCampaign", "http": {"method": "GET", "requestUri": "/campaigns/{id}", "responseCode": 200}, "input": {"shape": "DescribeCampaignRequest"}, "output": {"shape": "DescribeCampaignResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Describes the specific campaign.</p>"}, "GetCampaignState": {"name": "GetCampaignState", "http": {"method": "GET", "requestUri": "/campaigns/{id}/state", "responseCode": 200}, "input": {"shape": "GetCampaignStateRequest"}, "output": {"shape": "GetCampaignStateResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Get state of a campaign for the specified Amazon Connect account.</p>"}, "GetCampaignStateBatch": {"name": "GetCampaignStateBatch", "http": {"method": "POST", "requestUri": "/campaigns-state", "responseCode": 200}, "input": {"shape": "GetCampaignStateBatchRequest"}, "output": {"shape": "GetCampaignStateBatchResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Get state of campaigns for the specified Amazon Connect account.</p>"}, "GetConnectInstanceConfig": {"name": "GetConnectInstanceConfig", "http": {"method": "GET", "requestUri": "/connect-instance/{connectInstanceId}/config", "responseCode": 200}, "input": {"shape": "GetConnectInstanceConfigRequest"}, "output": {"shape": "GetConnectInstanceConfigResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Get the specific Connect instance config.</p>"}, "GetInstanceOnboardingJobStatus": {"name": "GetInstanceOnboardingJobStatus", "http": {"method": "GET", "requestUri": "/connect-instance/{connectInstanceId}/onboarding", "responseCode": 200}, "input": {"shape": "GetInstanceOnboardingJobStatusRequest"}, "output": {"shape": "GetInstanceOnboardingJobStatusResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Get the specific instance onboarding job status.</p>"}, "ListCampaigns": {"name": "ListCampaigns", "http": {"method": "POST", "requestUri": "/campaigns-summary", "responseCode": 200}, "input": {"shape": "ListCampaignsRequest"}, "output": {"shape": "ListCampaignsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Provides summary information about the campaigns under the specified Amazon Connect account.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags/{arn}", "responseCode": 200}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}], "documentation": "<p>List tags for a resource.</p>", "idempotent": true}, "PauseCampaign": {"name": "PauseCampaign", "http": {"method": "POST", "requestUri": "/campaigns/{id}/pause", "responseCode": 200}, "input": {"shape": "PauseCampaignRequest"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "InvalidCampaignStateException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Pauses a campaign for the specified Amazon Connect account.</p>"}, "PutDialRequestBatch": {"name": "PutDialRequestBatch", "http": {"method": "PUT", "requestUri": "/campaigns/{id}/dial-requests", "responseCode": 200}, "input": {"shape": "PutDialRequestBatchRequest"}, "output": {"shape": "PutDialRequestBatchResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "InvalidCampaignStateException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Creates dials requests for the specified campaign Amazon Connect account. This API is idempotent.</p>", "idempotent": true}, "ResumeCampaign": {"name": "ResumeCampaign", "http": {"method": "POST", "requestUri": "/campaigns/{id}/resume", "responseCode": 200}, "input": {"shape": "ResumeCampaignRequest"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "InvalidCampaignStateException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Stops a campaign for the specified Amazon Connect account.</p>"}, "StartCampaign": {"name": "StartCampaign", "http": {"method": "POST", "requestUri": "/campaigns/{id}/start", "responseCode": 200}, "input": {"shape": "StartCampaignRequest"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "InvalidCampaignStateException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Starts a campaign for the specified Amazon Connect account.</p>"}, "StartInstanceOnboardingJob": {"name": "StartInstanceOnboardingJob", "http": {"method": "PUT", "requestUri": "/connect-instance/{connectInstanceId}/onboarding", "responseCode": 200}, "input": {"shape": "StartInstanceOnboardingJobRequest"}, "output": {"shape": "StartInstanceOnboardingJobResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Onboard the specific Amazon Connect instance to Connect Campaigns.</p>", "idempotent": true}, "StopCampaign": {"name": "StopCampaign", "http": {"method": "POST", "requestUri": "/campaigns/{id}/stop", "responseCode": 200}, "input": {"shape": "StopCampaignRequest"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "InvalidCampaignStateException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Stops a campaign for the specified Amazon Connect account.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags/{arn}", "responseCode": 200}, "input": {"shape": "TagResourceRequest"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Tag a resource.</p>", "idempotent": true}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags/{arn}", "responseCode": 200}, "input": {"shape": "UntagResourceRequest"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Untag a resource.</p>", "idempotent": true}, "UpdateCampaignDialerConfig": {"name": "UpdateCampaignDialerConfig", "http": {"method": "POST", "requestUri": "/campaigns/{id}/dialer-config", "responseCode": 200}, "input": {"shape": "UpdateCampaignDialerConfigRequest"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Updates the dialer config of a campaign. This API is idempotent.</p>", "idempotent": true}, "UpdateCampaignName": {"name": "UpdateCampaignName", "http": {"method": "POST", "requestUri": "/campaigns/{id}/name", "responseCode": 200}, "input": {"shape": "UpdateCampaignNameRequest"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Updates the name of a campaign. This API is idempotent.</p>", "idempotent": true}, "UpdateCampaignOutboundCallConfig": {"name": "UpdateCampaignOutboundCallConfig", "http": {"method": "POST", "requestUri": "/campaigns/{id}/outbound-call-config", "responseCode": 200}, "input": {"shape": "UpdateCampaignOutboundCallConfigRequest"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Updates the outbound call config of a campaign. This API is idempotent.</p>", "idempotent": true}}, "shapes": {"AccessDeniedException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}, "xAmzErrorType": {"shape": "XAmazonErrorType", "location": "header", "locationName": "x-amzn-ErrorType"}}, "documentation": "<p>You do not have sufficient access to perform this action.</p>", "error": {"httpStatusCode": 403, "senderFault": true}, "exception": true}, "AgentlessDialerConfig": {"type": "structure", "members": {"dialingCapacity": {"shape": "DialingCapacity"}}, "documentation": "<p><PERSON><PERSON> Dialer config</p>"}, "AnswerMachineDetectionConfig": {"type": "structure", "required": ["enableAnswerMachineDetection"], "members": {"enableAnswerMachineDetection": {"shape": "Boolean", "documentation": "<p>Enable or disable answering machine detection</p>"}, "awaitAnswerMachinePrompt": {"shape": "Boolean", "documentation": "<p>Enable or disable await answer machine prompt</p>"}}, "documentation": "<p>Answering Machine Detection config</p>"}, "Arn": {"type": "string", "documentation": "<p>Arn</p>", "max": 500, "min": 20, "pattern": "arn:.*"}, "AttributeName": {"type": "string", "documentation": "<p>The key of the attribute. Attribute keys can include only alphanumeric, dash, and underscore characters.</p>", "max": 32767, "min": 0, "pattern": "[a-zA-Z0-9\\-_]+"}, "AttributeValue": {"type": "string", "documentation": "<p>The value of the attribute.</p>", "max": 32767, "min": 0, "pattern": ".*"}, "Attributes": {"type": "map", "key": {"shape": "AttributeName"}, "value": {"shape": "AttributeValue"}, "documentation": "<p>A custom key-value pair using an attribute map. The attributes are standard Amazon Connect attributes, and can be accessed in contact flows just like any other contact attributes.</p>", "sensitive": true}, "BandwidthAllocation": {"type": "double", "documentation": "<p>The bandwidth allocation of a queue resource.</p>", "box": true, "max": 1, "min": 0}, "Boolean": {"type": "boolean", "box": true}, "Campaign": {"type": "structure", "required": ["id", "arn", "name", "connectInstanceId", "dialerConfig", "outboundCallConfig"], "members": {"id": {"shape": "CampaignId"}, "arn": {"shape": "CampaignArn"}, "name": {"shape": "CampaignName"}, "connectInstanceId": {"shape": "InstanceId"}, "dialerConfig": {"shape": "DialerConfig"}, "outboundCallConfig": {"shape": "OutboundCallConfig"}, "tags": {"shape": "TagMap"}}, "documentation": "<p>An Amazon Connect campaign.</p>"}, "CampaignArn": {"type": "string", "documentation": "<p>The resource name of an Amazon Connect campaign.</p>", "max": 500, "min": 20}, "CampaignFilters": {"type": "structure", "members": {"instanceIdFilter": {"shape": "Instance<PERSON>d<PERSON><PERSON>er"}}, "documentation": "<p>Filter model by type</p>"}, "CampaignId": {"type": "string", "documentation": "<p>Identifier representing a Campaign</p>", "max": 256, "min": 0, "pattern": "[\\S]*"}, "CampaignName": {"type": "string", "documentation": "<p>The name of an Amazon Connect Campaign name.</p>", "max": 127, "min": 1}, "CampaignState": {"type": "string", "documentation": "<p>State of a campaign</p>", "enum": ["Initialized", "Running", "Paused", "Stopped", "Failed"]}, "CampaignSummary": {"type": "structure", "required": ["id", "arn", "name", "connectInstanceId"], "members": {"id": {"shape": "CampaignId"}, "arn": {"shape": "CampaignArn"}, "name": {"shape": "CampaignName"}, "connectInstanceId": {"shape": "InstanceId"}}, "documentation": "<p>An Amazon Connect campaign summary.</p>"}, "CampaignSummaryList": {"type": "list", "member": {"shape": "CampaignSummary"}, "documentation": "<p>A list of Amazon Connect campaigns.</p>"}, "ClientToken": {"type": "string", "documentation": "<p>Client provided parameter used for idempotency. Its value must be unique for each request.</p>", "max": 200, "min": 0, "pattern": "[a-zA-Z0-9_\\-.]*"}, "ConflictException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}, "xAmzErrorType": {"shape": "XAmazonErrorType", "location": "header", "locationName": "x-amzn-ErrorType"}}, "documentation": "<p>The request could not be processed because of conflict in the current state of the resource.</p>", "error": {"httpStatusCode": 409, "senderFault": true}, "exception": true}, "ContactFlowId": {"type": "string", "documentation": "<p>The identifier of the contact flow for the outbound call.</p>", "max": 500, "min": 0}, "CreateCampaignRequest": {"type": "structure", "required": ["name", "connectInstanceId", "dialerConfig", "outboundCallConfig"], "members": {"name": {"shape": "CampaignName"}, "connectInstanceId": {"shape": "InstanceId"}, "dialerConfig": {"shape": "DialerConfig"}, "outboundCallConfig": {"shape": "OutboundCallConfig"}, "tags": {"shape": "TagMap"}}, "documentation": "<p>The request for Create Campaign API.</p>"}, "CreateCampaignResponse": {"type": "structure", "members": {"id": {"shape": "CampaignId"}, "arn": {"shape": "CampaignArn"}, "tags": {"shape": "TagMap"}}, "documentation": "<p>The response for Create Campaign API</p>"}, "DeleteCampaignRequest": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "CampaignId", "location": "uri", "locationName": "id"}}, "documentation": "<p>DeleteCampaignRequest</p>"}, "DeleteConnectInstanceConfigRequest": {"type": "structure", "required": ["connectInstanceId"], "members": {"connectInstanceId": {"shape": "InstanceId", "location": "uri", "locationName": "connectInstanceId"}}, "documentation": "<p>DeleteCampaignRequest</p>"}, "DeleteInstanceOnboardingJobRequest": {"type": "structure", "required": ["connectInstanceId"], "members": {"connectInstanceId": {"shape": "InstanceId", "location": "uri", "locationName": "connectInstanceId"}}, "documentation": "<p>The request for DeleteInstanceOnboardingJob API.</p>"}, "DescribeCampaignRequest": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "CampaignId", "location": "uri", "locationName": "id"}}, "documentation": "<p>DescribeCampaignRequests</p>"}, "DescribeCampaignResponse": {"type": "structure", "members": {"campaign": {"shape": "Campaign"}}, "documentation": "<p>DescribeCampaignResponse</p>"}, "DestinationPhoneNumber": {"type": "string", "documentation": "<p>The phone number of the customer, in E.164 format.</p>", "max": 20, "min": 0, "pattern": "[\\d\\-+]*", "sensitive": true}, "DialRequest": {"type": "structure", "required": ["clientToken", "phoneNumber", "expirationTime", "attributes"], "members": {"clientToken": {"shape": "ClientToken"}, "phoneNumber": {"shape": "DestinationPhoneNumber"}, "expirationTime": {"shape": "TimeStamp"}, "attributes": {"shape": "Attributes"}}, "documentation": "<p>A dial request for a campaign.</p>"}, "DialRequestId": {"type": "string", "documentation": "<p>Identifier representing a Dial request</p>", "max": 256, "min": 0, "pattern": "[a-zA-Z0-9_\\-.]*"}, "DialRequestList": {"type": "list", "member": {"shape": "DialRequest"}, "documentation": "<p>A list of dial requests.</p>", "max": 25, "min": 1}, "DialerConfig": {"type": "structure", "members": {"progressiveDialerConfig": {"shape": "ProgressiveDialerConfig"}, "predictiveDialerConfig": {"shape": "PredictiveDialerConfig"}, "agentlessDialerConfig": {"shape": "AgentlessDialerConfig"}}, "documentation": "<p>The possible types of dialer config parameters</p>", "union": true}, "DialingCapacity": {"type": "double", "documentation": "<p>Allocates dialing capacity for this campaign between multiple active campaigns</p>", "box": true, "max": 1, "min": 0.01}, "Enabled": {"type": "boolean", "documentation": "<p>Boolean to indicate if custom encryption has been enabled.</p>"}, "EncryptionConfig": {"type": "structure", "required": ["enabled"], "members": {"enabled": {"shape": "Enabled"}, "encryptionType": {"shape": "EncryptionType"}, "keyArn": {"shape": "EncryptionKey"}}, "documentation": "<p>Encryption config for Connect Instance. Note that sensitive data will always be encrypted. If disabled, service will perform encryption with its own key. If enabled, a KMS key id needs to be provided and KMS charges will apply. KMS is only type supported</p>"}, "EncryptionKey": {"type": "string", "documentation": "<p>KMS key id/arn for encryption config.</p>", "max": 500, "min": 0}, "EncryptionType": {"type": "string", "documentation": "<p>Server-side encryption type.</p>", "enum": ["KMS"]}, "FailedCampaignStateResponse": {"type": "structure", "members": {"campaignId": {"shape": "CampaignId"}, "failureCode": {"shape": "GetCampaignStateBatchFailureCode"}}, "documentation": "<p>Failed response of campaign state</p>"}, "FailedCampaignStateResponseList": {"type": "list", "member": {"shape": "FailedCampaignStateResponse"}, "documentation": "<p>List of failed requests of campaign state</p>", "max": 25, "min": 0}, "FailedRequest": {"type": "structure", "members": {"clientToken": {"shape": "ClientToken"}, "id": {"shape": "DialRequestId"}, "failureCode": {"shape": "FailureCode"}}, "documentation": "<p>A failed request identified by the unique client token.</p>"}, "FailedRequestList": {"type": "list", "member": {"shape": "FailedRequest"}, "documentation": "<p>A list of failed requests.</p>", "max": 25, "min": 0}, "FailureCode": {"type": "string", "documentation": "<p>A predefined code indicating the error that caused the failure.</p>", "enum": ["InvalidInput", "RequestThrottled", "UnknownE<PERSON>r"]}, "GetCampaignStateBatchFailureCode": {"type": "string", "documentation": "<p>A predefined code indicating the error that caused the failure in getting state of campaigns</p>", "enum": ["ResourceNotFound", "UnknownE<PERSON>r"]}, "GetCampaignStateBatchRequest": {"type": "structure", "required": ["campaignIds"], "members": {"campaignIds": {"shape": "GetCampaignStateBatchRequestCampaignIdsList"}}, "documentation": "<p>GetCampaignStateBatchRequest</p>"}, "GetCampaignStateBatchRequestCampaignIdsList": {"type": "list", "member": {"shape": "CampaignId"}, "documentation": "<p>List of CampaignId</p>", "max": 25, "min": 1}, "GetCampaignStateBatchResponse": {"type": "structure", "members": {"successfulRequests": {"shape": "SuccessfulCampaignStateResponseList"}, "failedRequests": {"shape": "FailedCampaignStateResponseList"}}, "documentation": "<p>GetCampaignStateBatchResponse</p>"}, "GetCampaignStateRequest": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "CampaignId", "location": "uri", "locationName": "id"}}, "documentation": "<p>GetCampaignStateRequest</p>"}, "GetCampaignStateResponse": {"type": "structure", "members": {"state": {"shape": "CampaignState"}}, "documentation": "<p>GetCampaignStateResponse</p>"}, "GetConnectInstanceConfigRequest": {"type": "structure", "required": ["connectInstanceId"], "members": {"connectInstanceId": {"shape": "InstanceId", "location": "uri", "locationName": "connectInstanceId"}}, "documentation": "<p>GetConnectInstanceConfigRequest</p>"}, "GetConnectInstanceConfigResponse": {"type": "structure", "members": {"connectInstanceConfig": {"shape": "InstanceConfig"}}, "documentation": "<p>GetConnectInstanceConfigResponse</p>"}, "GetInstanceOnboardingJobStatusRequest": {"type": "structure", "required": ["connectInstanceId"], "members": {"connectInstanceId": {"shape": "InstanceId", "location": "uri", "locationName": "connectInstanceId"}}, "documentation": "<p>GetInstanceOnboardingJobStatusRequest</p>"}, "GetInstanceOnboardingJobStatusResponse": {"type": "structure", "members": {"connectInstanceOnboardingJobStatus": {"shape": "InstanceOnboardingJobStatus"}}, "documentation": "<p>GetInstanceOnboardingJobStatusResponse</p>"}, "InstanceConfig": {"type": "structure", "required": ["connectInstanceId", "serviceLinkedRoleArn", "encryptionConfig"], "members": {"connectInstanceId": {"shape": "InstanceId"}, "serviceLinkedRoleArn": {"shape": "ServiceLinkedRoleArn"}, "encryptionConfig": {"shape": "EncryptionConfig"}}, "documentation": "<p>Instance config object</p>"}, "InstanceId": {"type": "string", "documentation": "<p>Amazon Connect Instance Id</p>", "max": 256, "min": 0, "pattern": "[a-zA-Z0-9_\\-.]*"}, "InstanceIdFilter": {"type": "structure", "required": ["value", "operator"], "members": {"value": {"shape": "InstanceId"}, "operator": {"shape": "InstanceIdFilterOperator"}}, "documentation": "<p>Connect instance identifier filter</p>"}, "InstanceIdFilterOperator": {"type": "string", "documentation": "<p>Operators for Connect instance identifier filter</p>", "enum": ["Eq"]}, "InstanceOnboardingJobFailureCode": {"type": "string", "documentation": "<p>Enumeration of the possible failure codes for instance onboarding job</p>", "enum": ["EVENT_BRIDGE_ACCESS_DENIED", "EVENT_BRIDGE_MANAGED_RULE_LIMIT_EXCEEDED", "IAM_ACCESS_DENIED", "KMS_ACCESS_DENIED", "KMS_KEY_NOT_FOUND", "INTERNAL_FAILURE"]}, "InstanceOnboardingJobStatus": {"type": "structure", "required": ["connectInstanceId", "status"], "members": {"connectInstanceId": {"shape": "InstanceId"}, "status": {"shape": "InstanceOnboardingJobStatusCode"}, "failureCode": {"shape": "InstanceOnboardingJobFailureCode"}}, "documentation": "<p>Instance onboarding job status object</p>"}, "InstanceOnboardingJobStatusCode": {"type": "string", "documentation": "<p>Enumeration of the possible states for instance onboarding job</p>", "enum": ["IN_PROGRESS", "SUCCEEDED", "FAILED"]}, "InternalServerException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}, "xAmzErrorType": {"shape": "XAmazonErrorType", "location": "header", "locationName": "x-amzn-ErrorType"}}, "documentation": "<p>Request processing failed because of an error or failure with the service.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true, "retryable": {"throttling": false}}, "InvalidCampaignStateException": {"type": "structure", "required": ["state", "message"], "members": {"state": {"shape": "CampaignState"}, "message": {"shape": "String"}, "xAmzErrorType": {"shape": "XAmazonErrorType", "location": "header", "locationName": "x-amzn-ErrorType"}}, "documentation": "<p>The request could not be processed because of conflict in the current state of the campaign.</p>", "error": {"httpStatusCode": 409, "senderFault": true}, "exception": true}, "InvalidStateException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}, "xAmzErrorType": {"shape": "XAmazonErrorType", "location": "header", "locationName": "x-amzn-ErrorType"}}, "documentation": "<p>The request could not be processed because of conflict in the current state.</p>", "error": {"httpStatusCode": 409, "senderFault": true}, "exception": true}, "ListCampaignsRequest": {"type": "structure", "members": {"maxResults": {"shape": "MaxResults"}, "nextToken": {"shape": "NextToken"}, "filters": {"shape": "CampaignFilters"}}, "documentation": "<p>ListCampaignsRequest</p>"}, "ListCampaignsResponse": {"type": "structure", "members": {"nextToken": {"shape": "NextToken"}, "campaignSummaryList": {"shape": "CampaignSummaryList"}}, "documentation": "<p>ListCampaignsResponse</p>"}, "ListTagsForResourceRequest": {"type": "structure", "required": ["arn"], "members": {"arn": {"shape": "<PERSON><PERSON>", "location": "uri", "locationName": "arn"}}, "documentation": "<p>ListTagsForResource</p>"}, "ListTagsForResourceResponse": {"type": "structure", "members": {"tags": {"shape": "TagMap"}}, "documentation": "<p>ListTagsForResponse</p>"}, "MaxResults": {"type": "integer", "documentation": "<p>The maximum number of results to return per page.</p>", "box": true, "max": 50, "min": 1}, "NextToken": {"type": "string", "documentation": "<p>The token for the next set of results.</p>", "max": 1000, "min": 0}, "OutboundCallConfig": {"type": "structure", "required": ["connectContactFlowId"], "members": {"connectContactFlowId": {"shape": "ContactFlowId"}, "connectSourcePhoneNumber": {"shape": "SourcePhoneNumber"}, "connectQueueId": {"shape": "QueueId"}, "answerMachineDetectionConfig": {"shape": "AnswerMachineDetectionConfig"}}, "documentation": "<p>The configuration used for outbound calls.</p>"}, "PauseCampaignRequest": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "CampaignId", "location": "uri", "locationName": "id"}}, "documentation": "<p>PauseCampaignRequest</p>"}, "PredictiveDialerConfig": {"type": "structure", "required": ["bandwidthAllocation"], "members": {"bandwidthAllocation": {"shape": "BandwidthAllocation"}, "dialingCapacity": {"shape": "DialingCapacity"}}, "documentation": "<p>Predictive Dialer config</p>"}, "ProgressiveDialerConfig": {"type": "structure", "required": ["bandwidthAllocation"], "members": {"bandwidthAllocation": {"shape": "BandwidthAllocation"}, "dialingCapacity": {"shape": "DialingCapacity"}}, "documentation": "<p>Progressive Dialer config</p>"}, "PutDialRequestBatchRequest": {"type": "structure", "required": ["id", "dialRequests"], "members": {"id": {"shape": "CampaignId", "location": "uri", "locationName": "id"}, "dialRequests": {"shape": "DialRequestList"}}, "documentation": "<p>PutDialRequestBatchRequest</p>"}, "PutDialRequestBatchResponse": {"type": "structure", "members": {"successfulRequests": {"shape": "SuccessfulRequestList"}, "failedRequests": {"shape": "FailedRequestList"}}, "documentation": "<p>PutDialRequestBatchResponse</p>"}, "QueueId": {"type": "string", "documentation": "<p>The queue for the call. If you specify a queue, the phone displayed for caller ID is the phone number specified in the queue. If you do not specify a queue, the queue defined in the contact flow is used. If you do not specify a queue, you must specify a source phone number.</p>", "max": 500, "min": 0}, "ResourceNotFoundException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}, "xAmzErrorType": {"shape": "XAmazonErrorType", "location": "header", "locationName": "x-amzn-ErrorType"}}, "documentation": "<p>The specified resource was not found.</p>", "error": {"httpStatusCode": 404, "senderFault": true}, "exception": true}, "ResumeCampaignRequest": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "CampaignId", "location": "uri", "locationName": "id"}}, "documentation": "<p>ResumeCampaignRequest</p>"}, "ServiceLinkedRoleArn": {"type": "string", "documentation": "<p>Service linked role arn</p>", "max": 256, "min": 0}, "ServiceQuotaExceededException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}, "xAmzErrorType": {"shape": "XAmazonErrorType", "location": "header", "locationName": "x-amzn-ErrorType"}}, "documentation": "<p>Request would cause a service quota to be exceeded.</p>", "error": {"httpStatusCode": 402, "senderFault": true}, "exception": true}, "SourcePhoneNumber": {"type": "string", "documentation": "<p>The phone number associated with the Amazon Connect instance, in E.164 format. If you do not specify a source phone number, you must specify a queue.</p>", "max": 100, "min": 0}, "StartCampaignRequest": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "CampaignId", "location": "uri", "locationName": "id"}}, "documentation": "<p>StartCampaignRequest</p>"}, "StartInstanceOnboardingJobRequest": {"type": "structure", "required": ["connectInstanceId", "encryptionConfig"], "members": {"connectInstanceId": {"shape": "InstanceId", "location": "uri", "locationName": "connectInstanceId"}, "encryptionConfig": {"shape": "EncryptionConfig"}}, "documentation": "<p>The request for StartInstanceOnboardingJob API.</p>"}, "StartInstanceOnboardingJobResponse": {"type": "structure", "members": {"connectInstanceOnboardingJobStatus": {"shape": "InstanceOnboardingJobStatus"}}, "documentation": "<p>The response for StartInstanceOnboardingJob API.</p>"}, "StopCampaignRequest": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "CampaignId", "location": "uri", "locationName": "id"}}, "documentation": "<p>StopCampaignRequest</p>"}, "String": {"type": "string"}, "SuccessfulCampaignStateResponse": {"type": "structure", "members": {"campaignId": {"shape": "CampaignId"}, "state": {"shape": "CampaignState"}}, "documentation": "<p>Successful response of campaign state</p>"}, "SuccessfulCampaignStateResponseList": {"type": "list", "member": {"shape": "SuccessfulCampaignStateResponse"}, "documentation": "<p>List of successful response of campaign state</p>", "max": 25, "min": 0}, "SuccessfulRequest": {"type": "structure", "members": {"clientToken": {"shape": "ClientToken"}, "id": {"shape": "DialRequestId"}}, "documentation": "<p>A successful request identified by the unique client token.</p>"}, "SuccessfulRequestList": {"type": "list", "member": {"shape": "SuccessfulRequest"}, "documentation": "<p>A list of successful requests identified by the unique client token.</p>", "max": 25, "min": 0}, "TagKey": {"type": "string", "documentation": "<p>Tag key.</p>", "max": 128, "min": 1, "pattern": "(?!aws:)[a-zA-Z+-=._:/]+"}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "documentation": "<p>List of tag keys.</p>", "max": 50, "min": 0}, "TagMap": {"type": "map", "key": {"shape": "TagKey"}, "value": {"shape": "TagValue"}, "documentation": "<p>Tag map with key and value.</p>"}, "TagResourceRequest": {"type": "structure", "required": ["arn", "tags"], "members": {"arn": {"shape": "<PERSON><PERSON>", "location": "uri", "locationName": "arn"}, "tags": {"shape": "TagMap"}}, "documentation": "<p>TagResourceRequest</p>"}, "TagValue": {"type": "string", "documentation": "<p>Tag value.</p>", "max": 256, "min": 0}, "ThrottlingException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}, "xAmzErrorType": {"shape": "XAmazonErrorType", "location": "header", "locationName": "x-amzn-ErrorType"}}, "documentation": "<p>The request was denied due to request throttling.</p>", "error": {"httpStatusCode": 429, "senderFault": true}, "exception": true, "retryable": {"throttling": false}}, "TimeStamp": {"type": "timestamp", "documentation": "<p>Timestamp with no UTC offset or timezone</p>", "timestampFormat": "iso8601"}, "UntagResourceRequest": {"type": "structure", "required": ["arn", "tagKeys"], "members": {"arn": {"shape": "<PERSON><PERSON>", "location": "uri", "locationName": "arn"}, "tagKeys": {"shape": "TagKeyList", "location": "querystring", "locationName": "tagKeys"}}, "documentation": "<p>UntagResourceRequest</p>"}, "UpdateCampaignDialerConfigRequest": {"type": "structure", "required": ["id", "dialerConfig"], "members": {"id": {"shape": "CampaignId", "location": "uri", "locationName": "id"}, "dialerConfig": {"shape": "DialerConfig"}}, "documentation": "<p>UpdateCampaignDialerConfigRequest</p>"}, "UpdateCampaignNameRequest": {"type": "structure", "required": ["id", "name"], "members": {"id": {"shape": "CampaignId", "location": "uri", "locationName": "id"}, "name": {"shape": "CampaignName"}}, "documentation": "<p>UpdateCampaignNameRequest</p>"}, "UpdateCampaignOutboundCallConfigRequest": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "CampaignId", "location": "uri", "locationName": "id"}, "connectContactFlowId": {"shape": "ContactFlowId"}, "connectSourcePhoneNumber": {"shape": "SourcePhoneNumber"}, "answerMachineDetectionConfig": {"shape": "AnswerMachineDetectionConfig"}}, "documentation": "<p>UpdateCampaignOutboundCallConfigRequest</p>"}, "ValidationException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}, "xAmzErrorType": {"shape": "XAmazonErrorType", "location": "header", "locationName": "x-amzn-ErrorType"}}, "documentation": "<p>The input fails to satisfy the constraints specified by an AWS service.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "XAmazonErrorType": {"type": "string", "documentation": "<p>A header that defines the error encountered while processing the request.</p>"}}, "documentation": "<p>Provide APIs to create and manage Amazon Connect Campaigns.</p>"}