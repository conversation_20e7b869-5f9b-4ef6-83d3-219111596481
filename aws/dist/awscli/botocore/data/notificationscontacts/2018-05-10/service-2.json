{"version": "2.0", "metadata": {"apiVersion": "2018-05-10", "auth": ["aws.auth#sigv4"], "endpointPrefix": "notifications-contacts", "protocol": "rest-json", "protocols": ["rest-json"], "serviceFullName": "AWS User Notifications Contacts", "serviceId": "NotificationsContacts", "signatureVersion": "v4", "signingName": "notifications-contacts", "uid": "notificationscontacts-2018-05-10"}, "operations": {"ActivateEmailContact": {"name": "ActivateEmailContact", "http": {"method": "PUT", "requestUri": "/emailcontacts/{arn}/activate/{code}", "responseCode": 200}, "input": {"shape": "ActivateEmailContactRequest"}, "output": {"shape": "ActivateEmailContactResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Activates an email contact using an activation code. This code is in the activation email sent to the email address associated with this email contact.</p>", "idempotent": true}, "CreateEmailContact": {"name": "CreateEmailContact", "http": {"method": "POST", "requestUri": "/2022-09-19/emailcontacts", "responseCode": 201}, "input": {"shape": "CreateEmailContactRequest"}, "output": {"shape": "CreateEmailContactResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}], "documentation": "<p>Creates an email contact for the provided email address.</p>"}, "DeleteEmailContact": {"name": "DeleteEmailContact", "http": {"method": "DELETE", "requestUri": "/emailcontacts/{arn}", "responseCode": 200}, "input": {"shape": "DeleteEmailContactRequest"}, "output": {"shape": "DeleteEmailContactResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Deletes an email contact.</p> <note> <p>Deleting an email contact removes it from all associated notification configurations.</p> </note>", "idempotent": true}, "GetEmailContact": {"name": "GetEmailContact", "http": {"method": "GET", "requestUri": "/emailcontacts/{arn}", "responseCode": 200}, "input": {"shape": "GetEmailContactRequest"}, "output": {"shape": "GetEmailContactResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Returns an email contact.</p>"}, "ListEmailContacts": {"name": "ListEmailContacts", "http": {"method": "GET", "requestUri": "/emailcontacts", "responseCode": 200}, "input": {"shape": "ListEmailContactsRequest"}, "output": {"shape": "ListEmailContactsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists all email contacts created under the Account.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags/{arn}", "responseCode": 200}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists all of the tags associated with the Amazon Resource Name (ARN) that you specify. The resource can be a user, server, or role.</p>"}, "SendActivationCode": {"name": "SendActivationCode", "http": {"method": "POST", "requestUri": "/2022-10-31/emailcontacts/{arn}/activate/send", "responseCode": 200}, "input": {"shape": "SendActivationCodeRequest"}, "output": {"shape": "SendActivationCodeResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Sends an activation email to the email address associated with the specified email contact.</p> <note> <p>It might take a few minutes for the activation email to arrive. If it doesn't arrive, check in your spam folder or try sending another activation email.</p> </note>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags/{arn}", "responseCode": 200}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Attaches a key-value pair to a resource, as identified by its Amazon Resource Name (ARN). Taggable resources in AWS User Notifications Contacts include email contacts.</p>", "idempotent": true}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags/{arn}", "responseCode": 200}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Detaches a key-value pair from a resource, as identified by its Amazon Resource Name (ARN). Taggable resources in AWS User Notifications Contacts include email contacts..</p>", "idempotent": true}}, "shapes": {"AccessDeniedException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>You do not have sufficient access to perform this action.</p>", "error": {"httpStatusCode": 403, "senderFault": true}, "exception": true}, "ActivateEmailContactRequest": {"type": "structure", "required": ["arn", "code"], "members": {"arn": {"shape": "EmailContactArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource.</p>", "location": "uri", "locationName": "arn"}, "code": {"shape": "Token", "documentation": "<p>The activation code for this email contact.</p> <p>An email contact has a maximum of five activation attempts. Activation codes expire after 12 hours and are generated by the <a href=\"https://docs.aws.amazon.com/notificationscontacts/latest/APIReference/API_SendActivationCode.html\">SendActivationCode</a> API action.</p>", "location": "uri", "locationName": "code"}}}, "ActivateEmailContactResponse": {"type": "structure", "members": {}}, "ConflictException": {"type": "structure", "required": ["message", "resourceId", "resourceType"], "members": {"message": {"shape": "ErrorMessage"}, "resourceId": {"shape": "ResourceId", "documentation": "<p>The resource ID that prompted the conflict error.</p>"}, "resourceType": {"shape": "ResourceType", "documentation": "<p>The resource type that prompted the conflict error.</p>"}}, "documentation": "<p>Updating or deleting a resource can cause an inconsistent state.</p>", "error": {"httpStatusCode": 409, "senderFault": true}, "exception": true}, "CreateEmailContactRequest": {"type": "structure", "required": ["name", "emailAddress"], "members": {"name": {"shape": "EmailContactName", "documentation": "<p>The name of the email contact.</p>"}, "emailAddress": {"shape": "EmailContactAddress", "documentation": "<p>The email address this email contact points to. The activation email and any subscribed emails are sent here.</p> <note> <p>This email address can't receive emails until it's activated.</p> </note>"}, "tags": {"shape": "TagMap", "documentation": "<p>A map of tags assigned to a resource. A tag is a string-to-string map of key-value pairs.</p>"}}}, "CreateEmailContactResponse": {"type": "structure", "required": ["arn"], "members": {"arn": {"shape": "EmailContactArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource.</p>"}}}, "CreationTime": {"type": "timestamp", "timestampFormat": "iso8601"}, "DeleteEmailContactRequest": {"type": "structure", "required": ["arn"], "members": {"arn": {"shape": "EmailContactArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource.</p>", "location": "uri", "locationName": "arn"}}}, "DeleteEmailContactResponse": {"type": "structure", "members": {}}, "EmailContact": {"type": "structure", "required": ["arn", "name", "address", "status", "creationTime", "updateTime"], "members": {"arn": {"shape": "EmailContactArn", "documentation": "<p>The Amazon Resource Name (ARN) of the email contact.</p>"}, "name": {"shape": "EmailContactName", "documentation": "<p>The name of the email contact.</p>"}, "address": {"shape": "SensitiveEmailContactAddress", "documentation": "<p>The email address this email contact points to. The activation email and any subscribed emails are sent here.</p>"}, "status": {"shape": "EmailContactStatus", "documentation": "<p>The status of the email contact. Only activated email contacts receive emails.</p>"}, "creationTime": {"shape": "CreationTime", "documentation": "<p>The creation time of the resource.</p>"}, "updateTime": {"shape": "UpdateTime", "documentation": "<p>The time the resource was last updated.</p>"}}, "documentation": "<p>An email contact.</p>"}, "EmailContactAddress": {"type": "string", "max": 254, "min": 6, "pattern": "(.+)@(.+)"}, "EmailContactArn": {"type": "string", "pattern": "arn:aws:notifications-contacts::[0-9]{12}:emailcontact/[a-z0-9]{27}"}, "EmailContactName": {"type": "string", "max": 64, "min": 1, "pattern": ".*[\\w-.~]+.*", "sensitive": true}, "EmailContactStatus": {"type": "string", "enum": ["inactive", "active"]}, "EmailContacts": {"type": "list", "member": {"shape": "EmailContact"}}, "ErrorMessage": {"type": "string"}, "GetEmailContactRequest": {"type": "structure", "required": ["arn"], "members": {"arn": {"shape": "EmailContactArn", "documentation": "<p>The Amazon Resource Name (ARN) of the email contact to get.</p>", "location": "uri", "locationName": "arn"}}}, "GetEmailContactResponse": {"type": "structure", "required": ["emailContact"], "members": {"emailContact": {"shape": "EmailContact", "documentation": "<p>The email contact for the provided email address.</p>"}}}, "Integer": {"type": "integer", "box": true}, "InternalServerException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>Unexpected error during processing of request.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true, "retryable": {"throttling": false}}, "ListEmailContactsRequest": {"type": "structure", "members": {"maxResults": {"shape": "ListEmailContactsRequestMaxResultsInteger", "documentation": "<p>The maximum number of results to include in the response. If more results exist than the specified MaxResults value, a token is included in the response so that the remaining results can be retrieved.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "String", "documentation": "<p>An optional token returned from a prior request. Use this token for pagination of results from this action. If this parameter is specified, the response includes only results beyond the token, up to the value specified by MaxResults.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListEmailContactsRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListEmailContactsResponse": {"type": "structure", "required": ["emailContacts"], "members": {"nextToken": {"shape": "String", "documentation": "<p>An optional token returned from a prior request. Use this token for pagination of results from this action. If this parameter is specified, the response includes only results beyond the token, up to the value specified by MaxResults.</p>"}, "emailContacts": {"shape": "EmailContacts", "documentation": "<p>A list of email contacts.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["arn"], "members": {"arn": {"shape": "EmailContactArn", "documentation": "<p>The ARN you specified to list the tags of.</p>", "location": "uri", "locationName": "arn"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"tags": {"shape": "TagMap", "documentation": "<p>Key-value pairs that are assigned to a resource, usually for the purpose of grouping and searching for items. Tags are metadata that you define.</p>"}}}, "QuotaCode": {"type": "string"}, "ResourceId": {"type": "string"}, "ResourceNotFoundException": {"type": "structure", "required": ["message", "resourceId", "resourceType"], "members": {"message": {"shape": "ErrorMessage"}, "resourceId": {"shape": "ResourceId", "documentation": "<p>The ID of the resource that wasn't found.</p>"}, "resourceType": {"shape": "ResourceType", "documentation": "<p>The type of resource that wasn't found.</p>"}}, "documentation": "<p>Your request references a resource which does not exist. </p>", "error": {"httpStatusCode": 404, "senderFault": true}, "exception": true}, "ResourceType": {"type": "string"}, "SendActivationCodeRequest": {"type": "structure", "required": ["arn"], "members": {"arn": {"shape": "EmailContactArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource.</p>", "location": "uri", "locationName": "arn"}}}, "SendActivationCodeResponse": {"type": "structure", "members": {}}, "SensitiveEmailContactAddress": {"type": "string", "max": 254, "min": 6, "pattern": "(.+)@(.+)", "sensitive": true}, "ServiceCode": {"type": "string"}, "ServiceQuotaExceededException": {"type": "structure", "required": ["message", "resourceId", "resourceType", "serviceCode", "quotaCode"], "members": {"message": {"shape": "ErrorMessage"}, "resourceId": {"shape": "ResourceId", "documentation": "<p>The ID of the resource that exceeds the service quota.</p>"}, "resourceType": {"shape": "ResourceType", "documentation": "<p>The type of the resource that exceeds the service quota.</p>"}, "serviceCode": {"shape": "ServiceCode", "documentation": "<p>The code for the service quota exceeded in <a href=\"https://docs.aws.amazon.com/servicequotas/latest/userguide/intro.html\">Service Quotas</a>.</p>"}, "quotaCode": {"shape": "QuotaCode", "documentation": "<p>The code for the service quota in <a href=\"https://docs.aws.amazon.com/servicequotas/latest/userguide/intro.html\">Service Quotas</a>.</p>"}}, "documentation": "<p> Request would cause a service quota to be exceeded.</p>", "error": {"httpStatusCode": 402, "senderFault": true}, "exception": true}, "String": {"type": "string"}, "TagKey": {"type": "string", "pattern": "(?!aws:).{1,128}"}, "TagKeys": {"type": "list", "member": {"shape": "TagKey"}}, "TagMap": {"type": "map", "key": {"shape": "TagKey"}, "value": {"shape": "TagValue"}, "documentation": "<p>Map of tags assigned to a resource</p>", "max": 200, "min": 0}, "TagResourceRequest": {"type": "structure", "required": ["arn", "tags"], "members": {"arn": {"shape": "EmailContactArn", "documentation": "<p>The ARN of the configuration.</p>", "location": "uri", "locationName": "arn"}, "tags": {"shape": "TagMap", "documentation": "<p>A list of tags to apply to the configuration.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0}, "ThrottlingException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "ErrorMessage"}, "serviceCode": {"shape": "ServiceCode", "documentation": "<p>Identifies the service being throttled.</p>"}, "quotaCode": {"shape": "QuotaCode", "documentation": "<p>Identifies the quota that is being throttled.</p>"}, "retryAfterSeconds": {"shape": "Integer", "documentation": "<p>The number of seconds a client should wait before retrying the request.</p>", "location": "header", "locationName": "Retry-After"}}, "documentation": "<p>The request was denied due to request throttling.</p>", "error": {"httpStatusCode": 429, "senderFault": true}, "exception": true, "retryable": {"throttling": true}}, "Token": {"type": "string", "max": 7, "min": 7, "pattern": "[a-z0-9]{7}", "sensitive": true}, "UntagResourceRequest": {"type": "structure", "required": ["arn", "tagKeys"], "members": {"arn": {"shape": "EmailContactArn", "documentation": "<p>The value of the resource that will have the tag removed. An Amazon Resource Name (ARN) is an identifier for a specific AWS resource, such as a server, user, or role.</p>", "location": "uri", "locationName": "arn"}, "tagKeys": {"shape": "TagKeys", "documentation": "<p>Specifies a list of tag keys that you want to remove from the specified resources.</p>", "location": "querystring", "locationName": "tagKeys"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateTime": {"type": "timestamp", "timestampFormat": "iso8601"}, "ValidationException": {"type": "structure", "required": ["message", "reason"], "members": {"message": {"shape": "ErrorMessage"}, "reason": {"shape": "ValidationExceptionReason", "documentation": "<p>The reason why your input is considered invalid.</p>"}, "fieldList": {"shape": "ValidationExceptionFieldList", "documentation": "<p>The list of input fields that are invalid.</p>"}}, "documentation": "<p>The input fails to satisfy the constraints specified by an AWS service.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "ValidationExceptionField": {"type": "structure", "required": ["name", "message"], "members": {"name": {"shape": "String", "documentation": "<p>The field name where the invalid entry was detected.</p>"}, "message": {"shape": "String", "documentation": "<p>A message with the reason for the validation exception error.</p>"}}, "documentation": "<p>Stores information about a field passed inside a request that resulted in an exception.</p>"}, "ValidationExceptionFieldList": {"type": "list", "member": {"shape": "ValidationExceptionField"}}, "ValidationExceptionReason": {"type": "string", "enum": ["fieldValidationFailed", "other"]}}, "documentation": "<p>AWS User Notifications Contacts is a service that allows you to create and manage email contacts for AWS User Notifications. The AWS User Notifications Contacts API Reference provides descriptions, API request parameters, and the JSON response for all email contact related API actions.</p>"}