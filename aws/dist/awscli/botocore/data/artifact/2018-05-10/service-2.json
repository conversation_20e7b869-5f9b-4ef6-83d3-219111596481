{"version": "2.0", "metadata": {"apiVersion": "2018-05-10", "auth": ["aws.auth#sigv4"], "endpointPrefix": "artifact", "protocol": "rest-json", "protocols": ["rest-json"], "serviceFullName": "AWS Artifact", "serviceId": "Artifact", "signatureVersion": "v4", "signingName": "artifact", "uid": "artifact-2018-05-10"}, "operations": {"GetAccountSettings": {"name": "GetAccountSettings", "http": {"method": "GET", "requestUri": "/v1/account-settings/get", "responseCode": 200}, "input": {"shape": "GetAccountSettingsRequest"}, "output": {"shape": "GetAccountSettingsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Get the account settings for Artifact.</p>"}, "GetReport": {"name": "GetReport", "http": {"method": "GET", "requestUri": "/v1/report/get", "responseCode": 200}, "input": {"shape": "GetReportRequest"}, "output": {"shape": "GetReportResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Get the content for a single report.</p>"}, "GetReportMetadata": {"name": "GetReportMetadata", "http": {"method": "GET", "requestUri": "/v1/report/getMetadata", "responseCode": 200}, "input": {"shape": "GetReportMetadataRequest"}, "output": {"shape": "GetReportMetadataResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Get the metadata for a single report.</p>"}, "GetTermForReport": {"name": "GetTermForReport", "http": {"method": "GET", "requestUri": "/v1/report/getTermForReport", "responseCode": 200}, "input": {"shape": "GetTermForReportRequest"}, "output": {"shape": "GetTermForReportResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Get the Term content associated with a single report.</p>"}, "ListCustomerAgreements": {"name": "ListCustomerAgreements", "http": {"method": "GET", "requestUri": "/v1/customer-agreement/list", "responseCode": 200}, "input": {"shape": "ListCustomerAgreementsRequest"}, "output": {"shape": "ListCustomerAgreementsResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}], "documentation": "<p>List active customer-agreements applicable to calling identity.</p>"}, "ListReports": {"name": "ListReports", "http": {"method": "GET", "requestUri": "/v1/report/list", "responseCode": 200}, "input": {"shape": "ListReportsRequest"}, "output": {"shape": "ListReportsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>List available reports.</p>"}, "PutAccountSettings": {"name": "PutAccountSettings", "http": {"method": "PUT", "requestUri": "/v1/account-settings/put", "responseCode": 200}, "input": {"shape": "PutAccountSettingsRequest"}, "output": {"shape": "PutAccountSettingsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Put the account settings for Artifact.</p>", "idempotent": true}}, "shapes": {"AcceptanceType": {"type": "string", "enum": ["PASSTHROUGH", "EXPLICIT"]}, "AccessDeniedException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>User does not have sufficient access to perform this action.</p>", "error": {"httpStatusCode": 403, "senderFault": true}, "exception": true}, "AccountSettings": {"type": "structure", "members": {"notificationSubscriptionStatus": {"shape": "NotificationSubscriptionStatus", "documentation": "<p>Notification subscription status of the customer.</p>"}}, "documentation": "<p>Account settings for the customer.</p>"}, "AgreementTerms": {"type": "list", "member": {"shape": "LongStringAttribute"}, "max": 10, "min": 0}, "AgreementType": {"type": "string", "enum": ["CUSTOM", "DEFAULT", "MODIFIED"]}, "ConflictException": {"type": "structure", "required": ["message", "resourceId", "resourceType"], "members": {"message": {"shape": "String"}, "resourceId": {"shape": "String", "documentation": "<p>Identifier of the affected resource.</p>"}, "resourceType": {"shape": "String", "documentation": "<p>Type of the affected resource.</p>"}}, "documentation": "<p>Request to create/modify content would result in a conflict.</p>", "error": {"httpStatusCode": 409, "senderFault": true}, "exception": true}, "CustomerAgreementIdAttribute": {"type": "string", "pattern": "customer-agreement-[a-zA-Z0-9]{16}"}, "CustomerAgreementList": {"type": "list", "member": {"shape": "CustomerAgreementSummary"}}, "CustomerAgreementState": {"type": "string", "enum": ["ACTIVE", "CUSTOMER_TERMINATED", "AWS_TERMINATED"]}, "CustomerAgreementSummary": {"type": "structure", "members": {"name": {"shape": "LongStringAttribute", "documentation": "<p>Name of the customer-agreement resource.</p>"}, "arn": {"shape": "LongStringAttribute", "documentation": "<p>ARN of the customer-agreement resource.</p>"}, "id": {"shape": "CustomerAgreementIdAttribute", "documentation": "<p>Identifier of the customer-agreement resource.</p>"}, "agreementArn": {"shape": "LongStringAttribute", "documentation": "<p>ARN of the agreement resource the customer-agreement resource represents.</p>"}, "awsAccountId": {"shape": "ShortStringAttribute", "documentation": "<p>AWS account Id that owns the resource.</p>"}, "organizationArn": {"shape": "LongStringAttribute", "documentation": "<p>ARN of the organization that owns the resource.</p>"}, "effectiveStart": {"shape": "TimestampAttribute", "documentation": "<p>Timestamp indicating when the agreement became effective.</p>"}, "effectiveEnd": {"shape": "TimestampAttribute", "documentation": "<p>Timestamp indicating when the agreement was terminated.</p>"}, "state": {"shape": "CustomerAgreementState", "documentation": "<p>State of the resource.</p>"}, "description": {"shape": "LongStringAttribute", "documentation": "<p>Description of the resource.</p>"}, "acceptanceTerms": {"shape": "AgreementTerms", "documentation": "<p>Terms required to accept the agreement resource.</p>"}, "terminateTerms": {"shape": "AgreementTerms", "documentation": "<p>Terms required to terminate the customer-agreement resource.</p>"}, "type": {"shape": "AgreementType", "documentation": "<p>Type of the customer-agreement resource.</p>"}}, "documentation": "<p>Summary for customer-agreement resource.</p>"}, "GetAccountSettingsRequest": {"type": "structure", "members": {}}, "GetAccountSettingsResponse": {"type": "structure", "members": {"accountSettings": {"shape": "AccountSettings"}}}, "GetReportMetadataRequest": {"type": "structure", "required": ["reportId"], "members": {"reportId": {"shape": "ReportId", "documentation": "<p>Unique resource ID for the report resource.</p>", "location": "querystring", "locationName": "reportId"}, "reportVersion": {"shape": "VersionAttribute", "documentation": "<p>Version for the report resource.</p>", "location": "querystring", "locationName": "reportVersion"}}}, "GetReportMetadataResponse": {"type": "structure", "members": {"reportDetails": {"shape": "ReportDetail", "documentation": "<p>Report resource detail.</p>"}}}, "GetReportRequest": {"type": "structure", "required": ["reportId", "termToken"], "members": {"reportId": {"shape": "ReportId", "documentation": "<p>Unique resource ID for the report resource.</p>", "location": "querystring", "locationName": "reportId"}, "reportVersion": {"shape": "VersionAttribute", "documentation": "<p>Version for the report resource.</p>", "location": "querystring", "locationName": "reportVersion"}, "termToken": {"shape": "ShortStringAttribute", "documentation": "<p>Unique download token provided by GetTermForReport API.</p>", "location": "querystring", "locationName": "termToken"}}}, "GetReportResponse": {"type": "structure", "members": {"documentPresignedUrl": {"shape": "GetReportResponseDocumentPresignedUrlString", "documentation": "<p>Presigned S3 url to access the report content.</p>"}}}, "GetReportResponseDocumentPresignedUrlString": {"type": "string", "max": 10240, "min": 1}, "GetTermForReportRequest": {"type": "structure", "required": ["reportId"], "members": {"reportId": {"shape": "ReportId", "documentation": "<p>Unique resource ID for the report resource.</p>", "location": "querystring", "locationName": "reportId"}, "reportVersion": {"shape": "VersionAttribute", "documentation": "<p>Version for the report resource.</p>", "location": "querystring", "locationName": "reportVersion"}}}, "GetTermForReportResponse": {"type": "structure", "members": {"documentPresignedUrl": {"shape": "GetTermForReportResponseDocumentPresignedUrlString", "documentation": "<p>Presigned S3 url to access the term content.</p>"}, "termToken": {"shape": "String", "documentation": "<p>Unique token representing this request event.</p>"}}}, "GetTermForReportResponseDocumentPresignedUrlString": {"type": "string", "max": 10240, "min": 1}, "Integer": {"type": "integer", "box": true}, "InternalServerException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}, "retryAfterSeconds": {"shape": "Integer", "documentation": "<p>Number of seconds in which the caller can retry the request.</p>", "location": "header", "locationName": "Retry-After"}}, "documentation": "<p>An unknown server exception has occurred.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true, "retryable": {"throttling": false}}, "ListCustomerAgreementsRequest": {"type": "structure", "members": {"maxResults": {"shape": "MaxResultsAttribute", "documentation": "<p>Maximum number of resources to return in the paginated response.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "NextTokenAttribute", "documentation": "<p>Pagination token to request the next page of resources.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListCustomerAgreementsResponse": {"type": "structure", "required": ["customerAgreements"], "members": {"customerAgreements": {"shape": "CustomerAgreementList", "documentation": "<p>List of customer-agreement resources.</p>"}, "nextToken": {"shape": "NextTokenAttribute", "documentation": "<p>Pagination token to request the next page of resources.</p>"}}}, "ListReportsRequest": {"type": "structure", "members": {"maxResults": {"shape": "MaxResultsAttribute", "documentation": "<p>Maximum number of resources to return in the paginated response.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "NextTokenAttribute", "documentation": "<p>Pagination token to request the next page of resources.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListReportsResponse": {"type": "structure", "members": {"reports": {"shape": "ReportsList", "documentation": "<p>List of report resources.</p>"}, "nextToken": {"shape": "NextTokenAttribute", "documentation": "<p>Pagination token to request the next page of resources.</p>"}}}, "LongStringAttribute": {"type": "string", "max": 1024, "min": 1, "pattern": "[^<>]*"}, "MaxResultsAttribute": {"type": "integer", "box": true, "max": 300, "min": 1}, "NextTokenAttribute": {"type": "string", "max": 2048, "min": 1}, "NotificationSubscriptionStatus": {"type": "string", "enum": ["SUBSCRIBED", "NOT_SUBSCRIBED"]}, "PublishedState": {"type": "string", "enum": ["PUBLISHED", "UNPUBLISHED"]}, "PutAccountSettingsRequest": {"type": "structure", "members": {"notificationSubscriptionStatus": {"shape": "NotificationSubscriptionStatus", "documentation": "<p>Desired notification subscription status.</p>"}}}, "PutAccountSettingsResponse": {"type": "structure", "members": {"accountSettings": {"shape": "AccountSettings"}}}, "ReportDetail": {"type": "structure", "members": {"id": {"shape": "ReportId", "documentation": "<p>Unique resource ID for the report resource.</p>"}, "name": {"shape": "ShortStringAttribute", "documentation": "<p>Name for the report resource.</p>"}, "description": {"shape": "LongStringAttribute", "documentation": "<p>Description for the report resource.</p>"}, "periodStart": {"shape": "TimestampAttribute", "documentation": "<p>Timestamp indicating the report resource effective start.</p>"}, "periodEnd": {"shape": "TimestampAttribute", "documentation": "<p>Timestamp indicating the report resource effective end.</p>"}, "createdAt": {"shape": "TimestampAttribute", "documentation": "<p>Timestamp indicating when the report resource was created.</p>"}, "lastModifiedAt": {"shape": "TimestampAttribute", "documentation": "<p>Timestamp indicating when the report resource was last modified.</p>"}, "deletedAt": {"shape": "TimestampAttribute", "documentation": "<p>Timestamp indicating when the report resource was deleted.</p>"}, "state": {"shape": "PublishedState", "documentation": "<p>Current state of the report resource</p>"}, "arn": {"shape": "LongStringAttribute", "documentation": "<p>ARN for the report resource.</p>"}, "series": {"shape": "ShortStringAttribute", "documentation": "<p>Series for the report resource.</p>"}, "category": {"shape": "ShortStringAttribute", "documentation": "<p>Category for the report resource.</p>"}, "companyName": {"shape": "ShortStringAttribute", "documentation": "<p>Associated company name for the report resource.</p>"}, "productName": {"shape": "ShortStringAttribute", "documentation": "<p>Associated product name for the report resource.</p>"}, "termArn": {"shape": "LongStringAttribute", "documentation": "<p>Unique resource ARN for term resource.</p>"}, "version": {"shape": "VersionAttribute", "documentation": "<p>Version for the report resource.</p>"}, "acceptanceType": {"shape": "AcceptanceType", "documentation": "<p>Acceptance type for report.</p>"}, "sequenceNumber": {"shape": "SequenceNumberAttribute", "documentation": "<p>Sequence number to enforce optimistic locking.</p>"}, "uploadState": {"shape": "UploadState", "documentation": "<p>The current state of the document upload.</p>"}, "statusMessage": {"shape": "StatusMessage", "documentation": "<p>The message associated with the current upload state.</p>"}}, "documentation": "<p>Full detail for report resource metadata.</p>"}, "ReportId": {"type": "string", "pattern": "report-[a-zA-Z0-9]{16}"}, "ReportSummary": {"type": "structure", "members": {"id": {"shape": "ReportId", "documentation": "<p>Unique resource ID for the report resource.</p>"}, "name": {"shape": "ShortStringAttribute", "documentation": "<p>Name for the report resource.</p>"}, "state": {"shape": "PublishedState", "documentation": "<p>Current state of the report resource.</p>"}, "arn": {"shape": "LongStringAttribute", "documentation": "<p>ARN for the report resource.</p>"}, "version": {"shape": "VersionAttribute", "documentation": "<p>Version for the report resource.</p>"}, "uploadState": {"shape": "UploadState", "documentation": "<p>The current state of the document upload.</p>"}, "description": {"shape": "LongStringAttribute", "documentation": "<p>Description for the report resource.</p>"}, "periodStart": {"shape": "TimestampAttribute", "documentation": "<p>Timestamp indicating the report resource effective start.</p>"}, "periodEnd": {"shape": "TimestampAttribute", "documentation": "<p>Timestamp indicating the report resource effective end.</p>"}, "series": {"shape": "ShortStringAttribute", "documentation": "<p>Series for the report resource.</p>"}, "category": {"shape": "ShortStringAttribute", "documentation": "<p>Category for the report resource.</p>"}, "companyName": {"shape": "ShortStringAttribute", "documentation": "<p>Associated company name for the report resource.</p>"}, "productName": {"shape": "ShortStringAttribute", "documentation": "<p>Associated product name for the report resource.</p>"}, "statusMessage": {"shape": "StatusMessage", "documentation": "<p>The message associated with the current upload state.</p>"}, "acceptanceType": {"shape": "AcceptanceType", "documentation": "<p>Acceptance type for report.</p>"}}, "documentation": "<p>Summary for report resource.</p>"}, "ReportsList": {"type": "list", "member": {"shape": "ReportSummary"}}, "ResourceNotFoundException": {"type": "structure", "required": ["message", "resourceId", "resourceType"], "members": {"message": {"shape": "String"}, "resourceId": {"shape": "String", "documentation": "<p>Identifier of the affected resource.</p>"}, "resourceType": {"shape": "String", "documentation": "<p>Type of the affected resource.</p>"}}, "documentation": "<p>Request references a resource which does not exist.</p>", "error": {"httpStatusCode": 404, "senderFault": true}, "exception": true}, "SequenceNumberAttribute": {"type": "long", "box": true, "min": 1}, "ServiceQuotaExceededException": {"type": "structure", "required": ["message", "resourceId", "resourceType", "serviceCode", "quotaCode"], "members": {"message": {"shape": "String"}, "resourceId": {"shape": "String", "documentation": "<p>Identifier of the affected resource.</p>"}, "resourceType": {"shape": "String", "documentation": "<p>Type of the affected resource.</p>"}, "serviceCode": {"shape": "String", "documentation": "<p>Code for the affected service.</p>"}, "quotaCode": {"shape": "String", "documentation": "<p>Code for the affected quota.</p>"}}, "documentation": "<p>Request would cause a service quota to be exceeded.</p>", "error": {"httpStatusCode": 402, "senderFault": true}, "exception": true}, "ShortStringAttribute": {"type": "string", "max": 256, "min": 1, "pattern": "[a-zA-Z0-9_\\-\\s]*"}, "StatusMessage": {"type": "string"}, "String": {"type": "string"}, "ThrottlingException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}, "serviceCode": {"shape": "String", "documentation": "<p>Code for the affected service.</p>"}, "quotaCode": {"shape": "String", "documentation": "<p>Code for the affected quota.</p>"}, "retryAfterSeconds": {"shape": "Integer", "documentation": "<p>Number of seconds in which the caller can retry the request.</p>", "location": "header", "locationName": "Retry-After"}}, "documentation": "<p>Request was denied due to request throttling.</p>", "error": {"httpStatusCode": 429, "senderFault": true}, "exception": true, "retryable": {"throttling": true}}, "TimestampAttribute": {"type": "timestamp", "timestampFormat": "iso8601"}, "UploadState": {"type": "string", "enum": ["PROCESSING", "COMPLETE", "FAILED", "FAULT"]}, "ValidationException": {"type": "structure", "required": ["message", "reason"], "members": {"message": {"shape": "String"}, "reason": {"shape": "ValidationExceptionReason", "documentation": "<p>Reason the request failed validation.</p>"}, "fieldList": {"shape": "ValidationExceptionFieldList", "documentation": "<p>The field that caused the error, if applicable.</p>"}}, "documentation": "<p>Request fails to satisfy the constraints specified by an AWS service.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "ValidationExceptionField": {"type": "structure", "required": ["name", "message"], "members": {"name": {"shape": "String", "documentation": "<p>Name of validation exception.</p>"}, "message": {"shape": "String", "documentation": "<p>Message describing why the field failed validation.</p>"}}, "documentation": "<p>Validation exception message and name.</p>"}, "ValidationExceptionFieldList": {"type": "list", "member": {"shape": "ValidationExceptionField"}}, "ValidationExceptionReason": {"type": "string", "enum": ["unknownOperation", "<PERSON><PERSON><PERSON><PERSON>", "fieldValidationFailed", "invalidToken", "other"]}, "VersionAttribute": {"type": "long", "box": true, "min": 1}}, "documentation": "<p>This reference provides descriptions of the low-level AWS Artifact Service API.</p>"}