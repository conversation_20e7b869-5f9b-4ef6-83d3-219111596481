{"version": "2.0", "metadata": {"apiVersion": "2019-05-01", "endpointPrefix": "workmailmessageflow", "jsonVersion": "1.1", "protocol": "rest-json", "serviceFullName": "Amazon WorkMail Message Flow", "serviceId": "WorkMailMessageFlow", "signatureVersion": "v4", "uid": "workmailmessageflow-2019-05-01"}, "operations": {"GetRawMessageContent": {"name": "GetRawMessageContent", "http": {"method": "GET", "requestUri": "/messages/{messageId}"}, "input": {"shape": "GetRawMessageContentRequest"}, "output": {"shape": "GetRawMessageContentResponse"}, "errors": [{"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves the raw content of an in-transit email message, in MIME format.</p>"}, "PutRawMessageContent": {"name": "PutRawMessageContent", "http": {"method": "POST", "requestUri": "/messages/{messageId}"}, "input": {"shape": "PutRawMessageContentRequest"}, "output": {"shape": "PutRawMessageContentResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidContentLocation"}, {"shape": "MessageRejected"}, {"shape": "MessageFrozen"}], "documentation": "<p>Updates the raw content of an in-transit email message, in MIME format.</p> <p>This example describes how to update in-transit email message. For more information and examples for using this API, see <a href=\"https://docs.aws.amazon.com/workmail/latest/adminguide/update-with-lambda.html\"> Updating message content with AWS Lambda</a>.</p> <note> <p>Updates to an in-transit message only appear when you call <code>PutRawMessageContent</code> from an AWS Lambda function configured with a synchronous <a href=\"https://docs.aws.amazon.com/workmail/latest/adminguide/lambda.html#synchronous-rules\"> Run Lambda</a> rule. If you call <code>PutRawMessageContent</code> on a delivered or sent message, the message remains unchanged, even though <a href=\"https://docs.aws.amazon.com/workmail/latest/APIReference/API_messageflow_GetRawMessageContent.html\">GetRawMessageContent</a> returns an updated message. </p> </note>"}}, "shapes": {"GetRawMessageContentRequest": {"type": "structure", "required": ["messageId"], "members": {"messageId": {"shape": "messageIdType", "documentation": "<p>The identifier of the email message to retrieve.</p>", "location": "uri", "locationName": "messageId"}}}, "GetRawMessageContentResponse": {"type": "structure", "required": ["messageContent"], "members": {"messageContent": {"shape": "messageContentBlob", "documentation": "<p>The raw content of the email message, in MIME format.</p>"}}, "payload": "messageContent"}, "InvalidContentLocation": {"type": "structure", "members": {"message": {"shape": "errorMessage"}}, "documentation": "<p>WorkMail could not access the updated email content. Possible reasons:</p> <ul> <li> <p>You made the request in a region other than your S3 bucket region.</p> </li> <li> <p>The <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/bucket-owner-condition.html\">S3 bucket owner</a> is not the same as the calling AWS account.</p> </li> <li> <p>You have an incomplete or missing S3 bucket policy. For more information about policies, see <a href=\"https://docs.aws.amazon.com/workmail/latest/adminguide/update-with-lambda.html\"> Updating message content with AWS Lambda </a> in the <i>WorkMail Administrator Guide</i>.</p> </li> </ul>", "exception": true}, "MessageFrozen": {"type": "structure", "members": {"message": {"shape": "errorMessage"}}, "documentation": "<p>The requested email is not eligible for update. This is usually the case for a redirected email.</p>", "exception": true}, "MessageRejected": {"type": "structure", "members": {"message": {"shape": "errorMessage"}}, "documentation": "<p>The requested email could not be updated due to an error in the MIME content. Check the error message for more information about what caused the error.</p>", "exception": true}, "PutRawMessageContentRequest": {"type": "structure", "required": ["messageId", "content"], "members": {"messageId": {"shape": "messageIdType", "documentation": "<p>The identifier of the email message being updated.</p>", "location": "uri", "locationName": "messageId"}, "content": {"shape": "RawMessageContent", "documentation": "<p>Describes the raw message content of the updated email message.</p>"}}}, "PutRawMessageContentResponse": {"type": "structure", "members": {}}, "RawMessageContent": {"type": "structure", "required": ["s3Reference"], "members": {"s3Reference": {"shape": "S3Reference", "documentation": "<p>The S3 reference of an email message.</p>"}}, "documentation": "<p>Provides the MIME content of the updated email message as an S3 object. All MIME content must meet the following criteria:</p> <ul> <li> <p>Each part of a multipart MIME message must be formatted properly.</p> </li> <li> <p>Attachments must be of a content type that Amazon SES supports. For more information, see <a href=\"https://docs.aws.amazon.com/ses/latest/DeveloperGuide/mime-types-appendix.html\">Unsupported Attachment Types</a>.</p> </li> <li> <p>If any of the MIME parts in a message contain content that is outside of the 7-bit ASCII character range, we recommend encoding that content.</p> </li> <li> <p>Per <a href=\"https://tools.ietf.org/html/rfc5321#section-*******.6\">RFC 5321</a>, the maximum length of each line of text, including the &lt;CRLF&gt;, must not exceed 1,000 characters.</p> </li> <li> <p>The message must contain all the required header fields. Check the returned error message for more information.</p> </li> <li> <p>The value of immutable headers must remain unchanged. Check the returned error message for more information.</p> </li> <li> <p>Certain unique headers can only appear once. Check the returned error message for more information.</p> </li> </ul>"}, "ResourceNotFoundException": {"type": "structure", "members": {"message": {"shape": "errorMessage"}}, "documentation": "<p>The requested email message is not found.</p>", "error": {"httpStatusCode": 404}, "exception": true}, "S3Reference": {"type": "structure", "required": ["bucket", "key"], "members": {"bucket": {"shape": "s3BucketIdType", "documentation": "<p>The S3 bucket name.</p>"}, "key": {"shape": "s3KeyIdType", "documentation": "<p>The S3 key object name.</p>"}, "objectVersion": {"shape": "s3VersionType", "documentation": "<p>If you enable versioning for the bucket, you can specify the object version.</p>"}}, "documentation": "<p>Amazon S3 object representing the updated message content, in MIME format.</p> <note> <p>The region for the S3 bucket containing the S3 object must match the region used for WorkMail operations. Also, for WorkMail to process an S3 object, it must have permission to access that object. For more information, see <a href=\"https://docs.aws.amazon.com/workmail/latest/adminguide/update-with-lambda.html\"> Updating message content with AWS Lambda</a>.</p> </note>"}, "errorMessage": {"type": "string"}, "messageContentBlob": {"type": "blob", "streaming": true}, "messageIdType": {"type": "string", "max": 120, "min": 1, "pattern": "[a-z0-9\\-]*"}, "s3BucketIdType": {"type": "string", "max": 63, "min": 3, "pattern": "^[a-z0-9][a-z0-9\\-]*"}, "s3KeyIdType": {"type": "string", "max": 1024, "min": 1, "pattern": "[a-zA-Z0-9\\-/]*"}, "s3VersionType": {"type": "string", "max": 1024, "min": 1, "pattern": ".+"}}, "documentation": "<p>The WorkMail Message Flow API provides access to email messages as they are being sent and received by a WorkMail organization.</p>"}