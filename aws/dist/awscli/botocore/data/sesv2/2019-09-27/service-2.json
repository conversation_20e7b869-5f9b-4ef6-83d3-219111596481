{"version": "2.0", "metadata": {"apiVersion": "2019-09-27", "endpointPrefix": "email", "jsonVersion": "1.1", "protocol": "rest-json", "protocols": ["rest-json"], "serviceAbbreviation": "Amazon SES V2", "serviceFullName": "Amazon Simple Email Service", "serviceId": "SESv2", "signatureVersion": "v4", "signingName": "ses", "uid": "sesv2-2019-09-27", "auth": ["aws.auth#sigv4"]}, "operations": {"BatchGetMetricData": {"name": "BatchGetMetricData", "http": {"method": "POST", "requestUri": "/v2/email/metrics/batch"}, "input": {"shape": "BatchGetMetricDataRequest"}, "output": {"shape": "BatchGetMetricDataResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServiceErrorException"}, {"shape": "TooManyRequestsException"}, {"shape": "NotFoundException"}], "documentation": "<p>Retrieves batches of metric data collected based on your sending activity.</p> <p>You can execute this operation no more than 16 times per second, and with at most 160 queries from the batches per second (cumulative).</p>"}, "CancelExportJob": {"name": "CancelExportJob", "http": {"method": "PUT", "requestUri": "/v2/email/export-jobs/{JobId}/cancel"}, "input": {"shape": "CancelExportJobRequest"}, "output": {"shape": "CancelExportJobResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "BadRequestException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Cancels an export job.</p>"}, "CreateConfigurationSet": {"name": "CreateConfigurationSet", "http": {"method": "POST", "requestUri": "/v2/email/configuration-sets"}, "input": {"shape": "CreateConfigurationSetRequest"}, "output": {"shape": "CreateConfigurationSetResponse"}, "errors": [{"shape": "AlreadyExistsException"}, {"shape": "NotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "LimitExceededException"}, {"shape": "BadRequestException"}, {"shape": "ConcurrentModificationException"}], "documentation": "<p>Create a configuration set. <i>Configuration sets</i> are groups of rules that you can apply to the emails that you send. You apply a configuration set to an email by specifying the name of the configuration set when you call the Amazon SES API v2. When you apply a configuration set to an email, all of the rules in that configuration set are applied to the email. </p>"}, "CreateConfigurationSetEventDestination": {"name": "CreateConfigurationSetEventDestination", "http": {"method": "POST", "requestUri": "/v2/email/configuration-sets/{ConfigurationSetName}/event-destinations"}, "input": {"shape": "CreateConfigurationSetEventDestinationRequest"}, "output": {"shape": "CreateConfigurationSetEventDestinationResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "AlreadyExistsException"}, {"shape": "LimitExceededException"}, {"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}], "documentation": "<p>Create an event destination. <i>Events</i> include message sends, deliveries, opens, clicks, bounces, and complaints. <i>Event destinations</i> are places that you can send information about these events to. For example, you can send event data to Amazon EventBridge and associate a rule to send the event to the specified target.</p> <p>A single configuration set can include more than one event destination.</p>"}, "CreateContact": {"name": "CreateContact", "http": {"method": "POST", "requestUri": "/v2/email/contact-lists/{ContactListName}/contacts"}, "input": {"shape": "CreateContactRequest"}, "output": {"shape": "CreateContactResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "TooManyRequestsException"}, {"shape": "NotFoundException"}, {"shape": "AlreadyExistsException"}], "documentation": "<p>Creates a contact, which is an end-user who is receiving the email, and adds them to a contact list.</p>"}, "CreateContactList": {"name": "CreateContactList", "http": {"method": "POST", "requestUri": "/v2/email/contact-lists"}, "input": {"shape": "CreateContactListRequest"}, "output": {"shape": "CreateContactListResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "TooManyRequestsException"}, {"shape": "AlreadyExistsException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Creates a contact list.</p>"}, "CreateCustomVerificationEmailTemplate": {"name": "CreateCustomVerificationEmailTemplate", "http": {"method": "POST", "requestUri": "/v2/email/custom-verification-email-templates"}, "input": {"shape": "CreateCustomVerificationEmailTemplateRequest"}, "output": {"shape": "CreateCustomVerificationEmailTemplateResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "AlreadyExistsException"}, {"shape": "NotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Creates a new custom verification email template.</p> <p>For more information about custom verification email templates, see <a href=\"https://docs.aws.amazon.com/ses/latest/dg/creating-identities.html#send-email-verify-address-custom\">Using custom verification email templates</a> in the <i>Amazon SES Developer Guide</i>.</p> <p>You can execute this operation no more than once per second.</p>"}, "CreateDedicatedIpPool": {"name": "CreateDedicatedIpPool", "http": {"method": "POST", "requestUri": "/v2/email/dedicated-ip-pools"}, "input": {"shape": "CreateDedicatedIpPoolRequest"}, "output": {"shape": "CreateDedicatedIpPoolResponse"}, "errors": [{"shape": "AlreadyExistsException"}, {"shape": "LimitExceededException"}, {"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}, {"shape": "ConcurrentModificationException"}], "documentation": "<p>Create a new pool of dedicated IP addresses. A pool can include one or more dedicated IP addresses that are associated with your Amazon Web Services account. You can associate a pool with a configuration set. When you send an email that uses that configuration set, the message is sent from one of the addresses in the associated pool.</p>"}, "CreateDeliverabilityTestReport": {"name": "CreateDeliverabilityTestReport", "http": {"method": "POST", "requestUri": "/v2/email/deliverability-dashboard/test"}, "input": {"shape": "CreateDeliverabilityTestReportRequest"}, "output": {"shape": "CreateDeliverabilityTestReportResponse"}, "errors": [{"shape": "AccountSuspendedException"}, {"shape": "SendingPausedException"}, {"shape": "MessageRejected"}, {"shape": "MailFromDomainNotVerifiedException"}, {"shape": "NotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "LimitExceededException"}, {"shape": "BadRequestException"}, {"shape": "ConcurrentModificationException"}], "documentation": "<p>Create a new predictive inbox placement test. Predictive inbox placement tests can help you predict how your messages will be handled by various email providers around the world. When you perform a predictive inbox placement test, you provide a sample message that contains the content that you plan to send to your customers. Amazon SES then sends that message to special email addresses spread across several major email providers. After about 24 hours, the test is complete, and you can use the <code>GetDeliverabilityTestReport</code> operation to view the results of the test.</p>"}, "CreateEmailIdentity": {"name": "CreateEmailIdentity", "http": {"method": "POST", "requestUri": "/v2/email/identities"}, "input": {"shape": "CreateEmailIdentityRequest"}, "output": {"shape": "CreateEmailIdentityResponse"}, "errors": [{"shape": "AlreadyExistsException"}, {"shape": "LimitExceededException"}, {"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}, {"shape": "ConcurrentModificationException"}, {"shape": "NotFoundException"}], "documentation": "<p>Starts the process of verifying an email identity. An <i>identity</i> is an email address or domain that you use when you send email. Before you can use an identity to send email, you first have to verify it. By verifying an identity, you demonstrate that you're the owner of the identity, and that you've given Amazon SES API v2 permission to send email from the identity.</p> <p>When you verify an email address, Amazon SES sends an email to the address. Your email address is verified as soon as you follow the link in the verification email. </p> <p>When you verify a domain without specifying the <code>DkimSigningAttributes</code> object, this operation provides a set of DKIM tokens. You can convert these tokens into CNAME records, which you then add to the DNS configuration for your domain. Your domain is verified when Amazon SES detects these records in the DNS configuration for your domain. This verification method is known as <a href=\"https://docs.aws.amazon.com/ses/latest/DeveloperGuide/easy-dkim.html\">Easy DKIM</a>.</p> <p>Alternatively, you can perform the verification process by providing your own public-private key pair. This verification method is known as Bring Your Own DKIM (BYODKIM). To use BYODKIM, your call to the <code>CreateEmailIdentity</code> operation has to include the <code>DkimSigningAttributes</code> object. When you specify this object, you provide a selector (a component of the DNS record name that identifies the public key to use for DKIM authentication) and a private key.</p> <p>When you verify a domain, this operation provides a set of DKIM tokens, which you can convert into CNAME tokens. You add these CNAME tokens to the DNS configuration for your domain. Your domain is verified when Amazon SES detects these records in the DNS configuration for your domain. For some DNS providers, it can take 72 hours or more to complete the domain verification process.</p> <p>Additionally, you can associate an existing configuration set with the email identity that you're verifying.</p>"}, "CreateEmailIdentityPolicy": {"name": "CreateEmailIdentityPolicy", "http": {"method": "POST", "requestUri": "/v2/email/identities/{EmailIdentity}/policies/{PolicyName}"}, "input": {"shape": "CreateEmailIdentityPolicyRequest"}, "output": {"shape": "CreateEmailIdentityPolicyResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "AlreadyExistsException"}, {"shape": "TooManyRequestsException"}, {"shape": "LimitExceededException"}, {"shape": "BadRequestException"}], "documentation": "<p>Creates the specified sending authorization policy for the given identity (an email address or a domain).</p> <note> <p>This API is for the identity owner only. If you have not verified the identity, this API will return an error.</p> </note> <p>Sending authorization is a feature that enables an identity owner to authorize other senders to use its identities. For information about using sending authorization, see the <a href=\"https://docs.aws.amazon.com/ses/latest/DeveloperGuide/sending-authorization.html\">Amazon SES Developer Guide</a>.</p> <p>You can execute this operation no more than once per second.</p>"}, "CreateEmailTemplate": {"name": "CreateEmailTemplate", "http": {"method": "POST", "requestUri": "/v2/email/templates"}, "input": {"shape": "CreateEmailTemplateRequest"}, "output": {"shape": "CreateEmailTemplateResponse"}, "errors": [{"shape": "AlreadyExistsException"}, {"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Creates an email template. Email templates enable you to send personalized email to one or more destinations in a single API operation. For more information, see the <a href=\"https://docs.aws.amazon.com/ses/latest/DeveloperGuide/send-personalized-email-api.html\">Amazon SES Developer Guide</a>.</p> <p>You can execute this operation no more than once per second.</p>"}, "CreateExportJob": {"name": "CreateExportJob", "http": {"method": "POST", "requestUri": "/v2/email/export-jobs"}, "input": {"shape": "CreateExportJobRequest"}, "output": {"shape": "CreateExportJobResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "TooManyRequestsException"}, {"shape": "NotFoundException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Creates an export job for a data source and destination.</p> <p>You can execute this operation no more than once per second.</p>"}, "CreateImportJob": {"name": "CreateImportJob", "http": {"method": "POST", "requestUri": "/v2/email/import-jobs"}, "input": {"shape": "CreateImportJobRequest"}, "output": {"shape": "CreateImportJobResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "LimitExceededException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Creates an import job for a data destination.</p>"}, "CreateMultiRegionEndpoint": {"name": "CreateMultiRegionEndpoint", "http": {"method": "POST", "requestUri": "/v2/email/multi-region-endpoints"}, "input": {"shape": "CreateMultiRegionEndpointRequest"}, "output": {"shape": "CreateMultiRegionEndpointResponse"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "TooManyRequestsException"}, {"shape": "AlreadyExistsException"}, {"shape": "BadRequestException"}], "documentation": "<p>Creates a multi-region endpoint (global-endpoint).</p> <p>The primary region is going to be the AWS-Region where the operation is executed. The secondary region has to be provided in request's parameters. From the data flow standpoint there is no difference between primary and secondary regions - sending traffic will be split equally between the two. The primary region is the region where the resource has been created and where it can be managed. </p>"}, "DeleteConfigurationSet": {"name": "DeleteConfigurationSet", "http": {"method": "DELETE", "requestUri": "/v2/email/configuration-sets/{ConfigurationSetName}"}, "input": {"shape": "DeleteConfigurationSetRequest"}, "output": {"shape": "DeleteConfigurationSetResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}, {"shape": "ConcurrentModificationException"}], "documentation": "<p>Delete an existing configuration set.</p> <p> <i>Configuration sets</i> are groups of rules that you can apply to the emails you send. You apply a configuration set to an email by including a reference to the configuration set in the headers of the email. When you apply a configuration set to an email, all of the rules in that configuration set are applied to the email.</p>"}, "DeleteConfigurationSetEventDestination": {"name": "DeleteConfigurationSetEventDestination", "http": {"method": "DELETE", "requestUri": "/v2/email/configuration-sets/{ConfigurationSetName}/event-destinations/{EventDestinationName}"}, "input": {"shape": "DeleteConfigurationSetEventDestinationRequest"}, "output": {"shape": "DeleteConfigurationSetEventDestinationResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}], "documentation": "<p>Delete an event destination.</p> <p> <i>Events</i> include message sends, deliveries, opens, clicks, bounces, and complaints. <i>Event destinations</i> are places that you can send information about these events to. For example, you can send event data to Amazon EventBridge and associate a rule to send the event to the specified target.</p>"}, "DeleteContact": {"name": "DeleteContact", "http": {"method": "DELETE", "requestUri": "/v2/email/contact-lists/{ContactListName}/contacts/{EmailAddress}"}, "input": {"shape": "DeleteContactRequest"}, "output": {"shape": "DeleteContactResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "TooManyRequestsException"}, {"shape": "NotFoundException"}], "documentation": "<p>Removes a contact from a contact list.</p>"}, "DeleteContactList": {"name": "DeleteContactList", "http": {"method": "DELETE", "requestUri": "/v2/email/contact-lists/{ContactListName}"}, "input": {"shape": "DeleteContactListRequest"}, "output": {"shape": "DeleteContactListResponse"}, "errors": [{"shape": "TooManyRequestsException"}, {"shape": "NotFoundException"}, {"shape": "BadRequestException"}, {"shape": "ConcurrentModificationException"}], "documentation": "<p>Deletes a contact list and all of the contacts on that list.</p>"}, "DeleteCustomVerificationEmailTemplate": {"name": "DeleteCustomVerificationEmailTemplate", "http": {"method": "DELETE", "requestUri": "/v2/email/custom-verification-email-templates/{TemplateName}"}, "input": {"shape": "DeleteCustomVerificationEmailTemplateRequest"}, "output": {"shape": "DeleteCustomVerificationEmailTemplateResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}], "documentation": "<p>Deletes an existing custom verification email template.</p> <p>For more information about custom verification email templates, see <a href=\"https://docs.aws.amazon.com/ses/latest/dg/creating-identities.html#send-email-verify-address-custom\">Using custom verification email templates</a> in the <i>Amazon SES Developer Guide</i>.</p> <p>You can execute this operation no more than once per second.</p>"}, "DeleteDedicatedIpPool": {"name": "DeleteDedicatedIpPool", "http": {"method": "DELETE", "requestUri": "/v2/email/dedicated-ip-pools/{PoolName}"}, "input": {"shape": "DeleteDedicatedIpPoolRequest"}, "output": {"shape": "DeleteDedicatedIpPoolResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}, {"shape": "ConcurrentModificationException"}], "documentation": "<p>Delete a dedicated IP pool.</p>"}, "DeleteEmailIdentity": {"name": "DeleteEmailIdentity", "http": {"method": "DELETE", "requestUri": "/v2/email/identities/{EmailIdentity}"}, "input": {"shape": "DeleteEmailIdentityRequest"}, "output": {"shape": "DeleteEmailIdentityResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}, {"shape": "ConcurrentModificationException"}], "documentation": "<p>Deletes an email identity. An identity can be either an email address or a domain name.</p>"}, "DeleteEmailIdentityPolicy": {"name": "DeleteEmailIdentityPolicy", "http": {"method": "DELETE", "requestUri": "/v2/email/identities/{EmailIdentity}/policies/{PolicyName}"}, "input": {"shape": "DeleteEmailIdentityPolicyRequest"}, "output": {"shape": "DeleteEmailIdentityPolicyResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}], "documentation": "<p>Deletes the specified sending authorization policy for the given identity (an email address or a domain). This API returns successfully even if a policy with the specified name does not exist.</p> <note> <p>This API is for the identity owner only. If you have not verified the identity, this API will return an error.</p> </note> <p>Sending authorization is a feature that enables an identity owner to authorize other senders to use its identities. For information about using sending authorization, see the <a href=\"https://docs.aws.amazon.com/ses/latest/DeveloperGuide/sending-authorization.html\">Amazon SES Developer Guide</a>.</p> <p>You can execute this operation no more than once per second.</p>"}, "DeleteEmailTemplate": {"name": "DeleteEmailTemplate", "http": {"method": "DELETE", "requestUri": "/v2/email/templates/{TemplateName}"}, "input": {"shape": "DeleteEmailTemplateRequest"}, "output": {"shape": "DeleteEmailTemplateResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}], "documentation": "<p>Deletes an email template.</p> <p>You can execute this operation no more than once per second.</p>"}, "DeleteMultiRegionEndpoint": {"name": "DeleteMultiRegionEndpoint", "http": {"method": "DELETE", "requestUri": "/v2/email/multi-region-endpoints/{EndpointName}"}, "input": {"shape": "DeleteMultiRegionEndpointRequest"}, "output": {"shape": "DeleteMultiRegionEndpointResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}, {"shape": "ConcurrentModificationException"}], "documentation": "<p>Deletes a multi-region endpoint (global-endpoint).</p> <p>Only multi-region endpoints (global-endpoints) whose primary region is the AWS-Region where operation is executed can be deleted.</p>"}, "DeleteSuppressedDestination": {"name": "DeleteSuppressedDestination", "http": {"method": "DELETE", "requestUri": "/v2/email/suppression/addresses/{EmailAddress}"}, "input": {"shape": "DeleteSuppressedDestinationRequest"}, "output": {"shape": "DeleteSuppressedDestinationResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "BadRequestException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Removes an email address from the suppression list for your account.</p>"}, "GetAccount": {"name": "GetAccount", "http": {"method": "GET", "requestUri": "/v2/email/account"}, "input": {"shape": "GetAccountRequest"}, "output": {"shape": "GetAccountResponse"}, "errors": [{"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}], "documentation": "<p>Obtain information about the email-sending status and capabilities of your Amazon SES account in the current Amazon Web Services Region.</p>"}, "GetBlacklistReports": {"name": "GetBlacklistReports", "http": {"method": "GET", "requestUri": "/v2/email/deliverability-dashboard/blacklist-report"}, "input": {"shape": "GetBlacklistReportsRequest"}, "output": {"shape": "GetBlacklistReportsResponse"}, "errors": [{"shape": "TooManyRequestsException"}, {"shape": "NotFoundException"}, {"shape": "BadRequestException"}], "documentation": "<p>Retrieve a list of the blacklists that your dedicated IP addresses appear on.</p>"}, "GetConfigurationSet": {"name": "GetConfigurationSet", "http": {"method": "GET", "requestUri": "/v2/email/configuration-sets/{ConfigurationSetName}"}, "input": {"shape": "GetConfigurationSetRequest"}, "output": {"shape": "GetConfigurationSetResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}], "documentation": "<p>Get information about an existing configuration set, including the dedicated IP pool that it's associated with, whether or not it's enabled for sending email, and more.</p> <p> <i>Configuration sets</i> are groups of rules that you can apply to the emails you send. You apply a configuration set to an email by including a reference to the configuration set in the headers of the email. When you apply a configuration set to an email, all of the rules in that configuration set are applied to the email.</p>"}, "GetConfigurationSetEventDestinations": {"name": "GetConfigurationSetEventDestinations", "http": {"method": "GET", "requestUri": "/v2/email/configuration-sets/{ConfigurationSetName}/event-destinations"}, "input": {"shape": "GetConfigurationSetEventDestinationsRequest"}, "output": {"shape": "GetConfigurationSetEventDestinationsResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}], "documentation": "<p>Retrieve a list of event destinations that are associated with a configuration set.</p> <p> <i>Events</i> include message sends, deliveries, opens, clicks, bounces, and complaints. <i>Event destinations</i> are places that you can send information about these events to. For example, you can send event data to Amazon EventBridge and associate a rule to send the event to the specified target.</p>"}, "GetContact": {"name": "GetContact", "http": {"method": "GET", "requestUri": "/v2/email/contact-lists/{ContactListName}/contacts/{EmailAddress}"}, "input": {"shape": "GetContactRequest"}, "output": {"shape": "GetContactResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "TooManyRequestsException"}, {"shape": "NotFoundException"}], "documentation": "<p>Returns a contact from a contact list.</p>"}, "GetContactList": {"name": "GetContactList", "http": {"method": "GET", "requestUri": "/v2/email/contact-lists/{ContactListName}"}, "input": {"shape": "GetContactListRequest"}, "output": {"shape": "GetContactListResponse"}, "errors": [{"shape": "TooManyRequestsException"}, {"shape": "NotFoundException"}, {"shape": "BadRequestException"}], "documentation": "<p>Returns contact list metadata. It does not return any information about the contacts present in the list.</p>"}, "GetCustomVerificationEmailTemplate": {"name": "GetCustomVerificationEmailTemplate", "http": {"method": "GET", "requestUri": "/v2/email/custom-verification-email-templates/{TemplateName}"}, "input": {"shape": "GetCustomVerificationEmailTemplateRequest"}, "output": {"shape": "GetCustomVerificationEmailTemplateResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}], "documentation": "<p>Returns the custom email verification template for the template name you specify.</p> <p>For more information about custom verification email templates, see <a href=\"https://docs.aws.amazon.com/ses/latest/dg/creating-identities.html#send-email-verify-address-custom\">Using custom verification email templates</a> in the <i>Amazon SES Developer Guide</i>.</p> <p>You can execute this operation no more than once per second.</p>"}, "GetDedicatedIp": {"name": "GetDedicatedIp", "http": {"method": "GET", "requestUri": "/v2/email/dedicated-ips/{IP}"}, "input": {"shape": "GetDedicatedIpRequest"}, "output": {"shape": "GetDedicatedIpResponse"}, "errors": [{"shape": "TooManyRequestsException"}, {"shape": "NotFoundException"}, {"shape": "BadRequestException"}], "documentation": "<p>Get information about a dedicated IP address, including the name of the dedicated IP pool that it's associated with, as well information about the automatic warm-up process for the address.</p>"}, "GetDedicatedIpPool": {"name": "GetDedicatedIpPool", "http": {"method": "GET", "requestUri": "/v2/email/dedicated-ip-pools/{PoolName}"}, "input": {"shape": "GetDedicatedIpPoolRequest"}, "output": {"shape": "GetDedicatedIpPoolResponse"}, "errors": [{"shape": "TooManyRequestsException"}, {"shape": "NotFoundException"}, {"shape": "BadRequestException"}], "documentation": "<p>Retrieve information about the dedicated pool.</p>"}, "GetDedicatedIps": {"name": "GetDedicatedIps", "http": {"method": "GET", "requestUri": "/v2/email/dedicated-ips"}, "input": {"shape": "GetDedicatedIpsRequest"}, "output": {"shape": "GetDedicatedIpsResponse"}, "errors": [{"shape": "TooManyRequestsException"}, {"shape": "NotFoundException"}, {"shape": "BadRequestException"}], "documentation": "<p>List the dedicated IP addresses that are associated with your Amazon Web Services account.</p>"}, "GetDeliverabilityDashboardOptions": {"name": "GetDeliverabilityDashboardOptions", "http": {"method": "GET", "requestUri": "/v2/email/deliverability-dashboard"}, "input": {"shape": "GetDeliverabilityDashboardOptionsRequest"}, "output": {"shape": "GetDeliverabilityDashboardOptionsResponse"}, "errors": [{"shape": "TooManyRequestsException"}, {"shape": "LimitExceededException"}, {"shape": "BadRequestException"}], "documentation": "<p>Retrieve information about the status of the Deliverability dashboard for your account. When the Deliverability dashboard is enabled, you gain access to reputation, deliverability, and other metrics for the domains that you use to send email. You also gain the ability to perform predictive inbox placement tests.</p> <p>When you use the Deliverability dashboard, you pay a monthly subscription charge, in addition to any other fees that you accrue by using Amazon SES and other Amazon Web Services services. For more information about the features and cost of a Deliverability dashboard subscription, see <a href=\"http://aws.amazon.com/ses/pricing/\">Amazon SES Pricing</a>.</p>"}, "GetDeliverabilityTestReport": {"name": "GetDeliverabilityTestReport", "http": {"method": "GET", "requestUri": "/v2/email/deliverability-dashboard/test-reports/{ReportId}"}, "input": {"shape": "GetDeliverabilityTestReportRequest"}, "output": {"shape": "GetDeliverabilityTestReportResponse"}, "errors": [{"shape": "TooManyRequestsException"}, {"shape": "NotFoundException"}, {"shape": "BadRequestException"}], "documentation": "<p>Retrieve the results of a predictive inbox placement test.</p>"}, "GetDomainDeliverabilityCampaign": {"name": "GetDomainDeliverabilityCampaign", "http": {"method": "GET", "requestUri": "/v2/email/deliverability-dashboard/campaigns/{CampaignId}"}, "input": {"shape": "GetDomainDeliverabilityCampaignRequest"}, "output": {"shape": "GetDomainDeliverabilityCampaignResponse"}, "errors": [{"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}, {"shape": "NotFoundException"}], "documentation": "<p>Retrieve all the deliverability data for a specific campaign. This data is available for a campaign only if the campaign sent email by using a domain that the Deliverability dashboard is enabled for.</p>"}, "GetDomainStatisticsReport": {"name": "GetDomainStatisticsReport", "http": {"method": "GET", "requestUri": "/v2/email/deliverability-dashboard/statistics-report/{Domain}"}, "input": {"shape": "GetDomainStatisticsReportRequest"}, "output": {"shape": "GetDomainStatisticsReportResponse"}, "errors": [{"shape": "TooManyRequestsException"}, {"shape": "NotFoundException"}, {"shape": "BadRequestException"}], "documentation": "<p>Retrieve inbox placement and engagement rates for the domains that you use to send email.</p>"}, "GetEmailIdentity": {"name": "GetEmailIdentity", "http": {"method": "GET", "requestUri": "/v2/email/identities/{EmailIdentity}"}, "input": {"shape": "GetEmailIdentityRequest"}, "output": {"shape": "GetEmailIdentityResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}], "documentation": "<p>Provides information about a specific identity, including the identity's verification status, sending authorization policies, its DKIM authentication status, and its custom Mail-From settings.</p>"}, "GetEmailIdentityPolicies": {"name": "GetEmailIdentityPolicies", "http": {"method": "GET", "requestUri": "/v2/email/identities/{EmailIdentity}/policies"}, "input": {"shape": "GetEmailIdentityPoliciesRequest"}, "output": {"shape": "GetEmailIdentityPoliciesResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}], "documentation": "<p>Returns the requested sending authorization policies for the given identity (an email address or a domain). The policies are returned as a map of policy names to policy contents. You can retrieve a maximum of 20 policies at a time.</p> <note> <p>This API is for the identity owner only. If you have not verified the identity, this API will return an error.</p> </note> <p>Sending authorization is a feature that enables an identity owner to authorize other senders to use its identities. For information about using sending authorization, see the <a href=\"https://docs.aws.amazon.com/ses/latest/DeveloperGuide/sending-authorization.html\">Amazon SES Developer Guide</a>.</p> <p>You can execute this operation no more than once per second.</p>"}, "GetEmailTemplate": {"name": "GetEmailTemplate", "http": {"method": "GET", "requestUri": "/v2/email/templates/{TemplateName}"}, "input": {"shape": "GetEmailTemplateRequest"}, "output": {"shape": "GetEmailTemplateResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}], "documentation": "<p>Displays the template object (which includes the subject line, HTML part and text part) for the template you specify.</p> <p>You can execute this operation no more than once per second.</p>"}, "GetExportJob": {"name": "GetExportJob", "http": {"method": "GET", "requestUri": "/v2/email/export-jobs/{JobId}"}, "input": {"shape": "GetExportJobRequest"}, "output": {"shape": "GetExportJobResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "NotFoundException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Provides information about an export job.</p>"}, "GetImportJob": {"name": "GetImportJob", "http": {"method": "GET", "requestUri": "/v2/email/import-jobs/{JobId}"}, "input": {"shape": "GetImportJobRequest"}, "output": {"shape": "GetImportJobResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "NotFoundException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Provides information about an import job.</p>"}, "GetMessageInsights": {"name": "GetMessageInsights", "http": {"method": "GET", "requestUri": "/v2/email/insights/{MessageId}/"}, "input": {"shape": "GetMessageInsightsRequest"}, "output": {"shape": "GetMessageInsightsResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}], "documentation": "<p>Provides information about a specific message, including the from address, the subject, the recipient address, email tags, as well as events associated with the message.</p> <p>You can execute this operation no more than once per second.</p>"}, "GetMultiRegionEndpoint": {"name": "GetMultiRegionEndpoint", "http": {"method": "GET", "requestUri": "/v2/email/multi-region-endpoints/{EndpointName}"}, "input": {"shape": "GetMultiRegionEndpointRequest"}, "output": {"shape": "GetMultiRegionEndpointResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}], "documentation": "<p>Displays the multi-region endpoint (global-endpoint) configuration.</p> <p>Only multi-region endpoints (global-endpoints) whose primary region is the AWS-Region where operation is executed can be displayed.</p>"}, "GetSuppressedDestination": {"name": "GetSuppressedDestination", "http": {"method": "GET", "requestUri": "/v2/email/suppression/addresses/{EmailAddress}"}, "input": {"shape": "GetSuppressedDestinationRequest"}, "output": {"shape": "GetSuppressedDestinationResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "TooManyRequestsException"}, {"shape": "NotFoundException"}], "documentation": "<p>Retrieves information about a specific email address that's on the suppression list for your account.</p>"}, "ListConfigurationSets": {"name": "ListConfigurationSets", "http": {"method": "GET", "requestUri": "/v2/email/configuration-sets"}, "input": {"shape": "ListConfigurationSetsRequest"}, "output": {"shape": "ListConfigurationSetsResponse"}, "errors": [{"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}], "documentation": "<p>List all of the configuration sets associated with your account in the current region.</p> <p> <i>Configuration sets</i> are groups of rules that you can apply to the emails you send. You apply a configuration set to an email by including a reference to the configuration set in the headers of the email. When you apply a configuration set to an email, all of the rules in that configuration set are applied to the email.</p>"}, "ListContactLists": {"name": "ListContactLists", "http": {"method": "GET", "requestUri": "/v2/email/contact-lists"}, "input": {"shape": "ListContactListsRequest"}, "output": {"shape": "ListContactListsResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Lists all of the contact lists available.</p> <p>If your output includes a \"NextToken\" field with a string value, this indicates there may be additional contacts on the filtered list - regardless of the number of contacts returned.</p>"}, "ListContacts": {"name": "ListContacts", "http": {"method": "POST", "requestUri": "/v2/email/contact-lists/{ContactListName}/contacts/list"}, "input": {"shape": "ListContactsRequest"}, "output": {"shape": "ListContactsResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "TooManyRequestsException"}, {"shape": "NotFoundException"}], "documentation": "<p>Lists the contacts present in a specific contact list.</p>"}, "ListCustomVerificationEmailTemplates": {"name": "ListCustomVerificationEmailTemplates", "http": {"method": "GET", "requestUri": "/v2/email/custom-verification-email-templates"}, "input": {"shape": "ListCustomVerificationEmailTemplatesRequest"}, "output": {"shape": "ListCustomVerificationEmailTemplatesResponse"}, "errors": [{"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}], "documentation": "<p>Lists the existing custom verification email templates for your account in the current Amazon Web Services Region.</p> <p>For more information about custom verification email templates, see <a href=\"https://docs.aws.amazon.com/ses/latest/dg/creating-identities.html#send-email-verify-address-custom\">Using custom verification email templates</a> in the <i>Amazon SES Developer Guide</i>.</p> <p>You can execute this operation no more than once per second.</p>"}, "ListDedicatedIpPools": {"name": "ListDedicatedIpPools", "http": {"method": "GET", "requestUri": "/v2/email/dedicated-ip-pools"}, "input": {"shape": "ListDedicatedIpPoolsRequest"}, "output": {"shape": "ListDedicatedIpPoolsResponse"}, "errors": [{"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}], "documentation": "<p>List all of the dedicated IP pools that exist in your Amazon Web Services account in the current Region.</p>"}, "ListDeliverabilityTestReports": {"name": "ListDeliverabilityTestReports", "http": {"method": "GET", "requestUri": "/v2/email/deliverability-dashboard/test-reports"}, "input": {"shape": "ListDeliverabilityTestReportsRequest"}, "output": {"shape": "ListDeliverabilityTestReportsResponse"}, "errors": [{"shape": "TooManyRequestsException"}, {"shape": "NotFoundException"}, {"shape": "BadRequestException"}], "documentation": "<p>Show a list of the predictive inbox placement tests that you've performed, regardless of their statuses. For predictive inbox placement tests that are complete, you can use the <code>GetDeliverabilityTestReport</code> operation to view the results.</p>"}, "ListDomainDeliverabilityCampaigns": {"name": "ListDomainDeliverabilityCampaigns", "http": {"method": "GET", "requestUri": "/v2/email/deliverability-dashboard/domains/{SubscribedDomain}/campaigns"}, "input": {"shape": "ListDomainDeliverabilityCampaignsRequest"}, "output": {"shape": "ListDomainDeliverabilityCampaignsResponse"}, "errors": [{"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}, {"shape": "NotFoundException"}], "documentation": "<p>Retrieve deliverability data for all the campaigns that used a specific domain to send email during a specified time range. This data is available for a domain only if you enabled the Deliverability dashboard for the domain.</p>"}, "ListEmailIdentities": {"name": "ListEmailIdentities", "http": {"method": "GET", "requestUri": "/v2/email/identities"}, "input": {"shape": "ListEmailIdentitiesRequest"}, "output": {"shape": "ListEmailIdentitiesResponse"}, "errors": [{"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}], "documentation": "<p>Returns a list of all of the email identities that are associated with your Amazon Web Services account. An identity can be either an email address or a domain. This operation returns identities that are verified as well as those that aren't. This operation returns identities that are associated with Amazon SES and Amazon Pinpoint.</p>"}, "ListEmailTemplates": {"name": "ListEmailTemplates", "http": {"method": "GET", "requestUri": "/v2/email/templates"}, "input": {"shape": "ListEmailTemplatesRequest"}, "output": {"shape": "ListEmailTemplatesResponse"}, "errors": [{"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}], "documentation": "<p>Lists the email templates present in your Amazon SES account in the current Amazon Web Services Region.</p> <p>You can execute this operation no more than once per second.</p>"}, "ListExportJobs": {"name": "ListExportJobs", "http": {"method": "POST", "requestUri": "/v2/email/list-export-jobs"}, "input": {"shape": "ListExportJobsRequest"}, "output": {"shape": "ListExportJobsResponse"}, "errors": [{"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}], "documentation": "<p>Lists all of the export jobs.</p>"}, "ListImportJobs": {"name": "ListImportJobs", "http": {"method": "POST", "requestUri": "/v2/email/import-jobs/list"}, "input": {"shape": "ListImportJobsRequest"}, "output": {"shape": "ListImportJobsResponse"}, "errors": [{"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}], "documentation": "<p>Lists all of the import jobs.</p>"}, "ListMultiRegionEndpoints": {"name": "ListMultiRegionEndpoints", "http": {"method": "GET", "requestUri": "/v2/email/multi-region-endpoints"}, "input": {"shape": "ListMultiRegionEndpointsRequest"}, "output": {"shape": "ListMultiRegionEndpointsResponse"}, "errors": [{"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}], "documentation": "<p>List the multi-region endpoints (global-endpoints).</p> <p>Only multi-region endpoints (global-endpoints) whose primary region is the AWS-Region where operation is executed will be listed.</p>"}, "ListRecommendations": {"name": "ListRecommendations", "http": {"method": "POST", "requestUri": "/v2/email/vdm/recommendations"}, "input": {"shape": "ListRecommendationsRequest"}, "output": {"shape": "ListRecommendationsResponse"}, "errors": [{"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}, {"shape": "NotFoundException"}], "documentation": "<p>Lists the recommendations present in your Amazon SES account in the current Amazon Web Services Region.</p> <p>You can execute this operation no more than once per second.</p>"}, "ListSuppressedDestinations": {"name": "ListSuppressedDestinations", "http": {"method": "GET", "requestUri": "/v2/email/suppression/addresses"}, "input": {"shape": "ListSuppressedDestinationsRequest"}, "output": {"shape": "ListSuppressedDestinationsResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "TooManyRequestsException"}, {"shape": "InvalidNextTokenException"}], "documentation": "<p>Retrieves a list of email addresses that are on the suppression list for your account.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/v2/email/tags"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "NotFoundException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Retrieve a list of the tags (keys and values) that are associated with a specified resource. A <i>tag</i> is a label that you optionally define and associate with a resource. Each tag consists of a required <i>tag key</i> and an optional associated <i>tag value</i>. A tag key is a general label that acts as a category for more specific tag values. A tag value acts as a descriptor within a tag key.</p>"}, "PutAccountDedicatedIpWarmupAttributes": {"name": "PutAccountDedicatedIpWarmupAttributes", "http": {"method": "PUT", "requestUri": "/v2/email/account/dedicated-ips/warmup"}, "input": {"shape": "PutAccountDedicatedIpWarmupAttributesRequest"}, "output": {"shape": "PutAccountDedicatedIpWarmupAttributesResponse"}, "errors": [{"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}], "documentation": "<p>Enable or disable the automatic warm-up feature for dedicated IP addresses.</p>"}, "PutAccountDetails": {"name": "PutAccountDetails", "http": {"method": "POST", "requestUri": "/v2/email/account/details"}, "input": {"shape": "PutAccountDetailsRequest"}, "output": {"shape": "PutAccountDetailsResponse"}, "errors": [{"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}, {"shape": "ConflictException"}], "documentation": "<p>Update your Amazon SES account details.</p>"}, "PutAccountSendingAttributes": {"name": "PutAccountSendingAttributes", "http": {"method": "PUT", "requestUri": "/v2/email/account/sending"}, "input": {"shape": "PutAccountSendingAttributesRequest"}, "output": {"shape": "PutAccountSendingAttributesResponse"}, "errors": [{"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}], "documentation": "<p>Enable or disable the ability of your account to send email.</p>"}, "PutAccountSuppressionAttributes": {"name": "PutAccountSuppressionAttributes", "http": {"method": "PUT", "requestUri": "/v2/email/account/suppression"}, "input": {"shape": "PutAccountSuppressionAttributesRequest"}, "output": {"shape": "PutAccountSuppressionAttributesResponse"}, "errors": [{"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}], "documentation": "<p>Change the settings for the account-level suppression list.</p>"}, "PutAccountVdmAttributes": {"name": "PutAccountVdmAttributes", "http": {"method": "PUT", "requestUri": "/v2/email/account/vdm"}, "input": {"shape": "PutAccountVdmAttributesRequest"}, "output": {"shape": "PutAccountVdmAttributesResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Update your Amazon SES account VDM attributes.</p> <p>You can execute this operation no more than once per second.</p>"}, "PutConfigurationSetArchivingOptions": {"name": "PutConfigurationSetArchivingOptions", "http": {"method": "PUT", "requestUri": "/v2/email/configuration-sets/{ConfigurationSetName}/archiving-options"}, "input": {"shape": "PutConfigurationSetArchivingOptionsRequest"}, "output": {"shape": "PutConfigurationSetArchivingOptionsResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}], "documentation": "<p>Associate the configuration set with a MailManager archive. When you send email using the <code>SendEmail</code> or <code>SendBulkEmail</code> operations the message as it will be given to the receiving SMTP server will be archived, along with the recipient information.</p>", "idempotent": true}, "PutConfigurationSetDeliveryOptions": {"name": "PutConfigurationSetDeliveryOptions", "http": {"method": "PUT", "requestUri": "/v2/email/configuration-sets/{ConfigurationSetName}/delivery-options"}, "input": {"shape": "PutConfigurationSetDeliveryOptionsRequest"}, "output": {"shape": "PutConfigurationSetDeliveryOptionsResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}], "documentation": "<p>Associate a configuration set with a dedicated IP pool. You can use dedicated IP pools to create groups of dedicated IP addresses for sending specific types of email.</p>"}, "PutConfigurationSetReputationOptions": {"name": "PutConfigurationSetReputationOptions", "http": {"method": "PUT", "requestUri": "/v2/email/configuration-sets/{ConfigurationSetName}/reputation-options"}, "input": {"shape": "PutConfigurationSetReputationOptionsRequest"}, "output": {"shape": "PutConfigurationSetReputationOptionsResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}], "documentation": "<p>Enable or disable collection of reputation metrics for emails that you send using a particular configuration set in a specific Amazon Web Services Region.</p>"}, "PutConfigurationSetSendingOptions": {"name": "PutConfigurationSetSendingOptions", "http": {"method": "PUT", "requestUri": "/v2/email/configuration-sets/{ConfigurationSetName}/sending"}, "input": {"shape": "PutConfigurationSetSendingOptionsRequest"}, "output": {"shape": "PutConfigurationSetSendingOptionsResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}], "documentation": "<p>Enable or disable email sending for messages that use a particular configuration set in a specific Amazon Web Services Region.</p>"}, "PutConfigurationSetSuppressionOptions": {"name": "PutConfigurationSetSuppressionOptions", "http": {"method": "PUT", "requestUri": "/v2/email/configuration-sets/{ConfigurationSetName}/suppression-options"}, "input": {"shape": "PutConfigurationSetSuppressionOptionsRequest"}, "output": {"shape": "PutConfigurationSetSuppressionOptionsResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}], "documentation": "<p>Specify the account suppression list preferences for a configuration set.</p>"}, "PutConfigurationSetTrackingOptions": {"name": "PutConfigurationSetTrackingOptions", "http": {"method": "PUT", "requestUri": "/v2/email/configuration-sets/{ConfigurationSetName}/tracking-options"}, "input": {"shape": "PutConfigurationSetTrackingOptionsRequest"}, "output": {"shape": "PutConfigurationSetTrackingOptionsResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}], "documentation": "<p>Specify a custom domain to use for open and click tracking elements in email that you send.</p>"}, "PutConfigurationSetVdmOptions": {"name": "PutConfigurationSetVdmOptions", "http": {"method": "PUT", "requestUri": "/v2/email/configuration-sets/{ConfigurationSetName}/vdm-options"}, "input": {"shape": "PutConfigurationSetVdmOptionsRequest"}, "output": {"shape": "PutConfigurationSetVdmOptionsResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}], "documentation": "<p>Specify VDM preferences for email that you send using the configuration set.</p> <p>You can execute this operation no more than once per second.</p>"}, "PutDedicatedIpInPool": {"name": "PutDedicatedIpInPool", "http": {"method": "PUT", "requestUri": "/v2/email/dedicated-ips/{IP}/pool"}, "input": {"shape": "PutDedicatedIpInPoolRequest"}, "output": {"shape": "PutDedicatedIpInPoolResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}], "documentation": "<p>Move a dedicated IP address to an existing dedicated IP pool.</p> <note> <p>The dedicated IP address that you specify must already exist, and must be associated with your Amazon Web Services account. </p> <p>The dedicated IP pool you specify must already exist. You can create a new pool by using the <code>CreateDedicatedIpPool</code> operation.</p> </note>"}, "PutDedicatedIpPoolScalingAttributes": {"name": "PutDedicatedIpPoolScalingAttributes", "http": {"method": "PUT", "requestUri": "/v2/email/dedicated-ip-pools/{PoolName}/scaling"}, "input": {"shape": "PutDedicatedIpPoolScalingAttributesRequest"}, "output": {"shape": "PutDedicatedIpPoolScalingAttributesResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "ConcurrentModificationException"}, {"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}], "documentation": "<p>Used to convert a dedicated IP pool to a different scaling mode.</p> <note> <p> <code>MANAGED</code> pools cannot be converted to <code>STANDARD</code> scaling mode.</p> </note>", "idempotent": true}, "PutDedicatedIpWarmupAttributes": {"name": "PutDedicatedIpWarmupAttributes", "http": {"method": "PUT", "requestUri": "/v2/email/dedicated-ips/{IP}/warmup"}, "input": {"shape": "PutDedicatedIpWarmupAttributesRequest"}, "output": {"shape": "PutDedicatedIpWarmupAttributesResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}], "documentation": "<p/>"}, "PutDeliverabilityDashboardOption": {"name": "PutDeliverabilityDashboardOption", "http": {"method": "PUT", "requestUri": "/v2/email/deliverability-dashboard"}, "input": {"shape": "PutDeliverabilityDashboardOptionRequest"}, "output": {"shape": "PutDeliverabilityDashboardOptionResponse"}, "errors": [{"shape": "AlreadyExistsException"}, {"shape": "NotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "LimitExceededException"}, {"shape": "BadRequestException"}], "documentation": "<p>Enable or disable the Deliverability dashboard. When you enable the Deliverability dashboard, you gain access to reputation, deliverability, and other metrics for the domains that you use to send email. You also gain the ability to perform predictive inbox placement tests.</p> <p>When you use the Deliverability dashboard, you pay a monthly subscription charge, in addition to any other fees that you accrue by using Amazon SES and other Amazon Web Services services. For more information about the features and cost of a Deliverability dashboard subscription, see <a href=\"http://aws.amazon.com/ses/pricing/\">Amazon SES Pricing</a>.</p>"}, "PutEmailIdentityConfigurationSetAttributes": {"name": "PutEmailIdentityConfigurationSetAttributes", "http": {"method": "PUT", "requestUri": "/v2/email/identities/{EmailIdentity}/configuration-set"}, "input": {"shape": "PutEmailIdentityConfigurationSetAttributesRequest"}, "output": {"shape": "PutEmailIdentityConfigurationSetAttributesResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}], "documentation": "<p>Used to associate a configuration set with an email identity.</p>"}, "PutEmailIdentityDkimAttributes": {"name": "PutEmailIdentityDkimAttributes", "http": {"method": "PUT", "requestUri": "/v2/email/identities/{EmailIdentity}/dkim"}, "input": {"shape": "PutEmailIdentityDkimAttributesRequest"}, "output": {"shape": "PutEmailIdentityDkimAttributesResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}], "documentation": "<p>Used to enable or disable DKIM authentication for an email identity.</p>"}, "PutEmailIdentityDkimSigningAttributes": {"name": "PutEmailIdentityDkimSigningAttributes", "http": {"method": "PUT", "requestUri": "/v1/email/identities/{EmailIdentity}/dkim/signing"}, "input": {"shape": "PutEmailIdentityDkimSigningAttributesRequest"}, "output": {"shape": "PutEmailIdentityDkimSigningAttributesResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}], "documentation": "<p>Used to configure or change the DKIM authentication settings for an email domain identity. You can use this operation to do any of the following:</p> <ul> <li> <p>Update the signing attributes for an identity that uses Bring Your Own DKIM (BYODKIM).</p> </li> <li> <p>Update the key length that should be used for Easy DKIM.</p> </li> <li> <p>Change from using no DKIM authentication to using Easy DKIM.</p> </li> <li> <p>Change from using no DKIM authentication to using BYODKIM.</p> </li> <li> <p>Change from using Easy DKIM to using BYODKIM.</p> </li> <li> <p>Change from using BYODKIM to using Easy DKIM.</p> </li> </ul>"}, "PutEmailIdentityFeedbackAttributes": {"name": "PutEmailIdentityFeedbackAttributes", "http": {"method": "PUT", "requestUri": "/v2/email/identities/{EmailIdentity}/feedback"}, "input": {"shape": "PutEmailIdentityFeedbackAttributesRequest"}, "output": {"shape": "PutEmailIdentityFeedbackAttributesResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}], "documentation": "<p>Used to enable or disable feedback forwarding for an identity. This setting determines what happens when an identity is used to send an email that results in a bounce or complaint event.</p> <p>If the value is <code>true</code>, you receive email notifications when bounce or complaint events occur. These notifications are sent to the address that you specified in the <code>Return-Path</code> header of the original email.</p> <p>You're required to have a method of tracking bounces and complaints. If you haven't set up another mechanism for receiving bounce or complaint notifications (for example, by setting up an event destination), you receive an email notification when these events occur (even if this setting is disabled).</p>"}, "PutEmailIdentityMailFromAttributes": {"name": "PutEmailIdentityMailFromAttributes", "http": {"method": "PUT", "requestUri": "/v2/email/identities/{EmailIdentity}/mail-from"}, "input": {"shape": "PutEmailIdentityMailFromAttributesRequest"}, "output": {"shape": "PutEmailIdentityMailFromAttributesResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}], "documentation": "<p>Used to enable or disable the custom Mail-From domain configuration for an email identity.</p>"}, "PutSuppressedDestination": {"name": "PutSuppressedDestination", "http": {"method": "PUT", "requestUri": "/v2/email/suppression/addresses"}, "input": {"shape": "PutSuppressedDestinationRequest"}, "output": {"shape": "PutSuppressedDestinationResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Adds an email address to the suppression list for your account.</p>"}, "SendBulkEmail": {"name": "SendBulkEmail", "http": {"method": "POST", "requestUri": "/v2/email/outbound-bulk-emails"}, "input": {"shape": "SendBulkEmailRequest"}, "output": {"shape": "SendBulkEmailResponse"}, "errors": [{"shape": "TooManyRequestsException"}, {"shape": "LimitExceededException"}, {"shape": "AccountSuspendedException"}, {"shape": "SendingPausedException"}, {"shape": "MessageRejected"}, {"shape": "MailFromDomainNotVerifiedException"}, {"shape": "NotFoundException"}, {"shape": "BadRequestException"}], "documentation": "<p>Composes an email message to multiple destinations.</p>"}, "SendCustomVerificationEmail": {"name": "SendCustomVerificationEmail", "http": {"method": "POST", "requestUri": "/v2/email/outbound-custom-verification-emails"}, "input": {"shape": "SendCustomVerificationEmailRequest"}, "output": {"shape": "SendCustomVerificationEmailResponse"}, "errors": [{"shape": "TooManyRequestsException"}, {"shape": "LimitExceededException"}, {"shape": "MessageRejected"}, {"shape": "SendingPausedException"}, {"shape": "MailFromDomainNotVerifiedException"}, {"shape": "NotFoundException"}, {"shape": "BadRequestException"}], "documentation": "<p>Adds an email address to the list of identities for your Amazon SES account in the current Amazon Web Services Region and attempts to verify it. As a result of executing this operation, a customized verification email is sent to the specified address.</p> <p>To use this operation, you must first create a custom verification email template. For more information about creating and using custom verification email templates, see <a href=\"https://docs.aws.amazon.com/ses/latest/dg/creating-identities.html#send-email-verify-address-custom\">Using custom verification email templates</a> in the <i>Amazon SES Developer Guide</i>.</p> <p>You can execute this operation no more than once per second.</p>"}, "SendEmail": {"name": "SendEmail", "http": {"method": "POST", "requestUri": "/v2/email/outbound-emails"}, "input": {"shape": "SendEmailRequest"}, "output": {"shape": "SendEmailResponse"}, "errors": [{"shape": "TooManyRequestsException"}, {"shape": "LimitExceededException"}, {"shape": "AccountSuspendedException"}, {"shape": "SendingPausedException"}, {"shape": "MessageRejected"}, {"shape": "MailFromDomainNotVerifiedException"}, {"shape": "NotFoundException"}, {"shape": "BadRequestException"}], "documentation": "<p>Sends an email message. You can use the Amazon SES API v2 to send the following types of messages:</p> <ul> <li> <p> <b>Simple</b> – A standard email message. When you create this type of message, you specify the sender, the recipient, and the message body, and Amazon SES assembles the message for you.</p> </li> <li> <p> <b>Raw</b> – A raw, MIME-formatted email message. When you send this type of email, you have to specify all of the message headers, as well as the message body. You can use this message type to send messages that contain attachments. The message that you specify has to be a valid MIME message.</p> </li> <li> <p> <b>Templated</b> – A message that contains personalization tags. When you send this type of email, Amazon SES API v2 automatically replaces the tags with values that you specify.</p> </li> </ul>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/v2/email/tags"}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ConcurrentModificationException"}, {"shape": "NotFoundException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Add one or more tags (keys and values) to a specified resource. A <i>tag</i> is a label that you optionally define and associate with a resource. Tags can help you categorize and manage resources in different ways, such as by purpose, owner, environment, or other criteria. A resource can have as many as 50 tags.</p> <p>Each tag consists of a required <i>tag key</i> and an associated <i>tag value</i>, both of which you define. A tag key is a general label that acts as a category for more specific tag values. A tag value acts as a descriptor within a tag key.</p>"}, "TestRenderEmailTemplate": {"name": "TestRenderEmailTemplate", "http": {"method": "POST", "requestUri": "/v2/email/templates/{TemplateName}/render"}, "input": {"shape": "TestRenderEmailTemplateRequest"}, "output": {"shape": "TestRenderEmailTemplateResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}], "documentation": "<p>Creates a preview of the MIME content of an email when provided with a template and a set of replacement data.</p> <p>You can execute this operation no more than once per second.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/v2/email/tags"}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ConcurrentModificationException"}, {"shape": "NotFoundException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Remove one or more tags (keys and values) from a specified resource.</p>"}, "UpdateConfigurationSetEventDestination": {"name": "UpdateConfigurationSetEventDestination", "http": {"method": "PUT", "requestUri": "/v2/email/configuration-sets/{ConfigurationSetName}/event-destinations/{EventDestinationName}"}, "input": {"shape": "UpdateConfigurationSetEventDestinationRequest"}, "output": {"shape": "UpdateConfigurationSetEventDestinationResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}], "documentation": "<p>Update the configuration of an event destination for a configuration set.</p> <p> <i>Events</i> include message sends, deliveries, opens, clicks, bounces, and complaints. <i>Event destinations</i> are places that you can send information about these events to. For example, you can send event data to Amazon EventBridge and associate a rule to send the event to the specified target.</p>"}, "UpdateContact": {"name": "UpdateContact", "http": {"method": "PUT", "requestUri": "/v2/email/contact-lists/{ContactListName}/contacts/{EmailAddress}"}, "input": {"shape": "UpdateContactRequest"}, "output": {"shape": "UpdateContactResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "TooManyRequestsException"}, {"shape": "NotFoundException"}, {"shape": "ConcurrentModificationException"}], "documentation": "<p>Updates a contact's preferences for a list.</p> <note> <p>You must specify all existing topic preferences in the <code>TopicPreferences</code> object, not just the ones that need updating; otherwise, all your existing preferences will be removed.</p> </note>"}, "UpdateContactList": {"name": "UpdateContactList", "http": {"method": "PUT", "requestUri": "/v2/email/contact-lists/{ContactListName}"}, "input": {"shape": "UpdateContactListRequest"}, "output": {"shape": "UpdateContactListResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "TooManyRequestsException"}, {"shape": "NotFoundException"}, {"shape": "ConcurrentModificationException"}], "documentation": "<p>Updates contact list metadata. This operation does a complete replacement.</p>"}, "UpdateCustomVerificationEmailTemplate": {"name": "UpdateCustomVerificationEmailTemplate", "http": {"method": "PUT", "requestUri": "/v2/email/custom-verification-email-templates/{TemplateName}"}, "input": {"shape": "UpdateCustomVerificationEmailTemplateRequest"}, "output": {"shape": "UpdateCustomVerificationEmailTemplateResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "BadRequestException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Updates an existing custom verification email template.</p> <p>For more information about custom verification email templates, see <a href=\"https://docs.aws.amazon.com/ses/latest/dg/creating-identities.html#send-email-verify-address-custom\">Using custom verification email templates</a> in the <i>Amazon SES Developer Guide</i>.</p> <p>You can execute this operation no more than once per second.</p>"}, "UpdateEmailIdentityPolicy": {"name": "UpdateEmailIdentityPolicy", "http": {"method": "PUT", "requestUri": "/v2/email/identities/{EmailIdentity}/policies/{PolicyName}"}, "input": {"shape": "UpdateEmailIdentityPolicyRequest"}, "output": {"shape": "UpdateEmailIdentityPolicyResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}], "documentation": "<p>Updates the specified sending authorization policy for the given identity (an email address or a domain). This API returns successfully even if a policy with the specified name does not exist.</p> <note> <p>This API is for the identity owner only. If you have not verified the identity, this API will return an error.</p> </note> <p>Sending authorization is a feature that enables an identity owner to authorize other senders to use its identities. For information about using sending authorization, see the <a href=\"https://docs.aws.amazon.com/ses/latest/DeveloperGuide/sending-authorization.html\">Amazon SES Developer Guide</a>.</p> <p>You can execute this operation no more than once per second.</p>"}, "UpdateEmailTemplate": {"name": "UpdateEmailTemplate", "http": {"method": "PUT", "requestUri": "/v2/email/templates/{TemplateName}"}, "input": {"shape": "UpdateEmailTemplateRequest"}, "output": {"shape": "UpdateEmailTemplateResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "BadRequestException"}], "documentation": "<p>Updates an email template. Email templates enable you to send personalized email to one or more destinations in a single API operation. For more information, see the <a href=\"https://docs.aws.amazon.com/ses/latest/DeveloperGuide/send-personalized-email-api.html\">Amazon SES Developer Guide</a>.</p> <p>You can execute this operation no more than once per second.</p>"}}, "shapes": {"AccountDetails": {"type": "structure", "members": {"MailType": {"shape": "MailType", "documentation": "<p>The type of email your account is sending. The mail type can be one of the following:</p> <ul> <li> <p> <code>MARKETING</code> – Most of your sending traffic is to keep your customers informed of your latest offering.</p> </li> <li> <p> <code>TRANSACTIONAL</code> – Most of your sending traffic is to communicate during a transaction with a customer.</p> </li> </ul>"}, "WebsiteURL": {"shape": "WebsiteURL", "documentation": "<p>The URL of your website. This information helps us better understand the type of content that you plan to send.</p>"}, "ContactLanguage": {"shape": "ContactLanguage", "documentation": "<p>The language you would prefer for the case. The contact language can be one of <code>ENGLISH</code> or <code>JAPANESE</code>.</p>"}, "UseCaseDescription": {"shape": "UseCaseDescription", "documentation": "<p>A description of the types of email that you plan to send.</p>"}, "AdditionalContactEmailAddresses": {"shape": "AdditionalContactEmailAddresses", "documentation": "<p>Additional email addresses where updates are sent about your account review process.</p>"}, "ReviewDetails": {"shape": "ReviewDetails", "documentation": "<p>Information about the review of the latest details you submitted.</p>"}}, "documentation": "<p>An object that contains information about your account details.</p>"}, "AccountSuspendedException": {"type": "structure", "members": {}, "documentation": "<p>The message can't be sent because the account's ability to send email has been permanently restricted.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "AdditionalContactEmailAddress": {"type": "string", "max": 254, "min": 6, "pattern": "^(.+)@(.+)$", "sensitive": true}, "AdditionalContactEmailAddresses": {"type": "list", "member": {"shape": "AdditionalContactEmailAddress"}, "max": 4, "min": 1, "sensitive": true}, "AdminEmail": {"type": "string"}, "AlreadyExistsException": {"type": "structure", "members": {}, "documentation": "<p>The resource specified in your request already exists.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "AmazonResourceName": {"type": "string"}, "ArchiveArn": {"type": "string", "max": 1011, "min": 20, "pattern": "arn:(aws|aws-[a-z-]+):ses:[a-z]{2}-[a-z-]+-[0-9]:[0-9]{1,20}:mailmanager-archive/a-[a-z0-9]{24,62}"}, "ArchivingOptions": {"type": "structure", "members": {"ArchiveArn": {"shape": "ArchiveArn", "documentation": "<p>The Amazon Resource Name (ARN) of the MailManager archive where the Amazon SES API v2 will archive sent emails.</p>"}}, "documentation": "<p>Used to associate a configuration set with a MailManager archive.</p>"}, "Attachment": {"type": "structure", "required": ["Raw<PERSON><PERSON><PERSON>", "FileName"], "members": {"RawContent": {"shape": "RawAttachmentData", "documentation": "<p> The raw data of the attachment. It needs to be base64-encoded if you are accessing Amazon SES directly through the HTTPS interface. If you are accessing Amazon SES using an Amazon Web Services SDK, the SDK takes care of the base 64-encoding for you.</p>"}, "ContentDisposition": {"shape": "AttachmentContentDisposition", "documentation": "<p> A standard descriptor indicating how the attachment should be rendered in the email. Supported values: <code>ATTACHMENT</code> or <code>INLINE</code>.</p>"}, "FileName": {"shape": "AttachmentFileName", "documentation": "<p>The file name for the attachment as it will appear in the email. Amazon SES restricts certain file extensions. To ensure attachments are accepted, check the <a href=\"https://docs.aws.amazon.com/ses/latest/dg/mime-types.html\">Unsupported attachment types</a> in the Amazon SES Developer Guide.</p>"}, "ContentDescription": {"shape": "AttachmentContentDescription", "documentation": "<p> A brief description of the attachment content.</p>"}, "ContentId": {"shape": "AttachmentContentId", "documentation": "<p> Unique identifier for the attachment, used for referencing attachments with INLINE disposition in HTML content.</p>"}, "ContentTransferEncoding": {"shape": "AttachmentContentTransferEncoding", "documentation": "<p> Specifies how the attachment is encoded. Supported values: <code>BASE64</code>, <code>QUOTED_PRINTABLE</code>, <code>SEVEN_BIT</code>.</p>"}, "ContentType": {"shape": "AttachmentContentType", "documentation": "<p> The MIME type of the attachment.</p> <note> <p>Example: <code>application/pdf</code>, <code>image/jpeg</code> </p> </note>"}}, "documentation": "<p> Contains metadata and attachment raw content.</p>"}, "AttachmentContentDescription": {"type": "string", "max": 1000}, "AttachmentContentDisposition": {"type": "string", "enum": ["ATTACHMENT", "INLINE"]}, "AttachmentContentId": {"type": "string", "max": 78, "min": 1}, "AttachmentContentTransferEncoding": {"type": "string", "enum": ["BASE64", "QUOTED_PRINTABLE", "SEVEN_BIT"]}, "AttachmentContentType": {"type": "string", "max": 78, "min": 1}, "AttachmentFileName": {"type": "string", "max": 255}, "AttachmentList": {"type": "list", "member": {"shape": "Attachment"}}, "AttributesData": {"type": "string"}, "BadRequestException": {"type": "structure", "members": {}, "documentation": "<p>The input you provided is invalid.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "BatchGetMetricDataQueries": {"type": "list", "member": {"shape": "BatchGetMetricDataQuery"}, "max": 10, "min": 1}, "BatchGetMetricDataQuery": {"type": "structure", "required": ["Id", "Namespace", "Metric", "StartDate", "EndDate"], "members": {"Id": {"shape": "QueryIdentifier", "documentation": "<p>The query identifier.</p>"}, "Namespace": {"shape": "MetricNamespace", "documentation": "<p>The query namespace - e.g. <code>VDM</code> </p>"}, "Metric": {"shape": "Metric", "documentation": "<p>The queried metric. This can be one of the following:</p> <ul> <li> <p> <code>SEND</code> – Emails sent eligible for tracking in the VDM dashboard. This excludes emails sent to the mailbox simulator and emails addressed to more than one recipient.</p> </li> <li> <p> <code>COMPLAINT</code> – Complaints received for your account. This excludes complaints from the mailbox simulator, those originating from your account-level suppression list (if enabled), and those for emails addressed to more than one recipient</p> </li> <li> <p> <code>PERMANENT_BOUNCE</code> – Permanent bounces - i.e. feedback received for emails sent to non-existent mailboxes. Excludes bounces from the mailbox simulator, those originating from your account-level suppression list (if enabled), and those for emails addressed to more than one recipient.</p> </li> <li> <p> <code>TRANSIENT_BOUNCE</code> – Transient bounces - i.e. feedback received for delivery failures excluding issues with non-existent mailboxes. Excludes bounces from the mailbox simulator, and those for emails addressed to more than one recipient.</p> </li> <li> <p> <code>OPEN</code> – Unique open events for emails including open trackers. Excludes opens for emails addressed to more than one recipient.</p> </li> <li> <p> <code>CLICK</code> – Unique click events for emails including wrapped links. Excludes clicks for emails addressed to more than one recipient.</p> </li> <li> <p> <code>DELIVERY</code> – Successful deliveries for email sending attempts. Excludes deliveries to the mailbox simulator and for emails addressed to more than one recipient.</p> </li> <li> <p> <code>DELIVERY_OPEN</code> – Successful deliveries for email sending attempts. Excludes deliveries to the mailbox simulator, for emails addressed to more than one recipient, and emails without open trackers.</p> </li> <li> <p> <code>DELIVERY_CLICK</code> – Successful deliveries for email sending attempts. Excludes deliveries to the mailbox simulator, for emails addressed to more than one recipient, and emails without click trackers.</p> </li> <li> <p> <code>DELIVERY_COMPLAINT</code> – Successful deliveries for email sending attempts. Excludes deliveries to the mailbox simulator, for emails addressed to more than one recipient, and emails addressed to recipients hosted by ISPs with which Amazon SES does not have a feedback loop agreement.</p> </li> </ul>"}, "Dimensions": {"shape": "Dimensions", "documentation": "<p>An object that contains mapping between <code>MetricDimensionName</code> and <code>MetricDimensionValue</code> to filter metrics by.</p>"}, "StartDate": {"shape": "Timestamp", "documentation": "<p>Represents the start date for the query interval.</p>"}, "EndDate": {"shape": "Timestamp", "documentation": "<p>Represents the end date for the query interval.</p>"}}, "documentation": "<p>Represents a single metric data query to include in a batch.</p>"}, "BatchGetMetricDataRequest": {"type": "structure", "required": ["Queries"], "members": {"Queries": {"shape": "BatchGetMetricDataQueries", "documentation": "<p>A list of queries for metrics to be retrieved.</p>"}}, "documentation": "<p>Represents a request to retrieve a batch of metric data.</p>"}, "BatchGetMetricDataResponse": {"type": "structure", "members": {"Results": {"shape": "MetricDataResultList", "documentation": "<p>A list of successfully retrieved <code>MetricDataResult</code>.</p>"}, "Errors": {"shape": "MetricDataErrorList", "documentation": "<p>A list of <code>MetricDataError</code> encountered while processing your metric data batch request.</p>"}}, "documentation": "<p>Represents the result of processing your metric data batch request</p>"}, "BehaviorOnMxFailure": {"type": "string", "documentation": "<p>The action to take if the required MX record can't be found when you send an email. When you set this value to <code>UseDefaultValue</code>, the mail is sent using <i>amazonses.com</i> as the MAIL FROM domain. When you set this value to <code>RejectMessage</code>, the Amazon SES API v2 returns a <code>MailFromDomainNotVerified</code> error, and doesn't attempt to deliver the email.</p> <p>These behaviors are taken when the custom MAIL FROM domain configuration is in the <code>Pending</code>, <code>Failed</code>, and <code>TemporaryFailure</code> states.</p>", "enum": ["USE_DEFAULT_VALUE", "REJECT_MESSAGE"]}, "BlacklistEntries": {"type": "list", "member": {"shape": "BlacklistEntry"}}, "BlacklistEntry": {"type": "structure", "members": {"RblName": {"shape": "RblName", "documentation": "<p>The name of the blacklist that the IP address appears on.</p>"}, "ListingTime": {"shape": "Timestamp", "documentation": "<p>The time when the blacklisting event occurred.</p>"}, "Description": {"shape": "BlacklistingDescription", "documentation": "<p>Additional information about the blacklisting event, as provided by the blacklist maintainer.</p>"}}, "documentation": "<p>An object that contains information about a blacklisting event that impacts one of the dedicated IP addresses that is associated with your account.</p>"}, "BlacklistItemName": {"type": "string", "documentation": "<p>An IP address that you want to obtain blacklist information for.</p>"}, "BlacklistItemNames": {"type": "list", "member": {"shape": "BlacklistItemName"}}, "BlacklistReport": {"type": "map", "key": {"shape": "BlacklistItemName"}, "value": {"shape": "BlacklistEntries"}}, "BlacklistingDescription": {"type": "string", "documentation": "<p>A description of the blacklisting event.</p>"}, "Body": {"type": "structure", "members": {"Text": {"shape": "Content", "documentation": "<p>An object that represents the version of the message that is displayed in email clients that don't support HTML, or clients where the recipient has disabled HTML rendering.</p>"}, "Html": {"shape": "Content", "documentation": "<p>An object that represents the version of the message that is displayed in email clients that support HTML. HTML messages can include formatted text, hyperlinks, images, and more. </p>"}}, "documentation": "<p>Represents the body of the email message.</p>"}, "Bounce": {"type": "structure", "members": {"BounceType": {"shape": "BounceType", "documentation": "<p>The type of the bounce, as determined by SES. Can be one of <code>UNDETERMINED</code>, <code>TRANSIENT</code>, or <code>PERMANENT</code> </p>"}, "BounceSubType": {"shape": "BounceSubType", "documentation": "<p>The subtype of the bounce, as determined by SES.</p>"}, "DiagnosticCode": {"shape": "DiagnosticCode", "documentation": "<p>The status code issued by the reporting Message Transfer Authority (MTA). This field only appears if a delivery status notification (DSN) was attached to the bounce and the <code>Diagnostic-Code</code> was provided in the DSN. </p>"}}, "documentation": "<p>Information about a <code>Bounce</code> event.</p>"}, "BounceSubType": {"type": "string"}, "BounceType": {"type": "string", "enum": ["UNDETERMINED", "TRANSIENT", "PERMANENT"]}, "BulkEmailContent": {"type": "structure", "members": {"Template": {"shape": "Template", "documentation": "<p>The template to use for the bulk email message.</p>"}}, "documentation": "<p>An object that contains the body of the message. You can specify a template message.</p>"}, "BulkEmailEntry": {"type": "structure", "required": ["Destination"], "members": {"Destination": {"shape": "Destination", "documentation": "<p>Represents the destination of the message, consisting of To:, CC:, and BCC: fields.</p> <note> <p>Amazon SES does not support the SMTPUTF8 extension, as described in <a href=\"https://tools.ietf.org/html/rfc6531\">RFC6531</a>. For this reason, the local part of a destination email address (the part of the email address that precedes the @ sign) may only contain <a href=\"https://en.wikipedia.org/wiki/Email_address#Local-part\">7-bit ASCII characters</a>. If the domain part of an address (the part after the @ sign) contains non-ASCII characters, they must be encoded using Punycode, as described in <a href=\"https://tools.ietf.org/html/rfc3492.html\">RFC3492</a>.</p> </note>"}, "ReplacementTags": {"shape": "MessageTagList", "documentation": "<p>A list of tags, in the form of name/value pairs, to apply to an email that you send using the <code>SendBulkTemplatedEmail</code> operation. Tags correspond to characteristics of the email that you define, so that you can publish email sending events.</p>"}, "ReplacementEmailContent": {"shape": "ReplacementEmail<PERSON><PERSON>nt", "documentation": "<p>The <code>ReplacementEmailContent</code> associated with a <code>BulkEmailEntry</code>.</p>"}, "ReplacementHeaders": {"shape": "MessageHeaderList", "documentation": "<p>The list of message headers associated with the <code>BulkEmailEntry</code> data type.</p> <ul> <li> <p>Headers Not Present in <code>BulkEmailEntry</code>: If a header is specified in <a href=\"https://docs.aws.amazon.com/ses/latest/APIReference-V2/API_Template.html\"> <code>Template</code> </a> but not in <code>BulkEmailEntry</code>, the header from <code>Template</code> will be added to the outgoing email.</p> </li> <li> <p>Headers Present in <code>BulkEmailEntry</code>: If a header is specified in <code>BulkEmailEntry</code>, it takes precedence over any header of the same name specified in <a href=\"https://docs.aws.amazon.com/ses/latest/APIReference-V2/API_Template.html\"> <code>Template</code> </a>:</p> <ul> <li> <p>If the header is also defined within <code>Template</code>, the value from <code>BulkEmailEntry</code> will replace the header's value in the email.</p> </li> <li> <p>If the header is not defined within <code>Template</code>, it will simply be added to the email as specified in <code>BulkEmailEntry</code>.</p> </li> </ul> </li> </ul>"}}}, "BulkEmailEntryList": {"type": "list", "member": {"shape": "BulkEmailEntry"}, "documentation": "<p>A list of <code>BulkEmailEntry</code> objects.</p>"}, "BulkEmailEntryResult": {"type": "structure", "members": {"Status": {"shape": "BulkEmailStatus", "documentation": "<p>The status of a message sent using the <code>SendBulkTemplatedEmail</code> operation.</p> <p>Possible values for this parameter include:</p> <ul> <li> <p>SUCCESS: Amazon SES accepted the message, and will attempt to deliver it to the recipients.</p> </li> <li> <p>MESSAGE_REJECTED: The message was rejected because it contained a virus.</p> </li> <li> <p>MAIL_FROM_DOMAIN_NOT_VERIFIED: The sender's email address or domain was not verified.</p> </li> <li> <p>CONFIGURATION_SET_DOES_NOT_EXIST: The configuration set you specified does not exist.</p> </li> <li> <p>TEMPLATE_DOES_NOT_EXIST: The template you specified does not exist.</p> </li> <li> <p>ACCOUNT_SUSPENDED: Your account has been shut down because of issues related to your email sending practices.</p> </li> <li> <p>ACCOUNT_THROTTLED: The number of emails you can send has been reduced because your account has exceeded its allocated sending limit.</p> </li> <li> <p>ACCOUNT_DAILY_QUOTA_EXCEEDED: You have reached or exceeded the maximum number of emails you can send from your account in a 24-hour period.</p> </li> <li> <p>INVALID_SENDING_POOL_NAME: The configuration set you specified refers to an IP pool that does not exist.</p> </li> <li> <p>ACCOUNT_SENDING_PAUSED: Email sending for the Amazon SES account was disabled using the <a href=\"https://docs.aws.amazon.com/ses/latest/APIReference/API_UpdateAccountSendingEnabled.html\">UpdateAccountSendingEnabled</a> operation.</p> </li> <li> <p>CONFIGURATION_SET_SENDING_PAUSED: Email sending for this configuration set was disabled using the <a href=\"https://docs.aws.amazon.com/ses/latest/APIReference/API_UpdateConfigurationSetSendingEnabled.html\">UpdateConfigurationSetSendingEnabled</a> operation.</p> </li> <li> <p>INVALID_PARAMETER_VALUE: One or more of the parameters you specified when calling this operation was invalid. See the error message for additional information.</p> </li> <li> <p>TRANSIENT_FAILURE: Amazon SES was unable to process your request because of a temporary issue.</p> </li> <li> <p>FAILED: Amazon SES was unable to process your request. See the error message for additional information.</p> </li> </ul>"}, "Error": {"shape": "ErrorMessage", "documentation": "<p>A description of an error that prevented a message being sent using the <code>SendBulkTemplatedEmail</code> operation.</p>"}, "MessageId": {"shape": "OutboundMessageId", "documentation": "<p>The unique message identifier returned from the <code>SendBulkTemplatedEmail</code> operation.</p>"}}, "documentation": "<p>The result of the <code>SendBulkEmail</code> operation of each specified <code>BulkEmailEntry</code>.</p>"}, "BulkEmailEntryResultList": {"type": "list", "member": {"shape": "BulkEmailEntryResult"}, "documentation": "<p>A list of <code>BulkMailEntry</code> objects.</p>"}, "BulkEmailStatus": {"type": "string", "enum": ["SUCCESS", "MESSAGE_REJECTED", "MAIL_FROM_DOMAIN_NOT_VERIFIED", "CONFIGURATION_SET_NOT_FOUND", "TEMPLATE_NOT_FOUND", "ACCOUNT_SUSPENDED", "ACCOUNT_THROTTLED", "ACCOUNT_DAILY_QUOTA_EXCEEDED", "INVALID_SENDING_POOL_NAME", "ACCOUNT_SENDING_PAUSED", "CONFIGURATION_SET_SENDING_PAUSED", "INVALID_PARAMETER", "TRANSIENT_FAILURE", "FAILED"]}, "CampaignId": {"type": "string"}, "CancelExportJobRequest": {"type": "structure", "required": ["JobId"], "members": {"JobId": {"shape": "JobId", "documentation": "<p>The export job ID.</p>", "location": "uri", "locationName": "JobId"}}, "documentation": "<p>Represents a request to cancel an export job using the export job ID.</p>"}, "CancelExportJobResponse": {"type": "structure", "members": {}, "documentation": "<p>An HTTP 200 response if the request succeeds, or an error message if the request fails.</p>"}, "CaseId": {"type": "string"}, "Charset": {"type": "string"}, "CloudWatchDestination": {"type": "structure", "required": ["DimensionConfigurations"], "members": {"DimensionConfigurations": {"shape": "CloudWatchDimensionConfigurations", "documentation": "<p>An array of objects that define the dimensions to use when you send email events to Amazon CloudWatch.</p>"}}, "documentation": "<p>An object that defines an Amazon CloudWatch destination for email events. You can use Amazon CloudWatch to monitor and gain insights on your email sending metrics.</p>"}, "CloudWatchDimensionConfiguration": {"type": "structure", "required": ["DimensionName", "DimensionValueSource", "DefaultDimensionValue"], "members": {"DimensionName": {"shape": "DimensionName", "documentation": "<p>The name of an Amazon CloudWatch dimension associated with an email sending metric. The name has to meet the following criteria:</p> <ul> <li> <p>It can only contain ASCII letters (a–z, A–Z), numbers (0–9), underscores (_), or dashes (-).</p> </li> <li> <p>It can contain no more than 256 characters.</p> </li> </ul>"}, "DimensionValueSource": {"shape": "DimensionValueSource", "documentation": "<p>The location where the Amazon SES API v2 finds the value of a dimension to publish to Amazon CloudWatch. To use the message tags that you specify using an <code>X-SES-MESSAGE-TAGS</code> header or a parameter to the <code>SendEmail</code> or <code>SendRawEmail</code> API, choose <code>messageTag</code>. To use your own email headers, choose <code>emailHeader</code>. To use link tags, choose <code>linkTags</code>.</p>"}, "DefaultDimensionValue": {"shape": "DefaultDimensionValue", "documentation": "<p>The default value of the dimension that is published to Amazon CloudWatch if you don't provide the value of the dimension when you send an email. This value has to meet the following criteria:</p> <ul> <li> <p>Can only contain ASCII letters (a–z, A–Z), numbers (0–9), underscores (_), or dashes (-), at signs (@), and periods (.).</p> </li> <li> <p>It can contain no more than 256 characters.</p> </li> </ul>"}}, "documentation": "<p>An object that defines the dimension configuration to use when you send email events to Amazon CloudWatch.</p>"}, "CloudWatchDimensionConfigurations": {"type": "list", "member": {"shape": "CloudWatchDimensionConfiguration"}}, "Complaint": {"type": "structure", "members": {"ComplaintSubType": {"shape": "ComplaintSubType", "documentation": "<p> Can either be <code>null</code> or <code>OnAccountSuppressionList</code>. If the value is <code>OnAccountSuppressionList</code>, SES accepted the message, but didn't attempt to send it because it was on the account-level suppression list. </p>"}, "ComplaintFeedbackType": {"shape": "ComplaintFeedbackType", "documentation": "<p> The value of the <code>Feedback-Type</code> field from the feedback report received from the ISP. </p>"}}, "documentation": "<p>Information about a <code>Complaint</code> event.</p>"}, "ComplaintFeedbackType": {"type": "string"}, "ComplaintSubType": {"type": "string"}, "ConcurrentModificationException": {"type": "structure", "members": {}, "documentation": "<p>The resource is being modified by another operation or thread.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true}, "ConfigurationSetName": {"type": "string", "documentation": "<p>The name of a configuration set.</p> <p> <i>Configuration sets</i> are groups of rules that you can apply to the emails you send. You apply a configuration set to an email by including a reference to the configuration set in the headers of the email. When you apply a configuration set to an email, all of the rules in that configuration set are applied to the email.</p>"}, "ConfigurationSetNameList": {"type": "list", "member": {"shape": "ConfigurationSetName"}}, "ConflictException": {"type": "structure", "members": {}, "documentation": "<p>If there is already an ongoing account details update under review.</p>", "error": {"httpStatusCode": 409}, "exception": true}, "Contact": {"type": "structure", "members": {"EmailAddress": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The contact's email address.</p>"}, "TopicPreferences": {"shape": "TopicPreferenceList", "documentation": "<p>The contact's preference for being opted-in to or opted-out of a topic.</p>"}, "TopicDefaultPreferences": {"shape": "TopicPreferenceList", "documentation": "<p>The default topic preferences applied to the contact.</p>"}, "UnsubscribeAll": {"shape": "UnsubscribeAll", "documentation": "<p>A boolean value status noting if the contact is unsubscribed from all contact list topics.</p>"}, "LastUpdatedTimestamp": {"shape": "Timestamp", "documentation": "<p>A timestamp noting the last time the contact's information was updated.</p>"}}, "documentation": "<p>A contact is the end-user who is receiving the email.</p>"}, "ContactLanguage": {"type": "string", "enum": ["EN", "JA"]}, "ContactList": {"type": "structure", "members": {"ContactListName": {"shape": "ContactListName", "documentation": "<p>The name of the contact list.</p>"}, "LastUpdatedTimestamp": {"shape": "Timestamp", "documentation": "<p>A timestamp noting the last time the contact list was updated.</p>"}}, "documentation": "<p>A list that contains contacts that have subscribed to a particular topic or topics.</p>"}, "ContactListDestination": {"type": "structure", "required": ["ContactListName", "ContactListImportAction"], "members": {"ContactListName": {"shape": "ContactListName", "documentation": "<p>The name of the contact list.</p>"}, "ContactListImportAction": {"shape": "ContactListImportAction", "documentation": "<p>&gt;The type of action to perform on the addresses. The following are the possible values:</p> <ul> <li> <p>PUT: add the addresses to the contact list. If the record already exists, it will override it with the new value.</p> </li> <li> <p>DELETE: remove the addresses from the contact list.</p> </li> </ul>"}}, "documentation": "<p>An object that contains details about the action of a contact list.</p>"}, "ContactListImportAction": {"type": "string", "enum": ["DELETE", "PUT"]}, "ContactListName": {"type": "string"}, "Content": {"type": "structure", "required": ["Data"], "members": {"Data": {"shape": "MessageData", "documentation": "<p>The content of the message itself.</p>"}, "Charset": {"shape": "Charset", "documentation": "<p>The character set for the content. Because of the constraints of the SMTP protocol, Amazon SES uses 7-bit ASCII by default. If the text includes characters outside of the ASCII range, you have to specify a character set. For example, you could specify <code>UTF-8</code>, <code>ISO-8859-1</code>, or <code>Shift_JIS</code>.</p>"}}, "documentation": "<p>An object that represents the content of the email, and optionally a character set specification.</p>"}, "Counter": {"type": "long"}, "CreateConfigurationSetEventDestinationRequest": {"type": "structure", "required": ["ConfigurationSetName", "EventDestinationName", "EventDestination"], "members": {"ConfigurationSetName": {"shape": "ConfigurationSetName", "documentation": "<p>The name of the configuration set .</p>", "location": "uri", "locationName": "ConfigurationSetName"}, "EventDestinationName": {"shape": "EventDestinationName", "documentation": "<p>A name that identifies the event destination within the configuration set.</p>"}, "EventDestination": {"shape": "EventDestinationDefinition", "documentation": "<p>An object that defines the event destination.</p>"}}, "documentation": "<p>A request to add an event destination to a configuration set.</p>"}, "CreateConfigurationSetEventDestinationResponse": {"type": "structure", "members": {}, "documentation": "<p>An HTTP 200 response if the request succeeds, or an error message if the request fails.</p>"}, "CreateConfigurationSetRequest": {"type": "structure", "required": ["ConfigurationSetName"], "members": {"ConfigurationSetName": {"shape": "ConfigurationSetName", "documentation": "<p>The name of the configuration set. The name can contain up to 64 alphanumeric characters, including letters, numbers, hyphens (-) and underscores (_) only.</p>"}, "TrackingOptions": {"shape": "TrackingOptions", "documentation": "<p>An object that defines the open and click tracking options for emails that you send using the configuration set.</p>"}, "DeliveryOptions": {"shape": "DeliveryOptions", "documentation": "<p>An object that defines the dedicated IP pool that is used to send emails that you send using the configuration set.</p>"}, "ReputationOptions": {"shape": "ReputationOptions", "documentation": "<p>An object that defines whether or not Amazon SES collects reputation metrics for the emails that you send that use the configuration set.</p>"}, "SendingOptions": {"shape": "SendingOptions", "documentation": "<p>An object that defines whether or not Amazon SES can send email that you send using the configuration set.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>An array of objects that define the tags (keys and values) to associate with the configuration set.</p>"}, "SuppressionOptions": {"shape": "SuppressionOptions"}, "VdmOptions": {"shape": "VdmOptions", "documentation": "<p>An object that defines the VDM options for emails that you send using the configuration set.</p>"}, "ArchivingOptions": {"shape": "ArchivingOptions", "documentation": "<p>An object that defines the MailManager archiving options for emails that you send using the configuration set.</p>"}}, "documentation": "<p>A request to create a configuration set.</p>"}, "CreateConfigurationSetResponse": {"type": "structure", "members": {}, "documentation": "<p>An HTTP 200 response if the request succeeds, or an error message if the request fails.</p>"}, "CreateContactListRequest": {"type": "structure", "required": ["ContactListName"], "members": {"ContactListName": {"shape": "ContactListName", "documentation": "<p>The name of the contact list.</p>"}, "Topics": {"shape": "Topics", "documentation": "<p>An interest group, theme, or label within a list. A contact list can have multiple topics.</p>"}, "Description": {"shape": "Description", "documentation": "<p>A description of what the contact list is about.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The tags associated with a contact list.</p>"}}}, "CreateContactListResponse": {"type": "structure", "members": {}}, "CreateContactRequest": {"type": "structure", "required": ["ContactListName", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "members": {"ContactListName": {"shape": "ContactListName", "documentation": "<p>The name of the contact list to which the contact should be added.</p>", "location": "uri", "locationName": "ContactListName"}, "EmailAddress": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The contact's email address.</p>"}, "TopicPreferences": {"shape": "TopicPreferenceList", "documentation": "<p>The contact's preferences for being opted-in to or opted-out of topics.</p>"}, "UnsubscribeAll": {"shape": "UnsubscribeAll", "documentation": "<p>A boolean value status noting if the contact is unsubscribed from all contact list topics.</p>"}, "AttributesData": {"shape": "AttributesData", "documentation": "<p>The attribute data attached to a contact.</p>"}}}, "CreateContactResponse": {"type": "structure", "members": {}}, "CreateCustomVerificationEmailTemplateRequest": {"type": "structure", "required": ["TemplateName", "FromEmailAddress", "TemplateSubject", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SuccessRedirectionURL", "FailureRedirectionURL"], "members": {"TemplateName": {"shape": "EmailTemplateName", "documentation": "<p>The name of the custom verification email template.</p>"}, "FromEmailAddress": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The email address that the custom verification email is sent from.</p>"}, "TemplateSubject": {"shape": "EmailTemplateSubject", "documentation": "<p>The subject line of the custom verification email.</p>"}, "TemplateContent": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The content of the custom verification email. The total size of the email must be less than 10 MB. The message body may contain HTML, with some limitations. For more information, see <a href=\"https://docs.aws.amazon.com/ses/latest/dg/creating-identities.html#send-email-verify-address-custom-faq\">Custom verification email frequently asked questions</a> in the <i>Amazon SES Developer Guide</i>.</p>"}, "SuccessRedirectionURL": {"shape": "SuccessRedirectionURL", "documentation": "<p>The URL that the recipient of the verification email is sent to if his or her address is successfully verified.</p>"}, "FailureRedirectionURL": {"shape": "FailureRedirectionURL", "documentation": "<p>The URL that the recipient of the verification email is sent to if his or her address is not successfully verified.</p>"}}, "documentation": "<p>Represents a request to create a custom verification email template.</p>"}, "CreateCustomVerificationEmailTemplateResponse": {"type": "structure", "members": {}, "documentation": "<p>If the action is successful, the service sends back an HTTP 200 response with an empty HTTP body.</p>"}, "CreateDedicatedIpPoolRequest": {"type": "structure", "required": ["PoolName"], "members": {"PoolName": {"shape": "PoolName", "documentation": "<p>The name of the dedicated IP pool.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>An object that defines the tags (keys and values) that you want to associate with the pool.</p>"}, "ScalingMode": {"shape": "ScalingMode", "documentation": "<p>The type of scaling mode.</p>"}}, "documentation": "<p>A request to create a new dedicated IP pool.</p>"}, "CreateDedicatedIpPoolResponse": {"type": "structure", "members": {}, "documentation": "<p>An HTTP 200 response if the request succeeds, or an error message if the request fails.</p>"}, "CreateDeliverabilityTestReportRequest": {"type": "structure", "required": ["FromEmailAddress", "Content"], "members": {"ReportName": {"shape": "ReportName", "documentation": "<p>A unique name that helps you to identify the predictive inbox placement test when you retrieve the results.</p>"}, "FromEmailAddress": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The email address that the predictive inbox placement test email was sent from.</p>"}, "Content": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The HTML body of the message that you sent when you performed the predictive inbox placement test.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>An array of objects that define the tags (keys and values) that you want to associate with the predictive inbox placement test.</p>"}}, "documentation": "<p>A request to perform a predictive inbox placement test. Predictive inbox placement tests can help you predict how your messages will be handled by various email providers around the world. When you perform a predictive inbox placement test, you provide a sample message that contains the content that you plan to send to your customers. We send that message to special email addresses spread across several major email providers around the world. The test takes about 24 hours to complete. When the test is complete, you can use the <code>GetDeliverabilityTestReport</code> operation to view the results of the test.</p>"}, "CreateDeliverabilityTestReportResponse": {"type": "structure", "required": ["ReportId", "DeliverabilityTestStatus"], "members": {"ReportId": {"shape": "ReportId", "documentation": "<p>A unique string that identifies the predictive inbox placement test.</p>"}, "DeliverabilityTestStatus": {"shape": "DeliverabilityTestStatus", "documentation": "<p>The status of the predictive inbox placement test. If the status is <code>IN_PROGRESS</code>, then the predictive inbox placement test is currently running. Predictive inbox placement tests are usually complete within 24 hours of creating the test. If the status is <code>COMPLETE</code>, then the test is finished, and you can use the <code>GetDeliverabilityTestReport</code> to view the results of the test.</p>"}}, "documentation": "<p>Information about the predictive inbox placement test that you created.</p>"}, "CreateEmailIdentityPolicyRequest": {"type": "structure", "required": ["EmailIdentity", "PolicyName", "Policy"], "members": {"EmailIdentity": {"shape": "Identity", "documentation": "<p>The email identity.</p>", "location": "uri", "locationName": "EmailIdentity"}, "PolicyName": {"shape": "PolicyName", "documentation": "<p>The name of the policy.</p> <p>The policy name cannot exceed 64 characters and can only include alphanumeric characters, dashes, and underscores.</p>", "location": "uri", "locationName": "PolicyName"}, "Policy": {"shape": "Policy", "documentation": "<p>The text of the policy in JSON format. The policy cannot exceed 4 KB.</p> <p>For information about the syntax of sending authorization policies, see the <a href=\"https://docs.aws.amazon.com/ses/latest/DeveloperGuide/sending-authorization-policies.html\">Amazon SES Developer Guide</a>.</p>"}}, "documentation": "<p>Represents a request to create a sending authorization policy for an identity. Sending authorization is an Amazon SES feature that enables you to authorize other senders to use your identities. For information, see the <a href=\"https://docs.aws.amazon.com/ses/latest/DeveloperGuide/sending-authorization-identity-owner-tasks-management.html\">Amazon SES Developer Guide</a>.</p>"}, "CreateEmailIdentityPolicyResponse": {"type": "structure", "members": {}, "documentation": "<p>An HTTP 200 response if the request succeeds, or an error message if the request fails.</p>"}, "CreateEmailIdentityRequest": {"type": "structure", "required": ["EmailIdentity"], "members": {"EmailIdentity": {"shape": "Identity", "documentation": "<p>The email address or domain to verify.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>An array of objects that define the tags (keys and values) to associate with the email identity.</p>"}, "DkimSigningAttributes": {"shape": "DkimSigningAttributes", "documentation": "<p>If your request includes this object, Amazon SES configures the identity to use Bring Your Own DKIM (BYODKIM) for DKIM authentication purposes, or, configures the key length to be used for <a href=\"https://docs.aws.amazon.com/ses/latest/DeveloperGuide/easy-dkim.html\">Easy DKIM</a>.</p> <p>You can only specify this object if the email identity is a domain, as opposed to an address.</p>"}, "ConfigurationSetName": {"shape": "ConfigurationSetName", "documentation": "<p>The configuration set to use by default when sending from this identity. Note that any configuration set defined in the email sending request takes precedence. </p>"}}, "documentation": "<p>A request to begin the verification process for an email identity (an email address or domain).</p>"}, "CreateEmailIdentityResponse": {"type": "structure", "members": {"IdentityType": {"shape": "IdentityType", "documentation": "<p>The email identity type. Note: the <code>MANAGED_DOMAIN</code> identity type is not supported.</p>"}, "VerifiedForSendingStatus": {"shape": "Enabled", "documentation": "<p>Specifies whether or not the identity is verified. You can only send email from verified email addresses or domains. For more information about verifying identities, see the <a href=\"https://docs.aws.amazon.com/pinpoint/latest/userguide/channels-email-manage-verify.html\">Amazon Pinpoint User Guide</a>.</p>"}, "DkimAttributes": {"shape": "DkimAttributes", "documentation": "<p>An object that contains information about the DKIM attributes for the identity.</p>"}}, "documentation": "<p>If the email identity is a domain, this object contains information about the DKIM verification status for the domain.</p> <p>If the email identity is an email address, this object is empty. </p>"}, "CreateEmailTemplateRequest": {"type": "structure", "required": ["TemplateName", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "members": {"TemplateName": {"shape": "EmailTemplateName", "documentation": "<p>The name of the template.</p>"}, "TemplateContent": {"shape": "EmailTemplateContent", "documentation": "<p>The content of the email template, composed of a subject line, an HTML part, and a text-only part.</p>"}}, "documentation": "<p>Represents a request to create an email template. For more information, see the <a href=\"https://docs.aws.amazon.com/ses/latest/DeveloperGuide/send-personalized-email-api.html\">Amazon SES Developer Guide</a>.</p>"}, "CreateEmailTemplateResponse": {"type": "structure", "members": {}, "documentation": "<p>If the action is successful, the service sends back an HTTP 200 response with an empty HTTP body.</p>"}, "CreateExportJobRequest": {"type": "structure", "required": ["ExportDataSource", "ExportDestination"], "members": {"ExportDataSource": {"shape": "ExportDataSource", "documentation": "<p>The data source for the export job.</p>"}, "ExportDestination": {"shape": "ExportDestination", "documentation": "<p>The destination for the export job.</p>"}}, "documentation": "<p>Represents a request to create an export job from a data source to a data destination.</p>"}, "CreateExportJobResponse": {"type": "structure", "members": {"JobId": {"shape": "JobId", "documentation": "<p>A string that represents the export job ID.</p>"}}, "documentation": "<p>An HTTP 200 response if the request succeeds, or an error message if the request fails.</p>"}, "CreateImportJobRequest": {"type": "structure", "required": ["ImportDestination", "ImportDataSource"], "members": {"ImportDestination": {"shape": "ImportDestination", "documentation": "<p>The destination for the import job.</p>"}, "ImportDataSource": {"shape": "ImportDataSource", "documentation": "<p>The data source for the import job.</p>"}}, "documentation": "<p>Represents a request to create an import job from a data source for a data destination.</p>"}, "CreateImportJobResponse": {"type": "structure", "members": {"JobId": {"shape": "JobId", "documentation": "<p>A string that represents the import job ID.</p>"}}, "documentation": "<p>An HTTP 200 response if the request succeeds, or an error message if the request fails.</p>"}, "CreateMultiRegionEndpointRequest": {"type": "structure", "required": ["EndpointName", "Details"], "members": {"EndpointName": {"shape": "EndpointName", "documentation": "<p>The name of the multi-region endpoint (global-endpoint).</p>"}, "Details": {"shape": "Details", "documentation": "<p>Contains details of a multi-region endpoint (global-endpoint) being created.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>An array of objects that define the tags (keys and values) to associate with the multi-region endpoint (global-endpoint).</p>"}}, "documentation": "<p>Represents a request to create a multi-region endpoint (global-endpoint).</p>"}, "CreateMultiRegionEndpointResponse": {"type": "structure", "members": {"Status": {"shape": "Status", "documentation": "<p>A status of the multi-region endpoint (global-endpoint) right after the create request.</p> <ul> <li> <p> <code>CREATING</code> – The resource is being provisioned.</p> </li> <li> <p> <code>READY</code> – The resource is ready to use.</p> </li> <li> <p> <code>FAILED</code> – The resource failed to be provisioned.</p> </li> <li> <p> <code>DELETING</code> – The resource is being deleted as requested.</p> </li> </ul>"}, "EndpointId": {"shape": "EndpointId", "documentation": "<p>The ID of the multi-region endpoint (global-endpoint).</p>"}}, "documentation": "<p>An HTTP 200 response if the request succeeds, or an error message if the request fails.</p>"}, "CustomRedirectDomain": {"type": "string", "documentation": "<p>The domain to use for tracking open and click events.</p>"}, "CustomVerificationEmailTemplateMetadata": {"type": "structure", "members": {"TemplateName": {"shape": "EmailTemplateName", "documentation": "<p>The name of the custom verification email template.</p>"}, "FromEmailAddress": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The email address that the custom verification email is sent from.</p>"}, "TemplateSubject": {"shape": "EmailTemplateSubject", "documentation": "<p>The subject line of the custom verification email.</p>"}, "SuccessRedirectionURL": {"shape": "SuccessRedirectionURL", "documentation": "<p>The URL that the recipient of the verification email is sent to if his or her address is successfully verified.</p>"}, "FailureRedirectionURL": {"shape": "FailureRedirectionURL", "documentation": "<p>The URL that the recipient of the verification email is sent to if his or her address is not successfully verified.</p>"}}, "documentation": "<p>Contains information about a custom verification email template.</p>"}, "CustomVerificationEmailTemplatesList": {"type": "list", "member": {"shape": "CustomVerificationEmailTemplateMetadata"}, "documentation": "<p>A list of the custom verification email templates that exist in your account.</p>"}, "DailyVolume": {"type": "structure", "members": {"StartDate": {"shape": "Timestamp", "documentation": "<p>The date that the DailyVolume metrics apply to, in Unix time.</p>"}, "VolumeStatistics": {"shape": "VolumeStatistics", "documentation": "<p>An object that contains inbox placement metrics for a specific day in the analysis period.</p>"}, "DomainIspPlacements": {"shape": "DomainIspPlacements", "documentation": "<p>An object that contains inbox placement metrics for a specified day in the analysis period, broken out by the recipient's email provider.</p>"}}, "documentation": "<p>An object that contains information about the volume of email sent on each day of the analysis period.</p>"}, "DailyVolumes": {"type": "list", "member": {"shape": "DailyVolume"}}, "DashboardAttributes": {"type": "structure", "members": {"EngagementMetrics": {"shape": "FeatureStatus", "documentation": "<p>Specifies the status of your VDM engagement metrics collection. Can be one of the following:</p> <ul> <li> <p> <code>ENABLED</code> – Amazon SES enables engagement metrics for your account.</p> </li> <li> <p> <code>DISABLED</code> – Amazon SES disables engagement metrics for your account.</p> </li> </ul>"}}, "documentation": "<p>An object containing additional settings for your VDM configuration as applicable to the Dashboard.</p>"}, "DashboardOptions": {"type": "structure", "members": {"EngagementMetrics": {"shape": "FeatureStatus", "documentation": "<p>Specifies the status of your VDM engagement metrics collection. Can be one of the following:</p> <ul> <li> <p> <code>ENABLED</code> – Amazon SES enables engagement metrics for the configuration set.</p> </li> <li> <p> <code>DISABLED</code> – Amazon SES disables engagement metrics for the configuration set.</p> </li> </ul>"}}, "documentation": "<p>An object containing additional settings for your VDM configuration as applicable to the Dashboard.</p>"}, "DataFormat": {"type": "string", "documentation": "<p>The data format of a file, can be one of the following:</p> <ul> <li> <p> <code>CSV</code> – A comma-separated values file.</p> </li> <li> <p> <code>JSON</code> – A JSON file.</p> </li> </ul>", "enum": ["CSV", "JSON"]}, "DedicatedIp": {"type": "structure", "required": ["Ip", "WarmupStatus", "WarmupPercentage"], "members": {"Ip": {"shape": "Ip", "documentation": "<p>An IPv4 address.</p>"}, "WarmupStatus": {"shape": "WarmupStatus", "documentation": "<p>The warm-up status of a dedicated IP address. The status can have one of the following values:</p> <ul> <li> <p> <code>IN_PROGRESS</code> – The IP address isn't ready to use because the dedicated IP warm-up process is ongoing.</p> </li> <li> <p> <code>DONE</code> – The dedicated IP warm-up process is complete, and the IP address is ready to use.</p> </li> </ul>"}, "WarmupPercentage": {"shape": "Percentage100Wrapper", "documentation": "<p>Indicates how complete the dedicated IP warm-up process is. When this value equals 1, the address has completed the warm-up process and is ready for use.</p>"}, "PoolName": {"shape": "PoolName", "documentation": "<p>The name of the dedicated IP pool that the IP address is associated with.</p>"}}, "documentation": "<p>Contains information about a dedicated IP address that is associated with your Amazon SES account.</p> <p>To learn more about requesting dedicated IP addresses, see <a href=\"https://docs.aws.amazon.com/ses/latest/DeveloperGuide/dedicated-ip-case.html\">Requesting and Relinquishing Dedicated IP Addresses</a> in the <i>Amazon SES Developer Guide</i>.</p>"}, "DedicatedIpList": {"type": "list", "member": {"shape": "DedicatedIp"}, "documentation": "<p>A list of dedicated IP addresses that are associated with your Amazon Web Services account.</p>"}, "DedicatedIpPool": {"type": "structure", "required": ["PoolName", "ScalingMode"], "members": {"PoolName": {"shape": "PoolName", "documentation": "<p>The name of the dedicated IP pool.</p>"}, "ScalingMode": {"shape": "ScalingMode", "documentation": "<p>The type of the dedicated IP pool.</p> <ul> <li> <p> <code>STANDARD</code> – A dedicated IP pool where you can control which IPs are part of the pool.</p> </li> <li> <p> <code>MANAGED</code> – A dedicated IP pool where the reputation and number of IPs are automatically managed by Amazon SES.</p> </li> </ul>"}}, "documentation": "<p>Contains information about a dedicated IP pool.</p>"}, "DefaultDimensionValue": {"type": "string", "documentation": "<p>The default value of the dimension that is published to Amazon CloudWatch if you don't provide the value of the dimension when you send an email. This value has to meet the following criteria:</p> <ul> <li> <p>It can only contain ASCII letters (a–z, A–Z), numbers (0–9), underscores (_), or dashes (-).</p> </li> <li> <p>It can contain no more than 256 characters.</p> </li> </ul>"}, "DeleteConfigurationSetEventDestinationRequest": {"type": "structure", "required": ["ConfigurationSetName", "EventDestinationName"], "members": {"ConfigurationSetName": {"shape": "ConfigurationSetName", "documentation": "<p>The name of the configuration set that contains the event destination to delete.</p>", "location": "uri", "locationName": "ConfigurationSetName"}, "EventDestinationName": {"shape": "EventDestinationName", "documentation": "<p>The name of the event destination to delete.</p>", "location": "uri", "locationName": "EventDestinationName"}}, "documentation": "<p>A request to delete an event destination from a configuration set.</p>"}, "DeleteConfigurationSetEventDestinationResponse": {"type": "structure", "members": {}, "documentation": "<p>An HTTP 200 response if the request succeeds, or an error message if the request fails.</p>"}, "DeleteConfigurationSetRequest": {"type": "structure", "required": ["ConfigurationSetName"], "members": {"ConfigurationSetName": {"shape": "ConfigurationSetName", "documentation": "<p>The name of the configuration set.</p>", "location": "uri", "locationName": "ConfigurationSetName"}}, "documentation": "<p>A request to delete a configuration set.</p>"}, "DeleteConfigurationSetResponse": {"type": "structure", "members": {}, "documentation": "<p>An HTTP 200 response if the request succeeds, or an error message if the request fails.</p>"}, "DeleteContactListRequest": {"type": "structure", "required": ["ContactListName"], "members": {"ContactListName": {"shape": "ContactListName", "documentation": "<p>The name of the contact list.</p>", "location": "uri", "locationName": "ContactListName"}}}, "DeleteContactListResponse": {"type": "structure", "members": {}}, "DeleteContactRequest": {"type": "structure", "required": ["ContactListName", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "members": {"ContactListName": {"shape": "ContactListName", "documentation": "<p>The name of the contact list from which the contact should be removed.</p>", "location": "uri", "locationName": "ContactListName"}, "EmailAddress": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The contact's email address.</p>", "location": "uri", "locationName": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, "DeleteContactResponse": {"type": "structure", "members": {}}, "DeleteCustomVerificationEmailTemplateRequest": {"type": "structure", "required": ["TemplateName"], "members": {"TemplateName": {"shape": "EmailTemplateName", "documentation": "<p>The name of the custom verification email template that you want to delete.</p>", "location": "uri", "locationName": "TemplateName"}}, "documentation": "<p>Represents a request to delete an existing custom verification email template.</p>"}, "DeleteCustomVerificationEmailTemplateResponse": {"type": "structure", "members": {}, "documentation": "<p>If the action is successful, the service sends back an HTTP 200 response with an empty HTTP body.</p>"}, "DeleteDedicatedIpPoolRequest": {"type": "structure", "required": ["PoolName"], "members": {"PoolName": {"shape": "PoolName", "documentation": "<p>The name of the dedicated IP pool that you want to delete.</p>", "location": "uri", "locationName": "PoolName"}}, "documentation": "<p>A request to delete a dedicated IP pool.</p>"}, "DeleteDedicatedIpPoolResponse": {"type": "structure", "members": {}, "documentation": "<p>An HTTP 200 response if the request succeeds, or an error message if the request fails.</p>"}, "DeleteEmailIdentityPolicyRequest": {"type": "structure", "required": ["EmailIdentity", "PolicyName"], "members": {"EmailIdentity": {"shape": "Identity", "documentation": "<p>The email identity.</p>", "location": "uri", "locationName": "EmailIdentity"}, "PolicyName": {"shape": "PolicyName", "documentation": "<p>The name of the policy.</p> <p>The policy name cannot exceed 64 characters and can only include alphanumeric characters, dashes, and underscores.</p>", "location": "uri", "locationName": "PolicyName"}}, "documentation": "<p>Represents a request to delete a sending authorization policy for an identity. Sending authorization is an Amazon SES feature that enables you to authorize other senders to use your identities. For information, see the <a href=\"https://docs.aws.amazon.com/ses/latest/DeveloperGuide/sending-authorization-identity-owner-tasks-management.html\">Amazon SES Developer Guide</a>.</p>"}, "DeleteEmailIdentityPolicyResponse": {"type": "structure", "members": {}, "documentation": "<p>An HTTP 200 response if the request succeeds, or an error message if the request fails.</p>"}, "DeleteEmailIdentityRequest": {"type": "structure", "required": ["EmailIdentity"], "members": {"EmailIdentity": {"shape": "Identity", "documentation": "<p>The identity (that is, the email address or domain) to delete.</p>", "location": "uri", "locationName": "EmailIdentity"}}, "documentation": "<p>A request to delete an existing email identity. When you delete an identity, you lose the ability to send email from that identity. You can restore your ability to send email by completing the verification process for the identity again.</p>"}, "DeleteEmailIdentityResponse": {"type": "structure", "members": {}, "documentation": "<p>An HTTP 200 response if the request succeeds, or an error message if the request fails.</p>"}, "DeleteEmailTemplateRequest": {"type": "structure", "required": ["TemplateName"], "members": {"TemplateName": {"shape": "EmailTemplateName", "documentation": "<p>The name of the template to be deleted.</p>", "location": "uri", "locationName": "TemplateName"}}, "documentation": "<p>Represents a request to delete an email template. For more information, see the <a href=\"https://docs.aws.amazon.com/ses/latest/DeveloperGuide/send-personalized-email-api.html\">Amazon SES Developer Guide</a>.</p>"}, "DeleteEmailTemplateResponse": {"type": "structure", "members": {}, "documentation": "<p>If the action is successful, the service sends back an HTTP 200 response with an empty HTTP body.</p>"}, "DeleteMultiRegionEndpointRequest": {"type": "structure", "required": ["EndpointName"], "members": {"EndpointName": {"shape": "EndpointName", "documentation": "<p>The name of the multi-region endpoint (global-endpoint) to be deleted.</p>", "location": "uri", "locationName": "EndpointName"}}, "documentation": "<p>Represents a request to delete a multi-region endpoint (global-endpoint).</p>"}, "DeleteMultiRegionEndpointResponse": {"type": "structure", "members": {"Status": {"shape": "Status", "documentation": "<p>A status of the multi-region endpoint (global-endpoint) right after the delete request.</p> <ul> <li> <p> <code>CREATING</code> – The resource is being provisioned.</p> </li> <li> <p> <code>READY</code> – The resource is ready to use.</p> </li> <li> <p> <code>FAILED</code> – The resource failed to be provisioned.</p> </li> <li> <p> <code>DELETING</code> – The resource is being deleted as requested.</p> </li> </ul>"}}, "documentation": "<p>An HTTP 200 response if the request succeeds, or an error message if the request fails.</p>"}, "DeleteSuppressedDestinationRequest": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "members": {"EmailAddress": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The suppressed email destination to remove from the account suppression list.</p>", "location": "uri", "locationName": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "documentation": "<p>A request to remove an email address from the suppression list for your account.</p>"}, "DeleteSuppressedDestinationResponse": {"type": "structure", "members": {}, "documentation": "<p>An HTTP 200 response if the request succeeds, or an error message if the request fails.</p>"}, "DeliverabilityDashboardAccountStatus": {"type": "string", "documentation": "<p>The current status of your Deliverability dashboard subscription. If this value is <code>PENDING_EXPIRATION</code>, your subscription is scheduled to expire at the end of the current calendar month.</p>", "enum": ["ACTIVE", "PENDING_EXPIRATION", "DISABLED"]}, "DeliverabilityTestReport": {"type": "structure", "members": {"ReportId": {"shape": "ReportId", "documentation": "<p>A unique string that identifies the predictive inbox placement test.</p>"}, "ReportName": {"shape": "ReportName", "documentation": "<p>A name that helps you identify a predictive inbox placement test report.</p>"}, "Subject": {"shape": "DeliverabilityTestSubject", "documentation": "<p>The subject line for an email that you submitted in a predictive inbox placement test.</p>"}, "FromEmailAddress": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The sender address that you specified for the predictive inbox placement test.</p>"}, "CreateDate": {"shape": "Timestamp", "documentation": "<p>The date and time when the predictive inbox placement test was created.</p>"}, "DeliverabilityTestStatus": {"shape": "DeliverabilityTestStatus", "documentation": "<p>The status of the predictive inbox placement test. If the status is <code>IN_PROGRESS</code>, then the predictive inbox placement test is currently running. Predictive inbox placement tests are usually complete within 24 hours of creating the test. If the status is <code>COMPLETE</code>, then the test is finished, and you can use the <code>GetDeliverabilityTestReport</code> to view the results of the test.</p>"}}, "documentation": "<p>An object that contains metadata related to a predictive inbox placement test.</p>"}, "DeliverabilityTestReports": {"type": "list", "member": {"shape": "DeliverabilityTestReport"}}, "DeliverabilityTestStatus": {"type": "string", "documentation": "<p>The status of a predictive inbox placement test. If the status is <code>IN_PROGRESS</code>, then the predictive inbox placement test is currently running. Predictive inbox placement tests are usually complete within 24 hours of creating the test. If the status is <code>COMPLETE</code>, then the test is finished, and you can use the <code>GetDeliverabilityTestReport</code> operation to view the results of the test.</p>", "enum": ["IN_PROGRESS", "COMPLETED"]}, "DeliverabilityTestSubject": {"type": "string", "documentation": "<p>The subject line for an email that you submitted in a predictive inbox placement test.</p>"}, "DeliveryEventType": {"type": "string", "documentation": "<p>The type of delivery events:</p> <ul> <li> <p> <code>SEND</code> - The send request was successful and SES will attempt to deliver the message to the recipient’s mail server. (If account-level or global suppression is being used, SES will still count it as a send, but delivery is suppressed.)</p> </li> <li> <p> <code>DELIVERY</code> - <PERSON><PERSON> successfully delivered the email to the recipient's mail server. Excludes deliveries to the mailbox simulator and emails addressed to more than one recipient.</p> </li> <li> <p> <code>TRANSIENT_BOUNCE</code> - Feedback received for delivery failures excluding issues with non-existent mailboxes. Excludes bounces from the mailbox simulator, and those from emails addressed to more than one recipient.</p> </li> <li> <p> <code>PERMANENT_BOUNCE</code> - Feedback received for emails sent to non-existent mailboxes. Excludes bounces from the mailbox simulator, those originating from your account-level suppression list (if enabled), and those from emails addressed to more than one recipient.</p> </li> <li> <p> <code>UNDETERMINED_BOUNCE</code> - <PERSON><PERSON> was unable to determine the bounce reason.</p> </li> <li> <p> <code>COMPLAINT</code> - Complaint received for the email. This excludes complaints from the mailbox simulator, those originating from your account-level suppression list (if enabled), and those from emails addressed to more than one recipient.</p> </li> </ul>", "enum": ["SEND", "DELIVERY", "TRANSIENT_BOUNCE", "PERMANENT_BOUNCE", "UNDETERMINED_BOUNCE", "COMPLAINT"]}, "DeliveryOptions": {"type": "structure", "members": {"TlsPolicy": {"shape": "TlsPolicy", "documentation": "<p>Specifies whether messages that use the configuration set are required to use Transport Layer Security (TLS). If the value is <code>Require</code>, messages are only delivered if a TLS connection can be established. If the value is <code>Optional</code>, messages can be delivered in plain text if a TLS connection can't be established.</p>"}, "SendingPoolName": {"shape": "PoolName", "documentation": "<p>The name of the dedicated IP pool to associate with the configuration set.</p>"}, "MaxDeliverySeconds": {"shape": "MaxDeliverySeconds", "documentation": "<p>The maximum amount of time, in seconds, that Amazon SES API v2 will attempt delivery of email. If specified, the value must greater than or equal to 300 seconds (5 minutes) and less than or equal to 50400 seconds (840 minutes). </p>"}}, "documentation": "<p>Used to associate a configuration set with a dedicated IP pool.</p>"}, "Description": {"type": "string"}, "Destination": {"type": "structure", "members": {"ToAddresses": {"shape": "EmailAddressList", "documentation": "<p>An array that contains the email addresses of the \"To\" recipients for the email.</p>"}, "CcAddresses": {"shape": "EmailAddressList", "documentation": "<p>An array that contains the email addresses of the \"CC\" (carbon copy) recipients for the email.</p>"}, "BccAddresses": {"shape": "EmailAddressList", "documentation": "<p>An array that contains the email addresses of the \"BCC\" (blind carbon copy) recipients for the email.</p>"}}, "documentation": "<p>An object that describes the recipients for an email.</p> <note> <p>Amazon SES does not support the SMTPUTF8 extension, as described in <a href=\"https://tools.ietf.org/html/rfc6531\">RFC6531</a>. For this reason, the <i>local part</i> of a destination email address (the part of the email address that precedes the @ sign) may only contain <a href=\"https://en.wikipedia.org/wiki/Email_address#Local-part\">7-bit ASCII characters</a>. If the <i>domain part</i> of an address (the part after the @ sign) contains non-ASCII characters, they must be encoded using Punycode, as described in <a href=\"https://tools.ietf.org/html/rfc3492.html\">RFC3492</a>.</p> </note>"}, "Details": {"type": "structure", "required": ["RoutesDetails"], "members": {"RoutesDetails": {"shape": "RoutesDetails", "documentation": "<p>A list of route configuration details. Must contain exactly one route configuration.</p>"}}, "documentation": "<p>An object that contains configuration details of multi-region endpoint (global-endpoint).</p>"}, "DiagnosticCode": {"type": "string"}, "DimensionName": {"type": "string", "documentation": "<p>The name of an Amazon CloudWatch dimension associated with an email sending metric. The name has to meet the following criteria:</p> <ul> <li> <p>It can only contain ASCII letters (a-z, A-Z), numbers (0-9), underscores (_), or dashes (-).</p> </li> <li> <p>It can contain no more than 256 characters.</p> </li> </ul>"}, "DimensionValueSource": {"type": "string", "documentation": "<p>The location where the Amazon SES API v2 finds the value of a dimension to publish to Amazon CloudWatch. To use the message tags that you specify using an <code>X-SES-MESSAGE-TAGS</code> header or a parameter to the <code>SendEmail</code> or <code>SendRawEmail</code> API, choose <code>messageTag</code>. To use your own email headers, choose <code>emailHeader</code>. To use link tags, choose <code>linkTags</code>.</p>", "enum": ["MESSAGE_TAG", "EMAIL_HEADER", "LINK_TAG"]}, "Dimensions": {"type": "map", "key": {"shape": "MetricDimensionName"}, "value": {"shape": "MetricDimensionValue"}, "max": 3, "min": 1}, "DisplayName": {"type": "string"}, "DkimAttributes": {"type": "structure", "members": {"SigningEnabled": {"shape": "Enabled", "documentation": "<p>If the value is <code>true</code>, then the messages that you send from the identity are signed using DKIM. If the value is <code>false</code>, then the messages that you send from the identity aren't DKIM-signed.</p>"}, "Status": {"shape": "DkimStatus", "documentation": "<p>Describes whether or not Amazon SES has successfully located the DKIM records in the DNS records for the domain. The status can be one of the following:</p> <ul> <li> <p> <code>PENDING</code> – The verification process was initiated, but Amazon SES hasn't yet detected the DKIM records in the DNS configuration for the domain.</p> </li> <li> <p> <code>SUCCESS</code> – The verification process completed successfully.</p> </li> <li> <p> <code>FAILED</code> – The verification process failed. This typically occurs when Amazon SES fails to find the DKIM records in the DNS configuration of the domain.</p> </li> <li> <p> <code>TEMPORARY_FAILURE</code> – A temporary issue is preventing Amazon SES from determining the DKIM authentication status of the domain.</p> </li> <li> <p> <code>NOT_STARTED</code> – The DKIM verification process hasn't been initiated for the domain.</p> </li> </ul>"}, "Tokens": {"shape": "DnsTokenList", "documentation": "<p>If you used <a href=\"https://docs.aws.amazon.com/ses/latest/DeveloperGuide/easy-dkim.html\">Easy DKIM</a> to configure DKIM authentication for the domain, then this object contains a set of unique strings that you use to create a set of CNAME records that you add to the DNS configuration for your domain. When Amazon SES detects these records in the DNS configuration for your domain, the DKIM authentication process is complete.</p> <p>If you configured DKIM authentication for the domain by providing your own public-private key pair, then this object contains the selector for the public key.</p> <p>Regardless of the DKIM authentication method you use, Amazon SES searches for the appropriate records in the DNS configuration of the domain for up to 72 hours.</p>"}, "SigningAttributesOrigin": {"shape": "DkimSigningAttributesOrigin", "documentation": "<p>A string that indicates how DKIM was configured for the identity. These are the possible values:</p> <ul> <li> <p> <code>AWS_SES</code> – Indicates that DKIM was configured for the identity by using <a href=\"https://docs.aws.amazon.com/ses/latest/DeveloperGuide/easy-dkim.html\">Easy DKIM</a>.</p> </li> <li> <p> <code>EXTERNAL</code> – Indicates that DKIM was configured for the identity by using Bring Your Own DKIM (BYODKIM).</p> </li> <li> <p> <code>AWS_SES_AF_SOUTH_1</code> – Indicates that DKIM was configured for the identity by replicating signing attributes from a parent identity in Africa (Cape Town) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_EU_NORTH_1</code> – Indicates that DKIM was configured for the identity by replicating signing attributes from a parent identity in Europe (Stockholm) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_AP_SOUTH_1</code> – Indicates that DKIM was configured for the identity by replicating signing attributes from a parent identity in Asia Pacific (Mumbai) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_AP_SOUTH_2</code> – Indicates that DKIM was configured for the identity by replicating signing attributes from a parent identity in Asia Pacific (Hyderabad) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_EU_WEST_3</code> – Indicates that DKIM was configured for the identity by replicating signing attributes from a parent identity in Europe (Paris) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_EU_WEST_2</code> – Indicates that DKIM was configured for the identity by replicating signing attributes from a parent identity in Europe (London) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_EU_SOUTH_1</code> – Indicates that DKIM was configured for the identity by replicating signing attributes from a parent identity in Europe (Milan) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_EU_WEST_1</code> – Indicates that DKIM was configured for the identity by replicating signing attributes from a parent identity in Europe (Ireland) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_AP_NORTHEAST_3</code> – Indicates that DKIM was configured for the identity by replicating signing attributes from a parent identity in Asia Pacific (Osaka) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_AP_NORTHEAST_2</code> – Indicates that DKIM was configured for the identity by replicating signing attributes from a parent identity in Asia Pacific (Seoul) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_ME_CENTRAL_1</code> – Indicates that DKIM was configured for the identity by replicating signing attributes from a parent identity in Middle East (UAE) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_ME_SOUTH_1</code> – Indicates that DKIM was configured for the identity by replicating signing attributes from a parent identity in Middle East (Bahrain) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_AP_NORTHEAST_1</code> – Indicates that DKIM was configured for the identity by replicating signing attributes from a parent identity in Asia Pacific (Tokyo) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_IL_CENTRAL_1</code> – Indicates that DKIM was configured for the identity by replicating signing attributes from a parent identity in Israel (Tel Aviv) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_SA_EAST_1</code> – Indicates that DKIM was configured for the identity by replicating signing attributes from a parent identity in South America (São Paulo) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_CA_CENTRAL_1</code> – Indicates that DKIM was configured for the identity by replicating signing attributes from a parent identity in Canada (Central) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_AP_SOUTHEAST_1</code> – Indicates that DKIM was configured for the identity by replicating signing attributes from a parent identity in Asia Pacific (Singapore) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_AP_SOUTHEAST_2</code> – Indicates that DKIM was configured for the identity by replicating signing attributes from a parent identity in Asia Pacific (Sydney) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_AP_SOUTHEAST_3</code> – Indicates that DKIM was configured for the identity by replicating signing attributes from a parent identity in Asia Pacific (Jakarta) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_EU_CENTRAL_1</code> – Indicates that DKIM was configured for the identity by replicating signing attributes from a parent identity in Europe (Frankfurt) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_EU_CENTRAL_2</code> – Indicates that DKIM was configured for the identity by replicating signing attributes from a parent identity in Europe (Zurich) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_US_EAST_1</code> – Indicates that DKIM was configured for the identity by replicating signing attributes from a parent identity in US East (N. Virginia) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_US_EAST_2</code> – Indicates that DKIM was configured for the identity by replicating signing attributes from a parent identity in US East (Ohio) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_US_WEST_1</code> – Indicates that DKIM was configured for the identity by replicating signing attributes from a parent identity in US West (N. California) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_US_WEST_2</code> – Indicates that DKIM was configured for the identity by replicating signing attributes from a parent identity in US West (Oregon) region using Deterministic Easy-DKIM (DEED). </p> </li> </ul>"}, "NextSigningKeyLength": {"shape": "DkimSigningKeyLength", "documentation": "<p>[Easy DKIM] The key length of the future DKIM key pair to be generated. This can be changed at most once per day.</p>"}, "CurrentSigningKeyLength": {"shape": "DkimSigningKeyLength", "documentation": "<p>[Easy DKIM] The key length of the DKIM key pair in use.</p>"}, "LastKeyGenerationTimestamp": {"shape": "Timestamp", "documentation": "<p>[Easy DKIM] The last time a key pair was generated for this identity.</p>"}}, "documentation": "<p>An object that contains information about the DKIM authentication status for an email identity.</p> <p>Amazon SES determines the authentication status by searching for specific records in the DNS configuration for the domain. If you used <a href=\"https://docs.aws.amazon.com/ses/latest/DeveloperGuide/easy-dkim.html\">Easy DKIM</a> to set up DKIM authentication, Amazon SES tries to find three unique CNAME records in the DNS configuration for your domain. If you provided a public key to perform DKIM authentication, Amazon SES tries to find a TXT record that uses the selector that you specified. The value of the TXT record must be a public key that's paired with the private key that you specified in the process of creating the identity</p>"}, "DkimSigningAttributes": {"type": "structure", "members": {"DomainSigningSelector": {"shape": "Selector", "documentation": "<p>[Bring Your Own DKIM] A string that's used to identify a public key in the DNS configuration for a domain.</p>"}, "DomainSigningPrivateKey": {"shape": "Private<PERSON><PERSON>", "documentation": "<p>[Bring Your Own DKIM] A private key that's used to generate a DKIM signature.</p> <p>The private key must use 1024 or 2048-bit RSA encryption, and must be encoded using base64 encoding.</p>"}, "NextSigningKeyLength": {"shape": "DkimSigningKeyLength", "documentation": "<p>[Easy DKIM] The key length of the future DKIM key pair to be generated. This can be changed at most once per day.</p>"}, "DomainSigningAttributesOrigin": {"shape": "DkimSigningAttributesOrigin", "documentation": "<p>The attribute to use for configuring DKIM for the identity depends on the operation: </p> <ol> <li> <p>For <code>PutEmailIdentityDkimSigningAttributes</code>: </p> <ul> <li> <p>None of the values are allowed - use the <a href=\"https://docs.aws.amazon.com/ses/latest/APIReference-V2/API_PutEmailIdentityDkimSigningAttributes.html#SES-PutEmailIdentityDkimSigningAttributes-request-SigningAttributesOrigin\"> <code>SigningAttributesOrigin</code> </a> parameter instead </p> </li> </ul> </li> <li> <p>For <code>CreateEmailIdentity</code> when replicating a parent identity's DKIM configuration: </p> <ul> <li> <p>Allowed values: All values except <code>AWS_SES</code> and <code>EXTERNAL</code> </p> </li> </ul> </li> </ol> <ul> <li> <p> <code>AWS_SES</code> – Configure DKIM for the identity by using Easy DKIM. </p> </li> <li> <p> <code>EXTERNAL</code> – Configure DKIM for the identity by using Bring Your Own DKIM (BYODKIM). </p> </li> <li> <p> <code>AWS_SES_AF_SOUTH_1</code> – Configure DKIM for the identity by replicating from a parent identity in Africa (Cape Town) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_EU_NORTH_1</code> – Configure DKIM for the identity by replicating from a parent identity in Europe (Stockholm) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_AP_SOUTH_1</code> – Configure DKIM for the identity by replicating from a parent identity in Asia Pacific (Mumbai) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_AP_SOUTH_2</code> – Configure DKIM for the identity by replicating from a parent identity in Asia Pacific (Hyderabad) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_EU_WEST_3</code> – Configure DKIM for the identity by replicating from a parent identity in Europe (Paris) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_EU_WEST_2</code> – Configure DKIM for the identity by replicating from a parent identity in Europe (London) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_EU_SOUTH_1</code> – Configure DKIM for the identity by replicating from a parent identity in Europe (Milan) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_EU_WEST_1</code> – Configure DKIM for the identity by replicating from a parent identity in Europe (Ireland) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_AP_NORTHEAST_3</code> – Configure DKIM for the identity by replicating from a parent identity in Asia Pacific (Osaka) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_AP_NORTHEAST_2</code> – Configure DKIM for the identity by replicating from a parent identity in Asia Pacific (Seoul) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_ME_CENTRAL_1</code> – Configure DKIM for the identity by replicating from a parent identity in Middle East (UAE) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_ME_SOUTH_1</code> – Configure DKIM for the identity by replicating from a parent identity in Middle East (Bahrain) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_AP_NORTHEAST_1</code> – Configure DKIM for the identity by replicating from a parent identity in Asia Pacific (Tokyo) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_IL_CENTRAL_1</code> – Configure DKIM for the identity by replicating from a parent identity in Israel (Tel Aviv) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_SA_EAST_1</code> – Configure DKIM for the identity by replicating from a parent identity in South America (São Paulo) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_CA_CENTRAL_1</code> – Configure DKIM for the identity by replicating from a parent identity in Canada (Central) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_AP_SOUTHEAST_1</code> – Configure DKIM for the identity by replicating from a parent identity in Asia Pacific (Singapore) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_AP_SOUTHEAST_2</code> – Configure DKIM for the identity by replicating from a parent identity in Asia Pacific (Sydney) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_AP_SOUTHEAST_3</code> – Configure DKIM for the identity by replicating from a parent identity in Asia Pacific (Jakarta) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_EU_CENTRAL_1</code> – Configure DKIM for the identity by replicating from a parent identity in Europe (Frankfurt) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_EU_CENTRAL_2</code> – Configure DKIM for the identity by replicating from a parent identity in Europe (Zurich) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_US_EAST_1</code> – Configure DKIM for the identity by replicating from a parent identity in US East (N. Virginia) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_US_EAST_2</code> – Configure DKIM for the identity by replicating from a parent identity in US East (Ohio) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_US_WEST_1</code> – Configure DKIM for the identity by replicating from a parent identity in US West (N. California) region using Deterministic Easy-DKIM (DEED). </p> </li> <li> <p> <code>AWS_SES_US_WEST_2</code> – Configure DKIM for the identity by replicating from a parent identity in US West (Oregon) region using Deterministic Easy-DKIM (DEED). </p> </li> </ul>"}}, "documentation": "<p>An object that contains configuration for Bring Your Own DKIM (BYODKIM), or, for Easy DKIM</p>"}, "DkimSigningAttributesOrigin": {"type": "string", "enum": ["AWS_SES", "EXTERNAL", "AWS_SES_AF_SOUTH_1", "AWS_SES_EU_NORTH_1", "AWS_SES_AP_SOUTH_1", "AWS_SES_EU_WEST_3", "AWS_SES_EU_WEST_2", "AWS_SES_EU_SOUTH_1", "AWS_SES_EU_WEST_1", "AWS_SES_AP_NORTHEAST_3", "AWS_SES_AP_NORTHEAST_2", "AWS_SES_ME_SOUTH_1", "AWS_SES_AP_NORTHEAST_1", "AWS_SES_IL_CENTRAL_1", "AWS_SES_SA_EAST_1", "AWS_SES_CA_CENTRAL_1", "AWS_SES_AP_SOUTHEAST_1", "AWS_SES_AP_SOUTHEAST_2", "AWS_SES_AP_SOUTHEAST_3", "AWS_SES_EU_CENTRAL_1", "AWS_SES_US_EAST_1", "AWS_SES_US_EAST_2", "AWS_SES_US_WEST_1", "AWS_SES_US_WEST_2", "AWS_SES_ME_CENTRAL_1", "AWS_SES_AP_SOUTH_2", "AWS_SES_EU_CENTRAL_2"]}, "DkimSigningKeyLength": {"type": "string", "enum": ["RSA_1024_BIT", "RSA_2048_BIT"]}, "DkimStatus": {"type": "string", "documentation": "<p>The DKIM authentication status of the identity. The status can be one of the following:</p> <ul> <li> <p> <code>PENDING</code> – The verification process was initiated, but Amazon SES hasn't yet detected the DKIM records in the DNS configuration for the domain.</p> </li> <li> <p> <code>SUCCESS</code> – The verification process completed successfully.</p> </li> <li> <p> <code>FAILED</code> – The verification process failed. This typically occurs when Amazon SES fails to find the DKIM records in the DNS configuration of the domain.</p> </li> <li> <p> <code>TEMPORARY_FAILURE</code> – A temporary issue is preventing Amazon SES from determining the DKIM authentication status of the domain.</p> </li> <li> <p> <code>NOT_STARTED</code> – The DKIM verification process hasn't been initiated for the domain.</p> </li> </ul>", "enum": ["PENDING", "SUCCESS", "FAILED", "TEMPORARY_FAILURE", "NOT_STARTED"]}, "DnsToken": {"type": "string"}, "DnsTokenList": {"type": "list", "member": {"shape": "DnsToken"}}, "Domain": {"type": "string"}, "DomainDeliverabilityCampaign": {"type": "structure", "members": {"CampaignId": {"shape": "CampaignId", "documentation": "<p>The unique identifier for the campaign. The Deliverability dashboard automatically generates and assigns this identifier to a campaign.</p>"}, "ImageUrl": {"shape": "ImageUrl", "documentation": "<p>The URL of an image that contains a snapshot of the email message that was sent.</p>"}, "Subject": {"shape": "Subject", "documentation": "<p>The subject line, or title, of the email message.</p>"}, "FromAddress": {"shape": "Identity", "documentation": "<p>The verified email address that the email message was sent from.</p>"}, "SendingIps": {"shape": "IpList", "documentation": "<p>The IP addresses that were used to send the email message.</p>"}, "FirstSeenDateTime": {"shape": "Timestamp", "documentation": "<p>The first time when the email message was delivered to any recipient's inbox. This value can help you determine how long it took for a campaign to deliver an email message.</p>"}, "LastSeenDateTime": {"shape": "Timestamp", "documentation": "<p>The last time when the email message was delivered to any recipient's inbox. This value can help you determine how long it took for a campaign to deliver an email message.</p>"}, "InboxCount": {"shape": "Volume", "documentation": "<p>The number of email messages that were delivered to recipients’ inboxes.</p>"}, "SpamCount": {"shape": "Volume", "documentation": "<p>The number of email messages that were delivered to recipients' spam or junk mail folders.</p>"}, "ReadRate": {"shape": "Percentage", "documentation": "<p>The percentage of email messages that were opened by recipients. Due to technical limitations, this value only includes recipients who opened the message by using an email client that supports images.</p>"}, "DeleteRate": {"shape": "Percentage", "documentation": "<p>The percentage of email messages that were deleted by recipients, without being opened first. Due to technical limitations, this value only includes recipients who opened the message by using an email client that supports images.</p>"}, "ReadDeleteRate": {"shape": "Percentage", "documentation": "<p>The percentage of email messages that were opened and then deleted by recipients. Due to technical limitations, this value only includes recipients who opened the message by using an email client that supports images.</p>"}, "ProjectedVolume": {"shape": "Volume", "documentation": "<p>The projected number of recipients that the email message was sent to.</p>"}, "Esps": {"shape": "Esps", "documentation": "<p>The major email providers who handled the email message.</p>"}}, "documentation": "<p>An object that contains the deliverability data for a specific campaign. This data is available for a campaign only if the campaign sent email by using a domain that the Deliverability dashboard is enabled for (<code>PutDeliverabilityDashboardOption</code> operation).</p>"}, "DomainDeliverabilityCampaignList": {"type": "list", "member": {"shape": "DomainDeliverabilityCampaign"}, "documentation": "<p/>"}, "DomainDeliverabilityTrackingOption": {"type": "structure", "members": {"Domain": {"shape": "Domain", "documentation": "<p>A verified domain that’s associated with your Amazon Web Services account and currently has an active Deliverability dashboard subscription.</p>"}, "SubscriptionStartDate": {"shape": "Timestamp", "documentation": "<p>The date when you enabled the Deliverability dashboard for the domain.</p>"}, "InboxPlacementTrackingOption": {"shape": "InboxPlacementTrackingOption", "documentation": "<p>An object that contains information about the inbox placement data settings for the domain.</p>"}}, "documentation": "<p>An object that contains information about the Deliverability dashboard subscription for a verified domain that you use to send email and currently has an active Deliverability dashboard subscription. If a Deliverability dashboard subscription is active for a domain, you gain access to reputation, inbox placement, and other metrics for the domain.</p>"}, "DomainDeliverabilityTrackingOptions": {"type": "list", "member": {"shape": "DomainDeliverabilityTrackingOption"}, "documentation": "<p>An object that contains information about the Deliverability dashboard subscription for a verified domain that you use to send email and currently has an active Deliverability dashboard subscription. If a Deliverability dashboard subscription is active for a domain, you gain access to reputation, inbox placement, and other metrics for the domain.</p>"}, "DomainIspPlacement": {"type": "structure", "members": {"IspName": {"shape": "IspName", "documentation": "<p>The name of the email provider that the inbox placement data applies to.</p>"}, "InboxRawCount": {"shape": "Volume", "documentation": "<p>The total number of messages that were sent from the selected domain to the specified email provider that arrived in recipients' inboxes.</p>"}, "SpamRawCount": {"shape": "Volume", "documentation": "<p>The total number of messages that were sent from the selected domain to the specified email provider that arrived in recipients' spam or junk mail folders.</p>"}, "InboxPercentage": {"shape": "Percentage", "documentation": "<p>The percentage of messages that were sent from the selected domain to the specified email provider that arrived in recipients' inboxes.</p>"}, "SpamPercentage": {"shape": "Percentage", "documentation": "<p>The percentage of messages that were sent from the selected domain to the specified email provider that arrived in recipients' spam or junk mail folders.</p>"}}, "documentation": "<p>An object that contains inbox placement data for email sent from one of your email domains to a specific email provider.</p>"}, "DomainIspPlacements": {"type": "list", "member": {"shape": "DomainIspPlacement"}}, "EmailAddress": {"type": "string"}, "EmailAddressFilterList": {"type": "list", "member": {"shape": "InsightsEmailAddress"}, "max": 5}, "EmailAddressList": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "EmailContent": {"type": "structure", "members": {"Simple": {"shape": "Message", "documentation": "<p>The simple email message. The message consists of a subject, message body and attachments list.</p>"}, "Raw": {"shape": "RawMessage", "documentation": "<p>The raw email message. The message has to meet the following criteria:</p> <ul> <li> <p>The message has to contain a header and a body, separated by one blank line.</p> </li> <li> <p>All of the required header fields must be present in the message.</p> </li> <li> <p>Each part of a multipart MIME message must be formatted properly.</p> </li> <li> <p>If you include attachments, they must be in a file format that the Amazon SES API v2 supports. </p> </li> <li> <p>The raw data of the message needs to base64-encoded if you are accessing Amazon SES directly through the HTTPS interface. If you are accessing Amazon SES using an Amazon Web Services SDK, the SDK takes care of the base 64-encoding for you.</p> </li> <li> <p>If any of the MIME parts in your message contain content that is outside of the 7-bit ASCII character range, you should encode that content to ensure that recipients' email clients render the message properly.</p> </li> <li> <p>The length of any single line of text in the message can't exceed 1,000 characters. This restriction is defined in <a href=\"https://tools.ietf.org/html/rfc5321\">RFC 5321</a>.</p> </li> </ul>"}, "Template": {"shape": "Template", "documentation": "<p>The template to use for the email message.</p>"}}, "documentation": "<p>An object that defines the entire content of the email, including the message headers, body content, and attachments. For a simple email message, you specify the subject and provide both text and HTML versions of the message body. You can also add attachments to simple and templated messages. For a raw message, you provide a complete MIME-formatted message, which can include custom headers and attachments.</p>"}, "EmailInsights": {"type": "structure", "members": {"Destination": {"shape": "InsightsEmailAddress", "documentation": "<p>The recipient of the email.</p>"}, "Isp": {"shape": "Isp", "documentation": "<p>The recipient's ISP (e.g., <code>Gmail</code>, <code>Yahoo</code>, etc.).</p>"}, "Events": {"shape": "InsightsEvents", "documentation": "<p>A list of events associated with the sent email.</p>"}}, "documentation": "<p>An email's insights contain metadata and delivery information about a specific email.</p>"}, "EmailInsightsList": {"type": "list", "member": {"shape": "EmailInsights"}}, "EmailSubject": {"type": "string", "max": 998, "min": 1, "sensitive": true}, "EmailSubjectFilterList": {"type": "list", "member": {"shape": "EmailSubject"}, "max": 1}, "EmailTemplateContent": {"type": "structure", "members": {"Subject": {"shape": "EmailTemplateSubject", "documentation": "<p>The subject line of the email.</p>"}, "Text": {"shape": "EmailTemplateText", "documentation": "<p>The email body that will be visible to recipients whose email clients do not display HTML.</p>"}, "Html": {"shape": "EmailTemplateHtml", "documentation": "<p>The HTML body of the email.</p>"}}, "documentation": "<p>The content of the email, composed of a subject line, an HTML part, and a text-only part.</p>"}, "EmailTemplateData": {"type": "string", "documentation": "<p>An object that defines the values to use for message variables in the template. This object is a set of key-value pairs. Each key defines a message variable in the template. The corresponding value defines the value to use for that variable.</p>", "max": 262144}, "EmailTemplateHtml": {"type": "string", "documentation": "<p>The HTML body of the email.</p>"}, "EmailTemplateMetadata": {"type": "structure", "members": {"TemplateName": {"shape": "EmailTemplateName", "documentation": "<p>The name of the template.</p>"}, "CreatedTimestamp": {"shape": "Timestamp", "documentation": "<p>The time and date the template was created.</p>"}}, "documentation": "<p>Contains information about an email template.</p>"}, "EmailTemplateMetadataList": {"type": "list", "member": {"shape": "EmailTemplateMetadata"}, "documentation": "<p>A list of the EmailTemplateMetadata object.</p>"}, "EmailTemplateName": {"type": "string", "documentation": "<p>The name of the template. You will refer to this name when you send email using the <code>SendEmail</code> or <code>SendBulkEmail</code> operations.</p>", "min": 1}, "EmailTemplateSubject": {"type": "string", "documentation": "<p>The subject line of the email.</p>"}, "EmailTemplateText": {"type": "string", "documentation": "<p>The email body that will be visible to recipients whose email clients do not display HTML.</p>"}, "Enabled": {"type": "boolean"}, "EnabledWrapper": {"type": "boolean"}, "EndpointId": {"type": "string", "documentation": "<p>The ID of the multi-region endpoint (global-endpoint).</p>"}, "EndpointName": {"type": "string", "documentation": "<p>The name of the multi-region endpoint (global-endpoint).</p>", "max": 64, "min": 1, "pattern": "^[\\w\\-_]+$"}, "EngagementEventType": {"type": "string", "documentation": "<p>The type of delivery events:</p> <ul> <li> <p> <code>OPEN</code> - Open event for emails including open trackers. Excludes opens for emails addressed to more than one recipient.</p> </li> <li> <p> <code>CLICK</code> - Click event for emails including wrapped links. Excludes clicks for emails addressed to more than one recipient.</p> </li> </ul>", "enum": ["OPEN", "CLICK"]}, "ErrorMessage": {"type": "string"}, "Esp": {"type": "string"}, "Esps": {"type": "list", "member": {"shape": "Esp"}}, "EventBridgeDestination": {"type": "structure", "required": ["EventBusArn"], "members": {"EventBusArn": {"shape": "AmazonResourceName", "documentation": "<p>The Amazon Resource Name (ARN) of the Amazon EventBridge bus to publish email events to. Only the default bus is supported. </p>"}}, "documentation": "<p>An object that defines an Amazon EventBridge destination for email events. You can use Amazon EventBridge to send notifications when certain email events occur.</p>"}, "EventDestination": {"type": "structure", "required": ["Name", "MatchingEventTypes"], "members": {"Name": {"shape": "EventDestinationName", "documentation": "<p>A name that identifies the event destination.</p>"}, "Enabled": {"shape": "Enabled", "documentation": "<p>If <code>true</code>, the event destination is enabled. When the event destination is enabled, the specified event types are sent to the destinations in this <code>EventDestinationDefinition</code>.</p> <p>If <code>false</code>, the event destination is disabled. When the event destination is disabled, events aren't sent to the specified destinations.</p>"}, "MatchingEventTypes": {"shape": "EventTypes", "documentation": "<p>The types of events that Amazon SES sends to the specified event destinations.</p> <ul> <li> <p> <code>SEND</code> - The send request was successful and SES will attempt to deliver the message to the recipient’s mail server. (If account-level or global suppression is being used, SES will still count it as a send, but delivery is suppressed.)</p> </li> <li> <p> <code>REJECT</code> - SES accepted the email, but determined that it contained a virus and didn’t attempt to deliver it to the recipient’s mail server.</p> </li> <li> <p> <code>BOUNCE</code> - (<i>Hard bounce</i>) The recipient's mail server permanently rejected the email. (<i>Soft bounces</i> are only included when SES fails to deliver the email after retrying for a period of time.)</p> </li> <li> <p> <code>COMPLAINT</code> - The email was successfully delivered to the recipient’s mail server, but the recipient marked it as spam.</p> </li> <li> <p> <code>DELIVERY</code> - SES successfully delivered the email to the recipient's mail server.</p> </li> <li> <p> <code>OPEN</code> - The recipient received the message and opened it in their email client.</p> </li> <li> <p> <code>CLICK</code> - The recipient clicked one or more links in the email.</p> </li> <li> <p> <code>RENDERING_FAILURE</code> - The email wasn't sent because of a template rendering issue. This event type can occur when template data is missing, or when there is a mismatch between template parameters and data. (This event type only occurs when you send email using the <a href=\"https://docs.aws.amazon.com/ses/latest/APIReference-V2/API_SendEmail.html\"> <code>SendEmail</code> </a> or <a href=\"https://docs.aws.amazon.com/ses/latest/APIReference-V2/API_SendBulkEmail.html\"> <code>SendBulkEmail</code> </a> API operations.) </p> </li> <li> <p> <code>DELIVERY_DELAY</code> - The email couldn't be delivered to the recipient’s mail server because a temporary issue occurred. Delivery delays can occur, for example, when the recipient's inbox is full, or when the receiving email server experiences a transient issue.</p> </li> <li> <p> <code>SUBSCRIPTION</code> - The email was successfully delivered, but the recipient updated their subscription preferences by clicking on an <i>unsubscribe</i> link as part of your <a href=\"https://docs.aws.amazon.com/ses/latest/dg/sending-email-subscription-management.html\">subscription management</a>.</p> </li> </ul>"}, "KinesisFirehoseDestination": {"shape": "KinesisFirehoseDestination", "documentation": "<p>An object that defines an Amazon Kinesis Data Firehose destination for email events. You can use Amazon Kinesis Data Firehose to stream data to other services, such as Amazon S3 and Amazon Redshift.</p>"}, "CloudWatchDestination": {"shape": "CloudWatchDestination", "documentation": "<p>An object that defines an Amazon CloudWatch destination for email events. You can use Amazon CloudWatch to monitor and gain insights on your email sending metrics.</p>"}, "SnsDestination": {"shape": "SnsDestination", "documentation": "<p>An object that defines an Amazon SNS destination for email events. You can use Amazon SNS to send notifications when certain email events occur.</p>"}, "EventBridgeDestination": {"shape": "EventBridgeDestination", "documentation": "<p>An object that defines an Amazon EventBridge destination for email events. You can use Amazon EventBridge to send notifications when certain email events occur.</p>"}, "PinpointDestination": {"shape": "PinpointDestination", "documentation": "<p>An object that defines an Amazon Pinpoint project destination for email events. You can send email event data to a Amazon Pinpoint project to view metrics using the Transactional Messaging dashboards that are built in to Amazon Pinpoint. For more information, see <a href=\"https://docs.aws.amazon.com/pinpoint/latest/userguide/analytics-transactional-messages.html\">Transactional Messaging Charts</a> in the <i>Amazon Pinpoint User Guide</i>.</p>"}}, "documentation": "<p>In the Amazon SES API v2, <i>events</i> include message sends, deliveries, opens, clicks, bounces, complaints and delivery delays. <i>Event destinations</i> are places that you can send information about these events to. For example, you can send event data to Amazon SNS to receive notifications when you receive bounces or complaints, or you can use Amazon Kinesis Data Firehose to stream data to Amazon S3 for long-term storage.</p>"}, "EventDestinationDefinition": {"type": "structure", "members": {"Enabled": {"shape": "Enabled", "documentation": "<p>If <code>true</code>, the event destination is enabled. When the event destination is enabled, the specified event types are sent to the destinations in this <code>EventDestinationDefinition</code>.</p> <p>If <code>false</code>, the event destination is disabled. When the event destination is disabled, events aren't sent to the specified destinations.</p>"}, "MatchingEventTypes": {"shape": "EventTypes", "documentation": "<p>An array that specifies which events the Amazon SES API v2 should send to the destinations in this <code>EventDestinationDefinition</code>.</p>"}, "KinesisFirehoseDestination": {"shape": "KinesisFirehoseDestination", "documentation": "<p>An object that defines an Amazon Kinesis Data Firehose destination for email events. You can use Amazon Kinesis Data Firehose to stream data to other services, such as Amazon S3 and Amazon Redshift.</p>"}, "CloudWatchDestination": {"shape": "CloudWatchDestination", "documentation": "<p>An object that defines an Amazon CloudWatch destination for email events. You can use Amazon CloudWatch to monitor and gain insights on your email sending metrics.</p>"}, "SnsDestination": {"shape": "SnsDestination", "documentation": "<p>An object that defines an Amazon SNS destination for email events. You can use Amazon SNS to send notifications when certain email events occur.</p>"}, "EventBridgeDestination": {"shape": "EventBridgeDestination", "documentation": "<p>An object that defines an Amazon EventBridge destination for email events. You can use Amazon EventBridge to send notifications when certain email events occur.</p>"}, "PinpointDestination": {"shape": "PinpointDestination", "documentation": "<p>An object that defines an Amazon Pinpoint project destination for email events. You can send email event data to a Amazon Pinpoint project to view metrics using the Transactional Messaging dashboards that are built in to Amazon Pinpoint. For more information, see <a href=\"https://docs.aws.amazon.com/pinpoint/latest/userguide/analytics-transactional-messages.html\">Transactional Messaging Charts</a> in the <i>Amazon Pinpoint User Guide</i>.</p>"}}, "documentation": "<p>An object that defines the event destination. Specifically, it defines which services receive events from emails sent using the configuration set that the event destination is associated with. Also defines the types of events that are sent to the event destination.</p>"}, "EventDestinationName": {"type": "string", "documentation": "<p>The name of an event destination.</p> <p> <i>Events</i> include message sends, deliveries, opens, clicks, bounces, and complaints. <i>Event destinations</i> are places that you can send information about these events to. For example, you can send event data to Amazon SNS to receive notifications when you receive bounces or complaints, or you can use Amazon Kinesis Data Firehose to stream data to Amazon S3 for long-term storage.</p>"}, "EventDestinations": {"type": "list", "member": {"shape": "EventDestination"}}, "EventDetails": {"type": "structure", "members": {"Bounce": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>Information about a <code>Bounce</code> event.</p>"}, "Complaint": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>Information about a <code>Complaint</code> event.</p>"}}, "documentation": "<p> Contains a <code>Bounce</code> object if the event type is <code>BOUNCE</code>. Contains a <code>Complaint</code> object if the event type is <code>COMPLAINT</code>. </p>"}, "EventType": {"type": "string", "documentation": "<p>An email sending event type. For example, email sends, opens, and bounces are all email events.</p>", "enum": ["SEND", "REJECT", "BOUNCE", "COMPLAINT", "DELIVERY", "OPEN", "CLICK", "RENDERING_FAILURE", "DELIVERY_DELAY", "SUBSCRIPTION"]}, "EventTypes": {"type": "list", "member": {"shape": "EventType"}}, "ExportDataSource": {"type": "structure", "members": {"MetricsDataSource": {"shape": "MetricsDataSource"}, "MessageInsightsDataSource": {"shape": "MessageInsightsDataSource"}}, "documentation": "<p>An object that contains details about the data source of the export job. It can only contain one of <code>MetricsDataSource</code> or <code>MessageInsightsDataSource</code> object.</p>"}, "ExportDestination": {"type": "structure", "required": ["DataFormat"], "members": {"DataFormat": {"shape": "DataFormat", "documentation": "<p>The data format of the final export job file, can be one of the following:</p> <ul> <li> <p> <code>CSV</code> - A comma-separated values file.</p> </li> <li> <p> <code>JSON</code> - A Json file.</p> </li> </ul>"}, "S3Url": {"shape": "S3Url", "documentation": "<p>An Amazon S3 pre-signed URL that points to the generated export file.</p>"}}, "documentation": "<p>An object that contains details about the destination of the export job.</p>"}, "ExportDimensionValue": {"type": "list", "member": {"shape": "MetricDimensionValue"}, "max": 10, "min": 1}, "ExportDimensions": {"type": "map", "key": {"shape": "MetricDimensionName"}, "value": {"shape": "ExportDimensionValue"}, "max": 3, "min": 1}, "ExportJobSummary": {"type": "structure", "members": {"JobId": {"shape": "JobId", "documentation": "<p>The export job ID.</p>"}, "ExportSourceType": {"shape": "ExportSourceType", "documentation": "<p>The source type of the export job.</p>"}, "JobStatus": {"shape": "JobStatus", "documentation": "<p>The status of the export job.</p>"}, "CreatedTimestamp": {"shape": "Timestamp", "documentation": "<p>The timestamp of when the export job was created.</p>"}, "CompletedTimestamp": {"shape": "Timestamp", "documentation": "<p>The timestamp of when the export job was completed.</p>"}}, "documentation": "<p>A summary of the export job.</p>"}, "ExportJobSummaryList": {"type": "list", "member": {"shape": "ExportJobSummary"}, "documentation": "<p>A list of the export job summaries.</p>"}, "ExportMetric": {"type": "structure", "members": {"Name": {"shape": "Metric"}, "Aggregation": {"shape": "MetricAggregation"}}, "documentation": "<p>An object that contains a mapping between a <code>Metric</code> and <code>MetricAggregation</code>.</p>"}, "ExportMetrics": {"type": "list", "member": {"shape": "ExportMetric"}, "max": 10, "min": 1}, "ExportSourceType": {"type": "string", "documentation": "<p>The type of data source of an export, can be one of the following:</p> <ul> <li> <p> <code>METRICS_DATA</code> - The metrics export.</p> </li> <li> <p> <code>MESSAGE_INSIGHTS</code> - The Message Insights export.</p> </li> </ul>", "enum": ["METRICS_DATA", "MESSAGE_INSIGHTS"]}, "ExportStatistics": {"type": "structure", "members": {"ProcessedRecordsCount": {"shape": "ProcessedRecordsCount", "documentation": "<p>The number of records that were processed to generate the final export file.</p>"}, "ExportedRecordsCount": {"shape": "ExportedRecordsCount", "documentation": "<p>The number of records that were exported to the final export file.</p> <p>This value might not be available for all export source types</p>"}}, "documentation": "<p>Statistics about the execution of an export job.</p>"}, "ExportedRecordsCount": {"type": "integer"}, "FailedRecordsCount": {"type": "integer"}, "FailedRecordsS3Url": {"type": "string"}, "FailureInfo": {"type": "structure", "members": {"FailedRecordsS3Url": {"shape": "FailedRecordsS3Url", "documentation": "<p>An Amazon S3 pre-signed URL that contains all the failed records and related information.</p>"}, "ErrorMessage": {"shape": "ErrorMessage", "documentation": "<p>A message about why the job failed.</p>"}}, "documentation": "<p>An object that contains the failure details about a job.</p>"}, "FailureRedirectionURL": {"type": "string", "documentation": "<p>The URL that the recipient of the verification email is sent to if his or her address is not successfully verified.</p>"}, "FeatureStatus": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "FeedbackId": {"type": "string"}, "GeneralEnforcementStatus": {"type": "string"}, "GetAccountRequest": {"type": "structure", "members": {}, "documentation": "<p>A request to obtain information about the email-sending capabilities of your Amazon SES account.</p>"}, "GetAccountResponse": {"type": "structure", "members": {"DedicatedIpAutoWarmupEnabled": {"shape": "Enabled", "documentation": "<p>Indicates whether or not the automatic warm-up feature is enabled for dedicated IP addresses that are associated with your account.</p>"}, "EnforcementStatus": {"shape": "GeneralEnforcementStatus", "documentation": "<p>The reputation status of your Amazon SES account. The status can be one of the following:</p> <ul> <li> <p> <code>HEALTHY</code> – There are no reputation-related issues that currently impact your account.</p> </li> <li> <p> <code>PROBATION</code> – We've identified potential issues with your Amazon SES account. We're placing your account under review while you work on correcting these issues.</p> </li> <li> <p> <code>SHUTDOWN</code> – Your account's ability to send email is currently paused because of an issue with the email sent from your account. When you correct the issue, you can contact us and request that your account's ability to send email is resumed.</p> </li> </ul>"}, "ProductionAccessEnabled": {"shape": "Enabled", "documentation": "<p>Indicates whether or not your account has production access in the current Amazon Web Services Region.</p> <p>If the value is <code>false</code>, then your account is in the <i>sandbox</i>. When your account is in the sandbox, you can only send email to verified identities. </p> <p>If the value is <code>true</code>, then your account has production access. When your account has production access, you can send email to any address. The sending quota and maximum sending rate for your account vary based on your specific use case.</p>"}, "SendQuota": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>An object that contains information about the per-day and per-second sending limits for your Amazon SES account in the current Amazon Web Services Region.</p>"}, "SendingEnabled": {"shape": "Enabled", "documentation": "<p>Indicates whether or not email sending is enabled for your Amazon SES account in the current Amazon Web Services Region.</p>"}, "SuppressionAttributes": {"shape": "SuppressionAttributes", "documentation": "<p>An object that contains information about the email address suppression preferences for your account in the current Amazon Web Services Region.</p>"}, "Details": {"shape": "AccountDetails", "documentation": "<p>An object that defines your account details.</p>"}, "VdmAttributes": {"shape": "VdmAttributes", "documentation": "<p>The VDM attributes that apply to your Amazon SES account.</p>"}}, "documentation": "<p>A list of details about the email-sending capabilities of your Amazon SES account in the current Amazon Web Services Region.</p>"}, "GetBlacklistReportsRequest": {"type": "structure", "required": ["BlacklistItemNames"], "members": {"BlacklistItemNames": {"shape": "BlacklistItemNames", "documentation": "<p>A list of IP addresses that you want to retrieve blacklist information about. You can only specify the dedicated IP addresses that you use to send email using Amazon SES or Amazon Pinpoint.</p>", "location": "querystring", "locationName": "BlacklistItemNames"}}, "documentation": "<p>A request to retrieve a list of the blacklists that your dedicated IP addresses appear on.</p>"}, "GetBlacklistReportsResponse": {"type": "structure", "required": ["BlacklistReport"], "members": {"BlacklistReport": {"shape": "BlacklistReport", "documentation": "<p>An object that contains information about a blacklist that one of your dedicated IP addresses appears on.</p>"}}, "documentation": "<p>An object that contains information about blacklist events.</p>"}, "GetConfigurationSetEventDestinationsRequest": {"type": "structure", "required": ["ConfigurationSetName"], "members": {"ConfigurationSetName": {"shape": "ConfigurationSetName", "documentation": "<p>The name of the configuration set that contains the event destination.</p>", "location": "uri", "locationName": "ConfigurationSetName"}}, "documentation": "<p>A request to obtain information about the event destinations for a configuration set.</p>"}, "GetConfigurationSetEventDestinationsResponse": {"type": "structure", "members": {"EventDestinations": {"shape": "EventDestinations", "documentation": "<p>An array that includes all of the events destinations that have been configured for the configuration set.</p>"}}, "documentation": "<p>Information about an event destination for a configuration set.</p>"}, "GetConfigurationSetRequest": {"type": "structure", "required": ["ConfigurationSetName"], "members": {"ConfigurationSetName": {"shape": "ConfigurationSetName", "documentation": "<p>The name of the configuration set.</p>", "location": "uri", "locationName": "ConfigurationSetName"}}, "documentation": "<p>A request to obtain information about a configuration set.</p>"}, "GetConfigurationSetResponse": {"type": "structure", "members": {"ConfigurationSetName": {"shape": "ConfigurationSetName", "documentation": "<p>The name of the configuration set.</p>"}, "TrackingOptions": {"shape": "TrackingOptions", "documentation": "<p>An object that defines the open and click tracking options for emails that you send using the configuration set.</p>"}, "DeliveryOptions": {"shape": "DeliveryOptions", "documentation": "<p>An object that defines the dedicated IP pool that is used to send emails that you send using the configuration set.</p>"}, "ReputationOptions": {"shape": "ReputationOptions", "documentation": "<p>An object that defines whether or not Amazon SES collects reputation metrics for the emails that you send that use the configuration set.</p>"}, "SendingOptions": {"shape": "SendingOptions", "documentation": "<p>An object that defines whether or not Amazon SES can send email that you send using the configuration set.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>An array of objects that define the tags (keys and values) that are associated with the configuration set.</p>"}, "SuppressionOptions": {"shape": "SuppressionOptions", "documentation": "<p>An object that contains information about the suppression list preferences for your account.</p>"}, "VdmOptions": {"shape": "VdmOptions", "documentation": "<p>An object that contains information about the VDM preferences for your configuration set.</p>"}, "ArchivingOptions": {"shape": "ArchivingOptions", "documentation": "<p>An object that defines the MailManager archive where sent emails are archived that you send using the configuration set.</p>"}}, "documentation": "<p>Information about a configuration set.</p>"}, "GetContactListRequest": {"type": "structure", "required": ["ContactListName"], "members": {"ContactListName": {"shape": "ContactListName", "documentation": "<p>The name of the contact list.</p>", "location": "uri", "locationName": "ContactListName"}}}, "GetContactListResponse": {"type": "structure", "members": {"ContactListName": {"shape": "ContactListName", "documentation": "<p>The name of the contact list.</p>"}, "Topics": {"shape": "Topics", "documentation": "<p>An interest group, theme, or label within a list. A contact list can have multiple topics.</p>"}, "Description": {"shape": "Description", "documentation": "<p>A description of what the contact list is about.</p>"}, "CreatedTimestamp": {"shape": "Timestamp", "documentation": "<p>A timestamp noting when the contact list was created.</p>"}, "LastUpdatedTimestamp": {"shape": "Timestamp", "documentation": "<p>A timestamp noting the last time the contact list was updated.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The tags associated with a contact list.</p>"}}}, "GetContactRequest": {"type": "structure", "required": ["ContactListName", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "members": {"ContactListName": {"shape": "ContactListName", "documentation": "<p>The name of the contact list to which the contact belongs.</p>", "location": "uri", "locationName": "ContactListName"}, "EmailAddress": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The contact's email address.</p>", "location": "uri", "locationName": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, "GetContactResponse": {"type": "structure", "members": {"ContactListName": {"shape": "ContactListName", "documentation": "<p>The name of the contact list to which the contact belongs.</p>"}, "EmailAddress": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The contact's email address.</p>"}, "TopicPreferences": {"shape": "TopicPreferenceList", "documentation": "<p>The contact's preference for being opted-in to or opted-out of a topic.&gt;</p>"}, "TopicDefaultPreferences": {"shape": "TopicPreferenceList", "documentation": "<p>The default topic preferences applied to the contact.</p>"}, "UnsubscribeAll": {"shape": "UnsubscribeAll", "documentation": "<p>A boolean value status noting if the contact is unsubscribed from all contact list topics.</p>"}, "AttributesData": {"shape": "AttributesData", "documentation": "<p>The attribute data attached to a contact.</p>"}, "CreatedTimestamp": {"shape": "Timestamp", "documentation": "<p>A timestamp noting when the contact was created.</p>"}, "LastUpdatedTimestamp": {"shape": "Timestamp", "documentation": "<p>A timestamp noting the last time the contact's information was updated.</p>"}}}, "GetCustomVerificationEmailTemplateRequest": {"type": "structure", "required": ["TemplateName"], "members": {"TemplateName": {"shape": "EmailTemplateName", "documentation": "<p>The name of the custom verification email template that you want to retrieve.</p>", "location": "uri", "locationName": "TemplateName"}}, "documentation": "<p>Represents a request to retrieve an existing custom verification email template.</p>"}, "GetCustomVerificationEmailTemplateResponse": {"type": "structure", "members": {"TemplateName": {"shape": "EmailTemplateName", "documentation": "<p>The name of the custom verification email template.</p>"}, "FromEmailAddress": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The email address that the custom verification email is sent from.</p>"}, "TemplateSubject": {"shape": "EmailTemplateSubject", "documentation": "<p>The subject line of the custom verification email.</p>"}, "TemplateContent": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The content of the custom verification email.</p>"}, "SuccessRedirectionURL": {"shape": "SuccessRedirectionURL", "documentation": "<p>The URL that the recipient of the verification email is sent to if his or her address is successfully verified.</p>"}, "FailureRedirectionURL": {"shape": "FailureRedirectionURL", "documentation": "<p>The URL that the recipient of the verification email is sent to if his or her address is not successfully verified.</p>"}}, "documentation": "<p>The following elements are returned by the service.</p>"}, "GetDedicatedIpPoolRequest": {"type": "structure", "required": ["PoolName"], "members": {"PoolName": {"shape": "PoolName", "documentation": "<p>The name of the dedicated IP pool to retrieve.</p>", "location": "uri", "locationName": "PoolName"}}, "documentation": "<p>A request to obtain more information about a dedicated IP pool.</p>"}, "GetDedicatedIpPoolResponse": {"type": "structure", "members": {"DedicatedIpPool": {"shape": "DedicatedIpPool", "documentation": "<p>An object that contains information about a dedicated IP pool.</p>"}}, "documentation": "<p>The following element is returned by the service.</p>"}, "GetDedicatedIpRequest": {"type": "structure", "required": ["Ip"], "members": {"Ip": {"shape": "Ip", "documentation": "<p>The IP address that you want to obtain more information about. The value you specify has to be a dedicated IP address that's assocaited with your Amazon Web Services account.</p>", "location": "uri", "locationName": "IP"}}, "documentation": "<p>A request to obtain more information about a dedicated IP address.</p>"}, "GetDedicatedIpResponse": {"type": "structure", "members": {"DedicatedIp": {"shape": "DedicatedIp", "documentation": "<p>An object that contains information about a dedicated IP address.</p>"}}, "documentation": "<p>Information about a dedicated IP address.</p>"}, "GetDedicatedIpsRequest": {"type": "structure", "members": {"PoolName": {"shape": "PoolName", "documentation": "<p>The name of the IP pool that the dedicated IP address is associated with.</p>", "location": "querystring", "locationName": "PoolName"}, "NextToken": {"shape": "NextToken", "documentation": "<p>A token returned from a previous call to <code>GetDedicatedIps</code> to indicate the position of the dedicated IP pool in the list of IP pools.</p>", "location": "querystring", "locationName": "NextToken"}, "PageSize": {"shape": "MaxItems", "documentation": "<p>The number of results to show in a single call to <code>GetDedicatedIpsRequest</code>. If the number of results is larger than the number you specified in this parameter, then the response includes a <code>NextToken</code> element, which you can use to obtain additional results.</p>", "location": "querystring", "locationName": "PageSize"}}, "documentation": "<p>A request to obtain more information about dedicated IP pools.</p>"}, "GetDedicatedIpsResponse": {"type": "structure", "members": {"DedicatedIps": {"shape": "DedicatedIpList", "documentation": "<p>A list of dedicated IP addresses that are associated with your Amazon Web Services account.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>A token that indicates that there are additional dedicated IP addresses to list. To view additional addresses, issue another request to <code>GetDedicatedIps</code>, passing this token in the <code>NextToken</code> parameter.</p>"}}, "documentation": "<p>Information about the dedicated IP addresses that are associated with your Amazon Web Services account.</p>"}, "GetDeliverabilityDashboardOptionsRequest": {"type": "structure", "members": {}, "documentation": "<p>Retrieve information about the status of the Deliverability dashboard for your Amazon Web Services account. When the Deliverability dashboard is enabled, you gain access to reputation, deliverability, and other metrics for your domains. You also gain the ability to perform predictive inbox placement tests.</p> <p>When you use the Deliverability dashboard, you pay a monthly subscription charge, in addition to any other fees that you accrue by using Amazon SES and other Amazon Web Services services. For more information about the features and cost of a Deliverability dashboard subscription, see <a href=\"http://aws.amazon.com/pinpoint/pricing/\">Amazon Pinpoint Pricing</a>.</p>"}, "GetDeliverabilityDashboardOptionsResponse": {"type": "structure", "required": ["DashboardEnabled"], "members": {"DashboardEnabled": {"shape": "Enabled", "documentation": "<p>Specifies whether the Deliverability dashboard is enabled. If this value is <code>true</code>, the dashboard is enabled.</p>"}, "SubscriptionExpiryDate": {"shape": "Timestamp", "documentation": "<p>The date when your current subscription to the Deliverability dashboard is scheduled to expire, if your subscription is scheduled to expire at the end of the current calendar month. This value is null if you have an active subscription that isn’t due to expire at the end of the month.</p>"}, "AccountStatus": {"shape": "DeliverabilityDashboardAccountStatus", "documentation": "<p>The current status of your Deliverability dashboard subscription. If this value is <code>PENDING_EXPIRATION</code>, your subscription is scheduled to expire at the end of the current calendar month.</p>"}, "ActiveSubscribedDomains": {"shape": "DomainDeliverabilityTrackingOptions", "documentation": "<p>An array of objects, one for each verified domain that you use to send email and currently has an active Deliverability dashboard subscription that isn’t scheduled to expire at the end of the current calendar month.</p>"}, "PendingExpirationSubscribedDomains": {"shape": "DomainDeliverabilityTrackingOptions", "documentation": "<p>An array of objects, one for each verified domain that you use to send email and currently has an active Deliverability dashboard subscription that's scheduled to expire at the end of the current calendar month.</p>"}}, "documentation": "<p>An object that shows the status of the Deliverability dashboard.</p>"}, "GetDeliverabilityTestReportRequest": {"type": "structure", "required": ["ReportId"], "members": {"ReportId": {"shape": "ReportId", "documentation": "<p>A unique string that identifies the predictive inbox placement test.</p>", "location": "uri", "locationName": "ReportId"}}, "documentation": "<p>A request to retrieve the results of a predictive inbox placement test.</p>"}, "GetDeliverabilityTestReportResponse": {"type": "structure", "required": ["DeliverabilityTestReport", "OverallPlacement", "IspPlacements"], "members": {"DeliverabilityTestReport": {"shape": "DeliverabilityTestReport", "documentation": "<p>An object that contains the results of the predictive inbox placement test.</p>"}, "OverallPlacement": {"shape": "PlacementStatistics", "documentation": "<p>An object that specifies how many test messages that were sent during the predictive inbox placement test were delivered to recipients' inboxes, how many were sent to recipients' spam folders, and how many weren't delivered.</p>"}, "IspPlacements": {"shape": "IspPlacements", "documentation": "<p>An object that describes how the test email was handled by several email providers, including Gmail, Hotmail, Yahoo, AOL, and others.</p>"}, "Message": {"shape": "MessageContent", "documentation": "<p>An object that contains the message that you sent when you performed this predictive inbox placement test.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>An array of objects that define the tags (keys and values) that are associated with the predictive inbox placement test.</p>"}}, "documentation": "<p>The results of the predictive inbox placement test.</p>"}, "GetDomainDeliverabilityCampaignRequest": {"type": "structure", "required": ["CampaignId"], "members": {"CampaignId": {"shape": "CampaignId", "documentation": "<p>The unique identifier for the campaign. The Deliverability dashboard automatically generates and assigns this identifier to a campaign.</p>", "location": "uri", "locationName": "CampaignId"}}, "documentation": "<p>Retrieve all the deliverability data for a specific campaign. This data is available for a campaign only if the campaign sent email by using a domain that the Deliverability dashboard is enabled for (<code>PutDeliverabilityDashboardOption</code> operation).</p>"}, "GetDomainDeliverabilityCampaignResponse": {"type": "structure", "required": ["DomainDeliverabilityCampaign"], "members": {"DomainDeliverabilityCampaign": {"shape": "DomainDeliverabilityCampaign", "documentation": "<p>An object that contains the deliverability data for the campaign.</p>"}}, "documentation": "<p>An object that contains all the deliverability data for a specific campaign. This data is available for a campaign only if the campaign sent email by using a domain that the Deliverability dashboard is enabled for.</p>"}, "GetDomainStatisticsReportRequest": {"type": "structure", "required": ["Domain", "StartDate", "EndDate"], "members": {"Domain": {"shape": "Identity", "documentation": "<p>The domain that you want to obtain deliverability metrics for.</p>", "location": "uri", "locationName": "Domain"}, "StartDate": {"shape": "Timestamp", "documentation": "<p>The first day (in Unix time) that you want to obtain domain deliverability metrics for.</p>", "location": "querystring", "locationName": "StartDate"}, "EndDate": {"shape": "Timestamp", "documentation": "<p>The last day (in Unix time) that you want to obtain domain deliverability metrics for. The <code>EndDate</code> that you specify has to be less than or equal to 30 days after the <code>StartDate</code>.</p>", "location": "querystring", "locationName": "EndDate"}}, "documentation": "<p>A request to obtain deliverability metrics for a domain.</p>"}, "GetDomainStatisticsReportResponse": {"type": "structure", "required": ["OverallVolume", "DailyVolumes"], "members": {"OverallVolume": {"shape": "OverallVolume", "documentation": "<p>An object that contains deliverability metrics for the domain that you specified. The data in this object is a summary of all of the data that was collected from the <code>StartDate</code> to the <code>EndDate</code>.</p>"}, "DailyVolumes": {"shape": "DailyVolumes", "documentation": "<p>An object that contains deliverability metrics for the domain that you specified. This object contains data for each day, starting on the <code>StartDate</code> and ending on the <code>EndDate</code>.</p>"}}, "documentation": "<p>An object that includes statistics that are related to the domain that you specified.</p>"}, "GetEmailIdentityPoliciesRequest": {"type": "structure", "required": ["EmailIdentity"], "members": {"EmailIdentity": {"shape": "Identity", "documentation": "<p>The email identity.</p>", "location": "uri", "locationName": "EmailIdentity"}}, "documentation": "<p>A request to return the policies of an email identity.</p>"}, "GetEmailIdentityPoliciesResponse": {"type": "structure", "members": {"Policies": {"shape": "PolicyMap", "documentation": "<p>A map of policy names to policies.</p>"}}, "documentation": "<p>Identity policies associated with email identity.</p>"}, "GetEmailIdentityRequest": {"type": "structure", "required": ["EmailIdentity"], "members": {"EmailIdentity": {"shape": "Identity", "documentation": "<p>The email identity.</p>", "location": "uri", "locationName": "EmailIdentity"}}, "documentation": "<p>A request to return details about an email identity.</p>"}, "GetEmailIdentityResponse": {"type": "structure", "members": {"IdentityType": {"shape": "IdentityType", "documentation": "<p>The email identity type. Note: the <code>MANAGED_DOMAIN</code> identity type is not supported.</p>"}, "FeedbackForwardingStatus": {"shape": "Enabled", "documentation": "<p>The feedback forwarding configuration for the identity.</p> <p>If the value is <code>true</code>, you receive email notifications when bounce or complaint events occur. These notifications are sent to the address that you specified in the <code>Return-Path</code> header of the original email.</p> <p>You're required to have a method of tracking bounces and complaints. If you haven't set up another mechanism for receiving bounce or complaint notifications (for example, by setting up an event destination), you receive an email notification when these events occur (even if this setting is disabled).</p>"}, "VerifiedForSendingStatus": {"shape": "Enabled", "documentation": "<p>Specifies whether or not the identity is verified. You can only send email from verified email addresses or domains. For more information about verifying identities, see the <a href=\"https://docs.aws.amazon.com/pinpoint/latest/userguide/channels-email-manage-verify.html\">Amazon Pinpoint User Guide</a>.</p>"}, "DkimAttributes": {"shape": "DkimAttributes", "documentation": "<p>An object that contains information about the DKIM attributes for the identity.</p>"}, "MailFromAttributes": {"shape": "MailFromAttributes", "documentation": "<p>An object that contains information about the Mail-From attributes for the email identity.</p>"}, "Policies": {"shape": "PolicyMap", "documentation": "<p>A map of policy names to policies.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>An array of objects that define the tags (keys and values) that are associated with the email identity.</p>"}, "ConfigurationSetName": {"shape": "ConfigurationSetName", "documentation": "<p>The configuration set used by default when sending from this identity.</p>"}, "VerificationStatus": {"shape": "VerificationStatus", "documentation": "<p>The verification status of the identity. The status can be one of the following:</p> <ul> <li> <p> <code>PENDING</code> – The verification process was initiated, but Amazon SES hasn't yet been able to verify the identity.</p> </li> <li> <p> <code>SUCCESS</code> – The verification process completed successfully.</p> </li> <li> <p> <code>FAILED</code> – The verification process failed.</p> </li> <li> <p> <code>TEMPORARY_FAILURE</code> – A temporary issue is preventing Amazon SES from determining the verification status of the identity.</p> </li> <li> <p> <code>NOT_STARTED</code> – The verification process hasn't been initiated for the identity.</p> </li> </ul>"}, "VerificationInfo": {"shape": "VerificationInfo", "documentation": "<p>An object that contains additional information about the verification status for the identity.</p>"}}, "documentation": "<p>Details about an email identity.</p>"}, "GetEmailTemplateRequest": {"type": "structure", "required": ["TemplateName"], "members": {"TemplateName": {"shape": "EmailTemplateName", "documentation": "<p>The name of the template.</p>", "location": "uri", "locationName": "TemplateName"}}, "documentation": "<p>Represents a request to display the template object (which includes the subject line, HTML part and text part) for the template you specify.</p>"}, "GetEmailTemplateResponse": {"type": "structure", "required": ["TemplateName", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "members": {"TemplateName": {"shape": "EmailTemplateName", "documentation": "<p>The name of the template.</p>"}, "TemplateContent": {"shape": "EmailTemplateContent", "documentation": "<p>The content of the email template, composed of a subject line, an HTML part, and a text-only part.</p>"}}, "documentation": "<p>The following element is returned by the service.</p>"}, "GetExportJobRequest": {"type": "structure", "required": ["JobId"], "members": {"JobId": {"shape": "JobId", "documentation": "<p>The export job ID.</p>", "location": "uri", "locationName": "JobId"}}, "documentation": "<p>Represents a request to retrieve information about an export job using the export job ID.</p>"}, "GetExportJobResponse": {"type": "structure", "members": {"JobId": {"shape": "JobId", "documentation": "<p>The export job ID.</p>"}, "ExportSourceType": {"shape": "ExportSourceType", "documentation": "<p>The type of source of the export job.</p>"}, "JobStatus": {"shape": "JobStatus", "documentation": "<p>The status of the export job.</p>"}, "ExportDestination": {"shape": "ExportDestination", "documentation": "<p>The destination of the export job.</p>"}, "ExportDataSource": {"shape": "ExportDataSource", "documentation": "<p>The data source of the export job.</p>"}, "CreatedTimestamp": {"shape": "Timestamp", "documentation": "<p>The timestamp of when the export job was created.</p>"}, "CompletedTimestamp": {"shape": "Timestamp", "documentation": "<p>The timestamp of when the export job was completed.</p>"}, "FailureInfo": {"shape": "FailureInfo", "documentation": "<p>The failure details about an export job.</p>"}, "Statistics": {"shape": "ExportStatistics", "documentation": "<p>The statistics about the export job.</p>"}}, "documentation": "<p>An HTTP 200 response if the request succeeds, or an error message if the request fails.</p>"}, "GetImportJobRequest": {"type": "structure", "required": ["JobId"], "members": {"JobId": {"shape": "JobId", "documentation": "<p>The ID of the import job.</p>", "location": "uri", "locationName": "JobId"}}, "documentation": "<p>Represents a request for information about an import job using the import job ID.</p>"}, "GetImportJobResponse": {"type": "structure", "members": {"JobId": {"shape": "JobId", "documentation": "<p>A string that represents the import job ID.</p>"}, "ImportDestination": {"shape": "ImportDestination", "documentation": "<p>The destination of the import job.</p>"}, "ImportDataSource": {"shape": "ImportDataSource", "documentation": "<p>The data source of the import job.</p>"}, "FailureInfo": {"shape": "FailureInfo", "documentation": "<p>The failure details about an import job.</p>"}, "JobStatus": {"shape": "JobStatus", "documentation": "<p>The status of the import job.</p>"}, "CreatedTimestamp": {"shape": "Timestamp", "documentation": "<p>The time stamp of when the import job was created.</p>"}, "CompletedTimestamp": {"shape": "Timestamp", "documentation": "<p>The time stamp of when the import job was completed.</p>"}, "ProcessedRecordsCount": {"shape": "ProcessedRecordsCount", "documentation": "<p>The current number of records processed.</p>"}, "FailedRecordsCount": {"shape": "FailedRecordsCount", "documentation": "<p>The number of records that failed processing because of invalid input or other reasons.</p>"}}, "documentation": "<p>An HTTP 200 response if the request succeeds, or an error message if the request fails.</p>"}, "GetMessageInsightsRequest": {"type": "structure", "required": ["MessageId"], "members": {"MessageId": {"shape": "OutboundMessageId", "documentation": "<p> A <code>MessageId</code> is a unique identifier for a message, and is returned when sending emails through Amazon SES. </p>", "location": "uri", "locationName": "MessageId"}}, "documentation": "<p>A request to return information about a message.</p>"}, "GetMessageInsightsResponse": {"type": "structure", "members": {"MessageId": {"shape": "OutboundMessageId", "documentation": "<p>A unique identifier for the message.</p>"}, "FromEmailAddress": {"shape": "InsightsEmailAddress", "documentation": "<p>The from address used to send the message.</p>"}, "Subject": {"shape": "EmailSubject", "documentation": "<p>The subject line of the message.</p>"}, "EmailTags": {"shape": "MessageTagList", "documentation": "<p> A list of tags, in the form of name/value pairs, that were applied to the email you sent, along with Amazon SES <a href=\"https://docs.aws.amazon.com/ses/latest/dg/monitor-using-event-publishing.html\">Auto-Tags</a>. </p>"}, "Insights": {"shape": "EmailInsightsList", "documentation": "<p>A set of insights associated with the message.</p>"}}, "documentation": "<p>Information about a message.</p>"}, "GetMultiRegionEndpointRequest": {"type": "structure", "required": ["EndpointName"], "members": {"EndpointName": {"shape": "EndpointName", "documentation": "<p>The name of the multi-region endpoint (global-endpoint).</p>", "location": "uri", "locationName": "EndpointName"}}, "documentation": "<p>Represents a request to display the multi-region endpoint (global-endpoint).</p>"}, "GetMultiRegionEndpointResponse": {"type": "structure", "members": {"EndpointName": {"shape": "EndpointName", "documentation": "<p>The name of the multi-region endpoint (global-endpoint).</p>"}, "EndpointId": {"shape": "EndpointId", "documentation": "<p>The ID of the multi-region endpoint (global-endpoint).</p>"}, "Routes": {"shape": "Routes", "documentation": "<p>Contains routes information for the multi-region endpoint (global-endpoint).</p>"}, "Status": {"shape": "Status", "documentation": "<p>The status of the multi-region endpoint (global-endpoint).</p> <ul> <li> <p> <code>CREATING</code> – The resource is being provisioned.</p> </li> <li> <p> <code>READY</code> – The resource is ready to use.</p> </li> <li> <p> <code>FAILED</code> – The resource failed to be provisioned.</p> </li> <li> <p> <code>DELETING</code> – The resource is being deleted as requested.</p> </li> </ul>"}, "CreatedTimestamp": {"shape": "Timestamp", "documentation": "<p>The time stamp of when the multi-region endpoint (global-endpoint) was created.</p>"}, "LastUpdatedTimestamp": {"shape": "Timestamp", "documentation": "<p>The time stamp of when the multi-region endpoint (global-endpoint) was last updated.</p>"}}, "documentation": "<p>An HTTP 200 response if the request succeeds, or an error message if the request fails.</p>"}, "GetSuppressedDestinationRequest": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "members": {"EmailAddress": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The email address that's on the account suppression list.</p>", "location": "uri", "locationName": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "documentation": "<p>A request to retrieve information about an email address that's on the suppression list for your account.</p>"}, "GetSuppressedDestinationResponse": {"type": "structure", "required": ["SuppressedDestination"], "members": {"SuppressedDestination": {"shape": "SuppressedDestination", "documentation": "<p>An object containing information about the suppressed email address.</p>"}}, "documentation": "<p>Information about the suppressed email address.</p>"}, "GuardianAttributes": {"type": "structure", "members": {"OptimizedSharedDelivery": {"shape": "FeatureStatus", "documentation": "<p>Specifies the status of your VDM optimized shared delivery. Can be one of the following:</p> <ul> <li> <p> <code>ENABLED</code> – Amazon SES enables optimized shared delivery for your account.</p> </li> <li> <p> <code>DISABLED</code> – Amazon SES disables optimized shared delivery for your account.</p> </li> </ul>"}}, "documentation": "<p>An object containing additional settings for your VDM configuration as applicable to the Guardian.</p>"}, "GuardianOptions": {"type": "structure", "members": {"OptimizedSharedDelivery": {"shape": "FeatureStatus", "documentation": "<p>Specifies the status of your VDM optimized shared delivery. Can be one of the following:</p> <ul> <li> <p> <code>ENABLED</code> – Amazon SES enables optimized shared delivery for the configuration set.</p> </li> <li> <p> <code>DISABLED</code> – Amazon SES disables optimized shared delivery for the configuration set.</p> </li> </ul>"}}, "documentation": "<p>An object containing additional settings for your VDM configuration as applicable to the Guardian.</p>"}, "HttpsPolicy": {"type": "string", "documentation": "<p>The https policy to use for tracking open and click events. If the value is OPTIONAL or HttpsPolicy is not specified, the open trackers use HTTP and click tracker use the original protocol of the link. If the value is REQUIRE, both open and click tracker uses HTTPS and if the value is REQ<PERSON><PERSON>_OPEN_ONLY open tracker uses HTTPS and link tracker is same as original protocol of the link. </p>", "enum": ["REQUIRE", "REQUIRE_OPEN_ONLY", "OPTIONAL"]}, "Identity": {"type": "string", "min": 1}, "IdentityInfo": {"type": "structure", "members": {"IdentityType": {"shape": "IdentityType", "documentation": "<p>The email identity type. Note: the <code>MANAGED_DOMAIN</code> type is not supported for email identity types.</p>"}, "IdentityName": {"shape": "Identity", "documentation": "<p>The address or domain of the identity.</p>"}, "SendingEnabled": {"shape": "Enabled", "documentation": "<p>Indicates whether or not you can send email from the identity.</p> <p>An <i>identity</i> is an email address or domain that you send email from. Before you can send email from an identity, you have to demostrate that you own the identity, and that you authorize Amazon SES to send email from that identity.</p>"}, "VerificationStatus": {"shape": "VerificationStatus", "documentation": "<p>The verification status of the identity. The status can be one of the following:</p> <ul> <li> <p> <code>PENDING</code> – The verification process was initiated, but Amazon SES hasn't yet been able to verify the identity.</p> </li> <li> <p> <code>SUCCESS</code> – The verification process completed successfully.</p> </li> <li> <p> <code>FAILED</code> – The verification process failed.</p> </li> <li> <p> <code>TEMPORARY_FAILURE</code> – A temporary issue is preventing Amazon SES from determining the verification status of the identity.</p> </li> <li> <p> <code>NOT_STARTED</code> – The verification process hasn't been initiated for the identity.</p> </li> </ul>"}}, "documentation": "<p>Information about an email identity.</p>"}, "IdentityInfoList": {"type": "list", "member": {"shape": "IdentityInfo"}}, "IdentityType": {"type": "string", "enum": ["EMAIL_ADDRESS", "DOMAIN", "MANAGED_DOMAIN"]}, "ImageUrl": {"type": "string"}, "ImportDataSource": {"type": "structure", "required": ["S3Url", "DataFormat"], "members": {"S3Url": {"shape": "S3Url", "documentation": "<p>An Amazon S3 URL in the format s3://<i>&lt;bucket_name&gt;</i>/<i>&lt;object&gt;</i>.</p>"}, "DataFormat": {"shape": "DataFormat", "documentation": "<p>The data format of the import job's data source.</p>"}}, "documentation": "<p>An object that contains details about the data source of the import job.</p>"}, "ImportDestination": {"type": "structure", "members": {"SuppressionListDestination": {"shape": "SuppressionListDestination", "documentation": "<p>An object that contains the action of the import job towards suppression list.</p>"}, "ContactListDestination": {"shape": "ContactListDestination", "documentation": "<p>An object that contains the action of the import job towards a contact list.</p>"}}, "documentation": "<p>An object that contains details about the resource destination the import job is going to target.</p>"}, "ImportDestinationType": {"type": "string", "documentation": "<p>The destination of the import job, which can be used to list import jobs that have a certain <code>ImportDestinationType</code>.</p>", "enum": ["SUPPRESSION_LIST", "CONTACT_LIST"]}, "ImportJobSummary": {"type": "structure", "members": {"JobId": {"shape": "JobId"}, "ImportDestination": {"shape": "ImportDestination"}, "JobStatus": {"shape": "JobStatus"}, "CreatedTimestamp": {"shape": "Timestamp", "documentation": "<p>The date and time when the import job was created.</p>"}, "ProcessedRecordsCount": {"shape": "ProcessedRecordsCount", "documentation": "<p>The current number of records processed.</p>"}, "FailedRecordsCount": {"shape": "FailedRecordsCount", "documentation": "<p>The number of records that failed processing because of invalid input or other reasons.</p>"}}, "documentation": "<p>A summary of the import job.</p>"}, "ImportJobSummaryList": {"type": "list", "member": {"shape": "ImportJobSummary"}, "documentation": "<p>A list of the import job summaries.</p>"}, "InboxPlacementTrackingOption": {"type": "structure", "members": {"Global": {"shape": "Enabled", "documentation": "<p>Specifies whether inbox placement data is being tracked for the domain.</p>"}, "TrackedIsps": {"shape": "IspNameList", "documentation": "<p>An array of strings, one for each major email provider that the inbox placement data applies to.</p>"}}, "documentation": "<p>An object that contains information about the inbox placement data settings for a verified domain that’s associated with your Amazon Web Services account. This data is available only if you enabled the Deliverability dashboard for the domain.</p>"}, "InsightsEmailAddress": {"type": "string", "max": 320, "min": 1, "sensitive": true}, "InsightsEvent": {"type": "structure", "members": {"Timestamp": {"shape": "Timestamp", "documentation": "<p>The timestamp of the event.</p>"}, "Type": {"shape": "EventType", "documentation": "<p>The type of event:</p> <ul> <li> <p> <code>SEND</code> - The send request was successful and SES will attempt to deliver the message to the recipient’s mail server. (If account-level or global suppression is being used, SES will still count it as a send, but delivery is suppressed.) </p> </li> <li> <p> <code>DELIVERY</code> - <PERSON><PERSON> successfully delivered the email to the recipient's mail server. Excludes deliveries to the mailbox simulator, and those from emails addressed to more than one recipient. </p> </li> <li> <p> <code>BOUNCE</code> - Feedback received for delivery failures. Additional details about the bounce are provided in the <code>Details</code> object. Excludes bounces from the mailbox simulator, and those from emails addressed to more than one recipient. </p> </li> <li> <p> <code>COMPLAINT</code> - <PERSON><PERSON><PERSON><PERSON> received for the email. Additional details about the complaint are provided in the <code>Details</code> object. This excludes complaints from the mailbox simulator, those originating from your account-level suppression list (if enabled), and those from emails addressed to more than one recipient. </p> </li> <li> <p> <code>OPEN</code> - Open event for emails including open trackers. Excludes opens for emails addressed to more than one recipient.</p> </li> <li> <p> <code>CLICK</code> - Click event for emails including wrapped links. Excludes clicks for emails addressed to more than one recipient.</p> </li> </ul>"}, "Details": {"shape": "EventDetails", "documentation": "<p>Details about bounce or complaint events.</p>"}}, "documentation": "<p>An object containing details about a specific event.</p>"}, "InsightsEvents": {"type": "list", "member": {"shape": "InsightsEvent"}}, "InternalServiceErrorException": {"type": "structure", "members": {}, "documentation": "<p>The request couldn't be processed because an error occurred with the Amazon SES API v2.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true}, "InvalidNextTokenException": {"type": "structure", "members": {}, "documentation": "<p>The specified request includes an invalid or expired token.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "Ip": {"type": "string", "documentation": "<p>An IPv4 address.</p>"}, "IpList": {"type": "list", "member": {"shape": "Ip"}}, "Isp": {"type": "string"}, "IspFilterList": {"type": "list", "member": {"shape": "Isp"}, "max": 5}, "IspName": {"type": "string", "documentation": "<p>The name of an email provider.</p>"}, "IspNameList": {"type": "list", "member": {"shape": "IspName"}}, "IspPlacement": {"type": "structure", "members": {"IspName": {"shape": "IspName", "documentation": "<p>The name of the email provider that the inbox placement data applies to.</p>"}, "PlacementStatistics": {"shape": "PlacementStatistics", "documentation": "<p>An object that contains inbox placement metrics for a specific email provider.</p>"}}, "documentation": "<p>An object that describes how email sent during the predictive inbox placement test was handled by a certain email provider.</p>"}, "IspPlacements": {"type": "list", "member": {"shape": "IspPlacement"}}, "JobId": {"type": "string", "documentation": "<p>A string that represents a job ID.</p>", "min": 1}, "JobStatus": {"type": "string", "documentation": "<p>The status of a job.</p> <ul> <li> <p> <code>CREATED</code> – Job has just been created.</p> </li> <li> <p> <code>PROCESSING</code> – Job is processing.</p> </li> <li> <p> <code>ERROR</code> – An error occurred during processing.</p> </li> <li> <p> <code>COMPLETED</code> – Job has completed processing successfully.</p> </li> </ul>", "enum": ["CREATED", "PROCESSING", "COMPLETED", "FAILED", "CANCELLED"]}, "KinesisFirehoseDestination": {"type": "structure", "required": ["IamRoleArn", "DeliveryStreamArn"], "members": {"IamRoleArn": {"shape": "AmazonResourceName", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role that the Amazon SES API v2 uses to send email events to the Amazon Kinesis Data Firehose stream.</p>"}, "DeliveryStreamArn": {"shape": "AmazonResourceName", "documentation": "<p>The Amazon Resource Name (ARN) of the Amazon Kinesis Data Firehose stream that the Amazon SES API v2 sends email events to.</p>"}}, "documentation": "<p>An object that defines an Amazon Kinesis Data Firehose destination for email events. You can use Amazon Kinesis Data Firehose to stream data to other services, such as Amazon S3 and Amazon Redshift.</p>"}, "LastDeliveryEventList": {"type": "list", "member": {"shape": "DeliveryEventType"}, "max": 5}, "LastEngagementEventList": {"type": "list", "member": {"shape": "EngagementEventType"}, "max": 2}, "LastFreshStart": {"type": "timestamp", "documentation": "<p>The date and time (in Unix time) when the reputation metrics were last given a fresh start. When your account is given a fresh start, your reputation metrics are calculated starting from the date of the fresh start.</p>"}, "LimitExceededException": {"type": "structure", "members": {}, "documentation": "<p>There are too many instances of the specified resource type.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "ListConfigurationSetsRequest": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>A token returned from a previous call to <code>ListConfigurationSets</code> to indicate the position in the list of configuration sets.</p>", "location": "querystring", "locationName": "NextToken"}, "PageSize": {"shape": "MaxItems", "documentation": "<p>The number of results to show in a single call to <code>ListConfigurationSets</code>. If the number of results is larger than the number you specified in this parameter, then the response includes a <code>NextToken</code> element, which you can use to obtain additional results.</p>", "location": "querystring", "locationName": "PageSize"}}, "documentation": "<p>A request to obtain a list of configuration sets for your Amazon SES account in the current Amazon Web Services Region.</p>"}, "ListConfigurationSetsResponse": {"type": "structure", "members": {"ConfigurationSets": {"shape": "ConfigurationSetNameList", "documentation": "<p>An array that contains all of the configuration sets in your Amazon SES account in the current Amazon Web Services Region.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>A token that indicates that there are additional configuration sets to list. To view additional configuration sets, issue another request to <code>ListConfigurationSets</code>, and pass this token in the <code>NextToken</code> parameter.</p>"}}, "documentation": "<p>A list of configuration sets in your Amazon SES account in the current Amazon Web Services Region.</p>"}, "ListContactListsRequest": {"type": "structure", "members": {"PageSize": {"shape": "MaxItems", "documentation": "<p>Maximum number of contact lists to return at once. Use this parameter to paginate results. If additional contact lists exist beyond the specified limit, the <code>NextToken</code> element is sent in the response. Use the <code>NextToken</code> value in subsequent requests to retrieve additional lists.</p>", "location": "querystring", "locationName": "PageSize"}, "NextToken": {"shape": "NextToken", "documentation": "<p>A string token indicating that there might be additional contact lists available to be listed. Use the token provided in the Response to use in the subsequent call to ListContactLists with the same parameters to retrieve the next page of contact lists.</p>", "location": "querystring", "locationName": "NextToken"}}}, "ListContactListsResponse": {"type": "structure", "members": {"ContactLists": {"shape": "ListOfContactLists", "documentation": "<p>The available contact lists.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>A string token indicating that there might be additional contact lists available to be listed. Copy this token to a subsequent call to <code>ListContactLists</code> with the same parameters to retrieve the next page of contact lists.</p>"}}}, "ListContactsFilter": {"type": "structure", "members": {"FilteredStatus": {"shape": "SubscriptionStatus", "documentation": "<p>The status by which you are filtering: <code>OPT_IN</code> or <code>OPT_OUT</code>.</p>"}, "TopicFilter": {"shape": "TopicFilter", "documentation": "<p>Used for filtering by a specific topic preference.</p>"}}, "documentation": "<p>A filter that can be applied to a list of contacts.</p>"}, "ListContactsRequest": {"type": "structure", "required": ["ContactListName"], "members": {"ContactListName": {"shape": "ContactListName", "documentation": "<p>The name of the contact list.</p>", "location": "uri", "locationName": "ContactListName"}, "Filter": {"shape": "ListContactsFilter", "documentation": "<p>A filter that can be applied to a list of contacts.</p>"}, "PageSize": {"shape": "MaxItems", "documentation": "<p>The number of contacts that may be returned at once, which is dependent on if there are more or less contacts than the value of the PageSize. Use this parameter to paginate results. If additional contacts exist beyond the specified limit, the <code>NextToken</code> element is sent in the response. Use the <code>NextToken</code> value in subsequent requests to retrieve additional contacts.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>A string token indicating that there might be additional contacts available to be listed. Use the token provided in the Response to use in the subsequent call to ListContacts with the same parameters to retrieve the next page of contacts.</p>"}}}, "ListContactsResponse": {"type": "structure", "members": {"Contacts": {"shape": "ListOfContacts", "documentation": "<p>The contacts present in a specific contact list.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>A string token indicating that there might be additional contacts available to be listed. Copy this token to a subsequent call to <code>ListContacts</code> with the same parameters to retrieve the next page of contacts.</p>"}}}, "ListCustomVerificationEmailTemplatesRequest": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>A token returned from a previous call to <code>ListCustomVerificationEmailTemplates</code> to indicate the position in the list of custom verification email templates.</p>", "location": "querystring", "locationName": "NextToken"}, "PageSize": {"shape": "MaxItems", "documentation": "<p>The number of results to show in a single call to <code>ListCustomVerificationEmailTemplates</code>. If the number of results is larger than the number you specified in this parameter, then the response includes a <code>NextToken</code> element, which you can use to obtain additional results.</p> <p>The value you specify has to be at least 1, and can be no more than 50.</p>", "location": "querystring", "locationName": "PageSize"}}, "documentation": "<p>Represents a request to list the existing custom verification email templates for your account.</p>"}, "ListCustomVerificationEmailTemplatesResponse": {"type": "structure", "members": {"CustomVerificationEmailTemplates": {"shape": "CustomVerificationEmailTemplatesList", "documentation": "<p>A list of the custom verification email templates that exist in your account.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>A token indicating that there are additional custom verification email templates available to be listed. Pass this token to a subsequent call to <code>ListCustomVerificationEmailTemplates</code> to retrieve the next 50 custom verification email templates.</p>"}}, "documentation": "<p>The following elements are returned by the service.</p>"}, "ListDedicatedIpPoolsRequest": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>A token returned from a previous call to <code>ListDedicatedIpPools</code> to indicate the position in the list of dedicated IP pools.</p>", "location": "querystring", "locationName": "NextToken"}, "PageSize": {"shape": "MaxItems", "documentation": "<p>The number of results to show in a single call to <code>ListDedicatedIpPools</code>. If the number of results is larger than the number you specified in this parameter, then the response includes a <code>NextToken</code> element, which you can use to obtain additional results.</p>", "location": "querystring", "locationName": "PageSize"}}, "documentation": "<p>A request to obtain a list of dedicated IP pools.</p>"}, "ListDedicatedIpPoolsResponse": {"type": "structure", "members": {"DedicatedIpPools": {"shape": "ListOfDedicatedIpPools", "documentation": "<p>A list of all of the dedicated IP pools that are associated with your Amazon Web Services account in the current Region.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>A token that indicates that there are additional IP pools to list. To view additional IP pools, issue another request to <code>ListDedicatedIpPools</code>, passing this token in the <code>NextToken</code> parameter.</p>"}}, "documentation": "<p>A list of dedicated IP pools.</p>"}, "ListDeliverabilityTestReportsRequest": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>A token returned from a previous call to <code>ListDeliverabilityTestReports</code> to indicate the position in the list of predictive inbox placement tests.</p>", "location": "querystring", "locationName": "NextToken"}, "PageSize": {"shape": "MaxItems", "documentation": "<p>The number of results to show in a single call to <code>ListDeliverabilityTestReports</code>. If the number of results is larger than the number you specified in this parameter, then the response includes a <code>NextToken</code> element, which you can use to obtain additional results.</p> <p>The value you specify has to be at least 0, and can be no more than 1000.</p>", "location": "querystring", "locationName": "PageSize"}}, "documentation": "<p>A request to list all of the predictive inbox placement tests that you've performed.</p>"}, "ListDeliverabilityTestReportsResponse": {"type": "structure", "required": ["DeliverabilityTestReports"], "members": {"DeliverabilityTestReports": {"shape": "DeliverabilityTestReports", "documentation": "<p>An object that contains a lists of predictive inbox placement tests that you've performed.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>A token that indicates that there are additional predictive inbox placement tests to list. To view additional predictive inbox placement tests, issue another request to <code>ListDeliverabilityTestReports</code>, and pass this token in the <code>NextToken</code> parameter.</p>"}}, "documentation": "<p>A list of the predictive inbox placement test reports that are available for your account, regardless of whether or not those tests are complete.</p>"}, "ListDomainDeliverabilityCampaignsRequest": {"type": "structure", "required": ["StartDate", "EndDate", "SubscribedDomain"], "members": {"StartDate": {"shape": "Timestamp", "documentation": "<p>The first day that you want to obtain deliverability data for.</p>", "location": "querystring", "locationName": "StartDate"}, "EndDate": {"shape": "Timestamp", "documentation": "<p>The last day that you want to obtain deliverability data for. This value has to be less than or equal to 30 days after the value of the <code>StartDate</code> parameter.</p>", "location": "querystring", "locationName": "EndDate"}, "SubscribedDomain": {"shape": "Domain", "documentation": "<p>The domain to obtain deliverability data for.</p>", "location": "uri", "locationName": "SubscribedDomain"}, "NextToken": {"shape": "NextToken", "documentation": "<p>A token that’s returned from a previous call to the <code>ListDomainDeliverabilityCampaigns</code> operation. This token indicates the position of a campaign in the list of campaigns.</p>", "location": "querystring", "locationName": "NextToken"}, "PageSize": {"shape": "MaxItems", "documentation": "<p>The maximum number of results to include in response to a single call to the <code>ListDomainDeliverabilityCampaigns</code> operation. If the number of results is larger than the number that you specify in this parameter, the response includes a <code>NextToken</code> element, which you can use to obtain additional results.</p>", "location": "querystring", "locationName": "PageSize"}}, "documentation": "<p>Retrieve deliverability data for all the campaigns that used a specific domain to send email during a specified time range. This data is available for a domain only if you enabled the Deliverability dashboard.</p>"}, "ListDomainDeliverabilityCampaignsResponse": {"type": "structure", "required": ["DomainDeliverabilityCampaigns"], "members": {"DomainDeliverabilityCampaigns": {"shape": "DomainDeliverabilityCampaignList", "documentation": "<p>An array of responses, one for each campaign that used the domain to send email during the specified time range.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>A token that’s returned from a previous call to the <code>ListDomainDeliverabilityCampaigns</code> operation. This token indicates the position of the campaign in the list of campaigns.</p>"}}, "documentation": "<p>An array of objects that provide deliverability data for all the campaigns that used a specific domain to send email during a specified time range. This data is available for a domain only if you enabled the Deliverability dashboard for the domain.</p>"}, "ListEmailIdentitiesRequest": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>A token returned from a previous call to <code>ListEmailIdentities</code> to indicate the position in the list of identities.</p>", "location": "querystring", "locationName": "NextToken"}, "PageSize": {"shape": "MaxItems", "documentation": "<p>The number of results to show in a single call to <code>ListEmailIdentities</code>. If the number of results is larger than the number you specified in this parameter, then the response includes a <code>NextToken</code> element, which you can use to obtain additional results.</p> <p>The value you specify has to be at least 0, and can be no more than 1000.</p>", "location": "querystring", "locationName": "PageSize"}}, "documentation": "<p>A request to list all of the email identities associated with your Amazon Web Services account. This list includes identities that you've already verified, identities that are unverified, and identities that were verified in the past, but are no longer verified.</p>"}, "ListEmailIdentitiesResponse": {"type": "structure", "members": {"EmailIdentities": {"shape": "IdentityInfoList", "documentation": "<p>An array that includes all of the email identities associated with your Amazon Web Services account.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>A token that indicates that there are additional configuration sets to list. To view additional configuration sets, issue another request to <code>ListEmailIdentities</code>, and pass this token in the <code>NextToken</code> parameter.</p>"}}, "documentation": "<p>A list of all of the identities that you've attempted to verify, regardless of whether or not those identities were successfully verified.</p>"}, "ListEmailTemplatesRequest": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>A token returned from a previous call to <code>ListEmailTemplates</code> to indicate the position in the list of email templates.</p>", "location": "querystring", "locationName": "NextToken"}, "PageSize": {"shape": "MaxItems", "documentation": "<p>The number of results to show in a single call to <code>ListEmailTemplates</code>. If the number of results is larger than the number you specified in this parameter, then the response includes a <code>NextToken</code> element, which you can use to obtain additional results.</p> <p>The value you specify has to be at least 1, and can be no more than 100.</p>", "location": "querystring", "locationName": "PageSize"}}, "documentation": "<p>Represents a request to list the email templates present in your Amazon SES account in the current Amazon Web Services Region. For more information, see the <a href=\"https://docs.aws.amazon.com/ses/latest/DeveloperGuide/send-personalized-email-api.html\">Amazon SES Developer Guide</a>.</p>"}, "ListEmailTemplatesResponse": {"type": "structure", "members": {"TemplatesMetadata": {"shape": "EmailTemplateMetadataList", "documentation": "<p>An array the contains the name and creation time stamp for each template in your Amazon SES account.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>A token indicating that there are additional email templates available to be listed. Pass this token to a subsequent <code>ListEmailTemplates</code> call to retrieve the next 10 email templates.</p>"}}, "documentation": "<p>The following elements are returned by the service.</p>"}, "ListExportJobsRequest": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>The pagination token returned from a previous call to <code>ListExportJobs</code> to indicate the position in the list of export jobs.</p>"}, "PageSize": {"shape": "MaxItems", "documentation": "<p>Maximum number of export jobs to return at once. Use this parameter to paginate results. If additional export jobs exist beyond the specified limit, the <code>NextToken</code> element is sent in the response. Use the <code>NextToken</code> value in subsequent calls to <code>ListExportJobs</code> to retrieve additional export jobs.</p>"}, "ExportSourceType": {"shape": "ExportSourceType", "documentation": "<p>A value used to list export jobs that have a certain <code>ExportSourceType</code>.</p>"}, "JobStatus": {"shape": "JobStatus", "documentation": "<p>A value used to list export jobs that have a certain <code>JobStatus</code>.</p>"}}, "documentation": "<p>Represents a request to list all export jobs with filters.</p>"}, "ListExportJobsResponse": {"type": "structure", "members": {"ExportJobs": {"shape": "ExportJobSummaryList", "documentation": "<p>A list of the export job summaries.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>A string token indicating that there might be additional export jobs available to be listed. Use this token to a subsequent call to <code>ListExportJobs</code> with the same parameters to retrieve the next page of export jobs.</p>"}}, "documentation": "<p>An HTTP 200 response if the request succeeds, or an error message if the request fails.</p>"}, "ListImportJobsRequest": {"type": "structure", "members": {"ImportDestinationType": {"shape": "ImportDestinationType", "documentation": "<p>The destination of the import job, which can be used to list import jobs that have a certain <code>ImportDestinationType</code>.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>A string token indicating that there might be additional import jobs available to be listed. Copy this token to a subsequent call to <code>ListImportJobs</code> with the same parameters to retrieve the next page of import jobs.</p>"}, "PageSize": {"shape": "MaxItems", "documentation": "<p>Maximum number of import jobs to return at once. Use this parameter to paginate results. If additional import jobs exist beyond the specified limit, the <code>NextToken</code> element is sent in the response. Use the <code>NextToken</code> value in subsequent requests to retrieve additional addresses.</p>"}}, "documentation": "<p>Represents a request to list all of the import jobs for a data destination within the specified maximum number of import jobs.</p>"}, "ListImportJobsResponse": {"type": "structure", "members": {"ImportJobs": {"shape": "ImportJobSummaryList", "documentation": "<p>A list of the import job summaries.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>A string token indicating that there might be additional import jobs available to be listed. Copy this token to a subsequent call to <code>ListImportJobs</code> with the same parameters to retrieve the next page of import jobs.</p>"}}, "documentation": "<p>An HTTP 200 response if the request succeeds, or an error message if the request fails.</p>"}, "ListManagementOptions": {"type": "structure", "required": ["ContactListName"], "members": {"ContactListName": {"shape": "ContactListName", "documentation": "<p>The name of the contact list.</p>"}, "TopicName": {"shape": "TopicName", "documentation": "<p>The name of the topic.</p>"}}, "documentation": "<p>An object used to specify a list or topic to which an email belongs, which will be used when a contact chooses to unsubscribe.</p>"}, "ListMultiRegionEndpointsRequest": {"type": "structure", "members": {"NextToken": {"shape": "NextTokenV2", "documentation": "<p>A token returned from a previous call to <code>ListMultiRegionEndpoints</code> to indicate the position in the list of multi-region endpoints (global-endpoints).</p>", "location": "querystring", "locationName": "NextToken"}, "PageSize": {"shape": "PageSizeV2", "documentation": "<p>The number of results to show in a single call to <code>ListMultiRegionEndpoints</code>. If the number of results is larger than the number you specified in this parameter, the response includes a <code>NextToken</code> element that you can use to retrieve the next page of results. </p>", "location": "querystring", "locationName": "PageSize"}}, "documentation": "<p>Represents a request to list all the multi-region endpoints (global-endpoints) whose primary region is the AWS-Region where operation is executed. </p>"}, "ListMultiRegionEndpointsResponse": {"type": "structure", "members": {"MultiRegionEndpoints": {"shape": "MultiRegionEndpoints", "documentation": "<p>An array that contains key multi-region endpoint (global-endpoint) properties.</p>"}, "NextToken": {"shape": "NextTokenV2", "documentation": "<p>A token indicating that there are additional multi-region endpoints (global-endpoints) available to be listed. Pass this token to a subsequent <code>ListMultiRegionEndpoints</code> call to retrieve the next page.</p>"}}, "documentation": "<p>The following elements are returned by the service.</p>"}, "ListOfContactLists": {"type": "list", "member": {"shape": "ContactList"}}, "ListOfContacts": {"type": "list", "member": {"shape": "Contact"}}, "ListOfDedicatedIpPools": {"type": "list", "member": {"shape": "PoolName"}, "documentation": "<p>A list of dedicated IP pools that are associated with your Amazon Web Services account.</p>"}, "ListRecommendationFilterValue": {"type": "string", "max": 512, "min": 1}, "ListRecommendationsFilter": {"type": "map", "key": {"shape": "ListRecommendationsFilterKey"}, "value": {"shape": "ListRecommendationFilterValue"}, "documentation": "<p>An object that contains mapping between <code>ListRecommendationsFilterKey</code> and <code>ListRecommendationFilterValue</code> to filter by.</p>", "max": 2, "min": 1}, "ListRecommendationsFilterKey": {"type": "string", "documentation": "<p>The <code>ListRecommendations</code> filter type. This can be one of the following:</p> <ul> <li> <p> <code>TYPE</code> – The recommendation type, with values like <code>DKIM</code>, <code>SPF</code>, <code>DMARC</code>, <code>BIMI</code>, or <code>COMPLAINT</code>.</p> </li> <li> <p> <code>IMPACT</code> – The recommendation impact, with values like <code>HIGH</code> or <code>LOW</code>.</p> </li> <li> <p> <code>STATUS</code> – The recommendation status, with values like <code>OPEN</code> or <code>FIXED</code>.</p> </li> <li> <p> <code>RESOURCE_ARN</code> – The resource affected by the recommendation, with values like <code>arn:aws:ses:us-east-1:123456789012:identity/example.com</code>.</p> </li> </ul>", "enum": ["TYPE", "IMPACT", "STATUS", "RESOURCE_ARN"]}, "ListRecommendationsRequest": {"type": "structure", "members": {"Filter": {"shape": "ListRecommendationsFilter", "documentation": "<p>Filters applied when retrieving recommendations. Can eiter be an individual filter, or combinations of <code>STATUS</code> and <code>IMPACT</code> or <code>STATUS</code> and <code>TYPE</code> </p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>A token returned from a previous call to <code>ListRecommendations</code> to indicate the position in the list of recommendations.</p>"}, "PageSize": {"shape": "MaxItems", "documentation": "<p>The number of results to show in a single call to <code>ListRecommendations</code>. If the number of results is larger than the number you specified in this parameter, then the response includes a <code>NextToken</code> element, which you can use to obtain additional results.</p> <p>The value you specify has to be at least 1, and can be no more than 100.</p>"}}, "documentation": "<p>Represents a request to list the existing recommendations for your account.</p>"}, "ListRecommendationsResponse": {"type": "structure", "members": {"Recommendations": {"shape": "RecommendationsList", "documentation": "<p>The recommendations applicable to your account.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>A string token indicating that there might be additional recommendations available to be listed. Use the token provided in the <code>ListRecommendationsResponse</code> to use in the subsequent call to <code>ListRecommendations</code> with the same parameters to retrieve the next page of recommendations.</p>"}}, "documentation": "<p>Contains the response to your request to retrieve the list of recommendations for your account.</p>"}, "ListSuppressedDestinationsRequest": {"type": "structure", "members": {"Reasons": {"shape": "SuppressionListReasons", "documentation": "<p>The factors that caused the email address to be added to .</p>", "location": "querystring", "locationName": "Reason"}, "StartDate": {"shape": "Timestamp", "documentation": "<p>Used to filter the list of suppressed email destinations so that it only includes addresses that were added to the list after a specific date.</p>", "location": "querystring", "locationName": "StartDate"}, "EndDate": {"shape": "Timestamp", "documentation": "<p>Used to filter the list of suppressed email destinations so that it only includes addresses that were added to the list before a specific date.</p>", "location": "querystring", "locationName": "EndDate"}, "NextToken": {"shape": "NextToken", "documentation": "<p>A token returned from a previous call to <code>ListSuppressedDestinations</code> to indicate the position in the list of suppressed email addresses.</p>", "location": "querystring", "locationName": "NextToken"}, "PageSize": {"shape": "MaxItems", "documentation": "<p>The number of results to show in a single call to <code>ListSuppressedDestinations</code>. If the number of results is larger than the number you specified in this parameter, then the response includes a <code>NextToken</code> element, which you can use to obtain additional results.</p>", "location": "querystring", "locationName": "PageSize"}}, "documentation": "<p>A request to obtain a list of email destinations that are on the suppression list for your account.</p>"}, "ListSuppressedDestinationsResponse": {"type": "structure", "members": {"SuppressedDestinationSummaries": {"shape": "SuppressedDestinationSummaries", "documentation": "<p>A list of summaries, each containing a summary for a suppressed email destination.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>A token that indicates that there are additional email addresses on the suppression list for your account. To view additional suppressed addresses, issue another request to <code>ListSuppressedDestinations</code>, and pass this token in the <code>NextToken</code> parameter.</p>"}}, "documentation": "<p>A list of suppressed email addresses.</p>"}, "ListTagsForResourceRequest": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "AmazonResourceName", "documentation": "<p>The Amazon Resource Name (ARN) of the resource that you want to retrieve tag information for.</p>", "location": "querystring", "locationName": "ResourceArn"}}}, "ListTagsForResourceResponse": {"type": "structure", "required": ["Tags"], "members": {"Tags": {"shape": "TagList", "documentation": "<p>An array that lists all the tags that are associated with the resource. Each tag consists of a required tag key (<code>Key</code>) and an associated tag value (<code>Value</code>)</p>"}}}, "MailFromAttributes": {"type": "structure", "required": ["MailFromDomain", "MailFromDomainStatus", "BehaviorOnMxFailure"], "members": {"MailFromDomain": {"shape": "MailFromDomainName", "documentation": "<p>The name of a domain that an email identity uses as a custom MAIL FROM domain.</p>"}, "MailFromDomainStatus": {"shape": "MailFromDomainStatus", "documentation": "<p>The status of the MAIL FROM domain. This status can have the following values:</p> <ul> <li> <p> <code>PENDING</code> – Amazon SES hasn't started searching for the MX record yet.</p> </li> <li> <p> <code>SUCCESS</code> – Amazon SES detected the required MX record for the MAIL FROM domain.</p> </li> <li> <p> <code>FAILED</code> – Amazon SES can't find the required MX record, or the record no longer exists.</p> </li> <li> <p> <code>TEMPORARY_FAILURE</code> – A temporary issue occurred, which prevented Amazon SES from determining the status of the MAIL FROM domain.</p> </li> </ul>"}, "BehaviorOnMxFailure": {"shape": "BehaviorOnMxFailure", "documentation": "<p>The action to take if the required MX record can't be found when you send an email. When you set this value to <code>USE_DEFAULT_VALUE</code>, the mail is sent using <i>amazonses.com</i> as the MAIL FROM domain. When you set this value to <code>REJECT_MESSAGE</code>, the Amazon SES API v2 returns a <code>MailFromDomainNotVerified</code> error, and doesn't attempt to deliver the email.</p> <p>These behaviors are taken when the custom MAIL FROM domain configuration is in the <code>Pending</code>, <code>Failed</code>, and <code>TemporaryFailure</code> states.</p>"}}, "documentation": "<p>A list of attributes that are associated with a MAIL FROM domain.</p>"}, "MailFromDomainName": {"type": "string", "documentation": "<p>The domain to use as a MAIL FROM domain.</p>"}, "MailFromDomainNotVerifiedException": {"type": "structure", "members": {}, "documentation": "<p>The message can't be sent because the sending domain isn't verified.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "MailFromDomainStatus": {"type": "string", "documentation": "<p>The status of the MAIL FROM domain. This status can have the following values:</p> <ul> <li> <p> <code>PENDING</code> – Amazon SES hasn't started searching for the MX record yet.</p> </li> <li> <p> <code>SUCCESS</code> – Amazon SES detected the required MX record for the MAIL FROM domain.</p> </li> <li> <p> <code>FAILED</code> – Amazon SES can't find the required MX record, or the record no longer exists.</p> </li> <li> <p> <code>TEMPORARY_FAILURE</code> – A temporary issue occurred, which prevented Amazon SES from determining the status of the MAIL FROM domain.</p> </li> </ul>", "enum": ["PENDING", "SUCCESS", "FAILED", "TEMPORARY_FAILURE"]}, "MailType": {"type": "string", "enum": ["MARKETING", "TRANSACTIONAL"]}, "Max24HourSend": {"type": "double"}, "MaxDeliverySeconds": {"type": "long", "max": 50400, "min": 300}, "MaxItems": {"type": "integer"}, "MaxSendRate": {"type": "double"}, "Message": {"type": "structure", "required": ["Subject", "Body"], "members": {"Subject": {"shape": "Content", "documentation": "<p>The subject line of the email. The subject line can only contain 7-bit ASCII characters. However, you can specify non-ASCII characters in the subject line by using encoded-word syntax, as described in <a href=\"https://tools.ietf.org/html/rfc2047\">RFC 2047</a>.</p>"}, "Body": {"shape": "Body", "documentation": "<p>The body of the message. You can specify an HTML version of the message, a text-only version of the message, or both.</p>"}, "Headers": {"shape": "MessageHeaderList", "documentation": "<p>The list of message headers that will be added to the email message.</p>"}, "Attachments": {"shape": "AttachmentList", "documentation": "<p> The List of attachments to include in your email. All recipients will receive the same attachments.</p>"}}, "documentation": "<p>Represents the email message that you're sending. The <code>Message</code> object consists of a subject line and a message body.</p>"}, "MessageContent": {"type": "string", "documentation": "<p>The body of an email message.</p>"}, "MessageData": {"type": "string"}, "MessageHeader": {"type": "structure", "required": ["Name", "Value"], "members": {"Name": {"shape": "MessageHeaderName", "documentation": "<p>The name of the message header. The message header name has to meet the following criteria:</p> <ul> <li> <p>Can contain any printable ASCII character (33 - 126) except for colon (:).</p> </li> <li> <p>Can contain no more than 126 characters.</p> </li> </ul>"}, "Value": {"shape": "MessageHeaderValue", "documentation": "<p>The value of the message header. The message header value has to meet the following criteria:</p> <ul> <li> <p>Can contain any printable ASCII character.</p> </li> <li> <p>Can contain no more than 870 characters.</p> </li> </ul>"}}, "documentation": "<p>Contains the name and value of a message header that you add to an email.</p>"}, "MessageHeaderList": {"type": "list", "member": {"shape": "MessageHeader"}, "documentation": "<p>A list of message headers. The list of message headers has to meet the following criteria:</p> <ul> <li> <p>Can contain no more than 15 headers in one message.</p> </li> </ul>", "max": 15, "min": 0}, "MessageHeaderName": {"type": "string", "documentation": "<p>The name of the message header. The message header name has to meet the following criteria:</p> <ul> <li> <p>Can contain any printable ASCII character (33 - 126) except for colon (:).</p> </li> <li> <p>Can contain no more than 126 characters.</p> </li> </ul>", "max": 126, "min": 1, "pattern": "^[!-9;-@A-~]+$"}, "MessageHeaderValue": {"type": "string", "documentation": "<p>The value of the message header. The message header value has to meet the following criteria:</p> <ul> <li> <p>Can contain any printable ASCII character.</p> </li> </ul>", "max": 870, "min": 1, "pattern": "[ -~]*"}, "MessageInsightsDataSource": {"type": "structure", "required": ["StartDate", "EndDate"], "members": {"StartDate": {"shape": "Timestamp", "documentation": "<p>Represents the start date for the export interval as a timestamp. The start date is inclusive.</p>"}, "EndDate": {"shape": "Timestamp", "documentation": "<p>Represents the end date for the export interval as a timestamp. The end date is inclusive.</p>"}, "Include": {"shape": "MessageInsightsFilters", "documentation": "<p>Filters for results to be included in the export file.</p>"}, "Exclude": {"shape": "MessageInsightsFilters", "documentation": "<p>Filters for results to be excluded from the export file.</p>"}, "MaxResults": {"shape": "MessageInsightsExportMaxResults", "documentation": "<p>The maximum number of results.</p>"}}, "documentation": "<p>An object that contains filters applied when performing the Message Insights export.</p>"}, "MessageInsightsExportMaxResults": {"type": "integer", "max": 10000, "min": 1}, "MessageInsightsFilters": {"type": "structure", "members": {"FromEmailAddress": {"shape": "EmailAddressFilterList", "documentation": "<p>The from address used to send the message.</p>"}, "Destination": {"shape": "EmailAddressFilterList", "documentation": "<p>The recipient's email address.</p>"}, "Subject": {"shape": "EmailSubjectFilterList", "documentation": "<p>The subject line of the message.</p>"}, "Isp": {"shape": "IspFilterList", "documentation": "<p>The recipient's ISP (e.g., <code>Gmail</code>, <code>Yahoo</code>, etc.).</p>"}, "LastDeliveryEvent": {"shape": "LastDeliveryEventList", "documentation": "<p> The last delivery-related event for the email, where the ordering is as follows: <code>SEND</code> &lt; <code>BOUNCE</code> &lt; <code>DELIVERY</code> &lt; <code>COMPLAINT</code>. </p>"}, "LastEngagementEvent": {"shape": "LastEngagementEventList", "documentation": "<p> The last engagement-related event for the email, where the ordering is as follows: <code>OPEN</code> &lt; <code>CLICK</code>. </p> <p> Engagement events are only available if <a href=\"https://docs.aws.amazon.com/ses/latest/dg/vdm-settings.html\">Engagement tracking</a> is enabled. </p>"}}, "documentation": "<p>An object containing Message Insights filters.</p> <p>If you specify multiple filters, the filters are joined by AND.</p> <p>If you specify multiple values for a filter, the values are joined by OR. Filter values are case-sensitive.</p> <p> <code>FromEmailAddress</code>, <code>Destination</code>, and <code>Subject</code> filters support partial match. A partial match is performed by using the <code>*</code> wildcard character placed at the beginning (suffix match), the end (prefix match) or both ends of the string (contains match). In order to match the literal characters <code>*</code> or <code>\\</code>, they must be escaped using the <code>\\</code> character. If no wildcard character is present, an exact match is performed. </p>"}, "MessageRejected": {"type": "structure", "members": {}, "documentation": "<p>The message can't be sent because it contains invalid content.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "MessageTag": {"type": "structure", "required": ["Name", "Value"], "members": {"Name": {"shape": "MessageTagName", "documentation": "<p>The name of the message tag. The message tag name has to meet the following criteria:</p> <ul> <li> <p>It can only contain ASCII letters (a–z, A–Z), numbers (0–9), underscores (_), or dashes (-).</p> </li> <li> <p>It can contain no more than 256 characters.</p> </li> </ul>"}, "Value": {"shape": "MessageTagValue", "documentation": "<p>The value of the message tag. The message tag value has to meet the following criteria:</p> <ul> <li> <p>It can only contain ASCII letters (a–z, A–Z), numbers (0–9), underscores (_), or dashes (-).</p> </li> <li> <p>It can contain no more than 256 characters.</p> </li> </ul>"}}, "documentation": "<p>Contains the name and value of a tag that you apply to an email. You can use message tags when you publish email sending events. </p>"}, "MessageTagList": {"type": "list", "member": {"shape": "MessageTag"}, "documentation": "<p>A list of message tags.</p>"}, "MessageTagName": {"type": "string", "documentation": "<p>The name of the message tag. The message tag name has to meet the following criteria:</p> <ul> <li> <p>It can only contain ASCII letters (a–z, A–Z), numbers (0–9), underscores (_), or dashes (-).</p> </li> <li> <p>It can contain no more than 256 characters.</p> </li> </ul>"}, "MessageTagValue": {"type": "string", "documentation": "<p>The value of the message tag. The message tag value has to meet the following criteria:</p> <ul> <li> <p>It can only contain ASCII letters (a–z, A–Z), numbers (0–9), underscores (_), or dashes (-).</p> </li> <li> <p>It can contain no more than 256 characters.</p> </li> </ul>"}, "Metric": {"type": "string", "documentation": "<p>The metric to export, can be one of the following:</p> <ul> <li> <p> <code>SEND</code> - Emails sent eligible for tracking in the VDM dashboard. This excludes emails sent to the mailbox simulator and emails addressed to more than one recipient.</p> </li> <li> <p> <code>COMPLAINT</code> - Complaints received for your account. This excludes complaints from the mailbox simulator, those originating from your account-level suppression list (if enabled), and those for emails addressed to more than one recipient</p> </li> <li> <p> <code>PERMANENT_BOUNCE</code> - Permanent bounces - i.e., feedback received for emails sent to non-existent mailboxes. Excludes bounces from the mailbox simulator, those originating from your account-level suppression list (if enabled), and those for emails addressed to more than one recipient.</p> </li> <li> <p> <code>TRANSIENT_BOUNCE</code> - Transient bounces - i.e., feedback received for delivery failures excluding issues with non-existent mailboxes. Excludes bounces from the mailbox simulator, and those for emails addressed to more than one recipient.</p> </li> <li> <p> <code>OPEN</code> - Unique open events for emails including open trackers. Excludes opens for emails addressed to more than one recipient.</p> </li> <li> <p> <code>CLICK</code> - Unique click events for emails including wrapped links. Excludes clicks for emails addressed to more than one recipient.</p> </li> <li> <p> <code>DELIVERY</code> - Successful deliveries for email sending attempts. Excludes deliveries to the mailbox simulator and for emails addressed to more than one recipient.</p> </li> <li> <p> <code>DELIVERY_OPEN</code> - Successful deliveries for email sending attempts. Excludes deliveries to the mailbox simulator, for emails addressed to more than one recipient, and emails without open trackers.</p> </li> <li> <p> <code>DELIVERY_CLICK</code> - Successful deliveries for email sending attempts. Excludes deliveries to the mailbox simulator, for emails addressed to more than one recipient, and emails without click trackers.</p> </li> <li> <p> <code>DELIVERY_COMPLAINT</code> - Successful deliveries for email sending attempts. Excludes deliveries to the mailbox simulator, for emails addressed to more than one recipient, and emails addressed to recipients hosted by ISPs with which Amazon SES does not have a feedback loop agreement.</p> </li> </ul>", "enum": ["SEND", "COMPLAINT", "PERMANENT_BOUNCE", "TRANSIENT_BOUNCE", "OPEN", "CLICK", "DELIVERY", "DELIVERY_OPEN", "DELIVERY_CLICK", "DELIVERY_COMPLAINT"]}, "MetricAggregation": {"type": "string", "documentation": "<p>The aggregation to apply to a metric, can be one of the following:</p> <ul> <li> <p> <code>VOLUME</code> - The volume of events for this metric.</p> </li> <li> <p> <code>RATE</code> - The rate for this metric relative to the <code>SEND</code> metric volume.</p> </li> </ul>", "enum": ["RATE", "VOLUME"]}, "MetricDataError": {"type": "structure", "members": {"Id": {"shape": "QueryIdentifier", "documentation": "<p>The query identifier.</p>"}, "Code": {"shape": "QueryErrorCode", "documentation": "<p>The query error code. Can be one of:</p> <ul> <li> <p> <code>INTERNAL_FAILURE</code> – Amazon SES has failed to process one of the queries.</p> </li> <li> <p> <code>ACCESS_DENIED</code> – You have insufficient access to retrieve metrics based on the given query.</p> </li> </ul>"}, "Message": {"shape": "QueryErrorMessage", "documentation": "<p>The error message associated with the current query error.</p>"}}, "documentation": "<p>An error corresponding to the unsuccessful processing of a single metric data query.</p>"}, "MetricDataErrorList": {"type": "list", "member": {"shape": "MetricDataError"}}, "MetricDataResult": {"type": "structure", "members": {"Id": {"shape": "QueryIdentifier", "documentation": "<p>The query identifier.</p>"}, "Timestamps": {"shape": "TimestampList", "documentation": "<p>A list of timestamps for the metric data results.</p>"}, "Values": {"shape": "MetricValueList", "documentation": "<p>A list of values (cumulative / sum) for the metric data results.</p>"}}, "documentation": "<p>The result of a single metric data query.</p>"}, "MetricDataResultList": {"type": "list", "member": {"shape": "MetricDataResult"}}, "MetricDimensionName": {"type": "string", "documentation": "<p>The <code>BatchGetMetricDataQuery</code> dimension name. This can be one of the following:</p> <ul> <li> <p> <code>EMAIL_IDENTITY</code> – The email identity used when sending messages.</p> </li> <li> <p> <code>CONFIGURATION_SET</code> – The configuration set used when sending messages (if one was used).</p> </li> <li> <p> <code>ISP</code> – The recipient ISP (e.g. <code>Gmail</code>, <code>Yahoo</code>, etc.).</p> </li> </ul>", "enum": ["EMAIL_IDENTITY", "CONFIGURATION_SET", "ISP"]}, "MetricDimensionValue": {"type": "string", "documentation": "<p>A list of values associated with the <code>MetricDimensionName</code> to filter metrics by. Can either be <code>*</code> as a wildcard for all values or a list of up to 10 specific values. If one <code>Dimension</code> has the <code>*</code> value, other dimensions can only contain one value. </p>"}, "MetricNamespace": {"type": "string", "enum": ["VDM"]}, "MetricValueList": {"type": "list", "member": {"shape": "Counter"}}, "MetricsDataSource": {"type": "structure", "required": ["Dimensions", "Namespace", "Metrics", "StartDate", "EndDate"], "members": {"Dimensions": {"shape": "ExportDimensions", "documentation": "<p>An object that contains a mapping between a <code>MetricDimensionName</code> and <code>MetricDimensionValue</code> to filter metrics by. Must contain a least 1 dimension but no more than 3 unique ones.</p>"}, "Namespace": {"shape": "MetricNamespace", "documentation": "<p>The metrics namespace - e.g., <code>VDM</code>.</p>"}, "Metrics": {"shape": "ExportMetrics", "documentation": "<p>A list of <code>ExportMetric</code> objects to export.</p>"}, "StartDate": {"shape": "Timestamp", "documentation": "<p>Represents the start date for the export interval as a timestamp.</p>"}, "EndDate": {"shape": "Timestamp", "documentation": "<p>Represents the end date for the export interval as a timestamp.</p>"}}, "documentation": "<p>An object that contains details about the data source for the metrics export.</p>"}, "MultiRegionEndpoint": {"type": "structure", "members": {"EndpointName": {"shape": "EndpointName", "documentation": "<p>The name of the multi-region endpoint (global-endpoint).</p>"}, "Status": {"shape": "Status", "documentation": "<p>The status of the multi-region endpoint (global-endpoint).</p> <ul> <li> <p> <code>CREATING</code> – The resource is being provisioned.</p> </li> <li> <p> <code>READY</code> – The resource is ready to use.</p> </li> <li> <p> <code>FAILED</code> – The resource failed to be provisioned.</p> </li> <li> <p> <code>DELETING</code> – The resource is being deleted as requested.</p> </li> </ul>"}, "EndpointId": {"shape": "EndpointId", "documentation": "<p>The ID of the multi-region endpoint (global-endpoint).</p>"}, "Regions": {"shape": "Regions", "documentation": "<p>Primary and secondary regions between which multi-region endpoint splits sending traffic.</p>"}, "CreatedTimestamp": {"shape": "Timestamp", "documentation": "<p>The time stamp of when the multi-region endpoint (global-endpoint) was created.</p>"}, "LastUpdatedTimestamp": {"shape": "Timestamp", "documentation": "<p>The time stamp of when the multi-region endpoint (global-endpoint) was last updated.</p>"}}, "documentation": "<p>An object that contains multi-region endpoint (global-endpoint) properties.</p>"}, "MultiRegionEndpoints": {"type": "list", "member": {"shape": "MultiRegionEndpoint"}}, "NextToken": {"type": "string"}, "NextTokenV2": {"type": "string", "max": 5000, "min": 1, "pattern": "^^([A-Za-z0-9+/]{4})*([A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$"}, "NotFoundException": {"type": "structure", "members": {}, "documentation": "<p>The resource you attempted to access doesn't exist.</p>", "error": {"httpStatusCode": 404}, "exception": true}, "OutboundMessageId": {"type": "string"}, "OverallVolume": {"type": "structure", "members": {"VolumeStatistics": {"shape": "VolumeStatistics", "documentation": "<p>An object that contains information about the numbers of messages that arrived in recipients' inboxes and junk mail folders.</p>"}, "ReadRatePercent": {"shape": "Percentage", "documentation": "<p>The percentage of emails that were sent from the domain that were read by their recipients.</p>"}, "DomainIspPlacements": {"shape": "DomainIspPlacements", "documentation": "<p>An object that contains inbox and junk mail placement metrics for individual email providers.</p>"}}, "documentation": "<p>An object that contains information about email that was sent from the selected domain.</p>"}, "PageSizeV2": {"type": "integer", "max": 1000, "min": 1}, "Percentage": {"type": "double", "documentation": "<p>An object that contains information about inbox placement percentages.</p>"}, "Percentage100Wrapper": {"type": "integer"}, "PinpointDestination": {"type": "structure", "members": {"ApplicationArn": {"shape": "AmazonResourceName", "documentation": "<p>The Amazon Resource Name (ARN) of the Amazon Pinpoint project to send email events to.</p>"}}, "documentation": "<p>An object that defines an Amazon Pinpoint project destination for email events. You can send email event data to a Amazon Pinpoint project to view metrics using the Transactional Messaging dashboards that are built in to Amazon Pinpoint. For more information, see <a href=\"https://docs.aws.amazon.com/pinpoint/latest/userguide/analytics-transactional-messages.html\">Transactional Messaging Charts</a> in the <i>Amazon Pinpoint User Guide</i>.</p>"}, "PlacementStatistics": {"type": "structure", "members": {"InboxPercentage": {"shape": "Percentage", "documentation": "<p>The percentage of emails that arrived in recipients' inboxes during the predictive inbox placement test.</p>"}, "SpamPercentage": {"shape": "Percentage", "documentation": "<p>The percentage of emails that arrived in recipients' spam or junk mail folders during the predictive inbox placement test.</p>"}, "MissingPercentage": {"shape": "Percentage", "documentation": "<p>The percentage of emails that didn't arrive in recipients' inboxes at all during the predictive inbox placement test.</p>"}, "SpfPercentage": {"shape": "Percentage", "documentation": "<p>The percentage of emails that were authenticated by using Sender Policy Framework (SPF) during the predictive inbox placement test.</p>"}, "DkimPercentage": {"shape": "Percentage", "documentation": "<p>The percentage of emails that were authenticated by using DomainKeys Identified Mail (DKIM) during the predictive inbox placement test.</p>"}}, "documentation": "<p>An object that contains inbox placement data for an email provider.</p>"}, "Policy": {"type": "string", "documentation": "<p>The text of the policy in JSON format. The policy cannot exceed 4 KB.</p> <p>For information about the syntax of sending authorization policies, see the <a href=\"https://docs.aws.amazon.com/ses/latest/DeveloperGuide/sending-authorization.html\">Amazon SES Developer Guide</a>.</p>", "min": 1}, "PolicyMap": {"type": "map", "key": {"shape": "PolicyName"}, "value": {"shape": "Policy"}, "documentation": "<p>An object that contains mapping between <code>PolicyName</code> and <code>Policy</code> text.</p>"}, "PolicyName": {"type": "string", "documentation": "<p>The name of the policy.</p> <p>The policy name cannot exceed 64 characters and can only include alphanumeric characters, dashes, and underscores.</p>", "max": 64, "min": 1}, "PoolName": {"type": "string", "documentation": "<p>The name of a dedicated IP pool.</p>"}, "PrimaryNameServer": {"type": "string"}, "PrivateKey": {"type": "string", "max": 20480, "min": 1, "pattern": "^[a-zA-Z0-9+\\/]+={0,2}$", "sensitive": true}, "ProcessedRecordsCount": {"type": "integer"}, "PutAccountDedicatedIpWarmupAttributesRequest": {"type": "structure", "members": {"AutoWarmupEnabled": {"shape": "Enabled", "documentation": "<p>Enables or disables the automatic warm-up feature for dedicated IP addresses that are associated with your Amazon SES account in the current Amazon Web Services Region. Set to <code>true</code> to enable the automatic warm-up feature, or set to <code>false</code> to disable it.</p>"}}, "documentation": "<p>A request to enable or disable the automatic IP address warm-up feature.</p>"}, "PutAccountDedicatedIpWarmupAttributesResponse": {"type": "structure", "members": {}, "documentation": "<p>An HTTP 200 response if the request succeeds, or an error message if the request fails.</p>"}, "PutAccountDetailsRequest": {"type": "structure", "required": ["MailType", "WebsiteURL"], "members": {"MailType": {"shape": "MailType", "documentation": "<p>The type of email your account will send.</p>"}, "WebsiteURL": {"shape": "WebsiteURL", "documentation": "<p>The URL of your website. This information helps us better understand the type of content that you plan to send.</p>"}, "ContactLanguage": {"shape": "ContactLanguage", "documentation": "<p>The language you would prefer to be contacted with.</p>"}, "UseCaseDescription": {"shape": "UseCaseDescription", "documentation": "<p>A description of the types of email that you plan to send.</p>"}, "AdditionalContactEmailAddresses": {"shape": "AdditionalContactEmailAddresses", "documentation": "<p>Additional email addresses that you would like to be notified regarding Amazon SES matters.</p>"}, "ProductionAccessEnabled": {"shape": "EnabledWrapper", "documentation": "<p>Indicates whether or not your account should have production access in the current Amazon Web Services Region.</p> <p>If the value is <code>false</code>, then your account is in the <i>sandbox</i>. When your account is in the sandbox, you can only send email to verified identities. </p> <p>If the value is <code>true</code>, then your account has production access. When your account has production access, you can send email to any address. The sending quota and maximum sending rate for your account vary based on your specific use case.</p>"}}, "documentation": "<p>A request to submit new account details.</p>"}, "PutAccountDetailsResponse": {"type": "structure", "members": {}, "documentation": "<p>An HTTP 200 response if the request succeeds, or an error message if the request fails.</p>"}, "PutAccountSendingAttributesRequest": {"type": "structure", "members": {"SendingEnabled": {"shape": "Enabled", "documentation": "<p>Enables or disables your account's ability to send email. Set to <code>true</code> to enable email sending, or set to <code>false</code> to disable email sending.</p> <note> <p>If Amazon Web Services paused your account's ability to send email, you can't use this operation to resume your account's ability to send email.</p> </note>"}}, "documentation": "<p>A request to change the ability of your account to send email.</p>"}, "PutAccountSendingAttributesResponse": {"type": "structure", "members": {}, "documentation": "<p>An HTTP 200 response if the request succeeds, or an error message if the request fails.</p>"}, "PutAccountSuppressionAttributesRequest": {"type": "structure", "members": {"SuppressedReasons": {"shape": "SuppressionListReasons", "documentation": "<p>A list that contains the reasons that email addresses will be automatically added to the suppression list for your account. This list can contain any or all of the following:</p> <ul> <li> <p> <code>COMPLAINT</code> – Amazon SES adds an email address to the suppression list for your account when a message sent to that address results in a complaint.</p> </li> <li> <p> <code>BOUNCE</code> – Amazon SES adds an email address to the suppression list for your account when a message sent to that address results in a hard bounce.</p> </li> </ul>"}}, "documentation": "<p>A request to change your account's suppression preferences.</p>"}, "PutAccountSuppressionAttributesResponse": {"type": "structure", "members": {}, "documentation": "<p>An HTTP 200 response if the request succeeds, or an error message if the request fails.</p>"}, "PutAccountVdmAttributesRequest": {"type": "structure", "required": ["VdmAttributes"], "members": {"VdmAttributes": {"shape": "VdmAttributes", "documentation": "<p>The VDM attributes that you wish to apply to your Amazon SES account.</p>"}}, "documentation": "<p>A request to submit new account VDM attributes.</p>"}, "PutAccountVdmAttributesResponse": {"type": "structure", "members": {}}, "PutConfigurationSetArchivingOptionsRequest": {"type": "structure", "required": ["ConfigurationSetName"], "members": {"ConfigurationSetName": {"shape": "ConfigurationSetName", "documentation": "<p>The name of the configuration set to associate with a MailManager archive.</p>", "location": "uri", "locationName": "ConfigurationSetName"}, "ArchiveArn": {"shape": "ArchiveArn", "documentation": "<p>The Amazon Resource Name (ARN) of the MailManager archive that the Amazon SES API v2 sends email to.</p>"}}, "documentation": "<p>A request to associate a configuration set with a MailManager archive.</p>"}, "PutConfigurationSetArchivingOptionsResponse": {"type": "structure", "members": {}, "documentation": "<p>An HTTP 200 response if the request succeeds, or an error message if the request fails.</p>"}, "PutConfigurationSetDeliveryOptionsRequest": {"type": "structure", "required": ["ConfigurationSetName"], "members": {"ConfigurationSetName": {"shape": "ConfigurationSetName", "documentation": "<p>The name of the configuration set to associate with a dedicated IP pool.</p>", "location": "uri", "locationName": "ConfigurationSetName"}, "TlsPolicy": {"shape": "TlsPolicy", "documentation": "<p>Specifies whether messages that use the configuration set are required to use Transport Layer Security (TLS). If the value is <code>Require</code>, messages are only delivered if a TLS connection can be established. If the value is <code>Optional</code>, messages can be delivered in plain text if a TLS connection can't be established.</p>"}, "SendingPoolName": {"shape": "SendingPoolName", "documentation": "<p>The name of the dedicated IP pool to associate with the configuration set.</p>"}, "MaxDeliverySeconds": {"shape": "MaxDeliverySeconds", "documentation": "<p>The maximum amount of time, in seconds, that Amazon SES API v2 will attempt delivery of email. If specified, the value must greater than or equal to 300 seconds (5 minutes) and less than or equal to 50400 seconds (840 minutes). </p>"}}, "documentation": "<p>A request to associate a configuration set with a dedicated IP pool.</p>"}, "PutConfigurationSetDeliveryOptionsResponse": {"type": "structure", "members": {}, "documentation": "<p>An HTTP 200 response if the request succeeds, or an error message if the request fails.</p>"}, "PutConfigurationSetReputationOptionsRequest": {"type": "structure", "required": ["ConfigurationSetName"], "members": {"ConfigurationSetName": {"shape": "ConfigurationSetName", "documentation": "<p>The name of the configuration set.</p>", "location": "uri", "locationName": "ConfigurationSetName"}, "ReputationMetricsEnabled": {"shape": "Enabled", "documentation": "<p>If <code>true</code>, tracking of reputation metrics is enabled for the configuration set. If <code>false</code>, tracking of reputation metrics is disabled for the configuration set.</p>"}}, "documentation": "<p>A request to enable or disable tracking of reputation metrics for a configuration set.</p>"}, "PutConfigurationSetReputationOptionsResponse": {"type": "structure", "members": {}, "documentation": "<p>An HTTP 200 response if the request succeeds, or an error message if the request fails.</p>"}, "PutConfigurationSetSendingOptionsRequest": {"type": "structure", "required": ["ConfigurationSetName"], "members": {"ConfigurationSetName": {"shape": "ConfigurationSetName", "documentation": "<p>The name of the configuration set to enable or disable email sending for.</p>", "location": "uri", "locationName": "ConfigurationSetName"}, "SendingEnabled": {"shape": "Enabled", "documentation": "<p>If <code>true</code>, email sending is enabled for the configuration set. If <code>false</code>, email sending is disabled for the configuration set.</p>"}}, "documentation": "<p>A request to enable or disable the ability of Amazon SES to send emails that use a specific configuration set.</p>"}, "PutConfigurationSetSendingOptionsResponse": {"type": "structure", "members": {}, "documentation": "<p>An HTTP 200 response if the request succeeds, or an error message if the request fails.</p>"}, "PutConfigurationSetSuppressionOptionsRequest": {"type": "structure", "required": ["ConfigurationSetName"], "members": {"ConfigurationSetName": {"shape": "ConfigurationSetName", "documentation": "<p>The name of the configuration set to change the suppression list preferences for.</p>", "location": "uri", "locationName": "ConfigurationSetName"}, "SuppressedReasons": {"shape": "SuppressionListReasons", "documentation": "<p>A list that contains the reasons that email addresses are automatically added to the suppression list for your account. This list can contain any or all of the following:</p> <ul> <li> <p> <code>COMPLAINT</code> – Amazon SES adds an email address to the suppression list for your account when a message sent to that address results in a complaint.</p> </li> <li> <p> <code>BOUNCE</code> – Amazon SES adds an email address to the suppression list for your account when a message sent to that address results in a hard bounce.</p> </li> </ul>"}}, "documentation": "<p>A request to change the account suppression list preferences for a specific configuration set.</p>"}, "PutConfigurationSetSuppressionOptionsResponse": {"type": "structure", "members": {}, "documentation": "<p>An HTTP 200 response if the request succeeds, or an error message if the request fails.</p>"}, "PutConfigurationSetTrackingOptionsRequest": {"type": "structure", "required": ["ConfigurationSetName"], "members": {"ConfigurationSetName": {"shape": "ConfigurationSetName", "documentation": "<p>The name of the configuration set.</p>", "location": "uri", "locationName": "ConfigurationSetName"}, "CustomRedirectDomain": {"shape": "CustomRedirectDomain", "documentation": "<p>The domain to use to track open and click events.</p>"}, "HttpsPolicy": {"shape": "HttpsPolicy"}}, "documentation": "<p>A request to add a custom domain for tracking open and click events to a configuration set.</p>"}, "PutConfigurationSetTrackingOptionsResponse": {"type": "structure", "members": {}, "documentation": "<p>An HTTP 200 response if the request succeeds, or an error message if the request fails.</p>"}, "PutConfigurationSetVdmOptionsRequest": {"type": "structure", "required": ["ConfigurationSetName"], "members": {"ConfigurationSetName": {"shape": "ConfigurationSetName", "documentation": "<p>The name of the configuration set.</p>", "location": "uri", "locationName": "ConfigurationSetName"}, "VdmOptions": {"shape": "VdmOptions", "documentation": "<p>The VDM options to apply to the configuration set.</p>"}}, "documentation": "<p>A request to add specific VDM settings to a configuration set.</p>"}, "PutConfigurationSetVdmOptionsResponse": {"type": "structure", "members": {}, "documentation": "<p>An HTTP 200 response if the request succeeds, or an error message if the request fails.</p>"}, "PutDedicatedIpInPoolRequest": {"type": "structure", "required": ["Ip", "DestinationPoolName"], "members": {"Ip": {"shape": "Ip", "documentation": "<p>The IP address that you want to move to the dedicated IP pool. The value you specify has to be a dedicated IP address that's associated with your Amazon Web Services account.</p>", "location": "uri", "locationName": "IP"}, "DestinationPoolName": {"shape": "PoolName", "documentation": "<p>The name of the IP pool that you want to add the dedicated IP address to. You have to specify an IP pool that already exists.</p>"}}, "documentation": "<p>A request to move a dedicated IP address to a dedicated IP pool.</p>"}, "PutDedicatedIpInPoolResponse": {"type": "structure", "members": {}, "documentation": "<p>An HTTP 200 response if the request succeeds, or an error message if the request fails.</p>"}, "PutDedicatedIpPoolScalingAttributesRequest": {"type": "structure", "required": ["PoolName", "ScalingMode"], "members": {"PoolName": {"shape": "PoolName", "documentation": "<p>The name of the dedicated IP pool.</p>", "location": "uri", "locationName": "PoolName"}, "ScalingMode": {"shape": "ScalingMode", "documentation": "<p>The scaling mode to apply to the dedicated IP pool.</p> <note> <p>Changing the scaling mode from <code>MANAGED</code> to <code>STANDARD</code> is not supported.</p> </note>"}}, "documentation": "<p>A request to convert a dedicated IP pool to a different scaling mode.</p>"}, "PutDedicatedIpPoolScalingAttributesResponse": {"type": "structure", "members": {}, "documentation": "<p>An HTTP 200 response if the request succeeds, or an error message if the request fails.</p>"}, "PutDedicatedIpWarmupAttributesRequest": {"type": "structure", "required": ["Ip", "WarmupPercentage"], "members": {"Ip": {"shape": "Ip", "documentation": "<p>The dedicated IP address that you want to update the warm-up attributes for.</p>", "location": "uri", "locationName": "IP"}, "WarmupPercentage": {"shape": "Percentage100Wrapper", "documentation": "<p>The warm-up percentage that you want to associate with the dedicated IP address.</p>"}}, "documentation": "<p>A request to change the warm-up attributes for a dedicated IP address. This operation is useful when you want to resume the warm-up process for an existing IP address.</p>"}, "PutDedicatedIpWarmupAttributesResponse": {"type": "structure", "members": {}, "documentation": "<p>An HTTP 200 response if the request succeeds, or an error message if the request fails.</p>"}, "PutDeliverabilityDashboardOptionRequest": {"type": "structure", "required": ["DashboardEnabled"], "members": {"DashboardEnabled": {"shape": "Enabled", "documentation": "<p>Specifies whether to enable the Deliverability dashboard. To enable the dashboard, set this value to <code>true</code>.</p>"}, "SubscribedDomains": {"shape": "DomainDeliverabilityTrackingOptions", "documentation": "<p>An array of objects, one for each verified domain that you use to send email and enabled the Deliverability dashboard for.</p>"}}, "documentation": "<p>Enable or disable the Deliverability dashboard. When you enable the Deliverability dashboard, you gain access to reputation, deliverability, and other metrics for the domains that you use to send email using Amazon SES API v2. You also gain the ability to perform predictive inbox placement tests.</p> <p>When you use the Deliverability dashboard, you pay a monthly subscription charge, in addition to any other fees that you accrue by using Amazon SES and other Amazon Web Services services. For more information about the features and cost of a Deliverability dashboard subscription, see <a href=\"http://aws.amazon.com/pinpoint/pricing/\">Amazon Pinpoint Pricing</a>.</p>"}, "PutDeliverabilityDashboardOptionResponse": {"type": "structure", "members": {}, "documentation": "<p>A response that indicates whether the Deliverability dashboard is enabled.</p>"}, "PutEmailIdentityConfigurationSetAttributesRequest": {"type": "structure", "required": ["EmailIdentity"], "members": {"EmailIdentity": {"shape": "Identity", "documentation": "<p>The email address or domain to associate with a configuration set.</p>", "location": "uri", "locationName": "EmailIdentity"}, "ConfigurationSetName": {"shape": "ConfigurationSetName", "documentation": "<p>The configuration set to associate with an email identity.</p>"}}, "documentation": "<p>A request to associate a configuration set with an email identity.</p>"}, "PutEmailIdentityConfigurationSetAttributesResponse": {"type": "structure", "members": {}, "documentation": "<p>If the action is successful, the service sends back an HTTP 200 response with an empty HTTP body.</p>"}, "PutEmailIdentityDkimAttributesRequest": {"type": "structure", "required": ["EmailIdentity"], "members": {"EmailIdentity": {"shape": "Identity", "documentation": "<p>The email identity.</p>", "location": "uri", "locationName": "EmailIdentity"}, "SigningEnabled": {"shape": "Enabled", "documentation": "<p>Sets the DKIM signing configuration for the identity.</p> <p>When you set this value <code>true</code>, then the messages that are sent from the identity are signed using DKIM. If you set this value to <code>false</code>, your messages are sent without DKIM signing.</p>"}}, "documentation": "<p>A request to enable or disable DKIM signing of email that you send from an email identity.</p>"}, "PutEmailIdentityDkimAttributesResponse": {"type": "structure", "members": {}, "documentation": "<p>An HTTP 200 response if the request succeeds, or an error message if the request fails.</p>"}, "PutEmailIdentityDkimSigningAttributesRequest": {"type": "structure", "required": ["EmailIdentity", "SigningAttributesOrigin"], "members": {"EmailIdentity": {"shape": "Identity", "documentation": "<p>The email identity.</p>", "location": "uri", "locationName": "EmailIdentity"}, "SigningAttributesOrigin": {"shape": "DkimSigningAttributesOrigin", "documentation": "<p>The method to use to configure DKIM for the identity. There are the following possible values:</p> <ul> <li> <p> <code>AWS_SES</code> – Configure DKIM for the identity by using <a href=\"https://docs.aws.amazon.com/ses/latest/DeveloperGuide/easy-dkim.html\">Easy DKIM</a>.</p> </li> <li> <p> <code>EXTERNAL</code> – Configure DKIM for the identity by using Bring Your Own DKIM (BYODKIM).</p> </li> </ul>"}, "SigningAttributes": {"shape": "DkimSigningAttributes", "documentation": "<p>An object that contains information about the private key and selector that you want to use to configure DKIM for the identity for Bring Your Own DKIM (BYODKIM) for the identity, or, configures the key length to be used for <a href=\"https://docs.aws.amazon.com/ses/latest/DeveloperGuide/easy-dkim.html\">Easy DKIM</a>.</p>"}}, "documentation": "<p>A request to change the DKIM attributes for an email identity.</p>"}, "PutEmailIdentityDkimSigningAttributesResponse": {"type": "structure", "members": {"DkimStatus": {"shape": "DkimStatus", "documentation": "<p>The DKIM authentication status of the identity. Amazon SES determines the authentication status by searching for specific records in the DNS configuration for your domain. If you used <a href=\"https://docs.aws.amazon.com/ses/latest/DeveloperGuide/easy-dkim.html\">Easy DKIM</a> to set up DKIM authentication, Amazon SES tries to find three unique CNAME records in the DNS configuration for your domain.</p> <p>If you provided a public key to perform DKIM authentication, Amazon SES tries to find a TXT record that uses the selector that you specified. The value of the TXT record must be a public key that's paired with the private key that you specified in the process of creating the identity.</p> <p>The status can be one of the following:</p> <ul> <li> <p> <code>PENDING</code> – The verification process was initiated, but Amazon SES hasn't yet detected the DKIM records in the DNS configuration for the domain.</p> </li> <li> <p> <code>SUCCESS</code> – The verification process completed successfully.</p> </li> <li> <p> <code>FAILED</code> – The verification process failed. This typically occurs when Amazon SES fails to find the DKIM records in the DNS configuration of the domain.</p> </li> <li> <p> <code>TEMPORARY_FAILURE</code> – A temporary issue is preventing Amazon SES from determining the DKIM authentication status of the domain.</p> </li> <li> <p> <code>NOT_STARTED</code> – The DKIM verification process hasn't been initiated for the domain.</p> </li> </ul>"}, "DkimTokens": {"shape": "DnsTokenList", "documentation": "<p>If you used <a href=\"https://docs.aws.amazon.com/ses/latest/DeveloperGuide/easy-dkim.html\">Easy DKIM</a> to configure DKIM authentication for the domain, then this object contains a set of unique strings that you use to create a set of CNAME records that you add to the DNS configuration for your domain. When Amazon SES detects these records in the DNS configuration for your domain, the DKIM authentication process is complete.</p> <p>If you configured DKIM authentication for the domain by providing your own public-private key pair, then this object contains the selector that's associated with your public key.</p> <p>Regardless of the DKIM authentication method you use, Amazon SES searches for the appropriate records in the DNS configuration of the domain for up to 72 hours.</p>"}}, "documentation": "<p>If the action is successful, the service sends back an HTTP 200 response.</p> <p>The following data is returned in JSON format by the service.</p>"}, "PutEmailIdentityFeedbackAttributesRequest": {"type": "structure", "required": ["EmailIdentity"], "members": {"EmailIdentity": {"shape": "Identity", "documentation": "<p>The email identity.</p>", "location": "uri", "locationName": "EmailIdentity"}, "EmailForwardingEnabled": {"shape": "Enabled", "documentation": "<p>Sets the feedback forwarding configuration for the identity.</p> <p>If the value is <code>true</code>, you receive email notifications when bounce or complaint events occur. These notifications are sent to the address that you specified in the <code>Return-Path</code> header of the original email.</p> <p>You're required to have a method of tracking bounces and complaints. If you haven't set up another mechanism for receiving bounce or complaint notifications (for example, by setting up an event destination), you receive an email notification when these events occur (even if this setting is disabled).</p>"}}, "documentation": "<p>A request to set the attributes that control how bounce and complaint events are processed.</p>"}, "PutEmailIdentityFeedbackAttributesResponse": {"type": "structure", "members": {}, "documentation": "<p>An HTTP 200 response if the request succeeds, or an error message if the request fails.</p>"}, "PutEmailIdentityMailFromAttributesRequest": {"type": "structure", "required": ["EmailIdentity"], "members": {"EmailIdentity": {"shape": "Identity", "documentation": "<p>The verified email identity.</p>", "location": "uri", "locationName": "EmailIdentity"}, "MailFromDomain": {"shape": "MailFromDomainName", "documentation": "<p> The custom MAIL FROM domain that you want the verified identity to use. The MAIL FROM domain must meet the following criteria:</p> <ul> <li> <p>It has to be a subdomain of the verified identity.</p> </li> <li> <p>It can't be used to receive email.</p> </li> <li> <p>It can't be used in a \"From\" address if the MAIL FROM domain is a destination for feedback forwarding emails.</p> </li> </ul>"}, "BehaviorOnMxFailure": {"shape": "BehaviorOnMxFailure", "documentation": "<p>The action to take if the required MX record isn't found when you send an email. When you set this value to <code>UseDefaultValue</code>, the mail is sent using <i>amazonses.com</i> as the MAIL FROM domain. When you set this value to <code>RejectMessage</code>, the Amazon SES API v2 returns a <code>MailFromDomainNotVerified</code> error, and doesn't attempt to deliver the email.</p> <p>These behaviors are taken when the custom MAIL FROM domain configuration is in the <code>Pending</code>, <code>Failed</code>, and <code>TemporaryFailure</code> states.</p>"}}, "documentation": "<p>A request to configure the custom MAIL FROM domain for a verified identity.</p>"}, "PutEmailIdentityMailFromAttributesResponse": {"type": "structure", "members": {}, "documentation": "<p>An HTTP 200 response if the request succeeds, or an error message if the request fails.</p>"}, "PutSuppressedDestinationRequest": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Reason"], "members": {"EmailAddress": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The email address that should be added to the suppression list for your account.</p>"}, "Reason": {"shape": "SuppressionListReason", "documentation": "<p>The factors that should cause the email address to be added to the suppression list for your account.</p>"}}, "documentation": "<p>A request to add an email destination to the suppression list for your account.</p>"}, "PutSuppressedDestinationResponse": {"type": "structure", "members": {}, "documentation": "<p>An HTTP 200 response if the request succeeds, or an error message if the request fails.</p>"}, "QueryErrorCode": {"type": "string", "enum": ["INTERNAL_FAILURE", "ACCESS_DENIED"]}, "QueryErrorMessage": {"type": "string"}, "QueryIdentifier": {"type": "string", "max": 255, "min": 1}, "RawAttachmentData": {"type": "blob"}, "RawMessage": {"type": "structure", "required": ["Data"], "members": {"Data": {"shape": "RawMessageData", "documentation": "<p>The raw email message. The message has to meet the following criteria:</p> <ul> <li> <p>The message has to contain a header and a body, separated by one blank line.</p> </li> <li> <p>All of the required header fields must be present in the message.</p> </li> <li> <p>Each part of a multipart MIME message must be formatted properly.</p> </li> <li> <p>Attachments must be in a file format that the Amazon SES supports.</p> </li> <li> <p>The raw data of the message needs to base64-encoded if you are accessing Amazon SES directly through the HTTPS interface. If you are accessing Amazon SES using an Amazon Web Services SDK, the SDK takes care of the base 64-encoding for you.</p> </li> <li> <p>If any of the MIME parts in your message contain content that is outside of the 7-bit ASCII character range, you should encode that content to ensure that recipients' email clients render the message properly.</p> </li> <li> <p>The length of any single line of text in the message can't exceed 1,000 characters. This restriction is defined in <a href=\"https://tools.ietf.org/html/rfc5321\">RFC 5321</a>.</p> </li> </ul>"}}, "documentation": "<p>Represents the raw content of an email message.</p>"}, "RawMessageData": {"type": "blob", "documentation": "<p>The raw email message. The message has to meet the following criteria:</p> <ul> <li> <p>The message has to contain a header and a body, separated by one blank line.</p> </li> <li> <p>All of the required header fields must be present in the message.</p> </li> <li> <p>Each part of a multipart MIME message must be formatted properly.</p> </li> <li> <p>Attachments must be in a file format that the Amazon SES API v2 supports. </p> </li> <li> <p>The entire message must be Base64 encoded.</p> </li> <li> <p>If any of the MIME parts in your message contain content that is outside of the 7-bit ASCII character range, you should encode that content to ensure that recipients' email clients render the message properly.</p> </li> <li> <p>The length of any single line of text in the message can't exceed 1,000 characters. This restriction is defined in <a href=\"https://tools.ietf.org/html/rfc5321\">RFC 5321</a>.</p> </li> </ul>"}, "RblName": {"type": "string", "documentation": "<p>The name of a blacklist that an IP address was found on.</p>"}, "Recommendation": {"type": "structure", "members": {"ResourceArn": {"shape": "AmazonResourceName", "documentation": "<p>The resource affected by the recommendation, with values like <code>arn:aws:ses:us-east-1:123456789012:identity/example.com</code>.</p>"}, "Type": {"shape": "RecommendationType", "documentation": "<p>The recommendation type, with values like <code>DKIM</code>, <code>SPF</code>, <code>DMARC</code>, <code>BIMI</code>, or <code>COMPLAINT</code>.</p>"}, "Description": {"shape": "RecommendationDescription", "documentation": "<p>The recommendation description / disambiguator - e.g. <code>DKIM1</code> and <code>DKIM2</code> are different recommendations about your DKIM setup.</p>"}, "Status": {"shape": "RecommendationStatus", "documentation": "<p>The recommendation status, with values like <code>OPEN</code> or <code>FIXED</code>.</p>"}, "CreatedTimestamp": {"shape": "Timestamp", "documentation": "<p>The first time this issue was encountered and the recommendation was generated.</p>"}, "LastUpdatedTimestamp": {"shape": "Timestamp", "documentation": "<p>The last time the recommendation was updated.</p>"}, "Impact": {"shape": "RecommendationImpact", "documentation": "<p>The recommendation impact, with values like <code>HIGH</code> or <code>LOW</code>.</p>"}}, "documentation": "<p>A recommendation generated for your account.</p>"}, "RecommendationDescription": {"type": "string"}, "RecommendationImpact": {"type": "string", "enum": ["LOW", "HIGH"]}, "RecommendationStatus": {"type": "string", "enum": ["OPEN", "FIXED"]}, "RecommendationType": {"type": "string", "enum": ["DKIM", "DMARC", "SPF", "BIMI", "COMPLAINT"]}, "RecommendationsList": {"type": "list", "member": {"shape": "Recommendation"}}, "Region": {"type": "string", "documentation": "<p>The name of an AWS-Region.</p>"}, "Regions": {"type": "list", "member": {"shape": "Region"}}, "RenderedEmailTemplate": {"type": "string", "documentation": "<p>The complete MIME message rendered by applying the data in the TemplateData parameter to the template specified in the TemplateName parameter.</p>"}, "ReplacementEmailContent": {"type": "structure", "members": {"ReplacementTemplate": {"shape": "ReplacementTemplate", "documentation": "<p>The <code>ReplacementTemplate</code> associated with <code>ReplacementEmailContent</code>.</p>"}}, "documentation": "<p>The <code>ReplaceEmailContent</code> object to be used for a specific <code>BulkEmailEntry</code>. The <code>ReplacementTemplate</code> can be specified within this object.</p>"}, "ReplacementTemplate": {"type": "structure", "members": {"ReplacementTemplateData": {"shape": "EmailTemplateData", "documentation": "<p>A list of replacement values to apply to the template. This parameter is a JSON object, typically consisting of key-value pairs in which the keys correspond to replacement tags in the email template.</p>"}}, "documentation": "<p>An object which contains <code>ReplacementTemplateData</code> to be used for a specific <code>BulkEmailEntry</code>.</p>"}, "ReportId": {"type": "string", "documentation": "<p>A unique string that identifies a Deliverability dashboard report.</p>"}, "ReportName": {"type": "string", "documentation": "<p>A name that helps you identify a report generated by the Deliverability dashboard.</p>"}, "ReputationOptions": {"type": "structure", "members": {"ReputationMetricsEnabled": {"shape": "Enabled", "documentation": "<p>If <code>true</code>, tracking of reputation metrics is enabled for the configuration set. If <code>false</code>, tracking of reputation metrics is disabled for the configuration set.</p>"}, "LastFreshStart": {"shape": "LastFreshStart", "documentation": "<p>The date and time (in Unix time) when the reputation metrics were last given a fresh start. When your account is given a fresh start, your reputation metrics are calculated starting from the date of the fresh start.</p>"}}, "documentation": "<p>Enable or disable collection of reputation metrics for emails that you send using this configuration set in the current Amazon Web Services Region. </p>"}, "ReviewDetails": {"type": "structure", "members": {"Status": {"shape": "ReviewStatus", "documentation": "<p>The status of the latest review of your account. The status can be one of the following:</p> <ul> <li> <p> <code>PENDING</code> – We have received your appeal and are in the process of reviewing it.</p> </li> <li> <p> <code>GRANTED</code> – Your appeal has been reviewed and your production access has been granted.</p> </li> <li> <p> <code>DENIED</code> – Your appeal has been reviewed and your production access has been denied.</p> </li> <li> <p> <code>FAILED</code> – An internal error occurred and we didn't receive your appeal. You can submit your appeal again.</p> </li> </ul>"}, "CaseId": {"shape": "CaseId", "documentation": "<p>The associated support center case ID (if any).</p>"}}, "documentation": "<p>An object that contains information about your account details review.</p>"}, "ReviewStatus": {"type": "string", "enum": ["PENDING", "FAILED", "GRANTED", "DENIED"]}, "Route": {"type": "structure", "required": ["Region"], "members": {"Region": {"shape": "Region", "documentation": "<p>The name of an AWS-Region.</p>"}}, "documentation": "<p>An object which contains an AWS-Region and routing status.</p>"}, "RouteDetails": {"type": "structure", "required": ["Region"], "members": {"Region": {"shape": "Region", "documentation": "<p>The name of an AWS-Region to be a secondary region for the multi-region endpoint (global-endpoint).</p>"}}, "documentation": "<p>An object that contains route configuration. Includes secondary region name.</p>"}, "Routes": {"type": "list", "member": {"shape": "Route"}, "documentation": "<p>A list of routes between which the traffic will be split when sending through the multi-region endpoint (global-endpoint).</p>"}, "RoutesDetails": {"type": "list", "member": {"shape": "RouteDetails"}, "documentation": "<p>A list of route configuration details. Must contain exactly one route configuration.</p>"}, "S3Url": {"type": "string", "documentation": "<p>An Amazon S3 URL in the format s3://<i>&lt;bucket_name&gt;</i>/<i>&lt;object&gt;</i> or a pre-signed URL.</p>", "pattern": "^s3:\\/\\/([^\\/]+)\\/(.*?([^\\/]+)\\/?)$"}, "SOARecord": {"type": "structure", "members": {"PrimaryNameServer": {"shape": "PrimaryNameServer", "documentation": "<p>Primary name server specified in the SOA record.</p>"}, "AdminEmail": {"shape": "AdminEmail", "documentation": "<p>Administrative contact email from the SOA record.</p>"}, "SerialNumber": {"shape": "SerialNumber", "documentation": "<p>Serial number from the SOA record.</p>"}}, "documentation": "<p>An object that contains information about the start of authority (SOA) record associated with the identity.</p>"}, "ScalingMode": {"type": "string", "enum": ["STANDARD", "MANAGED"]}, "Selector": {"type": "string", "max": 63, "min": 1, "pattern": "^(([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9\\-]*[a-zA-Z0-9]))$"}, "SendBulkEmailRequest": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "BulkEmailEntries"], "members": {"FromEmailAddress": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The email address to use as the \"From\" address for the email. The address that you specify has to be verified.</p>"}, "FromEmailAddressIdentityArn": {"shape": "AmazonResourceName", "documentation": "<p>This parameter is used only for sending authorization. It is the ARN of the identity that is associated with the sending authorization policy that permits you to use the email address specified in the <code>FromEmailAddress</code> parameter.</p> <p>For example, if the owner of example.com (which has ARN arn:aws:ses:us-east-1:123456789012:identity/example.com) attaches a policy to it that authorizes you <NAME_EMAIL>, then you would specify the <code>FromEmailAddressIdentityArn</code> to be arn:aws:ses:us-east-1:123456789012:identity/example.com, and the <code>FromEmailAddress</code> <NAME_EMAIL>.</p> <p>For more information about sending authorization, see the <a href=\"https://docs.aws.amazon.com/ses/latest/DeveloperGuide/sending-authorization.html\">Amazon SES Developer Guide</a>.</p>"}, "ReplyToAddresses": {"shape": "EmailAddressList", "documentation": "<p>The \"Reply-to\" email addresses for the message. When the recipient replies to the message, each Reply-to address receives the reply.</p>"}, "FeedbackForwardingEmailAddress": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The address that you want bounce and complaint notifications to be sent to.</p>"}, "FeedbackForwardingEmailAddressIdentityArn": {"shape": "AmazonResourceName", "documentation": "<p>This parameter is used only for sending authorization. It is the ARN of the identity that is associated with the sending authorization policy that permits you to use the email address specified in the <code>FeedbackForwardingEmailAddress</code> parameter.</p> <p>For example, if the owner of example.com (which has ARN arn:aws:ses:us-east-1:123456789012:identity/example.com) attaches a policy to it that authorizes you <NAME_EMAIL>, then you would specify the <code>FeedbackForwardingEmailAddressIdentityArn</code> to be arn:aws:ses:us-east-1:123456789012:identity/example.com, and the <code>FeedbackForwardingEmailAddress</code> <NAME_EMAIL>.</p> <p>For more information about sending authorization, see the <a href=\"https://docs.aws.amazon.com/ses/latest/DeveloperGuide/sending-authorization.html\">Amazon SES Developer Guide</a>.</p>"}, "DefaultEmailTags": {"shape": "MessageTagList", "documentation": "<p>A list of tags, in the form of name/value pairs, to apply to an email that you send using the <code>SendEmail</code> operation. Tags correspond to characteristics of the email that you define, so that you can publish email sending events.</p>"}, "DefaultContent": {"shape": "BulkEmailContent", "documentation": "<p>An object that contains the body of the message. You can specify a template message.</p>"}, "BulkEmailEntries": {"shape": "BulkEmailEntryList", "documentation": "<p>The list of bulk email entry objects.</p>"}, "ConfigurationSetName": {"shape": "ConfigurationSetName", "documentation": "<p>The name of the configuration set to use when sending the email.</p>"}, "EndpointId": {"shape": "EndpointId", "documentation": "<p>The ID of the multi-region endpoint (global-endpoint).</p>", "contextParam": {"name": "EndpointId"}}}, "documentation": "<p>Represents a request to send email messages to multiple destinations using Amazon SES. For more information, see the <a href=\"https://docs.aws.amazon.com/ses/latest/DeveloperGuide/send-personalized-email-api.html\">Amazon SES Developer Guide</a>.</p>"}, "SendBulkEmailResponse": {"type": "structure", "required": ["BulkEmailEntryResults"], "members": {"BulkEmailEntryResults": {"shape": "BulkEmailEntryResultList", "documentation": "<p>One object per intended recipient. Check each response object and retry any messages with a failure status.</p>"}}, "documentation": "<p>The following data is returned in JSON format by the service.</p>"}, "SendCustomVerificationEmailRequest": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "TemplateName"], "members": {"EmailAddress": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The email address to verify.</p>"}, "TemplateName": {"shape": "EmailTemplateName", "documentation": "<p>The name of the custom verification email template to use when sending the verification email.</p>"}, "ConfigurationSetName": {"shape": "ConfigurationSetName", "documentation": "<p>Name of a configuration set to use when sending the verification email.</p>"}}, "documentation": "<p>Represents a request to send a custom verification email to a specified recipient.</p>"}, "SendCustomVerificationEmailResponse": {"type": "structure", "members": {"MessageId": {"shape": "OutboundMessageId", "documentation": "<p>The unique message identifier returned from the <code>SendCustomVerificationEmail</code> operation.</p>"}}, "documentation": "<p>The following element is returned by the service.</p>"}, "SendEmailRequest": {"type": "structure", "required": ["Content"], "members": {"FromEmailAddress": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The email address to use as the \"From\" address for the email. The address that you specify has to be verified. </p>"}, "FromEmailAddressIdentityArn": {"shape": "AmazonResourceName", "documentation": "<p>This parameter is used only for sending authorization. It is the ARN of the identity that is associated with the sending authorization policy that permits you to use the email address specified in the <code>FromEmailAddress</code> parameter.</p> <p>For example, if the owner of example.com (which has ARN arn:aws:ses:us-east-1:123456789012:identity/example.com) attaches a policy to it that authorizes you <NAME_EMAIL>, then you would specify the <code>FromEmailAddressIdentityArn</code> to be arn:aws:ses:us-east-1:123456789012:identity/example.com, and the <code>FromEmailAddress</code> <NAME_EMAIL>.</p> <p>For more information about sending authorization, see the <a href=\"https://docs.aws.amazon.com/ses/latest/DeveloperGuide/sending-authorization.html\">Amazon SES Developer Guide</a>.</p> <p>For Raw emails, the <code>FromEmailAddressIdentityArn</code> value overrides the X-SES-SOURCE-ARN and X-SES-FROM-ARN headers specified in raw email message content.</p>"}, "Destination": {"shape": "Destination", "documentation": "<p>An object that contains the recipients of the email message.</p>"}, "ReplyToAddresses": {"shape": "EmailAddressList", "documentation": "<p>The \"Reply-to\" email addresses for the message. When the recipient replies to the message, each Reply-to address receives the reply.</p>"}, "FeedbackForwardingEmailAddress": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The address that you want bounce and complaint notifications to be sent to.</p>"}, "FeedbackForwardingEmailAddressIdentityArn": {"shape": "AmazonResourceName", "documentation": "<p>This parameter is used only for sending authorization. It is the ARN of the identity that is associated with the sending authorization policy that permits you to use the email address specified in the <code>FeedbackForwardingEmailAddress</code> parameter.</p> <p>For example, if the owner of example.com (which has ARN arn:aws:ses:us-east-1:123456789012:identity/example.com) attaches a policy to it that authorizes you <NAME_EMAIL>, then you would specify the <code>FeedbackForwardingEmailAddressIdentityArn</code> to be arn:aws:ses:us-east-1:123456789012:identity/example.com, and the <code>FeedbackForwardingEmailAddress</code> <NAME_EMAIL>.</p> <p>For more information about sending authorization, see the <a href=\"https://docs.aws.amazon.com/ses/latest/DeveloperGuide/sending-authorization.html\">Amazon SES Developer Guide</a>.</p>"}, "Content": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>An object that contains the body of the message. You can send either a Simple message, Raw message, or a Templated message.</p>"}, "EmailTags": {"shape": "MessageTagList", "documentation": "<p>A list of tags, in the form of name/value pairs, to apply to an email that you send using the <code>SendEmail</code> operation. Tags correspond to characteristics of the email that you define, so that you can publish email sending events. </p>"}, "ConfigurationSetName": {"shape": "ConfigurationSetName", "documentation": "<p>The name of the configuration set to use when sending the email.</p>"}, "EndpointId": {"shape": "EndpointId", "documentation": "<p>The ID of the multi-region endpoint (global-endpoint).</p>", "contextParam": {"name": "EndpointId"}}, "ListManagementOptions": {"shape": "ListManagementOptions", "documentation": "<p>An object used to specify a list or topic to which an email belongs, which will be used when a contact chooses to unsubscribe.</p>"}}, "documentation": "<p>Represents a request to send a single formatted email using Amazon SES. For more information, see the <a href=\"https://docs.aws.amazon.com/ses/latest/DeveloperGuide/send-email-formatted.html\">Amazon SES Developer Guide</a>.</p>"}, "SendEmailResponse": {"type": "structure", "members": {"MessageId": {"shape": "OutboundMessageId", "documentation": "<p>A unique identifier for the message that is generated when the message is accepted.</p> <note> <p>It's possible for Amazon SES to accept a message without sending it. For example, this can happen when the message that you're trying to send has an attachment that contains a virus, or when you send a templated email that contains invalid personalization content.</p> </note>"}}, "documentation": "<p>A unique message ID that you receive when an email is accepted for sending.</p>"}, "SendQuota": {"type": "structure", "members": {"Max24HourSend": {"shape": "Max24HourSend", "documentation": "<p>The maximum number of emails that you can send in the current Amazon Web Services Region over a 24-hour period. A value of -1 signifies an unlimited quota. (This value is also referred to as your <i>sending quota</i>.)</p>"}, "MaxSendRate": {"shape": "MaxSendRate", "documentation": "<p>The maximum number of emails that you can send per second in the current Amazon Web Services Region. This value is also called your <i>maximum sending rate</i> or your <i>maximum TPS (transactions per second) rate</i>.</p>"}, "SentLast24Hours": {"shape": "SentLast24Hours", "documentation": "<p>The number of emails sent from your Amazon SES account in the current Amazon Web Services Region over the past 24 hours.</p>"}}, "documentation": "<p>An object that contains information about the per-day and per-second sending limits for your Amazon SES account in the current Amazon Web Services Region.</p>"}, "SendingOptions": {"type": "structure", "members": {"SendingEnabled": {"shape": "Enabled", "documentation": "<p>If <code>true</code>, email sending is enabled for the configuration set. If <code>false</code>, email sending is disabled for the configuration set.</p>"}}, "documentation": "<p>Used to enable or disable email sending for messages that use this configuration set in the current Amazon Web Services Region.</p>"}, "SendingPausedException": {"type": "structure", "members": {}, "documentation": "<p>The message can't be sent because the account's ability to send email is currently paused.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "SendingPoolName": {"type": "string", "documentation": "<p>The name of the dedicated IP pool to associate with the configuration set.</p>"}, "SentLast24Hours": {"type": "double"}, "SerialNumber": {"type": "long"}, "SnsDestination": {"type": "structure", "required": ["TopicArn"], "members": {"TopicArn": {"shape": "AmazonResourceName", "documentation": "<p>The Amazon Resource Name (ARN) of the Amazon SNS topic to publish email events to. For more information about Amazon SNS topics, see the <a href=\"https://docs.aws.amazon.com/sns/latest/dg/CreateTopic.html\">Amazon SNS Developer Guide</a>.</p>"}}, "documentation": "<p>An object that defines an Amazon SNS destination for email events. You can use Amazon SNS to send notifications when certain email events occur.</p>"}, "Status": {"type": "string", "documentation": "<p>The status of the multi-region endpoint (global-endpoint).</p> <ul> <li> <p> <code>CREATING</code> – The resource is being provisioned.</p> </li> <li> <p> <code>READY</code> – The resource is ready to use.</p> </li> <li> <p> <code>FAILED</code> – The resource failed to be provisioned.</p> </li> <li> <p> <code>DELETING</code> – The resource is being deleted as requested.</p> </li> </ul>", "enum": ["CREATING", "READY", "FAILED", "DELETING"]}, "Subject": {"type": "string"}, "SubscriptionStatus": {"type": "string", "enum": ["OPT_IN", "OPT_OUT"]}, "SuccessRedirectionURL": {"type": "string", "documentation": "<p>The URL that the recipient of the verification email is sent to if his or her address is successfully verified.</p>"}, "SuppressedDestination": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Reason", "LastUpdateTime"], "members": {"EmailAddress": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The email address that is on the suppression list for your account.</p>"}, "Reason": {"shape": "SuppressionListReason", "documentation": "<p>The reason that the address was added to the suppression list for your account.</p>"}, "LastUpdateTime": {"shape": "Timestamp", "documentation": "<p>The date and time when the suppressed destination was last updated, shown in Unix time format.</p>"}, "Attributes": {"shape": "SuppressedDestinationAttributes", "documentation": "<p>An optional value that can contain additional information about the reasons that the address was added to the suppression list for your account.</p>"}}, "documentation": "<p>An object that contains information about an email address that is on the suppression list for your account.</p>"}, "SuppressedDestinationAttributes": {"type": "structure", "members": {"MessageId": {"shape": "OutboundMessageId", "documentation": "<p>The unique identifier of the email message that caused the email address to be added to the suppression list for your account.</p>"}, "FeedbackId": {"shape": "FeedbackId", "documentation": "<p>A unique identifier that's generated when an email address is added to the suppression list for your account.</p>"}}, "documentation": "<p>An object that contains additional attributes that are related an email address that is on the suppression list for your account.</p>"}, "SuppressedDestinationSummaries": {"type": "list", "member": {"shape": "SuppressedDestinationSummary"}}, "SuppressedDestinationSummary": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Reason", "LastUpdateTime"], "members": {"EmailAddress": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The email address that's on the suppression list for your account.</p>"}, "Reason": {"shape": "SuppressionListReason", "documentation": "<p>The reason that the address was added to the suppression list for your account.</p>"}, "LastUpdateTime": {"shape": "Timestamp", "documentation": "<p>The date and time when the suppressed destination was last updated, shown in Unix time format.</p>"}}, "documentation": "<p>A summary that describes the suppressed email address.</p>"}, "SuppressionAttributes": {"type": "structure", "members": {"SuppressedReasons": {"shape": "SuppressionListReasons", "documentation": "<p>A list that contains the reasons that email addresses will be automatically added to the suppression list for your account. This list can contain any or all of the following:</p> <ul> <li> <p> <code>COMPLAINT</code> – Amazon SES adds an email address to the suppression list for your account when a message sent to that address results in a complaint.</p> </li> <li> <p> <code>BOUNCE</code> – Amazon SES adds an email address to the suppression list for your account when a message sent to that address results in a hard bounce.</p> </li> </ul>"}}, "documentation": "<p>An object that contains information about the email address suppression preferences for your account in the current Amazon Web Services Region.</p>"}, "SuppressionListDestination": {"type": "structure", "required": ["SuppressionListImportAction"], "members": {"SuppressionListImportAction": {"shape": "SuppressionListImportAction", "documentation": "<p>The type of action to perform on the address. The following are possible values:</p> <ul> <li> <p>PUT: add the addresses to the suppression list. If the record already exists, it will override it with the new value.</p> </li> <li> <p>DELETE: remove the addresses from the suppression list.</p> </li> </ul>"}}, "documentation": "<p>An object that contains details about the action of suppression list.</p>"}, "SuppressionListImportAction": {"type": "string", "documentation": "<p>The type of action to perform on the address. The following are possible values:</p> <ul> <li> <p>PUT: add the addresses to the suppression list.</p> </li> <li> <p>DELETE: remove the address from the suppression list.</p> </li> </ul>", "enum": ["DELETE", "PUT"]}, "SuppressionListReason": {"type": "string", "documentation": "<p>The reason that the address was added to the suppression list for your account. The value can be one of the following:</p> <ul> <li> <p> <code>COMPLAINT</code> – Amazon SES added an email address to the suppression list for your account because a message sent to that address results in a complaint.</p> </li> <li> <p> <code>BOUNCE</code> – Amazon SES added an email address to the suppression list for your account because a message sent to that address results in a hard bounce.</p> </li> </ul>", "enum": ["BOUNCE", "COMPLAINT"]}, "SuppressionListReasons": {"type": "list", "member": {"shape": "SuppressionListReason"}}, "SuppressionOptions": {"type": "structure", "members": {"SuppressedReasons": {"shape": "SuppressionListReasons", "documentation": "<p>A list that contains the reasons that email addresses are automatically added to the suppression list for your account. This list can contain any or all of the following:</p> <ul> <li> <p> <code>COMPLAINT</code> – Amazon SES adds an email address to the suppression list for your account when a message sent to that address results in a complaint.</p> </li> <li> <p> <code>BOUNCE</code> – Amazon SES adds an email address to the suppression list for your account when a message sent to that address results in a hard bounce.</p> </li> </ul>"}}, "documentation": "<p>An object that contains information about the suppression list preferences for your account.</p>"}, "Tag": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {"shape": "TagKey", "documentation": "<p>One part of a key-value pair that defines a tag. The maximum length of a tag key is 128 characters. The minimum length is 1 character.</p>"}, "Value": {"shape": "TagValue", "documentation": "<p>The optional part of a key-value pair that defines a tag. The maximum length of a tag value is 256 characters. The minimum length is 0 characters. If you don't want a resource to have a specific tag value, don't specify a value for this parameter. If you don't specify a value, Amazon SES sets the value to an empty string.</p>"}}, "documentation": "<p>An object that defines the tags that are associated with a resource. A <i>tag</i> is a label that you optionally define and associate with a resource. Tags can help you categorize and manage resources in different ways, such as by purpose, owner, environment, or other criteria. A resource can have as many as 50 tags.</p> <p>Each tag consists of a required <i>tag key</i> and an associated <i>tag value</i>, both of which you define. A tag key is a general label that acts as a category for a more specific tag value. A tag value acts as a descriptor within a tag key. A tag key can contain as many as 128 characters. A tag value can contain as many as 256 characters. The characters can be Unicode letters, digits, white space, or one of the following symbols: _ . : / = + -. The following additional restrictions apply to tags:</p> <ul> <li> <p>Tag keys and values are case sensitive.</p> </li> <li> <p>For each associated resource, each tag key must be unique and it can have only one value.</p> </li> <li> <p>The <code>aws:</code> prefix is reserved for use by Amazon Web Services; you can’t use it in any tag keys or values that you define. In addition, you can't edit or remove tag keys or values that use this prefix. Tags that use this prefix don’t count against the limit of 50 tags per resource.</p> </li> <li> <p>You can associate tags with public or shared resources, but the tags are available only for your Amazon Web Services account, not any other accounts that share the resource. In addition, the tags are available only for resources that are located in the specified Amazon Web Services Region for your Amazon Web Services account.</p> </li> </ul>"}, "TagKey": {"type": "string"}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}}, "TagList": {"type": "list", "member": {"shape": "Tag"}}, "TagResourceRequest": {"type": "structure", "required": ["ResourceArn", "Tags"], "members": {"ResourceArn": {"shape": "AmazonResourceName", "documentation": "<p>The Amazon Resource Name (ARN) of the resource that you want to add one or more tags to.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>A list of the tags that you want to add to the resource. A tag consists of a required tag key (<code>Key</code>) and an associated tag value (<code>Value</code>). The maximum length of a tag key is 128 characters. The maximum length of a tag value is 256 characters.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string"}, "Template": {"type": "structure", "members": {"TemplateName": {"shape": "EmailTemplateName", "documentation": "<p>The name of the template. You will refer to this name when you send email using the <code>SendEmail</code> or <code>SendBulkEmail</code> operations. </p>"}, "TemplateArn": {"shape": "AmazonResourceName", "documentation": "<p>The Amazon Resource Name (ARN) of the template.</p>"}, "TemplateContent": {"shape": "EmailTemplateContent", "documentation": "<p>The content of the template.</p> <note> <p>Amazon SES supports only simple substitions when you send email using the <code>SendEmail</code> or <code>SendBulkEmail</code> operations and you provide the full template content in the request.</p> </note>"}, "TemplateData": {"shape": "EmailTemplateData", "documentation": "<p>An object that defines the values to use for message variables in the template. This object is a set of key-value pairs. Each key defines a message variable in the template. The corresponding value defines the value to use for that variable.</p>"}, "Headers": {"shape": "MessageHeaderList", "documentation": "<p>The list of message headers that will be added to the email message.</p>"}, "Attachments": {"shape": "AttachmentList", "documentation": "<p> The List of attachments to include in your email. All recipients will receive the same attachments.</p>"}}, "documentation": "<p>An object that defines the email template to use for an email message, and the values to use for any message variables in that template. An <i>email template</i> is a type of message template that contains content that you want to reuse in email messages that you send. You can specifiy the email template by providing the name or ARN of an <i>email template</i> previously saved in your Amazon SES account or by providing the full template content.</p>"}, "TemplateContent": {"type": "string", "documentation": "<p>The content of the custom verification email template.</p>"}, "TestRenderEmailTemplateRequest": {"type": "structure", "required": ["TemplateName", "TemplateData"], "members": {"TemplateName": {"shape": "EmailTemplateName", "documentation": "<p>The name of the template.</p>", "location": "uri", "locationName": "TemplateName"}, "TemplateData": {"shape": "EmailTemplateData", "documentation": "<p>A list of replacement values to apply to the template. This parameter is a JSON object, typically consisting of key-value pairs in which the keys correspond to replacement tags in the email template.</p>"}}, "documentation": "<p>&gt;Represents a request to create a preview of the MIME content of an email when provided with a template and a set of replacement data.</p>"}, "TestRenderEmailTemplateResponse": {"type": "structure", "required": ["RenderedTemplate"], "members": {"RenderedTemplate": {"shape": "RenderedEmailTemplate", "documentation": "<p>The complete MIME message rendered by applying the data in the <code>TemplateData</code> parameter to the template specified in the TemplateName parameter.</p>"}}, "documentation": "<p>The following element is returned by the service.</p>"}, "Timestamp": {"type": "timestamp"}, "TimestampList": {"type": "list", "member": {"shape": "Timestamp"}}, "TlsPolicy": {"type": "string", "documentation": "<p>Specifies whether messages that use the configuration set are required to use Transport Layer Security (TLS). If the value is <code>Require</code>, messages are only delivered if a TLS connection can be established. If the value is <code>Optional</code>, messages can be delivered in plain text if a TLS connection can't be established.</p>", "enum": ["REQUIRE", "OPTIONAL"]}, "TooManyRequestsException": {"type": "structure", "members": {}, "documentation": "<p>Too many requests have been made to the operation.</p>", "error": {"httpStatusCode": 429}, "exception": true}, "Topic": {"type": "structure", "required": ["TopicName", "DisplayName", "DefaultSubscriptionStatus"], "members": {"TopicName": {"shape": "TopicName", "documentation": "<p>The name of the topic.</p>"}, "DisplayName": {"shape": "DisplayName", "documentation": "<p>The name of the topic the contact will see.</p>"}, "Description": {"shape": "Description", "documentation": "<p>A description of what the topic is about, which the contact will see.</p>"}, "DefaultSubscriptionStatus": {"shape": "SubscriptionStatus", "documentation": "<p>The default subscription status to be applied to a contact if the contact has not noted their preference for subscribing to a topic.</p>"}}, "documentation": "<p>An interest group, theme, or label within a list. Lists can have multiple topics.</p>"}, "TopicFilter": {"type": "structure", "members": {"TopicName": {"shape": "TopicName", "documentation": "<p>The name of a topic on which you wish to apply the filter.</p>"}, "UseDefaultIfPreferenceUnavailable": {"shape": "UseDefaultIfPreferenceUnavailable", "documentation": "<p>Notes that the default subscription status should be applied to a contact because the contact has not noted their preference for subscribing to a topic.</p>"}}, "documentation": "<p>Used for filtering by a specific topic preference.</p>"}, "TopicName": {"type": "string"}, "TopicPreference": {"type": "structure", "required": ["TopicName", "SubscriptionStatus"], "members": {"TopicName": {"shape": "TopicName", "documentation": "<p>The name of the topic.</p>"}, "SubscriptionStatus": {"shape": "SubscriptionStatus", "documentation": "<p>The contact's subscription status to a topic which is either <code>OPT_IN</code> or <code>OPT_OUT</code>.</p>"}}, "documentation": "<p>The contact's preference for being opted-in to or opted-out of a topic.</p>"}, "TopicPreferenceList": {"type": "list", "member": {"shape": "TopicPreference"}}, "Topics": {"type": "list", "member": {"shape": "Topic"}}, "TrackingOptions": {"type": "structure", "required": ["CustomRedirectDomain"], "members": {"CustomRedirectDomain": {"shape": "CustomRedirectDomain", "documentation": "<p>The domain to use for tracking open and click events.</p>"}, "HttpsPolicy": {"shape": "HttpsPolicy", "documentation": "<p>The https policy to use for tracking open and click events.</p>"}}, "documentation": "<p>An object that defines the tracking options for a configuration set. When you use the Amazon SES API v2 to send an email, it contains an invisible image that's used to track when recipients open your email. If your email contains links, those links are changed slightly in order to track when recipients click them.</p> <p>These images and links include references to a domain operated by Amazon Web Services. You can optionally configure the Amazon SES to use a domain that you operate for these images and links.</p>"}, "UnsubscribeAll": {"type": "boolean"}, "UntagResourceRequest": {"type": "structure", "required": ["ResourceArn", "TagKeys"], "members": {"ResourceArn": {"shape": "AmazonResourceName", "documentation": "<p>The Amazon Resource Name (ARN) of the resource that you want to remove one or more tags from.</p>", "location": "querystring", "locationName": "ResourceArn"}, "TagKeys": {"shape": "TagKeyList", "documentation": "<p>The tags (tag keys) that you want to remove from the resource. When you specify a tag key, the action removes both that key and its associated tag value.</p> <p>To remove more than one tag from the resource, append the <code>TagKeys</code> parameter and argument for each additional tag to remove, separated by an ampersand. For example: <code>/v2/email/tags?ResourceArn=ResourceArn&amp;TagKeys=Key1&amp;TagKeys=Key2</code> </p>", "location": "querystring", "locationName": "TagKeys"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateConfigurationSetEventDestinationRequest": {"type": "structure", "required": ["ConfigurationSetName", "EventDestinationName", "EventDestination"], "members": {"ConfigurationSetName": {"shape": "ConfigurationSetName", "documentation": "<p>The name of the configuration set that contains the event destination to modify.</p>", "location": "uri", "locationName": "ConfigurationSetName"}, "EventDestinationName": {"shape": "EventDestinationName", "documentation": "<p>The name of the event destination.</p>", "location": "uri", "locationName": "EventDestinationName"}, "EventDestination": {"shape": "EventDestinationDefinition", "documentation": "<p>An object that defines the event destination.</p>"}}, "documentation": "<p>A request to change the settings for an event destination for a configuration set.</p>"}, "UpdateConfigurationSetEventDestinationResponse": {"type": "structure", "members": {}, "documentation": "<p>An HTTP 200 response if the request succeeds, or an error message if the request fails.</p>"}, "UpdateContactListRequest": {"type": "structure", "required": ["ContactListName"], "members": {"ContactListName": {"shape": "ContactListName", "documentation": "<p>The name of the contact list.</p>", "location": "uri", "locationName": "ContactListName"}, "Topics": {"shape": "Topics", "documentation": "<p>An interest group, theme, or label within a list. A contact list can have multiple topics.</p>"}, "Description": {"shape": "Description", "documentation": "<p>A description of what the contact list is about.</p>"}}}, "UpdateContactListResponse": {"type": "structure", "members": {}}, "UpdateContactRequest": {"type": "structure", "required": ["ContactListName", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "members": {"ContactListName": {"shape": "ContactListName", "documentation": "<p>The name of the contact list.</p>", "location": "uri", "locationName": "ContactListName"}, "EmailAddress": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The contact's email address.</p>", "location": "uri", "locationName": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "TopicPreferences": {"shape": "TopicPreferenceList", "documentation": "<p>The contact's preference for being opted-in to or opted-out of a topic.</p>"}, "UnsubscribeAll": {"shape": "UnsubscribeAll", "documentation": "<p>A boolean value status noting if the contact is unsubscribed from all contact list topics.</p>"}, "AttributesData": {"shape": "AttributesData", "documentation": "<p>The attribute data attached to a contact.</p>"}}}, "UpdateContactResponse": {"type": "structure", "members": {}}, "UpdateCustomVerificationEmailTemplateRequest": {"type": "structure", "required": ["TemplateName", "FromEmailAddress", "TemplateSubject", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SuccessRedirectionURL", "FailureRedirectionURL"], "members": {"TemplateName": {"shape": "EmailTemplateName", "documentation": "<p>The name of the custom verification email template that you want to update.</p>", "location": "uri", "locationName": "TemplateName"}, "FromEmailAddress": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The email address that the custom verification email is sent from.</p>"}, "TemplateSubject": {"shape": "EmailTemplateSubject", "documentation": "<p>The subject line of the custom verification email.</p>"}, "TemplateContent": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The content of the custom verification email. The total size of the email must be less than 10 MB. The message body may contain HTML, with some limitations. For more information, see <a href=\"https://docs.aws.amazon.com/ses/latest/dg/creating-identities.html#send-email-verify-address-custom-faq\">Custom verification email frequently asked questions</a> in the <i>Amazon SES Developer Guide</i>.</p>"}, "SuccessRedirectionURL": {"shape": "SuccessRedirectionURL", "documentation": "<p>The URL that the recipient of the verification email is sent to if his or her address is successfully verified.</p>"}, "FailureRedirectionURL": {"shape": "FailureRedirectionURL", "documentation": "<p>The URL that the recipient of the verification email is sent to if his or her address is not successfully verified.</p>"}}, "documentation": "<p>Represents a request to update an existing custom verification email template.</p>"}, "UpdateCustomVerificationEmailTemplateResponse": {"type": "structure", "members": {}, "documentation": "<p>If the action is successful, the service sends back an HTTP 200 response with an empty HTTP body.</p>"}, "UpdateEmailIdentityPolicyRequest": {"type": "structure", "required": ["EmailIdentity", "PolicyName", "Policy"], "members": {"EmailIdentity": {"shape": "Identity", "documentation": "<p>The email identity.</p>", "location": "uri", "locationName": "EmailIdentity"}, "PolicyName": {"shape": "PolicyName", "documentation": "<p>The name of the policy.</p> <p>The policy name cannot exceed 64 characters and can only include alphanumeric characters, dashes, and underscores.</p>", "location": "uri", "locationName": "PolicyName"}, "Policy": {"shape": "Policy", "documentation": "<p>The text of the policy in JSON format. The policy cannot exceed 4 KB.</p> <p> For information about the syntax of sending authorization policies, see the <a href=\"https://docs.aws.amazon.com/ses/latest/DeveloperGuide/sending-authorization-policies.html\">Amazon SES Developer Guide</a>.</p>"}}, "documentation": "<p>Represents a request to update a sending authorization policy for an identity. Sending authorization is an Amazon SES feature that enables you to authorize other senders to use your identities. For information, see the <a href=\"https://docs.aws.amazon.com/ses/latest/DeveloperGuide/sending-authorization-identity-owner-tasks-management.html\">Amazon SES Developer Guide</a>.</p>"}, "UpdateEmailIdentityPolicyResponse": {"type": "structure", "members": {}, "documentation": "<p>An HTTP 200 response if the request succeeds, or an error message if the request fails.</p>"}, "UpdateEmailTemplateRequest": {"type": "structure", "required": ["TemplateName", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "members": {"TemplateName": {"shape": "EmailTemplateName", "documentation": "<p>The name of the template.</p>", "location": "uri", "locationName": "TemplateName"}, "TemplateContent": {"shape": "EmailTemplateContent", "documentation": "<p>The content of the email template, composed of a subject line, an HTML part, and a text-only part.</p>"}}, "documentation": "<p>Represents a request to update an email template. For more information, see the <a href=\"https://docs.aws.amazon.com/ses/latest/DeveloperGuide/send-personalized-email-api.html\">Amazon SES Developer Guide</a>.</p>"}, "UpdateEmailTemplateResponse": {"type": "structure", "members": {}, "documentation": "<p>If the action is successful, the service sends back an HTTP 200 response with an empty HTTP body.</p>"}, "UseCaseDescription": {"type": "string", "deprecated": true, "deprecatedMessage": "Use case description is optional and deprecated", "max": 5000, "sensitive": true}, "UseDefaultIfPreferenceUnavailable": {"type": "boolean"}, "VdmAttributes": {"type": "structure", "required": ["VdmEnabled"], "members": {"VdmEnabled": {"shape": "FeatureStatus", "documentation": "<p>Specifies the status of your VDM configuration. Can be one of the following:</p> <ul> <li> <p> <code>ENABLED</code> – Amazon SES enables VDM for your account.</p> </li> <li> <p> <code>DISABLED</code> – Amazon SES disables VDM for your account.</p> </li> </ul>"}, "DashboardAttributes": {"shape": "DashboardAttributes", "documentation": "<p>Specifies additional settings for your VDM configuration as applicable to the Dashboard.</p>"}, "GuardianAttributes": {"shape": "GuardianAttributes", "documentation": "<p>Specifies additional settings for your VDM configuration as applicable to the Guardian.</p>"}}, "documentation": "<p>The VDM attributes that apply to your Amazon SES account.</p>"}, "VdmOptions": {"type": "structure", "members": {"DashboardOptions": {"shape": "DashboardOptions", "documentation": "<p>Specifies additional settings for your VDM configuration as applicable to the Dashboard.</p>"}, "GuardianOptions": {"shape": "GuardianOptions", "documentation": "<p>Specifies additional settings for your VDM configuration as applicable to the Guardian.</p>"}}, "documentation": "<p>An object that defines the VDM settings that apply to emails that you send using the configuration set.</p>"}, "VerificationError": {"type": "string", "enum": ["SERVICE_ERROR", "DNS_SERVER_ERROR", "HOST_NOT_FOUND", "TYPE_NOT_FOUND", "INVALID_VALUE", "REPLICATION_ACCESS_DENIED", "REPLICATION_PRIMARY_NOT_FOUND", "REPLICATION_PRIMARY_BYO_DKIM_NOT_SUPPORTED", "REPLICATION_REPLICA_AS_PRIMARY_NOT_SUPPORTED", "REPLICATION_PRIMARY_INVALID_REGION"]}, "VerificationInfo": {"type": "structure", "members": {"LastCheckedTimestamp": {"shape": "Timestamp", "documentation": "<p>The last time a verification attempt was made for this identity.</p>"}, "LastSuccessTimestamp": {"shape": "Timestamp", "documentation": "<p>The last time a successful verification was made for this identity.</p>"}, "ErrorType": {"shape": "VerificationError", "documentation": "<p>Provides the reason for the failure describing why Amazon SES was not able to successfully verify the identity. Below are the possible values: </p> <ul> <li> <p> <code>INVALID_VALUE</code> – Amazon SES was able to find the record, but the value contained within the record was invalid. Ensure you have published the correct values for the record.</p> </li> <li> <p> <code>TYPE_NOT_FOUND</code> – The queried hostname exists but does not have the requested type of DNS record. Ensure that you have published the correct type of DNS record.</p> </li> <li> <p> <code>HOST_NOT_FOUND</code> – The queried hostname does not exist or was not reachable at the time of the request. Ensure that you have published the required DNS record(s). </p> </li> <li> <p> <code>SERVICE_ERROR</code> – A temporary issue is preventing Amazon SES from determining the verification status of the domain.</p> </li> <li> <p> <code>DNS_SERVER_ERROR</code> – The DNS server encountered an issue and was unable to complete the request.</p> </li> <li> <p> <code>REPLICATION_ACCESS_DENIED</code> – The verification failed because the user does not have the required permissions to replicate the DKIM key from the primary region. Ensure you have the necessary permissions in both primary and replica regions. </p> </li> <li> <p> <code>REPLICATION_PRIMARY_NOT_FOUND</code> – The verification failed because no corresponding identity was found in the specified primary region. Ensure the identity exists in the primary region before attempting replication. </p> </li> <li> <p> <code>REPLICATION_PRIMARY_BYO_DKIM_NOT_SUPPORTED</code> – The verification failed because the identity in the primary region is configured with Bring Your Own DKIM (BYODKIM). DKIM key replication is only supported for identities using Easy DKIM. </p> </li> <li> <p> <code>REPLICATION_REPLICA_AS_PRIMARY_NOT_SUPPORTED</code> – The verification failed because the specified primary identity is a replica of another identity, and multi-level replication is not supported; the primary identity must be a non-replica identity. </p> </li> <li> <p> <code>REPLICATION_PRIMARY_INVALID_REGION</code> – The verification failed due to an invalid primary region specified. Ensure you provide a valid Amazon Web Services region where Amazon SES is available and different from the replica region. </p> </li> </ul>"}, "SOARecord": {"shape": "SOARecord", "documentation": "<p>An object that contains information about the start of authority (SOA) record associated with the identity.</p>"}}, "documentation": "<p>An object that contains additional information about the verification status for the identity.</p>"}, "VerificationStatus": {"type": "string", "enum": ["PENDING", "SUCCESS", "FAILED", "TEMPORARY_FAILURE", "NOT_STARTED"]}, "Volume": {"type": "long", "documentation": "<p>An object that contains information about inbox placement volume.</p>"}, "VolumeStatistics": {"type": "structure", "members": {"InboxRawCount": {"shape": "Volume", "documentation": "<p>The total number of emails that arrived in recipients' inboxes.</p>"}, "SpamRawCount": {"shape": "Volume", "documentation": "<p>The total number of emails that arrived in recipients' spam or junk mail folders.</p>"}, "ProjectedInbox": {"shape": "Volume", "documentation": "<p>An estimate of the percentage of emails sent from the current domain that will arrive in recipients' inboxes.</p>"}, "ProjectedSpam": {"shape": "Volume", "documentation": "<p>An estimate of the percentage of emails sent from the current domain that will arrive in recipients' spam or junk mail folders.</p>"}}, "documentation": "<p>An object that contains information about the amount of email that was delivered to recipients.</p>"}, "WarmupStatus": {"type": "string", "documentation": "<p>The warmup status of a dedicated IP.</p>", "enum": ["IN_PROGRESS", "DONE"]}, "WebsiteURL": {"type": "string", "max": 1000, "min": 1, "pattern": "^(([^:/?#]+):)?(//([^/?#]*))?([^?#]*)(\\?([^#]*))?(#(.*))?", "sensitive": true}}, "documentation": "<fullname>Amazon SES API v2</fullname> <p> <a href=\"http://aws.amazon.com/ses\">Amazon SES</a> is an Amazon Web Services service that you can use to send email messages to your customers.</p> <p>If you're new to Amazon SES API v2, you might find it helpful to review the <a href=\"https://docs.aws.amazon.com/ses/latest/DeveloperGuide/\">Amazon Simple Email Service Developer Guide</a>. The <i>Amazon SES Developer Guide</i> provides information and code samples that demonstrate how to use Amazon SES API v2 features programmatically.</p>"}