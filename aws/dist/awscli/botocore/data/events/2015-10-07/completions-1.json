{"version": "1.0", "resources": {"Rule": {"operation": "ListRules", "resourceIdentifier": {"Name": "Rules[].Name", "EventPattern": "Rules[].EventPattern", "State": "Rules[].State", "Description": "Rules[].Description", "ScheduleExpression": "Rules[].ScheduleExpression", "RoleArn": "Rules[].RoleArn"}}}, "operations": {"DeleteRule": {"Name": {"completions": [{"parameters": {}, "resourceName": "Rule", "resourceIdentifier": "Name"}]}}, "DescribeRule": {"Name": {"completions": [{"parameters": {}, "resourceName": "Rule", "resourceIdentifier": "Name"}]}}, "DisableRule": {"Name": {"completions": [{"parameters": {}, "resourceName": "Rule", "resourceIdentifier": "Name"}]}}, "EnableRule": {"Name": {"completions": [{"parameters": {}, "resourceName": "Rule", "resourceIdentifier": "Name"}]}}, "PutRule": {"Name": {"completions": [{"parameters": {}, "resourceName": "Rule", "resourceIdentifier": "Name"}]}, "ScheduleExpression": {"completions": [{"parameters": {}, "resourceName": "Rule", "resourceIdentifier": "ScheduleExpression"}]}, "EventPattern": {"completions": [{"parameters": {}, "resourceName": "Rule", "resourceIdentifier": "EventPattern"}]}, "State": {"completions": [{"parameters": {}, "resourceName": "Rule", "resourceIdentifier": "State"}]}, "Description": {"completions": [{"parameters": {}, "resourceName": "Rule", "resourceIdentifier": "Description"}]}, "RoleArn": {"completions": [{"parameters": {}, "resourceName": "Rule", "resourceIdentifier": "RoleArn"}]}}, "TestEventPattern": {"EventPattern": {"completions": [{"parameters": {}, "resourceName": "Rule", "resourceIdentifier": "EventPattern"}]}}}}