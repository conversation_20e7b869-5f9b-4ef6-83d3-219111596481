{"version": "2.0", "metadata": {"apiVersion": "2023-09-07", "endpointPrefix": "freetier", "jsonVersion": "1.0", "protocol": "json", "serviceFullName": "AWS Free Tier", "serviceId": "FreeTier", "signatureVersion": "v4", "signingName": "freetier", "targetPrefix": "AWSFreeTierService", "uid": "freetier-2023-09-07"}, "operations": {"GetFreeTierUsage": {"name": "GetFreeTierUsage", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetFreeTierUsageRequest"}, "output": {"shape": "GetFreeTierUsageResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Returns a list of all Free Tier usage objects that match your filters.</p>"}}, "shapes": {"Dimension": {"type": "string", "enum": ["SERVICE", "OPERATION", "USAGE_TYPE", "REGION", "FREE_TIER_TYPE", "DESCRIPTION", "USAGE_PERCENTAGE"]}, "DimensionValues": {"type": "structure", "required": ["Key", "MatchOptions", "Values"], "members": {"Key": {"shape": "Dimension", "documentation": "<p>The name of the dimension that you want to filter on.</p>"}, "MatchOptions": {"shape": "MatchOptions", "documentation": "<p>The match options that you can use to filter your results. You can specify only one of these values in the array.</p>"}, "Values": {"shape": "Values", "documentation": "<p>The metadata values you can specify to filter upon, so that the results all match at least one of the specified values.</p>"}}, "documentation": "<p>Contains the specifications for the filters to use for your request.</p>"}, "Expression": {"type": "structure", "members": {"And": {"shape": "Expressions", "documentation": "<p>Return results that match all <code>Expressions</code> that you specified in the array.</p>"}, "Dimensions": {"shape": "DimensionValues", "documentation": "<p>The specific dimension, values, and match type to filter objects with.</p>"}, "Not": {"shape": "Expression", "documentation": "<p>Return results that don’t match the <code>Expression</code> that you specified.</p>"}, "Or": {"shape": "Expressions", "documentation": "<p>Return results that match any of the <code>Expressions</code> that you specified. in the array.</p>"}}, "documentation": "<p>Use <code>Expression</code> to filter in the <code>GetFreeTierUsage</code> API operation.</p> <p>You can use the following patterns:</p> <ul> <li> <p>Simple dimension values (<code>Dimensions</code> root operator)</p> </li> <li> <p>Complex expressions with logical operators (<code>AND</code>, <code>NOT</code>, and <code>OR</code> root operators).</p> </li> </ul> <p>For <i>simple dimension values</i>, you can set the dimension name, values, and match type for the filters that you plan to use.</p> <p> <b>Example for simple dimension values</b> </p> <p>You can filter to match exactly for <code>REGION==us-east-1 OR REGION==us-west-1</code>.</p> <p>The corresponding <code>Expression</code> appears like the following: <code>{ \"Dimensions\": { \"Key\": \"REGION\", \"Values\": [ \"us-east-1\", \"us-west-1\" ], \"MatchOptions\": [\"EQUALS\"] } }</code> </p> <p>As shown in the previous example, lists of dimension values are combined with <code>OR</code> when you apply the filter.</p> <p>For <i>complex expressions with logical operators</i>, you can have nested expressions to use the logical operators and specify advanced filtering.</p> <p> <b>Example for complex expressions with logical operators</b> </p> <p>You can filter by <code>((REGION == us-east-1 OR REGION == us-west-1) OR (SERVICE CONTAINS AWSLambda)) AND (USAGE_TYPE !CONTAINS DataTransfer)</code>.</p> <p>The corresponding <code>Expression</code> appears like the following: <code>{ \"And\": [ {\"Or\": [ {\"Dimensions\": { \"Key\": \"REGION\", \"Values\": [ \"us-east-1\", \"us-west-1\" ], \"MatchOptions\": [\"EQUALS\"] }}, {\"Dimensions\": { \"Key\": \"SERVICE\", \"Values\": [\"AWSLambda\"], \"MatchOptions\": [\"CONTAINS\"] } } ]}, {\"Not\": {\"Dimensions\": { \"Key\": \"USAGE_TYPE\", \"Values\": [\"DataTransfer\"], \"MatchOptions\": [\"CONTAINS\"] }}} ] }</code> </p> <p>In the following <b>Contents</b>, you must specify exactly one of the following root operators.</p>"}, "Expressions": {"type": "list", "member": {"shape": "Expression"}}, "FreeTierUsage": {"type": "structure", "members": {"actualUsageAmount": {"shape": "GenericDouble", "documentation": "<p>Describes the actual usage accrued month-to-day (MTD) that you've used so far.</p>"}, "description": {"shape": "GenericString", "documentation": "<p>The description of the Free Tier offer.</p>"}, "forecastedUsageAmount": {"shape": "GenericDouble", "documentation": "<p>Describes the forecasted usage by the month that you're expected to use.</p>"}, "freeTierType": {"shape": "GenericString", "documentation": "<p>Describes the type of the Free Tier offer. For example, the offer can be <code>\"12 Months Free\"</code>, <code>\"Always Free\"</code>, and <code>\"Free Trial\"</code>.</p>"}, "limit": {"shape": "GenericDouble", "documentation": "<p>Describes the maximum usage allowed in Free Tier.</p>"}, "operation": {"shape": "GenericString", "documentation": "<p>Describes <code>usageType</code> more granularly with the specific Amazon Web Service API operation. For example, this can be the <code>RunInstances</code> API operation for Amazon Elastic Compute Cloud.</p>"}, "region": {"shape": "GenericString", "documentation": "<p>Describes the Amazon Web Services Region for which this offer is applicable</p>"}, "service": {"shape": "GenericString", "documentation": "<p>The name of the Amazon Web Service providing the Free Tier offer. For example, this can be Amazon Elastic Compute Cloud.</p>"}, "unit": {"shape": "GenericString", "documentation": "<p>Describes the unit of the <code>usageType</code>, such as <code>Hrs</code>.</p>"}, "usageType": {"shape": "GenericString", "documentation": "<p>Describes the usage details of the offer. For example, this might be <code>Global-BoxUsage:freetrial</code>.</p>"}}, "documentation": "<p>Consists of a Amazon Web Services Free Tier offer’s metadata and your data usage for the offer.</p>"}, "FreeTierUsages": {"type": "list", "member": {"shape": "FreeTierUsage"}}, "GenericDouble": {"type": "double"}, "GenericString": {"type": "string", "max": 1024, "min": 0, "pattern": "^[\\S\\s]*$"}, "GetFreeTierUsageRequest": {"type": "structure", "members": {"filter": {"shape": "Expression", "documentation": "<p>An expression that specifies the conditions that you want each <code>FreeTierUsage</code> object to meet.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in the response. <code>MaxResults</code> means that there can be up to the specified number of values, but there might be fewer results based on your filters.</p>"}, "nextToken": {"shape": "NextPageToken", "documentation": "<p>The pagination token that indicates the next set of results to retrieve.</p>"}}}, "GetFreeTierUsageResponse": {"type": "structure", "required": ["freeTierUsages"], "members": {"freeTierUsages": {"shape": "FreeTierUsages", "documentation": "<p>The list of Free Tier usage objects that meet your filter expression.</p>"}, "nextToken": {"shape": "NextPageToken", "documentation": "<p>The pagination token that indicates the next set of results to retrieve.</p>"}}}, "InternalServerException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "GenericString"}}, "documentation": "<p>An unexpected error occurred during the processing of your request.</p>", "exception": true, "fault": true}, "MatchOption": {"type": "string", "enum": ["EQUALS", "STARTS_WITH", "ENDS_WITH", "CONTAINS", "GREATER_THAN_OR_EQUAL"]}, "MatchOptions": {"type": "list", "member": {"shape": "MatchOption"}}, "MaxResults": {"type": "integer", "box": true, "max": 1000, "min": 1}, "NextPageToken": {"type": "string", "max": 8192, "min": 1, "pattern": "^[\\S\\s]*$"}, "ThrottlingException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "GenericString"}}, "documentation": "<p>The request was denied due to request throttling.</p>", "exception": true}, "ValidationException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "GenericString"}}, "documentation": "<p>The input fails to satisfy the constraints specified by an Amazon Web Service.</p>", "exception": true}, "Value": {"type": "string", "max": 20, "min": 1, "pattern": "^[ a-zA-Z0-9\\-\\:\\.\\_\\/\\,\\$\\(\\)]*$"}, "Values": {"type": "list", "member": {"shape": "Value"}, "min": 1}}, "documentation": "<p>You can use the Amazon Web Services Free Tier API to query programmatically your Free Tier usage data.</p> <p>Free Tier tracks your monthly usage data for all free tier offers that are associated with your Amazon Web Services account. You can use the Free Tier API to filter and show only the data that you want.</p> <p>Service endpoint</p> <p>The Free Tier API provides the following endpoint:</p> <ul> <li> <p>https://freetier.us-east-1.api.aws</p> </li> </ul> <p>For more information, see <a href=\"https://docs.aws.amazon.com/awsaccountbilling/latest/aboutv2/billing-free-tier.html\">Using the Amazon Web Services Free Tier</a> in the <i>Billing User Guide</i>.</p>"}