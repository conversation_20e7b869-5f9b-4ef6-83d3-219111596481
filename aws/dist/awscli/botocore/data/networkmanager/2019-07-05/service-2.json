{"version": "2.0", "metadata": {"apiVersion": "2019-07-05", "endpointPrefix": "networkmanager", "jsonVersion": "1.1", "protocol": "rest-json", "protocols": ["rest-json"], "serviceAbbreviation": "NetworkManager", "serviceFullName": "AWS Network Manager", "serviceId": "NetworkManager", "signatureVersion": "v4", "signingName": "networkmanager", "uid": "networkmanager-2019-07-05", "auth": ["aws.auth#sigv4"]}, "operations": {"AcceptAttachment": {"name": "AcceptAttachment", "http": {"method": "POST", "requestUri": "/attachments/{attachmentId}/accept"}, "input": {"shape": "AcceptAttachmentRequest"}, "output": {"shape": "AcceptAttachmentResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Accepts a core network attachment request. </p> <p>Once the attachment request is accepted by a core network owner, the attachment is created and connected to a core network.</p>"}, "AssociateConnectPeer": {"name": "AssociateConnectPeer", "http": {"method": "POST", "requestUri": "/global-networks/{globalNetworkId}/connect-peer-associations"}, "input": {"shape": "AssociateConnectPeerRequest"}, "output": {"shape": "AssociateConnectPeerResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Associates a core network Connect peer with a device and optionally, with a link. </p> <p>If you specify a link, it must be associated with the specified device. You can only associate core network Connect peers that have been created on a core network Connect attachment on a core network. </p>"}, "AssociateCustomerGateway": {"name": "AssociateCustomerGateway", "http": {"method": "POST", "requestUri": "/global-networks/{globalNetworkId}/customer-gateway-associations"}, "input": {"shape": "AssociateCustomerGatewayRequest"}, "output": {"shape": "AssociateCustomerGatewayResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Associates a customer gateway with a device and optionally, with a link. If you specify a link, it must be associated with the specified device. </p> <p>You can only associate customer gateways that are connected to a VPN attachment on a transit gateway or core network registered in your global network. When you register a transit gateway or core network, customer gateways that are connected to the transit gateway are automatically included in the global network. To list customer gateways that are connected to a transit gateway, use the <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/APIReference/API_DescribeVpnConnections.html\">DescribeVpnConnections</a> EC2 API and filter by <code>transit-gateway-id</code>.</p> <p>You cannot associate a customer gateway with more than one device and link. </p>"}, "AssociateLink": {"name": "AssociateLink", "http": {"method": "POST", "requestUri": "/global-networks/{globalNetworkId}/link-associations"}, "input": {"shape": "AssociateLinkRequest"}, "output": {"shape": "AssociateLinkResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Associates a link to a device. A device can be associated to multiple links and a link can be associated to multiple devices. The device and link must be in the same global network and the same site.</p>"}, "AssociateTransitGatewayConnectPeer": {"name": "AssociateTransitGatewayConnectPeer", "http": {"method": "POST", "requestUri": "/global-networks/{globalNetworkId}/transit-gateway-connect-peer-associations"}, "input": {"shape": "AssociateTransitGatewayConnectPeerRequest"}, "output": {"shape": "AssociateTransitGatewayConnectPeerResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Associates a transit gateway Connect peer with a device, and optionally, with a link. If you specify a link, it must be associated with the specified device. </p> <p>You can only associate transit gateway Connect peers that have been created on a transit gateway that's registered in your global network.</p> <p>You cannot associate a transit gateway Connect peer with more than one device and link. </p>"}, "CreateConnectAttachment": {"name": "CreateConnectAttachment", "http": {"method": "POST", "requestUri": "/connect-attachments"}, "input": {"shape": "CreateConnectAttachmentRequest"}, "output": {"shape": "CreateConnectAttachmentResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates a core network Connect attachment from a specified core network attachment. </p> <p>A core network Connect attachment is a GRE-based tunnel attachment that you can use to establish a connection between a core network and an appliance. A core network Connect attachment uses an existing VPC attachment as the underlying transport mechanism.</p>"}, "CreateConnectPeer": {"name": "CreateConnectPeer", "http": {"method": "POST", "requestUri": "/connect-peers"}, "input": {"shape": "CreateConnectPeerRequest"}, "output": {"shape": "CreateConnectPeerResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates a core network Connect peer for a specified core network connect attachment between a core network and an appliance. The peer address and transit gateway address must be the same IP address family (IPv4 or IPv6).</p>"}, "CreateConnection": {"name": "CreateConnection", "http": {"method": "POST", "requestUri": "/global-networks/{globalNetworkId}/connections"}, "input": {"shape": "CreateConnectionRequest"}, "output": {"shape": "CreateConnectionResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates a connection between two devices. The devices can be a physical or virtual appliance that connects to a third-party appliance in a VPC, or a physical appliance that connects to another physical appliance in an on-premises network.</p>"}, "CreateCoreNetwork": {"name": "CreateCoreNetwork", "http": {"method": "POST", "requestUri": "/core-networks"}, "input": {"shape": "CreateCoreNetworkRequest"}, "output": {"shape": "CreateCoreNetworkResponse"}, "errors": [{"shape": "CoreNetworkPolicyException"}, {"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates a core network as part of your global network, and optionally, with a core network policy.</p>"}, "CreateDevice": {"name": "CreateDevice", "http": {"method": "POST", "requestUri": "/global-networks/{globalNetworkId}/devices"}, "input": {"shape": "CreateDeviceRequest"}, "output": {"shape": "CreateDeviceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates a new device in a global network. If you specify both a site ID and a location, the location of the site is used for visualization in the Network Manager console.</p>"}, "CreateDirectConnectGatewayAttachment": {"name": "CreateDirectConnectGatewayAttachment", "http": {"method": "POST", "requestUri": "/direct-connect-gateway-attachments"}, "input": {"shape": "CreateDirectConnectGatewayAttachmentRequest"}, "output": {"shape": "CreateDirectConnectGatewayAttachmentResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates an Amazon Web Services Direct Connect gateway attachment </p>"}, "CreateGlobalNetwork": {"name": "CreateGlobalNetwork", "http": {"method": "POST", "requestUri": "/global-networks"}, "input": {"shape": "CreateGlobalNetworkRequest"}, "output": {"shape": "CreateGlobalNetworkResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates a new, empty global network.</p>"}, "CreateLink": {"name": "CreateLink", "http": {"method": "POST", "requestUri": "/global-networks/{globalNetworkId}/links"}, "input": {"shape": "CreateLinkRequest"}, "output": {"shape": "CreateLinkResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates a new link for a specified site.</p>"}, "CreateSite": {"name": "CreateSite", "http": {"method": "POST", "requestUri": "/global-networks/{globalNetworkId}/sites"}, "input": {"shape": "CreateSiteRequest"}, "output": {"shape": "CreateSiteResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates a new site in a global network.</p>"}, "CreateSiteToSiteVpnAttachment": {"name": "CreateSiteToSiteVpnAttachment", "http": {"method": "POST", "requestUri": "/site-to-site-vpn-attachments"}, "input": {"shape": "CreateSiteToSiteVpnAttachmentRequest"}, "output": {"shape": "CreateSiteToSiteVpnAttachmentResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates an Amazon Web Services site-to-site VPN attachment on an edge location of a core network.</p>"}, "CreateTransitGatewayPeering": {"name": "CreateTransitGatewayPeering", "http": {"method": "POST", "requestUri": "/transit-gateway-peerings"}, "input": {"shape": "CreateTransitGatewayPeeringRequest"}, "output": {"shape": "CreateTransitGatewayPeeringResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates a transit gateway peering connection.</p>"}, "CreateTransitGatewayRouteTableAttachment": {"name": "CreateTransitGatewayRouteTableAttachment", "http": {"method": "POST", "requestUri": "/transit-gateway-route-table-attachments"}, "input": {"shape": "CreateTransitGatewayRouteTableAttachmentRequest"}, "output": {"shape": "CreateTransitGatewayRouteTableAttachmentResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates a transit gateway route table attachment.</p>"}, "CreateVpcAttachment": {"name": "CreateVpcAttachment", "http": {"method": "POST", "requestUri": "/vpc-attachments"}, "input": {"shape": "CreateVpcAttachmentRequest"}, "output": {"shape": "CreateVpcAttachmentResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates a VPC attachment on an edge location of a core network.</p>"}, "DeleteAttachment": {"name": "DeleteAttachment", "http": {"method": "DELETE", "requestUri": "/attachments/{attachmentId}"}, "input": {"shape": "DeleteAttachmentRequest"}, "output": {"shape": "DeleteAttachmentResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes an attachment. Supports all attachment types.</p>"}, "DeleteConnectPeer": {"name": "DeleteConnectPeer", "http": {"method": "DELETE", "requestUri": "/connect-peers/{connectPeerId}"}, "input": {"shape": "DeleteConnectPeerRequest"}, "output": {"shape": "DeleteConnectPeerResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes a Connect peer.</p>"}, "DeleteConnection": {"name": "DeleteConnection", "http": {"method": "DELETE", "requestUri": "/global-networks/{globalNetworkId}/connections/{connectionId}"}, "input": {"shape": "DeleteConnectionRequest"}, "output": {"shape": "DeleteConnectionResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes the specified connection in your global network.</p>"}, "DeleteCoreNetwork": {"name": "DeleteCoreNetwork", "http": {"method": "DELETE", "requestUri": "/core-networks/{coreNetworkId}"}, "input": {"shape": "DeleteCoreNetworkRequest"}, "output": {"shape": "DeleteCoreNetworkResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes a core network along with all core network policies. This can only be done if there are no attachments on a core network.</p>"}, "DeleteCoreNetworkPolicyVersion": {"name": "DeleteCoreNetworkPolicyVersion", "http": {"method": "DELETE", "requestUri": "/core-networks/{coreNetworkId}/core-network-policy-versions/{policyVersionId}"}, "input": {"shape": "DeleteCoreNetworkPolicyVersionRequest"}, "output": {"shape": "DeleteCoreNetworkPolicyVersionResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ConflictException"}], "documentation": "<p>Deletes a policy version from a core network. You can't delete the current LIVE policy.</p>"}, "DeleteDevice": {"name": "DeleteDevice", "http": {"method": "DELETE", "requestUri": "/global-networks/{globalNetworkId}/devices/{deviceId}"}, "input": {"shape": "DeleteDeviceRequest"}, "output": {"shape": "DeleteDeviceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes an existing device. You must first disassociate the device from any links and customer gateways.</p>"}, "DeleteGlobalNetwork": {"name": "DeleteGlobalNetwork", "http": {"method": "DELETE", "requestUri": "/global-networks/{globalNetworkId}"}, "input": {"shape": "DeleteGlobalNetworkRequest"}, "output": {"shape": "DeleteGlobalNetworkResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes an existing global network. You must first delete all global network objects (devices, links, and sites), deregister all transit gateways, and delete any core networks.</p>"}, "DeleteLink": {"name": "DeleteLink", "http": {"method": "DELETE", "requestUri": "/global-networks/{globalNetworkId}/links/{linkId}"}, "input": {"shape": "DeleteLinkRequest"}, "output": {"shape": "DeleteLinkResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes an existing link. You must first disassociate the link from any devices and customer gateways.</p>"}, "DeletePeering": {"name": "DeletePeering", "http": {"method": "DELETE", "requestUri": "/peerings/{peeringId}"}, "input": {"shape": "DeletePeeringRequest"}, "output": {"shape": "DeletePeeringResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes an existing peering connection.</p>"}, "DeleteResourcePolicy": {"name": "DeleteResourcePolicy", "http": {"method": "DELETE", "requestUri": "/resource-policy/{resourceArn}"}, "input": {"shape": "DeleteResourcePolicyRequest"}, "output": {"shape": "DeleteResourcePolicyResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes a resource policy for the specified resource. This revokes the access of the principals specified in the resource policy.</p>"}, "DeleteSite": {"name": "DeleteSite", "http": {"method": "DELETE", "requestUri": "/global-networks/{globalNetworkId}/sites/{siteId}"}, "input": {"shape": "DeleteSiteRequest"}, "output": {"shape": "DeleteSiteResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes an existing site. The site cannot be associated with any device or link.</p>"}, "DeregisterTransitGateway": {"name": "DeregisterTransitGateway", "http": {"method": "DELETE", "requestUri": "/global-networks/{globalNetworkId}/transit-gateway-registrations/{transitGatewayArn}"}, "input": {"shape": "DeregisterTransitGatewayRequest"}, "output": {"shape": "DeregisterTransitGatewayResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deregisters a transit gateway from your global network. This action does not delete your transit gateway, or modify any of its attachments. This action removes any customer gateway associations.</p>"}, "DescribeGlobalNetworks": {"name": "DescribeGlobalNetworks", "http": {"method": "GET", "requestUri": "/global-networks"}, "input": {"shape": "DescribeGlobalNetworksRequest"}, "output": {"shape": "DescribeGlobalNetworksResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Describes one or more global networks. By default, all global networks are described. To describe the objects in your global network, you must use the appropriate <code>Get*</code> action. For example, to list the transit gateways in your global network, use <a>GetTransitGatewayRegistrations</a>.</p>"}, "DisassociateConnectPeer": {"name": "DisassociateConnectPeer", "http": {"method": "DELETE", "requestUri": "/global-networks/{globalNetworkId}/connect-peer-associations/{connectPeerId}"}, "input": {"shape": "DisassociateConnectPeerRequest"}, "output": {"shape": "DisassociateConnectPeerResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Disassociates a core network Connect peer from a device and a link. </p>"}, "DisassociateCustomerGateway": {"name": "DisassociateCustomerGateway", "http": {"method": "DELETE", "requestUri": "/global-networks/{globalNetworkId}/customer-gateway-associations/{customerGatewayArn}"}, "input": {"shape": "DisassociateCustomerGatewayRequest"}, "output": {"shape": "DisassociateCustomerGatewayResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Disassociates a customer gateway from a device and a link.</p>"}, "DisassociateLink": {"name": "DisassociateLink", "http": {"method": "DELETE", "requestUri": "/global-networks/{globalNetworkId}/link-associations"}, "input": {"shape": "DisassociateLinkRequest"}, "output": {"shape": "DisassociateLinkResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Disassociates an existing device from a link. You must first disassociate any customer gateways that are associated with the link.</p>"}, "DisassociateTransitGatewayConnectPeer": {"name": "DisassociateTransitGatewayConnectPeer", "http": {"method": "DELETE", "requestUri": "/global-networks/{globalNetworkId}/transit-gateway-connect-peer-associations/{transitGatewayConnectPeerArn}"}, "input": {"shape": "DisassociateTransitGatewayConnectPeerRequest"}, "output": {"shape": "DisassociateTransitGatewayConnectPeerResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Disassociates a transit gateway Connect peer from a device and link.</p>"}, "ExecuteCoreNetworkChangeSet": {"name": "ExecuteCoreNetworkChangeSet", "http": {"method": "POST", "requestUri": "/core-networks/{coreNetworkId}/core-network-change-sets/{policyVersionId}/execute"}, "input": {"shape": "ExecuteCoreNetworkChangeSetRequest"}, "output": {"shape": "ExecuteCoreNetworkChangeSetResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ConflictException"}], "documentation": "<p>Executes a change set on your core network. Deploys changes globally based on the policy submitted..</p>"}, "GetConnectAttachment": {"name": "GetConnectAttachment", "http": {"method": "GET", "requestUri": "/connect-attachments/{attachmentId}"}, "input": {"shape": "GetConnectAttachmentRequest"}, "output": {"shape": "GetConnectAttachmentResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Returns information about a core network Connect attachment.</p>"}, "GetConnectPeer": {"name": "GetConnectPeer", "http": {"method": "GET", "requestUri": "/connect-peers/{connectPeerId}"}, "input": {"shape": "GetConnectPeerRequest"}, "output": {"shape": "GetConnectPeerResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Returns information about a core network Connect peer.</p>"}, "GetConnectPeerAssociations": {"name": "GetConnectPeerAssociations", "http": {"method": "GET", "requestUri": "/global-networks/{globalNetworkId}/connect-peer-associations"}, "input": {"shape": "GetConnectPeerAssociationsRequest"}, "output": {"shape": "GetConnectPeerAssociationsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Returns information about a core network Connect peer associations.</p>"}, "GetConnections": {"name": "GetConnections", "http": {"method": "GET", "requestUri": "/global-networks/{globalNetworkId}/connections"}, "input": {"shape": "GetConnectionsRequest"}, "output": {"shape": "GetConnectionsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets information about one or more of your connections in a global network.</p>"}, "GetCoreNetwork": {"name": "GetCoreNetwork", "http": {"method": "GET", "requestUri": "/core-networks/{coreNetworkId}"}, "input": {"shape": "GetCoreNetworkRequest"}, "output": {"shape": "GetCoreNetworkResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Returns information about the LIVE policy for a core network.</p>"}, "GetCoreNetworkChangeEvents": {"name": "GetCoreNetworkChangeEvents", "http": {"method": "GET", "requestUri": "/core-networks/{coreNetworkId}/core-network-change-events/{policyVersionId}"}, "input": {"shape": "GetCoreNetworkChangeEventsRequest"}, "output": {"shape": "GetCoreNetworkChangeEventsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Returns information about a core network change event.</p>"}, "GetCoreNetworkChangeSet": {"name": "GetCoreNetworkChangeSet", "http": {"method": "GET", "requestUri": "/core-networks/{coreNetworkId}/core-network-change-sets/{policyVersionId}"}, "input": {"shape": "GetCoreNetworkChangeSetRequest"}, "output": {"shape": "GetCoreNetworkChangeSetResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Returns a change set between the LIVE core network policy and a submitted policy.</p>"}, "GetCoreNetworkPolicy": {"name": "GetCoreNetworkPolicy", "http": {"method": "GET", "requestUri": "/core-networks/{coreNetworkId}/core-network-policy"}, "input": {"shape": "GetCoreNetworkPolicyRequest"}, "output": {"shape": "GetCoreNetworkPolicyResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Returns details about a core network policy. You can get details about your current live policy or any previous policy version.</p>"}, "GetCustomerGatewayAssociations": {"name": "GetCustomerGatewayAssociations", "http": {"method": "GET", "requestUri": "/global-networks/{globalNetworkId}/customer-gateway-associations"}, "input": {"shape": "GetCustomerGatewayAssociationsRequest"}, "output": {"shape": "GetCustomerGatewayAssociationsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets the association information for customer gateways that are associated with devices and links in your global network.</p>"}, "GetDevices": {"name": "GetDevices", "http": {"method": "GET", "requestUri": "/global-networks/{globalNetworkId}/devices"}, "input": {"shape": "GetDevicesRequest"}, "output": {"shape": "GetDevicesResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets information about one or more of your devices in a global network.</p>"}, "GetDirectConnectGatewayAttachment": {"name": "GetDirectConnectGatewayAttachment", "http": {"method": "GET", "requestUri": "/direct-connect-gateway-attachments/{attachmentId}"}, "input": {"shape": "GetDirectConnectGatewayAttachmentRequest"}, "output": {"shape": "GetDirectConnectGatewayAttachmentResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Returns information about a specific Amazon Web Services Direct Connect gateway attachment.</p>"}, "GetLinkAssociations": {"name": "GetLinkAssociations", "http": {"method": "GET", "requestUri": "/global-networks/{globalNetworkId}/link-associations"}, "input": {"shape": "GetLinkAssociationsRequest"}, "output": {"shape": "GetLinkAssociationsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets the link associations for a device or a link. Either the device ID or the link ID must be specified.</p>"}, "GetLinks": {"name": "GetLinks", "http": {"method": "GET", "requestUri": "/global-networks/{globalNetworkId}/links"}, "input": {"shape": "GetLinksRequest"}, "output": {"shape": "GetLinksResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets information about one or more links in a specified global network.</p> <p>If you specify the site ID, you cannot specify the type or provider in the same request. You can specify the type and provider in the same request.</p>"}, "GetNetworkResourceCounts": {"name": "GetNetworkResourceCounts", "http": {"method": "GET", "requestUri": "/global-networks/{globalNetworkId}/network-resource-count"}, "input": {"shape": "GetNetworkResourceCountsRequest"}, "output": {"shape": "GetNetworkResourceCountsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets the count of network resources, by resource type, for the specified global network.</p>"}, "GetNetworkResourceRelationships": {"name": "GetNetworkResourceRelationships", "http": {"method": "GET", "requestUri": "/global-networks/{globalNetworkId}/network-resource-relationships"}, "input": {"shape": "GetNetworkResourceRelationshipsRequest"}, "output": {"shape": "GetNetworkResourceRelationshipsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets the network resource relationships for the specified global network.</p>"}, "GetNetworkResources": {"name": "GetNetworkResources", "http": {"method": "GET", "requestUri": "/global-networks/{globalNetworkId}/network-resources"}, "input": {"shape": "GetNetworkResourcesRequest"}, "output": {"shape": "GetNetworkResourcesResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Describes the network resources for the specified global network.</p> <p>The results include information from the corresponding Describe call for the resource, minus any sensitive information such as pre-shared keys.</p>"}, "GetNetworkRoutes": {"name": "GetNetworkRoutes", "http": {"method": "POST", "requestUri": "/global-networks/{globalNetworkId}/network-routes"}, "input": {"shape": "GetNetworkRoutesRequest"}, "output": {"shape": "GetNetworkRoutesResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets the network routes of the specified global network.</p>"}, "GetNetworkTelemetry": {"name": "GetNetworkTelemetry", "http": {"method": "GET", "requestUri": "/global-networks/{globalNetworkId}/network-telemetry"}, "input": {"shape": "GetNetworkTelemetryRequest"}, "output": {"shape": "GetNetworkTelemetryResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets the network telemetry of the specified global network.</p>"}, "GetResourcePolicy": {"name": "GetResourcePolicy", "http": {"method": "GET", "requestUri": "/resource-policy/{resourceArn}"}, "input": {"shape": "GetResourcePolicyRequest"}, "output": {"shape": "GetResourcePolicyResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Returns information about a resource policy.</p>"}, "GetRouteAnalysis": {"name": "GetRouteAnalysis", "http": {"method": "GET", "requestUri": "/global-networks/{globalNetworkId}/route-analyses/{routeAnalysisId}"}, "input": {"shape": "GetRouteAnalysisRequest"}, "output": {"shape": "GetRouteAnalysisResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets information about the specified route analysis.</p>"}, "GetSiteToSiteVpnAttachment": {"name": "GetSiteToSiteVpnAttachment", "http": {"method": "GET", "requestUri": "/site-to-site-vpn-attachments/{attachmentId}"}, "input": {"shape": "GetSiteToSiteVpnAttachmentRequest"}, "output": {"shape": "GetSiteToSiteVpnAttachmentResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Returns information about a site-to-site VPN attachment.</p>"}, "GetSites": {"name": "GetSites", "http": {"method": "GET", "requestUri": "/global-networks/{globalNetworkId}/sites"}, "input": {"shape": "GetSitesRequest"}, "output": {"shape": "GetSitesResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets information about one or more of your sites in a global network.</p>"}, "GetTransitGatewayConnectPeerAssociations": {"name": "GetTransitGatewayConnectPeerAssociations", "http": {"method": "GET", "requestUri": "/global-networks/{globalNetworkId}/transit-gateway-connect-peer-associations"}, "input": {"shape": "GetTransitGatewayConnectPeerAssociationsRequest"}, "output": {"shape": "GetTransitGatewayConnectPeerAssociationsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets information about one or more of your transit gateway Connect peer associations in a global network.</p>"}, "GetTransitGatewayPeering": {"name": "GetTransitGatewayPeering", "http": {"method": "GET", "requestUri": "/transit-gateway-peerings/{peeringId}"}, "input": {"shape": "GetTransitGatewayPeeringRequest"}, "output": {"shape": "GetTransitGatewayPeeringResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Returns information about a transit gateway peer.</p>"}, "GetTransitGatewayRegistrations": {"name": "GetTransitGatewayRegistrations", "http": {"method": "GET", "requestUri": "/global-networks/{globalNetworkId}/transit-gateway-registrations"}, "input": {"shape": "GetTransitGatewayRegistrationsRequest"}, "output": {"shape": "GetTransitGatewayRegistrationsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets information about the transit gateway registrations in a specified global network.</p>"}, "GetTransitGatewayRouteTableAttachment": {"name": "GetTransitGatewayRouteTableAttachment", "http": {"method": "GET", "requestUri": "/transit-gateway-route-table-attachments/{attachmentId}"}, "input": {"shape": "GetTransitGatewayRouteTableAttachmentRequest"}, "output": {"shape": "GetTransitGatewayRouteTableAttachmentResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Returns information about a transit gateway route table attachment.</p>"}, "GetVpcAttachment": {"name": "GetVpcAttachment", "http": {"method": "GET", "requestUri": "/vpc-attachments/{attachmentId}"}, "input": {"shape": "GetVpcAttachmentRequest"}, "output": {"shape": "GetVpcAttachmentResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Returns information about a VPC attachment.</p>"}, "ListAttachments": {"name": "ListAttachments", "http": {"method": "GET", "requestUri": "/attachments"}, "input": {"shape": "ListAttachmentsRequest"}, "output": {"shape": "ListAttachmentsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Returns a list of core network attachments.</p>"}, "ListConnectPeers": {"name": "ListConnectPeers", "http": {"method": "GET", "requestUri": "/connect-peers"}, "input": {"shape": "ListConnectPeersRequest"}, "output": {"shape": "ListConnectPeersResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Returns a list of core network Connect peers.</p>"}, "ListCoreNetworkPolicyVersions": {"name": "ListCoreNetworkPolicyVersions", "http": {"method": "GET", "requestUri": "/core-networks/{coreNetworkId}/core-network-policy-versions"}, "input": {"shape": "ListCoreNetworkPolicyVersionsRequest"}, "output": {"shape": "ListCoreNetworkPolicyVersionsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Returns a list of core network policy versions.</p>"}, "ListCoreNetworks": {"name": "ListCoreNetworks", "http": {"method": "GET", "requestUri": "/core-networks"}, "input": {"shape": "ListCoreNetworksRequest"}, "output": {"shape": "ListCoreNetworksResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Returns a list of owned and shared core networks.</p>"}, "ListOrganizationServiceAccessStatus": {"name": "ListOrganizationServiceAccessStatus", "http": {"method": "GET", "requestUri": "/organizations/service-access"}, "input": {"shape": "ListOrganizationServiceAccessStatusRequest"}, "output": {"shape": "ListOrganizationServiceAccessStatusResponse"}, "documentation": "<p>Gets the status of the Service Linked Role (SLR) deployment for the accounts in a given Amazon Web Services Organization.</p>"}, "ListPeerings": {"name": "ListPeerings", "http": {"method": "GET", "requestUri": "/peerings"}, "input": {"shape": "ListPeeringsRequest"}, "output": {"shape": "ListPeeringsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists the peerings for a core network.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags/{resourceArn}"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists the tags for a specified resource.</p>"}, "PutCoreNetworkPolicy": {"name": "PutCoreNetworkPolicy", "http": {"method": "POST", "requestUri": "/core-networks/{coreNetworkId}/core-network-policy"}, "input": {"shape": "PutCoreNetworkPolicyRequest"}, "output": {"shape": "PutCoreNetworkPolicyResponse"}, "errors": [{"shape": "CoreNetworkPolicyException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ConflictException"}], "documentation": "<p>Creates a new, immutable version of a core network policy. A subsequent change set is created showing the differences between the LIVE policy and the submitted policy.</p>"}, "PutResourcePolicy": {"name": "PutResourcePolicy", "http": {"method": "POST", "requestUri": "/resource-policy/{resourceArn}"}, "input": {"shape": "PutResourcePolicyRequest"}, "output": {"shape": "PutResourcePolicyResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates or updates a resource policy.</p>"}, "RegisterTransitGateway": {"name": "RegisterTransitGateway", "http": {"method": "POST", "requestUri": "/global-networks/{globalNetworkId}/transit-gateway-registrations"}, "input": {"shape": "RegisterTransitGatewayRequest"}, "output": {"shape": "RegisterTransitGatewayResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Registers a transit gateway in your global network. Not all Regions support transit gateways for global networks. For a list of the supported Regions, see <a href=\"https://docs.aws.amazon.com/network-manager/latest/tgwnm/what-are-global-networks.html#nm-available-regions\">Region Availability</a> in the <i>Amazon Web Services Transit Gateways for Global Networks User Guide</i>. The transit gateway can be in any of the supported Amazon Web Services Regions, but it must be owned by the same Amazon Web Services account that owns the global network. You cannot register a transit gateway in more than one global network.</p>"}, "RejectAttachment": {"name": "RejectAttachment", "http": {"method": "POST", "requestUri": "/attachments/{attachmentId}/reject"}, "input": {"shape": "RejectAttachmentRequest"}, "output": {"shape": "RejectAttachmentResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Rejects a core network attachment request.</p>"}, "RestoreCoreNetworkPolicyVersion": {"name": "RestoreCoreNetworkPolicyVersion", "http": {"method": "POST", "requestUri": "/core-networks/{coreNetworkId}/core-network-policy-versions/{policyVersionId}/restore"}, "input": {"shape": "RestoreCoreNetworkPolicyVersionRequest"}, "output": {"shape": "RestoreCoreNetworkPolicyVersionResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ConflictException"}], "documentation": "<p><PERSON>ores a previous policy version as a new, immutable version of a core network policy. A subsequent change set is created showing the differences between the LIVE policy and restored policy.</p>"}, "StartOrganizationServiceAccessUpdate": {"name": "StartOrganizationServiceAccessUpdate", "http": {"method": "POST", "requestUri": "/organizations/service-access"}, "input": {"shape": "StartOrganizationServiceAccessUpdateRequest"}, "output": {"shape": "StartOrganizationServiceAccessUpdateResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Enables the Network Manager service for an Amazon Web Services Organization. This can only be called by a management account within the organization. </p>"}, "StartRouteAnalysis": {"name": "StartRouteAnalysis", "http": {"method": "POST", "requestUri": "/global-networks/{globalNetworkId}/route-analyses"}, "input": {"shape": "StartRouteAnalysisRequest"}, "output": {"shape": "StartRouteAnalysisResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Starts analyzing the routing path between the specified source and destination. For more information, see <a href=\"https://docs.aws.amazon.com/vpc/latest/tgw/route-analyzer.html\">Route Analyzer</a>.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags/{resourceArn}"}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Tags a specified resource.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags/{resourceArn}"}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Removes tags from a specified resource.</p>"}, "UpdateConnection": {"name": "UpdateConnection", "http": {"method": "PATCH", "requestUri": "/global-networks/{globalNetworkId}/connections/{connectionId}"}, "input": {"shape": "UpdateConnectionRequest"}, "output": {"shape": "UpdateConnectionResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Updates the information for an existing connection. To remove information for any of the parameters, specify an empty string.</p>"}, "UpdateCoreNetwork": {"name": "UpdateCoreNetwork", "http": {"method": "PATCH", "requestUri": "/core-networks/{coreNetworkId}"}, "input": {"shape": "UpdateCoreNetworkRequest"}, "output": {"shape": "UpdateCoreNetworkResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Updates the description of a core network.</p>"}, "UpdateDevice": {"name": "UpdateDevice", "http": {"method": "PATCH", "requestUri": "/global-networks/{globalNetworkId}/devices/{deviceId}"}, "input": {"shape": "UpdateDeviceRequest"}, "output": {"shape": "UpdateDeviceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Updates the details for an existing device. To remove information for any of the parameters, specify an empty string.</p>"}, "UpdateDirectConnectGatewayAttachment": {"name": "UpdateDirectConnectGatewayAttachment", "http": {"method": "PATCH", "requestUri": "/direct-connect-gateway-attachments/{attachmentId}"}, "input": {"shape": "UpdateDirectConnectGatewayAttachmentRequest"}, "output": {"shape": "UpdateDirectConnectGatewayAttachmentResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Updates the edge locations associated with an Amazon Web Services Direct Connect gateway attachment. </p>"}, "UpdateGlobalNetwork": {"name": "UpdateGlobalNetwork", "http": {"method": "PATCH", "requestUri": "/global-networks/{globalNetworkId}"}, "input": {"shape": "UpdateGlobalNetworkRequest"}, "output": {"shape": "UpdateGlobalNetworkResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Updates an existing global network. To remove information for any of the parameters, specify an empty string.</p>"}, "UpdateLink": {"name": "UpdateLink", "http": {"method": "PATCH", "requestUri": "/global-networks/{globalNetworkId}/links/{linkId}"}, "input": {"shape": "UpdateLinkRequest"}, "output": {"shape": "UpdateLinkResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Updates the details for an existing link. To remove information for any of the parameters, specify an empty string.</p>"}, "UpdateNetworkResourceMetadata": {"name": "UpdateNetworkResourceMetadata", "http": {"method": "PATCH", "requestUri": "/global-networks/{globalNetworkId}/network-resources/{resourceArn}/metadata"}, "input": {"shape": "UpdateNetworkResourceMetadataRequest"}, "output": {"shape": "UpdateNetworkResourceMetadataResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Updates the resource metadata for the specified global network.</p>"}, "UpdateSite": {"name": "UpdateSite", "http": {"method": "PATCH", "requestUri": "/global-networks/{globalNetworkId}/sites/{siteId}"}, "input": {"shape": "UpdateSiteRequest"}, "output": {"shape": "UpdateSiteResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Updates the information for an existing site. To remove information for any of the parameters, specify an empty string.</p>"}, "UpdateVpcAttachment": {"name": "UpdateVpcAttachment", "http": {"method": "PATCH", "requestUri": "/vpc-attachments/{attachmentId}"}, "input": {"shape": "UpdateVpcAttachmentRequest"}, "output": {"shape": "UpdateVpcAttachmentResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Updates a VPC attachment.</p>"}}, "shapes": {"AWSAccountId": {"type": "string", "max": 12, "min": 12, "pattern": "[\\s\\S]*"}, "AWSLocation": {"type": "structure", "members": {"Zone": {"shape": "ConstrainedString", "documentation": "<p>The Zone that the device is located in. Specify the ID of an Availability Zone, Local Zone, Wavelength Zone, or an Outpost.</p>"}, "SubnetArn": {"shape": "SubnetArn", "documentation": "<p>The Amazon Resource Name (ARN) of the subnet that the device is located in.</p>"}}, "documentation": "<p>Specifies a location in Amazon Web Services.</p>"}, "AcceptAttachmentRequest": {"type": "structure", "required": ["AttachmentId"], "members": {"AttachmentId": {"shape": "AttachmentId", "documentation": "<p>The ID of the attachment. </p>", "location": "uri", "locationName": "attachmentId"}}}, "AcceptAttachmentResponse": {"type": "structure", "members": {"Attachment": {"shape": "Attachment", "documentation": "<p>The response to the attachment request. </p>"}}}, "AccessDeniedException": {"type": "structure", "required": ["Message"], "members": {"Message": {"shape": "ServerSideString"}}, "documentation": "<p>You do not have sufficient access to perform this action.</p>", "error": {"httpStatusCode": 403}, "exception": true}, "AccountId": {"type": "string", "max": 50, "min": 0}, "AccountStatus": {"type": "structure", "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The ID of an account within the Amazon Web Services Organization.</p>"}, "SLRDeploymentStatus": {"shape": "SLRDeploymentStatus", "documentation": "<p>The status of SLR deployment for the account.</p>"}}, "documentation": "<p>Describes the current status of an account within an Amazon Web Services Organization, including service-linked roles (SLRs).</p>"}, "AccountStatusList": {"type": "list", "member": {"shape": "Account<PERSON><PERSON><PERSON>"}}, "Action": {"type": "string", "max": 50, "min": 0}, "AssociateConnectPeerRequest": {"type": "structure", "required": ["GlobalNetworkId", "ConnectPeerId", "DeviceId"], "members": {"GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of your global network.</p>", "location": "uri", "locationName": "globalNetworkId"}, "ConnectPeerId": {"shape": "ConnectPeerId", "documentation": "<p>The ID of the Connect peer.</p>"}, "DeviceId": {"shape": "DeviceId", "documentation": "<p>The ID of the device.</p>"}, "LinkId": {"shape": "LinkId", "documentation": "<p>The ID of the link.</p>"}}}, "AssociateConnectPeerResponse": {"type": "structure", "members": {"ConnectPeerAssociation": {"shape": "ConnectPeerAssociation", "documentation": "<p>The response to the Connect peer request.</p>"}}}, "AssociateCustomerGatewayRequest": {"type": "structure", "required": ["CustomerGatewayArn", "GlobalNetworkId", "DeviceId"], "members": {"CustomerGatewayArn": {"shape": "CustomerGatewayArn", "documentation": "<p>The Amazon Resource Name (ARN) of the customer gateway.</p>"}, "GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>", "location": "uri", "locationName": "globalNetworkId"}, "DeviceId": {"shape": "DeviceId", "documentation": "<p>The ID of the device.</p>"}, "LinkId": {"shape": "LinkId", "documentation": "<p>The ID of the link.</p>"}}}, "AssociateCustomerGatewayResponse": {"type": "structure", "members": {"CustomerGatewayAssociation": {"shape": "CustomerGatewayAssociation", "documentation": "<p>The customer gateway association.</p>"}}}, "AssociateLinkRequest": {"type": "structure", "required": ["GlobalNetworkId", "DeviceId", "LinkId"], "members": {"GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>", "location": "uri", "locationName": "globalNetworkId"}, "DeviceId": {"shape": "DeviceId", "documentation": "<p>The ID of the device.</p>"}, "LinkId": {"shape": "LinkId", "documentation": "<p>The ID of the link.</p>"}}}, "AssociateLinkResponse": {"type": "structure", "members": {"LinkAssociation": {"shape": "LinkAssociation", "documentation": "<p>The link association.</p>"}}}, "AssociateTransitGatewayConnectPeerRequest": {"type": "structure", "required": ["GlobalNetworkId", "TransitGatewayConnectPeerArn", "DeviceId"], "members": {"GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>", "location": "uri", "locationName": "globalNetworkId"}, "TransitGatewayConnectPeerArn": {"shape": "TransitGatewayConnectPeerArn", "documentation": "<p>The Amazon Resource Name (ARN) of the Connect peer.</p>"}, "DeviceId": {"shape": "DeviceId", "documentation": "<p>The ID of the device.</p>"}, "LinkId": {"shape": "LinkId", "documentation": "<p>The ID of the link.</p>"}}}, "AssociateTransitGatewayConnectPeerResponse": {"type": "structure", "members": {"TransitGatewayConnectPeerAssociation": {"shape": "TransitGatewayConnectPeerAssociation", "documentation": "<p>The transit gateway Connect peer association.</p>"}}}, "Attachment": {"type": "structure", "members": {"CoreNetworkId": {"shape": "CoreNetworkId", "documentation": "<p>The ID of a core network.</p>"}, "CoreNetworkArn": {"shape": "CoreNetworkArn", "documentation": "<p>The ARN of a core network.</p>"}, "AttachmentId": {"shape": "AttachmentId", "documentation": "<p>The ID of the attachment.</p>"}, "OwnerAccountId": {"shape": "AWSAccountId", "documentation": "<p>The ID of the attachment account owner.</p>"}, "AttachmentType": {"shape": "AttachmentType", "documentation": "<p>The type of attachment.</p>"}, "State": {"shape": "AttachmentState", "documentation": "<p>The state of the attachment.</p>"}, "EdgeLocation": {"shape": "ExternalRegionCode", "documentation": "<p>The Region where the edge is located. This is returned for all attachment types except a Direct Connect gateway attachment, which instead returns <code>EdgeLocations</code>.</p>"}, "EdgeLocations": {"shape": "ExternalRegionCodeList", "documentation": "<p>The edge locations that the Direct Connect gateway is associated with. This is returned only for Direct Connect gateway attachments. All other attachment types retrun <code>EdgeLocation</code>.</p>"}, "ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The attachment resource ARN.</p>"}, "AttachmentPolicyRuleNumber": {"shape": "Integer", "documentation": "<p>The policy rule number associated with the attachment.</p>"}, "SegmentName": {"shape": "ConstrainedString", "documentation": "<p>The name of the segment attachment.</p>"}, "NetworkFunctionGroupName": {"shape": "NetworkFunctionGroupName", "documentation": "<p>The name of the network function group.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The tags associated with the attachment.</p>"}, "ProposedSegmentChange": {"shape": "ProposedSegmentChange", "documentation": "<p>The attachment to move from one segment to another.</p>"}, "ProposedNetworkFunctionGroupChange": {"shape": "ProposedNetworkFunctionGroupChange", "documentation": "<p>Describes a proposed change to a network function group associated with the attachment.</p>"}, "CreatedAt": {"shape": "DateTime", "documentation": "<p>The timestamp when the attachment was created.</p>"}, "UpdatedAt": {"shape": "DateTime", "documentation": "<p>The timestamp when the attachment was last updated.</p>"}, "LastModificationErrors": {"shape": "AttachmentErrorList", "documentation": "<p>Describes the error associated with the attachment request.</p>"}}, "documentation": "<p>Describes a core network attachment.</p>"}, "AttachmentError": {"type": "structure", "members": {"Code": {"shape": "AttachmentErrorCode", "documentation": "<p>The error code for the attachment request. </p>"}, "Message": {"shape": "ServerSideString", "documentation": "<p>The message associated with the error <code>code</code>.</p>"}, "ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The ARN of the requested attachment resource.</p>"}, "RequestId": {"shape": "ServerSideString", "documentation": "<p>The ID of the attachment request.</p>"}}, "documentation": "<p>Describes the error associated with an attachment request.</p>"}, "AttachmentErrorCode": {"type": "string", "enum": ["VPC_NOT_FOUND", "SUBNET_NOT_FOUND", "SUBNET_DUPLICATED_IN_AVAILABILITY_ZONE", "SUBNET_NO_FREE_ADDRESSES", "SUBNET_UNSUPPORTED_AVAILABILITY_ZONE", "SUBNET_NO_IPV6_CIDRS", "VPN_CONNECTION_NOT_FOUND", "MAXIMUM_NO_ENCAP_LIMIT_EXCEEDED", "DIRECT_CONNECT_GATEWAY_NOT_FOUND", "DIRECT_CONNECT_GATEWAY_EXISTING_ATTACHMENTS", "DIRECT_CONNECT_GATEWAY_NO_PRIVATE_VIF"]}, "AttachmentErrorList": {"type": "list", "member": {"shape": "AttachmentError"}, "max": 20, "min": 0}, "AttachmentId": {"type": "string", "max": 50, "min": 0, "pattern": "^attachment-([0-9a-f]{8,17})$"}, "AttachmentList": {"type": "list", "member": {"shape": "Attachment"}}, "AttachmentState": {"type": "string", "enum": ["REJECTED", "PENDING_ATTACHMENT_ACCEPTANCE", "CREATING", "FAILED", "AVAILABLE", "UPDATING", "PENDING_NETWORK_UPDATE", "PENDING_TAG_ACCEPTANCE", "DELETING"]}, "AttachmentType": {"type": "string", "enum": ["CONNECT", "SITE_TO_SITE_VPN", "VPC", "DIRECT_CONNECT_GATEWAY", "TRANSIT_GATEWAY_ROUTE_TABLE"]}, "Bandwidth": {"type": "structure", "members": {"UploadSpeed": {"shape": "Integer", "documentation": "<p>Upload speed in Mbps.</p>"}, "DownloadSpeed": {"shape": "Integer", "documentation": "<p>Download speed in Mbps.</p>"}}, "documentation": "<p>Describes bandwidth information.</p>"}, "BgpOptions": {"type": "structure", "members": {"PeerAsn": {"shape": "<PERSON>", "documentation": "<p>The Peer ASN of the BGP.</p>"}}, "documentation": "<p>Describes the BGP options.</p>"}, "Boolean": {"type": "boolean"}, "ChangeAction": {"type": "string", "enum": ["ADD", "MODIFY", "REMOVE"]}, "ChangeSetState": {"type": "string", "enum": ["PENDING_GENERATION", "FAILED_GENERATION", "READY_TO_EXECUTE", "EXECUTING", "EXECUTION_SUCCEEDED", "OUT_OF_DATE"]}, "ChangeStatus": {"type": "string", "enum": ["NOT_STARTED", "IN_PROGRESS", "COMPLETE", "FAILED"]}, "ChangeType": {"type": "string", "enum": ["CORE_NETWORK_SEGMENT", "NETWORK_FUNCTION_GROUP", "CORE_NETWORK_EDGE", "ATTACHMENT_MAPPING", "ATTACHMENT_ROUTE_PROPAGATION", "ATTACHMENT_ROUTE_STATIC", "CORE_NETWORK_CONFIGURATION", "SEGMENTS_CONFIGURATION", "SEGMENT_ACTIONS_CONFIGURATION", "ATTACHMENT_POLICIES_CONFIGURATION"]}, "ClientToken": {"type": "string", "max": 256, "min": 0, "pattern": "[\\s\\S]*"}, "ConflictException": {"type": "structure", "required": ["Message", "ResourceId", "ResourceType"], "members": {"Message": {"shape": "ServerSideString"}, "ResourceId": {"shape": "ServerSideString", "documentation": "<p>The ID of the resource.</p>"}, "ResourceType": {"shape": "ServerSideString", "documentation": "<p>The resource type.</p>"}}, "documentation": "<p>There was a conflict processing the request. Updating or deleting the resource can cause an inconsistent state.</p>", "error": {"httpStatusCode": 409}, "exception": true}, "ConnectAttachment": {"type": "structure", "members": {"Attachment": {"shape": "Attachment", "documentation": "<p>The attachment details.</p>"}, "TransportAttachmentId": {"shape": "AttachmentId", "documentation": "<p>The ID of the transport attachment.</p>"}, "Options": {"shape": "ConnectAttachmentOptions", "documentation": "<p>Options for connecting an attachment.</p>"}}, "documentation": "<p>Describes a core network Connect attachment.</p>"}, "ConnectAttachmentOptions": {"type": "structure", "members": {"Protocol": {"shape": "TunnelProtocol", "documentation": "<p>The protocol used for the attachment connection.</p>"}}, "documentation": "<p>Describes a core network Connect attachment options.</p>"}, "ConnectPeer": {"type": "structure", "members": {"CoreNetworkId": {"shape": "CoreNetworkId", "documentation": "<p>The ID of a core network.</p>"}, "ConnectAttachmentId": {"shape": "AttachmentId", "documentation": "<p>The ID of the attachment to connect.</p>"}, "ConnectPeerId": {"shape": "ConnectPeerId", "documentation": "<p>The ID of the Connect peer.</p>"}, "EdgeLocation": {"shape": "ExternalRegionCode", "documentation": "<p>The Connect peer Regions where edges are located.</p>"}, "State": {"shape": "ConnectPeerState", "documentation": "<p>The state of the Connect peer.</p>"}, "CreatedAt": {"shape": "DateTime", "documentation": "<p>The timestamp when the Connect peer was created.</p>"}, "Configuration": {"shape": "ConnectPeerConfiguration", "documentation": "<p>The configuration of the Connect peer.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The list of key-value tags associated with the Connect peer.</p>"}, "SubnetArn": {"shape": "SubnetArn", "documentation": "<p>The subnet ARN for the Connect peer. This only applies only when the protocol is NO_ENCAP.</p>"}, "LastModificationErrors": {"shape": "ConnectPeerErrorList", "documentation": "<p>Describes the error associated with the attachment request.</p>"}}, "documentation": "<p>Describes a core network Connect peer.</p>"}, "ConnectPeerAssociation": {"type": "structure", "members": {"ConnectPeerId": {"shape": "ConnectPeerId", "documentation": "<p>The ID of the Connect peer.</p>"}, "GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>"}, "DeviceId": {"shape": "DeviceId", "documentation": "<p>The ID of the device to connect to.</p>"}, "LinkId": {"shape": "LinkId", "documentation": "<p>The ID of the link.</p>"}, "State": {"shape": "ConnectPeerAssociationState", "documentation": "<p>The state of the Connect peer association.</p>"}}, "documentation": "<p>Describes a core network Connect peer association.</p>"}, "ConnectPeerAssociationList": {"type": "list", "member": {"shape": "ConnectPeerAssociation"}}, "ConnectPeerAssociationState": {"type": "string", "enum": ["PENDING", "AVAILABLE", "DELETING", "DELETED"]}, "ConnectPeerBgpConfiguration": {"type": "structure", "members": {"CoreNetworkAsn": {"shape": "<PERSON>", "documentation": "<p>The ASN of the Coret Network.</p>"}, "PeerAsn": {"shape": "<PERSON>", "documentation": "<p>The ASN of the Connect peer.</p>"}, "CoreNetworkAddress": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>The address of a core network.</p>"}, "PeerAddress": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>The address of a core network Connect peer.</p>"}}, "documentation": "<p>Describes a core network BGP configuration.</p>"}, "ConnectPeerBgpConfigurationList": {"type": "list", "member": {"shape": "ConnectPeerBgpConfiguration"}}, "ConnectPeerConfiguration": {"type": "structure", "members": {"CoreNetworkAddress": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>The IP address of a core network.</p>"}, "PeerAddress": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>The IP address of the Connect peer.</p>"}, "InsideCidrBlocks": {"shape": "ConstrainedStringList", "documentation": "<p>The inside IP addresses used for a Connect peer configuration.</p>"}, "Protocol": {"shape": "TunnelProtocol", "documentation": "<p>The protocol used for a Connect peer configuration.</p>"}, "BgpConfigurations": {"shape": "ConnectPeerBgpConfigurationList", "documentation": "<p>The Connect peer BGP configurations.</p>"}}, "documentation": "<p>Describes a core network Connect peer configuration.</p>"}, "ConnectPeerError": {"type": "structure", "members": {"Code": {"shape": "ConnectPeerErrorCode", "documentation": "<p>The error code for the Connect peer request.</p>"}, "Message": {"shape": "ServerSideString", "documentation": "<p>The message associated with the error <code>code</code>.</p>"}, "ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The ARN of the requested Connect peer resource.</p>"}, "RequestId": {"shape": "ServerSideString", "documentation": "<p>The ID of the Connect peer request.</p>"}}, "documentation": "<p>Describes an error associated with a Connect peer request</p>"}, "ConnectPeerErrorCode": {"type": "string", "enum": ["EDGE_LOCATION_NO_FREE_IPS", "EDGE_LOCATION_PEER_DUPLICATE", "SUBNET_NOT_FOUND", "IP_OUTSIDE_SUBNET_CIDR_RANGE", "INVALID_INSIDE_CIDR_BLOCK", "NO_ASSOCIATED_CIDR_BLOCK"]}, "ConnectPeerErrorList": {"type": "list", "member": {"shape": "ConnectPeerError"}, "max": 20, "min": 0}, "ConnectPeerId": {"type": "string", "max": 50, "min": 0, "pattern": "^connect-peer-([0-9a-f]{8,17})$"}, "ConnectPeerIdList": {"type": "list", "member": {"shape": "ConnectPeerId"}}, "ConnectPeerState": {"type": "string", "enum": ["CREATING", "FAILED", "AVAILABLE", "DELETING"]}, "ConnectPeerSummary": {"type": "structure", "members": {"CoreNetworkId": {"shape": "CoreNetworkId", "documentation": "<p>The ID of a core network.</p>"}, "ConnectAttachmentId": {"shape": "AttachmentId", "documentation": "<p>The ID of a Connect peer attachment.</p>"}, "ConnectPeerId": {"shape": "ConnectPeerId", "documentation": "<p>The ID of a Connect peer.</p>"}, "EdgeLocation": {"shape": "ExternalRegionCode", "documentation": "<p>The Region where the edge is located.</p>"}, "ConnectPeerState": {"shape": "ConnectPeerState", "documentation": "<p>The state of a Connect peer.</p>"}, "CreatedAt": {"shape": "DateTime", "documentation": "<p>The timestamp when a Connect peer was created.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The list of key-value tags associated with the Connect peer summary.</p>"}, "SubnetArn": {"shape": "SubnetArn", "documentation": "<p>The subnet ARN for the Connect peer summary.</p>"}}, "documentation": "<p>Summary description of a Connect peer.</p>"}, "ConnectPeerSummaryList": {"type": "list", "member": {"shape": "ConnectPeerSummary"}}, "Connection": {"type": "structure", "members": {"ConnectionId": {"shape": "ConnectionId", "documentation": "<p>The ID of the connection.</p>"}, "ConnectionArn": {"shape": "ConnectionArn", "documentation": "<p>The Amazon Resource Name (ARN) of the connection.</p>"}, "GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>"}, "DeviceId": {"shape": "DeviceId", "documentation": "<p>The ID of the first device in the connection.</p>"}, "ConnectedDeviceId": {"shape": "DeviceId", "documentation": "<p>The ID of the second device in the connection.</p>"}, "LinkId": {"shape": "LinkId", "documentation": "<p>The ID of the link for the first device in the connection.</p>"}, "ConnectedLinkId": {"shape": "LinkId", "documentation": "<p>The ID of the link for the second device in the connection.</p>"}, "Description": {"shape": "ConstrainedString", "documentation": "<p>The description of the connection.</p>"}, "CreatedAt": {"shape": "DateTime", "documentation": "<p>The date and time that the connection was created.</p>"}, "State": {"shape": "ConnectionState", "documentation": "<p>The state of the connection.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The tags for the connection.</p>"}}, "documentation": "<p>Describes a connection.</p>"}, "ConnectionArn": {"type": "string", "max": 500, "min": 0, "pattern": "[\\s\\S]*"}, "ConnectionHealth": {"type": "structure", "members": {"Type": {"shape": "ConnectionType", "documentation": "<p>The connection type.</p>"}, "Status": {"shape": "ConnectionStatus", "documentation": "<p>The connection status.</p>"}, "Timestamp": {"shape": "DateTime", "documentation": "<p>The time the status was last updated.</p>"}}, "documentation": "<p>Describes connection health.</p>"}, "ConnectionId": {"type": "string", "max": 50, "min": 0, "pattern": "[\\s\\S]*"}, "ConnectionIdList": {"type": "list", "member": {"shape": "ConnectionId"}}, "ConnectionList": {"type": "list", "member": {"shape": "Connection"}}, "ConnectionState": {"type": "string", "enum": ["PENDING", "AVAILABLE", "DELETING", "UPDATING"]}, "ConnectionStatus": {"type": "string", "enum": ["UP", "DOWN"]}, "ConnectionType": {"type": "string", "enum": ["BGP", "IPSEC"]}, "ConstrainedString": {"type": "string", "max": 256, "min": 0, "pattern": "[\\s\\S]*"}, "ConstrainedStringList": {"type": "list", "member": {"shape": "ConstrainedString"}}, "CoreNetwork": {"type": "structure", "members": {"GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network that your core network is a part of. </p>"}, "CoreNetworkId": {"shape": "CoreNetworkId", "documentation": "<p>The ID of a core network.</p>"}, "CoreNetworkArn": {"shape": "CoreNetworkArn", "documentation": "<p>The ARN of a core network.</p>"}, "Description": {"shape": "ConstrainedString", "documentation": "<p>The description of a core network.</p>"}, "CreatedAt": {"shape": "DateTime", "documentation": "<p>The timestamp when a core network was created.</p>"}, "State": {"shape": "CoreNetworkState", "documentation": "<p>The current state of a core network.</p>"}, "Segments": {"shape": "CoreNetworkSegmentList", "documentation": "<p>The segments within a core network.</p>"}, "NetworkFunctionGroups": {"shape": "CoreNetworkNetworkFunctionGroupList", "documentation": "<p>The network function groups associated with a core network.</p>"}, "Edges": {"shape": "CoreNetworkEdgeList", "documentation": "<p>The edges within a core network.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The list of key-value tags associated with a core network.</p>"}}, "documentation": "<p>Describes a core network.</p>"}, "CoreNetworkArn": {"type": "string", "max": 500, "min": 0, "pattern": "[\\s\\S]*"}, "CoreNetworkChange": {"type": "structure", "members": {"Type": {"shape": "ChangeType", "documentation": "<p>The type of change.</p>"}, "Action": {"shape": "ChangeAction", "documentation": "<p>The action to take for a core network.</p>"}, "Identifier": {"shape": "ConstrainedString", "documentation": "<p>The resource identifier.</p>"}, "PreviousValues": {"shape": "CoreNetworkChangeValues", "documentation": "<p>The previous values for a core network.</p>"}, "NewValues": {"shape": "CoreNetworkChangeValues", "documentation": "<p>The new value for a core network</p>"}, "IdentifierPath": {"shape": "ConstrainedString", "documentation": "<p>Uniquely identifies the path for a change within the changeset. For example, the <code>IdentifierPath</code> for a core network segment change might be <code>\"CORE_NETWORK_SEGMENT/us-east-1/devsegment\"</code>.</p>"}}, "documentation": "<p>Details describing a core network change.</p>"}, "CoreNetworkChangeEvent": {"type": "structure", "members": {"Type": {"shape": "ChangeType", "documentation": "<p>Describes the type of change event. </p>"}, "Action": {"shape": "ChangeAction", "documentation": "<p>The action taken for the change event.</p>"}, "IdentifierPath": {"shape": "ConstrainedString", "documentation": "<p>Uniquely identifies the path for a change within the changeset. For example, the <code>IdentifierPath</code> for a core network segment change might be <code>\"CORE_NETWORK_SEGMENT/us-east-1/devsegment\"</code>.</p>"}, "EventTime": {"shape": "DateTime", "documentation": "<p>The timestamp for an event change in status.</p>"}, "Status": {"shape": "ChangeStatus", "documentation": "<p>The status of the core network change event.</p>"}, "Values": {"shape": "CoreNetworkChangeEventValues", "documentation": "<p>Details of the change event.</p>"}}, "documentation": "<p>Describes a core network change event. This can be a change to a segment, attachment, route, etc.</p>"}, "CoreNetworkChangeEventList": {"type": "list", "member": {"shape": "CoreNetworkChangeEvent"}}, "CoreNetworkChangeEventValues": {"type": "structure", "members": {"EdgeLocation": {"shape": "ExternalRegionCode", "documentation": "<p>The edge location for the core network change event.</p>"}, "SegmentName": {"shape": "ConstrainedString", "documentation": "<p>The segment name if the change event is associated with a segment.</p>"}, "NetworkFunctionGroupName": {"shape": "ConstrainedString", "documentation": "<p>The changed network function group name.</p>"}, "AttachmentId": {"shape": "AttachmentId", "documentation": "<p>The ID of the attachment if the change event is associated with an attachment. </p>"}, "Cidr": {"shape": "ConstrainedString", "documentation": "<p>For a <code>STATIC_ROUTE</code> event, this is the IP address.</p>"}}, "documentation": "<p>Describes a core network change event.</p>"}, "CoreNetworkChangeList": {"type": "list", "member": {"shape": "CoreNetworkChange"}}, "CoreNetworkChangeValues": {"type": "structure", "members": {"SegmentName": {"shape": "ConstrainedString", "documentation": "<p>The names of the segments in a core network.</p>"}, "NetworkFunctionGroupName": {"shape": "ConstrainedString", "documentation": "<p>The network function group name if the change event is associated with a network function group.</p>"}, "EdgeLocations": {"shape": "ExternalRegionCodeList", "documentation": "<p>The Regions where edges are located in a core network. </p>"}, "Asn": {"shape": "<PERSON>", "documentation": "<p>The ASN of a core network.</p>"}, "Cidr": {"shape": "ConstrainedString", "documentation": "<p>The IP addresses used for a core network.</p>"}, "DestinationIdentifier": {"shape": "ConstrainedString", "documentation": "<p>The ID of the destination.</p>"}, "InsideCidrBlocks": {"shape": "ConstrainedStringList", "documentation": "<p>The inside IP addresses used for core network change values.</p>"}, "SharedSegments": {"shape": "ConstrainedStringList", "documentation": "<p>The shared segments for a core network change value. </p>"}, "ServiceInsertionActions": {"shape": "ServiceInsertionActionList", "documentation": "<p>Describes the service insertion action. </p>"}, "VpnEcmpSupport": {"shape": "Boolean", "documentation": "<p>Indicates whether Equal Cost Multipath (ECMP) is enabled for the core network.</p>"}, "DnsSupport": {"shape": "Boolean", "documentation": "<p>Indicates whether public DNS support is supported. The default is <code>true</code>. </p>"}, "SecurityGroupReferencingSupport": {"shape": "Boolean", "documentation": "<p>Indicates whether security group referencing is enabled for the core network.</p>"}}, "documentation": "<p>Describes a core network change.</p>"}, "CoreNetworkEdge": {"type": "structure", "members": {"EdgeLocation": {"shape": "ExternalRegionCode", "documentation": "<p>The Region where a core network edge is located.</p>"}, "Asn": {"shape": "<PERSON>", "documentation": "<p>The ASN of a core network edge.</p>"}, "InsideCidrBlocks": {"shape": "ConstrainedStringList", "documentation": "<p>The inside IP addresses used for core network edges.</p>"}}, "documentation": "<p>Describes a core network edge.</p>"}, "CoreNetworkEdgeList": {"type": "list", "member": {"shape": "CoreNetworkEdge"}}, "CoreNetworkId": {"type": "string", "max": 50, "min": 0, "pattern": "^core-network-([0-9a-f]{8,17})$"}, "CoreNetworkNetworkFunctionGroup": {"type": "structure", "members": {"Name": {"shape": "ConstrainedString", "documentation": "<p>The name of the network function group.</p>"}, "EdgeLocations": {"shape": "ExternalRegionCodeList", "documentation": "<p>The core network edge locations.</p>"}, "Segments": {"shape": "ServiceInsertionSegments", "documentation": "<p>The segments associated with the network function group.</p>"}}, "documentation": "<p>Describes a network function group.</p>"}, "CoreNetworkNetworkFunctionGroupIdentifier": {"type": "structure", "members": {"CoreNetworkId": {"shape": "CoreNetworkId", "documentation": "<p>The ID of the core network.</p>"}, "NetworkFunctionGroupName": {"shape": "ConstrainedString", "documentation": "<p>The network function group name.</p>"}, "EdgeLocation": {"shape": "ExternalRegionCode", "documentation": "<p>The location for the core network edge.</p>"}}, "documentation": "<p>Describes a core network </p>"}, "CoreNetworkNetworkFunctionGroupList": {"type": "list", "member": {"shape": "CoreNetworkNetworkFunctionGroup"}}, "CoreNetworkPolicy": {"type": "structure", "members": {"CoreNetworkId": {"shape": "CoreNetworkId", "documentation": "<p>The ID of a core network.</p>"}, "PolicyVersionId": {"shape": "Integer", "documentation": "<p>The ID of the policy version.</p>"}, "Alias": {"shape": "CoreNetworkPolicyAlias", "documentation": "<p>Whether a core network policy is the current LIVE policy or the most recently submitted policy.</p>"}, "Description": {"shape": "ConstrainedString", "documentation": "<p>The description of a core network policy.</p>"}, "CreatedAt": {"shape": "DateTime", "documentation": "<p>The timestamp when a core network policy was created.</p>"}, "ChangeSetState": {"shape": "ChangeSetState", "documentation": "<p>The state of a core network policy.</p>"}, "PolicyErrors": {"shape": "CoreNetworkPolicyErrorList", "documentation": "<p>Describes any errors in a core network policy.</p>"}, "PolicyDocument": {"shape": "CoreNetworkPolicyDocument", "documentation": "<p>Describes a core network policy.</p>", "jsonvalue": true}}, "documentation": "<p>Describes a core network policy. You can have only one LIVE Core Policy.</p>"}, "CoreNetworkPolicyAlias": {"type": "string", "enum": ["LIVE", "LATEST"]}, "CoreNetworkPolicyDocument": {"type": "string", "max": 10000000, "min": 0, "pattern": "[\\s\\S]*"}, "CoreNetworkPolicyError": {"type": "structure", "required": ["ErrorCode", "Message"], "members": {"ErrorCode": {"shape": "ServerSideString", "documentation": "<p>The error code associated with a core network policy error.</p>"}, "Message": {"shape": "ServerSideString", "documentation": "<p>The message associated with a core network policy error code.</p>"}, "Path": {"shape": "ServerSideString", "documentation": "<p>The JSON path where the error was discovered in the policy document.</p>"}}, "documentation": "<p>Provides details about an error in a core network policy.</p>"}, "CoreNetworkPolicyErrorList": {"type": "list", "member": {"shape": "CoreNetworkPolicyError"}}, "CoreNetworkPolicyException": {"type": "structure", "required": ["Message"], "members": {"Message": {"shape": "ServerSideString"}, "Errors": {"shape": "CoreNetworkPolicyErrorList", "documentation": "<p>Describes a core network policy exception.</p>"}}, "documentation": "<p>Describes a core network policy exception.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "CoreNetworkPolicyVersion": {"type": "structure", "members": {"CoreNetworkId": {"shape": "CoreNetworkId", "documentation": "<p>The ID of a core network.</p>"}, "PolicyVersionId": {"shape": "Integer", "documentation": "<p>The ID of the policy version.</p>"}, "Alias": {"shape": "CoreNetworkPolicyAlias", "documentation": "<p>Whether a core network policy is the current policy or the most recently submitted policy.</p>"}, "Description": {"shape": "ConstrainedString", "documentation": "<p>The description of a core network policy version.</p>"}, "CreatedAt": {"shape": "DateTime", "documentation": "<p>The timestamp when a core network policy version was created.</p>"}, "ChangeSetState": {"shape": "ChangeSetState", "documentation": "<p>The status of the policy version change set.</p>"}}, "documentation": "<p>Describes a core network policy version.</p>"}, "CoreNetworkPolicyVersionList": {"type": "list", "member": {"shape": "CoreNetworkPolicyVersion"}}, "CoreNetworkSegment": {"type": "structure", "members": {"Name": {"shape": "ConstrainedString", "documentation": "<p>The name of a core network segment.</p>"}, "EdgeLocations": {"shape": "ExternalRegionCodeList", "documentation": "<p>The Regions where the edges are located.</p>"}, "SharedSegments": {"shape": "ConstrainedStringList", "documentation": "<p>The shared segments of a core network.</p>"}}, "documentation": "<p>Describes a core network segment, which are dedicated routes. Only attachments within this segment can communicate with each other.</p>"}, "CoreNetworkSegmentEdgeIdentifier": {"type": "structure", "members": {"CoreNetworkId": {"shape": "CoreNetworkId", "documentation": "<p>The ID of a core network.</p>"}, "SegmentName": {"shape": "ConstrainedString", "documentation": "<p>The name of the segment edge.</p>"}, "EdgeLocation": {"shape": "ExternalRegionCode", "documentation": "<p>The Region where the segment edge is located.</p>"}}, "documentation": "<p>Returns details about a core network edge.</p>"}, "CoreNetworkSegmentList": {"type": "list", "member": {"shape": "CoreNetworkSegment"}}, "CoreNetworkState": {"type": "string", "enum": ["CREATING", "UPDATING", "AVAILABLE", "DELETING"]}, "CoreNetworkSummary": {"type": "structure", "members": {"CoreNetworkId": {"shape": "CoreNetworkId", "documentation": "<p>The ID of a core network.</p>"}, "CoreNetworkArn": {"shape": "CoreNetworkArn", "documentation": "<p>a core network ARN.</p>"}, "GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The global network ID.</p>"}, "OwnerAccountId": {"shape": "AWSAccountId", "documentation": "<p>The ID of the account owner.</p>"}, "State": {"shape": "CoreNetworkState", "documentation": "<p>The state of a core network.</p>"}, "Description": {"shape": "ConstrainedString", "documentation": "<p>The description of a core network.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The key-value tags associated with a core network summary.</p>"}}, "documentation": "<p>Returns summary information about a core network.</p>"}, "CoreNetworkSummaryList": {"type": "list", "member": {"shape": "CoreNetworkSummary"}}, "CreateConnectAttachmentRequest": {"type": "structure", "required": ["CoreNetworkId", "EdgeLocation", "TransportAttachmentId", "Options"], "members": {"CoreNetworkId": {"shape": "CoreNetworkId", "documentation": "<p>The ID of a core network where you want to create the attachment. </p>"}, "EdgeLocation": {"shape": "ExternalRegionCode", "documentation": "<p>The Region where the edge is located.</p>"}, "TransportAttachmentId": {"shape": "AttachmentId", "documentation": "<p>The ID of the attachment between the two connections.</p>"}, "Options": {"shape": "ConnectAttachmentOptions", "documentation": "<p>Options for creating an attachment.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The list of key-value tags associated with the request.</p>"}, "ClientToken": {"shape": "ClientToken", "documentation": "<p>The client token associated with the request.</p>", "idempotencyToken": true}}}, "CreateConnectAttachmentResponse": {"type": "structure", "members": {"ConnectAttachment": {"shape": "ConnectAttachment", "documentation": "<p>The response to a Connect attachment request.</p>"}}}, "CreateConnectPeerRequest": {"type": "structure", "required": ["ConnectAttachmentId", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "members": {"ConnectAttachmentId": {"shape": "AttachmentId", "documentation": "<p>The ID of the connection attachment.</p>"}, "CoreNetworkAddress": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>A Connect peer core network address. This only applies only when the protocol is <code>GRE</code>.</p>"}, "PeerAddress": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>The Connect peer address.</p>"}, "BgpOptions": {"shape": "BgpOptions", "documentation": "<p>The Connect peer BGP options. This only applies only when the protocol is <code>GRE</code>.</p>"}, "InsideCidrBlocks": {"shape": "ConstrainedStringList", "documentation": "<p>The inside IP addresses used for BGP peering.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The tags associated with the peer request.</p>"}, "ClientToken": {"shape": "ClientToken", "documentation": "<p>The client token associated with the request.</p>", "idempotencyToken": true}, "SubnetArn": {"shape": "SubnetArn", "documentation": "<p>The subnet ARN for the Connect peer. This only applies only when the protocol is NO_ENCAP.</p>"}}}, "CreateConnectPeerResponse": {"type": "structure", "members": {"ConnectPeer": {"shape": "ConnectPeer", "documentation": "<p>The response to the request.</p>"}}}, "CreateConnectionRequest": {"type": "structure", "required": ["GlobalNetworkId", "DeviceId", "ConnectedDeviceId"], "members": {"GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>", "location": "uri", "locationName": "globalNetworkId"}, "DeviceId": {"shape": "DeviceId", "documentation": "<p>The ID of the first device in the connection.</p>"}, "ConnectedDeviceId": {"shape": "DeviceId", "documentation": "<p>The ID of the second device in the connection.</p>"}, "LinkId": {"shape": "LinkId", "documentation": "<p>The ID of the link for the first device.</p>"}, "ConnectedLinkId": {"shape": "LinkId", "documentation": "<p>The ID of the link for the second device.</p>"}, "Description": {"shape": "ConstrainedString", "documentation": "<p>A description of the connection.</p> <p>Length Constraints: Maximum length of 256 characters.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The tags to apply to the resource during creation.</p>"}}}, "CreateConnectionResponse": {"type": "structure", "members": {"Connection": {"shape": "Connection", "documentation": "<p>Information about the connection.</p>"}}}, "CreateCoreNetworkRequest": {"type": "structure", "required": ["GlobalNetworkId"], "members": {"GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network that a core network will be a part of. </p>"}, "Description": {"shape": "ConstrainedString", "documentation": "<p>The description of a core network.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>Key-value tags associated with a core network request.</p>"}, "PolicyDocument": {"shape": "CoreNetworkPolicyDocument", "documentation": "<p>The policy document for creating a core network.</p>"}, "ClientToken": {"shape": "ClientToken", "documentation": "<p>The client token associated with a core network request.</p>", "idempotencyToken": true}}}, "CreateCoreNetworkResponse": {"type": "structure", "members": {"CoreNetwork": {"shape": "CoreNetwork", "documentation": "<p>Returns details about a core network.</p>"}}}, "CreateDeviceRequest": {"type": "structure", "required": ["GlobalNetworkId"], "members": {"GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>", "location": "uri", "locationName": "globalNetworkId"}, "AWSLocation": {"shape": "AWSLocation", "documentation": "<p>The Amazon Web Services location of the device, if applicable. For an on-premises device, you can omit this parameter.</p>"}, "Description": {"shape": "ConstrainedString", "documentation": "<p>A description of the device.</p> <p>Constraints: Maximum length of 256 characters.</p>"}, "Type": {"shape": "ConstrainedString", "documentation": "<p>The type of the device.</p>"}, "Vendor": {"shape": "ConstrainedString", "documentation": "<p>The vendor of the device.</p> <p>Constraints: Maximum length of 128 characters.</p>"}, "Model": {"shape": "ConstrainedString", "documentation": "<p>The model of the device.</p> <p>Constraints: Maximum length of 128 characters.</p>"}, "SerialNumber": {"shape": "ConstrainedString", "documentation": "<p>The serial number of the device.</p> <p>Constraints: Maximum length of 128 characters.</p>"}, "Location": {"shape": "Location", "documentation": "<p>The location of the device.</p>"}, "SiteId": {"shape": "SiteId", "documentation": "<p>The ID of the site.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The tags to apply to the resource during creation.</p>"}}}, "CreateDeviceResponse": {"type": "structure", "members": {"Device": {"shape": "<PERSON><PERSON>", "documentation": "<p>Information about the device.</p>"}}}, "CreateDirectConnectGatewayAttachmentRequest": {"type": "structure", "required": ["CoreNetworkId", "DirectConnectGatewayArn", "EdgeLocations"], "members": {"CoreNetworkId": {"shape": "CoreNetworkId", "documentation": "<p>The ID of the Cloud WAN core network that the Direct Connect gateway attachment should be attached to.</p>"}, "DirectConnectGatewayArn": {"shape": "DirectConnectGatewayArn", "documentation": "<p>The ARN of the Direct Connect gateway attachment.</p>"}, "EdgeLocations": {"shape": "ExternalRegionCodeList", "documentation": "<p>One or more core network edge locations that the Direct Connect gateway attachment is associated with. </p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The key value tags to apply to the Direct Connect gateway attachment during creation.</p>"}, "ClientToken": {"shape": "ClientToken", "documentation": "<p>client token</p>", "idempotencyToken": true}}}, "CreateDirectConnectGatewayAttachmentResponse": {"type": "structure", "members": {"DirectConnectGatewayAttachment": {"shape": "DirectConnectGatewayAttachment", "documentation": "<p>Describes the details of a <code>CreateDirectConnectGatewayAttachment</code> request.</p>"}}}, "CreateGlobalNetworkRequest": {"type": "structure", "members": {"Description": {"shape": "ConstrainedString", "documentation": "<p>A description of the global network.</p> <p>Constraints: Maximum length of 256 characters.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The tags to apply to the resource during creation.</p>"}}}, "CreateGlobalNetworkResponse": {"type": "structure", "members": {"GlobalNetwork": {"shape": "GlobalNetwork", "documentation": "<p>Information about the global network object.</p>"}}}, "CreateLinkRequest": {"type": "structure", "required": ["GlobalNetworkId", "Bandwidth", "SiteId"], "members": {"GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>", "location": "uri", "locationName": "globalNetworkId"}, "Description": {"shape": "ConstrainedString", "documentation": "<p>A description of the link.</p> <p>Constraints: Maximum length of 256 characters.</p>"}, "Type": {"shape": "ConstrainedString", "documentation": "<p>The type of the link.</p> <p>Constraints: Maximum length of 128 characters. Cannot include the following characters: | \\ ^</p>"}, "Bandwidth": {"shape": "Bandwidth", "documentation": "<p> The upload speed and download speed in Mbps. </p>"}, "Provider": {"shape": "ConstrainedString", "documentation": "<p>The provider of the link.</p> <p>Constraints: Maximum length of 128 characters. Cannot include the following characters: | \\ ^</p>"}, "SiteId": {"shape": "SiteId", "documentation": "<p>The ID of the site.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The tags to apply to the resource during creation.</p>"}}}, "CreateLinkResponse": {"type": "structure", "members": {"Link": {"shape": "Link", "documentation": "<p>Information about the link.</p>"}}}, "CreateSiteRequest": {"type": "structure", "required": ["GlobalNetworkId"], "members": {"GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>", "location": "uri", "locationName": "globalNetworkId"}, "Description": {"shape": "ConstrainedString", "documentation": "<p>A description of your site.</p> <p>Constraints: Maximum length of 256 characters.</p>"}, "Location": {"shape": "Location", "documentation": "<p>The site location. This information is used for visualization in the Network Manager console. If you specify the address, the latitude and longitude are automatically calculated.</p> <ul> <li> <p> <code>Address</code>: The physical address of the site.</p> </li> <li> <p> <code>Latitude</code>: The latitude of the site. </p> </li> <li> <p> <code>Longitude</code>: The longitude of the site.</p> </li> </ul>"}, "Tags": {"shape": "TagList", "documentation": "<p>The tags to apply to the resource during creation.</p>"}}}, "CreateSiteResponse": {"type": "structure", "members": {"Site": {"shape": "Site", "documentation": "<p>Information about the site.</p>"}}}, "CreateSiteToSiteVpnAttachmentRequest": {"type": "structure", "required": ["CoreNetworkId", "VpnConnectionArn"], "members": {"CoreNetworkId": {"shape": "CoreNetworkId", "documentation": "<p>The ID of a core network where you're creating a site-to-site VPN attachment.</p>"}, "VpnConnectionArn": {"shape": "VpnConnectionArn", "documentation": "<p>The ARN identifying the VPN attachment.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The tags associated with the request.</p>"}, "ClientToken": {"shape": "ClientToken", "documentation": "<p>The client token associated with the request.</p>", "idempotencyToken": true}}}, "CreateSiteToSiteVpnAttachmentResponse": {"type": "structure", "members": {"SiteToSiteVpnAttachment": {"shape": "SiteToSiteVpnAttachment", "documentation": "<p>Details about a site-to-site VPN attachment.</p>"}}}, "CreateTransitGatewayPeeringRequest": {"type": "structure", "required": ["CoreNetworkId", "TransitGatewayArn"], "members": {"CoreNetworkId": {"shape": "CoreNetworkId", "documentation": "<p>The ID of a core network.</p>"}, "TransitGatewayArn": {"shape": "TransitGatewayArn", "documentation": "<p>The ARN of the transit gateway for the peering request.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The list of key-value tags associated with the request.</p>"}, "ClientToken": {"shape": "ClientToken", "documentation": "<p>The client token associated with the request.</p>", "idempotencyToken": true}}}, "CreateTransitGatewayPeeringResponse": {"type": "structure", "members": {"TransitGatewayPeering": {"shape": "TransitGatewayPeering", "documentation": "<p>Returns information about the transit gateway peering connection request.</p>"}}}, "CreateTransitGatewayRouteTableAttachmentRequest": {"type": "structure", "required": ["PeeringId", "TransitGatewayRouteTableArn"], "members": {"PeeringId": {"shape": "PeeringId", "documentation": "<p>The ID of the peer for the </p>"}, "TransitGatewayRouteTableArn": {"shape": "TransitGatewayRouteTableArn", "documentation": "<p>The ARN of the transit gateway route table for the attachment request. For example, <code>\"TransitGatewayRouteTableArn\": \"arn:aws:ec2:us-west-2:123456789012:transit-gateway-route-table/tgw-rtb-9876543210123456\"</code>.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The list of key-value tags associated with the request.</p>"}, "ClientToken": {"shape": "ClientToken", "documentation": "<p>The client token associated with the request.</p>", "idempotencyToken": true}}}, "CreateTransitGatewayRouteTableAttachmentResponse": {"type": "structure", "members": {"TransitGatewayRouteTableAttachment": {"shape": "TransitGatewayRouteTableAttachment", "documentation": "<p>The route table associated with the create transit gateway route table attachment request.</p>"}}}, "CreateVpcAttachmentRequest": {"type": "structure", "required": ["CoreNetworkId", "VpcArn", "SubnetArns"], "members": {"CoreNetworkId": {"shape": "CoreNetworkId", "documentation": "<p>The ID of a core network for the VPC attachment.</p>"}, "VpcArn": {"shape": "VpcArn", "documentation": "<p>The ARN of the VPC.</p>"}, "SubnetArns": {"shape": "SubnetArnList", "documentation": "<p>The subnet ARN of the VPC attachment.</p>"}, "Options": {"shape": "VpcOptions", "documentation": "<p>Options for the VPC attachment.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The key-value tags associated with the request.</p>"}, "ClientToken": {"shape": "ClientToken", "documentation": "<p>The client token associated with the request.</p>", "idempotencyToken": true}}}, "CreateVpcAttachmentResponse": {"type": "structure", "members": {"VpcAttachment": {"shape": "VpcAttachment", "documentation": "<p>Provides details about the VPC attachment.</p>"}}}, "CustomerGatewayArn": {"type": "string", "max": 500, "min": 0, "pattern": "[\\s\\S]*"}, "CustomerGatewayArnList": {"type": "list", "member": {"shape": "CustomerGatewayArn"}}, "CustomerGatewayAssociation": {"type": "structure", "members": {"CustomerGatewayArn": {"shape": "CustomerGatewayArn", "documentation": "<p>The Amazon Resource Name (ARN) of the customer gateway.</p>"}, "GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>"}, "DeviceId": {"shape": "DeviceId", "documentation": "<p>The ID of the device.</p>"}, "LinkId": {"shape": "LinkId", "documentation": "<p>The ID of the link.</p>"}, "State": {"shape": "CustomerGatewayAssociationState", "documentation": "<p>The association state.</p>"}}, "documentation": "<p>Describes the association between a customer gateway, a device, and a link.</p>"}, "CustomerGatewayAssociationList": {"type": "list", "member": {"shape": "CustomerGatewayAssociation"}}, "CustomerGatewayAssociationState": {"type": "string", "enum": ["PENDING", "AVAILABLE", "DELETING", "DELETED"]}, "DateTime": {"type": "timestamp"}, "DeleteAttachmentRequest": {"type": "structure", "required": ["AttachmentId"], "members": {"AttachmentId": {"shape": "AttachmentId", "documentation": "<p>The ID of the attachment to delete.</p>", "location": "uri", "locationName": "attachmentId"}}}, "DeleteAttachmentResponse": {"type": "structure", "members": {"Attachment": {"shape": "Attachment", "documentation": "<p>Information about the deleted attachment.</p>"}}}, "DeleteConnectPeerRequest": {"type": "structure", "required": ["ConnectPeerId"], "members": {"ConnectPeerId": {"shape": "ConnectPeerId", "documentation": "<p>The ID of the deleted Connect peer.</p>", "location": "uri", "locationName": "connectPeerId"}}}, "DeleteConnectPeerResponse": {"type": "structure", "members": {"ConnectPeer": {"shape": "ConnectPeer", "documentation": "<p>Information about the deleted Connect peer.</p>"}}}, "DeleteConnectionRequest": {"type": "structure", "required": ["GlobalNetworkId", "ConnectionId"], "members": {"GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>", "location": "uri", "locationName": "globalNetworkId"}, "ConnectionId": {"shape": "ConnectionId", "documentation": "<p>The ID of the connection.</p>", "location": "uri", "locationName": "connectionId"}}}, "DeleteConnectionResponse": {"type": "structure", "members": {"Connection": {"shape": "Connection", "documentation": "<p>Information about the connection.</p>"}}}, "DeleteCoreNetworkPolicyVersionRequest": {"type": "structure", "required": ["CoreNetworkId", "PolicyVersionId"], "members": {"CoreNetworkId": {"shape": "CoreNetworkId", "documentation": "<p>The ID of a core network for the deleted policy.</p>", "location": "uri", "locationName": "coreNetworkId"}, "PolicyVersionId": {"shape": "Integer", "documentation": "<p>The version ID of the deleted policy.</p>", "location": "uri", "locationName": "policyVersionId"}}}, "DeleteCoreNetworkPolicyVersionResponse": {"type": "structure", "members": {"CoreNetworkPolicy": {"shape": "CoreNetworkPolicy", "documentation": "<p>Returns information about the deleted policy version. </p>"}}}, "DeleteCoreNetworkRequest": {"type": "structure", "required": ["CoreNetworkId"], "members": {"CoreNetworkId": {"shape": "CoreNetworkId", "documentation": "<p>The network ID of the deleted core network.</p>", "location": "uri", "locationName": "coreNetworkId"}}}, "DeleteCoreNetworkResponse": {"type": "structure", "members": {"CoreNetwork": {"shape": "CoreNetwork", "documentation": "<p>Information about the deleted core network.</p>"}}}, "DeleteDeviceRequest": {"type": "structure", "required": ["GlobalNetworkId", "DeviceId"], "members": {"GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>", "location": "uri", "locationName": "globalNetworkId"}, "DeviceId": {"shape": "DeviceId", "documentation": "<p>The ID of the device.</p>", "location": "uri", "locationName": "deviceId"}}}, "DeleteDeviceResponse": {"type": "structure", "members": {"Device": {"shape": "<PERSON><PERSON>", "documentation": "<p>Information about the device.</p>"}}}, "DeleteGlobalNetworkRequest": {"type": "structure", "required": ["GlobalNetworkId"], "members": {"GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>", "location": "uri", "locationName": "globalNetworkId"}}}, "DeleteGlobalNetworkResponse": {"type": "structure", "members": {"GlobalNetwork": {"shape": "GlobalNetwork", "documentation": "<p>Information about the global network.</p>"}}}, "DeleteLinkRequest": {"type": "structure", "required": ["GlobalNetworkId", "LinkId"], "members": {"GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>", "location": "uri", "locationName": "globalNetworkId"}, "LinkId": {"shape": "LinkId", "documentation": "<p>The ID of the link.</p>", "location": "uri", "locationName": "linkId"}}}, "DeleteLinkResponse": {"type": "structure", "members": {"Link": {"shape": "Link", "documentation": "<p>Information about the link.</p>"}}}, "DeletePeeringRequest": {"type": "structure", "required": ["PeeringId"], "members": {"PeeringId": {"shape": "PeeringId", "documentation": "<p>The ID of the peering connection to delete.</p>", "location": "uri", "locationName": "peeringId"}}}, "DeletePeeringResponse": {"type": "structure", "members": {"Peering": {"shape": "Peering", "documentation": "<p>Information about a deleted peering connection.</p>"}}}, "DeleteResourcePolicyRequest": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The ARN of the policy to delete.</p>", "location": "uri", "locationName": "resourceArn"}}}, "DeleteResourcePolicyResponse": {"type": "structure", "members": {}}, "DeleteSiteRequest": {"type": "structure", "required": ["GlobalNetworkId", "SiteId"], "members": {"GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>", "location": "uri", "locationName": "globalNetworkId"}, "SiteId": {"shape": "SiteId", "documentation": "<p>The ID of the site.</p>", "location": "uri", "locationName": "siteId"}}}, "DeleteSiteResponse": {"type": "structure", "members": {"Site": {"shape": "Site", "documentation": "<p>Information about the site.</p>"}}}, "DeregisterTransitGatewayRequest": {"type": "structure", "required": ["GlobalNetworkId", "TransitGatewayArn"], "members": {"GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>", "location": "uri", "locationName": "globalNetworkId"}, "TransitGatewayArn": {"shape": "TransitGatewayArn", "documentation": "<p>The Amazon Resource Name (ARN) of the transit gateway.</p>", "location": "uri", "locationName": "transitGatewayArn"}}}, "DeregisterTransitGatewayResponse": {"type": "structure", "members": {"TransitGatewayRegistration": {"shape": "TransitGatewayRegistration", "documentation": "<p>The transit gateway registration information.</p>"}}}, "DescribeGlobalNetworksRequest": {"type": "structure", "members": {"GlobalNetworkIds": {"shape": "GlobalNetworkIdList", "documentation": "<p>The IDs of one or more global networks. The maximum is 10.</p>", "location": "querystring", "locationName": "globalNetworkIds"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}}}, "DescribeGlobalNetworksResponse": {"type": "structure", "members": {"GlobalNetworks": {"shape": "GlobalNetworkList", "documentation": "<p>Information about the global networks.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results.</p>"}}}, "Device": {"type": "structure", "members": {"DeviceId": {"shape": "DeviceId", "documentation": "<p>The ID of the device.</p>"}, "DeviceArn": {"shape": "DeviceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the device.</p>"}, "GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>"}, "AWSLocation": {"shape": "AWSLocation", "documentation": "<p>The Amazon Web Services location of the device.</p>"}, "Description": {"shape": "ConstrainedString", "documentation": "<p>The description of the device.</p>"}, "Type": {"shape": "ConstrainedString", "documentation": "<p>The device type.</p>"}, "Vendor": {"shape": "ConstrainedString", "documentation": "<p>The device vendor.</p>"}, "Model": {"shape": "ConstrainedString", "documentation": "<p>The device model.</p>"}, "SerialNumber": {"shape": "ConstrainedString", "documentation": "<p>The device serial number.</p>"}, "Location": {"shape": "Location", "documentation": "<p>The site location.</p>"}, "SiteId": {"shape": "SiteId", "documentation": "<p>The site ID.</p>"}, "CreatedAt": {"shape": "DateTime", "documentation": "<p>The date and time that the site was created.</p>"}, "State": {"shape": "DeviceState", "documentation": "<p>The device state.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The tags for the device.</p>"}}, "documentation": "<p>Describes a device.</p>"}, "DeviceArn": {"type": "string", "max": 500, "min": 0, "pattern": "[\\s\\S]*"}, "DeviceId": {"type": "string", "max": 50, "min": 0, "pattern": "[\\s\\S]*"}, "DeviceIdList": {"type": "list", "member": {"shape": "DeviceId"}}, "DeviceList": {"type": "list", "member": {"shape": "<PERSON><PERSON>"}}, "DeviceState": {"type": "string", "enum": ["PENDING", "AVAILABLE", "DELETING", "UPDATING"]}, "DirectConnectGatewayArn": {"type": "string", "max": 500, "min": 0, "pattern": "^arn:[^:]{1,63}:directconnect::[^:]{0,63}:dx-gateway\\/[0-9a-f]{8}-([0-9a-f]{4}-){3}[0-9a-f]{12}$"}, "DirectConnectGatewayAttachment": {"type": "structure", "members": {"Attachment": {"shape": "Attachment"}, "DirectConnectGatewayArn": {"shape": "DirectConnectGatewayArn", "documentation": "<p>The Direct Connect gateway attachment ARN.</p>"}}, "documentation": "<p>Describes a Direct Connect gateway attachment.</p>"}, "DisassociateConnectPeerRequest": {"type": "structure", "required": ["GlobalNetworkId", "ConnectPeerId"], "members": {"GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>", "location": "uri", "locationName": "globalNetworkId"}, "ConnectPeerId": {"shape": "ConnectPeerId", "documentation": "<p>The ID of the Connect peer to disassociate from a device.</p>", "location": "uri", "locationName": "connectPeerId"}}}, "DisassociateConnectPeerResponse": {"type": "structure", "members": {"ConnectPeerAssociation": {"shape": "ConnectPeerAssociation", "documentation": "<p>Describes the Connect peer association.</p>"}}}, "DisassociateCustomerGatewayRequest": {"type": "structure", "required": ["GlobalNetworkId", "CustomerGatewayArn"], "members": {"GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>", "location": "uri", "locationName": "globalNetworkId"}, "CustomerGatewayArn": {"shape": "CustomerGatewayArn", "documentation": "<p>The Amazon Resource Name (ARN) of the customer gateway.</p>", "location": "uri", "locationName": "customerGatewayArn"}}}, "DisassociateCustomerGatewayResponse": {"type": "structure", "members": {"CustomerGatewayAssociation": {"shape": "CustomerGatewayAssociation", "documentation": "<p>Information about the customer gateway association.</p>"}}}, "DisassociateLinkRequest": {"type": "structure", "required": ["GlobalNetworkId", "DeviceId", "LinkId"], "members": {"GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>", "location": "uri", "locationName": "globalNetworkId"}, "DeviceId": {"shape": "DeviceId", "documentation": "<p>The ID of the device.</p>", "location": "querystring", "locationName": "deviceId"}, "LinkId": {"shape": "LinkId", "documentation": "<p>The ID of the link.</p>", "location": "querystring", "locationName": "linkId"}}}, "DisassociateLinkResponse": {"type": "structure", "members": {"LinkAssociation": {"shape": "LinkAssociation", "documentation": "<p>Information about the link association.</p>"}}}, "DisassociateTransitGatewayConnectPeerRequest": {"type": "structure", "required": ["GlobalNetworkId", "TransitGatewayConnectPeerArn"], "members": {"GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>", "location": "uri", "locationName": "globalNetworkId"}, "TransitGatewayConnectPeerArn": {"shape": "TransitGatewayConnectPeerArn", "documentation": "<p>The Amazon Resource Name (ARN) of the transit gateway Connect peer.</p>", "location": "uri", "locationName": "transitGatewayConnectPeerArn"}}}, "DisassociateTransitGatewayConnectPeerResponse": {"type": "structure", "members": {"TransitGatewayConnectPeerAssociation": {"shape": "TransitGatewayConnectPeerAssociation", "documentation": "<p>The transit gateway Connect peer association.</p>"}}}, "EdgeOverride": {"type": "structure", "members": {"EdgeSets": {"shape": "EdgeSetList", "documentation": "<p>The list of edge locations.</p>"}, "UseEdge": {"shape": "ConstrainedString", "documentation": "<p>The edge that should be used when overriding the current edge order.</p>"}}, "documentation": "<p>Describes the edge that's used for the override. </p>"}, "EdgeSet": {"type": "list", "member": {"shape": "ConstrainedString"}}, "EdgeSetList": {"type": "list", "member": {"shape": "EdgeSet"}}, "ExceptionContextKey": {"type": "string"}, "ExceptionContextMap": {"type": "map", "key": {"shape": "ExceptionContextKey"}, "value": {"shape": "ExceptionContextValue"}}, "ExceptionContextValue": {"type": "string"}, "ExecuteCoreNetworkChangeSetRequest": {"type": "structure", "required": ["CoreNetworkId", "PolicyVersionId"], "members": {"CoreNetworkId": {"shape": "CoreNetworkId", "documentation": "<p>The ID of a core network.</p>", "location": "uri", "locationName": "coreNetworkId"}, "PolicyVersionId": {"shape": "Integer", "documentation": "<p>The ID of the policy version.</p>", "location": "uri", "locationName": "policyVersionId"}}}, "ExecuteCoreNetworkChangeSetResponse": {"type": "structure", "members": {}}, "ExternalRegionCode": {"type": "string", "max": 63, "min": 1, "pattern": "[\\s\\S]*"}, "ExternalRegionCodeList": {"type": "list", "member": {"shape": "ExternalRegionCode"}}, "FilterMap": {"type": "map", "key": {"shape": "<PERSON><PERSON><PERSON><PERSON>"}, "value": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "FilterName": {"type": "string", "max": 128, "pattern": "^[0-9a-zA-Z\\.-]*$"}, "FilterValue": {"type": "string", "max": 255, "pattern": "^[0-9a-zA-Z\\*\\.\\\\/\\?-]*$"}, "FilterValues": {"type": "list", "member": {"shape": "FilterValue"}}, "GetConnectAttachmentRequest": {"type": "structure", "required": ["AttachmentId"], "members": {"AttachmentId": {"shape": "AttachmentId", "documentation": "<p>The ID of the attachment.</p>", "location": "uri", "locationName": "attachmentId"}}}, "GetConnectAttachmentResponse": {"type": "structure", "members": {"ConnectAttachment": {"shape": "ConnectAttachment", "documentation": "<p>Details about the Connect attachment.</p>"}}}, "GetConnectPeerAssociationsRequest": {"type": "structure", "required": ["GlobalNetworkId"], "members": {"GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>", "location": "uri", "locationName": "globalNetworkId"}, "ConnectPeerIds": {"shape": "ConnectPeerIdList", "documentation": "<p>The IDs of the Connect peers.</p>", "location": "querystring", "locationName": "connectPeerIds"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}}}, "GetConnectPeerAssociationsResponse": {"type": "structure", "members": {"ConnectPeerAssociations": {"shape": "ConnectPeerAssociationList", "documentation": "<p>Displays a list of Connect peer associations.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results.</p>"}}}, "GetConnectPeerRequest": {"type": "structure", "required": ["ConnectPeerId"], "members": {"ConnectPeerId": {"shape": "ConnectPeerId", "documentation": "<p>The ID of the Connect peer.</p>", "location": "uri", "locationName": "connectPeerId"}}}, "GetConnectPeerResponse": {"type": "structure", "members": {"ConnectPeer": {"shape": "ConnectPeer", "documentation": "<p>Returns information about a core network Connect peer.</p>"}}}, "GetConnectionsRequest": {"type": "structure", "required": ["GlobalNetworkId"], "members": {"GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>", "location": "uri", "locationName": "globalNetworkId"}, "ConnectionIds": {"shape": "ConnectionIdList", "documentation": "<p>One or more connection IDs.</p>", "location": "querystring", "locationName": "connectionIds"}, "DeviceId": {"shape": "DeviceId", "documentation": "<p>The ID of the device.</p>", "location": "querystring", "locationName": "deviceId"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}}}, "GetConnectionsResponse": {"type": "structure", "members": {"Connections": {"shape": "ConnectionList", "documentation": "<p>Information about the connections.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to use for the next page of results.</p>"}}}, "GetCoreNetworkChangeEventsRequest": {"type": "structure", "required": ["CoreNetworkId", "PolicyVersionId"], "members": {"CoreNetworkId": {"shape": "CoreNetworkId", "documentation": "<p>The ID of a core network.</p>", "location": "uri", "locationName": "coreNetworkId"}, "PolicyVersionId": {"shape": "Integer", "documentation": "<p>The ID of the policy version.</p>", "location": "uri", "locationName": "policyVersionId"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}}}, "GetCoreNetworkChangeEventsResponse": {"type": "structure", "members": {"CoreNetworkChangeEvents": {"shape": "CoreNetworkChangeEventList", "documentation": "<p>The response to <code>GetCoreNetworkChangeEventsRequest</code>.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results.</p>"}}}, "GetCoreNetworkChangeSetRequest": {"type": "structure", "required": ["CoreNetworkId", "PolicyVersionId"], "members": {"CoreNetworkId": {"shape": "CoreNetworkId", "documentation": "<p>The ID of a core network.</p>", "location": "uri", "locationName": "coreNetworkId"}, "PolicyVersionId": {"shape": "Integer", "documentation": "<p>The ID of the policy version.</p>", "location": "uri", "locationName": "policyVersionId"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}}}, "GetCoreNetworkChangeSetResponse": {"type": "structure", "members": {"CoreNetworkChanges": {"shape": "CoreNetworkChangeList", "documentation": "<p>Describes a core network changes.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results.</p>"}}}, "GetCoreNetworkPolicyRequest": {"type": "structure", "required": ["CoreNetworkId"], "members": {"CoreNetworkId": {"shape": "CoreNetworkId", "documentation": "<p>The ID of a core network.</p>", "location": "uri", "locationName": "coreNetworkId"}, "PolicyVersionId": {"shape": "Integer", "documentation": "<p>The ID of a core network policy version.</p>", "location": "querystring", "locationName": "policyVersionId"}, "Alias": {"shape": "CoreNetworkPolicyAlias", "documentation": "<p>The alias of a core network policy </p>", "location": "querystring", "locationName": "alias"}}}, "GetCoreNetworkPolicyResponse": {"type": "structure", "members": {"CoreNetworkPolicy": {"shape": "CoreNetworkPolicy", "documentation": "<p>The details about a core network policy.</p>"}}}, "GetCoreNetworkRequest": {"type": "structure", "required": ["CoreNetworkId"], "members": {"CoreNetworkId": {"shape": "CoreNetworkId", "documentation": "<p>The ID of a core network.</p>", "location": "uri", "locationName": "coreNetworkId"}}}, "GetCoreNetworkResponse": {"type": "structure", "members": {"CoreNetwork": {"shape": "CoreNetwork", "documentation": "<p>Details about a core network.</p>"}}}, "GetCustomerGatewayAssociationsRequest": {"type": "structure", "required": ["GlobalNetworkId"], "members": {"GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>", "location": "uri", "locationName": "globalNetworkId"}, "CustomerGatewayArns": {"shape": "CustomerGatewayArnList", "documentation": "<p>One or more customer gateway Amazon Resource Names (ARNs). The maximum is 10.</p>", "location": "querystring", "locationName": "customerGatewayArns"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}}}, "GetCustomerGatewayAssociationsResponse": {"type": "structure", "members": {"CustomerGatewayAssociations": {"shape": "CustomerGatewayAssociationList", "documentation": "<p>The customer gateway associations.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results.</p>"}}}, "GetDevicesRequest": {"type": "structure", "required": ["GlobalNetworkId"], "members": {"GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>", "location": "uri", "locationName": "globalNetworkId"}, "DeviceIds": {"shape": "DeviceIdList", "documentation": "<p>One or more device IDs. The maximum is 10.</p>", "location": "querystring", "locationName": "deviceIds"}, "SiteId": {"shape": "SiteId", "documentation": "<p>The ID of the site.</p>", "location": "querystring", "locationName": "siteId"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}}}, "GetDevicesResponse": {"type": "structure", "members": {"Devices": {"shape": "DeviceList", "documentation": "<p>The devices.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results.</p>"}}}, "GetDirectConnectGatewayAttachmentRequest": {"type": "structure", "required": ["AttachmentId"], "members": {"AttachmentId": {"shape": "AttachmentId", "documentation": "<p>The ID of the Direct Connect gateway attachment that you want to see details about.</p>", "location": "uri", "locationName": "attachmentId"}}}, "GetDirectConnectGatewayAttachmentResponse": {"type": "structure", "members": {"DirectConnectGatewayAttachment": {"shape": "DirectConnectGatewayAttachment", "documentation": "<p>Shows details about the Direct Connect gateway attachment. </p>"}}}, "GetLinkAssociationsRequest": {"type": "structure", "required": ["GlobalNetworkId"], "members": {"GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>", "location": "uri", "locationName": "globalNetworkId"}, "DeviceId": {"shape": "DeviceId", "documentation": "<p>The ID of the device.</p>", "location": "querystring", "locationName": "deviceId"}, "LinkId": {"shape": "LinkId", "documentation": "<p>The ID of the link.</p>", "location": "querystring", "locationName": "linkId"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}}}, "GetLinkAssociationsResponse": {"type": "structure", "members": {"LinkAssociations": {"shape": "LinkAssociationList", "documentation": "<p>The link associations.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results.</p>"}}}, "GetLinksRequest": {"type": "structure", "required": ["GlobalNetworkId"], "members": {"GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>", "location": "uri", "locationName": "globalNetworkId"}, "LinkIds": {"shape": "LinkIdList", "documentation": "<p>One or more link IDs. The maximum is 10.</p>", "location": "querystring", "locationName": "linkIds"}, "SiteId": {"shape": "SiteId", "documentation": "<p>The ID of the site.</p>", "location": "querystring", "locationName": "siteId"}, "Type": {"shape": "ConstrainedString", "documentation": "<p>The link type.</p>", "location": "querystring", "locationName": "type"}, "Provider": {"shape": "ConstrainedString", "documentation": "<p>The link provider.</p>", "location": "querystring", "locationName": "provider"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}}}, "GetLinksResponse": {"type": "structure", "members": {"Links": {"shape": "LinkList", "documentation": "<p>The links.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results.</p>"}}}, "GetNetworkResourceCountsRequest": {"type": "structure", "required": ["GlobalNetworkId"], "members": {"GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>", "location": "uri", "locationName": "globalNetworkId"}, "ResourceType": {"shape": "ConstrainedString", "documentation": "<p>The resource type.</p> <p>The following are the supported resource types for Direct Connect:</p> <ul> <li> <p> <code>dxcon</code> </p> </li> <li> <p> <code>dx-gateway</code> </p> </li> <li> <p> <code>dx-vif</code> </p> </li> </ul> <p>The following are the supported resource types for Network Manager:</p> <ul> <li> <p> <code>attachment</code> </p> </li> <li> <p> <code>connect-peer</code> </p> </li> <li> <p> <code>connection</code> </p> </li> <li> <p> <code>core-network</code> </p> </li> <li> <p> <code>device</code> </p> </li> <li> <p> <code>link</code> </p> </li> <li> <p> <code>peering</code> </p> </li> <li> <p> <code>site</code> </p> </li> </ul> <p>The following are the supported resource types for Amazon VPC:</p> <ul> <li> <p> <code>customer-gateway</code> </p> </li> <li> <p> <code>transit-gateway</code> </p> </li> <li> <p> <code>transit-gateway-attachment</code> </p> </li> <li> <p> <code>transit-gateway-connect-peer</code> </p> </li> <li> <p> <code>transit-gateway-route-table</code> </p> </li> <li> <p> <code>vpn-connection</code> </p> </li> </ul>", "location": "querystring", "locationName": "resourceType"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}}}, "GetNetworkResourceCountsResponse": {"type": "structure", "members": {"NetworkResourceCounts": {"shape": "NetworkResourceCountList", "documentation": "<p>The count of resources.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results.</p>"}}}, "GetNetworkResourceRelationshipsRequest": {"type": "structure", "required": ["GlobalNetworkId"], "members": {"GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>", "location": "uri", "locationName": "globalNetworkId"}, "CoreNetworkId": {"shape": "CoreNetworkId", "documentation": "<p>The ID of a core network.</p>", "location": "querystring", "locationName": "coreNetworkId"}, "RegisteredGatewayArn": {"shape": "ResourceArn", "documentation": "<p>The ARN of the registered gateway.</p>", "location": "querystring", "locationName": "registeredGatewayArn"}, "AwsRegion": {"shape": "ExternalRegionCode", "documentation": "<p>The Amazon Web Services Region.</p>", "location": "querystring", "locationName": "awsRegion"}, "AccountId": {"shape": "AWSAccountId", "documentation": "<p>The Amazon Web Services account ID.</p>", "location": "querystring", "locationName": "accountId"}, "ResourceType": {"shape": "ConstrainedString", "documentation": "<p>The resource type.</p> <p>The following are the supported resource types for Direct Connect:</p> <ul> <li> <p> <code>dxcon</code> </p> </li> <li> <p> <code>dx-gateway</code> </p> </li> <li> <p> <code>dx-vif</code> </p> </li> </ul> <p>The following are the supported resource types for Network Manager:</p> <ul> <li> <p> <code>attachment</code> </p> </li> <li> <p> <code>connect-peer</code> </p> </li> <li> <p> <code>connection</code> </p> </li> <li> <p> <code>core-network</code> </p> </li> <li> <p> <code>device</code> </p> </li> <li> <p> <code>link</code> </p> </li> <li> <p> <code>peering</code> </p> </li> <li> <p> <code>site</code> </p> </li> </ul> <p>The following are the supported resource types for Amazon VPC:</p> <ul> <li> <p> <code>customer-gateway</code> </p> </li> <li> <p> <code>transit-gateway</code> </p> </li> <li> <p> <code>transit-gateway-attachment</code> </p> </li> <li> <p> <code>transit-gateway-connect-peer</code> </p> </li> <li> <p> <code>transit-gateway-route-table</code> </p> </li> <li> <p> <code>vpn-connection</code> </p> </li> </ul>", "location": "querystring", "locationName": "resourceType"}, "ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The ARN of the gateway.</p>", "location": "querystring", "locationName": "resourceArn"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}}}, "GetNetworkResourceRelationshipsResponse": {"type": "structure", "members": {"Relationships": {"shape": "RelationshipList", "documentation": "<p>The resource relationships.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results.</p>"}}}, "GetNetworkResourcesRequest": {"type": "structure", "required": ["GlobalNetworkId"], "members": {"GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>", "location": "uri", "locationName": "globalNetworkId"}, "CoreNetworkId": {"shape": "CoreNetworkId", "documentation": "<p>The ID of a core network.</p>", "location": "querystring", "locationName": "coreNetworkId"}, "RegisteredGatewayArn": {"shape": "ResourceArn", "documentation": "<p>The ARN of the gateway.</p>", "location": "querystring", "locationName": "registeredGatewayArn"}, "AwsRegion": {"shape": "ExternalRegionCode", "documentation": "<p>The Amazon Web Services Region.</p>", "location": "querystring", "locationName": "awsRegion"}, "AccountId": {"shape": "AWSAccountId", "documentation": "<p>The Amazon Web Services account ID.</p>", "location": "querystring", "locationName": "accountId"}, "ResourceType": {"shape": "ConstrainedString", "documentation": "<p>The resource type.</p> <p>The following are the supported resource types for Direct Connect:</p> <ul> <li> <p> <code>dxcon</code> </p> </li> <li> <p> <code>dx-gateway</code> </p> </li> <li> <p> <code>dx-vif</code> </p> </li> </ul> <p>The following are the supported resource types for Network Manager:</p> <ul> <li> <p> <code>attachment</code> </p> </li> <li> <p> <code>connect-peer</code> </p> </li> <li> <p> <code>connection</code> </p> </li> <li> <p> <code>core-network</code> </p> </li> <li> <p> <code>device</code> </p> </li> <li> <p> <code>link</code> </p> </li> <li> <p> <code>peering</code> </p> </li> <li> <p> <code>site</code> </p> </li> </ul> <p>The following are the supported resource types for Amazon VPC:</p> <ul> <li> <p> <code>customer-gateway</code> </p> </li> <li> <p> <code>transit-gateway</code> </p> </li> <li> <p> <code>transit-gateway-attachment</code> </p> </li> <li> <p> <code>transit-gateway-connect-peer</code> </p> </li> <li> <p> <code>transit-gateway-route-table</code> </p> </li> <li> <p> <code>vpn-connection</code> </p> </li> </ul>", "location": "querystring", "locationName": "resourceType"}, "ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The ARN of the resource.</p>", "location": "querystring", "locationName": "resourceArn"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}}}, "GetNetworkResourcesResponse": {"type": "structure", "members": {"NetworkResources": {"shape": "NetworkResourceList", "documentation": "<p>The network resources.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results.</p>"}}}, "GetNetworkRoutesRequest": {"type": "structure", "required": ["GlobalNetworkId", "RouteTableIdentifier"], "members": {"GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>", "location": "uri", "locationName": "globalNetworkId"}, "RouteTableIdentifier": {"shape": "RouteTableIdentifier", "documentation": "<p>The ID of the route table.</p>"}, "ExactCidrMatches": {"shape": "ConstrainedStringList", "documentation": "<p>An exact CIDR block.</p>"}, "LongestPrefixMatches": {"shape": "ConstrainedStringList", "documentation": "<p>The most specific route that matches the traffic (longest prefix match).</p>"}, "SubnetOfMatches": {"shape": "ConstrainedStringList", "documentation": "<p>The routes with a subnet that match the specified CIDR filter.</p>"}, "SupernetOfMatches": {"shape": "ConstrainedStringList", "documentation": "<p>The routes with a CIDR that encompasses the CIDR filter. Example: If you specify 10.0.1.0/30, then the result returns 10.0.1.0/29.</p>"}, "PrefixListIds": {"shape": "ConstrainedStringList", "documentation": "<p>The IDs of the prefix lists.</p>"}, "States": {"shape": "RouteStateList", "documentation": "<p>The route states.</p>"}, "Types": {"shape": "RouteTypeList", "documentation": "<p>The route types.</p>"}, "DestinationFilters": {"shape": "FilterMap", "documentation": "<p>Filter by route table destination. Possible Values: TRANSIT_GATEWAY_ATTACHMENT_ID, RESOURCE_ID, or RESOURCE_TYPE.</p>"}}}, "GetNetworkRoutesResponse": {"type": "structure", "members": {"RouteTableArn": {"shape": "ResourceArn", "documentation": "<p>The ARN of the route table.</p>"}, "CoreNetworkSegmentEdge": {"shape": "CoreNetworkSegmentEdgeIdentifier", "documentation": "<p>Describes a core network segment edge.</p>"}, "RouteTableType": {"shape": "RouteTableType", "documentation": "<p>The route table type.</p>"}, "RouteTableTimestamp": {"shape": "DateTime", "documentation": "<p>The route table creation time.</p>"}, "NetworkRoutes": {"shape": "NetworkRouteList", "documentation": "<p>The network routes.</p>"}}}, "GetNetworkTelemetryRequest": {"type": "structure", "required": ["GlobalNetworkId"], "members": {"GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>", "location": "uri", "locationName": "globalNetworkId"}, "CoreNetworkId": {"shape": "CoreNetworkId", "documentation": "<p>The ID of a core network.</p>", "location": "querystring", "locationName": "coreNetworkId"}, "RegisteredGatewayArn": {"shape": "ResourceArn", "documentation": "<p>The ARN of the gateway.</p>", "location": "querystring", "locationName": "registeredGatewayArn"}, "AwsRegion": {"shape": "ExternalRegionCode", "documentation": "<p>The Amazon Web Services Region.</p>", "location": "querystring", "locationName": "awsRegion"}, "AccountId": {"shape": "AWSAccountId", "documentation": "<p>The Amazon Web Services account ID.</p>", "location": "querystring", "locationName": "accountId"}, "ResourceType": {"shape": "ConstrainedString", "documentation": "<p>The resource type. The following are the supported resource types:</p> <ul> <li> <p> <code>connect-peer</code> </p> </li> <li> <p> <code>transit-gateway-connect-peer</code> </p> </li> <li> <p> <code>vpn-connection</code> </p> </li> </ul>", "location": "querystring", "locationName": "resourceType"}, "ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The ARN of the resource.</p>", "location": "querystring", "locationName": "resourceArn"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}}}, "GetNetworkTelemetryResponse": {"type": "structure", "members": {"NetworkTelemetry": {"shape": "NetworkTelemetryList", "documentation": "<p>The network telemetry.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results.</p>"}}}, "GetResourcePolicyRequest": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The ARN of the resource.</p>", "location": "uri", "locationName": "resourceArn"}}}, "GetResourcePolicyResponse": {"type": "structure", "members": {"PolicyDocument": {"shape": "ResourcePolicyDocument", "documentation": "<p>The resource policy document.</p>", "jsonvalue": true}}}, "GetRouteAnalysisRequest": {"type": "structure", "required": ["GlobalNetworkId", "RouteAnalysisId"], "members": {"GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>", "location": "uri", "locationName": "globalNetworkId"}, "RouteAnalysisId": {"shape": "ConstrainedString", "documentation": "<p>The ID of the route analysis.</p>", "location": "uri", "locationName": "routeAnalysisId"}}}, "GetRouteAnalysisResponse": {"type": "structure", "members": {"RouteAnalysis": {"shape": "RouteAnalysis", "documentation": "<p>The route analysis.</p>"}}}, "GetSiteToSiteVpnAttachmentRequest": {"type": "structure", "required": ["AttachmentId"], "members": {"AttachmentId": {"shape": "AttachmentId", "documentation": "<p>The ID of the attachment.</p>", "location": "uri", "locationName": "attachmentId"}}}, "GetSiteToSiteVpnAttachmentResponse": {"type": "structure", "members": {"SiteToSiteVpnAttachment": {"shape": "SiteToSiteVpnAttachment", "documentation": "<p>Describes the site-to-site attachment.</p>"}}}, "GetSitesRequest": {"type": "structure", "required": ["GlobalNetworkId"], "members": {"GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>", "location": "uri", "locationName": "globalNetworkId"}, "SiteIds": {"shape": "SiteIdList", "documentation": "<p>One or more site IDs. The maximum is 10.</p>", "location": "querystring", "locationName": "siteIds"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}}}, "GetSitesResponse": {"type": "structure", "members": {"Sites": {"shape": "SiteList", "documentation": "<p>The sites.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results.</p>"}}}, "GetTransitGatewayConnectPeerAssociationsRequest": {"type": "structure", "required": ["GlobalNetworkId"], "members": {"GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>", "location": "uri", "locationName": "globalNetworkId"}, "TransitGatewayConnectPeerArns": {"shape": "TransitGatewayConnectPeerArnList", "documentation": "<p>One or more transit gateway Connect peer Amazon Resource Names (ARNs).</p>", "location": "querystring", "locationName": "transitGatewayConnectPeerArns"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}}}, "GetTransitGatewayConnectPeerAssociationsResponse": {"type": "structure", "members": {"TransitGatewayConnectPeerAssociations": {"shape": "TransitGatewayConnectPeerAssociationList", "documentation": "<p>Information about the transit gateway Connect peer associations.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to use for the next page of results.</p>"}}}, "GetTransitGatewayPeeringRequest": {"type": "structure", "required": ["PeeringId"], "members": {"PeeringId": {"shape": "PeeringId", "documentation": "<p>The ID of the peering request.</p>", "location": "uri", "locationName": "peeringId"}}}, "GetTransitGatewayPeeringResponse": {"type": "structure", "members": {"TransitGatewayPeering": {"shape": "TransitGatewayPeering", "documentation": "<p>Returns information about a transit gateway peering. </p>"}}}, "GetTransitGatewayRegistrationsRequest": {"type": "structure", "required": ["GlobalNetworkId"], "members": {"GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>", "location": "uri", "locationName": "globalNetworkId"}, "TransitGatewayArns": {"shape": "TransitGatewayArnList", "documentation": "<p>The Amazon Resource Names (ARNs) of one or more transit gateways. The maximum is 10.</p>", "location": "querystring", "locationName": "transitGatewayArns"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}}}, "GetTransitGatewayRegistrationsResponse": {"type": "structure", "members": {"TransitGatewayRegistrations": {"shape": "TransitGatewayRegistrationList", "documentation": "<p>The transit gateway registrations.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results.</p>"}}}, "GetTransitGatewayRouteTableAttachmentRequest": {"type": "structure", "required": ["AttachmentId"], "members": {"AttachmentId": {"shape": "AttachmentId", "documentation": "<p>The ID of the transit gateway route table attachment.</p>", "location": "uri", "locationName": "attachmentId"}}}, "GetTransitGatewayRouteTableAttachmentResponse": {"type": "structure", "members": {"TransitGatewayRouteTableAttachment": {"shape": "TransitGatewayRouteTableAttachment", "documentation": "<p>Returns information about the transit gateway route table attachment.</p>"}}}, "GetVpcAttachmentRequest": {"type": "structure", "required": ["AttachmentId"], "members": {"AttachmentId": {"shape": "AttachmentId", "documentation": "<p>The ID of the attachment.</p>", "location": "uri", "locationName": "attachmentId"}}}, "GetVpcAttachmentResponse": {"type": "structure", "members": {"VpcAttachment": {"shape": "VpcAttachment", "documentation": "<p>Returns details about a VPC attachment.</p>"}}}, "GlobalNetwork": {"type": "structure", "members": {"GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>"}, "GlobalNetworkArn": {"shape": "GlobalNetworkArn", "documentation": "<p>The Amazon Resource Name (ARN) of the global network.</p>"}, "Description": {"shape": "ConstrainedString", "documentation": "<p>The description of the global network.</p>"}, "CreatedAt": {"shape": "DateTime", "documentation": "<p>The date and time that the global network was created.</p>"}, "State": {"shape": "GlobalNetworkState", "documentation": "<p>The state of the global network.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The tags for the global network.</p>"}}, "documentation": "<p>Describes a global network. This is a single private network acting as a high-level container for your network objects, including an Amazon Web Services-managed Core Network.</p>"}, "GlobalNetworkArn": {"type": "string", "max": 500, "min": 0, "pattern": "[\\s\\S]*"}, "GlobalNetworkId": {"type": "string", "max": 50, "min": 0, "pattern": "[\\s\\S]*"}, "GlobalNetworkIdList": {"type": "list", "member": {"shape": "GlobalNetworkId"}}, "GlobalNetworkList": {"type": "list", "member": {"shape": "GlobalNetwork"}}, "GlobalNetworkState": {"type": "string", "enum": ["PENDING", "AVAILABLE", "DELETING", "UPDATING"]}, "IPAddress": {"type": "string", "max": 50, "min": 1, "pattern": "[\\s\\S]*"}, "Integer": {"type": "integer"}, "InternalServerException": {"type": "structure", "required": ["Message"], "members": {"Message": {"shape": "ServerSideString"}, "RetryAfterSeconds": {"shape": "RetryAfterSeconds", "documentation": "<p>Indicates when to retry the request.</p>", "location": "header", "locationName": "Retry-After"}}, "documentation": "<p>The request has failed due to an internal error.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true}, "Link": {"type": "structure", "members": {"LinkId": {"shape": "LinkId", "documentation": "<p>The ID of the link.</p>"}, "LinkArn": {"shape": "LinkArn", "documentation": "<p>The Amazon Resource Name (ARN) of the link.</p>"}, "GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>"}, "SiteId": {"shape": "SiteId", "documentation": "<p>The ID of the site.</p>"}, "Description": {"shape": "ConstrainedString", "documentation": "<p>The description of the link.</p>"}, "Type": {"shape": "ConstrainedString", "documentation": "<p>The type of the link.</p>"}, "Bandwidth": {"shape": "Bandwidth", "documentation": "<p>The bandwidth for the link.</p>"}, "Provider": {"shape": "ConstrainedString", "documentation": "<p>The provider of the link.</p>"}, "CreatedAt": {"shape": "DateTime", "documentation": "<p>The date and time that the link was created.</p>"}, "State": {"shape": "LinkState", "documentation": "<p>The state of the link.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The tags for the link.</p>"}}, "documentation": "<p>Describes a link.</p>"}, "LinkArn": {"type": "string", "max": 500, "min": 0, "pattern": "[\\s\\S]*"}, "LinkAssociation": {"type": "structure", "members": {"GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>"}, "DeviceId": {"shape": "DeviceId", "documentation": "<p>The device ID for the link association.</p>"}, "LinkId": {"shape": "LinkId", "documentation": "<p>The ID of the link.</p>"}, "LinkAssociationState": {"shape": "LinkAssociationState", "documentation": "<p>The state of the association.</p>"}}, "documentation": "<p>Describes the association between a device and a link.</p>"}, "LinkAssociationList": {"type": "list", "member": {"shape": "LinkAssociation"}}, "LinkAssociationState": {"type": "string", "enum": ["PENDING", "AVAILABLE", "DELETING", "DELETED"]}, "LinkId": {"type": "string", "max": 50, "min": 0, "pattern": "[\\s\\S]*"}, "LinkIdList": {"type": "list", "member": {"shape": "LinkId"}}, "LinkList": {"type": "list", "member": {"shape": "Link"}}, "LinkState": {"type": "string", "enum": ["PENDING", "AVAILABLE", "DELETING", "UPDATING"]}, "ListAttachmentsRequest": {"type": "structure", "members": {"CoreNetworkId": {"shape": "CoreNetworkId", "documentation": "<p>The ID of a core network.</p>", "location": "querystring", "locationName": "coreNetworkId"}, "AttachmentType": {"shape": "AttachmentType", "documentation": "<p>The type of attachment.</p>", "location": "querystring", "locationName": "attachmentType"}, "EdgeLocation": {"shape": "ExternalRegionCode", "documentation": "<p>The Region where the edge is located.</p>", "location": "querystring", "locationName": "edgeLocation"}, "State": {"shape": "AttachmentState", "documentation": "<p>The state of the attachment.</p>", "location": "querystring", "locationName": "state"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListAttachmentsResponse": {"type": "structure", "members": {"Attachments": {"shape": "AttachmentList", "documentation": "<p>Describes the list of attachments.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results.</p>"}}}, "ListConnectPeersRequest": {"type": "structure", "members": {"CoreNetworkId": {"shape": "CoreNetworkId", "documentation": "<p>The ID of a core network.</p>", "location": "querystring", "locationName": "coreNetworkId"}, "ConnectAttachmentId": {"shape": "AttachmentId", "documentation": "<p>The ID of the attachment.</p>", "location": "querystring", "locationName": "connectAttachmentId"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListConnectPeersResponse": {"type": "structure", "members": {"ConnectPeers": {"shape": "ConnectPeerSummaryList", "documentation": "<p>Describes the Connect peers.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results.</p>"}}}, "ListCoreNetworkPolicyVersionsRequest": {"type": "structure", "required": ["CoreNetworkId"], "members": {"CoreNetworkId": {"shape": "CoreNetworkId", "documentation": "<p>The ID of a core network.</p>", "location": "uri", "locationName": "coreNetworkId"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListCoreNetworkPolicyVersionsResponse": {"type": "structure", "members": {"CoreNetworkPolicyVersions": {"shape": "CoreNetworkPolicyVersionList", "documentation": "<p>Describes core network policy versions.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results.</p>"}}}, "ListCoreNetworksRequest": {"type": "structure", "members": {"MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListCoreNetworksResponse": {"type": "structure", "members": {"CoreNetworks": {"shape": "CoreNetworkSummaryList", "documentation": "<p>Describes the list of core networks.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results.</p>"}}}, "ListOrganizationServiceAccessStatusRequest": {"type": "structure", "members": {"MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListOrganizationServiceAccessStatusResponse": {"type": "structure", "members": {"OrganizationStatus": {"shape": "OrganizationStatus", "documentation": "<p>Displays the status of an Amazon Web Services Organization.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results.</p>"}}}, "ListPeeringsRequest": {"type": "structure", "members": {"CoreNetworkId": {"shape": "CoreNetworkId", "documentation": "<p>The ID of a core network.</p>", "location": "querystring", "locationName": "coreNetworkId"}, "PeeringType": {"shape": "PeeringType", "documentation": "<p>Returns a list of a peering requests.</p>", "location": "querystring", "locationName": "peeringType"}, "EdgeLocation": {"shape": "ExternalRegionCode", "documentation": "<p>Returns a list edge locations for the </p>", "location": "querystring", "locationName": "edgeLocation"}, "State": {"shape": "PeeringState", "documentation": "<p>Returns a list of the peering request states.</p>", "location": "querystring", "locationName": "state"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListPeeringsResponse": {"type": "structure", "members": {"Peerings": {"shape": "PeeringList", "documentation": "<p>Lists the transit gateway peerings for the <code>ListPeerings</code> request.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource.</p>", "location": "uri", "locationName": "resourceArn"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"TagList": {"shape": "TagList", "documentation": "<p>The list of tags.</p>"}}}, "Location": {"type": "structure", "members": {"Address": {"shape": "ConstrainedString", "documentation": "<p>The physical address.</p>"}, "Latitude": {"shape": "ConstrainedString", "documentation": "<p>The latitude.</p>"}, "Longitude": {"shape": "ConstrainedString", "documentation": "<p>The longitude.</p>"}}, "documentation": "<p>Describes a location.</p>", "sensitive": true}, "Long": {"type": "long"}, "MaxResults": {"type": "integer", "max": 500, "min": 1}, "NetworkFunctionGroup": {"type": "structure", "members": {"Name": {"shape": "ConstrainedString", "documentation": "<p>The name of the network function group.</p>"}}, "documentation": "<p>Describes a network function group for service insertion.</p>"}, "NetworkFunctionGroupList": {"type": "list", "member": {"shape": "NetworkFunctionGroup"}}, "NetworkFunctionGroupName": {"type": "string", "pattern": "[\\s\\S]*"}, "NetworkResource": {"type": "structure", "members": {"RegisteredGatewayArn": {"shape": "ResourceArn", "documentation": "<p>The ARN of the gateway.</p>"}, "CoreNetworkId": {"shape": "CoreNetworkId", "documentation": "<p>The ID of a core network.</p>"}, "AwsRegion": {"shape": "ExternalRegionCode", "documentation": "<p>The Amazon Web Services Region.</p>"}, "AccountId": {"shape": "AWSAccountId", "documentation": "<p>The Amazon Web Services account ID.</p>"}, "ResourceType": {"shape": "ConstrainedString", "documentation": "<p>The resource type.</p> <p>The following are the supported resource types for Direct Connect:</p> <ul> <li> <p> <code>dxcon</code> </p> </li> <li> <p> <code>dx-gateway</code> </p> </li> <li> <p> <code>dx-vif</code> </p> </li> </ul> <p>The following are the supported resource types for Network Manager:</p> <ul> <li> <p> <code>attachment</code> </p> </li> <li> <p> <code>connect-peer</code> </p> </li> <li> <p> <code>connection</code> </p> </li> <li> <p> <code>core-network</code> </p> </li> <li> <p> <code>device</code> </p> </li> <li> <p> <code>link</code> </p> </li> <li> <p> <code>peering</code> </p> </li> <li> <p> <code>site</code> </p> </li> </ul> <p>The following are the supported resource types for Amazon VPC:</p> <ul> <li> <p> <code>customer-gateway</code> </p> </li> <li> <p> <code>transit-gateway</code> </p> </li> <li> <p> <code>transit-gateway-attachment</code> </p> </li> <li> <p> <code>transit-gateway-connect-peer</code> </p> </li> <li> <p> <code>transit-gateway-route-table</code> </p> </li> <li> <p> <code>vpn-connection</code> </p> </li> </ul>"}, "ResourceId": {"shape": "ConstrainedString", "documentation": "<p>The ID of the resource.</p>"}, "ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The ARN of the resource.</p>"}, "Definition": {"shape": "ConstrainedString", "documentation": "<p>Information about the resource, in JSON format. Network Manager gets this information by describing the resource using its Describe API call.</p>"}, "DefinitionTimestamp": {"shape": "DateTime", "documentation": "<p>The time that the resource definition was retrieved.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The tags.</p>"}, "Metadata": {"shape": "NetworkResourceMetadataMap", "documentation": "<p>The resource metadata.</p>"}}, "documentation": "<p>Describes a network resource.</p>"}, "NetworkResourceCount": {"type": "structure", "members": {"ResourceType": {"shape": "ConstrainedString", "documentation": "<p>The resource type.</p>"}, "Count": {"shape": "Integer", "documentation": "<p>The resource count.</p>"}}, "documentation": "<p>Describes a resource count.</p>"}, "NetworkResourceCountList": {"type": "list", "member": {"shape": "NetworkResourceCount"}}, "NetworkResourceList": {"type": "list", "member": {"shape": "NetworkResource"}}, "NetworkResourceMetadataMap": {"type": "map", "key": {"shape": "ConstrainedString"}, "value": {"shape": "ConstrainedString"}}, "NetworkResourceSummary": {"type": "structure", "members": {"RegisteredGatewayArn": {"shape": "ResourceArn", "documentation": "<p>The ARN of the gateway.</p>"}, "ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The ARN of the resource.</p>"}, "ResourceType": {"shape": "ConstrainedString", "documentation": "<p>The resource type.</p>"}, "Definition": {"shape": "ConstrainedString", "documentation": "<p>Information about the resource, in JSON format. Network Manager gets this information by describing the resource using its Describe API call.</p>"}, "NameTag": {"shape": "ConstrainedString", "documentation": "<p>The value for the Name tag.</p>"}, "IsMiddlebox": {"shape": "Boolean", "documentation": "<p>Indicates whether this is a middlebox appliance.</p>"}}, "documentation": "<p>Describes a network resource.</p>"}, "NetworkRoute": {"type": "structure", "members": {"DestinationCidrBlock": {"shape": "ConstrainedString", "documentation": "<p>A unique identifier for the route, such as a CIDR block.</p>"}, "Destinations": {"shape": "NetworkRouteDestinationList", "documentation": "<p>The destinations.</p>"}, "PrefixListId": {"shape": "ConstrainedString", "documentation": "<p>The ID of the prefix list.</p>"}, "State": {"shape": "RouteState", "documentation": "<p>The route state. The possible values are <code>active</code> and <code>blackhole</code>.</p>"}, "Type": {"shape": "RouteType", "documentation": "<p>The route type. The possible values are <code>propagated</code> and <code>static</code>.</p>"}}, "documentation": "<p>Describes a network route.</p>"}, "NetworkRouteDestination": {"type": "structure", "members": {"CoreNetworkAttachmentId": {"shape": "AttachmentId", "documentation": "<p>The ID of a core network attachment.</p>"}, "TransitGatewayAttachmentId": {"shape": "TransitGatewayAttachmentId", "documentation": "<p>The ID of the transit gateway attachment.</p>"}, "SegmentName": {"shape": "ConstrainedString", "documentation": "<p>The name of the segment.</p>"}, "NetworkFunctionGroupName": {"shape": "ConstrainedString", "documentation": "<p>The network function group name associated with the destination.</p>"}, "EdgeLocation": {"shape": "ExternalRegionCode", "documentation": "<p>The edge location for the network destination.</p>"}, "ResourceType": {"shape": "ConstrainedString", "documentation": "<p>The resource type.</p>"}, "ResourceId": {"shape": "ConstrainedString", "documentation": "<p>The ID of the resource.</p>"}}, "documentation": "<p>Describes the destination of a network route.</p>"}, "NetworkRouteDestinationList": {"type": "list", "member": {"shape": "NetworkRouteDestination"}}, "NetworkRouteList": {"type": "list", "member": {"shape": "NetworkRoute"}}, "NetworkTelemetry": {"type": "structure", "members": {"RegisteredGatewayArn": {"shape": "ResourceArn", "documentation": "<p>The ARN of the gateway.</p>"}, "CoreNetworkId": {"shape": "CoreNetworkId", "documentation": "<p>The ID of a core network.</p>"}, "AwsRegion": {"shape": "ExternalRegionCode", "documentation": "<p>The Amazon Web Services Region.</p>"}, "AccountId": {"shape": "AWSAccountId", "documentation": "<p>The Amazon Web Services account ID.</p>"}, "ResourceType": {"shape": "ConstrainedString", "documentation": "<p>The resource type.</p>"}, "ResourceId": {"shape": "ConstrainedString", "documentation": "<p>The ID of the resource.</p>"}, "ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The ARN of the resource.</p>"}, "Address": {"shape": "ConstrainedString", "documentation": "<p>The address.</p>"}, "Health": {"shape": "ConnectionHealth", "documentation": "<p>The connection health.</p>"}}, "documentation": "<p>Describes the telemetry information for a resource.</p>"}, "NetworkTelemetryList": {"type": "list", "member": {"shape": "NetworkTelemetry"}}, "NextToken": {"type": "string", "max": 2048, "min": 0, "pattern": "[\\s\\S]*"}, "OrganizationAwsServiceAccessStatus": {"type": "string", "max": 50, "min": 0}, "OrganizationId": {"type": "string", "max": 50, "min": 0, "pattern": "^o-([0-9a-f]{8,17})$"}, "OrganizationStatus": {"type": "structure", "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The ID of an Amazon Web Services Organization.</p>"}, "OrganizationAwsServiceAccessStatus": {"shape": "OrganizationAwsServiceAccessStatus", "documentation": "<p>The status of the organization's AWS service access. This will be <code>ENABLED</code> or <code>DISABLED</code>.</p>"}, "SLRDeploymentStatus": {"shape": "SLRDeploymentStatus", "documentation": "<p>The status of the SLR deployment for the account. This will be either <code>SUCCEEDED</code> or <code>IN_PROGRESS</code>.</p>"}, "AccountStatusList": {"shape": "AccountStatusList", "documentation": "<p>The current service-linked role (SLR) deployment status for an Amazon Web Services Organization's accounts. This will be either <code>SUCCEEDED</code> or <code>IN_PROGRESS</code>.</p>"}}, "documentation": "<p>The status of an Amazon Web Services Organization and the accounts within that organization.</p>"}, "PathComponent": {"type": "structure", "members": {"Sequence": {"shape": "Integer", "documentation": "<p>The sequence number in the path. The destination is 0.</p>"}, "Resource": {"shape": "NetworkResourceSummary", "documentation": "<p>The resource.</p>"}, "DestinationCidrBlock": {"shape": "ConstrainedString", "documentation": "<p>The destination CIDR block in the route table.</p>"}}, "documentation": "<p>Describes a path component.</p>"}, "PathComponentList": {"type": "list", "member": {"shape": "PathComponent"}}, "Peering": {"type": "structure", "members": {"CoreNetworkId": {"shape": "CoreNetworkId", "documentation": "<p>The ID of the core network for the peering request.</p>"}, "CoreNetworkArn": {"shape": "CoreNetworkArn", "documentation": "<p>The ARN of a core network.</p>"}, "PeeringId": {"shape": "PeeringId", "documentation": "<p>The ID of the peering attachment. </p>"}, "OwnerAccountId": {"shape": "AWSAccountId", "documentation": "<p>The ID of the account owner.</p>"}, "PeeringType": {"shape": "PeeringType", "documentation": "<p>The type of peering. This will be <code>TRANSIT_GATEWAY</code>.</p>"}, "State": {"shape": "PeeringState", "documentation": "<p>The current state of the peering connection. </p>"}, "EdgeLocation": {"shape": "ExternalRegionCode", "documentation": "<p>The edge location for the peer.</p>"}, "ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The resource ARN of the peer.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The list of key-value tags associated with the peering.</p>"}, "CreatedAt": {"shape": "DateTime", "documentation": "<p>The timestamp when the attachment peer was created.</p>"}, "LastModificationErrors": {"shape": "PeeringErrorList", "documentation": "<p>Describes the error associated with the Connect peer request.</p>"}}, "documentation": "<p>Describes a peering connection.</p>"}, "PeeringError": {"type": "structure", "members": {"Code": {"shape": "PeeringErrorCode", "documentation": "<p>The error code for the peering request.</p>"}, "Message": {"shape": "ServerSideString", "documentation": "<p>The message associated with the error <code>code</code>.</p>"}, "ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The ARN of the requested peering resource.</p>"}, "RequestId": {"shape": "ServerSideString", "documentation": "<p>The ID of the Peering request.</p>"}, "MissingPermissionsContext": {"shape": "PermissionsErrorContext", "documentation": "<p>Provides additional information about missing permissions for the peering error.</p>"}}, "documentation": "<p>Describes an error associated with a peering request.</p>"}, "PeeringErrorCode": {"type": "string", "enum": ["TRANSIT_GATEWAY_NOT_FOUND", "TRANSIT_GATEWAY_PEERS_LIMIT_EXCEEDED", "MISSING_PERMISSIONS", "INTERNAL_ERROR", "EDGE_LOCATION_PEER_DUPLICATE", "INVALID_TRANSIT_GATEWAY_STATE"]}, "PeeringErrorList": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "max": 20, "min": 0}, "PeeringId": {"type": "string", "max": 50, "min": 0, "pattern": "^peering-([0-9a-f]{8,17})$"}, "PeeringList": {"type": "list", "member": {"shape": "Peering"}}, "PeeringState": {"type": "string", "enum": ["CREATING", "FAILED", "AVAILABLE", "DELETING"]}, "PeeringType": {"type": "string", "enum": ["TRANSIT_GATEWAY"]}, "PermissionsErrorContext": {"type": "structure", "members": {"MissingPermission": {"shape": "ServerSideString", "documentation": "<p>The missing permissions.</p>"}}, "documentation": "<p>Describes additional information about missing permissions. </p>"}, "ProposedNetworkFunctionGroupChange": {"type": "structure", "members": {"Tags": {"shape": "TagList", "documentation": "<p>The list of proposed changes to the key-value tags associated with the network function group.</p>"}, "AttachmentPolicyRuleNumber": {"shape": "Integer", "documentation": "<p>The proposed new attachment policy rule number for the network function group.</p>"}, "NetworkFunctionGroupName": {"shape": "ConstrainedString", "documentation": "<p>The proposed name change for the network function group name.</p>"}}, "documentation": "<p>Describes proposed changes to a network function group. </p>"}, "ProposedSegmentChange": {"type": "structure", "members": {"Tags": {"shape": "TagList", "documentation": "<p>The list of key-value tags that changed for the segment.</p>"}, "AttachmentPolicyRuleNumber": {"shape": "Integer", "documentation": "<p>The rule number in the policy document that applies to this change.</p>"}, "SegmentName": {"shape": "ConstrainedString", "documentation": "<p>The name of the segment to change.</p>"}}, "documentation": "<p>Describes a proposed segment change. In some cases, the segment change must first be evaluated and accepted. </p>"}, "PutCoreNetworkPolicyRequest": {"type": "structure", "required": ["CoreNetworkId", "PolicyDocument"], "members": {"CoreNetworkId": {"shape": "CoreNetworkId", "documentation": "<p>The ID of a core network.</p>", "location": "uri", "locationName": "coreNetworkId"}, "PolicyDocument": {"shape": "CoreNetworkPolicyDocument", "documentation": "<p>The policy document.</p>", "jsonvalue": true}, "Description": {"shape": "ConstrainedString", "documentation": "<p>a core network policy description.</p>"}, "LatestVersionId": {"shape": "Integer", "documentation": "<p>The ID of a core network policy. </p>"}, "ClientToken": {"shape": "ClientToken", "documentation": "<p>The client token associated with the request.</p>", "idempotencyToken": true}}}, "PutCoreNetworkPolicyResponse": {"type": "structure", "members": {"CoreNetworkPolicy": {"shape": "CoreNetworkPolicy", "documentation": "<p>Describes the changed core network policy.</p>"}}}, "PutResourcePolicyRequest": {"type": "structure", "required": ["PolicyDocument", "ResourceArn"], "members": {"PolicyDocument": {"shape": "ResourcePolicyDocument", "documentation": "<p>The JSON resource policy document.</p>", "jsonvalue": true}, "ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The ARN of the resource policy. </p>", "location": "uri", "locationName": "resourceArn"}}}, "PutResourcePolicyResponse": {"type": "structure", "members": {}}, "ReasonContextKey": {"type": "string", "max": 10000000, "min": 0, "pattern": "[\\s\\S]*"}, "ReasonContextMap": {"type": "map", "key": {"shape": "ReasonContextKey"}, "value": {"shape": "ReasonContextValue"}}, "ReasonContextValue": {"type": "string", "max": 10000000, "min": 0, "pattern": "[\\s\\S]*"}, "RegisterTransitGatewayRequest": {"type": "structure", "required": ["GlobalNetworkId", "TransitGatewayArn"], "members": {"GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>", "location": "uri", "locationName": "globalNetworkId"}, "TransitGatewayArn": {"shape": "TransitGatewayArn", "documentation": "<p>The Amazon Resource Name (ARN) of the transit gateway.</p>"}}}, "RegisterTransitGatewayResponse": {"type": "structure", "members": {"TransitGatewayRegistration": {"shape": "TransitGatewayRegistration", "documentation": "<p>Information about the transit gateway registration.</p>"}}}, "RejectAttachmentRequest": {"type": "structure", "required": ["AttachmentId"], "members": {"AttachmentId": {"shape": "AttachmentId", "documentation": "<p>The ID of the attachment.</p>", "location": "uri", "locationName": "attachmentId"}}}, "RejectAttachmentResponse": {"type": "structure", "members": {"Attachment": {"shape": "Attachment", "documentation": "<p>Describes the rejected attachment request.</p>"}}}, "Relationship": {"type": "structure", "members": {"From": {"shape": "ConstrainedString", "documentation": "<p>The ARN of the resource.</p>"}, "To": {"shape": "ConstrainedString", "documentation": "<p>The ARN of the resource.</p>"}}, "documentation": "<p>Describes a resource relationship.</p>"}, "RelationshipList": {"type": "list", "member": {"shape": "Relationship"}}, "ResourceArn": {"type": "string", "max": 1500, "min": 0, "pattern": "[\\s\\S]*"}, "ResourceNotFoundException": {"type": "structure", "required": ["Message", "ResourceId", "ResourceType"], "members": {"Message": {"shape": "ServerSideString"}, "ResourceId": {"shape": "ServerSideString", "documentation": "<p>The ID of the resource.</p>"}, "ResourceType": {"shape": "ServerSideString", "documentation": "<p>The resource type.</p>"}, "Context": {"shape": "ExceptionContextMap", "documentation": "<p>The specified resource could not be found.</p>"}}, "documentation": "<p>The specified resource could not be found.</p>", "error": {"httpStatusCode": 404}, "exception": true}, "ResourcePolicyDocument": {"type": "string", "max": 10000000, "min": 0, "pattern": "[\\s\\S]*"}, "RestoreCoreNetworkPolicyVersionRequest": {"type": "structure", "required": ["CoreNetworkId", "PolicyVersionId"], "members": {"CoreNetworkId": {"shape": "CoreNetworkId", "documentation": "<p>The ID of a core network.</p>", "location": "uri", "locationName": "coreNetworkId"}, "PolicyVersionId": {"shape": "Integer", "documentation": "<p>The ID of the policy version to restore.</p>", "location": "uri", "locationName": "policyVersionId"}}}, "RestoreCoreNetworkPolicyVersionResponse": {"type": "structure", "members": {"CoreNetworkPolicy": {"shape": "CoreNetworkPolicy", "documentation": "<p>Describes the restored core network policy.</p>"}}}, "RetryAfterSeconds": {"type": "integer"}, "RouteAnalysis": {"type": "structure", "members": {"GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>"}, "OwnerAccountId": {"shape": "AWSAccountId", "documentation": "<p>The ID of the AWS account that created the route analysis.</p>"}, "RouteAnalysisId": {"shape": "ConstrainedString", "documentation": "<p>The ID of the route analysis.</p>"}, "StartTimestamp": {"shape": "DateTime", "documentation": "<p>The time that the analysis started.</p>"}, "Status": {"shape": "RouteAnalysisStatus", "documentation": "<p>The status of the route analysis.</p>"}, "Source": {"shape": "RouteAnalysisEndpointOptions", "documentation": "<p>The source.</p>"}, "Destination": {"shape": "RouteAnalysisEndpointOptions", "documentation": "<p>The destination.</p>"}, "IncludeReturnPath": {"shape": "Boolean", "documentation": "<p>Indicates whether to analyze the return path. The return path is not analyzed if the forward path analysis does not succeed.</p>"}, "UseMiddleboxes": {"shape": "Boolean", "documentation": "<p>Indicates whether to include the location of middlebox appliances in the route analysis.</p>"}, "ForwardPath": {"shape": "RouteAnalysisPath", "documentation": "<p>The forward path.</p>"}, "ReturnPath": {"shape": "RouteAnalysisPath", "documentation": "<p>The return path.</p>"}}, "documentation": "<p>Describes a route analysis.</p>"}, "RouteAnalysisCompletion": {"type": "structure", "members": {"ResultCode": {"shape": "RouteAnalysisCompletionResultCode", "documentation": "<p>The result of the analysis. If the status is <code>NOT_CONNECTED</code>, check the reason code.</p>"}, "ReasonCode": {"shape": "RouteAnalysisCompletionReasonCode", "documentation": "<p>The reason code. Available only if a connection is not found.</p> <ul> <li> <p> <code>BLACKHOLE_ROUTE_FOR_DESTINATION_FOUND</code> - Found a black hole route with the destination CIDR block.</p> </li> <li> <p> <code>CYCLIC_PATH_DETECTED</code> - Found the same resource multiple times while traversing the path.</p> </li> <li> <p> <code>INACTIVE_ROUTE_FOR_DESTINATION_FOUND</code> - Found an inactive route with the destination CIDR block.</p> </li> <li> <p> <code>MAX_HOPS_EXCEEDED</code> - Analysis exceeded 64 hops without finding the destination.</p> </li> <li> <p> <code>ROUTE_NOT_FOUND</code> - Cannot find a route table with the destination CIDR block.</p> </li> <li> <p> <code>TGW_ATTACH_ARN_NO_MATCH</code> - Found an attachment, but not with the correct destination ARN.</p> </li> <li> <p> <code>TGW_ATTACH_NOT_FOUND</code> - Cannot find an attachment.</p> </li> <li> <p> <code>TGW_ATTACH_NOT_IN_TGW</code> - Found an attachment, but not to the correct transit gateway.</p> </li> <li> <p> <code>TGW_ATTACH_STABLE_ROUTE_TABLE_NOT_FOUND</code> - The state of the route table association is not associated.</p> </li> </ul>"}, "ReasonContext": {"shape": "ReasonContextMap", "documentation": "<p>Additional information about the path. Available only if a connection is not found.</p>"}}, "documentation": "<p>Describes the status of an analysis at completion.</p>"}, "RouteAnalysisCompletionReasonCode": {"type": "string", "enum": ["TRANSIT_GATEWAY_ATTACHMENT_NOT_FOUND", "TRANSIT_GATEWAY_ATTACHMENT_NOT_IN_TRANSIT_GATEWAY", "CYCLIC_PATH_DETECTED", "TRANSIT_GATEWAY_ATTACHMENT_STABLE_ROUTE_TABLE_NOT_FOUND", "ROUTE_NOT_FOUND", "BLACKHOLE_ROUTE_FOR_DESTINATION_FOUND", "INACTIVE_ROUTE_FOR_DESTINATION_FOUND", "TRANSIT_GATEWAY_ATTACHMENT_ATTACH_ARN_NO_MATCH", "MAX_HOPS_EXCEEDED", "POSSIBLE_MIDDLEBOX", "NO_DESTINATION_ARN_PROVIDED"]}, "RouteAnalysisCompletionResultCode": {"type": "string", "enum": ["CONNECTED", "NOT_CONNECTED"]}, "RouteAnalysisEndpointOptions": {"type": "structure", "members": {"TransitGatewayAttachmentArn": {"shape": "TransitGatewayAttachmentArn", "documentation": "<p>The ARN of the transit gateway attachment.</p>"}, "TransitGatewayArn": {"shape": "TransitGatewayArn", "documentation": "<p>The ARN of the transit gateway.</p>"}, "IpAddress": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>The IP address.</p>"}}, "documentation": "<p>Describes a source or a destination.</p>"}, "RouteAnalysisEndpointOptionsSpecification": {"type": "structure", "members": {"TransitGatewayAttachmentArn": {"shape": "TransitGatewayAttachmentArn", "documentation": "<p>The ARN of the transit gateway attachment.</p>"}, "IpAddress": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>The IP address.</p>"}}, "documentation": "<p>Describes a source or a destination.</p>"}, "RouteAnalysisPath": {"type": "structure", "members": {"CompletionStatus": {"shape": "RouteAnalysisCompletion", "documentation": "<p>The status of the analysis at completion.</p>"}, "Path": {"shape": "PathComponentList", "documentation": "<p>The route analysis path.</p>"}}, "documentation": "<p>Describes a route analysis path.</p>"}, "RouteAnalysisStatus": {"type": "string", "enum": ["RUNNING", "COMPLETED", "FAILED"]}, "RouteState": {"type": "string", "enum": ["ACTIVE", "BLACKHOLE"]}, "RouteStateList": {"type": "list", "member": {"shape": "RouteState"}}, "RouteTableIdentifier": {"type": "structure", "members": {"TransitGatewayRouteTableArn": {"shape": "TransitGatewayRouteTableArn", "documentation": "<p>The ARN of the transit gateway route table for the attachment request. For example, <code>\"TransitGatewayRouteTableArn\": \"arn:aws:ec2:us-west-2:123456789012:transit-gateway-route-table/tgw-rtb-9876543210123456\"</code>.</p>"}, "CoreNetworkSegmentEdge": {"shape": "CoreNetworkSegmentEdgeIdentifier", "documentation": "<p>The segment edge in a core network.</p>"}, "CoreNetworkNetworkFunctionGroup": {"shape": "CoreNetworkNetworkFunctionGroupIdentifier", "documentation": "<p>The route table identifier associated with the network function group.</p>"}}, "documentation": "<p>Describes a route table.</p>"}, "RouteTableType": {"type": "string", "enum": ["TRANSIT_GATEWAY_ROUTE_TABLE", "CORE_NETWORK_SEGMENT", "NETWORK_FUNCTION_GROUP"]}, "RouteType": {"type": "string", "enum": ["PROPAGATED", "STATIC"]}, "RouteTypeList": {"type": "list", "member": {"shape": "RouteType"}}, "SLRDeploymentStatus": {"type": "string", "max": 50, "min": 0}, "SegmentActionServiceInsertion": {"type": "string", "enum": ["send-via", "send-to"]}, "SendViaMode": {"type": "string", "enum": ["dual-hop", "single-hop"]}, "ServerSideString": {"type": "string", "max": 10000000, "min": 0, "pattern": "[\\s\\S]*"}, "ServiceInsertionAction": {"type": "structure", "members": {"Action": {"shape": "SegmentActionServiceInsertion", "documentation": "<p>The action the service insertion takes for traffic. <code>send-via</code> sends east-west traffic between attachments. <code>send-to</code> sends north-south traffic to the security appliance, and then from that to either the Internet or to an on-premesis location. </p>"}, "Mode": {"shape": "SendViaMode", "documentation": "<p>Describes the mode packets take for the <code>send-via</code> action. This is not used when the action is <code>send-to</code>. <code>dual-hop</code> packets traverse attachments in both the source to the destination core network edges. This mode requires that an inspection attachment must be present in all Regions of the service insertion-enabled segments. For <code>single-hop</code>, packets traverse a single intermediate inserted attachment. You can use <code>EdgeOverride</code> to specify a specific edge to use. </p>"}, "WhenSentTo": {"shape": "WhenSentTo", "documentation": "<p>The list of destination segments if the service insertion action is <code>send-via</code>.</p>"}, "Via": {"shape": "Via", "documentation": "<p>The list of network function groups and any edge overrides for the chosen service insertion action. Used for both <code>send-to</code> or <code>send-via</code>.</p>"}}, "documentation": "<p>Describes the action that the service insertion will take for any segments associated with it.</p>"}, "ServiceInsertionActionList": {"type": "list", "member": {"shape": "ServiceInsertionAction"}}, "ServiceInsertionSegments": {"type": "structure", "members": {"SendVia": {"shape": "ConstrainedStringList", "documentation": "<p>The list of segments associated with the <code>send-via</code> action.</p>"}, "SendTo": {"shape": "ConstrainedStringList", "documentation": "<p>The list of segments associated with the <code>send-to</code> action.</p>"}}, "documentation": "<p>Describes the segments associated with the service insertion action.</p>"}, "ServiceQuotaExceededException": {"type": "structure", "required": ["Message", "LimitCode", "ServiceCode"], "members": {"Message": {"shape": "ServerSideString", "documentation": "<p>The error message.</p>"}, "ResourceId": {"shape": "ServerSideString", "documentation": "<p>The ID of the resource.</p>"}, "ResourceType": {"shape": "ServerSideString", "documentation": "<p>The resource type.</p>"}, "LimitCode": {"shape": "ServerSideString", "documentation": "<p>The limit code.</p>"}, "ServiceCode": {"shape": "ServerSideString", "documentation": "<p>The service code.</p>"}}, "documentation": "<p>A service limit was exceeded.</p>", "error": {"httpStatusCode": 402}, "exception": true}, "Site": {"type": "structure", "members": {"SiteId": {"shape": "SiteId", "documentation": "<p>The ID of the site.</p>"}, "SiteArn": {"shape": "SiteArn", "documentation": "<p>The Amazon Resource Name (ARN) of the site.</p>"}, "GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>"}, "Description": {"shape": "ConstrainedString", "documentation": "<p>The description of the site.</p>"}, "Location": {"shape": "Location", "documentation": "<p>The location of the site.</p>"}, "CreatedAt": {"shape": "DateTime", "documentation": "<p>The date and time that the site was created.</p>"}, "State": {"shape": "SiteState", "documentation": "<p>The state of the site.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The tags for the site.</p>"}}, "documentation": "<p>Describes a site.</p>"}, "SiteArn": {"type": "string", "max": 500, "min": 0, "pattern": "[\\s\\S]*"}, "SiteId": {"type": "string", "max": 50, "min": 0, "pattern": "[\\s\\S]*"}, "SiteIdList": {"type": "list", "member": {"shape": "SiteId"}}, "SiteList": {"type": "list", "member": {"shape": "Site"}}, "SiteState": {"type": "string", "enum": ["PENDING", "AVAILABLE", "DELETING", "UPDATING"]}, "SiteToSiteVpnAttachment": {"type": "structure", "members": {"Attachment": {"shape": "Attachment", "documentation": "<p>Provides details about a site-to-site VPN attachment.</p>"}, "VpnConnectionArn": {"shape": "VpnConnectionArn", "documentation": "<p>The ARN of the site-to-site VPN attachment. </p>"}}, "documentation": "<p>Creates a site-to-site VPN attachment.</p>"}, "StartOrganizationServiceAccessUpdateRequest": {"type": "structure", "required": ["Action"], "members": {"Action": {"shape": "Action", "documentation": "<p>The action to take for the update request. This can be either <code>ENABLE</code> or <code>DISABLE</code>.</p>"}}}, "StartOrganizationServiceAccessUpdateResponse": {"type": "structure", "members": {"OrganizationStatus": {"shape": "OrganizationStatus", "documentation": "<p>The status of the service access update request for an Amazon Web Services Organization.</p>"}}}, "StartRouteAnalysisRequest": {"type": "structure", "required": ["GlobalNetworkId", "Source", "Destination"], "members": {"GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>", "location": "uri", "locationName": "globalNetworkId"}, "Source": {"shape": "RouteAnalysisEndpointOptionsSpecification", "documentation": "<p>The source from which traffic originates.</p>"}, "Destination": {"shape": "RouteAnalysisEndpointOptionsSpecification", "documentation": "<p>The destination.</p>"}, "IncludeReturnPath": {"shape": "Boolean", "documentation": "<p>Indicates whether to analyze the return path. The default is <code>false</code>.</p>"}, "UseMiddleboxes": {"shape": "Boolean", "documentation": "<p>Indicates whether to include the location of middlebox appliances in the route analysis. The default is <code>false</code>.</p>"}}}, "StartRouteAnalysisResponse": {"type": "structure", "members": {"RouteAnalysis": {"shape": "RouteAnalysis", "documentation": "<p>The route analysis.</p>"}}}, "SubnetArn": {"type": "string", "max": 500, "min": 0, "pattern": "^arn:[^:]{1,63}:ec2:[^:]{0,63}:[^:]{0,63}:subnet\\/subnet-[0-9a-f]{8,17}$|^$"}, "SubnetArnList": {"type": "list", "member": {"shape": "SubnetArn"}}, "Tag": {"type": "structure", "members": {"Key": {"shape": "TagKey", "documentation": "<p>The tag key.</p> <p>Constraints: Maximum length of 128 characters.</p>"}, "Value": {"shape": "TagValue", "documentation": "<p>The tag value.</p> <p>Constraints: Maximum length of 256 characters.</p>"}}, "documentation": "<p>Describes a tag.</p>"}, "TagKey": {"type": "string", "max": 10000000, "min": 0, "pattern": "[\\s\\S]*"}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}}, "TagList": {"type": "list", "member": {"shape": "Tag"}}, "TagResourceRequest": {"type": "structure", "required": ["ResourceArn", "Tags"], "members": {"ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource.</p>", "location": "uri", "locationName": "resourceArn"}, "Tags": {"shape": "TagList", "documentation": "<p>The tags to apply to the specified resource.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 10000000, "min": 0, "pattern": "[\\s\\S]*"}, "ThrottlingException": {"type": "structure", "required": ["Message"], "members": {"Message": {"shape": "ServerSideString"}, "RetryAfterSeconds": {"shape": "RetryAfterSeconds", "documentation": "<p>Indicates when to retry the request.</p>", "location": "header", "locationName": "Retry-After"}}, "documentation": "<p>The request was denied due to request throttling.</p>", "error": {"httpStatusCode": 429}, "exception": true}, "TransitGatewayArn": {"type": "string", "max": 500, "min": 0, "pattern": "[\\s\\S]*"}, "TransitGatewayArnList": {"type": "list", "member": {"shape": "TransitGatewayArn"}}, "TransitGatewayAttachmentArn": {"type": "string", "max": 500, "min": 0, "pattern": "[\\s\\S]*"}, "TransitGatewayAttachmentId": {"type": "string", "max": 50, "min": 0, "pattern": "[\\s\\S]*"}, "TransitGatewayConnectPeerArn": {"type": "string", "max": 500, "min": 0, "pattern": "[\\s\\S]*"}, "TransitGatewayConnectPeerArnList": {"type": "list", "member": {"shape": "TransitGatewayConnectPeerArn"}}, "TransitGatewayConnectPeerAssociation": {"type": "structure", "members": {"TransitGatewayConnectPeerArn": {"shape": "TransitGatewayConnectPeerArn", "documentation": "<p>The Amazon Resource Name (ARN) of the transit gateway Connect peer.</p>"}, "GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>"}, "DeviceId": {"shape": "DeviceId", "documentation": "<p>The ID of the device.</p>"}, "LinkId": {"shape": "LinkId", "documentation": "<p>The ID of the link.</p>"}, "State": {"shape": "TransitGatewayConnectPeerAssociationState", "documentation": "<p>The state of the association.</p>"}}, "documentation": "<p>Describes a transit gateway Connect peer association.</p>"}, "TransitGatewayConnectPeerAssociationList": {"type": "list", "member": {"shape": "TransitGatewayConnectPeerAssociation"}}, "TransitGatewayConnectPeerAssociationState": {"type": "string", "enum": ["PENDING", "AVAILABLE", "DELETING", "DELETED"]}, "TransitGatewayPeering": {"type": "structure", "members": {"Peering": {"shape": "Peering", "documentation": "<p>Describes a transit gateway peer connection.</p>"}, "TransitGatewayArn": {"shape": "TransitGatewayArn", "documentation": "<p>The ARN of the transit gateway.</p>"}, "TransitGatewayPeeringAttachmentId": {"shape": "TransitGatewayPeeringAttachmentId", "documentation": "<p>The ID of the transit gateway peering attachment.</p>"}}, "documentation": "<p>Describes a transit gateway peering attachment.</p>"}, "TransitGatewayPeeringAttachmentId": {"type": "string", "max": 50, "min": 0, "pattern": "^tgw-attach-([0-9a-f]{8,17})$"}, "TransitGatewayRegistration": {"type": "structure", "members": {"GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>"}, "TransitGatewayArn": {"shape": "TransitGatewayArn", "documentation": "<p>The Amazon Resource Name (ARN) of the transit gateway.</p>"}, "State": {"shape": "TransitGatewayRegistrationStateReason", "documentation": "<p>The state of the transit gateway registration.</p>"}}, "documentation": "<p>Describes the registration of a transit gateway to a global network.</p>"}, "TransitGatewayRegistrationList": {"type": "list", "member": {"shape": "TransitGatewayRegistration"}}, "TransitGatewayRegistrationState": {"type": "string", "enum": ["PENDING", "AVAILABLE", "DELETING", "DELETED", "FAILED"]}, "TransitGatewayRegistrationStateReason": {"type": "structure", "members": {"Code": {"shape": "TransitGatewayRegistrationState", "documentation": "<p>The code for the state reason.</p>"}, "Message": {"shape": "ConstrainedString", "documentation": "<p>The message for the state reason.</p>"}}, "documentation": "<p>Describes the status of a transit gateway registration.</p>"}, "TransitGatewayRouteTableArn": {"type": "string", "max": 500, "min": 0, "pattern": "[\\s\\S]*"}, "TransitGatewayRouteTableAttachment": {"type": "structure", "members": {"Attachment": {"shape": "Attachment"}, "PeeringId": {"shape": "PeeringId", "documentation": "<p>The ID of the peering attachment.</p>"}, "TransitGatewayRouteTableArn": {"shape": "TransitGatewayRouteTableArn", "documentation": "<p>The ARN of the transit gateway attachment route table. For example, <code>\"TransitGatewayRouteTableArn\": \"arn:aws:ec2:us-west-2:123456789012:transit-gateway-route-table/tgw-rtb-9876543210123456\"</code>.</p>"}}, "documentation": "<p>Describes a transit gateway route table attachment.</p>"}, "TunnelProtocol": {"type": "string", "enum": ["GRE", "NO_ENCAP"]}, "UntagResourceRequest": {"type": "structure", "required": ["ResourceArn", "TagKeys"], "members": {"ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource.</p>", "location": "uri", "locationName": "resourceArn"}, "TagKeys": {"shape": "TagKeyList", "documentation": "<p>The tag keys to remove from the specified resource.</p>", "location": "querystring", "locationName": "tagKeys"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateConnectionRequest": {"type": "structure", "required": ["GlobalNetworkId", "ConnectionId"], "members": {"GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>", "location": "uri", "locationName": "globalNetworkId"}, "ConnectionId": {"shape": "ConnectionId", "documentation": "<p>The ID of the connection.</p>", "location": "uri", "locationName": "connectionId"}, "LinkId": {"shape": "LinkId", "documentation": "<p>The ID of the link for the first device in the connection.</p>"}, "ConnectedLinkId": {"shape": "LinkId", "documentation": "<p>The ID of the link for the second device in the connection.</p>"}, "Description": {"shape": "ConstrainedString", "documentation": "<p>A description of the connection.</p> <p>Length Constraints: Maximum length of 256 characters.</p>"}}}, "UpdateConnectionResponse": {"type": "structure", "members": {"Connection": {"shape": "Connection", "documentation": "<p>Information about the connection.</p>"}}}, "UpdateCoreNetworkRequest": {"type": "structure", "required": ["CoreNetworkId"], "members": {"CoreNetworkId": {"shape": "CoreNetworkId", "documentation": "<p>The ID of a core network.</p>", "location": "uri", "locationName": "coreNetworkId"}, "Description": {"shape": "ConstrainedString", "documentation": "<p>The description of the update.</p>"}}}, "UpdateCoreNetworkResponse": {"type": "structure", "members": {"CoreNetwork": {"shape": "CoreNetwork", "documentation": "<p>Returns information about a core network update.</p>"}}}, "UpdateDeviceRequest": {"type": "structure", "required": ["GlobalNetworkId", "DeviceId"], "members": {"GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>", "location": "uri", "locationName": "globalNetworkId"}, "DeviceId": {"shape": "DeviceId", "documentation": "<p>The ID of the device.</p>", "location": "uri", "locationName": "deviceId"}, "AWSLocation": {"shape": "AWSLocation", "documentation": "<p>The Amazon Web Services location of the device, if applicable. For an on-premises device, you can omit this parameter.</p>"}, "Description": {"shape": "ConstrainedString", "documentation": "<p>A description of the device.</p> <p>Constraints: Maximum length of 256 characters.</p>"}, "Type": {"shape": "ConstrainedString", "documentation": "<p>The type of the device.</p>"}, "Vendor": {"shape": "ConstrainedString", "documentation": "<p>The vendor of the device.</p> <p>Constraints: Maximum length of 128 characters.</p>"}, "Model": {"shape": "ConstrainedString", "documentation": "<p>The model of the device.</p> <p>Constraints: Maximum length of 128 characters.</p>"}, "SerialNumber": {"shape": "ConstrainedString", "documentation": "<p>The serial number of the device.</p> <p>Constraints: Maximum length of 128 characters.</p>"}, "Location": {"shape": "Location"}, "SiteId": {"shape": "SiteId", "documentation": "<p>The ID of the site.</p>"}}}, "UpdateDeviceResponse": {"type": "structure", "members": {"Device": {"shape": "<PERSON><PERSON>", "documentation": "<p>Information about the device.</p>"}}}, "UpdateDirectConnectGatewayAttachmentRequest": {"type": "structure", "required": ["AttachmentId"], "members": {"AttachmentId": {"shape": "AttachmentId", "documentation": "<p>The ID of the Direct Connect gateway attachment for the updated edge locations. </p>", "location": "uri", "locationName": "attachmentId"}, "EdgeLocations": {"shape": "ExternalRegionCodeList", "documentation": "<p>One or more edge locations to update for the Direct Connect gateway attachment. The updated array of edge locations overwrites the previous array of locations. <code>EdgeLocations</code> is only used for Direct Connect gateway attachments.</p>"}}}, "UpdateDirectConnectGatewayAttachmentResponse": {"type": "structure", "members": {"DirectConnectGatewayAttachment": {"shape": "DirectConnectGatewayAttachment", "documentation": "<p>Returns details of the Direct Connect gateway attachment with the updated edge locations.</p>"}}}, "UpdateGlobalNetworkRequest": {"type": "structure", "required": ["GlobalNetworkId"], "members": {"GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of your global network.</p>", "location": "uri", "locationName": "globalNetworkId"}, "Description": {"shape": "ConstrainedString", "documentation": "<p>A description of the global network.</p> <p>Constraints: Maximum length of 256 characters.</p>"}}}, "UpdateGlobalNetworkResponse": {"type": "structure", "members": {"GlobalNetwork": {"shape": "GlobalNetwork", "documentation": "<p>Information about the global network object.</p>"}}}, "UpdateLinkRequest": {"type": "structure", "required": ["GlobalNetworkId", "LinkId"], "members": {"GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>", "location": "uri", "locationName": "globalNetworkId"}, "LinkId": {"shape": "LinkId", "documentation": "<p>The ID of the link.</p>", "location": "uri", "locationName": "linkId"}, "Description": {"shape": "ConstrainedString", "documentation": "<p>A description of the link.</p> <p>Constraints: Maximum length of 256 characters.</p>"}, "Type": {"shape": "ConstrainedString", "documentation": "<p>The type of the link.</p> <p>Constraints: Maximum length of 128 characters.</p>"}, "Bandwidth": {"shape": "Bandwidth", "documentation": "<p>The upload and download speed in Mbps. </p>"}, "Provider": {"shape": "ConstrainedString", "documentation": "<p>The provider of the link.</p> <p>Constraints: Maximum length of 128 characters.</p>"}}}, "UpdateLinkResponse": {"type": "structure", "members": {"Link": {"shape": "Link", "documentation": "<p>Information about the link.</p>"}}}, "UpdateNetworkResourceMetadataRequest": {"type": "structure", "required": ["GlobalNetworkId", "ResourceArn", "<PERSON><PERSON><PERSON>"], "members": {"GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>", "location": "uri", "locationName": "globalNetworkId"}, "ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The ARN of the resource.</p>", "location": "uri", "locationName": "resourceArn"}, "Metadata": {"shape": "NetworkResourceMetadataMap", "documentation": "<p>The resource metadata.</p>"}}}, "UpdateNetworkResourceMetadataResponse": {"type": "structure", "members": {"ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The ARN of the resource.</p>"}, "Metadata": {"shape": "NetworkResourceMetadataMap", "documentation": "<p>The updated resource metadata.</p>"}}}, "UpdateSiteRequest": {"type": "structure", "required": ["GlobalNetworkId", "SiteId"], "members": {"GlobalNetworkId": {"shape": "GlobalNetworkId", "documentation": "<p>The ID of the global network.</p>", "location": "uri", "locationName": "globalNetworkId"}, "SiteId": {"shape": "SiteId", "documentation": "<p>The ID of your site.</p>", "location": "uri", "locationName": "siteId"}, "Description": {"shape": "ConstrainedString", "documentation": "<p>A description of your site.</p> <p>Constraints: Maximum length of 256 characters.</p>"}, "Location": {"shape": "Location", "documentation": "<p>The site location:</p> <ul> <li> <p> <code>Address</code>: The physical address of the site.</p> </li> <li> <p> <code>Latitude</code>: The latitude of the site. </p> </li> <li> <p> <code>Longitude</code>: The longitude of the site.</p> </li> </ul>"}}}, "UpdateSiteResponse": {"type": "structure", "members": {"Site": {"shape": "Site", "documentation": "<p>Information about the site.</p>"}}}, "UpdateVpcAttachmentRequest": {"type": "structure", "required": ["AttachmentId"], "members": {"AttachmentId": {"shape": "AttachmentId", "documentation": "<p>The ID of the attachment.</p>", "location": "uri", "locationName": "attachmentId"}, "AddSubnetArns": {"shape": "SubnetArnList", "documentation": "<p>Adds a subnet ARN to the VPC attachment.</p>"}, "RemoveSubnetArns": {"shape": "SubnetArnList", "documentation": "<p>Removes a subnet ARN from the attachment.</p>"}, "Options": {"shape": "VpcOptions", "documentation": "<p>Additional options for updating the VPC attachment. </p>"}}}, "UpdateVpcAttachmentResponse": {"type": "structure", "members": {"VpcAttachment": {"shape": "VpcAttachment", "documentation": "<p>Describes the updated VPC attachment.</p>"}}}, "ValidationException": {"type": "structure", "required": ["Message"], "members": {"Message": {"shape": "ServerSideString"}, "Reason": {"shape": "ValidationExceptionReason", "documentation": "<p>The reason for the error.</p>"}, "Fields": {"shape": "ValidationExceptionFieldList", "documentation": "<p>The fields that caused the error, if applicable.</p>"}}, "documentation": "<p>The input fails to satisfy the constraints.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "ValidationExceptionField": {"type": "structure", "required": ["Name", "Message"], "members": {"Name": {"shape": "ServerSideString", "documentation": "<p>The name of the field.</p>"}, "Message": {"shape": "ServerSideString", "documentation": "<p>The message for the field.</p>"}}, "documentation": "<p>Describes a validation exception for a field.</p>"}, "ValidationExceptionFieldList": {"type": "list", "member": {"shape": "ValidationExceptionField"}}, "ValidationExceptionReason": {"type": "string", "enum": ["UnknownOperation", "CannotParse", "FieldValidationFailed", "Other"]}, "Via": {"type": "structure", "members": {"NetworkFunctionGroups": {"shape": "NetworkFunctionGroupList", "documentation": "<p>The list of network function groups associated with the service insertion action.</p>"}, "WithEdgeOverrides": {"shape": "WithEdgeOverridesList", "documentation": "<p>Describes any edge overrides. An edge override is a specific edge to be used for traffic.</p>"}}, "documentation": "<p>The list of network function groups and edge overrides for the service insertion action. Used for both the <code>send-to</code> and <code>send-via</code> actions.</p>"}, "VpcArn": {"type": "string", "max": 500, "min": 0, "pattern": "^arn:[^:]{1,63}:ec2:[^:]{0,63}:[^:]{0,63}:vpc\\/vpc-[0-9a-f]{8,17}$"}, "VpcAttachment": {"type": "structure", "members": {"Attachment": {"shape": "Attachment", "documentation": "<p>Provides details about the VPC attachment.</p>"}, "SubnetArns": {"shape": "SubnetArnList", "documentation": "<p>The subnet ARNs.</p>"}, "Options": {"shape": "VpcOptions", "documentation": "<p>Provides details about the VPC attachment.</p>"}}, "documentation": "<p>Describes a VPC attachment.</p>"}, "VpcOptions": {"type": "structure", "members": {"Ipv6Support": {"shape": "Boolean", "documentation": "<p>Indicates whether IPv6 is supported.</p>"}, "ApplianceModeSupport": {"shape": "Boolean", "documentation": "<p>Indicates whether appliance mode is supported. If enabled, traffic flow between a source and destination use the same Availability Zone for the VPC attachment for the lifetime of that flow. The default value is <code>false</code>.</p>"}, "DnsSupport": {"shape": "Boolean", "documentation": "<p>Indicates whether DNS is supported.</p>"}, "SecurityGroupReferencingSupport": {"shape": "Boolean", "documentation": "<p>Indicates whether security group referencing is enabled for this VPC attachment. The default is <code>true</code>. However, at the core network policy-level the default is set to <code>false</code>.</p>"}}, "documentation": "<p>Describes the VPC options.</p>"}, "VpnConnectionArn": {"type": "string", "max": 500, "min": 0, "pattern": "^arn:[^:]{1,63}:ec2:[^:]{0,63}:[^:]{0,63}:vpn-connection\\/vpn-[0-9a-f]{8,17}$"}, "WhenSentTo": {"type": "structure", "members": {"WhenSentToSegmentsList": {"shape": "WhenSentToSegmentsList", "documentation": "<p>The list of destination segments when the service insertion action is <code>send-to</code>.</p>"}}, "documentation": "<p>Displays a list of the destination segments. Used only when the service insertion action is <code>send-to</code>. </p>"}, "WhenSentToSegmentsList": {"type": "list", "member": {"shape": "ConstrainedString"}}, "WithEdgeOverridesList": {"type": "list", "member": {"shape": "EdgeOverride"}}}, "documentation": "<p>Amazon Web Services enables you to centrally manage your Amazon Web Services Cloud WAN core network and your Transit Gateway network across Amazon Web Services accounts, Regions, and on-premises locations.</p>"}