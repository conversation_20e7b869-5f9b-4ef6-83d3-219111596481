{"version": "2.0", "metadata": {"apiVersion": "2020-07-20", "auth": ["aws.auth#sigv4"], "endpointPrefix": "sso", "jsonVersion": "1.1", "protocol": "json", "protocols": ["json"], "serviceAbbreviation": "SSO Admin", "serviceFullName": "AWS Single Sign-On Admin", "serviceId": "SSO Admin", "signatureVersion": "v4", "signingName": "sso", "targetPrefix": "SWBExternalService", "uid": "sso-admin-2020-07-20"}, "operations": {"AttachCustomerManagedPolicyReferenceToPermissionSet": {"name": "AttachCustomerManagedPolicyReferenceToPermissionSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AttachCustomerManagedPolicyReferenceToPermissionSetRequest"}, "output": {"shape": "AttachCustomerManagedPolicyReferenceToPermissionSetResponse"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Attaches the specified customer managed policy to the specified <a>PermissionSet</a>.</p>"}, "AttachManagedPolicyToPermissionSet": {"name": "AttachManagedPolicyToPermissionSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AttachManagedPolicyToPermissionSetRequest"}, "output": {"shape": "AttachManagedPolicyToPermissionSetResponse"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Attaches an Amazon Web Services managed policy ARN to a permission set.</p> <note> <p>If the permission set is already referenced by one or more account assignments, you will need to call <code> <a>ProvisionPermissionSet</a> </code> after this operation. Calling <code>ProvisionPermissionSet</code> applies the corresponding IAM policy updates to all assigned accounts.</p> </note>"}, "CreateAccountAssignment": {"name": "CreateAccountAssignment", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateAccountAssignmentRequest"}, "output": {"shape": "CreateAccountAssignmentResponse"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Assigns access to a principal for a specified Amazon Web Services account using a specified permission set.</p> <note> <p>The term <i>principal</i> here refers to a user or group that is defined in IAM Identity Center.</p> </note> <note> <p>As part of a successful <code>CreateAccountAssignment</code> call, the specified permission set will automatically be provisioned to the account in the form of an IAM policy. That policy is attached to the IAM role created in IAM Identity Center. If the permission set is subsequently updated, the corresponding IAM policies attached to roles in your accounts will not be updated automatically. In this case, you must call <code> <a>ProvisionPermissionSet</a> </code> to make these updates.</p> </note> <note> <p> After a successful response, call <code>DescribeAccountAssignmentCreationStatus</code> to describe the status of an assignment creation request. </p> </note>"}, "CreateApplication": {"name": "CreateApplication", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateApplicationRequest"}, "output": {"shape": "CreateApplicationResponse"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Creates an OAuth 2.0 customer managed application in IAM Identity Center for the given application provider.</p> <note> <p>This API does not support creating SAML 2.0 customer managed applications or Amazon Web Services managed applications. To learn how to create an Amazon Web Services managed application, see the application user guide. You can create a SAML 2.0 customer managed application in the Amazon Web Services Management Console only. See <a href=\"https://docs.aws.amazon.com/singlesignon/latest/userguide/customermanagedapps-saml2-setup.html\">Setting up customer managed SAML 2.0 applications</a>. For more information on these application types, see <a href=\"https://docs.aws.amazon.com/singlesignon/latest/userguide/awsapps.html\">Amazon Web Services managed applications</a>.</p> </note>"}, "CreateApplicationAssignment": {"name": "CreateApplicationAssignment", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateApplicationAssignmentRequest"}, "output": {"shape": "CreateApplicationAssignmentResponse"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Grant application access to a user or group.</p>"}, "CreateInstance": {"name": "CreateInstance", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateInstanceRequest"}, "output": {"shape": "CreateInstanceResponse"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Creates an instance of IAM Identity Center for a standalone Amazon Web Services account that is not managed by Organizations or a member Amazon Web Services account in an organization. You can create only one instance per account and across all Amazon Web Services Regions.</p> <p>The CreateInstance request is rejected if the following apply: </p> <ul> <li> <p>The instance is created within the organization management account.</p> </li> <li> <p>An instance already exists in the same account.</p> </li> </ul>"}, "CreateInstanceAccessControlAttributeConfiguration": {"name": "CreateInstanceAccessControlAttributeConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateInstanceAccessControlAttributeConfigurationRequest"}, "output": {"shape": "CreateInstanceAccessControlAttributeConfigurationResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Enables the attributes-based access control (ABAC) feature for the specified IAM Identity Center instance. You can also specify new attributes to add to your ABAC configuration during the enabling process. For more information about ABAC, see <a href=\"/singlesignon/latest/userguide/abac.html\">Attribute-Based Access Control</a> in the <i>IAM Identity Center User Guide</i>.</p> <note> <p>After a successful response, call <code>DescribeInstanceAccessControlAttributeConfiguration</code> to validate that <code>InstanceAccessControlAttributeConfiguration</code> was created.</p> </note>"}, "CreatePermissionSet": {"name": "CreatePermissionSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreatePermissionSetRequest"}, "output": {"shape": "CreatePermissionSetResponse"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Creates a permission set within a specified IAM Identity Center instance.</p> <note> <p>To grant users and groups access to Amazon Web Services account resources, use <code> <a>CreateAccountAssignment</a> </code>.</p> </note>"}, "CreateTrustedTokenIssuer": {"name": "CreateTrusted<PERSON><PERSON><PERSON><PERSON><PERSON>", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateTrustedTokenIssuerRequest"}, "output": {"shape": "CreateTrustedTokenIssuerResponse"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Creates a connection to a trusted token issuer in an instance of IAM Identity Center. A trusted token issuer enables trusted identity propagation to be used with applications that authenticate outside of Amazon Web Services.</p> <p>This trusted token issuer describes an external identity provider (IdP) that can generate claims or assertions in the form of access tokens for a user. Applications enabled for IAM Identity Center can use these tokens for authentication. </p>"}, "DeleteAccountAssignment": {"name": "DeleteAccountAssignment", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteAccountAssignmentRequest"}, "output": {"shape": "DeleteAccountAssignmentResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Deletes a principal's access from a specified Amazon Web Services account using a specified permission set.</p> <note> <p>After a successful response, call <code>DescribeAccountAssignmentDeletionStatus</code> to describe the status of an assignment deletion request.</p> </note>"}, "DeleteApplication": {"name": "DeleteApplication", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteApplicationRequest"}, "output": {"shape": "DeleteApplicationResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Deletes the association with the application. The connected service resource still exists.</p>"}, "DeleteApplicationAccessScope": {"name": "DeleteApplicationAccessScope", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteApplicationAccessScopeRequest"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Deletes an IAM Identity Center access scope from an application.</p>", "idempotent": true}, "DeleteApplicationAssignment": {"name": "DeleteApplicationAssignment", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteApplicationAssignmentRequest"}, "output": {"shape": "DeleteApplicationAssignmentResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Revoke application access to an application by deleting application assignments for a user or group.</p>", "idempotent": true}, "DeleteApplicationAuthenticationMethod": {"name": "DeleteApplicationAuthenticationMethod", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteApplicationAuthenticationMethodRequest"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Deletes an authentication method from an application.</p>", "idempotent": true}, "DeleteApplicationGrant": {"name": "DeleteApplicationGrant", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteApplicationGrantRequest"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Deletes a grant from an application.</p>", "idempotent": true}, "DeleteInlinePolicyFromPermissionSet": {"name": "DeleteInlinePolicyFromPermissionSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteInlinePolicyFromPermissionSetRequest"}, "output": {"shape": "DeleteInlinePolicyFromPermissionSetResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Deletes the inline policy from a specified permission set.</p>"}, "DeleteInstance": {"name": "DeleteInstance", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteInstanceRequest"}, "output": {"shape": "DeleteInstanceResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Deletes the instance of IAM Identity Center. Only the account that owns the instance can call this API. Neither the delegated administrator nor member account can delete the organization instance, but those roles can delete their own instance.</p>"}, "DeleteInstanceAccessControlAttributeConfiguration": {"name": "DeleteInstanceAccessControlAttributeConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteInstanceAccessControlAttributeConfigurationRequest"}, "output": {"shape": "DeleteInstanceAccessControlAttributeConfigurationResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Disables the attributes-based access control (ABAC) feature for the specified IAM Identity Center instance and deletes all of the attribute mappings that have been configured. Once deleted, any attributes that are received from an identity source and any custom attributes you have previously configured will not be passed. For more information about ABAC, see <a href=\"/singlesignon/latest/userguide/abac.html\">Attribute-Based Access Control</a> in the <i>IAM Identity Center User Guide</i>.</p>"}, "DeletePermissionSet": {"name": "DeletePermissionSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeletePermissionSetRequest"}, "output": {"shape": "DeletePermissionSetResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Deletes the specified permission set.</p>"}, "DeletePermissionsBoundaryFromPermissionSet": {"name": "DeletePermissionsBoundaryFromPermissionSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeletePermissionsBoundaryFromPermissionSetRequest"}, "output": {"shape": "DeletePermissionsBoundaryFromPermissionSetResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Deletes the permissions boundary from a specified <a>PermissionSet</a>.</p>"}, "DeleteTrustedTokenIssuer": {"name": "DeleteTrusted<PERSON><PERSON><PERSON><PERSON><PERSON>", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteTrustedTokenIssuerRequest"}, "output": {"shape": "DeleteTrustedTokenIssuerResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Deletes a trusted token issuer configuration from an instance of IAM Identity Center.</p> <note> <p>Deleting this trusted token issuer configuration will cause users to lose access to any applications that are configured to use the trusted token issuer.</p> </note>"}, "DescribeAccountAssignmentCreationStatus": {"name": "DescribeAccountAssignmentCreationStatus", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeAccountAssignmentCreationStatusRequest"}, "output": {"shape": "DescribeAccountAssignmentCreationStatusResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Describes the status of the assignment creation request.</p>"}, "DescribeAccountAssignmentDeletionStatus": {"name": "DescribeAccountAssignmentDeletionStatus", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeAccountAssignmentDeletionStatusRequest"}, "output": {"shape": "DescribeAccountAssignmentDeletionStatusResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Describes the status of the assignment deletion request.</p>"}, "DescribeApplication": {"name": "DescribeApplication", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeApplicationRequest"}, "output": {"shape": "DescribeApplicationResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Retrieves the details of an application associated with an instance of IAM Identity Center.</p>"}, "DescribeApplicationAssignment": {"name": "DescribeApplicationAssignment", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeApplicationAssignmentRequest"}, "output": {"shape": "DescribeApplicationAssignmentResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Retrieves a direct assignment of a user or group to an application. If the user doesn’t have a direct assignment to the application, the user may still have access to the application through a group. Therefore, don’t use this API to test access to an application for a user. Instead use <a>ListApplicationAssignmentsForPrincipal</a>.</p>"}, "DescribeApplicationProvider": {"name": "DescribeApplicationProvider", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeApplicationProviderRequest"}, "output": {"shape": "DescribeApplicationProviderResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Retrieves details about a provider that can be used to connect an Amazon Web Services managed application or customer managed application to IAM Identity Center.</p>"}, "DescribeInstance": {"name": "DescribeInstance", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeInstanceRequest"}, "output": {"shape": "DescribeInstanceResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Returns the details of an instance of IAM Identity Center. The status can be one of the following:</p> <ul> <li> <p> <code>CREATE_IN_PROGRESS</code> - The instance is in the process of being created. When the instance is ready for use, DescribeInstance returns the status of <code>ACTIVE</code>. While the instance is in the <code>CREATE_IN_PROGRESS</code> state, you can call only DescribeInstance and DeleteInstance operations.</p> </li> <li> <p> <code>DELETE_IN_PROGRESS</code> - The instance is being deleted. Returns <code>AccessDeniedException</code> after the delete operation completes. </p> </li> <li> <p> <code>ACTIVE</code> - The instance is active.</p> </li> </ul>"}, "DescribeInstanceAccessControlAttributeConfiguration": {"name": "DescribeInstanceAccessControlAttributeConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeInstanceAccessControlAttributeConfigurationRequest"}, "output": {"shape": "DescribeInstanceAccessControlAttributeConfigurationResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Returns the list of IAM Identity Center identity store attributes that have been configured to work with attributes-based access control (ABAC) for the specified IAM Identity Center instance. This will not return attributes configured and sent by an external identity provider. For more information about ABAC, see <a href=\"/singlesignon/latest/userguide/abac.html\">Attribute-Based Access Control</a> in the <i>IAM Identity Center User Guide</i>.</p>"}, "DescribePermissionSet": {"name": "DescribePermissionSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribePermissionSetRequest"}, "output": {"shape": "DescribePermissionSetResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Gets the details of the permission set.</p>"}, "DescribePermissionSetProvisioningStatus": {"name": "DescribePermissionSetProvisioningStatus", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribePermissionSetProvisioningStatusRequest"}, "output": {"shape": "DescribePermissionSetProvisioningStatusResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Describes the status for the given permission set provisioning request.</p>"}, "DescribeTrustedTokenIssuer": {"name": "DescribeTrusted<PERSON><PERSON><PERSON><PERSON><PERSON>", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeTrustedTokenIssuerRequest"}, "output": {"shape": "DescribeTrustedTokenIssuerResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Retrieves details about a trusted token issuer configuration stored in an instance of IAM Identity Center. Details include the name of the trusted token issuer, the issuer URL, and the path of the source attribute and the destination attribute for a trusted token issuer configuration. </p>"}, "DetachCustomerManagedPolicyReferenceFromPermissionSet": {"name": "DetachCustomerManagedPolicyReferenceFromPermissionSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DetachCustomerManagedPolicyReferenceFromPermissionSetRequest"}, "output": {"shape": "DetachCustomerManagedPolicyReferenceFromPermissionSetResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Detaches the specified customer managed policy from the specified <a>PermissionSet</a>.</p>"}, "DetachManagedPolicyFromPermissionSet": {"name": "DetachManagedPolicyFromPermissionSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DetachManagedPolicyFromPermissionSetRequest"}, "output": {"shape": "DetachManagedPolicyFromPermissionSetResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Detaches the attached Amazon Web Services managed policy ARN from the specified permission set.</p>"}, "GetApplicationAccessScope": {"name": "GetApplicationAccessScope", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetApplicationAccessScopeRequest"}, "output": {"shape": "GetApplicationAccessScopeResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Retrieves the authorized targets for an IAM Identity Center access scope for an application.</p>"}, "GetApplicationAssignmentConfiguration": {"name": "GetApplicationAssignmentConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetApplicationAssignmentConfigurationRequest"}, "output": {"shape": "GetApplicationAssignmentConfigurationResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Retrieves the configuration of <a>PutApplicationAssignmentConfiguration</a>.</p>"}, "GetApplicationAuthenticationMethod": {"name": "GetApplicationAuthenticationMethod", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetApplicationAuthenticationMethodRequest"}, "output": {"shape": "GetApplicationAuthenticationMethodResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Retrieves details about an authentication method used by an application.</p>"}, "GetApplicationGrant": {"name": "GetApplicationGrant", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetApplicationGrantRequest"}, "output": {"shape": "GetApplicationGrantResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Retrieves details about an application grant.</p>"}, "GetInlinePolicyForPermissionSet": {"name": "GetInlinePolicyForPermissionSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetInlinePolicyForPermissionSetRequest"}, "output": {"shape": "GetInlinePolicyForPermissionSetResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Obtains the inline policy assigned to the permission set.</p>"}, "GetPermissionsBoundaryForPermissionSet": {"name": "GetPermissionsBoundaryForPermissionSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetPermissionsBoundaryForPermissionSetRequest"}, "output": {"shape": "GetPermissionsBoundaryForPermissionSetResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Obtains the permissions boundary for a specified <a>PermissionSet</a>.</p>"}, "ListAccountAssignmentCreationStatus": {"name": "ListAccountAssignmentCreationStatus", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListAccountAssignmentCreationStatusRequest"}, "output": {"shape": "ListAccountAssignmentCreationStatusResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists the status of the Amazon Web Services account assignment creation requests for a specified IAM Identity Center instance.</p>"}, "ListAccountAssignmentDeletionStatus": {"name": "ListAccountAssignmentDeletionStatus", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListAccountAssignmentDeletionStatusRequest"}, "output": {"shape": "ListAccountAssignmentDeletionStatusResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists the status of the Amazon Web Services account assignment deletion requests for a specified IAM Identity Center instance.</p>"}, "ListAccountAssignments": {"name": "ListAccountAssignments", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListAccountAssignmentsRequest"}, "output": {"shape": "ListAccountAssignmentsResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists the assignee of the specified Amazon Web Services account with the specified permission set.</p>"}, "ListAccountAssignmentsForPrincipal": {"name": "ListAccountAssignmentsForPrincipal", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListAccountAssignmentsForPrincipalRequest"}, "output": {"shape": "ListAccountAssignmentsForPrincipalResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Retrieves a list of the IAM Identity Center associated Amazon Web Services accounts that the principal has access to. This action must be called from the management account containing your organization instance of IAM Identity Center. This action is not valid for account instances of IAM Identity Center.</p>"}, "ListAccountsForProvisionedPermissionSet": {"name": "ListAccountsForProvisionedPermissionSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListAccountsForProvisionedPermissionSetRequest"}, "output": {"shape": "ListAccountsForProvisionedPermissionSetResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists all the Amazon Web Services accounts where the specified permission set is provisioned.</p>"}, "ListApplicationAccessScopes": {"name": "ListApplicationAccessScopes", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListApplicationAccessScopesRequest"}, "output": {"shape": "ListApplicationAccessScopesResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists the access scopes and authorized targets associated with an application.</p>"}, "ListApplicationAssignments": {"name": "ListApplicationAssignments", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListApplicationAssignmentsRequest"}, "output": {"shape": "ListApplicationAssignmentsResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists Amazon Web Services account users that are assigned to an application.</p>"}, "ListApplicationAssignmentsForPrincipal": {"name": "ListApplicationAssignmentsForPrincipal", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListApplicationAssignmentsForPrincipalRequest"}, "output": {"shape": "ListApplicationAssignmentsForPrincipalResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists the applications to which a specified principal is assigned. You must provide a filter when calling this action from a member account against your organization instance of IAM Identity Center. A filter is not required when called from the management account against an organization instance of IAM Identity Center, or from a member account against an account instance of IAM Identity Center in the same account.</p>"}, "ListApplicationAuthenticationMethods": {"name": "ListApplicationAuthenticationMethods", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListApplicationAuthenticationMethodsRequest"}, "output": {"shape": "ListApplicationAuthenticationMethodsResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists all of the authentication methods supported by the specified application.</p>"}, "ListApplicationGrants": {"name": "ListApplicationGrants", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListApplicationGrantsRequest"}, "output": {"shape": "ListApplicationGrantsResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>List the grants associated with an application.</p>"}, "ListApplicationProviders": {"name": "ListApplicationProviders", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListApplicationProvidersRequest"}, "output": {"shape": "ListApplicationProvidersResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists the application providers configured in the IAM Identity Center identity store.</p>"}, "ListApplications": {"name": "ListApplications", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListApplicationsRequest"}, "output": {"shape": "ListApplicationsResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists all applications associated with the instance of IAM Identity Center. When listing applications for an organization instance in the management account, member accounts must use the <code>applicationAccount</code> parameter to filter the list to only applications created from that account. When listing applications for an account instance in the same member account, a filter is not required.</p>"}, "ListCustomerManagedPolicyReferencesInPermissionSet": {"name": "ListCustomerManagedPolicyReferencesInPermissionSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListCustomerManagedPolicyReferencesInPermissionSetRequest"}, "output": {"shape": "ListCustomerManagedPolicyReferencesInPermissionSetResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists all customer managed policies attached to a specified <a>PermissionSet</a>.</p>"}, "ListInstances": {"name": "ListInstances", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListInstancesRequest"}, "output": {"shape": "ListInstancesResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists the details of the organization and account instances of IAM Identity Center that were created in or visible to the account calling this API. </p>"}, "ListManagedPoliciesInPermissionSet": {"name": "ListManagedPoliciesInPermissionSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListManagedPoliciesInPermissionSetRequest"}, "output": {"shape": "ListManagedPoliciesInPermissionSetResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists the Amazon Web Services managed policy that is attached to a specified permission set.</p>"}, "ListPermissionSetProvisioningStatus": {"name": "ListPermissionSetProvisioningStatus", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListPermissionSetProvisioningStatusRequest"}, "output": {"shape": "ListPermissionSetProvisioningStatusResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists the status of the permission set provisioning requests for a specified IAM Identity Center instance.</p>"}, "ListPermissionSets": {"name": "ListPermissionSets", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListPermissionSetsRequest"}, "output": {"shape": "ListPermissionSetsResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists the <a>PermissionSet</a>s in an IAM Identity Center instance.</p>"}, "ListPermissionSetsProvisionedToAccount": {"name": "ListPermissionSetsProvisionedToAccount", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListPermissionSetsProvisionedToAccountRequest"}, "output": {"shape": "ListPermissionSetsProvisionedToAccountResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists all the permission sets that are provisioned to a specified Amazon Web Services account.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists the tags that are attached to a specified resource.</p>"}, "ListTrustedTokenIssuers": {"name": "ListTrustedTokenIssuers", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTrustedTokenIssuersRequest"}, "output": {"shape": "ListTrustedTokenIssuersResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}], "documentation": "<p>Lists all the trusted token issuers configured in an instance of IAM Identity Center.</p>"}, "ProvisionPermissionSet": {"name": "ProvisionPermissionSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ProvisionPermissionSetRequest"}, "output": {"shape": "ProvisionPermissionSetResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>The process by which a specified permission set is provisioned to the specified target.</p>"}, "PutApplicationAccessScope": {"name": "PutApplicationAccessScope", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutApplicationAccessScopeRequest"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Adds or updates the list of authorized targets for an IAM Identity Center access scope for an application.</p>", "idempotent": true}, "PutApplicationAssignmentConfiguration": {"name": "PutApplicationAssignmentConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutApplicationAssignmentConfigurationRequest"}, "output": {"shape": "PutApplicationAssignmentConfigurationResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Configure how users gain access to an application. If <code>AssignmentsRequired</code> is <code>true</code> (default value), users don’t have access to the application unless an assignment is created using the <a href=\"https://docs.aws.amazon.com/singlesignon/latest/APIReference/API_CreateApplicationAssignment.html\">CreateApplicationAssignment API</a>. If <code>false</code>, all users have access to the application. If an assignment is created using <a href=\"https://docs.aws.amazon.com/singlesignon/latest/APIReference/API_CreateApplicationAssignment.html\">CreateApplicationAssignment</a>., the user retains access if <code>AssignmentsRequired</code> is set to <code>true</code>. </p>", "idempotent": true}, "PutApplicationAuthenticationMethod": {"name": "PutApplicationAuthenticationMethod", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutApplicationAuthenticationMethodRequest"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Adds or updates an authentication method for an application.</p>", "idempotent": true}, "PutApplicationGrant": {"name": "PutApplicationGrant", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutApplicationGrantRequest"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Creates a configuration for an application to use grants. Conceptually grants are authorization to request actions related to tokens. This configuration will be used when parties are requesting and receiving tokens during the trusted identity propagation process. For more information on the IAM Identity Center supported grant workflows, see <a href=\"https://docs.aws.amazon.com/singlesignon/latest/userguide/customermanagedapps-saml2-oauth2.html\">SAML 2.0 and OAuth 2.0</a>.</p> <p>A grant is created between your applications and Identity Center instance which enables an application to use specified mechanisms to obtain tokens. These tokens are used by your applications to gain access to Amazon Web Services resources on behalf of users. The following elements are within these exchanges:</p> <ul> <li> <p> <b>Requester</b> - The application requesting access to Amazon Web Services resources.</p> </li> <li> <p> <b>Subject</b> - Typically the user that is requesting access to Amazon Web Services resources.</p> </li> <li> <p> <b>Grant</b> - Conceptually, a grant is authorization to access Amazon Web Services resources. These grants authorize token generation for authenticating access to the requester and for the request to make requests on behalf of the subjects. There are four types of grants:</p> <ul> <li> <p> <b>AuthorizationCode</b> - Allows an application to request authorization through a series of user-agent redirects.</p> </li> <li> <p> <b>JWT bearer </b> - Authorizes an application to exchange a JSON Web Token that came from an external identity provider. To learn more, see <a href=\"https://datatracker.ietf.org/doc/html/rfc6749\">RFC 6479</a>.</p> </li> <li> <p> <b>Refresh token</b> - Enables application to request new access tokens to replace expiring or expired access tokens.</p> </li> <li> <p> <b>Exchange token</b> - A grant that requests tokens from the authorization server by providing a ‘subject’ token with access scope authorizing trusted identity propagation to this application. To learn more, see <a href=\"https://datatracker.ietf.org/doc/html/rfc8693\">RFC 8693</a>.</p> </li> </ul> </li> <li> <p> <b>Authorization server</b> - IAM Identity Center requests tokens.</p> </li> </ul> <p>User credentials are never shared directly within these exchanges. Instead, applications use grants to request access tokens from IAM Identity Center. For more information, see <a href=\"https://datatracker.ietf.org/doc/html/rfc6749\">RFC 6479</a>.</p> <p class=\"title\"> <b>Use cases</b> </p> <ul> <li> <p>Connecting to custom applications.</p> </li> <li> <p>Configuring an Amazon Web Services service to make calls to another Amazon Web Services services using JWT tokens.</p> </li> </ul>", "idempotent": true}, "PutInlinePolicyToPermissionSet": {"name": "PutInlinePolicyToPermissionSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutInlinePolicyToPermissionSetRequest"}, "output": {"shape": "PutInlinePolicyToPermissionSetResponse"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Attaches an inline policy to a permission set.</p> <note> <p>If the permission set is already referenced by one or more account assignments, you will need to call <code> <a>ProvisionPermissionSet</a> </code> after this action to apply the corresponding IAM policy updates to all assigned accounts.</p> </note>"}, "PutPermissionsBoundaryToPermissionSet": {"name": "PutPermissionsBoundaryToPermissionSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutPermissionsBoundaryToPermissionSetRequest"}, "output": {"shape": "PutPermissionsBoundaryToPermissionSetResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Attaches an Amazon Web Services managed or customer managed policy to the specified <a>PermissionSet</a> as a permissions boundary.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Associates a set of tags with a specified resource.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Disassociates a set of tags from a specified resource.</p>"}, "UpdateApplication": {"name": "UpdateApplication", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateApplicationRequest"}, "output": {"shape": "UpdateApplicationResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Updates application properties. </p>"}, "UpdateInstance": {"name": "UpdateInstance", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateInstanceRequest"}, "output": {"shape": "UpdateInstanceResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Update the details for the instance of IAM Identity Center that is owned by the Amazon Web Services account.</p>"}, "UpdateInstanceAccessControlAttributeConfiguration": {"name": "UpdateInstanceAccessControlAttributeConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateInstanceAccessControlAttributeConfigurationRequest"}, "output": {"shape": "UpdateInstanceAccessControlAttributeConfigurationResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Updates the IAM Identity Center identity store attributes that you can use with the IAM Identity Center instance for attributes-based access control (ABAC). When using an external identity provider as an identity source, you can pass attributes through the SAML assertion as an alternative to configuring attributes from the IAM Identity Center identity store. If a SAML assertion passes any of these attributes, IAM Identity Center replaces the attribute value with the value from the IAM Identity Center identity store. For more information about ABAC, see <a href=\"/singlesignon/latest/userguide/abac.html\">Attribute-Based Access Control</a> in the <i>IAM Identity Center User Guide</i>.</p>"}, "UpdatePermissionSet": {"name": "UpdatePermissionSet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdatePermissionSetRequest"}, "output": {"shape": "UpdatePermissionSetResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Updates an existing permission set.</p>"}, "UpdateTrustedTokenIssuer": {"name": "UpdateTrusted<PERSON><PERSON><PERSON><PERSON><PERSON>", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateTrustedTokenIssuerRequest"}, "output": {"shape": "UpdateTrustedTokenIssuerResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Updates the name of the trusted token issuer, or the path of a source attribute or destination attribute for a trusted token issuer configuration.</p> <note> <p>Updating this trusted token issuer configuration might cause users to lose access to any applications that are configured to use the trusted token issuer.</p> </note>"}}, "shapes": {"AccessControlAttribute": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {"shape": "AccessControlAttributeKey", "documentation": "<p>The name of the attribute associated with your identities in your identity source. This is used to map a specified attribute in your identity source with an attribute in IAM Identity Center.</p>"}, "Value": {"shape": "AccessControlAttributeValue", "documentation": "<p>The value used for mapping a specified attribute to an identity source.</p>"}}, "documentation": "<p>These are IAM Identity Center identity store attributes that you can configure for use in attributes-based access control (ABAC). You can create permissions policies that determine who can access your Amazon Web Services resources based upon the configured attribute values. When you enable ABAC and specify <code>AccessControlAttributes</code>, IAM Identity Center passes the attribute values of the authenticated user into IAM for use in policy evaluation.</p>"}, "AccessControlAttributeKey": {"type": "string", "max": 128, "min": 1, "pattern": "[\\p{L}\\p{Z}\\p{N}_.:\\/=+\\-@]+"}, "AccessControlAttributeList": {"type": "list", "member": {"shape": "AccessControlAttribute"}, "max": 50, "min": 0}, "AccessControlAttributeValue": {"type": "structure", "required": ["Source"], "members": {"Source": {"shape": "AccessControlAttributeValueSourceList", "documentation": "<p>The identity source to use when mapping a specified attribute to IAM Identity Center.</p>"}}, "documentation": "<p>The value used for mapping a specified attribute to an identity source. For more information, see <a href=\"https://docs.aws.amazon.com/singlesignon/latest/userguide/attributemappingsconcept.html\">Attribute mappings</a> in the <i>IAM Identity Center User Guide</i>.</p>"}, "AccessControlAttributeValueSource": {"type": "string", "max": 256, "min": 0, "pattern": "[\\p{L}\\p{Z}\\p{N}_.:\\/=+\\-@\\[\\]\\{\\}\\$\\\\\"]*"}, "AccessControlAttributeValueSourceList": {"type": "list", "member": {"shape": "AccessControlAttributeValueSource"}, "max": 1, "min": 1}, "AccessDeniedException": {"type": "structure", "members": {"Message": {"shape": "AccessDeniedExceptionMessage"}}, "documentation": "<p>You do not have sufficient access to perform this action.</p>", "exception": true}, "AccessDeniedExceptionMessage": {"type": "string"}, "AccountAssignment": {"type": "structure", "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The identifier of the Amazon Web Services account.</p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the permission set. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "PrincipalType": {"shape": "PrincipalType", "documentation": "<p>The entity type for which the assignment will be created.</p>"}, "PrincipalId": {"shape": "PrincipalId", "documentation": "<p>An identifier for an object in IAM Identity Center, such as a user or group. PrincipalIds are GUIDs (For example, f81d4fae-7dec-11d0-a765-00a0c91e6bf6). For more information about PrincipalIds in IAM Identity Center, see the <a href=\"/singlesignon/latest/IdentityStoreAPIReference/welcome.html\">IAM Identity Center Identity Store API Reference</a>.</p>"}}, "documentation": "<p>The assignment that indicates a principal's limited access to a specified Amazon Web Services account with a specified permission set.</p> <note> <p>The term <i>principal</i> here refers to a user or group that is defined in IAM Identity Center.</p> </note>"}, "AccountAssignmentForPrincipal": {"type": "structure", "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The account ID number of the Amazon Web Services account.</p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the IAM Identity Center permission set assigned to this principal for this Amazon Web Services account.</p>"}, "PrincipalId": {"shape": "PrincipalId", "documentation": "<p>The ID of the principal.</p>"}, "PrincipalType": {"shape": "PrincipalType", "documentation": "<p>The type of the principal.</p>"}}, "documentation": "<p>A structure that describes an assignment of an Amazon Web Services account to a principal and the permissions that principal has in the account.</p>"}, "AccountAssignmentList": {"type": "list", "member": {"shape": "AccountAssignment"}}, "AccountAssignmentListForPrincipal": {"type": "list", "member": {"shape": "AccountAssignmentForPrincipal"}}, "AccountAssignmentOperationStatus": {"type": "structure", "members": {"Status": {"shape": "StatusValues", "documentation": "<p>The status of the permission set provisioning process.</p>"}, "RequestId": {"shape": "UUId", "documentation": "<p>The identifier for tracking the request operation that is generated by the universally unique identifier (UUID) workflow.</p>"}, "FailureReason": {"shape": "Reason", "documentation": "<p>The message that contains an error or exception in case of an operation failure.</p>"}, "TargetId": {"shape": "TargetId", "documentation": "<p>TargetID is an Amazon Web Services account identifier, (For example, ************).</p>"}, "TargetType": {"shape": "TargetType", "documentation": "<p>The entity type for which the assignment will be created.</p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the permission set. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "PrincipalType": {"shape": "PrincipalType", "documentation": "<p>The entity type for which the assignment will be created.</p>"}, "PrincipalId": {"shape": "PrincipalId", "documentation": "<p>An identifier for an object in IAM Identity Center, such as a user or group. PrincipalIds are GUIDs (For example, f81d4fae-7dec-11d0-a765-00a0c91e6bf6). For more information about PrincipalIds in IAM Identity Center, see the <a href=\"/singlesignon/latest/IdentityStoreAPIReference/welcome.html\">IAM Identity Center Identity Store API Reference</a>.</p>"}, "CreatedDate": {"shape": "Date", "documentation": "<p>The date that the permission set was created.</p>"}}, "documentation": "<p>The status of the creation or deletion operation of an assignment that a principal needs to access an account.</p>"}, "AccountAssignmentOperationStatusList": {"type": "list", "member": {"shape": "AccountAssignmentOperationStatusMetadata"}}, "AccountAssignmentOperationStatusMetadata": {"type": "structure", "members": {"Status": {"shape": "StatusValues", "documentation": "<p>The status of the permission set provisioning process.</p>"}, "RequestId": {"shape": "UUId", "documentation": "<p>The identifier for tracking the request operation that is generated by the universally unique identifier (UUID) workflow.</p>"}, "CreatedDate": {"shape": "Date", "documentation": "<p>The date that the permission set was created.</p>"}}, "documentation": "<p>Provides information about the <a>AccountAssignment</a> creation request.</p>"}, "AccountId": {"type": "string", "max": 12, "min": 12, "pattern": "\\d{12}"}, "AccountList": {"type": "list", "member": {"shape": "AccountId"}}, "ActorPolicyDocument": {"type": "structure", "members": {}, "document": true}, "Application": {"type": "structure", "members": {"ApplicationArn": {"shape": "ApplicationArn", "documentation": "<p>The ARN of the application.</p>"}, "ApplicationProviderArn": {"shape": "ApplicationProviderArn", "documentation": "<p>The ARN of the application provider for this application.</p>"}, "Name": {"shape": "NameType", "documentation": "<p>The name of the application.</p>"}, "ApplicationAccount": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID number of the application.</p>"}, "InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the instance of IAM Identity Center that is configured with this application.</p>"}, "Status": {"shape": "ApplicationStatus", "documentation": "<p>The current status of the application in this instance of IAM Identity Center.</p>"}, "PortalOptions": {"shape": "PortalOptions", "documentation": "<p>A structure that describes the options for the access portal associated with this application.</p>"}, "Description": {"shape": "Description", "documentation": "<p>The description of the application.</p>"}, "CreatedDate": {"shape": "Date", "documentation": "<p>The date and time when the application was originally created.</p>"}}, "documentation": "<p>A structure that describes an application that uses IAM Identity Center for access management.</p>"}, "ApplicationArn": {"type": "string", "max": 1224, "min": 10, "pattern": "arn:(aws|aws-us-gov|aws-cn|aws-iso|aws-iso-b):sso::\\d{12}:application/(sso)?ins-[a-zA-Z0-9-.]{16}/apl-[a-zA-Z0-9]{16}"}, "ApplicationAssignment": {"type": "structure", "required": ["ApplicationArn", "PrincipalId", "PrincipalType"], "members": {"ApplicationArn": {"shape": "ApplicationArn", "documentation": "<p>The ARN of the application that has principals assigned.</p>"}, "PrincipalId": {"shape": "PrincipalId", "documentation": "<p>The unique identifier of the principal assigned to the application.</p>"}, "PrincipalType": {"shape": "PrincipalType", "documentation": "<p>The type of the principal assigned to the application.</p>"}}, "documentation": "<p>A structure that describes an assignment of a principal to an application.</p>"}, "ApplicationAssignmentForPrincipal": {"type": "structure", "members": {"ApplicationArn": {"shape": "ApplicationArn", "documentation": "<p>The ARN of the application to which the specified principal is assigned.</p>"}, "PrincipalId": {"shape": "PrincipalId", "documentation": "<p>The unique identifier of the principal assigned to the application.</p>"}, "PrincipalType": {"shape": "PrincipalType", "documentation": "<p>The type of the principal assigned to the application.</p>"}}, "documentation": "<p>A structure that describes an application to which a principal is assigned.</p>"}, "ApplicationAssignmentListForPrincipal": {"type": "list", "member": {"shape": "ApplicationAssignmentForPrincipal"}}, "ApplicationAssignmentsList": {"type": "list", "member": {"shape": "ApplicationAssignment"}}, "ApplicationList": {"type": "list", "member": {"shape": "Application"}, "max": 50, "min": 0}, "ApplicationNameType": {"type": "string", "max": 100, "min": 1, "pattern": "[\\S\\s]*"}, "ApplicationProvider": {"type": "structure", "required": ["ApplicationProviderArn"], "members": {"ApplicationProviderArn": {"shape": "ApplicationProviderArn", "documentation": "<p>The ARN of the application provider.</p>"}, "FederationProtocol": {"shape": "FederationProtocol", "documentation": "<p>The protocol that the application provider uses to perform federation.</p>"}, "DisplayData": {"shape": "DisplayData", "documentation": "<p>A structure that describes how IAM Identity Center represents the application provider in the portal.</p>"}, "ResourceServerConfig": {"shape": "ResourceServerConfig", "documentation": "<p>A structure that describes the application provider's resource server.</p>"}}, "documentation": "<p>A structure that describes a provider that can be used to connect an Amazon Web Services managed application or customer managed application to IAM Identity Center.</p>"}, "ApplicationProviderArn": {"type": "string", "max": 1224, "min": 10, "pattern": "arn:(aws|aws-us-gov|aws-cn|aws-iso|aws-iso-b):sso::aws:applicationProvider/[a-zA-Z0-9-/]+"}, "ApplicationProviderList": {"type": "list", "member": {"shape": "ApplicationProvider"}}, "ApplicationStatus": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "ApplicationUrl": {"type": "string", "max": 512, "min": 1, "pattern": "http(s)?:\\/\\/[-a-zA-Z0-9+&@#\\/%?=~_|!:,.;]*[-a-zA-Z0-9+&bb@#\\/%?=~_|]"}, "ApplicationVisibility": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "AssignmentRequired": {"type": "boolean", "box": true}, "AttachCustomerManagedPolicyReferenceToPermissionSetRequest": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn", "CustomerManagedPolicyReference"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. </p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the <code>PermissionSet</code>.</p>"}, "CustomerManagedPolicyReference": {"shape": "CustomerManagedPolicyReference", "documentation": "<p>Specifies the name and path of a customer managed policy. You must have an IAM policy that matches the name and path in each Amazon Web Services account where you want to deploy your permission set.</p>"}}}, "AttachCustomerManagedPolicyReferenceToPermissionSetResponse": {"type": "structure", "members": {}}, "AttachManagedPolicyToPermissionSetRequest": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn", "ManagedPolicyArn"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the <a>PermissionSet</a> that the managed policy should be attached to.</p>"}, "ManagedPolicyArn": {"shape": "ManagedPolicyArn", "documentation": "<p>The Amazon Web Services managed policy ARN to be attached to a permission set.</p>"}}}, "AttachManagedPolicyToPermissionSetResponse": {"type": "structure", "members": {}}, "AttachedManagedPolicy": {"type": "structure", "members": {"Name": {"shape": "Name", "documentation": "<p>The name of the Amazon Web Services managed policy.</p>"}, "Arn": {"shape": "ManagedPolicyArn", "documentation": "<p>The ARN of the Amazon Web Services managed policy. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}}, "documentation": "<p>A structure that stores a list of managed policy ARNs that describe the associated Amazon Web Services managed policy.</p>"}, "AttachedManagedPolicyList": {"type": "list", "member": {"shape": "AttachedManagedPolicy"}}, "AuthenticationMethod": {"type": "structure", "members": {"Iam": {"shape": "IamAuthenticationMethod", "documentation": "<p>A structure that describes details for IAM authentication.</p>"}}, "documentation": "<p>A structure that describes an authentication method that can be used by an application.</p>", "union": true}, "AuthenticationMethodItem": {"type": "structure", "members": {"AuthenticationMethodType": {"shape": "AuthenticationMethodType", "documentation": "<p>The type of authentication that is used by this method.</p>"}, "AuthenticationMethod": {"shape": "AuthenticationMethod", "documentation": "<p>A structure that describes an authentication method. The contents of this structure is determined by the <code>AuthenticationMethodType</code>.</p>"}}, "documentation": "<p>A structure that describes an authentication method and its type.</p>"}, "AuthenticationMethodType": {"type": "string", "enum": ["IAM"]}, "AuthenticationMethods": {"type": "list", "member": {"shape": "AuthenticationMethodItem"}}, "AuthorizationCodeGrant": {"type": "structure", "members": {"RedirectUris": {"shape": "RedirectUris", "documentation": "<p>A list of URIs that are valid locations to redirect a user's browser after the user is authorized.</p> <note> <p>RedirectUris is required when the grant type is <code>authorization_code</code>.</p> </note>"}}, "documentation": "<p>A structure that defines configuration settings for an application that supports the OAuth 2.0 Authorization Code Grant.</p>"}, "AuthorizedTokenIssuer": {"type": "structure", "members": {"TrustedTokenIssuerArn": {"shape": "TrustedTokenIssuerArn", "documentation": "<p>The ARN of the trusted token issuer.</p>"}, "AuthorizedAudiences": {"shape": "TokenIssuerAudiences", "documentation": "<p>An array list of authorized audiences, or applications, that can consume the tokens generated by the associated trusted token issuer.</p>"}}, "documentation": "<p>A structure that describes a trusted token issuer and associates it with a set of authorized audiences.</p>"}, "AuthorizedTokenIssuers": {"type": "list", "member": {"shape": "Authorized<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "max": 10, "min": 1}, "ClaimAttributePath": {"type": "string", "max": 255, "min": 1, "pattern": "\\p{L}+(?:(\\.|\\_)\\p{L}+){0,2}"}, "ClientToken": {"type": "string", "max": 64, "min": 1, "pattern": "[!-~]+"}, "ConflictException": {"type": "structure", "members": {"Message": {"shape": "ConflictExceptionMessage"}}, "documentation": "<p>Occurs when a conflict with a previous successful write is detected. This generally occurs when the previous write did not have time to propagate to the host serving the current request. A retry (with appropriate backoff logic) is the recommended response to this exception.</p>", "exception": true}, "ConflictExceptionMessage": {"type": "string"}, "CreateAccountAssignmentRequest": {"type": "structure", "required": ["InstanceArn", "TargetId", "TargetType", "PermissionSetArn", "PrincipalType", "PrincipalId"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "TargetId": {"shape": "TargetId", "documentation": "<p>TargetID is an Amazon Web Services account identifier, (For example, ************).</p>"}, "TargetType": {"shape": "TargetType", "documentation": "<p>The entity type for which the assignment will be created.</p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the permission set that the admin wants to grant the principal access to.</p>"}, "PrincipalType": {"shape": "PrincipalType", "documentation": "<p>The entity type for which the assignment will be created.</p>"}, "PrincipalId": {"shape": "PrincipalId", "documentation": "<p>An identifier for an object in IAM Identity Center, such as a user or group. PrincipalIds are GUIDs (For example, f81d4fae-7dec-11d0-a765-00a0c91e6bf6). For more information about PrincipalIds in IAM Identity Center, see the <a href=\"/singlesignon/latest/IdentityStoreAPIReference/welcome.html\">IAM Identity Center Identity Store API Reference</a>.</p>"}}}, "CreateAccountAssignmentResponse": {"type": "structure", "members": {"AccountAssignmentCreationStatus": {"shape": "AccountAssignmentOperationStatus", "documentation": "<p>The status object for the account assignment creation operation.</p>"}}}, "CreateApplicationAssignmentRequest": {"type": "structure", "required": ["ApplicationArn", "PrincipalId", "PrincipalType"], "members": {"ApplicationArn": {"shape": "ApplicationArn", "documentation": "<p>The ARN of the application for which the assignment is created.</p>"}, "PrincipalId": {"shape": "PrincipalId", "documentation": "<p>An identifier for an object in IAM Identity Center, such as a user or group. PrincipalIds are GUIDs (For example, f81d4fae-7dec-11d0-a765-00a0c91e6bf6). For more information about PrincipalIds in IAM Identity Center, see the <a href=\"https://docs.aws.amazon.com/singlesignon/latest/IdentityStoreAPIReference/welcome.html\">IAM Identity Center Identity Store API Reference</a>.</p>"}, "PrincipalType": {"shape": "PrincipalType", "documentation": "<p>The entity type for which the assignment will be created.</p>"}}}, "CreateApplicationAssignmentResponse": {"type": "structure", "members": {}}, "CreateApplicationRequest": {"type": "structure", "required": ["InstanceArn", "ApplicationProviderArn", "Name"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the instance of IAM Identity Center under which the operation will run. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "ApplicationProviderArn": {"shape": "ApplicationProviderArn", "documentation": "<p>The ARN of the application provider under which the operation will run.</p>"}, "Name": {"shape": "ApplicationNameType", "documentation": "<p>The name of the .</p>"}, "Description": {"shape": "Description", "documentation": "<p>The description of the .</p>"}, "PortalOptions": {"shape": "PortalOptions", "documentation": "<p>A structure that describes the options for the portal associated with an application.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>Specifies tags to be attached to the application.</p>"}, "Status": {"shape": "ApplicationStatus", "documentation": "<p>Specifies whether the application is enabled or disabled.</p>"}, "ClientToken": {"shape": "ClientToken", "documentation": "<p>Specifies a unique, case-sensitive ID that you provide to ensure the idempotency of the request. This lets you safely retry the request without accidentally performing the same operation a second time. Passing the same value to a later call to an operation requires that you also pass the same value for all other parameters. We recommend that you use a <a href=\"https://wikipedia.org/wiki/Universally_unique_identifier\">UUID type of value</a>.</p> <p>If you don't provide this value, then Amazon Web Services generates a random one for you.</p> <p>If you retry the operation with the same <code>ClientToken</code>, but with different parameters, the retry fails with an <code>IdempotentParameterMismatch</code> error.</p>", "idempotencyToken": true}}}, "CreateApplicationResponse": {"type": "structure", "members": {"ApplicationArn": {"shape": "ApplicationArn", "documentation": "<p>Specifies the ARN of the application.</p>"}}}, "CreateInstanceAccessControlAttributeConfigurationRequest": {"type": "structure", "required": ["InstanceArn", "InstanceAccessControlAttributeConfiguration"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed.</p>"}, "InstanceAccessControlAttributeConfiguration": {"shape": "InstanceAccessControlAttributeConfiguration", "documentation": "<p>Specifies the IAM Identity Center identity store attributes to add to your ABAC configuration. When using an external identity provider as an identity source, you can pass attributes through the SAML assertion. Doing so provides an alternative to configuring attributes from the IAM Identity Center identity store. If a SAML assertion passes any of these attributes, IAM Identity Center will replace the attribute value with the value from the IAM Identity Center identity store.</p>"}}}, "CreateInstanceAccessControlAttributeConfigurationResponse": {"type": "structure", "members": {}}, "CreateInstanceRequest": {"type": "structure", "members": {"Name": {"shape": "NameType", "documentation": "<p>The name of the instance of IAM Identity Center.</p>"}, "ClientToken": {"shape": "ClientToken", "documentation": "<p>Specifies a unique, case-sensitive ID that you provide to ensure the idempotency of the request. This lets you safely retry the request without accidentally performing the same operation a second time. Passing the same value to a later call to an operation requires that you also pass the same value for all other parameters. We recommend that you use a <a href=\"https://wikipedia.org/wiki/Universally_unique_identifier\">UUID type of value</a>.</p> <p>If you don't provide this value, then Amazon Web Services generates a random one for you.</p> <p>If you retry the operation with the same <code>ClientToken</code>, but with different parameters, the retry fails with an <code>IdempotentParameterMismatch</code> error.</p>", "idempotencyToken": true}, "Tags": {"shape": "TagList", "documentation": "<p>Specifies tags to be attached to the instance of IAM Identity Center.</p>"}}}, "CreateInstanceResponse": {"type": "structure", "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the instance of IAM Identity Center under which the operation will run. </p> <p>For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}}}, "CreatePermissionSetRequest": {"type": "structure", "required": ["Name", "InstanceArn"], "members": {"Name": {"shape": "PermissionSetName", "documentation": "<p>The name of the <a>PermissionSet</a>.</p>"}, "Description": {"shape": "PermissionSetDescription", "documentation": "<p>The description of the <a>PermissionSet</a>.</p>"}, "InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "SessionDuration": {"shape": "Duration", "documentation": "<p>The length of time that the application user sessions are valid in the ISO-8601 standard.</p>"}, "RelayState": {"shape": "RelayState", "documentation": "<p>Used to redirect users within the application during the federation authentication process.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The tags to attach to the new <a>PermissionSet</a>.</p>"}}}, "CreatePermissionSetResponse": {"type": "structure", "members": {"PermissionSet": {"shape": "PermissionSet", "documentation": "<p>Defines the level of access on an Amazon Web Services account.</p>"}}}, "CreateTrustedTokenIssuerRequest": {"type": "structure", "required": ["InstanceArn", "Name", "TrustedTokenIssuerType", "TrustedTokenIssuerConfiguration"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>Specifies the ARN of the instance of IAM Identity Center to contain the new trusted token issuer configuration.</p>"}, "Name": {"shape": "TrustedTokenIssuerName", "documentation": "<p>Specifies the name of the new trusted token issuer configuration.</p>"}, "TrustedTokenIssuerType": {"shape": "TrustedTokenIssuerType", "documentation": "<p>Specifies the type of the new trusted token issuer.</p>"}, "TrustedTokenIssuerConfiguration": {"shape": "TrustedTokenIssuerConfiguration", "documentation": "<p>Specifies settings that apply to the new trusted token issuer configuration. The settings that are available depend on what <code>TrustedTokenIssuerType</code> you specify.</p>"}, "ClientToken": {"shape": "ClientToken", "documentation": "<p>Specifies a unique, case-sensitive ID that you provide to ensure the idempotency of the request. This lets you safely retry the request without accidentally performing the same operation a second time. Passing the same value to a later call to an operation requires that you also pass the same value for all other parameters. We recommend that you use a <a href=\"https://wikipedia.org/wiki/Universally_unique_identifier\">UUID type of value.</a>.</p> <p>If you don't provide this value, then Amazon Web Services generates a random one for you.</p> <p>If you retry the operation with the same <code>ClientToken</code>, but with different parameters, the retry fails with an <code>IdempotentParameterMismatch</code> error.</p>", "idempotencyToken": true}, "Tags": {"shape": "TagList", "documentation": "<p>Specifies tags to be attached to the new trusted token issuer configuration.</p>"}}}, "CreateTrustedTokenIssuerResponse": {"type": "structure", "members": {"TrustedTokenIssuerArn": {"shape": "TrustedTokenIssuerArn", "documentation": "<p>The ARN of the new trusted token issuer configuration.</p>"}}}, "CustomerManagedPolicyReference": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "ManagedPolicyName", "documentation": "<p>The name of the IAM policy that you have configured in each account where you want to deploy your permission set.</p>"}, "Path": {"shape": "ManagedPolicyPath", "documentation": "<p>The path to the IAM policy that you have configured in each account where you want to deploy your permission set. The default is <code>/</code>. For more information, see <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/reference_identifiers.html#identifiers-friendly-names\">Friendly names and paths</a> in the <i>IAM User Guide</i>.</p>"}}, "documentation": "<p>Specifies the name and path of a customer managed policy. You must have an IAM policy that matches the name and path in each Amazon Web Services account where you want to deploy your permission set.</p>"}, "CustomerManagedPolicyReferenceList": {"type": "list", "member": {"shape": "CustomerManagedPolicyReference"}}, "Date": {"type": "timestamp"}, "DeleteAccountAssignmentRequest": {"type": "structure", "required": ["InstanceArn", "TargetId", "TargetType", "PermissionSetArn", "PrincipalType", "PrincipalId"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "TargetId": {"shape": "TargetId", "documentation": "<p>TargetID is an Amazon Web Services account identifier, (For example, ************).</p>"}, "TargetType": {"shape": "TargetType", "documentation": "<p>The entity type for which the assignment will be deleted.</p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the permission set that will be used to remove access.</p>"}, "PrincipalType": {"shape": "PrincipalType", "documentation": "<p>The entity type for which the assignment will be deleted.</p>"}, "PrincipalId": {"shape": "PrincipalId", "documentation": "<p>An identifier for an object in IAM Identity Center, such as a user or group. PrincipalIds are GUIDs (For example, f81d4fae-7dec-11d0-a765-00a0c91e6bf6). For more information about PrincipalIds in IAM Identity Center, see the <a href=\"/singlesignon/latest/IdentityStoreAPIReference/welcome.html\">IAM Identity Center Identity Store API Reference</a>.</p>"}}}, "DeleteAccountAssignmentResponse": {"type": "structure", "members": {"AccountAssignmentDeletionStatus": {"shape": "AccountAssignmentOperationStatus", "documentation": "<p>The status object for the account assignment deletion operation.</p>"}}}, "DeleteApplicationAccessScopeRequest": {"type": "structure", "required": ["ApplicationArn", "<PERSON><PERSON>"], "members": {"ApplicationArn": {"shape": "ApplicationArn", "documentation": "<p>Specifies the ARN of the application with the access scope to delete.</p>"}, "Scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>Specifies the name of the access scope to remove from the application.</p>"}}}, "DeleteApplicationAssignmentRequest": {"type": "structure", "required": ["ApplicationArn", "PrincipalId", "PrincipalType"], "members": {"ApplicationArn": {"shape": "ApplicationArn", "documentation": "<p>Specifies the ARN of the application.</p>"}, "PrincipalId": {"shape": "PrincipalId", "documentation": "<p>An identifier for an object in IAM Identity Center, such as a user or group. PrincipalIds are GUIDs (For example, f81d4fae-7dec-11d0-a765-00a0c91e6bf6). For more information about PrincipalIds in IAM Identity Center, see the <a href=\"https://docs.aws.amazon.com/singlesignon/latest/IdentityStoreAPIReference/welcome.html\">IAM Identity Center Identity Store API Reference</a>.</p>"}, "PrincipalType": {"shape": "PrincipalType", "documentation": "<p>The entity type for which the assignment will be deleted.</p>"}}}, "DeleteApplicationAssignmentResponse": {"type": "structure", "members": {}}, "DeleteApplicationAuthenticationMethodRequest": {"type": "structure", "required": ["ApplicationArn", "AuthenticationMethodType"], "members": {"ApplicationArn": {"shape": "ApplicationArn", "documentation": "<p>Specifies the ARN of the application with the authentication method to delete.</p>"}, "AuthenticationMethodType": {"shape": "AuthenticationMethodType", "documentation": "<p>Specifies the authentication method type to delete from the application.</p>"}}}, "DeleteApplicationGrantRequest": {"type": "structure", "required": ["ApplicationArn", "GrantType"], "members": {"ApplicationArn": {"shape": "ApplicationArn", "documentation": "<p>Specifies the ARN of the application with the grant to delete.</p>"}, "GrantType": {"shape": "GrantType", "documentation": "<p>Specifies the type of grant to delete from the application.</p>"}}}, "DeleteApplicationRequest": {"type": "structure", "required": ["ApplicationArn"], "members": {"ApplicationArn": {"shape": "ApplicationArn", "documentation": "<p>Specifies the ARN of the application. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>. </p>"}}}, "DeleteApplicationResponse": {"type": "structure", "members": {}}, "DeleteInlinePolicyFromPermissionSetRequest": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the permission set that will be used to remove access.</p>"}}}, "DeleteInlinePolicyFromPermissionSetResponse": {"type": "structure", "members": {}}, "DeleteInstanceAccessControlAttributeConfigurationRequest": {"type": "structure", "required": ["InstanceArn"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed.</p>"}}}, "DeleteInstanceAccessControlAttributeConfigurationResponse": {"type": "structure", "members": {}}, "DeleteInstanceRequest": {"type": "structure", "required": ["InstanceArn"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the instance of IAM Identity Center under which the operation will run.</p>"}}}, "DeleteInstanceResponse": {"type": "structure", "members": {}}, "DeletePermissionSetRequest": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the permission set that should be deleted.</p>"}}}, "DeletePermissionSetResponse": {"type": "structure", "members": {}}, "DeletePermissionsBoundaryFromPermissionSetRequest": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. </p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the <code>PermissionSet</code>.</p>"}}}, "DeletePermissionsBoundaryFromPermissionSetResponse": {"type": "structure", "members": {}}, "DeleteTrustedTokenIssuerRequest": {"type": "structure", "required": ["TrustedTokenIssuerArn"], "members": {"TrustedTokenIssuerArn": {"shape": "TrustedTokenIssuerArn", "documentation": "<p>Specifies the ARN of the trusted token issuer configuration to delete.</p>"}}}, "DeleteTrustedTokenIssuerResponse": {"type": "structure", "members": {}}, "DescribeAccountAssignmentCreationStatusRequest": {"type": "structure", "required": ["InstanceArn", "AccountAssignmentCreationRequestId"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "AccountAssignmentCreationRequestId": {"shape": "UUId", "documentation": "<p>The identifier that is used to track the request operation progress.</p>"}}}, "DescribeAccountAssignmentCreationStatusResponse": {"type": "structure", "members": {"AccountAssignmentCreationStatus": {"shape": "AccountAssignmentOperationStatus", "documentation": "<p>The status object for the account assignment creation operation.</p>"}}}, "DescribeAccountAssignmentDeletionStatusRequest": {"type": "structure", "required": ["InstanceArn", "AccountAssignmentDeletionRequestId"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "AccountAssignmentDeletionRequestId": {"shape": "UUId", "documentation": "<p>The identifier that is used to track the request operation progress.</p>"}}}, "DescribeAccountAssignmentDeletionStatusResponse": {"type": "structure", "members": {"AccountAssignmentDeletionStatus": {"shape": "AccountAssignmentOperationStatus", "documentation": "<p>The status object for the account assignment deletion operation.</p>"}}}, "DescribeApplicationAssignmentRequest": {"type": "structure", "required": ["ApplicationArn", "PrincipalId", "PrincipalType"], "members": {"ApplicationArn": {"shape": "ApplicationArn", "documentation": "<p>Specifies the ARN of the application. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "PrincipalId": {"shape": "PrincipalId", "documentation": "<p>An identifier for an object in IAM Identity Center, such as a user or group. PrincipalIds are GUIDs (For example, f81d4fae-7dec-11d0-a765-00a0c91e6bf6). For more information about PrincipalIds in IAM Identity Center, see the <a href=\"https://docs.aws.amazon.com/singlesignon/latest/IdentityStoreAPIReference/welcome.html\">IAM Identity Center Identity Store API Reference</a>.</p>"}, "PrincipalType": {"shape": "PrincipalType", "documentation": "<p>The entity type for which the assignment will be created.</p>"}}}, "DescribeApplicationAssignmentResponse": {"type": "structure", "members": {"PrincipalType": {"shape": "PrincipalType", "documentation": "<p>The entity type for which the assignment will be created.</p>"}, "PrincipalId": {"shape": "PrincipalId", "documentation": "<p>An identifier for an object in IAM Identity Center, such as a user or group. PrincipalIds are GUIDs (For example, f81d4fae-7dec-11d0-a765-00a0c91e6bf6). For more information about PrincipalIds in IAM Identity Center, see the <a href=\"https://docs.aws.amazon.com/singlesignon/latest/IdentityStoreAPIReference/welcome.html\">IAM Identity Center Identity Store API Reference</a>.</p>"}, "ApplicationArn": {"shape": "ApplicationArn", "documentation": "<p>Specifies the ARN of the application. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}}}, "DescribeApplicationProviderRequest": {"type": "structure", "required": ["ApplicationProviderArn"], "members": {"ApplicationProviderArn": {"shape": "ApplicationProviderArn", "documentation": "<p>Specifies the ARN of the application provider for which you want details.</p>"}}}, "DescribeApplicationProviderResponse": {"type": "structure", "required": ["ApplicationProviderArn"], "members": {"ApplicationProviderArn": {"shape": "ApplicationProviderArn", "documentation": "<p>The ARN of the application provider.</p>"}, "FederationProtocol": {"shape": "FederationProtocol", "documentation": "<p>The protocol used to federate to the application provider.</p>"}, "DisplayData": {"shape": "DisplayData", "documentation": "<p>A structure with details about the display data for the application provider.</p>"}, "ResourceServerConfig": {"shape": "ResourceServerConfig", "documentation": "<p>A structure with details about the receiving application.</p>"}}}, "DescribeApplicationRequest": {"type": "structure", "required": ["ApplicationArn"], "members": {"ApplicationArn": {"shape": "ApplicationArn", "documentation": "<p>Specifies the ARN of the application. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}}}, "DescribeApplicationResponse": {"type": "structure", "members": {"ApplicationArn": {"shape": "ApplicationArn", "documentation": "<p>Specifies the ARN of the application.</p>"}, "ApplicationProviderArn": {"shape": "ApplicationProviderArn", "documentation": "<p>The ARN of the application provider under which the operation will run.</p>"}, "Name": {"shape": "NameType", "documentation": "<p>The application name.</p>"}, "ApplicationAccount": {"shape": "AccountId", "documentation": "<p>The account ID.</p>"}, "InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center application under which the operation will run. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "Status": {"shape": "ApplicationStatus", "documentation": "<p>Specifies whether the application is enabled or disabled.</p>"}, "PortalOptions": {"shape": "PortalOptions", "documentation": "<p>A structure that describes the options for the portal associated with an application.</p>"}, "Description": {"shape": "Description", "documentation": "<p>The description of the .</p>"}, "CreatedDate": {"shape": "Date", "documentation": "<p>The date the application was created.</p>"}}}, "DescribeInstanceAccessControlAttributeConfigurationRequest": {"type": "structure", "required": ["InstanceArn"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed.</p>"}}}, "DescribeInstanceAccessControlAttributeConfigurationResponse": {"type": "structure", "members": {"Status": {"shape": "InstanceAccessControlAttributeConfigurationStatus", "documentation": "<p>The status of the attribute configuration process.</p>"}, "StatusReason": {"shape": "InstanceAccessControlAttributeConfigurationStatusReason", "documentation": "<p>Provides more details about the current status of the specified attribute.</p>"}, "InstanceAccessControlAttributeConfiguration": {"shape": "InstanceAccessControlAttributeConfiguration", "documentation": "<p>Gets the list of IAM Identity Center identity store attributes that have been added to your ABAC configuration.</p>"}}}, "DescribeInstanceRequest": {"type": "structure", "required": ["InstanceArn"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the instance of IAM Identity Center under which the operation will run.</p>"}}}, "DescribeInstanceResponse": {"type": "structure", "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the instance of IAM Identity Center under which the operation will run. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "IdentityStoreId": {"shape": "Id", "documentation": "<p>The identifier of the identity store that is connected to the instance of IAM Identity Center.</p>"}, "OwnerAccountId": {"shape": "AccountId", "documentation": "<p>The identifier of the Amazon Web Services account for which the instance was created.</p>"}, "Name": {"shape": "NameType", "documentation": "<p>Specifies the instance name.</p>"}, "CreatedDate": {"shape": "Date", "documentation": "<p>The date the instance was created.</p>"}, "Status": {"shape": "InstanceStatus", "documentation": "<p>The status of the instance. </p>"}}}, "DescribePermissionSetProvisioningStatusRequest": {"type": "structure", "required": ["InstanceArn", "ProvisionPermissionSetRequestId"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "ProvisionPermissionSetRequestId": {"shape": "UUId", "documentation": "<p>The identifier that is provided by the <a>ProvisionPermissionSet</a> call to retrieve the current status of the provisioning workflow.</p>"}}}, "DescribePermissionSetProvisioningStatusResponse": {"type": "structure", "members": {"PermissionSetProvisioningStatus": {"shape": "PermissionSetProvisioningStatus", "documentation": "<p>The status object for the permission set provisioning operation.</p>"}}}, "DescribePermissionSetRequest": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the permission set. </p>"}}}, "DescribePermissionSetResponse": {"type": "structure", "members": {"PermissionSet": {"shape": "PermissionSet", "documentation": "<p>Describes the level of access on an Amazon Web Services account.</p>"}}}, "DescribeTrustedTokenIssuerRequest": {"type": "structure", "required": ["TrustedTokenIssuerArn"], "members": {"TrustedTokenIssuerArn": {"shape": "TrustedTokenIssuerArn", "documentation": "<p>Specifies the ARN of the trusted token issuer configuration that you want details about.</p>"}}}, "DescribeTrustedTokenIssuerResponse": {"type": "structure", "members": {"TrustedTokenIssuerArn": {"shape": "TrustedTokenIssuerArn", "documentation": "<p>The ARN of the trusted token issuer configuration.</p>"}, "Name": {"shape": "TrustedTokenIssuerName", "documentation": "<p>The name of the trusted token issuer configuration.</p>"}, "TrustedTokenIssuerType": {"shape": "TrustedTokenIssuerType", "documentation": "<p>The type of the trusted token issuer.</p>"}, "TrustedTokenIssuerConfiguration": {"shape": "TrustedTokenIssuerConfiguration", "documentation": "<p>A structure the describes the settings that apply of this trusted token issuer.</p>"}}}, "Description": {"type": "string", "max": 128, "min": 1}, "DetachCustomerManagedPolicyReferenceFromPermissionSetRequest": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn", "CustomerManagedPolicyReference"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. </p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the <code>PermissionSet</code>.</p>"}, "CustomerManagedPolicyReference": {"shape": "CustomerManagedPolicyReference", "documentation": "<p>Specifies the name and path of a customer managed policy. You must have an IAM policy that matches the name and path in each Amazon Web Services account where you want to deploy your permission set.</p>"}}}, "DetachCustomerManagedPolicyReferenceFromPermissionSetResponse": {"type": "structure", "members": {}}, "DetachManagedPolicyFromPermissionSetRequest": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn", "ManagedPolicyArn"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the <a>PermissionSet</a> from which the policy should be detached.</p>"}, "ManagedPolicyArn": {"shape": "ManagedPolicyArn", "documentation": "<p>The Amazon Web Services managed policy ARN to be detached from a permission set.</p>"}}}, "DetachManagedPolicyFromPermissionSetResponse": {"type": "structure", "members": {}}, "DisplayData": {"type": "structure", "members": {"DisplayName": {"shape": "Name", "documentation": "<p>The name of the application provider that appears in the portal.</p>"}, "IconUrl": {"shape": "IconUrl", "documentation": "<p>A URL that points to an icon that represents the application provider.</p>"}, "Description": {"shape": "Description", "documentation": "<p>The description of the application provider that appears in the portal.</p>"}}, "documentation": "<p>A structure that describes how the portal represents an application provider.</p>"}, "Duration": {"type": "string", "max": 100, "min": 1, "pattern": "(-?)P(?=\\d|T\\d)(?:(\\d+)Y)?(?:(\\d+)M)?(?:(\\d+)([DW]))?(?:T(?:(\\d+)H)?(?:(\\d+)M)?(?:(\\d+(?:\\.\\d+)?)S)?)?"}, "FederationProtocol": {"type": "string", "enum": ["SAML", "OAUTH"]}, "GetApplicationAccessScopeRequest": {"type": "structure", "required": ["ApplicationArn", "<PERSON><PERSON>"], "members": {"ApplicationArn": {"shape": "ApplicationArn", "documentation": "<p>Specifies the ARN of the application with the access scope that you want to retrieve.</p>"}, "Scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>Specifies the name of the access scope for which you want the authorized targets.</p>"}}}, "GetApplicationAccessScopeResponse": {"type": "structure", "required": ["<PERSON><PERSON>"], "members": {"Scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>The name of the access scope that can be used with the authorized targets.</p>"}, "AuthorizedTargets": {"shape": "ScopeTargets", "documentation": "<p>An array of authorized targets associated with this access scope.</p>"}}}, "GetApplicationAssignmentConfigurationRequest": {"type": "structure", "required": ["ApplicationArn"], "members": {"ApplicationArn": {"shape": "ApplicationArn", "documentation": "<p>Specifies the ARN of the application. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}}}, "GetApplicationAssignmentConfigurationResponse": {"type": "structure", "required": ["AssignmentRequired"], "members": {"AssignmentRequired": {"shape": "AssignmentRequired", "documentation": "<p>If <code>AssignmentsRequired</code> is <code>true</code> (default value), users don’t have access to the application unless an assignment is created using the <a href=\"https://docs.aws.amazon.com/singlesignon/latest/APIReference/API_CreateApplicationAssignment.html\">CreateApplicationAssignment API</a>. If <code>false</code>, all users have access to the application. </p>"}}}, "GetApplicationAuthenticationMethodRequest": {"type": "structure", "required": ["ApplicationArn", "AuthenticationMethodType"], "members": {"ApplicationArn": {"shape": "ApplicationArn", "documentation": "<p>Specifies the ARN of the application.</p>"}, "AuthenticationMethodType": {"shape": "AuthenticationMethodType", "documentation": "<p>Specifies the type of authentication method for which you want details.</p>"}}}, "GetApplicationAuthenticationMethodResponse": {"type": "structure", "members": {"AuthenticationMethod": {"shape": "AuthenticationMethod", "documentation": "<p>A structure that contains details about the requested authentication method.</p>"}}}, "GetApplicationGrantRequest": {"type": "structure", "required": ["ApplicationArn", "GrantType"], "members": {"ApplicationArn": {"shape": "ApplicationArn", "documentation": "<p>Specifies the ARN of the application that contains the grant.</p>"}, "GrantType": {"shape": "GrantType", "documentation": "<p>Specifies the type of grant.</p>"}}}, "GetApplicationGrantResponse": {"type": "structure", "required": ["<PERSON>"], "members": {"Grant": {"shape": "<PERSON>", "documentation": "<p>A structure that describes the requested grant.</p>"}}}, "GetInlinePolicyForPermissionSetRequest": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the permission set.</p>"}}}, "GetInlinePolicyForPermissionSetResponse": {"type": "structure", "members": {"InlinePolicy": {"shape": "PermissionSetPolicyDocument", "documentation": "<p>The inline policy that is attached to the permission set.</p> <note> <p>For <code>Length Constraints</code>, if a valid ARN is provided for a permission set, it is possible for an empty inline policy to be returned.</p> </note>"}}}, "GetPermissionsBoundaryForPermissionSetRequest": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. </p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the <code>PermissionSet</code>.</p>"}}}, "GetPermissionsBoundaryForPermissionSetResponse": {"type": "structure", "members": {"PermissionsBoundary": {"shape": "PermissionsBoundary", "documentation": "<p>The permissions boundary attached to the specified permission set.</p>"}}}, "Grant": {"type": "structure", "members": {"AuthorizationCode": {"shape": "AuthorizationCodeGrant", "documentation": "<p>Configuration options for the <code>authorization_code</code> grant type.</p>"}, "JwtBearer": {"shape": "JwtBearerGrant", "documentation": "<p>Configuration options for the <code>urn:ietf:params:oauth:grant-type:jwt-bearer</code> grant type.</p>"}, "RefreshToken": {"shape": "RefreshTokenGrant", "documentation": "<p>Configuration options for the <code>refresh_token</code> grant type.</p>"}, "TokenExchange": {"shape": "TokenExchangeGrant", "documentation": "<p>Configuration options for the <code>urn:ietf:params:oauth:grant-type:token-exchange</code> grant type.</p>"}}, "documentation": "<p>The Grant union represents the set of possible configuration options for the selected grant type. Exactly one member of the union must be specified, and must match the grant type selected.</p>", "union": true}, "GrantItem": {"type": "structure", "required": ["GrantType", "<PERSON>"], "members": {"GrantType": {"shape": "GrantType", "documentation": "<p>The type of the selected grant.</p>"}, "Grant": {"shape": "<PERSON>", "documentation": "<p>The configuration structure for the selected grant.</p>"}}, "documentation": "<p>A structure that defines a single grant and its configuration.</p>"}, "GrantType": {"type": "string", "enum": ["authorization_code", "refresh_token", "urn:ietf:params:oauth:grant-type:jwt-bearer", "urn:ietf:params:oauth:grant-type:token-exchange"]}, "Grants": {"type": "list", "member": {"shape": "GrantItem"}}, "IamAuthenticationMethod": {"type": "structure", "required": ["ActorP<PERSON>y"], "members": {"ActorPolicy": {"shape": "ActorPolicyDocument", "documentation": "<p>An IAM policy document in JSON.</p>"}}, "documentation": "<p>A structure that describes details for authentication that uses IAM.</p>"}, "IconUrl": {"type": "string", "max": 768, "min": 1, "pattern": "(http|https):\\/\\/.*"}, "Id": {"type": "string", "max": 64, "min": 1, "pattern": "[a-zA-Z0-9-]*"}, "InstanceAccessControlAttributeConfiguration": {"type": "structure", "required": ["AccessControlAttributes"], "members": {"AccessControlAttributes": {"shape": "AccessControlAttributeList", "documentation": "<p>Lists the attributes that are configured for ABAC in the specified IAM Identity Center instance.</p>"}}, "documentation": "<p>Specifies the attributes to add to your attribute-based access control (ABAC) configuration.</p>"}, "InstanceAccessControlAttributeConfigurationStatus": {"type": "string", "enum": ["ENABLED", "CREATION_IN_PROGRESS", "CREATION_FAILED"]}, "InstanceAccessControlAttributeConfigurationStatusReason": {"type": "string"}, "InstanceArn": {"type": "string", "max": 1224, "min": 10, "pattern": "arn:(aws|aws-us-gov|aws-cn|aws-iso|aws-iso-b):sso:::instance/(sso)?ins-[a-zA-Z0-9-.]{16}"}, "InstanceList": {"type": "list", "member": {"shape": "InstanceMetadata"}, "max": 10, "min": 0}, "InstanceMetadata": {"type": "structure", "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "IdentityStoreId": {"shape": "Id", "documentation": "<p>The identifier of the identity store that is connected to the Identity Center instance.</p>"}, "OwnerAccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID number of the owner of the Identity Center instance.</p>"}, "Name": {"shape": "NameType", "documentation": "<p>The name of the Identity Center instance.</p>"}, "CreatedDate": {"shape": "Date", "documentation": "<p>The date and time that the Identity Center instance was created.</p>"}, "Status": {"shape": "InstanceStatus", "documentation": "<p>The current status of this Identity Center instance.</p>"}}, "documentation": "<p>Provides information about the IAM Identity Center instance.</p>"}, "InstanceStatus": {"type": "string", "enum": ["CREATE_IN_PROGRESS", "DELETE_IN_PROGRESS", "ACTIVE"]}, "InternalFailureMessage": {"type": "string"}, "InternalServerException": {"type": "structure", "members": {"Message": {"shape": "InternalFailureMessage"}}, "documentation": "<p>The request processing has failed because of an unknown error, exception, or failure with an internal server.</p>", "exception": true, "fault": true}, "JMESPath": {"type": "string", "max": 255, "min": 1, "pattern": "\\p{L}+(?:\\.\\p{L}+){0,2}"}, "JwksRetrievalOption": {"type": "string", "enum": ["OPEN_ID_DISCOVERY"]}, "JwtBearerGrant": {"type": "structure", "members": {"AuthorizedTokenIssuers": {"shape": "AuthorizedTokenIssuers", "documentation": "<p>A list of allowed token issuers trusted by the Identity Center instances for this application.</p> <note> <p> <code>AuthorizedTokenIssuers</code> is required when the grant type is <code>JwtBearerGrant</code>.</p> </note>"}}, "documentation": "<p>A structure that defines configuration settings for an application that supports the JWT Bearer Token Authorization Grant. The <code>AuthorizedAudience</code> field is the aud claim. For more information, see <a href=\"https://datatracker.ietf.org/doc/html/rfc7523\">RFC 7523</a>.</p>"}, "ListAccountAssignmentCreationStatusRequest": {"type": "structure", "required": ["InstanceArn"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to display for the assignment.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>The pagination token for the list API. Initially the value is null. Use the output of previous API calls to make subsequent calls.</p>"}, "Filter": {"shape": "OperationStatusFilter", "documentation": "<p>Filters results based on the passed attribute value.</p>"}}}, "ListAccountAssignmentCreationStatusResponse": {"type": "structure", "members": {"AccountAssignmentsCreationStatus": {"shape": "AccountAssignmentOperationStatusList", "documentation": "<p>The status object for the account assignment creation operation.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>The pagination token for the list API. Initially the value is null. Use the output of previous API calls to make subsequent calls.</p>"}}}, "ListAccountAssignmentDeletionStatusRequest": {"type": "structure", "required": ["InstanceArn"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to display for the assignment.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>The pagination token for the list API. Initially the value is null. Use the output of previous API calls to make subsequent calls.</p>"}, "Filter": {"shape": "OperationStatusFilter", "documentation": "<p>Filters results based on the passed attribute value.</p>"}}}, "ListAccountAssignmentDeletionStatusResponse": {"type": "structure", "members": {"AccountAssignmentsDeletionStatus": {"shape": "AccountAssignmentOperationStatusList", "documentation": "<p>The status object for the account assignment deletion operation.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>The pagination token for the list API. Initially the value is null. Use the output of previous API calls to make subsequent calls.</p>"}}}, "ListAccountAssignmentsFilter": {"type": "structure", "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The ID number of an Amazon Web Services account that filters the results in the response.</p>"}}, "documentation": "<p>A structure that describes a filter for account assignments.</p>"}, "ListAccountAssignmentsForPrincipalRequest": {"type": "structure", "required": ["InstanceArn", "PrincipalId", "PrincipalType"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>Specifies the ARN of the instance of IAM Identity Center that contains the principal.</p>"}, "PrincipalId": {"shape": "PrincipalId", "documentation": "<p>Specifies the principal for which you want to retrieve the list of account assignments.</p>"}, "PrincipalType": {"shape": "PrincipalType", "documentation": "<p>Specifies the type of the principal.</p>"}, "Filter": {"shape": "ListAccountAssignmentsFilter", "documentation": "<p>Specifies an Amazon Web Services account ID number. Results are filtered to only those that match this ID number.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>Specifies that you want to receive the next page of results. Valid only if you received a <code>NextToken</code> response in the previous request. If you did, it indicates that more output is available. Set this parameter to the value provided by the previous call's <code>NextToken</code> response to request the next page of results.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>Specifies the total number of results that you want included in each response. If additional items exist beyond the number you specify, the <code>NextToken</code> response element is returned with a value (not null). Include the specified value as the <code>NextToken</code> request parameter in the next call to the operation to get the next set of results. Note that the service might return fewer results than the maximum even when there are more results available. You should check <code>NextToken</code> after every operation to ensure that you receive all of the results.</p>"}}}, "ListAccountAssignmentsForPrincipalResponse": {"type": "structure", "members": {"AccountAssignments": {"shape": "AccountAssignmentListForPrincipal", "documentation": "<p>An array list of the account assignments for the principal.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>If present, this value indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>. This indicates that this is the last page of results.</p>"}}}, "ListAccountAssignmentsRequest": {"type": "structure", "required": ["InstanceArn", "AccountId", "PermissionSetArn"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "AccountId": {"shape": "TargetId", "documentation": "<p>The identifier of the Amazon Web Services account from which to list the assignments.</p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the permission set from which to list assignments.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to display for the assignment.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>The pagination token for the list API. Initially the value is null. Use the output of previous API calls to make subsequent calls.</p>"}}}, "ListAccountAssignmentsResponse": {"type": "structure", "members": {"AccountAssignments": {"shape": "AccountAssignmentList", "documentation": "<p>The list of assignments that match the input Amazon Web Services account and permission set.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>The pagination token for the list API. Initially the value is null. Use the output of previous API calls to make subsequent calls.</p>"}}}, "ListAccountsForProvisionedPermissionSetRequest": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the <a>PermissionSet</a> from which the associated Amazon Web Services accounts will be listed.</p>"}, "ProvisioningStatus": {"shape": "ProvisioningStatus", "documentation": "<p>The permission set provisioning status for an Amazon Web Services account.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to display for the <a>PermissionSet</a>.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>The pagination token for the list API. Initially the value is null. Use the output of previous API calls to make subsequent calls.</p>"}}}, "ListAccountsForProvisionedPermissionSetResponse": {"type": "structure", "members": {"AccountIds": {"shape": "AccountList", "documentation": "<p>The list of Amazon Web Services <code>AccountIds</code>.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>The pagination token for the list API. Initially the value is null. Use the output of previous API calls to make subsequent calls.</p>"}}}, "ListApplicationAccessScopesRequest": {"type": "structure", "required": ["ApplicationArn"], "members": {"ApplicationArn": {"shape": "ApplicationArn", "documentation": "<p>Specifies the ARN of the application.</p>"}, "MaxResults": {"shape": "ListApplicationAccessScopesRequestMaxResultsInteger", "documentation": "<p>Specifies the total number of results that you want included in each response. If additional items exist beyond the number you specify, the <code>NextToken</code> response element is returned with a value (not null). Include the specified value as the <code>NextToken</code> request parameter in the next call to the operation to get the next set of results. Note that the service might return fewer results than the maximum even when there are more results available. You should check <code>NextToken</code> after every operation to ensure that you receive all of the results.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>Specifies that you want to receive the next page of results. Valid only if you received a <code>NextToken</code> response in the previous request. If you did, it indicates that more output is available. Set this parameter to the value provided by the previous call's <code>NextToken</code> response to request the next page of results.</p>"}}}, "ListApplicationAccessScopesRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 10, "min": 1}, "ListApplicationAccessScopesResponse": {"type": "structure", "required": ["<PERSON><PERSON><PERSON>"], "members": {"Scopes": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>An array list of access scopes and their authorized targets that are associated with the application.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>If present, this value indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>. This indicates that this is the last page of results.</p>"}}}, "ListApplicationAssignmentsFilter": {"type": "structure", "members": {"ApplicationArn": {"shape": "ApplicationArn", "documentation": "<p>The ARN of an application.</p>"}}, "documentation": "<p>A structure that describes a filter for application assignments.</p>"}, "ListApplicationAssignmentsForPrincipalRequest": {"type": "structure", "required": ["InstanceArn", "PrincipalId", "PrincipalType"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>Specifies the instance of IAM Identity Center that contains principal and applications.</p>"}, "PrincipalId": {"shape": "PrincipalId", "documentation": "<p>Specifies the unique identifier of the principal for which you want to retrieve its assignments.</p>"}, "PrincipalType": {"shape": "PrincipalType", "documentation": "<p>Specifies the type of the principal for which you want to retrieve its assignments.</p>"}, "Filter": {"shape": "ListApplicationAssignmentsFilter", "documentation": "<p>Filters the output to include only assignments associated with the application that has the specified ARN.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>Specifies that you want to receive the next page of results. Valid only if you received a <code>NextToken</code> response in the previous request. If you did, it indicates that more output is available. Set this parameter to the value provided by the previous call's <code>NextToken</code> response to request the next page of results.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>Specifies the total number of results that you want included in each response. If additional items exist beyond the number you specify, the <code>NextToken</code> response element is returned with a value (not null). Include the specified value as the <code>NextToken</code> request parameter in the next call to the operation to get the next set of results. Note that the service might return fewer results than the maximum even when there are more results available. You should check <code>NextToken</code> after every operation to ensure that you receive all of the results.</p>"}}}, "ListApplicationAssignmentsForPrincipalResponse": {"type": "structure", "members": {"ApplicationAssignments": {"shape": "ApplicationAssignmentListForPrincipal", "documentation": "<p>An array list of the application assignments for the specified principal.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>If present, this value indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>. This indicates that this is the last page of results.</p>"}}}, "ListApplicationAssignmentsRequest": {"type": "structure", "required": ["ApplicationArn"], "members": {"ApplicationArn": {"shape": "ApplicationArn", "documentation": "<p>Specifies the ARN of the application.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>Specifies the total number of results that you want included in each response. If additional items exist beyond the number you specify, the <code>NextToken</code> response element is returned with a value (not null). Include the specified value as the <code>NextToken</code> request parameter in the next call to the operation to get the next set of results. Note that the service might return fewer results than the maximum even when there are more results available. You should check <code>NextToken</code> after every operation to ensure that you receive all of the results.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>Specifies that you want to receive the next page of results. Valid only if you received a <code>NextToken</code> response in the previous request. If you did, it indicates that more output is available. Set this parameter to the value provided by the previous call's <code>NextToken</code> response to request the next page of results.</p>"}}}, "ListApplicationAssignmentsResponse": {"type": "structure", "members": {"ApplicationAssignments": {"shape": "ApplicationAssignmentsList", "documentation": "<p>The list of users assigned to an application.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>If present, this value indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>. This indicates that this is the last page of results.</p>"}}}, "ListApplicationAuthenticationMethodsRequest": {"type": "structure", "required": ["ApplicationArn"], "members": {"ApplicationArn": {"shape": "ApplicationArn", "documentation": "<p>Specifies the ARN of the application with the authentication methods you want to list.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>Specifies that you want to receive the next page of results. Valid only if you received a <code>NextToken</code> response in the previous request. If you did, it indicates that more output is available. Set this parameter to the value provided by the previous call's <code>NextToken</code> response to request the next page of results.</p>"}}}, "ListApplicationAuthenticationMethodsResponse": {"type": "structure", "members": {"AuthenticationMethods": {"shape": "AuthenticationMethods", "documentation": "<p>An array list of authentication methods for the specified application.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>If present, this value indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>. This indicates that this is the last page of results.</p>"}}}, "ListApplicationGrantsRequest": {"type": "structure", "required": ["ApplicationArn"], "members": {"ApplicationArn": {"shape": "ApplicationArn", "documentation": "<p>Specifies the ARN of the application whose grants you want to list.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>Specifies that you want to receive the next page of results. Valid only if you received a <code>NextToken</code> response in the previous request. If you did, it indicates that more output is available. Set this parameter to the value provided by the previous call's <code>NextToken</code> response to request the next page of results.</p>"}}}, "ListApplicationGrantsResponse": {"type": "structure", "required": ["<PERSON>s"], "members": {"Grants": {"shape": "<PERSON>s", "documentation": "<p>An array list of structures that describe the requested grants.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>If present, this value indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>. This indicates that this is the last page of results.</p>"}}}, "ListApplicationProvidersRequest": {"type": "structure", "members": {"MaxResults": {"shape": "MaxResults", "documentation": "<p>Specifies the total number of results that you want included in each response. If additional items exist beyond the number you specify, the <code>NextToken</code> response element is returned with a value (not null). Include the specified value as the <code>NextToken</code> request parameter in the next call to the operation to get the next set of results. Note that the service might return fewer results than the maximum even when there are more results available. You should check <code>NextToken</code> after every operation to ensure that you receive all of the results.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>Specifies that you want to receive the next page of results. Valid only if you received a <code>NextToken</code> response in the previous request. If you did, it indicates that more output is available. Set this parameter to the value provided by the previous call's <code>NextToken</code> response to request the next page of results.</p>"}}}, "ListApplicationProvidersResponse": {"type": "structure", "members": {"ApplicationProviders": {"shape": "ApplicationProviderList", "documentation": "<p>An array list of structures that describe application providers.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>If present, this value indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>. This indicates that this is the last page of results.</p>"}}}, "ListApplicationsFilter": {"type": "structure", "members": {"ApplicationAccount": {"shape": "AccountId", "documentation": "<p>An Amazon Web Services account ID number that filters the results in the response.</p>"}, "ApplicationProvider": {"shape": "ApplicationProviderArn", "documentation": "<p>The ARN of an application provider that can filter the results in the response.</p>"}}, "documentation": "<p>A structure that describes a filter for applications.</p>"}, "ListApplicationsRequest": {"type": "structure", "required": ["InstanceArn"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center application under which the operation will run. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>Specifies the total number of results that you want included in each response. If additional items exist beyond the number you specify, the <code>NextToken</code> response element is returned with a value (not null). Include the specified value as the <code>NextToken</code> request parameter in the next call to the operation to get the next set of results. Note that the service might return fewer results than the maximum even when there are more results available. You should check <code>NextToken</code> after every operation to ensure that you receive all of the results.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>Specifies that you want to receive the next page of results. Valid only if you received a <code>NextToken</code> response in the previous request. If you did, it indicates that more output is available. Set this parameter to the value provided by the previous call's <code>NextToken</code> response to request the next page of results.</p>"}, "Filter": {"shape": "ListApplicationsFilter", "documentation": "<p>Filters response results. </p>"}}}, "ListApplicationsResponse": {"type": "structure", "members": {"Applications": {"shape": "ApplicationList", "documentation": "<p>Retrieves all applications associated with the instance.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>If present, this value indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>. This indicates that this is the last page of results.</p>"}}}, "ListCustomerManagedPolicyReferencesInPermissionSetRequest": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. </p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the <code>PermissionSet</code>. </p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to display for the list call.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>The pagination token for the list API. Initially the value is null. Use the output of previous API calls to make subsequent calls.</p>"}}}, "ListCustomerManagedPolicyReferencesInPermissionSetResponse": {"type": "structure", "members": {"CustomerManagedPolicyReferences": {"shape": "CustomerManagedPolicyReferenceList", "documentation": "<p>Specifies the names and paths of the customer managed policies that you have attached to your permission set.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>The pagination token for the list API. Initially the value is null. Use the output of previous API calls to make subsequent calls.</p>"}}}, "ListInstancesRequest": {"type": "structure", "members": {"MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to display for the instance.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>The pagination token for the list API. Initially the value is null. Use the output of previous API calls to make subsequent calls.</p>"}}}, "ListInstancesResponse": {"type": "structure", "members": {"Instances": {"shape": "InstanceList", "documentation": "<p>Lists the IAM Identity Center instances that the caller has access to.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>The pagination token for the list API. Initially the value is null. Use the output of previous API calls to make subsequent calls.</p>"}}}, "ListManagedPoliciesInPermissionSetRequest": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the <a>PermissionSet</a> whose managed policies will be listed.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to display for the <a>PermissionSet</a>.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>The pagination token for the list API. Initially the value is null. Use the output of previous API calls to make subsequent calls.</p>"}}}, "ListManagedPoliciesInPermissionSetResponse": {"type": "structure", "members": {"AttachedManagedPolicies": {"shape": "AttachedManagedPolicyList", "documentation": "<p>An array of the <a>AttachedManagedPolicy</a> data type object.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>The pagination token for the list API. Initially the value is null. Use the output of previous API calls to make subsequent calls.</p>"}}}, "ListPermissionSetProvisioningStatusRequest": {"type": "structure", "required": ["InstanceArn"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to display for the assignment.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>The pagination token for the list API. Initially the value is null. Use the output of previous API calls to make subsequent calls.</p>"}, "Filter": {"shape": "OperationStatusFilter", "documentation": "<p>Filters results based on the passed attribute value.</p>"}}}, "ListPermissionSetProvisioningStatusResponse": {"type": "structure", "members": {"PermissionSetsProvisioningStatus": {"shape": "PermissionSetProvisioningStatusList", "documentation": "<p>The status object for the permission set provisioning operation.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>The pagination token for the list API. Initially the value is null. Use the output of previous API calls to make subsequent calls.</p>"}}}, "ListPermissionSetsProvisionedToAccountRequest": {"type": "structure", "required": ["InstanceArn", "AccountId"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "AccountId": {"shape": "AccountId", "documentation": "<p>The identifier of the Amazon Web Services account from which to list the assignments.</p>"}, "ProvisioningStatus": {"shape": "ProvisioningStatus", "documentation": "<p>The status object for the permission set provisioning operation.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to display for the assignment.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>The pagination token for the list API. Initially the value is null. Use the output of previous API calls to make subsequent calls.</p>"}}}, "ListPermissionSetsProvisionedToAccountResponse": {"type": "structure", "members": {"NextToken": {"shape": "Token", "documentation": "<p>The pagination token for the list API. Initially the value is null. Use the output of previous API calls to make subsequent calls.</p>"}, "PermissionSets": {"shape": "PermissionSetList", "documentation": "<p>Defines the level of access that an Amazon Web Services account has.</p>"}}}, "ListPermissionSetsRequest": {"type": "structure", "required": ["InstanceArn"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>The pagination token for the list API. Initially the value is null. Use the output of previous API calls to make subsequent calls.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to display for the assignment.</p>"}}}, "ListPermissionSetsResponse": {"type": "structure", "members": {"PermissionSets": {"shape": "PermissionSetList", "documentation": "<p>Defines the level of access on an Amazon Web Services account.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>The pagination token for the list API. Initially the value is null. Use the output of previous API calls to make subsequent calls.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["ResourceArn"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "ResourceArn": {"shape": "TaggableResourceArn", "documentation": "<p>The ARN of the resource with the tags to be listed.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>The pagination token for the list API. Initially the value is null. Use the output of previous API calls to make subsequent calls.</p>"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"Tags": {"shape": "TagList", "documentation": "<p>A set of key-value pairs that are used to manage the resource.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>The pagination token for the list API. Initially the value is null. Use the output of previous API calls to make subsequent calls.</p>"}}}, "ListTrustedTokenIssuersRequest": {"type": "structure", "required": ["InstanceArn"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>Specifies the ARN of the instance of IAM Identity Center with the trusted token issuer configurations that you want to list.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>Specifies the total number of results that you want included in each response. If additional items exist beyond the number you specify, the <code>NextToken</code> response element is returned with a value (not null). Include the specified value as the <code>NextToken</code> request parameter in the next call to the operation to get the next set of results. Note that the service might return fewer results than the maximum even when there are more results available. You should check <code>NextToken</code> after every operation to ensure that you receive all of the results.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>Specifies that you want to receive the next page of results. Valid only if you received a <code>NextToken</code> response in the previous request. If you did, it indicates that more output is available. Set this parameter to the value provided by the previous call's <code>NextToken</code> response to request the next page of results.</p>"}}}, "ListTrustedTokenIssuersResponse": {"type": "structure", "members": {"TrustedTokenIssuers": {"shape": "TrustedTokenIssuerList", "documentation": "<p>An array list of the trusted token issuer configurations.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>If present, this value indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>. This indicates that this is the last page of results.</p>"}}}, "ManagedPolicyArn": {"type": "string", "max": 2048, "min": 20, "pattern": "arn:(aws|aws-us-gov|aws-cn|aws-iso|aws-iso-b):iam::aws:policy((/[A-Za-z0-9\\.,\\+@=_-]+)*)/([A-Za-z0-9\\.,\\+=@_-]+)"}, "ManagedPolicyName": {"type": "string", "max": 128, "min": 1, "pattern": "[\\w+=,.@-]+"}, "ManagedPolicyPath": {"type": "string", "max": 512, "min": 1, "pattern": "((/[A-Za-z0-9\\.,\\+@=_-]+)*)/"}, "MaxResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "Name": {"type": "string", "max": 100, "min": 1}, "NameType": {"type": "string", "max": 255, "min": 0, "pattern": "[\\w+=,.@-]+"}, "OidcJwtConfiguration": {"type": "structure", "required": ["IssuerUrl", "ClaimAttributePath", "IdentityStoreAttributePath", "JwksRetrievalOption"], "members": {"IssuerUrl": {"shape": "TrustedTokenIssuerUrl", "documentation": "<p>The URL that IAM Identity Center uses for OpenID Discovery. OpenID Discovery is used to obtain the information required to verify the tokens that the trusted token issuer generates.</p>"}, "ClaimAttributePath": {"shape": "ClaimAttributePath", "documentation": "<p>The path of the source attribute in the JWT from the trusted token issuer. The attribute mapped by this JMESPath expression is compared against the attribute mapped by <code>IdentityStoreAttributePath</code> when a trusted token issuer token is exchanged for an IAM Identity Center token.</p>"}, "IdentityStoreAttributePath": {"shape": "JMESPath", "documentation": "<p>The path of the destination attribute in a JWT from IAM Identity Center. The attribute mapped by this JMESPath expression is compared against the attribute mapped by <code>ClaimAttributePath</code> when a trusted token issuer token is exchanged for an IAM Identity Center token. </p>"}, "JwksRetrievalOption": {"shape": "JwksRetrievalOption", "documentation": "<p>The method that the trusted token issuer can use to retrieve the JSON Web Key Set used to verify a JWT.</p>"}}, "documentation": "<p>A structure that describes configuration settings for a trusted token issuer that supports OpenID Connect (OIDC) and JSON Web Tokens (JWTs).</p>"}, "OidcJwtUpdateConfiguration": {"type": "structure", "members": {"ClaimAttributePath": {"shape": "ClaimAttributePath", "documentation": "<p>The path of the source attribute in the JWT from the trusted token issuer. The attribute mapped by this JMESPath expression is compared against the attribute mapped by <code>IdentityStoreAttributePath</code> when a trusted token issuer token is exchanged for an IAM Identity Center token.</p>"}, "IdentityStoreAttributePath": {"shape": "JMESPath", "documentation": "<p>The path of the destination attribute in a JWT from IAM Identity Center. The attribute mapped by this JMESPath expression is compared against the attribute mapped by <code>ClaimAttributePath</code> when a trusted token issuer token is exchanged for an IAM Identity Center token.</p>"}, "JwksRetrievalOption": {"shape": "JwksRetrievalOption", "documentation": "<p>The method that the trusted token issuer can use to retrieve the JSON Web Key Set used to verify a JWT.</p>"}}, "documentation": "<p>A structure that describes updated configuration settings for a trusted token issuer that supports OpenID Connect (OIDC) and JSON Web Tokens (JWTs).</p>"}, "OperationStatusFilter": {"type": "structure", "members": {"Status": {"shape": "StatusValues", "documentation": "<p>Filters the list operations result based on the status attribute.</p>"}}, "documentation": "<p>Filters the operation status list based on the passed attribute value.</p>"}, "PermissionSet": {"type": "structure", "members": {"Name": {"shape": "PermissionSetName", "documentation": "<p>The name of the permission set.</p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the permission set. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "Description": {"shape": "PermissionSetDescription", "documentation": "<p>The description of the <a>PermissionSet</a>.</p>"}, "CreatedDate": {"shape": "Date", "documentation": "<p>The date that the permission set was created.</p>"}, "SessionDuration": {"shape": "Duration", "documentation": "<p>The length of time that the application user sessions are valid for in the ISO-8601 standard.</p>"}, "RelayState": {"shape": "RelayState", "documentation": "<p>Used to redirect users within the application during the federation authentication process.</p>"}}, "documentation": "<p>An entity that contains IAM policies.</p>"}, "PermissionSetArn": {"type": "string", "max": 1224, "min": 10, "pattern": "arn:(aws|aws-us-gov|aws-cn|aws-iso|aws-iso-b):sso:::permissionSet/(sso)?ins-[a-zA-Z0-9-.]{16}/ps-[a-zA-Z0-9-./]{16}"}, "PermissionSetDescription": {"type": "string", "max": 700, "min": 1, "pattern": "[\\u0009\\u000A\\u000D\\u0020-\\u007E\\u00A1-\\u00FF]*"}, "PermissionSetList": {"type": "list", "member": {"shape": "PermissionSetArn"}}, "PermissionSetName": {"type": "string", "max": 32, "min": 1, "pattern": "[\\w+=,.@-]+"}, "PermissionSetPolicyDocument": {"type": "string", "max": 32768, "min": 1, "pattern": "[\\u0009\\u000A\\u000D\\u0020-\\u00FF]+"}, "PermissionSetProvisioningStatus": {"type": "structure", "members": {"Status": {"shape": "StatusValues", "documentation": "<p>The status of the permission set provisioning process.</p>"}, "RequestId": {"shape": "UUId", "documentation": "<p>The identifier for tracking the request operation that is generated by the universally unique identifier (UUID) workflow.</p>"}, "AccountId": {"shape": "AccountId", "documentation": "<p>The identifier of the Amazon Web Services account from which to list the assignments.</p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the permission set that is being provisioned. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "FailureReason": {"shape": "Reason", "documentation": "<p>The message that contains an error or exception in case of an operation failure.</p>"}, "CreatedDate": {"shape": "Date", "documentation": "<p>The date that the permission set was created.</p>"}}, "documentation": "<p>A structure that is used to provide the status of the provisioning operation for a specified permission set.</p>"}, "PermissionSetProvisioningStatusList": {"type": "list", "member": {"shape": "PermissionSetProvisioningStatusMetadata"}}, "PermissionSetProvisioningStatusMetadata": {"type": "structure", "members": {"Status": {"shape": "StatusValues", "documentation": "<p>The status of the permission set provisioning process.</p>"}, "RequestId": {"shape": "UUId", "documentation": "<p>The identifier for tracking the request operation that is generated by the universally unique identifier (UUID) workflow.</p>"}, "CreatedDate": {"shape": "Date", "documentation": "<p>The date that the permission set was created.</p>"}}, "documentation": "<p>Provides information about the permission set provisioning status.</p>"}, "PermissionsBoundary": {"type": "structure", "members": {"CustomerManagedPolicyReference": {"shape": "CustomerManagedPolicyReference", "documentation": "<p>Specifies the name and path of a customer managed policy. You must have an IAM policy that matches the name and path in each Amazon Web Services account where you want to deploy your permission set.</p>"}, "ManagedPolicyArn": {"shape": "ManagedPolicyArn", "documentation": "<p>The Amazon Web Services managed policy ARN that you want to attach to a permission set as a permissions boundary.</p>"}}, "documentation": "<p>Specifies the configuration of the Amazon Web Services managed or customer managed policy that you want to set as a permissions boundary. Specify either <code>CustomerManagedPolicyReference</code> to use the name and path of a customer managed policy, or <code>ManagedPolicyArn</code> to use the ARN of an Amazon Web Services managed policy. A permissions boundary represents the maximum permissions that any policy can grant your role. For more information, see <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/access_policies_boundaries.html\">Permissions boundaries for IAM entities</a> in the <i>IAM User Guide</i>.</p> <important> <p>Policies used as permissions boundaries don't provide permissions. You must also attach an IAM policy to the role. To learn how the effective permissions for a role are evaluated, see <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/reference_policies_evaluation-logic.html\">IAM JSON policy evaluation logic</a> in the <i>IAM User Guide</i>.</p> </important>"}, "PortalOptions": {"type": "structure", "members": {"SignInOptions": {"shape": "SignInOptions", "documentation": "<p>A structure that describes the sign-in options for the access portal.</p>"}, "Visibility": {"shape": "ApplicationVisibility", "documentation": "<p>Indicates whether this application is visible in the access portal.</p>"}}, "documentation": "<p>A structure that describes the options for the access portal associated with an application.</p>"}, "PrincipalId": {"type": "string", "max": 47, "min": 1, "pattern": "([0-9a-f]{10}-|)[A-Fa-f0-9]{8}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{12}"}, "PrincipalType": {"type": "string", "enum": ["USER", "GROUP"]}, "ProvisionPermissionSetRequest": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn", "TargetType"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the permission set.</p>"}, "TargetId": {"shape": "TargetId", "documentation": "<p>TargetID is an Amazon Web Services account identifier, (For example, ************).</p>"}, "TargetType": {"shape": "ProvisionTargetType", "documentation": "<p>The entity type for which the assignment will be created.</p>"}}}, "ProvisionPermissionSetResponse": {"type": "structure", "members": {"PermissionSetProvisioningStatus": {"shape": "PermissionSetProvisioningStatus", "documentation": "<p>The status object for the permission set provisioning operation.</p>"}}}, "ProvisionTargetType": {"type": "string", "enum": ["AWS_ACCOUNT", "ALL_PROVISIONED_ACCOUNTS"]}, "ProvisioningStatus": {"type": "string", "enum": ["LATEST_PERMISSION_SET_PROVISIONED", "LATEST_PERMISSION_SET_NOT_PROVISIONED"]}, "PutApplicationAccessScopeRequest": {"type": "structure", "required": ["<PERSON><PERSON>", "ApplicationArn"], "members": {"Scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>Specifies the name of the access scope to be associated with the specified targets.</p>"}, "AuthorizedTargets": {"shape": "ScopeTargets", "documentation": "<p>Specifies an array list of ARNs that represent the authorized targets for this access scope.</p>"}, "ApplicationArn": {"shape": "ApplicationArn", "documentation": "<p>Specifies the ARN of the application with the access scope with the targets to add or update.</p>"}}}, "PutApplicationAssignmentConfigurationRequest": {"type": "structure", "required": ["ApplicationArn", "AssignmentRequired"], "members": {"ApplicationArn": {"shape": "ApplicationArn", "documentation": "<p>Specifies the ARN of the application. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "AssignmentRequired": {"shape": "AssignmentRequired", "documentation": "<p>If <code>AssignmentsRequired</code> is <code>true</code> (default value), users don’t have access to the application unless an assignment is created using the <a href=\"https://docs.aws.amazon.com/singlesignon/latest/APIReference/API_CreateApplicationAssignment.html\">CreateApplicationAssignment API</a>. If <code>false</code>, all users have access to the application. </p>"}}}, "PutApplicationAssignmentConfigurationResponse": {"type": "structure", "members": {}}, "PutApplicationAuthenticationMethodRequest": {"type": "structure", "required": ["ApplicationArn", "AuthenticationMethodType", "AuthenticationMethod"], "members": {"ApplicationArn": {"shape": "ApplicationArn", "documentation": "<p>Specifies the ARN of the application with the authentication method to add or update.</p>"}, "AuthenticationMethodType": {"shape": "AuthenticationMethodType", "documentation": "<p>Specifies the type of the authentication method that you want to add or update.</p>"}, "AuthenticationMethod": {"shape": "AuthenticationMethod", "documentation": "<p>Specifies a structure that describes the authentication method to add or update. The structure type you provide is determined by the <code>AuthenticationMethodType</code> parameter.</p>"}}}, "PutApplicationGrantRequest": {"type": "structure", "required": ["ApplicationArn", "GrantType", "<PERSON>"], "members": {"ApplicationArn": {"shape": "ApplicationArn", "documentation": "<p>Specifies the ARN of the application to update.</p>"}, "GrantType": {"shape": "GrantType", "documentation": "<p>Specifies the type of grant to update.</p>"}, "Grant": {"shape": "<PERSON>", "documentation": "<p>Specifies a structure that describes the grant to update.</p>"}}}, "PutInlinePolicyToPermissionSetRequest": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn", "InlinePolicy"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the permission set.</p>"}, "InlinePolicy": {"shape": "PermissionSetPolicyDocument", "documentation": "<p>The inline policy to attach to a <a>PermissionSet</a>.</p>"}}}, "PutInlinePolicyToPermissionSetResponse": {"type": "structure", "members": {}}, "PutPermissionsBoundaryToPermissionSetRequest": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn", "PermissionsBoundary"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. </p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the <code>PermissionSet</code>.</p>"}, "PermissionsBoundary": {"shape": "PermissionsBoundary", "documentation": "<p>The permissions boundary that you want to attach to a <code>PermissionSet</code>.</p>"}}}, "PutPermissionsBoundaryToPermissionSetResponse": {"type": "structure", "members": {}}, "Reason": {"type": "string", "pattern": "[\\p{L}\\p{M}\\p{Z}\\p{S}\\p{N}\\p{P}]*"}, "RedirectUris": {"type": "list", "member": {"shape": "URI"}, "max": 10, "min": 1}, "RefreshTokenGrant": {"type": "structure", "members": {}, "documentation": "<p>A structure that defines configuration settings for an application that supports the OAuth 2.0 Refresh Token Grant. For more, see <a href=\"https://datatracker.ietf.org/doc/html/rfc6749#section-1.5\">RFC 6749</a>.</p>"}, "RelayState": {"type": "string", "max": 240, "min": 1, "pattern": "[a-zA-Z0-9&$@#\\\\\\/%?=~\\-_'\"|!:,.;*+\\[\\]\\ \\(\\)\\{\\}]+"}, "ResourceNotFoundException": {"type": "structure", "members": {"Message": {"shape": "ResourceNotFoundMessage"}}, "documentation": "<p>Indicates that a requested resource is not found.</p>", "exception": true}, "ResourceNotFoundMessage": {"type": "string"}, "ResourceServerConfig": {"type": "structure", "members": {"Scopes": {"shape": "ResourceServerScopes", "documentation": "<p>A list of the IAM Identity Center access scopes that are associated with this resource server.</p>"}}, "documentation": "<p>A structure that describes the configuration of a resource server.</p>"}, "ResourceServerScope": {"type": "string", "max": 80, "min": 1, "pattern": "[^:=\\-\\.\\s][0-9a-zA-Z_:\\-\\.]+"}, "ResourceServerScopeDetails": {"type": "structure", "members": {"LongDescription": {"shape": "Description", "documentation": "<p>The description of an access scope for a resource server.</p>"}, "DetailedTitle": {"shape": "Description", "documentation": "<p>The title of an access scope for a resource server.</p>"}}, "documentation": "<p>A structure that describes details for an IAM Identity Center access scope that is associated with a resource server.</p>"}, "ResourceServerScopes": {"type": "map", "key": {"shape": "ResourceServerScope"}, "value": {"shape": "ResourceServerScopeDetails"}}, "Scope": {"type": "string", "pattern": "([A-Za-z0-9_]{1,50})(:[A-Za-z0-9_]{1,50}){0,1}(:[A-Za-z0-9_]{1,50}){0,1}"}, "ScopeDetails": {"type": "structure", "required": ["<PERSON><PERSON>"], "members": {"Scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>The name of the access scope.</p>"}, "AuthorizedTargets": {"shape": "ScopeTargets", "documentation": "<p>An array list of ARNs of applications.</p>"}}, "documentation": "<p>A structure that describes an IAM Identity Center access scope and its authorized targets.</p>"}, "ScopeTarget": {"type": "string", "max": 100, "min": 1, "pattern": "arn:(aws|aws-us-gov|aws-cn|aws-iso|aws-iso-b):sso::(\\d{12}:application/(sso)?ins-[a-zA-Z0-9-.]{16}/apl-[a-zA-Z0-9]{16}|:instance/(sso)?ins-[a-zA-Z0-9-.]{16})"}, "ScopeTargets": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON>ar<PERSON>"}, "max": 10, "min": 1}, "Scopes": {"type": "list", "member": {"shape": "ScopeDetails"}, "max": 10, "min": 0}, "ServiceQuotaExceededException": {"type": "structure", "members": {"Message": {"shape": "ServiceQuotaExceededMessage"}}, "documentation": "<p>Indicates that the principal has crossed the permitted number of resources that can be created.</p>", "exception": true}, "ServiceQuotaExceededMessage": {"type": "string"}, "SignInOptions": {"type": "structure", "required": ["Origin"], "members": {"Origin": {"shape": "SignInOrigin", "documentation": "<p>This determines how IAM Identity Center navigates the user to the target application. It can be one of the following values:</p> <ul> <li> <p> <code>APPLICATION</code>: IAM Identity Center redirects the customer to the configured <code>ApplicationUrl</code>.</p> </li> <li> <p> <code>IDENTITY_CENTER</code>: IAM Identity Center uses SAML identity-provider initiated authentication to sign the customer directly into a SAML-based application.</p> </li> </ul>"}, "ApplicationUrl": {"shape": "ApplicationUrl", "documentation": "<p>The URL that accepts authentication requests for an application. This is a required parameter if the <code>Origin</code> parameter is <code>APPLICATION</code>.</p>"}}, "documentation": "<p>A structure that describes the sign-in options for an application portal.</p>"}, "SignInOrigin": {"type": "string", "enum": ["IDENTITY_CENTER", "APPLICATION"]}, "StatusValues": {"type": "string", "enum": ["IN_PROGRESS", "FAILED", "SUCCEEDED"]}, "Tag": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {"shape": "TagKey", "documentation": "<p>The key for the tag.</p>"}, "Value": {"shape": "TagValue", "documentation": "<p>The value of the tag.</p>"}}, "documentation": "<p>A set of key-value pairs that are used to manage the resource. Tags can only be applied to permission sets and cannot be applied to corresponding roles that IAM Identity Center creates in Amazon Web Services accounts.</p>"}, "TagKey": {"type": "string", "max": 128, "min": 1, "pattern": "([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)"}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 75, "min": 1}, "TagList": {"type": "list", "member": {"shape": "Tag"}, "max": 75, "min": 0}, "TagResourceRequest": {"type": "structure", "required": ["ResourceArn", "Tags"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "ResourceArn": {"shape": "TaggableResourceArn", "documentation": "<p>The ARN of the resource with the tags to be listed.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>A set of key-value pairs that are used to manage the resource.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0, "pattern": "([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)"}, "TaggableResourceArn": {"type": "string", "max": 2048, "min": 10, "pattern": "arn:(aws|aws-us-gov|aws-cn|aws-iso|aws-iso-b):sso::((:instance/(sso)?ins-[a-zA-Z0-9-.]{16})|(:permissionSet/(sso)?ins-[a-zA-Z0-9-.]{16}/ps-[a-zA-Z0-9-./]{16})|(\\d{12}:application/(sso)?ins-[a-zA-Z0-9-.]{16}/apl-[a-zA-Z0-9]{16})|(\\d{12}:trustedTokenIssuer/(sso)?ins-[a-zA-Z0-9-.]{16}/tti-[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}))"}, "TargetId": {"type": "string", "max": 12, "min": 12, "pattern": "\\d{12}"}, "TargetType": {"type": "string", "enum": ["AWS_ACCOUNT"]}, "ThrottlingException": {"type": "structure", "members": {"Message": {"shape": "ThrottlingExceptionMessage"}}, "documentation": "<p>Indicates that the principal has crossed the throttling limits of the API operations.</p>", "exception": true}, "ThrottlingExceptionMessage": {"type": "string"}, "Token": {"type": "string", "max": 2048, "min": 0, "pattern": "[-a-zA-Z0-9+=/_]*"}, "TokenExchangeGrant": {"type": "structure", "members": {}, "documentation": "<p>A structure that defines configuration settings for an application that supports the OAuth 2.0 Token Exchange Grant. For more information, see <a href=\"https://datatracker.ietf.org/doc/html/rfc8693\">RFC 8693</a>.</p>"}, "TokenIssuerAudience": {"type": "string", "max": 512, "min": 1}, "TokenIssuerAudiences": {"type": "list", "member": {"shape": "TokenIssuerAudience"}, "max": 10, "min": 1}, "TrustedTokenIssuerArn": {"type": "string", "max": 1224, "min": 10, "pattern": "arn:(aws|aws-us-gov|aws-cn|aws-iso|aws-iso-b):sso::\\d{12}:trustedTokenIssuer/(sso)?ins-[a-zA-Z0-9-.]{16}/tti-[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}"}, "TrustedTokenIssuerConfiguration": {"type": "structure", "members": {"OidcJwtConfiguration": {"shape": "OidcJwtConfiguration", "documentation": "<p>A structure that describes the settings for a trusted token issuer that works with OpenID Connect (OIDC) by using JSON Web Tokens (JWT).</p>"}}, "documentation": "<p>A structure that describes the configuration of a trusted token issuer. The structure and available settings are determined by the type of the trusted token issuer.</p>", "union": true}, "TrustedTokenIssuerList": {"type": "list", "member": {"shape": "TrustedTokenIssuerMetadata"}}, "TrustedTokenIssuerMetadata": {"type": "structure", "members": {"TrustedTokenIssuerArn": {"shape": "TrustedTokenIssuerArn", "documentation": "<p>The ARN of the trusted token issuer configuration in the instance of IAM Identity Center.</p>"}, "Name": {"shape": "TrustedTokenIssuerName", "documentation": "<p>The name of the trusted token issuer configuration in the instance of IAM Identity Center.</p>"}, "TrustedTokenIssuerType": {"shape": "TrustedTokenIssuerType", "documentation": "<p>The type of trusted token issuer.</p>"}}, "documentation": "<p>A structure that describes a trusted token issuer.</p>"}, "TrustedTokenIssuerName": {"type": "string", "max": 255, "min": 1, "pattern": "[\\w+=,.@-]+"}, "TrustedTokenIssuerType": {"type": "string", "enum": ["OIDC_JWT"]}, "TrustedTokenIssuerUpdateConfiguration": {"type": "structure", "members": {"OidcJwtConfiguration": {"shape": "OidcJwtUpdateConfiguration", "documentation": "<p>A structure that describes an updated configuration for a trusted token issuer that uses OpenID Connect (OIDC) with JSON web tokens (JWT).</p>"}}, "documentation": "<p>A structure that contains details to be updated for a trusted token issuer configuration. The structure and settings that you can include depend on the type of the trusted token issuer being updated.</p>", "union": true}, "TrustedTokenIssuerUrl": {"type": "string", "max": 512, "min": 1, "pattern": "https?:\\/\\/[-a-zA-Z0-9+&@\\/%=~_|!:,.;]*[-a-zA-Z0-9+&@\\/%=~_|]"}, "URI": {"type": "string"}, "UUId": {"type": "string", "max": 36, "min": 36, "pattern": "\\b[0-9a-f]{8}\\b-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-\\b[0-9a-f]{12}\\b"}, "UntagResourceRequest": {"type": "structure", "required": ["ResourceArn", "TagKeys"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "ResourceArn": {"shape": "TaggableResourceArn", "documentation": "<p>The ARN of the resource with the tags to be listed.</p>"}, "TagKeys": {"shape": "TagKeyList", "documentation": "<p>The keys of tags that are attached to the resource.</p>"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateApplicationPortalOptions": {"type": "structure", "members": {"SignInOptions": {"shape": "SignInOptions"}}, "documentation": "<p>A structure that describes the options for the access portal associated with an application that can be updated.</p>"}, "UpdateApplicationRequest": {"type": "structure", "required": ["ApplicationArn"], "members": {"ApplicationArn": {"shape": "ApplicationArn", "documentation": "<p>Specifies the ARN of the application. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "Name": {"shape": "ApplicationNameType", "documentation": "<p>Specifies the updated name for the application.</p>"}, "Description": {"shape": "Description", "documentation": "<p>The description of the .</p>"}, "Status": {"shape": "ApplicationStatus", "documentation": "<p>Specifies whether the application is enabled or disabled.</p>"}, "PortalOptions": {"shape": "UpdateApplicationPortalOptions", "documentation": "<p>A structure that describes the options for the portal associated with an application.</p>"}}}, "UpdateApplicationResponse": {"type": "structure", "members": {}}, "UpdateInstanceAccessControlAttributeConfigurationRequest": {"type": "structure", "required": ["InstanceArn", "InstanceAccessControlAttributeConfiguration"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed.</p>"}, "InstanceAccessControlAttributeConfiguration": {"shape": "InstanceAccessControlAttributeConfiguration", "documentation": "<p>Updates the attributes for your ABAC configuration.</p>"}}}, "UpdateInstanceAccessControlAttributeConfigurationResponse": {"type": "structure", "members": {}}, "UpdateInstanceRequest": {"type": "structure", "required": ["Name", "InstanceArn"], "members": {"Name": {"shape": "NameType", "documentation": "<p>Updates the instance name.</p>"}, "InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the instance of IAM Identity Center under which the operation will run. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}}}, "UpdateInstanceResponse": {"type": "structure", "members": {}}, "UpdatePermissionSetRequest": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>The ARN of the IAM Identity Center instance under which the operation will be executed. For more information about ARNs, see <a href=\"/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs) and Amazon Web Services Service Namespaces</a> in the <i>Amazon Web Services General Reference</i>.</p>"}, "PermissionSetArn": {"shape": "PermissionSetArn", "documentation": "<p>The ARN of the permission set.</p>"}, "Description": {"shape": "PermissionSetDescription", "documentation": "<p>The description of the <a>PermissionSet</a>.</p>"}, "SessionDuration": {"shape": "Duration", "documentation": "<p>The length of time that the application user sessions are valid for in the ISO-8601 standard.</p>"}, "RelayState": {"shape": "RelayState", "documentation": "<p>Used to redirect users within the application during the federation authentication process.</p>"}}}, "UpdatePermissionSetResponse": {"type": "structure", "members": {}}, "UpdateTrustedTokenIssuerRequest": {"type": "structure", "required": ["TrustedTokenIssuerArn"], "members": {"TrustedTokenIssuerArn": {"shape": "TrustedTokenIssuerArn", "documentation": "<p>Specifies the ARN of the trusted token issuer configuration that you want to update.</p>"}, "Name": {"shape": "TrustedTokenIssuerName", "documentation": "<p>Specifies the updated name to be applied to the trusted token issuer configuration.</p>"}, "TrustedTokenIssuerConfiguration": {"shape": "TrustedTokenIssuerUpdateConfiguration", "documentation": "<p>Specifies a structure with settings to apply to the specified trusted token issuer. The settings that you can provide are determined by the type of the trusted token issuer that you are updating.</p>"}}}, "UpdateTrustedTokenIssuerResponse": {"type": "structure", "members": {}}, "ValidationException": {"type": "structure", "members": {"Message": {"shape": "ValidationExceptionMessage"}}, "documentation": "<p>The request failed because it contains a syntax error.</p>", "exception": true}, "ValidationExceptionMessage": {"type": "string"}}, "documentation": "<p>IAM Identity Center is the Amazon Web Services solution for connecting your workforce users to Amazon Web Services managed applications and other Amazon Web Services resources. You can connect your existing identity provider and synchronize users and groups from your directory, or create and manage your users directly in IAM Identity Center. You can then use IAM Identity Center for either or both of the following:</p> <ul> <li> <p>User access to applications</p> </li> <li> <p>User access to Amazon Web Services accounts</p> </li> </ul> <p>This guide provides information about single sign-on operations that you can use for access to applications and Amazon Web Services accounts. For information about IAM Identity Center features, see the <a href=\"https://docs.aws.amazon.com/singlesignon/latest/userguide/what-is.html\">IAM Identity Center User Guide</a>.</p> <note> <p>IAM Identity Center uses the <code>sso</code> and <code>identitystore</code> API namespaces.</p> </note> <p>Many API operations for IAM Identity Center rely on identifiers for users and groups, known as principals. For more information about how to work with principals and principal IDs in IAM Identity Center, see the <a href=\"https://docs.aws.amazon.com/singlesignon/latest/IdentityStoreAPIReference/welcome.html\">Identity Store API Reference</a>.</p> <note> <p>Amazon Web Services provides SDKs that consist of libraries and sample code for various programming languages and platforms (Java, Ruby, .Net, iOS, Android, and more). The SDKs provide a convenient way to create programmatic access to IAM Identity Center and other Amazon Web Services services. For more information about the Amazon Web Services SDKs, including how to download and install them, see <a href=\"http://aws.amazon.com/tools/\">Tools for Amazon Web Services</a>.</p> </note>"}