{"version": "2.0", "metadata": {"apiVersion": "2018-11-15", "endpointPrefix": "backup", "jsonVersion": "1.1", "protocol": "rest-json", "protocols": ["rest-json"], "serviceFullName": "AWS Backup", "serviceId": "Backup", "signatureVersion": "v4", "uid": "backup-2018-11-15", "auth": ["aws.auth#sigv4"]}, "operations": {"AssociateBackupVaultMpaApprovalTeam": {"name": "AssociateBackupVaultMpaApprovalTeam", "http": {"method": "PUT", "requestUri": "/backup-vaults/{backupVaultName}/mpaApprovalTeam", "responseCode": 204}, "input": {"shape": "AssociateBackupVaultMpaApprovalTeamInput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "InvalidRequestException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Associates an MPA approval team with a backup vault.</p>"}, "CancelLegalHold": {"name": "CancelLegalHold", "http": {"method": "DELETE", "requestUri": "/legal-holds/{legalHoldId}", "responseCode": 201}, "input": {"shape": "CancelLegalHoldInput"}, "output": {"shape": "CancelLegalHoldOutput"}, "errors": [{"shape": "InvalidParameterValueException"}, {"shape": "InvalidResourceStateException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Removes the specified legal hold on a recovery point. This action can only be performed by a user with sufficient permissions.</p>", "idempotent": true}, "CreateBackupPlan": {"name": "CreateBackupPlan", "http": {"method": "PUT", "requestUri": "/backup/plans/"}, "input": {"shape": "CreateBackupPlanInput"}, "output": {"shape": "CreateBackupPlanOutput"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "AlreadyExistsException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Creates a backup plan using a backup plan name and backup rules. A backup plan is a document that contains information that Backup uses to schedule tasks that create recovery points for resources.</p> <p>If you call <code>CreateBackupPlan</code> with a plan that already exists, you receive an <code>AlreadyExistsException</code> exception.</p>", "idempotent": true}, "CreateBackupSelection": {"name": "CreateBackupSelection", "http": {"method": "PUT", "requestUri": "/backup/plans/{backupPlanId}/selections/"}, "input": {"shape": "CreateBackupSelectionInput"}, "output": {"shape": "CreateBackupSelectionOutput"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "AlreadyExistsException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Creates a JSON document that specifies a set of resources to assign to a backup plan. For examples, see <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/assigning-resources.html#assigning-resources-json\">Assigning resources programmatically</a>. </p>", "idempotent": true}, "CreateBackupVault": {"name": "CreateBackupVault", "http": {"method": "PUT", "requestUri": "/backup-vaults/{backupVaultName}"}, "input": {"shape": "CreateBackupVaultInput"}, "output": {"shape": "CreateBackupVaultOutput"}, "errors": [{"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}, {"shape": "LimitExceededException"}, {"shape": "AlreadyExistsException"}], "documentation": "<p>Creates a logical container where backups are stored. A <code>CreateBackupVault</code> request includes a name, optionally one or more resource tags, an encryption key, and a request ID.</p> <note> <p>Do not include sensitive data, such as passport numbers, in the name of a backup vault.</p> </note>", "idempotent": true}, "CreateFramework": {"name": "CreateFramework", "http": {"method": "POST", "requestUri": "/audit/frameworks"}, "input": {"shape": "CreateFrameworkInput"}, "output": {"shape": "CreateFrameworkOutput"}, "errors": [{"shape": "AlreadyExistsException"}, {"shape": "LimitExceededException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Creates a framework with one or more controls. A framework is a collection of controls that you can use to evaluate your backup practices. By using pre-built customizable controls to define your policies, you can evaluate whether your backup practices comply with your policies and which resources are not yet in compliance.</p>", "idempotent": true}, "CreateLegalHold": {"name": "CreateLegalHold", "http": {"method": "POST", "requestUri": "/legal-holds/"}, "input": {"shape": "CreateLegalHoldInput"}, "output": {"shape": "CreateLegalHoldOutput"}, "errors": [{"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Creates a legal hold on a recovery point (backup). A legal hold is a restraint on altering or deleting a backup until an authorized user cancels the legal hold. Any actions to delete or disassociate a recovery point will fail with an error if one or more active legal holds are on the recovery point.</p>", "idempotent": true}, "CreateLogicallyAirGappedBackupVault": {"name": "CreateLogicallyAirGappedBackupVault", "http": {"method": "PUT", "requestUri": "/logically-air-gapped-backup-vaults/{backupVaultName}"}, "input": {"shape": "CreateLogicallyAirGappedBackupVaultInput"}, "output": {"shape": "CreateLogicallyAirGappedBackupVaultOutput"}, "errors": [{"shape": "AlreadyExistsException"}, {"shape": "InvalidParameterValueException"}, {"shape": "LimitExceededException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Creates a logical container to where backups may be copied.</p> <p>This request includes a name, the Region, the maximum number of retention days, the minimum number of retention days, and optionally can include tags and a creator request ID.</p> <note> <p>Do not include sensitive data, such as passport numbers, in the name of a backup vault.</p> </note>", "idempotent": true}, "CreateReportPlan": {"name": "CreateReportPlan", "http": {"method": "POST", "requestUri": "/audit/report-plans"}, "input": {"shape": "CreateReportPlanInput"}, "output": {"shape": "CreateReportPlanOutput"}, "errors": [{"shape": "AlreadyExistsException"}, {"shape": "LimitExceededException"}, {"shape": "InvalidParameterValueException"}, {"shape": "ServiceUnavailableException"}, {"shape": "MissingParameterValueException"}], "documentation": "<p>Creates a report plan. A report plan is a document that contains information about the contents of the report and where <PERSON><PERSON> will deliver it.</p> <p>If you call <code>CreateReportPlan</code> with a plan that already exists, you receive an <code>AlreadyExistsException</code> exception.</p>", "idempotent": true}, "CreateRestoreAccessBackupVault": {"name": "CreateRestoreAccessBackupVault", "http": {"method": "PUT", "requestUri": "/restore-access-backup-vaults"}, "input": {"shape": "CreateRestoreAccessBackupVaultInput"}, "output": {"shape": "CreateRestoreAccessBackupVaultOutput"}, "errors": [{"shape": "AlreadyExistsException"}, {"shape": "InvalidParameterValueException"}, {"shape": "LimitExceededException"}, {"shape": "MissingParameterValueException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidRequestException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Creates a restore access backup vault that provides temporary access to recovery points in a logically air-gapped backup vault, subject to MPA approval.</p>", "idempotent": true}, "CreateRestoreTestingPlan": {"name": "CreateRestoreTestingPlan", "http": {"method": "PUT", "requestUri": "/restore-testing/plans", "responseCode": 201}, "input": {"shape": "CreateRestoreTestingPlanInput"}, "output": {"shape": "CreateRestoreTestingPlanOutput"}, "errors": [{"shape": "AlreadyExistsException"}, {"shape": "ConflictException"}, {"shape": "InvalidParameterValueException"}, {"shape": "LimitExceededException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Creates a restore testing plan.</p> <p>The first of two steps to create a restore testing plan. After this request is successful, finish the procedure using CreateRestoreTestingSelection.</p>", "idempotent": true}, "CreateRestoreTestingSelection": {"name": "CreateRestoreTestingSelection", "http": {"method": "PUT", "requestUri": "/restore-testing/plans/{RestoreTestingPlanName}/selections", "responseCode": 201}, "input": {"shape": "CreateRestoreTestingSelectionInput"}, "output": {"shape": "CreateRestoreTestingSelectionOutput"}, "errors": [{"shape": "AlreadyExistsException"}, {"shape": "InvalidParameterValueException"}, {"shape": "LimitExceededException"}, {"shape": "MissingParameterValueException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>This request can be sent after CreateRestoreTestingPlan request returns successfully. This is the second part of creating a resource testing plan, and it must be completed sequentially.</p> <p>This consists of <code>RestoreTestingSelectionName</code>, <code>ProtectedResourceType</code>, and one of the following:</p> <ul> <li> <p> <code>ProtectedResourceArns</code> </p> </li> <li> <p> <code>ProtectedResourceConditions</code> </p> </li> </ul> <p>Each protected resource type can have one single value.</p> <p>A restore testing selection can include a wildcard value (\"*\") for <code>ProtectedResourceArns</code> along with <code>ProtectedResourceConditions</code>. Alternatively, you can include up to 30 specific protected resource ARNs in <code>ProtectedResourceArns</code>.</p> <p>Cannot select by both protected resource types AND specific ARNs. Request will fail if both are included.</p>", "idempotent": true}, "DeleteBackupPlan": {"name": "DeleteBackupPlan", "http": {"method": "DELETE", "requestUri": "/backup/plans/{backupPlanId}"}, "input": {"shape": "DeleteBackupPlanInput"}, "output": {"shape": "DeleteBackupPlanOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Deletes a backup plan. A backup plan can only be deleted after all associated selections of resources have been deleted. Deleting a backup plan deletes the current version of a backup plan. Previous versions, if any, will still exist.</p>"}, "DeleteBackupSelection": {"name": "DeleteBackupSelection", "http": {"method": "DELETE", "requestUri": "/backup/plans/{backupPlanId}/selections/{selectionId}"}, "input": {"shape": "DeleteBackupSelectionInput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Deletes the resource selection associated with a backup plan that is specified by the <code>SelectionId</code>.</p>"}, "DeleteBackupVault": {"name": "DeleteBackupVault", "http": {"method": "DELETE", "requestUri": "/backup-vaults/{backupVaultName}"}, "input": {"shape": "DeleteBackupVaultInput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Deletes the backup vault identified by its name. A vault can be deleted only if it is empty.</p>", "idempotent": true}, "DeleteBackupVaultAccessPolicy": {"name": "DeleteBackupVaultAccessPolicy", "http": {"method": "DELETE", "requestUri": "/backup-vaults/{backupVaultName}/access-policy"}, "input": {"shape": "DeleteBackupVaultAccessPolicyInput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Deletes the policy document that manages permissions on a backup vault.</p>", "idempotent": true}, "DeleteBackupVaultLockConfiguration": {"name": "DeleteBackupVaultLockConfiguration", "http": {"method": "DELETE", "requestUri": "/backup-vaults/{backupVaultName}/vault-lock"}, "input": {"shape": "DeleteBackupVaultLockConfigurationInput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "InvalidRequestException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Deletes Backup Vault Lock from a backup vault specified by a backup vault name.</p> <p>If the Vault Lock configuration is immutable, then you cannot delete Vault Lock using API operations, and you will receive an <code>InvalidRequestException</code> if you attempt to do so. For more information, see <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/vault-lock.html\">Vault Lock</a> in the <i>Backup Developer Guide</i>.</p>", "idempotent": true}, "DeleteBackupVaultNotifications": {"name": "DeleteBackupVaultNotifications", "http": {"method": "DELETE", "requestUri": "/backup-vaults/{backupVaultName}/notification-configuration"}, "input": {"shape": "DeleteBackupVaultNotificationsInput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Deletes event notifications for the specified backup vault.</p>", "idempotent": true}, "DeleteFramework": {"name": "DeleteFramework", "http": {"method": "DELETE", "requestUri": "/audit/frameworks/{frameworkName}"}, "input": {"shape": "DeleteFrameworkInput"}, "errors": [{"shape": "MissingParameterValueException"}, {"shape": "InvalidParameterValueException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Deletes the framework specified by a framework name.</p>"}, "DeleteRecoveryPoint": {"name": "DeleteRecoveryPoint", "http": {"method": "DELETE", "requestUri": "/backup-vaults/{backupVaultName}/recovery-points/{recoveryPointArn}"}, "input": {"shape": "DeleteRecoveryPointInput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "InvalidResourceStateException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Deletes the recovery point specified by a recovery point ID.</p> <p>If the recovery point ID belongs to a continuous backup, calling this endpoint deletes the existing continuous backup and stops future continuous backup.</p> <p>When an IAM role's permissions are insufficient to call this API, the service sends back an HTTP 200 response with an empty HTTP body, but the recovery point is not deleted. Instead, it enters an <code>EXPIRED</code> state.</p> <p> <code>EXPIRED</code> recovery points can be deleted with this API once the IAM role has the <code>iam:CreateServiceLinkedRole</code> action. To learn more about adding this role, see <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/deleting-backups.html#deleting-backups-troubleshooting\"> Troubleshooting manual deletions</a>.</p> <p>If the user or role is deleted or the permission within the role is removed, the deletion will not be successful and will enter an <code>EXPIRED</code> state.</p>", "idempotent": true}, "DeleteReportPlan": {"name": "DeleteReportPlan", "http": {"method": "DELETE", "requestUri": "/audit/report-plans/{reportPlanName}"}, "input": {"shape": "DeleteReportPlanInput"}, "errors": [{"shape": "MissingParameterValueException"}, {"shape": "InvalidParameterValueException"}, {"shape": "ConflictException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Deletes the report plan specified by a report plan name.</p>", "idempotent": true}, "DeleteRestoreTestingPlan": {"name": "DeleteRestoreTestingPlan", "http": {"method": "DELETE", "requestUri": "/restore-testing/plans/{RestoreTestingPlanName}", "responseCode": 204}, "input": {"shape": "DeleteRestoreTestingPlanInput"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>This request deletes the specified restore testing plan.</p> <p>Deletion can only successfully occur if all associated restore testing selections are deleted first.</p>", "idempotent": true}, "DeleteRestoreTestingSelection": {"name": "DeleteRestoreTestingSelection", "http": {"method": "DELETE", "requestUri": "/restore-testing/plans/{RestoreTestingPlanName}/selections/{RestoreTestingSelectionName}", "responseCode": 204}, "input": {"shape": "DeleteRestoreTestingSelectionInput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Input the Restore Testing Plan name and Restore Testing Selection name.</p> <p>All testing selections associated with a restore testing plan must be deleted before the restore testing plan can be deleted.</p>", "idempotent": true}, "DescribeBackupJob": {"name": "DescribeBackupJob", "http": {"method": "GET", "requestUri": "/backup-jobs/{backupJobId}"}, "input": {"shape": "DescribeBackupJobInput"}, "output": {"shape": "DescribeBackupJobOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}, {"shape": "DependencyFailureException"}], "documentation": "<p>Returns backup job details for the specified <code>BackupJobId</code>.</p>", "idempotent": true}, "DescribeBackupVault": {"name": "DescribeBackupVault", "http": {"method": "GET", "requestUri": "/backup-vaults/{backupVaultName}"}, "input": {"shape": "DescribeBackupVaultInput"}, "output": {"shape": "DescribeBackupVaultOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Returns metadata about a backup vault specified by its name.</p>", "idempotent": true}, "DescribeCopyJob": {"name": "DescribeCopyJob", "http": {"method": "GET", "requestUri": "/copy-jobs/{copyJobId}"}, "input": {"shape": "DescribeCopyJobInput"}, "output": {"shape": "DescribeCopyJobOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Returns metadata associated with creating a copy of a resource.</p>", "idempotent": true}, "DescribeFramework": {"name": "DescribeFramework", "http": {"method": "GET", "requestUri": "/audit/frameworks/{frameworkName}"}, "input": {"shape": "DescribeFrameworkInput"}, "output": {"shape": "DescribeFrameworkOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Returns the framework details for the specified <code>FrameworkName</code>.</p>"}, "DescribeGlobalSettings": {"name": "DescribeGlobalSettings", "http": {"method": "GET", "requestUri": "/global-settings"}, "input": {"shape": "DescribeGlobalSettingsInput"}, "output": {"shape": "DescribeGlobalSettingsOutput"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Describes whether the Amazon Web Services account is opted in to cross-account backup. Returns an error if the account is not a member of an Organizations organization. Example: <code>describe-global-settings --region us-west-2</code> </p>"}, "DescribeProtectedResource": {"name": "DescribeProtectedResource", "http": {"method": "GET", "requestUri": "/resources/{resourceArn}"}, "input": {"shape": "DescribeProtectedResourceInput"}, "output": {"shape": "DescribeProtectedResourceOutput"}, "errors": [{"shape": "MissingParameterValueException"}, {"shape": "InvalidParameterValueException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns information about a saved resource, including the last time it was backed up, its Amazon Resource Name (ARN), and the Amazon Web Services service type of the saved resource.</p>", "idempotent": true}, "DescribeRecoveryPoint": {"name": "DescribeRecoveryPoint", "http": {"method": "GET", "requestUri": "/backup-vaults/{backupVaultName}/recovery-points/{recoveryPointArn}"}, "input": {"shape": "DescribeRecoveryPointInput"}, "output": {"shape": "DescribeRecoveryPointOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Returns metadata associated with a recovery point, including ID, status, encryption, and lifecycle.</p>", "idempotent": true}, "DescribeRegionSettings": {"name": "DescribeRegionSettings", "http": {"method": "GET", "requestUri": "/account-settings"}, "input": {"shape": "DescribeRegionSettingsInput"}, "output": {"shape": "DescribeRegionSettingsOutput"}, "errors": [{"shape": "ServiceUnavailableException"}], "documentation": "<p>Returns the current service opt-in settings for the Region. If service opt-in is enabled for a service, Backup tries to protect that service's resources in this Region, when the resource is included in an on-demand backup or scheduled backup plan. Otherwise, Backup does not try to protect that service's resources in this Region.</p>"}, "DescribeReportJob": {"name": "DescribeReportJob", "http": {"method": "GET", "requestUri": "/audit/report-jobs/{reportJobId}"}, "input": {"shape": "DescribeReportJobInput"}, "output": {"shape": "DescribeReportJobOutput"}, "errors": [{"shape": "ServiceUnavailableException"}, {"shape": "MissingParameterValueException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns the details associated with creating a report as specified by its <code>ReportJobId</code>.</p>"}, "DescribeReportPlan": {"name": "DescribeReportPlan", "http": {"method": "GET", "requestUri": "/audit/report-plans/{reportPlanName}"}, "input": {"shape": "DescribeReportPlanInput"}, "output": {"shape": "DescribeReportPlanOutput"}, "errors": [{"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Returns a list of all report plans for an Amazon Web Services account and Amazon Web Services Region.</p>"}, "DescribeRestoreJob": {"name": "DescribeRestoreJob", "http": {"method": "GET", "requestUri": "/restore-jobs/{restoreJobId}"}, "input": {"shape": "DescribeRestoreJobInput"}, "output": {"shape": "DescribeRestoreJobOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}, {"shape": "DependencyFailureException"}], "documentation": "<p>Returns metadata associated with a restore job that is specified by a job ID.</p>", "idempotent": true}, "DisassociateBackupVaultMpaApprovalTeam": {"name": "DisassociateBackupVaultMpaApprovalTeam", "http": {"method": "POST", "requestUri": "/backup-vaults/{backupVaultName}/mpaApprovalTeam?delete", "responseCode": 204}, "input": {"shape": "DisassociateBackupVaultMpaApprovalTeamInput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "InvalidRequestException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Removes the association between an MPA approval team and a backup vault, disabling the MPA approval workflow for restore operations.</p>"}, "DisassociateRecoveryPoint": {"name": "DisassociateRecoveryPoint", "http": {"method": "POST", "requestUri": "/backup-vaults/{backupVaultName}/recovery-points/{recoveryPointArn}/disassociate"}, "input": {"shape": "DisassociateRecoveryPointInput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "InvalidResourceStateException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Deletes the specified continuous backup recovery point from Backup and releases control of that continuous backup to the source service, such as Amazon RDS. The source service will continue to create and retain continuous backups using the lifecycle that you specified in your original backup plan.</p> <p>Does not support snapshot backup recovery points.</p>"}, "DisassociateRecoveryPointFromParent": {"name": "DisassociateRecoveryPointFromParent", "http": {"method": "DELETE", "requestUri": "/backup-vaults/{backupVaultName}/recovery-points/{recoveryPointArn}/parentAssociation", "responseCode": 204}, "input": {"shape": "DisassociateRecoveryPointFromParentInput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>This action to a specific child (nested) recovery point removes the relationship between the specified recovery point and its parent (composite) recovery point.</p>"}, "ExportBackupPlanTemplate": {"name": "ExportBackupPlanTemplate", "http": {"method": "GET", "requestUri": "/backup/plans/{backupPlanId}/toTemplate/"}, "input": {"shape": "ExportBackupPlanTemplateInput"}, "output": {"shape": "ExportBackupPlanTemplateOutput"}, "errors": [{"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns the backup plan that is specified by the plan ID as a backup template.</p>"}, "GetBackupPlan": {"name": "GetBackupPlan", "http": {"method": "GET", "requestUri": "/backup/plans/{backupPlanId}/"}, "input": {"shape": "GetBackupPlanInput"}, "output": {"shape": "GetBackupPlanOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Returns <code>BackupPlan</code> details for the specified <code>BackupPlanId</code>. The details are the body of a backup plan in JSON format, in addition to plan metadata.</p>", "idempotent": true}, "GetBackupPlanFromJSON": {"name": "GetBackupPlanFromJSON", "http": {"method": "POST", "requestUri": "/backup/template/json/to<PERSON>lan"}, "input": {"shape": "GetBackupPlanFromJSONInput"}, "output": {"shape": "GetBackupPlanFromJSONOutput"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Returns a valid JSON document specifying a backup plan or an error.</p>"}, "GetBackupPlanFromTemplate": {"name": "GetBackupPlanFromTemplate", "http": {"method": "GET", "requestUri": "/backup/template/plans/{templateId}/toPlan"}, "input": {"shape": "GetBackupPlanFromTemplateInput"}, "output": {"shape": "GetBackupPlanFromTemplateOutput"}, "errors": [{"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns the template specified by its <code>templateId</code> as a backup plan.</p>"}, "GetBackupSelection": {"name": "GetBackupSelection", "http": {"method": "GET", "requestUri": "/backup/plans/{backupPlanId}/selections/{selectionId}"}, "input": {"shape": "GetBackupSelectionInput"}, "output": {"shape": "GetBackupSelectionOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Returns selection metadata and a document in JSON format that specifies a list of resources that are associated with a backup plan.</p>", "idempotent": true}, "GetBackupVaultAccessPolicy": {"name": "GetBackupVaultAccessPolicy", "http": {"method": "GET", "requestUri": "/backup-vaults/{backupVaultName}/access-policy"}, "input": {"shape": "GetBackupVaultAccessPolicyInput"}, "output": {"shape": "GetBackupVaultAccessPolicyOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Returns the access policy document that is associated with the named backup vault.</p>", "idempotent": true}, "GetBackupVaultNotifications": {"name": "GetBackupVaultNotifications", "http": {"method": "GET", "requestUri": "/backup-vaults/{backupVaultName}/notification-configuration"}, "input": {"shape": "GetBackupVaultNotificationsInput"}, "output": {"shape": "GetBackupVaultNotificationsOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Returns event notifications for the specified backup vault.</p>", "idempotent": true}, "GetLegalHold": {"name": "GetLegalHold", "http": {"method": "GET", "requestUri": "/legal-holds/{legalHoldId}/"}, "input": {"shape": "GetLegalHoldInput"}, "output": {"shape": "GetLegalHoldOutput"}, "errors": [{"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>This action returns details for a specified legal hold. The details are the body of a legal hold in JSON format, in addition to metadata.</p>", "idempotent": true}, "GetRecoveryPointIndexDetails": {"name": "GetRecoveryPointIndexDetails", "http": {"method": "GET", "requestUri": "/backup-vaults/{backupVaultName}/recovery-points/{recoveryPointArn}/index"}, "input": {"shape": "GetRecoveryPointIndexDetailsInput"}, "output": {"shape": "GetRecoveryPointIndexDetailsOutput"}, "errors": [{"shape": "MissingParameterValueException"}, {"shape": "InvalidParameterValueException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>This operation returns the metadata and details specific to the backup index associated with the specified recovery point.</p>", "idempotent": true}, "GetRecoveryPointRestoreMetadata": {"name": "GetRecoveryPointRestoreMetadata", "http": {"method": "GET", "requestUri": "/backup-vaults/{backupVaultName}/recovery-points/{recoveryPointArn}/restore-metadata"}, "input": {"shape": "GetRecoveryPointRestoreMetadataInput"}, "output": {"shape": "GetRecoveryPointRestoreMetadataOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Returns a set of metadata key-value pairs that were used to create the backup.</p>", "idempotent": true}, "GetRestoreJobMetadata": {"name": "GetRestoreJobMetadata", "http": {"method": "GET", "requestUri": "/restore-jobs/{restoreJobId}/metadata"}, "input": {"shape": "GetRestoreJobMetadataInput"}, "output": {"shape": "GetRestoreJobMetadataOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>This request returns the metadata for the specified restore job.</p>"}, "GetRestoreTestingInferredMetadata": {"name": "GetRestoreTestingInferredMetadata", "http": {"method": "GET", "requestUri": "/restore-testing/inferred-metadata", "responseCode": 200}, "input": {"shape": "GetRestoreTestingInferredMetadataInput"}, "output": {"shape": "GetRestoreTestingInferredMetadataOutput"}, "errors": [{"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>This request returns the minimal required set of metadata needed to start a restore job with secure default settings. <code>BackupVaultName</code> and <code>RecoveryPointArn</code> are required parameters. <code>BackupVaultAccountId</code> is an optional parameter.</p>"}, "GetRestoreTestingPlan": {"name": "GetRestoreTestingPlan", "http": {"method": "GET", "requestUri": "/restore-testing/plans/{RestoreTestingPlanName}", "responseCode": 200}, "input": {"shape": "GetRestoreTestingPlanInput"}, "output": {"shape": "GetRestoreTestingPlanOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Returns <code>RestoreTestingPlan</code> details for the specified <code>RestoreTestingPlanName</code>. The details are the body of a restore testing plan in JSON format, in addition to plan metadata.</p>"}, "GetRestoreTestingSelection": {"name": "GetRestoreTestingSelection", "http": {"method": "GET", "requestUri": "/restore-testing/plans/{RestoreTestingPlanName}/selections/{RestoreTestingSelectionName}", "responseCode": 200}, "input": {"shape": "GetRestoreTestingSelectionInput"}, "output": {"shape": "GetRestoreTestingSelectionOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Returns RestoreTestingSelection, which displays resources and elements of the restore testing plan.</p>"}, "GetSupportedResourceTypes": {"name": "GetSupportedResourceTypes", "http": {"method": "GET", "requestUri": "/supported-resource-types"}, "output": {"shape": "GetSupportedResourceTypesOutput"}, "errors": [{"shape": "ServiceUnavailableException"}], "documentation": "<p>Returns the Amazon Web Services resource types supported by Backup.</p>"}, "ListBackupJobSummaries": {"name": "ListBackupJobSummaries", "http": {"method": "GET", "requestUri": "/audit/backup-job-summaries"}, "input": {"shape": "ListBackupJobSummariesInput"}, "output": {"shape": "ListBackupJobSummariesOutput"}, "errors": [{"shape": "InvalidParameterValueException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>This is a request for a summary of backup jobs created or running within the most recent 30 days. You can include parameters AccountID, State, ResourceType, MessageCategory, AggregationPeriod, MaxResults, or NextToken to filter results.</p> <p>This request returns a summary that contains Region, Account, State, ResourceType, MessageCategory, StartTime, EndTime, and Count of included jobs.</p>"}, "ListBackupJobs": {"name": "ListBackupJobs", "http": {"method": "GET", "requestUri": "/backup-jobs/"}, "input": {"shape": "ListBackupJobsInput"}, "output": {"shape": "ListBackupJobsOutput"}, "errors": [{"shape": "InvalidParameterValueException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Returns a list of existing backup jobs for an authenticated account for the last 30 days. For a longer period of time, consider using these <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/monitoring.html\">monitoring tools</a>.</p>", "idempotent": true}, "ListBackupPlanTemplates": {"name": "ListBackupPlanTemplates", "http": {"method": "GET", "requestUri": "/backup/template/plans"}, "input": {"shape": "ListBackupPlanTemplatesInput"}, "output": {"shape": "ListBackupPlanTemplatesOutput"}, "errors": [{"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Lists the backup plan templates.</p>"}, "ListBackupPlanVersions": {"name": "ListBackupPlanVersions", "http": {"method": "GET", "requestUri": "/backup/plans/{backupPlanId}/versions/"}, "input": {"shape": "ListBackupPlanVersionsInput"}, "output": {"shape": "ListBackupPlanVersionsOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Returns version metadata of your backup plans, including Amazon Resource Names (ARNs), backup plan IDs, creation and deletion dates, plan names, and version IDs.</p>", "idempotent": true}, "ListBackupPlans": {"name": "ListBackupPlans", "http": {"method": "GET", "requestUri": "/backup/plans/"}, "input": {"shape": "ListBackupPlansInput"}, "output": {"shape": "ListBackupPlansOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Lists the active backup plans for the account.</p>", "idempotent": true}, "ListBackupSelections": {"name": "ListBackupSelections", "http": {"method": "GET", "requestUri": "/backup/plans/{backupPlanId}/selections/"}, "input": {"shape": "ListBackupSelectionsInput"}, "output": {"shape": "ListBackupSelectionsOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Returns an array containing metadata of the resources associated with the target backup plan.</p>", "idempotent": true}, "ListBackupVaults": {"name": "ListBackupVaults", "http": {"method": "GET", "requestUri": "/backup-vaults/"}, "input": {"shape": "ListBackupVaultsInput"}, "output": {"shape": "ListBackupVaultsOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Returns a list of recovery point storage containers along with information about them.</p>", "idempotent": true}, "ListCopyJobSummaries": {"name": "ListCopyJobSummaries", "http": {"method": "GET", "requestUri": "/audit/copy-job-summaries"}, "input": {"shape": "ListCopyJobSummariesInput"}, "output": {"shape": "ListCopyJobSummariesOutput"}, "errors": [{"shape": "InvalidParameterValueException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>This request obtains a list of copy jobs created or running within the the most recent 30 days. You can include parameters AccountID, State, ResourceType, MessageCategory, AggregationPeriod, MaxResults, or NextToken to filter results.</p> <p>This request returns a summary that contains Region, Account, State, RestourceType, MessageCategory, StartTime, EndTime, and Count of included jobs.</p>"}, "ListCopyJobs": {"name": "ListCopyJobs", "http": {"method": "GET", "requestUri": "/copy-jobs/"}, "input": {"shape": "ListCopyJobsInput"}, "output": {"shape": "ListCopyJobsOutput"}, "errors": [{"shape": "InvalidParameterValueException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Returns metadata about your copy jobs.</p>"}, "ListFrameworks": {"name": "ListFrameworks", "http": {"method": "GET", "requestUri": "/audit/frameworks"}, "input": {"shape": "ListFrameworksInput"}, "output": {"shape": "ListFrameworksOutput"}, "errors": [{"shape": "InvalidParameterValueException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Returns a list of all frameworks for an Amazon Web Services account and Amazon Web Services Region.</p>"}, "ListIndexedRecoveryPoints": {"name": "ListIndexedRecoveryPoints", "http": {"method": "GET", "requestUri": "/indexes/recovery-point/"}, "input": {"shape": "ListIndexedRecoveryPointsInput"}, "output": {"shape": "ListIndexedRecoveryPointsOutput"}, "errors": [{"shape": "InvalidParameterValueException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>This operation returns a list of recovery points that have an associated index, belonging to the specified account.</p> <p>Optional parameters you can include are: MaxResults; NextToken; SourceResourceArns; CreatedBefore; CreatedAfter; and ResourceType.</p>", "idempotent": true}, "ListLegalHolds": {"name": "ListLegalHolds", "http": {"method": "GET", "requestUri": "/legal-holds/"}, "input": {"shape": "ListLegalHoldsInput"}, "output": {"shape": "ListLegalHoldsOutput"}, "errors": [{"shape": "InvalidParameterValueException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>This action returns metadata about active and previous legal holds.</p>", "idempotent": true}, "ListProtectedResources": {"name": "ListProtectedResources", "http": {"method": "GET", "requestUri": "/resources/"}, "input": {"shape": "ListProtectedResourcesInput"}, "output": {"shape": "ListProtectedResourcesOutput"}, "errors": [{"shape": "InvalidParameterValueException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Returns an array of resources successfully backed up by Backup, including the time the resource was saved, an Amazon Resource Name (ARN) of the resource, and a resource type.</p>", "idempotent": true}, "ListProtectedResourcesByBackupVault": {"name": "ListProtectedResourcesByBackupVault", "http": {"method": "GET", "requestUri": "/backup-vaults/{backupVaultName}/resources/"}, "input": {"shape": "ListProtectedResourcesByBackupVaultInput"}, "output": {"shape": "ListProtectedResourcesByBackupVaultOutput"}, "errors": [{"shape": "InvalidParameterValueException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>This request lists the protected resources corresponding to each backup vault.</p>"}, "ListRecoveryPointsByBackupVault": {"name": "ListRecoveryPointsByBackupVault", "http": {"method": "GET", "requestUri": "/backup-vaults/{backupVaultName}/recovery-points/"}, "input": {"shape": "ListRecoveryPointsByBackupVaultInput"}, "output": {"shape": "ListRecoveryPointsByBackupVaultOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Returns detailed information about the recovery points stored in a backup vault.</p>", "idempotent": true}, "ListRecoveryPointsByLegalHold": {"name": "ListRecoveryPointsByLegalHold", "http": {"method": "GET", "requestUri": "/legal-holds/{legalHoldId}/recovery-points"}, "input": {"shape": "ListRecoveryPointsByLegalHoldInput"}, "output": {"shape": "ListRecoveryPointsByLegalHoldOutput"}, "errors": [{"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>This action returns recovery point ARNs (Amazon Resource Names) of the specified legal hold.</p>", "idempotent": true}, "ListRecoveryPointsByResource": {"name": "ListRecoveryPointsByResource", "http": {"method": "GET", "requestUri": "/resources/{resourceArn}/recovery-points/"}, "input": {"shape": "ListRecoveryPointsByResourceInput"}, "output": {"shape": "ListRecoveryPointsByResourceOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>The information about the recovery points of the type specified by a resource Amazon Resource Name (ARN).</p> <note> <p>For Amazon EFS and Amazon EC2, this action only lists recovery points created by Backup.</p> </note>", "idempotent": true}, "ListReportJobs": {"name": "ListReportJobs", "http": {"method": "GET", "requestUri": "/audit/report-jobs"}, "input": {"shape": "ListReportJobsInput"}, "output": {"shape": "ListReportJobsOutput"}, "errors": [{"shape": "InvalidParameterValueException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns details about your report jobs.</p>"}, "ListReportPlans": {"name": "ListReportPlans", "http": {"method": "GET", "requestUri": "/audit/report-plans"}, "input": {"shape": "ListReportPlansInput"}, "output": {"shape": "ListReportPlansOutput"}, "errors": [{"shape": "InvalidParameterValueException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Returns a list of your report plans. For detailed information about a single report plan, use <code>DescribeReportPlan</code>.</p>"}, "ListRestoreAccessBackupVaults": {"name": "ListRestoreAccessBackupVaults", "http": {"method": "GET", "requestUri": "/logically-air-gapped-backup-vaults/{backupVaultName}/restore-access-backup-vaults/"}, "input": {"shape": "ListRestoreAccessBackupVaultsInput"}, "output": {"shape": "ListRestoreAccessBackupVaultsOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Returns a list of restore access backup vaults associated with a specified backup vault.</p>"}, "ListRestoreJobSummaries": {"name": "ListRestoreJobSummaries", "http": {"method": "GET", "requestUri": "/audit/restore-job-summaries"}, "input": {"shape": "ListRestoreJobSummariesInput"}, "output": {"shape": "ListRestoreJobSummariesOutput"}, "errors": [{"shape": "InvalidParameterValueException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>This request obtains a summary of restore jobs created or running within the the most recent 30 days. You can include parameters AccountID, State, ResourceType, AggregationPeriod, MaxResults, or NextToken to filter results.</p> <p>This request returns a summary that contains Region, Account, State, RestourceType, MessageCategory, StartTime, EndTime, and Count of included jobs.</p>"}, "ListRestoreJobs": {"name": "ListRestoreJobs", "http": {"method": "GET", "requestUri": "/restore-jobs/"}, "input": {"shape": "ListRestoreJobsInput"}, "output": {"shape": "ListRestoreJobsOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Returns a list of jobs that Backup initiated to restore a saved resource, including details about the recovery process.</p>", "idempotent": true}, "ListRestoreJobsByProtectedResource": {"name": "ListRestoreJobsByProtectedResource", "http": {"method": "GET", "requestUri": "/resources/{resourceArn}/restore-jobs/"}, "input": {"shape": "ListRestoreJobsByProtectedResourceInput"}, "output": {"shape": "ListRestoreJobsByProtectedResourceOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>This returns restore jobs that contain the specified protected resource.</p> <p>You must include <code>ResourceArn</code>. You can optionally include <code>NextToken</code>, <code>ByStatus</code>, <code>MaxResults</code>, <code>ByRecoveryPointCreationDateAfter</code> , and <code>ByRecoveryPointCreationDateBefore</code>.</p>"}, "ListRestoreTestingPlans": {"name": "ListRestoreTestingPlans", "http": {"method": "GET", "requestUri": "/restore-testing/plans", "responseCode": 200}, "input": {"shape": "ListRestoreTestingPlansInput"}, "output": {"shape": "ListRestoreTestingPlansOutput"}, "errors": [{"shape": "InvalidParameterValueException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Returns a list of restore testing plans.</p>"}, "ListRestoreTestingSelections": {"name": "ListRestoreTestingSelections", "http": {"method": "GET", "requestUri": "/restore-testing/plans/{RestoreTestingPlanName}/selections", "responseCode": 200}, "input": {"shape": "ListRestoreTestingSelectionsInput"}, "output": {"shape": "ListRestoreTestingSelectionsOutput"}, "errors": [{"shape": "InvalidParameterValueException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Returns a list of restore testing selections. Can be filtered by <code>MaxResults</code> and <code>RestoreTestingPlanName</code>.</p>"}, "ListTags": {"name": "ListTags", "http": {"method": "GET", "requestUri": "/tags/{resourceArn}/"}, "input": {"shape": "ListTagsInput"}, "output": {"shape": "ListTagsOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Returns the tags assigned to the resource, such as a target recovery point, backup plan, or backup vault.</p> <p>This operation returns results depending on the resource type used in the value for <code>resourceArn</code>. For example, recovery points of Amazon DynamoDB with Advanced Settings have an ARN (Amazon Resource Name) that begins with <code>arn:aws:backup</code>. Recovery points (backups) of DynamoDB without Advanced Settings enabled have an ARN that begins with <code>arn:aws:dynamodb</code>.</p> <p>When this operation is called and when you include values of <code>resourceArn</code> that have an ARN other than <code>arn:aws:backup</code>, it may return one of the exceptions listed below. To prevent this exception, include only values representing resource types that are fully managed by Backup. These have an ARN that begins <code>arn:aws:backup</code> and they are noted in the <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/backup-feature-availability.html#features-by-resource\">Feature availability by resource</a> table.</p>", "idempotent": true}, "PutBackupVaultAccessPolicy": {"name": "PutBackupVaultAccessPolicy", "http": {"method": "PUT", "requestUri": "/backup-vaults/{backupVaultName}/access-policy"}, "input": {"shape": "PutBackupVaultAccessPolicyInput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Sets a resource-based policy that is used to manage access permissions on the target backup vault. Requires a backup vault name and an access policy document in JSON format.</p>", "idempotent": true}, "PutBackupVaultLockConfiguration": {"name": "PutBackupVaultLockConfiguration", "http": {"method": "PUT", "requestUri": "/backup-vaults/{backupVaultName}/vault-lock"}, "input": {"shape": "PutBackupVaultLockConfigurationInput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "InvalidRequestException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Applies Backup Vault Lock to a backup vault, preventing attempts to delete any recovery point stored in or created in a backup vault. Vault Lock also prevents attempts to update the lifecycle policy that controls the retention period of any recovery point currently stored in a backup vault. If specified, Vault Lock enforces a minimum and maximum retention period for future backup and copy jobs that target a backup vault.</p> <note> <p>Backup Vault Lock has been assessed by Cohasset Associates for use in environments that are subject to SEC 17a-4, CFTC, and FINRA regulations. For more information about how Backup Vault Lock relates to these regulations, see the <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/samples/cohassetreport.zip\">Cohasset Associates Compliance Assessment.</a> </p> </note> <p>For more information, see <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/vault-lock.html\">Backup Vault Lock</a>.</p>", "idempotent": true}, "PutBackupVaultNotifications": {"name": "PutBackupVaultNotifications", "http": {"method": "PUT", "requestUri": "/backup-vaults/{backupVaultName}/notification-configuration"}, "input": {"shape": "PutBackupVaultNotificationsInput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Turns on notifications on a backup vault for the specified topic and events.</p>", "idempotent": true}, "PutRestoreValidationResult": {"name": "PutRestoreValidationResult", "http": {"method": "PUT", "requestUri": "/restore-jobs/{restoreJobId}/validations", "responseCode": 204}, "input": {"shape": "PutRestoreValidationResultInput"}, "errors": [{"shape": "InvalidParameterValueException"}, {"shape": "InvalidRequestException"}, {"shape": "MissingParameterValueException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>This request allows you to send your independent self-run restore test validation results. <code>RestoreJobId</code> and <code>ValidationStatus</code> are required. Optionally, you can input a <code>ValidationStatusMessage</code>.</p>", "idempotent": true}, "RevokeRestoreAccessBackupVault": {"name": "RevokeRestoreAccessBackupVault", "http": {"method": "DELETE", "requestUri": "/logically-air-gapped-backup-vaults/{backupVaultName}/restore-access-backup-vaults/{restoreAccessBackupVaultArn}"}, "input": {"shape": "RevokeRestoreAccessBackupVaultInput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidParameterValueException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Revokes access to a restore access backup vault, removing the ability to restore from its recovery points and permanently deleting the vault.</p>"}, "StartBackupJob": {"name": "StartBackupJob", "http": {"method": "PUT", "requestUri": "/backup-jobs"}, "input": {"shape": "StartBackupJobInput"}, "output": {"shape": "StartBackupJobOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "InvalidRequestException"}, {"shape": "ServiceUnavailableException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Starts an on-demand backup job for the specified resource.</p>", "idempotent": true}, "StartCopyJob": {"name": "StartCopyJob", "http": {"method": "PUT", "requestUri": "/copy-jobs"}, "input": {"shape": "StartCopyJobInput"}, "output": {"shape": "StartCopyJobOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}, {"shape": "LimitExceededException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Starts a job to create a one-time copy of the specified resource.</p> <p>Does not support continuous backups.</p>", "idempotent": true}, "StartReportJob": {"name": "StartReportJob", "http": {"method": "POST", "requestUri": "/audit/report-jobs/{reportPlanName}"}, "input": {"shape": "StartReportJobInput"}, "output": {"shape": "StartReportJobOutput"}, "errors": [{"shape": "InvalidParameterValueException"}, {"shape": "ServiceUnavailableException"}, {"shape": "MissingParameterValueException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Starts an on-demand report job for the specified report plan.</p>", "idempotent": true}, "StartRestoreJob": {"name": "StartRestoreJob", "http": {"method": "PUT", "requestUri": "/restore-jobs"}, "input": {"shape": "StartRestoreJobInput"}, "output": {"shape": "StartRestoreJobOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Recovers the saved resource identified by an Amazon Resource Name (ARN).</p>", "idempotent": true}, "StopBackupJob": {"name": "StopBackupJob", "http": {"method": "POST", "requestUri": "/backup-jobs/{backupJobId}"}, "input": {"shape": "StopBackupJobInput"}, "errors": [{"shape": "MissingParameterValueException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "InvalidRequestException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Attempts to cancel a job to create a one-time backup of a resource.</p> <p>This action is not supported for the following services:</p> <ul> <li> <p>Amazon Aurora</p> </li> <li> <p>Amazon DocumentDB (with MongoDB compatibility)</p> </li> <li> <p>Amazon FSx for Lustre</p> </li> <li> <p>Amazon FSx for NetApp ONTAP</p> </li> <li> <p>Amazon FSx for OpenZFS</p> </li> <li> <p>Amazon FSx for Windows File Server</p> </li> <li> <p>Amazon Neptune</p> </li> <li> <p>SAP HANA databases on Amazon EC2 instances</p> </li> <li> <p>Amazon RDS</p> </li> </ul>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags/{resourceArn}"}, "input": {"shape": "TagResourceInput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Assigns a set of key-value pairs to a resource.</p>", "idempotent": true}, "UntagResource": {"name": "UntagResource", "http": {"method": "POST", "requestUri": "/untag/{resourceArn}"}, "input": {"shape": "UntagResourceInput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Removes a set of key-value pairs from a recovery point, backup plan, or backup vault identified by an Amazon Resource Name (ARN)</p> <p>This API is not supported for recovery points for resource types including Aurora, Amazon DocumentDB. Amazon EBS, Amazon FSx, Neptune, and Amazon RDS.</p>", "idempotent": true}, "UpdateBackupPlan": {"name": "UpdateBackupPlan", "http": {"method": "POST", "requestUri": "/backup/plans/{backupPlanId}"}, "input": {"shape": "UpdateBackupPlanInput"}, "output": {"shape": "UpdateBackupPlanOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Updates the specified backup plan. The new version is uniquely identified by its ID.</p>", "idempotent": true}, "UpdateFramework": {"name": "UpdateFramework", "http": {"method": "PUT", "requestUri": "/audit/frameworks/{frameworkName}"}, "input": {"shape": "UpdateFrameworkInput"}, "output": {"shape": "UpdateFrameworkOutput"}, "errors": [{"shape": "AlreadyExistsException"}, {"shape": "ResourceNotFoundException"}, {"shape": "LimitExceededException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ConflictException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Updates the specified framework.</p>", "idempotent": true}, "UpdateGlobalSettings": {"name": "UpdateGlobalSettings", "http": {"method": "PUT", "requestUri": "/global-settings"}, "input": {"shape": "UpdateGlobalSettingsInput"}, "errors": [{"shape": "ServiceUnavailableException"}, {"shape": "MissingParameterValueException"}, {"shape": "InvalidParameterValueException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Updates whether the Amazon Web Services account is opted in to cross-account backup. Returns an error if the account is not an Organizations management account. Use the <code>DescribeGlobalSettings</code> API to determine the current settings.</p>"}, "UpdateRecoveryPointIndexSettings": {"name": "UpdateRecoveryPointIndexSettings", "http": {"method": "POST", "requestUri": "/backup-vaults/{backupVaultName}/recovery-points/{recoveryPointArn}/index"}, "input": {"shape": "UpdateRecoveryPointIndexSettingsInput"}, "output": {"shape": "UpdateRecoveryPointIndexSettingsOutput"}, "errors": [{"shape": "MissingParameterValueException"}, {"shape": "InvalidParameterValueException"}, {"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>This operation updates the settings of a recovery point index.</p> <p>Required: BackupVaultName, RecoveryPointArn, and IAMRoleArn</p>", "idempotent": true}, "UpdateRecoveryPointLifecycle": {"name": "UpdateRecoveryPointLifecycle", "http": {"method": "POST", "requestUri": "/backup-vaults/{backupVaultName}/recovery-points/{recoveryPointArn}"}, "input": {"shape": "UpdateRecoveryPointLifecycleInput"}, "output": {"shape": "UpdateRecoveryPointLifecycleOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "InvalidRequestException"}, {"shape": "MissingParameterValueException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Sets the transition lifecycle of a recovery point.</p> <p>The lifecycle defines when a protected resource is transitioned to cold storage and when it expires. Backup transitions and expires backups automatically according to the lifecycle that you define.</p> <p>Resource types that can transition to cold storage are listed in the <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/backup-feature-availability.html#features-by-resource\">Feature availability by resource</a> table. Backup ignores this expression for other resource types.</p> <p>Backups transitioned to cold storage must be stored in cold storage for a minimum of 90 days. Therefore, the “retention” setting must be 90 days greater than the “transition to cold after days” setting. The “transition to cold after days” setting cannot be changed after a backup has been transitioned to cold.</p> <important> <p>If your lifecycle currently uses the parameters <code>DeleteAfterDays</code> and <code>MoveToColdStorageAfterDays</code>, include these parameters and their values when you call this operation. Not including them may result in your plan updating with null values.</p> </important> <p>This operation does not support continuous backups.</p>", "idempotent": true}, "UpdateRegionSettings": {"name": "UpdateRegionSettings", "http": {"method": "PUT", "requestUri": "/account-settings"}, "input": {"shape": "UpdateRegionSettingsInput"}, "errors": [{"shape": "ServiceUnavailableException"}, {"shape": "MissingParameterValueException"}, {"shape": "InvalidParameterValueException"}], "documentation": "<p>Updates the current service opt-in settings for the Region.</p> <p>Use the <code>DescribeRegionSettings</code> API to determine the resource types that are supported.</p>"}, "UpdateReportPlan": {"name": "UpdateReportPlan", "http": {"method": "PUT", "requestUri": "/audit/report-plans/{reportPlanName}"}, "input": {"shape": "UpdateReportPlanInput"}, "output": {"shape": "UpdateReportPlanOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterValueException"}, {"shape": "ServiceUnavailableException"}, {"shape": "MissingParameterValueException"}, {"shape": "ConflictException"}], "documentation": "<p>Updates the specified report plan.</p>", "idempotent": true}, "UpdateRestoreTestingPlan": {"name": "UpdateRestoreTestingPlan", "http": {"method": "PUT", "requestUri": "/restore-testing/plans/{RestoreTestingPlanName}", "responseCode": 200}, "input": {"shape": "UpdateRestoreTestingPlanInput"}, "output": {"shape": "UpdateRestoreTestingPlanOutput"}, "errors": [{"shape": "ConflictException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>This request will send changes to your specified restore testing plan. <code>RestoreTestingPlanName</code> cannot be updated after it is created.</p> <p> <code>RecoveryPointSelection</code> can contain:</p> <ul> <li> <p> <code>Algorithm</code> </p> </li> <li> <p> <code>ExcludeVaults</code> </p> </li> <li> <p> <code>IncludeVaults</code> </p> </li> <li> <p> <code>RecoveryPointTypes</code> </p> </li> <li> <p> <code>SelectionWindowDays</code> </p> </li> </ul>", "idempotent": true}, "UpdateRestoreTestingSelection": {"name": "UpdateRestoreTestingSelection", "http": {"method": "PUT", "requestUri": "/restore-testing/plans/{RestoreTestingPlanName}/selections/{RestoreTestingSelectionName}", "responseCode": 200}, "input": {"shape": "UpdateRestoreTestingSelectionInput"}, "output": {"shape": "UpdateRestoreTestingSelectionOutput"}, "errors": [{"shape": "ConflictException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingParameterValueException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Updates the specified restore testing selection.</p> <p>Most elements except the <code>RestoreTestingSelectionName</code> can be updated with this request.</p> <p>You can use either protected resource ARNs or conditions, but not both.</p>", "idempotent": true}}, "shapes": {"ARN": {"type": "string"}, "AccountId": {"type": "string", "pattern": "^[0-9]{12}$"}, "AdvancedBackupSetting": {"type": "structure", "members": {"ResourceType": {"shape": "ResourceType", "documentation": "<p>Specifies an object containing resource type and backup options. The only supported resource type is Amazon EC2 instances with Windows Volume Shadow Copy Service (VSS). For a CloudFormation example, see the <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/integrate-cloudformation-with-aws-backup.html\">sample CloudFormation template to enable Windows VSS</a> in the <i>Backup User Guide</i>.</p> <p>Valid values: <code>EC2</code>.</p>"}, "BackupOptions": {"shape": "BackupOptions", "documentation": "<p>Specifies the backup option for a selected resource. This option is only available for Windows VSS backup jobs.</p> <p>Valid values: </p> <p>Set to <code>\"WindowsVSS\":\"enabled\"</code> to enable the <code>WindowsVSS</code> backup option and create a Windows VSS backup. </p> <p>Set to <code>\"WindowsVSS\":\"disabled\"</code> to create a regular backup. The <code>WindowsVSS</code> option is not enabled by default.</p> <p>If you specify an invalid option, you get an <code>InvalidParameterValueException</code> exception.</p> <p>For more information about Windows VSS backups, see <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/windows-backups.html\">Creating a VSS-Enabled Windows Backup</a>.</p>"}}, "documentation": "<p>The backup options for each resource type.</p>"}, "AdvancedBackupSettings": {"type": "list", "member": {"shape": "AdvancedBackupSetting"}}, "AggregationPeriod": {"type": "string", "enum": ["ONE_DAY", "SEVEN_DAYS", "FOURTEEN_DAYS"]}, "AlreadyExistsException": {"type": "structure", "members": {"Code": {"shape": "string"}, "Message": {"shape": "string"}, "CreatorRequestId": {"shape": "string", "documentation": "<p/>"}, "Arn": {"shape": "string", "documentation": "<p/>"}, "Type": {"shape": "string", "documentation": "<p/>"}, "Context": {"shape": "string", "documentation": "<p/>"}}, "documentation": "<p>The required resource already exists.</p>", "exception": true}, "AssociateBackupVaultMpaApprovalTeamInput": {"type": "structure", "required": ["BackupVaultName", "MpaApprovalTeamArn"], "members": {"BackupVaultName": {"shape": "BackupVaultName", "documentation": "<p>The name of the backup vault to associate with the MPA approval team.</p>", "location": "uri", "locationName": "backup<PERSON><PERSON><PERSON><PERSON>"}, "MpaApprovalTeamArn": {"shape": "ARN", "documentation": "<p>The Amazon Resource Name (ARN) of the MPA approval team to associate with the backup vault.</p>"}, "RequesterComment": {"shape": "RequesterComment", "documentation": "<p>A comment provided by the requester explaining the association request.</p>"}}}, "BackupJob": {"type": "structure", "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The account ID that owns the backup job.</p>"}, "BackupJobId": {"shape": "string", "documentation": "<p>Uniquely identifies a request to Back<PERSON> to back up a resource.</p>"}, "BackupVaultName": {"shape": "BackupVaultName", "documentation": "<p>The name of a logical container where backups are stored. Backup vaults are identified by names that are unique to the account used to create them and the Amazon Web Services Region where they are created.</p>"}, "BackupVaultArn": {"shape": "ARN", "documentation": "<p>An Amazon Resource Name (ARN) that uniquely identifies a backup vault; for example, <code>arn:aws:backup:us-east-1:************:backup-vault:aBackupVault</code>.</p>"}, "RecoveryPointArn": {"shape": "ARN", "documentation": "<p>An ARN that uniquely identifies a recovery point; for example, <code>arn:aws:backup:us-east-1:************:recovery-point:1EB3B5E7-9EB0-435A-A80B-108B488B0D45</code>.</p>"}, "ResourceArn": {"shape": "ARN", "documentation": "<p>An ARN that uniquely identifies a resource. The format of the ARN depends on the resource type.</p>"}, "CreationDate": {"shape": "timestamp", "documentation": "<p>The date and time a backup job is created, in Unix format and Coordinated Universal Time (UTC). The value of <code>CreationDate</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "CompletionDate": {"shape": "timestamp", "documentation": "<p>The date and time a job to create a backup job is completed, in Unix format and Coordinated Universal Time (UTC). The value of <code>CompletionDate</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "State": {"shape": "BackupJobState", "documentation": "<p>The current state of a backup job.</p>"}, "StatusMessage": {"shape": "string", "documentation": "<p>A detailed message explaining the status of the job to back up a resource.</p>"}, "PercentDone": {"shape": "string", "documentation": "<p>Contains an estimated percentage complete of a job at the time the job status was queried.</p>"}, "BackupSizeInBytes": {"shape": "<PERSON>", "documentation": "<p>The size, in bytes, of a backup (recovery point).</p> <p>This value can render differently depending on the resource type as <PERSON>up pulls in data information from other Amazon Web Services services. For example, the value returned may show a value of <code>0</code>, which may differ from the anticipated value.</p> <p>The expected behavior for values by resource type are described as follows:</p> <ul> <li> <p>Amazon Aurora, Amazon DocumentDB, and Amazon Neptune do not have this value populate from the operation <code>GetBackupJobStatus</code>.</p> </li> <li> <p>For Amazon DynamoDB with advanced features, this value refers to the size of the recovery point (backup).</p> </li> <li> <p>Amazon EC2 and Amazon EBS show volume size (provisioned storage) returned as part of this value. Amazon EBS does not return backup size information; snapshot size will have the same value as the original resource that was backed up.</p> </li> <li> <p>For Amazon EFS, this value refers to the delta bytes transferred during a backup.</p> </li> <li> <p>Amazon FSx does not populate this value from the operation <code>GetBackupJobStatus</code> for FSx file systems.</p> </li> <li> <p>An Amazon RDS instance will show as <code>0</code>.</p> </li> <li> <p>For virtual machines running VMware, this value is passed to Backup through an asynchronous workflow, which can mean this displayed value can under-represent the actual backup size.</p> </li> </ul>"}, "IamRoleArn": {"shape": "IAMRoleArn", "documentation": "<p>Specifies the IAM role ARN used to create the target recovery point. IAM roles other than the default role must include either <code>AWSBackup</code> or <code>AwsBackup</code> in the role name. For example, <code>arn:aws:iam::************:role/AWSBackupRDSAccess</code>. Role names without those strings lack permissions to perform backup jobs.</p>"}, "CreatedBy": {"shape": "RecoveryPointCreator", "documentation": "<p>Contains identifying information about the creation of a backup job, including the <code>BackupPlanArn</code>, <code>BackupPlanId</code>, <code>BackupPlanVersion</code>, and <code>BackupRuleId</code> of the backup plan used to create it.</p>"}, "ExpectedCompletionDate": {"shape": "timestamp", "documentation": "<p>The date and time a job to back up resources is expected to be completed, in Unix format and Coordinated Universal Time (UTC). The value of <code>ExpectedCompletionDate</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "StartBy": {"shape": "timestamp", "documentation": "<p>Specifies the time in Unix format and Coordinated Universal Time (UTC) when a backup job must be started before it is canceled. The value is calculated by adding the start window to the scheduled time. So if the scheduled time were 6:00 PM and the start window is 2 hours, the <code>StartBy</code> time would be 8:00 PM on the date specified. The value of <code>StartBy</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>The type of Amazon Web Services resource to be backed up; for example, an Amazon Elastic Block Store (Amazon EBS) volume or an Amazon Relational Database Service (Amazon RDS) database. For Windows Volume Shadow Copy Service (VSS) backups, the only supported resource type is Amazon EC2.</p>"}, "BytesTransferred": {"shape": "<PERSON>", "documentation": "<p>The size in bytes transferred to a backup vault at the time that the job status was queried.</p>"}, "BackupOptions": {"shape": "BackupOptions", "documentation": "<p>Specifies the backup option for a selected resource. This option is only available for Windows Volume Shadow Copy Service (VSS) backup jobs.</p> <p>Valid values: Set to <code>\"WindowsVSS\":\"enabled\"</code> to enable the <code>WindowsVSS</code> backup option and create a Windows VSS backup. Set to <code>\"WindowsVSS\":\"disabled\"</code> to create a regular backup. If you specify an invalid option, you get an <code>InvalidParameterValueException</code> exception.</p>"}, "BackupType": {"shape": "string", "documentation": "<p>Represents the type of backup for a backup job.</p>"}, "ParentJobId": {"shape": "string", "documentation": "<p>This uniquely identifies a request to Back<PERSON> to back up a resource. The return will be the parent (composite) job ID.</p>"}, "IsParent": {"shape": "boolean", "documentation": "<p>This is a boolean value indicating this is a parent (composite) backup job.</p>"}, "ResourceName": {"shape": "string", "documentation": "<p>The non-unique name of the resource that belongs to the specified backup.</p>"}, "InitiationDate": {"shape": "timestamp", "documentation": "<p>The date on which the backup job was initiated.</p>"}, "MessageCategory": {"shape": "string", "documentation": "<p>This parameter is the job count for the specified message category.</p> <p>Example strings may include <code>AccessDenied</code>, <code>SUCCESS</code>, <code>AGGREGATE_ALL</code>, and <code>INVALIDPARAMETERS</code>. See <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/monitoring.html\">Monitoring</a> for a list of MessageCategory strings.</p> <p>The the value ANY returns count of all message categories.</p> <p> <code>AGGREGATE_ALL</code> aggregates job counts for all message categories and returns the sum.</p>"}}, "documentation": "<p>Contains detailed information about a backup job.</p>"}, "BackupJobChildJobsInState": {"type": "map", "key": {"shape": "BackupJobState"}, "value": {"shape": "<PERSON>"}}, "BackupJobState": {"type": "string", "enum": ["CREATED", "PENDING", "RUNNING", "ABORTING", "ABORTED", "COMPLETED", "FAILED", "EXPIRED", "PARTIAL"]}, "BackupJobStatus": {"type": "string", "enum": ["CREATED", "PENDING", "RUNNING", "ABORTING", "ABORTED", "COMPLETED", "FAILED", "EXPIRED", "PARTIAL", "AGGREGATE_ALL", "ANY"]}, "BackupJobSummary": {"type": "structure", "members": {"Region": {"shape": "Region", "documentation": "<p>The Amazon Web Services Regions within the job summary.</p>"}, "AccountId": {"shape": "AccountId", "documentation": "<p>The account ID that owns the jobs within the summary.</p>"}, "State": {"shape": "BackupJobStatus", "documentation": "<p>This value is job count for jobs with the specified state.</p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>This value is the job count for the specified resource type. The request <code>GetSupportedResourceTypes</code> returns strings for supported resource types.</p>"}, "MessageCategory": {"shape": "MessageCategory", "documentation": "<p>This parameter is the job count for the specified message category.</p> <p>Example strings include <code>AccessDenied</code>, <code>Success</code>, and <code>InvalidParameters</code>. See <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/monitoring.html\">Monitoring</a> for a list of MessageCategory strings.</p> <p>The the value ANY returns count of all message categories.</p> <p> <code>AGGREGATE_ALL</code> aggregates job counts for all message categories and returns the sum.</p>"}, "Count": {"shape": "integer", "documentation": "<p>The value as a number of jobs in a job summary.</p>"}, "StartTime": {"shape": "timestamp", "documentation": "<p>The value of time in number format of a job start time.</p> <p>This value is the time in Unix format, Coordinated Universal Time (UTC), and accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "EndTime": {"shape": "timestamp", "documentation": "<p>The value of time in number format of a job end time.</p> <p>This value is the time in Unix format, Coordinated Universal Time (UTC), and accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}}, "documentation": "<p>This is a summary of jobs created or running within the most recent 30 days.</p> <p>The returned summary may contain the following: Region, Account, State, RestourceType, MessageCategory, StartTime, EndTime, and Count of included jobs.</p>"}, "BackupJobSummaryList": {"type": "list", "member": {"shape": "BackupJob<PERSON><PERSON><PERSON><PERSON>"}}, "BackupJobsList": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON><PERSON>"}}, "BackupOptionKey": {"type": "string", "pattern": "^[a-zA-Z0-9\\-\\_\\.]{1,50}$"}, "BackupOptionValue": {"type": "string", "pattern": "^[a-zA-Z0-9\\-\\_\\.]{1,50}$"}, "BackupOptions": {"type": "map", "key": {"shape": "BackupOptionKey"}, "value": {"shape": "BackupOptionValue"}}, "BackupPlan": {"type": "structure", "required": ["BackupPlanName", "Rules"], "members": {"BackupPlanName": {"shape": "BackupPlanName", "documentation": "<p>The display name of a backup plan. Must contain only alphanumeric or '-_.' special characters.</p> <p>If this is set in the console, it can contain 1 to 50 characters; if this is set through CLI or API, it can contain 1 to 200 characters.</p>"}, "Rules": {"shape": "BackupRules", "documentation": "<p>An array of <code>BackupRule</code> objects, each of which specifies a scheduled task that is used to back up a selection of resources. </p>"}, "AdvancedBackupSettings": {"shape": "AdvancedBackupSettings", "documentation": "<p>Contains a list of <code>BackupOptions</code> for each resource type.</p>"}}, "documentation": "<p>Contains an optional backup plan display name and an array of <code>BackupRule</code> objects, each of which specifies a backup rule. Each rule in a backup plan is a separate scheduled task and can back up a different selection of Amazon Web Services resources.</p>"}, "BackupPlanInput": {"type": "structure", "required": ["BackupPlanName", "Rules"], "members": {"BackupPlanName": {"shape": "BackupPlanName", "documentation": "<p>The display name of a backup plan. Must contain 1 to 50 alphanumeric or '-_.' characters.</p>"}, "Rules": {"shape": "BackupRulesInput", "documentation": "<p>An array of <code>BackupRule</code> objects, each of which specifies a scheduled task that is used to back up a selection of resources.</p>"}, "AdvancedBackupSettings": {"shape": "AdvancedBackupSettings", "documentation": "<p>Specifies a list of <code>BackupOptions</code> for each resource type. These settings are only available for Windows Volume Shadow Copy Service (VSS) backup jobs.</p>"}}, "documentation": "<p>Contains an optional backup plan display name and an array of <code>BackupRule</code> objects, each of which specifies a backup rule. Each rule in a backup plan is a separate scheduled task. </p>"}, "BackupPlanName": {"type": "string"}, "BackupPlanTemplatesList": {"type": "list", "member": {"shape": "BackupPlanTemplatesListMember"}}, "BackupPlanTemplatesListMember": {"type": "structure", "members": {"BackupPlanTemplateId": {"shape": "string", "documentation": "<p>Uniquely identifies a stored backup plan template.</p>"}, "BackupPlanTemplateName": {"shape": "string", "documentation": "<p>The optional display name of a backup plan template.</p>"}}, "documentation": "<p>An object specifying metadata associated with a backup plan template.</p>"}, "BackupPlanVersionsList": {"type": "list", "member": {"shape": "BackupPlansListMember"}}, "BackupPlansList": {"type": "list", "member": {"shape": "BackupPlansListMember"}}, "BackupPlansListMember": {"type": "structure", "members": {"BackupPlanArn": {"shape": "ARN", "documentation": "<p>An Amazon Resource Name (ARN) that uniquely identifies a backup plan; for example, <code>arn:aws:backup:us-east-1:************:plan:8F81F553-3A74-4A3F-B93D-B3360DC80C50</code>.</p>"}, "BackupPlanId": {"shape": "string", "documentation": "<p>Uniquely identifies a backup plan.</p>"}, "CreationDate": {"shape": "timestamp", "documentation": "<p>The date and time a resource backup plan is created, in Unix format and Coordinated Universal Time (UTC). The value of <code>CreationDate</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "DeletionDate": {"shape": "timestamp", "documentation": "<p>The date and time a backup plan is deleted, in Unix format and Coordinated Universal Time (UTC). The value of <code>DeletionDate</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "VersionId": {"shape": "string", "documentation": "<p>Unique, randomly generated, Unicode, UTF-8 encoded strings that are at most 1,024 bytes long. Version IDs cannot be edited.</p>"}, "BackupPlanName": {"shape": "BackupPlanName", "documentation": "<p>The display name of a saved backup plan.</p>"}, "CreatorRequestId": {"shape": "string", "documentation": "<p>A unique string that identifies the request and allows failed requests to be retried without the risk of running the operation twice. This parameter is optional.</p> <p>If used, this parameter must contain 1 to 50 alphanumeric or '-_.' characters.</p>"}, "LastExecutionDate": {"shape": "timestamp", "documentation": "<p>The last time this backup plan was run. A date and time, in Unix format and Coordinated Universal Time (UTC). The value of <code>LastExecutionDate</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "AdvancedBackupSettings": {"shape": "AdvancedBackupSettings", "documentation": "<p>Contains a list of <code>BackupOptions</code> for a resource type.</p>"}}, "documentation": "<p>Contains metadata about a backup plan.</p>"}, "BackupRule": {"type": "structure", "required": ["RuleName", "TargetBackupVaultName"], "members": {"RuleName": {"shape": "BackupRuleName", "documentation": "<p>A display name for a backup rule. Must contain 1 to 50 alphanumeric or '-_.' characters.</p>"}, "TargetBackupVaultName": {"shape": "BackupVaultName", "documentation": "<p>The name of a logical container where backups are stored. Backup vaults are identified by names that are unique to the account used to create them and the Amazon Web Services Region where they are created.</p>"}, "ScheduleExpression": {"shape": "CronExpression", "documentation": "<p>A cron expression in UTC specifying when Backup initiates a backup job. When no CRON expression is provided, Backup will use the default expression <code>cron(0 5 ? * * *)</code>.</p> <p>For more information about Amazon Web Services cron expressions, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/events/ScheduledEvents.html\">Schedule Expressions for Rules</a> in the <i>Amazon CloudWatch Events User Guide</i>.</p> <p>Two examples of Amazon Web Services cron expressions are <code> 15 * ? * * *</code> (take a backup every hour at 15 minutes past the hour) and <code>0 12 * * ? *</code> (take a backup every day at 12 noon UTC).</p> <p>For a table of examples, click the preceding link and scroll down the page.</p>"}, "StartWindowMinutes": {"shape": "WindowMinutes", "documentation": "<p>A value in minutes after a backup is scheduled before a job will be canceled if it doesn't start successfully. This value is optional. If this value is included, it must be at least 60 minutes to avoid errors.</p> <p>During the start window, the backup job status remains in <code>CREATED</code> status until it has successfully begun or until the start window time has run out. If within the start window time Back<PERSON> receives an error that allows the job to be retried, Back<PERSON> will automatically retry to begin the job at least every 10 minutes until the backup successfully begins (the job status changes to <code>RUNNING</code>) or until the job status changes to <code>EXPIRED</code> (which is expected to occur when the start window time is over).</p>"}, "CompletionWindowMinutes": {"shape": "WindowMinutes", "documentation": "<p>A value in minutes after a backup job is successfully started before it must be completed or it will be canceled by Backup. This value is optional.</p>"}, "Lifecycle": {"shape": "Lifecycle", "documentation": "<p>The lifecycle defines when a protected resource is transitioned to cold storage and when it expires. Backup transitions and expires backups automatically according to the lifecycle that you define. </p> <p>Backups transitioned to cold storage must be stored in cold storage for a minimum of 90 days. Therefore, the “retention” setting must be 90 days greater than the “transition to cold after days” setting. The “transition to cold after days” setting cannot be changed after a backup has been transitioned to cold. </p> <p>Resource types that can transition to cold storage are listed in the <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/backup-feature-availability.html#features-by-resource\">Feature availability by resource</a> table. Backup ignores this expression for other resource types.</p>"}, "RecoveryPointTags": {"shape": "Tags", "documentation": "<p>The tags that are assigned to resources that are associated with this rule when restored from backup.</p>"}, "RuleId": {"shape": "string", "documentation": "<p>Uniquely identifies a rule that is used to schedule the backup of a selection of resources.</p>"}, "CopyActions": {"shape": "CopyActions", "documentation": "<p>An array of <code>CopyAction</code> objects, which contains the details of the copy operation.</p>"}, "EnableContinuousBackup": {"shape": "Boolean", "documentation": "<p>Specifies whether Backup creates continuous backups. True causes Backup to create continuous backups capable of point-in-time restore (PITR). False (or not specified) causes Backup to create snapshot backups.</p>"}, "ScheduleExpressionTimezone": {"shape": "Timezone", "documentation": "<p>The timezone in which the schedule expression is set. By default, ScheduleExpressions are in UTC. You can modify this to a specified timezone.</p>"}, "IndexActions": {"shape": "IndexActions", "documentation": "<p>IndexActions is an array you use to specify how backup data should be indexed.</p> <p>eEach BackupRule can have 0 or 1 IndexAction, as each backup can have up to one index associated with it.</p> <p>Within the array is ResourceType. Only one will be accepted for each BackupRule.</p>"}}, "documentation": "<p>Specifies a scheduled task used to back up a selection of resources.</p>"}, "BackupRuleInput": {"type": "structure", "required": ["RuleName", "TargetBackupVaultName"], "members": {"RuleName": {"shape": "BackupRuleName", "documentation": "<p>A display name for a backup rule. Must contain 1 to 50 alphanumeric or '-_.' characters.</p>"}, "TargetBackupVaultName": {"shape": "BackupVaultName", "documentation": "<p>The name of a logical container where backups are stored. Backup vaults are identified by names that are unique to the account used to create them and the Amazon Web Services Region where they are created.</p>"}, "ScheduleExpression": {"shape": "CronExpression", "documentation": "<p>A CRON expression in UTC specifying when Backup initiates a backup job. When no CRON expression is provided, Backup will use the default expression <code>cron(0 5 ? * * *)</code>.</p>"}, "StartWindowMinutes": {"shape": "WindowMinutes", "documentation": "<p>A value in minutes after a backup is scheduled before a job will be canceled if it doesn't start successfully. This value is optional. If this value is included, it must be at least 60 minutes to avoid errors.</p> <p>This parameter has a maximum value of 100 years (52,560,000 minutes).</p> <p>During the start window, the backup job status remains in <code>CREATED</code> status until it has successfully begun or until the start window time has run out. If within the start window time <PERSON><PERSON> receives an error that allows the job to be retried, Backup will automatically retry to begin the job at least every 10 minutes until the backup successfully begins (the job status changes to <code>RUNNING</code>) or until the job status changes to <code>EXPIRED</code> (which is expected to occur when the start window time is over).</p>"}, "CompletionWindowMinutes": {"shape": "WindowMinutes", "documentation": "<p>A value in minutes after a backup job is successfully started before it must be completed or it will be canceled by Backup. This value is optional.</p>"}, "Lifecycle": {"shape": "Lifecycle", "documentation": "<p>The lifecycle defines when a protected resource is transitioned to cold storage and when it expires. Backup will transition and expire backups automatically according to the lifecycle that you define. </p> <p>Backups transitioned to cold storage must be stored in cold storage for a minimum of 90 days. Therefore, the “retention” setting must be 90 days greater than the “transition to cold after days” setting. The “transition to cold after days” setting cannot be changed after a backup has been transitioned to cold storage.</p> <p>Resource types that can transition to cold storage are listed in the <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/backup-feature-availability.html#features-by-resource\">Feature availability by resource</a> table. Backup ignores this expression for other resource types.</p> <p>This parameter has a maximum value of 100 years (36,500 days).</p>"}, "RecoveryPointTags": {"shape": "Tags", "documentation": "<p>The tags to assign to the resources.</p>"}, "CopyActions": {"shape": "CopyActions", "documentation": "<p>An array of <code>CopyAction</code> objects, which contains the details of the copy operation.</p>"}, "EnableContinuousBackup": {"shape": "Boolean", "documentation": "<p>Specifies whether Backup creates continuous backups. True causes Backup to create continuous backups capable of point-in-time restore (PITR). False (or not specified) causes Backup to create snapshot backups.</p>"}, "ScheduleExpressionTimezone": {"shape": "Timezone", "documentation": "<p>The timezone in which the schedule expression is set. By default, ScheduleExpressions are in UTC. You can modify this to a specified timezone.</p>"}, "IndexActions": {"shape": "IndexActions", "documentation": "<p>There can up to one IndexAction in each BackupRule, as each backup can have 0 or 1 backup index associated with it.</p> <p>Within the array is ResourceTypes. Only 1 resource type will be accepted for each BackupRule. Valid values:</p> <ul> <li> <p> <code>EBS</code> for Amazon Elastic Block Store</p> </li> <li> <p> <code>S3</code> for Amazon Simple Storage Service (Amazon S3)</p> </li> </ul>"}}, "documentation": "<p>Specifies a scheduled task used to back up a selection of resources.</p>"}, "BackupRuleName": {"type": "string", "pattern": "^[a-zA-Z0-9\\-\\_\\.]{1,50}$"}, "BackupRules": {"type": "list", "member": {"shape": "BackupRule"}}, "BackupRulesInput": {"type": "list", "member": {"shape": "BackupRuleInput"}}, "BackupSelection": {"type": "structure", "required": ["SelectionName", "IamRoleArn"], "members": {"SelectionName": {"shape": "BackupSelectionName", "documentation": "<p>The display name of a resource selection document. Must contain 1 to 50 alphanumeric or '-_.' characters.</p>"}, "IamRoleArn": {"shape": "IAMRoleArn", "documentation": "<p>The ARN of the IAM role that Backup uses to authenticate when backing up the target resource; for example, <code>arn:aws:iam::************:role/S3Access</code>.</p>"}, "Resources": {"shape": "ResourceArns", "documentation": "<p>The Amazon Resource Names (ARNs) of the resources to assign to a backup plan. The maximum number of ARNs is 500 without wildcards, or 30 ARNs with wildcards.</p> <p>If you need to assign many resources to a backup plan, consider a different resource selection strategy, such as assigning all resources of a resource type or refining your resource selection using tags.</p> <p>If you specify multiple ARNs, the resources much match any of the ARNs (OR logic).</p>"}, "ListOfTags": {"shape": "ListOfTags", "documentation": "<p>The conditions that you define to assign resources to your backup plans using tags. For example, <code>\"StringEquals\": { \"ConditionKey\": \"backup\", \"ConditionValue\": \"daily\"}</code>.</p> <p> <code>ListOfTags</code> supports only <code>StringEquals</code>. Condition operators are case sensitive.</p> <p>If you specify multiple conditions, the resources much match any of the conditions (OR logic).</p>"}, "NotResources": {"shape": "ResourceArns", "documentation": "<p>The Amazon Resource Names (ARNs) of the resources to exclude from a backup plan. The maximum number of ARNs is 500 without wildcards, or 30 ARNs with wildcards.</p> <p>If you need to exclude many resources from a backup plan, consider a different resource selection strategy, such as assigning only one or a few resource types or refining your resource selection using tags.</p>"}, "Conditions": {"shape": "Conditions", "documentation": "<p>The conditions that you define to assign resources to your backup plans using tags. For example, <code>\"StringEquals\": { \"ConditionKey\": \"aws:ResourceTag/backup\", \"ConditionValue\": \"daily\" }</code>.</p> <p> <code>Conditions</code> supports <code>StringEquals</code>, <code>StringLike</code>, <code>StringNotEquals</code>, and <code>StringNotLike</code>. Condition operators are case sensitive.</p> <p>If you specify multiple conditions, the resources much match all conditions (AND logic).</p>"}}, "documentation": "<p>Used to specify a set of resources to a backup plan.</p> <p>We recommend that you specify conditions, tags, or resources to include or exclude. Otherwise, Backup attempts to select all supported and opted-in storage resources, which could have unintended cost implications.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/assigning-resources.html#assigning-resources-json\">Assigning resources programmatically</a>.</p>"}, "BackupSelectionName": {"type": "string", "pattern": "^[a-zA-Z0-9\\-\\_\\.]{1,50}$"}, "BackupSelectionsList": {"type": "list", "member": {"shape": "BackupSelectionsListMember"}}, "BackupSelectionsListMember": {"type": "structure", "members": {"SelectionId": {"shape": "string", "documentation": "<p>Uniquely identifies a request to assign a set of resources to a backup plan.</p>"}, "SelectionName": {"shape": "BackupSelectionName", "documentation": "<p>The display name of a resource selection document.</p>"}, "BackupPlanId": {"shape": "string", "documentation": "<p>Uniquely identifies a backup plan.</p>"}, "CreationDate": {"shape": "timestamp", "documentation": "<p>The date and time a backup plan is created, in Unix format and Coordinated Universal Time (UTC). The value of <code>CreationDate</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "CreatorRequestId": {"shape": "string", "documentation": "<p>A unique string that identifies the request and allows failed requests to be retried without the risk of running the operation twice. This parameter is optional.</p> <p>If used, this parameter must contain 1 to 50 alphanumeric or '-_.' characters.</p>"}, "IamRoleArn": {"shape": "IAMRoleArn", "documentation": "<p>Specifies the IAM role Amazon Resource Name (ARN) to create the target recovery point; for example, <code>arn:aws:iam::************:role/S3Access</code>.</p>"}}, "documentation": "<p>Contains metadata about a <code>BackupSelection</code> object.</p>"}, "BackupVaultEvent": {"type": "string", "enum": ["BACKUP_JOB_STARTED", "BACKUP_JOB_COMPLETED", "BACKUP_JOB_SUCCESSFUL", "BACKUP_JOB_FAILED", "BACKUP_JOB_EXPIRED", "RESTORE_JOB_STARTED", "RESTORE_JOB_COMPLETED", "RESTORE_JOB_SUCCESSFUL", "RESTORE_JOB_FAILED", "COPY_JOB_STARTED", "COPY_JOB_SUCCESSFUL", "COPY_JOB_FAILED", "RECOVERY_POINT_MODIFIED", "BACKUP_PLAN_CREATED", "BACKUP_PLAN_MODIFIED", "S3_BACKUP_OBJECT_FAILED", "S3_RESTORE_OBJECT_FAILED", "CONTINUOUS_BACKUP_INTERRUPTED", "RECOVERY_POINT_INDEX_COMPLETED", "RECOVERY_POINT_INDEX_DELETED", "RECOVERY_POINT_INDEXING_FAILED"]}, "BackupVaultEvents": {"type": "list", "member": {"shape": "BackupVaultEvent"}}, "BackupVaultList": {"type": "list", "member": {"shape": "BackupVaultListMember"}}, "BackupVaultListMember": {"type": "structure", "members": {"BackupVaultName": {"shape": "BackupVaultName", "documentation": "<p>The name of a logical container where backups are stored. Backup vaults are identified by names that are unique to the account used to create them and the Amazon Web Services Region where they are created.</p>"}, "BackupVaultArn": {"shape": "ARN", "documentation": "<p>An Amazon Resource Name (ARN) that uniquely identifies a backup vault; for example, <code>arn:aws:backup:us-east-1:************:backup-vault:aBackupVault</code>.</p>"}, "VaultType": {"shape": "VaultType", "documentation": "<p>The type of vault in which the described recovery point is stored.</p>"}, "VaultState": {"shape": "VaultState", "documentation": "<p>The current state of the vault.</p>"}, "CreationDate": {"shape": "timestamp", "documentation": "<p>The date and time a resource backup is created, in Unix format and Coordinated Universal Time (UTC). The value of <code>CreationDate</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "EncryptionKeyArn": {"shape": "ARN", "documentation": "<p>A server-side encryption key you can specify to encrypt your backups from services that support full Backup management; for example, <code>arn:aws:kms:us-west-2:************:key/1234abcd-12ab-34cd-56ef-1234567890ab</code>. If you specify a key, you must specify its ARN, not its alias. If you do not specify a key, Backup creates a KMS key for you by default.</p> <p>To learn which Backup services support full Backup management and how Backup handles encryption for backups from services that do not yet support full Backup, see <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/encryption.html\"> Encryption for backups in Backup</a> </p>"}, "CreatorRequestId": {"shape": "string", "documentation": "<p>A unique string that identifies the request and allows failed requests to be retried without the risk of running the operation twice. This parameter is optional.</p> <p>If used, this parameter must contain 1 to 50 alphanumeric or '-_.' characters.</p>"}, "NumberOfRecoveryPoints": {"shape": "long", "documentation": "<p>The number of recovery points that are stored in a backup vault.</p>"}, "Locked": {"shape": "Boolean", "documentation": "<p>A Boolean value that indicates whether Backup Vault Lock applies to the selected backup vault. If <code>true</code>, Vault Lock prevents delete and update operations on the recovery points in the selected vault.</p>"}, "MinRetentionDays": {"shape": "<PERSON>", "documentation": "<p>The Backup Vault Lock setting that specifies the minimum retention period that the vault retains its recovery points. If this parameter is not specified, Vault Lock does not enforce a minimum retention period.</p> <p>If specified, any backup or copy job to the vault must have a lifecycle policy with a retention period equal to or longer than the minimum retention period. If the job's retention period is shorter than that minimum retention period, then the vault fails the backup or copy job, and you should either modify your lifecycle settings or use a different vault. Recovery points already stored in the vault prior to Vault Lock are not affected.</p>"}, "MaxRetentionDays": {"shape": "<PERSON>", "documentation": "<p>The Backup Vault Lock setting that specifies the maximum retention period that the vault retains its recovery points. If this parameter is not specified, Vault Lock does not enforce a maximum retention period on the recovery points in the vault (allowing indefinite storage).</p> <p>If specified, any backup or copy job to the vault must have a lifecycle policy with a retention period equal to or shorter than the maximum retention period. If the job's retention period is longer than that maximum retention period, then the vault fails the backup or copy job, and you should either modify your lifecycle settings or use a different vault. Recovery points already stored in the vault prior to Vault Lock are not affected.</p>"}, "LockDate": {"shape": "timestamp", "documentation": "<p>The date and time when Backup Vault Lock configuration becomes immutable, meaning it cannot be changed or deleted.</p> <p>If you applied Vault Lock to your vault without specifying a lock date, you can change your Vault Lock settings, or delete Vault Lock from the vault entirely, at any time.</p> <p>This value is in Unix format, Coordinated Universal Time (UTC), and accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}}, "documentation": "<p>Contains metadata about a backup vault.</p>"}, "BackupVaultName": {"type": "string", "pattern": "^[a-zA-Z0-9\\-\\_]{2,50}$"}, "Boolean": {"type": "boolean"}, "CalculatedLifecycle": {"type": "structure", "members": {"MoveToColdStorageAt": {"shape": "timestamp", "documentation": "<p>A timestamp that specifies when to transition a recovery point to cold storage.</p>"}, "DeleteAt": {"shape": "timestamp", "documentation": "<p>A timestamp that specifies when to delete a recovery point.</p>"}}, "documentation": "<p>Contains <code>DeleteAt</code> and <code>MoveToColdStorageAt</code> timestamps, which are used to specify a lifecycle for a recovery point.</p> <p>The lifecycle defines when a protected resource is transitioned to cold storage and when it expires. Backup transitions and expires backups automatically according to the lifecycle that you define.</p> <p>Backups transitioned to cold storage must be stored in cold storage for a minimum of 90 days. Therefore, the “retention” setting must be 90 days greater than the “transition to cold after days” setting. The “transition to cold after days” setting cannot be changed after a backup has been transitioned to cold.</p> <p>Resource types that can transition to cold storage are listed in the <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/backup-feature-availability.html#features-by-resource\">Feature availability by resource</a> table. Backup ignores this expression for other resource types.</p>"}, "CancelLegalHoldInput": {"type": "structure", "required": ["LegalHoldId", "CancelDescription"], "members": {"LegalHoldId": {"shape": "string", "documentation": "<p>The ID of the legal hold.</p>", "location": "uri", "locationName": "legalHoldId"}, "CancelDescription": {"shape": "string", "documentation": "<p>A string the describes the reason for removing the legal hold.</p>", "location": "querystring", "locationName": "cancelDescription"}, "RetainRecordInDays": {"shape": "<PERSON>", "documentation": "<p>The integer amount, in days, after which to remove legal hold.</p>", "location": "querystring", "locationName": "retainRecordInDays"}}}, "CancelLegalHoldOutput": {"type": "structure", "members": {}}, "ComplianceResourceIdList": {"type": "list", "member": {"shape": "string"}, "max": 100, "min": 1}, "Condition": {"type": "structure", "required": ["ConditionType", "Condition<PERSON><PERSON>", "ConditionValue"], "members": {"ConditionType": {"shape": "ConditionType", "documentation": "<p>An operation applied to a key-value pair used to assign resources to your backup plan. Condition only supports <code>StringEquals</code>. For more flexible assignment options, including <code>StringLike</code> and the ability to exclude resources from your backup plan, use <code>Conditions</code> (with an \"s\" on the end) for your <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/API_BackupSelection.html\"> <code>BackupSelection</code> </a>.</p>"}, "ConditionKey": {"shape": "Condition<PERSON><PERSON>", "documentation": "<p>The key in a key-value pair. For example, in the tag <code>Department: Accounting</code>, <code>Department</code> is the key.</p>"}, "ConditionValue": {"shape": "ConditionValue", "documentation": "<p>The value in a key-value pair. For example, in the tag <code>Department: Accounting</code>, <code>Accounting</code> is the value.</p>"}}, "documentation": "<p>Contains an array of triplets made up of a condition type (such as <code>StringEquals</code>), a key, and a value. Used to filter resources using their tags and assign them to a backup plan. Case sensitive.</p>"}, "ConditionKey": {"type": "string"}, "ConditionParameter": {"type": "structure", "members": {"ConditionKey": {"shape": "Condition<PERSON><PERSON>", "documentation": "<p>The key in a key-value pair. For example, in the tag <code>Department: Accounting</code>, <code>Department</code> is the key.</p>"}, "ConditionValue": {"shape": "ConditionValue", "documentation": "<p>The value in a key-value pair. For example, in the tag <code>Department: Accounting</code>, <code>Accounting</code> is the value.</p>"}}, "documentation": "<p>Includes information about tags you define to assign tagged resources to a backup plan.</p> <p>Include the prefix <code>aws:ResourceTag</code> in your tags. For example, <code>\"aws:ResourceTag/TagKey1\": \"Value1\"</code>.</p>"}, "ConditionParameters": {"type": "list", "member": {"shape": "ConditionParameter"}}, "ConditionType": {"type": "string", "enum": ["STRINGEQUALS"]}, "ConditionValue": {"type": "string"}, "Conditions": {"type": "structure", "members": {"StringEquals": {"shape": "ConditionParameters", "documentation": "<p>Filters the values of your tagged resources for only those resources that you tagged with the same value. Also called \"exact matching.\"</p>"}, "StringNotEquals": {"shape": "ConditionParameters", "documentation": "<p>Filters the values of your tagged resources for only those resources that you tagged that do not have the same value. Also called \"negated matching.\"</p>"}, "StringLike": {"shape": "ConditionParameters", "documentation": "<p>Filters the values of your tagged resources for matching tag values with the use of a wildcard character (*) anywhere in the string. For example, \"prod*\" or \"*rod*\" matches the tag value \"production\".</p>"}, "StringNotLike": {"shape": "ConditionParameters", "documentation": "<p>Filters the values of your tagged resources for non-matching tag values with the use of a wildcard character (*) anywhere in the string.</p>"}}, "documentation": "<p>Contains information about which resources to include or exclude from a backup plan using their tags. Conditions are case sensitive.</p>"}, "ConflictException": {"type": "structure", "members": {"Code": {"shape": "string"}, "Message": {"shape": "string"}, "Type": {"shape": "string", "documentation": "<p/>"}, "Context": {"shape": "string", "documentation": "<p/>"}}, "documentation": "<p>Backup can't perform the action that you requested until it finishes performing a previous action. Try again later.</p>", "exception": true}, "ControlInputParameter": {"type": "structure", "members": {"ParameterName": {"shape": "ParameterName", "documentation": "<p>The name of a parameter, for example, <code>BackupPlanFrequency</code>.</p>"}, "ParameterValue": {"shape": "ParameterValue", "documentation": "<p>The value of parameter, for example, <code>hourly</code>.</p>"}}, "documentation": "<p>The parameters for a control. A control can have zero, one, or more than one parameter. An example of a control with two parameters is: \"backup plan frequency is at least <code>daily</code> and the retention period is at least <code>1 year</code>\". The first parameter is <code>daily</code>. The second parameter is <code>1 year</code>.</p>"}, "ControlInputParameters": {"type": "list", "member": {"shape": "ControlInputParameter"}}, "ControlName": {"type": "string"}, "ControlScope": {"type": "structure", "members": {"ComplianceResourceIds": {"shape": "ComplianceResourceIdList", "documentation": "<p>The ID of the only Amazon Web Services resource that you want your control scope to contain.</p>"}, "ComplianceResourceTypes": {"shape": "ResourceTypeList", "documentation": "<p>Describes whether the control scope includes one or more types of resources, such as <code>EFS</code> or <code>RDS</code>.</p>"}, "Tags": {"shape": "stringMap", "documentation": "<p>The tag key-value pair applied to those Amazon Web Services resources that you want to trigger an evaluation for a rule. A maximum of one key-value pair can be provided. The tag value is optional, but it cannot be an empty string if you are creating or editing a framework from the console (though the value can be an empty string when included in a CloudFormation template).</p> <p>The structure to assign a tag is: <code>[{\"Key\":\"string\",\"Value\":\"string\"}]</code>.</p>"}}, "documentation": "<p>A framework consists of one or more controls. Each control has its own control scope. The control scope can include one or more resource types, a combination of a tag key and value, or a combination of one resource type and one resource ID. If no scope is specified, evaluations for the rule are triggered when any resource in your recording group changes in configuration.</p> <note> <p>To set a control scope that includes all of a particular resource, leave the <code>ControlScope</code> empty or do not pass it when calling <code>CreateFramework</code>.</p> </note>"}, "CopyAction": {"type": "structure", "required": ["DestinationBackupVaultArn"], "members": {"Lifecycle": {"shape": "Lifecycle"}, "DestinationBackupVaultArn": {"shape": "ARN", "documentation": "<p>An Amazon Resource Name (ARN) that uniquely identifies the destination backup vault for the copied backup. For example, <code>arn:aws:backup:us-east-1:************:backup-vault:aBackupVault</code>.</p>"}}, "documentation": "<p>The details of the copy operation.</p>"}, "CopyActions": {"type": "list", "member": {"shape": "CopyAction"}}, "CopyJob": {"type": "structure", "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The account ID that owns the copy job.</p>"}, "CopyJobId": {"shape": "string", "documentation": "<p>Uniquely identifies a copy job.</p>"}, "SourceBackupVaultArn": {"shape": "ARN", "documentation": "<p>An Amazon Resource Name (ARN) that uniquely identifies a source copy vault; for example, <code>arn:aws:backup:us-east-1:************:backup-vault:aBackupVault</code>. </p>"}, "SourceRecoveryPointArn": {"shape": "ARN", "documentation": "<p>An ARN that uniquely identifies a source recovery point; for example, <code>arn:aws:backup:us-east-1:************:recovery-point:1EB3B5E7-9EB0-435A-A80B-108B488B0D45</code>.</p>"}, "DestinationBackupVaultArn": {"shape": "ARN", "documentation": "<p>An Amazon Resource Name (ARN) that uniquely identifies a destination copy vault; for example, <code>arn:aws:backup:us-east-1:************:backup-vault:aBackupVault</code>.</p>"}, "DestinationRecoveryPointArn": {"shape": "ARN", "documentation": "<p>An ARN that uniquely identifies a destination recovery point; for example, <code>arn:aws:backup:us-east-1:************:recovery-point:1EB3B5E7-9EB0-435A-A80B-108B488B0D45</code>.</p>"}, "ResourceArn": {"shape": "ARN", "documentation": "<p>The Amazon Web Services resource to be copied; for example, an Amazon Elastic Block Store (Amazon EBS) volume or an Amazon Relational Database Service (Amazon RDS) database.</p>"}, "CreationDate": {"shape": "timestamp", "documentation": "<p>The date and time a copy job is created, in Unix format and Coordinated Universal Time (UTC). The value of <code>CreationDate</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "CompletionDate": {"shape": "timestamp", "documentation": "<p>The date and time a copy job is completed, in Unix format and Coordinated Universal Time (UTC). The value of <code>CompletionDate</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "State": {"shape": "CopyJobState", "documentation": "<p>The current state of a copy job.</p>"}, "StatusMessage": {"shape": "string", "documentation": "<p>A detailed message explaining the status of the job to copy a resource.</p>"}, "BackupSizeInBytes": {"shape": "<PERSON>", "documentation": "<p>The size, in bytes, of a copy job.</p>"}, "IamRoleArn": {"shape": "IAMRoleArn", "documentation": "<p>Specifies the IAM role ARN used to copy the target recovery point; for example, <code>arn:aws:iam::************:role/S3Access</code>.</p>"}, "CreatedBy": {"shape": "RecoveryPointCreator"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>The type of Amazon Web Services resource to be copied; for example, an Amazon Elastic Block Store (Amazon EBS) volume or an Amazon Relational Database Service (Amazon RDS) database.</p>"}, "ParentJobId": {"shape": "string", "documentation": "<p>This uniquely identifies a request to <PERSON><PERSON> to copy a resource. The return will be the parent (composite) job ID.</p>"}, "IsParent": {"shape": "boolean", "documentation": "<p>This is a boolean value indicating this is a parent (composite) copy job.</p>"}, "CompositeMemberIdentifier": {"shape": "string", "documentation": "<p>The identifier of a resource within a composite group, such as nested (child) recovery point belonging to a composite (parent) stack. The ID is transferred from the <a href=\"https://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/resources-section-structure.html#resources-section-structure-syntax\"> logical ID</a> within a stack.</p>"}, "NumberOfChildJobs": {"shape": "<PERSON>", "documentation": "<p>The number of child (nested) copy jobs.</p>"}, "ChildJobsInState": {"shape": "CopyJobChildJobsInState", "documentation": "<p>This returns the statistics of the included child (nested) copy jobs.</p>"}, "ResourceName": {"shape": "string", "documentation": "<p>The non-unique name of the resource that belongs to the specified backup.</p>"}, "MessageCategory": {"shape": "string", "documentation": "<p>This parameter is the job count for the specified message category.</p> <p>Example strings may include <code>AccessDenied</code>, <code>SUCCESS</code>, <code>AGGREGATE_ALL</code>, and <code>InvalidParameters</code>. See <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/monitoring.html\">Monitoring</a> for a list of MessageCategory strings.</p> <p>The the value ANY returns count of all message categories.</p> <p> <code>AGGREGATE_ALL</code> aggregates job counts for all message categories and returns the sum</p>"}}, "documentation": "<p>Contains detailed information about a copy job.</p>"}, "CopyJobChildJobsInState": {"type": "map", "key": {"shape": "CopyJobState"}, "value": {"shape": "<PERSON>"}}, "CopyJobState": {"type": "string", "enum": ["CREATED", "RUNNING", "COMPLETED", "FAILED", "PARTIAL"]}, "CopyJobStatus": {"type": "string", "enum": ["CREATED", "RUNNING", "ABORTING", "ABORTED", "COMPLETING", "COMPLETED", "FAILING", "FAILED", "PARTIAL", "AGGREGATE_ALL", "ANY"]}, "CopyJobSummary": {"type": "structure", "members": {"Region": {"shape": "Region", "documentation": "<p>The Amazon Web Services Regions within the job summary.</p>"}, "AccountId": {"shape": "AccountId", "documentation": "<p>The account ID that owns the jobs within the summary.</p>"}, "State": {"shape": "CopyJobStatus", "documentation": "<p>This value is job count for jobs with the specified state.</p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>This value is the job count for the specified resource type. The request <code>GetSupportedResourceTypes</code> returns strings for supported resource types</p>"}, "MessageCategory": {"shape": "MessageCategory", "documentation": "<p>This parameter is the job count for the specified message category.</p> <p>Example strings include <code>AccessDenied</code>, <code>Success</code>, and <code>InvalidParameters</code>. See <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/monitoring.html\">Monitoring</a> for a list of MessageCategory strings.</p> <p>The the value ANY returns count of all message categories.</p> <p> <code>AGGREGATE_ALL</code> aggregates job counts for all message categories and returns the sum.</p>"}, "Count": {"shape": "integer", "documentation": "<p>The value as a number of jobs in a job summary.</p>"}, "StartTime": {"shape": "timestamp", "documentation": "<p>The value of time in number format of a job start time.</p> <p>This value is the time in Unix format, Coordinated Universal Time (UTC), and accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "EndTime": {"shape": "timestamp", "documentation": "<p>The value of time in number format of a job end time.</p> <p>This value is the time in Unix format, Coordinated Universal Time (UTC), and accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}}, "documentation": "<p>This is a summary of copy jobs created or running within the most recent 30 days.</p> <p>The returned summary may contain the following: Region, Account, State, RestourceType, MessageCategory, StartTime, EndTime, and Count of included jobs.</p>"}, "CopyJobSummaryList": {"type": "list", "member": {"shape": "Copy<PERSON>ob<PERSON><PERSON><PERSON><PERSON>"}}, "CopyJobsList": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON><PERSON>"}}, "CreateBackupPlanInput": {"type": "structure", "required": ["BackupPlan"], "members": {"BackupPlan": {"shape": "BackupPlanInput", "documentation": "<p>The body of a backup plan. Includes a <code>BackupPlanName</code> and one or more sets of <code>Rules</code>.</p>"}, "BackupPlanTags": {"shape": "Tags", "documentation": "<p>The tags to assign to the backup plan.</p>"}, "CreatorRequestId": {"shape": "string", "documentation": "<p>Identifies the request and allows failed requests to be retried without the risk of running the operation twice. If the request includes a <code>CreatorRequestId</code> that matches an existing backup plan, that plan is returned. This parameter is optional.</p> <p>If used, this parameter must contain 1 to 50 alphanumeric or '-_.' characters.</p>", "idempotencyToken": true}}}, "CreateBackupPlanOutput": {"type": "structure", "members": {"BackupPlanId": {"shape": "string", "documentation": "<p>The ID of the backup plan.</p>"}, "BackupPlanArn": {"shape": "ARN", "documentation": "<p>An Amazon Resource Name (ARN) that uniquely identifies a backup plan; for example, <code>arn:aws:backup:us-east-1:************:plan:8F81F553-3A74-4A3F-B93D-B3360DC80C50</code>.</p>"}, "CreationDate": {"shape": "timestamp", "documentation": "<p>The date and time that a backup plan is created, in Unix format and Coordinated Universal Time (UTC). The value of <code>CreationDate</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "VersionId": {"shape": "string", "documentation": "<p>Unique, randomly generated, Unicode, UTF-8 encoded strings that are at most 1,024 bytes long. They cannot be edited.</p>"}, "AdvancedBackupSettings": {"shape": "AdvancedBackupSettings", "documentation": "<p>The settings for a resource type. This option is only available for Windows Volume Shadow Copy Service (VSS) backup jobs.</p>"}}}, "CreateBackupSelectionInput": {"type": "structure", "required": ["BackupPlanId", "BackupSelection"], "members": {"BackupPlanId": {"shape": "string", "documentation": "<p>The ID of the backup plan.</p>", "location": "uri", "locationName": "backupPlanId"}, "BackupSelection": {"shape": "BackupSelection", "documentation": "<p>The body of a request to assign a set of resources to a backup plan.</p>"}, "CreatorRequestId": {"shape": "string", "documentation": "<p>A unique string that identifies the request and allows failed requests to be retried without the risk of running the operation twice. This parameter is optional.</p> <p>If used, this parameter must contain 1 to 50 alphanumeric or '-_.' characters.</p>", "idempotencyToken": true}}}, "CreateBackupSelectionOutput": {"type": "structure", "members": {"SelectionId": {"shape": "string", "documentation": "<p>Uniquely identifies the body of a request to assign a set of resources to a backup plan.</p>"}, "BackupPlanId": {"shape": "string", "documentation": "<p>The ID of the backup plan.</p>"}, "CreationDate": {"shape": "timestamp", "documentation": "<p>The date and time a backup selection is created, in Unix format and Coordinated Universal Time (UTC). The value of <code>CreationDate</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}}}, "CreateBackupVaultInput": {"type": "structure", "required": ["BackupVaultName"], "members": {"BackupVaultName": {"shape": "BackupVaultName", "documentation": "<p>The name of a logical container where backups are stored. Backup vaults are identified by names that are unique to the account used to create them and the Amazon Web Services Region where they are created. They consist of letters, numbers, and hyphens.</p>", "location": "uri", "locationName": "backup<PERSON><PERSON><PERSON><PERSON>"}, "BackupVaultTags": {"shape": "Tags", "documentation": "<p>The tags to assign to the backup vault.</p>"}, "EncryptionKeyArn": {"shape": "ARN", "documentation": "<p>The server-side encryption key that is used to protect your backups; for example, <code>arn:aws:kms:us-west-2:************:key/1234abcd-12ab-34cd-56ef-1234567890ab</code>.</p>"}, "CreatorRequestId": {"shape": "string", "documentation": "<p>A unique string that identifies the request and allows failed requests to be retried without the risk of running the operation twice. This parameter is optional.</p> <p>If used, this parameter must contain 1 to 50 alphanumeric or '-_.' characters.</p>", "idempotencyToken": true}}}, "CreateBackupVaultOutput": {"type": "structure", "members": {"BackupVaultName": {"shape": "BackupVaultName", "documentation": "<p>The name of a logical container where backups are stored. Backup vaults are identified by names that are unique to the account used to create them and the Region where they are created. They consist of lowercase letters, numbers, and hyphens.</p>"}, "BackupVaultArn": {"shape": "ARN", "documentation": "<p>An Amazon Resource Name (ARN) that uniquely identifies a backup vault; for example, <code>arn:aws:backup:us-east-1:************:backup-vault:aBackupVault</code>.</p>"}, "CreationDate": {"shape": "timestamp", "documentation": "<p>The date and time a backup vault is created, in Unix format and Coordinated Universal Time (UTC). The value of <code>CreationDate</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}}}, "CreateFrameworkInput": {"type": "structure", "required": ["FrameworkName", "FrameworkControls"], "members": {"FrameworkName": {"shape": "FrameworkName", "documentation": "<p>The unique name of the framework. The name must be between 1 and 256 characters, starting with a letter, and consisting of letters (a-z, A-Z), numbers (0-9), and underscores (_).</p>"}, "FrameworkDescription": {"shape": "FrameworkDescription", "documentation": "<p>An optional description of the framework with a maximum of 1,024 characters.</p>"}, "FrameworkControls": {"shape": "FrameworkControls", "documentation": "<p>The controls that make up the framework. Each control in the list has a name, input parameters, and scope.</p>"}, "IdempotencyToken": {"shape": "string", "documentation": "<p>A customer-chosen string that you can use to distinguish between otherwise identical calls to <code>CreateFrameworkInput</code>. Retrying a successful request with the same idempotency token results in a success message with no action taken.</p>", "idempotencyToken": true}, "FrameworkTags": {"shape": "stringMap", "documentation": "<p>The tags to assign to the framework.</p>"}}}, "CreateFrameworkOutput": {"type": "structure", "members": {"FrameworkName": {"shape": "FrameworkName", "documentation": "<p>The unique name of the framework. The name must be between 1 and 256 characters, starting with a letter, and consisting of letters (a-z, A-Z), numbers (0-9), and underscores (_).</p>"}, "FrameworkArn": {"shape": "ARN", "documentation": "<p>An Amazon Resource Name (ARN) that uniquely identifies a resource. The format of the ARN depends on the resource type.</p>"}}}, "CreateLegalHoldInput": {"type": "structure", "required": ["Title", "Description"], "members": {"Title": {"shape": "string", "documentation": "<p>The title of the legal hold.</p>"}, "Description": {"shape": "string", "documentation": "<p>The description of the legal hold.</p>"}, "IdempotencyToken": {"shape": "string", "documentation": "<p>This is a user-chosen string used to distinguish between otherwise identical calls. Retrying a successful request with the same idempotency token results in a success message with no action taken.</p>", "idempotencyToken": true}, "RecoveryPointSelection": {"shape": "RecoveryPointSelection", "documentation": "<p>The criteria to assign a set of resources, such as resource types or backup vaults.</p>"}, "Tags": {"shape": "Tags", "documentation": "<p>Optional tags to include. A tag is a key-value pair you can use to manage, filter, and search for your resources. Allowed characters include UTF-8 letters, numbers, spaces, and the following characters: + - = . _ : /. </p>"}}}, "CreateLegalHoldOutput": {"type": "structure", "members": {"Title": {"shape": "string", "documentation": "<p>The title of the legal hold.</p>"}, "Status": {"shape": "LegalHoldStatus", "documentation": "<p>The status of the legal hold.</p>"}, "Description": {"shape": "string", "documentation": "<p>The description of the legal hold.</p>"}, "LegalHoldId": {"shape": "string", "documentation": "<p>The ID of the legal hold.</p>"}, "LegalHoldArn": {"shape": "ARN", "documentation": "<p>The Amazon Resource Name (ARN) of the legal hold.</p>"}, "CreationDate": {"shape": "timestamp", "documentation": "<p>The time when the legal hold was created.</p>"}, "RecoveryPointSelection": {"shape": "RecoveryPointSelection", "documentation": "<p>The criteria to assign to a set of resources, such as resource types or backup vaults.</p>"}}}, "CreateLogicallyAirGappedBackupVaultInput": {"type": "structure", "required": ["BackupVaultName", "MinRetentionDays", "MaxRetentionDays"], "members": {"BackupVaultName": {"shape": "BackupVaultName", "documentation": "<p>The name of a logical container where backups are stored. Logically air-gapped backup vaults are identified by names that are unique to the account used to create them and the Region where they are created.</p>", "location": "uri", "locationName": "backup<PERSON><PERSON><PERSON><PERSON>"}, "BackupVaultTags": {"shape": "Tags", "documentation": "<p>The tags to assign to the vault.</p>"}, "CreatorRequestId": {"shape": "string", "documentation": "<p>The ID of the creation request.</p> <p>This parameter is optional. If used, this parameter must contain 1 to 50 alphanumeric or '-_.' characters.</p>", "idempotencyToken": true}, "MinRetentionDays": {"shape": "<PERSON>", "documentation": "<p>This setting specifies the minimum retention period that the vault retains its recovery points.</p> <p>The minimum value accepted is 7 days.</p>"}, "MaxRetentionDays": {"shape": "<PERSON>", "documentation": "<p>The maximum retention period that the vault retains its recovery points.</p>"}}}, "CreateLogicallyAirGappedBackupVaultOutput": {"type": "structure", "members": {"BackupVaultName": {"shape": "BackupVaultName", "documentation": "<p>The name of a logical container where backups are stored. Logically air-gapped backup vaults are identified by names that are unique to the account used to create them and the Region where they are created.</p>"}, "BackupVaultArn": {"shape": "ARN", "documentation": "<p>The ARN (Amazon Resource Name) of the vault.</p>"}, "CreationDate": {"shape": "timestamp", "documentation": "<p>The date and time when the vault was created.</p> <p>This value is in Unix format, Coordinated Universal Time (UTC), and accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "VaultState": {"shape": "VaultState", "documentation": "<p>The current state of the vault.</p>"}}}, "CreateReportPlanInput": {"type": "structure", "required": ["ReportPlanName", "ReportDeliveryChannel", "ReportSetting"], "members": {"ReportPlanName": {"shape": "ReportPlanName", "documentation": "<p>The unique name of the report plan. The name must be between 1 and 256 characters, starting with a letter, and consisting of letters (a-z, A-Z), numbers (0-9), and underscores (_).</p>"}, "ReportPlanDescription": {"shape": "ReportPlanDescription", "documentation": "<p>An optional description of the report plan with a maximum of 1,024 characters.</p>"}, "ReportDeliveryChannel": {"shape": "ReportDeliveryChannel", "documentation": "<p>A structure that contains information about where and how to deliver your reports, specifically your Amazon S3 bucket name, S3 key prefix, and the formats of your reports.</p>"}, "ReportSetting": {"shape": "ReportSetting", "documentation": "<p>Identifies the report template for the report. Reports are built using a report template. The report templates are:</p> <p> <code>RESOURCE_COMPLIANCE_REPORT | CONTROL_COMPLIANCE_REPORT | BACKUP_JOB_REPORT | COPY_JOB_REPORT | RESTORE_JOB_REPORT</code> </p> <p>If the report template is <code>RESOURCE_COMPLIANCE_REPORT</code> or <code>CONTROL_COMPLIANCE_REPORT</code>, this API resource also describes the report coverage by Amazon Web Services Regions and frameworks.</p>"}, "ReportPlanTags": {"shape": "stringMap", "documentation": "<p>The tags to assign to the report plan.</p>"}, "IdempotencyToken": {"shape": "string", "documentation": "<p>A customer-chosen string that you can use to distinguish between otherwise identical calls to <code>CreateReportPlanInput</code>. Retrying a successful request with the same idempotency token results in a success message with no action taken.</p>", "idempotencyToken": true}}}, "CreateReportPlanOutput": {"type": "structure", "members": {"ReportPlanName": {"shape": "ReportPlanName", "documentation": "<p>The unique name of the report plan.</p>"}, "ReportPlanArn": {"shape": "ARN", "documentation": "<p>An Amazon Resource Name (ARN) that uniquely identifies a resource. The format of the ARN depends on the resource type.</p>"}, "CreationTime": {"shape": "timestamp", "documentation": "<p>The date and time a backup vault is created, in Unix format and Coordinated Universal Time (UTC). The value of <code>CreationTime</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}}}, "CreateRestoreAccessBackupVaultInput": {"type": "structure", "required": ["SourceBackupVaultArn"], "members": {"SourceBackupVaultArn": {"shape": "ARN", "documentation": "<p>The ARN of the source backup vault containing the recovery points to which temporary access is requested.</p>"}, "BackupVaultName": {"shape": "BackupVaultName", "documentation": "<p>The name of the backup vault to associate with an MPA approval team.</p>"}, "BackupVaultTags": {"shape": "Tags", "documentation": "<p>Optional tags to assign to the restore access backup vault.</p>"}, "CreatorRequestId": {"shape": "string", "documentation": "<p>A unique string that identifies the request and allows failed requests to be retried without the risk of executing the operation twice.</p>", "idempotencyToken": true}, "RequesterComment": {"shape": "RequesterComment", "documentation": "<p>A comment explaining the reason for requesting restore access to the backup vault.</p>"}}}, "CreateRestoreAccessBackupVaultOutput": {"type": "structure", "members": {"RestoreAccessBackupVaultArn": {"shape": "ARN", "documentation": "<p>The ARN that uniquely identifies the created restore access backup vault.</p>"}, "VaultState": {"shape": "VaultState", "documentation": "<p>The current state of the restore access backup vault.</p>"}, "RestoreAccessBackupVaultName": {"shape": "BackupVaultName", "documentation": "<p>The name of the created restore access backup vault.</p>"}, "CreationDate": {"shape": "timestamp", "documentation": "<p>&gt;The date and time when the restore access backup vault was created, in Unix format and Coordinated Universal Time </p>"}}}, "CreateRestoreTestingPlanInput": {"type": "structure", "required": ["RestoreTestingPlan"], "members": {"CreatorRequestId": {"shape": "String", "documentation": "<p>This is a unique string that identifies the request and allows failed requests to be retriedwithout the risk of running the operation twice. This parameter is optional. If used, this parameter must contain 1 to 50 alphanumeric or '-_.' characters.</p>"}, "RestoreTestingPlan": {"shape": "RestoreTestingPlanForCreate", "documentation": "<p>A restore testing plan must contain a unique <code>RestoreTestingPlanName</code> string you create and must contain a <code>ScheduleExpression</code> cron. You may optionally include a <code>StartWindowHours</code> integer and a <code>CreatorRequestId</code> string.</p> <p>The <code>RestoreTestingPlanName</code> is a unique string that is the name of the restore testing plan. This cannot be changed after creation, and it must consist of only alphanumeric characters and underscores.</p>"}, "Tags": {"shape": "SensitiveStringMap", "documentation": "<p>The tags to assign to the restore testing plan.</p>"}}}, "CreateRestoreTestingPlanOutput": {"type": "structure", "required": ["CreationTime", "RestoreTestingPlanArn", "RestoreTestingPlanName"], "members": {"CreationTime": {"shape": "Timestamp", "documentation": "<p>The date and time a restore testing plan was created, in Unix format and Coordinated Universal Time (UTC). The value of <code>CreationTime</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087AM.</p>"}, "RestoreTestingPlanArn": {"shape": "String", "documentation": "<p>An Amazon Resource Name (ARN) that uniquely identifies the created restore testing plan.</p>"}, "RestoreTestingPlanName": {"shape": "String", "documentation": "<p>This unique string is the name of the restore testing plan.</p> <p>The name cannot be changed after creation. The name consists of only alphanumeric characters and underscores. Maximum length is 50.</p>"}}}, "CreateRestoreTestingSelectionInput": {"type": "structure", "required": ["RestoreTestingPlanName", "RestoreTestingSelection"], "members": {"CreatorRequestId": {"shape": "String", "documentation": "<p>This is an optional unique string that identifies the request and allows failed requests to be retried without the risk of running the operation twice. If used, this parameter must contain 1 to 50 alphanumeric or '-_.' characters.</p>"}, "RestoreTestingPlanName": {"shape": "String", "documentation": "<p>Input the restore testing plan name that was returned from the related CreateRestoreTestingPlan request.</p>", "location": "uri", "locationName": "RestoreTestingPlanName"}, "RestoreTestingSelection": {"shape": "RestoreTestingSelectionForCreate", "documentation": "<p>This consists of <code>RestoreTestingSelectionName</code>, <code>ProtectedResourceType</code>, and one of the following:</p> <ul> <li> <p> <code>ProtectedResourceArns</code> </p> </li> <li> <p> <code>ProtectedResourceConditions</code> </p> </li> </ul> <p>Each protected resource type can have one single value.</p> <p>A restore testing selection can include a wildcard value (\"*\") for <code>ProtectedResourceArns</code> along with <code>ProtectedResourceConditions</code>. Alternatively, you can include up to 30 specific protected resource ARNs in <code>ProtectedResourceArns</code>.</p>"}}}, "CreateRestoreTestingSelectionOutput": {"type": "structure", "required": ["CreationTime", "RestoreTestingPlanArn", "RestoreTestingPlanName", "RestoreTestingSelectionName"], "members": {"CreationTime": {"shape": "Timestamp", "documentation": "<p>The time that the resource testing selection was created.</p>"}, "RestoreTestingPlanArn": {"shape": "String", "documentation": "<p>The ARN of the restore testing plan with which the restore testing selection is associated.</p>"}, "RestoreTestingPlanName": {"shape": "String", "documentation": "<p>The name of the restore testing plan.</p> <p>The name cannot be changed after creation. The name consists of only alphanumeric characters and underscores. Maximum length is 50.</p>"}, "RestoreTestingSelectionName": {"shape": "String", "documentation": "<p>The name of the restore testing selection for the related restore testing plan.</p>"}}}, "CronExpression": {"type": "string"}, "DateRange": {"type": "structure", "required": ["FromDate", "ToDate"], "members": {"FromDate": {"shape": "timestamp", "documentation": "<p>This value is the beginning date, inclusive.</p> <p>The date and time are in Unix format and Coordinated Universal Time (UTC), and it is accurate to milliseconds (milliseconds are optional).</p>"}, "ToDate": {"shape": "timestamp", "documentation": "<p>This value is the end date, inclusive.</p> <p>The date and time are in Unix format and Coordinated Universal Time (UTC), and it is accurate to milliseconds (milliseconds are optional).</p>"}}, "documentation": "<p>This is a resource filter containing FromDate: DateTime and ToDate: DateTime. Both values are required. Future DateTime values are not permitted.</p> <p>The date and time are in Unix format and Coordinated Universal Time (UTC), and it is accurate to milliseconds ((milliseconds are optional). For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "DeleteBackupPlanInput": {"type": "structure", "required": ["BackupPlanId"], "members": {"BackupPlanId": {"shape": "string", "documentation": "<p>Uniquely identifies a backup plan.</p>", "location": "uri", "locationName": "backupPlanId"}}}, "DeleteBackupPlanOutput": {"type": "structure", "members": {"BackupPlanId": {"shape": "string", "documentation": "<p>Uniquely identifies a backup plan.</p>"}, "BackupPlanArn": {"shape": "ARN", "documentation": "<p>An Amazon Resource Name (ARN) that uniquely identifies a backup plan; for example, <code>arn:aws:backup:us-east-1:************:plan:8F81F553-3A74-4A3F-B93D-B3360DC80C50</code>.</p>"}, "DeletionDate": {"shape": "timestamp", "documentation": "<p>The date and time a backup plan is deleted, in Unix format and Coordinated Universal Time (UTC). The value of <code>DeletionDate</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "VersionId": {"shape": "string", "documentation": "<p>Unique, randomly generated, Unicode, UTF-8 encoded strings that are at most 1,024 bytes long. Version IDs cannot be edited.</p>"}}}, "DeleteBackupSelectionInput": {"type": "structure", "required": ["BackupPlanId", "SelectionId"], "members": {"BackupPlanId": {"shape": "string", "documentation": "<p>Uniquely identifies a backup plan.</p>", "location": "uri", "locationName": "backupPlanId"}, "SelectionId": {"shape": "string", "documentation": "<p>Uniquely identifies the body of a request to assign a set of resources to a backup plan.</p>", "location": "uri", "locationName": "selectionId"}}}, "DeleteBackupVaultAccessPolicyInput": {"type": "structure", "required": ["BackupVaultName"], "members": {"BackupVaultName": {"shape": "BackupVaultName", "documentation": "<p>The name of a logical container where backups are stored. Backup vaults are identified by names that are unique to the account used to create them and the Amazon Web Services Region where they are created. They consist of lowercase letters, numbers, and hyphens.</p>", "location": "uri", "locationName": "backup<PERSON><PERSON><PERSON><PERSON>"}}}, "DeleteBackupVaultInput": {"type": "structure", "required": ["BackupVaultName"], "members": {"BackupVaultName": {"shape": "string", "documentation": "<p>The name of a logical container where backups are stored. Backup vaults are identified by names that are unique to the account used to create them and the Amazon Web Services Region where they are created.</p>", "location": "uri", "locationName": "backup<PERSON><PERSON><PERSON><PERSON>"}}}, "DeleteBackupVaultLockConfigurationInput": {"type": "structure", "required": ["BackupVaultName"], "members": {"BackupVaultName": {"shape": "BackupVaultName", "documentation": "<p>The name of the backup vault from which to delete Backup Vault Lock.</p>", "location": "uri", "locationName": "backup<PERSON><PERSON><PERSON><PERSON>"}}}, "DeleteBackupVaultNotificationsInput": {"type": "structure", "required": ["BackupVaultName"], "members": {"BackupVaultName": {"shape": "BackupVaultName", "documentation": "<p>The name of a logical container where backups are stored. Backup vaults are identified by names that are unique to the account used to create them and the Region where they are created.</p>", "location": "uri", "locationName": "backup<PERSON><PERSON><PERSON><PERSON>"}}}, "DeleteFrameworkInput": {"type": "structure", "required": ["FrameworkName"], "members": {"FrameworkName": {"shape": "FrameworkName", "documentation": "<p>The unique name of a framework.</p>", "location": "uri", "locationName": "frameworkName"}}}, "DeleteRecoveryPointInput": {"type": "structure", "required": ["BackupVaultName", "RecoveryPointArn"], "members": {"BackupVaultName": {"shape": "BackupVaultName", "documentation": "<p>The name of a logical container where backups are stored. Backup vaults are identified by names that are unique to the account used to create them and the Amazon Web Services Region where they are created.</p>", "location": "uri", "locationName": "backup<PERSON><PERSON><PERSON><PERSON>"}, "RecoveryPointArn": {"shape": "ARN", "documentation": "<p>An Amazon Resource Name (ARN) that uniquely identifies a recovery point; for example, <code>arn:aws:backup:us-east-1:************:recovery-point:1EB3B5E7-9EB0-435A-A80B-108B488B0D45</code>.</p>", "location": "uri", "locationName": "recoveryPointArn"}}}, "DeleteReportPlanInput": {"type": "structure", "required": ["ReportPlanName"], "members": {"ReportPlanName": {"shape": "ReportPlanName", "documentation": "<p>The unique name of a report plan.</p>", "location": "uri", "locationName": "reportPlanName"}}}, "DeleteRestoreTestingPlanInput": {"type": "structure", "required": ["RestoreTestingPlanName"], "members": {"RestoreTestingPlanName": {"shape": "String", "documentation": "<p>Required unique name of the restore testing plan you wish to delete.</p>", "location": "uri", "locationName": "RestoreTestingPlanName"}}}, "DeleteRestoreTestingSelectionInput": {"type": "structure", "required": ["RestoreTestingPlanName", "RestoreTestingSelectionName"], "members": {"RestoreTestingPlanName": {"shape": "String", "documentation": "<p>Required unique name of the restore testing plan that contains the restore testing selection you wish to delete.</p>", "location": "uri", "locationName": "RestoreTestingPlanName"}, "RestoreTestingSelectionName": {"shape": "String", "documentation": "<p>Required unique name of the restore testing selection you wish to delete.</p>", "location": "uri", "locationName": "RestoreTestingSelectionName"}}}, "DependencyFailureException": {"type": "structure", "members": {"Code": {"shape": "string"}, "Message": {"shape": "string"}, "Type": {"shape": "string", "documentation": "<p/>"}, "Context": {"shape": "string", "documentation": "<p/>"}}, "documentation": "<p>A dependent Amazon Web Services service or resource returned an error to the Backup service, and the action cannot be completed.</p>", "exception": true, "fault": true}, "DescribeBackupJobInput": {"type": "structure", "required": ["BackupJobId"], "members": {"BackupJobId": {"shape": "string", "documentation": "<p>Uniquely identifies a request to Back<PERSON> to back up a resource.</p>", "location": "uri", "locationName": "backup<PERSON>ob<PERSON>d"}}}, "DescribeBackupJobOutput": {"type": "structure", "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>Returns the account ID that owns the backup job.</p>"}, "BackupJobId": {"shape": "string", "documentation": "<p>Uniquely identifies a request to Back<PERSON> to back up a resource.</p>"}, "BackupVaultName": {"shape": "BackupVaultName", "documentation": "<p>The name of a logical container where backups are stored. Backup vaults are identified by names that are unique to the account used to create them and the Amazon Web Services Region where they are created.</p>"}, "BackupVaultArn": {"shape": "ARN", "documentation": "<p>An Amazon Resource Name (ARN) that uniquely identifies a backup vault; for example, <code>arn:aws:backup:us-east-1:************:backup-vault:aBackupVault</code>.</p>"}, "RecoveryPointArn": {"shape": "ARN", "documentation": "<p>An ARN that uniquely identifies a recovery point; for example, <code>arn:aws:backup:us-east-1:************:recovery-point:1EB3B5E7-9EB0-435A-A80B-108B488B0D45</code>.</p>"}, "ResourceArn": {"shape": "ARN", "documentation": "<p>An ARN that uniquely identifies a saved resource. The format of the ARN depends on the resource type.</p>"}, "CreationDate": {"shape": "timestamp", "documentation": "<p>The date and time that a backup job is created, in Unix format and Coordinated Universal Time (UTC). The value of <code>CreationDate</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "CompletionDate": {"shape": "timestamp", "documentation": "<p>The date and time that a job to create a backup job is completed, in Unix format and Coordinated Universal Time (UTC). The value of <code>CompletionDate</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "State": {"shape": "BackupJobState", "documentation": "<p>The current state of a backup job.</p>"}, "StatusMessage": {"shape": "string", "documentation": "<p>A detailed message explaining the status of the job to back up a resource.</p>"}, "PercentDone": {"shape": "string", "documentation": "<p>Contains an estimated percentage that is complete of a job at the time the job status was queried.</p>"}, "BackupSizeInBytes": {"shape": "<PERSON>", "documentation": "<p>The size, in bytes, of a backup (recovery point).</p> <p>This value can render differently depending on the resource type as <PERSON>up pulls in data information from other Amazon Web Services services. For example, the value returned may show a value of <code>0</code>, which may differ from the anticipated value.</p> <p>The expected behavior for values by resource type are described as follows:</p> <ul> <li> <p>Amazon Aurora, Amazon DocumentDB, and Amazon Neptune do not have this value populate from the operation <code>GetBackupJobStatus</code>.</p> </li> <li> <p>For Amazon DynamoDB with advanced features, this value refers to the size of the recovery point (backup).</p> </li> <li> <p>Amazon EC2 and Amazon EBS show volume size (provisioned storage) returned as part of this value. Amazon EBS does not return backup size information; snapshot size will have the same value as the original resource that was backed up.</p> </li> <li> <p>For Amazon EFS, this value refers to the delta bytes transferred during a backup.</p> </li> <li> <p>Amazon FSx does not populate this value from the operation <code>GetBackupJobStatus</code> for FSx file systems.</p> </li> <li> <p>An Amazon RDS instance will show as <code>0</code>.</p> </li> <li> <p>For virtual machines running VMware, this value is passed to Backup through an asynchronous workflow, which can mean this displayed value can under-represent the actual backup size.</p> </li> </ul>"}, "IamRoleArn": {"shape": "IAMRoleArn", "documentation": "<p>Specifies the IAM role ARN used to create the target recovery point; for example, <code>arn:aws:iam::************:role/S3Access</code>.</p>"}, "CreatedBy": {"shape": "RecoveryPointCreator", "documentation": "<p>Contains identifying information about the creation of a backup job, including the <code>BackupPlanArn</code>, <code>BackupPlanId</code>, <code>BackupPlanVersion</code>, and <code>BackupRuleId</code> of the backup plan that is used to create it.</p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>The type of Amazon Web Services resource to be backed up; for example, an Amazon Elastic Block Store (Amazon EBS) volume or an Amazon Relational Database Service (Amazon RDS) database.</p>"}, "BytesTransferred": {"shape": "<PERSON>", "documentation": "<p>The size in bytes transferred to a backup vault at the time that the job status was queried.</p>"}, "ExpectedCompletionDate": {"shape": "timestamp", "documentation": "<p>The date and time that a job to back up resources is expected to be completed, in Unix format and Coordinated Universal Time (UTC). The value of <code>ExpectedCompletionDate</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "StartBy": {"shape": "timestamp", "documentation": "<p>Specifies the time in Unix format and Coordinated Universal Time (UTC) when a backup job must be started before it is canceled. The value is calculated by adding the start window to the scheduled time. So if the scheduled time were 6:00 PM and the start window is 2 hours, the <code>StartBy</code> time would be 8:00 PM on the date specified. The value of <code>StartBy</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "BackupOptions": {"shape": "BackupOptions", "documentation": "<p>Represents the options specified as part of backup plan or on-demand backup job.</p>"}, "BackupType": {"shape": "string", "documentation": "<p>Represents the actual backup type selected for a backup job. For example, if a successful Windows Volume Shadow Copy Service (VSS) backup was taken, <code>BackupType</code> returns <code>\"WindowsVSS\"</code>. If <code>BackupType</code> is empty, then the backup type was a regular backup.</p>"}, "ParentJobId": {"shape": "string", "documentation": "<p>This returns the parent (composite) resource backup job ID.</p>"}, "IsParent": {"shape": "boolean", "documentation": "<p>This returns the boolean value that a backup job is a parent (composite) job.</p>"}, "NumberOfChildJobs": {"shape": "<PERSON>", "documentation": "<p>This returns the number of child (nested) backup jobs.</p>"}, "ChildJobsInState": {"shape": "BackupJobChildJobsInState", "documentation": "<p>This returns the statistics of the included child (nested) backup jobs.</p>"}, "ResourceName": {"shape": "string", "documentation": "<p>The non-unique name of the resource that belongs to the specified backup.</p>"}, "InitiationDate": {"shape": "timestamp", "documentation": "<p>The date a backup job was initiated.</p>"}, "MessageCategory": {"shape": "string", "documentation": "<p>The job count for the specified message category.</p> <p>Example strings may include <code>AccessDenied</code>, <code>SUCCESS</code>, <code>AGGREGATE_ALL</code>, and <code>INVALIDPARAMETERS</code>. View <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/monitoring.html\">Monitoring</a> for a list of accepted MessageCategory strings.</p>"}}}, "DescribeBackupVaultInput": {"type": "structure", "required": ["BackupVaultName"], "members": {"BackupVaultName": {"shape": "string", "documentation": "<p>The name of a logical container where backups are stored. Backup vaults are identified by names that are unique to the account used to create them and the Amazon Web Services Region where they are created.</p>", "location": "uri", "locationName": "backup<PERSON><PERSON><PERSON><PERSON>"}, "BackupVaultAccountId": {"shape": "string", "documentation": "<p>The account ID of the specified backup vault.</p>", "location": "querystring", "locationName": "backupVaultAccountId"}}}, "DescribeBackupVaultOutput": {"type": "structure", "members": {"BackupVaultName": {"shape": "string", "documentation": "<p>The name of a logical container where backups are stored. Backup vaults are identified by names that are unique to the account used to create them and the Region where they are created.</p>"}, "BackupVaultArn": {"shape": "ARN", "documentation": "<p>An Amazon Resource Name (ARN) that uniquely identifies a backup vault; for example, <code>arn:aws:backup:us-east-1:************:backup-vault:aBackupVault</code>.</p>"}, "VaultType": {"shape": "VaultType", "documentation": "<p>The type of vault described.</p>"}, "VaultState": {"shape": "VaultState", "documentation": "<p>The current state of the vault.-&gt;</p>"}, "EncryptionKeyArn": {"shape": "ARN", "documentation": "<p>The server-side encryption key that is used to protect your backups; for example, <code>arn:aws:kms:us-west-2:************:key/1234abcd-12ab-34cd-56ef-1234567890ab</code>.</p>"}, "CreationDate": {"shape": "timestamp", "documentation": "<p>The date and time that a backup vault is created, in Unix format and Coordinated Universal Time (UTC). The value of <code>CreationDate</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "CreatorRequestId": {"shape": "string", "documentation": "<p>A unique string that identifies the request and allows failed requests to be retried without the risk of running the operation twice. This parameter is optional. If used, this parameter must contain 1 to 50 alphanumeric or '-_.' characters.</p>"}, "NumberOfRecoveryPoints": {"shape": "long", "documentation": "<p>The number of recovery points that are stored in a backup vault.</p> <p>Recovery point count value displayed in the console can be an approximation. Use <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/API_ListRecoveryPointsByBackupVault.html\"> <code>ListRecoveryPointsByBackupVault</code> </a> API to obtain the exact count.</p>"}, "Locked": {"shape": "Boolean", "documentation": "<p>A Boolean that indicates whether Backup Vault Lock is currently protecting the backup vault. <code>True</code> means that Vault Lock causes delete or update operations on the recovery points stored in the vault to fail.</p>"}, "MinRetentionDays": {"shape": "<PERSON>", "documentation": "<p>The Backup Vault Lock setting that specifies the minimum retention period that the vault retains its recovery points. If this parameter is not specified, Vault Lock will not enforce a minimum retention period.</p> <p>If specified, any backup or copy job to the vault must have a lifecycle policy with a retention period equal to or longer than the minimum retention period. If the job's retention period is shorter than that minimum retention period, then the vault fails the backup or copy job, and you should either modify your lifecycle settings or use a different vault. Recovery points already stored in the vault prior to Vault Lock are not affected.</p>"}, "MaxRetentionDays": {"shape": "<PERSON>", "documentation": "<p>The Backup Vault Lock setting that specifies the maximum retention period that the vault retains its recovery points. If this parameter is not specified, Vault Lock does not enforce a maximum retention period on the recovery points in the vault (allowing indefinite storage).</p> <p>If specified, any backup or copy job to the vault must have a lifecycle policy with a retention period equal to or shorter than the maximum retention period. If the job's retention period is longer than that maximum retention period, then the vault fails the backup or copy job, and you should either modify your lifecycle settings or use a different vault. Recovery points already stored in the vault prior to Vault Lock are not affected.</p>"}, "LockDate": {"shape": "timestamp", "documentation": "<p>The date and time when Backup Vault Lock configuration cannot be changed or deleted.</p> <p>If you applied Vault Lock to your vault without specifying a lock date, you can change any of your Vault Lock settings, or delete Vault Lock from the vault entirely, at any time.</p> <p>This value is in Unix format, Coordinated Universal Time (UTC), and accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "SourceBackupVaultArn": {"shape": "ARN", "documentation": "<p>The ARN of the source backup vault from which this restore access backup vault was created.</p>"}, "MpaApprovalTeamArn": {"shape": "ARN", "documentation": "<p>The ARN of the MPA approval team associated with this backup vault.</p>"}, "MpaSessionArn": {"shape": "ARN", "documentation": "<p>The ARN of the MPA session associated with this backup vault.</p>"}, "LatestMpaApprovalTeamUpdate": {"shape": "LatestMpaApprovalTeamUpdate", "documentation": "<p>Information about the latest update to the MPA approval team association for this backup vault.</p>"}}}, "DescribeCopyJobInput": {"type": "structure", "required": ["CopyJobId"], "members": {"CopyJobId": {"shape": "string", "documentation": "<p>Uniquely identifies a copy job.</p>", "location": "uri", "locationName": "copyJobId"}}}, "DescribeCopyJobOutput": {"type": "structure", "members": {"CopyJob": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>Contains detailed information about a copy job.</p>"}}}, "DescribeFrameworkInput": {"type": "structure", "required": ["FrameworkName"], "members": {"FrameworkName": {"shape": "FrameworkName", "documentation": "<p>The unique name of a framework.</p>", "location": "uri", "locationName": "frameworkName"}}}, "DescribeFrameworkOutput": {"type": "structure", "members": {"FrameworkName": {"shape": "FrameworkName", "documentation": "<p>The unique name of a framework.</p>"}, "FrameworkArn": {"shape": "ARN", "documentation": "<p>An Amazon Resource Name (ARN) that uniquely identifies a resource. The format of the ARN depends on the resource type.</p>"}, "FrameworkDescription": {"shape": "FrameworkDescription", "documentation": "<p>An optional description of the framework.</p>"}, "FrameworkControls": {"shape": "FrameworkControls", "documentation": "<p>The controls that make up the framework. Each control in the list has a name, input parameters, and scope.</p>"}, "CreationTime": {"shape": "timestamp", "documentation": "<p>The date and time that a framework is created, in ISO 8601 representation. The value of <code>CreationTime</code> is accurate to milliseconds. For example, 2020-07-10T15:00:00.000-08:00 represents the 10th of July 2020 at 3:00 PM 8 hours behind UTC.</p>"}, "DeploymentStatus": {"shape": "string", "documentation": "<p>The deployment status of a framework. The statuses are:</p> <p> <code>CREATE_IN_PROGRESS | UPDATE_IN_PROGRESS | DELETE_IN_PROGRESS | COMPLETED | FAILED</code> </p>"}, "FrameworkStatus": {"shape": "string", "documentation": "<p>A framework consists of one or more controls. Each control governs a resource, such as backup plans, backup selections, backup vaults, or recovery points. You can also turn Config recording on or off for each resource. The statuses are:</p> <ul> <li> <p> <code>ACTIVE</code> when recording is turned on for all resources governed by the framework.</p> </li> <li> <p> <code>PARTIALLY_ACTIVE</code> when recording is turned off for at least one resource governed by the framework.</p> </li> <li> <p> <code>INACTIVE</code> when recording is turned off for all resources governed by the framework.</p> </li> <li> <p> <code>UNAVAILABLE</code> when Backup is unable to validate recording status at this time.</p> </li> </ul>"}, "IdempotencyToken": {"shape": "string", "documentation": "<p>A customer-chosen string that you can use to distinguish between otherwise identical calls to <code>DescribeFrameworkOutput</code>. Retrying a successful request with the same idempotency token results in a success message with no action taken.</p>"}}}, "DescribeGlobalSettingsInput": {"type": "structure", "members": {}}, "DescribeGlobalSettingsOutput": {"type": "structure", "members": {"GlobalSettings": {"shape": "GlobalSettings", "documentation": "<p>The status of the flag <code>isCrossAccountBackupEnabled</code>.</p>"}, "LastUpdateTime": {"shape": "timestamp", "documentation": "<p>The date and time that the flag <code>isCrossAccountBackupEnabled</code> was last updated. This update is in Unix format and Coordinated Universal Time (UTC). The value of <code>LastUpdateTime</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}}}, "DescribeProtectedResourceInput": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "ARN", "documentation": "<p>An Amazon Resource Name (ARN) that uniquely identifies a resource. The format of the ARN depends on the resource type.</p>", "location": "uri", "locationName": "resourceArn"}}}, "DescribeProtectedResourceOutput": {"type": "structure", "members": {"ResourceArn": {"shape": "ARN", "documentation": "<p>An ARN that uniquely identifies a resource. The format of the ARN depends on the resource type.</p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>The type of Amazon Web Services resource saved as a recovery point; for example, an Amazon EBS volume or an Amazon RDS database.</p>"}, "LastBackupTime": {"shape": "timestamp", "documentation": "<p>The date and time that a resource was last backed up, in Unix format and Coordinated Universal Time (UTC). The value of <code>LastBackupTime</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "ResourceName": {"shape": "string", "documentation": "<p>The name of the resource that belongs to the specified backup.</p>"}, "LastBackupVaultArn": {"shape": "ARN", "documentation": "<p>The ARN (Amazon Resource Name) of the backup vault that contains the most recent backup recovery point.</p>"}, "LastRecoveryPointArn": {"shape": "ARN", "documentation": "<p>The ARN (Amazon Resource Name) of the most recent recovery point.</p>"}, "LatestRestoreExecutionTimeMinutes": {"shape": "<PERSON>", "documentation": "<p>The time, in minutes, that the most recent restore job took to complete.</p>"}, "LatestRestoreJobCreationDate": {"shape": "timestamp", "documentation": "<p>The creation date of the most recent restore job.</p>"}, "LatestRestoreRecoveryPointCreationDate": {"shape": "timestamp", "documentation": "<p>The date the most recent recovery point was created.</p>"}}}, "DescribeRecoveryPointInput": {"type": "structure", "required": ["BackupVaultName", "RecoveryPointArn"], "members": {"BackupVaultName": {"shape": "BackupVaultName", "documentation": "<p>The name of a logical container where backups are stored. Backup vaults are identified by names that are unique to the account used to create them and the Amazon Web Services Region where they are created.</p>", "location": "uri", "locationName": "backup<PERSON><PERSON><PERSON><PERSON>"}, "RecoveryPointArn": {"shape": "ARN", "documentation": "<p>An Amazon Resource Name (ARN) that uniquely identifies a recovery point; for example, <code>arn:aws:backup:us-east-1:************:recovery-point:1EB3B5E7-9EB0-435A-A80B-108B488B0D45</code>.</p>", "location": "uri", "locationName": "recoveryPointArn"}, "BackupVaultAccountId": {"shape": "AccountId", "documentation": "<p>The account ID of the specified backup vault.</p>", "location": "querystring", "locationName": "backupVaultAccountId"}}}, "DescribeRecoveryPointOutput": {"type": "structure", "members": {"RecoveryPointArn": {"shape": "ARN", "documentation": "<p>An ARN that uniquely identifies a recovery point; for example, <code>arn:aws:backup:us-east-1:************:recovery-point:1EB3B5E7-9EB0-435A-A80B-108B488B0D45</code>.</p>"}, "BackupVaultName": {"shape": "BackupVaultName", "documentation": "<p>The name of a logical container where backups are stored. Backup vaults are identified by names that are unique to the account used to create them and the Region where they are created.</p>"}, "BackupVaultArn": {"shape": "ARN", "documentation": "<p>An ARN that uniquely identifies a backup vault; for example, <code>arn:aws:backup:us-east-1:************:backup-vault:aBackupVault</code>.</p>"}, "SourceBackupVaultArn": {"shape": "ARN", "documentation": "<p>An Amazon Resource Name (ARN) that uniquely identifies the source vault where the resource was originally backed up in; for example, <code>arn:aws:backup:us-east-1:************:backup-vault:aBackupVault</code>. If the recovery is restored to the same Amazon Web Services account or Region, this value will be <code>null</code>.</p>"}, "ResourceArn": {"shape": "ARN", "documentation": "<p>An ARN that uniquely identifies a saved resource. The format of the ARN depends on the resource type.</p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>The type of Amazon Web Services resource to save as a recovery point; for example, an Amazon Elastic Block Store (Amazon EBS) volume or an Amazon Relational Database Service (Amazon RDS) database.</p>"}, "CreatedBy": {"shape": "RecoveryPointCreator", "documentation": "<p>Contains identifying information about the creation of a recovery point, including the <code>BackupPlanArn</code>, <code>BackupPlanId</code>, <code>BackupPlanVersion</code>, and <code>BackupRuleId</code> of the backup plan used to create it.</p>"}, "IamRoleArn": {"shape": "IAMRoleArn", "documentation": "<p>Specifies the IAM role ARN used to create the target recovery point; for example, <code>arn:aws:iam::************:role/S3Access</code>.</p>"}, "Status": {"shape": "RecoveryPointStatus", "documentation": "<p>A status code specifying the state of the recovery point. For more information, see <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/applicationstackbackups.html#cfnrecoverypointstatus\"> Recovery point status</a> in the <i>Backup Developer Guide</i>.</p> <ul> <li> <p> <code>CREATING</code> status indicates that an Backup job has been initiated for a resource. The backup process has started and is actively processing a backup job for the associated recovery point.</p> </li> <li> <p> <code>AVAILABLE</code> status indicates that the backup was successfully created for the recovery point. The backup process has completed without any issues, and the recovery point is now ready for use.</p> </li> <li> <p> <code>PARTIAL</code> status indicates a composite recovery point has one or more nested recovery points that were not in the backup.</p> </li> <li> <p> <code>EXPIRED</code> status indicates that the recovery point has exceeded its retention period, but <PERSON><PERSON> lacks permission or is otherwise unable to delete it. To manually delete these recovery points, see <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/gs-cleanup-resources.html#cleanup-backups\"> Step 3: Delete the recovery points</a> in the <i>Clean up resources</i> section of <i>Getting started</i>.</p> </li> <li> <p> <code>STOPPED</code> status occurs on a continuous backup where a user has taken some action that causes the continuous backup to be disabled. This can be caused by the removal of permissions, turning off versioning, turning off events being sent to EventBridge, or disabling the EventBridge rules that are put in place by Backup. For recovery points of Amazon S3, Amazon RDS, and Amazon Aurora resources, this status occurs when the retention period of a continuous backup rule is changed.</p> <p>To resolve <code>STOPPED</code> status, ensure that all requested permissions are in place and that versioning is enabled on the S3 bucket. Once these conditions are met, the next instance of a backup rule running will result in a new continuous recovery point being created. The recovery points with STOPPED status do not need to be deleted.</p> <p>For SAP HANA on Amazon EC2 <code>STOPPED</code> status occurs due to user action, application misconfiguration, or backup failure. To ensure that future continuous backups succeed, refer to the recovery point status and check SAP HANA for details.</p> </li> </ul>"}, "StatusMessage": {"shape": "string", "documentation": "<p>A status message explaining the status of the recovery point.</p>"}, "CreationDate": {"shape": "timestamp", "documentation": "<p>The date and time that a recovery point is created, in Unix format and Coordinated Universal Time (UTC). The value of <code>CreationDate</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "InitiationDate": {"shape": "timestamp", "documentation": "<p>The date and time when the backup job that created this recovery point was initiated, in Unix format and Coordinated Universal Time (UTC).</p>"}, "CompletionDate": {"shape": "timestamp", "documentation": "<p>The date and time that a job to create a recovery point is completed, in Unix format and Coordinated Universal Time (UTC). The value of <code>CompletionDate</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "BackupSizeInBytes": {"shape": "<PERSON>", "documentation": "<p>The size, in bytes, of a backup.</p>"}, "CalculatedLifecycle": {"shape": "CalculatedLifecycle", "documentation": "<p>A <code>CalculatedLifecycle</code> object containing <code>DeleteAt</code> and <code>MoveToColdStorageAt</code> timestamps.</p>"}, "Lifecycle": {"shape": "Lifecycle", "documentation": "<p>The lifecycle defines when a protected resource is transitioned to cold storage and when it expires. Backup transitions and expires backups automatically according to the lifecycle that you define.</p> <p>Backups that are transitioned to cold storage must be stored in cold storage for a minimum of 90 days. Therefore, the “retention” setting must be 90 days greater than the “transition to cold after days” setting. The “transition to cold after days” setting cannot be changed after a backup has been transitioned to cold. </p> <p>Resource types that can transition to cold storage are listed in the <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/backup-feature-availability.html#features-by-resource\">Feature availability by resource</a> table. Backup ignores this expression for other resource types.</p>"}, "EncryptionKeyArn": {"shape": "ARN", "documentation": "<p>The server-side encryption key used to protect your backups; for example, <code>arn:aws:kms:us-west-2:************:key/1234abcd-12ab-34cd-56ef-1234567890ab</code>.</p>"}, "IsEncrypted": {"shape": "boolean", "documentation": "<p>A Boolean value that is returned as <code>TRUE</code> if the specified recovery point is encrypted, or <code>FALSE</code> if the recovery point is not encrypted.</p>"}, "StorageClass": {"shape": "StorageClass", "documentation": "<p>Specifies the storage class of the recovery point. Valid values are <code>WARM</code> or <code>COLD</code>.</p>"}, "LastRestoreTime": {"shape": "timestamp", "documentation": "<p>The date and time that a recovery point was last restored, in Unix format and Coordinated Universal Time (UTC). The value of <code>LastRestoreTime</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "ParentRecoveryPointArn": {"shape": "ARN", "documentation": "<p>This is an ARN that uniquely identifies a parent (composite) recovery point; for example, <code>arn:aws:backup:us-east-1:************:recovery-point:1EB3B5E7-9EB0-435A-A80B-108B488B0D45</code>.</p>"}, "CompositeMemberIdentifier": {"shape": "string", "documentation": "<p>The identifier of a resource within a composite group, such as nested (child) recovery point belonging to a composite (parent) stack. The ID is transferred from the <a href=\"https://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/resources-section-structure.html#resources-section-structure-syntax\"> logical ID</a> within a stack.</p>"}, "IsParent": {"shape": "boolean", "documentation": "<p>This returns the boolean value that a recovery point is a parent (composite) job.</p>"}, "ResourceName": {"shape": "string", "documentation": "<p>The name of the resource that belongs to the specified backup.</p>"}, "VaultType": {"shape": "VaultType", "documentation": "<p>The type of vault in which the described recovery point is stored.</p>"}, "IndexStatus": {"shape": "IndexStatus", "documentation": "<p>This is the current status for the backup index associated with the specified recovery point.</p> <p>Statuses are: <code>PENDING</code> | <code>ACTIVE</code> | <code>FAILED</code> | <code>DELETING</code> </p> <p>A recovery point with an index that has the status of <code>ACTIVE</code> can be included in a search.</p>"}, "IndexStatusMessage": {"shape": "string", "documentation": "<p>A string in the form of a detailed message explaining the status of a backup index associated with the recovery point.</p>"}}}, "DescribeRegionSettingsInput": {"type": "structure", "members": {}}, "DescribeRegionSettingsOutput": {"type": "structure", "members": {"ResourceTypeOptInPreference": {"shape": "ResourceTypeOptInPreference", "documentation": "<p>The services along with the opt-in preferences in the Region.</p>"}, "ResourceTypeManagementPreference": {"shape": "ResourceTypeManagementPreference", "documentation": "<p>Returns whether Backup fully manages the backups for a resource type.</p> <p>For the benefits of full Backup management, see <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/whatisbackup.html#full-management\">Full Backup management</a>.</p> <p>For a list of resource types and whether each supports full Backup management, see the <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/backup-feature-availability.html#features-by-resource\">Feature availability by resource</a> table.</p> <p>If <code>\"DynamoDB\":false</code>, you can enable full Backup management for DynamoDB backup by enabling <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/advanced-ddb-backup.html#advanced-ddb-backup-enable-cli\"> Backup's advanced DynamoDB backup features</a>.</p>"}}}, "DescribeReportJobInput": {"type": "structure", "required": ["ReportJobId"], "members": {"ReportJobId": {"shape": "ReportJobId", "documentation": "<p>The identifier of the report job. A unique, randomly generated, Unicode, UTF-8 encoded string that is at most 1,024 bytes long. The report job ID cannot be edited.</p>", "location": "uri", "locationName": "reportJobId"}}}, "DescribeReportJobOutput": {"type": "structure", "members": {"ReportJob": {"shape": "ReportJob", "documentation": "<p>The information about a report job, including its completion and creation times, report destination, unique report job ID, Amazon Resource Name (ARN), report template, status, and status message.</p>"}}}, "DescribeReportPlanInput": {"type": "structure", "required": ["ReportPlanName"], "members": {"ReportPlanName": {"shape": "ReportPlanName", "documentation": "<p>The unique name of a report plan.</p>", "location": "uri", "locationName": "reportPlanName"}}}, "DescribeReportPlanOutput": {"type": "structure", "members": {"ReportPlan": {"shape": "ReportPlan", "documentation": "<p>Returns details about the report plan that is specified by its name. These details include the report plan's Amazon Resource Name (ARN), description, settings, delivery channel, deployment status, creation time, and last attempted and successful run times.</p>"}}}, "DescribeRestoreJobInput": {"type": "structure", "required": ["RestoreJobId"], "members": {"RestoreJobId": {"shape": "RestoreJobId", "documentation": "<p>Uniquely identifies the job that restores a recovery point.</p>", "location": "uri", "locationName": "restoreJobId"}}}, "DescribeRestoreJobOutput": {"type": "structure", "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>Returns the account ID that owns the restore job.</p>"}, "RestoreJobId": {"shape": "string", "documentation": "<p>Uniquely identifies the job that restores a recovery point.</p>"}, "RecoveryPointArn": {"shape": "ARN", "documentation": "<p>An ARN that uniquely identifies a recovery point; for example, <code>arn:aws:backup:us-east-1:************:recovery-point:1EB3B5E7-9EB0-435A-A80B-108B488B0D45</code>.</p>"}, "CreationDate": {"shape": "timestamp", "documentation": "<p>The date and time that a restore job is created, in Unix format and Coordinated Universal Time (UTC). The value of <code>CreationDate</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "CompletionDate": {"shape": "timestamp", "documentation": "<p>The date and time that a job to restore a recovery point is completed, in Unix format and Coordinated Universal Time (UTC). The value of <code>CompletionDate</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "Status": {"shape": "RestoreJobStatus", "documentation": "<p>Status code specifying the state of the job that is initiated by Backup to restore a recovery point.</p>"}, "StatusMessage": {"shape": "string", "documentation": "<p>A message showing the status of a job to restore a recovery point.</p>"}, "PercentDone": {"shape": "string", "documentation": "<p>Contains an estimated percentage that is complete of a job at the time the job status was queried.</p>"}, "BackupSizeInBytes": {"shape": "<PERSON>", "documentation": "<p>The size, in bytes, of the restored resource.</p>"}, "IamRoleArn": {"shape": "IAMRoleArn", "documentation": "<p>Specifies the IAM role ARN used to create the target recovery point; for example, <code>arn:aws:iam::************:role/S3Access</code>.</p>"}, "ExpectedCompletionTimeMinutes": {"shape": "<PERSON>", "documentation": "<p>The amount of time in minutes that a job restoring a recovery point is expected to take.</p>"}, "CreatedResourceArn": {"shape": "ARN", "documentation": "<p>The Amazon Resource Name (ARN) of the resource that was created by the restore job.</p> <p>The format of the ARN depends on the resource type of the backed-up resource.</p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>Returns metadata associated with a restore job listed by resource type.</p>"}, "RecoveryPointCreationDate": {"shape": "timestamp", "documentation": "<p>The creation date of the recovery point made by the specifed restore job.</p>"}, "CreatedBy": {"shape": "RestoreJobCreator", "documentation": "<p>Contains identifying information about the creation of a restore job.</p>"}, "ValidationStatus": {"shape": "RestoreValidationStatus", "documentation": "<p>The status of validation run on the indicated restore job.</p>"}, "ValidationStatusMessage": {"shape": "string", "documentation": "<p>The status message.</p>"}, "DeletionStatus": {"shape": "RestoreDeletionStatus", "documentation": "<p>The status of the data generated by the restore test.</p>"}, "DeletionStatusMessage": {"shape": "string", "documentation": "<p>This describes the restore job deletion status.</p>"}}}, "DisassociateBackupVaultMpaApprovalTeamInput": {"type": "structure", "required": ["BackupVaultName"], "members": {"BackupVaultName": {"shape": "BackupVaultName", "documentation": "<p>The name of the backup vault from which to disassociate the MPA approval team.</p>", "location": "uri", "locationName": "backup<PERSON><PERSON><PERSON><PERSON>"}, "RequesterComment": {"shape": "RequesterComment", "documentation": "<p>An optional comment explaining the reason for disassociating the MPA approval team from the backup vault.</p>"}}}, "DisassociateRecoveryPointFromParentInput": {"type": "structure", "required": ["BackupVaultName", "RecoveryPointArn"], "members": {"BackupVaultName": {"shape": "BackupVaultName", "documentation": "<p>The name of a logical container where the child (nested) recovery point is stored. Backup vaults are identified by names that are unique to the account used to create them and the Amazon Web Services Region where they are created.</p>", "location": "uri", "locationName": "backup<PERSON><PERSON><PERSON><PERSON>"}, "RecoveryPointArn": {"shape": "ARN", "documentation": "<p>The Amazon Resource Name (ARN) that uniquely identifies the child (nested) recovery point; for example, <code>arn:aws:backup:us-east-1:************:recovery-point:1EB3B5E7-9EB0-435A-A80B-108B488B0D45.</code> </p>", "location": "uri", "locationName": "recoveryPointArn"}}}, "DisassociateRecoveryPointInput": {"type": "structure", "required": ["BackupVaultName", "RecoveryPointArn"], "members": {"BackupVaultName": {"shape": "BackupVaultName", "documentation": "<p>The unique name of an Backup vault.</p>", "location": "uri", "locationName": "backup<PERSON><PERSON><PERSON><PERSON>"}, "RecoveryPointArn": {"shape": "ARN", "documentation": "<p>An Amazon Resource Name (ARN) that uniquely identifies an Backup recovery point.</p>", "location": "uri", "locationName": "recoveryPointArn"}}}, "ExportBackupPlanTemplateInput": {"type": "structure", "required": ["BackupPlanId"], "members": {"BackupPlanId": {"shape": "string", "documentation": "<p>Uniquely identifies a backup plan.</p>", "location": "uri", "locationName": "backupPlanId"}}}, "ExportBackupPlanTemplateOutput": {"type": "structure", "members": {"BackupPlanTemplateJson": {"shape": "string", "documentation": "<p>The body of a backup plan template in JSON format.</p> <note> <p>This is a signed JSON document that cannot be modified before being passed to <code>GetBackupPlanFromJSON.</code> </p> </note>"}}}, "FormatList": {"type": "list", "member": {"shape": "string"}}, "Framework": {"type": "structure", "members": {"FrameworkName": {"shape": "FrameworkName", "documentation": "<p>The unique name of a framework. This name is between 1 and 256 characters, starting with a letter, and consisting of letters (a-z, A-Z), numbers (0-9), and underscores (_).</p>"}, "FrameworkArn": {"shape": "ARN", "documentation": "<p>An Amazon Resource Name (ARN) that uniquely identifies a resource. The format of the ARN depends on the resource type.</p>"}, "FrameworkDescription": {"shape": "FrameworkDescription", "documentation": "<p>An optional description of the framework with a maximum 1,024 characters.</p>"}, "NumberOfControls": {"shape": "integer", "documentation": "<p>The number of controls contained by the framework.</p>"}, "CreationTime": {"shape": "timestamp", "documentation": "<p>The date and time that a framework is created, in ISO 8601 representation. The value of <code>CreationTime</code> is accurate to milliseconds. For example, 2020-07-10T15:00:00.000-08:00 represents the 10th of July 2020 at 3:00 PM 8 hours behind UTC.</p>"}, "DeploymentStatus": {"shape": "string", "documentation": "<p>The deployment status of a framework. The statuses are:</p> <p> <code>CREATE_IN_PROGRESS | UPDATE_IN_PROGRESS | DELETE_IN_PROGRESS | COMPLETED | FAILED</code> </p>"}}, "documentation": "<p>Contains detailed information about a framework. Frameworks contain controls, which evaluate and report on your backup events and resources. Frameworks generate daily compliance results.</p>"}, "FrameworkControl": {"type": "structure", "required": ["ControlName"], "members": {"ControlName": {"shape": "ControlName", "documentation": "<p>The name of a control. This name is between 1 and 256 characters.</p>"}, "ControlInputParameters": {"shape": "ControlInputParameters", "documentation": "<p>The name/value pairs.</p>"}, "ControlScope": {"shape": "ControlScope", "documentation": "<p>The scope of a control. The control scope defines what the control will evaluate. Three examples of control scopes are: a specific backup plan, all backup plans with a specific tag, or all backup plans.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/API_ControlScope.html\"> <code>ControlScope</code>.</a> </p>"}}, "documentation": "<p>Contains detailed information about all of the controls of a framework. Each framework must contain at least one control.</p>"}, "FrameworkControls": {"type": "list", "member": {"shape": "FrameworkControl"}}, "FrameworkDescription": {"type": "string", "max": 1024, "min": 0, "pattern": ".*\\S.*"}, "FrameworkList": {"type": "list", "member": {"shape": "Framework"}}, "FrameworkName": {"type": "string", "max": 256, "min": 1, "pattern": "[a-zA-Z][_a-zA-Z0-9]*"}, "GetBackupPlanFromJSONInput": {"type": "structure", "required": ["BackupPlanTemplateJson"], "members": {"BackupPlanTemplateJson": {"shape": "string", "documentation": "<p>A customer-supplied backup plan document in JSON format.</p>"}}}, "GetBackupPlanFromJSONOutput": {"type": "structure", "members": {"BackupPlan": {"shape": "BackupPlan", "documentation": "<p>Specifies the body of a backup plan. Includes a <code>BackupPlanName</code> and one or more sets of <code>Rules</code>.</p>"}}}, "GetBackupPlanFromTemplateInput": {"type": "structure", "required": ["BackupPlanTemplateId"], "members": {"BackupPlanTemplateId": {"shape": "string", "documentation": "<p>Uniquely identifies a stored backup plan template.</p>", "location": "uri", "locationName": "templateId"}}}, "GetBackupPlanFromTemplateOutput": {"type": "structure", "members": {"BackupPlanDocument": {"shape": "BackupPlan", "documentation": "<p>Returns the body of a backup plan based on the target template, including the name, rules, and backup vault of the plan.</p>"}}}, "GetBackupPlanInput": {"type": "structure", "required": ["BackupPlanId"], "members": {"BackupPlanId": {"shape": "string", "documentation": "<p>Uniquely identifies a backup plan.</p>", "location": "uri", "locationName": "backupPlanId"}, "VersionId": {"shape": "string", "documentation": "<p>Unique, randomly generated, Unicode, UTF-8 encoded strings that are at most 1,024 bytes long. Version IDs cannot be edited.</p>", "location": "querystring", "locationName": "versionId"}}}, "GetBackupPlanOutput": {"type": "structure", "members": {"BackupPlan": {"shape": "BackupPlan", "documentation": "<p>Specifies the body of a backup plan. Includes a <code>BackupPlanName</code> and one or more sets of <code>Rules</code>.</p>"}, "BackupPlanId": {"shape": "string", "documentation": "<p>Uniquely identifies a backup plan.</p>"}, "BackupPlanArn": {"shape": "ARN", "documentation": "<p>An Amazon Resource Name (ARN) that uniquely identifies a backup plan; for example, <code>arn:aws:backup:us-east-1:************:plan:8F81F553-3A74-4A3F-B93D-B3360DC80C50</code>.</p>"}, "VersionId": {"shape": "string", "documentation": "<p>Unique, randomly generated, Unicode, UTF-8 encoded strings that are at most 1,024 bytes long. Version IDs cannot be edited.</p>"}, "CreatorRequestId": {"shape": "string", "documentation": "<p>A unique string that identifies the request and allows failed requests to be retried without the risk of running the operation twice. </p>"}, "CreationDate": {"shape": "timestamp", "documentation": "<p>The date and time that a backup plan is created, in Unix format and Coordinated Universal Time (UTC). The value of <code>CreationDate</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "DeletionDate": {"shape": "timestamp", "documentation": "<p>The date and time that a backup plan is deleted, in Unix format and Coordinated Universal Time (UTC). The value of <code>DeletionDate</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "LastExecutionDate": {"shape": "timestamp", "documentation": "<p>The last time this backup plan was run. A date and time, in Unix format and Coordinated Universal Time (UTC). The value of <code>LastExecutionDate</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "AdvancedBackupSettings": {"shape": "AdvancedBackupSettings", "documentation": "<p>Contains a list of <code>BackupOptions</code> for each resource type. The list is populated only if the advanced option is set for the backup plan.</p>"}}}, "GetBackupSelectionInput": {"type": "structure", "required": ["BackupPlanId", "SelectionId"], "members": {"BackupPlanId": {"shape": "string", "documentation": "<p>Uniquely identifies a backup plan.</p>", "location": "uri", "locationName": "backupPlanId"}, "SelectionId": {"shape": "string", "documentation": "<p>Uniquely identifies the body of a request to assign a set of resources to a backup plan.</p>", "location": "uri", "locationName": "selectionId"}}}, "GetBackupSelectionOutput": {"type": "structure", "members": {"BackupSelection": {"shape": "BackupSelection", "documentation": "<p>Specifies the body of a request to assign a set of resources to a backup plan.</p>"}, "SelectionId": {"shape": "string", "documentation": "<p>Uniquely identifies the body of a request to assign a set of resources to a backup plan.</p>"}, "BackupPlanId": {"shape": "string", "documentation": "<p>Uniquely identifies a backup plan.</p>"}, "CreationDate": {"shape": "timestamp", "documentation": "<p>The date and time a backup selection is created, in Unix format and Coordinated Universal Time (UTC). The value of <code>CreationDate</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "CreatorRequestId": {"shape": "string", "documentation": "<p>A unique string that identifies the request and allows failed requests to be retried without the risk of running the operation twice.</p>"}}}, "GetBackupVaultAccessPolicyInput": {"type": "structure", "required": ["BackupVaultName"], "members": {"BackupVaultName": {"shape": "BackupVaultName", "documentation": "<p>The name of a logical container where backups are stored. Backup vaults are identified by names that are unique to the account used to create them and the Amazon Web Services Region where they are created.</p>", "location": "uri", "locationName": "backup<PERSON><PERSON><PERSON><PERSON>"}}}, "GetBackupVaultAccessPolicyOutput": {"type": "structure", "members": {"BackupVaultName": {"shape": "BackupVaultName", "documentation": "<p>The name of a logical container where backups are stored. Backup vaults are identified by names that are unique to the account used to create them and the Region where they are created.</p>"}, "BackupVaultArn": {"shape": "ARN", "documentation": "<p>An Amazon Resource Name (ARN) that uniquely identifies a backup vault; for example, <code>arn:aws:backup:us-east-1:************:backup-vault:aBackupVault</code>.</p>"}, "Policy": {"shape": "IAMPolicy", "documentation": "<p>The backup vault access policy document in JSON format.</p>"}}}, "GetBackupVaultNotificationsInput": {"type": "structure", "required": ["BackupVaultName"], "members": {"BackupVaultName": {"shape": "BackupVaultName", "documentation": "<p>The name of a logical container where backups are stored. Backup vaults are identified by names that are unique to the account used to create them and the Amazon Web Services Region where they are created.</p>", "location": "uri", "locationName": "backup<PERSON><PERSON><PERSON><PERSON>"}}}, "GetBackupVaultNotificationsOutput": {"type": "structure", "members": {"BackupVaultName": {"shape": "BackupVaultName", "documentation": "<p>The name of a logical container where backups are stored. Backup vaults are identified by names that are unique to the account used to create them and the Region where they are created.</p>"}, "BackupVaultArn": {"shape": "ARN", "documentation": "<p>An Amazon Resource Name (ARN) that uniquely identifies a backup vault; for example, <code>arn:aws:backup:us-east-1:************:backup-vault:aBackupVault</code>.</p>"}, "SNSTopicArn": {"shape": "ARN", "documentation": "<p>An ARN that uniquely identifies an Amazon Simple Notification Service (Amazon SNS) topic; for example, <code>arn:aws:sns:us-west-2:************:MyTopic</code>.</p>"}, "BackupVaultEvents": {"shape": "BackupVaultEvents", "documentation": "<p>An array of events that indicate the status of jobs to back up resources to the backup vault.</p>"}}}, "GetLegalHoldInput": {"type": "structure", "required": ["LegalHoldId"], "members": {"LegalHoldId": {"shape": "string", "documentation": "<p>The ID of the legal hold.</p>", "location": "uri", "locationName": "legalHoldId"}}}, "GetLegalHoldOutput": {"type": "structure", "members": {"Title": {"shape": "string", "documentation": "<p>The title of the legal hold.</p>"}, "Status": {"shape": "LegalHoldStatus", "documentation": "<p>The status of the legal hold.</p>"}, "Description": {"shape": "string", "documentation": "<p>The description of the legal hold.</p>"}, "CancelDescription": {"shape": "string", "documentation": "<p>The reason for removing the legal hold.</p>"}, "LegalHoldId": {"shape": "string", "documentation": "<p>The ID of the legal hold.</p>"}, "LegalHoldArn": {"shape": "ARN", "documentation": "<p>The framework ARN for the specified legal hold. The format of the ARN depends on the resource type.</p>"}, "CreationDate": {"shape": "timestamp", "documentation": "<p>The time when the legal hold was created.</p>"}, "CancellationDate": {"shape": "timestamp", "documentation": "<p>The time when the legal hold was cancelled.</p>"}, "RetainRecordUntil": {"shape": "timestamp", "documentation": "<p>The date and time until which the legal hold record is retained.</p>"}, "RecoveryPointSelection": {"shape": "RecoveryPointSelection", "documentation": "<p>The criteria to assign a set of resources, such as resource types or backup vaults.</p>"}}}, "GetRecoveryPointIndexDetailsInput": {"type": "structure", "required": ["BackupVaultName", "RecoveryPointArn"], "members": {"BackupVaultName": {"shape": "BackupVaultName", "documentation": "<p>The name of a logical container where backups are stored. Backup vaults are identified by names that are unique to the account used to create them and the Region where they are created.</p> <p>Accepted characters include lowercase letters, numbers, and hyphens.</p>", "location": "uri", "locationName": "backup<PERSON><PERSON><PERSON><PERSON>"}, "RecoveryPointArn": {"shape": "ARN", "documentation": "<p>An ARN that uniquely identifies a recovery point; for example, <code>arn:aws:backup:us-east-1:************:recovery-point:1EB3B5E7-9EB0-435A-A80B-108B488B0D45</code>.</p>", "location": "uri", "locationName": "recoveryPointArn"}}}, "GetRecoveryPointIndexDetailsOutput": {"type": "structure", "members": {"RecoveryPointArn": {"shape": "ARN", "documentation": "<p>An ARN that uniquely identifies a recovery point; for example, <code>arn:aws:backup:us-east-1:************:recovery-point:1EB3B5E7-9EB0-435A-A80B-108B488B0D45</code>.</p>"}, "BackupVaultArn": {"shape": "ARN", "documentation": "<p>An ARN that uniquely identifies the backup vault where the recovery point index is stored.</p> <p>For example, <code>arn:aws:backup:us-east-1:************:backup-vault:aBackupVault</code>.</p>"}, "SourceResourceArn": {"shape": "ARN", "documentation": "<p>A string of the Amazon Resource Name (ARN) that uniquely identifies the source resource.</p>"}, "IndexCreationDate": {"shape": "timestamp", "documentation": "<p>The date and time that a backup index was created, in Unix format and Coordinated Universal Time (UTC). The value of <code>CreationDate</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "IndexDeletionDate": {"shape": "timestamp", "documentation": "<p>The date and time that a backup index was deleted, in Unix format and Coordinated Universal Time (UTC). The value of <code>CreationDate</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "IndexCompletionDate": {"shape": "timestamp", "documentation": "<p>The date and time that a backup index finished creation, in Unix format and Coordinated Universal Time (UTC). The value of <code>CreationDate</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "IndexStatus": {"shape": "IndexStatus", "documentation": "<p>This is the current status for the backup index associated with the specified recovery point.</p> <p>Statuses are: <code>PENDING</code> | <code>ACTIVE</code> | <code>FAILED</code> | <code>DELETING</code> </p> <p>A recovery point with an index that has the status of <code>ACTIVE</code> can be included in a search.</p>"}, "IndexStatusMessage": {"shape": "string", "documentation": "<p>A detailed message explaining the status of a backup index associated with the recovery point.</p>"}, "TotalItemsIndexed": {"shape": "<PERSON>", "documentation": "<p>Count of items within the backup index associated with the recovery point.</p>"}}}, "GetRecoveryPointRestoreMetadataInput": {"type": "structure", "required": ["BackupVaultName", "RecoveryPointArn"], "members": {"BackupVaultName": {"shape": "BackupVaultName", "documentation": "<p>The name of a logical container where backups are stored. Backup vaults are identified by names that are unique to the account used to create them and the Amazon Web Services Region where they are created.</p>", "location": "uri", "locationName": "backup<PERSON><PERSON><PERSON><PERSON>"}, "RecoveryPointArn": {"shape": "ARN", "documentation": "<p>An Amazon Resource Name (ARN) that uniquely identifies a recovery point; for example, <code>arn:aws:backup:us-east-1:************:recovery-point:1EB3B5E7-9EB0-435A-A80B-108B488B0D45</code>.</p>", "location": "uri", "locationName": "recoveryPointArn"}, "BackupVaultAccountId": {"shape": "AccountId", "documentation": "<p>The account ID of the specified backup vault.</p>", "location": "querystring", "locationName": "backupVaultAccountId"}}}, "GetRecoveryPointRestoreMetadataOutput": {"type": "structure", "members": {"BackupVaultArn": {"shape": "ARN", "documentation": "<p>An ARN that uniquely identifies a backup vault; for example, <code>arn:aws:backup:us-east-1:************:backup-vault:aBackupVault</code>.</p>"}, "RecoveryPointArn": {"shape": "ARN", "documentation": "<p>An ARN that uniquely identifies a recovery point; for example, <code>arn:aws:backup:us-east-1:************:recovery-point:1EB3B5E7-9EB0-435A-A80B-108B488B0D45</code>.</p>"}, "RestoreMetadata": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>The set of metadata key-value pairs that describe the original configuration of the backed-up resource. These values vary depending on the service that is being restored.</p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>The resource type of the recovery point.</p>"}}}, "GetRestoreJobMetadataInput": {"type": "structure", "required": ["RestoreJobId"], "members": {"RestoreJobId": {"shape": "RestoreJobId", "documentation": "<p>This is a unique identifier of a restore job within Backup.</p>", "location": "uri", "locationName": "restoreJobId"}}}, "GetRestoreJobMetadataOutput": {"type": "structure", "members": {"RestoreJobId": {"shape": "RestoreJobId", "documentation": "<p>This is a unique identifier of a restore job within Backup.</p>"}, "Metadata": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>This contains the metadata of the specified backup job.</p>"}}}, "GetRestoreTestingInferredMetadataInput": {"type": "structure", "required": ["BackupVaultName", "RecoveryPointArn"], "members": {"BackupVaultAccountId": {"shape": "String", "documentation": "<p>The account ID of the specified backup vault.</p>", "location": "querystring", "locationName": "BackupVaultAccountId"}, "BackupVaultName": {"shape": "String", "documentation": "<p>The name of a logical container where backups are stored. Backup vaults are identified by names that are unique to the account used to create them and the Amazon Web ServicesRegion where they are created. They consist of letters, numbers, and hyphens.</p>", "location": "querystring", "locationName": "BackupVaultName"}, "RecoveryPointArn": {"shape": "String", "documentation": "<p>An Amazon Resource Name (ARN) that uniquely identifies a recovery point; for example, <code>arn:aws:backup:us-east-1:************:recovery-point:1EB3B5E7-9EB0-435A-A80B-108B488B0D45</code>. </p>", "location": "querystring", "locationName": "RecoveryPointArn"}}}, "GetRestoreTestingInferredMetadataOutput": {"type": "structure", "required": ["InferredMetadata"], "members": {"InferredMetadata": {"shape": "stringMap", "documentation": "<p>This is a string map of the metadata inferred from the request.</p>"}}}, "GetRestoreTestingPlanInput": {"type": "structure", "required": ["RestoreTestingPlanName"], "members": {"RestoreTestingPlanName": {"shape": "String", "documentation": "<p>Required unique name of the restore testing plan.</p>", "location": "uri", "locationName": "RestoreTestingPlanName"}}}, "GetRestoreTestingPlanOutput": {"type": "structure", "required": ["RestoreTestingPlan"], "members": {"RestoreTestingPlan": {"shape": "RestoreTestingPlanForGet", "documentation": "<p>Specifies the body of a restore testing plan. Includes <code>RestoreTestingPlanName</code>.</p>"}}}, "GetRestoreTestingSelectionInput": {"type": "structure", "required": ["RestoreTestingPlanName", "RestoreTestingSelectionName"], "members": {"RestoreTestingPlanName": {"shape": "String", "documentation": "<p>Required unique name of the restore testing plan.</p>", "location": "uri", "locationName": "RestoreTestingPlanName"}, "RestoreTestingSelectionName": {"shape": "String", "documentation": "<p>Required unique name of the restore testing selection.</p>", "location": "uri", "locationName": "RestoreTestingSelectionName"}}}, "GetRestoreTestingSelectionOutput": {"type": "structure", "required": ["RestoreTestingSelection"], "members": {"RestoreTestingSelection": {"shape": "RestoreTestingSelectionForGet", "documentation": "<p>Unique name of the restore testing selection.</p>"}}}, "GetSupportedResourceTypesOutput": {"type": "structure", "members": {"ResourceTypes": {"shape": "ResourceTypes", "documentation": "<p>Contains a string with the supported Amazon Web Services resource types:</p> <ul> <li> <p> <code>Aurora</code> for Amazon Aurora</p> </li> <li> <p> <code>CloudFormation</code> for CloudFormation</p> </li> <li> <p> <code>DocumentDB</code> for Amazon DocumentDB (with MongoDB compatibility)</p> </li> <li> <p> <code>DynamoDB</code> for Amazon DynamoDB</p> </li> <li> <p> <code>EBS</code> for Amazon Elastic Block Store</p> </li> <li> <p> <code>EC2</code> for Amazon Elastic Compute Cloud</p> </li> <li> <p> <code>EFS</code> for Amazon Elastic File System</p> </li> <li> <p> <code>FSx</code> for Amazon FSx</p> </li> <li> <p> <code>Neptune</code> for Amazon Neptune</p> </li> <li> <p> <code>RDS</code> for Amazon Relational Database Service</p> </li> <li> <p> <code>Redshift</code> for Amazon Redshift</p> </li> <li> <p> <code>S3</code> for Amazon Simple Storage Service (Amazon S3)</p> </li> <li> <p> <code>SAP HANA on Amazon EC2</code> for SAP HANA databases on Amazon Elastic Compute Cloud instances</p> </li> <li> <p> <code>Storage Gateway</code> for Storage Gateway</p> </li> <li> <p> <code>Timestream</code> for Amazon Timestream</p> </li> <li> <p> <code>VirtualMachine</code> for VMware virtual machines</p> </li> </ul>"}}}, "GlobalSettings": {"type": "map", "key": {"shape": "GlobalSettingsName"}, "value": {"shape": "GlobalSettingsValue"}}, "GlobalSettingsName": {"type": "string"}, "GlobalSettingsValue": {"type": "string"}, "IAMPolicy": {"type": "string"}, "IAMRoleArn": {"type": "string"}, "Index": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "IndexAction": {"type": "structure", "members": {"ResourceTypes": {"shape": "ResourceTypes", "documentation": "<p>0 or 1 index action will be accepted for each BackupRule.</p> <p>Valid values:</p> <ul> <li> <p> <code>EBS</code> for Amazon Elastic Block Store</p> </li> <li> <p> <code>S3</code> for Amazon Simple Storage Service (Amazon S3)</p> </li> </ul>"}}, "documentation": "<p>This is an optional array within a BackupRule.</p> <p>IndexAction consists of one ResourceTypes.</p>"}, "IndexActions": {"type": "list", "member": {"shape": "IndexAction"}}, "IndexStatus": {"type": "string", "enum": ["PENDING", "ACTIVE", "FAILED", "DELETING"]}, "IndexedRecoveryPoint": {"type": "structure", "members": {"RecoveryPointArn": {"shape": "ARN", "documentation": "<p>An ARN that uniquely identifies a recovery point; for example, <code>arn:aws:backup:us-east-1:************:recovery-point:1EB3B5E7-9EB0-435A-A80B-108B488B0D45</code> </p>"}, "SourceResourceArn": {"shape": "ARN", "documentation": "<p>A string of the Amazon Resource Name (ARN) that uniquely identifies the source resource.</p>"}, "IamRoleArn": {"shape": "ARN", "documentation": "<p>This specifies the IAM role ARN used for this operation.</p> <p>For example, arn:aws:iam::************:role/S3Access</p>"}, "BackupCreationDate": {"shape": "timestamp", "documentation": "<p>The date and time that a backup was created, in Unix format and Coordinated Universal Time (UTC). The value of <code>CreationDate</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>The resource type of the indexed recovery point.</p> <ul> <li> <p> <code>EBS</code> for Amazon Elastic Block Store</p> </li> <li> <p> <code>S3</code> for Amazon Simple Storage Service (Amazon S3)</p> </li> </ul>"}, "IndexCreationDate": {"shape": "timestamp", "documentation": "<p>The date and time that a backup index was created, in Unix format and Coordinated Universal Time (UTC). The value of <code>CreationDate</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "IndexStatus": {"shape": "IndexStatus", "documentation": "<p>This is the current status for the backup index associated with the specified recovery point.</p> <p>Statuses are: <code>PENDING</code> | <code>ACTIVE</code> | <code>FAILED</code> | <code>DELETING</code> </p> <p>A recovery point with an index that has the status of <code>ACTIVE</code> can be included in a search.</p>"}, "IndexStatusMessage": {"shape": "string", "documentation": "<p>A string in the form of a detailed message explaining the status of a backup index associated with the recovery point.</p>"}, "BackupVaultArn": {"shape": "ARN", "documentation": "<p>An ARN that uniquely identifies the backup vault where the recovery point index is stored.</p> <p>For example, <code>arn:aws:backup:us-east-1:************:backup-vault:aBackupVault</code>.</p>"}}, "documentation": "<p>This is a recovery point that has an associated backup index.</p> <p>Only recovery points with a backup index can be included in a search.</p>"}, "IndexedRecoveryPointList": {"type": "list", "member": {"shape": "IndexedRecoveryPoint"}}, "InvalidParameterValueException": {"type": "structure", "members": {"Code": {"shape": "string"}, "Message": {"shape": "string"}, "Type": {"shape": "string", "documentation": "<p/>"}, "Context": {"shape": "string", "documentation": "<p/>"}}, "documentation": "<p>Indicates that something is wrong with a parameter's value. For example, the value is out of range.</p>", "exception": true}, "InvalidRequestException": {"type": "structure", "members": {"Code": {"shape": "string"}, "Message": {"shape": "string"}, "Type": {"shape": "string", "documentation": "<p/>"}, "Context": {"shape": "string", "documentation": "<p/>"}}, "documentation": "<p>Indicates that something is wrong with the input to the request. For example, a parameter is of the wrong type.</p>", "exception": true}, "InvalidResourceStateException": {"type": "structure", "members": {"Code": {"shape": "string"}, "Message": {"shape": "string"}, "Type": {"shape": "string", "documentation": "<p/>"}, "Context": {"shape": "string", "documentation": "<p/>"}}, "documentation": "<p>Back<PERSON> is already performing an action on this recovery point. It can't perform the action you requested until the first action finishes. Try again later.</p>", "exception": true}, "IsEnabled": {"type": "boolean"}, "KeyValue": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {"shape": "String", "documentation": "<p>The tag key (String). The key can't start with <code>aws:</code>.</p> <p>Length Constraints: Minimum length of 1. Maximum length of 128.</p> <p>Pattern: <code>^(?![aA]{1}[wW]{1}[sS]{1}:)([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]+)$</code> </p>"}, "Value": {"shape": "String", "documentation": "<p>The value of the key.</p> <p>Length Constraints: Maximum length of 256.</p> <p>Pattern: <code>^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$</code> </p>"}}, "documentation": "<p>Pair of two related strings. Allowed characters are letters, white space, and numbers that can be represented in UTF-8 and the following characters: <code> + - = . _ : /</code> </p>"}, "KeyValueList": {"type": "list", "member": {"shape": "KeyValue"}}, "LatestMpaApprovalTeamUpdate": {"type": "structure", "members": {"MpaSessionArn": {"shape": "ARN", "documentation": "<p>The ARN of the MPA session associated with this update.</p>"}, "Status": {"shape": "MpaSessionStatus", "documentation": "<p>The current status of the MPA approval team update.</p>"}, "StatusMessage": {"shape": "string", "documentation": "<p>A message describing the current status of the MPA approval team update.</p>"}, "InitiationDate": {"shape": "timestamp", "documentation": "<p>The date and time when the MPA approval team update was initiated.</p>"}, "ExpiryDate": {"shape": "timestamp", "documentation": "<p>The date and time when the MPA approval team update will expire.</p>"}}, "documentation": "<p>Contains information about the latest update to an MPA approval team association.</p>"}, "LatestRevokeRequest": {"type": "structure", "members": {"MpaSessionArn": {"shape": "string", "documentation": "<p>The ARN of the MPA session associated with this revoke request.</p>"}, "Status": {"shape": "MpaRevokeSessionStatus", "documentation": "<p>The current status of the revoke request.</p>"}, "StatusMessage": {"shape": "string", "documentation": "<p>A message describing the current status of the revoke request.</p>"}, "InitiationDate": {"shape": "timestamp", "documentation": "<p>The date and time when the revoke request was initiated.</p>"}, "ExpiryDate": {"shape": "timestamp", "documentation": "<p>The date and time when the revoke request will expire.</p>"}}, "documentation": "<p>Contains information about the latest request to revoke access to a backup vault.</p>"}, "LegalHold": {"type": "structure", "members": {"Title": {"shape": "string", "documentation": "<p>The title of a legal hold.</p>"}, "Status": {"shape": "LegalHoldStatus", "documentation": "<p>The status of the legal hold.</p>"}, "Description": {"shape": "string", "documentation": "<p>The description of a legal hold.</p>"}, "LegalHoldId": {"shape": "string", "documentation": "<p>The ID of the legal hold.</p>"}, "LegalHoldArn": {"shape": "ARN", "documentation": "<p>The Amazon Resource Name (ARN) of the legal hold; for example, <code>arn:aws:backup:us-east-1:************:recovery-point:1EB3B5E7-9EB0-435A-A80B-108B488B0D45</code>.</p>"}, "CreationDate": {"shape": "timestamp", "documentation": "<p>The time when the legal hold was created.</p>"}, "CancellationDate": {"shape": "timestamp", "documentation": "<p>The time when the legal hold was cancelled.</p>"}}, "documentation": "<p>A legal hold is an administrative tool that helps prevent backups from being deleted while under a hold. While the hold is in place, backups under a hold cannot be deleted and lifecycle policies that would alter the backup status (such as transition to cold storage) are delayed until the legal hold is removed. A backup can have more than one legal hold. Legal holds are applied to one or more backups (also known as recovery points). These backups can be filtered by resource types and by resource IDs.</p>"}, "LegalHoldStatus": {"type": "string", "enum": ["CREATING", "ACTIVE", "CANCELING", "CANCELED"]}, "LegalHoldsList": {"type": "list", "member": {"shape": "LegalHold"}}, "Lifecycle": {"type": "structure", "members": {"MoveToColdStorageAfterDays": {"shape": "<PERSON>", "documentation": "<p>The number of days after creation that a recovery point is moved to cold storage.</p>"}, "DeleteAfterDays": {"shape": "<PERSON>", "documentation": "<p>The number of days after creation that a recovery point is deleted. This value must be at least 90 days after the number of days specified in <code>MoveToColdStorageAfterDays</code>.</p>"}, "OptInToArchiveForSupportedResources": {"shape": "Boolean", "documentation": "<p>If the value is true, your backup plan transitions supported resources to archive (cold) storage tier in accordance with your lifecycle settings.</p>"}}, "documentation": "<p>Specifies the time period, in days, before a recovery point transitions to cold storage or is deleted.</p> <p>Backups transitioned to cold storage must be stored in cold storage for a minimum of 90 days. Therefore, on the console, the retention setting must be 90 days greater than the transition to cold after days setting. The transition to cold after days setting can't be changed after a backup has been transitioned to cold.</p> <p>Resource types that can transition to cold storage are listed in the <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/backup-feature-availability.html#features-by-resource\">Feature availability by resource</a> table. Backup ignores this expression for other resource types.</p> <p>To remove the existing lifecycle and retention periods and keep your recovery points indefinitely, specify -1 for <code>MoveToColdStorageAfterDays</code> and <code>DeleteAfterDays</code>.</p>"}, "LimitExceededException": {"type": "structure", "members": {"Code": {"shape": "string"}, "Message": {"shape": "string"}, "Type": {"shape": "string", "documentation": "<p/>"}, "Context": {"shape": "string", "documentation": "<p/>"}}, "documentation": "<p>A limit in the request has been exceeded; for example, a maximum number of items allowed in a request.</p>", "exception": true}, "ListBackupJobSummariesInput": {"type": "structure", "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>Returns the job count for the specified account.</p> <p>If the request is sent from a member account or an account not part of Amazon Web Services Organizations, jobs within requestor's account will be returned.</p> <p>Root, admin, and delegated administrator accounts can use the value ANY to return job counts from every account in the organization.</p> <p> <code>AGGREGATE_ALL</code> aggregates job counts from all accounts within the authenticated organization, then returns the sum.</p>", "location": "querystring", "locationName": "AccountId"}, "State": {"shape": "BackupJobStatus", "documentation": "<p>This parameter returns the job count for jobs with the specified state.</p> <p>The the value ANY returns count of all states.</p> <p> <code>AGGREGATE_ALL</code> aggregates job counts for all states and returns the sum.</p> <p> <code>Completed with issues</code> is a status found only in the Backup console. For API, this status refers to jobs with a state of <code>COMPLETED</code> and a <code>MessageCategory</code> with a value other than <code>SUCCESS</code>; that is, the status is completed but comes with a status message. To obtain the job count for <code>Completed with issues</code>, run two GET requests, and subtract the second, smaller number:</p> <p>GET /audit/backup-job-summaries?AggregationPeriod=FOURTEEN_DAYS&amp;State=COMPLETED</p> <p>GET /audit/backup-job-summaries?AggregationPeriod=FOURTEEN_DAYS&amp;MessageCategory=SUCCESS&amp;State=COMPLETED</p>", "location": "querystring", "locationName": "State"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>Returns the job count for the specified resource type. Use request <code>GetSupportedResourceTypes</code> to obtain strings for supported resource types.</p> <p>The the value ANY returns count of all resource types.</p> <p> <code>AGGREGATE_ALL</code> aggregates job counts for all resource types and returns the sum.</p> <p>The type of Amazon Web Services resource to be backed up; for example, an Amazon Elastic Block Store (Amazon EBS) volume or an Amazon Relational Database Service (Amazon RDS) database.</p>", "location": "querystring", "locationName": "ResourceType"}, "MessageCategory": {"shape": "MessageCategory", "documentation": "<p>This parameter returns the job count for the specified message category.</p> <p>Example accepted strings include <code>AccessDenied</code>, <code>Success</code>, and <code>InvalidParameters</code>. See <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/monitoring.html\">Monitoring</a> for a list of accepted MessageCategory strings.</p> <p>The the value ANY returns count of all message categories.</p> <p> <code>AGGREGATE_ALL</code> aggregates job counts for all message categories and returns the sum.</p>", "location": "querystring", "locationName": "MessageCategory"}, "AggregationPeriod": {"shape": "AggregationPeriod", "documentation": "<p>The period for the returned results.</p> <ul> <li> <p> <code>ONE_DAY</code> - The daily job count for the prior 14 days.</p> </li> <li> <p> <code>SEVEN_DAYS</code> - The aggregated job count for the prior 7 days.</p> </li> <li> <p> <code>FOURTEEN_DAYS</code> - The aggregated job count for prior 14 days.</p> </li> </ul>", "location": "querystring", "locationName": "AggregationPeriod"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of items to be returned.</p> <p>The value is an integer. Range of accepted values is from 1 to 500.</p>", "location": "querystring", "locationName": "MaxResults"}, "NextToken": {"shape": "string", "documentation": "<p>The next item following a partial list of returned resources. For example, if a request is made to return <code>MaxResults</code> number of resources, <code>NextToken</code> allows you to return more items in your list starting at the location pointed to by the next token.</p>", "location": "querystring", "locationName": "NextToken"}}}, "ListBackupJobSummariesOutput": {"type": "structure", "members": {"BackupJobSummaries": {"shape": "BackupJobSummaryList", "documentation": "<p>The summary information.</p>"}, "AggregationPeriod": {"shape": "string", "documentation": "<p>The period for the returned results.</p> <ul> <li> <p> <code>ONE_DAY</code> - The daily job count for the prior 14 days.</p> </li> <li> <p> <code>SEVEN_DAYS</code> - The aggregated job count for the prior 7 days.</p> </li> <li> <p> <code>FOURTEEN_DAYS</code> - The aggregated job count for prior 14 days.</p> </li> </ul>"}, "NextToken": {"shape": "string", "documentation": "<p>The next item following a partial list of returned resources. For example, if a request is made to return <code>MaxResults</code> number of resources, <code>NextToken</code> allows you to return more items in your list starting at the location pointed to by the next token.</p>"}}}, "ListBackupJobsInput": {"type": "structure", "members": {"NextToken": {"shape": "string", "documentation": "<p>The next item following a partial list of returned items. For example, if a request is made to return <code>MaxResults</code> number of items, <code>NextToken</code> allows you to return more items in your list starting at the location pointed to by the next token.</p>", "location": "querystring", "locationName": "nextToken"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of items to be returned.</p>", "location": "querystring", "locationName": "maxResults"}, "ByResourceArn": {"shape": "ARN", "documentation": "<p>Returns only backup jobs that match the specified resource Amazon Resource Name (ARN).</p>", "location": "querystring", "locationName": "resourceArn"}, "ByState": {"shape": "BackupJobState", "documentation": "<p>Returns only backup jobs that are in the specified state.</p> <p> <code>Completed with issues</code> is a status found only in the Backup console. For API, this status refers to jobs with a state of <code>COMPLETED</code> and a <code>MessageCategory</code> with a value other than <code>SUCCESS</code>; that is, the status is completed but comes with a status message.</p> <p>To obtain the job count for <code>Completed with issues</code>, run two GET requests, and subtract the second, smaller number:</p> <p>GET /backup-jobs/?state=COMPLETED</p> <p>GET /backup-jobs/?messageCategory=SUCCESS&amp;state=COMPLETED</p>", "location": "querystring", "locationName": "state"}, "ByBackupVaultName": {"shape": "BackupVaultName", "documentation": "<p>Returns only backup jobs that will be stored in the specified backup vault. Backup vaults are identified by names that are unique to the account used to create them and the Amazon Web Services Region where they are created.</p>", "location": "querystring", "locationName": "backup<PERSON><PERSON><PERSON><PERSON>"}, "ByCreatedBefore": {"shape": "timestamp", "documentation": "<p>Returns only backup jobs that were created before the specified date.</p>", "location": "querystring", "locationName": "createdBefore"}, "ByCreatedAfter": {"shape": "timestamp", "documentation": "<p>Returns only backup jobs that were created after the specified date.</p>", "location": "querystring", "locationName": "createdAfter"}, "ByResourceType": {"shape": "ResourceType", "documentation": "<p>Returns only backup jobs for the specified resources:</p> <ul> <li> <p> <code>Aurora</code> for Amazon Aurora</p> </li> <li> <p> <code>CloudFormation</code> for CloudFormation</p> </li> <li> <p> <code>DocumentDB</code> for Amazon DocumentDB (with MongoDB compatibility)</p> </li> <li> <p> <code>DynamoDB</code> for Amazon DynamoDB</p> </li> <li> <p> <code>EBS</code> for Amazon Elastic Block Store</p> </li> <li> <p> <code>EC2</code> for Amazon Elastic Compute Cloud</p> </li> <li> <p> <code>EFS</code> for Amazon Elastic File System</p> </li> <li> <p> <code>FSx</code> for Amazon FSx</p> </li> <li> <p> <code>Neptune</code> for Amazon Neptune</p> </li> <li> <p> <code>RDS</code> for Amazon Relational Database Service</p> </li> <li> <p> <code>Redshift</code> for Amazon Redshift</p> </li> <li> <p> <code>S3</code> for Amazon Simple Storage Service (Amazon S3)</p> </li> <li> <p> <code>SAP HANA on Amazon EC2</code> for SAP HANA databases on Amazon Elastic Compute Cloud instances</p> </li> <li> <p> <code>Storage Gateway</code> for Storage Gateway</p> </li> <li> <p> <code>Timestream</code> for Amazon Timestream</p> </li> <li> <p> <code>VirtualMachine</code> for VMware virtual machines</p> </li> </ul>", "location": "querystring", "locationName": "resourceType"}, "ByAccountId": {"shape": "AccountId", "documentation": "<p>The account ID to list the jobs from. Returns only backup jobs associated with the specified account ID.</p> <p>If used from an Organizations management account, passing <code>*</code> returns all jobs across the organization.</p>", "location": "querystring", "locationName": "accountId"}, "ByCompleteAfter": {"shape": "timestamp", "documentation": "<p>Returns only backup jobs completed after a date expressed in Unix format and Coordinated Universal Time (UTC).</p>", "location": "querystring", "locationName": "completeAfter"}, "ByCompleteBefore": {"shape": "timestamp", "documentation": "<p>Returns only backup jobs completed before a date expressed in Unix format and Coordinated Universal Time (UTC).</p>", "location": "querystring", "locationName": "completeBefore"}, "ByParentJobId": {"shape": "string", "documentation": "<p>This is a filter to list child (nested) jobs based on parent job ID.</p>", "location": "querystring", "locationName": "parentJobId"}, "ByMessageCategory": {"shape": "string", "documentation": "<p>This is an optional parameter that can be used to filter out jobs with a MessageCategory which matches the value you input.</p> <p>Example strings may include <code>AccessDenied</code>, <code>SUCCESS</code>, <code>AGGREGATE_ALL</code>, and <code>InvalidParameters</code>.</p> <p>View <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/monitoring.html\">Monitoring</a> </p> <p>The wildcard () returns count of all message categories.</p> <p> <code>AGGREGATE_ALL</code> aggregates job counts for all message categories and returns the sum.</p>", "location": "querystring", "locationName": "messageCategory"}}}, "ListBackupJobsOutput": {"type": "structure", "members": {"BackupJobs": {"shape": "BackupJobsList", "documentation": "<p>An array of structures containing metadata about your backup jobs returned in JSON format.</p>"}, "NextToken": {"shape": "string", "documentation": "<p>The next item following a partial list of returned items. For example, if a request is made to return <code>MaxResults</code> number of items, <code>NextToken</code> allows you to return more items in your list starting at the location pointed to by the next token.</p>"}}}, "ListBackupPlanTemplatesInput": {"type": "structure", "members": {"NextToken": {"shape": "string", "documentation": "<p>The next item following a partial list of returned items. For example, if a request is made to return <code>MaxResults</code> number of items, <code>NextToken</code> allows you to return more items in your list starting at the location pointed to by the next token.</p>", "location": "querystring", "locationName": "nextToken"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of items to return.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListBackupPlanTemplatesOutput": {"type": "structure", "members": {"NextToken": {"shape": "string", "documentation": "<p>The next item following a partial list of returned items. For example, if a request is made to return <code>MaxResults</code> number of items, <code>NextToken</code> allows you to return more items in your list starting at the location pointed to by the next token.</p>"}, "BackupPlanTemplatesList": {"shape": "BackupPlanTemplatesList", "documentation": "<p>An array of template list items containing metadata about your saved templates.</p>"}}}, "ListBackupPlanVersionsInput": {"type": "structure", "required": ["BackupPlanId"], "members": {"BackupPlanId": {"shape": "string", "documentation": "<p>Uniquely identifies a backup plan.</p>", "location": "uri", "locationName": "backupPlanId"}, "NextToken": {"shape": "string", "documentation": "<p>The next item following a partial list of returned items. For example, if a request is made to return <code>MaxResults</code> number of items, <code>NextToken</code> allows you to return more items in your list starting at the location pointed to by the next token.</p>", "location": "querystring", "locationName": "nextToken"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of items to be returned.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListBackupPlanVersionsOutput": {"type": "structure", "members": {"NextToken": {"shape": "string", "documentation": "<p>The next item following a partial list of returned items. For example, if a request is made to return <code>MaxResults</code> number of items, <code>NextToken</code> allows you to return more items in your list starting at the location pointed to by the next token.</p>"}, "BackupPlanVersionsList": {"shape": "BackupPlanVersionsList", "documentation": "<p>An array of version list items containing metadata about your backup plans.</p>"}}}, "ListBackupPlansInput": {"type": "structure", "members": {"NextToken": {"shape": "string", "documentation": "<p>The next item following a partial list of returned items. For example, if a request is made to return <code>MaxResults</code> number of items, <code>NextToken</code> allows you to return more items in your list starting at the location pointed to by the next token.</p>", "location": "querystring", "locationName": "nextToken"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of items to be returned.</p>", "location": "querystring", "locationName": "maxResults"}, "IncludeDeleted": {"shape": "Boolean", "documentation": "<p>A Boolean value with a default value of <code>FALSE</code> that returns deleted backup plans when set to <code>TRUE</code>.</p>", "location": "querystring", "locationName": "includeDeleted"}}}, "ListBackupPlansOutput": {"type": "structure", "members": {"NextToken": {"shape": "string", "documentation": "<p>The next item following a partial list of returned items. For example, if a request is made to return <code>MaxResults</code> number of items, <code>NextToken</code> allows you to return more items in your list starting at the location pointed to by the next token.</p>"}, "BackupPlansList": {"shape": "BackupPlansList", "documentation": "<p>Information about the backup plans.</p>"}}}, "ListBackupSelectionsInput": {"type": "structure", "required": ["BackupPlanId"], "members": {"BackupPlanId": {"shape": "string", "documentation": "<p>Uniquely identifies a backup plan.</p>", "location": "uri", "locationName": "backupPlanId"}, "NextToken": {"shape": "string", "documentation": "<p>The next item following a partial list of returned items. For example, if a request is made to return <code>MaxResults</code> number of items, <code>NextToken</code> allows you to return more items in your list starting at the location pointed to by the next token.</p>", "location": "querystring", "locationName": "nextToken"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of items to be returned.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListBackupSelectionsOutput": {"type": "structure", "members": {"NextToken": {"shape": "string", "documentation": "<p>The next item following a partial list of returned items. For example, if a request is made to return <code>MaxResults</code> number of items, <code>NextToken</code> allows you to return more items in your list starting at the location pointed to by the next token.</p>"}, "BackupSelectionsList": {"shape": "BackupSelectionsList", "documentation": "<p>An array of backup selection list items containing metadata about each resource in the list.</p>"}}}, "ListBackupVaultsInput": {"type": "structure", "members": {"ByVaultType": {"shape": "VaultType", "documentation": "<p>This parameter will sort the list of vaults by vault type.</p>", "location": "querystring", "locationName": "vaultType"}, "ByShared": {"shape": "boolean", "documentation": "<p>This parameter will sort the list of vaults by shared vaults.</p>", "location": "querystring", "locationName": "shared"}, "NextToken": {"shape": "string", "documentation": "<p>The next item following a partial list of returned items. For example, if a request is made to return <code>MaxResults</code> number of items, <code>NextToken</code> allows you to return more items in your list starting at the location pointed to by the next token.</p>", "location": "querystring", "locationName": "nextToken"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of items to be returned.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListBackupVaultsOutput": {"type": "structure", "members": {"BackupVaultList": {"shape": "BackupVaultList", "documentation": "<p>An array of backup vault list members containing vault metadata, including Amazon Resource Name (ARN), display name, creation date, number of saved recovery points, and encryption information if the resources saved in the backup vault are encrypted.</p>"}, "NextToken": {"shape": "string", "documentation": "<p>The next item following a partial list of returned items. For example, if a request is made to return <code>MaxResults</code> number of items, <code>NextToken</code> allows you to return more items in your list starting at the location pointed to by the next token.</p>"}}}, "ListCopyJobSummariesInput": {"type": "structure", "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>Returns the job count for the specified account.</p> <p>If the request is sent from a member account or an account not part of Amazon Web Services Organizations, jobs within requestor's account will be returned.</p> <p>Root, admin, and delegated administrator accounts can use the value ANY to return job counts from every account in the organization.</p> <p> <code>AGGREGATE_ALL</code> aggregates job counts from all accounts within the authenticated organization, then returns the sum.</p>", "location": "querystring", "locationName": "AccountId"}, "State": {"shape": "CopyJobStatus", "documentation": "<p>This parameter returns the job count for jobs with the specified state.</p> <p>The the value ANY returns count of all states.</p> <p> <code>AGGREGATE_ALL</code> aggregates job counts for all states and returns the sum.</p>", "location": "querystring", "locationName": "State"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>Returns the job count for the specified resource type. Use request <code>GetSupportedResourceTypes</code> to obtain strings for supported resource types.</p> <p>The the value ANY returns count of all resource types.</p> <p> <code>AGGREGATE_ALL</code> aggregates job counts for all resource types and returns the sum.</p> <p>The type of Amazon Web Services resource to be backed up; for example, an Amazon Elastic Block Store (Amazon EBS) volume or an Amazon Relational Database Service (Amazon RDS) database.</p>", "location": "querystring", "locationName": "ResourceType"}, "MessageCategory": {"shape": "MessageCategory", "documentation": "<p>This parameter returns the job count for the specified message category.</p> <p>Example accepted strings include <code>AccessDenied</code>, <code>Success</code>, and <code>InvalidParameters</code>. See <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/monitoring.html\">Monitoring</a> for a list of accepted MessageCategory strings.</p> <p>The the value ANY returns count of all message categories.</p> <p> <code>AGGREGATE_ALL</code> aggregates job counts for all message categories and returns the sum.</p>", "location": "querystring", "locationName": "MessageCategory"}, "AggregationPeriod": {"shape": "AggregationPeriod", "documentation": "<p>The period for the returned results.</p> <ul> <li> <p> <code>ONE_DAY</code> - The daily job count for the prior 14 days.</p> </li> <li> <p> <code>SEVEN_DAYS</code> - The aggregated job count for the prior 7 days.</p> </li> <li> <p> <code>FOURTEEN_DAYS</code> - The aggregated job count for prior 14 days.</p> </li> </ul>", "location": "querystring", "locationName": "AggregationPeriod"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>This parameter sets the maximum number of items to be returned.</p> <p>The value is an integer. Range of accepted values is from 1 to 500.</p>", "location": "querystring", "locationName": "MaxResults"}, "NextToken": {"shape": "string", "documentation": "<p>The next item following a partial list of returned resources. For example, if a request is made to return <code>MaxResults</code> number of resources, <code>NextToken</code> allows you to return more items in your list starting at the location pointed to by the next token.</p>", "location": "querystring", "locationName": "NextToken"}}}, "ListCopyJobSummariesOutput": {"type": "structure", "members": {"CopyJobSummaries": {"shape": "CopyJobSummaryList", "documentation": "<p>This return shows a summary that contains Region, Account, State, ResourceType, MessageCategory, StartTime, EndTime, and Count of included jobs.</p>"}, "AggregationPeriod": {"shape": "string", "documentation": "<p>The period for the returned results.</p> <ul> <li> <p> <code>ONE_DAY</code> - The daily job count for the prior 14 days.</p> </li> <li> <p> <code>SEVEN_DAYS</code> - The aggregated job count for the prior 7 days.</p> </li> <li> <p> <code>FOURTEEN_DAYS</code> - The aggregated job count for prior 14 days.</p> </li> </ul>"}, "NextToken": {"shape": "string", "documentation": "<p>The next item following a partial list of returned resources. For example, if a request is made to return <code>MaxResults</code> number of resources, <code>NextToken</code> allows you to return more items in your list starting at the location pointed to by the next token.</p>"}}}, "ListCopyJobsInput": {"type": "structure", "members": {"NextToken": {"shape": "string", "documentation": "<p>The next item following a partial list of returned items. For example, if a request is made to return MaxResults number of items, NextToken allows you to return more items in your list starting at the location pointed to by the next token. </p>", "location": "querystring", "locationName": "nextToken"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of items to be returned.</p>", "location": "querystring", "locationName": "maxResults"}, "ByResourceArn": {"shape": "ARN", "documentation": "<p>Returns only copy jobs that match the specified resource Amazon Resource Name (ARN). </p>", "location": "querystring", "locationName": "resourceArn"}, "ByState": {"shape": "CopyJobState", "documentation": "<p>Returns only copy jobs that are in the specified state.</p>", "location": "querystring", "locationName": "state"}, "ByCreatedBefore": {"shape": "timestamp", "documentation": "<p>Returns only copy jobs that were created before the specified date.</p>", "location": "querystring", "locationName": "createdBefore"}, "ByCreatedAfter": {"shape": "timestamp", "documentation": "<p>Returns only copy jobs that were created after the specified date.</p>", "location": "querystring", "locationName": "createdAfter"}, "ByResourceType": {"shape": "ResourceType", "documentation": "<p>Returns only backup jobs for the specified resources:</p> <ul> <li> <p> <code>Aurora</code> for Amazon Aurora</p> </li> <li> <p> <code>CloudFormation</code> for CloudFormation</p> </li> <li> <p> <code>DocumentDB</code> for Amazon DocumentDB (with MongoDB compatibility)</p> </li> <li> <p> <code>DynamoDB</code> for Amazon DynamoDB</p> </li> <li> <p> <code>EBS</code> for Amazon Elastic Block Store</p> </li> <li> <p> <code>EC2</code> for Amazon Elastic Compute Cloud</p> </li> <li> <p> <code>EFS</code> for Amazon Elastic File System</p> </li> <li> <p> <code>FSx</code> for Amazon FSx</p> </li> <li> <p> <code>Neptune</code> for Amazon Neptune</p> </li> <li> <p> <code>RDS</code> for Amazon Relational Database Service</p> </li> <li> <p> <code>Redshift</code> for Amazon Redshift</p> </li> <li> <p> <code>S3</code> for Amazon Simple Storage Service (Amazon S3)</p> </li> <li> <p> <code>SAP HANA on Amazon EC2</code> for SAP HANA databases on Amazon Elastic Compute Cloud instances</p> </li> <li> <p> <code>Storage Gateway</code> for Storage Gateway</p> </li> <li> <p> <code>Timestream</code> for Amazon Timestream</p> </li> <li> <p> <code>VirtualMachine</code> for VMware virtual machines</p> </li> </ul>", "location": "querystring", "locationName": "resourceType"}, "ByDestinationVaultArn": {"shape": "string", "documentation": "<p>An Amazon Resource Name (ARN) that uniquely identifies a source backup vault to copy from; for example, <code>arn:aws:backup:us-east-1:************:backup-vault:aBackupVault</code>. </p>", "location": "querystring", "locationName": "destinationVaultArn"}, "ByAccountId": {"shape": "AccountId", "documentation": "<p>The account ID to list the jobs from. Returns only copy jobs associated with the specified account ID.</p>", "location": "querystring", "locationName": "accountId"}, "ByCompleteBefore": {"shape": "timestamp", "documentation": "<p>Returns only copy jobs completed before a date expressed in Unix format and Coordinated Universal Time (UTC).</p>", "location": "querystring", "locationName": "completeBefore"}, "ByCompleteAfter": {"shape": "timestamp", "documentation": "<p>Returns only copy jobs completed after a date expressed in Unix format and Coordinated Universal Time (UTC).</p>", "location": "querystring", "locationName": "completeAfter"}, "ByParentJobId": {"shape": "string", "documentation": "<p>This is a filter to list child (nested) jobs based on parent job ID.</p>", "location": "querystring", "locationName": "parentJobId"}, "ByMessageCategory": {"shape": "string", "documentation": "<p>This is an optional parameter that can be used to filter out jobs with a MessageCategory which matches the value you input.</p> <p>Example strings may include <code>AccessDenied</code>, <code>SUCCESS</code>, <code>AGGREGATE_ALL</code>, and <code>INVALIDPARAMETERS</code>.</p> <p>View <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/monitoring.html\">Monitoring</a> for a list of accepted strings.</p> <p>The the value ANY returns count of all message categories.</p> <p> <code>AGGREGATE_ALL</code> aggregates job counts for all message categories and returns the sum.</p>", "location": "querystring", "locationName": "messageCategory"}}}, "ListCopyJobsOutput": {"type": "structure", "members": {"CopyJobs": {"shape": "CopyJobsList", "documentation": "<p>An array of structures containing metadata about your copy jobs returned in JSON format. </p>"}, "NextToken": {"shape": "string", "documentation": "<p>The next item following a partial list of returned items. For example, if a request is made to return MaxResults number of items, NextToken allows you to return more items in your list starting at the location pointed to by the next token. </p>"}}}, "ListFrameworksInput": {"type": "structure", "members": {"MaxResults": {"shape": "MaxFrameworkInputs", "documentation": "<p>The number of desired results from 1 to 1000. Optional. If unspecified, the query will return 1 MB of data.</p>", "location": "querystring", "locationName": "MaxResults"}, "NextToken": {"shape": "string", "documentation": "<p>An identifier that was returned from the previous call to this operation, which can be used to return the next set of items in the list.</p>", "location": "querystring", "locationName": "NextToken"}}}, "ListFrameworksOutput": {"type": "structure", "members": {"Frameworks": {"shape": "FrameworkList", "documentation": "<p>The frameworks with details for each framework, including the framework name, Amazon Resource Name (ARN), description, number of controls, creation time, and deployment status.</p>"}, "NextToken": {"shape": "string", "documentation": "<p>An identifier that was returned from the previous call to this operation, which can be used to return the next set of items in the list.</p>"}}}, "ListIndexedRecoveryPointsInput": {"type": "structure", "members": {"NextToken": {"shape": "string", "documentation": "<p>The next item following a partial list of returned recovery points.</p> <p>For example, if a request is made to return <code>MaxResults</code> number of indexed recovery points, <code>NextToken</code> allows you to return more items in your list starting at the location pointed to by the next token.</p>", "location": "querystring", "locationName": "nextToken"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of resource list items to be returned.</p>", "location": "querystring", "locationName": "maxResults"}, "SourceResourceArn": {"shape": "ARN", "documentation": "<p>A string of the Amazon Resource Name (ARN) that uniquely identifies the source resource.</p>", "location": "querystring", "locationName": "sourceResourceArn"}, "CreatedBefore": {"shape": "timestamp", "documentation": "<p>Returns only indexed recovery points that were created before the specified date.</p>", "location": "querystring", "locationName": "createdBefore"}, "CreatedAfter": {"shape": "timestamp", "documentation": "<p>Returns only indexed recovery points that were created after the specified date.</p>", "location": "querystring", "locationName": "createdAfter"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>Returns a list of indexed recovery points for the specified resource type(s).</p> <p>Accepted values include:</p> <ul> <li> <p> <code>EBS</code> for Amazon Elastic Block Store</p> </li> <li> <p> <code>S3</code> for Amazon Simple Storage Service (Amazon S3)</p> </li> </ul>", "location": "querystring", "locationName": "resourceType"}, "IndexStatus": {"shape": "IndexStatus", "documentation": "<p>Include this parameter to filter the returned list by the indicated statuses.</p> <p>Accepted values: <code>PENDING</code> | <code>ACTIVE</code> | <code>FAILED</code> | <code>DELETING</code> </p> <p>A recovery point with an index that has the status of <code>ACTIVE</code> can be included in a search.</p>", "location": "querystring", "locationName": "indexStatus"}}}, "ListIndexedRecoveryPointsOutput": {"type": "structure", "members": {"IndexedRecoveryPoints": {"shape": "IndexedRecoveryPointList", "documentation": "<p>This is a list of recovery points that have an associated index, belonging to the specified account.</p>"}, "NextToken": {"shape": "string", "documentation": "<p>The next item following a partial list of returned recovery points.</p> <p>For example, if a request is made to return <code>MaxResults</code> number of indexed recovery points, <code>NextToken</code> allows you to return more items in your list starting at the location pointed to by the next token.</p>"}}}, "ListLegalHoldsInput": {"type": "structure", "members": {"NextToken": {"shape": "string", "documentation": "<p>The next item following a partial list of returned resources. For example, if a request is made to return <code>MaxResults</code> number of resources, <code>NextToken</code> allows you to return more items in your list starting at the location pointed to by the next token.</p>", "location": "querystring", "locationName": "nextToken"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of resource list items to be returned.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListLegalHoldsOutput": {"type": "structure", "members": {"NextToken": {"shape": "string", "documentation": "<p>The next item following a partial list of returned resources. For example, if a request is made to return <code>MaxResults</code> number of resources, <code>NextToken</code> allows you to return more items in your list starting at the location pointed to by the next token.</p>"}, "LegalHolds": {"shape": "LegalHoldsList", "documentation": "<p>This is an array of returned legal holds, both active and previous.</p>"}}}, "ListOfTags": {"type": "list", "member": {"shape": "Condition"}}, "ListProtectedResourcesByBackupVaultInput": {"type": "structure", "required": ["BackupVaultName"], "members": {"BackupVaultName": {"shape": "BackupVaultName", "documentation": "<p>The list of protected resources by backup vault within the vault(s) you specify by name.</p>", "location": "uri", "locationName": "backup<PERSON><PERSON><PERSON><PERSON>"}, "BackupVaultAccountId": {"shape": "AccountId", "documentation": "<p>The list of protected resources by backup vault within the vault(s) you specify by account ID.</p>", "location": "querystring", "locationName": "backupVaultAccountId"}, "NextToken": {"shape": "string", "documentation": "<p>The next item following a partial list of returned items. For example, if a request is made to return <code>MaxResults</code> number of items, <code>NextToken</code> allows you to return more items in your list starting at the location pointed to by the next token.</p>", "location": "querystring", "locationName": "nextToken"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of items to be returned.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListProtectedResourcesByBackupVaultOutput": {"type": "structure", "members": {"Results": {"shape": "ProtectedResourcesList", "documentation": "<p>These are the results returned for the request ListProtectedResourcesByBackupVault.</p>"}, "NextToken": {"shape": "string", "documentation": "<p>The next item following a partial list of returned items. For example, if a request is made to return <code>MaxResults</code> number of items, <code>NextToken</code> allows you to return more items in your list starting at the location pointed to by the next token.</p>"}}}, "ListProtectedResourcesInput": {"type": "structure", "members": {"NextToken": {"shape": "string", "documentation": "<p>The next item following a partial list of returned items. For example, if a request is made to return <code>MaxResults</code> number of items, <code>NextToken</code> allows you to return more items in your list starting at the location pointed to by the next token.</p>", "location": "querystring", "locationName": "nextToken"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of items to be returned.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListProtectedResourcesOutput": {"type": "structure", "members": {"Results": {"shape": "ProtectedResourcesList", "documentation": "<p>An array of resources successfully backed up by Backup including the time the resource was saved, an Amazon Resource Name (ARN) of the resource, and a resource type.</p>"}, "NextToken": {"shape": "string", "documentation": "<p>The next item following a partial list of returned items. For example, if a request is made to return <code>MaxResults</code> number of items, <code>NextToken</code> allows you to return more items in your list starting at the location pointed to by the next token.</p>"}}}, "ListRecoveryPointsByBackupVaultInput": {"type": "structure", "required": ["BackupVaultName"], "members": {"BackupVaultName": {"shape": "BackupVaultName", "documentation": "<p>The name of a logical container where backups are stored. Backup vaults are identified by names that are unique to the account used to create them and the Amazon Web Services Region where they are created.</p> <note> <p>Backup vault name might not be available when a supported service creates the backup.</p> </note>", "location": "uri", "locationName": "backup<PERSON><PERSON><PERSON><PERSON>"}, "BackupVaultAccountId": {"shape": "AccountId", "documentation": "<p>This parameter will sort the list of recovery points by account ID.</p>", "location": "querystring", "locationName": "backupVaultAccountId"}, "NextToken": {"shape": "string", "documentation": "<p>The next item following a partial list of returned items. For example, if a request is made to return <code>MaxResults</code> number of items, <code>NextToken</code> allows you to return more items in your list starting at the location pointed to by the next token.</p>", "location": "querystring", "locationName": "nextToken"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of items to be returned.</p>", "location": "querystring", "locationName": "maxResults"}, "ByResourceArn": {"shape": "ARN", "documentation": "<p>Returns only recovery points that match the specified resource Amazon Resource Name (ARN).</p>", "location": "querystring", "locationName": "resourceArn"}, "ByResourceType": {"shape": "ResourceType", "documentation": "<p>Returns only recovery points that match the specified resource type(s):</p> <ul> <li> <p> <code>Aurora</code> for Amazon Aurora</p> </li> <li> <p> <code>CloudFormation</code> for CloudFormation</p> </li> <li> <p> <code>DocumentDB</code> for Amazon DocumentDB (with MongoDB compatibility)</p> </li> <li> <p> <code>DynamoDB</code> for Amazon DynamoDB</p> </li> <li> <p> <code>EBS</code> for Amazon Elastic Block Store</p> </li> <li> <p> <code>EC2</code> for Amazon Elastic Compute Cloud</p> </li> <li> <p> <code>EFS</code> for Amazon Elastic File System</p> </li> <li> <p> <code>FSx</code> for Amazon FSx</p> </li> <li> <p> <code>Neptune</code> for Amazon Neptune</p> </li> <li> <p> <code>RDS</code> for Amazon Relational Database Service</p> </li> <li> <p> <code>Redshift</code> for Amazon Redshift</p> </li> <li> <p> <code>S3</code> for Amazon Simple Storage Service (Amazon S3)</p> </li> <li> <p> <code>SAP HANA on Amazon EC2</code> for SAP HANA databases on Amazon Elastic Compute Cloud instances</p> </li> <li> <p> <code>Storage Gateway</code> for Storage Gateway</p> </li> <li> <p> <code>Timestream</code> for Amazon Timestream</p> </li> <li> <p> <code>VirtualMachine</code> for VMware virtual machines</p> </li> </ul>", "location": "querystring", "locationName": "resourceType"}, "ByBackupPlanId": {"shape": "string", "documentation": "<p>Returns only recovery points that match the specified backup plan ID.</p>", "location": "querystring", "locationName": "backupPlanId"}, "ByCreatedBefore": {"shape": "timestamp", "documentation": "<p>Returns only recovery points that were created before the specified timestamp.</p>", "location": "querystring", "locationName": "createdBefore"}, "ByCreatedAfter": {"shape": "timestamp", "documentation": "<p>Returns only recovery points that were created after the specified timestamp.</p>", "location": "querystring", "locationName": "createdAfter"}, "ByParentRecoveryPointArn": {"shape": "ARN", "documentation": "<p>This returns only recovery points that match the specified parent (composite) recovery point Amazon Resource Name (ARN).</p>", "location": "querystring", "locationName": "parentRecoveryPointArn"}}}, "ListRecoveryPointsByBackupVaultOutput": {"type": "structure", "members": {"NextToken": {"shape": "string", "documentation": "<p>The next item following a partial list of returned items. For example, if a request is made to return <code>MaxResults</code> number of items, <code>NextToken</code> allows you to return more items in your list starting at the location pointed to by the next token.</p>"}, "RecoveryPoints": {"shape": "RecoveryPointByBackupVaultList", "documentation": "<p>An array of objects that contain detailed information about recovery points saved in a backup vault.</p>"}}}, "ListRecoveryPointsByLegalHoldInput": {"type": "structure", "required": ["LegalHoldId"], "members": {"LegalHoldId": {"shape": "string", "documentation": "<p>The ID of the legal hold.</p>", "location": "uri", "locationName": "legalHoldId"}, "NextToken": {"shape": "string", "documentation": "<p>The next item following a partial list of returned resources. For example, if a request is made to return <code>MaxResults</code> number of resources, <code>NextToken</code> allows you to return more items in your list starting at the location pointed to by the next token.</p>", "location": "querystring", "locationName": "nextToken"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of resource list items to be returned.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListRecoveryPointsByLegalHoldOutput": {"type": "structure", "members": {"RecoveryPoints": {"shape": "RecoveryPointsList", "documentation": "<p>The recovery points.</p>"}, "NextToken": {"shape": "string", "documentation": "<p>The next item following a partial list of returned resources.</p>"}}}, "ListRecoveryPointsByResourceInput": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "ARN", "documentation": "<p>An ARN that uniquely identifies a resource. The format of the ARN depends on the resource type.</p>", "location": "uri", "locationName": "resourceArn"}, "NextToken": {"shape": "string", "documentation": "<p>The next item following a partial list of returned items. For example, if a request is made to return <code>MaxResults</code> number of items, <code>NextToken</code> allows you to return more items in your list starting at the location pointed to by the next token.</p>", "location": "querystring", "locationName": "nextToken"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of items to be returned.</p> <note> <p>Amazon RDS requires a value of at least 20.</p> </note>", "location": "querystring", "locationName": "maxResults"}, "ManagedByAWSBackupOnly": {"shape": "boolean", "documentation": "<p>This attribute filters recovery points based on ownership.</p> <p>If this is set to <code>TRUE</code>, the response will contain recovery points associated with the selected resources that are managed by Backup.</p> <p>If this is set to <code>FALSE</code>, the response will contain all recovery points associated with the selected resource.</p> <p>Type: Boolean</p>", "location": "querystring", "locationName": "managedByAWSBackupOnly"}}}, "ListRecoveryPointsByResourceOutput": {"type": "structure", "members": {"NextToken": {"shape": "string", "documentation": "<p>The next item following a partial list of returned items. For example, if a request is made to return <code>MaxResults</code> number of items, <code>NextToken</code> allows you to return more items in your list starting at the location pointed to by the next token.</p>"}, "RecoveryPoints": {"shape": "RecoveryPointByResourceList", "documentation": "<p>An array of objects that contain detailed information about recovery points of the specified resource type.</p> <note> <p>Only Amazon EFS and Amazon EC2 recovery points return BackupVaultName.</p> </note>"}}}, "ListReportJobsInput": {"type": "structure", "members": {"ByReportPlanName": {"shape": "ReportPlanName", "documentation": "<p>Returns only report jobs with the specified report plan name.</p>", "location": "querystring", "locationName": "ReportPlanName"}, "ByCreationBefore": {"shape": "timestamp", "documentation": "<p>Returns only report jobs that were created before the date and time specified in Unix format and Coordinated Universal Time (UTC). For example, the value ********** represents Friday, January 26, 2018 12:11:30 AM.</p>", "location": "querystring", "locationName": "CreationBefore"}, "ByCreationAfter": {"shape": "timestamp", "documentation": "<p>Returns only report jobs that were created after the date and time specified in Unix format and Coordinated Universal Time (UTC). For example, the value ********** represents Friday, January 26, 2018 12:11:30 AM.</p>", "location": "querystring", "locationName": "CreationAfter"}, "ByStatus": {"shape": "string", "documentation": "<p>Returns only report jobs that are in the specified status. The statuses are:</p> <p> <code>CREATED | RUNNING | COMPLETED | FAILED</code> </p>", "location": "querystring", "locationName": "Status"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The number of desired results from 1 to 1000. Optional. If unspecified, the query will return 1 MB of data.</p>", "location": "querystring", "locationName": "MaxResults"}, "NextToken": {"shape": "string", "documentation": "<p>An identifier that was returned from the previous call to this operation, which can be used to return the next set of items in the list.</p>", "location": "querystring", "locationName": "NextToken"}}}, "ListReportJobsOutput": {"type": "structure", "members": {"ReportJobs": {"shape": "ReportJobList", "documentation": "<p>Details about your report jobs in JSON format.</p>"}, "NextToken": {"shape": "string", "documentation": "<p>An identifier that was returned from the previous call to this operation, which can be used to return the next set of items in the list.</p>"}}}, "ListReportPlansInput": {"type": "structure", "members": {"MaxResults": {"shape": "MaxResults", "documentation": "<p>The number of desired results from 1 to 1000. Optional. If unspecified, the query will return 1 MB of data.</p>", "location": "querystring", "locationName": "MaxResults"}, "NextToken": {"shape": "string", "documentation": "<p>An identifier that was returned from the previous call to this operation, which can be used to return the next set of items in the list.</p>", "location": "querystring", "locationName": "NextToken"}}}, "ListReportPlansOutput": {"type": "structure", "members": {"ReportPlans": {"shape": "ReportPlanList", "documentation": "<p>The report plans with detailed information for each plan. This information includes the Amazon Resource Name (ARN), report plan name, description, settings, delivery channel, deployment status, creation time, and last times the report plan attempted to and successfully ran.</p>"}, "NextToken": {"shape": "string", "documentation": "<p>An identifier that was returned from the previous call to this operation, which can be used to return the next set of items in the list.</p>"}}}, "ListRestoreAccessBackupVaultsInput": {"type": "structure", "required": ["BackupVaultName"], "members": {"BackupVaultName": {"shape": "BackupVaultName", "documentation": "<p>The name of the backup vault for which to list associated restore access backup vaults.</p>", "location": "uri", "locationName": "backup<PERSON><PERSON><PERSON><PERSON>"}, "NextToken": {"shape": "string", "documentation": "<p>The pagination token from a previous request to retrieve the next set of results.</p>", "location": "querystring", "locationName": "nextToken"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of items to return in the response.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListRestoreAccessBackupVaultsOutput": {"type": "structure", "members": {"NextToken": {"shape": "string", "documentation": "<p>The pagination token to use in a subsequent request to retrieve the next set of results.</p>"}, "RestoreAccessBackupVaults": {"shape": "RestoreAccessBackupVaultList", "documentation": "<p>A list of restore access backup vaults associated with the specified backup vault.</p>"}}}, "ListRestoreJobSummariesInput": {"type": "structure", "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>Returns the job count for the specified account.</p> <p>If the request is sent from a member account or an account not part of Amazon Web Services Organizations, jobs within requestor's account will be returned.</p> <p>Root, admin, and delegated administrator accounts can use the value ANY to return job counts from every account in the organization.</p> <p> <code>AGGREGATE_ALL</code> aggregates job counts from all accounts within the authenticated organization, then returns the sum.</p>", "location": "querystring", "locationName": "AccountId"}, "State": {"shape": "RestoreJobState", "documentation": "<p>This parameter returns the job count for jobs with the specified state.</p> <p>The the value ANY returns count of all states.</p> <p> <code>AGGREGATE_ALL</code> aggregates job counts for all states and returns the sum.</p>", "location": "querystring", "locationName": "State"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>Returns the job count for the specified resource type. Use request <code>GetSupportedResourceTypes</code> to obtain strings for supported resource types.</p> <p>The the value ANY returns count of all resource types.</p> <p> <code>AGGREGATE_ALL</code> aggregates job counts for all resource types and returns the sum.</p> <p>The type of Amazon Web Services resource to be backed up; for example, an Amazon Elastic Block Store (Amazon EBS) volume or an Amazon Relational Database Service (Amazon RDS) database.</p>", "location": "querystring", "locationName": "ResourceType"}, "AggregationPeriod": {"shape": "AggregationPeriod", "documentation": "<p>The period for the returned results.</p> <ul> <li> <p> <code>ONE_DAY</code> - The daily job count for the prior 14 days.</p> </li> <li> <p> <code>SEVEN_DAYS</code> - The aggregated job count for the prior 7 days.</p> </li> <li> <p> <code>FOURTEEN_DAYS</code> - The aggregated job count for prior 14 days.</p> </li> </ul>", "location": "querystring", "locationName": "AggregationPeriod"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>This parameter sets the maximum number of items to be returned.</p> <p>The value is an integer. Range of accepted values is from 1 to 500.</p>", "location": "querystring", "locationName": "MaxResults"}, "NextToken": {"shape": "string", "documentation": "<p>The next item following a partial list of returned resources. For example, if a request is made to return <code>MaxResults</code> number of resources, <code>NextToken</code> allows you to return more items in your list starting at the location pointed to by the next token.</p>", "location": "querystring", "locationName": "NextToken"}}}, "ListRestoreJobSummariesOutput": {"type": "structure", "members": {"RestoreJobSummaries": {"shape": "RestoreJobSummaryList", "documentation": "<p>This return contains a summary that contains Region, Account, State, ResourceType, MessageCategory, StartTime, EndTime, and Count of included jobs.</p>"}, "AggregationPeriod": {"shape": "string", "documentation": "<p>The period for the returned results.</p> <ul> <li> <p> <code>ONE_DAY</code> - The daily job count for the prior 14 days.</p> </li> <li> <p> <code>SEVEN_DAYS</code> - The aggregated job count for the prior 7 days.</p> </li> <li> <p> <code>FOURTEEN_DAYS</code> - The aggregated job count for prior 14 days.</p> </li> </ul>"}, "NextToken": {"shape": "string", "documentation": "<p>The next item following a partial list of returned resources. For example, if a request is made to return <code>MaxResults</code> number of resources, <code>NextToken</code> allows you to return more items in your list starting at the location pointed to by the next token.</p>"}}}, "ListRestoreJobsByProtectedResourceInput": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "ARN", "documentation": "<p>Returns only restore jobs that match the specified resource Amazon Resource Name (ARN).</p>", "location": "uri", "locationName": "resourceArn"}, "ByStatus": {"shape": "RestoreJobStatus", "documentation": "<p>Returns only restore jobs associated with the specified job status.</p>", "location": "querystring", "locationName": "status"}, "ByRecoveryPointCreationDateAfter": {"shape": "timestamp", "documentation": "<p>Returns only restore jobs of recovery points that were created after the specified date.</p>", "location": "querystring", "locationName": "recoveryPointCreationDateAfter"}, "ByRecoveryPointCreationDateBefore": {"shape": "timestamp", "documentation": "<p>Returns only restore jobs of recovery points that were created before the specified date.</p>", "location": "querystring", "locationName": "recoveryPointCreationDateBefore"}, "NextToken": {"shape": "string", "documentation": "<p>The next item following a partial list of returned items. For example, if a request ismade to return <code>MaxResults</code> number of items, <code>NextToken</code> allows you to return more items in your list starting at the location pointed to by the next token.</p>", "location": "querystring", "locationName": "nextToken"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of items to be returned.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListRestoreJobsByProtectedResourceOutput": {"type": "structure", "members": {"RestoreJobs": {"shape": "RestoreJobsList", "documentation": "<p>An array of objects that contain detailed information about jobs to restore saved resources.&gt;</p>"}, "NextToken": {"shape": "string", "documentation": "<p>The next item following a partial list of returned items. For example, if a request is made to return <code>MaxResults</code> number of items, <code>NextToken</code> allows youto return more items in your list starting at the location pointed to by the next token</p>"}}}, "ListRestoreJobsInput": {"type": "structure", "members": {"NextToken": {"shape": "string", "documentation": "<p>The next item following a partial list of returned items. For example, if a request is made to return <code>MaxResults</code> number of items, <code>NextToken</code> allows you to return more items in your list starting at the location pointed to by the next token.</p>", "location": "querystring", "locationName": "nextToken"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of items to be returned.</p>", "location": "querystring", "locationName": "maxResults"}, "ByAccountId": {"shape": "AccountId", "documentation": "<p>The account ID to list the jobs from. Returns only restore jobs associated with the specified account ID.</p>", "location": "querystring", "locationName": "accountId"}, "ByResourceType": {"shape": "ResourceType", "documentation": "<p>Include this parameter to return only restore jobs for the specified resources:</p> <ul> <li> <p> <code>Aurora</code> for Amazon Aurora</p> </li> <li> <p> <code>CloudFormation</code> for CloudFormation</p> </li> <li> <p> <code>DocumentDB</code> for Amazon DocumentDB (with MongoDB compatibility)</p> </li> <li> <p> <code>DynamoDB</code> for Amazon DynamoDB</p> </li> <li> <p> <code>EBS</code> for Amazon Elastic Block Store</p> </li> <li> <p> <code>EC2</code> for Amazon Elastic Compute Cloud</p> </li> <li> <p> <code>EFS</code> for Amazon Elastic File System</p> </li> <li> <p> <code>FSx</code> for Amazon FSx</p> </li> <li> <p> <code>Neptune</code> for Amazon Neptune</p> </li> <li> <p> <code>RDS</code> for Amazon Relational Database Service</p> </li> <li> <p> <code>Redshift</code> for Amazon Redshift</p> </li> <li> <p> <code>S3</code> for Amazon Simple Storage Service (Amazon S3)</p> </li> <li> <p> <code>SAP HANA on Amazon EC2</code> for SAP HANA databases on Amazon Elastic Compute Cloud instances</p> </li> <li> <p> <code>Storage Gateway</code> for Storage Gateway</p> </li> <li> <p> <code>Timestream</code> for Amazon Timestream</p> </li> <li> <p> <code>VirtualMachine</code> for VMware virtual machines</p> </li> </ul>", "location": "querystring", "locationName": "resourceType"}, "ByCreatedBefore": {"shape": "timestamp", "documentation": "<p>Returns only restore jobs that were created before the specified date.</p>", "location": "querystring", "locationName": "createdBefore"}, "ByCreatedAfter": {"shape": "timestamp", "documentation": "<p>Returns only restore jobs that were created after the specified date.</p>", "location": "querystring", "locationName": "createdAfter"}, "ByStatus": {"shape": "RestoreJobStatus", "documentation": "<p>Returns only restore jobs associated with the specified job status.</p>", "location": "querystring", "locationName": "status"}, "ByCompleteBefore": {"shape": "timestamp", "documentation": "<p>Returns only copy jobs completed before a date expressed in Unix format and Coordinated Universal Time (UTC).</p>", "location": "querystring", "locationName": "completeBefore"}, "ByCompleteAfter": {"shape": "timestamp", "documentation": "<p>Returns only copy jobs completed after a date expressed in Unix format and Coordinated Universal Time (UTC).</p>", "location": "querystring", "locationName": "completeAfter"}, "ByRestoreTestingPlanArn": {"shape": "ARN", "documentation": "<p>This returns only restore testing jobs that match the specified resource Amazon Resource Name (ARN).</p>", "location": "querystring", "locationName": "restoreTestingPlanArn"}}}, "ListRestoreJobsOutput": {"type": "structure", "members": {"RestoreJobs": {"shape": "RestoreJobsList", "documentation": "<p>An array of objects that contain detailed information about jobs to restore saved resources.</p>"}, "NextToken": {"shape": "string", "documentation": "<p>The next item following a partial list of returned items. For example, if a request is made to return <code>MaxResults</code> number of items, <code>NextToken</code> allows you to return more items in your list starting at the location pointed to by the next token.</p>"}}}, "ListRestoreTestingPlansInput": {"type": "structure", "members": {"MaxResults": {"shape": "ListRestoreTestingPlansInputMaxResultsInteger", "documentation": "<p>The maximum number of items to be returned.</p>", "location": "querystring", "locationName": "MaxResults"}, "NextToken": {"shape": "String", "documentation": "<p>The next item following a partial list of returned items. For example, if a request is made to return <code>MaxResults</code> number of items, <code>NextToken</code> allows you to return more items in your list starting at the location pointed to by the nexttoken.</p>", "location": "querystring", "locationName": "NextToken"}}}, "ListRestoreTestingPlansInputMaxResultsInteger": {"type": "integer", "box": true, "max": 1000, "min": 1}, "ListRestoreTestingPlansOutput": {"type": "structure", "required": ["RestoreTestingPlans"], "members": {"NextToken": {"shape": "String", "documentation": "<p>The next item following a partial list of returned items. For example, if a request is made to return <code>MaxResults</code> number of items, <code>NextToken</code> allows you to return more items in your list starting at the location pointed to by the nexttoken.</p>"}, "RestoreTestingPlans": {"shape": "RestoreTestingPlans", "documentation": "<p>This is a returned list of restore testing plans.</p>"}}}, "ListRestoreTestingSelectionsInput": {"type": "structure", "required": ["RestoreTestingPlanName"], "members": {"MaxResults": {"shape": "ListRestoreTestingSelectionsInputMaxResultsInteger", "documentation": "<p>The maximum number of items to be returned.</p>", "location": "querystring", "locationName": "MaxResults"}, "NextToken": {"shape": "String", "documentation": "<p>The next item following a partial list of returned items. For example, if a request is made to return <code>MaxResults</code> number of items, <code>NextToken</code> allows you to return more items in your list starting at the location pointed to by the nexttoken.</p>", "location": "querystring", "locationName": "NextToken"}, "RestoreTestingPlanName": {"shape": "String", "documentation": "<p>Returns restore testing selections by the specified restore testing plan name.</p>", "location": "uri", "locationName": "RestoreTestingPlanName"}}}, "ListRestoreTestingSelectionsInputMaxResultsInteger": {"type": "integer", "box": true, "max": 1000, "min": 1}, "ListRestoreTestingSelectionsOutput": {"type": "structure", "required": ["RestoreTestingSelections"], "members": {"NextToken": {"shape": "String", "documentation": "<p>The next item following a partial list of returned items. For example, if a request is made to return <code>MaxResults</code> number of items, <code>NextToken</code> allows you to return more items in your list starting at the location pointed to by the nexttoken.</p>"}, "RestoreTestingSelections": {"shape": "RestoreTestingSelections", "documentation": "<p>The returned restore testing selections associated with the restore testing plan.</p>"}}}, "ListTagsInput": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "ARN", "documentation": "<p>An Amazon Resource Name (ARN) that uniquely identifies a resource. The format of the ARN depends on the type of resource. Valid targets for <code>ListTags</code> are recovery points, backup plans, and backup vaults.</p>", "location": "uri", "locationName": "resourceArn"}, "NextToken": {"shape": "string", "documentation": "<p>The next item following a partial list of returned items. For example, if a request is made to return <code>MaxResults</code> number of items, <code>NextToken</code> allows you to return more items in your list starting at the location pointed to by the next token.</p>", "location": "querystring", "locationName": "nextToken"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of items to be returned.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListTagsOutput": {"type": "structure", "members": {"NextToken": {"shape": "string", "documentation": "<p>The next item following a partial list of returned items. For example, if a request is made to return <code>MaxResults</code> number of items, <code>NextToken</code> allows you to return more items in your list starting at the location pointed to by the next token.</p>"}, "Tags": {"shape": "Tags", "documentation": "<p>Information about the tags.</p>"}}}, "Long": {"type": "long"}, "MaxFrameworkInputs": {"type": "integer", "max": 1000, "min": 1}, "MaxResults": {"type": "integer", "max": 1000, "min": 1}, "MessageCategory": {"type": "string"}, "Metadata": {"type": "map", "key": {"shape": "Metada<PERSON><PERSON><PERSON>"}, "value": {"shape": "MetadataValue"}, "sensitive": true}, "MetadataKey": {"type": "string"}, "MetadataValue": {"type": "string"}, "MissingParameterValueException": {"type": "structure", "members": {"Code": {"shape": "string"}, "Message": {"shape": "string"}, "Type": {"shape": "string", "documentation": "<p/>"}, "Context": {"shape": "string", "documentation": "<p/>"}}, "documentation": "<p>Indicates that a required parameter is missing.</p>", "exception": true}, "MpaRevokeSessionStatus": {"type": "string", "enum": ["PENDING", "FAILED"]}, "MpaSessionStatus": {"type": "string", "enum": ["PENDING", "APPROVED", "FAILED"]}, "ParameterName": {"type": "string"}, "ParameterValue": {"type": "string"}, "ProtectedResource": {"type": "structure", "members": {"ResourceArn": {"shape": "ARN", "documentation": "<p>An Amazon Resource Name (ARN) that uniquely identifies a resource. The format of the ARN depends on the resource type.</p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>The type of Amazon Web Services resource; for example, an Amazon Elastic Block Store (Amazon EBS) volume or an Amazon Relational Database Service (Amazon RDS) database. For Windows Volume Shadow Copy Service (VSS) backups, the only supported resource type is Amazon EC2.</p>"}, "LastBackupTime": {"shape": "timestamp", "documentation": "<p>The date and time a resource was last backed up, in Unix format and Coordinated Universal Time (UTC). The value of <code>LastBackupTime</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "ResourceName": {"shape": "string", "documentation": "<p>The non-unique name of the resource that belongs to the specified backup.</p>"}, "LastBackupVaultArn": {"shape": "ARN", "documentation": "<p>The ARN (Amazon Resource Name) of the backup vault that contains the most recent backup recovery point.</p>"}, "LastRecoveryPointArn": {"shape": "ARN", "documentation": "<p>The ARN (Amazon Resource Name) of the most recent recovery point.</p>"}}, "documentation": "<p>A structure that contains information about a backed-up resource.</p>"}, "ProtectedResourceConditions": {"type": "structure", "members": {"StringEquals": {"shape": "KeyValueList", "documentation": "<p>Filters the values of your tagged resources for only those resources that you tagged with the same value. Also called \"exact matching.\"</p>"}, "StringNotEquals": {"shape": "KeyValueList", "documentation": "<p>Filters the values of your tagged resources for only those resources that you tagged that do not have the same value. Also called \"negated matching.\"</p>"}}, "documentation": "<p>The conditions that you define for resources in your restore testing plan using tags.</p>"}, "ProtectedResourcesList": {"type": "list", "member": {"shape": "ProtectedResource"}}, "PutBackupVaultAccessPolicyInput": {"type": "structure", "required": ["BackupVaultName"], "members": {"BackupVaultName": {"shape": "BackupVaultName", "documentation": "<p>The name of a logical container where backups are stored. Backup vaults are identified by names that are unique to the account used to create them and the Amazon Web Services Region where they are created.</p>", "location": "uri", "locationName": "backup<PERSON><PERSON><PERSON><PERSON>"}, "Policy": {"shape": "IAMPolicy", "documentation": "<p>The backup vault access policy document in JSON format.</p>"}}}, "PutBackupVaultLockConfigurationInput": {"type": "structure", "required": ["BackupVaultName"], "members": {"BackupVaultName": {"shape": "BackupVaultName", "documentation": "<p>The Backup Vault Lock configuration that specifies the name of the backup vault it protects.</p>", "location": "uri", "locationName": "backup<PERSON><PERSON><PERSON><PERSON>"}, "MinRetentionDays": {"shape": "<PERSON>", "documentation": "<p>The Backup Vault Lock configuration that specifies the minimum retention period that the vault retains its recovery points. This setting can be useful if, for example, your organization's policies require you to retain certain data for at least seven years (2555 days).</p> <p>This parameter is required when a vault lock is created through CloudFormation; otherwise, this parameter is optional. If this parameter is not specified, Vault Lock will not enforce a minimum retention period.</p> <p>If this parameter is specified, any backup or copy job to the vault must have a lifecycle policy with a retention period equal to or longer than the minimum retention period. If the job's retention period is shorter than that minimum retention period, then the vault fails that backup or copy job, and you should either modify your lifecycle settings or use a different vault. The shortest minimum retention period you can specify is 1 day. Recovery points already saved in the vault prior to Vault Lock are not affected.</p>"}, "MaxRetentionDays": {"shape": "<PERSON>", "documentation": "<p>The Backup Vault Lock configuration that specifies the maximum retention period that the vault retains its recovery points. This setting can be useful if, for example, your organization's policies require you to destroy certain data after retaining it for four years (1460 days).</p> <p>If this parameter is not included, Vault Lock does not enforce a maximum retention period on the recovery points in the vault. If this parameter is included without a value, Vault Lock will not enforce a maximum retention period.</p> <p>If this parameter is specified, any backup or copy job to the vault must have a lifecycle policy with a retention period equal to or shorter than the maximum retention period. If the job's retention period is longer than that maximum retention period, then the vault fails the backup or copy job, and you should either modify your lifecycle settings or use a different vault. The longest maximum retention period you can specify is 36500 days (approximately 100 years). Recovery points already saved in the vault prior to Vault Lock are not affected.</p>"}, "ChangeableForDays": {"shape": "<PERSON>", "documentation": "<p>The Backup Vault Lock configuration that specifies the number of days before the lock date. For example, setting <code>ChangeableForDays</code> to 30 on Jan. 1, 2022 at 8pm UTC will set the lock date to Jan. 31, 2022 at 8pm UTC.</p> <p>Backup enforces a 72-hour cooling-off period before Vault Lock takes effect and becomes immutable. Therefore, you must set <code>ChangeableForDays</code> to 3 or greater.</p> <p>Before the lock date, you can delete Vault Lock from the vault using <code>DeleteBackupVaultLockConfiguration</code> or change the Vault Lock configuration using <code>PutBackupVaultLockConfiguration</code>. On and after the lock date, the Vault Lock becomes immutable and cannot be changed or deleted.</p> <p>If this parameter is not specified, you can delete Vault Lock from the vault using <code>DeleteBackupVaultLockConfiguration</code> or change the Vault Lock configuration using <code>PutBackupVaultLockConfiguration</code> at any time.</p>"}}}, "PutBackupVaultNotificationsInput": {"type": "structure", "required": ["BackupVaultName", "SNSTopicArn", "BackupVaultEvents"], "members": {"BackupVaultName": {"shape": "BackupVaultName", "documentation": "<p>The name of a logical container where backups are stored. Backup vaults are identified by names that are unique to the account used to create them and the Amazon Web Services Region where they are created.</p>", "location": "uri", "locationName": "backup<PERSON><PERSON><PERSON><PERSON>"}, "SNSTopicArn": {"shape": "ARN", "documentation": "<p>The Amazon Resource Name (ARN) that specifies the topic for a backup vault’s events; for example, <code>arn:aws:sns:us-west-2:************:MyVaultTopic</code>.</p>"}, "BackupVaultEvents": {"shape": "BackupVaultEvents", "documentation": "<p>An array of events that indicate the status of jobs to back up resources to the backup vault. For the list of supported events, common use cases, and code samples, see <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/backup-notifications.html\">Notification options with Backup</a>.</p>"}}}, "PutRestoreValidationResultInput": {"type": "structure", "required": ["RestoreJobId", "ValidationStatus"], "members": {"RestoreJobId": {"shape": "RestoreJobId", "documentation": "<p>This is a unique identifier of a restore job within Backup.</p>", "location": "uri", "locationName": "restoreJobId"}, "ValidationStatus": {"shape": "RestoreValidationStatus", "documentation": "<p>The status of your restore validation.</p>"}, "ValidationStatusMessage": {"shape": "string", "documentation": "<p>This is an optional message string you can input to describe the validation status for the restore test validation.</p>"}}}, "RecoveryPointByBackupVault": {"type": "structure", "members": {"RecoveryPointArn": {"shape": "ARN", "documentation": "<p>An Amazon Resource Name (ARN) that uniquely identifies a recovery point; for example, <code>arn:aws:backup:us-east-1:************:recovery-point:1EB3B5E7-9EB0-435A-A80B-108B488B0D45</code>.</p>"}, "BackupVaultName": {"shape": "BackupVaultName", "documentation": "<p>The name of a logical container where backups are stored. Backup vaults are identified by names that are unique to the account used to create them and the Amazon Web Services Region where they are created.</p>"}, "BackupVaultArn": {"shape": "ARN", "documentation": "<p>An ARN that uniquely identifies a backup vault; for example, <code>arn:aws:backup:us-east-1:************:backup-vault:aBackupVault</code>.</p>"}, "SourceBackupVaultArn": {"shape": "ARN", "documentation": "<p>The backup vault where the recovery point was originally copied from. If the recovery point is restored to the same account this value will be <code>null</code>.</p>"}, "ResourceArn": {"shape": "ARN", "documentation": "<p>An ARN that uniquely identifies a resource. The format of the ARN depends on the resource type.</p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>The type of Amazon Web Services resource saved as a recovery point; for example, an Amazon Elastic Block Store (Amazon EBS) volume or an Amazon Relational Database Service (Amazon RDS) database. For Windows Volume Shadow Copy Service (VSS) backups, the only supported resource type is Amazon EC2.</p>"}, "CreatedBy": {"shape": "RecoveryPointCreator", "documentation": "<p>Contains identifying information about the creation of a recovery point, including the <code>BackupPlanArn</code>, <code>BackupPlanId</code>, <code>BackupPlanVersion</code>, and <code>BackupRuleId</code> of the backup plan that is used to create it.</p>"}, "IamRoleArn": {"shape": "IAMRoleArn", "documentation": "<p>Specifies the IAM role ARN used to create the target recovery point; for example, <code>arn:aws:iam::************:role/S3Access</code>.</p>"}, "Status": {"shape": "RecoveryPointStatus", "documentation": "<p>A status code specifying the state of the recovery point.</p>"}, "StatusMessage": {"shape": "string", "documentation": "<p>A message explaining the current status of the recovery point.</p>"}, "CreationDate": {"shape": "timestamp", "documentation": "<p>The date and time a recovery point is created, in Unix format and Coordinated Universal Time (UTC). The value of <code>CreationDate</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "InitiationDate": {"shape": "timestamp", "documentation": "<p>The date and time when the backup job that created this recovery point was initiated, in Unix format and Coordinated Universal Time (UTC).</p>"}, "CompletionDate": {"shape": "timestamp", "documentation": "<p>The date and time a job to restore a recovery point is completed, in Unix format and Coordinated Universal Time (UTC). The value of <code>CompletionDate</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "BackupSizeInBytes": {"shape": "<PERSON>", "documentation": "<p>The size, in bytes, of a backup.</p>"}, "CalculatedLifecycle": {"shape": "CalculatedLifecycle", "documentation": "<p>A <code>CalculatedLifecycle</code> object containing <code>DeleteAt</code> and <code>MoveToColdStorageAt</code> timestamps.</p>"}, "Lifecycle": {"shape": "Lifecycle", "documentation": "<p>The lifecycle defines when a protected resource is transitioned to cold storage and when it expires. Backup transitions and expires backups automatically according to the lifecycle that you define. </p> <p>Backups transitioned to cold storage must be stored in cold storage for a minimum of 90 days. Therefore, the “retention” setting must be 90 days greater than the “transition to cold after days” setting. The “transition to cold after days” setting cannot be changed after a backup has been transitioned to cold. </p> <p>Resource types that can transition to cold storage are listed in the <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/backup-feature-availability.html#features-by-resource\">Feature availability by resource</a> table. Backup ignores this expression for other resource types.</p>"}, "EncryptionKeyArn": {"shape": "ARN", "documentation": "<p>The server-side encryption key that is used to protect your backups; for example, <code>arn:aws:kms:us-west-2:************:key/1234abcd-12ab-34cd-56ef-1234567890ab</code>.</p>"}, "IsEncrypted": {"shape": "boolean", "documentation": "<p>A Boolean value that is returned as <code>TRUE</code> if the specified recovery point is encrypted, or <code>FALSE</code> if the recovery point is not encrypted.</p>"}, "LastRestoreTime": {"shape": "timestamp", "documentation": "<p>The date and time a recovery point was last restored, in Unix format and Coordinated Universal Time (UTC). The value of <code>LastRestoreTime</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "ParentRecoveryPointArn": {"shape": "ARN", "documentation": "<p>The Amazon Resource Name (ARN) of the parent (composite) recovery point.</p>"}, "CompositeMemberIdentifier": {"shape": "string", "documentation": "<p>The identifier of a resource within a composite group, such as nested (child) recovery point belonging to a composite (parent) stack. The ID is transferred from the <a href=\"https://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/resources-section-structure.html#resources-section-structure-syntax\"> logical ID</a> within a stack.</p>"}, "IsParent": {"shape": "boolean", "documentation": "<p>This is a boolean value indicating this is a parent (composite) recovery point.</p>"}, "ResourceName": {"shape": "string", "documentation": "<p>The non-unique name of the resource that belongs to the specified backup.</p>"}, "VaultType": {"shape": "VaultType", "documentation": "<p>The type of vault in which the described recovery point is stored.</p>"}, "IndexStatus": {"shape": "IndexStatus", "documentation": "<p>This is the current status for the backup index associated with the specified recovery point.</p> <p>Statuses are: <code>PENDING</code> | <code>ACTIVE</code> | <code>FAILED</code> | <code>DELETING</code> </p> <p>A recovery point with an index that has the status of <code>ACTIVE</code> can be included in a search.</p>"}, "IndexStatusMessage": {"shape": "string", "documentation": "<p>A string in the form of a detailed message explaining the status of a backup index associated with the recovery point.</p>"}}, "documentation": "<p>Contains detailed information about the recovery points stored in a backup vault.</p>"}, "RecoveryPointByBackupVaultList": {"type": "list", "member": {"shape": "RecoveryPointByBackupVault"}}, "RecoveryPointByResource": {"type": "structure", "members": {"RecoveryPointArn": {"shape": "ARN", "documentation": "<p>An Amazon Resource Name (ARN) that uniquely identifies a recovery point; for example, <code>arn:aws:backup:us-east-1:************:recovery-point:1EB3B5E7-9EB0-435A-A80B-108B488B0D45</code>.</p>"}, "CreationDate": {"shape": "timestamp", "documentation": "<p>The date and time a recovery point is created, in Unix format and Coordinated Universal Time (UTC). The value of <code>CreationDate</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "Status": {"shape": "RecoveryPointStatus", "documentation": "<p>A status code specifying the state of the recovery point.</p>"}, "StatusMessage": {"shape": "string", "documentation": "<p>A message explaining the current status of the recovery point.</p>"}, "EncryptionKeyArn": {"shape": "ARN", "documentation": "<p>The server-side encryption key that is used to protect your backups; for example, <code>arn:aws:kms:us-west-2:************:key/1234abcd-12ab-34cd-56ef-1234567890ab</code>.</p>"}, "BackupSizeBytes": {"shape": "<PERSON>", "documentation": "<p>The size, in bytes, of a backup.</p>"}, "BackupVaultName": {"shape": "BackupVaultName", "documentation": "<p>The name of a logical container where backups are stored. Backup vaults are identified by names that are unique to the account used to create them and the Amazon Web Services Region where they are created.</p>"}, "IsParent": {"shape": "boolean", "documentation": "<p>This is a boolean value indicating this is a parent (composite) recovery point.</p>"}, "ParentRecoveryPointArn": {"shape": "ARN", "documentation": "<p>The Amazon Resource Name (ARN) of the parent (composite) recovery point.</p>"}, "ResourceName": {"shape": "string", "documentation": "<p>The non-unique name of the resource that belongs to the specified backup.</p>"}, "VaultType": {"shape": "VaultType", "documentation": "<p>The type of vault in which the described recovery point is stored.</p>"}, "IndexStatus": {"shape": "IndexStatus", "documentation": "<p>This is the current status for the backup index associated with the specified recovery point.</p> <p>Statuses are: <code>PENDING</code> | <code>ACTIVE</code> | <code>FAILED</code> | <code>DELETING</code> </p> <p>A recovery point with an index that has the status of <code>ACTIVE</code> can be included in a search.</p>"}, "IndexStatusMessage": {"shape": "string", "documentation": "<p>A string in the form of a detailed message explaining the status of a backup index associated with the recovery point.</p>"}}, "documentation": "<p>Contains detailed information about a saved recovery point.</p>"}, "RecoveryPointByResourceList": {"type": "list", "member": {"shape": "RecoveryPointByResource"}}, "RecoveryPointCreator": {"type": "structure", "members": {"BackupPlanId": {"shape": "string", "documentation": "<p>Uniquely identifies a backup plan.</p>"}, "BackupPlanArn": {"shape": "ARN", "documentation": "<p>An Amazon Resource Name (ARN) that uniquely identifies a backup plan; for example, <code>arn:aws:backup:us-east-1:************:plan:8F81F553-3A74-4A3F-B93D-B3360DC80C50</code>.</p>"}, "BackupPlanVersion": {"shape": "string", "documentation": "<p>Version IDs are unique, randomly generated, Unicode, UTF-8 encoded strings that are at most 1,024 bytes long. They cannot be edited.</p>"}, "BackupRuleId": {"shape": "string", "documentation": "<p>Uniquely identifies a rule used to schedule the backup of a selection of resources.</p>"}}, "documentation": "<p>Contains information about the backup plan and rule that <PERSON><PERSON> used to initiate the recovery point backup.</p>"}, "RecoveryPointMember": {"type": "structure", "members": {"RecoveryPointArn": {"shape": "ARN", "documentation": "<p>The Amazon Resource Name (ARN) of the parent (composite) recovery point.</p>"}, "ResourceArn": {"shape": "ARN", "documentation": "<p>The Amazon Resource Name (ARN) that uniquely identifies a saved resource.</p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>The Amazon Web Services resource type that is saved as a recovery point.</p>"}, "BackupVaultName": {"shape": "BackupVaultName", "documentation": "<p>The name of the backup vault (the logical container in which backups are stored).</p>"}}, "documentation": "<p>This is a recovery point which is a child (nested) recovery point of a parent (composite) recovery point. These recovery points can be disassociated from their parent (composite) recovery point, in which case they will no longer be a member.</p>"}, "RecoveryPointSelection": {"type": "structure", "members": {"VaultNames": {"shape": "VaultNames", "documentation": "<p>These are the names of the vaults in which the selected recovery points are contained.</p>"}, "ResourceIdentifiers": {"shape": "ResourceIdentifiers", "documentation": "<p>These are the resources included in the resource selection (including type of resources and vaults).</p>"}, "DateRange": {"shape": "DateRange"}}, "documentation": "<p>This specifies criteria to assign a set of resources, such as resource types or backup vaults.</p>"}, "RecoveryPointStatus": {"type": "string", "enum": ["COMPLETED", "PARTIAL", "DELETING", "EXPIRED", "AVAILABLE", "STOPPED", "CREATING"]}, "RecoveryPointsList": {"type": "list", "member": {"shape": "RecoveryPointMember"}}, "Region": {"type": "string"}, "ReportDeliveryChannel": {"type": "structure", "required": ["S3BucketName"], "members": {"S3BucketName": {"shape": "string", "documentation": "<p>The unique name of the S3 bucket that receives your reports.</p>"}, "S3KeyPrefix": {"shape": "string", "documentation": "<p>The prefix for where Backup Audit Manager delivers your reports to Amazon S3. The prefix is this part of the following path: s3://your-bucket-name/<code>prefix</code>/Backup/us-west-2/year/month/day/report-name. If not specified, there is no prefix.</p>"}, "Formats": {"shape": "FormatList", "documentation": "<p>The format of your reports: <code>CSV</code>, <code>JSON</code>, or both. If not specified, the default format is <code>CSV</code>.</p>"}}, "documentation": "<p>Contains information from your report plan about where to deliver your reports, specifically your Amazon S3 bucket name, S3 key prefix, and the formats of your reports.</p>"}, "ReportDestination": {"type": "structure", "members": {"S3BucketName": {"shape": "string", "documentation": "<p>The unique name of the Amazon S3 bucket that receives your reports.</p>"}, "S3Keys": {"shape": "stringList", "documentation": "<p>The object key that uniquely identifies your reports in your S3 bucket.</p>"}}, "documentation": "<p>Contains information from your report job about your report destination.</p>"}, "ReportJob": {"type": "structure", "members": {"ReportJobId": {"shape": "ReportJobId", "documentation": "<p>The identifier for a report job. A unique, randomly generated, Unicode, UTF-8 encoded string that is at most 1,024 bytes long. Report job IDs cannot be edited.</p>"}, "ReportPlanArn": {"shape": "ARN", "documentation": "<p>An Amazon Resource Name (ARN) that uniquely identifies a resource. The format of the ARN depends on the resource type.</p>"}, "ReportTemplate": {"shape": "string", "documentation": "<p>Identifies the report template for the report. Reports are built using a report template. The report templates are: </p> <p> <code>RESOURCE_COMPLIANCE_REPORT | CONTROL_COMPLIANCE_REPORT | BACKUP_JOB_REPORT | COPY_JOB_REPORT | RESTORE_JOB_REPORT</code> </p>"}, "CreationTime": {"shape": "timestamp", "documentation": "<p>The date and time that a report job is created, in Unix format and Coordinated Universal Time (UTC). The value of <code>CreationTime</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "CompletionTime": {"shape": "timestamp", "documentation": "<p>The date and time that a report job is completed, in Unix format and Coordinated Universal Time (UTC). The value of <code>CompletionTime</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "Status": {"shape": "string", "documentation": "<p>The status of a report job. The statuses are:</p> <p> <code>CREATED | RUNNING | COMPLETED | FAILED</code> </p> <p> <code>COMPLETED</code> means that the report is available for your review at your designated destination. If the status is <code>FAILED</code>, review the <code>StatusMessage</code> for the reason.</p>"}, "StatusMessage": {"shape": "string", "documentation": "<p>A message explaining the status of the report job.</p>"}, "ReportDestination": {"shape": "ReportDestination", "documentation": "<p>The S3 bucket name and S3 keys for the destination where the report job publishes the report.</p>"}}, "documentation": "<p>Contains detailed information about a report job. A report job compiles a report based on a report plan and publishes it to Amazon S3.</p>"}, "ReportJobId": {"type": "string"}, "ReportJobList": {"type": "list", "member": {"shape": "ReportJob"}}, "ReportPlan": {"type": "structure", "members": {"ReportPlanArn": {"shape": "ARN", "documentation": "<p>An Amazon Resource Name (ARN) that uniquely identifies a resource. The format of the ARN depends on the resource type.</p>"}, "ReportPlanName": {"shape": "ReportPlanName", "documentation": "<p>The unique name of the report plan. This name is between 1 and 256 characters starting with a letter, and consisting of letters (a-z, A-Z), numbers (0-9), and underscores (_).</p>"}, "ReportPlanDescription": {"shape": "ReportPlanDescription", "documentation": "<p>An optional description of the report plan with a maximum 1,024 characters.</p>"}, "ReportSetting": {"shape": "ReportSetting", "documentation": "<p>Identifies the report template for the report. Reports are built using a report template. The report templates are:</p> <p> <code>RESOURCE_COMPLIANCE_REPORT | CONTROL_COMPLIANCE_REPORT | BACKUP_JOB_REPORT | COPY_JOB_REPORT | RESTORE_JOB_REPORT</code> </p> <p>If the report template is <code>RESOURCE_COMPLIANCE_REPORT</code> or <code>CONTROL_COMPLIANCE_REPORT</code>, this API resource also describes the report coverage by Amazon Web Services Regions and frameworks.</p>"}, "ReportDeliveryChannel": {"shape": "ReportDeliveryChannel", "documentation": "<p>Contains information about where and how to deliver your reports, specifically your Amazon S3 bucket name, S3 key prefix, and the formats of your reports.</p>"}, "DeploymentStatus": {"shape": "string", "documentation": "<p>The deployment status of a report plan. The statuses are:</p> <p> <code>CREATE_IN_PROGRESS | UPDATE_IN_PROGRESS | DELETE_IN_PROGRESS | COMPLETED</code> </p>"}, "CreationTime": {"shape": "timestamp", "documentation": "<p>The date and time that a report plan is created, in Unix format and Coordinated Universal Time (UTC). The value of <code>CreationTime</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "LastAttemptedExecutionTime": {"shape": "timestamp", "documentation": "<p>The date and time that a report job associated with this report plan last attempted to run, in Unix format and Coordinated Universal Time (UTC). The value of <code>LastAttemptedExecutionTime</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "LastSuccessfulExecutionTime": {"shape": "timestamp", "documentation": "<p>The date and time that a report job associated with this report plan last successfully ran, in Unix format and Coordinated Universal Time (UTC). The value of <code>LastSuccessfulExecutionTime</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}}, "documentation": "<p>Contains detailed information about a report plan.</p>"}, "ReportPlanDescription": {"type": "string", "max": 1024, "min": 0, "pattern": ".*\\S.*"}, "ReportPlanList": {"type": "list", "member": {"shape": "ReportPlan"}}, "ReportPlanName": {"type": "string", "max": 256, "min": 1, "pattern": "[a-zA-Z][_a-zA-Z0-9]*"}, "ReportSetting": {"type": "structure", "required": ["ReportTemplate"], "members": {"ReportTemplate": {"shape": "string", "documentation": "<p>Identifies the report template for the report. Reports are built using a report template. The report templates are:</p> <p> <code>RESOURCE_COMPLIANCE_REPORT | CONTROL_COMPLIANCE_REPORT | BACKUP_JOB_REPORT | COPY_JOB_REPORT | RESTORE_JOB_REPORT</code> </p>"}, "FrameworkArns": {"shape": "stringList", "documentation": "<p>The Amazon Resource Names (ARNs) of the frameworks a report covers.</p>"}, "NumberOfFrameworks": {"shape": "integer", "documentation": "<p>The number of frameworks a report covers.</p>"}, "Accounts": {"shape": "stringList", "documentation": "<p>These are the accounts to be included in the report.</p> <p>Use string value of <code>ROOT</code> to include all organizational units.</p>"}, "OrganizationUnits": {"shape": "stringList", "documentation": "<p>These are the Organizational Units to be included in the report.</p>"}, "Regions": {"shape": "stringList", "documentation": "<p>These are the Regions to be included in the report.</p> <p>Use the wildcard as the string value to include all Regions.</p>"}}, "documentation": "<p>Contains detailed information about a report setting.</p>"}, "RequesterComment": {"type": "string", "sensitive": true}, "ResourceArns": {"type": "list", "member": {"shape": "ARN"}}, "ResourceIdentifiers": {"type": "list", "member": {"shape": "string"}}, "ResourceNotFoundException": {"type": "structure", "members": {"Code": {"shape": "string"}, "Message": {"shape": "string"}, "Type": {"shape": "string", "documentation": "<p/>"}, "Context": {"shape": "string", "documentation": "<p/>"}}, "documentation": "<p>A resource that is required for the action doesn't exist.</p>", "exception": true}, "ResourceType": {"type": "string", "pattern": "^[a-zA-Z0-9\\-\\_\\.]{1,50}$"}, "ResourceTypeList": {"type": "list", "member": {"shape": "ARN"}}, "ResourceTypeManagementPreference": {"type": "map", "key": {"shape": "ResourceType"}, "value": {"shape": "IsEnabled"}}, "ResourceTypeOptInPreference": {"type": "map", "key": {"shape": "ResourceType"}, "value": {"shape": "IsEnabled"}}, "ResourceTypes": {"type": "list", "member": {"shape": "ResourceType"}}, "RestoreAccessBackupVaultList": {"type": "list", "member": {"shape": "RestoreAccessBackupVaultListMember"}}, "RestoreAccessBackupVaultListMember": {"type": "structure", "members": {"RestoreAccessBackupVaultArn": {"shape": "ARN", "documentation": "<p>The ARN of the restore access backup vault.</p>"}, "CreationDate": {"shape": "timestamp", "documentation": "<p>The date and time when the restore access backup vault was created.</p>"}, "ApprovalDate": {"shape": "timestamp", "documentation": "<p>The date and time when the restore access backup vault was approved.</p>"}, "VaultState": {"shape": "VaultState", "documentation": "<p>The current state of the restore access backup vault.</p>"}, "LatestRevokeRequest": {"shape": "LatestRevokeRequest", "documentation": "<p>Information about the latest request to revoke access to this backup vault.</p>"}}, "documentation": "<p>Contains information about a restore access backup vault.</p>"}, "RestoreDeletionStatus": {"type": "string", "enum": ["DELETING", "FAILED", "SUCCESSFUL"]}, "RestoreJobCreator": {"type": "structure", "members": {"RestoreTestingPlanArn": {"shape": "ARN", "documentation": "<p>An Amazon Resource Name (ARN) that uniquely identifies a restore testing plan.</p>"}}, "documentation": "<p>Contains information about the restore testing plan that <PERSON><PERSON> used to initiate the restore job.</p>"}, "RestoreJobId": {"type": "string"}, "RestoreJobState": {"type": "string", "enum": ["CREATED", "PENDING", "RUNNING", "ABORTED", "COMPLETED", "FAILED", "AGGREGATE_ALL", "ANY"]}, "RestoreJobStatus": {"type": "string", "enum": ["PENDING", "RUNNING", "COMPLETED", "ABORTED", "FAILED"]}, "RestoreJobSummary": {"type": "structure", "members": {"Region": {"shape": "Region", "documentation": "<p>The Amazon Web Services Regions within the job summary.</p>"}, "AccountId": {"shape": "AccountId", "documentation": "<p>The account ID that owns the jobs within the summary.</p>"}, "State": {"shape": "RestoreJobState", "documentation": "<p>This value is job count for jobs with the specified state.</p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>This value is the job count for the specified resource type. The request <code>GetSupportedResourceTypes</code> returns strings for supported resource types.</p>"}, "Count": {"shape": "integer", "documentation": "<p>The value as a number of jobs in a job summary.</p>"}, "StartTime": {"shape": "timestamp", "documentation": "<p>The value of time in number format of a job start time.</p> <p>This value is the time in Unix format, Coordinated Universal Time (UTC), and accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "EndTime": {"shape": "timestamp", "documentation": "<p>The value of time in number format of a job end time.</p> <p>This value is the time in Unix format, Coordinated Universal Time (UTC), and accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}}, "documentation": "<p>This is a summary of restore jobs created or running within the most recent 30 days.</p> <p>The returned summary may contain the following: Region, Account, State, ResourceType, MessageCategory, StartTime, EndTime, and Count of included jobs.</p>"}, "RestoreJobSummaryList": {"type": "list", "member": {"shape": "RestoreJobSummary"}}, "RestoreJobsList": {"type": "list", "member": {"shape": "RestoreJobsListMember"}}, "RestoreJobsListMember": {"type": "structure", "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The account ID that owns the restore job.</p>"}, "RestoreJobId": {"shape": "string", "documentation": "<p>Uniquely identifies the job that restores a recovery point.</p>"}, "RecoveryPointArn": {"shape": "ARN", "documentation": "<p>An ARN that uniquely identifies a recovery point; for example, <code>arn:aws:backup:us-east-1:************:recovery-point:1EB3B5E7-9EB0-435A-A80B-108B488B0D45</code>.</p>"}, "CreationDate": {"shape": "timestamp", "documentation": "<p>The date and time a restore job is created, in Unix format and Coordinated Universal Time (UTC). The value of <code>CreationDate</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "CompletionDate": {"shape": "timestamp", "documentation": "<p>The date and time a job to restore a recovery point is completed, in Unix format and Coordinated Universal Time (UTC). The value of <code>CompletionDate</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "Status": {"shape": "RestoreJobStatus", "documentation": "<p>A status code specifying the state of the job initiated by Backup to restore a recovery point.</p>"}, "StatusMessage": {"shape": "string", "documentation": "<p>A detailed message explaining the status of the job to restore a recovery point.</p>"}, "PercentDone": {"shape": "string", "documentation": "<p>Contains an estimated percentage complete of a job at the time the job status was queried.</p>"}, "BackupSizeInBytes": {"shape": "<PERSON>", "documentation": "<p>The size, in bytes, of the restored resource.</p>"}, "IamRoleArn": {"shape": "IAMRoleArn", "documentation": "<p>The IAM role ARN used to create the target recovery point; for example, <code>arn:aws:iam::************:role/S3Access</code>.</p>"}, "ExpectedCompletionTimeMinutes": {"shape": "<PERSON>", "documentation": "<p>The amount of time in minutes that a job restoring a recovery point is expected to take.</p>"}, "CreatedResourceArn": {"shape": "ARN", "documentation": "<p>An Amazon Resource Name (ARN) that uniquely identifies a resource. The format of the ARN depends on the resource type.</p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>The resource type of the listed restore jobs; for example, an Amazon Elastic Block Store (Amazon EBS) volume or an Amazon Relational Database Service (Amazon RDS) database. For Windows Volume Shadow Copy Service (VSS) backups, the only supported resource type is Amazon EC2.</p>"}, "RecoveryPointCreationDate": {"shape": "timestamp", "documentation": "<p>The date on which a recovery point was created.</p>"}, "CreatedBy": {"shape": "RestoreJobCreator", "documentation": "<p>Contains identifying information about the creation of a restore job.</p>"}, "ValidationStatus": {"shape": "RestoreValidationStatus", "documentation": "<p>The status of validation run on the indicated restore job.</p>"}, "ValidationStatusMessage": {"shape": "string", "documentation": "<p>This describes the status of validation run on the indicated restore job.</p>"}, "DeletionStatus": {"shape": "RestoreDeletionStatus", "documentation": "<p>This notes the status of the data generated by the restore test. The status may be <code>Deleting</code>, <code>Failed</code>, or <code>Successful</code>.</p>"}, "DeletionStatusMessage": {"shape": "string", "documentation": "<p>This describes the restore job deletion status.</p>"}}, "documentation": "<p>Contains metadata about a restore job.</p>"}, "RestoreTestingPlanForCreate": {"type": "structure", "required": ["RecoveryPointSelection", "RestoreTestingPlanName", "ScheduleExpression"], "members": {"RecoveryPointSelection": {"shape": "RestoreTestingRecoveryPointSelection", "documentation": "<p> <code>RecoveryPointSelection</code> has five parameters (three required and two optional). The values you specify determine which recovery point is included in the restore test. You must indicate with <code>Algorithm</code> if you want the latest recovery point within your <code>SelectionWindowDays</code> or if you want a random recovery point, and you must indicate through <code>IncludeVaults</code> from which vaults the recovery points can be chosen.</p> <p> <code>Algorithm</code> (<i>required</i>) Valid values: \"<code>LATEST_WITHIN_WINDOW</code>\" or \"<code>RANDOM_WITHIN_WINDOW</code>\".</p> <p> <code>Recovery point types</code> (<i>required</i>) Valid values: \"<code>SNAPSHOT</code>\" and/or \"<code>CONTINUOUS</code>\". Include <code>SNAPSHOT</code> to restore only snapshot recovery points; include <code>CONTINUOUS</code> to restore continuous recovery points (point in time restore / PITR); use both to restore either a snapshot or a continuous recovery point. The recovery point will be determined by the value for <code>Algorithm</code>.</p> <p> <code>IncludeVaults</code> (<i>required</i>). You must include one or more backup vaults. Use the wildcard [\"*\"] or specific ARNs.</p> <p> <code>SelectionWindowDays</code> (<i>optional</i>) Value must be an integer (in days) from 1 to 365. If not included, the value defaults to <code>30</code>.</p> <p> <code>ExcludeVaults</code> (<i>optional</i>). You can choose to input one or more specific backup vault ARNs to exclude those vaults' contents from restore eligibility. Or, you can include a list of selectors. If this parameter and its value are not included, it defaults to empty list.</p>"}, "RestoreTestingPlanName": {"shape": "String", "documentation": "<p>The RestoreTestingPlanName is a unique string that is the name of the restore testing plan. This cannot be changed after creation, and it must consist of only alphanumeric characters and underscores.</p>"}, "ScheduleExpression": {"shape": "String", "documentation": "<p>A CRON expression in specified timezone when a restore testing plan is executed. When no CRON expression is provided, Backup will use the default expression <code>cron(0 5 ? * * *)</code>.</p>"}, "ScheduleExpressionTimezone": {"shape": "String", "documentation": "<p>Optional. This is the timezone in which the schedule expression is set. By default, ScheduleExpressions are in UTC. You can modify this to a specified timezone.</p>"}, "StartWindowHours": {"shape": "integer", "documentation": "<p>Defaults to 24 hours.</p> <p>A value in hours after a restore test is scheduled before a job will be canceled if it doesn't start successfully. This value is optional. If this value is included, this parameter has a maximum value of 168 hours (one week).</p>"}}, "documentation": "<p>This contains metadata about a restore testing plan.</p>"}, "RestoreTestingPlanForGet": {"type": "structure", "required": ["CreationTime", "RecoveryPointSelection", "RestoreTestingPlanArn", "RestoreTestingPlanName", "ScheduleExpression"], "members": {"CreationTime": {"shape": "Timestamp", "documentation": "<p>The date and time that a restore testing plan was created, in Unix format and Coordinated Universal Time (UTC). The value of <code>CreationTime</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "CreatorRequestId": {"shape": "String", "documentation": "<p>This identifies the request and allows failed requests to be retried without the risk of running the operation twice. If the request includes a <code>CreatorRequestId</code> that matches an existing backup plan, that plan is returned. This parameter is optional.</p> <p>If used, this parameter must contain 1 to 50 alphanumeric or '-_.' characters.</p>"}, "LastExecutionTime": {"shape": "Timestamp", "documentation": "<p>The last time a restore test was run with the specified restore testing plan. A date and time, in Unix format and Coordinated Universal Time (UTC). The value of <code>LastExecutionDate</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "LastUpdateTime": {"shape": "Timestamp", "documentation": "<p>The date and time that the restore testing plan was updated. This update is in Unix format and Coordinated Universal Time (UTC). The value of <code>LastUpdateTime</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "RecoveryPointSelection": {"shape": "RestoreTestingRecoveryPointSelection", "documentation": "<p>The specified criteria to assign a set of resources, such as recovery point types or backup vaults.</p>"}, "RestoreTestingPlanArn": {"shape": "String", "documentation": "<p>An Amazon Resource Name (ARN) that uniquely identifies a restore testing plan.</p>"}, "RestoreTestingPlanName": {"shape": "String", "documentation": "<p>The restore testing plan name.</p>"}, "ScheduleExpression": {"shape": "String", "documentation": "<p>A CRON expression in specified timezone when a restore testing plan is executed. When no CRON expression is provided, Backup will use the default expression <code>cron(0 5 ? * * *)</code>.</p>"}, "ScheduleExpressionTimezone": {"shape": "String", "documentation": "<p>Optional. This is the timezone in which the schedule expression is set. By default, ScheduleExpressions are in UTC. You can modify this to a specified timezone.</p>"}, "StartWindowHours": {"shape": "integer", "documentation": "<p>Defaults to 24 hours.</p> <p>A value in hours after a restore test is scheduled before a job will be canceled if it doesn't start successfully. This value is optional. If this value is included, this parameter has a maximum value of 168 hours (one week).</p>"}}, "documentation": "<p>This contains metadata about a restore testing plan.</p>"}, "RestoreTestingPlanForList": {"type": "structure", "required": ["CreationTime", "RestoreTestingPlanArn", "RestoreTestingPlanName", "ScheduleExpression"], "members": {"CreationTime": {"shape": "Timestamp", "documentation": "<p>The date and time that a restore testing plan was created, in Unix format and Coordinated Universal Time (UTC). The value of <code>CreationTime</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "LastExecutionTime": {"shape": "Timestamp", "documentation": "<p>The last time a restore test was run with the specified restore testing plan. A date and time, in Unix format and Coordinated Universal Time (UTC). The value of <code>LastExecutionDate</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "LastUpdateTime": {"shape": "Timestamp", "documentation": "<p>The date and time that the restore testing plan was updated. This update is in Unix format and Coordinated Universal Time (UTC). The value of <code>LastUpdateTime</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "RestoreTestingPlanArn": {"shape": "String", "documentation": "<p>An Amazon Resource Name (ARN) that uniquely identifiesa restore testing plan.</p>"}, "RestoreTestingPlanName": {"shape": "String", "documentation": "<p>The restore testing plan name.</p>"}, "ScheduleExpression": {"shape": "String", "documentation": "<p>A CRON expression in specified timezone when a restore testing plan is executed. When no CRON expression is provided, Backup will use the default expression <code>cron(0 5 ? * * *)</code>.</p>"}, "ScheduleExpressionTimezone": {"shape": "String", "documentation": "<p>Optional. This is the timezone in which the schedule expression is set. By default, ScheduleExpressions are in UTC. You can modify this to a specified timezone.</p>"}, "StartWindowHours": {"shape": "integer", "documentation": "<p>Defaults to 24 hours.</p> <p>A value in hours after a restore test is scheduled before a job will be canceled if it doesn't start successfully. This value is optional. If this value is included, this parameter has a maximum value of 168 hours (one week).</p>"}}, "documentation": "<p>This contains metadata about a restore testing plan.</p>"}, "RestoreTestingPlanForUpdate": {"type": "structure", "members": {"RecoveryPointSelection": {"shape": "RestoreTestingRecoveryPointSelection", "documentation": "<p>Required: <code>Algorithm</code>; <code>RecoveryPointTypes</code>; <code>IncludeVaults</code> (<i>one or more</i>).</p> <p>Optional: <i>SelectionWindowDays</i> (<i>'30' if not specified</i>); <code>ExcludeVaults</code> (defaults to empty list if not listed).</p>"}, "ScheduleExpression": {"shape": "String", "documentation": "<p>A CRON expression in specified timezone when a restore testing plan is executed. When no CRON expression is provided, Backup will use the default expression <code>cron(0 5 ? * * *)</code>.</p>"}, "ScheduleExpressionTimezone": {"shape": "String", "documentation": "<p>Optional. This is the timezone in which the schedule expression is set. By default, ScheduleExpressions are in UTC. You can modify this to a specified timezone.</p>"}, "StartWindowHours": {"shape": "integer", "documentation": "<p>Defaults to 24 hours.</p> <p>A value in hours after a restore test is scheduled before a job will be canceled if it doesn't start successfully. This value is optional. If this value is included, this parameter has a maximum value of 168 hours (one week).</p>"}}, "documentation": "<p>This contains metadata about a restore testing plan.</p>"}, "RestoreTestingPlans": {"type": "list", "member": {"shape": "RestoreTestingPlanForList"}}, "RestoreTestingRecoveryPointSelection": {"type": "structure", "members": {"Algorithm": {"shape": "RestoreTestingRecoveryPointSelectionAlgorithm", "documentation": "<p>Acceptable values include \"LATEST_WITHIN_WINDOW\" or \"RANDOM_WITHIN_WINDOW\"</p>"}, "ExcludeVaults": {"shape": "stringList", "documentation": "<p>Accepted values include specific ARNs or list of selectors. Defaults to empty list if not listed.</p>"}, "IncludeVaults": {"shape": "stringList", "documentation": "<p>Accepted values include wildcard [\"*\"] or by specific ARNs or ARN wilcard replacement [\"arn:aws:backup:us-west-2:************:backup-vault:asdf\", ...] [\"arn:aws:backup:*:*:backup-vault:asdf-*\", ...]</p>"}, "RecoveryPointTypes": {"shape": "RestoreTestingRecoveryPointTypeList", "documentation": "<p>These are the types of recovery points.</p> <p>Include <code>SNAPSHOT</code> to restore only snapshot recovery points; include <code>CONTINUOUS</code> to restore continuous recovery points (point in time restore / PITR); use both to restore either a snapshot or a continuous recovery point. The recovery point will be determined by the value for <code>Algorithm</code>.</p>"}, "SelectionWindowDays": {"shape": "integer", "documentation": "<p>Accepted values are integers from 1 to 365.</p>"}}, "documentation": "<p> <code>RecoveryPointSelection</code> has five parameters (three required and two optional). The values you specify determine which recovery point is included in the restore test. You must indicate with <code>Algorithm</code> if you want the latest recovery point within your <code>SelectionWindowDays</code> or if you want a random recovery point, and you must indicate through <code>IncludeVaults</code> from which vaults the recovery points can be chosen.</p> <p> <code>Algorithm</code> (<i>required</i>) Valid values: \"<code>LATEST_WITHIN_WINDOW</code>\" or \"<code>RANDOM_WITHIN_WINDOW</code>\".</p> <p> <code>Recovery point types</code> (<i>required</i>) Valid values: \"<code>SNAPSHOT</code>\" and/or \"<code>CONTINUOUS</code>\". Include <code>SNAPSHOT</code> to restore only snapshot recovery points; include <code>CONTINUOUS</code> to restore continuous recovery points (point in time restore / PITR); use both to restore either a snapshot or a continuous recovery point. The recovery point will be determined by the value for <code>Algorithm</code>.</p> <p> <code>IncludeVaults</code> (<i>required</i>). You must include one or more backup vaults. Use the wildcard [\"*\"] or specific ARNs.</p> <p> <code>SelectionWindowDays</code> (<i>optional</i>) Value must be an integer (in days) from 1 to 365. If not included, the value defaults to <code>30</code>.</p> <p> <code>ExcludeVaults</code> (<i>optional</i>). You can choose to input one or more specific backup vault ARNs to exclude those vaults' contents from restore eligibility. Or, you can include a list of selectors. If this parameter and its value are not included, it defaults to empty list.</p>"}, "RestoreTestingRecoveryPointSelectionAlgorithm": {"type": "string", "enum": ["LATEST_WITHIN_WINDOW", "RANDOM_WITHIN_WINDOW"]}, "RestoreTestingRecoveryPointType": {"type": "string", "enum": ["CONTINUOUS", "SNAPSHOT"]}, "RestoreTestingRecoveryPointTypeList": {"type": "list", "member": {"shape": "RestoreTestingRecoveryPointType"}}, "RestoreTestingSelectionForCreate": {"type": "structure", "required": ["IamRoleArn", "ProtectedResourceType", "RestoreTestingSelectionName"], "members": {"IamRoleArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role that Backup uses to create the target resource; for example: <code>arn:aws:iam::************:role/S3Access</code>. </p>"}, "ProtectedResourceArns": {"shape": "stringList", "documentation": "<p>Each protected resource can be filtered by its specific ARNs, such as <code>ProtectedResourceArns: [\"arn:aws:...\", \"arn:aws:...\"]</code> or by a wildcard: <code>ProtectedResourceArns: [\"*\"]</code>, but not both.</p>"}, "ProtectedResourceConditions": {"shape": "ProtectedResourceConditions", "documentation": "<p>If you have included the wildcard in ProtectedResourceArns, you can include resource conditions, such as <code>ProtectedResourceConditions: { StringEquals: [{ key: \"XXXX\", value: \"YYYY\" }]</code>.</p>"}, "ProtectedResourceType": {"shape": "String", "documentation": "<p>The type of Amazon Web Services resource included in a restore testing selection; for example, an Amazon EBS volume or an Amazon RDS database.</p> <p>Supported resource types accepted include:</p> <ul> <li> <p> <code>Aurora</code> for Amazon Aurora</p> </li> <li> <p> <code>DocumentDB</code> for Amazon DocumentDB (with MongoDB compatibility)</p> </li> <li> <p> <code>DynamoDB</code> for Amazon DynamoDB</p> </li> <li> <p> <code>EBS</code> for Amazon Elastic Block Store</p> </li> <li> <p> <code>EC2</code> for Amazon Elastic Compute Cloud</p> </li> <li> <p> <code>EFS</code> for Amazon Elastic File System</p> </li> <li> <p> <code>FSx</code> for Amazon FSx</p> </li> <li> <p> <code>Neptune</code> for Amazon Neptune</p> </li> <li> <p> <code>RDS</code> for Amazon Relational Database Service</p> </li> <li> <p> <code>S3</code> for Amazon S3</p> </li> </ul>"}, "RestoreMetadataOverrides": {"shape": "SensitiveStringMap", "documentation": "<p>You can override certain restore metadata keys by including the parameter <code>RestoreMetadataOverrides</code> in the body of <code>RestoreTestingSelection</code>. Key values are not case sensitive.</p> <p>See the complete list of <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/restore-testing-inferred-metadata.html\">restore testing inferred metadata</a>.</p>"}, "RestoreTestingSelectionName": {"shape": "String", "documentation": "<p>The unique name of the restore testing selection that belongs to the related restore testing plan.</p>"}, "ValidationWindowHours": {"shape": "integer", "documentation": "<p>This is amount of hours (0 to 168) available to run a validation script on the data. The data will be deleted upon the completion of the validation script or the end of the specified retention period, whichever comes first.</p>"}}, "documentation": "<p>This contains metadata about a specific restore testing selection.</p> <p>ProtectedResourceType is required, such as Amazon EBS or Amazon EC2.</p> <p>This consists of <code>RestoreTestingSelectionName</code>, <code>ProtectedResourceType</code>, and one of the following:</p> <ul> <li> <p> <code>ProtectedResourceArns</code> </p> </li> <li> <p> <code>ProtectedResourceConditions</code> </p> </li> </ul> <p>Each protected resource type can have one single value.</p> <p>A restore testing selection can include a wildcard value (\"*\") for <code>ProtectedResourceArns</code> along with <code>ProtectedResourceConditions</code>. Alternatively, you can include up to 30 specific protected resource ARNs in <code>ProtectedResourceArns</code>.</p> <p> <code>ProtectedResourceConditions</code> examples include as <code>StringEquals</code> and <code>StringNotEquals</code>.</p>"}, "RestoreTestingSelectionForGet": {"type": "structure", "required": ["CreationTime", "IamRoleArn", "ProtectedResourceType", "RestoreTestingPlanName", "RestoreTestingSelectionName"], "members": {"CreationTime": {"shape": "Timestamp", "documentation": "<p>The date and time that a restore testing selection was created, in Unix format and Coordinated Universal Time (UTC). The value of <code>CreationTime</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 201812:11:30.087 AM.</p>"}, "CreatorRequestId": {"shape": "String", "documentation": "<p>This identifies the request and allows failed requests to be retried without the risk of running the operation twice. If the request includes a <code>CreatorRequestId</code> that matches an existing backup plan, that plan is returned. This parameter is optional.</p> <p>If used, this parameter must contain 1 to 50 alphanumeric or '-_.' characters.</p>"}, "IamRoleArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role that Backup uses to create the target resource; for example:<code>arn:aws:iam::************:role/S3Access</code>.</p>"}, "ProtectedResourceArns": {"shape": "stringList", "documentation": "<p>You can include specific ARNs, such as <code>ProtectedResourceArns: [\"arn:aws:...\", \"arn:aws:...\"]</code> or you can include a wildcard: <code>ProtectedResourceArns: [\"*\"]</code>, but not both.</p>"}, "ProtectedResourceConditions": {"shape": "ProtectedResourceConditions", "documentation": "<p>In a resource testing selection, this parameter filters by specific conditions such as <code>StringEquals</code> or <code>StringNotEquals</code>.</p>"}, "ProtectedResourceType": {"shape": "String", "documentation": "<p>The type of Amazon Web Services resource included in a resource testing selection; for example, an Amazon EBS volume or an Amazon RDS database.</p>"}, "RestoreMetadataOverrides": {"shape": "SensitiveStringMap", "documentation": "<p>You can override certain restore metadata keys by including the parameter <code>RestoreMetadataOverrides</code> in the body of <code>RestoreTestingSelection</code>. Key values are not case sensitive.</p> <p>See the complete list of <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/restore-testing-inferred-metadata.html\">restore testing inferred metadata</a>.</p>"}, "RestoreTestingPlanName": {"shape": "String", "documentation": "<p>The RestoreTestingPlanName is a unique string that is the name of the restore testing plan.</p>"}, "RestoreTestingSelectionName": {"shape": "String", "documentation": "<p>The unique name of the restore testing selection that belongs to the related restore testing plan.</p>"}, "ValidationWindowHours": {"shape": "integer", "documentation": "<p>This is amount of hours (1 to 168) available to run a validation script on the data. The data will be deleted upon the completion of the validation script or the end of the specified retention period, whichever comes first.</p>"}}, "documentation": "<p>This contains metadata about a restore testing selection.</p>"}, "RestoreTestingSelectionForList": {"type": "structure", "required": ["CreationTime", "IamRoleArn", "ProtectedResourceType", "RestoreTestingPlanName", "RestoreTestingSelectionName"], "members": {"CreationTime": {"shape": "Timestamp", "documentation": "<p>The date and time that a restore testing selection was created, in Unix format and Coordinated Universal Time (UTC). The value of <code>CreationTime</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26,2018 12:11:30.087 AM.</p>"}, "IamRoleArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role that Backup uses to create the target resource; for example: <code>arn:aws:iam::************:role/S3Access</code>.</p>"}, "ProtectedResourceType": {"shape": "String", "documentation": "<p>The type of Amazon Web Services resource included in a restore testing selection; for example, an Amazon EBS volume or an Amazon RDS database.</p>"}, "RestoreTestingPlanName": {"shape": "String", "documentation": "<p>Unique string that is the name of the restore testing plan.</p> <p>The name cannot be changed after creation. The name must consist of only alphanumeric characters and underscores. Maximum length is 50.</p>"}, "RestoreTestingSelectionName": {"shape": "String", "documentation": "<p>Unique name of a restore testing selection.</p>"}, "ValidationWindowHours": {"shape": "integer", "documentation": "<p>This value represents the time, in hours, data is retained after a restore test so that optional validation can be completed.</p> <p>Accepted value is an integer between 0 and 168 (the hourly equivalent of seven days).</p>"}}, "documentation": "<p>This contains metadata about a restore testing selection.</p>"}, "RestoreTestingSelectionForUpdate": {"type": "structure", "members": {"IamRoleArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role that Backup uses to create the target resource; for example: <code>arn:aws:iam::************:role/S3Access</code>.</p>"}, "ProtectedResourceArns": {"shape": "stringList", "documentation": "<p>You can include a list of specific ARNs, such as <code>ProtectedResourceArns: [\"arn:aws:...\", \"arn:aws:...\"]</code> or you can include a wildcard: <code>ProtectedResourceArns: [\"*\"]</code>, but not both.</p>"}, "ProtectedResourceConditions": {"shape": "ProtectedResourceConditions", "documentation": "<p>The conditions that you define for resources in your restore testing plan using tags.</p>"}, "RestoreMetadataOverrides": {"shape": "SensitiveStringMap", "documentation": "<p>You can override certain restore metadata keys by including the parameter <code>RestoreMetadataOverrides</code> in the body of <code>RestoreTestingSelection</code>. Key values are not case sensitive.</p> <p>See the complete list of <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/restore-testing-inferred-metadata.html\">restore testing inferred metadata</a>.</p>"}, "ValidationWindowHours": {"shape": "integer", "documentation": "<p>This value represents the time, in hours, data is retained after a restore test so that optional validation can be completed.</p> <p>Accepted value is an integer between 0 and 168 (the hourly equivalent of seven days).</p>"}}, "documentation": "<p>This contains metadata about a restore testing selection.</p>"}, "RestoreTestingSelections": {"type": "list", "member": {"shape": "RestoreTestingSelectionForList"}}, "RestoreValidationStatus": {"type": "string", "enum": ["FAILED", "SUCCESSFUL", "TIMED_OUT", "VALIDATING"]}, "RevokeRestoreAccessBackupVaultInput": {"type": "structure", "required": ["BackupVaultName", "RestoreAccessBackupVaultArn"], "members": {"BackupVaultName": {"shape": "BackupVaultName", "documentation": "<p>The name of the source backup vault associated with the restore access backup vault to be revoked.</p>", "location": "uri", "locationName": "backup<PERSON><PERSON><PERSON><PERSON>"}, "RestoreAccessBackupVaultArn": {"shape": "ARN", "documentation": "<p>The ARN of the restore access backup vault to revoke.</p>", "location": "uri", "locationName": "restoreAccessBackupVaultArn"}, "RequesterComment": {"shape": "RequesterComment", "documentation": "<p>A comment explaining the reason for revoking access to the restore access backup vault.</p>", "location": "querystring", "locationName": "requesterComment"}}}, "SensitiveStringMap": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "String"}, "sensitive": true}, "ServiceUnavailableException": {"type": "structure", "members": {"Code": {"shape": "string"}, "Message": {"shape": "string"}, "Type": {"shape": "string", "documentation": "<p/>"}, "Context": {"shape": "string", "documentation": "<p/>"}}, "documentation": "<p>The request failed due to a temporary failure of the server.</p>", "exception": true, "fault": true}, "StartBackupJobInput": {"type": "structure", "required": ["BackupVaultName", "ResourceArn", "IamRoleArn"], "members": {"BackupVaultName": {"shape": "BackupVaultName", "documentation": "<p>The name of a logical container where backups are stored. Backup vaults are identified by names that are unique to the account used to create them and the Amazon Web Services Region where they are created.</p>"}, "ResourceArn": {"shape": "ARN", "documentation": "<p>An Amazon Resource Name (ARN) that uniquely identifies a resource. The format of the ARN depends on the resource type.</p>"}, "IamRoleArn": {"shape": "IAMRoleArn", "documentation": "<p>Specifies the IAM role ARN used to create the target recovery point; for example, <code>arn:aws:iam::************:role/S3Access</code>.</p>"}, "IdempotencyToken": {"shape": "string", "documentation": "<p>A customer-chosen string that you can use to distinguish between otherwise identical calls to <code>StartBackupJob</code>. Retrying a successful request with the same idempotency token results in a success message with no action taken.</p>", "idempotencyToken": true}, "StartWindowMinutes": {"shape": "WindowMinutes", "documentation": "<p>A value in minutes after a backup is scheduled before a job will be canceled if it doesn't start successfully. This value is optional, and the default is 8 hours. If this value is included, it must be at least 60 minutes to avoid errors.</p> <p>This parameter has a maximum value of 100 years (52,560,000 minutes).</p> <p>During the start window, the backup job status remains in <code>CREATED</code> status until it has successfully begun or until the start window time has run out. If within the start window time Back<PERSON> receives an error that allows the job to be retried, <PERSON><PERSON> will automatically retry to begin the job at least every 10 minutes until the backup successfully begins (the job status changes to <code>RUNNING</code>) or until the job status changes to <code>EXPIRED</code> (which is expected to occur when the start window time is over).</p>"}, "CompleteWindowMinutes": {"shape": "WindowMinutes", "documentation": "<p>A value in minutes during which a successfully started backup must complete, or else Backup will cancel the job. This value is optional. This value begins counting down from when the backup was scheduled. It does not add additional time for <code>StartWindowMinutes</code>, or if the backup started later than scheduled.</p> <p>Like <code>StartWindowMinutes</code>, this parameter has a maximum value of 100 years (52,560,000 minutes).</p>"}, "Lifecycle": {"shape": "Lifecycle", "documentation": "<p>The lifecycle defines when a protected resource is transitioned to cold storage and when it expires. Backup will transition and expire backups automatically according to the lifecycle that you define. </p> <p>Backups transitioned to cold storage must be stored in cold storage for a minimum of 90 days. Therefore, the “retention” setting must be 90 days greater than the “transition to cold after days” setting. The “transition to cold after days” setting cannot be changed after a backup has been transitioned to cold. </p> <p>Resource types that can transition to cold storage are listed in the <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/backup-feature-availability.html#features-by-resource\">Feature availability by resource</a> table. Backup ignores this expression for other resource types.</p> <p>This parameter has a maximum value of 100 years (36,500 days).</p>"}, "RecoveryPointTags": {"shape": "Tags", "documentation": "<p>The tags to assign to the resources.</p>"}, "BackupOptions": {"shape": "BackupOptions", "documentation": "<p>The backup option for a selected resource. This option is only available for Windows Volume Shadow Copy Service (VSS) backup jobs.</p> <p>Valid values: Set to <code>\"WindowsVSS\":\"enabled\"</code> to enable the <code>WindowsVSS</code> backup option and create a Windows VSS backup. Set to <code>\"WindowsVSS\"\"disabled\"</code> to create a regular backup. The <code>WindowsVSS</code> option is not enabled by default.</p>"}, "Index": {"shape": "Index", "documentation": "<p>Include this parameter to enable index creation if your backup job has a resource type that supports backup indexes.</p> <p>Resource types that support backup indexes include:</p> <ul> <li> <p> <code>EBS</code> for Amazon Elastic Block Store</p> </li> <li> <p> <code>S3</code> for Amazon Simple Storage Service (Amazon S3)</p> </li> </ul> <p>Index can have 1 of 2 possible values, either <code>ENABLED</code> or <code>DISABLED</code>.</p> <p>To create a backup index for an eligible <code>ACTIVE</code> recovery point that does not yet have a backup index, set value to <code>ENABLED</code>.</p> <p>To delete a backup index, set value to <code>DISABLED</code>.</p>"}}}, "StartBackupJobOutput": {"type": "structure", "members": {"BackupJobId": {"shape": "string", "documentation": "<p>Uniquely identifies a request to Back<PERSON> to back up a resource.</p>"}, "RecoveryPointArn": {"shape": "ARN", "documentation": "<p> <i>Note: This field is only returned for Amazon EFS and Advanced DynamoDB resources.</i> </p> <p>An ARN that uniquely identifies a recovery point; for example, <code>arn:aws:backup:us-east-1:************:recovery-point:1EB3B5E7-9EB0-435A-A80B-108B488B0D45</code>.</p>"}, "CreationDate": {"shape": "timestamp", "documentation": "<p>The date and time that a backup job is created, in Unix format and Coordinated Universal Time (UTC). The value of <code>CreationDate</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "IsParent": {"shape": "boolean", "documentation": "<p>This is a returned boolean value indicating this is a parent (composite) backup job.</p>"}}}, "StartCopyJobInput": {"type": "structure", "required": ["RecoveryPointArn", "SourceBackupVaultName", "DestinationBackupVaultArn", "IamRoleArn"], "members": {"RecoveryPointArn": {"shape": "ARN", "documentation": "<p>An ARN that uniquely identifies a recovery point to use for the copy job; for example, arn:aws:backup:us-east-1:************:recovery-point:1EB3B5E7-9EB0-435A-A80B-108B488B0D45. </p>"}, "SourceBackupVaultName": {"shape": "BackupVaultName", "documentation": "<p>The name of a logical source container where backups are stored. Backup vaults are identified by names that are unique to the account used to create them and the Amazon Web Services Region where they are created.</p>"}, "DestinationBackupVaultArn": {"shape": "ARN", "documentation": "<p>An Amazon Resource Name (ARN) that uniquely identifies a destination backup vault to copy to; for example, <code>arn:aws:backup:us-east-1:************:backup-vault:aBackupVault</code>.</p>"}, "IamRoleArn": {"shape": "IAMRoleArn", "documentation": "<p>Specifies the IAM role ARN used to copy the target recovery point; for example, <code>arn:aws:iam::************:role/S3Access</code>.</p>"}, "IdempotencyToken": {"shape": "string", "documentation": "<p>A customer-chosen string that you can use to distinguish between otherwise identical calls to <code>StartCopyJob</code>. Retrying a successful request with the same idempotency token results in a success message with no action taken.</p>", "idempotencyToken": true}, "Lifecycle": {"shape": "Lifecycle"}}}, "StartCopyJobOutput": {"type": "structure", "members": {"CopyJobId": {"shape": "string", "documentation": "<p>Uniquely identifies a copy job.</p>"}, "CreationDate": {"shape": "timestamp", "documentation": "<p>The date and time that a copy job is created, in Unix format and Coordinated Universal Time (UTC). The value of <code>CreationDate</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "IsParent": {"shape": "boolean", "documentation": "<p>This is a returned boolean value indicating this is a parent (composite) copy job.</p>"}}}, "StartReportJobInput": {"type": "structure", "required": ["ReportPlanName"], "members": {"ReportPlanName": {"shape": "ReportPlanName", "documentation": "<p>The unique name of a report plan.</p>", "location": "uri", "locationName": "reportPlanName"}, "IdempotencyToken": {"shape": "string", "documentation": "<p>A customer-chosen string that you can use to distinguish between otherwise identical calls to <code>StartReportJobInput</code>. Retrying a successful request with the same idempotency token results in a success message with no action taken.</p>", "idempotencyToken": true}}}, "StartReportJobOutput": {"type": "structure", "members": {"ReportJobId": {"shape": "ReportJobId", "documentation": "<p>The identifier of the report job. A unique, randomly generated, Unicode, UTF-8 encoded string that is at most 1,024 bytes long. The report job ID cannot be edited.</p>"}}}, "StartRestoreJobInput": {"type": "structure", "required": ["RecoveryPointArn", "<PERSON><PERSON><PERSON>"], "members": {"RecoveryPointArn": {"shape": "ARN", "documentation": "<p>An ARN that uniquely identifies a recovery point; for example, <code>arn:aws:backup:us-east-1:************:recovery-point:1EB3B5E7-9EB0-435A-A80B-108B488B0D45</code>.</p>"}, "Metadata": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>A set of metadata key-value pairs.</p> <p>You can get configuration metadata about a resource at the time it was backed up by calling <code>GetRecoveryPointRestoreMetadata</code>. However, values in addition to those provided by <code>GetRecoveryPointRestoreMetadata</code> might be required to restore a resource. For example, you might need to provide a new resource name if the original already exists.</p> <p>For more information about the metadata for each resource, see the following:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/restoring-aur.html#aur-restore-cli\">Metadata for Amazon Aurora</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/restoring-docdb.html#docdb-restore-cli\">Metadata for Amazon DocumentDB</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/restore-application-stacks.html#restoring-cfn-cli\">Metadata for CloudFormation</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/restoring-dynamodb.html#ddb-restore-cli\">Metadata for Amazon DynamoDB</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/restoring-ebs.html#ebs-restore-cli\"> Metadata for Amazon EBS</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/restoring-ec2.html#restoring-ec2-cli\">Metadata for Amazon EC2</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/restoring-efs.html#efs-restore-cli\">Metadata for Amazon EFS</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/restoring-fsx.html#fsx-restore-cli\">Metadata for Amazon FSx</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/restoring-nep.html#nep-restore-cli\">Metadata for Amazon Neptune</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/restoring-rds.html#rds-restore-cli\">Metadata for Amazon RDS</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/redshift-restores.html#redshift-restore-api\">Metadata for Amazon Redshift</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/restoring-storage-gateway.html#restoring-sgw-cli\">Metadata for Storage Gateway</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/restoring-s3.html#s3-restore-cli\">Metadata for Amazon S3</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/timestream-restore.html#timestream-restore-api\">Metadata for Amazon Timestream</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/restoring-vm.html#vm-restore-cli\">Metadata for virtual machines</a> </p> </li> </ul>"}, "IamRoleArn": {"shape": "IAMRoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role that Backup uses to create the target resource; for example: <code>arn:aws:iam::************:role/S3Access</code>.</p>"}, "IdempotencyToken": {"shape": "string", "documentation": "<p>A customer-chosen string that you can use to distinguish between otherwise identical calls to <code>StartRestoreJob</code>. Retrying a successful request with the same idempotency token results in a success message with no action taken.</p>", "idempotencyToken": true}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>Starts a job to restore a recovery point for one of the following resources:</p> <ul> <li> <p> <code>Aurora</code> - Amazon Aurora</p> </li> <li> <p> <code>DocumentDB</code> - Amazon DocumentDB</p> </li> <li> <p> <code>CloudFormation</code> - CloudFormation</p> </li> <li> <p> <code>DynamoDB</code> - Amazon DynamoDB</p> </li> <li> <p> <code>EBS</code> - Amazon Elastic Block Store</p> </li> <li> <p> <code>EC2</code> - Amazon Elastic Compute Cloud</p> </li> <li> <p> <code>EFS</code> - Amazon Elastic File System</p> </li> <li> <p> <code>FSx</code> - Amazon FSx</p> </li> <li> <p> <code>Neptune</code> - Amazon Neptune</p> </li> <li> <p> <code>RDS</code> - Amazon Relational Database Service</p> </li> <li> <p> <code>Redshift</code> - Amazon Redshift</p> </li> <li> <p> <code>Storage Gateway</code> - Storage Gateway</p> </li> <li> <p> <code>S3</code> - Amazon Simple Storage Service</p> </li> <li> <p> <code>Timestream</code> - Amazon Timestream</p> </li> <li> <p> <code>VirtualMachine</code> - Virtual machines</p> </li> </ul>"}, "CopySourceTagsToRestoredResource": {"shape": "boolean", "documentation": "<p>This is an optional parameter. If this equals <code>True</code>, tags included in the backup will be copied to the restored resource.</p> <p>This can only be applied to backups created through Backup.</p>"}}}, "StartRestoreJobOutput": {"type": "structure", "members": {"RestoreJobId": {"shape": "RestoreJobId", "documentation": "<p>Uniquely identifies the job that restores a recovery point.</p>"}}}, "StopBackupJobInput": {"type": "structure", "required": ["BackupJobId"], "members": {"BackupJobId": {"shape": "string", "documentation": "<p>Uniquely identifies a request to Back<PERSON> to back up a resource.</p>", "location": "uri", "locationName": "backup<PERSON>ob<PERSON>d"}}}, "StorageClass": {"type": "string", "enum": ["WARM", "COLD", "DELETED"]}, "String": {"type": "string"}, "TagKey": {"type": "string"}, "TagKeyList": {"type": "list", "member": {"shape": "string"}, "sensitive": true}, "TagResourceInput": {"type": "structure", "required": ["ResourceArn", "Tags"], "members": {"ResourceArn": {"shape": "ARN", "documentation": "<p>The ARN that uniquely identifies the resource.</p>", "location": "uri", "locationName": "resourceArn"}, "Tags": {"shape": "Tags", "documentation": "<p>Key-value pairs that are used to help organize your resources. You can assign your own metadata to the resources you create. For clarity, this is the structure to assign tags: <code>[{\"Key\":\"string\",\"Value\":\"string\"}]</code>.</p>"}}}, "TagValue": {"type": "string"}, "Tags": {"type": "map", "key": {"shape": "TagKey"}, "value": {"shape": "TagValue"}, "sensitive": true}, "Timestamp": {"type": "timestamp"}, "Timezone": {"type": "string"}, "UntagResourceInput": {"type": "structure", "required": ["ResourceArn", "TagKeyList"], "members": {"ResourceArn": {"shape": "ARN", "documentation": "<p>An ARN that uniquely identifies a resource. The format of the ARN depends on the type of the tagged resource.</p> <p>ARNs that do not include <code>backup</code> are incompatible with tagging. <code>TagResource</code> and <code>UntagResource</code> with invalid ARNs will result in an error. Acceptable ARN content can include <code>arn:aws:backup:us-east</code>. Invalid ARN content may look like <code>arn:aws:ec2:us-east</code>.</p>", "location": "uri", "locationName": "resourceArn"}, "TagKeyList": {"shape": "TagKeyList", "documentation": "<p>The keys to identify which key-value tags to remove from a resource.</p>"}}}, "UpdateBackupPlanInput": {"type": "structure", "required": ["BackupPlanId", "BackupPlan"], "members": {"BackupPlanId": {"shape": "string", "documentation": "<p>The ID of the backup plan.</p>", "location": "uri", "locationName": "backupPlanId"}, "BackupPlan": {"shape": "BackupPlanInput", "documentation": "<p>The body of a backup plan. Includes a <code>BackupPlanName</code> and one or more sets of <code>Rules</code>.</p>"}}}, "UpdateBackupPlanOutput": {"type": "structure", "members": {"BackupPlanId": {"shape": "string", "documentation": "<p>Uniquely identifies a backup plan.</p>"}, "BackupPlanArn": {"shape": "ARN", "documentation": "<p>An Amazon Resource Name (ARN) that uniquely identifies a backup plan; for example, <code>arn:aws:backup:us-east-1:************:plan:8F81F553-3A74-4A3F-B93D-B3360DC80C50</code>.</p>"}, "CreationDate": {"shape": "timestamp", "documentation": "<p>The date and time a backup plan is created, in Unix format and Coordinated Universal Time (UTC). The value of <code>CreationDate</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}, "VersionId": {"shape": "string", "documentation": "<p>Unique, randomly generated, Unicode, UTF-8 encoded strings that are at most 1,024 bytes long. Version Ids cannot be edited.</p>"}, "AdvancedBackupSettings": {"shape": "AdvancedBackupSettings", "documentation": "<p>Contains a list of <code>BackupOptions</code> for each resource type.</p>"}}}, "UpdateFrameworkInput": {"type": "structure", "required": ["FrameworkName"], "members": {"FrameworkName": {"shape": "FrameworkName", "documentation": "<p>The unique name of a framework. This name is between 1 and 256 characters, starting with a letter, and consisting of letters (a-z, A-Z), numbers (0-9), and underscores (_).</p>", "location": "uri", "locationName": "frameworkName"}, "FrameworkDescription": {"shape": "FrameworkDescription", "documentation": "<p>An optional description of the framework with a maximum 1,024 characters.</p>"}, "FrameworkControls": {"shape": "FrameworkControls", "documentation": "<p>The controls that make up the framework. Each control in the list has a name, input parameters, and scope.</p>"}, "IdempotencyToken": {"shape": "string", "documentation": "<p>A customer-chosen string that you can use to distinguish between otherwise identical calls to <code>UpdateFrameworkInput</code>. Retrying a successful request with the same idempotency token results in a success message with no action taken.</p>", "idempotencyToken": true}}}, "UpdateFrameworkOutput": {"type": "structure", "members": {"FrameworkName": {"shape": "FrameworkName", "documentation": "<p>The unique name of a framework. This name is between 1 and 256 characters, starting with a letter, and consisting of letters (a-z, A-Z), numbers (0-9), and underscores (_).</p>"}, "FrameworkArn": {"shape": "ARN", "documentation": "<p>An Amazon Resource Name (ARN) that uniquely identifies a resource. The format of the ARN depends on the resource type.</p>"}, "CreationTime": {"shape": "timestamp", "documentation": "<p>The date and time that a framework is created, in ISO 8601 representation. The value of <code>CreationTime</code> is accurate to milliseconds. For example, 2020-07-10T15:00:00.000-08:00 represents the 10th of July 2020 at 3:00 PM 8 hours behind UTC.</p>"}}}, "UpdateGlobalSettingsInput": {"type": "structure", "members": {"GlobalSettings": {"shape": "GlobalSettings", "documentation": "<p>A value for <code>isCrossAccountBackupEnabled</code> and a Region. Example: <code>update-global-settings --global-settings isCrossAccountBackupEnabled=false --region us-west-2</code>.</p>"}}}, "UpdateRecoveryPointIndexSettingsInput": {"type": "structure", "required": ["BackupVaultName", "RecoveryPointArn", "Index"], "members": {"BackupVaultName": {"shape": "BackupVaultName", "documentation": "<p>The name of a logical container where backups are stored. Backup vaults are identified by names that are unique to the account used to create them and the Region where they are created.</p> <p>Accepted characters include lowercase letters, numbers, and hyphens.</p>", "location": "uri", "locationName": "backup<PERSON><PERSON><PERSON><PERSON>"}, "RecoveryPointArn": {"shape": "ARN", "documentation": "<p>An ARN that uniquely identifies a recovery point; for example, <code>arn:aws:backup:us-east-1:************:recovery-point:1EB3B5E7-9EB0-435A-A80B-108B488B0D45</code>.</p>", "location": "uri", "locationName": "recoveryPointArn"}, "IamRoleArn": {"shape": "IAMRoleArn", "documentation": "<p>This specifies the IAM role ARN used for this operation.</p> <p>For example, arn:aws:iam::************:role/S3Access</p>"}, "Index": {"shape": "Index", "documentation": "<p>Index can have 1 of 2 possible values, either <code>ENABLED</code> or <code>DISABLED</code>.</p> <p>To create a backup index for an eligible <code>ACTIVE</code> recovery point that does not yet have a backup index, set value to <code>ENABLED</code>.</p> <p>To delete a backup index, set value to <code>DISABLED</code>.</p>"}}}, "UpdateRecoveryPointIndexSettingsOutput": {"type": "structure", "members": {"BackupVaultName": {"shape": "BackupVaultName", "documentation": "<p>The name of a logical container where backups are stored. Backup vaults are identified by names that are unique to the account used to create them and the Region where they are created.</p>"}, "RecoveryPointArn": {"shape": "ARN", "documentation": "<p>An ARN that uniquely identifies a recovery point; for example, <code>arn:aws:backup:us-east-1:************:recovery-point:1EB3B5E7-9EB0-435A-A80B-108B488B0D45</code>.</p>"}, "IndexStatus": {"shape": "IndexStatus", "documentation": "<p>This is the current status for the backup index associated with the specified recovery point.</p> <p>Statuses are: <code>PENDING</code> | <code>ACTIVE</code> | <code>FAILED</code> | <code>DELETING</code> </p> <p>A recovery point with an index that has the status of <code>ACTIVE</code> can be included in a search.</p>"}, "Index": {"shape": "Index", "documentation": "<p>Index can have 1 of 2 possible values, either <code>ENABLED</code> or <code>DISABLED</code>.</p> <p>A value of <code>ENABLED</code> means a backup index for an eligible <code>ACTIVE</code> recovery point has been created.</p> <p>A value of <code>DISABLED</code> means a backup index was deleted.</p>"}}}, "UpdateRecoveryPointLifecycleInput": {"type": "structure", "required": ["BackupVaultName", "RecoveryPointArn"], "members": {"BackupVaultName": {"shape": "BackupVaultName", "documentation": "<p>The name of a logical container where backups are stored. Backup vaults are identified by names that are unique to the account used to create them and the Amazon Web Services Region where they are created.</p>", "location": "uri", "locationName": "backup<PERSON><PERSON><PERSON><PERSON>"}, "RecoveryPointArn": {"shape": "ARN", "documentation": "<p>An Amazon Resource Name (ARN) that uniquely identifies a recovery point; for example, <code>arn:aws:backup:us-east-1:************:recovery-point:1EB3B5E7-9EB0-435A-A80B-108B488B0D45</code>.</p>", "location": "uri", "locationName": "recoveryPointArn"}, "Lifecycle": {"shape": "Lifecycle", "documentation": "<p>The lifecycle defines when a protected resource is transitioned to cold storage and when it expires. Backup transitions and expires backups automatically according to the lifecycle that you define. </p> <p>Backups transitioned to cold storage must be stored in cold storage for a minimum of 90 days. Therefore, the “retention” setting must be 90 days greater than the “transition to cold after days” setting. The “transition to cold after days” setting cannot be changed after a backup has been transitioned to cold. </p>"}}}, "UpdateRecoveryPointLifecycleOutput": {"type": "structure", "members": {"BackupVaultArn": {"shape": "ARN", "documentation": "<p>An ARN that uniquely identifies a backup vault; for example, <code>arn:aws:backup:us-east-1:************:backup-vault:aBackupVault</code>.</p>"}, "RecoveryPointArn": {"shape": "ARN", "documentation": "<p>An Amazon Resource Name (ARN) that uniquely identifies a recovery point; for example, <code>arn:aws:backup:us-east-1:************:recovery-point:1EB3B5E7-9EB0-435A-A80B-108B488B0D45</code>.</p>"}, "Lifecycle": {"shape": "Lifecycle", "documentation": "<p>The lifecycle defines when a protected resource is transitioned to cold storage and when it expires. Backup transitions and expires backups automatically according to the lifecycle that you define.</p> <p>Backups transitioned to cold storage must be stored in cold storage for a minimum of 90 days. Therefore, the “retention” setting must be 90 days greater than the “transition to cold after days” setting. The “transition to cold after days” setting cannot be changed after a backup has been transitioned to cold.</p> <p>Resource types that can transition to cold storage are listed in the <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/backup-feature-availability.html#features-by-resource\">Feature availability by resource</a> table. Backup ignores this expression for other resource types.</p>"}, "CalculatedLifecycle": {"shape": "CalculatedLifecycle", "documentation": "<p>A <code>CalculatedLifecycle</code> object containing <code>DeleteAt</code> and <code>MoveToColdStorageAt</code> timestamps.</p>"}}}, "UpdateRegionSettingsInput": {"type": "structure", "members": {"ResourceTypeOptInPreference": {"shape": "ResourceTypeOptInPreference", "documentation": "<p>Updates the list of services along with the opt-in preferences for the Region.</p> <p>If resource assignments are only based on tags, then service opt-in settings are applied. If a resource type is explicitly assigned to a backup plan, such as Amazon S3, Amazon EC2, or Amazon RDS, it will be included in the backup even if the opt-in is not enabled for that particular service. If both a resource type and tags are specified in a resource assignment, the resource type specified in the backup plan takes priority over the tag condition. Service opt-in settings are disregarded in this situation.</p>"}, "ResourceTypeManagementPreference": {"shape": "ResourceTypeManagementPreference", "documentation": "<p>Enables or disables full Backup management of backups for a resource type. To enable full Backup management for DynamoDB along with <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/advanced-ddb-backup.html\"> Backup's advanced DynamoDB backup features</a>, follow the procedure to <a href=\"https://docs.aws.amazon.com/aws-backup/latest/devguide/advanced-ddb-backup.html#advanced-ddb-backup-enable-cli\"> enable advanced DynamoDB backup programmatically</a>.</p>"}}}, "UpdateReportPlanInput": {"type": "structure", "required": ["ReportPlanName"], "members": {"ReportPlanName": {"shape": "ReportPlanName", "documentation": "<p>The unique name of the report plan. This name is between 1 and 256 characters, starting with a letter, and consisting of letters (a-z, A-Z), numbers (0-9), and underscores (_).</p>", "location": "uri", "locationName": "reportPlanName"}, "ReportPlanDescription": {"shape": "ReportPlanDescription", "documentation": "<p>An optional description of the report plan with a maximum 1,024 characters.</p>"}, "ReportDeliveryChannel": {"shape": "ReportDeliveryChannel", "documentation": "<p>The information about where to deliver your reports, specifically your Amazon S3 bucket name, S3 key prefix, and the formats of your reports.</p>"}, "ReportSetting": {"shape": "ReportSetting", "documentation": "<p>The report template for the report. Reports are built using a report template. The report templates are:</p> <p> <code>RESOURCE_COMPLIANCE_REPORT | CONTROL_COMPLIANCE_REPORT | BACKUP_JOB_REPORT | COPY_JOB_REPORT | RESTORE_JOB_REPORT</code> </p> <p>If the report template is <code>RESOURCE_COMPLIANCE_REPORT</code> or <code>CONTROL_COMPLIANCE_REPORT</code>, this API resource also describes the report coverage by Amazon Web Services Regions and frameworks.</p>"}, "IdempotencyToken": {"shape": "string", "documentation": "<p>A customer-chosen string that you can use to distinguish between otherwise identical calls to <code>UpdateReportPlanInput</code>. Retrying a successful request with the same idempotency token results in a success message with no action taken.</p>", "idempotencyToken": true}}}, "UpdateReportPlanOutput": {"type": "structure", "members": {"ReportPlanName": {"shape": "ReportPlanName", "documentation": "<p>The unique name of the report plan.</p>"}, "ReportPlanArn": {"shape": "ARN", "documentation": "<p>An Amazon Resource Name (ARN) that uniquely identifies a resource. The format of the ARN depends on the resource type.</p>"}, "CreationTime": {"shape": "timestamp", "documentation": "<p>The date and time that a report plan is created, in Unix format and Coordinated Universal Time (UTC). The value of <code>CreationTime</code> is accurate to milliseconds. For example, the value **********.087 represents Friday, January 26, 2018 12:11:30.087 AM.</p>"}}}, "UpdateRestoreTestingPlanInput": {"type": "structure", "required": ["RestoreTestingPlan", "RestoreTestingPlanName"], "members": {"RestoreTestingPlan": {"shape": "RestoreTestingPlanForUpdate", "documentation": "<p>Specifies the body of a restore testing plan.</p>"}, "RestoreTestingPlanName": {"shape": "String", "documentation": "<p>The name of the restore testing plan name.</p>", "location": "uri", "locationName": "RestoreTestingPlanName"}}}, "UpdateRestoreTestingPlanOutput": {"type": "structure", "required": ["CreationTime", "RestoreTestingPlanArn", "RestoreTestingPlanName", "UpdateTime"], "members": {"CreationTime": {"shape": "Timestamp", "documentation": "<p>The time the resource testing plan was created.</p>"}, "RestoreTestingPlanArn": {"shape": "String", "documentation": "<p>Unique ARN (Amazon Resource Name) of the restore testing plan.</p>"}, "RestoreTestingPlanName": {"shape": "String", "documentation": "<p>The name cannot be changed after creation. The name consists of only alphanumeric characters and underscores. Maximum length is 50.</p>"}, "UpdateTime": {"shape": "Timestamp", "documentation": "<p>The time the update completed for the restore testing plan.</p>"}}}, "UpdateRestoreTestingSelectionInput": {"type": "structure", "required": ["RestoreTestingPlanName", "RestoreTestingSelection", "RestoreTestingSelectionName"], "members": {"RestoreTestingPlanName": {"shape": "String", "documentation": "<p>The restore testing plan name is required to update the indicated testing plan.</p>", "location": "uri", "locationName": "RestoreTestingPlanName"}, "RestoreTestingSelection": {"shape": "RestoreTestingSelectionForUpdate", "documentation": "<p>To update your restore testing selection, you can use either protected resource ARNs or conditions, but not both. That is, if your selection has <code>ProtectedResourceArns</code>, requesting an update with the parameter <code>ProtectedResourceConditions</code> will be unsuccessful.</p>"}, "RestoreTestingSelectionName": {"shape": "String", "documentation": "<p>The required restore testing selection name of the restore testing selection you wish to update.</p>", "location": "uri", "locationName": "RestoreTestingSelectionName"}}}, "UpdateRestoreTestingSelectionOutput": {"type": "structure", "required": ["CreationTime", "RestoreTestingPlanArn", "RestoreTestingPlanName", "RestoreTestingSelectionName", "UpdateTime"], "members": {"CreationTime": {"shape": "Timestamp", "documentation": "<p>The time the resource testing selection was updated successfully.</p>"}, "RestoreTestingPlanArn": {"shape": "String", "documentation": "<p>Unique string that is the name of the restore testing plan.</p>"}, "RestoreTestingPlanName": {"shape": "String", "documentation": "<p>The restore testing plan with which the updated restore testing selection is associated.</p>"}, "RestoreTestingSelectionName": {"shape": "String", "documentation": "<p>The returned restore testing selection name.</p>"}, "UpdateTime": {"shape": "Timestamp", "documentation": "<p>The time the update completed for the restore testing selection.</p>"}}}, "VaultNames": {"type": "list", "member": {"shape": "string"}}, "VaultState": {"type": "string", "enum": ["CREATING", "AVAILABLE", "FAILED"]}, "VaultType": {"type": "string", "enum": ["BACKUP_VAULT", "LOGICALLY_AIR_GAPPED_BACKUP_VAULT", "RESTORE_ACCESS_BACKUP_VAULT"]}, "WindowMinutes": {"type": "long"}, "boolean": {"type": "boolean"}, "integer": {"type": "integer"}, "long": {"type": "long"}, "string": {"type": "string"}, "stringList": {"type": "list", "member": {"shape": "string"}}, "stringMap": {"type": "map", "key": {"shape": "string"}, "value": {"shape": "string"}}, "timestamp": {"type": "timestamp"}}, "documentation": "<fullname>Backup</fullname> <p>Backup is a unified backup service designed to protect Amazon Web Services services and their associated data. Backup simplifies the creation, migration, restoration, and deletion of backups, while also providing reporting and auditing.</p>"}