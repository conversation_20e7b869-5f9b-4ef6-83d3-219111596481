{"version": "2.0", "metadata": {"apiVersion": "2018-08-08", "endpointPrefix": "globalaccelerator", "jsonVersion": "1.1", "protocol": "json", "protocols": ["json"], "serviceFullName": "AWS Global Accelerator", "serviceId": "Global Accelerator", "signatureVersion": "v4", "signingName": "globalaccelerator", "targetPrefix": "GlobalAccelerator_V20180706", "uid": "globalaccelerator-2018-08-08", "auth": ["aws.auth#sigv4"]}, "operations": {"AddCustomRoutingEndpoints": {"name": "AddCustomRoutingEndpoints", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AddCustomRoutingEndpointsRequest"}, "output": {"shape": "AddCustomRoutingEndpointsResponse"}, "errors": [{"shape": "EndpointAlreadyExistsException"}, {"shape": "EndpointGroupNotFoundException"}, {"shape": "InternalServiceErrorException"}, {"shape": "InvalidArgumentException"}, {"shape": "LimitExceededException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}], "documentation": "<p>Associate a virtual private cloud (VPC) subnet endpoint with your custom routing accelerator.</p> <p>The listener port range must be large enough to support the number of IP addresses that can be specified in your subnet. The number of ports required is: subnet size times the number of ports per destination EC2 instances. For example, a subnet defined as /24 requires a listener port range of at least 255 ports. </p> <p>Note: You must have enough remaining listener ports available to map to the subnet ports, or the call will fail with a LimitExceededException.</p> <p>By default, all destinations in a subnet in a custom routing accelerator cannot receive traffic. To enable all destinations to receive traffic, or to specify individual port mappings that can receive traffic, see the <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/api/API_AllowCustomRoutingTraffic.html\"> AllowCustomRoutingTraffic</a> operation.</p>"}, "AddEndpoints": {"name": "AddEndpoints", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AddEndpointsRequest"}, "output": {"shape": "AddEndpointsResponse"}, "errors": [{"shape": "TransactionInProgressException"}, {"shape": "EndpointGroupNotFoundException"}, {"shape": "InternalServiceErrorException"}, {"shape": "InvalidArgumentException"}, {"shape": "LimitExceededException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Add endpoints to an endpoint group. The <code>AddEndpoints</code> API operation is the recommended option for adding endpoints. The alternative options are to add endpoints when you create an endpoint group (with the <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/api/API_CreateEndpointGroup.html\">CreateEndpointGroup</a> API) or when you update an endpoint group (with the <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/api/API_UpdateEndpointGroup.html\">UpdateEndpointGroup</a> API). </p> <p>There are two advantages to using <code>AddEndpoints</code> to add endpoints in Global Accelerator:</p> <ul> <li> <p>It's faster, because Global Accelerator only has to resolve the new endpoints that you're adding, rather than resolving new and existing endpoints.</p> </li> <li> <p>It's more convenient, because you don't need to specify the current endpoints that are already in the endpoint group, in addition to the new endpoints that you want to add.</p> </li> </ul> <p>For information about endpoint types and requirements for endpoints that you can add to Global Accelerator, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/about-endpoints.html\"> Endpoints for standard accelerators</a> in the <i>Global Accelerator Developer Guide</i>.</p>"}, "AdvertiseByoipCidr": {"name": "AdvertiseByoipCidr", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AdvertiseByoipCidrRequest"}, "output": {"shape": "AdvertiseByoipCidrResponse"}, "errors": [{"shape": "InternalServiceErrorException"}, {"shape": "InvalidArgumentException"}, {"shape": "AccessDeniedException"}, {"shape": "ByoipCidrNotFoundException"}, {"shape": "IncorrectCidrStateException"}], "documentation": "<p>Advertises an IPv4 address range that is provisioned for use with your Amazon Web Services resources through bring your own IP addresses (BYOIP). It can take a few minutes before traffic to the specified addresses starts routing to Amazon Web Services because of propagation delays. </p> <p>To stop advertising the BYOIP address range, use <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/api/WithdrawByoipCidr.html\"> WithdrawByoipCidr</a>.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/using-byoip.html\">Bring your own IP addresses (BYOIP)</a> in the <i>Global Accelerator Developer Guide</i>.</p>"}, "AllowCustomRoutingTraffic": {"name": "AllowCustomRoutingTraffic", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AllowCustomRoutingTrafficRequest"}, "errors": [{"shape": "EndpointGroupNotFoundException"}, {"shape": "InvalidArgumentException"}, {"shape": "InternalServiceErrorException"}], "documentation": "<p>Specify the Amazon EC2 instance (destination) IP addresses and ports for a VPC subnet endpoint that can receive traffic for a custom routing accelerator. You can allow traffic to all destinations in the subnet endpoint, or allow traffic to a specified list of destination IP addresses and ports in the subnet. Note that you cannot specify IP addresses or ports outside of the range that you configured for the endpoint group.</p> <p>After you make changes, you can verify that the updates are complete by checking the status of your accelerator: the status changes from IN_PROGRESS to DEPLOYED.</p>"}, "CreateAccelerator": {"name": "CreateAccelerator", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateAcceleratorRequest"}, "output": {"shape": "CreateAcceleratorResponse"}, "errors": [{"shape": "InternalServiceErrorException"}, {"shape": "InvalidArgumentException"}, {"shape": "LimitExceededException"}, {"shape": "AccessDeniedException"}, {"shape": "TransactionInProgressException"}], "documentation": "<p>Create an accelerator. An accelerator includes one or more listeners that process inbound connections and direct traffic to one or more endpoint groups, each of which includes endpoints, such as Network Load Balancers. </p> <important> <p>Global Accelerator is a global service that supports endpoints in multiple Amazon Web Services Regions but you must specify the US West (Oregon) Region to create, update, or otherwise work with accelerators. That is, for example, specify <code>--region us-west-2</code> on Amazon Web Services CLI commands.</p> </important>"}, "CreateCrossAccountAttachment": {"name": "CreateCrossAccountAttachment", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateCrossAccountAttachmentRequest"}, "output": {"shape": "CreateCrossAccountAttachmentResponse"}, "errors": [{"shape": "InternalServiceErrorException"}, {"shape": "InvalidArgumentException"}, {"shape": "LimitExceededException"}, {"shape": "AccessDeniedException"}, {"shape": "TransactionInProgressException"}], "documentation": "<p>Create a cross-account attachment in Global Accelerator. You create a cross-account attachment to specify the <i>principals</i> who have permission to work with <i>resources</i> in accelerators in their own account. You specify, in the same attachment, the resources that are shared.</p> <p>A principal can be an Amazon Web Services account number or the Amazon Resource Name (ARN) for an accelerator. For account numbers that are listed as principals, to work with a resource listed in the attachment, you must sign in to an account specified as a principal. Then, you can work with resources that are listed, with any of your accelerators. If an accelerator ARN is listed in the cross-account attachment as a principal, anyone with permission to make updates to the accelerator can work with resources that are listed in the attachment. </p> <p>Specify each principal and resource separately. To specify two CIDR address pools, list them individually under <code>Resources</code>, and so on. For a command line operation, for example, you might use a statement like the following:</p> <p> <code> \"Resources\": [{\"Cidr\": \"************/24\"},{\"Cidr\": \"************/24\"}]</code> </p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/cross-account-resources.html\"> Working with cross-account attachments and resources in Global Accelerator</a> in the <i> Global Accelerator Developer Guide</i>.</p>"}, "CreateCustomRoutingAccelerator": {"name": "CreateCustomRoutingAccelerator", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateCustomRoutingAcceleratorRequest"}, "output": {"shape": "CreateCustomRoutingAcceleratorResponse"}, "errors": [{"shape": "InternalServiceErrorException"}, {"shape": "InvalidArgumentException"}, {"shape": "LimitExceededException"}, {"shape": "AccessDeniedException"}, {"shape": "TransactionInProgressException"}], "documentation": "<p>Create a custom routing accelerator. A custom routing accelerator directs traffic to one of possibly thousands of Amazon EC2 instance destinations running in a single or multiple virtual private clouds (VPC) subnet endpoints.</p> <p>Be aware that, by default, all destination EC2 instances in a VPC subnet endpoint cannot receive traffic. To enable all destinations to receive traffic, or to specify individual port mappings that can receive traffic, see the <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/api/API_AllowCustomRoutingTraffic.html\"> AllowCustomRoutingTraffic</a> operation.</p> <important> <p>Global Accelerator is a global service that supports endpoints in multiple Amazon Web Services Regions but you must specify the US West (Oregon) Region to create, update, or otherwise work with accelerators. That is, for example, specify <code>--region us-west-2</code> on Amazon Web Services CLI commands.</p> </important>"}, "CreateCustomRoutingEndpointGroup": {"name": "CreateCustomRoutingEndpointGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateCustomRoutingEndpointGroupRequest"}, "output": {"shape": "CreateCustomRoutingEndpointGroupResponse"}, "errors": [{"shape": "AcceleratorNotFoundException"}, {"shape": "EndpointGroupAlreadyExistsException"}, {"shape": "ListenerNotFoundException"}, {"shape": "InternalServiceErrorException"}, {"shape": "InvalidArgumentException"}, {"shape": "InvalidPortRangeException"}, {"shape": "LimitExceededException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Create an endpoint group for the specified listener for a custom routing accelerator. An endpoint group is a collection of endpoints in one Amazon Web Services Region. </p>"}, "CreateCustomRoutingListener": {"name": "CreateCustomRoutingListener", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateCustomRoutingListenerRequest"}, "output": {"shape": "CreateCustomRoutingListenerResponse"}, "errors": [{"shape": "InvalidArgumentException"}, {"shape": "AcceleratorNotFoundException"}, {"shape": "InvalidPortRangeException"}, {"shape": "InternalServiceErrorException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Create a listener to process inbound connections from clients to a custom routing accelerator. Connections arrive to assigned static IP addresses on the port range that you specify. </p>"}, "CreateEndpointGroup": {"name": "CreateEndpointGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateEndpointGroupRequest"}, "output": {"shape": "CreateEndpointGroupResponse"}, "errors": [{"shape": "AcceleratorNotFoundException"}, {"shape": "EndpointGroupAlreadyExistsException"}, {"shape": "ListenerNotFoundException"}, {"shape": "InternalServiceErrorException"}, {"shape": "InvalidArgumentException"}, {"shape": "LimitExceededException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Create an endpoint group for the specified listener. An endpoint group is a collection of endpoints in one Amazon Web Services Region. A resource must be valid and active when you add it as an endpoint.</p> <p>For more information about endpoint types and requirements for endpoints that you can add to Global Accelerator, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/about-endpoints.html\"> Endpoints for standard accelerators</a> in the <i>Global Accelerator Developer Guide</i>.</p>"}, "CreateListener": {"name": "CreateListener", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateListenerRequest"}, "output": {"shape": "CreateListenerResponse"}, "errors": [{"shape": "InvalidArgumentException"}, {"shape": "AcceleratorNotFoundException"}, {"shape": "InvalidPortRangeException"}, {"shape": "InternalServiceErrorException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Create a listener to process inbound connections from clients to an accelerator. Connections arrive to assigned static IP addresses on a port, port range, or list of port ranges that you specify. </p>"}, "DeleteAccelerator": {"name": "DeleteAccelerator", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteAcceleratorRequest"}, "errors": [{"shape": "AcceleratorNotFoundException"}, {"shape": "AcceleratorNotDisabledException"}, {"shape": "AssociatedListenerFoundException"}, {"shape": "InternalServiceErrorException"}, {"shape": "InvalidArgumentException"}, {"shape": "TransactionInProgressException"}], "documentation": "<p>Delete an accelerator. Before you can delete an accelerator, you must disable it and remove all dependent resources (listeners and endpoint groups). To disable the accelerator, update the accelerator to set <code>Enabled</code> to false.</p> <important> <p>When you create an accelerator, by default, Global Accelerator provides you with a set of two static IP addresses. Alternatively, you can bring your own IP address ranges to Global Accelerator and assign IP addresses from those ranges. </p> <p>The IP addresses are assigned to your accelerator for as long as it exists, even if you disable the accelerator and it no longer accepts or routes traffic. However, when you <i>delete</i> an accelerator, you lose the static IP addresses that are assigned to the accelerator, so you can no longer route traffic by using them. As a best practice, ensure that you have permissions in place to avoid inadvertently deleting accelerators. You can use IAM policies with Global Accelerator to limit the users who have permissions to delete an accelerator. For more information, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/auth-and-access-control.html\">Identity and access management</a> in the <i>Global Accelerator Developer Guide</i>.</p> </important>"}, "DeleteCrossAccountAttachment": {"name": "DeleteCrossAccountAttachment", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteCrossAccountAttachmentRequest"}, "errors": [{"shape": "AttachmentNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServiceErrorException"}, {"shape": "InvalidArgumentException"}, {"shape": "TransactionInProgressException"}], "documentation": "<p>Delete a cross-account attachment. When you delete an attachment, Global Accelerator revokes the permission to use the resources in the attachment from all principals in the list of principals. Global Accelerator revokes the permission for specific resources.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/cross-account-resources.html\"> Working with cross-account attachments and resources in Global Accelerator</a> in the <i> Global Accelerator Developer Guide</i>.</p>"}, "DeleteCustomRoutingAccelerator": {"name": "DeleteCustomRoutingAccelerator", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteCustomRoutingAcceleratorRequest"}, "errors": [{"shape": "AcceleratorNotFoundException"}, {"shape": "AcceleratorNotDisabledException"}, {"shape": "AssociatedListenerFoundException"}, {"shape": "InternalServiceErrorException"}, {"shape": "InvalidArgumentException"}, {"shape": "TransactionInProgressException"}], "documentation": "<p>Delete a custom routing accelerator. Before you can delete an accelerator, you must disable it and remove all dependent resources (listeners and endpoint groups). To disable the accelerator, update the accelerator to set <code>Enabled</code> to false.</p> <important> <p>When you create a custom routing accelerator, by default, Global Accelerator provides you with a set of two static IP addresses. </p> <p>The IP addresses are assigned to your accelerator for as long as it exists, even if you disable the accelerator and it no longer accepts or routes traffic. However, when you <i>delete</i> an accelerator, you lose the static IP addresses that are assigned to the accelerator, so you can no longer route traffic by using them. As a best practice, ensure that you have permissions in place to avoid inadvertently deleting accelerators. You can use IAM policies with Global Accelerator to limit the users who have permissions to delete an accelerator. For more information, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/auth-and-access-control.html\">Identity and access management</a> in the <i>Global Accelerator Developer Guide</i>.</p> </important>"}, "DeleteCustomRoutingEndpointGroup": {"name": "DeleteCustomRoutingEndpointGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteCustomRoutingEndpointGroupRequest"}, "errors": [{"shape": "InvalidArgumentException"}, {"shape": "EndpointGroupNotFoundException"}, {"shape": "InternalServiceErrorException"}], "documentation": "<p>Delete an endpoint group from a listener for a custom routing accelerator.</p>"}, "DeleteCustomRoutingListener": {"name": "DeleteCustomRoutingListener", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteCustomRoutingListenerRequest"}, "errors": [{"shape": "InvalidArgumentException"}, {"shape": "ListenerNotFoundException"}, {"shape": "AssociatedEndpointGroupFoundException"}, {"shape": "InternalServiceErrorException"}], "documentation": "<p>Delete a listener for a custom routing accelerator.</p>"}, "DeleteEndpointGroup": {"name": "DeleteEndpointGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteEndpointGroupRequest"}, "errors": [{"shape": "InvalidArgumentException"}, {"shape": "EndpointGroupNotFoundException"}, {"shape": "InternalServiceErrorException"}], "documentation": "<p>Delete an endpoint group from a listener.</p>"}, "DeleteListener": {"name": "DeleteListener", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteListenerRequest"}, "errors": [{"shape": "InvalidArgumentException"}, {"shape": "ListenerNotFoundException"}, {"shape": "AssociatedEndpointGroupFoundException"}, {"shape": "InternalServiceErrorException"}], "documentation": "<p>Delete a listener from an accelerator.</p>"}, "DenyCustomRoutingTraffic": {"name": "DenyCustomRoutingTraffic", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DenyCustomRoutingTrafficRequest"}, "errors": [{"shape": "EndpointGroupNotFoundException"}, {"shape": "InvalidArgumentException"}, {"shape": "InternalServiceErrorException"}], "documentation": "<p>Specify the Amazon EC2 instance (destination) IP addresses and ports for a VPC subnet endpoint that cannot receive traffic for a custom routing accelerator. You can deny traffic to all destinations in the VPC endpoint, or deny traffic to a specified list of destination IP addresses and ports. Note that you cannot specify IP addresses or ports outside of the range that you configured for the endpoint group.</p> <p>After you make changes, you can verify that the updates are complete by checking the status of your accelerator: the status changes from IN_PROGRESS to DEPLOYED.</p>"}, "DeprovisionByoipCidr": {"name": "DeprovisionByoipCidr", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeprovisionByoipCidrRequest"}, "output": {"shape": "DeprovisionByoipCidrResponse"}, "errors": [{"shape": "InternalServiceErrorException"}, {"shape": "InvalidArgumentException"}, {"shape": "AccessDeniedException"}, {"shape": "ByoipCidrNotFoundException"}, {"shape": "IncorrectCidrStateException"}], "documentation": "<p>Releases the specified address range that you provisioned to use with your Amazon Web Services resources through bring your own IP addresses (BYOIP) and deletes the corresponding address pool. </p> <p>Before you can release an address range, you must stop advertising it by using <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/api/WithdrawByoipCidr.html\">WithdrawByoipCidr</a> and you must not have any accelerators that are using static IP addresses allocated from its address range. </p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/using-byoip.html\">Bring your own IP addresses (BYOIP)</a> in the <i>Global Accelerator Developer Guide</i>.</p>"}, "DescribeAccelerator": {"name": "DescribeAccelerator", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeAcceleratorRequest"}, "output": {"shape": "DescribeAcceleratorResponse"}, "errors": [{"shape": "AcceleratorNotFoundException"}, {"shape": "InternalServiceErrorException"}, {"shape": "InvalidArgumentException"}], "documentation": "<p>Describe an accelerator. </p>"}, "DescribeAcceleratorAttributes": {"name": "DescribeAcceleratorAttributes", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeAcceleratorAttributesRequest"}, "output": {"shape": "DescribeAcceleratorAttributesResponse"}, "errors": [{"shape": "AcceleratorNotFoundException"}, {"shape": "InternalServiceErrorException"}, {"shape": "InvalidArgumentException"}], "documentation": "<p>Describe the attributes of an accelerator. </p>"}, "DescribeCrossAccountAttachment": {"name": "DescribeCrossAccountAttachment", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeCrossAccountAttachmentRequest"}, "output": {"shape": "DescribeCrossAccountAttachmentResponse"}, "errors": [{"shape": "AttachmentNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServiceErrorException"}, {"shape": "InvalidArgumentException"}], "documentation": "<p>Gets configuration information about a cross-account attachment.</p>"}, "DescribeCustomRoutingAccelerator": {"name": "DescribeCustomRoutingAccelerator", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeCustomRoutingAcceleratorRequest"}, "output": {"shape": "DescribeCustomRoutingAcceleratorResponse"}, "errors": [{"shape": "AcceleratorNotFoundException"}, {"shape": "InternalServiceErrorException"}, {"shape": "InvalidArgumentException"}], "documentation": "<p>Describe a custom routing accelerator. </p>"}, "DescribeCustomRoutingAcceleratorAttributes": {"name": "DescribeCustomRoutingAcceleratorAttributes", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeCustomRoutingAcceleratorAttributesRequest"}, "output": {"shape": "DescribeCustomRoutingAcceleratorAttributesResponse"}, "errors": [{"shape": "AcceleratorNotFoundException"}, {"shape": "InternalServiceErrorException"}, {"shape": "InvalidArgumentException"}], "documentation": "<p>Describe the attributes of a custom routing accelerator. </p>"}, "DescribeCustomRoutingEndpointGroup": {"name": "DescribeCustomRoutingEndpointGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeCustomRoutingEndpointGroupRequest"}, "output": {"shape": "DescribeCustomRoutingEndpointGroupResponse"}, "errors": [{"shape": "InvalidArgumentException"}, {"shape": "EndpointGroupNotFoundException"}, {"shape": "InternalServiceErrorException"}], "documentation": "<p>Describe an endpoint group for a custom routing accelerator. </p>"}, "DescribeCustomRoutingListener": {"name": "DescribeCustomRoutingListener", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeCustomRoutingListenerRequest"}, "output": {"shape": "DescribeCustomRoutingListenerResponse"}, "errors": [{"shape": "InvalidArgumentException"}, {"shape": "ListenerNotFoundException"}, {"shape": "InternalServiceErrorException"}], "documentation": "<p>The description of a listener for a custom routing accelerator.</p>"}, "DescribeEndpointGroup": {"name": "DescribeEndpointGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeEndpointGroupRequest"}, "output": {"shape": "DescribeEndpointGroupResponse"}, "errors": [{"shape": "InvalidArgumentException"}, {"shape": "EndpointGroupNotFoundException"}, {"shape": "InternalServiceErrorException"}], "documentation": "<p>Describe an endpoint group. </p>"}, "DescribeListener": {"name": "DescribeListener", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeListenerRequest"}, "output": {"shape": "DescribeListenerResponse"}, "errors": [{"shape": "InvalidArgumentException"}, {"shape": "ListenerNotFoundException"}, {"shape": "InternalServiceErrorException"}], "documentation": "<p>Describe a listener. </p>"}, "ListAccelerators": {"name": "ListAccelerators", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListAcceleratorsRequest"}, "output": {"shape": "ListAcceleratorsResponse"}, "errors": [{"shape": "InvalidArgumentException"}, {"shape": "InvalidNextTokenException"}, {"shape": "InternalServiceErrorException"}], "documentation": "<p>List the accelerators for an Amazon Web Services account. </p>"}, "ListByoipCidrs": {"name": "ListByoipCidrs", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListByoipCidrsRequest"}, "output": {"shape": "ListByoipCidrsResponse"}, "errors": [{"shape": "InternalServiceErrorException"}, {"shape": "InvalidArgumentException"}, {"shape": "AccessDeniedException"}, {"shape": "InvalidNextTokenException"}], "documentation": "<p>Lists the IP address ranges that were specified in calls to <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/api/ProvisionByoipCidr.html\">ProvisionByoipCidr</a>, including the current state and a history of state changes.</p>"}, "ListCrossAccountAttachments": {"name": "ListCrossAccountAttachments", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListCrossAccountAttachmentsRequest"}, "output": {"shape": "ListCrossAccountAttachmentsResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "InvalidArgumentException"}, {"shape": "InvalidNextTokenException"}, {"shape": "InternalServiceErrorException"}], "documentation": "<p>List the cross-account attachments that have been created in Global Accelerator.</p>"}, "ListCrossAccountResourceAccounts": {"name": "ListCrossAccountResourceAccounts", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListCrossAccountResourceAccountsRequest"}, "output": {"shape": "ListCrossAccountResourceAccountsResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "InternalServiceErrorException"}], "documentation": "<p>List the accounts that have cross-account resources.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/cross-account-resources.html\"> Working with cross-account attachments and resources in Global Accelerator</a> in the <i> Global Accelerator Developer Guide</i>.</p>"}, "ListCrossAccountResources": {"name": "ListCrossAccountResources", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListCrossAccountResourcesRequest"}, "output": {"shape": "ListCrossAccountResourcesResponse"}, "errors": [{"shape": "InternalServiceErrorException"}, {"shape": "InvalidArgumentException"}, {"shape": "InvalidNextTokenException"}, {"shape": "AccessDeniedException"}, {"shape": "AcceleratorNotFoundException"}], "documentation": "<p>List the cross-account resources available to work with.</p>"}, "ListCustomRoutingAccelerators": {"name": "ListCustomRoutingAccelerators", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListCustomRoutingAcceleratorsRequest"}, "output": {"shape": "ListCustomRoutingAcceleratorsResponse"}, "errors": [{"shape": "InvalidArgumentException"}, {"shape": "InvalidNextTokenException"}, {"shape": "InternalServiceErrorException"}], "documentation": "<p>List the custom routing accelerators for an Amazon Web Services account. </p>"}, "ListCustomRoutingEndpointGroups": {"name": "ListCustomRoutingEndpointGroups", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListCustomRoutingEndpointGroupsRequest"}, "output": {"shape": "ListCustomRoutingEndpointGroupsResponse"}, "errors": [{"shape": "ListenerNotFoundException"}, {"shape": "InvalidNextTokenException"}, {"shape": "InvalidArgumentException"}, {"shape": "InternalServiceErrorException"}], "documentation": "<p>List the endpoint groups that are associated with a listener for a custom routing accelerator. </p>"}, "ListCustomRoutingListeners": {"name": "ListCustomRoutingListeners", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListCustomRoutingListenersRequest"}, "output": {"shape": "ListCustomRoutingListenersResponse"}, "errors": [{"shape": "InvalidArgumentException"}, {"shape": "AcceleratorNotFoundException"}, {"shape": "InvalidNextTokenException"}, {"shape": "InternalServiceErrorException"}], "documentation": "<p>List the listeners for a custom routing accelerator. </p>"}, "ListCustomRoutingPortMappings": {"name": "ListCustomRoutingPortMappings", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListCustomRoutingPortMappingsRequest"}, "output": {"shape": "ListCustomRoutingPortMappingsResponse"}, "errors": [{"shape": "InvalidArgumentException"}, {"shape": "InvalidNextTokenException"}, {"shape": "EndpointGroupNotFoundException"}, {"shape": "AcceleratorNotFoundException"}, {"shape": "InternalServiceErrorException"}], "documentation": "<p>Provides a complete mapping from the public accelerator IP address and port to destination EC2 instance IP addresses and ports in the virtual public cloud (VPC) subnet endpoint for a custom routing accelerator. For each subnet endpoint that you add, Global Accelerator creates a new static port mapping for the accelerator. The port mappings don't change after Global Accelerator generates them, so you can retrieve and cache the full mapping on your servers. </p> <p>If you remove a subnet from your accelerator, Global Accelerator removes (reclaims) the port mappings. If you add a subnet to your accelerator, Global Accelerator creates new port mappings (the existing ones don't change). If you add or remove EC2 instances in your subnet, the port mappings don't change, because the mappings are created when you add the subnet to Global Accelerator.</p> <p>The mappings also include a flag for each destination denoting which destination IP addresses and ports are allowed or denied traffic.</p>"}, "ListCustomRoutingPortMappingsByDestination": {"name": "ListCustomRoutingPortMappingsByDestination", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListCustomRoutingPortMappingsByDestinationRequest"}, "output": {"shape": "ListCustomRoutingPortMappingsByDestinationResponse"}, "errors": [{"shape": "InvalidArgumentException"}, {"shape": "InvalidNextTokenException"}, {"shape": "EndpointNotFoundException"}, {"shape": "InternalServiceErrorException"}], "documentation": "<p>List the port mappings for a specific EC2 instance (destination) in a VPC subnet endpoint. The response is the mappings for one destination IP address. This is useful when your subnet endpoint has mappings that span multiple custom routing accelerators in your account, or for scenarios where you only want to list the port mappings for a specific destination instance.</p>"}, "ListEndpointGroups": {"name": "ListEndpointGroups", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListEndpointGroupsRequest"}, "output": {"shape": "ListEndpointGroupsResponse"}, "errors": [{"shape": "ListenerNotFoundException"}, {"shape": "InvalidNextTokenException"}, {"shape": "InvalidArgumentException"}, {"shape": "InternalServiceErrorException"}], "documentation": "<p>List the endpoint groups that are associated with a listener. </p>"}, "ListListeners": {"name": "ListListeners", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListListenersRequest"}, "output": {"shape": "ListListenersResponse"}, "errors": [{"shape": "InvalidArgumentException"}, {"shape": "AcceleratorNotFoundException"}, {"shape": "InvalidNextTokenException"}, {"shape": "InternalServiceErrorException"}], "documentation": "<p>List the listeners for an accelerator. </p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "AcceleratorNotFoundException"}, {"shape": "AttachmentNotFoundException"}, {"shape": "EndpointGroupNotFoundException"}, {"shape": "ListenerNotFoundException"}, {"shape": "InternalServiceErrorException"}, {"shape": "InvalidArgumentException"}], "documentation": "<p>List all tags for an accelerator. </p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/tagging-in-global-accelerator.html\">Tagging in Global Accelerator</a> in the <i>Global Accelerator Developer Guide</i>. </p>"}, "ProvisionByoipCidr": {"name": "ProvisionByoipCidr", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ProvisionByoipCidrRequest"}, "output": {"shape": "ProvisionByoipCidrResponse"}, "errors": [{"shape": "InternalServiceErrorException"}, {"shape": "InvalidArgumentException"}, {"shape": "LimitExceededException"}, {"shape": "AccessDeniedException"}, {"shape": "IncorrectCidrStateException"}], "documentation": "<p>Provisions an IP address range to use with your Amazon Web Services resources through bring your own IP addresses (BYOIP) and creates a corresponding address pool. After the address range is provisioned, it is ready to be advertised using <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/api/AdvertiseByoipCidr.html\"> AdvertiseByoipCidr</a>.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/using-byoip.html\">Bring your own IP addresses (BYOIP)</a> in the <i>Global Accelerator Developer Guide</i>.</p>"}, "RemoveCustomRoutingEndpoints": {"name": "RemoveCustomRoutingEndpoints", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "RemoveCustomRoutingEndpointsRequest"}, "errors": [{"shape": "EndpointGroupNotFoundException"}, {"shape": "EndpointNotFoundException"}, {"shape": "InternalServiceErrorException"}, {"shape": "InvalidArgumentException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}], "documentation": "<p>Remove endpoints from a custom routing accelerator.</p>"}, "RemoveEndpoints": {"name": "RemoveEndpoints", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "RemoveEndpointsRequest"}, "errors": [{"shape": "EndpointGroupNotFoundException"}, {"shape": "InternalServiceErrorException"}, {"shape": "InvalidArgumentException"}, {"shape": "AccessDeniedException"}, {"shape": "TransactionInProgressException"}], "documentation": "<p>Remove endpoints from an endpoint group. </p> <p>The <code>RemoveEndpoints</code> API operation is the recommended option for removing endpoints. The alternative is to remove endpoints by updating an endpoint group by using the <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/api/API_UpdateEndpointGroup.html\">UpdateEndpointGroup</a> API operation. There are two advantages to using <code>AddEndpoints</code> to remove endpoints instead:</p> <ul> <li> <p>It's more convenient, because you only need to specify the endpoints that you want to remove. With the <code>UpdateEndpointGroup</code> API operation, you must specify all of the endpoints in the endpoint group except the ones that you want to remove from the group.</p> </li> <li> <p>It's faster, because Global Accelerator doesn't need to resolve any endpoints. With the <code>UpdateEndpointGroup</code> API operation, Global Accelerator must resolve all of the endpoints that remain in the group.</p> </li> </ul>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "AcceleratorNotFoundException"}, {"shape": "InternalServiceErrorException"}, {"shape": "InvalidArgumentException"}], "documentation": "<p>Add tags to an accelerator resource. </p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/tagging-in-global-accelerator.html\">Tagging in Global Accelerator</a> in the <i>Global Accelerator Developer Guide</i>. </p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "AcceleratorNotFoundException"}, {"shape": "InternalServiceErrorException"}, {"shape": "InvalidArgumentException"}], "documentation": "<p>Remove tags from a Global Accelerator resource. When you specify a tag key, the action removes both that key and its associated value. The operation succeeds even if you attempt to remove tags from an accelerator that was already removed.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/tagging-in-global-accelerator.html\">Tagging in Global Accelerator</a> in the <i>Global Accelerator Developer Guide</i>.</p>"}, "UpdateAccelerator": {"name": "UpdateAccelerator", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateAcceleratorRequest"}, "output": {"shape": "UpdateAcceleratorResponse"}, "errors": [{"shape": "AcceleratorNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServiceErrorException"}, {"shape": "InvalidArgumentException"}, {"shape": "TransactionInProgressException"}, {"shape": "ConflictException"}], "documentation": "<p>Update an accelerator to make changes, such as the following: </p> <ul> <li> <p>Change the name of the accelerator.</p> </li> <li> <p>Disable the accelerator so that it no longer accepts or routes traffic, or so that you can delete it.</p> </li> <li> <p>Enable the accelerator, if it is disabled.</p> </li> <li> <p>Change the IP address type to dual-stack if it is IPv4, or change the IP address type to IPv4 if it's dual-stack.</p> </li> </ul> <p>Be aware that static IP addresses remain assigned to your accelerator for as long as it exists, even if you disable the accelerator and it no longer accepts or routes traffic. However, when you delete the accelerator, you lose the static IP addresses that are assigned to it, so you can no longer route traffic by using them.</p> <important> <p>Global Accelerator is a global service that supports endpoints in multiple Amazon Web Services Regions but you must specify the US West (Oregon) Region to create, update, or otherwise work with accelerators. That is, for example, specify <code>--region us-west-2</code> on Amazon Web Services CLI commands.</p> </important>"}, "UpdateAcceleratorAttributes": {"name": "UpdateAcceleratorAttributes", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateAcceleratorAttributesRequest"}, "output": {"shape": "UpdateAcceleratorAttributesResponse"}, "errors": [{"shape": "AcceleratorNotFoundException"}, {"shape": "InternalServiceErrorException"}, {"shape": "InvalidArgumentException"}, {"shape": "AccessDeniedException"}, {"shape": "TransactionInProgressException"}], "documentation": "<p>Update the attributes for an accelerator. </p>"}, "UpdateCrossAccountAttachment": {"name": "UpdateCrossAccountAttachment", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateCrossAccountAttachmentRequest"}, "output": {"shape": "UpdateCrossAccountAttachmentResponse"}, "errors": [{"shape": "AttachmentNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServiceErrorException"}, {"shape": "InvalidArgumentException"}, {"shape": "LimitExceededException"}, {"shape": "TransactionInProgressException"}], "documentation": "<p>Update a cross-account attachment to add or remove principals or resources. When you update an attachment to remove a principal (account ID or accelerator) or a resource, Global Accelerator revokes the permission for specific resources. </p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/cross-account-resources.html\"> Working with cross-account attachments and resources in Global Accelerator</a> in the <i> Global Accelerator Developer Guide</i>.</p>"}, "UpdateCustomRoutingAccelerator": {"name": "UpdateCustomRoutingAccelerator", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateCustomRoutingAcceleratorRequest"}, "output": {"shape": "UpdateCustomRoutingAcceleratorResponse"}, "errors": [{"shape": "AcceleratorNotFoundException"}, {"shape": "InternalServiceErrorException"}, {"shape": "InvalidArgumentException"}, {"shape": "TransactionInProgressException"}, {"shape": "ConflictException"}], "documentation": "<p>Update a custom routing accelerator. </p>"}, "UpdateCustomRoutingAcceleratorAttributes": {"name": "UpdateCustomRoutingAcceleratorAttributes", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateCustomRoutingAcceleratorAttributesRequest"}, "output": {"shape": "UpdateCustomRoutingAcceleratorAttributesResponse"}, "errors": [{"shape": "AcceleratorNotFoundException"}, {"shape": "InternalServiceErrorException"}, {"shape": "InvalidArgumentException"}, {"shape": "AccessDeniedException"}, {"shape": "TransactionInProgressException"}], "documentation": "<p>Update the attributes for a custom routing accelerator. </p>"}, "UpdateCustomRoutingListener": {"name": "UpdateCustomRoutingListener", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateCustomRoutingListenerRequest"}, "output": {"shape": "UpdateCustomRoutingListenerResponse"}, "errors": [{"shape": "InvalidArgumentException"}, {"shape": "InvalidPortRangeException"}, {"shape": "ListenerNotFoundException"}, {"shape": "InternalServiceErrorException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Update a listener for a custom routing accelerator. </p>"}, "UpdateEndpointGroup": {"name": "UpdateEndpointGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateEndpointGroupRequest"}, "output": {"shape": "UpdateEndpointGroupResponse"}, "errors": [{"shape": "InvalidArgumentException"}, {"shape": "EndpointGroupNotFoundException"}, {"shape": "InternalServiceErrorException"}, {"shape": "LimitExceededException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Update an endpoint group. A resource must be valid and active when you add it as an endpoint.</p>"}, "UpdateListener": {"name": "UpdateListener", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateListenerRequest"}, "output": {"shape": "UpdateListenerResponse"}, "errors": [{"shape": "InvalidArgumentException"}, {"shape": "InvalidPortRangeException"}, {"shape": "ListenerNotFoundException"}, {"shape": "InternalServiceErrorException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Update a listener. </p>"}, "WithdrawByoipCidr": {"name": "WithdrawByoipCidr", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "WithdrawByoipCidrRequest"}, "output": {"shape": "WithdrawByoipCidrResponse"}, "errors": [{"shape": "InternalServiceErrorException"}, {"shape": "InvalidArgumentException"}, {"shape": "AccessDeniedException"}, {"shape": "ByoipCidrNotFoundException"}, {"shape": "IncorrectCidrStateException"}], "documentation": "<p>Stops advertising an address range that is provisioned as an address pool. You can perform this operation at most once every 10 seconds, even if you specify different address ranges each time.</p> <p>It can take a few minutes before traffic to the specified addresses stops routing to Amazon Web Services because of propagation delays.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/using-byoip.html\">Bring your own IP addresses (BYOIP)</a> in the <i>Global Accelerator Developer Guide</i>.</p>"}}, "shapes": {"Accelerator": {"type": "structure", "members": {"AcceleratorArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the accelerator.</p>"}, "Name": {"shape": "GenericString", "documentation": "<p>The name of the accelerator. The name must contain only alphanumeric characters or hyphens (-), and must not begin or end with a hyphen.</p>"}, "IpAddressType": {"shape": "IpAddressType", "documentation": "<p>The IP address type that an accelerator supports. For a standard accelerator, the value can be IPV4 or DUAL_STACK.</p>"}, "Enabled": {"shape": "GenericBoolean", "documentation": "<p>Indicates whether the accelerator is enabled. The value is true or false. The default value is true. </p> <p>If the value is set to true, the accelerator cannot be deleted. If set to false, accelerator can be deleted.</p>"}, "IpSets": {"shape": "IpSets", "documentation": "<p>The static IP addresses that Global Accelerator associates with the accelerator.</p>"}, "DnsName": {"shape": "GenericString", "documentation": "<p>The Domain Name System (DNS) name that Global Accelerator creates that points to an accelerator's static IPv4 addresses.</p> <p>The naming convention for the DNS name for an accelerator is the following: A lowercase letter a, followed by a 16-bit random hex string, followed by .awsglobalaccelerator.com. For example: a1234567890abcdef.awsglobalaccelerator.com.</p> <p>If you have a dual-stack accelerator, you also have a second DNS name, <code>DualStackDnsName</code>, that points to both the A record and the AAAA record for all four static addresses for the accelerator: two IPv4 addresses and two IPv6 addresses.</p> <p>For more information about the default DNS name, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/dns-addressing-custom-domains.dns-addressing.html\"> Support for DNS addressing in Global Accelerator</a> in the <i>Global Accelerator Developer Guide</i>.</p>"}, "Status": {"shape": "AcceleratorStatus", "documentation": "<p>Describes the deployment status of the accelerator.</p>"}, "CreatedTime": {"shape": "Timestamp", "documentation": "<p>The date and time that the accelerator was created.</p>"}, "LastModifiedTime": {"shape": "Timestamp", "documentation": "<p>The date and time that the accelerator was last modified.</p>"}, "DualStackDnsName": {"shape": "GenericString", "documentation": "<p>The Domain Name System (DNS) name that Global Accelerator creates that points to a dual-stack accelerator's four static IP addresses: two IPv4 addresses and two IPv6 addresses.</p> <p>The naming convention for the dual-stack DNS name is the following: A lowercase letter a, followed by a 16-bit random hex string, followed by .dualstack.awsglobalaccelerator.com. For example: a1234567890abcdef.dualstack.awsglobalaccelerator.com.</p> <p>Note: Global Accelerator also assigns a default DNS name, <code>DnsName</code>, to your accelerator that points just to the static IPv4 addresses. </p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/about-accelerators.html#about-accelerators.dns-addressing\"> Support for DNS addressing in Global Accelerator</a> in the <i>Global Accelerator Developer Guide</i>.</p>"}, "Events": {"shape": "AcceleratorEvents", "documentation": "<p>A history of changes that you make to an accelerator in Global Accelerator.</p>"}}, "documentation": "<p>An accelerator is a complex type that includes one or more listeners that process inbound connections and then direct traffic to one or more endpoint groups, each of which includes endpoints, such as load balancers.</p>"}, "AcceleratorAttributes": {"type": "structure", "members": {"FlowLogsEnabled": {"shape": "GenericBoolean", "documentation": "<p>Indicates whether flow logs are enabled. The default value is false. If the value is true, <code>FlowLogsS3Bucket</code> and <code>FlowLogsS3Prefix</code> must be specified.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/monitoring-global-accelerator.flow-logs.html\">Flow logs</a> in the <i>Global Accelerator Developer Guide</i>.</p>"}, "FlowLogsS3Bucket": {"shape": "GenericString", "documentation": "<p>The name of the Amazon S3 bucket for the flow logs. Attribute is required if <code>FlowLogsEnabled</code> is <code>true</code>. The bucket must exist and have a bucket policy that grants Global Accelerator permission to write to the bucket.</p>"}, "FlowLogsS3Prefix": {"shape": "GenericString", "documentation": "<p>The prefix for the location in the Amazon S3 bucket for the flow logs. Attribute is required if <code>FlowLogsEnabled</code> is <code>true</code>.</p> <p>If you specify slash (/) for the S3 bucket prefix, the log file bucket folder structure will include a double slash (//), like the following:</p> <p>s3-bucket_name//AWSLogs/aws_account_id</p>"}}, "documentation": "<p>Attributes of an accelerator.</p>"}, "AcceleratorEvent": {"type": "structure", "members": {"Message": {"shape": "GenericString", "documentation": "<p>A string that contains an <code>Event</code> message describing changes or errors when you update an accelerator in Global Accelerator from IPv4 to dual-stack, or dual-stack to IPv4.</p>"}, "Timestamp": {"shape": "Timestamp", "documentation": "<p>A timestamp for when you update an accelerator in Global Accelerator from IPv4 to dual-stack, or dual-stack to IPv4.</p>"}}, "documentation": "<p>A complex type that contains a <code>Timestamp</code> value and <code>Message</code> for changes that you make to an accelerator in Global Accelerator. Messages stored here provide progress or error information when you update an accelerator from IPv4 to dual-stack, or from dual-stack to IPv4. Global Accelerator stores a maximum of ten event messages. </p>"}, "AcceleratorEvents": {"type": "list", "member": {"shape": "AcceleratorEvent"}}, "AcceleratorNotDisabledException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The accelerator that you specified could not be disabled.</p>", "exception": true}, "AcceleratorNotFoundException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The accelerator that you specified doesn't exist.</p>", "exception": true}, "AcceleratorStatus": {"type": "string", "enum": ["DEPLOYED", "IN_PROGRESS"]}, "Accelerators": {"type": "list", "member": {"shape": "Accelerator"}}, "AccessDeniedException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>You don't have access permission.</p>", "exception": true}, "AddCustomRoutingEndpointsRequest": {"type": "structure", "required": ["EndpointConfigurations", "EndpointGroupArn"], "members": {"EndpointConfigurations": {"shape": "CustomRoutingEndpointConfigurations", "documentation": "<p>The list of endpoint objects to add to a custom routing accelerator.</p>"}, "EndpointGroupArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the endpoint group for the custom routing endpoint.</p>"}}}, "AddCustomRoutingEndpointsResponse": {"type": "structure", "members": {"EndpointDescriptions": {"shape": "CustomRoutingEndpointDescriptions", "documentation": "<p>The endpoint objects added to the custom routing accelerator.</p>"}, "EndpointGroupArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the endpoint group for the custom routing endpoint.</p>"}}}, "AddEndpointsRequest": {"type": "structure", "required": ["EndpointConfigurations", "EndpointGroupArn"], "members": {"EndpointConfigurations": {"shape": "EndpointConfigurations", "documentation": "<p>The list of endpoint objects.</p>"}, "EndpointGroupArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the endpoint group.</p>"}}}, "AddEndpointsResponse": {"type": "structure", "members": {"EndpointDescriptions": {"shape": "EndpointDescriptions", "documentation": "<p>The list of endpoint objects.</p>"}, "EndpointGroupArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the endpoint group.</p>"}}}, "AdvertiseByoipCidrRequest": {"type": "structure", "required": ["<PERSON><PERSON><PERSON>"], "members": {"Cidr": {"shape": "GenericString", "documentation": "<p>The address range, in CIDR notation. This must be the exact range that you provisioned. You can't advertise only a portion of the provisioned range.</p> <p> For more information, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/using-byoip.html\">Bring your own IP addresses (BYOIP)</a> in the Global Accelerator Developer Guide.</p>"}}}, "AdvertiseByoipCidrResponse": {"type": "structure", "members": {"ByoipCidr": {"shape": "ByoipCidr", "documentation": "<p>Information about the address range.</p>"}}}, "AllowCustomRoutingTrafficRequest": {"type": "structure", "required": ["EndpointGroupArn", "EndpointId"], "members": {"EndpointGroupArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the endpoint group.</p>"}, "EndpointId": {"shape": "GenericString", "documentation": "<p>An ID for the endpoint. For custom routing accelerators, this is the virtual private cloud (VPC) subnet ID.</p>"}, "DestinationAddresses": {"shape": "DestinationAddresses", "documentation": "<p>A list of specific Amazon EC2 instance IP addresses (destination addresses) in a subnet that you want to allow to receive traffic. The IP addresses must be a subset of the IP addresses that you specified for the endpoint group.</p> <p> <code>DestinationAddresses</code> is required if <code>AllowAllTrafficToEndpoint</code> is <code>FALSE</code> or is not specified.</p>"}, "DestinationPorts": {"shape": "DestinationPorts", "documentation": "<p>A list of specific Amazon EC2 instance ports (destination ports) that you want to allow to receive traffic.</p>"}, "AllowAllTrafficToEndpoint": {"shape": "GenericBoolean", "documentation": "<p>Indicates whether all destination IP addresses and ports for a specified VPC subnet endpoint can receive traffic from a custom routing accelerator. The value is TRUE or FALSE. </p> <p>When set to TRUE, <i>all</i> destinations in the custom routing VPC subnet can receive traffic. Note that you cannot specify destination IP addresses and ports when the value is set to TRUE.</p> <p>When set to FALSE (or not specified), you <i>must</i> specify a list of destination IP addresses that are allowed to receive traffic. A list of ports is optional. If you don't specify a list of ports, the ports that can accept traffic is the same as the ports configured for the endpoint group.</p> <p>The default value is FALSE.</p>"}}}, "AssociatedEndpointGroupFoundException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The listener that you specified has an endpoint group associated with it. You must remove all dependent resources from a listener before you can delete it.</p>", "exception": true}, "AssociatedListenerFoundException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The accelerator that you specified has a listener associated with it. You must remove all dependent resources from an accelerator before you can delete it.</p>", "exception": true}, "Attachment": {"type": "structure", "members": {"AttachmentArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the cross-account attachment.</p>"}, "Name": {"shape": "AttachmentName", "documentation": "<p>The name of the cross-account attachment.</p>"}, "Principals": {"shape": "Principals", "documentation": "<p>The principals included in the cross-account attachment.</p>"}, "Resources": {"shape": "Resources", "documentation": "<p>The resources included in the cross-account attachment.</p>"}, "LastModifiedTime": {"shape": "Timestamp", "documentation": "<p>The date and time that the cross-account attachment was last modified.</p>"}, "CreatedTime": {"shape": "Timestamp", "documentation": "<p>The date and time that the cross-account attachment was created.</p>"}}, "documentation": "<p>A cross-account attachment in Global Accelerator. A cross-account attachment specifies the <i>principals</i> who have permission to work with <i>resources</i> in your account, which you also list in the attachment.</p>"}, "AttachmentName": {"type": "string", "max": 64, "pattern": "[\\S\\s]+"}, "AttachmentNotFoundException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>No cross-account attachment was found.</p>", "exception": true}, "Attachments": {"type": "list", "member": {"shape": "Attachment"}}, "AwsAccountId": {"type": "string", "max": 12, "min": 12, "pattern": "^\\d{12}$"}, "AwsAccountIds": {"type": "list", "member": {"shape": "AwsAccountId"}}, "ByoipCidr": {"type": "structure", "members": {"Cidr": {"shape": "GenericString", "documentation": "<p>The address range, in CIDR notation.</p> <p> For more information, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/using-byoip.html\">Bring your own IP addresses (BYOIP)</a> in the Global Accelerator Developer Guide.</p>"}, "State": {"shape": "ByoipCidrState", "documentation": "<p>The state of the address pool.</p>"}, "Events": {"shape": "ByoipCidrEvents", "documentation": "<p>A history of status changes for an IP address range that you bring to Global Accelerator through bring your own IP address (BYOIP).</p>"}}, "documentation": "<p>Information about an IP address range that is provisioned for use with your Amazon Web Services resources through bring your own IP address (BYOIP).</p> <p>The following describes each BYOIP <code>State</code> that your IP address range can be in.</p> <ul> <li> <p> <b>PENDING_PROVISIONING</b> — You’ve submitted a request to provision an IP address range but it is not yet provisioned with Global Accelerator.</p> </li> <li> <p> <b>READY</b> — The address range is provisioned with Global Accelerator and can be advertised.</p> </li> <li> <p> <b>PENDING_ADVERTISING</b> — You’ve submitted a request for Global Accelerator to advertise an address range but it is not yet being advertised.</p> </li> <li> <p> <b>ADVERTISING</b> — The address range is being advertised by Global Accelerator.</p> </li> <li> <p> <b>PENDING_WITHDRAWING</b> — You’ve submitted a request to withdraw an address range from being advertised but it is still being advertised by Global Accelerator.</p> </li> <li> <p> <b>PENDING_DEPROVISIONING</b> — You’ve submitted a request to deprovision an address range from Global Accelerator but it is still provisioned.</p> </li> <li> <p> <b>DEPROVISIONED</b> — The address range is deprovisioned from Global Accelerator.</p> </li> <li> <p> <b>FAILED_PROVISION </b> — The request to provision the address range from Global Accelerator was not successful. Please make sure that you provide all of the correct information, and try again. If the request fails a second time, contact Amazon Web Services support.</p> </li> <li> <p> <b>FAILED_ADVERTISING</b> — The request for Global Accelerator to advertise the address range was not successful. Please make sure that you provide all of the correct information, and try again. If the request fails a second time, contact Amazon Web Services support.</p> </li> <li> <p> <b>FAILED_WITHDRAW</b> — The request to withdraw the address range from advertising by Global Accelerator was not successful. Please make sure that you provide all of the correct information, and try again. If the request fails a second time, contact Amazon Web Services support.</p> </li> <li> <p> <b>FAILED_DEPROVISION </b> — The request to deprovision the address range from Global Accelerator was not successful. Please make sure that you provide all of the correct information, and try again. If the request fails a second time, contact Amazon Web Services support.</p> </li> </ul>"}, "ByoipCidrEvent": {"type": "structure", "members": {"Message": {"shape": "GenericString", "documentation": "<p>A string that contains an <code>Event</code> message describing changes that you make in the status of an IP address range that you bring to Global Accelerator through bring your own IP address (BYOIP).</p>"}, "Timestamp": {"shape": "Timestamp", "documentation": "<p>A timestamp for when you make a status change for an IP address range that you bring to Global Accelerator through bring your own IP address (BYOIP).</p>"}}, "documentation": "<p>A complex type that contains a <code>Message</code> and a <code>Timestamp</code> value for changes that you make in the status of an IP address range that you bring to Global Accelerator through bring your own IP address (BYOIP).</p>"}, "ByoipCidrEvents": {"type": "list", "member": {"shape": "ByoipCidrEvent"}}, "ByoipCidrNotFoundException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The CIDR that you specified was not found or is incorrect.</p>", "exception": true}, "ByoipCidrState": {"type": "string", "enum": ["PENDING_PROVISIONING", "READY", "PENDING_ADVERTISING", "ADVERTISING", "PENDING_WITHDRAWING", "PENDING_DEPROVISIONING", "DEPROVISIONED", "FAILED_PROVISION", "FAILED_ADVERTISING", "FAILED_WITHDRAW", "FAILED_DEPROVISION"]}, "ByoipCidrs": {"type": "list", "member": {"shape": "ByoipCidr"}}, "CidrAuthorizationContext": {"type": "structure", "required": ["Message", "Signature"], "members": {"Message": {"shape": "GenericString", "documentation": "<p>The plain-text authorization message for the prefix and account.</p>"}, "Signature": {"shape": "GenericString", "documentation": "<p>The signed authorization message for the prefix and account.</p>"}}, "documentation": "<p>Provides authorization for Amazon to bring a specific IP address range to a specific Amazon Web Services account using bring your own IP addresses (BYOIP). </p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/using-byoip.html\">Bring your own IP addresses (BYOIP)</a> in the <i>Global Accelerator Developer Guide</i>.</p>"}, "ClientAffinity": {"type": "string", "enum": ["NONE", "SOURCE_IP"]}, "ConflictException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>You can't use both of those options.</p>", "exception": true}, "CreateAcceleratorRequest": {"type": "structure", "required": ["Name", "IdempotencyToken"], "members": {"Name": {"shape": "GenericString", "documentation": "<p>The name of the accelerator. The name can have a maximum of 64 characters, must contain only alphanumeric characters, periods (.), or hyphens (-), and must not begin or end with a hyphen or period.</p>"}, "IpAddressType": {"shape": "IpAddressType", "documentation": "<p>The IP address type that an accelerator supports. For a standard accelerator, the value can be IPV4 or DUAL_STACK.</p>"}, "IpAddresses": {"shape": "IpAddresses", "documentation": "<p>Optionally, if you've added your own IP address pool to Global Accelerator (BYOIP), you can choose an IPv4 address from your own pool to use for the accelerator's static IPv4 address when you create an accelerator. </p> <p>After you bring an address range to Amazon Web Services, it appears in your account as an address pool. When you create an accelerator, you can assign one IPv4 address from your range to it. Global Accelerator assigns you a second static IPv4 address from an Amazon IP address range. If you bring two IPv4 address ranges to Amazon Web Services, you can assign one IPv4 address from each range to your accelerator. This restriction is because Global Accelerator assigns each address range to a different network zone, for high availability.</p> <p>You can specify one or two addresses, separated by a space. Do not include the /32 suffix.</p> <p>Note that you can't update IP addresses for an existing accelerator. To change them, you must create a new accelerator with the new addresses.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/using-byoip.html\">Bring your own IP addresses (BYOIP)</a> in the <i>Global Accelerator Developer Guide</i>.</p>"}, "Enabled": {"shape": "GenericBoolean", "documentation": "<p>Indicates whether an accelerator is enabled. The value is true or false. The default value is true. </p> <p>If the value is set to true, an accelerator cannot be deleted. If set to false, the accelerator can be deleted.</p>"}, "IdempotencyToken": {"shape": "IdempotencyToken", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency—that is, the uniqueness—of an accelerator.</p>", "idempotencyToken": true}, "Tags": {"shape": "Tags", "documentation": "<p>Create tags for an accelerator.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/tagging-in-global-accelerator.html\">Tagging in Global Accelerator</a> in the <i>Global Accelerator Developer Guide</i>.</p>"}}}, "CreateAcceleratorResponse": {"type": "structure", "members": {"Accelerator": {"shape": "Accelerator", "documentation": "<p>The accelerator that is created by specifying a listener and the supported IP address types.</p>"}}}, "CreateCrossAccountAttachmentRequest": {"type": "structure", "required": ["Name", "IdempotencyToken"], "members": {"Name": {"shape": "AttachmentName", "documentation": "<p>The name of the cross-account attachment. </p>"}, "Principals": {"shape": "Principals", "documentation": "<p>The principals to include in the cross-account attachment. A principal can be an Amazon Web Services account number or the Amazon Resource Name (ARN) for an accelerator. </p>"}, "Resources": {"shape": "Resources", "documentation": "<p>The Amazon Resource Names (ARNs) for the resources to include in the cross-account attachment. A resource can be any supported Amazon Web Services resource type for Global Accelerator or a CIDR range for a bring your own IP address (BYOIP) address pool. </p>"}, "IdempotencyToken": {"shape": "IdempotencyToken", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency—that is, the uniqueness—of the request.</p>", "idempotencyToken": true}, "Tags": {"shape": "Tags", "documentation": "<p>Add tags for a cross-account attachment.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/tagging-in-global-accelerator.html\">Tagging in Global Accelerator</a> in the <i>Global Accelerator Developer Guide</i>.</p>"}}}, "CreateCrossAccountAttachmentResponse": {"type": "structure", "members": {"CrossAccountAttachment": {"shape": "Attachment", "documentation": "<p>Information about the cross-account attachment.</p>"}}}, "CreateCustomRoutingAcceleratorRequest": {"type": "structure", "required": ["Name", "IdempotencyToken"], "members": {"Name": {"shape": "GenericString", "documentation": "<p>The name of a custom routing accelerator. The name can have a maximum of 64 characters, must contain only alphanumeric characters or hyphens (-), and must not begin or end with a hyphen.</p>"}, "IpAddressType": {"shape": "IpAddressType", "documentation": "<p>The IP address type that an accelerator supports. For a custom routing accelerator, the value must be IPV4.</p>"}, "IpAddresses": {"shape": "IpAddresses", "documentation": "<p>Optionally, if you've added your own IP address pool to Global Accelerator (BYOIP), you can choose an IPv4 address from your own pool to use for the accelerator's static IPv4 address when you create an accelerator. </p> <p>After you bring an address range to Amazon Web Services, it appears in your account as an address pool. When you create an accelerator, you can assign one IPv4 address from your range to it. Global Accelerator assigns you a second static IPv4 address from an Amazon IP address range. If you bring two IPv4 address ranges to Amazon Web Services, you can assign one IPv4 address from each range to your accelerator. This restriction is because Global Accelerator assigns each address range to a different network zone, for high availability.</p> <p>You can specify one or two addresses, separated by a space. Do not include the /32 suffix.</p> <p>Note that you can't update IP addresses for an existing accelerator. To change them, you must create a new accelerator with the new addresses.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/using-byoip.html\">Bring your own IP addresses (BYOIP)</a> in the <i>Global Accelerator Developer Guide</i>.</p>"}, "Enabled": {"shape": "GenericBoolean", "documentation": "<p>Indicates whether an accelerator is enabled. The value is true or false. The default value is true. </p> <p>If the value is set to true, an accelerator cannot be deleted. If set to false, the accelerator can be deleted.</p>"}, "IdempotencyToken": {"shape": "IdempotencyToken", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency—that is, the uniqueness—of the request.</p>", "idempotencyToken": true}, "Tags": {"shape": "Tags", "documentation": "<p>Create tags for an accelerator.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/tagging-in-global-accelerator.html\">Tagging in Global Accelerator</a> in the <i>Global Accelerator Developer Guide</i>.</p>"}}}, "CreateCustomRoutingAcceleratorResponse": {"type": "structure", "members": {"Accelerator": {"shape": "CustomRoutingAccelerator", "documentation": "<p>The accelerator that is created.</p>"}}}, "CreateCustomRoutingEndpointGroupRequest": {"type": "structure", "required": ["Listener<PERSON>rn", "EndpointGroupRegion", "DestinationConfigurations", "IdempotencyToken"], "members": {"ListenerArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the listener for a custom routing endpoint.</p>"}, "EndpointGroupRegion": {"shape": "GenericString", "documentation": "<p>The Amazon Web Services Region where the endpoint group is located. A listener can have only one endpoint group in a specific Region.</p>"}, "DestinationConfigurations": {"shape": "CustomRoutingDestinationConfigurations", "documentation": "<p>Sets the port range and protocol for all endpoints (virtual private cloud subnets) in a custom routing endpoint group to accept client traffic on.</p>"}, "IdempotencyToken": {"shape": "IdempotencyToken", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency—that is, the uniqueness—of the request.</p>", "idempotencyToken": true}}}, "CreateCustomRoutingEndpointGroupResponse": {"type": "structure", "members": {"EndpointGroup": {"shape": "CustomRoutingEndpointGroup", "documentation": "<p>The information about the endpoint group created for a custom routing accelerator.</p>"}}}, "CreateCustomRoutingListenerRequest": {"type": "structure", "required": ["AcceleratorArn", "PortRanges", "IdempotencyToken"], "members": {"AcceleratorArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the accelerator for a custom routing listener.</p>"}, "PortRanges": {"shape": "PortRanges", "documentation": "<p>The port range to support for connections from clients to your accelerator.</p> <p>Separately, you set port ranges for endpoints. For more information, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/about-custom-routing-endpoints.html\">About endpoints for custom routing accelerators</a>.</p>"}, "IdempotencyToken": {"shape": "IdempotencyToken", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency—that is, the uniqueness—of the request.</p>", "idempotencyToken": true}}}, "CreateCustomRoutingListenerResponse": {"type": "structure", "members": {"Listener": {"shape": "CustomRoutingListener", "documentation": "<p>The listener that you've created for a custom routing accelerator.</p>"}}}, "CreateEndpointGroupRequest": {"type": "structure", "required": ["Listener<PERSON>rn", "EndpointGroupRegion", "IdempotencyToken"], "members": {"ListenerArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the listener.</p>"}, "EndpointGroupRegion": {"shape": "GenericString", "documentation": "<p>The Amazon Web Services Region where the endpoint group is located. A listener can have only one endpoint group in a specific Region.</p>"}, "EndpointConfigurations": {"shape": "EndpointConfigurations", "documentation": "<p>The list of endpoint objects.</p>"}, "TrafficDialPercentage": {"shape": "TrafficDialPercentage", "documentation": "<p>The percentage of traffic to send to an Amazon Web Services Region. Additional traffic is distributed to other endpoint groups for this listener. </p> <p>Use this action to increase (dial up) or decrease (dial down) traffic to a specific Region. The percentage is applied to the traffic that would otherwise have been routed to the Region based on optimal routing.</p> <p>The default value is 100.</p>"}, "HealthCheckPort": {"shape": "HealthCheckPort", "documentation": "<p>The port that Global Accelerator uses to check the health of endpoints that are part of this endpoint group. The default port is the listener port that this endpoint group is associated with. If listener port is a list of ports, Global Accelerator uses the first port in the list.</p>"}, "HealthCheckProtocol": {"shape": "HealthCheckProtocol", "documentation": "<p>The protocol that Global Accelerator uses to check the health of endpoints that are part of this endpoint group. The default value is TCP.</p>"}, "HealthCheckPath": {"shape": "HealthCheckPath", "documentation": "<p>If the protocol is HTTP/S, then this specifies the path that is the destination for health check targets. The default value is slash (/).</p>"}, "HealthCheckIntervalSeconds": {"shape": "HealthCheckIntervalSeconds", "documentation": "<p>The time—10 seconds or 30 seconds—between each health check for an endpoint. The default value is 30.</p>"}, "ThresholdCount": {"shape": "ThresholdCount", "documentation": "<p>The number of consecutive health checks required to set the state of a healthy endpoint to unhealthy, or to set an unhealthy endpoint to healthy. The default value is 3.</p>"}, "IdempotencyToken": {"shape": "IdempotencyToken", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency—that is, the uniqueness—of the request.</p>", "idempotencyToken": true}, "PortOverrides": {"shape": "PortOverrides", "documentation": "<p>Override specific listener ports used to route traffic to endpoints that are part of this endpoint group. For example, you can create a port override in which the listener receives user traffic on ports 80 and 443, but your accelerator routes that traffic to ports 1080 and 1443, respectively, on the endpoints.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/about-endpoint-groups-port-override.html\"> Overriding listener ports</a> in the <i>Global Accelerator Developer Guide</i>.</p>"}}}, "CreateEndpointGroupResponse": {"type": "structure", "members": {"EndpointGroup": {"shape": "EndpointGroup", "documentation": "<p>The information about the endpoint group that was created.</p>"}}}, "CreateListenerRequest": {"type": "structure", "required": ["AcceleratorArn", "PortRanges", "Protocol", "IdempotencyToken"], "members": {"AcceleratorArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of your accelerator.</p>"}, "PortRanges": {"shape": "PortRanges", "documentation": "<p>The list of port ranges to support for connections from clients to your accelerator.</p>"}, "Protocol": {"shape": "Protocol", "documentation": "<p>The protocol for connections from clients to your accelerator.</p>"}, "ClientAffinity": {"shape": "ClientAffinity", "documentation": "<p>Client affinity lets you direct all requests from a user to the same endpoint, if you have stateful applications, regardless of the port and protocol of the client request. Client affinity gives you control over whether to always route each client to the same specific endpoint.</p> <p>Global Accelerator uses a consistent-flow hashing algorithm to choose the optimal endpoint for a connection. If client affinity is <code>NONE</code>, Global Accelerator uses the \"five-tuple\" (5-tuple) properties—source IP address, source port, destination IP address, destination port, and protocol—to select the hash value, and then chooses the best endpoint. However, with this setting, if someone uses different ports to connect to Global Accelerator, their connections might not be always routed to the same endpoint because the hash value changes. </p> <p>If you want a given client to always be routed to the same endpoint, set client affinity to <code>SOURCE_IP</code> instead. When you use the <code>SOURCE_IP</code> setting, Global Accelerator uses the \"two-tuple\" (2-tuple) properties— source (client) IP address and destination IP address—to select the hash value.</p> <p>The default value is <code>NONE</code>.</p>"}, "IdempotencyToken": {"shape": "IdempotencyToken", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency—that is, the uniqueness—of the request.</p>", "idempotencyToken": true}}}, "CreateListenerResponse": {"type": "structure", "members": {"Listener": {"shape": "Listener", "documentation": "<p>The listener that you've created.</p>"}}}, "CrossAccountResource": {"type": "structure", "members": {"EndpointId": {"shape": "GenericString", "documentation": "<p>The endpoint ID for the endpoint that is listed in a cross-account attachment and can be added to an accelerator by specified principals.</p>"}, "Cidr": {"shape": "GenericString", "documentation": "<p>An IP address range, in CIDR format, that is specified as an Amazon Web Services resource. The address must be provisioned and advertised in Global Accelerator by following the bring your own IP address (BYOIP) process for Global Accelerator.</p> <p> For more information, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/using-byoip.html\">Bring your own IP addresses (BYOIP)</a> in the Global Accelerator Developer Guide.</p>"}, "AttachmentArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the cross-account attachment that specifies the resources (endpoints or CIDR range) that can be added to accelerators and principals that have permission to add them.</p>"}}, "documentation": "<p>An endpoint (Amazon Web Services resource) or an IP address range, in CIDR format, that is listed in a cross-account attachment. A cross-account resource can be added to an accelerator by specified principals, which are also listed in the attachment.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/cross-account-resources.html\"> Working with cross-account attachments and resources in Global Accelerator</a> in the <i> Global Accelerator Developer Guide</i>.</p>"}, "CrossAccountResources": {"type": "list", "member": {"shape": "CrossAccountResource"}}, "CustomRoutingAccelerator": {"type": "structure", "members": {"AcceleratorArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the custom routing accelerator.</p>"}, "Name": {"shape": "GenericString", "documentation": "<p>The name of the accelerator. The name must contain only alphanumeric characters or hyphens (-), and must not begin or end with a hyphen.</p>"}, "IpAddressType": {"shape": "IpAddressType", "documentation": "<p>The IP address type that an accelerator supports. For a custom routing accelerator, the value must be IPV4.</p>"}, "Enabled": {"shape": "GenericBoolean", "documentation": "<p>Indicates whether the accelerator is enabled. The value is true or false. The default value is true. </p> <p>If the value is set to true, the accelerator cannot be deleted. If set to false, accelerator can be deleted.</p>"}, "IpSets": {"shape": "IpSets", "documentation": "<p>The static IP addresses that Global Accelerator associates with the accelerator.</p>"}, "DnsName": {"shape": "GenericString", "documentation": "<p>The Domain Name System (DNS) name that Global Accelerator creates that points to an accelerator's static IPv4 addresses. </p> <p>The naming convention for the DNS name is the following: A lowercase letter a, followed by a 16-bit random hex string, followed by .awsglobalaccelerator.com. For example: a1234567890abcdef.awsglobalaccelerator.com.</p> <p>If you have a dual-stack accelerator, you also have a second DNS name, <code>DualStackDnsName</code>, that points to both the A record and the AAAA record for all four static addresses for the accelerator: two IPv4 addresses and two IPv6 addresses.</p> <p>For more information about the default DNS name, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/dns-addressing-custom-domains.dns-addressing.html\"> Support for DNS addressing in Global Accelerator</a> in the <i>Global Accelerator Developer Guide</i>.</p>"}, "Status": {"shape": "CustomRoutingAcceleratorStatus", "documentation": "<p>Describes the deployment status of the accelerator.</p>"}, "CreatedTime": {"shape": "Timestamp", "documentation": "<p>The date and time that the accelerator was created.</p>"}, "LastModifiedTime": {"shape": "Timestamp", "documentation": "<p>The date and time that the accelerator was last modified.</p>"}}, "documentation": "<p>Attributes of a custom routing accelerator.</p>"}, "CustomRoutingAcceleratorAttributes": {"type": "structure", "members": {"FlowLogsEnabled": {"shape": "GenericBoolean", "documentation": "<p>Indicates whether flow logs are enabled. The default value is false. If the value is true, <code>FlowLogsS3Bucket</code> and <code>FlowLogsS3Prefix</code> must be specified.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/monitoring-global-accelerator.flow-logs.html\">Flow logs</a> in the <i>Global Accelerator Developer Guide</i>.</p>"}, "FlowLogsS3Bucket": {"shape": "GenericString", "documentation": "<p>The name of the Amazon S3 bucket for the flow logs. Attribute is required if <code>FlowLogsEnabled</code> is <code>true</code>. The bucket must exist and have a bucket policy that grants Global Accelerator permission to write to the bucket.</p>"}, "FlowLogsS3Prefix": {"shape": "GenericString", "documentation": "<p>The prefix for the location in the Amazon S3 bucket for the flow logs. Attribute is required if <code>FlowLogsEnabled</code> is <code>true</code>.</p> <p>If you don’t specify a prefix, the flow logs are stored in the root of the bucket. If you specify slash (/) for the S3 bucket prefix, the log file bucket folder structure will include a double slash (//), like the following:</p> <p>DOC-EXAMPLE-BUCKET//AWSLogs/aws_account_id</p>"}}, "documentation": "<p>Attributes of a custom routing accelerator.</p>"}, "CustomRoutingAcceleratorStatus": {"type": "string", "enum": ["DEPLOYED", "IN_PROGRESS"]}, "CustomRoutingAccelerators": {"type": "list", "member": {"shape": "CustomRoutingAccelerator"}}, "CustomRoutingDestinationConfiguration": {"type": "structure", "required": ["FromPort", "ToPort", "Protocols"], "members": {"FromPort": {"shape": "PortNumber", "documentation": "<p>The first port, inclusive, in the range of ports for the endpoint group that is associated with a custom routing accelerator.</p>"}, "ToPort": {"shape": "PortNumber", "documentation": "<p>The last port, inclusive, in the range of ports for the endpoint group that is associated with a custom routing accelerator.</p>"}, "Protocols": {"shape": "CustomRoutingProtocols", "documentation": "<p>The protocol for the endpoint group that is associated with a custom routing accelerator. The protocol can be either TCP or UDP.</p>"}}, "documentation": "<p>For a custom routing accelerator, sets the port range and protocol for all endpoints (virtual private cloud subnets) in an endpoint group to accept client traffic on.</p>"}, "CustomRoutingDestinationConfigurations": {"type": "list", "member": {"shape": "CustomRoutingDestinationConfiguration"}, "max": 100, "min": 1}, "CustomRoutingDestinationDescription": {"type": "structure", "members": {"FromPort": {"shape": "PortNumber", "documentation": "<p>The first port, inclusive, in the range of ports for the endpoint group that is associated with a custom routing accelerator.</p>"}, "ToPort": {"shape": "PortNumber", "documentation": "<p>The last port, inclusive, in the range of ports for the endpoint group that is associated with a custom routing accelerator.</p>"}, "Protocols": {"shape": "Protocols", "documentation": "<p>The protocol for the endpoint group that is associated with a custom routing accelerator. The protocol can be either TCP or UDP.</p>"}}, "documentation": "<p>For a custom routing accelerator, describes the port range and protocol for all endpoints (virtual private cloud subnets) in an endpoint group to accept client traffic on.</p>"}, "CustomRoutingDestinationDescriptions": {"type": "list", "member": {"shape": "CustomRoutingDestinationDescription"}}, "CustomRoutingDestinationTrafficState": {"type": "string", "enum": ["ALLOW", "DENY"]}, "CustomRoutingEndpointConfiguration": {"type": "structure", "members": {"EndpointId": {"shape": "GenericString", "documentation": "<p>An ID for the endpoint. For custom routing accelerators, this is the virtual private cloud (VPC) subnet ID. </p>"}, "AttachmentArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the cross-account attachment that specifies the endpoints (resources) that can be added to accelerators and principals that have permission to add the endpoints.</p>"}}, "documentation": "<p>The list of endpoint objects. For custom routing, this is a list of virtual private cloud (VPC) subnet IDs.</p>"}, "CustomRoutingEndpointConfigurations": {"type": "list", "member": {"shape": "CustomRoutingEndpointConfiguration"}, "max": 20, "min": 1}, "CustomRoutingEndpointDescription": {"type": "structure", "members": {"EndpointId": {"shape": "GenericString", "documentation": "<p>An ID for the endpoint. For custom routing accelerators, this is the virtual private cloud (VPC) subnet ID. </p>"}}, "documentation": "<p>A complex type for an endpoint for a custom routing accelerator. Each endpoint group can include one or more endpoints, which are virtual private cloud (VPC) subnets.</p>"}, "CustomRoutingEndpointDescriptions": {"type": "list", "member": {"shape": "CustomRoutingEndpointDescription"}}, "CustomRoutingEndpointGroup": {"type": "structure", "members": {"EndpointGroupArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the endpoint group.</p>"}, "EndpointGroupRegion": {"shape": "GenericString", "documentation": "<p>The Amazon Web Services Region where the endpoint group is located.</p>"}, "DestinationDescriptions": {"shape": "CustomRoutingDestinationDescriptions", "documentation": "<p>For a custom routing accelerator, describes the port range and protocol for all endpoints (virtual private cloud subnets) in an endpoint group to accept client traffic on.</p>"}, "EndpointDescriptions": {"shape": "CustomRoutingEndpointDescriptions", "documentation": "<p>For a custom routing accelerator, describes the endpoints (virtual private cloud subnets) in an endpoint group to accept client traffic on.</p>"}}, "documentation": "<p>A complex type for the endpoint group for a custom routing accelerator. An Amazon Web Services Region can have only one endpoint group for a specific listener. </p>"}, "CustomRoutingEndpointGroups": {"type": "list", "member": {"shape": "CustomRoutingEndpointGroup"}}, "CustomRoutingListener": {"type": "structure", "members": {"ListenerArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the listener.</p>"}, "PortRanges": {"shape": "PortRanges", "documentation": "<p>The port range to support for connections from clients to your accelerator.</p> <p>Separately, you set port ranges for endpoints. For more information, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/about-custom-routing-endpoints.html\">About endpoints for custom routing accelerators</a>.</p>"}}, "documentation": "<p>A complex type for a listener for a custom routing accelerator.</p>"}, "CustomRoutingListeners": {"type": "list", "member": {"shape": "CustomRoutingListener"}}, "CustomRoutingProtocol": {"type": "string", "enum": ["TCP", "UDP"]}, "CustomRoutingProtocols": {"type": "list", "member": {"shape": "CustomRoutingProtocol"}, "max": 2, "min": 1}, "DeleteAcceleratorRequest": {"type": "structure", "required": ["AcceleratorArn"], "members": {"AcceleratorArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of an accelerator.</p>"}}}, "DeleteCrossAccountAttachmentRequest": {"type": "structure", "required": ["AttachmentArn"], "members": {"AttachmentArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) for the cross-account attachment to delete.</p>"}}}, "DeleteCustomRoutingAcceleratorRequest": {"type": "structure", "required": ["AcceleratorArn"], "members": {"AcceleratorArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the custom routing accelerator to delete.</p>"}}}, "DeleteCustomRoutingEndpointGroupRequest": {"type": "structure", "required": ["EndpointGroupArn"], "members": {"EndpointGroupArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the endpoint group to delete.</p>"}}}, "DeleteCustomRoutingListenerRequest": {"type": "structure", "required": ["Listener<PERSON>rn"], "members": {"ListenerArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the listener to delete.</p>"}}}, "DeleteEndpointGroupRequest": {"type": "structure", "required": ["EndpointGroupArn"], "members": {"EndpointGroupArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the endpoint group to delete.</p>"}}}, "DeleteListenerRequest": {"type": "structure", "required": ["Listener<PERSON>rn"], "members": {"ListenerArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the listener.</p>"}}}, "DenyCustomRoutingTrafficRequest": {"type": "structure", "required": ["EndpointGroupArn", "EndpointId"], "members": {"EndpointGroupArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the endpoint group.</p>"}, "EndpointId": {"shape": "GenericString", "documentation": "<p>An ID for the endpoint. For custom routing accelerators, this is the virtual private cloud (VPC) subnet ID.</p>"}, "DestinationAddresses": {"shape": "DestinationAddresses", "documentation": "<p>A list of specific Amazon EC2 instance IP addresses (destination addresses) in a subnet that you want to prevent from receiving traffic. The IP addresses must be a subset of the IP addresses allowed for the VPC subnet associated with the endpoint group.</p>"}, "DestinationPorts": {"shape": "DestinationPorts", "documentation": "<p>A list of specific Amazon EC2 instance ports (destination ports) in a subnet endpoint that you want to prevent from receiving traffic.</p>"}, "DenyAllTrafficToEndpoint": {"shape": "GenericBoolean", "documentation": "<p>Indicates whether all destination IP addresses and ports for a specified VPC subnet endpoint <i>cannot</i> receive traffic from a custom routing accelerator. The value is TRUE or FALSE. </p> <p>When set to TRUE, <i>no</i> destinations in the custom routing VPC subnet can receive traffic. Note that you cannot specify destination IP addresses and ports when the value is set to TRUE.</p> <p>When set to FALSE (or not specified), you <i>must</i> specify a list of destination IP addresses that cannot receive traffic. A list of ports is optional. If you don't specify a list of ports, the ports that can accept traffic is the same as the ports configured for the endpoint group.</p> <p>The default value is FALSE.</p>"}}}, "DeprovisionByoipCidrRequest": {"type": "structure", "required": ["<PERSON><PERSON><PERSON>"], "members": {"Cidr": {"shape": "GenericString", "documentation": "<p>The address range, in CIDR notation. The prefix must be the same prefix that you specified when you provisioned the address range.</p> <p> For more information, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/using-byoip.html\">Bring your own IP addresses (BYOIP)</a> in the Global Accelerator Developer Guide.</p>"}}}, "DeprovisionByoipCidrResponse": {"type": "structure", "members": {"ByoipCidr": {"shape": "ByoipCidr", "documentation": "<p>Information about the address range.</p>"}}}, "DescribeAcceleratorAttributesRequest": {"type": "structure", "required": ["AcceleratorArn"], "members": {"AcceleratorArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the accelerator with the attributes that you want to describe.</p>"}}}, "DescribeAcceleratorAttributesResponse": {"type": "structure", "members": {"AcceleratorAttributes": {"shape": "AcceleratorAttributes", "documentation": "<p>The attributes of the accelerator.</p>"}}}, "DescribeAcceleratorRequest": {"type": "structure", "required": ["AcceleratorArn"], "members": {"AcceleratorArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the accelerator to describe.</p>"}}}, "DescribeAcceleratorResponse": {"type": "structure", "members": {"Accelerator": {"shape": "Accelerator", "documentation": "<p>The description of the accelerator.</p>"}}}, "DescribeCrossAccountAttachmentRequest": {"type": "structure", "required": ["AttachmentArn"], "members": {"AttachmentArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) for the cross-account attachment to describe.</p>"}}}, "DescribeCrossAccountAttachmentResponse": {"type": "structure", "members": {"CrossAccountAttachment": {"shape": "Attachment", "documentation": "<p>Information about the cross-account attachment.</p>"}}}, "DescribeCustomRoutingAcceleratorAttributesRequest": {"type": "structure", "required": ["AcceleratorArn"], "members": {"AcceleratorArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the custom routing accelerator to describe the attributes for.</p>"}}}, "DescribeCustomRoutingAcceleratorAttributesResponse": {"type": "structure", "members": {"AcceleratorAttributes": {"shape": "CustomRoutingAcceleratorAttributes", "documentation": "<p>The attributes of the custom routing accelerator.</p>"}}}, "DescribeCustomRoutingAcceleratorRequest": {"type": "structure", "required": ["AcceleratorArn"], "members": {"AcceleratorArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the accelerator to describe.</p>"}}}, "DescribeCustomRoutingAcceleratorResponse": {"type": "structure", "members": {"Accelerator": {"shape": "CustomRoutingAccelerator", "documentation": "<p>The description of the custom routing accelerator.</p>"}}}, "DescribeCustomRoutingEndpointGroupRequest": {"type": "structure", "required": ["EndpointGroupArn"], "members": {"EndpointGroupArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the endpoint group to describe.</p>"}}}, "DescribeCustomRoutingEndpointGroupResponse": {"type": "structure", "members": {"EndpointGroup": {"shape": "CustomRoutingEndpointGroup", "documentation": "<p>The description of an endpoint group for a custom routing accelerator.</p>"}}}, "DescribeCustomRoutingListenerRequest": {"type": "structure", "required": ["Listener<PERSON>rn"], "members": {"ListenerArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the listener to describe.</p>"}}}, "DescribeCustomRoutingListenerResponse": {"type": "structure", "members": {"Listener": {"shape": "CustomRoutingListener", "documentation": "<p>The description of a listener for a custom routing accelerator.</p>"}}}, "DescribeEndpointGroupRequest": {"type": "structure", "required": ["EndpointGroupArn"], "members": {"EndpointGroupArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the endpoint group to describe.</p>"}}}, "DescribeEndpointGroupResponse": {"type": "structure", "members": {"EndpointGroup": {"shape": "EndpointGroup", "documentation": "<p>The description of an endpoint group.</p>"}}}, "DescribeListenerRequest": {"type": "structure", "required": ["Listener<PERSON>rn"], "members": {"ListenerArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the listener to describe.</p>"}}}, "DescribeListenerResponse": {"type": "structure", "members": {"Listener": {"shape": "Listener", "documentation": "<p>The description of a listener.</p>"}}}, "DestinationAddresses": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "max": 100}, "DestinationPortMapping": {"type": "structure", "members": {"AcceleratorArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the custom routing accelerator that you have port mappings for.</p>"}, "AcceleratorSocketAddresses": {"shape": "SocketAddresses", "documentation": "<p>The IP address/port combinations (sockets) that map to a given destination socket address.</p>"}, "EndpointGroupArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the endpoint group.</p>"}, "EndpointId": {"shape": "GenericString", "documentation": "<p>The ID for the virtual private cloud (VPC) subnet.</p>"}, "EndpointGroupRegion": {"shape": "GenericString", "documentation": "<p>The Amazon Web Services Region for the endpoint group.</p>"}, "DestinationSocketAddress": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The endpoint IP address/port combination for traffic received on the accelerator socket address.</p>"}, "IpAddressType": {"shape": "IpAddressType", "documentation": "<p>The IP address type that an accelerator supports. For a custom routing accelerator, the value must be IPV4.</p>"}, "DestinationTrafficState": {"shape": "CustomRoutingDestinationTrafficState", "documentation": "<p>Indicates whether or not a port mapping destination can receive traffic. The value is either ALLOW, if traffic is allowed to the destination, or DENY, if traffic is not allowed to the destination.</p>"}}, "documentation": "<p>The port mappings for a specified endpoint IP address (destination).</p>"}, "DestinationPortMappings": {"type": "list", "member": {"shape": "DestinationPortMapping"}}, "DestinationPorts": {"type": "list", "member": {"shape": "PortNumber"}, "max": 100}, "EndpointAlreadyExistsException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The endpoint that you specified doesn't exist.</p>", "exception": true}, "EndpointConfiguration": {"type": "structure", "members": {"EndpointId": {"shape": "GenericString", "documentation": "<p>An ID for the endpoint. If the endpoint is a Network Load Balancer or Application Load Balancer, this is the Amazon Resource Name (ARN) of the resource. If the endpoint is an Elastic IP address, this is the Elastic IP address allocation ID. For Amazon EC2 instances, this is the EC2 instance ID. A resource must be valid and active when you add it as an endpoint.</p> <p>For cross-account endpoints, this must be the ARN of the resource.</p>"}, "Weight": {"shape": "EndpointWeight", "documentation": "<p>The weight associated with the endpoint. When you add weights to endpoints, you configure Global Accelerator to route traffic based on proportions that you specify. For example, you might specify endpoint weights of 4, 5, 5, and 6 (sum=20). The result is that 4/20 of your traffic, on average, is routed to the first endpoint, 5/20 is routed both to the second and third endpoints, and 6/20 is routed to the last endpoint. For more information, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/about-endpoints-endpoint-weights.html\">Endpoint weights</a> in the <i>Global Accelerator Developer Guide</i>.</p>"}, "ClientIPPreservationEnabled": {"shape": "GenericBoolean", "documentation": "<p>Indicates whether client IP address preservation is enabled for an endpoint. The value is true or false. The default value is true for Application Load Balancer endpoints. </p> <p>If the value is set to true, the client's IP address is preserved in the <code>X-Forwarded-For</code> request header as traffic travels to applications on the endpoint fronted by the accelerator.</p> <p>Client IP address preservation is supported, in specific Amazon Web Services Regions, for endpoints that are Application Load Balancers, Amazon EC2 instances, and Network Load Balancers with security groups. IMPORTANT: You cannot use client IP address preservation with Network Load Balancers with TLS listeners.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/preserve-client-ip-address.html\"> Preserve client IP addresses in Global Accelerator</a> in the <i>Global Accelerator Developer Guide</i>.</p>"}, "AttachmentArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the cross-account attachment that specifies the endpoints (resources) that can be added to accelerators and principals that have permission to add the endpoints.</p>"}}, "documentation": "<p>A complex type for endpoints. A resource must be valid and active when you add it as an endpoint.</p>"}, "EndpointConfigurations": {"type": "list", "member": {"shape": "EndpointConfiguration"}, "max": 10, "min": 0}, "EndpointDescription": {"type": "structure", "members": {"EndpointId": {"shape": "GenericString", "documentation": "<p>An ID for the endpoint. If the endpoint is a Network Load Balancer or Application Load Balancer, this is the Amazon Resource Name (ARN) of the resource. If the endpoint is an Elastic IP address, this is the Elastic IP address allocation ID. For Amazon EC2 instances, this is the EC2 instance ID. </p> <p>An Application Load Balancer can be either internal or internet-facing.</p>"}, "Weight": {"shape": "EndpointWeight", "documentation": "<p>The weight associated with the endpoint. When you add weights to endpoints, you configure Global Accelerator to route traffic based on proportions that you specify. For example, you might specify endpoint weights of 4, 5, 5, and 6 (sum=20). The result is that 4/20 of your traffic, on average, is routed to the first endpoint, 5/20 is routed both to the second and third endpoints, and 6/20 is routed to the last endpoint. For more information, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/about-endpoints-endpoint-weights.html\">Endpoint weights</a> in the <i>Global Accelerator Developer Guide</i>. </p>"}, "HealthState": {"shape": "HealthState", "documentation": "<p>The health status of the endpoint.</p>"}, "HealthReason": {"shape": "GenericString", "documentation": "<p>Returns a null result.</p>"}, "ClientIPPreservationEnabled": {"shape": "GenericBoolean", "documentation": "<p>Indicates whether client IP address preservation is enabled for an endpoint. The value is true or false. The default value is true for Application Load Balancers endpoints. </p> <p>If the value is set to true, the client's IP address is preserved in the <code>X-Forwarded-For</code> request header as traffic travels to applications on the endpoint fronted by the accelerator.</p> <p>Client IP address preservation is supported, in specific Amazon Web Services Regions, for endpoints that are Application Load Balancers, Amazon EC2 instances, and Network Load Balancers with security groups. IMPORTANT: You cannot use client IP address preservation with Network Load Balancers with TLS listeners.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/preserve-client-ip-address.html\"> Preserve client IP addresses in Global Accelerator</a> in the <i>Global Accelerator Developer Guide</i>.</p>"}}, "documentation": "<p>A complex type for an endpoint. Each endpoint group can include one or more endpoints, such as load balancers.</p>"}, "EndpointDescriptions": {"type": "list", "member": {"shape": "EndpointDescription"}}, "EndpointGroup": {"type": "structure", "members": {"EndpointGroupArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the endpoint group.</p>"}, "EndpointGroupRegion": {"shape": "GenericString", "documentation": "<p>The Amazon Web Services Region where the endpoint group is located.</p>"}, "EndpointDescriptions": {"shape": "EndpointDescriptions", "documentation": "<p>The list of endpoint objects.</p>"}, "TrafficDialPercentage": {"shape": "TrafficDialPercentage", "documentation": "<p>The percentage of traffic to send to an Amazon Web Services Region. Additional traffic is distributed to other endpoint groups for this listener. </p> <p>Use this action to increase (dial up) or decrease (dial down) traffic to a specific Region. The percentage is applied to the traffic that would otherwise have been routed to the Region based on optimal routing.</p> <p>The default value is 100.</p>"}, "HealthCheckPort": {"shape": "HealthCheckPort", "documentation": "<p>The port that Global Accelerator uses to perform health checks on endpoints that are part of this endpoint group. </p> <p>The default port is the port for the listener that this endpoint group is associated with. If the listener port is a list, Global Accelerator uses the first specified port in the list of ports.</p>"}, "HealthCheckProtocol": {"shape": "HealthCheckProtocol", "documentation": "<p>The protocol that Global Accelerator uses to perform health checks on endpoints that are part of this endpoint group. The default value is TCP.</p>"}, "HealthCheckPath": {"shape": "HealthCheckPath", "documentation": "<p>If the protocol is HTTP/S, then this value provides the ping path that Global Accelerator uses for the destination on the endpoints for health checks. The default is slash (/).</p>"}, "HealthCheckIntervalSeconds": {"shape": "HealthCheckIntervalSeconds", "documentation": "<p>The time—10 seconds or 30 seconds—between health checks for each endpoint. The default value is 30.</p>"}, "ThresholdCount": {"shape": "ThresholdCount", "documentation": "<p>The number of consecutive health checks required to set the state of a healthy endpoint to unhealthy, or to set an unhealthy endpoint to healthy. The default value is 3.</p>"}, "PortOverrides": {"shape": "PortOverrides", "documentation": "<p>Allows you to override the destination ports used to route traffic to an endpoint. Using a port override lets you map a list of external destination ports (that your users send traffic to) to a list of internal destination ports that you want an application endpoint to receive traffic on. </p>"}}, "documentation": "<p>A complex type for the endpoint group. An Amazon Web Services Region can have only one endpoint group for a specific listener. </p>"}, "EndpointGroupAlreadyExistsException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The endpoint group that you specified already exists.</p>", "exception": true}, "EndpointGroupNotFoundException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The endpoint group that you specified doesn't exist.</p>", "exception": true}, "EndpointGroups": {"type": "list", "member": {"shape": "EndpointGroup"}}, "EndpointIdentifier": {"type": "structure", "required": ["EndpointId"], "members": {"EndpointId": {"shape": "GenericString", "documentation": "<p>An ID for the endpoint. If the endpoint is a Network Load Balancer or Application Load Balancer, this is the Amazon Resource Name (ARN) of the resource. If the endpoint is an Elastic IP address, this is the Elastic IP address allocation ID. For Amazon EC2 instances, this is the EC2 instance ID. </p> <p>An Application Load Balancer can be either internal or internet-facing.</p>"}, "ClientIPPreservationEnabled": {"shape": "GenericBoolean", "documentation": "<p>Indicates whether client IP address preservation is enabled for an endpoint. The value is true or false. </p> <p>If the value is set to true, the client's IP address is preserved in the <code>X-Forwarded-For</code> request header as traffic travels to applications on the endpoint fronted by the accelerator.</p>"}}, "documentation": "<p>A complex type for an endpoint. Specifies information about the endpoint to remove from the endpoint group.</p>"}, "EndpointIdentifiers": {"type": "list", "member": {"shape": "EndpointIdentifier"}, "max": 10, "min": 1}, "EndpointIds": {"type": "list", "member": {"shape": "GenericString"}}, "EndpointNotFoundException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The endpoint that you specified doesn't exist.</p>", "exception": true}, "EndpointWeight": {"type": "integer", "max": 255, "min": 0}, "ErrorMessage": {"type": "string"}, "GenericBoolean": {"type": "boolean"}, "GenericString": {"type": "string", "max": 255}, "HealthCheckIntervalSeconds": {"type": "integer", "max": 30, "min": 10}, "HealthCheckPath": {"type": "string", "max": 255, "pattern": "^/[-a-zA-Z0-9@:%_\\\\+.~#?&/=]*$"}, "HealthCheckPort": {"type": "integer", "max": 65535, "min": 1}, "HealthCheckProtocol": {"type": "string", "enum": ["TCP", "HTTP", "HTTPS"]}, "HealthState": {"type": "string", "enum": ["INITIAL", "HEALTHY", "UNHEALTHY"]}, "IdempotencyToken": {"type": "string", "max": 255}, "IncorrectCidrStateException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The CIDR that you specified is not valid for this action. For example, the state of the CIDR might be incorrect for this action.</p>", "exception": true}, "InternalServiceErrorException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>There was an internal error for Global Accelerator.</p>", "exception": true}, "InvalidArgumentException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>An argument that you specified is invalid.</p>", "exception": true}, "InvalidNextTokenException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>There isn't another item to return.</p>", "exception": true}, "InvalidPortRangeException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The port numbers that you specified are not valid numbers or are not unique for this accelerator.</p>", "exception": true}, "IpAddress": {"type": "string", "max": 45}, "IpAddressFamily": {"type": "string", "enum": ["IPv4", "IPv6"]}, "IpAddressType": {"type": "string", "enum": ["IPV4", "DUAL_STACK"]}, "IpAddresses": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "max": 2, "min": 0}, "IpSet": {"type": "structure", "members": {"IpFamily": {"shape": "GenericString", "documentation": "<p>IpFamily is deprecated and has been replaced by IpAddressFamily.</p>", "deprecated": true, "deprecatedMessage": "IpFamily has been replaced by IpAddressFamily"}, "IpAddresses": {"shape": "IpAddresses", "documentation": "<p>The array of IP addresses in the IP address set. An IP address set can have a maximum of two IP addresses.</p>"}, "IpAddressFamily": {"shape": "IpAddressFamily", "documentation": "<p>The types of IP addresses included in this IP set. </p>"}}, "documentation": "<p>A complex type for the set of IP addresses for an accelerator.</p>"}, "IpSets": {"type": "list", "member": {"shape": "IpSet"}}, "LimitExceededException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>Processing your request would cause you to exceed an Global Accelerator limit.</p>", "exception": true}, "ListAcceleratorsRequest": {"type": "structure", "members": {"MaxResults": {"shape": "MaxResults", "documentation": "<p>The number of Global Accelerator objects that you want to return with this call. The default value is 10.</p>"}, "NextToken": {"shape": "GenericString", "documentation": "<p>The token for the next set of results. You receive this token from a previous call.</p>"}}}, "ListAcceleratorsResponse": {"type": "structure", "members": {"Accelerators": {"shape": "Accelerators", "documentation": "<p>The list of accelerators for a customer account.</p>"}, "NextToken": {"shape": "GenericString", "documentation": "<p>The token for the next set of results. You receive this token from a previous call.</p>"}}}, "ListByoipCidrsRequest": {"type": "structure", "members": {"MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return with a single call. To retrieve the remaining results, make another call with the returned <code>nextToken</code> value.</p>"}, "NextToken": {"shape": "GenericString", "documentation": "<p>The token for the next page of results.</p>"}}}, "ListByoipCidrsResponse": {"type": "structure", "members": {"ByoipCidrs": {"shape": "ByoipCidrs", "documentation": "<p>Information about your address ranges.</p>"}, "NextToken": {"shape": "GenericString", "documentation": "<p>The token for the next page of results.</p>"}}}, "ListCrossAccountAttachmentsRequest": {"type": "structure", "members": {"MaxResults": {"shape": "MaxResults", "documentation": "<p>The number of cross-account attachment objects that you want to return with this call. The default value is 10.</p>"}, "NextToken": {"shape": "GenericString", "documentation": "<p>The token for the next set of results. You receive this token from a previous call.</p>"}}}, "ListCrossAccountAttachmentsResponse": {"type": "structure", "members": {"CrossAccountAttachments": {"shape": "Attachments", "documentation": "<p>Information about the cross-account attachments.</p>"}, "NextToken": {"shape": "GenericString", "documentation": "<p>The token for the next set of results. You receive this token from a previous call.</p>"}}}, "ListCrossAccountResourceAccountsRequest": {"type": "structure", "members": {}}, "ListCrossAccountResourceAccountsResponse": {"type": "structure", "members": {"ResourceOwnerAwsAccountIds": {"shape": "AwsAccountIds", "documentation": "<p>The account IDs of principals (resource owners) in a cross-account attachment who can work with resources listed in the same attachment.</p>"}}}, "ListCrossAccountResourcesRequest": {"type": "structure", "required": ["ResourceOwnerAwsAccountId"], "members": {"AcceleratorArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of an accelerator in a cross-account attachment.</p>"}, "ResourceOwnerAwsAccountId": {"shape": "AwsAccountId", "documentation": "<p>The account ID of a resource owner in a cross-account attachment.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The number of cross-account resource objects that you want to return with this call. The default value is 10.</p>"}, "NextToken": {"shape": "GenericString", "documentation": "<p>The token for the next set of results. You receive this token from a previous call.</p>"}}}, "ListCrossAccountResourcesResponse": {"type": "structure", "members": {"CrossAccountResources": {"shape": "CrossAccountResources", "documentation": "<p>The cross-account resources used with an accelerator.</p>"}, "NextToken": {"shape": "GenericString", "documentation": "<p>The token for the next set of results. You receive this token from a previous call.</p>"}}}, "ListCustomRoutingAcceleratorsRequest": {"type": "structure", "members": {"MaxResults": {"shape": "MaxResults", "documentation": "<p>The number of custom routing Global Accelerator objects that you want to return with this call. The default value is 10.</p>"}, "NextToken": {"shape": "GenericString", "documentation": "<p>The token for the next set of results. You receive this token from a previous call.</p>"}}}, "ListCustomRoutingAcceleratorsResponse": {"type": "structure", "members": {"Accelerators": {"shape": "CustomRoutingAccelerators", "documentation": "<p>The list of custom routing accelerators for a customer account.</p>"}, "NextToken": {"shape": "GenericString", "documentation": "<p>The token for the next set of results. You receive this token from a previous call.</p>"}}}, "ListCustomRoutingEndpointGroupsRequest": {"type": "structure", "required": ["Listener<PERSON>rn"], "members": {"ListenerArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the listener to list endpoint groups for.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The number of endpoint group objects that you want to return with this call. The default value is 10.</p>"}, "NextToken": {"shape": "GenericString", "documentation": "<p>The token for the next set of results. You receive this token from a previous call.</p>"}}}, "ListCustomRoutingEndpointGroupsResponse": {"type": "structure", "members": {"EndpointGroups": {"shape": "CustomRoutingEndpointGroups", "documentation": "<p>The list of the endpoint groups associated with a listener for a custom routing accelerator.</p>"}, "NextToken": {"shape": "GenericString", "documentation": "<p>The token for the next set of results. You receive this token from a previous call.</p>"}}}, "ListCustomRoutingListenersRequest": {"type": "structure", "required": ["AcceleratorArn"], "members": {"AcceleratorArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the accelerator to list listeners for.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The number of listener objects that you want to return with this call. The default value is 10.</p>"}, "NextToken": {"shape": "GenericString", "documentation": "<p>The token for the next set of results. You receive this token from a previous call.</p>"}}}, "ListCustomRoutingListenersResponse": {"type": "structure", "members": {"Listeners": {"shape": "CustomRoutingListeners", "documentation": "<p>The list of listeners for a custom routing accelerator.</p>"}, "NextToken": {"shape": "GenericString", "documentation": "<p>The token for the next set of results. You receive this token from a previous call.</p>"}}}, "ListCustomRoutingPortMappingsByDestinationRequest": {"type": "structure", "required": ["EndpointId", "DestinationAddress"], "members": {"EndpointId": {"shape": "GenericString", "documentation": "<p>The ID for the virtual private cloud (VPC) subnet.</p>"}, "DestinationAddress": {"shape": "GenericString", "documentation": "<p>The endpoint IP address in a virtual private cloud (VPC) subnet for which you want to receive back port mappings.</p>"}, "MaxResults": {"shape": "PortMappingsMaxResults", "documentation": "<p>The number of destination port mappings that you want to return with this call. The default value is 10.</p>"}, "NextToken": {"shape": "GenericString", "documentation": "<p>The token for the next set of results. You receive this token from a previous call.</p>"}}}, "ListCustomRoutingPortMappingsByDestinationResponse": {"type": "structure", "members": {"DestinationPortMappings": {"shape": "DestinationPortMappings", "documentation": "<p>The port mappings for the endpoint IP address that you specified in the request.</p>"}, "NextToken": {"shape": "GenericString", "documentation": "<p>The token for the next set of results. You receive this token from a previous call.</p>"}}}, "ListCustomRoutingPortMappingsRequest": {"type": "structure", "required": ["AcceleratorArn"], "members": {"AcceleratorArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the accelerator to list the custom routing port mappings for.</p>"}, "EndpointGroupArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the endpoint group to list the custom routing port mappings for.</p>"}, "MaxResults": {"shape": "PortMappingsMaxResults", "documentation": "<p>The number of destination port mappings that you want to return with this call. The default value is 10.</p>"}, "NextToken": {"shape": "GenericString", "documentation": "<p>The token for the next set of results. You receive this token from a previous call.</p>"}}}, "ListCustomRoutingPortMappingsResponse": {"type": "structure", "members": {"PortMappings": {"shape": "PortMappings", "documentation": "<p>The port mappings for a custom routing accelerator.</p>"}, "NextToken": {"shape": "GenericString", "documentation": "<p>The token for the next set of results. You receive this token from a previous call.</p>"}}}, "ListEndpointGroupsRequest": {"type": "structure", "required": ["Listener<PERSON>rn"], "members": {"ListenerArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the listener.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The number of endpoint group objects that you want to return with this call. The default value is 10.</p>"}, "NextToken": {"shape": "GenericString", "documentation": "<p>The token for the next set of results. You receive this token from a previous call.</p>"}}}, "ListEndpointGroupsResponse": {"type": "structure", "members": {"EndpointGroups": {"shape": "EndpointGroups", "documentation": "<p>The list of the endpoint groups associated with a listener.</p>"}, "NextToken": {"shape": "GenericString", "documentation": "<p>The token for the next set of results. You receive this token from a previous call.</p>"}}}, "ListListenersRequest": {"type": "structure", "required": ["AcceleratorArn"], "members": {"AcceleratorArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the accelerator for which you want to list listener objects.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The number of listener objects that you want to return with this call. The default value is 10.</p>"}, "NextToken": {"shape": "GenericString", "documentation": "<p>The token for the next set of results. You receive this token from a previous call.</p>"}}}, "ListListenersResponse": {"type": "structure", "members": {"Listeners": {"shape": "Listeners", "documentation": "<p>The list of listeners for an accelerator.</p>"}, "NextToken": {"shape": "GenericString", "documentation": "<p>The token for the next set of results. You receive this token from a previous call.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the accelerator to list tags for. An ARN uniquely identifies an accelerator.</p>"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"Tags": {"shape": "Tags", "documentation": "<p>Root level tag for the Tags parameters.</p>"}}}, "Listener": {"type": "structure", "members": {"ListenerArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the listener.</p>"}, "PortRanges": {"shape": "PortRanges", "documentation": "<p>The list of port ranges for the connections from clients to the accelerator.</p>"}, "Protocol": {"shape": "Protocol", "documentation": "<p>The protocol for the connections from clients to the accelerator.</p>"}, "ClientAffinity": {"shape": "ClientAffinity", "documentation": "<p>Client affinity lets you direct all requests from a user to the same endpoint, if you have stateful applications, regardless of the port and protocol of the client request. Client affinity gives you control over whether to always route each client to the same specific endpoint.</p> <p>Global Accelerator uses a consistent-flow hashing algorithm to choose the optimal endpoint for a connection. If client affinity is <code>NONE</code>, Global Accelerator uses the \"five-tuple\" (5-tuple) properties—source IP address, source port, destination IP address, destination port, and protocol—to select the hash value, and then chooses the best endpoint. However, with this setting, if someone uses different ports to connect to Global Accelerator, their connections might not be always routed to the same endpoint because the hash value changes. </p> <p>If you want a given client to always be routed to the same endpoint, set client affinity to <code>SOURCE_IP</code> instead. When you use the <code>SOURCE_IP</code> setting, Global Accelerator uses the \"two-tuple\" (2-tuple) properties— source (client) IP address and destination IP address—to select the hash value.</p> <p>The default value is <code>NONE</code>.</p>"}}, "documentation": "<p>A complex type for a listener.</p>"}, "ListenerNotFoundException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The listener that you specified doesn't exist.</p>", "exception": true}, "Listeners": {"type": "list", "member": {"shape": "Listener"}}, "MaxResults": {"type": "integer", "max": 100, "min": 1}, "PortMapping": {"type": "structure", "members": {"AcceleratorPort": {"shape": "PortNumber", "documentation": "<p>The accelerator port.</p>"}, "EndpointGroupArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the endpoint group.</p>"}, "EndpointId": {"shape": "GenericString", "documentation": "<p>The IP address of the VPC subnet (the subnet ID).</p>"}, "DestinationSocketAddress": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The EC2 instance IP address and port number in the virtual private cloud (VPC) subnet.</p>"}, "Protocols": {"shape": "CustomRoutingProtocols", "documentation": "<p>The protocols supported by the endpoint group.</p>"}, "DestinationTrafficState": {"shape": "CustomRoutingDestinationTrafficState", "documentation": "<p>Indicates whether or not a port mapping destination can receive traffic. The value is either ALLOW, if traffic is allowed to the destination, or DENY, if traffic is not allowed to the destination.</p>"}}, "documentation": "<p>Returns the ports and associated IP addresses and ports of Amazon EC2 instances in your virtual private cloud (VPC) subnets. Custom routing is a port mapping protocol in Global Accelerator that statically associates port ranges with VPC subnets, which allows Global Accelerator to route to specific instances and ports within one or more subnets. </p>"}, "PortMappings": {"type": "list", "member": {"shape": "PortMapping"}}, "PortMappingsMaxResults": {"type": "integer", "max": 20000, "min": 1}, "PortNumber": {"type": "integer", "max": 65535, "min": 1}, "PortOverride": {"type": "structure", "members": {"ListenerPort": {"shape": "PortNumber", "documentation": "<p>The listener port that you want to map to a specific endpoint port. This is the port that user traffic arrives to the Global Accelerator on.</p>"}, "EndpointPort": {"shape": "PortNumber", "documentation": "<p>The endpoint port that you want a listener port to be mapped to. This is the port on the endpoint, such as the Application Load Balancer or Amazon EC2 instance.</p>"}}, "documentation": "<p>Override specific listener ports used to route traffic to endpoints that are part of an endpoint group. For example, you can create a port override in which the listener receives user traffic on ports 80 and 443, but your accelerator routes that traffic to ports 1080 and 1443, respectively, on the endpoints.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/about-endpoint-groups-port-override.html\"> Overriding listener ports</a> in the <i>Global Accelerator Developer Guide</i>.</p>"}, "PortOverrides": {"type": "list", "member": {"shape": "PortOverride"}, "max": 10, "min": 0}, "PortRange": {"type": "structure", "members": {"FromPort": {"shape": "PortNumber", "documentation": "<p>The first port in the range of ports, inclusive.</p>"}, "ToPort": {"shape": "PortNumber", "documentation": "<p>The last port in the range of ports, inclusive.</p>"}}, "documentation": "<p>A complex type for a range of ports for a listener.</p>"}, "PortRanges": {"type": "list", "member": {"shape": "PortRange"}, "max": 10, "min": 1}, "Principal": {"type": "string", "max": 256, "pattern": "(^\\d{12}$|arn:.*)"}, "Principals": {"type": "list", "member": {"shape": "Principal"}}, "Protocol": {"type": "string", "enum": ["TCP", "UDP"]}, "Protocols": {"type": "list", "member": {"shape": "Protocol"}}, "ProvisionByoipCidrRequest": {"type": "structure", "required": ["<PERSON><PERSON><PERSON>", "CidrAuthorizationContext"], "members": {"Cidr": {"shape": "GenericString", "documentation": "<p>The public IPv4 address range, in CIDR notation. The most specific IP prefix that you can specify is /24. The address range cannot overlap with another address range that you've brought to this Amazon Web Services Region or another Region.</p> <p> For more information, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/using-byoip.html\">Bring your own IP addresses (BYOIP)</a> in the Global Accelerator Developer Guide.</p>"}, "CidrAuthorizationContext": {"shape": "CidrAuthorizationContext", "documentation": "<p>A signed document that proves that you are authorized to bring the specified IP address range to Amazon using BYOIP. </p>"}}}, "ProvisionByoipCidrResponse": {"type": "structure", "members": {"ByoipCidr": {"shape": "ByoipCidr", "documentation": "<p>Information about the address range.</p>"}}}, "RemoveCustomRoutingEndpointsRequest": {"type": "structure", "required": ["EndpointIds", "EndpointGroupArn"], "members": {"EndpointIds": {"shape": "EndpointIds", "documentation": "<p>The IDs for the endpoints. For custom routing accelerators, endpoint IDs are the virtual private cloud (VPC) subnet IDs. </p>"}, "EndpointGroupArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the endpoint group to remove endpoints from.</p>"}}}, "RemoveEndpointsRequest": {"type": "structure", "required": ["EndpointIdentifiers", "EndpointGroupArn"], "members": {"EndpointIdentifiers": {"shape": "EndpointIdentifiers", "documentation": "<p>The identifiers of the endpoints that you want to remove.</p>"}, "EndpointGroupArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the endpoint group.</p>"}}}, "Resource": {"type": "structure", "members": {"EndpointId": {"shape": "GenericString", "documentation": "<p>The endpoint ID for the endpoint that is specified as a Amazon Web Services resource. </p> <p>An endpoint ID for the cross-account feature is the ARN of an Amazon Web Services resource, such as a Network Load Balancer, that Global Accelerator supports as an endpoint for an accelerator.</p>"}, "Cidr": {"shape": "GenericString", "documentation": "<p>An IP address range, in CIDR format, that is specified as resource. The address must be provisioned and advertised in Global Accelerator by following the bring your own IP address (BYOIP) process for Global Accelerator</p> <p> For more information, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/using-byoip.html\">Bring your own IP addresses (BYOIP)</a> in the Global Accelerator Developer Guide.</p>"}, "Region": {"shape": "GenericString", "documentation": "<p>The Amazon Web Services Region where a shared endpoint resource is located.</p>"}}, "documentation": "<p>A resource is one of the following: the ARN for an Amazon Web Services resource that is supported by Global Accelerator to be added as an endpoint, or a CIDR range that specifies a bring your own IP (BYOIP) address pool.</p>"}, "ResourceArn": {"type": "string", "max": 1011, "min": 1}, "Resources": {"type": "list", "member": {"shape": "Resource"}}, "SocketAddress": {"type": "structure", "members": {"IpAddress": {"shape": "GenericString", "documentation": "<p>The IP address for the socket address.</p>"}, "Port": {"shape": "PortNumber", "documentation": "<p>The port for the socket address.</p>"}}, "documentation": "<p>An IP address/port combination.</p>"}, "SocketAddresses": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "Tag": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {"shape": "TagKey", "documentation": "<p>A string that contains a <code>Tag</code> key.</p>"}, "Value": {"shape": "TagValue", "documentation": "<p>A string that contains a <code>Tag</code> value.</p>"}}, "documentation": "<p>A complex type that contains a <code>Tag</code> key and <code>Tag</code> value.</p>"}, "TagKey": {"type": "string", "max": 128, "min": 1}, "TagKeys": {"type": "list", "member": {"shape": "TagKey"}, "max": 200, "min": 0}, "TagResourceRequest": {"type": "structure", "required": ["ResourceArn", "Tags"], "members": {"ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the Global Accelerator resource to add tags to. An ARN uniquely identifies a resource.</p>"}, "Tags": {"shape": "Tags", "documentation": "<p>The tags to add to a resource. A tag consists of a key and a value that you define.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0}, "Tags": {"type": "list", "member": {"shape": "Tag"}}, "ThresholdCount": {"type": "integer", "max": 10, "min": 1}, "Timestamp": {"type": "timestamp"}, "TrafficDialPercentage": {"type": "float", "max": 100, "min": 0}, "TransactionInProgressException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>There's already a transaction in progress. Another transaction can't be processed.</p>", "exception": true}, "UntagResourceRequest": {"type": "structure", "required": ["ResourceArn", "TagKeys"], "members": {"ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the Global Accelerator resource to remove tags from. An ARN uniquely identifies a resource.</p>"}, "TagKeys": {"shape": "TagKeys", "documentation": "<p>The tag key pairs that you want to remove from the specified resources.</p>"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateAcceleratorAttributesRequest": {"type": "structure", "required": ["AcceleratorArn"], "members": {"AcceleratorArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the accelerator that you want to update.</p>"}, "FlowLogsEnabled": {"shape": "GenericBoolean", "documentation": "<p>Update whether flow logs are enabled. The default value is false. If the value is true, <code>FlowLogsS3Bucket</code> and <code>FlowLogsS3Prefix</code> must be specified.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/monitoring-global-accelerator.flow-logs.html\">Flow Logs</a> in the <i>Global Accelerator Developer Guide</i>.</p>"}, "FlowLogsS3Bucket": {"shape": "GenericString", "documentation": "<p>The name of the Amazon S3 bucket for the flow logs. Attribute is required if <code>FlowLogsEnabled</code> is <code>true</code>. The bucket must exist and have a bucket policy that grants Global Accelerator permission to write to the bucket.</p>"}, "FlowLogsS3Prefix": {"shape": "GenericString", "documentation": "<p>Update the prefix for the location in the Amazon S3 bucket for the flow logs. Attribute is required if <code>FlowLogsEnabled</code> is <code>true</code>. </p> <p>If you specify slash (/) for the S3 bucket prefix, the log file bucket folder structure will include a double slash (//), like the following:</p> <p>s3-bucket_name//AWSLogs/aws_account_id</p>"}}}, "UpdateAcceleratorAttributesResponse": {"type": "structure", "members": {"AcceleratorAttributes": {"shape": "AcceleratorAttributes", "documentation": "<p>Updated attributes for the accelerator.</p>"}}}, "UpdateAcceleratorRequest": {"type": "structure", "required": ["AcceleratorArn"], "members": {"AcceleratorArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the accelerator to update.</p>"}, "Name": {"shape": "GenericString", "documentation": "<p>The name of the accelerator. The name can have a maximum of 64 characters, must contain only alphanumeric characters, periods (.), or hyphens (-), and must not begin or end with a hyphen or period.</p>"}, "IpAddressType": {"shape": "IpAddressType", "documentation": "<p>The IP address type that an accelerator supports. For a standard accelerator, the value can be IPV4 or DUAL_STACK.</p>"}, "IpAddresses": {"shape": "IpAddresses", "documentation": "<p>The IP addresses for an accelerator.</p>"}, "Enabled": {"shape": "GenericBoolean", "documentation": "<p>Indicates whether an accelerator is enabled. The value is true or false. The default value is true. </p> <p>If the value is set to true, the accelerator cannot be deleted. If set to false, the accelerator can be deleted.</p>"}}}, "UpdateAcceleratorResponse": {"type": "structure", "members": {"Accelerator": {"shape": "Accelerator", "documentation": "<p>Information about the updated accelerator.</p>"}}}, "UpdateCrossAccountAttachmentRequest": {"type": "structure", "required": ["AttachmentArn"], "members": {"AttachmentArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the cross-account attachment to update.</p>"}, "Name": {"shape": "AttachmentName", "documentation": "<p>The name of the cross-account attachment. </p>"}, "AddPrincipals": {"shape": "Principals", "documentation": "<p>The principals to add to the cross-account attachment. A principal is an account or the Amazon Resource Name (ARN) of an accelerator that the attachment gives permission to work with resources from another account. The resources are also listed in the attachment.</p> <p>To add more than one principal, separate the account numbers or accelerator ARNs, or both, with commas.</p>"}, "RemovePrincipals": {"shape": "Principals", "documentation": "<p>The principals to remove from the cross-account attachment. A principal is an account or the Amazon Resource Name (ARN) of an accelerator that the attachment gives permission to work with resources from another account. The resources are also listed in the attachment.</p> <p>To remove more than one principal, separate the account numbers or accelerator ARNs, or both, with commas.</p>"}, "AddResources": {"shape": "Resources", "documentation": "<p>The resources to add to the cross-account attachment. A resource listed in a cross-account attachment can be used with an accelerator by the principals that are listed in the attachment.</p> <p>To add more than one resource, separate the resource ARNs with commas.</p>"}, "RemoveResources": {"shape": "Resources", "documentation": "<p>The resources to remove from the cross-account attachment. A resource listed in a cross-account attachment can be used with an accelerator by the principals that are listed in the attachment.</p> <p>To remove more than one resource, separate the resource ARNs with commas.</p>"}}}, "UpdateCrossAccountAttachmentResponse": {"type": "structure", "members": {"CrossAccountAttachment": {"shape": "Attachment", "documentation": "<p>Information about the updated cross-account attachment.</p>"}}}, "UpdateCustomRoutingAcceleratorAttributesRequest": {"type": "structure", "required": ["AcceleratorArn"], "members": {"AcceleratorArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the custom routing accelerator to update attributes for.</p>"}, "FlowLogsEnabled": {"shape": "GenericBoolean", "documentation": "<p>Update whether flow logs are enabled. The default value is false. If the value is true, <code>FlowLogsS3Bucket</code> and <code>FlowLogsS3Prefix</code> must be specified.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/monitoring-global-accelerator.flow-logs.html\">Flow logs</a> in the <i>Global Accelerator Developer Guide</i>.</p>"}, "FlowLogsS3Bucket": {"shape": "GenericString", "documentation": "<p>The name of the Amazon S3 bucket for the flow logs. Attribute is required if <code>FlowLogsEnabled</code> is <code>true</code>. The bucket must exist and have a bucket policy that grants Global Accelerator permission to write to the bucket.</p>"}, "FlowLogsS3Prefix": {"shape": "GenericString", "documentation": "<p>Update the prefix for the location in the Amazon S3 bucket for the flow logs. Attribute is required if <code>FlowLogsEnabled</code> is <code>true</code>. </p> <p>If you don’t specify a prefix, the flow logs are stored in the root of the bucket. If you specify slash (/) for the S3 bucket prefix, the log file bucket folder structure will include a double slash (//), like the following:</p> <p>DOC-EXAMPLE-BUCKET//AWSLogs/aws_account_id</p>"}}}, "UpdateCustomRoutingAcceleratorAttributesResponse": {"type": "structure", "members": {"AcceleratorAttributes": {"shape": "CustomRoutingAcceleratorAttributes", "documentation": "<p>Updated custom routing accelerator.</p>"}}}, "UpdateCustomRoutingAcceleratorRequest": {"type": "structure", "required": ["AcceleratorArn"], "members": {"AcceleratorArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the accelerator to update.</p>"}, "Name": {"shape": "GenericString", "documentation": "<p>The name of the accelerator. The name can have a maximum of 64 characters, must contain only alphanumeric characters, periods (.), or hyphens (-), and must not begin or end with a hyphen or period.</p>"}, "IpAddressType": {"shape": "IpAddressType", "documentation": "<p>The IP address type that an accelerator supports. For a custom routing accelerator, the value must be IPV4.</p>"}, "IpAddresses": {"shape": "IpAddresses", "documentation": "<p>The IP addresses for an accelerator.</p>"}, "Enabled": {"shape": "GenericBoolean", "documentation": "<p>Indicates whether an accelerator is enabled. The value is true or false. The default value is true. </p> <p>If the value is set to true, the accelerator cannot be deleted. If set to false, the accelerator can be deleted.</p>"}}}, "UpdateCustomRoutingAcceleratorResponse": {"type": "structure", "members": {"Accelerator": {"shape": "CustomRoutingAccelerator", "documentation": "<p>Information about the updated custom routing accelerator.</p>"}}}, "UpdateCustomRoutingListenerRequest": {"type": "structure", "required": ["Listener<PERSON>rn", "PortRanges"], "members": {"ListenerArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the listener to update.</p>"}, "PortRanges": {"shape": "PortRanges", "documentation": "<p>The updated port range to support for connections from clients to your accelerator. If you remove ports that are currently being used by a subnet endpoint, the call fails.</p> <p>Separately, you set port ranges for endpoints. For more information, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/about-custom-routing-endpoints.html\">About endpoints for custom routing accelerators</a>.</p>"}}}, "UpdateCustomRoutingListenerResponse": {"type": "structure", "members": {"Listener": {"shape": "CustomRoutingListener", "documentation": "<p>Information for the updated listener for a custom routing accelerator.</p>"}}}, "UpdateEndpointGroupRequest": {"type": "structure", "required": ["EndpointGroupArn"], "members": {"EndpointGroupArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the endpoint group.</p>"}, "EndpointConfigurations": {"shape": "EndpointConfigurations", "documentation": "<p>The list of endpoint objects. A resource must be valid and active when you add it as an endpoint.</p>"}, "TrafficDialPercentage": {"shape": "TrafficDialPercentage", "documentation": "<p>The percentage of traffic to send to an Amazon Web Services Region. Additional traffic is distributed to other endpoint groups for this listener. </p> <p>Use this action to increase (dial up) or decrease (dial down) traffic to a specific Region. The percentage is applied to the traffic that would otherwise have been routed to the Region based on optimal routing.</p> <p>The default value is 100.</p>"}, "HealthCheckPort": {"shape": "HealthCheckPort", "documentation": "<p>The port that Global Accelerator uses to check the health of endpoints that are part of this endpoint group. The default port is the listener port that this endpoint group is associated with. If the listener port is a list of ports, Global Accelerator uses the first port in the list.</p>"}, "HealthCheckProtocol": {"shape": "HealthCheckProtocol", "documentation": "<p>The protocol that Global Accelerator uses to check the health of endpoints that are part of this endpoint group. The default value is TCP.</p>"}, "HealthCheckPath": {"shape": "HealthCheckPath", "documentation": "<p>If the protocol is HTTP/S, then this specifies the path that is the destination for health check targets. The default value is slash (/).</p>"}, "HealthCheckIntervalSeconds": {"shape": "HealthCheckIntervalSeconds", "documentation": "<p>The time—10 seconds or 30 seconds—between each health check for an endpoint. The default value is 30.</p>"}, "ThresholdCount": {"shape": "ThresholdCount", "documentation": "<p>The number of consecutive health checks required to set the state of a healthy endpoint to unhealthy, or to set an unhealthy endpoint to healthy. The default value is 3.</p>"}, "PortOverrides": {"shape": "PortOverrides", "documentation": "<p>Override specific listener ports used to route traffic to endpoints that are part of this endpoint group. For example, you can create a port override in which the listener receives user traffic on ports 80 and 443, but your accelerator routes that traffic to ports 1080 and 1443, respectively, on the endpoints.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/about-endpoint-groups-port-override.html\"> Overriding listener ports</a> in the <i>Global Accelerator Developer Guide</i>.</p>"}}}, "UpdateEndpointGroupResponse": {"type": "structure", "members": {"EndpointGroup": {"shape": "EndpointGroup", "documentation": "<p>The information about the endpoint group that was updated.</p>"}}}, "UpdateListenerRequest": {"type": "structure", "required": ["Listener<PERSON>rn"], "members": {"ListenerArn": {"shape": "GenericString", "documentation": "<p>The Amazon Resource Name (ARN) of the listener to update.</p>"}, "PortRanges": {"shape": "PortRanges", "documentation": "<p>The updated list of port ranges for the connections from clients to the accelerator.</p>"}, "Protocol": {"shape": "Protocol", "documentation": "<p>The updated protocol for the connections from clients to the accelerator.</p>"}, "ClientAffinity": {"shape": "ClientAffinity", "documentation": "<p>Client affinity lets you direct all requests from a user to the same endpoint, if you have stateful applications, regardless of the port and protocol of the client request. Client affinity gives you control over whether to always route each client to the same specific endpoint.</p> <p>Global Accelerator uses a consistent-flow hashing algorithm to choose the optimal endpoint for a connection. If client affinity is <code>NONE</code>, Global Accelerator uses the \"five-tuple\" (5-tuple) properties—source IP address, source port, destination IP address, destination port, and protocol—to select the hash value, and then chooses the best endpoint. However, with this setting, if someone uses different ports to connect to Global Accelerator, their connections might not be always routed to the same endpoint because the hash value changes. </p> <p>If you want a given client to always be routed to the same endpoint, set client affinity to <code>SOURCE_IP</code> instead. When you use the <code>SOURCE_IP</code> setting, Global Accelerator uses the \"two-tuple\" (2-tuple) properties— source (client) IP address and destination IP address—to select the hash value.</p> <p>The default value is <code>NONE</code>.</p>"}}}, "UpdateListenerResponse": {"type": "structure", "members": {"Listener": {"shape": "Listener", "documentation": "<p>Information for the updated listener.</p>"}}}, "WithdrawByoipCidrRequest": {"type": "structure", "required": ["<PERSON><PERSON><PERSON>"], "members": {"Cidr": {"shape": "GenericString", "documentation": "<p>The address range, in CIDR notation.</p> <p> For more information, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/using-byoip.html\">Bring your own IP addresses (BYOIP)</a> in the Global Accelerator Developer Guide.</p>"}}}, "WithdrawByoipCidrResponse": {"type": "structure", "members": {"ByoipCidr": {"shape": "ByoipCidr", "documentation": "<p>Information about the BYOIP address pool.</p>"}}}}, "documentation": "<fullname>Global Accelerator</fullname> <p>This is the <i>Global Accelerator API Reference</i>. This guide is for developers who need detailed information about Global Accelerator API actions, data types, and errors. For more information about Global Accelerator features, see the <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/what-is-global-accelerator.html\">Global Accelerator Developer Guide</a>.</p> <p>Global Accelerator is a service in which you create <i>accelerators</i> to improve the performance of your applications for local and global users. Depending on the type of accelerator you choose, you can gain additional benefits. </p> <ul> <li> <p>By using a standard accelerator, you can improve availability of your internet applications that are used by a global audience. With a standard accelerator, Global Accelerator directs traffic to optimal endpoints over the Amazon Web Services global network. </p> </li> <li> <p>For other scenarios, you might choose a custom routing accelerator. With a custom routing accelerator, you can use application logic to directly map one or more users to a specific endpoint among many endpoints.</p> </li> </ul> <important> <p>Global Accelerator is a global service that supports endpoints in multiple Amazon Web Services Regions but you must specify the US West (Oregon) Region to create, update, or otherwise work with accelerators. That is, for example, specify <code>--region us-west-2</code> on Amazon Web Services CLI commands.</p> </important> <p>By default, Global Accelerator provides you with static IP addresses that you associate with your accelerator. The static IP addresses are anycast from the Amazon Web Services edge network. For IPv4, Global Accelerator provides two static IPv4 addresses. For dual-stack, Global Accelerator provides a total of four addresses: two static IPv4 addresses and two static IPv6 addresses. With a standard accelerator for IPv4, instead of using the addresses that Global Accelerator provides, you can configure these entry points to be IPv4 addresses from your own IP address ranges that you bring to Global Accelerator (BYOIP). </p> <p>For a standard accelerator, they distribute incoming application traffic across multiple endpoint resources in multiple Amazon Web Services Regions , which increases the availability of your applications. Endpoints for standard accelerators can be Network Load Balancers, Application Load Balancers, Amazon EC2 instances, or Elastic IP addresses that are located in one Amazon Web Services Region or multiple Amazon Web Services Regions. For custom routing accelerators, you map traffic that arrives to the static IP addresses to specific Amazon EC2 servers in endpoints that are virtual private cloud (VPC) subnets.</p> <important> <p>The static IP addresses remain assigned to your accelerator for as long as it exists, even if you disable the accelerator and it no longer accepts or routes traffic. However, when you <i>delete</i> an accelerator, you lose the static IP addresses that are assigned to it, so you can no longer route traffic by using them. You can use IAM policies like tag-based permissions with Global Accelerator to limit the users who have permissions to delete an accelerator. For more information, see <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/access-control-manage-access-tag-policies.html\">Tag-based policies</a>.</p> </important> <p>For standard accelerators, Global Accelerator uses the Amazon Web Services global network to route traffic to the optimal regional endpoint based on health, client location, and policies that you configure. The service reacts instantly to changes in health or configuration to ensure that internet traffic from clients is always directed to healthy endpoints.</p> <p>For more information about understanding and using Global Accelerator, see the <a href=\"https://docs.aws.amazon.com/global-accelerator/latest/dg/what-is-global-accelerator.html\">Global Accelerator Developer Guide</a>.</p>"}