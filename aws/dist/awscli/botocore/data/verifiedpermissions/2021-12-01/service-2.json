{"version": "2.0", "metadata": {"apiVersion": "2021-12-01", "auth": ["aws.auth#sigv4"], "endpointPrefix": "verifiedpermissions", "jsonVersion": "1.0", "protocol": "json", "protocols": ["json"], "serviceFullName": "Amazon Verified Permissions", "serviceId": "VerifiedPermissions", "signatureVersion": "v4", "signingName": "verifiedpermissions", "targetPrefix": "VerifiedPermissions", "uid": "verifiedpermissions-2021-12-01"}, "operations": {"BatchGetPolicy": {"name": "BatchGetPolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "BatchGetPolicyInput"}, "output": {"shape": "BatchGetPolicyOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Retrieves information about a group (batch) of policies.</p> <note> <p>The <code>BatchGetPolicy</code> operation doesn't have its own IAM permission. To authorize this operation for Amazon Web Services principals, include the permission <code>verifiedpermissions:GetPolicy</code> in their IAM policies.</p> </note>"}, "BatchIsAuthorized": {"name": "BatchIsAuthorized", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "BatchIsAuthorizedInput"}, "output": {"shape": "BatchIsAuthorizedOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Makes a series of decisions about multiple authorization requests for one principal or resource. Each request contains the equivalent content of an <code>IsAuthorized</code> request: principal, action, resource, and context. Either the <code>principal</code> or the <code>resource</code> parameter must be identical across all requests. For example, Verified Permissions won't evaluate a pair of requests where <code>bob</code> views <code>photo1</code> and <code>alice</code> views <code>photo2</code>. Authorization of <code>bob</code> to view <code>photo1</code> and <code>photo2</code>, or <code>bob</code> and <code>alice</code> to view <code>photo1</code>, are valid batches. </p> <p>The request is evaluated against all policies in the specified policy store that match the entities that you declare. The result of the decisions is a series of <code>Allow</code> or <code>Deny</code> responses, along with the IDs of the policies that produced each decision.</p> <p>The <code>entities</code> of a <code>BatchIsAuthorized</code> API request can contain up to 100 principals and up to 100 resources. The <code>requests</code> of a <code>BatchIsAuthorized</code> API request can contain up to 30 requests.</p> <note> <p>The <code>BatchIsAuthorized</code> operation doesn't have its own IAM permission. To authorize this operation for Amazon Web Services principals, include the permission <code>verifiedpermissions:IsAuthorized</code> in their IAM policies.</p> </note>"}, "BatchIsAuthorizedWithToken": {"name": "BatchIsAuthorizedWithToken", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "BatchIsAuthorizedWithTokenInput"}, "output": {"shape": "BatchIsAuthorizedWithTokenOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Makes a series of decisions about multiple authorization requests for one token. The principal in this request comes from an external identity source in the form of an identity or access token, formatted as a <a href=\"https://wikipedia.org/wiki/JSON_Web_Token\">JSON web token (JWT)</a>. The information in the parameters can also define additional context that Verified Permissions can include in the evaluations.</p> <p>The request is evaluated against all policies in the specified policy store that match the entities that you provide in the entities declaration and in the token. The result of the decisions is a series of <code>Allow</code> or <code>Deny</code> responses, along with the IDs of the policies that produced each decision.</p> <p>The <code>entities</code> of a <code>BatchIsAuthorizedWithToken</code> API request can contain up to 100 resources and up to 99 user groups. The <code>requests</code> of a <code>BatchIsAuthorizedWithToken</code> API request can contain up to 30 requests.</p> <note> <p>The <code>BatchIsAuthorizedWithToken</code> operation doesn't have its own IAM permission. To authorize this operation for Amazon Web Services principals, include the permission <code>verifiedpermissions:IsAuthorizedWithToken</code> in their IAM policies.</p> </note>"}, "CreateIdentitySource": {"name": "CreateIdentitySource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateIdentitySourceInput"}, "output": {"shape": "CreateIdentitySourceOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Adds an identity source to a policy store–an Amazon Cognito user pool or OpenID Connect (OIDC) identity provider (IdP). </p> <p>After you create an identity source, you can use the identities provided by the IdP as proxies for the principal in authorization queries that use the <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_IsAuthorizedWithToken.html\">IsAuthorizedWithToken</a> or <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_BatchIsAuthorizedWithToken.html\">BatchIsAuthorizedWithToken</a> API operations. These identities take the form of tokens that contain claims about the user, such as IDs, attributes and group memberships. Identity sources provide identity (ID) tokens and access tokens. Verified Permissions derives information about your user and session from token claims. Access tokens provide action <code>context</code> to your policies, and ID tokens provide principal <code>Attributes</code>.</p> <important> <p>Tokens from an identity source user continue to be usable until they expire. Token revocation and resource deletion have no effect on the validity of a token in your policy store</p> </important> <note> <p>To reference a user from this identity source in your Cedar policies, refer to the following syntax examples.</p> <ul> <li> <p>Amazon Cognito user pool: <code>Namespace::[Entity type]::[User pool ID]|[user principal attribute]</code>, for example <code>MyCorp::User::us-east-1_EXAMPLE|a1b2c3d4-5678-90ab-cdef-EXAMPLE11111</code>.</p> </li> <li> <p>OpenID Connect (OIDC) provider: <code>Namespace::[Entity type]::[entityIdPrefix]|[user principal attribute]</code>, for example <code>MyCorp::User::MyOIDCProvider|a1b2c3d4-5678-90ab-cdef-EXAMPLE22222</code>.</p> </li> </ul> </note> <note> <p>Verified Permissions is <i> <a href=\"https://wikipedia.org/wiki/Eventual_consistency\">eventually consistent</a> </i>. It can take a few seconds for a new or changed element to propagate through the service and be visible in the results of other Verified Permissions operations.</p> </note>", "idempotent": true}, "CreatePolicy": {"name": "CreatePolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreatePolicyInput"}, "output": {"shape": "CreatePolicyOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates a Cedar policy and saves it in the specified policy store. You can create either a static policy or a policy linked to a policy template.</p> <ul> <li> <p>To create a static policy, provide the Cedar policy text in the <code>StaticPolicy</code> section of the <code>PolicyDefinition</code>.</p> </li> <li> <p>To create a policy that is dynamically linked to a policy template, specify the policy template ID and the principal and resource to associate with this policy in the <code>templateLinked</code> section of the <code>PolicyDefinition</code>. If the policy template is ever updated, any policies linked to the policy template automatically use the updated template.</p> </li> </ul> <note> <p>Creating a policy causes it to be validated against the schema in the policy store. If the policy doesn't pass validation, the operation fails and the policy isn't stored.</p> </note> <note> <p>Verified Permissions is <i> <a href=\"https://wikipedia.org/wiki/Eventual_consistency\">eventually consistent</a> </i>. It can take a few seconds for a new or changed element to propagate through the service and be visible in the results of other Verified Permissions operations.</p> </note>", "idempotent": true}, "CreatePolicyStore": {"name": "CreatePolicyStore", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreatePolicyStoreInput"}, "output": {"shape": "CreatePolicyStoreOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates a policy store. A policy store is a container for policy resources.</p> <note> <p>Although <a href=\"https://docs.cedarpolicy.com/schema/schema.html#namespace\">Cedar supports multiple namespaces</a>, Verified Permissions currently supports only one namespace per policy store.</p> </note> <note> <p>Verified Permissions is <i> <a href=\"https://wikipedia.org/wiki/Eventual_consistency\">eventually consistent</a> </i>. It can take a few seconds for a new or changed element to propagate through the service and be visible in the results of other Verified Permissions operations.</p> </note>", "idempotent": true}, "CreatePolicyTemplate": {"name": "CreatePolicyTemplate", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreatePolicyTemplateInput"}, "output": {"shape": "CreatePolicyTemplateOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates a policy template. A template can use placeholders for the principal and resource. A template must be instantiated into a policy by associating it with specific principals and resources to use for the placeholders. That instantiated policy can then be considered in authorization decisions. The instantiated policy works identically to any other policy, except that it is dynamically linked to the template. If the template changes, then any policies that are linked to that template are immediately updated as well.</p> <note> <p>Verified Permissions is <i> <a href=\"https://wikipedia.org/wiki/Eventual_consistency\">eventually consistent</a> </i>. It can take a few seconds for a new or changed element to propagate through the service and be visible in the results of other Verified Permissions operations.</p> </note>", "idempotent": true}, "DeleteIdentitySource": {"name": "DeleteIdentitySource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteIdentitySourceInput"}, "output": {"shape": "DeleteIdentitySourceOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes an identity source that references an identity provider (IdP) such as Amazon Cognito. After you delete the identity source, you can no longer use tokens for identities from that identity source to represent principals in authorization queries made using <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_IsAuthorizedWithToken.html\">IsAuthorizedWithToken</a>. operations.</p>", "idempotent": true}, "DeletePolicy": {"name": "DeletePolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeletePolicyInput"}, "output": {"shape": "DeletePolicyOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes the specified policy from the policy store.</p> <p>This operation is idempotent; if you specify a policy that doesn't exist, the request response returns a successful <code>HTTP 200</code> status code.</p>", "idempotent": true}, "DeletePolicyStore": {"name": "DeletePolicyStore", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeletePolicyStoreInput"}, "output": {"shape": "DeletePolicyStoreOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "InvalidStateException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes the specified policy store.</p> <p>This operation is idempotent. If you specify a policy store that does not exist, the request response will still return a successful HTTP 200 status code.</p>", "idempotent": true}, "DeletePolicyTemplate": {"name": "DeletePolicyTemplate", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeletePolicyTemplateInput"}, "output": {"shape": "DeletePolicyTemplateOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes the specified policy template from the policy store.</p> <important> <p>This operation also deletes any policies that were created from the specified policy template. Those policies are immediately removed from all future API responses, and are asynchronously deleted from the policy store.</p> </important>", "idempotent": true}, "GetIdentitySource": {"name": "GetIdentitySource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetIdentitySourceInput"}, "output": {"shape": "GetIdentitySourceOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Retrieves the details about the specified identity source.</p>"}, "GetPolicy": {"name": "GetPolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetPolicyInput"}, "output": {"shape": "GetPolicyOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Retrieves information about the specified policy.</p>"}, "GetPolicyStore": {"name": "GetPolicyStore", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetPolicyStoreInput"}, "output": {"shape": "GetPolicyStoreOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Retrieves details about a policy store.</p>"}, "GetPolicyTemplate": {"name": "GetPolicyTemplate", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetPolicyTemplateInput"}, "output": {"shape": "GetPolicyTemplateOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Retrieve the details for the specified policy template in the specified policy store.</p>"}, "GetSchema": {"name": "GetSchema", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetSchemaInput"}, "output": {"shape": "GetSchemaOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Retrieve the details for the specified schema in the specified policy store.</p>"}, "IsAuthorized": {"name": "IsAuthorized", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "IsAuthorizedInput"}, "output": {"shape": "IsAuthorizedOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Makes an authorization decision about a service request described in the parameters. The information in the parameters can also define additional context that Verified Permissions can include in the evaluation. The request is evaluated against all matching policies in the specified policy store. The result of the decision is either <code>Allow</code> or <code>Deny</code>, along with a list of the policies that resulted in the decision.</p>"}, "IsAuthorizedWithToken": {"name": "IsAuthorizedWithToken", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "IsAuthorizedWithTokenInput"}, "output": {"shape": "IsAuthorizedWithTokenOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Makes an authorization decision about a service request described in the parameters. The principal in this request comes from an external identity source in the form of an identity token formatted as a <a href=\"https://wikipedia.org/wiki/JSON_Web_Token\">JSON web token (JWT)</a>. The information in the parameters can also define additional context that Verified Permissions can include in the evaluation. The request is evaluated against all matching policies in the specified policy store. The result of the decision is either <code>Allow</code> or <code>Deny</code>, along with a list of the policies that resulted in the decision.</p> <p>Verified Permissions validates each token that is specified in a request by checking its expiration date and its signature.</p> <important> <p>Tokens from an identity source user continue to be usable until they expire. Token revocation and resource deletion have no effect on the validity of a token in your policy store</p> </important>"}, "ListIdentitySources": {"name": "ListIdentitySources", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListIdentitySourcesInput"}, "output": {"shape": "ListIdentitySourcesOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Returns a paginated list of all of the identity sources defined in the specified policy store.</p>"}, "ListPolicies": {"name": "ListPolicies", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListPoliciesInput"}, "output": {"shape": "ListPoliciesOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Returns a paginated list of all policies stored in the specified policy store.</p>"}, "ListPolicyStores": {"name": "ListPolicyStores", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListPolicyStoresInput"}, "output": {"shape": "ListPolicyStoresOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Returns a paginated list of all policy stores in the calling Amazon Web Services account.</p>"}, "ListPolicyTemplates": {"name": "ListPolicyTemplates", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListPolicyTemplatesInput"}, "output": {"shape": "ListPolicyTemplatesOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Returns a paginated list of all policy templates in the specified policy store.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTagsForResourceInput"}, "output": {"shape": "ListTagsForResourceOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Returns the tags associated with the specified Amazon Verified Permissions resource. In Verified Permissions, policy stores can be tagged.</p>"}, "PutSchema": {"name": "PutSchema", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutSchemaInput"}, "output": {"shape": "PutSchemaOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates or updates the policy schema in the specified policy store. The schema is used to validate any Cedar policies and policy templates submitted to the policy store. Any changes to the schema validate only policies and templates submitted after the schema change. Existing policies and templates are not re-evaluated against the changed schema. If you later update a policy, then it is evaluated against the new schema at that time.</p> <note> <p>Verified Permissions is <i> <a href=\"https://wikipedia.org/wiki/Eventual_consistency\">eventually consistent</a> </i>. It can take a few seconds for a new or changed element to propagate through the service and be visible in the results of other Verified Permissions operations.</p> </note>", "idempotent": true}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "TagResourceInput"}, "output": {"shape": "TagResourceOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "TooManyTagsException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Assigns one or more tags (key-value pairs) to the specified Amazon Verified Permissions resource. Tags can help you organize and categorize your resources. You can also use them to scope user permissions by granting a user permission to access or change only resources with certain tag values. In Verified Permissions, policy stores can be tagged.</p> <p>Tags don't have any semantic meaning to Amazon Web Services and are interpreted strictly as strings of characters.</p> <p>You can use the TagResource action with a resource that already has tags. If you specify a new tag key, this tag is appended to the list of tags associated with the resource. If you specify a tag key that is already associated with the resource, the new tag value that you specify replaces the previous value for that tag.</p> <p>You can associate as many as 50 tags with a resource.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UntagResourceInput"}, "output": {"shape": "UntagResourceOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Removes one or more tags from the specified Amazon Verified Permissions resource. In Verified Permissions, policy stores can be tagged.</p>"}, "UpdateIdentitySource": {"name": "UpdateIdentitySource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateIdentitySourceInput"}, "output": {"shape": "UpdateIdentitySourceOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Updates the specified identity source to use a new identity provider (IdP), or to change the mapping of identities from the IdP to a different principal entity type.</p> <note> <p>Verified Permissions is <i> <a href=\"https://wikipedia.org/wiki/Eventual_consistency\">eventually consistent</a> </i>. It can take a few seconds for a new or changed element to propagate through the service and be visible in the results of other Verified Permissions operations.</p> </note>", "idempotent": true}, "UpdatePolicy": {"name": "UpdatePolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdatePolicyInput"}, "output": {"shape": "UpdatePolicyOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Modifies a Cedar static policy in the specified policy store. You can change only certain elements of the <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_UpdatePolicyInput.html#amazonverifiedpermissions-UpdatePolicy-request-UpdatePolicyDefinition\">UpdatePolicyDefinition</a> parameter. You can directly update only static policies. To change a template-linked policy, you must update the template instead, using <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_UpdatePolicyTemplate.html\">UpdatePolicyTemplate</a>.</p> <note> <ul> <li> <p>If policy validation is enabled in the policy store, then updating a static policy causes Verified Permissions to validate the policy against the schema in the policy store. If the updated static policy doesn't pass validation, the operation fails and the update isn't stored.</p> </li> <li> <p>When you edit a static policy, you can change only certain elements of a static policy:</p> <ul> <li> <p>The action referenced by the policy. </p> </li> <li> <p>A condition clause, such as when and unless. </p> </li> </ul> <p>You can't change these elements of a static policy: </p> <ul> <li> <p>Changing a policy from a static policy to a template-linked policy. </p> </li> <li> <p>Changing the effect of a static policy from permit or forbid. </p> </li> <li> <p>The principal referenced by a static policy. </p> </li> <li> <p>The resource referenced by a static policy. </p> </li> </ul> </li> <li> <p>To update a template-linked policy, you must update the template instead. </p> </li> </ul> </note> <note> <p>Verified Permissions is <i> <a href=\"https://wikipedia.org/wiki/Eventual_consistency\">eventually consistent</a> </i>. It can take a few seconds for a new or changed element to propagate through the service and be visible in the results of other Verified Permissions operations.</p> </note>", "idempotent": true}, "UpdatePolicyStore": {"name": "UpdatePolicyStore", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdatePolicyStoreInput"}, "output": {"shape": "UpdatePolicyStoreOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Modifies the validation setting for a policy store.</p> <note> <p>Verified Permissions is <i> <a href=\"https://wikipedia.org/wiki/Eventual_consistency\">eventually consistent</a> </i>. It can take a few seconds for a new or changed element to propagate through the service and be visible in the results of other Verified Permissions operations.</p> </note>", "idempotent": true}, "UpdatePolicyTemplate": {"name": "UpdatePolicyTemplate", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdatePolicyTemplateInput"}, "output": {"shape": "UpdatePolicyTemplateOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Updates the specified policy template. You can update only the description and the some elements of the <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_UpdatePolicyTemplate.html#amazonverifiedpermissions-UpdatePolicyTemplate-request-policyBody\">policyBody</a>. </p> <important> <p>Changes you make to the policy template content are immediately (within the constraints of eventual consistency) reflected in authorization decisions that involve all template-linked policies instantiated from this template.</p> </important> <note> <p>Verified Permissions is <i> <a href=\"https://wikipedia.org/wiki/Eventual_consistency\">eventually consistent</a> </i>. It can take a few seconds for a new or changed element to propagate through the service and be visible in the results of other Verified Permissions operations.</p> </note>", "idempotent": true}}, "shapes": {"AccessDeniedException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>You don't have sufficient access to perform this action.</p>", "exception": true}, "ActionId": {"type": "string", "max": 200, "min": 1, "pattern": ".*", "sensitive": true}, "ActionIdentifier": {"type": "structure", "required": ["actionType", "actionId"], "members": {"actionType": {"shape": "ActionType", "documentation": "<p>The type of an action.</p>"}, "actionId": {"shape": "ActionId", "documentation": "<p>The ID of an action.</p>"}}, "documentation": "<p>Contains information about an action for a request for which an authorization decision is made.</p> <p>This data type is used as a request parameter to the <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_IsAuthorized.html\">IsAuthorized</a>, <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_BatchIsAuthorized.html\">BatchIsAuthorized</a>, and <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_IsAuthorizedWithToken.html\">IsAuthorizedWithToken</a> operations.</p> <p>Example: <code>{ \"actionId\": \"&lt;action name&gt;\", \"actionType\": \"Action\" }</code> </p>"}, "ActionIdentifierList": {"type": "list", "member": {"shape": "ActionIdentifier"}}, "ActionType": {"type": "string", "max": 200, "min": 1, "pattern": "Action$|^.+::Action", "sensitive": true}, "AmazonResourceName": {"type": "string", "documentation": "<p>An Amazon Resource Name (ARN) uniquely identifies an AWS resource.</p>", "max": 2048, "min": 1}, "AttributeValue": {"type": "structure", "members": {"boolean": {"shape": "BooleanAttribute", "documentation": "<p>An attribute value of <a href=\"https://docs.cedarpolicy.com/policies/syntax-datatypes.html#boolean\">Boolean</a> type.</p> <p>Example: <code>{\"boolean\": true}</code> </p>"}, "entityIdentifier": {"shape": "EntityIdentifier", "documentation": "<p>An attribute value of type <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_EntityIdentifier.html\">EntityIdentifier</a>.</p> <p>Example: <code>\"entityIdentifier\": { \"entityId\": \"&lt;id&gt;\", \"entityType\": \"&lt;entity type&gt;\"}</code> </p>"}, "long": {"shape": "LongAttribute", "documentation": "<p>An attribute value of <a href=\"https://docs.cedarpolicy.com/policies/syntax-datatypes.html#long\">Long</a> type.</p> <p>Example: <code>{\"long\": 0}</code> </p>"}, "string": {"shape": "StringAttribute", "documentation": "<p>An attribute value of <a href=\"https://docs.cedarpolicy.com/policies/syntax-datatypes.html#string\">String</a> type.</p> <p>Example: <code>{\"string\": \"abc\"}</code> </p>"}, "set": {"shape": "SetAttribute", "documentation": "<p>An attribute value of <a href=\"https://docs.cedarpolicy.com/policies/syntax-datatypes.html#set\">Set</a> type.</p> <p>Example: <code>{\"set\": [ {} ] }</code> </p>"}, "record": {"shape": "RecordAttribute", "documentation": "<p>An attribute value of <a href=\"https://docs.cedarpolicy.com/policies/syntax-datatypes.html#record\">Record</a> type.</p> <p>Example: <code>{\"record\": { \"keyName\": {} } }</code> </p>"}, "ipaddr": {"shape": "IpAddr", "documentation": "<p>An attribute value of <a href=\"https://docs.cedarpolicy.com/policies/syntax-datatypes.html#datatype-ipaddr\">ipaddr</a> type.</p> <p>Example: <code>{\"ip\": \"*************\"}</code> </p>"}, "decimal": {"shape": "Decimal", "documentation": "<p>An attribute value of <a href=\"https://docs.cedarpolicy.com/policies/syntax-datatypes.html#datatype-decimal\">decimal</a> type.</p> <p>Example: <code>{\"decimal\": \"1.1\"}</code> </p>"}}, "documentation": "<p>The value of an attribute.</p> <p>Contains information about the runtime context for a request for which an authorization decision is made. </p> <p>This data type is used as a member of the <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_ContextDefinition.html\">ContextDefinition</a> structure which is uses as a request parameter for the <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_IsAuthorized.html\">IsAuthorized</a>, <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_BatchIsAuthorized.html\">BatchIsAuthorized</a>, and <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_IsAuthorizedWithToken.html\">IsAuthorizedWithToken</a> operations.</p>", "union": true}, "Audience": {"type": "string", "max": 255, "min": 1}, "Audiences": {"type": "list", "member": {"shape": "Audience"}, "max": 255, "min": 1}, "BatchGetPolicyErrorCode": {"type": "string", "enum": ["POLICY_STORE_NOT_FOUND", "POLICY_NOT_FOUND"]}, "BatchGetPolicyErrorItem": {"type": "structure", "required": ["code", "policyStoreId", "policyId", "message"], "members": {"code": {"shape": "BatchGetPolicyErrorCode", "documentation": "<p>The error code that was returned.</p>"}, "policyStoreId": {"shape": "String", "documentation": "<p>The identifier of the policy store associated with the failed request.</p>"}, "policyId": {"shape": "String", "documentation": "<p>The identifier of the policy associated with the failed request.</p>"}, "message": {"shape": "String", "documentation": "<p>A detailed error message.</p>"}}, "documentation": "<p>Contains the information about an error resulting from a <code>BatchGetPolicy</code> API call.</p>"}, "BatchGetPolicyErrorList": {"type": "list", "member": {"shape": "BatchGetPolicyErrorItem"}}, "BatchGetPolicyInput": {"type": "structure", "required": ["requests"], "members": {"requests": {"shape": "BatchGetPolicyInputList", "documentation": "<p>An array of up to 100 policies you want information about.</p>"}}}, "BatchGetPolicyInputItem": {"type": "structure", "required": ["policyStoreId", "policyId"], "members": {"policyStoreId": {"shape": "PolicyStoreId", "documentation": "<p>The identifier of the policy store where the policy you want information about is stored.</p>"}, "policyId": {"shape": "PolicyId", "documentation": "<p>The identifier of the policy you want information about.</p>"}}, "documentation": "<p>Information about a policy that you include in a <code>BatchGetPolicy</code> API request.</p>"}, "BatchGetPolicyInputList": {"type": "list", "member": {"shape": "BatchGetPolicyInputItem"}, "max": 100, "min": 1}, "BatchGetPolicyOutput": {"type": "structure", "required": ["results", "errors"], "members": {"results": {"shape": "BatchGetPolicyOutputList", "documentation": "<p>Information about the policies listed in the request that were successfully returned. These results are returned in the order they were requested.</p>"}, "errors": {"shape": "BatchGetPolicyErrorList", "documentation": "<p>Information about the policies from the request that resulted in an error. These results are returned in the order they were requested.</p>"}}}, "BatchGetPolicyOutputItem": {"type": "structure", "required": ["policyStoreId", "policyId", "policyType", "definition", "createdDate", "lastUpdatedDate"], "members": {"policyStoreId": {"shape": "PolicyStoreId", "documentation": "<p>The identifier of the policy store where the policy you want information about is stored.</p>"}, "policyId": {"shape": "PolicyId", "documentation": "<p>The identifier of the policy you want information about.</p>"}, "policyType": {"shape": "PolicyType", "documentation": "<p>The type of the policy. This is one of the following values:</p> <ul> <li> <p> <code>STATIC</code> </p> </li> <li> <p> <code>TEMPLATE_LINKED</code> </p> </li> </ul>"}, "definition": {"shape": "PolicyDefinitionDetail", "documentation": "<p>The policy definition of an item in the list of policies returned.</p>"}, "createdDate": {"shape": "TimestampFormat", "documentation": "<p>The date and time the policy was created.</p>"}, "lastUpdatedDate": {"shape": "TimestampFormat", "documentation": "<p>The date and time the policy was most recently updated.</p>"}}, "documentation": "<p>Contains information about a policy returned from a <code>BatchGetPolicy</code> API request.</p>"}, "BatchGetPolicyOutputList": {"type": "list", "member": {"shape": "BatchGetPolicyOutputItem"}}, "BatchIsAuthorizedInput": {"type": "structure", "required": ["policyStoreId", "requests"], "members": {"policyStoreId": {"shape": "PolicyStoreId", "documentation": "<p>Specifies the ID of the policy store. Policies in this policy store will be used to make the authorization decisions for the input.</p>"}, "entities": {"shape": "EntitiesDefinition", "documentation": "<p>(Optional) Specifies the list of resources and principals and their associated attributes that Verified Permissions can examine when evaluating the policies. These additional entities and their attributes can be referenced and checked by conditional elements in the policies in the specified policy store.</p> <note> <p>You can include only principal and resource entities in this parameter; you can't include actions. You must specify actions in the schema.</p> </note>"}, "requests": {"shape": "BatchIsAuthorizedInputList", "documentation": "<p>An array of up to 30 requests that you want Verified Permissions to evaluate.</p>"}}}, "BatchIsAuthorizedInputItem": {"type": "structure", "members": {"principal": {"shape": "EntityIdentifier", "documentation": "<p>Specifies the principal for which the authorization decision is to be made.</p>"}, "action": {"shape": "ActionIdentifier", "documentation": "<p>Specifies the requested action to be authorized. For example, <code>PhotoFlash::ReadPhoto</code>.</p>"}, "resource": {"shape": "EntityIdentifier", "documentation": "<p>Specifies the resource that you want an authorization decision for. For example, <code>PhotoFlash::Photo</code>.</p>"}, "context": {"shape": "ContextDefinition", "documentation": "<p>Specifies additional context that can be used to make more granular authorization decisions.</p>"}}, "documentation": "<p>An authorization request that you include in a <code>BatchIsAuthorized</code> API request.</p>"}, "BatchIsAuthorizedInputList": {"type": "list", "member": {"shape": "BatchIsAuthorizedInputItem"}, "min": 1}, "BatchIsAuthorizedOutput": {"type": "structure", "required": ["results"], "members": {"results": {"shape": "BatchIsAuthorizedOutputList", "documentation": "<p>A series of <code>Allow</code> or <code>Deny</code> decisions for each request, and the policies that produced them. These results are returned in the order they were requested.</p>"}}}, "BatchIsAuthorizedOutputItem": {"type": "structure", "required": ["request", "decision", "determiningPolicies", "errors"], "members": {"request": {"shape": "BatchIsAuthorizedInputItem", "documentation": "<p>The authorization request that initiated the decision.</p>"}, "decision": {"shape": "Decision", "documentation": "<p>An authorization decision that indicates if the authorization request should be allowed or denied.</p>"}, "determiningPolicies": {"shape": "DeterminingPolicyList", "documentation": "<p>The list of determining policies used to make the authorization decision. For example, if there are two matching policies, where one is a forbid and the other is a permit, then the forbid policy will be the determining policy. In the case of multiple matching permit policies then there would be multiple determining policies. In the case that no policies match, and hence the response is DENY, there would be no determining policies.</p>"}, "errors": {"shape": "EvaluationErrorList", "documentation": "<p>Errors that occurred while making an authorization decision. For example, a policy might reference an entity or attribute that doesn't exist in the request.</p>"}}, "documentation": "<p>The decision, based on policy evaluation, from an individual authorization request in a <code>BatchIsAuthorized</code> API request.</p>"}, "BatchIsAuthorizedOutputList": {"type": "list", "member": {"shape": "BatchIsAuthorizedOutputItem"}}, "BatchIsAuthorizedWithTokenInput": {"type": "structure", "required": ["policyStoreId", "requests"], "members": {"policyStoreId": {"shape": "PolicyStoreId", "documentation": "<p>Specifies the ID of the policy store. Policies in this policy store will be used to make an authorization decision for the input.</p>"}, "identityToken": {"shape": "Token", "documentation": "<p>Specifies an identity (ID) token for the principal that you want to authorize in each request. This token is provided to you by the identity provider (IdP) associated with the specified identity source. You must specify either an <code>accessToken</code>, an <code>identityToken</code>, or both.</p> <p>Must be an ID token. Verified Permissions returns an error if the <code>token_use</code> claim in the submitted token isn't <code>id</code>.</p>"}, "accessToken": {"shape": "Token", "documentation": "<p>Specifies an access token for the principal that you want to authorize in each request. This token is provided to you by the identity provider (IdP) associated with the specified identity source. You must specify either an <code>accessToken</code>, an <code>identityToken</code>, or both.</p> <p>Must be an access token. Verified Permissions returns an error if the <code>token_use</code> claim in the submitted token isn't <code>access</code>.</p>"}, "entities": {"shape": "EntitiesDefinition", "documentation": "<p>(Optional) Specifies the list of resources and their associated attributes that Verified Permissions can examine when evaluating the policies. These additional entities and their attributes can be referenced and checked by conditional elements in the policies in the specified policy store.</p> <important> <p>You can't include principals in this parameter, only resource and action entities. This parameter can't include any entities of a type that matches the user or group entity types that you defined in your identity source.</p> <ul> <li> <p>The <code>BatchIsAuthorizedWithToken</code> operation takes principal attributes from <b> <i>only</i> </b> the <code>identityToken</code> or <code>accessToken</code> passed to the operation.</p> </li> <li> <p>For action entities, you can include only their <code>Identifier</code> and <code>EntityType</code>. </p> </li> </ul> </important>"}, "requests": {"shape": "BatchIsAuthorizedWithTokenInputList", "documentation": "<p>An array of up to 30 requests that you want Verified Permissions to evaluate.</p>"}}}, "BatchIsAuthorizedWithTokenInputItem": {"type": "structure", "members": {"action": {"shape": "ActionIdentifier", "documentation": "<p>Specifies the requested action to be authorized. For example, <code>PhotoFlash::ReadPhoto</code>.</p>"}, "resource": {"shape": "EntityIdentifier", "documentation": "<p>Specifies the resource that you want an authorization decision for. For example, <code>PhotoFlash::Photo</code>.</p>"}, "context": {"shape": "ContextDefinition", "documentation": "<p>Specifies additional context that can be used to make more granular authorization decisions.</p>"}}, "documentation": "<p>An authorization request that you include in a <code>BatchIsAuthorizedWithToken</code> API request.</p>"}, "BatchIsAuthorizedWithTokenInputList": {"type": "list", "member": {"shape": "BatchIsAuthorizedWithTokenInputItem"}, "min": 1}, "BatchIsAuthorizedWithTokenOutput": {"type": "structure", "required": ["results"], "members": {"principal": {"shape": "EntityIdentifier", "documentation": "<p>The identifier of the principal in the ID or access token.</p>"}, "results": {"shape": "BatchIsAuthorizedWithTokenOutputList", "documentation": "<p>A series of <code>Allow</code> or <code>Deny</code> decisions for each request, and the policies that produced them. These results are returned in the order they were requested.</p>"}}}, "BatchIsAuthorizedWithTokenOutputItem": {"type": "structure", "required": ["request", "decision", "determiningPolicies", "errors"], "members": {"request": {"shape": "BatchIsAuthorizedWithTokenInputItem", "documentation": "<p>The authorization request that initiated the decision.</p>"}, "decision": {"shape": "Decision", "documentation": "<p>An authorization decision that indicates if the authorization request should be allowed or denied.</p>"}, "determiningPolicies": {"shape": "DeterminingPolicyList", "documentation": "<p>The list of determining policies used to make the authorization decision. For example, if there are two matching policies, where one is a forbid and the other is a permit, then the forbid policy will be the determining policy. In the case of multiple matching permit policies then there would be multiple determining policies. In the case that no policies match, and hence the response is DENY, there would be no determining policies.</p>"}, "errors": {"shape": "EvaluationErrorList", "documentation": "<p>Errors that occurred while making an authorization decision. For example, a policy might reference an entity or attribute that doesn't exist in the request.</p>"}}, "documentation": "<p>The decision, based on policy evaluation, from an individual authorization request in a <code>BatchIsAuthorizedWithToken</code> API request.</p>"}, "BatchIsAuthorizedWithTokenOutputList": {"type": "list", "member": {"shape": "BatchIsAuthorizedWithTokenOutputItem"}}, "Boolean": {"type": "boolean", "box": true}, "BooleanAttribute": {"type": "boolean", "box": true, "sensitive": true}, "CedarJson": {"type": "string", "sensitive": true}, "CedarVersion": {"type": "string", "enum": ["CEDAR_2", "CEDAR_4"]}, "Claim": {"type": "string", "min": 1, "sensitive": true}, "ClientId": {"type": "string", "max": 255, "min": 1, "pattern": ".*", "sensitive": true}, "ClientIds": {"type": "list", "member": {"shape": "ClientId"}, "max": 1000, "min": 0}, "CognitoGroupConfiguration": {"type": "structure", "required": ["groupEntityType"], "members": {"groupEntityType": {"shape": "GroupEntityType", "documentation": "<p>The name of the schema entity type that's mapped to the user pool group. Defaults to <code>AWS::CognitoGroup</code>.</p>"}}, "documentation": "<p>The type of entity that a policy store maps to groups from an Amazon Cognito user pool identity source.</p> <p>This data type is part of a <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_CognitoUserPoolConfiguration.html\">CognitoUserPoolConfiguration</a> structure and is a request parameter in <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_CreateIdentitySource.html\">CreateIdentitySource</a>.</p>"}, "CognitoGroupConfigurationDetail": {"type": "structure", "members": {"groupEntityType": {"shape": "GroupEntityType", "documentation": "<p>The name of the schema entity type that's mapped to the user pool group. Defaults to <code>AWS::CognitoGroup</code>.</p>"}}, "documentation": "<p>The type of entity that a policy store maps to groups from an Amazon Cognito user pool identity source.</p> <p>This data type is part of an <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_CognitoUserPoolConfigurationItem.html\">CognitoUserPoolConfigurationDetail</a> structure and is a response parameter to <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_GetIdentitySource.html\">GetIdentitySource</a>.</p>"}, "CognitoGroupConfigurationItem": {"type": "structure", "members": {"groupEntityType": {"shape": "GroupEntityType", "documentation": "<p>The name of the schema entity type that's mapped to the user pool group. Defaults to <code>AWS::CognitoGroup</code>.</p>"}}, "documentation": "<p>The type of entity that a policy store maps to groups from an Amazon Cognito user pool identity source.</p> <p>This data type is part of an <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_CognitoUserPoolConfigurationDetail.html\">CognitoUserPoolConfigurationItem</a> structure and is a response parameter to <a href=\"http://forums.aws.amazon.com/verifiedpermissions/latest/apireference/API_ListIdentitySources.html\">ListIdentitySources</a>.</p>"}, "CognitoUserPoolConfiguration": {"type": "structure", "required": ["userPoolArn"], "members": {"userPoolArn": {"shape": "UserPoolArn", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of the Amazon Cognito user pool that contains the identities to be authorized.</p> <p>Example: <code>\"UserPoolArn\": \"arn:aws:cognito-idp:us-east-1:123456789012:userpool/us-east-1_1a2b3c4d5\"</code> </p>"}, "clientIds": {"shape": "ClientIds", "documentation": "<p>The unique application client IDs that are associated with the specified Amazon Cognito user pool.</p> <p>Example: <code>\"ClientIds\": [\"&amp;ExampleCogClientId;\"]</code> </p>"}, "groupConfiguration": {"shape": "CognitoGroupConfiguration", "documentation": "<p>The type of entity that a policy store maps to groups from an Amazon Cognito user pool identity source.</p>"}}, "documentation": "<p>The configuration for an identity source that represents a connection to an Amazon Cognito user pool used as an identity provider for Verified Permissions.</p> <p>This data type part of a <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_Configuration.html\">Configuration</a> structure that is used as a parameter to <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_CreateIdentitySource.html\">CreateIdentitySource</a>.</p> <p>Example:<code>\"CognitoUserPoolConfiguration\":{\"UserPoolArn\":\"arn:aws:cognito-idp:us-east-1:123456789012:userpool/us-east-1_1a2b3c4d5\",\"ClientIds\": [\"a1b2c3d4e5f6g7h8i9j0kalbmc\"],\"groupConfiguration\": {\"groupEntityType\": \"MyCorp::Group\"}}</code> </p>"}, "CognitoUserPoolConfigurationDetail": {"type": "structure", "required": ["userPoolArn", "clientIds", "issuer"], "members": {"userPoolArn": {"shape": "UserPoolArn", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of the Amazon Cognito user pool that contains the identities to be authorized.</p> <p>Example: <code>\"userPoolArn\": \"arn:aws:cognito-idp:us-east-1:123456789012:userpool/us-east-1_1a2b3c4d5\"</code> </p>"}, "clientIds": {"shape": "ClientIds", "documentation": "<p>The unique application client IDs that are associated with the specified Amazon Cognito user pool.</p> <p>Example: <code>\"clientIds\": [\"&amp;ExampleCogClientId;\"]</code> </p>"}, "issuer": {"shape": "Issuer", "documentation": "<p>The OpenID Connect (OIDC) <code>issuer</code> ID of the Amazon Cognito user pool that contains the identities to be authorized.</p> <p>Example: <code>\"issuer\": \"https://cognito-idp.us-east-1.amazonaws.com/us-east-1_1a2b3c4d5\"</code> </p>"}, "groupConfiguration": {"shape": "CognitoGroupConfigurationDetail", "documentation": "<p>The type of entity that a policy store maps to groups from an Amazon Cognito user pool identity source.</p>"}}, "documentation": "<p>The configuration for an identity source that represents a connection to an Amazon Cognito user pool used as an identity provider for Verified Permissions.</p> <p>This data type is used as a field that is part of an <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_ConfigurationDetail.html\">ConfigurationDetail</a> structure that is part of the response to <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_GetIdentitySource.html\">GetIdentitySource</a>.</p> <p>Example:<code>\"CognitoUserPoolConfiguration\":{\"UserPoolArn\":\"arn:aws:cognito-idp:us-east-1:123456789012:userpool/us-east-1_1a2b3c4d5\",\"ClientIds\": [\"a1b2c3d4e5f6g7h8i9j0kalbmc\"],\"groupConfiguration\": {\"groupEntityType\": \"MyCorp::Group\"}}</code> </p>"}, "CognitoUserPoolConfigurationItem": {"type": "structure", "required": ["userPoolArn", "clientIds", "issuer"], "members": {"userPoolArn": {"shape": "UserPoolArn", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of the Amazon Cognito user pool that contains the identities to be authorized.</p> <p>Example: <code>\"userPoolArn\": \"arn:aws:cognito-idp:us-east-1:123456789012:userpool/us-east-1_1a2b3c4d5\"</code> </p>"}, "clientIds": {"shape": "ClientIds", "documentation": "<p>The unique application client IDs that are associated with the specified Amazon Cognito user pool.</p> <p>Example: <code>\"clientIds\": [\"&amp;ExampleCogClientId;\"]</code> </p>"}, "issuer": {"shape": "Issuer", "documentation": "<p>The OpenID Connect (OIDC) <code>issuer</code> ID of the Amazon Cognito user pool that contains the identities to be authorized.</p> <p>Example: <code>\"issuer\": \"https://cognito-idp.us-east-1.amazonaws.com/us-east-1_1a2b3c4d5\"</code> </p>"}, "groupConfiguration": {"shape": "CognitoGroupConfigurationItem", "documentation": "<p>The type of entity that a policy store maps to groups from an Amazon Cognito user pool identity source.</p>"}}, "documentation": "<p>The configuration for an identity source that represents a connection to an Amazon Cognito user pool used as an identity provider for Verified Permissions.</p> <p>This data type is used as a field that is part of the <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_ConfigurationItem.html\">ConfigurationItem</a> structure that is part of the response to <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_ListIdentitySources.html\">ListIdentitySources</a>.</p> <p>Example:<code>\"CognitoUserPoolConfiguration\":{\"UserPoolArn\":\"arn:aws:cognito-idp:us-east-1:123456789012:userpool/us-east-1_1a2b3c4d5\",\"ClientIds\": [\"a1b2c3d4e5f6g7h8i9j0kalbmc\"],\"groupConfiguration\": {\"groupEntityType\": \"MyCorp::Group\"}}</code> </p>"}, "Configuration": {"type": "structure", "members": {"cognitoUserPoolConfiguration": {"shape": "CognitoUserPoolConfiguration", "documentation": "<p>Contains configuration details of a Amazon Cognito user pool that Verified Permissions can use as a source of authenticated identities as entities. It specifies the <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of a Amazon Cognito user pool and one or more application client IDs.</p> <p>Example: <code>\"configuration\":{\"cognitoUserPoolConfiguration\":{\"userPoolArn\":\"arn:aws:cognito-idp:us-east-1:123456789012:userpool/us-east-1_1a2b3c4d5\",\"clientIds\": [\"a1b2c3d4e5f6g7h8i9j0kalbmc\"],\"groupConfiguration\": {\"groupEntityType\": \"MyCorp::Group\"}}}</code> </p>"}, "openIdConnectConfiguration": {"shape": "OpenIdConnectConfiguration", "documentation": "<p>Contains configuration details of an OpenID Connect (OIDC) identity provider, or identity source, that Verified Permissions can use to generate entities from authenticated identities. It specifies the issuer URL, token type that you want to use, and policy store entity details.</p> <p>Example:<code>\"configuration\":{\"openIdConnectConfiguration\":{\"issuer\":\"https://auth.example.com\",\"tokenSelection\":{\"accessTokenOnly\":{\"audiences\":[\"https://myapp.example.com\",\"https://myapp2.example.com\"],\"principalIdClaim\":\"sub\"}},\"entityIdPrefix\":\"MyOIDCProvider\",\"groupConfiguration\":{\"groupClaim\":\"groups\",\"groupEntityType\":\"MyCorp::UserGroup\"}}}</code> </p>"}}, "documentation": "<p>Contains configuration information used when creating a new identity source.</p> <p>This data type is used as a request parameter for the <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_CreateIdentitySource.html\">CreateIdentitySource</a> operation.</p>", "union": true}, "ConfigurationDetail": {"type": "structure", "members": {"cognitoUserPoolConfiguration": {"shape": "CognitoUserPoolConfigurationDetail", "documentation": "<p>Contains configuration details of a Amazon Cognito user pool that Verified Permissions can use as a source of authenticated identities as entities. It specifies the <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of a Amazon Cognito user pool, the policy store entity that you want to assign to user groups, and one or more application client IDs.</p> <p>Example: <code>\"configuration\":{\"cognitoUserPoolConfiguration\":{\"userPoolArn\":\"arn:aws:cognito-idp:us-east-1:123456789012:userpool/us-east-1_1a2b3c4d5\",\"clientIds\": [\"a1b2c3d4e5f6g7h8i9j0kalbmc\"],\"groupConfiguration\": {\"groupEntityType\": \"MyCorp::Group\"}}}</code> </p>"}, "openIdConnectConfiguration": {"shape": "OpenIdConnectConfigurationDetail", "documentation": "<p>Contains configuration details of an OpenID Connect (OIDC) identity provider, or identity source, that Verified Permissions can use to generate entities from authenticated identities. It specifies the issuer URL, token type that you want to use, and policy store entity details.</p> <p>Example:<code>\"configuration\":{\"openIdConnectConfiguration\":{\"issuer\":\"https://auth.example.com\",\"tokenSelection\":{\"accessTokenOnly\":{\"audiences\":[\"https://myapp.example.com\",\"https://myapp2.example.com\"],\"principalIdClaim\":\"sub\"}},\"entityIdPrefix\":\"MyOIDCProvider\",\"groupConfiguration\":{\"groupClaim\":\"groups\",\"groupEntityType\":\"MyCorp::UserGroup\"}}}</code> </p>"}}, "documentation": "<p>Contains configuration information about an identity source.</p> <p>This data type is a response parameter to the <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_GetIdentitySource.html\">GetIdentitySource</a> operation.</p>", "union": true}, "ConfigurationItem": {"type": "structure", "members": {"cognitoUserPoolConfiguration": {"shape": "CognitoUserPoolConfigurationItem", "documentation": "<p>Contains configuration details of a Amazon Cognito user pool that Verified Permissions can use as a source of authenticated identities as entities. It specifies the <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of a Amazon Cognito user pool, the policy store entity that you want to assign to user groups, and one or more application client IDs.</p> <p>Example: <code>\"configuration\":{\"cognitoUserPoolConfiguration\":{\"userPoolArn\":\"arn:aws:cognito-idp:us-east-1:123456789012:userpool/us-east-1_1a2b3c4d5\",\"clientIds\": [\"a1b2c3d4e5f6g7h8i9j0kalbmc\"],\"groupConfiguration\": {\"groupEntityType\": \"MyCorp::Group\"}}}</code> </p>"}, "openIdConnectConfiguration": {"shape": "OpenIdConnectConfigurationItem", "documentation": "<p>Contains configuration details of an OpenID Connect (OIDC) identity provider, or identity source, that Verified Permissions can use to generate entities from authenticated identities. It specifies the issuer URL, token type that you want to use, and policy store entity details.</p> <p>Example:<code>\"configuration\":{\"openIdConnectConfiguration\":{\"issuer\":\"https://auth.example.com\",\"tokenSelection\":{\"accessTokenOnly\":{\"audiences\":[\"https://myapp.example.com\",\"https://myapp2.example.com\"],\"principalIdClaim\":\"sub\"}},\"entityIdPrefix\":\"MyOIDCProvider\",\"groupConfiguration\":{\"groupClaim\":\"groups\",\"groupEntityType\":\"MyCorp::UserGroup\"}}}</code> </p>"}}, "documentation": "<p>Contains configuration information about an identity source.</p> <p>This data type is a response parameter to the <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_ListIdentitySources.html\">ListIdentitySources</a> operation.</p>", "union": true}, "ConflictException": {"type": "structure", "required": ["message", "resources"], "members": {"message": {"shape": "String"}, "resources": {"shape": "ResourceConflictList", "documentation": "<p>The list of resources referenced with this failed request.</p>"}}, "documentation": "<p>The request failed because another request to modify a resource occurred at the same.</p>", "exception": true}, "ContextDefinition": {"type": "structure", "members": {"contextMap": {"shape": "ContextMap", "documentation": "<p>An list of attributes that are needed to successfully evaluate an authorization request. Each attribute in this array must include a map of a data type and its value.</p> <p>Example: <code>\"contextMap\":{\"&lt;KeyName1&gt;\":{\"boolean\":true},\"&lt;KeyName2&gt;\":{\"long\":1234}}</code> </p>"}, "cedarJson": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>A Cedar JSON string representation of the context needed to successfully evaluate an authorization request.</p> <p>Example: <code>{\"cedarJson\":\"{\\\"&lt;KeyName1&gt;\\\": true, \\\"&lt;KeyName2&gt;\\\": 1234}\" }</code> </p>"}}, "documentation": "<p>Contains additional details about the context of the request. Verified Permissions evaluates this information in an authorization request as part of the <code>when</code> and <code>unless</code> clauses in a policy.</p> <p>This data type is used as a request parameter for the <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_IsAuthorized.html\">IsAuthorized</a>, <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_BatchIsAuthorized.html\">BatchIsAuthorized</a>, and <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_IsAuthorizedWithToken.html\">IsAuthorizedWithToken</a> operations.</p> <p>If you're passing context as part of the request, exactly one instance of <code>context</code> must be passed. If you don't want to pass context, omit the <code>context</code> parameter from your request rather than sending <code>context {}</code>.</p> <p>Example: <code>\"context\":{\"contextMap\":{\"&lt;KeyName1&gt;\":{\"boolean\":true},\"&lt;KeyName2&gt;\":{\"long\":1234}}}</code> </p>", "union": true}, "ContextMap": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "AttributeValue"}, "sensitive": true}, "CreateIdentitySourceInput": {"type": "structure", "required": ["policyStoreId", "configuration"], "members": {"clientToken": {"shape": "IdempotencyToken", "documentation": "<p>Specifies a unique, case-sensitive ID that you provide to ensure the idempotency of the request. This lets you safely retry the request without accidentally performing the same operation a second time. Passing the same value to a later call to an operation requires that you also pass the same value for all other parameters. We recommend that you use a <a href=\"https://wikipedia.org/wiki/Universally_unique_identifier\">UUID type of value.</a>.</p> <p>If you don't provide this value, then Amazon Web Services generates a random one for you.</p> <p>If you retry the operation with the same <code>ClientToken</code>, but with different parameters, the retry fails with an <code>ConflictException</code> error.</p> <p>Verified Permissions recognizes a <code>ClientToken</code> for eight hours. After eight hours, the next request with the same parameters performs the operation again regardless of the value of <code>ClientToken</code>.</p>", "idempotencyToken": true}, "policyStoreId": {"shape": "PolicyStoreId", "documentation": "<p>Specifies the ID of the policy store in which you want to store this identity source. Only policies and requests made using this policy store can reference identities from the identity provider configured in the new identity source.</p>"}, "configuration": {"shape": "Configuration", "documentation": "<p>Specifies the details required to communicate with the identity provider (IdP) associated with this identity source.</p>"}, "principalEntityType": {"shape": "PrincipalEntityType", "documentation": "<p>Specifies the namespace and data type of the principals generated for identities authenticated by the new identity source.</p>"}}}, "CreateIdentitySourceOutput": {"type": "structure", "required": ["createdDate", "identitySourceId", "lastUpdatedDate", "policyStoreId"], "members": {"createdDate": {"shape": "TimestampFormat", "documentation": "<p>The date and time the identity source was originally created.</p>"}, "identitySourceId": {"shape": "IdentitySourceId", "documentation": "<p>The unique ID of the new identity source.</p>"}, "lastUpdatedDate": {"shape": "TimestampFormat", "documentation": "<p>The date and time the identity source was most recently updated.</p>"}, "policyStoreId": {"shape": "PolicyStoreId", "documentation": "<p>The ID of the policy store that contains the identity source.</p>"}}}, "CreatePolicyInput": {"type": "structure", "required": ["policyStoreId", "definition"], "members": {"clientToken": {"shape": "IdempotencyToken", "documentation": "<p>Specifies a unique, case-sensitive ID that you provide to ensure the idempotency of the request. This lets you safely retry the request without accidentally performing the same operation a second time. Passing the same value to a later call to an operation requires that you also pass the same value for all other parameters. We recommend that you use a <a href=\"https://wikipedia.org/wiki/Universally_unique_identifier\">UUID type of value.</a>.</p> <p>If you don't provide this value, then Amazon Web Services generates a random one for you.</p> <p>If you retry the operation with the same <code>ClientToken</code>, but with different parameters, the retry fails with an <code>ConflictException</code> error.</p> <p>Verified Permissions recognizes a <code>ClientToken</code> for eight hours. After eight hours, the next request with the same parameters performs the operation again regardless of the value of <code>ClientToken</code>.</p>", "idempotencyToken": true}, "policyStoreId": {"shape": "PolicyStoreId", "documentation": "<p>Specifies the <code>PolicyStoreId</code> of the policy store you want to store the policy in.</p>"}, "definition": {"shape": "PolicyDefinition", "documentation": "<p>A structure that specifies the policy type and content to use for the new policy. You must include either a static or a templateLinked element. The policy content must be written in the Cedar policy language.</p>"}}}, "CreatePolicyOutput": {"type": "structure", "required": ["policyStoreId", "policyId", "policyType", "createdDate", "lastUpdatedDate"], "members": {"policyStoreId": {"shape": "PolicyStoreId", "documentation": "<p>The ID of the policy store that contains the new policy.</p>"}, "policyId": {"shape": "PolicyId", "documentation": "<p>The unique ID of the new policy.</p>"}, "policyType": {"shape": "PolicyType", "documentation": "<p>The policy type of the new policy.</p>"}, "principal": {"shape": "EntityIdentifier", "documentation": "<p>The principal specified in the new policy's scope. This response element isn't present when <code>principal</code> isn't specified in the policy content.</p>"}, "resource": {"shape": "EntityIdentifier", "documentation": "<p>The resource specified in the new policy's scope. This response element isn't present when the <code>resource</code> isn't specified in the policy content.</p>"}, "actions": {"shape": "ActionIdentifierList", "documentation": "<p>The action that a policy permits or forbids. For example, <code>{\"actions\": [{\"actionId\": \"ViewPhoto\", \"actionType\": \"PhotoFlash::Action\"}, {\"entityID\": \"SharePhoto\", \"entityType\": \"PhotoFlash::Action\"}]}</code>.</p>"}, "createdDate": {"shape": "TimestampFormat", "documentation": "<p>The date and time the policy was originally created.</p>"}, "lastUpdatedDate": {"shape": "TimestampFormat", "documentation": "<p>The date and time the policy was last updated.</p>"}, "effect": {"shape": "PolicyEffect", "documentation": "<p>The effect of the decision that a policy returns to an authorization request. For example, <code>\"effect\": \"Permit\"</code>.</p>"}}}, "CreatePolicyStoreInput": {"type": "structure", "required": ["validationSettings"], "members": {"clientToken": {"shape": "IdempotencyToken", "documentation": "<p>Specifies a unique, case-sensitive ID that you provide to ensure the idempotency of the request. This lets you safely retry the request without accidentally performing the same operation a second time. Passing the same value to a later call to an operation requires that you also pass the same value for all other parameters. We recommend that you use a <a href=\"https://wikipedia.org/wiki/Universally_unique_identifier\">UUID type of value.</a>.</p> <p>If you don't provide this value, then Amazon Web Services generates a random one for you.</p> <p>If you retry the operation with the same <code>ClientToken</code>, but with different parameters, the retry fails with an <code>ConflictException</code> error.</p> <p>Verified Permissions recognizes a <code>ClientToken</code> for eight hours. After eight hours, the next request with the same parameters performs the operation again regardless of the value of <code>ClientToken</code>.</p>", "idempotencyToken": true}, "validationSettings": {"shape": "ValidationSettings", "documentation": "<p>Specifies the validation setting for this policy store.</p> <p>Currently, the only valid and required value is <code>Mode</code>.</p> <important> <p>We recommend that you turn on <code>STRICT</code> mode only after you define a schema. If a schema doesn't exist, then <code>STRICT</code> mode causes any policy to fail validation, and Verified Permissions rejects the policy. You can turn off validation by using the <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_UpdatePolicyStore\">UpdatePolicyStore</a>. Then, when you have a schema defined, use <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_UpdatePolicyStore\">UpdatePolicyStore</a> again to turn validation back on.</p> </important>"}, "description": {"shape": "PolicyStoreDescription", "documentation": "<p>Descriptive text that you can provide to help with identification of the current policy store.</p>"}, "deletionProtection": {"shape": "DeletionProtection", "documentation": "<p>Specifies whether the policy store can be deleted. If enabled, the policy store can't be deleted.</p> <p>The default state is <code>DISABLED</code>.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The list of key-value pairs to associate with the policy store.</p>"}}}, "CreatePolicyStoreOutput": {"type": "structure", "required": ["policyStoreId", "arn", "createdDate", "lastUpdatedDate"], "members": {"policyStoreId": {"shape": "PolicyStoreId", "documentation": "<p>The unique ID of the new policy store.</p>"}, "arn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the new policy store.</p>"}, "createdDate": {"shape": "TimestampFormat", "documentation": "<p>The date and time the policy store was originally created.</p>"}, "lastUpdatedDate": {"shape": "TimestampFormat", "documentation": "<p>The date and time the policy store was last updated.</p>"}}}, "CreatePolicyTemplateInput": {"type": "structure", "required": ["policyStoreId", "statement"], "members": {"clientToken": {"shape": "IdempotencyToken", "documentation": "<p>Specifies a unique, case-sensitive ID that you provide to ensure the idempotency of the request. This lets you safely retry the request without accidentally performing the same operation a second time. Passing the same value to a later call to an operation requires that you also pass the same value for all other parameters. We recommend that you use a <a href=\"https://wikipedia.org/wiki/Universally_unique_identifier\">UUID type of value.</a>.</p> <p>If you don't provide this value, then Amazon Web Services generates a random one for you.</p> <p>If you retry the operation with the same <code>ClientToken</code>, but with different parameters, the retry fails with an <code>ConflictException</code> error.</p> <p>Verified Permissions recognizes a <code>ClientToken</code> for eight hours. After eight hours, the next request with the same parameters performs the operation again regardless of the value of <code>ClientToken</code>.</p>", "idempotencyToken": true}, "policyStoreId": {"shape": "PolicyStoreId", "documentation": "<p>The ID of the policy store in which to create the policy template.</p>"}, "description": {"shape": "PolicyTemplateDescription", "documentation": "<p>Specifies a description for the policy template.</p>"}, "statement": {"shape": "PolicyStatement", "documentation": "<p>Specifies the content that you want to use for the new policy template, written in the Cedar policy language.</p>"}}}, "CreatePolicyTemplateOutput": {"type": "structure", "required": ["policyStoreId", "policyTemplateId", "createdDate", "lastUpdatedDate"], "members": {"policyStoreId": {"shape": "PolicyStoreId", "documentation": "<p>The ID of the policy store that contains the policy template.</p>"}, "policyTemplateId": {"shape": "PolicyTemplateId", "documentation": "<p>The unique ID of the new policy template.</p>"}, "createdDate": {"shape": "TimestampFormat", "documentation": "<p>The date and time the policy template was originally created.</p>"}, "lastUpdatedDate": {"shape": "TimestampFormat", "documentation": "<p>The date and time the policy template was most recently updated.</p>"}}}, "Decimal": {"type": "string", "max": 23, "min": 3, "pattern": "-?\\d{1,15}\\.\\d{1,4}", "sensitive": true}, "Decision": {"type": "string", "enum": ["ALLOW", "DENY"]}, "DeleteIdentitySourceInput": {"type": "structure", "required": ["policyStoreId", "identitySourceId"], "members": {"policyStoreId": {"shape": "PolicyStoreId", "documentation": "<p>Specifies the ID of the policy store that contains the identity source that you want to delete.</p>"}, "identitySourceId": {"shape": "IdentitySourceId", "documentation": "<p>Specifies the ID of the identity source that you want to delete.</p>"}}}, "DeleteIdentitySourceOutput": {"type": "structure", "members": {}}, "DeletePolicyInput": {"type": "structure", "required": ["policyStoreId", "policyId"], "members": {"policyStoreId": {"shape": "PolicyStoreId", "documentation": "<p>Specifies the ID of the policy store that contains the policy that you want to delete.</p>"}, "policyId": {"shape": "PolicyId", "documentation": "<p>Specifies the ID of the policy that you want to delete.</p>"}}}, "DeletePolicyOutput": {"type": "structure", "members": {}}, "DeletePolicyStoreInput": {"type": "structure", "required": ["policyStoreId"], "members": {"policyStoreId": {"shape": "PolicyStoreId", "documentation": "<p>Specifies the ID of the policy store that you want to delete.</p>"}}}, "DeletePolicyStoreOutput": {"type": "structure", "members": {}}, "DeletePolicyTemplateInput": {"type": "structure", "required": ["policyStoreId", "policyTemplateId"], "members": {"policyStoreId": {"shape": "PolicyStoreId", "documentation": "<p>Specifies the ID of the policy store that contains the policy template that you want to delete.</p>"}, "policyTemplateId": {"shape": "PolicyTemplateId", "documentation": "<p>Specifies the ID of the policy template that you want to delete.</p>"}}}, "DeletePolicyTemplateOutput": {"type": "structure", "members": {}}, "DeletionProtection": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "DeterminingPolicyItem": {"type": "structure", "required": ["policyId"], "members": {"policyId": {"shape": "PolicyId", "documentation": "<p>The Id of a policy that determined to an authorization decision.</p> <p>Example: <code>\"policyId\":\"SPEXAMPLEabcdefg111111\"</code> </p>"}}, "documentation": "<p>Contains information about one of the policies that determined an authorization decision.</p> <p>This data type is used as an element in a response parameter for the <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_IsAuthorized.html\">IsAuthorized</a>, <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_BatchIsAuthorized.html\">BatchIsAuthorized</a>, and <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_IsAuthorizedWithToken.html\">IsAuthorizedWithToken</a> operations.</p> <p>Example: <code>\"determiningPolicies\":[{\"policyId\":\"SPEXAMPLEabcdefg111111\"}]</code> </p>"}, "DeterminingPolicyList": {"type": "list", "member": {"shape": "DeterminingPolicyItem"}}, "DiscoveryUrl": {"type": "string", "max": 2048, "min": 1, "pattern": "https://.*"}, "EntitiesDefinition": {"type": "structure", "members": {"entityList": {"shape": "EntityList", "documentation": "<p>An array of entities that are needed to successfully evaluate an authorization request. Each entity in this array must include an identifier for the entity, the attributes of the entity, and a list of any parent entities.</p> <note> <p>If you include multiple entities with the same <code>identifier</code>, only the last one is processed in the request.</p> </note>"}, "cedarJson": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>A Cedar JSON string representation of the entities needed to successfully evaluate an authorization request.</p> <p>Example: <code>{\"cedarJson\": \"[{\\\"uid\\\":{\\\"type\\\":\\\"Photo\\\",\\\"id\\\":\\\"VacationPhoto94.jpg\\\"},\\\"attrs\\\":{\\\"accessLevel\\\":\\\"public\\\"},\\\"parents\\\":[]}]\"}</code> </p>"}}, "documentation": "<p>Contains the list of entities to be considered during an authorization request. This includes all principals, resources, and actions required to successfully evaluate the request.</p> <p>This data type is used as a field in the response parameter for the <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_IsAuthorized.html\">IsAuthorized</a> and <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_IsAuthorizedWithToken.html\">IsAuthorizedWithToken</a> operations.</p>", "union": true}, "EntityAttributes": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "AttributeValue"}}, "EntityId": {"type": "string", "max": 200, "min": 1, "pattern": ".*", "sensitive": true}, "EntityIdPrefix": {"type": "string", "max": 100, "min": 1, "sensitive": true}, "EntityIdentifier": {"type": "structure", "required": ["entityType", "entityId"], "members": {"entityType": {"shape": "EntityType", "documentation": "<p>The type of an entity.</p> <p>Example: <code>\"entityType\":\"<i>typeName</i>\"</code> </p>"}, "entityId": {"shape": "EntityId", "documentation": "<p>The identifier of an entity.</p> <p> <code>\"entityId\":\"<i>identifier</i>\"</code> </p>"}}, "documentation": "<p>Contains the identifier of an entity, including its ID and type.</p> <p>This data type is used as a request parameter for <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_IsAuthorized.html\">IsAuthorized</a> operation, and as a response parameter for the <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_CreatePolicy.html\">CreatePolicy</a>, <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_GetPolicy.html\">GetPolicy</a>, and <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_UpdatePolicy.html\">UpdatePolicy</a> operations.</p> <p>Example: <code>{\"entityId\":\"<i>string</i>\",\"entityType\":\"<i>string</i>\"}</code> </p>"}, "EntityItem": {"type": "structure", "required": ["identifier"], "members": {"identifier": {"shape": "EntityIdentifier", "documentation": "<p>The identifier of the entity.</p>"}, "attributes": {"shape": "EntityAttributes", "documentation": "<p>A list of attributes for the entity.</p>"}, "parents": {"shape": "ParentList", "documentation": "<p>The parent entities in the hierarchy that contains the entity. A principal or resource entity can be defined with at most 99 <i>transitive parents</i> per authorization request. </p> <p>A transitive parent is an entity in the hierarchy of entities including all direct parents, and parents of parents. For example, a user can be a member of 91 groups if one of those groups is a member of eight groups, for a total of 100: one entity, 91 entity parents, and eight parents of parents. </p>"}}, "documentation": "<p>Contains information about an entity that can be referenced in a Cedar policy.</p> <p>This data type is used as one of the fields in the <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_EntitiesDefinition.html\">EntitiesDefinition</a> structure.</p> <p> <code>{ \"identifier\": { \"entityType\": \"Photo\", \"entityId\": \"VacationPhoto94.jpg\" }, \"attributes\": {}, \"parents\": [ { \"entityType\": \"Album\", \"entityId\": \"alice_folder\" } ] }</code> </p>"}, "EntityList": {"type": "list", "member": {"shape": "EntityItem"}}, "EntityReference": {"type": "structure", "members": {"unspecified": {"shape": "Boolean", "documentation": "<p>Used to indicate that a principal or resource is not specified. This can be used to search for policies that are not associated with a specific principal or resource.</p>"}, "identifier": {"shape": "EntityIdentifier", "documentation": "<p>The identifier of the entity. It can consist of either an EntityType and EntityId, a principal, or a resource.</p>"}}, "documentation": "<p>Contains information about a principal or resource that can be referenced in a Cedar policy.</p> <p>This data type is used as part of the <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_PolicyFilter.html\">PolicyFilter</a> structure that is used as a request parameter for the <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_ListPolicies.html\">ListPolicies</a> operation..</p>", "union": true}, "EntityType": {"type": "string", "max": 200, "min": 1, "pattern": ".*", "sensitive": true}, "EvaluationErrorItem": {"type": "structure", "required": ["errorDescription"], "members": {"errorDescription": {"shape": "String", "documentation": "<p>The error description.</p>"}}, "documentation": "<p>Contains a description of an evaluation error.</p> <p>This data type is a response parameter of the <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_IsAuthorized.html\">IsAuthorized</a>, <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_BatchIsAuthorized.html\">BatchIsAuthorized</a>, and <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_IsAuthorizedWithToken.html\">IsAuthorizedWithToken</a> operations.</p>", "sensitive": true}, "EvaluationErrorList": {"type": "list", "member": {"shape": "EvaluationErrorItem"}}, "GetIdentitySourceInput": {"type": "structure", "required": ["policyStoreId", "identitySourceId"], "members": {"policyStoreId": {"shape": "PolicyStoreId", "documentation": "<p>Specifies the ID of the policy store that contains the identity source you want information about.</p>"}, "identitySourceId": {"shape": "IdentitySourceId", "documentation": "<p>Specifies the ID of the identity source you want information about.</p>"}}}, "GetIdentitySourceOutput": {"type": "structure", "required": ["createdDate", "identitySourceId", "lastUpdatedDate", "policyStoreId", "principalEntityType"], "members": {"createdDate": {"shape": "TimestampFormat", "documentation": "<p>The date and time that the identity source was originally created.</p>"}, "details": {"shape": "IdentitySourceDetails", "documentation": "<p>A structure that describes the configuration of the identity source.</p>", "deprecated": true, "deprecatedMessage": "This attribute has been replaced by configuration.cognitoUserPoolConfiguration"}, "identitySourceId": {"shape": "IdentitySourceId", "documentation": "<p>The ID of the identity source.</p>"}, "lastUpdatedDate": {"shape": "TimestampFormat", "documentation": "<p>The date and time that the identity source was most recently updated.</p>"}, "policyStoreId": {"shape": "PolicyStoreId", "documentation": "<p>The ID of the policy store that contains the identity source.</p>"}, "principalEntityType": {"shape": "PrincipalEntityType", "documentation": "<p>The data type of principals generated for identities authenticated by this identity source.</p>"}, "configuration": {"shape": "ConfigurationDetail", "documentation": "<p>Contains configuration information about an identity source.</p>"}}}, "GetPolicyInput": {"type": "structure", "required": ["policyStoreId", "policyId"], "members": {"policyStoreId": {"shape": "PolicyStoreId", "documentation": "<p>Specifies the ID of the policy store that contains the policy that you want information about.</p>"}, "policyId": {"shape": "PolicyId", "documentation": "<p>Specifies the ID of the policy you want information about.</p>"}}}, "GetPolicyOutput": {"type": "structure", "required": ["policyStoreId", "policyId", "policyType", "definition", "createdDate", "lastUpdatedDate"], "members": {"policyStoreId": {"shape": "PolicyStoreId", "documentation": "<p>The ID of the policy store that contains the policy that you want information about.</p>"}, "policyId": {"shape": "PolicyId", "documentation": "<p>The unique ID of the policy that you want information about.</p>"}, "policyType": {"shape": "PolicyType", "documentation": "<p>The type of the policy.</p>"}, "principal": {"shape": "EntityIdentifier", "documentation": "<p>The principal specified in the policy's scope. This element isn't included in the response when <code>Principal</code> isn't present in the policy content.</p>"}, "resource": {"shape": "EntityIdentifier", "documentation": "<p>The resource specified in the policy's scope. This element isn't included in the response when <code>Resource</code> isn't present in the policy content.</p>"}, "actions": {"shape": "ActionIdentifierList", "documentation": "<p>The action that a policy permits or forbids. For example, <code>{\"actions\": [{\"actionId\": \"ViewPhoto\", \"actionType\": \"PhotoFlash::Action\"}, {\"entityID\": \"SharePhoto\", \"entityType\": \"PhotoFlash::Action\"}]}</code>.</p>"}, "definition": {"shape": "PolicyDefinitionDetail", "documentation": "<p>The definition of the requested policy.</p>"}, "createdDate": {"shape": "TimestampFormat", "documentation": "<p>The date and time that the policy was originally created.</p>"}, "lastUpdatedDate": {"shape": "TimestampFormat", "documentation": "<p>The date and time that the policy was last updated.</p>"}, "effect": {"shape": "PolicyEffect", "documentation": "<p>The effect of the decision that a policy returns to an authorization request. For example, <code>\"effect\": \"Permit\"</code>.</p>"}}}, "GetPolicyStoreInput": {"type": "structure", "required": ["policyStoreId"], "members": {"policyStoreId": {"shape": "PolicyStoreId", "documentation": "<p>Specifies the ID of the policy store that you want information about.</p>"}, "tags": {"shape": "Boolean", "documentation": "<p>Specifies whether to return the tags that are attached to the policy store. If this parameter is included in the API call, the tags are returned, otherwise they are not returned.</p> <note> <p>If this parameter is included in the API call but there are no tags attached to the policy store, the <code>tags</code> response parameter is omitted from the response.</p> </note>"}}}, "GetPolicyStoreOutput": {"type": "structure", "required": ["policyStoreId", "arn", "validationSettings", "createdDate", "lastUpdatedDate"], "members": {"policyStoreId": {"shape": "PolicyStoreId", "documentation": "<p>The ID of the policy store;</p>"}, "arn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the policy store.</p>"}, "validationSettings": {"shape": "ValidationSettings", "documentation": "<p>The current validation settings for the policy store.</p>"}, "createdDate": {"shape": "TimestampFormat", "documentation": "<p>The date and time that the policy store was originally created.</p>"}, "lastUpdatedDate": {"shape": "TimestampFormat", "documentation": "<p>The date and time that the policy store was last updated.</p>"}, "description": {"shape": "PolicyStoreDescription", "documentation": "<p>Descriptive text that you can provide to help with identification of the current policy store.</p>"}, "deletionProtection": {"shape": "DeletionProtection", "documentation": "<p>Specifies whether the policy store can be deleted. If enabled, the policy store can't be deleted.</p> <p>The default state is <code>DISABLED</code>.</p>"}, "cedarVersion": {"shape": "CedarVersion", "documentation": "<p>The version of the Cedar language used with policies, policy templates, and schemas in this policy store. For more information, see <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/userguide/cedar4-faq.html\">Amazon Verified Permissions upgrade to Cedar v4 FAQ</a>.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The list of tags associated with the policy store.</p>"}}}, "GetPolicyTemplateInput": {"type": "structure", "required": ["policyStoreId", "policyTemplateId"], "members": {"policyStoreId": {"shape": "PolicyStoreId", "documentation": "<p>Specifies the ID of the policy store that contains the policy template that you want information about.</p>"}, "policyTemplateId": {"shape": "PolicyTemplateId", "documentation": "<p>Specifies the ID of the policy template that you want information about.</p>"}}}, "GetPolicyTemplateOutput": {"type": "structure", "required": ["policyStoreId", "policyTemplateId", "statement", "createdDate", "lastUpdatedDate"], "members": {"policyStoreId": {"shape": "PolicyStoreId", "documentation": "<p>The ID of the policy store that contains the policy template.</p>"}, "policyTemplateId": {"shape": "PolicyTemplateId", "documentation": "<p>The ID of the policy template.</p>"}, "description": {"shape": "PolicyTemplateDescription", "documentation": "<p>The description of the policy template.</p>"}, "statement": {"shape": "PolicyStatement", "documentation": "<p>The content of the body of the policy template written in the Cedar policy language.</p>"}, "createdDate": {"shape": "TimestampFormat", "documentation": "<p>The date and time that the policy template was originally created.</p>"}, "lastUpdatedDate": {"shape": "TimestampFormat", "documentation": "<p>The date and time that the policy template was most recently updated.</p>"}}}, "GetSchemaInput": {"type": "structure", "required": ["policyStoreId"], "members": {"policyStoreId": {"shape": "PolicyStoreId", "documentation": "<p>Specifies the ID of the policy store that contains the schema.</p>"}}}, "GetSchemaOutput": {"type": "structure", "required": ["policyStoreId", "schema", "createdDate", "lastUpdatedDate"], "members": {"policyStoreId": {"shape": "PolicyStoreId", "documentation": "<p>The ID of the policy store that contains the schema.</p>"}, "schema": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The body of the schema, written in Cedar schema JSON.</p>"}, "createdDate": {"shape": "TimestampFormat", "documentation": "<p>The date and time that the schema was originally created.</p>"}, "lastUpdatedDate": {"shape": "TimestampFormat", "documentation": "<p>The date and time that the schema was most recently updated.</p>"}, "namespaces": {"shape": "NamespaceList", "documentation": "<p>The namespaces of the entities referenced by this schema.</p>"}}}, "GroupEntityType": {"type": "string", "max": 200, "min": 1, "pattern": "([_a-zA-Z][_a-zA-Z0-9]*::)*[_a-zA-Z][_a-zA-Z0-9]*", "sensitive": true}, "IdempotencyToken": {"type": "string", "max": 64, "min": 1, "pattern": "[a-zA-Z0-9-]*"}, "IdentitySourceDetails": {"type": "structure", "members": {"clientIds": {"shape": "ClientIds", "documentation": "<p>The application client IDs associated with the specified Amazon Cognito user pool that are enabled for this identity source.</p>", "deprecated": true, "deprecatedMessage": "This attribute has been replaced by configuration.cognitoUserPoolConfiguration.clientIds"}, "userPoolArn": {"shape": "UserPoolArn", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of the Amazon Cognito user pool whose identities are accessible to this Verified Permissions policy store.</p>", "deprecated": true, "deprecatedMessage": "This attribute has been replaced by configuration.cognitoUserPoolConfiguration.userPoolArn"}, "discoveryUrl": {"shape": "DiscoveryUrl", "documentation": "<p>The well-known URL that points to this user pool's OIDC discovery endpoint. This is a URL string in the following format. This URL replaces the placeholders for both the Amazon Web Services Region and the user pool identifier with those appropriate for this user pool.</p> <p> <code>https://cognito-idp.<i>&lt;region&gt;</i>.amazonaws.com/<i>&lt;user-pool-id&gt;</i>/.well-known/openid-configuration</code> </p>", "deprecated": true, "deprecatedMessage": "This attribute has been replaced by configuration.cognitoUserPoolConfiguration.issuer"}, "openIdIssuer": {"shape": "OpenIdIssuer", "documentation": "<p>A string that identifies the type of OIDC service represented by this identity source. </p> <p>At this time, the only valid value is <code>cognito</code>.</p>", "deprecated": true, "deprecatedMessage": "This attribute has been replaced by configuration"}}, "documentation": "<p>A structure that contains configuration of the identity source.</p> <p>This data type was a response parameter for the <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_GetIdentitySource.html\">GetIdentitySource</a> operation. Replaced by <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_ConfigurationDetail.html\">ConfigurationDetail</a>.</p>", "deprecated": true, "deprecatedMessage": "This shape has been replaced by ConfigurationDetail"}, "IdentitySourceFilter": {"type": "structure", "members": {"principalEntityType": {"shape": "PrincipalEntityType", "documentation": "<p>The Cedar entity type of the principals returned by the identity provider (IdP) associated with this identity source.</p>"}}, "documentation": "<p>A structure that defines characteristics of an identity source that you can use to filter.</p> <p>This data type is a request parameter for the <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_ListIdentityStores.html\">ListIdentityStores</a> operation.</p>"}, "IdentitySourceFilters": {"type": "list", "member": {"shape": "IdentitySourceFilter"}, "max": 10, "min": 0}, "IdentitySourceId": {"type": "string", "max": 200, "min": 1, "pattern": "[a-zA-Z0-9-]*"}, "IdentitySourceItem": {"type": "structure", "required": ["createdDate", "identitySourceId", "lastUpdatedDate", "policyStoreId", "principalEntityType"], "members": {"createdDate": {"shape": "TimestampFormat", "documentation": "<p>The date and time the identity source was originally created.</p>"}, "details": {"shape": "IdentitySourceItemDetails", "documentation": "<p>A structure that contains the details of the associated identity provider (IdP).</p>", "deprecated": true, "deprecatedMessage": "This attribute has been replaced by configuration.cognitoUserPoolConfiguration"}, "identitySourceId": {"shape": "IdentitySourceId", "documentation": "<p>The unique identifier of the identity source.</p>"}, "lastUpdatedDate": {"shape": "TimestampFormat", "documentation": "<p>The date and time the identity source was most recently updated.</p>"}, "policyStoreId": {"shape": "PolicyStoreId", "documentation": "<p>The identifier of the policy store that contains the identity source.</p>"}, "principalEntityType": {"shape": "PrincipalEntityType", "documentation": "<p>The Cedar entity type of the principals returned from the IdP associated with this identity source.</p>"}, "configuration": {"shape": "ConfigurationItem", "documentation": "<p>Contains configuration information about an identity source.</p>"}}, "documentation": "<p>A structure that defines an identity source.</p> <p>This data type is a response parameter to the <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_ListIdentitySources.html\">ListIdentitySources</a> operation.</p>"}, "IdentitySourceItemDetails": {"type": "structure", "members": {"clientIds": {"shape": "ClientIds", "documentation": "<p>The application client IDs associated with the specified Amazon Cognito user pool that are enabled for this identity source.</p>", "deprecated": true, "deprecatedMessage": "This attribute has been replaced by configuration.cognitoUserPoolConfiguration.clientIds"}, "userPoolArn": {"shape": "UserPoolArn", "documentation": "<p>The Amazon Cognito user pool whose identities are accessible to this Verified Permissions policy store.</p>", "deprecated": true, "deprecatedMessage": "This attribute has been replaced by configuration.cognitoUserPoolConfiguration.userPoolArn"}, "discoveryUrl": {"shape": "DiscoveryUrl", "documentation": "<p>The well-known URL that points to this user pool's OIDC discovery endpoint. This is a URL string in the following format. This URL replaces the placeholders for both the Amazon Web Services Region and the user pool identifier with those appropriate for this user pool.</p> <p> <code>https://cognito-idp.<i>&lt;region&gt;</i>.amazonaws.com/<i>&lt;user-pool-id&gt;</i>/.well-known/openid-configuration</code> </p>", "deprecated": true, "deprecatedMessage": "This attribute has been replaced by configuration.cognitoUserPoolConfiguration.issuer"}, "openIdIssuer": {"shape": "OpenIdIssuer", "documentation": "<p>A string that identifies the type of OIDC service represented by this identity source. </p> <p>At this time, the only valid value is <code>cognito</code>.</p>", "deprecated": true, "deprecatedMessage": "This attribute has been replaced by configuration"}}, "documentation": "<p>A structure that contains configuration of the identity source.</p> <p>This data type was a response parameter for the <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_ListIdentitySources.html\">ListIdentitySources</a> operation. Replaced by <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_ConfigurationItem.html\">ConfigurationItem</a>.</p>", "deprecated": true, "deprecatedMessage": "This shape has been replaced by ConfigurationItem"}, "IdentitySources": {"type": "list", "member": {"shape": "IdentitySourceItem"}}, "InternalServerException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The request failed because of an internal error. Try your request again later</p>", "exception": true, "fault": true, "retryable": {"throttling": false}}, "InvalidStateException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The policy store can't be deleted because deletion protection is enabled. To delete this policy store, disable deletion protection.</p>", "exception": true}, "IpAddr": {"type": "string", "max": 44, "min": 1, "pattern": "[0-9a-fA-F\\.:\\/]*", "sensitive": true}, "IsAuthorizedInput": {"type": "structure", "required": ["policyStoreId"], "members": {"policyStoreId": {"shape": "PolicyStoreId", "documentation": "<p>Specifies the ID of the policy store. Policies in this policy store will be used to make an authorization decision for the input.</p>"}, "principal": {"shape": "EntityIdentifier", "documentation": "<p>Specifies the principal for which the authorization decision is to be made.</p>"}, "action": {"shape": "ActionIdentifier", "documentation": "<p>Specifies the requested action to be authorized. For example, is the principal authorized to perform this action on the resource?</p>"}, "resource": {"shape": "EntityIdentifier", "documentation": "<p>Specifies the resource for which the authorization decision is to be made.</p>"}, "context": {"shape": "ContextDefinition", "documentation": "<p>Specifies additional context that can be used to make more granular authorization decisions.</p>"}, "entities": {"shape": "EntitiesDefinition", "documentation": "<p>(Optional) Specifies the list of resources and principals and their associated attributes that Verified Permissions can examine when evaluating the policies. These additional entities and their attributes can be referenced and checked by conditional elements in the policies in the specified policy store.</p> <note> <p>You can include only principal and resource entities in this parameter; you can't include actions. You must specify actions in the schema.</p> </note>"}}}, "IsAuthorizedOutput": {"type": "structure", "required": ["decision", "determiningPolicies", "errors"], "members": {"decision": {"shape": "Decision", "documentation": "<p>An authorization decision that indicates if the authorization request should be allowed or denied.</p>"}, "determiningPolicies": {"shape": "DeterminingPolicyList", "documentation": "<p>The list of determining policies used to make the authorization decision. For example, if there are two matching policies, where one is a forbid and the other is a permit, then the forbid policy will be the determining policy. In the case of multiple matching permit policies then there would be multiple determining policies. In the case that no policies match, and hence the response is DENY, there would be no determining policies.</p>"}, "errors": {"shape": "EvaluationErrorList", "documentation": "<p>Errors that occurred while making an authorization decision, for example, a policy references an Entity or entity Attribute that does not exist in the slice.</p>"}}}, "IsAuthorizedWithTokenInput": {"type": "structure", "required": ["policyStoreId"], "members": {"policyStoreId": {"shape": "PolicyStoreId", "documentation": "<p>Specifies the ID of the policy store. Policies in this policy store will be used to make an authorization decision for the input.</p>"}, "identityToken": {"shape": "Token", "documentation": "<p>Specifies an identity token for the principal to be authorized. This token is provided to you by the identity provider (IdP) associated with the specified identity source. You must specify either an <code>accessToken</code>, an <code>identityToken</code>, or both.</p> <p>Must be an ID token. Verified Permissions returns an error if the <code>token_use</code> claim in the submitted token isn't <code>id</code>.</p>"}, "accessToken": {"shape": "Token", "documentation": "<p>Specifies an access token for the principal to be authorized. This token is provided to you by the identity provider (IdP) associated with the specified identity source. You must specify either an <code>accessToken</code>, an <code>identityToken</code>, or both.</p> <p>Must be an access token. Verified Permissions returns an error if the <code>token_use</code> claim in the submitted token isn't <code>access</code>.</p>"}, "action": {"shape": "ActionIdentifier", "documentation": "<p>Specifies the requested action to be authorized. Is the specified principal authorized to perform this action on the specified resource.</p>"}, "resource": {"shape": "EntityIdentifier", "documentation": "<p>Specifies the resource for which the authorization decision is made. For example, is the principal allowed to perform the action on the resource?</p>"}, "context": {"shape": "ContextDefinition", "documentation": "<p>Specifies additional context that can be used to make more granular authorization decisions.</p>"}, "entities": {"shape": "EntitiesDefinition", "documentation": "<p>(Optional) Specifies the list of resources and their associated attributes that Verified Permissions can examine when evaluating the policies. These additional entities and their attributes can be referenced and checked by conditional elements in the policies in the specified policy store.</p> <important> <p>You can't include principals in this parameter, only resource and action entities. This parameter can't include any entities of a type that matches the user or group entity types that you defined in your identity source.</p> <ul> <li> <p>The <code>IsAuthorizedWithToken</code> operation takes principal attributes from <b> <i>only</i> </b> the <code>identityToken</code> or <code>accessToken</code> passed to the operation.</p> </li> <li> <p>For action entities, you can include only their <code>Identifier</code> and <code>EntityType</code>. </p> </li> </ul> </important>"}}}, "IsAuthorizedWithTokenOutput": {"type": "structure", "required": ["decision", "determiningPolicies", "errors"], "members": {"decision": {"shape": "Decision", "documentation": "<p>An authorization decision that indicates if the authorization request should be allowed or denied.</p>"}, "determiningPolicies": {"shape": "DeterminingPolicyList", "documentation": "<p>The list of determining policies used to make the authorization decision. For example, if there are multiple matching policies, where at least one is a forbid policy, then because forbid always overrides permit the forbid policies are the determining policies. If all matching policies are permit policies, then those policies are the determining policies. When no policies match and the response is the default DENY, there are no determining policies.</p>"}, "errors": {"shape": "EvaluationErrorList", "documentation": "<p>Errors that occurred while making an authorization decision. For example, a policy references an entity or entity attribute that does not exist in the slice.</p>"}, "principal": {"shape": "EntityIdentifier", "documentation": "<p>The identifier of the principal in the ID or access token.</p>"}}}, "Issuer": {"type": "string", "max": 2048, "min": 1, "pattern": "https://.*"}, "ListIdentitySourcesInput": {"type": "structure", "required": ["policyStoreId"], "members": {"policyStoreId": {"shape": "PolicyStoreId", "documentation": "<p>Specifies the ID of the policy store that contains the identity sources that you want to list.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>Specifies that you want to receive the next page of results. Valid only if you received a <code>NextToken</code> response in the previous request. If you did, it indicates that more output is available. Set this parameter to the value provided by the previous call's <code>NextToken</code> response to request the next page of results.</p>"}, "maxResults": {"shape": "ListIdentitySourcesMaxResults", "documentation": "<p>Specifies the total number of results that you want included in each response. If additional items exist beyond the number you specify, the <code>NextToken</code> response element is returned with a value (not null). Include the specified value as the <code>NextToken</code> request parameter in the next call to the operation to get the next set of results. Note that the service might return fewer results than the maximum even when there are more results available. You should check <code>NextToken</code> after every operation to ensure that you receive all of the results.</p> <p>If you do not specify this parameter, the operation defaults to 10 identity sources per response. You can specify a maximum of 50 identity sources per response.</p>"}, "filters": {"shape": "IdentitySourceFilters", "documentation": "<p>Specifies characteristics of an identity source that you can use to limit the output to matching identity sources.</p>"}}}, "ListIdentitySourcesMaxResults": {"type": "integer", "box": true, "min": 1}, "ListIdentitySourcesOutput": {"type": "structure", "required": ["identitySources"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>If present, this value indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>. This indicates that this is the last page of results.</p>"}, "identitySources": {"shape": "IdentitySources", "documentation": "<p>The list of identity sources stored in the specified policy store.</p>"}}}, "ListPoliciesInput": {"type": "structure", "required": ["policyStoreId"], "members": {"policyStoreId": {"shape": "PolicyStoreId", "documentation": "<p>Specifies the ID of the policy store you want to list policies from.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>Specifies that you want to receive the next page of results. Valid only if you received a <code>NextToken</code> response in the previous request. If you did, it indicates that more output is available. Set this parameter to the value provided by the previous call's <code>NextToken</code> response to request the next page of results.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>Specifies the total number of results that you want included in each response. If additional items exist beyond the number you specify, the <code>NextToken</code> response element is returned with a value (not null). Include the specified value as the <code>NextToken</code> request parameter in the next call to the operation to get the next set of results. Note that the service might return fewer results than the maximum even when there are more results available. You should check <code>NextToken</code> after every operation to ensure that you receive all of the results.</p> <p>If you do not specify this parameter, the operation defaults to 10 policies per response. You can specify a maximum of 50 policies per response.</p>"}, "filter": {"shape": "PolicyFilter", "documentation": "<p>Specifies a filter that limits the response to only policies that match the specified criteria. For example, you list only the policies that reference a specified principal.</p>"}}}, "ListPoliciesOutput": {"type": "structure", "required": ["policies"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>If present, this value indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>. This indicates that this is the last page of results.</p>"}, "policies": {"shape": "PolicyList", "documentation": "<p>Lists all policies that are available in the specified policy store.</p>"}}}, "ListPolicyStoresInput": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>Specifies that you want to receive the next page of results. Valid only if you received a <code>NextToken</code> response in the previous request. If you did, it indicates that more output is available. Set this parameter to the value provided by the previous call's <code>NextToken</code> response to request the next page of results.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>Specifies the total number of results that you want included in each response. If additional items exist beyond the number you specify, the <code>NextToken</code> response element is returned with a value (not null). Include the specified value as the <code>NextToken</code> request parameter in the next call to the operation to get the next set of results. Note that the service might return fewer results than the maximum even when there are more results available. You should check <code>NextToken</code> after every operation to ensure that you receive all of the results.</p> <p>If you do not specify this parameter, the operation defaults to 10 policy stores per response. You can specify a maximum of 50 policy stores per response.</p>"}}}, "ListPolicyStoresOutput": {"type": "structure", "required": ["policyStores"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>If present, this value indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>. This indicates that this is the last page of results.</p>"}, "policyStores": {"shape": "PolicyStoreList", "documentation": "<p>The list of policy stores in the account.</p>"}}}, "ListPolicyTemplatesInput": {"type": "structure", "required": ["policyStoreId"], "members": {"policyStoreId": {"shape": "PolicyStoreId", "documentation": "<p>Specifies the ID of the policy store that contains the policy templates you want to list.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>Specifies that you want to receive the next page of results. Valid only if you received a <code>NextToken</code> response in the previous request. If you did, it indicates that more output is available. Set this parameter to the value provided by the previous call's <code>NextToken</code> response to request the next page of results.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>Specifies the total number of results that you want included in each response. If additional items exist beyond the number you specify, the <code>NextToken</code> response element is returned with a value (not null). Include the specified value as the <code>NextToken</code> request parameter in the next call to the operation to get the next set of results. Note that the service might return fewer results than the maximum even when there are more results available. You should check <code>NextToken</code> after every operation to ensure that you receive all of the results.</p> <p>If you do not specify this parameter, the operation defaults to 10 policy templates per response. You can specify a maximum of 50 policy templates per response.</p>"}}}, "ListPolicyTemplatesOutput": {"type": "structure", "required": ["policyTemplates"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>If present, this value indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>. This indicates that this is the last page of results.</p>"}, "policyTemplates": {"shape": "PolicyTemplatesList", "documentation": "<p>The list of the policy templates in the specified policy store.</p>"}}}, "ListTagsForResourceInput": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "AmazonResourceName", "documentation": "<p>The ARN of the resource for which you want to view tags.</p>"}}}, "ListTagsForResourceOutput": {"type": "structure", "members": {"tags": {"shape": "TagMap", "documentation": "<p>The list of tags associated with the resource.</p>"}}}, "LongAttribute": {"type": "long", "box": true, "sensitive": true}, "MaxResults": {"type": "integer", "box": true, "min": 1}, "Namespace": {"type": "string", "max": 100, "min": 0, "pattern": ".*", "sensitive": true}, "NamespaceList": {"type": "list", "member": {"shape": "Namespace"}}, "NextToken": {"type": "string", "max": 8000, "min": 1, "pattern": "[A-Za-z0-9-_=+/\\.]*"}, "OpenIdConnectAccessTokenConfiguration": {"type": "structure", "members": {"principalIdClaim": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>The claim that determines the principal in OIDC access tokens. For example, <code>sub</code>.</p>"}, "audiences": {"shape": "Audiences", "documentation": "<p>The access token <code>aud</code> claim values that you want to accept in your policy store. For example, <code>https://myapp.example.com, https://myapp2.example.com</code>.</p>"}}, "documentation": "<p>The configuration of an OpenID Connect (OIDC) identity source for handling access token claims. Contains the claim that you want to identify as the principal in an authorization request, and the values of the <code>aud</code> claim, or audiences, that you want to accept.</p> <p>This data type is part of a <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_OpenIdConnectTokenSelection.html\">OpenIdConnectTokenSelection</a> structure, which is a parameter of <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_CreateIdentitySource.html\">CreateIdentitySource</a>.</p>"}, "OpenIdConnectAccessTokenConfigurationDetail": {"type": "structure", "members": {"principalIdClaim": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>The claim that determines the principal in OIDC access tokens. For example, <code>sub</code>.</p>"}, "audiences": {"shape": "Audiences", "documentation": "<p>The access token <code>aud</code> claim values that you want to accept in your policy store. For example, <code>https://myapp.example.com, https://myapp2.example.com</code>.</p>"}}, "documentation": "<p>The configuration of an OpenID Connect (OIDC) identity source for handling access token claims. Contains the claim that you want to identify as the principal in an authorization request, and the values of the <code>aud</code> claim, or audiences, that you want to accept.</p> <p>This data type is part of a <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_OpenIdConnectTokenSelectionDetail.html\">OpenIdConnectTokenSelectionDetail</a> structure, which is a parameter of <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_GetIdentitySource.html\">GetIdentitySource</a>.</p>"}, "OpenIdConnectAccessTokenConfigurationItem": {"type": "structure", "members": {"principalIdClaim": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>The claim that determines the principal in OIDC access tokens. For example, <code>sub</code>.</p>"}, "audiences": {"shape": "Audiences", "documentation": "<p>The access token <code>aud</code> claim values that you want to accept in your policy store. For example, <code>https://myapp.example.com, https://myapp2.example.com</code>.</p>"}}, "documentation": "<p>The configuration of an OpenID Connect (OIDC) identity source for handling access token claims. Contains the claim that you want to identify as the principal in an authorization request, and the values of the <code>aud</code> claim, or audiences, that you want to accept.</p> <p>This data type is part of a <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_OpenIdConnectTokenSelectionItem.html\">OpenIdConnectTokenSelectionItem</a> structure, which is a parameter of <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_ListIdentitySources.html\">ListIdentitySources</a>.</p>"}, "OpenIdConnectConfiguration": {"type": "structure", "required": ["issuer", "tokenSelection"], "members": {"issuer": {"shape": "Issuer", "documentation": "<p>The issuer URL of an OIDC identity provider. This URL must have an OIDC discovery endpoint at the path <code>.well-known/openid-configuration</code>.</p>"}, "entityIdPrefix": {"shape": "EntityIdPrefix", "documentation": "<p>A descriptive string that you want to prefix to user entities from your OIDC identity provider. For example, if you set an <code>entityIdPrefix</code> of <code>MyOIDCProvider</code>, you can reference principals in your policies in the format <code>MyCorp::User::MyOIDCProvider|Carlos</code>.</p>"}, "groupConfiguration": {"shape": "OpenIdConnectGroupConfiguration", "documentation": "<p>The claim in OIDC identity provider tokens that indicates a user's group membership, and the entity type that you want to map it to. For example, this object can map the contents of a <code>groups</code> claim to <code>MyCorp::UserGroup</code>.</p>"}, "tokenSelection": {"shape": "OpenIdConnectTokenSelection", "documentation": "<p>The token type that you want to process from your OIDC identity provider. Your policy store can process either identity (ID) or access tokens from a given OIDC identity source.</p>"}}, "documentation": "<p>Contains configuration details of an OpenID Connect (OIDC) identity provider, or identity source, that Verified Permissions can use to generate entities from authenticated identities. It specifies the issuer URL, token type that you want to use, and policy store entity details.</p> <p>This data type is part of a <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_Configuration.html\">Configuration</a> structure, which is a parameter to <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_CreateIdentitySource.html\">CreateIdentitySource</a>.</p>"}, "OpenIdConnectConfigurationDetail": {"type": "structure", "required": ["issuer", "tokenSelection"], "members": {"issuer": {"shape": "Issuer", "documentation": "<p>The issuer URL of an OIDC identity provider. This URL must have an OIDC discovery endpoint at the path <code>.well-known/openid-configuration</code>.</p>"}, "entityIdPrefix": {"shape": "EntityIdPrefix", "documentation": "<p>A descriptive string that you want to prefix to user entities from your OIDC identity provider. For example, if you set an <code>entityIdPrefix</code> of <code>MyOIDCProvider</code>, you can reference principals in your policies in the format <code>MyCorp::User::MyOIDCProvider|Carlos</code>.</p>"}, "groupConfiguration": {"shape": "OpenIdConnectGroupConfigurationDetail", "documentation": "<p>The claim in OIDC identity provider tokens that indicates a user's group membership, and the entity type that you want to map it to. For example, this object can map the contents of a <code>groups</code> claim to <code>MyCorp::UserGroup</code>.</p>"}, "tokenSelection": {"shape": "OpenIdConnectTokenSelectionDetail", "documentation": "<p>The token type that you want to process from your OIDC identity provider. Your policy store can process either identity (ID) or access tokens from a given OIDC identity source.</p>"}}, "documentation": "<p>Contains configuration details of an OpenID Connect (OIDC) identity provider, or identity source, that Verified Permissions can use to generate entities from authenticated identities. It specifies the issuer URL, token type that you want to use, and policy store entity details.</p> <p>This data type is part of a <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_ConfigurationDetail.html\">ConfigurationDetail</a> structure, which is a parameter to <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_GetIdentitySource.html\">GetIdentitySource</a>.</p>"}, "OpenIdConnectConfigurationItem": {"type": "structure", "required": ["issuer", "tokenSelection"], "members": {"issuer": {"shape": "Issuer", "documentation": "<p>The issuer URL of an OIDC identity provider. This URL must have an OIDC discovery endpoint at the path <code>.well-known/openid-configuration</code>.</p>"}, "entityIdPrefix": {"shape": "EntityIdPrefix", "documentation": "<p>A descriptive string that you want to prefix to user entities from your OIDC identity provider. For example, if you set an <code>entityIdPrefix</code> of <code>MyOIDCProvider</code>, you can reference principals in your policies in the format <code>MyCorp::User::MyOIDCProvider|Carlos</code>.</p>"}, "groupConfiguration": {"shape": "OpenIdConnectGroupConfigurationItem", "documentation": "<p>The claim in OIDC identity provider tokens that indicates a user's group membership, and the entity type that you want to map it to. For example, this object can map the contents of a <code>groups</code> claim to <code>MyCorp::UserGroup</code>.</p>"}, "tokenSelection": {"shape": "OpenIdConnectTokenSelectionItem", "documentation": "<p>The token type that you want to process from your OIDC identity provider. Your policy store can process either identity (ID) or access tokens from a given OIDC identity source.</p>"}}, "documentation": "<p>Contains configuration details of an OpenID Connect (OIDC) identity provider, or identity source, that Verified Permissions can use to generate entities from authenticated identities. It specifies the issuer URL, token type that you want to use, and policy store entity details.</p> <p>This data type is part of a <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_ConfigurationDetail.html\">ConfigurationItem</a> structure, which is a parameter to <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_ListIdentitySources.html\">ListIdentitySources</a>.</p>"}, "OpenIdConnectGroupConfiguration": {"type": "structure", "required": ["groupClaim", "groupEntityType"], "members": {"groupClaim": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>The token claim that you want Verified Permissions to interpret as group membership. For example, <code>groups</code>.</p>"}, "groupEntityType": {"shape": "GroupEntityType", "documentation": "<p>The policy store entity type that you want to map your users' group claim to. For example, <code>MyCorp::UserGroup</code>. A group entity type is an entity that can have a user entity type as a member.</p>"}}, "documentation": "<p>The claim in OIDC identity provider tokens that indicates a user's group membership, and the entity type that you want to map it to. For example, this object can map the contents of a <code>groups</code> claim to <code>MyCorp::UserGroup</code>.</p> <p>This data type is part of a <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_OpenIdConnectConfiguration.html\">OpenIdConnectConfiguration</a> structure, which is a parameter of <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_CreateIdentitySource.html\">CreateIdentitySource</a>.</p>"}, "OpenIdConnectGroupConfigurationDetail": {"type": "structure", "required": ["groupClaim", "groupEntityType"], "members": {"groupClaim": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>The token claim that you want Verified Permissions to interpret as group membership. For example, <code>groups</code>.</p>"}, "groupEntityType": {"shape": "GroupEntityType", "documentation": "<p>The policy store entity type that you want to map your users' group claim to. For example, <code>MyCorp::UserGroup</code>. A group entity type is an entity that can have a user entity type as a member.</p>"}}, "documentation": "<p>The claim in OIDC identity provider tokens that indicates a user's group membership, and the entity type that you want to map it to. For example, this object can map the contents of a <code>groups</code> claim to <code>MyCorp::UserGroup</code>.</p> <p>This data type is part of a <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_OpenIdConnectConfigurationDetail.html\">OpenIdConnectConfigurationDetail</a> structure, which is a parameter of <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_GetIdentitySource.html\">GetIdentitySource</a>.</p>"}, "OpenIdConnectGroupConfigurationItem": {"type": "structure", "required": ["groupClaim", "groupEntityType"], "members": {"groupClaim": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>The token claim that you want Verified Permissions to interpret as group membership. For example, <code>groups</code>.</p>"}, "groupEntityType": {"shape": "GroupEntityType", "documentation": "<p>The policy store entity type that you want to map your users' group claim to. For example, <code>MyCorp::UserGroup</code>. A group entity type is an entity that can have a user entity type as a member.</p>"}}, "documentation": "<p>The claim in OIDC identity provider tokens that indicates a user's group membership, and the entity type that you want to map it to. For example, this object can map the contents of a <code>groups</code> claim to <code>MyCorp::UserGroup</code>.</p> <p>This data type is part of a <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_OpenIdConnectConfigurationItem.html\">OpenIdConnectConfigurationItem</a> structure, which is a parameter of <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_ListIdentitySources.html\">ListIdentitySourcea</a>.</p>"}, "OpenIdConnectIdentityTokenConfiguration": {"type": "structure", "members": {"principalIdClaim": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>The claim that determines the principal in OIDC access tokens. For example, <code>sub</code>.</p>"}, "clientIds": {"shape": "ClientIds", "documentation": "<p>The ID token audience, or client ID, claim values that you want to accept in your policy store from an OIDC identity provider. For example, <code>1example23456789, 2example10111213</code>.</p>"}}, "documentation": "<p>The configuration of an OpenID Connect (OIDC) identity source for handling identity (ID) token claims. Contains the claim that you want to identify as the principal in an authorization request, and the values of the <code>aud</code> claim, or audiences, that you want to accept.</p> <p>This data type is part of a <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_OpenIdConnectTokenSelection.html\">OpenIdConnectTokenSelection</a> structure, which is a parameter of <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_CreateIdentitySource.html\">CreateIdentitySource</a>.</p>"}, "OpenIdConnectIdentityTokenConfigurationDetail": {"type": "structure", "members": {"principalIdClaim": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>The claim that determines the principal in OIDC access tokens. For example, <code>sub</code>.</p>"}, "clientIds": {"shape": "ClientIds", "documentation": "<p>The ID token audience, or client ID, claim values that you want to accept in your policy store from an OIDC identity provider. For example, <code>1example23456789, 2example10111213</code>.</p>"}}, "documentation": "<p>The configuration of an OpenID Connect (OIDC) identity source for handling identity (ID) token claims. Contains the claim that you want to identify as the principal in an authorization request, and the values of the <code>aud</code> claim, or audiences, that you want to accept.</p> <p>This data type is part of a <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_OpenIdConnectTokenSelectionDetail.html\">OpenIdConnectTokenSelectionDetail</a> structure, which is a parameter of <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_GetIdentitySource.html\">GetIdentitySource</a>.</p>"}, "OpenIdConnectIdentityTokenConfigurationItem": {"type": "structure", "members": {"principalIdClaim": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>The claim that determines the principal in OIDC access tokens. For example, <code>sub</code>.</p>"}, "clientIds": {"shape": "ClientIds", "documentation": "<p>The ID token audience, or client ID, claim values that you want to accept in your policy store from an OIDC identity provider. For example, <code>1example23456789, 2example10111213</code>.</p>"}}, "documentation": "<p>The configuration of an OpenID Connect (OIDC) identity source for handling identity (ID) token claims. Contains the claim that you want to identify as the principal in an authorization request, and the values of the <code>aud</code> claim, or audiences, that you want to accept.</p> <p>This data type is part of a <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_OpenIdConnectTokenSelectionItem.html\">OpenIdConnectTokenSelectionItem</a> structure, which is a parameter of <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_ListIdentitySources.html\">ListIdentitySources</a>.</p>"}, "OpenIdConnectTokenSelection": {"type": "structure", "members": {"accessTokenOnly": {"shape": "OpenIdConnectAccessTokenConfiguration", "documentation": "<p>The OIDC configuration for processing access tokens. Contains allowed audience claims, for example <code>https://auth.example.com</code>, and the claim that you want to map to the principal, for example <code>sub</code>.</p>"}, "identityTokenOnly": {"shape": "OpenIdConnectIdentityTokenConfiguration", "documentation": "<p>The OIDC configuration for processing identity (ID) tokens. Contains allowed client ID claims, for example <code>1example23456789</code>, and the claim that you want to map to the principal, for example <code>sub</code>.</p>"}}, "documentation": "<p>The token type that you want to process from your OIDC identity provider. Your policy store can process either identity (ID) or access tokens from a given OIDC identity source.</p> <p>This data type is part of a <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_OpenIdConnectConfiguration.html\">OpenIdConnectConfiguration</a> structure, which is a parameter of <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_CreateIdentitySource.html\">CreateIdentitySource</a>.</p>", "union": true}, "OpenIdConnectTokenSelectionDetail": {"type": "structure", "members": {"accessTokenOnly": {"shape": "OpenIdConnectAccessTokenConfigurationDetail", "documentation": "<p>The OIDC configuration for processing access tokens. Contains allowed audience claims, for example <code>https://auth.example.com</code>, and the claim that you want to map to the principal, for example <code>sub</code>.</p>"}, "identityTokenOnly": {"shape": "OpenIdConnectIdentityTokenConfigurationDetail", "documentation": "<p>The OIDC configuration for processing identity (ID) tokens. Contains allowed client ID claims, for example <code>1example23456789</code>, and the claim that you want to map to the principal, for example <code>sub</code>.</p>"}}, "documentation": "<p>The token type that you want to process from your OIDC identity provider. Your policy store can process either identity (ID) or access tokens from a given OIDC identity source.</p> <p>This data type is part of a <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_OpenIdConnectConfigurationDetail.html\">OpenIdConnectConfigurationDetail</a> structure, which is a parameter of <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_GetIdentitySource.html\">GetIdentitySource</a>.</p>", "union": true}, "OpenIdConnectTokenSelectionItem": {"type": "structure", "members": {"accessTokenOnly": {"shape": "OpenIdConnectAccessTokenConfigurationItem", "documentation": "<p>The OIDC configuration for processing access tokens. Contains allowed audience claims, for example <code>https://auth.example.com</code>, and the claim that you want to map to the principal, for example <code>sub</code>.</p>"}, "identityTokenOnly": {"shape": "OpenIdConnectIdentityTokenConfigurationItem", "documentation": "<p>The OIDC configuration for processing identity (ID) tokens. Contains allowed client ID claims, for example <code>1example23456789</code>, and the claim that you want to map to the principal, for example <code>sub</code>.</p>"}}, "documentation": "<p>The token type that you want to process from your OIDC identity provider. Your policy store can process either identity (ID) or access tokens from a given OIDC identity source.</p> <p>This data type is part of a <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_OpenIdConnectConfigurationItem.html\">OpenIdConnectConfigurationItem</a> structure, which is a parameter of <a href=\"http://amazonaws.com/verifiedpermissions/latest/apireference/API_ListIdentitySources.html\">ListIdentitySources</a>.</p>", "union": true}, "OpenIdIssuer": {"type": "string", "enum": ["COGNITO"]}, "ParentList": {"type": "list", "member": {"shape": "EntityIdentifier"}}, "PolicyDefinition": {"type": "structure", "members": {"static": {"shape": "StaticPolicyDefinition", "documentation": "<p>A structure that describes a static policy. An static policy doesn't use a template or allow placeholders for entities.</p>"}, "templateLinked": {"shape": "TemplateLinkedPolicyDefinition", "documentation": "<p>A structure that describes a policy that was instantiated from a template. The template can specify placeholders for <code>principal</code> and <code>resource</code>. When you use <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_CreatePolicy.html\">CreatePolicy</a> to create a policy from a template, you specify the exact principal and resource to use for the instantiated policy.</p>"}}, "documentation": "<p>A structure that contains the details for a Cedar policy definition. It includes the policy type, a description, and a policy body. This is a top level data type used to create a policy.</p> <p>This data type is used as a request parameter for the <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_CreatePolicy.html\">CreatePolicy</a> operation. This structure must always have either an <code>static</code> or a <code>templateLinked</code> element.</p>", "union": true}, "PolicyDefinitionDetail": {"type": "structure", "members": {"static": {"shape": "StaticPolicyDefinitionDetail", "documentation": "<p>Information about a static policy that wasn't created with a policy template.</p>"}, "templateLinked": {"shape": "TemplateLinkedPolicyDefinitionDetail", "documentation": "<p>Information about a template-linked policy that was created by instantiating a policy template.</p>"}}, "documentation": "<p>A structure that describes a policy definition. It must always have either an <code>static</code> or a <code>templateLinked</code> element.</p> <p>This data type is used as a response parameter for the <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_GetPolicy.html\">GetPolicy</a> operation.</p>", "union": true}, "PolicyDefinitionItem": {"type": "structure", "members": {"static": {"shape": "StaticPolicyDefinitionItem", "documentation": "<p>Information about a static policy that wasn't created with a policy template.</p>"}, "templateLinked": {"shape": "TemplateLinkedPolicyDefinitionItem", "documentation": "<p>Information about a template-linked policy that was created by instantiating a policy template.</p>"}}, "documentation": "<p>A structure that describes a <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_PolicyDefinintion.html\">PolicyDefinintion</a>. It will always have either an <code>StaticPolicy</code> or a <code>TemplateLinkedPolicy</code> element.</p> <p>This data type is used as a response parameter for the <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_CreatePolicy.html\">CreatePolicy</a> and <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_ListPolicies.html\">ListPolicies</a> operations. </p>", "union": true}, "PolicyEffect": {"type": "string", "enum": ["Permit", "Forbid"]}, "PolicyFilter": {"type": "structure", "members": {"principal": {"shape": "EntityReference", "documentation": "<p>Filters the output to only policies that reference the specified principal.</p>"}, "resource": {"shape": "EntityReference", "documentation": "<p>Filters the output to only policies that reference the specified resource.</p>"}, "policyType": {"shape": "PolicyType", "documentation": "<p>Filters the output to only policies of the specified type.</p>"}, "policyTemplateId": {"shape": "PolicyTemplateId", "documentation": "<p>Filters the output to only template-linked policies that were instantiated from the specified policy template.</p>"}}, "documentation": "<p>Contains information about a filter to refine policies returned in a query.</p> <p>This data type is used as a response parameter for the <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_ListPolicies.html\">ListPolicies</a> operation.</p>"}, "PolicyId": {"type": "string", "max": 200, "min": 1, "pattern": "[a-zA-Z0-9-]*"}, "PolicyItem": {"type": "structure", "required": ["policyStoreId", "policyId", "policyType", "definition", "createdDate", "lastUpdatedDate"], "members": {"policyStoreId": {"shape": "PolicyStoreId", "documentation": "<p>The identifier of the policy store where the policy you want information about is stored.</p>"}, "policyId": {"shape": "PolicyId", "documentation": "<p>The identifier of the policy you want information about.</p>"}, "policyType": {"shape": "PolicyType", "documentation": "<p>The type of the policy. This is one of the following values:</p> <ul> <li> <p> <code>STATIC</code> </p> </li> <li> <p> <code>TEMPLATE_LINKED</code> </p> </li> </ul>"}, "principal": {"shape": "EntityIdentifier", "documentation": "<p>The principal associated with the policy.</p>"}, "resource": {"shape": "EntityIdentifier", "documentation": "<p>The resource associated with the policy.</p>"}, "actions": {"shape": "ActionIdentifierList", "documentation": "<p>The action that a policy permits or forbids. For example, <code>{\"actions\": [{\"actionId\": \"ViewPhoto\", \"actionType\": \"PhotoFlash::Action\"}, {\"entityID\": \"SharePhoto\", \"entityType\": \"PhotoFlash::Action\"}]}</code>.</p>"}, "definition": {"shape": "PolicyDefinitionItem", "documentation": "<p>The policy definition of an item in the list of policies returned.</p>"}, "createdDate": {"shape": "TimestampFormat", "documentation": "<p>The date and time the policy was created.</p>"}, "lastUpdatedDate": {"shape": "TimestampFormat", "documentation": "<p>The date and time the policy was most recently updated.</p>"}, "effect": {"shape": "PolicyEffect", "documentation": "<p>The effect of the decision that a policy returns to an authorization request. For example, <code>\"effect\": \"Permit\"</code>.</p>"}}, "documentation": "<p>Contains information about a policy.</p> <p>This data type is used as a response parameter for the <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_ListPolicies.html\">ListPolicies</a> operation.</p>"}, "PolicyList": {"type": "list", "member": {"shape": "PolicyItem"}}, "PolicyStatement": {"type": "string", "max": 10000, "min": 1, "sensitive": true}, "PolicyStoreDescription": {"type": "string", "max": 150, "min": 0, "sensitive": true}, "PolicyStoreId": {"type": "string", "max": 200, "min": 1, "pattern": "[a-zA-Z0-9-/_]*"}, "PolicyStoreItem": {"type": "structure", "required": ["policyStoreId", "arn", "createdDate"], "members": {"policyStoreId": {"shape": "PolicyStoreId", "documentation": "<p>The unique identifier of the policy store.</p>"}, "arn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the policy store.</p>"}, "createdDate": {"shape": "TimestampFormat", "documentation": "<p>The date and time the policy was created.</p>"}, "lastUpdatedDate": {"shape": "TimestampFormat", "documentation": "<p>The date and time the policy store was most recently updated.</p>"}, "description": {"shape": "PolicyStoreDescription", "documentation": "<p>Descriptive text that you can provide to help with identification of the current policy store.</p>"}}, "documentation": "<p>Contains information about a policy store.</p> <p>This data type is used as a response parameter for the <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_ListPolicyStores.html\">ListPolicyStores</a> operation.</p>"}, "PolicyStoreList": {"type": "list", "member": {"shape": "PolicyStoreItem"}}, "PolicyTemplateDescription": {"type": "string", "max": 150, "min": 0, "sensitive": true}, "PolicyTemplateId": {"type": "string", "max": 200, "min": 1, "pattern": "[a-zA-Z0-9-/_]*"}, "PolicyTemplateItem": {"type": "structure", "required": ["policyStoreId", "policyTemplateId", "createdDate", "lastUpdatedDate"], "members": {"policyStoreId": {"shape": "PolicyStoreId", "documentation": "<p>The unique identifier of the policy store that contains the template.</p>"}, "policyTemplateId": {"shape": "PolicyTemplateId", "documentation": "<p>The unique identifier of the policy template.</p>"}, "description": {"shape": "PolicyTemplateDescription", "documentation": "<p>The description attached to the policy template.</p>"}, "createdDate": {"shape": "TimestampFormat", "documentation": "<p>The date and time that the policy template was created.</p>"}, "lastUpdatedDate": {"shape": "TimestampFormat", "documentation": "<p>The date and time that the policy template was most recently updated.</p>"}}, "documentation": "<p>Contains details about a policy template</p> <p>This data type is used as a response parameter for the <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_ListPolicyTemplates.html\">ListPolicyTemplates</a> operation.</p>"}, "PolicyTemplatesList": {"type": "list", "member": {"shape": "PolicyTemplateItem"}}, "PolicyType": {"type": "string", "enum": ["STATIC", "TEMPLATE_LINKED"]}, "PrincipalEntityType": {"type": "string", "max": 200, "min": 1, "pattern": ".*", "sensitive": true}, "PutSchemaInput": {"type": "structure", "required": ["policyStoreId", "definition"], "members": {"policyStoreId": {"shape": "PolicyStoreId", "documentation": "<p>Specifies the ID of the policy store in which to place the schema.</p>"}, "definition": {"shape": "SchemaDefinition", "documentation": "<p>Specifies the definition of the schema to be stored. The schema definition must be written in Cedar schema JSON.</p>"}}}, "PutSchemaOutput": {"type": "structure", "required": ["policyStoreId", "namespaces", "createdDate", "lastUpdatedDate"], "members": {"policyStoreId": {"shape": "PolicyStoreId", "documentation": "<p>The unique ID of the policy store that contains the schema.</p>"}, "namespaces": {"shape": "NamespaceList", "documentation": "<p>Identifies the namespaces of the entities referenced by this schema.</p>"}, "createdDate": {"shape": "TimestampFormat", "documentation": "<p>The date and time that the schema was originally created.</p>"}, "lastUpdatedDate": {"shape": "TimestampFormat", "documentation": "<p>The date and time that the schema was last updated.</p>"}}}, "RecordAttribute": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "AttributeValue"}}, "ResourceArn": {"type": "string", "max": 2500, "min": 1, "pattern": "arn:[^:]*:[^:]*:[^:]*:[^:]*:.*"}, "ResourceConflict": {"type": "structure", "required": ["resourceId", "resourceType"], "members": {"resourceId": {"shape": "String", "documentation": "<p>The unique identifier of the resource involved in a conflict.</p>"}, "resourceType": {"shape": "ResourceType", "documentation": "<p>The type of the resource involved in a conflict.</p>"}}, "documentation": "<p>Contains information about a resource conflict.</p>"}, "ResourceConflictList": {"type": "list", "member": {"shape": "ResourceConflict"}}, "ResourceNotFoundException": {"type": "structure", "required": ["message", "resourceId", "resourceType"], "members": {"message": {"shape": "String"}, "resourceId": {"shape": "String", "documentation": "<p>The unique ID of the resource referenced in the failed request.</p>"}, "resourceType": {"shape": "ResourceType", "documentation": "<p>The resource type of the resource referenced in the failed request.</p>"}}, "documentation": "<p>The request failed because it references a resource that doesn't exist.</p>", "exception": true}, "ResourceType": {"type": "string", "enum": ["IDENTITY_SOURCE", "POLICY_STORE", "POLICY", "POLICY_TEMPLATE", "SCHEMA"]}, "SchemaDefinition": {"type": "structure", "members": {"cedarJson": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>A JSON string representation of the schema supported by applications that use this policy store. To delete the schema, run <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_PutSchema.html\">PutSchema</a> with <code>{}</code> for this parameter. For more information, see <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/userguide/schema.html\">Policy store schema</a> in the <i>Amazon Verified Permissions User Guide</i>.</p>"}}, "documentation": "<p>Contains a list of principal types, resource types, and actions that can be specified in policies stored in the same policy store. If the validation mode for the policy store is set to <code>STRICT</code>, then policies that can't be validated by this schema are rejected by Verified Permissions and can't be stored in the policy store.</p>", "union": true}, "SchemaJson": {"type": "string", "min": 1, "sensitive": true}, "ServiceQuotaExceededException": {"type": "structure", "required": ["message", "resourceType"], "members": {"message": {"shape": "String"}, "resourceId": {"shape": "String", "documentation": "<p>The unique ID of the resource referenced in the failed request.</p>"}, "resourceType": {"shape": "ResourceType", "documentation": "<p>The resource type of the resource referenced in the failed request.</p>"}, "serviceCode": {"shape": "String", "documentation": "<p>The code for the Amazon Web Services service that owns the quota.</p>"}, "quotaCode": {"shape": "String", "documentation": "<p>The quota code recognized by the Amazon Web Services Service Quotas service.</p>"}}, "documentation": "<p>The request failed because it would cause a service quota to be exceeded.</p>", "exception": true}, "SetAttribute": {"type": "list", "member": {"shape": "AttributeValue"}}, "StaticPolicyDefinition": {"type": "structure", "required": ["statement"], "members": {"description": {"shape": "StaticPolicyDescription", "documentation": "<p>The description of the static policy.</p>"}, "statement": {"shape": "PolicyStatement", "documentation": "<p>The policy content of the static policy, written in the Cedar policy language.</p>"}}, "documentation": "<p>Contains information about a static policy.</p> <p>This data type is used as a field that is part of the <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_PolicyDefinitionDetail.html\">PolicyDefinitionDetail</a> type.</p>"}, "StaticPolicyDefinitionDetail": {"type": "structure", "required": ["statement"], "members": {"description": {"shape": "StaticPolicyDescription", "documentation": "<p>A description of the static policy.</p>"}, "statement": {"shape": "PolicyStatement", "documentation": "<p>The content of the static policy written in the Cedar policy language.</p>"}}, "documentation": "<p>A structure that contains details about a static policy. It includes the description and policy body.</p> <p>This data type is used within a <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_PolicyDefinition.html\">PolicyDefinition</a> structure as part of a request parameter for the <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_CreatePolicy.html\">CreatePolicy</a> operation.</p>"}, "StaticPolicyDefinitionItem": {"type": "structure", "members": {"description": {"shape": "StaticPolicyDescription", "documentation": "<p>A description of the static policy.</p>"}}, "documentation": "<p>A structure that contains details about a static policy. It includes the description and policy statement.</p> <p>This data type is used within a <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_PolicyDefinition.html\">PolicyDefinition</a> structure as part of a request parameter for the <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_CreatePolicy.html\">CreatePolicy</a> operation.</p>"}, "StaticPolicyDescription": {"type": "string", "max": 150, "min": 0, "sensitive": true}, "String": {"type": "string"}, "StringAttribute": {"type": "string", "sensitive": true}, "TagKey": {"type": "string", "max": 128, "min": 1}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 200, "min": 1}, "TagMap": {"type": "map", "key": {"shape": "TagKey"}, "value": {"shape": "TagValue"}, "max": 200, "min": 0}, "TagResourceInput": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {"shape": "AmazonResourceName", "documentation": "<p>The ARN of the resource that you're adding tags to.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The list of key-value pairs to associate with the resource.</p>"}}}, "TagResourceOutput": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0}, "TemplateLinkedPolicyDefinition": {"type": "structure", "required": ["policyTemplateId"], "members": {"policyTemplateId": {"shape": "PolicyTemplateId", "documentation": "<p>The unique identifier of the policy template used to create this policy.</p>"}, "principal": {"shape": "EntityIdentifier", "documentation": "<p>The principal associated with this template-linked policy. Verified Permissions substitutes this principal for the <code>?principal</code> placeholder in the policy template when it evaluates an authorization request.</p>"}, "resource": {"shape": "EntityIdentifier", "documentation": "<p>The resource associated with this template-linked policy. Verified Permissions substitutes this resource for the <code>?resource</code> placeholder in the policy template when it evaluates an authorization request.</p>"}}, "documentation": "<p>Contains information about a policy created by instantiating a policy template.</p>"}, "TemplateLinkedPolicyDefinitionDetail": {"type": "structure", "required": ["policyTemplateId"], "members": {"policyTemplateId": {"shape": "PolicyTemplateId", "documentation": "<p>The unique identifier of the policy template used to create this policy.</p>"}, "principal": {"shape": "EntityIdentifier", "documentation": "<p>The principal associated with this template-linked policy. Verified Permissions substitutes this principal for the <code>?principal</code> placeholder in the policy template when it evaluates an authorization request.</p>"}, "resource": {"shape": "EntityIdentifier", "documentation": "<p>The resource associated with this template-linked policy. Verified Permissions substitutes this resource for the <code>?resource</code> placeholder in the policy template when it evaluates an authorization request.</p>"}}, "documentation": "<p>Contains information about a policy that was created by instantiating a policy template. </p>"}, "TemplateLinkedPolicyDefinitionItem": {"type": "structure", "required": ["policyTemplateId"], "members": {"policyTemplateId": {"shape": "PolicyTemplateId", "documentation": "<p>The unique identifier of the policy template used to create this policy.</p>"}, "principal": {"shape": "EntityIdentifier", "documentation": "<p>The principal associated with this template-linked policy. Verified Permissions substitutes this principal for the <code>?principal</code> placeholder in the policy template when it evaluates an authorization request.</p>"}, "resource": {"shape": "EntityIdentifier", "documentation": "<p>The resource associated with this template-linked policy. Verified Permissions substitutes this resource for the <code>?resource</code> placeholder in the policy template when it evaluates an authorization request.</p>"}}, "documentation": "<p>Contains information about a policy created by instantiating a policy template. </p> <p>This </p>"}, "ThrottlingException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}, "serviceCode": {"shape": "String", "documentation": "<p>The code for the Amazon Web Services service that owns the quota.</p>"}, "quotaCode": {"shape": "String", "documentation": "<p>The quota code recognized by the Amazon Web Services Service Quotas service.</p>"}}, "documentation": "<p>The request failed because it exceeded a throttling quota.</p>", "exception": true, "retryable": {"throttling": true}}, "TimestampFormat": {"type": "timestamp", "timestampFormat": "iso8601"}, "Token": {"type": "string", "max": 131072, "min": 1, "pattern": "[A-Za-z0-9-_=]+.[A-Za-z0-9-_=]+.[A-Za-z0-9-_=]+", "sensitive": true}, "TooManyTagsException": {"type": "structure", "members": {"message": {"shape": "String"}, "resourceName": {"shape": "AmazonResourceName"}}, "documentation": "<p>No more tags be added because the limit (50) has been reached. To add new tags, use <code>UntagResource</code> to remove existing tags.</p>", "exception": true}, "UntagResourceInput": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {"shape": "AmazonResourceName", "documentation": "<p>The ARN of the resource from which you are removing tags.</p>"}, "tagKeys": {"shape": "TagKeyList", "documentation": "<p>The list of tag keys to remove from the resource.</p>"}}}, "UntagResourceOutput": {"type": "structure", "members": {}}, "UpdateCognitoGroupConfiguration": {"type": "structure", "required": ["groupEntityType"], "members": {"groupEntityType": {"shape": "GroupEntityType", "documentation": "<p>The name of the schema entity type that's mapped to the user pool group. Defaults to <code>AWS::CognitoGroup</code>.</p>"}}, "documentation": "<p>The user group entities from an Amazon Cognito user pool identity source.</p>"}, "UpdateCognitoUserPoolConfiguration": {"type": "structure", "required": ["userPoolArn"], "members": {"userPoolArn": {"shape": "UserPoolArn", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of the Amazon Cognito user pool associated with this identity source.</p>"}, "clientIds": {"shape": "ClientIds", "documentation": "<p>The client ID of an app client that is configured for the specified Amazon Cognito user pool.</p>"}, "groupConfiguration": {"shape": "UpdateCognitoGroupConfiguration", "documentation": "<p>The configuration of the user groups from an Amazon Cognito user pool identity source.</p>"}}, "documentation": "<p>Contains configuration details of a Amazon Cognito user pool for use with an identity source.</p>"}, "UpdateConfiguration": {"type": "structure", "members": {"cognitoUserPoolConfiguration": {"shape": "UpdateCognitoUserPoolConfiguration", "documentation": "<p>Contains configuration details of a Amazon Cognito user pool.</p>"}, "openIdConnectConfiguration": {"shape": "UpdateOpenIdConnectConfiguration", "documentation": "<p>Contains configuration details of an OpenID Connect (OIDC) identity provider, or identity source, that Verified Permissions can use to generate entities from authenticated identities. It specifies the issuer URL, token type that you want to use, and policy store entity details.</p>"}}, "documentation": "<p>Contains an update to replace the configuration in an existing identity source.</p>", "union": true}, "UpdateIdentitySourceInput": {"type": "structure", "required": ["policyStoreId", "identitySourceId", "updateConfiguration"], "members": {"policyStoreId": {"shape": "PolicyStoreId", "documentation": "<p>Specifies the ID of the policy store that contains the identity source that you want to update.</p>"}, "identitySourceId": {"shape": "IdentitySourceId", "documentation": "<p>Specifies the ID of the identity source that you want to update.</p>"}, "updateConfiguration": {"shape": "UpdateConfiguration", "documentation": "<p>Specifies the details required to communicate with the identity provider (IdP) associated with this identity source.</p>"}, "principalEntityType": {"shape": "PrincipalEntityType", "documentation": "<p>Specifies the data type of principals generated for identities authenticated by the identity source.</p>"}}}, "UpdateIdentitySourceOutput": {"type": "structure", "required": ["createdDate", "identitySourceId", "lastUpdatedDate", "policyStoreId"], "members": {"createdDate": {"shape": "TimestampFormat", "documentation": "<p>The date and time that the updated identity source was originally created.</p>"}, "identitySourceId": {"shape": "IdentitySourceId", "documentation": "<p>The ID of the updated identity source.</p>"}, "lastUpdatedDate": {"shape": "TimestampFormat", "documentation": "<p>The date and time that the identity source was most recently updated.</p>"}, "policyStoreId": {"shape": "PolicyStoreId", "documentation": "<p>The ID of the policy store that contains the updated identity source.</p>"}}}, "UpdateOpenIdConnectAccessTokenConfiguration": {"type": "structure", "members": {"principalIdClaim": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>The claim that determines the principal in OIDC access tokens. For example, <code>sub</code>.</p>"}, "audiences": {"shape": "Audiences", "documentation": "<p>The access token <code>aud</code> claim values that you want to accept in your policy store. For example, <code>https://myapp.example.com, https://myapp2.example.com</code>.</p>"}}, "documentation": "<p>The configuration of an OpenID Connect (OIDC) identity source for handling access token claims. Contains the claim that you want to identify as the principal in an authorization request, and the values of the <code>aud</code> claim, or audiences, that you want to accept.</p> <p>This data type is part of a <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_UpdateOpenIdConnectTokenSelection.html\">UpdateOpenIdConnectTokenSelection</a> structure, which is a parameter to <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_UpdateIdentitySource.html\">UpdateIdentitySource</a>.</p>"}, "UpdateOpenIdConnectConfiguration": {"type": "structure", "required": ["issuer", "tokenSelection"], "members": {"issuer": {"shape": "Issuer", "documentation": "<p>The issuer URL of an OIDC identity provider. This URL must have an OIDC discovery endpoint at the path <code>.well-known/openid-configuration</code>.</p>"}, "entityIdPrefix": {"shape": "EntityIdPrefix", "documentation": "<p>A descriptive string that you want to prefix to user entities from your OIDC identity provider. For example, if you set an <code>entityIdPrefix</code> of <code>MyOIDCProvider</code>, you can reference principals in your policies in the format <code>MyCorp::User::MyOIDCProvider|Carlos</code>.</p>"}, "groupConfiguration": {"shape": "UpdateOpenIdConnectGroupConfiguration", "documentation": "<p>The claim in OIDC identity provider tokens that indicates a user's group membership, and the entity type that you want to map it to. For example, this object can map the contents of a <code>groups</code> claim to <code>MyCorp::UserGroup</code>.</p>"}, "tokenSelection": {"shape": "UpdateOpenIdConnectTokenSelection", "documentation": "<p>The token type that you want to process from your OIDC identity provider. Your policy store can process either identity (ID) or access tokens from a given OIDC identity source.</p>"}}, "documentation": "<p>Contains configuration details of an OpenID Connect (OIDC) identity provider, or identity source, that Verified Permissions can use to generate entities from authenticated identities. It specifies the issuer URL, token type that you want to use, and policy store entity details.</p> <p>This data type is part of a <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_UpdateConfiguration.html\">UpdateConfiguration</a> structure, which is a parameter to <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_UpdateIdentitySource.html\">UpdateIdentitySource</a>.</p>"}, "UpdateOpenIdConnectGroupConfiguration": {"type": "structure", "required": ["groupClaim", "groupEntityType"], "members": {"groupClaim": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>The token claim that you want Verified Permissions to interpret as group membership. For example, <code>groups</code>.</p>"}, "groupEntityType": {"shape": "GroupEntityType", "documentation": "<p>The policy store entity type that you want to map your users' group claim to. For example, <code>MyCorp::UserGroup</code>. A group entity type is an entity that can have a user entity type as a member.</p>"}}, "documentation": "<p>The claim in OIDC identity provider tokens that indicates a user's group membership, and the entity type that you want to map it to. For example, this object can map the contents of a <code>groups</code> claim to <code>MyCorp::UserGroup</code>.</p> <p>This data type is part of a <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_UpdateOpenIdConnectConfiguration.html\">UpdateOpenIdConnectConfiguration</a> structure, which is a parameter to <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_UpdateIdentitySource.html\">UpdateIdentitySource</a>.</p>"}, "UpdateOpenIdConnectIdentityTokenConfiguration": {"type": "structure", "members": {"principalIdClaim": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>The claim that determines the principal in OIDC access tokens. For example, <code>sub</code>.</p>"}, "clientIds": {"shape": "ClientIds", "documentation": "<p>The ID token audience, or client ID, claim values that you want to accept in your policy store from an OIDC identity provider. For example, <code>1example23456789, 2example10111213</code>.</p>"}}, "documentation": "<p>The configuration of an OpenID Connect (OIDC) identity source for handling identity (ID) token claims. Contains the claim that you want to identify as the principal in an authorization request, and the values of the <code>aud</code> claim, or audiences, that you want to accept.</p> <p>This data type is part of a <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_UpdateOpenIdConnectTokenSelection.html\">UpdateOpenIdConnectTokenSelection</a> structure, which is a parameter to <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_UpdateIdentitySource.html\">UpdateIdentitySource</a>.</p>"}, "UpdateOpenIdConnectTokenSelection": {"type": "structure", "members": {"accessTokenOnly": {"shape": "UpdateOpenIdConnectAccessTokenConfiguration", "documentation": "<p>The OIDC configuration for processing access tokens. Contains allowed audience claims, for example <code>https://auth.example.com</code>, and the claim that you want to map to the principal, for example <code>sub</code>.</p>"}, "identityTokenOnly": {"shape": "UpdateOpenIdConnectIdentityTokenConfiguration", "documentation": "<p>The OIDC configuration for processing identity (ID) tokens. Contains allowed client ID claims, for example <code>1example23456789</code>, and the claim that you want to map to the principal, for example <code>sub</code>.</p>"}}, "documentation": "<p>The token type that you want to process from your OIDC identity provider. Your policy store can process either identity (ID) or access tokens from a given OIDC identity source.</p> <p>This data type is part of a <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_UpdateOpenIdConnectConfiguration.html\">UpdateOpenIdConnectConfiguration</a> structure, which is a parameter to <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_UpdateIdentitySource.html\">UpdateIdentitySource</a>.</p>", "union": true}, "UpdatePolicyDefinition": {"type": "structure", "members": {"static": {"shape": "UpdateStaticPolicyDefinition", "documentation": "<p>Contains details about the updates to be applied to a static policy.</p>"}}, "documentation": "<p>Contains information about updates to be applied to a policy.</p> <p>This data type is used as a request parameter in the <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_UpdatePolicy.html\">UpdatePolicy</a> operation.</p>", "union": true}, "UpdatePolicyInput": {"type": "structure", "required": ["policyStoreId", "policyId", "definition"], "members": {"policyStoreId": {"shape": "PolicyStoreId", "documentation": "<p>Specifies the ID of the policy store that contains the policy that you want to update.</p>"}, "policyId": {"shape": "PolicyId", "documentation": "<p>Specifies the ID of the policy that you want to update. To find this value, you can use <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_ListPolicies.html\">ListPolicies</a>.</p>"}, "definition": {"shape": "UpdatePolicyDefinition", "documentation": "<p>Specifies the updated policy content that you want to replace on the specified policy. The content must be valid Cedar policy language text.</p> <p>You can change only the following elements from the policy definition:</p> <ul> <li> <p>The <code>action</code> referenced by the policy.</p> </li> <li> <p>Any conditional clauses, such as <code>when</code> or <code>unless</code> clauses.</p> </li> </ul> <p>You <b>can't</b> change the following elements:</p> <ul> <li> <p>Changing from <code>static</code> to <code>templateLinked</code>.</p> </li> <li> <p>Changing the effect of the policy from <code>permit</code> or <code>forbid</code>.</p> </li> <li> <p>The <code>principal</code> referenced by the policy.</p> </li> <li> <p>The <code>resource</code> referenced by the policy.</p> </li> </ul>"}}}, "UpdatePolicyOutput": {"type": "structure", "required": ["policyStoreId", "policyId", "policyType", "createdDate", "lastUpdatedDate"], "members": {"policyStoreId": {"shape": "PolicyStoreId", "documentation": "<p>The ID of the policy store that contains the policy that was updated.</p>"}, "policyId": {"shape": "PolicyId", "documentation": "<p>The ID of the policy that was updated.</p>"}, "policyType": {"shape": "PolicyType", "documentation": "<p>The type of the policy that was updated.</p>"}, "principal": {"shape": "EntityIdentifier", "documentation": "<p>The principal specified in the policy's scope. This element isn't included in the response when <code>Principal</code> isn't present in the policy content.</p>"}, "resource": {"shape": "EntityIdentifier", "documentation": "<p>The resource specified in the policy's scope. This element isn't included in the response when <code>Resource</code> isn't present in the policy content.</p>"}, "actions": {"shape": "ActionIdentifierList", "documentation": "<p>The action that a policy permits or forbids. For example, <code>{\"actions\": [{\"actionId\": \"ViewPhoto\", \"actionType\": \"PhotoFlash::Action\"}, {\"entityID\": \"SharePhoto\", \"entityType\": \"PhotoFlash::Action\"}]}</code>.</p>"}, "createdDate": {"shape": "TimestampFormat", "documentation": "<p>The date and time that the policy was originally created.</p>"}, "lastUpdatedDate": {"shape": "TimestampFormat", "documentation": "<p>The date and time that the policy was most recently updated.</p>"}, "effect": {"shape": "PolicyEffect", "documentation": "<p>The effect of the decision that a policy returns to an authorization request. For example, <code>\"effect\": \"Permit\"</code>.</p>"}}}, "UpdatePolicyStoreInput": {"type": "structure", "required": ["policyStoreId", "validationSettings"], "members": {"policyStoreId": {"shape": "PolicyStoreId", "documentation": "<p>Specifies the ID of the policy store that you want to update</p>"}, "validationSettings": {"shape": "ValidationSettings", "documentation": "<p>A structure that defines the validation settings that want to enable for the policy store.</p>"}, "deletionProtection": {"shape": "DeletionProtection", "documentation": "<p>Specifies whether the policy store can be deleted. If enabled, the policy store can't be deleted.</p> <p>When you call <code>UpdatePolicyStore</code>, this parameter is unchanged unless explicitly included in the call.</p>"}, "description": {"shape": "PolicyStoreDescription", "documentation": "<p>Descriptive text that you can provide to help with identification of the current policy store.</p>"}}}, "UpdatePolicyStoreOutput": {"type": "structure", "required": ["policyStoreId", "arn", "createdDate", "lastUpdatedDate"], "members": {"policyStoreId": {"shape": "PolicyStoreId", "documentation": "<p>The ID of the updated policy store.</p>"}, "arn": {"shape": "ResourceArn", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of the updated policy store.</p>"}, "createdDate": {"shape": "TimestampFormat", "documentation": "<p>The date and time that the policy store was originally created.</p>"}, "lastUpdatedDate": {"shape": "TimestampFormat", "documentation": "<p>The date and time that the policy store was most recently updated.</p>"}}}, "UpdatePolicyTemplateInput": {"type": "structure", "required": ["policyStoreId", "policyTemplateId", "statement"], "members": {"policyStoreId": {"shape": "PolicyStoreId", "documentation": "<p>Specifies the ID of the policy store that contains the policy template that you want to update.</p>"}, "policyTemplateId": {"shape": "PolicyTemplateId", "documentation": "<p>Specifies the ID of the policy template that you want to update.</p>"}, "description": {"shape": "PolicyTemplateDescription", "documentation": "<p>Specifies a new description to apply to the policy template.</p>"}, "statement": {"shape": "PolicyStatement", "documentation": "<p>Specifies new statement content written in Cedar policy language to replace the current body of the policy template.</p> <p>You can change only the following elements of the policy body:</p> <ul> <li> <p>The <code>action</code> referenced by the policy template.</p> </li> <li> <p>Any conditional clauses, such as <code>when</code> or <code>unless</code> clauses.</p> </li> </ul> <p>You <b>can't</b> change the following elements:</p> <ul> <li> <p>The effect (<code>permit</code> or <code>forbid</code>) of the policy template.</p> </li> <li> <p>The <code>principal</code> referenced by the policy template.</p> </li> <li> <p>The <code>resource</code> referenced by the policy template.</p> </li> </ul>"}}}, "UpdatePolicyTemplateOutput": {"type": "structure", "required": ["policyStoreId", "policyTemplateId", "createdDate", "lastUpdatedDate"], "members": {"policyStoreId": {"shape": "PolicyStoreId", "documentation": "<p>The ID of the policy store that contains the updated policy template.</p>"}, "policyTemplateId": {"shape": "PolicyTemplateId", "documentation": "<p>The ID of the updated policy template.</p>"}, "createdDate": {"shape": "TimestampFormat", "documentation": "<p>The date and time that the policy template was originally created.</p>"}, "lastUpdatedDate": {"shape": "TimestampFormat", "documentation": "<p>The date and time that the policy template was most recently updated.</p>"}}}, "UpdateStaticPolicyDefinition": {"type": "structure", "required": ["statement"], "members": {"description": {"shape": "StaticPolicyDescription", "documentation": "<p>Specifies the description to be added to or replaced on the static policy.</p>"}, "statement": {"shape": "PolicyStatement", "documentation": "<p>Specifies the Cedar policy language text to be added to or replaced on the static policy.</p> <important> <p>You can change only the following elements from the original content:</p> <ul> <li> <p>The <code>action</code> referenced by the policy.</p> </li> <li> <p>Any conditional clauses, such as <code>when</code> or <code>unless</code> clauses.</p> </li> </ul> <p>You <b>can't</b> change the following elements:</p> <ul> <li> <p>Changing from <code>StaticPolicy</code> to <code>TemplateLinkedPolicy</code>.</p> </li> <li> <p>The effect (<code>permit</code> or <code>forbid</code>) of the policy.</p> </li> <li> <p>The <code>principal</code> referenced by the policy.</p> </li> <li> <p>The <code>resource</code> referenced by the policy.</p> </li> </ul> </important>"}}, "documentation": "<p>Contains information about an update to a static policy.</p>"}, "UserPoolArn": {"type": "string", "max": 255, "min": 1, "pattern": "arn:[a-zA-Z0-9-]+:cognito-idp:(([a-zA-Z0-9-]+:\\d{12}:userpool/[\\w-]+_[0-9a-zA-Z]+))"}, "ValidationException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}, "fieldList": {"shape": "ValidationExceptionFieldList", "documentation": "<p>The list of fields that aren't valid.</p>"}}, "documentation": "<p>The request failed because one or more input parameters don't satisfy their constraint requirements. The output is provided as a list of fields and a reason for each field that isn't valid.</p> <p>The possible reasons include the following:</p> <ul> <li> <p> <b>UnrecognizedEntityType</b> </p> <p>The policy includes an entity type that isn't found in the schema.</p> </li> <li> <p> <b>UnrecognizedActionId</b> </p> <p>The policy includes an action id that isn't found in the schema.</p> </li> <li> <p> <b>InvalidActionApplication</b> </p> <p>The policy includes an action that, according to the schema, doesn't support the specified principal and resource.</p> </li> <li> <p> <b>UnexpectedType</b> </p> <p>The policy included an operand that isn't a valid type for the specified operation.</p> </li> <li> <p> <b>IncompatibleTypes</b> </p> <p>The types of elements included in a <code>set</code>, or the types of expressions used in an <code>if...then...else</code> clause aren't compatible in this context.</p> </li> <li> <p> <b>MissingAttribute</b> </p> <p>The policy attempts to access a record or entity attribute that isn't specified in the schema. Test for the existence of the attribute first before attempting to access its value. For more information, see the <a href=\"https://docs.cedarpolicy.com/policies/syntax-operators.html#has-presence-of-attribute-test\">has (presence of attribute test) operator</a> in the <i>Cedar Policy Language Guide</i>.</p> </li> <li> <p> <b>UnsafeOptionalAttributeAccess</b> </p> <p>The policy attempts to access a record or entity attribute that is optional and isn't guaranteed to be present. Test for the existence of the attribute first before attempting to access its value. For more information, see the <a href=\"https://docs.cedarpolicy.com/policies/syntax-operators.html#has-presence-of-attribute-test\">has (presence of attribute test) operator</a> in the <i>Cedar Policy Language Guide</i>.</p> </li> <li> <p> <b>ImpossiblePolicy</b> </p> <p>Cedar has determined that a policy condition always evaluates to false. If the policy is always false, it can never apply to any query, and so it can never affect an authorization decision.</p> </li> <li> <p> <b>WrongNumberArguments</b> </p> <p>The policy references an extension type with the wrong number of arguments.</p> </li> <li> <p> <b>FunctionArgumentValidationError</b> </p> <p>Cedar couldn't parse the argument passed to an extension type. For example, a string that is to be parsed as an IPv4 address can contain only digits and the period character.</p> </li> </ul>", "exception": true}, "ValidationExceptionField": {"type": "structure", "required": ["path", "message"], "members": {"path": {"shape": "String", "documentation": "<p>The path to the specific element that Verified Permissions found to be not valid.</p>"}, "message": {"shape": "String", "documentation": "<p>Describes the policy validation error.</p>"}}, "documentation": "<p>Details about a field that failed policy validation.</p>"}, "ValidationExceptionFieldList": {"type": "list", "member": {"shape": "ValidationExceptionField"}}, "ValidationMode": {"type": "string", "enum": ["OFF", "STRICT"]}, "ValidationSettings": {"type": "structure", "required": ["mode"], "members": {"mode": {"shape": "ValidationMode", "documentation": "<p>The validation mode currently configured for this policy store. The valid values are:</p> <ul> <li> <p> <b>OFF</b> – Neither Verified Permissions nor Cedar perform any validation on policies. No validation errors are reported by either service.</p> </li> <li> <p> <b>STRICT</b> – Requires a schema to be present in the policy store. <PERSON> performs validation on all submitted new or updated static policies and policy templates. Any that fail validation are rejected and Cedar doesn't store them in the policy store.</p> </li> </ul> <important> <p>If <code>Mode=STRICT</code> and the policy store doesn't contain a schema, Verified Permissions rejects all static policies and policy templates because there is no schema to validate against. </p> <p>To submit a static policy or policy template without a schema, you must turn off validation.</p> </important>"}}, "documentation": "<p>A structure that contains Cedar policy validation settings for the policy store. The validation mode determines which validation failures that <PERSON> considers serious enough to block acceptance of a new or edited static policy or policy template. </p> <p>This data type is used as a request parameter in the <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_CreatePolicyStore.html\">CreatePolicyStore</a> and <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/apireference/API_UpdatePolicyStore.html\">UpdatePolicyStore</a> operations.</p>"}}, "documentation": "<p>Amazon Verified Permissions is a permissions management service from Amazon Web Services. You can use Verified Permissions to manage permissions for your application, and authorize user access based on those permissions. Using Verified Permissions, application developers can grant access based on information about the users, resources, and requested actions. You can also evaluate additional information like group membership, attributes of the resources, and session context, such as time of request and IP addresses. Verified Permissions manages these permissions by letting you create and store authorization policies for your applications, such as consumer-facing web sites and enterprise business systems.</p> <p>Verified Permissions uses Cedar as the policy language to express your permission requirements. Cedar supports both role-based access control (RBAC) and attribute-based access control (ABAC) authorization models.</p> <p>For more information about configuring, administering, and using Amazon Verified Permissions in your applications, see the <a href=\"https://docs.aws.amazon.com/verifiedpermissions/latest/userguide/\">Amazon Verified Permissions User Guide</a>.</p> <p>For more information about the Cedar policy language, see the <a href=\"https://docs.cedarpolicy.com/\">Cedar Policy Language Guide</a>.</p> <important> <p>When you write Cedar policies that reference principals, resources and actions, you can define the unique identifiers used for each of those elements. We strongly recommend that you follow these best practices:</p> <ul> <li> <p> <b>Use values like universally unique identifiers (UUIDs) for all principal and resource identifiers.</b> </p> <p>For example, if user <code>jane</code> leaves the company, and you later let someone else use the name <code>jane</code>, then that new user automatically gets access to everything granted by policies that still reference <code>User::\"jane\"</code>. Cedar can’t distinguish between the new user and the old. This applies to both principal and resource identifiers. Always use identifiers that are guaranteed unique and never reused to ensure that you don’t unintentionally grant access because of the presence of an old identifier in a policy.</p> <p>Where you use a UUID for an entity, we recommend that you follow it with the // comment specifier and the ‘friendly’ name of your entity. This helps to make your policies easier to understand. For example: principal == User::\"a1b2c3d4-e5f6-a1b2-c3d4-EXAMPLE11111\", // alice</p> </li> <li> <p> <b>Do not include personally identifying, confidential, or sensitive information as part of the unique identifier for your principals or resources.</b> These identifiers are included in log entries shared in CloudTrail trails.</p> </li> </ul> </important> <p>Several operations return structures that appear similar, but have different purposes. As new functionality is added to the product, the structure used in a parameter of one operation might need to change in a way that wouldn't make sense for the same parameter in a different operation. To help you understand the purpose of each, the following naming convention is used for the structures:</p> <ul> <li> <p>Parameter type structures that end in <code>Detail</code> are used in <code>Get</code> operations.</p> </li> <li> <p>Parameter type structures that end in <code>Item</code> are used in <code>List</code> operations.</p> </li> <li> <p>Parameter type structures that use neither suffix are used in the mutating (create and update) operations.</p> </li> </ul>"}