{"version": "1.0", "resources": {"PhoneNumbersOptedOut": {"operation": "ListPhoneNumbersOptedOut", "resourceIdentifier": {"phoneNumber": "phoneNumbers[]"}}, "PlatformApplication": {"operation": "ListPlatformApplications", "resourceIdentifier": {"PlatformApplicationArn": "PlatformApplications[].PlatformApplicationArn", "Attributes": "PlatformApplications[].Attributes"}}, "Subscription": {"operation": "ListSubscriptions", "resourceIdentifier": {"SubscriptionArn": "Subscriptions[].SubscriptionArn", "Protocol": "Subscriptions[].Protocol", "Endpoint": "Subscriptions[].Endpoint"}}, "Topic": {"operation": "ListTopics", "resourceIdentifier": {"TopicArn": "Topics[].TopicArn"}}}, "operations": {"AddPermission": {"TopicArn": {"completions": [{"parameters": {}, "resourceName": "Topic", "resourceIdentifier": "TopicArn"}]}}, "CheckIfPhoneNumberIsOptedOut": {"phoneNumber": {"completions": [{"parameters": {}, "resourceName": "PhoneNumbersOptedOut", "resourceIdentifier": "phoneNumber"}]}}, "ConfirmSubscription": {"TopicArn": {"completions": [{"parameters": {}, "resourceName": "Topic", "resourceIdentifier": "TopicArn"}]}}, "DeletePlatformApplication": {"PlatformApplicationArn": {"completions": [{"parameters": {}, "resourceName": "PlatformApplication", "resourceIdentifier": "PlatformApplicationArn"}]}}, "DeleteTopic": {"TopicArn": {"completions": [{"parameters": {}, "resourceName": "Topic", "resourceIdentifier": "TopicArn"}]}}, "GetPlatformApplicationAttributes": {"PlatformApplicationArn": {"completions": [{"parameters": {}, "resourceName": "PlatformApplication", "resourceIdentifier": "PlatformApplicationArn"}]}}, "GetSubscriptionAttributes": {"SubscriptionArn": {"completions": [{"parameters": {}, "resourceName": "Subscription", "resourceIdentifier": "SubscriptionArn"}]}}, "GetTopicAttributes": {"TopicArn": {"completions": [{"parameters": {}, "resourceName": "Topic", "resourceIdentifier": "TopicArn"}]}}, "ListEndpointsByPlatformApplication": {"PlatformApplicationArn": {"completions": [{"parameters": {}, "resourceName": "PlatformApplication", "resourceIdentifier": "PlatformApplicationArn"}]}}, "ListSubscriptionsByTopic": {"TopicArn": {"completions": [{"parameters": {}, "resourceName": "Topic", "resourceIdentifier": "TopicArn"}]}}, "OptInPhoneNumber": {"phoneNumber": {"completions": [{"parameters": {}, "resourceName": "PhoneNumbersOptedOut", "resourceIdentifier": "phoneNumber"}]}}, "Publish": {"TopicArn": {"completions": [{"parameters": {}, "resourceName": "Topic", "resourceIdentifier": "TopicArn"}]}, "PhoneNumber": {"completions": [{"parameters": {}, "resourceName": "PhoneNumbersOptedOut", "resourceIdentifier": "phoneNumber"}]}}, "RemovePermission": {"TopicArn": {"completions": [{"parameters": {}, "resourceName": "Topic", "resourceIdentifier": "TopicArn"}]}}, "SetEndpointAttributes": {"Attributes": {"completions": [{"parameters": {}, "resourceName": "PlatformApplication", "resourceIdentifier": "Attributes"}]}}, "SetPlatformApplicationAttributes": {"PlatformApplicationArn": {"completions": [{"parameters": {}, "resourceName": "PlatformApplication", "resourceIdentifier": "PlatformApplicationArn"}]}, "Attributes": {"completions": [{"parameters": {}, "resourceName": "PlatformApplication", "resourceIdentifier": "Attributes"}]}}, "SetSubscriptionAttributes": {"SubscriptionArn": {"completions": [{"parameters": {}, "resourceName": "Subscription", "resourceIdentifier": "SubscriptionArn"}]}}, "SetTopicAttributes": {"TopicArn": {"completions": [{"parameters": {}, "resourceName": "Topic", "resourceIdentifier": "TopicArn"}]}}, "Subscribe": {"TopicArn": {"completions": [{"parameters": {}, "resourceName": "Topic", "resourceIdentifier": "TopicArn"}]}, "Protocol": {"completions": [{"parameters": {}, "resourceName": "Subscription", "resourceIdentifier": "Protocol"}]}, "Endpoint": {"completions": [{"parameters": {}, "resourceName": "Subscription", "resourceIdentifier": "Endpoint"}]}, "Attributes": {"completions": [{"parameters": {}, "resourceName": "PlatformApplication", "resourceIdentifier": "Attributes"}]}}, "Unsubscribe": {"SubscriptionArn": {"completions": [{"parameters": {}, "resourceName": "Subscription", "resourceIdentifier": "SubscriptionArn"}]}}}}