{"version": "2.0", "metadata": {"apiVersion": "2020-06-08", "endpointPrefix": "inspector2", "jsonVersion": "1.1", "protocol": "rest-json", "protocols": ["rest-json"], "serviceAbbreviation": "Inspector2", "serviceFullName": "Inspector2", "serviceId": "Inspector2", "signatureVersion": "v4", "signingName": "inspector2", "uid": "inspector2-2020-06-08", "auth": ["aws.auth#sigv4"]}, "operations": {"AssociateMember": {"name": "AssociateMember", "http": {"method": "POST", "requestUri": "/members/associate", "responseCode": 200}, "input": {"shape": "AssociateMemberRequest"}, "output": {"shape": "AssociateMemberResponse"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Associates an Amazon Web Services account with an Amazon Inspector delegated administrator. An HTTP 200 response indicates the association was successfully started, but doesn’t indicate whether it was completed. You can check if the association completed by using <a href=\"https://docs.aws.amazon.com/inspector/v2/APIReference/API_ListMembers.html\">ListMembers</a> for multiple accounts or <a href=\"https://docs.aws.amazon.com/inspector/v2/APIReference/API_GetMember.html\">GetMembers</a> for a single account.</p>"}, "BatchAssociateCodeSecurityScanConfiguration": {"name": "BatchAssociateCodeSecurityScanConfiguration", "http": {"method": "POST", "requestUri": "/codesecurity/scan-configuration/batch/associate", "responseCode": 200}, "input": {"shape": "BatchAssociateCodeSecurityScanConfigurationRequest"}, "output": {"shape": "BatchAssociateCodeSecurityScanConfigurationResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Associates multiple code repositories with an Amazon Inspector code security scan configuration.</p>"}, "BatchDisassociateCodeSecurityScanConfiguration": {"name": "BatchDisassociateCodeSecurityScanConfiguration", "http": {"method": "POST", "requestUri": "/codesecurity/scan-configuration/batch/disassociate", "responseCode": 200}, "input": {"shape": "BatchDisassociateCodeSecurityScanConfigurationRequest"}, "output": {"shape": "BatchDisassociateCodeSecurityScanConfigurationResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Disassociates multiple code repositories from an Amazon Inspector code security scan configuration.</p>"}, "BatchGetAccountStatus": {"name": "BatchGetAccountStatus", "http": {"method": "POST", "requestUri": "/status/batch/get", "responseCode": 200}, "input": {"shape": "BatchGetAccountStatusRequest"}, "output": {"shape": "BatchGetAccountStatusResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Retrieves the Amazon Inspector status of multiple Amazon Web Services accounts within your environment.</p>"}, "BatchGetCodeSnippet": {"name": "BatchGetCodeSnippet", "http": {"method": "POST", "requestUri": "/codesnippet/batchget", "responseCode": 200}, "input": {"shape": "BatchGetCodeSnippetRequest"}, "output": {"shape": "BatchGetCodeSnippetResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Retrieves code snippets from findings that Amazon Inspector detected code vulnerabilities in.</p>"}, "BatchGetFindingDetails": {"name": "BatchGetFindingDetails", "http": {"method": "POST", "requestUri": "/findings/details/batch/get", "responseCode": 200}, "input": {"shape": "BatchGetFindingDetailsRequest"}, "output": {"shape": "BatchGetFindingDetailsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets vulnerability details for findings.</p>"}, "BatchGetFreeTrialInfo": {"name": "BatchGetFreeTrialInfo", "http": {"method": "POST", "requestUri": "/freetrialinfo/batchget", "responseCode": 200}, "input": {"shape": "BatchGetFreeTrialInfoRequest"}, "output": {"shape": "BatchGetFreeTrialInfoResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets free trial status for multiple Amazon Web Services accounts.</p>"}, "BatchGetMemberEc2DeepInspectionStatus": {"name": "BatchGetMemberEc2DeepInspectionStatus", "http": {"method": "POST", "requestUri": "/ec2deepinspectionstatus/member/batch/get", "responseCode": 200}, "input": {"shape": "BatchGetMemberEc2DeepInspectionStatusRequest"}, "output": {"shape": "BatchGetMemberEc2DeepInspectionStatusResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Retrieves Amazon Inspector deep inspection activation status of multiple member accounts within your organization. You must be the delegated administrator of an organization in Amazon Inspector to use this API.</p>"}, "BatchUpdateMemberEc2DeepInspectionStatus": {"name": "BatchUpdateMemberEc2DeepInspectionStatus", "http": {"method": "POST", "requestUri": "/ec2deepinspectionstatus/member/batch/update", "responseCode": 200}, "input": {"shape": "BatchUpdateMemberEc2DeepInspectionStatusRequest"}, "output": {"shape": "BatchUpdateMemberEc2DeepInspectionStatusResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Activates or deactivates Amazon Inspector deep inspection for the provided member accounts in your organization. You must be the delegated administrator of an organization in Amazon Inspector to use this API.</p>"}, "CancelFindingsReport": {"name": "CancelFindingsReport", "http": {"method": "POST", "requestUri": "/reporting/cancel", "responseCode": 200}, "input": {"shape": "CancelFindingsReportRequest"}, "output": {"shape": "CancelFindingsReportResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Cancels the given findings report.</p>"}, "CancelSbomExport": {"name": "CancelSbomExport", "http": {"method": "POST", "requestUri": "/sbomexport/cancel", "responseCode": 200}, "input": {"shape": "CancelSbomExportRequest"}, "output": {"shape": "CancelSbomExportResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Cancels a software bill of materials (SBOM) report.</p>", "idempotent": true}, "CreateCisScanConfiguration": {"name": "CreateCisScanConfiguration", "http": {"method": "POST", "requestUri": "/cis/scan-configuration/create", "responseCode": 200}, "input": {"shape": "CreateCisScanConfigurationRequest"}, "output": {"shape": "CreateCisScanConfigurationResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates a CIS scan configuration.</p>"}, "CreateCodeSecurityIntegration": {"name": "CreateCodeSecurityIntegration", "http": {"method": "POST", "requestUri": "/codesecurity/integration/create", "responseCode": 200}, "input": {"shape": "CreateCodeSecurityIntegrationRequest"}, "output": {"shape": "CreateCodeSecurityIntegrationResponse"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates a code security integration with a source code repository provider.</p> <p>After calling the <code>CreateCodeSecurityIntegration</code> operation, you complete authentication and authorization with your provider. Next you call the <code>UpdateCodeSecurityIntegration</code> operation to provide the <code>details</code> to complete the integration setup</p>"}, "CreateCodeSecurityScanConfiguration": {"name": "CreateCodeSecurityScanConfiguration", "http": {"method": "POST", "requestUri": "/codesecurity/scan-configuration/create", "responseCode": 200}, "input": {"shape": "CreateCodeSecurityScanConfigurationRequest"}, "output": {"shape": "CreateCodeSecurityScanConfigurationResponse"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates a scan configuration for code security scanning.</p>"}, "CreateFilter": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "http": {"method": "POST", "requestUri": "/filters/create", "responseCode": 200}, "input": {"shape": "CreateFilterRequest"}, "output": {"shape": "CreateFilterResponse"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "BadRequestException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates a filter resource using specified filter criteria. When the filter action is set to <code>SUPPRESS</code> this action creates a suppression rule.</p>"}, "CreateFindingsReport": {"name": "CreateFindingsReport", "http": {"method": "POST", "requestUri": "/reporting/create", "responseCode": 200}, "input": {"shape": "CreateFindingsReportRequest"}, "output": {"shape": "CreateFindingsReportResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates a finding report. By default only <code>ACTIVE</code> findings are returned in the report. To see <code>SUPRESSED</code> or <code>CLOSED</code> findings you must specify a value for the <code>findingStatus</code> filter criteria. </p>"}, "CreateSbomExport": {"name": "CreateSbomExport", "http": {"method": "POST", "requestUri": "/sbomexport/create", "responseCode": 200}, "input": {"shape": "CreateSbomExportRequest"}, "output": {"shape": "CreateSbomExportResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates a software bill of materials (SBOM) report.</p>", "idempotent": true}, "DeleteCisScanConfiguration": {"name": "DeleteCisScanConfiguration", "http": {"method": "POST", "requestUri": "/cis/scan-configuration/delete", "responseCode": 200}, "input": {"shape": "DeleteCisScanConfigurationRequest"}, "output": {"shape": "DeleteCisScanConfigurationResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes a CIS scan configuration.</p>"}, "DeleteCodeSecurityIntegration": {"name": "DeleteCodeSecurityIntegration", "http": {"method": "POST", "requestUri": "/codesecurity/integration/delete", "responseCode": 200}, "input": {"shape": "DeleteCodeSecurityIntegrationRequest"}, "output": {"shape": "DeleteCodeSecurityIntegrationResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes a code security integration.</p>"}, "DeleteCodeSecurityScanConfiguration": {"name": "DeleteCodeSecurityScanConfiguration", "http": {"method": "POST", "requestUri": "/codesecurity/scan-configuration/delete", "responseCode": 200}, "input": {"shape": "DeleteCodeSecurityScanConfigurationRequest"}, "output": {"shape": "DeleteCodeSecurityScanConfigurationResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes a code security scan configuration.</p>"}, "DeleteFilter": {"name": "DeleteFilter", "http": {"method": "POST", "requestUri": "/filters/delete", "responseCode": 200}, "input": {"shape": "DeleteFilterRequest"}, "output": {"shape": "DeleteFilterResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes a filter resource.</p>"}, "DescribeOrganizationConfiguration": {"name": "DescribeOrganizationConfiguration", "http": {"method": "POST", "requestUri": "/organizationconfiguration/describe", "responseCode": 200}, "input": {"shape": "DescribeOrganizationConfigurationRequest"}, "output": {"shape": "DescribeOrganizationConfigurationResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Describe Amazon Inspector configuration settings for an Amazon Web Services organization.</p>"}, "Disable": {"name": "Disable", "http": {"method": "POST", "requestUri": "/disable", "responseCode": 200}, "input": {"shape": "DisableRequest"}, "output": {"shape": "DisableResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Disables Amazon Inspector scans for one or more Amazon Web Services accounts. Disabling all scan types in an account disables the Amazon Inspector service.</p>"}, "DisableDelegatedAdminAccount": {"name": "DisableDelegatedAdminAccount", "http": {"method": "POST", "requestUri": "/delegatedadminaccounts/disable", "responseCode": 200}, "input": {"shape": "DisableDelegatedAdminAccountRequest"}, "output": {"shape": "DisableDelegatedAdminAccountResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Disables the Amazon Inspector delegated administrator for your organization.</p>"}, "DisassociateMember": {"name": "DisassociateMember", "http": {"method": "POST", "requestUri": "/members/disassociate", "responseCode": 200}, "input": {"shape": "DisassociateMemberRequest"}, "output": {"shape": "DisassociateMemberResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Disassociates a member account from an Amazon Inspector delegated administrator.</p>"}, "Enable": {"name": "Enable", "http": {"method": "POST", "requestUri": "/enable", "responseCode": 200}, "input": {"shape": "EnableRequest"}, "output": {"shape": "EnableResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Enables Amazon Inspector scans for one or more Amazon Web Services accounts.</p>"}, "EnableDelegatedAdminAccount": {"name": "EnableDelegatedAdminAccount", "http": {"method": "POST", "requestUri": "/delegatedadminaccounts/enable", "responseCode": 200}, "input": {"shape": "EnableDelegatedAdminAccountRequest"}, "output": {"shape": "EnableDelegatedAdminAccountResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Enables the Amazon Inspector delegated administrator for your Organizations organization.</p>"}, "GetCisScanReport": {"name": "GetCisScanReport", "http": {"method": "POST", "requestUri": "/cis/scan/report/get", "responseCode": 200}, "input": {"shape": "GetCisScanReportRequest"}, "output": {"shape": "GetCisScanReportResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Retrieves a CIS scan report.</p>"}, "GetCisScanResultDetails": {"name": "GetCisScanResultDetails", "http": {"method": "POST", "requestUri": "/cis/scan-result/details/get", "responseCode": 200}, "input": {"shape": "GetCisScanResultDetailsRequest"}, "output": {"shape": "GetCisScanResultDetailsResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Retrieves CIS scan result details.</p>"}, "GetClustersForImage": {"name": "GetClustersForImage", "http": {"method": "POST", "requestUri": "/cluster/get", "responseCode": 200}, "input": {"shape": "GetClustersForImageRequest"}, "output": {"shape": "GetClustersForImageResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Returns a list of clusters and metadata associated with an image.</p>"}, "GetCodeSecurityIntegration": {"name": "GetCodeSecurityIntegration", "http": {"method": "POST", "requestUri": "/codesecurity/integration/get", "responseCode": 200}, "input": {"shape": "GetCodeSecurityIntegrationRequest"}, "output": {"shape": "GetCodeSecurityIntegrationResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Retrieves information about a code security integration.</p>"}, "GetCodeSecurityScan": {"name": "GetCodeSecurityScan", "http": {"method": "POST", "requestUri": "/codesecurity/scan/get", "responseCode": 200}, "input": {"shape": "GetCodeSecurityScanRequest"}, "output": {"shape": "GetCodeSecurityScanResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Retrieves information about a specific code security scan.</p>"}, "GetCodeSecurityScanConfiguration": {"name": "GetCodeSecurityScanConfiguration", "http": {"method": "POST", "requestUri": "/codesecurity/scan-configuration/get", "responseCode": 200}, "input": {"shape": "GetCodeSecurityScanConfigurationRequest"}, "output": {"shape": "GetCodeSecurityScanConfigurationResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Retrieves information about a code security scan configuration.</p>"}, "GetConfiguration": {"name": "GetConfiguration", "http": {"method": "POST", "requestUri": "/configuration/get", "responseCode": 200}, "input": {"shape": "GetConfigurationRequest"}, "output": {"shape": "GetConfigurationResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Retrieves setting configurations for Inspector scans.</p>"}, "GetDelegatedAdminAccount": {"name": "GetDelegatedAdminAccount", "http": {"method": "POST", "requestUri": "/delegatedadminaccounts/get", "responseCode": 200}, "input": {"shape": "GetDelegatedAdminAccountRequest"}, "output": {"shape": "GetDelegatedAdminAccountResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Retrieves information about the Amazon Inspector delegated administrator for your organization.</p>"}, "GetEc2DeepInspectionConfiguration": {"name": "GetEc2DeepInspectionConfiguration", "http": {"method": "POST", "requestUri": "/ec2deepinspectionconfiguration/get", "responseCode": 200}, "input": {"shape": "GetEc2DeepInspectionConfigurationRequest"}, "output": {"shape": "GetEc2DeepInspectionConfigurationResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Retrieves the activation status of Amazon Inspector deep inspection and custom paths associated with your account. </p>"}, "GetEncryptionKey": {"name": "GetEncryptionKey", "http": {"method": "GET", "requestUri": "/encryptionkey/get", "responseCode": 200}, "input": {"shape": "GetEncryptionKeyRequest"}, "output": {"shape": "GetEncryptionKeyResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets an encryption key.</p>"}, "GetFindingsReportStatus": {"name": "GetFindingsReportStatus", "http": {"method": "POST", "requestUri": "/reporting/status/get", "responseCode": 200}, "input": {"shape": "GetFindingsReportStatusRequest"}, "output": {"shape": "GetFindingsReportStatusResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets the status of a findings report.</p>"}, "GetMember": {"name": "GetMember", "http": {"method": "POST", "requestUri": "/members/get", "responseCode": 200}, "input": {"shape": "GetMemberRequest"}, "output": {"shape": "GetMemberResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets member information for your organization.</p>"}, "GetSbomExport": {"name": "GetSbomExport", "http": {"method": "POST", "requestUri": "/sbomexport/get", "responseCode": 200}, "input": {"shape": "GetSbomExportRequest"}, "output": {"shape": "GetSbomExportResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets details of a software bill of materials (SBOM) report.</p>", "idempotent": true}, "ListAccountPermissions": {"name": "ListAccountPermissions", "http": {"method": "POST", "requestUri": "/accountpermissions/list", "responseCode": 200}, "input": {"shape": "ListAccountPermissionsRequest"}, "output": {"shape": "ListAccountPermissionsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists the permissions an account has to configure Amazon Inspector.</p>"}, "ListCisScanConfigurations": {"name": "ListCisScanConfigurations", "http": {"method": "POST", "requestUri": "/cis/scan-configuration/list", "responseCode": 200}, "input": {"shape": "ListCisScanConfigurationsRequest"}, "output": {"shape": "ListCisScanConfigurationsResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists CIS scan configurations.</p>"}, "ListCisScanResultsAggregatedByChecks": {"name": "ListCisScanResultsAggregatedByChecks", "http": {"method": "POST", "requestUri": "/cis/scan-result/check/list", "responseCode": 200}, "input": {"shape": "ListCisScanResultsAggregatedByChecksRequest"}, "output": {"shape": "ListCisScanResultsAggregatedByChecksResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists scan results aggregated by checks.</p>"}, "ListCisScanResultsAggregatedByTargetResource": {"name": "ListCisScanResultsAggregatedByTargetResource", "http": {"method": "POST", "requestUri": "/cis/scan-result/resource/list", "responseCode": 200}, "input": {"shape": "ListCisScanResultsAggregatedByTargetResourceRequest"}, "output": {"shape": "ListCisScanResultsAggregatedByTargetResourceResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists scan results aggregated by a target resource.</p>"}, "ListCisScans": {"name": "ListCisScans", "http": {"method": "POST", "requestUri": "/cis/scan/list", "responseCode": 200}, "input": {"shape": "ListCisScansRequest"}, "output": {"shape": "ListCisScansResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Returns a CIS scan list.</p>"}, "ListCodeSecurityIntegrations": {"name": "ListCodeSecurityIntegrations", "http": {"method": "POST", "requestUri": "/codesecurity/integration/list", "responseCode": 200}, "input": {"shape": "ListCodeSecurityIntegrationsRequest"}, "output": {"shape": "ListCodeSecurityIntegrationsResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists all code security integrations in your account.</p>"}, "ListCodeSecurityScanConfigurationAssociations": {"name": "ListCodeSecurityScanConfigurationAssociations", "http": {"method": "POST", "requestUri": "/codesecurity/scan-configuration/associations/list", "responseCode": 200}, "input": {"shape": "ListCodeSecurityScanConfigurationAssociationsRequest"}, "output": {"shape": "ListCodeSecurityScanConfigurationAssociationsResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists the associations between code repositories and Amazon Inspector code security scan configurations.</p>"}, "ListCodeSecurityScanConfigurations": {"name": "ListCodeSecurityScanConfigurations", "http": {"method": "POST", "requestUri": "/codesecurity/scan-configuration/list", "responseCode": 200}, "input": {"shape": "ListCodeSecurityScanConfigurationsRequest"}, "output": {"shape": "ListCodeSecurityScanConfigurationsResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists all code security scan configurations in your account.</p>"}, "ListCoverage": {"name": "ListCoverage", "http": {"method": "POST", "requestUri": "/coverage/list", "responseCode": 200}, "input": {"shape": "ListCoverageRequest"}, "output": {"shape": "ListCoverageResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists coverage details for your environment.</p>"}, "ListCoverageStatistics": {"name": "ListCoverageStatistics", "http": {"method": "POST", "requestUri": "/coverage/statistics/list", "responseCode": 200}, "input": {"shape": "ListCoverageStatisticsRequest"}, "output": {"shape": "ListCoverageStatisticsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists Amazon Inspector coverage statistics for your environment.</p>"}, "ListDelegatedAdminAccounts": {"name": "ListDelegatedAdminAccounts", "http": {"method": "POST", "requestUri": "/delegatedadminaccounts/list", "responseCode": 200}, "input": {"shape": "ListDelegatedAdminAccountsRequest"}, "output": {"shape": "ListDelegatedAdminAccountsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists information about the Amazon Inspector delegated administrator of your organization.</p>"}, "ListFilters": {"name": "ListFilters", "http": {"method": "POST", "requestUri": "/filters/list", "responseCode": 200}, "input": {"shape": "ListFiltersRequest"}, "output": {"shape": "ListFiltersResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists the filters associated with your account.</p>"}, "ListFindingAggregations": {"name": "ListFindingAggregations", "http": {"method": "POST", "requestUri": "/findings/aggregation/list", "responseCode": 200}, "input": {"shape": "ListFindingAggregationsRequest"}, "output": {"shape": "ListFindingAggregationsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists aggregated finding data for your environment based on specific criteria.</p>"}, "ListFindings": {"name": "ListFindings", "http": {"method": "POST", "requestUri": "/findings/list", "responseCode": 200}, "input": {"shape": "ListFindingsRequest"}, "output": {"shape": "ListFindingsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists findings for your environment.</p>"}, "ListMembers": {"name": "ListMembers", "http": {"method": "POST", "requestUri": "/members/list", "responseCode": 200}, "input": {"shape": "ListMembersRequest"}, "output": {"shape": "ListMembersResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>List members associated with the Amazon Inspector delegated administrator for your organization.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists all tags attached to a given resource.</p>"}, "ListUsageTotals": {"name": "ListUsageTotals", "http": {"method": "POST", "requestUri": "/usage/list", "responseCode": 200}, "input": {"shape": "ListUsageTotalsRequest"}, "output": {"shape": "ListUsageTotalsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists the Amazon Inspector usage totals over the last 30 days.</p>"}, "ResetEncryptionKey": {"name": "ResetEncryptionKey", "http": {"method": "PUT", "requestUri": "/encryptionkey/reset", "responseCode": 200}, "input": {"shape": "ResetEncryptionKeyRequest"}, "output": {"shape": "ResetEncryptionKeyResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Resets an encryption key. After the key is reset your resources will be encrypted by an Amazon Web Services owned key.</p>", "idempotent": true}, "SearchVulnerabilities": {"name": "SearchVulnerabilities", "http": {"method": "POST", "requestUri": "/vulnerabilities/search", "responseCode": 200}, "input": {"shape": "SearchVulnerabilitiesRequest"}, "output": {"shape": "SearchVulnerabilitiesResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists Amazon Inspector coverage details for a specific vulnerability.</p>"}, "SendCisSessionHealth": {"name": "SendCisSessionHealth", "http": {"method": "PUT", "requestUri": "/cissession/health/send", "responseCode": 200}, "input": {"shape": "SendCisSessionHealthRequest"}, "output": {"shape": "SendCisSessionHealthResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p> Sends a CIS session health. This API is used by the Amazon Inspector SSM plugin to communicate with the Amazon Inspector service. The Amazon Inspector SSM plugin calls this API to start a CIS scan session for the scan ID supplied by the service. </p>", "idempotent": true}, "SendCisSessionTelemetry": {"name": "SendCisSessionTelemetry", "http": {"method": "PUT", "requestUri": "/cissession/telemetry/send", "responseCode": 200}, "input": {"shape": "SendCisSessionTelemetryRequest"}, "output": {"shape": "SendCisSessionTelemetryResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p> Sends a CIS session telemetry. This API is used by the Amazon Inspector SSM plugin to communicate with the Amazon Inspector service. The Amazon Inspector SSM plugin calls this API to start a CIS scan session for the scan ID supplied by the service. </p>", "idempotent": true}, "StartCisSession": {"name": "StartCisSession", "http": {"method": "PUT", "requestUri": "/cissession/start", "responseCode": 200}, "input": {"shape": "StartCisSessionRequest"}, "output": {"shape": "StartCisSessionResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p> Starts a CIS session. This API is used by the Amazon Inspector SSM plugin to communicate with the Amazon Inspector service. The Amazon Inspector SSM plugin calls this API to start a CIS scan session for the scan ID supplied by the service. </p>", "idempotent": true}, "StartCodeSecurityScan": {"name": "StartCodeSecurityScan", "http": {"method": "POST", "requestUri": "/codesecurity/scan/start", "responseCode": 200}, "input": {"shape": "StartCodeSecurityScanRequest"}, "output": {"shape": "StartCodeSecurityScanResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Initiates a code security scan on a specified repository.</p>"}, "StopCisSession": {"name": "StopCisSession", "http": {"method": "PUT", "requestUri": "/cissession/stop", "responseCode": 200}, "input": {"shape": "StopCisSessionRequest"}, "output": {"shape": "StopCisSessionResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p> Stops a CIS session. This API is used by the Amazon Inspector SSM plugin to communicate with the Amazon Inspector service. The Amazon Inspector SSM plugin calls this API to stop a CIS scan session for the scan ID supplied by the service. </p>", "idempotent": true}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Adds tags to a resource.</p>", "idempotent": true}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Removes tags from a resource.</p>"}, "UpdateCisScanConfiguration": {"name": "UpdateCisScanConfiguration", "http": {"method": "POST", "requestUri": "/cis/scan-configuration/update", "responseCode": 200}, "input": {"shape": "UpdateCisScanConfigurationRequest"}, "output": {"shape": "UpdateCisScanConfigurationResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Updates a CIS scan configuration.</p>"}, "UpdateCodeSecurityIntegration": {"name": "UpdateCodeSecurityIntegration", "http": {"method": "POST", "requestUri": "/codesecurity/integration/update", "responseCode": 200}, "input": {"shape": "UpdateCodeSecurityIntegrationRequest"}, "output": {"shape": "UpdateCodeSecurityIntegrationResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Updates an existing code security integration.</p> <p>After calling the <code>CreateCodeSecurityIntegration</code> operation, you complete authentication and authorization with your provider. Next you call the <code>UpdateCodeSecurityIntegration</code> operation to provide the <code>details</code> to complete the integration setup</p>"}, "UpdateCodeSecurityScanConfiguration": {"name": "UpdateCodeSecurityScanConfiguration", "http": {"method": "POST", "requestUri": "/codesecurity/scan-configuration/update", "responseCode": 200}, "input": {"shape": "UpdateCodeSecurityScanConfigurationRequest"}, "output": {"shape": "UpdateCodeSecurityScanConfigurationResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Updates an existing code security scan configuration.</p>"}, "UpdateConfiguration": {"name": "UpdateConfiguration", "http": {"method": "POST", "requestUri": "/configuration/update", "responseCode": 200}, "input": {"shape": "UpdateConfigurationRequest"}, "output": {"shape": "UpdateConfigurationResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Updates setting configurations for your Amazon Inspector account. When you use this API as an Amazon Inspector delegated administrator this updates the setting for all accounts you manage. Member accounts in an organization cannot update this setting.</p>"}, "UpdateEc2DeepInspectionConfiguration": {"name": "UpdateEc2DeepInspectionConfiguration", "http": {"method": "POST", "requestUri": "/ec2deepinspectionconfiguration/update", "responseCode": 200}, "input": {"shape": "UpdateEc2DeepInspectionConfigurationRequest"}, "output": {"shape": "UpdateEc2DeepInspectionConfigurationResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Activates, deactivates Amazon Inspector deep inspection, or updates custom paths for your account. </p>"}, "UpdateEncryptionKey": {"name": "UpdateEncryptionKey", "http": {"method": "PUT", "requestUri": "/encryptionkey/update", "responseCode": 200}, "input": {"shape": "UpdateEncryptionKeyRequest"}, "output": {"shape": "UpdateEncryptionKeyResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p>Updates an encryption key. A <code>ResourceNotFoundException</code> means that an Amazon Web Services owned key is being used for encryption.</p>", "idempotent": true}, "UpdateFilter": {"name": "UpdateFilter", "http": {"method": "POST", "requestUri": "/filters/update", "responseCode": 200}, "input": {"shape": "UpdateFilterRequest"}, "output": {"shape": "UpdateFilterResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Specifies the action that is to be applied to the findings that match the filter.</p>"}, "UpdateOrgEc2DeepInspectionConfiguration": {"name": "UpdateOrgEc2DeepInspectionConfiguration", "http": {"method": "POST", "requestUri": "/ec2deepinspectionconfiguration/org/update", "responseCode": 200}, "input": {"shape": "UpdateOrgEc2DeepInspectionConfigurationRequest"}, "output": {"shape": "UpdateOrgEc2DeepInspectionConfigurationResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Updates the Amazon Inspector deep inspection custom paths for your organization. You must be an Amazon Inspector delegated administrator to use this API.</p>"}, "UpdateOrganizationConfiguration": {"name": "UpdateOrganizationConfiguration", "http": {"method": "POST", "requestUri": "/organizationconfiguration/update", "responseCode": 200}, "input": {"shape": "UpdateOrganizationConfigurationRequest"}, "output": {"shape": "UpdateOrganizationConfigurationResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Updates the configurations for your Amazon Inspector organization.</p>"}}, "shapes": {"AccessDeniedException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>You do not have sufficient access to perform this action.</p> <p> For <code>Enable</code>, you receive this error if you attempt to use a feature in an unsupported Amazon Web Services Region. </p>", "error": {"httpStatusCode": 403, "senderFault": true}, "exception": true}, "Account": {"type": "structure", "required": ["accountId", "resourceStatus", "status"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The ID of the Amazon Web Services account.</p>"}, "resourceStatus": {"shape": "ResourceStatus", "documentation": "<p>Details of the status of Amazon Inspector scans by resource type.</p>"}, "status": {"shape": "Status", "documentation": "<p>The status of Amazon Inspector for the account.</p>"}}, "documentation": "<p>An Amazon Web Services account within your environment that Amazon Inspector has been enabled for.</p>"}, "AccountAggregation": {"type": "structure", "members": {"findingType": {"shape": "AggregationFindingType", "documentation": "<p>The type of finding.</p>"}, "resourceType": {"shape": "AggregationResourceType", "documentation": "<p>The type of resource.</p>"}, "sortBy": {"shape": "AccountSortBy", "documentation": "<p>The value to sort by.</p>"}, "sortOrder": {"shape": "SortOrder", "documentation": "<p>The sort order (ascending or descending).</p>"}}, "documentation": "<p>An object that contains details about an aggregation response based on Amazon Web Services accounts.</p>"}, "AccountAggregationResponse": {"type": "structure", "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID.</p>"}, "exploitAvailableCount": {"shape": "<PERSON>", "documentation": "<p> The number of findings that have an exploit available. </p>"}, "fixAvailableCount": {"shape": "<PERSON>", "documentation": "<p> Details about the number of fixes. </p>"}, "severityCounts": {"shape": "SeverityCounts", "documentation": "<p>The number of findings by severity.</p>"}}, "documentation": "<p>An aggregation of findings by Amazon Web Services account ID.</p>"}, "AccountId": {"type": "string", "max": 12, "min": 12, "pattern": "^\\d{12}$"}, "AccountIdFilterList": {"type": "list", "member": {"shape": "CisStringFilter"}, "max": 10, "min": 1}, "AccountIdSet": {"type": "list", "member": {"shape": "AccountId"}, "max": 100, "min": 0}, "AccountList": {"type": "list", "member": {"shape": "Account"}}, "AccountSortBy": {"type": "string", "enum": ["CRITICAL", "HIGH", "ALL"]}, "AccountState": {"type": "structure", "required": ["accountId", "resourceState", "state"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID.</p>"}, "resourceState": {"shape": "ResourceState", "documentation": "<p>An object detailing which resources Amazon Inspector is enabled to scan for the account.</p>"}, "state": {"shape": "State", "documentation": "<p>An object detailing the status of Amazon Inspector for the account.</p>"}}, "documentation": "<p>An object with details the status of an Amazon Web Services account within your Amazon Inspector environment.</p>"}, "AccountStateList": {"type": "list", "member": {"shape": "AccountState"}, "max": 100, "min": 0}, "AggCounts": {"type": "long"}, "AggregationFindingType": {"type": "string", "enum": ["NETWORK_REACHABILITY", "PACKAGE_VULNERABILITY", "CODE_VULNERABILITY"]}, "AggregationRequest": {"type": "structure", "members": {"accountAggregation": {"shape": "AccountAggregation", "documentation": "<p>An object that contains details about an aggregation request based on Amazon Web Services account IDs.</p>"}, "amiAggregation": {"shape": "AmiAggregation", "documentation": "<p>An object that contains details about an aggregation request based on Amazon Machine Images (AMIs).</p>"}, "awsEcrContainerAggregation": {"shape": "AwsEcrContainerAggregation", "documentation": "<p>An object that contains details about an aggregation request based on Amazon ECR container images.</p>"}, "codeRepositoryAggregation": {"shape": "CodeRepositoryAggregation", "documentation": "<p>An object that contains details about an aggregation request based on code repositories.</p>"}, "ec2InstanceAggregation": {"shape": "Ec2InstanceAggregation", "documentation": "<p>An object that contains details about an aggregation request based on Amazon EC2 instances.</p>"}, "findingTypeAggregation": {"shape": "FindingTypeAggregation", "documentation": "<p>An object that contains details about an aggregation request based on finding types.</p>"}, "imageLayerAggregation": {"shape": "ImageLayerAggregation", "documentation": "<p>An object that contains details about an aggregation request based on container image layers.</p>"}, "lambdaFunctionAggregation": {"shape": "LambdaFunctionAggregation", "documentation": "<p>Returns an object with findings aggregated by Amazon Web Services Lambda function.</p>"}, "lambdaLayerAggregation": {"shape": "LambdaLayerAggregation", "documentation": "<p>Returns an object with findings aggregated by Amazon Web Services Lambda layer.</p>"}, "packageAggregation": {"shape": "PackageAggregation", "documentation": "<p>An object that contains details about an aggregation request based on operating system package type.</p>"}, "repositoryAggregation": {"shape": "RepositoryAggregation", "documentation": "<p>An object that contains details about an aggregation request based on Amazon ECR repositories.</p>"}, "titleAggregation": {"shape": "TitleAggregation", "documentation": "<p>An object that contains details about an aggregation request based on finding title.</p>"}}, "documentation": "<p>Contains details about an aggregation request.</p>", "union": true}, "AggregationResourceType": {"type": "string", "enum": ["AWS_EC2_INSTANCE", "AWS_ECR_CONTAINER_IMAGE", "AWS_LAMBDA_FUNCTION"]}, "AggregationResponse": {"type": "structure", "members": {"accountAggregation": {"shape": "AccountAggregationResponse", "documentation": "<p>An object that contains details about an aggregation response based on Amazon Web Services account IDs.</p>"}, "amiAggregation": {"shape": "AmiAggregationResponse", "documentation": "<p>An object that contains details about an aggregation response based on Amazon Machine Images (AMIs).</p>"}, "awsEcrContainerAggregation": {"shape": "AwsEcrContainerAggregationResponse", "documentation": "<p>An object that contains details about an aggregation response based on Amazon ECR container images.</p>"}, "codeRepositoryAggregation": {"shape": "CodeRepositoryAggregationResponse", "documentation": "<p>An object that contains details about an aggregation response based on code repositories.</p>"}, "ec2InstanceAggregation": {"shape": "Ec2InstanceAggregationResponse", "documentation": "<p>An object that contains details about an aggregation response based on Amazon EC2 instances.</p>"}, "findingTypeAggregation": {"shape": "FindingTypeAggregationResponse", "documentation": "<p>An object that contains details about an aggregation response based on finding types.</p>"}, "imageLayerAggregation": {"shape": "ImageLayerAggregationResponse", "documentation": "<p>An object that contains details about an aggregation response based on container image layers.</p>"}, "lambdaFunctionAggregation": {"shape": "LambdaFunctionAggregationResponse", "documentation": "<p>An aggregation of findings by Amazon Web Services Lambda function.</p>"}, "lambdaLayerAggregation": {"shape": "LambdaLayerAggregationResponse", "documentation": "<p>An aggregation of findings by Amazon Web Services Lambda layer.</p>"}, "packageAggregation": {"shape": "PackageAggregationResponse", "documentation": "<p>An object that contains details about an aggregation response based on operating system package type.</p>"}, "repositoryAggregation": {"shape": "RepositoryAggregationResponse", "documentation": "<p>An object that contains details about an aggregation response based on Amazon ECR repositories.</p>"}, "titleAggregation": {"shape": "TitleAggregationResponse", "documentation": "<p>An object that contains details about an aggregation response based on finding title.</p>"}}, "documentation": "<p>A structure that contains details about the results of an aggregation type.</p>", "union": true}, "AggregationResponseList": {"type": "list", "member": {"shape": "AggregationResponse"}}, "AggregationType": {"type": "string", "enum": ["FINDING_TYPE", "PACKAGE", "TITLE", "REPOSITORY", "AMI", "AWS_EC2_INSTANCE", "AWS_ECR_CONTAINER", "IMAGE_LAYER", "ACCOUNT", "AWS_LAMBDA_FUNCTION", "LAMBDA_LAYER", "CODE_REPOSITORY"]}, "AmiAggregation": {"type": "structure", "members": {"amis": {"shape": "StringFilterList", "documentation": "<p>The IDs of AMIs to aggregate findings for.</p>"}, "sortBy": {"shape": "AmiSortBy", "documentation": "<p>The value to sort results by.</p>"}, "sortOrder": {"shape": "SortOrder", "documentation": "<p>The order to sort results by.</p>"}}, "documentation": "<p>The details that define an aggregation based on Amazon machine images (AMIs).</p>"}, "AmiAggregationResponse": {"type": "structure", "required": ["ami"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID for the AMI.</p>"}, "affectedInstances": {"shape": "<PERSON>", "documentation": "<p>The IDs of Amazon EC2 instances using this AMI.</p>"}, "ami": {"shape": "AmiId", "documentation": "<p>The ID of the AMI that findings were aggregated for.</p>"}, "severityCounts": {"shape": "SeverityCounts", "documentation": "<p>An object that contains the count of matched findings per severity.</p>"}}, "documentation": "<p>A response that contains the results of a finding aggregation by AMI.</p>"}, "AmiId": {"type": "string", "pattern": "^ami-([a-z0-9]{8}|[a-z0-9]{17}|\\*)$"}, "AmiSortBy": {"type": "string", "enum": ["CRITICAL", "HIGH", "ALL", "AFFECTED_INSTANCES"]}, "Architecture": {"type": "string", "enum": ["X86_64", "ARM64"]}, "ArchitectureList": {"type": "list", "member": {"shape": "Architecture"}, "max": 1, "min": 1}, "Arn": {"type": "string", "max": 1011, "min": 1}, "AssociateConfigurationRequest": {"type": "structure", "required": ["resource", "scanConfigurationArn"], "members": {"resource": {"shape": "CodeSecurityResource"}, "scanConfigurationArn": {"shape": "ScanConfigurationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the scan configuration.</p>"}}, "documentation": "<p>Contains details about a request to associate a code repository with a scan configuration.</p>"}, "AssociateConfigurationRequestList": {"type": "list", "member": {"shape": "AssociateConfigurationRequest"}, "max": 25, "min": 1}, "AssociateMemberRequest": {"type": "structure", "required": ["accountId"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the member account to be associated.</p>"}}}, "AssociateMemberResponse": {"type": "structure", "required": ["accountId"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the successfully associated member account.</p>"}}}, "AssociationResultStatusCode": {"type": "string", "enum": ["INTERNAL_ERROR", "ACCESS_DENIED", "SCAN_CONFIGURATION_NOT_FOUND", "INVALID_INPUT", "RESOURCE_NOT_FOUND", "QUOTA_EXCEEDED"]}, "AssociationResultStatusMessage": {"type": "string", "min": 1}, "AtigData": {"type": "structure", "members": {"firstSeen": {"shape": "FirstSeen", "documentation": "<p>The date and time this vulnerability was first observed.</p>"}, "lastSeen": {"shape": "LastSeen", "documentation": "<p>The date and time this vulnerability was last observed.</p>"}, "targets": {"shape": "Targets", "documentation": "<p>The commercial sectors this vulnerability targets.</p>"}, "ttps": {"shape": "Ttps", "documentation": "<p>The <a href=\"https://attack.mitre.org/\">MITRE ATT&amp;CK</a> tactics, techniques, and procedures (TTPs) associated with vulnerability.</p>"}}, "documentation": "<p>The Amazon Web Services Threat Intel Group (ATIG) details for a specific vulnerability.</p>"}, "AuthorizationUrl": {"type": "string", "sensitive": true}, "AutoEnable": {"type": "structure", "required": ["ec2", "ecr"], "members": {"codeRepository": {"shape": "Boolean", "documentation": "<p>Represents whether code repository scans are automatically enabled for new members of your Amazon Inspector organization.</p>"}, "ec2": {"shape": "Boolean", "documentation": "<p>Represents whether Amazon EC2 scans are automatically enabled for new members of your Amazon Inspector organization.</p>"}, "ecr": {"shape": "Boolean", "documentation": "<p>Represents whether Amazon ECR scans are automatically enabled for new members of your Amazon Inspector organization.</p>"}, "lambda": {"shape": "Boolean", "documentation": "<p>Represents whether Amazon Web Services Lambda standard scans are automatically enabled for new members of your Amazon Inspector organization. </p>"}, "lambdaCode": {"shape": "Boolean", "documentation": "<p>Represents whether Lambda code scans are automatically enabled for new members of your Amazon Inspector organization. </p>"}}, "documentation": "<p>Represents which scan types are automatically enabled for new members of your Amazon Inspector organization.</p>"}, "AwsEc2InstanceDetails": {"type": "structure", "members": {"iamInstanceProfileArn": {"shape": "NonEmptyString", "documentation": "<p>The IAM instance profile ARN of the Amazon EC2 instance.</p>"}, "imageId": {"shape": "NonEmptyString", "documentation": "<p>The image ID of the Amazon EC2 instance.</p>"}, "ipV4Addresses": {"shape": "IpV4AddressList", "documentation": "<p>The IPv4 addresses of the Amazon EC2 instance.</p>"}, "ipV6Addresses": {"shape": "IpV6AddressList", "documentation": "<p>The IPv6 addresses of the Amazon EC2 instance.</p>"}, "keyName": {"shape": "NonEmptyString", "documentation": "<p>The name of the key pair used to launch the Amazon EC2 instance.</p>"}, "launchedAt": {"shape": "DateTimeTimestamp", "documentation": "<p>The date and time the Amazon EC2 instance was launched at.</p>"}, "platform": {"shape": "Platform", "documentation": "<p>The platform of the Amazon EC2 instance.</p>"}, "subnetId": {"shape": "NonEmptyString", "documentation": "<p>The subnet ID of the Amazon EC2 instance.</p>"}, "type": {"shape": "NonEmptyString", "documentation": "<p>The type of the Amazon EC2 instance.</p>"}, "vpcId": {"shape": "NonEmptyString", "documentation": "<p>The VPC ID of the Amazon EC2 instance.</p>"}}, "documentation": "<p>Details of the Amazon EC2 instance involved in a finding.</p>"}, "AwsEcrContainerAggregation": {"type": "structure", "members": {"architectures": {"shape": "StringFilterList", "documentation": "<p>The architecture of the containers.</p>"}, "imageShas": {"shape": "StringFilterList", "documentation": "<p>The image SHA values.</p>"}, "imageTags": {"shape": "StringFilterList", "documentation": "<p>The image tags.</p>"}, "inUseCount": {"shape": "NumberFilterList", "documentation": "<p>The number of Amazon ECS tasks or Amazon EKS pods where the Amazon ECR container image is in use.</p>"}, "lastInUseAt": {"shape": "DateFilterList", "documentation": "<p>The last time an Amazon ECR image was used in an Amazon ECS task or Amazon EKS pod.</p>"}, "repositories": {"shape": "StringFilterList", "documentation": "<p>The container repositories.</p>"}, "resourceIds": {"shape": "StringFilterList", "documentation": "<p>The container resource IDs.</p>"}, "sortBy": {"shape": "AwsEcrContainerSortBy", "documentation": "<p>The value to sort by.</p>"}, "sortOrder": {"shape": "SortOrder", "documentation": "<p>The sort order (ascending or descending).</p>"}}, "documentation": "<p>An aggregation of information about Amazon ECR containers.</p>"}, "AwsEcrContainerAggregationResponse": {"type": "structure", "required": ["resourceId"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the account that owns the container.</p>"}, "architecture": {"shape": "String", "documentation": "<p>The architecture of the container.</p>"}, "imageSha": {"shape": "String", "documentation": "<p>The SHA value of the container image.</p>"}, "imageTags": {"shape": "StringList", "documentation": "<p>The container image stags.</p>"}, "inUseCount": {"shape": "<PERSON>", "documentation": "<p>The number of Amazon ECS tasks or Amazon EKS pods where the Amazon ECR container image is in use.</p>"}, "lastInUseAt": {"shape": "DateTimeTimestamp", "documentation": "<p>The last time an Amazon ECR image was used in an Amazon ECS task or Amazon EKS pod.</p>"}, "repository": {"shape": "String", "documentation": "<p>The container repository.</p>"}, "resourceId": {"shape": "NonEmptyString", "documentation": "<p>The resource ID of the container.</p>"}, "severityCounts": {"shape": "SeverityCounts", "documentation": "<p>The number of finding by severity.</p>"}}, "documentation": "<p>An aggregation of information about Amazon ECR containers.</p>"}, "AwsEcrContainerImageDetails": {"type": "structure", "required": ["imageHash", "registry", "repositoryName"], "members": {"architecture": {"shape": "NonEmptyString", "documentation": "<p>The architecture of the Amazon ECR container image.</p>"}, "author": {"shape": "String", "documentation": "<p>The image author of the Amazon ECR container image.</p>"}, "imageHash": {"shape": "ImageHash", "documentation": "<p>The image hash of the Amazon ECR container image.</p>"}, "imageTags": {"shape": "ImageTagList", "documentation": "<p>The image tags attached to the Amazon ECR container image.</p>"}, "inUseCount": {"shape": "<PERSON>", "documentation": "<p>The number of Amazon ECS tasks or Amazon EKS pods where the Amazon ECR container image is in use.</p>"}, "lastInUseAt": {"shape": "DateTimeTimestamp", "documentation": "<p>The last time an Amazon ECR image was used in an Amazon ECS task or Amazon EKS pod.</p>"}, "platform": {"shape": "Platform", "documentation": "<p>The platform of the Amazon ECR container image.</p>"}, "pushedAt": {"shape": "DateTimeTimestamp", "documentation": "<p>The date and time the Amazon ECR container image was pushed.</p>"}, "registry": {"shape": "NonEmptyString", "documentation": "<p>The registry for the Amazon ECR container image.</p>"}, "repositoryName": {"shape": "NonEmptyString", "documentation": "<p>The name of the repository the Amazon ECR container image resides in.</p>"}}, "documentation": "<p>The image details of the Amazon ECR container image.</p>"}, "AwsEcrContainerSortBy": {"type": "string", "enum": ["CRITICAL", "HIGH", "ALL"]}, "AwsEcsMetadataDetails": {"type": "structure", "required": ["detailsGroup", "taskDefinitionArn"], "members": {"detailsGroup": {"shape": "AwsEcsMetadataDetailsDetailsGroupString", "documentation": "<p>The details group information for a task in a cluster.</p>"}, "taskDefinitionArn": {"shape": "AwsEcsMetadataDetailsTaskDefinitionArnString", "documentation": "<p>The task definition ARN.</p>"}}, "documentation": "<p><PERSON><PERSON><PERSON> about tasks where an image was in use.</p>"}, "AwsEcsMetadataDetailsDetailsGroupString": {"type": "string", "max": 256, "min": 1}, "AwsEcsMetadataDetailsTaskDefinitionArnString": {"type": "string", "max": 2048, "min": 1}, "AwsEksMetadataDetails": {"type": "structure", "members": {"namespace": {"shape": "AwsEksMetadataDetailsNamespaceString", "documentation": "<p>The namespace for an Amazon EKS cluster.</p>"}, "workloadInfoList": {"shape": "AwsEksWorkloadInfoList", "documentation": "<p>The list of workloads.</p>"}}, "documentation": "<p>The metadata for an Amazon EKS pod where an Amazon ECR image is in use.</p>"}, "AwsEksMetadataDetailsNamespaceString": {"type": "string", "max": 256, "min": 1}, "AwsEksWorkloadInfo": {"type": "structure", "required": ["name", "type"], "members": {"name": {"shape": "AwsEksWorkloadInfoNameString", "documentation": "<p>The name of the workload.</p>"}, "type": {"shape": "AwsEksWorkloadInfoTypeString", "documentation": "<p>The workload type.</p>"}}, "documentation": "<p>Information about the workload.</p>"}, "AwsEksWorkloadInfoList": {"type": "list", "member": {"shape": "AwsEksWorkloadInfo"}, "max": 100, "min": 0}, "AwsEksWorkloadInfoNameString": {"type": "string", "max": 256, "min": 1}, "AwsEksWorkloadInfoTypeString": {"type": "string", "max": 256, "min": 1}, "AwsLambdaFunctionDetails": {"type": "structure", "required": ["codeSha256", "executionRoleArn", "functionName", "runtime", "version"], "members": {"architectures": {"shape": "ArchitectureList", "documentation": "<p>The instruction set architecture that the Amazon Web Services Lambda function supports. Architecture is a string array with one of the valid values. The default architecture value is <code>x86_64</code>.</p>"}, "codeSha256": {"shape": "NonEmptyString", "documentation": "<p>The SHA256 hash of the Amazon Web Services Lambda function's deployment package.</p>"}, "executionRoleArn": {"shape": "ExecutionRoleArn", "documentation": "<p>The Amazon Web Services Lambda function's execution role.</p>"}, "functionName": {"shape": "FunctionName", "documentation": "<p>The name of the Amazon Web Services Lambda function.</p>"}, "lastModifiedAt": {"shape": "Timestamp", "documentation": "<p>The date and time that a user last updated the configuration, in <a href=\"https://www.iso.org/iso-8601-date-and-time-format.html\">ISO 8601 format</a> </p>"}, "layers": {"shape": "LayerList", "documentation": "<p>The Amazon Web Services Lambda function's <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/configuration-layers.html\"> layers</a>. A Lambda function can have up to five layers.</p>"}, "packageType": {"shape": "PackageType", "documentation": "<p>The type of deployment package. Set to <code>Image</code> for container image and set <code>Zip</code> for .zip file archive.</p>"}, "runtime": {"shape": "Runtime", "documentation": "<p>The runtime environment for the Amazon Web Services Lambda function.</p>"}, "version": {"shape": "Version", "documentation": "<p>The version of the Amazon Web Services Lambda function.</p>"}, "vpcConfig": {"shape": "LambdaVpcConfig", "documentation": "<p>The Amazon Web Services Lambda function's networking configuration.</p>"}}, "documentation": "<p> A summary of information about the Amazon Web Services Lambda function.</p>"}, "BadRequestException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>One or more tags submitted as part of the request is not valid.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "BatchAssociateCodeSecurityScanConfigurationRequest": {"type": "structure", "required": ["associateConfigurationRequests"], "members": {"associateConfigurationRequests": {"shape": "AssociateConfigurationRequestList", "documentation": "<p>A list of code repositories to associate with the specified scan configuration.</p>"}}}, "BatchAssociateCodeSecurityScanConfigurationResponse": {"type": "structure", "members": {"failedAssociations": {"shape": "FailedAssociationResultList", "documentation": "<p>Details of any code repositories that failed to be associated with the scan configuration.</p>"}, "successfulAssociations": {"shape": "SuccessfulAssociationResultList", "documentation": "<p>Details of code repositories that were successfully associated with the scan configuration.</p>"}}}, "BatchDisassociateCodeSecurityScanConfigurationRequest": {"type": "structure", "required": ["disassociateConfigurationRequests"], "members": {"disassociateConfigurationRequests": {"shape": "DisassociateConfigurationRequestList", "documentation": "<p>A list of code repositories to disassociate from the specified scan configuration.</p>"}}}, "BatchDisassociateCodeSecurityScanConfigurationResponse": {"type": "structure", "members": {"failedAssociations": {"shape": "FailedAssociationResultList", "documentation": "<p>Details of any code repositories that failed to be disassociated from the scan configuration.</p>"}, "successfulAssociations": {"shape": "SuccessfulAssociationResultList", "documentation": "<p>Details of code repositories that were successfully disassociated from the scan configuration.</p>"}}}, "BatchGetAccountStatusRequest": {"type": "structure", "members": {"accountIds": {"shape": "AccountIdSet", "documentation": "<p>The 12-digit Amazon Web Services account IDs of the accounts to retrieve Amazon Inspector status for.</p>"}}}, "BatchGetAccountStatusResponse": {"type": "structure", "required": ["accounts"], "members": {"accounts": {"shape": "AccountStateList", "documentation": "<p>An array of objects that provide details on the status of Amazon Inspector for each of the requested accounts.</p>"}, "failedAccounts": {"shape": "FailedAccountList", "documentation": "<p>An array of objects detailing any accounts that failed to enable Amazon Inspector and why.</p>"}}}, "BatchGetCodeSnippetRequest": {"type": "structure", "required": ["findingArns"], "members": {"findingArns": {"shape": "BatchGetCodeSnippetRequestFindingArnsList", "documentation": "<p>An array of finding ARNs for the findings you want to retrieve code snippets from.</p>"}}}, "BatchGetCodeSnippetRequestFindingArnsList": {"type": "list", "member": {"shape": "FindingArn"}, "max": 10, "min": 1}, "BatchGetCodeSnippetResponse": {"type": "structure", "members": {"codeSnippetResults": {"shape": "CodeSnippetResultList", "documentation": "<p>The retrieved code snippets associated with the provided finding ARNs.</p>"}, "errors": {"shape": "CodeSnippetErrorList", "documentation": "<p>Any errors Amazon Inspector encountered while trying to retrieve the requested code snippets.</p>"}}}, "BatchGetFindingDetailsRequest": {"type": "structure", "required": ["findingArns"], "members": {"findingArns": {"shape": "FindingArnList", "documentation": "<p>A list of finding ARNs.</p>"}}}, "BatchGetFindingDetailsResponse": {"type": "structure", "members": {"errors": {"shape": "FindingDetailsErrorList", "documentation": "<p>Error information for findings that details could not be returned for.</p>"}, "findingDetails": {"shape": "FindingDetails", "documentation": "<p>A finding's vulnerability details.</p>"}}}, "BatchGetFreeTrialInfoRequest": {"type": "structure", "required": ["accountIds"], "members": {"accountIds": {"shape": "BatchGetFreeTrialInfoRequestAccountIdsList", "documentation": "<p>The account IDs to get free trial status for.</p>"}}}, "BatchGetFreeTrialInfoRequestAccountIdsList": {"type": "list", "member": {"shape": "MeteringAccountId"}, "max": 100, "min": 1}, "BatchGetFreeTrialInfoResponse": {"type": "structure", "required": ["accounts", "failedAccounts"], "members": {"accounts": {"shape": "FreeTrialAccountInfoList", "documentation": "<p>An array of objects that provide Amazon Inspector free trial details for each of the requested accounts. </p>"}, "failedAccounts": {"shape": "FreeTrialInfoErrorList", "documentation": "<p>An array of objects detailing any accounts that free trial data could not be returned for.</p>"}}}, "BatchGetMemberEc2DeepInspectionStatusRequest": {"type": "structure", "members": {"accountIds": {"shape": "AccountIdSet", "documentation": "<p>The unique identifiers for the Amazon Web Services accounts to retrieve Amazon Inspector deep inspection activation status for. </p>"}}}, "BatchGetMemberEc2DeepInspectionStatusResponse": {"type": "structure", "members": {"accountIds": {"shape": "MemberAccountEc2DeepInspectionStatusStateList", "documentation": "<p>An array of objects that provide details on the activation status of Amazon Inspector deep inspection for each of the requested accounts. </p>"}, "failedAccountIds": {"shape": "FailedMemberAccountEc2DeepInspectionStatusStateList", "documentation": "<p>An array of objects that provide details on any accounts that failed to activate Amazon Inspector deep inspection and why. </p>"}}}, "BatchUpdateMemberEc2DeepInspectionStatusRequest": {"type": "structure", "required": ["accountIds"], "members": {"accountIds": {"shape": "MemberAccountEc2DeepInspectionStatusList", "documentation": "<p>The unique identifiers for the Amazon Web Services accounts to change Amazon Inspector deep inspection status for.</p>"}}}, "BatchUpdateMemberEc2DeepInspectionStatusResponse": {"type": "structure", "members": {"accountIds": {"shape": "MemberAccountEc2DeepInspectionStatusStateList", "documentation": "<p>An array of objects that provide details for each of the accounts that Amazon Inspector deep inspection status was successfully changed for. </p>"}, "failedAccountIds": {"shape": "FailedMemberAccountEc2DeepInspectionStatusStateList", "documentation": "<p>An array of objects that provide details for each of the accounts that Amazon Inspector deep inspection status could not be successfully changed for. </p>"}}}, "BenchmarkProfile": {"type": "string", "max": 128, "min": 0}, "BenchmarkVersion": {"type": "string", "max": 8, "min": 0}, "Boolean": {"type": "boolean", "box": true}, "CancelFindingsReportRequest": {"type": "structure", "required": ["reportId"], "members": {"reportId": {"shape": "ReportId", "documentation": "<p>The ID of the report to be canceled.</p>"}}}, "CancelFindingsReportResponse": {"type": "structure", "required": ["reportId"], "members": {"reportId": {"shape": "ReportId", "documentation": "<p>The ID of the canceled report.</p>"}}}, "CancelSbomExportRequest": {"type": "structure", "required": ["reportId"], "members": {"reportId": {"shape": "ReportId", "documentation": "<p>The report ID of the SBOM export to cancel.</p>"}}}, "CancelSbomExportResponse": {"type": "structure", "members": {"reportId": {"shape": "ReportId", "documentation": "<p>The report ID of the canceled SBOM export.</p>"}}}, "CheckCount": {"type": "integer", "max": 65536, "min": 0}, "CheckIdFilterList": {"type": "list", "member": {"shape": "CisStringFilter"}, "max": 10, "min": 1}, "CisAccountIdList": {"type": "list", "member": {"shape": "AccountId"}, "max": 10000, "min": 1}, "CisCheckAggregation": {"type": "structure", "required": ["scanArn"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The account ID for the CIS check.</p>"}, "checkDescription": {"shape": "String", "documentation": "<p>The description for the CIS check.</p>"}, "checkId": {"shape": "String", "documentation": "<p>The check ID for the CIS check.</p>"}, "level": {"shape": "CisSecurityLevel", "documentation": "<p>The CIS check level.</p>"}, "platform": {"shape": "String", "documentation": "<p>The CIS check platform.</p>"}, "scanArn": {"shape": "CisScanArn", "documentation": "<p>The scan ARN for the CIS check scan ARN.</p>"}, "statusCounts": {"shape": "StatusCounts", "documentation": "<p>The CIS check status counts.</p>"}, "title": {"shape": "String", "documentation": "<p>The CIS check title.</p>"}}, "documentation": "<p>A CIS check.</p>"}, "CisCheckAggregationList": {"type": "list", "member": {"shape": "CisCheckAggregation"}, "max": 1000, "min": 1}, "CisDateFilter": {"type": "structure", "members": {"earliestScanStartTime": {"shape": "Timestamp", "documentation": "<p>The CIS date filter's earliest scan start time.</p>"}, "latestScanStartTime": {"shape": "Timestamp", "documentation": "<p>The CIS date filter's latest scan start time.</p>"}}, "documentation": "<p>The CIS date filter.</p>"}, "CisFindingArn": {"type": "string", "pattern": "^arn:aws(-gov|-cn)?:inspector2:[-.a-z0-9]{0,20}:\\d{12}:owner/\\d{12}/cis-finding/[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$"}, "CisFindingArnFilterList": {"type": "list", "member": {"shape": "CisStringFilter"}, "max": 10, "min": 1}, "CisFindingStatus": {"type": "string", "enum": ["PASSED", "FAILED", "SKIPPED"]}, "CisFindingStatusComparison": {"type": "string", "enum": ["EQUALS"]}, "CisFindingStatusFilter": {"type": "structure", "required": ["comparison", "value"], "members": {"comparison": {"shape": "CisFindingStatusComparison", "documentation": "<p>The comparison value of the CIS finding status filter.</p>"}, "value": {"shape": "CisFindingStatus", "documentation": "<p>The value of the CIS finding status filter.</p>"}}, "documentation": "<p>The CIS finding status filter.</p>"}, "CisFindingStatusFilterList": {"type": "list", "member": {"shape": "CisFindingStatusFilter"}, "max": 10, "min": 1}, "CisNumberFilter": {"type": "structure", "members": {"lowerInclusive": {"shape": "Integer", "documentation": "<p>The CIS number filter's lower inclusive.</p>"}, "upperInclusive": {"shape": "Integer", "documentation": "<p>The CIS number filter's upper inclusive.</p>"}}, "documentation": "<p>The CIS number filter.</p>"}, "CisNumberFilterList": {"type": "list", "member": {"shape": "CisNumberFilter"}, "max": 10, "min": 1}, "CisOwnerId": {"type": "string", "pattern": "^\\d{12}|o-[a-z0-9]{10,32}$"}, "CisReportFormat": {"type": "string", "enum": ["PDF", "CSV"]}, "CisReportStatus": {"type": "string", "enum": ["SUCCEEDED", "FAILED", "IN_PROGRESS"]}, "CisResultStatus": {"type": "string", "enum": ["PASSED", "FAILED", "SKIPPED"]}, "CisResultStatusComparison": {"type": "string", "enum": ["EQUALS"]}, "CisResultStatusFilter": {"type": "structure", "required": ["comparison", "value"], "members": {"comparison": {"shape": "CisResultStatusComparison", "documentation": "<p>The comparison value of the CIS result status filter.</p>"}, "value": {"shape": "CisResultStatus", "documentation": "<p>The value of the CIS result status filter.</p>"}}, "documentation": "<p>The CIS result status filter.</p>"}, "CisResultStatusFilterList": {"type": "list", "member": {"shape": "CisResultStatusFilter"}, "max": 10, "min": 1}, "CisRuleDetails": {"type": "blob", "max": 1000, "min": 0}, "CisRuleStatus": {"type": "string", "enum": ["FAILED", "PASSED", "NOT_EVALUATED", "INFORMATIONAL", "UNKNOWN", "NOT_APPLICABLE", "ERROR"]}, "CisScan": {"type": "structure", "required": ["scanArn", "scanConfigurationArn"], "members": {"failedChecks": {"shape": "Integer", "documentation": "<p>The CIS scan's failed checks.</p>"}, "scanArn": {"shape": "CisScanArn", "documentation": "<p>The CIS scan's ARN.</p>"}, "scanConfigurationArn": {"shape": "CisScanConfigurationArn", "documentation": "<p>The CIS scan's configuration ARN.</p>"}, "scanDate": {"shape": "Timestamp", "documentation": "<p>The CIS scan's date.</p>"}, "scanName": {"shape": "CisScanName", "documentation": "<p>The the name of the scan configuration that's associated with this scan.</p>"}, "scheduledBy": {"shape": "String", "documentation": "<p>The account or organization that schedules the CIS scan.</p>"}, "securityLevel": {"shape": "CisSecurityLevel", "documentation": "<p> The security level for the CIS scan. Security level refers to the Benchmark levels that CIS assigns to a profile. </p>"}, "status": {"shape": "CisScanStatus", "documentation": "<p>The CIS scan's status.</p>"}, "targets": {"shape": "CisTargets", "documentation": "<p>The CIS scan's targets.</p>"}, "totalChecks": {"shape": "Integer", "documentation": "<p>The CIS scan's total checks.</p>"}}, "documentation": "<p>The CIS scan.</p>"}, "CisScanArn": {"type": "string", "pattern": "^arn:aws(-us-gov|-cn)?:inspector2:[-.a-z0-9]{0,20}:\\d{12}:owner/(\\d{12}|o-[a-z0-9]{10,32})/cis-scan/[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$"}, "CisScanArnFilterList": {"type": "list", "member": {"shape": "CisStringFilter"}, "max": 10, "min": 1}, "CisScanConfiguration": {"type": "structure", "required": ["scanConfigurationArn"], "members": {"ownerId": {"shape": "CisOwnerId", "documentation": "<p>The CIS scan configuration's owner ID.</p>"}, "scanConfigurationArn": {"shape": "CisScanConfigurationArn", "documentation": "<p>The CIS scan configuration's scan configuration ARN.</p>"}, "scanName": {"shape": "CisScanName", "documentation": "<p>The name of the CIS scan configuration.</p>"}, "schedule": {"shape": "Schedule", "documentation": "<p>The CIS scan configuration's schedule.</p>"}, "securityLevel": {"shape": "CisSecurityLevel", "documentation": "<p>The CIS scan configuration's security level.</p>"}, "tags": {"shape": "CisTagMap", "documentation": "<p>The CIS scan configuration's tags.</p>"}, "targets": {"shape": "CisTargets", "documentation": "<p>The CIS scan configuration's targets.</p>"}}, "documentation": "<p>The CIS scan configuration.</p>"}, "CisScanConfigurationArn": {"type": "string", "pattern": "^arn:aws(-us-gov|-cn)?:inspector2:[a-z]{2}(-gov)?-[a-z]+-[0-9]{1}:[0-9]{12}:owner/(o-[a-z0-9]+|[0-9]{12})/cis-configuration/[0-9a-fA-F-]+$"}, "CisScanConfigurationArnFilterList": {"type": "list", "member": {"shape": "CisStringFilter"}, "max": 10, "min": 1}, "CisScanConfigurationList": {"type": "list", "member": {"shape": "CisScanConfiguration"}, "max": 100, "min": 0}, "CisScanConfigurationsSortBy": {"type": "string", "enum": ["SCAN_NAME", "SCAN_CONFIGURATION_ARN"]}, "CisScanDateFilterList": {"type": "list", "member": {"shape": "CisDateFilter"}, "max": 1, "min": 1}, "CisScanList": {"type": "list", "member": {"shape": "CisScan"}, "max": 50, "min": 0}, "CisScanName": {"type": "string", "max": 128, "min": 1}, "CisScanNameFilterList": {"type": "list", "member": {"shape": "CisStringFilter"}, "max": 10, "min": 1}, "CisScanResultDetails": {"type": "structure", "required": ["scanArn"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The CIS scan result details' account ID.</p>"}, "checkDescription": {"shape": "String", "documentation": "<p>The account ID that's associated with the CIS scan result details.</p>"}, "checkId": {"shape": "String", "documentation": "<p>The CIS scan result details' check ID.</p>"}, "findingArn": {"shape": "CisFindingArn", "documentation": "<p>The CIS scan result details' finding ARN.</p>"}, "level": {"shape": "CisSecurityLevel", "documentation": "<p>The CIS scan result details' level.</p>"}, "platform": {"shape": "String", "documentation": "<p>The CIS scan result details' platform.</p>"}, "remediation": {"shape": "String", "documentation": "<p>The CIS scan result details' remediation.</p>"}, "scanArn": {"shape": "CisScanArn", "documentation": "<p>The CIS scan result details' scan ARN.</p>"}, "status": {"shape": "CisFindingStatus", "documentation": "<p>The CIS scan result details' status.</p>"}, "statusReason": {"shape": "String", "documentation": "<p>The CIS scan result details' status reason.</p>"}, "targetResourceId": {"shape": "ResourceId", "documentation": "<p>The CIS scan result details' target resource ID.</p>"}, "title": {"shape": "String", "documentation": "<p>The CIS scan result details' title.</p>"}}, "documentation": "<p>The CIS scan result details.</p>"}, "CisScanResultDetailsFilterCriteria": {"type": "structure", "members": {"checkIdFilters": {"shape": "CheckIdFilterList", "documentation": "<p>The criteria's check ID filters.</p>"}, "findingArnFilters": {"shape": "CisFindingArnFilterList", "documentation": "<p>The criteria's finding ARN filters.</p>"}, "findingStatusFilters": {"shape": "CisFindingStatusFilterList", "documentation": "<p>The criteria's finding status filters.</p>"}, "securityLevelFilters": {"shape": "CisSecurityLevelFilterList", "documentation": "<p> The criteria's security level filters. . Security level refers to the Benchmark levels that CIS assigns to a profile. </p>"}, "titleFilters": {"shape": "TitleFilterList", "documentation": "<p>The criteria's title filters.</p>"}}, "documentation": "<p>The CIS scan result details filter criteria.</p>"}, "CisScanResultDetailsList": {"type": "list", "member": {"shape": "CisScanResultDetails"}, "max": 1000, "min": 1}, "CisScanResultDetailsSortBy": {"type": "string", "enum": ["CHECK_ID", "STATUS"]}, "CisScanResultsAggregatedByChecksFilterCriteria": {"type": "structure", "members": {"accountIdFilters": {"shape": "OneAccountIdFilterList", "documentation": "<p>The criteria's account ID filters.</p>"}, "checkIdFilters": {"shape": "CheckIdFilterList", "documentation": "<p>The criteria's check ID filters.</p>"}, "failedResourcesFilters": {"shape": "CisNumberFilterList", "documentation": "<p>The criteria's failed resources filters.</p>"}, "platformFilters": {"shape": "PlatformFilterList", "documentation": "<p>The criteria's platform filters.</p>"}, "securityLevelFilters": {"shape": "CisSecurityLevelFilterList", "documentation": "<p>The criteria's security level filters.</p>"}, "titleFilters": {"shape": "TitleFilterList", "documentation": "<p>The criteria's title filters.</p>"}}, "documentation": "<p>The scan results aggregated by checks filter criteria.</p>"}, "CisScanResultsAggregatedByChecksSortBy": {"type": "string", "enum": ["CHECK_ID", "TITLE", "PLATFORM", "FAILED_COUNTS", "SECURITY_LEVEL"]}, "CisScanResultsAggregatedByTargetResourceFilterCriteria": {"type": "structure", "members": {"accountIdFilters": {"shape": "AccountIdFilterList", "documentation": "<p>The criteria's account ID filters.</p>"}, "checkIdFilters": {"shape": "CheckIdFilterList", "documentation": "<p>The criteria's check ID filters.</p>"}, "failedChecksFilters": {"shape": "CisNumberFilterList", "documentation": "<p>The criteria's failed checks filters.</p>"}, "platformFilters": {"shape": "PlatformFilterList", "documentation": "<p>The criteria's platform filters.</p>"}, "statusFilters": {"shape": "CisResultStatusFilterList", "documentation": "<p>The criteria's status filter.</p>"}, "targetResourceIdFilters": {"shape": "ResourceIdFilterList", "documentation": "<p>The criteria's target resource ID filters.</p>"}, "targetResourceTagFilters": {"shape": "ResourceTagFilterList", "documentation": "<p>The criteria's target resource tag filters.</p>"}, "targetStatusFilters": {"shape": "TargetStatusFilterList", "documentation": "<p>The criteria's target status filters.</p>"}, "targetStatusReasonFilters": {"shape": "TargetStatusReasonFilterList", "documentation": "<p>The criteria's target status reason filters.</p>"}}, "documentation": "<p>The scan results aggregated by target resource filter criteria.</p>"}, "CisScanResultsAggregatedByTargetResourceSortBy": {"type": "string", "enum": ["RESOURCE_ID", "FAILED_COUNTS", "ACCOUNT_ID", "PLATFORM", "TARGET_STATUS", "TARGET_STATUS_REASON"]}, "CisScanResultsMaxResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "CisScanStatus": {"type": "string", "enum": ["FAILED", "COMPLETED", "CANCELLED", "IN_PROGRESS"]}, "CisScanStatusComparison": {"type": "string", "enum": ["EQUALS"]}, "CisScanStatusFilter": {"type": "structure", "required": ["comparison", "value"], "members": {"comparison": {"shape": "CisScanStatusComparison", "documentation": "<p>The filter comparison value.</p>"}, "value": {"shape": "CisScanStatus", "documentation": "<p>The filter value.</p>"}}, "documentation": "<p>The CIS scan status filter.</p>"}, "CisScanStatusFilterList": {"type": "list", "member": {"shape": "CisScanStatusFilter"}, "max": 10, "min": 1}, "CisScheduledByFilterList": {"type": "list", "member": {"shape": "CisStringFilter"}, "max": 10, "min": 1}, "CisSecurityLevel": {"type": "string", "enum": ["LEVEL_1", "LEVEL_2"]}, "CisSecurityLevelComparison": {"type": "string", "enum": ["EQUALS"]}, "CisSecurityLevelFilter": {"type": "structure", "required": ["comparison", "value"], "members": {"comparison": {"shape": "CisSecurityLevelComparison", "documentation": "<p>The CIS security filter comparison value.</p>"}, "value": {"shape": "CisSecurityLevel", "documentation": "<p>The CIS security filter value.</p>"}}, "documentation": "<p> The CIS security level filter. Security level refers to the Benchmark levels that CIS assigns to a profile. </p>"}, "CisSecurityLevelFilterList": {"type": "list", "member": {"shape": "CisSecurityLevelFilter"}, "max": 10, "min": 1}, "CisSessionMessage": {"type": "structure", "required": ["cisRuleDetails", "ruleId", "status"], "members": {"cisRuleDetails": {"shape": "CisRuleDetails", "documentation": "<p>The CIS rule details for the CIS session message.</p>"}, "ruleId": {"shape": "RuleId", "documentation": "<p>The rule ID for the CIS session message.</p>"}, "status": {"shape": "CisRuleStatus", "documentation": "<p>The status of the CIS session message.</p>"}}, "documentation": "<p>The CIS session message.</p>"}, "CisSessionMessages": {"type": "list", "member": {"shape": "CisSessionMessage"}, "max": 150, "min": 1}, "CisSortOrder": {"type": "string", "enum": ["ASC", "DESC"]}, "CisStringComparison": {"type": "string", "enum": ["EQUALS", "PREFIX", "NOT_EQUALS"]}, "CisStringFilter": {"type": "structure", "required": ["comparison", "value"], "members": {"comparison": {"shape": "CisStringComparison", "documentation": "<p>The comparison value of the CIS string filter.</p>"}, "value": {"shape": "String", "documentation": "<p>The value of the CIS string filter.</p>"}}, "documentation": "<p>The CIS string filter.</p>"}, "CisTagMap": {"type": "map", "key": {"shape": "MapKey"}, "value": {"shape": "MapValue"}}, "CisTargetResourceAggregation": {"type": "structure", "required": ["scanArn"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The account ID for the CIS target resource.</p>"}, "platform": {"shape": "String", "documentation": "<p>The platform for the CIS target resource.</p>"}, "scanArn": {"shape": "CisScanArn", "documentation": "<p>The scan ARN for the CIS target resource.</p>"}, "statusCounts": {"shape": "StatusCounts", "documentation": "<p>The target resource status counts.</p>"}, "targetResourceId": {"shape": "ResourceId", "documentation": "<p>The ID of the target resource.</p>"}, "targetResourceTags": {"shape": "TargetResourceTags", "documentation": "<p>The tag for the target resource.</p>"}, "targetStatus": {"shape": "CisTargetStatus", "documentation": "<p>The status of the target resource.</p>"}, "targetStatusReason": {"shape": "CisTargetStatusReason", "documentation": "<p>The reason for the target resource.</p>"}}, "documentation": "<p>The CIS target resource aggregation.</p>"}, "CisTargetResourceAggregationList": {"type": "list", "member": {"shape": "CisTargetResourceAggregation"}, "max": 1000, "min": 1}, "CisTargetStatus": {"type": "string", "enum": ["TIMED_OUT", "CANCELLED", "COMPLETED"]}, "CisTargetStatusComparison": {"type": "string", "enum": ["EQUALS"]}, "CisTargetStatusFilter": {"type": "structure", "required": ["comparison", "value"], "members": {"comparison": {"shape": "CisTargetStatusComparison", "documentation": "<p>The comparison value of the CIS target status filter.</p>"}, "value": {"shape": "CisTargetStatus", "documentation": "<p>The value of the CIS target status filter.</p>"}}, "documentation": "<p>The CIS target status filter.</p>"}, "CisTargetStatusReason": {"type": "string", "enum": ["SCAN_IN_PROGRESS", "UNSUPPORTED_OS", "SSM_UNMANAGED"]}, "CisTargetStatusReasonFilter": {"type": "structure", "required": ["comparison", "value"], "members": {"comparison": {"shape": "CisTargetStatusComparison", "documentation": "<p>The comparison value of the CIS target status reason filter.</p>"}, "value": {"shape": "CisTargetStatusReason", "documentation": "<p>The value of the CIS target status reason filter.</p>"}}, "documentation": "<p>The CIS target status reason filter.</p>"}, "CisTargets": {"type": "structure", "members": {"accountIds": {"shape": "CisAccountIdList", "documentation": "<p>The CIS target account ids.</p>"}, "targetResourceTags": {"shape": "TargetResourceTags", "documentation": "<p>The CIS target resource tags.</p>"}}, "documentation": "<p>The CIS targets.</p>"}, "CisaAction": {"type": "string", "min": 0}, "CisaData": {"type": "structure", "members": {"action": {"shape": "CisaAction", "documentation": "<p>The remediation action recommended by CISA for this vulnerability.</p>"}, "dateAdded": {"shape": "CisaDateAdded", "documentation": "<p>The date and time CISA added this vulnerability to their catalogue.</p>"}, "dateDue": {"shape": "CisaDateDue", "documentation": "<p>The date and time CISA expects a fix to have been provided vulnerability.</p>"}}, "documentation": "<p>The Cybersecurity and Infrastructure Security Agency (CISA) details for a specific vulnerability.</p>"}, "CisaDateAdded": {"type": "timestamp"}, "CisaDateDue": {"type": "timestamp"}, "ClientToken": {"type": "string", "max": 64, "min": 1}, "ClusterDetails": {"type": "structure", "required": ["clusterMetadata", "lastInUse"], "members": {"clusterMetadata": {"shape": "ClusterMetadata"}, "lastInUse": {"shape": "Timestamp", "documentation": "<p>The last timestamp when Amazon Inspector recorded the image in use in the task or pod in the cluster.</p>"}, "runningUnitCount": {"shape": "<PERSON>", "documentation": "<p>The number of tasks or pods where an image was running on the cluster.</p>"}, "stoppedUnitCount": {"shape": "<PERSON>", "documentation": "<p>The number of tasks or pods where an image was stopped on the cluster in the last 24 hours.</p>"}}, "documentation": "<p>Details about the task or pod in the cluster.</p>"}, "ClusterForImageFilterCriteria": {"type": "structure", "required": ["resourceId"], "members": {"resourceId": {"shape": "ClusterForImageFilterCriteriaResourceIdString", "documentation": "<p>The resource Id to be used in the filter criteria.</p>"}}, "documentation": "<p>The filter criteria to be used.</p>"}, "ClusterForImageFilterCriteriaResourceIdString": {"type": "string", "pattern": "^arn:.*:ecr:.*:\\d{12}:repository\\/(?:[a-z0-9]+(?:[._-][a-z0-9]+)*\\/)*[a-z0-9]+(?:[._-][a-z0-9]+)*(\\/sha256:[a-z0-9]{64})?$"}, "ClusterInformation": {"type": "structure", "required": ["clusterArn"], "members": {"clusterArn": {"shape": "ClusterInformationClusterArnString", "documentation": "<p>The cluster ARN.</p>"}, "clusterDetails": {"shape": "ClusterInformationClusterDetailsList", "documentation": "<p>Details about the cluster.</p>"}}, "documentation": "<p>Information about the cluster.</p>"}, "ClusterInformationClusterArnString": {"type": "string", "max": 2048, "min": 1, "pattern": "^arn:aws(?:-[a-z0-9-]+)?:(?:ecs|eks):[a-z0-9-]+:[0-9]{12}:cluster/[a-zA-Z0-9_-]+$"}, "ClusterInformationClusterDetailsList": {"type": "list", "member": {"shape": "ClusterDetails"}, "max": 100, "min": 1}, "ClusterInformationList": {"type": "list", "member": {"shape": "ClusterInformation"}, "max": 100, "min": 1}, "ClusterMetadata": {"type": "structure", "members": {"awsEcsMetadataDetails": {"shape": "AwsEcsMetadataDetails", "documentation": "<p>The details for an Amazon ECS cluster in the cluster metadata.</p>"}, "awsEksMetadataDetails": {"shape": "AwsEksMetadataDetails", "documentation": "<p>The details for an Amazon EKS cluster in the cluster metadata.</p>"}}, "documentation": "<p>The metadata for a cluster.</p>", "union": true}, "CodeFilePath": {"type": "structure", "required": ["endLine", "fileName", "filePath", "startLine"], "members": {"endLine": {"shape": "Integer", "documentation": "<p>The line number of the last line of code that a vulnerability was found in.</p>"}, "fileName": {"shape": "NonEmptyString", "documentation": "<p>The name of the file the code vulnerability was found in.</p>"}, "filePath": {"shape": "NonEmptyString", "documentation": "<p>The file path to the code that a vulnerability was found in.</p>"}, "startLine": {"shape": "Integer", "documentation": "<p>The line number of the first line of code that a vulnerability was found in.</p>"}}, "documentation": "<p>Contains information on where a code vulnerability is located in your Lambda function.</p>"}, "CodeLine": {"type": "structure", "required": ["content", "lineNumber"], "members": {"content": {"shape": "CodeLineContentString", "documentation": "<p>The content of a line of code</p>"}, "lineNumber": {"shape": "Integer", "documentation": "<p>The line number that a section of code is located at.</p>"}}, "documentation": "<p>Contains information on the lines of code associated with a code snippet.</p>"}, "CodeLineContentString": {"type": "string", "max": 240, "min": 0}, "CodeLineList": {"type": "list", "member": {"shape": "CodeLine"}, "max": 20, "min": 1}, "CodeRepositoryAggregation": {"type": "structure", "members": {"projectNames": {"shape": "StringFilterList", "documentation": "<p>The project names to include in the aggregation results.</p>"}, "providerTypes": {"shape": "StringFilterList", "documentation": "<p>The repository provider types to include in the aggregation results.</p>"}, "resourceIds": {"shape": "StringFilterList", "documentation": "<p>The resource IDs to include in the aggregation results.</p>"}, "sortBy": {"shape": "CodeRepositorySortBy", "documentation": "<p>The value to sort results by in the code repository aggregation.</p>"}, "sortOrder": {"shape": "SortOrder", "documentation": "<p>The order to sort results by (ascending or descending) in the code repository aggregation.</p>"}}, "documentation": "<p>The details that define an aggregation based on code repositories.</p>"}, "CodeRepositoryAggregationResponse": {"type": "structure", "required": ["projectNames"], "members": {"accountId": {"shape": "String", "documentation": "<p>The Amazon Web Services account ID associated with the code repository.</p>"}, "exploitAvailableActiveFindingsCount": {"shape": "<PERSON>", "documentation": "<p>The number of active findings that have an exploit available for the code repository.</p>"}, "fixAvailableActiveFindingsCount": {"shape": "<PERSON>", "documentation": "<p>The number of active findings that have a fix available for the code repository.</p>"}, "projectNames": {"shape": "String", "documentation": "<p>The names of the projects associated with the code repository.</p>"}, "providerType": {"shape": "String", "documentation": "<p>The type of repository provider for the code repository.</p>"}, "resourceId": {"shape": "String", "documentation": "<p>The resource ID of the code repository.</p>"}, "severityCounts": {"shape": "SeverityCounts"}}, "documentation": "<p>A response that contains the results of a finding aggregation by code repository.</p>"}, "CodeRepositoryDetails": {"type": "structure", "members": {"integrationArn": {"shape": "CodeRepositoryIntegrationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the code security integration associated with the repository.</p>"}, "projectName": {"shape": "CodeRepositoryProjectName", "documentation": "<p>The name of the project in the code repository.</p>"}, "providerType": {"shape": "CodeRepositoryProviderType", "documentation": "<p>The type of repository provider (such as GitHub, GitLab, etc.).</p>"}}, "documentation": "<p>Contains details about a code repository associated with a finding.</p>"}, "CodeRepositoryIntegrationArn": {"type": "string", "pattern": "^arn:(aws[a-zA-Z-]*)?:inspector2:[a-z]{2}(-gov)?-[a-z]+-\\d{1}:\\d{12}:codesecurity-integration\\/[a-f0-9-]{36}$"}, "CodeRepositoryMetadata": {"type": "structure", "required": ["projectName", "providerType", "providerTypeVisibility"], "members": {"integrationArn": {"shape": "CodeRepositoryIntegrationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the code security integration associated with the repository.</p>"}, "lastScannedCommitId": {"shape": "CommitId", "documentation": "<p>The ID of the last commit that was scanned in the repository.</p>"}, "onDemandScan": {"shape": "CodeRepositoryOnDemandScan", "documentation": "<p>Information about on-demand scans performed on the repository.</p>"}, "projectName": {"shape": "CodeRepositoryMetadataProjectNameString", "documentation": "<p>The name of the project in the code repository.</p>"}, "providerType": {"shape": "CodeRepositoryMetadataProviderTypeString", "documentation": "<p>The type of repository provider (such as GitHub, GitLab, etc.).</p>"}, "providerTypeVisibility": {"shape": "CodeRepositoryMetadataProviderTypeVisibilityString", "documentation": "<p>The visibility setting of the repository (public or private).</p>"}, "scanConfiguration": {"shape": "ProjectCodeSecurityScanConfiguration", "documentation": "<p>The scan configuration settings applied to the code repository.</p>"}}, "documentation": "<p>Contains metadata information about a code repository that is being scanned by Amazon Inspector.</p>"}, "CodeRepositoryMetadataProjectNameString": {"type": "string", "max": 300, "min": 1}, "CodeRepositoryMetadataProviderTypeString": {"type": "string", "max": 300, "min": 1}, "CodeRepositoryMetadataProviderTypeVisibilityString": {"type": "string", "max": 300, "min": 1}, "CodeRepositoryOnDemandScan": {"type": "structure", "members": {"lastScanAt": {"shape": "DateTimeTimestamp", "documentation": "<p>The timestamp when the last on-demand scan was performed.</p>"}, "lastScannedCommitId": {"shape": "CommitId", "documentation": "<p>The ID of the last commit that was scanned during an on-demand scan.</p>"}, "scanStatus": {"shape": "ScanStatus"}}, "documentation": "<p>Contains information about on-demand scans performed on a code repository.</p>"}, "CodeRepositoryProjectName": {"type": "string", "max": 512, "min": 1}, "CodeRepositoryProviderType": {"type": "string", "enum": ["GITHUB", "GITLAB_SELF_MANAGED"]}, "CodeRepositorySortBy": {"type": "string", "enum": ["CRITICAL", "HIGH", "ALL"]}, "CodeScanStatus": {"type": "string", "enum": ["IN_PROGRESS", "SUCCESSFUL", "FAILED", "SKIPPED"]}, "CodeSecurityClientToken": {"type": "string", "max": 64, "min": 1, "pattern": "^[\\S]+$"}, "CodeSecurityIntegrationArn": {"type": "string", "documentation": "<p>arn:aws:inspector2:<region>:<account-id>:codesecurity-integration/<uuid></p>", "pattern": "^arn:(aws[a-zA-Z-]*)?:inspector2:[a-z]{2}(-gov)?-[a-z]+-\\d{1}:\\d{12}:codesecurity-integration/[a-f0-9-]{36}$"}, "CodeSecurityIntegrationSummary": {"type": "structure", "required": ["createdOn", "integrationArn", "lastUpdateOn", "name", "status", "statusReason", "type"], "members": {"createdOn": {"shape": "Timestamp", "documentation": "<p>The timestamp when the code security integration was created.</p>"}, "integrationArn": {"shape": "CodeSecurityIntegrationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the code security integration.</p>"}, "lastUpdateOn": {"shape": "Timestamp", "documentation": "<p>The timestamp when the code security integration was last updated.</p>"}, "name": {"shape": "IntegrationName", "documentation": "<p>The name of the code security integration.</p>"}, "status": {"shape": "IntegrationStatus", "documentation": "<p>The current status of the code security integration.</p>"}, "statusReason": {"shape": "String", "documentation": "<p>The reason for the current status of the code security integration.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags associated with the code security integration.</p>"}, "type": {"shape": "IntegrationType", "documentation": "<p>The type of repository provider for the integration.</p>"}}, "documentation": "<p>A summary of information about a code security integration.</p>"}, "CodeSecurityResource": {"type": "structure", "members": {"projectId": {"shape": "ProjectId", "documentation": "<p>The unique identifier of the project in the code repository.</p>"}}, "documentation": "<p>Identifies a specific resource in a code repository that will be scanned.</p>", "union": true}, "CodeSecurityScanConfiguration": {"type": "structure", "required": ["ruleSetCategories"], "members": {"continuousIntegrationScanConfiguration": {"shape": "ContinuousIntegrationScanConfiguration", "documentation": "<p>Configuration settings for continuous integration scans that run automatically when code changes are made.</p>"}, "periodicScanConfiguration": {"shape": "PeriodicScanConfiguration", "documentation": "<p>Configuration settings for periodic scans that run on a scheduled basis.</p>"}, "ruleSetCategories": {"shape": "RuleSetCategories", "documentation": "<p>The categories of security rules to be applied during the scan.</p>"}}, "documentation": "<p>Contains the configuration settings for code security scans.</p>"}, "CodeSecurityScanConfigurationAssociationSummaries": {"type": "list", "member": {"shape": "CodeSecurityScanConfigurationAssociationSummary"}}, "CodeSecurityScanConfigurationAssociationSummary": {"type": "structure", "members": {"resource": {"shape": "CodeSecurityResource"}}, "documentation": "<p>A summary of an association between a code repository and a scan configuration.</p>"}, "CodeSecurityScanConfigurationSummaries": {"type": "list", "member": {"shape": "CodeSecurityScanConfigurationSummary"}}, "CodeSecurityScanConfigurationSummary": {"type": "structure", "required": ["name", "ownerAccountId", "ruleSetCategories", "scanConfigurationArn"], "members": {"continuousIntegrationScanSupportedEvents": {"shape": "ContinuousIntegrationScanSupportedEvents", "documentation": "<p>The repository events that trigger continuous integration scans.</p>"}, "frequencyExpression": {"shape": "FrequencyExpression", "documentation": "<p>The schedule expression for periodic scans, in cron format.</p>"}, "name": {"shape": "ScanConfigurationName", "documentation": "<p>The name of the scan configuration.</p>"}, "ownerAccountId": {"shape": "OwnerId", "documentation": "<p>The Amazon Web Services account ID that owns the scan configuration.</p>"}, "periodicScanFrequency": {"shape": "PeriodicScanFrequency", "documentation": "<p>The frequency at which periodic scans are performed.</p>"}, "ruleSetCategories": {"shape": "RuleSetCategories", "documentation": "<p>The categories of security rules applied during the scan.</p>"}, "scanConfigurationArn": {"shape": "ScanConfigurationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the scan configuration.</p>"}, "scopeSettings": {"shape": "ScopeSettings", "documentation": "<p>The scope settings that define which repositories will be scanned. If the <code>ScopeSetting</code> parameter is <code>ALL</code> the scan configuration applies to all existing and future projects imported into Amazon Inspector.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags associated with the scan configuration.</p>"}}, "documentation": "<p>A summary of information about a code security scan configuration.</p>"}, "CodeSecurityUuid": {"type": "string", "pattern": "^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$"}, "CodeSnippetError": {"type": "structure", "required": ["errorCode", "errorMessage", "findingArn"], "members": {"errorCode": {"shape": "CodeSnippetErrorCode", "documentation": "<p>The error code for the error that prevented a code snippet from being retrieved.</p>"}, "errorMessage": {"shape": "NonEmptyString", "documentation": "<p>The error message received when Amazon Inspector failed to retrieve a code snippet.</p>"}, "findingArn": {"shape": "FindingArn", "documentation": "<p>The ARN of the finding that a code snippet couldn't be retrieved for.</p>"}}, "documentation": "<p>Contains information about any errors encountered while trying to retrieve a code snippet.</p>"}, "CodeSnippetErrorCode": {"type": "string", "enum": ["INTERNAL_ERROR", "ACCESS_DENIED", "CODE_SNIPPET_NOT_FOUND", "INVALID_INPUT"]}, "CodeSnippetErrorList": {"type": "list", "member": {"shape": "CodeSnippetError"}}, "CodeSnippetResult": {"type": "structure", "members": {"codeSnippet": {"shape": "CodeLineList", "documentation": "<p>Contains information on the retrieved code snippet.</p>"}, "endLine": {"shape": "Integer", "documentation": "<p>The line number of the last line of a code snippet.</p>"}, "findingArn": {"shape": "FindingArn", "documentation": "<p>The ARN of a finding that the code snippet is associated with.</p>"}, "startLine": {"shape": "Integer", "documentation": "<p>The line number of the first line of a code snippet.</p>"}, "suggestedFixes": {"shape": "SuggestedFixes", "documentation": "<p>Details of a suggested code fix.</p>"}}, "documentation": "<p>Contains information on a code snippet retrieved by Amazon Inspector from a code vulnerability finding.</p>"}, "CodeSnippetResultList": {"type": "list", "member": {"shape": "CodeSnippetResult"}}, "CodeVulnerabilityDetails": {"type": "structure", "required": ["cwes", "detectorId", "detectorName", "filePath"], "members": {"cwes": {"shape": "CweList", "documentation": "<p>The Common Weakness Enumeration (CWE) item associated with the detected vulnerability.</p>"}, "detectorId": {"shape": "NonEmptyString", "documentation": "<p>The ID for the Amazon CodeGuru detector associated with the finding. For more information on detectors see <a href=\"https://docs.aws.amazon.com/codeguru/detector-library\">Amazon CodeGuru Detector Library</a>.</p>"}, "detectorName": {"shape": "NonEmptyString", "documentation": "<p>The name of the detector used to identify the code vulnerability. For more information on detectors see <a href=\"https://docs.aws.amazon.com/codeguru/detector-library\">CodeGuru Detector Library</a>.</p>"}, "detectorTags": {"shape": "DetectorTagList", "documentation": "<p>The detector tag associated with the vulnerability. Detector tags group related vulnerabilities by common themes or tactics. For a list of available tags by programming language, see <a href=\"https://docs.aws.amazon.com/codeguru/detector-library/java/tags/\">Java tags</a>, or <a href=\"https://docs.aws.amazon.com/codeguru/detector-library/python/tags/\">Python tags</a>. </p>"}, "filePath": {"shape": "CodeFilePath", "documentation": "<p>Contains information on where the code vulnerability is located in your code.</p>"}, "referenceUrls": {"shape": "ReferenceUrls", "documentation": "<p>A URL containing supporting documentation about the code vulnerability detected.</p>"}, "ruleId": {"shape": "NonEmptyString", "documentation": "<p>The identifier for a rule that was used to detect the code vulnerability.</p>"}, "sourceLambdaLayerArn": {"shape": "LambdaLayerArn", "documentation": "<p>The Amazon Resource Name (ARN) of the Lambda layer that the code vulnerability was detected in.</p>"}}, "documentation": "<p>Contains information on the code vulnerability identified in your Lambda function.</p>"}, "CommitId": {"type": "string", "max": 40, "min": 0, "pattern": "^([a-f0-9]{40})$"}, "Component": {"type": "string"}, "ComponentArn": {"type": "string"}, "ComponentType": {"type": "string"}, "ComputePlatform": {"type": "structure", "members": {"product": {"shape": "Product", "documentation": "<p>The compute platform product.</p>"}, "vendor": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>The compute platform vendor.</p>"}, "version": {"shape": "PlatformVersion", "documentation": "<p>The compute platform version.</p>"}}, "documentation": "<p>A compute platform.</p>"}, "ConfigurationLevel": {"type": "string", "enum": ["ORGANIZATION", "ACCOUNT"]}, "ConflictException": {"type": "structure", "required": ["message", "resourceId", "resourceType"], "members": {"message": {"shape": "String"}, "resourceId": {"shape": "String", "documentation": "<p>The ID of the conflicting resource.</p>"}, "resourceType": {"shape": "String", "documentation": "<p>The type of the conflicting resource.</p>"}}, "documentation": "<p>A conflict occurred. This exception occurs when the same resource is being modified by concurrent requests.</p>", "error": {"httpStatusCode": 409, "senderFault": true}, "exception": true}, "ContinuousIntegrationScanConfiguration": {"type": "structure", "required": ["supportedEvents"], "members": {"supportedEvents": {"shape": "ContinuousIntegrationScanSupportedEvents", "documentation": "<p>The repository events that trigger continuous integration scans, such as pull requests or commits.</p>"}}, "documentation": "<p>Configuration settings for continuous integration scans that run automatically when code changes are made.</p>"}, "ContinuousIntegrationScanEvent": {"type": "string", "enum": ["PULL_REQUEST", "PUSH"]}, "ContinuousIntegrationScanSupportedEvents": {"type": "list", "member": {"shape": "ContinuousIntegrationScanEvent"}, "max": 2, "min": 1}, "Counts": {"type": "structure", "members": {"count": {"shape": "AggCounts", "documentation": "<p>The number of resources.</p>"}, "groupKey": {"shape": "GroupKey", "documentation": "<p>The key associated with this group</p>"}}, "documentation": "<p>a structure that contains information on the count of resources within a group.</p>"}, "CountsList": {"type": "list", "member": {"shape": "Counts"}, "max": 5, "min": 1}, "CoverageDateFilter": {"type": "structure", "members": {"endInclusive": {"shape": "DateTimeTimestamp", "documentation": "<p>A timestamp representing the end of the time period to filter results by.</p>"}, "startInclusive": {"shape": "DateTimeTimestamp", "documentation": "<p>A timestamp representing the start of the time period to filter results by.</p>"}}, "documentation": "<p>Contains details of a coverage date filter.</p>"}, "CoverageDateFilterList": {"type": "list", "member": {"shape": "CoverageDateFilter"}, "max": 10, "min": 1}, "CoverageFilterCriteria": {"type": "structure", "members": {"accountId": {"shape": "CoverageStringFilterList", "documentation": "<p>An array of Amazon Web Services account IDs to return coverage statistics for.</p>"}, "codeRepositoryProjectName": {"shape": "CoverageStringFilterList", "documentation": "<p>Filter criteria for code repositories based on project name.</p>"}, "codeRepositoryProviderType": {"shape": "CoverageStringFilterList", "documentation": "<p>Filter criteria for code repositories based on provider type (such as GitHub, GitLab, etc.).</p>"}, "codeRepositoryProviderTypeVisibility": {"shape": "CoverageStringFilterList", "documentation": "<p>Filter criteria for code repositories based on visibility setting (public or private).</p>"}, "ec2InstanceTags": {"shape": "CoverageMapFilterList", "documentation": "<p>The Amazon EC2 instance tags to filter on.</p>"}, "ecrImageInUseCount": {"shape": "CoverageNumberFilterList", "documentation": "<p>The number of Amazon ECR images in use.</p>"}, "ecrImageLastInUseAt": {"shape": "CoverageDateFilterList", "documentation": "<p>The Amazon ECR image that was last in use.</p>"}, "ecrImageTags": {"shape": "CoverageStringFilterList", "documentation": "<p>The Amazon ECR image tags to filter on.</p>"}, "ecrRepositoryName": {"shape": "CoverageStringFilterList", "documentation": "<p>The Amazon ECR repository name to filter on.</p>"}, "imagePulledAt": {"shape": "CoverageDateFilterList", "documentation": "<p>The date an image was last pulled at.</p>"}, "lambdaFunctionName": {"shape": "CoverageStringFilterList", "documentation": "<p>Returns coverage statistics for Amazon Web Services Lambda functions filtered by function names.</p>"}, "lambdaFunctionRuntime": {"shape": "CoverageStringFilterList", "documentation": "<p>Returns coverage statistics for Amazon Web Services Lambda functions filtered by runtime.</p>"}, "lambdaFunctionTags": {"shape": "CoverageMapFilterList", "documentation": "<p>Returns coverage statistics for Amazon Web Services Lambda functions filtered by tag.</p>"}, "lastScannedAt": {"shape": "CoverageDateFilterList", "documentation": "<p>Filters Amazon Web Services resources based on whether Amazon Inspector has checked them for vulnerabilities within the specified time range.</p>"}, "lastScannedCommitId": {"shape": "CoverageStringFilterList", "documentation": "<p>Filter criteria for code repositories based on the ID of the last scanned commit.</p>"}, "resourceId": {"shape": "CoverageStringFilterList", "documentation": "<p>An array of Amazon Web Services resource IDs to return coverage statistics for.</p>"}, "resourceType": {"shape": "CoverageStringFilterList", "documentation": "<p>An array of Amazon Web Services resource types to return coverage statistics for. The values can be <code>AWS_EC2_INSTANCE</code>, <code>AWS_LAMBDA_FUNCTION</code>, <code>AWS_ECR_CONTAINER_IMAGE</code>, <code>AWS_ECR_REPOSITORY</code> or <code>AWS_ACCOUNT</code>.</p>"}, "scanMode": {"shape": "CoverageStringFilterList", "documentation": "<p>The filter to search for Amazon EC2 instance coverage by scan mode. Valid values are <code>EC2_SSM_AGENT_BASED</code> and <code>EC2_AGENTLESS</code>.</p>"}, "scanStatusCode": {"shape": "CoverageStringFilterList", "documentation": "<p>The scan status code to filter on. Valid values are: <code>ValidationException</code>, <code>InternalServerException</code>, <code>ResourceNotFoundException</code>, <code>BadRequestException</code>, and <code>ThrottlingException</code>.</p>"}, "scanStatusReason": {"shape": "CoverageStringFilterList", "documentation": "<p>The scan status reason to filter on.</p>"}, "scanType": {"shape": "CoverageStringFilterList", "documentation": "<p>An array of Amazon Inspector scan types to return coverage statistics for.</p>"}}, "documentation": "<p>A structure that identifies filter criteria for <code>GetCoverageStatistics</code>.</p>"}, "CoverageMapComparison": {"type": "string", "enum": ["EQUALS"]}, "CoverageMapFilter": {"type": "structure", "required": ["comparison", "key"], "members": {"comparison": {"shape": "CoverageMapComparison", "documentation": "<p>The operator to compare coverage on.</p>"}, "key": {"shape": "NonEmptyString", "documentation": "<p>The tag key associated with the coverage map filter.</p>"}, "value": {"shape": "NonEmptyString", "documentation": "<p>The tag value associated with the coverage map filter.</p>"}}, "documentation": "<p>Contains details of a coverage map filter.</p>"}, "CoverageMapFilterList": {"type": "list", "member": {"shape": "CoverageMapFilter"}, "max": 10, "min": 1}, "CoverageNumberFilter": {"type": "structure", "members": {"lowerInclusive": {"shape": "<PERSON>", "documentation": "<p>The lower inclusive for the coverage number.</p>"}, "upperInclusive": {"shape": "<PERSON>", "documentation": "<p>The upper inclusive for the coverage number.&gt;</p>"}}, "documentation": "<p>The coverage number to be used in the filter.</p>"}, "CoverageNumberFilterList": {"type": "list", "member": {"shape": "CoverageNumberFilter"}, "max": 10, "min": 1}, "CoverageResourceType": {"type": "string", "enum": ["AWS_EC2_INSTANCE", "AWS_ECR_CONTAINER_IMAGE", "AWS_ECR_REPOSITORY", "AWS_LAMBDA_FUNCTION", "CODE_REPOSITORY"]}, "CoverageStringComparison": {"type": "string", "enum": ["EQUALS", "NOT_EQUALS"]}, "CoverageStringFilter": {"type": "structure", "required": ["comparison", "value"], "members": {"comparison": {"shape": "CoverageStringComparison", "documentation": "<p>The operator to compare strings on.</p>"}, "value": {"shape": "CoverageStringInput", "documentation": "<p>The value to compare strings on.</p>"}}, "documentation": "<p>Contains details of a coverage string filter.</p>"}, "CoverageStringFilterList": {"type": "list", "member": {"shape": "CoverageStringFilter"}, "max": 10, "min": 1}, "CoverageStringInput": {"type": "string", "max": 1024, "min": 1}, "CoveredResource": {"type": "structure", "required": ["accountId", "resourceId", "resourceType", "scanType"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the covered resource.</p>"}, "lastScannedAt": {"shape": "DateTimeTimestamp", "documentation": "<p>The date and time the resource was last checked for vulnerabilities.</p>"}, "resourceId": {"shape": "ResourceId", "documentation": "<p>The ID of the covered resource.</p>"}, "resourceMetadata": {"shape": "ResourceScanMetadata", "documentation": "<p>An object that contains details about the metadata.</p>"}, "resourceType": {"shape": "CoverageResourceType", "documentation": "<p>The type of the covered resource.</p>"}, "scanMode": {"shape": "ScanMode", "documentation": "<p>The scan method that is applied to the instance.</p>"}, "scanStatus": {"shape": "ScanStatus", "documentation": "<p>The status of the scan covering the resource.</p>"}, "scanType": {"shape": "ScanType", "documentation": "<p>The Amazon Inspector scan type covering the resource.</p>"}}, "documentation": "<p>An object that contains details about a resource covered by Amazon Inspector.</p>"}, "CoveredResources": {"type": "list", "member": {"shape": "CoveredResource"}}, "CreateCisScanConfigurationRequest": {"type": "structure", "required": ["scanName", "schedule", "securityLevel", "targets"], "members": {"scanName": {"shape": "CisScanName", "documentation": "<p>The scan name for the CIS scan configuration.</p>"}, "schedule": {"shape": "Schedule", "documentation": "<p>The schedule for the CIS scan configuration.</p>"}, "securityLevel": {"shape": "CisSecurityLevel", "documentation": "<p> The security level for the CIS scan configuration. Security level refers to the Benchmark levels that CIS assigns to a profile. </p>"}, "tags": {"shape": "CisTagMap", "documentation": "<p>The tags for the CIS scan configuration.</p>"}, "targets": {"shape": "CreateCisTargets", "documentation": "<p>The targets for the CIS scan configuration.</p>"}}}, "CreateCisScanConfigurationResponse": {"type": "structure", "members": {"scanConfigurationArn": {"shape": "CisScanConfigurationArn", "documentation": "<p>The scan configuration ARN for the CIS scan configuration.</p>"}}}, "CreateCisTargets": {"type": "structure", "required": ["accountIds", "targetResourceTags"], "members": {"accountIds": {"shape": "TargetAccountList", "documentation": "<p>The CIS target account ids.</p>"}, "targetResourceTags": {"shape": "TargetResourceTags", "documentation": "<p>The CIS target resource tags.</p>"}}, "documentation": "<p>Creates CIS targets.</p>"}, "CreateCodeSecurityIntegrationRequest": {"type": "structure", "required": ["name", "type"], "members": {"details": {"shape": "CreateIntegrationDetail", "documentation": "<p>The integration details specific to the repository provider type.</p>"}, "name": {"shape": "IntegrationName", "documentation": "<p>The name of the code security integration.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags to apply to the code security integration.</p>"}, "type": {"shape": "IntegrationType", "documentation": "<p>The type of repository provider for the integration.</p>"}}}, "CreateCodeSecurityIntegrationResponse": {"type": "structure", "required": ["integrationArn", "status"], "members": {"authorizationUrl": {"shape": "AuthorizationUrl", "documentation": "<p>The URL used to authorize the integration with the repository provider.</p>"}, "integrationArn": {"shape": "CodeSecurityIntegrationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the created code security integration.</p>"}, "status": {"shape": "IntegrationStatus", "documentation": "<p>The current status of the code security integration.</p>"}}}, "CreateCodeSecurityScanConfigurationRequest": {"type": "structure", "required": ["configuration", "level", "name"], "members": {"configuration": {"shape": "CodeSecurityScanConfiguration", "documentation": "<p>The configuration settings for the code security scan.</p>"}, "level": {"shape": "ConfigurationLevel", "documentation": "<p>The security level for the scan configuration.</p>"}, "name": {"shape": "ScanConfigurationName", "documentation": "<p>The name of the scan configuration.</p>"}, "scopeSettings": {"shape": "ScopeSettings", "documentation": "<p>The scope settings that define which repositories will be scanned. Include this parameter to create a default scan configuration. Otherwise Amazon Inspector creates a general scan configuration. </p> <p>A default scan configuration automatically applies to all existing and future projects imported into Amazon Inspector. Use the <code>BatchAssociateCodeSecurityScanConfiguration</code> operation to associate a general scan configuration with projects.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags to apply to the scan configuration.</p>"}}}, "CreateCodeSecurityScanConfigurationResponse": {"type": "structure", "required": ["scanConfigurationArn"], "members": {"scanConfigurationArn": {"shape": "ScanConfigurationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the created scan configuration.</p>"}}}, "CreateFilterRequest": {"type": "structure", "required": ["action", "filterCriteria", "name"], "members": {"action": {"shape": "FilterAction", "documentation": "<p>Defines the action that is to be applied to the findings that match the filter.</p>"}, "description": {"shape": "FilterDescription", "documentation": "<p>A description of the filter.</p>"}, "filterCriteria": {"shape": "FilterCriteria", "documentation": "<p>Defines the criteria to be used in the filter for querying findings.</p>"}, "name": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The name of the filter. Minimum length of 3. Maximum length of 64. Valid characters include alphanumeric characters, dot (.), underscore (_), and dash (-). Spaces are not allowed.</p>"}, "reason": {"shape": "FilterReason", "documentation": "<p>The reason for creating the filter.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>A list of tags for the filter.</p>"}}}, "CreateFilterResponse": {"type": "structure", "required": ["arn"], "members": {"arn": {"shape": "FilterArn", "documentation": "<p>The Amazon Resource Number (ARN) of the successfully created filter.</p>"}}}, "CreateFindingsReportRequest": {"type": "structure", "required": ["reportFormat", "s3Destination"], "members": {"filterCriteria": {"shape": "FilterCriteria", "documentation": "<p>The filter criteria to apply to the results of the finding report.</p>"}, "reportFormat": {"shape": "ReportFormat", "documentation": "<p>The format to generate the report in.</p>"}, "s3Destination": {"shape": "Destination", "documentation": "<p>The Amazon S3 export destination for the report.</p>"}}}, "CreateFindingsReportResponse": {"type": "structure", "members": {"reportId": {"shape": "ReportId", "documentation": "<p>The ID of the report.</p>"}}}, "CreateGitLabSelfManagedIntegrationDetail": {"type": "structure", "required": ["accessToken", "instanceUrl"], "members": {"accessToken": {"shape": "GitLabAccessToken", "documentation": "<p>The personal access token used to authenticate with the self-managed GitLab instance.</p>"}, "instanceUrl": {"shape": "InstanceUrl", "documentation": "<p>The URL of the self-managed GitLab instance.</p>"}}, "documentation": "<p>Contains details required to create an integration with a self-managed GitLab instance.</p>"}, "CreateIntegrationDetail": {"type": "structure", "members": {"gitlabSelfManaged": {"shape": "CreateGitLabSelfManagedIntegrationDetail", "documentation": "<p>Details specific to creating an integration with a self-managed GitLab instance.</p>"}}, "documentation": "<p>Contains details required to create a code security integration with a specific repository provider.</p>", "union": true}, "CreateSbomExportRequest": {"type": "structure", "required": ["reportFormat", "s3Destination"], "members": {"reportFormat": {"shape": "SbomReportFormat", "documentation": "<p>The output format for the software bill of materials (SBOM) report.</p>"}, "resourceFilterCriteria": {"shape": "ResourceFilterCriteria", "documentation": "<p>The resource filter criteria for the software bill of materials (SBOM) report.</p>"}, "s3Destination": {"shape": "Destination", "documentation": "<p>Contains details of the Amazon S3 bucket and KMS key used to export findings.</p>"}}}, "CreateSbomExportResponse": {"type": "structure", "members": {"reportId": {"shape": "ReportId", "documentation": "<p>The report ID for the software bill of materials (SBOM) report.</p>"}}}, "Currency": {"type": "string", "enum": ["USD"]}, "Cvss2": {"type": "structure", "members": {"baseScore": {"shape": "Cvss2BaseScore", "documentation": "<p>The CVSS v2 base score for the vulnerability.</p>"}, "scoringVector": {"shape": "Cvss2ScoringVector", "documentation": "<p>The scoring vector associated with the CVSS v2 score.</p>"}}, "documentation": "<p>The Common Vulnerability Scoring System (CVSS) version 2 details for the vulnerability.</p>"}, "Cvss2BaseScore": {"type": "double"}, "Cvss2ScoringVector": {"type": "string", "min": 0}, "Cvss3": {"type": "structure", "members": {"baseScore": {"shape": "Cvss3BaseScore", "documentation": "<p>The CVSS v3 base score for the vulnerability.</p>"}, "scoringVector": {"shape": "Cvss3ScoringVector", "documentation": "<p>The scoring vector associated with the CVSS v3 score.</p>"}}, "documentation": "<p>The Common Vulnerability Scoring System (CVSS) version 3 details for the vulnerability.</p>"}, "Cvss3BaseScore": {"type": "double"}, "Cvss3ScoringVector": {"type": "string", "min": 0}, "CvssScore": {"type": "structure", "required": ["baseScore", "scoringVector", "source", "version"], "members": {"baseScore": {"shape": "Double", "documentation": "<p>The base CVSS score used for the finding.</p>"}, "scoringVector": {"shape": "NonEmptyString", "documentation": "<p>The vector string of the CVSS score.</p>"}, "source": {"shape": "NonEmptyString", "documentation": "<p>The source of the CVSS score.</p>"}, "version": {"shape": "NonEmptyString", "documentation": "<p>The version of CVSS used for the score.</p>"}}, "documentation": "<p>The CVSS score for a finding.</p>"}, "CvssScoreAdjustment": {"type": "structure", "required": ["metric", "reason"], "members": {"metric": {"shape": "NonEmptyString", "documentation": "<p>The metric used to adjust the CVSS score.</p>"}, "reason": {"shape": "NonEmptyString", "documentation": "<p>The reason the CVSS score has been adjustment.</p>"}}, "documentation": "<p>Details on adjustments Amazon Inspector made to the CVSS score for a finding.</p>"}, "CvssScoreAdjustmentList": {"type": "list", "member": {"shape": "CvssScoreAdjustment"}}, "CvssScoreDetails": {"type": "structure", "required": ["score", "scoreSource", "scoringVector", "version"], "members": {"adjustments": {"shape": "CvssScoreAdjustmentList", "documentation": "<p>An object that contains details about adjustment Amazon Inspector made to the CVSS score.</p>"}, "cvssSource": {"shape": "NonEmptyString", "documentation": "<p>The source of the CVSS data.</p>"}, "score": {"shape": "Double", "documentation": "<p>The CVSS score.</p>"}, "scoreSource": {"shape": "NonEmptyString", "documentation": "<p>The source for the CVSS score.</p>"}, "scoringVector": {"shape": "NonEmptyString", "documentation": "<p>The vector for the CVSS score.</p>"}, "version": {"shape": "NonEmptyString", "documentation": "<p>The CVSS version used in scoring.</p>"}}, "documentation": "<p>Information about the CVSS score.</p>"}, "CvssScoreList": {"type": "list", "member": {"shape": "CvssScore"}}, "Cwe": {"type": "string", "min": 0}, "CweList": {"type": "list", "member": {"shape": "NonEmptyString"}, "max": 10, "min": 1}, "Cwes": {"type": "list", "member": {"shape": "Cwe"}, "min": 0}, "DailySchedule": {"type": "structure", "required": ["startTime"], "members": {"startTime": {"shape": "Time", "documentation": "<p>The schedule start time.</p>"}}, "documentation": "<p>A daily schedule.</p>"}, "DateFilter": {"type": "structure", "members": {"endInclusive": {"shape": "Timestamp", "documentation": "<p>A timestamp representing the end of the time period filtered on.</p>"}, "startInclusive": {"shape": "Timestamp", "documentation": "<p>A timestamp representing the start of the time period filtered on.</p>"}}, "documentation": "<p>Contains details on the time range used to filter findings.</p>"}, "DateFilterList": {"type": "list", "member": {"shape": "DateFilter"}, "max": 10, "min": 1}, "DateTimeTimestamp": {"type": "timestamp"}, "Day": {"type": "string", "enum": ["SUN", "MON", "TUE", "WED", "THU", "FRI", "SAT"]}, "DaysList": {"type": "list", "member": {"shape": "Day"}, "max": 7, "min": 1}, "DelegatedAdmin": {"type": "structure", "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the Amazon Inspector delegated administrator for your organization.</p>"}, "relationshipStatus": {"shape": "RelationshipStatus", "documentation": "<p>The status of the Amazon Inspector delegated administrator.</p>"}}, "documentation": "<p>Details of the Amazon Inspector delegated administrator for your organization.</p>"}, "DelegatedAdminAccount": {"type": "structure", "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the Amazon Inspector delegated administrator for your organization.</p>"}, "status": {"shape": "DelegatedAdminStatus", "documentation": "<p>The status of the Amazon Inspector delegated administrator.</p>"}}, "documentation": "<p>Details of the Amazon Inspector delegated administrator for your organization.</p>"}, "DelegatedAdminAccountList": {"type": "list", "member": {"shape": "DelegatedAdminAccount"}, "max": 5, "min": 0}, "DelegatedAdminStatus": {"type": "string", "enum": ["ENABLED", "DISABLE_IN_PROGRESS"]}, "DeleteCisScanConfigurationRequest": {"type": "structure", "required": ["scanConfigurationArn"], "members": {"scanConfigurationArn": {"shape": "CisScanConfigurationArn", "documentation": "<p>The ARN of the CIS scan configuration.</p>"}}}, "DeleteCisScanConfigurationResponse": {"type": "structure", "required": ["scanConfigurationArn"], "members": {"scanConfigurationArn": {"shape": "CisScanConfigurationArn", "documentation": "<p>The ARN of the CIS scan configuration.</p>"}}}, "DeleteCodeSecurityIntegrationRequest": {"type": "structure", "required": ["integrationArn"], "members": {"integrationArn": {"shape": "CodeSecurityIntegrationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the code security integration to delete.</p>"}}}, "DeleteCodeSecurityIntegrationResponse": {"type": "structure", "members": {"integrationArn": {"shape": "CodeSecurityIntegrationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the deleted code security integration.</p>"}}}, "DeleteCodeSecurityScanConfigurationRequest": {"type": "structure", "required": ["scanConfigurationArn"], "members": {"scanConfigurationArn": {"shape": "ScanConfigurationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the scan configuration to delete.</p>"}}}, "DeleteCodeSecurityScanConfigurationResponse": {"type": "structure", "members": {"scanConfigurationArn": {"shape": "ScanConfigurationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the deleted scan configuration.</p>"}}}, "DeleteFilterRequest": {"type": "structure", "required": ["arn"], "members": {"arn": {"shape": "FilterArn", "documentation": "<p>The Amazon Resource Number (ARN) of the filter to be deleted.</p>"}}}, "DeleteFilterResponse": {"type": "structure", "required": ["arn"], "members": {"arn": {"shape": "FilterArn", "documentation": "<p>The Amazon Resource Number (ARN) of the filter that has been deleted.</p>"}}}, "DescribeOrganizationConfigurationRequest": {"type": "structure", "members": {}}, "DescribeOrganizationConfigurationResponse": {"type": "structure", "members": {"autoEnable": {"shape": "AutoEnable", "documentation": "<p>The scan types are automatically enabled for new members of your organization.</p>"}, "maxAccountLimitReached": {"shape": "Boolean", "documentation": "<p>Represents whether your organization has reached the maximum Amazon Web Services account limit for Amazon Inspector.</p>"}}}, "Destination": {"type": "structure", "required": ["bucketName", "kmsKeyArn"], "members": {"bucketName": {"shape": "String", "documentation": "<p>The name of the Amazon S3 bucket to export findings to.</p>"}, "keyPrefix": {"shape": "String", "documentation": "<p>The prefix that the findings will be written under.</p>"}, "kmsKeyArn": {"shape": "String", "documentation": "<p>The ARN of the KMS key used to encrypt data when exporting findings.</p>"}}, "documentation": "<p>Contains details of the Amazon S3 bucket and KMS key used to export findings.</p>"}, "DetectionPlatforms": {"type": "list", "member": {"shape": "NonEmptyString"}, "max": 100, "min": 0}, "DetectorTagList": {"type": "list", "member": {"shape": "NonEmptyString"}, "max": 10, "min": 1}, "DisableDelegatedAdminAccountRequest": {"type": "structure", "required": ["delegatedAdminAccountId"], "members": {"delegatedAdminAccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the current Amazon Inspector delegated administrator.</p>"}}}, "DisableDelegatedAdminAccountResponse": {"type": "structure", "required": ["delegatedAdminAccountId"], "members": {"delegatedAdminAccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the successfully disabled delegated administrator.</p>"}}}, "DisableRequest": {"type": "structure", "members": {"accountIds": {"shape": "AccountIdSet", "documentation": "<p>An array of account IDs you want to disable Amazon Inspector scans for.</p>"}, "resourceTypes": {"shape": "DisableResourceTypeList", "documentation": "<p>The resource scan types you want to disable.</p>"}}}, "DisableResourceTypeList": {"type": "list", "member": {"shape": "ResourceScanType"}, "max": 3, "min": 0}, "DisableResponse": {"type": "structure", "required": ["accounts"], "members": {"accounts": {"shape": "AccountList", "documentation": "<p>Information on the accounts that have had Amazon Inspector scans successfully disabled. Details are provided for each account.</p>"}, "failedAccounts": {"shape": "FailedAccountList", "documentation": "<p>Information on any accounts for which Amazon Inspector scans could not be disabled. Details are provided for each account.</p>"}}}, "DisassociateConfigurationRequest": {"type": "structure", "required": ["resource", "scanConfigurationArn"], "members": {"resource": {"shape": "CodeSecurityResource"}, "scanConfigurationArn": {"shape": "ScanConfigurationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the scan configuration to disassociate from a code repository.</p>"}}, "documentation": "<p>Contains details about a request to disassociate a code repository from a scan configuration.</p>"}, "DisassociateConfigurationRequestList": {"type": "list", "member": {"shape": "DisassociateConfigurationRequest"}, "max": 25, "min": 1}, "DisassociateMemberRequest": {"type": "structure", "required": ["accountId"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the member account to disassociate.</p>"}}}, "DisassociateMemberResponse": {"type": "structure", "required": ["accountId"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the successfully disassociated member.</p>"}}}, "Double": {"type": "double", "box": true}, "Ec2Configuration": {"type": "structure", "required": ["scanMode"], "members": {"scanMode": {"shape": "Ec2ScanMode", "documentation": "<p>The scan method that is applied to the instance.</p>"}}, "documentation": "<p>Enables agent-based scanning, which scans instances that are not managed by SSM.</p>"}, "Ec2ConfigurationState": {"type": "structure", "members": {"scanModeState": {"shape": "Ec2ScanModeState", "documentation": "<p>An object that contains details about the state of the Amazon EC2 scan mode.</p>"}}, "documentation": "<p>Details about the state of the EC2 scan configuration for your environment.</p>"}, "Ec2DeepInspectionStatus": {"type": "string", "enum": ["ACTIVATED", "DEACTIVATED", "PENDING", "FAILED"]}, "Ec2InstanceAggregation": {"type": "structure", "members": {"amis": {"shape": "StringFilterList", "documentation": "<p>The AMI IDs associated with the Amazon EC2 instances to aggregate findings for.</p>"}, "instanceIds": {"shape": "StringFilterList", "documentation": "<p>The Amazon EC2 instance IDs to aggregate findings for.</p>"}, "instanceTags": {"shape": "MapFilterList", "documentation": "<p>The Amazon EC2 instance tags to aggregate findings for.</p>"}, "operatingSystems": {"shape": "StringFilterList", "documentation": "<p>The operating system types to aggregate findings for. Valid values must be uppercase and underscore separated, examples are <code>ORACLE_LINUX_7</code> and <code>ALPINE_LINUX_3_8</code>.</p>"}, "sortBy": {"shape": "Ec2InstanceSortBy", "documentation": "<p>The value to sort results by.</p>"}, "sortOrder": {"shape": "SortOrder", "documentation": "<p>The order to sort results by.</p>"}}, "documentation": "<p>The details that define an aggregation based on Amazon EC2 instances.</p>"}, "Ec2InstanceAggregationResponse": {"type": "structure", "required": ["instanceId"], "members": {"accountId": {"shape": "String", "documentation": "<p>The Amazon Web Services account for the Amazon EC2 instance.</p>"}, "ami": {"shape": "AmiId", "documentation": "<p>The Amazon Machine Image (AMI) of the Amazon EC2 instance.</p>"}, "instanceId": {"shape": "NonEmptyString", "documentation": "<p>The Amazon EC2 instance ID.</p>"}, "instanceTags": {"shape": "TagMap", "documentation": "<p>The tags attached to the instance.</p>"}, "networkFindings": {"shape": "<PERSON>", "documentation": "<p>The number of network findings for the Amazon EC2 instance.</p>"}, "operatingSystem": {"shape": "String", "documentation": "<p>The operating system of the Amazon EC2 instance.</p>"}, "severityCounts": {"shape": "SeverityCounts", "documentation": "<p>An object that contains the count of matched findings per severity.</p>"}}, "documentation": "<p>A response that contains the results of a finding aggregation by Amazon EC2 instance.</p>"}, "Ec2InstanceSortBy": {"type": "string", "enum": ["NETWORK_FINDINGS", "CRITICAL", "HIGH", "ALL"]}, "Ec2Metadata": {"type": "structure", "members": {"amiId": {"shape": "AmiId", "documentation": "<p>The ID of the Amazon Machine Image (AMI) used to launch the instance.</p>"}, "platform": {"shape": "Ec2Platform", "documentation": "<p>The platform of the instance.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags attached to the instance.</p>"}}, "documentation": "<p>Meta data details of an Amazon EC2 instance.</p>"}, "Ec2Platform": {"type": "string", "enum": ["WINDOWS", "LINUX", "UNKNOWN", "MACOS"]}, "Ec2ScanMode": {"type": "string", "enum": ["EC2_SSM_AGENT_BASED", "EC2_HYBRID"]}, "Ec2ScanModeState": {"type": "structure", "members": {"scanMode": {"shape": "Ec2ScanMode", "documentation": "<p>The scan method that is applied to the instance.</p>"}, "scanModeStatus": {"shape": "Ec2ScanModeStatus", "documentation": "<p>The status of the Amazon EC2 scan mode setting.</p>"}}, "documentation": "<p>The state of your Amazon EC2 scan mode configuration.</p>"}, "Ec2ScanModeStatus": {"type": "string", "enum": ["SUCCESS", "PENDING"]}, "EcrConfiguration": {"type": "structure", "required": ["rescanDuration"], "members": {"pullDateRescanDuration": {"shape": "EcrPullDateRescanDuration", "documentation": "<p>The rescan duration configured for image pull date.</p>"}, "pullDateRescanMode": {"shape": "EcrPullDateRescanMode", "documentation": "<p>The pull date for the re-scan mode.</p>"}, "rescanDuration": {"shape": "EcrRescanDuration", "documentation": "<p>The rescan duration configured for image push date.</p>"}}, "documentation": "<p>Details about the ECR automated re-scan duration setting for your environment.</p>"}, "EcrConfigurationState": {"type": "structure", "members": {"rescanDurationState": {"shape": "EcrRescanDurationState", "documentation": "<p>An object that contains details about the state of the ECR re-scan settings.</p>"}}, "documentation": "<p>Details about the state of the ECR scans for your environment.</p>"}, "EcrContainerImageMetadata": {"type": "structure", "members": {"imagePulledAt": {"shape": "DateTimeTimestamp", "documentation": "<p>The date an image was last pulled at.</p>"}, "inUseCount": {"shape": "<PERSON>", "documentation": "<p>The number of Amazon ECS tasks or Amazon EKS pods where the Amazon ECR container image is in use.</p>"}, "lastInUseAt": {"shape": "DateTimeTimestamp", "documentation": "<p>The last time an Amazon ECR image was used in an Amazon ECS task or Amazon EKS pod.</p>"}, "tags": {"shape": "TagList", "documentation": "<p>Tags associated with the Amazon ECR image metadata.</p>"}}, "documentation": "<p>Information on the Amazon ECR image metadata associated with a finding.</p>"}, "EcrPullDateRescanDuration": {"type": "string", "enum": ["DAYS_14", "DAYS_30", "DAYS_60", "DAYS_90", "DAYS_180"]}, "EcrPullDateRescanMode": {"type": "string", "enum": ["LAST_PULL_DATE", "LAST_IN_USE_AT"]}, "EcrRepositoryMetadata": {"type": "structure", "members": {"name": {"shape": "String", "documentation": "<p>The name of the Amazon ECR repository.</p>"}, "scanFrequency": {"shape": "EcrScanFrequency", "documentation": "<p>The frequency of scans.</p>"}}, "documentation": "<p>Information on the Amazon ECR repository metadata associated with a finding.</p>"}, "EcrRescanDuration": {"type": "string", "enum": ["LIFETIME", "DAYS_30", "DAYS_180", "DAYS_14", "DAYS_60", "DAYS_90"]}, "EcrRescanDurationState": {"type": "structure", "members": {"pullDateRescanDuration": {"shape": "EcrPullDateRescanDuration", "documentation": "<p>The rescan duration configured for image pull date.</p>"}, "pullDateRescanMode": {"shape": "EcrPullDateRescanMode", "documentation": "<p>The pull date for the re-scan mode.</p>"}, "rescanDuration": {"shape": "EcrRescanDuration", "documentation": "<p>The rescan duration configured for image push date. </p>"}, "status": {"shape": "EcrRescanDurationStatus", "documentation": "<p>The status of changes to the ECR automated re-scan duration.</p>"}, "updatedAt": {"shape": "DateTimeTimestamp", "documentation": "<p>A timestamp representing when the last time the ECR scan duration setting was changed.</p>"}}, "documentation": "<p>Details about the state of your ECR re-scan duration settings. The ECR re-scan duration defines how long an ECR image will be actively scanned by Amazon Inspector. When the number of days since an image was last pushed exceeds the duration configured for image pull date, and the duration configured for image pull date, the monitoring state of that image becomes <code>inactive</code> and all associated findings are scheduled for closure.</p>"}, "EcrRescanDurationStatus": {"type": "string", "enum": ["SUCCESS", "PENDING", "FAILED"]}, "EcrScanFrequency": {"type": "string", "enum": ["MANUAL", "SCAN_ON_PUSH", "CONTINUOUS_SCAN"]}, "EnableDelegatedAdminAccountRequest": {"type": "structure", "required": ["delegatedAdminAccountId"], "members": {"clientToken": {"shape": "ClientToken", "documentation": "<p>The idempotency token for the request.</p>", "idempotencyToken": true}, "delegatedAdminAccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the Amazon Inspector delegated administrator.</p>"}}}, "EnableDelegatedAdminAccountResponse": {"type": "structure", "required": ["delegatedAdminAccountId"], "members": {"delegatedAdminAccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the successfully Amazon Inspector delegated administrator.</p>"}}}, "EnableRequest": {"type": "structure", "required": ["resourceTypes"], "members": {"accountIds": {"shape": "AccountIdSet", "documentation": "<p>A list of account IDs you want to enable Amazon Inspector scans for.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>The idempotency token for the request.</p>", "idempotencyToken": true}, "resourceTypes": {"shape": "EnableResourceTypeList", "documentation": "<p>The resource scan types you want to enable.</p>"}}}, "EnableResourceTypeList": {"type": "list", "member": {"shape": "ResourceScanType"}, "max": 3, "min": 1}, "EnableResponse": {"type": "structure", "required": ["accounts"], "members": {"accounts": {"shape": "AccountList", "documentation": "<p>Information on the accounts that have had Amazon Inspector scans successfully enabled. Details are provided for each account.</p>"}, "failedAccounts": {"shape": "FailedAccountList", "documentation": "<p>Information on any accounts for which Amazon Inspector scans could not be enabled. Details are provided for each account.</p>"}}}, "Epss": {"type": "structure", "members": {"score": {"shape": "EpssScore", "documentation": "<p>The Exploit Prediction Scoring System (EPSS) score.</p>"}}, "documentation": "<p>Details about the Exploit Prediction Scoring System (EPSS) score.</p>"}, "EpssDetails": {"type": "structure", "members": {"score": {"shape": "EpssScoreValue", "documentation": "<p>The EPSS score.</p>"}}, "documentation": "<p>Details about the Exploit Prediction Scoring System (EPSS) score for a finding.</p>"}, "EpssScore": {"type": "double"}, "EpssScoreValue": {"type": "double", "max": 1.0, "min": 0.0}, "ErrorCode": {"type": "string", "enum": ["ALREADY_ENABLED", "ENABLE_IN_PROGRESS", "DISABLE_IN_PROGRESS", "SUSPEND_IN_PROGRESS", "RESOURCE_NOT_FOUND", "ACCESS_DENIED", "INTERNAL_ERROR", "SSM_UNAVAILABLE", "SSM_THROTTLED", "EVENTBRIDGE_UNAVAILABLE", "EVENTBRIDGE_THROTTLED", "RESOURCE_SCAN_NOT_DISABLED", "DISASSOCIATE_ALL_MEMBERS", "ACCOUNT_IS_ISOLATED", "EC2_SSM_RESOURCE_DATA_SYNC_LIMIT_EXCEEDED", "EC2_SSM_ASSOCIATION_VERSION_LIMIT_EXCEEDED"]}, "ErrorMessage": {"type": "string"}, "Evidence": {"type": "structure", "members": {"evidenceDetail": {"shape": "EvidenceDetail", "documentation": "<p>The evidence details.</p>"}, "evidenceRule": {"shape": "EvidenceRule", "documentation": "<p>The evidence rule.</p>"}, "severity": {"shape": "EvidenceSeverity", "documentation": "<p>The evidence severity.</p>"}}, "documentation": "<p>Details of the evidence for a vulnerability identified in a finding.</p>"}, "EvidenceDetail": {"type": "string", "min": 0}, "EvidenceList": {"type": "list", "member": {"shape": "Evidence"}}, "EvidenceRule": {"type": "string", "min": 0}, "EvidenceSeverity": {"type": "string", "min": 0}, "ExecutionRoleArn": {"type": "string", "pattern": "^arn:(aws[a-zA-Z-]*)?:iam::\\d{12}:role/?[a-zA-Z_0-9+=,.@\\-_/]+$"}, "ExploitAvailable": {"type": "string", "enum": ["YES", "NO"]}, "ExploitObserved": {"type": "structure", "members": {"firstSeen": {"shape": "FirstSeen", "documentation": "<p>The date an time when the exploit was first seen.</p>"}, "lastSeen": {"shape": "LastSeen", "documentation": "<p>The date an time when the exploit was last seen.</p>"}}, "documentation": "<p>Contains information on when this exploit was observed.</p>"}, "ExploitabilityDetails": {"type": "structure", "members": {"lastKnownExploitAt": {"shape": "DateTimeTimestamp", "documentation": "<p>The date and time of the last exploit associated with a finding discovered in your environment.</p>"}}, "documentation": "<p>The details of an exploit available for a finding discovered in your environment.</p>"}, "ExternalReportStatus": {"type": "string", "enum": ["SUCCEEDED", "IN_PROGRESS", "CANCELLED", "FAILED"]}, "FailedAccount": {"type": "structure", "required": ["accountId", "errorCode", "errorMessage"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID.</p>"}, "errorCode": {"shape": "ErrorCode", "documentation": "<p>The error code explaining why the account failed to enable Amazon Inspector.</p>"}, "errorMessage": {"shape": "NonEmptyString", "documentation": "<p>The error message received when the account failed to enable Amazon Inspector.</p>"}, "resourceStatus": {"shape": "ResourceStatus", "documentation": "<p>An object detailing which resources Amazon Inspector is enabled to scan for the account.</p>"}, "status": {"shape": "Status", "documentation": "<p>The status of Amazon Inspector for the account.</p>"}}, "documentation": "<p>An object with details on why an account failed to enable Amazon Inspector.</p>"}, "FailedAccountList": {"type": "list", "member": {"shape": "FailedAccount"}, "max": 100, "min": 0}, "FailedAssociationResult": {"type": "structure", "members": {"resource": {"shape": "CodeSecurityResource"}, "scanConfigurationArn": {"shape": "ScanConfigurationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the scan configuration that failed to be associated or disassociated.</p>"}, "statusCode": {"shape": "AssociationResultStatusCode", "documentation": "<p>The status code indicating why the association or disassociation failed.</p>"}, "statusMessage": {"shape": "AssociationResultStatusMessage", "documentation": "<p>A message explaining why the association or disassociation failed.</p>"}}, "documentation": "<p>Details about a failed attempt to associate or disassociate a code repository with a scan configuration.</p>"}, "FailedAssociationResultList": {"type": "list", "member": {"shape": "FailedAssociationResult"}}, "FailedMemberAccountEc2DeepInspectionStatusState": {"type": "structure", "required": ["accountId"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The unique identifier for the Amazon Web Services account of the organization member that failed to activate Amazon Inspector deep inspection.</p>"}, "ec2ScanStatus": {"shape": "Status", "documentation": "<p>The status of EC2 scanning in the account that failed to activate Amazon Inspector deep inspection.</p>"}, "errorMessage": {"shape": "NonEmptyString", "documentation": "<p>The error message explaining why the account failed to activate Amazon Inspector deep inspection.</p>"}}, "documentation": "<p>An object that contains details about a member account in your organization that failed to activate Amazon Inspector deep inspection.</p>"}, "FailedMemberAccountEc2DeepInspectionStatusStateList": {"type": "list", "member": {"shape": "FailedMemberAccountEc2DeepInspectionStatusState"}, "max": 100, "min": 0}, "FilePath": {"type": "string", "max": 1024, "min": 1}, "Filter": {"type": "structure", "required": ["action", "arn", "createdAt", "criteria", "name", "ownerId", "updatedAt"], "members": {"action": {"shape": "FilterAction", "documentation": "<p>The action that is to be applied to the findings that match the filter.</p>"}, "arn": {"shape": "FilterArn", "documentation": "<p>The Amazon Resource Number (ARN) associated with this filter.</p>"}, "createdAt": {"shape": "DateTimeTimestamp", "documentation": "<p>The date and time this filter was created at.</p>"}, "criteria": {"shape": "FilterCriteria", "documentation": "<p>Details on the filter criteria associated with this filter.</p>"}, "description": {"shape": "FilterDescription", "documentation": "<p>A description of the filter.</p>"}, "name": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The name of the filter.</p>"}, "ownerId": {"shape": "OwnerId", "documentation": "<p>The Amazon Web Services account ID of the account that created the filter.</p>"}, "reason": {"shape": "FilterReason", "documentation": "<p>The reason for the filter.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags attached to the filter.</p>"}, "updatedAt": {"shape": "DateTimeTimestamp", "documentation": "<p>The date and time the filter was last updated at.</p>"}}, "documentation": "<p>Details about a filter.</p>"}, "FilterAction": {"type": "string", "enum": ["NONE", "SUPPRESS"]}, "FilterArn": {"type": "string", "max": 128, "min": 1}, "FilterArnList": {"type": "list", "member": {"shape": "FilterArn"}}, "FilterCriteria": {"type": "structure", "members": {"awsAccountId": {"shape": "StringFilterList", "documentation": "<p>Details of the Amazon Web Services account IDs used to filter findings.</p>"}, "codeRepositoryProjectName": {"shape": "StringFilterList", "documentation": "<p>Filter criteria for findings based on the project name in a code repository.</p>"}, "codeRepositoryProviderType": {"shape": "StringFilterList", "documentation": "<p>Filter criteria for findings based on the repository provider type (such as GitHub, GitLab, etc.).</p>"}, "codeVulnerabilityDetectorName": {"shape": "StringFilterList", "documentation": "<p>The name of the detector used to identify a code vulnerability in a Lambda function used to filter findings.</p>"}, "codeVulnerabilityDetectorTags": {"shape": "StringFilterList", "documentation": "<p>The detector type tag associated with the vulnerability used to filter findings. Detector tags group related vulnerabilities by common themes or tactics. For a list of available tags by programming language, see <a href=\"https://docs.aws.amazon.com/codeguru/detector-library/java/tags/\">Java tags</a>, or <a href=\"https://docs.aws.amazon.com/codeguru/detector-library/python/tags/\">Python tags</a>. </p>"}, "codeVulnerabilityFilePath": {"shape": "StringFilterList", "documentation": "<p>The file path to the file in a Lambda function that contains a code vulnerability used to filter findings.</p>"}, "componentId": {"shape": "StringFilterList", "documentation": "<p>Details of the component IDs used to filter findings.</p>"}, "componentType": {"shape": "StringFilterList", "documentation": "<p>Details of the component types used to filter findings.</p>"}, "ec2InstanceImageId": {"shape": "StringFilterList", "documentation": "<p>Details of the Amazon EC2 instance image IDs used to filter findings.</p>"}, "ec2InstanceSubnetId": {"shape": "StringFilterList", "documentation": "<p>Details of the Amazon EC2 instance subnet IDs used to filter findings.</p>"}, "ec2InstanceVpcId": {"shape": "StringFilterList", "documentation": "<p>Details of the Amazon EC2 instance VPC IDs used to filter findings.</p>"}, "ecrImageArchitecture": {"shape": "StringFilterList", "documentation": "<p>Details of the Amazon ECR image architecture types used to filter findings.</p>"}, "ecrImageHash": {"shape": "StringFilterList", "documentation": "<p>Details of the Amazon ECR image hashes used to filter findings.</p>"}, "ecrImageInUseCount": {"shape": "NumberFilterList", "documentation": "<p>Filter criteria indicating when details for an Amazon ECR image include when an Amazon ECR image is in use.</p>"}, "ecrImageLastInUseAt": {"shape": "DateFilterList", "documentation": "<p>Filter criteria indicating when an Amazon ECR image was last used in an Amazon ECS cluster task or Amazon EKS cluster pod.</p>"}, "ecrImagePushedAt": {"shape": "DateFilterList", "documentation": "<p>Details on the Amazon ECR image push date and time used to filter findings.</p>"}, "ecrImageRegistry": {"shape": "StringFilterList", "documentation": "<p>Details on the Amazon ECR registry used to filter findings.</p>"}, "ecrImageRepositoryName": {"shape": "StringFilterList", "documentation": "<p>Details on the name of the Amazon ECR repository used to filter findings.</p>"}, "ecrImageTags": {"shape": "StringFilterList", "documentation": "<p>The tags attached to the Amazon ECR container image.</p>"}, "epssScore": {"shape": "NumberFilterList", "documentation": "<p>The EPSS score used to filter findings.</p>"}, "exploitAvailable": {"shape": "StringFilterList", "documentation": "<p>Filters the list of Amazon Web Services Lambda findings by the availability of exploits.</p>"}, "findingArn": {"shape": "StringFilterList", "documentation": "<p>Details on the finding ARNs used to filter findings.</p>"}, "findingStatus": {"shape": "StringFilterList", "documentation": "<p>Details on the finding status types used to filter findings.</p>"}, "findingType": {"shape": "StringFilterList", "documentation": "<p>Details on the finding types used to filter findings.</p>"}, "firstObservedAt": {"shape": "DateFilterList", "documentation": "<p>Details on the date and time a finding was first seen used to filter findings.</p>"}, "fixAvailable": {"shape": "StringFilterList", "documentation": "<p>Details on whether a fix is available through a version update. This value can be <code>YES</code>, <code>NO</code>, or <code>PARTIAL</code>. A <code>PARTIAL</code> fix means that some, but not all, of the packages identified in the finding have fixes available through updated versions.</p>"}, "inspectorScore": {"shape": "NumberFilterList", "documentation": "<p>The Amazon Inspector score to filter on.</p>"}, "lambdaFunctionExecutionRoleArn": {"shape": "StringFilterList", "documentation": "<p>Filters the list of Amazon Web Services Lambda functions by execution role.</p>"}, "lambdaFunctionLastModifiedAt": {"shape": "DateFilterList", "documentation": "<p>Filters the list of Amazon Web Services Lambda functions by the date and time that a user last updated the configuration, in <a href=\"https://www.iso.org/iso-8601-date-and-time-format.html\">ISO 8601 format</a> </p>"}, "lambdaFunctionLayers": {"shape": "StringFilterList", "documentation": "<p>Filters the list of Amazon Web Services Lambda functions by the function's <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/configuration-layers.html\"> layers</a>. A Lambda function can have up to five layers.</p>"}, "lambdaFunctionName": {"shape": "StringFilterList", "documentation": "<p>Filters the list of Amazon Web Services Lambda functions by the name of the function.</p>"}, "lambdaFunctionRuntime": {"shape": "StringFilterList", "documentation": "<p>Filters the list of Amazon Web Services Lambda functions by the runtime environment for the Lambda function.</p>"}, "lastObservedAt": {"shape": "DateFilterList", "documentation": "<p>Details on the date and time a finding was last seen used to filter findings.</p>"}, "networkProtocol": {"shape": "StringFilterList", "documentation": "<p>Details on network protocol used to filter findings.</p>"}, "portRange": {"shape": "PortRangeFilterList", "documentation": "<p>Details on the port ranges used to filter findings.</p>"}, "relatedVulnerabilities": {"shape": "StringFilterList", "documentation": "<p>Details on the related vulnerabilities used to filter findings.</p>"}, "resourceId": {"shape": "StringFilterList", "documentation": "<p>Details on the resource IDs used to filter findings.</p>"}, "resourceTags": {"shape": "MapFilterList", "documentation": "<p>Details on the resource tags used to filter findings.</p>"}, "resourceType": {"shape": "StringFilterList", "documentation": "<p>Details on the resource types used to filter findings.</p>"}, "severity": {"shape": "StringFilterList", "documentation": "<p>Details on the severity used to filter findings.</p>"}, "title": {"shape": "StringFilterList", "documentation": "<p>Details on the finding title used to filter findings.</p>"}, "updatedAt": {"shape": "DateFilterList", "documentation": "<p>Details on the date and time a finding was last updated at used to filter findings.</p>"}, "vendorSeverity": {"shape": "StringFilterList", "documentation": "<p>Details on the vendor severity used to filter findings.</p>"}, "vulnerabilityId": {"shape": "StringFilterList", "documentation": "<p>Details on the vulnerability ID used to filter findings.</p>"}, "vulnerabilitySource": {"shape": "StringFilterList", "documentation": "<p>Details on the vulnerability type used to filter findings.</p>"}, "vulnerablePackages": {"shape": "PackageFilterList", "documentation": "<p>Details on the vulnerable packages used to filter findings.</p>"}}, "documentation": "<p>Details on the criteria used to define the filter.</p>"}, "FilterDescription": {"type": "string", "max": 512, "min": 1}, "FilterList": {"type": "list", "member": {"shape": "Filter"}}, "FilterName": {"type": "string", "max": 128, "min": 1}, "FilterReason": {"type": "string", "max": 512, "min": 1}, "Finding": {"type": "structure", "required": ["awsAccountId", "description", "findingArn", "firstObservedAt", "lastObservedAt", "remediation", "resources", "severity", "status", "type"], "members": {"awsAccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID associated with the finding.</p>"}, "codeVulnerabilityDetails": {"shape": "CodeVulnerabilityDetails", "documentation": "<p>Details about the code vulnerability identified in a Lambda function used to filter findings.</p>"}, "description": {"shape": "FindingDescription", "documentation": "<p>The description of the finding.</p>"}, "epss": {"shape": "EpssDetails", "documentation": "<p>The finding's EPSS score.</p>"}, "exploitAvailable": {"shape": "ExploitAvailable", "documentation": "<p>If a finding discovered in your environment has an exploit available.</p>"}, "exploitabilityDetails": {"shape": "ExploitabilityDetails", "documentation": "<p>The details of an exploit available for a finding discovered in your environment.</p>"}, "findingArn": {"shape": "FindingArn", "documentation": "<p>The Amazon Resource Number (ARN) of the finding.</p>"}, "firstObservedAt": {"shape": "DateTimeTimestamp", "documentation": "<p>The date and time that the finding was first observed.</p>"}, "fixAvailable": {"shape": "FixAvailable", "documentation": "<p>Details on whether a fix is available through a version update. This value can be <code>YES</code>, <code>NO</code>, or <code>PARTIAL</code>. A <code>PARTIAL</code> fix means that some, but not all, of the packages identified in the finding have fixes available through updated versions.</p>"}, "inspectorScore": {"shape": "Double", "documentation": "<p>The Amazon Inspector score given to the finding.</p>"}, "inspectorScoreDetails": {"shape": "InspectorScoreDetails", "documentation": "<p>An object that contains details of the Amazon Inspector score.</p>"}, "lastObservedAt": {"shape": "DateTimeTimestamp", "documentation": "<p> The date and time the finding was last observed. This timestamp for this field remains unchanged until a finding is updated. </p>"}, "networkReachabilityDetails": {"shape": "NetworkReachabilityDetails", "documentation": "<p>An object that contains the details of a network reachability finding.</p>"}, "packageVulnerabilityDetails": {"shape": "PackageVulnerabilityDetails", "documentation": "<p>An object that contains the details of a package vulnerability finding.</p>"}, "remediation": {"shape": "Remediation", "documentation": "<p>An object that contains the details about how to remediate a finding.</p>"}, "resources": {"shape": "ResourceList", "documentation": "<p>Contains information on the resources involved in a finding. The <code>resource</code> value determines the valid values for <code>type</code> in your request. For more information, see <a href=\"https://docs.aws.amazon.com/inspector/latest/user/findings-types.html\">Finding types</a> in the Amazon Inspector user guide.</p>"}, "severity": {"shape": "Severity", "documentation": "<p>The severity of the finding. <code>UNTRIAGED</code> applies to <code>PACKAGE_VULNERABILITY</code> type findings that the vendor has not assigned a severity yet. For more information, see <a href=\"https://docs.aws.amazon.com/inspector/latest/user/findings-understanding-severity.html\">Severity levels for findings</a> in the Amazon Inspector user guide.</p>"}, "status": {"shape": "FindingStatus", "documentation": "<p>The status of the finding.</p>"}, "title": {"shape": "Finding<PERSON>itle", "documentation": "<p>The title of the finding.</p>"}, "type": {"shape": "FindingType", "documentation": "<p>The type of the finding. The <code>type</code> value determines the valid values for <code>resource</code> in your request. For more information, see <a href=\"https://docs.aws.amazon.com/inspector/latest/user/findings-types.html\">Finding types</a> in the Amazon Inspector user guide.</p>"}, "updatedAt": {"shape": "DateTimeTimestamp", "documentation": "<p>The date and time the finding was last updated at.</p>"}}, "documentation": "<p>Details about an Amazon Inspector finding.</p>"}, "FindingArn": {"type": "string", "max": 100, "min": 1, "pattern": "^arn:(aws[a-zA-Z-]*)?:inspector2:[a-z]{2}(-gov)?-[a-z]+-\\d{1}:\\d{12}:finding/[a-f0-9]{32}$"}, "FindingArnList": {"type": "list", "member": {"shape": "FindingArn"}, "max": 10, "min": 1}, "FindingDescription": {"type": "string", "max": 1024, "min": 1}, "FindingDetail": {"type": "structure", "members": {"cisaData": {"shape": "CisaData", "documentation": "<p>The Cybersecurity and Infrastructure Security Agency (CISA) details for a specific vulnerability.</p>"}, "cwes": {"shape": "Cwes", "documentation": "<p>The Common Weakness Enumerations (CWEs) associated with the vulnerability.</p>"}, "epssScore": {"shape": "Double", "documentation": "<p>The Exploit Prediction Scoring System (EPSS) score of the vulnerability.</p>"}, "evidences": {"shape": "EvidenceList", "documentation": "<p>Information on the evidence of the vulnerability.</p>"}, "exploitObserved": {"shape": "ExploitObserved", "documentation": "<p>Contains information on when this exploit was observed.</p>"}, "findingArn": {"shape": "FindingArn", "documentation": "<p>The finding ARN that the vulnerability details are associated with.</p>"}, "referenceUrls": {"shape": "VulnerabilityReferenceUrls", "documentation": "<p>The reference URLs for the vulnerability data.</p>"}, "riskScore": {"shape": "RiskScore", "documentation": "<p>The risk score of the vulnerability.</p>"}, "tools": {"shape": "Tools", "documentation": "<p>The known malware tools or kits that can exploit the vulnerability.</p>"}, "ttps": {"shape": "Ttps", "documentation": "<p>The MITRE adversary tactics, techniques, or procedures (TTPs) associated with the vulnerability.</p>"}}, "documentation": "<p>Details of the vulnerability identified in a finding.</p>"}, "FindingDetails": {"type": "list", "member": {"shape": "FindingDetail"}, "min": 0}, "FindingDetailsError": {"type": "structure", "required": ["errorCode", "errorMessage", "findingArn"], "members": {"errorCode": {"shape": "FindingDetailsErrorCode", "documentation": "<p>The error code.</p>"}, "errorMessage": {"shape": "NonEmptyString", "documentation": "<p>The error message.</p>"}, "findingArn": {"shape": "FindingArn", "documentation": "<p>The finding ARN that returned an error.</p>"}}, "documentation": "<p>Details about an error encountered when trying to return vulnerability data for a finding.</p>"}, "FindingDetailsErrorCode": {"type": "string", "enum": ["INTERNAL_ERROR", "ACCESS_DENIED", "FINDING_DETAILS_NOT_FOUND", "INVALID_INPUT"]}, "FindingDetailsErrorList": {"type": "list", "member": {"shape": "FindingDetailsError"}}, "FindingList": {"type": "list", "member": {"shape": "Finding"}, "max": 25, "min": 0}, "FindingStatus": {"type": "string", "enum": ["ACTIVE", "SUPPRESSED", "CLOSED"]}, "FindingTitle": {"type": "string", "max": 1024, "min": 1}, "FindingType": {"type": "string", "enum": ["NETWORK_REACHABILITY", "PACKAGE_VULNERABILITY", "CODE_VULNERABILITY"]}, "FindingTypeAggregation": {"type": "structure", "members": {"findingType": {"shape": "AggregationFindingType", "documentation": "<p>The finding type to aggregate.</p>"}, "resourceType": {"shape": "AggregationResourceType", "documentation": "<p>The resource type to aggregate.</p>"}, "sortBy": {"shape": "FindingTypeSortBy", "documentation": "<p>The value to sort results by.</p>"}, "sortOrder": {"shape": "SortOrder", "documentation": "<p>The order to sort results by.</p>"}}, "documentation": "<p>The details that define an aggregation based on finding type.</p>"}, "FindingTypeAggregationResponse": {"type": "structure", "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The ID of the Amazon Web Services account associated with the findings.</p>"}, "exploitAvailableCount": {"shape": "<PERSON>", "documentation": "<p>The number of findings that have an exploit available.</p>"}, "fixAvailableCount": {"shape": "<PERSON>", "documentation": "<p> Details about the number of fixes. </p>"}, "severityCounts": {"shape": "SeverityCounts", "documentation": "<p>The value to sort results by.</p>"}}, "documentation": "<p>A response that contains the results of a finding type aggregation.</p>"}, "FindingTypeSortBy": {"type": "string", "enum": ["CRITICAL", "HIGH", "ALL"]}, "FirstSeen": {"type": "timestamp"}, "FixAvailable": {"type": "string", "enum": ["YES", "NO", "PARTIAL"]}, "FreeTrialAccountInfo": {"type": "structure", "required": ["accountId", "freeTrialInfo"], "members": {"accountId": {"shape": "MeteringAccountId", "documentation": "<p>The account associated with the Amazon Inspector free trial information.</p>"}, "freeTrialInfo": {"shape": "FreeTrialInfoList", "documentation": "<p>Contains information about the Amazon Inspector free trial for an account.</p>"}}, "documentation": "<p>Information about the Amazon Inspector free trial for an account.</p>"}, "FreeTrialAccountInfoList": {"type": "list", "member": {"shape": "FreeTrialAccountInfo"}}, "FreeTrialInfo": {"type": "structure", "required": ["end", "start", "status", "type"], "members": {"end": {"shape": "Timestamp", "documentation": "<p>The date and time that the Amazon Inspector free trail ends for a given account.</p>"}, "start": {"shape": "Timestamp", "documentation": "<p>The date and time that the Amazon Inspector free trail started for a given account.</p>"}, "status": {"shape": "FreeTrialStatus", "documentation": "<p>The order to sort results by.</p>"}, "type": {"shape": "FreeTrialType", "documentation": "<p>The type of scan covered by the Amazon Inspector free trail.</p>"}}, "documentation": "<p>An object that contains information about the Amazon Inspector free trial for an account.</p>"}, "FreeTrialInfoError": {"type": "structure", "required": ["accountId", "code", "message"], "members": {"accountId": {"shape": "MeteringAccountId", "documentation": "<p>The account associated with the Amazon Inspector free trial information.</p>"}, "code": {"shape": "FreeTrialInfoErrorCode", "documentation": "<p>The error code.</p>"}, "message": {"shape": "String", "documentation": "<p>The error message returned.</p>"}}, "documentation": "<p>Information about an error received while accessing free trail data for an account.</p>"}, "FreeTrialInfoErrorCode": {"type": "string", "enum": ["ACCESS_DENIED", "INTERNAL_ERROR"]}, "FreeTrialInfoErrorList": {"type": "list", "member": {"shape": "FreeTrialInfoError"}}, "FreeTrialInfoList": {"type": "list", "member": {"shape": "FreeTrialInfo"}}, "FreeTrialStatus": {"type": "string", "enum": ["ACTIVE", "INACTIVE"]}, "FreeTrialType": {"type": "string", "enum": ["EC2", "ECR", "LAMBDA", "LAMBDA_CODE", "CODE_REPOSITORY"]}, "FrequencyExpression": {"type": "string", "max": 256, "min": 1}, "FunctionName": {"type": "string", "pattern": "^[a-zA-Z0-9-_\\.]+(:(\\$LATEST|[a-zA-Z0-9-_]+))?$"}, "GetCisScanReportRequest": {"type": "structure", "required": ["scanArn"], "members": {"reportFormat": {"shape": "CisReportFormat", "documentation": "<p> The format of the report. Valid values are <code>PDF</code> and <code>CSV</code>. If no value is specified, the report format defaults to <code>PDF</code>. </p>"}, "scanArn": {"shape": "CisScanArn", "documentation": "<p>The scan ARN.</p>"}, "targetAccounts": {"shape": "ReportTargetAccounts", "documentation": "<p>The target accounts.</p>"}}}, "GetCisScanReportResponse": {"type": "structure", "members": {"status": {"shape": "CisReportStatus", "documentation": "<p>The status.</p>"}, "url": {"shape": "String", "documentation": "<p> The URL where a PDF or CSV of the CIS scan report can be downloaded. </p>"}}}, "GetCisScanResultDetailsMaxResults": {"type": "integer", "box": true, "max": 1000, "min": 1}, "GetCisScanResultDetailsRequest": {"type": "structure", "required": ["accountId", "scanArn", "targetResourceId"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The account ID.</p>"}, "filterCriteria": {"shape": "CisScanResultDetailsFilterCriteria", "documentation": "<p>The filter criteria.</p>"}, "maxResults": {"shape": "GetCisScanResultDetailsMaxResults", "documentation": "<p>The maximum number of CIS scan result details to be returned in a single page of results.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The pagination token from a previous request that's used to retrieve the next page of results.</p>"}, "scanArn": {"shape": "CisScanArn", "documentation": "<p>The scan ARN.</p>"}, "sortBy": {"shape": "CisScanResultDetailsSortBy", "documentation": "<p>The sort by order.</p>"}, "sortOrder": {"shape": "CisSortOrder", "documentation": "<p>The sort order.</p>"}, "targetResourceId": {"shape": "ResourceId", "documentation": "<p>The target resource ID.</p>"}}}, "GetCisScanResultDetailsResponse": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The pagination token from a previous request that's used to retrieve the next page of results.</p>"}, "scanResultDetails": {"shape": "CisScanResultDetailsList", "documentation": "<p>The scan result details.</p>"}}}, "GetClustersForImageNextToken": {"type": "string", "max": 3000, "min": 1}, "GetClustersForImageRequest": {"type": "structure", "required": ["filter"], "members": {"filter": {"shape": "ClusterForImageFilterCriteria", "documentation": "<p>The resource Id for the Amazon ECR image.</p>"}, "maxResults": {"shape": "GetClustersForImageRequestMaxResultsInteger", "documentation": "<p>The maximum number of results to be returned in a single page of results.</p>"}, "nextToken": {"shape": "GetClustersForImageNextToken", "documentation": "<p>The pagination token from a previous request used to retrieve the next page of results.</p>"}}}, "GetClustersForImageRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "GetClustersForImageResponse": {"type": "structure", "required": ["cluster"], "members": {"cluster": {"shape": "ClusterInformationList", "documentation": "<p>A unit of work inside of a cluster, which can include metadata about the cluster.</p>"}, "nextToken": {"shape": "GetClustersForImageNextToken", "documentation": "<p>The pagination token from a previous request used to retrieve the next page of results.</p>"}}}, "GetCodeSecurityIntegrationRequest": {"type": "structure", "required": ["integrationArn"], "members": {"integrationArn": {"shape": "CodeSecurityIntegrationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the code security integration to retrieve.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags associated with the code security integration.</p>"}}}, "GetCodeSecurityIntegrationResponse": {"type": "structure", "required": ["createdOn", "integrationArn", "lastUpdateOn", "name", "status", "statusReason", "type"], "members": {"authorizationUrl": {"shape": "AuthorizationUrl", "documentation": "<p>The URL used to authorize the integration with the repository provider. This is only returned if reauthorization is required to fix a connection issue. Otherwise, it is null.</p>"}, "createdOn": {"shape": "Timestamp", "documentation": "<p>The timestamp when the code security integration was created.</p>"}, "integrationArn": {"shape": "CodeSecurityIntegrationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the code security integration.</p>"}, "lastUpdateOn": {"shape": "Timestamp", "documentation": "<p>The timestamp when the code security integration was last updated.</p>"}, "name": {"shape": "IntegrationName", "documentation": "<p>The name of the code security integration.</p>"}, "status": {"shape": "IntegrationStatus", "documentation": "<p>The current status of the code security integration.</p>"}, "statusReason": {"shape": "String", "documentation": "<p>The reason for the current status of the code security integration.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags associated with the code security integration.</p>"}, "type": {"shape": "IntegrationType", "documentation": "<p>The type of repository provider for the integration.</p>"}}}, "GetCodeSecurityScanConfigurationRequest": {"type": "structure", "required": ["scanConfigurationArn"], "members": {"scanConfigurationArn": {"shape": "ScanConfigurationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the scan configuration to retrieve.</p>"}}}, "GetCodeSecurityScanConfigurationResponse": {"type": "structure", "members": {"configuration": {"shape": "CodeSecurityScanConfiguration", "documentation": "<p>The configuration settings for the code security scan.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The timestamp when the scan configuration was created.</p>"}, "lastUpdatedAt": {"shape": "Timestamp", "documentation": "<p>The timestamp when the scan configuration was last updated.</p>"}, "level": {"shape": "ConfigurationLevel", "documentation": "<p>The security level for the scan configuration.</p>"}, "name": {"shape": "ScanConfigurationName", "documentation": "<p>The name of the scan configuration.</p>"}, "scanConfigurationArn": {"shape": "ScanConfigurationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the scan configuration.</p>"}, "scopeSettings": {"shape": "ScopeSettings", "documentation": "<p>The scope settings that define which repositories will be scanned. If the <code>ScopeSetting</code> parameter is <code>ALL</code> the scan configuration applies to all existing and future projects imported into Amazon Inspector.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags associated with the scan configuration.</p>"}}}, "GetCodeSecurityScanRequest": {"type": "structure", "required": ["resource", "scanId"], "members": {"resource": {"shape": "CodeSecurityResource", "documentation": "<p>The resource identifier for the code repository that was scanned.</p>"}, "scanId": {"shape": "CodeSecurityUuid", "documentation": "<p>The unique identifier of the scan to retrieve.</p>"}}}, "GetCodeSecurityScanResponse": {"type": "structure", "members": {"accountId": {"shape": "String", "documentation": "<p>The Amazon Web Services account ID associated with the scan.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The timestamp when the scan was created.</p>"}, "lastCommitId": {"shape": "String", "documentation": "<p>The identifier of the last commit that was scanned. This is only returned if the scan was successful or skipped.</p>"}, "resource": {"shape": "CodeSecurityResource", "documentation": "<p>The resource identifier for the code repository that was scanned.</p>"}, "scanId": {"shape": "CodeSecurityUuid", "documentation": "<p>The unique identifier of the scan.</p>"}, "status": {"shape": "CodeScanStatus", "documentation": "<p>The current status of the scan.</p>"}, "statusReason": {"shape": "String", "documentation": "<p>The reason for the current status of the scan.</p>"}, "updatedAt": {"shape": "Timestamp", "documentation": "<p>The timestamp when the scan was last updated.</p>"}}}, "GetConfigurationRequest": {"type": "structure", "members": {}}, "GetConfigurationResponse": {"type": "structure", "members": {"ec2Configuration": {"shape": "Ec2ConfigurationState", "documentation": "<p>Specifies how the Amazon EC2 automated scan mode is currently configured for your environment.</p>"}, "ecrConfiguration": {"shape": "EcrConfigurationState", "documentation": "<p>Specifies how the ECR automated re-scan duration is currently configured for your environment.</p>"}}}, "GetDelegatedAdminAccountRequest": {"type": "structure", "members": {}}, "GetDelegatedAdminAccountResponse": {"type": "structure", "members": {"delegatedAdmin": {"shape": "DelegatedAdmin", "documentation": "<p>The Amazon Web Services account ID of the Amazon Inspector delegated administrator.</p>"}}}, "GetEc2DeepInspectionConfigurationRequest": {"type": "structure", "members": {}}, "GetEc2DeepInspectionConfigurationResponse": {"type": "structure", "members": {"errorMessage": {"shape": "NonEmptyString", "documentation": "<p>An error message explaining why Amazon Inspector deep inspection configurations could not be retrieved for your account.</p>"}, "orgPackagePaths": {"shape": "PathList", "documentation": "<p>The Amazon Inspector deep inspection custom paths for your organization.</p>"}, "packagePaths": {"shape": "PathList", "documentation": "<p>The Amazon Inspector deep inspection custom paths for your account.</p>"}, "status": {"shape": "Ec2DeepInspectionStatus", "documentation": "<p>The activation status of Amazon Inspector deep inspection in your account.</p>"}}}, "GetEncryptionKeyRequest": {"type": "structure", "required": ["resourceType", "scanType"], "members": {"resourceType": {"shape": "ResourceType", "documentation": "<p>The resource type the key encrypts.</p>", "location": "querystring", "locationName": "resourceType"}, "scanType": {"shape": "ScanType", "documentation": "<p>The scan type the key encrypts.</p>", "location": "querystring", "locationName": "scanType"}}}, "GetEncryptionKeyResponse": {"type": "structure", "required": ["kmsKeyId"], "members": {"kmsKeyId": {"shape": "KmsKeyArn", "documentation": "<p>A kms key ID.</p>"}}}, "GetFindingsReportStatusRequest": {"type": "structure", "members": {"reportId": {"shape": "ReportId", "documentation": "<p>The ID of the report to retrieve the status of.</p>"}}}, "GetFindingsReportStatusResponse": {"type": "structure", "members": {"destination": {"shape": "Destination", "documentation": "<p>The destination of the report.</p>"}, "errorCode": {"shape": "ReportingErrorCode", "documentation": "<p>The error code of the report.</p>"}, "errorMessage": {"shape": "ErrorMessage", "documentation": "<p>The error message of the report.</p>"}, "filterCriteria": {"shape": "FilterCriteria", "documentation": "<p>The filter criteria associated with the report.</p>"}, "reportId": {"shape": "ReportId", "documentation": "<p>The ID of the report.</p>"}, "status": {"shape": "ExternalReportStatus", "documentation": "<p>The status of the report.</p>"}}}, "GetMemberRequest": {"type": "structure", "required": ["accountId"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the member account to retrieve information on.</p>"}}}, "GetMemberResponse": {"type": "structure", "members": {"member": {"shape": "Member", "documentation": "<p>Details of the retrieved member account.</p>"}}}, "GetSbomExportRequest": {"type": "structure", "required": ["reportId"], "members": {"reportId": {"shape": "ReportId", "documentation": "<p>The report ID of the SBOM export to get details for.</p>"}}}, "GetSbomExportResponse": {"type": "structure", "members": {"errorCode": {"shape": "ReportingErrorCode", "documentation": "<p>An error code.</p>"}, "errorMessage": {"shape": "NonEmptyString", "documentation": "<p>An error message.</p>"}, "filterCriteria": {"shape": "ResourceFilterCriteria", "documentation": "<p>Contains details about the resource filter criteria used for the software bill of materials (SBOM) report.</p>"}, "format": {"shape": "SbomReportFormat", "documentation": "<p>The format of the software bill of materials (SBOM) report.</p>"}, "reportId": {"shape": "ReportId", "documentation": "<p>The report ID of the software bill of materials (SBOM) report.</p>"}, "s3Destination": {"shape": "Destination", "documentation": "<p>Contains details of the Amazon S3 bucket and KMS key used to export findings</p>"}, "status": {"shape": "ExternalReportStatus", "documentation": "<p>The status of the software bill of materials (SBOM) report.</p>"}}}, "GitHubAuthCode": {"type": "string", "max": 1024, "min": 1, "sensitive": true}, "GitHubInstallationId": {"type": "string", "max": 1024, "min": 1}, "GitLabAccessToken": {"type": "string", "sensitive": true}, "GitLabAuthCode": {"type": "string", "max": 1024, "min": 1, "sensitive": true}, "GroupKey": {"type": "string", "enum": ["SCAN_STATUS_CODE", "SCAN_STATUS_REASON", "ACCOUNT_ID", "RESOURCE_TYPE", "ECR_REPOSITORY_NAME"]}, "ImageHash": {"type": "string", "max": 71, "min": 71, "pattern": "^sha256:[a-z0-9]{64}$"}, "ImageLayerAggregation": {"type": "structure", "members": {"layerHashes": {"shape": "StringFilterList", "documentation": "<p>The hashes associated with the layers.</p>"}, "repositories": {"shape": "StringFilterList", "documentation": "<p>The repository associated with the container image hosting the layers.</p>"}, "resourceIds": {"shape": "StringFilterList", "documentation": "<p>The ID of the container image layer.</p>"}, "sortBy": {"shape": "ImageLayerSortBy", "documentation": "<p>The value to sort results by.</p>"}, "sortOrder": {"shape": "SortOrder", "documentation": "<p>The order to sort results by.</p>"}}, "documentation": "<p>The details that define an aggregation based on container image layers.</p>"}, "ImageLayerAggregationResponse": {"type": "structure", "required": ["accountId", "layerHash", "repository", "resourceId"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The ID of the Amazon Web Services account that owns the container image hosting the layer image.</p>"}, "layerHash": {"shape": "NonEmptyString", "documentation": "<p>The layer hash.</p>"}, "repository": {"shape": "NonEmptyString", "documentation": "<p>The repository the layer resides in.</p>"}, "resourceId": {"shape": "NonEmptyString", "documentation": "<p>The resource ID of the container image layer.</p>"}, "severityCounts": {"shape": "SeverityCounts", "documentation": "<p>An object that represents the count of matched findings per severity.</p>"}}, "documentation": "<p>A response that contains the results of a finding aggregation by image layer.</p>"}, "ImageLayerSortBy": {"type": "string", "enum": ["CRITICAL", "HIGH", "ALL"]}, "ImageTagList": {"type": "list", "member": {"shape": "NonEmptyString"}}, "InspectorScoreDetails": {"type": "structure", "members": {"adjustedCvss": {"shape": "CvssScoreDetails", "documentation": "<p>An object that contains details about the CVSS score given to a finding.</p>"}}, "documentation": "<p>Information about the Amazon Inspector score given to a finding.</p>"}, "InstanceUrl": {"type": "string", "pattern": "^https://[-a-zA-Z0-9()@:%_+.~#?&//=]{1,1024}$", "sensitive": true}, "Integer": {"type": "integer", "box": true}, "IntegrationName": {"type": "string", "max": 60, "min": 1, "pattern": "^[a-zA-Z0-9-_$:.]*$"}, "IntegrationStatus": {"type": "string", "enum": ["PENDING", "IN_PROGRESS", "ACTIVE", "INACTIVE", "DISABLING"]}, "IntegrationSummaries": {"type": "list", "member": {"shape": "CodeSecurityIntegrationSummary"}, "max": 100, "min": 0}, "IntegrationType": {"type": "string", "enum": ["GITLAB_SELF_MANAGED", "GITHUB"]}, "InternalServerException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}, "retryAfterSeconds": {"shape": "Integer", "documentation": "<p>The number of seconds to wait before retrying the request.</p>", "location": "header", "locationName": "Retry-After"}}, "documentation": "<p>The request has failed due to an internal failure of the Amazon Inspector service.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true, "retryable": {"throttling": false}}, "IpV4Address": {"type": "string", "max": 15, "min": 7, "pattern": "^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$"}, "IpV4AddressList": {"type": "list", "member": {"shape": "IpV4Address"}}, "IpV6Address": {"type": "string", "max": 47, "min": 1}, "IpV6AddressList": {"type": "list", "member": {"shape": "IpV6Address"}}, "KmsKeyArn": {"type": "string", "pattern": "^arn:aws(-(us-gov|cn))?:kms:([a-z0-9][-.a-z0-9]{0,62})?:[0-9]{12}?:key/(([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})|(mrk-[0-9a-zA-Z]{32}))$"}, "LambdaFunctionAggregation": {"type": "structure", "members": {"functionNames": {"shape": "StringFilterList", "documentation": "<p>The Amazon Web Services Lambda function names to include in the aggregation results.</p>"}, "functionTags": {"shape": "MapFilterList", "documentation": "<p>The tags to include in the aggregation results.</p>"}, "resourceIds": {"shape": "StringFilterList", "documentation": "<p>The resource IDs to include in the aggregation results.</p>"}, "runtimes": {"shape": "StringFilterList", "documentation": "<p>Returns findings aggregated by Amazon Web Services Lambda function runtime environments.</p>"}, "sortBy": {"shape": "LambdaFunctionSortBy", "documentation": "<p>The finding severity to use for sorting the results.</p>"}, "sortOrder": {"shape": "SortOrder", "documentation": "<p>The order to use for sorting the results.</p>"}}, "documentation": "<p>The details that define a findings aggregation based on Amazon Web Services Lambda functions.</p>"}, "LambdaFunctionAggregationResponse": {"type": "structure", "required": ["resourceId"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The ID of the Amazon Web Services account that owns the Amazon Web Services Lambda function. </p>"}, "functionName": {"shape": "String", "documentation": "<p>The Amazon Web Services Lambda function names included in the aggregation results.</p>"}, "lambdaTags": {"shape": "TagMap", "documentation": "<p>The tags included in the aggregation results.</p>"}, "lastModifiedAt": {"shape": "DateTimeTimestamp", "documentation": "<p>The date that the Amazon Web Services Lambda function included in the aggregation results was last changed.</p>"}, "resourceId": {"shape": "NonEmptyString", "documentation": "<p>The resource IDs included in the aggregation results.</p>"}, "runtime": {"shape": "String", "documentation": "<p>The runtimes included in the aggregation results.</p>"}, "severityCounts": {"shape": "SeverityCounts", "documentation": "<p>An object that contains the counts of aggregated finding per severity.</p>"}}, "documentation": "<p>A response that contains the results of an Amazon Web Services Lambda function finding aggregation.</p>"}, "LambdaFunctionMetadata": {"type": "structure", "members": {"functionName": {"shape": "String", "documentation": "<p>The name of a function.</p>"}, "functionTags": {"shape": "TagMap", "documentation": "<p>The resource tags on an Amazon Web Services Lambda function.</p>"}, "layers": {"shape": "LambdaLayerList", "documentation": "<p>The layers for an Amazon Web Services Lambda function. A Lambda function can have up to five layers.</p>"}, "runtime": {"shape": "Runtime", "documentation": "<p>An Amazon Web Services Lambda function's runtime.</p>"}}, "documentation": "<p>The Amazon Web Services Lambda function metadata.</p>"}, "LambdaFunctionSortBy": {"type": "string", "enum": ["CRITICAL", "HIGH", "ALL"]}, "LambdaLayerAggregation": {"type": "structure", "members": {"functionNames": {"shape": "StringFilterList", "documentation": "<p>The names of the Amazon Web Services Lambda functions associated with the layers.</p>"}, "layerArns": {"shape": "StringFilterList", "documentation": "<p>The Amazon Resource Name (ARN) of the Amazon Web Services Lambda function layer. </p>"}, "resourceIds": {"shape": "StringFilterList", "documentation": "<p>The resource IDs for the Amazon Web Services Lambda function layers.</p>"}, "sortBy": {"shape": "LambdaLayerSortBy", "documentation": "<p>The finding severity to use for sorting the results.</p>"}, "sortOrder": {"shape": "SortOrder", "documentation": "<p>The order to use for sorting the results.</p>"}}, "documentation": "<p>The details that define a findings aggregation based on an Amazon Web Services Lambda function's layers.</p>"}, "LambdaLayerAggregationResponse": {"type": "structure", "required": ["accountId", "functionName", "layerArn", "resourceId"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The account ID of the Amazon Web Services Lambda function layer.</p>"}, "functionName": {"shape": "NonEmptyString", "documentation": "<p>The names of the Amazon Web Services Lambda functions associated with the layers.</p>"}, "layerArn": {"shape": "NonEmptyString", "documentation": "<p>The Amazon Resource Name (ARN) of the Amazon Web Services Lambda function layer.</p>"}, "resourceId": {"shape": "NonEmptyString", "documentation": "<p>The Resource ID of the Amazon Web Services Lambda function layer.</p>"}, "severityCounts": {"shape": "SeverityCounts", "documentation": "<p>An object that contains the counts of aggregated finding per severity.</p>"}}, "documentation": "<p>A response that contains the results of an Amazon Web Services Lambda function layer finding aggregation.</p>"}, "LambdaLayerArn": {"type": "string", "pattern": "^arn:[a-zA-Z0-9-]+:lambda:[a-zA-Z0-9-]+:\\d{12}:layer:[a-zA-Z0-9-_]+:[0-9]+$"}, "LambdaLayerList": {"type": "list", "member": {"shape": "String"}, "max": 5, "min": 0}, "LambdaLayerSortBy": {"type": "string", "enum": ["CRITICAL", "HIGH", "ALL"]}, "LambdaVpcConfig": {"type": "structure", "members": {"securityGroupIds": {"shape": "SecurityGroupIdList", "documentation": "<p>The VPC security groups and subnets that are attached to an Amazon Web Services Lambda function. For more information, see <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/configuration-vpc.html\">VPC Settings</a>.</p>"}, "subnetIds": {"shape": "SubnetIdList", "documentation": "<p>A list of VPC subnet IDs.</p>"}, "vpcId": {"shape": "VpcId", "documentation": "<p>The ID of the VPC.</p>"}}, "documentation": "<p>The VPC security groups and subnets that are attached to an Amazon Web Services Lambda function. For more information, see <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/configuration-vpc.html\">VPC Settings</a>.</p>"}, "LastSeen": {"type": "timestamp"}, "LayerList": {"type": "list", "member": {"shape": "LambdaLayerArn"}, "max": 5, "min": 1}, "ListAccountPermissionsMaxResults": {"type": "integer", "box": true, "max": 1024, "min": 1}, "ListAccountPermissionsRequest": {"type": "structure", "members": {"maxResults": {"shape": "ListAccountPermissionsMaxResults", "documentation": "<p>The maximum number of results the response can return. If your request would return more than the maximum the response will return a <code>nextToken</code> value, use this value when you call the action again to get the remaining results.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request to a list action. If your response returns more than the <code>maxResults</code> maximum value it will also return a <code>nextToken</code> value. For subsequent calls, use the NextToken value returned from the previous request to continue listing results after the first page.</p>"}, "service": {"shape": "Service", "documentation": "<p>The service scan type to check permissions for.</p>"}}}, "ListAccountPermissionsResponse": {"type": "structure", "required": ["permissions"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request to a list action. For subsequent calls, use the <code>NextToken</code> value returned from the previous request to continue listing results after the first page.</p>"}, "permissions": {"shape": "Permissions", "documentation": "<p>Contains details on the permissions an account has to configure Amazon Inspector.</p>"}}}, "ListCisScanConfigurationsFilterCriteria": {"type": "structure", "members": {"scanConfigurationArnFilters": {"shape": "CisScanConfigurationArnFilterList", "documentation": "<p>The list of scan configuration ARN filters.</p>"}, "scanNameFilters": {"shape": "CisScanNameFilterList", "documentation": "<p>The list of scan name filters.</p>"}, "targetResourceTagFilters": {"shape": "ResourceTagFilterList", "documentation": "<p>The list of target resource tag filters.</p>"}}, "documentation": "<p>A list of CIS scan configurations filter criteria.</p>"}, "ListCisScanConfigurationsMaxResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListCisScanConfigurationsRequest": {"type": "structure", "members": {"filterCriteria": {"shape": "ListCisScanConfigurationsFilterCriteria", "documentation": "<p>The CIS scan configuration filter criteria.</p>"}, "maxResults": {"shape": "ListCisScanConfigurationsMaxResults", "documentation": "<p>The maximum number of CIS scan configurations to be returned in a single page of results.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The pagination token from a previous request that's used to retrieve the next page of results.</p>"}, "sortBy": {"shape": "CisScanConfigurationsSortBy", "documentation": "<p>The CIS scan configuration sort by order.</p>"}, "sortOrder": {"shape": "CisSortOrder", "documentation": "<p>The CIS scan configuration sort order order.</p>"}}}, "ListCisScanConfigurationsResponse": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The pagination token from a previous request that's used to retrieve the next page of results.</p>"}, "scanConfigurations": {"shape": "CisScanConfigurationList", "documentation": "<p>The CIS scan configuration scan configurations.</p>"}}}, "ListCisScanResultsAggregatedByChecksRequest": {"type": "structure", "required": ["scanArn"], "members": {"filterCriteria": {"shape": "CisScanResultsAggregatedByChecksFilterCriteria", "documentation": "<p>The filter criteria.</p>"}, "maxResults": {"shape": "CisScanResultsMaxResults", "documentation": "<p>The maximum number of scan results aggregated by checks to be returned in a single page of results.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The pagination token from a previous request that's used to retrieve the next page of results.</p>"}, "scanArn": {"shape": "CisScanArn", "documentation": "<p>The scan ARN.</p>"}, "sortBy": {"shape": "CisScanResultsAggregatedByChecksSortBy", "documentation": "<p>The sort by order.</p>"}, "sortOrder": {"shape": "CisSortOrder", "documentation": "<p>The sort order.</p>"}}}, "ListCisScanResultsAggregatedByChecksResponse": {"type": "structure", "members": {"checkAggregations": {"shape": "CisCheckAggregationList", "documentation": "<p>The check aggregations.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The pagination token from a previous request that's used to retrieve the next page of results.</p>"}}}, "ListCisScanResultsAggregatedByTargetResourceRequest": {"type": "structure", "required": ["scanArn"], "members": {"filterCriteria": {"shape": "CisScanResultsAggregatedByTargetResourceFilterCriteria", "documentation": "<p>The filter criteria.</p>"}, "maxResults": {"shape": "CisScanResultsMaxResults", "documentation": "<p>The maximum number of scan results aggregated by a target resource to be returned in a single page of results.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The pagination token from a previous request that's used to retrieve the next page of results.</p>"}, "scanArn": {"shape": "CisScanArn", "documentation": "<p>The scan ARN.</p>"}, "sortBy": {"shape": "CisScanResultsAggregatedByTargetResourceSortBy", "documentation": "<p>The sort by order.</p>"}, "sortOrder": {"shape": "CisSortOrder", "documentation": "<p>The sort order.</p>"}}}, "ListCisScanResultsAggregatedByTargetResourceResponse": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The pagination token from a previous request that's used to retrieve the next page of results.</p>"}, "targetResourceAggregations": {"shape": "CisTargetResourceAggregationList", "documentation": "<p>The resource aggregations.</p>"}}}, "ListCisScansDetailLevel": {"type": "string", "enum": ["ORGANIZATION", "MEMBER"]}, "ListCisScansFilterCriteria": {"type": "structure", "members": {"failedChecksFilters": {"shape": "CisNumberFilterList", "documentation": "<p>The list of failed checks filters.</p>"}, "scanArnFilters": {"shape": "CisScanArnFilterList", "documentation": "<p>The list of scan ARN filters.</p>"}, "scanAtFilters": {"shape": "CisScanDateFilterList", "documentation": "<p>The list of scan at filters.</p>"}, "scanConfigurationArnFilters": {"shape": "CisScanConfigurationArnFilterList", "documentation": "<p>The list of scan configuration ARN filters.</p>"}, "scanNameFilters": {"shape": "CisScanNameFilterList", "documentation": "<p>The list of scan name filters.</p>"}, "scanStatusFilters": {"shape": "CisScanStatusFilterList", "documentation": "<p>The list of scan status filters.</p>"}, "scheduledByFilters": {"shape": "CisScheduledByFilterList", "documentation": "<p>The list of scheduled by filters.</p>"}, "targetAccountIdFilters": {"shape": "AccountIdFilterList", "documentation": "<p>The list of target account ID filters.</p>"}, "targetResourceIdFilters": {"shape": "ResourceIdFilterList", "documentation": "<p>The list of target resource ID filters.</p>"}, "targetResourceTagFilters": {"shape": "ResourceTagFilterList", "documentation": "<p>The list of target resource tag filters.</p>"}}, "documentation": "<p>A list of CIS scans filter criteria.</p>"}, "ListCisScansMaxResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListCisScansRequest": {"type": "structure", "members": {"detailLevel": {"shape": "ListCisScansDetailLevel", "documentation": "<p>The detail applied to the CIS scan.</p>"}, "filterCriteria": {"shape": "ListCisScansFilterCriteria", "documentation": "<p>The CIS scan filter criteria.</p>"}, "maxResults": {"shape": "ListCisScansMaxResults", "documentation": "<p>The maximum number of results to be returned.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The pagination token from a previous request that's used to retrieve the next page of results.</p>"}, "sortBy": {"shape": "ListCisScansSortBy", "documentation": "<p>The CIS scans sort by order.</p>"}, "sortOrder": {"shape": "CisSortOrder", "documentation": "<p>The CIS scans sort order.</p>"}}}, "ListCisScansResponse": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The pagination token from a previous request that's used to retrieve the next page of results.</p>"}, "scans": {"shape": "CisScanList", "documentation": "<p>The CIS scans.</p>"}}}, "ListCisScansSortBy": {"type": "string", "enum": ["STATUS", "SCHEDULED_BY", "SCAN_START_DATE", "FAILED_CHECKS"]}, "ListCodeSecurityIntegrationsRequest": {"type": "structure", "members": {"maxResults": {"shape": "ListCodeSecurityIntegrationsRequestMaxResultsInteger", "documentation": "<p>The maximum number of results to return in a single call.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "String", "documentation": "<p>A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request. For subsequent calls, use the NextToken value returned from the previous request to continue listing results after the first page.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListCodeSecurityIntegrationsRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListCodeSecurityIntegrationsResponse": {"type": "structure", "members": {"integrations": {"shape": "IntegrationSummaries", "documentation": "<p>A list of code security integration summaries.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request. For subsequent calls, use the NextToken value returned from the previous request to continue listing results after the first page.</p>"}}}, "ListCodeSecurityScanConfigurationAssociationsRequest": {"type": "structure", "required": ["scanConfigurationArn"], "members": {"maxResults": {"shape": "ListCodeSecurityScanConfigurationAssociationsRequestMaxResultsInteger", "documentation": "<p>The maximum number of results to return in the response. If your request would return more than the maximum the response will return a <code>nextToken</code> value, use this value when you call the action again to get the remaining results.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request to a list action. For subsequent calls, use the <code>NextToken</code> value returned from the previous request to continue listing results after the first page.</p>", "location": "querystring", "locationName": "nextToken"}, "scanConfigurationArn": {"shape": "ScanConfigurationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the scan configuration to list associations for.</p>"}}}, "ListCodeSecurityScanConfigurationAssociationsRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListCodeSecurityScanConfigurationAssociationsResponse": {"type": "structure", "members": {"associations": {"shape": "CodeSecurityScanConfigurationAssociationSummaries", "documentation": "<p>A list of associations between code repositories and scan configurations.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request to a list action. For subsequent calls, use the <code>NextToken</code> value returned from the previous request to continue listing results after the first page.</p>"}}}, "ListCodeSecurityScanConfigurationsRequest": {"type": "structure", "members": {"maxResults": {"shape": "ListCodeSecurityScanConfigurationsRequestMaxResultsInteger", "documentation": "<p>The maximum number of results to return in a single call.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request. For subsequent calls, use the NextToken value returned from the previous request to continue listing results after the first page.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListCodeSecurityScanConfigurationsRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListCodeSecurityScanConfigurationsResponse": {"type": "structure", "members": {"configurations": {"shape": "CodeSecurityScanConfigurationSummaries", "documentation": "<p>A list of code security scan configuration summaries.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request. For subsequent calls, use the NextToken value returned from the previous request to continue listing results after the first page.</p>"}}}, "ListCoverageMaxResults": {"type": "integer", "box": true, "max": 200, "min": 1}, "ListCoverageRequest": {"type": "structure", "members": {"filterCriteria": {"shape": "CoverageFilterCriteria", "documentation": "<p>An object that contains details on the filters to apply to the coverage data for your environment.</p>"}, "maxResults": {"shape": "ListCoverageMaxResults", "documentation": "<p>The maximum number of results the response can return. If your request would return more than the maximum the response will return a <code>nextToken</code> value, use this value when you call the action again to get the remaining results.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request to a list action. If your response returns more than the <code>maxResults</code> maximum value it will also return a <code>nextToken</code> value. For subsequent calls, use the <code>nextToken</code> value returned from the previous request to continue listing results after the first page.</p>"}}}, "ListCoverageResponse": {"type": "structure", "members": {"coveredResources": {"shape": "CoveredResources", "documentation": "<p>An object that contains details on the covered resources in your environment.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request to a list action. For subsequent calls, use the <code>NextToken</code> value returned from the previous request to continue listing results after the first page.</p>"}}}, "ListCoverageStatisticsRequest": {"type": "structure", "members": {"filterCriteria": {"shape": "CoverageFilterCriteria", "documentation": "<p>An object that contains details on the filters to apply to the coverage data for your environment.</p>"}, "groupBy": {"shape": "GroupKey", "documentation": "<p>The value to group the results by.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request to a list action. For subsequent calls, use the <code>NextToken</code> value returned from the previous request to continue listing results after the first page.</p>"}}}, "ListCoverageStatisticsResponse": {"type": "structure", "required": ["totalCounts"], "members": {"countsByGroup": {"shape": "CountsList", "documentation": "<p>An array with the number for each group.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request to a list action. For subsequent calls, use the <code>NextToken</code> value returned from the previous request to continue listing results after the first page.</p>"}, "totalCounts": {"shape": "<PERSON>", "documentation": "<p>The total number for all groups.</p>"}}}, "ListDelegatedAdminAccountsRequest": {"type": "structure", "members": {"maxResults": {"shape": "ListDelegatedAdminMaxResults", "documentation": "<p>The maximum number of results the response can return. If your request would return more than the maximum the response will return a <code>nextToken</code> value, use this value when you call the action again to get the remaining results.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request to a list action. If your response returns more than the <code>maxResults</code> maximum value it will also return a <code>nextToken</code> value. For subsequent calls, use the <code>nextToken</code> value returned from the previous request to continue listing results after the first page.</p>"}}}, "ListDelegatedAdminAccountsResponse": {"type": "structure", "members": {"delegatedAdminAccounts": {"shape": "DelegatedAdminAccountList", "documentation": "<p>Details of the Amazon Inspector delegated administrator of your organization.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request to a list action. For subsequent calls, use the <code>NextToken</code> value returned from the previous request to continue listing results after the first page.</p>"}}}, "ListDelegatedAdminMaxResults": {"type": "integer", "box": true, "max": 5, "min": 1}, "ListFilterMaxResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListFiltersRequest": {"type": "structure", "members": {"action": {"shape": "FilterAction", "documentation": "<p>The action the filter applies to matched findings.</p>"}, "arns": {"shape": "FilterArnList", "documentation": "<p>The Amazon resource number (ARN) of the filter.</p>"}, "maxResults": {"shape": "ListFilterMaxResults", "documentation": "<p>The maximum number of results the response can return. If your request would return more than the maximum the response will return a <code>nextToken</code> value, use this value when you call the action again to get the remaining results.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request to a list action. If your response returns more than the <code>maxResults</code> maximum value it will also return a <code>nextToken</code> value. For subsequent calls, use the <code>nextToken</code> value returned from the previous request to continue listing results after the first page.</p>"}}}, "ListFiltersResponse": {"type": "structure", "required": ["filters"], "members": {"filters": {"shape": "FilterList", "documentation": "<p>Contains details on the filters associated with your account.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request to a list action. For subsequent calls, use the <code>NextToken</code> value returned from the previous request to continue listing results after the first page.</p>"}}}, "ListFindingAggregationsMaxResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListFindingAggregationsRequest": {"type": "structure", "required": ["aggregationType"], "members": {"accountIds": {"shape": "StringFilterList", "documentation": "<p>The Amazon Web Services account IDs to retrieve finding aggregation data for.</p>"}, "aggregationRequest": {"shape": "AggregationRequest", "documentation": "<p>Details of the aggregation request that is used to filter your aggregation results.</p>"}, "aggregationType": {"shape": "AggregationType", "documentation": "<p>The type of the aggregation request.</p>"}, "maxResults": {"shape": "ListFindingAggregationsMaxResults", "documentation": "<p>The maximum number of results the response can return. If your request would return more than the maximum the response will return a <code>nextToken</code> value, use this value when you call the action again to get the remaining results.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request to a list action. If your response returns more than the <code>maxResults</code> maximum value it will also return a <code>nextToken</code> value. For subsequent calls, use the <code>nextToken</code> value returned from the previous request to continue listing results after the first page.</p>"}}}, "ListFindingAggregationsResponse": {"type": "structure", "required": ["aggregationType"], "members": {"aggregationType": {"shape": "AggregationType", "documentation": "<p>The type of aggregation to perform.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request to a list action. For subsequent calls, use the <code>NextToken</code> value returned from the previous request to continue listing results after the first page.</p>"}, "responses": {"shape": "AggregationResponseList", "documentation": "<p>Objects that contain the results of an aggregation operation.</p>"}}}, "ListFindingsMaxResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListFindingsRequest": {"type": "structure", "members": {"filterCriteria": {"shape": "FilterCriteria", "documentation": "<p>Details on the filters to apply to your finding results.</p>"}, "maxResults": {"shape": "ListFindingsMaxResults", "documentation": "<p>The maximum number of results the response can return. If your request would return more than the maximum the response will return a <code>nextToken</code> value, use this value when you call the action again to get the remaining results.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request to a list action. If your response returns more than the <code>maxResults</code> maximum value it will also return a <code>nextToken</code> value. For subsequent calls, use the <code>nextToken</code> value returned from the previous request to continue listing results after the first page.</p>"}, "sortCriteria": {"shape": "SortCriteria", "documentation": "<p>Details on the sort criteria to apply to your finding results.</p>"}}}, "ListFindingsResponse": {"type": "structure", "members": {"findings": {"shape": "FindingList", "documentation": "<p>Contains details on the findings in your environment.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request to a list action. For subsequent calls, use the <code>NextToken</code> value returned from the previous request to continue listing results after the first page.</p>"}}}, "ListMembersMaxResults": {"type": "integer", "box": true, "max": 50, "min": 1}, "ListMembersRequest": {"type": "structure", "members": {"maxResults": {"shape": "ListMembersMaxResults", "documentation": "<p>The maximum number of results the response can return. If your request would return more than the maximum the response will return a <code>nextToken</code> value, use this value when you call the action again to get the remaining results.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request to a list action. If your response returns more than the <code>maxResults</code> maximum value it will also return a <code>nextToken</code> value. For subsequent calls, use the <code>nextToken</code> value returned from the previous request to continue listing results after the first page.</p>"}, "onlyAssociated": {"shape": "Boolean", "documentation": "<p>Specifies whether to list only currently associated members if <code>True</code> or to list all members within the organization if <code>False</code>.</p>"}}}, "ListMembersResponse": {"type": "structure", "members": {"members": {"shape": "MemberList", "documentation": "<p>An object that contains details for each member account.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The pagination parameter to be used on the next list operation to retrieve more items.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon resource number (ARN) of the resource to list tags of.</p>", "location": "uri", "locationName": "resourceArn"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"tags": {"shape": "TagMap", "documentation": "<p>The tags associated with the resource.</p>"}}}, "ListUsageTotalsMaxResults": {"type": "integer", "box": true, "max": 500, "min": 1}, "ListUsageTotalsNextToken": {"type": "string", "min": 1}, "ListUsageTotalsRequest": {"type": "structure", "members": {"accountIds": {"shape": "UsageAccountIdList", "documentation": "<p>The Amazon Web Services account IDs to retrieve usage totals for.</p>"}, "maxResults": {"shape": "ListUsageTotalsMaxResults", "documentation": "<p>The maximum number of results the response can return. If your request would return more than the maximum the response will return a <code>nextToken</code> value, use this value when you call the action again to get the remaining results.</p>"}, "nextToken": {"shape": "ListUsageTotalsNextToken", "documentation": "<p>A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request to a list action. If your response returns more than the <code>maxResults</code> maximum value it will also return a <code>nextToken</code> value. For subsequent calls, use the <code>nextToken</code> value returned from the previous request to continue listing results after the first page.</p>"}}}, "ListUsageTotalsResponse": {"type": "structure", "members": {"nextToken": {"shape": "ListUsageTotalsNextToken", "documentation": "<p>The pagination parameter to be used on the next list operation to retrieve more items.</p>"}, "totals": {"shape": "UsageTotalList", "documentation": "<p>An object with details on the total usage for the requested account.</p>"}}}, "Long": {"type": "long", "box": true}, "MapComparison": {"type": "string", "enum": ["EQUALS"]}, "MapFilter": {"type": "structure", "required": ["comparison", "key"], "members": {"comparison": {"shape": "MapComparison", "documentation": "<p>The operator to use when comparing values in the filter.</p>"}, "key": {"shape": "MapKey", "documentation": "<p>The tag key used in the filter.</p>"}, "value": {"shape": "MapValue", "documentation": "<p>The tag value used in the filter.</p>"}}, "documentation": "<p>An object that describes details of a map filter.</p>"}, "MapFilterList": {"type": "list", "member": {"shape": "MapFilter"}, "max": 10, "min": 1}, "MapKey": {"type": "string", "max": 128, "min": 1}, "MapValue": {"type": "string", "max": 256, "min": 0}, "Member": {"type": "structure", "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the member account.</p>"}, "delegatedAdminAccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the Amazon Inspector delegated administrator for this member account.</p>"}, "relationshipStatus": {"shape": "RelationshipStatus", "documentation": "<p>The status of the member account.</p>"}, "updatedAt": {"shape": "DateTimeTimestamp", "documentation": "<p>A timestamp showing when the status of this member was last updated.</p>"}}, "documentation": "<p>Details on a member account in your organization.</p>"}, "MemberAccountEc2DeepInspectionStatus": {"type": "structure", "required": ["accountId", "activateDeepInspection"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The unique identifier for the Amazon Web Services account of the organization member.</p>"}, "activateDeepInspection": {"shape": "Boolean", "documentation": "<p>Whether Amazon Inspector deep inspection is active in the account. If <code>TRUE</code> Amazon Inspector deep inspection is active, if <code>FALSE</code> it is not active.</p>"}}, "documentation": "<p>An object that contains details about the status of Amazon Inspector deep inspection for a member account in your organization.</p>"}, "MemberAccountEc2DeepInspectionStatusList": {"type": "list", "member": {"shape": "MemberAccountEc2DeepInspectionStatus"}, "max": 100, "min": 0}, "MemberAccountEc2DeepInspectionStatusState": {"type": "structure", "required": ["accountId"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The unique identifier for the Amazon Web Services account of the organization member</p>"}, "errorMessage": {"shape": "NonEmptyString", "documentation": "<p>The error message explaining why the account failed to activate Amazon Inspector deep inspection.</p>"}, "status": {"shape": "Ec2DeepInspectionStatus", "documentation": "<p>The state of Amazon Inspector deep inspection in the member account.</p>"}}, "documentation": "<p>An object that contains details about the state of Amazon Inspector deep inspection for a member account.</p>"}, "MemberAccountEc2DeepInspectionStatusStateList": {"type": "list", "member": {"shape": "MemberAccountEc2DeepInspectionStatusState"}, "max": 100, "min": 0}, "MemberList": {"type": "list", "member": {"shape": "Member"}, "max": 50, "min": 0}, "MeteringAccountId": {"type": "string", "pattern": "[0-9]{12}"}, "MonthlyCostEstimate": {"type": "double", "min": 0}, "MonthlySchedule": {"type": "structure", "required": ["day", "startTime"], "members": {"day": {"shape": "Day", "documentation": "<p>The monthly schedule's day.</p>"}, "startTime": {"shape": "Time", "documentation": "<p>The monthly schedule's start time.</p>"}}, "documentation": "<p>A monthly schedule.</p>"}, "NetworkPath": {"type": "structure", "members": {"steps": {"shape": "StepList", "documentation": "<p>The details on the steps in the network path.</p>"}}, "documentation": "<p>Information on the network path associated with a finding.</p>"}, "NetworkProtocol": {"type": "string", "enum": ["TCP", "UDP"]}, "NetworkReachabilityDetails": {"type": "structure", "required": ["networkPath", "openPortRange", "protocol"], "members": {"networkPath": {"shape": "NetworkPath", "documentation": "<p>An object that contains details about a network path associated with a finding.</p>"}, "openPortRange": {"shape": "PortRange", "documentation": "<p>An object that contains details about the open port range associated with a finding.</p>"}, "protocol": {"shape": "NetworkProtocol", "documentation": "<p>The protocol associated with a finding.</p>"}}, "documentation": "<p>Contains the details of a network reachability finding.</p>"}, "NextToken": {"type": "string", "max": 1000000, "min": 0}, "NonEmptyString": {"type": "string", "min": 1}, "NonEmptyStringList": {"type": "list", "member": {"shape": "NonEmptyString"}}, "NumberFilter": {"type": "structure", "members": {"lowerInclusive": {"shape": "Double", "documentation": "<p>The lowest number to be included in the filter.</p>"}, "upperInclusive": {"shape": "Double", "documentation": "<p>The highest number to be included in the filter.</p>"}}, "documentation": "<p>An object that describes the details of a number filter.</p>"}, "NumberFilterList": {"type": "list", "member": {"shape": "NumberFilter"}, "max": 10, "min": 1}, "OneAccountIdFilterList": {"type": "list", "member": {"shape": "CisStringFilter"}, "max": 1, "min": 1}, "OneTimeSchedule": {"type": "structure", "members": {}, "documentation": "<p>A one time schedule.</p>"}, "Operation": {"type": "string", "enum": ["ENABLE_SCANNING", "DISABLE_SCANNING", "ENABLE_REPOSITORY", "DISABLE_REPOSITORY"]}, "OwnerId": {"type": "string", "max": 34, "min": 12, "pattern": "(^\\d{12}$)|(^o-[a-z0-9]{10,32}$)"}, "PackageAggregation": {"type": "structure", "members": {"packageNames": {"shape": "StringFilterList", "documentation": "<p>The names of packages to aggregate findings on.</p>"}, "sortBy": {"shape": "PackageSortBy", "documentation": "<p>The value to sort results by.</p>"}, "sortOrder": {"shape": "SortOrder", "documentation": "<p>The order to sort results by.</p>"}}, "documentation": "<p>The details that define an aggregation based on operating system package type.</p>"}, "PackageAggregationResponse": {"type": "structure", "required": ["packageName"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The ID of the Amazon Web Services account associated with the findings.</p>"}, "packageName": {"shape": "NonEmptyString", "documentation": "<p>The name of the operating system package.</p>"}, "severityCounts": {"shape": "SeverityCounts", "documentation": "<p>An object that contains the count of matched findings per severity.</p>"}}, "documentation": "<p>A response that contains the results of a finding aggregation by image layer.</p>"}, "PackageArchitecture": {"type": "string", "max": 64, "min": 1}, "PackageEpoch": {"type": "integer"}, "PackageFilter": {"type": "structure", "members": {"architecture": {"shape": "StringFilter", "documentation": "<p>An object that contains details on the package architecture type to filter on.</p>"}, "epoch": {"shape": "NumberFilter", "documentation": "<p>An object that contains details on the package epoch to filter on.</p>"}, "filePath": {"shape": "StringFilter", "documentation": "<p>An object that contains details on the package file path to filter on.</p>"}, "name": {"shape": "StringFilter", "documentation": "<p>An object that contains details on the name of the package to filter on.</p>"}, "release": {"shape": "StringFilter", "documentation": "<p>An object that contains details on the package release to filter on.</p>"}, "sourceLambdaLayerArn": {"shape": "StringFilter", "documentation": "<p>An object that describes the details of a string filter.</p>"}, "sourceLayerHash": {"shape": "StringFilter", "documentation": "<p>An object that contains details on the source layer hash to filter on.</p>"}, "version": {"shape": "StringFilter", "documentation": "<p>The package version to filter on.</p>"}}, "documentation": "<p>Contains information on the details of a package filter.</p>"}, "PackageFilterList": {"type": "list", "member": {"shape": "PackageFilter"}, "max": 10, "min": 1}, "PackageManager": {"type": "string", "enum": ["BUNDLER", "CARGO", "COMPOSER", "NPM", "NUGET", "PIPENV", "POETRY", "YARN", "GOBINARY", "GOMOD", "JAR", "OS", "PIP", "PYTHONPKG", "NODEPKG", "POM", "GEMSPEC", "DOTNET_CORE"]}, "PackageName": {"type": "string", "max": 1024, "min": 1}, "PackageRelease": {"type": "string", "max": 1024, "min": 1}, "PackageSortBy": {"type": "string", "enum": ["CRITICAL", "HIGH", "ALL"]}, "PackageType": {"type": "string", "enum": ["IMAGE", "ZIP"]}, "PackageVersion": {"type": "string", "max": 1024, "min": 1}, "PackageVulnerabilityDetails": {"type": "structure", "required": ["source", "vulnerabilityId"], "members": {"cvss": {"shape": "CvssScoreList", "documentation": "<p>An object that contains details about the CVSS score of a finding.</p>"}, "referenceUrls": {"shape": "NonEmptyStringList", "documentation": "<p>One or more URLs that contain details about this vulnerability type.</p>"}, "relatedVulnerabilities": {"shape": "VulnerabilityIdList", "documentation": "<p>One or more vulnerabilities related to the one identified in this finding.</p>"}, "source": {"shape": "NonEmptyString", "documentation": "<p>The source of the vulnerability information.</p>"}, "sourceUrl": {"shape": "NonEmptyString", "documentation": "<p>A URL to the source of the vulnerability information.</p>"}, "vendorCreatedAt": {"shape": "DateTimeTimestamp", "documentation": "<p>The date and time that this vulnerability was first added to the vendor's database.</p>"}, "vendorSeverity": {"shape": "NonEmptyString", "documentation": "<p>The severity the vendor has given to this vulnerability type.</p>"}, "vendorUpdatedAt": {"shape": "DateTimeTimestamp", "documentation": "<p>The date and time the vendor last updated this vulnerability in their database.</p>"}, "vulnerabilityId": {"shape": "VulnerabilityId", "documentation": "<p>The ID given to this vulnerability.</p>"}, "vulnerablePackages": {"shape": "VulnerablePackageList", "documentation": "<p>The packages impacted by this vulnerability.</p>"}}, "documentation": "<p>Information about a package vulnerability finding.</p>"}, "Path": {"type": "string", "max": 512, "min": 1, "pattern": "^(?:/(?:\\.[-\\w]+|[-\\w]+(?:\\.[-\\w]+)?))+/?$"}, "PathList": {"type": "list", "member": {"shape": "Path"}, "max": 5, "min": 0}, "PeriodicScanConfiguration": {"type": "structure", "members": {"frequency": {"shape": "PeriodicScanFrequency", "documentation": "<p>The frequency at which periodic scans are performed (such as weekly or monthly).</p> <p>If you don't provide the <code>frequencyExpression</code> Amazon Inspector chooses day for the scan to run. If you provide the <code>frequencyExpression</code>, the schedule must match the specified <code>frequency</code>.</p>"}, "frequencyExpression": {"shape": "FrequencyExpression", "documentation": "<p>The schedule expression for periodic scans, in cron format.</p>"}}, "documentation": "<p>Configuration settings for periodic scans that run on a scheduled basis.</p>"}, "PeriodicScanFrequency": {"type": "string", "enum": ["WEEKLY", "MONTHLY", "NEVER"]}, "Permission": {"type": "structure", "required": ["operation", "service"], "members": {"operation": {"shape": "Operation", "documentation": "<p>The operations that can be performed with the given permissions.</p>"}, "service": {"shape": "Service", "documentation": "<p>The services that the permissions allow an account to perform the given operations for.</p>"}}, "documentation": "<p>Contains information on the permissions an account has within Amazon Inspector.</p>"}, "Permissions": {"type": "list", "member": {"shape": "Permission"}, "max": 1024, "min": 0}, "Platform": {"type": "string", "max": 1024, "min": 1}, "PlatformFilterList": {"type": "list", "member": {"shape": "CisStringFilter"}, "max": 10, "min": 1}, "PlatformVersion": {"type": "string", "max": 8, "min": 0}, "Port": {"type": "integer", "box": true, "max": 65535, "min": 0}, "PortRange": {"type": "structure", "required": ["begin", "end"], "members": {"begin": {"shape": "Port", "documentation": "<p>The beginning port in a port range.</p>"}, "end": {"shape": "Port", "documentation": "<p>The ending port in a port range.</p>"}}, "documentation": "<p>Details about the port range associated with a finding.</p>"}, "PortRangeFilter": {"type": "structure", "members": {"beginInclusive": {"shape": "Port", "documentation": "<p>The port number the port range begins at.</p>"}, "endInclusive": {"shape": "Port", "documentation": "<p>The port number the port range ends at.</p>"}}, "documentation": "<p>An object that describes the details of a port range filter.</p>"}, "PortRangeFilterList": {"type": "list", "member": {"shape": "PortRangeFilter"}, "max": 10, "min": 1}, "Product": {"type": "string", "max": 32, "min": 0}, "ProjectCodeSecurityScanConfiguration": {"type": "structure", "members": {"continuousIntegrationScanConfigurations": {"shape": "ProjectContinuousIntegrationScanConfigurationList", "documentation": "<p>The continuous integration scan configurations applied to the project.</p>"}, "periodicScanConfigurations": {"shape": "ProjectPeriodicScanConfigurationList", "documentation": "<p>The periodic scan configurations applied to the project.</p>"}}, "documentation": "<p>Contains the scan configuration settings applied to a specific project in a code repository.</p>"}, "ProjectContinuousIntegrationScanConfiguration": {"type": "structure", "members": {"ruleSetCategories": {"shape": "RuleSetCategories", "documentation": "<p>The categories of security rules applied during continuous integration scans for the project.</p>"}, "supportedEvent": {"shape": "ContinuousIntegrationScanEvent", "documentation": "<p>The repository event that triggers continuous integration scans for the project.</p>"}}, "documentation": "<p>Contains the continuous integration scan configuration settings applied to a specific project.</p>"}, "ProjectContinuousIntegrationScanConfigurationList": {"type": "list", "member": {"shape": "ProjectContinuousIntegrationScanConfiguration"}}, "ProjectId": {"type": "string", "pattern": "^project-[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$"}, "ProjectPeriodicScanConfiguration": {"type": "structure", "members": {"frequencyExpression": {"shape": "FrequencyExpression", "documentation": "<p>The schedule expression for periodic scans, in cron format, applied to the project.</p>"}, "ruleSetCategories": {"shape": "RuleSetCategories", "documentation": "<p>The categories of security rules applied during periodic scans for the project.</p>"}}, "documentation": "<p>Contains the periodic scan configuration settings applied to a specific project.</p>"}, "ProjectPeriodicScanConfigurationList": {"type": "list", "member": {"shape": "ProjectPeriodicScanConfiguration"}}, "ProjectSelectionScope": {"type": "string", "enum": ["ALL"]}, "Reason": {"type": "string", "max": 1024, "min": 0}, "Recommendation": {"type": "structure", "members": {"Url": {"shape": "NonEmptyString", "documentation": "<p>The URL address to the CVE remediation recommendations.</p>"}, "text": {"shape": "NonEmptyString", "documentation": "<p>The recommended course of action to remediate the finding.</p>"}}, "documentation": "<p>Details about the recommended course of action to remediate the finding.</p>"}, "ReferenceUrls": {"type": "list", "member": {"shape": "NonEmptyString"}, "max": 10, "min": 1}, "RelatedVulnerabilities": {"type": "list", "member": {"shape": "RelatedVulnerability"}, "max": 100, "min": 0}, "RelatedVulnerability": {"type": "string", "min": 0}, "RelationshipStatus": {"type": "string", "enum": ["CREATED", "INVITED", "DISABLED", "ENABLED", "REMOVED", "RESIGNED", "DELETED", "EMAIL_VERIFICATION_IN_PROGRESS", "EMAIL_VERIFICATION_FAILED", "REGION_DISABLED", "ACCOUNT_SUSPENDED", "CANNOT_CREATE_DETECTOR_IN_ORG_MASTER"]}, "Remediation": {"type": "structure", "members": {"recommendation": {"shape": "Recommendation", "documentation": "<p>An object that contains information about the recommended course of action to remediate the finding.</p>"}}, "documentation": "<p>Information on how to remediate a finding.</p>"}, "ReportFormat": {"type": "string", "enum": ["CSV", "JSON"]}, "ReportId": {"type": "string", "pattern": "\\b[a-f0-9]{8}\\b-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-\\b[a-f0-9]{12}\\b"}, "ReportTargetAccounts": {"type": "list", "member": {"shape": "AccountId"}, "max": 1, "min": 0}, "ReportingErrorCode": {"type": "string", "enum": ["INTERNAL_ERROR", "INVALID_PERMISSIONS", "NO_FINDINGS_FOUND", "BUCKET_NOT_FOUND", "INCOMPATIBLE_BUCKET_REGION", "MALFORMED_KMS_KEY"]}, "RepositoryAggregation": {"type": "structure", "members": {"repositories": {"shape": "StringFilterList", "documentation": "<p>The names of repositories to aggregate findings on.</p>"}, "sortBy": {"shape": "RepositorySortBy", "documentation": "<p>The value to sort results by.</p>"}, "sortOrder": {"shape": "SortOrder", "documentation": "<p>The order to sort results by.</p>"}}, "documentation": "<p>The details that define an aggregation based on repository.</p>"}, "RepositoryAggregationResponse": {"type": "structure", "required": ["repository"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The ID of the Amazon Web Services account associated with the findings.</p>"}, "affectedImages": {"shape": "<PERSON>", "documentation": "<p>The number of container images impacted by the findings.</p>"}, "repository": {"shape": "NonEmptyString", "documentation": "<p>The name of the repository associated with the findings.</p>"}, "severityCounts": {"shape": "SeverityCounts", "documentation": "<p>An object that represent the count of matched findings per severity.</p>"}}, "documentation": "<p>A response that contains details on the results of a finding aggregation by repository.</p>"}, "RepositorySortBy": {"type": "string", "enum": ["CRITICAL", "HIGH", "ALL", "AFFECTED_IMAGES"]}, "ResetEncryptionKeyRequest": {"type": "structure", "required": ["resourceType", "scanType"], "members": {"resourceType": {"shape": "ResourceType", "documentation": "<p>The resource type the key encrypts.</p>"}, "scanType": {"shape": "ScanType", "documentation": "<p>The scan type the key encrypts.</p>"}}}, "ResetEncryptionKeyResponse": {"type": "structure", "members": {}}, "Resource": {"type": "structure", "required": ["id", "type"], "members": {"details": {"shape": "ResourceDetails", "documentation": "<p>An object that contains details about the resource involved in a finding.</p>"}, "id": {"shape": "NonEmptyString", "documentation": "<p>The ID of the resource.</p>"}, "partition": {"shape": "NonEmptyString", "documentation": "<p>The partition of the resource.</p>"}, "region": {"shape": "NonEmptyString", "documentation": "<p>The Amazon Web Services Region the impacted resource is located in.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags attached to the resource.</p>"}, "type": {"shape": "ResourceType", "documentation": "<p>The type of resource.</p>"}}, "documentation": "<p>Details about the resource involved in a finding.</p>"}, "ResourceDetails": {"type": "structure", "members": {"awsEc2Instance": {"shape": "AwsEc2InstanceDetails", "documentation": "<p>An object that contains details about the Amazon EC2 instance involved in the finding.</p>"}, "awsEcrContainerImage": {"shape": "AwsEcrContainerImageDetails", "documentation": "<p>An object that contains details about the Amazon ECR container image involved in the finding.</p>"}, "awsLambdaFunction": {"shape": "AwsLambdaFunctionDetails", "documentation": "<p>A summary of the information about an Amazon Web Services Lambda function affected by a finding.</p>"}, "codeRepository": {"shape": "CodeRepositoryDetails", "documentation": "<p>Contains details about a code repository resource associated with a finding.</p>"}}, "documentation": "<p>Contains details about the resource involved in the finding.</p>"}, "ResourceFilterCriteria": {"type": "structure", "members": {"accountId": {"shape": "ResourceStringFilterList", "documentation": "<p>The account IDs used as resource filter criteria.</p>"}, "ec2InstanceTags": {"shape": "ResourceMapFilterList", "documentation": "<p>The EC2 instance tags used as resource filter criteria.</p>"}, "ecrImageTags": {"shape": "ResourceStringFilterList", "documentation": "<p>The ECR image tags used as resource filter criteria.</p>"}, "ecrRepositoryName": {"shape": "ResourceStringFilterList", "documentation": "<p>The ECR repository names used as resource filter criteria.</p>"}, "lambdaFunctionName": {"shape": "ResourceStringFilterList", "documentation": "<p>The Amazon Web Services Lambda function name used as resource filter criteria.</p>"}, "lambdaFunctionTags": {"shape": "ResourceMapFilterList", "documentation": "<p>The Amazon Web Services Lambda function tags used as resource filter criteria.</p>"}, "resourceId": {"shape": "ResourceStringFilterList", "documentation": "<p>The resource IDs used as resource filter criteria.</p>"}, "resourceType": {"shape": "ResourceStringFilterList", "documentation": "<p>The resource types used as resource filter criteria.</p>"}}, "documentation": "<p>The resource filter criteria for a Software bill of materials (SBOM) report.</p>"}, "ResourceId": {"type": "string", "max": 341, "min": 10, "pattern": "(^arn:.*:ecr:.*:\\d{12}:repository\\/(?:[a-z0-9]+(?:[._-][a-z0-9]+)*\\/)*[a-z0-9]+(?:[._-][a-z0-9]+)*(\\/sha256:[a-z0-9]{64})?$)|(^i-([a-z0-9]{8}|[a-z0-9]{17}|\\\\*)$|(^arn:(aws[a-zA-Z-]*)?:lambda:[a-z]{2}(-gov)?-[a-z]+-\\d{1}:\\d{12}:function:[a-zA-Z0-9-_\\.]+(:(\\$LATEST|[a-zA-Z0-9-_]+))?$)|(^arn:(aws[a-zA-Z-]*)?:inspector2:[a-z]{2}(-gov)?-[a-z]+-\\d{1}:\\d{12}:codesecurity-integration\\/[a-f0-9-]{36}\\/project-[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$))"}, "ResourceIdFilterList": {"type": "list", "member": {"shape": "CisStringFilter"}, "max": 10, "min": 1}, "ResourceList": {"type": "list", "member": {"shape": "Resource"}, "max": 10, "min": 1}, "ResourceMapComparison": {"type": "string", "enum": ["EQUALS"]}, "ResourceMapFilter": {"type": "structure", "required": ["comparison", "key"], "members": {"comparison": {"shape": "ResourceMapComparison", "documentation": "<p>The filter's comparison.</p>"}, "key": {"shape": "NonEmptyString", "documentation": "<p>The filter's key.</p>"}, "value": {"shape": "NonEmptyString", "documentation": "<p>The filter's value.</p>"}}, "documentation": "<p>A resource map filter for a software bill of material report.</p>"}, "ResourceMapFilterList": {"type": "list", "member": {"shape": "ResourceMapFilter"}, "max": 10, "min": 1}, "ResourceNotFoundException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The operation tried to access an invalid resource. Make sure the resource is specified correctly.</p>", "error": {"httpStatusCode": 404, "senderFault": true}, "exception": true}, "ResourceScanMetadata": {"type": "structure", "members": {"codeRepository": {"shape": "CodeRepositoryMetadata", "documentation": "<p>Contains metadata about scan coverage for a code repository resource.</p>"}, "ec2": {"shape": "Ec2Metadata", "documentation": "<p>An object that contains metadata details for an Amazon EC2 instance.</p>"}, "ecrImage": {"shape": "EcrContainerImageMetadata", "documentation": "<p>An object that contains details about the container metadata for an Amazon ECR image.</p>"}, "ecrRepository": {"shape": "EcrRepositoryMetadata", "documentation": "<p>An object that contains details about the repository an Amazon ECR image resides in.</p>"}, "lambdaFunction": {"shape": "LambdaFunctionMetadata", "documentation": "<p>An object that contains metadata details for an Amazon Web Services Lambda function.</p>"}}, "documentation": "<p>An object that contains details about the metadata for an Amazon ECR resource.</p>"}, "ResourceScanType": {"type": "string", "enum": ["EC2", "ECR", "LAMBDA", "LAMBDA_CODE", "CODE_REPOSITORY"]}, "ResourceState": {"type": "structure", "required": ["ec2", "ecr"], "members": {"codeRepository": {"shape": "State"}, "ec2": {"shape": "State", "documentation": "<p>An object detailing the state of Amazon Inspector scanning for Amazon EC2 resources.</p>"}, "ecr": {"shape": "State", "documentation": "<p>An object detailing the state of Amazon Inspector scanning for Amazon ECR resources.</p>"}, "lambda": {"shape": "State", "documentation": "<p>An object that described the state of Amazon Inspector scans for an account.</p>"}, "lambdaCode": {"shape": "State", "documentation": "<p>An object that described the state of Amazon Inspector scans for an account.</p>"}}, "documentation": "<p>Details the state of Amazon Inspector for each resource type Amazon Inspector scans.</p>"}, "ResourceStatus": {"type": "structure", "required": ["ec2", "ecr"], "members": {"codeRepository": {"shape": "Status", "documentation": "<p>The status of Amazon Inspector scanning for code repositories.</p>"}, "ec2": {"shape": "Status", "documentation": "<p>The status of Amazon Inspector scanning for Amazon EC2 resources.</p>"}, "ecr": {"shape": "Status", "documentation": "<p>The status of Amazon Inspector scanning for Amazon ECR resources.</p>"}, "lambda": {"shape": "Status", "documentation": "<p>The status of Amazon Inspector scanning for Amazon Web Services Lambda function.</p>"}, "lambdaCode": {"shape": "Status", "documentation": "<p>The status of Amazon Inspector scanning for custom application code for Amazon Web Services Lambda functions. </p>"}}, "documentation": "<p>Details the status of Amazon Inspector for each resource type Amazon Inspector scans.</p>"}, "ResourceStringComparison": {"type": "string", "enum": ["EQUALS", "NOT_EQUALS"]}, "ResourceStringFilter": {"type": "structure", "required": ["comparison", "value"], "members": {"comparison": {"shape": "ResourceStringComparison", "documentation": "<p>The filter's comparison.</p>"}, "value": {"shape": "ResourceStringInput", "documentation": "<p>The filter's value.</p>"}}, "documentation": "<p>A resource string filter for a software bill of materials report.</p>"}, "ResourceStringFilterList": {"type": "list", "member": {"shape": "ResourceStringFilter"}, "max": 10, "min": 1}, "ResourceStringInput": {"type": "string", "max": 1024, "min": 1}, "ResourceTagFilterList": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON><PERSON>"}, "max": 10, "min": 1}, "ResourceType": {"type": "string", "enum": ["AWS_EC2_INSTANCE", "AWS_ECR_CONTAINER_IMAGE", "AWS_ECR_REPOSITORY", "AWS_LAMBDA_FUNCTION", "CODE_REPOSITORY"]}, "RiskScore": {"type": "integer", "box": true}, "RuleId": {"type": "string", "max": 500, "min": 1}, "RuleSetCategories": {"type": "list", "member": {"shape": "RuleSetCategory"}, "max": 3, "min": 1}, "RuleSetCategory": {"type": "string", "enum": ["SAST", "IAC", "SCA"]}, "Runtime": {"type": "string", "enum": ["NODEJS", "NODEJS_12_X", "NODEJS_14_X", "NODEJS_16_X", "JAVA_8", "JAVA_8_AL2", "JAVA_11", "PYTHON_3_7", "PYTHON_3_8", "PYTHON_3_9", "UNSUPPORTED", "NODEJS_18_X", "GO_1_X", "JAVA_17", "PYTHON_3_10", "PYTHON_3_11", "DOTNETCORE_3_1", "DOTNET_6", "DOTNET_7", "RUBY_2_7", "RUBY_3_2"]}, "SbomReportFormat": {"type": "string", "enum": ["CYCLONEDX_1_4", "SPDX_2_3"]}, "ScanConfigurationArn": {"type": "string", "documentation": "<p>arn:aws:inspector2:<region>:<account-id>:owner/<owner-id>/codesecurity-configuration/<uuid></p>", "pattern": "^arn:(aws[a-zA-Z-]*)?:inspector2:[a-z]{2}(-gov)?-[a-z]+-\\d{1}:\\d{12}:owner/(\\d{12}|o-[a-z0-9]{10,32})/codesecurity-configuration/[a-f0-9-]{36}$"}, "ScanConfigurationName": {"type": "string", "max": 60, "min": 1, "pattern": "^[a-zA-Z0-9-_$:.]*$"}, "ScanMode": {"type": "string", "enum": ["EC2_SSM_AGENT_BASED", "EC2_AGENTLESS"]}, "ScanStatus": {"type": "structure", "required": ["reason", "statusCode"], "members": {"reason": {"shape": "ScanStatusReason", "documentation": "<p>The scan status. Possible return values and descriptions are: </p> <p> <code>ACCESS_DENIED</code> - Resource access policy restricting Amazon Inspector access. Please update the IAM policy.</p> <p> <code>ACCESS_DENIED_TO_ENCRYPTION_KEY</code> - The KMS key policy doesn't allow Amazon Inspector access. Update the key policy.</p> <p> <code>DEEP_INSPECTION_COLLECTION_TIME_LIMIT_EXCEEDED</code> - Amazon Inspector failed to extract the package inventory because the package collection time exceeding the maximum threshold of 15 minutes.</p> <p> <code>DEEP_INSPECTION_DAILY_SSM_INVENTORY_LIMIT_EXCEEDED</code> - The SSM agent couldn't send inventory to Amazon Inspector because the SSM quota for Inventory data collected per instance per day has already been reached for this instance.</p> <p> <code>DEEP_INSPECTION_NO_INVENTORY</code> - The Amazon Inspector plugin hasn't yet been able to collect an inventory of packages for this instance. This is usually the result of a pending scan, however, if this status persists after 6 hours, use SSM to ensure that the required Amazon Inspector associations exist and are running for the instance.</p> <p> <code>DEEP_INSPECTION_PACKAGE_COLLECTION_LIMIT_EXCEEDED</code> - The instance has exceeded the 5000 package limit for Amazon Inspector Deep inspection. To resume Deep inspection for this instance you can try to adjust the custom paths associated with the account.</p> <p> <code>EC2_INSTANCE_STOPPED</code> - This EC2 instance is in a stopped state, therefore, Amazon Inspector will pause scanning. The existing findings will continue to exist until the instance is terminated. Once the instance is re-started, Inspector will automatically start scanning the instance again. Please note that you will not be charged for this instance while it's in a stopped state.</p> <p> <code>EXCLUDED_BY_TAG</code> - This resource was not scanned because it has been excluded by a tag.</p> <p> <code>IMAGE_SIZE_EXCEEDED</code> - Reserved for future use.</p> <p> <code>INTEGRATION_CONNNECTION_LOST</code> - Amazon Inspector couldn't communicate with the source code management platform.</p> <p> <code>INTERNAL_ERROR</code> - Amazon Inspector has encountered an internal error for this resource. Amazon Inspector service will automatically resolve the issue and resume the scanning. No action required from the user.</p> <p> <code>NO INVENTORY</code> - Amazon Inspector couldn't find software application inventory to scan for vulnerabilities. This might be caused due to required Amazon Inspector associations being deleted or failing to run on your resource. Please verify the status of <code>InspectorInventoryCollection-do-not-delete</code> association in the SSM console for the resource. Additionally, you can verify the instance's inventory in the SSM Fleet Manager console.</p> <p> <code>NO_RESOURCES_FOUND</code> - Reserved for future use.</p> <p> <code>NO_SCAN_CONFIGURATION_ASSOCIATED</code> - The code repository resource doesn't have an associated scan configuration.</p> <p> <code>PENDING_DISABLE</code> - This resource is pending cleanup during disablement. The customer will not be billed while a resource is in the pending disable status.</p> <p> <code>PENDING_INITIAL_SCAN</code> - This resource has been identified for scanning, results will be available soon.</p> <p> <code>RESOURCE_TERMINATED</code> - This resource has been terminated. The findings and coverage associated with this resource are in the process of being cleaned up.</p> <p> <code>SCAN_ELIGIBILITY_EXPIRED</code> - The configured scan duration has lapsed for this image.</p> <p> <code>SCAN_FREQUENCY_MANUAL</code> - This image will not be covered by Amazon Inspector due to the repository scan frequency configuration.</p> <p> <code>SCAN_FREQUENCY_SCAN_ON_PUSH</code> - This image will be scanned one time and will not new findings because of the scan frequency configuration.</p> <p> <code>SCAN_IN_PROGRESS</code> - The resource is currently being scanned.</p> <p> <code>STALE_INVENTORY</code> - Amazon Inspector wasn't able to collect an updated software application inventory in the last 7 days. Please confirm the required Amazon Inspector associations still exist and you can still see an updated inventory in the SSM console.</p> <p> <code>SUCCESSFUL</code> - The scan was successful.</p> <p> <code>UNMANAGED_EC2_INSTANCE</code> - The EC2 instance is not managed by SSM, please use the following SSM automation to remediate the issue: <a href=\"https://docs.aws.amazon.com/systems-manager-automation-runbooks/latest/userguide/automation-awssupport-troubleshoot-managed-instance.html\">https://docs.aws.amazon.com/systems-manager-automation-runbooks/latest/userguide/automation-awssupport-troubleshoot-managed-instance.html</a>. Once the instance becomes managed by SSM, Inspector will automatically begin scanning this instance. </p> <p> <code>UNSUPPORTED_CONFIG_FILE</code> - Reserved for future use.</p> <p> <code>UNSUPPORTED_LANGUAGE</code> - The scan was unsuccessful because the repository contains files in an unsupported programming language.</p> <p> <code>UNSUPPORTED_MEDIA_TYPE </code>- The ECR image has an unsupported media type.</p> <p> <code>UNSUPPORTED_OS</code> - Amazon Inspector does not support this OS, architecture, or image manifest type at this time. To see a complete list of supported operating systems see: <a href=\" https://docs.aws.amazon.com/inspector/latest/user/supported.html\">https://docs.aws.amazon.com/inspector/latest/user/supported.html</a>.</p> <p> <code>UNSUPPORTED_RUNTIME</code> - The function was not scanned because it has an unsupported runtime. To see a complete list of supported runtimes see: <a href=\" https://docs.aws.amazon.com/inspector/latest/user/supported.html\">https://docs.aws.amazon.com/inspector/latest/user/supported.html</a>.</p>"}, "statusCode": {"shape": "ScanStatusCode", "documentation": "<p>The status code of the scan.</p>"}}, "documentation": "<p>The status of the scan.</p>"}, "ScanStatusCode": {"type": "string", "enum": ["ACTIVE", "INACTIVE"]}, "ScanStatusReason": {"type": "string", "enum": ["PENDING_INITIAL_SCAN", "ACCESS_DENIED", "INTERNAL_ERROR", "UNMANAGED_EC2_INSTANCE", "UNSUPPORTED_OS", "SCAN_ELIGIBILITY_EXPIRED", "RESOURCE_TERMINATED", "SUCCESSFUL", "NO_RESOURCES_FOUND", "IMAGE_SIZE_EXCEEDED", "SCAN_FREQUENCY_MANUAL", "SCAN_FREQUENCY_SCAN_ON_PUSH", "EC2_INSTANCE_STOPPED", "PENDING_DISABLE", "NO_INVENTORY", "STALE_INVENTORY", "EXCLUDED_BY_TAG", "UNSUPPORTED_RUNTIME", "UNSUPPORTED_MEDIA_TYPE", "UNSUPPORTED_CONFIG_FILE", "DEEP_INSPECTION_PACKAGE_COLLECTION_LIMIT_EXCEEDED", "DEEP_INSPECTION_DAILY_SSM_INVENTORY_LIMIT_EXCEEDED", "DEEP_INSPECTION_COLLECTION_TIME_LIMIT_EXCEEDED", "DEEP_INSPECTION_NO_INVENTORY", "AGENTLESS_INSTANCE_STORAGE_LIMIT_EXCEEDED", "AGENTLESS_INSTANCE_COLLECTION_TIME_LIMIT_EXCEEDED", "PENDING_REVIVAL_SCAN", "INTEGRATION_CONNECTION_LOST", "ACCESS_DENIED_TO_ENCRYPTION_KEY", "UNSUPPORTED_LANGUAGE", "NO_SCAN_CONFIGURATION_ASSOCIATED", "SCAN_IN_PROGRESS"]}, "ScanType": {"type": "string", "enum": ["NETWORK", "PACKAGE", "CODE"]}, "Schedule": {"type": "structure", "members": {"daily": {"shape": "DailySchedule", "documentation": "<p>The schedule's daily.</p>"}, "monthly": {"shape": "MonthlySchedule", "documentation": "<p>The schedule's monthly.</p>"}, "oneTime": {"shape": "OneTimeSchedule", "documentation": "<p>The schedule's one time.</p>"}, "weekly": {"shape": "WeeklySchedule", "documentation": "<p>The schedule's weekly.</p>"}}, "documentation": "<p>A schedule.</p>", "union": true}, "ScopeSettings": {"type": "structure", "members": {"projectSelectionScope": {"shape": "ProjectSelectionScope", "documentation": "<p>The scope of projects to be selected for scanning within the integrated repositories. Setting the value to <code>ALL</code> applies the scope settings to all existing and future projects imported into Amazon Inspector.</p>"}}, "documentation": "<p>Defines the scope of repositories to be included in code security scans.</p>"}, "SearchVulnerabilitiesFilterCriteria": {"type": "structure", "required": ["vulnerabilityIds"], "members": {"vulnerabilityIds": {"shape": "VulnIdList", "documentation": "<p>The IDs for specific vulnerabilities.</p>"}}, "documentation": "<p>Details on the criteria used to define the filter for a vulnerability search. </p>"}, "SearchVulnerabilitiesRequest": {"type": "structure", "required": ["filterCriteria"], "members": {"filterCriteria": {"shape": "SearchVulnerabilitiesFilterCriteria", "documentation": "<p>The criteria used to filter the results of a vulnerability search.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request to a list action. For subsequent calls, use the <code>NextToken</code> value returned from the previous request to continue listing results after the first page.</p>"}}}, "SearchVulnerabilitiesResponse": {"type": "structure", "required": ["vulnerabilities"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The pagination parameter to be used on the next list operation to retrieve more items.</p>"}, "vulnerabilities": {"shape": "Vulnerabilities", "documentation": "<p>Details about the listed vulnerability.</p>"}}}, "SecurityGroupId": {"type": "string", "pattern": "^sg-([a-z0-9]{8}|[a-z0-9]{17}|\\*)$"}, "SecurityGroupIdList": {"type": "list", "member": {"shape": "SecurityGroupId"}, "max": 5, "min": 0}, "SendCisSessionHealthRequest": {"type": "structure", "required": ["scanJobId", "sessionToken"], "members": {"scanJobId": {"shape": "UUID", "documentation": "<p>A unique identifier for the scan job.</p>"}, "sessionToken": {"shape": "UUID", "documentation": "<p>The unique token that identifies the CIS session.</p>"}}}, "SendCisSessionHealthResponse": {"type": "structure", "members": {}}, "SendCisSessionTelemetryRequest": {"type": "structure", "required": ["messages", "scanJobId", "sessionToken"], "members": {"messages": {"shape": "CisSessionMessages", "documentation": "<p>The CIS session telemetry messages.</p>"}, "scanJobId": {"shape": "UUID", "documentation": "<p>A unique identifier for the scan job.</p>"}, "sessionToken": {"shape": "UUID", "documentation": "<p>The unique token that identifies the CIS session.</p>"}}}, "SendCisSessionTelemetryResponse": {"type": "structure", "members": {}}, "Service": {"type": "string", "enum": ["EC2", "ECR", "LAMBDA"]}, "ServiceQuotaExceededException": {"type": "structure", "required": ["message", "resourceId"], "members": {"message": {"shape": "String"}, "resourceId": {"shape": "String", "documentation": "<p>The ID of the resource that exceeds a service quota.</p>"}}, "documentation": "<p>You have exceeded your service quota. To perform the requested action, remove some of the relevant resources, or use Service Quotas to request a service quota increase.</p>", "error": {"httpStatusCode": 402, "senderFault": true}, "exception": true}, "Severity": {"type": "string", "enum": ["INFORMATIONAL", "LOW", "MEDIUM", "HIGH", "CRITICAL", "UNTRIAGED"]}, "SeverityCounts": {"type": "structure", "members": {"all": {"shape": "<PERSON>", "documentation": "<p>The total count of findings from all severities.</p>"}, "critical": {"shape": "<PERSON>", "documentation": "<p>The total count of critical severity findings.</p>"}, "high": {"shape": "<PERSON>", "documentation": "<p>The total count of high severity findings.</p>"}, "medium": {"shape": "<PERSON>", "documentation": "<p>The total count of medium severity findings.</p>"}}, "documentation": "<p>An object that contains the counts of aggregated finding per severity.</p>"}, "SortCriteria": {"type": "structure", "required": ["field", "sortOrder"], "members": {"field": {"shape": "SortField", "documentation": "<p>The finding detail field by which results are sorted.</p>"}, "sortOrder": {"shape": "SortOrder", "documentation": "<p>The order by which findings are sorted.</p>"}}, "documentation": "<p>Details about the criteria used to sort finding results.</p>"}, "SortField": {"type": "string", "enum": ["AWS_ACCOUNT_ID", "FINDING_TYPE", "SEVERITY", "FIRST_OBSERVED_AT", "LAST_OBSERVED_AT", "FINDING_STATUS", "RESOURCE_TYPE", "ECR_IMAGE_PUSHED_AT", "ECR_IMAGE_REPOSITORY_NAME", "ECR_IMAGE_REGISTRY", "NETWORK_PROTOCOL", "COMPONENT_TYPE", "VULNERABILITY_ID", "VULNERABILITY_SOURCE", "INSPECTOR_SCORE", "VENDOR_SEVERITY", "EPSS_SCORE"]}, "SortOrder": {"type": "string", "enum": ["ASC", "DESC"]}, "SourceLayerHash": {"type": "string", "max": 71, "min": 71, "pattern": "^sha256:[a-z0-9]{64}$"}, "StartCisSessionMessage": {"type": "structure", "required": ["sessionToken"], "members": {"sessionToken": {"shape": "UUID", "documentation": "<p>The unique token that identifies the CIS session.</p>"}}, "documentation": "<p>The start CIS session message.</p>"}, "StartCisSessionRequest": {"type": "structure", "required": ["message", "scanJobId"], "members": {"message": {"shape": "StartCisSessionMessage", "documentation": "<p>The start CIS session message.</p>"}, "scanJobId": {"shape": "UUID", "documentation": "<p>A unique identifier for the scan job.</p>"}}}, "StartCisSessionResponse": {"type": "structure", "members": {}}, "StartCodeSecurityScanRequest": {"type": "structure", "required": ["resource"], "members": {"clientToken": {"shape": "CodeSecurityClientToken", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency of the request.</p>", "idempotencyToken": true}, "resource": {"shape": "CodeSecurityResource", "documentation": "<p>The resource identifier for the code repository to scan.</p>"}}}, "StartCodeSecurityScanResponse": {"type": "structure", "members": {"scanId": {"shape": "CodeSecurityUuid", "documentation": "<p>The unique identifier of the initiated scan.</p>"}, "status": {"shape": "CodeScanStatus", "documentation": "<p>The current status of the initiated scan.</p>"}}}, "State": {"type": "structure", "required": ["errorCode", "errorMessage", "status"], "members": {"errorCode": {"shape": "ErrorCode", "documentation": "<p>The error code explaining why the account failed to enable Amazon Inspector.</p>"}, "errorMessage": {"shape": "NonEmptyString", "documentation": "<p>The error message received when the account failed to enable Amazon Inspector.</p>"}, "status": {"shape": "Status", "documentation": "<p>The status of Amazon Inspector for the account.</p>"}}, "documentation": "<p>An object that described the state of Amazon Inspector scans for an account.</p>"}, "Status": {"type": "string", "enum": ["ENABLING", "ENABLED", "DISABLING", "DISABLED", "SUSPENDING", "SUSPENDED"]}, "StatusCounts": {"type": "structure", "members": {"failed": {"shape": "Integer", "documentation": "<p>The number of checks that failed.</p>"}, "passed": {"shape": "Integer", "documentation": "<p>The number of checks that passed.</p>"}, "skipped": {"shape": "Integer", "documentation": "<p>The number of checks that were skipped.</p>"}}, "documentation": "<p>The status counts.</p>"}, "Step": {"type": "structure", "required": ["componentId", "componentType"], "members": {"componentArn": {"shape": "ComponentArn", "documentation": "<p>The component ARN. The ARN can be null and is not displayed in the Amazon Web Services console.</p>"}, "componentId": {"shape": "Component", "documentation": "<p>The component ID.</p>"}, "componentType": {"shape": "ComponentType", "documentation": "<p>The component type.</p>"}}, "documentation": "<p>Details about the step associated with a finding.</p>"}, "StepList": {"type": "list", "member": {"shape": "Step"}, "max": 30, "min": 1}, "StopCisMessageProgress": {"type": "structure", "members": {"errorChecks": {"shape": "CheckCount", "documentation": "<p>The progress' error checks.</p>"}, "failedChecks": {"shape": "CheckCount", "documentation": "<p>The progress' failed checks.</p>"}, "informationalChecks": {"shape": "CheckCount", "documentation": "<p>The progress' informational checks.</p>"}, "notApplicableChecks": {"shape": "CheckCount", "documentation": "<p>The progress' not applicable checks.</p>"}, "notEvaluatedChecks": {"shape": "CheckCount", "documentation": "<p>The progress' not evaluated checks.</p>"}, "successfulChecks": {"shape": "CheckCount", "documentation": "<p>The progress' successful checks.</p>"}, "totalChecks": {"shape": "CheckCount", "documentation": "<p>The progress' total checks.</p>"}, "unknownChecks": {"shape": "CheckCount", "documentation": "<p>The progress' unknown checks.</p>"}}, "documentation": "<p>The stop CIS message progress.</p>"}, "StopCisSessionMessage": {"type": "structure", "required": ["progress", "status"], "members": {"benchmarkProfile": {"shape": "BenchmarkProfile", "documentation": "<p>The message benchmark profile.</p>"}, "benchmarkVersion": {"shape": "BenchmarkVersion", "documentation": "<p>The message benchmark version.</p>"}, "computePlatform": {"shape": "ComputePlatform", "documentation": "<p>The message compute platform.</p>"}, "progress": {"shape": "StopCisMessageProgress", "documentation": "<p>The progress of the message.</p>"}, "reason": {"shape": "Reason", "documentation": "<p>The reason for the message.</p>"}, "status": {"shape": "StopCisSessionStatus", "documentation": "<p>The status of the message.</p>"}}, "documentation": "<p>The stop CIS session message.</p>"}, "StopCisSessionRequest": {"type": "structure", "required": ["message", "scanJobId", "sessionToken"], "members": {"message": {"shape": "StopCisSessionMessage", "documentation": "<p>The stop CIS session message.</p>"}, "scanJobId": {"shape": "UUID", "documentation": "<p>A unique identifier for the scan job.</p>"}, "sessionToken": {"shape": "UUID", "documentation": "<p>The unique token that identifies the CIS session.</p>"}}}, "StopCisSessionResponse": {"type": "structure", "members": {}}, "StopCisSessionStatus": {"type": "string", "enum": ["SUCCESS", "FAILED", "INTERRUPTED", "UNSUPPORTED_OS"]}, "String": {"type": "string"}, "StringComparison": {"type": "string", "enum": ["EQUALS", "PREFIX", "NOT_EQUALS"]}, "StringFilter": {"type": "structure", "required": ["comparison", "value"], "members": {"comparison": {"shape": "StringComparison", "documentation": "<p>The operator to use when comparing values in the filter.</p>"}, "value": {"shape": "StringInput", "documentation": "<p>The value to filter on.</p>"}}, "documentation": "<p>An object that describes the details of a string filter.</p>"}, "StringFilterList": {"type": "list", "member": {"shape": "StringFilter"}, "max": 10, "min": 1}, "StringInput": {"type": "string", "max": 1024, "min": 1}, "StringList": {"type": "list", "member": {"shape": "NonEmptyString"}}, "SubnetId": {"type": "string", "pattern": "^subnet-([a-z0-9]{8}|[a-z0-9]{17}|\\*)$"}, "SubnetIdList": {"type": "list", "member": {"shape": "SubnetId"}, "max": 16, "min": 0}, "SuccessfulAssociationResult": {"type": "structure", "members": {"resource": {"shape": "CodeSecurityResource"}, "scanConfigurationArn": {"shape": "ScanConfigurationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the scan configuration that was successfully associated or disassociated.</p>"}}, "documentation": "<p>Details about a successful association or disassociation between a code repository and a scan configuration.</p>"}, "SuccessfulAssociationResultList": {"type": "list", "member": {"shape": "SuccessfulAssociationResult"}}, "SuggestedFix": {"type": "structure", "members": {"code": {"shape": "SuggestedFixCodeString", "documentation": "<p>The fix's code.</p>"}, "description": {"shape": "SuggestedFixDescriptionString", "documentation": "<p>The fix's description.</p>"}}, "documentation": "<p>A suggested fix for a vulnerability in your Lambda function code.</p>"}, "SuggestedFixCodeString": {"type": "string", "max": 2500, "min": 1}, "SuggestedFixDescriptionString": {"type": "string", "max": 1000, "min": 1}, "SuggestedFixes": {"type": "list", "member": {"shape": "SuggestedFix"}, "max": 5, "min": 1}, "TagComparison": {"type": "string", "enum": ["EQUALS"]}, "TagFilter": {"type": "structure", "required": ["comparison", "key", "value"], "members": {"comparison": {"shape": "TagComparison", "documentation": "<p>The tag filter comparison value.</p>"}, "key": {"shape": "NonEmptyString", "documentation": "<p>The tag filter key.</p>"}, "value": {"shape": "NonEmptyString", "documentation": "<p>The tag filter value.</p>"}}, "documentation": "<p>The tag filter.</p>"}, "TagKey": {"type": "string", "max": 128, "min": 1, "pattern": "^(?!aws:)[a-zA-Z+-=._:/]+$"}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 50, "min": 0}, "TagList": {"type": "list", "member": {"shape": "String"}}, "TagMap": {"type": "map", "key": {"shape": "MapKey"}, "value": {"shape": "MapValue"}}, "TagResourceRequest": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the resource to apply a tag to.</p>", "location": "uri", "locationName": "resourceArn"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags to be added to a resource.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValueList": {"type": "list", "member": {"shape": "TargetResourceTagsValue"}, "max": 5, "min": 1}, "Target": {"type": "string", "max": 50, "min": 0}, "TargetAccount": {"type": "string", "pattern": "^\\d{12}|ALL_ACCOUNTS|SELF$"}, "TargetAccountList": {"type": "list", "member": {"shape": "TargetAccount"}, "max": 10000, "min": 1}, "TargetResourceTags": {"type": "map", "key": {"shape": "TargetResourceTagsKey"}, "value": {"shape": "TagValueList"}, "max": 5, "min": 1}, "TargetResourceTagsKey": {"type": "string", "max": 128, "min": 1, "pattern": "^[\\p{L}\\p{Z}\\p{N}_.:/=\\-@]*$"}, "TargetResourceTagsValue": {"type": "string", "max": 256, "min": 1}, "TargetStatusFilterList": {"type": "list", "member": {"shape": "CisTargetStatusFilter"}, "max": 10, "min": 1}, "TargetStatusReasonFilterList": {"type": "list", "member": {"shape": "CisTargetStatusReasonFilter"}, "max": 10, "min": 1}, "Targets": {"type": "list", "member": {"shape": "Target"}, "min": 0}, "ThrottlingException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}, "retryAfterSeconds": {"shape": "Integer", "documentation": "<p>The number of seconds to wait before retrying the request.</p>", "location": "header", "locationName": "Retry-After"}}, "documentation": "<p>The limit on the number of requests per second was exceeded.</p>", "error": {"httpStatusCode": 429, "senderFault": true}, "exception": true, "retryable": {"throttling": true}}, "Time": {"type": "structure", "required": ["timeOfDay", "timezone"], "members": {"timeOfDay": {"shape": "TimeOfDay", "documentation": "<p>The time of day in 24-hour format (00:00).</p>"}, "timezone": {"shape": "Timezone", "documentation": "<p>The timezone.</p>"}}, "documentation": "<p>The time.</p>"}, "TimeOfDay": {"type": "string", "pattern": "^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$"}, "Timestamp": {"type": "timestamp"}, "Timezone": {"type": "string", "max": 50, "min": 1}, "TitleAggregation": {"type": "structure", "members": {"findingType": {"shape": "AggregationFindingType", "documentation": "<p>The type of finding to aggregate on.</p>"}, "resourceType": {"shape": "AggregationResourceType", "documentation": "<p>The resource type to aggregate on.</p>"}, "sortBy": {"shape": "TitleSortBy", "documentation": "<p>The value to sort results by.</p>"}, "sortOrder": {"shape": "SortOrder", "documentation": "<p>The order to sort results by.</p>"}, "titles": {"shape": "StringFilterList", "documentation": "<p>The finding titles to aggregate on.</p>"}, "vulnerabilityIds": {"shape": "StringFilterList", "documentation": "<p>The vulnerability IDs of the findings.</p>"}}, "documentation": "<p>The details that define an aggregation based on finding title.</p>"}, "TitleAggregationResponse": {"type": "structure", "required": ["title"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The ID of the Amazon Web Services account associated with the findings.</p>"}, "severityCounts": {"shape": "SeverityCounts", "documentation": "<p>An object that represent the count of matched findings per severity.</p>"}, "title": {"shape": "NonEmptyString", "documentation": "<p>The title that the findings were aggregated on.</p>"}, "vulnerabilityId": {"shape": "String", "documentation": "<p>The vulnerability ID of the finding.</p>"}}, "documentation": "<p>A response that contains details on the results of a finding aggregation by title.</p>"}, "TitleFilterList": {"type": "list", "member": {"shape": "CisStringFilter"}, "max": 10, "min": 1}, "TitleSortBy": {"type": "string", "enum": ["CRITICAL", "HIGH", "ALL"]}, "Tool": {"type": "string", "min": 0}, "Tools": {"type": "list", "member": {"shape": "Tool"}}, "Ttp": {"type": "string", "max": 30, "min": 0}, "Ttps": {"type": "list", "member": {"shape": "Ttp"}, "min": 0}, "UUID": {"type": "string", "pattern": "^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$"}, "UntagResourceRequest": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) for the resource to remove tags from.</p>", "location": "uri", "locationName": "resourceArn"}, "tagKeys": {"shape": "TagKeyList", "documentation": "<p>The tag keys to remove from the resource.</p>", "location": "querystring", "locationName": "tagKeys"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateCisScanConfigurationRequest": {"type": "structure", "required": ["scanConfigurationArn"], "members": {"scanConfigurationArn": {"shape": "CisScanConfigurationArn", "documentation": "<p>The CIS scan configuration ARN.</p>"}, "scanName": {"shape": "CisScanName", "documentation": "<p>The scan name for the CIS scan configuration.</p>"}, "schedule": {"shape": "Schedule", "documentation": "<p>The schedule for the CIS scan configuration.</p>"}, "securityLevel": {"shape": "CisSecurityLevel", "documentation": "<p> The security level for the CIS scan configuration. Security level refers to the Benchmark levels that CIS assigns to a profile. </p>"}, "targets": {"shape": "UpdateCisTargets", "documentation": "<p>The targets for the CIS scan configuration.</p>"}}}, "UpdateCisScanConfigurationResponse": {"type": "structure", "required": ["scanConfigurationArn"], "members": {"scanConfigurationArn": {"shape": "CisScanConfigurationArn", "documentation": "<p>The CIS scan configuration ARN.</p>"}}}, "UpdateCisTargets": {"type": "structure", "members": {"accountIds": {"shape": "TargetAccountList", "documentation": "<p>The target account ids.</p>"}, "targetResourceTags": {"shape": "TargetResourceTags", "documentation": "<p>The target resource tags.</p>"}}, "documentation": "<p>Updates CIS targets.</p>"}, "UpdateCodeSecurityIntegrationRequest": {"type": "structure", "required": ["details", "integrationArn"], "members": {"details": {"shape": "UpdateIntegrationDetails", "documentation": "<p>The updated integration details specific to the repository provider type.</p>"}, "integrationArn": {"shape": "CodeSecurityIntegrationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the code security integration to update.</p>"}}}, "UpdateCodeSecurityIntegrationResponse": {"type": "structure", "required": ["integrationArn", "status"], "members": {"integrationArn": {"shape": "CodeSecurityIntegrationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the updated code security integration.</p>"}, "status": {"shape": "IntegrationStatus", "documentation": "<p>The current status of the updated code security integration.</p>"}}}, "UpdateCodeSecurityScanConfigurationRequest": {"type": "structure", "required": ["configuration", "scanConfigurationArn"], "members": {"configuration": {"shape": "CodeSecurityScanConfiguration", "documentation": "<p>The updated configuration settings for the code security scan.</p>"}, "scanConfigurationArn": {"shape": "ScanConfigurationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the scan configuration to update.</p>"}}}, "UpdateCodeSecurityScanConfigurationResponse": {"type": "structure", "members": {"scanConfigurationArn": {"shape": "ScanConfigurationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the updated scan configuration.</p>"}}}, "UpdateConfigurationRequest": {"type": "structure", "members": {"ec2Configuration": {"shape": "Ec2Configuration", "documentation": "<p>Specifies how the Amazon EC2 automated scan will be updated for your environment.</p>"}, "ecrConfiguration": {"shape": "EcrConfiguration", "documentation": "<p>Specifies how the ECR automated re-scan will be updated for your environment.</p>"}}}, "UpdateConfigurationResponse": {"type": "structure", "members": {}}, "UpdateEc2DeepInspectionConfigurationRequest": {"type": "structure", "members": {"activateDeepInspection": {"shape": "Boolean", "documentation": "<p>Specify <code>TRUE</code> to activate Amazon Inspector deep inspection in your account, or <code>FALSE</code> to deactivate. Member accounts in an organization cannot deactivate deep inspection, instead the delegated administrator for the organization can deactivate a member account using <a href=\"https://docs.aws.amazon.com/inspector/v2/APIReference/API_BatchUpdateMemberEc2DeepInspectionStatus.html\">BatchUpdateMemberEc2DeepInspectionStatus</a>.</p>"}, "packagePaths": {"shape": "PathList", "documentation": "<p>The Amazon Inspector deep inspection custom paths you are adding for your account.</p>"}}}, "UpdateEc2DeepInspectionConfigurationResponse": {"type": "structure", "members": {"errorMessage": {"shape": "NonEmptyString", "documentation": "<p>An error message explaining why new Amazon Inspector deep inspection custom paths could not be added.</p>"}, "orgPackagePaths": {"shape": "PathList", "documentation": "<p>The current Amazon Inspector deep inspection custom paths for the organization.</p>"}, "packagePaths": {"shape": "PathList", "documentation": "<p>The current Amazon Inspector deep inspection custom paths for your account.</p>"}, "status": {"shape": "Ec2DeepInspectionStatus", "documentation": "<p>The status of Amazon Inspector deep inspection in your account.</p>"}}}, "UpdateEncryptionKeyRequest": {"type": "structure", "required": ["kmsKeyId", "resourceType", "scanType"], "members": {"kmsKeyId": {"shape": "KmsKeyArn", "documentation": "<p>A KMS key ID for the encryption key.</p>"}, "resourceType": {"shape": "ResourceType", "documentation": "<p>The resource type for the encryption key.</p>"}, "scanType": {"shape": "ScanType", "documentation": "<p>The scan type for the encryption key.</p>"}}}, "UpdateEncryptionKeyResponse": {"type": "structure", "members": {}}, "UpdateFilterRequest": {"type": "structure", "required": ["filterArn"], "members": {"action": {"shape": "FilterAction", "documentation": "<p>Specifies the action that is to be applied to the findings that match the filter.</p>"}, "description": {"shape": "FilterDescription", "documentation": "<p>A description of the filter.</p>"}, "filterArn": {"shape": "FilterArn", "documentation": "<p>The Amazon Resource Number (ARN) of the filter to update.</p>"}, "filterCriteria": {"shape": "FilterCriteria", "documentation": "<p>Defines the criteria to be update in the filter.</p>"}, "name": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The name of the filter.</p>"}, "reason": {"shape": "FilterReason", "documentation": "<p>The reason the filter was updated.</p>"}}}, "UpdateFilterResponse": {"type": "structure", "required": ["arn"], "members": {"arn": {"shape": "FilterArn", "documentation": "<p>The Amazon Resource Number (ARN) of the successfully updated filter.</p>"}}}, "UpdateGitHubIntegrationDetail": {"type": "structure", "required": ["code", "installationId"], "members": {"code": {"shape": "GitHubAuthCode", "documentation": "<p>The authorization code received from GitHub to update the integration.</p>"}, "installationId": {"shape": "GitHubInstallationId", "documentation": "<p>The installation ID of the GitHub App associated with the integration.</p>"}}, "documentation": "<p>Contains details required to update an integration with GitHub.</p>"}, "UpdateGitLabSelfManagedIntegrationDetail": {"type": "structure", "required": ["authCode"], "members": {"authCode": {"shape": "GitLabAuthCode", "documentation": "<p>The authorization code received from the self-managed GitLab instance to update the integration.</p>"}}, "documentation": "<p>Contains details required to update an integration with a self-managed GitLab instance.</p>"}, "UpdateIntegrationDetails": {"type": "structure", "members": {"github": {"shape": "UpdateGitHubIntegrationDetail", "documentation": "<p>Details specific to updating an integration with GitHub.</p>"}, "gitlabSelfManaged": {"shape": "UpdateGitLabSelfManagedIntegrationDetail", "documentation": "<p>Details specific to updating an integration with a self-managed GitLab instance.</p>"}}, "documentation": "<p>Contains details required to update a code security integration with a specific repository provider.</p>", "union": true}, "UpdateOrgEc2DeepInspectionConfigurationRequest": {"type": "structure", "required": ["orgPackagePaths"], "members": {"orgPackagePaths": {"shape": "PathList", "documentation": "<p>The Amazon Inspector deep inspection custom paths you are adding for your organization.</p>"}}}, "UpdateOrgEc2DeepInspectionConfigurationResponse": {"type": "structure", "members": {}}, "UpdateOrganizationConfigurationRequest": {"type": "structure", "required": ["autoEnable"], "members": {"autoEnable": {"shape": "AutoEnable", "documentation": "<p>Defines which scan types are enabled automatically for new members of your Amazon Inspector organization.</p>"}}}, "UpdateOrganizationConfigurationResponse": {"type": "structure", "required": ["autoEnable"], "members": {"autoEnable": {"shape": "AutoEnable", "documentation": "<p>The updated status of scan types automatically enabled for new members of your Amazon Inspector organization.</p>"}}}, "Usage": {"type": "structure", "members": {"currency": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The currency type used when calculating usage data.</p>"}, "estimatedMonthlyCost": {"shape": "MonthlyCostEstimate", "documentation": "<p>The estimated monthly cost of Amazon Inspector.</p>"}, "total": {"shape": "UsageValue", "documentation": "<p>The total of usage.</p>"}, "type": {"shape": "UsageType", "documentation": "<p>The type scan.</p>"}}, "documentation": "<p>Contains usage information about the cost of Amazon Inspector operation.</p>"}, "UsageAccountId": {"type": "string", "pattern": "[0-9]{12}"}, "UsageAccountIdList": {"type": "list", "member": {"shape": "UsageAccountId"}, "max": 7000, "min": 1}, "UsageList": {"type": "list", "member": {"shape": "Usage"}}, "UsageTotal": {"type": "structure", "members": {"accountId": {"shape": "MeteringAccountId", "documentation": "<p>The account ID of the account that usage data was retrieved for.</p>"}, "usage": {"shape": "UsageList", "documentation": "<p>An object representing the total usage for an account.</p>"}}, "documentation": "<p>The total of usage for an account ID.</p>"}, "UsageTotalList": {"type": "list", "member": {"shape": "UsageTotal"}}, "UsageType": {"type": "string", "enum": ["EC2_INSTANCE_HOURS", "ECR_INITIAL_SCAN", "ECR_RESCAN", "LAMBDA_FUNCTION_HOURS", "LAMBDA_FUNCTION_CODE_HOURS", "CODE_REPOSITORY_SAST", "CODE_REPOSITORY_IAC", "CODE_REPOSITORY_SCA"]}, "UsageValue": {"type": "double", "min": 0}, "ValidationException": {"type": "structure", "required": ["message", "reason"], "members": {"fields": {"shape": "ValidationExceptionFields", "documentation": "<p>The fields that failed validation.</p>"}, "message": {"shape": "String"}, "reason": {"shape": "ValidationExceptionReason", "documentation": "<p>The reason for the validation failure.</p>"}}, "documentation": "<p>The request has failed validation due to missing required fields or having invalid inputs.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "ValidationExceptionField": {"type": "structure", "required": ["message", "name"], "members": {"message": {"shape": "String", "documentation": "<p>The validation exception message.</p>"}, "name": {"shape": "String", "documentation": "<p>The name of the validation exception.</p>"}}, "documentation": "<p>An object that describes a validation exception.</p>"}, "ValidationExceptionFields": {"type": "list", "member": {"shape": "ValidationExceptionField"}}, "ValidationExceptionReason": {"type": "string", "enum": ["CANNOT_PARSE", "FIELD_VALIDATION_FAILED", "OTHER"]}, "Vendor": {"type": "string", "max": 16, "min": 0}, "VendorCreatedAt": {"type": "timestamp"}, "VendorSeverity": {"type": "string", "max": 64, "min": 1}, "VendorUpdatedAt": {"type": "timestamp"}, "Version": {"type": "string", "pattern": "^\\$LATEST|[0-9]+$"}, "VpcId": {"type": "string", "pattern": "^vpc-([a-z0-9]{8}|[a-z0-9]{17}|\\*)$"}, "VulnId": {"type": "string", "pattern": "^CVE-[12][0-9]{3}-[0-9]{1,10}$"}, "VulnIdList": {"type": "list", "member": {"shape": "VulnId"}, "max": 1, "min": 1}, "Vulnerabilities": {"type": "list", "member": {"shape": "Vulnerability"}, "max": 1, "min": 0}, "Vulnerability": {"type": "structure", "required": ["id"], "members": {"atigData": {"shape": "AtigData", "documentation": "<p>An object that contains information about the Amazon Web Services Threat Intel Group (ATIG) details for the vulnerability.</p>"}, "cisaData": {"shape": "CisaData", "documentation": "<p>An object that contains the Cybersecurity and Infrastructure Security Agency (CISA) details for the vulnerability.</p>"}, "cvss2": {"shape": "Cvss2", "documentation": "<p>An object that contains the Common Vulnerability Scoring System (CVSS) Version 2 details for the vulnerability.</p>"}, "cvss3": {"shape": "Cvss3", "documentation": "<p>An object that contains the Common Vulnerability Scoring System (CVSS) Version 3 details for the vulnerability.</p>"}, "cwes": {"shape": "Cwes", "documentation": "<p>The Common Weakness Enumeration (CWE) associated with the vulnerability.</p>"}, "description": {"shape": "VulnerabilityDescription", "documentation": "<p>A description of the vulnerability.</p>"}, "detectionPlatforms": {"shape": "DetectionPlatforms", "documentation": "<p>Platforms that the vulnerability can be detected on.</p>"}, "epss": {"shape": "Epss", "documentation": "<p>An object that contains the Exploit Prediction Scoring System (EPSS) score for a vulnerability.</p>"}, "exploitObserved": {"shape": "ExploitObserved", "documentation": "<p>An object that contains details on when the exploit was observed.</p>"}, "id": {"shape": "NonEmptyString", "documentation": "<p>The ID for the specific vulnerability.</p>"}, "referenceUrls": {"shape": "VulnerabilityReferenceUrls", "documentation": "<p>Links to various resources with more information on this vulnerability. </p>"}, "relatedVulnerabilities": {"shape": "RelatedVulnerabilities", "documentation": "<p>A list of related vulnerabilities.</p>"}, "source": {"shape": "VulnerabilitySource", "documentation": "<p>The source of the vulnerability information. Possible results are <code>RHEL</code>, <code>AMAZON_CVE</code>, <code>DEBIAN</code> or <code>NVD</code>.</p>"}, "sourceUrl": {"shape": "VulnerabilitySourceUrl", "documentation": "<p>A link to the official source material for this vulnerability.</p>"}, "vendorCreatedAt": {"shape": "VendorCreatedAt", "documentation": "<p>The date and time when the vendor created this vulnerability.</p>"}, "vendorSeverity": {"shape": "Vendor<PERSON><PERSON><PERSON>", "documentation": "<p>The severity assigned by the vendor.</p>"}, "vendorUpdatedAt": {"shape": "VendorUpdatedAt", "documentation": "<p>The date and time when the vendor last updated this vulnerability.</p>"}}, "documentation": "<p>Contains details about a specific vulnerability Amazon Inspector can detect.</p>"}, "VulnerabilityDescription": {"type": "string"}, "VulnerabilityId": {"type": "string", "max": 128, "min": 1}, "VulnerabilityIdList": {"type": "list", "member": {"shape": "VulnerabilityId"}}, "VulnerabilityReferenceUrl": {"type": "string", "min": 0}, "VulnerabilityReferenceUrls": {"type": "list", "member": {"shape": "VulnerabilityReferenceUrl"}, "max": 100, "min": 0}, "VulnerabilitySource": {"type": "string", "enum": ["NVD"]}, "VulnerabilitySourceUrl": {"type": "string", "min": 0}, "VulnerablePackage": {"type": "structure", "required": ["name", "version"], "members": {"arch": {"shape": "PackageArchitecture", "documentation": "<p>The architecture of the vulnerable package.</p>"}, "epoch": {"shape": "PackageEpoch", "documentation": "<p>The epoch of the vulnerable package.</p>"}, "filePath": {"shape": "FilePath", "documentation": "<p>The file path of the vulnerable package.</p>"}, "fixedInVersion": {"shape": "PackageVersion", "documentation": "<p>The version of the package that contains the vulnerability fix.</p>"}, "name": {"shape": "PackageName", "documentation": "<p>The name of the vulnerable package.</p>"}, "packageManager": {"shape": "PackageManager", "documentation": "<p>The package manager of the vulnerable package.</p>"}, "release": {"shape": "PackageRelease", "documentation": "<p>The release of the vulnerable package.</p>"}, "remediation": {"shape": "VulnerablePackageRemediation", "documentation": "<p>The code to run in your environment to update packages with a fix available.</p>"}, "sourceLambdaLayerArn": {"shape": "LambdaLayerArn", "documentation": "<p>The Amazon Resource Number (ARN) of the Amazon Web Services Lambda function affected by a finding.</p>"}, "sourceLayerHash": {"shape": "SourceLayerHash", "documentation": "<p>The source layer hash of the vulnerable package.</p>"}, "version": {"shape": "PackageVersion", "documentation": "<p>The version of the vulnerable package.</p>"}}, "documentation": "<p>Information on the vulnerable package identified by a finding.</p>"}, "VulnerablePackageList": {"type": "list", "member": {"shape": "VulnerablePackage"}}, "VulnerablePackageRemediation": {"type": "string", "max": 1024, "min": 1}, "WeeklySchedule": {"type": "structure", "required": ["days", "startTime"], "members": {"days": {"shape": "DaysList", "documentation": "<p>The weekly schedule's days.</p>"}, "startTime": {"shape": "Time", "documentation": "<p>The weekly schedule's start time.</p>"}}, "documentation": "<p>A weekly schedule.</p>"}}, "documentation": "<p>Amazon Inspector is a vulnerability discovery service that automates continuous scanning for security vulnerabilities within your Amazon EC2, Amazon ECR, and Amazon Web Services Lambda environments.</p>"}