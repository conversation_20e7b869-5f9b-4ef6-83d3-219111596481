{"version": "2.0", "metadata": {"apiVersion": "2017-11-28", "endpointPrefix": "<PERSON><PERSON><PERSON>", "jsonVersion": "1.1", "protocol": "rest-json", "protocols": ["rest-json"], "serviceFullName": "Amazon GuardDuty", "serviceId": "GuardDuty", "signatureVersion": "v4", "signingName": "<PERSON><PERSON><PERSON>", "uid": "guardduty-2017-11-28", "auth": ["aws.auth#sigv4"]}, "operations": {"AcceptAdministratorInvitation": {"name": "AcceptAdministratorInvitation", "http": {"method": "POST", "requestUri": "/detector/{detectorId}/administrator", "responseCode": 200}, "input": {"shape": "AcceptAdministratorInvitationRequest"}, "output": {"shape": "AcceptAdministratorInvitationResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Accepts the invitation to be a member account and get monitored by a GuardDuty administrator account that sent the invitation.</p>"}, "AcceptInvitation": {"name": "AcceptInvitation", "http": {"method": "POST", "requestUri": "/detector/{detectorId}/master", "responseCode": 200}, "input": {"shape": "AcceptInvitationRequest"}, "output": {"shape": "AcceptInvitationResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Accepts the invitation to be monitored by a GuardDuty administrator account.</p>", "deprecated": true, "deprecatedMessage": "This operation is deprecated, use AcceptAdministratorInvitation instead"}, "ArchiveFindings": {"name": "ArchiveFindings", "http": {"method": "POST", "requestUri": "/detector/{detectorId}/findings/archive", "responseCode": 200}, "input": {"shape": "ArchiveFindingsRequest"}, "output": {"shape": "ArchiveFindingsResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Archives GuardDuty findings that are specified by the list of finding IDs.</p> <note> <p>Only the administrator account can archive findings. Member accounts don't have permission to archive findings from their accounts.</p> </note>"}, "CreateDetector": {"name": "CreateDetector", "http": {"method": "POST", "requestUri": "/detector", "responseCode": 200}, "input": {"shape": "CreateDetectorRequest"}, "output": {"shape": "CreateDetectorResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Creates a single GuardDuty detector. A detector is a resource that represents the GuardDuty service. To start using GuardDuty, you must create a detector in each Region where you enable the service. You can have only one detector per account per Region. All data sources are enabled in a new detector by default.</p> <ul> <li> <p>When you don't specify any <code>features</code>, with an exception to <code>RUNTIME_MONITORING</code>, all the optional features are enabled by default.</p> </li> <li> <p>When you specify some of the <code>features</code>, any feature that is not specified in the API call gets enabled by default, with an exception to <code>RUNTIME_MONITORING</code>. </p> </li> </ul> <p>Specifying both EKS Runtime Monitoring (<code>EKS_RUNTIME_MONITORING</code>) and Runtime Monitoring (<code>RUNTIME_MONITORING</code>) will cause an error. You can add only one of these two features because Runtime Monitoring already includes the threat detection for Amazon EKS resources. For more information, see <a href=\"https://docs.aws.amazon.com/guardduty/latest/ug/runtime-monitoring.html\">Runtime Monitoring</a>.</p> <p>There might be regional differences because some data sources might not be available in all the Amazon Web Services Regions where GuardDuty is presently supported. For more information, see <a href=\"https://docs.aws.amazon.com/guardduty/latest/ug/guardduty_regions.html\">Regions and endpoints</a>.</p>"}, "CreateFilter": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "http": {"method": "POST", "requestUri": "/detector/{detectorId}/filter", "responseCode": 200}, "input": {"shape": "CreateFilterRequest"}, "output": {"shape": "CreateFilterResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Creates a filter using the specified finding criteria. The maximum number of saved filters per Amazon Web Services account per Region is 100. For more information, see <a href=\"https://docs.aws.amazon.com/guardduty/latest/ug/guardduty_limits.html\">Quotas for GuardDuty</a>.</p>"}, "CreateIPSet": {"name": "CreateIPSet", "http": {"method": "POST", "requestUri": "/detector/{detectorId}/ipset", "responseCode": 200}, "input": {"shape": "CreateIPSetRequest"}, "output": {"shape": "CreateIPSetResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Creates a new IPSet, which is called a trusted IP list in the console user interface. An IPSet is a list of IP addresses that are trusted for secure communication with Amazon Web Services infrastructure and applications. GuardDuty doesn't generate findings for IP addresses that are included in IPSets. Only users from the administrator account can use this operation.</p>"}, "CreateMalwareProtectionPlan": {"name": "CreateMalwareProtectionPlan", "http": {"method": "POST", "requestUri": "/malware-protection-plan", "responseCode": 200}, "input": {"shape": "CreateMalwareProtectionPlanRequest"}, "output": {"shape": "CreateMalwareProtectionPlanResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Creates a new Malware Protection plan for the protected resource.</p> <p>When you create a Malware Protection plan, the Amazon Web Services service terms for GuardDuty Malware Protection apply. For more information, see <a href=\"http://aws.amazon.com/service-terms/#87._Amazon_GuardDuty\">Amazon Web Services service terms for GuardDuty Malware Protection</a>.</p>"}, "CreateMembers": {"name": "CreateM<PERSON>bers", "http": {"method": "POST", "requestUri": "/detector/{detectorId}/member", "responseCode": 200}, "input": {"shape": "CreateMembersRequest"}, "output": {"shape": "CreateMembersResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Creates member accounts of the current Amazon Web Services account by specifying a list of Amazon Web Services account IDs. This step is a prerequisite for managing the associated member accounts either by invitation or through an organization.</p> <p>As a delegated administrator, using <code>CreateMembers</code> will enable GuardDuty in the added member accounts, with the exception of the organization delegated administrator account. A delegated administrator must enable <PERSON><PERSON><PERSON><PERSON> prior to being added as a member.</p> <p>When you use CreateMembers as an Organizations delegated administrator, GuardDuty applies your organization's auto-enable settings to the member accounts in this request, irrespective of the accounts being new or existing members. For more information about the existing auto-enable settings for your organization, see <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_DescribeOrganizationConfiguration.html\">DescribeOrganizationConfiguration</a>.</p> <p>If you disassociate a member account that was added by invitation, the member account details obtained from this API, including the associated email addresses, will be retained. This is done so that the delegated administrator can invoke the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_InviteMembers.html\">InviteMembers</a> API without the need to invoke the CreateMembers API again. To remove the details associated with a member account, the delegated administrator must invoke the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_DeleteMembers.html\">DeleteMembers</a> API. </p> <p>When the member accounts added through Organizations are later disassociated, you (administrator) can't invite them by calling the InviteMembers API. You can create an association with these member accounts again only by calling the CreateMembers API.</p>"}, "CreatePublishingDestination": {"name": "CreatePublishingDestination", "http": {"method": "POST", "requestUri": "/detector/{detectorId}/publishingDestination", "responseCode": 200}, "input": {"shape": "CreatePublishingDestinationRequest"}, "output": {"shape": "CreatePublishingDestinationResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Creates a publishing destination where you can export your GuardDuty findings. Before you start exporting the findings, the destination resource must exist.</p>"}, "CreateSampleFindings": {"name": "CreateSampleFindings", "http": {"method": "POST", "requestUri": "/detector/{detectorId}/findings/create", "responseCode": 200}, "input": {"shape": "CreateSampleFindingsRequest"}, "output": {"shape": "CreateSampleFindingsResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Generates sample findings of types specified by the list of finding types. If 'NULL' is specified for <code>findingTypes</code>, the API generates sample findings of all supported finding types.</p>"}, "CreateThreatIntelSet": {"name": "CreateThreatIntelSet", "http": {"method": "POST", "requestUri": "/detector/{detectorId}/threatintelset", "responseCode": 200}, "input": {"shape": "CreateThreatIntelSetRequest"}, "output": {"shape": "CreateThreatIntelSetResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Creates a new ThreatIntelSet. ThreatIntelSets consist of known malicious IP addresses. GuardDuty generates findings based on ThreatIntelSets. Only users of the administrator account can use this operation.</p>"}, "DeclineInvitations": {"name": "DeclineInvitations", "http": {"method": "POST", "requestUri": "/invitation/decline", "responseCode": 200}, "input": {"shape": "DeclineInvitationsRequest"}, "output": {"shape": "DeclineInvitationsResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Declines invitations sent to the current member account by Amazon Web Services accounts specified by their account IDs.</p>"}, "DeleteDetector": {"name": "DeleteDetector", "http": {"method": "DELETE", "requestUri": "/detector/{detectorId}", "responseCode": 200}, "input": {"shape": "DeleteDetectorRequest"}, "output": {"shape": "DeleteDetectorResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Deletes an Amazon GuardDuty detector that is specified by the detector ID.</p>"}, "DeleteFilter": {"name": "DeleteFilter", "http": {"method": "DELETE", "requestUri": "/detector/{detectorId}/filter/{filterName}", "responseCode": 200}, "input": {"shape": "DeleteFilterRequest"}, "output": {"shape": "DeleteFilterResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Deletes the filter specified by the filter name.</p>"}, "DeleteIPSet": {"name": "DeleteIPSet", "http": {"method": "DELETE", "requestUri": "/detector/{detectorId}/ipset/{ipSetId}", "responseCode": 200}, "input": {"shape": "DeleteIPSetRequest"}, "output": {"shape": "DeleteIPSetResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Deletes the IPSet specified by the <code>ipSetId</code>. IPSets are called trusted IP lists in the console user interface.</p>"}, "DeleteInvitations": {"name": "DeleteInvitations", "http": {"method": "POST", "requestUri": "/invitation/delete", "responseCode": 200}, "input": {"shape": "DeleteInvitationsRequest"}, "output": {"shape": "DeleteInvitationsResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Deletes invitations sent to the current member account by Amazon Web Services accounts specified by their account IDs.</p>"}, "DeleteMalwareProtectionPlan": {"name": "DeleteMalwareProtectionPlan", "http": {"method": "DELETE", "requestUri": "/malware-protection-plan/{malwareProtectionPlanId}", "responseCode": 200}, "input": {"shape": "DeleteMalwareProtectionPlanRequest"}, "errors": [{"shape": "BadRequestException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Deletes the Malware Protection plan ID associated with the Malware Protection plan resource. Use this API only when you no longer want to protect the resource associated with this Malware Protection plan ID.</p>"}, "DeleteMembers": {"name": "DeleteMembers", "http": {"method": "POST", "requestUri": "/detector/{detectorId}/member/delete", "responseCode": 200}, "input": {"shape": "DeleteMembersRequest"}, "output": {"shape": "DeleteMembersResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Deletes GuardDuty member accounts (to the current GuardDuty administrator account) specified by the account IDs.</p> <p>With <code>autoEnableOrganizationMembers</code> configuration for your organization set to <code>ALL</code>, you'll receive an error if you attempt to disable GuardDuty for a member account in your organization.</p>"}, "DeletePublishingDestination": {"name": "DeletePublishingDestination", "http": {"method": "DELETE", "requestUri": "/detector/{detectorId}/publishingDestination/{destinationId}", "responseCode": 200}, "input": {"shape": "DeletePublishingDestinationRequest"}, "output": {"shape": "DeletePublishingDestinationResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Deletes the publishing definition with the specified <code>destinationId</code>.</p>"}, "DeleteThreatIntelSet": {"name": "DeleteThreatIntelSet", "http": {"method": "DELETE", "requestUri": "/detector/{detectorId}/threatintelset/{threatIntelSetId}", "responseCode": 200}, "input": {"shape": "DeleteThreatIntelSetRequest"}, "output": {"shape": "DeleteThreatIntelSetResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Deletes the ThreatIntelSet specified by the ThreatIntelSet ID.</p>"}, "DescribeMalwareScans": {"name": "DescribeMalwareScans", "http": {"method": "POST", "requestUri": "/detector/{detectorId}/malware-scans", "responseCode": 200}, "input": {"shape": "DescribeMalwareScansRequest"}, "output": {"shape": "DescribeMalwareScansResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Returns a list of malware scans. Each member account can view the malware scans for their own accounts. An administrator can view the malware scans for all the member accounts.</p> <p>There might be regional differences because some data sources might not be available in all the Amazon Web Services Regions where GuardDuty is presently supported. For more information, see <a href=\"https://docs.aws.amazon.com/guardduty/latest/ug/guardduty_regions.html\">Regions and endpoints</a>.</p>"}, "DescribeOrganizationConfiguration": {"name": "DescribeOrganizationConfiguration", "http": {"method": "GET", "requestUri": "/detector/{detectorId}/admin", "responseCode": 200}, "input": {"shape": "DescribeOrganizationConfigurationRequest"}, "output": {"shape": "DescribeOrganizationConfigurationResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Returns information about the account selected as the delegated administrator for GuardDuty.</p> <p>There might be regional differences because some data sources might not be available in all the Amazon Web Services Regions where GuardDuty is presently supported. For more information, see <a href=\"https://docs.aws.amazon.com/guardduty/latest/ug/guardduty_regions.html\">Regions and endpoints</a>.</p>"}, "DescribePublishingDestination": {"name": "DescribePublishingDestination", "http": {"method": "GET", "requestUri": "/detector/{detectorId}/publishingDestination/{destinationId}", "responseCode": 200}, "input": {"shape": "DescribePublishingDestinationRequest"}, "output": {"shape": "DescribePublishingDestinationResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Returns information about the publishing destination specified by the provided <code>destinationId</code>.</p>"}, "DisableOrganizationAdminAccount": {"name": "DisableOrganizationAdminAccount", "http": {"method": "POST", "requestUri": "/admin/disable", "responseCode": 200}, "input": {"shape": "DisableOrganizationAdminAccountRequest"}, "output": {"shape": "DisableOrganizationAdminAccountResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Removes the existing GuardDuty delegated administrator of the organization. Only the organization's management account can run this API operation.</p>"}, "DisassociateFromAdministratorAccount": {"name": "DisassociateFromAdministratorAccount", "http": {"method": "POST", "requestUri": "/detector/{detectorId}/administrator/disassociate", "responseCode": 200}, "input": {"shape": "DisassociateFromAdministratorAccountRequest"}, "output": {"shape": "DisassociateFromAdministratorAccountResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Disassociates the current GuardDuty member account from its administrator account.</p> <p>When you disassociate an invited member from a GuardDuty delegated administrator, the member account details obtained from the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_CreateMembers.html\">CreateMembers</a> API, including the associated email addresses, are retained. This is done so that the delegated administrator can invoke the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_InviteMembers.html\">InviteMembers</a> API without the need to invoke the CreateMembers API again. To remove the details associated with a member account, the delegated administrator must invoke the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_DeleteMembers.html\">DeleteMembers</a> API. </p> <p>With <code>autoEnableOrganizationMembers</code> configuration for your organization set to <code>ALL</code>, you'll receive an error if you attempt to disable <PERSON><PERSON><PERSON><PERSON> in a member account.</p>"}, "DisassociateFromMasterAccount": {"name": "DisassociateFromMasterAccount", "http": {"method": "POST", "requestUri": "/detector/{detectorId}/master/disassociate", "responseCode": 200}, "input": {"shape": "DisassociateFromMasterAccountRequest"}, "output": {"shape": "DisassociateFromMasterAccountResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Disassociates the current GuardDuty member account from its administrator account.</p> <p>When you disassociate an invited member from a GuardDuty delegated administrator, the member account details obtained from the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_CreateMembers.html\">CreateMembers</a> API, including the associated email addresses, are retained. This is done so that the delegated administrator can invoke the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_InviteMembers.html\">InviteMembers</a> API without the need to invoke the CreateMembers API again. To remove the details associated with a member account, the delegated administrator must invoke the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_DeleteMembers.html\">DeleteMembers</a> API.</p>", "deprecated": true, "deprecatedMessage": "This operation is deprecated, use DisassociateFromAdministratorAccount instead"}, "DisassociateMembers": {"name": "DisassociateMembers", "http": {"method": "POST", "requestUri": "/detector/{detectorId}/member/disassociate", "responseCode": 200}, "input": {"shape": "DisassociateMembersRequest"}, "output": {"shape": "DisassociateMembersResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Disassociates GuardDuty member accounts (from the current administrator account) specified by the account IDs.</p> <p>When you disassociate an invited member from a GuardDuty delegated administrator, the member account details obtained from the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_CreateMembers.html\">CreateMembers</a> API, including the associated email addresses, are retained. This is done so that the delegated administrator can invoke the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_InviteMembers.html\">InviteMembers</a> API without the need to invoke the CreateMembers API again. To remove the details associated with a member account, the delegated administrator must invoke the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_DeleteMembers.html\">DeleteMembers</a> API. </p> <p>With <code>autoEnableOrganizationMembers</code> configuration for your organization set to <code>ALL</code>, you'll receive an error if you attempt to disassociate a member account before removing them from your organization.</p> <p>If you disassociate a member account that was added by invitation, the member account details obtained from this API, including the associated email addresses, will be retained. This is done so that the delegated administrator can invoke the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_InviteMembers.html\">InviteMembers</a> API without the need to invoke the CreateMembers API again. To remove the details associated with a member account, the delegated administrator must invoke the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_DeleteMembers.html\">DeleteMembers</a> API. </p> <p>When the member accounts added through Organizations are later disassociated, you (administrator) can't invite them by calling the InviteMembers API. You can create an association with these member accounts again only by calling the CreateMembers API.</p>"}, "EnableOrganizationAdminAccount": {"name": "EnableOrganizationAdminAccount", "http": {"method": "POST", "requestUri": "/admin/enable", "responseCode": 200}, "input": {"shape": "EnableOrganizationAdminAccountRequest"}, "output": {"shape": "EnableOrganizationAdminAccountResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Designates an Amazon Web Services account within the organization as your GuardDuty delegated administrator. Only the organization's management account can run this API operation.</p>"}, "GetAdministratorAccount": {"name": "GetAdministratorAccount", "http": {"method": "GET", "requestUri": "/detector/{detectorId}/administrator", "responseCode": 200}, "input": {"shape": "GetAdministratorAccountRequest"}, "output": {"shape": "GetAdministratorAccountResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Provides the details of the GuardDuty administrator account associated with the current GuardDuty member account.</p> <p>Based on the type of account that runs this API, the following list shows how the API behavior varies:</p> <ul> <li> <p>When the GuardDuty administrator account runs this API, it will return success (<code>HTTP 200</code>) but no content.</p> </li> <li> <p>When a member account runs this API, it will return the details of the GuardDuty administrator account that is associated with this calling member account.</p> </li> <li> <p>When an individual account (not associated with an organization) runs this API, it will return success (<code>HTTP 200</code>) but no content.</p> </li> </ul>"}, "GetCoverageStatistics": {"name": "GetCoverageStatistics", "http": {"method": "POST", "requestUri": "/detector/{detectorId}/coverage/statistics", "responseCode": 200}, "input": {"shape": "GetCoverageStatisticsRequest"}, "output": {"shape": "GetCoverageStatisticsResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Retrieves aggregated statistics for your account. If you are a GuardDuty administrator, you can retrieve the statistics for all the resources associated with the active member accounts in your organization who have enabled Runtime Monitoring and have the GuardDuty security agent running on their resources.</p>"}, "GetDetector": {"name": "GetDetector", "http": {"method": "GET", "requestUri": "/detector/{detectorId}", "responseCode": 200}, "input": {"shape": "GetDetectorRequest"}, "output": {"shape": "GetDetectorResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Retrieves a GuardDuty detector specified by the detectorId.</p> <p>There might be regional differences because some data sources might not be available in all the Amazon Web Services Regions where GuardDuty is presently supported. For more information, see <a href=\"https://docs.aws.amazon.com/guardduty/latest/ug/guardduty_regions.html\">Regions and endpoints</a>.</p>"}, "GetFilter": {"name": "<PERSON><PERSON><PERSON><PERSON>", "http": {"method": "GET", "requestUri": "/detector/{detectorId}/filter/{filterName}", "responseCode": 200}, "input": {"shape": "GetFilterRequest"}, "output": {"shape": "GetFilterResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Returns the details of the filter specified by the filter name.</p>"}, "GetFindings": {"name": "GetFindings", "http": {"method": "POST", "requestUri": "/detector/{detectorId}/findings/get", "responseCode": 200}, "input": {"shape": "GetFindingsRequest"}, "output": {"shape": "GetFindingsResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Describes Amazon GuardDuty findings specified by finding IDs.</p>"}, "GetFindingsStatistics": {"name": "GetFindingsStatistics", "http": {"method": "POST", "requestUri": "/detector/{detectorId}/findings/statistics", "responseCode": 200}, "input": {"shape": "GetFindingsStatisticsRequest"}, "output": {"shape": "GetFindingsStatisticsResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Lists GuardDuty findings statistics for the specified detector ID.</p> <p>You must provide either <code>findingStatisticTypes</code> or <code>groupBy</code> parameter, and not both. You can use the <code>maxResults</code> and <code>orderBy</code> parameters only when using <code>groupBy</code>.</p> <p>There might be regional differences because some flags might not be available in all the Regions where GuardDuty is currently supported. For more information, see <a href=\"https://docs.aws.amazon.com/guardduty/latest/ug/guardduty_regions.html\">Regions and endpoints</a>.</p>"}, "GetIPSet": {"name": "GetIPSet", "http": {"method": "GET", "requestUri": "/detector/{detectorId}/ipset/{ipSetId}", "responseCode": 200}, "input": {"shape": "GetIPSetRequest"}, "output": {"shape": "GetIPSetResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Retrieves the IPSet specified by the <code>ipSetId</code>.</p>"}, "GetInvitationsCount": {"name": "GetInvitationsCount", "http": {"method": "GET", "requestUri": "/invitation/count", "responseCode": 200}, "input": {"shape": "GetInvitationsCountRequest"}, "output": {"shape": "GetInvitationsCountResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Returns the count of all GuardDuty membership invitations that were sent to the current member account except the currently accepted invitation.</p>"}, "GetMalwareProtectionPlan": {"name": "GetMalwareProtectionPlan", "http": {"method": "GET", "requestUri": "/malware-protection-plan/{malwareProtectionPlanId}", "responseCode": 200}, "input": {"shape": "GetMalwareProtectionPlanRequest"}, "output": {"shape": "GetMalwareProtectionPlanResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves the Malware Protection plan details associated with a Malware Protection plan ID.</p>"}, "GetMalwareScanSettings": {"name": "GetMalwareScanSettings", "http": {"method": "GET", "requestUri": "/detector/{detectorId}/malware-scan-settings", "responseCode": 200}, "input": {"shape": "GetMalwareScanSettingsRequest"}, "output": {"shape": "GetMalwareScanSettingsResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Returns the details of the malware scan settings.</p> <p>There might be regional differences because some data sources might not be available in all the Amazon Web Services Regions where GuardDuty is presently supported. For more information, see <a href=\"https://docs.aws.amazon.com/guardduty/latest/ug/guardduty_regions.html\">Regions and endpoints</a>.</p>"}, "GetMasterAccount": {"name": "GetMasterAccount", "http": {"method": "GET", "requestUri": "/detector/{detectorId}/master", "responseCode": 200}, "input": {"shape": "GetMasterAccountRequest"}, "output": {"shape": "GetMasterAccountResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Provides the details for the GuardDuty administrator account associated with the current GuardDuty member account.</p>", "deprecated": true, "deprecatedMessage": "This operation is deprecated, use GetAdministratorAccount instead"}, "GetMemberDetectors": {"name": "GetMemberDetectors", "http": {"method": "POST", "requestUri": "/detector/{detectorId}/member/detector/get", "responseCode": 200}, "input": {"shape": "GetMemberDetectorsRequest"}, "output": {"shape": "GetMemberDetectorsResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Describes which data sources are enabled for the member account's detector.</p> <p>There might be regional differences because some data sources might not be available in all the Amazon Web Services Regions where GuardDuty is presently supported. For more information, see <a href=\"https://docs.aws.amazon.com/guardduty/latest/ug/guardduty_regions.html\">Regions and endpoints</a>.</p>"}, "GetMembers": {"name": "GetMembers", "http": {"method": "POST", "requestUri": "/detector/{detectorId}/member/get", "responseCode": 200}, "input": {"shape": "GetMembersRequest"}, "output": {"shape": "GetMembersResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Retrieves GuardDuty member accounts (of the current GuardDuty administrator account) specified by the account IDs.</p>"}, "GetOrganizationStatistics": {"name": "GetOrganizationStatistics", "http": {"method": "GET", "requestUri": "/organization/statistics", "responseCode": 200}, "output": {"shape": "GetOrganizationStatisticsResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Retrieves how many active member accounts have each feature enabled within GuardDuty. Only a delegated GuardDuty administrator of an organization can run this API.</p> <p>When you create a new organization, it might take up to 24 hours to generate the statistics for the entire organization.</p>"}, "GetRemainingFreeTrialDays": {"name": "GetRemainingFreeTrialDays", "http": {"method": "POST", "requestUri": "/detector/{detectorId}/freeTrial/daysRemaining", "responseCode": 200}, "input": {"shape": "GetRemainingFreeTrialDaysRequest"}, "output": {"shape": "GetRemainingFreeTrialDaysResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Provides the number of days left for each data source used in the free trial period.</p>"}, "GetThreatIntelSet": {"name": "GetThreatIntelSet", "http": {"method": "GET", "requestUri": "/detector/{detectorId}/threatintelset/{threatIntelSetId}", "responseCode": 200}, "input": {"shape": "GetThreatIntelSetRequest"}, "output": {"shape": "GetThreatIntelSetResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Retrieves the ThreatIntelSet that is specified by the ThreatIntelSet ID.</p>"}, "GetUsageStatistics": {"name": "GetUsageStatistics", "http": {"method": "POST", "requestUri": "/detector/{detectorId}/usage/statistics", "responseCode": 200}, "input": {"shape": "GetUsageStatisticsRequest"}, "output": {"shape": "GetUsageStatisticsResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Lists Amazon GuardDuty usage statistics over the last 30 days for the specified detector ID. For newly enabled detectors or data sources, the cost returned will include only the usage so far under 30 days. This may differ from the cost metrics in the console, which project usage over 30 days to provide a monthly cost estimate. For more information, see <a href=\"https://docs.aws.amazon.com/guardduty/latest/ug/monitoring_costs.html#usage-calculations\">Understanding How Usage Costs are Calculated</a>.</p>"}, "InviteMembers": {"name": "InviteMembers", "http": {"method": "POST", "requestUri": "/detector/{detectorId}/member/invite", "responseCode": 200}, "input": {"shape": "InviteMembersRequest"}, "output": {"shape": "InviteMembersResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Invites Amazon Web Services accounts to become members of an organization administered by the Amazon Web Services account that invokes this API. If you are using Amazon Web Services Organizations to manage your GuardDuty environment, this step is not needed. For more information, see <a href=\"https://docs.aws.amazon.com/guardduty/latest/ug/guardduty_organizations.html\">Managing accounts with organizations</a>.</p> <p>To invite Amazon Web Services accounts, the first step is to ensure that GuardDuty has been enabled in the potential member accounts. You can now invoke this API to add accounts by invitation. The invited accounts can either accept or decline the invitation from their GuardDuty accounts. Each invited Amazon Web Services account can choose to accept the invitation from only one Amazon Web Services account. For more information, see <a href=\"https://docs.aws.amazon.com/guardduty/latest/ug/guardduty_invitations.html\">Managing GuardDuty accounts by invitation</a>.</p> <p>After the invite has been accepted and you choose to disassociate a member account (by using <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_DisassociateMembers.html\">DisassociateMembers</a>) from your account, the details of the member account obtained by invoking <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_CreateMembers.html\">CreateMembers</a>, including the associated email addresses, will be retained. This is done so that you can invoke InviteMembers without the need to invoke <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_CreateMembers.html\">CreateMembers</a> again. To remove the details associated with a member account, you must also invoke <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_DeleteMembers.html\">DeleteMembers</a>. </p> <p>If you disassociate a member account that was added by invitation, the member account details obtained from this API, including the associated email addresses, will be retained. This is done so that the delegated administrator can invoke the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_InviteMembers.html\">InviteMembers</a> API without the need to invoke the CreateMembers API again. To remove the details associated with a member account, the delegated administrator must invoke the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_DeleteMembers.html\">DeleteMembers</a> API. </p> <p>When the member accounts added through Organizations are later disassociated, you (administrator) can't invite them by calling the InviteMembers API. You can create an association with these member accounts again only by calling the CreateMembers API.</p>"}, "ListCoverage": {"name": "ListCoverage", "http": {"method": "POST", "requestUri": "/detector/{detectorId}/coverage", "responseCode": 200}, "input": {"shape": "ListCoverageRequest"}, "output": {"shape": "ListCoverageResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Lists coverage details for your GuardDuty account. If you're a GuardDuty administrator, you can retrieve all resources associated with the active member accounts in your organization.</p> <p>Make sure the accounts have Runtime Monitoring enabled and GuardDuty agent running on their resources.</p>"}, "ListDetectors": {"name": "ListDetectors", "http": {"method": "GET", "requestUri": "/detector", "responseCode": 200}, "input": {"shape": "ListDetectorsRequest"}, "output": {"shape": "ListDetectorsResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Lists detectorIds of all the existing Amazon GuardDuty detector resources.</p>"}, "ListFilters": {"name": "ListFilters", "http": {"method": "GET", "requestUri": "/detector/{detectorId}/filter", "responseCode": 200}, "input": {"shape": "ListFiltersRequest"}, "output": {"shape": "ListFiltersResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Returns a paginated list of the current filters.</p>"}, "ListFindings": {"name": "ListFindings", "http": {"method": "POST", "requestUri": "/detector/{detectorId}/findings", "responseCode": 200}, "input": {"shape": "ListFindingsRequest"}, "output": {"shape": "ListFindingsResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Lists GuardDuty findings for the specified detector ID.</p> <p>There might be regional differences because some flags might not be available in all the Regions where GuardDuty is currently supported. For more information, see <a href=\"https://docs.aws.amazon.com/guardduty/latest/ug/guardduty_regions.html\">Regions and endpoints</a>.</p>"}, "ListIPSets": {"name": "ListIPSets", "http": {"method": "GET", "requestUri": "/detector/{detectorId}/ipset", "responseCode": 200}, "input": {"shape": "ListIPSetsRequest"}, "output": {"shape": "ListIPSetsResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Lists the IPSets of the GuardDuty service specified by the detector ID. If you use this operation from a member account, the IPSets returned are the IPSets from the associated administrator account.</p>"}, "ListInvitations": {"name": "ListInvitations", "http": {"method": "GET", "requestUri": "/invitation", "responseCode": 200}, "input": {"shape": "ListInvitationsRequest"}, "output": {"shape": "ListInvitationsResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Lists all GuardDuty membership invitations that were sent to the current Amazon Web Services account.</p>"}, "ListMalwareProtectionPlans": {"name": "ListMalwareProtectionPlans", "http": {"method": "GET", "requestUri": "/malware-protection-plan", "responseCode": 200}, "input": {"shape": "ListMalwareProtectionPlansRequest"}, "output": {"shape": "ListMalwareProtectionPlansResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Lists the Malware Protection plan IDs associated with the protected resources in your Amazon Web Services account.</p>"}, "ListMembers": {"name": "ListMembers", "http": {"method": "GET", "requestUri": "/detector/{detectorId}/member", "responseCode": 200}, "input": {"shape": "ListMembersRequest"}, "output": {"shape": "ListMembersResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Lists details about all member accounts for the current GuardDuty administrator account.</p>"}, "ListOrganizationAdminAccounts": {"name": "ListOrganizationAdminAccounts", "http": {"method": "GET", "requestUri": "/admin", "responseCode": 200}, "input": {"shape": "ListOrganizationAdminAccountsRequest"}, "output": {"shape": "ListOrganizationAdminAccountsResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Lists the accounts designated as GuardDuty delegated administrators. Only the organization's management account can run this API operation.</p>"}, "ListPublishingDestinations": {"name": "ListPublishingDestinations", "http": {"method": "GET", "requestUri": "/detector/{detectorId}/publishingDestination", "responseCode": 200}, "input": {"shape": "ListPublishingDestinationsRequest"}, "output": {"shape": "ListPublishingDestinationsResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Returns a list of publishing destinations associated with the specified <code>detectorId</code>.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Lists tags for a resource. Tagging is currently supported for detectors, finding filters, IP sets, threat intel sets, and publishing destination, with a limit of 50 tags per resource. When invoked, this operation returns all assigned tags for a given resource.</p>"}, "ListThreatIntelSets": {"name": "ListThreatIntelSets", "http": {"method": "GET", "requestUri": "/detector/{detectorId}/threatintelset", "responseCode": 200}, "input": {"shape": "ListThreatIntelSetsRequest"}, "output": {"shape": "ListThreatIntelSetsResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Lists the ThreatIntelSets of the GuardDuty service specified by the detector ID. If you use this operation from a member account, the ThreatIntelSets associated with the administrator account are returned.</p>"}, "StartMalwareScan": {"name": "StartMalwareScan", "http": {"method": "POST", "requestUri": "/malware-scan/start", "responseCode": 200}, "input": {"shape": "StartMalwareScanRequest"}, "output": {"shape": "StartMalwareScanResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ConflictException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Initiates the malware scan. Invoking this API will automatically create the <a href=\"https://docs.aws.amazon.com/guardduty/latest/ug/slr-permissions-malware-protection.html\">Service-linked role</a> in the corresponding account.</p> <p>When the malware scan starts, you can use the associated scan ID to track the status of the scan. For more information, see <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_DescribeMalwareScans.html\">DescribeMalwareScans</a>.</p>"}, "StartMonitoringMembers": {"name": "StartMonitoringMembers", "http": {"method": "POST", "requestUri": "/detector/{detectorId}/member/start", "responseCode": 200}, "input": {"shape": "StartMonitoringMembersRequest"}, "output": {"shape": "StartMonitoringMembersResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Turns on GuardDuty monitoring of the specified member accounts. Use this operation to restart monitoring of accounts that you stopped monitoring with the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_StopMonitoringMembers.html\">StopMonitoringMembers</a> operation.</p>"}, "StopMonitoringMembers": {"name": "StopMonitoringMembers", "http": {"method": "POST", "requestUri": "/detector/{detectorId}/member/stop", "responseCode": 200}, "input": {"shape": "StopMonitoringMembersRequest"}, "output": {"shape": "StopMonitoringMembersResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Stops GuardDuty monitoring for the specified member accounts. Use the <code>StartMonitoringMembers</code> operation to restart monitoring for those accounts.</p> <p>With <code>autoEnableOrganizationMembers</code> configuration for your organization set to <code>ALL</code>, you'll receive an error if you attempt to stop monitoring the member accounts in your organization.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags/{resourceArn}", "responseCode": 204}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Adds tags to a resource.</p>"}, "UnarchiveFindings": {"name": "UnarchiveFindings", "http": {"method": "POST", "requestUri": "/detector/{detectorId}/findings/unarchive", "responseCode": 200}, "input": {"shape": "UnarchiveFindingsRequest"}, "output": {"shape": "UnarchiveFindingsResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Unarchives GuardDuty findings specified by the <code>findingIds</code>.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags/{resourceArn}", "responseCode": 204}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Removes tags from a resource.</p>"}, "UpdateDetector": {"name": "UpdateDetector", "http": {"method": "POST", "requestUri": "/detector/{detectorId}", "responseCode": 200}, "input": {"shape": "UpdateDetectorRequest"}, "output": {"shape": "UpdateDetectorResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Updates the GuardDuty detector specified by the detector ID.</p> <p>Specifying both EKS Runtime Monitoring (<code>EKS_RUNTIME_MONITORING</code>) and Runtime Monitoring (<code>RUNTIME_MONITORING</code>) will cause an error. You can add only one of these two features because Runtime Monitoring already includes the threat detection for Amazon EKS resources. For more information, see <a href=\"https://docs.aws.amazon.com/guardduty/latest/ug/runtime-monitoring.html\">Runtime Monitoring</a>.</p> <p>There might be regional differences because some data sources might not be available in all the Amazon Web Services Regions where GuardDuty is presently supported. For more information, see <a href=\"https://docs.aws.amazon.com/guardduty/latest/ug/guardduty_regions.html\">Regions and endpoints</a>.</p>"}, "UpdateFilter": {"name": "UpdateFilter", "http": {"method": "POST", "requestUri": "/detector/{detectorId}/filter/{filterName}", "responseCode": 200}, "input": {"shape": "UpdateFilterRequest"}, "output": {"shape": "UpdateFilterResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Updates the filter specified by the filter name.</p>"}, "UpdateFindingsFeedback": {"name": "UpdateFindingsFeedback", "http": {"method": "POST", "requestUri": "/detector/{detectorId}/findings/feedback", "responseCode": 200}, "input": {"shape": "UpdateFindingsFeedbackRequest"}, "output": {"shape": "UpdateFindingsFeedbackResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Marks the specified GuardDuty findings as useful or not useful.</p>"}, "UpdateIPSet": {"name": "UpdateIPSet", "http": {"method": "POST", "requestUri": "/detector/{detectorId}/ipset/{ipSetId}", "responseCode": 200}, "input": {"shape": "UpdateIPSetRequest"}, "output": {"shape": "UpdateIPSetResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Updates the IPSet specified by the IPSet ID.</p>"}, "UpdateMalwareProtectionPlan": {"name": "UpdateMalwareProtectionPlan", "http": {"method": "PATCH", "requestUri": "/malware-protection-plan/{malwareProtectionPlanId}", "responseCode": 200}, "input": {"shape": "UpdateMalwareProtectionPlanRequest"}, "errors": [{"shape": "BadRequestException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Updates an existing Malware Protection plan resource.</p>"}, "UpdateMalwareScanSettings": {"name": "UpdateMalwareScanSettings", "http": {"method": "POST", "requestUri": "/detector/{detectorId}/malware-scan-settings", "responseCode": 200}, "input": {"shape": "UpdateMalwareScanSettingsRequest"}, "output": {"shape": "UpdateMalwareScanSettingsResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Updates the malware scan settings.</p> <p>There might be regional differences because some data sources might not be available in all the Amazon Web Services Regions where GuardDuty is presently supported. For more information, see <a href=\"https://docs.aws.amazon.com/guardduty/latest/ug/guardduty_regions.html\">Regions and endpoints</a>.</p>"}, "UpdateMemberDetectors": {"name": "UpdateMemberDetectors", "http": {"method": "POST", "requestUri": "/detector/{detectorId}/member/detector/update", "responseCode": 200}, "input": {"shape": "UpdateMemberDetectorsRequest"}, "output": {"shape": "UpdateMemberDetectorsResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Contains information on member accounts to be updated.</p> <p>Specifying both EKS Runtime Monitoring (<code>EKS_RUNTIME_MONITORING</code>) and Runtime Monitoring (<code>RUNTIME_MONITORING</code>) will cause an error. You can add only one of these two features because Runtime Monitoring already includes the threat detection for Amazon EKS resources. For more information, see <a href=\"https://docs.aws.amazon.com/guardduty/latest/ug/runtime-monitoring.html\">Runtime Monitoring</a>.</p> <p>There might be regional differences because some data sources might not be available in all the Amazon Web Services Regions where GuardDuty is presently supported. For more information, see <a href=\"https://docs.aws.amazon.com/guardduty/latest/ug/guardduty_regions.html\">Regions and endpoints</a>.</p>"}, "UpdateOrganizationConfiguration": {"name": "UpdateOrganizationConfiguration", "http": {"method": "POST", "requestUri": "/detector/{detectorId}/admin", "responseCode": 200}, "input": {"shape": "UpdateOrganizationConfigurationRequest"}, "output": {"shape": "UpdateOrganizationConfigurationResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Configures the delegated administrator account with the provided values. You must provide a value for either <code>autoEnableOrganizationMembers</code> or <code>autoEnable</code>, but not both. </p> <p>Specifying both EKS Runtime Monitoring (<code>EKS_RUNTIME_MONITORING</code>) and Runtime Monitoring (<code>RUNTIME_MONITORING</code>) will cause an error. You can add only one of these two features because Runtime Monitoring already includes the threat detection for Amazon EKS resources. For more information, see <a href=\"https://docs.aws.amazon.com/guardduty/latest/ug/runtime-monitoring.html\">Runtime Monitoring</a>.</p> <p>There might be regional differences because some data sources might not be available in all the Amazon Web Services Regions where GuardDuty is presently supported. For more information, see <a href=\"https://docs.aws.amazon.com/guardduty/latest/ug/guardduty_regions.html\">Regions and endpoints</a>.</p>"}, "UpdatePublishingDestination": {"name": "UpdatePublishingDestination", "http": {"method": "POST", "requestUri": "/detector/{detectorId}/publishingDestination/{destinationId}", "responseCode": 200}, "input": {"shape": "UpdatePublishingDestinationRequest"}, "output": {"shape": "UpdatePublishingDestinationResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Updates information about the publishing destination specified by the <code>destinationId</code>.</p>"}, "UpdateThreatIntelSet": {"name": "UpdateThreatIntelSet", "http": {"method": "POST", "requestUri": "/detector/{detectorId}/threatintelset/{threatIntelSetId}", "responseCode": 200}, "input": {"shape": "UpdateThreatIntelSetRequest"}, "output": {"shape": "UpdateThreatIntelSetResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Updates the ThreatIntelSet specified by the ThreatIntelSet ID.</p>"}}, "shapes": {"AcceptAdministratorInvitationRequest": {"type": "structure", "required": ["DetectorId", "AdministratorId", "InvitationId"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The unique ID of the detector of the GuardDuty member account.</p>", "location": "uri", "locationName": "detectorId"}, "AdministratorId": {"shape": "String", "documentation": "<p>The account ID of the GuardDuty administrator account whose invitation you're accepting.</p>", "locationName": "administratorId"}, "InvitationId": {"shape": "String", "documentation": "<p>The value that is used to validate the administrator account to the member account.</p>", "locationName": "invitationId"}}}, "AcceptAdministratorInvitationResponse": {"type": "structure", "members": {}}, "AcceptInvitationRequest": {"type": "structure", "required": ["DetectorId", "MasterId", "InvitationId"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The unique ID of the detector of the GuardDuty member account.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}, "MasterId": {"shape": "String", "documentation": "<p>The account ID of the GuardDuty administrator account whose invitation you're accepting.</p>", "locationName": "masterId"}, "InvitationId": {"shape": "String", "documentation": "<p>The value that is used to validate the administrator account to the member account.</p>", "locationName": "invitationId"}}, "deprecated": true, "deprecatedMessage": "This input is deprecated, use AcceptAdministratorInvitationRequest instead"}, "AcceptInvitationResponse": {"type": "structure", "members": {}, "deprecated": true, "deprecatedMessage": "This output is deprecated, use AcceptAdministratorInvitationResponse instead"}, "AccessControlList": {"type": "structure", "members": {"AllowsPublicReadAccess": {"shape": "Boolean", "documentation": "<p>A value that indicates whether public read access for the bucket is enabled through an Access Control List (ACL).</p>", "locationName": "allowsPublicReadAccess"}, "AllowsPublicWriteAccess": {"shape": "Boolean", "documentation": "<p>A value that indicates whether public write access for the bucket is enabled through an Access Control List (ACL).</p>", "locationName": "allowsPublicWriteAccess"}}, "documentation": "<p>Contains information on the current access control policies for the bucket.</p>"}, "AccessDeniedException": {"type": "structure", "members": {"Message": {"shape": "String", "documentation": "<p>The error message.</p>", "locationName": "message"}, "Type": {"shape": "String", "documentation": "<p>The error type.</p>", "locationName": "__type"}}, "documentation": "<p>An access denied exception object.</p>", "error": {"httpStatusCode": 403}, "exception": true}, "AccessKey": {"type": "structure", "members": {"PrincipalId": {"shape": "String", "documentation": "<p>Principal ID of the user.</p>", "locationName": "principalId"}, "UserName": {"shape": "String", "documentation": "<p>Name of the user.</p>", "locationName": "userName"}, "UserType": {"shape": "String", "documentation": "<p>Type of the user.</p>", "locationName": "userType"}}, "documentation": "<p>Contains information about the access keys.</p>"}, "AccessKeyDetails": {"type": "structure", "members": {"AccessKeyId": {"shape": "String", "documentation": "<p>The access key ID of the user.</p>", "locationName": "accessKeyId"}, "PrincipalId": {"shape": "String", "documentation": "<p>The principal ID of the user.</p>", "locationName": "principalId"}, "UserName": {"shape": "String", "documentation": "<p>The name of the user.</p>", "locationName": "userName"}, "UserType": {"shape": "String", "documentation": "<p>The type of the user.</p>", "locationName": "userType"}}, "documentation": "<p>Contains information about the access keys.</p>"}, "Account": {"type": "structure", "required": ["<PERSON><PERSON>"], "members": {"Uid": {"shape": "String", "documentation": "<p>ID of the member's Amazon Web Services account</p>", "locationName": "uid"}, "Name": {"shape": "String", "documentation": "<p>Name of the member's Amazon Web Services account.</p>", "locationName": "account"}}, "documentation": "<p>Contains information about the account.</p>"}, "AccountDetail": {"type": "structure", "required": ["AccountId", "Email"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The member account ID.</p>", "locationName": "accountId"}, "Email": {"shape": "Email", "documentation": "<p>The email address of the member account.</p> <p>The rules for a valid email address:</p> <ul> <li> <p>The email address must be a minimum of 6 and a maximum of 64 characters long.</p> </li> <li> <p>All characters must be 7-bit ASCII characters.</p> </li> <li> <p>There must be one and only one @ symbol, which separates the local name from the domain name.</p> </li> <li> <p>The local name can't contain any of the following characters:</p> <p>whitespace, \" ' ( ) &lt; &gt; [ ] : ' , \\ | % &amp;</p> </li> <li> <p>The local name can't begin with a dot (.).</p> </li> <li> <p>The domain name can consist of only the characters [a-z], [A-Z], [0-9], hyphen (-), or dot (.).</p> </li> <li> <p>The domain name can't begin or end with a dot (.) or hyphen (-).</p> </li> <li> <p>The domain name must contain at least one dot. </p> </li> </ul>", "locationName": "email"}}, "documentation": "<p>Contains information about the account.</p>"}, "AccountDetails": {"type": "list", "member": {"shape": "AccountDetail"}, "max": 50, "min": 1}, "AccountFreeTrialInfo": {"type": "structure", "members": {"AccountId": {"shape": "String", "documentation": "<p>The account identifier of the GuardDuty member account.</p>", "locationName": "accountId"}, "DataSources": {"shape": "DataSourcesFreeTrial", "documentation": "<p>Describes the data source enabled for the GuardDuty member account.</p>", "deprecated": true, "deprecatedMessage": "This parameter is deprecated, use Features instead", "locationName": "dataSources"}, "Features": {"shape": "FreeTrialFeatureConfigurationsResults", "documentation": "<p>A list of features enabled for the GuardDuty account.</p>", "locationName": "features"}}, "documentation": "<p>Provides details of the GuardDuty member account that uses a free trial service.</p>"}, "AccountFreeTrialInfos": {"type": "list", "member": {"shape": "AccountFreeTrialInfo"}}, "AccountId": {"type": "string", "max": 12, "min": 12}, "AccountIds": {"type": "list", "member": {"shape": "AccountId"}, "max": 50, "min": 1}, "AccountLevelPermissions": {"type": "structure", "members": {"BlockPublicAccess": {"shape": "BlockPublicAccess", "documentation": "<p>Describes the S3 Block Public Access settings of the bucket's parent account.</p>", "locationName": "blockPublicAccess"}}, "documentation": "<p>Contains information about the account level permissions on the S3 bucket.</p>"}, "AccountStatistics": {"type": "structure", "members": {"AccountId": {"shape": "String", "documentation": "<p>The ID of the Amazon Web Services account.</p>", "locationName": "accountId"}, "LastGeneratedAt": {"shape": "Timestamp", "documentation": "<p>The timestamp at which the finding for this account was last generated.</p>", "locationName": "lastGeneratedAt"}, "TotalFindings": {"shape": "Integer", "documentation": "<p>The total number of findings associated with an account.</p>", "locationName": "totalFindings"}}, "documentation": "<p>Represents a list of map of accounts with the number of findings associated with each account.</p>"}, "Action": {"type": "structure", "members": {"ActionType": {"shape": "String", "documentation": "<p>The GuardDuty finding activity type.</p>", "locationName": "actionType"}, "AwsApiCallAction": {"shape": "AwsApiCallAction", "documentation": "<p>Information about the AWS_API_CALL action described in this finding.</p>", "locationName": "awsApiCallAction"}, "DnsRequestAction": {"shape": "DnsRequestAction", "documentation": "<p>Information about the DNS_REQUEST action described in this finding.</p>", "locationName": "dnsRequestAction"}, "NetworkConnectionAction": {"shape": "NetworkConnectionAction", "documentation": "<p>Information about the NETWORK_CONNECTION action described in this finding.</p>", "locationName": "networkConnectionAction"}, "PortProbeAction": {"shape": "PortProbeAction", "documentation": "<p>Information about the PORT_PROBE action described in this finding.</p>", "locationName": "portProbeAction"}, "KubernetesApiCallAction": {"shape": "KubernetesApiCallAction", "documentation": "<p>Information about the Kubernetes API call action described in this finding.</p>", "locationName": "kubernetesApiCallAction"}, "RdsLoginAttemptAction": {"shape": "RdsLoginAttemptAction", "documentation": "<p>Information about <code>RDS_LOGIN_ATTEMPT</code> action described in this finding.</p>", "locationName": "rdsLoginAttemptAction"}, "KubernetesPermissionCheckedDetails": {"shape": "KubernetesPermissionCheckedDetails", "documentation": "<p>Information whether the user has the permission to use a specific Kubernetes API.</p>", "locationName": "kubernetesPermissionCheckedDetails"}, "KubernetesRoleBindingDetails": {"shape": "KubernetesRoleBindingDetails", "documentation": "<p>Information about the role binding that grants the permission defined in a Kubernetes role.</p>", "locationName": "kubernetesRoleBindingDetails"}, "KubernetesRoleDetails": {"shape": "KubernetesRoleDetails", "documentation": "<p>Information about the Kubernetes role name and role type.</p>", "locationName": "kubernetesRoleDetails"}}, "documentation": "<p>Contains information about actions.</p>"}, "Actor": {"type": "structure", "required": ["Id"], "members": {"Id": {"shape": "String", "documentation": "<p>ID of the threat actor.</p>", "locationName": "id"}, "User": {"shape": "User", "documentation": "<p>Contains information about the user credentials used by the threat actor.</p>", "locationName": "user"}, "Session": {"shape": "Session", "documentation": "<p>Contains information about the user session where the activity initiated.</p>", "locationName": "session"}, "Process": {"shape": "ActorProcess", "documentation": "<p>Contains information about the process associated with the threat actor. This includes details such as process name, path, execution time, and unique identifiers that help track the actor's activities within the system.</p>", "locationName": "process"}}, "documentation": "<p>Information about the actors involved in an attack sequence.</p>"}, "ActorIds": {"type": "list", "member": {"shape": "String"}, "max": 400}, "ActorProcess": {"type": "structure", "required": ["Name", "Path"], "members": {"Name": {"shape": "ProcessName", "documentation": "<p>The name of the process as it appears in the system.</p>", "locationName": "name"}, "Path": {"shape": "ProcessPath", "documentation": "<p>The full file path to the process executable on the system.</p>", "locationName": "path"}, "Sha256": {"shape": "ProcessSha256", "documentation": "<p>The SHA256 hash of the process executable file, which can be used for identification and verification purposes.</p>", "locationName": "sha256"}}, "documentation": "<p>Contains information about a process involved in a GuardDuty finding, including process identification, execution details, and file information.</p>"}, "Actors": {"type": "list", "member": {"shape": "Actor"}, "max": 400}, "AdditionalSequenceTypes": {"type": "list", "member": {"shape": "FindingType"}}, "AddonDetails": {"type": "structure", "members": {"AddonVersion": {"shape": "String", "documentation": "<p>Version of the installed EKS add-on.</p>", "locationName": "addonVersion"}, "AddonStatus": {"shape": "String", "documentation": "<p>Status of the installed EKS add-on.</p>", "locationName": "addonStatus"}}, "documentation": "<p>Information about the installed EKS add-on (GuardDuty security agent).</p>"}, "AdminAccount": {"type": "structure", "members": {"AdminAccountId": {"shape": "String", "documentation": "<p>The Amazon Web Services account ID for the account.</p>", "locationName": "adminAccountId"}, "AdminStatus": {"shape": "AdminStatus", "documentation": "<p>Indicates whether the account is enabled as the delegated administrator.</p>", "locationName": "adminStatus"}}, "documentation": "<p>The account within the organization specified as the GuardDuty delegated administrator.</p>"}, "AdminAccounts": {"type": "list", "member": {"shape": "AdminAccount"}, "max": 1, "min": 0}, "AdminStatus": {"type": "string", "enum": ["ENABLED", "DISABLE_IN_PROGRESS"], "max": 300, "min": 1}, "Administrator": {"type": "structure", "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The ID of the account used as the administrator account.</p>", "locationName": "accountId"}, "InvitationId": {"shape": "String", "documentation": "<p>The value that is used to validate the administrator account to the member account.</p>", "locationName": "invitationId"}, "RelationshipStatus": {"shape": "String", "documentation": "<p>The status of the relationship between the administrator and member accounts.</p>", "locationName": "relationshipStatus"}, "InvitedAt": {"shape": "String", "documentation": "<p>The timestamp when the invitation was sent.</p>", "locationName": "invitedAt"}}, "documentation": "<p>Contains information about the administrator account and invitation.</p>"}, "AffectedResources": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "String"}}, "AgentDetails": {"type": "structure", "members": {"Version": {"shape": "String", "documentation": "<p>Version of the installed GuardDuty security agent.</p>", "locationName": "version"}}, "documentation": "<p>Information about the installed GuardDuty security agent.</p>"}, "Anomaly": {"type": "structure", "members": {"Profiles": {"shape": "AnomalyProfiles", "documentation": "<p>Information about the types of profiles.</p>", "locationName": "profiles"}, "Unusual": {"shape": "AnomalyUnusual", "documentation": "<p>Information about the behavior of the anomalies.</p>", "locationName": "unusual"}}, "documentation": "<p>Contains information about the anomalies.</p>"}, "AnomalyObject": {"type": "structure", "members": {"ProfileType": {"shape": "ProfileType", "documentation": "<p>The type of behavior of the profile.</p>", "locationName": "profileType"}, "ProfileSubtype": {"shape": "ProfileSubtype", "documentation": "<p>The frequency of the anomaly.</p>", "locationName": "profileSubtype"}, "Observations": {"shape": "Observations", "documentation": "<p>The recorded value.</p>", "locationName": "observations"}}, "documentation": "<p>Contains information about the unusual anomalies.</p>"}, "AnomalyProfileFeatureObjects": {"type": "list", "member": {"shape": "AnomalyObject"}}, "AnomalyProfileFeatures": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "AnomalyProfileFeatureObjects"}}, "AnomalyProfiles": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "AnomalyProfileFeatures"}}, "AnomalyUnusual": {"type": "structure", "members": {"Behavior": {"shape": "Behavior", "documentation": "<p>The behavior of the anomalous activity that caused GuardDuty to generate the finding.</p>", "locationName": "behavior"}}, "documentation": "<p>Contains information about the behavior of the anomaly that is new to GuardDuty.</p>"}, "AnomalyUnusualBehaviorFeature": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "AnomalyObject"}}, "ArchiveFindingsRequest": {"type": "structure", "required": ["DetectorId", "FindingIds"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The ID of the detector that specifies the GuardDuty service whose findings you want to archive.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}, "FindingIds": {"shape": "FindingIds", "documentation": "<p>The IDs of the findings that you want to archive.</p>", "locationName": "findingIds"}}}, "ArchiveFindingsResponse": {"type": "structure", "members": {}}, "AutoEnableMembers": {"type": "string", "enum": ["NEW", "ALL", "NONE"]}, "AutonomousSystem": {"type": "structure", "required": ["Name", "Number"], "members": {"Name": {"shape": "String", "documentation": "<p>Name associated with the Autonomous System (AS).</p>", "locationName": "name"}, "Number": {"shape": "Integer", "documentation": "<p>The unique number that identifies the Autonomous System (AS).</p>", "locationName": "number"}}, "documentation": "<p>Contains information about the Autonomous System (AS) associated with the network endpoints involved in an attack sequence.</p>"}, "AwsApiCallAction": {"type": "structure", "members": {"Api": {"shape": "String", "documentation": "<p>The Amazon Web Services API name.</p>", "locationName": "api"}, "CallerType": {"shape": "String", "documentation": "<p>The Amazon Web Services API caller type.</p>", "locationName": "callerType"}, "DomainDetails": {"shape": "DomainDetails", "documentation": "<p>The domain information for the Amazon Web Services API call.</p>", "locationName": "domainDetails"}, "ErrorCode": {"shape": "String", "documentation": "<p>The error code of the failed Amazon Web Services API action.</p>", "locationName": "errorCode"}, "UserAgent": {"shape": "String", "documentation": "<p>The agent through which the API request was made.</p>", "locationName": "userAgent"}, "RemoteIpDetails": {"shape": "RemoteIpDetails", "documentation": "<p>The remote IP information of the connection that initiated the Amazon Web Services API call.</p>", "locationName": "remoteIpDetails"}, "ServiceName": {"shape": "String", "documentation": "<p>The Amazon Web Services service name whose API was invoked.</p>", "locationName": "serviceName"}, "RemoteAccountDetails": {"shape": "RemoteAccountDetails", "documentation": "<p>The details of the Amazon Web Services account that made the API call. This field appears if the call was made from outside your account.</p>", "locationName": "remoteAccountDetails"}, "AffectedResources": {"shape": "AffectedResources", "documentation": "<p>The details of the Amazon Web Services account that made the API call. This field identifies the resources that were affected by this API call.</p>", "locationName": "affectedResources"}}, "documentation": "<p>Contains information about the API action.</p>"}, "BadRequestException": {"type": "structure", "members": {"Message": {"shape": "String", "documentation": "<p>The error message.</p>", "locationName": "message"}, "Type": {"shape": "String", "documentation": "<p>The error type.</p>", "locationName": "__type"}}, "documentation": "<p>A bad request exception object.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "Behavior": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "AnomalyUnusualBehaviorFeature"}}, "BlockPublicAccess": {"type": "structure", "members": {"IgnorePublicAcls": {"shape": "Boolean", "documentation": "<p>Indicates if S3 Block Public Access is set to <code>IgnorePublicAcls</code>.</p>", "locationName": "ignorePublicAcls"}, "RestrictPublicBuckets": {"shape": "Boolean", "documentation": "<p>Indicates if S3 Block Public Access is set to <code>RestrictPublicBuckets</code>.</p>", "locationName": "restrictPublicBuckets"}, "BlockPublicAcls": {"shape": "Boolean", "documentation": "<p>Indicates if S3 Block Public Access is set to <code>BlockPublicAcls</code>.</p>", "locationName": "blockPublicAcls"}, "BlockPublicPolicy": {"shape": "Boolean", "documentation": "<p>Indicates if S3 Block Public Access is set to <code>BlockPublicPolicy</code>.</p>", "locationName": "blockPublicPolicy"}}, "documentation": "<p>Contains information on how the bucker owner's S3 Block Public Access settings are being applied to the S3 bucket. See <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/access-control-block-public-access.html\">S3 Block Public Access</a> for more information. </p>"}, "Boolean": {"type": "boolean"}, "BucketLevelPermissions": {"type": "structure", "members": {"AccessControlList": {"shape": "AccessControlList", "documentation": "<p>Contains information on how Access Control Policies are applied to the bucket.</p>", "locationName": "accessControlList"}, "BucketPolicy": {"shape": "BucketPolicy", "documentation": "<p>Contains information on the bucket policies for the S3 bucket.</p>", "locationName": "bucketPolicy"}, "BlockPublicAccess": {"shape": "BlockPublicAccess", "documentation": "<p>Contains information on which account level S3 Block Public Access settings are applied to the S3 bucket.</p>", "locationName": "blockPublicAccess"}}, "documentation": "<p>Contains information about the bucket level permissions for the S3 bucket.</p>"}, "BucketPolicy": {"type": "structure", "members": {"AllowsPublicReadAccess": {"shape": "Boolean", "documentation": "<p>A value that indicates whether public read access for the bucket is enabled through a bucket policy.</p>", "locationName": "allowsPublicReadAccess"}, "AllowsPublicWriteAccess": {"shape": "Boolean", "documentation": "<p>A value that indicates whether public write access for the bucket is enabled through a bucket policy.</p>", "locationName": "allowsPublicWriteAccess"}}, "documentation": "<p>Contains information on the current bucket policies for the S3 bucket.</p>"}, "City": {"type": "structure", "members": {"CityName": {"shape": "String", "documentation": "<p>The city name of the remote IP address.</p>", "locationName": "cityName"}}, "documentation": "<p>Contains information about the city associated with the IP address.</p>"}, "ClientToken": {"type": "string", "max": 64, "min": 0}, "CloudTrailConfigurationResult": {"type": "structure", "required": ["Status"], "members": {"Status": {"shape": "DataSourceStatus", "documentation": "<p>Describes whether CloudTrail is enabled as a data source for the detector.</p>", "locationName": "status"}}, "documentation": "<p>Contains information on the status of CloudTrail as a data source for the detector.</p>"}, "ClusterStatus": {"type": "string", "enum": ["CREATING", "ACTIVE", "DELETING", "FAILED", "UPDATING", "PENDING"]}, "Condition": {"type": "structure", "members": {"Eq": {"shape": "Eq", "documentation": "<p>Represents the <i>equal</i> condition to be applied to a single field when querying for findings.</p>", "deprecated": true, "locationName": "eq"}, "Neq": {"shape": "Neq", "documentation": "<p>Represents the <i>not equal</i> condition to be applied to a single field when querying for findings.</p>", "deprecated": true, "locationName": "neq"}, "Gt": {"shape": "Integer", "documentation": "<p>Represents a <i>greater than</i> condition to be applied to a single field when querying for findings.</p>", "deprecated": true, "locationName": "gt"}, "Gte": {"shape": "Integer", "documentation": "<p>Represents a <i>greater than or equal</i> condition to be applied to a single field when querying for findings.</p>", "deprecated": true, "locationName": "gte"}, "Lt": {"shape": "Integer", "documentation": "<p>Represents a <i>less than</i> condition to be applied to a single field when querying for findings.</p>", "deprecated": true, "locationName": "lt"}, "Lte": {"shape": "Integer", "documentation": "<p>Represents a <i>less than or equal</i> condition to be applied to a single field when querying for findings.</p>", "deprecated": true, "locationName": "lte"}, "Equals": {"shape": "Equals", "documentation": "<p>Represents an <i>equal</i> <b/> condition to be applied to a single field when querying for findings.</p>", "locationName": "equals"}, "NotEquals": {"shape": "NotEquals", "documentation": "<p>Represents a <i>not equal</i> <b/> condition to be applied to a single field when querying for findings.</p>", "locationName": "notEquals"}, "GreaterThan": {"shape": "<PERSON>", "documentation": "<p>Represents a <i>greater than</i> condition to be applied to a single field when querying for findings.</p>", "locationName": "greaterThan"}, "GreaterThanOrEqual": {"shape": "<PERSON>", "documentation": "<p>Represents a <i>greater than or equal</i> condition to be applied to a single field when querying for findings.</p>", "locationName": "greaterThanOrEqual"}, "LessThan": {"shape": "<PERSON>", "documentation": "<p>Represents a <i>less than</i> condition to be applied to a single field when querying for findings.</p>", "locationName": "lessThan"}, "LessThanOrEqual": {"shape": "<PERSON>", "documentation": "<p>Represents a <i>less than or equal</i> condition to be applied to a single field when querying for findings.</p>", "locationName": "lessThanOrEqual"}}, "documentation": "<p>Contains information about the condition.</p>"}, "ConflictException": {"type": "structure", "members": {"Message": {"shape": "String", "documentation": "<p>The error message.</p>", "locationName": "message"}, "Type": {"shape": "String", "documentation": "<p>The error type.</p>", "locationName": "__type"}}, "documentation": "<p>A request conflict exception object.</p>", "error": {"httpStatusCode": 409}, "exception": true}, "Container": {"type": "structure", "members": {"ContainerRuntime": {"shape": "String", "documentation": "<p>The container runtime (such as, Docker or containerd) used to run the container.</p>", "locationName": "containerRuntime"}, "Id": {"shape": "String", "documentation": "<p>Container ID.</p>", "locationName": "id"}, "Name": {"shape": "String", "documentation": "<p>Container name.</p>", "locationName": "name"}, "Image": {"shape": "String", "documentation": "<p>Container image.</p>", "locationName": "image"}, "ImagePrefix": {"shape": "String", "documentation": "<p>Part of the image name before the last slash. For example, imagePrefix for public.ecr.aws/amazonlinux/amazonlinux:latest would be public.ecr.aws/amazonlinux. If the image name is relative and does not have a slash, this field is empty.</p>", "locationName": "imagePrefix"}, "VolumeMounts": {"shape": "VolumeMounts", "documentation": "<p>Container volume mounts.</p>", "locationName": "volumeMounts"}, "SecurityContext": {"shape": "SecurityContext", "documentation": "<p>Container security context.</p>", "locationName": "securityContext"}}, "documentation": "<p>Details of a container.</p>"}, "ContainerFindingResource": {"type": "structure", "required": ["Image"], "members": {"Image": {"shape": "String", "documentation": "<p>The container image information, including the image name and tag used to run the container that was involved in the finding.</p>", "locationName": "image"}, "ImageUid": {"shape": "ContainerImageUid", "documentation": "<p>The unique ID associated with the container image.</p>", "locationName": "imageUid"}}, "documentation": "<p>Contains information about container resources involved in a GuardDuty finding. This structure provides details about containers that were identified as part of suspicious or malicious activity.</p>"}, "ContainerImageUid": {"type": "string", "max": 1024, "min": 1}, "ContainerInstanceDetails": {"type": "structure", "members": {"CoveredContainerInstances": {"shape": "<PERSON>", "documentation": "<p>Represents the nodes in the Amazon ECS cluster that has a <code>HEALTHY</code> coverage status.</p>", "locationName": "coveredContainerInstances"}, "CompatibleContainerInstances": {"shape": "<PERSON>", "documentation": "<p>Represents total number of nodes in the Amazon ECS cluster.</p>", "locationName": "compatibleContainerInstances"}}, "documentation": "<p>Contains information about the Amazon EC2 instance that is running the Amazon ECS container.</p>"}, "ContainerUid": {"type": "string", "max": 256, "min": 0}, "ContainerUids": {"type": "list", "member": {"shape": "ContainerUid"}}, "Containers": {"type": "list", "member": {"shape": "Container"}}, "CountByCoverageStatus": {"type": "map", "key": {"shape": "CoverageStatus"}, "value": {"shape": "<PERSON>"}}, "CountByResourceType": {"type": "map", "key": {"shape": "ResourceType"}, "value": {"shape": "<PERSON>"}}, "CountBySeverity": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "Integer"}}, "Country": {"type": "structure", "members": {"CountryCode": {"shape": "String", "documentation": "<p>The country code of the remote IP address.</p>", "locationName": "countryCode"}, "CountryName": {"shape": "String", "documentation": "<p>The country name of the remote IP address.</p>", "locationName": "countryName"}}, "documentation": "<p>Contains information about the country where the remote IP address is located.</p>"}, "CoverageEc2InstanceDetails": {"type": "structure", "members": {"InstanceId": {"shape": "String", "documentation": "<p>The Amazon EC2 instance ID.</p>", "locationName": "instanceId"}, "InstanceType": {"shape": "String", "documentation": "<p>The instance type of the Amazon EC2 instance.</p>", "locationName": "instanceType"}, "ClusterArn": {"shape": "String", "documentation": "<p>The cluster ARN of the Amazon ECS cluster running on the Amazon EC2 instance.</p>", "locationName": "clusterArn"}, "AgentDetails": {"shape": "AgentDetails", "documentation": "<p>Information about the installed security agent.</p>", "locationName": "agentDetails"}, "ManagementType": {"shape": "ManagementType", "documentation": "<p>Indicates how the GuardDuty security agent is managed for this resource.</p> <ul> <li> <p> <code>AUTO_MANAGED</code> indicates that GuardDuty deploys and manages updates for this resource.</p> </li> <li> <p> <code>MANUAL</code> indicates that you are responsible to deploy, update, and manage the GuardDuty security agent updates for this resource.</p> </li> </ul> <note> <p>The <code>DISABLED</code> status doesn't apply to Amazon EC2 instances and Amazon EKS clusters.</p> </note>", "locationName": "managementType"}}, "documentation": "<p>Contains information about the Amazon EC2 instance runtime coverage details.</p>"}, "CoverageEcsClusterDetails": {"type": "structure", "members": {"ClusterName": {"shape": "String", "documentation": "<p>The name of the Amazon ECS cluster.</p>", "locationName": "clusterName"}, "FargateDetails": {"shape": "FargateDetails", "documentation": "<p>Information about the Fargate details associated with the Amazon ECS cluster.</p>", "locationName": "fargateDetails"}, "ContainerInstanceDetails": {"shape": "ContainerInstanceDetails", "documentation": "<p>Information about the Amazon ECS container running on Amazon EC2 instance.</p>", "locationName": "containerInstanceDetails"}}, "documentation": "<p>Contains information about Amazon ECS cluster runtime coverage details.</p>"}, "CoverageEksClusterDetails": {"type": "structure", "members": {"ClusterName": {"shape": "String", "documentation": "<p>Name of the EKS cluster.</p>", "locationName": "clusterName"}, "CoveredNodes": {"shape": "<PERSON>", "documentation": "<p>Represents the nodes within the EKS cluster that have a <code>HEALTHY</code> coverage status.</p>", "locationName": "coveredNodes"}, "CompatibleNodes": {"shape": "<PERSON>", "documentation": "<p>Represents all the nodes within the EKS cluster in your account.</p>", "locationName": "compatibleNodes"}, "AddonDetails": {"shape": "AddonDetails", "documentation": "<p>Information about the installed EKS add-on.</p>", "locationName": "addonDetails"}, "ManagementType": {"shape": "ManagementType", "documentation": "<p>Indicates how the Amazon EKS add-on GuardDuty agent is managed for this EKS cluster.</p> <p> <code>AUTO_MANAGED</code> indicates GuardDuty deploys and manages updates for this resource.</p> <p> <code>MA<PERSON><PERSON></code> indicates that you are responsible to deploy, update, and manage the Amazon EKS add-on GuardDuty agent for this resource.</p>", "locationName": "managementType"}}, "documentation": "<p>Information about the EKS cluster that has a coverage status.</p>"}, "CoverageFilterCondition": {"type": "structure", "members": {"Equals": {"shape": "Equals", "documentation": "<p>Represents an equal condition that is applied to a single field while retrieving the coverage details.</p>", "locationName": "equals"}, "NotEquals": {"shape": "NotEquals", "documentation": "<p>Represents a not equal condition that is applied to a single field while retrieving the coverage details.</p>", "locationName": "notEquals"}}, "documentation": "<p>Represents a condition that when matched will be added to the response of the operation.</p>"}, "CoverageFilterCriteria": {"type": "structure", "members": {"FilterCriterion": {"shape": "CoverageFilterCriterionList", "documentation": "<p>Represents a condition that when matched will be added to the response of the operation.</p>", "locationName": "filterCriterion"}}, "documentation": "<p>Represents the criteria used in the filter.</p>"}, "CoverageFilterCriterion": {"type": "structure", "members": {"CriterionKey": {"shape": "CoverageFilterCriterionKey", "documentation": "<p>An enum value representing possible filter fields.</p> <note> <p>Replace the enum value <code>CLUSTER_NAME</code> with <code>EKS_CLUSTER_NAME</code>. <code>CLUSTER_NAME</code> has been deprecated.</p> </note>", "locationName": "<PERSON><PERSON><PERSON>"}, "FilterCondition": {"shape": "CoverageFilterCondition", "documentation": "<p>Contains information about the condition.</p>", "locationName": "filterCondition"}}, "documentation": "<p>Represents a condition that when matched will be added to the response of the operation.</p>"}, "CoverageFilterCriterionKey": {"type": "string", "enum": ["ACCOUNT_ID", "CLUSTER_NAME", "RESOURCE_TYPE", "COVERAGE_STATUS", "ADDON_VERSION", "MANAGEMENT_TYPE", "EKS_CLUSTER_NAME", "ECS_CLUSTER_NAME", "AGENT_VERSION", "INSTANCE_ID", "CLUSTER_ARN"]}, "CoverageFilterCriterionList": {"type": "list", "member": {"shape": "CoverageFilterCriterion"}, "max": 50, "min": 0}, "CoverageResource": {"type": "structure", "members": {"ResourceId": {"shape": "String", "documentation": "<p>The unique ID of the resource.</p>", "locationName": "resourceId"}, "DetectorId": {"shape": "DetectorId", "documentation": "<p>The unique ID of the GuardDuty detector associated with the resource.</p>", "locationName": "detectorId"}, "AccountId": {"shape": "AccountId", "documentation": "<p>The unique ID of the Amazon Web Services account.</p>", "locationName": "accountId"}, "ResourceDetails": {"shape": "CoverageResourceDetails", "documentation": "<p>Information about the resource for which the coverage statistics are retrieved.</p>", "locationName": "resourceDetails"}, "CoverageStatus": {"shape": "CoverageStatus", "documentation": "<p>Represents the status of the EKS cluster coverage.</p>", "locationName": "coverageStatus"}, "Issue": {"shape": "String", "documentation": "<p>Represents the reason why a coverage status was <code>UNHEALTHY</code> for the EKS cluster.</p>", "locationName": "issue"}, "UpdatedAt": {"shape": "Timestamp", "documentation": "<p>The timestamp at which the coverage details for the resource were last updated. This is in UTC format.</p>", "locationName": "updatedAt"}}, "documentation": "<p>Information about the resource of the GuardDuty account.</p>"}, "CoverageResourceDetails": {"type": "structure", "members": {"EksClusterDetails": {"shape": "CoverageEksClusterDetails", "documentation": "<p>EKS cluster details involved in the coverage statistics.</p>", "locationName": "eksClusterDetails"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>The type of Amazon Web Services resource.</p>", "locationName": "resourceType"}, "EcsClusterDetails": {"shape": "CoverageEcsClusterDetails", "documentation": "<p>Information about the Amazon ECS cluster that is assessed for runtime coverage.</p>", "locationName": "ecsClusterDetails"}, "Ec2InstanceDetails": {"shape": "CoverageEc2InstanceDetails", "documentation": "<p>Information about the Amazon EC2 instance assessed for runtime coverage.</p>", "locationName": "ec2InstanceDetails"}}, "documentation": "<p>Information about the resource for each individual EKS cluster.</p>"}, "CoverageResources": {"type": "list", "member": {"shape": "CoverageResource"}}, "CoverageSortCriteria": {"type": "structure", "members": {"AttributeName": {"shape": "CoverageSortKey", "documentation": "<p>Represents the field name used to sort the coverage details.</p> <note> <p>Replace the enum value <code>CLUSTER_NAME</code> with <code>EKS_CLUSTER_NAME</code>. <code>CLUSTER_NAME</code> has been deprecated.</p> </note>", "locationName": "attributeName"}, "OrderBy": {"shape": "OrderBy", "documentation": "<p>The order in which the sorted findings are to be displayed.</p>", "locationName": "orderBy"}}, "documentation": "<p>Information about the sorting criteria used in the coverage statistics.</p>"}, "CoverageSortKey": {"type": "string", "enum": ["ACCOUNT_ID", "CLUSTER_NAME", "COVERAGE_STATUS", "ISSUE", "ADDON_VERSION", "UPDATED_AT", "EKS_CLUSTER_NAME", "ECS_CLUSTER_NAME", "INSTANCE_ID"]}, "CoverageStatistics": {"type": "structure", "members": {"CountByResourceType": {"shape": "CountByResourceType", "documentation": "<p>Represents coverage statistics for EKS clusters aggregated by resource type.</p>", "locationName": "countByResourceType"}, "CountByCoverageStatus": {"shape": "CountByCoverageStatus", "documentation": "<p>Represents coverage statistics for EKS clusters aggregated by coverage status.</p>", "locationName": "countByCoverageStatus"}}, "documentation": "<p>Information about the coverage statistics for a resource.</p>"}, "CoverageStatisticsType": {"type": "string", "enum": ["COUNT_BY_RESOURCE_TYPE", "COUNT_BY_COVERAGE_STATUS"]}, "CoverageStatisticsTypeList": {"type": "list", "member": {"shape": "CoverageStatisticsType"}}, "CoverageStatus": {"type": "string", "enum": ["HEALTHY", "UNHEALTHY"]}, "CreateDetectorRequest": {"type": "structure", "required": ["Enable"], "members": {"Enable": {"shape": "Boolean", "documentation": "<p>A Boolean value that specifies whether the detector is to be enabled.</p>", "locationName": "enable"}, "ClientToken": {"shape": "ClientToken", "documentation": "<p>The idempotency token for the create request.</p>", "idempotencyToken": true, "locationName": "clientToken"}, "FindingPublishingFrequency": {"shape": "FindingPublishingFrequency", "documentation": "<p>A value that specifies how frequently updated findings are exported.</p>", "locationName": "findingPublishingFrequency"}, "DataSources": {"shape": "DataSourceConfigurations", "documentation": "<p>Describes which data sources will be enabled for the detector.</p> <p>There might be regional differences because some data sources might not be available in all the Amazon Web Services Regions where GuardDuty is presently supported. For more information, see <a href=\"https://docs.aws.amazon.com/guardduty/latest/ug/guardduty_regions.html\">Regions and endpoints</a>.</p>", "deprecated": true, "deprecatedMessage": "This parameter is deprecated, use Features instead", "locationName": "dataSources"}, "Tags": {"shape": "TagMap", "documentation": "<p>The tags to be added to a new detector resource.</p>", "locationName": "tags"}, "Features": {"shape": "DetectorFeatureConfigurations", "documentation": "<p>A list of features that will be configured for the detector.</p>", "locationName": "features"}}}, "CreateDetectorResponse": {"type": "structure", "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The unique ID of the created detector.</p>", "locationName": "detectorId"}, "UnprocessedDataSources": {"shape": "UnprocessedDataSourcesResult", "documentation": "<p>Specifies the data sources that couldn't be enabled when GuardDuty was enabled for the first time.</p>", "locationName": "unprocessedDataSources"}}}, "CreateFilterRequest": {"type": "structure", "required": ["DetectorId", "Name", "FindingCriteria"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The detector ID associated with the GuardDuty account for which you want to create a filter.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}, "Name": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The name of the filter. Valid characters include period (.), underscore (_), dash (-), and alphanumeric characters. A whitespace is considered to be an invalid character.</p>", "locationName": "name"}, "Description": {"shape": "FilterDescription", "documentation": "<p>The description of the filter. Valid characters include alphanumeric characters, and special characters such as hyphen, period, colon, underscore, parentheses (<code>{ }</code>, <code>[ ]</code>, and <code>( )</code>), forward slash, horizontal tab, vertical tab, newline, form feed, return, and whitespace.</p>", "locationName": "description"}, "Action": {"shape": "FilterAction", "documentation": "<p>Specifies the action that is to be applied to the findings that match the filter.</p>", "locationName": "action"}, "Rank": {"shape": "FilterRank", "documentation": "<p>Specifies the position of the filter in the list of current filters. Also specifies the order in which this filter is applied to the findings.</p>", "locationName": "rank"}, "FindingCriteria": {"shape": "FindingCriteria", "documentation": "<p>Represents the criteria to be used in the filter for querying findings.</p> <p>You can only use the following attributes to query findings:</p> <ul> <li> <p>accountId</p> </li> <li> <p>id</p> </li> <li> <p>region</p> </li> <li> <p>severity</p> <p>To filter on the basis of severity, the API and CLI use the following input list for the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_FindingCriteria.html\">FindingCriteria</a> condition:</p> <ul> <li> <p> <b>Low</b>: <code>[\"1\", \"2\", \"3\"]</code> </p> </li> <li> <p> <b>Medium</b>: <code>[\"4\", \"5\", \"6\"]</code> </p> </li> <li> <p> <b>High</b>: <code>[\"7\", \"8\"]</code> </p> </li> <li> <p> <b>Critical</b>: <code>[\"9\", \"10\"]</code> </p> </li> </ul> <p>For more information, see <a href=\"https://docs.aws.amazon.com/guardduty/latest/ug/guardduty_findings-severity.html\">Findings severity levels</a> in the <i>Amazon GuardDuty User Guide</i>.</p> </li> <li> <p>type</p> </li> <li> <p>updatedAt</p> <p>Type: ISO 8601 string format: YYYY-MM-DDTHH:MM:SS.SSSZ or YYYY-MM-DDTHH:MM:SSZ depending on whether the value contains milliseconds.</p> </li> <li> <p>resource.accessKeyDetails.accessKeyId</p> </li> <li> <p>resource.accessKeyDetails.principalId</p> </li> <li> <p>resource.accessKeyDetails.userName</p> </li> <li> <p>resource.accessKeyDetails.userType</p> </li> <li> <p>resource.instanceDetails.iamInstanceProfile.id</p> </li> <li> <p>resource.instanceDetails.imageId</p> </li> <li> <p>resource.instanceDetails.instanceId</p> </li> <li> <p>resource.instanceDetails.tags.key</p> </li> <li> <p>resource.instanceDetails.tags.value</p> </li> <li> <p>resource.instanceDetails.networkInterfaces.ipv6Addresses</p> </li> <li> <p>resource.instanceDetails.networkInterfaces.privateIpAddresses.privateIpAddress</p> </li> <li> <p>resource.instanceDetails.networkInterfaces.publicDnsName</p> </li> <li> <p>resource.instanceDetails.networkInterfaces.publicIp</p> </li> <li> <p>resource.instanceDetails.networkInterfaces.securityGroups.groupId</p> </li> <li> <p>resource.instanceDetails.networkInterfaces.securityGroups.groupName</p> </li> <li> <p>resource.instanceDetails.networkInterfaces.subnetId</p> </li> <li> <p>resource.instanceDetails.networkInterfaces.vpcId</p> </li> <li> <p>resource.instanceDetails.outpostArn</p> </li> <li> <p>resource.resourceType</p> </li> <li> <p>resource.s3BucketDetails.publicAccess.effectivePermissions</p> </li> <li> <p>resource.s3BucketDetails.name</p> </li> <li> <p>resource.s3BucketDetails.tags.key</p> </li> <li> <p>resource.s3BucketDetails.tags.value</p> </li> <li> <p>resource.s3BucketDetails.type</p> </li> <li> <p>service.action.actionType</p> </li> <li> <p>service.action.awsApiCallAction.api</p> </li> <li> <p>service.action.awsApiCallAction.callerType</p> </li> <li> <p>service.action.awsApiCallAction.errorCode</p> </li> <li> <p>service.action.awsApiCallAction.remoteIpDetails.city.cityName</p> </li> <li> <p>service.action.awsApiCallAction.remoteIpDetails.country.countryName</p> </li> <li> <p>service.action.awsApiCallAction.remoteIpDetails.ipAddressV4</p> </li> <li> <p>service.action.awsApiCallAction.remoteIpDetails.ipAddressV6</p> </li> <li> <p>service.action.awsApiCallAction.remoteIpDetails.organization.asn</p> </li> <li> <p>service.action.awsApiCallAction.remoteIpDetails.organization.asnOrg</p> </li> <li> <p>service.action.awsApiCallAction.serviceName</p> </li> <li> <p>service.action.dnsRequestAction.domain</p> </li> <li> <p>service.action.dnsRequestAction.domainWithSuffix</p> </li> <li> <p>service.action.networkConnectionAction.blocked</p> </li> <li> <p>service.action.networkConnectionAction.connectionDirection</p> </li> <li> <p>service.action.networkConnectionAction.localPortDetails.port</p> </li> <li> <p>service.action.networkConnectionAction.protocol</p> </li> <li> <p>service.action.networkConnectionAction.remoteIpDetails.city.cityName</p> </li> <li> <p>service.action.networkConnectionAction.remoteIpDetails.country.countryName</p> </li> <li> <p>service.action.networkConnectionAction.remoteIpDetails.ipAddressV4</p> </li> <li> <p>service.action.networkConnectionAction.remoteIpDetails.ipAddressV6</p> </li> <li> <p>service.action.networkConnectionAction.remoteIpDetails.organization.asn</p> </li> <li> <p>service.action.networkConnectionAction.remoteIpDetails.organization.asnOrg</p> </li> <li> <p>service.action.networkConnectionAction.remotePortDetails.port</p> </li> <li> <p>service.action.awsApiCallAction.remoteAccountDetails.affiliated</p> </li> <li> <p>service.action.kubernetesApiCallAction.remoteIpDetails.ipAddressV4</p> </li> <li> <p>service.action.kubernetesApiCallAction.remoteIpDetails.ipAddressV6</p> </li> <li> <p>service.action.kubernetesApiCallAction.namespace</p> </li> <li> <p>service.action.kubernetesApiCallAction.remoteIpDetails.organization.asn</p> </li> <li> <p>service.action.kubernetesApiCallAction.requestUri</p> </li> <li> <p>service.action.kubernetesApiCallAction.statusCode</p> </li> <li> <p>service.action.networkConnectionAction.localIpDetails.ipAddressV4</p> </li> <li> <p>service.action.networkConnectionAction.localIpDetails.ipAddressV6</p> </li> <li> <p>service.action.networkConnectionAction.protocol</p> </li> <li> <p>service.action.awsApiCallAction.serviceName</p> </li> <li> <p>service.action.awsApiCallAction.remoteAccountDetails.accountId</p> </li> <li> <p>service.additionalInfo.threatListName</p> </li> <li> <p>service.resourceRole</p> </li> <li> <p>resource.eksClusterDetails.name</p> </li> <li> <p>resource.kubernetesDetails.kubernetesWorkloadDetails.name</p> </li> <li> <p>resource.kubernetesDetails.kubernetesWorkloadDetails.namespace</p> </li> <li> <p>resource.kubernetesDetails.kubernetesUserDetails.username</p> </li> <li> <p>resource.kubernetesDetails.kubernetesWorkloadDetails.containers.image</p> </li> <li> <p>resource.kubernetesDetails.kubernetesWorkloadDetails.containers.imagePrefix</p> </li> <li> <p>service.ebsVolumeScanDetails.scanId</p> </li> <li> <p>service.ebsVolumeScanDetails.scanDetections.threatDetectedByName.threatNames.name</p> </li> <li> <p>service.ebsVolumeScanDetails.scanDetections.threatDetectedByName.threatNames.severity</p> </li> <li> <p>service.ebsVolumeScanDetails.scanDetections.threatDetectedByName.threatNames.filePaths.hash</p> </li> <li> <p>resource.ecsClusterDetails.name</p> </li> <li> <p>resource.ecsClusterDetails.taskDetails.containers.image</p> </li> <li> <p>resource.ecsClusterDetails.taskDetails.definitionArn</p> </li> <li> <p>resource.containerDetails.image</p> </li> <li> <p>resource.rdsDbInstanceDetails.dbInstanceIdentifier</p> </li> <li> <p>resource.rdsDbInstanceDetails.dbClusterIdentifier</p> </li> <li> <p>resource.rdsDbInstanceDetails.engine</p> </li> <li> <p>resource.rdsDbUserDetails.user</p> </li> <li> <p>resource.rdsDbInstanceDetails.tags.key</p> </li> <li> <p>resource.rdsDbInstanceDetails.tags.value</p> </li> <li> <p>service.runtimeDetails.process.executableSha256</p> </li> <li> <p>service.runtimeDetails.process.name</p> </li> <li> <p>service.runtimeDetails.process.executablePath</p> </li> <li> <p>resource.lambdaDetails.functionName</p> </li> <li> <p>resource.lambdaDetails.functionArn</p> </li> <li> <p>resource.lambdaDetails.tags.key</p> </li> <li> <p>resource.lambdaDetails.tags.value</p> </li> </ul>", "locationName": "findingCriteria"}, "ClientToken": {"shape": "ClientToken", "documentation": "<p>The idempotency token for the create request.</p>", "idempotencyToken": true, "locationName": "clientToken"}, "Tags": {"shape": "TagMap", "documentation": "<p>The tags to be added to a new filter resource.</p>", "locationName": "tags"}}}, "CreateFilterResponse": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The name of the successfully created filter.</p>", "locationName": "name"}}}, "CreateIPSetRequest": {"type": "structure", "required": ["DetectorId", "Name", "Format", "Location", "Activate"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The unique ID of the detector of the GuardDuty account for which you want to create an IPSet.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}, "Name": {"shape": "Name", "documentation": "<p>The user-friendly name to identify the IPSet.</p> <p> Allowed characters are alphanumeric, whitespace, dash (-), and underscores (_).</p>", "locationName": "name"}, "Format": {"shape": "IpSetFormat", "documentation": "<p>The format of the file that contains the IPSet.</p>", "locationName": "format"}, "Location": {"shape": "Location", "documentation": "<p>The URI of the file that contains the IPSet. </p>", "locationName": "location"}, "Activate": {"shape": "Boolean", "documentation": "<p>A Boolean value that indicates whether GuardDuty is to start using the uploaded IPSet.</p>", "locationName": "activate"}, "ClientToken": {"shape": "ClientToken", "documentation": "<p>The idempotency token for the create request.</p>", "idempotencyToken": true, "locationName": "clientToken"}, "Tags": {"shape": "TagMap", "documentation": "<p>The tags to be added to a new IP set resource.</p>", "locationName": "tags"}}}, "CreateIPSetResponse": {"type": "structure", "required": ["IpSetId"], "members": {"IpSetId": {"shape": "String", "documentation": "<p>The ID of the IPSet resource.</p>", "locationName": "ipSetId"}}}, "CreateMalwareProtectionPlanRequest": {"type": "structure", "required": ["Role", "ProtectedResource"], "members": {"ClientToken": {"shape": "ClientToken", "documentation": "<p>The idempotency token for the create request.</p>", "idempotencyToken": true, "locationName": "clientToken"}, "Role": {"shape": "String", "documentation": "<p>Amazon Resource Name (ARN) of the IAM role that has the permissions to scan and add tags to the associated protected resource.</p>", "locationName": "role"}, "ProtectedResource": {"shape": "CreateProtectedResource", "documentation": "<p>Information about the protected resource that is associated with the created Malware Protection plan. Presently, <code>S3Bucket</code> is the only supported protected resource.</p>", "locationName": "protectedResource"}, "Actions": {"shape": "MalwareProtectionPlanActions", "documentation": "<p>Information about whether the tags will be added to the S3 object after scanning.</p>", "locationName": "actions"}, "Tags": {"shape": "TagMap", "documentation": "<p>Tags added to the Malware Protection plan resource. </p>", "locationName": "tags"}}}, "CreateMalwareProtectionPlanResponse": {"type": "structure", "members": {"MalwareProtectionPlanId": {"shape": "String", "documentation": "<p>A unique identifier associated with the Malware Protection plan resource.</p>", "locationName": "malwareProtectionPlanId"}}}, "CreateMembersRequest": {"type": "structure", "required": ["DetectorId", "AccountDetails"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The unique ID of the detector of the GuardDuty account for which you want to associate member accounts.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}, "AccountDetails": {"shape": "AccountDetails", "documentation": "<p>A list of account ID and email address pairs of the accounts that you want to associate with the GuardDuty administrator account.</p>", "locationName": "accountDetails"}}}, "CreateMembersResponse": {"type": "structure", "required": ["UnprocessedAccounts"], "members": {"UnprocessedAccounts": {"shape": "UnprocessedAccounts", "documentation": "<p>A list of objects that include the <code>accountIds</code> of the unprocessed accounts and a result string that explains why each was unprocessed.</p>", "locationName": "unprocessedAccounts"}}}, "CreateProtectedResource": {"type": "structure", "members": {"S3Bucket": {"shape": "CreateS3BucketResource", "documentation": "<p>Information about the protected S3 bucket resource.</p>", "locationName": "s3Bucket"}}, "documentation": "<p>Information about the protected resource that is associated with the created Malware Protection plan. Presently, <code>S3Bucket</code> is the only supported protected resource.</p>"}, "CreatePublishingDestinationRequest": {"type": "structure", "required": ["DetectorId", "DestinationType", "DestinationProperties"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The ID of the GuardDuty detector associated with the publishing destination.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}, "DestinationType": {"shape": "DestinationType", "documentation": "<p>The type of resource for the publishing destination. Currently only Amazon S3 buckets are supported.</p>", "locationName": "destinationType"}, "DestinationProperties": {"shape": "DestinationProperties", "documentation": "<p>The properties of the publishing destination, including the ARNs for the destination and the KMS key used for encryption.</p>", "locationName": "destinationProperties"}, "ClientToken": {"shape": "ClientToken", "documentation": "<p>The idempotency token for the request.</p>", "idempotencyToken": true, "locationName": "clientToken"}}}, "CreatePublishingDestinationResponse": {"type": "structure", "required": ["DestinationId"], "members": {"DestinationId": {"shape": "String", "documentation": "<p>The ID of the publishing destination that is created.</p>", "locationName": "destinationId"}}}, "CreateS3BucketResource": {"type": "structure", "members": {"BucketName": {"shape": "String", "documentation": "<p>Name of the S3 bucket.</p>", "locationName": "bucketName"}, "ObjectPrefixes": {"shape": "MalwareProtectionPlanObjectPrefixesList", "documentation": "<p>Information about the specified object prefixes. The S3 object will be scanned only if it belongs to any of the specified object prefixes.</p>", "locationName": "objectPrefixes"}}, "documentation": "<p>Information about the protected S3 bucket resource.</p>"}, "CreateSampleFindingsRequest": {"type": "structure", "required": ["DetectorId"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The ID of the detector for which you need to create sample findings.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}, "FindingTypes": {"shape": "FindingTypes", "documentation": "<p>The types of sample findings to generate.</p>", "locationName": "findingTypes"}}}, "CreateSampleFindingsResponse": {"type": "structure", "members": {}}, "CreateThreatIntelSetRequest": {"type": "structure", "required": ["DetectorId", "Name", "Format", "Location", "Activate"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The unique ID of the detector of the GuardDuty account for which you want to create a <code>ThreatIntelSet</code>.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}, "Name": {"shape": "Name", "documentation": "<p>A user-friendly ThreatIntelSet name displayed in all findings that are generated by activity that involves IP addresses included in this ThreatIntelSet.</p>", "locationName": "name"}, "Format": {"shape": "ThreatIntelSetFormat", "documentation": "<p>The format of the file that contains the ThreatIntelSet.</p>", "locationName": "format"}, "Location": {"shape": "Location", "documentation": "<p>The URI of the file that contains the ThreatIntelSet. </p>", "locationName": "location"}, "Activate": {"shape": "Boolean", "documentation": "<p>A Boolean value that indicates whether GuardDuty is to start using the uploaded ThreatIntelSet.</p>", "locationName": "activate"}, "ClientToken": {"shape": "ClientToken", "documentation": "<p>The idempotency token for the create request.</p>", "idempotencyToken": true, "locationName": "clientToken"}, "Tags": {"shape": "TagMap", "documentation": "<p>The tags to be added to a new threat list resource.</p>", "locationName": "tags"}}}, "CreateThreatIntelSetResponse": {"type": "structure", "required": ["ThreatIntelSetId"], "members": {"ThreatIntelSetId": {"shape": "String", "documentation": "<p>The ID of the ThreatIntelSet resource.</p>", "locationName": "threatIntelSetId"}}}, "Criterion": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "Condition"}}, "CriterionKey": {"type": "string", "enum": ["EC2_INSTANCE_ARN", "SCAN_ID", "ACCOUNT_ID", "GUARDDUTY_FINDING_ID", "SCAN_START_TIME", "SCAN_STATUS", "SCAN_TYPE"]}, "DNSLogsConfigurationResult": {"type": "structure", "required": ["Status"], "members": {"Status": {"shape": "DataSourceStatus", "documentation": "<p>Denotes whether DNS logs is enabled as a data source.</p>", "locationName": "status"}}, "documentation": "<p>Contains information on the status of DNS logs as a data source.</p>"}, "DataSource": {"type": "string", "enum": ["FLOW_LOGS", "CLOUD_TRAIL", "DNS_LOGS", "S3_LOGS", "KUBERNETES_AUDIT_LOGS", "EC2_MALWARE_SCAN"]}, "DataSourceConfigurations": {"type": "structure", "members": {"S3Logs": {"shape": "S3LogsConfiguration", "documentation": "<p>Describes whether S3 data event logs are enabled as a data source.</p>", "locationName": "s3Logs"}, "Kubernetes": {"shape": "KubernetesConfiguration", "documentation": "<p>Describes whether any Kubernetes logs are enabled as data sources.</p>", "locationName": "kubernetes"}, "MalwareProtection": {"shape": "MalwareProtectionConfiguration", "documentation": "<p>Describes whether Malware Protection is enabled as a data source.</p>", "locationName": "malwareProtection"}}, "documentation": "<p>Contains information about which data sources are enabled.</p>"}, "DataSourceConfigurationsResult": {"type": "structure", "required": ["CloudTrail", "DNSLogs", "FlowLogs", "S3Logs"], "members": {"CloudTrail": {"shape": "CloudTrailConfigurationResult", "documentation": "<p>An object that contains information on the status of CloudTrail as a data source.</p>", "locationName": "cloudTrail"}, "DNSLogs": {"shape": "DNSLogsConfigurationResult", "documentation": "<p>An object that contains information on the status of DNS logs as a data source.</p>", "locationName": "dnsLogs"}, "FlowLogs": {"shape": "FlowLogsConfigurationResult", "documentation": "<p>An object that contains information on the status of VPC flow logs as a data source.</p>", "locationName": "flowLogs"}, "S3Logs": {"shape": "S3LogsConfigurationResult", "documentation": "<p>An object that contains information on the status of S3 Data event logs as a data source.</p>", "locationName": "s3Logs"}, "Kubernetes": {"shape": "KubernetesConfigurationResult", "documentation": "<p>An object that contains information on the status of all Kubernetes data sources.</p>", "locationName": "kubernetes"}, "MalwareProtection": {"shape": "MalwareProtectionConfigurationResult", "documentation": "<p>Describes the configuration of Malware Protection data sources.</p>", "locationName": "malwareProtection"}}, "documentation": "<p>Contains information on the status of data sources for the detector.</p>"}, "DataSourceFreeTrial": {"type": "structure", "members": {"FreeTrialDaysRemaining": {"shape": "Integer", "documentation": "<p>A value that specifies the number of days left to use each enabled data source.</p>", "locationName": "freeTrialDaysRemaining"}}, "documentation": "<p>Contains information about which data sources are enabled for the GuardDuty member account.</p>"}, "DataSourceList": {"type": "list", "member": {"shape": "DataSource"}}, "DataSourceStatus": {"type": "string", "enum": ["ENABLED", "DISABLED"], "max": 300, "min": 1}, "DataSourcesFreeTrial": {"type": "structure", "members": {"CloudTrail": {"shape": "DataSourceFreeTrial", "documentation": "<p>Describes whether any Amazon Web Services CloudTrail management event logs are enabled as data sources.</p>", "locationName": "cloudTrail"}, "DnsLogs": {"shape": "DataSourceFreeTrial", "documentation": "<p>Describes whether any DNS logs are enabled as data sources.</p>", "locationName": "dnsLogs"}, "FlowLogs": {"shape": "DataSourceFreeTrial", "documentation": "<p>Describes whether any VPC Flow logs are enabled as data sources.</p>", "locationName": "flowLogs"}, "S3Logs": {"shape": "DataSourceFreeTrial", "documentation": "<p>Describes whether any S3 data event logs are enabled as data sources.</p>", "locationName": "s3Logs"}, "Kubernetes": {"shape": "KubernetesDataSourceFreeTrial", "documentation": "<p>Describes whether any Kubernetes logs are enabled as data sources.</p>", "locationName": "kubernetes"}, "MalwareProtection": {"shape": "MalwareProtectionDataSourceFreeTrial", "documentation": "<p>Describes whether Malware Protection is enabled as a data source.</p>", "locationName": "malwareProtection"}}, "documentation": "<p>Contains information about which data sources are enabled for the GuardDuty member account.</p>"}, "DateStatistics": {"type": "structure", "members": {"Date": {"shape": "Timestamp", "documentation": "<p>The timestamp when the total findings count is observed.</p> <p>For example, <code>Date</code> would look like <code>\"2024-09-05T17:00:00-07:00\"</code> whereas <code>LastGeneratedAt</code> would look like 2024-09-05T17:12:29-07:00\".</p>", "locationName": "date"}, "LastGeneratedAt": {"shape": "Timestamp", "documentation": "<p>The timestamp at which the last finding in the findings count, was generated.</p>", "locationName": "lastGeneratedAt"}, "Severity": {"shape": "Double", "documentation": "<p>The severity of the findings generated on each date.</p>", "locationName": "severity"}, "TotalFindings": {"shape": "Integer", "documentation": "<p>The total number of findings that were generated per severity level on each date.</p>", "locationName": "totalFindings"}}, "documentation": "<p>Represents list a map of dates with a count of total findings generated on each date.</p>"}, "DeclineInvitationsRequest": {"type": "structure", "required": ["AccountIds"], "members": {"AccountIds": {"shape": "AccountIds", "documentation": "<p>A list of account IDs of the Amazon Web Services accounts that sent invitations to the current member account that you want to decline invitations from.</p>", "locationName": "accountIds"}}}, "DeclineInvitationsResponse": {"type": "structure", "required": ["UnprocessedAccounts"], "members": {"UnprocessedAccounts": {"shape": "UnprocessedAccounts", "documentation": "<p>A list of objects that contain the unprocessed account and a result string that explains why it was unprocessed.</p>", "locationName": "unprocessedAccounts"}}}, "DefaultServerSideEncryption": {"type": "structure", "members": {"EncryptionType": {"shape": "String", "documentation": "<p>The type of encryption used for objects within the S3 bucket.</p>", "locationName": "encryptionType"}, "KmsMasterKeyArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the KMS encryption key. Only available if the bucket <code>EncryptionType</code> is <code>aws:kms</code>.</p>", "locationName": "kmsMasterKeyArn"}}, "documentation": "<p>Contains information on the server side encryption method used in the S3 bucket. See <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/serv-side-encryption.html\">S3 Server-Side Encryption</a> for more information.</p>"}, "DeleteDetectorRequest": {"type": "structure", "required": ["DetectorId"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The unique ID of the detector that you want to delete.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}}}, "DeleteDetectorResponse": {"type": "structure", "members": {}}, "DeleteFilterRequest": {"type": "structure", "required": ["DetectorId", "<PERSON><PERSON><PERSON><PERSON>"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The unique ID of the detector that is associated with the filter.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}, "FilterName": {"shape": "String", "documentation": "<p>The name of the filter that you want to delete.</p>", "location": "uri", "locationName": "filterName"}}}, "DeleteFilterResponse": {"type": "structure", "members": {}}, "DeleteIPSetRequest": {"type": "structure", "required": ["DetectorId", "IpSetId"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The unique ID of the detector associated with the IPSet.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}, "IpSetId": {"shape": "String", "documentation": "<p>The unique ID of the IPSet to delete.</p>", "location": "uri", "locationName": "ipSetId"}}}, "DeleteIPSetResponse": {"type": "structure", "members": {}}, "DeleteInvitationsRequest": {"type": "structure", "required": ["AccountIds"], "members": {"AccountIds": {"shape": "AccountIds", "documentation": "<p>A list of account IDs of the Amazon Web Services accounts that sent invitations to the current member account that you want to delete invitations from.</p>", "locationName": "accountIds"}}}, "DeleteInvitationsResponse": {"type": "structure", "required": ["UnprocessedAccounts"], "members": {"UnprocessedAccounts": {"shape": "UnprocessedAccounts", "documentation": "<p>A list of objects that contain the unprocessed account and a result string that explains why it was unprocessed.</p>", "locationName": "unprocessedAccounts"}}}, "DeleteMalwareProtectionPlanRequest": {"type": "structure", "required": ["MalwareProtectionPlanId"], "members": {"MalwareProtectionPlanId": {"shape": "String", "documentation": "<p>A unique identifier associated with Malware Protection plan resource.</p>", "location": "uri", "locationName": "malwareProtectionPlanId"}}}, "DeleteMembersRequest": {"type": "structure", "required": ["DetectorId", "AccountIds"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The unique ID of the detector of the GuardDuty account whose members you want to delete.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}, "AccountIds": {"shape": "AccountIds", "documentation": "<p>A list of account IDs of the GuardDuty member accounts that you want to delete.</p>", "locationName": "accountIds"}}}, "DeleteMembersResponse": {"type": "structure", "required": ["UnprocessedAccounts"], "members": {"UnprocessedAccounts": {"shape": "UnprocessedAccounts", "documentation": "<p>The accounts that could not be processed.</p>", "locationName": "unprocessedAccounts"}}}, "DeletePublishingDestinationRequest": {"type": "structure", "required": ["DetectorId", "DestinationId"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The unique ID of the detector associated with the publishing destination to delete.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}, "DestinationId": {"shape": "String", "documentation": "<p>The ID of the publishing destination to delete.</p>", "location": "uri", "locationName": "destinationId"}}}, "DeletePublishingDestinationResponse": {"type": "structure", "members": {}}, "DeleteThreatIntelSetRequest": {"type": "structure", "required": ["DetectorId", "ThreatIntelSetId"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The unique ID of the detector that is associated with the threatIntelSet.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}, "ThreatIntelSetId": {"shape": "String", "documentation": "<p>The unique ID of the threatIntelSet that you want to delete.</p>", "location": "uri", "locationName": "threatIntelSetId"}}}, "DeleteThreatIntelSetResponse": {"type": "structure", "members": {}}, "DescribeMalwareScansRequest": {"type": "structure", "required": ["DetectorId"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The unique ID of the detector that the request is associated with.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}, "NextToken": {"shape": "String", "documentation": "<p>You can use this parameter when paginating results. Set the value of this parameter to null on your first call to the list action. For subsequent calls to the action, fill nextToken in the request with the value of NextToken from the previous response to continue listing data.</p>", "locationName": "nextToken"}, "MaxResults": {"shape": "IntegerValueWithMax", "documentation": "<p>You can use this parameter to indicate the maximum number of items that you want in the response. The default value is 50. The maximum value is 50.</p>", "locationName": "maxResults"}, "FilterCriteria": {"shape": "FilterCriteria", "documentation": "<p>Represents the criteria to be used in the filter for describing scan entries.</p>", "locationName": "filterCriteria"}, "SortCriteria": {"shape": "SortCriteria", "documentation": "<p>Represents the criteria used for sorting scan entries. The <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_SortCriteria.html#guardduty-Type-SortCriteria-attributeName\"> <code>attributeName</code> </a> is required and it must be <code>scanStartTime</code>.</p>", "locationName": "sortCriteria"}}}, "DescribeMalwareScansResponse": {"type": "structure", "required": ["Scans"], "members": {"Scans": {"shape": "Scans", "documentation": "<p>Contains information about malware scans associated with GuardDuty Malware Protection for EC2.</p>", "locationName": "scans"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination parameter to be used on the next list operation to retrieve more items.</p>", "locationName": "nextToken"}}}, "DescribeOrganizationConfigurationRequest": {"type": "structure", "required": ["DetectorId"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The detector ID of the delegated administrator for which you need to retrieve the information.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>You can use this parameter to indicate the maximum number of items that you want in the response.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "String", "documentation": "<p>You can use this parameter when paginating results. Set the value of this parameter to null on your first call to the list action. For subsequent calls to the action, fill <code>nextToken</code> in the request with the value of <code>NextToken</code> from the previous response to continue listing data.</p>", "location": "querystring", "locationName": "nextToken"}}}, "DescribeOrganizationConfigurationResponse": {"type": "structure", "required": ["MemberAccountLimitReached"], "members": {"AutoEnable": {"shape": "Boolean", "documentation": "<p>Indicates whether GuardDuty is automatically enabled for accounts added to the organization.</p> <p>Even though this is still supported, we recommend using <code>AutoEnableOrganizationMembers</code> to achieve the similar results.</p>", "deprecated": true, "deprecatedMessage": "This field is deprecated, use AutoEnableOrganizationMembers instead", "locationName": "autoEnable"}, "MemberAccountLimitReached": {"shape": "Boolean", "documentation": "<p>Indicates whether the maximum number of allowed member accounts are already associated with the delegated administrator account for your organization.</p>", "locationName": "memberAccountLimitReached"}, "DataSources": {"shape": "OrganizationDataSourceConfigurationsResult", "documentation": "<p>Describes which data sources are enabled automatically for member accounts.</p>", "deprecated": true, "deprecatedMessage": "This parameter is deprecated, use Features instead", "locationName": "dataSources"}, "Features": {"shape": "OrganizationFeaturesConfigurationsResults", "documentation": "<p>A list of features that are configured for this organization.</p>", "locationName": "features"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination parameter to be used on the next list operation to retrieve more items.</p>", "locationName": "nextToken"}, "AutoEnableOrganizationMembers": {"shape": "AutoEnableMembers", "documentation": "<p>Indicates the auto-enablement configuration of GuardDuty or any of the corresponding protection plans for the member accounts in the organization.</p> <ul> <li> <p> <code>NEW</code>: Indicates that when a new account joins the organization, they will have GuardDuty or any of the corresponding protection plans enabled automatically. </p> </li> <li> <p> <code>ALL</code>: Indicates that all accounts in the organization have GuardDuty and any of the corresponding protection plans enabled automatically. This includes <code>NEW</code> accounts that join the organization and accounts that may have been suspended or removed from the organization in GuardDuty.</p> </li> <li> <p> <code>NONE</code>: Indicates that GuardDuty or any of the corresponding protection plans will not be automatically enabled for any account in the organization. The administrator must manage GuardDuty for each account in the organization individually.</p> <p>When you update the auto-enable setting from <code>ALL</code> or <code>NEW</code> to <code>NONE</code>, this action doesn't disable the corresponding option for your existing accounts. This configuration will apply to the new accounts that join the organization. After you update the auto-enable settings, no new account will have the corresponding option as enabled.</p> </li> </ul>", "locationName": "autoEnableOrganizationMembers"}}}, "DescribePublishingDestinationRequest": {"type": "structure", "required": ["DetectorId", "DestinationId"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The unique ID of the detector associated with the publishing destination to retrieve.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}, "DestinationId": {"shape": "String", "documentation": "<p>The ID of the publishing destination to retrieve.</p>", "location": "uri", "locationName": "destinationId"}}}, "DescribePublishingDestinationResponse": {"type": "structure", "required": ["DestinationId", "DestinationType", "Status", "PublishingFailureStartTimestamp", "DestinationProperties"], "members": {"DestinationId": {"shape": "String", "documentation": "<p>The ID of the publishing destination.</p>", "locationName": "destinationId"}, "DestinationType": {"shape": "DestinationType", "documentation": "<p>The type of publishing destination. Currently, only Amazon S3 buckets are supported.</p>", "locationName": "destinationType"}, "Status": {"shape": "PublishingStatus", "documentation": "<p>The status of the publishing destination.</p>", "locationName": "status"}, "PublishingFailureStartTimestamp": {"shape": "<PERSON>", "documentation": "<p>The time, in epoch millisecond format, at which Guard<PERSON>uty was first unable to publish findings to the destination.</p>", "locationName": "publishingFailureStartTimestamp"}, "DestinationProperties": {"shape": "DestinationProperties", "documentation": "<p>A <code>DestinationProperties</code> object that includes the <code>DestinationArn</code> and <code>KmsKeyArn</code> of the publishing destination.</p>", "locationName": "destinationProperties"}}}, "Destination": {"type": "structure", "required": ["DestinationId", "DestinationType", "Status"], "members": {"DestinationId": {"shape": "String", "documentation": "<p>The unique ID of the publishing destination.</p>", "locationName": "destinationId"}, "DestinationType": {"shape": "DestinationType", "documentation": "<p>The type of resource used for the publishing destination. Currently, only Amazon S3 buckets are supported.</p>", "locationName": "destinationType"}, "Status": {"shape": "PublishingStatus", "documentation": "<p>The status of the publishing destination.</p>", "locationName": "status"}}, "documentation": "<p>Contains information about the publishing destination, including the ID, type, and status.</p>"}, "DestinationProperties": {"type": "structure", "members": {"DestinationArn": {"shape": "String", "documentation": "<p>The ARN of the resource to publish to.</p> <p>To specify an S3 bucket folder use the following format: <code>arn:aws:s3:::DOC-EXAMPLE-BUCKET/myFolder/</code> </p>", "locationName": "destinationArn"}, "KmsKeyArn": {"shape": "String", "documentation": "<p>The ARN of the KMS key to use for encryption.</p>", "locationName": "kmsKeyArn"}}, "documentation": "<p>Contains the Amazon Resource Name (ARN) of the resource to publish to, such as an S3 bucket, and the ARN of the KMS key to use to encrypt published findings.</p>"}, "DestinationType": {"type": "string", "enum": ["S3"], "max": 300, "min": 1}, "Destinations": {"type": "list", "member": {"shape": "Destination"}}, "Detection": {"type": "structure", "members": {"Anomaly": {"shape": "Anomaly", "documentation": "<p>The details about the anomalous activity that caused GuardDuty to generate the finding.</p>", "locationName": "anomaly"}, "Sequence": {"shape": "Sequence", "documentation": "<p>The details about the attack sequence.</p>", "locationName": "sequence"}}, "documentation": "<p>Contains information about the detected behavior.</p>"}, "DetectorAdditionalConfiguration": {"type": "structure", "members": {"Name": {"shape": "FeatureAdditionalConfiguration", "documentation": "<p>Name of the additional configuration.</p>", "locationName": "name"}, "Status": {"shape": "FeatureStatus", "documentation": "<p>Status of the additional configuration.</p>", "locationName": "status"}}, "documentation": "<p>Information about the additional configuration for a feature in your GuardDuty account.</p>"}, "DetectorAdditionalConfigurationResult": {"type": "structure", "members": {"Name": {"shape": "FeatureAdditionalConfiguration", "documentation": "<p>Name of the additional configuration.</p>", "locationName": "name"}, "Status": {"shape": "FeatureStatus", "documentation": "<p>Status of the additional configuration.</p>", "locationName": "status"}, "UpdatedAt": {"shape": "Timestamp", "documentation": "<p>The timestamp at which the additional configuration was last updated. This is in UTC format.</p>", "locationName": "updatedAt"}}, "documentation": "<p>Information about the additional configuration.</p>"}, "DetectorAdditionalConfigurationResults": {"type": "list", "member": {"shape": "DetectorAdditionalConfigurationResult"}}, "DetectorAdditionalConfigurations": {"type": "list", "member": {"shape": "DetectorAdditionalConfiguration"}}, "DetectorFeature": {"type": "string", "enum": ["S3_DATA_EVENTS", "EKS_AUDIT_LOGS", "EBS_MALWARE_PROTECTION", "RDS_LOGIN_EVENTS", "EKS_RUNTIME_MONITORING", "LAMBDA_NETWORK_LOGS", "RUNTIME_MONITORING"]}, "DetectorFeatureConfiguration": {"type": "structure", "members": {"Name": {"shape": "DetectorFeature", "documentation": "<p>The name of the feature.</p>", "locationName": "name"}, "Status": {"shape": "FeatureStatus", "documentation": "<p>The status of the feature.</p>", "locationName": "status"}, "AdditionalConfiguration": {"shape": "DetectorAdditionalConfigurations", "documentation": "<p>Additional configuration for a resource.</p>", "locationName": "additionalConfiguration"}}, "documentation": "<p>Contains information about a GuardDuty feature.</p> <p>Specifying both EKS Runtime Monitoring (<code>EKS_RUNTIME_MONITORING</code>) and Runtime Monitoring (<code>RUNTIME_MONITORING</code>) will cause an error. You can add only one of these two features because Runtime Monitoring already includes the threat detection for Amazon EKS resources. For more information, see <a href=\"https://docs.aws.amazon.com/guardduty/latest/ug/runtime-monitoring.html\">Runtime Monitoring</a>.</p>"}, "DetectorFeatureConfigurationResult": {"type": "structure", "members": {"Name": {"shape": "DetectorFeatureResult", "documentation": "<p>Indicates the name of the feature that can be enabled for the detector.</p>", "locationName": "name"}, "Status": {"shape": "FeatureStatus", "documentation": "<p>Indicates the status of the feature that is enabled for the detector.</p>", "locationName": "status"}, "UpdatedAt": {"shape": "Timestamp", "documentation": "<p>The timestamp at which the feature object was updated.</p>", "locationName": "updatedAt"}, "AdditionalConfiguration": {"shape": "DetectorAdditionalConfigurationResults", "documentation": "<p>Additional configuration for a resource.</p>", "locationName": "additionalConfiguration"}}, "documentation": "<p>Contains information about a GuardDuty feature.</p> <p>Specifying both EKS Runtime Monitoring (<code>EKS_RUNTIME_MONITORING</code>) and Runtime Monitoring (<code>RUNTIME_MONITORING</code>) will cause an error. You can add only one of these two features because Runtime Monitoring already includes the threat detection for Amazon EKS resources. For more information, see <a href=\"https://docs.aws.amazon.com/guardduty/latest/ug/runtime-monitoring.html\">Runtime Monitoring</a>.</p>"}, "DetectorFeatureConfigurations": {"type": "list", "member": {"shape": "DetectorFeatureConfiguration"}}, "DetectorFeatureConfigurationsResults": {"type": "list", "member": {"shape": "DetectorFeatureConfigurationResult"}}, "DetectorFeatureResult": {"type": "string", "enum": ["FLOW_LOGS", "CLOUD_TRAIL", "DNS_LOGS", "S3_DATA_EVENTS", "EKS_AUDIT_LOGS", "EBS_MALWARE_PROTECTION", "RDS_LOGIN_EVENTS", "EKS_RUNTIME_MONITORING", "LAMBDA_NETWORK_LOGS", "RUNTIME_MONITORING"]}, "DetectorId": {"type": "string", "max": 300, "min": 1}, "DetectorIds": {"type": "list", "member": {"shape": "DetectorId"}, "max": 50, "min": 0}, "DetectorStatus": {"type": "string", "enum": ["ENABLED", "DISABLED"], "max": 300, "min": 1}, "DisableOrganizationAdminAccountRequest": {"type": "structure", "required": ["AdminAccountId"], "members": {"AdminAccountId": {"shape": "String", "documentation": "<p>The Amazon Web Services Account ID for the organizations account to be disabled as a GuardDuty delegated administrator.</p>", "locationName": "adminAccountId"}}}, "DisableOrganizationAdminAccountResponse": {"type": "structure", "members": {}}, "DisassociateFromAdministratorAccountRequest": {"type": "structure", "required": ["DetectorId"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The unique ID of the detector of the GuardDuty member account.</p>", "location": "uri", "locationName": "detectorId"}}}, "DisassociateFromAdministratorAccountResponse": {"type": "structure", "members": {}}, "DisassociateFromMasterAccountRequest": {"type": "structure", "required": ["DetectorId"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The unique ID of the detector of the GuardDuty member account.</p>", "location": "uri", "locationName": "detectorId"}}, "deprecated": true, "deprecatedMessage": "This input is deprecated, use DisassociateFromAdministratorAccountRequest instead"}, "DisassociateFromMasterAccountResponse": {"type": "structure", "members": {}, "deprecated": true, "deprecatedMessage": "This output is deprecated, use DisassociateFromAdministratorAccountResponse instead"}, "DisassociateMembersRequest": {"type": "structure", "required": ["DetectorId", "AccountIds"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The unique ID of the detector of the GuardDuty account whose members you want to disassociate from the administrator account.</p>", "location": "uri", "locationName": "detectorId"}, "AccountIds": {"shape": "AccountIds", "documentation": "<p>A list of account IDs of the GuardDuty member accounts that you want to disassociate from the administrator account.</p>", "locationName": "accountIds"}}}, "DisassociateMembersResponse": {"type": "structure", "required": ["UnprocessedAccounts"], "members": {"UnprocessedAccounts": {"shape": "UnprocessedAccounts", "documentation": "<p>A list of objects that contain the unprocessed account and a result string that explains why it was unprocessed.</p>", "locationName": "unprocessedAccounts"}}}, "DnsRequestAction": {"type": "structure", "members": {"Domain": {"shape": "String", "documentation": "<p>The domain information for the DNS query.</p>", "locationName": "domain"}, "Protocol": {"shape": "String", "documentation": "<p>The network connection protocol observed in the activity that prompted GuardDuty to generate the finding.</p>", "locationName": "protocol"}, "Blocked": {"shape": "Boolean", "documentation": "<p>Indicates whether the targeted port is blocked.</p>", "locationName": "blocked"}, "DomainWithSuffix": {"shape": "String", "documentation": "<p>The second and top level domain involved in the activity that potentially prompted GuardDuty to generate this finding. For a list of top-level and second-level domains, see <a href=\"https://publicsuffix.org/\">public suffix list</a>.</p>", "locationName": "domainWithSuffix"}}, "documentation": "<p>Contains information about the DNS_REQUEST action described in this finding.</p>"}, "DomainDetails": {"type": "structure", "members": {"Domain": {"shape": "String", "documentation": "<p>The domain information for the Amazon Web Services API call.</p>", "locationName": "domain"}}, "documentation": "<p>Contains information about the domain.</p>"}, "Double": {"type": "double"}, "EbsSnapshotPreservation": {"type": "string", "enum": ["NO_RETENTION", "RETENTION_WITH_FINDING"]}, "EbsVolumeDetails": {"type": "structure", "members": {"ScannedVolumeDetails": {"shape": "VolumeDetails", "documentation": "<p>List of EBS volumes that were scanned.</p>", "locationName": "scannedVolumeDetails"}, "SkippedVolumeDetails": {"shape": "VolumeDetails", "documentation": "<p>List of EBS volumes that were skipped from the malware scan.</p>", "locationName": "skippedVolumeDetails"}}, "documentation": "<p>Contains list of scanned and skipped EBS volumes with details.</p>"}, "EbsVolumeScanDetails": {"type": "structure", "members": {"ScanId": {"shape": "String", "documentation": "<p>Unique Id of the malware scan that generated the finding.</p>", "locationName": "scanId"}, "ScanStartedAt": {"shape": "Timestamp", "documentation": "<p>Returns the start date and time of the malware scan.</p>", "locationName": "scanStartedAt"}, "ScanCompletedAt": {"shape": "Timestamp", "documentation": "<p>Returns the completion date and time of the malware scan.</p>", "locationName": "scanCompletedAt"}, "TriggerFindingId": {"shape": "String", "documentation": "<p>Guard<PERSON>uty finding ID that triggered a malware scan.</p>", "locationName": "triggerFindingId"}, "Sources": {"shape": "Sources", "documentation": "<p>Contains list of threat intelligence sources used to detect threats.</p>", "locationName": "sources"}, "ScanDetections": {"shape": "ScanDetections", "documentation": "<p>Contains a complete view providing malware scan result details.</p>", "locationName": "scanDetections"}, "ScanType": {"shape": "ScanType", "documentation": "<p>Specifies the scan type that invoked the malware scan.</p>", "locationName": "scanType"}}, "documentation": "<p>Contains details from the malware scan that created a finding.</p>"}, "EbsVolumesResult": {"type": "structure", "members": {"Status": {"shape": "DataSourceStatus", "documentation": "<p>Describes whether scanning EBS volumes is enabled as a data source.</p>", "locationName": "status"}, "Reason": {"shape": "String", "documentation": "<p>Specifies the reason why scanning EBS volumes (Malware Protection) was not enabled as a data source.</p>", "locationName": "reason"}}, "documentation": "<p>Describes the configuration of scanning EBS volumes as a data source.</p>"}, "Ec2Instance": {"type": "structure", "members": {"AvailabilityZone": {"shape": "String", "documentation": "<p>The availability zone of the Amazon EC2 instance. For more information, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/using-regions-availability-zones.html#concepts-availability-zones\">Availability zones</a> in the <i>Amazon EC2 User Guide</i>.</p>", "locationName": "availabilityZone"}, "ImageDescription": {"shape": "String", "documentation": "<p>The image description of the Amazon EC2 instance.</p>", "locationName": "imageDescription"}, "InstanceState": {"shape": "String", "documentation": "<p>The state of the Amazon EC2 instance. For more information, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/ec2-instance-lifecycle.html\">Amazon EC2 instance state changes</a> in the <i>Amazon EC2 User Guide</i>.</p>", "locationName": "instanceState"}, "IamInstanceProfile": {"shape": "IamInstanceProfile"}, "InstanceType": {"shape": "String", "documentation": "<p>Type of the Amazon EC2 instance.</p>", "locationName": "instanceType"}, "OutpostArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the Amazon Web Services Outpost. This shows applicable Amazon Web Services Outposts instances.</p>", "locationName": "outpostArn"}, "Platform": {"shape": "String", "documentation": "<p>The platform of the Amazon EC2 instance.</p>", "locationName": "platform"}, "ProductCodes": {"shape": "ProductCodes", "documentation": "<p>The product code of the Amazon EC2 instance.</p>", "locationName": "productCodes"}, "Ec2NetworkInterfaceUids": {"shape": "Ec2NetworkInterfaceUids", "documentation": "<p>The ID of the network interface.</p>", "locationName": "ec2NetworkInterfaceUids"}}, "documentation": "<p>Details about the potentially impacted Amazon EC2 instance resource.</p>"}, "Ec2InstanceUid": {"type": "string", "max": 256, "min": 0}, "Ec2InstanceUids": {"type": "list", "member": {"shape": "Ec2InstanceUid"}, "max": 25, "min": 0}, "Ec2NetworkInterface": {"type": "structure", "members": {"Ipv6Addresses": {"shape": "Ipv6Addresses", "documentation": "<p>A list of IPv6 addresses for the Amazon EC2 instance.</p>", "locationName": "ipv6Addresses"}, "PrivateIpAddresses": {"shape": "PrivateIpAddresses", "documentation": "<p>Other private IP address information of the Amazon EC2 instance.</p>", "locationName": "privateIpAddresses"}, "PublicIp": {"shape": "String", "documentation": "<p>The public IP address of the Amazon EC2 instance.</p>", "locationName": "publicIp"}, "SecurityGroups": {"shape": "SecurityGroups", "documentation": "<p>The security groups associated with the Amazon EC2 instance.</p>", "locationName": "securityGroups"}, "SubNetId": {"shape": "String", "documentation": "<p>The subnet ID of the Amazon EC2 instance.</p>", "locationName": "subNetId"}, "VpcId": {"shape": "String", "documentation": "<p>The VPC ID of the Amazon EC2 instance.</p>", "locationName": "vpcId"}}, "documentation": "<p>Contains information about the elastic network interface of the Amazon EC2 instance.</p>"}, "Ec2NetworkInterfaceUids": {"type": "list", "member": {"shape": "String"}}, "EcsClusterDetails": {"type": "structure", "members": {"Name": {"shape": "String", "documentation": "<p>The name of the ECS Cluster.</p>", "locationName": "name"}, "Arn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) that identifies the cluster.</p>", "locationName": "arn"}, "Status": {"shape": "String", "documentation": "<p>The status of the ECS cluster.</p>", "locationName": "status"}, "ActiveServicesCount": {"shape": "Integer", "documentation": "<p>The number of services that are running on the cluster in an ACTIVE state.</p>", "locationName": "activeServicesCount"}, "RegisteredContainerInstancesCount": {"shape": "Integer", "documentation": "<p>The number of container instances registered into the cluster.</p>", "locationName": "registeredContainerInstancesCount"}, "RunningTasksCount": {"shape": "Integer", "documentation": "<p>The number of tasks in the cluster that are in the RUNNING state.</p>", "locationName": "runningTasksCount"}, "Tags": {"shape": "Tags", "documentation": "<p>The tags of the ECS Cluster.</p>", "locationName": "tags"}, "TaskDetails": {"shape": "EcsTaskDetails", "documentation": "<p>Contains information about the details of the ECS Task.</p>", "locationName": "taskDetails"}}, "documentation": "<p>Contains information about the details of the ECS Cluster.</p>"}, "EcsTaskDetails": {"type": "structure", "members": {"Arn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the task.</p>", "locationName": "arn"}, "DefinitionArn": {"shape": "String", "documentation": "<p>The ARN of the task definition that creates the task.</p>", "locationName": "definitionArn"}, "Version": {"shape": "String", "documentation": "<p>The version counter for the task.</p>", "locationName": "version"}, "TaskCreatedAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp for the time when the task was created.</p>", "locationName": "createdAt"}, "StartedAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp for the time when the task started.</p>", "locationName": "startedAt"}, "StartedBy": {"shape": "String", "documentation": "<p>Contains the tag specified when a task is started.</p>", "locationName": "startedBy"}, "Tags": {"shape": "Tags", "documentation": "<p>The tags of the ECS Task.</p>", "locationName": "tags"}, "Volumes": {"shape": "Volumes", "documentation": "<p>The list of data volume definitions for the task.</p>", "locationName": "volumes"}, "Containers": {"shape": "Containers", "documentation": "<p>The containers that's associated with the task.</p>", "locationName": "containers"}, "Group": {"shape": "String", "documentation": "<p>The name of the task group that's associated with the task.</p>", "locationName": "group"}, "LaunchType": {"shape": "String", "documentation": "<p>A capacity on which the task is running. For example, <code>Fargate</code> and <code>EC2</code>.</p>", "locationName": "launchType"}}, "documentation": "<p>Contains information about the task in an ECS cluster.</p>"}, "EksCluster": {"type": "structure", "members": {"Arn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) that uniquely identifies the Amazon EKS cluster involved in the finding.</p>", "locationName": "arn"}, "CreatedAt": {"shape": "Timestamp", "documentation": "<p>The timestamp indicating when the Amazon EKS cluster was created, in UTC format.</p>", "locationName": "createdAt"}, "Status": {"shape": "ClusterStatus", "documentation": "<p>The current status of the Amazon EKS cluster.</p>", "locationName": "status"}, "VpcId": {"shape": "String", "documentation": "<p>The ID of the Amazon Virtual Private Cloud (Amazon VPC) associated with the Amazon EKS cluster.</p>", "locationName": "vpcId"}, "Ec2InstanceUids": {"shape": "Ec2InstanceUids", "documentation": "<p>A list of unique identifiers for the Amazon EC2 instances that serve as worker nodes in the Amazon EKS cluster.</p>", "locationName": "ec2InstanceUids"}}, "documentation": "<p>Contains information about the Amazon EKS cluster involved in a GuardDuty finding, including cluster identification, status, and network configuration.</p>"}, "EksClusterDetails": {"type": "structure", "members": {"Name": {"shape": "String", "documentation": "<p>EKS cluster name.</p>", "locationName": "name"}, "Arn": {"shape": "String", "documentation": "<p>EKS cluster ARN.</p>", "locationName": "arn"}, "VpcId": {"shape": "String", "documentation": "<p>The VPC ID to which the EKS cluster is attached.</p>", "locationName": "vpcId"}, "Status": {"shape": "String", "documentation": "<p>The EKS cluster status.</p>", "locationName": "status"}, "Tags": {"shape": "Tags", "documentation": "<p>The EKS cluster tags.</p>", "locationName": "tags"}, "CreatedAt": {"shape": "Timestamp", "documentation": "<p>The timestamp when the EKS cluster was created.</p>", "locationName": "createdAt"}}, "documentation": "<p>Details about the EKS cluster involved in a Kubernetes finding.</p>"}, "Email": {"type": "string", "max": 64, "min": 6, "pattern": "See rules in parameter description", "sensitive": true}, "EnableOrganizationAdminAccountRequest": {"type": "structure", "required": ["AdminAccountId"], "members": {"AdminAccountId": {"shape": "String", "documentation": "<p>The Amazon Web Services account ID for the organization account to be enabled as a GuardDuty delegated administrator.</p>", "locationName": "adminAccountId"}}}, "EnableOrganizationAdminAccountResponse": {"type": "structure", "members": {}}, "EndpointIds": {"type": "list", "member": {"shape": "String"}, "max": 400}, "Eq": {"type": "list", "member": {"shape": "String"}}, "Equals": {"type": "list", "member": {"shape": "String"}}, "Evidence": {"type": "structure", "members": {"ThreatIntelligenceDetails": {"shape": "ThreatIntelligenceDetails", "documentation": "<p>A list of threat intelligence details related to the evidence.</p>", "locationName": "threatIntelligenceDetails"}}, "documentation": "<p>Contains information about the reason that the finding was generated.</p>"}, "FargateDetails": {"type": "structure", "members": {"Issues": {"shape": "Issues", "documentation": "<p>Runtime coverage issues identified for the resource running on Amazon Web Services Fargate.</p>", "locationName": "issues"}, "ManagementType": {"shape": "ManagementType", "documentation": "<p>Indicates how the GuardDuty security agent is managed for this resource.</p> <ul> <li> <p> <code>AUTO_MANAGED</code> indicates that GuardDuty deploys and manages updates for this resource.</p> </li> <li> <p> <code>DISABLED</code> indicates that the deployment of the GuardDuty security agent is disabled for this resource.</p> </li> </ul> <note> <p>The <code>MANUAL</code> status doesn't apply to the Amazon Web Services Fargate (Amazon ECS only) woprkloads.</p> </note>", "locationName": "managementType"}}, "documentation": "<p>Contains information about Amazon Web Services Fargate details associated with an Amazon ECS cluster.</p>"}, "FeatureAdditionalConfiguration": {"type": "string", "enum": ["EKS_ADDON_MANAGEMENT", "ECS_FARGATE_AGENT_MANAGEMENT", "EC2_AGENT_MANAGEMENT"]}, "FeatureStatus": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "Feedback": {"type": "string", "enum": ["USEFUL", "NOT_USEFUL"]}, "FilePaths": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "FilterAction": {"type": "string", "enum": ["NOOP", "ARCHIVE"], "max": 300, "min": 1}, "FilterCondition": {"type": "structure", "members": {"EqualsValue": {"shape": "NonEmptyString", "documentation": "<p>Represents an <i>equal</i> <b/> condition to be applied to a single field when querying for scan entries.</p>", "locationName": "equalsValue"}, "GreaterThan": {"shape": "LongValue", "documentation": "<p>Represents a <i>greater than</i> condition to be applied to a single field when querying for scan entries.</p>", "locationName": "greaterThan"}, "LessThan": {"shape": "LongValue", "documentation": "<p>Represents a <i>less than</i> condition to be applied to a single field when querying for scan entries.</p>", "locationName": "lessThan"}}, "documentation": "<p>Contains information about the condition.</p>"}, "FilterCriteria": {"type": "structure", "members": {"FilterCriterion": {"shape": "FilterCriterionList", "documentation": "<p>Represents a condition that when matched will be added to the response of the operation.</p>", "locationName": "filterCriterion"}}, "documentation": "<p>Represents the criteria to be used in the filter for describing scan entries.</p>"}, "FilterCriterion": {"type": "structure", "members": {"CriterionKey": {"shape": "Criterion<PERSON>ey", "documentation": "<p>An enum value representing possible scan properties to match with given scan entries.</p>", "locationName": "<PERSON><PERSON><PERSON>"}, "FilterCondition": {"shape": "FilterCondition", "documentation": "<p>Contains information about the condition.</p>", "locationName": "filterCondition"}}, "documentation": "<p>Represents a condition that when matched will be added to the response of the operation. Irrespective of using any filter criteria, an administrator account can view the scan entries for all of its member accounts. However, each member account can view the scan entries only for their own account.</p>"}, "FilterCriterionList": {"type": "list", "member": {"shape": "FilterCriterion"}, "max": 1, "min": 0}, "FilterDescription": {"type": "string", "max": 512, "min": 0}, "FilterName": {"type": "string", "max": 64, "min": 3}, "FilterNames": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON><PERSON>"}, "max": 50, "min": 0}, "FilterRank": {"type": "integer", "max": 100, "min": 1}, "Finding": {"type": "structure", "required": ["AccountId", "<PERSON><PERSON>", "CreatedAt", "Id", "Region", "Resource", "SchemaVersion", "Severity", "Type", "UpdatedAt"], "members": {"AccountId": {"shape": "String", "documentation": "<p>The ID of the account in which the finding was generated.</p>", "locationName": "accountId"}, "Arn": {"shape": "String", "documentation": "<p>The ARN of the finding.</p>", "locationName": "arn"}, "Confidence": {"shape": "Double", "documentation": "<p>The confidence score for the finding.</p>", "locationName": "confidence"}, "CreatedAt": {"shape": "String", "documentation": "<p>The time and date when the finding was created.</p>", "locationName": "createdAt"}, "Description": {"shape": "String", "documentation": "<p>The description of the finding.</p>", "locationName": "description"}, "Id": {"shape": "String", "documentation": "<p>The ID of the finding.</p>", "locationName": "id"}, "Partition": {"shape": "String", "documentation": "<p>The partition associated with the finding.</p>", "locationName": "partition"}, "Region": {"shape": "String", "documentation": "<p>The Region where the finding was generated. For findings generated from <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/cloudtrail-concepts.html#cloudtrail-concepts-global-service-events\">Global Service Events</a>, the Region value in the finding might differ from the Region where GuardDuty identifies the potential threat. For more information, see <a href=\"https://docs.aws.amazon.com/guardduty/latest/ug/guardduty_data-sources.html#cloudtrail_global\">How GuardDuty handles Amazon Web Services CloudTrail global events</a> in the <i>Amazon GuardDuty User Guide</i>.</p>", "locationName": "region"}, "Resource": {"shape": "Resource", "locationName": "resource"}, "SchemaVersion": {"shape": "String", "documentation": "<p>The version of the schema used for the finding.</p>", "locationName": "schemaVersion"}, "Service": {"shape": "Service", "locationName": "service"}, "Severity": {"shape": "Double", "documentation": "<p>The severity of the finding.</p>", "locationName": "severity"}, "Title": {"shape": "String", "documentation": "<p>The title of the finding.</p>", "locationName": "title"}, "Type": {"shape": "FindingType", "documentation": "<p>The type of finding.</p>", "locationName": "type"}, "UpdatedAt": {"shape": "String", "documentation": "<p>The time and date when the finding was last updated.</p>", "locationName": "updatedAt"}, "AssociatedAttackSequenceArn": {"shape": "String", "documentation": "<p>Amazon Resource Name (ARN) associated with the attack sequence finding.</p>", "locationName": "associatedAttackSequenceArn"}}, "documentation": "<p>Contains information about the finding that is generated when abnormal or suspicious activity is detected.</p>"}, "FindingCriteria": {"type": "structure", "members": {"Criterion": {"shape": "Criterion", "documentation": "<p>Represents a map of finding properties that match specified conditions and values when querying findings.</p>", "locationName": "criterion"}}, "documentation": "<p>Contains information about the criteria used for querying findings.</p>"}, "FindingId": {"type": "string", "max": 300, "min": 1}, "FindingIds": {"type": "list", "member": {"shape": "FindingId"}, "max": 50, "min": 0}, "FindingPublishingFrequency": {"type": "string", "enum": ["FIFTEEN_MINUTES", "ONE_HOUR", "SIX_HOURS"]}, "FindingResourceType": {"type": "string", "enum": ["EC2_INSTANCE", "EC2_NETWORK_INTERFACE", "S3_BUCKET", "S3_OBJECT", "ACCESS_KEY", "EKS_CLUSTER", "KUBERNETES_WORKLOAD", "CONTAINER"]}, "FindingStatisticType": {"type": "string", "enum": ["COUNT_BY_SEVERITY"]}, "FindingStatisticTypes": {"type": "list", "member": {"shape": "FindingStatisticType"}, "max": 10, "min": 0}, "FindingStatistics": {"type": "structure", "members": {"CountBySeverity": {"shape": "Count<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>Represents a list of map of severity to count statistics for a set of findings.</p>", "deprecated": true, "deprecatedMessage": "This parameter is deprecated. Please set GroupBy to 'SEVERITY' to return GroupedBySeverity instead.", "locationName": "countBySeverity"}, "GroupedByAccount": {"shape": "GroupedByAccount", "documentation": "<p>Represents a list of map of accounts with a findings count associated with each account.</p>", "locationName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "GroupedByDate": {"shape": "GroupedByDate", "documentation": "<p>Represents a list of map of dates with a count of total findings generated on each date per severity level.</p>", "locationName": "groupedByDate"}, "GroupedByFindingType": {"shape": "GroupedByFindingType", "documentation": "<p>Represents a list of map of finding types with a count of total findings generated for each type. </p> <p>Based on the <code>orderBy</code> parameter, this request returns either the most occurring finding types or the least occurring finding types. If the <code>orderBy</code> parameter is <code>ASC</code>, this will represent the least occurring finding types in your account; otherwise, this will represent the most occurring finding types. The default value of <code>orderBy</code> is <code>DESC</code>.</p>", "locationName": "groupedByFindingType"}, "GroupedByResource": {"shape": "GroupedByResource", "documentation": "<p>Represents a list of map of top resources with a count of total findings.</p>", "locationName": "groupedByResource"}, "GroupedBySeverity": {"shape": "GroupedBySeverity", "documentation": "<p>Represents a list of map of total findings for each severity level.</p>", "locationName": "groupedBySeverity"}}, "documentation": "<p>Contains information about finding statistics.</p>"}, "FindingType": {"type": "string", "max": 50, "min": 1}, "FindingTypeStatistics": {"type": "structure", "members": {"FindingType": {"shape": "String", "documentation": "<p>Name of the finding type.</p>", "locationName": "findingType"}, "LastGeneratedAt": {"shape": "Timestamp", "documentation": "<p>The timestamp at which this finding type was last generated in your environment.</p>", "locationName": "lastGeneratedAt"}, "TotalFindings": {"shape": "Integer", "documentation": "<p>The total number of findings associated with generated for each distinct finding type.</p>", "locationName": "totalFindings"}}, "documentation": "<p>Information about each finding type associated with the <code>groupedByFindingType</code> statistics.</p>"}, "FindingTypes": {"type": "list", "member": {"shape": "FindingType"}, "max": 50, "min": 0}, "Findings": {"type": "list", "member": {"shape": "Finding"}, "max": 50, "min": 0}, "FlagsList": {"type": "list", "member": {"shape": "String"}}, "FlowLogsConfigurationResult": {"type": "structure", "required": ["Status"], "members": {"Status": {"shape": "DataSourceStatus", "documentation": "<p>Denotes whether VPC flow logs is enabled as a data source.</p>", "locationName": "status"}}, "documentation": "<p>Contains information on the status of VPC flow logs as a data source.</p>"}, "FreeTrialFeatureConfigurationResult": {"type": "structure", "members": {"Name": {"shape": "FreeTrialFeatureResult", "documentation": "<p>The name of the feature for which the free trial is configured.</p>", "locationName": "name"}, "FreeTrialDaysRemaining": {"shape": "Integer", "documentation": "<p>The number of the remaining free trial days for the feature.</p>", "locationName": "freeTrialDaysRemaining"}}, "documentation": "<p>Contains information about the free trial period for a feature.</p>"}, "FreeTrialFeatureConfigurationsResults": {"type": "list", "member": {"shape": "FreeTrialFeatureConfigurationResult"}}, "FreeTrialFeatureResult": {"type": "string", "enum": ["FLOW_LOGS", "CLOUD_TRAIL", "DNS_LOGS", "S3_DATA_EVENTS", "EKS_AUDIT_LOGS", "EBS_MALWARE_PROTECTION", "RDS_LOGIN_EVENTS", "EKS_RUNTIME_MONITORING", "LAMBDA_NETWORK_LOGS", "FARGATE_RUNTIME_MONITORING", "EC2_RUNTIME_MONITORING"]}, "GeoLocation": {"type": "structure", "members": {"Lat": {"shape": "Double", "documentation": "<p>The latitude information of the remote IP address.</p>", "locationName": "lat"}, "Lon": {"shape": "Double", "documentation": "<p>The longitude information of the remote IP address.</p>", "locationName": "lon"}}, "documentation": "<p>Contains information about the location of the remote IP address. By default, GuardDuty returns <code>Geolocation</code> with <code>Lat</code> and <code>Lon</code> as <code>0.0</code>.</p>"}, "GetAdministratorAccountRequest": {"type": "structure", "required": ["DetectorId"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The unique ID of the detector of the GuardDuty member account.</p>", "location": "uri", "locationName": "detectorId"}}}, "GetAdministratorAccountResponse": {"type": "structure", "required": ["Administrator"], "members": {"Administrator": {"shape": "Administrator", "documentation": "<p>The administrator account details.</p>", "locationName": "administrator"}}}, "GetCoverageStatisticsRequest": {"type": "structure", "required": ["DetectorId", "StatisticsType"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The unique ID of the GuardDuty detector.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}, "FilterCriteria": {"shape": "CoverageFilterCriteria", "documentation": "<p>Represents the criteria used to filter the coverage statistics.</p>", "locationName": "filterCriteria"}, "StatisticsType": {"shape": "CoverageStatisticsTypeList", "documentation": "<p>Represents the statistics type used to aggregate the coverage details.</p>", "locationName": "statisticsType"}}}, "GetCoverageStatisticsResponse": {"type": "structure", "members": {"CoverageStatistics": {"shape": "CoverageStatistics", "documentation": "<p>Represents the count aggregated by the <code>statusCode</code> and <code>resourceType</code>.</p>", "locationName": "coverageStatistics"}}}, "GetDetectorRequest": {"type": "structure", "required": ["DetectorId"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The unique ID of the detector that you want to get.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}}}, "GetDetectorResponse": {"type": "structure", "required": ["ServiceRole", "Status"], "members": {"CreatedAt": {"shape": "String", "documentation": "<p>The timestamp of when the detector was created.</p>", "locationName": "createdAt"}, "FindingPublishingFrequency": {"shape": "FindingPublishingFrequency", "documentation": "<p>The publishing frequency of the finding.</p>", "locationName": "findingPublishingFrequency"}, "ServiceRole": {"shape": "String", "documentation": "<p>The GuardDuty service role.</p>", "locationName": "serviceRole"}, "Status": {"shape": "DetectorStatus", "documentation": "<p>The detector status.</p>", "locationName": "status"}, "UpdatedAt": {"shape": "String", "documentation": "<p>The last-updated timestamp for the detector.</p>", "locationName": "updatedAt"}, "DataSources": {"shape": "DataSourceConfigurationsResult", "documentation": "<p>Describes which data sources are enabled for the detector.</p>", "deprecated": true, "deprecatedMessage": "This parameter is deprecated, use Features instead", "locationName": "dataSources"}, "Tags": {"shape": "TagMap", "documentation": "<p>The tags of the detector resource.</p>", "locationName": "tags"}, "Features": {"shape": "DetectorFeatureConfigurationsResults", "documentation": "<p>Describes the features that have been enabled for the detector.</p>", "locationName": "features"}}}, "GetFilterRequest": {"type": "structure", "required": ["DetectorId", "<PERSON><PERSON><PERSON><PERSON>"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The unique ID of the detector that is associated with this filter.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}, "FilterName": {"shape": "String", "documentation": "<p>The name of the filter you want to get.</p>", "location": "uri", "locationName": "filterName"}}}, "GetFilterResponse": {"type": "structure", "required": ["Name", "Action", "FindingCriteria"], "members": {"Name": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The name of the filter.</p>", "locationName": "name"}, "Description": {"shape": "FilterDescription", "documentation": "<p>The description of the filter.</p>", "locationName": "description"}, "Action": {"shape": "FilterAction", "documentation": "<p>Specifies the action that is to be applied to the findings that match the filter.</p>", "locationName": "action"}, "Rank": {"shape": "FilterRank", "documentation": "<p>Specifies the position of the filter in the list of current filters. Also specifies the order in which this filter is applied to the findings.</p>", "locationName": "rank"}, "FindingCriteria": {"shape": "FindingCriteria", "documentation": "<p>Represents the criteria to be used in the filter for querying findings.</p>", "locationName": "findingCriteria"}, "Tags": {"shape": "TagMap", "documentation": "<p>The tags of the filter resource.</p>", "locationName": "tags"}}}, "GetFindingsRequest": {"type": "structure", "required": ["DetectorId", "FindingIds"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The ID of the detector that specifies the GuardDuty service whose findings you want to retrieve.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}, "FindingIds": {"shape": "FindingIds", "documentation": "<p>The IDs of the findings that you want to retrieve.</p>", "locationName": "findingIds"}, "SortCriteria": {"shape": "SortCriteria", "documentation": "<p>Represents the criteria used for sorting findings.</p>", "locationName": "sortCriteria"}}}, "GetFindingsResponse": {"type": "structure", "required": ["Findings"], "members": {"Findings": {"shape": "Findings", "documentation": "<p>A list of findings.</p>", "locationName": "findings"}}}, "GetFindingsStatisticsRequest": {"type": "structure", "required": ["DetectorId"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The ID of the detector whose findings statistics you want to retrieve.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}, "FindingStatisticTypes": {"shape": "FindingStatisticTypes", "documentation": "<p>The types of finding statistics to retrieve.</p>", "deprecated": true, "deprecatedMessage": "This parameter is deprecated, please use GroupBy instead", "locationName": "findingStatisticTypes"}, "FindingCriteria": {"shape": "FindingCriteria", "documentation": "<p>Represents the criteria that is used for querying findings.</p>", "locationName": "findingCriteria"}, "GroupBy": {"shape": "GroupByType", "documentation": "<p>Displays the findings statistics grouped by one of the listed valid values.</p>", "locationName": "groupBy"}, "OrderBy": {"shape": "OrderBy", "documentation": "<p>Displays the sorted findings in the requested order. The default value of <code>orderBy</code> is <code>DESC</code>.</p> <p>You can use this parameter only with the <code>groupBy</code> parameter.</p>", "locationName": "orderBy"}, "MaxResults": {"shape": "MaxResults100", "documentation": "<p>The maximum number of results to be returned in the response. The default value is 25.</p> <p>You can use this parameter only with the <code>groupBy</code> parameter.</p>", "locationName": "maxResults"}}}, "GetFindingsStatisticsResponse": {"type": "structure", "required": ["FindingStatistics"], "members": {"FindingStatistics": {"shape": "FindingStatistics", "documentation": "<p>The finding statistics object.</p>", "locationName": "findingStatistics"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination parameter to be used on the next list operation to retrieve more items.</p> <p>This parameter is currently not supported.</p>", "locationName": "nextToken"}}}, "GetIPSetRequest": {"type": "structure", "required": ["DetectorId", "IpSetId"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The unique ID of the detector that is associated with the IPSet.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}, "IpSetId": {"shape": "String", "documentation": "<p>The unique ID of the IPSet to retrieve.</p>", "location": "uri", "locationName": "ipSetId"}}}, "GetIPSetResponse": {"type": "structure", "required": ["Name", "Format", "Location", "Status"], "members": {"Name": {"shape": "Name", "documentation": "<p>The user-friendly name for the IPSet.</p>", "locationName": "name"}, "Format": {"shape": "IpSetFormat", "documentation": "<p>The format of the file that contains the IPSet.</p>", "locationName": "format"}, "Location": {"shape": "Location", "documentation": "<p>The URI of the file that contains the IPSet.</p>", "locationName": "location"}, "Status": {"shape": "IpSetStatus", "documentation": "<p>The status of IPSet file that was uploaded.</p>", "locationName": "status"}, "Tags": {"shape": "TagMap", "documentation": "<p>The tags of the IPSet resource.</p>", "locationName": "tags"}}}, "GetInvitationsCountRequest": {"type": "structure", "members": {}}, "GetInvitationsCountResponse": {"type": "structure", "members": {"InvitationsCount": {"shape": "Integer", "documentation": "<p>The number of received invitations.</p>", "locationName": "invitationsCount"}}}, "GetMalwareProtectionPlanRequest": {"type": "structure", "required": ["MalwareProtectionPlanId"], "members": {"MalwareProtectionPlanId": {"shape": "String", "documentation": "<p>A unique identifier associated with Malware Protection plan resource.</p>", "location": "uri", "locationName": "malwareProtectionPlanId"}}}, "GetMalwareProtectionPlanResponse": {"type": "structure", "members": {"Arn": {"shape": "String", "documentation": "<p>Amazon Resource Name (ARN) of the protected resource.</p>", "locationName": "arn"}, "Role": {"shape": "String", "documentation": "<p>Amazon Resource Name (ARN) of the IAM role that includes the permissions to scan and add tags to the associated protected resource.</p>", "locationName": "role"}, "ProtectedResource": {"shape": "CreateProtectedResource", "documentation": "<p>Information about the protected resource that is associated with the created Malware Protection plan. Presently, <code>S3Bucket</code> is the only supported protected resource.</p>", "locationName": "protectedResource"}, "Actions": {"shape": "MalwareProtectionPlanActions", "documentation": "<p>Information about whether the tags will be added to the S3 object after scanning.</p>", "locationName": "actions"}, "CreatedAt": {"shape": "Timestamp", "documentation": "<p>The timestamp when the Malware Protection plan resource was created.</p>", "locationName": "createdAt"}, "Status": {"shape": "MalwareProtectionPlanStatus", "documentation": "<p>Malware Protection plan status.</p>", "locationName": "status"}, "StatusReasons": {"shape": "MalwareProtectionPlanStatusReasonsList", "documentation": "<p>Information about the issue code and message associated to the status of your Malware Protection plan.</p>", "locationName": "statusReasons"}, "Tags": {"shape": "TagMap", "documentation": "<p>Tags added to the Malware Protection plan resource.</p>", "locationName": "tags"}}}, "GetMalwareScanSettingsRequest": {"type": "structure", "required": ["DetectorId"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The unique ID of the detector that is associated with this scan.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}}}, "GetMalwareScanSettingsResponse": {"type": "structure", "members": {"ScanResourceCriteria": {"shape": "ScanResourceCriteria", "documentation": "<p>Represents the criteria to be used in the filter for scanning resources.</p>", "locationName": "scanResourceCriteria"}, "EbsSnapshotPreservation": {"shape": "EbsSnapshotPreservation", "documentation": "<p>An enum value representing possible snapshot preservation settings.</p>", "locationName": "ebsSnapshotPreservation"}}}, "GetMasterAccountRequest": {"type": "structure", "required": ["DetectorId"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The unique ID of the detector of the GuardDuty member account.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}}, "deprecated": true, "deprecatedMessage": "This input is deprecated, use GetAdministratorAccountRequest instead"}, "GetMasterAccountResponse": {"type": "structure", "required": ["Master"], "members": {"Master": {"shape": "Master", "documentation": "<p>The administrator account details.</p>", "locationName": "master"}}, "deprecated": true, "deprecatedMessage": "This output is deprecated, use GetAdministratorAccountResponse instead"}, "GetMemberDetectorsRequest": {"type": "structure", "required": ["DetectorId", "AccountIds"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The detector ID for the administrator account.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}, "AccountIds": {"shape": "AccountIds", "documentation": "<p>A list of member account IDs.</p>", "locationName": "accountIds"}}}, "GetMemberDetectorsResponse": {"type": "structure", "required": ["MemberDataSourceConfigurations", "UnprocessedAccounts"], "members": {"MemberDataSourceConfigurations": {"shape": "MemberDataSourceConfigurations", "documentation": "<p>An object that describes which data sources are enabled for a member account.</p>", "locationName": "members"}, "UnprocessedAccounts": {"shape": "UnprocessedAccounts", "documentation": "<p>A list of member account IDs that were unable to be processed along with an explanation for why they were not processed.</p>", "locationName": "unprocessedAccounts"}}}, "GetMembersRequest": {"type": "structure", "required": ["DetectorId", "AccountIds"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The unique ID of the detector of the GuardDuty account whose members you want to retrieve.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}, "AccountIds": {"shape": "AccountIds", "documentation": "<p>A list of account IDs of the GuardDuty member accounts that you want to describe.</p>", "locationName": "accountIds"}}}, "GetMembersResponse": {"type": "structure", "required": ["Members", "UnprocessedAccounts"], "members": {"Members": {"shape": "Members", "documentation": "<p>A list of members.</p>", "locationName": "members"}, "UnprocessedAccounts": {"shape": "UnprocessedAccounts", "documentation": "<p>A list of objects that contain the unprocessed account and a result string that explains why it was unprocessed.</p>", "locationName": "unprocessedAccounts"}}}, "GetOrganizationStatisticsResponse": {"type": "structure", "members": {"OrganizationDetails": {"shape": "OrganizationDetails", "documentation": "<p>Information about the statistics report for your organization.</p>", "locationName": "organizationDetails"}}}, "GetRemainingFreeTrialDaysRequest": {"type": "structure", "required": ["DetectorId"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The unique ID of the detector of the GuardDuty member account.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}, "AccountIds": {"shape": "AccountIds", "documentation": "<p>A list of account identifiers of the GuardDuty member account.</p>", "locationName": "accountIds"}}}, "GetRemainingFreeTrialDaysResponse": {"type": "structure", "members": {"Accounts": {"shape": "AccountFreeTrialInfos", "documentation": "<p>The member accounts which were included in a request and were processed successfully.</p>", "locationName": "accounts"}, "UnprocessedAccounts": {"shape": "UnprocessedAccounts", "documentation": "<p>The member account that was included in a request but for which the request could not be processed.</p>", "locationName": "unprocessedAccounts"}}}, "GetThreatIntelSetRequest": {"type": "structure", "required": ["DetectorId", "ThreatIntelSetId"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The unique ID of the detector that is associated with the threatIntelSet.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}, "ThreatIntelSetId": {"shape": "String", "documentation": "<p>The unique ID of the threatIntelSet that you want to get.</p>", "location": "uri", "locationName": "threatIntelSetId"}}}, "GetThreatIntelSetResponse": {"type": "structure", "required": ["Name", "Format", "Location", "Status"], "members": {"Name": {"shape": "Name", "documentation": "<p>A user-friendly ThreatIntelSet name displayed in all findings that are generated by activity that involves IP addresses included in this ThreatIntelSet.</p>", "locationName": "name"}, "Format": {"shape": "ThreatIntelSetFormat", "documentation": "<p>The format of the threatIntelSet.</p>", "locationName": "format"}, "Location": {"shape": "Location", "documentation": "<p>The URI of the file that contains the ThreatIntelSet. </p>", "locationName": "location"}, "Status": {"shape": "ThreatIntelSetStatus", "documentation": "<p>The status of threatIntelSet file uploaded.</p>", "locationName": "status"}, "Tags": {"shape": "TagMap", "documentation": "<p>The tags of the threat list resource.</p>", "locationName": "tags"}}}, "GetUsageStatisticsRequest": {"type": "structure", "required": ["DetectorId", "UsageStatisticType", "UsageCriteria"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The ID of the detector that specifies the GuardDuty service whose usage statistics you want to retrieve.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}, "UsageStatisticType": {"shape": "UsageStatisticType", "documentation": "<p>The type of usage statistics to retrieve.</p>", "locationName": "usageStatisticsType"}, "UsageCriteria": {"shape": "UsageCriteria", "documentation": "<p>Represents the criteria used for querying usage.</p>", "locationName": "usageCriteria"}, "Unit": {"shape": "String", "documentation": "<p>The currency unit you would like to view your usage statistics in. Current valid values are USD.</p>", "locationName": "unit"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in the response.</p>", "locationName": "maxResults"}, "NextToken": {"shape": "String", "documentation": "<p>A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request to a list action. For subsequent calls, use the NextToken value returned from the previous request to continue listing results after the first page.</p>", "locationName": "nextToken"}}}, "GetUsageStatisticsResponse": {"type": "structure", "members": {"UsageStatistics": {"shape": "UsageStatistics", "documentation": "<p>The usage statistics object. If a UsageStatisticType was provided, the objects representing other types will be null.</p>", "locationName": "usageStatistics"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination parameter to be used on the next list operation to retrieve more items.</p>", "locationName": "nextToken"}}}, "GroupByType": {"type": "string", "enum": ["ACCOUNT", "DATE", "FINDING_TYPE", "RESOURCE", "SEVERITY"]}, "GroupedByAccount": {"type": "list", "member": {"shape": "AccountStatistics"}}, "GroupedByDate": {"type": "list", "member": {"shape": "DateStatistics"}}, "GroupedByFindingType": {"type": "list", "member": {"shape": "FindingTypeStatistics"}}, "GroupedByResource": {"type": "list", "member": {"shape": "ResourceStatistics"}}, "GroupedBySeverity": {"type": "list", "member": {"shape": "SeverityStatistics"}}, "Groups": {"type": "list", "member": {"shape": "String"}}, "GuardDutyArn": {"type": "string", "pattern": "^arn:[A-Za-z_.-]{1,20}:guardduty:[A-Za-z0-9_/.-]{0,63}:\\d+:detector/[A-Za-z0-9_/.-]{32,264}$"}, "HighestSeverityThreatDetails": {"type": "structure", "members": {"Severity": {"shape": "String", "documentation": "<p>Severity level of the highest severity threat detected.</p>", "locationName": "severity"}, "ThreatName": {"shape": "String", "documentation": "<p>Threat name of the highest severity threat detected as part of the malware scan.</p>", "locationName": "threatName"}, "Count": {"shape": "Integer", "documentation": "<p>Total number of infected files with the highest severity threat detected.</p>", "locationName": "count"}}, "documentation": "<p>Contains details of the highest severity threat detected during scan and number of infected files.</p>"}, "HostPath": {"type": "structure", "members": {"Path": {"shape": "String", "documentation": "<p>Path of the file or directory on the host that the volume maps to.</p>", "locationName": "path"}}, "documentation": "<p>Represents a pre-existing file or directory on the host machine that the volume maps to.</p>"}, "IamInstanceProfile": {"type": "structure", "members": {"Arn": {"shape": "String", "documentation": "<p>The profile ARN of the EC2 instance.</p>", "locationName": "arn"}, "Id": {"shape": "String", "documentation": "<p>The profile ID of the EC2 instance.</p>", "locationName": "id"}}, "documentation": "<p>Contains information about the EC2 instance profile.</p>"}, "ImpersonatedUser": {"type": "structure", "members": {"Username": {"shape": "String", "documentation": "<p>Information about the <code>username</code> that was being impersonated.</p>", "locationName": "username"}, "Groups": {"shape": "Groups", "documentation": "<p>The <code>group</code> to which the user name belongs.</p>", "locationName": "groups"}}, "documentation": "<p>Contains information about the impersonated user.</p>"}, "Indicator": {"type": "structure", "required": ["Key"], "members": {"Key": {"shape": "IndicatorType", "documentation": "<p>Specific indicator keys observed in the attack sequence. For description of the valid values for key, see <a href=\"https://docs.aws.amazon.com/guardduty/latest/ug/guardduty_findings-summary.html#guardduty-extended-threat-detection-attack-sequence-finding-details\">Attack sequence finding details</a> in the <i>Amazon GuardDuty User Guide</i>.</p>", "locationName": "key"}, "Values": {"shape": "Indicator<PERSON><PERSON><PERSON>", "documentation": "<p>Values associated with each indicator key. For example, if the indicator key is <code>SUSPICIOUS_NETWORK</code>, then the value will be the name of the network. If the indicator key is <code>ATTACK_TACTIC</code>, then the value will be one of the MITRE tactics. </p>", "locationName": "values"}, "Title": {"shape": "IndicatorTitle", "documentation": "<p>Title describing the indicator.</p>", "locationName": "title"}}, "documentation": "<p>Contains information about the indicators that include a set of signals observed in an attack sequence.</p>"}, "IndicatorTitle": {"type": "string", "max": 256, "min": 1}, "IndicatorType": {"type": "string", "enum": ["SUSPICIOUS_USER_AGENT", "SUSPICIOUS_NETWORK", "MALICIOUS_IP", "TOR_IP", "ATTACK_TACTIC", "HIGH_RISK_API", "ATTACK_TECHNIQUE", "UNUSUAL_API_FOR_ACCOUNT", "UNUSUAL_ASN_FOR_ACCOUNT", "UNUSUAL_ASN_FOR_USER", "SUSPICIOUS_PROCESS", "MALICIOUS_DOMAIN", "MALICIOUS_PROCESS", "CRYPTOMINING_IP", "CRYPTOMINING_DOMAIN", "CRYPTOMINING_PROCESS"]}, "IndicatorValueString": {"type": "string", "max": 256, "min": 1}, "IndicatorValues": {"type": "list", "member": {"shape": "IndicatorValueString"}, "max": 400, "min": 1}, "Indicators": {"type": "list", "member": {"shape": "Indicator"}, "max": 400}, "InstanceArn": {"type": "string", "pattern": "^arn:(aws|aws-cn|aws-us-gov):[a-z]+:[a-z]+(-[0-9]+|-[a-z]+)+:([0-9]{12}):[a-z\\-]+\\/[a-zA-Z0-9]*$"}, "InstanceDetails": {"type": "structure", "members": {"AvailabilityZone": {"shape": "String", "documentation": "<p>The Availability Zone of the EC2 instance.</p>", "locationName": "availabilityZone"}, "IamInstanceProfile": {"shape": "IamInstanceProfile", "documentation": "<p>The profile information of the EC2 instance.</p>", "locationName": "iamInstanceProfile"}, "ImageDescription": {"shape": "String", "documentation": "<p>The image description of the EC2 instance.</p>", "locationName": "imageDescription"}, "ImageId": {"shape": "String", "documentation": "<p>The image ID of the EC2 instance.</p>", "locationName": "imageId"}, "InstanceId": {"shape": "String", "documentation": "<p>The ID of the EC2 instance.</p>", "locationName": "instanceId"}, "InstanceState": {"shape": "String", "documentation": "<p>The state of the EC2 instance.</p>", "locationName": "instanceState"}, "InstanceType": {"shape": "String", "documentation": "<p>The type of the EC2 instance.</p>", "locationName": "instanceType"}, "OutpostArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the Amazon Web Services Outpost. Only applicable to Amazon Web Services Outposts instances.</p>", "locationName": "outpostArn"}, "LaunchTime": {"shape": "String", "documentation": "<p>The launch time of the EC2 instance.</p>", "locationName": "launchTime"}, "NetworkInterfaces": {"shape": "NetworkInterfaces", "documentation": "<p>The elastic network interface information of the EC2 instance.</p>", "locationName": "networkInterfaces"}, "Platform": {"shape": "String", "documentation": "<p>The platform of the EC2 instance.</p>", "locationName": "platform"}, "ProductCodes": {"shape": "ProductCodes", "documentation": "<p>The product code of the EC2 instance.</p>", "locationName": "productCodes"}, "Tags": {"shape": "Tags", "documentation": "<p>The tags of the EC2 instance.</p>", "locationName": "tags"}}, "documentation": "<p>Contains information about the details of an instance.</p>"}, "Integer": {"type": "integer"}, "IntegerValueWithMax": {"type": "integer", "max": 50, "min": 1}, "InternalServerErrorException": {"type": "structure", "members": {"Message": {"shape": "String", "documentation": "<p>The error message.</p>", "locationName": "message"}, "Type": {"shape": "String", "documentation": "<p>The error type.</p>", "locationName": "__type"}}, "documentation": "<p>An internal server error exception object.</p>", "error": {"httpStatusCode": 500}, "exception": true}, "Invitation": {"type": "structure", "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The ID of the account that the invitation was sent from.</p>", "locationName": "accountId"}, "InvitationId": {"shape": "String", "documentation": "<p>The ID of the invitation. This value is used to validate the inviter account to the member account.</p>", "locationName": "invitationId"}, "RelationshipStatus": {"shape": "String", "documentation": "<p>The status of the relationship between the inviter and invitee accounts.</p>", "locationName": "relationshipStatus"}, "InvitedAt": {"shape": "String", "documentation": "<p>The timestamp when the invitation was sent.</p>", "locationName": "invitedAt"}}, "documentation": "<p>Contains information about the invitation to become a member account.</p>"}, "Invitations": {"type": "list", "member": {"shape": "Invitation"}, "max": 50, "min": 0}, "InviteMembersRequest": {"type": "structure", "required": ["DetectorId", "AccountIds"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The unique ID of the detector of the GuardDuty account with which you want to invite members.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}, "AccountIds": {"shape": "AccountIds", "documentation": "<p>A list of account IDs of the accounts that you want to invite to GuardDuty as members.</p>", "locationName": "accountIds"}, "DisableEmailNotification": {"shape": "Boolean", "documentation": "<p>A Boolean value that specifies whether you want to disable email notification to the accounts that you are inviting to GuardDuty as members.</p>", "locationName": "disableEmailNotification"}, "Message": {"shape": "String", "documentation": "<p>The invitation message that you want to send to the accounts that you're inviting to GuardDuty as members.</p>", "locationName": "message"}}}, "InviteMembersResponse": {"type": "structure", "required": ["UnprocessedAccounts"], "members": {"UnprocessedAccounts": {"shape": "UnprocessedAccounts", "documentation": "<p>A list of objects that contain the unprocessed account and a result string that explains why it was unprocessed.</p>", "locationName": "unprocessedAccounts"}}}, "IpSetFormat": {"type": "string", "enum": ["TXT", "STIX", "OTX_CSV", "ALIEN_VAULT", "PROOF_POINT", "FIRE_EYE"], "max": 300, "min": 1}, "IpSetIds": {"type": "list", "member": {"shape": "String"}, "max": 50, "min": 0}, "IpSetStatus": {"type": "string", "enum": ["INACTIVE", "ACTIVATING", "ACTIVE", "DEACTIVATING", "ERROR", "DELETE_PENDING", "DELETED"], "max": 300, "min": 1}, "Ipv6Addresses": {"type": "list", "member": {"shape": "String"}}, "Issues": {"type": "list", "member": {"shape": "String"}, "max": 50, "min": 0}, "ItemPath": {"type": "structure", "members": {"NestedItemPath": {"shape": "String", "documentation": "<p>The nested item path where the infected file was found.</p>", "locationName": "nestedItemPath"}, "Hash": {"shape": "String", "documentation": "<p>The hash value of the infected resource.</p>", "locationName": "hash"}}, "documentation": "<p>Information about the nested item path and hash of the protected resource.</p>"}, "ItemPaths": {"type": "list", "member": {"shape": "ItemPath"}}, "KubernetesApiCallAction": {"type": "structure", "members": {"RequestUri": {"shape": "String", "documentation": "<p>The Kubernetes API request URI.</p>", "locationName": "requestUri"}, "Verb": {"shape": "String", "documentation": "<p>The Kubernetes API request HTTP verb.</p>", "locationName": "verb"}, "SourceIps": {"shape": "SourceIps", "documentation": "<p>The IP of the Kubernetes API caller and the IPs of any proxies or load balancers between the caller and the API endpoint.</p>", "locationName": "sourceIPs"}, "UserAgent": {"shape": "String", "documentation": "<p>The user agent of the caller of the Kubernetes API.</p>", "locationName": "userAgent"}, "RemoteIpDetails": {"shape": "RemoteIpDetails", "locationName": "remoteIpDetails"}, "StatusCode": {"shape": "Integer", "documentation": "<p>The resulting HTTP response code of the Kubernetes API call action.</p>", "locationName": "statusCode"}, "Parameters": {"shape": "String", "documentation": "<p>Parameters related to the Kubernetes API call action.</p>", "locationName": "parameters"}, "Resource": {"shape": "String", "documentation": "<p>The resource component in the Kubernetes API call action.</p>", "locationName": "resource"}, "Subresource": {"shape": "String", "documentation": "<p>The name of the sub-resource in the Kubernetes API call action.</p>", "locationName": "subresource"}, "Namespace": {"shape": "String", "documentation": "<p>The name of the namespace where the Kubernetes API call action takes place.</p>", "locationName": "namespace"}, "ResourceName": {"shape": "String", "documentation": "<p>The name of the resource in the Kubernetes API call action.</p>", "locationName": "resourceName"}}, "documentation": "<p>Information about the Kubernetes API call action described in this finding.</p>"}, "KubernetesAuditLogsConfiguration": {"type": "structure", "required": ["Enable"], "members": {"Enable": {"shape": "Boolean", "documentation": "<p>The status of Kubernetes audit logs as a data source.</p>", "locationName": "enable"}}, "documentation": "<p>Describes whether Kubernetes audit logs are enabled as a data source.</p>"}, "KubernetesAuditLogsConfigurationResult": {"type": "structure", "required": ["Status"], "members": {"Status": {"shape": "DataSourceStatus", "documentation": "<p>A value that describes whether Kubernetes audit logs are enabled as a data source.</p>", "locationName": "status"}}, "documentation": "<p>Describes whether Kubernetes audit logs are enabled as a data source.</p>"}, "KubernetesConfiguration": {"type": "structure", "required": ["AuditLogs"], "members": {"AuditLogs": {"shape": "KubernetesAuditLogsConfiguration", "documentation": "<p>The status of Kubernetes audit logs as a data source.</p>", "locationName": "auditLogs"}}, "documentation": "<p>Describes whether any Kubernetes data sources are enabled.</p>"}, "KubernetesConfigurationResult": {"type": "structure", "required": ["AuditLogs"], "members": {"AuditLogs": {"shape": "KubernetesAuditLogsConfigurationResult", "documentation": "<p>Describes whether Kubernetes audit logs are enabled as a data source.</p>", "locationName": "auditLogs"}}, "documentation": "<p>Describes whether any Kubernetes logs will be enabled as a data source.</p>"}, "KubernetesDataSourceFreeTrial": {"type": "structure", "members": {"AuditLogs": {"shape": "DataSourceFreeTrial", "documentation": "<p>Describes whether Kubernetes audit logs are enabled as a data source.</p>", "locationName": "auditLogs"}}, "documentation": "<p>Provides details about the Kubernetes resources when it is enabled as a data source.</p>"}, "KubernetesDetails": {"type": "structure", "members": {"KubernetesUserDetails": {"shape": "KubernetesUserDetails", "documentation": "<p>Details about the Kubernetes user involved in a Kubernetes finding.</p>", "locationName": "kubernetesUserDetails"}, "KubernetesWorkloadDetails": {"shape": "KubernetesWorkloadDetails", "documentation": "<p>Details about the Kubernetes workload involved in a Kubernetes finding.</p>", "locationName": "kubernetesWorkloadDetails"}}, "documentation": "<p>Details about Kubernetes resources such as a Kubernetes user or workload resource involved in a Kubernetes finding.</p>"}, "KubernetesPermissionCheckedDetails": {"type": "structure", "members": {"Verb": {"shape": "String", "documentation": "<p>The verb component of the Kubernetes API call. For example, when you check whether or not you have the permission to call the <code>CreatePod</code> API, the verb component will be <code>Create</code>.</p>", "locationName": "verb"}, "Resource": {"shape": "String", "documentation": "<p>The Kubernetes resource with which your Kubernetes API call will interact.</p>", "locationName": "resource"}, "Namespace": {"shape": "String", "documentation": "<p>The namespace where the Kubernetes API action will take place.</p>", "locationName": "namespace"}, "Allowed": {"shape": "Boolean", "documentation": "<p>Information whether the user has the permission to call the Kubernetes API.</p>", "locationName": "allowed"}}, "documentation": "<p>Information about the Kubernetes API for which you check if you have permission to call.</p>"}, "KubernetesResourcesTypes": {"type": "string", "enum": ["PODS", "JOBS", "CRONJOBS", "DEPLOYMENTS", "DAEMONSETS", "STATEFULSETS", "REPLICASETS", "REPLICATIONCONTROLLERS"]}, "KubernetesRoleBindingDetails": {"type": "structure", "members": {"Kind": {"shape": "String", "documentation": "<p>The kind of the role. For role binding, this value will be <code>RoleBinding</code>.</p>", "locationName": "kind"}, "Name": {"shape": "String", "documentation": "<p>The name of the <code>RoleBinding</code>.</p>", "locationName": "name"}, "Uid": {"shape": "String", "documentation": "<p>The unique identifier of the role binding.</p>", "locationName": "uid"}, "RoleRefName": {"shape": "String", "documentation": "<p>The name of the role being referenced. This must match the name of the <code>Role</code> or <code>ClusterRole</code> that you want to bind to.</p>", "locationName": "roleRefName"}, "RoleRefKind": {"shape": "String", "documentation": "<p>The type of the role being referenced. This could be either <code>Role</code> or <code>ClusterRole</code>.</p>", "locationName": "roleRefKind"}}, "documentation": "<p>Contains information about the role binding that grants the permission defined in a Kubernetes role.</p>"}, "KubernetesRoleDetails": {"type": "structure", "members": {"Kind": {"shape": "String", "documentation": "<p>The kind of role. For this API, the value of <code>kind</code> will be <code>Role</code>.</p>", "locationName": "kind"}, "Name": {"shape": "String", "documentation": "<p>The name of the <PERSON><PERSON><PERSON><PERSON> role.</p>", "locationName": "name"}, "Uid": {"shape": "String", "documentation": "<p>The unique identifier of the Kubernetes role name.</p>", "locationName": "uid"}}, "documentation": "<p>Information about the Kubernetes role name and role type.</p>"}, "KubernetesUserDetails": {"type": "structure", "members": {"Username": {"shape": "String", "documentation": "<p>The username of the user who called the Kubernetes API.</p>", "locationName": "username"}, "Uid": {"shape": "String", "documentation": "<p>The user ID of the user who called the Kubernetes API.</p>", "locationName": "uid"}, "Groups": {"shape": "Groups", "documentation": "<p>The groups that include the user who called the Kubernetes API.</p>", "locationName": "groups"}, "SessionName": {"shape": "SessionNameList", "documentation": "<p>Entity that assumes the IAM role when Kubernetes RBAC permissions are assigned to that role.</p>", "locationName": "<PERSON><PERSON><PERSON>"}, "ImpersonatedUser": {"shape": "ImpersonatedUser", "documentation": "<p>Information about the impersonated user.</p>", "locationName": "impersonated<PERSON><PERSON>"}}, "documentation": "<p>Details about the Kubernetes user involved in a Kubernetes finding.</p>"}, "KubernetesWorkload": {"type": "structure", "members": {"ContainerUids": {"shape": "ContainerUids", "documentation": "<p>A list of unique identifiers for the containers that are part of the Kubernetes workload.</p>", "locationName": "containerUids"}, "Namespace": {"shape": "String", "documentation": "<p>The Kubernetes namespace in which the workload is running, providing logical isolation within the cluster.</p>", "locationName": "namespace"}, "KubernetesResourcesTypes": {"shape": "KubernetesResourcesTypes", "documentation": "<p>The types of Kubernetes resources involved in the workload.</p>", "locationName": "type"}}, "documentation": "<p>Contains information about Kubernetes workloads involved in a GuardDuty finding, including pods, deployments, and other Kubernetes resources.</p>"}, "KubernetesWorkloadDetails": {"type": "structure", "members": {"Name": {"shape": "String", "documentation": "<p>Kubernetes workload name.</p>", "locationName": "name"}, "Type": {"shape": "String", "documentation": "<p>Kubernetes workload type (e.g. Pod, Deployment, etc.).</p>", "locationName": "type"}, "Uid": {"shape": "String", "documentation": "<p>Kubernetes workload ID.</p>", "locationName": "uid"}, "Namespace": {"shape": "String", "documentation": "<p>Kubernetes namespace that the workload is part of.</p>", "locationName": "namespace"}, "HostNetwork": {"shape": "Boolean", "documentation": "<p>Whether the hostNetwork flag is enabled for the pods included in the workload.</p>", "locationName": "hostNetwork"}, "Containers": {"shape": "Containers", "documentation": "<p>Containers running as part of the Kubernetes workload.</p>", "locationName": "containers"}, "Volumes": {"shape": "Volumes", "documentation": "<p>Volumes used by the Kubernetes workload.</p>", "locationName": "volumes"}, "ServiceAccountName": {"shape": "String", "documentation": "<p>The service account name that is associated with a Kubernetes workload.</p>", "locationName": "serviceAccountName"}, "HostIPC": {"shape": "Boolean", "documentation": "<p>Whether the host IPC flag is enabled for the pods in the workload.</p>", "locationName": "hostIPC"}, "HostPID": {"shape": "Boolean", "documentation": "<p>Whether the host PID flag is enabled for the pods in the workload. </p>", "locationName": "hostPID"}}, "documentation": "<p>Details about the Kubernetes workload involved in a Kubernetes finding.</p>"}, "LambdaDetails": {"type": "structure", "members": {"FunctionArn": {"shape": "String", "documentation": "<p>Amazon Resource Name (ARN) of the Lambda function.</p>", "locationName": "functionArn"}, "FunctionName": {"shape": "String", "documentation": "<p>Name of the Lambda function.</p>", "locationName": "functionName"}, "Description": {"shape": "String", "documentation": "<p>Description of the Lambda function.</p>", "locationName": "description"}, "LastModifiedAt": {"shape": "Timestamp", "documentation": "<p>The timestamp when the Lambda function was last modified. This field is in the UTC date string format <code>(2023-03-22T19:37:20.168Z)</code>.</p>", "locationName": "lastModifiedAt"}, "RevisionId": {"shape": "String", "documentation": "<p>The revision ID of the Lambda function version.</p>", "locationName": "revisionId"}, "FunctionVersion": {"shape": "String", "documentation": "<p>The version of the Lambda function.</p>", "locationName": "functionVersion"}, "Role": {"shape": "String", "documentation": "<p>The execution role of the Lambda function.</p>", "locationName": "role"}, "VpcConfig": {"shape": "VpcConfig", "documentation": "<p>Amazon Virtual Private Cloud configuration details associated with your Lambda function.</p>", "locationName": "vpcConfig"}, "Tags": {"shape": "Tags", "documentation": "<p>A list of tags attached to this resource, listed in the format of <code>key</code>:<code>value</code> pair.</p>", "locationName": "tags"}}, "documentation": "<p>Information about the Lambda function involved in the finding.</p>"}, "Lineage": {"type": "list", "member": {"shape": "LineageObject"}}, "LineageObject": {"type": "structure", "members": {"StartTime": {"shape": "Timestamp", "documentation": "<p>The time when the process started. This is in UTC format.</p>", "locationName": "startTime"}, "NamespacePid": {"shape": "Integer", "documentation": "<p>The process ID of the child process.</p>", "locationName": "namespacePid"}, "UserId": {"shape": "Integer", "documentation": "<p>The user ID of the user that executed the process.</p>", "locationName": "userId"}, "Name": {"shape": "String", "documentation": "<p>The name of the process.</p>", "locationName": "name"}, "Pid": {"shape": "Integer", "documentation": "<p>The ID of the process.</p>", "locationName": "pid"}, "Uuid": {"shape": "String", "documentation": "<p>The unique ID assigned to the process by GuardDuty.</p>", "locationName": "uuid"}, "ExecutablePath": {"shape": "String", "documentation": "<p>The absolute path of the process executable file.</p>", "locationName": "executablePath"}, "Euid": {"shape": "Integer", "documentation": "<p>The effective user ID that was used to execute the process.</p>", "locationName": "euid"}, "ParentUuid": {"shape": "String", "documentation": "<p>The unique ID of the parent process. This ID is assigned to the parent process by GuardDuty.</p>", "locationName": "parentUuid"}}, "documentation": "<p>Information about the runtime process details.</p>"}, "ListCoverageRequest": {"type": "structure", "required": ["DetectorId"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The unique ID of the detector whose coverage details you want to retrieve.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}, "NextToken": {"shape": "String", "documentation": "<p>A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request to a list action. For subsequent calls, use the NextToken value returned from the previous request to continue listing results after the first page.</p>", "locationName": "nextToken"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in the response.</p>", "locationName": "maxResults"}, "FilterCriteria": {"shape": "CoverageFilterCriteria", "documentation": "<p>Represents the criteria used to filter the coverage details.</p>", "locationName": "filterCriteria"}, "SortCriteria": {"shape": "CoverageSortCriteria", "documentation": "<p>Represents the criteria used to sort the coverage details.</p>", "locationName": "sortCriteria"}}}, "ListCoverageResponse": {"type": "structure", "required": ["Resources"], "members": {"Resources": {"shape": "CoverageResources", "documentation": "<p>A list of resources and their attributes providing cluster details.</p>", "locationName": "resources"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination parameter to be used on the next list operation to retrieve more items.</p>", "locationName": "nextToken"}}}, "ListDetectorsRequest": {"type": "structure", "members": {"MaxResults": {"shape": "MaxResults", "documentation": "<p>You can use this parameter to indicate the maximum number of items that you want in the response. The default value is 50. The maximum value is 50.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "String", "documentation": "<p>You can use this parameter when paginating results. Set the value of this parameter to null on your first call to the list action. For subsequent calls to the action, fill nextToken in the request with the value of NextToken from the previous response to continue listing data.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListDetectorsResponse": {"type": "structure", "required": ["DetectorIds"], "members": {"DetectorIds": {"shape": "DetectorIds", "documentation": "<p>A list of detector IDs.</p>", "locationName": "detectorIds"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination parameter to be used on the next list operation to retrieve more items.</p>", "locationName": "nextToken"}}}, "ListFiltersRequest": {"type": "structure", "required": ["DetectorId"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The unique ID of the detector that is associated with the filter.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>You can use this parameter to indicate the maximum number of items that you want in the response. The default value is 50. The maximum value is 50.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "String", "documentation": "<p>You can use this parameter when paginating results. Set the value of this parameter to null on your first call to the list action. For subsequent calls to the action, fill nextToken in the request with the value of NextToken from the previous response to continue listing data.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListFiltersResponse": {"type": "structure", "required": ["FilterNames"], "members": {"FilterNames": {"shape": "FilterNames", "documentation": "<p>A list of filter names.</p>", "locationName": "filterNames"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination parameter to be used on the next list operation to retrieve more items.</p>", "locationName": "nextToken"}}}, "ListFindingsRequest": {"type": "structure", "required": ["DetectorId"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The ID of the detector that specifies the GuardDuty service whose findings you want to list.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}, "FindingCriteria": {"shape": "FindingCriteria", "documentation": "<p>Represents the criteria used for querying findings. Valid values include:</p> <ul> <li> <p>JSON field name</p> </li> <li> <p>accountId</p> </li> <li> <p>region</p> </li> <li> <p>confidence</p> </li> <li> <p>id</p> </li> <li> <p>resource.accessKeyDetails.accessKeyId</p> </li> <li> <p>resource.accessKeyDetails.principalId</p> </li> <li> <p>resource.accessKeyDetails.userName</p> </li> <li> <p>resource.accessKeyDetails.userType</p> </li> <li> <p>resource.instanceDetails.iamInstanceProfile.id</p> </li> <li> <p>resource.instanceDetails.imageId</p> </li> <li> <p>resource.instanceDetails.instanceId</p> </li> <li> <p>resource.instanceDetails.networkInterfaces.ipv6Addresses</p> </li> <li> <p>resource.instanceDetails.networkInterfaces.privateIpAddresses.privateIpAddress</p> </li> <li> <p>resource.instanceDetails.networkInterfaces.publicDnsName</p> </li> <li> <p>resource.instanceDetails.networkInterfaces.publicIp</p> </li> <li> <p>resource.instanceDetails.networkInterfaces.securityGroups.groupId</p> </li> <li> <p>resource.instanceDetails.networkInterfaces.securityGroups.groupName</p> </li> <li> <p>resource.instanceDetails.networkInterfaces.subnetId</p> </li> <li> <p>resource.instanceDetails.networkInterfaces.vpcId</p> </li> <li> <p>resource.instanceDetails.tags.key</p> </li> <li> <p>resource.instanceDetails.tags.value</p> </li> <li> <p>resource.resourceType</p> </li> <li> <p>service.action.actionType</p> </li> <li> <p>service.action.awsApiCallAction.api</p> </li> <li> <p>service.action.awsApiCallAction.callerType</p> </li> <li> <p>service.action.awsApiCallAction.remoteIpDetails.city.cityName</p> </li> <li> <p>service.action.awsApiCallAction.remoteIpDetails.country.countryName</p> </li> <li> <p>service.action.awsApiCallAction.remoteIpDetails.ipAddressV4</p> </li> <li> <p>service.action.awsApiCallAction.remoteIpDetails.organization.asn</p> </li> <li> <p>service.action.awsApiCallAction.remoteIpDetails.organization.asnOrg</p> </li> <li> <p>service.action.awsApiCallAction.serviceName</p> </li> <li> <p>service.action.dnsRequestAction.domain</p> </li> <li> <p>service.action.dnsRequestAction.domainWithSuffix</p> </li> <li> <p>service.action.networkConnectionAction.blocked</p> </li> <li> <p>service.action.networkConnectionAction.connectionDirection</p> </li> <li> <p>service.action.networkConnectionAction.localPortDetails.port</p> </li> <li> <p>service.action.networkConnectionAction.protocol</p> </li> <li> <p>service.action.networkConnectionAction.remoteIpDetails.country.countryName</p> </li> <li> <p>service.action.networkConnectionAction.remoteIpDetails.ipAddressV4</p> </li> <li> <p>service.action.networkConnectionAction.remoteIpDetails.organization.asn</p> </li> <li> <p>service.action.networkConnectionAction.remoteIpDetails.organization.asnOrg</p> </li> <li> <p>service.action.networkConnectionAction.remotePortDetails.port</p> </li> <li> <p>service.additionalInfo.threatListName</p> </li> <li> <p>service.archived</p> <p>When this attribute is set to 'true', only archived findings are listed. When it's set to 'false', only unarchived findings are listed. When this attribute is not set, all existing findings are listed.</p> </li> <li> <p>service.ebsVolumeScanDetails.scanId</p> </li> <li> <p>service.resourceRole</p> </li> <li> <p>severity</p> </li> <li> <p>type</p> </li> <li> <p>updatedAt</p> <p>Type: Timestamp in Unix Epoch millisecond format: 1486685375000</p> </li> </ul>", "locationName": "findingCriteria"}, "SortCriteria": {"shape": "SortCriteria", "documentation": "<p>Represents the criteria used for sorting findings.</p>", "locationName": "sortCriteria"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>You can use this parameter to indicate the maximum number of items you want in the response. The default value is 50. The maximum value is 50.</p>", "locationName": "maxResults"}, "NextToken": {"shape": "String", "documentation": "<p>You can use this parameter when paginating results. Set the value of this parameter to null on your first call to the list action. For subsequent calls to the action, fill nextToken in the request with the value of NextToken from the previous response to continue listing data.</p>", "locationName": "nextToken"}}}, "ListFindingsResponse": {"type": "structure", "required": ["FindingIds"], "members": {"FindingIds": {"shape": "FindingIds", "documentation": "<p>The IDs of the findings that you're listing.</p>", "locationName": "findingIds"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination parameter to be used on the next list operation to retrieve more items.</p>", "locationName": "nextToken"}}}, "ListIPSetsRequest": {"type": "structure", "required": ["DetectorId"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The unique ID of the detector that is associated with IPSet.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>You can use this parameter to indicate the maximum number of items you want in the response. The default value is 50. The maximum value is 50.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "String", "documentation": "<p>You can use this parameter when paginating results. Set the value of this parameter to null on your first call to the list action. For subsequent calls to the action, fill nextToken in the request with the value of NextToken from the previous response to continue listing data.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListIPSetsResponse": {"type": "structure", "required": ["IpSetIds"], "members": {"IpSetIds": {"shape": "IpSetIds", "documentation": "<p>The IDs of the IPSet resources.</p>", "locationName": "ipSetIds"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination parameter to be used on the next list operation to retrieve more items.</p>", "locationName": "nextToken"}}}, "ListInvitationsRequest": {"type": "structure", "members": {"MaxResults": {"shape": "MaxResults", "documentation": "<p>You can use this parameter to indicate the maximum number of items that you want in the response. The default value is 50. The maximum value is 50.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "String", "documentation": "<p>You can use this parameter when paginating results. Set the value of this parameter to null on your first call to the list action. For subsequent calls to the action, fill nextToken in the request with the value of NextToken from the previous response to continue listing data.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListInvitationsResponse": {"type": "structure", "members": {"Invitations": {"shape": "Invitations", "documentation": "<p>A list of invitation descriptions.</p>", "locationName": "invitations"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination parameter to be used on the next list operation to retrieve more items.</p>", "locationName": "nextToken"}}}, "ListMalwareProtectionPlansRequest": {"type": "structure", "members": {"NextToken": {"shape": "String", "documentation": "<p>You can use this parameter when paginating results. Set the value of this parameter to null on your first call to the list action. For subsequent calls to the action, fill nextToken in the request with the value of <code>NextToken</code> from the previous response to continue listing data.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListMalwareProtectionPlansResponse": {"type": "structure", "members": {"MalwareProtectionPlans": {"shape": "MalwareProtectionPlansSummary", "documentation": "<p>A list of unique identifiers associated with each Malware Protection plan.</p>", "locationName": "malwareProtectionPlans"}, "NextToken": {"shape": "String", "documentation": "<p>You can use this parameter when paginating results. Set the value of this parameter to null on your first call to the list action. For subsequent calls to the action, fill nextToken in the request with the value of <code>NextToken</code> from the previous response to continue listing data.</p>", "locationName": "nextToken"}}}, "ListMembersRequest": {"type": "structure", "required": ["DetectorId"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The unique ID of the detector that is associated with the member.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>You can use this parameter to indicate the maximum number of items you want in the response. The default value is 50. The maximum value is 50.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "String", "documentation": "<p>You can use this parameter when paginating results. Set the value of this parameter to null on your first call to the list action. For subsequent calls to the action, fill nextToken in the request with the value of NextToken from the previous response to continue listing data.</p>", "location": "querystring", "locationName": "nextToken"}, "OnlyAssociated": {"shape": "String", "documentation": "<p>Specifies whether to only return associated members or to return all members (including members who haven't been invited yet or have been disassociated). Member accounts must have been previously associated with the GuardDuty administrator account using <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_CreateMembers.html\"> <code>Create Members</code> </a>. </p>", "location": "querystring", "locationName": "onlyAssociated"}}}, "ListMembersResponse": {"type": "structure", "members": {"Members": {"shape": "Members", "documentation": "<p>A list of members.</p> <note> <p>The values for <code>email</code> and <code>invitedAt</code> are available only if the member accounts are added by invitation.</p> </note>", "locationName": "members"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination parameter to be used on the next list operation to retrieve more items.</p>", "locationName": "nextToken"}}}, "ListOrganizationAdminAccountsRequest": {"type": "structure", "members": {"MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in the response.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "String", "documentation": "<p>A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request to a list action. For subsequent calls, use the <code>NextToken</code> value returned from the previous request to continue listing results after the first page.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListOrganizationAdminAccountsResponse": {"type": "structure", "members": {"AdminAccounts": {"shape": "AdminAccounts", "documentation": "<p>A list of accounts configured as GuardDuty delegated administrators.</p>", "locationName": "adminAccounts"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination parameter to be used on the next list operation to retrieve more items.</p>", "locationName": "nextToken"}}}, "ListPublishingDestinationsRequest": {"type": "structure", "required": ["DetectorId"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The detector ID for which you want to retrieve the publishing destination.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in the response.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "String", "documentation": "<p>A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request to a list action. For subsequent calls, use the <code>NextToken</code> value returned from the previous request to continue listing results after the first page.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListPublishingDestinationsResponse": {"type": "structure", "required": ["Destinations"], "members": {"Destinations": {"shape": "Destinations", "documentation": "<p>A <code>Destinations</code> object that includes information about each publishing destination returned.</p>", "locationName": "destinations"}, "NextToken": {"shape": "String", "documentation": "<p>A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request to a list action. For subsequent calls, use the <code>NextToken</code> value returned from the previous request to continue listing results after the first page.</p>", "locationName": "nextToken"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "GuardDutyArn", "documentation": "<p>The Amazon Resource Name (ARN) for the given GuardDuty resource. </p>", "location": "uri", "locationName": "resourceArn"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"Tags": {"shape": "TagMap", "documentation": "<p>The tags associated with the resource.</p>", "locationName": "tags"}}}, "ListThreatIntelSetsRequest": {"type": "structure", "required": ["DetectorId"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The unique ID of the detector that is associated with the threatIntelSet.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>You can use this parameter to indicate the maximum number of items that you want in the response. The default value is 50. The maximum value is 50.</p>", "location": "querystring", "locationName": "maxResults"}, "NextToken": {"shape": "String", "documentation": "<p>You can use this parameter to paginate results in the response. Set the value of this parameter to null on your first call to the list action. For subsequent calls to the action, fill nextToken in the request with the value of NextToken from the previous response to continue listing data.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListThreatIntelSetsResponse": {"type": "structure", "required": ["ThreatIntelSetIds"], "members": {"ThreatIntelSetIds": {"shape": "ThreatIntelSetIds", "documentation": "<p>The IDs of the ThreatIntelSet resources.</p>", "locationName": "threatIntelSetIds"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination parameter to be used on the next list operation to retrieve more items.</p>", "locationName": "nextToken"}}}, "LocalIpDetails": {"type": "structure", "members": {"IpAddressV4": {"shape": "SensitiveString", "documentation": "<p>The IPv4 local address of the connection.</p>", "locationName": "ipAddressV4"}, "IpAddressV6": {"shape": "SensitiveString", "documentation": "<p>The IPv6 local address of the connection.</p>", "locationName": "ipAddressV6"}}, "documentation": "<p>Contains information about the local IP address of the connection.</p>"}, "LocalPortDetails": {"type": "structure", "members": {"Port": {"shape": "Integer", "documentation": "<p>The port number of the local connection.</p>", "locationName": "port"}, "PortName": {"shape": "String", "documentation": "<p>The port name of the local connection.</p>", "locationName": "portName"}}, "documentation": "<p>Contains information about the port for the local connection.</p>"}, "Location": {"type": "string", "max": 300, "min": 1}, "LoginAttribute": {"type": "structure", "members": {"User": {"shape": "String", "documentation": "<p>Indicates the user name which attempted to log in.</p>", "locationName": "user"}, "Application": {"shape": "String", "documentation": "<p>Indicates the application name used to attempt log in.</p>", "locationName": "application"}, "FailedLoginAttempts": {"shape": "Integer", "documentation": "<p>Represents the sum of failed (unsuccessful) login attempts made to establish a connection to the database instance.</p>", "locationName": "failed<PERSON><PERSON>in<PERSON><PERSON><PERSON>s"}, "SuccessfulLoginAttempts": {"shape": "Integer", "documentation": "<p>Represents the sum of successful connections (a correct combination of login attributes) made to the database instance by the actor.</p>", "locationName": "successfulLogin<PERSON>ttempts"}}, "documentation": "<p>Information about the login attempts.</p>"}, "LoginAttributes": {"type": "list", "member": {"shape": "LoginAttribute"}}, "Long": {"type": "long"}, "LongValue": {"type": "long"}, "MalwareProtectionConfiguration": {"type": "structure", "members": {"ScanEc2InstanceWithFindings": {"shape": "ScanEc2InstanceWithFindings", "documentation": "<p>Describes the configuration of Malware Protection for EC2 instances with findings.</p>", "locationName": "scanEc2InstanceWithFindings"}}, "documentation": "<p>Describes whether Malware Protection will be enabled as a data source.</p>"}, "MalwareProtectionConfigurationResult": {"type": "structure", "members": {"ScanEc2InstanceWithFindings": {"shape": "ScanEc2InstanceWithFindingsResult", "documentation": "<p>Describes the configuration of Malware Protection for EC2 instances with findings.</p>", "locationName": "scanEc2InstanceWithFindings"}, "ServiceRole": {"shape": "String", "documentation": "<p>The GuardDuty Malware Protection service role.</p>", "locationName": "serviceRole"}}, "documentation": "<p>An object that contains information on the status of all Malware Protection data sources.</p>"}, "MalwareProtectionDataSourceFreeTrial": {"type": "structure", "members": {"ScanEc2InstanceWithFindings": {"shape": "DataSourceFreeTrial", "documentation": "<p>Describes whether Malware Protection for EC2 instances with findings is enabled as a data source.</p>", "locationName": "scanEc2InstanceWithFindings"}}, "documentation": "<p>Provides details about Malware Protection when it is enabled as a data source.</p>"}, "MalwareProtectionPlanActions": {"type": "structure", "members": {"Tagging": {"shape": "MalwareProtectionPlanTaggingAction", "documentation": "<p>Indicates whether the scanned S3 object will have tags about the scan result.</p>", "locationName": "tagging"}}, "documentation": "<p>Information about whether the tags will be added to the S3 object after scanning.</p>"}, "MalwareProtectionPlanObjectPrefixesList": {"type": "list", "member": {"shape": "String"}, "max": 5, "min": 0}, "MalwareProtectionPlanStatus": {"type": "string", "enum": ["ACTIVE", "WARNING", "ERROR"]}, "MalwareProtectionPlanStatusReason": {"type": "structure", "members": {"Code": {"shape": "String", "documentation": "<p>Issue code.</p>", "locationName": "code"}, "Message": {"shape": "String", "documentation": "<p>Issue message that specifies the reason. For information about potential troubleshooting steps, see <a href=\"https://docs.aws.amazon.com/guardduty/latest/ug/troubleshoot-s3-malware-protection-status-errors.html\">Troubleshooting Malware Protection for S3 status issues</a> in the <i>Amazon GuardDuty User Guide</i>.</p>", "locationName": "message"}}, "documentation": "<p>Information about the issue code and message associated to the status of your Malware Protection plan.</p>"}, "MalwareProtectionPlanStatusReasonsList": {"type": "list", "member": {"shape": "MalwareProtectionPlanStatusReason"}, "max": 50, "min": 0}, "MalwareProtectionPlanSummary": {"type": "structure", "members": {"MalwareProtectionPlanId": {"shape": "String", "documentation": "<p>A unique identifier associated with Malware Protection plan.</p>", "locationName": "malwareProtectionPlanId"}}, "documentation": "<p>Information about the Malware Protection plan resource.</p>"}, "MalwareProtectionPlanTaggingAction": {"type": "structure", "members": {"Status": {"shape": "MalwareProtectionPlanTaggingActionStatus", "documentation": "<p>Indicates whether or not the tags will added.</p>", "locationName": "status"}}, "documentation": "<p>Information about adding tags to the scanned S3 object after the scan result.</p>"}, "MalwareProtectionPlanTaggingActionStatus": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "MalwareProtectionPlansSummary": {"type": "list", "member": {"shape": "MalwareProtectionPlanSummary"}}, "MalwareScanDetails": {"type": "structure", "members": {"Threats": {"shape": "Threats", "documentation": "<p>Information about the detected threats associated with the generated GuardDuty finding.</p>", "locationName": "threats"}}, "documentation": "<p>Information about the malware scan that generated a GuardDuty finding.</p>"}, "ManagementType": {"type": "string", "enum": ["AUTO_MANAGED", "MANUAL", "DISABLED"]}, "MapEquals": {"type": "list", "member": {"shape": "ScanConditionPair"}}, "Master": {"type": "structure", "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The ID of the account used as the administrator account.</p>", "locationName": "accountId"}, "InvitationId": {"shape": "String", "documentation": "<p>The value used to validate the administrator account to the member account.</p>", "locationName": "invitationId"}, "RelationshipStatus": {"shape": "String", "documentation": "<p>The status of the relationship between the administrator and member accounts.</p>", "locationName": "relationshipStatus"}, "InvitedAt": {"shape": "String", "documentation": "<p>The timestamp when the invitation was sent.</p>", "locationName": "invitedAt"}}, "documentation": "<p>Contains information about the administrator account and invitation.</p>"}, "MaxResults": {"type": "integer", "max": 50, "min": 1}, "MaxResults100": {"type": "integer", "max": 100, "min": 1}, "Member": {"type": "structure", "required": ["AccountId", "MasterId", "Email", "RelationshipStatus", "UpdatedAt"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The ID of the member account.</p>", "locationName": "accountId"}, "DetectorId": {"shape": "DetectorId", "documentation": "<p>The detector ID of the member account.</p>", "locationName": "detectorId"}, "MasterId": {"shape": "String", "documentation": "<p>The administrator account ID.</p>", "locationName": "masterId"}, "Email": {"shape": "Email", "documentation": "<p>The email address of the member account.</p>", "locationName": "email"}, "RelationshipStatus": {"shape": "String", "documentation": "<p>The status of the relationship between the member and the administrator.</p>", "locationName": "relationshipStatus"}, "InvitedAt": {"shape": "String", "documentation": "<p>The timestamp when the invitation was sent.</p>", "locationName": "invitedAt"}, "UpdatedAt": {"shape": "String", "documentation": "<p>The last-updated timestamp of the member.</p>", "locationName": "updatedAt"}, "AdministratorId": {"shape": "String", "documentation": "<p>The administrator account ID.</p>", "locationName": "administratorId"}}, "documentation": "<p>Contains information about the member account. </p>"}, "MemberAdditionalConfiguration": {"type": "structure", "members": {"Name": {"shape": "OrgFeatureAdditionalConfiguration", "documentation": "<p>Name of the additional configuration.</p>", "locationName": "name"}, "Status": {"shape": "FeatureStatus", "documentation": "<p>Status of the additional configuration.</p>", "locationName": "status"}}, "documentation": "<p>Information about the additional configuration for the member account.</p>"}, "MemberAdditionalConfigurationResult": {"type": "structure", "members": {"Name": {"shape": "OrgFeatureAdditionalConfiguration", "documentation": "<p>Indicates the name of the additional configuration that is set for the member account.</p>", "locationName": "name"}, "Status": {"shape": "FeatureStatus", "documentation": "<p>Indicates the status of the additional configuration that is set for the member account.</p>", "locationName": "status"}, "UpdatedAt": {"shape": "Timestamp", "documentation": "<p>The timestamp at which the additional configuration was set for the member account. This is in UTC format.</p>", "locationName": "updatedAt"}}, "documentation": "<p>Information about the additional configuration for the member account.</p>"}, "MemberAdditionalConfigurationResults": {"type": "list", "member": {"shape": "MemberAdditionalConfigurationResult"}}, "MemberAdditionalConfigurations": {"type": "list", "member": {"shape": "MemberAdditionalConfiguration"}}, "MemberDataSourceConfiguration": {"type": "structure", "required": ["AccountId"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The account ID for the member account.</p>", "locationName": "accountId"}, "DataSources": {"shape": "DataSourceConfigurationsResult", "documentation": "<p>Contains information on the status of data sources for the account.</p>", "deprecated": true, "deprecatedMessage": "This parameter is deprecated, use Features instead", "locationName": "dataSources"}, "Features": {"shape": "MemberFeaturesConfigurationsResults", "documentation": "<p>Contains information about the status of the features for the member account.</p>", "locationName": "features"}}, "documentation": "<p>Contains information on which data sources are enabled for a member account.</p>"}, "MemberDataSourceConfigurations": {"type": "list", "member": {"shape": "MemberDataSourceConfiguration"}, "max": 50, "min": 1}, "MemberFeaturesConfiguration": {"type": "structure", "members": {"Name": {"shape": "OrgFeature", "documentation": "<p>The name of the feature.</p>", "locationName": "name"}, "Status": {"shape": "FeatureStatus", "documentation": "<p>The status of the feature.</p>", "locationName": "status"}, "AdditionalConfiguration": {"shape": "MemberAdditionalConfigurations", "documentation": "<p>Additional configuration of the feature for the member account.</p>", "locationName": "additionalConfiguration"}}, "documentation": "<p>Contains information about the features for the member account.</p>"}, "MemberFeaturesConfigurationResult": {"type": "structure", "members": {"Name": {"shape": "OrgFeature", "documentation": "<p>Indicates the name of the feature that is enabled for the detector.</p>", "locationName": "name"}, "Status": {"shape": "FeatureStatus", "documentation": "<p>Indicates the status of the feature that is enabled for the detector.</p>", "locationName": "status"}, "UpdatedAt": {"shape": "Timestamp", "documentation": "<p>The timestamp at which the feature object was updated.</p>", "locationName": "updatedAt"}, "AdditionalConfiguration": {"shape": "MemberAdditionalConfigurationResults", "documentation": "<p>Indicates the additional configuration of the feature that is configured for the member account.</p>", "locationName": "additionalConfiguration"}}, "documentation": "<p>Contains information about the features for the member account.</p>"}, "MemberFeaturesConfigurations": {"type": "list", "member": {"shape": "MemberFeaturesConfiguration"}}, "MemberFeaturesConfigurationsResults": {"type": "list", "member": {"shape": "MemberFeaturesConfigurationResult"}}, "Members": {"type": "list", "member": {"shape": "Member"}, "max": 50, "min": 0}, "MemoryRegionsList": {"type": "list", "member": {"shape": "String"}}, "MfaStatus": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "Name": {"type": "string", "max": 300, "min": 1}, "Neq": {"type": "list", "member": {"shape": "String"}}, "NetworkConnection": {"type": "structure", "required": ["Direction"], "members": {"Direction": {"shape": "NetworkDirection", "documentation": "<p>The direction in which the network traffic is flowing.</p>", "locationName": "direction"}}, "documentation": "<p>Contains information about the network connection.</p>"}, "NetworkConnectionAction": {"type": "structure", "members": {"Blocked": {"shape": "Boolean", "documentation": "<p>Indicates whether EC2 blocked the network connection to your instance.</p>", "locationName": "blocked"}, "ConnectionDirection": {"shape": "String", "documentation": "<p>The network connection direction.</p>", "locationName": "connectionDirection"}, "LocalPortDetails": {"shape": "LocalPortDetails", "documentation": "<p>The local port information of the connection.</p>", "locationName": "localPortDetails"}, "Protocol": {"shape": "String", "documentation": "<p>The network connection protocol.</p>", "locationName": "protocol"}, "LocalIpDetails": {"shape": "LocalIpDetails", "documentation": "<p>The local IP information of the connection.</p>", "locationName": "localIpDetails"}, "LocalNetworkInterface": {"shape": "String", "documentation": "<p>The EC2 instance's local elastic network interface utilized for the connection.</p>", "locationName": "localNetworkInterface"}, "RemoteIpDetails": {"shape": "RemoteIpDetails", "documentation": "<p>The remote IP information of the connection.</p>", "locationName": "remoteIpDetails"}, "RemotePortDetails": {"shape": "RemotePortDetails", "documentation": "<p>The remote port information of the connection.</p>", "locationName": "remotePortDetails"}}, "documentation": "<p>Contains information about the NETWORK_CONNECTION action described in the finding.</p>"}, "NetworkDirection": {"type": "string", "enum": ["INBOUND", "OUTBOUND"]}, "NetworkEndpoint": {"type": "structure", "required": ["Id"], "members": {"Id": {"shape": "String", "documentation": "<p>The ID of the network endpoint.</p>", "locationName": "id"}, "Ip": {"shape": "String", "documentation": "<p>The IP address associated with the network endpoint.</p>", "locationName": "ip"}, "Domain": {"shape": "String", "documentation": "<p>The domain information for the network endpoint.</p>", "locationName": "domain"}, "Port": {"shape": "Integer", "documentation": "<p>The port number associated with the network endpoint.</p>", "locationName": "port"}, "Location": {"shape": "NetworkGeoLocation", "documentation": "<p>Information about the location of the network endpoint.</p>", "locationName": "location"}, "AutonomousSystem": {"shape": "AutonomousSystem", "documentation": "<p>The Autonomous System (AS) of the network endpoint.</p>", "locationName": "autonomousSystem"}, "Connection": {"shape": "NetworkConnection", "documentation": "<p>Information about the network connection.</p>", "locationName": "connection"}}, "documentation": "<p>Contains information about network endpoints that were observed in the attack sequence.</p>"}, "NetworkEndpoints": {"type": "list", "member": {"shape": "NetworkEndpoint"}, "max": 400}, "NetworkGeoLocation": {"type": "structure", "required": ["City", "Country", "Latitude", "Longitude"], "members": {"City": {"shape": "String", "documentation": "<p>The name of the city.</p>", "locationName": "city"}, "Country": {"shape": "String", "documentation": "<p>The name of the country.</p>", "locationName": "country"}, "Latitude": {"shape": "Double", "documentation": "<p>The latitude information of the endpoint location.</p>", "locationName": "lat"}, "Longitude": {"shape": "Double", "documentation": "<p>The longitude information of the endpoint location.</p>", "locationName": "lon"}}, "documentation": "<p>Contains information about network endpoint location.</p>"}, "NetworkInterface": {"type": "structure", "members": {"Ipv6Addresses": {"shape": "Ipv6Addresses", "documentation": "<p>A list of IPv6 addresses for the EC2 instance.</p>", "locationName": "ipv6Addresses"}, "NetworkInterfaceId": {"shape": "String", "documentation": "<p>The ID of the network interface.</p>", "locationName": "networkInterfaceId"}, "PrivateDnsName": {"shape": "String", "documentation": "<p>The private DNS name of the EC2 instance.</p>", "locationName": "privateDnsName"}, "PrivateIpAddress": {"shape": "SensitiveString", "documentation": "<p>The private IP address of the EC2 instance.</p>", "locationName": "privateIpAddress"}, "PrivateIpAddresses": {"shape": "PrivateIpAddresses", "documentation": "<p>Other private IP address information of the EC2 instance.</p>", "locationName": "privateIpAddresses"}, "PublicDnsName": {"shape": "String", "documentation": "<p>The public DNS name of the EC2 instance.</p>", "locationName": "publicDnsName"}, "PublicIp": {"shape": "String", "documentation": "<p>The public IP address of the EC2 instance.</p>", "locationName": "publicIp"}, "SecurityGroups": {"shape": "SecurityGroups", "documentation": "<p>The security groups associated with the EC2 instance.</p>", "locationName": "securityGroups"}, "SubnetId": {"shape": "String", "documentation": "<p>The subnet ID of the EC2 instance.</p>", "locationName": "subnetId"}, "VpcId": {"shape": "String", "documentation": "<p>The VPC ID of the EC2 instance.</p>", "locationName": "vpcId"}}, "documentation": "<p>Contains information about the elastic network interface of the EC2 instance.</p>"}, "NetworkInterfaces": {"type": "list", "member": {"shape": "NetworkInterface"}}, "NonEmptyString": {"type": "string", "max": 200, "min": 1}, "NotEquals": {"type": "list", "member": {"shape": "String"}}, "ObservationTexts": {"type": "list", "member": {"shape": "String"}}, "Observations": {"type": "structure", "members": {"Text": {"shape": "ObservationTexts", "documentation": "<p>The text that was unusual.</p>", "locationName": "text"}}, "documentation": "<p>Contains information about the observed behavior.</p>"}, "OrderBy": {"type": "string", "enum": ["ASC", "DESC"]}, "OrgFeature": {"type": "string", "enum": ["S3_DATA_EVENTS", "EKS_AUDIT_LOGS", "EBS_MALWARE_PROTECTION", "RDS_LOGIN_EVENTS", "EKS_RUNTIME_MONITORING", "LAMBDA_NETWORK_LOGS", "RUNTIME_MONITORING"]}, "OrgFeatureAdditionalConfiguration": {"type": "string", "enum": ["EKS_ADDON_MANAGEMENT", "ECS_FARGATE_AGENT_MANAGEMENT", "EC2_AGENT_MANAGEMENT"]}, "OrgFeatureStatus": {"type": "string", "enum": ["NEW", "NONE", "ALL"]}, "Organization": {"type": "structure", "members": {"Asn": {"shape": "String", "documentation": "<p>The Autonomous System Number (ASN) of the internet provider of the remote IP address.</p>", "locationName": "asn"}, "AsnOrg": {"shape": "String", "documentation": "<p>The organization that registered this ASN.</p>", "locationName": "asnOrg"}, "Isp": {"shape": "String", "documentation": "<p>The ISP information for the internet provider.</p>", "locationName": "isp"}, "Org": {"shape": "String", "documentation": "<p>The name of the internet provider.</p>", "locationName": "org"}}, "documentation": "<p>Contains information about the ISP organization of the remote IP address.</p>"}, "OrganizationAdditionalConfiguration": {"type": "structure", "members": {"Name": {"shape": "OrgFeatureAdditionalConfiguration", "documentation": "<p>The name of the additional configuration that will be configured for the organization. These values are applicable to only Runtime Monitoring protection plan.</p>", "locationName": "name"}, "AutoEnable": {"shape": "OrgFeatureStatus", "documentation": "<p>The status of the additional configuration that will be configured for the organization. Use one of the following values to configure the feature status for the entire organization:</p> <ul> <li> <p> <code>NEW</code>: Indicates that when a new account joins the organization, they will have the additional configuration enabled automatically. </p> </li> <li> <p> <code>ALL</code>: Indicates that all accounts in the organization have the additional configuration enabled automatically. This includes <code>NEW</code> accounts that join the organization and accounts that may have been suspended or removed from the organization in GuardDuty.</p> <p>It may take up to 24 hours to update the configuration for all the member accounts.</p> </li> <li> <p> <code>NONE</code>: Indicates that the additional configuration will not be automatically enabled for any account in the organization. The administrator must manage the additional configuration for each account individually.</p> </li> </ul>", "locationName": "autoEnable"}}, "documentation": "<p>A list of additional configurations which will be configured for the organization. </p> <p>Additional configuration applies to only GuardDuty Runtime Monitoring protection plan.</p>"}, "OrganizationAdditionalConfigurationResult": {"type": "structure", "members": {"Name": {"shape": "OrgFeatureAdditionalConfiguration", "documentation": "<p>The name of the additional configuration that is configured for the member accounts within the organization. These values are applicable to only Runtime Monitoring protection plan.</p>", "locationName": "name"}, "AutoEnable": {"shape": "OrgFeatureStatus", "documentation": "<p>Describes the status of the additional configuration that is configured for the member accounts within the organization. One of the following values is the status for the entire organization:</p> <ul> <li> <p> <code>NEW</code>: Indicates that when a new account joins the organization, they will have the additional configuration enabled automatically. </p> </li> <li> <p> <code>ALL</code>: Indicates that all accounts in the organization have the additional configuration enabled automatically. This includes <code>NEW</code> accounts that join the organization and accounts that may have been suspended or removed from the organization in GuardDuty.</p> <p>It may take up to 24 hours to update the configuration for all the member accounts.</p> </li> <li> <p> <code>NONE</code>: Indicates that the additional configuration will not be automatically enabled for any account in the organization. The administrator must manage the additional configuration for each account individually.</p> </li> </ul>", "locationName": "autoEnable"}}, "documentation": "<p>A list of additional configuration which will be configured for the organization.</p>"}, "OrganizationAdditionalConfigurationResults": {"type": "list", "member": {"shape": "OrganizationAdditionalConfigurationResult"}}, "OrganizationAdditionalConfigurations": {"type": "list", "member": {"shape": "OrganizationAdditionalConfiguration"}}, "OrganizationDataSourceConfigurations": {"type": "structure", "members": {"S3Logs": {"shape": "OrganizationS3LogsConfiguration", "documentation": "<p>Describes whether S3 data event logs are enabled for new members of the organization.</p>", "locationName": "s3Logs"}, "Kubernetes": {"shape": "OrganizationKubernetesConfiguration", "documentation": "<p>Describes the configuration of Kubernetes data sources for new members of the organization.</p>", "locationName": "kubernetes"}, "MalwareProtection": {"shape": "OrganizationMalwareProtectionConfiguration", "documentation": "<p>Describes the configuration of Malware Protection for new members of the organization.</p>", "locationName": "malwareProtection"}}, "documentation": "<p>An object that contains information on which data sources will be configured to be automatically enabled for new members within the organization.</p>"}, "OrganizationDataSourceConfigurationsResult": {"type": "structure", "required": ["S3Logs"], "members": {"S3Logs": {"shape": "OrganizationS3LogsConfigurationResult", "documentation": "<p>Describes whether S3 data event logs are enabled as a data source.</p>", "locationName": "s3Logs"}, "Kubernetes": {"shape": "OrganizationKubernetesConfigurationResult", "documentation": "<p>Describes the configuration of Kubernetes data sources.</p>", "locationName": "kubernetes"}, "MalwareProtection": {"shape": "OrganizationMalwareProtectionConfigurationResult", "documentation": "<p>Describes the configuration of Malware Protection data source for an organization.</p>", "locationName": "malwareProtection"}}, "documentation": "<p>An object that contains information on which data sources are automatically enabled for new members within the organization.</p>"}, "OrganizationDetails": {"type": "structure", "members": {"UpdatedAt": {"shape": "Timestamp", "documentation": "<p>The timestamp at which the organization statistics was last updated. This is in UTC format.</p>", "locationName": "updatedAt"}, "OrganizationStatistics": {"shape": "OrganizationStatistics", "documentation": "<p>Information about the GuardDuty coverage statistics for members in your Amazon Web Services organization.</p>", "locationName": "organizationStatistics"}}, "documentation": "<p>Information about GuardDuty coverage statistics for members in your Amazon Web Services organization.</p>"}, "OrganizationEbsVolumes": {"type": "structure", "members": {"AutoEnable": {"shape": "Boolean", "documentation": "<p>Whether scanning EBS volumes should be auto-enabled for new members joining the organization.</p>", "locationName": "autoEnable"}}, "documentation": "<p>Organization-wide EBS volumes scan configuration.</p>"}, "OrganizationEbsVolumesResult": {"type": "structure", "members": {"AutoEnable": {"shape": "Boolean", "documentation": "<p>An object that contains the status of whether scanning EBS volumes should be auto-enabled for new members joining the organization.</p>", "locationName": "autoEnable"}}, "documentation": "<p>An object that contains information on the status of whether EBS volumes scanning will be enabled as a data source for an organization.</p>"}, "OrganizationFeatureConfiguration": {"type": "structure", "members": {"Name": {"shape": "OrgFeature", "documentation": "<p>The name of the feature that will be configured for the organization.</p>", "locationName": "name"}, "AutoEnable": {"shape": "OrgFeatureStatus", "documentation": "<p>Describes the status of the feature that is configured for the member accounts within the organization. One of the following values is the status for the entire organization:</p> <ul> <li> <p> <code>NEW</code>: Indicates that when a new account joins the organization, they will have the feature enabled automatically. </p> </li> <li> <p> <code>ALL</code>: Indicates that all accounts in the organization have the feature enabled automatically. This includes <code>NEW</code> accounts that join the organization and accounts that may have been suspended or removed from the organization in GuardDuty.</p> <p>It may take up to 24 hours to update the configuration for all the member accounts.</p> </li> <li> <p> <code>NONE</code>: Indicates that the feature will not be automatically enabled for any account in the organization. The administrator must manage the feature for each account individually.</p> </li> </ul>", "locationName": "autoEnable"}, "AdditionalConfiguration": {"shape": "OrganizationAdditionalConfigurations", "documentation": "<p>The additional information that will be configured for the organization.</p>", "locationName": "additionalConfiguration"}}, "documentation": "<p>A list of features which will be configured for the organization.</p>"}, "OrganizationFeatureConfigurationResult": {"type": "structure", "members": {"Name": {"shape": "OrgFeature", "documentation": "<p>The name of the feature that is configured for the member accounts within the organization.</p>", "locationName": "name"}, "AutoEnable": {"shape": "OrgFeatureStatus", "documentation": "<p>Describes the status of the feature that is configured for the member accounts within the organization.</p> <ul> <li> <p> <code>NEW</code>: Indicates that when a new account joins the organization, they will have the feature enabled automatically. </p> </li> <li> <p> <code>ALL</code>: Indicates that all accounts in the organization have the feature enabled automatically. This includes <code>NEW</code> accounts that join the organization and accounts that may have been suspended or removed from the organization in GuardDuty.</p> </li> <li> <p> <code>NONE</code>: Indicates that the feature will not be automatically enabled for any account in the organization. In this case, each account will be managed individually by the administrator.</p> </li> </ul>", "locationName": "autoEnable"}, "AdditionalConfiguration": {"shape": "OrganizationAdditionalConfigurationResults", "documentation": "<p>The additional configuration that is configured for the member accounts within the organization.</p>", "locationName": "additionalConfiguration"}}, "documentation": "<p>A list of features which will be configured for the organization.</p>"}, "OrganizationFeatureStatistics": {"type": "structure", "members": {"Name": {"shape": "OrgFeature", "documentation": "<p>Name of the feature.</p>", "locationName": "name"}, "EnabledAccountsCount": {"shape": "Integer", "documentation": "<p>Total number of accounts that have enabled a specific feature.</p>", "locationName": "enabledAccountsCount"}, "AdditionalConfiguration": {"shape": "OrganizationFeatureStatisticsAdditionalConfigurations", "documentation": "<p>Name of the additional configuration.</p>", "locationName": "additionalConfiguration"}}, "documentation": "<p>Information about the number of accounts that have enabled a specific feature.</p>"}, "OrganizationFeatureStatisticsAdditionalConfiguration": {"type": "structure", "members": {"Name": {"shape": "OrgFeatureAdditionalConfiguration", "documentation": "<p>Name of the additional configuration within a feature.</p>", "locationName": "name"}, "EnabledAccountsCount": {"shape": "Integer", "documentation": "<p>Total number of accounts that have enabled the additional configuration.</p>", "locationName": "enabledAccountsCount"}}, "documentation": "<p>Information about the coverage statistic for the additional configuration of the feature.</p>"}, "OrganizationFeatureStatisticsAdditionalConfigurations": {"type": "list", "member": {"shape": "OrganizationFeatureStatisticsAdditionalConfiguration"}}, "OrganizationFeatureStatisticsResults": {"type": "list", "member": {"shape": "OrganizationFeatureStatistics"}}, "OrganizationFeaturesConfigurations": {"type": "list", "member": {"shape": "OrganizationFeatureConfiguration"}}, "OrganizationFeaturesConfigurationsResults": {"type": "list", "member": {"shape": "OrganizationFeatureConfigurationResult"}}, "OrganizationKubernetesAuditLogsConfiguration": {"type": "structure", "required": ["AutoEnable"], "members": {"AutoEnable": {"shape": "Boolean", "documentation": "<p>A value that contains information on whether Kubernetes audit logs should be enabled automatically as a data source for the organization.</p>", "locationName": "autoEnable"}}, "documentation": "<p>Organization-wide Kubernetes audit logs configuration.</p>"}, "OrganizationKubernetesAuditLogsConfigurationResult": {"type": "structure", "required": ["AutoEnable"], "members": {"AutoEnable": {"shape": "Boolean", "documentation": "<p>Whether Kubernetes audit logs data source should be auto-enabled for new members joining the organization.</p>", "locationName": "autoEnable"}}, "documentation": "<p>The current configuration of Kubernetes audit logs as a data source for the organization.</p>"}, "OrganizationKubernetesConfiguration": {"type": "structure", "required": ["AuditLogs"], "members": {"AuditLogs": {"shape": "OrganizationKubernetesAuditLogsConfiguration", "documentation": "<p>Whether Kubernetes audit logs data source should be auto-enabled for new members joining the organization.</p>", "locationName": "auditLogs"}}, "documentation": "<p>Organization-wide Kubernetes data sources configurations.</p>"}, "OrganizationKubernetesConfigurationResult": {"type": "structure", "required": ["AuditLogs"], "members": {"AuditLogs": {"shape": "OrganizationKubernetesAuditLogsConfigurationResult", "documentation": "<p>The current configuration of Kubernetes audit logs as a data source for the organization.</p>", "locationName": "auditLogs"}}, "documentation": "<p>The current configuration of all Kubernetes data sources for the organization.</p>"}, "OrganizationMalwareProtectionConfiguration": {"type": "structure", "members": {"ScanEc2InstanceWithFindings": {"shape": "OrganizationScanEc2InstanceWithFindings", "documentation": "<p>Whether Malware Protection for EC2 instances with findings should be auto-enabled for new members joining the organization.</p>", "locationName": "scanEc2InstanceWithFindings"}}, "documentation": "<p>Organization-wide Malware Protection configurations.</p>"}, "OrganizationMalwareProtectionConfigurationResult": {"type": "structure", "members": {"ScanEc2InstanceWithFindings": {"shape": "OrganizationScanEc2InstanceWithFindingsResult", "documentation": "<p>Describes the configuration for scanning EC2 instances with findings for an organization.</p>", "locationName": "scanEc2InstanceWithFindings"}}, "documentation": "<p>An object that contains information on the status of all Malware Protection data source for an organization.</p>"}, "OrganizationS3LogsConfiguration": {"type": "structure", "required": ["AutoEnable"], "members": {"AutoEnable": {"shape": "Boolean", "documentation": "<p>A value that contains information on whether S3 data event logs will be enabled automatically as a data source for the organization.</p>", "locationName": "autoEnable"}}, "documentation": "<p>Describes whether S3 data event logs will be automatically enabled for new members of the organization.</p>"}, "OrganizationS3LogsConfigurationResult": {"type": "structure", "required": ["AutoEnable"], "members": {"AutoEnable": {"shape": "Boolean", "documentation": "<p>A value that describes whether S3 data event logs are automatically enabled for new members of the organization.</p>", "locationName": "autoEnable"}}, "documentation": "<p>The current configuration of S3 data event logs as a data source for the organization.</p>"}, "OrganizationScanEc2InstanceWithFindings": {"type": "structure", "members": {"EbsVolumes": {"shape": "OrganizationEbsVolumes", "documentation": "<p>Whether scanning EBS volumes should be auto-enabled for new members joining the organization.</p>", "locationName": "ebsVolumes"}}, "documentation": "<p>Organization-wide EC2 instances with findings scan configuration.</p>"}, "OrganizationScanEc2InstanceWithFindingsResult": {"type": "structure", "members": {"EbsVolumes": {"shape": "OrganizationEbsVolumesResult", "documentation": "<p>Describes the configuration for scanning EBS volumes for an organization.</p>", "locationName": "ebsVolumes"}}, "documentation": "<p>An object that contains information on the status of scanning EC2 instances with findings for an organization.</p>"}, "OrganizationStatistics": {"type": "structure", "members": {"TotalAccountsCount": {"shape": "Integer", "documentation": "<p>Total number of accounts in your Amazon Web Services organization.</p>", "locationName": "totalAccountsCount"}, "MemberAccountsCount": {"shape": "Integer", "documentation": "<p>Total number of accounts in your Amazon Web Services organization that are associated with GuardDuty.</p>", "locationName": "memberAccountsCount"}, "ActiveAccountsCount": {"shape": "Integer", "documentation": "<p>Total number of active accounts in your Amazon Web Services organization that are associated with GuardDuty.</p>", "locationName": "activeAccountsCount"}, "EnabledAccountsCount": {"shape": "Integer", "documentation": "<p>Total number of accounts that have enabled GuardDuty.</p>", "locationName": "enabledAccountsCount"}, "CountByFeature": {"shape": "OrganizationFeatureStatisticsResults", "documentation": "<p>Retrieves the coverage statistics for each feature.</p>", "locationName": "countByFeature"}}, "documentation": "<p>Information about the coverage statistics of the features for the entire Amazon Web Services organization.</p> <p>When you create a new Amazon Web Services organization, it might take up to 24 hours to generate the statistics summary for this organization.</p>"}, "Owner": {"type": "structure", "members": {"Id": {"shape": "String", "documentation": "<p>The canonical user ID of the bucket owner. For information about locating your canonical user ID see <a href=\"https://docs.aws.amazon.com/general/latest/gr/acct-identifiers.html#FindingCanonicalId\">Finding Your Account Canonical User ID.</a> </p>", "locationName": "id"}}, "documentation": "<p>Contains information on the owner of the bucket.</p>"}, "PermissionConfiguration": {"type": "structure", "members": {"BucketLevelPermissions": {"shape": "BucketLevelPermissions", "documentation": "<p>Contains information about the bucket level permissions for the S3 bucket.</p>", "locationName": "bucketLevelPermissions"}, "AccountLevelPermissions": {"shape": "AccountLevelPermissions", "documentation": "<p>Contains information about the account level permissions on the S3 bucket.</p>", "locationName": "accountLevelPermissions"}}, "documentation": "<p>Contains information about how permissions are configured for the S3 bucket.</p>"}, "PortProbeAction": {"type": "structure", "members": {"Blocked": {"shape": "Boolean", "documentation": "<p>Indicates whether EC2 blocked the port probe to the instance, such as with an ACL.</p>", "locationName": "blocked"}, "PortProbeDetails": {"shape": "PortProbeDetails", "documentation": "<p>A list of objects related to port probe details.</p>", "locationName": "portProbeDetails"}}, "documentation": "<p>Contains information about the PORT_PROBE action described in the finding.</p>"}, "PortProbeDetail": {"type": "structure", "members": {"LocalPortDetails": {"shape": "LocalPortDetails", "documentation": "<p>The local port information of the connection.</p>", "locationName": "localPortDetails"}, "LocalIpDetails": {"shape": "LocalIpDetails", "documentation": "<p>The local IP information of the connection.</p>", "locationName": "localIpDetails"}, "RemoteIpDetails": {"shape": "RemoteIpDetails", "documentation": "<p>The remote IP information of the connection.</p>", "locationName": "remoteIpDetails"}}, "documentation": "<p>Contains information about the port probe details.</p>"}, "PortProbeDetails": {"type": "list", "member": {"shape": "PortProbeDetail"}}, "PositiveLong": {"type": "long", "min": 0}, "PrivateIpAddressDetails": {"type": "structure", "members": {"PrivateDnsName": {"shape": "String", "documentation": "<p>The private DNS name of the EC2 instance.</p>", "locationName": "privateDnsName"}, "PrivateIpAddress": {"shape": "SensitiveString", "documentation": "<p>The private IP address of the EC2 instance.</p>", "locationName": "privateIpAddress"}}, "documentation": "<p>Contains other private IP address information of the EC2 instance.</p>"}, "PrivateIpAddresses": {"type": "list", "member": {"shape": "PrivateIpAddressDetails"}}, "ProcessDetails": {"type": "structure", "members": {"Name": {"shape": "String", "documentation": "<p>The name of the process.</p>", "locationName": "name"}, "ExecutablePath": {"shape": "String", "documentation": "<p>The absolute path of the process executable file.</p>", "locationName": "executablePath"}, "ExecutableSha256": {"shape": "String", "documentation": "<p>The <code>SHA256</code> hash of the process executable.</p>", "locationName": "executableSha256"}, "NamespacePid": {"shape": "Integer", "documentation": "<p>The ID of the child process.</p>", "locationName": "namespacePid"}, "Pwd": {"shape": "String", "documentation": "<p>The present working directory of the process.</p>", "locationName": "pwd"}, "Pid": {"shape": "Integer", "documentation": "<p>The ID of the process.</p>", "locationName": "pid"}, "StartTime": {"shape": "Timestamp", "documentation": "<p>The time when the process started. This is in UTC format.</p>", "locationName": "startTime"}, "Uuid": {"shape": "String", "documentation": "<p>The unique ID assigned to the process by GuardDuty.</p>", "locationName": "uuid"}, "ParentUuid": {"shape": "String", "documentation": "<p>The unique ID of the parent process. This ID is assigned to the parent process by GuardDuty.</p>", "locationName": "parentUuid"}, "User": {"shape": "String", "documentation": "<p>The user that executed the process.</p>", "locationName": "user"}, "UserId": {"shape": "Integer", "documentation": "<p>The unique ID of the user that executed the process.</p>", "locationName": "userId"}, "Euid": {"shape": "Integer", "documentation": "<p>The effective user ID of the user that executed the process.</p>", "locationName": "euid"}, "Lineage": {"shape": "Lineage", "documentation": "<p>Information about the process's lineage.</p>", "locationName": "lineage"}}, "documentation": "<p>Information about the observed process.</p>"}, "ProcessName": {"type": "string", "max": 4096, "min": 0}, "ProcessPath": {"type": "string", "max": 4096, "min": 0}, "ProcessSha256": {"type": "string", "max": 1024, "min": 0}, "ProductCode": {"type": "structure", "members": {"Code": {"shape": "String", "documentation": "<p>The product code information.</p>", "locationName": "productCodeId"}, "ProductType": {"shape": "String", "documentation": "<p>The product code type.</p>", "locationName": "productCodeType"}}, "documentation": "<p>Contains information about the product code for the EC2 instance.</p>"}, "ProductCodes": {"type": "list", "member": {"shape": "ProductCode"}}, "ProfileSubtype": {"type": "string", "enum": ["FREQUENT", "INFREQUENT", "UNSEEN", "RARE"]}, "ProfileType": {"type": "string", "enum": ["FREQUENCY"]}, "PublicAccess": {"type": "structure", "members": {"PermissionConfiguration": {"shape": "PermissionConfiguration", "documentation": "<p>Contains information about how permissions are configured for the S3 bucket.</p>", "locationName": "permissionConfiguration"}, "EffectivePermission": {"shape": "String", "documentation": "<p>Describes the effective permission on this bucket after factoring all attached policies.</p>", "locationName": "effectivePermission"}}, "documentation": "<p>Describes the public access policies that apply to the S3 bucket.</p>"}, "PublicAccessConfiguration": {"type": "structure", "members": {"PublicAclAccess": {"shape": "PublicAccessStatus", "documentation": "<p>Indicates whether or not there is a setting that allows public access to the Amazon S3 buckets through access control lists (ACLs).</p>", "locationName": "publicAclAccess"}, "PublicPolicyAccess": {"shape": "PublicAccessStatus", "documentation": "<p>Indicates whether or not there is a setting that allows public access to the Amazon S3 bucket policy.</p>", "locationName": "publicPolicyAccess"}, "PublicAclIgnoreBehavior": {"shape": "PublicAclIgnoreBehavior", "documentation": "<p>Indicates whether or not there is a setting that ignores all public access control lists (ACLs) on the Amazon S3 bucket and the objects that it contains.</p>", "locationName": "publicAclIgnoreBehavior"}, "PublicBucketRestrictBehavior": {"shape": "PublicBucketRestrictBehavior", "documentation": "<p>Indicates whether or not there is a setting that restricts access to the bucket with specified policies.</p>", "locationName": "publicBucketRestrictBehavior"}}, "documentation": "<p>Describes public access policies that apply to the Amazon S3 bucket.</p> <p>For information about each of the following settings, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/access-control-block-public-access.html\">Blocking public access to your Amazon S3 storage</a> in the <i>Amazon S3 User Guide</i>.</p>"}, "PublicAccessStatus": {"type": "string", "enum": ["BLOCKED", "ALLOWED"]}, "PublicAclIgnoreBehavior": {"type": "string", "enum": ["IGNORED", "NOT_IGNORED"]}, "PublicBucketRestrictBehavior": {"type": "string", "enum": ["RESTRICTED", "NOT_RESTRICTED"]}, "PublishingStatus": {"type": "string", "enum": ["PENDING_VERIFICATION", "PUBLISHING", "UNABLE_TO_PUBLISH_FIX_DESTINATION_PROPERTY", "STOPPED"], "max": 300, "min": 1}, "RdsDbInstanceDetails": {"type": "structure", "members": {"DbInstanceIdentifier": {"shape": "String", "documentation": "<p>The identifier associated to the database instance that was involved in the finding.</p>", "locationName": "dbInstanceIdentifier"}, "Engine": {"shape": "String", "documentation": "<p>The database engine of the database instance involved in the finding.</p>", "locationName": "engine"}, "EngineVersion": {"shape": "String", "documentation": "<p>The version of the database engine that was involved in the finding.</p>", "locationName": "engineVersion"}, "DbClusterIdentifier": {"shape": "String", "documentation": "<p>The identifier of the database cluster that contains the database instance ID involved in the finding.</p>", "locationName": "dbClusterIdentifier"}, "DbInstanceArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) that identifies the database instance involved in the finding.</p>", "locationName": "dbInstanceArn"}, "Tags": {"shape": "Tags", "documentation": "<p>Information about the tag key-value pairs.</p>", "locationName": "tags"}}, "documentation": "<p>Contains information about the resource type <code>RDSDBInstance</code> involved in a GuardDuty finding.</p>"}, "RdsDbUserDetails": {"type": "structure", "members": {"User": {"shape": "String", "documentation": "<p>The user name used in the anomalous login attempt.</p>", "locationName": "user"}, "Application": {"shape": "String", "documentation": "<p>The application name used in the anomalous login attempt.</p>", "locationName": "application"}, "Database": {"shape": "String", "documentation": "<p>The name of the database instance involved in the anomalous login attempt.</p>", "locationName": "database"}, "Ssl": {"shape": "String", "documentation": "<p>The version of the Secure Socket Layer (SSL) used for the network.</p>", "locationName": "ssl"}, "AuthMethod": {"shape": "String", "documentation": "<p>The authentication method used by the user involved in the finding.</p>", "locationName": "authMethod"}}, "documentation": "<p>Contains information about the user and authentication details for a database instance involved in the finding.</p>"}, "RdsLimitlessDbDetails": {"type": "structure", "members": {"DbShardGroupIdentifier": {"shape": "String", "documentation": "<p>The name associated with the Limitless DB shard group.</p>", "locationName": "dbShardGroupIdentifier"}, "DbShardGroupResourceId": {"shape": "String", "documentation": "<p>The resource identifier of the DB shard group within the Limitless Database.</p>", "locationName": "dbShardGroupResourceId"}, "DbShardGroupArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) that identifies the DB shard group.</p>", "locationName": "dbShardGroupArn"}, "Engine": {"shape": "String", "documentation": "<p>The database engine of the database instance involved in the finding.</p>", "locationName": "engine"}, "EngineVersion": {"shape": "String", "documentation": "<p>The version of the database engine.</p>", "locationName": "engineVersion"}, "DbClusterIdentifier": {"shape": "String", "documentation": "<p>The name of the database cluster that is a part of the Limitless Database.</p>", "locationName": "dbClusterIdentifier"}, "Tags": {"shape": "Tags", "documentation": "<p>Information about the tag key-value pair.</p>", "locationName": "tags"}}, "documentation": "<p>Contains information about the resource type <code>RDSLimitlessDB</code> that is involved in a GuardDuty finding.</p>"}, "RdsLoginAttemptAction": {"type": "structure", "members": {"RemoteIpDetails": {"shape": "RemoteIpDetails", "locationName": "remoteIpDetails"}, "LoginAttributes": {"shape": "LoginAttributes", "documentation": "<p>Indicates the login attributes used in the login attempt.</p>"}}, "documentation": "<p>Indicates that a login attempt was made to the potentially compromised database from a remote IP address.</p>"}, "RemoteAccountDetails": {"type": "structure", "members": {"AccountId": {"shape": "String", "documentation": "<p>The Amazon Web Services account ID of the remote API caller.</p>", "locationName": "accountId"}, "Affiliated": {"shape": "Boolean", "documentation": "<p>Details on whether the Amazon Web Services account of the remote API caller is related to your GuardDuty environment. If this value is <code>True</code> the API caller is affiliated to your account in some way. If it is <code>False</code> the API caller is from outside your environment.</p>", "locationName": "affiliated"}}, "documentation": "<p>Contains details about the remote Amazon Web Services account that made the API call.</p>"}, "RemoteIpDetails": {"type": "structure", "members": {"City": {"shape": "City", "documentation": "<p>The city information of the remote IP address.</p>", "locationName": "city"}, "Country": {"shape": "Country", "documentation": "<p>The country code of the remote IP address.</p>", "locationName": "country"}, "GeoLocation": {"shape": "GeoLocation", "documentation": "<p>The location information of the remote IP address.</p>", "locationName": "geoLocation"}, "IpAddressV4": {"shape": "SensitiveString", "documentation": "<p>The IPv4 remote address of the connection.</p>", "locationName": "ipAddressV4"}, "IpAddressV6": {"shape": "SensitiveString", "documentation": "<p>The IPv6 remote address of the connection.</p>", "locationName": "ipAddressV6"}, "Organization": {"shape": "Organization", "documentation": "<p>The ISP organization information of the remote IP address.</p>", "locationName": "organization"}}, "documentation": "<p>Contains information about the remote IP address of the connection.</p>"}, "RemotePortDetails": {"type": "structure", "members": {"Port": {"shape": "Integer", "documentation": "<p>The port number of the remote connection.</p>", "locationName": "port"}, "PortName": {"shape": "String", "documentation": "<p>The port name of the remote connection.</p>", "locationName": "portName"}}, "documentation": "<p>Contains information about the remote port.</p>"}, "Resource": {"type": "structure", "members": {"AccessKeyDetails": {"shape": "AccessKeyDetails", "documentation": "<p>The IAM access key details (user information) of a user that engaged in the activity that prompted GuardDuty to generate a finding.</p>", "locationName": "accessKeyDetails"}, "S3BucketDetails": {"shape": "S3BucketDetails", "documentation": "<p>Contains information on the S3 bucket.</p>", "locationName": "s3BucketDetails"}, "InstanceDetails": {"shape": "InstanceDetails", "documentation": "<p>The information about the EC2 instance associated with the activity that prompted GuardDuty to generate a finding.</p>", "locationName": "instanceDetails"}, "EksClusterDetails": {"shape": "EksClusterDetails", "documentation": "<p>Details about the EKS cluster involved in a Kubernetes finding.</p>", "locationName": "eksClusterDetails"}, "KubernetesDetails": {"shape": "KubernetesDetails", "documentation": "<p>Details about the Kubernetes user and workload involved in a Kubernetes finding.</p>", "locationName": "kubernetesDetails"}, "ResourceType": {"shape": "String", "documentation": "<p>The type of Amazon Web Services resource.</p>", "locationName": "resourceType"}, "EbsVolumeDetails": {"shape": "EbsVolumeDetails", "documentation": "<p>Contains list of scanned and skipped EBS volumes with details.</p>", "locationName": "ebsVolumeDetails"}, "EcsClusterDetails": {"shape": "EcsClusterDetails", "documentation": "<p>Contains information about the details of the ECS Cluster.</p>", "locationName": "ecsClusterDetails"}, "ContainerDetails": {"shape": "Container", "locationName": "containerDetails"}, "RdsDbInstanceDetails": {"shape": "RdsDbInstanceDetails", "documentation": "<p>Contains information about the database instance to which an anomalous login attempt was made.</p>", "locationName": "rdsDbInstanceDetails"}, "RdsLimitlessDbDetails": {"shape": "RdsLimitlessDbDetails", "documentation": "<p>Contains information about the RDS Limitless database that was involved in a GuardDuty finding.</p>", "locationName": "rdsLimitlessDbDetails"}, "RdsDbUserDetails": {"shape": "RdsDbUserDetails", "documentation": "<p>Contains information about the user details through which anomalous login attempt was made.</p>", "locationName": "rdsDbUserDetails"}, "LambdaDetails": {"shape": "LambdaDetails", "documentation": "<p>Contains information about the Lambda function that was involved in a finding.</p>", "locationName": "lambdaDetails"}}, "documentation": "<p>Contains information about the Amazon Web Services resource associated with the activity that prompted GuardDuty to generate a finding.</p>"}, "ResourceArn": {"type": "string", "pattern": "^arn:[A-Za-z-]+:[A-Za-z0-9]+:[A-Za-z0-9-]+:\\d+:(([A-Za-z0-9-]+)[:\\/])?[A-Za-z0-9-]*$"}, "ResourceData": {"type": "structure", "members": {"S3Bucket": {"shape": "S3Bucket", "documentation": "<p>Contains information about the Amazon S3 bucket.</p>", "locationName": "s3Bucket"}, "Ec2Instance": {"shape": "Ec2Instance", "documentation": "<p>Contains information about the Amazon EC2 instance.</p>", "locationName": "ec2Instance"}, "AccessKey": {"shape": "AccessKey", "documentation": "<p>Contains information about the IAM access key details of a user that involved in the GuardDuty finding.</p>", "locationName": "accessKey"}, "Ec2NetworkInterface": {"shape": "Ec2NetworkInterface", "documentation": "<p>Contains information about the elastic network interface of the Amazon EC2 instance.</p>", "locationName": "ec2NetworkInterface"}, "S3Object": {"shape": "S3Object", "documentation": "<p>Contains information about the Amazon S3 object.</p>", "locationName": "s3Object"}, "EksCluster": {"shape": "EksCluster", "documentation": "<p>Contains detailed information about the Amazon EKS cluster associated with the activity that prompted GuardDuty to generate a finding.</p>", "locationName": "eksCluster"}, "KubernetesWorkload": {"shape": "KubernetesWorkload", "documentation": "<p>Contains detailed information about the Kubernetes workload associated with the activity that prompted GuardDuty to generate a finding.</p>", "locationName": "kubernetesWorkload"}, "Container": {"shape": "ContainerFindingResource", "documentation": "<p>Contains detailed information about the container associated with the activity that prompted GuardDuty to generate a finding.</p>", "locationName": "container"}}, "documentation": "<p>Contains information about the Amazon Web Services resource that is associated with the activity that prompted GuardDuty to generate a finding.</p>"}, "ResourceDetails": {"type": "structure", "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p>Instance ARN that was scanned in the scan entry.</p>", "locationName": "instanceArn"}}, "documentation": "<p>Represents the resources that were scanned in the scan entry.</p>"}, "ResourceList": {"type": "list", "member": {"shape": "String"}}, "ResourceNotFoundException": {"type": "structure", "members": {"Message": {"shape": "String", "documentation": "<p>The error message.</p>", "locationName": "message"}, "Type": {"shape": "String", "documentation": "<p>The error type.</p>", "locationName": "__type"}}, "documentation": "<p>The requested resource can't be found.</p>", "error": {"httpStatusCode": 404}, "exception": true}, "ResourceStatistics": {"type": "structure", "members": {"AccountId": {"shape": "String", "documentation": "<p>The ID of the Amazon Web Services account.</p>", "locationName": "accountId"}, "LastGeneratedAt": {"shape": "Timestamp", "documentation": "<p>The timestamp at which the statistics for this resource was last generated.</p>", "locationName": "lastGeneratedAt"}, "ResourceId": {"shape": "String", "documentation": "<p>ID associated with each resource. The following list provides the mapping of the resource type and resource ID.</p> <p class=\"title\"> <b>Mapping of resource and resource ID</b> </p> <ul> <li> <p>AccessKey - <code>resource.accessKeyDetails.accessKeyId</code> </p> </li> <li> <p>Container - <code>resource.containerDetails.id</code> </p> </li> <li> <p>ECSCluster - <code>resource.ecsClusterDetails.name</code> </p> </li> <li> <p>EKSCluster - <code>resource.eksClusterDetails.name</code> </p> </li> <li> <p>Instance - <code>resource.instanceDetails.instanceId</code> </p> </li> <li> <p>KubernetesCluster - <code>resource.kubernetesDetails.kubernetesWorkloadDetails.name</code> </p> </li> <li> <p>Lambda - <code>resource.lambdaDetails.functionName</code> </p> </li> <li> <p>RDSDBInstance - <code>resource.rdsDbInstanceDetails.dbInstanceIdentifier</code> </p> </li> <li> <p>S3Bucket - <code>resource.s3BucketDetails.name</code> </p> </li> <li> <p>S3Object - <code>resource.s3BucketDetails.name</code> </p> </li> </ul>", "locationName": "resourceId"}, "ResourceType": {"shape": "String", "documentation": "<p>The type of resource.</p>", "locationName": "resourceType"}, "TotalFindings": {"shape": "Integer", "documentation": "<p>The total number of findings associated with this resource.</p>", "locationName": "totalFindings"}}, "documentation": "<p>Information about each resource type associated with the <code>groupedByResource</code> statistics.</p>"}, "ResourceType": {"type": "string", "enum": ["EKS", "ECS", "EC2"]}, "ResourceUids": {"type": "list", "member": {"shape": "String"}, "max": 400}, "ResourceV2": {"type": "structure", "required": ["<PERSON><PERSON>", "ResourceType"], "members": {"Uid": {"shape": "String", "documentation": "<p>The unique identifier of the resource.</p>", "locationName": "uid"}, "Name": {"shape": "String", "documentation": "<p>The name of the resource.</p>", "locationName": "name"}, "AccountId": {"shape": "String", "documentation": "<p>The Amazon Web Services account ID to which the resource belongs.</p>", "locationName": "accountId"}, "ResourceType": {"shape": "FindingResourceType", "documentation": "<p>The type of the Amazon Web Services resource.</p>", "locationName": "resourceType"}, "Region": {"shape": "String", "documentation": "<p>The Amazon Web Services Region where the resource belongs.</p>", "locationName": "region"}, "Service": {"shape": "String", "documentation": "<p>The Amazon Web Services service of the resource.</p>", "locationName": "service"}, "CloudPartition": {"shape": "String", "documentation": "<p>The cloud partition within the Amazon Web Services Region to which the resource belongs.</p>", "locationName": "cloudPartition"}, "Tags": {"shape": "Tags", "documentation": "<p>Contains information about the tags associated with the resource.</p>", "locationName": "tags"}, "Data": {"shape": "ResourceData", "documentation": "<p>Contains information about the Amazon Web Services resource associated with the activity that prompted GuardDuty to generate a finding.</p>", "locationName": "data"}}, "documentation": "<p>Contains information about the Amazon Web Services resource that is associated with the GuardDuty finding.</p>"}, "Resources": {"type": "list", "member": {"shape": "ResourceV2"}, "max": 400}, "RuntimeContext": {"type": "structure", "members": {"ModifyingProcess": {"shape": "ProcessDetails", "documentation": "<p>Information about the process that modified the current process. This is available for multiple finding types.</p>", "locationName": "modifyingProcess"}, "ModifiedAt": {"shape": "Timestamp", "documentation": "<p>The timestamp at which the process modified the current process. The timestamp is in UTC date string format.</p>", "locationName": "modifiedAt"}, "ScriptPath": {"shape": "String", "documentation": "<p>The path to the script that was executed.</p>", "locationName": "script<PERSON>ath"}, "LibraryPath": {"shape": "String", "documentation": "<p>The path to the new library that was loaded.</p>", "locationName": "libraryPath"}, "LdPreloadValue": {"shape": "String", "documentation": "<p>The value of the LD_PRELOAD environment variable.</p>", "locationName": "ldPreloadValue"}, "SocketPath": {"shape": "String", "documentation": "<p>The path to the docket socket that was accessed.</p>", "locationName": "socketPath"}, "RuncBinaryPath": {"shape": "String", "documentation": "<p>The path to the leveraged <code>runc</code> implementation.</p>", "locationName": "runcBinary<PERSON>ath"}, "ReleaseAgentPath": {"shape": "String", "documentation": "<p>The path in the container that modified the release agent file.</p>", "locationName": "releaseAgentPath"}, "MountSource": {"shape": "String", "documentation": "<p>The path on the host that is mounted by the container.</p>", "locationName": "mountSource"}, "MountTarget": {"shape": "String", "documentation": "<p>The path in the container that is mapped to the host directory.</p>", "locationName": "mount<PERSON>ar<PERSON>"}, "FileSystemType": {"shape": "String", "documentation": "<p>Represents the type of mounted fileSystem.</p>", "locationName": "fileSystemType"}, "Flags": {"shape": "FlagsList", "documentation": "<p>Represents options that control the behavior of a runtime operation or action. For example, a filesystem mount operation may contain a read-only flag.</p>", "locationName": "flags"}, "ModuleName": {"shape": "String", "documentation": "<p>The name of the module loaded into the kernel.</p>", "locationName": "moduleName"}, "ModuleFilePath": {"shape": "String", "documentation": "<p>The path to the module loaded into the kernel.</p>", "locationName": "moduleFilePath"}, "ModuleSha256": {"shape": "String", "documentation": "<p>The <code>SHA256</code> hash of the module.</p>", "locationName": "moduleSha256"}, "ShellHistoryFilePath": {"shape": "String", "documentation": "<p>The path to the modified shell history file.</p>", "locationName": "shellHistoryFilePath"}, "TargetProcess": {"shape": "ProcessDetails", "documentation": "<p>Information about the process that had its memory overwritten by the current process.</p>", "locationName": "targetProcess"}, "AddressFamily": {"shape": "String", "documentation": "<p>Represents the communication protocol associated with the address. For example, the address family <code>AF_INET</code> is used for IP version of 4 protocol.</p>", "locationName": "addressFamily"}, "IanaProtocolNumber": {"shape": "Integer", "documentation": "<p>Specifies a particular protocol within the address family. Usually there is a single protocol in address families. For example, the address family <code>AF_INET</code> only has the IP protocol.</p>", "locationName": "ianaProtocolNumber"}, "MemoryRegions": {"shape": "MemoryRegionsList", "documentation": "<p>Specifies the Region of a process's address space such as stack and heap.</p>", "locationName": "memoryRegions"}, "ToolName": {"shape": "String", "documentation": "<p>Name of the potentially suspicious tool.</p>", "locationName": "toolName"}, "ToolCategory": {"shape": "String", "documentation": "<p>Category that the tool belongs to. Some of the examples are Backdoor Tool, Pentest Tool, Network Scanner, and Network Sniffer.</p>", "locationName": "toolCategory"}, "ServiceName": {"shape": "String", "documentation": "<p>Name of the security service that has been potentially disabled.</p>", "locationName": "serviceName"}, "CommandLineExample": {"shape": "String", "documentation": "<p>Example of the command line involved in the suspicious activity.</p>", "locationName": "commandLineExample"}, "ThreatFilePath": {"shape": "String", "documentation": "<p>The suspicious file path for which the threat intelligence details were found.</p>", "locationName": "threatFilePath"}}, "documentation": "<p>Additional information about the suspicious activity.</p>"}, "RuntimeDetails": {"type": "structure", "members": {"Process": {"shape": "ProcessDetails", "documentation": "<p>Information about the observed process.</p>", "locationName": "process"}, "Context": {"shape": "RuntimeContext", "documentation": "<p>Additional information about the suspicious activity.</p>", "locationName": "context"}}, "documentation": "<p>Information about the process and any required context values for a specific finding.</p>"}, "S3Bucket": {"type": "structure", "members": {"OwnerId": {"shape": "String", "documentation": "<p>The owner ID of the associated S3Amazon S3bucket.</p>", "locationName": "ownerId"}, "CreatedAt": {"shape": "Timestamp", "documentation": "<p>The timestamp at which the Amazon S3 bucket was created.</p>", "locationName": "createdAt"}, "EncryptionType": {"shape": "String", "documentation": "<p>The type of encryption used for the Amazon S3 buckets and its objects. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/serv-side-encryption.html\">Protecting data with server-side encryption</a> in the <i>Amazon S3 User Guide</i>.</p>", "locationName": "encryptionType"}, "EncryptionKeyArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the encryption key that is used to encrypt the Amazon S3 bucket and its objects.</p>", "locationName": "encryptionKeyArn"}, "EffectivePermission": {"shape": "String", "documentation": "<p>Describes the effective permissions on this S3 bucket, after factoring all the attached policies.</p>", "locationName": "effectivePermission"}, "PublicReadAccess": {"shape": "PublicAccessStatus", "documentation": "<p>Indicates whether or not the public read access is allowed for an Amazon S3 bucket.</p>", "locationName": "publicReadAccess"}, "PublicWriteAccess": {"shape": "PublicAccessStatus", "documentation": "<p>Indicates whether or not the public write access is allowed for an Amazon S3 bucket.</p>", "locationName": "publicWriteAccess"}, "AccountPublicAccess": {"shape": "PublicAccessConfiguration", "documentation": "<p>Contains information about the public access policies that apply to the Amazon S3 bucket at the account level.</p>", "locationName": "accountPublicAccess"}, "BucketPublicAccess": {"shape": "PublicAccessConfiguration", "documentation": "<p>Contains information about public access policies that apply to the Amazon S3 bucket.</p>", "locationName": "bucketPublicAccess"}, "S3ObjectUids": {"shape": "S3ObjectUids", "documentation": "<p>Represents a list of Amazon S3 object identifiers.</p>", "locationName": "s3ObjectUids"}}, "documentation": "<p>Contains information about the Amazon S3 bucket policies and encryption.</p>"}, "S3BucketDetail": {"type": "structure", "members": {"Arn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the S3 bucket.</p>", "locationName": "arn"}, "Name": {"shape": "String", "documentation": "<p>The name of the S3 bucket.</p>", "locationName": "name"}, "Type": {"shape": "String", "documentation": "<p>Describes whether the bucket is a source or destination bucket.</p>", "locationName": "type"}, "CreatedAt": {"shape": "Timestamp", "documentation": "<p>The date and time the bucket was created at.</p>", "locationName": "createdAt"}, "Owner": {"shape": "Owner", "documentation": "<p>The owner of the S3 bucket.</p>", "locationName": "owner"}, "Tags": {"shape": "Tags", "documentation": "<p>All tags attached to the S3 bucket</p>", "locationName": "tags"}, "DefaultServerSideEncryption": {"shape": "DefaultServerSideEncryption", "documentation": "<p>Describes the server side encryption method used in the S3 bucket.</p>", "locationName": "defaultServerSideEncryption"}, "PublicAccess": {"shape": "PublicAccess", "documentation": "<p>Describes the public access policies that apply to the S3 bucket.</p>", "locationName": "publicAccess"}, "S3ObjectDetails": {"shape": "S3ObjectDetails", "documentation": "<p>Information about the S3 object that was scanned.</p>", "locationName": "s3ObjectDetails"}}, "documentation": "<p>Contains information on the S3 bucket.</p>"}, "S3BucketDetails": {"type": "list", "member": {"shape": "S3BucketDetail"}}, "S3LogsConfiguration": {"type": "structure", "required": ["Enable"], "members": {"Enable": {"shape": "Boolean", "documentation": "<p> The status of S3 data event logs as a data source.</p>", "locationName": "enable"}}, "documentation": "<p>Describes whether S3 data event logs will be enabled as a data source.</p>"}, "S3LogsConfigurationResult": {"type": "structure", "required": ["Status"], "members": {"Status": {"shape": "DataSourceStatus", "documentation": "<p>A value that describes whether S3 data event logs are automatically enabled for new members of the organization.</p>", "locationName": "status"}}, "documentation": "<p>Describes whether S3 data event logs will be enabled as a data source.</p>"}, "S3Object": {"type": "structure", "members": {"ETag": {"shape": "String", "documentation": "<p>The entity tag is a hash of the Amazon S3 object. The ETag reflects changes only to the contents of an object, and not its metadata.</p>", "locationName": "eTag"}, "Key": {"shape": "String", "documentation": "<p>The key of the Amazon S3 object.</p>", "locationName": "key"}, "VersionId": {"shape": "String", "documentation": "<p>The version Id of the Amazon S3 object.</p>", "locationName": "versionId"}}, "documentation": "<p>Contains information about the Amazon S3 object.</p>"}, "S3ObjectDetail": {"type": "structure", "members": {"ObjectArn": {"shape": "String", "documentation": "<p>Amazon Resource Name (ARN) of the S3 object.</p>", "locationName": "objectArn"}, "Key": {"shape": "String", "documentation": "<p>Key of the S3 object.</p>", "locationName": "key"}, "ETag": {"shape": "String", "documentation": "<p>The entity tag is a hash of the S3 object. The ETag reflects changes only to the contents of an object, and not its metadata.</p>", "locationName": "eTag"}, "Hash": {"shape": "String", "documentation": "<p>Hash of the threat detected in this finding.</p>", "locationName": "hash"}, "VersionId": {"shape": "String", "documentation": "<p>Version ID of the object.</p>", "locationName": "versionId"}}, "documentation": "<p>Information about the S3 object that was scanned</p>"}, "S3ObjectDetails": {"type": "list", "member": {"shape": "S3ObjectDetail"}}, "S3ObjectUids": {"type": "list", "member": {"shape": "String"}}, "Scan": {"type": "structure", "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The unique ID of the detector that is associated with the request.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "locationName": "detectorId"}, "AdminDetectorId": {"shape": "DetectorId", "documentation": "<p>The unique detector ID of the administrator account that the request is associated with. If the account is an administrator, the <code>AdminDetectorId</code> will be the same as the one used for <code>DetectorId</code>.</p> <p>To find the <code>detectorId</code> in the current Region, see the <PERSON><PERSON><PERSON> page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "locationName": "adminDetectorId"}, "ScanId": {"shape": "NonEmptyString", "documentation": "<p>The unique scan ID associated with a scan entry.</p>", "locationName": "scanId"}, "ScanStatus": {"shape": "ScanStatus", "documentation": "<p>An enum value representing possible scan statuses.</p>", "locationName": "scanStatus"}, "FailureReason": {"shape": "NonEmptyString", "documentation": "<p>Represents the reason for <code>FAILED</code> scan status.</p>", "locationName": "failureReason"}, "ScanStartTime": {"shape": "Timestamp", "documentation": "<p>The timestamp of when the scan was triggered.</p>", "locationName": "scanStartTime"}, "ScanEndTime": {"shape": "Timestamp", "documentation": "<p>The timestamp of when the scan was finished.</p>", "locationName": "scanEndTime"}, "TriggerDetails": {"shape": "TriggerDetails", "documentation": "<p>Specifies the reason why the scan was initiated.</p>", "locationName": "triggerDetails"}, "ResourceDetails": {"shape": "ResourceDetails", "documentation": "<p>Represents the resources that were scanned in the scan entry.</p>", "locationName": "resourceDetails"}, "ScanResultDetails": {"shape": "ScanResultDetails", "documentation": "<p>Represents the result of the scan.</p>", "locationName": "scanResultDetails"}, "AccountId": {"shape": "AccountId", "documentation": "<p>The ID for the account that belongs to the scan.</p>", "locationName": "accountId"}, "TotalBytes": {"shape": "PositiveLong", "documentation": "<p>Represents total bytes that were scanned.</p>", "locationName": "totalBytes"}, "FileCount": {"shape": "PositiveLong", "documentation": "<p>Represents the number of files that were scanned.</p>", "locationName": "fileCount"}, "AttachedVolumes": {"shape": "VolumeDetails", "documentation": "<p>List of volumes that were attached to the original instance to be scanned.</p>", "locationName": "attachedVolumes"}, "ScanType": {"shape": "ScanType", "documentation": "<p>Specifies the scan type that invoked the malware scan.</p>", "locationName": "scanType"}}, "documentation": "<p>Contains information about malware scans associated with GuardDuty Malware Protection for EC2.</p>"}, "ScanCondition": {"type": "structure", "required": ["MapEquals"], "members": {"MapEquals": {"shape": "MapEquals", "documentation": "<p>Represents an <i>mapEqual</i> <b/> condition to be applied to a single field when triggering for malware scan.</p>", "locationName": "mapEquals"}}, "documentation": "<p>Contains information about the condition.</p>"}, "ScanConditionPair": {"type": "structure", "required": ["Key"], "members": {"Key": {"shape": "TagKey", "documentation": "<p>Represents the <b>key</b> in the map condition.</p>", "locationName": "key"}, "Value": {"shape": "TagValue", "documentation": "<p>Represents optional <b>value</b> in the map condition. If not specified, only the <b>key</b> will be matched.</p>", "locationName": "value"}}, "documentation": "<p>Represents the <code>key:value</code> pair to be matched against given resource property.</p>"}, "ScanCriterion": {"type": "map", "key": {"shape": "ScanCriterionKey"}, "value": {"shape": "ScanCondition"}, "documentation": "<p>Represents a map of resource properties that match specified conditions and values when triggering malware scans.</p>"}, "ScanCriterionKey": {"type": "string", "documentation": "<p>An enum value representing possible resource properties to match with given scan condition.</p>", "enum": ["EC2_INSTANCE_TAG"]}, "ScanDetections": {"type": "structure", "members": {"ScannedItemCount": {"shape": "ScannedItemCount", "documentation": "<p>Total number of scanned files.</p>", "locationName": "scannedItemCount"}, "ThreatsDetectedItemCount": {"shape": "ThreatsDetectedItemCount", "documentation": "<p>Total number of infected files.</p>", "locationName": "threatsDetectedItemCount"}, "HighestSeverityThreatDetails": {"shape": "HighestSeverityThreatDetails", "documentation": "<p>Details of the highest severity threat detected during malware scan and number of infected files.</p>", "locationName": "highestSeverityThreatDetails"}, "ThreatDetectedByName": {"shape": "ThreatDetectedByName", "documentation": "<p>Contains details about identified threats organized by threat name.</p>", "locationName": "threatDetectedByName"}}, "documentation": "<p>Contains a complete view providing malware scan result details.</p>"}, "ScanEc2InstanceWithFindings": {"type": "structure", "members": {"EbsVolumes": {"shape": "Boolean", "documentation": "<p>Describes the configuration for scanning EBS volumes as data source.</p>", "locationName": "ebsVolumes"}}, "documentation": "<p>Describes whether Malware Protection for EC2 instances with findings will be enabled as a data source.</p>"}, "ScanEc2InstanceWithFindingsResult": {"type": "structure", "members": {"EbsVolumes": {"shape": "EbsVolumesResult", "documentation": "<p>Describes the configuration of scanning EBS volumes as a data source.</p>", "locationName": "ebsVolumes"}}, "documentation": "<p>An object that contains information on the status of whether Malware Protection for EC2 instances with findings will be enabled as a data source.</p>"}, "ScanFilePath": {"type": "structure", "members": {"FilePath": {"shape": "String", "documentation": "<p>The file path of the infected file.</p>", "locationName": "filePath"}, "VolumeArn": {"shape": "String", "documentation": "<p>EBS volume ARN details of the infected file.</p>", "locationName": "volumeArn"}, "Hash": {"shape": "String", "documentation": "<p>The hash value of the infected file.</p>", "locationName": "hash"}, "FileName": {"shape": "String", "documentation": "<p>File name of the infected file.</p>", "locationName": "fileName"}}, "documentation": "<p>Contains details of infected file including name, file path and hash.</p>"}, "ScanResourceCriteria": {"type": "structure", "members": {"Include": {"shape": "ScanCriterion", "documentation": "<p>Represents condition that when matched will allow a malware scan for a certain resource.</p>", "locationName": "include"}, "Exclude": {"shape": "ScanCriterion", "documentation": "<p>Represents condition that when matched will prevent a malware scan for a certain resource.</p>", "locationName": "exclude"}}, "documentation": "<p>Contains information about criteria used to filter resources before triggering malware scan.</p>"}, "ScanResult": {"type": "string", "enum": ["CLEAN", "INFECTED"]}, "ScanResultDetails": {"type": "structure", "members": {"ScanResult": {"shape": "ScanResult", "documentation": "<p>An enum value representing possible scan results.</p>", "locationName": "scanResult"}}, "documentation": "<p>Represents the result of the scan.</p>"}, "ScanStatus": {"type": "string", "enum": ["RUNNING", "COMPLETED", "FAILED", "SKIPPED"]}, "ScanThreatName": {"type": "structure", "members": {"Name": {"shape": "String", "documentation": "<p>The name of the identified threat.</p>", "locationName": "name"}, "Severity": {"shape": "String", "documentation": "<p>Severity of threat identified as part of the malware scan.</p>", "locationName": "severity"}, "ItemCount": {"shape": "Integer", "documentation": "<p>Total number of files infected with given threat.</p>", "locationName": "itemCount"}, "FilePaths": {"shape": "FilePaths", "documentation": "<p>List of infected files in EBS volume with details.</p>", "locationName": "filePaths"}}, "documentation": "<p>Contains files infected with the given threat providing details of malware name and severity.</p>"}, "ScanThreatNames": {"type": "list", "member": {"shape": "ScanThreatName"}}, "ScanType": {"type": "string", "enum": ["GUARDDUTY_INITIATED", "ON_DEMAND"]}, "ScannedItemCount": {"type": "structure", "members": {"TotalGb": {"shape": "Integer", "documentation": "<p>Total GB of files scanned for malware.</p>", "locationName": "totalGb"}, "Files": {"shape": "Integer", "documentation": "<p>Number of files scanned.</p>", "locationName": "files"}, "Volumes": {"shape": "Integer", "documentation": "<p>Total number of scanned volumes.</p>", "locationName": "volumes"}}, "documentation": "<p>Total number of scanned files.</p>"}, "Scans": {"type": "list", "member": {"shape": "<PERSON><PERSON>"}}, "SecurityContext": {"type": "structure", "members": {"Privileged": {"shape": "Boolean", "documentation": "<p>Whether the container is privileged.</p>", "locationName": "privileged"}, "AllowPrivilegeEscalation": {"shape": "Boolean", "documentation": "<p>Whether or not a container or a Kubernetes pod is allowed to gain more privileges than its parent process.</p>", "locationName": "allowPrivilegeEscalation"}}, "documentation": "<p>Container security context.</p>"}, "SecurityGroup": {"type": "structure", "members": {"GroupId": {"shape": "String", "documentation": "<p>The security group ID of the EC2 instance.</p>", "locationName": "groupId"}, "GroupName": {"shape": "String", "documentation": "<p>The security group name of the EC2 instance.</p>", "locationName": "groupName"}}, "documentation": "<p>Contains information about the security groups associated with the EC2 instance.</p>"}, "SecurityGroups": {"type": "list", "member": {"shape": "SecurityGroup"}}, "SensitiveString": {"type": "string", "sensitive": true}, "Sequence": {"type": "structure", "required": ["<PERSON><PERSON>", "Description", "Signals"], "members": {"Uid": {"shape": "String", "documentation": "<p>Unique identifier of the attack sequence.</p>", "locationName": "uid"}, "Description": {"shape": "SequenceDescription", "documentation": "<p>Description of the attack sequence.</p>", "locationName": "description"}, "Actors": {"shape": "Actors", "documentation": "<p>Contains information about the actors involved in the attack sequence.</p>", "locationName": "actors"}, "Resources": {"shape": "Resources", "documentation": "<p>Contains information about the resources involved in the attack sequence.</p>", "locationName": "resources"}, "Endpoints": {"shape": "NetworkEndpoints", "documentation": "<p>Contains information about the network endpoints that were used in the attack sequence.</p>", "locationName": "endpoints"}, "Signals": {"shape": "Signals", "documentation": "<p>Contains information about the signals involved in the attack sequence.</p>", "locationName": "signals"}, "SequenceIndicators": {"shape": "Indicators", "documentation": "<p>Contains information about the indicators observed in the attack sequence.</p>", "locationName": "sequenceIndicators"}, "AdditionalSequenceTypes": {"shape": "AdditionalSequenceTypes", "documentation": "<p>Additional types of sequences that may be associated with the attack sequence finding, providing further context about the nature of the detected threat.</p>", "locationName": "additionalSequenceTypes"}}, "documentation": "<p>Contains information about the GuardDuty attack sequence finding.</p>"}, "SequenceDescription": {"type": "string", "max": 4096}, "Service": {"type": "structure", "members": {"Action": {"shape": "Action", "documentation": "<p>Information about the activity that is described in a finding.</p>", "locationName": "action"}, "Evidence": {"shape": "Evidence", "documentation": "<p>An evidence object associated with the service.</p>", "locationName": "evidence"}, "Archived": {"shape": "Boolean", "documentation": "<p>Indicates whether this finding is archived.</p>", "locationName": "archived"}, "Count": {"shape": "Integer", "documentation": "<p>The total count of the occurrences of this finding type.</p>", "locationName": "count"}, "DetectorId": {"shape": "DetectorId", "documentation": "<p>The detector ID for the GuardDuty service.</p>", "locationName": "detectorId"}, "EventFirstSeen": {"shape": "String", "documentation": "<p>The first-seen timestamp of the activity that prompted GuardDuty to generate this finding.</p>", "locationName": "eventFirstSeen"}, "EventLastSeen": {"shape": "String", "documentation": "<p>The last-seen timestamp of the activity that prompted GuardDuty to generate this finding.</p>", "locationName": "eventLastSeen"}, "ResourceRole": {"shape": "String", "documentation": "<p>The resource role information for this finding.</p>", "locationName": "resourceRole"}, "ServiceName": {"shape": "String", "documentation": "<p>The name of the Amazon Web Services service (GuardDuty) that generated a finding.</p>", "locationName": "serviceName"}, "UserFeedback": {"shape": "String", "documentation": "<p>Feedback that was submitted about the finding.</p>", "locationName": "userFeedback"}, "AdditionalInfo": {"shape": "ServiceAdditionalInfo", "documentation": "<p>Contains additional information about the generated finding.</p>", "locationName": "additionalInfo"}, "FeatureName": {"shape": "String", "documentation": "<p>The name of the feature that generated a finding.</p>", "locationName": "featureName"}, "EbsVolumeScanDetails": {"shape": "EbsVolumeScanDetails", "documentation": "<p>Returns details from the malware scan that created a finding.</p>", "locationName": "ebsVolumeScanDetails"}, "RuntimeDetails": {"shape": "RuntimeDetails", "documentation": "<p>Information about the process and any required context values for a specific finding</p>", "locationName": "runtimeDetails"}, "Detection": {"shape": "Detection", "documentation": "<p>Contains information about the detected unusual behavior.</p>", "locationName": "detection"}, "MalwareScanDetails": {"shape": "MalwareScanDetails", "documentation": "<p>Returns details from the malware scan that generated a GuardDuty finding.</p>", "locationName": "malwareScanDetails"}}, "documentation": "<p>Contains additional information about the generated finding.</p>"}, "ServiceAdditionalInfo": {"type": "structure", "members": {"Value": {"shape": "String", "documentation": "<p>This field specifies the value of the additional information.</p>", "locationName": "value"}, "Type": {"shape": "String", "documentation": "<p>Describes the type of the additional information.</p>", "locationName": "type"}}, "documentation": "<p>Additional information about the generated finding.</p>"}, "Session": {"type": "structure", "members": {"Uid": {"shape": "String", "documentation": "<p>The unique identifier of the session.</p>", "locationName": "uid"}, "MfaStatus": {"shape": "MfaStatus", "documentation": "<p>Indicates whether or not multi-factor authencation (MFA) was used during authentication.</p> <p>In Amazon Web Services CloudTrail, you can find this value as <code>userIdentity.sessionContext.attributes.mfaAuthenticated</code>.</p>", "locationName": "mfaStatus"}, "CreatedTime": {"shape": "Timestamp", "documentation": "<p>The timestamp for when the session was created.</p> <p>In Amazon Web Services CloudTrail, you can find this value as <code>userIdentity.sessionContext.attributes.creationDate</code>.</p>", "locationName": "createdTime"}, "Issuer": {"shape": "String", "documentation": "<p>Identifier of the session issuer.</p> <p>In Amazon Web Services CloudTrail, you can find this value as <code>userIdentity.sessionContext.sessionIssuer.arn</code>.</p>", "locationName": "issuer"}}, "documentation": "<p>Contains information about the authenticated session.</p>"}, "SessionNameList": {"type": "list", "member": {"shape": "String"}}, "SeverityStatistics": {"type": "structure", "members": {"LastGeneratedAt": {"shape": "Timestamp", "documentation": "<p>The timestamp at which a finding type for a specific severity was last generated.</p>", "locationName": "lastGeneratedAt"}, "Severity": {"shape": "Double", "documentation": "<p>The severity level associated with each finding type.</p>", "locationName": "severity"}, "TotalFindings": {"shape": "Integer", "documentation": "<p>The total number of findings associated with this severity.</p>", "locationName": "totalFindings"}}, "documentation": "<p>Information about severity level for each finding type.</p>"}, "Signal": {"type": "structure", "required": ["<PERSON><PERSON>", "Type", "Name", "CreatedAt", "UpdatedAt", "FirstSeenAt", "LastSeenAt", "Count"], "members": {"Uid": {"shape": "String", "documentation": "<p>The unique identifier of the signal.</p>", "locationName": "uid"}, "Type": {"shape": "SignalType", "documentation": "<p>The type of the signal used to identify an attack sequence.</p> <p>Signals can be GuardDuty findings or activities observed in data sources that GuardDuty monitors. For more information, see <a href=\"https://docs.aws.amazon.com/guardduty/latest/ug/guardduty_data-sources.html\">Foundational data sources</a> in the <i>Amazon GuardDuty User Guide</i>.</p> <p>A signal type can be one of the valid values listed in this API. Here are the related descriptions:</p> <ul> <li> <p> <code>FINDING</code> - Individually generated GuardDuty finding.</p> </li> <li> <p> <code>CLOUD_TRAIL</code> - Activity observed from CloudTrail logs</p> </li> <li> <p> <code>S3_DATA_EVENTS</code> - Activity observed from CloudTrail data events for S3. Activities associated with this type will show up only when you have enabled GuardDuty S3 Protection feature in your account. For more information about S3 Protection and steps to enable it, see <a href=\"https://docs.aws.amazon.com/guardduty/latest/ug/s3-protection.html\">S3 Protection</a> in the <i>Amazon GuardDuty User Guide</i>.</p> </li> </ul>", "locationName": "type"}, "Description": {"shape": "SignalDescription", "documentation": "<p>The description of the signal.</p>", "locationName": "description"}, "Name": {"shape": "String", "documentation": "<p>The name of the signal. For example, when signal type is <code>FINDING</code>, the signal name is the name of the finding.</p>", "locationName": "name"}, "CreatedAt": {"shape": "Timestamp", "documentation": "<p>The timestamp when the first finding or activity related to this signal was observed.</p>", "locationName": "createdAt"}, "UpdatedAt": {"shape": "Timestamp", "documentation": "<p>The timestamp when this signal was last observed.</p>", "locationName": "updatedAt"}, "FirstSeenAt": {"shape": "Timestamp", "documentation": "<p>The timestamp when the first finding or activity related to this signal was observed.</p>", "locationName": "firstSeenAt"}, "LastSeenAt": {"shape": "Timestamp", "documentation": "<p>The timestamp when the last finding or activity related to this signal was observed.</p>", "locationName": "lastSeenAt"}, "Severity": {"shape": "Double", "documentation": "<p>The severity associated with the signal. For more information about severity, see <a href=\"https://docs.aws.amazon.com/guardduty/latest/ug/guardduty_findings-severity.html\">Findings severity levels</a> in the <i>Amazon GuardDuty User Guide</i>.</p>", "locationName": "severity"}, "Count": {"shape": "Integer", "documentation": "<p>The number of times this signal was observed.</p>", "locationName": "count"}, "ResourceUids": {"shape": "ResourceUids", "documentation": "<p>Information about the unique identifiers of the resources involved in the signal.</p>", "locationName": "resourceUids"}, "ActorIds": {"shape": "ActorIds", "documentation": "<p>Information about the IDs of the threat actors involved in the signal.</p>", "locationName": "actorIds"}, "EndpointIds": {"shape": "EndpointIds", "documentation": "<p>Information about the endpoint IDs associated with this signal.</p>", "locationName": "endpointIds"}, "SignalIndicators": {"shape": "Indicators", "documentation": "<p>Contains information about the indicators associated with the signals.</p>", "locationName": "signalIndicators"}}, "documentation": "<p>Contains information about the signals involved in the attack sequence.</p>"}, "SignalDescription": {"type": "string", "max": 2000}, "SignalType": {"type": "string", "enum": ["FINDING", "CLOUD_TRAIL", "S3_DATA_EVENTS", "EKS_AUDIT_LOGS", "FLOW_LOGS", "DNS_LOGS", "RUNTIME_MONITORING"]}, "Signals": {"type": "list", "member": {"shape": "Signal"}, "max": 100, "min": 1}, "SortCriteria": {"type": "structure", "members": {"AttributeName": {"shape": "String", "documentation": "<p>Represents the finding attribute, such as <code>accountId</code>, that sorts the findings.</p>", "locationName": "attributeName"}, "OrderBy": {"shape": "OrderBy", "documentation": "<p>The order by which the sorted findings are to be displayed.</p>", "locationName": "orderBy"}}, "documentation": "<p>Contains information about the criteria used for sorting findings.</p>"}, "SourceIps": {"type": "list", "member": {"shape": "String"}}, "Sources": {"type": "list", "member": {"shape": "String"}}, "StartMalwareScanRequest": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "ResourceArn", "documentation": "<p>Amazon Resource Name (ARN) of the resource for which you invoked the API.</p>", "locationName": "resourceArn"}}}, "StartMalwareScanResponse": {"type": "structure", "members": {"ScanId": {"shape": "NonEmptyString", "documentation": "<p>A unique identifier that gets generated when you invoke the API without any error. Each malware scan has a corresponding scan ID. Using this scan ID, you can monitor the status of your malware scan.</p>", "locationName": "scanId"}}}, "StartMonitoringMembersRequest": {"type": "structure", "required": ["DetectorId", "AccountIds"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The unique ID of the detector of the GuardDuty administrator account associated with the member accounts to monitor.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}, "AccountIds": {"shape": "AccountIds", "documentation": "<p>A list of account IDs of the GuardDuty member accounts to start monitoring.</p>", "locationName": "accountIds"}}}, "StartMonitoringMembersResponse": {"type": "structure", "required": ["UnprocessedAccounts"], "members": {"UnprocessedAccounts": {"shape": "UnprocessedAccounts", "documentation": "<p>A list of objects that contain the unprocessed account and a result string that explains why it was unprocessed.</p>", "locationName": "unprocessedAccounts"}}}, "StopMonitoringMembersRequest": {"type": "structure", "required": ["DetectorId", "AccountIds"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The unique ID of the detector associated with the GuardDuty administrator account that is monitoring member accounts.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}, "AccountIds": {"shape": "AccountIds", "documentation": "<p>A list of account IDs for the member accounts to stop monitoring.</p>", "locationName": "accountIds"}}}, "StopMonitoringMembersResponse": {"type": "structure", "required": ["UnprocessedAccounts"], "members": {"UnprocessedAccounts": {"shape": "UnprocessedAccounts", "documentation": "<p>A list of objects that contain an accountId for each account that could not be processed, and a result string that indicates why the account was not processed. </p>", "locationName": "unprocessedAccounts"}}}, "String": {"type": "string"}, "SubnetIds": {"type": "list", "member": {"shape": "String"}}, "Tag": {"type": "structure", "members": {"Key": {"shape": "String", "documentation": "<p>Describes the key associated with the tag.</p>", "locationName": "key"}, "Value": {"shape": "String", "documentation": "<p>Describes the value associated with the tag key.</p>", "locationName": "value"}}, "documentation": "<p>Contains information about a tag key-value pair.</p>"}, "TagKey": {"type": "string", "max": 128, "min": 1, "pattern": "^(?!aws:)[a-zA-Z+-=._:/]+$"}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 200, "min": 1}, "TagMap": {"type": "map", "key": {"shape": "TagKey"}, "value": {"shape": "TagValue"}, "max": 200, "min": 1}, "TagResourceRequest": {"type": "structure", "required": ["ResourceArn", "Tags"], "members": {"ResourceArn": {"shape": "GuardDutyArn", "documentation": "<p>The Amazon Resource Name (ARN) for the GuardDuty resource to apply a tag to.</p>", "location": "uri", "locationName": "resourceArn"}, "Tags": {"shape": "TagMap", "documentation": "<p>The tags to be added to a resource.</p>", "locationName": "tags"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256}, "Tags": {"type": "list", "member": {"shape": "Tag"}}, "Threat": {"type": "structure", "members": {"Name": {"shape": "String", "documentation": "<p>Name of the detected threat that caused GuardDuty to generate this finding.</p>", "locationName": "name"}, "Source": {"shape": "String", "documentation": "<p>Source of the threat that generated this finding.</p>", "locationName": "source"}, "ItemPaths": {"shape": "ItemPaths", "documentation": "<p>Information about the nested item path and hash of the protected resource.</p>", "locationName": "itemPaths"}}, "documentation": "<p>Information about the detected threats associated with the generated finding.</p>"}, "ThreatDetectedByName": {"type": "structure", "members": {"ItemCount": {"shape": "Integer", "documentation": "<p>Total number of infected files identified.</p>", "locationName": "itemCount"}, "UniqueThreatNameCount": {"shape": "Integer", "documentation": "<p>Total number of unique threats by name identified, as part of the malware scan.</p>", "locationName": "uniqueThreatNameCount"}, "Shortened": {"shape": "Boolean", "documentation": "<p>Flag to determine if the finding contains every single infected file-path and/or every threat.</p>", "locationName": "shortened"}, "ThreatNames": {"shape": "ScanThreatNames", "documentation": "<p>List of identified threats with details, organized by threat name.</p>", "locationName": "threatNames"}}, "documentation": "<p>Contains details about identified threats organized by threat name.</p>"}, "ThreatIntelSetFormat": {"type": "string", "enum": ["TXT", "STIX", "OTX_CSV", "ALIEN_VAULT", "PROOF_POINT", "FIRE_EYE"], "max": 300, "min": 1}, "ThreatIntelSetIds": {"type": "list", "member": {"shape": "String"}, "max": 50, "min": 0}, "ThreatIntelSetStatus": {"type": "string", "enum": ["INACTIVE", "ACTIVATING", "ACTIVE", "DEACTIVATING", "ERROR", "DELETE_PENDING", "DELETED"], "max": 300, "min": 1}, "ThreatIntelligenceDetail": {"type": "structure", "members": {"ThreatListName": {"shape": "String", "documentation": "<p>The name of the threat intelligence list that triggered the finding.</p>", "locationName": "threatListName"}, "ThreatNames": {"shape": "ThreatNames", "documentation": "<p>A list of names of the threats in the threat intelligence list that triggered the finding.</p>", "locationName": "threatNames"}, "ThreatFileSha256": {"shape": "String", "documentation": "<p>SHA256 of the file that generated the finding.</p>", "locationName": "threatFileSha256"}}, "documentation": "<p>An instance of a threat intelligence detail that constitutes evidence for the finding.</p>"}, "ThreatIntelligenceDetails": {"type": "list", "member": {"shape": "ThreatIntelligenceDetail"}}, "ThreatNames": {"type": "list", "member": {"shape": "String"}}, "Threats": {"type": "list", "member": {"shape": "Threat"}}, "ThreatsDetectedItemCount": {"type": "structure", "members": {"Files": {"shape": "Integer", "documentation": "<p>Total number of infected files.</p>", "locationName": "files"}}, "documentation": "<p>Contains total number of infected files.</p>"}, "Timestamp": {"type": "timestamp"}, "Total": {"type": "structure", "members": {"Amount": {"shape": "String", "documentation": "<p>The total usage.</p>", "locationName": "amount"}, "Unit": {"shape": "String", "documentation": "<p>The currency unit that the amount is given in.</p>", "locationName": "unit"}}, "documentation": "<p>Contains the total usage with the corresponding currency unit for that value.</p>"}, "TriggerDetails": {"type": "structure", "members": {"GuardDutyFindingId": {"shape": "NonEmptyString", "documentation": "<p>The ID of the GuardDuty finding that triggered the malware scan.</p>", "locationName": "guardDutyFindingId"}, "Description": {"shape": "NonEmptyString", "documentation": "<p>The description of the scan trigger.</p>", "locationName": "description"}}, "documentation": "<p>Represents the reason the scan was triggered.</p>"}, "UnarchiveFindingsRequest": {"type": "structure", "required": ["DetectorId", "FindingIds"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The ID of the detector associated with the findings to unarchive.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}, "FindingIds": {"shape": "FindingIds", "documentation": "<p>The IDs of the findings to unarchive.</p>", "locationName": "findingIds"}}}, "UnarchiveFindingsResponse": {"type": "structure", "members": {}}, "UnprocessedAccount": {"type": "structure", "required": ["AccountId", "Result"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID.</p>", "locationName": "accountId"}, "Result": {"shape": "String", "documentation": "<p>A reason why the account hasn't been processed.</p>", "locationName": "result"}}, "documentation": "<p>Contains information about the accounts that weren't processed.</p>"}, "UnprocessedAccounts": {"type": "list", "member": {"shape": "UnprocessedAccount"}, "max": 50, "min": 0}, "UnprocessedDataSourcesResult": {"type": "structure", "members": {"MalwareProtection": {"shape": "MalwareProtectionConfigurationResult", "locationName": "malwareProtection"}}, "documentation": "<p>Specifies the names of the data sources that couldn't be enabled.</p>"}, "UntagResourceRequest": {"type": "structure", "required": ["ResourceArn", "TagKeys"], "members": {"ResourceArn": {"shape": "GuardDutyArn", "documentation": "<p>The Amazon Resource Name (ARN) for the resource to remove tags from.</p>", "location": "uri", "locationName": "resourceArn"}, "TagKeys": {"shape": "TagKeyList", "documentation": "<p>The tag keys to remove from the resource.</p>", "location": "querystring", "locationName": "tagKeys"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateDetectorRequest": {"type": "structure", "required": ["DetectorId"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The unique ID of the detector to update.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}, "Enable": {"shape": "Boolean", "documentation": "<p>Specifies whether the detector is enabled or not enabled.</p>", "locationName": "enable"}, "FindingPublishingFrequency": {"shape": "FindingPublishingFrequency", "documentation": "<p>An enum value that specifies how frequently findings are exported, such as to CloudWatch Events.</p>", "locationName": "findingPublishingFrequency"}, "DataSources": {"shape": "DataSourceConfigurations", "documentation": "<p>Describes which data sources will be updated.</p> <p>There might be regional differences because some data sources might not be available in all the Amazon Web Services Regions where GuardDuty is presently supported. For more information, see <a href=\"https://docs.aws.amazon.com/guardduty/latest/ug/guardduty_regions.html\">Regions and endpoints</a>.</p>", "deprecated": true, "deprecatedMessage": "This parameter is deprecated, use Features instead", "locationName": "dataSources"}, "Features": {"shape": "DetectorFeatureConfigurations", "documentation": "<p>Provides the features that will be updated for the detector.</p>", "locationName": "features"}}}, "UpdateDetectorResponse": {"type": "structure", "members": {}}, "UpdateFilterRequest": {"type": "structure", "required": ["DetectorId", "<PERSON><PERSON><PERSON><PERSON>"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The unique ID of the detector that specifies the GuardDuty service where you want to update a filter.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}, "FilterName": {"shape": "String", "documentation": "<p>The name of the filter.</p>", "location": "uri", "locationName": "filterName"}, "Description": {"shape": "FilterDescription", "documentation": "<p>The description of the filter. Valid characters include alphanumeric characters, and special characters such as hyphen, period, colon, underscore, parentheses (<code>{ }</code>, <code>[ ]</code>, and <code>( )</code>), forward slash, horizontal tab, vertical tab, newline, form feed, return, and whitespace.</p>", "locationName": "description"}, "Action": {"shape": "FilterAction", "documentation": "<p>Specifies the action that is to be applied to the findings that match the filter.</p>", "locationName": "action"}, "Rank": {"shape": "FilterRank", "documentation": "<p>Specifies the position of the filter in the list of current filters. Also specifies the order in which this filter is applied to the findings.</p>", "locationName": "rank"}, "FindingCriteria": {"shape": "FindingCriteria", "documentation": "<p>Represents the criteria to be used in the filter for querying findings.</p>", "locationName": "findingCriteria"}}}, "UpdateFilterResponse": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The name of the filter.</p>", "locationName": "name"}}}, "UpdateFindingsFeedbackRequest": {"type": "structure", "required": ["DetectorId", "FindingIds", "<PERSON><PERSON><PERSON>"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The ID of the detector that is associated with the findings for which you want to update the feedback.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}, "FindingIds": {"shape": "FindingIds", "documentation": "<p>The IDs of the findings that you want to mark as useful or not useful.</p>", "locationName": "findingIds"}, "Feedback": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>The feedback for the finding.</p>", "locationName": "feedback"}, "Comments": {"shape": "String", "documentation": "<p>Additional feedback about the GuardDuty findings.</p>", "locationName": "comments"}}}, "UpdateFindingsFeedbackResponse": {"type": "structure", "members": {}}, "UpdateIPSetRequest": {"type": "structure", "required": ["DetectorId", "IpSetId"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The detectorID that specifies the GuardDuty service whose IPSet you want to update.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}, "IpSetId": {"shape": "String", "documentation": "<p>The unique ID that specifies the IPSet that you want to update.</p>", "location": "uri", "locationName": "ipSetId"}, "Name": {"shape": "Name", "documentation": "<p>The unique ID that specifies the IPSet that you want to update.</p>", "locationName": "name"}, "Location": {"shape": "Location", "documentation": "<p>The updated URI of the file that contains the IPSet. </p>", "locationName": "location"}, "Activate": {"shape": "Boolean", "documentation": "<p>The updated Boolean value that specifies whether the IPSet is active or not.</p>", "locationName": "activate"}}}, "UpdateIPSetResponse": {"type": "structure", "members": {}}, "UpdateMalwareProtectionPlanRequest": {"type": "structure", "required": ["MalwareProtectionPlanId"], "members": {"MalwareProtectionPlanId": {"shape": "String", "documentation": "<p>A unique identifier associated with the Malware Protection plan.</p>", "location": "uri", "locationName": "malwareProtectionPlanId"}, "Role": {"shape": "String", "documentation": "<p>Amazon Resource Name (ARN) of the IAM role with permissions to scan and add tags to the associated protected resource.</p>", "locationName": "role"}, "Actions": {"shape": "MalwareProtectionPlanActions", "documentation": "<p>Information about whether the tags will be added to the S3 object after scanning.</p>", "locationName": "actions"}, "ProtectedResource": {"shape": "UpdateProtectedResource", "documentation": "<p>Information about the protected resource that is associated with the created Malware Protection plan. Presently, <code>S3Bucket</code> is the only supported protected resource.</p>", "locationName": "protectedResource"}}}, "UpdateMalwareScanSettingsRequest": {"type": "structure", "required": ["DetectorId"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The unique ID of the detector that specifies the GuardDuty service where you want to update scan settings.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}, "ScanResourceCriteria": {"shape": "ScanResourceCriteria", "documentation": "<p>Represents the criteria to be used in the filter for selecting resources to scan.</p>", "locationName": "scanResourceCriteria"}, "EbsSnapshotPreservation": {"shape": "EbsSnapshotPreservation", "documentation": "<p>An enum value representing possible snapshot preservation settings.</p>", "locationName": "ebsSnapshotPreservation"}}}, "UpdateMalwareScanSettingsResponse": {"type": "structure", "members": {}}, "UpdateMemberDetectorsRequest": {"type": "structure", "required": ["DetectorId", "AccountIds"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The detector ID of the administrator account.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}, "AccountIds": {"shape": "AccountIds", "documentation": "<p>A list of member account IDs to be updated.</p>", "locationName": "accountIds"}, "DataSources": {"shape": "DataSourceConfigurations", "documentation": "<p>Describes which data sources will be updated.</p>", "deprecated": true, "deprecatedMessage": "This parameter is deprecated, use Features instead", "locationName": "dataSources"}, "Features": {"shape": "MemberFeaturesConfigurations", "documentation": "<p>A list of features that will be updated for the specified member accounts.</p>", "locationName": "features"}}}, "UpdateMemberDetectorsResponse": {"type": "structure", "required": ["UnprocessedAccounts"], "members": {"UnprocessedAccounts": {"shape": "UnprocessedAccounts", "documentation": "<p>A list of member account IDs that were unable to be processed along with an explanation for why they were not processed.</p>", "locationName": "unprocessedAccounts"}}}, "UpdateOrganizationConfigurationRequest": {"type": "structure", "required": ["DetectorId"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The ID of the detector that configures the delegated administrator.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}, "AutoEnable": {"shape": "Boolean", "documentation": "<p>Represents whether to automatically enable member accounts in the organization. This applies to only new member accounts, not the existing member accounts. When a new account joins the organization, the chosen features will be enabled for them by default.</p> <p>Even though this is still supported, we recommend using <code>AutoEnableOrganizationMembers</code> to achieve the similar results. You must provide a value for either <code>autoEnableOrganizationMembers</code> or <code>autoEnable</code>.</p>", "deprecated": true, "deprecatedMessage": "This field is deprecated, use AutoEnableOrganizationMembers instead", "locationName": "autoEnable"}, "DataSources": {"shape": "OrganizationDataSourceConfigurations", "documentation": "<p>Describes which data sources will be updated.</p>", "deprecated": true, "deprecatedMessage": "This parameter is deprecated, use Features instead", "locationName": "dataSources"}, "Features": {"shape": "OrganizationFeaturesConfigurations", "documentation": "<p>A list of features that will be configured for the organization.</p>", "locationName": "features"}, "AutoEnableOrganizationMembers": {"shape": "AutoEnableMembers", "documentation": "<p>Indicates the auto-enablement configuration of GuardDuty for the member accounts in the organization. You must provide a value for either <code>autoEnableOrganizationMembers</code> or <code>autoEnable</code>. </p> <p>Use one of the following configuration values for <code>autoEnableOrganizationMembers</code>:</p> <ul> <li> <p> <code>NEW</code>: Indicates that when a new account joins the organization, they will have GuardDuty enabled automatically. </p> </li> <li> <p> <code>ALL</code>: Indicates that all accounts in the organization have GuardDuty enabled automatically. This includes <code>NEW</code> accounts that join the organization and accounts that may have been suspended or removed from the organization in GuardDuty.</p> <p>It may take up to 24 hours to update the configuration for all the member accounts.</p> </li> <li> <p> <code>NONE</code>: Indicates that Guard<PERSON>ut<PERSON> will not be automatically enabled for any account in the organization. The administrator must manage GuardDuty for each account in the organization individually.</p> <p>When you update the auto-enable setting from <code>ALL</code> or <code>NEW</code> to <code>NONE</code>, this action doesn't disable the corresponding option for your existing accounts. This configuration will apply to the new accounts that join the organization. After you update the auto-enable settings, no new account will have the corresponding option as enabled.</p> </li> </ul>", "locationName": "autoEnableOrganizationMembers"}}}, "UpdateOrganizationConfigurationResponse": {"type": "structure", "members": {}}, "UpdateProtectedResource": {"type": "structure", "members": {"S3Bucket": {"shape": "UpdateS3BucketResource", "documentation": "<p>Information about the protected S3 bucket resource.</p>", "locationName": "s3Bucket"}}, "documentation": "<p>Information about the protected resource that is associated with the created Malware Protection plan. Presently, <code>S3Bucket</code> is the only supported protected resource.</p>"}, "UpdatePublishingDestinationRequest": {"type": "structure", "required": ["DetectorId", "DestinationId"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The ID of the detector associated with the publishing destinations to update.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}, "DestinationId": {"shape": "String", "documentation": "<p>The ID of the publishing destination to update.</p>", "location": "uri", "locationName": "destinationId"}, "DestinationProperties": {"shape": "DestinationProperties", "documentation": "<p>A <code>DestinationProperties</code> object that includes the <code>DestinationArn</code> and <code>KmsKeyArn</code> of the publishing destination.</p>", "locationName": "destinationProperties"}}}, "UpdatePublishingDestinationResponse": {"type": "structure", "members": {}}, "UpdateS3BucketResource": {"type": "structure", "members": {"ObjectPrefixes": {"shape": "MalwareProtectionPlanObjectPrefixesList", "documentation": "<p>Information about the specified object prefixes. The S3 object will be scanned only if it belongs to any of the specified object prefixes.</p>", "locationName": "objectPrefixes"}}, "documentation": "<p>Information about the protected S3 bucket resource.</p>"}, "UpdateThreatIntelSetRequest": {"type": "structure", "required": ["DetectorId", "ThreatIntelSetId"], "members": {"DetectorId": {"shape": "DetectorId", "documentation": "<p>The detectorID that specifies the GuardDuty service whose ThreatIntelSet you want to update.</p> <p>To find the <code>detectorId</code> in the current Region, see the Settings page in the GuardDuty console, or run the <a href=\"https://docs.aws.amazon.com/guardduty/latest/APIReference/API_ListDetectors.html\">ListDetectors</a> API.</p>", "location": "uri", "locationName": "detectorId"}, "ThreatIntelSetId": {"shape": "String", "documentation": "<p>The unique ID that specifies the ThreatIntelSet that you want to update.</p>", "location": "uri", "locationName": "threatIntelSetId"}, "Name": {"shape": "Name", "documentation": "<p>The unique ID that specifies the ThreatIntelSet that you want to update.</p>", "locationName": "name"}, "Location": {"shape": "Location", "documentation": "<p>The updated URI of the file that contains the ThreateIntelSet.</p>", "locationName": "location"}, "Activate": {"shape": "Boolean", "documentation": "<p>The updated Boolean value that specifies whether the ThreateIntelSet is active or not.</p>", "locationName": "activate"}}}, "UpdateThreatIntelSetResponse": {"type": "structure", "members": {}}, "UsageAccountResult": {"type": "structure", "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The Account ID that generated usage.</p>", "locationName": "accountId"}, "Total": {"shape": "Total", "documentation": "<p>Represents the total of usage for the Account ID.</p>", "locationName": "total"}}, "documentation": "<p>Contains information on the total of usage based on account IDs.</p>"}, "UsageAccountResultList": {"type": "list", "member": {"shape": "UsageAccountResult"}}, "UsageCriteria": {"type": "structure", "members": {"AccountIds": {"shape": "AccountIds", "documentation": "<p>The account IDs to aggregate usage statistics from.</p>", "locationName": "accountIds"}, "DataSources": {"shape": "DataSourceList", "documentation": "<p>The data sources to aggregate usage statistics from.</p>", "deprecated": true, "deprecatedMessage": "This parameter is deprecated, use Features instead", "locationName": "dataSources"}, "Resources": {"shape": "ResourceList", "documentation": "<p>The resources to aggregate usage statistics from. Only accepts exact resource names.</p>", "locationName": "resources"}, "Features": {"shape": "UsageFeatureList", "documentation": "<p>The features to aggregate usage statistics from.</p>", "locationName": "features"}}, "documentation": "<p>Contains information about the criteria used to query usage statistics.</p>"}, "UsageDataSourceResult": {"type": "structure", "members": {"DataSource": {"shape": "DataSource", "documentation": "<p>The data source type that generated usage.</p>", "locationName": "dataSource"}, "Total": {"shape": "Total", "documentation": "<p>Represents the total of usage for the specified data source.</p>", "locationName": "total"}}, "documentation": "<p>Contains information on the result of usage based on data source type.</p>"}, "UsageDataSourceResultList": {"type": "list", "member": {"shape": "UsageDataSourceResult"}}, "UsageFeature": {"type": "string", "enum": ["FLOW_LOGS", "CLOUD_TRAIL", "DNS_LOGS", "S3_DATA_EVENTS", "EKS_AUDIT_LOGS", "EBS_MALWARE_PROTECTION", "RDS_LOGIN_EVENTS", "LAMBDA_NETWORK_LOGS", "EKS_RUNTIME_MONITORING", "FARGATE_RUNTIME_MONITORING", "EC2_RUNTIME_MONITORING", "RDS_DBI_PROTECTION_PROVISIONED", "RDS_DBI_PROTECTION_SERVERLESS"]}, "UsageFeatureList": {"type": "list", "member": {"shape": "UsageFeature"}}, "UsageFeatureResult": {"type": "structure", "members": {"Feature": {"shape": "UsageFeature", "documentation": "<p>The feature that generated the usage cost.</p>", "locationName": "feature"}, "Total": {"shape": "Total", "locationName": "total"}}, "documentation": "<p>Contains information about the result of the total usage based on the feature.</p>"}, "UsageFeatureResultList": {"type": "list", "member": {"shape": "UsageFeatureResult"}}, "UsageResourceResult": {"type": "structure", "members": {"Resource": {"shape": "String", "documentation": "<p>The Amazon Web Services resource that generated usage.</p>", "locationName": "resource"}, "Total": {"shape": "Total", "documentation": "<p>Represents the sum total of usage for the specified resource type.</p>", "locationName": "total"}}, "documentation": "<p>Contains information on the sum of usage based on an Amazon Web Services resource.</p>"}, "UsageResourceResultList": {"type": "list", "member": {"shape": "UsageResourceResult"}}, "UsageStatisticType": {"type": "string", "enum": ["SUM_BY_ACCOUNT", "SUM_BY_DATA_SOURCE", "SUM_BY_RESOURCE", "TOP_RESOURCES", "SUM_BY_FEATURES", "TOP_ACCOUNTS_BY_FEATURE"]}, "UsageStatistics": {"type": "structure", "members": {"SumByAccount": {"shape": "UsageAccountResultList", "documentation": "<p>The usage statistic sum organized by account ID.</p>", "locationName": "sumByAccount"}, "TopAccountsByFeature": {"shape": "UsageTopAccountsResultList", "documentation": "<p>Lists the top 50 accounts by feature that have generated the most GuardDuty usage, in the order from most to least expensive.</p> <p>Currently, this doesn't support <code>RDS_LOGIN_EVENTS</code>.</p>", "locationName": "topAccountsByFeature"}, "SumByDataSource": {"shape": "UsageDataSourceResultList", "documentation": "<p>The usage statistic sum organized by on data source.</p>", "locationName": "sumByDataSource"}, "SumByResource": {"shape": "UsageResourceResultList", "documentation": "<p>The usage statistic sum organized by resource.</p>", "locationName": "sumByResource"}, "TopResources": {"shape": "UsageResourceResultList", "documentation": "<p>Lists the top 50 resources that have generated the most GuardDuty usage, in order from most to least expensive.</p>", "locationName": "topResources"}, "SumByFeature": {"shape": "UsageFeatureResultList", "documentation": "<p>The usage statistic sum organized by feature.</p>", "locationName": "sumByFeature"}}, "documentation": "<p>Contains the result of GuardDuty usage. If a UsageStatisticType is provided the result for other types will be null. </p>"}, "UsageTopAccountResult": {"type": "structure", "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The unique account ID.</p>", "locationName": "accountId"}, "Total": {"shape": "Total", "locationName": "total"}}, "documentation": "<p>Contains information on the total of usage based on the topmost 50 account IDs.</p>"}, "UsageTopAccountsByFeatureList": {"type": "list", "member": {"shape": "UsageTopAccountResult"}}, "UsageTopAccountsResult": {"type": "structure", "members": {"Feature": {"shape": "UsageFeature", "documentation": "<p>Features by which you can generate the usage statistics.</p> <p> <code>RDS_LOGIN_EVENTS</code> is currently not supported with <code>topAccountsByFeature</code>.</p>", "locationName": "feature"}, "Accounts": {"shape": "UsageTopAccountsByFeatureList", "documentation": "<p>The accounts that contributed to the total usage cost.</p>", "locationName": "accounts"}}, "documentation": "<p>Information about the usage statistics, calculated by top accounts by feature.</p>"}, "UsageTopAccountsResultList": {"type": "list", "member": {"shape": "UsageTopAccountsResult"}}, "User": {"type": "structure", "required": ["Name", "<PERSON><PERSON>", "Type"], "members": {"Name": {"shape": "String", "documentation": "<p>The name of the user.</p>", "locationName": "name"}, "Uid": {"shape": "String", "documentation": "<p>The unique identifier of the user.</p>", "locationName": "uid"}, "Type": {"shape": "String", "documentation": "<p>The type of the user.</p>", "locationName": "type"}, "CredentialUid": {"shape": "String", "documentation": "<p>The credentials of the user ID.</p>", "locationName": "credentialUid"}, "Account": {"shape": "Account", "documentation": "<p>Contains information about the Amazon Web Services account.</p>", "locationName": "account"}}, "documentation": "<p>Contains information about the user involved in the attack sequence.</p>"}, "Volume": {"type": "structure", "members": {"Name": {"shape": "String", "documentation": "<p>Volume name.</p>", "locationName": "name"}, "HostPath": {"shape": "HostPath", "documentation": "<p>Represents a pre-existing file or directory on the host machine that the volume maps to.</p>", "locationName": "hostPath"}}, "documentation": "<p>Volume used by the Kubernetes workload.</p>"}, "VolumeDetail": {"type": "structure", "members": {"VolumeArn": {"shape": "String", "documentation": "<p>EBS volume ARN information.</p>", "locationName": "volumeArn"}, "VolumeType": {"shape": "String", "documentation": "<p>The EBS volume type.</p>", "locationName": "volumeType"}, "DeviceName": {"shape": "String", "documentation": "<p>The device name for the EBS volume.</p>", "locationName": "deviceName"}, "VolumeSizeInGB": {"shape": "Integer", "documentation": "<p>EBS volume size in GB.</p>", "locationName": "volumeSizeInGB"}, "EncryptionType": {"shape": "String", "documentation": "<p>EBS volume encryption type.</p>", "locationName": "encryptionType"}, "SnapshotArn": {"shape": "String", "documentation": "<p>Snapshot ARN of the EBS volume.</p>", "locationName": "snapshotArn"}, "KmsKeyArn": {"shape": "String", "documentation": "<p>KMS key ARN used to encrypt the EBS volume.</p>", "locationName": "kmsKeyArn"}}, "documentation": "<p>Contains EBS volume details.</p>"}, "VolumeDetails": {"type": "list", "member": {"shape": "VolumeDetail"}}, "VolumeMount": {"type": "structure", "members": {"Name": {"shape": "String", "documentation": "<p>Volume mount name.</p>", "locationName": "name"}, "MountPath": {"shape": "String", "documentation": "<p>Volume mount path.</p>", "locationName": "mount<PERSON>ath"}}, "documentation": "<p>Container volume mount.</p>"}, "VolumeMounts": {"type": "list", "member": {"shape": "VolumeMount"}}, "Volumes": {"type": "list", "member": {"shape": "Volume"}}, "VpcConfig": {"type": "structure", "members": {"SubnetIds": {"shape": "SubnetIds", "documentation": "<p>The identifiers of the subnets that are associated with your Lambda function.</p>", "locationName": "subnetIds"}, "VpcId": {"shape": "String", "documentation": "<p>The identifier of the Amazon Virtual Private Cloud.</p>", "locationName": "vpcId"}, "SecurityGroups": {"shape": "SecurityGroups", "documentation": "<p>The identifier of the security group attached to the Lambda function.</p>", "locationName": "securityGroups"}}, "documentation": "<p>Amazon Virtual Private Cloud configuration details associated with your Lambda function.</p>"}}, "documentation": "<p>Amazon GuardDuty is a continuous security monitoring service that analyzes and processes the following foundational data sources - VPC flow logs, Amazon Web Services CloudTrail management event logs, CloudTrail S3 data event logs, EKS audit logs, DNS logs, Amazon EBS volume data, runtime activity belonging to container workloads, such as Amazon EKS, Amazon ECS (including Amazon Web Services Fargate), and Amazon EC2 instances. It uses threat intelligence feeds, such as lists of malicious IPs and domains, and machine learning to identify unexpected, potentially unauthorized, and malicious activity within your Amazon Web Services environment. This can include issues like escalations of privileges, uses of exposed credentials, or communication with malicious IPs, domains, or presence of malware on your Amazon EC2 instances and container workloads. For example, GuardDuty can detect compromised EC2 instances and container workloads serving malware, or mining bitcoin. </p> <p>GuardDuty also monitors Amazon Web Services account access behavior for signs of compromise, such as unauthorized infrastructure deployments like EC2 instances deployed in a Region that has never been used, or unusual API calls like a password policy change to reduce password strength. </p> <p>GuardDuty informs you about the status of your Amazon Web Services environment by producing security findings that you can view in the GuardDuty console or through Amazon EventBridge. For more information, see the <i> <a href=\"https://docs.aws.amazon.com/guardduty/latest/ug/what-is-guardduty.html\">Amazon GuardDuty User Guide</a> </i>. </p>"}