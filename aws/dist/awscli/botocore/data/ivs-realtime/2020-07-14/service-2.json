{"version": "2.0", "metadata": {"apiVersion": "2020-07-14", "auth": ["aws.auth#sigv4"], "endpointPrefix": "ivsrealtime", "protocol": "rest-json", "protocols": ["rest-json"], "serviceAbbreviation": "ivsrealtime", "serviceFullName": "Amazon Interactive Video Service RealTime", "serviceId": "IVS RealTime", "signatureVersion": "v4", "signingName": "ivs", "uid": "ivs-realtime-2020-07-14"}, "operations": {"CreateEncoderConfiguration": {"name": "CreateEncoderConfiguration", "http": {"method": "POST", "requestUri": "/CreateEncoderConfiguration", "responseCode": 200}, "input": {"shape": "CreateEncoderConfigurationRequest"}, "output": {"shape": "CreateEncoderConfigurationResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}, {"shape": "PendingVerification"}], "documentation": "<p>Creates an EncoderConfiguration object.</p>"}, "CreateIngestConfiguration": {"name": "CreateIngestConfiguration", "http": {"method": "POST", "requestUri": "/CreateIngestConfiguration", "responseCode": 200}, "input": {"shape": "CreateIngestConfigurationRequest"}, "output": {"shape": "CreateIngestConfigurationResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "PendingVerification"}], "documentation": "<p>Creates a new IngestConfiguration resource, used to specify the ingest protocol for a stage.</p>"}, "CreateParticipantToken": {"name": "CreateParticipantToken", "http": {"method": "POST", "requestUri": "/CreateParticipantToken", "responseCode": 200}, "input": {"shape": "CreateParticipantTokenRequest"}, "output": {"shape": "CreateParticipantTokenResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "PendingVerification"}], "documentation": "<p>Creates an additional token for a specified stage. This can be done after stage creation or when tokens expire. Tokens always are scoped to the stage for which they are created.</p> <p>Encryption keys are owned by Amazon IVS and never used directly by your application.</p>"}, "CreateStage": {"name": "CreateStage", "http": {"method": "POST", "requestUri": "/CreateStage", "responseCode": 200}, "input": {"shape": "CreateStageRequest"}, "output": {"shape": "CreateStageResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "PendingVerification"}], "documentation": "<p>Creates a new stage (and optionally participant tokens).</p>"}, "CreateStorageConfiguration": {"name": "CreateStorageConfiguration", "http": {"method": "POST", "requestUri": "/CreateStorageConfiguration", "responseCode": 200}, "input": {"shape": "CreateStorageConfigurationRequest"}, "output": {"shape": "CreateStorageConfigurationResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}, {"shape": "PendingVerification"}], "documentation": "<p>Creates a new storage configuration, used to enable recording to Amazon S3. When a StorageConfiguration is created, IVS will modify the S3 bucketPolicy of the provided bucket. This will ensure that IVS has sufficient permissions to write content to the provided bucket.</p>"}, "DeleteEncoderConfiguration": {"name": "DeleteEncoderConfiguration", "http": {"method": "POST", "requestUri": "/DeleteEncoderConfiguration", "responseCode": 200}, "input": {"shape": "DeleteEncoderConfigurationRequest"}, "output": {"shape": "DeleteEncoderConfigurationResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}], "documentation": "<p>Deletes an EncoderConfiguration resource. Ensures that no Compositions are using this template; otherwise, returns an error.</p>"}, "DeleteIngestConfiguration": {"name": "DeleteIngestConfiguration", "http": {"method": "POST", "requestUri": "/DeleteIngestConfiguration", "responseCode": 200}, "input": {"shape": "DeleteIngestConfigurationRequest"}, "output": {"shape": "DeleteIngestConfigurationResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "PendingVerification"}], "documentation": "<p>Deletes a specified IngestConfiguration, so it can no longer be used to broadcast. An IngestConfiguration cannot be deleted if the publisher is actively streaming to a stage, unless <code>force</code> is set to <code>true</code>.</p>"}, "DeletePublicKey": {"name": "DeletePublicKey", "http": {"method": "POST", "requestUri": "/DeletePublicKey", "responseCode": 200}, "input": {"shape": "DeletePublicKeyRequest"}, "output": {"shape": "DeletePublicKeyResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "PendingVerification"}], "documentation": "<p>Deletes the specified public key used to sign stage participant tokens. This invalidates future participant tokens generated using the key pair’s private key. </p>"}, "DeleteStage": {"name": "DeleteStage", "http": {"method": "POST", "requestUri": "/DeleteStage", "responseCode": 200}, "input": {"shape": "DeleteStageRequest"}, "output": {"shape": "DeleteStageResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "PendingVerification"}], "documentation": "<p>Shuts down and deletes the specified stage (disconnecting all participants). This operation also removes the <code>stageArn</code> from the associated <a>IngestConfiguration</a>, if there are participants using the IngestConfiguration to publish to the stage.</p>"}, "DeleteStorageConfiguration": {"name": "DeleteStorageConfiguration", "http": {"method": "POST", "requestUri": "/DeleteStorageConfiguration", "responseCode": 200}, "input": {"shape": "DeleteStorageConfigurationRequest"}, "output": {"shape": "DeleteStorageConfigurationResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}], "documentation": "<p>Deletes the storage configuration for the specified ARN.</p> <p>If you try to delete a storage configuration that is used by a Composition, you will get an error (409 ConflictException). To avoid this, for all Compositions that reference the storage configuration, first use <a>StopComposition</a> and wait for it to complete, then use DeleteStorageConfiguration.</p>"}, "DisconnectParticipant": {"name": "DisconnectParticipant", "http": {"method": "POST", "requestUri": "/DisconnectParticipant", "responseCode": 200}, "input": {"shape": "DisconnectParticipantRequest"}, "output": {"shape": "DisconnectParticipantResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "PendingVerification"}], "documentation": "<p>Disconnects a specified participant from a specified stage. If the participant is publishing using an <a>IngestConfiguration</a>, DisconnectParticipant also updates the <code>stageArn</code> in the IngestConfiguration to be an empty string.</p>"}, "GetComposition": {"name": "GetComposition", "http": {"method": "POST", "requestUri": "/GetComposition", "responseCode": 200}, "input": {"shape": "GetCompositionRequest"}, "output": {"shape": "GetCompositionResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}], "documentation": "<p>Get information about the specified Composition resource.</p>"}, "GetEncoderConfiguration": {"name": "GetEncoderConfiguration", "http": {"method": "POST", "requestUri": "/GetEncoderConfiguration", "responseCode": 200}, "input": {"shape": "GetEncoderConfigurationRequest"}, "output": {"shape": "GetEncoderConfigurationResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}], "documentation": "<p>Gets information about the specified EncoderConfiguration resource. </p>"}, "GetIngestConfiguration": {"name": "GetIngestConfiguration", "http": {"method": "POST", "requestUri": "/GetIngestConfiguration", "responseCode": 200}, "input": {"shape": "GetIngestConfigurationRequest"}, "output": {"shape": "GetIngestConfigurationResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Gets information about the specified IngestConfiguration.</p>"}, "GetParticipant": {"name": "GetParticipant", "http": {"method": "POST", "requestUri": "/GetParticipant", "responseCode": 200}, "input": {"shape": "GetParticipantRequest"}, "output": {"shape": "GetParticipantResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Gets information about the specified participant token.</p>"}, "GetPublicKey": {"name": "GetPublicKey", "http": {"method": "POST", "requestUri": "/GetPublicKey", "responseCode": 200}, "input": {"shape": "GetPublicKeyRequest"}, "output": {"shape": "GetPublicKeyResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Gets information for the specified public key.</p>"}, "GetStage": {"name": "GetStage", "http": {"method": "POST", "requestUri": "/GetStage", "responseCode": 200}, "input": {"shape": "GetStageRequest"}, "output": {"shape": "GetStageResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Gets information for the specified stage.</p>"}, "GetStageSession": {"name": "GetStageSession", "http": {"method": "POST", "requestUri": "/GetStageSession", "responseCode": 200}, "input": {"shape": "GetStageSessionRequest"}, "output": {"shape": "GetStageSessionResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Gets information for the specified stage session.</p>"}, "GetStorageConfiguration": {"name": "GetStorageConfiguration", "http": {"method": "POST", "requestUri": "/GetStorageConfiguration", "responseCode": 200}, "input": {"shape": "GetStorageConfigurationRequest"}, "output": {"shape": "GetStorageConfigurationResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}], "documentation": "<p>Gets the storage configuration for the specified ARN.</p>"}, "ImportPublicKey": {"name": "ImportPublicKey", "http": {"method": "POST", "requestUri": "/ImportPublicKey", "responseCode": 200}, "input": {"shape": "ImportPublicKeyRequest"}, "output": {"shape": "ImportPublicKeyResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}, {"shape": "PendingVerification"}], "documentation": "<p>Import a public key to be used for signing stage participant tokens.</p>"}, "ListCompositions": {"name": "ListCompositions", "http": {"method": "POST", "requestUri": "/ListCompositions", "responseCode": 200}, "input": {"shape": "ListCompositionsRequest"}, "output": {"shape": "ListCompositionsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}], "documentation": "<p>Gets summary information about all Compositions in your account, in the AWS region where the API request is processed. </p>"}, "ListEncoderConfigurations": {"name": "ListEncoderConfigurations", "http": {"method": "POST", "requestUri": "/ListEncoderConfigurations", "responseCode": 200}, "input": {"shape": "ListEncoderConfigurationsRequest"}, "output": {"shape": "ListEncoderConfigurationsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}], "documentation": "<p>Gets summary information about all EncoderConfigurations in your account, in the AWS region where the API request is processed.</p>"}, "ListIngestConfigurations": {"name": "ListIngestConfigurations", "http": {"method": "POST", "requestUri": "/ListIngestConfigurations", "responseCode": 200}, "input": {"shape": "ListIngestConfigurationsRequest"}, "output": {"shape": "ListIngestConfigurationsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists all IngestConfigurations in your account, in the AWS region where the API request is processed.</p>"}, "ListParticipantEvents": {"name": "ListParticipantEvents", "http": {"method": "POST", "requestUri": "/ListParticipantEvents", "responseCode": 200}, "input": {"shape": "ListParticipantEventsRequest"}, "output": {"shape": "ListParticipantEventsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists events for a specified participant that occurred during a specified stage session.</p>"}, "ListParticipantReplicas": {"name": "ListParticipantReplicas", "http": {"method": "POST", "requestUri": "/ListParticipantReplicas", "responseCode": 200}, "input": {"shape": "ListParticipantReplicasRequest"}, "output": {"shape": "ListParticipantReplicasResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists all the replicas for a participant from a source stage.</p>"}, "ListParticipants": {"name": "ListParticipants", "http": {"method": "POST", "requestUri": "/ListParticipants", "responseCode": 200}, "input": {"shape": "ListParticipantsRequest"}, "output": {"shape": "ListParticipantsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists all participants in a specified stage session.</p>"}, "ListPublicKeys": {"name": "ListPublicKeys", "http": {"method": "POST", "requestUri": "/ListPublicKeys", "responseCode": 200}, "input": {"shape": "ListPublicKeysRequest"}, "output": {"shape": "ListPublicKeysResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Gets summary information about all public keys in your account, in the AWS region where the API request is processed.</p>"}, "ListStageSessions": {"name": "ListStageSessions", "http": {"method": "POST", "requestUri": "/ListStageSessions", "responseCode": 200}, "input": {"shape": "ListStageSessionsRequest"}, "output": {"shape": "ListStageSessionsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Gets all sessions for a specified stage.</p>"}, "ListStages": {"name": "ListStages", "http": {"method": "POST", "requestUri": "/ListStages", "responseCode": 200}, "input": {"shape": "ListStagesRequest"}, "output": {"shape": "ListStagesResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}], "documentation": "<p>Gets summary information about all stages in your account, in the AWS region where the API request is processed.</p>"}, "ListStorageConfigurations": {"name": "ListStorageConfigurations", "http": {"method": "POST", "requestUri": "/ListStorageConfigurations", "responseCode": 200}, "input": {"shape": "ListStorageConfigurationsRequest"}, "output": {"shape": "ListStorageConfigurationsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}], "documentation": "<p>Gets summary information about all storage configurations in your account, in the AWS region where the API request is processed.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets information about AWS tags for the specified ARN.</p>"}, "StartComposition": {"name": "StartComposition", "http": {"method": "POST", "requestUri": "/StartComposition", "responseCode": 200}, "input": {"shape": "StartCompositionRequest"}, "output": {"shape": "StartCompositionResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}, {"shape": "PendingVerification"}], "documentation": "<p>Starts a Composition from a stage based on the configuration provided in the request.</p> <p>A Composition is an ephemeral resource that exists after this operation returns successfully. Composition stops and the resource is deleted:</p> <ul> <li> <p>When <a>StopComposition</a> is called.</p> </li> <li> <p>After a 1-minute timeout, when all participants are disconnected from the stage.</p> </li> <li> <p>After a 1-minute timeout, if there are no participants in the stage when StartComposition is called.</p> </li> <li> <p>When broadcasting to the IVS channel fails and all retries are exhausted.</p> </li> <li> <p>When broadcasting is disconnected and all attempts to reconnect are exhausted.</p> </li> </ul>"}, "StartParticipantReplication": {"name": "StartParticipantReplication", "http": {"method": "POST", "requestUri": "/StartParticipantReplication", "responseCode": 200}, "input": {"shape": "StartParticipantReplicationRequest"}, "output": {"shape": "StartParticipantReplicationResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}, {"shape": "PendingVerification"}], "documentation": "<p>Starts replicating a publishing participant from a source stage to a destination stage.</p>"}, "StopComposition": {"name": "StopComposition", "http": {"method": "POST", "requestUri": "/StopComposition", "responseCode": 200}, "input": {"shape": "StopCompositionRequest"}, "output": {"shape": "StopCompositionResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}], "documentation": "<p>Stops and deletes a Composition resource. Any broadcast from the Composition resource is stopped.</p>"}, "StopParticipantReplication": {"name": "StopParticipantReplication", "http": {"method": "POST", "requestUri": "/StopParticipantReplication", "responseCode": 200}, "input": {"shape": "StopParticipantReplicationRequest"}, "output": {"shape": "StopParticipantReplicationResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Stops a replicated participant session.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Adds or updates tags for the AWS resource with the specified ARN.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Removes tags from the resource with the specified ARN.</p>", "idempotent": true}, "UpdateIngestConfiguration": {"name": "UpdateIngestConfiguration", "http": {"method": "POST", "requestUri": "/UpdateIngestConfiguration", "responseCode": 200}, "input": {"shape": "UpdateIngestConfigurationRequest"}, "output": {"shape": "UpdateIngestConfigurationResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "PendingVerification"}], "documentation": "<p>Updates a specified IngestConfiguration. Only the stage ARN attached to the IngestConfiguration can be updated. An IngestConfiguration that is active cannot be updated.</p>"}, "UpdateStage": {"name": "UpdateStage", "http": {"method": "POST", "requestUri": "/UpdateStage", "responseCode": 200}, "input": {"shape": "UpdateStageRequest"}, "output": {"shape": "UpdateStageResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}, {"shape": "PendingVerification"}], "documentation": "<p>Updates a stage’s configuration.</p>"}}, "shapes": {"AccessDeniedException": {"type": "structure", "members": {"accessControlAllowOrigin": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "Access-Control-Allow-Origin"}, "accessControlExposeHeaders": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "Access-Control-Expose-Headers"}, "cacheControl": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "Cache-Control"}, "contentSecurityPolicy": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "Content-Security-Policy"}, "strictTransportSecurity": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "Strict-Transport-Security"}, "xContentTypeOptions": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "X-Content-Type-Options"}, "xFrameOptions": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "X-Frame-Options"}, "xAmznErrorType": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "x-amzn-ErrorType"}, "exceptionMessage": {"shape": "errorMessage", "documentation": "<p>User does not have sufficient access to perform this action.</p>"}}, "documentation": "<p/>", "error": {"httpStatusCode": 403, "senderFault": true}, "exception": true}, "AttributeKey": {"type": "string", "max": 128, "min": 0, "pattern": "[a-zA-Z0-9-_]*"}, "AutoParticipantRecordingConfiguration": {"type": "structure", "required": ["storageConfigurationArn"], "members": {"storageConfigurationArn": {"shape": "AutoParticipantRecordingStorageConfigurationArn", "documentation": "<p>ARN of the <a>StorageConfiguration</a> resource to use for individual participant recording. Default: <code>\"\"</code> (empty string, no storage configuration is specified). Individual participant recording cannot be started unless a storage configuration is specified, when a <a>Stage</a> is created or updated. To disable individual participant recording, set this to <code>\"\"</code>; other fields in this object will get reset to their defaults when sending <code>\"\"</code>. </p>"}, "mediaTypes": {"shape": "ParticipantRecordingMediaTypeList", "documentation": "<p>Types of media to be recorded. Default: <code>AUDIO_VIDEO</code>.</p>"}, "thumbnailConfiguration": {"shape": "ParticipantThumbnailConfiguration", "documentation": "<p>A complex type that allows you to enable/disable the recording of thumbnails for individual participant recording and modify the interval at which thumbnails are generated for the live session.</p>"}, "recordingReconnectWindowSeconds": {"shape": "ParticipantRecordingReconnectWindowSeconds", "documentation": "<p>If a stage publisher disconnects and then reconnects within the specified interval, the multiple recordings will be considered a single recording and merged together.</p> <p>The default value is 0, which disables merging.</p>"}, "hlsConfiguration": {"shape": "ParticipantRecordingHlsConfiguration", "documentation": "<p>HLS configuration object for individual participant recording.</p>"}, "recordParticipantReplicas": {"shape": "RecordParticipantReplicas", "documentation": "<p>Optional field to disable replica participant recording. If this is set to <code>false</code> when a participant is a replica, replica participants are not recorded. Default: <code>true</code>.</p>"}}, "documentation": "<p>Object specifying a configuration for individual participant recording.</p>"}, "AutoParticipantRecordingStorageConfigurationArn": {"type": "string", "max": 128, "min": 0, "pattern": "^$|^arn:aws:ivs:[a-z0-9-]+:[0-9]+:storage-configuration/[a-zA-Z0-9-]+$"}, "Bitrate": {"type": "integer", "box": true, "max": 8500000, "min": 1}, "Boolean": {"type": "boolean"}, "ChannelArn": {"type": "string", "max": 128, "min": 1, "pattern": "arn:aws:ivs:[a-z0-9-]+:[0-9]+:channel/[a-zA-Z0-9-]+"}, "ChannelDestinationConfiguration": {"type": "structure", "required": ["channelArn"], "members": {"channelArn": {"shape": "ChannelArn", "documentation": "<p>ARN of the channel to use for broadcasting. The channel and stage resources must be in the same AWS account and region. The channel must be offline (not broadcasting).</p>"}, "encoderConfigurationArn": {"shape": "EncoderConfigurationArn", "documentation": "<p>ARN of the <a>EncoderConfiguration</a> resource. The encoder configuration and stage resources must be in the same AWS account and region.</p>"}}, "documentation": "<p>Object specifying a channel as a destination.</p>"}, "Composition": {"type": "structure", "required": ["arn", "stageArn", "state", "layout", "destinations"], "members": {"arn": {"shape": "CompositionArn", "documentation": "<p>ARN of the Composition resource.</p>"}, "stageArn": {"shape": "StageArn", "documentation": "<p>ARN of the stage used as input</p>"}, "state": {"shape": "CompositionState", "documentation": "<p>State of the Composition.</p>"}, "layout": {"shape": "LayoutConfiguration", "documentation": "<p>Layout object to configure composition parameters.</p>"}, "destinations": {"shape": "DestinationList", "documentation": "<p>Array of Destination objects. A Composition can contain either one destination (<code>channel</code> or <code>s3</code>) or two (one <code>channel</code> and one <code>s3</code>).</p>"}, "tags": {"shape": "Tags", "documentation": "<p>Tags attached to the resource. Array of maps, each of the form <code>string:string (key:value)</code>. See <a href=\"https://docs.aws.amazon.com/tag-editor/latest/userguide/best-practices-and-strats.html\">Best practices and strategies</a> in <i>Tagging AWS Resources and Tag Editor</i> for details, including restrictions that apply to tags and \"Tag naming limits and requirements\"; Amazon IVS has no constraints on tags beyond what is documented there.</p>"}, "startTime": {"shape": "Time", "documentation": "<p>UTC time of the Composition start. This is an ISO 8601 timestamp; <i>note that this is returned as a string</i>.</p>"}, "endTime": {"shape": "Time", "documentation": "<p>UTC time of the Composition end. This is an ISO 8601 timestamp; <i>note that this is returned as a string</i>.</p>"}}, "documentation": "<p>Object specifying a Composition resource.</p>"}, "CompositionArn": {"type": "string", "max": 128, "min": 1, "pattern": "arn:aws:ivs:[a-z0-9-]+:[0-9]+:composition/[a-zA-Z0-9-]+"}, "CompositionClientToken": {"type": "string", "max": 64, "min": 1, "pattern": "[a-zA-Z0-9-_]*"}, "CompositionRecordingHlsConfiguration": {"type": "structure", "members": {"targetSegmentDurationSeconds": {"shape": "CompositionRecordingTargetSegmentDurationSeconds", "documentation": "<p>Defines the target duration for recorded segments generated when using composite recording. Default: 2.</p>"}}, "documentation": "<p>An object representing a configuration of HLS recordings for server-side composition.</p>"}, "CompositionRecordingTargetSegmentDurationSeconds": {"type": "integer", "box": true, "max": 10, "min": 2}, "CompositionState": {"type": "string", "enum": ["STARTING", "ACTIVE", "STOPPING", "FAILED", "STOPPED"]}, "CompositionSummary": {"type": "structure", "required": ["arn", "stageArn", "destinations", "state"], "members": {"arn": {"shape": "CompositionArn", "documentation": "<p>ARN of the Composition resource.</p>"}, "stageArn": {"shape": "StageArn", "documentation": "<p>ARN of the attached stage.</p>"}, "destinations": {"shape": "DestinationSummaryList", "documentation": "<p>Array of Destination objects.</p>"}, "state": {"shape": "CompositionState", "documentation": "<p>State of the Composition resource.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>Tags attached to the resource. Array of maps, each of the form <code>string:string (key:value)</code>. See <a href=\"https://docs.aws.amazon.com/tag-editor/latest/userguide/best-practices-and-strats.html\">Best practices and strategies</a> in <i>Tagging AWS Resources and Tag Editor</i> for details, including restrictions that apply to tags and \"Tag naming limits and requirements\"; Amazon IVS has no constraints on tags beyond what is documented there.</p>"}, "startTime": {"shape": "Time", "documentation": "<p>UTC time of the Composition start. This is an ISO 8601 timestamp; <i>note that this is returned as a string</i>.</p>"}, "endTime": {"shape": "Time", "documentation": "<p>UTC time of the Composition end. This is an ISO 8601 timestamp; <i>note that this is returned as a string</i>.</p>"}}, "documentation": "<p>Summary information about a Composition.</p>"}, "CompositionSummaryList": {"type": "list", "member": {"shape": "CompositionSummary"}}, "CompositionThumbnailConfiguration": {"type": "structure", "members": {"targetIntervalSeconds": {"shape": "ThumbnailIntervalSeconds", "documentation": "<p>The targeted thumbnail-generation interval in seconds. Default: 60.</p>"}, "storage": {"shape": "ThumbnailStorageTypeList", "documentation": "<p>Indicates the format in which thumbnails are recorded. <code>SEQUENTIAL</code> records all generated thumbnails in a serial manner, to the media/thumbnails/(width)x(height) directory, where (width) and (height) are the width and height of the thumbnail. <code>LATEST</code> saves the latest thumbnail in media/latest_thumbnail/(width)x(height)/thumb.jpg and overwrites it at the interval specified by <code>targetIntervalSeconds</code>. You can enable both <code>SEQUENTIAL</code> and <code>LATEST</code>. Default: <code>SEQUENTIAL</code>.</p>"}}, "documentation": "<p>An object representing a configuration of thumbnails for recorded video for a <a>Composition</a>.</p>"}, "CompositionThumbnailConfigurationList": {"type": "list", "member": {"shape": "CompositionThumbnailConfiguration"}, "max": 1, "min": 0}, "ConflictException": {"type": "structure", "members": {"accessControlAllowOrigin": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "Access-Control-Allow-Origin"}, "accessControlExposeHeaders": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "Access-Control-Expose-Headers"}, "cacheControl": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "Cache-Control"}, "contentSecurityPolicy": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "Content-Security-Policy"}, "strictTransportSecurity": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "Strict-Transport-Security"}, "xContentTypeOptions": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "X-Content-Type-Options"}, "xFrameOptions": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "X-Frame-Options"}, "xAmznErrorType": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "x-amzn-ErrorType"}, "exceptionMessage": {"shape": "errorMessage", "documentation": "<p>Updating or deleting a resource can cause an inconsistent state.</p>"}}, "documentation": "<p/>", "error": {"httpStatusCode": 409, "senderFault": true}, "exception": true}, "CreateEncoderConfigurationRequest": {"type": "structure", "members": {"name": {"shape": "EncoderConfigurationName", "documentation": "<p>Optional name to identify the resource.</p>"}, "video": {"shape": "Video", "documentation": "<p>Video configuration. Default: video resolution 1280x720, bitrate 2500 kbps, 30 fps.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>Tags attached to the resource. Array of maps, each of the form <code>string:string (key:value)</code>. See <a href=\"https://docs.aws.amazon.com/tag-editor/latest/userguide/best-practices-and-strats.html\">Best practices and strategies</a> in <i>Tagging AWS Resources and Tag Editor</i> for details, including restrictions that apply to tags and \"Tag naming limits and requirements\"; Amazon IVS has no constraints on tags beyond what is documented there.</p>"}}}, "CreateEncoderConfigurationResponse": {"type": "structure", "members": {"encoderConfiguration": {"shape": "EncoderConfiguration", "documentation": "<p>The EncoderConfiguration that was created.</p>"}}}, "CreateIngestConfigurationRequest": {"type": "structure", "required": ["ingestProtocol"], "members": {"name": {"shape": "IngestConfigurationName", "documentation": "<p>Optional name that can be specified for the IngestConfiguration being created.</p>"}, "stageArn": {"shape": "IngestConfigurationStageArn", "documentation": "<p>ARN of the stage with which the IngestConfiguration is associated.</p>"}, "userId": {"shape": "UserId", "documentation": "<p>Customer-assigned name to help identify the participant using the IngestConfiguration; this can be used to link a participant to a user in the customer’s own systems. This can be any UTF-8 encoded text. <i>This field is exposed to all stage participants and should not be used for personally identifying, confidential, or sensitive information.</i> </p>"}, "attributes": {"shape": "ParticipantAttributes", "documentation": "<p>Application-provided attributes to store in the IngestConfiguration and attach to a stage. Map keys and values can contain UTF-8 encoded text. The maximum length of this field is 1 KB total. <i>This field is exposed to all stage participants and should not be used for personally identifying, confidential, or sensitive information.</i> </p>"}, "ingestProtocol": {"shape": "IngestProtocol", "documentation": "<p>Type of ingest protocol that the user employs to broadcast. If this is set to <code>RTMP</code>, <code>insecureIngest</code> must be set to <code>true</code>.</p>"}, "insecureIngest": {"shape": "InsecureIngest", "documentation": "<p>Whether the stage allows insecure RTMP ingest. This must be set to <code>true</code>, if <code>ingestProtocol</code> is set to <code>RTMP</code>. Default: <code>false</code>. </p>"}, "tags": {"shape": "Tags", "documentation": "<p>Tags attached to the resource. Array of maps, each of the form <code>string:string (key:value)</code>. See <a href=\"https://docs.aws.amazon.com/tag-editor/latest/userguide/best-practices-and-strats.html\">Best practices and strategies</a> in <i>Tagging AWS Resources and Tag Editor</i> for details, including restrictions that apply to tags and \"Tag naming limits and requirements\"; Amazon IVS has no constraints on tags beyond what is documented there.</p>"}}}, "CreateIngestConfigurationResponse": {"type": "structure", "members": {"ingestConfiguration": {"shape": "IngestConfiguration", "documentation": "<p>The IngestConfiguration that was created.</p>"}}}, "CreateParticipantTokenRequest": {"type": "structure", "required": ["stageArn"], "members": {"stageArn": {"shape": "StageArn", "documentation": "<p>ARN of the stage to which this token is scoped.</p>"}, "duration": {"shape": "ParticipantTokenDurationMinutes", "documentation": "<p>Duration (in minutes), after which the token expires. Default: 720 (12 hours).</p>"}, "userId": {"shape": "ParticipantTokenUserId", "documentation": "<p>Name that can be specified to help identify the token. This can be any UTF-8 encoded text. <i>This field is exposed to all stage participants and should not be used for personally identifying, confidential, or sensitive information.</i> </p>"}, "attributes": {"shape": "ParticipantTokenAttributes", "documentation": "<p>Application-provided attributes to encode into the token and attach to a stage. Map keys and values can contain UTF-8 encoded text. The maximum length of this field is 1 KB total. <i>This field is exposed to all stage participants and should not be used for personally identifying, confidential, or sensitive information.</i> </p>"}, "capabilities": {"shape": "ParticipantTokenCapabilities", "documentation": "<p>Set of capabilities that the user is allowed to perform in the stage. Default: <code>PUBLISH, SUBSCRIBE</code>.</p>"}}}, "CreateParticipantTokenResponse": {"type": "structure", "members": {"participantToken": {"shape": "ParticipantToken", "documentation": "<p>The participant token that was created.</p>"}}}, "CreateStageRequest": {"type": "structure", "members": {"name": {"shape": "StageName", "documentation": "<p>Optional name that can be specified for the stage being created.</p>"}, "participantTokenConfigurations": {"shape": "ParticipantTokenConfigurations", "documentation": "<p>Array of participant token configuration objects to attach to the new stage.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>Tags attached to the resource. Array of maps, each of the form <code>string:string (key:value)</code>. See <a href=\"https://docs.aws.amazon.com/tag-editor/latest/userguide/best-practices-and-strats.html\">Best practices and strategies</a> in <i>Tagging AWS Resources and Tag Editor</i> for details, including restrictions that apply to tags and \"Tag naming limits and requirements\"; Amazon IVS has no constraints on tags beyond what is documented there. </p>"}, "autoParticipantRecordingConfiguration": {"shape": "AutoParticipantRecordingConfiguration", "documentation": "<p>Configuration object for individual participant recording, to attach to the new stage.</p>"}}}, "CreateStageResponse": {"type": "structure", "members": {"stage": {"shape": "Stage", "documentation": "<p>The stage that was created.</p>"}, "participantTokens": {"shape": "ParticipantTokenList", "documentation": "<p>Participant tokens attached to the stage. These correspond to the <code>participants</code> in the request.</p>"}}}, "CreateStorageConfigurationRequest": {"type": "structure", "required": ["s3"], "members": {"name": {"shape": "StorageConfigurationName", "documentation": "<p>Storage configuration name. The value does not need to be unique.</p>"}, "s3": {"shape": "S3StorageConfiguration", "documentation": "<p>A complex type that contains a storage configuration for where recorded video will be stored.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>Tags attached to the resource. Array of maps, each of the form <code>string:string (key:value)</code>. See <a href=\"https://docs.aws.amazon.com/tag-editor/latest/userguide/best-practices-and-strats.html\">Best practices and strategies</a> in <i>Tagging AWS Resources and Tag Editor</i> for details, including restrictions that apply to tags and \"Tag naming limits and requirements\"; Amazon IVS has no constraints on tags beyond what is documented there.</p>"}}}, "CreateStorageConfigurationResponse": {"type": "structure", "members": {"storageConfiguration": {"shape": "StorageConfiguration", "documentation": "<p>The StorageConfiguration that was created.</p>"}}}, "DeleteEncoderConfigurationRequest": {"type": "structure", "required": ["arn"], "members": {"arn": {"shape": "EncoderConfigurationArn", "documentation": "<p>ARN of the EncoderConfiguration.</p>"}}}, "DeleteEncoderConfigurationResponse": {"type": "structure", "members": {}}, "DeleteIngestConfigurationRequest": {"type": "structure", "required": ["arn"], "members": {"arn": {"shape": "IngestConfigurationArn", "documentation": "<p>ARN of the IngestConfiguration.</p>"}, "force": {"shape": "Boolean", "documentation": "<p>Optional field to force deletion of the IngestConfiguration. If this is set to <code>true</code> when a participant is actively publishing, the participant is disconnected from the stage, followed by deletion of the IngestConfiguration. Default: <code>false</code>.</p>"}}}, "DeleteIngestConfigurationResponse": {"type": "structure", "members": {}}, "DeletePublicKeyRequest": {"type": "structure", "required": ["arn"], "members": {"arn": {"shape": "PublicKeyArn", "documentation": "<p>ARN of the public key to be deleted.</p>"}}}, "DeletePublicKeyResponse": {"type": "structure", "members": {}}, "DeleteStageRequest": {"type": "structure", "required": ["arn"], "members": {"arn": {"shape": "StageArn", "documentation": "<p>ARN of the stage to be deleted.</p>"}}}, "DeleteStageResponse": {"type": "structure", "members": {}}, "DeleteStorageConfigurationRequest": {"type": "structure", "required": ["arn"], "members": {"arn": {"shape": "StorageConfigurationArn", "documentation": "<p>ARN of the storage configuration to be deleted.</p>"}}}, "DeleteStorageConfigurationResponse": {"type": "structure", "members": {}}, "Destination": {"type": "structure", "required": ["id", "state", "configuration"], "members": {"id": {"shape": "String", "documentation": "<p>Unique identifier for this destination, assigned by IVS.</p>"}, "state": {"shape": "DestinationState", "documentation": "<p>State of the Composition Destination.</p>"}, "startTime": {"shape": "Time", "documentation": "<p>UTC time of the destination start. This is an ISO 8601 timestamp; <i>note that this is returned as a string</i>.</p>"}, "endTime": {"shape": "Time", "documentation": "<p>UTC time of the destination end. This is an ISO 8601 timestamp; <i>note that this is returned as a string</i>.</p>"}, "configuration": {"shape": "DestinationConfiguration", "documentation": "<p>Configuration used to create this destination.</p>"}, "detail": {"shape": "DestinationDetail", "documentation": "<p>Optional details regarding the status of the destination.</p>"}}, "documentation": "<p>Object specifying the status of a Destination.</p>"}, "DestinationConfiguration": {"type": "structure", "members": {"name": {"shape": "DestinationConfigurationName", "documentation": "<p>Name that can be specified to help identify the destination.</p>"}, "channel": {"shape": "ChannelDestinationConfiguration", "documentation": "<p>An IVS channel to be used for broadcasting, for server-side composition. Either a <code>channel</code> or an <code>s3</code> must be specified. </p>"}, "s3": {"shape": "S3DestinationConfiguration", "documentation": "<p>An S3 storage configuration to be used for recording video data. Either a <code>channel</code> or an <code>s3</code> must be specified.</p>"}}, "documentation": "<p>Complex data type that defines destination-configuration objects.</p>"}, "DestinationConfigurationList": {"type": "list", "member": {"shape": "DestinationConfiguration"}, "max": 2, "min": 1}, "DestinationConfigurationName": {"type": "string", "max": 128, "min": 0, "pattern": "[a-zA-Z0-9-_]*"}, "DestinationDetail": {"type": "structure", "members": {"s3": {"shape": "S3Detail", "documentation": "<p>An S3 detail object to return information about the S3 destination.</p>"}}, "documentation": "<p>Complex data type that defines destination-detail objects.</p>"}, "DestinationList": {"type": "list", "member": {"shape": "Destination"}, "max": 2, "min": 1}, "DestinationState": {"type": "string", "enum": ["STARTING", "ACTIVE", "STOPPING", "RECONNECTING", "FAILED", "STOPPED"]}, "DestinationSummary": {"type": "structure", "required": ["id", "state"], "members": {"id": {"shape": "String", "documentation": "<p>Unique identifier for this destination, assigned by IVS.</p>"}, "state": {"shape": "DestinationState", "documentation": "<p>State of the Composition Destination.</p>"}, "startTime": {"shape": "Time", "documentation": "<p>UTC time of the destination start. This is an ISO 8601 timestamp; <i>note that this is returned as a string</i>.</p>"}, "endTime": {"shape": "Time", "documentation": "<p>UTC time of the destination end. This is an ISO 8601 timestamp; <i>note that this is returned as a string</i>.</p>"}}, "documentation": "<p>Summary information about a Destination.</p>"}, "DestinationSummaryList": {"type": "list", "member": {"shape": "DestinationSummary"}, "max": 2, "min": 1}, "DisconnectParticipantReason": {"type": "string", "max": 128, "min": 0}, "DisconnectParticipantRequest": {"type": "structure", "required": ["stageArn", "participantId"], "members": {"stageArn": {"shape": "StageArn", "documentation": "<p>ARN of the stage to which the participant is attached.</p>"}, "participantId": {"shape": "ParticipantTokenId", "documentation": "<p>Identifier of the participant to be disconnected. IVS assigns this; it is returned by <a>CreateParticipantToken</a> (for streams using WebRTC ingest) or <a>CreateIngestConfiguration</a> (for streams using RTMP ingest).</p>"}, "reason": {"shape": "DisconnectParticipantReason", "documentation": "<p>Description of why this participant is being disconnected.</p>"}}}, "DisconnectParticipantResponse": {"type": "structure", "members": {}}, "EncoderConfiguration": {"type": "structure", "required": ["arn"], "members": {"arn": {"shape": "EncoderConfigurationArn", "documentation": "<p>ARN of the EncoderConfiguration resource.</p>"}, "name": {"shape": "EncoderConfigurationName", "documentation": "<p>Optional name to identify the resource.</p>"}, "video": {"shape": "Video", "documentation": "<p>Video configuration. Default: video resolution 1280x720, bitrate 2500 kbps, 30 fps</p>"}, "tags": {"shape": "Tags", "documentation": "<p>Tags attached to the resource. Array of maps, each of the form <code>string:string (key:value)</code>. See <a href=\"https://docs.aws.amazon.com/tag-editor/latest/userguide/best-practices-and-strats.html\">Best practices and strategies</a> in <i>Tagging AWS Resources and Tag Editor</i> for details, including restrictions that apply to tags and \"Tag naming limits and requirements\"; Amazon IVS has no constraints on tags beyond what is documented there.</p>"}}, "documentation": "<p>Settings for transcoding.</p>"}, "EncoderConfigurationArn": {"type": "string", "max": 128, "min": 1, "pattern": "arn:aws:ivs:[a-z0-9-]+:[0-9]+:encoder-configuration/[a-zA-Z0-9-]+"}, "EncoderConfigurationArnList": {"type": "list", "member": {"shape": "EncoderConfigurationArn"}, "max": 1, "min": 1}, "EncoderConfigurationName": {"type": "string", "max": 128, "min": 0, "pattern": "[a-zA-Z0-9-_]*"}, "EncoderConfigurationSummary": {"type": "structure", "required": ["arn"], "members": {"arn": {"shape": "EncoderConfigurationArn", "documentation": "<p>ARN of the EncoderConfiguration resource.</p>"}, "name": {"shape": "EncoderConfigurationName", "documentation": "<p>Optional name to identify the resource.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>Tags attached to the resource. Array of maps, each of the form <code>string:string (key:value)</code>. See <a href=\"https://docs.aws.amazon.com/tag-editor/latest/userguide/best-practices-and-strats.html\">Best practices and strategies</a> in <i>Tagging AWS Resources and Tag Editor</i> for details, including restrictions that apply to tags and \"Tag naming limits and requirements\"; Amazon IVS has no constraints on tags beyond what is documented there.</p>"}}, "documentation": "<p>Summary information about an EncoderConfiguration.</p>"}, "EncoderConfigurationSummaryList": {"type": "list", "member": {"shape": "EncoderConfigurationSummary"}}, "Event": {"type": "structure", "members": {"name": {"shape": "EventName", "documentation": "<p>The name of the event.</p>"}, "participantId": {"shape": "ParticipantId", "documentation": "<p>Unique identifier for the participant who triggered the event. This is assigned by IVS.</p>"}, "eventTime": {"shape": "Time", "documentation": "<p>ISO 8601 timestamp (returned as a string) for when the event occurred.</p>"}, "remoteParticipantId": {"shape": "ParticipantId", "documentation": "<p>Unique identifier for the remote participant. For a subscribe event, this is the publisher. For a publish or join event, this is null. This is assigned by IVS.</p>"}, "errorCode": {"shape": "EventErrorCode", "documentation": "<p>If the event is an error event, the error code is provided to give insight into the specific error that occurred. If the event is not an error event, this field is null.</p> <ul> <li> <p> <code>B_FRAME_PRESENT</code> — The participant's stream includes B-frames. For details, see <a href=\"https://docs.aws.amazon.com/ivs/latest/RealTimeUserGuide/rt-rtmp-publishing.html\"> IVS RTMP Publishing</a>.</p> </li> <li> <p> <code>BITRATE_EXCEEDED</code> — The participant exceeded the maximum supported bitrate. For details, see <a href=\"https://docs.aws.amazon.com/ivs/latest/RealTimeUserGuide/service-quotas.html\"> Service Quotas</a>.</p> </li> <li> <p> <code>INSUFFICIENT_CAPABILITIES</code> — The participant tried to take an action that the participant’s token is not allowed to do. For details on participant capabilities, see the <code>capabilities</code> field in <a>CreateParticipantToken</a>.</p> </li> <li> <p> <code>INTERNAL_SERVER_EXCEPTION</code> — The participant failed to publish to the stage due to an internal server error.</p> </li> <li> <p> <code>INVALID_AUDIO_CODEC</code> — The participant is using an invalid audio codec. For details, see <a href=\"https://docs.aws.amazon.com/ivs/latest/RealTimeUserGuide/rt-stream-ingest.html\"> Stream Ingest</a>.</p> </li> <li> <p> <code>INVALID_INPUT</code> — The participant is using an invalid input stream.</p> </li> <li> <p> <code>INVALID_PROTOCOL</code> — The participant's IngestConfiguration resource is configured for RTMPS but they tried streaming with RTMP. For details, see <a href=\"https://docs.aws.amazon.com/ivs/latest/RealTimeUserGuide/rt-rtmp-publishing.html\"> IVS RTMP Publishing</a>.</p> </li> <li> <p> <code>INVALID_STREAM_KEY</code> — The participant is using an invalid stream key. For details, see <a href=\"https://docs.aws.amazon.com/ivs/latest/RealTimeUserGuide/rt-rtmp-publishing.html\"> IVS RTMP Publishing</a>.</p> </li> <li> <p> <code>INVALID_VIDEO_CODEC</code> — The participant is using an invalid video codec. For details, see <a href=\"https://docs.aws.amazon.com/ivs/latest/RealTimeUserGuide/rt-stream-ingest.html\"> Stream Ingest</a>.</p> </li> <li> <p> <code>PUBLISHER_NOT_FOUND</code> — The participant tried to subscribe to a publisher that doesn’t exist.</p> </li> <li> <p> <code>QUOTA_EXCEEDED</code> — The number of participants who want to publish/subscribe to a stage exceeds the quota. For details, see <a href=\"https://docs.aws.amazon.com/ivs/latest/RealTimeUserGuide/service-quotas.html\"> Service Quotas</a>.</p> </li> <li> <p> <code>RESOLUTION_EXCEEDED</code> — The participant exceeded the maximum supported resolution. For details, see <a href=\"https://docs.aws.amazon.com/ivs/latest/RealTimeUserGuide/service-quotas.html\"> Service Quotas</a>.</p> </li> <li> <p> <code>REUSE_OF_STREAM_KEY</code> — The participant tried to use a stream key that is associated with another active stage session.</p> </li> <li> <p> <code>STREAM_DURATION_EXCEEDED</code> — The participant exceeded the maximum allowed stream duration. For details, see <a href=\"https://docs.aws.amazon.com/ivs/latest/RealTimeUserGuide/service-quotas.html\"> Service Quotas</a>.</p> </li> </ul>"}, "destinationStageArn": {"shape": "StageArn", "documentation": "<p>ARN of the stage where the participant is replicated. Applicable only if the event name is <code>REPLICATION_STARTED</code> or <code>REPLICATION_STOPPED</code>.</p>"}, "destinationSessionId": {"shape": "StageSessionId", "documentation": "<p>ID of the session within the destination stage. Applicable only if the event name is <code>REPLICATION_STARTED</code> or <code>REPLICATION_STOPPED</code>.</p>"}, "replica": {"shape": "Replica", "documentation": "<p>If true, this indicates the <code>participantId</code> is a replicated participant. If this is a subscribe event, then this flag refers to <code>remoteParticipantId</code>.</p>"}}, "documentation": "<p>An occurrence during a stage session.</p>"}, "EventErrorCode": {"type": "string", "enum": ["INSUFFICIENT_CAPABILITIES", "QUOTA_EXCEEDED", "PUBLISHER_NOT_FOUND", "BITRATE_EXCEEDED", "RESOLUTION_EXCEEDED", "STREAM_DURATION_EXCEEDED", "INVALID_AUDIO_CODEC", "INVALID_VIDEO_CODEC", "INVALID_PROTOCOL", "INVALID_STREAM_KEY", "REUSE_OF_STREAM_KEY", "B_FRAME_PRESENT", "INVALID_INPUT", "INTERNAL_SERVER_EXCEPTION"]}, "EventList": {"type": "list", "member": {"shape": "Event"}}, "EventName": {"type": "string", "enum": ["JOINED", "LEFT", "PUBLISH_STARTED", "PUBLISH_STOPPED", "SUBSCRIBE_STARTED", "SUBSCRIBE_STOPPED", "PUBLISH_ERROR", "SUBSCRIBE_ERROR", "JOIN_ERROR", "REPLICATION_STARTED", "REPLICATION_STOPPED"]}, "Framerate": {"type": "float", "box": true, "max": 60, "min": 1}, "GetCompositionRequest": {"type": "structure", "required": ["arn"], "members": {"arn": {"shape": "CompositionArn", "documentation": "<p>ARN of the Composition resource.</p>"}}}, "GetCompositionResponse": {"type": "structure", "members": {"composition": {"shape": "Composition", "documentation": "<p>The Composition that was returned.</p>"}}}, "GetEncoderConfigurationRequest": {"type": "structure", "required": ["arn"], "members": {"arn": {"shape": "EncoderConfigurationArn", "documentation": "<p>ARN of the EncoderConfiguration resource.</p>"}}}, "GetEncoderConfigurationResponse": {"type": "structure", "members": {"encoderConfiguration": {"shape": "EncoderConfiguration", "documentation": "<p>The EncoderConfiguration that was returned.</p>"}}}, "GetIngestConfigurationRequest": {"type": "structure", "required": ["arn"], "members": {"arn": {"shape": "IngestConfigurationArn", "documentation": "<p>ARN of the ingest for which the information is to be retrieved.</p>"}}}, "GetIngestConfigurationResponse": {"type": "structure", "members": {"ingestConfiguration": {"shape": "IngestConfiguration", "documentation": "<p>The IngestConfiguration that was returned.</p>"}}}, "GetParticipantRequest": {"type": "structure", "required": ["stageArn", "sessionId", "participantId"], "members": {"stageArn": {"shape": "StageArn", "documentation": "<p>Stage ARN.</p>"}, "sessionId": {"shape": "StageSessionId", "documentation": "<p>ID of a session within the stage.</p>"}, "participantId": {"shape": "ParticipantId", "documentation": "<p>Unique identifier for the participant. This is assigned by IVS and returned by <a>CreateParticipantToken</a>.</p>"}}}, "GetParticipantResponse": {"type": "structure", "members": {"participant": {"shape": "Participant", "documentation": "<p>The participant that is returned.</p>"}}}, "GetPublicKeyRequest": {"type": "structure", "required": ["arn"], "members": {"arn": {"shape": "PublicKeyArn", "documentation": "<p>ARN of the public key for which the information is to be retrieved.</p>"}}}, "GetPublicKeyResponse": {"type": "structure", "members": {"publicKey": {"shape": "PublicKey", "documentation": "<p>The public key that is returned.</p>"}}}, "GetStageRequest": {"type": "structure", "required": ["arn"], "members": {"arn": {"shape": "StageArn", "documentation": "<p>ARN of the stage for which the information is to be retrieved.</p>"}}}, "GetStageResponse": {"type": "structure", "members": {"stage": {"shape": "Stage", "documentation": "<p>The stage that is returned.</p>"}}}, "GetStageSessionRequest": {"type": "structure", "required": ["stageArn", "sessionId"], "members": {"stageArn": {"shape": "StageArn", "documentation": "<p>ARN of the stage for which the information is to be retrieved.</p>"}, "sessionId": {"shape": "StageSessionId", "documentation": "<p>ID of a session within the stage.</p>"}}}, "GetStageSessionResponse": {"type": "structure", "members": {"stageSession": {"shape": "StageSession", "documentation": "<p>The stage session that is returned.</p>"}}}, "GetStorageConfigurationRequest": {"type": "structure", "required": ["arn"], "members": {"arn": {"shape": "StorageConfigurationArn", "documentation": "<p>ARN of the storage configuration to be retrieved.</p>"}}}, "GetStorageConfigurationResponse": {"type": "structure", "members": {"storageConfiguration": {"shape": "StorageConfiguration", "documentation": "<p>The StorageConfiguration that was returned.</p>"}}}, "GridConfiguration": {"type": "structure", "members": {"featuredParticipantAttribute": {"shape": "AttributeKey", "documentation": "<p>This attribute name identifies the featured slot. A participant with this attribute set to <code>\"true\"</code> (as a string value) in <a>ParticipantTokenConfiguration</a> is placed in the featured slot. Default: <code>\"\"</code> (no featured participant).</p>"}, "omitStoppedVideo": {"shape": "OmitStoppedVideo", "documentation": "<p>Determines whether to omit participants with stopped video in the composition. Default: <code>false</code>.</p>"}, "videoAspectRatio": {"shape": "VideoAspectRatio", "documentation": "<p>Sets the non-featured participant display mode, to control the aspect ratio of video tiles. <code>VIDEO</code> is 16:9, <code>SQUARE</code> is 1:1, and <code>PORTRAIT</code> is 3:4. Default: <code>VIDEO</code>.</p>"}, "videoFillMode": {"shape": "VideoFillMode", "documentation": "<p>Defines how video content fits within the participant tile: <code>FILL</code> (stretched), <code>COVER</code> (cropped), or <code>CONTAIN</code> (letterboxed). When not set, <code>videoFillMode</code> defaults to <code>COVER</code> fill mode for participants in the grid and to <code>CONTAIN</code> fill mode for featured participants.</p>"}, "gridGap": {"shape": "GridGap", "documentation": "<p>Specifies the spacing between participant tiles in pixels. Default: <code>2</code>.</p>"}}, "documentation": "<p>Configuration information specific to Grid layout, for server-side composition. See \"Layouts\" in <a href=\"https://docs.aws.amazon.com/ivs/latest/RealTimeUserGuide/server-side-composition.html\">Server-Side Composition</a>.</p>"}, "GridGap": {"type": "integer", "min": 0}, "Height": {"type": "integer", "box": true, "max": 1920, "min": 2}, "ImportPublicKeyRequest": {"type": "structure", "required": ["publicKeyMaterial"], "members": {"publicKeyMaterial": {"shape": "PublicKeyMaterial", "documentation": "<p>The content of the public key to be imported.</p>"}, "name": {"shape": "PublicKeyName", "documentation": "<p>Name of the public key to be imported.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>Tags attached to the resource. Array of maps, each of the form <code>string:string (key:value)</code>. See <a href=\"https://docs.aws.amazon.com/tag-editor/latest/userguide/best-practices-and-strats.html\">Best practices and strategies</a> in <i>Tagging AWS Resources and Tag Editor</i> for details, including restrictions that apply to tags and \"Tag naming limits and requirements\"; Amazon IVS has no constraints on tags beyond what is documented there.</p>"}}}, "ImportPublicKeyResponse": {"type": "structure", "members": {"publicKey": {"shape": "PublicKey", "documentation": "<p>The public key that was imported.</p>"}}}, "IngestConfiguration": {"type": "structure", "required": ["arn", "ingestProtocol", "streamKey", "stageArn", "participantId", "state"], "members": {"name": {"shape": "IngestConfigurationName", "documentation": "<p>Ingest name</p>"}, "arn": {"shape": "IngestConfigurationArn", "documentation": "<p>Ingest configuration ARN.</p>"}, "ingestProtocol": {"shape": "IngestProtocol", "documentation": "<p>Type of ingest protocol that the user employs for broadcasting.</p>"}, "streamKey": {"shape": "StreamKey", "documentation": "<p>Ingest-key value for the RTMP(S) protocol.</p>"}, "stageArn": {"shape": "IngestConfigurationStageArn", "documentation": "<p>ARN of the stage with which the IngestConfiguration is associated.</p>"}, "participantId": {"shape": "ParticipantId", "documentation": "<p>ID of the participant within the stage.</p>"}, "state": {"shape": "IngestConfigurationState", "documentation": "<p>State of the ingest configuration. It is <code>ACTIVE</code> if a publisher currently is publishing to the stage associated with the ingest configuration.</p>"}, "userId": {"shape": "UserId", "documentation": "<p>Customer-assigned name to help identify the participant using the IngestConfiguration; this can be used to link a participant to a user in the customer’s own systems. This can be any UTF-8 encoded text. <i>This field is exposed to all stage participants and should not be used for personally identifying, confidential, or sensitive information.</i> </p>"}, "attributes": {"shape": "ParticipantAttributes", "documentation": "<p>Application-provided attributes to to store in the IngestConfiguration and attach to a stage. Map keys and values can contain UTF-8 encoded text. The maximum length of this field is 1 KB total. <i>This field is exposed to all stage participants and should not be used for personally identifying, confidential, or sensitive information.</i> </p>"}, "tags": {"shape": "Tags", "documentation": "<p>Tags attached to the resource. Array of maps, each of the form <code>string:string (key:value)</code>. See <a href=\"https://docs.aws.amazon.com/tag-editor/latest/userguide/best-practices-and-strats.html\">Best practices and strategies</a> in <i>Tagging AWS Resources and Tag Editor</i> for details, including restrictions that apply to tags and \"Tag naming limits and requirements\"; Amazon IVS has no constraints on tags beyond what is documented there.</p>"}}, "documentation": "<p>Object specifying an ingest configuration.</p>"}, "IngestConfigurationArn": {"type": "string", "max": 128, "min": 1, "pattern": "arn:aws:ivs:[a-z0-9-]+:[0-9]+:ingest-configuration/[a-zA-Z0-9-]+"}, "IngestConfigurationList": {"type": "list", "member": {"shape": "IngestConfigurationSummary"}}, "IngestConfigurationName": {"type": "string", "max": 128, "min": 0, "pattern": "[a-zA-Z0-9-_]*"}, "IngestConfigurationStageArn": {"type": "string", "max": 128, "min": 0, "pattern": "^$|^arn:aws:ivs:[a-z0-9-]+:[0-9]+:stage/[a-zA-Z0-9-]+$"}, "IngestConfigurationState": {"type": "string", "enum": ["ACTIVE", "INACTIVE"]}, "IngestConfigurationSummary": {"type": "structure", "required": ["arn", "ingestProtocol", "stageArn", "participantId", "state"], "members": {"name": {"shape": "IngestConfigurationName", "documentation": "<p>Ingest name.</p>"}, "arn": {"shape": "IngestConfigurationArn", "documentation": "<p>Ingest configuration ARN.</p>"}, "ingestProtocol": {"shape": "IngestProtocol", "documentation": "<p>Type of ingest protocol that the user employs for broadcasting.</p>"}, "stageArn": {"shape": "IngestConfigurationStageArn", "documentation": "<p>ARN of the stage with which the IngestConfiguration is associated.</p>"}, "participantId": {"shape": "ParticipantId", "documentation": "<p>ID of the participant within the stage.</p>"}, "state": {"shape": "IngestConfigurationState", "documentation": "<p>State of the ingest configuration. It is <code>ACTIVE</code> if a publisher currently is publishing to the stage associated with the ingest configuration.</p>"}, "userId": {"shape": "UserId", "documentation": "<p>Customer-assigned name to help identify the participant using the IngestConfiguration; this can be used to link a participant to a user in the customer’s own systems. This can be any UTF-8 encoded text. <i>This field is exposed to all stage participants and should not be used for personally identifying, confidential, or sensitive information.</i> </p>"}}, "documentation": "<p>Summary information about an IngestConfiguration.</p>"}, "IngestProtocol": {"type": "string", "enum": ["RTMP", "RTMPS"]}, "InsecureIngest": {"type": "boolean"}, "InternalServerException": {"type": "structure", "members": {"accessControlAllowOrigin": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "Access-Control-Allow-Origin"}, "accessControlExposeHeaders": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "Access-Control-Expose-Headers"}, "cacheControl": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "Cache-Control"}, "contentSecurityPolicy": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "Content-Security-Policy"}, "strictTransportSecurity": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "Strict-Transport-Security"}, "xContentTypeOptions": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "X-Content-Type-Options"}, "xFrameOptions": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "X-Frame-Options"}, "xAmznErrorType": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "x-amzn-ErrorType"}, "exceptionMessage": {"shape": "errorMessage", "documentation": "<p>Unexpected error during processing of request.</p>"}}, "documentation": "<p/>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true}, "LayoutConfiguration": {"type": "structure", "members": {"grid": {"shape": "GridConfiguration", "documentation": "<p>Configuration related to grid layout. Default: Grid layout.</p>"}, "pip": {"shape": "PipConfiguration", "documentation": "<p>Configuration related to PiP layout.</p>"}}, "documentation": "<p>Configuration information of supported layouts for server-side composition.</p>"}, "ListCompositionsRequest": {"type": "structure", "members": {"filterByStageArn": {"shape": "StageArn", "documentation": "<p>Filters the Composition list to match the specified Stage ARN.</p>"}, "filterByEncoderConfigurationArn": {"shape": "EncoderConfigurationArn", "documentation": "<p>Filters the Composition list to match the specified EncoderConfiguration attached to at least one of its output.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The first Composition to retrieve. This is used for pagination; see the <code>nextToken</code> response field.</p>"}, "maxResults": {"shape": "MaxCompositionResults", "documentation": "<p>Maximum number of results to return. Default: 100.</p>"}}}, "ListCompositionsResponse": {"type": "structure", "required": ["compositions"], "members": {"compositions": {"shape": "CompositionSummaryList", "documentation": "<p>List of the matching Compositions (summary information only).</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>If there are more compositions than <code>maxResults</code>, use <code>nextToken</code> in the request to get the next set.</p>"}}}, "ListEncoderConfigurationsRequest": {"type": "structure", "members": {"nextToken": {"shape": "PaginationToken", "documentation": "<p>The first encoder configuration to retrieve. This is used for pagination; see the <code>nextToken</code> response field.</p>"}, "maxResults": {"shape": "MaxEncoderConfigurationResults", "documentation": "<p>Maximum number of results to return. Default: 100.</p>"}}}, "ListEncoderConfigurationsResponse": {"type": "structure", "required": ["encoderConfigurations"], "members": {"encoderConfigurations": {"shape": "EncoderConfigurationSummaryList", "documentation": "<p>List of the matching EncoderConfigurations (summary information only).</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>If there are more encoder configurations than <code>maxResults</code>, use <code>nextToken</code> in the request to get the next set.</p>"}}}, "ListIngestConfigurationsRequest": {"type": "structure", "members": {"filterByStageArn": {"shape": "StageArn", "documentation": "<p>Filters the response list to match the specified stage ARN. Only one filter (by stage ARN or by state) can be used at a time.</p>"}, "filterByState": {"shape": "IngestConfigurationState", "documentation": "<p>Filters the response list to match the specified state. Only one filter (by stage ARN or by state) can be used at a time.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The first IngestConfiguration to retrieve. This is used for pagination; see the <code>nextToken</code> response field.</p>"}, "maxResults": {"shape": "MaxIngestConfigurationResults", "documentation": "<p>Maximum number of results to return. Default: 50.</p>"}}}, "ListIngestConfigurationsResponse": {"type": "structure", "required": ["ingestConfigurations"], "members": {"ingestConfigurations": {"shape": "IngestConfigurationList", "documentation": "<p>List of the matching ingest configurations (summary information only).</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>If there are more IngestConfigurations than <code>maxResults</code>, use <code>nextToken</code> in the request to get the next set.</p>"}}}, "ListParticipantEventsRequest": {"type": "structure", "required": ["stageArn", "sessionId", "participantId"], "members": {"stageArn": {"shape": "StageArn", "documentation": "<p>Stage ARN.</p>"}, "sessionId": {"shape": "StageSessionId", "documentation": "<p>ID of a session within the stage.</p>"}, "participantId": {"shape": "ParticipantId", "documentation": "<p>Unique identifier for this participant. This is assigned by IVS and returned by <a>CreateParticipantToken</a>.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The first participant event to retrieve. This is used for pagination; see the <code>nextToken</code> response field.</p>"}, "maxResults": {"shape": "MaxParticipantEventResults", "documentation": "<p>Maximum number of results to return. Default: 50.</p>"}}}, "ListParticipantEventsResponse": {"type": "structure", "required": ["events"], "members": {"events": {"shape": "EventList", "documentation": "<p>List of the matching events.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>If there are more events than <code>maxResults</code>, use <code>nextToken</code> in the request to get the next set. </p>"}}}, "ListParticipantReplicasRequest": {"type": "structure", "required": ["sourceStageArn", "participantId"], "members": {"sourceStageArn": {"shape": "StageArn", "documentation": "<p>ARN of the stage where the participant is publishing.</p>"}, "participantId": {"shape": "ParticipantId", "documentation": "<p>Participant ID of the publisher that has been replicated. This is assigned by IVS and returned by <a>CreateParticipantToken</a> or the <code>jti</code> (JWT ID) used to <a href=\"https://docs.aws.amazon.com/ivs/latest/RealTimeUserGuide/getting-started-distribute-tokens.html#getting-started-distribute-tokens-self-signed\">create a self signed token</a>.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The first participant to retrieve. This is used for pagination; see the <code>nextToken</code> response field.</p>"}, "maxResults": {"shape": "MaxParticipantReplicaResults", "documentation": "<p>Maximum number of results to return. Default: 50.</p>"}}}, "ListParticipantReplicasResponse": {"type": "structure", "required": ["replicas"], "members": {"replicas": {"shape": "ParticipantReplicaList", "documentation": "<p>List of all participant replicas.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>If there are more participants than <code>maxResults</code>, use <code>nextToken</code> in the request to get the next set.</p>"}}}, "ListParticipantsRequest": {"type": "structure", "required": ["stageArn", "sessionId"], "members": {"stageArn": {"shape": "StageArn", "documentation": "<p>Stage ARN.</p>"}, "sessionId": {"shape": "StageSessionId", "documentation": "<p>ID of the session within the stage.</p>"}, "filterByUserId": {"shape": "UserId", "documentation": "<p>Filters the response list to match the specified user ID. Only one of <code>filterByUserId</code>, <code>filterByPublished</code>, <code>filterByState</code>, or <code>filterByRecordingState</code> can be provided per request. A <code>userId</code> is a customer-assigned name to help identify the token; this can be used to link a participant to a user in the customer’s own systems.</p>"}, "filterByPublished": {"shape": "Published", "documentation": "<p>Filters the response list to only show participants who published during the stage session. Only one of <code>filterByUserId</code>, <code>filterByPublished</code>, <code>filterByState</code>, or <code>filterByRecordingState</code> can be provided per request.</p>"}, "filterByState": {"shape": "ParticipantState", "documentation": "<p>Filters the response list to only show participants in the specified state. Only one of <code>filterByUserId</code>, <code>filterByPublished</code>, <code>filterByState</code>, or <code>filterByRecordingState</code> can be provided per request.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The first participant to retrieve. This is used for pagination; see the <code>nextToken</code> response field.</p>"}, "maxResults": {"shape": "MaxParticipantResults", "documentation": "<p>Maximum number of results to return. Default: 50.</p>"}, "filterByRecordingState": {"shape": "ParticipantRecordingFilterByRecordingState", "documentation": "<p>Filters the response list to only show participants with the specified recording state. Only one of <code>filterByUserId</code>, <code>filterByPublished</code>, <code>filterByState</code>, or <code>filterByRecordingState</code> can be provided per request.</p>"}}}, "ListParticipantsResponse": {"type": "structure", "required": ["participants"], "members": {"participants": {"shape": "ParticipantList", "documentation": "<p>List of the matching participants (summary information only).</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>If there are more participants than <code>maxResults</code>, use <code>nextToken</code> in the request to get the next set.</p>"}}}, "ListPublicKeysRequest": {"type": "structure", "members": {"nextToken": {"shape": "PaginationToken", "documentation": "<p>The first public key to retrieve. This is used for pagination; see the <code>nextToken</code> response field.</p>"}, "maxResults": {"shape": "MaxPublicKeyResults", "documentation": "<p>Maximum number of results to return. Default: 50.</p>"}}}, "ListPublicKeysResponse": {"type": "structure", "required": ["publicKeys"], "members": {"publicKeys": {"shape": "PublicKeyList", "documentation": "<p>List of the matching public keys (summary information only).</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>If there are more public keys than <code>maxResults</code>, use <code>nextToken</code> in the request to get the next set.</p>"}}}, "ListStageSessionsRequest": {"type": "structure", "required": ["stageArn"], "members": {"stageArn": {"shape": "StageArn", "documentation": "<p>Stage ARN.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The first stage session to retrieve. This is used for pagination; see the <code>nextToken</code> response field.</p>"}, "maxResults": {"shape": "MaxStageSessionResults", "documentation": "<p>Maximum number of results to return. Default: 50.</p>"}}}, "ListStageSessionsResponse": {"type": "structure", "required": ["stageSessions"], "members": {"stageSessions": {"shape": "StageSessionList", "documentation": "<p>List of matching stage sessions.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>If there are more stage sessions than <code>maxResults</code>, use <code>nextToken</code> in the request to get the next set.</p>"}}}, "ListStagesRequest": {"type": "structure", "members": {"nextToken": {"shape": "PaginationToken", "documentation": "<p>The first stage to retrieve. This is used for pagination; see the <code>nextToken</code> response field.</p>"}, "maxResults": {"shape": "MaxStageResults", "documentation": "<p>Maximum number of results to return. Default: 50.</p>"}}}, "ListStagesResponse": {"type": "structure", "required": ["stages"], "members": {"stages": {"shape": "StageSummaryList", "documentation": "<p>List of the matching stages (summary information only).</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>If there are more stages than <code>maxResults</code>, use <code>nextToken</code> in the request to get the next set.</p>"}}}, "ListStorageConfigurationsRequest": {"type": "structure", "members": {"nextToken": {"shape": "PaginationToken", "documentation": "<p>The first storage configuration to retrieve. This is used for pagination; see the <code>nextToken</code> response field.</p>"}, "maxResults": {"shape": "MaxStorageConfigurationResults", "documentation": "<p>Maximum number of storage configurations to return. Default: your service quota or 100, whichever is smaller.</p>"}}}, "ListStorageConfigurationsResponse": {"type": "structure", "required": ["storageConfigurations"], "members": {"storageConfigurations": {"shape": "StorageConfigurationSummaryList", "documentation": "<p>List of the matching storage configurations.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>If there are more storage configurations than <code>maxResults</code>, use <code>nextToken</code> in the request to get the next set.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "ResourceArn", "documentation": "<p>The ARN of the resource to be retrieved. The ARN must be URL-encoded.</p>", "location": "uri", "locationName": "resourceArn"}}}, "ListTagsForResourceResponse": {"type": "structure", "required": ["tags"], "members": {"tags": {"shape": "Tags", "documentation": "<p>Tags attached to the resource. Array of maps, each of the form <code>string:string (key:value)</code>.</p>"}}}, "MaxCompositionResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "MaxEncoderConfigurationResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "MaxIngestConfigurationResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "MaxParticipantEventResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "MaxParticipantReplicaResults": {"type": "integer", "box": true, "max": 50, "min": 1}, "MaxParticipantResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "MaxPublicKeyResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "MaxStageResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "MaxStageSessionResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "MaxStorageConfigurationResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "OmitStoppedVideo": {"type": "boolean"}, "PaginationToken": {"type": "string", "max": 1024, "min": 0, "pattern": "[a-zA-Z0-9+/=_-]*"}, "Participant": {"type": "structure", "members": {"participantId": {"shape": "ParticipantId", "documentation": "<p>Unique identifier for this participant, assigned by IVS.</p>"}, "userId": {"shape": "UserId", "documentation": "<p>Customer-assigned name to help identify the token; this can be used to link a participant to a user in the customer’s own systems. This can be any UTF-8 encoded text. <i>This field is exposed to all stage participants and should not be used for personally identifying, confidential, or sensitive information</i>.</p>"}, "state": {"shape": "ParticipantState", "documentation": "<p>Whether the participant is connected to or disconnected from the stage.</p>"}, "firstJoinTime": {"shape": "Time", "documentation": "<p>ISO 8601 timestamp (returned as a string) when the participant first joined the stage session.</p>"}, "attributes": {"shape": "ParticipantAttributes", "documentation": "<p>Application-provided attributes to encode into the token and attach to a stage. Map keys and values can contain UTF-8 encoded text. The maximum length of this field is 1 KB total. <i>This field is exposed to all stage participants and should not be used for personally identifying, confidential, or sensitive information</i>.</p>"}, "published": {"shape": "Published", "documentation": "<p>Whether the participant ever published to the stage session.</p>"}, "ispName": {"shape": "ParticipantClientAttribute", "documentation": "<p>The participant’s Internet Service Provider.</p>"}, "osName": {"shape": "ParticipantClientAttribute", "documentation": "<p>The participant’s operating system.</p>"}, "osVersion": {"shape": "ParticipantClientAttribute", "documentation": "<p>The participant’s operating system version.</p>"}, "browserName": {"shape": "ParticipantClientAttribute", "documentation": "<p>The participant’s browser.</p>"}, "browserVersion": {"shape": "ParticipantClientAttribute", "documentation": "<p>The participant’s browser version.</p>"}, "sdkVersion": {"shape": "ParticipantClientAttribute", "documentation": "<p>The participant’s SDK version.</p>"}, "recordingS3BucketName": {"shape": "ParticipantRecordingS3BucketName", "documentation": "<p>Name of the S3 bucket to where the participant is being recorded, if individual participant recording is enabled, or <code>\"\"</code> (empty string), if recording is not enabled.</p>"}, "recordingS3Prefix": {"shape": "ParticipantRecordingS3Prefix", "documentation": "<p>S3 prefix of the S3 bucket where the participant is being recorded, if individual participant recording is enabled, or <code>\"\"</code> (empty string), if recording is not enabled. If individual participant recording merge is enabled, and if a stage publisher disconnects from a stage and then reconnects, IVS tries to record to the same S3 prefix as the previous session. See <a href=\"/ivs/latest/RealTimeUserGuide/rt-individual-participant-recording.html#ind-part-rec-merge-frag\"> Merge Fragmented Individual Participant Recordings</a>.</p>"}, "recordingState": {"shape": "ParticipantRecordingState", "documentation": "<p>The participant’s recording state.</p>"}, "protocol": {"shape": "ParticipantProtocol", "documentation": "<p>Type of ingest protocol that the participant employs for broadcasting.</p>"}, "replicationType": {"shape": "ReplicationType", "documentation": "<p>Indicates if the participant has been replicated to another stage or is a replica from another stage. Default: <code>NONE</code>. </p>"}, "replicationState": {"shape": "ReplicationState", "documentation": "<p>The participant's replication state.</p>"}, "sourceStageArn": {"shape": "StageArn", "documentation": "<p>Source stage ARN from which this participant is replicated, if <code>replicationType</code> is <code>REPLICA</code>. </p>"}, "sourceSessionId": {"shape": "StageSessionId", "documentation": "<p>ID of the session within the source stage, if <code>replicationType</code> is <code>REPLICA</code>.</p>"}}, "documentation": "<p>Object describing a participant that has joined a stage.</p>"}, "ParticipantAttributes": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "String"}}, "ParticipantClientAttribute": {"type": "string", "max": 128, "min": 0, "pattern": "[a-zA-Z0-9-_.,:;\\s]*"}, "ParticipantId": {"type": "string", "max": 64, "min": 0, "pattern": "[a-zA-Z0-9-]*"}, "ParticipantList": {"type": "list", "member": {"shape": "ParticipantSummary"}}, "ParticipantProtocol": {"type": "string", "enum": ["UNKNOWN", "WHIP", "RTMP", "RTMPS"]}, "ParticipantRecordingFilterByRecordingState": {"type": "string", "enum": ["STARTING", "ACTIVE", "STOPPING", "STOPPED", "FAILED"]}, "ParticipantRecordingHlsConfiguration": {"type": "structure", "members": {"targetSegmentDurationSeconds": {"shape": "ParticipantRecordingTargetSegmentDurationSeconds", "documentation": "<p>Defines the target duration for recorded segments generated when recording a stage participant. Segments may have durations longer than the specified value when needed to ensure each segment begins with a keyframe. Default: 6.</p>"}}, "documentation": "<p>An object representing a configuration of participant HLS recordings for individual participant recording.</p>"}, "ParticipantRecordingMediaType": {"type": "string", "enum": ["AUDIO_VIDEO", "AUDIO_ONLY", "NONE"]}, "ParticipantRecordingMediaTypeList": {"type": "list", "member": {"shape": "ParticipantRecordingMediaType"}, "max": 1, "min": 0}, "ParticipantRecordingReconnectWindowSeconds": {"type": "integer", "max": 300, "min": 0}, "ParticipantRecordingS3BucketName": {"type": "string", "max": 63, "min": 0, "pattern": "[a-z0-9-.]*"}, "ParticipantRecordingS3Prefix": {"type": "string", "max": 256, "min": 0, "pattern": "[a-zA-Z0-9-]*"}, "ParticipantRecordingState": {"type": "string", "enum": ["STARTING", "ACTIVE", "STOPPING", "STOPPED", "FAILED", "DISABLED"]}, "ParticipantRecordingTargetSegmentDurationSeconds": {"type": "integer", "box": true, "max": 10, "min": 2}, "ParticipantReplica": {"type": "structure", "required": ["sourceStageArn", "participantId", "sourceSessionId", "destinationStageArn", "destinationSessionId", "replicationState"], "members": {"sourceStageArn": {"shape": "StageArn", "documentation": "<p>ARN of the stage from which this participant is replicated.</p>"}, "participantId": {"shape": "ParticipantId", "documentation": "<p>Participant ID of the publisher that will be replicated. This is assigned by IVS and returned by <a>CreateParticipantToken</a> or the <code>jti</code> (JWT ID) used to <a href=\"https://docs.aws.amazon.com/ivs/latest/RealTimeUserGuide/getting-started-distribute-tokens.html#getting-started-distribute-tokens-self-signed\"> create a self signed token</a>.</p>"}, "sourceSessionId": {"shape": "StageSessionId", "documentation": "<p>ID of the session within the source stage.</p>"}, "destinationStageArn": {"shape": "StageArn", "documentation": "<p>ARN of the stage where the participant is replicated.</p>"}, "destinationSessionId": {"shape": "StageSessionId", "documentation": "<p>ID of the session within the destination stage.</p>"}, "replicationState": {"shape": "ReplicationState", "documentation": "<p>Replica’s current replication state.</p>"}}, "documentation": "<p>Information about the replicated destination stage for a participant.</p>"}, "ParticipantReplicaList": {"type": "list", "member": {"shape": "ParticipantReplica"}}, "ParticipantState": {"type": "string", "enum": ["CONNECTED", "DISCONNECTED"]}, "ParticipantSummary": {"type": "structure", "members": {"participantId": {"shape": "ParticipantId", "documentation": "<p>Unique identifier for this participant, assigned by IVS.</p>"}, "userId": {"shape": "UserId", "documentation": "<p>Customer-assigned name to help identify the token; this can be used to link a participant to a user in the customer’s own systems. This can be any UTF-8 encoded text. <i>This field is exposed to all stage participants and should not be used for personally identifying, confidential, or sensitive information</i>.</p>"}, "state": {"shape": "ParticipantState", "documentation": "<p>Whether the participant is connected to or disconnected from the stage.</p>"}, "firstJoinTime": {"shape": "Time", "documentation": "<p>ISO 8601 timestamp (returned as a string) when the participant first joined the stage session.</p>"}, "published": {"shape": "Published", "documentation": "<p>Whether the participant ever published to the stage session.</p>"}, "recordingState": {"shape": "ParticipantRecordingState", "documentation": "<p>The participant’s recording state.</p>"}, "replicationType": {"shape": "ReplicationType", "documentation": "<p>Indicates if the participant has been replicated to another stage or is a replica from another stage. Default: <code>NONE</code>. </p>"}, "replicationState": {"shape": "ReplicationState", "documentation": "<p>The participant's replication state.</p>"}, "sourceStageArn": {"shape": "StageArn", "documentation": "<p>ARN of the stage from which this participant is replicated.</p>"}, "sourceSessionId": {"shape": "StageSessionId", "documentation": "<p>ID of the session within the source stage, if <code>replicationType</code> is <code>REPLICA</code>.</p>"}}, "documentation": "<p>Summary object describing a participant that has joined a stage.</p>"}, "ParticipantThumbnailConfiguration": {"type": "structure", "members": {"targetIntervalSeconds": {"shape": "ThumbnailIntervalSeconds", "documentation": "<p>The targeted thumbnail-generation interval in seconds. This is configurable only if <code>recordingMode</code> is <code>INTERVAL</code>. Default: 60.</p>"}, "storage": {"shape": "ThumbnailStorageTypeList", "documentation": "<p>Indicates the format in which thumbnails are recorded. <code>SEQUENTIAL</code> records all generated thumbnails in a serial manner, to the media/thumbnails/high directory. <code>LATEST</code> saves the latest thumbnail in media/latest_thumbnail/high/thumb.jpg and overwrites it at the interval specified by <code>targetIntervalSeconds</code>. You can enable both <code>SEQUENTIAL</code> and <code>LATEST</code>. Default: <code>SEQUENTIAL</code>.</p>"}, "recordingMode": {"shape": "ThumbnailRecordingMode", "documentation": "<p>Thumbnail recording mode. Default: <code>DISABLED</code>.</p>"}}, "documentation": "<p>An object representing a configuration of thumbnails for recorded video from an individual participant.</p>"}, "ParticipantToken": {"type": "structure", "members": {"participantId": {"shape": "ParticipantTokenId", "documentation": "<p>Unique identifier for this participant token, assigned by IVS.</p>"}, "token": {"shape": "ParticipantTokenString", "documentation": "<p>The issued client token, encrypted.</p>"}, "userId": {"shape": "ParticipantTokenUserId", "documentation": "<p>Customer-assigned name to help identify the token; this can be used to link a participant to a user in the customer’s own systems. This can be any UTF-8 encoded text. <i>This field is exposed to all stage participants and should not be used for personally identifying, confidential, or sensitive information.</i> </p>"}, "attributes": {"shape": "ParticipantTokenAttributes", "documentation": "<p>Application-provided attributes to encode into the token and attach to a stage. <i>This field is exposed to all stage participants and should not be used for personally identifying, confidential, or sensitive information.</i> </p>"}, "duration": {"shape": "ParticipantTokenDurationMinutes", "documentation": "<p>Duration (in minutes), after which the participant token expires. Default: 720 (12 hours).</p>"}, "capabilities": {"shape": "ParticipantTokenCapabilities", "documentation": "<p>Set of capabilities that the user is allowed to perform in the stage.</p>"}, "expirationTime": {"shape": "ParticipantTokenExpirationTime", "documentation": "<p>ISO 8601 timestamp (returned as a string) for when this token expires.</p>"}}, "documentation": "<p>Object specifying a participant token in a stage.</p> <p> <b>Important</b>: Treat tokens as opaque; i.e., do not build functionality based on token contents. The format of tokens could change in the future.</p>"}, "ParticipantTokenAttributes": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "String"}}, "ParticipantTokenCapabilities": {"type": "list", "member": {"shape": "ParticipantTokenCapability"}, "max": 2, "min": 0}, "ParticipantTokenCapability": {"type": "string", "enum": ["PUBLISH", "SUBSCRIBE"]}, "ParticipantTokenConfiguration": {"type": "structure", "members": {"duration": {"shape": "ParticipantTokenDurationMinutes", "documentation": "<p>Duration (in minutes), after which the corresponding participant token expires. Default: 720 (12 hours).</p>"}, "userId": {"shape": "ParticipantTokenUserId", "documentation": "<p>Customer-assigned name to help identify the token; this can be used to link a participant to a user in the customer’s own systems. This can be any UTF-8 encoded text. <i>This field is exposed to all stage participants and should not be used for personally identifying, confidential, or sensitive information.</i> </p>"}, "attributes": {"shape": "ParticipantTokenAttributes", "documentation": "<p>Application-provided attributes to encode into the corresponding participant token and attach to a stage. Map keys and values can contain UTF-8 encoded text. The maximum length of this field is 1 KB total. <i>This field is exposed to all stage participants and should not be used for personally identifying, confidential, or sensitive information.</i> </p>"}, "capabilities": {"shape": "ParticipantTokenCapabilities", "documentation": "<p>Set of capabilities that the user is allowed to perform in the stage.</p>"}}, "documentation": "<p>Object specifying a participant token configuration in a stage.</p>"}, "ParticipantTokenConfigurations": {"type": "list", "member": {"shape": "ParticipantTokenConfiguration"}, "max": 12, "min": 0}, "ParticipantTokenDurationMinutes": {"type": "integer", "box": true, "max": 20160, "min": 1}, "ParticipantTokenExpirationTime": {"type": "timestamp", "timestampFormat": "iso8601"}, "ParticipantTokenId": {"type": "string", "max": 64, "min": 0, "pattern": "[a-zA-Z0-9-_]*"}, "ParticipantTokenList": {"type": "list", "member": {"shape": "ParticipantToken"}}, "ParticipantTokenString": {"type": "string", "sensitive": true}, "ParticipantTokenUserId": {"type": "string", "max": 128, "min": 0}, "PendingVerification": {"type": "structure", "members": {"accessControlAllowOrigin": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "Access-Control-Allow-Origin"}, "accessControlExposeHeaders": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "Access-Control-Expose-Headers"}, "cacheControl": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "Cache-Control"}, "contentSecurityPolicy": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "Content-Security-Policy"}, "strictTransportSecurity": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "Strict-Transport-Security"}, "xContentTypeOptions": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "X-Content-Type-Options"}, "xFrameOptions": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "X-Frame-Options"}, "xAmznErrorType": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "x-amzn-ErrorType"}, "exceptionMessage": {"shape": "errorMessage", "documentation": "<p> Your account is pending verification. </p>"}}, "documentation": "<p/>", "error": {"httpStatusCode": 403, "senderFault": true}, "exception": true}, "PipBehavior": {"type": "string", "enum": ["STATIC", "DYNAMIC"]}, "PipConfiguration": {"type": "structure", "members": {"featuredParticipantAttribute": {"shape": "AttributeKey", "documentation": "<p>This attribute name identifies the featured slot. A participant with this attribute set to <code>\"true\"</code> (as a string value) in <a>ParticipantTokenConfiguration</a> is placed in the featured slot. Default: <code>\"\"</code> (no featured participant).</p>"}, "omitStoppedVideo": {"shape": "OmitStoppedVideo", "documentation": "<p>Determines whether to omit participants with stopped video in the composition. Default: <code>false</code>.</p>"}, "videoFillMode": {"shape": "VideoFillMode", "documentation": "<p>Defines how video content fits within the participant tile: <code>FILL</code> (stretched), <code>COVER</code> (cropped), or <code>CONTAIN</code> (letterboxed). Default: <code>COVER</code>.</p>"}, "gridGap": {"shape": "GridGap", "documentation": "<p>Specifies the spacing between participant tiles in pixels. Default: <code>0</code>.</p>"}, "pipParticipantAttribute": {"shape": "AttributeKey", "documentation": "<p>Specifies the participant for the PiP window. A participant with this attribute set to <code>\"true\"</code> (as a string value) in <a>ParticipantTokenConfiguration</a> is placed in the PiP slot. Default: <code>\"\"</code> (no PiP participant).</p>"}, "pipBehavior": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>Defines PiP behavior when all participants have left: <code>STATIC</code> (maintains original position/size) or <code>DYNAMIC</code> (expands to full composition). Default: <code>STATIC</code>.</p>"}, "pipOffset": {"shape": "PipOffset", "documentation": "<p>Sets the PiP window’s offset position in pixels from the closest edges determined by <code>PipPosition</code>. Default: <code>0</code>.</p>"}, "pipPosition": {"shape": "PipPosition", "documentation": "<p>Determines the corner position of the PiP window. Default: <code>BOTTOM_RIGHT</code>.</p>"}, "pipWidth": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>Specifies the width of the PiP window in pixels. When this is not set explicitly, <code>pipWidth</code>’s value will be based on the size of the composition and the aspect ratio of the participant’s video.</p>"}, "pipHeight": {"shape": "PipHeight", "documentation": "<p>Specifies the height of the PiP window in pixels. When this is not set explicitly, <code>pipHeight</code>’s value will be based on the size of the composition and the aspect ratio of the participant’s video.</p>"}}, "documentation": "<p>Configuration information specific to Picture-in-Picture (PiP) layout, for <a href=\"https://docs.aws.amazon.com/ivs/latest/RealTimeUserGuide/server-side-composition.html\">server-side composition</a>. </p>"}, "PipHeight": {"type": "integer", "box": true, "min": 1}, "PipOffset": {"type": "integer", "min": 0}, "PipPosition": {"type": "string", "enum": ["TOP_LEFT", "TOP_RIGHT", "BOTTOM_LEFT", "BOTTOM_RIGHT"]}, "PipWidth": {"type": "integer", "box": true, "min": 1}, "PublicKey": {"type": "structure", "members": {"arn": {"shape": "PublicKeyArn", "documentation": "<p>Public key ARN.</p>"}, "name": {"shape": "PublicKeyName", "documentation": "<p>Public key name.</p>"}, "publicKeyMaterial": {"shape": "PublicKeyMaterial", "documentation": "<p>Public key material.</p>"}, "fingerprint": {"shape": "PublicKeyFingerprint", "documentation": "<p>The public key fingerprint, a short string used to identify or verify the full public key.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>Tags attached to the resource. Array of maps, each of the form <code>string:string (key:value)</code>. See <a href=\"https://docs.aws.amazon.com/tag-editor/latest/userguide/best-practices-and-strats.html\">Best practices and strategies</a> in <i>Tagging AWS Resources and Tag Editor</i> for details, including restrictions that apply to tags and \"Tag naming limits and requirements\"; Amazon IVS has no constraints on tags beyond what is documented there.</p>"}}, "documentation": "<p>Object specifying a public key used to sign stage participant tokens.</p>"}, "PublicKeyArn": {"type": "string", "max": 128, "min": 1, "pattern": "arn:aws:ivs:[a-z0-9-]+:[0-9]+:public-key/[a-zA-Z0-9-]+"}, "PublicKeyFingerprint": {"type": "string"}, "PublicKeyList": {"type": "list", "member": {"shape": "PublicKeySummary"}}, "PublicKeyMaterial": {"type": "string", "pattern": ".*-----BEGIN PUBLIC KEY-----\\r?\\n([a-zA-Z0-9+/=\\r\\n]+)\\r?\\n-----END PUBLIC KEY-----(\\r?\\n)?.*"}, "PublicKeyName": {"type": "string", "max": 128, "min": 0, "pattern": "[a-zA-Z0-9-_]*"}, "PublicKeySummary": {"type": "structure", "members": {"arn": {"shape": "PublicKeyArn", "documentation": "<p>Public key ARN.</p>"}, "name": {"shape": "PublicKeyName", "documentation": "<p>Public key name.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>Tags attached to the resource. Array of maps, each of the form <code>string:string (key:value)</code>. See <a href=\"https://docs.aws.amazon.com/tag-editor/latest/userguide/best-practices-and-strats.html\">Best practices and strategies</a> in <i>Tagging AWS Resources and Tag Editor</i> for details, including restrictions that apply to tags and \"Tag naming limits and requirements\"; Amazon IVS has no constraints on tags beyond what is documented there.</p>"}}, "documentation": "<p>Summary information about a public key.</p>"}, "Published": {"type": "boolean"}, "ReconnectWindowSeconds": {"type": "integer", "box": true, "max": 60, "min": 0}, "RecordParticipantReplicas": {"type": "boolean"}, "RecordingConfiguration": {"type": "structure", "members": {"hlsConfiguration": {"shape": "CompositionRecordingHlsConfiguration", "documentation": "<p>An HLS configuration object to return information about how the recording will be configured.</p>"}, "format": {"shape": "RecordingConfigurationFormat", "documentation": "<p>The recording format for storing a recording in Amazon S3.</p>"}}, "documentation": "<p>An object representing a configuration to record a stage stream.</p>"}, "RecordingConfigurationFormat": {"type": "string", "enum": ["HLS"]}, "Replica": {"type": "boolean"}, "ReplicationState": {"type": "string", "enum": ["ACTIVE", "STOPPED"]}, "ReplicationType": {"type": "string", "enum": ["SOURCE", "REPLICA", "NONE"]}, "ResourceArn": {"type": "string", "max": 128, "min": 1, "pattern": "arn:aws:ivs:[a-z0-9-]+:[0-9]+:[a-z-]/[a-zA-Z0-9-]+"}, "ResourceNotFoundException": {"type": "structure", "members": {"accessControlAllowOrigin": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "Access-Control-Allow-Origin"}, "accessControlExposeHeaders": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "Access-Control-Expose-Headers"}, "cacheControl": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "Cache-Control"}, "contentSecurityPolicy": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "Content-Security-Policy"}, "strictTransportSecurity": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "Strict-Transport-Security"}, "xContentTypeOptions": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "X-Content-Type-Options"}, "xFrameOptions": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "X-Frame-Options"}, "xAmznErrorType": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "x-amzn-ErrorType"}, "exceptionMessage": {"shape": "errorMessage", "documentation": "<p>Request references a resource which does not exist.</p>"}}, "documentation": "<p/>", "error": {"httpStatusCode": 404, "senderFault": true}, "exception": true}, "S3BucketName": {"type": "string", "max": 63, "min": 3, "pattern": "[a-z0-9-.]+"}, "S3DestinationConfiguration": {"type": "structure", "required": ["storageConfigurationArn", "encoderConfigurationArns"], "members": {"storageConfigurationArn": {"shape": "StorageConfigurationArn", "documentation": "<p>ARN of the <a>StorageConfiguration</a> where recorded videos will be stored.</p>"}, "encoderConfigurationArns": {"shape": "EncoderConfigurationArnList", "documentation": "<p>ARNs of the <a>EncoderConfiguration</a> resource. The encoder configuration and stage resources must be in the same AWS account and region. </p>"}, "recordingConfiguration": {"shape": "RecordingConfiguration", "documentation": "<p>Array of maps, each of the form <code>string:string (key:value)</code>. This is an optional customer specification, currently used only to specify the recording format for storing a recording in Amazon S3.</p>"}, "thumbnailConfigurations": {"shape": "CompositionThumbnailConfigurationList", "documentation": "<p>A complex type that allows you to enable/disable the recording of thumbnails for a <a>Composition</a> and modify the interval at which thumbnails are generated for the live session.</p>"}}, "documentation": "<p>A complex type that describes an S3 location where recorded videos will be stored.</p>"}, "S3Detail": {"type": "structure", "required": ["recordingPrefix"], "members": {"recordingPrefix": {"shape": "String", "documentation": "<p>The S3 bucket prefix under which the recording is stored.</p>"}}, "documentation": "<p>Complex data type that defines S3Detail objects.</p>"}, "S3StorageConfiguration": {"type": "structure", "required": ["bucketName"], "members": {"bucketName": {"shape": "S3BucketName", "documentation": "<p>Location (S3 bucket name) where recorded videos will be stored. Note that the StorageConfiguration and S3 bucket must be in the same region as the Composition.</p>"}}, "documentation": "<p>A complex type that describes an S3 location where recorded videos will be stored.</p>"}, "ServiceQuotaExceededException": {"type": "structure", "members": {"accessControlAllowOrigin": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "Access-Control-Allow-Origin"}, "accessControlExposeHeaders": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "Access-Control-Expose-Headers"}, "cacheControl": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "Cache-Control"}, "contentSecurityPolicy": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "Content-Security-Policy"}, "strictTransportSecurity": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "Strict-Transport-Security"}, "xContentTypeOptions": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "X-Content-Type-Options"}, "xFrameOptions": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "X-Frame-Options"}, "xAmznErrorType": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "x-amzn-ErrorType"}, "exceptionMessage": {"shape": "errorMessage", "documentation": "<p>Request would cause a service quota to be exceeded.</p>"}}, "documentation": "<p/>", "error": {"httpStatusCode": 402, "senderFault": true}, "exception": true}, "Stage": {"type": "structure", "required": ["arn"], "members": {"arn": {"shape": "StageArn", "documentation": "<p>Stage ARN.</p>"}, "name": {"shape": "StageName", "documentation": "<p>Stage name.</p>"}, "activeSessionId": {"shape": "StageSessionId", "documentation": "<p>ID of the active session within the stage.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>Tags attached to the resource. Array of maps, each of the form <code>string:string (key:value)</code>. See <a href=\"https://docs.aws.amazon.com/tag-editor/latest/userguide/best-practices-and-strats.html\">Best practices and strategies</a> in <i>Tagging AWS Resources and Tag Editor</i> for details, including restrictions that apply to tags and \"Tag naming limits and requirements\"; Amazon IVS has no constraints on tags beyond what is documented there.</p>"}, "autoParticipantRecordingConfiguration": {"shape": "AutoParticipantRecordingConfiguration", "documentation": "<p>Configuration object for individual participant recording, attached to the stage.</p>"}, "endpoints": {"shape": "StageEndpoints", "documentation": "<p>Summary information about various endpoints for a stage.</p>"}}, "documentation": "<p>Object specifying a stage.</p>"}, "StageArn": {"type": "string", "max": 128, "min": 1, "pattern": "arn:aws:ivs:[a-z0-9-]+:[0-9]+:stage/[a-zA-Z0-9-]+"}, "StageEndpoint": {"type": "string", "max": 2048, "min": 0}, "StageEndpoints": {"type": "structure", "members": {"events": {"shape": "StageEndpoint", "documentation": "<p>Events endpoint.</p>"}, "whip": {"shape": "StageEndpoint", "documentation": "<p>The endpoint to be used for IVS real-time streaming using the WHIP protocol.</p>"}, "rtmp": {"shape": "StageEndpoint", "documentation": "<p>The endpoint to be used for IVS real-time streaming using the RTMP protocol.</p>"}, "rtmps": {"shape": "StageEndpoint", "documentation": "<p>The endpoint to be used for IVS real-time streaming using the RTMPS protocol.</p>"}}, "documentation": "<p>Summary information about various endpoints for a stage. We recommend that you cache these values at stage creation; the values can be cached for up to 14 days.</p>"}, "StageName": {"type": "string", "max": 128, "min": 0, "pattern": "[a-zA-Z0-9-_]*"}, "StageSession": {"type": "structure", "members": {"sessionId": {"shape": "StageSessionId", "documentation": "<p>ID of the session within the stage.</p>"}, "startTime": {"shape": "Time", "documentation": "<p> ISO 8601 timestamp (returned as a string) when this stage session began.</p>"}, "endTime": {"shape": "Time", "documentation": "<p>ISO 8601 timestamp (returned as a string) when the stage session ended. This is null if the stage is active.</p>"}}, "documentation": "<p>A stage session begins when the first participant joins a stage and ends after the last participant leaves the stage. A stage session helps with debugging stages by grouping events and participants into shorter periods of time (i.e., a session), which is helpful when stages are used over long periods of time.</p>"}, "StageSessionId": {"type": "string", "max": 16, "min": 16, "pattern": "st-[a-zA-Z0-9]+"}, "StageSessionList": {"type": "list", "member": {"shape": "StageSessionSummary"}}, "StageSessionSummary": {"type": "structure", "members": {"sessionId": {"shape": "StageSessionId", "documentation": "<p>ID of the session within the stage.</p>"}, "startTime": {"shape": "Time", "documentation": "<p> ISO 8601 timestamp (returned as a string) when this stage session began.</p>"}, "endTime": {"shape": "Time", "documentation": "<p>ISO 8601 timestamp (returned as a string) when the stage session ended. This is null if the stage is active.</p>"}}, "documentation": "<p>Summary information about a stage session.</p>"}, "StageSummary": {"type": "structure", "required": ["arn"], "members": {"arn": {"shape": "StageArn", "documentation": "<p>Stage ARN.</p>"}, "name": {"shape": "StageName", "documentation": "<p>Stage name.</p>"}, "activeSessionId": {"shape": "StageSessionId", "documentation": "<p>ID of the active session within the stage.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>Tags attached to the resource. Array of maps, each of the form <code>string:string (key:value)</code>. See <a href=\"https://docs.aws.amazon.com/tag-editor/latest/userguide/best-practices-and-strats.html\">Best practices and strategies</a> in <i>Tagging AWS Resources and Tag Editor</i> for details, including restrictions that apply to tags and \"Tag naming limits and requirements\"; Amazon IVS has no constraints on tags beyond what is documented there.</p>"}}, "documentation": "<p>Summary information about a stage.</p>"}, "StageSummaryList": {"type": "list", "member": {"shape": "StageSummary"}}, "StartCompositionRequest": {"type": "structure", "required": ["stageArn", "destinations"], "members": {"stageArn": {"shape": "StageArn", "documentation": "<p>ARN of the stage to be used for compositing.</p>"}, "idempotencyToken": {"shape": "CompositionClientToken", "documentation": "<p>Idempotency token.</p>", "idempotencyToken": true}, "layout": {"shape": "LayoutConfiguration", "documentation": "<p>Layout object to configure composition parameters.</p>"}, "destinations": {"shape": "DestinationConfigurationList", "documentation": "<p>Array of destination configuration.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>Tags attached to the resource. Array of maps, each of the form <code>string:string (key:value)</code>. See <a href=\"https://docs.aws.amazon.com/tag-editor/latest/userguide/best-practices-and-strats.html\">Best practices and strategies</a> in <i>Tagging AWS Resources and Tag Editor</i> for details, including restrictions that apply to tags and \"Tag naming limits and requirements\"; Amazon IVS has no constraints on tags beyond what is documented there.</p>"}}}, "StartCompositionResponse": {"type": "structure", "members": {"composition": {"shape": "Composition", "documentation": "<p>The Composition that was created.</p>"}}}, "StartParticipantReplicationRequest": {"type": "structure", "required": ["sourceStageArn", "destinationStageArn", "participantId"], "members": {"sourceStageArn": {"shape": "StageArn", "documentation": "<p>ARN of the stage where the participant is publishing.</p>"}, "destinationStageArn": {"shape": "StageArn", "documentation": "<p>ARN of the stage to which the participant will be replicated.</p>"}, "participantId": {"shape": "ParticipantId", "documentation": "<p>Participant ID of the publisher that will be replicated. This is assigned by IVS and returned by <a>CreateParticipantToken</a> or the <code>jti</code> (JWT ID) used to <a href=\"https://docs.aws.amazon.com/ivs/latest/RealTimeUserGuide/getting-started-distribute-tokens.html#getting-started-distribute-tokens-self-signed\">create a self signed token</a>. </p>"}, "reconnectWindowSeconds": {"shape": "ReconnectWindowSeconds", "documentation": "<p>If the participant disconnects and then reconnects within the specified interval, replication will continue to be <code>ACTIVE</code>. Default: 0.</p>"}, "attributes": {"shape": "ParticipantAttributes", "documentation": "<p>Application-provided attributes to set on the replicated participant in the destination stage. Map keys and values can contain UTF-8 encoded text. The maximum length of this field is 1 KB total. <i>This field is exposed to all stage participants and should not be used for personally identifying, confidential, or sensitive information.</i> </p> <p>These attributes are merged with any attributes set for this participant when creating the token. If there is overlap in keys, the values in these attributes are replaced.</p>"}}}, "StartParticipantReplicationResponse": {"type": "structure", "members": {"accessControlAllowOrigin": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "Access-Control-Allow-Origin"}, "accessControlExposeHeaders": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "Access-Control-Expose-Headers"}, "cacheControl": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "Cache-Control"}, "contentSecurityPolicy": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "Content-Security-Policy"}, "strictTransportSecurity": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "Strict-Transport-Security"}, "xContentTypeOptions": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "X-Content-Type-Options"}, "xFrameOptions": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "X-Frame-Options"}}}, "StopCompositionRequest": {"type": "structure", "required": ["arn"], "members": {"arn": {"shape": "CompositionArn", "documentation": "<p>ARN of the Composition.</p>"}}}, "StopCompositionResponse": {"type": "structure", "members": {}}, "StopParticipantReplicationRequest": {"type": "structure", "required": ["sourceStageArn", "destinationStageArn", "participantId"], "members": {"sourceStageArn": {"shape": "StageArn", "documentation": "<p>ARN of the stage where the participant is publishing.</p>"}, "destinationStageArn": {"shape": "StageArn", "documentation": "<p>ARN of the stage where the participant has been replicated.</p>"}, "participantId": {"shape": "ParticipantId", "documentation": "<p>Participant ID of the publisher that has been replicated. This is assigned by IVS and returned by <a>CreateParticipantToken</a> or the <code>jti</code> (JWT ID) used to <a href=\"https://docs.aws.amazon.com/ivs/latest/RealTimeUserGuide/getting-started-distribute-tokens.html#getting-started-distribute-tokens-self-signed\"> create a self signed token</a>.</p>"}}}, "StopParticipantReplicationResponse": {"type": "structure", "members": {"accessControlAllowOrigin": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "Access-Control-Allow-Origin"}, "accessControlExposeHeaders": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "Access-Control-Expose-Headers"}, "cacheControl": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "Cache-Control"}, "contentSecurityPolicy": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "Content-Security-Policy"}, "strictTransportSecurity": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "Strict-Transport-Security"}, "xContentTypeOptions": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "X-Content-Type-Options"}, "xFrameOptions": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "X-Frame-Options"}}}, "StorageConfiguration": {"type": "structure", "required": ["arn"], "members": {"arn": {"shape": "StorageConfigurationArn", "documentation": "<p>ARN of the storage configuration.</p>"}, "name": {"shape": "StorageConfigurationName", "documentation": "<p>Name of the storage configuration.</p>"}, "s3": {"shape": "S3StorageConfiguration", "documentation": "<p>An S3 destination configuration where recorded videos will be stored.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>Tags attached to the resource. Array of maps, each of the form <code>string:string (key:value)</code>. See <a href=\"https://docs.aws.amazon.com/tag-editor/latest/userguide/best-practices-and-strats.html\">Best practices and strategies</a> in <i>Tagging AWS Resources and Tag Editor</i> for details, including restrictions that apply to tags and \"Tag naming limits and requirements\"; Amazon IVS has no constraints on tags beyond what is documented there.</p>"}}, "documentation": "<p>A complex type that describes a location where recorded videos will be stored.</p>"}, "StorageConfigurationArn": {"type": "string", "max": 128, "min": 1, "pattern": "arn:aws:ivs:[a-z0-9-]+:[0-9]+:storage-configuration/[a-zA-Z0-9-]+"}, "StorageConfigurationName": {"type": "string", "max": 128, "min": 0, "pattern": "[a-zA-Z0-9-_]*"}, "StorageConfigurationSummary": {"type": "structure", "required": ["arn"], "members": {"arn": {"shape": "StorageConfigurationArn", "documentation": "<p>ARN of the storage configuration.</p>"}, "name": {"shape": "StorageConfigurationName", "documentation": "<p>Name of the storage configuration.</p>"}, "s3": {"shape": "S3StorageConfiguration", "documentation": "<p>An S3 destination configuration where recorded videos will be stored.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>Tags attached to the resource. Array of maps, each of the form <code>string:string (key:value)</code>. See <a href=\"https://docs.aws.amazon.com/tag-editor/latest/userguide/best-practices-and-strats.html\">Best practices and strategies</a> in <i>Tagging AWS Resources and Tag Editor</i> for details, including restrictions that apply to tags and \"Tag naming limits and requirements\"; Amazon IVS has no constraints on tags beyond what is documented there.</p>"}}, "documentation": "<p>Summary information about a storage configuration.</p>"}, "StorageConfigurationSummaryList": {"type": "list", "member": {"shape": "StorageConfigurationSummary"}}, "StreamKey": {"type": "string", "pattern": "rt_[0-9]+_[a-z0-9-]+_[a-zA-Z0-9-]+_.+", "sensitive": true}, "String": {"type": "string"}, "TagKey": {"type": "string", "max": 128, "min": 1}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 50, "min": 0}, "TagResourceRequest": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {"shape": "ResourceArn", "documentation": "<p>The ARN of the resource to be tagged. The ARN must be URL-encoded.</p>", "location": "uri", "locationName": "resourceArn"}, "tags": {"shape": "Tags", "documentation": "<p>Array of tags to be added or updated. Array of maps, each of the form <code>string:string (key:value)</code>. See <a href=\"https://docs.aws.amazon.com/tag-editor/latest/userguide/best-practices-and-strats.html\">Best practices and strategies</a> in <i>Tagging AWS Resources and Tag Editor</i> for details, including restrictions that apply to tags and \"Tag naming limits and requirements\"; Amazon IVS has no constraints on tags beyond what is documented there.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0}, "Tags": {"type": "map", "key": {"shape": "TagKey"}, "value": {"shape": "TagValue"}, "max": 50, "min": 0}, "ThumbnailIntervalSeconds": {"type": "integer", "box": true, "max": 86400, "min": 1}, "ThumbnailRecordingMode": {"type": "string", "enum": ["INTERVAL", "DISABLED"]}, "ThumbnailStorageType": {"type": "string", "enum": ["SEQUENTIAL", "LATEST"]}, "ThumbnailStorageTypeList": {"type": "list", "member": {"shape": "ThumbnailStorageType"}, "max": 2, "min": 0}, "Time": {"type": "timestamp", "timestampFormat": "iso8601"}, "UntagResourceRequest": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {"shape": "ResourceArn", "documentation": "<p>The ARN of the resource to be untagged. The ARN must be URL-encoded.</p>", "location": "uri", "locationName": "resourceArn"}, "tagKeys": {"shape": "TagKeyList", "documentation": "<p>Array of tag keys (strings) for the tags to be removed. See <a href=\"https://docs.aws.amazon.com/tag-editor/latest/userguide/best-practices-and-strats.html\">Best practices and strategies</a> in <i>Tagging AWS Resources and Tag Editor</i> for details, including restrictions that apply to tags and \"Tag naming limits and requirements\"; Amazon IVS has no constraints on tags beyond what is documented there.</p>", "location": "querystring", "locationName": "tagKeys"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateIngestConfigurationRequest": {"type": "structure", "required": ["arn"], "members": {"arn": {"shape": "IngestConfigurationArn", "documentation": "<p>ARN of the IngestConfiguration, for which the related stage ARN needs to be updated.</p>"}, "stageArn": {"shape": "IngestConfigurationStageArn", "documentation": "<p>Stage ARN that needs to be updated.</p>"}}}, "UpdateIngestConfigurationResponse": {"type": "structure", "members": {"ingestConfiguration": {"shape": "IngestConfiguration", "documentation": "<p>The updated IngestConfiguration.</p>"}}}, "UpdateStageRequest": {"type": "structure", "required": ["arn"], "members": {"arn": {"shape": "StageArn", "documentation": "<p>ARN of the stage to be updated.</p>"}, "name": {"shape": "StageName", "documentation": "<p>Name of the stage to be updated.</p>"}, "autoParticipantRecordingConfiguration": {"shape": "AutoParticipantRecordingConfiguration", "documentation": "<p>Configuration object for individual participant recording, to attach to the stage. Note that this cannot be updated while recording is active.</p>"}}}, "UpdateStageResponse": {"type": "structure", "members": {"stage": {"shape": "Stage", "documentation": "<p>The updated stage.</p>"}}}, "UserId": {"type": "string", "max": 128, "min": 0}, "ValidationException": {"type": "structure", "members": {"accessControlAllowOrigin": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "Access-Control-Allow-Origin"}, "accessControlExposeHeaders": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "Access-Control-Expose-Headers"}, "cacheControl": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "Cache-Control"}, "contentSecurityPolicy": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "Content-Security-Policy"}, "strictTransportSecurity": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "Strict-Transport-Security"}, "xContentTypeOptions": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "X-Content-Type-Options"}, "xFrameOptions": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "X-Frame-Options"}, "xAmznErrorType": {"shape": "String", "documentation": "<p/>", "location": "header", "locationName": "x-amzn-ErrorType"}, "exceptionMessage": {"shape": "errorMessage", "documentation": "<p>The input fails to satisfy the constraints specified by an Amazon Web Services service.</p>"}}, "documentation": "<p/>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "Video": {"type": "structure", "members": {"width": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>Video-resolution width. This must be an even number. Note that the maximum value is determined by <code>width</code> times <code>height</code>, such that the maximum total pixels is 2073600 (1920x1080 or 1080x1920). Default: 1280.</p>"}, "height": {"shape": "Height", "documentation": "<p>Video-resolution height. This must be an even number. Note that the maximum value is determined by <code>width</code> times <code>height</code>, such that the maximum total pixels is 2073600 (1920x1080 or 1080x1920). Default: 720.</p>"}, "framerate": {"shape": "Framerate", "documentation": "<p>Video frame rate, in fps. Default: 30.</p>"}, "bitrate": {"shape": "Bitrate", "documentation": "<p>Bitrate for generated output, in bps. Default: 2500000.</p>"}}, "documentation": "<p>Settings for video.</p>"}, "VideoAspectRatio": {"type": "string", "enum": ["AUTO", "VIDEO", "SQUARE", "PORTRAIT"]}, "VideoFillMode": {"type": "string", "enum": ["FILL", "COVER", "CONTAIN"]}, "Width": {"type": "integer", "box": true, "max": 1920, "min": 2}, "errorMessage": {"type": "string"}}, "documentation": "<p>The Amazon Interactive Video Service (IVS) real-time API is REST compatible, using a standard HTTP API and an AWS EventBridge event stream for responses. JSON is used for both requests and responses, including errors. </p> <p> <b>Key Concepts</b> </p> <ul> <li> <p> <b>Stage</b> — A virtual space where participants can exchange video in real time.</p> </li> <li> <p> <b>Participant token</b> — A token that authenticates a participant when they join a stage.</p> </li> <li> <p> <b>Participant object</b> — Represents participants (people) in the stage and contains information about them. When a token is created, it includes a participant ID; when a participant uses that token to join a stage, the participant is associated with that participant ID. There is a 1:1 mapping between participant tokens and participants.</p> </li> </ul> <p>For server-side composition:</p> <ul> <li> <p> <b>Composition process</b> — Composites participants of a stage into a single video and forwards it to a set of outputs (e.g., IVS channels). Composition operations support this process.</p> </li> <li> <p> <b>Composition</b> — Controls the look of the outputs, including how participants are positioned in the video.</p> </li> </ul> <p>For participant replication:</p> <ul> <li> <p> <b>Source stage</b> — The stage where the participant originally joined, which is used as the source for replication.</p> </li> <li> <p> <b>Destination stage</b> — The stage to which the participant is replicated. </p> </li> <li> <p> <b>Replicated participant</b> — A participant in a stage that is replicated to one or more destination stages. </p> </li> <li> <p> <b>Replica participant</b> — A participant in a destination stage that is replicated from another stage (the source stage).</p> </li> </ul> <p>For more information about your IVS live stream, also see <a href=\"https://docs.aws.amazon.com/ivs/latest/RealTimeUserGuide/getting-started.html\">Getting Started with Amazon IVS Real-Time Streaming</a>.</p> <p> <b>Tagging</b> </p> <p>A <i>tag</i> is a metadata label that you assign to an AWS resource. A tag comprises a <i>key</i> and a <i>value</i>, both set by you. For example, you might set a tag as <code>topic:nature</code> to label a particular video category. See <a href=\"https://docs.aws.amazon.com/tag-editor/latest/userguide/best-practices-and-strats.html\">Best practices and strategies</a> in <i>Tagging AWS Resources and Tag Editor</i> for details, including restrictions that apply to tags and \"Tag naming limits and requirements\"; Amazon IVS stages has no service-specific constraints beyond what is documented there.</p> <p>Tags can help you identify and organize your AWS resources. For example, you can use the same tag for different resources to indicate that they are related. You can also use tags to manage access (see <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/access_tags.html\">Access Tags</a>).</p> <p>The Amazon IVS real-time API has these tag-related operations: <a>TagResource</a>, <a>UntagResource</a>, and <a>ListTagsForResource</a>. The following resource supports tagging: Stage.</p> <p>At most 50 tags can be applied to a resource.</p>"}