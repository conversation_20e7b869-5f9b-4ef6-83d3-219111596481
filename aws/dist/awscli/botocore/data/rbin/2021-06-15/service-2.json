{"version": "2.0", "metadata": {"apiVersion": "2021-06-15", "endpointPrefix": "rbin", "jsonVersion": "1.1", "protocol": "rest-json", "protocols": ["rest-json"], "serviceFullName": "Amazon Recycle Bin", "serviceId": "rbin", "signatureVersion": "v4", "signingName": "rbin", "uid": "rbin-2021-06-15", "auth": ["aws.auth#sigv4"]}, "operations": {"CreateRule": {"name": "CreateRule", "http": {"method": "POST", "requestUri": "/rules", "responseCode": 201}, "input": {"shape": "CreateRuleRequest"}, "output": {"shape": "CreateRuleResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates a Recycle Bin retention rule. You can create two types of retention rules:</p> <ul> <li> <p> <b>Tag-level retention rules</b> - These retention rules use resource tags to identify the resources to protect. For each retention rule, you specify one or more tag key and value pairs. Resources (of the specified type) that have at least one of these tag key and value pairs are automatically retained in the Recycle Bin upon deletion. Use this type of retention rule to protect specific resources in your account based on their tags.</p> </li> <li> <p> <b>Region-level retention rules</b> - These retention rules, by default, apply to all of the resources (of the specified type) in the Region, even if the resources are not tagged. However, you can specify exclusion tags to exclude resources that have specific tags. Use this type of retention rule to protect all resources of a specific type in a Region.</p> </li> </ul> <p>For more information, see <a href=\"https://docs.aws.amazon.com/ebs/latest/userguide/recycle-bin.html\"> Create Recycle Bin retention rules</a> in the <i>Amazon EBS User Guide</i>.</p>"}, "DeleteRule": {"name": "DeleteRule", "http": {"method": "DELETE", "requestUri": "/rules/{identifier}", "responseCode": 204}, "input": {"shape": "DeleteRuleRequest"}, "output": {"shape": "DeleteRuleResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Deletes a Recycle Bin retention rule. For more information, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/recycle-bin-working-with-rules.html#recycle-bin-delete-rule\"> Delete Recycle Bin retention rules</a> in the <i>Amazon Elastic Compute Cloud User Guide</i>.</p>"}, "GetRule": {"name": "GetRule", "http": {"method": "GET", "requestUri": "/rules/{identifier}", "responseCode": 200}, "input": {"shape": "GetRuleRequest"}, "output": {"shape": "GetRuleResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Gets information about a Recycle Bin retention rule.</p>"}, "ListRules": {"name": "ListRules", "http": {"method": "POST", "requestUri": "/list-rules", "responseCode": 200}, "input": {"shape": "ListRulesRequest"}, "output": {"shape": "ListRulesResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists the Recycle Bin retention rules in the Region.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Lists the tags assigned to a retention rule.</p>"}, "LockRule": {"name": "LockRule", "http": {"method": "PATCH", "requestUri": "/rules/{identifier}/lock", "responseCode": 200}, "input": {"shape": "LockRuleRequest"}, "output": {"shape": "LockRuleResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Locks a Region-level retention rule. A locked retention rule can't be modified or deleted.</p> <note> <p>You can't lock tag-level retention rules, or Region-level retention rules that have exclusion tags.</p> </note>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags/{resourceArn}", "responseCode": 201}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Assigns tags to the specified retention rule.</p>"}, "UnlockRule": {"name": "UnlockRule", "http": {"method": "PATCH", "requestUri": "/rules/{identifier}/unlock", "responseCode": 200}, "input": {"shape": "UnlockRuleRequest"}, "output": {"shape": "UnlockRuleResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<p>Unlocks a retention rule. After a retention rule is unlocked, it can be modified or deleted only after the unlock delay period expires.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags/{resourceArn}", "responseCode": 204}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Unassigns a tag from a retention rule.</p>"}, "UpdateRule": {"name": "UpdateRule", "http": {"method": "PATCH", "requestUri": "/rules/{identifier}", "responseCode": 200}, "input": {"shape": "UpdateRuleRequest"}, "output": {"shape": "UpdateRuleResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Updates an existing Recycle Bin retention rule. You can update a retention rule's description, resource tags, and retention period at any time after creation. You can't update a retention rule's resource type after creation. For more information, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/recycle-bin-working-with-rules.html#recycle-bin-update-rule\"> Update Recycle Bin retention rules</a> in the <i>Amazon Elastic Compute Cloud User Guide</i>.</p>"}}, "shapes": {"ConflictException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}, "Reason": {"shape": "ConflictExceptionReason", "documentation": "<p>The reason for the exception.</p>"}}, "documentation": "<p>The specified retention rule lock request can't be completed.</p>", "error": {"httpStatusCode": 409}, "exception": true}, "ConflictExceptionReason": {"type": "string", "enum": ["INVALID_RULE_STATE"]}, "CreateRuleRequest": {"type": "structure", "required": ["RetentionPeriod", "ResourceType"], "members": {"RetentionPeriod": {"shape": "RetentionPeriod", "documentation": "<p>Information about the retention period for which the retention rule is to retain resources.</p>"}, "Description": {"shape": "Description", "documentation": "<p>The retention rule description.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>Information about the tags to assign to the retention rule.</p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>The resource type to be retained by the retention rule. Currently, only Amazon EBS snapshots and EBS-backed AMIs are supported. To retain snapshots, specify <code>EBS_SNAPSHOT</code>. To retain EBS-backed AMIs, specify <code>EC2_IMAGE</code>.</p>"}, "ResourceTags": {"shape": "ResourceTags", "documentation": "<p>[Tag-level retention rules only] Specifies the resource tags to use to identify resources that are to be retained by a tag-level retention rule. For tag-level retention rules, only deleted resources, of the specified resource type, that have one or more of the specified tag key and value pairs are retained. If a resource is deleted, but it does not have any of the specified tag key and value pairs, it is immediately deleted without being retained by the retention rule.</p> <p>You can add the same tag key and value pair to a maximum or five retention rules.</p> <p>To create a Region-level retention rule, omit this parameter. A Region-level retention rule does not have any resource tags specified. It retains all deleted resources of the specified resource type in the Region in which the rule is created, even if the resources are not tagged.</p>"}, "LockConfiguration": {"shape": "LockConfiguration", "documentation": "<p>Information about the retention rule lock configuration.</p>"}, "ExcludeResourceTags": {"shape": "ExcludeResourceTags", "documentation": "<p>[Region-level retention rules only] Specifies the exclusion tags to use to identify resources that are to be excluded, or ignored, by a Region-level retention rule. Resources that have any of these tags are not retained by the retention rule upon deletion.</p> <p>You can't specify exclusion tags for tag-level retention rules.</p>"}}}, "CreateRuleResponse": {"type": "structure", "members": {"Identifier": {"shape": "RuleIdentifier", "documentation": "<p>The unique ID of the retention rule.</p>"}, "RetentionPeriod": {"shape": "RetentionPeriod"}, "Description": {"shape": "Description", "documentation": "<p>The retention rule description.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>Information about the tags assigned to the retention rule.</p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>The resource type retained by the retention rule.</p>"}, "ResourceTags": {"shape": "ResourceTags", "documentation": "<p>[Tag-level retention rules only] Information about the resource tags used to identify resources that are retained by the retention rule.</p>"}, "Status": {"shape": "RuleStatus", "documentation": "<p>The state of the retention rule. Only retention rules that are in the <code>available</code> state retain resources.</p>"}, "LockConfiguration": {"shape": "LockConfiguration", "documentation": "<p>Information about the retention rule lock configuration.</p>"}, "LockState": {"shape": "LockState", "documentation": "<p>[Region-level retention rules only] The lock state for the retention rule.</p> <ul> <li> <p> <code>locked</code> - The retention rule is locked and can't be modified or deleted.</p> </li> <li> <p> <code>pending_unlock</code> - The retention rule has been unlocked but it is still within the unlock delay period. The retention rule can be modified or deleted only after the unlock delay period has expired.</p> </li> <li> <p> <code>unlocked</code> - The retention rule is unlocked and it can be modified or deleted by any user with the required permissions.</p> </li> <li> <p> <code>null</code> - The retention rule has never been locked. Once a retention rule has been locked, it can transition between the <code>locked</code> and <code>unlocked</code> states only; it can never transition back to <code>null</code>.</p> </li> </ul>"}, "RuleArn": {"shape": "RuleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the retention rule.</p>"}, "ExcludeResourceTags": {"shape": "ExcludeResourceTags", "documentation": "<p>[Region-level retention rules only] Information about the exclusion tags used to identify resources that are to be excluded, or ignored, by the retention rule.</p>"}}}, "DeleteRuleRequest": {"type": "structure", "required": ["Identifier"], "members": {"Identifier": {"shape": "RuleIdentifier", "documentation": "<p>The unique ID of the retention rule.</p>", "location": "uri", "locationName": "identifier"}}}, "DeleteRuleResponse": {"type": "structure", "members": {}}, "Description": {"type": "string", "pattern": "^[\\S ]{0,255}$"}, "ErrorMessage": {"type": "string"}, "ExcludeResourceTags": {"type": "list", "member": {"shape": "ResourceTag"}, "max": 5, "min": 0}, "GetRuleRequest": {"type": "structure", "required": ["Identifier"], "members": {"Identifier": {"shape": "RuleIdentifier", "documentation": "<p>The unique ID of the retention rule.</p>", "location": "uri", "locationName": "identifier"}}}, "GetRuleResponse": {"type": "structure", "members": {"Identifier": {"shape": "RuleIdentifier", "documentation": "<p>The unique ID of the retention rule.</p>"}, "Description": {"shape": "Description", "documentation": "<p>The retention rule description.</p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>The resource type retained by the retention rule.</p>"}, "RetentionPeriod": {"shape": "RetentionPeriod", "documentation": "<p>Information about the retention period for which the retention rule is to retain resources.</p>"}, "ResourceTags": {"shape": "ResourceTags", "documentation": "<p>[Tag-level retention rules only] Information about the resource tags used to identify resources that are retained by the retention rule.</p>"}, "Status": {"shape": "RuleStatus", "documentation": "<p>The state of the retention rule. Only retention rules that are in the <code>available</code> state retain resources.</p>"}, "LockConfiguration": {"shape": "LockConfiguration", "documentation": "<p>Information about the retention rule lock configuration.</p>"}, "LockState": {"shape": "LockState", "documentation": "<p>[Region-level retention rules only] The lock state for the retention rule.</p> <ul> <li> <p> <code>locked</code> - The retention rule is locked and can't be modified or deleted.</p> </li> <li> <p> <code>pending_unlock</code> - The retention rule has been unlocked but it is still within the unlock delay period. The retention rule can be modified or deleted only after the unlock delay period has expired.</p> </li> <li> <p> <code>unlocked</code> - The retention rule is unlocked and it can be modified or deleted by any user with the required permissions.</p> </li> <li> <p> <code>null</code> - The retention rule has never been locked. Once a retention rule has been locked, it can transition between the <code>locked</code> and <code>unlocked</code> states only; it can never transition back to <code>null</code>.</p> </li> </ul>"}, "LockEndTime": {"shape": "TimeStamp", "documentation": "<p>The date and time at which the unlock delay is set to expire. Only returned for retention rules that have been unlocked and that are still within the unlock delay period.</p>"}, "RuleArn": {"shape": "RuleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the retention rule.</p>"}, "ExcludeResourceTags": {"shape": "ExcludeResourceTags", "documentation": "<p>[Region-level retention rules only] Information about the exclusion tags used to identify resources that are to be excluded, or ignored, by the retention rule.</p>"}}}, "InternalServerException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The service could not respond to the request due to an internal problem.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true}, "ListRulesRequest": {"type": "structure", "required": ["ResourceType"], "members": {"MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return with a single call. To retrieve the remaining results, make another call with the returned <code>NextToken</code> value.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token for the next page of results.</p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>The resource type retained by the retention rule. Only retention rules that retain the specified resource type are listed. Currently, only Amazon EBS snapshots and EBS-backed AMIs are supported. To list retention rules that retain snapshots, specify <code>EBS_SNAPSHOT</code>. To list retention rules that retain EBS-backed AMIs, specify <code>EC2_IMAGE</code>.</p>"}, "ResourceTags": {"shape": "ResourceTags", "documentation": "<p>[Tag-level retention rules only] Information about the resource tags used to identify resources that are retained by the retention rule.</p>"}, "LockState": {"shape": "LockState", "documentation": "<p>The lock state of the retention rules to list. Only retention rules with the specified lock state are returned.</p>"}, "ExcludeResourceTags": {"shape": "ExcludeResourceTags", "documentation": "<p>[Region-level retention rules only] Information about the exclusion tags used to identify resources that are to be excluded, or ignored, by the retention rule.</p>"}}}, "ListRulesResponse": {"type": "structure", "members": {"Rules": {"shape": "RuleSummaryList", "documentation": "<p>Information about the retention rules.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to use to retrieve the next page of results. This value is <code>null</code> when there are no more results to return.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "RuleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the retention rule.</p>", "location": "uri", "locationName": "resourceArn"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"Tags": {"shape": "TagList", "documentation": "<p>Information about the tags assigned to the retention rule.</p>"}}}, "LockConfiguration": {"type": "structure", "required": ["UnlockDelay"], "members": {"UnlockDelay": {"shape": "UnlockDelay", "documentation": "<p>Information about the retention rule unlock delay.</p>"}}, "documentation": "<p>Information about a retention rule lock configuration.</p>"}, "LockRuleRequest": {"type": "structure", "required": ["Identifier", "LockConfiguration"], "members": {"Identifier": {"shape": "RuleIdentifier", "documentation": "<p>The unique ID of the retention rule.</p>", "location": "uri", "locationName": "identifier"}, "LockConfiguration": {"shape": "LockConfiguration", "documentation": "<p>Information about the retention rule lock configuration.</p>"}}}, "LockRuleResponse": {"type": "structure", "members": {"Identifier": {"shape": "RuleIdentifier", "documentation": "<p>The unique ID of the retention rule.</p>"}, "Description": {"shape": "Description", "documentation": "<p>The retention rule description.</p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>The resource type retained by the retention rule.</p>"}, "RetentionPeriod": {"shape": "RetentionPeriod"}, "ResourceTags": {"shape": "ResourceTags", "documentation": "<p>[Tag-level retention rules only] Information about the resource tags used to identify resources that are retained by the retention rule.</p>"}, "Status": {"shape": "RuleStatus", "documentation": "<p>The state of the retention rule. Only retention rules that are in the <code>available</code> state retain resources.</p>"}, "LockConfiguration": {"shape": "LockConfiguration", "documentation": "<p>Information about the retention rule lock configuration.</p>"}, "LockState": {"shape": "LockState", "documentation": "<p>[Region-level retention rules only] The lock state for the retention rule.</p> <ul> <li> <p> <code>locked</code> - The retention rule is locked and can't be modified or deleted.</p> </li> <li> <p> <code>pending_unlock</code> - The retention rule has been unlocked but it is still within the unlock delay period. The retention rule can be modified or deleted only after the unlock delay period has expired.</p> </li> <li> <p> <code>unlocked</code> - The retention rule is unlocked and it can be modified or deleted by any user with the required permissions.</p> </li> <li> <p> <code>null</code> - The retention rule has never been locked. Once a retention rule has been locked, it can transition between the <code>locked</code> and <code>unlocked</code> states only; it can never transition back to <code>null</code>.</p> </li> </ul>"}, "RuleArn": {"shape": "RuleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the retention rule.</p>"}, "ExcludeResourceTags": {"shape": "ExcludeResourceTags", "documentation": "<p>[Region-level retention rules only] Information about the exclusion tags used to identify resources that are to be excluded, or ignored, by the retention rule.</p>"}}}, "LockState": {"type": "string", "enum": ["locked", "pending_unlock", "unlocked"]}, "MaxResults": {"type": "integer", "max": 1000, "min": 1}, "NextToken": {"type": "string", "pattern": "^[A-Za-z0-9+/=]{1,2048}$"}, "ResourceNotFoundException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}, "Reason": {"shape": "ResourceNotFoundExceptionReason", "documentation": "<p>The reason for the exception.</p>"}}, "documentation": "<p>The specified resource was not found.</p>", "error": {"httpStatusCode": 404}, "exception": true}, "ResourceNotFoundExceptionReason": {"type": "string", "enum": ["RULE_NOT_FOUND"]}, "ResourceTag": {"type": "structure", "required": ["ResourceTagKey"], "members": {"ResourceTagKey": {"shape": "ResourceTagKey", "documentation": "<p>The tag key.</p>"}, "ResourceTagValue": {"shape": "ResourceTagValue", "documentation": "<p>The tag value.</p>"}}, "documentation": "<p>[Tag-level retention rules only] Information about the resource tags used to identify resources that are retained by the retention rule.</p>"}, "ResourceTagKey": {"type": "string", "pattern": "^[\\S\\s]{1,128}$"}, "ResourceTagValue": {"type": "string", "pattern": "^[\\S\\s]{0,256}$"}, "ResourceTags": {"type": "list", "member": {"shape": "ResourceTag"}, "max": 50, "min": 0}, "ResourceType": {"type": "string", "enum": ["EBS_SNAPSHOT", "EC2_IMAGE"]}, "RetentionPeriod": {"type": "structure", "required": ["RetentionPeriodValue", "RetentionPeriodUnit"], "members": {"RetentionPeriodValue": {"shape": "RetentionPeriodValue", "documentation": "<p>The period value for which the retention rule is to retain resources. The period is measured using the unit specified for <b>RetentionPeriodUnit</b>.</p>"}, "RetentionPeriodUnit": {"shape": "RetentionPeriodUnit", "documentation": "<p>The unit of time in which the retention period is measured. Currently, only <code>DAYS</code> is supported.</p>"}}, "documentation": "<p>Information about the retention period for which the retention rule is to retain resources.</p>"}, "RetentionPeriodUnit": {"type": "string", "enum": ["DAYS"]}, "RetentionPeriodValue": {"type": "integer", "max": 3650, "min": 1}, "RuleArn": {"type": "string", "max": 1011, "min": 0, "pattern": "^arn:aws(-[a-z]{1,3}){0,2}:rbin:[a-z\\-0-9]{0,63}:[0-9]{12}:rule/[0-9a-zA-Z]{11}{0,1011}$"}, "RuleIdentifier": {"type": "string", "pattern": "[0-9a-zA-Z]{11}"}, "RuleStatus": {"type": "string", "enum": ["pending", "available"]}, "RuleSummary": {"type": "structure", "members": {"Identifier": {"shape": "RuleIdentifier", "documentation": "<p>The unique ID of the retention rule.</p>"}, "Description": {"shape": "Description", "documentation": "<p>The retention rule description.</p>"}, "RetentionPeriod": {"shape": "RetentionPeriod", "documentation": "<p>Information about the retention period for which the retention rule is to retain resources.</p>"}, "LockState": {"shape": "LockState", "documentation": "<p>[Region-level retention rules only] The lock state for the retention rule.</p> <ul> <li> <p> <code>locked</code> - The retention rule is locked and can't be modified or deleted.</p> </li> <li> <p> <code>pending_unlock</code> - The retention rule has been unlocked but it is still within the unlock delay period. The retention rule can be modified or deleted only after the unlock delay period has expired.</p> </li> <li> <p> <code>unlocked</code> - The retention rule is unlocked and it can be modified or deleted by any user with the required permissions.</p> </li> <li> <p> <code>null</code> - The retention rule has never been locked. Once a retention rule has been locked, it can transition between the <code>locked</code> and <code>unlocked</code> states only; it can never transition back to <code>null</code>.</p> </li> </ul>"}, "RuleArn": {"shape": "RuleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the retention rule.</p>"}}, "documentation": "<p>Information about a Recycle Bin retention rule.</p>"}, "RuleSummaryList": {"type": "list", "member": {"shape": "RuleSummary"}}, "ServiceQuotaExceededException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}, "Reason": {"shape": "ServiceQuotaExceededExceptionReason", "documentation": "<p>The reason for the exception.</p>"}}, "documentation": "<p>The request would cause a service quota for the number of tags per resource to be exceeded.</p>", "error": {"httpStatusCode": 402}, "exception": true}, "ServiceQuotaExceededExceptionReason": {"type": "string", "enum": ["SERVICE_QUOTA_EXCEEDED"]}, "Tag": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {"shape": "TagKey", "documentation": "<p>The tag key.</p>"}, "Value": {"shape": "TagValue", "documentation": "<p>The tag value.</p>"}}, "documentation": "<p>Information about the tags to assign to the retention rule.</p>"}, "TagKey": {"type": "string", "max": 128, "min": 1, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 200, "min": 0}, "TagList": {"type": "list", "member": {"shape": "Tag"}, "max": 200, "min": 0}, "TagResourceRequest": {"type": "structure", "required": ["ResourceArn", "Tags"], "members": {"ResourceArn": {"shape": "RuleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the retention rule.</p>", "location": "uri", "locationName": "resourceArn"}, "Tags": {"shape": "TagList", "documentation": "<p>Information about the tags to assign to the retention rule.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "TimeStamp": {"type": "timestamp"}, "UnlockDelay": {"type": "structure", "required": ["UnlockDelayValue", "UnlockDelayUnit"], "members": {"UnlockDelayValue": {"shape": "UnlockDelayValue", "documentation": "<p>The unlock delay period, measured in the unit specified for <b> UnlockDelayUnit</b>.</p>"}, "UnlockDelayUnit": {"shape": "UnlockDelayUnit", "documentation": "<p>The unit of time in which to measure the unlock delay. Currently, the unlock delay can be measure only in days.</p>"}}, "documentation": "<p>Information about the retention rule unlock delay. The unlock delay is the period after which a retention rule can be modified or edited after it has been unlocked by a user with the required permissions. The retention rule can't be modified or deleted during the unlock delay.</p>"}, "UnlockDelayUnit": {"type": "string", "enum": ["DAYS"]}, "UnlockDelayValue": {"type": "integer", "max": 30, "min": 7}, "UnlockRuleRequest": {"type": "structure", "required": ["Identifier"], "members": {"Identifier": {"shape": "RuleIdentifier", "documentation": "<p>The unique ID of the retention rule.</p>", "location": "uri", "locationName": "identifier"}}}, "UnlockRuleResponse": {"type": "structure", "members": {"Identifier": {"shape": "RuleIdentifier", "documentation": "<p>The unique ID of the retention rule.</p>"}, "Description": {"shape": "Description", "documentation": "<p>The retention rule description.</p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>The resource type retained by the retention rule.</p>"}, "RetentionPeriod": {"shape": "RetentionPeriod"}, "ResourceTags": {"shape": "ResourceTags", "documentation": "<p>[Tag-level retention rules only] Information about the resource tags used to identify resources that are retained by the retention rule.</p>"}, "Status": {"shape": "RuleStatus", "documentation": "<p>The state of the retention rule. Only retention rules that are in the <code>available</code> state retain resources.</p>"}, "LockConfiguration": {"shape": "LockConfiguration", "documentation": "<p>Information about the retention rule lock configuration.</p>"}, "LockState": {"shape": "LockState", "documentation": "<p>[Region-level retention rules only] The lock state for the retention rule.</p> <ul> <li> <p> <code>locked</code> - The retention rule is locked and can't be modified or deleted.</p> </li> <li> <p> <code>pending_unlock</code> - The retention rule has been unlocked but it is still within the unlock delay period. The retention rule can be modified or deleted only after the unlock delay period has expired.</p> </li> <li> <p> <code>unlocked</code> - The retention rule is unlocked and it can be modified or deleted by any user with the required permissions.</p> </li> <li> <p> <code>null</code> - The retention rule has never been locked. Once a retention rule has been locked, it can transition between the <code>locked</code> and <code>unlocked</code> states only; it can never transition back to <code>null</code>.</p> </li> </ul>"}, "LockEndTime": {"shape": "TimeStamp", "documentation": "<p>The date and time at which the unlock delay is set to expire. Only returned for retention rules that have been unlocked and that are still within the unlock delay period.</p>"}, "RuleArn": {"shape": "RuleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the retention rule.</p>"}, "ExcludeResourceTags": {"shape": "ExcludeResourceTags", "documentation": "<p>[Region-level retention rules only] Information about the exclusion tags used to identify resources that are to be excluded, or ignored, by the retention rule.</p>"}}}, "UntagResourceRequest": {"type": "structure", "required": ["ResourceArn", "TagKeys"], "members": {"ResourceArn": {"shape": "RuleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the retention rule.</p>", "location": "uri", "locationName": "resourceArn"}, "TagKeys": {"shape": "TagKeyList", "documentation": "<p>The tag keys of the tags to unassign. All tags that have the specified tag key are unassigned.</p>", "location": "querystring", "locationName": "tagKeys"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateRuleRequest": {"type": "structure", "required": ["Identifier"], "members": {"Identifier": {"shape": "RuleIdentifier", "documentation": "<p>The unique ID of the retention rule.</p>", "location": "uri", "locationName": "identifier"}, "RetentionPeriod": {"shape": "RetentionPeriod", "documentation": "<p>Information about the retention period for which the retention rule is to retain resources.</p>"}, "Description": {"shape": "Description", "documentation": "<p>The retention rule description.</p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<note> <p>This parameter is currently not supported. You can't update a retention rule's resource type after creation.</p> </note>"}, "ResourceTags": {"shape": "ResourceTags", "documentation": "<p>[Tag-level retention rules only] Specifies the resource tags to use to identify resources that are to be retained by a tag-level retention rule. For tag-level retention rules, only deleted resources, of the specified resource type, that have one or more of the specified tag key and value pairs are retained. If a resource is deleted, but it does not have any of the specified tag key and value pairs, it is immediately deleted without being retained by the retention rule.</p> <p>You can add the same tag key and value pair to a maximum or five retention rules.</p> <p>To create a Region-level retention rule, omit this parameter. A Region-level retention rule does not have any resource tags specified. It retains all deleted resources of the specified resource type in the Region in which the rule is created, even if the resources are not tagged.</p>"}, "ExcludeResourceTags": {"shape": "ExcludeResourceTags", "documentation": "<p>[Region-level retention rules only] Specifies the exclusion tags to use to identify resources that are to be excluded, or ignored, by a Region-level retention rule. Resources that have any of these tags are not retained by the retention rule upon deletion.</p> <p>You can't specify exclusion tags for tag-level retention rules.</p>"}}}, "UpdateRuleResponse": {"type": "structure", "members": {"Identifier": {"shape": "RuleIdentifier", "documentation": "<p>The unique ID of the retention rule.</p>"}, "RetentionPeriod": {"shape": "RetentionPeriod"}, "Description": {"shape": "Description", "documentation": "<p>The retention rule description.</p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>The resource type retained by the retention rule.</p>"}, "ResourceTags": {"shape": "ResourceTags", "documentation": "<p>[Tag-level retention rules only] Information about the resource tags used to identify resources that are retained by the retention rule.</p>"}, "Status": {"shape": "RuleStatus", "documentation": "<p>The state of the retention rule. Only retention rules that are in the <code>available</code> state retain resources.</p>"}, "LockState": {"shape": "LockState", "documentation": "<p>[Region-level retention rules only] The lock state for the retention rule.</p> <ul> <li> <p> <code>locked</code> - The retention rule is locked and can't be modified or deleted.</p> </li> <li> <p> <code>pending_unlock</code> - The retention rule has been unlocked but it is still within the unlock delay period. The retention rule can be modified or deleted only after the unlock delay period has expired.</p> </li> <li> <p> <code>unlocked</code> - The retention rule is unlocked and it can be modified or deleted by any user with the required permissions.</p> </li> <li> <p> <code>null</code> - The retention rule has never been locked. Once a retention rule has been locked, it can transition between the <code>locked</code> and <code>unlocked</code> states only; it can never transition back to <code>null</code>.</p> </li> </ul>"}, "LockEndTime": {"shape": "TimeStamp", "documentation": "<p>The date and time at which the unlock delay is set to expire. Only returned for retention rules that have been unlocked and that are still within the unlock delay period.</p>"}, "RuleArn": {"shape": "RuleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the retention rule.</p>"}, "ExcludeResourceTags": {"shape": "ExcludeResourceTags", "documentation": "<p>[Region-level retention rules only] Information about the exclusion tags used to identify resources that are to be excluded, or ignored, by the retention rule.</p>"}}}, "ValidationException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}, "Reason": {"shape": "ValidationExceptionReason", "documentation": "<p>The reason for the exception.</p>"}}, "documentation": "<p>One or more of the parameters in the request is not valid.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "ValidationExceptionReason": {"type": "string", "enum": ["INVALID_PAGE_TOKEN", "INVALID_PARAMETER_VALUE"]}}, "documentation": "<p>This is the <i>Recycle Bin API Reference</i>. This documentation provides descriptions and syntax for each of the actions and data types in Recycle Bin.</p> <p>Recycle Bin is a resource recovery feature that enables you to restore accidentally deleted snapshots and EBS-backed AMIs. When using Recycle Bin, if your resources are deleted, they are retained in the Recycle Bin for a time period that you specify.</p> <p>You can restore a resource from the Recycle Bin at any time before its retention period expires. After you restore a resource from the Recycle Bin, the resource is removed from the Recycle Bin, and you can then use it in the same way you use any other resource of that type in your account. If the retention period expires and the resource is not restored, the resource is permanently deleted from the Recycle Bin and is no longer available for recovery. For more information about Recycle Bin, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/recycle-bin.html\"> Recycle Bin</a> in the <i>Amazon Elastic Compute Cloud User Guide</i>.</p>"}