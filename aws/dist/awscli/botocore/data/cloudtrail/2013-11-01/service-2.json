{"version": "2.0", "metadata": {"apiVersion": "2013-11-01", "endpointPrefix": "cloudtrail", "jsonVersion": "1.1", "protocol": "json", "protocols": ["json"], "serviceAbbreviation": "CloudTrail", "serviceFullName": "AWS CloudTrail", "serviceId": "CloudTrail", "signatureVersion": "v4", "targetPrefix": "com.amazonaws.cloudtrail.v20131101.CloudTrail_20131101", "uid": "cloudtrail-2013-11-01", "auth": ["aws.auth#sigv4"]}, "operations": {"AddTags": {"name": "AddTags", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AddTagsRequest"}, "output": {"shape": "AddTagsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "CloudTrailARNInvalidException"}, {"shape": "EventDataStoreARNInvalidException"}, {"shape": "ChannelARNInvalidException"}, {"shape": "ResourceTypeNotSupportedException"}, {"shape": "TagsLimitExceededException"}, {"shape": "InvalidTrailNameException"}, {"shape": "InvalidTagParameterException"}, {"shape": "InactiveEventDataStoreException"}, {"shape": "EventDataStoreNotFoundException"}, {"shape": "ChannelNotFoundException"}, {"shape": "UnsupportedOperationException"}, {"shape": "OperationNotPermittedException"}, {"shape": "NotOrganizationMasterAccountException"}, {"shape": "NoManagementAccountSLRExistsException"}, {"shape": "ConflictException"}], "documentation": "<p>Adds one or more tags to a trail, event data store, dashboard, or channel, up to a limit of 50. Overwrites an existing tag's value when a new value is specified for an existing tag key. Tag key names must be unique; you cannot have two keys with the same name but different values. If you specify a key without a value, the tag will be created with the specified key and a value of null. You can tag a trail or event data store that applies to all Amazon Web Services Regions only from the Region in which the trail or event data store was created (also known as its home Region).</p>", "idempotent": true}, "CancelQuery": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CancelQueryRequest"}, "output": {"shape": "CancelQueryResponse"}, "errors": [{"shape": "EventDataStoreARNInvalidException"}, {"shape": "EventDataStoreNotFoundException"}, {"shape": "InactiveEventDataStoreException"}, {"shape": "InactiveQueryException"}, {"shape": "InvalidParameterException"}, {"shape": "QueryIdNotFoundException"}, {"shape": "OperationNotPermittedException"}, {"shape": "UnsupportedOperationException"}, {"shape": "NoManagementAccountSLRExistsException"}, {"shape": "ConflictException"}], "documentation": "<p>Cancels a query if the query is not in a terminated state, such as <code>CANCELLED</code>, <code>FAILED</code>, <code>TIMED_OUT</code>, or <code>FINISHED</code>. You must specify an ARN value for <code>EventDataStore</code>. The ID of the query that you want to cancel is also required. When you run <code>CancelQuery</code>, the query status might show as <code>CANCELLED</code> even if the operation is not yet finished.</p>", "idempotent": true}, "CreateChannel": {"name": "CreateChannel", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateChannelRequest"}, "output": {"shape": "CreateChannelResponse"}, "errors": [{"shape": "ChannelMaxLimitExceededException"}, {"shape": "InvalidSourceException"}, {"shape": "ChannelAlreadyExistsException"}, {"shape": "EventDataStoreARNInvalidException"}, {"shape": "EventDataStoreNotFoundException"}, {"shape": "InvalidEventDataStoreCategoryException"}, {"shape": "InactiveEventDataStoreException"}, {"shape": "InvalidParameterException"}, {"shape": "InvalidTagParameterException"}, {"shape": "TagsLimitExceededException"}, {"shape": "OperationNotPermittedException"}, {"shape": "UnsupportedOperationException"}], "documentation": "<p>Creates a channel for CloudTrail to ingest events from a partner or external source. After you create a channel, a CloudTrail Lake event data store can log events from the partner or source that you specify.</p>"}, "CreateDashboard": {"name": "CreateDashboard", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateDashboardRequest"}, "output": {"shape": "CreateDashboardResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "InvalidTagParameterException"}, {"shape": "EventDataStoreNotFoundException"}, {"shape": "InactiveEventDataStoreException"}, {"shape": "InsufficientEncryptionPolicyException"}, {"shape": "InvalidQueryStatementException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "UnsupportedOperationException"}], "documentation": "<p> Creates a custom dashboard or the Highlights dashboard. </p> <ul> <li> <p> <b>Custom dashboards</b> - Custom dashboards allow you to query events in any event data store type. You can add up to 10 widgets to a custom dashboard. You can manually refresh a custom dashboard, or you can set a refresh schedule.</p> </li> <li> <p> <b>Highlights dashboard</b> - You can create the Highlights dashboard to see a summary of key user activities and API usage across all your event data stores. CloudTrail Lake manages the Highlights dashboard and refreshes the dashboard every 6 hours. To create the Highlights dashboard, you must set and enable a refresh schedule.</p> </li> </ul> <p> CloudTrail runs queries to populate the dashboard's widgets during a manual or scheduled refresh. CloudTrail must be granted permissions to run the <code>StartQuery</code> operation on your behalf. To provide permissions, run the <code>PutResourcePolicy</code> operation to attach a resource-based policy to each event data store. For more information, see <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/security_iam_resource-based-policy-examples.html#security_iam_resource-based-policy-examples-eds-dashboard\">Example: Allow CloudTrail to run queries to populate a dashboard</a> in the <i>CloudTrail User Guide</i>. </p> <p> To set a refresh schedule, CloudTrail must be granted permissions to run the <code>StartDashboardRefresh</code> operation to refresh the dashboard on your behalf. To provide permissions, run the <code>PutResourcePolicy</code> operation to attach a resource-based policy to the dashboard. For more information, see <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/security_iam_resource-based-policy-examples.html#security_iam_resource-based-policy-examples-dashboards\"> Resource-based policy example for a dashboard</a> in the <i>CloudTrail User Guide</i>. </p> <p>For more information about dashboards, see <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/lake-dashboard.html\">CloudTrail Lake dashboards</a> in the <i>CloudTrail User Guide</i>.</p>", "idempotent": true}, "CreateEventDataStore": {"name": "CreateEventDataStore", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateEventDataStoreRequest"}, "output": {"shape": "CreateEventDataStoreResponse"}, "errors": [{"shape": "EventDataStoreAlreadyExistsException"}, {"shape": "EventDataStoreMaxLimitExceededException"}, {"shape": "InvalidEventSelectorsException"}, {"shape": "InvalidParameterException"}, {"shape": "InvalidTagParameterException"}, {"shape": "OperationNotPermittedException"}, {"shape": "UnsupportedOperationException"}, {"shape": "ConflictException"}, {"shape": "InsufficientEncryptionPolicyException"}, {"shape": "InvalidKmsKeyIdException"}, {"shape": "KmsKeyNotFoundException"}, {"shape": "KmsException"}, {"shape": "CloudTrailAccessNotEnabledException"}, {"shape": "InsufficientDependencyServiceAccessPermissionException"}, {"shape": "NotOrganizationMasterAccountException"}, {"shape": "OrganizationsNotInUseException"}, {"shape": "OrganizationNotInAllFeaturesModeException"}, {"shape": "NoManagementAccountSLRExistsException"}], "documentation": "<p>Creates a new event data store.</p>"}, "CreateTrail": {"name": "CreateTrail", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateTrailRequest"}, "output": {"shape": "CreateTrailResponse"}, "errors": [{"shape": "MaximumNumberOfTrailsExceededException"}, {"shape": "TrailAlreadyExistsException"}, {"shape": "S3BucketDoesNotExistException"}, {"shape": "InsufficientS3BucketPolicyException"}, {"shape": "InsufficientSnsTopicPolicyException"}, {"shape": "InsufficientEncryptionPolicyException"}, {"shape": "InvalidS3BucketNameException"}, {"shape": "InvalidS3PrefixException"}, {"shape": "InvalidSnsTopicNameException"}, {"shape": "InvalidKmsKeyIdException"}, {"shape": "InvalidTrailNameException"}, {"shape": "TrailNotProvidedException"}, {"shape": "TagsLimitExceededException"}, {"shape": "InvalidParameterCombinationException"}, {"shape": "InvalidParameterException"}, {"shape": "KmsKeyNotFoundException"}, {"shape": "KmsKeyDisabledException"}, {"shape": "KmsException"}, {"shape": "InvalidCloudWatchLogsLogGroupArnException"}, {"shape": "InvalidCloudWatchLogsRoleArnException"}, {"shape": "CloudWatchLogsDeliveryUnavailableException"}, {"shape": "InvalidTagParameterException"}, {"shape": "UnsupportedOperationException"}, {"shape": "OperationNotPermittedException"}, {"shape": "CloudTrailAccessNotEnabledException"}, {"shape": "InsufficientDependencyServiceAccessPermissionException"}, {"shape": "NotOrganizationMasterAccountException"}, {"shape": "OrganizationsNotInUseException"}, {"shape": "OrganizationNotInAllFeaturesModeException"}, {"shape": "NoManagementAccountSLRExistsException"}, {"shape": "CloudTrailInvalidClientTokenIdException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Creates a trail that specifies the settings for delivery of log data to an Amazon S3 bucket. </p>", "idempotent": true}, "DeleteChannel": {"name": "DeleteChannel", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteChannelRequest"}, "output": {"shape": "DeleteChannelResponse"}, "errors": [{"shape": "ChannelARNInvalidException"}, {"shape": "ChannelNotFoundException"}, {"shape": "OperationNotPermittedException"}, {"shape": "UnsupportedOperationException"}], "documentation": "<p>Deletes a channel.</p>"}, "DeleteDashboard": {"name": "DeleteDashboard", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteDashboardRequest"}, "output": {"shape": "DeleteDashboardResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "UnsupportedOperationException"}], "documentation": "<p> Deletes the specified dashboard. You cannot delete a dashboard that has termination protection enabled. </p>", "idempotent": true}, "DeleteEventDataStore": {"name": "DeleteEventDataStore", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteEventDataStoreRequest"}, "output": {"shape": "DeleteEventDataStoreResponse"}, "errors": [{"shape": "EventDataStoreARNInvalidException"}, {"shape": "EventDataStoreNotFoundException"}, {"shape": "EventDataStoreTerminationProtectedException"}, {"shape": "EventDataStoreHasOngoingImportException"}, {"shape": "InactiveEventDataStoreException"}, {"shape": "InvalidParameterException"}, {"shape": "OperationNotPermittedException"}, {"shape": "UnsupportedOperationException"}, {"shape": "NotOrganizationMasterAccountException"}, {"shape": "NoManagementAccountSLRExistsException"}, {"shape": "ChannelExistsForEDSException"}, {"shape": "InsufficientDependencyServiceAccessPermissionException"}, {"shape": "ConflictException"}, {"shape": "EventDataStoreFederationEnabledException"}], "documentation": "<p>Disables the event data store specified by <code>EventDataStore</code>, which accepts an event data store ARN. After you run <code>DeleteEventDataStore</code>, the event data store enters a <code>PENDING_DELETION</code> state, and is automatically deleted after a wait period of seven days. <code>TerminationProtectionEnabled</code> must be set to <code>False</code> on the event data store and the <code>FederationStatus</code> must be <code>DISABLED</code>. You cannot delete an event data store if <code>TerminationProtectionEnabled</code> is <code>True</code> or the <code>FederationStatus</code> is <code>ENABLED</code>.</p> <p>After you run <code>DeleteEventDataStore</code> on an event data store, you cannot run <code>ListQueries</code>, <code>DescribeQuery</code>, or <code>GetQueryResults</code> on queries that are using an event data store in a <code>PENDING_DELETION</code> state. An event data store in the <code>PENDING_DELETION</code> state does not incur costs.</p>"}, "DeleteResourcePolicy": {"name": "DeleteResourcePolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteResourcePolicyRequest"}, "output": {"shape": "DeleteResourcePolicyResponse"}, "errors": [{"shape": "ResourceARNNotValidException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourcePolicyNotFoundException"}, {"shape": "ResourceTypeNotSupportedException"}, {"shape": "ConflictException"}, {"shape": "OperationNotPermittedException"}, {"shape": "UnsupportedOperationException"}], "documentation": "<p> Deletes the resource-based policy attached to the CloudTrail event data store, dashboard, or channel. </p>", "idempotent": true}, "DeleteTrail": {"name": "DeleteTrail", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteTrailRequest"}, "output": {"shape": "DeleteTrailResponse"}, "errors": [{"shape": "TrailNotFoundException"}, {"shape": "InvalidTrailNameException"}, {"shape": "CloudTrailARNInvalidException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InvalidHomeRegionException"}, {"shape": "UnsupportedOperationException"}, {"shape": "OperationNotPermittedException"}, {"shape": "NotOrganizationMasterAccountException"}, {"shape": "NoManagementAccountSLRExistsException"}, {"shape": "InsufficientDependencyServiceAccessPermissionException"}], "documentation": "<p>Deletes a trail. This operation must be called from the Region in which the trail was created. <code>DeleteTrail</code> cannot be called on the shadow trails (replicated trails in other Regions) of a trail that is enabled in all Regions.</p>", "idempotent": true}, "DeregisterOrganizationDelegatedAdmin": {"name": "DeregisterOrganizationDelegatedAdmin", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeregisterOrganizationDelegatedAdminRequest"}, "output": {"shape": "DeregisterOrganizationDelegatedAdminResponse"}, "errors": [{"shape": "AccountNotFoundException"}, {"shape": "AccountNotRegisteredException"}, {"shape": "CloudTrailAccessNotEnabledException"}, {"shape": "ConflictException"}, {"shape": "InsufficientDependencyServiceAccessPermissionException"}, {"shape": "InvalidParameterException"}, {"shape": "NotOrganizationManagementAccountException"}, {"shape": "OrganizationNotInAllFeaturesModeException"}, {"shape": "OrganizationsNotInUseException"}, {"shape": "UnsupportedOperationException"}, {"shape": "OperationNotPermittedException"}], "documentation": "<p>Removes CloudTrail delegated administrator permissions from a member account in an organization.</p>", "idempotent": true}, "DescribeQuery": {"name": "Describe<PERSON><PERSON>y", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeQueryRequest"}, "output": {"shape": "DescribeQueryResponse"}, "errors": [{"shape": "EventDataStoreARNInvalidException"}, {"shape": "EventDataStoreNotFoundException"}, {"shape": "InactiveEventDataStoreException"}, {"shape": "InvalidParameterException"}, {"shape": "QueryIdNotFoundException"}, {"shape": "OperationNotPermittedException"}, {"shape": "UnsupportedOperationException"}, {"shape": "NoManagementAccountSLRExistsException"}], "documentation": "<p>Returns metadata about a query, including query run time in milliseconds, number of events scanned and matched, and query status. If the query results were delivered to an S3 bucket, the response also provides the S3 URI and the delivery status.</p> <p>You must specify either <code>QueryId</code> or <code>QueryAlias</code>. Specifying the <code>QueryAlias</code> parameter returns information about the last query run for the alias. You can provide <code>RefreshId</code> along with <code>QueryAlias</code> to view the query results of a dashboard query for the specified <code>RefreshId</code>.</p>", "idempotent": true}, "DescribeTrails": {"name": "DescribeTrails", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeTrailsRequest"}, "output": {"shape": "DescribeTrailsResponse"}, "errors": [{"shape": "UnsupportedOperationException"}, {"shape": "OperationNotPermittedException"}, {"shape": "InvalidTrailNameException"}, {"shape": "CloudTrailARNInvalidException"}, {"shape": "NoManagementAccountSLRExistsException"}], "documentation": "<p>Retrieves settings for one or more trails associated with the current Region for your account.</p>", "idempotent": true}, "DisableFederation": {"name": "DisableFederation", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DisableFederationRequest"}, "output": {"shape": "DisableFederationResponse"}, "errors": [{"shape": "EventDataStoreARNInvalidException"}, {"shape": "EventDataStoreNotFoundException"}, {"shape": "InvalidParameterException"}, {"shape": "InactiveEventDataStoreException"}, {"shape": "OperationNotPermittedException"}, {"shape": "UnsupportedOperationException"}, {"shape": "CloudTrailAccessNotEnabledException"}, {"shape": "InsufficientDependencyServiceAccessPermissionException"}, {"shape": "NotOrganizationMasterAccountException"}, {"shape": "NoManagementAccountSLRExistsException"}, {"shape": "OrganizationsNotInUseException"}, {"shape": "OrganizationNotInAllFeaturesModeException"}, {"shape": "ConcurrentModificationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Disables Lake query federation on the specified event data store. When you disable federation, CloudTrail disables the integration with Glue, Lake Formation, and Amazon Athena. After disabling Lake query federation, you can no longer query your event data in Amazon Athena.</p> <p>No CloudTrail Lake data is deleted when you disable federation and you can continue to run queries in CloudTrail Lake.</p>"}, "EnableFederation": {"name": "EnableFederation", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "EnableFederationRequest"}, "output": {"shape": "EnableFederationResponse"}, "errors": [{"shape": "EventDataStoreARNInvalidException"}, {"shape": "EventDataStoreNotFoundException"}, {"shape": "InvalidParameterException"}, {"shape": "InactiveEventDataStoreException"}, {"shape": "OperationNotPermittedException"}, {"shape": "UnsupportedOperationException"}, {"shape": "CloudTrailAccessNotEnabledException"}, {"shape": "InsufficientDependencyServiceAccessPermissionException"}, {"shape": "NotOrganizationMasterAccountException"}, {"shape": "NoManagementAccountSLRExistsException"}, {"shape": "OrganizationsNotInUseException"}, {"shape": "OrganizationNotInAllFeaturesModeException"}, {"shape": "ConcurrentModificationException"}, {"shape": "AccessDeniedException"}, {"shape": "EventDataStoreFederationEnabledException"}], "documentation": "<p> Enables Lake query federation on the specified event data store. Federating an event data store lets you view the metadata associated with the event data store in the Glue <a href=\"https://docs.aws.amazon.com/glue/latest/dg/components-overview.html#data-catalog-intro\">Data Catalog</a> and run SQL queries against your event data using Amazon Athena. The table metadata stored in the Glue Data Catalog lets the Athena query engine know how to find, read, and process the data that you want to query.</p> <p>When you enable Lake query federation, CloudTrail creates a managed database named <code>aws:cloudtrail</code> (if the database doesn't already exist) and a managed federated table in the Glue Data Catalog. The event data store ID is used for the table name. CloudTrail registers the role ARN and event data store in <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/query-federation-lake-formation.html\">Lake Formation</a>, the service responsible for allowing fine-grained access control of the federated resources in the Glue Data Catalog.</p> <p>For more information about Lake query federation, see <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/query-federation.html\">Federate an event data store</a>.</p>"}, "GenerateQuery": {"name": "GenerateQuery", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GenerateQueryRequest"}, "output": {"shape": "GenerateQueryResponse"}, "errors": [{"shape": "EventDataStoreARNInvalidException"}, {"shape": "EventDataStoreNotFoundException"}, {"shape": "InactiveEventDataStoreException"}, {"shape": "InvalidParameterException"}, {"shape": "GenerateResponseException"}, {"shape": "OperationNotPermittedException"}, {"shape": "UnsupportedOperationException"}, {"shape": "NoManagementAccountSLRExistsException"}], "documentation": "<p> Generates a query from a natural language prompt. This operation uses generative artificial intelligence (generative AI) to produce a ready-to-use SQL query from the prompt. </p> <p>The prompt can be a question or a statement about the event data in your event data store. For example, you can enter prompts like \"What are my top errors in the past month?\" and “Give me a list of users that used SNS.”</p> <p>The prompt must be in English. For information about limitations, permissions, and supported Regions, see <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/lake-query-generator.html\">Create CloudTrail Lake queries from natural language prompts</a> in the <i>CloudTrail </i> user guide.</p> <note> <p>Do not include any personally identifying, confidential, or sensitive information in your prompts.</p> <p>This feature uses generative AI large language models (LLMs); we recommend double-checking the LLM response.</p> </note>", "idempotent": true}, "GetChannel": {"name": "GetChannel", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetChannelRequest"}, "output": {"shape": "GetChannelResponse"}, "errors": [{"shape": "ChannelARNInvalidException"}, {"shape": "ChannelNotFoundException"}, {"shape": "OperationNotPermittedException"}, {"shape": "UnsupportedOperationException"}], "documentation": "<p> Returns information about a specific channel. </p>", "idempotent": true}, "GetDashboard": {"name": "GetDashboard", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetDashboardRequest"}, "output": {"shape": "GetDashboardResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "UnsupportedOperationException"}], "documentation": "<p> Returns the specified dashboard. </p>", "idempotent": true}, "GetEventConfiguration": {"name": "GetEventConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetEventConfigurationRequest"}, "output": {"shape": "GetEventConfigurationResponse"}, "errors": [{"shape": "CloudTrailARNInvalidException"}, {"shape": "UnsupportedOperationException"}, {"shape": "OperationNotPermittedException"}, {"shape": "EventDataStoreARNInvalidException"}, {"shape": "EventDataStoreNotFoundException"}, {"shape": "InvalidEventDataStoreStatusException"}, {"shape": "InvalidParameterException"}, {"shape": "InvalidEventDataStoreCategoryException"}, {"shape": "NoManagementAccountSLRExistsException"}, {"shape": "InvalidParameterCombinationException"}], "documentation": "<p>Retrieves the current event configuration settings for the specified event data store, including details about maximum event size and context key selectors configured for the event data store.</p>", "idempotent": true}, "GetEventDataStore": {"name": "GetEventDataStore", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetEventDataStoreRequest"}, "output": {"shape": "GetEventDataStoreResponse"}, "errors": [{"shape": "EventDataStoreARNInvalidException"}, {"shape": "EventDataStoreNotFoundException"}, {"shape": "InvalidParameterException"}, {"shape": "OperationNotPermittedException"}, {"shape": "UnsupportedOperationException"}, {"shape": "NoManagementAccountSLRExistsException"}], "documentation": "<p>Returns information about an event data store specified as either an ARN or the ID portion of the ARN.</p>", "idempotent": true}, "GetEventSelectors": {"name": "GetEventSelectors", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetEventSelectorsRequest"}, "output": {"shape": "GetEventSelectorsResponse"}, "errors": [{"shape": "TrailNotFoundException"}, {"shape": "InvalidTrailNameException"}, {"shape": "CloudTrailARNInvalidException"}, {"shape": "UnsupportedOperationException"}, {"shape": "OperationNotPermittedException"}, {"shape": "NoManagementAccountSLRExistsException"}], "documentation": "<p>Describes the settings for the event selectors that you configured for your trail. The information returned for your event selectors includes the following:</p> <ul> <li> <p>If your event selector includes read-only events, write-only events, or all events. This applies to management events, data events, and network activity events.</p> </li> <li> <p>If your event selector includes management events.</p> </li> <li> <p>If your event selector includes network activity events, the event sources for which you are logging network activity events.</p> </li> <li> <p>If your event selector includes data events, the resources on which you are logging data events.</p> </li> </ul> <p>For more information about logging management, data, and network activity events, see the following topics in the <i>CloudTrail User Guide</i>:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/logging-management-events-with-cloudtrail.html\">Logging management events</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/logging-data-events-with-cloudtrail.html\">Logging data events</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/logging-network-events-with-cloudtrail.html\">Logging network activity events</a> </p> </li> </ul>", "idempotent": true}, "GetImport": {"name": "GetImport", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetImportRequest"}, "output": {"shape": "GetImportResponse"}, "errors": [{"shape": "ImportNotFoundException"}, {"shape": "InvalidParameterException"}, {"shape": "OperationNotPermittedException"}, {"shape": "UnsupportedOperationException"}], "documentation": "<p> Returns information about a specific import. </p>"}, "GetInsightSelectors": {"name": "GetInsightSelectors", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetInsightSelectorsRequest"}, "output": {"shape": "GetInsightSelectorsResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "InvalidParameterCombinationException"}, {"shape": "TrailNotFoundException"}, {"shape": "InvalidTrailNameException"}, {"shape": "CloudTrailARNInvalidException"}, {"shape": "UnsupportedOperationException"}, {"shape": "OperationNotPermittedException"}, {"shape": "InsightNotEnabledException"}, {"shape": "NoManagementAccountSLRExistsException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Describes the settings for the Insights event selectors that you configured for your trail or event data store. <code>GetInsightSelectors</code> shows if CloudTrail Insights event logging is enabled on the trail or event data store, and if it is, which Insights types are enabled. If you run <code>GetInsightSelectors</code> on a trail or event data store that does not have Insights events enabled, the operation throws the exception <code>InsightNotEnabledException</code> </p> <p>Specify either the <code>EventDataStore</code> parameter to get Insights event selectors for an event data store, or the <code>TrailName</code> parameter to the get Insights event selectors for a trail. You cannot specify these parameters together.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/logging-insights-events-with-cloudtrail.html\">Working with CloudTrail Insights</a> in the <i>CloudTrail User Guide</i>.</p>", "idempotent": true}, "GetQueryResults": {"name": "GetQueryResults", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetQueryResultsRequest"}, "output": {"shape": "GetQueryResultsResponse"}, "errors": [{"shape": "EventDataStoreARNInvalidException"}, {"shape": "EventDataStoreNotFoundException"}, {"shape": "InactiveEventDataStoreException"}, {"shape": "InvalidMaxResultsException"}, {"shape": "InvalidNextTokenException"}, {"shape": "InvalidParameterException"}, {"shape": "QueryIdNotFoundException"}, {"shape": "InsufficientEncryptionPolicyException"}, {"shape": "OperationNotPermittedException"}, {"shape": "UnsupportedOperationException"}, {"shape": "NoManagementAccountSLRExistsException"}], "documentation": "<p>Gets event data results of a query. You must specify the <code>QueryID</code> value returned by the <code>StartQuery</code> operation.</p>"}, "GetResourcePolicy": {"name": "GetResourcePolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetResourcePolicyRequest"}, "output": {"shape": "GetResourcePolicyResponse"}, "errors": [{"shape": "ResourceARNNotValidException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourcePolicyNotFoundException"}, {"shape": "ResourceTypeNotSupportedException"}, {"shape": "OperationNotPermittedException"}, {"shape": "UnsupportedOperationException"}], "documentation": "<p> Retrieves the JSON text of the resource-based policy document attached to the CloudTrail event data store, dashboard, or channel. </p>", "idempotent": true}, "GetTrail": {"name": "GetTrail", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetTrailRequest"}, "output": {"shape": "GetTrailResponse"}, "errors": [{"shape": "CloudTrailARNInvalidException"}, {"shape": "TrailNotFoundException"}, {"shape": "InvalidTrailNameException"}, {"shape": "UnsupportedOperationException"}, {"shape": "OperationNotPermittedException"}], "documentation": "<p>Returns settings information for a specified trail.</p>", "idempotent": true}, "GetTrailStatus": {"name": "GetTrailStatus", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetTrailStatusRequest"}, "output": {"shape": "GetTrailStatusResponse"}, "errors": [{"shape": "CloudTrailARNInvalidException"}, {"shape": "TrailNotFoundException"}, {"shape": "InvalidTrailNameException"}, {"shape": "UnsupportedOperationException"}, {"shape": "OperationNotPermittedException"}], "documentation": "<p>Returns a JSON-formatted list of information about the specified trail. Fields include information on delivery errors, Amazon SNS and Amazon S3 errors, and start and stop logging times for each trail. This operation returns trail status from a single Region. To return trail status from all Regions, you must call the operation on each Region.</p>", "idempotent": true}, "ListChannels": {"name": "ListChannels", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListChannelsRequest"}, "output": {"shape": "ListChannelsResponse"}, "errors": [{"shape": "InvalidNextTokenException"}, {"shape": "OperationNotPermittedException"}, {"shape": "UnsupportedOperationException"}], "documentation": "<p> Lists the channels in the current account, and their source names. </p>", "idempotent": true}, "ListDashboards": {"name": "ListDashboards", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListDashboardsRequest"}, "output": {"shape": "ListDashboardsResponse"}, "errors": [{"shape": "UnsupportedOperationException"}], "documentation": "<p> Returns information about all dashboards in the account, in the current Region. </p>", "idempotent": true}, "ListEventDataStores": {"name": "ListEventDataStores", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListEventDataStoresRequest"}, "output": {"shape": "ListEventDataStoresResponse"}, "errors": [{"shape": "InvalidMaxResultsException"}, {"shape": "InvalidNextTokenException"}, {"shape": "OperationNotPermittedException"}, {"shape": "UnsupportedOperationException"}, {"shape": "NoManagementAccountSLRExistsException"}], "documentation": "<p>Returns information about all event data stores in the account, in the current Region.</p>", "idempotent": true}, "ListImportFailures": {"name": "ListImportFailures", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListImportFailuresRequest"}, "output": {"shape": "ListImportFailuresResponse"}, "errors": [{"shape": "InvalidNextTokenException"}, {"shape": "OperationNotPermittedException"}, {"shape": "UnsupportedOperationException"}, {"shape": "InvalidParameterException"}], "documentation": "<p> Returns a list of failures for the specified import. </p>", "idempotent": true}, "ListImports": {"name": "ListImports", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListImportsRequest"}, "output": {"shape": "ListImportsResponse"}, "errors": [{"shape": "EventDataStoreARNInvalidException"}, {"shape": "InvalidNextTokenException"}, {"shape": "InvalidParameterException"}, {"shape": "OperationNotPermittedException"}, {"shape": "UnsupportedOperationException"}], "documentation": "<p> Returns information on all imports, or a select set of imports by <code>ImportStatus</code> or <code>Destination</code>. </p>", "idempotent": true}, "ListInsightsMetricData": {"name": "ListInsightsMetricData", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListInsightsMetricDataRequest"}, "output": {"shape": "ListInsightsMetricDataResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "OperationNotPermittedException"}, {"shape": "UnsupportedOperationException"}], "documentation": "<p>Returns Insights metrics data for trails that have enabled Insights. The request must include the <code>EventSource</code>, <code>EventName</code>, and <code>InsightType</code> parameters.</p> <p>If the <code>InsightType</code> is set to <code>ApiErrorRateInsight</code>, the request must also include the <code>ErrorCode</code> parameter.</p> <p>The following are the available time periods for <code>ListInsightsMetricData</code>. Each cutoff is inclusive.</p> <ul> <li> <p>Data points with a period of 60 seconds (1-minute) are available for 15 days.</p> </li> <li> <p>Data points with a period of 300 seconds (5-minute) are available for 63 days.</p> </li> <li> <p>Data points with a period of 3600 seconds (1 hour) are available for 90 days.</p> </li> </ul> <p>Access to the <code>ListInsightsMetricData</code> API operation is linked to the <code>cloudtrail:LookupEvents</code> action. To use this operation, you must have permissions to perform the <code>cloudtrail:LookupEvents</code> action.</p>", "idempotent": true}, "ListPublicKeys": {"name": "ListPublicKeys", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListPublicKeysRequest"}, "output": {"shape": "ListPublicKeysResponse"}, "errors": [{"shape": "InvalidTimeRangeException"}, {"shape": "UnsupportedOperationException"}, {"shape": "OperationNotPermittedException"}, {"shape": "InvalidTokenException"}], "documentation": "<p>Returns all public keys whose private keys were used to sign the digest files within the specified time range. The public key is needed to validate digest files that were signed with its corresponding private key.</p> <note> <p>CloudTrail uses different private and public key pairs per Region. Each digest file is signed with a private key unique to its Region. When you validate a digest file from a specific Region, you must look in the same Region for its corresponding public key.</p> </note>", "idempotent": true}, "ListQueries": {"name": "ListQueries", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListQueriesRequest"}, "output": {"shape": "ListQueriesResponse"}, "errors": [{"shape": "EventDataStoreARNInvalidException"}, {"shape": "EventDataStoreNotFoundException"}, {"shape": "InactiveEventDataStoreException"}, {"shape": "InvalidDateRangeException"}, {"shape": "InvalidMaxResultsException"}, {"shape": "InvalidNextTokenException"}, {"shape": "InvalidParameterException"}, {"shape": "InvalidQueryStatusException"}, {"shape": "OperationNotPermittedException"}, {"shape": "UnsupportedOperationException"}, {"shape": "NoManagementAccountSLRExistsException"}], "documentation": "<p>Returns a list of queries and query statuses for the past seven days. You must specify an ARN value for <code>EventDataStore</code>. Optionally, to shorten the list of results, you can specify a time range, formatted as timestamps, by adding <code>StartTime</code> and <code>EndTime</code> parameters, and a <code>QueryStatus</code> value. Valid values for <code>QueryStatus</code> include <code>QUEUED</code>, <code>RUNNING</code>, <code>FINISHED</code>, <code>FAILED</code>, <code>TIMED_OUT</code>, or <code>CANCELLED</code>.</p>", "idempotent": true}, "ListTags": {"name": "ListTags", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTagsRequest"}, "output": {"shape": "ListTagsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "CloudTrailARNInvalidException"}, {"shape": "EventDataStoreARNInvalidException"}, {"shape": "ChannelARNInvalidException"}, {"shape": "ResourceTypeNotSupportedException"}, {"shape": "InvalidTrailNameException"}, {"shape": "InactiveEventDataStoreException"}, {"shape": "EventDataStoreNotFoundException"}, {"shape": "UnsupportedOperationException"}, {"shape": "OperationNotPermittedException"}, {"shape": "InvalidTokenException"}, {"shape": "NoManagementAccountSLRExistsException"}], "documentation": "<p>Lists the tags for the specified trails, event data stores, dashboards, or channels in the current Region.</p>", "idempotent": true}, "ListTrails": {"name": "ListTrails", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTrailsRequest"}, "output": {"shape": "ListTrailsResponse"}, "errors": [{"shape": "UnsupportedOperationException"}, {"shape": "OperationNotPermittedException"}], "documentation": "<p>Lists trails that are in the current account.</p>", "idempotent": true}, "LookupEvents": {"name": "LookupEvents", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "LookupEventsRequest"}, "output": {"shape": "LookupEventsResponse"}, "errors": [{"shape": "InvalidLookupAttributesException"}, {"shape": "InvalidTimeRangeException"}, {"shape": "InvalidMaxResultsException"}, {"shape": "InvalidNextTokenException"}, {"shape": "InvalidEventCategoryException"}, {"shape": "UnsupportedOperationException"}, {"shape": "OperationNotPermittedException"}], "documentation": "<p>Looks up <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/cloudtrail-concepts.html#cloudtrail-concepts-management-events\">management events</a> or <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/cloudtrail-concepts.html#cloudtrail-concepts-insights-events\">CloudTrail Insights events</a> that are captured by CloudTrail. You can look up events that occurred in a Region within the last 90 days.</p> <note> <p> <code>LookupEvents</code> returns recent Insights events for trails that enable Insights. To view Insights events for an event data store, you can run queries on your Insights event data store, and you can also view the Lake dashboard for Insights.</p> </note> <p>Lookup supports the following attributes for management events:</p> <ul> <li> <p>Amazon Web Services access key</p> </li> <li> <p>Event ID</p> </li> <li> <p>Event name</p> </li> <li> <p>Event source</p> </li> <li> <p>Read only</p> </li> <li> <p>Resource name</p> </li> <li> <p>Resource type</p> </li> <li> <p>User name</p> </li> </ul> <p>Lookup supports the following attributes for Insights events:</p> <ul> <li> <p>Event ID</p> </li> <li> <p>Event name</p> </li> <li> <p>Event source</p> </li> </ul> <p>All attributes are optional. The default number of results returned is 50, with a maximum of 50 possible. The response includes a token that you can use to get the next page of results.</p> <important> <p>The rate of lookup requests is limited to two per second, per account, per Region. If this limit is exceeded, a throttling error occurs.</p> </important>", "idempotent": true}, "PutEventConfiguration": {"name": "PutEventConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutEventConfigurationRequest"}, "output": {"shape": "PutEventConfigurationResponse"}, "errors": [{"shape": "EventDataStoreARNInvalidException"}, {"shape": "EventDataStoreNotFoundException"}, {"shape": "InvalidEventDataStoreStatusException"}, {"shape": "InvalidEventDataStoreCategoryException"}, {"shape": "InactiveEventDataStoreException"}, {"shape": "UnsupportedOperationException"}, {"shape": "OperationNotPermittedException"}, {"shape": "ThrottlingException"}, {"shape": "InvalidParameterException"}, {"shape": "InvalidParameterCombinationException"}, {"shape": "CloudTrailARNInvalidException"}, {"shape": "ConflictException"}, {"shape": "NotOrganizationMasterAccountException"}, {"shape": "NoManagementAccountSLRExistsException"}, {"shape": "InsufficientDependencyServiceAccessPermissionException"}, {"shape": "InsufficientIAMAccessPermissionException"}], "documentation": "<p>Updates the event configuration settings for the specified event data store. You can update the maximum event size and context key selectors.</p>", "idempotent": true}, "PutEventSelectors": {"name": "PutEventSelectors", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutEventSelectorsRequest"}, "output": {"shape": "PutEventSelectorsResponse"}, "errors": [{"shape": "TrailNotFoundException"}, {"shape": "InvalidTrailNameException"}, {"shape": "CloudTrailARNInvalidException"}, {"shape": "InvalidHomeRegionException"}, {"shape": "InvalidEventSelectorsException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "UnsupportedOperationException"}, {"shape": "OperationNotPermittedException"}, {"shape": "NotOrganizationMasterAccountException"}, {"shape": "NoManagementAccountSLRExistsException"}, {"shape": "InsufficientDependencyServiceAccessPermissionException"}], "documentation": "<p>Configures event selectors (also referred to as <i>basic event selectors</i>) or advanced event selectors for your trail. You can use either <code>AdvancedEventSelectors</code> or <code>EventSelectors</code>, but not both. If you apply <code>AdvancedEventSelectors</code> to a trail, any existing <code>EventSelectors</code> are overwritten.</p> <p>You can use <code>AdvancedEventSelectors</code> to log management events, data events for all resource types, and network activity events.</p> <p>You can use <code>EventSelectors</code> to log management events and data events for the following resource types:</p> <ul> <li> <p> <code>AWS::DynamoDB::Table</code> </p> </li> <li> <p> <code>AWS::Lambda::Function</code> </p> </li> <li> <p> <code>AWS::S3::Object</code> </p> </li> </ul> <p>You can't use <code>EventSelectors</code> to log network activity events.</p> <p>If you want your trail to log Insights events, be sure the event selector or advanced event selector enables logging of the Insights event types you want configured for your trail. For more information about logging Insights events, see <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/logging-insights-events-with-cloudtrail.html\">Working with CloudTrail Insights</a> in the <i>CloudTrail User Guide</i>. By default, trails created without specific event selectors are configured to log all read and write management events, and no data events or network activity events.</p> <p>When an event occurs in your account, CloudTrail evaluates the event selectors or advanced event selectors in all trails. For each trail, if the event matches any event selector, the trail processes and logs the event. If the event doesn't match any event selector, the trail doesn't log the event.</p> <p>Example</p> <ol> <li> <p>You create an event selector for a trail and specify that you want to log write-only events.</p> </li> <li> <p>The EC2 <code>GetConsoleOutput</code> and <code>RunInstances</code> API operations occur in your account.</p> </li> <li> <p>CloudTrail evaluates whether the events match your event selectors.</p> </li> <li> <p>The <code>RunInstances</code> is a write-only event and it matches your event selector. The trail logs the event.</p> </li> <li> <p>The <code>GetConsoleOutput</code> is a read-only event that doesn't match your event selector. The trail doesn't log the event. </p> </li> </ol> <p>The <code>PutEventSelectors</code> operation must be called from the Region in which the trail was created; otherwise, an <code>InvalidHomeRegionException</code> exception is thrown.</p> <p>You can configure up to five event selectors for each trail.</p> <p>You can add advanced event selectors, and conditions for your advanced event selectors, up to a maximum of 500 values for all conditions and selectors on a trail. For more information, see <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/logging-management-events-with-cloudtrail.html\">Logging management events</a>, <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/logging-data-events-with-cloudtrail.html\">Logging data events</a>, <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/logging-network-events-with-cloudtrail.html\">Logging network activity events</a>, and <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/WhatIsCloudTrail-Limits.html\">Quotas in CloudTrail</a> in the <i>CloudTrail User Guide</i>.</p>", "idempotent": true}, "PutInsightSelectors": {"name": "PutInsightSelectors", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutInsightSelectorsRequest"}, "output": {"shape": "PutInsightSelectorsResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "InvalidParameterCombinationException"}, {"shape": "TrailNotFoundException"}, {"shape": "InvalidTrailNameException"}, {"shape": "CloudTrailARNInvalidException"}, {"shape": "InvalidHomeRegionException"}, {"shape": "InvalidInsightSelectorsException"}, {"shape": "InsufficientS3BucketPolicyException"}, {"shape": "InsufficientEncryptionPolicyException"}, {"shape": "S3BucketDoesNotExistException"}, {"shape": "KmsException"}, {"shape": "UnsupportedOperationException"}, {"shape": "OperationNotPermittedException"}, {"shape": "NotOrganizationMasterAccountException"}, {"shape": "NoManagementAccountSLRExistsException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Lets you enable Insights event logging by specifying the Insights selectors that you want to enable on an existing trail or event data store. You also use <code>PutInsightSelectors</code> to turn off Insights event logging, by passing an empty list of Insights types. The valid Insights event types are <code>ApiErrorRateInsight</code> and <code>ApiCallRateInsight</code>.</p> <p>To enable Insights on an event data store, you must specify the ARNs (or ID suffix of the ARNs) for the source event data store (<code>EventDataStore</code>) and the destination event data store (<code>InsightsDestination</code>). The source event data store logs management events and enables Insights. The destination event data store logs Insights events based upon the management event activity of the source event data store. The source and destination event data stores must belong to the same Amazon Web Services account.</p> <p>To log Insights events for a trail, you must specify the name (<code>TrailName</code>) of the CloudTrail trail for which you want to change or add Insights selectors.</p> <p>To log CloudTrail Insights events on API call volume, the trail or event data store must log <code>write</code> management events. To log CloudTrail Insights events on API error rate, the trail or event data store must log <code>read</code> or <code>write</code> management events. You can call <code>GetEventSelectors</code> on a trail to check whether the trail logs management events. You can call <code>GetEventDataStore</code> on an event data store to check whether the event data store logs management events.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/logging-insights-events-with-cloudtrail.html\">Working with CloudTrail Insights</a> in the <i>CloudTrail User Guide</i>.</p>", "idempotent": true}, "PutResourcePolicy": {"name": "PutResourcePolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutResourcePolicyRequest"}, "output": {"shape": "PutResourcePolicyResponse"}, "errors": [{"shape": "ResourceARNNotValidException"}, {"shape": "ResourcePolicyNotValidException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceTypeNotSupportedException"}, {"shape": "ConflictException"}, {"shape": "OperationNotPermittedException"}, {"shape": "UnsupportedOperationException"}], "documentation": "<p> Attaches a resource-based permission policy to a CloudTrail event data store, dashboard, or channel. For more information about resource-based policies, see <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/security_iam_resource-based-policy-examples.html\">CloudTrail resource-based policy examples</a> in the <i>CloudTrail User Guide</i>. </p>", "idempotent": true}, "RegisterOrganizationDelegatedAdmin": {"name": "RegisterOrganizationDelegatedAdmin", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "RegisterOrganizationDelegatedAdminRequest"}, "output": {"shape": "RegisterOrganizationDelegatedAdminResponse"}, "errors": [{"shape": "AccountRegisteredException"}, {"shape": "AccountNotFoundException"}, {"shape": "InsufficientDependencyServiceAccessPermissionException"}, {"shape": "InvalidParameterException"}, {"shape": "CannotDelegateManagementAccountException"}, {"shape": "CloudTrailAccessNotEnabledException"}, {"shape": "ConflictException"}, {"shape": "DelegatedAdminAccountLimitExceededException"}, {"shape": "NotOrganizationManagementAccountException"}, {"shape": "OrganizationNotInAllFeaturesModeException"}, {"shape": "OrganizationsNotInUseException"}, {"shape": "UnsupportedOperationException"}, {"shape": "OperationNotPermittedException"}, {"shape": "InsufficientIAMAccessPermissionException"}], "documentation": "<p>Registers an organization’s member account as the CloudTrail <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/cloudtrail-delegated-administrator.html\">delegated administrator</a>.</p>", "idempotent": true}, "RemoveTags": {"name": "RemoveTags", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "RemoveTagsRequest"}, "output": {"shape": "RemoveTagsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "CloudTrailARNInvalidException"}, {"shape": "EventDataStoreARNInvalidException"}, {"shape": "ChannelARNInvalidException"}, {"shape": "ResourceTypeNotSupportedException"}, {"shape": "InvalidTrailNameException"}, {"shape": "InvalidTagParameterException"}, {"shape": "InactiveEventDataStoreException"}, {"shape": "EventDataStoreNotFoundException"}, {"shape": "ChannelNotFoundException"}, {"shape": "UnsupportedOperationException"}, {"shape": "OperationNotPermittedException"}, {"shape": "NotOrganizationMasterAccountException"}, {"shape": "NoManagementAccountSLRExistsException"}, {"shape": "ConflictException"}], "documentation": "<p>Removes the specified tags from a trail, event data store, dashboard, or channel.</p>", "idempotent": true}, "RestoreEventDataStore": {"name": "RestoreEventDataStore", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "RestoreEventDataStoreRequest"}, "output": {"shape": "RestoreEventDataStoreResponse"}, "errors": [{"shape": "EventDataStoreARNInvalidException"}, {"shape": "EventDataStoreNotFoundException"}, {"shape": "EventDataStoreMaxLimitExceededException"}, {"shape": "InvalidEventDataStoreStatusException"}, {"shape": "InvalidParameterException"}, {"shape": "OperationNotPermittedException"}, {"shape": "UnsupportedOperationException"}, {"shape": "CloudTrailAccessNotEnabledException"}, {"shape": "InsufficientDependencyServiceAccessPermissionException"}, {"shape": "OrganizationsNotInUseException"}, {"shape": "NotOrganizationMasterAccountException"}, {"shape": "NoManagementAccountSLRExistsException"}, {"shape": "OrganizationNotInAllFeaturesModeException"}], "documentation": "<p>Restores a deleted event data store specified by <code>EventDataStore</code>, which accepts an event data store ARN. You can only restore a deleted event data store within the seven-day wait period after deletion. Restoring an event data store can take several minutes, depending on the size of the event data store.</p>"}, "SearchSampleQueries": {"name": "SearchSampleQueries", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "SearchSampleQueriesRequest"}, "output": {"shape": "SearchSampleQueriesResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "UnsupportedOperationException"}, {"shape": "OperationNotPermittedException"}], "documentation": "<p> Searches sample queries and returns a list of sample queries that are sorted by relevance. To search for sample queries, provide a natural language <code>SearchPhrase</code> in English. </p>", "idempotent": true}, "StartDashboardRefresh": {"name": "StartDashboardRefresh", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StartDashboardRefreshRequest"}, "output": {"shape": "StartDashboardRefreshResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "EventDataStoreNotFoundException"}, {"shape": "InactiveEventDataStoreException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "UnsupportedOperationException"}], "documentation": "<p> Starts a refresh of the specified dashboard. </p> <p> Each time a dashboard is refreshed, CloudTrail runs queries to populate the dashboard's widgets. CloudTrail must be granted permissions to run the <code>StartQuery</code> operation on your behalf. To provide permissions, run the <code>PutResourcePolicy</code> operation to attach a resource-based policy to each event data store. For more information, see <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/security_iam_resource-based-policy-examples.html#security_iam_resource-based-policy-examples-eds-dashboard\">Example: Allow CloudTrail to run queries to populate a dashboard</a> in the <i>CloudTrail User Guide</i>. </p>", "idempotent": true}, "StartEventDataStoreIngestion": {"name": "StartEventDataStoreIngestion", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StartEventDataStoreIngestionRequest"}, "output": {"shape": "StartEventDataStoreIngestionResponse"}, "errors": [{"shape": "EventDataStoreARNInvalidException"}, {"shape": "EventDataStoreNotFoundException"}, {"shape": "InvalidEventDataStoreStatusException"}, {"shape": "InvalidParameterException"}, {"shape": "InvalidEventDataStoreCategoryException"}, {"shape": "OperationNotPermittedException"}, {"shape": "UnsupportedOperationException"}, {"shape": "NotOrganizationMasterAccountException"}, {"shape": "NoManagementAccountSLRExistsException"}, {"shape": "InsufficientDependencyServiceAccessPermissionException"}, {"shape": "ConflictException"}], "documentation": "<p>Starts the ingestion of live events on an event data store specified as either an ARN or the ID portion of the ARN. To start ingestion, the event data store <code>Status</code> must be <code>STOPPED_INGESTION</code> and the <code>eventCategory</code> must be <code>Management</code>, <code>Data</code>, <code>NetworkActivity</code>, or <code>ConfigurationItem</code>.</p>"}, "StartImport": {"name": "StartImport", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StartImportRequest"}, "output": {"shape": "StartImportResponse"}, "errors": [{"shape": "AccountHasOngoingImportException"}, {"shape": "EventDataStoreARNInvalidException"}, {"shape": "EventDataStoreNotFoundException"}, {"shape": "InvalidEventDataStoreStatusException"}, {"shape": "InvalidEventDataStoreCategoryException"}, {"shape": "InactiveEventDataStoreException"}, {"shape": "InvalidImportSourceException"}, {"shape": "ImportNotFoundException"}, {"shape": "InvalidParameterException"}, {"shape": "InsufficientEncryptionPolicyException"}, {"shape": "OperationNotPermittedException"}, {"shape": "UnsupportedOperationException"}, {"shape": "OperationNotPermittedException"}, {"shape": "UnsupportedOperationException"}], "documentation": "<p> Starts an import of logged trail events from a source S3 bucket to a destination event data store. By default, CloudTrail only imports events contained in the S3 bucket's <code>CloudTrail</code> prefix and the prefixes inside the <code>CloudTrail</code> prefix, and does not check prefixes for other Amazon Web Services services. If you want to import CloudTrail events contained in another prefix, you must include the prefix in the <code>S3LocationUri</code>. For more considerations about importing trail events, see <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/cloudtrail-copy-trail-to-lake.html#cloudtrail-trail-copy-considerations\">Considerations for copying trail events</a> in the <i>CloudTrail User Guide</i>. </p> <p> When you start a new import, the <code>Destinations</code> and <code>ImportSource</code> parameters are required. Before starting a new import, disable any access control lists (ACLs) attached to the source S3 bucket. For more information about disabling ACLs, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/about-object-ownership.html\">Controlling ownership of objects and disabling ACLs for your bucket</a>. </p> <p> When you retry an import, the <code>ImportID</code> parameter is required. </p> <note> <p> If the destination event data store is for an organization, you must use the management account to import trail events. You cannot use the delegated administrator account for the organization. </p> </note>"}, "StartLogging": {"name": "StartLogging", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StartLoggingRequest"}, "output": {"shape": "StartLoggingResponse"}, "errors": [{"shape": "CloudTrailARNInvalidException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "TrailNotFoundException"}, {"shape": "InvalidTrailNameException"}, {"shape": "InvalidHomeRegionException"}, {"shape": "UnsupportedOperationException"}, {"shape": "OperationNotPermittedException"}, {"shape": "NotOrganizationMasterAccountException"}, {"shape": "NoManagementAccountSLRExistsException"}, {"shape": "InsufficientDependencyServiceAccessPermissionException"}], "documentation": "<p>Starts the recording of Amazon Web Services API calls and log file delivery for a trail. For a trail that is enabled in all Regions, this operation must be called from the Region in which the trail was created. This operation cannot be called on the shadow trails (replicated trails in other Regions) of a trail that is enabled in all Regions.</p>", "idempotent": true}, "StartQuery": {"name": "Start<PERSON>uery", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StartQueryRequest"}, "output": {"shape": "StartQueryResponse"}, "errors": [{"shape": "EventDataStoreARNInvalidException"}, {"shape": "EventDataStoreNotFoundException"}, {"shape": "InactiveEventDataStoreException"}, {"shape": "InvalidParameterException"}, {"shape": "InvalidQueryStatementException"}, {"shape": "MaxConcurrentQueriesException"}, {"shape": "InsufficientEncryptionPolicyException"}, {"shape": "InvalidS3PrefixException"}, {"shape": "InvalidS3BucketNameException"}, {"shape": "InsufficientS3BucketPolicyException"}, {"shape": "S3BucketDoesNotExistException"}, {"shape": "OperationNotPermittedException"}, {"shape": "UnsupportedOperationException"}, {"shape": "NoManagementAccountSLRExistsException"}], "documentation": "<p>Starts a CloudTrail Lake query. Use the <code>QueryStatement</code> parameter to provide your SQL query, enclosed in single quotation marks. Use the optional <code>DeliveryS3Uri</code> parameter to deliver the query results to an S3 bucket.</p> <p> <code>StartQuery</code> requires you specify either the <code>QueryStatement</code> parameter, or a <code>QueryAlias</code> and any <code>QueryParameters</code>. In the current release, the <code>QueryAlias</code> and <code>QueryParameters</code> parameters are used only for the queries that populate the CloudTrail Lake dashboards.</p>", "idempotent": true}, "StopEventDataStoreIngestion": {"name": "StopEventDataStoreIngestion", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StopEventDataStoreIngestionRequest"}, "output": {"shape": "StopEventDataStoreIngestionResponse"}, "errors": [{"shape": "EventDataStoreARNInvalidException"}, {"shape": "EventDataStoreNotFoundException"}, {"shape": "InvalidEventDataStoreStatusException"}, {"shape": "InvalidParameterException"}, {"shape": "InvalidEventDataStoreCategoryException"}, {"shape": "OperationNotPermittedException"}, {"shape": "UnsupportedOperationException"}, {"shape": "NotOrganizationMasterAccountException"}, {"shape": "NoManagementAccountSLRExistsException"}, {"shape": "InsufficientDependencyServiceAccessPermissionException"}, {"shape": "ConflictException"}], "documentation": "<p>Stops the ingestion of live events on an event data store specified as either an ARN or the ID portion of the ARN. To stop ingestion, the event data store <code>Status</code> must be <code>ENABLED</code> and the <code>eventCategory</code> must be <code>Management</code>, <code>Data</code>, <code>NetworkActivity</code>, or <code>ConfigurationItem</code>.</p>"}, "StopImport": {"name": "StopImport", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StopImportRequest"}, "output": {"shape": "StopImportResponse"}, "errors": [{"shape": "ImportNotFoundException"}, {"shape": "InvalidParameterException"}, {"shape": "OperationNotPermittedException"}, {"shape": "UnsupportedOperationException"}], "documentation": "<p> Stops a specified import. </p>"}, "StopLogging": {"name": "StopLogging", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StopLoggingRequest"}, "output": {"shape": "StopLoggingResponse"}, "errors": [{"shape": "TrailNotFoundException"}, {"shape": "InvalidTrailNameException"}, {"shape": "CloudTrailARNInvalidException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InvalidHomeRegionException"}, {"shape": "UnsupportedOperationException"}, {"shape": "OperationNotPermittedException"}, {"shape": "NotOrganizationMasterAccountException"}, {"shape": "NoManagementAccountSLRExistsException"}, {"shape": "InsufficientDependencyServiceAccessPermissionException"}], "documentation": "<p>Suspends the recording of Amazon Web Services API calls and log file delivery for the specified trail. Under most circumstances, there is no need to use this action. You can update a trail without stopping it first. This action is the only way to stop recording. For a trail enabled in all Regions, this operation must be called from the Region in which the trail was created, or an <code>InvalidHomeRegionException</code> will occur. This operation cannot be called on the shadow trails (replicated trails in other Regions) of a trail enabled in all Regions.</p>", "idempotent": true}, "UpdateChannel": {"name": "UpdateChannel", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateChannelRequest"}, "output": {"shape": "UpdateChannelResponse"}, "errors": [{"shape": "ChannelARNInvalidException"}, {"shape": "ChannelNotFoundException"}, {"shape": "ChannelAlreadyExistsException"}, {"shape": "EventDataStoreARNInvalidException"}, {"shape": "EventDataStoreNotFoundException"}, {"shape": "InvalidEventDataStoreCategoryException"}, {"shape": "InactiveEventDataStoreException"}, {"shape": "InvalidParameterException"}, {"shape": "OperationNotPermittedException"}, {"shape": "UnsupportedOperationException"}], "documentation": "<p>Updates a channel specified by a required channel ARN or UUID.</p>", "idempotent": true}, "UpdateDashboard": {"name": "UpdateDashboard", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateDashboardRequest"}, "output": {"shape": "UpdateDashboardResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "EventDataStoreNotFoundException"}, {"shape": "InactiveEventDataStoreException"}, {"shape": "InsufficientEncryptionPolicyException"}, {"shape": "InvalidQueryStatementException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "UnsupportedOperationException"}], "documentation": "<p> Updates the specified dashboard. </p> <p> To set a refresh schedule, CloudTrail must be granted permissions to run the <code>StartDashboardRefresh</code> operation to refresh the dashboard on your behalf. To provide permissions, run the <code>PutResourcePolicy</code> operation to attach a resource-based policy to the dashboard. For more information, see <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/security_iam_resource-based-policy-examples.html#security_iam_resource-based-policy-examples-dashboards\"> Resource-based policy example for a dashboard</a> in the <i>CloudTrail User Guide</i>. </p> <p> CloudTrail runs queries to populate the dashboard's widgets during a manual or scheduled refresh. CloudTrail must be granted permissions to run the <code>StartQuery</code> operation on your behalf. To provide permissions, run the <code>PutResourcePolicy</code> operation to attach a resource-based policy to each event data store. For more information, see <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/security_iam_resource-based-policy-examples.html#security_iam_resource-based-policy-examples-eds-dashboard\">Example: Allow CloudTrail to run queries to populate a dashboard</a> in the <i>CloudTrail User Guide</i>. </p>", "idempotent": true}, "UpdateEventDataStore": {"name": "UpdateEventDataStore", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateEventDataStoreRequest"}, "output": {"shape": "UpdateEventDataStoreResponse"}, "errors": [{"shape": "EventDataStoreAlreadyExistsException"}, {"shape": "EventDataStoreARNInvalidException"}, {"shape": "EventDataStoreNotFoundException"}, {"shape": "InvalidEventSelectorsException"}, {"shape": "InvalidInsightSelectorsException"}, {"shape": "EventDataStoreHasOngoingImportException"}, {"shape": "InactiveEventDataStoreException"}, {"shape": "InvalidParameterException"}, {"shape": "OperationNotPermittedException"}, {"shape": "UnsupportedOperationException"}, {"shape": "InsufficientEncryptionPolicyException"}, {"shape": "InvalidKmsKeyIdException"}, {"shape": "KmsKeyNotFoundException"}, {"shape": "KmsException"}, {"shape": "CloudTrailAccessNotEnabledException"}, {"shape": "InsufficientDependencyServiceAccessPermissionException"}, {"shape": "OrganizationsNotInUseException"}, {"shape": "NotOrganizationMasterAccountException"}, {"shape": "NoManagementAccountSLRExistsException"}, {"shape": "OrganizationNotInAllFeaturesModeException"}], "documentation": "<p>Updates an event data store. The required <code>EventDataStore</code> value is an ARN or the ID portion of the ARN. Other parameters are optional, but at least one optional parameter must be specified, or CloudTrail throws an error. <code>RetentionPeriod</code> is in days, and valid values are integers between 7 and 3653 if the <code>BillingMode</code> is set to <code>EXTENDABLE_RETENTION_PRICING</code>, or between 7 and 2557 if <code>BillingMode</code> is set to <code>FIXED_RETENTION_PRICING</code>. By default, <code>TerminationProtection</code> is enabled.</p> <p>For event data stores for CloudTrail events, <code>AdvancedEventSelectors</code> includes or excludes management, data, or network activity events in your event data store. For more information about <code>AdvancedEventSelectors</code>, see <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/APIReference/API_AdvancedEventSelector.html\">AdvancedEventSelectors</a>.</p> <p> For event data stores for CloudTrail Insights events, Config configuration items, Audit Manager evidence, or non-Amazon Web Services events, <code>AdvancedEventSelectors</code> includes events of that type in your event data store.</p>", "idempotent": true}, "UpdateTrail": {"name": "UpdateTrail", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateTrailRequest"}, "output": {"shape": "UpdateTrailResponse"}, "errors": [{"shape": "S3BucketDoesNotExistException"}, {"shape": "InsufficientS3BucketPolicyException"}, {"shape": "InsufficientSnsTopicPolicyException"}, {"shape": "InsufficientEncryptionPolicyException"}, {"shape": "TrailNotFoundException"}, {"shape": "InvalidS3BucketNameException"}, {"shape": "InvalidS3PrefixException"}, {"shape": "InvalidSnsTopicNameException"}, {"shape": "InvalidKmsKeyIdException"}, {"shape": "InvalidTrailNameException"}, {"shape": "TrailNotProvidedException"}, {"shape": "InvalidEventSelectorsException"}, {"shape": "CloudTrailARNInvalidException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InvalidParameterCombinationException"}, {"shape": "InvalidHomeRegionException"}, {"shape": "KmsKeyNotFoundException"}, {"shape": "KmsKeyDisabledException"}, {"shape": "KmsException"}, {"shape": "InvalidCloudWatchLogsLogGroupArnException"}, {"shape": "InvalidCloudWatchLogsRoleArnException"}, {"shape": "CloudWatchLogsDeliveryUnavailableException"}, {"shape": "UnsupportedOperationException"}, {"shape": "OperationNotPermittedException"}, {"shape": "CloudTrailAccessNotEnabledException"}, {"shape": "InsufficientDependencyServiceAccessPermissionException"}, {"shape": "OrganizationsNotInUseException"}, {"shape": "NotOrganizationMasterAccountException"}, {"shape": "OrganizationNotInAllFeaturesModeException"}, {"shape": "NoManagementAccountSLRExistsException"}, {"shape": "CloudTrailInvalidClientTokenIdException"}, {"shape": "InvalidParameterException"}], "documentation": "<p>Updates trail settings that control what events you are logging, and how to handle log files. Changes to a trail do not require stopping the CloudTrail service. Use this action to designate an existing bucket for log delivery. If the existing bucket has previously been a target for CloudTrail log files, an IAM policy exists for the bucket. <code>UpdateTrail</code> must be called from the Region in which the trail was created; otherwise, an <code>InvalidHomeRegionException</code> is thrown.</p>", "idempotent": true}}, "shapes": {"AccessDeniedException": {"type": "structure", "members": {}, "documentation": "<p> You do not have sufficient access to perform this action. </p>", "exception": true}, "AccountHasOngoingImportException": {"type": "structure", "members": {}, "documentation": "<p> This exception is thrown when you start a new import and a previous import is still in progress. </p>", "exception": true}, "AccountId": {"type": "string", "max": 16, "min": 12, "pattern": "\\d+"}, "AccountNotFoundException": {"type": "structure", "members": {}, "documentation": "<p>This exception is thrown when the specified account is not found or not part of an organization.</p>", "exception": true}, "AccountNotRegisteredException": {"type": "structure", "members": {}, "documentation": "<p>This exception is thrown when the specified account is not registered as the CloudTrail delegated administrator.</p>", "exception": true}, "AccountRegisteredException": {"type": "structure", "members": {}, "documentation": "<p>This exception is thrown when the account is already registered as the CloudTrail delegated administrator.</p>", "exception": true}, "AddTagsRequest": {"type": "structure", "required": ["ResourceId", "TagsList"], "members": {"ResourceId": {"shape": "String", "documentation": "<p>Specifies the ARN of the trail, event data store, dashboard, or channel to which one or more tags will be added.</p> <p>The format of a trail ARN is: <code>arn:aws:cloudtrail:us-east-2:************:trail/MyTrail</code> </p> <p>The format of an event data store ARN is: <code>arn:aws:cloudtrail:us-east-2:************:eventdatastore/EXAMPLE-f852-4e8f-8bd1-bcf6cEXAMPLE</code> </p> <p>The format of a dashboard ARN is: <code>arn:aws:cloudtrail:us-east-1:************:dashboard/exampleDash</code> </p> <p>The format of a channel ARN is: <code>arn:aws:cloudtrail:us-east-2:************:channel/***********</code> </p>"}, "TagsList": {"shape": "TagsList", "documentation": "<p>Contains a list of tags, up to a limit of 50</p>"}}, "documentation": "<p>Specifies the tags to add to a trail, event data store, dashboard, or channel.</p>"}, "AddTagsResponse": {"type": "structure", "members": {}, "documentation": "<p>Returns the objects or data if successful. Otherwise, returns an error.</p>"}, "AdvancedEventSelector": {"type": "structure", "required": ["FieldSelectors"], "members": {"Name": {"shape": "SelectorName", "documentation": "<p>An optional, descriptive name for an advanced event selector, such as \"Log data events for only two S3 buckets\".</p>"}, "FieldSelectors": {"shape": "AdvancedFieldSelectors", "documentation": "<p>Contains all selector statements in an advanced event selector.</p>"}}, "documentation": "<p>Advanced event selectors let you create fine-grained selectors for CloudTrail management, data, and network activity events. They help you control costs by logging only those events that are important to you. For more information about configuring advanced event selectors, see the <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/logging-data-events-with-cloudtrail.html\">Logging data events</a>, <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/logging-network-events-with-cloudtrail.html\">Logging network activity events</a>, and <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/logging-management-events-with-cloudtrail.html\">Logging management events</a> topics in the <i>CloudTrail User Guide</i>.</p> <p>You cannot apply both event selectors and advanced event selectors to a trail.</p> <p>For information about configurable advanced event selector fields, see <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/APIReference/API_AdvancedEventSelector.html\">AdvancedEventSelector</a> in the <i>CloudTrail API Reference</i>.</p>"}, "AdvancedEventSelectors": {"type": "list", "member": {"shape": "AdvancedEventSelector"}}, "AdvancedFieldSelector": {"type": "structure", "required": ["Field"], "members": {"Field": {"shape": "SelectorField", "documentation": "<p> A field in a CloudTrail event record on which to filter events to be logged. For event data stores for CloudTrail Insights events, Config configuration items, Audit Manager evidence, or events outside of Amazon Web Services, the field is used only for selecting events as filtering is not supported.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/APIReference/API_AdvancedFieldSelector.html\">AdvancedFieldSelector</a> in the <i>CloudTrail API Reference</i>.</p> <note> <p>Selectors don't support the use of wildcards like <code>*</code> . To match multiple values with a single condition, you may use <code>StartsWith</code>, <code>EndsWith</code>, <code>NotStartsWith</code>, or <code>NotEndsWith</code> to explicitly match the beginning or end of the event field.</p> </note>"}, "Equals": {"shape": "Operator", "documentation": "<p> An operator that includes events that match the exact value of the event record field specified as the value of <code>Field</code>. This is the only valid operator that you can use with the <code>readOnly</code>, <code>eventCategory</code>, and <code>resources.type</code> fields.</p>"}, "StartsWith": {"shape": "Operator", "documentation": "<p>An operator that includes events that match the first few characters of the event record field specified as the value of <code>Field</code>.</p>"}, "EndsWith": {"shape": "Operator", "documentation": "<p>An operator that includes events that match the last few characters of the event record field specified as the value of <code>Field</code>.</p>"}, "NotEquals": {"shape": "Operator", "documentation": "<p> An operator that excludes events that match the exact value of the event record field specified as the value of <code>Field</code>. </p>"}, "NotStartsWith": {"shape": "Operator", "documentation": "<p> An operator that excludes events that match the first few characters of the event record field specified as the value of <code>Field</code>. </p>"}, "NotEndsWith": {"shape": "Operator", "documentation": "<p> An operator that excludes events that match the last few characters of the event record field specified as the value of <code>Field</code>. </p>"}}, "documentation": "<p>A single selector statement in an advanced event selector.</p>"}, "AdvancedFieldSelectors": {"type": "list", "member": {"shape": "AdvancedFieldSelector"}, "min": 1}, "BillingMode": {"type": "string", "enum": ["EXTENDABLE_RETENTION_PRICING", "FIXED_RETENTION_PRICING"]}, "Boolean": {"type": "boolean"}, "ByteBuffer": {"type": "blob"}, "CancelQueryRequest": {"type": "structure", "required": ["QueryId"], "members": {"EventDataStore": {"shape": "EventDataStoreArn", "documentation": "<p>The ARN (or the ID suffix of the ARN) of an event data store on which the specified query is running.</p>", "deprecated": true, "deprecatedMessage": "EventDataStore is no longer required by CancelQueryRequest"}, "QueryId": {"shape": "UUID", "documentation": "<p>The ID of the query that you want to cancel. The <code>QueryId</code> comes from the response of a <code>StartQuery</code> operation.</p>"}, "EventDataStoreOwnerAccountId": {"shape": "AccountId", "documentation": "<p> The account ID of the event data store owner. </p>"}}}, "CancelQueryResponse": {"type": "structure", "required": ["QueryId", "QueryStatus"], "members": {"QueryId": {"shape": "UUID", "documentation": "<p>The ID of the canceled query.</p>"}, "QueryStatus": {"shape": "QueryStatus", "documentation": "<p>Shows the status of a query after a <code>CancelQuery</code> request. Typically, the values shown are either <code>RUNNING</code> or <code>CANCELLED</code>.</p>"}, "EventDataStoreOwnerAccountId": {"shape": "AccountId", "documentation": "<p> The account ID of the event data store owner. </p>"}}}, "CannotDelegateManagementAccountException": {"type": "structure", "members": {}, "documentation": "<p>This exception is thrown when the management account of an organization is registered as the CloudTrail delegated administrator.</p>", "exception": true}, "Channel": {"type": "structure", "members": {"ChannelArn": {"shape": "ChannelArn", "documentation": "<p>The Amazon Resource Name (ARN) of a channel.</p>"}, "Name": {"shape": "ChannelName", "documentation": "<p> The name of the CloudTrail channel. For service-linked channels, the name is <code>aws-service-channel/service-name/custom-suffix</code> where <code>service-name</code> represents the name of the Amazon Web Services service that created the channel and <code>custom-suffix</code> represents the suffix created by the Amazon Web Services service. </p>"}}, "documentation": "<p>Contains information about a returned CloudTrail channel.</p>"}, "ChannelARNInvalidException": {"type": "structure", "members": {}, "documentation": "<p>This exception is thrown when the specified value of <code>ChannelARN</code> is not valid.</p>", "exception": true}, "ChannelAlreadyExistsException": {"type": "structure", "members": {}, "documentation": "<p> This exception is thrown when the provided channel already exists. </p>", "exception": true}, "ChannelArn": {"type": "string", "max": 256, "min": 3, "pattern": "^[a-zA-Z0-9._/\\-:]+$"}, "ChannelExistsForEDSException": {"type": "structure", "members": {}, "documentation": "<p>This exception is thrown when the specified event data store cannot yet be deleted because it is in use by a channel.</p>", "exception": true}, "ChannelMaxLimitExceededException": {"type": "structure", "members": {}, "documentation": "<p> This exception is thrown when the maximum number of channels limit is exceeded. </p>", "exception": true}, "ChannelName": {"type": "string", "max": 128, "min": 3, "pattern": "^[a-zA-Z0-9._\\-]+$"}, "ChannelNotFoundException": {"type": "structure", "members": {}, "documentation": "<p>This exception is thrown when CloudTrail cannot find the specified channel.</p>", "exception": true}, "Channels": {"type": "list", "member": {"shape": "Channel"}}, "CloudTrailARNInvalidException": {"type": "structure", "members": {}, "documentation": "<p>This exception is thrown when an operation is called with an ARN that is not valid.</p> <p>The following is the format of a trail ARN: <code>arn:aws:cloudtrail:us-east-2:************:trail/MyTrail</code> </p> <p>The following is the format of an event data store ARN: <code>arn:aws:cloudtrail:us-east-2:************:eventdatastore/EXAMPLE-f852-4e8f-8bd1-bcf6cEXAMPLE</code> </p> <p>The following is the format of a dashboard ARN: <code>arn:aws:cloudtrail:us-east-1:************:dashboard/exampleDash</code> </p> <p>The following is the format of a channel ARN: <code>arn:aws:cloudtrail:us-east-2:************:channel/***********</code> </p>", "exception": true}, "CloudTrailAccessNotEnabledException": {"type": "structure", "members": {}, "documentation": "<p>This exception is thrown when trusted access has not been enabled between CloudTrail and Organizations. For more information, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_integrate_services.html#orgs_how-to-enable-disable-trusted-access\">How to enable or disable trusted access</a> in the <i>Organizations User Guide</i> and <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/creating-an-organizational-trail-prepare.html\">Prepare For Creating a Trail For Your Organization</a> in the <i>CloudTrail User Guide</i>.</p>", "exception": true}, "CloudTrailInvalidClientTokenIdException": {"type": "structure", "members": {}, "documentation": "<p>This exception is thrown when a call results in the <code>InvalidClientTokenId</code> error code. This can occur when you are creating or updating a trail to send notifications to an Amazon SNS topic that is in a suspended Amazon Web Services account.</p>", "exception": true}, "CloudWatchLogsDeliveryUnavailableException": {"type": "structure", "members": {}, "documentation": "<p><PERSON><PERSON> set a CloudWatch Logs delivery for this Region.</p>", "exception": true}, "ConcurrentModificationException": {"type": "structure", "members": {}, "documentation": "<p> You are trying to update a resource when another request is in progress. Allow sufficient wait time for the previous request to complete, then retry your request. </p>", "exception": true}, "ConflictException": {"type": "structure", "members": {}, "documentation": "<p>This exception is thrown when the specified resource is not ready for an operation. This can occur when you try to run an operation on a resource before CloudTrail has time to fully load the resource, or because another operation is modifying the resource. If this exception occurs, wait a few minutes, and then try the operation again.</p>", "exception": true}, "ContextKeySelector": {"type": "structure", "required": ["Type", "Equals"], "members": {"Type": {"shape": "Type", "documentation": "<p>Specifies the type of the event record field in ContextKeySelector. Valid values include RequestContext, TagContext.</p>"}, "Equals": {"shape": "OperatorTargetList", "documentation": "<p>A list of keys defined by Type to be included in CloudTrail enriched events. </p>"}}, "documentation": "<p>An object that contains information types to be included in CloudTrail enriched events.</p>"}, "ContextKeySelectors": {"type": "list", "member": {"shape": "ContextKeySelector"}, "max": 2}, "CreateChannelRequest": {"type": "structure", "required": ["Name", "Source", "Destinations"], "members": {"Name": {"shape": "ChannelName", "documentation": "<p>The name of the channel.</p>"}, "Source": {"shape": "Source", "documentation": "<p>The name of the partner or external event source. You cannot change this name after you create the channel. A maximum of one channel is allowed per source.</p> <p> A source can be either <code>Custom</code> for all valid non-Amazon Web Services events, or the name of a partner event source. For information about the source names for available partners, see <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/query-event-data-store-integration.html#cloudtrail-lake-partner-information\">Additional information about integration partners</a> in the CloudTrail User Guide. </p>"}, "Destinations": {"shape": "Destinations", "documentation": "<p>One or more event data stores to which events arriving through a channel will be logged.</p>"}, "Tags": {"shape": "TagsList"}}}, "CreateChannelResponse": {"type": "structure", "members": {"ChannelArn": {"shape": "ChannelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the new channel.</p>"}, "Name": {"shape": "ChannelName", "documentation": "<p>The name of the new channel.</p>"}, "Source": {"shape": "Source", "documentation": "<p>The partner or external event source name.</p>"}, "Destinations": {"shape": "Destinations", "documentation": "<p>The event data stores that log the events arriving through the channel.</p>"}, "Tags": {"shape": "TagsList"}}}, "CreateDashboardRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "DashboardName", "documentation": "<p> The name of the dashboard. The name must be unique to your account. </p> <p>To create the Highlights dashboard, the name must be <code>AWSCloudTrail-Highlights</code>.</p>"}, "RefreshSchedule": {"shape": "RefreshSchedule", "documentation": "<p> The refresh schedule configuration for the dashboard. </p> <p>To create the Highlights dashboard, you must set a refresh schedule and set the <code>Status</code> to <code>ENABLED</code>. The <code>Unit</code> for the refresh schedule must be <code>HOURS</code> and the <code>Value</code> must be <code>6</code>.</p>"}, "TagsList": {"shape": "TagsList"}, "TerminationProtectionEnabled": {"shape": "TerminationProtectionEnabled", "documentation": "<p> Specifies whether termination protection is enabled for the dashboard. If termination protection is enabled, you cannot delete the dashboard until termination protection is disabled. </p>"}, "Widgets": {"shape": "RequestWidgetList", "documentation": "<p> An array of widgets for a custom dashboard. A custom dashboard can have a maximum of ten widgets. </p> <p>You do not need to specify widgets for the Highlights dashboard.</p>"}}}, "CreateDashboardResponse": {"type": "structure", "members": {"DashboardArn": {"shape": "DashboardArn", "documentation": "<p> The ARN for the dashboard. </p>"}, "Name": {"shape": "DashboardName", "documentation": "<p> The name of the dashboard. </p>"}, "Type": {"shape": "DashboardType", "documentation": "<p> The dashboard type. </p>"}, "Widgets": {"shape": "WidgetList", "documentation": "<p> An array of widgets for the dashboard. </p>"}, "TagsList": {"shape": "TagsList"}, "RefreshSchedule": {"shape": "RefreshSchedule", "documentation": "<p> The refresh schedule for the dashboard, if configured. </p>"}, "TerminationProtectionEnabled": {"shape": "TerminationProtectionEnabled", "documentation": "<p> Indicates whether termination protection is enabled for the dashboard. </p>"}}}, "CreateEventDataStoreRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "EventDataStoreName", "documentation": "<p>The name of the event data store.</p>"}, "AdvancedEventSelectors": {"shape": "AdvancedEventSelectors", "documentation": "<p>The advanced event selectors to use to select the events for the data store. You can configure up to five advanced event selectors for each event data store.</p> <p> For more information about how to use advanced event selectors to log CloudTrail events, see <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/logging-data-events-with-cloudtrail.html#creating-data-event-selectors-advanced\">Log events by using advanced event selectors</a> in the CloudTrail User Guide.</p> <p>For more information about how to use advanced event selectors to include Config configuration items in your event data store, see <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/lake-eds-cli.html#lake-cli-create-eds-config\">Create an event data store for Config configuration items</a> in the CloudTrail User Guide.</p> <p>For more information about how to use advanced event selectors to include events outside of Amazon Web Services events in your event data store, see <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/lake-integrations-cli.html#lake-cli-create-integration\">Create an integration to log events from outside Amazon Web Services</a> in the CloudTrail User Guide.</p>"}, "MultiRegionEnabled": {"shape": "Boolean", "documentation": "<p>Specifies whether the event data store includes events from all Regions, or only from the Region in which the event data store is created.</p>"}, "OrganizationEnabled": {"shape": "Boolean", "documentation": "<p>Specifies whether an event data store collects events logged for an organization in Organizations.</p>"}, "RetentionPeriod": {"shape": "RetentionPeriod", "documentation": "<p>The retention period of the event data store, in days. If <code>BillingMode</code> is set to <code>EXTENDABLE_RETENTION_PRICING</code>, you can set a retention period of up to 3653 days, the equivalent of 10 years. If <code>BillingMode</code> is set to <code>FIXED_RETENTION_PRICING</code>, you can set a retention period of up to 2557 days, the equivalent of seven years.</p> <p>CloudTrail Lake determines whether to retain an event by checking if the <code>eventTime</code> of the event is within the specified retention period. For example, if you set a retention period of 90 days, CloudTrail will remove events when the <code>eventTime</code> is older than 90 days.</p> <note> <p>If you plan to copy trail events to this event data store, we recommend that you consider both the age of the events that you want to copy as well as how long you want to keep the copied events in your event data store. For example, if you copy trail events that are 5 years old and specify a retention period of 7 years, the event data store will retain those events for two years.</p> </note>"}, "TerminationProtectionEnabled": {"shape": "TerminationProtectionEnabled", "documentation": "<p>Specifies whether termination protection is enabled for the event data store. If termination protection is enabled, you cannot delete the event data store until termination protection is disabled.</p>"}, "TagsList": {"shape": "TagsList"}, "KmsKeyId": {"shape": "EventDataStoreKmsKeyId", "documentation": "<p>Specifies the KMS key ID to use to encrypt the events delivered by CloudTrail. The value can be an alias name prefixed by <code>alias/</code>, a fully specified ARN to an alias, a fully specified ARN to a key, or a globally unique identifier.</p> <important> <p>Disabling or deleting the KMS key, or removing CloudTrail permissions on the key, prevents CloudTrail from logging events to the event data store, and prevents users from querying the data in the event data store that was encrypted with the key. After you associate an event data store with a KMS key, the KMS key cannot be removed or changed. Before you disable or delete a KMS key that you are using with an event data store, delete or back up your event data store.</p> </important> <p>CloudTrail also supports KMS multi-Region keys. For more information about multi-Region keys, see <a href=\"https://docs.aws.amazon.com/kms/latest/developerguide/multi-region-keys-overview.html\">Using multi-Region keys</a> in the <i>Key Management Service Developer Guide</i>.</p> <p>Examples:</p> <ul> <li> <p> <code>alias/MyAliasName</code> </p> </li> <li> <p> <code>arn:aws:kms:us-east-2:************:alias/MyAliasName</code> </p> </li> <li> <p> <code>arn:aws:kms:us-east-2:************:key/********-1234-1234-1234-************</code> </p> </li> <li> <p> <code>********-1234-1234-1234-************</code> </p> </li> </ul>"}, "StartIngestion": {"shape": "Boolean", "documentation": "<p>Specifies whether the event data store should start ingesting live events. The default is true.</p>"}, "BillingMode": {"shape": "BillingMode", "documentation": "<p>The billing mode for the event data store determines the cost for ingesting events and the default and maximum retention period for the event data store.</p> <p>The following are the possible values:</p> <ul> <li> <p> <code>EXTENDABLE_RETENTION_PRICING</code> - This billing mode is generally recommended if you want a flexible retention period of up to 3653 days (about 10 years). The default retention period for this billing mode is 366 days.</p> </li> <li> <p> <code>FIXED_RETENTION_PRICING</code> - This billing mode is recommended if you expect to ingest more than 25 TB of event data per month and need a retention period of up to 2557 days (about 7 years). The default retention period for this billing mode is 2557 days.</p> </li> </ul> <p>The default value is <code>EXTENDABLE_RETENTION_PRICING</code>.</p> <p>For more information about CloudTrail pricing, see <a href=\"http://aws.amazon.com/cloudtrail/pricing/\">CloudTrail Pricing</a> and <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/cloudtrail-lake-manage-costs.html\">Managing CloudTrail Lake costs</a>.</p>"}}}, "CreateEventDataStoreResponse": {"type": "structure", "members": {"EventDataStoreArn": {"shape": "EventDataStoreArn", "documentation": "<p>The ARN of the event data store.</p>"}, "Name": {"shape": "EventDataStoreName", "documentation": "<p>The name of the event data store.</p>"}, "Status": {"shape": "EventDataStoreStatus", "documentation": "<p>The status of event data store creation.</p>"}, "AdvancedEventSelectors": {"shape": "AdvancedEventSelectors", "documentation": "<p>The advanced event selectors that were used to select the events for the data store.</p>"}, "MultiRegionEnabled": {"shape": "Boolean", "documentation": "<p>Indicates whether the event data store collects events from all Regions, or only from the Region in which it was created.</p>"}, "OrganizationEnabled": {"shape": "Boolean", "documentation": "<p>Indicates whether an event data store is collecting logged events for an organization in Organizations.</p>"}, "RetentionPeriod": {"shape": "RetentionPeriod", "documentation": "<p>The retention period of an event data store, in days.</p>"}, "TerminationProtectionEnabled": {"shape": "TerminationProtectionEnabled", "documentation": "<p>Indicates whether termination protection is enabled for the event data store.</p>"}, "TagsList": {"shape": "TagsList"}, "CreatedTimestamp": {"shape": "Date", "documentation": "<p>The timestamp that shows when the event data store was created.</p>"}, "UpdatedTimestamp": {"shape": "Date", "documentation": "<p>The timestamp that shows when an event data store was updated, if applicable. <code>UpdatedTimestamp</code> is always either the same or newer than the time shown in <code>CreatedTimestamp</code>.</p>"}, "KmsKeyId": {"shape": "EventDataStoreKmsKeyId", "documentation": "<p>Specifies the KMS key ID that encrypts the events delivered by CloudTrail. The value is a fully specified ARN to a KMS key in the following format.</p> <p> <code>arn:aws:kms:us-east-2:************:key/********-1234-1234-1234-************</code> </p>"}, "BillingMode": {"shape": "BillingMode", "documentation": "<p>The billing mode for the event data store.</p>"}}}, "CreateTrailRequest": {"type": "structure", "required": ["Name", "S3BucketName"], "members": {"Name": {"shape": "String", "documentation": "<p>Specifies the name of the trail. The name must meet the following requirements:</p> <ul> <li> <p>Contain only ASCII letters (a-z, A-Z), numbers (0-9), periods (.), underscores (_), or dashes (-)</p> </li> <li> <p>Start with a letter or number, and end with a letter or number</p> </li> <li> <p>Be between 3 and 128 characters</p> </li> <li> <p>Have no adjacent periods, underscores or dashes. Names like <code>my-_namespace</code> and <code>my--namespace</code> are not valid.</p> </li> <li> <p>Not be in IP address format (for example, ***********)</p> </li> </ul>"}, "S3BucketName": {"shape": "String", "documentation": "<p>Specifies the name of the Amazon S3 bucket designated for publishing log files. For information about bucket naming rules, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/bucketnamingrules.html\">Bucket naming rules</a> in the <i>Amazon Simple Storage Service User Guide</i>. </p>"}, "S3KeyPrefix": {"shape": "String", "documentation": "<p>Specifies the Amazon S3 key prefix that comes after the name of the bucket you have designated for log file delivery. For more information, see <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/get-and-view-cloudtrail-log-files.html#cloudtrail-find-log-files\">Finding Your CloudTrail Log Files</a>. The maximum length is 200 characters.</p>"}, "SnsTopicName": {"shape": "String", "documentation": "<p>Specifies the name or ARN of the Amazon SNS topic defined for notification of log file delivery. The maximum length is 256 characters.</p>"}, "IncludeGlobalServiceEvents": {"shape": "Boolean", "documentation": "<p>Specifies whether the trail is publishing events from global services such as IAM to the log files.</p>"}, "IsMultiRegionTrail": {"shape": "Boolean", "documentation": "<p>Specifies whether the trail is created in the current Region or in all Regions. The default is false, which creates a trail only in the Region where you are signed in. As a best practice, consider creating trails that log events in all Regions.</p>"}, "EnableLogFileValidation": {"shape": "Boolean", "documentation": "<p>Specifies whether log file integrity validation is enabled. The default is false.</p> <note> <p>When you disable log file integrity validation, the chain of digest files is broken after one hour. CloudTrail does not create digest files for log files that were delivered during a period in which log file integrity validation was disabled. For example, if you enable log file integrity validation at noon on January 1, disable it at noon on January 2, and re-enable it at noon on January 10, digest files will not be created for the log files delivered from noon on January 2 to noon on January 10. The same applies whenever you stop CloudTrail logging or delete a trail.</p> </note>"}, "CloudWatchLogsLogGroupArn": {"shape": "String", "documentation": "<p>Specifies a log group name using an Amazon Resource Name (ARN), a unique identifier that represents the log group to which CloudTrail logs will be delivered. You must use a log group that exists in your account.</p> <p>Not required unless you specify <code>CloudWatchLogsRoleArn</code>.</p>"}, "CloudWatchLogsRoleArn": {"shape": "String", "documentation": "<p>Specifies the role for the CloudWatch Logs endpoint to assume to write to a user's log group. You must use a role that exists in your account.</p>"}, "KmsKeyId": {"shape": "String", "documentation": "<p>Specifies the KMS key ID to use to encrypt the logs delivered by CloudTrail. The value can be an alias name prefixed by <code>alias/</code>, a fully specified ARN to an alias, a fully specified ARN to a key, or a globally unique identifier.</p> <p>CloudTrail also supports KMS multi-Region keys. For more information about multi-Region keys, see <a href=\"https://docs.aws.amazon.com/kms/latest/developerguide/multi-region-keys-overview.html\">Using multi-Region keys</a> in the <i>Key Management Service Developer Guide</i>.</p> <p>Examples:</p> <ul> <li> <p> <code>alias/MyAliasName</code> </p> </li> <li> <p> <code>arn:aws:kms:us-east-2:************:alias/MyAliasName</code> </p> </li> <li> <p> <code>arn:aws:kms:us-east-2:************:key/********-1234-1234-1234-************</code> </p> </li> <li> <p> <code>********-1234-1234-1234-************</code> </p> </li> </ul>"}, "IsOrganizationTrail": {"shape": "Boolean", "documentation": "<p>Specifies whether the trail is created for all accounts in an organization in Organizations, or only for the current Amazon Web Services account. The default is false, and cannot be true unless the call is made on behalf of an Amazon Web Services account that is the management account or delegated administrator account for an organization in Organizations.</p>"}, "TagsList": {"shape": "TagsList"}}, "documentation": "<p>Specifies the settings for each trail.</p>"}, "CreateTrailResponse": {"type": "structure", "members": {"Name": {"shape": "String", "documentation": "<p>Specifies the name of the trail.</p>"}, "S3BucketName": {"shape": "String", "documentation": "<p>Specifies the name of the Amazon S3 bucket designated for publishing log files.</p>"}, "S3KeyPrefix": {"shape": "String", "documentation": "<p>Specifies the Amazon S3 key prefix that comes after the name of the bucket you have designated for log file delivery. For more information, see <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/get-and-view-cloudtrail-log-files.html#cloudtrail-find-log-files\">Finding Your CloudTrail Log Files</a>.</p>"}, "SnsTopicName": {"shape": "String", "documentation": "<p>This field is no longer in use. Use <code>SnsTopicARN</code>.</p>", "deprecated": true}, "SnsTopicARN": {"shape": "String", "documentation": "<p>Specifies the ARN of the Amazon SNS topic that CloudTrail uses to send notifications when log files are delivered. The format of a topic ARN is:</p> <p> <code>arn:aws:sns:us-east-2:************:MyTopic</code> </p>"}, "IncludeGlobalServiceEvents": {"shape": "Boolean", "documentation": "<p>Specifies whether the trail is publishing events from global services such as IAM to the log files.</p>"}, "IsMultiRegionTrail": {"shape": "Boolean", "documentation": "<p>Specifies whether the trail exists in one Region or in all Regions.</p>"}, "TrailARN": {"shape": "String", "documentation": "<p>Specifies the ARN of the trail that was created. The format of a trail ARN is:</p> <p> <code>arn:aws:cloudtrail:us-east-2:************:trail/MyTrail</code> </p>"}, "LogFileValidationEnabled": {"shape": "Boolean", "documentation": "<p>Specifies whether log file integrity validation is enabled.</p>"}, "CloudWatchLogsLogGroupArn": {"shape": "String", "documentation": "<p>Specifies the Amazon Resource Name (ARN) of the log group to which CloudTrail logs will be delivered.</p>"}, "CloudWatchLogsRoleArn": {"shape": "String", "documentation": "<p>Specifies the role for the CloudWatch Logs endpoint to assume to write to a user's log group.</p>"}, "KmsKeyId": {"shape": "String", "documentation": "<p>Specifies the KMS key ID that encrypts the events delivered by CloudTrail. The value is a fully specified ARN to a KMS key in the following format.</p> <p> <code>arn:aws:kms:us-east-2:************:key/********-1234-1234-1234-************</code> </p>"}, "IsOrganizationTrail": {"shape": "Boolean", "documentation": "<p>Specifies whether the trail is an organization trail.</p>"}}, "documentation": "<p>Returns the objects or data listed below if successful. Otherwise, returns an error.</p>"}, "DashboardArn": {"type": "string", "pattern": "^[a-zA-Z0-9._/\\-:]+$"}, "DashboardDetail": {"type": "structure", "members": {"DashboardArn": {"shape": "DashboardArn", "documentation": "<p> The ARN for the dashboard. </p>"}, "Type": {"shape": "DashboardType", "documentation": "<p> The type of dashboard. </p>"}}, "documentation": "<p> Provides information about a CloudTrail Lake dashboard. </p>"}, "DashboardName": {"type": "string", "max": 128, "min": 3, "pattern": "^[a-zA-Z0-9_\\-]+$"}, "DashboardStatus": {"type": "string", "enum": ["CREATING", "CREATED", "UPDATING", "UPDATED", "DELETING"]}, "DashboardType": {"type": "string", "enum": ["MANAGED", "CUSTOM"]}, "Dashboards": {"type": "list", "member": {"shape": "DashboardDetail"}}, "DataResource": {"type": "structure", "members": {"Type": {"shape": "String", "documentation": "<p>The resource type in which you want to log data events. You can specify the following <i>basic</i> event selector resource types:</p> <ul> <li> <p> <code>AWS::DynamoDB::Table</code> </p> </li> <li> <p> <code>AWS::Lambda::Function</code> </p> </li> <li> <p> <code>AWS::S3::Object</code> </p> </li> </ul> <p>Additional resource types are available through <i>advanced</i> event selectors. For more information, see <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/APIReference/API_AdvancedEventSelector.html\">AdvancedEventSelector</a>.</p>"}, "Values": {"shape": "DataResourceValues", "documentation": "<p>An array of Amazon Resource Name (ARN) strings or partial ARN strings for the specified resource type.</p> <ul> <li> <p>To log data events for all objects in all S3 buckets in your Amazon Web Services account, specify the prefix as <code>arn:aws:s3</code>.</p> <note> <p>This also enables logging of data event activity performed by any user or role in your Amazon Web Services account, even if that activity is performed on a bucket that belongs to another Amazon Web Services account.</p> </note> </li> <li> <p>To log data events for all objects in an S3 bucket, specify the bucket and an empty object prefix such as <code>arn:aws:s3:::amzn-s3-demo-bucket1/</code>. The trail logs data events for all objects in this S3 bucket.</p> </li> <li> <p>To log data events for specific objects, specify the S3 bucket and object prefix such as <code>arn:aws:s3:::amzn-s3-demo-bucket1/example-images</code>. The trail logs data events for objects in this S3 bucket that match the prefix.</p> </li> <li> <p>To log data events for all Lambda functions in your Amazon Web Services account, specify the prefix as <code>arn:aws:lambda</code>.</p> <note> <p>This also enables logging of <code>Invoke</code> activity performed by any user or role in your Amazon Web Services account, even if that activity is performed on a function that belongs to another Amazon Web Services account. </p> </note> </li> <li> <p>To log data events for a specific Lambda function, specify the function ARN.</p> <note> <p>Lambda function ARNs are exact. For example, if you specify a function ARN <i>arn:aws:lambda:us-west-2:************:function:helloworld</i>, data events will only be logged for <i>arn:aws:lambda:us-west-2:************:function:helloworld</i>. They will not be logged for <i>arn:aws:lambda:us-west-2:************:function:helloworld2</i>.</p> </note> </li> <li> <p>To log data events for all DynamoDB tables in your Amazon Web Services account, specify the prefix as <code>arn:aws:dynamodb</code>.</p> </li> </ul>"}}, "documentation": "<p>You can configure the <code>DataResource</code> in an <code>EventSelector</code> to log data events for the following three resource types:</p> <ul> <li> <p> <code>AWS::DynamoDB::Table</code> </p> </li> <li> <p> <code>AWS::Lambda::Function</code> </p> </li> <li> <p> <code>AWS::S3::Object</code> </p> </li> </ul> <p>To log data events for all other resource types including objects stored in <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/directory-buckets-overview.html\">directory buckets</a>, you must use <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/APIReference/API_AdvancedEventSelector.html\">AdvancedEventSelectors</a>. You must also use <code>AdvancedEventSelectors</code> if you want to filter on the <code>eventName</code> field.</p> <p>Configure the <code>DataResource</code> to specify the resource type and resource ARNs for which you want to log data events.</p> <note> <p>The total number of allowed data resources is 250. This number can be distributed between 1 and 5 event selectors, but the total cannot exceed 250 across all selectors for the trail.</p> </note> <p>The following example demonstrates how logging works when you configure logging of all data events for a general purpose bucket named <code>amzn-s3-demo-bucket1</code>. In this example, the CloudTrail user specified an empty prefix, and the option to log both <code>Read</code> and <code>Write</code> data events.</p> <ol> <li> <p>A user uploads an image file to <code>amzn-s3-demo-bucket1</code>.</p> </li> <li> <p>The <code>PutObject</code> API operation is an Amazon S3 object-level API. It is recorded as a data event in CloudTrail. Because the CloudTrail user specified an S3 bucket with an empty prefix, events that occur on any object in that bucket are logged. The trail processes and logs the event.</p> </li> <li> <p>A user uploads an object to an Amazon S3 bucket named <code>arn:aws:s3:::amzn-s3-demo-bucket1</code>.</p> </li> <li> <p>The <code>PutObject</code> API operation occurred for an object in an S3 bucket that the CloudTrail user didn't specify for the trail. The trail doesn’t log the event.</p> </li> </ol> <p>The following example demonstrates how logging works when you configure logging of Lambda data events for a Lambda function named <i>MyLambdaFunction</i>, but not for all Lambda functions.</p> <ol> <li> <p>A user runs a script that includes a call to the <i>MyLambdaFunction</i> function and the <i>MyOtherLambdaFunction</i> function.</p> </li> <li> <p>The <code>Invoke</code> API operation on <i>MyLambdaFunction</i> is an Lambda API. It is recorded as a data event in CloudTrail. Because the CloudTrail user specified logging data events for <i>MyLambdaFunction</i>, any invocations of that function are logged. The trail processes and logs the event.</p> </li> <li> <p>The <code>Invoke</code> API operation on <i>MyOtherLambdaFunction</i> is an Lambda API. Because the CloudTrail user did not specify logging data events for all Lambda functions, the <code>Invoke</code> operation for <i>MyOtherLambdaFunction</i> does not match the function specified for the trail. The trail doesn’t log the event. </p> </li> </ol>"}, "DataResourceValues": {"type": "list", "member": {"shape": "String"}}, "DataResources": {"type": "list", "member": {"shape": "DataResource"}}, "Date": {"type": "timestamp"}, "DelegatedAdminAccountLimitExceededException": {"type": "structure", "members": {}, "documentation": "<p>This exception is thrown when the maximum number of CloudTrail delegated administrators is reached.</p>", "exception": true}, "DeleteChannelRequest": {"type": "structure", "required": ["Channel"], "members": {"Channel": {"shape": "ChannelArn", "documentation": "<p>The ARN or the <code>UUID</code> value of the channel that you want to delete.</p>"}}}, "DeleteChannelResponse": {"type": "structure", "members": {}}, "DeleteDashboardRequest": {"type": "structure", "required": ["DashboardId"], "members": {"DashboardId": {"shape": "DashboardArn", "documentation": "<p> The name or ARN for the dashboard. </p>"}}}, "DeleteDashboardResponse": {"type": "structure", "members": {}}, "DeleteEventDataStoreRequest": {"type": "structure", "required": ["EventDataStore"], "members": {"EventDataStore": {"shape": "EventDataStoreArn", "documentation": "<p>The ARN (or the ID suffix of the ARN) of the event data store to delete.</p>"}}}, "DeleteEventDataStoreResponse": {"type": "structure", "members": {}}, "DeleteResourcePolicyRequest": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "ResourceArn", "documentation": "<p> The Amazon Resource Name (ARN) of the CloudTrail event data store, dashboard, or channel you're deleting the resource-based policy from.</p> <p>Example event data store ARN format: <code>arn:aws:cloudtrail:us-east-2:************:eventdatastore/EXAMPLE-f852-4e8f-8bd1-bcf6cEXAMPLE</code> </p> <p>Example dashboard ARN format: <code>arn:aws:cloudtrail:us-east-1:************:dashboard/exampleDash</code> </p> <p>Example channel ARN format: <code>arn:aws:cloudtrail:us-east-2:************:channel/***********</code> </p>"}}}, "DeleteResourcePolicyResponse": {"type": "structure", "members": {}}, "DeleteTrailRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "String", "documentation": "<p>Specifies the name or the CloudTrail ARN of the trail to be deleted. The following is the format of a trail ARN. <code>arn:aws:cloudtrail:us-east-2:************:trail/MyTrail</code> </p>"}}, "documentation": "<p>The request that specifies the name of a trail to delete.</p>"}, "DeleteTrailResponse": {"type": "structure", "members": {}, "documentation": "<p>Returns the objects or data listed below if successful. Otherwise, returns an error.</p>"}, "DeliveryS3Uri": {"type": "string", "max": 1024, "pattern": "s3://[a-z0-9][\\.\\-a-z0-9]{1,61}[a-z0-9](/.*)?"}, "DeliveryStatus": {"type": "string", "enum": ["SUCCESS", "FAILED", "FAILED_SIGNING_FILE", "PENDING", "RESOURCE_NOT_FOUND", "ACCESS_DENIED", "ACCESS_DENIED_SIGNING_FILE", "CANCELLED", "UNKNOWN"]}, "DeregisterOrganizationDelegatedAdminRequest": {"type": "structure", "required": ["DelegatedAdminAccountId"], "members": {"DelegatedAdminAccountId": {"shape": "AccountId", "documentation": "<p>A delegated administrator account ID. This is a member account in an organization that is currently designated as a delegated administrator.</p>"}}, "documentation": "<p>Removes CloudTrail delegated administrator permissions from a specified member account in an organization that is currently designated as a delegated administrator.</p>"}, "DeregisterOrganizationDelegatedAdminResponse": {"type": "structure", "members": {}, "documentation": "<p>Returns the following response if successful. Otherwise, returns an error.</p>"}, "DescribeQueryRequest": {"type": "structure", "members": {"EventDataStore": {"shape": "EventDataStoreArn", "documentation": "<p>The ARN (or the ID suffix of the ARN) of an event data store on which the specified query was run.</p>", "deprecated": true, "deprecatedMessage": "EventDataStore is no longer required by DescribeQueryRequest"}, "QueryId": {"shape": "UUID", "documentation": "<p>The query ID.</p>"}, "QueryAlias": {"shape": "Query<PERSON><PERSON><PERSON>", "documentation": "<p> The alias that identifies a query template. </p>"}, "RefreshId": {"shape": "RefreshId", "documentation": "<p> The ID of the dashboard refresh. </p>"}, "EventDataStoreOwnerAccountId": {"shape": "AccountId", "documentation": "<p> The account ID of the event data store owner. </p>"}}}, "DescribeQueryResponse": {"type": "structure", "members": {"QueryId": {"shape": "UUID", "documentation": "<p>The ID of the query.</p>"}, "QueryString": {"shape": "QueryStatement", "documentation": "<p>The SQL code of a query.</p>"}, "QueryStatus": {"shape": "QueryStatus", "documentation": "<p>The status of a query. Values for <code>QueryStatus</code> include <code>QUEUED</code>, <code>RUNNING</code>, <code>FINISHED</code>, <code>FAILED</code>, <code>TIMED_OUT</code>, or <code>CANCELLED</code> </p>"}, "QueryStatistics": {"shape": "QueryStatisticsForDescribeQuery", "documentation": "<p><PERSON><PERSON><PERSON> about a query, including the number of events that were matched, the total number of events scanned, the query run time in milliseconds, and the query's creation time.</p>"}, "ErrorMessage": {"shape": "ErrorMessage", "documentation": "<p>The error message returned if a query failed.</p>"}, "DeliveryS3Uri": {"shape": "DeliveryS3Uri", "documentation": "<p>The URI for the S3 bucket where CloudTrail delivered query results, if applicable.</p>"}, "DeliveryStatus": {"shape": "DeliveryStatus", "documentation": "<p>The delivery status.</p>"}, "Prompt": {"shape": "Prompt", "documentation": "<p> The prompt used for a generated query. For information about generated queries, see <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/lake-query-generator.html\">Create CloudTrail Lake queries from natural language prompts</a> in the <i>CloudTrail </i> user guide. </p>"}, "EventDataStoreOwnerAccountId": {"shape": "AccountId", "documentation": "<p> The account ID of the event data store owner. </p>"}}}, "DescribeTrailsRequest": {"type": "structure", "members": {"trailNameList": {"shape": "TrailNameList", "documentation": "<p>Specifies a list of trail names, trail ARNs, or both, of the trails to describe. The format of a trail ARN is:</p> <p> <code>arn:aws:cloudtrail:us-east-2:************:trail/MyTrail</code> </p> <p>If an empty list is specified, information for the trail in the current Region is returned.</p> <ul> <li> <p>If an empty list is specified and <code>IncludeShadowTrails</code> is false, then information for all trails in the current Region is returned.</p> </li> <li> <p>If an empty list is specified and IncludeShadowTrails is null or true, then information for all trails in the current Region and any associated shadow trails in other Regions is returned.</p> </li> </ul> <note> <p>If one or more trail names are specified, information is returned only if the names match the names of trails belonging only to the current Region and current account. To return information about a trail in another Region, you must specify its trail ARN.</p> </note>"}, "includeShadowTrails": {"shape": "Boolean", "documentation": "<p>Specifies whether to include shadow trails in the response. A shadow trail is the replication in a Region of a trail that was created in a different Region, or in the case of an organization trail, the replication of an organization trail in member accounts. If you do not include shadow trails, organization trails in a member account and Region replication trails will not be returned. The default is true.</p>"}}, "documentation": "<p>Returns information about the trail.</p>"}, "DescribeTrailsResponse": {"type": "structure", "members": {"trailList": {"shape": "TrailList", "documentation": "<p>The list of trail objects. Trail objects with string values are only returned if values for the objects exist in a trail's configuration. For example, <code>SNSTopicName</code> and <code>SNSTopicARN</code> are only returned in results if a trail is configured to send SNS notifications. Similarly, <code>KMSKeyId</code> only appears in results if a trail's log files are encrypted with KMS customer managed keys.</p>"}}, "documentation": "<p>Returns the objects or data listed below if successful. Otherwise, returns an error.</p>"}, "Destination": {"type": "structure", "required": ["Type", "Location"], "members": {"Type": {"shape": "DestinationType", "documentation": "<p>The type of destination for events arriving from a channel. For channels used for a CloudTrail Lake integration, the value is <code>EVENT_DATA_STORE</code>. For service-linked channels, the value is <code>AWS_SERVICE</code>. </p>"}, "Location": {"shape": "Location", "documentation": "<p> For channels used for a CloudTrail Lake integration, the location is the ARN of an event data store that receives events from a channel. For service-linked channels, the location is the name of the Amazon Web Services service.</p>"}}, "documentation": "<p>Contains information about the destination receiving events.</p>"}, "DestinationType": {"type": "string", "enum": ["EVENT_DATA_STORE", "AWS_SERVICE"]}, "Destinations": {"type": "list", "member": {"shape": "Destination"}, "max": 200, "min": 1}, "DisableFederationRequest": {"type": "structure", "required": ["EventDataStore"], "members": {"EventDataStore": {"shape": "EventDataStoreArn", "documentation": "<p> The ARN (or ID suffix of the ARN) of the event data store for which you want to disable Lake query federation. </p>"}}}, "DisableFederationResponse": {"type": "structure", "members": {"EventDataStoreArn": {"shape": "EventDataStoreArn", "documentation": "<p> The ARN of the event data store for which you disabled Lake query federation. </p>"}, "FederationStatus": {"shape": "FederationStatus", "documentation": "<p> The federation status. </p>"}}}, "Double": {"type": "double"}, "EnableFederationRequest": {"type": "structure", "required": ["EventDataStore", "FederationRoleArn"], "members": {"EventDataStore": {"shape": "EventDataStoreArn", "documentation": "<p>The ARN (or ID suffix of the ARN) of the event data store for which you want to enable Lake query federation.</p>"}, "FederationRoleArn": {"shape": "FederationRoleArn", "documentation": "<p> The ARN of the federation role to use for the event data store. Amazon Web Services services like Lake Formation use this federation role to access data for the federated event data store. The federation role must exist in your account and provide the <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/query-federation.html#query-federation-permissions-role\">required minimum permissions</a>. </p>"}}}, "EnableFederationResponse": {"type": "structure", "members": {"EventDataStoreArn": {"shape": "EventDataStoreArn", "documentation": "<p> The ARN of the event data store for which you enabled Lake query federation. </p>"}, "FederationStatus": {"shape": "FederationStatus", "documentation": "<p> The federation status. </p>"}, "FederationRoleArn": {"shape": "FederationRoleArn", "documentation": "<p> The ARN of the federation role. </p>"}}}, "ErrorCode": {"type": "string", "max": 128, "pattern": "^[\\w\\d\\s_.,\\-:\\[\\]]+$"}, "ErrorMessage": {"type": "string", "max": 1000, "min": 4, "pattern": ".*"}, "Event": {"type": "structure", "members": {"EventId": {"shape": "String", "documentation": "<p>The CloudTrail ID of the event returned.</p>"}, "EventName": {"shape": "String", "documentation": "<p>The name of the event returned.</p>"}, "ReadOnly": {"shape": "String", "documentation": "<p>Information about whether the event is a write event or a read event. </p>"}, "AccessKeyId": {"shape": "String", "documentation": "<p>The Amazon Web Services access key ID that was used to sign the request. If the request was made with temporary security credentials, this is the access key ID of the temporary credentials.</p>"}, "EventTime": {"shape": "Date", "documentation": "<p>The date and time of the event returned.</p>"}, "EventSource": {"shape": "String", "documentation": "<p>The Amazon Web Services service to which the request was made.</p>"}, "Username": {"shape": "String", "documentation": "<p>A user name or role name of the requester that called the API in the event returned.</p>"}, "Resources": {"shape": "ResourceList", "documentation": "<p>A list of resources referenced by the event returned.</p>"}, "CloudTrailEvent": {"shape": "String", "documentation": "<p>A JSON string that contains a representation of the event returned.</p>"}}, "documentation": "<p>Contains information about an event that was returned by a lookup request. The result includes a representation of a CloudTrail event.</p>"}, "EventCategory": {"type": "string", "enum": ["insight"]}, "EventDataStore": {"type": "structure", "members": {"EventDataStoreArn": {"shape": "EventDataStoreArn", "documentation": "<p>The ARN of the event data store.</p>"}, "Name": {"shape": "EventDataStoreName", "documentation": "<p>The name of the event data store.</p>"}, "TerminationProtectionEnabled": {"shape": "TerminationProtectionEnabled", "documentation": "<p>Indicates whether the event data store is protected from termination.</p>", "deprecated": true, "deprecatedMessage": "TerminationProtectionEnabled is no longer returned by ListEventDataStores"}, "Status": {"shape": "EventDataStoreStatus", "documentation": "<p>The status of an event data store.</p>", "deprecated": true, "deprecatedMessage": "Status is no longer returned by ListEventDataStores"}, "AdvancedEventSelectors": {"shape": "AdvancedEventSelectors", "documentation": "<p>The advanced event selectors that were used to select events for the data store.</p>", "deprecated": true, "deprecatedMessage": "AdvancedEventSelectors is no longer returned by ListEventDataStores"}, "MultiRegionEnabled": {"shape": "Boolean", "documentation": "<p>Indicates whether the event data store includes events from all Regions, or only from the Region in which it was created.</p>", "deprecated": true, "deprecatedMessage": "MultiRegionEnabled is no longer returned by ListEventDataStores"}, "OrganizationEnabled": {"shape": "Boolean", "documentation": "<p>Indicates that an event data store is collecting logged events for an organization.</p>", "deprecated": true, "deprecatedMessage": "OrganizationEnabled is no longer returned by ListEventDataStores"}, "RetentionPeriod": {"shape": "RetentionPeriod", "documentation": "<p>The retention period, in days.</p>", "deprecated": true, "deprecatedMessage": "RetentionPeriod is no longer returned by ListEventDataStores"}, "CreatedTimestamp": {"shape": "Date", "documentation": "<p>The timestamp of the event data store's creation.</p>", "deprecated": true, "deprecatedMessage": "CreatedTimestamp is no longer returned by ListEventDataStores"}, "UpdatedTimestamp": {"shape": "Date", "documentation": "<p>The timestamp showing when an event data store was updated, if applicable. <code>UpdatedTimestamp</code> is always either the same or newer than the time shown in <code>CreatedTimestamp</code>.</p>", "deprecated": true, "deprecatedMessage": "UpdatedTimestamp is no longer returned by ListEventDataStores"}}, "documentation": "<p>A storage lake of event data against which you can run complex SQL-based queries. An event data store can include events that you have logged on your account. To select events for an event data store, use <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/cloudtrail-lake-concepts.html#adv-event-selectors\">advanced event selectors</a>.</p>"}, "EventDataStoreARNInvalidException": {"type": "structure", "members": {}, "documentation": "<p>The specified event data store ARN is not valid or does not map to an event data store in your account.</p>", "exception": true}, "EventDataStoreAlreadyExistsException": {"type": "structure", "members": {}, "documentation": "<p>An event data store with that name already exists.</p>", "exception": true}, "EventDataStoreArn": {"type": "string", "max": 256, "min": 3, "pattern": "^[a-zA-Z0-9._/\\-:]+$"}, "EventDataStoreFederationEnabledException": {"type": "structure", "members": {}, "documentation": "<p> You cannot delete the event data store because Lake query federation is enabled. To delete the event data store, run the <code>DisableFederation</code> operation to disable Lake query federation on the event data store. </p>", "exception": true}, "EventDataStoreHasOngoingImportException": {"type": "structure", "members": {}, "documentation": "<p> This exception is thrown when you try to update or delete an event data store that currently has an import in progress. </p>", "exception": true}, "EventDataStoreKmsKeyId": {"type": "string", "max": 350, "min": 1, "pattern": "^[a-zA-Z0-9._/\\-:]+$"}, "EventDataStoreList": {"type": "list", "member": {"shape": "EventDataStoreArn"}, "max": 1, "min": 1}, "EventDataStoreMaxLimitExceededException": {"type": "structure", "members": {}, "documentation": "<p>Your account has used the maximum number of event data stores.</p>", "exception": true}, "EventDataStoreName": {"type": "string", "max": 128, "min": 3, "pattern": "^[a-zA-Z0-9._\\-]+$"}, "EventDataStoreNotFoundException": {"type": "structure", "members": {}, "documentation": "<p>The specified event data store was not found.</p>", "exception": true}, "EventDataStoreStatus": {"type": "string", "enum": ["CREATED", "ENABLED", "PENDING_DELETION", "STARTING_INGESTION", "STOPPING_INGESTION", "STOPPED_INGESTION"]}, "EventDataStoreTerminationProtectedException": {"type": "structure", "members": {}, "documentation": "<p>The event data store cannot be deleted because termination protection is enabled for it.</p>", "exception": true}, "EventDataStores": {"type": "list", "member": {"shape": "EventDataStore"}}, "EventName": {"type": "string", "max": 128, "pattern": "^[A-Za-z0-9_]+$"}, "EventSelector": {"type": "structure", "members": {"ReadWriteType": {"shape": "ReadWriteType", "documentation": "<p>Specify if you want your trail to log read-only events, write-only events, or all. For example, the EC2 <code>GetConsoleOutput</code> is a read-only API operation and <code>RunInstances</code> is a write-only API operation.</p> <p> By default, the value is <code>All</code>.</p>"}, "IncludeManagementEvents": {"shape": "Boolean", "documentation": "<p>Specify if you want your event selector to include management events for your trail.</p> <p> For more information, see <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/logging-management-events-with-cloudtrail.html\">Management Events</a> in the <i>CloudTrail User Guide</i>.</p> <p>By default, the value is <code>true</code>.</p> <p>The first copy of management events is free. You are charged for additional copies of management events that you are logging on any subsequent trail in the same Region. For more information about CloudTrail pricing, see <a href=\"http://aws.amazon.com/cloudtrail/pricing/\">CloudTrail Pricing</a>.</p>"}, "DataResources": {"shape": "DataResources", "documentation": "<p>CloudTrail supports data event logging for Amazon S3 objects in standard S3 buckets, Lambda functions, and Amazon DynamoDB tables with basic event selectors. You can specify up to 250 resources for an individual event selector, but the total number of data resources cannot exceed 250 across all event selectors in a trail. This limit does not apply if you configure resource logging for all data events.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/logging-data-events-with-cloudtrail.html\">Data Events</a> and <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/WhatIsCloudTrail-Limits.html\">Limits in CloudTrail</a> in the <i>CloudTrail User Guide</i>.</p> <note> <p>To log data events for all other resource types including objects stored in <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/directory-buckets-overview.html\">directory buckets</a>, you must use <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/APIReference/API_AdvancedEventSelector.html\">AdvancedEventSelectors</a>. You must also use <code>AdvancedEventSelectors</code> if you want to filter on the <code>eventName</code> field.</p> </note>"}, "ExcludeManagementEventSources": {"shape": "ExcludeManagementEventSources", "documentation": "<p>An optional list of service event sources from which you do not want management events to be logged on your trail. In this release, the list can be empty (disables the filter), or it can filter out Key Management Service or Amazon RDS Data API events by containing <code>kms.amazonaws.com</code> or <code>rdsdata.amazonaws.com</code>. By default, <code>ExcludeManagementEventSources</code> is empty, and KMS and Amazon RDS Data API events are logged to your trail. You can exclude management event sources only in Regions that support the event source.</p>"}}, "documentation": "<p>Use event selectors to further specify the management and data event settings for your trail. By default, trails created without specific event selectors will be configured to log all read and write management events, and no data events. When an event occurs in your account, CloudTrail evaluates the event selector for all trails. For each trail, if the event matches any event selector, the trail processes and logs the event. If the event doesn't match any event selector, the trail doesn't log the event.</p> <p>You can configure up to five event selectors for a trail.</p> <p>You cannot apply both event selectors and advanced event selectors to a trail.</p>"}, "EventSelectors": {"type": "list", "member": {"shape": "EventSelector"}}, "EventSource": {"type": "string", "max": 256, "pattern": "^[a-z0-9_-]+\\.amazonaws\\.com$"}, "EventsList": {"type": "list", "member": {"shape": "Event"}}, "ExcludeManagementEventSources": {"type": "list", "member": {"shape": "String"}}, "FederationRoleArn": {"type": "string", "max": 125, "min": 3, "pattern": "^[a-zA-Z0-9._/\\-:@=\\+,\\.]+$"}, "FederationStatus": {"type": "string", "enum": ["ENABLING", "ENABLED", "DISABLING", "DISABLED"]}, "GenerateQueryRequest": {"type": "structure", "required": ["EventDataStores", "Prompt"], "members": {"EventDataStores": {"shape": "EventDataStoreList", "documentation": "<p> The ARN (or ID suffix of the ARN) of the event data store that you want to query. You can only specify one event data store. </p>"}, "Prompt": {"shape": "Prompt", "documentation": "<p> The prompt that you want to use to generate the query. The prompt must be in English. For example prompts, see <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/lake-query-generator.html#lake-query-generator-examples\">Example prompts</a> in the <i>CloudTrail </i> user guide. </p>"}}}, "GenerateQueryResponse": {"type": "structure", "members": {"QueryStatement": {"shape": "QueryStatement", "documentation": "<p> The SQL query statement generated from the prompt. </p>"}, "QueryAlias": {"shape": "Query<PERSON><PERSON><PERSON>", "documentation": "<p> An alias that identifies the prompt. When you run the <code>StartQuery</code> operation, you can pass in either the <code>QueryAlias</code> or <code>QueryStatement</code> parameter. </p>"}, "EventDataStoreOwnerAccountId": {"shape": "AccountId", "documentation": "<p> The account ID of the event data store owner. </p>"}}}, "GenerateResponseException": {"type": "structure", "members": {}, "documentation": "<p> This exception is thrown when a valid query could not be generated for the provided prompt. </p>", "exception": true}, "GetChannelRequest": {"type": "structure", "required": ["Channel"], "members": {"Channel": {"shape": "ChannelArn", "documentation": "<p>The ARN or <code>UUID</code> of a channel.</p>"}}}, "GetChannelResponse": {"type": "structure", "members": {"ChannelArn": {"shape": "ChannelArn", "documentation": "<p>The ARN of an channel returned by a <code>GetChannel</code> request.</p>"}, "Name": {"shape": "ChannelName", "documentation": "<p> The name of the CloudTrail channel. For service-linked channels, the name is <code>aws-service-channel/service-name/custom-suffix</code> where <code>service-name</code> represents the name of the Amazon Web Services service that created the channel and <code>custom-suffix</code> represents the suffix generated by the Amazon Web Services service. </p>"}, "Source": {"shape": "Source", "documentation": "<p>The source for the CloudTrail channel.</p>"}, "SourceConfig": {"shape": "SourceConfig", "documentation": "<p> Provides information about the advanced event selectors configured for the channel, and whether the channel applies to all Regions or a single Region. </p>"}, "Destinations": {"shape": "Destinations", "documentation": "<p>The destinations for the channel. For channels created for integrations, the destinations are the event data stores that log events arriving through the channel. For service-linked channels, the destination is the Amazon Web Services service that created the service-linked channel to receive events.</p>"}, "IngestionStatus": {"shape": "IngestionStatus", "documentation": "<p>A table showing information about the most recent successful and failed attempts to ingest events.</p>"}}}, "GetDashboardRequest": {"type": "structure", "required": ["DashboardId"], "members": {"DashboardId": {"shape": "DashboardArn", "documentation": "<p> The name or ARN for the dashboard. </p>"}}}, "GetDashboardResponse": {"type": "structure", "members": {"DashboardArn": {"shape": "DashboardArn", "documentation": "<p> The ARN for the dashboard. </p>"}, "Type": {"shape": "DashboardType", "documentation": "<p> The type of dashboard. </p>"}, "Status": {"shape": "DashboardStatus", "documentation": "<p> The status of the dashboard. </p>"}, "Widgets": {"shape": "WidgetList", "documentation": "<p> An array of widgets for the dashboard. </p>"}, "RefreshSchedule": {"shape": "RefreshSchedule", "documentation": "<p> The refresh schedule for the dashboard, if configured. </p>"}, "CreatedTimestamp": {"shape": "Date", "documentation": "<p> The timestamp that shows when the dashboard was created. </p>"}, "UpdatedTimestamp": {"shape": "Date", "documentation": "<p> The timestamp that shows when the dashboard was last updated. </p>"}, "LastRefreshId": {"shape": "RefreshId", "documentation": "<p> The ID of the last dashboard refresh. </p>"}, "LastRefreshFailureReason": {"shape": "ErrorMessage", "documentation": "<p> Provides information about failures for the last scheduled refresh. </p>"}, "TerminationProtectionEnabled": {"shape": "TerminationProtectionEnabled", "documentation": "<p> Indicates whether termination protection is enabled for the dashboard. </p>"}}}, "GetEventConfigurationRequest": {"type": "structure", "members": {"EventDataStore": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) or ID suffix of the ARN of the event data store for which you want to retrieve event configuration settings.</p>"}}}, "GetEventConfigurationResponse": {"type": "structure", "members": {"EventDataStoreArn": {"shape": "EventDataStoreArn", "documentation": "<p>The Amazon Resource Name (ARN) or ID suffix of the ARN of the event data store for which the event configuration settings are returned.</p>"}, "MaxEventSize": {"shape": "MaxEventSize", "documentation": "<p>The maximum allowed size for events stored in the specified event data store.</p>"}, "ContextKeySelectors": {"shape": "ContextKeySelectors", "documentation": "<p>The list of context key selectors that are configured for the event data store.</p>"}}}, "GetEventDataStoreRequest": {"type": "structure", "required": ["EventDataStore"], "members": {"EventDataStore": {"shape": "EventDataStoreArn", "documentation": "<p>The ARN (or ID suffix of the ARN) of the event data store about which you want information.</p>"}}}, "GetEventDataStoreResponse": {"type": "structure", "members": {"EventDataStoreArn": {"shape": "EventDataStoreArn", "documentation": "<p>The event data store Amazon Resource Number (ARN).</p>"}, "Name": {"shape": "EventDataStoreName", "documentation": "<p>The name of the event data store.</p>"}, "Status": {"shape": "EventDataStoreStatus", "documentation": "<p>The status of an event data store.</p>"}, "AdvancedEventSelectors": {"shape": "AdvancedEventSelectors", "documentation": "<p>The advanced event selectors used to select events for the data store.</p>"}, "MultiRegionEnabled": {"shape": "Boolean", "documentation": "<p>Indicates whether the event data store includes events from all Regions, or only from the Region in which it was created.</p>"}, "OrganizationEnabled": {"shape": "Boolean", "documentation": "<p>Indicates whether an event data store is collecting logged events for an organization in Organizations.</p>"}, "RetentionPeriod": {"shape": "RetentionPeriod", "documentation": "<p>The retention period of the event data store, in days.</p>"}, "TerminationProtectionEnabled": {"shape": "TerminationProtectionEnabled", "documentation": "<p>Indicates that termination protection is enabled.</p>"}, "CreatedTimestamp": {"shape": "Date", "documentation": "<p>The timestamp of the event data store's creation.</p>"}, "UpdatedTimestamp": {"shape": "Date", "documentation": "<p>Shows the time that an event data store was updated, if applicable. <code>UpdatedTimestamp</code> is always either the same or newer than the time shown in <code>CreatedTimestamp</code>.</p>"}, "KmsKeyId": {"shape": "EventDataStoreKmsKeyId", "documentation": "<p>Specifies the KMS key ID that encrypts the events delivered by CloudTrail. The value is a fully specified ARN to a KMS key in the following format.</p> <p> <code>arn:aws:kms:us-east-2:************:key/********-1234-1234-1234-************</code> </p>"}, "BillingMode": {"shape": "BillingMode", "documentation": "<p>The billing mode for the event data store.</p>"}, "FederationStatus": {"shape": "FederationStatus", "documentation": "<p> Indicates the <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/query-federation.html\">Lake query federation</a> status. The status is <code>ENABLED</code> if Lake query federation is enabled, or <code>DISABLED</code> if Lake query federation is disabled. You cannot delete an event data store if the <code>FederationStatus</code> is <code>ENABLED</code>. </p>"}, "FederationRoleArn": {"shape": "FederationRoleArn", "documentation": "<p> If Lake query federation is enabled, provides the ARN of the federation role used to access the resources for the federated event data store. </p>"}, "PartitionKeys": {"shape": "PartitionKeyList", "documentation": "<p>The partition keys for the event data store. To improve query performance and efficiency, CloudTrail Lake organizes event data into partitions based on values derived from partition keys.</p>"}}}, "GetEventSelectorsRequest": {"type": "structure", "required": ["TrailName"], "members": {"TrailName": {"shape": "String", "documentation": "<p>Specifies the name of the trail or trail ARN. If you specify a trail name, the string must meet the following requirements:</p> <ul> <li> <p>Contain only ASCII letters (a-z, A-Z), numbers (0-9), periods (.), underscores (_), or dashes (-)</p> </li> <li> <p>Start with a letter or number, and end with a letter or number</p> </li> <li> <p>Be between 3 and 128 characters</p> </li> <li> <p>Have no adjacent periods, underscores or dashes. Names like <code>my-_namespace</code> and <code>my--namespace</code> are not valid.</p> </li> <li> <p>Not be in IP address format (for example, ***********)</p> </li> </ul> <p>If you specify a trail ARN, it must be in the format:</p> <p> <code>arn:aws:cloudtrail:us-east-2:************:trail/MyTrail</code> </p>"}}}, "GetEventSelectorsResponse": {"type": "structure", "members": {"TrailARN": {"shape": "String", "documentation": "<p>The specified trail ARN that has the event selectors.</p>"}, "EventSelectors": {"shape": "EventSelectors", "documentation": "<p>The event selectors that are configured for the trail.</p>"}, "AdvancedEventSelectors": {"shape": "AdvancedEventSelectors", "documentation": "<p> The advanced event selectors that are configured for the trail. </p>"}}}, "GetImportRequest": {"type": "structure", "required": ["ImportId"], "members": {"ImportId": {"shape": "UUID", "documentation": "<p> The ID for the import. </p>"}}}, "GetImportResponse": {"type": "structure", "members": {"ImportId": {"shape": "UUID", "documentation": "<p> The ID of the import. </p>"}, "Destinations": {"shape": "ImportDestinations", "documentation": "<p> The ARN of the destination event data store. </p>"}, "ImportSource": {"shape": "ImportSource", "documentation": "<p> The source S3 bucket. </p>"}, "StartEventTime": {"shape": "Date", "documentation": "<p> Used with <code>EndEventTime</code> to bound a <code>StartImport</code> request, and limit imported trail events to only those events logged within a specified time period. </p>"}, "EndEventTime": {"shape": "Date", "documentation": "<p> Used with <code>StartEventTime</code> to bound a <code>StartImport</code> request, and limit imported trail events to only those events logged within a specified time period. </p>"}, "ImportStatus": {"shape": "ImportStatus", "documentation": "<p> The status of the import. </p>"}, "CreatedTimestamp": {"shape": "Date", "documentation": "<p> The timestamp of the import's creation. </p>"}, "UpdatedTimestamp": {"shape": "Date", "documentation": "<p> The timestamp of when the import was updated. </p>"}, "ImportStatistics": {"shape": "ImportStatistics", "documentation": "<p> Provides statistics for the import. CloudTrail does not update import statistics in real-time. Returned values for parameters such as <code>EventsCompleted</code> may be lower than the actual value, because CloudTrail updates statistics incrementally over the course of the import. </p>"}}}, "GetInsightSelectorsRequest": {"type": "structure", "members": {"TrailName": {"shape": "String", "documentation": "<p>Specifies the name of the trail or trail ARN. If you specify a trail name, the string must meet the following requirements:</p> <ul> <li> <p>Contain only ASCII letters (a-z, A-Z), numbers (0-9), periods (.), underscores (_), or dashes (-)</p> </li> <li> <p>Start with a letter or number, and end with a letter or number</p> </li> <li> <p>Be between 3 and 128 characters</p> </li> <li> <p>Have no adjacent periods, underscores or dashes. Names like <code>my-_namespace</code> and <code>my--namespace</code> are not valid.</p> </li> <li> <p>Not be in IP address format (for example, ***********)</p> </li> </ul> <p>If you specify a trail ARN, it must be in the format:</p> <p> <code>arn:aws:cloudtrail:us-east-2:************:trail/MyTrail</code> </p> <p>You cannot use this parameter with the <code>EventDataStore</code> parameter.</p>"}, "EventDataStore": {"shape": "EventDataStoreArn", "documentation": "<p> Specifies the ARN (or ID suffix of the ARN) of the event data store for which you want to get Insights selectors. </p> <p>You cannot use this parameter with the <code>TrailName</code> parameter.</p>"}}}, "GetInsightSelectorsResponse": {"type": "structure", "members": {"TrailARN": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of a trail for which you want to get Insights selectors.</p>"}, "InsightSelectors": {"shape": "InsightSelectors", "documentation": "<p>A JSON string that contains the Insight types you want to log on a trail or event data store. <code>ApiErrorRateInsight</code> and <code>ApiCallRateInsight</code> are supported as Insights types.</p>"}, "EventDataStoreArn": {"shape": "EventDataStoreArn", "documentation": "<p> The ARN of the source event data store that enabled Insights events. </p>"}, "InsightsDestination": {"shape": "EventDataStoreArn", "documentation": "<p> The ARN of the destination event data store that logs Insights events. </p>"}}}, "GetQueryResultsRequest": {"type": "structure", "required": ["QueryId"], "members": {"EventDataStore": {"shape": "EventDataStoreArn", "documentation": "<p>The ARN (or ID suffix of the ARN) of the event data store against which the query was run.</p>", "deprecated": true, "deprecatedMessage": "EventDataStore is no longer required by GetQueryResultsRequest"}, "QueryId": {"shape": "UUID", "documentation": "<p>The ID of the query for which you want to get results.</p>"}, "NextToken": {"shape": "PaginationToken", "documentation": "<p>A token you can use to get the next page of query results.</p>"}, "MaxQueryResults": {"shape": "MaxQueryResults", "documentation": "<p>The maximum number of query results to display on a single page.</p>"}, "EventDataStoreOwnerAccountId": {"shape": "AccountId", "documentation": "<p> The account ID of the event data store owner. </p>"}}}, "GetQueryResultsResponse": {"type": "structure", "members": {"QueryStatus": {"shape": "QueryStatus", "documentation": "<p>The status of the query. Values include <code>QUEUED</code>, <code>RUNNING</code>, <code>FINISHED</code>, <code>FAILED</code>, <code>TIMED_OUT</code>, or <code>CANCELLED</code>.</p>"}, "QueryStatistics": {"shape": "QueryStatistics", "documentation": "<p>Shows the count of query results.</p>"}, "QueryResultRows": {"shape": "QueryResultRows", "documentation": "<p>Contains the individual event results of the query.</p>"}, "NextToken": {"shape": "PaginationToken", "documentation": "<p>A token you can use to get the next page of query results.</p>"}, "ErrorMessage": {"shape": "ErrorMessage", "documentation": "<p>The error message returned if a query failed.</p>"}}}, "GetResourcePolicyRequest": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "ResourceArn", "documentation": "<p> The Amazon Resource Name (ARN) of the CloudTrail event data store, dashboard, or channel attached to the resource-based policy.</p> <p>Example event data store ARN format: <code>arn:aws:cloudtrail:us-east-2:************:eventdatastore/EXAMPLE-f852-4e8f-8bd1-bcf6cEXAMPLE</code> </p> <p>Example dashboard ARN format: <code>arn:aws:cloudtrail:us-east-1:************:dashboard/exampleDash</code> </p> <p>Example channel ARN format: <code>arn:aws:cloudtrail:us-east-2:************:channel/***********</code> </p>"}}}, "GetResourcePolicyResponse": {"type": "structure", "members": {"ResourceArn": {"shape": "ResourceArn", "documentation": "<p> The Amazon Resource Name (ARN) of the CloudTrail event data store, dashboard, or channel attached to resource-based policy. </p> <p>Example event data store ARN format: <code>arn:aws:cloudtrail:us-east-2:************:eventdatastore/EXAMPLE-f852-4e8f-8bd1-bcf6cEXAMPLE</code> </p> <p>Example dashboard ARN format: <code>arn:aws:cloudtrail:us-east-1:************:dashboard/exampleDash</code> </p> <p>Example channel ARN format: <code>arn:aws:cloudtrail:us-east-2:************:channel/***********</code> </p>"}, "ResourcePolicy": {"shape": "ResourcePolicy", "documentation": "<p> A JSON-formatted string that contains the resource-based policy attached to the CloudTrail event data store, dashboard, or channel. </p>"}, "DelegatedAdminResourcePolicy": {"shape": "ResourcePolicy", "documentation": "<p> The default resource-based policy that is automatically generated for the delegated administrator of an Organizations organization. This policy will be evaluated in tandem with any policy you submit for the resource. For more information about this policy, see <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/cloudtrail-lake-organizations.html#cloudtrail-lake-organizations-eds-rbp\">Default resource policy for delegated administrators</a>. </p>"}}}, "GetTrailRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "String", "documentation": "<p>The name or the Amazon Resource Name (ARN) of the trail for which you want to retrieve settings information.</p>"}}}, "GetTrailResponse": {"type": "structure", "members": {"Trail": {"shape": "Trail"}}}, "GetTrailStatusRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "String", "documentation": "<p>Specifies the name or the CloudTrail ARN of the trail for which you are requesting status. To get the status of a shadow trail (a replication of the trail in another Region), you must specify its ARN.</p> <p> The following is the format of a trail ARN: <code>arn:aws:cloudtrail:us-east-2:************:trail/MyTrail</code> </p> <note> <p>If the trail is an organization trail and you are a member account in the organization in Organizations, you must provide the full ARN of that trail, and not just the name.</p> </note>"}}, "documentation": "<p>The name of a trail about which you want the current status.</p>"}, "GetTrailStatusResponse": {"type": "structure", "members": {"IsLogging": {"shape": "Boolean", "documentation": "<p>Whether the CloudTrail trail is currently logging Amazon Web Services API calls.</p>"}, "LatestDeliveryError": {"shape": "String", "documentation": "<p>Displays any Amazon S3 error that CloudTrail encountered when attempting to deliver log files to the designated bucket. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/ErrorResponses.html\">Error Responses</a> in the Amazon S3 API Reference. </p> <note> <p>This error occurs only when there is a problem with the destination S3 bucket, and does not occur for requests that time out. To resolve the issue, fix the <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/create-s3-bucket-policy-for-cloudtrail.html\">bucket policy</a> so that CloudTrail can write to the bucket; or create a new bucket and call <code>UpdateTrail</code> to specify the new bucket.</p> </note>"}, "LatestNotificationError": {"shape": "String", "documentation": "<p>Displays any Amazon SNS error that CloudTrail encountered when attempting to send a notification. For more information about Amazon SNS errors, see the <a href=\"https://docs.aws.amazon.com/sns/latest/dg/welcome.html\">Amazon SNS Developer Guide</a>. </p>"}, "LatestDeliveryTime": {"shape": "Date", "documentation": "<p>Specifies the date and time that CloudTrail last delivered log files to an account's Amazon S3 bucket.</p>"}, "LatestNotificationTime": {"shape": "Date", "documentation": "<p>Specifies the date and time of the most recent Amazon SNS notification that CloudTrail has written a new log file to an account's Amazon S3 bucket.</p>"}, "StartLoggingTime": {"shape": "Date", "documentation": "<p>Specifies the most recent date and time when CloudTrail started recording API calls for an Amazon Web Services account.</p>"}, "StopLoggingTime": {"shape": "Date", "documentation": "<p>Specifies the most recent date and time when CloudTrail stopped recording API calls for an Amazon Web Services account.</p>"}, "LatestCloudWatchLogsDeliveryError": {"shape": "String", "documentation": "<p>Displays any CloudWatch Logs error that CloudTrail encountered when attempting to deliver logs to CloudWatch Logs.</p>"}, "LatestCloudWatchLogsDeliveryTime": {"shape": "Date", "documentation": "<p>Displays the most recent date and time when CloudTrail delivered logs to CloudWatch Logs.</p>"}, "LatestDigestDeliveryTime": {"shape": "Date", "documentation": "<p>Specifies the date and time that CloudTrail last delivered a digest file to an account's Amazon S3 bucket.</p>"}, "LatestDigestDeliveryError": {"shape": "String", "documentation": "<p>Displays any Amazon S3 error that CloudTrail encountered when attempting to deliver a digest file to the designated bucket. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/API/ErrorResponses.html\">Error Responses</a> in the Amazon S3 API Reference. </p> <note> <p>This error occurs only when there is a problem with the destination S3 bucket, and does not occur for requests that time out. To resolve the issue, fix the <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/create-s3-bucket-policy-for-cloudtrail.html\">bucket policy</a> so that CloudTrail can write to the bucket; or create a new bucket and call <code>UpdateTrail</code> to specify the new bucket.</p> </note>"}, "LatestDeliveryAttemptTime": {"shape": "String", "documentation": "<p>This field is no longer in use.</p>"}, "LatestNotificationAttemptTime": {"shape": "String", "documentation": "<p>This field is no longer in use.</p>"}, "LatestNotificationAttemptSucceeded": {"shape": "String", "documentation": "<p>This field is no longer in use.</p>"}, "LatestDeliveryAttemptSucceeded": {"shape": "String", "documentation": "<p>This field is no longer in use.</p>"}, "TimeLoggingStarted": {"shape": "String", "documentation": "<p>This field is no longer in use.</p>"}, "TimeLoggingStopped": {"shape": "String", "documentation": "<p>This field is no longer in use.</p>"}}, "documentation": "<p>Returns the objects or data listed below if successful. Otherwise, returns an error.</p>"}, "ImportDestinations": {"type": "list", "member": {"shape": "EventDataStoreArn"}, "max": 1, "min": 1}, "ImportFailureList": {"type": "list", "member": {"shape": "ImportFailureListItem"}}, "ImportFailureListItem": {"type": "structure", "members": {"Location": {"shape": "String", "documentation": "<p> The location of the failure in the S3 bucket. </p>"}, "Status": {"shape": "ImportFailureStatus", "documentation": "<p> The status of the import. </p>"}, "ErrorType": {"shape": "String", "documentation": "<p> The type of import error. </p>"}, "ErrorMessage": {"shape": "String", "documentation": "<p> Provides the reason the import failed. </p>"}, "LastUpdatedTime": {"shape": "Date", "documentation": "<p> When the import was last updated. </p>"}}, "documentation": "<p> Provides information about an import failure. </p>"}, "ImportFailureStatus": {"type": "string", "enum": ["FAILED", "RETRY", "SUCCEEDED"]}, "ImportNotFoundException": {"type": "structure", "members": {}, "documentation": "<p> The specified import was not found. </p>", "exception": true}, "ImportSource": {"type": "structure", "required": ["S3"], "members": {"S3": {"shape": "S3ImportSource", "documentation": "<p> The source S3 bucket. </p>"}}, "documentation": "<p> The import source. </p>"}, "ImportStatistics": {"type": "structure", "members": {"PrefixesFound": {"shape": "<PERSON>", "documentation": "<p> The number of S3 prefixes found for the import. </p>"}, "PrefixesCompleted": {"shape": "<PERSON>", "documentation": "<p> The number of S3 prefixes that completed import. </p>"}, "FilesCompleted": {"shape": "<PERSON>", "documentation": "<p>The number of log files that completed import.</p>"}, "EventsCompleted": {"shape": "<PERSON>", "documentation": "<p> The number of trail events imported into the event data store. </p>"}, "FailedEntries": {"shape": "<PERSON>", "documentation": "<p> The number of failed entries. </p>"}}, "documentation": "<p> Provides statistics for the specified <code>ImportID</code>. CloudTrail does not update import statistics in real-time. Returned values for parameters such as <code>EventsCompleted</code> may be lower than the actual value, because CloudTrail updates statistics incrementally over the course of the import. </p>"}, "ImportStatus": {"type": "string", "enum": ["INITIALIZING", "IN_PROGRESS", "FAILED", "STOPPED", "COMPLETED"]}, "ImportsList": {"type": "list", "member": {"shape": "ImportsListItem"}}, "ImportsListItem": {"type": "structure", "members": {"ImportId": {"shape": "UUID", "documentation": "<p> The ID of the import. </p>"}, "ImportStatus": {"shape": "ImportStatus", "documentation": "<p> The status of the import. </p>"}, "Destinations": {"shape": "ImportDestinations", "documentation": "<p> The ARN of the destination event data store. </p>"}, "CreatedTimestamp": {"shape": "Date", "documentation": "<p> The timestamp of the import's creation. </p>"}, "UpdatedTimestamp": {"shape": "Date", "documentation": "<p> The timestamp of the import's last update. </p>"}}, "documentation": "<p> Contains information about an import that was returned by a lookup request. </p>"}, "InactiveEventDataStoreException": {"type": "structure", "members": {}, "documentation": "<p>The event data store is inactive.</p>", "exception": true}, "InactiveQueryException": {"type": "structure", "members": {}, "documentation": "<p>The specified query cannot be canceled because it is in the <code>FINISHED</code>, <code>FAILED</code>, <code>TIMED_OUT</code>, or <code>CANCELLED</code> state.</p>", "exception": true}, "IngestionStatus": {"type": "structure", "members": {"LatestIngestionSuccessTime": {"shape": "Date", "documentation": "<p>The time stamp of the most recent successful ingestion of events for the channel.</p>"}, "LatestIngestionSuccessEventID": {"shape": "UUID", "documentation": "<p>The event ID of the most recent successful ingestion of events.</p>"}, "LatestIngestionErrorCode": {"shape": "ErrorMessage", "documentation": "<p>The error code for the most recent failure to ingest events.</p>"}, "LatestIngestionAttemptTime": {"shape": "Date", "documentation": "<p>The time stamp of the most recent attempt to ingest events on the channel.</p>"}, "LatestIngestionAttemptEventID": {"shape": "UUID", "documentation": "<p>The event ID of the most recent attempt to ingest events.</p>"}}, "documentation": "<p>A table showing information about the most recent successful and failed attempts to ingest events.</p>"}, "InsightNotEnabledException": {"type": "structure", "members": {}, "documentation": "<p>If you run <code>GetInsightSelectors</code> on a trail or event data store that does not have Insights events enabled, the operation throws the exception <code>InsightNotEnabledException</code>.</p>", "exception": true}, "InsightSelector": {"type": "structure", "members": {"InsightType": {"shape": "InsightType", "documentation": "<p>The type of Insights events to log on a trail or event data store. <code>ApiCallRateInsight</code> and <code>ApiErrorRateInsight</code> are valid Insight types.</p> <p>The <code>ApiCallRateInsight</code> Insights type analyzes write-only management API calls that are aggregated per minute against a baseline API call volume.</p> <p>The <code>ApiErrorRateInsight</code> Insights type analyzes management API calls that result in error codes. The error is shown if the API call is unsuccessful.</p>"}}, "documentation": "<p>A JSON string that contains a list of Insights types that are logged on a trail or event data store.</p>"}, "InsightSelectors": {"type": "list", "member": {"shape": "InsightSelector"}}, "InsightType": {"type": "string", "enum": ["ApiCallRateInsight", "ApiErrorRateInsight"]}, "InsightsMetricDataType": {"type": "string", "enum": ["FillWithZeros", "NonZeroData"]}, "InsightsMetricMaxResults": {"type": "integer", "max": 21600, "min": 1}, "InsightsMetricNextToken": {"type": "string", "max": 5000, "min": 1}, "InsightsMetricPeriod": {"type": "integer", "max": 3600, "min": 60}, "InsightsMetricValues": {"type": "list", "member": {"shape": "Double"}}, "InsufficientDependencyServiceAccessPermissionException": {"type": "structure", "members": {}, "documentation": "<p>This exception is thrown when the IAM identity that is used to create the organization resource lacks one or more required permissions for creating an organization resource in a required service.</p>", "exception": true}, "InsufficientEncryptionPolicyException": {"type": "structure", "members": {}, "documentation": "<p>For the <code>CreateTrail</code> <code>PutInsightSelectors</code>, <code>UpdateTrail</code>, <code>StartQuery</code>, and <code>StartImport</code> operations, this exception is thrown when the policy on the S3 bucket or KMS key does not have sufficient permissions for the operation.</p> <p>For all other operations, this exception is thrown when the policy for the KMS key does not have sufficient permissions for the operation.</p>", "exception": true}, "InsufficientIAMAccessPermissionException": {"type": "structure", "members": {}, "documentation": "<p>The task can't be completed because you are signed in with an account that lacks permissions to view or create a service-linked role. Sign in with an account that has the required permissions and then try again.</p>", "exception": true}, "InsufficientS3BucketPolicyException": {"type": "structure", "members": {}, "documentation": "<p>This exception is thrown when the policy on the S3 bucket is not sufficient.</p>", "exception": true}, "InsufficientSnsTopicPolicyException": {"type": "structure", "members": {}, "documentation": "<p>This exception is thrown when the policy on the Amazon SNS topic is not sufficient.</p>", "exception": true}, "Integer": {"type": "integer"}, "InvalidCloudWatchLogsLogGroupArnException": {"type": "structure", "members": {}, "documentation": "<p>This exception is thrown when the provided CloudWatch Logs log group is not valid.</p>", "exception": true}, "InvalidCloudWatchLogsRoleArnException": {"type": "structure", "members": {}, "documentation": "<p>This exception is thrown when the provided role is not valid.</p>", "exception": true}, "InvalidDateRangeException": {"type": "structure", "members": {}, "documentation": "<p>A date range for the query was specified that is not valid. Be sure that the start time is chronologically before the end time. For more information about writing a query, see <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/query-create-edit-query.html\">Create or edit a query</a> in the <i>CloudTrail User Guide</i>.</p>", "exception": true}, "InvalidEventCategoryException": {"type": "structure", "members": {}, "documentation": "<p>Occurs if an event category that is not valid is specified as a value of <code>EventCategory</code>.</p>", "exception": true}, "InvalidEventDataStoreCategoryException": {"type": "structure", "members": {}, "documentation": "<p>This exception is thrown when event categories of specified event data stores are not valid.</p>", "exception": true}, "InvalidEventDataStoreStatusException": {"type": "structure", "members": {}, "documentation": "<p>The event data store is not in a status that supports the operation.</p>", "exception": true}, "InvalidEventSelectorsException": {"type": "structure", "members": {}, "documentation": "<p>This exception is thrown when the <code>PutEventSelectors</code> operation is called with a number of event selectors, advanced event selectors, or data resources that is not valid. The combination of event selectors or advanced event selectors and data resources is not valid. A trail can have up to 5 event selectors. If a trail uses advanced event selectors, a maximum of 500 total values for all conditions in all advanced event selectors is allowed. A trail is limited to 250 data resources. These data resources can be distributed across event selectors, but the overall total cannot exceed 250.</p> <p>You can:</p> <ul> <li> <p>Specify a valid number of event selectors (1 to 5) for a trail.</p> </li> <li> <p>Specify a valid number of data resources (1 to 250) for an event selector. The limit of number of resources on an individual event selector is configurable up to 250. However, this upper limit is allowed only if the total number of data resources does not exceed 250 across all event selectors for a trail.</p> </li> <li> <p>Specify up to 500 values for all conditions in all advanced event selectors for a trail.</p> </li> <li> <p>Specify a valid value for a parameter. For example, specifying the <code>ReadWriteType</code> parameter with a value of <code>read-only</code> is not valid.</p> </li> </ul>", "exception": true}, "InvalidHomeRegionException": {"type": "structure", "members": {}, "documentation": "<p>This exception is thrown when an operation is called on a trail from a Region other than the Region in which the trail was created.</p>", "exception": true}, "InvalidImportSourceException": {"type": "structure", "members": {}, "documentation": "<p> This exception is thrown when the provided source S3 bucket is not valid for import. </p>", "exception": true}, "InvalidInsightSelectorsException": {"type": "structure", "members": {}, "documentation": "<p>For <code>PutInsightSelectors</code>, this exception is thrown when the formatting or syntax of the <code>InsightSelectors</code> JSON statement is not valid, or the specified <code>InsightType</code> in the <code>InsightSelectors</code> statement is not valid. Valid values for <code>InsightType</code> are <code>ApiCallRateInsight</code> and <code>ApiErrorRateInsight</code>. To enable Insights on an event data store, the destination event data store specified by the <code>InsightsDestination</code> parameter must log Insights events and the source event data store specified by the <code>EventDataStore</code> parameter must log management events.</p> <p>For <code>UpdateEventDataStore</code>, this exception is thrown if Insights are enabled on the event data store and the updated advanced event selectors are not compatible with the configured <code>InsightSelectors</code>. If the <code>InsightSelectors</code> includes an <code>InsightType</code> of <code>ApiCallRateInsight</code>, the source event data store must log <code>write</code> management events. If the <code>InsightSelectors</code> includes an <code>InsightType</code> of <code>ApiErrorRateInsight</code>, the source event data store must log management events.</p>", "exception": true}, "InvalidKmsKeyIdException": {"type": "structure", "members": {}, "documentation": "<p>This exception is thrown when the KMS key ARN is not valid.</p>", "exception": true}, "InvalidLookupAttributesException": {"type": "structure", "members": {}, "documentation": "<p>Occurs when a lookup attribute is specified that is not valid.</p>", "exception": true}, "InvalidMaxResultsException": {"type": "structure", "members": {}, "documentation": "<p>This exception is thrown if the limit specified is not valid.</p>", "exception": true}, "InvalidNextTokenException": {"type": "structure", "members": {}, "documentation": "<p>A token that is not valid, or a token that was previously used in a request with different parameters. This exception is thrown if the token is not valid.</p>", "exception": true}, "InvalidParameterCombinationException": {"type": "structure", "members": {}, "documentation": "<p>This exception is thrown when the combination of parameters provided is not valid.</p>", "exception": true}, "InvalidParameterException": {"type": "structure", "members": {}, "documentation": "<p>The request includes a parameter that is not valid.</p>", "exception": true}, "InvalidQueryStatementException": {"type": "structure", "members": {}, "documentation": "<p>The query that was submitted has validation errors, or uses incorrect syntax or unsupported keywords. For more information about writing a query, see <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/query-create-edit-query.html\">Create or edit a query</a> in the <i>CloudTrail User Guide</i>.</p>", "exception": true}, "InvalidQueryStatusException": {"type": "structure", "members": {}, "documentation": "<p>The query status is not valid for the operation.</p>", "exception": true}, "InvalidS3BucketNameException": {"type": "structure", "members": {}, "documentation": "<p>This exception is thrown when the provided S3 bucket name is not valid.</p>", "exception": true}, "InvalidS3PrefixException": {"type": "structure", "members": {}, "documentation": "<p>This exception is thrown when the provided S3 prefix is not valid.</p>", "exception": true}, "InvalidSnsTopicNameException": {"type": "structure", "members": {}, "documentation": "<p>This exception is thrown when the provided SNS topic name is not valid.</p>", "exception": true}, "InvalidSourceException": {"type": "structure", "members": {}, "documentation": "<p>This exception is thrown when the specified value of <code>Source</code> is not valid.</p>", "exception": true}, "InvalidTagParameterException": {"type": "structure", "members": {}, "documentation": "<p>This exception is thrown when the specified tag key or values are not valid. It can also occur if there are duplicate tags or too many tags on the resource.</p>", "exception": true}, "InvalidTimeRangeException": {"type": "structure", "members": {}, "documentation": "<p>Occurs if the timestamp values are not valid. Either the start time occurs after the end time, or the time range is outside the range of possible values.</p>", "exception": true}, "InvalidTokenException": {"type": "structure", "members": {}, "documentation": "<p>Reserved for future use.</p>", "exception": true}, "InvalidTrailNameException": {"type": "structure", "members": {}, "documentation": "<p>This exception is thrown when the provided trail name is not valid. Trail names must meet the following requirements:</p> <ul> <li> <p>Contain only ASCII letters (a-z, A-Z), numbers (0-9), periods (.), underscores (_), or dashes (-)</p> </li> <li> <p>Start with a letter or number, and end with a letter or number</p> </li> <li> <p>Be between 3 and 128 characters</p> </li> <li> <p>Have no adjacent periods, underscores or dashes. Names like <code>my-_namespace</code> and <code>my--namespace</code> are not valid.</p> </li> <li> <p>Not be in IP address format (for example, ***********)</p> </li> </ul>", "exception": true}, "KmsException": {"type": "structure", "members": {}, "documentation": "<p>This exception is thrown when there is an issue with the specified KMS key and the trail or event data store can't be updated.</p>", "exception": true}, "KmsKeyDisabledException": {"type": "structure", "members": {}, "documentation": "<p>This exception is no longer in use.</p>", "deprecated": true, "exception": true}, "KmsKeyNotFoundException": {"type": "structure", "members": {}, "documentation": "<p>This exception is thrown when the KMS key does not exist, when the S3 bucket and the KMS key are not in the same Region, or when the KMS key associated with the Amazon SNS topic either does not exist or is not in the same Region.</p>", "exception": true}, "ListChannelsMaxResultsCount": {"type": "integer", "max": 1000, "min": 1}, "ListChannelsRequest": {"type": "structure", "members": {"MaxResults": {"shape": "ListChannelsMaxResultsCount", "documentation": "<p> The maximum number of CloudTrail channels to display on a single page. </p>"}, "NextToken": {"shape": "PaginationToken", "documentation": "<p>The token to use to get the next page of results after a previous API call. This token must be passed in with the same parameters that were specified in the original call. For example, if the original call specified an AttributeKey of '<PERSON>rname' with a value of 'root', the call with NextToken should include those same parameters.</p>"}}}, "ListChannelsResponse": {"type": "structure", "members": {"Channels": {"shape": "Channels", "documentation": "<p> The list of channels in the account. </p>"}, "NextToken": {"shape": "PaginationToken", "documentation": "<p>The token to use to get the next page of results after a previous API call.</p>"}}}, "ListDashboardsMaxResultsCount": {"type": "integer", "max": 1000, "min": 1}, "ListDashboardsRequest": {"type": "structure", "members": {"NamePrefix": {"shape": "DashboardName", "documentation": "<p> Specify a name prefix to filter on. </p>"}, "Type": {"shape": "DashboardType", "documentation": "<p> Specify a dashboard type to filter on: <code>CUSTOM</code> or <code>MANAGED</code>. </p>"}, "NextToken": {"shape": "PaginationToken", "documentation": "<p> A token you can use to get the next page of dashboard results. </p>"}, "MaxResults": {"shape": "ListDashboardsMaxResultsCount", "documentation": "<p> The maximum number of dashboards to display on a single page. </p>"}}}, "ListDashboardsResponse": {"type": "structure", "members": {"Dashboards": {"shape": "Dashboards", "documentation": "<p> Contains information about dashboards in the account, in the current Region that match the applied filters. </p>"}, "NextToken": {"shape": "PaginationToken", "documentation": "<p> A token you can use to get the next page of dashboard results. </p>"}}}, "ListEventDataStoresMaxResultsCount": {"type": "integer", "max": 1000, "min": 1}, "ListEventDataStoresRequest": {"type": "structure", "members": {"NextToken": {"shape": "PaginationToken", "documentation": "<p>A token you can use to get the next page of event data store results.</p>"}, "MaxResults": {"shape": "ListEventDataStoresMaxResultsCount", "documentation": "<p>The maximum number of event data stores to display on a single page.</p>"}}}, "ListEventDataStoresResponse": {"type": "structure", "members": {"EventDataStores": {"shape": "EventDataStores", "documentation": "<p>Contains information about event data stores in the account, in the current Region.</p>"}, "NextToken": {"shape": "PaginationToken", "documentation": "<p>A token you can use to get the next page of results.</p>"}}}, "ListImportFailuresMaxResultsCount": {"type": "integer", "max": 1000, "min": 1}, "ListImportFailuresRequest": {"type": "structure", "required": ["ImportId"], "members": {"ImportId": {"shape": "UUID", "documentation": "<p> The ID of the import. </p>"}, "MaxResults": {"shape": "ListImportFailuresMaxResultsCount", "documentation": "<p> The maximum number of failures to display on a single page. </p>"}, "NextToken": {"shape": "PaginationToken", "documentation": "<p> A token you can use to get the next page of import failures. </p>"}}}, "ListImportFailuresResponse": {"type": "structure", "members": {"Failures": {"shape": "ImportFailureList", "documentation": "<p> Contains information about the import failures. </p>"}, "NextToken": {"shape": "PaginationToken", "documentation": "<p> A token you can use to get the next page of results. </p>"}}}, "ListImportsMaxResultsCount": {"type": "integer", "max": 1000, "min": 1}, "ListImportsRequest": {"type": "structure", "members": {"MaxResults": {"shape": "ListImportsMaxResultsCount", "documentation": "<p> The maximum number of imports to display on a single page. </p>"}, "Destination": {"shape": "EventDataStoreArn", "documentation": "<p> The ARN of the destination event data store. </p>"}, "ImportStatus": {"shape": "ImportStatus", "documentation": "<p> The status of the import. </p>"}, "NextToken": {"shape": "PaginationToken", "documentation": "<p> A token you can use to get the next page of import results. </p>"}}}, "ListImportsResponse": {"type": "structure", "members": {"Imports": {"shape": "ImportsList", "documentation": "<p> The list of returned imports. </p>"}, "NextToken": {"shape": "PaginationToken", "documentation": "<p> A token you can use to get the next page of import results. </p>"}}}, "ListInsightsMetricDataRequest": {"type": "structure", "required": ["EventSource", "EventName", "InsightType"], "members": {"EventSource": {"shape": "EventSource", "documentation": "<p>The Amazon Web Services service to which the request was made, such as <code>iam.amazonaws.com</code> or <code>s3.amazonaws.com</code>.</p>"}, "EventName": {"shape": "EventName", "documentation": "<p>The name of the event, typically the Amazon Web Services API on which unusual levels of activity were recorded.</p>"}, "InsightType": {"shape": "InsightType", "documentation": "<p>The type of CloudTrail Insights event, which is either <code>ApiCallRateInsight</code> or <code>ApiErrorRateInsight</code>. The <code>ApiCallRateInsight</code> Insights type analyzes write-only management API calls that are aggregated per minute against a baseline API call volume. The <code>ApiErrorRateInsight</code> Insights type analyzes management API calls that result in error codes.</p>"}, "ErrorCode": {"shape": "ErrorCode", "documentation": "<p>Conditionally required if the <code>InsightType</code> parameter is set to <code>ApiErrorRateInsight</code>.</p> <p>If returning metrics for the <code>ApiErrorRateInsight</code> Insights type, this is the error to retrieve data for. For example, <code>AccessDenied</code>.</p>"}, "StartTime": {"shape": "Date", "documentation": "<p>Specifies, in UTC, the start time for time-series data. The value specified is inclusive; results include data points with the specified time stamp.</p> <p>The default is 90 days before the time of request.</p>"}, "EndTime": {"shape": "Date", "documentation": "<p>Specifies, in UTC, the end time for time-series data. The value specified is exclusive; results include data points up to the specified time stamp.</p> <p>The default is the time of request.</p>"}, "Period": {"shape": "InsightsMetricPeriod", "documentation": "<p>Granularity of data to retrieve, in seconds. Valid values are <code>60</code>, <code>300</code>, and <code>3600</code>. If you specify any other value, you will get an error. The default is 3600 seconds.</p>"}, "DataType": {"shape": "InsightsMetricDataType", "documentation": "<p>Type of data points to return. Valid values are <code>NonZeroData</code> and <code>FillWithZeros</code>. The default is <code>NonZeroData</code>.</p>"}, "MaxResults": {"shape": "InsightsMetricMaxResults", "documentation": "<p>The maximum number of data points to return. Valid values are integers from 1 to 21600. The default value is 21600.</p>"}, "NextToken": {"shape": "InsightsMetricNextToken", "documentation": "<p>Returned if all datapoints can't be returned in a single call. For example, due to reaching <code>MaxResults</code>.</p> <p>Add this parameter to the request to continue retrieving results starting from the last evaluated point.</p>"}}}, "ListInsightsMetricDataResponse": {"type": "structure", "members": {"EventSource": {"shape": "EventSource", "documentation": "<p>The Amazon Web Services service to which the request was made, such as <code>iam.amazonaws.com</code> or <code>s3.amazonaws.com</code>.</p>"}, "EventName": {"shape": "EventName", "documentation": "<p>The name of the event, typically the Amazon Web Services API on which unusual levels of activity were recorded.</p>"}, "InsightType": {"shape": "InsightType", "documentation": "<p>The type of CloudTrail Insights event, which is either <code>ApiCallRateInsight</code> or <code>ApiErrorRateInsight</code>. The <code>ApiCallRateInsight</code> Insights type analyzes write-only management API calls that are aggregated per minute against a baseline API call volume. The <code>ApiErrorRateInsight</code> Insights type analyzes management API calls that result in error codes.</p>"}, "ErrorCode": {"shape": "ErrorCode", "documentation": "<p>Only returned if <code>InsightType</code> parameter was set to <code>ApiErrorRateInsight</code>.</p> <p>If returning metrics for the <code>ApiErrorRateInsight</code> Insights type, this is the error to retrieve data for. For example, <code>AccessDenied</code>.</p>"}, "Timestamps": {"shape": "Timestamps", "documentation": "<p>List of timestamps at intervals corresponding to the specified time period.</p>"}, "Values": {"shape": "InsightsMetricValues", "documentation": "<p>List of values representing the API call rate or error rate at each timestamp. The number of values is equal to the number of timestamps.</p>"}, "NextToken": {"shape": "InsightsMetricNextToken", "documentation": "<p>Only returned if the full results could not be returned in a single query. You can set the <code>NextToken</code> parameter in the next request to this value to continue retrieval.</p>"}}}, "ListPublicKeysRequest": {"type": "structure", "members": {"StartTime": {"shape": "Date", "documentation": "<p>Optionally specifies, in UTC, the start of the time range to look up public keys for CloudTrail digest files. If not specified, the current time is used, and the current public key is returned.</p>"}, "EndTime": {"shape": "Date", "documentation": "<p>Optionally specifies, in UTC, the end of the time range to look up public keys for CloudTrail digest files. If not specified, the current time is used.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>Reserved for future use.</p>"}}, "documentation": "<p>Requests the public keys for a specified time range.</p>"}, "ListPublicKeysResponse": {"type": "structure", "members": {"PublicKeyList": {"shape": "PublicKeyList", "documentation": "<p>Contains an array of PublicKey objects.</p> <note> <p>The returned public keys may have validity time ranges that overlap.</p> </note>"}, "NextToken": {"shape": "String", "documentation": "<p>Reserved for future use.</p>"}}, "documentation": "<p>Returns the objects or data listed below if successful. Otherwise, returns an error.</p>"}, "ListQueriesMaxResultsCount": {"type": "integer", "max": 1000, "min": 1}, "ListQueriesRequest": {"type": "structure", "required": ["EventDataStore"], "members": {"EventDataStore": {"shape": "EventDataStoreArn", "documentation": "<p>The ARN (or the ID suffix of the ARN) of an event data store on which queries were run.</p>"}, "NextToken": {"shape": "PaginationToken", "documentation": "<p>A token you can use to get the next page of results.</p>"}, "MaxResults": {"shape": "ListQueriesMaxResultsCount", "documentation": "<p>The maximum number of queries to show on a page.</p>"}, "StartTime": {"shape": "Date", "documentation": "<p>Use with <code>EndTime</code> to bound a <code>ListQueries</code> request, and limit its results to only those queries run within a specified time period.</p>"}, "EndTime": {"shape": "Date", "documentation": "<p>Use with <code>StartTime</code> to bound a <code>ListQueries</code> request, and limit its results to only those queries run within a specified time period.</p>"}, "QueryStatus": {"shape": "QueryStatus", "documentation": "<p>The status of queries that you want to return in results. Valid values for <code>QueryStatus</code> include <code>QUEUED</code>, <code>RUNNING</code>, <code>FINISHED</code>, <code>FAILED</code>, <code>TIMED_OUT</code>, or <code>CANCELLED</code>.</p>"}}}, "ListQueriesResponse": {"type": "structure", "members": {"Queries": {"shape": "Queries", "documentation": "<p>Lists matching query results, and shows query ID, status, and creation time of each query.</p>"}, "NextToken": {"shape": "PaginationToken", "documentation": "<p>A token you can use to get the next page of results.</p>"}}}, "ListTagsRequest": {"type": "structure", "required": ["ResourceIdList"], "members": {"ResourceIdList": {"shape": "ResourceIdList", "documentation": "<p>Specifies a list of trail, event data store, dashboard, or channel ARNs whose tags will be listed. The list has a limit of 20 ARNs.</p> <p> Example trail ARN format: <code>arn:aws:cloudtrail:us-east-2:************:trail/MyTrail</code> </p> <p>Example event data store ARN format: <code>arn:aws:cloudtrail:us-east-2:************:eventdatastore/EXAMPLE-f852-4e8f-8bd1-bcf6cEXAMPLE</code> </p> <p>Example dashboard ARN format: <code>arn:aws:cloudtrail:us-east-1:************:dashboard/exampleDash</code> </p> <p>Example channel ARN format: <code>arn:aws:cloudtrail:us-east-2:************:channel/***********</code> </p>"}, "NextToken": {"shape": "String", "documentation": "<p>Reserved for future use.</p>"}}, "documentation": "<p>Specifies a list of tags to return.</p>"}, "ListTagsResponse": {"type": "structure", "members": {"ResourceTagList": {"shape": "ResourceTagList", "documentation": "<p>A list of resource tags.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>Reserved for future use.</p>"}}, "documentation": "<p>Returns the objects or data listed below if successful. Otherwise, returns an error.</p>"}, "ListTrailsRequest": {"type": "structure", "members": {"NextToken": {"shape": "String", "documentation": "<p>The token to use to get the next page of results after a previous API call. This token must be passed in with the same parameters that were specified in the original call. For example, if the original call specified an AttributeKey of '<PERSON>rname' with a value of 'root', the call with NextToken should include those same parameters.</p>"}}}, "ListTrailsResponse": {"type": "structure", "members": {"Trails": {"shape": "Trails", "documentation": "<p>Returns the name, ARN, and home Region of trails in the current account.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The token to use to get the next page of results after a previous API call. If the token does not appear, there are no more results to return. The token must be passed in with the same parameters as the previous call. For example, if the original call specified an AttributeKey of 'Username' with a value of 'root', the call with NextToken should include those same parameters.</p>"}}}, "Location": {"type": "string", "max": 1024, "min": 3, "pattern": "^[a-zA-Z0-9._/\\-:*]+$"}, "Long": {"type": "long"}, "LookupAttribute": {"type": "structure", "required": ["AttributeKey", "AttributeValue"], "members": {"AttributeKey": {"shape": "LookupAttributeKey", "documentation": "<p>Specifies an attribute on which to filter the events returned.</p>"}, "AttributeValue": {"shape": "LookupAttributeValue", "documentation": "<p>Specifies a value for the specified <code>AttributeKey</code>.</p> <p>The maximum length for the <code>AttributeValue</code> is 2000 characters. The following characters ('<code>_</code>', '<code> </code>', '<code>,</code>', '<code>\\\\n</code>') count as two characters towards the 2000 character limit.</p>"}}, "documentation": "<p>Specifies an attribute and value that filter the events returned.</p>"}, "LookupAttributeKey": {"type": "string", "enum": ["EventId", "EventName", "Read<PERSON>nly", "Username", "ResourceType", "ResourceName", "EventSource", "AccessKeyId"]}, "LookupAttributeValue": {"type": "string", "max": 2000, "min": 1}, "LookupAttributesList": {"type": "list", "member": {"shape": "LookupAttribute"}}, "LookupEventsRequest": {"type": "structure", "members": {"LookupAttributes": {"shape": "LookupAttributesList", "documentation": "<p>Contains a list of lookup attributes. Currently the list can contain only one item.</p>"}, "StartTime": {"shape": "Date", "documentation": "<p>Specifies that only events that occur after or at the specified time are returned. If the specified start time is after the specified end time, an error is returned.</p>"}, "EndTime": {"shape": "Date", "documentation": "<p>Specifies that only events that occur before or at the specified time are returned. If the specified end time is before the specified start time, an error is returned.</p>"}, "EventCategory": {"shape": "EventCategory", "documentation": "<p>Specifies the event category. If you do not specify an event category, events of the category are not returned in the response. For example, if you do not specify <code>insight</code> as the value of <code>EventCategory</code>, no Insights events are returned.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The number of events to return. Possible values are 1 through 50. The default is 50.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to use to get the next page of results after a previous API call. This token must be passed in with the same parameters that were specified in the original call. For example, if the original call specified an AttributeKey of '<PERSON>rname' with a value of 'root', the call with NextToken should include those same parameters.</p>"}}, "documentation": "<p>Contains a request for LookupEvents.</p>"}, "LookupEventsResponse": {"type": "structure", "members": {"Events": {"shape": "EventsList", "documentation": "<p>A list of events returned based on the lookup attributes specified and the CloudTrail event. The events list is sorted by time. The most recent event is listed first.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to use to get the next page of results after a previous API call. If the token does not appear, there are no more results to return. The token must be passed in with the same parameters as the previous call. For example, if the original call specified an AttributeKey of 'Username' with a value of 'root', the call with NextToken should include those same parameters.</p>"}}, "documentation": "<p>Contains a response to a LookupEvents action.</p>"}, "MaxConcurrentQueriesException": {"type": "structure", "members": {}, "documentation": "<p>You are already running the maximum number of concurrent queries. The maximum number of concurrent queries is 10. Wait a minute for some queries to finish, and then run the query again.</p>", "exception": true}, "MaxEventSize": {"type": "string", "enum": ["Standard", "Large"]}, "MaxQueryResults": {"type": "integer", "max": 1000, "min": 1}, "MaxResults": {"type": "integer", "max": 50, "min": 1}, "MaximumNumberOfTrailsExceededException": {"type": "structure", "members": {}, "documentation": "<p>This exception is thrown when the maximum number of trails is reached.</p>", "exception": true}, "NextToken": {"type": "string"}, "NoManagementAccountSLRExistsException": {"type": "structure", "members": {}, "documentation": "<p> This exception is thrown when the management account does not have a service-linked role. </p>", "exception": true}, "NotOrganizationManagementAccountException": {"type": "structure", "members": {}, "documentation": "<p> This exception is thrown when the account making the request is not the organization's management account. </p>", "exception": true}, "NotOrganizationMasterAccountException": {"type": "structure", "members": {}, "documentation": "<p>This exception is thrown when the Amazon Web Services account making the request to create or update an organization trail or event data store is not the management account for an organization in Organizations. For more information, see <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/creating-an-organizational-trail-prepare.html\">Prepare For Creating a Trail For Your Organization</a> or <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/cloudtrail-lake-organizations.html\">Organization event data stores</a>.</p>", "exception": true}, "OperationNotPermittedException": {"type": "structure", "members": {}, "documentation": "<p>This exception is thrown when the requested operation is not permitted.</p>", "exception": true}, "Operator": {"type": "list", "member": {"shape": "OperatorValue"}, "min": 1}, "OperatorTargetList": {"type": "list", "member": {"shape": "OperatorTargetListMember"}, "max": 50, "min": 1}, "OperatorTargetListMember": {"type": "string", "max": 128, "min": 1}, "OperatorValue": {"type": "string", "max": 2048, "min": 1, "pattern": ".+"}, "OrganizationNotInAllFeaturesModeException": {"type": "structure", "members": {}, "documentation": "<p>This exception is thrown when Organizations is not configured to support all features. All features must be enabled in Organizations to support creating an organization trail or event data store.</p>", "exception": true}, "OrganizationsNotInUseException": {"type": "structure", "members": {}, "documentation": "<p>This exception is thrown when the request is made from an Amazon Web Services account that is not a member of an organization. To make this request, sign in using the credentials of an account that belongs to an organization.</p>", "exception": true}, "PaginationToken": {"type": "string", "max": 1000, "min": 4, "pattern": ".*"}, "PartitionKey": {"type": "structure", "required": ["Name", "Type"], "members": {"Name": {"shape": "PartitionKeyName", "documentation": "<p>The name of the partition key.</p>"}, "Type": {"shape": "PartitionKeyType", "documentation": "<p>The data type of the partition key. For example, <code>bigint</code> or <code>string</code>.</p>"}}, "documentation": "<p>Contains information about a partition key for an event data store.</p>"}, "PartitionKeyList": {"type": "list", "member": {"shape": "PartitionKey"}, "max": 2}, "PartitionKeyName": {"type": "string", "max": 255, "min": 1, "pattern": "[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*"}, "PartitionKeyType": {"type": "string", "max": 255, "min": 0, "pattern": "[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*"}, "Prompt": {"type": "string", "max": 500, "min": 3, "pattern": "^[ -~\\n]*$"}, "PublicKey": {"type": "structure", "members": {"Value": {"shape": "ByteBuffer", "documentation": "<p>The DER encoded public key value in PKCS#1 format.</p>"}, "ValidityStartTime": {"shape": "Date", "documentation": "<p>The starting time of validity of the public key.</p>"}, "ValidityEndTime": {"shape": "Date", "documentation": "<p>The ending time of validity of the public key.</p>"}, "Fingerprint": {"shape": "String", "documentation": "<p>The fingerprint of the public key.</p>"}}, "documentation": "<p>Contains information about a returned public key.</p>"}, "PublicKeyList": {"type": "list", "member": {"shape": "PublicKey"}}, "PutEventConfigurationRequest": {"type": "structure", "required": ["MaxEventSize", "ContextKeySelectors"], "members": {"EventDataStore": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) or ID suffix of the ARN of the event data store for which you want to update event configuration settings.</p>"}, "MaxEventSize": {"shape": "MaxEventSize", "documentation": "<p>The maximum allowed size for events to be stored in the specified event data store. If you are using context key selectors, MaxEventSize must be set to Large.</p>"}, "ContextKeySelectors": {"shape": "ContextKeySelectors", "documentation": "<p>A list of context key selectors that will be included to provide enriched event data.</p>"}}}, "PutEventConfigurationResponse": {"type": "structure", "members": {"EventDataStoreArn": {"shape": "EventDataStoreArn", "documentation": "<p>The Amazon Resource Name (ARN) or ID suffix of the ARN of the event data store for which the event configuration settings were updated.</p>"}, "MaxEventSize": {"shape": "MaxEventSize", "documentation": "<p>The maximum allowed size for events stored in the specified event data store.</p>"}, "ContextKeySelectors": {"shape": "ContextKeySelectors", "documentation": "<p>The list of context key selectors that are configured for the event data store.</p>"}}}, "PutEventSelectorsRequest": {"type": "structure", "required": ["TrailName"], "members": {"TrailName": {"shape": "String", "documentation": "<p>Specifies the name of the trail or trail ARN. If you specify a trail name, the string must meet the following requirements:</p> <ul> <li> <p>Contain only ASCII letters (a-z, A-Z), numbers (0-9), periods (.), underscores (_), or dashes (-)</p> </li> <li> <p>Start with a letter or number, and end with a letter or number</p> </li> <li> <p>Be between 3 and 128 characters</p> </li> <li> <p>Have no adjacent periods, underscores or dashes. Names like <code>my-_namespace</code> and <code>my--namespace</code> are not valid.</p> </li> <li> <p>Not be in IP address format (for example, ***********)</p> </li> </ul> <p>If you specify a trail ARN, it must be in the following format.</p> <p> <code>arn:aws:cloudtrail:us-east-2:************:trail/MyTrail</code> </p>"}, "EventSelectors": {"shape": "EventSelectors", "documentation": "<p>Specifies the settings for your event selectors. You can use event selectors to log management events and data events for the following resource types:</p> <ul> <li> <p> <code>AWS::DynamoDB::Table</code> </p> </li> <li> <p> <code>AWS::Lambda::Function</code> </p> </li> <li> <p> <code>AWS::S3::Object</code> </p> </li> </ul> <p>You can't use event selectors to log network activity events.</p> <p>You can configure up to five event selectors for a trail. You can use either <code>EventSelectors</code> or <code>AdvancedEventSelectors</code> in a <code>PutEventSelectors</code> request, but not both. If you apply <code>EventSelectors</code> to a trail, any existing <code>AdvancedEventSelectors</code> are overwritten.</p>"}, "AdvancedEventSelectors": {"shape": "AdvancedEventSelectors", "documentation": "<p> Specifies the settings for advanced event selectors. You can use advanced event selectors to log management events, data events for all resource types, and network activity events.</p> <p>You can add advanced event selectors, and conditions for your advanced event selectors, up to a maximum of 500 values for all conditions and selectors on a trail. You can use either <code>AdvancedEventSelectors</code> or <code>EventSelectors</code>, but not both. If you apply <code>AdvancedEventSelectors</code> to a trail, any existing <code>EventSelectors</code> are overwritten. For more information about advanced event selectors, see <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/logging-data-events-with-cloudtrail.html\">Logging data events</a> and <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/logging-network-events-with-cloudtrail.html\">Logging network activity events</a> in the <i>CloudTrail User Guide</i>. </p>"}}}, "PutEventSelectorsResponse": {"type": "structure", "members": {"TrailARN": {"shape": "String", "documentation": "<p>Specifies the ARN of the trail that was updated with event selectors. The following is the format of a trail ARN.</p> <p> <code>arn:aws:cloudtrail:us-east-2:************:trail/MyTrail</code> </p>"}, "EventSelectors": {"shape": "EventSelectors", "documentation": "<p>Specifies the event selectors configured for your trail.</p>"}, "AdvancedEventSelectors": {"shape": "AdvancedEventSelectors", "documentation": "<p>Specifies the advanced event selectors configured for your trail.</p>"}}}, "PutInsightSelectorsRequest": {"type": "structure", "required": ["InsightSelectors"], "members": {"TrailName": {"shape": "String", "documentation": "<p>The name of the CloudTrail trail for which you want to change or add Insights selectors.</p> <p>You cannot use this parameter with the <code>EventDataStore</code> and <code>InsightsDestination</code> parameters.</p>"}, "InsightSelectors": {"shape": "InsightSelectors", "documentation": "<p>A JSON string that contains the Insights types you want to log on a trail or event data store. <code>ApiCallRateInsight</code> and <code>ApiErrorRateInsight</code> are valid Insight types.</p> <p>The <code>ApiCallRateInsight</code> Insights type analyzes write-only management API calls that are aggregated per minute against a baseline API call volume.</p> <p>The <code>ApiErrorRateInsight</code> Insights type analyzes management API calls that result in error codes. The error is shown if the API call is unsuccessful.</p>"}, "EventDataStore": {"shape": "EventDataStoreArn", "documentation": "<p>The ARN (or ID suffix of the ARN) of the source event data store for which you want to change or add Insights selectors. To enable Insights on an event data store, you must provide both the <code>EventDataStore</code> and <code>InsightsDestination</code> parameters.</p> <p>You cannot use this parameter with the <code>TrailName</code> parameter.</p>"}, "InsightsDestination": {"shape": "EventDataStoreArn", "documentation": "<p> The ARN (or ID suffix of the ARN) of the destination event data store that logs Insights events. To enable Insights on an event data store, you must provide both the <code>EventDataStore</code> and <code>InsightsDestination</code> parameters. </p> <p>You cannot use this parameter with the <code>TrailName</code> parameter.</p>"}}}, "PutInsightSelectorsResponse": {"type": "structure", "members": {"TrailARN": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of a trail for which you want to change or add Insights selectors.</p>"}, "InsightSelectors": {"shape": "InsightSelectors", "documentation": "<p>A JSON string that contains the Insights event types that you want to log on a trail or event data store. The valid Insights types are <code>ApiErrorRateInsight</code> and <code>ApiCallRateInsight</code>.</p>"}, "EventDataStoreArn": {"shape": "EventDataStoreArn", "documentation": "<p>The Amazon Resource Name (ARN) of the source event data store for which you want to change or add Insights selectors.</p>"}, "InsightsDestination": {"shape": "EventDataStoreArn", "documentation": "<p> The ARN of the destination event data store that logs Insights events. </p>"}}}, "PutResourcePolicyRequest": {"type": "structure", "required": ["ResourceArn", "ResourcePolicy"], "members": {"ResourceArn": {"shape": "ResourceArn", "documentation": "<p> The Amazon Resource Name (ARN) of the CloudTrail event data store, dashboard, or channel attached to the resource-based policy.</p> <p>Example event data store ARN format: <code>arn:aws:cloudtrail:us-east-2:************:eventdatastore/EXAMPLE-f852-4e8f-8bd1-bcf6cEXAMPLE</code> </p> <p>Example dashboard ARN format: <code>arn:aws:cloudtrail:us-east-1:************:dashboard/exampleDash</code> </p> <p>Example channel ARN format: <code>arn:aws:cloudtrail:us-east-2:************:channel/***********</code> </p>"}, "ResourcePolicy": {"shape": "ResourcePolicy", "documentation": "<p> A JSON-formatted string for an Amazon Web Services resource-based policy. </p> <p> For example resource-based policies, see <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/security_iam_resource-based-policy-examples.html\">CloudTrail resource-based policy examples</a> in the <i>CloudTrail User Guide</i>.</p>"}}}, "PutResourcePolicyResponse": {"type": "structure", "members": {"ResourceArn": {"shape": "ResourceArn", "documentation": "<p> The Amazon Resource Name (ARN) of the CloudTrail event data store, dashboard, or channel attached to the resource-based policy. </p> <p>Example event data store ARN format: <code>arn:aws:cloudtrail:us-east-2:************:eventdatastore/EXAMPLE-f852-4e8f-8bd1-bcf6cEXAMPLE</code> </p> <p>Example dashboard ARN format: <code>arn:aws:cloudtrail:us-east-1:************:dashboard/exampleDash</code> </p> <p>Example channel ARN format: <code>arn:aws:cloudtrail:us-east-2:************:channel/***********</code> </p>"}, "ResourcePolicy": {"shape": "ResourcePolicy", "documentation": "<p> The JSON-formatted string of the Amazon Web Services resource-based policy attached to the CloudTrail event data store, dashboard, or channel. </p>"}, "DelegatedAdminResourcePolicy": {"shape": "ResourcePolicy", "documentation": "<p> The default resource-based policy that is automatically generated for the delegated administrator of an Organizations organization. This policy will be evaluated in tandem with any policy you submit for the resource. For more information about this policy, see <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/cloudtrail-lake-organizations.html#cloudtrail-lake-organizations-eds-rbp\">Default resource policy for delegated administrators</a>. </p>"}}}, "Queries": {"type": "list", "member": {"shape": "Query"}}, "Query": {"type": "structure", "members": {"QueryId": {"shape": "UUID", "documentation": "<p>The ID of a query.</p>"}, "QueryStatus": {"shape": "QueryStatus", "documentation": "<p>The status of the query. This can be <code>QUEUED</code>, <code>RUNNING</code>, <code>FINISHED</code>, <code>FAILED</code>, <code>TIMED_OUT</code>, or <code>CANCELLED</code>.</p>"}, "CreationTime": {"shape": "Date", "documentation": "<p>The creation time of a query.</p>"}}, "documentation": "<p>A SQL string of criteria about events that you want to collect in an event data store.</p>"}, "QueryAlias": {"type": "string", "max": 256, "min": 1, "pattern": "^[a-zA-Z][a-zA-Z0-9._\\-]*$"}, "QueryIdNotFoundException": {"type": "structure", "members": {}, "documentation": "<p>The query ID does not exist or does not map to a query.</p>", "exception": true}, "QueryParameter": {"type": "string", "max": 1024, "min": 1, "pattern": ".*"}, "QueryParameterKey": {"type": "string", "max": 128, "min": 3, "pattern": "^[a-zA-Z0-9._/\\-:$]+$"}, "QueryParameterValue": {"type": "string", "max": 128, "min": 1, "pattern": "^[a-zA-Z0-9._/\\-:]+$"}, "QueryParameterValues": {"type": "map", "key": {"shape": "QueryParameterKey"}, "value": {"shape": "QueryParameterValue"}}, "QueryParameters": {"type": "list", "member": {"shape": "QueryParameter"}, "max": 10, "min": 1}, "QueryResultColumn": {"type": "map", "key": {"shape": "QueryResultKey"}, "value": {"shape": "QueryResultValue"}}, "QueryResultKey": {"type": "string"}, "QueryResultRow": {"type": "list", "member": {"shape": "QueryResultColumn"}}, "QueryResultRows": {"type": "list", "member": {"shape": "QueryResultRow"}}, "QueryResultValue": {"type": "string"}, "QueryStatement": {"type": "string", "max": 10000, "min": 1, "pattern": "(?s).*"}, "QueryStatistics": {"type": "structure", "members": {"ResultsCount": {"shape": "Integer", "documentation": "<p>The number of results returned.</p>"}, "TotalResultsCount": {"shape": "Integer", "documentation": "<p>The total number of results returned by a query.</p>"}, "BytesScanned": {"shape": "<PERSON>", "documentation": "<p>The total bytes that the query scanned in the event data store. This value matches the number of bytes for which your account is billed for the query, unless the query is still running.</p>"}}, "documentation": "<p><PERSON><PERSON><PERSON> about a query, such as the number of results.</p>"}, "QueryStatisticsForDescribeQuery": {"type": "structure", "members": {"EventsMatched": {"shape": "<PERSON>", "documentation": "<p>The number of events that matched a query.</p>"}, "EventsScanned": {"shape": "<PERSON>", "documentation": "<p>The number of events that the query scanned in the event data store.</p>"}, "BytesScanned": {"shape": "<PERSON>", "documentation": "<p>The total bytes that the query scanned in the event data store. This value matches the number of bytes for which your account is billed for the query, unless the query is still running.</p>"}, "ExecutionTimeInMillis": {"shape": "Integer", "documentation": "<p>The query's run time, in milliseconds.</p>"}, "CreationTime": {"shape": "Date", "documentation": "<p>The creation time of the query.</p>"}}, "documentation": "<p>Gets metadata about a query, including the number of events that were matched, the total number of events scanned, the query run time in milliseconds, and the query's creation time.</p>"}, "QueryStatus": {"type": "string", "enum": ["QUEUED", "RUNNING", "FINISHED", "FAILED", "CANCELLED", "TIMED_OUT"]}, "ReadWriteType": {"type": "string", "enum": ["Read<PERSON>nly", "WriteOnly", "All"]}, "RefreshId": {"type": "string", "max": 20, "min": 10, "pattern": "\\d+"}, "RefreshSchedule": {"type": "structure", "members": {"Frequency": {"shape": "RefreshScheduleFrequency", "documentation": "<p> The frequency at which you want the dashboard refreshed. </p>"}, "Status": {"shape": "RefreshScheduleStatus", "documentation": "<p> Specifies whether the refresh schedule is enabled. Set the value to <code>ENABLED</code> to enable the refresh schedule, or to <code>DISABLED</code> to turn off the refresh schedule. </p>"}, "TimeOfDay": {"shape": "TimeOfDay", "documentation": "<p> The time of day in UTC to run the schedule; for hourly only refer to minutes; default is 00:00. </p>"}}, "documentation": "<p> The schedule for a dashboard refresh. </p>"}, "RefreshScheduleFrequency": {"type": "structure", "members": {"Unit": {"shape": "RefreshScheduleFrequencyUnit", "documentation": "<p> The unit to use for the refresh. </p> <p>For custom dashboards, the unit can be <code>HOURS</code> or <code>DAYS</code>.</p> <p>For the Highlights dashboard, the <code>Unit</code> must be <code>HOURS</code>.</p>"}, "Value": {"shape": "RefreshScheduleFrequencyValue", "documentation": "<p> The value for the refresh schedule. </p> <p> For custom dashboards, the following values are valid when the unit is <code>HOURS</code>: <code>1</code>, <code>6</code>, <code>12</code>, <code>24</code> </p> <p>For custom dashboards, the only valid value when the unit is <code>DAYS</code> is <code>1</code>.</p> <p>For the Highlights dashboard, the <code>Value</code> must be <code>6</code>.</p>"}}, "documentation": "<p> Specifies the frequency for a dashboard refresh schedule. </p> <p> For a custom dashboard, you can schedule a refresh for every 1, 6, 12, or 24 hours, or every day. </p>"}, "RefreshScheduleFrequencyUnit": {"type": "string", "enum": ["HOURS", "DAYS"]}, "RefreshScheduleFrequencyValue": {"type": "integer"}, "RefreshScheduleStatus": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "RegisterOrganizationDelegatedAdminRequest": {"type": "structure", "required": ["MemberAccountId"], "members": {"MemberAccountId": {"shape": "AccountId", "documentation": "<p>An organization member account ID that you want to designate as a delegated administrator.</p>"}}, "documentation": "<p>Specifies an organization member account ID as a CloudTrail delegated administrator.</p>"}, "RegisterOrganizationDelegatedAdminResponse": {"type": "structure", "members": {}, "documentation": "<p>Returns the following response if successful. Otherwise, returns an error.</p>"}, "RemoveTagsRequest": {"type": "structure", "required": ["ResourceId", "TagsList"], "members": {"ResourceId": {"shape": "String", "documentation": "<p>Specifies the ARN of the trail, event data store, dashboard, or channel from which tags should be removed.</p> <p> Example trail ARN format: <code>arn:aws:cloudtrail:us-east-2:************:trail/MyTrail</code> </p> <p>Example event data store ARN format: <code>arn:aws:cloudtrail:us-east-2:************:eventdatastore/EXAMPLE-f852-4e8f-8bd1-bcf6cEXAMPLE</code> </p> <p>Example dashboard ARN format: <code>arn:aws:cloudtrail:us-east-1:************:dashboard/exampleDash</code> </p> <p>Example channel ARN format: <code>arn:aws:cloudtrail:us-east-2:************:channel/***********</code> </p>"}, "TagsList": {"shape": "TagsList", "documentation": "<p>Specifies a list of tags to be removed.</p>"}}, "documentation": "<p>Specifies the tags to remove from a trail, event data store, dashboard, or channel.</p>"}, "RemoveTagsResponse": {"type": "structure", "members": {}, "documentation": "<p>Returns the objects or data listed below if successful. Otherwise, returns an error.</p>"}, "RequestWidget": {"type": "structure", "required": ["QueryStatement", "ViewProperties"], "members": {"QueryStatement": {"shape": "QueryStatement", "documentation": "<p> The query statement for the widget. For custom dashboard widgets, you can query across multiple event data stores as long as all event data stores exist in your account. </p> <note> <p>When a query uses <code>?</code> with <code>eventTime</code>, <code>?</code> must be surrounded by single quotes as follows: <code>'?'</code>.</p> </note>"}, "QueryParameters": {"shape": "QueryParameters", "documentation": "<p> The optional query parameters. The following query parameters are valid: <code>$StartTime$</code>, <code>$EndTime$</code>, and <code>$Period$</code>. </p>"}, "ViewProperties": {"shape": "ViewPropertiesMap", "documentation": "<p> The view properties for the widget. For more information about view properties, see <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/lake-widget-properties.html\"> View properties for widgets </a> in the <i>CloudTrail User Guide</i>. </p>"}}, "documentation": "<p> Contains information about a widget on a CloudTrail Lake dashboard. </p>"}, "RequestWidgetList": {"type": "list", "member": {"shape": "RequestWidget"}}, "Resource": {"type": "structure", "members": {"ResourceType": {"shape": "String", "documentation": "<p>The type of a resource referenced by the event returned. When the resource type cannot be determined, null is returned. Some examples of resource types are: <b>Instance</b> for EC2, <b>Trail</b> for CloudTrail, <b>DBInstance</b> for Amazon RDS, and <b>AccessKey</b> for IAM. To learn more about how to look up and filter events by the resource types supported for a service, see <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/view-cloudtrail-events-console.html#filtering-cloudtrail-events\">Filtering CloudTrail Events</a>.</p>"}, "ResourceName": {"shape": "String", "documentation": "<p>The name of the resource referenced by the event returned. These are user-created names whose values will depend on the environment. For example, the resource name might be \"auto-scaling-test-group\" for an Auto Scaling Group or \"i-1234567\" for an EC2 Instance.</p>"}}, "documentation": "<p>Specifies the type and name of a resource referenced by an event.</p>"}, "ResourceARNNotValidException": {"type": "structure", "members": {}, "documentation": "<p> This exception is thrown when the provided resource does not exist, or the ARN format of the resource is not valid. </p> <p>The following is the format of an event data store ARN: <code>arn:aws:cloudtrail:us-east-2:************:eventdatastore/EXAMPLE-f852-4e8f-8bd1-bcf6cEXAMPLE</code> </p> <p>The following is the format of a dashboard ARN: <code>arn:aws:cloudtrail:us-east-1:************:dashboard/exampleDash</code> </p> <p>The following is the format of a channel ARN: <code>arn:aws:cloudtrail:us-east-2:************:channel/***********</code> </p>", "exception": true}, "ResourceArn": {"type": "string", "max": 256, "min": 3, "pattern": "^[a-zA-Z0-9._/\\-:]+$"}, "ResourceIdList": {"type": "list", "member": {"shape": "String"}}, "ResourceList": {"type": "list", "member": {"shape": "Resource"}, "documentation": "<p>A list of resources referenced by the event returned.</p>"}, "ResourceNotFoundException": {"type": "structure", "members": {}, "documentation": "<p>This exception is thrown when the specified resource is not found.</p>", "exception": true}, "ResourcePolicy": {"type": "string", "max": 8192, "min": 1}, "ResourcePolicyNotFoundException": {"type": "structure", "members": {}, "documentation": "<p> This exception is thrown when the specified resource policy is not found. </p>", "exception": true}, "ResourcePolicyNotValidException": {"type": "structure", "members": {}, "documentation": "<p> This exception is thrown when the resouce-based policy has syntax errors, or contains a principal that is not valid. </p>", "exception": true}, "ResourceTag": {"type": "structure", "members": {"ResourceId": {"shape": "String", "documentation": "<p>Specifies the ARN of the resource.</p>"}, "TagsList": {"shape": "TagsList", "documentation": "<p>A list of tags.</p>"}}, "documentation": "<p>A resource tag.</p>"}, "ResourceTagList": {"type": "list", "member": {"shape": "ResourceTag"}}, "ResourceTypeNotSupportedException": {"type": "structure", "members": {}, "documentation": "<p>This exception is thrown when the specified resource type is not supported by CloudTrail.</p>", "exception": true}, "RestoreEventDataStoreRequest": {"type": "structure", "required": ["EventDataStore"], "members": {"EventDataStore": {"shape": "EventDataStoreArn", "documentation": "<p>The ARN (or the ID suffix of the ARN) of the event data store that you want to restore.</p>"}}}, "RestoreEventDataStoreResponse": {"type": "structure", "members": {"EventDataStoreArn": {"shape": "EventDataStoreArn", "documentation": "<p>The event data store ARN.</p>"}, "Name": {"shape": "EventDataStoreName", "documentation": "<p>The name of the event data store.</p>"}, "Status": {"shape": "EventDataStoreStatus", "documentation": "<p>The status of the event data store.</p>"}, "AdvancedEventSelectors": {"shape": "AdvancedEventSelectors", "documentation": "<p>The advanced event selectors that were used to select events.</p>"}, "MultiRegionEnabled": {"shape": "Boolean", "documentation": "<p>Indicates whether the event data store is collecting events from all Regions, or only from the Region in which the event data store was created.</p>"}, "OrganizationEnabled": {"shape": "Boolean", "documentation": "<p>Indicates whether an event data store is collecting logged events for an organization in Organizations.</p>"}, "RetentionPeriod": {"shape": "RetentionPeriod", "documentation": "<p>The retention period, in days.</p>"}, "TerminationProtectionEnabled": {"shape": "TerminationProtectionEnabled", "documentation": "<p>Indicates that termination protection is enabled and the event data store cannot be automatically deleted.</p>"}, "CreatedTimestamp": {"shape": "Date", "documentation": "<p>The timestamp of an event data store's creation.</p>"}, "UpdatedTimestamp": {"shape": "Date", "documentation": "<p>The timestamp that shows when an event data store was updated, if applicable. <code>UpdatedTimestamp</code> is always either the same or newer than the time shown in <code>CreatedTimestamp</code>.</p>"}, "KmsKeyId": {"shape": "EventDataStoreKmsKeyId", "documentation": "<p>Specifies the KMS key ID that encrypts the events delivered by CloudTrail. The value is a fully specified ARN to a KMS key in the following format.</p> <p> <code>arn:aws:kms:us-east-2:************:key/********-1234-1234-1234-************</code> </p>"}, "BillingMode": {"shape": "BillingMode", "documentation": "<p>The billing mode for the event data store.</p>"}}}, "RetentionPeriod": {"type": "integer", "max": 3653, "min": 7}, "S3BucketDoesNotExistException": {"type": "structure", "members": {}, "documentation": "<p>This exception is thrown when the specified S3 bucket does not exist.</p>", "exception": true}, "S3ImportSource": {"type": "structure", "required": ["S3LocationUri", "S3BucketRegion", "S3BucketAccessRoleArn"], "members": {"S3LocationUri": {"shape": "String", "documentation": "<p> The URI for the source S3 bucket. </p>"}, "S3BucketRegion": {"shape": "String", "documentation": "<p> The Region associated with the source S3 bucket. </p>"}, "S3BucketAccessRoleArn": {"shape": "String", "documentation": "<p> The IAM ARN role used to access the source S3 bucket. </p>"}}, "documentation": "<p> The settings for the source S3 bucket. </p>"}, "SampleQueryDescription": {"type": "string"}, "SampleQueryName": {"type": "string"}, "SampleQueryRelevance": {"type": "float"}, "SampleQuerySQL": {"type": "string"}, "SearchSampleQueriesMaxResults": {"type": "integer", "max": 50, "min": 1}, "SearchSampleQueriesRequest": {"type": "structure", "required": ["SearchPhrase"], "members": {"SearchPhrase": {"shape": "SearchSampleQueriesSearchPhrase", "documentation": "<p> The natural language phrase to use for the semantic search. The phrase must be in English. The length constraint is in characters, not words.</p>"}, "MaxResults": {"shape": "SearchSampleQueriesMaxResults", "documentation": "<p> The maximum number of results to return on a single page. The default value is 10. </p>"}, "NextToken": {"shape": "PaginationToken", "documentation": "<p> A token you can use to get the next page of results. The length constraint is in characters, not words. </p>"}}}, "SearchSampleQueriesResponse": {"type": "structure", "members": {"SearchResults": {"shape": "SearchSampleQueriesSearchResults", "documentation": "<p> A list of objects containing the search results ordered from most relevant to least relevant. </p>"}, "NextToken": {"shape": "PaginationToken", "documentation": "<p> A token you can use to get the next page of results.</p>"}}}, "SearchSampleQueriesSearchPhrase": {"type": "string", "max": 1000, "min": 2, "pattern": "^[ -~\\n]*$"}, "SearchSampleQueriesSearchResult": {"type": "structure", "members": {"Name": {"shape": "SampleQueryName", "documentation": "<p> The name of a sample query. </p>"}, "Description": {"shape": "SampleQueryDescription", "documentation": "<p> A longer description of a sample query. </p>"}, "SQL": {"shape": "SampleQuerySQL", "documentation": "<p> The SQL code of the sample query. </p>"}, "Relevance": {"shape": "SampleQueryRelevance", "documentation": "<p> A value between 0 and 1 indicating the similarity between the search phrase and result. </p>"}}, "documentation": "<p> A search result returned by the <code>SearchSampleQueries</code> operation. </p>"}, "SearchSampleQueriesSearchResults": {"type": "list", "member": {"shape": "SearchSampleQueriesSearchResult"}}, "SelectorField": {"type": "string", "max": 1000, "min": 1, "pattern": "[\\w|\\d|\\.|_]+"}, "SelectorName": {"type": "string", "max": 1000, "min": 0, "pattern": ".*"}, "ServiceQuotaExceededException": {"type": "structure", "members": {}, "documentation": "<p> This exception is thrown when the quota is exceeded. For information about CloudTrail quotas, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/ct.html#limits_cloudtrail\">Service quotas</a> in the <i>Amazon Web Services General Reference</i>. </p>", "exception": true}, "Source": {"type": "string", "max": 256, "min": 1, "pattern": ".*"}, "SourceConfig": {"type": "structure", "members": {"ApplyToAllRegions": {"shape": "Boolean", "documentation": "<p> Specifies whether the channel applies to a single Region or to all Regions.</p>"}, "AdvancedEventSelectors": {"shape": "AdvancedEventSelectors", "documentation": "<p> The advanced event selectors that are configured for the channel.</p>"}}, "documentation": "<p> Contains configuration information about the channel. </p>"}, "StartDashboardRefreshRequest": {"type": "structure", "required": ["DashboardId"], "members": {"DashboardId": {"shape": "DashboardArn", "documentation": "<p> The name or ARN of the dashboard. </p>"}, "QueryParameterValues": {"shape": "QueryParameterValues", "documentation": "<p> The query parameter values for the dashboard </p> <p>For custom dashboards, the following query parameters are valid: <code>$StartTime$</code>, <code>$EndTime$</code>, and <code>$Period$</code>.</p> <p>For managed dashboards, the following query parameters are valid: <code>$StartTime$</code>, <code>$EndTime$</code>, <code>$Period$</code>, and <code>$EventDataStoreId$</code>. The <code>$EventDataStoreId$</code> query parameter is required.</p>"}}}, "StartDashboardRefreshResponse": {"type": "structure", "members": {"RefreshId": {"shape": "RefreshId", "documentation": "<p> The refresh ID for the dashboard. </p>"}}}, "StartEventDataStoreIngestionRequest": {"type": "structure", "required": ["EventDataStore"], "members": {"EventDataStore": {"shape": "EventDataStoreArn", "documentation": "<p>The ARN (or ID suffix of the ARN) of the event data store for which you want to start ingestion.</p>"}}}, "StartEventDataStoreIngestionResponse": {"type": "structure", "members": {}}, "StartImportRequest": {"type": "structure", "members": {"Destinations": {"shape": "ImportDestinations", "documentation": "<p> The ARN of the destination event data store. Use this parameter for a new import. </p>"}, "ImportSource": {"shape": "ImportSource", "documentation": "<p> The source S3 bucket for the import. Use this parameter for a new import. </p>"}, "StartEventTime": {"shape": "Date", "documentation": "<p> Use with <code>EndEventTime</code> to bound a <code>StartImport</code> request, and limit imported trail events to only those events logged within a specified time period. When you specify a time range, CloudTrail checks the prefix and log file names to verify the names contain a date between the specified <code>StartEventTime</code> and <code>EndEventTime</code> before attempting to import events. </p>"}, "EndEventTime": {"shape": "Date", "documentation": "<p> Use with <code>StartEventTime</code> to bound a <code>StartImport</code> request, and limit imported trail events to only those events logged within a specified time period. When you specify a time range, CloudTrail checks the prefix and log file names to verify the names contain a date between the specified <code>StartEventTime</code> and <code>EndEventTime</code> before attempting to import events. </p>"}, "ImportId": {"shape": "UUID", "documentation": "<p> The ID of the import. Use this parameter when you are retrying an import. </p>"}}}, "StartImportResponse": {"type": "structure", "members": {"ImportId": {"shape": "UUID", "documentation": "<p> The ID of the import. </p>"}, "Destinations": {"shape": "ImportDestinations", "documentation": "<p> The ARN of the destination event data store. </p>"}, "ImportSource": {"shape": "ImportSource", "documentation": "<p> The source S3 bucket for the import. </p>"}, "StartEventTime": {"shape": "Date", "documentation": "<p> Used with <code>EndEventTime</code> to bound a <code>StartImport</code> request, and limit imported trail events to only those events logged within a specified time period. </p>"}, "EndEventTime": {"shape": "Date", "documentation": "<p> Used with <code>StartEventTime</code> to bound a <code>StartImport</code> request, and limit imported trail events to only those events logged within a specified time period. </p>"}, "ImportStatus": {"shape": "ImportStatus", "documentation": "<p> Shows the status of the import after a <code>StartImport</code> request. An import finishes with a status of <code>COMPLETED</code> if there were no failures, or <code>FAILED</code> if there were failures. </p>"}, "CreatedTimestamp": {"shape": "Date", "documentation": "<p> The timestamp for the import's creation. </p>"}, "UpdatedTimestamp": {"shape": "Date", "documentation": "<p> The timestamp of the import's last update, if applicable. </p>"}}}, "StartLoggingRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "String", "documentation": "<p>Specifies the name or the CloudTrail ARN of the trail for which CloudTrail logs Amazon Web Services API calls. The following is the format of a trail ARN.</p> <p> <code>arn:aws:cloudtrail:us-east-2:************:trail/MyTrail</code> </p>"}}, "documentation": "<p>The request to CloudTrail to start logging Amazon Web Services API calls for an account.</p>"}, "StartLoggingResponse": {"type": "structure", "members": {}, "documentation": "<p>Returns the objects or data listed below if successful. Otherwise, returns an error.</p>"}, "StartQueryRequest": {"type": "structure", "members": {"QueryStatement": {"shape": "QueryStatement", "documentation": "<p>The SQL code of your query.</p>"}, "DeliveryS3Uri": {"shape": "DeliveryS3Uri", "documentation": "<p> The URI for the S3 bucket where CloudTrail delivers the query results. </p>"}, "QueryAlias": {"shape": "Query<PERSON><PERSON><PERSON>", "documentation": "<p> The alias that identifies a query template. </p>"}, "QueryParameters": {"shape": "QueryParameters", "documentation": "<p> The query parameters for the specified <code>QueryAlias</code>. </p>"}, "EventDataStoreOwnerAccountId": {"shape": "AccountId", "documentation": "<p> The account ID of the event data store owner. </p>"}}}, "StartQueryResponse": {"type": "structure", "members": {"QueryId": {"shape": "UUID", "documentation": "<p>The ID of the started query.</p>"}, "EventDataStoreOwnerAccountId": {"shape": "AccountId", "documentation": "<p> The account ID of the event data store owner. </p>"}}}, "StopEventDataStoreIngestionRequest": {"type": "structure", "required": ["EventDataStore"], "members": {"EventDataStore": {"shape": "EventDataStoreArn", "documentation": "<p>The ARN (or ID suffix of the ARN) of the event data store for which you want to stop ingestion.</p>"}}}, "StopEventDataStoreIngestionResponse": {"type": "structure", "members": {}}, "StopImportRequest": {"type": "structure", "required": ["ImportId"], "members": {"ImportId": {"shape": "UUID", "documentation": "<p> The ID of the import. </p>"}}}, "StopImportResponse": {"type": "structure", "members": {"ImportId": {"shape": "UUID", "documentation": "<p> The ID for the import. </p>"}, "ImportSource": {"shape": "ImportSource", "documentation": "<p> The source S3 bucket for the import. </p>"}, "Destinations": {"shape": "ImportDestinations", "documentation": "<p> The ARN of the destination event data store. </p>"}, "ImportStatus": {"shape": "ImportStatus", "documentation": "<p> The status of the import. </p>"}, "CreatedTimestamp": {"shape": "Date", "documentation": "<p> The timestamp of the import's creation. </p>"}, "UpdatedTimestamp": {"shape": "Date", "documentation": "<p> The timestamp of the import's last update. </p>"}, "StartEventTime": {"shape": "Date", "documentation": "<p> Used with <code>EndEventTime</code> to bound a <code>StartImport</code> request, and limit imported trail events to only those events logged within a specified time period. </p>"}, "EndEventTime": {"shape": "Date", "documentation": "<p> Used with <code>StartEventTime</code> to bound a <code>StartImport</code> request, and limit imported trail events to only those events logged within a specified time period. </p>"}, "ImportStatistics": {"shape": "ImportStatistics", "documentation": "<p> Returns information on the stopped import. </p>"}}}, "StopLoggingRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "String", "documentation": "<p>Specifies the name or the CloudTrail ARN of the trail for which CloudTrail will stop logging Amazon Web Services API calls. The following is the format of a trail ARN.</p> <p> <code>arn:aws:cloudtrail:us-east-2:************:trail/MyTrail</code> </p>"}}, "documentation": "<p>Passes the request to CloudTrail to stop logging Amazon Web Services API calls for the specified account.</p>"}, "StopLoggingResponse": {"type": "structure", "members": {}, "documentation": "<p>Returns the objects or data listed below if successful. Otherwise, returns an error.</p>"}, "String": {"type": "string"}, "Tag": {"type": "structure", "required": ["Key"], "members": {"Key": {"shape": "TagKey", "documentation": "<p>The key in a key-value pair. The key must be must be no longer than 128 Unicode characters. The key must be unique for the resource to which it applies.</p>"}, "Value": {"shape": "TagValue", "documentation": "<p>The value in a key-value pair of a tag. The value must be no longer than 256 Unicode characters.</p>"}}, "documentation": "<p>A custom key-value pair associated with a resource such as a CloudTrail trail, event data store, dashboard, or channel.</p>"}, "TagKey": {"type": "string", "max": 128, "min": 1}, "TagValue": {"type": "string", "max": 256, "min": 1}, "TagsLimitExceededException": {"type": "structure", "members": {}, "documentation": "<p>The number of tags per trail, event data store, dashboard, or channel has exceeded the permitted amount. Currently, the limit is 50.</p>", "exception": true}, "TagsList": {"type": "list", "member": {"shape": "Tag"}, "documentation": "<p>A list of tags.</p>", "max": 200}, "TerminationProtectionEnabled": {"type": "boolean"}, "ThrottlingException": {"type": "structure", "members": {}, "documentation": "<p> This exception is thrown when the request rate exceeds the limit. </p>", "exception": true}, "TimeOfDay": {"type": "string", "pattern": "^[0-9]{2}:[0-9]{2}"}, "Timestamps": {"type": "list", "member": {"shape": "Date"}}, "Trail": {"type": "structure", "members": {"Name": {"shape": "String", "documentation": "<p>Name of the trail set by calling <a>CreateTrail</a>. The maximum length is 128 characters.</p>"}, "S3BucketName": {"shape": "String", "documentation": "<p>Name of the Amazon S3 bucket into which CloudTrail delivers your trail files. See <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/bucketnamingrules.html\">Amazon S3 Bucket naming rules</a>.</p>"}, "S3KeyPrefix": {"shape": "String", "documentation": "<p>Specifies the Amazon S3 key prefix that comes after the name of the bucket you have designated for log file delivery. For more information, see <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/get-and-view-cloudtrail-log-files.html#cloudtrail-find-log-files\">Finding Your CloudTrail Log Files</a>. The maximum length is 200 characters.</p>"}, "SnsTopicName": {"shape": "String", "documentation": "<p>This field is no longer in use. Use <code>SnsTopicARN</code>.</p>", "deprecated": true}, "SnsTopicARN": {"shape": "String", "documentation": "<p>Specifies the ARN of the Amazon SNS topic that CloudTrail uses to send notifications when log files are delivered. The following is the format of a topic ARN.</p> <p> <code>arn:aws:sns:us-east-2:************:MyTopic</code> </p>"}, "IncludeGlobalServiceEvents": {"shape": "Boolean", "documentation": "<p>Set to <b>True</b> to include Amazon Web Services API calls from Amazon Web Services global services such as IAM. Otherwise, <b>False</b>.</p>"}, "IsMultiRegionTrail": {"shape": "Boolean", "documentation": "<p>Specifies whether the trail exists only in one Region or exists in all Regions.</p>"}, "HomeRegion": {"shape": "String", "documentation": "<p>The Region in which the trail was created.</p>"}, "TrailARN": {"shape": "String", "documentation": "<p>Specifies the ARN of the trail. The following is the format of a trail ARN.</p> <p> <code>arn:aws:cloudtrail:us-east-2:************:trail/MyTrail</code> </p>"}, "LogFileValidationEnabled": {"shape": "Boolean", "documentation": "<p>Specifies whether log file validation is enabled.</p>"}, "CloudWatchLogsLogGroupArn": {"shape": "String", "documentation": "<p>Specifies an Amazon Resource Name (ARN), a unique identifier that represents the log group to which CloudTrail logs will be delivered.</p>"}, "CloudWatchLogsRoleArn": {"shape": "String", "documentation": "<p>Specifies the role for the CloudWatch Logs endpoint to assume to write to a user's log group.</p>"}, "KmsKeyId": {"shape": "String", "documentation": "<p>Specifies the KMS key ID that encrypts the logs delivered by CloudTrail. The value is a fully specified ARN to a KMS key in the following format.</p> <p> <code>arn:aws:kms:us-east-2:************:key/********-1234-1234-1234-************</code> </p>"}, "HasCustomEventSelectors": {"shape": "Boolean", "documentation": "<p>Specifies if the trail has custom event selectors.</p>"}, "HasInsightSelectors": {"shape": "Boolean", "documentation": "<p>Specifies whether a trail has insight types specified in an <code>InsightSelector</code> list.</p>"}, "IsOrganizationTrail": {"shape": "Boolean", "documentation": "<p>Specifies whether the trail is an organization trail.</p>"}}, "documentation": "<p>The settings for a trail.</p>"}, "TrailAlreadyExistsException": {"type": "structure", "members": {}, "documentation": "<p>This exception is thrown when the specified trail already exists.</p>", "exception": true}, "TrailInfo": {"type": "structure", "members": {"TrailARN": {"shape": "String", "documentation": "<p>The ARN of a trail.</p>"}, "Name": {"shape": "String", "documentation": "<p>The name of a trail.</p>"}, "HomeRegion": {"shape": "String", "documentation": "<p>The Amazon Web Services Region in which a trail was created.</p>"}}, "documentation": "<p>Information about a CloudTrail trail, including the trail's name, home Region, and Amazon Resource Name (ARN).</p>"}, "TrailList": {"type": "list", "member": {"shape": "Trail"}}, "TrailNameList": {"type": "list", "member": {"shape": "String"}}, "TrailNotFoundException": {"type": "structure", "members": {}, "documentation": "<p>This exception is thrown when the trail with the given name is not found.</p>", "exception": true}, "TrailNotProvidedException": {"type": "structure", "members": {}, "documentation": "<p>This exception is no longer in use.</p>", "exception": true}, "Trails": {"type": "list", "member": {"shape": "TrailInfo"}}, "Type": {"type": "string", "enum": ["TagContext", "RequestContext"]}, "UUID": {"type": "string", "max": 36, "min": 36, "pattern": "^[a-f0-9\\-]+$"}, "UnsupportedOperationException": {"type": "structure", "members": {}, "documentation": "<p>This exception is thrown when the requested operation is not supported.</p>", "exception": true}, "UpdateChannelRequest": {"type": "structure", "required": ["Channel"], "members": {"Channel": {"shape": "ChannelArn", "documentation": "<p>The ARN or ID (the ARN suffix) of the channel that you want to update.</p>"}, "Destinations": {"shape": "Destinations", "documentation": "<p>The ARNs of event data stores that you want to log events arriving through the channel.</p>"}, "Name": {"shape": "ChannelName", "documentation": "<p> Changes the name of the channel. </p>"}}}, "UpdateChannelResponse": {"type": "structure", "members": {"ChannelArn": {"shape": "ChannelArn", "documentation": "<p>The ARN of the channel that was updated.</p>"}, "Name": {"shape": "ChannelName", "documentation": "<p>The name of the channel that was updated.</p>"}, "Source": {"shape": "Source", "documentation": "<p>The event source of the channel that was updated.</p>"}, "Destinations": {"shape": "Destinations", "documentation": "<p>The event data stores that log events arriving through the channel.</p>"}}}, "UpdateDashboardRequest": {"type": "structure", "required": ["DashboardId"], "members": {"DashboardId": {"shape": "DashboardArn", "documentation": "<p> The name or ARN of the dashboard. </p>"}, "Widgets": {"shape": "RequestWidgetList", "documentation": "<p> An array of widgets for the dashboard. A custom dashboard can have a maximum of 10 widgets. </p> <p>To add new widgets, pass in an array that includes the existing widgets along with any new widgets. Run the <code>GetDashboard</code> operation to get the list of widgets for the dashboard.</p> <p>To remove widgets, pass in an array that includes the existing widgets minus the widgets you want removed.</p>"}, "RefreshSchedule": {"shape": "RefreshSchedule", "documentation": "<p> The refresh schedule configuration for the dashboard. </p>"}, "TerminationProtectionEnabled": {"shape": "TerminationProtectionEnabled", "documentation": "<p> Specifies whether termination protection is enabled for the dashboard. If termination protection is enabled, you cannot delete the dashboard until termination protection is disabled. </p>"}}}, "UpdateDashboardResponse": {"type": "structure", "members": {"DashboardArn": {"shape": "DashboardArn", "documentation": "<p> The ARN for the dashboard. </p>"}, "Name": {"shape": "DashboardName", "documentation": "<p> The name for the dashboard. </p>"}, "Type": {"shape": "DashboardType", "documentation": "<p> The type of dashboard. </p>"}, "Widgets": {"shape": "WidgetList", "documentation": "<p> An array of widgets for the dashboard. </p>"}, "RefreshSchedule": {"shape": "RefreshSchedule", "documentation": "<p> The refresh schedule for the dashboard, if configured. </p>"}, "TerminationProtectionEnabled": {"shape": "TerminationProtectionEnabled", "documentation": "<p> Indicates whether termination protection is enabled for the dashboard. </p>"}, "CreatedTimestamp": {"shape": "Date", "documentation": "<p> The timestamp that shows when the dashboard was created. </p>"}, "UpdatedTimestamp": {"shape": "Date", "documentation": "<p> The timestamp that shows when the dashboard was updated. </p>"}}}, "UpdateEventDataStoreRequest": {"type": "structure", "required": ["EventDataStore"], "members": {"EventDataStore": {"shape": "EventDataStoreArn", "documentation": "<p>The ARN (or the ID suffix of the ARN) of the event data store that you want to update.</p>"}, "Name": {"shape": "EventDataStoreName", "documentation": "<p>The event data store name.</p>"}, "AdvancedEventSelectors": {"shape": "AdvancedEventSelectors", "documentation": "<p>The advanced event selectors used to select events for the event data store. You can configure up to five advanced event selectors for each event data store.</p>"}, "MultiRegionEnabled": {"shape": "Boolean", "documentation": "<p>Specifies whether an event data store collects events from all Regions, or only from the Region in which it was created.</p>"}, "OrganizationEnabled": {"shape": "Boolean", "documentation": "<p>Specifies whether an event data store collects events logged for an organization in Organizations.</p> <note> <p>Only the management account for the organization can convert an organization event data store to a non-organization event data store, or convert a non-organization event data store to an organization event data store.</p> </note>"}, "RetentionPeriod": {"shape": "RetentionPeriod", "documentation": "<p>The retention period of the event data store, in days. If <code>BillingMode</code> is set to <code>EXTENDABLE_RETENTION_PRICING</code>, you can set a retention period of up to 3653 days, the equivalent of 10 years. If <code>BillingMode</code> is set to <code>FIXED_RETENTION_PRICING</code>, you can set a retention period of up to 2557 days, the equivalent of seven years.</p> <p>CloudTrail Lake determines whether to retain an event by checking if the <code>eventTime</code> of the event is within the specified retention period. For example, if you set a retention period of 90 days, CloudTrail will remove events when the <code>eventTime</code> is older than 90 days.</p> <note> <p>If you decrease the retention period of an event data store, CloudTrail will remove any events with an <code>eventTime</code> older than the new retention period. For example, if the previous retention period was 365 days and you decrease it to 100 days, CloudTrail will remove events with an <code>eventTime</code> older than 100 days.</p> </note>"}, "TerminationProtectionEnabled": {"shape": "TerminationProtectionEnabled", "documentation": "<p>Indicates that termination protection is enabled and the event data store cannot be automatically deleted.</p>"}, "KmsKeyId": {"shape": "EventDataStoreKmsKeyId", "documentation": "<p>Specifies the KMS key ID to use to encrypt the events delivered by CloudTrail. The value can be an alias name prefixed by <code>alias/</code>, a fully specified ARN to an alias, a fully specified ARN to a key, or a globally unique identifier.</p> <important> <p>Disabling or deleting the KMS key, or removing CloudTrail permissions on the key, prevents CloudTrail from logging events to the event data store, and prevents users from querying the data in the event data store that was encrypted with the key. After you associate an event data store with a KMS key, the KMS key cannot be removed or changed. Before you disable or delete a KMS key that you are using with an event data store, delete or back up your event data store.</p> </important> <p>CloudTrail also supports KMS multi-Region keys. For more information about multi-Region keys, see <a href=\"https://docs.aws.amazon.com/kms/latest/developerguide/multi-region-keys-overview.html\">Using multi-Region keys</a> in the <i>Key Management Service Developer Guide</i>.</p> <p>Examples:</p> <ul> <li> <p> <code>alias/MyAliasName</code> </p> </li> <li> <p> <code>arn:aws:kms:us-east-2:************:alias/MyAliasName</code> </p> </li> <li> <p> <code>arn:aws:kms:us-east-2:************:key/********-1234-1234-1234-************</code> </p> </li> <li> <p> <code>********-1234-1234-1234-************</code> </p> </li> </ul>"}, "BillingMode": {"shape": "BillingMode", "documentation": "<note> <p>You can't change the billing mode from <code>EXTENDABLE_RETENTION_PRICING</code> to <code>FIXED_RETENTION_PRICING</code>. If <code>BillingMode</code> is set to <code>EXTENDABLE_RETENTION_PRICING</code> and you want to use <code>FIXED_RETENTION_PRICING</code> instead, you'll need to stop ingestion on the event data store and create a new event data store that uses <code>FIXED_RETENTION_PRICING</code>.</p> </note> <p>The billing mode for the event data store determines the cost for ingesting events and the default and maximum retention period for the event data store.</p> <p>The following are the possible values:</p> <ul> <li> <p> <code>EXTENDABLE_RETENTION_PRICING</code> - This billing mode is generally recommended if you want a flexible retention period of up to 3653 days (about 10 years). The default retention period for this billing mode is 366 days.</p> </li> <li> <p> <code>FIXED_RETENTION_PRICING</code> - This billing mode is recommended if you expect to ingest more than 25 TB of event data per month and need a retention period of up to 2557 days (about 7 years). The default retention period for this billing mode is 2557 days.</p> </li> </ul> <p>For more information about CloudTrail pricing, see <a href=\"http://aws.amazon.com/cloudtrail/pricing/\">CloudTrail Pricing</a> and <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/cloudtrail-lake-manage-costs.html\">Managing CloudTrail Lake costs</a>.</p>"}}}, "UpdateEventDataStoreResponse": {"type": "structure", "members": {"EventDataStoreArn": {"shape": "EventDataStoreArn", "documentation": "<p>The ARN of the event data store.</p>"}, "Name": {"shape": "EventDataStoreName", "documentation": "<p>The name of the event data store.</p>"}, "Status": {"shape": "EventDataStoreStatus", "documentation": "<p>The status of an event data store.</p>"}, "AdvancedEventSelectors": {"shape": "AdvancedEventSelectors", "documentation": "<p>The advanced event selectors that are applied to the event data store.</p>"}, "MultiRegionEnabled": {"shape": "Boolean", "documentation": "<p>Indicates whether the event data store includes events from all Regions, or only from the Region in which it was created.</p>"}, "OrganizationEnabled": {"shape": "Boolean", "documentation": "<p>Indicates whether an event data store is collecting logged events for an organization in Organizations.</p>"}, "RetentionPeriod": {"shape": "RetentionPeriod", "documentation": "<p>The retention period, in days.</p>"}, "TerminationProtectionEnabled": {"shape": "TerminationProtectionEnabled", "documentation": "<p>Indicates whether termination protection is enabled for the event data store.</p>"}, "CreatedTimestamp": {"shape": "Date", "documentation": "<p>The timestamp that shows when an event data store was first created.</p>"}, "UpdatedTimestamp": {"shape": "Date", "documentation": "<p>The timestamp that shows when the event data store was last updated. <code>UpdatedTimestamp</code> is always either the same or newer than the time shown in <code>CreatedTimestamp</code>.</p>"}, "KmsKeyId": {"shape": "EventDataStoreKmsKeyId", "documentation": "<p>Specifies the KMS key ID that encrypts the events delivered by CloudTrail. The value is a fully specified ARN to a KMS key in the following format.</p> <p> <code>arn:aws:kms:us-east-2:************:key/********-1234-1234-1234-************</code> </p>"}, "BillingMode": {"shape": "BillingMode", "documentation": "<p>The billing mode for the event data store.</p>"}, "FederationStatus": {"shape": "FederationStatus", "documentation": "<p> Indicates the <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/query-federation.html\">Lake query federation</a> status. The status is <code>ENABLED</code> if Lake query federation is enabled, or <code>DISABLED</code> if Lake query federation is disabled. You cannot delete an event data store if the <code>FederationStatus</code> is <code>ENABLED</code>. </p>"}, "FederationRoleArn": {"shape": "FederationRoleArn", "documentation": "<p> If Lake query federation is enabled, provides the ARN of the federation role used to access the resources for the federated event data store. </p>"}}}, "UpdateTrailRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "String", "documentation": "<p>Specifies the name of the trail or trail ARN. If <code>Name</code> is a trail name, the string must meet the following requirements:</p> <ul> <li> <p>Contain only ASCII letters (a-z, A-Z), numbers (0-9), periods (.), underscores (_), or dashes (-)</p> </li> <li> <p>Start with a letter or number, and end with a letter or number</p> </li> <li> <p>Be between 3 and 128 characters</p> </li> <li> <p>Have no adjacent periods, underscores or dashes. Names like <code>my-_namespace</code> and <code>my--namespace</code> are not valid.</p> </li> <li> <p>Not be in IP address format (for example, ***********)</p> </li> </ul> <p>If <code>Name</code> is a trail ARN, it must be in the following format.</p> <p> <code>arn:aws:cloudtrail:us-east-2:************:trail/MyTrail</code> </p>"}, "S3BucketName": {"shape": "String", "documentation": "<p>Specifies the name of the Amazon S3 bucket designated for publishing log files. See <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/bucketnamingrules.html\">Amazon S3 Bucket naming rules</a>.</p>"}, "S3KeyPrefix": {"shape": "String", "documentation": "<p>Specifies the Amazon S3 key prefix that comes after the name of the bucket you have designated for log file delivery. For more information, see <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/get-and-view-cloudtrail-log-files.html#cloudtrail-find-log-files\">Finding Your CloudTrail Log Files</a>. The maximum length is 200 characters.</p>"}, "SnsTopicName": {"shape": "String", "documentation": "<p>Specifies the name or ARN of the Amazon SNS topic defined for notification of log file delivery. The maximum length is 256 characters.</p>"}, "IncludeGlobalServiceEvents": {"shape": "Boolean", "documentation": "<p>Specifies whether the trail is publishing events from global services such as IAM to the log files.</p>"}, "IsMultiRegionTrail": {"shape": "Boolean", "documentation": "<p>Specifies whether the trail applies only to the current Region or to all Regions. The default is false. If the trail exists only in the current Region and this value is set to true, shadow trails (replications of the trail) will be created in the other Regions. If the trail exists in all Regions and this value is set to false, the trail will remain in the Region where it was created, and its shadow trails in other Regions will be deleted. As a best practice, consider using trails that log events in all Regions.</p>"}, "EnableLogFileValidation": {"shape": "Boolean", "documentation": "<p>Specifies whether log file validation is enabled. The default is false.</p> <note> <p>When you disable log file integrity validation, the chain of digest files is broken after one hour. CloudTrail does not create digest files for log files that were delivered during a period in which log file integrity validation was disabled. For example, if you enable log file integrity validation at noon on January 1, disable it at noon on January 2, and re-enable it at noon on January 10, digest files will not be created for the log files delivered from noon on January 2 to noon on January 10. The same applies whenever you stop CloudTrail logging or delete a trail.</p> </note>"}, "CloudWatchLogsLogGroupArn": {"shape": "String", "documentation": "<p>Specifies a log group name using an Amazon Resource Name (ARN), a unique identifier that represents the log group to which CloudTrail logs are delivered. You must use a log group that exists in your account.</p> <p>Not required unless you specify <code>CloudWatchLogsRoleArn</code>.</p>"}, "CloudWatchLogsRoleArn": {"shape": "String", "documentation": "<p>Specifies the role for the CloudWatch Logs endpoint to assume to write to a user's log group. You must use a role that exists in your account.</p>"}, "KmsKeyId": {"shape": "String", "documentation": "<p>Specifies the KMS key ID to use to encrypt the logs delivered by CloudTrail. The value can be an alias name prefixed by \"alias/\", a fully specified ARN to an alias, a fully specified ARN to a key, or a globally unique identifier.</p> <p>CloudTrail also supports KMS multi-Region keys. For more information about multi-Region keys, see <a href=\"https://docs.aws.amazon.com/kms/latest/developerguide/multi-region-keys-overview.html\">Using multi-Region keys</a> in the <i>Key Management Service Developer Guide</i>.</p> <p>Examples:</p> <ul> <li> <p>alias/MyAliasName</p> </li> <li> <p>arn:aws:kms:us-east-2:************:alias/MyAliasName</p> </li> <li> <p>arn:aws:kms:us-east-2:************:key/********-1234-1234-1234-************</p> </li> <li> <p>********-1234-1234-1234-************</p> </li> </ul>"}, "IsOrganizationTrail": {"shape": "Boolean", "documentation": "<p>Specifies whether the trail is applied to all accounts in an organization in Organizations, or only for the current Amazon Web Services account. The default is false, and cannot be true unless the call is made on behalf of an Amazon Web Services account that is the management account for an organization in Organizations. If the trail is not an organization trail and this is set to <code>true</code>, the trail will be created in all Amazon Web Services accounts that belong to the organization. If the trail is an organization trail and this is set to <code>false</code>, the trail will remain in the current Amazon Web Services account but be deleted from all member accounts in the organization.</p> <note> <p>Only the management account for the organization can convert an organization trail to a non-organization trail, or convert a non-organization trail to an organization trail.</p> </note>"}}, "documentation": "<p>Specifies settings to update for the trail.</p>"}, "UpdateTrailResponse": {"type": "structure", "members": {"Name": {"shape": "String", "documentation": "<p>Specifies the name of the trail.</p>"}, "S3BucketName": {"shape": "String", "documentation": "<p>Specifies the name of the Amazon S3 bucket designated for publishing log files.</p>"}, "S3KeyPrefix": {"shape": "String", "documentation": "<p>Specifies the Amazon S3 key prefix that comes after the name of the bucket you have designated for log file delivery. For more information, see <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/get-and-view-cloudtrail-log-files.html#cloudtrail-find-log-files\">Finding Your IAM Log Files</a>.</p>"}, "SnsTopicName": {"shape": "String", "documentation": "<p>This field is no longer in use. Use <code>SnsTopicARN</code>.</p>", "deprecated": true}, "SnsTopicARN": {"shape": "String", "documentation": "<p>Specifies the ARN of the Amazon SNS topic that CloudTrail uses to send notifications when log files are delivered. The following is the format of a topic ARN.</p> <p> <code>arn:aws:sns:us-east-2:************:MyTopic</code> </p>"}, "IncludeGlobalServiceEvents": {"shape": "Boolean", "documentation": "<p>Specifies whether the trail is publishing events from global services such as IAM to the log files.</p>"}, "IsMultiRegionTrail": {"shape": "Boolean", "documentation": "<p>Specifies whether the trail exists in one Region or in all Regions.</p>"}, "TrailARN": {"shape": "String", "documentation": "<p>Specifies the ARN of the trail that was updated. The following is the format of a trail ARN.</p> <p> <code>arn:aws:cloudtrail:us-east-2:************:trail/MyTrail</code> </p>"}, "LogFileValidationEnabled": {"shape": "Boolean", "documentation": "<p>Specifies whether log file integrity validation is enabled.</p>"}, "CloudWatchLogsLogGroupArn": {"shape": "String", "documentation": "<p>Specifies the Amazon Resource Name (ARN) of the log group to which CloudTrail logs are delivered.</p>"}, "CloudWatchLogsRoleArn": {"shape": "String", "documentation": "<p>Specifies the role for the CloudWatch Logs endpoint to assume to write to a user's log group.</p>"}, "KmsKeyId": {"shape": "String", "documentation": "<p>Specifies the KMS key ID that encrypts the logs delivered by CloudTrail. The value is a fully specified ARN to a KMS key in the following format.</p> <p> <code>arn:aws:kms:us-east-2:************:key/********-1234-1234-1234-************</code> </p>"}, "IsOrganizationTrail": {"shape": "Boolean", "documentation": "<p>Specifies whether the trail is an organization trail.</p>"}}, "documentation": "<p>Returns the objects or data listed below if successful. Otherwise, returns an error.</p>"}, "ViewPropertiesKey": {"type": "string", "max": 128, "min": 3, "pattern": "^[a-zA-Z0-9._\\-]+$"}, "ViewPropertiesMap": {"type": "map", "key": {"shape": "ViewPropertiesKey"}, "value": {"shape": "ViewPropertiesValue"}}, "ViewPropertiesValue": {"type": "string", "max": 128, "min": 1, "pattern": "^[a-zA-Z0-9._\\- ]+$"}, "Widget": {"type": "structure", "members": {"QueryAlias": {"shape": "Query<PERSON><PERSON><PERSON>", "documentation": "<p>The query alias used to identify the query for the widget. </p>"}, "QueryStatement": {"shape": "QueryStatement", "documentation": "<p> The SQL query statement for the widget. </p>"}, "QueryParameters": {"shape": "QueryParameters", "documentation": "<p> The query parameters for the widget. </p>"}, "ViewProperties": {"shape": "ViewPropertiesMap", "documentation": "<p> The view properties for the widget. For more information about view properties, see <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/lake-widget-properties.html\"> View properties for widgets </a> in the <i>CloudTrail User Guide</i>.. </p>"}}, "documentation": "<p> A widget on a CloudTrail Lake dashboard. </p>"}, "WidgetList": {"type": "list", "member": {"shape": "Widget"}}}, "documentation": "<fullname>CloudTrail</fullname> <p>This is the CloudTrail API Reference. It provides descriptions of actions, data types, common parameters, and common errors for CloudTrail.</p> <p>CloudTrail is a web service that records Amazon Web Services API calls for your Amazon Web Services account and delivers log files to an Amazon S3 bucket. The recorded information includes the identity of the user, the start time of the Amazon Web Services API call, the source IP address, the request parameters, and the response elements returned by the service.</p> <note> <p>As an alternative to the API, you can use one of the Amazon Web Services SDKs, which consist of libraries and sample code for various programming languages and platforms (Java, Ruby, .NET, iOS, Android, etc.). The SDKs provide programmatic access to CloudTrail. For example, the SDKs handle cryptographically signing requests, managing errors, and retrying requests automatically. For more information about the Amazon Web Services SDKs, including how to download and install them, see <a href=\"http://aws.amazon.com/tools/\">Tools to Build on Amazon Web Services</a>.</p> </note> <p>See the <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/cloudtrail-user-guide.html\">CloudTrail User Guide</a> for information about the data that is included with each Amazon Web Services API call listed in the log files.</p>"}