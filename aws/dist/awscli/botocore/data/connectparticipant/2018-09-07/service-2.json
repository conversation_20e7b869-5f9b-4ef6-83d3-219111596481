{"version": "2.0", "metadata": {"apiVersion": "2018-09-07", "endpointPrefix": "participant.connect", "jsonVersion": "1.1", "protocol": "rest-json", "protocols": ["rest-json"], "serviceAbbreviation": "Amazon Connect Participant", "serviceFullName": "Amazon Connect Participant Service", "serviceId": "ConnectParticipant", "signatureVersion": "v4", "signingName": "execute-api", "uid": "connectparticipant-2018-09-07", "auth": ["aws.auth#sigv4"]}, "operations": {"CancelParticipantAuthentication": {"name": "CancelParticipantAuthentication", "http": {"method": "POST", "requestUri": "/participant/cancel-authentication"}, "input": {"shape": "CancelParticipantAuthenticationRequest"}, "output": {"shape": "CancelParticipantAuthenticationResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Cancels the authentication session. The opted out branch of the Authenticate Customer flow block will be taken.</p> <note> <p>The current supported channel is chat. This API is not supported for Apple Messages for Business, WhatsApp, or SMS chats.</p> </note>"}, "CompleteAttachmentUpload": {"name": "CompleteAttachmentUpload", "http": {"method": "POST", "requestUri": "/participant/complete-attachment-upload"}, "input": {"shape": "CompleteAttachmentUploadRequest"}, "output": {"shape": "CompleteAttachmentUploadResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}], "documentation": "<p>Allows you to confirm that the attachment has been uploaded using the pre-signed URL provided in StartAttachmentUpload API. A conflict exception is thrown when an attachment with that identifier is already being uploaded.</p> <p>For security recommendations, see <a href=\"https://docs.aws.amazon.com/connect/latest/adminguide/security-best-practices.html#bp-security-chat\">Amazon Connect Chat security best practices</a>.</p> <note> <p> <code>ConnectionToken</code> is used for invoking this API instead of <code>ParticipantToken</code>.</p> </note> <p>The Amazon Connect Participant Service APIs do not use <a href=\"https://docs.aws.amazon.com/general/latest/gr/signature-version-4.html\">Signature Version 4 authentication</a>.</p>"}, "CreateParticipantConnection": {"name": "CreateParticipantConnection", "http": {"method": "POST", "requestUri": "/participant/connection"}, "input": {"shape": "CreateParticipantConnectionRequest"}, "output": {"shape": "CreateParticipantConnectionResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Creates the participant's connection. </p> <p>For security recommendations, see <a href=\"https://docs.aws.amazon.com/connect/latest/adminguide/security-best-practices.html#bp-security-chat\">Amazon Connect Chat security best practices</a>.</p> <note> <p> <code>ParticipantToken</code> is used for invoking this API instead of <code>ConnectionToken</code>.</p> </note> <p>The participant token is valid for the lifetime of the participant – until they are part of a contact.</p> <p>The response URL for <code>WEBSOCKET</code> Type has a connect expiry timeout of 100s. Clients must manually connect to the returned websocket URL and subscribe to the desired topic. </p> <p>For chat, you need to publish the following on the established websocket connection:</p> <p> <code>{\"topic\":\"aws/subscribe\",\"content\":{\"topics\":[\"aws/chat\"]}}</code> </p> <p>Upon websocket URL expiry, as specified in the response ConnectionExpiry parameter, clients need to call this API again to obtain a new websocket URL and perform the same steps as before.</p> <p> <b>Message streaming support</b>: This API can also be used together with the <a href=\"https://docs.aws.amazon.com/connect/latest/APIReference/API_StartContactStreaming.html\">StartContactStreaming</a> API to create a participant connection for chat contacts that are not using a websocket. For more information about message streaming, <a href=\"https://docs.aws.amazon.com/connect/latest/adminguide/chat-message-streaming.html\">Enable real-time chat message streaming</a> in the <i>Amazon Connect Administrator Guide</i>.</p> <p> <b>Feature specifications</b>: For information about feature specifications, such as the allowed number of open websocket connections per participant, see <a href=\"https://docs.aws.amazon.com/connect/latest/adminguide/amazon-connect-service-limits.html#feature-limits\">Feature specifications</a> in the <i>Amazon Connect Administrator Guide</i>. </p> <note> <p>The Amazon Connect Participant Service APIs do not use <a href=\"https://docs.aws.amazon.com/general/latest/gr/signature-version-4.html\">Signature Version 4 authentication</a>.</p> </note>"}, "DescribeView": {"name": "Describe<PERSON>iew", "http": {"method": "GET", "requestUri": "/participant/views/{ViewToken}"}, "input": {"shape": "DescribeViewRequest"}, "output": {"shape": "DescribeViewResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}], "documentation": "<p>Retrieves the view for the specified view token.</p> <p>For security recommendations, see <a href=\"https://docs.aws.amazon.com/connect/latest/adminguide/security-best-practices.html#bp-security-chat\">Amazon Connect Chat security best practices</a>.</p>"}, "DisconnectParticipant": {"name": "DisconnectParticipant", "http": {"method": "POST", "requestUri": "/participant/disconnect"}, "input": {"shape": "DisconnectParticipantRequest"}, "output": {"shape": "DisconnectParticipantResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Disconnects a participant. </p> <p>For security recommendations, see <a href=\"https://docs.aws.amazon.com/connect/latest/adminguide/security-best-practices.html#bp-security-chat\">Amazon Connect Chat security best practices</a>.</p> <note> <p> <code>ConnectionToken</code> is used for invoking this API instead of <code>ParticipantToken</code>.</p> </note> <p>The Amazon Connect Participant Service APIs do not use <a href=\"https://docs.aws.amazon.com/general/latest/gr/signature-version-4.html\">Signature Version 4 authentication</a>.</p>"}, "GetAttachment": {"name": "GetAttachment", "http": {"method": "POST", "requestUri": "/participant/attachment"}, "input": {"shape": "GetAttachmentRequest"}, "output": {"shape": "GetAttachmentResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Provides a pre-signed URL for download of a completed attachment. This is an asynchronous API for use with active contacts.</p> <p>For security recommendations, see <a href=\"https://docs.aws.amazon.com/connect/latest/adminguide/security-best-practices.html#bp-security-chat\">Amazon Connect Chat security best practices</a>.</p> <note> <p> <code>ConnectionToken</code> is used for invoking this API instead of <code>ParticipantToken</code>.</p> </note> <p>The Amazon Connect Participant Service APIs do not use <a href=\"https://docs.aws.amazon.com/general/latest/gr/signature-version-4.html\">Signature Version 4 authentication</a>.</p>"}, "GetAuthenticationUrl": {"name": "GetAuthenticationUrl", "http": {"method": "POST", "requestUri": "/participant/authentication-url"}, "input": {"shape": "GetAuthenticationUrlRequest"}, "output": {"shape": "GetAuthenticationUrlResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Retrieves the AuthenticationUrl for the current authentication session for the AuthenticateCustomer flow block. </p> <p>For security recommendations, see <a href=\"https://docs.aws.amazon.com/connect/latest/adminguide/security-best-practices.html#bp-security-chat\">Amazon Connect Chat security best practices</a>.</p> <note> <ul> <li> <p>This API can only be called within one minute of receiving the authenticationInitiated event.</p> </li> <li> <p>The current supported channel is chat. This API is not supported for Apple Messages for Business, WhatsApp, or SMS chats.</p> </li> </ul> </note>"}, "GetTranscript": {"name": "GetTranscript", "http": {"method": "POST", "requestUri": "/participant/transcript"}, "input": {"shape": "GetTranscriptRequest"}, "output": {"shape": "GetTranscriptResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Retrieves a transcript of the session, including details about any attachments. For information about accessing past chat contact transcripts for a persistent chat, see <a href=\"https://docs.aws.amazon.com/connect/latest/adminguide/chat-persistence.html\">Enable persistent chat</a>. </p> <p>For security recommendations, see <a href=\"https://docs.aws.amazon.com/connect/latest/adminguide/security-best-practices.html#bp-security-chat\">Amazon Connect Chat security best practices</a>.</p> <p>If you have a process that consumes events in the transcript of an chat that has ended, note that chat transcripts contain the following event content types if the event has occurred during the chat session:</p> <ul> <li> <p> <code>application/vnd.amazonaws.connect.event.participant.left</code> </p> </li> <li> <p> <code>application/vnd.amazonaws.connect.event.participant.joined</code> </p> </li> <li> <p> <code>application/vnd.amazonaws.connect.event.chat.ended</code> </p> </li> <li> <p> <code>application/vnd.amazonaws.connect.event.transfer.succeeded</code> </p> </li> <li> <p> <code>application/vnd.amazonaws.connect.event.transfer.failed</code> </p> </li> </ul> <note> <p> <code>ConnectionToken</code> is used for invoking this API instead of <code>ParticipantToken</code>.</p> </note> <p>The Amazon Connect Participant Service APIs do not use <a href=\"https://docs.aws.amazon.com/general/latest/gr/signature-version-4.html\">Signature Version 4 authentication</a>.</p>"}, "SendEvent": {"name": "SendEvent", "http": {"method": "POST", "requestUri": "/participant/event"}, "input": {"shape": "SendEventRequest"}, "output": {"shape": "SendEventResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}], "documentation": "<note> <p>The <code>application/vnd.amazonaws.connect.event.connection.acknowledged</code> ContentType will no longer be supported starting December 31, 2024. This event has been migrated to the <a href=\"https://docs.aws.amazon.com/connect-participant/latest/APIReference/API_CreateParticipantConnection.html\">CreateParticipantConnection</a> API using the <code>ConnectParticipant</code> field.</p> </note> <p>Sends an event. Message receipts are not supported when there are more than two active participants in the chat. Using the SendEvent API for message receipts when a supervisor is barged-in will result in a conflict exception.</p> <p>For security recommendations, see <a href=\"https://docs.aws.amazon.com/connect/latest/adminguide/security-best-practices.html#bp-security-chat\">Amazon Connect Chat security best practices</a>.</p> <note> <p> <code>ConnectionToken</code> is used for invoking this API instead of <code>ParticipantToken</code>.</p> </note> <p>The Amazon Connect Participant Service APIs do not use <a href=\"https://docs.aws.amazon.com/general/latest/gr/signature-version-4.html\">Signature Version 4 authentication</a>.</p>"}, "SendMessage": {"name": "SendMessage", "http": {"method": "POST", "requestUri": "/participant/message"}, "input": {"shape": "SendMessageRequest"}, "output": {"shape": "SendMessageResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}], "documentation": "<p>Sends a message.</p> <p>For security recommendations, see <a href=\"https://docs.aws.amazon.com/connect/latest/adminguide/security-best-practices.html#bp-security-chat\">Amazon Connect Chat security best practices</a>.</p> <note> <p> <code>ConnectionToken</code> is used for invoking this API instead of <code>ParticipantToken</code>.</p> </note> <p>The Amazon Connect Participant Service APIs do not use <a href=\"https://docs.aws.amazon.com/general/latest/gr/signature-version-4.html\">Signature Version 4 authentication</a>.</p>"}, "StartAttachmentUpload": {"name": "StartAttachmentUpload", "http": {"method": "POST", "requestUri": "/participant/start-attachment-upload"}, "input": {"shape": "StartAttachmentUploadRequest"}, "output": {"shape": "StartAttachmentUploadResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Provides a pre-signed Amazon S3 URL in response for uploading the file directly to S3.</p> <p>For security recommendations, see <a href=\"https://docs.aws.amazon.com/connect/latest/adminguide/security-best-practices.html#bp-security-chat\">Amazon Connect Chat security best practices</a>.</p> <note> <p> <code>ConnectionToken</code> is used for invoking this API instead of <code>ParticipantToken</code>.</p> </note> <p>The Amazon Connect Participant Service APIs do not use <a href=\"https://docs.aws.amazon.com/general/latest/gr/signature-version-4.html\">Signature Version 4 authentication</a>.</p>"}}, "shapes": {"ARN": {"type": "string"}, "AccessDeniedException": {"type": "structure", "required": ["Message"], "members": {"Message": {"shape": "Message"}}, "documentation": "<p>You do not have sufficient access to perform this action.</p>", "error": {"httpStatusCode": 403}, "exception": true}, "ArtifactId": {"type": "string", "max": 256, "min": 1}, "ArtifactStatus": {"type": "string", "enum": ["APPROVED", "REJECTED", "IN_PROGRESS"]}, "AttachmentIdList": {"type": "list", "member": {"shape": "ArtifactId"}, "max": 1, "min": 1}, "AttachmentItem": {"type": "structure", "members": {"ContentType": {"shape": "ContentType", "documentation": "<p>Describes the MIME file type of the attachment. For a list of supported file types, see <a href=\"https://docs.aws.amazon.com/connect/latest/adminguide/feature-limits.html\">Feature specifications</a> in the <i>Amazon Connect Administrator Guide</i>.</p>"}, "AttachmentId": {"shape": "ArtifactId", "documentation": "<p>A unique identifier for the attachment.</p>"}, "AttachmentName": {"shape": "AttachmentName", "documentation": "<p>A case-sensitive name of the attachment being uploaded.</p>"}, "Status": {"shape": "ArtifactStatus", "documentation": "<p>Status of the attachment.</p>"}}, "documentation": "<p>The case-insensitive input to indicate standard MIME type that describes the format of the file that will be uploaded.</p>"}, "AttachmentName": {"type": "string", "max": 256, "min": 1}, "AttachmentSizeInBytes": {"type": "long", "min": 1}, "Attachments": {"type": "list", "member": {"shape": "AttachmentItem"}}, "AuthenticationUrl": {"type": "string", "max": 2083, "min": 1}, "Bool": {"type": "boolean"}, "CancelParticipantAuthenticationRequest": {"type": "structure", "required": ["SessionId", "ConnectionToken"], "members": {"SessionId": {"shape": "SessionId", "documentation": "<p>The <code>sessionId</code> provided in the <code>authenticationInitiated</code> event.</p>"}, "ConnectionToken": {"shape": "ParticipantToken", "documentation": "<p>The authentication token associated with the participant's connection.</p>", "location": "header", "locationName": "X-Amz-Bearer"}}}, "CancelParticipantAuthenticationResponse": {"type": "structure", "members": {}}, "ChatContent": {"type": "string", "max": 16384, "min": 1}, "ChatContentType": {"type": "string", "max": 100, "min": 1}, "ChatItemId": {"type": "string", "max": 256, "min": 1}, "ChatItemType": {"type": "string", "enum": ["TYPING", "PARTICIPANT_JOINED", "PARTICIPANT_LEFT", "CHAT_ENDED", "TRANSFER_SUCCEEDED", "TRANSFER_FAILED", "MESSAGE", "EVENT", "ATTACHMENT", "CONNECTION_ACK", "MESSAGE_DELIVERED", "MESSAGE_READ"]}, "ClientToken": {"type": "string", "max": 500}, "CompleteAttachmentUploadRequest": {"type": "structure", "required": ["AttachmentIds", "ClientToken", "ConnectionToken"], "members": {"AttachmentIds": {"shape": "AttachmentIdList", "documentation": "<p>A list of unique identifiers for the attachments.</p>"}, "ClientToken": {"shape": "NonEmptyClientToken", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency of the request. If not provided, the Amazon Web Services SDK populates this field. For more information about idempotency, see <a href=\"https://aws.amazon.com/builders-library/making-retries-safe-with-idempotent-APIs/\">Making retries safe with idempotent APIs</a>.</p>", "idempotencyToken": true}, "ConnectionToken": {"shape": "ParticipantToken", "documentation": "<p>The authentication token associated with the participant's connection.</p>", "location": "header", "locationName": "X-Amz-Bearer"}}}, "CompleteAttachmentUploadResponse": {"type": "structure", "members": {}}, "ConflictException": {"type": "structure", "required": ["Message"], "members": {"Message": {"shape": "Reason"}}, "documentation": "<p>The requested operation conflicts with the current state of a service resource associated with the request. </p>", "error": {"httpStatusCode": 409}, "exception": true}, "ConnectionCredentials": {"type": "structure", "members": {"ConnectionToken": {"shape": "ParticipantToken", "documentation": "<p>The connection token.</p>"}, "Expiry": {"shape": "ISO8601Datetime", "documentation": "<p>The expiration of the token.</p> <p>It's specified in ISO 8601 format: yyyy-MM-ddThh:mm:ss.SSSZ. For example, 2019-11-08T02:41:28.172Z.</p>"}}, "documentation": "<p>Connection credentials. </p>"}, "ConnectionType": {"type": "string", "enum": ["WEBSOCKET", "CONNECTION_CREDENTIALS"]}, "ConnectionTypeList": {"type": "list", "member": {"shape": "ConnectionType"}, "min": 1}, "ContactId": {"type": "string", "max": 256, "min": 1}, "ContentType": {"type": "string", "max": 255, "min": 1}, "CreateParticipantConnectionRequest": {"type": "structure", "required": ["ParticipantToken"], "members": {"Type": {"shape": "ConnectionTypeList", "documentation": "<p>Type of connection information required. If you need <code>CONNECTION_CREDENTIALS</code> along with marking participant as connected, pass <code>CONNECTION_CREDENTIALS</code> in <code>Type</code>.</p>"}, "ParticipantToken": {"shape": "ParticipantToken", "documentation": "<p>This is a header parameter.</p> <p>The ParticipantToken as obtained from <a href=\"https://docs.aws.amazon.com/connect/latest/APIReference/API_StartChatContact.html\">StartChatContact</a> API response.</p>", "location": "header", "locationName": "X-Amz-Bearer"}, "ConnectParticipant": {"shape": "Bool", "documentation": "<p>Amazon Connect Participant is used to mark the participant as connected for customer participant in message streaming, as well as for agent or manager participant in non-streaming chats.</p>"}}}, "CreateParticipantConnectionResponse": {"type": "structure", "members": {"Websocket": {"shape": "Websocket", "documentation": "<p>Creates the participant's websocket connection.</p>"}, "ConnectionCredentials": {"shape": "ConnectionCredentials", "documentation": "<p>Creates the participant's connection credentials. The authentication token associated with the participant's connection.</p>"}}}, "DescribeViewRequest": {"type": "structure", "required": ["ViewToken", "ConnectionToken"], "members": {"ViewToken": {"shape": "ViewToken", "documentation": "<p>An encrypted token originating from the interactive message of a ShowView block operation. Represents the desired view.</p>", "location": "uri", "locationName": "ViewToken"}, "ConnectionToken": {"shape": "ParticipantToken", "documentation": "<p>The connection token.</p>", "location": "header", "locationName": "X-Amz-Bearer"}}}, "DescribeViewResponse": {"type": "structure", "members": {"View": {"shape": "View", "documentation": "<p>A view resource object. Contains metadata and content necessary to render the view.</p>"}}}, "DisconnectParticipantRequest": {"type": "structure", "required": ["ConnectionToken"], "members": {"ClientToken": {"shape": "ClientToken", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency of the request. If not provided, the Amazon Web Services SDK populates this field. For more information about idempotency, see <a href=\"https://aws.amazon.com/builders-library/making-retries-safe-with-idempotent-APIs/\">Making retries safe with idempotent APIs</a>.</p>", "idempotencyToken": true}, "ConnectionToken": {"shape": "ParticipantToken", "documentation": "<p>The authentication token associated with the participant's connection.</p>", "location": "header", "locationName": "X-Amz-Bearer"}}}, "DisconnectParticipantResponse": {"type": "structure", "members": {}}, "DisplayName": {"type": "string", "max": 256, "min": 1}, "GetAttachmentRequest": {"type": "structure", "required": ["AttachmentId", "ConnectionToken"], "members": {"AttachmentId": {"shape": "ArtifactId", "documentation": "<p>A unique identifier for the attachment.</p>"}, "ConnectionToken": {"shape": "ParticipantToken", "documentation": "<p>The authentication token associated with the participant's connection.</p>", "location": "header", "locationName": "X-Amz-Bearer"}, "UrlExpiryInSeconds": {"shape": "URLExpiryInSeconds", "documentation": "<p>The expiration time of the URL in ISO timestamp. It's specified in ISO 8601 format: yyyy-MM-ddThh:mm:ss.SSSZ. For example, 2019-11-08T02:41:28.172Z.</p>"}}}, "GetAttachmentResponse": {"type": "structure", "required": ["AttachmentSizeInBytes"], "members": {"Url": {"shape": "PreSignedAttachmentUrl", "documentation": "<p>This is the pre-signed URL that can be used for uploading the file to Amazon S3 when used in response to <a href=\"https://docs.aws.amazon.com/connect-participant/latest/APIReference/API_StartAttachmentUpload.html\">StartAttachmentUpload</a>.</p>"}, "UrlExpiry": {"shape": "ISO8601Datetime", "documentation": "<p>The expiration time of the URL in ISO timestamp. It's specified in ISO 8601 format: yyyy-MM-ddThh:mm:ss.SSSZ. For example, 2019-11-08T02:41:28.172Z.</p>"}, "AttachmentSizeInBytes": {"shape": "AttachmentSizeInBytes", "documentation": "<p>The size of the attachment in bytes.</p>", "box": true}}}, "GetAuthenticationUrlRequest": {"type": "structure", "required": ["SessionId", "RedirectUri", "ConnectionToken"], "members": {"SessionId": {"shape": "SessionId", "documentation": "<p>The sessionId provided in the authenticationInitiated event.</p>"}, "RedirectUri": {"shape": "RedirectURI", "documentation": "<p>The URL where the customer will be redirected after Amazon Cognito authorizes the user.</p>"}, "ConnectionToken": {"shape": "ParticipantToken", "documentation": "<p>The authentication token associated with the participant's connection.</p>", "location": "header", "locationName": "X-Amz-Bearer"}}}, "GetAuthenticationUrlResponse": {"type": "structure", "members": {"AuthenticationUrl": {"shape": "AuthenticationUrl", "documentation": "<p>The URL where the customer will sign in to the identity provider. This URL contains the authorize endpoint for the Cognito UserPool used in the authentication.</p>"}}}, "GetTranscriptRequest": {"type": "structure", "required": ["ConnectionToken"], "members": {"ContactId": {"shape": "ContactId", "documentation": "<p>The contactId from the current contact chain for which transcript is needed.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in the page. Default: 10. </p>", "box": true}, "NextToken": {"shape": "NextToken", "documentation": "<p>The pagination token. Use the value returned previously in the next subsequent request to retrieve the next set of results.</p>"}, "ScanDirection": {"shape": "ScanDirection", "documentation": "<p>The direction from StartPosition from which to retrieve message. Default: BACKWARD when no StartPosition is provided, FORWARD with StartPosition. </p>"}, "SortOrder": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The sort order for the records. Default: DESCENDING.</p>"}, "StartPosition": {"shape": "StartPosition", "documentation": "<p>A filtering option for where to start.</p>"}, "ConnectionToken": {"shape": "ParticipantToken", "documentation": "<p>The authentication token associated with the participant's connection.</p>", "location": "header", "locationName": "X-Amz-Bearer"}}}, "GetTranscriptResponse": {"type": "structure", "members": {"InitialContactId": {"shape": "ContactId", "documentation": "<p>The initial contact ID for the contact. </p>"}, "Transcript": {"shape": "Transcript", "documentation": "<p>The list of messages in the session.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The pagination token. Use the value returned previously in the next subsequent request to retrieve the next set of results.</p>"}}}, "ISO8601Datetime": {"type": "string"}, "Instant": {"type": "string", "max": 100, "min": 1}, "InternalServerException": {"type": "structure", "required": ["Message"], "members": {"Message": {"shape": "Message"}}, "documentation": "<p>This exception occurs when there is an internal failure in the Amazon Connect service.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true}, "Item": {"type": "structure", "members": {"AbsoluteTime": {"shape": "Instant", "documentation": "<p>The time when the message or event was sent.</p> <p>It's specified in ISO 8601 format: yyyy-MM-ddThh:mm:ss.SSSZ. For example, 2019-11-08T02:41:28.172Z.</p>"}, "Content": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The content of the message or event.</p>"}, "ContentType": {"shape": "ChatContentType", "documentation": "<p>The type of content of the item.</p>"}, "Id": {"shape": "ChatItemId", "documentation": "<p>The ID of the item.</p>"}, "Type": {"shape": "ChatItemType", "documentation": "<p>Type of the item: message or event. </p>"}, "ParticipantId": {"shape": "ParticipantId", "documentation": "<p>The ID of the sender in the session.</p>"}, "DisplayName": {"shape": "DisplayName", "documentation": "<p>The chat display name of the sender.</p>"}, "ParticipantRole": {"shape": "ParticipantRole", "documentation": "<p>The role of the sender. For example, is it a customer, agent, or system.</p>"}, "Attachments": {"shape": "Attachments", "documentation": "<p>Provides information about the attachments.</p>"}, "MessageMetadata": {"shape": "MessageMetadata", "documentation": "<p>The metadata related to the message. Currently this supports only information related to message receipts.</p>"}, "RelatedContactId": {"shape": "ContactId", "documentation": "<p>The contactId on which the transcript item was originally sent. This field is only populated for persistent chats when the transcript item is from the past chat session. For more information, see <a href=\"https://docs.aws.amazon.com/connect/latest/adminguide/chat-persistence.html\">Enable persistent chat</a>.</p>"}, "ContactId": {"shape": "ContactId", "documentation": "<p>The contactId on which the transcript item was originally sent. This field is populated only when the transcript item is from the current chat session.</p>"}}, "documentation": "<p>An item - message or event - that has been sent. </p>"}, "MaxResults": {"type": "integer", "max": 100, "min": 0}, "Message": {"type": "string"}, "MessageMetadata": {"type": "structure", "members": {"MessageId": {"shape": "ChatItemId", "documentation": "<p>The identifier of the message that contains the metadata information. </p>"}, "Receipts": {"shape": "Receipts", "documentation": "<p>The list of receipt information for a message for different recipients.</p>"}}, "documentation": "<p>Contains metadata related to a message.</p>"}, "MostRecent": {"type": "integer", "max": 100, "min": 0}, "NextToken": {"type": "string", "max": 1000, "min": 1}, "NonEmptyClientToken": {"type": "string", "max": 500, "min": 1}, "ParticipantId": {"type": "string", "max": 256, "min": 1}, "ParticipantRole": {"type": "string", "enum": ["AGENT", "CUSTOMER", "SYSTEM", "CUSTOM_BOT", "SUPERVISOR"]}, "ParticipantToken": {"type": "string", "max": 1000, "min": 1}, "PreSignedAttachmentUrl": {"type": "string", "max": 2000, "min": 1}, "PreSignedConnectionUrl": {"type": "string", "max": 2000, "min": 1}, "Reason": {"type": "string", "max": 2000, "min": 1}, "Receipt": {"type": "structure", "members": {"DeliveredTimestamp": {"shape": "Instant", "documentation": "<p>The time when the message was delivered to the recipient.</p>"}, "ReadTimestamp": {"shape": "Instant", "documentation": "<p>The time when the message was read by the recipient.</p>"}, "RecipientParticipantId": {"shape": "ParticipantId", "documentation": "<p>The identifier of the recipient of the message. </p>"}}, "documentation": "<p>The receipt for the message delivered to the recipient.</p>"}, "Receipts": {"type": "list", "member": {"shape": "Receipt"}}, "RedirectURI": {"type": "string", "max": 1024, "min": 1}, "ResourceId": {"type": "string"}, "ResourceNotFoundException": {"type": "structure", "members": {"Message": {"shape": "Message"}, "ResourceId": {"shape": "ResourceId", "documentation": "<p>The identifier of the resource.</p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>The type of Amazon Connect resource.</p>"}}, "documentation": "<p>The resource was not found.</p>", "error": {"httpStatusCode": 404}, "exception": true}, "ResourceType": {"type": "string", "enum": ["CONTACT", "CONTACT_FLOW", "INSTANCE", "PARTICIPANT", "HIERARCHY_LEVEL", "HIERARCHY_GROUP", "USER", "PHONE_NUMBER"]}, "ScanDirection": {"type": "string", "enum": ["FORWARD", "BACKWARD"]}, "SendEventRequest": {"type": "structure", "required": ["ContentType", "ConnectionToken"], "members": {"ContentType": {"shape": "ChatContentType", "documentation": "<p>The content type of the request. Supported types are:</p> <ul> <li> <p>application/vnd.amazonaws.connect.event.typing</p> </li> <li> <p>application/vnd.amazonaws.connect.event.connection.acknowledged (will be deprecated on December 31, 2024) </p> </li> <li> <p>application/vnd.amazonaws.connect.event.message.delivered</p> </li> <li> <p>application/vnd.amazonaws.connect.event.message.read</p> </li> </ul>"}, "Content": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The content of the event to be sent (for example, message text). For content related to message receipts, this is supported in the form of a JSON string.</p> <p>Sample Content: \"{\\\"messageId\\\":\\\"11111111-aaaa-bbbb-cccc-EXAMPLE01234\\\"}\"</p>"}, "ClientToken": {"shape": "ClientToken", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency of the request. If not provided, the Amazon Web Services SDK populates this field. For more information about idempotency, see <a href=\"https://aws.amazon.com/builders-library/making-retries-safe-with-idempotent-APIs/\">Making retries safe with idempotent APIs</a>.</p>", "idempotencyToken": true}, "ConnectionToken": {"shape": "ParticipantToken", "documentation": "<p>The authentication token associated with the participant's connection.</p>", "location": "header", "locationName": "X-Amz-Bearer"}}}, "SendEventResponse": {"type": "structure", "members": {"Id": {"shape": "ChatItemId", "documentation": "<p>The ID of the response.</p>"}, "AbsoluteTime": {"shape": "Instant", "documentation": "<p>The time when the event was sent.</p> <p>It's specified in ISO 8601 format: yyyy-MM-ddThh:mm:ss.SSSZ. For example, 2019-11-08T02:41:28.172Z.</p>"}}}, "SendMessageRequest": {"type": "structure", "required": ["ContentType", "Content", "ConnectionToken"], "members": {"ContentType": {"shape": "ChatContentType", "documentation": "<p>The type of the content. Supported types are <code>text/plain</code>, <code>text/markdown</code>, <code>application/json</code>, and <code>application/vnd.amazonaws.connect.message.interactive.response</code>.</p>"}, "Content": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The content of the message. </p> <ul> <li> <p>For <code>text/plain</code> and <code>text/markdown</code>, the Length Constraints are Minimum of 1, Maximum of 1024. </p> </li> <li> <p>For <code>application/json</code>, the Length Constraints are Minimum of 1, Maximum of 12000. </p> </li> <li> <p>For <code>application/vnd.amazonaws.connect.message.interactive.response</code>, the Length Constraints are Minimum of 1, Maximum of 12288.</p> </li> </ul>"}, "ClientToken": {"shape": "ClientToken", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency of the request. If not provided, the Amazon Web Services SDK populates this field. For more information about idempotency, see <a href=\"https://aws.amazon.com/builders-library/making-retries-safe-with-idempotent-APIs/\">Making retries safe with idempotent APIs</a>.</p>", "idempotencyToken": true}, "ConnectionToken": {"shape": "ParticipantToken", "documentation": "<p>The authentication token associated with the connection.</p>", "location": "header", "locationName": "X-Amz-Bearer"}}}, "SendMessageResponse": {"type": "structure", "members": {"Id": {"shape": "ChatItemId", "documentation": "<p>The ID of the message.</p>"}, "AbsoluteTime": {"shape": "Instant", "documentation": "<p>The time when the message was sent.</p> <p>It's specified in ISO 8601 format: yyyy-MM-ddThh:mm:ss.SSSZ. For example, 2019-11-08T02:41:28.172Z.</p>"}}}, "ServiceQuotaExceededException": {"type": "structure", "required": ["Message"], "members": {"Message": {"shape": "Message"}}, "documentation": "<p>The number of attachments per contact exceeds the quota.</p>", "error": {"httpStatusCode": 402}, "exception": true}, "SessionId": {"type": "string", "max": 36, "min": 36}, "SortKey": {"type": "string", "enum": ["DESCENDING", "ASCENDING"]}, "StartAttachmentUploadRequest": {"type": "structure", "required": ["ContentType", "AttachmentSizeInBytes", "AttachmentName", "ClientToken", "ConnectionToken"], "members": {"ContentType": {"shape": "ContentType", "documentation": "<p>Describes the MIME file type of the attachment. For a list of supported file types, see <a href=\"https://docs.aws.amazon.com/connect/latest/adminguide/feature-limits.html\">Feature specifications</a> in the <i>Amazon Connect Administrator Guide</i>.</p>"}, "AttachmentSizeInBytes": {"shape": "AttachmentSizeInBytes", "documentation": "<p>The size of the attachment in bytes.</p>"}, "AttachmentName": {"shape": "AttachmentName", "documentation": "<p>A case-sensitive name of the attachment being uploaded.</p>"}, "ClientToken": {"shape": "NonEmptyClientToken", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency of the request. If not provided, the Amazon Web Services SDK populates this field. For more information about idempotency, see <a href=\"https://aws.amazon.com/builders-library/making-retries-safe-with-idempotent-APIs/\">Making retries safe with idempotent APIs</a>.</p>", "idempotencyToken": true}, "ConnectionToken": {"shape": "ParticipantToken", "documentation": "<p>The authentication token associated with the participant's connection.</p>", "location": "header", "locationName": "X-Amz-Bearer"}}}, "StartAttachmentUploadResponse": {"type": "structure", "members": {"AttachmentId": {"shape": "ArtifactId", "documentation": "<p>A unique identifier for the attachment.</p>"}, "UploadMetadata": {"shape": "UploadMetadata", "documentation": "<p>The headers to be provided while uploading the file to the URL.</p>"}}}, "StartPosition": {"type": "structure", "members": {"Id": {"shape": "ChatItemId", "documentation": "<p>The ID of the message or event where to start. </p>"}, "AbsoluteTime": {"shape": "Instant", "documentation": "<p>The time in ISO format where to start.</p> <p>It's specified in ISO 8601 format: yyyy-MM-ddThh:mm:ss.SSSZ. For example, 2019-11-08T02:41:28.172Z.</p>"}, "MostRecent": {"shape": "MostRecent", "documentation": "<p>The start position of the most recent message where you want to start. </p>"}}, "documentation": "<p>A filtering option for where to start. For example, if you sent 100 messages, start with message 50. </p>"}, "ThrottlingException": {"type": "structure", "required": ["Message"], "members": {"Message": {"shape": "Message"}}, "documentation": "<p>The request was denied due to request throttling.</p>", "error": {"httpStatusCode": 429}, "exception": true}, "Transcript": {"type": "list", "member": {"shape": "<PERSON><PERSON>"}}, "URLExpiryInSeconds": {"type": "integer", "max": 300, "min": 5}, "UploadMetadata": {"type": "structure", "members": {"Url": {"shape": "UploadMetadataUrl", "documentation": "<p>This is the pre-signed URL that can be used for uploading the file to Amazon S3 when used in response to <a href=\"https://docs.aws.amazon.com/connect-participant/latest/APIReference/API_StartAttachmentUpload.html\">StartAttachmentUpload</a>.</p>"}, "UrlExpiry": {"shape": "ISO8601Datetime", "documentation": "<p>The expiration time of the URL in ISO timestamp. It's specified in ISO 8601 format: yyyy-MM-ddThh:mm:ss.SSSZ. For example, 2019-11-08T02:41:28.172Z.</p>"}, "HeadersToInclude": {"shape": "UploadMetadataSignedHeaders", "documentation": "<p>The headers to be provided while uploading the file to the URL.</p>"}}, "documentation": "<p>Fields to be used while uploading the attachment.</p>"}, "UploadMetadataSignedHeaders": {"type": "map", "key": {"shape": "UploadMetadataSignedHeadersKey"}, "value": {"shape": "UploadMetadataSignedHeadersValue"}}, "UploadMetadataSignedHeadersKey": {"type": "string", "max": 128, "min": 1}, "UploadMetadataSignedHeadersValue": {"type": "string", "max": 256, "min": 1}, "UploadMetadataUrl": {"type": "string", "max": 2000, "min": 1}, "ValidationException": {"type": "structure", "required": ["Message"], "members": {"Message": {"shape": "Reason"}}, "documentation": "<p>The input fails to satisfy the constraints specified by Amazon Connect.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "View": {"type": "structure", "members": {"Id": {"shape": "ViewId", "documentation": "<p>The identifier of the view.</p>"}, "Arn": {"shape": "ARN", "documentation": "<p>The Amazon Resource Name (ARN) of the view.</p>"}, "Name": {"shape": "ViewName", "documentation": "<p>The name of the view.</p>"}, "Version": {"shape": "ViewVersion", "documentation": "<p>The current version of the view.</p>"}, "Content": {"shape": "ViewContent", "documentation": "<p>View content containing all content necessary to render a view except for runtime input data.</p>"}}, "documentation": "<p>A view resource object. Contains metadata and content necessary to render the view.</p>"}, "ViewAction": {"type": "string", "max": 255, "min": 1, "pattern": "^([\\p{L}\\p{N}_.:\\/=+\\-@()']+[\\p{L}\\p{Z}\\p{N}_.:\\/=+\\-@()']*)$", "sensitive": true}, "ViewActions": {"type": "list", "member": {"shape": "ViewAction"}}, "ViewContent": {"type": "structure", "members": {"InputSchema": {"shape": "ViewInputSchema", "documentation": "<p>The schema representing the input data that the view template must be supplied to render.</p>"}, "Template": {"shape": "ViewTemplate", "documentation": "<p>The view template representing the structure of the view.</p>"}, "Actions": {"shape": "ViewActions", "documentation": "<p>A list of actions possible from the view</p>"}}, "documentation": "<p>View content containing all content necessary to render a view except for runtime input data.</p>"}, "ViewId": {"type": "string", "max": 500, "min": 1, "pattern": "^[a-zA-Z0-9\\_\\-:\\/$]+$"}, "ViewInputSchema": {"type": "string", "sensitive": true}, "ViewName": {"type": "string", "max": 255, "min": 1, "pattern": "^([\\p{L}\\p{N}_.:\\/=+\\-@()']+[\\p{L}\\p{Z}\\p{N}_.:\\/=+\\-@()']*)$", "sensitive": true}, "ViewTemplate": {"type": "string", "sensitive": true}, "ViewToken": {"type": "string", "max": 1000, "min": 1}, "ViewVersion": {"type": "integer"}, "Websocket": {"type": "structure", "members": {"Url": {"shape": "PreSignedConnectionUrl", "documentation": "<p>The URL of the websocket.</p>"}, "ConnectionExpiry": {"shape": "ISO8601Datetime", "documentation": "<p>The URL expiration timestamp in ISO date format.</p> <p>It's specified in ISO 8601 format: yyyy-MM-ddThh:mm:ss.SSSZ. For example, 2019-11-08T02:41:28.172Z.</p>"}}, "documentation": "<p>The websocket for the participant's connection.</p>"}}, "documentation": "<ul> <li> <p> <a href=\"https://docs.aws.amazon.com/connect/latest/APIReference/API_Operations_Amazon_Connect_Participant_Service.html\">Participant Service actions</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/connect/latest/APIReference/API_Types_Amazon_Connect_Participant_Service.html\">Participant Service data types</a> </p> </li> </ul> <p>Amazon Connect is an easy-to-use omnichannel cloud contact center service that enables companies of any size to deliver superior customer service at a lower cost. Amazon Connect communications capabilities make it easy for companies to deliver personalized interactions across communication channels, including chat. </p> <p>Use the Amazon Connect Participant Service to manage participants (for example, agents, customers, and managers listening in), and to send messages and events within a chat contact. The APIs in the service enable the following: sending chat messages, attachment sharing, managing a participant's connection state and message events, and retrieving chat transcripts.</p>"}