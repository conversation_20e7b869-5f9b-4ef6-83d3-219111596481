{"version": "2.0", "metadata": {"apiVersion": "2018-03-01", "endpointPrefix": "fsx", "jsonVersion": "1.1", "protocol": "json", "protocols": ["json"], "serviceFullName": "Amazon FSx", "serviceId": "FSx", "signatureVersion": "v4", "signingName": "fsx", "targetPrefix": "AWSSimbaAPIService_v20180301", "uid": "fsx-2018-03-01", "auth": ["aws.auth#sigv4"]}, "operations": {"AssociateFileSystemAliases": {"name": "AssociateFileSystemAliases", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AssociateFileSystemAliasesRequest"}, "output": {"shape": "AssociateFileSystemAliasesResponse"}, "errors": [{"shape": "BadRequest"}, {"shape": "FileSystemNotFound"}, {"shape": "InternalServerError"}], "documentation": "<p>Use this action to associate one or more Domain Name Server (DNS) aliases with an existing Amazon FSx for Windows File Server file system. A file system can have a maximum of 50 DNS aliases associated with it at any one time. If you try to associate a DNS alias that is already associated with the file system, FSx takes no action on that alias in the request. For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/WindowsGuide/managing-dns-aliases.html\">Working with DNS Aliases</a> and <a href=\"https://docs.aws.amazon.com/fsx/latest/WindowsGuide/walkthrough05-file-system-custom-CNAME.html\">Walkthrough 5: Using DNS aliases to access your file system</a>, including additional steps you must take to be able to access your file system using a DNS alias.</p> <p>The system response shows the DNS aliases that Amazon FSx is attempting to associate with the file system. Use the API operation to monitor the status of the aliases Amazon FSx is associating with the file system.</p>"}, "CancelDataRepositoryTask": {"name": "CancelDataRepositoryTask", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CancelDataRepositoryTaskRequest"}, "output": {"shape": "CancelDataRepositoryTaskResponse"}, "errors": [{"shape": "BadRequest"}, {"shape": "UnsupportedOperation"}, {"shape": "DataRepositoryTaskNotFound"}, {"shape": "DataRepositoryTaskEnded"}, {"shape": "InternalServerError"}], "documentation": "<p>Cancels an existing Amazon FSx for Lustre data repository task if that task is in either the <code>PENDING</code> or <code>EXECUTING</code> state. When you cancel an export task, Amazon FSx does the following.</p> <ul> <li> <p>Any files that FSx has already exported are not reverted.</p> </li> <li> <p>FSx continues to export any files that are in-flight when the cancel operation is received.</p> </li> <li> <p>FSx does not export any files that have not yet been exported.</p> </li> </ul> <p>For a release task, Amazon FSx will stop releasing files upon cancellation. Any files that have already been released will remain in the released state.</p>", "idempotent": true}, "CopyBackup": {"name": "CopyBackup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CopyBackupRequest"}, "output": {"shape": "CopyBackupResponse"}, "errors": [{"shape": "BadRequest"}, {"shape": "BackupNotFound"}, {"shape": "ServiceLimitExceeded"}, {"shape": "UnsupportedOperation"}, {"shape": "IncompatibleParameterError"}, {"shape": "InternalServerError"}, {"shape": "InvalidSourceKmsKey"}, {"shape": "InvalidDestinationKmsKey"}, {"shape": "InvalidRegion"}, {"shape": "SourceBackupUnavailable"}, {"shape": "IncompatibleRegionForMultiAZ"}], "documentation": "<p>Copies an existing backup within the same Amazon Web Services account to another Amazon Web Services Region (cross-Region copy) or within the same Amazon Web Services Region (in-Region copy). You can have up to five backup copy requests in progress to a single destination Region per account.</p> <p>You can use cross-Region backup copies for cross-Region disaster recovery. You can periodically take backups and copy them to another Region so that in the event of a disaster in the primary Region, you can restore from backup and recover availability quickly in the other Region. You can make cross-Region copies only within your Amazon Web Services partition. A partition is a grouping of Regions. Amazon Web Services currently has three partitions: <code>aws</code> (Standard Regions), <code>aws-cn</code> (China Regions), and <code>aws-us-gov</code> (Amazon Web Services GovCloud [US] Regions).</p> <p>You can also use backup copies to clone your file dataset to another Region or within the same Region.</p> <p>You can use the <code>SourceRegion</code> parameter to specify the Amazon Web Services Region from which the backup will be copied. For example, if you make the call from the <code>us-west-1</code> Region and want to copy a backup from the <code>us-east-2</code> Region, you specify <code>us-east-2</code> in the <code>SourceRegion</code> parameter to make a cross-Region copy. If you don't specify a Region, the backup copy is created in the same Region where the request is sent from (in-Region copy).</p> <p>For more information about creating backup copies, see <a href=\"https://docs.aws.amazon.com/fsx/latest/WindowsGuide/using-backups.html#copy-backups\"> Copying backups</a> in the <i>Amazon FSx for Windows User Guide</i>, <a href=\"https://docs.aws.amazon.com/fsx/latest/LustreGuide/using-backups-fsx.html#copy-backups\">Copying backups</a> in the <i>Amazon FSx for Lustre User Guide</i>, and <a href=\"https://docs.aws.amazon.com/fsx/latest/OpenZFSGuide/using-backups.html#copy-backups\">Copying backups</a> in the <i>Amazon FSx for OpenZFS User Guide</i>.</p>", "idempotent": true}, "CopySnapshotAndUpdateVolume": {"name": "CopySnapshotAndUpdateVolume", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CopySnapshotAndUpdateVolumeRequest"}, "output": {"shape": "CopySnapshotAndUpdateVolumeResponse"}, "errors": [{"shape": "BadRequest"}, {"shape": "IncompatibleParameterError"}, {"shape": "InternalServerError"}, {"shape": "ServiceLimitExceeded"}], "documentation": "<p>Updates an existing volume by using a snapshot from another Amazon FSx for OpenZFS file system. For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/OpenZFSGuide/on-demand-replication.html\">on-demand data replication</a> in the Amazon FSx for OpenZFS User Guide.</p>", "idempotent": true}, "CreateAndAttachS3AccessPoint": {"name": "CreateAndAttachS3AccessPoint", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateAndAttachS3AccessPointRequest"}, "output": {"shape": "CreateAndAttachS3AccessPointResponse"}, "errors": [{"shape": "BadRequest"}, {"shape": "IncompatibleParameterError"}, {"shape": "InternalServerError"}, {"shape": "UnsupportedOperation"}, {"shape": "VolumeNotFound"}, {"shape": "InvalidAccessPoint"}, {"shape": "InvalidRequest"}, {"shape": "AccessPointAlreadyOwnedByYou"}, {"shape": "TooManyAccessPoints"}], "documentation": "<p>Creates an S3 access point and attaches it to an Amazon FSx volume. For FSx for OpenZFS file systems, the volume must be hosted on a high-availability file system, either Single-AZ or Multi-AZ. For more information, see <a href=\"fsx/latest/OpenZFSGuide/s3accesspoints-for-FSx.html\">Accessing your data using access points</a> in the Amazon FSx for OpenZFS User Guide. </p> <p>The requester requires the following permissions to perform these actions:</p> <ul> <li> <p> <code>fsx:CreateAndAttachS3AccessPoint</code> </p> </li> <li> <p> <code>s3:CreateAccessPoint</code> </p> </li> <li> <p> <code>s3:GetAccessPoint</code> </p> </li> <li> <p> <code>s3:PutAccessPointPolicy</code> </p> </li> <li> <p> <code>s3:DeleteAccessPoint</code> </p> </li> </ul> <p>The following actions are related to <code>CreateAndAttachS3AccessPoint</code>:</p> <ul> <li> <p> <a>DescribeS3AccessPointAttachments</a> </p> </li> <li> <p> <a>DetachAndDeleteS3AccessPoint</a> </p> </li> </ul>"}, "CreateBackup": {"name": "CreateBackup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateBackupRequest"}, "output": {"shape": "CreateBackupResponse"}, "errors": [{"shape": "BadRequest"}, {"shape": "UnsupportedOperation"}, {"shape": "FileSystemNotFound"}, {"shape": "VolumeNotFound"}, {"shape": "BackupInProgress"}, {"shape": "IncompatibleParameterError"}, {"shape": "ServiceLimitExceeded"}, {"shape": "InternalServerError"}], "documentation": "<p>Creates a backup of an existing Amazon FSx for Windows File Server file system, Amazon FSx for Lustre file system, Amazon FSx for NetApp ONTAP volume, or Amazon FSx for OpenZFS file system. We recommend creating regular backups so that you can restore a file system or volume from a backup if an issue arises with the original file system or volume.</p> <p>For Amazon FSx for Lustre file systems, you can create a backup only for file systems that have the following configuration:</p> <ul> <li> <p>A Persistent deployment type</p> </li> <li> <p>Are <i>not</i> linked to a data repository</p> </li> </ul> <p>For more information about backups, see the following:</p> <ul> <li> <p>For Amazon FSx for Lustre, see <a href=\"https://docs.aws.amazon.com/fsx/latest/LustreGuide/using-backups-fsx.html\">Working with FSx for Lustre backups</a>.</p> </li> <li> <p>For Amazon FSx for Windows, see <a href=\"https://docs.aws.amazon.com/fsx/latest/WindowsGuide/using-backups.html\">Working with FSx for Windows backups</a>.</p> </li> <li> <p>For Amazon FSx for NetApp ONTAP, see <a href=\"https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/using-backups.html\">Working with FSx for NetApp ONTAP backups</a>.</p> </li> <li> <p>For Amazon FSx for OpenZFS, see <a href=\"https://docs.aws.amazon.com/fsx/latest/OpenZFSGuide/using-backups.html\">Working with FSx for OpenZFS backups</a>.</p> </li> </ul> <p>If a backup with the specified client request token exists and the parameters match, this operation returns the description of the existing backup. If a backup with the specified client request token exists and the parameters don't match, this operation returns <code>IncompatibleParameterError</code>. If a backup with the specified client request token doesn't exist, <code>CreateBackup</code> does the following: </p> <ul> <li> <p>Creates a new Amazon FSx backup with an assigned ID, and an initial lifecycle state of <code>CREATING</code>.</p> </li> <li> <p>Returns the description of the backup.</p> </li> </ul> <p>By using the idempotent operation, you can retry a <code>CreateBackup</code> operation without the risk of creating an extra backup. This approach can be useful when an initial call fails in a way that makes it unclear whether a backup was created. If you use the same client request token and the initial call created a backup, the operation returns a successful result because all the parameters are the same.</p> <p>The <code>CreateBackup</code> operation returns while the backup's lifecycle state is still <code>CREATING</code>. You can check the backup creation status by calling the <a href=\"https://docs.aws.amazon.com/fsx/latest/APIReference/API_DescribeBackups.html\">DescribeBackups</a> operation, which returns the backup state along with other information.</p>", "idempotent": true}, "CreateDataRepositoryAssociation": {"name": "CreateDataRepositoryAssociation", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateDataRepositoryAssociationRequest"}, "output": {"shape": "CreateDataRepositoryAssociationResponse"}, "errors": [{"shape": "BadRequest"}, {"shape": "UnsupportedOperation"}, {"shape": "FileSystemNotFound"}, {"shape": "IncompatibleParameterError"}, {"shape": "ServiceLimitExceeded"}, {"shape": "InternalServerError"}], "documentation": "<p>Creates an Amazon FSx for Lustre data repository association (DRA). A data repository association is a link between a directory on the file system and an Amazon S3 bucket or prefix. You can have a maximum of 8 data repository associations on a file system. Data repository associations are supported on all FSx for Lustre 2.12 and 2.15 file systems, excluding <code>scratch_1</code> deployment type.</p> <p>Each data repository association must have a unique Amazon FSx file system directory and a unique S3 bucket or prefix associated with it. You can configure a data repository association for automatic import only, for automatic export only, or for both. To learn more about linking a data repository to your file system, see <a href=\"https://docs.aws.amazon.com/fsx/latest/LustreGuide/create-dra-linked-data-repo.html\">Linking your file system to an S3 bucket</a>.</p> <note> <p> <code>CreateDataRepositoryAssociation</code> isn't supported on Amazon File Cache resources. To create a DRA on Amazon File Cache, use the <code>CreateFileCache</code> operation.</p> </note>", "idempotent": true}, "CreateDataRepositoryTask": {"name": "CreateDataRepositoryTask", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateDataRepositoryTaskRequest"}, "output": {"shape": "CreateDataRepositoryTaskResponse"}, "errors": [{"shape": "BadRequest"}, {"shape": "UnsupportedOperation"}, {"shape": "FileSystemNotFound"}, {"shape": "IncompatibleParameterError"}, {"shape": "ServiceLimitExceeded"}, {"shape": "InternalServerError"}, {"shape": "DataRepositoryTaskExecuting"}], "documentation": "<p>Creates an Amazon FSx for Lustre data repository task. A <code>CreateDataRepositoryTask</code> operation will fail if a data repository is not linked to the FSx file system.</p> <p>You use import and export data repository tasks to perform bulk operations between your FSx for Lustre file system and its linked data repositories. An example of a data repository task is exporting any data and metadata changes, including POSIX metadata, to files, directories, and symbolic links (symlinks) from your FSx file system to a linked data repository.</p> <p>You use release data repository tasks to release data from your file system for files that are exported to S3. The metadata of released files remains on the file system so users or applications can still access released files by reading the files again, which will restore data from Amazon S3 to the FSx for Lustre file system.</p> <p>To learn more about data repository tasks, see <a href=\"https://docs.aws.amazon.com/fsx/latest/LustreGuide/data-repository-tasks.html\">Data Repository Tasks</a>. To learn more about linking a data repository to your file system, see <a href=\"https://docs.aws.amazon.com/fsx/latest/LustreGuide/create-dra-linked-data-repo.html\">Linking your file system to an S3 bucket</a>.</p>", "idempotent": true}, "CreateFileCache": {"name": "CreateFile<PERSON>ache", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateFileCacheRequest"}, "output": {"shape": "CreateFileCacheResponse"}, "errors": [{"shape": "BadRequest"}, {"shape": "IncompatibleParameterError"}, {"shape": "InvalidNetworkSettings"}, {"shape": "InvalidPerUnitStorageThroughput"}, {"shape": "ServiceLimitExceeded"}, {"shape": "InternalServerError"}, {"shape": "MissingFileCacheConfiguration"}], "documentation": "<p>Creates a new Amazon File Cache resource.</p> <p>You can use this operation with a client request token in the request that Amazon File Cache uses to ensure idempotent creation. If a cache with the specified client request token exists and the parameters match, <code>CreateFileCache</code> returns the description of the existing cache. If a cache with the specified client request token exists and the parameters don't match, this call returns <code>IncompatibleParameterError</code>. If a file cache with the specified client request token doesn't exist, <code>CreateFileCache</code> does the following: </p> <ul> <li> <p>Creates a new, empty Amazon File Cache resource with an assigned ID, and an initial lifecycle state of <code>CREATING</code>.</p> </li> <li> <p>Returns the description of the cache in JSON format.</p> </li> </ul> <note> <p>The <code>CreateFileCache</code> call returns while the cache's lifecycle state is still <code>CREATING</code>. You can check the cache creation status by calling the <a href=\"https://docs.aws.amazon.com/fsx/latest/APIReference/API_DescribeFileCaches.html\">DescribeFileCaches</a> operation, which returns the cache state along with other information.</p> </note>", "idempotent": true}, "CreateFileSystem": {"name": "CreateFileSystem", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateFileSystemRequest"}, "output": {"shape": "CreateFileSystemResponse"}, "errors": [{"shape": "BadRequest"}, {"shape": "ActiveDirectoryError"}, {"shape": "IncompatibleParameterError"}, {"shape": "InvalidImportPath"}, {"shape": "InvalidExportPath"}, {"shape": "InvalidNetworkSettings"}, {"shape": "InvalidPerUnitStorageThroughput"}, {"shape": "ServiceLimitExceeded"}, {"shape": "InternalServerError"}, {"shape": "MissingFileSystemConfiguration"}], "documentation": "<p>Creates a new, empty Amazon FSx file system. You can create the following supported Amazon FSx file systems using the <code>CreateFileSystem</code> API operation:</p> <ul> <li> <p>Amazon FSx for Lustre</p> </li> <li> <p>Amazon FSx for NetApp ONTAP</p> </li> <li> <p>Amazon FSx for OpenZFS</p> </li> <li> <p>Amazon FSx for Windows File Server</p> </li> </ul> <p>This operation requires a client request token in the request that Amazon FSx uses to ensure idempotent creation. This means that calling the operation multiple times with the same client request token has no effect. By using the idempotent operation, you can retry a <code>CreateFileSystem</code> operation without the risk of creating an extra file system. This approach can be useful when an initial call fails in a way that makes it unclear whether a file system was created. Examples are if a transport level timeout occurred, or your connection was reset. If you use the same client request token and the initial call created a file system, the client receives success as long as the parameters are the same.</p> <p>If a file system with the specified client request token exists and the parameters match, <code>CreateFileSystem</code> returns the description of the existing file system. If a file system with the specified client request token exists and the parameters don't match, this call returns <code>IncompatibleParameterError</code>. If a file system with the specified client request token doesn't exist, <code>CreateFileSystem</code> does the following:</p> <ul> <li> <p>Creates a new, empty Amazon FSx file system with an assigned ID, and an initial lifecycle state of <code>CREATING</code>.</p> </li> <li> <p>Returns the description of the file system in JSON format.</p> </li> </ul> <note> <p>The <code>CreateFileSystem</code> call returns while the file system's lifecycle state is still <code>CREATING</code>. You can check the file-system creation status by calling the <a href=\"https://docs.aws.amazon.com/fsx/latest/APIReference/API_DescribeFileSystems.html\">DescribeFileSystems</a> operation, which returns the file system state along with other information.</p> </note>"}, "CreateFileSystemFromBackup": {"name": "CreateFileSystemFromBackup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateFileSystemFromBackupRequest"}, "output": {"shape": "CreateFileSystemFromBackupResponse"}, "errors": [{"shape": "BadRequest"}, {"shape": "ActiveDirectoryError"}, {"shape": "IncompatibleParameterError"}, {"shape": "InvalidNetworkSettings"}, {"shape": "InvalidPerUnitStorageThroughput"}, {"shape": "ServiceLimitExceeded"}, {"shape": "BackupNotFound"}, {"shape": "InternalServerError"}, {"shape": "MissingFileSystemConfiguration"}], "documentation": "<p>Creates a new Amazon FSx for Lustre, Amazon FSx for Windows File Server, or Amazon FSx for OpenZFS file system from an existing Amazon FSx backup.</p> <p>If a file system with the specified client request token exists and the parameters match, this operation returns the description of the file system. If a file system with the specified client request token exists but the parameters don't match, this call returns <code>IncompatibleParameterError</code>. If a file system with the specified client request token doesn't exist, this operation does the following:</p> <ul> <li> <p>Creates a new Amazon FSx file system from backup with an assigned ID, and an initial lifecycle state of <code>CREATING</code>.</p> </li> <li> <p>Returns the description of the file system.</p> </li> </ul> <p>Parameters like the Active Directory, default share name, automatic backup, and backup settings default to the parameters of the file system that was backed up, unless overridden. You can explicitly supply other settings.</p> <p>By using the idempotent operation, you can retry a <code>CreateFileSystemFromBackup</code> call without the risk of creating an extra file system. This approach can be useful when an initial call fails in a way that makes it unclear whether a file system was created. Examples are if a transport level timeout occurred, or your connection was reset. If you use the same client request token and the initial call created a file system, the client receives a success message as long as the parameters are the same.</p> <note> <p>The <code>CreateFileSystemFromBackup</code> call returns while the file system's lifecycle state is still <code>CREATING</code>. You can check the file-system creation status by calling the <a href=\"https://docs.aws.amazon.com/fsx/latest/APIReference/API_DescribeFileSystems.html\"> DescribeFileSystems</a> operation, which returns the file system state along with other information.</p> </note>"}, "CreateSnapshot": {"name": "CreateSnapshot", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateSnapshotRequest"}, "output": {"shape": "CreateSnapshotResponse"}, "errors": [{"shape": "BadRequest"}, {"shape": "VolumeNotFound"}, {"shape": "ServiceLimitExceeded"}, {"shape": "InternalServerError"}], "documentation": "<p>Creates a snapshot of an existing Amazon FSx for OpenZFS volume. With snapshots, you can easily undo file changes and compare file versions by restoring the volume to a previous version.</p> <p>If a snapshot with the specified client request token exists, and the parameters match, this operation returns the description of the existing snapshot. If a snapshot with the specified client request token exists, and the parameters don't match, this operation returns <code>IncompatibleParameterError</code>. If a snapshot with the specified client request token doesn't exist, <code>CreateSnapshot</code> does the following:</p> <ul> <li> <p>Creates a new OpenZFS snapshot with an assigned ID, and an initial lifecycle state of <code>CREATING</code>.</p> </li> <li> <p>Returns the description of the snapshot.</p> </li> </ul> <p>By using the idempotent operation, you can retry a <code>CreateSnapshot</code> operation without the risk of creating an extra snapshot. This approach can be useful when an initial call fails in a way that makes it unclear whether a snapshot was created. If you use the same client request token and the initial call created a snapshot, the operation returns a successful result because all the parameters are the same.</p> <p>The <code>CreateSnapshot</code> operation returns while the snapshot's lifecycle state is still <code>CREATING</code>. You can check the snapshot creation status by calling the <a href=\"https://docs.aws.amazon.com/fsx/latest/APIReference/API_DescribeSnapshots.html\">DescribeSnapshots</a> operation, which returns the snapshot state along with other information.</p>", "idempotent": true}, "CreateStorageVirtualMachine": {"name": "CreateStorageVirtualMachine", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateStorageVirtualMachineRequest"}, "output": {"shape": "CreateStorageVirtualMachineResponse"}, "errors": [{"shape": "ActiveDirectoryError"}, {"shape": "BadRequest"}, {"shape": "FileSystemNotFound"}, {"shape": "IncompatibleParameterError"}, {"shape": "InternalServerError"}, {"shape": "ServiceLimitExceeded"}, {"shape": "UnsupportedOperation"}], "documentation": "<p>Creates a storage virtual machine (SVM) for an Amazon FSx for ONTAP file system.</p>"}, "CreateVolume": {"name": "CreateVolume", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateVolumeRequest"}, "output": {"shape": "CreateVolumeResponse"}, "errors": [{"shape": "BadRequest"}, {"shape": "FileSystemNotFound"}, {"shape": "IncompatibleParameterError"}, {"shape": "InternalServerError"}, {"shape": "MissingVolumeConfiguration"}, {"shape": "ServiceLimitExceeded"}, {"shape": "StorageVirtualMachineNotFound"}, {"shape": "UnsupportedOperation"}], "documentation": "<p>Creates an FSx for ONTAP or Amazon FSx for OpenZFS storage volume.</p>"}, "CreateVolumeFromBackup": {"name": "CreateVolumeFromBackup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateVolumeFromBackupRequest"}, "output": {"shape": "CreateVolumeFromBackupResponse"}, "errors": [{"shape": "BackupNotFound"}, {"shape": "BadRequest"}, {"shape": "FileSystemNotFound"}, {"shape": "IncompatibleParameterError"}, {"shape": "InternalServerError"}, {"shape": "MissingVolumeConfiguration"}, {"shape": "ServiceLimitExceeded"}, {"shape": "StorageVirtualMachineNotFound"}], "documentation": "<p>Creates a new Amazon FSx for NetApp ONTAP volume from an existing Amazon FSx volume backup.</p>"}, "DeleteBackup": {"name": "DeleteBackup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteBackupRequest"}, "output": {"shape": "DeleteBackupResponse"}, "errors": [{"shape": "BadRequest"}, {"shape": "BackupInProgress"}, {"shape": "BackupNotFound"}, {"shape": "BackupRestoring"}, {"shape": "IncompatibleParameterError"}, {"shape": "InternalServerError"}, {"shape": "BackupBeingCopied"}], "documentation": "<p>Deletes an Amazon FSx backup. After deletion, the backup no longer exists, and its data is gone.</p> <p>The <code>DeleteBackup</code> call returns instantly. The backup won't show up in later <code>DescribeBackups</code> calls.</p> <important> <p>The data in a deleted backup is also deleted and can't be recovered by any means.</p> </important>", "idempotent": true}, "DeleteDataRepositoryAssociation": {"name": "DeleteDataRepositoryAssociation", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteDataRepositoryAssociationRequest"}, "output": {"shape": "DeleteDataRepositoryAssociationResponse"}, "errors": [{"shape": "BadRequest"}, {"shape": "IncompatibleParameterError"}, {"shape": "DataRepositoryAssociationNotFound"}, {"shape": "ServiceLimitExceeded"}, {"shape": "InternalServerError"}], "documentation": "<p>Deletes a data repository association on an Amazon FSx for Lustre file system. Deleting the data repository association unlinks the file system from the Amazon S3 bucket. When deleting a data repository association, you have the option of deleting the data in the file system that corresponds to the data repository association. Data repository associations are supported on all FSx for Lustre 2.12 and 2.15 file systems, excluding <code>scratch_1</code> deployment type.</p>", "idempotent": true}, "DeleteFileCache": {"name": "DeleteFileCache", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteFileCacheRequest"}, "output": {"shape": "DeleteFileCacheResponse"}, "errors": [{"shape": "BadRequest"}, {"shape": "IncompatibleParameterError"}, {"shape": "FileCacheNotFound"}, {"shape": "ServiceLimitExceeded"}, {"shape": "InternalServerError"}], "documentation": "<p>Deletes an Amazon File Cache resource. After deletion, the cache no longer exists, and its data is gone.</p> <p>The <code>DeleteFileCache</code> operation returns while the cache has the <code>DELETING</code> status. You can check the cache deletion status by calling the <a href=\"https://docs.aws.amazon.com/fsx/latest/APIReference/API_DescribeFileCaches.html\">DescribeFileCaches</a> operation, which returns a list of caches in your account. If you pass the cache ID for a deleted cache, the <code>DescribeFileCaches</code> operation returns a <code>FileCacheNotFound</code> error.</p> <important> <p>The data in a deleted cache is also deleted and can't be recovered by any means.</p> </important>", "idempotent": true}, "DeleteFileSystem": {"name": "DeleteFileSystem", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteFileSystemRequest"}, "output": {"shape": "DeleteFileSystemResponse"}, "errors": [{"shape": "BadRequest"}, {"shape": "IncompatibleParameterError"}, {"shape": "FileSystemNotFound"}, {"shape": "ServiceLimitExceeded"}, {"shape": "InternalServerError"}], "documentation": "<p>Deletes a file system. After deletion, the file system no longer exists, and its data is gone. Any existing automatic backups and snapshots are also deleted.</p> <p>To delete an Amazon FSx for NetApp ONTAP file system, first delete all the volumes and storage virtual machines (SVMs) on the file system. Then provide a <code>FileSystemId</code> value to the <code>DeleteFileSystem</code> operation.</p> <p>Before deleting an Amazon FSx for OpenZFS file system, make sure that there aren't any Amazon S3 access points attached to any volume. For more information on how to list S3 access points that are attached to volumes, see <a href=\"https://docs.aws.amazon.com/fsx/latest/OpenZFSGuide/access-points-list\">Listing S3 access point attachments</a>. For more information on how to delete S3 access points, see <a href=\"https://docs.aws.amazon.com/fsx/latest/OpenZFSGuide/delete-points-list\">Deleting an S3 access point attachment</a>.</p> <p>By default, when you delete an Amazon FSx for Windows File Server file system, a final backup is created upon deletion. This final backup isn't subject to the file system's retention policy, and must be manually deleted.</p> <p>To delete an Amazon FSx for Lustre file system, first <a href=\"https://docs.aws.amazon.com/fsx/latest/LustreGuide/unmounting-fs.html\">unmount</a> it from every connected Amazon EC2 instance, then provide a <code>FileSystemId</code> value to the <code>DeleteFileSystem</code> operation. By default, Amazon FSx will not take a final backup when the <code>DeleteFileSystem</code> operation is invoked. On file systems not linked to an Amazon S3 bucket, set <code>SkipFinalBackup</code> to <code>false</code> to take a final backup of the file system you are deleting. Backups cannot be enabled on S3-linked file systems. To ensure all of your data is written back to S3 before deleting your file system, you can either monitor for the <a href=\"https://docs.aws.amazon.com/fsx/latest/LustreGuide/monitoring-cloudwatch.html#auto-import-export-metrics\">AgeOfOldestQueuedMessage</a> metric to be zero (if using automatic export) or you can run an <a href=\"https://docs.aws.amazon.com/fsx/latest/LustreGuide/export-data-repo-task-dra.html\">export data repository task</a>. If you have automatic export enabled and want to use an export data repository task, you have to disable automatic export before executing the export data repository task.</p> <p>The <code>DeleteFileSystem</code> operation returns while the file system has the <code>DELETING</code> status. You can check the file system deletion status by calling the <a href=\"https://docs.aws.amazon.com/fsx/latest/APIReference/API_DescribeFileSystems.html\">DescribeFileSystems</a> operation, which returns a list of file systems in your account. If you pass the file system ID for a deleted file system, the <code>DescribeFileSystems</code> operation returns a <code>FileSystemNotFound</code> error.</p> <note> <p>If a data repository task is in a <code>PENDING</code> or <code>EXECUTING</code> state, deleting an Amazon FSx for Lustre file system will fail with an HTTP status code 400 (Bad Request).</p> </note> <important> <p>The data in a deleted file system is also deleted and can't be recovered by any means.</p> </important>", "idempotent": true}, "DeleteSnapshot": {"name": "DeleteSnapshot", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteSnapshotRequest"}, "output": {"shape": "DeleteSnapshotResponse"}, "errors": [{"shape": "BadRequest"}, {"shape": "InternalServerError"}, {"shape": "SnapshotNotFound"}], "documentation": "<p>Deletes an Amazon FSx for OpenZFS snapshot. After deletion, the snapshot no longer exists, and its data is gone. Deleting a snapshot doesn't affect snapshots stored in a file system backup. </p> <p>The <code>DeleteSnapshot</code> operation returns instantly. The snapshot appears with the lifecycle status of <code>DELETING</code> until the deletion is complete.</p>", "idempotent": true}, "DeleteStorageVirtualMachine": {"name": "DeleteStorageVirtualMachine", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteStorageVirtualMachineRequest"}, "output": {"shape": "DeleteStorageVirtualMachineResponse"}, "errors": [{"shape": "BadRequest"}, {"shape": "IncompatibleParameterError"}, {"shape": "InternalServerError"}, {"shape": "StorageVirtualMachineNotFound"}], "documentation": "<p>Deletes an existing Amazon FSx for ONTAP storage virtual machine (SVM). Prior to deleting an SVM, you must delete all non-root volumes in the SVM, otherwise the operation will fail.</p>"}, "DeleteVolume": {"name": "DeleteVolume", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteVolumeRequest"}, "output": {"shape": "DeleteVolumeResponse"}, "errors": [{"shape": "BadRequest"}, {"shape": "IncompatibleParameterError"}, {"shape": "InternalServerError"}, {"shape": "VolumeNotFound"}, {"shape": "ServiceLimitExceeded"}], "documentation": "<p>Deletes an Amazon FSx for NetApp ONTAP or Amazon FSx for OpenZFS volume.</p>"}, "DescribeBackups": {"name": "DescribeBackups", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeBackupsRequest"}, "output": {"shape": "DescribeBackupsResponse"}, "errors": [{"shape": "BadRequest"}, {"shape": "FileSystemNotFound"}, {"shape": "VolumeNotFound"}, {"shape": "BackupNotFound"}, {"shape": "InternalServerError"}], "documentation": "<p>Returns the description of a specific Amazon FSx backup, if a <code>BackupIds</code> value is provided for that backup. Otherwise, it returns all backups owned by your Amazon Web Services account in the Amazon Web Services Region of the endpoint that you're calling.</p> <p>When retrieving all backups, you can optionally specify the <code>MaxResults</code> parameter to limit the number of backups in a response. If more backups remain, Amazon FSx returns a <code>NextToken</code> value in the response. In this case, send a later request with the <code>NextToken</code> request parameter set to the value of the <code>NextToken</code> value from the last response.</p> <p>This operation is used in an iterative process to retrieve a list of your backups. <code>DescribeBackups</code> is called first without a <code>NextToken</code> value. Then the operation continues to be called with the <code>NextToken</code> parameter set to the value of the last <code>NextToken</code> value until a response has no <code>NextToken</code> value.</p> <p>When using this operation, keep the following in mind:</p> <ul> <li> <p>The operation might return fewer than the <code>MaxResults</code> value of backup descriptions while still including a <code>NextToken</code> value.</p> </li> <li> <p>The order of the backups returned in the response of one <code>DescribeBackups</code> call and the order of the backups returned across the responses of a multi-call iteration is unspecified.</p> </li> </ul>"}, "DescribeDataRepositoryAssociations": {"name": "DescribeDataRepositoryAssociations", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeDataRepositoryAssociationsRequest"}, "output": {"shape": "DescribeDataRepositoryAssociationsResponse"}, "errors": [{"shape": "BadRequest"}, {"shape": "FileSystemNotFound"}, {"shape": "DataRepositoryAssociationNotFound"}, {"shape": "InvalidDataRepositoryType"}, {"shape": "InternalServerError"}], "documentation": "<p>Returns the description of specific Amazon FSx for Lustre or Amazon File Cache data repository associations, if one or more <code>AssociationIds</code> values are provided in the request, or if filters are used in the request. Data repository associations are supported on Amazon File Cache resources and all FSx for Lustre 2.12 and 2,15 file systems, excluding <code>scratch_1</code> deployment type.</p> <p>You can use filters to narrow the response to include just data repository associations for specific file systems (use the <code>file-system-id</code> filter with the ID of the file system) or caches (use the <code>file-cache-id</code> filter with the ID of the cache), or data repository associations for a specific repository type (use the <code>data-repository-type</code> filter with a value of <code>S3</code> or <code>NFS</code>). If you don't use filters, the response returns all data repository associations owned by your Amazon Web Services account in the Amazon Web Services Region of the endpoint that you're calling.</p> <p>When retrieving all data repository associations, you can paginate the response by using the optional <code>MaxResults</code> parameter to limit the number of data repository associations returned in a response. If more data repository associations remain, a <code>NextToken</code> value is returned in the response. In this case, send a later request with the <code>NextToken</code> request parameter set to the value of <code>NextToken</code> from the last response.</p>", "idempotent": true}, "DescribeDataRepositoryTasks": {"name": "DescribeDataRepositoryTasks", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeDataRepositoryTasksRequest"}, "output": {"shape": "DescribeDataRepositoryTasksResponse"}, "errors": [{"shape": "BadRequest"}, {"shape": "FileSystemNotFound"}, {"shape": "DataRepositoryTaskNotFound"}, {"shape": "InternalServerError"}], "documentation": "<p>Returns the description of specific Amazon FSx for Lustre or Amazon File Cache data repository tasks, if one or more <code>TaskIds</code> values are provided in the request, or if filters are used in the request. You can use filters to narrow the response to include just tasks for specific file systems or caches, or tasks in a specific lifecycle state. Otherwise, it returns all data repository tasks owned by your Amazon Web Services account in the Amazon Web Services Region of the endpoint that you're calling.</p> <p>When retrieving all tasks, you can paginate the response by using the optional <code>MaxResults</code> parameter to limit the number of tasks returned in a response. If more tasks remain, a <code>NextToken</code> value is returned in the response. In this case, send a later request with the <code>NextToken</code> request parameter set to the value of <code>NextToken</code> from the last response.</p>"}, "DescribeFileCaches": {"name": "DescribeFileCaches", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeFileCachesRequest"}, "output": {"shape": "DescribeFileCachesResponse"}, "errors": [{"shape": "BadRequest"}, {"shape": "FileCacheNotFound"}, {"shape": "InternalServerError"}], "documentation": "<p>Returns the description of a specific Amazon File Cache resource, if a <code>FileCacheIds</code> value is provided for that cache. Otherwise, it returns descriptions of all caches owned by your Amazon Web Services account in the Amazon Web Services Region of the endpoint that you're calling.</p> <p>When retrieving all cache descriptions, you can optionally specify the <code>MaxResults</code> parameter to limit the number of descriptions in a response. If more cache descriptions remain, the operation returns a <code>NextToken</code> value in the response. In this case, send a later request with the <code>NextToken</code> request parameter set to the value of <code>NextToken</code> from the last response.</p> <p>This operation is used in an iterative process to retrieve a list of your cache descriptions. <code>DescribeFileCaches</code> is called first without a <code>NextToken</code>value. Then the operation continues to be called with the <code>NextToken</code> parameter set to the value of the last <code>NextToken</code> value until a response has no <code>NextToken</code>.</p> <p>When using this operation, keep the following in mind:</p> <ul> <li> <p>The implementation might return fewer than <code>MaxResults</code> cache descriptions while still including a <code>NextToken</code> value.</p> </li> <li> <p>The order of caches returned in the response of one <code>DescribeFileCaches</code> call and the order of caches returned across the responses of a multicall iteration is unspecified.</p> </li> </ul>", "idempotent": true}, "DescribeFileSystemAliases": {"name": "DescribeFileSystemAliases", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeFileSystemAliasesRequest"}, "output": {"shape": "DescribeFileSystemAliasesResponse"}, "errors": [{"shape": "BadRequest"}, {"shape": "FileSystemNotFound"}, {"shape": "InternalServerError"}], "documentation": "<p>Returns the DNS aliases that are associated with the specified Amazon FSx for Windows File Server file system. A history of all DNS aliases that have been associated with and disassociated from the file system is available in the list of <a>AdministrativeAction</a> provided in the <a>DescribeFileSystems</a> operation response.</p>"}, "DescribeFileSystems": {"name": "DescribeFileSystems", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeFileSystemsRequest"}, "output": {"shape": "DescribeFileSystemsResponse"}, "errors": [{"shape": "BadRequest"}, {"shape": "FileSystemNotFound"}, {"shape": "InternalServerError"}], "documentation": "<p>Returns the description of specific Amazon FSx file systems, if a <code>FileSystemIds</code> value is provided for that file system. Otherwise, it returns descriptions of all file systems owned by your Amazon Web Services account in the Amazon Web Services Region of the endpoint that you're calling.</p> <p>When retrieving all file system descriptions, you can optionally specify the <code>MaxResults</code> parameter to limit the number of descriptions in a response. If more file system descriptions remain, Amazon FSx returns a <code>NextToken</code> value in the response. In this case, send a later request with the <code>NextToken</code> request parameter set to the value of <code>NextToken</code> from the last response.</p> <p>This operation is used in an iterative process to retrieve a list of your file system descriptions. <code>DescribeFileSystems</code> is called first without a <code>NextToken</code>value. Then the operation continues to be called with the <code>NextToken</code> parameter set to the value of the last <code>NextToken</code> value until a response has no <code>NextToken</code>.</p> <p>When using this operation, keep the following in mind:</p> <ul> <li> <p>The implementation might return fewer than <code>MaxResults</code> file system descriptions while still including a <code>NextToken</code> value.</p> </li> <li> <p>The order of file systems returned in the response of one <code>DescribeFileSystems</code> call and the order of file systems returned across the responses of a multicall iteration is unspecified.</p> </li> </ul>"}, "DescribeS3AccessPointAttachments": {"name": "DescribeS3AccessPointAttachments", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeS3AccessPointAttachmentsRequest"}, "output": {"shape": "DescribeS3AccessPointAttachmentsResponse"}, "errors": [{"shape": "S3AccessPointAttachmentNotFound"}, {"shape": "BadRequest"}, {"shape": "InternalServerError"}, {"shape": "UnsupportedOperation"}], "documentation": "<p>Describes one or more S3 access points attached to Amazon FSx volumes.</p> <p>The requester requires the following permission to perform this action:</p> <ul> <li> <p> <code>fsx:DescribeS3AccessPointAttachments</code> </p> </li> </ul>"}, "DescribeSharedVpcConfiguration": {"name": "DescribeSharedVpcConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeSharedVpcConfigurationRequest"}, "output": {"shape": "DescribeSharedVpcConfigurationResponse"}, "errors": [{"shape": "BadRequest"}, {"shape": "InternalServerError"}], "documentation": "<p>Indicates whether participant accounts in your organization can create Amazon FSx for NetApp ONTAP Multi-AZ file systems in subnets that are shared by a virtual private cloud (VPC) owner. For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/creating-file-systems.html#fsxn-vpc-shared-subnets\">Creating FSx for ONTAP file systems in shared subnets</a>. </p>"}, "DescribeSnapshots": {"name": "DescribeSnapshots", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeSnapshotsRequest"}, "output": {"shape": "DescribeSnapshotsResponse"}, "errors": [{"shape": "BadRequest"}, {"shape": "InternalServerError"}, {"shape": "SnapshotNotFound"}], "documentation": "<p>Returns the description of specific Amazon FSx for OpenZFS snapshots, if a <code>SnapshotIds</code> value is provided. Otherwise, this operation returns all snapshots owned by your Amazon Web Services account in the Amazon Web Services Region of the endpoint that you're calling.</p> <p>When retrieving all snapshots, you can optionally specify the <code>MaxResults</code> parameter to limit the number of snapshots in a response. If more backups remain, Amazon FSx returns a <code>NextToken</code> value in the response. In this case, send a later request with the <code>NextToken</code> request parameter set to the value of <code>NextToken</code> from the last response. </p> <p>Use this operation in an iterative process to retrieve a list of your snapshots. <code>DescribeSnapshots</code> is called first without a <code>NextToken</code> value. Then the operation continues to be called with the <code>NextToken</code> parameter set to the value of the last <code>NextToken</code> value until a response has no <code>NextToken</code> value.</p> <p>When using this operation, keep the following in mind:</p> <ul> <li> <p>The operation might return fewer than the <code>MaxResults</code> value of snapshot descriptions while still including a <code>NextToken</code> value.</p> </li> <li> <p>The order of snapshots returned in the response of one <code>DescribeSnapshots</code> call and the order of backups returned across the responses of a multi-call iteration is unspecified. </p> </li> </ul>"}, "DescribeStorageVirtualMachines": {"name": "DescribeStorageVirtualMachines", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeStorageVirtualMachinesRequest"}, "output": {"shape": "DescribeStorageVirtualMachinesResponse"}, "errors": [{"shape": "BadRequest"}, {"shape": "InternalServerError"}, {"shape": "StorageVirtualMachineNotFound"}], "documentation": "<p>Describes one or more Amazon FSx for NetApp ONTAP storage virtual machines (SVMs).</p>"}, "DescribeVolumes": {"name": "DescribeVolumes", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeVolumesRequest"}, "output": {"shape": "DescribeVolumesResponse"}, "errors": [{"shape": "BadRequest"}, {"shape": "InternalServerError"}, {"shape": "VolumeNotFound"}], "documentation": "<p>Describes one or more Amazon FSx for NetApp ONTAP or Amazon FSx for OpenZFS volumes.</p>"}, "DetachAndDeleteS3AccessPoint": {"name": "DetachAndDeleteS3AccessPoint", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DetachAndDeleteS3AccessPointRequest"}, "output": {"shape": "DetachAndDeleteS3AccessPointResponse"}, "errors": [{"shape": "BadRequest"}, {"shape": "IncompatibleParameterError"}, {"shape": "InternalServerError"}, {"shape": "UnsupportedOperation"}, {"shape": "S3AccessPointAttachmentNotFound"}], "documentation": "<p>Detaches an S3 access point from an Amazon FSx volume and deletes the S3 access point.</p> <p>The requester requires the following permission to perform this action:</p> <ul> <li> <p> <code>fsx:DetachAndDeleteS3AccessPoint</code> </p> </li> <li> <p> <code>s3:DeleteAccessPoint</code> </p> </li> </ul>"}, "DisassociateFileSystemAliases": {"name": "DisassociateFileSystemAliases", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DisassociateFileSystemAliasesRequest"}, "output": {"shape": "DisassociateFileSystemAliasesResponse"}, "errors": [{"shape": "BadRequest"}, {"shape": "FileSystemNotFound"}, {"shape": "InternalServerError"}], "documentation": "<p>Use this action to disassociate, or remove, one or more Domain Name Service (DNS) aliases from an Amazon FSx for Windows File Server file system. If you attempt to disassociate a DNS alias that is not associated with the file system, Amazon FSx responds with an HTTP status code 400 (Bad Request). For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/WindowsGuide/managing-dns-aliases.html\">Working with DNS Aliases</a>.</p> <p>The system generated response showing the DNS aliases that Amazon FSx is attempting to disassociate from the file system. Use the API operation to monitor the status of the aliases Amazon FSx is disassociating with the file system.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "BadRequest"}, {"shape": "InternalServerError"}, {"shape": "ResourceNotFound"}, {"shape": "NotServiceResourceError"}, {"shape": "ResourceDoesNotSupportTagging"}], "documentation": "<p>Lists tags for Amazon FSx resources.</p> <p>When retrieving all tags, you can optionally specify the <code>MaxResults</code> parameter to limit the number of tags in a response. If more tags remain, Amazon FSx returns a <code>NextToken</code> value in the response. In this case, send a later request with the <code>NextToken</code> request parameter set to the value of <code>NextToken</code> from the last response.</p> <p>This action is used in an iterative process to retrieve a list of your tags. <code>ListTagsForResource</code> is called first without a <code>NextToken</code>value. Then the action continues to be called with the <code>NextToken</code> parameter set to the value of the last <code>NextToken</code> value until a response has no <code>NextToken</code>.</p> <p>When using this action, keep the following in mind:</p> <ul> <li> <p>The implementation might return fewer than <code>MaxResults</code> file system descriptions while still including a <code>NextToken</code> value.</p> </li> <li> <p>The order of tags returned in the response of one <code>ListTagsForResource</code> call and the order of tags returned across the responses of a multi-call iteration is unspecified.</p> </li> </ul>"}, "ReleaseFileSystemNfsV3Locks": {"name": "ReleaseFileSystemNfsV3Locks", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ReleaseFileSystemNfsV3LocksRequest"}, "output": {"shape": "ReleaseFileSystemNfsV3LocksResponse"}, "errors": [{"shape": "BadRequest"}, {"shape": "IncompatibleParameterError"}, {"shape": "FileSystemNotFound"}, {"shape": "ServiceLimitExceeded"}, {"shape": "InternalServerError"}], "documentation": "<p>Releases the file system lock from an Amazon FSx for OpenZFS file system.</p>", "idempotent": true}, "RestoreVolumeFromSnapshot": {"name": "RestoreVolumeFromSnapshot", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "RestoreVolumeFromSnapshotRequest"}, "output": {"shape": "RestoreVolumeFromSnapshotResponse"}, "errors": [{"shape": "BadRequest"}, {"shape": "InternalServerError"}, {"shape": "VolumeNotFound"}], "documentation": "<p>Returns an Amazon FSx for OpenZFS volume to the state saved by the specified snapshot.</p>", "idempotent": true}, "StartMisconfiguredStateRecovery": {"name": "StartMisconfiguredStateRecovery", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StartMisconfiguredStateRecoveryRequest"}, "output": {"shape": "StartMisconfiguredStateRecoveryResponse"}, "errors": [{"shape": "BadRequest"}, {"shape": "FileSystemNotFound"}, {"shape": "InternalServerError"}], "documentation": "<p>After performing steps to repair the Active Directory configuration of an FSx for Windows File Server file system, use this action to initiate the process of Amazon FSx attempting to reconnect to the file system.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "BadRequest"}, {"shape": "InternalServerError"}, {"shape": "ResourceNotFound"}, {"shape": "NotServiceResourceError"}, {"shape": "ResourceDoesNotSupportTagging"}], "documentation": "<p>Tags an Amazon FSx resource.</p>", "idempotent": true}, "UntagResource": {"name": "UntagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "BadRequest"}, {"shape": "InternalServerError"}, {"shape": "ResourceNotFound"}, {"shape": "NotServiceResourceError"}, {"shape": "ResourceDoesNotSupportTagging"}], "documentation": "<p>This action removes a tag from an Amazon FSx resource.</p>", "idempotent": true}, "UpdateDataRepositoryAssociation": {"name": "UpdateDataRepositoryAssociation", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateDataRepositoryAssociationRequest"}, "output": {"shape": "UpdateDataRepositoryAssociationResponse"}, "errors": [{"shape": "BadRequest"}, {"shape": "IncompatibleParameterError"}, {"shape": "DataRepositoryAssociationNotFound"}, {"shape": "ServiceLimitExceeded"}, {"shape": "InternalServerError"}], "documentation": "<p>Updates the configuration of an existing data repository association on an Amazon FSx for Lustre file system. Data repository associations are supported on all FSx for Lustre 2.12 and 2.15 file systems, excluding <code>scratch_1</code> deployment type.</p>", "idempotent": true}, "UpdateFileCache": {"name": "UpdateFileCache", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateFileCacheRequest"}, "output": {"shape": "UpdateFileCacheResponse"}, "errors": [{"shape": "BadRequest"}, {"shape": "UnsupportedOperation"}, {"shape": "IncompatibleParameterError"}, {"shape": "InternalServerError"}, {"shape": "FileCacheNotFound"}, {"shape": "MissingFileCacheConfiguration"}, {"shape": "ServiceLimitExceeded"}], "documentation": "<p>Updates the configuration of an existing Amazon File Cache resource. You can update multiple properties in a single request.</p>", "idempotent": true}, "UpdateFileSystem": {"name": "UpdateFileSystem", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateFileSystemRequest"}, "output": {"shape": "UpdateFileSystemResponse"}, "errors": [{"shape": "BadRequest"}, {"shape": "UnsupportedOperation"}, {"shape": "IncompatibleParameterError"}, {"shape": "InvalidNetworkSettings"}, {"shape": "InternalServerError"}, {"shape": "FileSystemNotFound"}, {"shape": "MissingFileSystemConfiguration"}, {"shape": "ServiceLimitExceeded"}], "documentation": "<p>Use this operation to update the configuration of an existing Amazon FSx file system. You can update multiple properties in a single request.</p> <p>For FSx for Windows File Server file systems, you can update the following properties:</p> <ul> <li> <p> <code>AuditLogConfiguration</code> </p> </li> <li> <p> <code>AutomaticBackupRetentionDays</code> </p> </li> <li> <p> <code>DailyAutomaticBackupStartTime</code> </p> </li> <li> <p> <code>DiskIopsConfiguration</code> </p> </li> <li> <p> <code>SelfManagedActiveDirectoryConfiguration</code> </p> </li> <li> <p> <code>StorageCapacity</code> </p> </li> <li> <p> <code>StorageType</code> </p> </li> <li> <p> <code>ThroughputCapacity</code> </p> </li> <li> <p> <code>WeeklyMaintenanceStartTime</code> </p> </li> </ul> <p>For FSx for Lustre file systems, you can update the following properties:</p> <ul> <li> <p> <code>AutoImportPolicy</code> </p> </li> <li> <p> <code>AutomaticBackupRetentionDays</code> </p> </li> <li> <p> <code>DailyAutomaticBackupStartTime</code> </p> </li> <li> <p> <code>DataCompressionType</code> </p> </li> <li> <p> <code>FileSystemTypeVersion</code> </p> </li> <li> <p> <code>LogConfiguration</code> </p> </li> <li> <p> <code>LustreReadCacheConfiguration</code> </p> </li> <li> <p> <code>LustreRootSquashConfiguration</code> </p> </li> <li> <p> <code>MetadataConfiguration</code> </p> </li> <li> <p> <code>PerUnitStorageThroughput</code> </p> </li> <li> <p> <code>StorageCapacity</code> </p> </li> <li> <p> <code>ThroughputCapacity</code> </p> </li> <li> <p> <code>WeeklyMaintenanceStartTime</code> </p> </li> </ul> <p>For FSx for ONTAP file systems, you can update the following properties:</p> <ul> <li> <p> <code>AddRouteTableIds</code> </p> </li> <li> <p> <code>AutomaticBackupRetentionDays</code> </p> </li> <li> <p> <code>DailyAutomaticBackupStartTime</code> </p> </li> <li> <p> <code>DiskIopsConfiguration</code> </p> </li> <li> <p> <code>FsxAdminPassword</code> </p> </li> <li> <p> <code>HAPairs</code> </p> </li> <li> <p> <code>RemoveRouteTableIds</code> </p> </li> <li> <p> <code>StorageCapacity</code> </p> </li> <li> <p> <code>ThroughputCapacity</code> </p> </li> <li> <p> <code>ThroughputCapacityPerHAPair</code> </p> </li> <li> <p> <code>WeeklyMaintenanceStartTime</code> </p> </li> </ul> <p>For FSx for OpenZFS file systems, you can update the following properties:</p> <ul> <li> <p> <code>AddRouteTableIds</code> </p> </li> <li> <p> <code>AutomaticBackupRetentionDays</code> </p> </li> <li> <p> <code>CopyTagsToBackups</code> </p> </li> <li> <p> <code>CopyTagsToVolumes</code> </p> </li> <li> <p> <code>DailyAutomaticBackupStartTime</code> </p> </li> <li> <p> <code>DiskIopsConfiguration</code> </p> </li> <li> <p> <code>ReadCacheConfiguration</code> </p> </li> <li> <p> <code>RemoveRouteTableIds</code> </p> </li> <li> <p> <code>StorageCapacity</code> </p> </li> <li> <p> <code>ThroughputCapacity</code> </p> </li> <li> <p> <code>WeeklyMaintenanceStartTime</code> </p> </li> </ul>"}, "UpdateSharedVpcConfiguration": {"name": "UpdateSharedVpcConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateSharedVpcConfigurationRequest"}, "output": {"shape": "UpdateSharedVpcConfigurationResponse"}, "errors": [{"shape": "BadRequest"}, {"shape": "IncompatibleParameterError"}, {"shape": "InternalServerError"}], "documentation": "<p>Configures whether participant accounts in your organization can create Amazon FSx for NetApp ONTAP Multi-AZ file systems in subnets that are shared by a virtual private cloud (VPC) owner. For more information, see the <a href=\"https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/maz-shared-vpc.html\">Amazon FSx for NetApp ONTAP User Guide</a>.</p> <note> <p>We strongly recommend that participant-created Multi-AZ file systems in the shared VPC are deleted before you disable this feature. Once the feature is disabled, these file systems will enter a <code>MISCONFIGURED</code> state and behave like Single-AZ file systems. For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/maz-shared-vpc.html#disabling-maz-vpc-sharing\">Important considerations before disabling shared VPC support for Multi-AZ file systems</a>.</p> </note>"}, "UpdateSnapshot": {"name": "UpdateSnapshot", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateSnapshotRequest"}, "output": {"shape": "UpdateSnapshotResponse"}, "errors": [{"shape": "BadRequest"}, {"shape": "SnapshotNotFound"}, {"shape": "InternalServerError"}], "documentation": "<p>Updates the name of an Amazon FSx for OpenZFS snapshot.</p>", "idempotent": true}, "UpdateStorageVirtualMachine": {"name": "UpdateStorageVirtualMachine", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateStorageVirtualMachineRequest"}, "output": {"shape": "UpdateStorageVirtualMachineResponse"}, "errors": [{"shape": "BadRequest"}, {"shape": "IncompatibleParameterError"}, {"shape": "InternalServerError"}, {"shape": "StorageVirtualMachineNotFound"}, {"shape": "UnsupportedOperation"}], "documentation": "<p>Updates an FSx for ONTAP storage virtual machine (SVM).</p>"}, "UpdateVolume": {"name": "UpdateVolume", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateVolumeRequest"}, "output": {"shape": "UpdateVolumeResponse"}, "errors": [{"shape": "BadRequest"}, {"shape": "IncompatibleParameterError"}, {"shape": "InternalServerError"}, {"shape": "MissingVolumeConfiguration"}, {"shape": "VolumeNotFound"}], "documentation": "<p>Updates the configuration of an Amazon FSx for NetApp ONTAP or Amazon FSx for OpenZFS volume.</p>"}}, "shapes": {"AWSAccountId": {"type": "string", "documentation": "<p>An Amazon Web Services account ID. This ID is a 12-digit number that you use to construct Amazon Resource Names (ARNs) for resources.</p>", "max": 12, "min": 12, "pattern": "^\\d{12}$"}, "AccessPointAlreadyOwnedByYou": {"type": "structure", "members": {"ErrorCode": {"shape": "ErrorCode", "documentation": "<p>An error code indicating that an access point with that name already exists in the Amazon Web Services Region in your Amazon Web Services account.</p>"}, "Message": {"shape": "ErrorMessage"}}, "documentation": "<p>An access point with that name already exists in the Amazon Web Services Region in your Amazon Web Services account.</p>", "exception": true}, "AccessPointPolicy": {"type": "string", "max": 200000, "min": 1}, "ActiveDirectoryBackupAttributes": {"type": "structure", "members": {"DomainName": {"shape": "ActiveDirectoryFullyQualifiedName", "documentation": "<p>The fully qualified domain name of the self-managed Active Directory directory.</p>"}, "ActiveDirectoryId": {"shape": "DirectoryId", "documentation": "<p>The ID of the Amazon Web Services Managed Microsoft Active Directory instance to which the file system is joined.</p>"}, "ResourceARN": {"shape": "ResourceARN"}}, "documentation": "<p>The Microsoft Active Directory attributes of the Amazon FSx for Windows File Server file system.</p>"}, "ActiveDirectoryError": {"type": "structure", "required": ["ActiveDirectoryId"], "members": {"ActiveDirectoryId": {"shape": "DirectoryId", "documentation": "<p>The directory ID of the directory that an error pertains to.</p>"}, "Type": {"shape": "ActiveDirectoryErrorType", "documentation": "<p>The type of Active Directory error.</p>"}, "Message": {"shape": "ErrorMessage"}}, "documentation": "<p>An Active Directory error.</p>", "exception": true}, "ActiveDirectoryErrorType": {"type": "string", "documentation": "<p>The type of error relating to Microsoft Active Directory. NOT_FOUND means that no directory was found by specifying the given directory. INCOMPATIBLE_MODE means that the directory specified is not a Microsoft AD directory. WRONG_VPC means that the specified directory isn't accessible from the specified VPC. WRONG_STAGE means that the specified directory isn't currently in the ACTIVE state.</p>", "enum": ["DOMAIN_NOT_FOUND", "INCOMPATIBLE_DOMAIN_MODE", "WRONG_VPC", "INVALID_DOMAIN_STAGE"]}, "ActiveDirectoryFullyQualifiedName": {"type": "string", "max": 255, "min": 1, "pattern": "^[^\\u0000\\u0085\\u2028\\u2029\\r\\n]{1,255}$"}, "AdminPassword": {"type": "string", "max": 50, "min": 8, "pattern": "^[^\\u0000\\u0085\\u2028\\u2029\\r\\n]{8,50}$", "sensitive": true}, "AdministrativeAction": {"type": "structure", "members": {"AdministrativeActionType": {"shape": "AdministrativeActionType"}, "ProgressPercent": {"shape": "ProgressPercent", "documentation": "<p>The percentage-complete status of a <code>STORAGE_OPTIMIZATION</code> or <code>DOWNLOAD_DATA_FROM_BACKUP</code> administrative action. Does not apply to any other administrative action type.</p>"}, "RequestTime": {"shape": "RequestTime", "documentation": "<p>The time that the administrative action request was received.</p>"}, "Status": {"shape": "Status", "documentation": "<p>The status of the administrative action, as follows:</p> <ul> <li> <p> <code>FAILED</code> - Amazon FSx failed to process the administrative action successfully.</p> </li> <li> <p> <code>IN_PROGRESS</code> - Amazon FSx is processing the administrative action.</p> </li> <li> <p> <code>PENDING</code> - Amazon FSx is waiting to process the administrative action.</p> </li> <li> <p> <code>COMPLETED</code> - Amazon FSx has finished processing the administrative task.</p> <p>For a backup restore to a second-generation FSx for ONTAP file system, indicates that all data has been downloaded to the volume, and clients now have read-write access to volume.</p> </li> <li> <p> <code>UPDATED_OPTIMIZING</code> - For a storage-capacity increase update, Amazon FSx has updated the file system with the new storage capacity, and is now performing the storage-optimization process.</p> </li> <li> <p> <code>PENDING</code> - For a backup restore to a second-generation FSx for ONTAP file system, indicates that the file metadata is being downloaded onto the volume. The volume's Lifecycle state is CREATING.</p> </li> <li> <p> <code>IN_PROGRESS</code> - For a backup restore to a second-generation FSx for ONTAP file system, indicates that all metadata has been downloaded to the new volume and client can access data with read-only access while Amazon FSx downloads the file data to the volume. Track the progress of this process with the <code>ProgressPercent</code> element.</p> </li> </ul>"}, "TargetFileSystemValues": {"shape": "FileSystem", "documentation": "<p>The target value for the administration action, provided in the <code>UpdateFileSystem</code> operation. Returned for <code>FILE_SYSTEM_UPDATE</code> administrative actions. </p>"}, "FailureDetails": {"shape": "AdministrativeActionFailureDetails"}, "TargetVolumeValues": {"shape": "Volume"}, "TargetSnapshotValues": {"shape": "Snapshot"}, "TotalTransferBytes": {"shape": "TotalTransferBytes", "documentation": "<p>The number of bytes that have transferred for the FSx for OpenZFS snapshot that you're copying.</p>"}, "RemainingTransferBytes": {"shape": "RemainingTransferBytes", "documentation": "<p>The remaining bytes to transfer for the FSx for OpenZFS snapshot that you're copying.</p>"}}, "documentation": "<p>Describes a specific Amazon FSx administrative action for the current Windows, Lustre, OpenZFS, or ONTAP file system or volume.</p>"}, "AdministrativeActionFailureDetails": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage", "documentation": "<p>Error message providing details about the failed administrative action.</p>"}}, "documentation": "<p>Provides information about a failed administrative action.</p>"}, "AdministrativeActionType": {"type": "string", "documentation": "<p>Describes the type of administrative action, as follows:</p> <ul> <li> <p> <code>FILE_SYSTEM_UPDATE</code> - A file system update administrative action initiated from the Amazon FSx console, API (<code>UpdateFileSystem</code>), or CLI (<code>update-file-system</code>).</p> </li> <li> <p> <code>THROUGHPUT_OPTIMIZATION</code> - After the <code>FILE_SYSTEM_UPDATE</code> task to increase a file system's throughput capacity has been completed successfully, a <code>THROUGHPUT_OPTIMIZATION</code> task starts.</p> <p>You can track the storage-optimization progress using the <code>ProgressPercent</code> property. When <code>THROUGHPUT_OPTIMIZATION</code> has been completed successfully, the parent <code>FILE_SYSTEM_UPDATE</code> action status changes to <code>COMPLETED</code>. For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/WindowsGuide/managing-throughput-capacity.html\">Managing throughput capacity</a> in the <i>Amazon FSx for Windows File Server User Guide</i>.</p> </li> <li> <p> <code>STORAGE_OPTIMIZATION</code> - After the <code>FILE_SYSTEM_UPDATE</code> task to increase a file system's storage capacity has completed successfully, a <code>STORAGE_OPTIMIZATION</code> task starts. </p> <ul> <li> <p>For Windows and ONTAP, storage optimization is the process of migrating the file system data to newer larger disks.</p> </li> <li> <p>For Lustre, storage optimization consists of rebalancing the data across the existing and newly added file servers.</p> </li> </ul> <p>You can track the storage-optimization progress using the <code>ProgressPercent</code> property. When <code>STORAGE_OPTIMIZATION</code> has been completed successfully, the parent <code>FILE_SYSTEM_UPDATE</code> action status changes to <code>COMPLETED</code>. For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/WindowsGuide/managing-storage-capacity.html\">Managing storage capacity</a> in the <i>Amazon FSx for Windows File Server User Guide</i>, <a href=\"https://docs.aws.amazon.com/fsx/latest/LustreGuide/managing-storage-capacity.html\">Managing storage capacity</a> in the <i>Amazon FSx for Lustre User Guide</i>, and <a href=\"https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/managing-storage-capacity.html\">Managing storage capacity and provisioned IOPS</a> in the <i>Amazon FSx for NetApp ONTAP User Guide</i>.</p> </li> <li> <p> <code>FILE_SYSTEM_ALIAS_ASSOCIATION</code> - A file system update to associate a new Domain Name System (DNS) alias with the file system. For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/APIReference/API_AssociateFileSystemAliases.html\"> AssociateFileSystemAliases</a>.</p> </li> <li> <p> <code>FILE_SYSTEM_ALIAS_DISASSOCIATION</code> - A file system update to disassociate a DNS alias from the file system. For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/APIReference/API_DisassociateFileSystemAliases.html\">DisassociateFileSystemAliases</a>.</p> </li> <li> <p> <code>IOPS_OPTIMIZATION</code> - After the <code>FILE_SYSTEM_UPDATE</code> task to increase a file system's throughput capacity has been completed successfully, a <code>IOPS_OPTIMIZATION</code> task starts.</p> <p>You can track the storage-optimization progress using the <code>ProgressPercent</code> property. When <code>IOPS_OPTIMIZATION</code> has been completed successfully, the parent <code>FILE_SYSTEM_UPDATE</code> action status changes to <code>COMPLETED</code>. For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/WindowsGuide/managing-provisioned-ssd-iops.html\">Managing provisioned SSD IOPS</a> in the Amazon FSx for Windows File Server User Guide.</p> </li> <li> <p> <code>STORAGE_TYPE_OPTIMIZATION</code> - After the <code>FILE_SYSTEM_UPDATE</code> task to increase a file system's throughput capacity has been completed successfully, a <code>STORAGE_TYPE_OPTIMIZATION</code> task starts.</p> <p>You can track the storage-optimization progress using the <code>ProgressPercent</code> property. When <code>STORAGE_TYPE_OPTIMIZATION</code> has been completed successfully, the parent <code>FILE_SYSTEM_UPDATE</code> action status changes to <code>COMPLETED</code>.</p> </li> <li> <p> <code>VOLUME_UPDATE</code> - A volume update to an Amazon FSx for OpenZFS volume initiated from the Amazon FSx console, API (<code>UpdateVolume</code>), or CLI (<code>update-volume</code>).</p> </li> <li> <p> <code>VOLUME_RESTORE</code> - An Amazon FSx for OpenZFS volume is returned to the state saved by the specified snapshot, initiated from an API (<code>RestoreVolumeFromSnapshot</code>) or CLI (<code>restore-volume-from-snapshot</code>).</p> </li> <li> <p> <code>SNAPSHOT_UPDATE</code> - A snapshot update to an Amazon FSx for OpenZFS volume initiated from the Amazon FSx console, API (<code>UpdateSnapshot</code>), or CLI (<code>update-snapshot</code>).</p> </li> <li> <p> <code>RELEASE_NFS_V3_LOCKS</code> - Tracks the release of Network File System (NFS) V3 locks on an Amazon FSx for OpenZFS file system.</p> </li> <li> <p> <code>DOWNLOAD_DATA_FROM_BACKUP</code> - An FSx for ONTAP backup is being restored to a new volume on a second-generation file system. Once the all the file metadata is loaded onto the volume, you can mount the volume with read-only access. during this process.</p> </li> <li> <p> <code>VOLUME_INITIALIZE_WITH_SNAPSHOT</code> - A volume is being created from a snapshot on a different FSx for OpenZFS file system. You can initiate this from the Amazon FSx console, API (<code>CreateVolume</code>), or CLI (<code>create-volume</code>) when using the using the <code>FULL_COPY</code> strategy.</p> </li> <li> <p> <code>VOLUME_UPDATE_WITH_SNAPSHOT</code> - A volume is being updated from a snapshot on a different FSx for OpenZFS file system. You can initiate this from the Amazon FSx console, API (<code>CopySnapshotAndUpdateVolume</code>), or CLI (<code>copy-snapshot-and-update-volume</code>).</p> </li> </ul>", "enum": ["FILE_SYSTEM_UPDATE", "STORAGE_OPTIMIZATION", "FILE_SYSTEM_ALIAS_ASSOCIATION", "FILE_SYSTEM_ALIAS_DISASSOCIATION", "VOLUME_UPDATE", "SNAPSHOT_UPDATE", "RELEASE_NFS_V3_LOCKS", "VOLUME_RESTORE", "THROUGHPUT_OPTIMIZATION", "IOPS_OPTIMIZATION", "STORAGE_TYPE_OPTIMIZATION", "MISCONFIGURED_STATE_RECOVERY", "VOLUME_UPDATE_WITH_SNAPSHOT", "VOLUME_INITIALIZE_WITH_SNAPSHOT", "DOWNLOAD_DATA_FROM_BACKUP"]}, "AdministrativeActions": {"type": "list", "member": {"shape": "AdministrativeAction"}, "max": 50}, "Aggregate": {"type": "string", "max": 6, "min": 5, "pattern": "^(aggr[0-9]{1,2})$"}, "AggregateConfiguration": {"type": "structure", "members": {"Aggregates": {"shape": "Aggregates", "documentation": "<p>The list of aggregates that this volume resides on. Aggregates are storage pools which make up your primary storage tier. Each high-availability (HA) pair has one aggregate. The names of the aggregates map to the names of the aggregates in the ONTAP CLI and REST API. For FlexVols, there will always be a single entry.</p> <p>Amazon FSx responds with an HTTP status code 400 (Bad Request) for the following conditions:</p> <ul> <li> <p>The strings in the value of <code>Aggregates</code> are not are not formatted as <code>aggrX</code>, where X is a number between 1 and 12.</p> </li> <li> <p>The value of <code>Aggregates</code> contains aggregates that are not present.</p> </li> <li> <p>One or more of the aggregates supplied are too close to the volume limit to support adding more volumes.</p> </li> </ul>"}, "TotalConstituents": {"shape": "TotalConstituents", "documentation": "<p>The total number of constituents this FlexGroup volume has. Not applicable for FlexVols.</p>"}}, "documentation": "<p>Used to specify configuration options for a volume’s storage aggregate or aggregates.</p>"}, "AggregateListMultiplier": {"type": "integer", "max": 200, "min": 1}, "Aggregates": {"type": "list", "member": {"shape": "Aggregate"}, "max": 6}, "Alias": {"type": "structure", "members": {"Name": {"shape": "AlternateDNSName", "documentation": "<p>The name of the DNS alias. The alias name has to meet the following requirements:</p> <ul> <li> <p>Formatted as a fully-qualified domain name (FQDN), <code>hostname.domain</code>, for example, <code>accounting.example.com</code>.</p> </li> <li> <p>Can contain alphanumeric characters, the underscore (_), and the hyphen (-).</p> </li> <li> <p>Cannot start or end with a hyphen.</p> </li> <li> <p>Can start with a numeric.</p> </li> </ul> <p>For DNS names, Amazon FSx stores alphabetic characters as lowercase letters (a-z), regardless of how you specify them: as uppercase letters, lowercase letters, or the corresponding letters in escape codes.</p>"}, "Lifecycle": {"shape": "AliasLifecycle", "documentation": "<p>Describes the state of the DNS alias.</p> <ul> <li> <p>AVAILABLE - The DNS alias is associated with an Amazon FSx file system.</p> </li> <li> <p>CREATING - Amazon FSx is creating the DNS alias and associating it with the file system.</p> </li> <li> <p>CREATE_FAILED - Amazon FSx was unable to associate the DNS alias with the file system.</p> </li> <li> <p>DELETING - Amazon FSx is disassociating the DNS alias from the file system and deleting it.</p> </li> <li> <p>DELETE_FAILED - Amazon FSx was unable to disassociate the DNS alias from the file system.</p> </li> </ul>"}}, "documentation": "<p>A DNS alias that is associated with the file system. You can use a DNS alias to access a file system using user-defined DNS names, in addition to the default DNS name that Amazon FSx assigns to the file system. For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/WindowsGuide/managing-dns-aliases.html\">DNS aliases</a> in the <i>FSx for Windows File Server User Guide</i>.</p>"}, "AliasLifecycle": {"type": "string", "enum": ["AVAILABLE", "CREATING", "DELETING", "CREATE_FAILED", "DELETE_FAILED"]}, "Aliases": {"type": "list", "member": {"shape": "<PERSON><PERSON>"}, "documentation": "<p>An array of one or more DNS aliases that are currently associated with the Amazon FSx file system. Aliases allow you to use existing DNS names to access the data in your Amazon FSx file system. You can associate up to 50 aliases with a file system at any time. You can associate additional DNS aliases after you create the file system using the AssociateFileSystemAliases operation. You can remove DNS aliases from the file system after it is created using the DisassociateFileSystemAliases operation. You only need to specify the alias name in the request payload. For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/WindowsGuide/managing-dns-aliases.html\">DNS aliases</a>.</p>", "max": 50}, "AlternateDNSName": {"type": "string", "max": 253, "min": 4, "pattern": "^[^\\u0000\\u0085\\u2028\\u2029\\r\\n]{4,253}$"}, "AlternateDNSNames": {"type": "list", "member": {"shape": "AlternateDNSName"}, "max": 50}, "ArchivePath": {"type": "string", "max": 4357, "min": 3, "pattern": "^[^\\u0000\\u0085\\u2028\\u2029\\r\\n]{3,4357}$"}, "AssociateFileSystemAliasesRequest": {"type": "structure", "required": ["FileSystemId", "Aliases"], "members": {"ClientRequestToken": {"shape": "ClientRequestToken", "idempotencyToken": true}, "FileSystemId": {"shape": "FileSystemId", "documentation": "<p>Specifies the file system with which you want to associate one or more DNS aliases.</p>"}, "Aliases": {"shape": "AlternateDNSNames", "documentation": "<p>An array of one or more DNS alias names to associate with the file system. The alias name has to comply with the following formatting requirements:</p> <ul> <li> <p>Formatted as a fully-qualified domain name (FQDN), <i> <code>hostname.domain</code> </i>, for example, <code>accounting.corp.example.com</code>.</p> </li> <li> <p>Can contain alphanumeric characters and the hyphen (-).</p> </li> <li> <p>Cannot start or end with a hyphen.</p> </li> <li> <p>Can start with a numeric.</p> </li> </ul> <p>For DNS alias names, Amazon FSx stores alphabetic characters as lowercase letters (a-z), regardless of how you specify them: as uppercase letters, lowercase letters, or the corresponding letters in escape codes.</p>"}}, "documentation": "<p>The request object specifying one or more DNS alias names to associate with an Amazon FSx for Windows File Server file system.</p>"}, "AssociateFileSystemAliasesResponse": {"type": "structure", "members": {"Aliases": {"shape": "Aliases", "documentation": "<p>An array of the DNS aliases that Amazon FSx is associating with the file system.</p>"}}, "documentation": "<p>The system generated response showing the DNS aliases that Amazon FSx is attempting to associate with the file system. Use the API operation to monitor the status of the aliases Amazon FSx is associating with the file system. It can take up to 2.5 minutes for the alias status to change from <code>CREATING</code> to <code>AVAILABLE</code>. </p>"}, "AutoExportPolicy": {"type": "structure", "members": {"Events": {"shape": "EventTypes", "documentation": "<p>The <code>AutoExportPolicy</code> can have the following event values:</p> <ul> <li> <p> <code>NEW</code> - New files and directories are automatically exported to the data repository as they are added to the file system.</p> </li> <li> <p> <code>CHANGED</code> - Changes to files and directories on the file system are automatically exported to the data repository.</p> </li> <li> <p> <code>DELETED</code> - Files and directories are automatically deleted on the data repository when they are deleted on the file system.</p> </li> </ul> <p>You can define any combination of event types for your <code>AutoExportPolicy</code>.</p>"}}, "documentation": "<p>Describes a data repository association's automatic export policy. The <code>AutoExportPolicy</code> defines the types of updated objects on the file system that will be automatically exported to the data repository. As you create, modify, or delete files, Amazon FSx for Lustre automatically exports the defined changes asynchronously once your application finishes modifying the file.</p> <p>The <code>AutoExportPolicy</code> is only supported on Amazon FSx for Lustre file systems with a data repository association.</p>"}, "AutoImportPolicy": {"type": "structure", "members": {"Events": {"shape": "EventTypes", "documentation": "<p>The <code>AutoImportPolicy</code> can have the following event values:</p> <ul> <li> <p> <code>NEW</code> - Amazon FSx automatically imports metadata of files added to the linked S3 bucket that do not currently exist in the FSx file system.</p> </li> <li> <p> <code>CHANGED</code> - Amazon FSx automatically updates file metadata and invalidates existing file content on the file system as files change in the data repository.</p> </li> <li> <p> <code>DELETED</code> - Amazon FSx automatically deletes files on the file system as corresponding files are deleted in the data repository.</p> </li> </ul> <p>You can define any combination of event types for your <code>AutoImportPolicy</code>.</p>"}}, "documentation": "<p>Describes the data repository association's automatic import policy. The AutoImportPolicy defines how Amazon FSx keeps your file metadata and directory listings up to date by importing changes to your Amazon FSx for Lustre file system as you modify objects in a linked S3 bucket.</p> <p>The <code>AutoImportPolicy</code> is only supported on Amazon FSx for Lustre file systems with a data repository association.</p>"}, "AutoImportPolicyType": {"type": "string", "enum": ["NONE", "NEW", "NEW_CHANGED", "NEW_CHANGED_DELETED"]}, "AutocommitPeriod": {"type": "structure", "required": ["Type"], "members": {"Type": {"shape": "AutocommitPeriodType", "documentation": "<p>Defines the type of time for the autocommit period of a file in an FSx for ONTAP SnapLock volume. Setting this value to <code>NONE</code> disables autocommit. The default value is <code>NONE</code>. </p>"}, "Value": {"shape": "AutocommitPeriodValue", "documentation": "<p>Defines the amount of time for the autocommit period of a file in an FSx for ONTAP SnapLock volume. The following ranges are valid: </p> <ul> <li> <p> <code>Minutes</code>: 5 - 65,535</p> </li> <li> <p> <code>Hours</code>: 1 - 65,535</p> </li> <li> <p> <code>Days</code>: 1 - 3,650</p> </li> <li> <p> <code>Months</code>: 1 - 120</p> </li> <li> <p> <code>Years</code>: 1 - 10</p> </li> </ul>"}}, "documentation": "<p>Sets the autocommit period of files in an FSx for ONTAP SnapLock volume, which determines how long the files must remain unmodified before they're automatically transitioned to the write once, read many (WORM) state. </p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/worm-state.html#worm-state-autocommit\">Autocommit</a>. </p>"}, "AutocommitPeriodType": {"type": "string", "enum": ["MINUTES", "HOURS", "DAYS", "MONTHS", "YEARS", "NONE"]}, "AutocommitPeriodValue": {"type": "integer", "max": 65535, "min": 1}, "AutomaticBackupRetentionDays": {"type": "integer", "documentation": "<p>The number of days to retain automatic backups. Setting this property to <code>0</code> disables automatic backups. You can retain automatic backups for a maximum of 90 days. The default is <code>30</code>.</p>", "max": 90, "min": 0}, "Backup": {"type": "structure", "required": ["BackupId", "Lifecycle", "Type", "CreationTime", "FileSystem"], "members": {"BackupId": {"shape": "BackupId", "documentation": "<p>The ID of the backup.</p>"}, "Lifecycle": {"shape": "BackupLifecycle", "documentation": "<p>The lifecycle status of the backup.</p> <ul> <li> <p> <code>AVAILABLE</code> - The backup is fully available.</p> </li> <li> <p> <code>PENDING</code> - For user-initiated backups on Lustre file systems only; Amazon FSx hasn't started creating the backup.</p> </li> <li> <p> <code>CREATING</code> - Amazon FSx is creating the backup.</p> </li> <li> <p> <code>TRANSFERRING</code> - For user-initiated backups on Lustre file systems only; Amazon FSx is transferring the backup to Amazon S3.</p> </li> <li> <p> <code>COPYING</code> - Amazon FSx is copying the backup.</p> </li> <li> <p> <code>DELETED</code> - Amazon FSx deleted the backup and it's no longer available.</p> </li> <li> <p> <code>FAILED</code> - Amazon FSx couldn't finish the backup.</p> </li> </ul>"}, "FailureDetails": {"shape": "BackupFailureDetails", "documentation": "<p>Details explaining any failures that occurred when creating a backup.</p>"}, "Type": {"shape": "BackupType", "documentation": "<p>The type of the file-system backup.</p>"}, "ProgressPercent": {"shape": "ProgressPercent"}, "CreationTime": {"shape": "CreationTime", "documentation": "<p>The time when a particular backup was created.</p>"}, "KmsKeyId": {"shape": "KmsKeyId", "documentation": "<p>The ID of the Key Management Service (KMS) key used to encrypt the backup of the Amazon FSx file system's data at rest. </p>"}, "ResourceARN": {"shape": "ResourceARN", "documentation": "<p>The Amazon Resource Name (ARN) for the backup resource.</p>"}, "Tags": {"shape": "Tags", "documentation": "<p>The tags associated with a particular file system.</p>"}, "FileSystem": {"shape": "FileSystem", "documentation": "<p>The metadata of the file system associated with the backup. This metadata is persisted even if the file system is deleted.</p>"}, "DirectoryInformation": {"shape": "ActiveDirectoryBackupAttributes", "documentation": "<p>The configuration of the self-managed Microsoft Active Directory directory to which the Windows File Server instance is joined.</p>"}, "OwnerId": {"shape": "AWSAccountId"}, "SourceBackupId": {"shape": "BackupId"}, "SourceBackupRegion": {"shape": "Region", "documentation": "<p>The source Region of the backup. Specifies the Region from where this backup is copied.</p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>Specifies the resource type that's backed up.</p>"}, "Volume": {"shape": "Volume"}, "SizeInBytes": {"shape": "SizeInBytes", "documentation": "<p> The size of the backup in bytes. This represents the amount of data that the file system would contain if you restore this backup. </p>"}}, "documentation": "<p>A backup of an Amazon FSx for Windows File Server, Amazon FSx for Lustre file system, Amazon FSx for NetApp ONTAP volume, or Amazon FSx for OpenZFS file system.</p>"}, "BackupBeingCopied": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}, "BackupId": {"shape": "BackupId"}}, "documentation": "<p>You can't delete a backup while it's being copied.</p>", "exception": true}, "BackupFailureDetails": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage", "documentation": "<p>A message describing the backup-creation failure.</p>"}}, "documentation": "<p>If backup creation fails, this structure contains the details of that failure.</p>"}, "BackupId": {"type": "string", "documentation": "<p>The ID of the source backup. Specifies the backup that you are copying.</p>", "max": 128, "min": 12, "pattern": "^(backup-[0-9a-f]{8,})$"}, "BackupIds": {"type": "list", "member": {"shape": "BackupId"}, "documentation": "<p>A list of backup IDs.</p>", "max": 50}, "BackupInProgress": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>Another backup is already under way. Wait for completion before initiating additional backups of this file system.</p>", "exception": true}, "BackupLifecycle": {"type": "string", "documentation": "<p>The lifecycle status of the backup.</p> <ul> <li> <p> <code>AVAILABLE</code> - The backup is fully available.</p> </li> <li> <p> <code>PENDING</code> - For user-initiated backups on Lustre file systems only; Amazon FSx hasn't started creating the backup.</p> </li> <li> <p> <code>CREATING</code> - Amazon FSx is creating the new user-initiated backup.</p> </li> <li> <p> <code>TRANSFERRING</code> - For user-initiated backups on Lustre file systems only; Amazon FSx is backing up the file system.</p> </li> <li> <p> <code>COPYING</code> - Amazon FSx is copying the backup.</p> </li> <li> <p> <code>DELETED</code> - Amazon FSx deleted the backup and it's no longer available.</p> </li> <li> <p> <code>FAILED</code> - Amazon FSx couldn't finish the backup.</p> </li> </ul>", "enum": ["AVAILABLE", "CREATING", "TRANSFERRING", "DELETED", "FAILED", "PENDING", "COPYING"]}, "BackupNotFound": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>No Amazon FSx backups were found based upon the supplied parameters.</p>", "exception": true}, "BackupRestoring": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}, "FileSystemId": {"shape": "FileSystemId", "documentation": "<p>The ID of a file system being restored from the backup.</p>"}}, "documentation": "<p>You can't delete a backup while it's being used to restore a file system.</p>", "exception": true}, "BackupType": {"type": "string", "documentation": "<p>The type of the backup.</p>", "enum": ["AUTOMATIC", "USER_INITIATED", "AWS_BACKUP"]}, "Backups": {"type": "list", "member": {"shape": "Backup"}, "documentation": "<p>A list of file system backups.</p>", "max": 50}, "BadRequest": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>A generic error indicating a failure with a client request.</p>", "exception": true}, "BatchImportMetaDataOnCreate": {"type": "boolean"}, "CancelDataRepositoryTaskRequest": {"type": "structure", "required": ["TaskId"], "members": {"TaskId": {"shape": "TaskId", "documentation": "<p>Specifies the data repository task to cancel.</p>"}}, "documentation": "<p>Cancels a data repository task.</p>"}, "CancelDataRepositoryTaskResponse": {"type": "structure", "members": {"Lifecycle": {"shape": "DataRepositoryTaskLifecycle", "documentation": "<p>The lifecycle status of the data repository task, as follows:</p> <ul> <li> <p> <code>PENDING</code> - Amazon FSx has not started the task.</p> </li> <li> <p> <code>EXECUTING</code> - Amazon FSx is processing the task.</p> </li> <li> <p> <code>FAILED</code> - Amazon FSx was not able to complete the task. For example, there may be files the task failed to process. The <a>DataRepositoryTaskFailureDetails</a> property provides more information about task failures.</p> </li> <li> <p> <code>SUCCEEDED</code> - FSx completed the task successfully.</p> </li> <li> <p> <code>CANCELED</code> - Amazon FSx canceled the task and it did not complete.</p> </li> <li> <p> <code>CANCELING</code> - FSx is in process of canceling the task.</p> </li> </ul>"}, "TaskId": {"shape": "TaskId", "documentation": "<p>The ID of the task being canceled.</p>"}}}, "CapacityToRelease": {"type": "long", "max": 2147483647, "min": 1}, "ClientRequestToken": {"type": "string", "documentation": "<p>(Optional) An idempotency token for resource creation, in a string of up to 63 ASCII characters. This token is automatically filled on your behalf when you use the Command Line Interface (CLI) or an Amazon Web Services SDK.</p>", "max": 63, "min": 1, "pattern": "[A-za-z0-9_.-]{0,63}$"}, "CompletionReport": {"type": "structure", "required": ["Enabled"], "members": {"Enabled": {"shape": "Flag", "documentation": "<p>Set <code>Enabled</code> to <code>True</code> to generate a <code>CompletionReport</code> when the task completes. If set to <code>true</code>, then you need to provide a report <code>Scope</code>, <code>Path</code>, and <code>Format</code>. Set <code>Enabled</code> to <code>False</code> if you do not want a <code>CompletionReport</code> generated when the task completes.</p>"}, "Path": {"shape": "ArchivePath", "documentation": "<p>Required if <code>Enabled</code> is set to <code>true</code>. Specifies the location of the report on the file system's linked S3 data repository. An absolute path that defines where the completion report will be stored in the destination location. The <code>Path</code> you provide must be located within the file system’s ExportPath. An example <code>Path</code> value is \"s3://amzn-s3-demo-bucket/myExportPath/optionalPrefix\". The report provides the following information for each file in the report: FilePath, FileStatus, and ErrorCode.</p>"}, "Format": {"shape": "ReportFormat", "documentation": "<p>Required if <code>Enabled</code> is set to <code>true</code>. Specifies the format of the <code>CompletionReport</code>. <code>REPORT_CSV_20191124</code> is the only format currently supported. When <code>Format</code> is set to <code>REPORT_CSV_20191124</code>, the <code>CompletionReport</code> is provided in CSV format, and is delivered to <code>{path}/task-{id}/failures.csv</code>. </p>"}, "Scope": {"shape": "ReportScope", "documentation": "<p>Required if <code>Enabled</code> is set to <code>true</code>. Specifies the scope of the <code>CompletionReport</code>; <code>FAILED_FILES_ONLY</code> is the only scope currently supported. When <code>Scope</code> is set to <code>FAILED_FILES_ONLY</code>, the <code>CompletionReport</code> only contains information about files that the data repository task failed to process.</p>"}}, "documentation": "<p>Provides a report detailing the data repository task results of the files processed that match the criteria specified in the report <code>Scope</code> parameter. FSx delivers the report to the file system's linked data repository in Amazon S3, using the path specified in the report <code>Path</code> parameter. You can specify whether or not a report gets generated for a task using the <code>Enabled</code> parameter.</p>"}, "CoolingPeriod": {"type": "integer", "max": 183, "min": 2}, "CopyBackupRequest": {"type": "structure", "required": ["SourceBackupId"], "members": {"ClientRequestToken": {"shape": "ClientRequestToken", "idempotencyToken": true}, "SourceBackupId": {"shape": "SourceBackupId", "documentation": "<p>The ID of the source backup. Specifies the ID of the backup that's being copied.</p>"}, "SourceRegion": {"shape": "Region", "documentation": "<p>The source Amazon Web Services Region of the backup. Specifies the Amazon Web Services Region from which the backup is being copied. The source and destination Regions must be in the same Amazon Web Services partition. If you don't specify a Region, <code>SourceRegion</code> defaults to the Region where the request is sent from (in-Region copy).</p>"}, "KmsKeyId": {"shape": "KmsKeyId"}, "CopyTags": {"shape": "Flag", "documentation": "<p>A Boolean flag indicating whether tags from the source backup should be copied to the backup copy. This value defaults to <code>false</code>.</p> <p>If you set <code>CopyTags</code> to <code>true</code> and the source backup has existing tags, you can use the <code>Tags</code> parameter to create new tags, provided that the sum of the source backup tags and the new tags doesn't exceed 50. Both sets of tags are merged. If there are tag conflicts (for example, two tags with the same key but different values), the tags created with the <code>Tags</code> parameter take precedence.</p>"}, "Tags": {"shape": "Tags"}}}, "CopyBackupResponse": {"type": "structure", "members": {"Backup": {"shape": "Backup"}}}, "CopySnapshotAndUpdateVolumeRequest": {"type": "structure", "required": ["VolumeId", "SourceSnapshotARN"], "members": {"ClientRequestToken": {"shape": "ClientRequestToken", "idempotencyToken": true}, "VolumeId": {"shape": "VolumeId", "documentation": "<p>Specifies the ID of the volume that you are copying the snapshot to.</p>"}, "SourceSnapshotARN": {"shape": "ResourceARN"}, "CopyStrategy": {"shape": "OpenZFSCopyStrategy", "documentation": "<p>Specifies the strategy to use when copying data from a snapshot to the volume. </p> <ul> <li> <p> <code>FULL_COPY</code> - Copies all data from the snapshot to the volume. </p> </li> <li> <p> <code>INCREMENTAL_COPY</code> - Copies only the snapshot data that's changed since the previous replication.</p> </li> </ul> <note> <p> <code>CLONE</code> isn't a valid copy strategy option for the <code>CopySnapshotAndUpdateVolume</code> operation.</p> </note>"}, "Options": {"shape": "UpdateOpenZFSVolumeOptions", "documentation": "<p>Confirms that you want to delete data on the destination volume that wasn’t there during the previous snapshot replication.</p> <p>Your replication will fail if you don’t include an option for a specific type of data and that data is on your destination. For example, if you don’t include <code>DELETE_INTERMEDIATE_SNAPSHOTS</code> and there are intermediate snapshots on the destination, you can’t copy the snapshot.</p> <ul> <li> <p> <code>DELETE_INTERMEDIATE_SNAPSHOTS</code> - Deletes snapshots on the destination volume that aren’t on the source volume.</p> </li> <li> <p> <code>DELETE_CLONED_VOLUMES</code> - Deletes snapshot clones on the destination volume that aren't on the source volume.</p> </li> <li> <p> <code>DELETE_INTERMEDIATE_DATA</code> - Overwrites snapshots on the destination volume that don’t match the source snapshot that you’re copying.</p> </li> </ul>"}}}, "CopySnapshotAndUpdateVolumeResponse": {"type": "structure", "members": {"VolumeId": {"shape": "VolumeId", "documentation": "<p>The ID of the volume that you copied the snapshot to.</p>"}, "Lifecycle": {"shape": "VolumeLifecycle", "documentation": "<p>The lifecycle state of the destination volume. </p>"}, "AdministrativeActions": {"shape": "AdministrativeActions", "documentation": "<p>A list of administrative actions for the file system that are in process or waiting to be processed. Administrative actions describe changes to the Amazon FSx system.</p>"}}}, "CopyTagsToDataRepositoryAssociations": {"type": "boolean"}, "CreateAggregateConfiguration": {"type": "structure", "members": {"Aggregates": {"shape": "Aggregates", "documentation": "<p>Used to specify the names of aggregates on which the volume will be created.</p>"}, "ConstituentsPerAggregate": {"shape": "AggregateListMultiplier", "documentation": "<p>Used to explicitly set the number of constituents within the FlexGroup per storage aggregate. This field is optional when creating a FlexGroup volume. If unspecified, the default value will be 8. This field cannot be provided when creating a FlexVol volume.</p>"}}, "documentation": "<p>Used to specify the configuration options for an FSx for ONTAP volume's storage aggregate or aggregates.</p>"}, "CreateAndAttachS3AccessPointOpenZFSConfiguration": {"type": "structure", "required": ["VolumeId", "FileSystemIdentity"], "members": {"VolumeId": {"shape": "VolumeId", "documentation": "<p>The ID of the FSx for OpenZFS volume to which you want the S3 access point attached.</p>"}, "FileSystemIdentity": {"shape": "OpenZFSFileSystemIdentity", "documentation": "<p>Specifies the file system user identity to use for authorizing file read and write requests that are made using this S3 access point.</p>"}}, "documentation": "<p>Specifies the FSx for OpenZFS volume that the S3 access point will be attached to, and the file system user identity.</p>"}, "CreateAndAttachS3AccessPointRequest": {"type": "structure", "required": ["Name", "Type"], "members": {"ClientRequestToken": {"shape": "ClientRequestToken", "idempotencyToken": true}, "Name": {"shape": "S3AccessPointAttachmentName", "documentation": "<p>The name you want to assign to this S3 access point.</p>"}, "Type": {"shape": "S3AccessPointAttachmentType", "documentation": "<p>The type of S3 access point you want to create. Only <code>OpenZFS</code> is supported.</p>"}, "OpenZFSConfiguration": {"shape": "CreateAndAttachS3AccessPointOpenZFSConfiguration", "documentation": "<p>Specifies the configuration to use when creating and attaching an S3 access point to an FSx for OpenZFS volume.</p>"}, "S3AccessPoint": {"shape": "CreateAndAttachS3AccessPointS3Configuration", "documentation": "<p>Specifies the virtual private cloud (VPC) configuration if you're creating an access point that is restricted to a VPC. For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/OpenZFSGuide/access-points-vpc.html\">Creating access points restricted to a virtual private cloud</a>.</p>"}}}, "CreateAndAttachS3AccessPointResponse": {"type": "structure", "members": {"S3AccessPointAttachment": {"shape": "S3AccessPointAttachment", "documentation": "<p>Describes the configuration of the S3 access point created.</p>"}}}, "CreateAndAttachS3AccessPointS3Configuration": {"type": "structure", "members": {"VpcConfiguration": {"shape": "S3AccessPointVpcConfiguration", "documentation": "<p>If included, Amazon S3 restricts access to this S3 access point to requests made from the specified virtual private cloud (VPC).</p>"}, "Policy": {"shape": "AccessPointPolicy", "documentation": "<p>Specifies an access policy to associate with the S3 access point configuration. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/access-points-policies.html\">Configuring IAM policies for using access points</a> in the Amazon Simple Storage Service User Guide.</p>"}}, "documentation": "<p>Used to create an S3 access point that accepts requests only from a virtual private cloud (VPC) to restrict data access to a private network.</p>"}, "CreateBackupRequest": {"type": "structure", "members": {"FileSystemId": {"shape": "FileSystemId", "documentation": "<p>The ID of the file system to back up.</p>"}, "ClientRequestToken": {"shape": "ClientRequestToken", "documentation": "<p>(Optional) A string of up to 63 ASCII characters that Amazon FSx uses to ensure idempotent creation. This string is automatically filled on your behalf when you use the Command Line Interface (CLI) or an Amazon Web Services SDK.</p>", "idempotencyToken": true}, "Tags": {"shape": "Tags", "documentation": "<p>(Optional) The tags to apply to the backup at backup creation. The key value of the <code>Name</code> tag appears in the console as the backup name. If you have set <code>CopyTagsToBackups</code> to <code>true</code>, and you specify one or more tags using the <code>CreateBackup</code> operation, no existing file system tags are copied from the file system to the backup.</p>"}, "VolumeId": {"shape": "VolumeId", "documentation": "<p>(Optional) The ID of the FSx for ONTAP volume to back up.</p>"}}, "documentation": "<p>The request object for the <code>CreateBackup</code> operation.</p>"}, "CreateBackupResponse": {"type": "structure", "members": {"Backup": {"shape": "Backup", "documentation": "<p>A description of the backup.</p>"}}, "documentation": "<p>The response object for the <code>CreateBackup</code> operation.</p>"}, "CreateDataRepositoryAssociationRequest": {"type": "structure", "required": ["FileSystemId", "DataRepositoryPath"], "members": {"FileSystemId": {"shape": "FileSystemId"}, "FileSystemPath": {"shape": "Namespace", "documentation": "<p>A path on the file system that points to a high-level directory (such as <code>/ns1/</code>) or subdirectory (such as <code>/ns1/subdir/</code>) that will be mapped 1-1 with <code>DataRepositoryPath</code>. The leading forward slash in the name is required. Two data repository associations cannot have overlapping file system paths. For example, if a data repository is associated with file system path <code>/ns1/</code>, then you cannot link another data repository with file system path <code>/ns1/ns2</code>.</p> <p>This path specifies where in your file system files will be exported from or imported to. This file system directory can be linked to only one Amazon S3 bucket, and no other S3 bucket can be linked to the directory.</p> <note> <p>If you specify only a forward slash (<code>/</code>) as the file system path, you can link only one data repository to the file system. You can only specify \"/\" as the file system path for the first data repository associated with a file system.</p> </note>"}, "DataRepositoryPath": {"shape": "ArchivePath", "documentation": "<p>The path to the Amazon S3 data repository that will be linked to the file system. The path can be an S3 bucket or prefix in the format <code>s3://bucket-name/prefix/</code> (where <code>prefix</code> is optional). This path specifies where in the S3 data repository files will be imported from or exported to.</p>"}, "BatchImportMetaDataOnCreate": {"shape": "BatchImportMetaDataOnCreate", "documentation": "<p>Set to <code>true</code> to run an import data repository task to import metadata from the data repository to the file system after the data repository association is created. Default is <code>false</code>.</p>"}, "ImportedFileChunkSize": {"shape": "Megabytes", "documentation": "<p>For files imported from a data repository, this value determines the stripe count and maximum amount of data per file (in MiB) stored on a single physical disk. The maximum number of disks that a single file can be striped across is limited by the total number of disks that make up the file system.</p> <p>The default chunk size is 1,024 MiB (1 GiB) and can go as high as 512,000 MiB (500 GiB). Amazon S3 objects have a maximum size of 5 TB.</p>"}, "S3": {"shape": "S3DataRepositoryConfiguration", "documentation": "<p>The configuration for an Amazon S3 data repository linked to an Amazon FSx Lustre file system with a data repository association. The configuration defines which file events (new, changed, or deleted files or directories) are automatically imported from the linked data repository to the file system or automatically exported from the file system to the data repository.</p>"}, "ClientRequestToken": {"shape": "ClientRequestToken", "idempotencyToken": true}, "Tags": {"shape": "Tags"}}}, "CreateDataRepositoryAssociationResponse": {"type": "structure", "members": {"Association": {"shape": "DataRepositoryAssociation", "documentation": "<p>The response object returned after the data repository association is created.</p>"}}}, "CreateDataRepositoryTaskRequest": {"type": "structure", "required": ["Type", "FileSystemId", "Report"], "members": {"Type": {"shape": "DataRepositoryTaskType", "documentation": "<p>Specifies the type of data repository task to create.</p> <ul> <li> <p> <code>EXPORT_TO_REPOSITORY</code> tasks export from your Amazon FSx for Lustre file system to a linked data repository.</p> </li> <li> <p> <code>IMPORT_METADATA_FROM_REPOSITORY</code> tasks import metadata changes from a linked S3 bucket to your Amazon FSx for Lustre file system.</p> </li> <li> <p> <code>RELEASE_DATA_FROM_FILESYSTEM</code> tasks release files in your Amazon FSx for Lustre file system that have been exported to a linked S3 bucket and that meet your specified release criteria.</p> </li> <li> <p> <code>AUTO_RELEASE_DATA</code> tasks automatically release files from an Amazon File Cache resource.</p> </li> </ul>"}, "Paths": {"shape": "DataRepositoryTaskPaths", "documentation": "<p>A list of paths for the data repository task to use when the task is processed. If a path that you provide isn't valid, the task fails. If you don't provide paths, the default behavior is to export all files to S3 (for export tasks), import all files from S3 (for import tasks), or release all exported files that meet the last accessed time criteria (for release tasks).</p> <ul> <li> <p>For export tasks, the list contains paths on the FSx for Lustre file system from which the files are exported to the Amazon S3 bucket. The default path is the file system root directory. The paths you provide need to be relative to the mount point of the file system. If the mount point is <code>/mnt/fsx</code> and <code>/mnt/fsx/path1</code> is a directory or file on the file system you want to export, then the path to provide is <code>path1</code>.</p> </li> <li> <p>For import tasks, the list contains paths in the Amazon S3 bucket from which POSIX metadata changes are imported to the FSx for Lustre file system. The path can be an S3 bucket or prefix in the format <code>s3://bucket-name/prefix</code> (where <code>prefix</code> is optional).</p> </li> <li> <p>For release tasks, the list contains directory or file paths on the FSx for Lustre file system from which to release exported files. If a directory is specified, files within the directory are released. If a file path is specified, only that file is released. To release all exported files in the file system, specify a forward slash (/) as the path.</p> <note> <p>A file must also meet the last accessed time criteria specified in for the file to be released.</p> </note> </li> </ul>"}, "FileSystemId": {"shape": "FileSystemId"}, "Report": {"shape": "CompletionReport", "documentation": "<p>Defines whether or not Amazon FSx provides a CompletionReport once the task has completed. A CompletionReport provides a detailed report on the files that Amazon FSx processed that meet the criteria specified by the <code>Scope</code> parameter. For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/LustreGuide/task-completion-report.html\">Working with Task Completion Reports</a>.</p>"}, "ClientRequestToken": {"shape": "ClientRequestToken", "idempotencyToken": true}, "Tags": {"shape": "Tags"}, "CapacityToRelease": {"shape": "CapacityToRelease", "documentation": "<p>Specifies the amount of data to release, in GiB, by an Amazon File Cache <code>AUTO_RELEASE_DATA</code> task that automatically releases files from the cache.</p>"}, "ReleaseConfiguration": {"shape": "ReleaseConfiguration", "documentation": "<p>The configuration that specifies the last accessed time criteria for files that will be released from an Amazon FSx for Lustre file system.</p>"}}}, "CreateDataRepositoryTaskResponse": {"type": "structure", "members": {"DataRepositoryTask": {"shape": "DataRepositoryTask", "documentation": "<p>The description of the data repository task that you just created.</p>"}}}, "CreateFileCacheDataRepositoryAssociations": {"type": "list", "member": {"shape": "FileCacheDataRepositoryAssociation"}, "max": 8}, "CreateFileCacheLustreConfiguration": {"type": "structure", "required": ["PerUnitStorageThroughput", "DeploymentType", "MetadataConfiguration"], "members": {"PerUnitStorageThroughput": {"shape": "PerUnitStorageThroughput", "documentation": "<p>Provisions the amount of read and write throughput for each 1 tebibyte (TiB) of cache storage capacity, in MB/s/TiB. The only supported value is <code>1000</code>.</p>"}, "DeploymentType": {"shape": "FileCacheLustreDeploymentType", "documentation": "<p>Specifies the cache deployment type, which must be <code>CACHE_1</code>.</p>"}, "WeeklyMaintenanceStartTime": {"shape": "WeeklyTime"}, "MetadataConfiguration": {"shape": "FileCacheLustreMetadataConfiguration", "documentation": "<p>The configuration for a Lustre MDT (Metadata Target) storage volume.</p>"}}, "documentation": "<p>The Amazon File Cache configuration for the cache that you are creating.</p>"}, "CreateFileCacheRequest": {"type": "structure", "required": ["FileCacheType", "FileCacheTypeVersion", "StorageCapacity", "SubnetIds"], "members": {"ClientRequestToken": {"shape": "ClientRequestToken", "documentation": "<p>An idempotency token for resource creation, in a string of up to 63 ASCII characters. This token is automatically filled on your behalf when you use the Command Line Interface (CLI) or an Amazon Web Services SDK.</p> <p>By using the idempotent operation, you can retry a <code>CreateFileCache</code> operation without the risk of creating an extra cache. This approach can be useful when an initial call fails in a way that makes it unclear whether a cache was created. Examples are if a transport level timeout occurred, or your connection was reset. If you use the same client request token and the initial call created a cache, the client receives success as long as the parameters are the same.</p>", "idempotencyToken": true}, "FileCacheType": {"shape": "FileCacheType", "documentation": "<p>The type of cache that you're creating, which must be <code>LUSTRE</code>.</p>"}, "FileCacheTypeVersion": {"shape": "FileSystemTypeVersion", "documentation": "<p>Sets the Lustre version for the cache that you're creating, which must be <code>2.12</code>.</p>"}, "StorageCapacity": {"shape": "StorageCapacity", "documentation": "<p>The storage capacity of the cache in gibibytes (GiB). Valid values are 1200 GiB, 2400 GiB, and increments of 2400 GiB.</p>"}, "SubnetIds": {"shape": "SubnetIds"}, "SecurityGroupIds": {"shape": "SecurityGroupIds", "documentation": "<p>A list of IDs specifying the security groups to apply to all network interfaces created for Amazon File Cache access. This list isn't returned in later requests to describe the cache.</p>"}, "Tags": {"shape": "Tags"}, "CopyTagsToDataRepositoryAssociations": {"shape": "CopyTagsToDataRepositoryAssociations", "documentation": "<p>A boolean flag indicating whether tags for the cache should be copied to data repository associations. This value defaults to false.</p>"}, "KmsKeyId": {"shape": "KmsKeyId", "documentation": "<p>Specifies the ID of the Key Management Service (KMS) key to use for encrypting data on an Amazon File Cache. If a <code>KmsKeyId</code> isn't specified, the Amazon FSx-managed KMS key for your account is used. For more information, see <a href=\"https://docs.aws.amazon.com/kms/latest/APIReference/API_Encrypt.html\">Encrypt</a> in the <i>Key Management Service API Reference</i>.</p>"}, "LustreConfiguration": {"shape": "CreateFileCacheLustreConfiguration", "documentation": "<p>The configuration for the Amazon File Cache resource being created.</p>"}, "DataRepositoryAssociations": {"shape": "CreateFileCacheDataRepositoryAssociations", "documentation": "<p>A list of up to 8 configurations for data repository associations (DRAs) to be created during the cache creation. The DRAs link the cache to either an Amazon S3 data repository or a Network File System (NFS) data repository that supports the NFSv3 protocol.</p> <p>The DRA configurations must meet the following requirements:</p> <ul> <li> <p>All configurations on the list must be of the same data repository type, either all S3 or all NFS. A cache can't link to different data repository types at the same time.</p> </li> <li> <p>An NFS DRA must link to an NFS file system that supports the NFSv3 protocol.</p> </li> </ul> <p>DRA automatic import and automatic export is not supported.</p>"}}}, "CreateFileCacheResponse": {"type": "structure", "members": {"FileCache": {"shape": "FileCacheCreating", "documentation": "<p>A description of the cache that was created.</p>"}}}, "CreateFileSystemFromBackupRequest": {"type": "structure", "required": ["BackupId", "SubnetIds"], "members": {"BackupId": {"shape": "BackupId"}, "ClientRequestToken": {"shape": "ClientRequestToken", "documentation": "<p>A string of up to 63 ASCII characters that Amazon FSx uses to ensure idempotent creation. This string is automatically filled on your behalf when you use the Command Line Interface (CLI) or an Amazon Web Services SDK.</p>", "idempotencyToken": true}, "SubnetIds": {"shape": "SubnetIds", "documentation": "<p>Specifies the IDs of the subnets that the file system will be accessible from. For Windows <code>MULTI_AZ_1</code> file system deployment types, provide exactly two subnet IDs, one for the preferred file server and one for the standby file server. You specify one of these subnets as the preferred subnet using the <code>WindowsConfiguration &gt; PreferredSubnetID</code> property.</p> <p>Windows <code>SINGLE_AZ_1</code> and <code>SINGLE_AZ_2</code> file system deployment types, Lustre file systems, and OpenZFS file systems provide exactly one subnet ID. The file server is launched in that subnet's Availability Zone.</p>"}, "SecurityGroupIds": {"shape": "SecurityGroupIds", "documentation": "<p>A list of IDs for the security groups that apply to the specified network interfaces created for file system access. These security groups apply to all network interfaces. This value isn't returned in later <code>DescribeFileSystem</code> requests.</p>"}, "Tags": {"shape": "Tags", "documentation": "<p>The tags to be applied to the file system at file system creation. The key value of the <code>Name</code> tag appears in the console as the file system name.</p>"}, "WindowsConfiguration": {"shape": "CreateFileSystemWindowsConfiguration", "documentation": "<p>The configuration for this Microsoft Windows file system.</p>"}, "LustreConfiguration": {"shape": "CreateFileSystemLustreConfiguration"}, "StorageType": {"shape": "StorageType", "documentation": "<p>Sets the storage type for the Windows, OpenZFS, or Lustre file system that you're creating from a backup. Valid values are <code>SSD</code>, <code>HDD</code>, and <code>INTELLIGENT_TIERING</code>.</p> <ul> <li> <p>Set to <code>SSD</code> to use solid state drive storage. SSD is supported on all Windows and OpenZFS deployment types.</p> </li> <li> <p>Set to <code>HDD</code> to use hard disk drive storage. HDD is supported on <code>SINGLE_AZ_2</code> and <code>MULTI_AZ_1</code> FSx for Windows File Server file system deployment types.</p> </li> <li> <p>Set to <code>INTELLIGENT_TIERING</code> to use fully elastic, intelligently-tiered storage. Intelligent-Tiering is only available for OpenZFS file systems with the Multi-AZ deployment type and for Lustre file systems with the Persistent_2 deployment type.</p> </li> </ul> <p> The default value is <code>SSD</code>. </p> <note> <p>HDD and SSD storage types have different minimum storage capacity requirements. A restored file system's storage capacity is tied to the file system that was backed up. You can create a file system that uses HDD storage from a backup of a file system that used SSD storage if the original SSD file system had a storage capacity of at least 2000 GiB.</p> </note>"}, "KmsKeyId": {"shape": "KmsKeyId"}, "FileSystemTypeVersion": {"shape": "FileSystemTypeVersion", "documentation": "<p>Sets the version for the Amazon FSx for Lustre file system that you're creating from a backup. Valid values are <code>2.10</code>, <code>2.12</code>, and <code>2.15</code>.</p> <p>You can enter a Lustre version that is newer than the backup's <code>FileSystemTypeVersion</code> setting. If you don't enter a newer Lustre version, it defaults to the backup's setting.</p>"}, "OpenZFSConfiguration": {"shape": "CreateFileSystemOpenZFSConfiguration", "documentation": "<p>The OpenZFS configuration for the file system that's being created. </p>"}, "StorageCapacity": {"shape": "StorageCapacity", "documentation": "<p>Sets the storage capacity of the OpenZFS file system that you're creating from a backup, in gibibytes (GiB). Valid values are from 64 GiB up to 524,288 GiB (512 TiB). However, the value that you specify must be equal to or greater than the backup's storage capacity value. If you don't use the <code>StorageCapacity</code> parameter, the default is the backup's <code>StorageCapacity</code> value.</p> <p>If used to create a file system other than OpenZFS, you must provide a value that matches the backup's <code>StorageCapacity</code> value. If you provide any other value, Amazon FSx responds with an HTTP status code 400 Bad Request. </p>"}}, "documentation": "<p>The request object for the <code>CreateFileSystemFromBackup</code> operation.</p>"}, "CreateFileSystemFromBackupResponse": {"type": "structure", "members": {"FileSystem": {"shape": "FileSystem", "documentation": "<p>A description of the file system.</p>"}}, "documentation": "<p>The response object for the <code>CreateFileSystemFromBackup</code> operation.</p>"}, "CreateFileSystemLustreConfiguration": {"type": "structure", "members": {"WeeklyMaintenanceStartTime": {"shape": "WeeklyTime", "documentation": "<p>(Optional) The preferred start time to perform weekly maintenance, formatted d:HH:MM in the UTC time zone, where d is the weekday number, from 1 through 7, beginning with Monday and ending with Sunday.</p>"}, "ImportPath": {"shape": "ArchivePath", "documentation": "<p>(Optional) The path to the Amazon S3 bucket (including the optional prefix) that you're using as the data repository for your Amazon FSx for Lustre file system. The root of your FSx for Lustre file system will be mapped to the root of the Amazon S3 bucket you select. An example is <code>s3://import-bucket/optional-prefix</code>. If you specify a prefix after the Amazon S3 bucket name, only object keys with that prefix are loaded into the file system.</p> <note> <p>This parameter is not supported for file systems with a data repository association.</p> </note>"}, "ExportPath": {"shape": "ArchivePath", "documentation": "<p>(Optional) Specifies the path in the Amazon S3 bucket where the root of your Amazon FSx file system is exported. The path must use the same Amazon S3 bucket as specified in ImportPath. You can provide an optional prefix to which new and changed data is to be exported from your Amazon FSx for Lustre file system. If an <code>ExportPath</code> value is not provided, Amazon FSx sets a default export path, <code>s3://import-bucket/FSxLustre[creation-timestamp]</code>. The timestamp is in UTC format, for example <code>s3://import-bucket/FSxLustre20181105T222312Z</code>.</p> <p>The Amazon S3 export bucket must be the same as the import bucket specified by <code>ImportPath</code>. If you specify only a bucket name, such as <code>s3://import-bucket</code>, you get a 1:1 mapping of file system objects to S3 bucket objects. This mapping means that the input data in S3 is overwritten on export. If you provide a custom prefix in the export path, such as <code>s3://import-bucket/[custom-optional-prefix]</code>, Amazon FSx exports the contents of your file system to that export prefix in the Amazon S3 bucket.</p> <note> <p>This parameter is not supported for file systems with a data repository association.</p> </note>"}, "ImportedFileChunkSize": {"shape": "Megabytes", "documentation": "<p>(Optional) For files imported from a data repository, this value determines the stripe count and maximum amount of data per file (in MiB) stored on a single physical disk. The maximum number of disks that a single file can be striped across is limited by the total number of disks that make up the file system.</p> <p>The default chunk size is 1,024 MiB (1 GiB) and can go as high as 512,000 MiB (500 GiB). Amazon S3 objects have a maximum size of 5 TB.</p> <note> <p>This parameter is not supported for file systems with a data repository association.</p> </note>"}, "DeploymentType": {"shape": "LustreDeploymentType", "documentation": "<p>(Optional) Choose <code>SCRATCH_1</code> and <code>SCRATCH_2</code> deployment types when you need temporary storage and shorter-term processing of data. The <code>SCRATCH_2</code> deployment type provides in-transit encryption of data and higher burst throughput capacity than <code>SCRATCH_1</code>.</p> <p>Choose <code>PERSISTENT_1</code> for longer-term storage and for throughput-focused workloads that aren’t latency-sensitive. <code>PERSISTENT_1</code> supports encryption of data in transit, and is available in all Amazon Web Services Regions in which FSx for Lustre is available.</p> <p>Choose <code>PERSISTENT_2</code> for longer-term storage and for latency-sensitive workloads that require the highest levels of IOPS/throughput. <code>PERSISTENT_2</code> supports the SSD and Intelligent-Tiering storage classes. You can optionally specify a metadata configuration mode for <code>PERSISTENT_2</code> which supports increasing metadata performance. <code>PERSISTENT_2</code> is available in a limited number of Amazon Web Services Regions. For more information, and an up-to-date list of Amazon Web Services Regions in which <code>PERSISTENT_2</code> is available, see <a href=\"https://docs.aws.amazon.com/fsx/latest/LustreGuide/using-fsx-lustre.html\">Deployment and storage class options for FSx for Lustre file systems</a> in the <i>Amazon FSx for Lustre User Guide</i>.</p> <note> <p>If you choose <code>PERSISTENT_2</code>, and you set <code>FileSystemTypeVersion</code> to <code>2.10</code>, the <code>CreateFileSystem</code> operation fails.</p> </note> <p>Encryption of data in transit is automatically turned on when you access <code>SCRATCH_2</code>, <code>PERSISTENT_1</code>, and <code>PERSISTENT_2</code> file systems from Amazon EC2 instances that support automatic encryption in the Amazon Web Services Regions where they are available. For more information about encryption in transit for FSx for Lustre file systems, see <a href=\"https://docs.aws.amazon.com/fsx/latest/LustreGuide/encryption-in-transit-fsxl.html\">Encrypting data in transit</a> in the <i>Amazon FSx for Lustre User Guide</i>.</p> <p>(Default = <code>SCRATCH_1</code>)</p>"}, "AutoImportPolicy": {"shape": "AutoImportPolicyType", "documentation": "<p> (Optional) When you create your file system, your existing S3 objects appear as file and directory listings. Use this parameter to choose how Amazon FSx keeps your file and directory listings up to date as you add or modify objects in your linked S3 bucket. <code>AutoImportPolicy</code> can have the following values:</p> <ul> <li> <p> <code>NONE</code> - (Default) AutoImport is off. Amazon FSx only updates file and directory listings from the linked S3 bucket when the file system is created. FSx does not update file and directory listings for any new or changed objects after choosing this option.</p> </li> <li> <p> <code>NEW</code> - AutoImport is on. Amazon FSx automatically imports directory listings of any new objects added to the linked S3 bucket that do not currently exist in the FSx file system. </p> </li> <li> <p> <code>NEW_CHANGED</code> - AutoImport is on. Amazon FSx automatically imports file and directory listings of any new objects added to the S3 bucket and any existing objects that are changed in the S3 bucket after you choose this option.</p> </li> <li> <p> <code>NEW_CHANGED_DELETED</code> - AutoImport is on. Amazon FSx automatically imports file and directory listings of any new objects added to the S3 bucket, any existing objects that are changed in the S3 bucket, and any objects that were deleted in the S3 bucket.</p> </li> </ul> <p>For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/LustreGuide/older-deployment-types.html#legacy-auto-import-from-s3\"> Automatically import updates from your S3 bucket</a>.</p> <note> <p>This parameter is not supported for file systems with a data repository association.</p> </note>"}, "PerUnitStorageThroughput": {"shape": "PerUnitStorageThroughput", "documentation": "<p>Required with <code>PERSISTENT_1</code> and <code>PERSISTENT_2</code> deployment types using an SSD or HDD storage class, provisions the amount of read and write throughput for each 1 tebibyte (TiB) of file system storage capacity, in MB/s/TiB. File system throughput capacity is calculated by multiplying ﬁle system storage capacity (TiB) by the <code>PerUnitStorageThroughput</code> (MB/s/TiB). For a 2.4-TiB ﬁle system, provisioning 50 MB/s/TiB of <code>PerUnitStorageThroughput</code> yields 120 MB/s of ﬁle system throughput. You pay for the amount of throughput that you provision. </p> <p>Valid values:</p> <ul> <li> <p>For <code>PERSISTENT_1</code> SSD storage: 50, 100, 200 MB/s/TiB.</p> </li> <li> <p>For <code>PERSISTENT_1</code> HDD storage: 12, 40 MB/s/TiB.</p> </li> <li> <p>For <code>PERSISTENT_2</code> SSD storage: 125, 250, 500, 1000 MB/s/TiB.</p> </li> </ul>"}, "DailyAutomaticBackupStartTime": {"shape": "DailyTime"}, "AutomaticBackupRetentionDays": {"shape": "AutomaticBackupRetentionDays", "documentation": "<p>The number of days to retain automatic backups. Setting this property to <code>0</code> disables automatic backups. You can retain automatic backups for a maximum of 90 days. The default is <code>0</code>.</p>"}, "CopyTagsToBackups": {"shape": "Flag", "documentation": "<p>(Optional) Not available for use with file systems that are linked to a data repository. A boolean flag indicating whether tags for the file system should be copied to backups. The default value is false. If <code>CopyTagsToBackups</code> is set to true, all file system tags are copied to all automatic and user-initiated backups when the user doesn't specify any backup-specific tags. If <code>CopyTagsToBackups</code> is set to true and you specify one or more backup tags, only the specified tags are copied to backups. If you specify one or more tags when creating a user-initiated backup, no tags are copied from the file system, regardless of this value.</p> <p>(Default = <code>false</code>)</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/LustreGuide/using-backups-fsx.html\"> Working with backups</a> in the <i>Amazon FSx for Lustre User Guide</i>.</p>"}, "DriveCacheType": {"shape": "DriveCacheType", "documentation": "<p>The type of drive cache used by <code>PERSISTENT_1</code> file systems that are provisioned with HDD storage devices. This parameter is required when storage type is HDD. Set this property to <code>READ</code> to improve the performance for frequently accessed files by caching up to 20% of the total storage capacity of the file system.</p> <p>This parameter is required when <code>StorageType</code> is set to <code>HDD</code>.</p>"}, "DataCompressionType": {"shape": "DataCompressionType", "documentation": "<p>Sets the data compression configuration for the file system. <code>DataCompressionType</code> can have the following values:</p> <ul> <li> <p> <code>NONE</code> - (Default) Data compression is turned off when the file system is created.</p> </li> <li> <p> <code>LZ4</code> - Data compression is turned on with the LZ4 algorithm.</p> </li> </ul> <p>For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/LustreGuide/data-compression.html\">Lustre data compression</a> in the <i>Amazon FSx for Lustre User Guide</i>.</p>"}, "EfaEnabled": {"shape": "Flag", "documentation": "<p>(Optional) Specifies whether Elastic Fabric Adapter (EFA) and GPUDirect Storage (GDS) support is enabled for the Amazon FSx for Lustre file system.</p> <p>(Default = <code>false</code>)</p>"}, "LogConfiguration": {"shape": "LustreLogCreateConfiguration", "documentation": "<p>The Lustre logging configuration used when creating an Amazon FSx for Lustre file system. When logging is enabled, Lustre logs error and warning events for data repositories associated with your file system to Amazon CloudWatch Logs.</p>"}, "RootSquashConfiguration": {"shape": "LustreRootSquashConfiguration", "documentation": "<p>The Lustre root squash configuration used when creating an Amazon FSx for Lustre file system. When enabled, root squash restricts root-level access from clients that try to access your file system as a root user.</p>"}, "MetadataConfiguration": {"shape": "CreateFileSystemLustreMetadataConfiguration", "documentation": "<p>The Lustre metadata performance configuration for the creation of an FSx for Lustre file system using a <code>PERSISTENT_2</code> deployment type.</p>"}, "ThroughputCapacity": {"shape": "ThroughputCapacityMbps", "documentation": "<p>Specifies the throughput of an FSx for Lustre file system using the Intelligent-Tiering storage class, measured in megabytes per second (MBps). Valid values are 4000 MBps or multiples of 4000 MBps. You pay for the amount of throughput that you provision.</p>"}, "DataReadCacheConfiguration": {"shape": "LustreReadCacheConfiguration", "documentation": "<p>Specifies the optional provisioned SSD read cache on FSx for Lustre file systems that use the Intelligent-Tiering storage class. Required when <code>StorageType</code> is set to <code>INTELLIGENT_TIERING</code>.</p>"}}, "documentation": "<p>The Lustre configuration for the file system being created.</p> <note> <p>The following parameters are not supported for file systems with a data repository association created with .</p> <ul> <li> <p> <code>AutoImportPolicy</code> </p> </li> <li> <p> <code>ExportPath</code> </p> </li> <li> <p> <code>ImportedFileChunkSize</code> </p> </li> <li> <p> <code>ImportPath</code> </p> </li> </ul> </note>"}, "CreateFileSystemLustreMetadataConfiguration": {"type": "structure", "required": ["Mode"], "members": {"Iops": {"shape": "MetadataIops", "documentation": "<p>(USER_PROVISIONED mode only) Specifies the number of Metadata IOPS to provision for the file system. This parameter sets the maximum rate of metadata disk IOPS supported by the file system.</p> <ul> <li> <p>For SSD file systems, valid values are <code>1500</code>, <code>3000</code>, <code>6000</code>, <code>12000</code>, and multiples of <code>12000</code> up to a maximum of <code>192000</code>.</p> </li> <li> <p>For Intelligent-Tiering file systems, valid values are <code>6000</code> and <code>12000</code>.</p> </li> </ul> <note> <p> <code>Iops</code> doesn’t have a default value. If you're using USER_PROVISIONED mode, you can choose to specify a valid value. If you're using AUTOMATIC mode, you cannot specify a value because FSx for Lustre automatically sets the value based on your file system storage capacity. </p> </note>"}, "Mode": {"shape": "MetadataConfigurationMode", "documentation": "<p>The metadata configuration mode for provisioning Metadata IOPS for an FSx for Lustre file system using a <code>PERSISTENT_2</code> deployment type.</p> <ul> <li> <p>In AUTOMATIC mode (supported only on SSD file systems), FSx for Lustre automatically provisions and scales the number of Metadata IOPS for your file system based on your file system storage capacity.</p> </li> <li> <p>In USER_PROVISIONED mode, you specify the number of Metadata IOPS to provision for your file system.</p> </li> </ul>"}}, "documentation": "<p>The Lustre metadata performance configuration for the creation of an Amazon FSx for Lustre file system using a <code>PERSISTENT_2</code> deployment type. The configuration uses a Metadata IOPS value to set the maximum rate of metadata disk IOPS supported by the file system.</p> <p>After creation, the file system supports increasing metadata performance. For more information on Metadata IOPS, see <a href=\"https://docs.aws.amazon.com/fsx/latest/LustreGuide/managing-metadata-performance.html#metadata-configuration\">Lustre metadata performance configuration</a> in the <i>Amazon FSx for Lustre User Guide</i>.</p>"}, "CreateFileSystemOntapConfiguration": {"type": "structure", "required": ["DeploymentType"], "members": {"AutomaticBackupRetentionDays": {"shape": "AutomaticBackupRetentionDays"}, "DailyAutomaticBackupStartTime": {"shape": "DailyTime"}, "DeploymentType": {"shape": "OntapDeploymentType", "documentation": "<p>Specifies the FSx for ONTAP file system deployment type to use in creating the file system. </p> <ul> <li> <p> <code>MULTI_AZ_1</code> - A high availability file system configured for Multi-AZ redundancy to tolerate temporary Availability Zone (AZ) unavailability. This is a first-generation FSx for ONTAP file system.</p> </li> <li> <p> <code>MULTI_AZ_2</code> - A high availability file system configured for Multi-AZ redundancy to tolerate temporary AZ unavailability. This is a second-generation FSx for ONTAP file system.</p> </li> <li> <p> <code>SINGLE_AZ_1</code> - A file system configured for Single-AZ redundancy. This is a first-generation FSx for ONTAP file system.</p> </li> <li> <p> <code>SINGLE_AZ_2</code> - A file system configured with multiple high-availability (HA) pairs for Single-AZ redundancy. This is a second-generation FSx for ONTAP file system.</p> </li> </ul> <p>For information about the use cases for Multi-AZ and Single-AZ deployments, refer to <a href=\"https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/high-availability-AZ.html\">Choosing a file system deployment type</a>. </p>"}, "EndpointIpAddressRange": {"shape": "IpAddressRange", "documentation": "<p>(Multi-AZ only) Specifies the IP address range in which the endpoints to access your file system will be created. By default in the Amazon FSx API, Amazon FSx selects an unused IP address range for you from the 198.19.* range. By default in the Amazon FSx console, Amazon FSx chooses the last 64 IP addresses from the VPC’s primary CIDR range to use as the endpoint IP address range for the file system. You can have overlapping endpoint IP addresses for file systems deployed in the same VPC/route tables, as long as they don't overlap with any subnet.</p>"}, "FsxAdminPassword": {"shape": "AdminPassword", "documentation": "<p>The ONTAP administrative password for the <code>fsxadmin</code> user with which you administer your file system using the NetApp ONTAP CLI and REST API.</p>"}, "DiskIopsConfiguration": {"shape": "DiskIopsConfiguration", "documentation": "<p>The SSD IOPS configuration for the FSx for ONTAP file system.</p>"}, "PreferredSubnetId": {"shape": "SubnetId", "documentation": "<p>Required when <code>DeploymentType</code> is set to <code>MULTI_AZ_1</code> or <code>MULTI_AZ_2</code>. This specifies the subnet in which you want the preferred file server to be located.</p>"}, "RouteTableIds": {"shape": "RouteTableIds", "documentation": "<p>(Multi-AZ only) Specifies the route tables in which Amazon FSx creates the rules for routing traffic to the correct file server. You should specify all virtual private cloud (VPC) route tables associated with the subnets in which your clients are located. By default, Amazon FSx selects your VPC's default route table.</p> <note> <p>Amazon FSx manages these route tables for Multi-AZ file systems using tag-based authentication. These route tables are tagged with <code>Key: AmazonFSx; Value: ManagedByAmazonFSx</code>. When creating FSx for ONTAP Multi-AZ file systems using CloudFormation we recommend that you add the <code>Key: AmazonFSx; Value: ManagedByAmazonFSx</code> tag manually.</p> </note>"}, "ThroughputCapacity": {"shape": "MegabytesPerSecond", "documentation": "<p>Sets the throughput capacity for the file system that you're creating in megabytes per second (MBps). For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/managing-throughput-capacity.html\">Managing throughput capacity</a> in the FSx for ONTAP User Guide.</p> <p>Amazon FSx responds with an HTTP status code 400 (Bad Request) for the following conditions:</p> <ul> <li> <p>The value of <code>ThroughputCapacity</code> and <code>ThroughputCapacityPerHAPair</code> are not the same value.</p> </li> <li> <p>The value of <code>ThroughputCapacity</code> when divided by the value of <code>HAPairs</code> is outside of the valid range for <code>ThroughputCapacity</code>.</p> </li> </ul>"}, "WeeklyMaintenanceStartTime": {"shape": "WeeklyTime"}, "HAPairs": {"shape": "HAPairs", "documentation": "<p>Specifies how many high-availability (HA) pairs of file servers will power your file system. First-generation file systems are powered by 1 HA pair. Second-generation multi-AZ file systems are powered by 1 HA pair. Second generation single-AZ file systems are powered by up to 12 HA pairs. The default value is 1. The value of this property affects the values of <code>StorageCapacity</code>, <code>Iops</code>, and <code>ThroughputCapacity</code>. For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/administering-file-systems.html#HA-pairs\">High-availability (HA) pairs</a> in the FSx for ONTAP user guide. Block storage protocol support (iSCSI and NVMe over TCP) is disabled on file systems with more than 6 HA pairs. For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/supported-fsx-clients.html#using-block-storage\">Using block storage protocols</a>. </p> <p>Amazon FSx responds with an HTTP status code 400 (Bad Request) for the following conditions:</p> <ul> <li> <p>The value of <code>HAPairs</code> is less than 1 or greater than 12.</p> </li> <li> <p>The value of <code>HAPairs</code> is greater than 1 and the value of <code>DeploymentType</code> is <code>SINGLE_AZ_1</code>, <code>MULTI_AZ_1</code>, or <code>MULTI_AZ_2</code>.</p> </li> </ul>"}, "ThroughputCapacityPerHAPair": {"shape": "ThroughputCapacityPerHAPair", "documentation": "<p>Use to choose the throughput capacity per HA pair, rather than the total throughput for the file system. </p> <p>You can define either the <code>ThroughputCapacityPerHAPair</code> or the <code>ThroughputCapacity</code> when creating a file system, but not both.</p> <p>This field and <code>ThroughputCapacity</code> are the same for file systems powered by one HA pair.</p> <ul> <li> <p>For <code>SINGLE_AZ_1</code> and <code>MULTI_AZ_1</code> file systems, valid values are 128, 256, 512, 1024, 2048, or 4096 MBps.</p> </li> <li> <p>For <code>SINGLE_AZ_2</code>, valid values are 1536, 3072, or 6144 MBps.</p> </li> <li> <p>For <code>MULTI_AZ_2</code>, valid values are 384, 768, 1536, 3072, or 6144 MBps.</p> </li> </ul> <p>Amazon FSx responds with an HTTP status code 400 (Bad Request) for the following conditions:</p> <ul> <li> <p>The value of <code>ThroughputCapacity</code> and <code>ThroughputCapacityPerHAPair</code> are not the same value for file systems with one HA pair.</p> </li> <li> <p>The value of deployment type is <code>SINGLE_AZ_2</code> and <code>ThroughputCapacity</code> / <code>ThroughputCapacityPerHAPair</code> is not a valid HA pair (a value between 1 and 12).</p> </li> <li> <p>The value of <code>ThroughputCapacityPerHAPair</code> is not a valid value.</p> </li> </ul>"}}, "documentation": "<p>The ONTAP configuration properties of the FSx for ONTAP file system that you are creating.</p>"}, "CreateFileSystemOpenZFSConfiguration": {"type": "structure", "required": ["DeploymentType", "ThroughputCapacity"], "members": {"AutomaticBackupRetentionDays": {"shape": "AutomaticBackupRetentionDays"}, "CopyTagsToBackups": {"shape": "Flag", "documentation": "<p>A Boolean value indicating whether tags for the file system should be copied to backups. This value defaults to <code>false</code>. If it's set to <code>true</code>, all tags for the file system are copied to all automatic and user-initiated backups where the user doesn't specify tags. If this value is <code>true</code>, and you specify one or more tags, only the specified tags are copied to backups. If you specify one or more tags when creating a user-initiated backup, no tags are copied from the file system, regardless of this value.</p>"}, "CopyTagsToVolumes": {"shape": "Flag", "documentation": "<p>A Boolean value indicating whether tags for the file system should be copied to volumes. This value defaults to <code>false</code>. If it's set to <code>true</code>, all tags for the file system are copied to volumes where the user doesn't specify tags. If this value is <code>true</code>, and you specify one or more tags, only the specified tags are copied to volumes. If you specify one or more tags when creating the volume, no tags are copied from the file system, regardless of this value.</p>"}, "DailyAutomaticBackupStartTime": {"shape": "DailyTime"}, "DeploymentType": {"shape": "OpenZFSDeploymentType", "documentation": "<p>Specifies the file system deployment type. Valid values are the following:</p> <ul> <li> <p> <code>MULTI_AZ_1</code>- Creates file systems with high availability and durability by replicating your data and supporting failover across multiple Availability Zones in the same Amazon Web Services Region.</p> </li> <li> <p> <code>SINGLE_AZ_HA_2</code>- Creates file systems with high availability and throughput capacities of 160 - 10,240 MB/s using an NVMe L2ARC cache by deploying a primary and standby file system within the same Availability Zone.</p> </li> <li> <p> <code>SINGLE_AZ_HA_1</code>- Creates file systems with high availability and throughput capacities of 64 - 4,096 MB/s by deploying a primary and standby file system within the same Availability Zone.</p> </li> <li> <p> <code>SINGLE_AZ_2</code>- Creates file systems with throughput capacities of 160 - 10,240 MB/s using an NVMe L2ARC cache that automatically recover within a single Availability Zone.</p> </li> <li> <p> <code>SINGLE_AZ_1</code>- Creates file systems with throughput capacities of 64 - 4,096 MBs that automatically recover within a single Availability Zone.</p> </li> </ul> <p>For a list of which Amazon Web Services Regions each deployment type is available in, see <a href=\"https://docs.aws.amazon.com/fsx/latest/OpenZFSGuide/availability-durability.html#available-aws-regions\">Deployment type availability</a>. For more information on the differences in performance between deployment types, see <a href=\"https://docs.aws.amazon.com/fsx/latest/OpenZFSGuide/performance.html#zfs-fs-performance\">File system performance</a> in the <i>Amazon FSx for OpenZFS User Guide</i>.</p>"}, "ThroughputCapacity": {"shape": "MegabytesPerSecond", "documentation": "<p>Specifies the throughput of an Amazon FSx for OpenZFS file system, measured in megabytes per second (MBps). Valid values depend on the <code>DeploymentType</code> that you choose, as follows:</p> <ul> <li> <p>For <code>MULTI_AZ_1</code> and <code>SINGLE_AZ_2</code>, valid values are 160, 320, 640, 1280, 2560, 3840, 5120, 7680, or 10240 MBps.</p> </li> <li> <p>For <code>SINGLE_AZ_1</code>, valid values are 64, 128, 256, 512, 1024, 2048, 3072, or 4096 MBps.</p> </li> </ul> <p>You pay for additional throughput capacity that you provision.</p>"}, "WeeklyMaintenanceStartTime": {"shape": "WeeklyTime"}, "DiskIopsConfiguration": {"shape": "DiskIopsConfiguration"}, "RootVolumeConfiguration": {"shape": "OpenZFSCreateRootVolumeConfiguration", "documentation": "<p>The configuration Amazon FSx uses when creating the root value of the Amazon FSx for OpenZFS file system. All volumes are children of the root volume. </p>"}, "PreferredSubnetId": {"shape": "SubnetId", "documentation": "<p>Required when <code>DeploymentType</code> is set to <code>MULTI_AZ_1</code>. This specifies the subnet in which you want the preferred file server to be located.</p>"}, "EndpointIpAddressRange": {"shape": "IpAddressRange", "documentation": "<p>(Multi-AZ only) Specifies the IP address range in which the endpoints to access your file system will be created. By default in the Amazon FSx API and Amazon FSx console, Amazon FSx selects an available /28 IP address range for you from one of the VPC's CIDR ranges. You can have overlapping endpoint IP addresses for file systems deployed in the same VPC/route tables, as long as they don't overlap with any subnet.</p>"}, "RouteTableIds": {"shape": "RouteTableIds", "documentation": "<p>(Multi-AZ only) Specifies the route tables in which Amazon FSx creates the rules for routing traffic to the correct file server. You should specify all virtual private cloud (VPC) route tables associated with the subnets in which your clients are located. By default, Amazon FSx selects your VPC's default route table.</p>"}, "ReadCacheConfiguration": {"shape": "OpenZFSReadCacheConfiguration", "documentation": "<p> Specifies the optional provisioned SSD read cache on file systems that use the Intelligent-Tiering storage class. </p>"}}, "documentation": "<p>The Amazon FSx for OpenZFS configuration properties for the file system that you are creating.</p>"}, "CreateFileSystemRequest": {"type": "structure", "required": ["FileSystemType", "SubnetIds"], "members": {"ClientRequestToken": {"shape": "ClientRequestToken", "documentation": "<p>A string of up to 63 ASCII characters that Amazon FSx uses to ensure idempotent creation. This string is automatically filled on your behalf when you use the Command Line Interface (CLI) or an Amazon Web Services SDK.</p>", "idempotencyToken": true}, "FileSystemType": {"shape": "FileSystemType", "documentation": "<p>The type of Amazon FSx file system to create. Valid values are <code>WINDOWS</code>, <code>LUSTRE</code>, <code>ONTAP</code>, and <code>OPENZFS</code>.</p>"}, "StorageCapacity": {"shape": "StorageCapacity", "documentation": "<p>Sets the storage capacity of the file system that you're creating, in gibibytes (GiB).</p> <p> <b>FSx for Lustre file systems</b> - The amount of storage capacity that you can configure depends on the value that you set for <code>StorageType</code> and the Lustre <code>DeploymentType</code>, as follows:</p> <ul> <li> <p>For <code>SCRATCH_2</code>, <code>PERSISTENT_2</code>, and <code>PERSISTENT_1</code> deployment types using SSD storage type, the valid values are 1200 GiB, 2400 GiB, and increments of 2400 GiB.</p> </li> <li> <p>For <code>PERSISTENT_1</code> HDD file systems, valid values are increments of 6000 GiB for 12 MB/s/TiB file systems and increments of 1800 GiB for 40 MB/s/TiB file systems.</p> </li> <li> <p>For <code>SCRATCH_1</code> deployment type, valid values are 1200 GiB, 2400 GiB, and increments of 3600 GiB.</p> </li> </ul> <p> <b>FSx for ONTAP file systems</b> - The amount of storage capacity that you can configure depends on the value of the <code>HAPairs</code> property. The minimum value is calculated as 1,024 * <code>HAPairs</code> and the maximum is calculated as 524,288 * <code>HAPairs</code>. </p> <p> <b>FSx for OpenZFS file systems</b> - The amount of storage capacity that you can configure is from 64 GiB up to 524,288 GiB (512 TiB).</p> <p> <b>FSx for Windows File Server file systems</b> - The amount of storage capacity that you can configure depends on the value that you set for <code>StorageType</code> as follows:</p> <ul> <li> <p>For SSD storage, valid values are 32 GiB-65,536 GiB (64 TiB).</p> </li> <li> <p>For HDD storage, valid values are 2000 GiB-65,536 GiB (64 TiB).</p> </li> </ul>"}, "StorageType": {"shape": "StorageType", "documentation": "<p>Sets the storage class for the file system that you're creating. Valid values are <code>SSD</code>, <code>HDD</code>, and <code>INTELLIGENT_TIERING</code>.</p> <ul> <li> <p>Set to <code>SSD</code> to use solid state drive storage. SSD is supported on all Windows, Lustre, ONTAP, and OpenZFS deployment types.</p> </li> <li> <p>Set to <code>HDD</code> to use hard disk drive storage, which is supported on <code>SINGLE_AZ_2</code> and <code>MULTI_AZ_1</code> Windows file system deployment types, and on <code>PERSISTENT_1</code> Lustre file system deployment types.</p> </li> <li> <p>Set to <code>INTELLIGENT_TIERING</code> to use fully elastic, intelligently-tiered storage. Intelligent-Tiering is only available for OpenZFS file systems with the Multi-AZ deployment type and for Lustre file systems with the Persistent_2 deployment type.</p> </li> </ul> <p>Default value is <code>SSD</code>. For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/WindowsGuide/optimize-fsx-costs.html#storage-type-options\"> Storage type options</a> in the <i>FSx for Windows File Server User Guide</i>, <a href=\"https://docs.aws.amazon.com/fsx/latest/LustreGuide/using-fsx-lustre.html#lustre-storage-classes\">FSx for Lustre storage classes</a> in the <i>FSx for Lustre User Guide</i>, and <a href=\"https://docs.aws.amazon.com/fsx/latest/OpenZFSGuide/performance-intelligent-tiering\">Working with Intelligent-Tiering</a> in the <i>Amazon FSx for OpenZFS User Guide</i>.</p>"}, "SubnetIds": {"shape": "SubnetIds", "documentation": "<p>Specifies the IDs of the subnets that the file system will be accessible from. For Windows and ONTAP <code>MULTI_AZ_1</code> deployment types,provide exactly two subnet IDs, one for the preferred file server and one for the standby file server. You specify one of these subnets as the preferred subnet using the <code>WindowsConfiguration &gt; PreferredSubnetID</code> or <code>OntapConfiguration &gt; PreferredSubnetID</code> properties. For more information about Multi-AZ file system configuration, see <a href=\"https://docs.aws.amazon.com/fsx/latest/WindowsGuide/high-availability-multiAZ.html\"> Availability and durability: Single-AZ and Multi-AZ file systems</a> in the <i>Amazon FSx for Windows User Guide</i> and <a href=\"https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/high-availability-multiAZ.html\"> Availability and durability</a> in the <i>Amazon FSx for ONTAP User Guide</i>.</p> <p>For Windows <code>SINGLE_AZ_1</code> and <code>SINGLE_AZ_2</code> and all Lustre deployment types, provide exactly one subnet ID. The file server is launched in that subnet's Availability Zone.</p>"}, "SecurityGroupIds": {"shape": "SecurityGroupIds", "documentation": "<p>A list of IDs specifying the security groups to apply to all network interfaces created for file system access. This list isn't returned in later requests to describe the file system.</p> <important> <p>You must specify a security group if you are creating a Multi-AZ FSx for ONTAP file system in a VPC subnet that has been shared with you.</p> </important>"}, "Tags": {"shape": "Tags", "documentation": "<p>The tags to apply to the file system that's being created. The key value of the <code>Name</code> tag appears in the console as the file system name.</p>"}, "KmsKeyId": {"shape": "KmsKeyId"}, "WindowsConfiguration": {"shape": "CreateFileSystemWindowsConfiguration", "documentation": "<p>The Microsoft Windows configuration for the file system that's being created.</p>"}, "LustreConfiguration": {"shape": "CreateFileSystemLustreConfiguration"}, "OntapConfiguration": {"shape": "CreateFileSystemOntapConfiguration"}, "FileSystemTypeVersion": {"shape": "FileSystemTypeVersion", "documentation": "<p>For FSx for Lustre file systems, sets the Lustre version for the file system that you're creating. Valid values are <code>2.10</code>, <code>2.12</code>, and <code>2.15</code>:</p> <ul> <li> <p> <code>2.10</code> is supported by the Scratch and Persistent_1 Lustre deployment types.</p> </li> <li> <p> <code>2.12</code> is supported by all Lustre deployment types, except for <code>PERSISTENT_2</code> with a metadata configuration mode.</p> </li> <li> <p> <code>2.15</code> is supported by all Lustre deployment types and is recommended for all new file systems.</p> </li> </ul> <p>Default value is <code>2.10</code>, except for the following deployments:</p> <ul> <li> <p>Default value is <code>2.12</code> when <code>DeploymentType</code> is set to <code>PERSISTENT_2</code> without a metadata configuration mode.</p> </li> <li> <p>Default value is <code>2.15</code> when <code>DeploymentType</code> is set to <code>PERSISTENT_2</code> with a metadata configuration mode.</p> </li> </ul>"}, "OpenZFSConfiguration": {"shape": "CreateFileSystemOpenZFSConfiguration", "documentation": "<p>The OpenZFS configuration for the file system that's being created.</p>"}}, "documentation": "<p>The request object used to create a new Amazon FSx file system.</p>"}, "CreateFileSystemResponse": {"type": "structure", "members": {"FileSystem": {"shape": "FileSystem", "documentation": "<p>The configuration of the file system that was created.</p>"}}, "documentation": "<p>The response object returned after the file system is created.</p>"}, "CreateFileSystemWindowsConfiguration": {"type": "structure", "required": ["ThroughputCapacity"], "members": {"ActiveDirectoryId": {"shape": "DirectoryId", "documentation": "<p>The ID for an existing Amazon Web Services Managed Microsoft Active Directory (AD) instance that the file system should join when it's created.</p>"}, "SelfManagedActiveDirectoryConfiguration": {"shape": "SelfManagedActiveDirectoryConfiguration"}, "DeploymentType": {"shape": "WindowsDeploymentType", "documentation": "<p>Specifies the file system deployment type, valid values are the following:</p> <ul> <li> <p> <code>MULTI_AZ_1</code> - Deploys a high availability file system that is configured for Multi-AZ redundancy to tolerate temporary Availability Zone (AZ) unavailability. You can only deploy a Multi-AZ file system in Amazon Web Services Regions that have a minimum of three Availability Zones. Also supports HDD storage type</p> </li> <li> <p> <code>SINGLE_AZ_1</code> - (Default) Choose to deploy a file system that is configured for single AZ redundancy.</p> </li> <li> <p> <code>SINGLE_AZ_2</code> - The latest generation Single AZ file system. Specifies a file system that is configured for single AZ redundancy and supports HDD storage type.</p> </li> </ul> <p>For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/WindowsGuide/high-availability-multiAZ.html\"> Availability and Durability: Single-AZ and Multi-AZ File Systems</a>.</p>"}, "PreferredSubnetId": {"shape": "SubnetId", "documentation": "<p>Required when <code>DeploymentType</code> is set to <code>MULTI_AZ_1</code>. This specifies the subnet in which you want the preferred file server to be located. For in-Amazon Web Services applications, we recommend that you launch your clients in the same Availability Zone (AZ) as your preferred file server to reduce cross-AZ data transfer costs and minimize latency. </p>"}, "ThroughputCapacity": {"shape": "MegabytesPerSecond", "documentation": "<p>Sets the throughput capacity of an Amazon FSx file system, measured in megabytes per second (MB/s), in 2 to the <i>n</i>th increments, between 2^3 (8) and 2^11 (2048).</p>"}, "WeeklyMaintenanceStartTime": {"shape": "WeeklyTime", "documentation": "<p>The preferred start time to perform weekly maintenance, formatted d:HH:MM in the UTC time zone, where d is the weekday number, from 1 through 7, beginning with Monday and ending with Sunday.</p>"}, "DailyAutomaticBackupStartTime": {"shape": "DailyTime", "documentation": "<p>The preferred time to take daily automatic backups, formatted HH:MM in the UTC time zone.</p>"}, "AutomaticBackupRetentionDays": {"shape": "AutomaticBackupRetentionDays", "documentation": "<p>The number of days to retain automatic backups. Setting this property to <code>0</code> disables automatic backups. You can retain automatic backups for a maximum of 90 days. The default is <code>30</code>.</p>"}, "CopyTagsToBackups": {"shape": "Flag", "documentation": "<p>A boolean flag indicating whether tags for the file system should be copied to backups. This value defaults to false. If it's set to true, all tags for the file system are copied to all automatic and user-initiated backups where the user doesn't specify tags. If this value is true, and you specify one or more tags, only the specified tags are copied to backups. If you specify one or more tags when creating a user-initiated backup, no tags are copied from the file system, regardless of this value.</p>"}, "Aliases": {"shape": "AlternateDNSNames", "documentation": "<p>An array of one or more DNS alias names that you want to associate with the Amazon FSx file system. Aliases allow you to use existing DNS names to access the data in your Amazon FSx file system. You can associate up to 50 aliases with a file system at any time. You can associate additional DNS aliases after you create the file system using the AssociateFileSystemAliases operation. You can remove DNS aliases from the file system after it is created using the DisassociateFileSystemAliases operation. You only need to specify the alias name in the request payload.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/WindowsGuide/managing-dns-aliases.html\">Working with DNS Aliases</a> and <a href=\"https://docs.aws.amazon.com/fsx/latest/WindowsGuide/walkthrough05-file-system-custom-CNAME.html\">Walkthrough 5: Using DNS aliases to access your file system</a>, including additional steps you must take to be able to access your file system using a DNS alias.</p> <p>An alias name has to meet the following requirements:</p> <ul> <li> <p>Formatted as a fully-qualified domain name (FQDN), <code>hostname.domain</code>, for example, <code>accounting.example.com</code>.</p> </li> <li> <p>Can contain alphanumeric characters, the underscore (_), and the hyphen (-).</p> </li> <li> <p>Cannot start or end with a hyphen.</p> </li> <li> <p>Can start with a numeric.</p> </li> </ul> <p>For DNS alias names, Amazon FSx stores alphabetic characters as lowercase letters (a-z), regardless of how you specify them: as uppercase letters, lowercase letters, or the corresponding letters in escape codes.</p>"}, "AuditLogConfiguration": {"shape": "WindowsAuditLogCreateConfiguration", "documentation": "<p>The configuration that Amazon FSx for Windows File Server uses to audit and log user accesses of files, folders, and file shares on the Amazon FSx for Windows File Server file system.</p>"}, "DiskIopsConfiguration": {"shape": "DiskIopsConfiguration", "documentation": "<p>The SSD IOPS (input/output operations per second) configuration for an Amazon FSx for Windows file system. By default, Amazon FSx automatically provisions 3 IOPS per GiB of storage capacity. You can provision additional IOPS per GiB of storage, up to the maximum limit associated with your chosen throughput capacity.</p>"}}, "documentation": "<p>The configuration object for the Microsoft Windows file system used in <code>CreateFileSystem</code> and <code>CreateFileSystemFromBackup</code> operations.</p>"}, "CreateOntapVolumeConfiguration": {"type": "structure", "required": ["StorageVirtualMachineId"], "members": {"JunctionPath": {"shape": "JunctionPath", "documentation": "<p>Specifies the location in the SVM's namespace where the volume is mounted. This parameter is required. The <code>JunctionPath</code> must have a leading forward slash, such as <code>/vol3</code>.</p>"}, "SecurityStyle": {"shape": "SecurityStyle", "documentation": "<p>Specifies the security style for the volume. If a volume's security style is not specified, it is automatically set to the root volume's security style. The security style determines the type of permissions that FSx for ONTAP uses to control data access. Specify one of the following values:</p> <ul> <li> <p> <code>UNIX</code> if the file system is managed by a UNIX administrator, the majority of users are NFS clients, and an application accessing the data uses a UNIX user as the service account. </p> </li> <li> <p> <code>NTFS</code> if the file system is managed by a Windows administrator, the majority of users are SMB clients, and an application accessing the data uses a Windows user as the service account.</p> </li> <li> <p> <code>MIXED</code> This is an advanced setting. For more information, see the topic <a href=\"https://docs.netapp.com/us-en/ontap/nfs-admin/security-styles-their-effects-concept.html\">What the security styles and their effects are</a> in the NetApp Documentation Center.</p> </li> </ul> <p>For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/managing-volumes.html#volume-security-style\">Volume security style</a> in the FSx for ONTAP User Guide.</p>"}, "SizeInMegabytes": {"shape": "VolumeCapacity", "documentation": "<p>Use <code>SizeInBytes</code> instead. Specifies the size of the volume, in megabytes (MB), that you are creating.</p>", "deprecated": true, "deprecatedMessage": "This property is deprecated, use SizeInBytes instead"}, "StorageEfficiencyEnabled": {"shape": "Flag", "documentation": "<p>Set to true to enable deduplication, compression, and compaction storage efficiency features on the volume, or set to false to disable them.</p> <p> <code>StorageEfficiencyEnabled</code> is required when creating a <code>RW</code> volume (<code>OntapVolumeType</code> set to <code>RW</code>).</p>"}, "StorageVirtualMachineId": {"shape": "StorageVirtualMachineId", "documentation": "<p>Specifies the ONTAP SVM in which to create the volume.</p>"}, "TieringPolicy": {"shape": "TieringPolicy"}, "OntapVolumeType": {"shape": "InputOntapVolumeType", "documentation": "<p>Specifies the type of volume you are creating. Valid values are the following:</p> <ul> <li> <p> <code>RW</code> specifies a read/write volume. <code>RW</code> is the default.</p> </li> <li> <p> <code>DP</code> specifies a data-protection volume. A <code>DP</code> volume is read-only and can be used as the destination of a NetApp SnapMirror relationship.</p> </li> </ul> <p>For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/managing-volumes.html#volume-types\">Volume types</a> in the Amazon FSx for NetApp ONTAP User Guide.</p>"}, "SnapshotPolicy": {"shape": "SnapshotPolicy", "documentation": "<p>Specifies the snapshot policy for the volume. There are three built-in snapshot policies:</p> <ul> <li> <p> <code>default</code>: This is the default policy. A maximum of six hourly snapshots taken five minutes past the hour. A maximum of two daily snapshots taken Monday through Saturday at 10 minutes after midnight. A maximum of two weekly snapshots taken every Sunday at 15 minutes after midnight.</p> </li> <li> <p> <code>default-1weekly</code>: This policy is the same as the <code>default</code> policy except that it only retains one snapshot from the weekly schedule.</p> </li> <li> <p> <code>none</code>: This policy does not take any snapshots. This policy can be assigned to volumes to prevent automatic snapshots from being taken.</p> </li> </ul> <p>You can also provide the name of a custom policy that you created with the ONTAP CLI or REST API.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/snapshots-ontap.html#snapshot-policies\">Snapshot policies</a> in the Amazon FSx for NetApp ONTAP User Guide.</p>"}, "CopyTagsToBackups": {"shape": "Flag", "documentation": "<p>A boolean flag indicating whether tags for the volume should be copied to backups. This value defaults to false. If it's set to true, all tags for the volume are copied to all automatic and user-initiated backups where the user doesn't specify tags. If this value is true, and you specify one or more tags, only the specified tags are copied to backups. If you specify one or more tags when creating a user-initiated backup, no tags are copied from the volume, regardless of this value.</p>"}, "SnaplockConfiguration": {"shape": "CreateSnaplockConfiguration", "documentation": "<p>Specifies the SnapLock configuration for an FSx for ONTAP volume. </p>"}, "VolumeStyle": {"shape": "VolumeStyle", "documentation": "<p>Use to specify the style of an ONTAP volume. FSx for ONTAP offers two styles of volumes that you can use for different purposes, FlexVol and FlexGroup volumes. For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/managing-volumes.html#volume-styles\">Volume styles</a> in the Amazon FSx for NetApp ONTAP User Guide.</p>"}, "AggregateConfiguration": {"shape": "CreateAggregateConfiguration", "documentation": "<p>Use to specify configuration options for a volume’s storage aggregate or aggregates.</p>"}, "SizeInBytes": {"shape": "VolumeCapacityBytes", "documentation": "<p>Specifies the configured size of the volume, in bytes.</p>"}}, "documentation": "<p>Specifies the configuration of the ONTAP volume that you are creating.</p>"}, "CreateOpenZFSOriginSnapshotConfiguration": {"type": "structure", "required": ["SnapshotARN", "CopyStrategy"], "members": {"SnapshotARN": {"shape": "ResourceARN"}, "CopyStrategy": {"shape": "OpenZFSCopyStrategy", "documentation": "<p>Specifies the strategy used when copying data from the snapshot to the new volume. </p> <ul> <li> <p> <code>CLONE</code> - The new volume references the data in the origin snapshot. Cloning a snapshot is faster than copying data from the snapshot to a new volume and doesn't consume disk throughput. However, the origin snapshot can't be deleted if there is a volume using its copied data.</p> </li> <li> <p> <code>FULL_COPY</code> - Copies all data from the snapshot to the new volume.</p> <p>Specify this option to create the volume from a snapshot on another FSx for OpenZFS file system.</p> </li> </ul> <note> <p>The <code>INCREMENTAL_COPY</code> option is only for updating an existing volume by using a snapshot from another FSx for OpenZFS file system. For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/APIReference/API_CopySnapshotAndUpdateVolume.html\">CopySnapshotAndUpdateVolume</a>.</p> </note>"}}, "documentation": "<p>The snapshot configuration to use when creating an Amazon FSx for OpenZFS volume from a snapshot. </p>"}, "CreateOpenZFSVolumeConfiguration": {"type": "structure", "required": ["ParentVolumeId"], "members": {"ParentVolumeId": {"shape": "VolumeId", "documentation": "<p>The ID of the volume to use as the parent volume of the volume that you are creating.</p>"}, "StorageCapacityReservationGiB": {"shape": "IntegerNoMaxFromNegativeOne", "documentation": "<p>Specifies the amount of storage in gibibytes (GiB) to reserve from the parent volume. Setting <code>StorageCapacityReservationGiB</code> guarantees that the specified amount of storage space on the parent volume will always be available for the volume. You can't reserve more storage than the parent volume has. To <i>not</i> specify a storage capacity reservation, set this to <code>0</code> or <code>-1</code>. For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/OpenZFSGuide/managing-volumes.html#volume-properties\">Volume properties</a> in the <i>Amazon FSx for OpenZFS User Guide</i>.</p>"}, "StorageCapacityQuotaGiB": {"shape": "IntegerNoMaxFromNegativeOne", "documentation": "<p>Sets the maximum storage size in gibibytes (GiB) for the volume. You can specify a quota that is larger than the storage on the parent volume. A volume quota limits the amount of storage that the volume can consume to the configured amount, but does not guarantee the space will be available on the parent volume. To guarantee quota space, you must also set <code>StorageCapacityReservationGiB</code>. To <i>not</i> specify a storage capacity quota, set this to <code>-1</code>. </p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/OpenZFSGuide/managing-volumes.html#volume-properties\">Volume properties</a> in the <i>Amazon FSx for OpenZFS User Guide</i>.</p>"}, "RecordSizeKiB": {"shape": "IntegerRecordSizeKiB", "documentation": "<p>Specifies the suggested block size for a volume in a ZFS dataset, in kibibytes (KiB). For file systems using the Intelligent-Tiering storage class, valid values are 128, 256, 512, 1024, 2048, or 4096 KiB, with a default of 1024 KiB. For all other file systems, valid values are 4, 8, 16, 32, 64, 128, 256, 512, or 1024 KiB, with a default of 128 KiB. We recommend using the default setting for the majority of use cases. Generally, workloads that write in fixed small or large record sizes may benefit from setting a custom record size, like database workloads (small record size) or media streaming workloads (large record size). For additional guidance on when to set a custom record size, see <a href=\"https://docs.aws.amazon.com/fsx/latest/OpenZFSGuide/performance.html#record-size-performance\"> ZFS Record size</a> in the <i>Amazon FSx for OpenZFS User Guide</i>.</p>"}, "DataCompressionType": {"shape": "OpenZFSDataCompressionType", "documentation": "<p>Specifies the method used to compress the data on the volume. The compression type is <code>NONE</code> by default.</p> <ul> <li> <p> <code>NONE</code> - Doesn't compress the data on the volume. <code>NONE</code> is the default.</p> </li> <li> <p> <code>ZSTD</code> - Compresses the data in the volume using the Zstandard (ZSTD) compression algorithm. ZSTD compression provides a higher level of data compression and higher read throughput performance than LZ4 compression.</p> </li> <li> <p> <code>LZ4</code> - Compresses the data in the volume using the LZ4 compression algorithm. LZ4 compression provides a lower level of compression and higher write throughput performance than ZSTD compression.</p> </li> </ul> <p>For more information about volume compression types and the performance of your Amazon FSx for OpenZFS file system, see <a href=\"https://docs.aws.amazon.com/fsx/latest/OpenZFSGuide/performance.html#performance-tips-zfs\"> Tips for maximizing performance</a> File system and volume settings in the <i>Amazon FSx for OpenZFS User Guide</i>.</p>"}, "CopyTagsToSnapshots": {"shape": "Flag", "documentation": "<p>A Boolean value indicating whether tags for the volume should be copied to snapshots. This value defaults to <code>false</code>. If this value is set to <code>true</code>, and you do not specify any tags, all tags for the original volume are copied over to snapshots. If this value is set to <code>true</code>, and you do specify one or more tags, only the specified tags for the original volume are copied over to snapshots. If you specify one or more tags when creating a new snapshot, no tags are copied over from the original volume, regardless of this value. </p>"}, "OriginSnapshot": {"shape": "CreateOpenZFSOriginSnapshotConfiguration", "documentation": "<p>The configuration object that specifies the snapshot to use as the origin of the data for the volume.</p>"}, "ReadOnly": {"shape": "Read<PERSON>nly", "documentation": "<p>A Boolean value indicating whether the volume is read-only.</p>"}, "NfsExports": {"shape": "OpenZFSNfsExports", "documentation": "<p>The configuration object for mounting a Network File System (NFS) file system.</p>"}, "UserAndGroupQuotas": {"shape": "OpenZFSUserAndGroupQuotas", "documentation": "<p>Configures how much storage users and groups can use on the volume.</p>"}}, "documentation": "<p>Specifies the configuration of the Amazon FSx for OpenZFS volume that you are creating.</p>"}, "CreateSnaplockConfiguration": {"type": "structure", "required": ["SnaplockType"], "members": {"AuditLogVolume": {"shape": "Flag", "documentation": "<p>Enables or disables the audit log volume for an FSx for ONTAP SnapLock volume. The default value is <code>false</code>. If you set <code>AuditLogVolume</code> to <code>true</code>, the SnapLock volume is created as an audit log volume. The minimum retention period for an audit log volume is six months. </p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/how-snaplock-works.html#snaplock-audit-log-volume\"> SnapLock audit log volumes</a>. </p>"}, "AutocommitPeriod": {"shape": "AutocommitPeriod", "documentation": "<p>The configuration object for setting the autocommit period of files in an FSx for ONTAP SnapLock volume. </p>"}, "PrivilegedDelete": {"shape": "PrivilegedDelete", "documentation": "<p>Enables, disables, or permanently disables privileged delete on an FSx for ONTAP SnapLock Enterprise volume. Enabling privileged delete allows SnapLock administrators to delete WORM files even if they have active retention periods. <code>PERMANENTLY_DISABLED</code> is a terminal state. If privileged delete is permanently disabled on a SnapLock volume, you can't re-enable it. The default value is <code>DISABLED</code>. </p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/snaplock-enterprise.html#privileged-delete\">Privileged delete</a>. </p>"}, "RetentionPeriod": {"shape": "SnaplockRetentionPeriod", "documentation": "<p>Specifies the retention period of an FSx for ONTAP SnapLock volume. </p>"}, "SnaplockType": {"shape": "SnaplockType", "documentation": "<p>Specifies the retention mode of an FSx for ONTAP SnapLock volume. After it is set, it can't be changed. You can choose one of the following retention modes: </p> <ul> <li> <p> <code>COMPLIANCE</code>: Files transitioned to write once, read many (WORM) on a Compliance volume can't be deleted until their retention periods expire. This retention mode is used to address government or industry-specific mandates or to protect against ransomware attacks. For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/snaplock-compliance.html\">SnapLock Compliance</a>. </p> </li> <li> <p> <code>ENTERPRISE</code>: Files transitioned to WORM on an Enterprise volume can be deleted by authorized users before their retention periods expire using privileged delete. This retention mode is used to advance an organization's data integrity and internal compliance or to test retention settings before using SnapLock Compliance. For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/snaplock-enterprise.html\">SnapLock Enterprise</a>. </p> </li> </ul>"}, "VolumeAppendModeEnabled": {"shape": "Flag", "documentation": "<p>Enables or disables volume-append mode on an FSx for ONTAP SnapLock volume. Volume-append mode allows you to create WORM-appendable files and write data to them incrementally. The default value is <code>false</code>. </p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/worm-state.html#worm-state-append\">Volume-append mode</a>. </p>"}}, "documentation": "<p>Defines the SnapLock configuration when creating an FSx for ONTAP SnapLock volume. </p>"}, "CreateSnapshotRequest": {"type": "structure", "required": ["Name", "VolumeId"], "members": {"ClientRequestToken": {"shape": "ClientRequestToken", "idempotencyToken": true}, "Name": {"shape": "SnapshotName", "documentation": "<p>The name of the snapshot. </p>"}, "VolumeId": {"shape": "VolumeId", "documentation": "<p>The ID of the volume that you are taking a snapshot of.</p>"}, "Tags": {"shape": "Tags"}}}, "CreateSnapshotResponse": {"type": "structure", "members": {"Snapshot": {"shape": "Snapshot", "documentation": "<p>A description of the snapshot.</p>"}}}, "CreateStorageVirtualMachineRequest": {"type": "structure", "required": ["FileSystemId", "Name"], "members": {"ActiveDirectoryConfiguration": {"shape": "CreateSvmActiveDirectoryConfiguration", "documentation": "<p>Describes the self-managed Microsoft Active Directory to which you want to join the SVM. Joining an Active Directory provides user authentication and access control for SMB clients, including Microsoft Windows and macOS clients accessing the file system.</p>"}, "ClientRequestToken": {"shape": "ClientRequestToken", "idempotencyToken": true}, "FileSystemId": {"shape": "FileSystemId"}, "Name": {"shape": "StorageVirtualMachineName", "documentation": "<p>The name of the SVM.</p>"}, "SvmAdminPassword": {"shape": "AdminPassword", "documentation": "<p>The password to use when managing the SVM using the NetApp ONTAP CLI or REST API. If you do not specify a password, you can still use the file system's <code>fsxadmin</code> user to manage the SVM.</p>"}, "Tags": {"shape": "Tags"}, "RootVolumeSecurityStyle": {"shape": "StorageVirtualMachineRootVolumeSecurityStyle", "documentation": "<p>The security style of the root volume of the SVM. Specify one of the following values:</p> <ul> <li> <p> <code>UNIX</code> if the file system is managed by a UNIX administrator, the majority of users are NFS clients, and an application accessing the data uses a UNIX user as the service account.</p> </li> <li> <p> <code>NTFS</code> if the file system is managed by a Microsoft Windows administrator, the majority of users are SMB clients, and an application accessing the data uses a Microsoft Windows user as the service account.</p> </li> <li> <p> <code>MIXED</code> This is an advanced setting. For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/volume-security-style.html\">Volume security style</a> in the Amazon FSx for NetApp ONTAP User Guide.</p> </li> </ul> <p/>"}}}, "CreateStorageVirtualMachineResponse": {"type": "structure", "members": {"StorageVirtualMachine": {"shape": "StorageVirtualMachine", "documentation": "<p>Returned after a successful <code>CreateStorageVirtualMachine</code> operation; describes the SVM just created.</p>"}}}, "CreateSvmActiveDirectoryConfiguration": {"type": "structure", "required": ["NetBiosName"], "members": {"NetBiosName": {"shape": "NetBiosAlias", "documentation": "<p>The NetBIOS name of the Active Directory computer object that will be created for your SVM.</p>"}, "SelfManagedActiveDirectoryConfiguration": {"shape": "SelfManagedActiveDirectoryConfiguration"}}, "documentation": "<p>The configuration that Amazon FSx uses to join the ONTAP storage virtual machine (SVM) to your self-managed (including on-premises) Microsoft Active Directory directory.</p>"}, "CreateVolumeFromBackupRequest": {"type": "structure", "required": ["BackupId", "Name"], "members": {"BackupId": {"shape": "BackupId"}, "ClientRequestToken": {"shape": "ClientRequestToken", "idempotencyToken": true}, "Name": {"shape": "VolumeName", "documentation": "<p>The name of the new volume you're creating.</p>"}, "OntapConfiguration": {"shape": "CreateOntapVolumeConfiguration", "documentation": "<p>Specifies the configuration of the ONTAP volume that you are creating.</p>"}, "Tags": {"shape": "Tags"}}}, "CreateVolumeFromBackupResponse": {"type": "structure", "members": {"Volume": {"shape": "Volume", "documentation": "<p>Returned after a successful <code>CreateVolumeFromBackup</code> API operation, describing the volume just created.</p>"}}}, "CreateVolumeRequest": {"type": "structure", "required": ["VolumeType", "Name"], "members": {"ClientRequestToken": {"shape": "ClientRequestToken", "idempotencyToken": true}, "VolumeType": {"shape": "VolumeType", "documentation": "<p>Specifies the type of volume to create; <code>ONTAP</code> and <code>OPENZFS</code> are the only valid volume types.</p>"}, "Name": {"shape": "VolumeName", "documentation": "<p>Specifies the name of the volume that you're creating.</p>"}, "OntapConfiguration": {"shape": "CreateOntapVolumeConfiguration", "documentation": "<p>Specifies the configuration to use when creating the ONTAP volume.</p>"}, "Tags": {"shape": "Tags"}, "OpenZFSConfiguration": {"shape": "CreateOpenZFSVolumeConfiguration", "documentation": "<p>Specifies the configuration to use when creating the OpenZFS volume.</p>"}}}, "CreateVolumeResponse": {"type": "structure", "members": {"Volume": {"shape": "Volume", "documentation": "<p>Returned after a successful <code>CreateVolume</code> API operation, describing the volume just created.</p>"}}}, "CreationTime": {"type": "timestamp", "documentation": "<p>The time that the resource was created, in seconds (since 1970-01-01T00:00:00Z), also known as Unix time.</p>"}, "DNSName": {"type": "string", "documentation": "<p>The file system's DNS name. You can mount your file system using its DNS name.</p>", "max": 275, "min": 16, "pattern": "^((fs|fc)i?-[0-9a-f]{8,}\\..{4,253})$"}, "DailyTime": {"type": "string", "documentation": "<p>A recurring daily time, in the format <code>HH:MM</code>. <code>HH</code> is the zero-padded hour of the day (0-23), and <code>MM</code> is the zero-padded minute of the hour. For example, <code>05:00</code> specifies 5 AM daily. </p>", "max": 5, "min": 5, "pattern": "^([01]\\d|2[0-3]):?([0-5]\\d)$"}, "DataCompressionType": {"type": "string", "enum": ["NONE", "LZ4"]}, "DataRepositoryAssociation": {"type": "structure", "members": {"AssociationId": {"shape": "DataRepositoryAssociationId", "documentation": "<p>The system-generated, unique ID of the data repository association.</p>"}, "ResourceARN": {"shape": "ResourceARN"}, "FileSystemId": {"shape": "FileSystemId"}, "Lifecycle": {"shape": "DataRepositoryLifecycle", "documentation": "<p>Describes the state of a data repository association. The lifecycle can have the following values:</p> <ul> <li> <p> <code>CREATING</code> - The data repository association between the file system or cache and the data repository is being created. The data repository is unavailable.</p> </li> <li> <p> <code>AVAILABLE</code> - The data repository association is available for use.</p> </li> <li> <p> <code>MISCONFIGURED</code> - The data repository association is misconfigured. Until the configuration is corrected, automatic import and automatic export will not work (only for Amazon FSx for Lustre).</p> </li> <li> <p> <code>UPDATING</code> - The data repository association is undergoing a customer initiated update that might affect its availability.</p> </li> <li> <p> <code>DELETING</code> - The data repository association is undergoing a customer initiated deletion.</p> </li> <li> <p> <code>FAILED</code> - The data repository association is in a terminal state that cannot be recovered.</p> </li> </ul>"}, "FailureDetails": {"shape": "DataRepositoryFailureDetails"}, "FileSystemPath": {"shape": "Namespace", "documentation": "<p>A path on the Amazon FSx for Lustre file system that points to a high-level directory (such as <code>/ns1/</code>) or subdirectory (such as <code>/ns1/subdir/</code>) that will be mapped 1-1 with <code>DataRepositoryPath</code>. The leading forward slash in the name is required. Two data repository associations cannot have overlapping file system paths. For example, if a data repository is associated with file system path <code>/ns1/</code>, then you cannot link another data repository with file system path <code>/ns1/ns2</code>.</p> <p>This path specifies where in your file system files will be exported from or imported to. This file system directory can be linked to only one Amazon S3 bucket, and no other S3 bucket can be linked to the directory.</p> <note> <p>If you specify only a forward slash (<code>/</code>) as the file system path, you can link only one data repository to the file system. You can only specify \"/\" as the file system path for the first data repository associated with a file system.</p> </note>"}, "DataRepositoryPath": {"shape": "ArchivePath", "documentation": "<p>The path to the data repository that will be linked to the cache or file system.</p> <ul> <li> <p>For Amazon File Cache, the path can be an NFS data repository that will be linked to the cache. The path can be in one of two formats:</p> <ul> <li> <p>If you are not using the <code>DataRepositorySubdirectories</code> parameter, the path is to an NFS Export directory (or one of its subdirectories) in the format <code>nsf://nfs-domain-name/exportpath</code>. You can therefore link a single NFS Export to a single data repository association.</p> </li> <li> <p>If you are using the <code>DataRepositorySubdirectories</code> parameter, the path is the domain name of the NFS file system in the format <code>nfs://filer-domain-name</code>, which indicates the root of the subdirectories specified with the <code>DataRepositorySubdirectories</code> parameter.</p> </li> </ul> </li> <li> <p>For Amazon File Cache, the path can be an S3 bucket or prefix in the format <code>s3://bucket-name/prefix/</code> (where <code>prefix</code> is optional).</p> </li> <li> <p>For Amazon FSx for Lustre, the path can be an S3 bucket or prefix in the format <code>s3://bucket-name/prefix/</code> (where <code>prefix</code> is optional).</p> </li> </ul>"}, "BatchImportMetaDataOnCreate": {"shape": "BatchImportMetaDataOnCreate", "documentation": "<p>A boolean flag indicating whether an import data repository task to import metadata should run after the data repository association is created. The task runs if this flag is set to <code>true</code>.</p> <note> <p> <code>BatchImportMetaDataOnCreate</code> is not supported for data repositories linked to an Amazon File Cache resource.</p> </note>"}, "ImportedFileChunkSize": {"shape": "Megabytes", "documentation": "<p>For files imported from a data repository, this value determines the stripe count and maximum amount of data per file (in MiB) stored on a single physical disk. The maximum number of disks that a single file can be striped across is limited by the total number of disks that make up the file system or cache.</p> <p>The default chunk size is 1,024 MiB (1 GiB) and can go as high as 512,000 MiB (500 GiB). Amazon S3 objects have a maximum size of 5 TB.</p>"}, "S3": {"shape": "S3DataRepositoryConfiguration", "documentation": "<p>The configuration for an Amazon S3 data repository linked to an Amazon FSx for Lustre file system with a data repository association.</p>"}, "Tags": {"shape": "Tags"}, "CreationTime": {"shape": "CreationTime"}, "FileCacheId": {"shape": "FileCacheId", "documentation": "<p>The globally unique ID of the Amazon File Cache resource.</p>"}, "FileCachePath": {"shape": "Namespace", "documentation": "<p>A path on the Amazon File Cache that points to a high-level directory (such as <code>/ns1/</code>) or subdirectory (such as <code>/ns1/subdir/</code>) that will be mapped 1-1 with <code>DataRepositoryPath</code>. The leading forward slash in the path is required. Two data repository associations cannot have overlapping cache paths. For example, if a data repository is associated with cache path <code>/ns1/</code>, then you cannot link another data repository with cache path <code>/ns1/ns2</code>.</p> <p>This path specifies the directory in your cache where files will be exported from. This cache directory can be linked to only one data repository (S3 or NFS) and no other data repository can be linked to the directory.</p> <note> <p>The cache path can only be set to root (/) on an NFS DRA when <code>DataRepositorySubdirectories</code> is specified. If you specify root (/) as the cache path, you can create only one DRA on the cache.</p> <p>The cache path cannot be set to root (/) for an S3 DRA.</p> </note>"}, "DataRepositorySubdirectories": {"shape": "SubDirectoriesPaths", "documentation": "<p>For Amazon File Cache, a list of NFS Exports that will be linked with an NFS data repository association. All the subdirectories must be on a single NFS file system. The Export paths are in the format <code>/exportpath1</code>. To use this parameter, you must configure <code>DataRepositoryPath</code> as the domain name of the NFS file system. The NFS file system domain name in effect is the root of the subdirectories. Note that <code>DataRepositorySubdirectories</code> is not supported for S3 data repositories.</p>"}, "NFS": {"shape": "NFSDataRepositoryConfiguration", "documentation": "<p>The configuration for an NFS data repository linked to an Amazon File Cache resource with a data repository association.</p>"}}, "documentation": "<p>The configuration of a data repository association that links an Amazon FSx for Lustre file system to an Amazon S3 bucket or an Amazon File Cache resource to an Amazon S3 bucket or an NFS file system. The data repository association configuration object is returned in the response of the following operations:</p> <ul> <li> <p> <code>CreateDataRepositoryAssociation</code> </p> </li> <li> <p> <code>UpdateDataRepositoryAssociation</code> </p> </li> <li> <p> <code>DescribeDataRepositoryAssociations</code> </p> </li> </ul> <p>Data repository associations are supported on Amazon File Cache resources and all FSx for Lustre 2.12 and 2.15 file systems, excluding Intelligent-Tiering and <code>scratch_1</code> file systems.</p>"}, "DataRepositoryAssociationId": {"type": "string", "max": 23, "min": 13, "pattern": "^(dra-[0-9a-f]{8,})$"}, "DataRepositoryAssociationIds": {"type": "list", "member": {"shape": "DataRepositoryAssociationId"}, "max": 50}, "DataRepositoryAssociationNotFound": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>No data repository associations were found based upon the supplied parameters.</p>", "exception": true}, "DataRepositoryAssociations": {"type": "list", "member": {"shape": "DataRepositoryAssociation"}, "max": 100}, "DataRepositoryConfiguration": {"type": "structure", "members": {"Lifecycle": {"shape": "DataRepositoryLifecycle", "documentation": "<p>Describes the state of the file system's S3 durable data repository, if it is configured with an S3 repository. The lifecycle can have the following values:</p> <ul> <li> <p> <code>CREATING</code> - The data repository configuration between the FSx file system and the linked S3 data repository is being created. The data repository is unavailable.</p> </li> <li> <p> <code>AVAILABLE</code> - The data repository is available for use.</p> </li> <li> <p> <code>MISCONFIGURED</code> - Amazon FSx cannot automatically import updates from the S3 bucket until the data repository configuration is corrected. For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/LustreGuide/troubleshooting.html#troubleshooting-misconfigured-data-repository\">Troubleshooting a Misconfigured linked S3 bucket</a>. </p> </li> <li> <p> <code>UPDATING</code> - The data repository is undergoing a customer initiated update and availability may be impacted.</p> </li> <li> <p> <code>FAILED</code> - The data repository is in a terminal state that cannot be recovered.</p> </li> </ul>"}, "ImportPath": {"shape": "ArchivePath", "documentation": "<p>The import path to the Amazon S3 bucket (and optional prefix) that you're using as the data repository for your FSx for Lustre file system, for example <code>s3://import-bucket/optional-prefix</code>. If a prefix is specified after the Amazon S3 bucket name, only object keys with that prefix are loaded into the file system.</p>"}, "ExportPath": {"shape": "ArchivePath", "documentation": "<p>The export path to the Amazon S3 bucket (and prefix) that you are using to store new and changed Lustre file system files in S3.</p>"}, "ImportedFileChunkSize": {"shape": "Megabytes", "documentation": "<p>For files imported from a data repository, this value determines the stripe count and maximum amount of data per file (in MiB) stored on a single physical disk. The maximum number of disks that a single file can be striped across is limited by the total number of disks that make up the file system.</p> <p>The default chunk size is 1,024 MiB (1 GiB) and can go as high as 512,000 MiB (500 GiB). Amazon S3 objects have a maximum size of 5 TB.</p>"}, "AutoImportPolicy": {"shape": "AutoImportPolicyType", "documentation": "<p>Describes the file system's linked S3 data repository's <code>AutoImportPolicy</code>. The AutoImportPolicy configures how Amazon FSx keeps your file and directory listings up to date as you add or modify objects in your linked S3 bucket. <code>AutoImportPolicy</code> can have the following values:</p> <ul> <li> <p> <code>NONE</code> - (Default) AutoImport is off. Amazon FSx only updates file and directory listings from the linked S3 bucket when the file system is created. FSx does not update file and directory listings for any new or changed objects after choosing this option.</p> </li> <li> <p> <code>NEW</code> - AutoImport is on. Amazon FSx automatically imports directory listings of any new objects added to the linked S3 bucket that do not currently exist in the FSx file system. </p> </li> <li> <p> <code>NEW_CHANGED</code> - AutoImport is on. Amazon FSx automatically imports file and directory listings of any new objects added to the S3 bucket and any existing objects that are changed in the S3 bucket after you choose this option.</p> </li> <li> <p> <code>NEW_CHANGED_DELETED</code> - AutoImport is on. Amazon FSx automatically imports file and directory listings of any new objects added to the S3 bucket, any existing objects that are changed in the S3 bucket, and any objects that were deleted in the S3 bucket.</p> </li> </ul>"}, "FailureDetails": {"shape": "DataRepositoryFailureDetails"}}, "documentation": "<p>The data repository configuration object for Lustre file systems returned in the response of the <code>CreateFileSystem</code> operation.</p> <p>This data type is not supported on file systems with a data repository association. For file systems with a data repository association, see .</p>"}, "DataRepositoryFailureDetails": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>Provides detailed information about the data repository if its <code>Lifecycle</code> is set to <code>MISCONFIGURED</code> or <code>FAILED</code>.</p>"}, "DataRepositoryLifecycle": {"type": "string", "enum": ["CREATING", "AVAILABLE", "MISCONFIGURED", "UPDATING", "DELETING", "FAILED"]}, "DataRepositoryTask": {"type": "structure", "required": ["TaskId", "Lifecycle", "Type", "CreationTime"], "members": {"TaskId": {"shape": "TaskId", "documentation": "<p>The system-generated, unique 17-digit ID of the data repository task.</p>"}, "Lifecycle": {"shape": "DataRepositoryTaskLifecycle", "documentation": "<p>The lifecycle status of the data repository task, as follows:</p> <ul> <li> <p> <code>PENDING</code> - The task has not started.</p> </li> <li> <p> <code>EXECUTING</code> - The task is in process.</p> </li> <li> <p> <code>FAILED</code> - The task was not able to be completed. For example, there may be files the task failed to process. The <a>DataRepositoryTaskFailureDetails</a> property provides more information about task failures.</p> </li> <li> <p> <code>SUCCEEDED</code> - The task has completed successfully.</p> </li> <li> <p> <code>CANCELED</code> - The task was canceled and it did not complete.</p> </li> <li> <p> <code>CANCELING</code> - The task is in process of being canceled.</p> </li> </ul> <note> <p>You cannot delete an FSx for Lustre file system if there are data repository tasks for the file system in the <code>PENDING</code> or <code>EXECUTING</code> states. Please retry when the data repository task is finished (with a status of <code>CANCELED</code>, <code>SUCCEEDED</code>, or <code>FAILED</code>). You can use the DescribeDataRepositoryTask action to monitor the task status. Contact the FSx team if you need to delete your file system immediately.</p> </note>"}, "Type": {"shape": "DataRepositoryTaskType", "documentation": "<p>The type of data repository task.</p> <ul> <li> <p> <code>EXPORT_TO_REPOSITORY</code> tasks export from your Amazon FSx for Lustre file system to a linked data repository.</p> </li> <li> <p> <code>IMPORT_METADATA_FROM_REPOSITORY</code> tasks import metadata changes from a linked S3 bucket to your Amazon FSx for Lustre file system.</p> </li> <li> <p> <code>RELEASE_DATA_FROM_FILESYSTEM</code> tasks release files in your Amazon FSx for Lustre file system that have been exported to a linked S3 bucket and that meet your specified release criteria.</p> </li> <li> <p> <code>AUTO_RELEASE_DATA</code> tasks automatically release files from an Amazon File Cache resource.</p> </li> </ul>"}, "CreationTime": {"shape": "CreationTime"}, "StartTime": {"shape": "StartTime", "documentation": "<p>The time the system began processing the task.</p>"}, "EndTime": {"shape": "EndTime", "documentation": "<p>The time the system completed processing the task, populated after the task is complete.</p>"}, "ResourceARN": {"shape": "ResourceARN"}, "Tags": {"shape": "Tags"}, "FileSystemId": {"shape": "FileSystemId", "documentation": "<p>The globally unique ID of the file system.</p>"}, "Paths": {"shape": "DataRepositoryTaskPaths", "documentation": "<p>An array of paths that specify the data for the data repository task to process. For example, in an EXPORT_TO_REPOSITORY task, the paths specify which data to export to the linked data repository.</p> <p>(Default) If <code>Paths</code> is not specified, Amazon FSx uses the file system root directory.</p>"}, "FailureDetails": {"shape": "DataRepositoryTaskFailureDetails", "documentation": "<p>Failure message describing why the task failed, it is populated only when <code>Lifecycle</code> is set to <code>FAILED</code>.</p>"}, "Status": {"shape": "DataRepositoryTaskStatus", "documentation": "<p>Provides the status of the number of files that the task has processed successfully and failed to process.</p>"}, "Report": {"shape": "CompletionReport"}, "CapacityToRelease": {"shape": "CapacityToRelease", "documentation": "<p>Specifies the amount of data to release, in GiB, by an Amazon File Cache AUTO_RELEASE_DATA task that automatically releases files from the cache.</p>"}, "FileCacheId": {"shape": "FileCacheId", "documentation": "<p>The system-generated, unique ID of the cache.</p>"}, "ReleaseConfiguration": {"shape": "ReleaseConfiguration", "documentation": "<p>The configuration that specifies the last accessed time criteria for files that will be released from an Amazon FSx for Lustre file system.</p>"}}, "documentation": "<p>A description of the data repository task.</p> <ul> <li> <p>You use import and export data repository tasks to perform bulk transfer operations between an Amazon FSx for Lustre file system and a linked data repository.</p> </li> <li> <p>You use release data repository tasks to release files that have been exported to a linked S3 bucket from your Amazon FSx for Lustre file system.</p> </li> <li> <p>An Amazon File Cache resource uses a task to automatically release files from the cache.</p> </li> </ul> <p>To learn more about data repository tasks, see <a href=\"https://docs.aws.amazon.com/fsx/latest/LustreGuide/data-repository-tasks.html\">Data Repository Tasks</a>. </p>"}, "DataRepositoryTaskEnded": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The data repository task could not be canceled because the task has already ended.</p>", "exception": true}, "DataRepositoryTaskExecuting": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>An existing data repository task is currently executing on the file system. Wait until the existing task has completed, then create the new task.</p>", "exception": true}, "DataRepositoryTaskFailureDetails": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>Provides information about why a data repository task failed. Only populated when the task <code>Lifecycle</code> is set to <code>FAILED</code>.</p>"}, "DataRepositoryTaskFilter": {"type": "structure", "members": {"Name": {"shape": "DataRepositoryTaskFilterName", "documentation": "<p>Name of the task property to use in filtering the tasks returned in the response.</p> <ul> <li> <p>Use <code>file-system-id</code> to retrieve data repository tasks for specific file systems.</p> </li> <li> <p>Use <code>task-lifecycle</code> to retrieve data repository tasks with one or more specific lifecycle states, as follows: CANCELED, EXECUTING, FAILED, PENDING, and SUCCEEDED.</p> </li> </ul>"}, "Values": {"shape": "DataRepositoryTaskFilterValues", "documentation": "<p>Use Values to include the specific file system IDs and task lifecycle states for the filters you are using.</p>"}}, "documentation": "<p>(Optional) An array of filter objects you can use to filter the response of data repository tasks you will see in the response. You can filter the tasks returned in the response by one or more file system IDs, task lifecycles, and by task type. A filter object consists of a filter <code>Name</code>, and one or more <code>Values</code> for the filter.</p>"}, "DataRepositoryTaskFilterName": {"type": "string", "enum": ["file-system-id", "task-lifecycle", "data-repository-association-id", "file-cache-id"]}, "DataRepositoryTaskFilterValue": {"type": "string", "max": 128, "min": 1, "pattern": "^[0-9a-zA-Z\\*\\.\\\\/\\?\\-\\_]*$"}, "DataRepositoryTaskFilterValues": {"type": "list", "member": {"shape": "DataRepositoryTaskFilterValue"}, "max": 20}, "DataRepositoryTaskFilters": {"type": "list", "member": {"shape": "DataRepositoryTaskFilter"}, "max": 3}, "DataRepositoryTaskLifecycle": {"type": "string", "enum": ["PENDING", "EXECUTING", "FAILED", "SUCCEEDED", "CANCELED", "CANCELING"]}, "DataRepositoryTaskNotFound": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The data repository task or tasks you specified could not be found.</p>", "exception": true}, "DataRepositoryTaskPath": {"type": "string", "max": 4096, "min": 0, "pattern": "^[^\\u0000\\u0085\\u2028\\u2029\\r\\n]{0,4096}$"}, "DataRepositoryTaskPaths": {"type": "list", "member": {"shape": "DataRepositoryTaskPath"}, "max": 100}, "DataRepositoryTaskStatus": {"type": "structure", "members": {"TotalCount": {"shape": "TotalCount", "documentation": "<p>The total number of files that the task will process. While a task is executing, the sum of <code>SucceededCount</code> plus <code>FailedCount</code> may not equal <code>TotalCount</code>. When the task is complete, <code>TotalCount</code> equals the sum of <code>SucceededCount</code> plus <code>FailedCount</code>.</p>"}, "SucceededCount": {"shape": "SucceededCount", "documentation": "<p>A running total of the number of files that the task has successfully processed.</p>"}, "FailedCount": {"shape": "FailedCount", "documentation": "<p>A running total of the number of files that the task failed to process.</p>"}, "LastUpdatedTime": {"shape": "LastUpdatedTime", "documentation": "<p>The time at which the task status was last updated.</p>"}, "ReleasedCapacity": {"shape": "ReleasedCapacity", "documentation": "<p>The total amount of data, in GiB, released by an Amazon File Cache AUTO_RELEASE_DATA task that automatically releases files from the cache.</p>"}}, "documentation": "<p>Provides the task status showing a running total of the total number of files to be processed, the number successfully processed, and the number of files the task failed to process.</p>"}, "DataRepositoryTaskType": {"type": "string", "enum": ["EXPORT_TO_REPOSITORY", "IMPORT_METADATA_FROM_REPOSITORY", "RELEASE_DATA_FROM_FILESYSTEM", "AUTO_RELEASE_DATA"]}, "DataRepositoryTasks": {"type": "list", "member": {"shape": "DataRepositoryTask"}, "max": 50}, "DeleteBackupRequest": {"type": "structure", "required": ["BackupId"], "members": {"BackupId": {"shape": "BackupId", "documentation": "<p>The ID of the backup that you want to delete.</p>"}, "ClientRequestToken": {"shape": "ClientRequestToken", "documentation": "<p>A string of up to 63 ASCII characters that Amazon FSx uses to ensure idempotent deletion. This parameter is automatically filled on your behalf when using the CLI or SDK.</p>", "idempotencyToken": true}}, "documentation": "<p>The request object for the <code>DeleteBackup</code> operation.</p>"}, "DeleteBackupResponse": {"type": "structure", "members": {"BackupId": {"shape": "BackupId", "documentation": "<p>The ID of the backup that was deleted.</p>"}, "Lifecycle": {"shape": "BackupLifecycle", "documentation": "<p>The lifecycle status of the backup. If the <code>DeleteBackup</code> operation is successful, the status is <code>DELETED</code>.</p>"}}, "documentation": "<p>The response object for the <code>DeleteBackup</code> operation.</p>"}, "DeleteDataInFileSystem": {"type": "boolean"}, "DeleteDataRepositoryAssociationRequest": {"type": "structure", "required": ["AssociationId"], "members": {"AssociationId": {"shape": "DataRepositoryAssociationId", "documentation": "<p>The ID of the data repository association that you want to delete.</p>"}, "ClientRequestToken": {"shape": "ClientRequestToken", "idempotencyToken": true}, "DeleteDataInFileSystem": {"shape": "DeleteDataInFileSystem", "documentation": "<p>Set to <code>true</code> to delete the data in the file system that corresponds to the data repository association.</p>"}}}, "DeleteDataRepositoryAssociationResponse": {"type": "structure", "members": {"AssociationId": {"shape": "DataRepositoryAssociationId", "documentation": "<p>The ID of the data repository association being deleted.</p>"}, "Lifecycle": {"shape": "DataRepositoryLifecycle", "documentation": "<p>Describes the lifecycle state of the data repository association being deleted.</p>"}, "DeleteDataInFileSystem": {"shape": "DeleteDataInFileSystem", "documentation": "<p>Indicates whether data in the file system that corresponds to the data repository association is being deleted. Default is <code>false</code>.</p>"}}}, "DeleteFileCacheRequest": {"type": "structure", "required": ["FileCacheId"], "members": {"FileCacheId": {"shape": "FileCacheId", "documentation": "<p>The ID of the cache that's being deleted.</p>"}, "ClientRequestToken": {"shape": "ClientRequestToken", "idempotencyToken": true}}}, "DeleteFileCacheResponse": {"type": "structure", "members": {"FileCacheId": {"shape": "FileCacheId", "documentation": "<p>The ID of the cache that's being deleted.</p>"}, "Lifecycle": {"shape": "FileCacheLifecycle", "documentation": "<p>The cache lifecycle for the deletion request. If the <code>DeleteFileCache</code> operation is successful, this status is <code>DELETING</code>.</p>"}}}, "DeleteFileSystemLustreConfiguration": {"type": "structure", "members": {"SkipFinalBackup": {"shape": "Flag", "documentation": "<p>Set <code>SkipFinalBackup</code> to false if you want to take a final backup of the file system you are deleting. By default, Amazon FSx will not take a final backup on your behalf when the <code>DeleteFileSystem</code> operation is invoked. (Default = true)</p> <note> <p>The <code>fsx:CreateBackup</code> permission is required if you set <code>SkipFinalBackup</code> to <code>false</code> in order to delete the file system and take a final backup.</p> </note>"}, "FinalBackupTags": {"shape": "Tags", "documentation": "<p>Use if <code>SkipFinalBackup</code> is set to <code>false</code>, and you want to apply an array of tags to the final backup. If you have set the file system property <code>CopyTagsToBackups</code> to true, and you specify one or more <code>FinalBackupTags</code> when deleting a file system, Amazon FSx will not copy any existing file system tags to the backup.</p>"}}, "documentation": "<p>The configuration object for the Amazon FSx for Lustre file system being deleted in the <code>DeleteFileSystem</code> operation.</p>"}, "DeleteFileSystemLustreResponse": {"type": "structure", "members": {"FinalBackupId": {"shape": "BackupId", "documentation": "<p>The ID of the final backup for this file system.</p>"}, "FinalBackupTags": {"shape": "Tags", "documentation": "<p>The set of tags applied to the final backup.</p>"}}, "documentation": "<p>The response object for the Amazon FSx for Lustre file system being deleted in the <code>DeleteFileSystem</code> operation.</p>"}, "DeleteFileSystemOpenZFSConfiguration": {"type": "structure", "members": {"SkipFinalBackup": {"shape": "Flag", "documentation": "<p>By default, Amazon FSx for OpenZFS takes a final backup on your behalf when the <code>DeleteFileSystem</code> operation is invoked. Doing this helps protect you from data loss, and we highly recommend taking the final backup. If you want to skip taking a final backup, set this value to <code>true</code>.</p>"}, "FinalBackupTags": {"shape": "Tags", "documentation": "<p>A list of tags to apply to the file system's final backup.</p>"}, "Options": {"shape": "DeleteFileSystemOpenZFSOptions", "documentation": "<p>To delete a file system if there are child volumes present below the root volume, use the string <code>DELETE_CHILD_VOLUMES_AND_SNAPSHOTS</code>. If your file system has child volumes and you don't use this option, the delete request will fail.</p>"}}, "documentation": "<p>The configuration object for the Amazon FSx for OpenZFS file system used in the <code>DeleteFileSystem</code> operation.</p>"}, "DeleteFileSystemOpenZFSOption": {"type": "string", "enum": ["DELETE_CHILD_VOLUMES_AND_SNAPSHOTS"]}, "DeleteFileSystemOpenZFSOptions": {"type": "list", "member": {"shape": "DeleteFileSystemOpenZFSOption"}, "max": 1}, "DeleteFileSystemOpenZFSResponse": {"type": "structure", "members": {"FinalBackupId": {"shape": "BackupId"}, "FinalBackupTags": {"shape": "Tags"}}, "documentation": "<p>The response object for the Amazon FSx for OpenZFS file system that's being deleted in the <code>DeleteFileSystem</code> operation.</p>"}, "DeleteFileSystemRequest": {"type": "structure", "required": ["FileSystemId"], "members": {"FileSystemId": {"shape": "FileSystemId", "documentation": "<p>The ID of the file system that you want to delete.</p>"}, "ClientRequestToken": {"shape": "ClientRequestToken", "documentation": "<p>A string of up to 63 ASCII characters that Amazon FSx uses to ensure idempotent deletion. This token is automatically filled on your behalf when using the Command Line Interface (CLI) or an Amazon Web Services SDK.</p>", "idempotencyToken": true}, "WindowsConfiguration": {"shape": "DeleteFileSystemWindowsConfiguration"}, "LustreConfiguration": {"shape": "DeleteFileSystemLustreConfiguration"}, "OpenZFSConfiguration": {"shape": "DeleteFileSystemOpenZFSConfiguration", "documentation": "<p>The configuration object for the OpenZFS file system used in the <code>DeleteFileSystem</code> operation.</p>"}}, "documentation": "<p>The request object for <code>DeleteFileSystem</code> operation.</p>"}, "DeleteFileSystemResponse": {"type": "structure", "members": {"FileSystemId": {"shape": "FileSystemId", "documentation": "<p>The ID of the file system that's being deleted.</p>"}, "Lifecycle": {"shape": "FileSystemLifecycle", "documentation": "<p>The file system lifecycle for the deletion request. If the <code>DeleteFileSystem</code> operation is successful, this status is <code>DELETING</code>.</p>"}, "WindowsResponse": {"shape": "DeleteFileSystemWindowsResponse"}, "LustreResponse": {"shape": "DeleteFileSystemLustreResponse"}, "OpenZFSResponse": {"shape": "DeleteFileSystemOpenZFSResponse", "documentation": "<p>The response object for the OpenZFS file system that's being deleted in the <code>DeleteFileSystem</code> operation.</p>"}}, "documentation": "<p>The response object for the <code>DeleteFileSystem</code> operation.</p>"}, "DeleteFileSystemWindowsConfiguration": {"type": "structure", "members": {"SkipFinalBackup": {"shape": "Flag", "documentation": "<p>By default, Amazon FSx for Windows takes a final backup on your behalf when the <code>DeleteFileSystem</code> operation is invoked. Doing this helps protect you from data loss, and we highly recommend taking the final backup. If you want to skip this backup, use this flag to do so.</p>"}, "FinalBackupTags": {"shape": "Tags", "documentation": "<p>A set of tags for your final backup.</p>"}}, "documentation": "<p>The configuration object for the Microsoft Windows file system used in the <code>DeleteFileSystem</code> operation.</p>"}, "DeleteFileSystemWindowsResponse": {"type": "structure", "members": {"FinalBackupId": {"shape": "BackupId", "documentation": "<p>The ID of the final backup for this file system.</p>"}, "FinalBackupTags": {"shape": "Tags", "documentation": "<p>The set of tags applied to the final backup.</p>"}}, "documentation": "<p>The response object for the Microsoft Windows file system used in the <code>DeleteFileSystem</code> operation.</p>"}, "DeleteOpenZFSVolumeOption": {"type": "string", "enum": ["DELETE_CHILD_VOLUMES_AND_SNAPSHOTS"]}, "DeleteOpenZFSVolumeOptions": {"type": "list", "member": {"shape": "DeleteOpenZFSVolumeOption"}, "max": 1}, "DeleteSnapshotRequest": {"type": "structure", "required": ["SnapshotId"], "members": {"ClientRequestToken": {"shape": "ClientRequestToken", "idempotencyToken": true}, "SnapshotId": {"shape": "SnapshotId", "documentation": "<p>The ID of the snapshot that you want to delete.</p>"}}}, "DeleteSnapshotResponse": {"type": "structure", "members": {"SnapshotId": {"shape": "SnapshotId", "documentation": "<p>The ID of the deleted snapshot.</p>"}, "Lifecycle": {"shape": "SnapshotLifecycle", "documentation": "<p>The lifecycle status of the snapshot. If the <code>DeleteSnapshot</code> operation is successful, this status is <code>DELETING</code>.</p>"}}}, "DeleteStorageVirtualMachineRequest": {"type": "structure", "required": ["StorageVirtualMachineId"], "members": {"ClientRequestToken": {"shape": "ClientRequestToken", "idempotencyToken": true}, "StorageVirtualMachineId": {"shape": "StorageVirtualMachineId", "documentation": "<p>The ID of the SVM that you want to delete.</p>"}}}, "DeleteStorageVirtualMachineResponse": {"type": "structure", "members": {"StorageVirtualMachineId": {"shape": "StorageVirtualMachineId", "documentation": "<p>The ID of the SVM Amazon FSx is deleting.</p>"}, "Lifecycle": {"shape": "StorageVirtualMachineLifecycle", "documentation": "<p>Describes the lifecycle state of the SVM being deleted.</p>"}}}, "DeleteVolumeOntapConfiguration": {"type": "structure", "members": {"SkipFinalBackup": {"shape": "Flag", "documentation": "<p>Set to true if you want to skip taking a final backup of the volume you are deleting.</p>"}, "FinalBackupTags": {"shape": "Tags"}, "BypassSnaplockEnterpriseRetention": {"shape": "Flag", "documentation": "<p>Setting this to <code>true</code> allows a SnapLock administrator to delete an FSx for ONTAP SnapLock Enterprise volume with unexpired write once, read many (WORM) files. The IAM permission <code>fsx:BypassSnaplockEnterpriseRetention</code> is also required to delete SnapLock Enterprise volumes with unexpired WORM files. The default value is <code>false</code>. </p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/snaplock-delete-volume.html\"> Deleting a SnapLock volume</a>. </p>"}}, "documentation": "<p>Use to specify skipping a final backup, adding tags to a final backup, or bypassing the retention period of an FSx for ONTAP SnapLock Enterprise volume when deleting an FSx for ONTAP volume. </p>"}, "DeleteVolumeOntapResponse": {"type": "structure", "members": {"FinalBackupId": {"shape": "BackupId"}, "FinalBackupTags": {"shape": "Tags"}}, "documentation": "<p>The response object for the Amazon FSx for NetApp ONTAP volume being deleted in the <code>DeleteVolume</code> operation.</p>"}, "DeleteVolumeOpenZFSConfiguration": {"type": "structure", "members": {"Options": {"shape": "DeleteOpenZFSVolumeOptions", "documentation": "<p>To delete the volume's child volumes, snapshots, and clones, use the string <code>DELETE_CHILD_VOLUMES_AND_SNAPSHOTS</code>.</p>"}}, "documentation": "<p>A value that specifies whether to delete all child volumes and snapshots. </p>"}, "DeleteVolumeRequest": {"type": "structure", "required": ["VolumeId"], "members": {"ClientRequestToken": {"shape": "ClientRequestToken", "idempotencyToken": true}, "VolumeId": {"shape": "VolumeId", "documentation": "<p>The ID of the volume that you are deleting.</p>"}, "OntapConfiguration": {"shape": "DeleteVolumeOntapConfiguration", "documentation": "<p>For Amazon FSx for ONTAP volumes, specify whether to take a final backup of the volume and apply tags to the backup. To apply tags to the backup, you must have the <code>fsx:TagResource</code> permission.</p>"}, "OpenZFSConfiguration": {"shape": "DeleteVolumeOpenZFSConfiguration", "documentation": "<p>For Amazon FSx for OpenZFS volumes, specify whether to delete all child volumes and snapshots.</p>"}}}, "DeleteVolumeResponse": {"type": "structure", "members": {"VolumeId": {"shape": "VolumeId", "documentation": "<p>The ID of the volume that's being deleted.</p>"}, "Lifecycle": {"shape": "VolumeLifecycle", "documentation": "<p>The lifecycle state of the volume being deleted. If the <code>DeleteVolume</code> operation is successful, this value is <code>DELETING</code>.</p>"}, "OntapResponse": {"shape": "DeleteVolumeOntapResponse", "documentation": "<p>Returned after a <code>DeleteVolume</code> request, showing the status of the delete request.</p>"}}}, "DescribeBackupsRequest": {"type": "structure", "members": {"BackupIds": {"shape": "BackupIds", "documentation": "<p>The IDs of the backups that you want to retrieve. This parameter value overrides any filters. If any IDs aren't found, a <code>BackupNotFound</code> error occurs.</p>"}, "Filters": {"shape": "Filters", "documentation": "<p>The filters structure. The supported names are <code>file-system-id</code>, <code>backup-type</code>, <code>file-system-type</code>, and <code>volume-id</code>.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>Maximum number of backups to return in the response. This parameter value must be greater than 0. The number of items that Amazon FSx returns is the minimum of the <code>MaxResults</code> parameter specified in the request and the service's internal maximum number of items per page.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>An opaque pagination token returned from a previous <code>DescribeBackups</code> operation. If a token is present, the operation continues the list from where the returning call left off.</p>"}}, "documentation": "<p>The request object for the <code>DescribeBackups</code> operation.</p>"}, "DescribeBackupsResponse": {"type": "structure", "members": {"Backups": {"shape": "Backups", "documentation": "<p>An array of backups.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>A <code>NextToken</code> value is present if there are more backups than returned in the response. You can use the <code>NextToken</code> value in the subsequent request to fetch the backups. </p>"}}, "documentation": "<p>Response object for the <code>DescribeBackups</code> operation.</p>"}, "DescribeDataRepositoryAssociationsRequest": {"type": "structure", "members": {"AssociationIds": {"shape": "DataRepositoryAssociationIds", "documentation": "<p>IDs of the data repository associations whose descriptions you want to retrieve (String).</p>"}, "Filters": {"shape": "Filters"}, "MaxResults": {"shape": "LimitedMaxResults", "documentation": "<p>The maximum number of resources to return in the response. This value must be an integer greater than zero.</p>"}, "NextToken": {"shape": "NextToken"}}}, "DescribeDataRepositoryAssociationsResponse": {"type": "structure", "members": {"Associations": {"shape": "DataRepositoryAssociations", "documentation": "<p>An array of one or more data repository association descriptions.</p>"}, "NextToken": {"shape": "NextToken"}}}, "DescribeDataRepositoryTasksRequest": {"type": "structure", "members": {"TaskIds": {"shape": "TaskIds", "documentation": "<p>(Optional) IDs of the tasks whose descriptions you want to retrieve (String).</p>"}, "Filters": {"shape": "DataRepositoryTaskFilters", "documentation": "<p>(Optional) You can use filters to narrow the <code>DescribeDataRepositoryTasks</code> response to include just tasks for specific file systems, or tasks in a specific lifecycle state.</p>"}, "MaxResults": {"shape": "MaxResults"}, "NextToken": {"shape": "NextToken"}}}, "DescribeDataRepositoryTasksResponse": {"type": "structure", "members": {"DataRepositoryTasks": {"shape": "DataRepositoryTasks", "documentation": "<p>The collection of data repository task descriptions returned.</p>"}, "NextToken": {"shape": "NextToken"}}}, "DescribeFileCachesRequest": {"type": "structure", "members": {"FileCacheIds": {"shape": "FileCacheIds", "documentation": "<p>IDs of the caches whose descriptions you want to retrieve (String).</p>"}, "MaxResults": {"shape": "MaxResults"}, "NextToken": {"shape": "NextToken"}}}, "DescribeFileCachesResponse": {"type": "structure", "members": {"FileCaches": {"shape": "FileCaches", "documentation": "<p>The response object for the <code>DescribeFileCaches</code> operation.</p>"}, "NextToken": {"shape": "NextToken"}}}, "DescribeFileSystemAliasesRequest": {"type": "structure", "required": ["FileSystemId"], "members": {"ClientRequestToken": {"shape": "ClientRequestToken", "idempotencyToken": true}, "FileSystemId": {"shape": "FileSystemId", "documentation": "<p>The ID of the file system to return the associated DNS aliases for (String).</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>Maximum number of DNS aliases to return in the response (integer). This parameter value must be greater than 0. The number of items that Amazon FSx returns is the minimum of the <code>MaxResults</code> parameter specified in the request and the service's internal maximum number of items per page.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>Opaque pagination token returned from a previous <code>DescribeFileSystemAliases</code> operation (String). If a token is included in the request, the action continues the list from where the previous returning call left off.</p>"}}, "documentation": "<p>The request object for <code>DescribeFileSystemAliases</code> operation.</p>"}, "DescribeFileSystemAliasesResponse": {"type": "structure", "members": {"Aliases": {"shape": "Aliases", "documentation": "<p>An array of one or more DNS aliases currently associated with the specified file system.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>Present if there are more DNS aliases than returned in the response (String). You can use the <code>NextToken</code> value in a later request to fetch additional descriptions. </p>"}}, "documentation": "<p>The response object for <code>DescribeFileSystemAliases</code> operation.</p>"}, "DescribeFileSystemsRequest": {"type": "structure", "members": {"FileSystemIds": {"shape": "FileSystemIds", "documentation": "<p>IDs of the file systems whose descriptions you want to retrieve (String).</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>Maximum number of file systems to return in the response (integer). This parameter value must be greater than 0. The number of items that Amazon FSx returns is the minimum of the <code>MaxResults</code> parameter specified in the request and the service's internal maximum number of items per page.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>Opaque pagination token returned from a previous <code>DescribeFileSystems</code> operation (String). If a token present, the operation continues the list from where the returning call left off.</p>"}}, "documentation": "<p>The request object for <code>DescribeFileSystems</code> operation.</p>"}, "DescribeFileSystemsResponse": {"type": "structure", "members": {"FileSystems": {"shape": "FileSystems", "documentation": "<p>An array of file system descriptions.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>Present if there are more file systems than returned in the response (String). You can use the <code>NextToken</code> value in the later request to fetch the descriptions. </p>"}}, "documentation": "<p>The response object for <code>DescribeFileSystems</code> operation.</p>"}, "DescribeS3AccessPointAttachmentsRequest": {"type": "structure", "members": {"Names": {"shape": "S3AccessPointAttachmentNames", "documentation": "<p>The names of the S3 access point attachments whose descriptions you want to retrieve.</p>"}, "Filters": {"shape": "S3AccessPointAttachmentsFilters", "documentation": "<p>Enter a filter Name and Values pair to view a select set of S3 access point attachments.</p>"}, "MaxResults": {"shape": "MaxResults"}, "NextToken": {"shape": "NextToken"}}}, "DescribeS3AccessPointAttachmentsResponse": {"type": "structure", "members": {"S3AccessPointAttachments": {"shape": "S3AccessPointAttachments", "documentation": "<p>Array of S3 access point attachments returned after a successful <code>DescribeS3AccessPointAttachments</code> operation.</p>"}, "NextToken": {"shape": "NextToken"}}}, "DescribeSharedVpcConfigurationRequest": {"type": "structure", "members": {}}, "DescribeSharedVpcConfigurationResponse": {"type": "structure", "members": {"EnableFsxRouteTableUpdatesFromParticipantAccounts": {"shape": "VerboseFlag", "documentation": "<p>Indicates whether participant accounts can create FSx for ONTAP Multi-AZ file systems in shared subnets.</p>"}}}, "DescribeSnapshotsRequest": {"type": "structure", "members": {"SnapshotIds": {"shape": "SnapshotIds", "documentation": "<p>The IDs of the snapshots that you want to retrieve. This parameter value overrides any filters. If any IDs aren't found, a <code>SnapshotNotFound</code> error occurs.</p>"}, "Filters": {"shape": "SnapshotFilters", "documentation": "<p>The filters structure. The supported names are <code>file-system-id</code> or <code>volume-id</code>.</p>"}, "MaxResults": {"shape": "MaxResults"}, "NextToken": {"shape": "NextToken"}, "IncludeShared": {"shape": "IncludeShared", "documentation": "<p>Set to <code>false</code> (default) if you want to only see the snapshots owned by your Amazon Web Services account. Set to <code>true</code> if you want to see the snapshots in your account and the ones shared with you from another account.</p>"}}}, "DescribeSnapshotsResponse": {"type": "structure", "members": {"Snapshots": {"shape": "Snapshots", "documentation": "<p>An array of snapshots.</p>"}, "NextToken": {"shape": "NextToken"}}}, "DescribeStorageVirtualMachinesRequest": {"type": "structure", "members": {"StorageVirtualMachineIds": {"shape": "StorageVirtualMachineIds", "documentation": "<p>Enter the ID of one or more SVMs that you want to view.</p>"}, "Filters": {"shape": "StorageVirtualMachineFilters", "documentation": "<p>Enter a filter name:value pair to view a select set of SVMs.</p>"}, "MaxResults": {"shape": "MaxResults"}, "NextToken": {"shape": "NextToken"}}}, "DescribeStorageVirtualMachinesResponse": {"type": "structure", "members": {"StorageVirtualMachines": {"shape": "StorageVirtualMachines", "documentation": "<p>Returned after a successful <code>DescribeStorageVirtualMachines</code> operation, describing each SVM.</p>"}, "NextToken": {"shape": "NextToken"}}}, "DescribeVolumesRequest": {"type": "structure", "members": {"VolumeIds": {"shape": "VolumeIds", "documentation": "<p>The IDs of the volumes whose descriptions you want to retrieve.</p>"}, "Filters": {"shape": "VolumeFilters", "documentation": "<p>Enter a filter <code>Name</code> and <code>Values</code> pair to view a select set of volumes.</p>"}, "MaxResults": {"shape": "MaxResults"}, "NextToken": {"shape": "NextToken"}}}, "DescribeVolumesResponse": {"type": "structure", "members": {"Volumes": {"shape": "Volumes", "documentation": "<p>Returned after a successful <code>DescribeVolumes</code> operation, describing each volume.</p>"}, "NextToken": {"shape": "NextToken"}}}, "DetachAndDeleteS3AccessPointRequest": {"type": "structure", "required": ["Name"], "members": {"ClientRequestToken": {"shape": "ClientRequestToken", "idempotencyToken": true}, "Name": {"shape": "S3AccessPointAttachmentName", "documentation": "<p>The name of the S3 access point attachment that you want to delete.</p>"}}}, "DetachAndDeleteS3AccessPointResponse": {"type": "structure", "members": {"Lifecycle": {"shape": "S3AccessPointAttachmentLifecycle", "documentation": "<p>The lifecycle status of the S3 access point attachment.</p>"}, "Name": {"shape": "S3AccessPointAttachmentName", "documentation": "<p>The name of the S3 access point attachment being deleted.</p>"}}}, "DirectoryId": {"type": "string", "max": 12, "min": 12, "pattern": "^d-[0-9a-f]{10}$"}, "DirectoryPassword": {"type": "string", "max": 256, "min": 1, "pattern": "^.{1,256}$", "sensitive": true}, "DirectoryUserName": {"type": "string", "max": 256, "min": 1, "pattern": "^[^\\u0000\\u0085\\u2028\\u2029\\r\\n]{1,256}$"}, "DisassociateFileSystemAliasesRequest": {"type": "structure", "required": ["FileSystemId", "Aliases"], "members": {"ClientRequestToken": {"shape": "ClientRequestToken", "idempotencyToken": true}, "FileSystemId": {"shape": "FileSystemId", "documentation": "<p>Specifies the file system from which to disassociate the DNS aliases.</p>"}, "Aliases": {"shape": "AlternateDNSNames", "documentation": "<p>An array of one or more DNS alias names to disassociate, or remove, from the file system.</p>"}}, "documentation": "<p>The request object of DNS aliases to disassociate from an Amazon FSx for Windows File Server file system.</p>"}, "DisassociateFileSystemAliasesResponse": {"type": "structure", "members": {"Aliases": {"shape": "Aliases", "documentation": "<p>An array of one or more DNS aliases that Amazon FSx is attempting to disassociate from the file system.</p>"}}, "documentation": "<p>The system generated response showing the DNS aliases that Amazon FSx is attempting to disassociate from the file system. Use the API operation to monitor the status of the aliases Amazon FSx is removing from the file system.</p>"}, "DiskIopsConfiguration": {"type": "structure", "members": {"Mode": {"shape": "DiskIopsConfigurationMode", "documentation": "<p>Specifies whether the file system is using the <code>AUTOMATIC</code> setting of SSD IOPS of 3 IOPS per GB of storage capacity, or if it using a <code>USER_PROVISIONED</code> value.</p>"}, "Iops": {"shape": "Iops", "documentation": "<p>The total number of SSD IOPS provisioned for the file system.</p> <p>The minimum and maximum values for this property depend on the value of <code>HAPairs</code> and <code>StorageCapacity</code>. The minimum value is calculated as <code>StorageCapacity</code> * 3 * <code>HAPairs</code> (3 IOPS per GB of <code>StorageCapacity</code>). The maximum value is calculated as 200,000 * <code>HAPairs</code>.</p> <p>Amazon FSx responds with an HTTP status code 400 (Bad Request) if the value of <code>Iops</code> is outside of the minimum or maximum values.</p>"}}, "documentation": "<p>The SSD IOPS (input/output operations per second) configuration for an Amazon FSx for NetApp ONTAP, Amazon FSx for Windows File Server, or FSx for OpenZFS file system. By default, Amazon FSx automatically provisions 3 IOPS per GB of storage capacity. You can provision additional IOPS per GB of storage. The configuration consists of the total number of provisioned SSD IOPS and how it is was provisioned, or the mode (by the customer or by Amazon FSx).</p>"}, "DiskIopsConfigurationMode": {"type": "string", "enum": ["AUTOMATIC", "USER_PROVISIONED"]}, "DnsIps": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "max": 3, "min": 1}, "DriveCacheType": {"type": "string", "enum": ["NONE", "READ"]}, "DurationSinceLastAccess": {"type": "structure", "members": {"Unit": {"shape": "Unit", "documentation": "<p>The unit of time used by the <code>Value</code> parameter to determine if a file can be released, based on when it was last accessed. <code>DAYS</code> is the only supported value. This is a required parameter.</p>"}, "Value": {"shape": "Value", "documentation": "<p>An integer that represents the minimum amount of time (in days) since a file was last accessed in the file system. Only exported files with a <code>MAX(atime, ctime, mtime)</code> timestamp that is more than this amount of time in the past (relative to the task create time) will be released. The default of <code>Value</code> is <code>0</code>. This is a required parameter.</p> <note> <p>If an exported file meets the last accessed time criteria, its file or directory path must also be specified in the <code>Paths</code> parameter of the operation in order for the file to be released.</p> </note>"}}, "documentation": "<p>Defines the minimum amount of time since last access for a file to be eligible for release. Only files that have been exported to S3 and that were last accessed or modified before this point-in-time are eligible to be released from the Amazon FSx for Lustre file system.</p>"}, "EndTime": {"type": "timestamp"}, "ErrorCode": {"type": "string", "max": 128, "min": 1}, "ErrorMessage": {"type": "string", "documentation": "<p>A detailed error message.</p>", "max": 256, "min": 1}, "EventType": {"type": "string", "enum": ["NEW", "CHANGED", "DELETED"]}, "EventTypes": {"type": "list", "member": {"shape": "EventType"}, "max": 3}, "FailedCount": {"type": "long"}, "FileCache": {"type": "structure", "members": {"OwnerId": {"shape": "AWSAccountId"}, "CreationTime": {"shape": "CreationTime"}, "FileCacheId": {"shape": "FileCacheId", "documentation": "<p>The system-generated, unique ID of the cache.</p>"}, "FileCacheType": {"shape": "FileCacheType", "documentation": "<p>The type of cache, which must be <code>LUSTRE</code>.</p>"}, "FileCacheTypeVersion": {"shape": "FileSystemTypeVersion", "documentation": "<p>The Lustre version of the cache, which must be <code>2.12</code>.</p>"}, "Lifecycle": {"shape": "FileCacheLifecycle", "documentation": "<p>The lifecycle status of the cache. The following are the possible values and what they mean:</p> <ul> <li> <p> <code>AVAILABLE</code> - The cache is in a healthy state, and is reachable and available for use.</p> </li> <li> <p> <code>CREATING</code> - The new cache is being created.</p> </li> <li> <p> <code>DELETING</code> - An existing cache is being deleted.</p> </li> <li> <p> <code>UPDATING</code> - The cache is undergoing a customer-initiated update.</p> </li> <li> <p> <code>FAILED</code> - An existing cache has experienced an unrecoverable failure. When creating a new cache, the cache was unable to be created.</p> </li> </ul>"}, "FailureDetails": {"shape": "FileCacheFailureDetails", "documentation": "<p>A structure providing details of any failures that occurred.</p>"}, "StorageCapacity": {"shape": "StorageCapacity", "documentation": "<p>The storage capacity of the cache in gibibytes (GiB).</p>"}, "VpcId": {"shape": "VpcId"}, "SubnetIds": {"shape": "SubnetIds"}, "NetworkInterfaceIds": {"shape": "NetworkInterfaceIds"}, "DNSName": {"shape": "DNSName", "documentation": "<p>The Domain Name System (DNS) name for the cache.</p>"}, "KmsKeyId": {"shape": "KmsKeyId", "documentation": "<p>Specifies the ID of the Key Management Service (KMS) key to use for encrypting data on an Amazon File Cache. If a <code>KmsKeyId</code> isn't specified, the Amazon FSx-managed KMS key for your account is used. For more information, see <a href=\"https://docs.aws.amazon.com/kms/latest/APIReference/API_Encrypt.html\">Encrypt</a> in the <i>Key Management Service API Reference</i>.</p>"}, "ResourceARN": {"shape": "ResourceARN"}, "LustreConfiguration": {"shape": "FileCacheLustreConfiguration", "documentation": "<p>The configuration for the Amazon File Cache resource.</p>"}, "DataRepositoryAssociationIds": {"shape": "DataRepositoryAssociationIds", "documentation": "<p>A list of IDs of data repository associations that are associated with this cache.</p>"}}, "documentation": "<p>A description of a specific Amazon File Cache resource, which is a response object from the <code>DescribeFileCaches</code> operation.</p>"}, "FileCacheCreating": {"type": "structure", "members": {"OwnerId": {"shape": "AWSAccountId"}, "CreationTime": {"shape": "CreationTime"}, "FileCacheId": {"shape": "FileCacheId", "documentation": "<p>The system-generated, unique ID of the cache.</p>"}, "FileCacheType": {"shape": "FileCacheType", "documentation": "<p>The type of cache, which must be <code>LUSTRE</code>.</p>"}, "FileCacheTypeVersion": {"shape": "FileSystemTypeVersion", "documentation": "<p>The Lustre version of the cache, which must be <code>2.12</code>.</p>"}, "Lifecycle": {"shape": "FileCacheLifecycle", "documentation": "<p>The lifecycle status of the cache. The following are the possible values and what they mean:</p> <ul> <li> <p> <code>AVAILABLE</code> - The cache is in a healthy state, and is reachable and available for use.</p> </li> <li> <p> <code>CREATING</code> - The new cache is being created.</p> </li> <li> <p> <code>DELETING</code> - An existing cache is being deleted.</p> </li> <li> <p> <code>UPDATING</code> - The cache is undergoing a customer-initiated update.</p> </li> <li> <p> <code>FAILED</code> - An existing cache has experienced an unrecoverable failure. When creating a new cache, the cache was unable to be created.</p> </li> </ul>"}, "FailureDetails": {"shape": "FileCacheFailureDetails", "documentation": "<p>A structure providing details of any failures that occurred in creating a cache.</p>"}, "StorageCapacity": {"shape": "StorageCapacity", "documentation": "<p>The storage capacity of the cache in gibibytes (GiB).</p>"}, "VpcId": {"shape": "VpcId"}, "SubnetIds": {"shape": "SubnetIds"}, "NetworkInterfaceIds": {"shape": "NetworkInterfaceIds"}, "DNSName": {"shape": "DNSName", "documentation": "<p>The Domain Name System (DNS) name for the cache.</p>"}, "KmsKeyId": {"shape": "KmsKeyId", "documentation": "<p>Specifies the ID of the Key Management Service (KMS) key to use for encrypting data on an Amazon File Cache. If a <code>KmsKeyId</code> isn't specified, the Amazon FSx-managed KMS key for your account is used. For more information, see <a href=\"https://docs.aws.amazon.com/kms/latest/APIReference/API_Encrypt.html\">Encrypt</a> in the <i>Key Management Service API Reference</i>.</p>"}, "ResourceARN": {"shape": "ResourceARN"}, "Tags": {"shape": "Tags"}, "CopyTagsToDataRepositoryAssociations": {"shape": "CopyTagsToDataRepositoryAssociations", "documentation": "<p>A boolean flag indicating whether tags for the cache should be copied to data repository associations.</p>"}, "LustreConfiguration": {"shape": "FileCacheLustreConfiguration", "documentation": "<p>The configuration for the Amazon File Cache resource.</p>"}, "DataRepositoryAssociationIds": {"shape": "DataRepositoryAssociationIds", "documentation": "<p>A list of IDs of data repository associations that are associated with this cache.</p>"}}, "documentation": "<p>The response object for the Amazon File Cache resource being created in the <code>CreateFileCache</code> operation.</p>"}, "FileCacheDataRepositoryAssociation": {"type": "structure", "required": ["FileCachePath", "DataRepositoryPath"], "members": {"FileCachePath": {"shape": "Namespace", "documentation": "<p>A path on the cache that points to a high-level directory (such as <code>/ns1/</code>) or subdirectory (such as <code>/ns1/subdir/</code>) that will be mapped 1-1 with <code>DataRepositoryPath</code>. The leading forward slash in the name is required. Two data repository associations cannot have overlapping cache paths. For example, if a data repository is associated with cache path <code>/ns1/</code>, then you cannot link another data repository with cache path <code>/ns1/ns2</code>.</p> <p>This path specifies where in your cache files will be exported from. This cache directory can be linked to only one data repository, and no data repository other can be linked to the directory.</p> <note> <p>The cache path can only be set to root (/) on an NFS DRA when <code>DataRepositorySubdirectories</code> is specified. If you specify root (/) as the cache path, you can create only one DRA on the cache.</p> <p>The cache path cannot be set to root (/) for an S3 DRA.</p> </note>"}, "DataRepositoryPath": {"shape": "ArchivePath", "documentation": "<p>The path to the S3 or NFS data repository that links to the cache. You must provide one of the following paths:</p> <ul> <li> <p>The path can be an NFS data repository that links to the cache. The path can be in one of two formats:</p> <ul> <li> <p>If you are not using the <code>DataRepositorySubdirectories</code> parameter, the path is to an NFS Export directory (or one of its subdirectories) in the format <code>nfs://nfs-domain-name/exportpath</code>. You can therefore link a single NFS Export to a single data repository association.</p> </li> <li> <p>If you are using the <code>DataRepositorySubdirectories</code> parameter, the path is the domain name of the NFS file system in the format <code>nfs://filer-domain-name</code>, which indicates the root of the subdirectories specified with the <code>DataRepositorySubdirectories</code> parameter.</p> </li> </ul> </li> <li> <p>The path can be an S3 bucket or prefix in the format <code>s3://bucket-name/prefix/</code> (where <code>prefix</code> is optional).</p> </li> </ul>"}, "DataRepositorySubdirectories": {"shape": "SubDirectoriesPaths", "documentation": "<p>A list of NFS Exports that will be linked with this data repository association. The Export paths are in the format <code>/exportpath1</code>. To use this parameter, you must configure <code>DataRepositoryPath</code> as the domain name of the NFS file system. The NFS file system domain name in effect is the root of the subdirectories. Note that <code>DataRepositorySubdirectories</code> is not supported for S3 data repositories.</p>"}, "NFS": {"shape": "FileCacheNFSConfiguration", "documentation": "<p>The configuration for a data repository association that links an Amazon File Cache resource to an NFS data repository.</p>"}}, "documentation": "<p>The configuration for a data repository association (DRA) to be created during the Amazon File Cache resource creation. The DRA links the cache to either an Amazon S3 bucket or prefix, or a Network File System (NFS) data repository that supports the NFSv3 protocol.</p> <p>The DRA does not support automatic import or automatic export.</p>"}, "FileCacheFailureDetails": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage", "documentation": "<p>A message describing any failures that occurred.</p>"}}, "documentation": "<p>A structure providing details of any failures that occurred.</p>"}, "FileCacheId": {"type": "string", "max": 21, "min": 11, "pattern": "^(fc-[0-9a-f]{8,})$"}, "FileCacheIds": {"type": "list", "member": {"shape": "FileCacheId"}, "max": 50}, "FileCacheLifecycle": {"type": "string", "enum": ["AVAILABLE", "CREATING", "DELETING", "UPDATING", "FAILED"]}, "FileCacheLustreConfiguration": {"type": "structure", "members": {"PerUnitStorageThroughput": {"shape": "PerUnitStorageThroughput", "documentation": "<p>Per unit storage throughput represents the megabytes per second of read or write throughput per 1 tebibyte of storage provisioned. Cache throughput capacity is equal to Storage capacity (TiB) * PerUnitStorageThroughput (MB/s/TiB). The only supported value is <code>1000</code>.</p>"}, "DeploymentType": {"shape": "FileCacheLustreDeploymentType", "documentation": "<p>The deployment type of the Amazon File Cache resource, which must be <code>CACHE_1</code>.</p>"}, "MountName": {"shape": "LustreFileSystemMountName", "documentation": "<p>You use the <code>MountName</code> value when mounting the cache. If you pass a cache ID to the <code>DescribeFileCaches</code> operation, it returns the the <code>MountName</code> value as part of the cache's description.</p>"}, "WeeklyMaintenanceStartTime": {"shape": "WeeklyTime"}, "MetadataConfiguration": {"shape": "FileCacheLustreMetadataConfiguration", "documentation": "<p>The configuration for a Lustre MDT (Metadata Target) storage volume.</p>"}, "LogConfiguration": {"shape": "LustreLogConfiguration", "documentation": "<p>The configuration for Lustre logging used to write the enabled logging events for your Amazon File Cache resource to Amazon CloudWatch Logs.</p>"}}, "documentation": "<p>The configuration for the Amazon File Cache resource.</p>"}, "FileCacheLustreDeploymentType": {"type": "string", "enum": ["CACHE_1"]}, "FileCacheLustreMetadataConfiguration": {"type": "structure", "required": ["StorageCapacity"], "members": {"StorageCapacity": {"shape": "MetadataStorageCapacity", "documentation": "<p>The storage capacity of the Lustre MDT (Metadata Target) storage volume in gibibytes (GiB). The only supported value is <code>2400</code> GiB.</p>"}}, "documentation": "<p>The configuration for a Lustre MDT (Metadata Target) storage volume. The metadata on Amazon File Cache is managed by a Lustre Metadata Server (MDS) while the actual metadata is persisted on an MDT.</p>"}, "FileCacheNFSConfiguration": {"type": "structure", "required": ["Version"], "members": {"Version": {"shape": "NfsVersion", "documentation": "<p>The version of the NFS (Network File System) protocol of the NFS data repository. The only supported value is <code>NFS3</code>, which indicates that the data repository must support the NFSv3 protocol.</p>"}, "DnsIps": {"shape": "RepositoryDnsIps", "documentation": "<p>A list of up to 2 IP addresses of DNS servers used to resolve the NFS file system domain name. The provided IP addresses can either be the IP addresses of a DNS forwarder or resolver that the customer manages and runs inside the customer VPC, or the IP addresses of the on-premises DNS servers.</p>"}}, "documentation": "<p>The configuration for an NFS data repository association (DRA) created during the creation of the Amazon File Cache resource.</p>"}, "FileCacheNotFound": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>No caches were found based upon supplied parameters.</p>", "exception": true}, "FileCacheType": {"type": "string", "enum": ["LUSTRE"]}, "FileCaches": {"type": "list", "member": {"shape": "FileCache"}, "max": 50}, "FileSystem": {"type": "structure", "members": {"OwnerId": {"shape": "AWSAccountId", "documentation": "<p>The Amazon Web Services account that created the file system. If the file system was created by a user in IAM Identity Center, the Amazon Web Services account to which the IAM user belongs is the owner.</p>"}, "CreationTime": {"shape": "CreationTime", "documentation": "<p>The time that the file system was created, in seconds (since 1970-01-01T00:00:00Z), also known as Unix time.</p>"}, "FileSystemId": {"shape": "FileSystemId", "documentation": "<p>The system-generated, unique 17-digit ID of the file system.</p>"}, "FileSystemType": {"shape": "FileSystemType", "documentation": "<p>The type of Amazon FSx file system, which can be <code>LUSTRE</code>, <code>WINDOWS</code>, <code>ONTAP</code>, or <code>OPENZFS</code>.</p>"}, "Lifecycle": {"shape": "FileSystemLifecycle", "documentation": "<p>The lifecycle status of the file system. The following are the possible values and what they mean:</p> <ul> <li> <p> <code>AVAILABLE</code> - The file system is in a healthy state, and is reachable and available for use.</p> </li> <li> <p> <code>CREATING</code> - Amazon FSx is creating the new file system.</p> </li> <li> <p> <code>DELETING</code> - Amazon FSx is deleting an existing file system.</p> </li> <li> <p> <code>FAILED</code> - An existing file system has experienced an unrecoverable failure. When creating a new file system, Amazon FSx was unable to create the file system.</p> </li> <li> <p> <code>MISCONFIGURED</code> - The file system is in a failed but recoverable state.</p> </li> <li> <p> <code>MISCONFIGURED_UNAVAILABLE</code> - (Amazon FSx for Windows File Server only) The file system is currently unavailable due to a change in your Active Directory configuration.</p> </li> <li> <p> <code>UPDATING</code> - The file system is undergoing a customer-initiated update.</p> </li> </ul>"}, "FailureDetails": {"shape": "FileSystemFailureDetails"}, "StorageCapacity": {"shape": "StorageCapacity", "documentation": "<p>The storage capacity of the file system in gibibytes (GiB).</p> <p>Amazon FSx responds with an HTTP status code 400 (Bad Request) if the value of <code>StorageCapacity</code> is outside of the minimum or maximum values.</p>"}, "StorageType": {"shape": "StorageType", "documentation": "<p>The type of storage the file system is using.</p> <ul> <li> <p>If set to <code>SSD</code>, the file system uses solid state drive storage.</p> </li> <li> <p>If set to <code>HDD</code>, the file system uses hard disk drive storage.</p> </li> <li> <p>If set to <code>INTELLIGENT_TIERING</code>, the file system uses fully elastic, intelligently-tiered storage.</p> </li> </ul>"}, "VpcId": {"shape": "VpcId", "documentation": "<p>The ID of the primary virtual private cloud (VPC) for the file system.</p>"}, "SubnetIds": {"shape": "SubnetIds", "documentation": "<p>Specifies the IDs of the subnets that the file system is accessible from. For the Amazon FSx Windows and ONTAP <code>MULTI_AZ_1</code> file system deployment type, there are two subnet IDs, one for the preferred file server and one for the standby file server. The preferred file server subnet identified in the <code>PreferredSubnetID</code> property. All other file systems have only one subnet ID.</p> <p>For FSx for Lustre file systems, and Single-AZ Windows file systems, this is the ID of the subnet that contains the file system's endpoint. For <code>MULTI_AZ_1</code> Windows and ONTAP file systems, the file system endpoint is available in the <code>PreferredSubnetID</code>.</p>"}, "NetworkInterfaceIds": {"shape": "NetworkInterfaceIds", "documentation": "<p>The IDs of the elastic network interfaces from which a specific file system is accessible. The elastic network interface is automatically created in the same virtual private cloud (VPC) that the Amazon FSx file system was created in. For more information, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/using-eni.html\">Elastic Network Interfaces</a> in the <i>Amazon EC2 User Guide.</i> </p> <p>For an Amazon FSx for Windows File Server file system, you can have one network interface ID. For an Amazon FSx for Lustre file system, you can have more than one.</p>"}, "DNSName": {"shape": "DNSName", "documentation": "<p>The Domain Name System (DNS) name for the file system.</p>"}, "KmsKeyId": {"shape": "KmsKeyId", "documentation": "<p>The ID of the Key Management Service (KMS) key used to encrypt Amazon FSx file system data. Used as follows with Amazon FSx file system types:</p> <ul> <li> <p>Amazon FSx for Lustre <code>PERSISTENT_1</code> and <code>PERSISTENT_2</code> deployment types only.</p> <p> <code>SCRATCH_1</code> and <code>SCRATCH_2</code> types are encrypted using the Amazon FSx service KMS key for your account.</p> </li> <li> <p>Amazon FSx for NetApp ONTAP</p> </li> <li> <p>Amazon FSx for OpenZFS</p> </li> <li> <p>Amazon FSx for Windows File Server</p> </li> </ul>"}, "ResourceARN": {"shape": "ResourceARN", "documentation": "<p>The Amazon Resource Name (ARN) of the file system resource.</p>"}, "Tags": {"shape": "Tags", "documentation": "<p>The tags to associate with the file system. For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/LustreGuide/tag-resources.html\">Tagging your Amazon FSx resources</a> in the <i>Amazon FSx for Lustre User Guide</i>.</p>"}, "WindowsConfiguration": {"shape": "WindowsFileSystemConfiguration", "documentation": "<p>The configuration for this Amazon FSx for Windows File Server file system.</p>"}, "LustreConfiguration": {"shape": "LustreFileSystemConfiguration"}, "AdministrativeActions": {"shape": "AdministrativeActions", "documentation": "<p>A list of administrative actions for the file system that are in process or waiting to be processed. Administrative actions describe changes to the Amazon FSx system that you have initiated using the <code>UpdateFileSystem</code> operation.</p>"}, "OntapConfiguration": {"shape": "OntapFileSystemConfiguration", "documentation": "<p>The configuration for this Amazon FSx for NetApp ONTAP file system.</p>"}, "FileSystemTypeVersion": {"shape": "FileSystemTypeVersion", "documentation": "<p>The Lustre version of the Amazon FSx for Lustre file system, which can be <code>2.10</code>, <code>2.12</code>, or <code>2.15</code>.</p>"}, "OpenZFSConfiguration": {"shape": "OpenZFSFileSystemConfiguration", "documentation": "<p>The configuration for this Amazon FSx for OpenZFS file system.</p>"}}, "documentation": "<p>A description of a specific Amazon FSx file system.</p>"}, "FileSystemAdministratorsGroupName": {"type": "string", "max": 256, "min": 1, "pattern": "^[^\\u0000\\u0085\\u2028\\u2029\\r\\n]{1,256}$"}, "FileSystemEndpoint": {"type": "structure", "members": {"DNSName": {"shape": "DNSName"}, "IpAddresses": {"shape": "OntapEndpointIpAddresses", "documentation": "<p>IP addresses of the file system endpoint.</p>"}}, "documentation": "<p>An Amazon FSx for NetApp ONTAP file system has two endpoints that are used to access data or to manage the file system using the NetApp ONTAP CLI, REST API, or NetApp SnapMirror. They are the <code>Management</code> and <code>Intercluster</code> endpoints.</p>"}, "FileSystemEndpoints": {"type": "structure", "members": {"Intercluster": {"shape": "FileSystemEndpoint", "documentation": "<p>An endpoint for managing your file system by setting up NetApp SnapMirror with other ONTAP systems.</p>"}, "Management": {"shape": "FileSystemEndpoint", "documentation": "<p>An endpoint for managing your file system using the NetApp ONTAP CLI and NetApp ONTAP API.</p>"}}, "documentation": "<p>An Amazon FSx for NetApp ONTAP file system has the following endpoints that are used to access data or to manage the file system using the NetApp ONTAP CLI, REST API, or NetApp SnapMirror.</p>"}, "FileSystemFailureDetails": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage", "documentation": "<p>A message describing any failures that occurred.</p>"}}, "documentation": "<p>A structure providing details of any failures that occurred.</p>"}, "FileSystemGID": {"type": "long", "max": 4294967295, "min": 0}, "FileSystemId": {"type": "string", "documentation": "<p>The globally unique ID of the file system, assigned by Amazon FSx.</p>", "max": 21, "min": 11, "pattern": "^(fs-[0-9a-f]{8,})$"}, "FileSystemIds": {"type": "list", "member": {"shape": "FileSystemId"}, "documentation": "<p>A list of file system IDs.</p>", "max": 50}, "FileSystemLifecycle": {"type": "string", "documentation": "<p>The lifecycle status of the file system.</p>", "enum": ["AVAILABLE", "CREATING", "FAILED", "DELETING", "MISCONFIGURED", "UPDATING", "MISCONFIGURED_UNAVAILABLE"]}, "FileSystemLustreMetadataConfiguration": {"type": "structure", "required": ["Mode"], "members": {"Iops": {"shape": "MetadataIops", "documentation": "<p>The number of Metadata IOPS provisioned for the file system.</p> <ul> <li> <p>For SSD file systems, valid values are <code>1500</code>, <code>3000</code>, <code>6000</code>, <code>12000</code>, and multiples of <code>12000</code> up to a maximum of <code>192000</code>.</p> </li> <li> <p>For Intelligent-Tiering file systems, valid values are <code>6000</code> and <code>12000</code>.</p> </li> </ul>"}, "Mode": {"shape": "MetadataConfigurationMode", "documentation": "<p>The metadata configuration mode for provisioning Metadata IOPS for the file system.</p> <ul> <li> <p>In AUTOMATIC mode (supported only on SSD file systems), FSx for Lustre automatically provisions and scales the number of Metadata IOPS on your file system based on your file system storage capacity.</p> </li> <li> <p>In USER_PROVISIONED mode, you can choose to specify the number of Metadata IOPS to provision for your file system.</p> </li> </ul>"}}, "documentation": "<p>The Lustre metadata performance configuration of an Amazon FSx for Lustre file system using a <code>PERSISTENT_2</code> deployment type. The configuration enables the file system to support increasing metadata performance.</p>"}, "FileSystemMaintenanceOperation": {"type": "string", "documentation": "<p>An enumeration specifying the currently ongoing maintenance operation.</p>", "enum": ["PATCHING", "BACKING_UP"]}, "FileSystemMaintenanceOperations": {"type": "list", "member": {"shape": "FileSystemMaintenanceOperation"}, "documentation": "<p>A list of maintenance operations.</p>", "max": 20}, "FileSystemNotFound": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>No Amazon FSx file systems were found based upon supplied parameters.</p>", "exception": true}, "FileSystemSecondaryGIDs": {"type": "list", "member": {"shape": "FileSystemGID"}, "max": 15}, "FileSystemType": {"type": "string", "documentation": "<p>The type of Amazon FSx file system.</p>", "enum": ["WINDOWS", "LUSTRE", "ONTAP", "OPENZFS"]}, "FileSystemTypeVersion": {"type": "string", "max": 20, "min": 1, "pattern": "^[0-9](.[0-9]*)*$"}, "FileSystemUID": {"type": "long", "max": 4294967295, "min": 0}, "FileSystems": {"type": "list", "member": {"shape": "FileSystem"}, "documentation": "<p>A list of file system resource descriptions.</p>", "max": 50}, "Filter": {"type": "structure", "members": {"Name": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The name for this filter.</p>"}, "Values": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The values of the filter. These are all the values for any of the applied filters.</p>"}}, "documentation": "<p>A filter used to restrict the results of describe calls. You can use multiple filters to return results that meet all applied filter requirements.</p>"}, "FilterName": {"type": "string", "documentation": "<p>The name for a filter.</p>", "enum": ["file-system-id", "backup-type", "file-system-type", "volume-id", "data-repository-type", "file-cache-id", "file-cache-type"]}, "FilterValue": {"type": "string", "documentation": "<p>The value for a filter.</p>", "max": 128, "min": 1, "pattern": "^[0-9a-zA-Z\\*\\.\\\\/\\?\\-\\_]*$"}, "FilterValues": {"type": "list", "member": {"shape": "FilterValue"}, "documentation": "<p>A list of filter values.</p>", "max": 20}, "Filters": {"type": "list", "member": {"shape": "Filter"}, "documentation": "<p>A list of <code>Filter</code> elements.</p>", "max": 10}, "Flag": {"type": "boolean"}, "FlexCacheEndpointType": {"type": "string", "enum": ["NONE", "ORIGIN", "CACHE"]}, "GeneralARN": {"type": "string", "max": 1024, "min": 8, "pattern": "^arn:[^:]{1,63}:[^:]{0,63}:[^:]{0,63}:(?:|\\d{12}):[^/].{0,1023}$"}, "HAPairs": {"type": "integer", "max": 12, "min": 1}, "IncludeShared": {"type": "boolean"}, "IncompatibleParameterError": {"type": "structure", "required": ["Parameter"], "members": {"Parameter": {"shape": "Parameter", "documentation": "<p>A parameter that is incompatible with the earlier request.</p>"}, "Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The error returned when a second request is received with the same client request token but different parameters settings. A client request token should always uniquely identify a single request.</p>", "exception": true}, "IncompatibleRegionForMultiAZ": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>Amazon FSx doesn't support Multi-AZ Windows File Server copy backup in the destination Region, so the copied backup can't be restored.</p>", "exception": true}, "InputOntapVolumeType": {"type": "string", "enum": ["RW", "DP"]}, "IntegerNoMax": {"type": "integer", "max": 2147483647, "min": 0}, "IntegerNoMaxFromNegativeOne": {"type": "integer", "max": 2147483647, "min": -1}, "IntegerRecordSizeKiB": {"type": "integer", "max": 4096, "min": 4}, "InternalServerError": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>A generic error indicating a server-side failure.</p>", "exception": true, "fault": true}, "InvalidAccessPoint": {"type": "structure", "members": {"ErrorCode": {"shape": "ErrorCode", "documentation": "<p>An error code indicating that the access point specified doesn't exist.</p>"}, "Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The access point specified doesn't exist.</p>", "exception": true}, "InvalidDataRepositoryType": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>You have filtered the response to a data repository type that is not supported.</p>", "exception": true}, "InvalidDestinationKmsKey": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The Key Management Service (KMS) key of the destination backup is not valid.</p>", "exception": true}, "InvalidExportPath": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The path provided for data repository export isn't valid.</p>", "exception": true}, "InvalidImportPath": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The path provided for data repository import isn't valid.</p>", "exception": true}, "InvalidNetworkSettings": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage", "documentation": "<p>Error message explaining what's wrong with network settings.</p>"}, "InvalidSubnetId": {"shape": "SubnetId", "documentation": "<p>The subnet ID that is either invalid or not part of the VPC specified.</p>"}, "InvalidSecurityGroupId": {"shape": "SecurityGroupId", "documentation": "<p>The security group ID is either invalid or not part of the VPC specified.</p>"}, "InvalidRouteTableId": {"shape": "RouteTableId", "documentation": "<p>The route table ID is either invalid or not part of the VPC specified.</p>"}}, "documentation": "<p>One or more network settings specified in the request are invalid.</p>", "exception": true}, "InvalidPerUnitStorageThroughput": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>An invalid value for <code>PerUnitStorageThroughput</code> was provided. Please create your file system again, using a valid value.</p>", "exception": true}, "InvalidRegion": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The Region provided for <code>SourceRegion</code> is not valid or is in a different Amazon Web Services partition.</p>", "exception": true}, "InvalidRequest": {"type": "structure", "members": {"ErrorCode": {"shape": "ErrorCode", "documentation": "<p>An error code indicating that the action or operation requested is invalid.</p>"}, "Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The action or operation requested is invalid. Verify that the action is typed correctly.</p>", "exception": true}, "InvalidSourceKmsKey": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The Key Management Service (KMS) key of the source backup is not valid.</p>", "exception": true}, "Iops": {"type": "long", "max": 2400000, "min": 0}, "IpAddress": {"type": "string", "max": 15, "min": 7, "pattern": "^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$"}, "IpAddressRange": {"type": "string", "max": 17, "min": 9, "pattern": "^[^\\u0000\\u0085\\u2028\\u2029\\r\\n]{9,17}$"}, "JunctionPath": {"type": "string", "max": 255, "min": 1, "pattern": "^[^\\u0000\\u0085\\u2028\\u2029\\r\\n]{1,255}$"}, "KmsKeyId": {"type": "string", "documentation": "<p>Specifies the ID of the Key Management Service (KMS) key to use for encrypting data on Amazon FSx file systems, as follows:</p> <ul> <li> <p>Amazon FSx for Lustre <code>PERSISTENT_1</code> and <code>PERSISTENT_2</code> deployment types only.</p> <p> <code>SCRATCH_1</code> and <code>SCRATCH_2</code> types are encrypted using the Amazon FSx service KMS key for your account.</p> </li> <li> <p>Amazon FSx for NetApp ONTAP</p> </li> <li> <p>Amazon FSx for OpenZFS</p> </li> <li> <p>Amazon FSx for Windows File Server</p> </li> </ul> <p>If a <code>KmsKeyId</code> isn't specified, the Amazon FSx-managed KMS key for your account is used. For more information, see <a href=\"https://docs.aws.amazon.com/kms/latest/APIReference/API_Encrypt.html\">Encrypt</a> in the <i>Key Management Service API Reference</i>.</p>", "max": 2048, "min": 1, "pattern": "^.{1,2048}$"}, "LastUpdatedTime": {"type": "timestamp"}, "LifecycleTransitionReason": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>Describes why a resource lifecycle state changed.</p>"}, "LimitedMaxResults": {"type": "integer", "max": 25, "min": 1}, "ListTagsForResourceRequest": {"type": "structure", "required": ["ResourceARN"], "members": {"ResourceARN": {"shape": "ResourceARN", "documentation": "<p>The ARN of the Amazon FSx resource that will have its tags listed.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>Maximum number of tags to return in the response (integer). This parameter value must be greater than 0. The number of items that Amazon FSx returns is the minimum of the <code>MaxResults</code> parameter specified in the request and the service's internal maximum number of items per page.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>Opaque pagination token returned from a previous <code>ListTagsForResource</code> operation (String). If a token present, the action continues the list from where the returning call left off.</p>"}}, "documentation": "<p>The request object for <code>ListTagsForResource</code> operation.</p>"}, "ListTagsForResourceResponse": {"type": "structure", "members": {"Tags": {"shape": "Tags", "documentation": "<p>A list of tags on the resource.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>This is present if there are more tags than returned in the response (String). You can use the <code>NextToken</code> value in the later request to fetch the tags. </p>"}}, "documentation": "<p>The response object for <code>ListTagsForResource</code> operation.</p>"}, "LustreAccessAuditLogLevel": {"type": "string", "enum": ["DISABLED", "WARN_ONLY", "ERROR_ONLY", "WARN_ERROR"]}, "LustreDeploymentType": {"type": "string", "enum": ["SCRATCH_1", "SCRATCH_2", "PERSISTENT_1", "PERSISTENT_2"]}, "LustreFileSystemConfiguration": {"type": "structure", "members": {"WeeklyMaintenanceStartTime": {"shape": "WeeklyTime", "documentation": "<p>The preferred start time to perform weekly maintenance, formatted d:HH:MM in the UTC time zone. Here, <code>d</code> is the weekday number, from 1 through 7, beginning with Monday and ending with Sunday.</p>"}, "DataRepositoryConfiguration": {"shape": "DataRepositoryConfiguration"}, "DeploymentType": {"shape": "LustreDeploymentType", "documentation": "<p>The deployment type of the FSx for Lustre file system. <i>Scratch deployment type</i> is designed for temporary storage and shorter-term processing of data.</p> <p> <code>SCRATCH_1</code> and <code>SCRATCH_2</code> deployment types are best suited for when you need temporary storage and shorter-term processing of data. The <code>SCRATCH_2</code> deployment type provides in-transit encryption of data and higher burst throughput capacity than <code>SCRATCH_1</code>.</p> <p>The <code>PERSISTENT_1</code> and <code>PERSISTENT_2</code> deployment type is used for longer-term storage and workloads and encryption of data in transit. <code>PERSISTENT_2</code> offers higher <code>PerUnitStorageThroughput</code> (up to 1000 MB/s/TiB) along with a lower minimum storage capacity requirement (600 GiB). To learn more about FSx for Lustre deployment types, see <a href=\"https://docs.aws.amazon.com/fsx/latest/LustreGuide/using-fsx-lustre.html\">Deployment and storage class options for FSx for Lustre file systems</a>.</p> <p>The default is <code>SCRATCH_1</code>.</p>"}, "PerUnitStorageThroughput": {"shape": "PerUnitStorageThroughput", "documentation": "<p>Per unit storage throughput represents the megabytes per second of read or write throughput per 1 tebibyte of storage provisioned. File system throughput capacity is equal to Storage capacity (TiB) * PerUnitStorageThroughput (MB/s/TiB). This option is only valid for <code>PERSISTENT_1</code> and <code>PERSISTENT_2</code> deployment types. </p> <p>Valid values:</p> <ul> <li> <p>For <code>PERSISTENT_1</code> SSD storage: 50, 100, 200.</p> </li> <li> <p>For <code>PERSISTENT_1</code> HDD storage: 12, 40.</p> </li> <li> <p>For <code>PERSISTENT_2</code> SSD storage: 125, 250, 500, 1000.</p> </li> </ul>"}, "MountName": {"shape": "LustreFileSystemMountName", "documentation": "<p>You use the <code>MountName</code> value when mounting the file system.</p> <p>For the <code>SCRATCH_1</code> deployment type, this value is always \"<code>fsx</code>\". For <code>SCRATCH_2</code>, <code>PERSISTENT_1</code>, and <code>PERSISTENT_2</code> deployment types, this value is a string that is unique within an Amazon Web Services Region. </p>"}, "DailyAutomaticBackupStartTime": {"shape": "DailyTime"}, "AutomaticBackupRetentionDays": {"shape": "AutomaticBackupRetentionDays"}, "CopyTagsToBackups": {"shape": "Flag", "documentation": "<p>A boolean flag indicating whether tags on the file system are copied to backups. If it's set to true, all tags on the file system are copied to all automatic backups and any user-initiated backups where the user doesn't specify any tags. If this value is true, and you specify one or more tags, only the specified tags are copied to backups. If you specify one or more tags when creating a user-initiated backup, no tags are copied from the file system, regardless of this value. (Default = false)</p>"}, "DriveCacheType": {"shape": "DriveCacheType", "documentation": "<p>The type of drive cache used by <code>PERSISTENT_1</code> file systems that are provisioned with HDD storage devices. This parameter is required when <code>StorageType</code> is HDD. When set to <code>READ</code> the file system has an SSD storage cache that is sized to 20% of the file system's storage capacity. This improves the performance for frequently accessed files by caching up to 20% of the total storage capacity.</p> <p>This parameter is required when <code>StorageType</code> is set to HDD.</p>"}, "DataCompressionType": {"shape": "DataCompressionType", "documentation": "<p>The data compression configuration for the file system. <code>DataCompressionType</code> can have the following values:</p> <ul> <li> <p> <code>NONE</code> - Data compression is turned off for the file system.</p> </li> <li> <p> <code>LZ4</code> - Data compression is turned on with the LZ4 algorithm.</p> </li> </ul> <p>For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/LustreGuide/data-compression.html\">Lustre data compression</a>.</p>"}, "LogConfiguration": {"shape": "LustreLogConfiguration", "documentation": "<p>The Lustre logging configuration. Lustre logging writes the enabled log events for your file system to Amazon CloudWatch Logs.</p>"}, "RootSquashConfiguration": {"shape": "LustreRootSquashConfiguration", "documentation": "<p>The Lustre root squash configuration for an Amazon FSx for Lustre file system. When enabled, root squash restricts root-level access from clients that try to access your file system as a root user.</p>"}, "MetadataConfiguration": {"shape": "FileSystemLustreMetadataConfiguration", "documentation": "<p>The Lustre metadata performance configuration for an Amazon FSx for Lustre file system using a <code>PERSISTENT_2</code> deployment type.</p>"}, "EfaEnabled": {"shape": "Flag", "documentation": "<p>Specifies whether Elastic Fabric Adapter (EFA) and GPUDirect Storage (GDS) support is enabled for the Amazon FSx for Lustre file system.</p>"}, "ThroughputCapacity": {"shape": "ThroughputCapacityMbps", "documentation": "<p>The throughput of an Amazon FSx for Lustre file system using the Intelligent-Tiering storage class, measured in megabytes per second (MBps).</p>"}, "DataReadCacheConfiguration": {"shape": "LustreReadCacheConfiguration", "documentation": "<p>Required when <code>StorageType</code> is set to <code>INTELLIGENT_TIERING</code>. Specifies the optional provisioned SSD read cache.</p>"}}, "documentation": "<p>The configuration for the Amazon FSx for Lustre file system.</p>"}, "LustreFileSystemMountName": {"type": "string", "max": 8, "min": 1, "pattern": "^([A-Za-z0-9_-]{1,8})$"}, "LustreLogConfiguration": {"type": "structure", "required": ["Level"], "members": {"Level": {"shape": "LustreAccessAuditLogLevel", "documentation": "<p>The data repository events that are logged by Amazon FSx.</p> <ul> <li> <p> <code>WARN_ONLY</code> - only warning events are logged.</p> </li> <li> <p> <code>ERROR_ONLY</code> - only error events are logged.</p> </li> <li> <p> <code>WARN_ERROR</code> - both warning events and error events are logged.</p> </li> <li> <p> <code>DISABLED</code> - logging of data repository events is turned off.</p> </li> </ul> <p>Note that Amazon File Cache uses a default setting of <code>WARN_ERROR</code>, which can't be changed.</p>"}, "Destination": {"shape": "GeneralARN", "documentation": "<p>The Amazon Resource Name (ARN) that specifies the destination of the logs. The destination can be any Amazon CloudWatch Logs log group ARN. The destination ARN must be in the same Amazon Web Services partition, Amazon Web Services Region, and Amazon Web Services account as your Amazon FSx file system.</p>"}}, "documentation": "<p>The configuration for Lustre logging used to write the enabled logging events for your Amazon FSx for Lustre file system or Amazon File Cache resource to Amazon CloudWatch Logs.</p>"}, "LustreLogCreateConfiguration": {"type": "structure", "required": ["Level"], "members": {"Level": {"shape": "LustreAccessAuditLogLevel", "documentation": "<p>Sets which data repository events are logged by Amazon FSx.</p> <ul> <li> <p> <code>WARN_ONLY</code> - only warning events are logged.</p> </li> <li> <p> <code>ERROR_ONLY</code> - only error events are logged.</p> </li> <li> <p> <code>WARN_ERROR</code> - both warning events and error events are logged.</p> </li> <li> <p> <code>DISABLED</code> - logging of data repository events is turned off.</p> </li> </ul>"}, "Destination": {"shape": "GeneralARN", "documentation": "<p>The Amazon Resource Name (ARN) that specifies the destination of the logs.</p> <p>The destination can be any Amazon CloudWatch Logs log group ARN, with the following requirements:</p> <ul> <li> <p>The destination ARN that you provide must be in the same Amazon Web Services partition, Amazon Web Services Region, and Amazon Web Services account as your Amazon FSx file system.</p> </li> <li> <p>The name of the Amazon CloudWatch Logs log group must begin with the <code>/aws/fsx</code> prefix.</p> </li> <li> <p>If you do not provide a destination, Amazon FSx will create and use a log stream in the CloudWatch Logs <code>/aws/fsx/lustre</code> log group (for Amazon FSx for Lustre) or <code>/aws/fsx/filecache</code> (for Amazon File Cache).</p> </li> <li> <p>If <code>Destination</code> is provided and the resource does not exist, the request will fail with a <code>BadRequest</code> error.</p> </li> <li> <p>If <code>Level</code> is set to <code>DISABLED</code>, you cannot specify a destination in <code>Destination</code>.</p> </li> </ul>"}}, "documentation": "<p>The Lustre logging configuration used when creating or updating an Amazon FSx for Lustre file system. An Amazon File Cache is created with Lustre logging enabled by default, with a setting of <code>WARN_ERROR</code> for the logging events. which can't be changed.</p> <p>Lustre logging writes the enabled logging events for your file system or cache to Amazon CloudWatch Logs.</p>"}, "LustreNoSquashNid": {"type": "string", "max": 43, "min": 11, "pattern": "^([0-9\\[\\]\\-]*\\.){3}([0-9\\[\\]\\-]*)@tcp$"}, "LustreNoSquashNids": {"type": "list", "member": {"shape": "LustreNoSquashNid"}, "max": 64}, "LustreReadCacheConfiguration": {"type": "structure", "members": {"SizingMode": {"shape": "LustreReadCacheSizingMode", "documentation": "<p> Specifies how the provisioned SSD read cache is sized, as follows: </p> <ul> <li> <p>Set to <code>NO_CACHE</code> if you do not want to use an SSD read cache with your Intelligent-Tiering file system.</p> </li> <li> <p>Set to <code>USER_PROVISIONED</code> to specify the exact size of your SSD read cache.</p> </li> <li> <p>Set to <code>PROPORTIONAL_TO_THROUGHPUT_CAPACITY</code> to have your SSD read cache automatically sized based on your throughput capacity.</p> </li> </ul>"}, "SizeGiB": {"shape": "StorageCapacity", "documentation": "<p> Required if <code>SizingMode</code> is set to <code>USER_PROVISIONED</code>. Specifies the size of the file system's SSD read cache, in gibibytes (GiB). </p> <p>The SSD read cache size is distributed across provisioned file servers in your file system. Intelligent-Tiering file systems support a minimum of 32 GiB and maximum of 131072 GiB for SSD read cache size for every 4,000 MB/s of throughput capacity provisioned.</p>"}}, "documentation": "<p> The configuration for the optional provisioned SSD read cache on Amazon FSx for Lustre file systems that use the Intelligent-Tiering storage class. </p>"}, "LustreReadCacheSizingMode": {"type": "string", "enum": ["NO_CACHE", "USER_PROVISIONED", "PROPORTIONAL_TO_THROUGHPUT_CAPACITY"]}, "LustreRootSquash": {"type": "string", "max": 21, "min": 3, "pattern": "^([0-9]{1,10}):([0-9]{1,10})$"}, "LustreRootSquashConfiguration": {"type": "structure", "members": {"RootSquash": {"shape": "LustreRootSquash", "documentation": "<p>You enable root squash by setting a user ID (UID) and group ID (GID) for the file system in the format <code>UID:GID</code> (for example, <code>365534:65534</code>). The UID and GID values can range from <code>0</code> to <code>4294967294</code>:</p> <ul> <li> <p>A non-zero value for UID and GID enables root squash. The UID and GID values can be different, but each must be a non-zero value.</p> </li> <li> <p>A value of <code>0</code> (zero) for UID and GID indicates root, and therefore disables root squash.</p> </li> </ul> <p>When root squash is enabled, the user ID and group ID of a root user accessing the file system are re-mapped to the UID and GID you provide.</p>"}, "NoSquashNids": {"shape": "LustreNoSquashNids", "documentation": "<p>When root squash is enabled, you can optionally specify an array of NIDs of clients for which root squash does not apply. A client NID is a Lustre Network Identifier used to uniquely identify a client. You can specify the NID as either a single address or a range of addresses:</p> <ul> <li> <p>A single address is described in standard Lustre NID format by specifying the client’s IP address followed by the Lustre network ID (for example, <code>********@tcp</code>).</p> </li> <li> <p>An address range is described using a dash to separate the range (for example, <code>10.0.[2-10].[1-255]@tcp</code>).</p> </li> </ul>"}}, "documentation": "<p>The configuration for Lustre root squash used to restrict root-level access from clients that try to access your FSx for Lustre file system as root. Use the <code>RootSquash</code> parameter to enable root squash. To learn more about Lustre root squash, see <a href=\"https://docs.aws.amazon.com/fsx/latest/LustreGuide/root-squash.html\">Lustre root squash</a>.</p> <p>You can also use the <code>NoSquashNids</code> parameter to provide an array of clients who are not affected by the root squash setting. These clients will access the file system as root, with unrestricted privileges.</p>"}, "MaxResults": {"type": "integer", "documentation": "<p>The maximum number of resources to return in the response. This value must be an integer greater than zero.</p>", "max": 2147483647, "min": 1}, "Megabytes": {"type": "integer", "max": 512000, "min": 1}, "MegabytesPerSecond": {"type": "integer", "documentation": "<p>The sustained throughput of an Amazon FSx file system in Megabytes per second (MBps).</p>", "max": 100000, "min": 8}, "MetadataConfigurationMode": {"type": "string", "enum": ["AUTOMATIC", "USER_PROVISIONED"]}, "MetadataIops": {"type": "integer", "max": 192000, "min": 1500}, "MetadataStorageCapacity": {"type": "integer", "max": 2147483647, "min": 0}, "MissingFileCacheConfiguration": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>A cache configuration is required for this operation.</p>", "exception": true}, "MissingFileSystemConfiguration": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>A file system configuration is required for this operation.</p>", "exception": true}, "MissingVolumeConfiguration": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>A volume configuration is required for this operation.</p>", "exception": true}, "NFSDataRepositoryConfiguration": {"type": "structure", "required": ["Version"], "members": {"Version": {"shape": "NfsVersion", "documentation": "<p>The version of the NFS (Network File System) protocol of the NFS data repository. Currently, the only supported value is <code>NFS3</code>, which indicates that the data repository must support the NFSv3 protocol.</p>"}, "DnsIps": {"shape": "RepositoryDnsIps", "documentation": "<p>A list of up to 2 IP addresses of DNS servers used to resolve the NFS file system domain name. The provided IP addresses can either be the IP addresses of a DNS forwarder or resolver that the customer manages and runs inside the customer VPC, or the IP addresses of the on-premises DNS servers.</p>"}, "AutoExportPolicy": {"shape": "AutoExportPolicy", "documentation": "<p>This parameter is not supported for Amazon File Cache.</p>"}}, "documentation": "<p>The configuration for a data repository association that links an Amazon File Cache resource to an NFS data repository.</p>"}, "Namespace": {"type": "string", "max": 4096, "min": 1, "pattern": "^[^\\u0000\\u0085\\u2028\\u2029\\r\\n]{1,4096}$"}, "NetBiosAlias": {"type": "string", "max": 15, "min": 1, "pattern": "^[^\\u0000\\u0085\\u2028\\u2029\\r\\n]{1,255}$"}, "NetworkInterfaceId": {"type": "string", "documentation": "<p>An elastic network interface ID. An elastic network interface is a logical networking component in a virtual private cloud (VPC) that represents a virtual network card. For more information, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/using-eni.html\">Elastic Network Interfaces</a> in the <i>Amazon EC2 User Guide for Linux Instances</i>.</p>", "max": 21, "min": 12, "pattern": "^(eni-[0-9a-f]{8,})$"}, "NetworkInterfaceIds": {"type": "list", "member": {"shape": "NetworkInterfaceId"}, "documentation": "<p>A list of network interface IDs.</p>", "max": 50}, "NextToken": {"type": "string", "documentation": "<p>(Optional) Opaque pagination token returned from a previous operation (String). If present, this token indicates from what point you can continue processing the request, where the previous <code>NextToken</code> value left off.</p>", "max": 255, "min": 1, "pattern": "^(?:[A-Za-z0-9+\\/]{4})*(?:[A-Za-z0-9+\\/]{2}==|[A-Za-z0-9+\\/]{3}=)?$"}, "NfsVersion": {"type": "string", "enum": ["NFS3"]}, "NotServiceResourceError": {"type": "structure", "required": ["ResourceARN"], "members": {"ResourceARN": {"shape": "ResourceARN", "documentation": "<p>The Amazon Resource Name (ARN) of the non-Amazon FSx resource.</p>"}, "Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The resource specified for the tagging operation is not a resource type owned by Amazon FSx. Use the API of the relevant service to perform the operation. </p>", "exception": true}, "OntapDeploymentType": {"type": "string", "enum": ["MULTI_AZ_1", "SINGLE_AZ_1", "SINGLE_AZ_2", "MULTI_AZ_2"]}, "OntapEndpointIpAddresses": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "max": 24, "min": 1}, "OntapFileSystemConfiguration": {"type": "structure", "members": {"AutomaticBackupRetentionDays": {"shape": "AutomaticBackupRetentionDays"}, "DailyAutomaticBackupStartTime": {"shape": "DailyTime"}, "DeploymentType": {"shape": "OntapDeploymentType", "documentation": "<p>Specifies the FSx for ONTAP file system deployment type in use in the file system. </p> <ul> <li> <p> <code>MULTI_AZ_1</code> - A high availability file system configured for Multi-AZ redundancy to tolerate temporary Availability Zone (AZ) unavailability. This is a first-generation FSx for ONTAP file system.</p> </li> <li> <p> <code>MULTI_AZ_2</code> - A high availability file system configured for Multi-AZ redundancy to tolerate temporary AZ unavailability. This is a second-generation FSx for ONTAP file system.</p> </li> <li> <p> <code>SINGLE_AZ_1</code> - A file system configured for Single-AZ redundancy. This is a first-generation FSx for ONTAP file system.</p> </li> <li> <p> <code>SINGLE_AZ_2</code> - A file system configured with multiple high-availability (HA) pairs for Single-AZ redundancy. This is a second-generation FSx for ONTAP file system.</p> </li> </ul> <p>For information about the use cases for Multi-AZ and Single-AZ deployments, refer to <a href=\"https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/high-availability-multiAZ.html\">Choosing Multi-AZ or Single-AZ file system deployment</a>. </p>"}, "EndpointIpAddressRange": {"shape": "IpAddressRange", "documentation": "<p>(Multi-AZ only) Specifies the IP address range in which the endpoints to access your file system will be created. By default in the Amazon FSx API, Amazon FSx selects an unused IP address range for you from the 198.19.* range. By default in the Amazon FSx console, Amazon FSx chooses the last 64 IP addresses from the VPC’s primary CIDR range to use as the endpoint IP address range for the file system. You can have overlapping endpoint IP addresses for file systems deployed in the same VPC/route tables.</p>"}, "Endpoints": {"shape": "FileSystemEndpoints", "documentation": "<p>The <code>Management</code> and <code>Intercluster</code> endpoints that are used to access data or to manage the file system using the NetApp ONTAP CLI, REST API, or NetApp SnapMirror.</p>"}, "DiskIopsConfiguration": {"shape": "DiskIopsConfiguration", "documentation": "<p>The SSD IOPS configuration for the ONTAP file system, specifying the number of provisioned IOPS and the provision mode.</p>"}, "PreferredSubnetId": {"shape": "SubnetId"}, "RouteTableIds": {"shape": "RouteTableIds", "documentation": "<p>(Multi-AZ only) The VPC route tables in which your file system's endpoints are created.</p>"}, "ThroughputCapacity": {"shape": "MegabytesPerSecond"}, "WeeklyMaintenanceStartTime": {"shape": "WeeklyTime"}, "FsxAdminPassword": {"shape": "AdminPassword", "documentation": "<p>You can use the <code>fsxadmin</code> user account to access the NetApp ONTAP CLI and REST API. The password value is always redacted in the response.</p>"}, "HAPairs": {"shape": "HAPairs", "documentation": "<p>Specifies how many high-availability (HA) file server pairs the file system will have. The default value is 1. The value of this property affects the values of <code>StorageCapacity</code>, <code>Iops</code>, and <code>ThroughputCapacity</code>. For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/HA-pairs.html\">High-availability (HA) pairs</a> in the FSx for ONTAP user guide.</p> <p>Amazon FSx responds with an HTTP status code 400 (Bad Request) for the following conditions:</p> <ul> <li> <p>The value of <code>HAPairs</code> is less than 1 or greater than 12.</p> </li> <li> <p>The value of <code>HAPairs</code> is greater than 1 and the value of <code>DeploymentType</code> is <code>SINGLE_AZ_1</code>, <code>MULTI_AZ_1</code>, or <code>MULTI_AZ_2</code>.</p> </li> </ul>"}, "ThroughputCapacityPerHAPair": {"shape": "ThroughputCapacityPerHAPair", "documentation": "<p>Use to choose the throughput capacity per HA pair. When the value of <code>HAPairs</code> is equal to 1, the value of <code>ThroughputCapacityPerHAPair</code> is the total throughput for the file system.</p> <p>This field and <code>ThroughputCapacity</code> cannot be defined in the same API call, but one is required.</p> <p>This field and <code>ThroughputCapacity</code> are the same for file systems with one HA pair.</p> <ul> <li> <p>For <code>SINGLE_AZ_1</code> and <code>MULTI_AZ_1</code> file systems, valid values are 128, 256, 512, 1024, 2048, or 4096 MBps.</p> </li> <li> <p>For <code>SINGLE_AZ_2</code>, valid values are 1536, 3072, or 6144 MBps.</p> </li> <li> <p>For <code>MULTI_AZ_2</code>, valid values are 384, 768, 1536, 3072, or 6144 MBps.</p> </li> </ul> <p>Amazon FSx responds with an HTTP status code 400 (Bad Request) for the following conditions:</p> <ul> <li> <p>The value of <code>ThroughputCapacity</code> and <code>ThroughputCapacityPerHAPair</code> are not the same value.</p> </li> <li> <p>The value of deployment type is <code>SINGLE_AZ_2</code> and <code>ThroughputCapacity</code> / <code>ThroughputCapacityPerHAPair</code> is not a valid HA pair (a value between 1 and 12).</p> </li> <li> <p>The value of <code>ThroughputCapacityPerHAPair</code> is not a valid value.</p> </li> </ul>"}}, "documentation": "<p>Configuration for the FSx for NetApp ONTAP file system.</p>"}, "OntapVolumeConfiguration": {"type": "structure", "members": {"FlexCacheEndpointType": {"shape": "FlexCacheEndpointType", "documentation": "<p>Specifies the FlexCache endpoint type of the volume. Valid values are the following:</p> <ul> <li> <p> <code>NONE</code> specifies that the volume doesn't have a FlexCache configuration. <code>NONE</code> is the default.</p> </li> <li> <p> <code>ORIGIN</code> specifies that the volume is the origin volume for a FlexCache volume.</p> </li> <li> <p> <code>CACHE</code> specifies that the volume is a FlexCache volume.</p> </li> </ul>"}, "JunctionPath": {"shape": "JunctionPath", "documentation": "<p>Specifies the directory that network-attached storage (NAS) clients use to mount the volume, along with the storage virtual machine (SVM) Domain Name System (DNS) name or IP address. You can create a <code>JunctionPath</code> directly below a parent volume junction or on a directory within a volume. A <code>JunctionPath</code> for a volume named <code>vol3</code> might be <code>/vol1/vol2/vol3</code>, or <code>/vol1/dir2/vol3</code>, or even <code>/dir1/dir2/vol3</code>.</p>"}, "SecurityStyle": {"shape": "SecurityStyle", "documentation": "<p>The security style for the volume, which can be <code>UNIX</code>, <code>NTFS</code>, or <code>MIXED</code>.</p>"}, "SizeInMegabytes": {"shape": "VolumeCapacity", "documentation": "<p>The configured size of the volume, in megabytes (MBs).</p>"}, "StorageEfficiencyEnabled": {"shape": "Flag", "documentation": "<p>The volume's storage efficiency setting.</p>"}, "StorageVirtualMachineId": {"shape": "StorageVirtualMachineId", "documentation": "<p>The ID of the volume's storage virtual machine.</p>"}, "StorageVirtualMachineRoot": {"shape": "Flag", "documentation": "<p>A Boolean flag indicating whether this volume is the root volume for its storage virtual machine (SVM). Only one volume on an SVM can be the root volume. This value defaults to <code>false</code>. If this value is <code>true</code>, then this is the SVM root volume.</p> <p>This flag is useful when you're deleting an SVM, because you must first delete all non-root volumes. This flag, when set to <code>false</code>, helps you identify which volumes to delete before you can delete the SVM.</p>"}, "TieringPolicy": {"shape": "TieringPolicy", "documentation": "<p>The volume's <code>TieringPolicy</code> setting.</p>"}, "UUID": {"shape": "UUID", "documentation": "<p>The volume's universally unique identifier (UUID).</p>"}, "OntapVolumeType": {"shape": "OntapVolumeType", "documentation": "<p>Specifies the type of volume. Valid values are the following:</p> <ul> <li> <p> <code>RW</code> specifies a read/write volume. <code>RW</code> is the default.</p> </li> <li> <p> <code>DP</code> specifies a data-protection volume. You can protect data by replicating it to data-protection mirror copies. If a disaster occurs, you can use these data-protection mirror copies to recover data.</p> </li> <li> <p> <code>LS</code> specifies a load-sharing mirror volume. A load-sharing mirror reduces the network traffic to a FlexVol volume by providing additional read-only access to clients.</p> </li> </ul>"}, "SnapshotPolicy": {"shape": "SnapshotPolicy", "documentation": "<p>Specifies the snapshot policy for the volume. There are three built-in snapshot policies:</p> <ul> <li> <p> <code>default</code>: This is the default policy. A maximum of six hourly snapshots taken five minutes past the hour. A maximum of two daily snapshots taken Monday through Saturday at 10 minutes after midnight. A maximum of two weekly snapshots taken every Sunday at 15 minutes after midnight.</p> </li> <li> <p> <code>default-1weekly</code>: This policy is the same as the <code>default</code> policy except that it only retains one snapshot from the weekly schedule.</p> </li> <li> <p> <code>none</code>: This policy does not take any snapshots. This policy can be assigned to volumes to prevent automatic snapshots from being taken.</p> </li> </ul> <p>You can also provide the name of a custom policy that you created with the ONTAP CLI or REST API.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/snapshots-ontap.html#snapshot-policies\">Snapshot policies</a> in the Amazon FSx for NetApp ONTAP User Guide.</p>"}, "CopyTagsToBackups": {"shape": "Flag", "documentation": "<p>A boolean flag indicating whether tags for the volume should be copied to backups. This value defaults to false. If it's set to true, all tags for the volume are copied to all automatic and user-initiated backups where the user doesn't specify tags. If this value is true, and you specify one or more tags, only the specified tags are copied to backups. If you specify one or more tags when creating a user-initiated backup, no tags are copied from the volume, regardless of this value.</p>"}, "SnaplockConfiguration": {"shape": "SnaplockConfiguration", "documentation": "<p>The SnapLock configuration object for an FSx for ONTAP SnapLock volume. </p>"}, "VolumeStyle": {"shape": "VolumeStyle", "documentation": "<p>Use to specify the style of an ONTAP volume. For more information about FlexVols and FlexGroups, see <a href=\"https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/volume-types.html\">Volume types</a> in Amazon FSx for NetApp ONTAP User Guide.</p>"}, "AggregateConfiguration": {"shape": "AggregateConfiguration", "documentation": "<p>This structure specifies configuration options for a volume’s storage aggregate or aggregates.</p>"}, "SizeInBytes": {"shape": "VolumeCapacityBytes", "documentation": "<p>The configured size of the volume, in bytes.</p>"}}, "documentation": "<p>The configuration of an Amazon FSx for NetApp ONTAP volume.</p>"}, "OntapVolumeType": {"type": "string", "enum": ["RW", "DP", "LS"]}, "OpenZFSClientConfiguration": {"type": "structure", "required": ["Clients", "Options"], "members": {"Clients": {"shape": "OpenZFSClients", "documentation": "<p>A value that specifies who can mount the file system. You can provide a wildcard character (<code>*</code>), an IP address (<code>0.0.0.0</code>), or a CIDR address (<code>*********/24</code>). By default, Amazon FSx uses the wildcard character when specifying the client. </p>"}, "Options": {"shape": "OpenZFSNfsExportOptions", "documentation": "<p>The options to use when mounting the file system. For a list of options that you can use with Network File System (NFS), see the <a href=\"https://linux.die.net/man/5/exports\">exports(5) - Linux man page</a>. When choosing your options, consider the following:</p> <ul> <li> <p> <code>crossmnt</code> is used by default. If you don't specify <code>crossmnt</code> when changing the client configuration, you won't be able to see or access snapshots in your file system's snapshot directory.</p> </li> <li> <p> <code>sync</code> is used by default. If you instead specify <code>async</code>, the system acknowledges writes before writing to disk. If the system crashes before the writes are finished, you lose the unwritten data. </p> </li> </ul>"}}, "documentation": "<p>Specifies who can mount an OpenZFS file system and the options available while mounting the file system.</p>"}, "OpenZFSClientConfigurations": {"type": "list", "member": {"shape": "OpenZFSClientConfiguration"}, "max": 25}, "OpenZFSClients": {"type": "string", "max": 128, "min": 1, "pattern": "^[ -~]{1,128}$"}, "OpenZFSCopyStrategy": {"type": "string", "enum": ["CLONE", "FULL_COPY", "INCREMENTAL_COPY"]}, "OpenZFSCreateRootVolumeConfiguration": {"type": "structure", "members": {"RecordSizeKiB": {"shape": "IntegerRecordSizeKiB", "documentation": "<p>Specifies the record size of an OpenZFS root volume, in kibibytes (KiB). Valid values are 4, 8, 16, 32, 64, 128, 256, 512, or 1024 KiB. The default is 128 KiB. Most workloads should use the default record size. Database workflows can benefit from a smaller record size, while streaming workflows can benefit from a larger record size. For additional guidance on setting a custom record size, see <a href=\"https://docs.aws.amazon.com/fsx/latest/OpenZFSGuide/performance.html#performance-tips-zfs\"> Tips for maximizing performance</a> in the <i>Amazon FSx for OpenZFS User Guide</i>.</p>"}, "DataCompressionType": {"shape": "OpenZFSDataCompressionType", "documentation": "<p>Specifies the method used to compress the data on the volume. The compression type is <code>NONE</code> by default.</p> <ul> <li> <p> <code>NONE</code> - Doesn't compress the data on the volume. <code>NONE</code> is the default.</p> </li> <li> <p> <code>ZSTD</code> - Compresses the data in the volume using the Zstandard (ZSTD) compression algorithm. Compared to LZ4, Z-Standard provides a better compression ratio to minimize on-disk storage utilization.</p> </li> <li> <p> <code>LZ4</code> - Compresses the data in the volume using the LZ4 compression algorithm. Compared to Z-Standard, LZ4 is less compute-intensive and delivers higher write throughput speeds.</p> </li> </ul>"}, "NfsExports": {"shape": "OpenZFSNfsExports", "documentation": "<p>The configuration object for mounting a file system.</p>"}, "UserAndGroupQuotas": {"shape": "OpenZFSUserAndGroupQuotas", "documentation": "<p>An object specifying how much storage users or groups can use on the volume.</p>"}, "CopyTagsToSnapshots": {"shape": "Flag", "documentation": "<p>A Boolean value indicating whether tags for the volume should be copied to snapshots of the volume. This value defaults to <code>false</code>. If it's set to <code>true</code>, all tags for the volume are copied to snapshots where the user doesn't specify tags. If this value is <code>true</code> and you specify one or more tags, only the specified tags are copied to snapshots. If you specify one or more tags when creating the snapshot, no tags are copied from the volume, regardless of this value. </p>"}, "ReadOnly": {"shape": "Read<PERSON>nly", "documentation": "<p>A Boolean value indicating whether the volume is read-only. Setting this value to <code>true</code> can be useful after you have completed changes to a volume and no longer want changes to occur. </p>"}}, "documentation": "<p>The configuration of an Amazon FSx for OpenZFS root volume.</p>"}, "OpenZFSDataCompressionType": {"type": "string", "enum": ["NONE", "ZSTD", "LZ4"]}, "OpenZFSDeploymentType": {"type": "string", "enum": ["SINGLE_AZ_1", "SINGLE_AZ_2", "SINGLE_AZ_HA_1", "SINGLE_AZ_HA_2", "MULTI_AZ_1"]}, "OpenZFSFileSystemConfiguration": {"type": "structure", "members": {"AutomaticBackupRetentionDays": {"shape": "AutomaticBackupRetentionDays"}, "CopyTagsToBackups": {"shape": "Flag", "documentation": "<p>A Boolean value indicating whether tags on the file system should be copied to backups. If it's set to <code>true</code>, all tags on the file system are copied to all automatic backups and any user-initiated backups where the user doesn't specify any tags. If this value is <code>true</code> and you specify one or more tags, only the specified tags are copied to backups. If you specify one or more tags when creating a user-initiated backup, no tags are copied from the file system, regardless of this value. </p>"}, "CopyTagsToVolumes": {"shape": "Flag", "documentation": "<p>A Boolean value indicating whether tags for the volume should be copied to snapshots. This value defaults to <code>false</code>. If it's set to <code>true</code>, all tags for the volume are copied to snapshots where the user doesn't specify tags. If this value is <code>true</code> and you specify one or more tags, only the specified tags are copied to snapshots. If you specify one or more tags when creating the snapshot, no tags are copied from the volume, regardless of this value. </p>"}, "DailyAutomaticBackupStartTime": {"shape": "DailyTime"}, "DeploymentType": {"shape": "OpenZFSDeploymentType", "documentation": "<p>Specifies the file-system deployment type. Amazon FSx for OpenZFS supports&#x2028; <code>MULTI_AZ_1</code>, <code>SINGLE_AZ_HA_2</code>, <code>SINGLE_AZ_HA_1</code>, <code>SINGLE_AZ_2</code>, and <code>SINGLE_AZ_1</code>.</p>"}, "ThroughputCapacity": {"shape": "MegabytesPerSecond", "documentation": "<p>The throughput of an Amazon FSx file system, measured in megabytes per second (MBps).</p>"}, "WeeklyMaintenanceStartTime": {"shape": "WeeklyTime"}, "DiskIopsConfiguration": {"shape": "DiskIopsConfiguration"}, "RootVolumeId": {"shape": "VolumeId", "documentation": "<p>The ID of the root volume of the OpenZFS file system. </p>"}, "PreferredSubnetId": {"shape": "SubnetId", "documentation": "<p>Required when <code>DeploymentType</code> is set to <code>MULTI_AZ_1</code>. This specifies the subnet in which you want the preferred file server to be located.</p>"}, "EndpointIpAddressRange": {"shape": "IpAddressRange", "documentation": "<p>(Multi-AZ only) Specifies the IP address range in which the endpoints to access your file system will be created. By default in the Amazon FSx API and Amazon FSx console, Amazon FSx selects an available /28 IP address range for you from one of the VPC's CIDR ranges. You can have overlapping endpoint IP addresses for file systems deployed in the same VPC/route tables.</p>"}, "RouteTableIds": {"shape": "RouteTableIds", "documentation": "<p>(Multi-AZ only) The VPC route tables in which your file system's endpoints are created.</p>"}, "EndpointIpAddress": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The IP address of the endpoint that is used to access data or to manage the file system.</p>"}, "ReadCacheConfiguration": {"shape": "OpenZFSReadCacheConfiguration", "documentation": "<p> Required when <code>StorageType</code> is set to <code>INTELLIGENT_TIERING</code>. Specifies the optional provisioned SSD read cache. </p>"}}, "documentation": "<p>The configuration for the Amazon FSx for OpenZFS file system. </p>"}, "OpenZFSFileSystemIdentity": {"type": "structure", "required": ["Type"], "members": {"Type": {"shape": "OpenZFSFileSystemUserType", "documentation": "<p>Specifies the FSx for OpenZFS user identity type, accepts only <code>POSIX</code>.</p>"}, "PosixUser": {"shape": "OpenZFSPosixFileSystemUser", "documentation": "<p>Specifies the UID and GIDs of the file system POSIX user.</p>"}}, "documentation": "<p>Specifies the file system user identity that will be used for authorizing all file access requests that are made using the S3 access point.</p>"}, "OpenZFSFileSystemUserType": {"type": "string", "enum": ["POSIX"]}, "OpenZFSNfsExport": {"type": "structure", "required": ["ClientConfigurations"], "members": {"ClientConfigurations": {"shape": "OpenZFSClientConfigurations", "documentation": "<p>A list of configuration objects that contain the client and options for mounting the OpenZFS file system. </p>"}}, "documentation": "<p>The Network File System (NFS) configurations for mounting an Amazon FSx for OpenZFS file system. </p>"}, "OpenZFSNfsExportOption": {"type": "string", "max": 128, "min": 1, "pattern": "^[ -~]{1,128}$"}, "OpenZFSNfsExportOptions": {"type": "list", "member": {"shape": "OpenZFSNfsExportOption"}, "max": 20, "min": 1}, "OpenZFSNfsExports": {"type": "list", "member": {"shape": "OpenZFSNfsExport"}, "max": 1}, "OpenZFSOriginSnapshotConfiguration": {"type": "structure", "members": {"SnapshotARN": {"shape": "ResourceARN"}, "CopyStrategy": {"shape": "OpenZFSCopyStrategy", "documentation": "<p>The strategy used when copying data from the snapshot to the new volume. </p> <ul> <li> <p> <code>CLONE</code> - The new volume references the data in the origin snapshot. Cloning a snapshot is faster than copying the data from a snapshot to a new volume and doesn't consume disk throughput. However, the origin snapshot can't be deleted if there is a volume using its copied data. </p> </li> <li> <p> <code>FULL_COPY</code> - Copies all data from the snapshot to the new volume.</p> </li> </ul> <note> <p>The <code>INCREMENTAL_COPY</code> option is only for updating an existing volume by using a snapshot from another FSx for OpenZFS file system. For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/APIReference/API_CopySnapshotAndUpdateVolume.html\">CopySnapshotAndUpdateVolume</a>.</p> </note>"}}, "documentation": "<p>The snapshot configuration used when creating an Amazon FSx for OpenZFS volume from a snapshot.</p>"}, "OpenZFSPosixFileSystemUser": {"type": "structure", "required": ["<PERSON><PERSON>", "Gid"], "members": {"Uid": {"shape": "FileSystemUID", "documentation": "<p>The UID of the file system user.</p>"}, "Gid": {"shape": "FileSystemGID", "documentation": "<p>The GID of the file system user.</p>"}, "SecondaryGids": {"shape": "FileSystemSecondaryGIDs", "documentation": "<p>The list of secondary GIDs for the file system user. </p>"}}, "documentation": "<p>The FSx for OpenZFS file system user that is used for authorizing all file access requests that are made using the S3 access point.</p>"}, "OpenZFSQuotaType": {"type": "string", "enum": ["USER", "GROUP"]}, "OpenZFSReadCacheConfiguration": {"type": "structure", "members": {"SizingMode": {"shape": "OpenZFSReadCacheSizingMode", "documentation": "<p> Specifies how the provisioned SSD read cache is sized, as follows: </p> <ul> <li> <p>Set to <code>NO_CACHE</code> if you do not want to use an SSD read cache with your Intelligent-Tiering file system.</p> </li> <li> <p>Set to <code>USER_PROVISIONED</code> to specify the exact size of your SSD read cache.</p> </li> <li> <p>Set to <code>PROPORTIONAL_TO_THROUGHPUT_CAPACITY</code> to have your SSD read cache automatically sized based on your throughput capacity.</p> </li> </ul>"}, "SizeGiB": {"shape": "StorageCapacity", "documentation": "<p> Required if <code>SizingMode</code> is set to <code>USER_PROVISIONED</code>. Specifies the size of the file system's SSD read cache, in gibibytes (GiB). </p>"}}, "documentation": "<p> The configuration for the optional provisioned SSD read cache on Amazon FSx for OpenZFS file systems that use the Intelligent-Tiering storage class. </p>"}, "OpenZFSReadCacheSizingMode": {"type": "string", "enum": ["NO_CACHE", "USER_PROVISIONED", "PROPORTIONAL_TO_THROUGHPUT_CAPACITY"]}, "OpenZFSUserAndGroupQuotas": {"type": "list", "member": {"shape": "OpenZFSUserOrGroupQuota"}, "max": 500}, "OpenZFSUserOrGroupQuota": {"type": "structure", "required": ["Type", "Id", "StorageCapacityQuotaGiB"], "members": {"Type": {"shape": "OpenZFSQuotaType", "documentation": "<p>Specifies whether the quota applies to a user or group.</p>"}, "Id": {"shape": "IntegerNoMax", "documentation": "<p>The ID of the user or group that the quota applies to.</p>"}, "StorageCapacityQuotaGiB": {"shape": "IntegerNoMax", "documentation": "<p>The user or group's storage quota, in gibibytes (GiB).</p>"}}, "documentation": "<p>Used to configure quotas that define how much storage a user or group can use on an FSx for OpenZFS volume. For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/OpenZFSGuide/managing-volumes.html#volume-properties\">Volume properties</a> in the FSx for OpenZFS User Guide. </p>"}, "OpenZFSVolumeConfiguration": {"type": "structure", "members": {"ParentVolumeId": {"shape": "VolumeId", "documentation": "<p>The ID of the parent volume.</p>"}, "VolumePath": {"shape": "VolumePath", "documentation": "<p>The path to the volume from the root volume. For example, <code>fsx/parentVolume/volume1</code>.</p>"}, "StorageCapacityReservationGiB": {"shape": "IntegerNoMax", "documentation": "<p>The amount of storage in gibibytes (GiB) to reserve from the parent volume. You can't reserve more storage than the parent volume has reserved.</p>"}, "StorageCapacityQuotaGiB": {"shape": "IntegerNoMax", "documentation": "<p>The maximum amount of storage in gibibytes (GiB) that the volume can use from its parent. You can specify a quota larger than the storage on the parent volume.</p>"}, "RecordSizeKiB": {"shape": "IntegerRecordSizeKiB", "documentation": "<p>The record size of an OpenZFS volume, in kibibytes (KiB). Valid values are 4, 8, 16, 32, 64, 128, 256, 512, or 1024 KiB. The default is 128 KiB. Most workloads should use the default record size. For guidance on when to set a custom record size, see the <i>Amazon FSx for OpenZFS User Guide</i>.</p>"}, "DataCompressionType": {"shape": "OpenZFSDataCompressionType", "documentation": "<p>Specifies the method used to compress the data on the volume. The compression type is <code>NONE</code> by default.</p> <ul> <li> <p> <code>NONE</code> - Doesn't compress the data on the volume. <code>NONE</code> is the default.</p> </li> <li> <p> <code>ZSTD</code> - Compresses the data in the volume using the Zstandard (ZSTD) compression algorithm. Compared to LZ4, Z-Standard provides a better compression ratio to minimize on-disk storage utilization.</p> </li> <li> <p> <code>LZ4</code> - Compresses the data in the volume using the LZ4 compression algorithm. Compared to Z-Standard, LZ4 is less compute-intensive and delivers higher write throughput speeds.</p> </li> </ul>"}, "CopyTagsToSnapshots": {"shape": "Flag", "documentation": "<p>A Boolean value indicating whether tags for the volume should be copied to snapshots. This value defaults to <code>false</code>. If it's set to <code>true</code>, all tags for the volume are copied to snapshots where the user doesn't specify tags. If this value is <code>true</code> and you specify one or more tags, only the specified tags are copied to snapshots. If you specify one or more tags when creating the snapshot, no tags are copied from the volume, regardless of this value.</p>"}, "OriginSnapshot": {"shape": "OpenZFSOriginSnapshotConfiguration", "documentation": "<p>The configuration object that specifies the snapshot to use as the origin of the data for the volume.</p>"}, "ReadOnly": {"shape": "Read<PERSON>nly", "documentation": "<p>A Boolean value indicating whether the volume is read-only.</p>"}, "NfsExports": {"shape": "OpenZFSNfsExports", "documentation": "<p>The configuration object for mounting a Network File System (NFS) file system.</p>"}, "UserAndGroupQuotas": {"shape": "OpenZFSUserAndGroupQuotas", "documentation": "<p>An object specifying how much storage users or groups can use on the volume.</p>"}, "RestoreToSnapshot": {"shape": "SnapshotId", "documentation": "<p>Specifies the ID of the snapshot to which the volume was restored.</p>"}, "DeleteIntermediateSnaphots": {"shape": "Flag", "documentation": "<p>A Boolean value indicating whether snapshots between the current state and the specified snapshot should be deleted when a volume is restored from snapshot.</p>"}, "DeleteClonedVolumes": {"shape": "Flag", "documentation": "<p>A Boolean value indicating whether dependent clone volumes created from intermediate snapshots should be deleted when a volume is restored from snapshot.</p>"}, "DeleteIntermediateData": {"shape": "Flag", "documentation": "<p>A Boolean value indicating whether snapshot data that differs between the current state and the specified snapshot should be overwritten when a volume is restored from a snapshot.</p>"}, "SourceSnapshotARN": {"shape": "ResourceARN"}, "DestinationSnapshot": {"shape": "SnapshotId", "documentation": "<p>The ID of the snapshot that's being copied or was most recently copied to the destination volume.</p>"}, "CopyStrategy": {"shape": "OpenZFSCopyStrategy", "documentation": "<p>Specifies the strategy used when copying data from the snapshot to the new volume. </p> <ul> <li> <p> <code>CLONE</code> - The new volume references the data in the origin snapshot. Cloning a snapshot is faster than copying data from the snapshot to a new volume and doesn't consume disk throughput. However, the origin snapshot can't be deleted if there is a volume using its copied data.</p> </li> <li> <p> <code>FULL_COPY</code> - Copies all data from the snapshot to the new volume.</p> <p>Specify this option to create the volume from a snapshot on another FSx for OpenZFS file system.</p> </li> </ul> <note> <p>The <code>INCREMENTAL_COPY</code> option is only for updating an existing volume by using a snapshot from another FSx for OpenZFS file system. For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/APIReference/API_CopySnapshotAndUpdateVolume.html\">CopySnapshotAndUpdateVolume</a>.</p> </note>"}}, "documentation": "<p>The configuration of an Amazon FSx for OpenZFS volume.</p>"}, "OrganizationalUnitDistinguishedName": {"type": "string", "max": 2000, "min": 1, "pattern": "^[^\\u0000\\u0085\\u2028\\u2029\\r\\n]{1,2000}$"}, "Parameter": {"type": "string", "documentation": "<p>The name of a parameter for the request. Parameter names are returned in PascalCase.</p>", "min": 1}, "PerUnitStorageThroughput": {"type": "integer", "max": 1000, "min": 12}, "PrivilegedDelete": {"type": "string", "enum": ["DISABLED", "ENABLED", "PERMANENTLY_DISABLED"]}, "ProgressPercent": {"type": "integer", "documentation": "<p>Displays the current percent of progress of an asynchronous task.</p>", "max": 100, "min": 0}, "ReadOnly": {"type": "boolean"}, "Region": {"type": "string", "max": 20, "min": 1, "pattern": "^[a-z0-9-]{1,20}$"}, "ReleaseConfiguration": {"type": "structure", "members": {"DurationSinceLastAccess": {"shape": "DurationSinceLastAccess", "documentation": "<p>Defines the point-in-time since an exported file was last accessed, in order for that file to be eligible for release. Only files that were last accessed before this point-in-time are eligible to be released from the file system.</p>"}}, "documentation": "<p>The configuration that specifies a minimum amount of time since last access for an exported file to be eligible for release from an Amazon FSx for Lustre file system. Only files that were last accessed before this point-in-time can be released. For example, if you specify a last accessed time criteria of 9 days, only files that were last accessed 9.00001 or more days ago can be released.</p> <p>Only file data that has been exported to S3 can be released. Files that have not yet been exported to S3, such as new or changed files that have not been exported, are not eligible for release. When files are released, their metadata stays on the file system, so they can still be accessed later. Users and applications can access a released file by reading the file again, which restores data from Amazon S3 to the FSx for Lustre file system.</p> <note> <p>If a file meets the last accessed time criteria, its file or directory path must also be specified with the <code>Paths</code> parameter of the operation in order for the file to be released.</p> </note>"}, "ReleaseFileSystemNfsV3LocksRequest": {"type": "structure", "required": ["FileSystemId"], "members": {"FileSystemId": {"shape": "FileSystemId"}, "ClientRequestToken": {"shape": "ClientRequestToken", "idempotencyToken": true}}}, "ReleaseFileSystemNfsV3LocksResponse": {"type": "structure", "members": {"FileSystem": {"shape": "FileSystem"}}}, "ReleasedCapacity": {"type": "long"}, "RemainingTransferBytes": {"type": "long", "min": 0}, "ReportFormat": {"type": "string", "enum": ["REPORT_CSV_20191124"]}, "ReportScope": {"type": "string", "enum": ["FAILED_FILES_ONLY"]}, "RepositoryDnsIps": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "max": 10}, "RequestTime": {"type": "timestamp"}, "ResourceARN": {"type": "string", "documentation": "<p>The Amazon Resource Name (ARN) for a given resource. ARNs uniquely identify Amazon Web Services resources. We require an ARN when you need to specify a resource unambiguously across all of Amazon Web Services. For more information, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs)</a> in the <i>Amazon Web Services General Reference</i>.</p>", "max": 512, "min": 8, "pattern": "^arn:(?=[^:]+:fsx:[^:]+:\\d{12}:)((|(?=[a-z0-9-.]{1,63})(?!\\d{1,3}(\\.\\d{1,3}){3})(?![^:]*-{2})(?![^:]*-\\.)(?![^:]*\\.-)[a-z0-9].*(?<!-)):){4}(?!/).{0,1024}$"}, "ResourceDoesNotSupportTagging": {"type": "structure", "required": ["ResourceARN"], "members": {"ResourceARN": {"shape": "ResourceARN", "documentation": "<p>The Amazon Resource Name (ARN) of the resource that doesn't support tagging.</p>"}, "Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The resource specified does not support tagging. </p>", "exception": true}, "ResourceNotFound": {"type": "structure", "required": ["ResourceARN"], "members": {"ResourceARN": {"shape": "ResourceARN", "documentation": "<p>The resource ARN of the resource that can't be found.</p>"}, "Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The resource specified by the Amazon Resource Name (ARN) can't be found.</p>", "exception": true}, "ResourceType": {"type": "string", "enum": ["FILE_SYSTEM", "VOLUME"]}, "RestoreOpenZFSVolumeOption": {"type": "string", "enum": ["DELETE_INTERMEDIATE_SNAPSHOTS", "DELETE_CLONED_VOLUMES"]}, "RestoreOpenZFSVolumeOptions": {"type": "list", "member": {"shape": "RestoreOpenZFSVolumeOption"}, "max": 2}, "RestoreVolumeFromSnapshotRequest": {"type": "structure", "required": ["VolumeId", "SnapshotId"], "members": {"ClientRequestToken": {"shape": "ClientRequestToken", "idempotencyToken": true}, "VolumeId": {"shape": "VolumeId", "documentation": "<p>The ID of the volume that you are restoring.</p>"}, "SnapshotId": {"shape": "SnapshotId", "documentation": "<p>The ID of the source snapshot. Specifies the snapshot that you are restoring from.</p>"}, "Options": {"shape": "RestoreOpenZFSVolumeOptions", "documentation": "<p>The settings used when restoring the specified volume from snapshot.</p> <ul> <li> <p> <code>DELETE_INTERMEDIATE_SNAPSHOTS</code> - Deletes snapshots between the current state and the specified snapshot. If there are intermediate snapshots and this option isn't used, <code>RestoreVolumeFromSnapshot</code> fails.</p> </li> <li> <p> <code>DELETE_CLONED_VOLUMES</code> - Deletes any dependent clone volumes created from intermediate snapshots. If there are any dependent clone volumes and this option isn't used, <code>RestoreVolumeFromSnapshot</code> fails.</p> </li> </ul>"}}}, "RestoreVolumeFromSnapshotResponse": {"type": "structure", "members": {"VolumeId": {"shape": "VolumeId", "documentation": "<p>The ID of the volume that you restored.</p>"}, "Lifecycle": {"shape": "VolumeLifecycle", "documentation": "<p>The lifecycle state of the volume being restored.</p>"}, "AdministrativeActions": {"shape": "AdministrativeActions", "documentation": "<p>A list of administrative actions for the file system that are in process or waiting to be processed. Administrative actions describe changes to the Amazon FSx system.</p>"}}}, "RetentionPeriod": {"type": "structure", "required": ["Type"], "members": {"Type": {"shape": "RetentionPeriodType", "documentation": "<p>Defines the type of time for the retention period of an FSx for ONTAP SnapLock volume. Set it to one of the valid types. If you set it to <code>INFINITE</code>, the files are retained forever. If you set it to <code>UNSPECIFIED</code>, the files are retained until you set an explicit retention period. </p>"}, "Value": {"shape": "RetentionPeriodValue", "documentation": "<p>Defines the amount of time for the retention period of an FSx for ONTAP SnapLock volume. You can't set a value for <code>INFINITE</code> or <code>UNSPECIFIED</code>. For all other options, the following ranges are valid: </p> <ul> <li> <p> <code>Seconds</code>: 0 - 65,535</p> </li> <li> <p> <code>Minutes</code>: 0 - 65,535</p> </li> <li> <p> <code>Hours</code>: 0 - 24</p> </li> <li> <p> <code>Days</code>: 0 - 365</p> </li> <li> <p> <code>Months</code>: 0 - 12</p> </li> <li> <p> <code>Years</code>: 0 - 100</p> </li> </ul>"}}, "documentation": "<p>Specifies the retention period of an FSx for ONTAP SnapLock volume. After it is set, it can't be changed. Files can't be deleted or modified during the retention period. </p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/snaplock-retention.html\">Working with the retention period in SnapLock</a>. </p>"}, "RetentionPeriodType": {"type": "string", "enum": ["SECONDS", "MINUTES", "HOURS", "DAYS", "MONTHS", "YEARS", "INFINITE", "UNSPECIFIED"]}, "RetentionPeriodValue": {"type": "integer", "max": 65535, "min": 0}, "RouteTableId": {"type": "string", "max": 21, "min": 12, "pattern": "^(rtb-[0-9a-f]{8,})$"}, "RouteTableIds": {"type": "list", "member": {"shape": "RouteTableId"}, "max": 50}, "S3AccessPoint": {"type": "structure", "members": {"ResourceARN": {"shape": "GeneralARN", "documentation": "<p>he S3 access point's ARN.</p>"}, "Alias": {"shape": "S3AccessPointAlias", "documentation": "<p>The S3 access point's alias.</p>"}, "VpcConfiguration": {"shape": "S3AccessPointVpcConfiguration", "documentation": "<p>The S3 access point's virtual private cloud (VPC) configuration.</p>"}}, "documentation": "<p>Describes the S3 access point configuration of the S3 access point attachment.</p>"}, "S3AccessPointAlias": {"type": "string", "max": 63, "min": 1, "pattern": "^[0-9a-z\\\\-]{1,63}"}, "S3AccessPointAttachment": {"type": "structure", "members": {"Lifecycle": {"shape": "S3AccessPointAttachmentLifecycle", "documentation": "<p>The lifecycle status of the S3 access point attachment. The lifecycle can have the following values:</p> <ul> <li> <p>AVAILABLE - the S3 access point attachment is available for use</p> </li> <li> <p>CREATING - Amazon FSx is creating the S3 access point and attachment</p> </li> <li> <p>DELETING - Amazon FSx is deleting the S3 access point and attachment</p> </li> <li> <p>FAILED - The S3 access point attachment is in a failed state. Delete and detach the S3 access point attachment, and create a new one.</p> </li> <li> <p>UPDATING - Amazon FSx is updating the S3 access point attachment</p> </li> </ul>"}, "LifecycleTransitionReason": {"shape": "LifecycleTransitionReason"}, "CreationTime": {"shape": "CreationTime"}, "Name": {"shape": "S3AccessPointAttachmentName", "documentation": "<p>The name of the S3 access point attachment; also used for the name of the S3 access point.</p>"}, "Type": {"shape": "S3AccessPointAttachmentType", "documentation": "<p>The type of Amazon FSx volume that the S3 access point is attached to. </p>"}, "OpenZFSConfiguration": {"shape": "S3AccessPointOpenZFSConfiguration", "documentation": "<p>The OpenZFSConfiguration of the S3 access point attachment.</p>"}, "S3AccessPoint": {"shape": "S3AccessPoint", "documentation": "<p>The S3 access point configuration of the S3 access point attachment.</p>"}}, "documentation": "<p>An S3 access point attached to an Amazon FSx volume.</p>"}, "S3AccessPointAttachmentLifecycle": {"type": "string", "enum": ["AVAILABLE", "CREATING", "DELETING", "UPDATING", "FAILED"]}, "S3AccessPointAttachmentName": {"type": "string", "max": 50, "min": 3, "pattern": "^(?=[a-z0-9])[a-z0-9-]{1,48}[a-z0-9]$"}, "S3AccessPointAttachmentNames": {"type": "list", "member": {"shape": "S3AccessPointAttachmentName"}, "max": 50}, "S3AccessPointAttachmentNotFound": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The access point specified was not found.</p>", "exception": true}, "S3AccessPointAttachmentType": {"type": "string", "enum": ["OPENZFS"]}, "S3AccessPointAttachments": {"type": "list", "member": {"shape": "S3AccessPointAttachment"}, "max": 1000}, "S3AccessPointAttachmentsFilter": {"type": "structure", "members": {"Name": {"shape": "S3AccessPointAttachmentsFilterName", "documentation": "<p>The name of the filter.</p>"}, "Values": {"shape": "S3AccessPointAttachmentsFilterValues", "documentation": "<p>The values of the filter.</p>"}}, "documentation": "<p>A set of Name and Values pairs used to view a select set of S3 access point attachments.</p>"}, "S3AccessPointAttachmentsFilterName": {"type": "string", "enum": ["file-system-id", "volume-id", "type"]}, "S3AccessPointAttachmentsFilterValue": {"type": "string", "max": 128, "min": 1, "pattern": "^[0-9a-zA-Z\\*\\.\\\\/\\?\\-\\_]*$"}, "S3AccessPointAttachmentsFilterValues": {"type": "list", "member": {"shape": "S3AccessPointAttachmentsFilterValue"}, "max": 20}, "S3AccessPointAttachmentsFilters": {"type": "list", "member": {"shape": "S3AccessPointAttachmentsFilter"}, "max": 2}, "S3AccessPointOpenZFSConfiguration": {"type": "structure", "members": {"VolumeId": {"shape": "VolumeId", "documentation": "<p>The ID of the FSx for OpenZFS volume that the S3 access point is attached to.</p>"}, "FileSystemIdentity": {"shape": "OpenZFSFileSystemIdentity", "documentation": "<p>The file system identity used to authorize file access requests made using the S3 access point.</p>"}}, "documentation": "<p>Describes the FSx for OpenZFS attachment configuration of an S3 access point attachment.</p>"}, "S3AccessPointVpcConfiguration": {"type": "structure", "members": {"VpcId": {"shape": "VpcId", "documentation": "<p>Specifies the virtual private cloud (VPC) for the S3 access point VPC configuration, if one exists.</p>"}}, "documentation": "<p>If included, Amazon S3 restricts access to this access point to requests from the specified virtual private cloud (VPC).</p>"}, "S3DataRepositoryConfiguration": {"type": "structure", "members": {"AutoImportPolicy": {"shape": "AutoImportPolicy", "documentation": "<p>Specifies the type of updated objects (new, changed, deleted) that will be automatically imported from the linked S3 bucket to your file system.</p>"}, "AutoExportPolicy": {"shape": "AutoExportPolicy", "documentation": "<p>Specifies the type of updated objects (new, changed, deleted) that will be automatically exported from your file system to the linked S3 bucket.</p>"}}, "documentation": "<p>The configuration for an Amazon S3 data repository linked to an Amazon FSx for Lustre file system with a data repository association. The configuration consists of an <code>AutoImportPolicy</code> that defines which file events on the data repository are automatically imported to the file system and an <code>AutoExportPolicy</code> that defines which file events on the file system are automatically exported to the data repository. File events are when files or directories are added, changed, or deleted on the file system or the data repository.</p> <note> <p>Data repository associations on Amazon File Cache don't use <code>S3DataRepositoryConfiguration</code> because they don't support automatic import or automatic export.</p> </note>"}, "SecurityGroupId": {"type": "string", "documentation": "<p>The ID of your Amazon EC2 security group. This ID is used to control network access to the endpoint that Amazon FSx creates on your behalf in each subnet. For more information, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/using-network-security.html\">Amazon EC2 Security groups for Linux instances</a> in the <i>Amazon EC2 User Guide</i>.</p>", "max": 20, "min": 11, "pattern": "^(sg-[0-9a-f]{8,})$"}, "SecurityGroupIds": {"type": "list", "member": {"shape": "SecurityGroupId"}, "documentation": "<p>A list of IDs specifying the security groups to apply to all network interfaces created for file system access. This list isn't returned in later requests to describe the file system.</p>", "max": 50}, "SecurityStyle": {"type": "string", "enum": ["UNIX", "NTFS", "MIXED"]}, "SelfManagedActiveDirectoryAttributes": {"type": "structure", "members": {"DomainName": {"shape": "ActiveDirectoryFullyQualifiedName", "documentation": "<p>The fully qualified domain name of the self-managed AD directory.</p>"}, "OrganizationalUnitDistinguishedName": {"shape": "OrganizationalUnitDistinguishedName", "documentation": "<p>The fully qualified distinguished name of the organizational unit within the self-managed AD directory to which the Windows File Server or ONTAP storage virtual machine (SVM) instance is joined.</p>"}, "FileSystemAdministratorsGroup": {"shape": "FileSystemAdministratorsGroupName", "documentation": "<p>The name of the domain group whose members have administrative privileges for the FSx file system.</p>"}, "UserName": {"shape": "DirectoryUserName", "documentation": "<p>The user name for the service account on your self-managed AD domain that FSx uses to join to your AD domain.</p>"}, "DnsIps": {"shape": "DnsIps", "documentation": "<p>A list of up to three IP addresses of DNS servers or domain controllers in the self-managed AD directory.</p>"}}, "documentation": "<p>The configuration of the self-managed Microsoft Active Directory (AD) directory to which the Windows File Server or ONTAP storage virtual machine (SVM) instance is joined.</p>"}, "SelfManagedActiveDirectoryConfiguration": {"type": "structure", "required": ["DomainName", "UserName", "Password", "DnsIps"], "members": {"DomainName": {"shape": "ActiveDirectoryFullyQualifiedName", "documentation": "<p>The fully qualified domain name of the self-managed AD directory, such as <code>corp.example.com</code>.</p>"}, "OrganizationalUnitDistinguishedName": {"shape": "OrganizationalUnitDistinguishedName", "documentation": "<p>(Optional) The fully qualified distinguished name of the organizational unit within your self-managed AD directory. Amazon FSx only accepts OU as the direct parent of the file system. An example is <code>OU=FSx,DC=yourdomain,DC=corp,DC=com</code>. To learn more, see <a href=\"https://tools.ietf.org/html/rfc2253\">RFC 2253</a>. If none is provided, the FSx file system is created in the default location of your self-managed AD directory. </p> <important> <p>Only Organizational Unit (OU) objects can be the direct parent of the file system that you're creating.</p> </important>"}, "FileSystemAdministratorsGroup": {"shape": "FileSystemAdministratorsGroupName", "documentation": "<p>(Optional) The name of the domain group whose members are granted administrative privileges for the file system. Administrative privileges include taking ownership of files and folders, setting audit controls (audit ACLs) on files and folders, and administering the file system remotely by using the FSx Remote PowerShell. The group that you specify must already exist in your domain. If you don't provide one, your AD domain's Domain Admins group is used.</p>"}, "UserName": {"shape": "DirectoryUserName", "documentation": "<p>The user name for the service account on your self-managed AD domain that Amazon FSx will use to join to your AD domain. This account must have the permission to join computers to the domain in the organizational unit provided in <code>OrganizationalUnitDistinguishedName</code>, or in the default location of your AD domain.</p>"}, "Password": {"shape": "DirectoryPassword", "documentation": "<p>The password for the service account on your self-managed AD domain that Amazon FSx will use to join to your AD domain.</p>"}, "DnsIps": {"shape": "DnsIps", "documentation": "<p>A list of up to three IP addresses of DNS servers or domain controllers in the self-managed AD directory. </p>"}}, "documentation": "<p>The configuration that Amazon FSx uses to join a FSx for Windows File Server file system or an FSx for ONTAP storage virtual machine (SVM) to a self-managed (including on-premises) Microsoft Active Directory (AD) directory. For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/WindowsGuide/self-managed-AD.html\"> Using Amazon FSx for Windows with your self-managed Microsoft Active Directory</a> or <a href=\"https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/managing-svms.html\">Managing FSx for ONTAP SVMs</a>.</p>"}, "SelfManagedActiveDirectoryConfigurationUpdates": {"type": "structure", "members": {"UserName": {"shape": "DirectoryUserName", "documentation": "<p>Specifies the updated user name for the service account on your self-managed Active Directory domain. Amazon FSx uses this account to join to your self-managed Active Directory domain.</p> <p>This account must have the permissions required to join computers to the domain in the organizational unit provided in <code>OrganizationalUnitDistinguishedName</code>.</p>"}, "Password": {"shape": "DirectoryPassword", "documentation": "<p>Specifies the updated password for the service account on your self-managed Active Directory domain. Amazon FSx uses this account to join to your self-managed Active Directory domain.</p>"}, "DnsIps": {"shape": "DnsIps", "documentation": "<p>A list of up to three DNS server or domain controller IP addresses in your self-managed Active Directory domain.</p>"}, "DomainName": {"shape": "ActiveDirectoryFullyQualifiedName", "documentation": "<p>Specifies an updated fully qualified domain name of your self-managed Active Directory configuration.</p>"}, "OrganizationalUnitDistinguishedName": {"shape": "OrganizationalUnitDistinguishedName", "documentation": "<p>Specifies an updated fully qualified distinguished name of the organization unit within your self-managed Active Directory.</p>"}, "FileSystemAdministratorsGroup": {"shape": "FileSystemAdministratorsGroupName", "documentation": "<p>For FSx for ONTAP file systems only - Specifies the updated name of the self-managed Active Directory domain group whose members are granted administrative privileges for the Amazon FSx resource.</p>"}}, "documentation": "<p>Specifies changes you are making to the self-managed Microsoft Active Directory configuration to which an FSx for Windows File Server file system or an FSx for ONTAP SVM is joined.</p>"}, "ServiceLimit": {"type": "string", "documentation": "<p>The types of limits on your service utilization. Limits include file system count, total throughput capacity, total storage, and total user-initiated backups. These limits apply for a specific account in a specific Amazon Web Services Region. You can increase some of them by contacting Amazon Web ServicesSupport.</p>", "enum": ["FILE_SYSTEM_COUNT", "TOTAL_THROUGHPUT_CAPACITY", "TOTAL_STORAGE", "TOTAL_USER_INITIATED_BACKUPS", "TOTAL_USER_TAGS", "TOTAL_IN_PROGRESS_COPY_BACKUPS", "STORAGE_VIRTUAL_MACHINES_PER_FILE_SYSTEM", "VOLUMES_PER_FILE_SYSTEM", "TOTAL_SSD_IOPS", "FILE_CACHE_COUNT"]}, "ServiceLimitExceeded": {"type": "structure", "required": ["Limit"], "members": {"Limit": {"shape": "ServiceLimit", "documentation": "<p>Enumeration of the service limit that was exceeded. </p>"}, "Message": {"shape": "ErrorMessage"}}, "documentation": "<p>An error indicating that a particular service limit was exceeded. You can increase some service limits by contacting Amazon Web ServicesSupport.</p>", "exception": true}, "SizeInBytes": {"type": "long", "min": 0}, "SnaplockConfiguration": {"type": "structure", "members": {"AuditLogVolume": {"shape": "Flag", "documentation": "<p>Enables or disables the audit log volume for an FSx for ONTAP SnapLock volume. The default value is <code>false</code>. If you set <code>AuditLogVolume</code> to <code>true</code>, the SnapLock volume is created as an audit log volume. The minimum retention period for an audit log volume is six months. </p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/how-snaplock-works.html#snaplock-audit-log-volume\"> SnapLock audit log volumes</a>. </p>"}, "AutocommitPeriod": {"shape": "AutocommitPeriod", "documentation": "<p>The configuration object for setting the autocommit period of files in an FSx for ONTAP SnapLock volume. </p>"}, "PrivilegedDelete": {"shape": "PrivilegedDelete", "documentation": "<p>Enables, disables, or permanently disables privileged delete on an FSx for ONTAP SnapLock Enterprise volume. Enabling privileged delete allows SnapLock administrators to delete write once, read many (WORM) files even if they have active retention periods. <code>PERMANENTLY_DISABLED</code> is a terminal state. If privileged delete is permanently disabled on a SnapLock volume, you can't re-enable it. The default value is <code>DISABLED</code>. </p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/snaplock-enterprise.html#privileged-delete\">Privileged delete</a>. </p>"}, "RetentionPeriod": {"shape": "SnaplockRetentionPeriod", "documentation": "<p>Specifies the retention period of an FSx for ONTAP SnapLock volume. </p>"}, "SnaplockType": {"shape": "SnaplockType", "documentation": "<p>Specifies the retention mode of an FSx for ONTAP SnapLock volume. After it is set, it can't be changed. You can choose one of the following retention modes: </p> <ul> <li> <p> <code>COMPLIANCE</code>: Files transitioned to write once, read many (WORM) on a Compliance volume can't be deleted until their retention periods expire. This retention mode is used to address government or industry-specific mandates or to protect against ransomware attacks. For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/snaplock-compliance.html\">SnapLock Compliance</a>. </p> </li> <li> <p> <code>ENTERPRISE</code>: Files transitioned to WORM on an Enterprise volume can be deleted by authorized users before their retention periods expire using privileged delete. This retention mode is used to advance an organization's data integrity and internal compliance or to test retention settings before using SnapLock Compliance. For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/snaplock-enterprise.html\">SnapLock Enterprise</a>. </p> </li> </ul>"}, "VolumeAppendModeEnabled": {"shape": "Flag", "documentation": "<p>Enables or disables volume-append mode on an FSx for ONTAP SnapLock volume. Volume-append mode allows you to create WORM-appendable files and write data to them incrementally. The default value is <code>false</code>. </p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/worm-state.html#worm-state-append\">Volume-append mode</a>. </p>"}}, "documentation": "<p>Specifies the SnapLock configuration for an FSx for ONTAP SnapLock volume. </p>"}, "SnaplockRetentionPeriod": {"type": "structure", "required": ["DefaultRetention", "MinimumRetention", "MaximumRetention"], "members": {"DefaultRetention": {"shape": "RetentionPeriod", "documentation": "<p>The retention period assigned to a write once, read many (WORM) file by default if an explicit retention period is not set for an FSx for ONTAP SnapLock volume. The default retention period must be greater than or equal to the minimum retention period and less than or equal to the maximum retention period. </p>"}, "MinimumRetention": {"shape": "RetentionPeriod", "documentation": "<p>The shortest retention period that can be assigned to a WORM file on an FSx for ONTAP SnapLock volume. </p>"}, "MaximumRetention": {"shape": "RetentionPeriod", "documentation": "<p>The longest retention period that can be assigned to a WORM file on an FSx for ONTAP SnapLock volume. </p>"}}, "documentation": "<p>The configuration to set the retention period of an FSx for ONTAP SnapLock volume. The retention period includes default, maximum, and minimum settings. For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/snaplock-retention.html\">Working with the retention period in SnapLock</a>. </p>"}, "SnaplockType": {"type": "string", "enum": ["COMPLIANCE", "ENTERPRISE"]}, "Snapshot": {"type": "structure", "members": {"ResourceARN": {"shape": "ResourceARN"}, "SnapshotId": {"shape": "SnapshotId", "documentation": "<p>The ID of the snapshot.</p>"}, "Name": {"shape": "SnapshotName", "documentation": "<p>The name of the snapshot.</p>"}, "VolumeId": {"shape": "VolumeId", "documentation": "<p>The ID of the volume that the snapshot is of.</p>"}, "CreationTime": {"shape": "CreationTime"}, "Lifecycle": {"shape": "SnapshotLifecycle", "documentation": "<p>The lifecycle status of the snapshot.</p> <ul> <li> <p> <code>PENDING</code> - Amazon FSx hasn't started creating the snapshot.</p> </li> <li> <p> <code>CREATING</code> - Amazon FSx is creating the snapshot.</p> </li> <li> <p> <code>DELETING</code> - Amazon FSx is deleting the snapshot.</p> </li> <li> <p> <code>AVAILABLE</code> - The snapshot is fully available.</p> </li> </ul>"}, "LifecycleTransitionReason": {"shape": "LifecycleTransitionReason"}, "Tags": {"shape": "Tags"}, "AdministrativeActions": {"shape": "AdministrativeActions", "documentation": "<p>A list of administrative actions for the file system that are in process or waiting to be processed. Administrative actions describe changes to the Amazon FSx system.</p>"}}, "documentation": "<p>A snapshot of an Amazon FSx for OpenZFS volume.</p>"}, "SnapshotFilter": {"type": "structure", "members": {"Name": {"shape": "SnapshotFilterName", "documentation": "<p>The name of the filter to use. You can filter by the <code>file-system-id</code> or by <code>volume-id</code>.</p>"}, "Values": {"shape": "SnapshotFilterValues", "documentation": "<p>The <code>file-system-id</code> or <code>volume-id</code> that you are filtering for.</p>"}}, "documentation": "<p>A filter used to restrict the results of <code>DescribeSnapshots</code> calls. You can use multiple filters to return results that meet all applied filter requirements.</p>"}, "SnapshotFilterName": {"type": "string", "enum": ["file-system-id", "volume-id"]}, "SnapshotFilterValue": {"type": "string", "max": 128, "min": 1, "pattern": "^[0-9a-zA-Z\\*\\.\\\\/\\?\\-\\_]*$"}, "SnapshotFilterValues": {"type": "list", "member": {"shape": "SnapshotFilterValue"}, "max": 20}, "SnapshotFilters": {"type": "list", "member": {"shape": "SnapshotFilter"}, "max": 2}, "SnapshotId": {"type": "string", "max": 28, "min": 11, "pattern": "^((fs)?volsnap-[0-9a-f]{8,})$"}, "SnapshotIds": {"type": "list", "member": {"shape": "SnapshotId"}, "max": 50}, "SnapshotLifecycle": {"type": "string", "enum": ["PENDING", "CREATING", "DELETING", "AVAILABLE"]}, "SnapshotName": {"type": "string", "max": 203, "min": 1, "pattern": "^[a-zA-Z0-9_:.-]{1,203}$"}, "SnapshotNotFound": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>No Amazon FSx snapshots were found based on the supplied parameters.</p>", "exception": true}, "SnapshotPolicy": {"type": "string", "max": 255, "min": 1}, "Snapshots": {"type": "list", "member": {"shape": "Snapshot"}, "max": 50}, "SourceBackupId": {"type": "string", "max": 128, "min": 12, "pattern": "^(backup-[0-9a-f]{8,})$"}, "SourceBackupUnavailable": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}, "BackupId": {"shape": "BackupId"}}, "documentation": "<p>The request was rejected because the lifecycle status of the source backup isn't <code>AVAILABLE</code>.</p>", "exception": true}, "StartMisconfiguredStateRecoveryRequest": {"type": "structure", "required": ["FileSystemId"], "members": {"ClientRequestToken": {"shape": "ClientRequestToken", "idempotencyToken": true}, "FileSystemId": {"shape": "FileSystemId"}}}, "StartMisconfiguredStateRecoveryResponse": {"type": "structure", "members": {"FileSystem": {"shape": "FileSystem"}}}, "StartTime": {"type": "timestamp"}, "Status": {"type": "string", "enum": ["FAILED", "IN_PROGRESS", "PENDING", "COMPLETED", "UPDATED_OPTIMIZING", "OPTIMIZING"]}, "StorageCapacity": {"type": "integer", "documentation": "<p>Specifies the file system's storage capacity, in gibibytes (GiB).</p>", "max": 2147483647, "min": 0}, "StorageType": {"type": "string", "documentation": "<p>Specifies the file system's storage type.</p>", "enum": ["SSD", "HDD", "INTELLIGENT_TIERING"]}, "StorageVirtualMachine": {"type": "structure", "members": {"ActiveDirectoryConfiguration": {"shape": "SvmActiveDirectoryConfiguration", "documentation": "<p>Describes the Microsoft Active Directory configuration to which the SVM is joined, if applicable.</p>"}, "CreationTime": {"shape": "CreationTime"}, "Endpoints": {"shape": "SvmEndpoints", "documentation": "<p>The endpoints that are used to access data or to manage the SVM using the NetApp ONTAP CLI, REST API, or NetApp CloudManager. They are the <code>Iscsi</code>, <code>Management</code>, <code>Nfs</code>, and <code>Smb</code> endpoints.</p>"}, "FileSystemId": {"shape": "FileSystemId"}, "Lifecycle": {"shape": "StorageVirtualMachineLifecycle", "documentation": "<p>Describes the SVM's lifecycle status.</p> <ul> <li> <p> <code>CREATED</code> - The SVM is fully available for use.</p> </li> <li> <p> <code>CREATING</code> - Amazon FSx is creating the new SVM.</p> </li> <li> <p> <code>DELETING</code> - Amazon FSx is deleting an existing SVM.</p> </li> <li> <p> <code>FAILED</code> - Amazon FSx was unable to create the SVM.</p> </li> <li> <p> <code>MISCONFIGURED</code> - The SVM is in a failed but recoverable state.</p> </li> <li> <p> <code>PENDING</code> - Amazon FSx has not started creating the SVM.</p> </li> </ul>"}, "Name": {"shape": "StorageVirtualMachineName", "documentation": "<p>The name of the SVM, if provisioned.</p>"}, "ResourceARN": {"shape": "ResourceARN"}, "StorageVirtualMachineId": {"shape": "StorageVirtualMachineId", "documentation": "<p>The SVM's system generated unique ID.</p>"}, "Subtype": {"shape": "StorageVirtualMachineSubtype", "documentation": "<p>Describes the SVM's subtype.</p>"}, "UUID": {"shape": "UUID", "documentation": "<p>The SVM's UUID (universally unique identifier).</p>"}, "Tags": {"shape": "Tags"}, "LifecycleTransitionReason": {"shape": "LifecycleTransitionReason", "documentation": "<p>Describes why the SVM lifecycle state changed.</p>"}, "RootVolumeSecurityStyle": {"shape": "StorageVirtualMachineRootVolumeSecurityStyle", "documentation": "<p>The security style of the root volume of the SVM.</p>"}}, "documentation": "<p>Describes the Amazon FSx for NetApp ONTAP storage virtual machine (SVM) configuration.</p>"}, "StorageVirtualMachineFilter": {"type": "structure", "members": {"Name": {"shape": "StorageVirtualMachineFilterName", "documentation": "<p>The name for this filter.</p>"}, "Values": {"shape": "StorageVirtualMachineFilterValues", "documentation": "<p>The values of the filter. These are all the values for any of the applied filters.</p>"}}, "documentation": "<p>A filter used to restrict the results of describe calls for Amazon FSx for NetApp ONTAP storage virtual machines (SVMs). You can use multiple filters to return results that meet all applied filter requirements.</p>"}, "StorageVirtualMachineFilterName": {"type": "string", "enum": ["file-system-id"]}, "StorageVirtualMachineFilterValue": {"type": "string", "max": 128, "min": 1, "pattern": "^[0-9a-zA-Z\\*\\.\\\\/\\?\\-\\_]*$"}, "StorageVirtualMachineFilterValues": {"type": "list", "member": {"shape": "StorageVirtualMachineFilterValue"}, "max": 20}, "StorageVirtualMachineFilters": {"type": "list", "member": {"shape": "StorageVirtualMachineFilter"}, "max": 1}, "StorageVirtualMachineId": {"type": "string", "max": 21, "min": 21, "pattern": "^(svm-[0-9a-f]{17,})$"}, "StorageVirtualMachineIds": {"type": "list", "member": {"shape": "StorageVirtualMachineId"}, "max": 50}, "StorageVirtualMachineLifecycle": {"type": "string", "enum": ["CREATED", "CREATING", "DELETING", "FAILED", "MISCONFIGURED", "PENDING"]}, "StorageVirtualMachineName": {"type": "string", "max": 47, "min": 1, "pattern": "^[^\\u0000\\u0085\\u2028\\u2029\\r\\n]{1,47}$"}, "StorageVirtualMachineNotFound": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>No FSx for ONTAP SVMs were found based upon the supplied parameters.</p>", "exception": true}, "StorageVirtualMachineRootVolumeSecurityStyle": {"type": "string", "enum": ["UNIX", "NTFS", "MIXED"]}, "StorageVirtualMachineSubtype": {"type": "string", "enum": ["DEFAULT", "DP_DESTINATION", "SYNC_DESTINATION", "SYNC_SOURCE"]}, "StorageVirtualMachines": {"type": "list", "member": {"shape": "StorageVirtualMachine"}, "max": 50}, "SubDirectoriesPaths": {"type": "list", "member": {"shape": "Namespace"}, "max": 500}, "SubnetId": {"type": "string", "documentation": "<p>The ID for a subnet. A <i>subnet</i> is a range of IP addresses in your virtual private cloud (VPC). For more information, see <a href=\"https://docs.aws.amazon.com/AmazonVPC/latest/UserGuide/VPC_Subnets.html\">VPC and subnets</a> in the <i>Amazon VPC User Guide.</i> </p>", "max": 24, "min": 15, "pattern": "^(subnet-[0-9a-f]{8,})$"}, "SubnetIds": {"type": "list", "member": {"shape": "SubnetId"}, "documentation": "<p>A list of subnet IDs that the cache will be accessible from. You can specify only one subnet ID in a call to the <code>CreateFileCache</code> operation.</p>", "max": 50}, "SucceededCount": {"type": "long"}, "SvmActiveDirectoryConfiguration": {"type": "structure", "members": {"NetBiosName": {"shape": "NetBiosAlias", "documentation": "<p>The NetBIOS name of the AD computer object to which the SVM is joined.</p>"}, "SelfManagedActiveDirectoryConfiguration": {"shape": "SelfManagedActiveDirectoryAttributes"}}, "documentation": "<p>Describes the Microsoft Active Directory (AD) directory configuration to which the FSx for ONTAP storage virtual machine (SVM) is joined. Note that account credentials are not returned in the response payload.</p>"}, "SvmEndpoint": {"type": "structure", "members": {"DNSName": {"shape": "DNSName"}, "IpAddresses": {"shape": "OntapEndpointIpAddresses", "documentation": "<p>The SVM endpoint's IP addresses.</p>"}}, "documentation": "<p>An Amazon FSx for NetApp ONTAP storage virtual machine (SVM) has four endpoints that are used to access data or to manage the SVM using the NetApp ONTAP CLI, REST API, or NetApp CloudManager. They are the <code>Iscsi</code>, <code>Management</code>, <code>Nfs</code>, and <code>Smb</code> endpoints.</p>"}, "SvmEndpoints": {"type": "structure", "members": {"Iscsi": {"shape": "SvmEndpoint", "documentation": "<p>An endpoint for connecting using the Internet Small Computer Systems Interface (iSCSI) protocol.</p>"}, "Management": {"shape": "SvmEndpoint", "documentation": "<p>An endpoint for managing SVMs using the NetApp ONTAP CLI, NetApp ONTAP API, or NetApp CloudManager.</p>"}, "Nfs": {"shape": "SvmEndpoint", "documentation": "<p>An endpoint for connecting using the Network File System (NFS) protocol.</p>"}, "Smb": {"shape": "SvmEndpoint", "documentation": "<p>An endpoint for connecting using the Server Message Block (SMB) protocol.</p>"}}, "documentation": "<p>An Amazon FSx for NetApp ONTAP storage virtual machine (SVM) has the following endpoints that are used to access data or to manage the SVM using the NetApp ONTAP CLI, REST API, or NetApp CloudManager.</p>"}, "Tag": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {"shape": "TagKey", "documentation": "<p>A value that specifies the <code>TagKey</code>, the name of the tag. Tag keys must be unique for the resource to which they are attached.</p>"}, "Value": {"shape": "TagValue", "documentation": "<p>A value that specifies the <code>TagValue</code>, the value assigned to the corresponding tag key. Tag values can be null and don't have to be unique in a tag set. For example, you can have a key-value pair in a tag set of <code>finances : April</code> and also of <code>payroll : April</code>.</p>"}}, "documentation": "<p>Specifies a key-value pair for a resource tag.</p>"}, "TagKey": {"type": "string", "documentation": "<p>A string of 1 to 128 characters that specifies the key for a tag. Tag keys must be unique for the resource to which they are attached.</p>", "max": 128, "min": 1, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "TagKeys": {"type": "list", "member": {"shape": "TagKey"}, "documentation": "<p>A list of <code>TagKey</code> values, with a maximum of 50 elements.</p>", "max": 50, "min": 1}, "TagResourceRequest": {"type": "structure", "required": ["ResourceARN", "Tags"], "members": {"ResourceARN": {"shape": "ResourceARN", "documentation": "<p>The Amazon Resource Name (ARN) of the Amazon FSx resource that you want to tag.</p>"}, "Tags": {"shape": "Tags", "documentation": "<p>A list of tags for the resource. If a tag with a given key already exists, the value is replaced by the one specified in this parameter.</p>"}}, "documentation": "<p>The request object for the <code>TagResource</code> operation.</p>"}, "TagResourceResponse": {"type": "structure", "members": {}, "documentation": "<p>The response object for the <code>TagResource</code> operation.</p>"}, "TagValue": {"type": "string", "documentation": "<p>A string of 0 to 256 characters that specifies the value for a tag. Tag values can be null and don't have to be unique in a tag set.</p>", "max": 256, "min": 0, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "Tags": {"type": "list", "member": {"shape": "Tag"}, "documentation": "<p>A list of <code>Tag</code> values, with a maximum of 50 elements.</p>", "max": 50, "min": 1}, "TaskId": {"type": "string", "max": 128, "min": 12, "pattern": "^(task-[0-9a-f]{17,})$"}, "TaskIds": {"type": "list", "member": {"shape": "TaskId"}, "max": 50}, "ThroughputCapacityMbps": {"type": "integer", "max": 2000000, "min": 4000}, "ThroughputCapacityPerHAPair": {"type": "integer", "max": 6144, "min": 128}, "TieringPolicy": {"type": "structure", "members": {"CoolingPeriod": {"shape": "CoolingPeriod", "documentation": "<p>Specifies the number of days that user data in a volume must remain inactive before it is considered \"cold\" and moved to the capacity pool. Used with the <code>AUTO</code> and <code>SNAPSHOT_ONLY</code> tiering policies. Enter a whole number between 2 and 183. Default values are 31 days for <code>AUTO</code> and 2 days for <code>SNAPSHOT_ONLY</code>.</p>"}, "Name": {"shape": "TieringPolicyName", "documentation": "<p>Specifies the tiering policy used to transition data. Default value is <code>SNAPSHOT_ONLY</code>.</p> <ul> <li> <p> <code>SNAPSHOT_ONLY</code> - moves cold snapshots to the capacity pool storage tier.</p> </li> <li> <p> <code>AUTO</code> - moves cold user data and snapshots to the capacity pool storage tier based on your access patterns.</p> </li> <li> <p> <code>ALL</code> - moves all user data blocks in both the active file system and Snapshot copies to the storage pool tier.</p> </li> <li> <p> <code>NONE</code> - keeps a volume's data in the primary storage tier, preventing it from being moved to the capacity pool tier.</p> </li> </ul>"}}, "documentation": "<p>Describes the data tiering policy for an ONTAP volume. When enabled, Amazon FSx for ONTAP's intelligent tiering automatically transitions a volume's data between the file system's primary storage and capacity pool storage based on your access patterns.</p> <p>Valid tiering policies are the following:</p> <ul> <li> <p> <code>SNAPSHOT_ONLY</code> - (Default value) moves cold snapshots to the capacity pool storage tier.</p> </li> </ul> <ul> <li> <p> <code>AUTO</code> - moves cold user data and snapshots to the capacity pool storage tier based on your access patterns.</p> </li> </ul> <ul> <li> <p> <code>ALL</code> - moves all user data blocks in both the active file system and Snapshot copies to the storage pool tier.</p> </li> </ul> <ul> <li> <p> <code>NONE</code> - keeps a volume's data in the primary storage tier, preventing it from being moved to the capacity pool tier.</p> </li> </ul>"}, "TieringPolicyName": {"type": "string", "enum": ["SNAPSHOT_ONLY", "AUTO", "ALL", "NONE"]}, "TooManyAccessPoints": {"type": "structure", "members": {"ErrorCode": {"shape": "ErrorCode", "documentation": "<p>An error code indicating that you have reached the maximum number of S3 access points attachments allowed for your account in this Amazon Web Services Region, or for the file system.</p>"}, "Message": {"shape": "ErrorMessage"}}, "documentation": "<p>You have reached the maximum number of S3 access points attachments allowed for your account in this Amazon Web Services Region, or for the file system. For more information, or to request an increase, see <a href=\"https://docs.aws.amazon.com/fsx/latest/OpenZFSGuide/limits.html\">Service quotas on FSx resources</a> in the FSx for OpenZFS User Guide.</p>", "exception": true}, "TotalConstituents": {"type": "integer", "max": 200, "min": 1}, "TotalCount": {"type": "long"}, "TotalTransferBytes": {"type": "long", "min": 0}, "UUID": {"type": "string", "max": 36, "pattern": "^[^\\u0000\\u0085\\u2028\\u2029\\r\\n]{1,36}$"}, "Unit": {"type": "string", "enum": ["DAYS"]}, "UnsupportedOperation": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The requested operation is not supported for this resource or API.</p>", "exception": true}, "UntagResourceRequest": {"type": "structure", "required": ["ResourceARN", "TagKeys"], "members": {"ResourceARN": {"shape": "ResourceARN", "documentation": "<p>The ARN of the Amazon FSx resource to untag.</p>"}, "TagKeys": {"shape": "TagKeys", "documentation": "<p>A list of keys of tags on the resource to untag. In case the tag key doesn't exist, the call will still succeed to be idempotent.</p>"}}, "documentation": "<p>The request object for <code>UntagResource</code> action.</p>"}, "UntagResourceResponse": {"type": "structure", "members": {}, "documentation": "<p>The response object for <code>UntagResource</code> action.</p>"}, "UpdateDataRepositoryAssociationRequest": {"type": "structure", "required": ["AssociationId"], "members": {"AssociationId": {"shape": "DataRepositoryAssociationId", "documentation": "<p>The ID of the data repository association that you are updating.</p>"}, "ClientRequestToken": {"shape": "ClientRequestToken", "idempotencyToken": true}, "ImportedFileChunkSize": {"shape": "Megabytes", "documentation": "<p>For files imported from a data repository, this value determines the stripe count and maximum amount of data per file (in MiB) stored on a single physical disk. The maximum number of disks that a single file can be striped across is limited by the total number of disks that make up the file system.</p> <p>The default chunk size is 1,024 MiB (1 GiB) and can go as high as 512,000 MiB (500 GiB). Amazon S3 objects have a maximum size of 5 TB.</p>"}, "S3": {"shape": "S3DataRepositoryConfiguration", "documentation": "<p>The configuration for an Amazon S3 data repository linked to an Amazon FSx Lustre file system with a data repository association. The configuration defines which file events (new, changed, or deleted files or directories) are automatically imported from the linked data repository to the file system or automatically exported from the file system to the data repository.</p>"}}}, "UpdateDataRepositoryAssociationResponse": {"type": "structure", "members": {"Association": {"shape": "DataRepositoryAssociation", "documentation": "<p>The response object returned after the data repository association is updated.</p>"}}}, "UpdateFileCacheLustreConfiguration": {"type": "structure", "members": {"WeeklyMaintenanceStartTime": {"shape": "WeeklyTime"}}, "documentation": "<p>The configuration update for an Amazon File Cache resource.</p>"}, "UpdateFileCacheRequest": {"type": "structure", "required": ["FileCacheId"], "members": {"FileCacheId": {"shape": "FileCacheId", "documentation": "<p>The ID of the cache that you are updating.</p>"}, "ClientRequestToken": {"shape": "ClientRequestToken", "idempotencyToken": true}, "LustreConfiguration": {"shape": "UpdateFileCacheLustreConfiguration", "documentation": "<p>The configuration updates for an Amazon File Cache resource.</p>"}}}, "UpdateFileCacheResponse": {"type": "structure", "members": {"FileCache": {"shape": "FileCache", "documentation": "<p>A description of the cache that was updated.</p>"}}}, "UpdateFileSystemLustreConfiguration": {"type": "structure", "members": {"WeeklyMaintenanceStartTime": {"shape": "WeeklyTime", "documentation": "<p>(Optional) The preferred start time to perform weekly maintenance, formatted d:HH:MM in the UTC time zone. d is the weekday number, from 1 through 7, beginning with Monday and ending with Sunday.</p>"}, "DailyAutomaticBackupStartTime": {"shape": "DailyTime"}, "AutomaticBackupRetentionDays": {"shape": "AutomaticBackupRetentionDays", "documentation": "<p>The number of days to retain automatic backups. Setting this property to <code>0</code> disables automatic backups. You can retain automatic backups for a maximum of 90 days. The default is <code>0</code>.</p>"}, "AutoImportPolicy": {"shape": "AutoImportPolicyType", "documentation": "<p> (Optional) When you create your file system, your existing S3 objects appear as file and directory listings. Use this property to choose how Amazon FSx keeps your file and directory listing up to date as you add or modify objects in your linked S3 bucket. <code>AutoImportPolicy</code> can have the following values:</p> <ul> <li> <p> <code>NONE</code> - (Default) AutoImport is off. Amazon FSx only updates file and directory listings from the linked S3 bucket when the file system is created. FSx does not update the file and directory listing for any new or changed objects after choosing this option.</p> </li> <li> <p> <code>NEW</code> - AutoImport is on. Amazon FSx automatically imports directory listings of any new objects added to the linked S3 bucket that do not currently exist in the FSx file system. </p> </li> <li> <p> <code>NEW_CHANGED</code> - AutoImport is on. Amazon FSx automatically imports file and directory listings of any new objects added to the S3 bucket and any existing objects that are changed in the S3 bucket after you choose this option.</p> </li> <li> <p> <code>NEW_CHANGED_DELETED</code> - AutoImport is on. Amazon FSx automatically imports file and directory listings of any new objects added to the S3 bucket, any existing objects that are changed in the S3 bucket, and any objects that were deleted in the S3 bucket.</p> </li> </ul> <p>This parameter is not supported for file systems with a data repository association.</p>"}, "DataCompressionType": {"shape": "DataCompressionType", "documentation": "<p>Sets the data compression configuration for the file system. <code>DataCompressionType</code> can have the following values:</p> <ul> <li> <p> <code>NONE</code> - Data compression is turned off for the file system.</p> </li> <li> <p> <code>LZ4</code> - Data compression is turned on with the LZ4 algorithm.</p> </li> </ul> <p>If you don't use <code>DataCompressionType</code>, the file system retains its current data compression configuration.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/LustreGuide/data-compression.html\">Lustre data compression</a>.</p>"}, "LogConfiguration": {"shape": "LustreLogCreateConfiguration", "documentation": "<p>The Lustre logging configuration used when updating an Amazon FSx for Lustre file system. When logging is enabled, Lustre logs error and warning events for data repositories associated with your file system to Amazon CloudWatch Logs.</p>"}, "RootSquashConfiguration": {"shape": "LustreRootSquashConfiguration", "documentation": "<p>The Lustre root squash configuration used when updating an Amazon FSx for Lustre file system. When enabled, root squash restricts root-level access from clients that try to access your file system as a root user.</p>"}, "PerUnitStorageThroughput": {"shape": "PerUnitStorageThroughput", "documentation": "<p>The throughput of an Amazon FSx for Lustre Persistent SSD-based file system, measured in megabytes per second per tebibyte (MB/s/TiB). You can increase or decrease your file system's throughput. Valid values depend on the deployment type of the file system, as follows:</p> <ul> <li> <p>For <code>PERSISTENT_1</code> SSD-based deployment types, valid values are 50, 100, and 200 MB/s/TiB.</p> </li> <li> <p>For <code>PERSISTENT_2</code> SSD-based deployment types, valid values are 125, 250, 500, and 1000 MB/s/TiB.</p> </li> </ul> <p>For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/LustreGuide/managing-throughput-capacity.html\"> Managing throughput capacity</a>.</p>"}, "MetadataConfiguration": {"shape": "UpdateFileSystemLustreMetadataConfiguration", "documentation": "<p>The Lustre metadata performance configuration for an Amazon FSx for Lustre file system using a <code>PERSISTENT_2</code> deployment type. When this configuration is enabled, the file system supports increasing metadata performance.</p>"}, "ThroughputCapacity": {"shape": "ThroughputCapacityMbps", "documentation": "<p>The throughput of an Amazon FSx for Lustre file system using an Intelligent-Tiering storage class, measured in megabytes per second (MBps). You can only increase your file system's throughput. Valid values are 4000 MBps or multiples of 4000 MBps.</p>"}, "DataReadCacheConfiguration": {"shape": "LustreReadCacheConfiguration", "documentation": "<p>Specifies the optional provisioned SSD read cache on Amazon FSx for Lustre file systems that use the Intelligent-Tiering storage class.</p>"}}, "documentation": "<p>The configuration object for Amazon FSx for Lustre file systems used in the <code>UpdateFileSystem</code> operation.</p>"}, "UpdateFileSystemLustreMetadataConfiguration": {"type": "structure", "members": {"Iops": {"shape": "MetadataIops", "documentation": "<p>(USER_PROVISIONED mode only) Specifies the number of Metadata IOPS to provision for your file system.</p> <ul> <li> <p>For SSD file systems, valid values are <code>1500</code>, <code>3000</code>, <code>6000</code>, <code>12000</code>, and multiples of <code>12000</code> up to a maximum of <code>192000</code>.</p> </li> <li> <p>For Intelligent-Tiering file systems, valid values are <code>6000</code> and <code>12000</code>.</p> </li> </ul> <p>The value you provide must be greater than or equal to the current number of Metadata IOPS provisioned for the file system.</p>"}, "Mode": {"shape": "MetadataConfigurationMode", "documentation": "<p>The metadata configuration mode for provisioning Metadata IOPS for an FSx for Lustre file system using a <code>PERSISTENT_2</code> deployment type.</p> <ul> <li> <p>To increase the Metadata IOPS or to switch an SSD file system from AUTOMATIC, specify <code>USER_PROVISIONED</code> as the value for this parameter. Then use the Iops parameter to provide a Metadata IOPS value that is greater than or equal to the current number of Metadata IOPS provisioned for the file system.</p> </li> <li> <p>To switch from USER_PROVISIONED mode on an SSD file system, specify <code>AUTOMATIC</code> as the value for this parameter, but do not input a value for Iops.</p> <note> <ul> <li> <p>If you request to switch from USER_PROVISIONED to AUTOMATIC mode and the current Metadata IOPS value is greater than the automated default, FSx for Lustre rejects the request because downscaling Metadata IOPS is not supported.</p> </li> <li> <p>AUTOMATIC mode is not supported on Intelligent-Tiering file systems. For Intelligent-Tiering file systems, use USER_PROVISIONED mode.</p> </li> </ul> </note> </li> </ul>"}}, "documentation": "<p>The Lustre metadata performance configuration update for an Amazon FSx for Lustre file system using a <code>PERSISTENT_2</code> deployment type. You can request an increase in your file system's Metadata IOPS and/or switch your file system's metadata configuration mode. For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/LustreGuide/managing-metadata-performance.html\">Managing metadata performance</a> in the <i>Amazon FSx for Lustre User Guide</i>.</p>"}, "UpdateFileSystemOntapConfiguration": {"type": "structure", "members": {"AutomaticBackupRetentionDays": {"shape": "AutomaticBackupRetentionDays"}, "DailyAutomaticBackupStartTime": {"shape": "DailyTime"}, "FsxAdminPassword": {"shape": "AdminPassword", "documentation": "<p>Update the password for the <code>fsxadmin</code> user by entering a new password. You use the <code>fsxadmin</code> user to access the NetApp ONTAP CLI and REST API to manage your file system resources. For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/managing-resources-ontap-apps.html\">Managing resources using NetApp Application</a>.</p>"}, "WeeklyMaintenanceStartTime": {"shape": "WeeklyTime"}, "DiskIopsConfiguration": {"shape": "DiskIopsConfiguration", "documentation": "<p>The SSD IOPS (input output operations per second) configuration for an Amazon FSx for NetApp ONTAP file system. The default is 3 IOPS per GB of storage capacity, but you can provision additional IOPS per GB of storage. The configuration consists of an IOPS mode (<code>AUTOMATIC</code> or <code>USER_PROVISIONED</code>), and in the case of <code>USER_PROVISIONED</code> IOPS, the total number of SSD IOPS provisioned. For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/increase-primary-storage.html\">Updating SSD storage capacity and IOPS</a>.</p>"}, "ThroughputCapacity": {"shape": "MegabytesPerSecond", "documentation": "<p>Enter a new value to change the amount of throughput capacity for the file system in megabytes per second (MBps). For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/managing-throughput-capacity.html\">Managing throughput capacity</a> in the FSx for ONTAP User Guide.</p> <p>Amazon FSx responds with an HTTP status code 400 (Bad Request) for the following conditions:</p> <ul> <li> <p>The value of <code>ThroughputCapacity</code> and <code>ThroughputCapacityPerHAPair</code> are not the same value.</p> </li> <li> <p>The value of <code>ThroughputCapacity</code> when divided by the value of <code>HAPairs</code> is outside of the valid range for <code>ThroughputCapacity</code>.</p> </li> </ul>"}, "AddRouteTableIds": {"shape": "RouteTableIds", "documentation": "<p>(Multi-AZ only) A list of IDs of new virtual private cloud (VPC) route tables to associate (add) with your Amazon FSx for NetApp ONTAP file system.</p>"}, "RemoveRouteTableIds": {"shape": "RouteTableIds", "documentation": "<p>(Multi-AZ only) A list of IDs of existing virtual private cloud (VPC) route tables to disassociate (remove) from your Amazon FSx for NetApp ONTAP file system. You can use the API operation to retrieve the list of VPC route table IDs for a file system.</p>"}, "ThroughputCapacityPerHAPair": {"shape": "ThroughputCapacityPerHAPair", "documentation": "<p>Use to choose the throughput capacity per HA pair, rather than the total throughput for the file system. </p> <p>This field and <code>ThroughputCapacity</code> cannot be defined in the same API call, but one is required.</p> <p>This field and <code>ThroughputCapacity</code> are the same for file systems with one HA pair.</p> <ul> <li> <p>For <code>SINGLE_AZ_1</code> and <code>MULTI_AZ_1</code> file systems, valid values are 128, 256, 512, 1024, 2048, or 4096 MBps.</p> </li> <li> <p>For <code>SINGLE_AZ_2</code>, valid values are 1536, 3072, or 6144 MBps.</p> </li> <li> <p>For <code>MULTI_AZ_2</code>, valid values are 384, 768, 1536, 3072, or 6144 MBps.</p> </li> </ul> <p>Amazon FSx responds with an HTTP status code 400 (Bad Request) for the following conditions:</p> <ul> <li> <p>The value of <code>ThroughputCapacity</code> and <code>ThroughputCapacityPerHAPair</code> are not the same value for file systems with one HA pair.</p> </li> <li> <p>The value of deployment type is <code>SINGLE_AZ_2</code> and <code>ThroughputCapacity</code> / <code>ThroughputCapacityPerHAPair</code> is not a valid HA pair (a value between 1 and 12).</p> </li> <li> <p>The value of <code>ThroughputCapacityPerHAPair</code> is not a valid value.</p> </li> </ul>"}, "HAPairs": {"shape": "HAPairs", "documentation": "<p>Use to update the number of high-availability (HA) pairs for a second-generation single-AZ file system. If you increase the number of HA pairs for your file system, you must specify proportional increases for <code>StorageCapacity</code>, <code>Iops</code>, and <code>ThroughputCapacity</code>. For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/administering-file-systems.html#HA-pairs\">High-availability (HA) pairs</a> in the FSx for ONTAP user guide. Block storage protocol support (iSCSI and NVMe over TCP) is disabled on file systems with more than 6 HA pairs. For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/supported-fsx-clients.html#using-block-storage\">Using block storage protocols</a>.</p>"}}, "documentation": "<p>The configuration updates for an Amazon FSx for NetApp ONTAP file system.</p>"}, "UpdateFileSystemOpenZFSConfiguration": {"type": "structure", "members": {"AutomaticBackupRetentionDays": {"shape": "AutomaticBackupRetentionDays"}, "CopyTagsToBackups": {"shape": "Flag", "documentation": "<p>A Boolean value indicating whether tags for the file system should be copied to backups. This value defaults to <code>false</code>. If it's set to <code>true</code>, all tags for the file system are copied to all automatic and user-initiated backups where the user doesn't specify tags. If this value is <code>true</code> and you specify one or more tags, only the specified tags are copied to backups. If you specify one or more tags when creating a user-initiated backup, no tags are copied from the file system, regardless of this value.</p>"}, "CopyTagsToVolumes": {"shape": "Flag", "documentation": "<p>A Boolean value indicating whether tags for the volume should be copied to snapshots. This value defaults to <code>false</code>. If it's set to <code>true</code>, all tags for the volume are copied to snapshots where the user doesn't specify tags. If this value is <code>true</code> and you specify one or more tags, only the specified tags are copied to snapshots. If you specify one or more tags when creating the snapshot, no tags are copied from the volume, regardless of this value.</p>"}, "DailyAutomaticBackupStartTime": {"shape": "DailyTime"}, "ThroughputCapacity": {"shape": "MegabytesPerSecond", "documentation": "<p>The throughput of an Amazon FSx for OpenZFS file system, measured in megabytes per second&#x2028; (MB/s). Valid values depend on the DeploymentType you choose, as follows:</p> <ul> <li> <p>For <code>MULTI_AZ_1</code> and <code>SINGLE_AZ_2</code>, valid values are 160, 320, 640, 1280, 2560, 3840, 5120, 7680, or 10240 MB/s.</p> </li> <li> <p>For <code>SINGLE_AZ_1</code>, valid values are 64, 128, 256, 512, 1024, 2048, 3072, or 4096 MB/s.</p> </li> </ul>"}, "WeeklyMaintenanceStartTime": {"shape": "WeeklyTime"}, "DiskIopsConfiguration": {"shape": "DiskIopsConfiguration"}, "AddRouteTableIds": {"shape": "RouteTableIds", "documentation": "<p>(Multi-AZ only) A list of IDs of new virtual private cloud (VPC) route tables to associate (add) with your Amazon FSx for OpenZFS file system.</p>"}, "RemoveRouteTableIds": {"shape": "RouteTableIds", "documentation": "<p>(Multi-AZ only) A list of IDs of existing virtual private cloud (VPC) route tables to disassociate (remove) from your Amazon FSx for OpenZFS file system. You can use the API operation to retrieve the list of VPC route table IDs for a file system.</p>"}, "ReadCacheConfiguration": {"shape": "OpenZFSReadCacheConfiguration", "documentation": "<p> The configuration for the optional provisioned SSD read cache on file systems that use the Intelligent-Tiering storage class.</p>"}}, "documentation": "<p>The configuration updates for an Amazon FSx for OpenZFS file system.</p>"}, "UpdateFileSystemRequest": {"type": "structure", "required": ["FileSystemId"], "members": {"FileSystemId": {"shape": "FileSystemId", "documentation": "<p>The ID of the file system that you are updating.</p>"}, "ClientRequestToken": {"shape": "ClientRequestToken", "documentation": "<p>A string of up to 63 ASCII characters that Amazon FSx uses to ensure idempotent updates. This string is automatically filled on your behalf when you use the Command Line Interface (CLI) or an Amazon Web Services SDK.</p>", "idempotencyToken": true}, "StorageCapacity": {"shape": "StorageCapacity", "documentation": "<p>Use this parameter to increase the storage capacity of an FSx for Windows File Server, FSx for Lustre, FSx for OpenZFS, or FSx for ONTAP file system. Specifies the storage capacity target value, in GiB, to increase the storage capacity for the file system that you're updating. </p> <note> <p>You can't make a storage capacity increase request if there is an existing storage capacity increase request in progress.</p> </note> <p>For Lustre file systems, the storage capacity target value can be the following:</p> <ul> <li> <p>For <code>SCRATCH_2</code>, <code>PERSISTENT_1</code>, and <code>PERSISTENT_2 SSD</code> deployment types, valid values are in multiples of 2400 GiB. The value must be greater than the current storage capacity.</p> </li> <li> <p>For <code>PERSISTENT HDD</code> file systems, valid values are multiples of 6000 GiB for 12-MBps throughput per TiB file systems and multiples of 1800 GiB for 40-MBps throughput per TiB file systems. The values must be greater than the current storage capacity.</p> </li> <li> <p>For <code>SCRATCH_1</code> file systems, you can't increase the storage capacity.</p> </li> </ul> <p>For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/LustreGuide/managing-storage-capacity.html\">Managing storage and throughput capacity</a> in the <i>FSx for Lustre User Guide</i>.</p> <p>For FSx for OpenZFS file systems, the storage capacity target value must be at least 10 percent greater than the current storage capacity value. For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/OpenZFSGuide/managing-storage-capacity.html\">Managing storage capacity</a> in the <i>FSx for OpenZFS User Guide</i>.</p> <p>For Windows file systems, the storage capacity target value must be at least 10 percent greater than the current storage capacity value. To increase storage capacity, the file system must have at least 16 MBps of throughput capacity. For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/WindowsGuide/managing-storage-capacity.html\">Managing storage capacity</a> in the <i>Amazon FSxfor Windows File Server User Guide</i>.</p> <p>For ONTAP file systems, the storage capacity target value must be at least 10 percent greater than the current storage capacity value. For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/managing-storage-capacity.html\">Managing storage capacity and provisioned IOPS</a> in the <i>Amazon FSx for NetApp ONTAP User Guide</i>.</p>"}, "WindowsConfiguration": {"shape": "UpdateFileSystemWindowsConfiguration", "documentation": "<p>The configuration updates for an Amazon FSx for Windows File Server file system.</p>"}, "LustreConfiguration": {"shape": "UpdateFileSystemLustreConfiguration"}, "OntapConfiguration": {"shape": "UpdateFileSystemOntapConfiguration"}, "OpenZFSConfiguration": {"shape": "UpdateFileSystemOpenZFSConfiguration", "documentation": "<p>The configuration updates for an FSx for OpenZFS file system.</p>"}, "StorageType": {"shape": "StorageType"}, "FileSystemTypeVersion": {"shape": "FileSystemTypeVersion", "documentation": "<p>The Lustre version you are updating an FSx for Lustre file system to. Valid values are <code>2.12</code> and <code>2.15</code>. The value you choose must be newer than the file system's current Lustre version.</p>"}}, "documentation": "<p>The request object for the <code>UpdateFileSystem</code> operation.</p>"}, "UpdateFileSystemResponse": {"type": "structure", "members": {"FileSystem": {"shape": "FileSystem", "documentation": "<p>A description of the file system that was updated.</p>"}}, "documentation": "<p>The response object for the <code>UpdateFileSystem</code> operation.</p>"}, "UpdateFileSystemWindowsConfiguration": {"type": "structure", "members": {"WeeklyMaintenanceStartTime": {"shape": "WeeklyTime", "documentation": "<p>The preferred start time to perform weekly maintenance, formatted d:HH:MM in the UTC time zone. Where d is the weekday number, from 1 through 7, with 1 = Monday and 7 = Sunday.</p>"}, "DailyAutomaticBackupStartTime": {"shape": "DailyTime", "documentation": "<p>The preferred time to start the daily automatic backup, in the UTC time zone, for example, <code>02:00</code> </p>"}, "AutomaticBackupRetentionDays": {"shape": "AutomaticBackupRetentionDays", "documentation": "<p>The number of days to retain automatic backups. Setting this property to <code>0</code> disables automatic backups. You can retain automatic backups for a maximum of 90 days. The default is <code>30</code>. For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/WindowsGuide/using-backups.html#automatic-backups\">Working with Automatic Daily Backups</a>.</p>"}, "ThroughputCapacity": {"shape": "MegabytesPerSecond", "documentation": "<p>Sets the target value for a file system's throughput capacity, in MB/s, that you are updating the file system to. Valid values are 8, 16, 32, 64, 128, 256, 512, 1024, 2048. You cannot make a throughput capacity update request if there is an existing throughput capacity update request in progress. For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/WindowsGuide/managing-throughput-capacity.html\">Managing Throughput Capacity</a>.</p>"}, "SelfManagedActiveDirectoryConfiguration": {"shape": "SelfManagedActiveDirectoryConfigurationUpdates", "documentation": "<p>The configuration Amazon FSx uses to join the Windows File Server instance to the self-managed Microsoft AD directory. You cannot make a self-managed Microsoft AD update request if there is an existing self-managed Microsoft AD update request in progress.</p>"}, "AuditLogConfiguration": {"shape": "WindowsAuditLogCreateConfiguration", "documentation": "<p>The configuration that Amazon FSx for Windows File Server uses to audit and log user accesses of files, folders, and file shares on the Amazon FSx for Windows File Server file system..</p>"}, "DiskIopsConfiguration": {"shape": "DiskIopsConfiguration", "documentation": "<p>The SSD IOPS (input/output operations per second) configuration for an Amazon FSx for Windows file system. By default, Amazon FSx automatically provisions 3 IOPS per GiB of storage capacity. You can provision additional IOPS per GiB of storage, up to the maximum limit associated with your chosen throughput capacity.</p>"}}, "documentation": "<p>Updates the configuration for an existing Amazon FSx for Windows File Server file system. Amazon FSx only overwrites existing properties with non-null values provided in the request.</p>"}, "UpdateOntapVolumeConfiguration": {"type": "structure", "members": {"JunctionPath": {"shape": "JunctionPath", "documentation": "<p>Specifies the location in the SVM's namespace where the volume is mounted. The <code>JunctionPath</code> must have a leading forward slash, such as <code>/vol3</code>.</p>"}, "SecurityStyle": {"shape": "SecurityStyle", "documentation": "<p>The security style for the volume, which can be <code>UNIX</code>, <code>NTFS</code>, or <code>MIXED</code>.</p>"}, "SizeInMegabytes": {"shape": "VolumeCapacity", "documentation": "<p>Specifies the size of the volume in megabytes.</p>"}, "StorageEfficiencyEnabled": {"shape": "Flag", "documentation": "<p>Default is <code>false</code>. Set to true to enable the deduplication, compression, and compaction storage efficiency features on the volume.</p>"}, "TieringPolicy": {"shape": "TieringPolicy", "documentation": "<p>Update the volume's data tiering policy.</p>"}, "SnapshotPolicy": {"shape": "SnapshotPolicy", "documentation": "<p>Specifies the snapshot policy for the volume. There are three built-in snapshot policies:</p> <ul> <li> <p> <code>default</code>: This is the default policy. A maximum of six hourly snapshots taken five minutes past the hour. A maximum of two daily snapshots taken Monday through Saturday at 10 minutes after midnight. A maximum of two weekly snapshots taken every Sunday at 15 minutes after midnight.</p> </li> <li> <p> <code>default-1weekly</code>: This policy is the same as the <code>default</code> policy except that it only retains one snapshot from the weekly schedule.</p> </li> <li> <p> <code>none</code>: This policy does not take any snapshots. This policy can be assigned to volumes to prevent automatic snapshots from being taken.</p> </li> </ul> <p>You can also provide the name of a custom policy that you created with the ONTAP CLI or REST API.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/snapshots-ontap.html#snapshot-policies\">Snapshot policies</a> in the <i>Amazon FSx for NetApp ONTAP User Guide</i>.</p>"}, "CopyTagsToBackups": {"shape": "Flag", "documentation": "<p>A boolean flag indicating whether tags for the volume should be copied to backups. This value defaults to false. If it's set to true, all tags for the volume are copied to all automatic and user-initiated backups where the user doesn't specify tags. If this value is true, and you specify one or more tags, only the specified tags are copied to backups. If you specify one or more tags when creating a user-initiated backup, no tags are copied from the volume, regardless of this value.</p>"}, "SnaplockConfiguration": {"shape": "UpdateSnaplockConfiguration", "documentation": "<p>The configuration object for updating the SnapLock configuration of an FSx for ONTAP SnapLock volume. </p>"}, "SizeInBytes": {"shape": "VolumeCapacityBytes", "documentation": "<p>The configured size of the volume, in bytes.</p>"}}, "documentation": "<p>Used to specify changes to the ONTAP configuration for the volume you are updating.</p>"}, "UpdateOpenZFSVolumeConfiguration": {"type": "structure", "members": {"StorageCapacityReservationGiB": {"shape": "IntegerNoMaxFromNegativeOne", "documentation": "<p>The amount of storage in gibibytes (GiB) to reserve from the parent volume. You can't reserve more storage than the parent volume has reserved. You can specify a value of <code>-1</code> to unset a volume's storage capacity reservation.</p>"}, "StorageCapacityQuotaGiB": {"shape": "IntegerNoMaxFromNegativeOne", "documentation": "<p>The maximum amount of storage in gibibytes (GiB) that the volume can use from its parent. You can specify a quota larger than the storage on the parent volume. You can specify a value of <code>-1</code> to unset a volume's storage capacity quota.</p>"}, "RecordSizeKiB": {"shape": "IntegerRecordSizeKiB", "documentation": "<p>Specifies the record size of an OpenZFS volume, in kibibytes (KiB). Valid values are 4, 8, 16, 32, 64, 128, 256, 512, or 1024 KiB. The default is 128 KiB. Most workloads should use the default record size. Database workflows can benefit from a smaller record size, while streaming workflows can benefit from a larger record size. For additional guidance on when to set a custom record size, see <a href=\"https://docs.aws.amazon.com/fsx/latest/OpenZFSGuide/performance.html#performance-tips-zfs\"> Tips for maximizing performance</a> in the <i>Amazon FSx for OpenZFS User Guide</i>.</p>"}, "DataCompressionType": {"shape": "OpenZFSDataCompressionType", "documentation": "<p>Specifies the method used to compress the data on the volume. The compression type is <code>NONE</code> by default.</p> <ul> <li> <p> <code>NONE</code> - Doesn't compress the data on the volume. <code>NONE</code> is the default.</p> </li> <li> <p> <code>ZSTD</code> - Compresses the data in the volume using the Zstandard (ZSTD) compression algorithm. Compared to LZ4, Z-Standard provides a better compression ratio to minimize on-disk storage utilization.</p> </li> <li> <p> <code>LZ4</code> - Compresses the data in the volume using the LZ4 compression algorithm. Compared to Z-Standard, LZ4 is less compute-intensive and delivers higher write throughput speeds.</p> </li> </ul>"}, "NfsExports": {"shape": "OpenZFSNfsExports", "documentation": "<p>The configuration object for mounting a Network File System (NFS) file system.</p>"}, "UserAndGroupQuotas": {"shape": "OpenZFSUserAndGroupQuotas", "documentation": "<p>An object specifying how much storage users or groups can use on the volume.</p>"}, "ReadOnly": {"shape": "Read<PERSON>nly", "documentation": "<p>A Boolean value indicating whether the volume is read-only.</p>"}}, "documentation": "<p>Used to specify changes to the OpenZFS configuration for the volume that you are updating.</p>"}, "UpdateOpenZFSVolumeOption": {"type": "string", "enum": ["DELETE_INTERMEDIATE_SNAPSHOTS", "DELETE_CLONED_VOLUMES", "DELETE_INTERMEDIATE_DATA"]}, "UpdateOpenZFSVolumeOptions": {"type": "list", "member": {"shape": "UpdateOpenZFSVolumeOption"}}, "UpdateSharedVpcConfigurationRequest": {"type": "structure", "members": {"EnableFsxRouteTableUpdatesFromParticipantAccounts": {"shape": "VerboseFlag", "documentation": "<p>Specifies whether participant accounts can create FSx for ONTAP Multi-AZ file systems in shared subnets. Set to <code>true</code> to enable or <code>false</code> to disable.</p>"}, "ClientRequestToken": {"shape": "ClientRequestToken", "idempotencyToken": true}}}, "UpdateSharedVpcConfigurationResponse": {"type": "structure", "members": {"EnableFsxRouteTableUpdatesFromParticipantAccounts": {"shape": "VerboseFlag", "documentation": "<p>Indicates whether participant accounts can create FSx for ONTAP Multi-AZ file systems in shared subnets.</p>"}}}, "UpdateSnaplockConfiguration": {"type": "structure", "members": {"AuditLogVolume": {"shape": "Flag", "documentation": "<p>Enables or disables the audit log volume for an FSx for ONTAP SnapLock volume. The default value is <code>false</code>. If you set <code>AuditLogVolume</code> to <code>true</code>, the SnapLock volume is created as an audit log volume. The minimum retention period for an audit log volume is six months. </p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/how-snaplock-works.html#snaplock-audit-log-volume\"> SnapLock audit log volumes</a>. </p>"}, "AutocommitPeriod": {"shape": "AutocommitPeriod", "documentation": "<p>The configuration object for setting the autocommit period of files in an FSx for ONTAP SnapLock volume. </p>"}, "PrivilegedDelete": {"shape": "PrivilegedDelete", "documentation": "<p>Enables, disables, or permanently disables privileged delete on an FSx for ONTAP SnapLock Enterprise volume. Enabling privileged delete allows SnapLock administrators to delete write once, read many (WORM) files even if they have active retention periods. <code>PERMANENTLY_DISABLED</code> is a terminal state. If privileged delete is permanently disabled on a SnapLock volume, you can't re-enable it. The default value is <code>DISABLED</code>. </p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/snaplock-enterprise.html#privileged-delete\">Privileged delete</a>. </p>"}, "RetentionPeriod": {"shape": "SnaplockRetentionPeriod", "documentation": "<p>Specifies the retention period of an FSx for ONTAP SnapLock volume. </p>"}, "VolumeAppendModeEnabled": {"shape": "Flag", "documentation": "<p>Enables or disables volume-append mode on an FSx for ONTAP SnapLock volume. Volume-append mode allows you to create WORM-appendable files and write data to them incrementally. The default value is <code>false</code>. </p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/worm-state.html#worm-state-append\">Volume-append mode</a>. </p>"}}, "documentation": "<p>Updates the SnapLock configuration for an existing FSx for ONTAP volume. </p>"}, "UpdateSnapshotRequest": {"type": "structure", "required": ["Name", "SnapshotId"], "members": {"ClientRequestToken": {"shape": "ClientRequestToken", "idempotencyToken": true}, "Name": {"shape": "SnapshotName", "documentation": "<p>The name of the snapshot to update.</p>"}, "SnapshotId": {"shape": "SnapshotId", "documentation": "<p>The ID of the snapshot that you want to update, in the format <code>fsvolsnap-0123456789abcdef0</code>.</p>"}}}, "UpdateSnapshotResponse": {"type": "structure", "members": {"Snapshot": {"shape": "Snapshot", "documentation": "<p>Returned after a successful <code>UpdateSnapshot</code> operation, describing the snapshot that you updated.</p>"}}}, "UpdateStorageVirtualMachineRequest": {"type": "structure", "required": ["StorageVirtualMachineId"], "members": {"ActiveDirectoryConfiguration": {"shape": "UpdateSvmActiveDirectoryConfiguration", "documentation": "<p>Specifies updates to an SVM's Microsoft Active Directory (AD) configuration.</p>"}, "ClientRequestToken": {"shape": "ClientRequestToken", "idempotencyToken": true}, "StorageVirtualMachineId": {"shape": "StorageVirtualMachineId", "documentation": "<p>The ID of the SVM that you want to update, in the format <code>svm-0123456789abcdef0</code>.</p>"}, "SvmAdminPassword": {"shape": "AdminPassword", "documentation": "<p>Specifies a new SvmAdminPassword.</p>"}}}, "UpdateStorageVirtualMachineResponse": {"type": "structure", "members": {"StorageVirtualMachine": {"shape": "StorageVirtualMachine"}}}, "UpdateSvmActiveDirectoryConfiguration": {"type": "structure", "members": {"SelfManagedActiveDirectoryConfiguration": {"shape": "SelfManagedActiveDirectoryConfigurationUpdates"}, "NetBiosName": {"shape": "NetBiosAlias", "documentation": "<p>Specifies an updated NetBIOS name of the AD computer object <code>NetBiosName</code> to which an SVM is joined.</p>"}}, "documentation": "<p>Specifies updates to an FSx for ONTAP storage virtual machine's (SVM) Microsoft Active Directory (AD) configuration. Note that account credentials are not returned in the response payload.</p>"}, "UpdateVolumeRequest": {"type": "structure", "required": ["VolumeId"], "members": {"ClientRequestToken": {"shape": "ClientRequestToken", "idempotencyToken": true}, "VolumeId": {"shape": "VolumeId", "documentation": "<p>The ID of the volume that you want to update, in the format <code>fsvol-0123456789abcdef0</code>.</p>"}, "OntapConfiguration": {"shape": "UpdateOntapVolumeConfiguration", "documentation": "<p>The configuration of the ONTAP volume that you are updating.</p>"}, "Name": {"shape": "VolumeName", "documentation": "<p>The name of the OpenZFS volume. OpenZFS root volumes are automatically named <code>FSX</code>. Child volume names must be unique among their parent volume's children. The name of the volume is part of the mount string for the OpenZFS volume. </p>"}, "OpenZFSConfiguration": {"shape": "UpdateOpenZFSVolumeConfiguration", "documentation": "<p>The configuration of the OpenZFS volume that you are updating.</p>"}}}, "UpdateVolumeResponse": {"type": "structure", "members": {"Volume": {"shape": "Volume", "documentation": "<p>A description of the volume just updated. Returned after a successful <code>UpdateVolume</code> API operation.</p>"}}}, "Value": {"type": "long", "min": 0}, "VerboseFlag": {"type": "string", "max": 5, "min": 4, "pattern": "^(?i)(true|false)$"}, "Volume": {"type": "structure", "members": {"CreationTime": {"shape": "CreationTime"}, "FileSystemId": {"shape": "FileSystemId"}, "Lifecycle": {"shape": "VolumeLifecycle", "documentation": "<p>The lifecycle status of the volume.</p> <ul> <li> <p> <code>AVAILABLE</code> - The volume is fully available for use.</p> </li> <li> <p> <code>CREATED</code> - The volume has been created.</p> </li> <li> <p> <code>CREATING</code> - Amazon FSx is creating the new volume.</p> </li> <li> <p> <code>DELETING</code> - Amazon FSx is deleting an existing volume.</p> </li> <li> <p> <code>FAILED</code> - Amazon FSx was unable to create the volume.</p> </li> <li> <p> <code>MISCONFIGURED</code> - The volume is in a failed but recoverable state.</p> </li> <li> <p> <code>PENDING</code> - Amazon FSx hasn't started creating the volume.</p> </li> </ul>"}, "Name": {"shape": "VolumeName", "documentation": "<p>The name of the volume.</p>"}, "OntapConfiguration": {"shape": "OntapVolumeConfiguration"}, "ResourceARN": {"shape": "ResourceARN"}, "Tags": {"shape": "Tags"}, "VolumeId": {"shape": "VolumeId", "documentation": "<p>The system-generated, unique ID of the volume.</p>"}, "VolumeType": {"shape": "VolumeType", "documentation": "<p>The type of the volume.</p>"}, "LifecycleTransitionReason": {"shape": "LifecycleTransitionReason", "documentation": "<p>The reason why the volume lifecycle status changed.</p>"}, "AdministrativeActions": {"shape": "AdministrativeActions", "documentation": "<p>A list of administrative actions for the volume that are in process or waiting to be processed. Administrative actions describe changes to the volume that you have initiated using the <code>UpdateVolume</code> action.</p>"}, "OpenZFSConfiguration": {"shape": "OpenZFSVolumeConfiguration", "documentation": "<p>The configuration of an Amazon FSx for OpenZFS volume.</p>"}}, "documentation": "<p>Describes an Amazon FSx volume.</p>"}, "VolumeCapacity": {"type": "integer", "max": 2147483647, "min": 0}, "VolumeCapacityBytes": {"type": "long", "max": 22517998000000000, "min": 0}, "VolumeFilter": {"type": "structure", "members": {"Name": {"shape": "VolumeFilterName", "documentation": "<p>The name for this filter.</p>"}, "Values": {"shape": "VolumeFilterValues", "documentation": "<p>The values of the filter. These are all the values for any of the applied filters.</p>"}}, "documentation": "<p>A filter used to restrict the results of describe calls for Amazon FSx for NetApp ONTAP or Amazon FSx for OpenZFS volumes. You can use multiple filters to return results that meet all applied filter requirements.</p>"}, "VolumeFilterName": {"type": "string", "enum": ["file-system-id", "storage-virtual-machine-id"]}, "VolumeFilterValue": {"type": "string", "max": 128, "min": 1, "pattern": "^[0-9a-zA-Z\\*\\.\\\\/\\?\\-\\_]*$"}, "VolumeFilterValues": {"type": "list", "member": {"shape": "VolumeFilterValue"}, "max": 20}, "VolumeFilters": {"type": "list", "member": {"shape": "VolumeFilter"}, "max": 2}, "VolumeId": {"type": "string", "max": 23, "min": 23, "pattern": "^(fsvol-[0-9a-f]{17,})$"}, "VolumeIds": {"type": "list", "member": {"shape": "VolumeId"}, "max": 50}, "VolumeLifecycle": {"type": "string", "enum": ["CREATING", "CREATED", "DELETING", "FAILED", "MISCONFIGURED", "PENDING", "AVAILABLE"]}, "VolumeName": {"type": "string", "max": 203, "min": 1, "pattern": "^[^\\u0000\\u0085\\u2028\\u2029\\r\\n]{1,203}$"}, "VolumeNotFound": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>No Amazon FSx volumes were found based upon the supplied parameters.</p>", "exception": true}, "VolumePath": {"type": "string", "max": 2048, "min": 1, "pattern": "^[A-za-z0-9\\_\\.\\:\\-\\/]*$"}, "VolumeStyle": {"type": "string", "enum": ["FLEXVOL", "FLEXGROUP"]}, "VolumeType": {"type": "string", "enum": ["ONTAP", "OPENZFS"]}, "Volumes": {"type": "list", "member": {"shape": "Volume"}, "max": 50}, "VpcId": {"type": "string", "documentation": "<p>The ID of your virtual private cloud (VPC). For more information, see <a href=\"https://docs.aws.amazon.com/AmazonVPC/latest/UserGuide/VPC_Subnets.html\">VPC and subnets</a> in the <i>Amazon VPC User Guide</i>.</p>", "max": 21, "min": 12, "pattern": "^(vpc-[0-9a-f]{8,})$"}, "WeeklyTime": {"type": "string", "documentation": "<p>The preferred start time to perform weekly maintenance, formatted d:HH:MM in the UTC time zone, where d is the weekday number, from 1 through 7, beginning with Monday and ending with Sunday.</p> <p>For example, <code>1:05:00</code> specifies maintenance at 5 AM Monday.</p>", "max": 7, "min": 7, "pattern": "^[1-7]:([01]\\d|2[0-3]):?([0-5]\\d)$"}, "WindowsAccessAuditLogLevel": {"type": "string", "enum": ["DISABLED", "SUCCESS_ONLY", "FAILURE_ONLY", "SUCCESS_AND_FAILURE"]}, "WindowsAuditLogConfiguration": {"type": "structure", "required": ["FileAccessAuditLogLevel", "FileShareAccessAuditLogLevel"], "members": {"FileAccessAuditLogLevel": {"shape": "WindowsAccessAuditLogLevel", "documentation": "<p>Sets which attempt type is logged by Amazon FSx for file and folder accesses.</p> <ul> <li> <p> <code>SUCCESS_ONLY</code> - only successful attempts to access files or folders are logged.</p> </li> <li> <p> <code>FAILURE_ONLY</code> - only failed attempts to access files or folders are logged.</p> </li> <li> <p> <code>SUCCESS_AND_FAILURE</code> - both successful attempts and failed attempts to access files or folders are logged.</p> </li> <li> <p> <code>DISABLED</code> - access auditing of files and folders is turned off.</p> </li> </ul>"}, "FileShareAccessAuditLogLevel": {"shape": "WindowsAccessAuditLogLevel", "documentation": "<p>Sets which attempt type is logged by Amazon FSx for file share accesses.</p> <ul> <li> <p> <code>SUCCESS_ONLY</code> - only successful attempts to access file shares are logged.</p> </li> <li> <p> <code>FAILURE_ONLY</code> - only failed attempts to access file shares are logged.</p> </li> <li> <p> <code>SUCCESS_AND_FAILURE</code> - both successful attempts and failed attempts to access file shares are logged.</p> </li> <li> <p> <code>DISABLED</code> - access auditing of file shares is turned off.</p> </li> </ul>"}, "AuditLogDestination": {"shape": "GeneralARN", "documentation": "<p>The Amazon Resource Name (ARN) for the destination of the audit logs. The destination can be any Amazon CloudWatch Logs log group ARN or Amazon Kinesis Data Firehose delivery stream ARN.</p> <p>The name of the Amazon CloudWatch Logs log group must begin with the <code>/aws/fsx</code> prefix. The name of the Amazon Kinesis Data Firehose delivery stream must begin with the <code>aws-fsx</code> prefix.</p> <p>The destination ARN (either CloudWatch Logs log group or Kinesis Data Firehose delivery stream) must be in the same Amazon Web Services partition, Amazon Web Services Region, and Amazon Web Services account as your Amazon FSx file system.</p>"}}, "documentation": "<p>The configuration that Amazon FSx for Windows File Server uses to audit and log user accesses of files, folders, and file shares on the Amazon FSx for Windows File Server file system. For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/WindowsGuide/file-access-auditing.html\"> File access auditing</a>.</p>"}, "WindowsAuditLogCreateConfiguration": {"type": "structure", "required": ["FileAccessAuditLogLevel", "FileShareAccessAuditLogLevel"], "members": {"FileAccessAuditLogLevel": {"shape": "WindowsAccessAuditLogLevel", "documentation": "<p>Sets which attempt type is logged by Amazon FSx for file and folder accesses.</p> <ul> <li> <p> <code>SUCCESS_ONLY</code> - only successful attempts to access files or folders are logged.</p> </li> <li> <p> <code>FAILURE_ONLY</code> - only failed attempts to access files or folders are logged.</p> </li> <li> <p> <code>SUCCESS_AND_FAILURE</code> - both successful attempts and failed attempts to access files or folders are logged.</p> </li> <li> <p> <code>DISABLED</code> - access auditing of files and folders is turned off.</p> </li> </ul>"}, "FileShareAccessAuditLogLevel": {"shape": "WindowsAccessAuditLogLevel", "documentation": "<p>Sets which attempt type is logged by Amazon FSx for file share accesses.</p> <ul> <li> <p> <code>SUCCESS_ONLY</code> - only successful attempts to access file shares are logged.</p> </li> <li> <p> <code>FAILURE_ONLY</code> - only failed attempts to access file shares are logged.</p> </li> <li> <p> <code>SUCCESS_AND_FAILURE</code> - both successful attempts and failed attempts to access file shares are logged.</p> </li> <li> <p> <code>DISABLED</code> - access auditing of file shares is turned off.</p> </li> </ul>"}, "AuditLogDestination": {"shape": "GeneralARN", "documentation": "<p>The Amazon Resource Name (ARN) that specifies the destination of the audit logs.</p> <p>The destination can be any Amazon CloudWatch Logs log group ARN or Amazon Kinesis Data Firehose delivery stream ARN, with the following requirements:</p> <ul> <li> <p>The destination ARN that you provide (either CloudWatch Logs log group or Kinesis Data Firehose delivery stream) must be in the same Amazon Web Services partition, Amazon Web Services Region, and Amazon Web Services account as your Amazon FSx file system.</p> </li> <li> <p>The name of the Amazon CloudWatch Logs log group must begin with the <code>/aws/fsx</code> prefix. The name of the Amazon Kinesis Data Firehose delivery stream must begin with the <code>aws-fsx</code> prefix.</p> </li> <li> <p>If you do not provide a destination in <code>AuditLogDestination</code>, Amazon FSx will create and use a log stream in the CloudWatch Logs <code>/aws/fsx/windows</code> log group.</p> </li> <li> <p>If <code>AuditLogDestination</code> is provided and the resource does not exist, the request will fail with a <code>BadRequest</code> error.</p> </li> <li> <p>If <code>FileAccessAuditLogLevel</code> and <code>FileShareAccessAuditLogLevel</code> are both set to <code>DISABLED</code>, you cannot specify a destination in <code>AuditLogDestination</code>.</p> </li> </ul>"}}, "documentation": "<p>The Windows file access auditing configuration used when creating or updating an Amazon FSx for Windows File Server file system.</p>"}, "WindowsDeploymentType": {"type": "string", "enum": ["MULTI_AZ_1", "SINGLE_AZ_1", "SINGLE_AZ_2"]}, "WindowsFileSystemConfiguration": {"type": "structure", "members": {"ActiveDirectoryId": {"shape": "DirectoryId", "documentation": "<p>The ID for an existing Amazon Web Services Managed Microsoft Active Directory instance that the file system is joined to.</p>"}, "SelfManagedActiveDirectoryConfiguration": {"shape": "SelfManagedActiveDirectoryAttributes"}, "DeploymentType": {"shape": "WindowsDeploymentType", "documentation": "<p>Specifies the file system deployment type, valid values are the following:</p> <ul> <li> <p> <code>MULTI_AZ_1</code> - Specifies a high availability file system that is configured for Multi-AZ redundancy to tolerate temporary Availability Zone (AZ) unavailability, and supports SSD and HDD storage.</p> </li> <li> <p> <code>SINGLE_AZ_1</code> - (<PERSON><PERSON>ult) Specifies a file system that is configured for single AZ redundancy, only supports SSD storage.</p> </li> <li> <p> <code>SINGLE_AZ_2</code> - Latest generation Single AZ file system. Specifies a file system that is configured for single AZ redundancy and supports SSD and HDD storage.</p> </li> </ul> <p>For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/WindowsGuide/high-availability-multiAZ.html\">Single-AZ and Multi-AZ File Systems</a>.</p>"}, "RemoteAdministrationEndpoint": {"shape": "DNSName", "documentation": "<p>For <code>MULTI_AZ_1</code> deployment types, use this endpoint when performing administrative tasks on the file system using Amazon FSx Remote PowerShell.</p> <p>For <code>SINGLE_AZ_1</code> and <code>SINGLE_AZ_2</code> deployment types, this is the DNS name of the file system.</p> <p>This endpoint is temporarily unavailable when the file system is undergoing maintenance.</p>"}, "PreferredSubnetId": {"shape": "SubnetId", "documentation": "<p>For <code>MULTI_AZ_1</code> deployment types, it specifies the ID of the subnet where the preferred file server is located. Must be one of the two subnet IDs specified in <code>SubnetIds</code> property. Amazon FSx serves traffic from this subnet except in the event of a failover to the secondary file server.</p> <p>For <code>SINGLE_AZ_1</code> and <code>SINGLE_AZ_2</code> deployment types, this value is the same as that for <code>SubnetIDs</code>. For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/WindowsGuide/high-availability-multiAZ.html#single-multi-az-resources\">Availability and durability: Single-AZ and Multi-AZ file systems</a>.</p>"}, "PreferredFileServerIp": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>For <code>MULTI_AZ_1</code> deployment types, the IP address of the primary, or preferred, file server.</p> <p>Use this IP address when mounting the file system on Linux SMB clients or Windows SMB clients that are not joined to a Microsoft Active Directory. Applicable for all Windows file system deployment types. This IP address is temporarily unavailable when the file system is undergoing maintenance. For Linux and Windows SMB clients that are joined to an Active Directory, use the file system's DNSName instead. For more information on mapping and mounting file shares, see <a href=\"https://docs.aws.amazon.com/fsx/latest/WindowsGuide/accessing-file-shares.html\">Accessing File Shares</a>.</p>"}, "ThroughputCapacity": {"shape": "MegabytesPerSecond", "documentation": "<p>The throughput of the Amazon FSx file system, measured in megabytes per second.</p>"}, "MaintenanceOperationsInProgress": {"shape": "FileSystemMaintenanceOperations", "documentation": "<p>The list of maintenance operations in progress for this file system.</p>"}, "WeeklyMaintenanceStartTime": {"shape": "WeeklyTime", "documentation": "<p>The preferred start time to perform weekly maintenance, formatted d:HH:MM in the UTC time zone. d is the weekday number, from 1 through 7, beginning with Monday and ending with Sunday.</p>"}, "DailyAutomaticBackupStartTime": {"shape": "DailyTime", "documentation": "<p>The preferred time to take daily automatic backups, in the UTC time zone.</p>"}, "AutomaticBackupRetentionDays": {"shape": "AutomaticBackupRetentionDays", "documentation": "<p>The number of days to retain automatic backups. Setting this to 0 disables automatic backups. You can retain automatic backups for a maximum of 90 days.</p>"}, "CopyTagsToBackups": {"shape": "Flag", "documentation": "<p>A boolean flag indicating whether tags on the file system should be copied to backups. This value defaults to false. If it's set to true, all tags on the file system are copied to all automatic backups and any user-initiated backups where the user doesn't specify any tags. If this value is true, and you specify one or more tags, only the specified tags are copied to backups. If you specify one or more tags when creating a user-initiated backup, no tags are copied from the file system, regardless of this value.</p>"}, "Aliases": {"shape": "Aliases"}, "AuditLogConfiguration": {"shape": "WindowsAuditLogConfiguration", "documentation": "<p>The configuration that Amazon FSx for Windows File Server uses to audit and log user accesses of files, folders, and file shares on the Amazon FSx for Windows File Server file system.</p>"}, "DiskIopsConfiguration": {"shape": "DiskIopsConfiguration", "documentation": "<p>The SSD IOPS (input/output operations per second) configuration for an Amazon FSx for Windows file system. By default, Amazon FSx automatically provisions 3 IOPS per GiB of storage capacity. You can provision additional IOPS per GiB of storage, up to the maximum limit associated with your chosen throughput capacity.</p>"}}, "documentation": "<p>The configuration for this Microsoft Windows file system.</p>"}}, "documentation": "<p>Amazon FSx is a fully managed service that makes it easy for storage and application administrators to launch and use shared file storage.</p>"}