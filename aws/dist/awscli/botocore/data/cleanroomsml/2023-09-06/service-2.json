{"version": "2.0", "metadata": {"apiVersion": "2023-09-06", "auth": ["aws.auth#sigv4"], "endpointPrefix": "cleanrooms-ml", "protocol": "rest-json", "protocols": ["rest-json"], "serviceFullName": "AWS Clean Rooms ML", "serviceId": "CleanRoomsML", "signatureVersion": "v4", "signingName": "cleanrooms-ml", "uid": "cleanroomsml-2023-09-06"}, "operations": {"CancelTrainedModel": {"name": "CancelTrainedModel", "http": {"method": "PATCH", "requestUri": "/memberships/{membershipIdentifier}/trained-models/{trainedModelArn}", "responseCode": 200}, "input": {"shape": "CancelTrainedModelRequest"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Submits a request to cancel the trained model job.</p>", "idempotent": true}, "CancelTrainedModelInferenceJob": {"name": "CancelTrainedModelInferenceJob", "http": {"method": "PATCH", "requestUri": "/memberships/{membershipIdentifier}/trained-model-inference-jobs/{trainedModelInferenceJobArn}", "responseCode": 200}, "input": {"shape": "CancelTrainedModelInferenceJobRequest"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Submits a request to cancel a trained model inference job.</p>", "idempotent": true}, "CreateAudienceModel": {"name": "CreateAudienceModel", "http": {"method": "POST", "requestUri": "/audience-model", "responseCode": 200}, "input": {"shape": "CreateAudienceModelRequest"}, "output": {"shape": "CreateAudienceModelResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Defines the information necessary to create an audience model. An audience model is a machine learning model that Clean Rooms ML trains to measure similarity between users. Clean Rooms ML manages training and storing the audience model. The audience model can be used in multiple calls to the <a>StartAudienceGenerationJob</a> API.</p>", "idempotent": true}, "CreateConfiguredAudienceModel": {"name": "CreateConfiguredAudienceModel", "http": {"method": "POST", "requestUri": "/configured-audience-model", "responseCode": 200}, "input": {"shape": "CreateConfiguredAudienceModelRequest"}, "output": {"shape": "CreateConfiguredAudienceModelResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Defines the information necessary to create a configured audience model.</p>", "idempotent": true}, "CreateConfiguredModelAlgorithm": {"name": "CreateConfiguredModelAlgorithm", "http": {"method": "POST", "requestUri": "/configured-model-algorithms", "responseCode": 200}, "input": {"shape": "CreateConfiguredModelAlgorithmRequest"}, "output": {"shape": "CreateConfiguredModelAlgorithmResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates a configured model algorithm using a container image stored in an ECR repository.</p>", "idempotent": true}, "CreateConfiguredModelAlgorithmAssociation": {"name": "CreateConfiguredModelAlgorithmAssociation", "http": {"method": "POST", "requestUri": "/memberships/{membershipIdentifier}/configured-model-algorithm-associations", "responseCode": 200}, "input": {"shape": "CreateConfiguredModelAlgorithmAssociationRequest"}, "output": {"shape": "CreateConfiguredModelAlgorithmAssociationResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Associates a configured model algorithm to a collaboration for use by any member of the collaboration.</p>", "idempotent": true}, "CreateMLInputChannel": {"name": "CreateMLInputChannel", "http": {"method": "POST", "requestUri": "/memberships/{membershipIdentifier}/ml-input-channels", "responseCode": 200}, "input": {"shape": "CreateMLInputChannelRequest"}, "output": {"shape": "CreateMLInputChannelResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Provides the information to create an ML input channel. An ML input channel is the result of a query that can be used for ML modeling.</p>", "idempotent": true}, "CreateTrainedModel": {"name": "CreateTrainedModel", "http": {"method": "POST", "requestUri": "/memberships/{membershipIdentifier}/trained-models", "responseCode": 200}, "input": {"shape": "CreateTrainedModelRequest"}, "output": {"shape": "CreateTrainedModelResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServiceException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates a trained model from an associated configured model algorithm using data from any member of the collaboration.</p>", "idempotent": true}, "CreateTrainingDataset": {"name": "CreateTrainingDataset", "http": {"method": "POST", "requestUri": "/training-dataset", "responseCode": 200}, "input": {"shape": "CreateTrainingDatasetRequest"}, "output": {"shape": "CreateTrainingDatasetResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Defines the information necessary to create a training dataset. In Clean Rooms ML, the <code>TrainingDataset</code> is metadata that points to a Glue table, which is read only during <code>AudienceModel</code> creation.</p>", "idempotent": true}, "DeleteAudienceGenerationJob": {"name": "DeleteAudienceGenerationJob", "http": {"method": "DELETE", "requestUri": "/audience-generation-job/{audienceGenerationJobArn}", "responseCode": 200}, "input": {"shape": "DeleteAudienceGenerationJobRequest"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Deletes the specified audience generation job, and removes all data associated with the job.</p>", "idempotent": true}, "DeleteAudienceModel": {"name": "DeleteAudienceModel", "http": {"method": "DELETE", "requestUri": "/audience-model/{audienceModelArn}", "responseCode": 200}, "input": {"shape": "DeleteAudienceModelRequest"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Specifies an audience model that you want to delete. You can't delete an audience model if there are any configured audience models that depend on the audience model.</p>", "idempotent": true}, "DeleteConfiguredAudienceModel": {"name": "DeleteConfiguredAudienceModel", "http": {"method": "DELETE", "requestUri": "/configured-audience-model/{configuredAudienceModelArn}", "responseCode": 200}, "input": {"shape": "DeleteConfiguredAudienceModelRequest"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Deletes the specified configured audience model. You can't delete a configured audience model if there are any lookalike models that use the configured audience model. If you delete a configured audience model, it will be removed from any collaborations that it is associated to.</p>", "idempotent": true}, "DeleteConfiguredAudienceModelPolicy": {"name": "DeleteConfiguredAudienceModelPolicy", "http": {"method": "DELETE", "requestUri": "/configured-audience-model/{configuredAudienceModelArn}/policy", "responseCode": 200}, "input": {"shape": "DeleteConfiguredAudienceModelPolicyRequest"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Deletes the specified configured audience model policy.</p>", "idempotent": true}, "DeleteConfiguredModelAlgorithm": {"name": "DeleteConfiguredModelAlgorithm", "http": {"method": "DELETE", "requestUri": "/configured-model-algorithms/{configuredModelAlgorithmArn}", "responseCode": 200}, "input": {"shape": "DeleteConfiguredModelAlgorithmRequest"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Deletes a configured model algorithm.</p>", "idempotent": true}, "DeleteConfiguredModelAlgorithmAssociation": {"name": "DeleteConfiguredModelAlgorithmAssociation", "http": {"method": "DELETE", "requestUri": "/memberships/{membershipIdentifier}/configured-model-algorithm-associations/{configuredModelAlgorithmAssociationArn}", "responseCode": 200}, "input": {"shape": "DeleteConfiguredModelAlgorithmAssociationRequest"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Deletes a configured model algorithm association.</p>", "idempotent": true}, "DeleteMLConfiguration": {"name": "DeleteMLConfiguration", "http": {"method": "DELETE", "requestUri": "/memberships/{membershipIdentifier}/ml-configurations", "responseCode": 200}, "input": {"shape": "DeleteMLConfigurationRequest"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Deletes a ML modeling configuration.</p>", "idempotent": true}, "DeleteMLInputChannelData": {"name": "DeleteMLInputChannelData", "http": {"method": "DELETE", "requestUri": "/memberships/{membershipIdentifier}/ml-input-channels/{mlInputChannelArn}", "responseCode": 200}, "input": {"shape": "DeleteMLInputChannelDataRequest"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Provides the information necessary to delete an ML input channel.</p>", "idempotent": true}, "DeleteTrainedModelOutput": {"name": "DeleteTrainedModelOutput", "http": {"method": "DELETE", "requestUri": "/memberships/{membershipIdentifier}/trained-models/{trainedModelArn}", "responseCode": 200}, "input": {"shape": "DeleteTrainedModelOutputRequest"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Deletes the model artifacts stored by the service.</p>", "idempotent": true}, "DeleteTrainingDataset": {"name": "DeleteTrainingDataset", "http": {"method": "DELETE", "requestUri": "/training-dataset/{trainingDatasetArn}", "responseCode": 200}, "input": {"shape": "DeleteTrainingDatasetRequest"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Specifies a training dataset that you want to delete. You can't delete a training dataset if there are any audience models that depend on the training dataset. In Clean Rooms ML, the <code>TrainingDataset</code> is metadata that points to a Glue table, which is read only during <code>AudienceModel</code> creation. This action deletes the metadata.</p>", "idempotent": true}, "GetAudienceGenerationJob": {"name": "GetAudienceGenerationJob", "http": {"method": "GET", "requestUri": "/audience-generation-job/{audienceGenerationJobArn}", "responseCode": 200}, "input": {"shape": "GetAudienceGenerationJobRequest"}, "output": {"shape": "GetAudienceGenerationJobResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns information about an audience generation job.</p>"}, "GetAudienceModel": {"name": "GetAudienceModel", "http": {"method": "GET", "requestUri": "/audience-model/{audienceModelArn}", "responseCode": 200}, "input": {"shape": "GetAudienceModelRequest"}, "output": {"shape": "GetAudienceModelResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns information about an audience model</p>"}, "GetCollaborationConfiguredModelAlgorithmAssociation": {"name": "GetCollaborationConfiguredModelAlgorithmAssociation", "http": {"method": "GET", "requestUri": "/collaborations/{collaborationIdentifier}/configured-model-algorithm-associations/{configuredModelAlgorithmAssociationArn}", "responseCode": 200}, "input": {"shape": "GetCollaborationConfiguredModelAlgorithmAssociationRequest"}, "output": {"shape": "GetCollaborationConfiguredModelAlgorithmAssociationResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Returns information about the configured model algorithm association in a collaboration.</p>"}, "GetCollaborationMLInputChannel": {"name": "GetCollaborationMLInputChannel", "http": {"method": "GET", "requestUri": "/collaborations/{collaborationIdentifier}/ml-input-channels/{mlInputChannelArn}", "responseCode": 200}, "input": {"shape": "GetCollaborationMLInputChannelRequest"}, "output": {"shape": "GetCollaborationMLInputChannelResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Returns information about a specific ML input channel in a collaboration.</p>"}, "GetCollaborationTrainedModel": {"name": "GetCollaborationTrainedModel", "http": {"method": "GET", "requestUri": "/collaborations/{collaborationIdentifier}/trained-models/{trainedModelArn}", "responseCode": 200}, "input": {"shape": "GetCollaborationTrainedModelRequest"}, "output": {"shape": "GetCollaborationTrainedModelResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Returns information about a trained model in a collaboration.</p>"}, "GetConfiguredAudienceModel": {"name": "GetConfiguredAudienceModel", "http": {"method": "GET", "requestUri": "/configured-audience-model/{configuredAudienceModelArn}", "responseCode": 200}, "input": {"shape": "GetConfiguredAudienceModelRequest"}, "output": {"shape": "GetConfiguredAudienceModelResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns information about a specified configured audience model.</p>"}, "GetConfiguredAudienceModelPolicy": {"name": "GetConfiguredAudienceModelPolicy", "http": {"method": "GET", "requestUri": "/configured-audience-model/{configuredAudienceModelArn}/policy", "responseCode": 200}, "input": {"shape": "GetConfiguredAudienceModelPolicyRequest"}, "output": {"shape": "GetConfiguredAudienceModelPolicyResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns information about a configured audience model policy.</p>"}, "GetConfiguredModelAlgorithm": {"name": "GetConfiguredModelAlgorithm", "http": {"method": "GET", "requestUri": "/configured-model-algorithms/{configuredModelAlgorithmArn}", "responseCode": 200}, "input": {"shape": "GetConfiguredModelAlgorithmRequest"}, "output": {"shape": "GetConfiguredModelAlgorithmResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns information about a configured model algorithm.</p>"}, "GetConfiguredModelAlgorithmAssociation": {"name": "GetConfiguredModelAlgorithmAssociation", "http": {"method": "GET", "requestUri": "/memberships/{membershipIdentifier}/configured-model-algorithm-associations/{configuredModelAlgorithmAssociationArn}", "responseCode": 200}, "input": {"shape": "GetConfiguredModelAlgorithmAssociationRequest"}, "output": {"shape": "GetConfiguredModelAlgorithmAssociationResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Returns information about a configured model algorithm association.</p>"}, "GetMLConfiguration": {"name": "GetMLConfiguration", "http": {"method": "GET", "requestUri": "/memberships/{membershipIdentifier}/ml-configurations", "responseCode": 200}, "input": {"shape": "GetMLConfigurationRequest"}, "output": {"shape": "GetMLConfigurationResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Returns information about a specific ML configuration.</p>"}, "GetMLInputChannel": {"name": "GetMLInputChannel", "http": {"method": "GET", "requestUri": "/memberships/{membershipIdentifier}/ml-input-channels/{mlInputChannelArn}", "responseCode": 200}, "input": {"shape": "GetMLInputChannelRequest"}, "output": {"shape": "GetMLInputChannelResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Returns information about an ML input channel.</p>"}, "GetTrainedModel": {"name": "GetTrainedModel", "http": {"method": "GET", "requestUri": "/memberships/{membershipIdentifier}/trained-models/{trainedModelArn}", "responseCode": 200}, "input": {"shape": "GetTrainedModelRequest"}, "output": {"shape": "GetTrainedModelResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Returns information about a trained model.</p>"}, "GetTrainedModelInferenceJob": {"name": "GetTrainedModelInferenceJob", "http": {"method": "GET", "requestUri": "/memberships/{membershipIdentifier}/trained-model-inference-jobs/{trainedModelInferenceJobArn}", "responseCode": 200}, "input": {"shape": "GetTrainedModelInferenceJobRequest"}, "output": {"shape": "GetTrainedModelInferenceJobResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Returns information about a trained model inference job.</p>"}, "GetTrainingDataset": {"name": "GetTrainingDataset", "http": {"method": "GET", "requestUri": "/training-dataset/{trainingDatasetArn}", "responseCode": 200}, "input": {"shape": "GetTrainingDatasetRequest"}, "output": {"shape": "GetTrainingDatasetResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns information about a training dataset.</p>"}, "ListAudienceExportJobs": {"name": "ListAudienceExportJobs", "http": {"method": "GET", "requestUri": "/audience-export-job", "responseCode": 200}, "input": {"shape": "ListAudienceExportJobsRequest"}, "output": {"shape": "ListAudienceExportJobsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Returns a list of the audience export jobs.</p>"}, "ListAudienceGenerationJobs": {"name": "ListAudienceGenerationJobs", "http": {"method": "GET", "requestUri": "/audience-generation-job", "responseCode": 200}, "input": {"shape": "ListAudienceGenerationJobsRequest"}, "output": {"shape": "ListAudienceGenerationJobsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Returns a list of audience generation jobs.</p>"}, "ListAudienceModels": {"name": "ListAudienceModels", "http": {"method": "GET", "requestUri": "/audience-model", "responseCode": 200}, "input": {"shape": "ListAudienceModelsRequest"}, "output": {"shape": "ListAudienceModelsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Returns a list of audience models.</p>"}, "ListCollaborationConfiguredModelAlgorithmAssociations": {"name": "ListCollaborationConfiguredModelAlgorithmAssociations", "http": {"method": "GET", "requestUri": "/collaborations/{collaborationIdentifier}/configured-model-algorithm-associations", "responseCode": 200}, "input": {"shape": "ListCollaborationConfiguredModelAlgorithmAssociationsRequest"}, "output": {"shape": "ListCollaborationConfiguredModelAlgorithmAssociationsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Returns a list of the configured model algorithm associations in a collaboration.</p>"}, "ListCollaborationMLInputChannels": {"name": "ListCollaborationMLInputChannels", "http": {"method": "GET", "requestUri": "/collaborations/{collaborationIdentifier}/ml-input-channels", "responseCode": 200}, "input": {"shape": "ListCollaborationMLInputChannelsRequest"}, "output": {"shape": "ListCollaborationMLInputChannelsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Returns a list of the ML input channels in a collaboration.</p>"}, "ListCollaborationTrainedModelExportJobs": {"name": "ListCollaborationTrainedModelExportJobs", "http": {"method": "GET", "requestUri": "/collaborations/{collaborationIdentifier}/trained-models/{trainedModelArn}/export-jobs", "responseCode": 200}, "input": {"shape": "ListCollaborationTrainedModelExportJobsRequest"}, "output": {"shape": "ListCollaborationTrainedModelExportJobsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Returns a list of the export jobs for a trained model in a collaboration.</p>"}, "ListCollaborationTrainedModelInferenceJobs": {"name": "ListCollaborationTrainedModelInferenceJobs", "http": {"method": "GET", "requestUri": "/collaborations/{collaborationIdentifier}/trained-model-inference-jobs", "responseCode": 200}, "input": {"shape": "ListCollaborationTrainedModelInferenceJobsRequest"}, "output": {"shape": "ListCollaborationTrainedModelInferenceJobsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Returns a list of trained model inference jobs in a specified collaboration.</p>"}, "ListCollaborationTrainedModels": {"name": "ListCollaborationTrainedModels", "http": {"method": "GET", "requestUri": "/collaborations/{collaborationIdentifier}/trained-models", "responseCode": 200}, "input": {"shape": "ListCollaborationTrainedModelsRequest"}, "output": {"shape": "ListCollaborationTrainedModelsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Returns a list of the trained models in a collaboration.</p>"}, "ListConfiguredAudienceModels": {"name": "ListConfiguredAudienceModels", "http": {"method": "GET", "requestUri": "/configured-audience-model", "responseCode": 200}, "input": {"shape": "ListConfiguredAudienceModelsRequest"}, "output": {"shape": "ListConfiguredAudienceModelsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Returns a list of the configured audience models.</p>"}, "ListConfiguredModelAlgorithmAssociations": {"name": "ListConfiguredModelAlgorithmAssociations", "http": {"method": "GET", "requestUri": "/memberships/{membershipIdentifier}/configured-model-algorithm-associations", "responseCode": 200}, "input": {"shape": "ListConfiguredModelAlgorithmAssociationsRequest"}, "output": {"shape": "ListConfiguredModelAlgorithmAssociationsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Returns a list of configured model algorithm associations.</p>"}, "ListConfiguredModelAlgorithms": {"name": "ListConfiguredModelAlgorithms", "http": {"method": "GET", "requestUri": "/configured-model-algorithms", "responseCode": 200}, "input": {"shape": "ListConfiguredModelAlgorithmsRequest"}, "output": {"shape": "ListConfiguredModelAlgorithmsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Returns a list of configured model algorithms.</p>"}, "ListMLInputChannels": {"name": "ListMLInputChannels", "http": {"method": "GET", "requestUri": "/memberships/{membershipIdentifier}/ml-input-channels", "responseCode": 200}, "input": {"shape": "ListMLInputChannelsRequest"}, "output": {"shape": "ListMLInputChannelsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Returns a list of ML input channels.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns a list of tags for a provided resource.</p>"}, "ListTrainedModelInferenceJobs": {"name": "ListTrainedModelInferenceJobs", "http": {"method": "GET", "requestUri": "/memberships/{membershipIdentifier}/trained-model-inference-jobs", "responseCode": 200}, "input": {"shape": "ListTrainedModelInferenceJobsRequest"}, "output": {"shape": "ListTrainedModelInferenceJobsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Returns a list of trained model inference jobs that match the request parameters.</p>"}, "ListTrainedModelVersions": {"name": "ListTrainedModelVersions", "http": {"method": "GET", "requestUri": "/memberships/{membershipIdentifier}/trained-models/{trainedModelArn}/versions", "responseCode": 200}, "input": {"shape": "ListTrainedModelVersionsRequest"}, "output": {"shape": "ListTrainedModelVersionsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Returns a list of trained model versions for a specified trained model. This operation allows you to view all versions of a trained model, including information about their status and creation details. You can use this to track the evolution of your trained models and select specific versions for inference or further training.</p>"}, "ListTrainedModels": {"name": "ListTrainedModels", "http": {"method": "GET", "requestUri": "/memberships/{membershipIdentifier}/trained-models", "responseCode": 200}, "input": {"shape": "ListTrainedModelsRequest"}, "output": {"shape": "ListTrainedModelsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Returns a list of trained models.</p>"}, "ListTrainingDatasets": {"name": "ListTrainingDatasets", "http": {"method": "GET", "requestUri": "/training-dataset", "responseCode": 200}, "input": {"shape": "ListTrainingDatasetsRequest"}, "output": {"shape": "ListTrainingDatasetsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Returns a list of training datasets.</p>"}, "PutConfiguredAudienceModelPolicy": {"name": "PutConfiguredAudienceModelPolicy", "http": {"method": "PUT", "requestUri": "/configured-audience-model/{configuredAudienceModelArn}/policy", "responseCode": 200}, "input": {"shape": "PutConfiguredAudienceModelPolicyRequest"}, "output": {"shape": "PutConfiguredAudienceModelPolicyResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Create or update the resource policy for a configured audience model.</p>", "idempotent": true}, "PutMLConfiguration": {"name": "PutMLConfiguration", "http": {"method": "PUT", "requestUri": "/memberships/{membershipIdentifier}/ml-configurations", "responseCode": 200}, "input": {"shape": "PutMLConfigurationRequest"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Assigns information about an ML configuration.</p>", "idempotent": true}, "StartAudienceExportJob": {"name": "StartAudienceExportJob", "http": {"method": "POST", "requestUri": "/audience-export-job", "responseCode": 200}, "input": {"shape": "StartAudienceExportJobRequest"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Export an audience of a specified size after you have generated an audience.</p>", "idempotent": true}, "StartAudienceGenerationJob": {"name": "StartAudienceGenerationJob", "http": {"method": "POST", "requestUri": "/audience-generation-job", "responseCode": 200}, "input": {"shape": "StartAudienceGenerationJobRequest"}, "output": {"shape": "StartAudienceGenerationJobResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Information necessary to start the audience generation job.</p>", "idempotent": true}, "StartTrainedModelExportJob": {"name": "StartTrainedModelExportJob", "http": {"method": "POST", "requestUri": "/memberships/{membershipIdentifier}/trained-models/{trainedModelArn}/export-jobs", "responseCode": 200}, "input": {"shape": "StartTrainedModelExportJobRequest"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Provides the information necessary to start a trained model export job.</p>", "idempotent": true}, "StartTrainedModelInferenceJob": {"name": "StartTrainedModelInferenceJob", "http": {"method": "POST", "requestUri": "/memberships/{membershipIdentifier}/trained-model-inference-jobs", "responseCode": 200}, "input": {"shape": "StartTrainedModelInferenceJobRequest"}, "output": {"shape": "StartTrainedModelInferenceJobResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Defines the information necessary to begin a trained model inference job.</p>", "idempotent": true}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Adds metadata tags to a specified resource.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Removes metadata tags from a specified resource.</p>", "idempotent": true}, "UpdateConfiguredAudienceModel": {"name": "UpdateConfiguredAudienceModel", "http": {"method": "PATCH", "requestUri": "/configured-audience-model/{configuredAudienceModelArn}", "responseCode": 200}, "input": {"shape": "UpdateConfiguredAudienceModelRequest"}, "output": {"shape": "UpdateConfiguredAudienceModelResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Provides the information necessary to update a configured audience model. Updates that impact audience generation jobs take effect when a new job starts, but do not impact currently running jobs.</p>", "idempotent": true}}, "shapes": {"AccessDeniedException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>You do not have sufficient access to perform this action.</p>", "error": {"httpStatusCode": 403, "senderFault": true}, "exception": true}, "AccountId": {"type": "string", "max": 12, "min": 12, "pattern": "[0-9]{12}"}, "AccountIdList": {"type": "list", "member": {"shape": "String"}, "max": 5, "min": 1}, "AlgorithmImage": {"type": "string", "max": 255, "min": 1, "pattern": ".*"}, "AnalysisTemplateArn": {"type": "string", "max": 200, "min": 0, "pattern": "arn:aws[-a-z]*:cleanrooms:[\\w]{2}-[\\w]{4,9}-[\\d]:[\\d]{12}:membership/[\\d\\w-]+/analysistemplate/[\\d\\w-]+"}, "AudienceDestination": {"type": "structure", "required": ["s3Destination"], "members": {"s3Destination": {"shape": "S3ConfigMap", "documentation": "<p>The Amazon S3 bucket and path for the configured audience.</p>"}}, "documentation": "<p>Defines the Amazon S3 bucket where the configured audience is stored.</p>"}, "AudienceExportJobList": {"type": "list", "member": {"shape": "AudienceExportJobSummary"}}, "AudienceExportJobStatus": {"type": "string", "enum": ["CREATE_PENDING", "CREATE_IN_PROGRESS", "CREATE_FAILED", "ACTIVE"]}, "AudienceExportJobSummary": {"type": "structure", "required": ["createTime", "updateTime", "name", "audienceGenerationJobArn", "audienceSize", "status"], "members": {"createTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time at which the audience export job was created.</p>"}, "updateTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The most recent time at which the audience export job was updated.</p>"}, "name": {"shape": "NameString", "documentation": "<p>The name of the audience export job.</p>"}, "audienceGenerationJobArn": {"shape": "AudienceGenerationJobArn", "documentation": "<p>The Amazon Resource Name (ARN) of the audience generation job that was exported.</p>"}, "audienceSize": {"shape": "AudienceSize"}, "description": {"shape": "ResourceDescription", "documentation": "<p>The description of the audience export job.</p>"}, "status": {"shape": "AudienceExportJobStatus", "documentation": "<p>The status of the audience export job.</p>"}, "statusDetails": {"shape": "StatusDetails"}, "outputLocation": {"shape": "S3Path", "documentation": "<p>The Amazon S3 bucket where the audience export is stored.</p>"}}, "documentation": "<p>Provides information about the audience export job.</p>"}, "AudienceGenerationJobArn": {"type": "string", "max": 2048, "min": 20, "pattern": "arn:aws[-a-z]*:cleanrooms-ml:[-a-z0-9]+:[0-9]{12}:audience-generation-job/[-a-zA-Z0-9_/.]+"}, "AudienceGenerationJobDataSource": {"type": "structure", "required": ["roleArn"], "members": {"dataSource": {"shape": "S3ConfigMap", "documentation": "<p>Defines the Amazon S3 bucket where the seed audience for the generating audience is stored. A valid data source is a JSON line file in the following format:</p> <p> <code>{\"user_id\": \"111111\"}</code> </p> <p> <code>{\"user_id\": \"222222\"}</code> </p> <p> <code>...</code> </p>"}, "roleArn": {"shape": "IamRoleArn", "documentation": "<p>The ARN of the IAM role that can read the Amazon S3 bucket where the seed audience is stored.</p>"}, "sqlParameters": {"shape": "ProtectedQuerySQLParameters", "documentation": "<p>The protected SQL query parameters.</p>"}, "sqlComputeConfiguration": {"shape": "ComputeConfiguration"}}, "documentation": "<p>Defines the Amazon S3 bucket where the seed audience for the generating audience is stored.</p>"}, "AudienceGenerationJobList": {"type": "list", "member": {"shape": "AudienceGenerationJobSummary"}}, "AudienceGenerationJobStatus": {"type": "string", "enum": ["CREATE_PENDING", "CREATE_IN_PROGRESS", "CREATE_FAILED", "ACTIVE", "DELETE_PENDING", "DELETE_IN_PROGRESS", "DELETE_FAILED"]}, "AudienceGenerationJobSummary": {"type": "structure", "required": ["createTime", "updateTime", "audienceGenerationJobArn", "name", "status", "configuredAudienceModelArn"], "members": {"createTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time at which the audience generation job was created.</p>"}, "updateTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The most recent time at which the audience generation job was updated.</p>"}, "audienceGenerationJobArn": {"shape": "AudienceGenerationJobArn", "documentation": "<p>The Amazon Resource Name (ARN) of the audience generation job.</p>"}, "name": {"shape": "NameString", "documentation": "<p>The name of the audience generation job.</p>"}, "description": {"shape": "ResourceDescription", "documentation": "<p>The description of the audience generation job.</p>"}, "status": {"shape": "AudienceGenerationJobStatus", "documentation": "<p>The status of the audience generation job.</p>"}, "configuredAudienceModelArn": {"shape": "ConfiguredAudienceModelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the configured audience model that was used for this audience generation job.</p>"}, "collaborationId": {"shape": "UUID", "documentation": "<p>The identifier of the collaboration that contains this audience generation job.</p>"}, "startedBy": {"shape": "AccountId", "documentation": "<p>The AWS Account that submitted the job.</p>"}}, "documentation": "<p>Provides information about the configured audience generation job.</p>"}, "AudienceModelArn": {"type": "string", "max": 2048, "min": 20, "pattern": "arn:aws[-a-z]*:cleanrooms-ml:[-a-z0-9]+:[0-9]{12}:audience-model/[-a-zA-Z0-9_/.]+"}, "AudienceModelList": {"type": "list", "member": {"shape": "AudienceModelSummary"}}, "AudienceModelStatus": {"type": "string", "enum": ["CREATE_PENDING", "CREATE_IN_PROGRESS", "CREATE_FAILED", "ACTIVE", "DELETE_PENDING", "DELETE_IN_PROGRESS", "DELETE_FAILED"]}, "AudienceModelSummary": {"type": "structure", "required": ["createTime", "updateTime", "audienceModelArn", "name", "trainingDatasetArn", "status"], "members": {"createTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time at which the audience model was created.</p>"}, "updateTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The most recent time at which the audience model was updated.</p>"}, "audienceModelArn": {"shape": "AudienceModelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the audience model.</p>"}, "name": {"shape": "NameString", "documentation": "<p>The name of the audience model.</p>"}, "trainingDatasetArn": {"shape": "TrainingDatasetArn", "documentation": "<p>The Amazon Resource Name (ARN) of the training dataset that was used for the audience model.</p>"}, "status": {"shape": "AudienceModelStatus", "documentation": "<p>The status of the audience model.</p>"}, "description": {"shape": "ResourceDescription", "documentation": "<p>The description of the audience model.</p>"}}, "documentation": "<p>Information about the audience model.</p>"}, "AudienceQualityMetrics": {"type": "structure", "required": ["relevanceMetrics"], "members": {"relevanceMetrics": {"shape": "RelevanceMetrics", "documentation": "<p>The relevance scores of the generated audience.</p>"}, "recallMetric": {"shape": "AudienceQualityMetricsRecallMetricDouble", "documentation": "<p>The recall score of the generated audience. Recall is the percentage of the most similar users (by default, the most similar 20%) from a sample of the training data that are included in the seed audience by the audience generation job. Values range from 0-1, larger values indicate a better audience. A recall value approximately equal to the maximum bin size indicates that the audience model is equivalent to random selection. </p>"}}, "documentation": "<p>Metrics that describe the quality of the generated audience.</p>"}, "AudienceQualityMetricsRecallMetricDouble": {"type": "double", "box": true, "max": 1.0, "min": 0.0}, "AudienceSize": {"type": "structure", "required": ["type", "value"], "members": {"type": {"shape": "AudienceSizeType", "documentation": "<p>Whether the audience size is defined in absolute terms or as a percentage. You can use the <code>ABSOLUTE</code> <a>AudienceSize</a> to configure out audience sizes using the count of identifiers in the output. You can use the <code>Percentage</code> <a>AudienceSize</a> to configure sizes in the range 1-100 percent.</p>"}, "value": {"shape": "AudienceSizeValue", "documentation": "<p>Specify an audience size value.</p>"}}, "documentation": "<p>The size of the generated audience. Must match one of the sizes in the configured audience model.</p>"}, "AudienceSizeBins": {"type": "list", "member": {"shape": "AudienceSizeValue"}, "max": 25, "min": 1}, "AudienceSizeConfig": {"type": "structure", "required": ["audienceSizeType", "audienceSizeBins"], "members": {"audienceSizeType": {"shape": "AudienceSizeType", "documentation": "<p>Whether the audience output sizes are defined as an absolute number or a percentage.</p>"}, "audienceSizeBins": {"shape": "AudienceSizeBins", "documentation": "<p>An array of the different audience output sizes.</p>"}}, "documentation": "<p>Returns the relevance scores at these audience sizes when used in the <a>GetAudienceGenerationJob</a> for a specified audience generation job and configured audience model.</p> <p>Specifies the list of allowed <code>audienceSize</code> values when used in the <a>StartAudienceExportJob</a> for an audience generation job. You can use the <code>ABSOLUTE</code> <a>AudienceSize</a> to configure out audience sizes using the count of identifiers in the output. You can use the <code>Percentage</code> <a>AudienceSize</a> to configure sizes in the range 1-100 percent.</p>"}, "AudienceSizeType": {"type": "string", "enum": ["ABSOLUTE", "PERCENTAGE"]}, "AudienceSizeValue": {"type": "integer", "box": true, "max": 20000000, "min": 1}, "Boolean": {"type": "boolean", "box": true}, "CancelTrainedModelInferenceJobRequest": {"type": "structure", "required": ["membershipIdentifier", "trainedModelInferenceJobArn"], "members": {"membershipIdentifier": {"shape": "UUID", "documentation": "<p>The membership ID of the trained model inference job that you want to cancel.</p>", "location": "uri", "locationName": "membershipIdentifier"}, "trainedModelInferenceJobArn": {"shape": "TrainedModelInferenceJobArn", "documentation": "<p>The Amazon Resource Name (ARN) of the trained model inference job that you want to cancel.</p>", "location": "uri", "locationName": "trainedModelInferenceJobArn"}}}, "CancelTrainedModelRequest": {"type": "structure", "required": ["membershipIdentifier", "trainedModelArn"], "members": {"membershipIdentifier": {"shape": "UUID", "documentation": "<p>The membership ID of the trained model job that you want to cancel.</p>", "location": "uri", "locationName": "membershipIdentifier"}, "trainedModelArn": {"shape": "TrainedModelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the trained model job that you want to cancel.</p>", "location": "uri", "locationName": "trainedModelArn"}, "versionIdentifier": {"shape": "UUID", "documentation": "<p>The version identifier of the trained model to cancel. This parameter allows you to specify which version of the trained model you want to cancel when multiple versions exist.</p> <p>If <code>versionIdentifier</code> is not specified, the base model will be cancelled.</p>", "location": "querystring", "locationName": "versionIdentifier"}}}, "CollaborationConfiguredModelAlgorithmAssociationList": {"type": "list", "member": {"shape": "CollaborationConfiguredModelAlgorithmAssociationSummary"}}, "CollaborationConfiguredModelAlgorithmAssociationSummary": {"type": "structure", "required": ["createTime", "updateTime", "configuredModelAlgorithmAssociationArn", "name", "membershipIdentifier", "collaborationIdentifier", "configuredModelAlgorithmArn", "creatorAccountId"], "members": {"createTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time at which the configured model algorithm association was created.</p>"}, "updateTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The most recent time at which the configured model algorithm association was updated.</p>"}, "configuredModelAlgorithmAssociationArn": {"shape": "ConfiguredModelAlgorithmAssociationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the configured model algorithm association.</p>"}, "name": {"shape": "NameString", "documentation": "<p>The name of the configured model algorithm association.</p>"}, "description": {"shape": "ResourceDescription", "documentation": "<p>The description of the configured model algorithm association.</p>"}, "membershipIdentifier": {"shape": "UUID", "documentation": "<p>The membership ID of the member that created the configured model algorithm association.</p>"}, "collaborationIdentifier": {"shape": "UUID", "documentation": "<p>The collaboration ID of the collaboration that contains the configured model algorithm association.</p>"}, "configuredModelAlgorithmArn": {"shape": "ConfiguredModelAlgorithmArn", "documentation": "<p>The Amazon Resource Name (ARN) of the configured model algorithm that is associated to the collaboration.</p>"}, "creatorAccountId": {"shape": "AccountId", "documentation": "<p>The account ID of the member that created the configured model algorithm association.</p>"}}, "documentation": "<p>Provides summary information about a configured model algorithm in a collaboration.</p>"}, "CollaborationMLInputChannelSummary": {"type": "structure", "required": ["createTime", "updateTime", "membershipIdentifier", "collaborationIdentifier", "name", "configuredModelAlgorithmAssociations", "mlInputChannelArn", "status", "creatorAccountId"], "members": {"createTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time at which the ML input channel was created.</p>"}, "updateTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The most recent time at which the ML input channel was updated.</p>"}, "membershipIdentifier": {"shape": "UUID", "documentation": "<p>The membership ID of the membership that contains the ML input channel.</p>"}, "collaborationIdentifier": {"shape": "UUID", "documentation": "<p>The collaboration ID of the collaboration that contains the ML input channel.</p>"}, "name": {"shape": "NameString", "documentation": "<p>The name of the ML input channel.</p>"}, "configuredModelAlgorithmAssociations": {"shape": "CollaborationMLInputChannelSummaryConfiguredModelAlgorithmAssociationsList", "documentation": "<p>The associated configured model algorithms used to create the ML input channel.</p>"}, "mlInputChannelArn": {"shape": "MLInputChannelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the ML input channel.</p>"}, "status": {"shape": "MLInputChannelStatus", "documentation": "<p>The status of the ML input channel.</p>"}, "creatorAccountId": {"shape": "AccountId", "documentation": "<p>The account ID of the member who created the ML input channel.</p>"}, "description": {"shape": "ResourceDescription", "documentation": "<p>The description of the ML input channel.</p>"}}, "documentation": "<p>Provides summary information about an ML input channel in a collaboration.</p>"}, "CollaborationMLInputChannelSummaryConfiguredModelAlgorithmAssociationsList": {"type": "list", "member": {"shape": "ConfiguredModelAlgorithmAssociationArn"}, "max": 1, "min": 1}, "CollaborationMLInputChannelsList": {"type": "list", "member": {"shape": "CollaborationMLInputChannelSummary"}}, "CollaborationTrainedModelExportJobList": {"type": "list", "member": {"shape": "CollaborationTrainedModelExportJobSummary"}}, "CollaborationTrainedModelExportJobSummary": {"type": "structure", "required": ["createTime", "updateTime", "name", "outputConfiguration", "status", "creatorAccountId", "trainedModelArn", "membershipIdentifier", "collaborationIdentifier"], "members": {"createTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time at which the trained model export job was created.</p>"}, "updateTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The most recent time at which the trained model export job was updated.</p>"}, "name": {"shape": "NameString", "documentation": "<p>The name of the trained model export job.</p>"}, "outputConfiguration": {"shape": "TrainedModelExportOutputConfiguration"}, "status": {"shape": "TrainedModelExportJobStatus", "documentation": "<p>The status of the trained model.</p>"}, "statusDetails": {"shape": "StatusDetails"}, "description": {"shape": "ResourceDescription", "documentation": "<p>The description of the trained model.</p>"}, "creatorAccountId": {"shape": "AccountId", "documentation": "<p>The account ID of the member that created the trained model.</p>"}, "trainedModelArn": {"shape": "TrainedModelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the trained model that is being exported.</p>"}, "trainedModelVersionIdentifier": {"shape": "UUID", "documentation": "<p>The version identifier of the trained model that was exported in this job.</p>"}, "membershipIdentifier": {"shape": "UUID", "documentation": "<p>The membership ID of the member that created the trained model export job.</p>"}, "collaborationIdentifier": {"shape": "UUID", "documentation": "<p>The collaboration ID of the collaboration that contains the trained model export job.</p>"}}, "documentation": "<p>Provides summary information about a trained model export job in a collaboration.</p>"}, "CollaborationTrainedModelInferenceJobList": {"type": "list", "member": {"shape": "CollaborationTrainedModelInferenceJobSummary"}}, "CollaborationTrainedModelInferenceJobSummary": {"type": "structure", "required": ["trainedModelInferenceJobArn", "membershipIdentifier", "trainedModelArn", "collaborationIdentifier", "status", "outputConfiguration", "name", "createTime", "updateTime", "creatorAccountId"], "members": {"trainedModelInferenceJobArn": {"shape": "TrainedModelInferenceJobArn", "documentation": "<p>The Amazon Resource Name (ARN) of the trained model inference job.</p>"}, "configuredModelAlgorithmAssociationArn": {"shape": "ConfiguredModelAlgorithmAssociationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the configured model algorithm association that is used for the trained model inference job.</p>"}, "membershipIdentifier": {"shape": "UUID", "documentation": "<p>The membership ID of the membership that contains the trained model inference job.</p>"}, "trainedModelArn": {"shape": "TrainedModelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the trained model that is used for the trained model inference job.</p>"}, "trainedModelVersionIdentifier": {"shape": "UUID", "documentation": "<p>The version identifier of the trained model that was used for inference in this job.</p>"}, "collaborationIdentifier": {"shape": "UUID", "documentation": "<p>The collaboration ID of the collaboration that contains the trained model inference job.</p>"}, "status": {"shape": "TrainedModelInferenceJobStatus", "documentation": "<p>The status of the trained model inference job.</p>"}, "outputConfiguration": {"shape": "InferenceOutputConfiguration", "documentation": "<p>Returns output configuration information for the trained model inference job.</p>"}, "name": {"shape": "NameString", "documentation": "<p>The name of the trained model inference job.</p>"}, "description": {"shape": "ResourceDescription", "documentation": "<p>The description of the trained model inference job.</p>"}, "metricsStatus": {"shape": "MetricsStatus", "documentation": "<p>the trained model inference job metrics status.</p>"}, "metricsStatusDetails": {"shape": "String", "documentation": "<p>Details about the metrics status for trained model inference job.</p>"}, "logsStatus": {"shape": "LogsStatus", "documentation": "<p>The trained model inference job logs status.</p>"}, "logsStatusDetails": {"shape": "String", "documentation": "<p>Details about the logs status for the trained model inference job.</p>"}, "createTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time at which the trained model inference job was created.</p>"}, "updateTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The most recent time at which the trained model inference job was updated.</p>"}, "creatorAccountId": {"shape": "AccountId", "documentation": "<p>The account ID that created the trained model inference job.</p>"}}, "documentation": "<p>Provides summary information about a trained model inference job in a collaboration.</p>"}, "CollaborationTrainedModelList": {"type": "list", "member": {"shape": "CollaborationTrainedModelSummary"}}, "CollaborationTrainedModelSummary": {"type": "structure", "required": ["createTime", "updateTime", "trainedModelArn", "name", "membershipIdentifier", "collaborationIdentifier", "status", "configuredModelAlgorithmAssociationArn", "creatorAccountId"], "members": {"createTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time at which the trained model was created.</p>"}, "updateTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The most recent time at which the trained model was updated.</p>"}, "trainedModelArn": {"shape": "TrainedModelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the trained model.</p>"}, "name": {"shape": "NameString", "documentation": "<p>The name of the trained model.</p>"}, "versionIdentifier": {"shape": "UUID", "documentation": "<p>The version identifier of this trained model version.</p>"}, "incrementalTrainingDataChannels": {"shape": "IncrementalTrainingDataChannelsOutput", "documentation": "<p>Information about the incremental training data channels used to create this version of the trained model.</p>"}, "description": {"shape": "ResourceDescription", "documentation": "<p>The description of the trained model.</p>"}, "membershipIdentifier": {"shape": "UUID", "documentation": "<p>The membership ID of the member that created the trained model.</p>"}, "collaborationIdentifier": {"shape": "UUID", "documentation": "<p>The collaboration ID of the collaboration that contains the trained model.</p>"}, "status": {"shape": "TrainedModelStatus", "documentation": "<p>The status of the trained model.</p>"}, "configuredModelAlgorithmAssociationArn": {"shape": "ConfiguredModelAlgorithmAssociationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the configured model algorithm association that is used for this trained model.</p>"}, "creatorAccountId": {"shape": "AccountId", "documentation": "<p>The account ID of the member that created the trained model.</p>"}}, "documentation": "<p>Provides summary information about a trained model in a collaboration.</p>"}, "ColumnName": {"type": "string", "max": 128, "min": 1, "pattern": "[a-zA-Z0-9_](([a-zA-Z0-9_ ]+-)*([a-zA-Z0-9_ ]+))?"}, "ColumnSchema": {"type": "structure", "required": ["columnName", "columnTypes"], "members": {"columnName": {"shape": "ColumnName", "documentation": "<p>The name of a column.</p>"}, "columnTypes": {"shape": "ColumnTypeList", "documentation": "<p>The data type of column.</p>"}}, "documentation": "<p><PERSON><PERSON><PERSON> for a column.</p>"}, "ColumnType": {"type": "string", "enum": ["USER_ID", "ITEM_ID", "TIMESTAMP", "CATEGORICAL_FEATURE", "NUMERICAL_FEATURE"]}, "ColumnTypeList": {"type": "list", "member": {"shape": "ColumnType"}, "max": 1, "min": 1}, "ComputeConfiguration": {"type": "structure", "members": {"worker": {"shape": "WorkerComputeConfiguration", "documentation": "<p>The worker instances that will perform the compute work.</p>"}}, "documentation": "<p>Provides configuration information for the instances that will perform the compute work.</p>", "union": true}, "ConfiguredAudienceModelArn": {"type": "string", "max": 2048, "min": 20, "pattern": "arn:aws[-a-z]*:cleanrooms-ml:[-a-z0-9]+:[0-9]{12}:configured-audience-model/[-a-zA-Z0-9_/.]+"}, "ConfiguredAudienceModelList": {"type": "list", "member": {"shape": "ConfiguredAudienceModelSummary"}}, "ConfiguredAudienceModelOutputConfig": {"type": "structure", "required": ["destination", "roleArn"], "members": {"destination": {"shape": "AudienceDestination"}, "roleArn": {"shape": "IamRoleArn", "documentation": "<p>The ARN of the IAM role that can write the Amazon S3 bucket.</p>"}}, "documentation": "<p>Configuration information necessary for the configure audience model output.</p>"}, "ConfiguredAudienceModelStatus": {"type": "string", "enum": ["ACTIVE"]}, "ConfiguredAudienceModelSummary": {"type": "structure", "required": ["createTime", "updateTime", "name", "audienceModelArn", "outputConfig", "configuredAudienceModelArn", "status"], "members": {"createTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time at which the configured audience model was created.</p>"}, "updateTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The most recent time at which the configured audience model was updated.</p>"}, "name": {"shape": "NameString", "documentation": "<p>The name of the configured audience model.</p>"}, "audienceModelArn": {"shape": "AudienceModelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the audience model that was used to create the configured audience model.</p>"}, "outputConfig": {"shape": "ConfiguredAudienceModelOutputConfig", "documentation": "<p>The output configuration of the configured audience model.</p>"}, "description": {"shape": "ResourceDescription", "documentation": "<p>The description of the configured audience model.</p>"}, "configuredAudienceModelArn": {"shape": "ConfiguredAudienceModelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the configured audience model that you are interested in.</p>"}, "status": {"shape": "ConfiguredAudienceModelStatus", "documentation": "<p>The status of the configured audience model.</p>"}}, "documentation": "<p>Information about the configured audience model.</p>"}, "ConfiguredModelAlgorithmArn": {"type": "string", "max": 2048, "min": 20, "pattern": "arn:aws[-a-z]*:cleanrooms-ml:[-a-z0-9]+:[0-9]{12}:configured-model-algorithm/[-a-zA-Z0-9_/.]+"}, "ConfiguredModelAlgorithmAssociationArn": {"type": "string", "max": 2048, "min": 20, "pattern": "arn:aws[-a-z]*:cleanrooms-ml:[-a-z0-9]+:[0-9]{12}:membership/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/configured-model-algorithm-association/[-a-zA-Z0-9_/.]+"}, "ConfiguredModelAlgorithmAssociationList": {"type": "list", "member": {"shape": "ConfiguredModelAlgorithmAssociationSummary"}}, "ConfiguredModelAlgorithmAssociationSummary": {"type": "structure", "required": ["createTime", "updateTime", "configuredModelAlgorithmAssociationArn", "configuredModelAlgorithmArn", "name", "membershipIdentifier", "collaborationIdentifier"], "members": {"createTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time at which the configured model algorithm association was created.</p>"}, "updateTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The most recent time at which the configured model algorithm association was updated.</p>"}, "configuredModelAlgorithmAssociationArn": {"shape": "ConfiguredModelAlgorithmAssociationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the configured model algorithm association.</p>"}, "configuredModelAlgorithmArn": {"shape": "ConfiguredModelAlgorithmArn", "documentation": "<p>The Amazon Resource Name (ARN) of the configured model algorithm that is being associated.</p>"}, "name": {"shape": "NameString", "documentation": "<p>The name of the configured model algorithm association.</p>"}, "description": {"shape": "ResourceDescription", "documentation": "<p>The description of the configured model algorithm association.</p>"}, "membershipIdentifier": {"shape": "UUID", "documentation": "<p>The membership ID of the member that created the configured model algorithm association.</p>"}, "collaborationIdentifier": {"shape": "UUID", "documentation": "<p>The collaboration ID of the collaboration that contains the configured model algorithm association.</p>"}}, "documentation": "<p>Provides summary information about the configured model algorithm association.</p>"}, "ConfiguredModelAlgorithmList": {"type": "list", "member": {"shape": "ConfiguredModelAlgorithmSummary"}}, "ConfiguredModelAlgorithmSummary": {"type": "structure", "required": ["createTime", "updateTime", "configuredModelAlgorithmArn", "name"], "members": {"createTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time at which the configured model algorithm was created.</p>"}, "updateTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The most recent time at which the configured model algorithm was updated.</p>"}, "configuredModelAlgorithmArn": {"shape": "ConfiguredModelAlgorithmArn", "documentation": "<p>The Amazon Resource Name (ARN) of the configured model algorithm.</p>"}, "name": {"shape": "NameString", "documentation": "<p>The name of the configured model algorithm.</p>"}, "description": {"shape": "ResourceDescription", "documentation": "<p>The description of the configured model algorithm.</p>"}}, "documentation": "<p>Provides summary information about a configured model algorithm.</p>"}, "ConflictException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>You can't complete this action because another resource depends on this resource.</p>", "error": {"httpStatusCode": 409, "senderFault": true}, "exception": true}, "ContainerArgument": {"type": "string", "max": 256, "min": 1, "pattern": ".*"}, "ContainerArguments": {"type": "list", "member": {"shape": "ContainerArgument"}, "max": 100, "min": 1}, "ContainerConfig": {"type": "structure", "required": ["imageUri"], "members": {"imageUri": {"shape": "AlgorithmImage", "documentation": "<p>The registry path of the docker image that contains the algorithm. Clean Rooms ML currently only supports the <code>registry/repository[:tag]</code> image path format. For more information about using images in Clean Rooms ML, see the <a href=\"https://docs.aws.amazon.com/sagemaker/latest/APIReference/API_AlgorithmSpecification.html#sagemaker-Type-AlgorithmSpecification-TrainingImage\">Sagemaker API reference</a>.</p>"}, "entrypoint": {"shape": "ContainerEntrypoint", "documentation": "<p>The entrypoint script for a Docker container used to run a training job. This script takes precedence over the default train processing instructions. See How Amazon SageMaker Runs Your Training Image for additional information. For more information, see <a href=\"https://docs.aws.amazon.com/sagemaker/latest/dg/your-algorithms-training-algo-dockerfile.html\">How Sagemaker runs your training image</a>.</p>"}, "arguments": {"shape": "ContainerArguments", "documentation": "<p>The arguments for a container used to run a training job. See How Amazon SageMaker Runs Your Training Image for additional information. For more information, see <a href=\"https://docs.aws.amazon.com/sagemaker/latest/dg/your-algorithms-training-algo-dockerfile.html\">How Sagemaker runs your training image</a>.</p>"}, "metricDefinitions": {"shape": "MetricDefinitionList", "documentation": "<p>A list of metric definition objects. Each object specifies the metric name and regular expressions used to parse algorithm logs. Amazon Web Services Clean Rooms ML publishes each metric to all members' Amazon CloudWatch using IAM role configured in <a>PutMLConfiguration</a>.</p>"}}, "documentation": "<p>Provides configuration information for the dockerized container where the model algorithm is stored.</p>"}, "ContainerEntrypoint": {"type": "list", "member": {"shape": "ContainerEntrypointString"}, "max": 100, "min": 1}, "ContainerEntrypointString": {"type": "string", "max": 256, "min": 1, "pattern": ".*"}, "CreateAudienceModelRequest": {"type": "structure", "required": ["name", "trainingDatasetArn"], "members": {"trainingDataStartTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The start date and time of the training window.</p>"}, "trainingDataEndTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The end date and time of the training window.</p>"}, "name": {"shape": "NameString", "documentation": "<p>The name of the audience model resource.</p>"}, "trainingDatasetArn": {"shape": "TrainingDatasetArn", "documentation": "<p>The Amazon Resource Name (ARN) of the training dataset for this audience model.</p>"}, "kmsKeyArn": {"shape": "KmsKeyArn", "documentation": "<p>The Amazon Resource Name (ARN) of the KMS key. This key is used to encrypt and decrypt customer-owned data in the trained ML model and the associated data.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The optional metadata that you apply to the resource to help you categorize and organize them. Each tag consists of a key and an optional value, both of which you define.</p> <p>The following basic restrictions apply to tags:</p> <ul> <li> <p>Maximum number of tags per resource - 50.</p> </li> <li> <p>For each resource, each tag key must be unique, and each tag key can have only one value.</p> </li> <li> <p>Maximum key length - 128 Unicode characters in UTF-8.</p> </li> <li> <p>Maximum value length - 256 Unicode characters in UTF-8.</p> </li> <li> <p>If your tagging schema is used across multiple services and resources, remember that other services may have restrictions on allowed characters. Generally allowed characters are: letters, numbers, and spaces representable in UTF-8, and the following characters: + - = . _ : / @.</p> </li> <li> <p>Tag keys and values are case sensitive.</p> </li> <li> <p>Do not use aws:, AWS:, or any upper or lowercase combination of such as a prefix for keys as it is reserved for AWS use. You cannot edit or delete tag keys with this prefix. Values can have this prefix. If a tag value has aws as its prefix but the key does not, then Clean Rooms ML considers it to be a user tag and will count against the limit of 50 tags. Tags with only the key prefix of aws do not count against your tags per resource limit.</p> </li> </ul>"}, "description": {"shape": "ResourceDescription", "documentation": "<p>The description of the audience model.</p>"}}}, "CreateAudienceModelResponse": {"type": "structure", "required": ["audienceModelArn"], "members": {"audienceModelArn": {"shape": "AudienceModelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the audience model.</p>"}}}, "CreateConfiguredAudienceModelRequest": {"type": "structure", "required": ["name", "audienceModelArn", "outputConfig", "sharedAudienceMetrics"], "members": {"name": {"shape": "NameString", "documentation": "<p>The name of the configured audience model.</p>"}, "audienceModelArn": {"shape": "AudienceModelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the audience model to use for the configured audience model.</p>"}, "outputConfig": {"shape": "ConfiguredAudienceModelOutputConfig", "documentation": "<p>Configure the Amazon S3 location and IAM Role for audiences created using this configured audience model. Each audience will have a unique location. The IAM Role must have <code>s3:PutObject</code> permission on the destination Amazon S3 location. If the destination is protected with Amazon S3 KMS-SSE, then the Role must also have the required KMS permissions.</p>"}, "description": {"shape": "ResourceDescription", "documentation": "<p>The description of the configured audience model.</p>"}, "sharedAudienceMetrics": {"shape": "MetricsList", "documentation": "<p>Whether audience metrics are shared.</p>"}, "minMatchingSeedSize": {"shape": "MinMatchingSeedSize", "documentation": "<p>The minimum number of users from the seed audience that must match with users in the training data of the audience model. The default value is 500.</p>"}, "audienceSizeConfig": {"shape": "AudienceSizeConfig", "documentation": "<p>Configure the list of output sizes of audiences that can be created using this configured audience model. A request to <a>StartAudienceGenerationJob</a> that uses this configured audience model must have an <code>audienceSize</code> selected from this list. You can use the <code>ABSOLUTE</code> <a>AudienceSize</a> to configure out audience sizes using the count of identifiers in the output. You can use the <code>Percentage</code> <a>AudienceSize</a> to configure sizes in the range 1-100 percent.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The optional metadata that you apply to the resource to help you categorize and organize them. Each tag consists of a key and an optional value, both of which you define.</p> <p>The following basic restrictions apply to tags:</p> <ul> <li> <p>Maximum number of tags per resource - 50.</p> </li> <li> <p>For each resource, each tag key must be unique, and each tag key can have only one value.</p> </li> <li> <p>Maximum key length - 128 Unicode characters in UTF-8.</p> </li> <li> <p>Maximum value length - 256 Unicode characters in UTF-8.</p> </li> <li> <p>If your tagging schema is used across multiple services and resources, remember that other services may have restrictions on allowed characters. Generally allowed characters are: letters, numbers, and spaces representable in UTF-8, and the following characters: + - = . _ : / @.</p> </li> <li> <p>Tag keys and values are case sensitive.</p> </li> <li> <p>Do not use aws:, AWS:, or any upper or lowercase combination of such as a prefix for keys as it is reserved for AWS use. You cannot edit or delete tag keys with this prefix. Values can have this prefix. If a tag value has aws as its prefix but the key does not, then Clean Rooms ML considers it to be a user tag and will count against the limit of 50 tags. Tags with only the key prefix of aws do not count against your tags per resource limit.</p> </li> </ul>"}, "childResourceTagOnCreatePolicy": {"shape": "TagOnCreatePolicy", "documentation": "<p>Configure how the service tags audience generation jobs created using this configured audience model. If you specify <code>NONE</code>, the tags from the <a>StartAudienceGenerationJob</a> request determine the tags of the audience generation job. If you specify <code>FROM_PARENT_RESOURCE</code>, the audience generation job inherits the tags from the configured audience model, by default. Tags in the <a>StartAudienceGenerationJob</a> will override the default.</p> <p>When the client is in a different account than the configured audience model, the tags from the client are never applied to a resource in the caller's account.</p>"}}}, "CreateConfiguredAudienceModelResponse": {"type": "structure", "required": ["configuredAudienceModelArn"], "members": {"configuredAudienceModelArn": {"shape": "ConfiguredAudienceModelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the configured audience model.</p>"}}}, "CreateConfiguredModelAlgorithmAssociationRequest": {"type": "structure", "required": ["membershipIdentifier", "configuredModelAlgorithmArn", "name"], "members": {"membershipIdentifier": {"shape": "UUID", "documentation": "<p>The membership ID of the member who is associating this configured model algorithm.</p>", "location": "uri", "locationName": "membershipIdentifier"}, "configuredModelAlgorithmArn": {"shape": "ConfiguredModelAlgorithmArn", "documentation": "<p>The Amazon Resource Name (ARN) of the configured model algorithm that you want to associate.</p>"}, "name": {"shape": "NameString", "documentation": "<p>The name of the configured model algorithm association.</p>"}, "description": {"shape": "ResourceDescription", "documentation": "<p>The description of the configured model algorithm association.</p>"}, "privacyConfiguration": {"shape": "PrivacyConfiguration", "documentation": "<p>Specifies the privacy configuration information for the configured model algorithm association. This information includes the maximum data size that can be exported.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The optional metadata that you apply to the resource to help you categorize and organize them. Each tag consists of a key and an optional value, both of which you define.</p> <p>The following basic restrictions apply to tags:</p> <ul> <li> <p>Maximum number of tags per resource - 50.</p> </li> <li> <p>For each resource, each tag key must be unique, and each tag key can have only one value.</p> </li> <li> <p>Maximum key length - 128 Unicode characters in UTF-8.</p> </li> <li> <p>Maximum value length - 256 Unicode characters in UTF-8.</p> </li> <li> <p>If your tagging schema is used across multiple services and resources, remember that other services may have restrictions on allowed characters. Generally allowed characters are: letters, numbers, and spaces representable in UTF-8, and the following characters: + - = . _ : / @.</p> </li> <li> <p>Tag keys and values are case sensitive.</p> </li> <li> <p>Do not use aws:, AWS:, or any upper or lowercase combination of such as a prefix for keys as it is reserved for AWS use. You cannot edit or delete tag keys with this prefix. Values can have this prefix. If a tag value has aws as its prefix but the key does not, then Clean Rooms ML considers it to be a user tag and will count against the limit of 50 tags. Tags with only the key prefix of aws do not count against your tags per resource limit.</p> </li> </ul>"}}}, "CreateConfiguredModelAlgorithmAssociationResponse": {"type": "structure", "required": ["configuredModelAlgorithmAssociationArn"], "members": {"configuredModelAlgorithmAssociationArn": {"shape": "ConfiguredModelAlgorithmAssociationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the configured model algorithm association.</p>"}}}, "CreateConfiguredModelAlgorithmRequest": {"type": "structure", "required": ["name", "roleArn"], "members": {"name": {"shape": "NameString", "documentation": "<p>The name of the configured model algorithm.</p>"}, "description": {"shape": "ResourceDescription", "documentation": "<p>The description of the configured model algorithm.</p>"}, "roleArn": {"shape": "IamRoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the role that is used to access the repository.</p>"}, "trainingContainerConfig": {"shape": "ContainerConfig", "documentation": "<p>Configuration information for the training container, including entrypoints and arguments.</p>"}, "inferenceContainerConfig": {"shape": "InferenceContainerConfig", "documentation": "<p>Configuration information for the inference container that is used when you run an inference job on a configured model algorithm.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The optional metadata that you apply to the resource to help you categorize and organize them. Each tag consists of a key and an optional value, both of which you define.</p> <p>The following basic restrictions apply to tags:</p> <ul> <li> <p>Maximum number of tags per resource - 50.</p> </li> <li> <p>For each resource, each tag key must be unique, and each tag key can have only one value.</p> </li> <li> <p>Maximum key length - 128 Unicode characters in UTF-8.</p> </li> <li> <p>Maximum value length - 256 Unicode characters in UTF-8.</p> </li> <li> <p>If your tagging schema is used across multiple services and resources, remember that other services may have restrictions on allowed characters. Generally allowed characters are: letters, numbers, and spaces representable in UTF-8, and the following characters: + - = . _ : / @.</p> </li> <li> <p>Tag keys and values are case sensitive.</p> </li> <li> <p>Do not use aws:, AWS:, or any upper or lowercase combination of such as a prefix for keys as it is reserved for AWS use. You cannot edit or delete tag keys with this prefix. Values can have this prefix. If a tag value has aws as its prefix but the key does not, then Clean Rooms ML considers it to be a user tag and will count against the limit of 50 tags. Tags with only the key prefix of aws do not count against your tags per resource limit.</p> </li> </ul>"}, "kmsKeyArn": {"shape": "KmsKeyArn", "documentation": "<p>The Amazon Resource Name (ARN) of the KMS key. This key is used to encrypt and decrypt customer-owned data in the configured ML model algorithm and associated data.</p>"}}}, "CreateConfiguredModelAlgorithmResponse": {"type": "structure", "required": ["configuredModelAlgorithmArn"], "members": {"configuredModelAlgorithmArn": {"shape": "ConfiguredModelAlgorithmArn", "documentation": "<p>The Amazon Resource Name (ARN) of the configured model algorithm.</p>"}}}, "CreateMLInputChannelRequest": {"type": "structure", "required": ["membershipIdentifier", "configuredModelAlgorithmAssociations", "inputChannel", "name", "retentionInDays"], "members": {"membershipIdentifier": {"shape": "UUID", "documentation": "<p>The membership ID of the member that is creating the ML input channel.</p>", "location": "uri", "locationName": "membershipIdentifier"}, "configuredModelAlgorithmAssociations": {"shape": "CreateMLInputChannelRequestConfiguredModelAlgorithmAssociationsList", "documentation": "<p>The associated configured model algorithms that are necessary to create this ML input channel.</p>"}, "inputChannel": {"shape": "InputChannel", "documentation": "<p>The input data that is used to create this ML input channel.</p>"}, "name": {"shape": "NameString", "documentation": "<p>The name of the ML input channel.</p>"}, "retentionInDays": {"shape": "CreateMLInputChannelRequestRetentionInDaysInteger", "documentation": "<p>The number of days that the data in the ML input channel is retained.</p>"}, "description": {"shape": "ResourceDescription", "documentation": "<p>The description of the ML input channel.</p>"}, "kmsKeyArn": {"shape": "KmsKeyArn", "documentation": "<p>The Amazon Resource Name (ARN) of the KMS key that is used to access the input channel.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The optional metadata that you apply to the resource to help you categorize and organize them. Each tag consists of a key and an optional value, both of which you define.</p> <p>The following basic restrictions apply to tags:</p> <ul> <li> <p>Maximum number of tags per resource - 50.</p> </li> <li> <p>For each resource, each tag key must be unique, and each tag key can have only one value.</p> </li> <li> <p>Maximum key length - 128 Unicode characters in UTF-8.</p> </li> <li> <p>Maximum value length - 256 Unicode characters in UTF-8.</p> </li> <li> <p>If your tagging schema is used across multiple services and resources, remember that other services may have restrictions on allowed characters. Generally allowed characters are: letters, numbers, and spaces representable in UTF-8, and the following characters: + - = . _ : / @.</p> </li> <li> <p>Tag keys and values are case sensitive.</p> </li> <li> <p>Do not use aws:, AWS:, or any upper or lowercase combination of such as a prefix for keys as it is reserved for AWS use. You cannot edit or delete tag keys with this prefix. Values can have this prefix. If a tag value has aws as its prefix but the key does not, then Clean Rooms ML considers it to be a user tag and will count against the limit of 50 tags. Tags with only the key prefix of aws do not count against your tags per resource limit.</p> </li> </ul>"}}}, "CreateMLInputChannelRequestConfiguredModelAlgorithmAssociationsList": {"type": "list", "member": {"shape": "ConfiguredModelAlgorithmAssociationArn"}, "max": 1, "min": 1}, "CreateMLInputChannelRequestRetentionInDaysInteger": {"type": "integer", "box": true, "max": 30, "min": 1}, "CreateMLInputChannelResponse": {"type": "structure", "required": ["mlInputChannelArn"], "members": {"mlInputChannelArn": {"shape": "MLInputChannelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the ML input channel.</p>"}}}, "CreateTrainedModelRequest": {"type": "structure", "required": ["membershipIdentifier", "name", "configuredModelAlgorithmAssociationArn", "resourceConfig", "dataChannels"], "members": {"membershipIdentifier": {"shape": "UUID", "documentation": "<p>The membership ID of the member that is creating the trained model.</p>", "location": "uri", "locationName": "membershipIdentifier"}, "name": {"shape": "NameString", "documentation": "<p>The name of the trained model.</p>"}, "configuredModelAlgorithmAssociationArn": {"shape": "ConfiguredModelAlgorithmAssociationArn", "documentation": "<p>The associated configured model algorithm used to train this model.</p>"}, "hyperparameters": {"shape": "HyperParameters", "documentation": "<p>Algorithm-specific parameters that influence the quality of the model. You set hyperparameters before you start the learning process.</p>"}, "environment": {"shape": "Environment", "documentation": "<p>The environment variables to set in the Docker container.</p>"}, "resourceConfig": {"shape": "ResourceConfig", "documentation": "<p>Information about the EC2 resources that are used to train this model.</p>"}, "stoppingCondition": {"shape": "StoppingCondition", "documentation": "<p>The criteria that is used to stop model training.</p>"}, "incrementalTrainingDataChannels": {"shape": "IncrementalTrainingDataChannels", "documentation": "<p>Specifies the incremental training data channels for the trained model. </p> <p>Incremental training allows you to create a new trained model with updates without retraining from scratch. You can specify up to one incremental training data channel that references a previously trained model and its version.</p> <p>Limit: Maximum of 20 channels total (including both <code>incrementalTrainingDataChannels</code> and <code>dataChannels</code>).</p>"}, "dataChannels": {"shape": "ModelTrainingDataChannels", "documentation": "<p>Defines the data channels that are used as input for the trained model request.</p> <p>Limit: Maximum of 20 channels total (including both <code>dataChannels</code> and <code>incrementalTrainingDataChannels</code>).</p>"}, "trainingInputMode": {"shape": "TrainingInputMode", "documentation": "<p>The input mode for accessing the training data. This parameter determines how the training data is made available to the training algorithm. Valid values are:</p> <ul> <li> <p> <code>File</code> - The training data is downloaded to the training instance and made available as files.</p> </li> <li> <p> <code>FastFile</code> - The training data is streamed directly from Amazon S3 to the training algorithm, providing faster access for large datasets.</p> </li> <li> <p> <code>Pipe</code> - The training data is streamed to the training algorithm using named pipes, which can improve performance for certain algorithms.</p> </li> </ul>"}, "description": {"shape": "ResourceDescription", "documentation": "<p>The description of the trained model.</p>"}, "kmsKeyArn": {"shape": "KmsKeyArn", "documentation": "<p>The Amazon Resource Name (ARN) of the KMS key. This key is used to encrypt and decrypt customer-owned data in the trained ML model and the associated data.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The optional metadata that you apply to the resource to help you categorize and organize them. Each tag consists of a key and an optional value, both of which you define.</p> <p>The following basic restrictions apply to tags:</p> <ul> <li> <p>Maximum number of tags per resource - 50.</p> </li> <li> <p>For each resource, each tag key must be unique, and each tag key can have only one value.</p> </li> <li> <p>Maximum key length - 128 Unicode characters in UTF-8.</p> </li> <li> <p>Maximum value length - 256 Unicode characters in UTF-8.</p> </li> <li> <p>If your tagging schema is used across multiple services and resources, remember that other services may have restrictions on allowed characters. Generally allowed characters are: letters, numbers, and spaces representable in UTF-8, and the following characters: + - = . _ : / @.</p> </li> <li> <p>Tag keys and values are case sensitive.</p> </li> <li> <p>Do not use aws:, AWS:, or any upper or lowercase combination of such as a prefix for keys as it is reserved for AWS use. You cannot edit or delete tag keys with this prefix. Values can have this prefix. If a tag value has aws as its prefix but the key does not, then Clean Rooms ML considers it to be a user tag and will count against the limit of 50 tags. Tags with only the key prefix of aws do not count against your tags per resource limit.</p> </li> </ul>"}}}, "CreateTrainedModelResponse": {"type": "structure", "required": ["trainedModelArn"], "members": {"trainedModelArn": {"shape": "TrainedModelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the trained model.</p>"}, "versionIdentifier": {"shape": "UUID", "documentation": "<p>The unique version identifier assigned to the newly created trained model. This identifier can be used to reference this specific version of the trained model in subsequent operations such as inference jobs or incremental training.</p> <p>The initial version identifier for the base version of the trained model is \"NULL\".</p>"}}}, "CreateTrainingDatasetRequest": {"type": "structure", "required": ["name", "roleArn", "trainingData"], "members": {"name": {"shape": "NameString", "documentation": "<p>The name of the training dataset. This name must be unique in your account and region.</p>"}, "roleArn": {"shape": "IamRoleArn", "documentation": "<p>The ARN of the IAM role that Clean Rooms ML can assume to read the data referred to in the <code>dataSource</code> field of each dataset.</p> <p>Passing a role across AWS accounts is not allowed. If you pass a role that isn't in your account, you get an <code>AccessDeniedException</code> error.</p>"}, "trainingData": {"shape": "CreateTrainingDatasetRequestTrainingDataList", "documentation": "<p>An array of information that lists the Dataset objects, which specifies the dataset type and details on its location and schema. You must provide a role that has read access to these tables.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The optional metadata that you apply to the resource to help you categorize and organize them. Each tag consists of a key and an optional value, both of which you define.</p> <p>The following basic restrictions apply to tags:</p> <ul> <li> <p>Maximum number of tags per resource - 50.</p> </li> <li> <p>For each resource, each tag key must be unique, and each tag key can have only one value.</p> </li> <li> <p>Maximum key length - 128 Unicode characters in UTF-8.</p> </li> <li> <p>Maximum value length - 256 Unicode characters in UTF-8.</p> </li> <li> <p>If your tagging schema is used across multiple services and resources, remember that other services may have restrictions on allowed characters. Generally allowed characters are: letters, numbers, and spaces representable in UTF-8, and the following characters: + - = . _ : / @.</p> </li> <li> <p>Tag keys and values are case sensitive.</p> </li> <li> <p>Do not use aws:, AWS:, or any upper or lowercase combination of such as a prefix for keys as it is reserved for AWS use. You cannot edit or delete tag keys with this prefix. Values can have this prefix. If a tag value has aws as its prefix but the key does not, then Clean Rooms ML considers it to be a user tag and will count against the limit of 50 tags. Tags with only the key prefix of aws do not count against your tags per resource limit.</p> </li> </ul>"}, "description": {"shape": "ResourceDescription", "documentation": "<p>The description of the training dataset.</p>"}}}, "CreateTrainingDatasetRequestTrainingDataList": {"type": "list", "member": {"shape": "Dataset"}, "max": 1, "min": 1}, "CreateTrainingDatasetResponse": {"type": "structure", "required": ["trainingDatasetArn"], "members": {"trainingDatasetArn": {"shape": "TrainingDatasetArn", "documentation": "<p>The Amazon Resource Name (ARN) of the training dataset resource.</p>"}}}, "DataSource": {"type": "structure", "required": ["glueDataSource"], "members": {"glueDataSource": {"shape": "GlueDataSource", "documentation": "<p>A GlueDataSource object that defines the catalog ID, database name, and table name for the training data.</p>"}}, "documentation": "<p>Defines information about the Glue data source that contains the training data.</p>"}, "Dataset": {"type": "structure", "required": ["type", "inputConfig"], "members": {"type": {"shape": "DatasetType", "documentation": "<p>What type of information is found in the dataset.</p>"}, "inputConfig": {"shape": "DatasetInputConfig", "documentation": "<p>A DatasetInputConfig object that defines the data source and schema mapping.</p>"}}, "documentation": "<p>Defines where the training dataset is located, what type of data it contains, and how to access the data.</p>"}, "DatasetInputConfig": {"type": "structure", "required": ["schema", "dataSource"], "members": {"schema": {"shape": "DatasetInputConfigSchemaList", "documentation": "<p>The schema information for the training data.</p>"}, "dataSource": {"shape": "DataSource", "documentation": "<p>A DataSource object that specifies the Glue data source for the training data.</p>"}}, "documentation": "<p>Defines the Glue data source and schema mapping information.</p>"}, "DatasetInputConfigSchemaList": {"type": "list", "member": {"shape": "ColumnSchema"}, "max": 100, "min": 1}, "DatasetList": {"type": "list", "member": {"shape": "Dataset"}}, "DatasetType": {"type": "string", "enum": ["INTERACTIONS"]}, "DeleteAudienceGenerationJobRequest": {"type": "structure", "required": ["audienceGenerationJobArn"], "members": {"audienceGenerationJobArn": {"shape": "AudienceGenerationJobArn", "documentation": "<p>The Amazon Resource Name (ARN) of the audience generation job that you want to delete.</p>", "location": "uri", "locationName": "audienceGenerationJobArn"}}}, "DeleteAudienceModelRequest": {"type": "structure", "required": ["audienceModelArn"], "members": {"audienceModelArn": {"shape": "AudienceModelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the audience model that you want to delete.</p>", "location": "uri", "locationName": "audienceModelArn"}}}, "DeleteConfiguredAudienceModelPolicyRequest": {"type": "structure", "required": ["configuredAudienceModelArn"], "members": {"configuredAudienceModelArn": {"shape": "ConfiguredAudienceModelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the configured audience model policy that you want to delete.</p>", "location": "uri", "locationName": "configuredAudienceModelArn"}}}, "DeleteConfiguredAudienceModelRequest": {"type": "structure", "required": ["configuredAudienceModelArn"], "members": {"configuredAudienceModelArn": {"shape": "ConfiguredAudienceModelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the configured audience model that you want to delete.</p>", "location": "uri", "locationName": "configuredAudienceModelArn"}}}, "DeleteConfiguredModelAlgorithmAssociationRequest": {"type": "structure", "required": ["configuredModelAlgorithmAssociationArn", "membershipIdentifier"], "members": {"configuredModelAlgorithmAssociationArn": {"shape": "ConfiguredModelAlgorithmAssociationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the configured model algorithm association that you want to delete.</p>", "location": "uri", "locationName": "configuredModelAlgorithmAssociationArn"}, "membershipIdentifier": {"shape": "UUID", "documentation": "<p>The membership ID of the member that is deleting the configured model algorithm association.</p>", "location": "uri", "locationName": "membershipIdentifier"}}}, "DeleteConfiguredModelAlgorithmRequest": {"type": "structure", "required": ["configuredModelAlgorithmArn"], "members": {"configuredModelAlgorithmArn": {"shape": "ConfiguredModelAlgorithmArn", "documentation": "<p>The Amazon Resource Name (ARN) of the configured model algorithm that you want to delete.</p>", "location": "uri", "locationName": "configuredModelAlgorithmArn"}}}, "DeleteMLConfigurationRequest": {"type": "structure", "required": ["membershipIdentifier"], "members": {"membershipIdentifier": {"shape": "UUID", "documentation": "<p>The membership ID of the of the member that is deleting the ML modeling configuration.</p>", "location": "uri", "locationName": "membershipIdentifier"}}}, "DeleteMLInputChannelDataRequest": {"type": "structure", "required": ["mlInputChannelArn", "membershipIdentifier"], "members": {"mlInputChannelArn": {"shape": "MLInputChannelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the ML input channel that you want to delete.</p>", "location": "uri", "locationName": "mlInputChannelArn"}, "membershipIdentifier": {"shape": "UUID", "documentation": "<p>The membership ID of the membership that contains the ML input channel you want to delete.</p>", "location": "uri", "locationName": "membershipIdentifier"}}}, "DeleteTrainedModelOutputRequest": {"type": "structure", "required": ["trainedModelArn", "membershipIdentifier"], "members": {"trainedModelArn": {"shape": "TrainedModelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the trained model whose output you want to delete.</p>", "location": "uri", "locationName": "trainedModelArn"}, "membershipIdentifier": {"shape": "UUID", "documentation": "<p>The membership ID of the member that is deleting the trained model output.</p>", "location": "uri", "locationName": "membershipIdentifier"}, "versionIdentifier": {"shape": "UUID", "documentation": "<p>The version identifier of the trained model to delete. If not specified, the operation will delete the base version of the trained model. When specified, only the particular version will be deleted.</p>", "location": "querystring", "locationName": "versionIdentifier"}}}, "DeleteTrainingDatasetRequest": {"type": "structure", "required": ["trainingDatasetArn"], "members": {"trainingDatasetArn": {"shape": "TrainingDatasetArn", "documentation": "<p>The Amazon Resource Name (ARN) of the training dataset that you want to delete.</p>", "location": "uri", "locationName": "trainingDatasetArn"}}}, "Destination": {"type": "structure", "required": ["s3Destination"], "members": {"s3Destination": {"shape": "S3ConfigMap"}}, "documentation": "<p>The Amazon S3 location where the exported model artifacts are stored.</p>"}, "Environment": {"type": "map", "key": {"shape": "EnvironmentKeyString"}, "value": {"shape": "EnvironmentValueString"}, "max": 100, "min": 0}, "EnvironmentKeyString": {"type": "string", "max": 512, "min": 1, "pattern": "[a-zA-Z_][a-zA-Z0-9_]*"}, "EnvironmentValueString": {"type": "string", "max": 512, "min": 1, "pattern": "[\\S\\s]*"}, "GetAudienceGenerationJobRequest": {"type": "structure", "required": ["audienceGenerationJobArn"], "members": {"audienceGenerationJobArn": {"shape": "AudienceGenerationJobArn", "documentation": "<p>The Amazon Resource Name (ARN) of the audience generation job that you are interested in.</p>", "location": "uri", "locationName": "audienceGenerationJobArn"}}}, "GetAudienceGenerationJobResponse": {"type": "structure", "required": ["createTime", "updateTime", "audienceGenerationJobArn", "name", "status", "configuredAudienceModelArn"], "members": {"createTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time at which the audience generation job was created.</p>"}, "updateTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The most recent time at which the audience generation job was updated.</p>"}, "audienceGenerationJobArn": {"shape": "AudienceGenerationJobArn", "documentation": "<p>The Amazon Resource Name (ARN) of the audience generation job.</p>"}, "name": {"shape": "NameString", "documentation": "<p>The name of the audience generation job.</p>"}, "description": {"shape": "ResourceDescription", "documentation": "<p>The description of the audience generation job.</p>"}, "status": {"shape": "AudienceGenerationJobStatus", "documentation": "<p>The status of the audience generation job.</p>"}, "statusDetails": {"shape": "StatusDetails", "documentation": "<p>Details about the status of the audience generation job.</p>"}, "configuredAudienceModelArn": {"shape": "ConfiguredAudienceModelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the configured audience model used for this audience generation job.</p>"}, "seedAudience": {"shape": "AudienceGenerationJobDataSource", "documentation": "<p>The seed audience that was used for this audience generation job. This field will be null if the account calling the API is the account that started this audience generation job. </p>"}, "includeSeedInOutput": {"shape": "Boolean", "documentation": "<p>Configure whether the seed users are included in the output audience. By default, Clean Rooms ML removes seed users from the output audience. If you specify <code>TRUE</code>, the seed users will appear first in the output. Clean Rooms ML does not explicitly reveal whether a user was in the seed, but the recipient of the audience will know that the first <code>minimumSeedSize</code> count of users are from the seed.</p>"}, "collaborationId": {"shape": "UUID", "documentation": "<p>The identifier of the collaboration that this audience generation job is associated with.</p>"}, "metrics": {"shape": "AudienceQualityMetrics", "documentation": "<p>The relevance scores for different audience sizes and the recall score of the generated audience. </p>"}, "startedBy": {"shape": "AccountId", "documentation": "<p>The AWS account that started this audience generation job.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags that are associated to this audience generation job.</p>"}, "protectedQueryIdentifier": {"shape": "String", "documentation": "<p>The unique identifier of the protected query for this audience generation job.</p>"}}}, "GetAudienceModelRequest": {"type": "structure", "required": ["audienceModelArn"], "members": {"audienceModelArn": {"shape": "AudienceModelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the audience model that you are interested in.</p>", "location": "uri", "locationName": "audienceModelArn"}}}, "GetAudienceModelResponse": {"type": "structure", "required": ["createTime", "updateTime", "audienceModelArn", "name", "trainingDatasetArn", "status"], "members": {"createTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time at which the audience model was created.</p>"}, "updateTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The most recent time at which the audience model was updated.</p>"}, "trainingDataStartTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The start date specified for the training window.</p>"}, "trainingDataEndTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The end date specified for the training window.</p>"}, "audienceModelArn": {"shape": "AudienceModelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the audience model.</p>"}, "name": {"shape": "NameString", "documentation": "<p>The name of the audience model.</p>"}, "trainingDatasetArn": {"shape": "TrainingDatasetArn", "documentation": "<p>The Amazon Resource Name (ARN) of the training dataset that was used for this audience model.</p>"}, "status": {"shape": "AudienceModelStatus", "documentation": "<p>The status of the audience model.</p>"}, "statusDetails": {"shape": "StatusDetails", "documentation": "<p>Details about the status of the audience model.</p>"}, "kmsKeyArn": {"shape": "KmsKeyArn", "documentation": "<p>The KMS key ARN used for the audience model.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags that are assigned to the audience model.</p>"}, "description": {"shape": "ResourceDescription", "documentation": "<p>The description of the audience model.</p>"}}}, "GetCollaborationConfiguredModelAlgorithmAssociationRequest": {"type": "structure", "required": ["configuredModelAlgorithmAssociationArn", "collaborationIdentifier"], "members": {"configuredModelAlgorithmAssociationArn": {"shape": "ConfiguredModelAlgorithmAssociationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the configured model algorithm association that you want to return information about.</p>", "location": "uri", "locationName": "configuredModelAlgorithmAssociationArn"}, "collaborationIdentifier": {"shape": "UUID", "documentation": "<p>The collaboration ID for the collaboration that contains the configured model algorithm association that you want to return information about.</p>", "location": "uri", "locationName": "collaborationIdentifier"}}}, "GetCollaborationConfiguredModelAlgorithmAssociationResponse": {"type": "structure", "required": ["createTime", "updateTime", "configuredModelAlgorithmAssociationArn", "membershipIdentifier", "collaborationIdentifier", "configuredModelAlgorithmArn", "name", "creatorAccountId"], "members": {"createTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time at which the configured model algorithm association was created.</p>"}, "updateTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The most recent time at which the configured model algorithm association was updated.</p>"}, "configuredModelAlgorithmAssociationArn": {"shape": "ConfiguredModelAlgorithmAssociationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the configured model algorithm association.</p>"}, "membershipIdentifier": {"shape": "UUID", "documentation": "<p>The membership ID of the member that created the configured model algorithm association.</p>"}, "collaborationIdentifier": {"shape": "UUID", "documentation": "<p>The collaboration ID of the collaboration that contains the configured model algorithm association.</p>"}, "configuredModelAlgorithmArn": {"shape": "ConfiguredModelAlgorithmArn", "documentation": "<p>The Amazon Resource Name (ARN) of the configured model algorithm association.</p>"}, "name": {"shape": "NameString", "documentation": "<p>The name of the configured model algorithm association.</p>"}, "description": {"shape": "ResourceDescription", "documentation": "<p>The description of the configured model algorithm association.</p>"}, "creatorAccountId": {"shape": "AccountId", "documentation": "<p>The account ID of the member that created the configured model algorithm association.</p>"}, "privacyConfiguration": {"shape": "PrivacyConfiguration"}}}, "GetCollaborationMLInputChannelRequest": {"type": "structure", "required": ["mlInputChannelArn", "collaborationIdentifier"], "members": {"mlInputChannelArn": {"shape": "MLInputChannelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the ML input channel that you want to get.</p>", "location": "uri", "locationName": "mlInputChannelArn"}, "collaborationIdentifier": {"shape": "UUID", "documentation": "<p>The collaboration ID of the collaboration that contains the ML input channel that you want to get.</p>", "location": "uri", "locationName": "collaborationIdentifier"}}}, "GetCollaborationMLInputChannelResponse": {"type": "structure", "required": ["createTime", "updateTime", "creatorAccountId", "membershipIdentifier", "collaborationIdentifier", "mlInputChannelArn", "name", "configuredModelAlgorithmAssociations", "status", "retentionInDays"], "members": {"createTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time at which the ML input channel was created.</p>"}, "updateTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The most recent time at which the ML input channel was updated.</p>"}, "creatorAccountId": {"shape": "AccountId", "documentation": "<p>The account ID of the member who created the ML input channel.</p>"}, "membershipIdentifier": {"shape": "UUID", "documentation": "<p>The membership ID of the membership that contains the ML input channel.</p>"}, "collaborationIdentifier": {"shape": "UUID", "documentation": "<p>The collaboration ID of the collaboration that contains the ML input channel.</p>"}, "mlInputChannelArn": {"shape": "MLInputChannelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the ML input channel.</p>"}, "name": {"shape": "NameString", "documentation": "<p>The name of the ML input channel.</p>"}, "configuredModelAlgorithmAssociations": {"shape": "GetCollaborationMLInputChannelResponseConfiguredModelAlgorithmAssociationsList", "documentation": "<p>The configured model algorithm associations that were used to create the ML input channel.</p>"}, "status": {"shape": "MLInputChannelStatus", "documentation": "<p>The status of the ML input channel.</p>"}, "statusDetails": {"shape": "StatusDetails"}, "retentionInDays": {"shape": "GetCollaborationMLInputChannelResponseRetentionInDaysInteger", "documentation": "<p>The number of days to retain the data for the ML input channel.</p>"}, "numberOfRecords": {"shape": "GetCollaborationMLInputChannelResponseNumberOfRecordsLong", "documentation": "<p>The number of records in the ML input channel.</p>"}, "description": {"shape": "ResourceDescription", "documentation": "<p>The description of the ML input channel.</p>"}}}, "GetCollaborationMLInputChannelResponseConfiguredModelAlgorithmAssociationsList": {"type": "list", "member": {"shape": "ConfiguredModelAlgorithmAssociationArn"}, "max": 1, "min": 1}, "GetCollaborationMLInputChannelResponseNumberOfRecordsLong": {"type": "long", "box": true, "max": 100000000000, "min": 0}, "GetCollaborationMLInputChannelResponseRetentionInDaysInteger": {"type": "integer", "box": true, "max": 30, "min": 1}, "GetCollaborationTrainedModelRequest": {"type": "structure", "required": ["trainedModelArn", "collaborationIdentifier"], "members": {"trainedModelArn": {"shape": "TrainedModelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the trained model that you want to return information about.</p>", "location": "uri", "locationName": "trainedModelArn"}, "collaborationIdentifier": {"shape": "UUID", "documentation": "<p>The collaboration ID that contains the trained model that you want to return information about.</p>", "location": "uri", "locationName": "collaborationIdentifier"}, "versionIdentifier": {"shape": "UUID", "documentation": "<p>The version identifier of the trained model to retrieve. If not specified, the operation returns information about the latest version of the trained model.</p>", "location": "querystring", "locationName": "versionIdentifier"}}}, "GetCollaborationTrainedModelResponse": {"type": "structure", "required": ["membershipIdentifier", "collaborationIdentifier", "trainedModelArn", "name", "status", "configuredModelAlgorithmAssociationArn", "createTime", "updateTime", "creatorAccountId"], "members": {"membershipIdentifier": {"shape": "UUID", "documentation": "<p>The membership ID of the member that created the trained model.</p>"}, "collaborationIdentifier": {"shape": "UUID", "documentation": "<p>The collaboration ID of the collaboration that contains the trained model.</p>"}, "trainedModelArn": {"shape": "TrainedModelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the trained model.</p>"}, "versionIdentifier": {"shape": "UUID", "documentation": "<p>The version identifier of the trained model. This unique identifier distinguishes this version from other versions of the same trained model.</p>"}, "incrementalTrainingDataChannels": {"shape": "IncrementalTrainingDataChannelsOutput", "documentation": "<p>Information about the incremental training data channels used to create this version of the trained model. This includes details about the base model that was used for incremental training and the channel configuration.</p>"}, "name": {"shape": "NameString", "documentation": "<p>The name of the trained model.</p>"}, "description": {"shape": "ResourceDescription", "documentation": "<p>The description of the trained model.</p>"}, "status": {"shape": "TrainedModelStatus", "documentation": "<p>The status of the trained model.</p>"}, "statusDetails": {"shape": "StatusDetails"}, "configuredModelAlgorithmAssociationArn": {"shape": "ConfiguredModelAlgorithmAssociationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the configured model algorithm association that was used to create this trained model.</p>"}, "resourceConfig": {"shape": "ResourceConfig", "documentation": "<p>The EC2 resource configuration that was used to train this model.</p>"}, "trainingInputMode": {"shape": "TrainingInputMode", "documentation": "<p>The input mode that was used for accessing the training data when this trained model was created. This indicates how the training data was made available to the training algorithm.</p>"}, "stoppingCondition": {"shape": "StoppingCondition", "documentation": "<p>The stopping condition that determined when model training ended.</p>"}, "metricsStatus": {"shape": "MetricsStatus", "documentation": "<p>The status of the model metrics.</p>"}, "metricsStatusDetails": {"shape": "String", "documentation": "<p>Details about the status information for the model metrics.</p>"}, "logsStatus": {"shape": "LogsStatus", "documentation": "<p>Status information for the logs.</p>"}, "logsStatusDetails": {"shape": "String", "documentation": "<p>Details about the status information for the logs.</p>"}, "trainingContainerImageDigest": {"shape": "String", "documentation": "<p>Information about the training container image.</p>"}, "createTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time at which the trained model was created.</p>"}, "updateTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The most recent time at which the trained model was updated.</p>"}, "creatorAccountId": {"shape": "AccountId", "documentation": "<p>The account ID of the member that created the trained model.</p>"}}}, "GetConfiguredAudienceModelPolicyRequest": {"type": "structure", "required": ["configuredAudienceModelArn"], "members": {"configuredAudienceModelArn": {"shape": "ConfiguredAudienceModelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the configured audience model that you are interested in.</p>", "location": "uri", "locationName": "configuredAudienceModelArn"}}}, "GetConfiguredAudienceModelPolicyResponse": {"type": "structure", "required": ["configuredAudienceModelArn", "configuredAudienceModelPolicy", "policyHash"], "members": {"configuredAudienceModelArn": {"shape": "ConfiguredAudienceModelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the configured audience model.</p>"}, "configuredAudienceModelPolicy": {"shape": "ResourcePolicy", "documentation": "<p>The configured audience model policy. This is a JSON IAM resource policy.</p>"}, "policyHash": {"shape": "Hash", "documentation": "<p>A cryptographic hash of the contents of the policy used to prevent unexpected concurrent modification of the policy.</p>"}}}, "GetConfiguredAudienceModelRequest": {"type": "structure", "required": ["configuredAudienceModelArn"], "members": {"configuredAudienceModelArn": {"shape": "ConfiguredAudienceModelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the configured audience model that you are interested in.</p>", "location": "uri", "locationName": "configuredAudienceModelArn"}}}, "GetConfiguredAudienceModelResponse": {"type": "structure", "required": ["createTime", "updateTime", "configuredAudienceModelArn", "name", "audienceModelArn", "outputConfig", "status", "sharedAudienceMetrics"], "members": {"createTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time at which the configured audience model was created.</p>"}, "updateTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The most recent time at which the configured audience model was updated.</p>"}, "configuredAudienceModelArn": {"shape": "ConfiguredAudienceModelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the configured audience model.</p>"}, "name": {"shape": "NameString", "documentation": "<p>The name of the configured audience model.</p>"}, "audienceModelArn": {"shape": "AudienceModelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the audience model used for this configured audience model.</p>"}, "outputConfig": {"shape": "ConfiguredAudienceModelOutputConfig", "documentation": "<p>The output configuration of the configured audience model</p>"}, "description": {"shape": "ResourceDescription", "documentation": "<p>The description of the configured audience model.</p>"}, "status": {"shape": "ConfiguredAudienceModelStatus", "documentation": "<p>The status of the configured audience model.</p>"}, "sharedAudienceMetrics": {"shape": "MetricsList", "documentation": "<p>Whether audience metrics are shared.</p>"}, "minMatchingSeedSize": {"shape": "MinMatchingSeedSize", "documentation": "<p>The minimum number of users from the seed audience that must match with users in the training data of the audience model.</p>"}, "audienceSizeConfig": {"shape": "AudienceSizeConfig", "documentation": "<p>The list of output sizes of audiences that can be created using this configured audience model. A request to <a>StartAudienceGenerationJob</a> that uses this configured audience model must have an <code>audienceSize</code> selected from this list. You can use the <code>ABSOLUTE</code> <a>AudienceSize</a> to configure out audience sizes using the count of identifiers in the output. You can use the <code>Percentage</code> <a>AudienceSize</a> to configure sizes in the range 1-100 percent.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags that are associated to this configured audience model.</p>"}, "childResourceTagOnCreatePolicy": {"shape": "TagOnCreatePolicy", "documentation": "<p>Provides the <code>childResourceTagOnCreatePolicy</code> that was used for this configured audience model.</p>"}}}, "GetConfiguredModelAlgorithmAssociationRequest": {"type": "structure", "required": ["configuredModelAlgorithmAssociationArn", "membershipIdentifier"], "members": {"configuredModelAlgorithmAssociationArn": {"shape": "ConfiguredModelAlgorithmAssociationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the configured model algorithm association that you want to return information about.</p>", "location": "uri", "locationName": "configuredModelAlgorithmAssociationArn"}, "membershipIdentifier": {"shape": "UUID", "documentation": "<p>The membership ID of the member that created the configured model algorithm association.</p>", "location": "uri", "locationName": "membershipIdentifier"}}}, "GetConfiguredModelAlgorithmAssociationResponse": {"type": "structure", "required": ["createTime", "updateTime", "configuredModelAlgorithmAssociationArn", "membershipIdentifier", "collaborationIdentifier", "configuredModelAlgorithmArn", "name"], "members": {"createTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time at which the configured model algorithm association was created.</p>"}, "updateTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The most recent time at which the configured model algorithm association was updated.</p>"}, "configuredModelAlgorithmAssociationArn": {"shape": "ConfiguredModelAlgorithmAssociationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the configured model algorithm association.</p>"}, "membershipIdentifier": {"shape": "UUID", "documentation": "<p>The membership ID of the member that created the configured model algorithm association.</p>"}, "collaborationIdentifier": {"shape": "UUID", "documentation": "<p>The collaboration ID of the collaboration that contains the configured model algorithm association.</p>"}, "configuredModelAlgorithmArn": {"shape": "ConfiguredModelAlgorithmArn", "documentation": "<p>The Amazon Resource Name (ARN) of the configured model algorithm that was associated to the collaboration.</p>"}, "name": {"shape": "NameString", "documentation": "<p>The name of the configured model algorithm association.</p>"}, "privacyConfiguration": {"shape": "PrivacyConfiguration", "documentation": "<p>The privacy configuration information for the configured model algorithm association.</p>"}, "description": {"shape": "ResourceDescription", "documentation": "<p>The description of the configured model algorithm association.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The optional metadata that you applied to the resource to help you categorize and organize them. Each tag consists of a key and an optional value, both of which you define.</p> <p>The following basic restrictions apply to tags:</p> <ul> <li> <p>Maximum number of tags per resource - 50.</p> </li> <li> <p>For each resource, each tag key must be unique, and each tag key can have only one value.</p> </li> <li> <p>Maximum key length - 128 Unicode characters in UTF-8.</p> </li> <li> <p>Maximum value length - 256 Unicode characters in UTF-8.</p> </li> <li> <p>If your tagging schema is used across multiple services and resources, remember that other services may have restrictions on allowed characters. Generally allowed characters are: letters, numbers, and spaces representable in UTF-8, and the following characters: + - = . _ : / @.</p> </li> <li> <p>Tag keys and values are case sensitive.</p> </li> <li> <p>Do not use aws:, AWS:, or any upper or lowercase combination of such as a prefix for keys as it is reserved for AWS use. You cannot edit or delete tag keys with this prefix. Values can have this prefix. If a tag value has aws as its prefix but the key does not, then Clean Rooms ML considers it to be a user tag and will count against the limit of 50 tags. Tags with only the key prefix of aws do not count against your tags per resource limit.</p> </li> </ul>"}}}, "GetConfiguredModelAlgorithmRequest": {"type": "structure", "required": ["configuredModelAlgorithmArn"], "members": {"configuredModelAlgorithmArn": {"shape": "ConfiguredModelAlgorithmArn", "documentation": "<p>The Amazon Resource Name (ARN) of the configured model algorithm that you want to return information about.</p>", "location": "uri", "locationName": "configuredModelAlgorithmArn"}}}, "GetConfiguredModelAlgorithmResponse": {"type": "structure", "required": ["createTime", "updateTime", "configuredModelAlgorithmArn", "name", "roleArn"], "members": {"createTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time at which the configured model algorithm was created.</p>"}, "updateTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The most recent time at which the configured model algorithm was updated.</p>"}, "configuredModelAlgorithmArn": {"shape": "ConfiguredModelAlgorithmArn", "documentation": "<p>The Amazon Resource Name (ARN) of the configured model algorithm.</p>"}, "name": {"shape": "NameString", "documentation": "<p>The name of the configured model algorithm.</p>"}, "trainingContainerConfig": {"shape": "ContainerConfig", "documentation": "<p>The configuration information of the training container for the configured model algorithm.</p>"}, "inferenceContainerConfig": {"shape": "InferenceContainerConfig", "documentation": "<p>Configuration information for the inference container.</p>"}, "roleArn": {"shape": "IamRoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the service role that was used to create the configured model algorithm.</p>"}, "description": {"shape": "ResourceDescription", "documentation": "<p>The description of the configured model algorithm.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The optional metadata that you applied to the resource to help you categorize and organize them. Each tag consists of a key and an optional value, both of which you define.</p> <p>The following basic restrictions apply to tags:</p> <ul> <li> <p>Maximum number of tags per resource - 50.</p> </li> <li> <p>For each resource, each tag key must be unique, and each tag key can have only one value.</p> </li> <li> <p>Maximum key length - 128 Unicode characters in UTF-8.</p> </li> <li> <p>Maximum value length - 256 Unicode characters in UTF-8.</p> </li> <li> <p>If your tagging schema is used across multiple services and resources, remember that other services may have restrictions on allowed characters. Generally allowed characters are: letters, numbers, and spaces representable in UTF-8, and the following characters: + - = . _ : / @.</p> </li> <li> <p>Tag keys and values are case sensitive.</p> </li> <li> <p>Do not use aws:, AWS:, or any upper or lowercase combination of such as a prefix for keys as it is reserved for AWS use. You cannot edit or delete tag keys with this prefix. Values can have this prefix. If a tag value has aws as its prefix but the key does not, then Clean Rooms ML considers it to be a user tag and will count against the limit of 50 tags. Tags with only the key prefix of aws do not count against your tags per resource limit.</p> </li> </ul>"}, "kmsKeyArn": {"shape": "KmsKeyArn", "documentation": "<p>The Amazon Resource Name (ARN) of the KMS key. This key is used to encrypt and decrypt customer-owned data in the configured ML model and associated data.</p>"}}}, "GetMLConfigurationRequest": {"type": "structure", "required": ["membershipIdentifier"], "members": {"membershipIdentifier": {"shape": "UUID", "documentation": "<p>The membership ID of the member that owns the ML configuration you want to return information about.</p>", "location": "uri", "locationName": "membershipIdentifier"}}}, "GetMLConfigurationResponse": {"type": "structure", "required": ["membershipIdentifier", "defaultOutputLocation", "createTime", "updateTime"], "members": {"membershipIdentifier": {"shape": "UUID", "documentation": "<p>The membership ID of the member that owns the ML configuration you requested.</p>"}, "defaultOutputLocation": {"shape": "MLOutputConfiguration", "documentation": "<p>The Amazon S3 location where ML model output is stored.</p>"}, "createTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time at which the ML configuration was created.</p>"}, "updateTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The most recent time at which the ML configuration was updated.</p>"}}}, "GetMLInputChannelRequest": {"type": "structure", "required": ["mlInputChannelArn", "membershipIdentifier"], "members": {"mlInputChannelArn": {"shape": "MLInputChannelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the ML input channel that you want to get.</p>", "location": "uri", "locationName": "mlInputChannelArn"}, "membershipIdentifier": {"shape": "UUID", "documentation": "<p>The membership ID of the membership that contains the ML input channel that you want to get.</p>", "location": "uri", "locationName": "membershipIdentifier"}}}, "GetMLInputChannelResponse": {"type": "structure", "required": ["createTime", "updateTime", "membershipIdentifier", "collaborationIdentifier", "inputChannel", "mlInputChannelArn", "name", "configuredModelAlgorithmAssociations", "status", "retentionInDays"], "members": {"createTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time at which the ML input channel was created.</p>"}, "updateTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The most recent time at which the ML input channel was updated.</p>"}, "membershipIdentifier": {"shape": "UUID", "documentation": "<p>The membership ID of the membership that contains the ML input channel.</p>"}, "collaborationIdentifier": {"shape": "UUID", "documentation": "<p>The collaboration ID of the collaboration that contains the ML input channel.</p>"}, "inputChannel": {"shape": "InputChannel", "documentation": "<p>The input channel that was used to create the ML input channel.</p>"}, "protectedQueryIdentifier": {"shape": "UUID", "documentation": "<p>The ID of the protected query that was used to create the ML input channel.</p>"}, "mlInputChannelArn": {"shape": "MLInputChannelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the ML input channel.</p>"}, "name": {"shape": "NameString", "documentation": "<p>The name of the ML input channel.</p>"}, "configuredModelAlgorithmAssociations": {"shape": "GetMLInputChannelResponseConfiguredModelAlgorithmAssociationsList", "documentation": "<p>The configured model algorithm associations that were used to create the ML input channel.</p>"}, "status": {"shape": "MLInputChannelStatus", "documentation": "<p>The status of the ML input channel.</p>"}, "statusDetails": {"shape": "StatusDetails"}, "retentionInDays": {"shape": "GetMLInputChannelResponseRetentionInDaysInteger", "documentation": "<p>The number of days to keep the data in the ML input channel.</p>"}, "numberOfRecords": {"shape": "GetMLInputChannelResponseNumberOfRecordsLong", "documentation": "<p>The number of records in the ML input channel.</p>"}, "numberOfFiles": {"shape": "GetMLInputChannelResponseNumberOfFilesDouble", "documentation": "<p>The number of files in the ML input channel.</p>"}, "sizeInGb": {"shape": "GetMLInputChannelResponseSizeInGbDouble", "documentation": "<p>The size, in GB, of the ML input channel.</p>"}, "description": {"shape": "ResourceDescription", "documentation": "<p>The description of the ML input channel.</p>"}, "kmsKeyArn": {"shape": "KmsKeyArn", "documentation": "<p>The Amazon Resource Name (ARN) of the KMS key that was used to create the ML input channel.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The optional metadata that you applied to the resource to help you categorize and organize them. Each tag consists of a key and an optional value, both of which you define.</p> <p>The following basic restrictions apply to tags:</p> <ul> <li> <p>Maximum number of tags per resource - 50.</p> </li> <li> <p>For each resource, each tag key must be unique, and each tag key can have only one value.</p> </li> <li> <p>Maximum key length - 128 Unicode characters in UTF-8.</p> </li> <li> <p>Maximum value length - 256 Unicode characters in UTF-8.</p> </li> <li> <p>If your tagging schema is used across multiple services and resources, remember that other services may have restrictions on allowed characters. Generally allowed characters are: letters, numbers, and spaces representable in UTF-8, and the following characters: + - = . _ : / @.</p> </li> <li> <p>Tag keys and values are case sensitive.</p> </li> <li> <p>Do not use aws:, AWS:, or any upper or lowercase combination of such as a prefix for keys as it is reserved for AWS use. You cannot edit or delete tag keys with this prefix. Values can have this prefix. If a tag value has aws as its prefix but the key does not, then Clean Rooms ML considers it to be a user tag and will count against the limit of 50 tags. Tags with only the key prefix of aws do not count against your tags per resource limit.</p> </li> </ul>"}}}, "GetMLInputChannelResponseConfiguredModelAlgorithmAssociationsList": {"type": "list", "member": {"shape": "ConfiguredModelAlgorithmAssociationArn"}, "max": 1, "min": 1}, "GetMLInputChannelResponseNumberOfFilesDouble": {"type": "double", "box": true, "max": 1000000, "min": 0}, "GetMLInputChannelResponseNumberOfRecordsLong": {"type": "long", "box": true, "max": 100000000000, "min": 0}, "GetMLInputChannelResponseRetentionInDaysInteger": {"type": "integer", "box": true, "max": 30, "min": 1}, "GetMLInputChannelResponseSizeInGbDouble": {"type": "double", "box": true, "max": 1000000, "min": 0}, "GetTrainedModelInferenceJobRequest": {"type": "structure", "required": ["membershipIdentifier", "trainedModelInferenceJobArn"], "members": {"membershipIdentifier": {"shape": "UUID", "documentation": "<p>Provides the membership ID of the membership that contains the trained model inference job that you are interested in.</p>", "location": "uri", "locationName": "membershipIdentifier"}, "trainedModelInferenceJobArn": {"shape": "TrainedModelInferenceJobArn", "documentation": "<p>Provides the Amazon Resource Name (ARN) of the trained model inference job that you are interested in.</p>", "location": "uri", "locationName": "trainedModelInferenceJobArn"}}}, "GetTrainedModelInferenceJobResponse": {"type": "structure", "required": ["createTime", "updateTime", "trainedModelInferenceJobArn", "name", "status", "trainedModelArn", "resourceConfig", "outputConfiguration", "membershipIdentifier", "dataSource"], "members": {"createTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time at which the trained model inference job was created.</p>"}, "updateTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The most recent time at which the trained model inference job was updated.</p>"}, "trainedModelInferenceJobArn": {"shape": "TrainedModelInferenceJobArn", "documentation": "<p>The Amazon Resource Name (ARN) of the trained model inference job.</p>"}, "configuredModelAlgorithmAssociationArn": {"shape": "ConfiguredModelAlgorithmAssociationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the configured model algorithm association that was used for the trained model inference job.</p>"}, "name": {"shape": "NameString", "documentation": "<p>The name of the trained model inference job.</p>"}, "status": {"shape": "TrainedModelInferenceJobStatus", "documentation": "<p>The status of the trained model inference job.</p>"}, "trainedModelArn": {"shape": "TrainedModelArn", "documentation": "<p>The Amazon Resource Name (ARN) for the trained model that was used for the trained model inference job.</p>"}, "trainedModelVersionIdentifier": {"shape": "UUID", "documentation": "<p>The version identifier of the trained model used for this inference job. This identifies the specific version of the trained model that was used to generate the inference results.</p>"}, "resourceConfig": {"shape": "InferenceResourceConfig", "documentation": "<p>The resource configuration information for the trained model inference job.</p>"}, "outputConfiguration": {"shape": "InferenceOutputConfiguration", "documentation": "<p>The output configuration information for the trained model inference job.</p>"}, "membershipIdentifier": {"shape": "UUID", "documentation": "<p>The membership ID of the membership that contains the trained model inference job.</p>"}, "dataSource": {"shape": "ModelInferenceDataSource", "documentation": "<p>The data source that was used for the trained model inference job.</p>"}, "containerExecutionParameters": {"shape": "InferenceContainerExecutionParameters", "documentation": "<p>The execution parameters for the model inference job container.</p>"}, "statusDetails": {"shape": "StatusDetails"}, "description": {"shape": "ResourceDescription", "documentation": "<p>The description of the trained model inference job.</p>"}, "inferenceContainerImageDigest": {"shape": "String", "documentation": "<p>Information about the training container image.</p>"}, "environment": {"shape": "InferenceEnvironmentMap", "documentation": "<p>The environment variables to set in the Docker container.</p>"}, "kmsKeyArn": {"shape": "KmsKeyArn", "documentation": "<p>The Amazon Resource Name (ARN) of the KMS key. This key is used to encrypt and decrypt customer-owned data in the ML inference job and associated data.</p>"}, "metricsStatus": {"shape": "MetricsStatus", "documentation": "<p>The metrics status for the trained model inference job.</p>"}, "metricsStatusDetails": {"shape": "String", "documentation": "<p>Details about the metrics status for the trained model inference job.</p>"}, "logsStatus": {"shape": "LogsStatus", "documentation": "<p>The logs status for the trained model inference job.</p>"}, "logsStatusDetails": {"shape": "String", "documentation": "<p>Details about the logs status for the trained model inference job.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The optional metadata that you applied to the resource to help you categorize and organize them. Each tag consists of a key and an optional value, both of which you define.</p> <p>The following basic restrictions apply to tags:</p> <ul> <li> <p>Maximum number of tags per resource - 50.</p> </li> <li> <p>For each resource, each tag key must be unique, and each tag key can have only one value.</p> </li> <li> <p>Maximum key length - 128 Unicode characters in UTF-8.</p> </li> <li> <p>Maximum value length - 256 Unicode characters in UTF-8.</p> </li> <li> <p>If your tagging schema is used across multiple services and resources, remember that other services may have restrictions on allowed characters. Generally allowed characters are: letters, numbers, and spaces representable in UTF-8, and the following characters: + - = . _ : / @.</p> </li> <li> <p>Tag keys and values are case sensitive.</p> </li> <li> <p>Do not use aws:, AWS:, or any upper or lowercase combination of such as a prefix for keys as it is reserved for AWS use. You cannot edit or delete tag keys with this prefix. Values can have this prefix. If a tag value has aws as its prefix but the key does not, then Clean Rooms ML considers it to be a user tag and will count against the limit of 50 tags. Tags with only the key prefix of aws do not count against your tags per resource limit.</p> </li> </ul>"}}}, "GetTrainedModelRequest": {"type": "structure", "required": ["trainedModelArn", "membershipIdentifier"], "members": {"trainedModelArn": {"shape": "TrainedModelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the trained model that you are interested in.</p>", "location": "uri", "locationName": "trainedModelArn"}, "membershipIdentifier": {"shape": "UUID", "documentation": "<p>The membership ID of the member that created the trained model that you are interested in.</p>", "location": "uri", "locationName": "membershipIdentifier"}, "versionIdentifier": {"shape": "UUID", "documentation": "<p>The version identifier of the trained model to retrieve. If not specified, the operation returns information about the latest version of the trained model.</p>", "location": "querystring", "locationName": "versionIdentifier"}}}, "GetTrainedModelResponse": {"type": "structure", "required": ["membershipIdentifier", "collaborationIdentifier", "trainedModelArn", "name", "status", "configuredModelAlgorithmAssociationArn", "createTime", "updateTime", "dataChannels"], "members": {"membershipIdentifier": {"shape": "UUID", "documentation": "<p>The membership ID of the member that created the trained model.</p>"}, "collaborationIdentifier": {"shape": "UUID", "documentation": "<p>The collaboration ID of the collaboration that contains the trained model.</p>"}, "trainedModelArn": {"shape": "TrainedModelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the trained model.</p>"}, "versionIdentifier": {"shape": "UUID", "documentation": "<p>The version identifier of the trained model. This unique identifier distinguishes this version from other versions of the same trained model.</p>"}, "incrementalTrainingDataChannels": {"shape": "IncrementalTrainingDataChannelsOutput", "documentation": "<p>Information about the incremental training data channels used to create this version of the trained model. This includes details about the base model that was used for incremental training and the channel configuration.</p>"}, "name": {"shape": "NameString", "documentation": "<p>The name of the trained model.</p>"}, "description": {"shape": "ResourceDescription", "documentation": "<p>The description of the trained model.</p>"}, "status": {"shape": "TrainedModelStatus", "documentation": "<p>The status of the trained model.</p>"}, "statusDetails": {"shape": "StatusDetails"}, "configuredModelAlgorithmAssociationArn": {"shape": "ConfiguredModelAlgorithmAssociationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the configured model algorithm association that was used to create the trained model.</p>"}, "resourceConfig": {"shape": "ResourceConfig", "documentation": "<p>The EC2 resource configuration that was used to create the trained model.</p>"}, "trainingInputMode": {"shape": "TrainingInputMode", "documentation": "<p>The input mode that was used for accessing the training data when this trained model was created. This indicates how the training data was made available to the training algorithm.</p>"}, "stoppingCondition": {"shape": "StoppingCondition", "documentation": "<p>The stopping condition that was used to terminate model training.</p>"}, "metricsStatus": {"shape": "MetricsStatus", "documentation": "<p>The status of the model metrics.</p>"}, "metricsStatusDetails": {"shape": "String", "documentation": "<p>Details about the metrics status for the trained model.</p>"}, "logsStatus": {"shape": "LogsStatus", "documentation": "<p>The logs status for the trained model.</p>"}, "logsStatusDetails": {"shape": "String", "documentation": "<p>Details about the logs status for the trained model.</p>"}, "trainingContainerImageDigest": {"shape": "String", "documentation": "<p>Information about the training image container.</p>"}, "createTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time at which the trained model was created.</p>"}, "updateTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The most recent time at which the trained model was updated.</p>"}, "hyperparameters": {"shape": "HyperParameters", "documentation": "<p>The hyperparameters that were used to create the trained model.</p>"}, "environment": {"shape": "Environment", "documentation": "<p>The EC2 environment that was used to create the trained model.</p>"}, "kmsKeyArn": {"shape": "KmsKeyArn", "documentation": "<p>The Amazon Resource Name (ARN) of the KMS key. This key is used to encrypt and decrypt customer-owned data in the trained ML model and associated data.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The optional metadata that you applied to the resource to help you categorize and organize them. Each tag consists of a key and an optional value, both of which you define.</p> <p>The following basic restrictions apply to tags:</p> <ul> <li> <p>Maximum number of tags per resource - 50.</p> </li> <li> <p>For each resource, each tag key must be unique, and each tag key can have only one value.</p> </li> <li> <p>Maximum key length - 128 Unicode characters in UTF-8.</p> </li> <li> <p>Maximum value length - 256 Unicode characters in UTF-8.</p> </li> <li> <p>If your tagging schema is used across multiple services and resources, remember that other services may have restrictions on allowed characters. Generally allowed characters are: letters, numbers, and spaces representable in UTF-8, and the following characters: + - = . _ : / @.</p> </li> <li> <p>Tag keys and values are case sensitive.</p> </li> <li> <p>Do not use aws:, AWS:, or any upper or lowercase combination of such as a prefix for keys as it is reserved for AWS use. You cannot edit or delete tag keys with this prefix. Values can have this prefix. If a tag value has aws as its prefix but the key does not, then Clean Rooms ML considers it to be a user tag and will count against the limit of 50 tags. Tags with only the key prefix of aws do not count against your tags per resource limit.</p> </li> </ul>"}, "dataChannels": {"shape": "ModelTrainingDataChannels", "documentation": "<p>The data channels that were used for the trained model.</p>"}}}, "GetTrainingDatasetRequest": {"type": "structure", "required": ["trainingDatasetArn"], "members": {"trainingDatasetArn": {"shape": "TrainingDatasetArn", "documentation": "<p>The Amazon Resource Name (ARN) of the training dataset that you are interested in.</p>", "location": "uri", "locationName": "trainingDatasetArn"}}}, "GetTrainingDatasetResponse": {"type": "structure", "required": ["createTime", "updateTime", "trainingDatasetArn", "name", "trainingData", "status", "roleArn"], "members": {"createTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time at which the training dataset was created.</p>"}, "updateTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The most recent time at which the training dataset was updated.</p>"}, "trainingDatasetArn": {"shape": "TrainingDatasetArn", "documentation": "<p>The Amazon Resource Name (ARN) of the training dataset.</p>"}, "name": {"shape": "NameString", "documentation": "<p>The name of the training dataset.</p>"}, "trainingData": {"shape": "DatasetList", "documentation": "<p><PERSON><PERSON><PERSON> about the requested training data. </p>"}, "status": {"shape": "TrainingDatasetStatus", "documentation": "<p>The status of the training dataset.</p>"}, "roleArn": {"shape": "IamRoleArn", "documentation": "<p>The IAM role used to read the training data.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags that are assigned to this training dataset.</p>"}, "description": {"shape": "ResourceDescription", "documentation": "<p>The description of the training dataset.</p>"}}}, "GlueDataSource": {"type": "structure", "required": ["tableName", "databaseName"], "members": {"tableName": {"shape": "GlueTableName", "documentation": "<p>The Glue table that contains the training data.</p>"}, "databaseName": {"shape": "GlueDatabaseName", "documentation": "<p>The Glue database that contains the training data.</p>"}, "catalogId": {"shape": "AccountId", "documentation": "<p>The Glue catalog that contains the training data.</p>"}}, "documentation": "<p>Defines the Glue data source that contains the training data.</p>"}, "GlueDatabaseName": {"type": "string", "max": 128, "min": 1, "pattern": "[a-zA-Z0-9_](([a-zA-Z0-9_]+-)*([a-zA-Z0-9_]+))?"}, "GlueTableName": {"type": "string", "max": 128, "min": 1, "pattern": "[a-zA-Z0-9_](([a-zA-Z0-9_ ]+-)*([a-zA-Z0-9_ ]+))?"}, "Hash": {"type": "string", "max": 128, "min": 64, "pattern": "[0-9a-f]+"}, "HyperParameters": {"type": "map", "key": {"shape": "HyperParametersKeyString"}, "value": {"shape": "HyperParametersValueString"}, "max": 100, "min": 0}, "HyperParametersKeyString": {"type": "string", "max": 256, "min": 1, "pattern": ".*"}, "HyperParametersValueString": {"type": "string", "max": 2500, "min": 1, "pattern": ".*"}, "IamRoleArn": {"type": "string", "max": 2048, "min": 20, "pattern": "arn:aws[-a-z]*:iam::[0-9]{12}:role/.+"}, "IncrementalTrainingDataChannel": {"type": "structure", "required": ["trainedModelArn", "channelName"], "members": {"trainedModelArn": {"shape": "TrainedModelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the base trained model to use for incremental training. This model serves as the starting point for the incremental training process.</p>"}, "versionIdentifier": {"shape": "UUID", "documentation": "<p>The version identifier of the base trained model to use for incremental training. If not specified, the latest version of the trained model is used.</p>"}, "channelName": {"shape": "ModelTrainingDataChannelName", "documentation": "<p>The name of the incremental training data channel. This name is used to identify the channel during the training process and must be unique within the training job.</p>"}}, "documentation": "<p>Defines an incremental training data channel that references a previously trained model. Incremental training allows you to update an existing trained model with new data, building upon the knowledge from a base model rather than training from scratch. This can significantly reduce training time and computational costs while improving model performance with additional data.</p>"}, "IncrementalTrainingDataChannelOutput": {"type": "structure", "required": ["channelName", "modelName"], "members": {"channelName": {"shape": "ModelTrainingDataChannelName", "documentation": "<p>The name of the incremental training data channel that was used.</p>"}, "versionIdentifier": {"shape": "UUID", "documentation": "<p>The version identifier of the trained model that was used for incremental training.</p>"}, "modelName": {"shape": "NameString", "documentation": "<p>The name of the base trained model that was used for incremental training.</p>"}}, "documentation": "<p>Contains information about an incremental training data channel that was used to create a trained model. This structure provides details about the base model and channel configuration used during incremental training.</p>"}, "IncrementalTrainingDataChannels": {"type": "list", "member": {"shape": "IncrementalTrainingDataChannel"}, "max": 1, "min": 1}, "IncrementalTrainingDataChannelsOutput": {"type": "list", "member": {"shape": "IncrementalTrainingDataChannelOutput"}, "max": 1, "min": 1}, "InferenceContainerConfig": {"type": "structure", "required": ["imageUri"], "members": {"imageUri": {"shape": "AlgorithmImage", "documentation": "<p>The registry path of the docker image that contains the inference algorithm. Clean Rooms ML currently only supports the <code>registry/repository[:tag]</code> image path format. For more information about using images in Clean Rooms ML, see the <a href=\"https://docs.aws.amazon.com/sagemaker/latest/APIReference/API_AlgorithmSpecification.html#sagemaker-Type-AlgorithmSpecification-TrainingImage\">Sagemaker API reference</a>.</p>"}}, "documentation": "<p>Provides configuration information for the inference container.</p>"}, "InferenceContainerExecutionParameters": {"type": "structure", "members": {"maxPayloadInMB": {"shape": "InferenceContainerExecutionParametersMaxPayloadInMBInteger", "documentation": "<p>The maximum size of the inference container payload, specified in MB. </p>"}}, "documentation": "<p>Provides execution parameters for the inference container.</p>"}, "InferenceContainerExecutionParametersMaxPayloadInMBInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "InferenceEnvironmentMap": {"type": "map", "key": {"shape": "InferenceEnvironmentMapKeyString"}, "value": {"shape": "InferenceEnvironmentMapValueString"}, "max": 16, "min": 0}, "InferenceEnvironmentMapKeyString": {"type": "string", "max": 1024, "min": 1, "pattern": "[a-zA-Z_][a-zA-Z0-9_]*"}, "InferenceEnvironmentMapValueString": {"type": "string", "max": 10240, "min": 1, "pattern": "[\\S\\s]*"}, "InferenceInstanceType": {"type": "string", "enum": ["ml.r7i.48xlarge", "ml.r6i.16xlarge", "ml.m6i.xlarge", "ml.m5.4xlarge", "ml.p2.xlarge", "ml.m4.16xlarge", "ml.r7i.16xlarge", "ml.m7i.xlarge", "ml.m6i.12xlarge", "ml.r7i.8xlarge", "ml.r7i.large", "ml.m7i.12xlarge", "ml.m6i.24xlarge", "ml.m7i.24xlarge", "ml.r6i.8xlarge", "ml.r6i.large", "ml.g5.2xlarge", "ml.m5.large", "ml.p3.16xlarge", "ml.m7i.48xlarge", "ml.m6i.16xlarge", "ml.p2.16xlarge", "ml.g5.4xlarge", "ml.m7i.16xlarge", "ml.c4.2xlarge", "ml.c5.2xlarge", "ml.c6i.32xlarge", "ml.c4.4xlarge", "ml.g5.8xlarge", "ml.c6i.xlarge", "ml.c5.4xlarge", "ml.g4dn.xlarge", "ml.c7i.xlarge", "ml.c6i.12xlarge", "ml.g4dn.12xlarge", "ml.c7i.12xlarge", "ml.c6i.24xlarge", "ml.g4dn.2xlarge", "ml.c7i.24xlarge", "ml.c7i.2xlarge", "ml.c4.8xlarge", "ml.c6i.2xlarge", "ml.g4dn.4xlarge", "ml.c7i.48xlarge", "ml.c7i.4xlarge", "ml.c6i.16xlarge", "ml.c5.9xlarge", "ml.g4dn.16xlarge", "ml.c7i.16xlarge", "ml.c6i.4xlarge", "ml.c5.xlarge", "ml.c4.xlarge", "ml.g4dn.8xlarge", "ml.c7i.8xlarge", "ml.c7i.large", "ml.g5.xlarge", "ml.c6i.8xlarge", "ml.c6i.large", "ml.g5.12xlarge", "ml.g5.24xlarge", "ml.m7i.2xlarge", "ml.c5.18xlarge", "ml.g5.48xlarge", "ml.m6i.2xlarge", "ml.g5.16xlarge", "ml.m7i.4xlarge", "ml.p3.2xlarge", "ml.r6i.32xlarge", "ml.m6i.4xlarge", "ml.m5.xlarge", "ml.m4.10xlarge", "ml.r6i.xlarge", "ml.m5.12xlarge", "ml.m4.xlarge", "ml.r7i.2xlarge", "ml.r7i.xlarge", "ml.r6i.12xlarge", "ml.m5.24xlarge", "ml.r7i.12xlarge", "ml.m7i.8xlarge", "ml.m7i.large", "ml.r6i.24xlarge", "ml.r6i.2xlarge", "ml.m4.2xlarge", "ml.r7i.24xlarge", "ml.r7i.4xlarge", "ml.m6i.8xlarge", "ml.m6i.large", "ml.m5.2xlarge", "ml.p2.8xlarge", "ml.r6i.4xlarge", "ml.m6i.32xlarge", "ml.p3.8xlarge", "ml.m4.4xlarge"]}, "InferenceOutputConfiguration": {"type": "structure", "required": ["members"], "members": {"accept": {"shape": "InferenceOutputConfigurationAcceptString", "documentation": "<p>The MIME type used to specify the output data.</p>"}, "members": {"shape": "InferenceReceiverMembers", "documentation": "<p>Defines the members that can receive inference output.</p>"}}, "documentation": "<p>Configuration information about how the inference output is stored.</p>"}, "InferenceOutputConfigurationAcceptString": {"type": "string", "max": 256, "min": 0, "pattern": ".*"}, "InferenceReceiverMember": {"type": "structure", "required": ["accountId"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The account ID of the member that can receive inference results.</p>"}}, "documentation": "<p>Defines who will receive inference results.</p>"}, "InferenceReceiverMembers": {"type": "list", "member": {"shape": "InferenceReceiverMember"}, "max": 1, "min": 1}, "InferenceResourceConfig": {"type": "structure", "required": ["instanceType"], "members": {"instanceType": {"shape": "InferenceInstanceType", "documentation": "<p>The type of instance that is used to perform model inference.</p>"}, "instanceCount": {"shape": "InferenceResourceConfigInstanceCountInteger", "documentation": "<p>The number of instances to use.</p>"}}, "documentation": "<p>Defines the resources used to perform model inference.</p>"}, "InferenceResourceConfigInstanceCountInteger": {"type": "integer", "box": true, "max": 10, "min": 1}, "InputChannel": {"type": "structure", "required": ["dataSource", "roleArn"], "members": {"dataSource": {"shape": "InputChannelDataSource", "documentation": "<p>The data source that is used to create the ML input channel.</p>"}, "roleArn": {"shape": "IamRoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the role used to run the query specified in the <code>dataSource</code> field of the input channel.</p> <p>Passing a role across AWS accounts is not allowed. If you pass a role that isn't in your account, you get an <code>AccessDeniedException</code> error.</p>"}}, "documentation": "<p>Provides information about the data source that is used to create an ML input channel.</p>"}, "InputChannelDataSource": {"type": "structure", "members": {"protectedQueryInputParameters": {"shape": "ProtectedQueryInputParameters"}}, "documentation": "<p>Provides the data source that is used to define an input channel.</p>", "union": true}, "InstanceType": {"type": "string", "enum": ["ml.m4.xlarge", "ml.m4.2xlarge", "ml.m4.4xlarge", "ml.m4.10xlarge", "ml.m4.16xlarge", "ml.g4dn.xlarge", "ml.g4dn.2xlarge", "ml.g4dn.4xlarge", "ml.g4dn.8xlarge", "ml.g4dn.12xlarge", "ml.g4dn.16xlarge", "ml.m5.large", "ml.m5.xlarge", "ml.m5.2xlarge", "ml.m5.4xlarge", "ml.m5.12xlarge", "ml.m5.24xlarge", "ml.c4.xlarge", "ml.c4.2xlarge", "ml.c4.4xlarge", "ml.c4.8xlarge", "ml.p2.xlarge", "ml.p2.8xlarge", "ml.p2.16xlarge", "ml.p3.2xlarge", "ml.p3.8xlarge", "ml.p3.16xlarge", "ml.p3dn.24xlarge", "ml.p4d.24xlarge", "ml.p4de.24xlarge", "ml.p5.48xlarge", "ml.c5.xlarge", "ml.c5.2xlarge", "ml.c5.4xlarge", "ml.c5.9xlarge", "ml.c5.18xlarge", "ml.c5n.xlarge", "ml.c5n.2xlarge", "ml.c5n.4xlarge", "ml.c5n.9xlarge", "ml.c5n.18xlarge", "ml.g5.xlarge", "ml.g5.2xlarge", "ml.g5.4xlarge", "ml.g5.8xlarge", "ml.g5.16xlarge", "ml.g5.12xlarge", "ml.g5.24xlarge", "ml.g5.48xlarge", "ml.trn1.2xlarge", "ml.trn1.32xlarge", "ml.trn1n.32xlarge", "ml.m6i.large", "ml.m6i.xlarge", "ml.m6i.2xlarge", "ml.m6i.4xlarge", "ml.m6i.8xlarge", "ml.m6i.12xlarge", "ml.m6i.16xlarge", "ml.m6i.24xlarge", "ml.m6i.32xlarge", "ml.c6i.xlarge", "ml.c6i.2xlarge", "ml.c6i.8xlarge", "ml.c6i.4xlarge", "ml.c6i.12xlarge", "ml.c6i.16xlarge", "ml.c6i.24xlarge", "ml.c6i.32xlarge", "ml.r5d.large", "ml.r5d.xlarge", "ml.r5d.2xlarge", "ml.r5d.4xlarge", "ml.r5d.8xlarge", "ml.r5d.12xlarge", "ml.r5d.16xlarge", "ml.r5d.24xlarge", "ml.t3.medium", "ml.t3.large", "ml.t3.xlarge", "ml.t3.2xlarge", "ml.r5.large", "ml.r5.xlarge", "ml.r5.2xlarge", "ml.r5.4xlarge", "ml.r5.8xlarge", "ml.r5.12xlarge", "ml.r5.16xlarge", "ml.r5.24xlarge"]}, "InternalServiceException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>An internal service error occurred. Retry your request. If the problem persists, contact AWS Support.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true}, "KmsKeyArn": {"type": "string", "max": 2048, "min": 20, "pattern": "arn:aws[-a-z]*:kms:[-a-z0-9]+:[0-9]{12}:key/.+"}, "ListAudienceExportJobsRequest": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token value retrieved from a previous call to access the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum size of the results that is returned per call.</p>", "location": "querystring", "locationName": "maxResults"}, "audienceGenerationJobArn": {"shape": "AudienceGenerationJobArn", "documentation": "<p>The Amazon Resource Name (ARN) of the audience generation job that you are interested in.</p>", "location": "querystring", "locationName": "audienceGenerationJobArn"}}}, "ListAudienceExportJobsResponse": {"type": "structure", "required": ["audienceExportJobs"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token value used to access the next page of results.</p>"}, "audienceExportJobs": {"shape": "AudienceExportJobList", "documentation": "<p>The audience export jobs that match the request.</p>"}}}, "ListAudienceGenerationJobsRequest": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token value retrieved from a previous call to access the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum size of the results that is returned per call.</p>", "location": "querystring", "locationName": "maxResults"}, "configuredAudienceModelArn": {"shape": "ConfiguredAudienceModelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the configured audience model that was used for the audience generation jobs that you are interested in.</p>", "location": "querystring", "locationName": "configuredAudienceModelArn"}, "collaborationId": {"shape": "UUID", "documentation": "<p>The identifier of the collaboration that contains the audience generation jobs that you are interested in.</p>", "location": "querystring", "locationName": "collaborationId"}}}, "ListAudienceGenerationJobsResponse": {"type": "structure", "required": ["audienceGenerationJobs"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token value used to access the next page of results.</p>"}, "audienceGenerationJobs": {"shape": "AudienceGenerationJobList", "documentation": "<p>The audience generation jobs that match the request.</p>"}}}, "ListAudienceModelsRequest": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token value retrieved from a previous call to access the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum size of the results that is returned per call.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListAudienceModelsResponse": {"type": "structure", "required": ["audienceModels"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token value used to access the next page of results.</p>"}, "audienceModels": {"shape": "AudienceModelList", "documentation": "<p>The audience models that match the request.</p>"}}}, "ListCollaborationConfiguredModelAlgorithmAssociationsRequest": {"type": "structure", "required": ["collaborationIdentifier"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token value retrieved from a previous call to access the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum size of the results that is returned per call.</p>", "location": "querystring", "locationName": "maxResults"}, "collaborationIdentifier": {"shape": "UUID", "documentation": "<p>The collaboration ID of the collaboration that contains the configured model algorithm associations that you are interested in.</p>", "location": "uri", "locationName": "collaborationIdentifier"}}}, "ListCollaborationConfiguredModelAlgorithmAssociationsResponse": {"type": "structure", "required": ["collaborationConfiguredModelAlgorithmAssociations"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token value used to access the next page of results.</p>"}, "collaborationConfiguredModelAlgorithmAssociations": {"shape": "CollaborationConfiguredModelAlgorithmAssociationList", "documentation": "<p>The configured model algorithm associations that belong to this collaboration.</p>"}}}, "ListCollaborationMLInputChannelsRequest": {"type": "structure", "required": ["collaborationIdentifier"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token value retrieved from a previous call to access the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return.</p>", "location": "querystring", "locationName": "maxResults"}, "collaborationIdentifier": {"shape": "UUID", "documentation": "<p>The collaboration ID of the collaboration that contains the ML input channels that you want to list.</p>", "location": "uri", "locationName": "collaborationIdentifier"}}}, "ListCollaborationMLInputChannelsResponse": {"type": "structure", "required": ["collaborationMLInputChannelsList"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token value used to access the next page of results.</p>"}, "collaborationMLInputChannelsList": {"shape": "CollaborationMLInputChannelsList", "documentation": "<p>The list of ML input channels that you wanted.</p>"}}}, "ListCollaborationTrainedModelExportJobsRequest": {"type": "structure", "required": ["collaborationIdentifier", "trainedModelArn"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token value retrieved from a previous call to access the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum size of the results that is returned per call.</p>", "location": "querystring", "locationName": "maxResults"}, "collaborationIdentifier": {"shape": "UUID", "documentation": "<p>The collaboration ID of the collaboration that contains the trained model export jobs that you are interested in.</p>", "location": "uri", "locationName": "collaborationIdentifier"}, "trainedModelArn": {"shape": "TrainedModelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the trained model that was used to create the export jobs that you are interested in.</p>", "location": "uri", "locationName": "trainedModelArn"}, "trainedModelVersionIdentifier": {"shape": "UUID", "documentation": "<p>The version identifier of the trained model to filter export jobs by. When specified, only export jobs for this specific version of the trained model are returned.</p>", "location": "querystring", "locationName": "trainedModelVersionIdentifier"}}}, "ListCollaborationTrainedModelExportJobsResponse": {"type": "structure", "required": ["collaborationTrainedModelExportJobs"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token value used to access the next page of results.</p>"}, "collaborationTrainedModelExportJobs": {"shape": "CollaborationTrainedModelExportJobList", "documentation": "<p>The exports jobs that exist for the requested trained model in the requested collaboration.</p>"}}}, "ListCollaborationTrainedModelInferenceJobsRequest": {"type": "structure", "required": ["collaborationIdentifier"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token value retrieved from a previous call to access the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum size of the results that is returned per call.</p>", "location": "querystring", "locationName": "maxResults"}, "collaborationIdentifier": {"shape": "UUID", "documentation": "<p>The collaboration ID of the collaboration that contains the trained model inference jobs that you are interested in.</p>", "location": "uri", "locationName": "collaborationIdentifier"}, "trainedModelArn": {"shape": "TrainedModelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the trained model that was used to create the trained model inference jobs that you are interested in.</p>", "location": "querystring", "locationName": "trainedModelArn"}, "trainedModelVersionIdentifier": {"shape": "UUID", "documentation": "<p>The version identifier of the trained model to filter inference jobs by. When specified, only inference jobs that used this specific version of the trained model are returned.</p>", "location": "querystring", "locationName": "trainedModelVersionIdentifier"}}}, "ListCollaborationTrainedModelInferenceJobsResponse": {"type": "structure", "required": ["collaborationTrainedModelInferenceJobs"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token value used to access the next page of results.</p>"}, "collaborationTrainedModelInferenceJobs": {"shape": "CollaborationTrainedModelInferenceJobList", "documentation": "<p>The trained model inference jobs that you are interested in.</p>"}}}, "ListCollaborationTrainedModelsRequest": {"type": "structure", "required": ["collaborationIdentifier"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token value retrieved from a previous call to access the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum size of the results that is returned per call.</p>", "location": "querystring", "locationName": "maxResults"}, "collaborationIdentifier": {"shape": "UUID", "documentation": "<p>The collaboration ID of the collaboration that contains the trained models you are interested in.</p>", "location": "uri", "locationName": "collaborationIdentifier"}}}, "ListCollaborationTrainedModelsResponse": {"type": "structure", "required": ["collaborationTrainedModels"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token value used to access the next page of results.</p>"}, "collaborationTrainedModels": {"shape": "CollaborationTrainedModelList", "documentation": "<p>The trained models in the collaboration that you requested.</p>"}}}, "ListConfiguredAudienceModelsRequest": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token value retrieved from a previous call to access the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum size of the results that is returned per call.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListConfiguredAudienceModelsResponse": {"type": "structure", "required": ["configuredAudienceModels"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token value used to access the next page of results.</p>"}, "configuredAudienceModels": {"shape": "ConfiguredAudienceModelList", "documentation": "<p>The configured audience models.</p>"}}}, "ListConfiguredModelAlgorithmAssociationsRequest": {"type": "structure", "required": ["membershipIdentifier"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token value retrieved from a previous call to access the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum size of the results that is returned per call.</p>", "location": "querystring", "locationName": "maxResults"}, "membershipIdentifier": {"shape": "UUID", "documentation": "<p>The membership ID of the member that created the configured model algorithm associations you are interested in.</p>", "location": "uri", "locationName": "membershipIdentifier"}}}, "ListConfiguredModelAlgorithmAssociationsResponse": {"type": "structure", "required": ["configuredModelAlgorithmAssociations"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token value used to access the next page of results.</p>"}, "configuredModelAlgorithmAssociations": {"shape": "ConfiguredModelAlgorithmAssociationList", "documentation": "<p>The list of configured model algorithm associations.</p>"}}}, "ListConfiguredModelAlgorithmsRequest": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token value retrieved from a previous call to access the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum size of the results that is returned per call.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListConfiguredModelAlgorithmsResponse": {"type": "structure", "required": ["configuredModelAlgorithms"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token value used to access the next page of results.</p>"}, "configuredModelAlgorithms": {"shape": "ConfiguredModelAlgorithmList", "documentation": "<p>The list of configured model algorithms.</p>"}}}, "ListMLInputChannelsRequest": {"type": "structure", "required": ["membershipIdentifier"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token value retrieved from a previous call to access the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of ML input channels to return.</p>", "location": "querystring", "locationName": "maxResults"}, "membershipIdentifier": {"shape": "UUID", "documentation": "<p>The membership ID of the membership that contains the ML input channels that you want to list.</p>", "location": "uri", "locationName": "membershipIdentifier"}}}, "ListMLInputChannelsResponse": {"type": "structure", "required": ["mlInputChannelsList"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token value used to access the next page of results.</p>"}, "mlInputChannelsList": {"shape": "MLInputChannelsList", "documentation": "<p>The list of ML input channels that you wanted.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "TaggableArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource that you are interested in.</p>", "location": "uri", "locationName": "resourceArn"}}}, "ListTagsForResourceResponse": {"type": "structure", "required": ["tags"], "members": {"tags": {"shape": "TagMap", "documentation": "<p>The tags that are associated with the resource.</p>"}}}, "ListTrainedModelInferenceJobsRequest": {"type": "structure", "required": ["membershipIdentifier"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token value retrieved from a previous call to access the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum size of the results that is returned per call.</p>", "location": "querystring", "locationName": "maxResults"}, "membershipIdentifier": {"shape": "UUID", "documentation": "<p>The membership </p>", "location": "uri", "locationName": "membershipIdentifier"}, "trainedModelArn": {"shape": "TrainedModelArn", "documentation": "<p>The Amazon Resource Name (ARN) of a trained model that was used to create the trained model inference jobs that you are interested in.</p>", "location": "querystring", "locationName": "trainedModelArn"}, "trainedModelVersionIdentifier": {"shape": "UUID", "documentation": "<p>The version identifier of the trained model to filter inference jobs by. When specified, only inference jobs that used this specific version of the trained model are returned.</p>", "location": "querystring", "locationName": "trainedModelVersionIdentifier"}}}, "ListTrainedModelInferenceJobsResponse": {"type": "structure", "required": ["trainedModelInferenceJobs"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token value used to access the next page of results.</p>"}, "trainedModelInferenceJobs": {"shape": "TrainedModelInferenceJobList", "documentation": "<p>Returns the requested trained model inference jobs.</p>"}}}, "ListTrainedModelVersionsRequest": {"type": "structure", "required": ["membershipIdentifier", "trainedModelArn"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The pagination token from a previous <code>ListTrainedModelVersions</code> request. Use this token to retrieve the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of trained model versions to return in a single page. The default value is 10, and the maximum value is 100.</p>", "location": "querystring", "locationName": "maxResults"}, "membershipIdentifier": {"shape": "UUID", "documentation": "<p>The membership identifier for the collaboration that contains the trained model.</p>", "location": "uri", "locationName": "membershipIdentifier"}, "trainedModelArn": {"shape": "TrainedModelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the trained model for which to list versions.</p>", "location": "uri", "locationName": "trainedModelArn"}, "status": {"shape": "TrainedModelStatus", "documentation": "<p>Filter the results to only include trained model versions with the specified status. Valid values include <code>CREATE_PENDING</code>, <code>CREATE_IN_PROGRESS</code>, <code>ACTIVE</code>, <code>CREATE_FAILED</code>, and others.</p>", "location": "querystring", "locationName": "status"}}}, "ListTrainedModelVersionsResponse": {"type": "structure", "required": ["trainedModels"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The pagination token to use in a subsequent <code>ListTrainedModelVersions</code> request to retrieve the next page of results. This value is null when there are no more results to return.</p>"}, "trainedModels": {"shape": "TrainedModelList", "documentation": "<p>A list of trained model versions that match the specified criteria. Each entry contains summary information about a trained model version, including its version identifier, status, and creation details.</p>"}}}, "ListTrainedModelsRequest": {"type": "structure", "required": ["membershipIdentifier"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token value retrieved from a previous call to access the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum size of the results that is returned per call.</p>", "location": "querystring", "locationName": "maxResults"}, "membershipIdentifier": {"shape": "UUID", "documentation": "<p>The membership ID of the member that created the trained models you are interested in.</p>", "location": "uri", "locationName": "membershipIdentifier"}}}, "ListTrainedModelsResponse": {"type": "structure", "required": ["trainedModels"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token value used to access the next page of results.</p>"}, "trainedModels": {"shape": "TrainedModelList", "documentation": "<p>The list of trained models.</p>"}}}, "ListTrainingDatasetsRequest": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token value retrieved from a previous call to access the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum size of the results that is returned per call.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListTrainingDatasetsResponse": {"type": "structure", "required": ["trainingDatasets"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token value used to access the next page of results.</p>"}, "trainingDatasets": {"shape": "TrainingDatasetList", "documentation": "<p>The training datasets that match the request.</p>"}}}, "LogsConfigurationPolicy": {"type": "structure", "required": ["allowedAccountIds"], "members": {"allowedAccountIds": {"shape": "AccountIdList", "documentation": "<p>A list of account IDs that are allowed to access the logs.</p>"}, "filterPattern": {"shape": "LogsConfigurationPolicyFilterPatternString", "documentation": "<p>A regular expression pattern that is used to parse the logs and return information that matches the pattern.</p>"}}, "documentation": "<p>Provides the information necessary for a user to access the logs.</p>"}, "LogsConfigurationPolicyFilterPatternString": {"type": "string", "max": 1024, "min": 0}, "LogsConfigurationPolicyList": {"type": "list", "member": {"shape": "LogsConfigurationPolicy"}, "max": 5, "min": 1}, "LogsStatus": {"type": "string", "enum": ["PUBLISH_SUCCEEDED", "PUBLISH_FAILED"]}, "MLInputChannelArn": {"type": "string", "max": 2048, "min": 20, "pattern": "arn:aws[-a-z]*:cleanrooms-ml:[-a-z0-9]+:[0-9]{12}:membership/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/ml-input-channel/[-a-zA-Z0-9_/.]+"}, "MLInputChannelStatus": {"type": "string", "enum": ["CREATE_PENDING", "CREATE_IN_PROGRESS", "CREATE_FAILED", "ACTIVE", "DELETE_PENDING", "DELETE_IN_PROGRESS", "DELETE_FAILED", "INACTIVE"]}, "MLInputChannelSummary": {"type": "structure", "required": ["createTime", "updateTime", "membershipIdentifier", "collaborationIdentifier", "name", "configuredModelAlgorithmAssociations", "mlInputChannelArn", "status"], "members": {"createTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time at which the ML input channel was created.</p>"}, "updateTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The most recent time at which the ML input channel was updated.</p>"}, "membershipIdentifier": {"shape": "UUID", "documentation": "<p>The membership ID of the membership that contains the ML input channel.</p>"}, "collaborationIdentifier": {"shape": "UUID", "documentation": "<p>The collaboration ID of the collaboration that contains the ML input channel.</p>"}, "name": {"shape": "NameString", "documentation": "<p>The name of the ML input channel.</p>"}, "configuredModelAlgorithmAssociations": {"shape": "MLInputChannelSummaryConfiguredModelAlgorithmAssociationsList", "documentation": "<p>The associated configured model algorithms used to create the ML input channel.</p>"}, "protectedQueryIdentifier": {"shape": "UUID", "documentation": "<p>The ID of the protected query that was used to create the ML input channel.</p>"}, "mlInputChannelArn": {"shape": "MLInputChannelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the ML input channel.</p>"}, "status": {"shape": "MLInputChannelStatus", "documentation": "<p>The status of the ML input channel.</p>"}, "description": {"shape": "ResourceDescription", "documentation": "<p>The description of the ML input channel.</p>"}}, "documentation": "<p>Provides summary information about the ML input channel.</p>"}, "MLInputChannelSummaryConfiguredModelAlgorithmAssociationsList": {"type": "list", "member": {"shape": "ConfiguredModelAlgorithmAssociationArn"}, "max": 1, "min": 1}, "MLInputChannelsList": {"type": "list", "member": {"shape": "MLInputChannelSummary"}}, "MLOutputConfiguration": {"type": "structure", "required": ["roleArn"], "members": {"destination": {"shape": "Destination", "documentation": "<p>The Amazon S3 location where exported model artifacts are stored.</p>"}, "roleArn": {"shape": "IamRoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the service access role that is used to store the model artifacts.</p>"}}, "documentation": "<p>Configuration information about how the exported model artifacts are stored.</p>"}, "MaxResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "MetricDefinition": {"type": "structure", "required": ["name", "regex"], "members": {"name": {"shape": "MetricName", "documentation": "<p>The name of the model metric.</p>"}, "regex": {"shape": "MetricRegex", "documentation": "<p>The regular expression statement that defines how the model metric is reported.</p>"}}, "documentation": "<p>Information about the model metric that is reported for a trained model.</p>"}, "MetricDefinitionList": {"type": "list", "member": {"shape": "MetricDefinition"}, "max": 40, "min": 0}, "MetricName": {"type": "string", "max": 255, "min": 1, "pattern": ".+"}, "MetricRegex": {"type": "string", "max": 500, "min": 1, "pattern": ".+"}, "MetricsConfigurationPolicy": {"type": "structure", "required": ["noiseLevel"], "members": {"noiseLevel": {"shape": "NoiseLevelType", "documentation": "<p>The noise level for the generated metrics.</p>"}}, "documentation": "<p>Provides the configuration policy for metrics generation.</p>"}, "MetricsList": {"type": "list", "member": {"shape": "SharedAudienceMetrics"}, "max": 1, "min": 1}, "MetricsStatus": {"type": "string", "enum": ["PUBLISH_SUCCEEDED", "PUBLISH_FAILED"]}, "MinMatchingSeedSize": {"type": "integer", "box": true, "max": 500000, "min": 25}, "ModelInferenceDataSource": {"type": "structure", "required": ["mlInputChannelArn"], "members": {"mlInputChannelArn": {"shape": "MLInputChannelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the ML input channel for this model inference data source.</p>"}}, "documentation": "<p>Defines information about the data source used for model inference.</p>"}, "ModelTrainingDataChannel": {"type": "structure", "required": ["mlInputChannelArn", "channelName"], "members": {"mlInputChannelArn": {"shape": "MLInputChannelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the ML input channel for this model training data channel.</p>"}, "channelName": {"shape": "ModelTrainingDataChannelName", "documentation": "<p>The name of the training data channel.</p>"}, "s3DataDistributionType": {"shape": "S3DataDistributionType", "documentation": "<p>Specifies how the training data stored in Amazon S3 should be distributed to training instances. This parameter controls the data distribution strategy for the training job:</p> <ul> <li> <p> <code>FullyReplicated</code> - The entire dataset is replicated on each training instance. This is suitable for smaller datasets and algorithms that require access to the complete dataset.</p> </li> <li> <p> <code>ShardedByS3Key</code> - The dataset is distributed across training instances based on Amazon S3 key names. This is suitable for larger datasets and distributed training scenarios where each instance processes a subset of the data.</p> </li> </ul>"}}, "documentation": "<p>Information about the model training data channel. A training data channel is a named data source that the training algorithms can consume. </p>"}, "ModelTrainingDataChannelName": {"type": "string", "max": 64, "min": 1, "pattern": "[A-Za-z0-9\\.\\-_]+"}, "ModelTrainingDataChannels": {"type": "list", "member": {"shape": "ModelTrainingDataChannel"}, "max": 20, "min": 1}, "NameString": {"type": "string", "max": 63, "min": 1, "pattern": "(?!\\s*$)[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDBFF-\\uDC00\\uDFFF\\t]*"}, "NextToken": {"type": "string", "max": 10240, "min": 1}, "NoiseLevelType": {"type": "string", "enum": ["HIGH", "MEDIUM", "LOW", "NONE"]}, "ParameterKey": {"type": "string", "max": 100, "min": 1, "pattern": "[0-9a-zA-Z_]+"}, "ParameterMap": {"type": "map", "key": {"shape": "Parameter<PERSON>ey"}, "value": {"shape": "ParameterValue"}}, "ParameterValue": {"type": "string", "max": 250, "min": 0}, "PolicyExistenceCondition": {"type": "string", "enum": ["POLICY_MUST_EXIST", "POLICY_MUST_NOT_EXIST"]}, "PrivacyConfiguration": {"type": "structure", "required": ["policies"], "members": {"policies": {"shape": "PrivacyConfigurationPolicies", "documentation": "<p>The privacy configuration policies for a configured model algorithm association.</p>"}}, "documentation": "<p>Information about the privacy configuration for a configured model algorithm association.</p>"}, "PrivacyConfigurationPolicies": {"type": "structure", "members": {"trainedModels": {"shape": "TrainedModelsConfigurationPolicy", "documentation": "<p>Specifies who will receive the trained models.</p>"}, "trainedModelExports": {"shape": "TrainedModelExportsConfigurationPolicy", "documentation": "<p>Specifies who will receive the trained model export.</p>"}, "trainedModelInferenceJobs": {"shape": "TrainedModelInferenceJobsConfigurationPolicy", "documentation": "<p>Specifies who will receive the trained model inference jobs.</p>"}}, "documentation": "<p>Information about the privacy configuration policies for a configured model algorithm association.</p>"}, "ProtectedQueryInputParameters": {"type": "structure", "required": ["sqlParameters"], "members": {"sqlParameters": {"shape": "ProtectedQuerySQLParameters"}, "computeConfiguration": {"shape": "ComputeConfiguration", "documentation": "<p>Provides configuration information for the workers that will perform the protected query.</p>"}}, "documentation": "<p>Provides information necessary to perform the protected query.</p>"}, "ProtectedQuerySQLParameters": {"type": "structure", "members": {"queryString": {"shape": "ProtectedQuerySQLParametersQueryStringString", "documentation": "<p>The query string to be submitted.</p>"}, "analysisTemplateArn": {"shape": "AnalysisTemplateArn", "documentation": "<p>The Amazon Resource Name (ARN) associated with the analysis template within a collaboration.</p>"}, "parameters": {"shape": "ParameterMap", "documentation": "<p>The protected query SQL parameters.</p>"}}, "documentation": "<p>The parameters for the SQL type Protected Query.</p>", "sensitive": true}, "ProtectedQuerySQLParametersQueryStringString": {"type": "string", "max": 500000, "min": 0}, "PutConfiguredAudienceModelPolicyRequest": {"type": "structure", "required": ["configuredAudienceModelArn", "configuredAudienceModelPolicy"], "members": {"configuredAudienceModelArn": {"shape": "ConfiguredAudienceModelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the configured audience model that the resource policy will govern.</p>", "location": "uri", "locationName": "configuredAudienceModelArn"}, "configuredAudienceModelPolicy": {"shape": "ResourcePolicy", "documentation": "<p>The IAM resource policy.</p>"}, "previousPolicyHash": {"shape": "Hash", "documentation": "<p>A cryptographic hash of the contents of the policy used to prevent unexpected concurrent modification of the policy.</p>"}, "policyExistenceCondition": {"shape": "PolicyExistenceCondition", "documentation": "<p>Use this to prevent unexpected concurrent modification of the policy.</p>"}}}, "PutConfiguredAudienceModelPolicyResponse": {"type": "structure", "required": ["configuredAudienceModelPolicy", "policyHash"], "members": {"configuredAudienceModelPolicy": {"shape": "ResourcePolicy", "documentation": "<p>The IAM resource policy.</p>"}, "policyHash": {"shape": "Hash", "documentation": "<p>A cryptographic hash of the contents of the policy used to prevent unexpected concurrent modification of the policy.</p>"}}}, "PutMLConfigurationRequest": {"type": "structure", "required": ["membershipIdentifier", "defaultOutputLocation"], "members": {"membershipIdentifier": {"shape": "UUID", "documentation": "<p>The membership ID of the member that is being configured.</p>", "location": "uri", "locationName": "membershipIdentifier"}, "defaultOutputLocation": {"shape": "MLOutputConfiguration", "documentation": "<p>The default Amazon S3 location where ML output is stored for the specified member.</p>"}}}, "RelevanceMetric": {"type": "structure", "required": ["audienceSize"], "members": {"audienceSize": {"shape": "AudienceSize"}, "score": {"shape": "RelevanceMetricScoreDouble", "documentation": "<p>The relevance score of the generated audience.</p>"}}, "documentation": "<p>The relevance score of a generated audience.</p>"}, "RelevanceMetricScoreDouble": {"type": "double", "box": true, "max": 10.0, "min": 0.0}, "RelevanceMetrics": {"type": "list", "member": {"shape": "RelevanceMetric"}}, "ResourceConfig": {"type": "structure", "required": ["instanceType", "volumeSizeInGB"], "members": {"instanceCount": {"shape": "ResourceConfigInstanceCountInteger", "documentation": "<p>The number of resources that are used to train the model.</p>"}, "instanceType": {"shape": "InstanceType", "documentation": "<p>The instance type that is used to train the model.</p>"}, "volumeSizeInGB": {"shape": "ResourceConfigVolumeSizeInGBInteger", "documentation": "<p>The maximum size of the instance that is used to train the model.</p>"}}, "documentation": "<p>Information about the EC2 resources that are used to train the model.</p>"}, "ResourceConfigInstanceCountInteger": {"type": "integer", "box": true, "max": 5, "min": 1}, "ResourceConfigVolumeSizeInGBInteger": {"type": "integer", "box": true, "max": 8192, "min": 1}, "ResourceDescription": {"type": "string", "max": 255, "min": 0, "pattern": "[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDBFF-\\uDC00\\uDFFF\\t\\r\\n]*"}, "ResourceNotFoundException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The resource you are requesting does not exist.</p>", "error": {"httpStatusCode": 404, "senderFault": true}, "exception": true}, "ResourcePolicy": {"type": "string", "max": 20480, "min": 1}, "S3ConfigMap": {"type": "structure", "required": ["s3Uri"], "members": {"s3Uri": {"shape": "S3Path", "documentation": "<p>The Amazon S3 location URI.</p>"}}, "documentation": "<p>Provides information about an Amazon S3 bucket and path.</p>"}, "S3DataDistributionType": {"type": "string", "enum": ["FullyReplicated", "ShardedByS3Key"]}, "S3Path": {"type": "string", "max": 1285, "min": 1, "pattern": "s3://.+"}, "ServiceQuotaExceededException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}, "quotaName": {"shape": "String", "documentation": "<p>The name of the service quota limit that was exceeded</p>"}, "quotaValue": {"shape": "ServiceQuotaExceededExceptionQuotaValueDouble", "documentation": "<p>The current limit on the service quota that was exceeded</p>"}}, "documentation": "<p>You have exceeded your service quota.</p>", "error": {"httpStatusCode": 402, "senderFault": true}, "exception": true}, "ServiceQuotaExceededExceptionQuotaValueDouble": {"type": "double", "box": true, "max": 100000, "min": 0}, "SharedAudienceMetrics": {"type": "string", "enum": ["ALL", "NONE"]}, "StartAudienceExportJobRequest": {"type": "structure", "required": ["name", "audienceGenerationJobArn", "audienceSize"], "members": {"name": {"shape": "NameString", "documentation": "<p>The name of the audience export job.</p>"}, "audienceGenerationJobArn": {"shape": "AudienceGenerationJobArn", "documentation": "<p>The Amazon Resource Name (ARN) of the audience generation job that you want to export.</p>"}, "audienceSize": {"shape": "AudienceSize"}, "description": {"shape": "ResourceDescription", "documentation": "<p>The description of the audience export job.</p>"}}}, "StartAudienceGenerationJobRequest": {"type": "structure", "required": ["name", "configuredAudienceModelArn", "seedAudience"], "members": {"name": {"shape": "NameString", "documentation": "<p>The name of the audience generation job.</p>"}, "configuredAudienceModelArn": {"shape": "ConfiguredAudienceModelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the configured audience model that is used for this audience generation job.</p>"}, "seedAudience": {"shape": "AudienceGenerationJobDataSource", "documentation": "<p>The seed audience that is used to generate the audience.</p>"}, "includeSeedInOutput": {"shape": "Boolean", "documentation": "<p>Whether the seed audience is included in the audience generation output.</p>"}, "collaborationId": {"shape": "UUID", "documentation": "<p>The identifier of the collaboration that contains the audience generation job.</p>"}, "description": {"shape": "ResourceDescription", "documentation": "<p>The description of the audience generation job.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The optional metadata that you apply to the resource to help you categorize and organize them. Each tag consists of a key and an optional value, both of which you define.</p> <p>The following basic restrictions apply to tags:</p> <ul> <li> <p>Maximum number of tags per resource - 50.</p> </li> <li> <p>For each resource, each tag key must be unique, and each tag key can have only one value.</p> </li> <li> <p>Maximum key length - 128 Unicode characters in UTF-8.</p> </li> <li> <p>Maximum value length - 256 Unicode characters in UTF-8.</p> </li> <li> <p>If your tagging schema is used across multiple services and resources, remember that other services may have restrictions on allowed characters. Generally allowed characters are: letters, numbers, and spaces representable in UTF-8, and the following characters: + - = . _ : / @.</p> </li> <li> <p>Tag keys and values are case sensitive.</p> </li> <li> <p>Do not use aws:, AWS:, or any upper or lowercase combination of such as a prefix for keys as it is reserved for AWS use. You cannot edit or delete tag keys with this prefix. Values can have this prefix. If a tag value has aws as its prefix but the key does not, then Clean Rooms ML considers it to be a user tag and will count against the limit of 50 tags. Tags with only the key prefix of aws do not count against your tags per resource limit.</p> </li> </ul>"}}}, "StartAudienceGenerationJobResponse": {"type": "structure", "required": ["audienceGenerationJobArn"], "members": {"audienceGenerationJobArn": {"shape": "AudienceGenerationJobArn", "documentation": "<p>The Amazon Resource Name (ARN) of the audience generation job.</p>"}}}, "StartTrainedModelExportJobRequest": {"type": "structure", "required": ["name", "trainedModelArn", "membershipIdentifier", "outputConfiguration"], "members": {"name": {"shape": "NameString", "documentation": "<p>The name of the trained model export job.</p>"}, "trainedModelArn": {"shape": "TrainedModelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the trained model that you want to export.</p>", "location": "uri", "locationName": "trainedModelArn"}, "trainedModelVersionIdentifier": {"shape": "UUID", "documentation": "<p>The version identifier of the trained model to export. This specifies which version of the trained model should be exported to the specified destination.</p>"}, "membershipIdentifier": {"shape": "UUID", "documentation": "<p>The membership ID of the member that is receiving the exported trained model artifacts.</p>", "location": "uri", "locationName": "membershipIdentifier"}, "outputConfiguration": {"shape": "TrainedModelExportOutputConfiguration", "documentation": "<p>The output configuration information for the trained model export job.</p>"}, "description": {"shape": "ResourceDescription", "documentation": "<p>The description of the trained model export job.</p>"}}}, "StartTrainedModelInferenceJobRequest": {"type": "structure", "required": ["membershipIdentifier", "name", "trainedModelArn", "resourceConfig", "outputConfiguration", "dataSource"], "members": {"membershipIdentifier": {"shape": "UUID", "documentation": "<p>The membership ID of the membership that contains the trained model inference job.</p>", "location": "uri", "locationName": "membershipIdentifier"}, "name": {"shape": "NameString", "documentation": "<p>The name of the trained model inference job.</p>"}, "trainedModelArn": {"shape": "TrainedModelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the trained model that is used for this trained model inference job.</p>"}, "trainedModelVersionIdentifier": {"shape": "UUID", "documentation": "<p>The version identifier of the trained model to use for inference. This specifies which version of the trained model should be used to generate predictions on the input data.</p>"}, "configuredModelAlgorithmAssociationArn": {"shape": "ConfiguredModelAlgorithmAssociationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the configured model algorithm association that is used for this trained model inference job.</p>"}, "resourceConfig": {"shape": "InferenceResourceConfig", "documentation": "<p>Defines the resource configuration for the trained model inference job.</p>"}, "outputConfiguration": {"shape": "InferenceOutputConfiguration", "documentation": "<p>Defines the output configuration information for the trained model inference job.</p>"}, "dataSource": {"shape": "ModelInferenceDataSource", "documentation": "<p>Defines the data source that is used for the trained model inference job.</p>"}, "description": {"shape": "ResourceDescription", "documentation": "<p>The description of the trained model inference job.</p>"}, "containerExecutionParameters": {"shape": "InferenceContainerExecutionParameters", "documentation": "<p>The execution parameters for the container.</p>"}, "environment": {"shape": "InferenceEnvironmentMap", "documentation": "<p>The environment variables to set in the Docker container.</p>"}, "kmsKeyArn": {"shape": "KmsKeyArn", "documentation": "<p>The Amazon Resource Name (ARN) of the KMS key. This key is used to encrypt and decrypt customer-owned data in the ML inference job and associated data.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The optional metadata that you apply to the resource to help you categorize and organize them. Each tag consists of a key and an optional value, both of which you define.</p> <p>The following basic restrictions apply to tags:</p> <ul> <li> <p>Maximum number of tags per resource - 50.</p> </li> <li> <p>For each resource, each tag key must be unique, and each tag key can have only one value.</p> </li> <li> <p>Maximum key length - 128 Unicode characters in UTF-8.</p> </li> <li> <p>Maximum value length - 256 Unicode characters in UTF-8.</p> </li> <li> <p>If your tagging schema is used across multiple services and resources, remember that other services may have restrictions on allowed characters. Generally allowed characters are: letters, numbers, and spaces representable in UTF-8, and the following characters: + - = . _ : / @.</p> </li> <li> <p>Tag keys and values are case sensitive.</p> </li> <li> <p>Do not use aws:, AWS:, or any upper or lowercase combination of such as a prefix for keys as it is reserved for AWS use. You cannot edit or delete tag keys with this prefix. Values can have this prefix. If a tag value has aws as its prefix but the key does not, then Clean Rooms ML considers it to be a user tag and will count against the limit of 50 tags. Tags with only the key prefix of aws do not count against your tags per resource limit.</p> </li> </ul>"}}}, "StartTrainedModelInferenceJobResponse": {"type": "structure", "required": ["trainedModelInferenceJobArn"], "members": {"trainedModelInferenceJobArn": {"shape": "TrainedModelInferenceJobArn", "documentation": "<p>The Amazon Resource Name (ARN) of the trained model inference job.</p>"}}}, "StatusDetails": {"type": "structure", "members": {"statusCode": {"shape": "String", "documentation": "<p>The status code that was returned. The status code is intended for programmatic error handling. Clean Rooms ML will not change the status code for existing error conditions.</p>"}, "message": {"shape": "String", "documentation": "<p>The error message that was returned. The message is intended for human consumption and can change at any time. Use the <code>statusCode</code> for programmatic error handling.</p>"}}, "documentation": "<p>Details about the status of a resource.</p>"}, "StoppingCondition": {"type": "structure", "members": {"maxRuntimeInSeconds": {"shape": "StoppingConditionMaxRuntimeInSecondsInteger", "documentation": "<p>The maximum amount of time, in seconds, that model training can run before it is terminated.</p>"}}, "documentation": "<p>The criteria used to stop model training.</p>"}, "StoppingConditionMaxRuntimeInSecondsInteger": {"type": "integer", "box": true, "max": 2419200, "min": 1}, "String": {"type": "string"}, "SyntheticTimestamp_date_time": {"type": "timestamp", "timestampFormat": "iso8601"}, "TagKey": {"type": "string", "max": 128, "min": 1}, "TagKeys": {"type": "list", "member": {"shape": "TagKey"}, "max": 200, "min": 0}, "TagMap": {"type": "map", "key": {"shape": "TagKey"}, "value": {"shape": "TagValue"}, "max": 200, "min": 0}, "TagOnCreatePolicy": {"type": "string", "enum": ["FROM_PARENT_RESOURCE", "NONE"]}, "TagResourceRequest": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {"shape": "TaggableArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource that you want to assign tags.</p>", "location": "uri", "locationName": "resourceArn"}, "tags": {"shape": "TagMap", "documentation": "<p>The optional metadata that you apply to the resource to help you categorize and organize them. Each tag consists of a key and an optional value, both of which you define.</p> <p>The following basic restrictions apply to tags:</p> <ul> <li> <p>Maximum number of tags per resource - 50.</p> </li> <li> <p>For each resource, each tag key must be unique, and each tag key can have only one value.</p> </li> <li> <p>Maximum key length - 128 Unicode characters in UTF-8.</p> </li> <li> <p>Maximum value length - 256 Unicode characters in UTF-8.</p> </li> <li> <p>If your tagging schema is used across multiple services and resources, remember that other services may have restrictions on allowed characters. Generally allowed characters are: letters, numbers, and spaces representable in UTF-8, and the following characters: + - = . _ : / @.</p> </li> <li> <p>Tag keys and values are case sensitive.</p> </li> <li> <p>Do not use aws:, AWS:, or any upper or lowercase combination of such as a prefix for keys as it is reserved for AWS use. You cannot edit or delete tag keys with this prefix. Values can have this prefix. If a tag value has aws as its prefix but the key does not, then Clean Rooms considers it to be a user tag and will count against the limit of 50 tags. Tags with only the key prefix of aws do not count against your tags per resource limit.</p> </li> </ul>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0}, "TaggableArn": {"type": "string", "max": 2048, "min": 20, "pattern": "arn:aws[-a-z]*:cleanrooms-ml:[-a-z0-9]+:[0-9]{12}:((membership/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/(configured-model-algorithm-association|trained-model|trained-model-inference-job|ml-input-channel))|training-dataset|audience-model|configured-audience-model|audience-generation-job|configured-model-algorithm)/[-a-zA-Z0-9_/.]+"}, "ThrottlingException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The request was denied due to request throttling.</p>", "error": {"httpStatusCode": 429, "senderFault": true}, "exception": true}, "TrainedModelArn": {"type": "string", "max": 2048, "min": 20, "pattern": "arn:aws[-a-z]*:cleanrooms-ml:[-a-z0-9]+:[0-9]{12}:membership/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/trained-model/[-a-zA-Z0-9_/.]+"}, "TrainedModelArtifactMaxSize": {"type": "structure", "required": ["unit", "value"], "members": {"unit": {"shape": "TrainedModelArtifactMaxSizeUnitType", "documentation": "<p>The unit of measurement for the maximum artifact size. Valid values include common storage units such as bytes, kilobytes, megabytes, gigabytes, and terabytes.</p>"}, "value": {"shape": "TrainedModelArtifactMaxSizeValue", "documentation": "<p>The numerical value for the maximum artifact size limit. This value is interpreted according to the specified unit.</p>"}}, "documentation": "<p>Specifies the maximum size limit for trained model artifacts. This configuration helps control storage costs and ensures that trained models don't exceed specified size constraints. The size limit applies to the total size of all artifacts produced by the training job.</p>"}, "TrainedModelArtifactMaxSizeUnitType": {"type": "string", "enum": ["GB"]}, "TrainedModelArtifactMaxSizeValue": {"type": "double", "box": true, "max": 10.0, "min": 0.01}, "TrainedModelExportFileType": {"type": "string", "enum": ["MODEL", "OUTPUT"]}, "TrainedModelExportFileTypeList": {"type": "list", "member": {"shape": "TrainedModelExportFileType"}, "max": 2, "min": 1}, "TrainedModelExportJobStatus": {"type": "string", "enum": ["CREATE_PENDING", "CREATE_IN_PROGRESS", "CREATE_FAILED", "ACTIVE"]}, "TrainedModelExportOutputConfiguration": {"type": "structure", "required": ["members"], "members": {"members": {"shape": "TrainedModelExportReceiverMembers", "documentation": "<p>The members that will received the exported trained model output.</p>"}}, "documentation": "<p>Information about the output of the trained model export job.</p>"}, "TrainedModelExportReceiverMember": {"type": "structure", "required": ["accountId"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The account ID of the member who will receive trained model exports.</p>"}}, "documentation": "<p>Provides information about the member who will receive trained model exports.</p>"}, "TrainedModelExportReceiverMembers": {"type": "list", "member": {"shape": "TrainedModelExportReceiverMember"}, "max": 1, "min": 1}, "TrainedModelExportsConfigurationPolicy": {"type": "structure", "required": ["maxSize", "filesToExport"], "members": {"maxSize": {"shape": "TrainedModelExportsMaxSize", "documentation": "<p>The maximum size of the data that can be exported.</p>"}, "filesToExport": {"shape": "TrainedModelExportFileTypeList", "documentation": "<p>The files that are exported during the trained model export job.</p>"}}, "documentation": "<p>Information about how the trained model exports are configured.</p>"}, "TrainedModelExportsMaxSize": {"type": "structure", "required": ["unit", "value"], "members": {"unit": {"shape": "TrainedModelExportsMaxSizeUnitType", "documentation": "<p>The unit of measurement for the data size.</p>"}, "value": {"shape": "TrainedModelExportsMaxSizeValue", "documentation": "<p>The maximum size of the dataset to export.</p>"}}, "documentation": "<p>The maximum size of the trained model metrics that can be exported. If the trained model metrics dataset is larger than this value, it will not be exported.</p>"}, "TrainedModelExportsMaxSizeUnitType": {"type": "string", "enum": ["GB"]}, "TrainedModelExportsMaxSizeValue": {"type": "double", "box": true, "max": 10.0, "min": 0.01}, "TrainedModelInferenceJobArn": {"type": "string", "max": 2048, "min": 20, "pattern": "arn:aws[-a-z]*:cleanrooms-ml:[-a-z0-9]+:[0-9]{12}:membership/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/trained-model-inference-job/[-a-zA-Z0-9_/.]+"}, "TrainedModelInferenceJobList": {"type": "list", "member": {"shape": "TrainedModelInferenceJobSummary"}}, "TrainedModelInferenceJobStatus": {"type": "string", "enum": ["CREATE_PENDING", "CREATE_IN_PROGRESS", "CREATE_FAILED", "ACTIVE", "CANCEL_PENDING", "CANCEL_IN_PROGRESS", "CANCEL_FAILED", "INACTIVE"]}, "TrainedModelInferenceJobSummary": {"type": "structure", "required": ["trainedModelInferenceJobArn", "membershipIdentifier", "trainedModelArn", "collaborationIdentifier", "status", "outputConfiguration", "name", "createTime", "updateTime"], "members": {"trainedModelInferenceJobArn": {"shape": "TrainedModelInferenceJobArn", "documentation": "<p>The Amazon Resource Name (ARN) of the trained model inference job.</p>"}, "configuredModelAlgorithmAssociationArn": {"shape": "ConfiguredModelAlgorithmAssociationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the configured model algorithm association that is used for the trained model inference job.</p>"}, "membershipIdentifier": {"shape": "UUID", "documentation": "<p>The membership ID of the membership that contains the trained model inference job.</p>"}, "trainedModelArn": {"shape": "TrainedModelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the trained model that is used for the trained model inference job.</p>"}, "trainedModelVersionIdentifier": {"shape": "UUID", "documentation": "<p>The version identifier of the trained model that was used for inference in this job.</p>"}, "collaborationIdentifier": {"shape": "UUID", "documentation": "<p>The collaboration ID of the collaboration that contains the trained model inference job.</p>"}, "status": {"shape": "TrainedModelInferenceJobStatus", "documentation": "<p>The status of the trained model inference job.</p>"}, "outputConfiguration": {"shape": "InferenceOutputConfiguration", "documentation": "<p>The output configuration information of the trained model job.</p>"}, "name": {"shape": "NameString", "documentation": "<p>The name of the trained model inference job.</p>"}, "description": {"shape": "ResourceDescription", "documentation": "<p>The description of the trained model inference job.</p>"}, "metricsStatus": {"shape": "MetricsStatus", "documentation": "<p>The metric status of the trained model inference job.</p>"}, "metricsStatusDetails": {"shape": "String", "documentation": "<p>Details about the metrics status for the trained model inference job.</p>"}, "logsStatus": {"shape": "LogsStatus", "documentation": "<p>The log status of the trained model inference job.</p>"}, "logsStatusDetails": {"shape": "String", "documentation": "<p>Details about the log status for the trained model inference job.</p>"}, "createTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time at which the trained model inference job was created.</p>"}, "updateTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The most recent time at which the trained model inference job was updated.</p>"}}, "documentation": "<p>Provides information about the trained model inference job.</p>"}, "TrainedModelInferenceJobsConfigurationPolicy": {"type": "structure", "members": {"containerLogs": {"shape": "LogsConfigurationPolicyList", "documentation": "<p>The logs container for the trained model inference job.</p>"}, "maxOutputSize": {"shape": "TrainedModelInferenceMaxOutputSize", "documentation": "<p>The maximum allowed size of the output of the trained model inference job.</p>"}}, "documentation": "<p>Provides configuration information for the trained model inference job.</p>"}, "TrainedModelInferenceMaxOutputSize": {"type": "structure", "required": ["unit", "value"], "members": {"unit": {"shape": "TrainedModelInferenceMaxOutputSizeUnitType", "documentation": "<p>The measurement unit to use.</p>"}, "value": {"shape": "TrainedModelInferenceMaxOutputSizeValue", "documentation": "<p>The maximum output size value.</p>"}}, "documentation": "<p>Information about the maximum output size for a trained model inference job.</p>"}, "TrainedModelInferenceMaxOutputSizeUnitType": {"type": "string", "enum": ["GB"]}, "TrainedModelInferenceMaxOutputSizeValue": {"type": "double", "box": true, "max": 50.0, "min": 0.01}, "TrainedModelList": {"type": "list", "member": {"shape": "TrainedModelSummary"}}, "TrainedModelStatus": {"type": "string", "enum": ["CREATE_PENDING", "CREATE_IN_PROGRESS", "CREATE_FAILED", "ACTIVE", "DELETE_PENDING", "DELETE_IN_PROGRESS", "DELETE_FAILED", "INACTIVE", "CANCEL_PENDING", "CANCEL_IN_PROGRESS", "CANCEL_FAILED"]}, "TrainedModelSummary": {"type": "structure", "required": ["createTime", "updateTime", "trainedModelArn", "name", "membershipIdentifier", "collaborationIdentifier", "status", "configuredModelAlgorithmAssociationArn"], "members": {"createTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time at which the trained model was created.</p>"}, "updateTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The most recent time at which the trained model was updated.</p>"}, "trainedModelArn": {"shape": "TrainedModelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the trained model.</p>"}, "versionIdentifier": {"shape": "UUID", "documentation": "<p>The version identifier of this trained model version.</p>"}, "incrementalTrainingDataChannels": {"shape": "IncrementalTrainingDataChannelsOutput", "documentation": "<p>Information about the incremental training data channels used to create this version of the trained model.</p>"}, "name": {"shape": "NameString", "documentation": "<p>The name of the trained model.</p>"}, "description": {"shape": "ResourceDescription", "documentation": "<p>The description of the trained model.</p>"}, "membershipIdentifier": {"shape": "UUID", "documentation": "<p>The membership ID of the member that created the trained model.</p>"}, "collaborationIdentifier": {"shape": "UUID", "documentation": "<p>The collaboration ID of the collaboration that contains the trained model.</p>"}, "status": {"shape": "TrainedModelStatus", "documentation": "<p>The status of the trained model.</p>"}, "configuredModelAlgorithmAssociationArn": {"shape": "ConfiguredModelAlgorithmAssociationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the configured model algorithm association that was used to create this trained model.</p>"}}, "documentation": "<p>Summary information about the trained model.</p>"}, "TrainedModelsConfigurationPolicy": {"type": "structure", "members": {"containerLogs": {"shape": "LogsConfigurationPolicyList", "documentation": "<p>The container for the logs of the trained model.</p>"}, "containerMetrics": {"shape": "MetricsConfigurationPolicy", "documentation": "<p>The container for the metrics of the trained model.</p>"}, "maxArtifactSize": {"shape": "TrainedModelArtifactMaxSize", "documentation": "<p>The maximum size limit for trained model artifacts as defined in the configuration policy. This setting helps enforce consistent size limits across trained models in the collaboration.</p>"}}, "documentation": "<p>The configuration policy for the trained models.</p>"}, "TrainingDatasetArn": {"type": "string", "max": 2048, "min": 20, "pattern": "arn:aws[-a-z]*:cleanrooms-ml:[-a-z0-9]+:[0-9]{12}:training-dataset/[-a-zA-Z0-9_/.]+"}, "TrainingDatasetList": {"type": "list", "member": {"shape": "TrainingDatasetSummary"}}, "TrainingDatasetStatus": {"type": "string", "enum": ["ACTIVE"]}, "TrainingDatasetSummary": {"type": "structure", "required": ["createTime", "updateTime", "trainingDatasetArn", "name", "status"], "members": {"createTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time at which the training dataset was created.</p>"}, "updateTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The most recent time at which the training dataset was updated.</p>"}, "trainingDatasetArn": {"shape": "TrainingDatasetArn", "documentation": "<p>The Amazon Resource Name (ARN) of the training dataset.</p>"}, "name": {"shape": "NameString", "documentation": "<p>The name of the training dataset.</p>"}, "status": {"shape": "TrainingDatasetStatus", "documentation": "<p>The status of the training dataset.</p>"}, "description": {"shape": "ResourceDescription", "documentation": "<p>The description of the training dataset.</p>"}}, "documentation": "<p>Provides information about the training dataset.</p>"}, "TrainingInputMode": {"type": "string", "enum": ["File", "FastFile", "<PERSON><PERSON>"]}, "UUID": {"type": "string", "max": 36, "min": 36, "pattern": "[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}"}, "UntagResourceRequest": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {"shape": "TaggableArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource that you want to remove tags from.</p>", "location": "uri", "locationName": "resourceArn"}, "tagKeys": {"shape": "TagKeys", "documentation": "<p>The key values of tags that you want to remove.</p>", "location": "querystring", "locationName": "tagKeys"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateConfiguredAudienceModelRequest": {"type": "structure", "required": ["configuredAudienceModelArn"], "members": {"configuredAudienceModelArn": {"shape": "ConfiguredAudienceModelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the configured audience model that you want to update.</p>", "location": "uri", "locationName": "configuredAudienceModelArn"}, "outputConfig": {"shape": "ConfiguredAudienceModelOutputConfig", "documentation": "<p>The new output configuration.</p>"}, "audienceModelArn": {"shape": "AudienceModelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the new audience model that you want to use.</p>"}, "sharedAudienceMetrics": {"shape": "MetricsList", "documentation": "<p>The new value for whether to share audience metrics.</p>"}, "minMatchingSeedSize": {"shape": "MinMatchingSeedSize", "documentation": "<p>The minimum number of users from the seed audience that must match with users in the training data of the audience model.</p>"}, "audienceSizeConfig": {"shape": "AudienceSizeConfig", "documentation": "<p>The new audience size configuration.</p>"}, "description": {"shape": "ResourceDescription", "documentation": "<p>The new description of the configured audience model.</p>"}}}, "UpdateConfiguredAudienceModelResponse": {"type": "structure", "required": ["configuredAudienceModelArn"], "members": {"configuredAudienceModelArn": {"shape": "ConfiguredAudienceModelArn", "documentation": "<p>The Amazon Resource Name (ARN) of the configured audience model that was updated.</p>"}}}, "ValidationException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The request parameters for this request are incorrect.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "WorkerComputeConfiguration": {"type": "structure", "members": {"type": {"shape": "WorkerComputeType", "documentation": "<p>The instance type of the compute workers that are used.</p>"}, "number": {"shape": "WorkerComputeConfigurationNumberInteger", "documentation": "<p>The number of compute workers that are used.</p>"}}, "documentation": "<p>Configuration information about the compute workers that perform the transform job.</p>"}, "WorkerComputeConfigurationNumberInteger": {"type": "integer", "box": true, "max": 400, "min": 2}, "WorkerComputeType": {"type": "string", "enum": ["CR.1X", "CR.4X"]}}, "documentation": "<p>Welcome to the <i>Amazon Web Services Clean Rooms ML API Reference</i>.</p> <p>Amazon Web Services Clean Rooms ML provides a privacy-enhancing method for two parties to identify similar users in their data without the need to share their data with each other. The first party brings the training data to Clean Rooms so that they can create and configure an audience model (lookalike model) and associate it with a collaboration. The second party then brings their seed data to Clean Rooms and generates an audience (lookalike segment) that resembles the training data.</p> <p>To learn more about Amazon Web Services Clean Rooms ML concepts, procedures, and best practices, see the <a href=\"https://docs.aws.amazon.com/clean-rooms/latest/userguide/machine-learning.html\">Clean Rooms User Guide</a>.</p> <p>To learn more about SQL commands, functions, and conditions supported in Clean Rooms, see the <a href=\"https://docs.aws.amazon.com/clean-rooms/latest/sql-reference/sql-reference.html\">Clean Rooms SQL Reference</a>.</p>"}