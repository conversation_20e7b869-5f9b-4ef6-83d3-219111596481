{"metadata": {"apiVersion": "2018-11-14", "endpointPrefix": "kafka", "signingName": "kafka", "serviceFullName": "Managed Streaming for Kafka", "serviceAbbreviation": "Kafka", "serviceId": "Kafka", "protocol": "rest-json", "jsonVersion": "1.1", "uid": "kafka-2018-11-14", "signatureVersion": "v4", "auth": ["aws.auth#sigv4"]}, "operations": {"BatchAssociateScramSecret": {"name": "BatchAssociateScramSecret", "http": {"method": "POST", "requestUri": "/v1/clusters/{clusterArn}/scram-secrets", "responseCode": 200}, "input": {"shape": "BatchAssociateScramSecretRequest"}, "output": {"shape": "BatchAssociateScramSecretResponse", "documentation": "\n            <p>200 response</p>\n         "}, "errors": [{"shape": "BadRequestException", "documentation": "\n            <p>The request isn't valid because the input is incorrect. Correct your input and then submit it again.</p>\n         "}, {"shape": "UnauthorizedException", "documentation": "\n            <p>The request is not authorized. The provided credentials couldn't be validated.</p>\n         "}, {"shape": "InternalServerErrorException", "documentation": "\n            <p>There was an unexpected internal server error. Retrying your request might resolve the issue.</p>\n         "}, {"shape": "ForbiddenException", "documentation": "\n            <p>Access forbidden. Check your credentials and then retry your request.</p>\n         "}, {"shape": "NotFoundException", "documentation": "\n            <p>The resource could not be found due to incorrect input. Correct the input, then retry the request.</p>\n         "}, {"shape": "ServiceUnavailableException", "documentation": "\n            <p>503 response</p>\n         "}, {"shape": "TooManyRequestsException", "documentation": "\n            <p>429 response</p>\n         "}], "documentation": "\n            <p>Associates one or more Scram Secrets with an Amazon MSK cluster.</p>\n         "}, "CreateCluster": {"name": "CreateCluster", "http": {"method": "POST", "requestUri": "/v1/clusters", "responseCode": 200}, "input": {"shape": "CreateClusterRequest"}, "output": {"shape": "CreateClusterResponse"}, "errors": [{"shape": "BadRequestException", "documentation": "\n            <p>The request isn't valid because the input is incorrect. Correct your input and then submit it again.</p>\n         "}, {"shape": "InternalServerErrorException", "documentation": "\n            <p>There was an unexpected internal server error. Retrying your request might resolve the issue.</p>\n         "}, {"shape": "UnauthorizedException", "documentation": "\n            <p>The request is not authorized. The provided credentials couldn't be validated.</p>\n         "}, {"shape": "ForbiddenException", "documentation": "\n            <p>Access forbidden. Check your credentials and then retry your request.</p>\n         "}, {"shape": "ServiceUnavailableException", "documentation": "\n            <p>503 response</p>\n         "}, {"shape": "TooManyRequestsException", "documentation": "\n            <p>429 response</p>\n         "}, {"shape": "ConflictException", "documentation": "\n            <p>This cluster name already exists. Retry your request using another name.</p>\n         "}], "documentation": "\n            <p>Creates a new MSK cluster.</p>\n         "}, "CreateClusterV2": {"name": "CreateClusterV2", "http": {"method": "POST", "requestUri": "/api/v2/clusters", "responseCode": 200}, "input": {"shape": "CreateClusterV2Request"}, "output": {"shape": "CreateClusterV2Response"}, "errors": [{"shape": "BadRequestException", "documentation": "\n            <p>The request isn't valid because the input is incorrect. Correct your input and then submit it again.</p>\n         "}, {"shape": "InternalServerErrorException", "documentation": "\n            <p>There was an unexpected internal server error. Retrying your request might resolve the issue.</p>\n         "}, {"shape": "UnauthorizedException", "documentation": "\n            <p>The request is not authorized. The provided credentials couldn't be validated.</p>\n         "}, {"shape": "ForbiddenException", "documentation": "\n            <p>Access forbidden. Check your credentials and then retry your request.</p>\n         "}, {"shape": "ServiceUnavailableException", "documentation": "\n            <p>503 response</p>\n         "}, {"shape": "TooManyRequestsException", "documentation": "\n            <p>429 response</p>\n         "}, {"shape": "ConflictException", "documentation": "\n            <p>This cluster name already exists. Retry your request using another name.</p>\n         "}], "documentation": "\n            <p>Creates a new MSK cluster.</p>\n         "}, "CreateConfiguration": {"name": "CreateConfiguration", "http": {"method": "POST", "requestUri": "/v1/configurations", "responseCode": 200}, "input": {"shape": "CreateConfigurationRequest"}, "output": {"shape": "CreateConfigurationResponse", "documentation": "\n            <p>200 response</p>\n         "}, "errors": [{"shape": "BadRequestException", "documentation": "\n            <p>The request isn't valid because the input is incorrect. Correct your input and then submit it again.</p>\n         "}, {"shape": "InternalServerErrorException", "documentation": "\n            <p>There was an unexpected internal server error. Retrying your request might resolve the issue.</p>\n         "}, {"shape": "UnauthorizedException", "documentation": "\n            <p>The request is not authorized. The provided credentials couldn't be validated.</p>\n         "}, {"shape": "ForbiddenException", "documentation": "\n            <p>Access forbidden. Check your credentials and then retry your request.</p>\n         "}, {"shape": "ServiceUnavailableException", "documentation": "\n            <p>503 response</p>\n         "}, {"shape": "TooManyRequestsException", "documentation": "\n            <p>429 response</p>\n         "}, {"shape": "ConflictException", "documentation": "\n            <p>This cluster name already exists. Retry your request using another name.</p>\n         "}], "documentation": "\n            <p>Creates a new MSK configuration.</p>\n         "}, "CreateReplicator": {"name": "CreateReplicator", "http": {"method": "POST", "requestUri": "/replication/v1/replicators", "responseCode": 200}, "input": {"shape": "CreateReplicatorRequest"}, "output": {"shape": "CreateReplicatorResponse", "documentation": "<p>HTTP Status Code 200: OK.</p>"}, "errors": [{"shape": "BadRequestException", "documentation": "<p>HTTP Status Code 400: Bad request due to incorrect input. Correct your request and then retry it.</p>"}, {"shape": "UnauthorizedException", "documentation": "<p>HTTP Status Code 401: Unauthorized request. The provided credentials couldn't be validated.</p>"}, {"shape": "InternalServerErrorException", "documentation": "<p>HTTP Status Code 500: Unexpected internal server error. Retrying your request might resolve the issue.</p>"}, {"shape": "ForbiddenException", "documentation": "<p>HTTP Status Code 403: Access forbidden. Correct your credentials and then retry your request.</p>"}, {"shape": "NotFoundException", "documentation": "<p>HTTP Status Code 404: Resource not found due to incorrect input. Correct your request and then retry it.</p>"}, {"shape": "ServiceUnavailableException", "documentation": "<p>HTTP Status Code 503: Service Unavailable. Retrying your request in some time might resolve the issue.</p>"}, {"shape": "TooManyRequestsException", "documentation": "<p>HTTP Status Code 429: Limit exceeded. Resource limit reached.</p>"}, {"shape": "ConflictException", "documentation": "<p>HTTP Status Code 409: Conflict. This replicator name already exists. Retry your request with another name.</p>"}], "documentation": "<p>Creates the replicator.</p>"}, "CreateVpcConnection": {"name": "CreateVpcConnection", "http": {"method": "POST", "requestUri": "/v1/vpc-connection", "responseCode": 200}, "input": {"shape": "CreateVpcConnectionRequest"}, "output": {"shape": "CreateVpcConnectionResponse", "documentation": "\n            <p>200 response</p>\n         "}, "errors": [{"shape": "BadRequestException", "documentation": "\n            <p>The request isn't valid because the input is incorrect. Correct your input and then submit it again.</p>\n         "}, {"shape": "InternalServerErrorException", "documentation": "\n            <p>There was an unexpected internal server error. Retrying your request might resolve the issue.</p>\n         "}, {"shape": "UnauthorizedException", "documentation": "\n            <p>The request is not authorized. The provided credentials couldn't be validated.</p>\n         "}, {"shape": "ForbiddenException", "documentation": "\n            <p>Access forbidden. Check your credentials and then retry your request.</p>\n         "}, {"shape": "ServiceUnavailableException", "documentation": "\n            <p>503 response</p>\n         "}, {"shape": "TooManyRequestsException", "documentation": "\n            <p>429 response</p>\n         "}], "documentation": "\n            <p>Creates a new MSK VPC connection.</p>\n         "}, "DeleteCluster": {"name": "DeleteCluster", "http": {"method": "DELETE", "requestUri": "/v1/clusters/{clusterArn}", "responseCode": 200}, "input": {"shape": "DeleteClusterRequest"}, "output": {"shape": "DeleteClusterResponse", "documentation": "\n            <p>Successful response.</p>\n         "}, "errors": [{"shape": "NotFoundException", "documentation": "\n            <p>The resource could not be found due to incorrect input. Correct the input, then retry the request.</p>\n         "}, {"shape": "BadRequestException", "documentation": "\n            <p>The request isn't valid because the input is incorrect. Correct your input and then submit it again.</p>\n         "}, {"shape": "InternalServerErrorException", "documentation": "\n            <p>There was an unexpected internal server error. Retrying your request might resolve the issue.</p>\n         "}, {"shape": "ForbiddenException", "documentation": "\n            <p>Access forbidden. Check your credentials and then retry your request.</p>\n         "}], "documentation": "\n            <p>Deletes the MSK cluster specified by the Amazon Resource Name (ARN) in the request.</p>\n         "}, "DeleteClusterPolicy": {"name": "DeleteClusterPolicy", "http": {"method": "DELETE", "requestUri": "/v1/clusters/{clusterArn}/policy", "responseCode": 200}, "input": {"shape": "DeleteClusterPolicyRequest"}, "output": {"shape": "DeleteClusterPolicyResponse", "documentation": "\n            <p>Successful response.</p>\n         "}, "errors": [{"shape": "NotFoundException", "documentation": "\n            <p>The resource could not be found due to incorrect input. Correct the input, then retry the request.</p>\n         "}, {"shape": "BadRequestException", "documentation": "\n            <p>The request isn't valid because the input is incorrect. Correct your input and then submit it again.</p>\n         "}, {"shape": "InternalServerErrorException", "documentation": "\n            <p>There was an unexpected internal server error. Retrying your request might resolve the issue.</p>\n         "}, {"shape": "ForbiddenException", "documentation": "\n            <p>Access forbidden. Check your credentials and then retry your request.</p>\n         "}], "documentation": "\n            <p>Deletes the MSK cluster policy specified by the Amazon Resource Name (ARN) in the request.</p>\n         "}, "DeleteConfiguration": {"name": "DeleteConfiguration", "http": {"method": "DELETE", "requestUri": "/v1/configurations/{arn}", "responseCode": 200}, "input": {"shape": "DeleteConfigurationRequest"}, "output": {"shape": "DeleteConfigurationResponse", "documentation": "\n            <p>Successful response.</p>\n         "}, "errors": [{"shape": "NotFoundException", "documentation": "\n            <p>The resource could not be found due to incorrect input. Correct the input, then retry the request.</p>\n         "}, {"shape": "BadRequestException", "documentation": "\n            <p>The request isn't valid because the input is incorrect. Correct your input and then submit it again.</p>\n         "}, {"shape": "InternalServerErrorException", "documentation": "\n            <p>There was an unexpected internal server error. Retrying your request might resolve the issue.</p>\n         "}, {"shape": "ForbiddenException", "documentation": "\n            <p>Access forbidden. Check your credentials and then retry your request.</p>\n         "}], "documentation": "\n            <p>Deletes an MSK Configuration.</p>\n         "}, "DeleteReplicator": {"name": "DeleteReplicator", "http": {"method": "DELETE", "requestUri": "/replication/v1/replicators/{replicatorArn}", "responseCode": 200}, "input": {"shape": "DeleteReplicatorRequest"}, "output": {"shape": "DeleteReplicatorResponse", "documentation": "HTTP Status Code 200: OK."}, "errors": [{"shape": "BadRequestException", "documentation": "<p>HTTP Status Code 400: Bad request due to incorrect input. Correct your request and then retry it.</p>"}, {"shape": "UnauthorizedException", "documentation": "<p>HTTP Status Code 401: Unauthorized request. The provided credentials couldn't be validated.</p>"}, {"shape": "InternalServerErrorException", "documentation": "<p>HTTP Status Code 500: Unexpected internal server error. Retrying your request might resolve the issue.</p>"}, {"shape": "ForbiddenException", "documentation": "<p>HTTP Status Code 403: Access forbidden. Correct your credentials and then retry your request.</p>"}, {"shape": "NotFoundException", "documentation": "<p>HTTP Status Code 404: Resource not found due to incorrect input. Correct your request and then retry it.</p>"}, {"shape": "ServiceUnavailableException", "documentation": "<p>HTTP Status Code 503: Service Unavailable. Retrying your request in some time might resolve the issue.</p>"}, {"shape": "TooManyRequestsException", "documentation": "<p>HTTP Status Code 429: Limit exceeded. Resource limit reached.</p>"}], "documentation": "<p>Deletes a replicator.</p>"}, "DeleteVpcConnection": {"name": "DeleteVpcConnection", "http": {"method": "DELETE", "requestUri": "/v1/vpc-connection/{arn}", "responseCode": 200}, "input": {"shape": "DeleteVpcConnectionRequest"}, "output": {"shape": "DeleteVpcConnectionResponse", "documentation": "\n            <p>Successful response.</p>\n         "}, "errors": [{"shape": "NotFoundException", "documentation": "\n            <p>The resource could not be found due to incorrect input. Correct the input, then retry the request.</p>\n         "}, {"shape": "BadRequestException", "documentation": "\n            <p>The request isn't valid because the input is incorrect. Correct your input and then submit it again.</p>\n         "}, {"shape": "InternalServerErrorException", "documentation": "\n            <p>There was an unexpected internal server error. Retrying your request might resolve the issue.</p>\n         "}, {"shape": "ForbiddenException", "documentation": "\n            <p>Access forbidden. Check your credentials and then retry your request.</p>\n         "}], "documentation": "\n            <p>Deletes a MSK VPC connection.</p>\n         "}, "DescribeCluster": {"name": "DescribeCluster", "http": {"method": "GET", "requestUri": "/v1/clusters/{clusterArn}", "responseCode": 200}, "input": {"shape": "DescribeClusterRequest"}, "output": {"shape": "DescribeClusterResponse", "documentation": "\n            <p>Successful response.</p>\n         "}, "errors": [{"shape": "NotFoundException", "documentation": "\n            <p>The resource could not be found due to incorrect input. Correct the input, then retry the request.</p>\n         "}, {"shape": "BadRequestException", "documentation": "\n            <p>The request isn't valid because the input is incorrect. Correct your input and then submit it again.</p>\n         "}, {"shape": "UnauthorizedException", "documentation": "\n            <p>The request is not authorized. The provided credentials couldn't be validated.</p>\n         "}, {"shape": "InternalServerErrorException", "documentation": "\n            <p>There was an unexpected internal server error. Retrying your request might resolve the issue.</p>\n         "}, {"shape": "ForbiddenException", "documentation": "\n            <p>Access forbidden. Check your credentials and then retry your request.</p>\n         "}], "documentation": "\n            <p>Returns a description of the MSK cluster whose Amazon Resource Name (ARN) is specified in the request.</p>\n         "}, "DescribeClusterV2": {"name": "DescribeClusterV2", "http": {"method": "GET", "requestUri": "/api/v2/clusters/{clusterArn}", "responseCode": 200}, "input": {"shape": "DescribeClusterV2Request"}, "output": {"shape": "DescribeClusterV2Response", "documentation": "\n            <p>Successful response.</p>\n         "}, "errors": [{"shape": "NotFoundException", "documentation": "\n            <p>The resource could not be found due to incorrect input. Correct the input, then retry the request.</p>\n         "}, {"shape": "BadRequestException", "documentation": "\n            <p>The request isn't valid because the input is incorrect. Correct your input and then submit it again.</p>\n         "}, {"shape": "UnauthorizedException", "documentation": "\n            <p>The request is not authorized. The provided credentials couldn't be validated.</p>\n         "}, {"shape": "InternalServerErrorException", "documentation": "\n            <p>There was an unexpected internal server error. Retrying your request might resolve the issue.</p>\n         "}, {"shape": "ForbiddenException", "documentation": "\n            <p>Access forbidden. Check your credentials and then retry your request.</p>\n         "}], "documentation": "\n            <p>Returns a description of the MSK cluster whose Amazon Resource Name (ARN) is specified in the request.</p>\n         "}, "DescribeClusterOperation": {"name": "DescribeClusterOperation", "http": {"method": "GET", "requestUri": "/v1/operations/{clusterOperationArn}", "responseCode": 200}, "input": {"shape": "DescribeClusterOperationRequest"}, "output": {"shape": "DescribeClusterOperationResponse", "documentation": "\n            <p>200 response</p>\n         "}, "errors": [{"shape": "NotFoundException", "documentation": "\n            <p>The resource could not be found due to incorrect input. Correct the input, then retry the request.</p>\n         "}, {"shape": "BadRequestException", "documentation": "\n            <p>The request isn't valid because the input is incorrect. Correct your input and then submit it again.</p>\n         "}, {"shape": "UnauthorizedException", "documentation": "\n            <p>The request is not authorized. The provided credentials couldn't be validated.</p>\n         "}, {"shape": "InternalServerErrorException", "documentation": "\n            <p>There was an unexpected internal server error. Retrying your request might resolve the issue.</p>\n         "}, {"shape": "ForbiddenException", "documentation": "\n            <p>Access forbidden. Check your credentials and then retry your request.</p>\n         "}], "documentation": "\n            <p>Returns a description of the cluster operation specified by the ARN.</p>\n         "}, "DescribeClusterOperationV2": {"name": "DescribeClusterOperationV2", "http": {"method": "GET", "requestUri": "/api/v2/operations/{clusterOperationArn}", "responseCode": 200}, "input": {"shape": "DescribeClusterOperationV2Request"}, "output": {"shape": "DescribeClusterOperationV2Response", "documentation": "\n            <p>HTTP Status Code 200: OK.</p>"}, "errors": [{"shape": "BadRequestException", "documentation": "\n            <p>HTTP Status Code 400: Bad request due to incorrect input. Correct your request and then retry it.</p>"}, {"shape": "UnauthorizedException", "documentation": "\n            <p>HTTP Status Code 401: Unauthorized request. The provided credentials couldn't be validated.</p>"}, {"shape": "InternalServerErrorException", "documentation": "\n            <p>HTTP Status Code 500: Unexpected internal server error. Retrying your request might resolve the issue.</p>"}, {"shape": "ForbiddenException", "documentation": "\n            <p>HTTP Status Code 403: Access forbidden. Correct your credentials and then retry your request.</p>"}, {"shape": "NotFoundException", "documentation": "\n            <p>HTTP Status Code 404: Resource not found due to incorrect input. Correct your request and then retry it.</p>"}, {"shape": "ServiceUnavailableException", "documentation": "\n            <p>HTTP Status Code 503: Service Unavailable. Retrying your request in some time might resolve the issue.</p>"}, {"shape": "TooManyRequestsException", "documentation": "\n            <p>HTTP Status Code 429: Limit exceeded. Resource limit reached.</p>"}], "documentation": "\n            <p>Returns a description of the cluster operation specified by the ARN.</p>\n"}, "DescribeConfiguration": {"name": "DescribeConfiguration", "http": {"method": "GET", "requestUri": "/v1/configurations/{arn}", "responseCode": 200}, "input": {"shape": "DescribeConfigurationRequest"}, "output": {"shape": "DescribeConfigurationResponse", "documentation": "\n            <p>200 response</p>\n         "}, "errors": [{"shape": "BadRequestException", "documentation": "\n            <p>The request isn't valid because the input is incorrect. Correct your input and then submit it again.</p>\n         "}, {"shape": "UnauthorizedException", "documentation": "\n            <p>The request is not authorized. The provided credentials couldn't be validated.</p>\n         "}, {"shape": "InternalServerErrorException", "documentation": "\n            <p>There was an unexpected internal server error. Retrying your request might resolve the issue.</p>\n         "}, {"shape": "ForbiddenException", "documentation": "\n            <p>Access forbidden. Check your credentials and then retry your request.</p>\n         "}, {"shape": "NotFoundException", "documentation": "\n            <p>The resource could not be found due to incorrect input. Correct the input, then retry the request.</p>\n         "}, {"shape": "ServiceUnavailableException", "documentation": "\n            <p>503 response</p>\n         "}], "documentation": "\n            <p>Returns a description of this MSK configuration.</p>\n         "}, "DescribeConfigurationRevision": {"name": "DescribeConfigurationRevision", "http": {"method": "GET", "requestUri": "/v1/configurations/{arn}/revisions/{revision}", "responseCode": 200}, "input": {"shape": "DescribeConfigurationRevisionRequest"}, "output": {"shape": "DescribeConfigurationRevisionResponse", "documentation": "\n            <p>200 response</p>\n         "}, "errors": [{"shape": "BadRequestException", "documentation": "\n            <p>The request isn't valid because the input is incorrect. Correct your input and then submit it again.</p>\n         "}, {"shape": "UnauthorizedException", "documentation": "\n            <p>The request is not authorized. The provided credentials couldn't be validated.</p>\n         "}, {"shape": "InternalServerErrorException", "documentation": "\n            <p>There was an unexpected internal server error. Retrying your request might resolve the issue.</p>\n         "}, {"shape": "ForbiddenException", "documentation": "\n            <p>Access forbidden. Check your credentials and then retry your request.</p>\n         "}, {"shape": "NotFoundException", "documentation": "\n            <p>The resource could not be found due to incorrect input. Correct the input, then retry the request.</p>\n         "}, {"shape": "ServiceUnavailableException", "documentation": "\n            <p>503 response</p>\n         "}], "documentation": "\n            <p>Returns a description of this revision of the configuration.</p>\n         "}, "DescribeReplicator": {"name": "DescribeReplicator", "http": {"method": "GET", "requestUri": "/replication/v1/replicators/{replicatorArn}", "responseCode": 200}, "input": {"shape": "DescribeReplicatorRequest"}, "output": {"shape": "DescribeReplicatorResponse", "documentation": "<p>HTTP Status Code 200: OK."}, "errors": [{"shape": "BadRequestException", "documentation": "<p>HTTP Status Code 400: Bad request due to incorrect input. Correct your request and then retry it.</p>"}, {"shape": "UnauthorizedException", "documentation": "<p>HTTP Status Code 401: Unauthorized request. The provided credentials couldn't be validated.</p>"}, {"shape": "InternalServerErrorException", "documentation": "<p>HTTP Status Code 500: Unexpected internal server error. Retrying your request might resolve the issue.</p>"}, {"shape": "ForbiddenException", "documentation": "<p>HTTP Status Code 403: Access forbidden. Correct your credentials and then retry your request.</p>"}, {"shape": "NotFoundException", "documentation": "<p>HTTP Status Code 404: Resource not found due to incorrect input. Correct your request and then retry it.</p>"}, {"shape": "ServiceUnavailableException", "documentation": "<p>HTTP Status Code 503: Service Unavailable. Retrying your request in some time might resolve the issue.</p>"}, {"shape": "TooManyRequestsException", "documentation": "<p>HTTP Status Code 429: Limit exceeded. Resource limit reached.</p>"}], "documentation": "<p>Describes a replicator.</p>"}, "DescribeVpcConnection": {"name": "DescribeVpcConnection", "http": {"method": "GET", "requestUri": "/v1/vpc-connection/{arn}", "responseCode": 200}, "input": {"shape": "DescribeVpcConnectionRequest"}, "output": {"shape": "DescribeVpcConnectionResponse", "documentation": "\n            <p>200 response</p>\n         "}, "errors": [{"shape": "BadRequestException", "documentation": "\n            <p>The request isn't valid because the input is incorrect. Correct your input and then submit it again.</p>\n         "}, {"shape": "UnauthorizedException", "documentation": "\n            <p>The request is not authorized. The provided credentials couldn't be validated.</p>\n         "}, {"shape": "InternalServerErrorException", "documentation": "\n            <p>There was an unexpected internal server error. Retrying your request might resolve the issue.</p>\n         "}, {"shape": "ForbiddenException", "documentation": "\n            <p>Access forbidden. Check your credentials and then retry your request.</p>\n         "}, {"shape": "NotFoundException", "documentation": "\n            <p>The resource could not be found due to incorrect input. Correct the input, then retry the request.</p>\n         "}, {"shape": "ServiceUnavailableException", "documentation": "\n            <p>503 response</p>\n         "}], "documentation": "\n            <p>Returns a description of this MSK VPC connection.</p>\n         "}, "BatchDisassociateScramSecret": {"name": "BatchDisassociateScramSecret", "http": {"method": "PATCH", "requestUri": "/v1/clusters/{clusterArn}/scram-secrets", "responseCode": 200}, "input": {"shape": "BatchDisassociateScramSecretRequest"}, "output": {"shape": "BatchDisassociateScramSecretResponse", "documentation": "\n            <p>200 response</p>\n         "}, "errors": [{"shape": "BadRequestException", "documentation": "\n            <p>The request isn't valid because the input is incorrect. Correct your input and then submit it again.</p>\n         "}, {"shape": "UnauthorizedException", "documentation": "\n            <p>The request is not authorized. The provided credentials couldn't be validated.</p>\n         "}, {"shape": "InternalServerErrorException", "documentation": "\n            <p>There was an unexpected internal server error. Retrying your request might resolve the issue.</p>\n         "}, {"shape": "ForbiddenException", "documentation": "\n            <p>Access forbidden. Check your credentials and then retry your request.</p>\n         "}, {"shape": "NotFoundException", "documentation": "\n            <p>The resource could not be found due to incorrect input. Correct the input, then retry the request.</p>\n         "}, {"shape": "ServiceUnavailableException", "documentation": "\n            <p>503 response</p>\n         "}, {"shape": "TooManyRequestsException", "documentation": "\n            <p>429 response</p>\n         "}], "documentation": "\n            <p>Disassociates one or more Scram Secrets from an Amazon MSK cluster.</p>\n         "}, "GetBootstrapBrokers": {"name": "GetBootstrapBrokers", "http": {"method": "GET", "requestUri": "/v1/clusters/{clusterArn}/bootstrap-brokers", "responseCode": 200}, "input": {"shape": "GetBootstrapBrokersRequest"}, "output": {"shape": "GetBootstrapBrokersResponse", "documentation": "\n            <p>Successful response.</p>\n         "}, "errors": [{"shape": "BadRequestException", "documentation": "\n            <p>The request isn't valid because the input is incorrect. Correct your input and then submit it again.</p>\n         "}, {"shape": "UnauthorizedException", "documentation": "\n            <p>The request is not authorized. The provided credentials couldn't be validated.</p>\n         "}, {"shape": "InternalServerErrorException", "documentation": "\n            <p>There was an unexpected internal server error. Retrying your request might resolve the issue.</p>\n         "}, {"shape": "ConflictException", "documentation": "\n            <p>This cluster name already exists. Retry your request using another name.</p>\n         "}, {"shape": "ForbiddenException", "documentation": "\n            <p>Access forbidden. Check your credentials and then retry your request.</p>\n         "}], "documentation": "\n            <p>A list of brokers that a client application can use to bootstrap. This list doesn't necessarily include all of the brokers in the cluster. The following Python 3.6 example shows how you can use the Amazon Resource Name (ARN) of a cluster to get its bootstrap brokers. If you don't know the ARN of your cluster, you can use the <code>ListClusters</code> operation to get the ARNs of all the clusters in this account and Region.</p>\n         "}, "GetCompatibleKafkaVersions": {"name": "GetCompatibleKafkaVersions", "http": {"method": "GET", "requestUri": "/v1/compatible-kafka-versions", "responseCode": 200}, "input": {"shape": "GetCompatibleKafkaVersionsRequest"}, "output": {"shape": "GetCompatibleKafkaVersionsResponse", "documentation": "\n            <p>Successful response.</p>\n         "}, "errors": [{"shape": "BadRequestException", "documentation": "n            <p>The request isn't valid because the input is incorrect. Correct your input and then submit it again.</p>n            "}, {"shape": "UnauthorizedException", "documentation": "n            <p>The request is not authorized. The provided credentials couldn't be validated.</p>n            "}, {"shape": "InternalServerErrorException", "documentation": "n            <p>There was an unexpected internal server error. Retrying your request might resolve the issue.</p>n            "}, {"shape": "ForbiddenException", "documentation": "n            <p>Access forbidden. Check your credentials and then retry your request.</p>n            "}, {"shape": "NotFoundException", "documentation": "n            <p>The resource could not be found due to incorrect input. Correct the input, then retry the request.</p>n            "}, {"shape": "ServiceUnavailableException", "documentation": "n            <p>503 response</p>n            "}, {"shape": "TooManyRequestsException", "documentation": "n            <p>429 response</p>n            "}], "documentation": "\n            <p>Gets the Apache Kafka versions to which you can update the MSK cluster.</p>\n         "}, "GetClusterPolicy": {"name": "GetClusterPolicy", "http": {"method": "GET", "requestUri": "/v1/clusters/{clusterArn}/policy", "responseCode": 200}, "input": {"shape": "GetClusterPolicyRequest"}, "output": {"shape": "GetClusterPolicyResponse", "documentation": "\n            <p>Successful response.</p>\n         "}, "errors": [{"shape": "NotFoundException", "documentation": "\n            <p>The resource could not be found due to incorrect input. Correct the input, then retry the request.</p>\n         "}, {"shape": "BadRequestException", "documentation": "\n            <p>The request isn't valid because the input is incorrect. Correct your input and then submit it again.</p>\n         "}, {"shape": "InternalServerErrorException", "documentation": "\n            <p>There was an unexpected internal server error. Retrying your request might resolve the issue.</p>\n         "}, {"shape": "ForbiddenException", "documentation": "\n            <p>Access forbidden. Check your credentials and then retry your request.</p>\n         "}], "documentation": "\n            <p>Get the MSK cluster policy specified by the Amazon Resource Name (ARN) in the request.</p>\n         "}, "ListClusterOperations": {"name": "ListClusterOperations", "http": {"method": "GET", "requestUri": "/v1/clusters/{clusterArn}/operations", "responseCode": 200}, "input": {"shape": "ListClusterOperationsRequest"}, "output": {"shape": "ListClusterOperationsResponse", "documentation": "\n            <p>Successful response.</p>\n         "}, "errors": [{"shape": "BadRequestException", "documentation": "\n            <p>The request isn't valid because the input is incorrect. Correct your input and then submit it again.</p>\n         "}, {"shape": "InternalServerErrorException", "documentation": "\n            <p>There was an unexpected internal server error. Retrying your request might resolve the issue.</p>\n         "}, {"shape": "UnauthorizedException", "documentation": "\n            <p>The request is not authorized. The provided credentials couldn't be validated.</p>\n         "}, {"shape": "ForbiddenException", "documentation": "\n            <p>Access forbidden. Check your credentials and then retry your request.</p>\n         "}], "documentation": "\n            <p>Returns a list of all the operations that have been performed on the specified MSK cluster.</p>\n         "}, "ListClusterOperationsV2": {"name": "ListClusterOperationsV2", "http": {"method": "GET", "requestUri": "/api/v2/clusters/{clusterArn}/operations", "responseCode": 200}, "input": {"shape": "ListClusterOperationsV2Request"}, "output": {"shape": "ListClusterOperationsV2Response", "documentation": "\n            <p>HTTP Status Code 200: OK.</p>"}, "errors": [{"shape": "BadRequestException", "documentation": "\n            <p>HTTP Status Code 400: Bad request due to incorrect input. Correct your request and then retry it.</p>"}, {"shape": "UnauthorizedException", "documentation": "\n            <p>HTTP Status Code 401: Unauthorized request. The provided credentials couldn't be validated.</p>"}, {"shape": "InternalServerErrorException", "documentation": "\n            <p>HTTP Status Code 500: Unexpected internal server error. Retrying your request might resolve the issue.</p>"}, {"shape": "ForbiddenException", "documentation": "\n            <p>HTTP Status Code 403: Access forbidden. Correct your credentials and then retry your request.</p>"}, {"shape": "NotFoundException", "documentation": "\n            <p>HTTP Status Code 404: Resource not found due to incorrect input. Correct your request and then retry it.</p>"}, {"shape": "ServiceUnavailableException", "documentation": "\n            <p>HTTP Status Code 503: Service Unavailable. Retrying your request in some time might resolve the issue.</p>"}, {"shape": "TooManyRequestsException", "documentation": "\n            <p>HTTP Status Code 429: Limit exceeded. Resource limit reached.</p>"}], "documentation": "\n            <p>Returns a list of all the operations that have been performed on the specified MSK cluster.</p>\n         "}, "ListClusters": {"name": "ListClusters", "http": {"method": "GET", "requestUri": "/v1/clusters", "responseCode": 200}, "input": {"shape": "ListClustersRequest"}, "output": {"shape": "ListClustersResponse", "documentation": "\n            <p>Successful response.</p>\n         "}, "errors": [{"shape": "BadRequestException", "documentation": "\n            <p>The request isn't valid because the input is incorrect. Correct your input and then submit it again.</p>\n         "}, {"shape": "InternalServerErrorException", "documentation": "\n            <p>There was an unexpected internal server error. Retrying your request might resolve the issue.</p>\n         "}, {"shape": "UnauthorizedException", "documentation": "\n            <p>The request is not authorized. The provided credentials couldn't be validated.</p>\n         "}, {"shape": "ForbiddenException", "documentation": "\n            <p>Access forbidden. Check your credentials and then retry your request.</p>\n         "}], "documentation": "\n            <p>Returns a list of all the MSK clusters in the current Region.</p>\n         "}, "ListClustersV2": {"name": "ListClustersV2", "http": {"method": "GET", "requestUri": "/api/v2/clusters", "responseCode": 200}, "input": {"shape": "ListClustersV2Request"}, "output": {"shape": "ListClustersV2Response", "documentation": "\n            <p>Successful response.</p>\n         "}, "errors": [{"shape": "BadRequestException", "documentation": "\n            <p>The request isn't valid because the input is incorrect. Correct your input and then submit it again.</p>\n         "}, {"shape": "InternalServerErrorException", "documentation": "\n            <p>There was an unexpected internal server error. Retrying your request might resolve the issue.</p>\n         "}, {"shape": "UnauthorizedException", "documentation": "\n            <p>The request is not authorized. The provided credentials couldn't be validated.</p>\n         "}, {"shape": "ForbiddenException", "documentation": "\n            <p>Access forbidden. Check your credentials and then retry your request.</p>\n         "}], "documentation": "\n            <p>Returns a list of all the MSK clusters in the current Region.</p>\n         "}, "ListConfigurationRevisions": {"name": "ListConfigurationRevisions", "http": {"method": "GET", "requestUri": "/v1/configurations/{arn}/revisions", "responseCode": 200}, "input": {"shape": "ListConfigurationRevisionsRequest"}, "output": {"shape": "ListConfigurationRevisionsResponse", "documentation": "\n            <p>200 response</p>\n         "}, "errors": [{"shape": "BadRequestException", "documentation": "\n            <p>The request isn't valid because the input is incorrect. Correct your input and then submit it again.</p>\n         "}, {"shape": "UnauthorizedException", "documentation": "\n            <p>The request is not authorized. The provided credentials couldn't be validated.</p>\n         "}, {"shape": "InternalServerErrorException", "documentation": "\n            <p>There was an unexpected internal server error. Retrying your request might resolve the issue.</p>\n         "}, {"shape": "ForbiddenException", "documentation": "\n            <p>Access forbidden. Check your credentials and then retry your request.</p>\n         "}, {"shape": "NotFoundException", "documentation": "\n            <p>The resource could not be found due to incorrect input. Correct the input, then retry the request.</p>\n         "}, {"shape": "ServiceUnavailableException", "documentation": "\n            <p>503 response</p>\n         "}], "documentation": "\n            <p>Returns a list of all the MSK configurations in this Region.</p>\n         "}, "ListConfigurations": {"name": "ListConfigurations", "http": {"method": "GET", "requestUri": "/v1/configurations", "responseCode": 200}, "input": {"shape": "ListConfigurationsRequest"}, "output": {"shape": "ListConfigurationsResponse", "documentation": "\n            <p>200 response</p>\n         "}, "errors": [{"shape": "ServiceUnavailableException", "documentation": "\n            <p>503 response</p>\n         "}, {"shape": "BadRequestException", "documentation": "\n            <p>The request isn't valid because the input is incorrect. Correct your input and then submit it again.</p>\n         "}, {"shape": "UnauthorizedException", "documentation": "\n            <p>The request is not authorized. The provided credentials couldn't be validated.</p>\n         "}, {"shape": "InternalServerErrorException", "documentation": "\n            <p>There was an unexpected internal server error. Retrying your request might resolve the issue.</p>\n         "}, {"shape": "ForbiddenException", "documentation": "\n            <p>Access forbidden. Check your credentials and then retry your request.</p>\n         "}], "documentation": "\n            <p>Returns a list of all the MSK configurations in this Region.</p>\n         "}, "ListKafkaVersions": {"name": "ListKafkaVersions", "http": {"method": "GET", "requestUri": "/v1/kafka-versions", "responseCode": 200}, "input": {"shape": "ListKafkaVersionsRequest"}, "output": {"shape": "ListKafkaVersionsResponse"}, "errors": [{"shape": "BadRequestException", "documentation": "\n            <p>The request isn't valid because the input is incorrect. Correct your input and then submit it again.</p>\n         "}, {"shape": "UnauthorizedException", "documentation": "\n            <p>The request is not authorized. The provided credentials couldn't be validated.</p>\n         "}, {"shape": "InternalServerErrorException", "documentation": "\n            <p>There was an unexpected internal server error. Retrying your request might resolve the issue.</p>\n         "}, {"shape": "ForbiddenException", "documentation": "\n            <p>Access forbidden. Check your credentials and then retry your request.</p>\n         "}], "documentation": "\n            <p>Returns a list of Apache Kafka versions.</p>\n         "}, "ListNodes": {"name": "ListNodes", "http": {"method": "GET", "requestUri": "/v1/clusters/{clusterArn}/nodes", "responseCode": 200}, "input": {"shape": "ListNodesRequest"}, "output": {"shape": "ListNodesResponse", "documentation": "\n            <p>Successful response.</p>\n         "}, "errors": [{"shape": "NotFoundException", "documentation": "\n            <p>The resource could not be found due to incorrect input. Correct the input, then retry the request.</p>\n         "}, {"shape": "BadRequestException", "documentation": "\n            <p>The request isn't valid because the input is incorrect. Correct your input and then submit it again.</p>\n         "}, {"shape": "InternalServerErrorException", "documentation": "\n            <p>There was an unexpected internal server error. Retrying your request might resolve the issue.</p>\n         "}, {"shape": "ForbiddenException", "documentation": "\n            <p>Access forbidden. Check your credentials and then retry your request.</p>\n         "}], "documentation": "\n            <p>Returns a list of the broker nodes in the cluster.</p>\n         "}, "ListReplicators": {"name": "ListReplicators", "http": {"method": "GET", "requestUri": "/replication/v1/replicators", "responseCode": 200}, "input": {"shape": "ListReplicatorsRequest"}, "output": {"shape": "ListReplicatorsResponse", "documentation": "HTTP Status Code 200: OK."}, "errors": [{"shape": "BadRequestException", "documentation": "<p>HTTP Status Code 400: Bad request due to incorrect input. Correct your request and then retry it.</p>"}, {"shape": "UnauthorizedException", "documentation": "<p>HTTP Status Code 401: Unauthorized request. The provided credentials couldn't be validated.</p>"}, {"shape": "InternalServerErrorException", "documentation": "<p>HTTP Status Code 500: Unexpected internal server error. Retrying your request might resolve the issue.</p>"}, {"shape": "ForbiddenException", "documentation": "<p>HTTP Status Code 403: Access forbidden. Correct your credentials and then retry your request.</p>"}, {"shape": "NotFoundException", "documentation": "<p>HTTP Status Code 404: Resource not found due to incorrect input. Correct your request and then retry it.</p>"}, {"shape": "ServiceUnavailableException", "documentation": "<p>HTTP Status Code 503: Service Unavailable. Retrying your request in some time might resolve the issue.</p>"}, {"shape": "TooManyRequestsException", "documentation": "<p>HTTP Status Code 429: Limit exceeded. Resource limit reached.</p>"}], "documentation": "<p>Lists the replicators.</p>"}, "ListScramSecrets": {"name": "ListScramSecrets", "http": {"method": "GET", "requestUri": "/v1/clusters/{clusterArn}/scram-secrets", "responseCode": 200}, "input": {"shape": "ListScramSecretsRequest"}, "output": {"shape": "ListScramSecretsResponse", "documentation": "\n            <p>200 response</p>\n         "}, "errors": [{"shape": "BadRequestException", "documentation": "\n            <p>The request isn't valid because the input is incorrect. Correct your input and then submit it again.</p>\n         "}, {"shape": "UnauthorizedException", "documentation": "\n            <p>The request is not authorized. The provided credentials couldn't be validated.</p>\n         "}, {"shape": "InternalServerErrorException", "documentation": "\n            <p>There was an unexpected internal server error. Retrying your request might resolve the issue.</p>\n         "}, {"shape": "ForbiddenException", "documentation": "\n            <p>Access forbidden. Check your credentials and then retry your request.</p>\n         "}, {"shape": "NotFoundException", "documentation": "\n            <p>The resource could not be found due to incorrect input. Correct the input, then retry the request.</p>\n         "}, {"shape": "ServiceUnavailableException", "documentation": "\n            <p>503 response</p>\n         "}, {"shape": "TooManyRequestsException", "documentation": "\n            <p>429 response</p>\n         "}], "documentation": "\n            <p>Returns a list of the Scram Secrets associated with an Amazon MSK cluster.</p>\n         "}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/v1/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse", "documentation": "\n            <p>Success response.</p>\n         "}, "errors": [{"shape": "NotFoundException", "documentation": "\n            <p>The resource could not be found due to incorrect input. Correct the input, then retry the request.</p>\n         "}, {"shape": "BadRequestException", "documentation": "\n            <p>The request isn't valid because the input is incorrect. Correct your input and then submit it again.</p>\n         "}, {"shape": "InternalServerErrorException", "documentation": "\n            <p>There was an unexpected internal server error. Retrying your request might resolve the issue.</p>\n         "}], "documentation": "\n            <p>Returns a list of the tags associated with the specified resource.</p>\n         "}, "ListClientVpcConnections": {"name": "ListClientVpcConnections", "http": {"method": "GET", "requestUri": "/v1/clusters/{clusterArn}/client-vpc-connections", "responseCode": 200}, "input": {"shape": "ListClientVpcConnectionsRequest"}, "output": {"shape": "ListClientVpcConnectionsResponse", "documentation": "\n            <p>200 response</p>\n         "}, "errors": [{"shape": "ServiceUnavailableException", "documentation": "\n            <p>503 response</p>\n         "}, {"shape": "BadRequestException", "documentation": "\n            <p>The request isn't valid because the input is incorrect. Correct your input and then submit it again.</p>\n         "}, {"shape": "UnauthorizedException", "documentation": "\n            <p>The request is not authorized. The provided credentials couldn't be validated.</p>\n         "}, {"shape": "InternalServerErrorException", "documentation": "\n            <p>There was an unexpected internal server error. Retrying your request might resolve the issue.</p>\n         "}, {"shape": "ForbiddenException", "documentation": "\n            <p>Access forbidden. Check your credentials and then retry your request.</p>\n         "}], "documentation": "\n            <p>Returns a list of all the VPC connections in this Region.</p>\n         "}, "ListVpcConnections": {"name": "ListVpcConnections", "http": {"method": "GET", "requestUri": "/v1/vpc-connections", "responseCode": 200}, "input": {"shape": "ListVpcConnectionsRequest"}, "output": {"shape": "ListVpcConnectionsResponse", "documentation": "\n            <p>200 response</p>\n         "}, "errors": [{"shape": "ServiceUnavailableException", "documentation": "\n            <p>503 response</p>\n         "}, {"shape": "BadRequestException", "documentation": "\n            <p>The request isn't valid because the input is incorrect. Correct your input and then submit it again.</p>\n         "}, {"shape": "UnauthorizedException", "documentation": "\n            <p>The request is not authorized. The provided credentials couldn't be validated.</p>\n         "}, {"shape": "InternalServerErrorException", "documentation": "\n            <p>There was an unexpected internal server error. Retrying your request might resolve the issue.</p>\n         "}, {"shape": "ForbiddenException", "documentation": "\n            <p>Access forbidden. Check your credentials and then retry your request.</p>\n         "}], "documentation": "\n            <p>Returns a list of all the VPC connections in this Region.</p>\n         "}, "RejectClientVpcConnection": {"name": "RejectClientVpcConnection", "http": {"method": "PUT", "requestUri": "/v1/clusters/{clusterArn}/client-vpc-connection", "responseCode": 200}, "input": {"shape": "RejectClientVpcConnectionRequest"}, "output": {"shape": "RejectClientVpcConnectionResponse", "documentation": "\n            <p>200 response</p>\n         "}, "errors": [{"shape": "ServiceUnavailableException", "documentation": "\n            <p>503 response</p>\n         "}, {"shape": "BadRequestException", "documentation": "\n            <p>The request isn't valid because the input is incorrect. Correct your input and then submit it again.</p>\n         "}, {"shape": "UnauthorizedException", "documentation": "\n            <p>The request is not authorized. The provided credentials couldn't be validated.</p>\n         "}, {"shape": "InternalServerErrorException", "documentation": "\n            <p>There was an unexpected internal server error. Retrying your request might resolve the issue.</p>\n         "}, {"shape": "ForbiddenException", "documentation": "\n            <p>Access forbidden. Check your credentials and then retry your request.</p>\n         "}], "documentation": "\n            <p>Returns empty response.</p>\n         "}, "PutClusterPolicy": {"name": "PutClusterPolicy", "http": {"method": "PUT", "requestUri": "/v1/clusters/{clusterArn}/policy", "responseCode": 200}, "input": {"shape": "PutClusterPolicyRequest"}, "output": {"shape": "PutClusterPolicyResponse", "documentation": "\n            <p>Successful response.</p>\n         "}, "errors": [{"shape": "BadRequestException", "documentation": "\n            <p>The request isn't valid because the input is incorrect. Correct your input and then submit it again.</p>\n         "}, {"shape": "InternalServerErrorException", "documentation": "\n            <p>There was an unexpected internal server error. Retrying your request might resolve the issue.</p>\n         "}, {"shape": "ForbiddenException", "documentation": "\n            <p>Access forbidden. Check your credentials and then retry your request.</p>\n         "}], "documentation": "\n            <p>Creates or updates the MSK cluster policy specified by the cluster Amazon Resource Name (ARN) in the request.</p>\n         "}, "RebootBroker": {"name": "RebootBroker", "http": {"method": "PUT", "requestUri": "/v1/clusters/{clusterArn}/reboot-broker", "responseCode": 200}, "input": {"shape": "RebootBrokerRequest"}, "output": {"shape": "RebootBrokerResponse", "documentation": "\n            <p>Successful response.</p>\n         "}, "errors": [{"shape": "BadRequestException", "documentation": "\n            <p>The request isn't valid because the input is incorrect. Correct your input and then submit it again.</p>\n         "}, {"shape": "UnauthorizedException", "documentation": "\n            <p>The request is not authorized. The provided credentials couldn't be validated.</p>\n         "}, {"shape": "InternalServerErrorException", "documentation": "\n            <p>There was an unexpected internal server error. Retrying your request might resolve the issue.</p>\n         "}, {"shape": "ForbiddenException", "documentation": "\n            <p>Access forbidden. Check your credentials and then retry your request.</p>\n         "}, {"shape": "NotFoundException", "documentation": "\n            <p>The resource could not be found due to incorrect input. Correct the input, then retry the request.</p>\n         "}, {"shape": "ServiceUnavailableException", "documentation": "\n            <p>503 response</p>\n         "}, {"shape": "TooManyRequestsException", "documentation": "\n            <p>429 response</p>\n            "}], "documentation": "Reboots brokers."}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/v1/tags/{resourceArn}", "responseCode": 204}, "input": {"shape": "TagResourceRequest"}, "errors": [{"shape": "NotFoundException", "documentation": "\n            <p>The resource could not be found due to incorrect input. Correct the input, then retry the request.</p>\n         "}, {"shape": "BadRequestException", "documentation": "\n            <p>The request isn't valid because the input is incorrect. Correct your input and then submit it again.</p>\n         "}, {"shape": "InternalServerErrorException", "documentation": "\n            <p>There was an unexpected internal server error. Retrying your request might resolve the issue.</p>\n         "}], "documentation": "\n            <p>Adds tags to the specified MSK resource.</p>\n         "}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/v1/tags/{resourceArn}", "responseCode": 204}, "input": {"shape": "UntagResourceRequest"}, "errors": [{"shape": "NotFoundException", "documentation": "\n            <p>The resource could not be found due to incorrect input. Correct the input, then retry the request.</p>\n         "}, {"shape": "BadRequestException", "documentation": "\n            <p>The request isn't valid because the input is incorrect. Correct your input and then submit it again.</p>\n         "}, {"shape": "InternalServerErrorException", "documentation": "\n            <p>There was an unexpected internal server error. Retrying your request might resolve the issue.</p>\n         "}], "documentation": "\n            <p>Removes the tags associated with the keys that are provided in the query.</p>\n         "}, "UpdateBrokerCount": {"name": "UpdateBrokerCount", "http": {"method": "PUT", "requestUri": "/v1/clusters/{clusterArn}/nodes/count", "responseCode": 200}, "input": {"shape": "UpdateBrokerCountRequest"}, "output": {"shape": "UpdateBrokerCountResponse", "documentation": "\n            <p>Successful response.</p>\n         "}, "errors": [{"shape": "ServiceUnavailableException", "documentation": "\n            <p>503 response</p>\n         "}, {"shape": "BadRequestException", "documentation": "\n            <p>The request isn't valid because the input is incorrect. Correct your input and then submit it again.</p>\n         "}, {"shape": "UnauthorizedException", "documentation": "\n            <p>The request is not authorized. The provided credentials couldn't be validated.</p>\n         "}, {"shape": "InternalServerErrorException", "documentation": "\n            <p>There was an unexpected internal server error. Retrying your request might resolve the issue.</p>\n         "}, {"shape": "ForbiddenException", "documentation": "\n            <p>Access forbidden. Check your credentials and then retry your request.</p>\n         "}], "documentation": "\n            <p>Updates the number of broker nodes in the cluster.</p>\n         "}, "UpdateBrokerType": {"name": "UpdateBrokerType", "http": {"method": "PUT", "requestUri": "/v1/clusters/{clusterArn}/nodes/type", "responseCode": 200}, "input": {"shape": "UpdateBrokerTypeRequest"}, "output": {"shape": "UpdateBrokerTypeResponse", "documentation": "\n            <p>Successful response.</p>\n         "}, "errors": [{"shape": "BadRequestException", "documentation": "\n            <p>The request isn't valid because the input is incorrect. Correct your input and then submit it again.</p>\n            "}, {"shape": "UnauthorizedException", "documentation": "\n            <p>The request is not authorized. The provided credentials couldn't be validated.</p>\n            "}, {"shape": "InternalServerErrorException", "documentation": "\n            <p>There was an unexpected internal server error. Retrying your request might resolve the issue.</p>\n            "}, {"shape": "ForbiddenException", "documentation": "\n            <p>Access forbidden. Check your credentials and then retry your request.</p>\n            "}, {"shape": "NotFoundException", "documentation": "\n            <p>The resource could not be found due to incorrect input. Correct the input, then retry the request.</p>\n            "}, {"shape": "ServiceUnavailableException", "documentation": "\n            <p>503 response</p>\n            "}, {"shape": "TooManyRequestsException", "documentation": "\n            <p>429 response</p>\n            "}], "documentation": "\n            <p>Updates EC2 instance type.</p>\n         "}, "UpdateBrokerStorage": {"name": "UpdateBrokerStorage", "http": {"method": "PUT", "requestUri": "/v1/clusters/{clusterArn}/nodes/storage", "responseCode": 200}, "input": {"shape": "UpdateBrokerStorageRequest"}, "output": {"shape": "UpdateBrokerStorageResponse", "documentation": "\n            <p>Successful response.</p>\n         "}, "errors": [{"shape": "ServiceUnavailableException", "documentation": "\n            <p>503 response</p>\n         "}, {"shape": "BadRequestException", "documentation": "\n            <p>The request isn't valid because the input is incorrect. Correct your input and then submit it again.</p>\n         "}, {"shape": "UnauthorizedException", "documentation": "\n            <p>The request is not authorized. The provided credentials couldn't be validated.</p>\n         "}, {"shape": "InternalServerErrorException", "documentation": "\n            <p>There was an unexpected internal server error. Retrying your request might resolve the issue.</p>\n         "}, {"shape": "ForbiddenException", "documentation": "\n            <p>Access forbidden. Check your credentials and then retry your request.</p>\n         "}], "documentation": "\n            <p>Updates the EBS storage associated with MSK brokers.</p>\n         "}, "UpdateConfiguration": {"name": "UpdateConfiguration", "http": {"method": "PUT", "requestUri": "/v1/configurations/{arn}", "responseCode": 200}, "input": {"shape": "UpdateConfigurationRequest"}, "output": {"shape": "UpdateConfigurationResponse", "documentation": "\n            <p>200 response</p>\n         "}, "errors": [{"shape": "BadRequestException", "documentation": "\n            <p>The request isn't valid because the input is incorrect. Correct your input and then submit it again.</p>\n         "}, {"shape": "UnauthorizedException", "documentation": "\n            <p>The request is not authorized. The provided credentials couldn't be validated.</p>\n         "}, {"shape": "InternalServerErrorException", "documentation": "\n            <p>There was an unexpected internal server error. Retrying your request might resolve the issue.</p>\n         "}, {"shape": "ForbiddenException", "documentation": "\n            <p>Access forbidden. Check your credentials and then retry your request.</p>\n         "}, {"shape": "NotFoundException", "documentation": "\n            <p>The resource could not be found due to incorrect input. Correct the input, then retry the request.</p>\n         "}, {"shape": "ServiceUnavailableException", "documentation": "\n            <p>503 response</p>\n         "}], "documentation": "\n            <p>Updates an MSK configuration.</p>\n         "}, "UpdateConnectivity": {"name": "UpdateConnectivity", "http": {"method": "PUT", "requestUri": "/v1/clusters/{clusterArn}/connectivity", "responseCode": 200}, "input": {"shape": "UpdateConnectivityRequest"}, "output": {"shape": "UpdateConnectivityResponse", "documentation": "\n            <p>Successful response.</p>\n         "}, "errors": [{"shape": "BadRequestException", "documentation": "\n            <p>The request isn't valid because the input is incorrect. Correct your input and then submit it again.</p>\n         "}, {"shape": "UnauthorizedException", "documentation": "\n            <p>The request is not authorized. The provided credentials couldn't be validated.</p>\n         "}, {"shape": "InternalServerErrorException", "documentation": "\n            <p>There was an unexpected internal server error. Retrying your request might resolve the issue.</p>\n         "}, {"shape": "ForbiddenException", "documentation": "\n            <p>Access forbidden. Check your credentials and then retry your request.</p>\n         "}, {"shape": "NotFoundException", "documentation": "\n            <p>The resource could not be found due to incorrect input. Correct the input, then retry the request.</p>\n         "}, {"shape": "ServiceUnavailableException", "documentation": "\n            <p>503 response</p>\n         "}], "documentation": "\n            <p>Updates the cluster's connectivity configuration.</p>\n         "}, "UpdateClusterConfiguration": {"name": "UpdateClusterConfiguration", "http": {"method": "PUT", "requestUri": "/v1/clusters/{clusterArn}/configuration", "responseCode": 200}, "input": {"shape": "UpdateClusterConfigurationRequest"}, "output": {"shape": "UpdateClusterConfigurationResponse", "documentation": "\n            <p>Successful response.</p>\n         "}, "errors": [{"shape": "BadRequestException", "documentation": "\n            <p>The request isn't valid because the input is incorrect. Correct your input and then submit it again.</p>\n         "}, {"shape": "UnauthorizedException", "documentation": "\n            <p>The request is not authorized. The provided credentials couldn't be validated.</p>\n         "}, {"shape": "InternalServerErrorException", "documentation": "\n            <p>There was an unexpected internal server error. Retrying your request might resolve the issue.</p>\n         "}, {"shape": "ForbiddenException", "documentation": "\n            <p>Access forbidden. Check your credentials and then retry your request.</p>\n         "}, {"shape": "NotFoundException", "documentation": "\n            <p>The resource could not be found due to incorrect input. Correct the input, then retry the request.</p>\n         "}, {"shape": "ServiceUnavailableException", "documentation": "\n            <p>503 response</p>\n         "}], "documentation": "\n            <p>Updates the cluster with the configuration that is specified in the request body.</p>\n         "}, "UpdateClusterKafkaVersion": {"name": "UpdateClusterKafkaVersion", "http": {"method": "PUT", "requestUri": "/v1/clusters/{clusterArn}/version", "responseCode": 200}, "input": {"shape": "UpdateClusterKafkaVersionRequest"}, "output": {"shape": "UpdateClusterKafkaVersionResponse", "documentation": "\n            <p>Successful response.</p>\n            "}, "errors": [{"shape": "BadRequestException", "documentation": "\n            <p>The request isn't valid because the input is incorrect. Correct your input and then submit it again.</p>\n            "}, {"shape": "UnauthorizedException", "documentation": "\n            <p>The request is not authorized. The provided credentials couldn't be validated.</p>\n            "}, {"shape": "InternalServerErrorException", "documentation": "\n            <p>There was an unexpected internal server error. Retrying your request might resolve the issue.</p>\n            "}, {"shape": "ForbiddenException", "documentation": "\n            <p>Access forbidden. Check your credentials and then retry your request.</p>\n            "}, {"shape": "NotFoundException", "documentation": "\n            <p>The resource could not be found due to incorrect input. Correct the input, then retry the request.</p>\n            "}, {"shape": "ServiceUnavailableException", "documentation": "\n            <p>503 response</p>\n            "}, {"shape": "TooManyRequestsException", "documentation": "\n            <p>429 response</p>\n            "}], "documentation": "\n            <p>Updates the Apache Kafka version for the cluster.</p>\n         "}, "UpdateMonitoring": {"name": "UpdateMonitoring", "http": {"method": "PUT", "requestUri": "/v1/clusters/{clusterArn}/monitoring", "responseCode": 200}, "input": {"shape": "UpdateMonitoringRequest"}, "output": {"shape": "UpdateMonitoringResponse", "documentation": "\n            <p>HTTP Status Code 200: OK.</p>\n         "}, "errors": [{"shape": "ServiceUnavailableException", "documentation": "\n            <p>503 response</p>\n         "}, {"shape": "BadRequestException", "documentation": "\n            <p>The request isn't valid because the input is incorrect. Correct your input and then submit it again.</p>\n         "}, {"shape": "UnauthorizedException", "documentation": "\n            <p>The request is not authorized. The provided credentials couldn't be validated.</p>\n         "}, {"shape": "InternalServerErrorException", "documentation": "\n            <p>There was an unexpected internal server error. Retrying your request might resolve the issue.</p>\n         "}, {"shape": "ForbiddenException", "documentation": "\n            <p>Access forbidden. Check your credentials and then retry your request.</p>\n         "}], "documentation": "\n            <p>Updates the monitoring settings for the cluster. You can use this operation to specify which Apache Kafka metrics you want Amazon MSK to send to Amazon CloudWatch. You can also specify settings for open monitoring with Prometheus.</p>\n         "}, "UpdateReplicationInfo": {"name": "UpdateReplicationInfo", "http": {"method": "PUT", "requestUri": "/replication/v1/replicators/{replicatorArn}/replication-info", "responseCode": 200}, "input": {"shape": "UpdateReplicationInfoRequest"}, "output": {"shape": "UpdateReplicationInfoResponse", "documentation": "HTTP Status Code 200: OK."}, "errors": [{"shape": "BadRequestException", "documentation": "<p>HTTP Status Code 400: Bad request due to incorrect input. Correct your request and then retry it.</p>"}, {"shape": "UnauthorizedException", "documentation": "<p>HTTP Status Code 401: Unauthorized request. The provided credentials couldn't be validated.</p>"}, {"shape": "InternalServerErrorException", "documentation": "<p>HTTP Status Code 500: Unexpected internal server error. Retrying your request might resolve the issue.</p>"}, {"shape": "ForbiddenException", "documentation": "<p>HTTP Status Code 403: Access forbidden. Correct your credentials and then retry your request.</p>"}, {"shape": "NotFoundException", "documentation": "<p>HTTP Status Code 404: Resource not found due to incorrect input. Correct your request and then retry it.</p>"}, {"shape": "ServiceUnavailableException", "documentation": "<p>HTTP Status Code 503: Service Unavailable. Retrying your request in some time might resolve the issue.</p>"}, {"shape": "TooManyRequestsException", "documentation": "<p>HTTP Status Code 429: Limit exceeded. Resource limit reached.</p>"}], "documentation": "<p>Updates replication info of a replicator.</p>"}, "UpdateSecurity": {"name": "UpdateSecurity", "http": {"method": "PATCH", "requestUri": "/v1/clusters/{clusterArn}/security", "responseCode": 200}, "input": {"shape": "UpdateSecurityRequest"}, "output": {"shape": "UpdateSecurityResponse"}, "errors": [{"shape": "BadRequestException", "documentation": "\n            <p>The request isn't valid because the input is incorrect. Correct your input and then submit it again.</p>\n         "}, {"shape": "UnauthorizedException", "documentation": "\n            <p>The request is not authorized. The provided credentials couldn't be validated.</p>\n         "}, {"shape": "InternalServerErrorException", "documentation": "\n            <p>There was an unexpected internal server error. Retrying your request might resolve the issue.</p>\n         "}, {"shape": "ForbiddenException", "documentation": "\n            <p>Access forbidden. Check your credentials and then retry your request.</p>\n         "}, {"shape": "NotFoundException", "documentation": "\n            <p>The resource could not be found due to incorrect input. Correct the input, then retry the request.</p>\n         "}, {"shape": "ServiceUnavailableException", "documentation": "\n            <p>The service cannot complete the request.</p>\n         "}, {"shape": "TooManyRequestsException", "documentation": "\n            <p>The request throughput limit was exceeded.</p>\n         "}], "documentation": "\n            <p>Updates the security settings for the cluster. You can use this operation to specify encryption and authentication on existing clusters.</p>\n         "}, "UpdateStorage": {"name": "UpdateStorage", "http": {"method": "PUT", "requestUri": "/v1/clusters/{clusterArn}/storage", "responseCode": 200}, "input": {"shape": "UpdateStorageRequest"}, "output": {"shape": "UpdateStorageResponse", "documentation": "HTTP Status Code 200: OK."}, "errors": [{"shape": "BadRequestException", "documentation": "HTTP Status Code 400: Bad request due to incorrect input. Correct your request and then retry it."}, {"shape": "UnauthorizedException", "documentation": "HTTP Status Code 401: Unauthorized request. The provided credentials couldn't be validated."}, {"shape": "InternalServerErrorException", "documentation": "HTTP Status Code 500: Unexpected internal server error. Retrying your request might resolve the issue."}, {"shape": "ForbiddenException", "documentation": "HTTP Status Code 403: Access forbidden. Correct your credentials and then retry your request."}, {"shape": "NotFoundException", "documentation": "HTTP Status Code 404: Resource not found due to incorrect input. Correct your request and then retry it."}, {"shape": "ServiceUnavailableException", "documentation": "HTTP Status Code 503: Service Unavailable. Retrying your request in some time might resolve the issue."}, {"shape": "TooManyRequestsException", "documentation": "HTTP Status Code 429: Limit exceeded. Resource limit reached."}], "documentation": "Updates cluster broker volume size (or) sets cluster storage mode to TIERED."}}, "shapes": {"AmazonMskCluster": {"type": "structure", "members": {"MskClusterArn": {"shape": "__string", "locationName": "mskClusterArn", "documentation": "<p>The Amazon Resource Name (ARN) of an Amazon MSK cluster.</p>"}}, "documentation": "<p>Details of an Amazon MSK Cluster.</p>", "required": ["MskClusterArn"]}, "BatchAssociateScramSecretRequest": {"type": "structure", "members": {"ClusterArn": {"shape": "__string", "location": "uri", "locationName": "clusterArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the cluster to be updated.</p>\n         "}, "SecretArnList": {"shape": "__listOf__string", "locationName": "secretArnList", "documentation": "\n            <p>List of AWS Secrets Manager secret ARNs.</p>\n         "}}, "documentation": "\n            <p>Associates sasl scram secrets to cluster.</p>\n         ", "required": ["ClusterArn", "SecretArnList"]}, "BatchAssociateScramSecretResponse": {"type": "structure", "members": {"ClusterArn": {"shape": "__string", "locationName": "clusterArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the cluster.</p>\n         "}, "UnprocessedScramSecrets": {"shape": "__listOfUnprocessedScramSecret", "locationName": "unprocessedScramSecrets", "documentation": "\n            <p>List of errors when associating secrets to cluster.</p>\n         "}}}, "BadRequestException": {"type": "structure", "members": {"InvalidParameter": {"shape": "__string", "locationName": "invalidParameter", "documentation": "\n            <p>The parameter that caused the error.</p>\n         "}, "Message": {"shape": "__string", "locationName": "message", "documentation": "\n            <p>The description of the error.</p>\n         "}}, "documentation": "\n            <p>Returns information about an error.</p>\n         ", "exception": true, "error": {"httpStatusCode": 400}}, "BrokerAZDistribution": {"type": "string", "documentation": "\n            <p>The distribution of broker nodes across Availability Zones. This is an optional parameter. If you don't specify it, Amazon MSK gives it the value DEFAULT. You can also explicitly set this parameter to the value DEFAULT. No other values are currently allowed.</p>\n         <p>Amazon MSK distributes the broker nodes evenly across the Availability Zones that correspond to the subnets you provide when you create the cluster.</p>\n         ", "enum": ["DEFAULT"]}, "BrokerCountUpdateInfo": {"type": "structure", "members": {"CreatedBrokerIds": {"shape": "__listOf__double", "locationName": "createdBrokerIds", "documentation": "\n            <p>Kafka Broker IDs of brokers being created.</p>\n         "}, "DeletedBrokerIds": {"shape": "__listOf__double", "locationName": "deletedBrokerIds", "documentation": "\n            <p>Kafka Broker IDs of brokers being deleted.</p>\n         "}}, "documentation": "\n            <p>Information regarding UpdateBrokerCount.</p>\n         "}, "BrokerEBSVolumeInfo": {"type": "structure", "members": {"KafkaBrokerNodeId": {"shape": "__string", "locationName": "kafkaBrokerNodeId", "documentation": "\n            <p>The ID of the broker to update.</p>\n         "}, "ProvisionedThroughput": {"shape": "ProvisionedThroughput", "locationName": "provisionedThroughput", "documentation": "\n            <p>EBS volume provisioned throughput information.</p>\n         "}, "VolumeSizeGB": {"shape": "__integer", "locationName": "volumeSizeGB", "documentation": "\n            <p>Size of the EBS volume to update.</p>\n         "}}, "documentation": "\n            <p>Specifies the EBS volume upgrade information. The broker identifier must be set to the keyword ALL. This means the changes apply to all the brokers in the cluster.</p>\n         ", "required": ["KafkaBrokerNodeId"]}, "BrokerLogs": {"type": "structure", "members": {"CloudWatchLogs": {"shape": "CloudWatchLogs", "locationName": "cloudWatchLogs"}, "Firehose": {"shape": "Firehose", "locationName": "firehose"}, "S3": {"shape": "S3", "locationName": "s3"}}}, "BrokerNodeGroupInfo": {"type": "structure", "members": {"BrokerAZDistribution": {"shape": "BrokerAZDistribution", "locationName": "brokerAZDistribution", "documentation": "\n            <p>The distribution of broker nodes across Availability Zones. This is an optional parameter. If you don't specify it, Amazon MSK gives it the value DEFAULT. You can also explicitly set this parameter to the value DEFAULT. No other values are currently allowed.</p>\n         <p>Amazon MSK distributes the broker nodes evenly across the Availability Zones that correspond to the subnets you provide when you create the cluster.</p>\n         "}, "ClientSubnets": {"shape": "__listOf__string", "locationName": "clientSubnets", "documentation": "\n            <p>The list of subnets to connect to in the client virtual private cloud (VPC). AWS creates elastic network interfaces inside these subnets. Client applications use elastic network interfaces to produce and consume data. Client subnets can't occupy the Availability Zone with ID use use1-az3.</p>\n         "}, "InstanceType": {"shape": "__stringMin5Max32", "locationName": "instanceType", "documentation": "\n            <p>The type of Amazon EC2 instances to use for Apache Kafka brokers. The following instance types are allowed: kafka.m5.large, kafka.m5.xlarge, kafka.m5.2xlarge,\nkafka.m5.4xlarge, kafka.m5.12xlarge, and kafka.m5.24xlarge.</p>\n         "}, "SecurityGroups": {"shape": "__listOf__string", "locationName": "securityGroups", "documentation": "\n            <p>The AWS security groups to associate with the elastic network interfaces in order to specify who can connect to and communicate with the Amazon MSK cluster. If you don't specify a security group, Amazon MSK uses the default security group associated with the VPC.</p>\n         "}, "StorageInfo": {"shape": "StorageInfo", "locationName": "storageInfo", "documentation": "\n            <p>Contains information about storage volumes attached to MSK broker nodes.</p>\n         "}, "ConnectivityInfo": {"shape": "ConnectivityInfo", "locationName": "connectivityInfo", "documentation": "\n            <p>Information about the broker access configuration.</p>\n         "}, "ZoneIds": {"shape": "__listOf__string", "locationName": "zoneIds", "documentation": "\n            <p>The list of zoneIds for the cluster in the virtual private cloud (VPC).</p>\n         "}}, "documentation": "\n            <p>Describes the setup to be used for Apache Kafka broker nodes in the cluster.</p>\n         ", "required": ["ClientSubnets", "InstanceType"]}, "BrokerNodeInfo": {"type": "structure", "members": {"AttachedENIId": {"shape": "__string", "locationName": "attachedENIId", "documentation": "\n            <p>The attached elastic network interface of the broker.</p>\n         "}, "BrokerId": {"shape": "__double", "locationName": "brokerId", "documentation": "\n            <p>The ID of the broker.</p>\n         "}, "ClientSubnet": {"shape": "__string", "locationName": "clientSubnet", "documentation": "\n            <p>The client subnet to which this broker node belongs.</p>\n         "}, "ClientVpcIpAddress": {"shape": "__string", "locationName": "clientVpcIpAddress", "documentation": "\n            <p>The virtual private cloud (VPC) of the client.</p>\n         "}, "CurrentBrokerSoftwareInfo": {"shape": "BrokerSoftwareInfo", "locationName": "currentBrokerSoftwareInfo", "documentation": "\n            <p>Information about the version of software currently deployed on the Apache Kafka brokers in the cluster.</p>\n         "}, "Endpoints": {"shape": "__listOf__string", "locationName": "endpoints", "documentation": "\n            <p>Endpoints for accessing the broker.</p>\n         "}}, "documentation": "\n            <p>BrokerNodeInfo</p>\n         "}, "BrokerSoftwareInfo": {"type": "structure", "members": {"ConfigurationArn": {"shape": "__string", "locationName": "configurationArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the configuration used for the cluster. This field isn't visible in this preview release.</p>\n         "}, "ConfigurationRevision": {"shape": "__long", "locationName": "configurationRevision", "documentation": "\n            <p>The revision of the configuration to use. This field isn't visible in this preview release.</p>\n         "}, "KafkaVersion": {"shape": "__string", "locationName": "kafkaVersion", "documentation": "\n            <p>The version of Apache Kafka.</p>\n         "}}, "documentation": "\n            <p>Information about the current software installed on the cluster.</p>\n         "}, "ClientAuthentication": {"type": "structure", "members": {"Sasl": {"shape": "Sasl", "locationName": "sasl", "documentation": "\n            <p>Details for ClientAuthentication using SASL.</p>\n         "}, "Tls": {"shape": "Tls", "locationName": "tls", "documentation": "\n            <p>Details for ClientAuthentication using TLS.</p>\n         "}, "Unauthenticated": {"shape": "Unauthenticated", "locationName": "unauthenticated", "documentation": "\n            <p>Contains information about unauthenticated traffic to the cluster.</p>\n         "}}, "documentation": "\n            <p>Includes all client authentication information.</p>\n         "}, "VpcConnectivityClientAuthentication": {"type": "structure", "members": {"Sasl": {"shape": "VpcConnectivitySasl", "locationName": "sasl", "documentation": "\n            <p>SASL authentication type details for VPC connectivity.</p>\n         "}, "Tls": {"shape": "VpcConnectivityTls", "locationName": "tls", "documentation": "\n            <p>TLS authentication type details for VPC connectivity.</p>\n         "}}, "documentation": "\n            <p>Includes all client authentication information for VPC connectivity.</p>\n         "}, "ServerlessClientAuthentication": {"type": "structure", "members": {"Sasl": {"shape": "ServerlessSasl", "locationName": "sasl", "documentation": "\n            <p>Details for ClientAuthentication using SASL.</p>\n         "}}, "documentation": "\n            <p>Includes all client authentication information.</p>\n         "}, "ClientBroker": {"type": "string", "documentation": "\n            <p>Client-broker encryption in transit setting.</p>\n         ", "enum": ["TLS", "TLS_PLAINTEXT", "PLAINTEXT"]}, "CloudWatchLogs": {"type": "structure", "members": {"Enabled": {"shape": "__boolean", "locationName": "enabled"}, "LogGroup": {"shape": "__string", "locationName": "logGroup"}}, "required": ["Enabled"]}, "ClusterInfo": {"type": "structure", "members": {"ActiveOperationArn": {"shape": "__string", "locationName": "activeOperationArn", "documentation": "\n            <p>Arn of active cluster operation.</p>\n         "}, "BrokerNodeGroupInfo": {"shape": "BrokerNodeGroupInfo", "locationName": "brokerNodeGroupInfo", "documentation": "\n            <p>Information about the broker nodes.</p>\n         "}, "ClientAuthentication": {"shape": "ClientAuthentication", "locationName": "clientAuthentication", "documentation": "\n            <p>Includes all client authentication information.</p>\n         "}, "ClusterArn": {"shape": "__string", "locationName": "clusterArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) that uniquely identifies the cluster.</p>\n         "}, "ClusterName": {"shape": "__string", "locationName": "clusterName", "documentation": "\n            <p>The name of the cluster.</p>\n         "}, "CreationTime": {"shape": "__timestampIso8601", "locationName": "creationTime", "documentation": "\n            <p>The time when the cluster was created.</p>\n         "}, "CurrentBrokerSoftwareInfo": {"shape": "BrokerSoftwareInfo", "locationName": "currentBrokerSoftwareInfo", "documentation": "\n            <p>Information about the version of software currently deployed on the Apache Kafka brokers in the cluster.</p>\n         "}, "CurrentVersion": {"shape": "__string", "locationName": "currentVersion", "documentation": "\n            <p>The current version of the MSK cluster.</p>\n         "}, "EncryptionInfo": {"shape": "EncryptionInfo", "locationName": "encryptionInfo", "documentation": "\n            <p>Includes all encryption-related information.</p>\n         "}, "EnhancedMonitoring": {"shape": "EnhancedMonitoring", "locationName": "enhancedMonitoring", "documentation": "\n            <p>Specifies which metrics are gathered for the MSK cluster. This property has the following possible values: DEFAULT, PER_BROKER, PER_TOPIC_PER_BROKER, and PER_TOPIC_PER_PARTITION. For a list of the metrics associated with each of these levels of monitoring, see <a href=\"https://docs.aws.amazon.com/msk/latest/developerguide/monitoring.html\">Monitoring</a>.</p>\n         "}, "OpenMonitoring": {"shape": "OpenMonitoring", "locationName": "openMonitoring", "documentation": "\n            <p>Settings for open monitoring using Prometheus.</p>\n         "}, "LoggingInfo": {"shape": "LoggingInfo", "locationName": "loggingInfo"}, "NumberOfBrokerNodes": {"shape": "__integer", "locationName": "numberOfBrokerNodes", "documentation": "\n            <p>The number of broker nodes in the cluster.</p>\n         "}, "State": {"shape": "ClusterState", "locationName": "state", "documentation": "\n            <p>The state of the cluster. The possible states are ACTIVE, CREATING, DELETING, FAILED, HEALING, MAINTENANCE, REBOOTING_BROKER, and UPDATING.</p>\n         "}, "StateInfo": {"shape": "StateInfo", "locationName": "stateInfo"}, "Tags": {"shape": "__mapOf__string", "locationName": "tags", "documentation": "\n            <p>Tags attached to the cluster.</p>\n         "}, "ZookeeperConnectString": {"shape": "__string", "locationName": "zookeeperConnectString", "documentation": "\n            <p>The connection string to use to connect to the Apache ZooKeeper cluster.</p>\n         "}, "ZookeeperConnectStringTls": {"shape": "__string", "locationName": "zookeeperConnectStringTls", "documentation": "\n            <p>The connection string to use to connect to zookeeper cluster on Tls port.</p>\n         "}, "StorageMode": {"shape": "StorageMode", "locationName": "storageMode", "documentation": "\n            <p>This controls storage mode for supported storage tiers.</p>\n         "}, "CustomerActionStatus": {"shape": "CustomerActionStatus", "locationName": "customerActionStatus", "documentation": "\n            <p>Determines if there is an action required from the customer.</p>\n         "}}, "documentation": "\n            <p>Returns information about a cluster.</p>\n         "}, "Cluster": {"type": "structure", "members": {"ActiveOperationArn": {"shape": "__string", "locationName": "activeOperationArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) that uniquely identifies a cluster operation.</p>\n         "}, "ClusterType": {"shape": "ClusterType", "locationName": "clusterType", "documentation": "\n            <p>Cluster Type.</p>\n         "}, "ClusterArn": {"shape": "__string", "locationName": "clusterArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) that uniquely identifies the cluster.</p>\n         "}, "ClusterName": {"shape": "__string", "locationName": "clusterName", "documentation": "\n            <p>The name of the cluster.</p>\n         "}, "CreationTime": {"shape": "__timestampIso8601", "locationName": "creationTime", "documentation": "\n            <p>The time when the cluster was created.</p>\n         "}, "CurrentVersion": {"shape": "__string", "locationName": "currentVersion", "documentation": "\n            <p>The current version of the MSK cluster.</p>\n         "}, "State": {"shape": "ClusterState", "locationName": "state", "documentation": "\n            <p>The state of the cluster. The possible states are ACTIVE, CREATING, DELETING, FAILED, HEALING, MAINTENANCE, REBOOTING_BROKER, and UPDATING.</p>\n         "}, "StateInfo": {"shape": "StateInfo", "locationName": "stateInfo", "documentation": "\n            <p>State Info for the Amazon MSK cluster.</p>\n         "}, "Tags": {"shape": "__mapOf__string", "locationName": "tags", "documentation": "\n            <p>Tags attached to the cluster.</p>\n         "}, "Provisioned": {"shape": "Provisioned", "locationName": "provisioned", "documentation": "\n            <p>Information about the provisioned cluster.</p>\n         "}, "Serverless": {"shape": "Serverless", "locationName": "serverless", "documentation": "\n            <p>Information about the serverless cluster.</p>\n         "}}, "documentation": "\n            <p>Returns information about a cluster.</p>\n         "}, "ClusterOperationInfo": {"type": "structure", "members": {"ClientRequestId": {"shape": "__string", "locationName": "clientRequestId", "documentation": "\n            <p>The ID of the API request that triggered this operation.</p>\n         "}, "ClusterArn": {"shape": "__string", "locationName": "clusterArn", "documentation": "\n            <p>ARN of the cluster.</p>\n         "}, "CreationTime": {"shape": "__timestampIso8601", "locationName": "creationTime", "documentation": "\n            <p>The time that the operation was created.</p>\n         "}, "EndTime": {"shape": "__timestampIso8601", "locationName": "endTime", "documentation": "\n            <p>The time at which the operation finished.</p>\n         "}, "ErrorInfo": {"shape": "ErrorInfo", "locationName": "errorInfo", "documentation": "\n            <p>Describes the error if the operation fails.</p>\n         "}, "OperationArn": {"shape": "__string", "locationName": "operationArn", "documentation": "\n            <p>ARN of the cluster operation.</p>\n         "}, "OperationState": {"shape": "__string", "locationName": "operationState", "documentation": "\n            <p>State of the cluster operation.</p>\n         "}, "OperationSteps": {"shape": "__listOfClusterOperationStep", "locationName": "operationSteps", "documentation": "\n            <p>Steps completed during the operation.</p>\n         "}, "OperationType": {"shape": "__string", "locationName": "operationType", "documentation": "\n            <p>Type of the cluster operation.</p>\n         "}, "SourceClusterInfo": {"shape": "MutableClusterInfo", "locationName": "sourceClusterInfo", "documentation": "\n            <p>Information about cluster attributes before a cluster is updated.</p>\n         "}, "TargetClusterInfo": {"shape": "MutableClusterInfo", "locationName": "targetClusterInfo", "documentation": "\n            <p>Information about cluster attributes after a cluster is updated.</p>\n         "}, "VpcConnectionInfo": {"shape": "VpcConnectionInfo", "locationName": "vpcConnectionInfo", "documentation": "\n            <p>Description of the VPC connection for CreateVpcConnection and DeleteVpcConnection operations.</p>\n         "}}, "documentation": "\n            <p>Returns information about a cluster operation.</p>\n         "}, "ClusterOperationStep": {"type": "structure", "members": {"StepInfo": {"shape": "ClusterOperationStepInfo", "locationName": "stepInfo", "documentation": "\n            <p>Information about the step and its status.</p>\n         "}, "StepName": {"shape": "__string", "locationName": "<PERSON><PERSON><PERSON>", "documentation": "\n            <p>The name of the step.</p>\n         "}}, "documentation": "\n            <p>Step taken during a cluster operation.</p>\n         "}, "ClusterOperationStepInfo": {"type": "structure", "members": {"StepStatus": {"shape": "__string", "locationName": "step<PERSON>tatus", "documentation": "\n            <p>The steps current status.</p>\n         "}}, "documentation": "\n            <p>State information about the operation step.</p>\n         "}, "ClusterState": {"type": "string", "documentation": "\n            <p>The state of the Apache Kafka cluster.</p>\n         ", "enum": ["ACTIVE", "CREATING", "DELETING", "FAILED", "HEALING", "MAINTENANCE", "REBOOTING_BROKER", "UPDATING"]}, "ClusterType": {"type": "string", "documentation": "\n            <p>The type of cluster.</p>\n         ", "enum": ["PROVISIONED", "SERVERLESS"]}, "ProvisionedRequest": {"type": "structure", "documentation": "\n            <p>Provisioned cluster request.</p>\n         ", "members": {"BrokerNodeGroupInfo": {"shape": "BrokerNodeGroupInfo", "locationName": "brokerNodeGroupInfo", "documentation": "\n            <p>Information about the brokers.</p>\n         "}, "ClientAuthentication": {"shape": "ClientAuthentication", "locationName": "clientAuthentication", "documentation": "\n            <p>Includes all client authentication information.</p>\n         "}, "ConfigurationInfo": {"shape": "ConfigurationInfo", "locationName": "configurationInfo", "documentation": "\n            <p>Represents the configuration that you want Amazon MSK to use for the brokers in a cluster.</p>\n         "}, "EncryptionInfo": {"shape": "EncryptionInfo", "locationName": "encryptionInfo", "documentation": "\n            <p>Includes all encryption-related information.</p>\n         "}, "EnhancedMonitoring": {"shape": "EnhancedMonitoring", "locationName": "enhancedMonitoring", "documentation": "\n            <p>Specifies the level of monitoring for the MSK cluster. The possible values are DEFAULT, PER_BROKER, PER_TOPIC_PER_BROKER, and PER_TOPIC_PER_PARTITION.</p>\n         "}, "OpenMonitoring": {"shape": "OpenMonitoringInfo", "locationName": "openMonitoring", "documentation": "\n            <p>The settings for open monitoring.</p>\n         "}, "KafkaVersion": {"shape": "__stringMin1Max128", "locationName": "kafkaVersion", "documentation": "\n            <p>The Apache Kafka version that you want for the cluster.</p>\n         "}, "LoggingInfo": {"shape": "LoggingInfo", "locationName": "loggingInfo", "documentation": "\n            <p>Log delivery information for the cluster.</p>\n         "}, "NumberOfBrokerNodes": {"shape": "__integerMin1Max15", "locationName": "numberOfBrokerNodes", "documentation": "\n            <p>The number of broker nodes in the cluster.</p>\n         "}, "StorageMode": {"shape": "StorageMode", "locationName": "storageMode", "documentation": "\n            <p>This controls storage mode for supported storage tiers.</p>\n         "}}, "required": ["BrokerNodeGroupInfo", "KafkaVersion", "NumberOfBrokerNodes"]}, "Provisioned": {"type": "structure", "documentation": "\n            <p>Provisioned cluster.</p>\n         ", "members": {"BrokerNodeGroupInfo": {"shape": "BrokerNodeGroupInfo", "locationName": "brokerNodeGroupInfo", "documentation": "\n            <p>Information about the brokers.</p>\n         "}, "CurrentBrokerSoftwareInfo": {"shape": "BrokerSoftwareInfo", "locationName": "currentBrokerSoftwareInfo", "documentation": "\n            <p>Information about the Apache Kafka version deployed on the brokers.</p>\n         "}, "ClientAuthentication": {"shape": "ClientAuthentication", "locationName": "clientAuthentication", "documentation": "\n            <p>Includes all client authentication information.</p>\n         "}, "EncryptionInfo": {"shape": "EncryptionInfo", "locationName": "encryptionInfo", "documentation": "\n            <p>Includes all encryption-related information.</p>\n         "}, "EnhancedMonitoring": {"shape": "EnhancedMonitoring", "locationName": "enhancedMonitoring", "documentation": "\n            <p>Specifies the level of monitoring for the MSK cluster. The possible values are DEFAULT, PER_BROKER, PER_TOPIC_PER_BROKER, and PER_TOPIC_PER_PARTITION.</p>\n         "}, "OpenMonitoring": {"shape": "OpenMonitoringInfo", "locationName": "openMonitoring", "documentation": "\n            <p>The settings for open monitoring.</p>\n         "}, "LoggingInfo": {"shape": "LoggingInfo", "locationName": "loggingInfo", "documentation": "\n            <p>Log delivery information for the cluster.</p>\n         "}, "NumberOfBrokerNodes": {"shape": "__integerMin1Max15", "locationName": "numberOfBrokerNodes", "documentation": "\n            <p>The number of broker nodes in the cluster.</p>\n         "}, "ZookeeperConnectString": {"shape": "__string", "locationName": "zookeeperConnectString", "documentation": "\n            <p>The connection string to use to connect to the Apache ZooKeeper cluster.</p>\n         "}, "ZookeeperConnectStringTls": {"shape": "__string", "locationName": "zookeeperConnectStringTls", "documentation": "\n            <p>The connection string to use to connect to the Apache ZooKeeper cluster on a TLS port.</p>\n         "}, "StorageMode": {"shape": "StorageMode", "locationName": "storageMode", "documentation": "\n            <p>This controls storage mode for supported storage tiers.</p>\n         "}, "CustomerActionStatus": {"shape": "CustomerActionStatus", "locationName": "customerActionStatus", "documentation": "\n            <p>Determines if there is an action required from the customer.</p>\n         "}}, "required": ["BrokerNodeGroupInfo", "NumberOfBrokerNodes"]}, "VpcConfig": {"type": "structure", "documentation": "\n            <p>The configuration of the Amazon VPCs for the cluster.</p>\n         ", "members": {"SubnetIds": {"shape": "__listOf__string", "locationName": "subnetIds", "documentation": "\n            <p>The IDs of the subnets associated with the cluster.</p>\n         "}, "SecurityGroupIds": {"shape": "__listOf__string", "locationName": "securityGroupIds", "documentation": "\n            <p>The IDs of the security groups associated with the cluster.</p>\n         "}}, "required": ["SubnetIds"]}, "ServerlessRequest": {"type": "structure", "documentation": "\n            <p>Serverless cluster request.</p>\n         ", "members": {"VpcConfigs": {"shape": "__listOfVpcConfig", "locationName": "vpcConfigs", "documentation": "\n            <p>The configuration of the Amazon VPCs for the cluster.</p>\n         "}, "ClientAuthentication": {"shape": "ServerlessClientAuthentication", "locationName": "clientAuthentication", "documentation": "\n            <p>Includes all client authentication information.</p>\n         "}}, "required": ["VpcConfigs"]}, "Serverless": {"type": "structure", "documentation": "\n            <p>Serverless cluster.</p>\n         ", "members": {"VpcConfigs": {"shape": "__listOfVpcConfig", "locationName": "vpcConfigs", "documentation": "\n            <p>The configuration of the Amazon VPCs for the cluster.</p>\n         "}, "ClientAuthentication": {"shape": "ServerlessClientAuthentication", "locationName": "clientAuthentication", "documentation": "\n            <p>Includes all client authentication information.</p>\n         "}}, "required": ["VpcConfigs"]}, "ClientVpcConnection": {"type": "structure", "documentation": "\n            <p>The client VPC connection object.</p>\n         ", "members": {"Authentication": {"shape": "__string", "locationName": "authentication", "documentation": "\n            <p>Information about the auth scheme of Vpc Connection.</p>\n         "}, "CreationTime": {"shape": "__timestampIso8601", "locationName": "creationTime", "documentation": "\n            <p>Creation time of the Vpc Connection.</p>\n         "}, "State": {"shape": "VpcConnectionState", "locationName": "state", "documentation": "\n            <p>State of the Vpc Connection.</p>\n         "}, "VpcConnectionArn": {"shape": "__string", "locationName": "vpcConnectionArn", "documentation": "\n            <p>The ARN that identifies the Vpc Connection.</p>\n         "}, "Owner": {"shape": "__string", "locationName": "owner", "documentation": "\n            <p>The Owner of the Vpc Connection.</p>\n         "}}, "required": ["VpcConnectionArn"]}, "VpcConnection": {"type": "structure", "documentation": "\n            <p>The VPC connection object.</p>\n         ", "members": {"VpcConnectionArn": {"shape": "__string", "locationName": "vpcConnectionArn", "documentation": "\n            <p>The ARN that identifies the Vpc Connection.</p>\n         "}, "TargetClusterArn": {"shape": "__string", "locationName": "targetClusterArn", "documentation": "\n            <p>The ARN that identifies the Cluster which the Vpc Connection belongs to.</p>\n         "}, "CreationTime": {"shape": "__timestampIso8601", "locationName": "creationTime", "documentation": "\n            <p>Creation time of the Vpc Connection.</p>\n         "}, "Authentication": {"shape": "__string", "locationName": "authentication", "documentation": "\n            <p>Information about the auth scheme of Vpc Connection.</p>\n         "}, "VpcId": {"shape": "__string", "locationName": "vpcId", "documentation": "\n            <p>The vpcId that belongs to the Vpc Connection.</p>\n         "}, "State": {"shape": "VpcConnectionState", "locationName": "state", "documentation": "\n            <p>State of the Vpc Connection.</p>\n         "}}, "required": ["VpcConnectionArn", "TargetClusterArn"]}, "CompatibleKafkaVersion": {"type": "structure", "members": {"SourceVersion": {"shape": "__string", "locationName": "sourceVersion", "documentation": "\n            <p>An Apache Kafka version.</p>\n            "}, "TargetVersions": {"shape": "__listOf__string", "locationName": "targetVersions", "documentation": "\n            <p>A list of Apache Kafka versions.</p>\n            "}}, "documentation": "\n            <p>Contains source Apache Kafka versions and compatible target Apache Kafka versions.</p>\n        "}, "Configuration": {"type": "structure", "members": {"Arn": {"shape": "__string", "locationName": "arn", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the configuration.</p>\n         "}, "CreationTime": {"shape": "__timestampIso8601", "locationName": "creationTime", "documentation": "\n            <p>The time when the configuration was created.</p>\n         "}, "Description": {"shape": "__string", "locationName": "description", "documentation": "\n            <p>The description of the configuration.</p>\n         "}, "KafkaVersions": {"shape": "__listOf__string", "locationName": "kafkaVersions", "documentation": "\n            <p>An array of the versions of Apache Kafka with which you can use this MSK configuration. You can use this configuration for an MSK cluster only if the Apache Kafka version specified for the cluster appears in this array.</p>\n         "}, "LatestRevision": {"shape": "ConfigurationRevision", "locationName": "latestRevision", "documentation": "\n            <p>Latest revision of the configuration.</p>\n         "}, "Name": {"shape": "__string", "locationName": "name", "documentation": "\n            <p>The name of the configuration.</p>\n         "}, "State": {"shape": "ConfigurationState", "locationName": "state", "documentation": "\n            <p>The state of the configuration. The possible states are ACTIVE, DELETING, and DELETE_FAILED. </p>\n         "}}, "documentation": "\n            <p>Represents an MSK Configuration.</p>\n         ", "required": ["Description", "LatestRevision", "CreationTime", "KafkaVersions", "<PERSON><PERSON>", "Name", "State"]}, "ConfigurationInfo": {"type": "structure", "members": {"Arn": {"shape": "__string", "locationName": "arn", "documentation": "\n            <p>ARN of the configuration to use.</p>\n         "}, "Revision": {"shape": "__long", "locationName": "revision", "documentation": "\n            <p>The revision of the configuration to use.</p>\n         "}}, "documentation": "\n            <p>Specifies the configuration to use for the brokers.</p>\n         ", "required": ["Revision", "<PERSON><PERSON>"]}, "ConfigurationRevision": {"type": "structure", "members": {"CreationTime": {"shape": "__timestampIso8601", "locationName": "creationTime", "documentation": "\n            <p>The time when the configuration revision was created.</p>\n         "}, "Description": {"shape": "__string", "locationName": "description", "documentation": "\n            <p>The description of the configuration revision.</p>\n         "}, "Revision": {"shape": "__long", "locationName": "revision", "documentation": "\n            <p>The revision number.</p>\n         "}}, "documentation": "\n            <p>Describes a configuration revision.</p>\n         ", "required": ["Revision", "CreationTime"]}, "ConfigurationState": {"type": "string", "documentation": "\n            <p>The state of a configuration.</p>\n         ", "enum": ["ACTIVE", "DELETING", "DELETE_FAILED"]}, "ConflictException": {"type": "structure", "members": {"InvalidParameter": {"shape": "__string", "locationName": "invalidParameter", "documentation": "\n            <p>The parameter that caused the error.</p>\n         "}, "Message": {"shape": "__string", "locationName": "message", "documentation": "\n            <p>The description of the error.</p>\n         "}}, "documentation": "\n            <p>Returns information about an error.</p>\n         ", "exception": true, "error": {"httpStatusCode": 409}}, "ConnectivityInfo": {"type": "structure", "members": {"PublicAccess": {"shape": "PublicAccess", "locationName": "publicAccess", "documentation": "\n            <p>Public access control for brokers.</p>\n         "}, "VpcConnectivity": {"shape": "VpcConnectivity", "locationName": "vpcConnectivity", "documentation": "\n            <p>VPC connectivity access control for brokers.</p>\n         "}}, "documentation": "\n            <p>Information about the broker access configuration.</p>\n         "}, "ConsumerGroupReplication": {"type": "structure", "members": {"ConsumerGroupsToExclude": {"shape": "__listOf__stringMax256", "locationName": "consumerGroupsToExclude", "documentation": "<p>List of regular expression patterns indicating the consumer groups that should not be replicated.</p>"}, "ConsumerGroupsToReplicate": {"shape": "__listOf__stringMax256", "locationName": "consumerGroupsToReplicate", "documentation": "<p>List of regular expression patterns indicating the consumer groups to copy.</p>"}, "DetectAndCopyNewConsumerGroups": {"shape": "__boolean", "locationName": "detectAndCopyNewConsumerGroups", "documentation": "<p>Enables synchronization of consumer groups to target cluster.</p>"}, "SynchroniseConsumerGroupOffsets": {"shape": "__boolean", "locationName": "synchroniseConsumerGroupOffsets", "documentation": "<p>Enables synchronization of consumer group offsets to target cluster. The translated offsets will be written to topic __consumer_offsets.</p>"}}, "documentation": "<p>Details about consumer group replication.</p>", "required": ["ConsumerGroupsToReplicate"]}, "ConsumerGroupReplicationUpdate": {"type": "structure", "members": {"ConsumerGroupsToExclude": {"shape": "__listOf__stringMax256", "locationName": "consumerGroupsToExclude", "documentation": "<p>List of regular expression patterns indicating the consumer groups that should not be replicated.</p>"}, "ConsumerGroupsToReplicate": {"shape": "__listOf__stringMax256", "locationName": "consumerGroupsToReplicate", "documentation": "<p>List of regular expression patterns indicating the consumer groups to copy.</p>"}, "DetectAndCopyNewConsumerGroups": {"shape": "__boolean", "locationName": "detectAndCopyNewConsumerGroups", "documentation": "<p>Enables synchronization of consumer groups to target cluster.</p>"}, "SynchroniseConsumerGroupOffsets": {"shape": "__boolean", "locationName": "synchroniseConsumerGroupOffsets", "documentation": "<p>Enables synchronization of consumer group offsets to target cluster. The translated offsets will be written to topic __consumer_offsets.</p>"}}, "documentation": "<p>Details about consumer group replication.</p>", "required": ["ConsumerGroupsToReplicate", "ConsumerGroupsToExclude", "SynchroniseConsumerGroupOffsets", "DetectAndCopyNewConsumerGroups"]}, "CreateClusterV2Request": {"type": "structure", "members": {"ClusterName": {"shape": "__stringMin1Max64", "locationName": "clusterName", "documentation": "\n            <p>The name of the cluster.</p>\n         "}, "Tags": {"shape": "__mapOf__string", "locationName": "tags", "documentation": "\n            <p>A map of tags that you want the cluster to have.</p>\n         "}, "Provisioned": {"shape": "ProvisionedRequest", "locationName": "provisioned", "documentation": "\n            <p>Information about the provisioned cluster.</p>\n         "}, "Serverless": {"shape": "ServerlessRequest", "locationName": "serverless", "documentation": "\n            <p>Information about the serverless cluster.</p>\n         "}}, "required": ["ClusterName"]}, "CreateClusterRequest": {"type": "structure", "members": {"BrokerNodeGroupInfo": {"shape": "BrokerNodeGroupInfo", "locationName": "brokerNodeGroupInfo", "documentation": "\n            <p>Information about the broker nodes in the cluster.</p>\n         "}, "ClientAuthentication": {"shape": "ClientAuthentication", "locationName": "clientAuthentication", "documentation": "\n            <p>Includes all client authentication related information.</p>\n         "}, "ClusterName": {"shape": "__stringMin1Max64", "locationName": "clusterName", "documentation": "\n            <p>The name of the cluster.</p>\n         "}, "ConfigurationInfo": {"shape": "ConfigurationInfo", "locationName": "configurationInfo", "documentation": "\n            <p>Represents the configuration that you want MSK to use for the brokers in a cluster.</p>\n         "}, "EncryptionInfo": {"shape": "EncryptionInfo", "locationName": "encryptionInfo", "documentation": "\n            <p>Includes all encryption-related information.</p>\n         "}, "EnhancedMonitoring": {"shape": "EnhancedMonitoring", "locationName": "enhancedMonitoring", "documentation": "\n            <p>Specifies the level of monitoring for the MSK cluster. The possible values are DEFAULT, PER_BROKER, PER_TOPIC_PER_BROKER, and PER_TOPIC_PER_PARTITION.</p>\n         "}, "OpenMonitoring": {"shape": "OpenMonitoringInfo", "locationName": "openMonitoring", "documentation": "\n            <p>The settings for open monitoring.</p>\n         "}, "KafkaVersion": {"shape": "__stringMin1Max128", "locationName": "kafkaVersion", "documentation": "\n            <p>The version of Apache Kafka.</p>\n         "}, "LoggingInfo": {"shape": "LoggingInfo", "locationName": "loggingInfo"}, "NumberOfBrokerNodes": {"shape": "__integerMin1Max15", "locationName": "numberOfBrokerNodes", "documentation": "\n            <p>The number of broker nodes in the cluster.</p>\n         "}, "Tags": {"shape": "__mapOf__string", "locationName": "tags", "documentation": "\n            <p>Create tags when creating the cluster.</p>\n         "}, "StorageMode": {"shape": "StorageMode", "locationName": "storageMode", "documentation": "\n            <p>This controls storage mode for supported storage tiers.</p>\n         "}}, "required": ["BrokerNodeGroupInfo", "KafkaVersion", "NumberOfBrokerNodes", "ClusterName"]}, "CreateClusterResponse": {"type": "structure", "members": {"ClusterArn": {"shape": "__string", "locationName": "clusterArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the cluster.</p>\n         "}, "ClusterName": {"shape": "__string", "locationName": "clusterName", "documentation": "\n            <p>The name of the MSK cluster.</p>\n         "}, "State": {"shape": "ClusterState", "locationName": "state", "documentation": "\n            <p>The state of the cluster. The possible states are ACTIVE, CREATING, DELETING, FAILED, HEALING, MAINTENANCE, REBOOTING_BROKER, and UPDATING.</p>\n         "}}}, "CreateClusterV2Response": {"type": "structure", "members": {"ClusterArn": {"shape": "__string", "locationName": "clusterArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the cluster.</p>\n         "}, "ClusterName": {"shape": "__string", "locationName": "clusterName", "documentation": "\n            <p>The name of the MSK cluster.</p>\n         "}, "State": {"shape": "ClusterState", "locationName": "state", "documentation": "\n            <p>The state of the cluster. The possible states are ACTIVE, CREATING, DELETING, FAILED, HEALING, MAINTENANCE, REBOOTING_BROKER, and UPDATING.</p>\n         "}, "ClusterType": {"shape": "ClusterType", "locationName": "clusterType", "documentation": "\n            <p>The type of the cluster. The possible states are PROVISIONED or SERVERLESS.</p>\n         "}}}, "CreateConfigurationRequest": {"type": "structure", "members": {"Description": {"shape": "__string", "locationName": "description", "documentation": "\n            <p>The description of the configuration.</p>\n         "}, "KafkaVersions": {"shape": "__listOf__string", "locationName": "kafkaVersions", "documentation": "\n            <p>The versions of Apache Kafka with which you can use this MSK configuration.</p>\n         "}, "Name": {"shape": "__string", "locationName": "name", "documentation": "\n            <p>The name of the configuration.</p>\n         "}, "ServerProperties": {"shape": "__blob", "locationName": "serverProperties", "documentation": "\n            <p>Contents of the <filename>server.properties</filename> file. When using the API, you must ensure that the contents of the file are base64 encoded. \n               When using the AWS Management Console, the SDK, or the AWS CLI, the contents of <filename>server.properties</filename> can be in plaintext.</p>\n         "}}, "required": ["ServerProperties", "Name"]}, "CreateConfigurationResponse": {"type": "structure", "members": {"Arn": {"shape": "__string", "locationName": "arn", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the configuration.</p>\n         "}, "CreationTime": {"shape": "__timestampIso8601", "locationName": "creationTime", "documentation": "\n            <p>The time when the configuration was created.</p>\n         "}, "LatestRevision": {"shape": "ConfigurationRevision", "locationName": "latestRevision", "documentation": "\n            <p>Latest revision of the configuration.</p>\n         "}, "Name": {"shape": "__string", "locationName": "name", "documentation": "\n            <p>The name of the configuration.</p>\n         "}, "State": {"shape": "ConfigurationState", "locationName": "state", "documentation": "\n            <p>The state of the configuration. The possible states are ACTIVE, DELETING, and DELETE_FAILED. </p>\n         "}}}, "CreateReplicatorRequest": {"type": "structure", "members": {"Description": {"shape": "__stringMax1024", "locationName": "description", "documentation": "<p>A summary description of the replicator.</p>"}, "KafkaClusters": {"shape": "__listOfKafkaCluster", "locationName": "kafkaClusters", "documentation": "<p>Kafka Clusters to use in setting up sources / targets for replication.</p>"}, "ReplicationInfoList": {"shape": "__listOfReplicationInfo", "locationName": "replicationInfoList", "documentation": "<p>A list of replication configurations, where each configuration targets a given source cluster to target cluster replication flow.</p>"}, "ReplicatorName": {"shape": "__stringMin1Max128Pattern09AZaZ09AZaZ0", "locationName": "replicatorName", "documentation": "<p>The name of the replicator. Alpha-numeric characters with '-' are allowed.</p>"}, "ServiceExecutionRoleArn": {"shape": "__string", "locationName": "serviceExecutionRoleArn", "documentation": "<p>The ARN of the IAM role used by the replicator to access resources in the customer's account (e.g source and target clusters)</p>"}, "Tags": {"shape": "__mapOf__string", "locationName": "tags", "documentation": "<p>List of tags to attach to created Replicator.</p>"}}, "documentation": "<p>Creates a replicator using the specified configuration.</p>", "required": ["ServiceExecutionRoleArn", "ReplicatorName", "ReplicationInfoList", "KafkaClusters"]}, "CreateReplicatorResponse": {"type": "structure", "members": {"ReplicatorArn": {"shape": "__string", "locationName": "replicatorArn", "documentation": "<p>The Amazon Resource Name (ARN) of the replicator.</p>"}, "ReplicatorName": {"shape": "__string", "locationName": "replicatorName", "documentation": "<p>Name of the replicator provided by the customer.</p>"}, "ReplicatorState": {"shape": "ReplicatorState", "locationName": "replicatorState", "documentation": "<p>State of the replicator.</p>"}}}, "CreateVpcConnectionRequest": {"type": "structure", "members": {"TargetClusterArn": {"shape": "__string", "locationName": "targetClusterArn", "documentation": "\n            <p>The cluster Amazon Resource Name (ARN) for the VPC connection.</p>\n         "}, "Authentication": {"shape": "__string", "locationName": "authentication", "documentation": "\n            <p>The authentication type of VPC connection.</p>\n         "}, "VpcId": {"shape": "__string", "locationName": "vpcId", "documentation": "\n            <p>The VPC ID of VPC connection.</p>\n         "}, "ClientSubnets": {"shape": "__listOf__string", "locationName": "clientSubnets", "documentation": "\n            <p>The list of client subnets.</p>\n         "}, "SecurityGroups": {"shape": "__listOf__string", "locationName": "securityGroups", "documentation": "\n            <p>The list of security groups.</p>\n         "}, "Tags": {"shape": "__mapOf__string", "locationName": "tags", "documentation": "\n            <p>A map of tags for the VPC connection.</p>\n         "}}, "required": ["TargetClusterArn", "Authentication", "VpcId", "ClientSubnets", "SecurityGroups"]}, "CreateVpcConnectionResponse": {"type": "structure", "members": {"VpcConnectionArn": {"shape": "__string", "locationName": "vpcConnectionArn", "documentation": "\n            <p>The VPC connection ARN.</p>\n         "}, "State": {"shape": "VpcConnectionState", "locationName": "state", "documentation": "\n            <p>The State of Vpc Connection.</p>\n         "}, "Authentication": {"shape": "__string", "locationName": "authentication", "documentation": "\n            <p>The authentication type of VPC connection.</p>\n         "}, "VpcId": {"shape": "__string", "locationName": "vpcId", "documentation": "\n            <p>The VPC ID of the VPC connection.</p>\n         "}, "ClientSubnets": {"shape": "__listOf__string", "locationName": "clientSubnets", "documentation": "\n            <p>The list of client subnets.</p>\n         "}, "SecurityGroups": {"shape": "__listOf__string", "locationName": "securityGroups", "documentation": "\n            <p>The list of security groups.</p>\n         "}, "CreationTime": {"shape": "__timestampIso8601", "locationName": "creationTime", "documentation": "\n            <p>The creation time of VPC connection.</p>\n         "}, "Tags": {"shape": "__mapOf__string", "locationName": "tags", "documentation": "\n            <p>A map of tags for the VPC connection.</p>\n         "}}}, "ClusterOperationV2": {"type": "structure", "members": {"ClusterArn": {"shape": "__string", "locationName": "clusterArn", "documentation": "\n            <p>ARN of the cluster.</p>"}, "ClusterType": {"shape": "ClusterType", "locationName": "clusterType", "documentation": "\n            <p>Type of the backend cluster.</p>"}, "StartTime": {"shape": "__timestampIso8601", "locationName": "startTime", "documentation": "\n            <p>The time at which operation was started.</p>"}, "EndTime": {"shape": "__timestampIso8601", "locationName": "endTime", "documentation": "\n            <p>The time at which the operation finished.</p>"}, "ErrorInfo": {"shape": "ErrorInfo", "locationName": "errorInfo", "documentation": "\n            <p>If cluster operation failed from an error, it describes the error.</p>"}, "OperationArn": {"shape": "__string", "locationName": "operationArn", "documentation": "\n            <p>ARN of the cluster operation.</p>"}, "OperationState": {"shape": "__string", "locationName": "operationState", "documentation": "\n            <p>State of the cluster operation.</p>"}, "OperationType": {"shape": "__string", "locationName": "operationType", "documentation": "\n            <p>Type of the cluster operation.</p>"}, "Provisioned": {"shape": "ClusterOperationV2Provisioned", "locationName": "provisioned", "documentation": "\n            <p>Properties of a provisioned cluster.</p>"}, "Serverless": {"shape": "ClusterOperationV2Serverless", "locationName": "serverless", "documentation": "\n            <p>Properties of a serverless cluster.</p>"}}, "documentation": "\n            <p>Returns information about a cluster operation.</p>"}, "ClusterOperationV2Provisioned": {"type": "structure", "members": {"OperationSteps": {"shape": "__listOfClusterOperationStep", "locationName": "operationSteps", "documentation": "\n            <p>Steps completed during the operation.</p>"}, "SourceClusterInfo": {"shape": "MutableClusterInfo", "locationName": "sourceClusterInfo", "documentation": "\n            <p>Information about cluster attributes before a cluster is updated.</p>"}, "TargetClusterInfo": {"shape": "MutableClusterInfo", "locationName": "targetClusterInfo", "documentation": "\n            <p>Information about cluster attributes after a cluster is updated.</p>"}, "VpcConnectionInfo": {"shape": "VpcConnectionInfo", "locationName": "vpcConnectionInfo", "documentation": "\n            <p>Description of the VPC connection for CreateVpcConnection and DeleteVpcConnection operations.</p>"}}, "documentation": "\n            <p>Returns information about a provisioned cluster operation.</p>"}, "ClusterOperationV2Serverless": {"type": "structure", "members": {"VpcConnectionInfo": {"shape": "VpcConnectionInfoServerless", "locationName": "vpcConnectionInfo", "documentation": "\n            <p>Description of the VPC connection for CreateVpcConnection and DeleteVpcConnection operations.</p>"}}, "documentation": "\n            <p>Returns information about a serverless cluster operation.</p>"}, "ClusterOperationV2Summary": {"type": "structure", "members": {"ClusterArn": {"shape": "__string", "locationName": "clusterArn", "documentation": "\n            <p>ARN of the cluster.</p>"}, "ClusterType": {"shape": "ClusterType", "locationName": "clusterType", "documentation": "\n            <p>Type of the backend cluster.</p>"}, "StartTime": {"shape": "__timestampIso8601", "locationName": "startTime", "documentation": "\n            <p>The time at which operation was started.</p>"}, "EndTime": {"shape": "__timestampIso8601", "locationName": "endTime", "documentation": "\n            <p>The time at which the operation finished.</p>"}, "OperationArn": {"shape": "__string", "locationName": "operationArn", "documentation": "\n            <p>ARN of the cluster operation.</p>"}, "OperationState": {"shape": "__string", "locationName": "operationState", "documentation": "\n            <p>State of the cluster operation.</p>"}, "OperationType": {"shape": "__string", "locationName": "operationType", "documentation": "\n            <p>Type of the cluster operation.</p>"}}, "documentation": "\n            <p>Returns information about a cluster operation.</p>"}, "ControllerNodeInfo": {"type": "structure", "members": {"Endpoints": {"shape": "__listOf__string", "locationName": "endpoints", "documentation": "\n            <p>Endpoints for accessing the Controller.</p>\n         "}}, "documentation": "\n            <p>Controller node information.</p>\n         "}, "CustomerActionStatus": {"type": "string", "documentation": "\n            <p>A type of an action required from the customer.</p>", "enum": ["CRITICAL_ACTION_REQUIRED", "ACTION_RECOMMENDED", "NONE"]}, "DeleteClusterRequest": {"type": "structure", "members": {"ClusterArn": {"shape": "__string", "location": "uri", "locationName": "clusterArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) that uniquely identifies the cluster.</p>\n         "}, "CurrentVersion": {"shape": "__string", "location": "querystring", "locationName": "currentVersion", "documentation": "\n            <p>The current version of the MSK cluster.</p>\n         "}}, "required": ["ClusterArn"]}, "DeleteClusterResponse": {"type": "structure", "members": {"ClusterArn": {"shape": "__string", "locationName": "clusterArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the cluster.</p>\n         "}, "State": {"shape": "ClusterState", "locationName": "state", "documentation": "\n            <p>The state of the cluster. The possible states are ACTIVE, CREATING, DELETING, FAILED, HEALING, MAINTENANCE, REBOOTING_BROKER, and UPDATING.</p>\n         "}}}, "DeleteClusterPolicyRequest": {"type": "structure", "members": {"ClusterArn": {"shape": "__string", "location": "uri", "locationName": "clusterArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the cluster.</p>\n         "}}, "required": ["ClusterArn"]}, "DeleteClusterPolicyResponse": {"type": "structure", "members": {}}, "DeleteConfigurationRequest": {"type": "structure", "members": {"Arn": {"shape": "__string", "location": "uri", "locationName": "arn", "documentation": "\n            <p>The Amazon Resource Name (ARN) that uniquely identifies an MSK configuration.</p>\n         "}}, "required": ["<PERSON><PERSON>"]}, "DeleteConfigurationResponse": {"type": "structure", "members": {"Arn": {"shape": "__string", "locationName": "arn", "documentation": "\n            <p>The Amazon Resource Name (ARN) that uniquely identifies an MSK configuration.</p>\n         "}, "State": {"shape": "ConfigurationState", "locationName": "state", "documentation": "\n            <p>The state of the configuration. The possible states are ACTIVE, DELETING, and DELETE_FAILED. </p>\n         "}}}, "DeleteReplicatorRequest": {"type": "structure", "members": {"CurrentVersion": {"shape": "__string", "location": "querystring", "locationName": "currentVersion", "documentation": "<p>The current version of the replicator.</p>"}, "ReplicatorArn": {"shape": "__string", "location": "uri", "locationName": "replicatorArn", "documentation": "<p>The Amazon Resource Name (ARN) of the replicator to be deleted.</p>"}}, "required": ["ReplicatorArn"]}, "DeleteReplicatorResponse": {"type": "structure", "members": {"ReplicatorArn": {"shape": "__string", "locationName": "replicatorArn", "documentation": "<p>The Amazon Resource Name (ARN) of the replicator.</p>"}, "ReplicatorState": {"shape": "ReplicatorState", "locationName": "replicatorState", "documentation": "<p>The state of the replicator.</p>"}}}, "DeleteVpcConnectionRequest": {"type": "structure", "members": {"Arn": {"shape": "__string", "location": "uri", "locationName": "arn", "documentation": "\n            <p>The Amazon Resource Name (ARN) that uniquely identifies an MSK VPC connection.</p>\n         "}}, "required": ["<PERSON><PERSON>"]}, "DeleteVpcConnectionResponse": {"type": "structure", "members": {"VpcConnectionArn": {"shape": "__string", "locationName": "vpcConnectionArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) that uniquely identifies an MSK VPC connection.</p>\n         "}, "State": {"shape": "VpcConnectionState", "locationName": "state", "documentation": "\n            <p>The state of the VPC connection.</p>\n         "}}}, "DescribeClusterOperationRequest": {"type": "structure", "members": {"ClusterOperationArn": {"shape": "__string", "location": "uri", "locationName": "clusterOperationArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) that uniquely identifies the MSK cluster operation.</p>\n         "}}, "required": ["ClusterOperationArn"]}, "DescribeClusterOperationV2Request": {"type": "structure", "members": {"ClusterOperationArn": {"shape": "__string", "location": "uri", "locationName": "clusterOperationArn", "documentation": "ARN of the cluster operation to describe."}}, "required": ["ClusterOperationArn"]}, "DescribeClusterOperationResponse": {"type": "structure", "members": {"ClusterOperationInfo": {"shape": "ClusterOperationInfo", "locationName": "clusterOperationInfo", "documentation": "\n            <p>Cluster operation information</p>\n         "}}}, "DescribeClusterOperationV2Response": {"type": "structure", "members": {"ClusterOperationInfo": {"shape": "ClusterOperationV2", "locationName": "clusterOperationInfo", "documentation": "\n            <p>Cluster operation information</p>"}}}, "DescribeClusterRequest": {"type": "structure", "members": {"ClusterArn": {"shape": "__string", "location": "uri", "locationName": "clusterArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) that uniquely identifies the cluster.</p>\n         "}}, "required": ["ClusterArn"]}, "DescribeClusterV2Request": {"type": "structure", "members": {"ClusterArn": {"shape": "__string", "location": "uri", "locationName": "clusterArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) that uniquely identifies the cluster.</p>\n         "}}, "required": ["ClusterArn"]}, "DescribeClusterResponse": {"type": "structure", "members": {"ClusterInfo": {"shape": "ClusterInfo", "locationName": "clusterInfo", "documentation": "\n            <p>The cluster information.</p>\n         "}}}, "DescribeClusterV2Response": {"type": "structure", "members": {"ClusterInfo": {"shape": "Cluster", "locationName": "clusterInfo", "documentation": "\n            <p>The cluster information.</p>\n         "}}}, "DescribeConfigurationRequest": {"type": "structure", "members": {"Arn": {"shape": "__string", "location": "uri", "locationName": "arn", "documentation": "\n            <p>The Amazon Resource Name (ARN) that uniquely identifies an MSK configuration and all of its revisions.</p>\n         "}}, "required": ["<PERSON><PERSON>"]}, "DescribeConfigurationResponse": {"type": "structure", "members": {"Arn": {"shape": "__string", "locationName": "arn", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the configuration.</p>\n         "}, "CreationTime": {"shape": "__timestampIso8601", "locationName": "creationTime", "documentation": "\n            <p>The time when the configuration was created.</p>\n         "}, "Description": {"shape": "__string", "locationName": "description", "documentation": "\n            <p>The description of the configuration.</p>\n         "}, "KafkaVersions": {"shape": "__listOf__string", "locationName": "kafkaVersions", "documentation": "\n            <p>The versions of Apache Kafka with which you can use this MSK configuration.</p>\n         "}, "LatestRevision": {"shape": "ConfigurationRevision", "locationName": "latestRevision", "documentation": "\n            <p>Latest revision of the configuration.</p>\n         "}, "Name": {"shape": "__string", "locationName": "name", "documentation": "\n            <p>The name of the configuration.</p>\n         "}, "State": {"shape": "ConfigurationState", "locationName": "state", "documentation": "\n            <p>The state of the configuration. The possible states are ACTIVE, DELETING, and DELETE_FAILED. </p>\n         "}}}, "DescribeConfigurationRevisionRequest": {"type": "structure", "members": {"Arn": {"shape": "__string", "location": "uri", "locationName": "arn", "documentation": "\n            <p>The Amazon Resource Name (ARN) that uniquely identifies an MSK configuration and all of its revisions.</p>\n         "}, "Revision": {"shape": "__long", "location": "uri", "locationName": "revision", "documentation": "\n            <p>A string that uniquely identifies a revision of an MSK configuration.</p>\n         "}}, "required": ["Revision", "<PERSON><PERSON>"]}, "DescribeConfigurationRevisionResponse": {"type": "structure", "members": {"Arn": {"shape": "__string", "locationName": "arn", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the configuration.</p>\n         "}, "CreationTime": {"shape": "__timestampIso8601", "locationName": "creationTime", "documentation": "\n            <p>The time when the configuration was created.</p>\n         "}, "Description": {"shape": "__string", "locationName": "description", "documentation": "\n            <p>The description of the configuration.</p>\n         "}, "Revision": {"shape": "__long", "locationName": "revision", "documentation": "\n            <p>The revision number.</p>\n         "}, "ServerProperties": {"shape": "__blob", "locationName": "serverProperties", "documentation": "\n            <p>Contents of the <filename>server.properties</filename> file. When using the API, you must ensure that the contents of the file are base64 encoded. \n               When using the AWS Management Console, the SDK, or the AWS CLI, the contents of <filename>server.properties</filename> can be in plaintext.</p>\n         "}}}, "DescribeVpcConnectionRequest": {"type": "structure", "members": {"Arn": {"shape": "__string", "location": "uri", "locationName": "arn", "documentation": "\n            <p>The Amazon Resource Name (ARN) that uniquely identifies a MSK VPC connection.</p>\n   "}}, "required": ["<PERSON><PERSON>"]}, "DescribeReplicatorRequest": {"type": "structure", "members": {"ReplicatorArn": {"shape": "__string", "location": "uri", "locationName": "replicatorArn", "documentation": "<p>The Amazon Resource Name (ARN) of the replicator to be described.</p>"}}, "required": ["ReplicatorArn"]}, "DescribeReplicatorResponse": {"type": "structure", "members": {"CreationTime": {"shape": "__timestampIso8601", "locationName": "creationTime", "documentation": "<p>The time when the replicator was created.</p>"}, "CurrentVersion": {"shape": "__string", "locationName": "currentVersion", "documentation": "<p>The current version number of the replicator.</p>"}, "IsReplicatorReference": {"shape": "__boolean", "locationName": "isReplicatorReference", "documentation": "<p>Whether this resource is a replicator reference.</p>"}, "KafkaClusters": {"shape": "__listOfKafkaClusterDescription", "locationName": "kafkaClusters", "documentation": "<p>Kafka Clusters used in setting up sources / targets for replication.</p>"}, "ReplicationInfoList": {"shape": "__listOfReplicationInfoDescription", "locationName": "replicationInfoList", "documentation": "<p>A list of replication configurations, where each configuration targets a given source cluster to target cluster replication flow.</p>"}, "ReplicatorArn": {"shape": "__string", "locationName": "replicatorArn", "documentation": "<p>The Amazon Resource Name (ARN) of the replicator.</p>"}, "ReplicatorDescription": {"shape": "__string", "locationName": "replicatorDescription", "documentation": "<p>The description of the replicator.</p>"}, "ReplicatorName": {"shape": "__string", "locationName": "replicatorName", "documentation": "<p>The name of the replicator.</p>"}, "ReplicatorResourceArn": {"shape": "__string", "locationName": "replicatorResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the replicator resource in the region where the replicator was created.</p>"}, "ReplicatorState": {"shape": "ReplicatorState", "locationName": "replicatorState", "documentation": "<p>State of the replicator.</p>"}, "ServiceExecutionRoleArn": {"shape": "__string", "locationName": "serviceExecutionRoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role used by the replicator to access resources in the customer's account (e.g source and target clusters)</p>"}, "StateInfo": {"shape": "ReplicationStateInfo", "locationName": "stateInfo", "documentation": "<p>Details about the state of the replicator.</p>"}, "Tags": {"shape": "__mapOf__string", "locationName": "tags", "documentation": "<p>List of tags attached to the Replicator.</p>"}}}, "DescribeVpcConnectionResponse": {"type": "structure", "members": {"VpcConnectionArn": {"shape": "__string", "locationName": "vpcConnectionArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) that uniquely identifies a MSK VPC connection.</p>\n   "}, "TargetClusterArn": {"shape": "__string", "locationName": "targetClusterArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) that uniquely identifies an MSK cluster.</p>\n   "}, "State": {"shape": "VpcConnectionState", "locationName": "state", "documentation": "\n            <p>The state of VPC connection.</p>\n   "}, "Authentication": {"shape": "__string", "locationName": "authentication", "documentation": "\n            <p>The authentication type of VPC connection.</p>\n   "}, "VpcId": {"shape": "__string", "locationName": "vpcId", "documentation": "\n            <p>The VPC Id for the VPC connection.</p>\n   "}, "Subnets": {"shape": "__listOf__string", "locationName": "subnets", "documentation": "\n            <p>The list of subnets for the VPC connection.</p>\n   "}, "SecurityGroups": {"shape": "__listOf__string", "locationName": "securityGroups", "documentation": "\n            <p>The list of security groups for the VPC connection.</p>\n   "}, "CreationTime": {"shape": "__timestampIso8601", "locationName": "creationTime", "documentation": "\n            <p>The creation time of the VPC connection.</p>\n   "}, "Tags": {"shape": "__mapOf__string", "locationName": "tags", "documentation": "\n            <p>A map of tags for the VPC connection.</p>\n         "}}}, "BatchDisassociateScramSecretRequest": {"type": "structure", "members": {"ClusterArn": {"shape": "__string", "location": "uri", "locationName": "clusterArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the cluster to be updated.</p>\n         "}, "SecretArnList": {"shape": "__listOf__string", "locationName": "secretArnList", "documentation": "\n            <p>List of AWS Secrets Manager secret ARNs.</p>\n         "}}, "documentation": "\n            <p>Disassociates sasl scram secrets to cluster.</p>\n         ", "required": ["ClusterArn", "SecretArnList"]}, "BatchDisassociateScramSecretResponse": {"type": "structure", "members": {"ClusterArn": {"shape": "__string", "locationName": "clusterArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the cluster.</p>\n         "}, "UnprocessedScramSecrets": {"shape": "__listOfUnprocessedScramSecret", "locationName": "unprocessedScramSecrets", "documentation": "\n            <p>List of errors when disassociating secrets to cluster.</p>\n         "}}}, "EBSStorageInfo": {"type": "structure", "members": {"ProvisionedThroughput": {"shape": "ProvisionedThroughput", "locationName": "provisionedThroughput", "documentation": "\n            <p>EBS volume provisioned throughput information.</p>\n         "}, "VolumeSize": {"shape": "__integerMin1Max16384", "locationName": "volumeSize", "documentation": "\n            <p>The size in GiB of the EBS volume for the data drive on each broker node.</p>\n         "}}, "documentation": "\n            <p>Contains information about the EBS storage volumes attached to Apache Kafka broker nodes.</p>\n         "}, "EncryptionAtRest": {"type": "structure", "members": {"DataVolumeKMSKeyId": {"shape": "__string", "locationName": "dataVolumeKMSKeyId", "documentation": "\n            <p>The ARN of the AWS KMS key for encrypting data at rest. If you don't specify a KMS key, MSK creates one for you and uses it.</p>\n         "}}, "documentation": "\n            <p>The data-volume encryption details.</p>\n         ", "required": ["DataVolumeKMSKeyId"]}, "EncryptionInTransit": {"type": "structure", "members": {"ClientBroker": {"shape": "ClientBroker", "locationName": "clientBroker", "documentation": "\n            <p>Indicates the encryption setting for data in transit between clients and brokers. The following are the possible values.</p>\n            <p>\n               TLS means that client-broker communication is enabled with TLS only.</p>\n            <p>\n               TLS_PLAINTEXT means that client-broker communication is enabled for both TLS-encrypted, as well as plaintext data.</p>\n            <p>\n               PLAINTEXT means that client-broker communication is enabled in plaintext only.</p>\n            <p>The default value is TLS_PLAINTEXT.</p>\n         "}, "InCluster": {"shape": "__boolean", "locationName": "inCluster", "documentation": "\n            <p>When set to true, it indicates that data communication among the broker nodes of the cluster is encrypted. When set to false, the communication happens in plaintext.</p>\n            <p>The default value is true.</p>\n         "}}, "documentation": "\n            <p>The settings for encrypting data in transit.</p>\n         "}, "EncryptionInfo": {"type": "structure", "members": {"EncryptionAtRest": {"shape": "EncryptionAtRest", "locationName": "encryptionAtRest", "documentation": "\n            <p>The data-volume encryption details.</p>\n         "}, "EncryptionInTransit": {"shape": "EncryptionInTransit", "locationName": "encryptionInTransit", "documentation": "\n            <p>The details for encryption in transit.</p>\n         "}}, "documentation": "\n            <p>Includes encryption-related information, such as the AWS KMS key used for encrypting data at rest and whether you want MSK to encrypt your data in transit.</p>\n         "}, "EnhancedMonitoring": {"type": "string", "documentation": "\n            <p>Specifies which metrics are gathered for the MSK cluster. This property has the following possible values: DEFAULT, PER_BROKER, PER_TOPIC_PER_BROKER, and PER_TOPIC_PER_PARTITION. For a list of the metrics associated with each of these levels of monitoring, see <a href=\"https://docs.aws.amazon.com/msk/latest/developerguide/monitoring.html\">Monitoring</a>.</p>\n         ", "enum": ["DEFAULT", "PER_BROKER", "PER_TOPIC_PER_BROKER", "PER_TOPIC_PER_PARTITION"]}, "Error": {"type": "structure", "members": {"InvalidParameter": {"shape": "__string", "locationName": "invalidParameter", "documentation": "\n            <p>The parameter that caused the error.</p>\n         "}, "Message": {"shape": "__string", "locationName": "message", "documentation": "\n            <p>The description of the error.</p>\n         "}}, "documentation": "\n            <p>Returns information about an error.</p>\n         "}, "ErrorInfo": {"type": "structure", "members": {"ErrorCode": {"shape": "__string", "locationName": "errorCode", "documentation": "\n            <p>A number describing the error programmatically.</p>\n         "}, "ErrorString": {"shape": "__string", "locationName": "errorString", "documentation": "\n            <p>An optional field to provide more details about the error.</p>\n         "}}, "documentation": "\n            <p>Returns information about an error state of the cluster.</p>\n         "}, "Firehose": {"type": "structure", "members": {"DeliveryStream": {"shape": "__string", "locationName": "deliveryStream"}, "Enabled": {"shape": "__boolean", "locationName": "enabled"}}, "required": ["Enabled"]}, "ForbiddenException": {"type": "structure", "members": {"InvalidParameter": {"shape": "__string", "locationName": "invalidParameter", "documentation": "\n            <p>The parameter that caused the error.</p>\n         "}, "Message": {"shape": "__string", "locationName": "message", "documentation": "\n            <p>The description of the error.</p>\n         "}}, "documentation": "\n            <p>Returns information about an error.</p>\n         ", "exception": true, "error": {"httpStatusCode": 403}}, "GetBootstrapBrokersRequest": {"type": "structure", "members": {"ClusterArn": {"shape": "__string", "location": "uri", "locationName": "clusterArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) that uniquely identifies the cluster.</p>\n         "}}, "required": ["ClusterArn"]}, "GetBootstrapBrokersResponse": {"type": "structure", "members": {"BootstrapBrokerString": {"shape": "__string", "locationName": "bootstrapBrokerString", "documentation": "\n            <p>A string containing one or more hostname:port pairs.</p>\n         "}, "BootstrapBrokerStringTls": {"shape": "__string", "locationName": "bootstrapBrokerStringTls", "documentation": "\n            <p>A string containing one or more DNS names (or IP) and TLS port pairs.</p>\n         "}, "BootstrapBrokerStringSaslScram": {"shape": "__string", "locationName": "bootstrapBrokerStringSaslScram", "documentation": "\n            <p>A string containing one or more DNS names (or IP) and Sasl Scram port pairs.</p>\n         "}, "BootstrapBrokerStringSaslIam": {"shape": "__string", "locationName": "bootstrapBrokerStringSaslIam", "documentation": "\n            <p>A string that contains one or more DNS names (or IP addresses) and SASL IAM port pairs.</p>\n         "}, "BootstrapBrokerStringPublicTls": {"shape": "__string", "locationName": "bootstrapBrokerStringPublicTls", "documentation": "\n            <p>A string containing one or more DNS names (or IP) and TLS port pairs.</p>\n         "}, "BootstrapBrokerStringPublicSaslScram": {"shape": "__string", "locationName": "bootstrapBrokerStringPublicSaslScram", "documentation": "\n            <p>A string containing one or more DNS names (or IP) and Sasl Scram port pairs.</p>\n         "}, "BootstrapBrokerStringPublicSaslIam": {"shape": "__string", "locationName": "bootstrapBrokerStringPublicSaslIam", "documentation": "\n            <p>A string that contains one or more DNS names (or IP addresses) and SASL IAM port pairs.</p>\n         "}, "BootstrapBrokerStringVpcConnectivityTls": {"shape": "__string", "locationName": "bootstrapBrokerStringVpcConnectivityTls", "documentation": "\n            <p>A string containing one or more DNS names (or IP) and TLS port pairs for VPC connectivity.</p>\n         "}, "BootstrapBrokerStringVpcConnectivitySaslScram": {"shape": "__string", "locationName": "bootstrapBrokerStringVpcConnectivitySaslScram", "documentation": "\n            <p>A string containing one or more DNS names (or IP) and SASL/SCRAM port pairs for VPC connectivity.</p>\n         "}, "BootstrapBrokerStringVpcConnectivitySaslIam": {"shape": "__string", "locationName": "bootstrapBrokerStringVpcConnectivitySaslIam", "documentation": "\n            <p>A string containing one or more DNS names (or IP) and SASL/IAM port pairs for VPC connectivity.</p>\n         "}}}, "GetCompatibleKafkaVersionsRequest": {"type": "structure", "members": {"ClusterArn": {"shape": "__string", "location": "querystring", "locationName": "clusterArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the cluster check.</p>\n            "}}}, "GetCompatibleKafkaVersionsResponse": {"type": "structure", "members": {"CompatibleKafkaVersions": {"shape": "__listOfCompatibleKafkaVersion", "locationName": "compatibleKafkaVersions", "documentation": "\n            <p>A list of CompatibleKafkaVersion objects.</p>\n            "}}}, "GetClusterPolicyRequest": {"type": "structure", "members": {"ClusterArn": {"shape": "__string", "location": "uri", "locationName": "clusterArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the cluster.</p>\n            "}}, "required": ["ClusterArn"]}, "GetClusterPolicyResponse": {"type": "structure", "members": {"CurrentVersion": {"shape": "__string", "locationName": "currentVersion", "documentation": "\n            <p>The version of cluster policy.</p>\n            "}, "Policy": {"shape": "__string", "locationName": "policy", "documentation": "\n            <p>The cluster policy.</p>\n            "}}}, "InternalServerErrorException": {"type": "structure", "members": {"InvalidParameter": {"shape": "__string", "locationName": "invalidParameter", "documentation": "\n            <p>The parameter that caused the error.</p>\n         "}, "Message": {"shape": "__string", "locationName": "message", "documentation": "\n            <p>The description of the error.</p>\n         "}}, "documentation": "\n            <p>Returns information about an error.</p>\n         ", "exception": true, "error": {"httpStatusCode": 500}}, "KafkaCluster": {"type": "structure", "members": {"AmazonMskCluster": {"shape": "AmazonMskCluster", "locationName": "amazonMskCluster", "documentation": "<p>Details of an Amazon MSK Cluster.</p>"}, "VpcConfig": {"shape": "KafkaClusterClientVpcConfig", "locationName": "vpcConfig", "documentation": "<p>Details of an Amazon VPC which has network connectivity to the Apache Kafka cluster.</p>"}}, "documentation": "<p>Information about Kafka Cluster to be used as source / target for replication.</p>", "required": ["VpcConfig", "AmazonMskCluster"]}, "KafkaClusterClientVpcConfig": {"type": "structure", "members": {"SecurityGroupIds": {"shape": "__listOf__string", "locationName": "securityGroupIds", "documentation": "<p>The security groups to attach to the ENIs for the broker nodes.</p>"}, "SubnetIds": {"shape": "__listOf__string", "locationName": "subnetIds", "documentation": "<p>The list of subnets in the client VPC to connect to.</p>"}}, "documentation": "<p>Details of an Amazon VPC which has network connectivity to the Apache Kafka cluster.</p>", "required": ["SubnetIds"]}, "KafkaClusterDescription": {"type": "structure", "members": {"AmazonMskCluster": {"shape": "AmazonMskCluster", "locationName": "amazonMskCluster", "documentation": "<p>Details of an Amazon MSK Cluster.</p>"}, "KafkaClusterAlias": {"shape": "__string", "locationName": "kafkaClusterAlias", "documentation": "<p>The alias of the Kafka cluster. Used to prefix names of replicated topics.</p> "}, "VpcConfig": {"shape": "KafkaClusterClientVpcConfig", "locationName": "vpcConfig", "documentation": "<p>Details of an Amazon VPC which has network connectivity to the Apache Kafka cluster.</p>"}}, "documentation": "<p>Information about Kafka Cluster used as source / target for replication.</p>"}, "KafkaClusterSummary": {"type": "structure", "members": {"AmazonMskCluster": {"shape": "AmazonMskCluster", "locationName": "amazonMskCluster", "documentation": "<p>Details of an Amazon MSK Cluster.</p>"}, "KafkaClusterAlias": {"shape": "__string", "locationName": "kafkaClusterAlias", "documentation": "<p>The alias of the Kafka cluster. Used to prefix names of replicated topics.</p> "}}, "documentation": "<p>Summarized information about Kafka Cluster used as source / target for replication.</p>"}, "KafkaVersion": {"type": "structure", "members": {"Version": {"shape": "__string", "locationName": "version"}, "Status": {"shape": "KafkaVersionStatus", "locationName": "status"}}}, "KafkaVersionStatus": {"type": "string", "enum": ["ACTIVE", "DEPRECATED"]}, "ListClusterOperationsRequest": {"type": "structure", "members": {"ClusterArn": {"shape": "__string", "location": "uri", "locationName": "clusterArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) that uniquely identifies the cluster.</p>\n         "}, "MaxResults": {"shape": "MaxResults", "location": "querystring", "locationName": "maxResults", "documentation": "\n            <p>The maximum number of results to return in the response. If there are more results, the response includes a NextToken parameter.</p>\n         "}, "NextToken": {"shape": "__string", "location": "querystring", "locationName": "nextToken", "documentation": "\n            <p>The paginated results marker. When the result of the operation is truncated, the call returns NextToken in the response. \n            To get the next batch, provide this token in your next request.</p>\n         "}}, "required": ["ClusterArn"]}, "ListClusterOperationsV2Request": {"type": "structure", "members": {"ClusterArn": {"shape": "__string", "location": "uri", "locationName": "clusterArn", "documentation": "The arn of the cluster whose operations are being requested."}, "MaxResults": {"shape": "MaxResults", "location": "querystring", "locationName": "maxResults", "documentation": "The maxResults of the query."}, "NextToken": {"shape": "__string", "location": "querystring", "locationName": "nextToken", "documentation": "The nextToken of the query."}}, "required": ["ClusterArn"]}, "ListClusterOperationsResponse": {"type": "structure", "members": {"ClusterOperationInfoList": {"shape": "__listOfClusterOperationInfo", "locationName": "clusterOperationInfoList", "documentation": "\n            <p>An array of cluster operation information objects.</p>\n         "}, "NextToken": {"shape": "__string", "locationName": "nextToken", "documentation": "\n            <p>If the response of ListClusterOperations is truncated, it returns a NextToken in the response. This Nexttoken should be sent in the subsequent request to ListClusterOperations.</p>\n         "}}}, "ListClusterOperationsV2Response": {"type": "structure", "members": {"ClusterOperationInfoList": {"shape": "__listOfClusterOperationV2Summary", "locationName": "clusterOperationInfoList", "documentation": "\n            <p>An array of cluster operation information objects.</p>"}, "NextToken": {"shape": "__string", "locationName": "nextToken", "documentation": "\n            <p>If the response of ListClusterOperationsV2 is truncated, it returns a NextToken in the response. This NextToken should be sent in the subsequent request to ListClusterOperationsV2.</p>"}}}, "ListClustersRequest": {"type": "structure", "members": {"ClusterNameFilter": {"shape": "__string", "location": "querystring", "locationName": "clusterNameFilter", "documentation": "\n            <p>Specify a prefix of the name of the clusters that you want to list. The service lists all the clusters whose names start with this prefix.</p>\n         "}, "MaxResults": {"shape": "MaxResults", "location": "querystring", "locationName": "maxResults", "documentation": "\n            <p>The maximum number of results to return in the response. If there are more results, the response includes a NextToken parameter.</p>\n         "}, "NextToken": {"shape": "__string", "location": "querystring", "locationName": "nextToken", "documentation": "\n            <p>The paginated results marker. When the result of the operation is truncated, the call returns NextToken in the response. \n            To get the next batch, provide this token in your next request.</p>\n         "}}}, "ListClustersV2Request": {"type": "structure", "members": {"ClusterNameFilter": {"shape": "__string", "location": "querystring", "locationName": "clusterNameFilter", "documentation": "\n            <p>Specify a prefix of the names of the clusters that you want to list. The service lists all the clusters whose names start with this prefix.</p>\n         "}, "ClusterTypeFilter": {"shape": "__string", "location": "querystring", "locationName": "clusterTypeFilter", "documentation": "\n            <p>Specify either PROVISIONED or SERVERLESS.</p>\n         "}, "MaxResults": {"shape": "MaxResults", "location": "querystring", "locationName": "maxResults", "documentation": "\n            <p>The maximum number of results to return in the response. If there are more results, the response includes a NextToken parameter.</p>\n         "}, "NextToken": {"shape": "__string", "location": "querystring", "locationName": "nextToken", "documentation": "\n            <p>The paginated results marker. When the result of the operation is truncated, the call returns NextToken in the response. \n            To get the next batch, provide this token in your next request.</p>\n         "}}}, "ListClustersResponse": {"type": "structure", "members": {"ClusterInfoList": {"shape": "__listOfClusterInfo", "locationName": "clusterInfoList", "documentation": "\n            <p>Information on each of the MSK clusters in the response.</p>\n         "}, "NextToken": {"shape": "__string", "locationName": "nextToken", "documentation": "\n            <p>The paginated results marker. When the result of a ListClusters operation is truncated, the call returns NextToken in the response. \n               To get another batch of clusters, provide this token in your next request.</p>\n         "}}}, "ListClustersV2Response": {"type": "structure", "members": {"ClusterInfoList": {"shape": "__listOfCluster", "locationName": "clusterInfoList", "documentation": "\n            <p>Information on each of the MSK clusters in the response.</p>\n         "}, "NextToken": {"shape": "__string", "locationName": "nextToken", "documentation": "\n            <p>The paginated results marker. When the result of a ListClusters operation is truncated, the call returns NextToken in the response. \n               To get another batch of clusters, provide this token in your next request.</p>\n         "}}}, "ListConfigurationRevisionsRequest": {"type": "structure", "members": {"Arn": {"shape": "__string", "location": "uri", "locationName": "arn", "documentation": "\n            <p>The Amazon Resource Name (ARN) that uniquely identifies an MSK configuration and all of its revisions.</p>\n         "}, "MaxResults": {"shape": "MaxResults", "location": "querystring", "locationName": "maxResults", "documentation": "\n            <p>The maximum number of results to return in the response. If there are more results, the response includes a NextToken parameter.</p>\n         "}, "NextToken": {"shape": "__string", "location": "querystring", "locationName": "nextToken", "documentation": "\n            <p>The paginated results marker. When the result of the operation is truncated, the call returns NextToken in the response. \n            To get the next batch, provide this token in your next request.</p>\n         "}}, "required": ["<PERSON><PERSON>"]}, "ListConfigurationRevisionsResponse": {"type": "structure", "members": {"NextToken": {"shape": "__string", "locationName": "nextToken", "documentation": "\n            <p>Paginated results marker.</p>\n         "}, "Revisions": {"shape": "__listOfConfigurationRevision", "locationName": "revisions", "documentation": "\n            <p>List of ConfigurationRevision objects.</p>\n         "}}}, "ListConfigurationsRequest": {"type": "structure", "members": {"MaxResults": {"shape": "MaxResults", "location": "querystring", "locationName": "maxResults", "documentation": "\n            <p>The maximum number of results to return in the response. If there are more results, the response includes a NextToken parameter.</p>\n         "}, "NextToken": {"shape": "__string", "location": "querystring", "locationName": "nextToken", "documentation": "\n            <p>The paginated results marker. When the result of the operation is truncated, the call returns NextToken in the response. \n            To get the next batch, provide this token in your next request.</p>\n         "}}}, "ListConfigurationsResponse": {"type": "structure", "members": {"Configurations": {"shape": "__listOfConfiguration", "locationName": "configurations", "documentation": "\n            <p>An array of MSK configurations.</p>\n         "}, "NextToken": {"shape": "__string", "locationName": "nextToken", "documentation": "\n            <p>The paginated results marker. When the result of a ListConfigurations operation is truncated, the call returns NextToken in the response. \n               To get another batch of configurations, provide this token in your next request.</p>\n         "}}}, "ListKafkaVersionsRequest": {"type": "structure", "members": {"MaxResults": {"shape": "MaxResults", "location": "querystring", "locationName": "maxResults", "documentation": "\n            <p>The maximum number of results to return in the response. If there are more results, the response includes a NextToken parameter.</p>"}, "NextToken": {"shape": "__string", "location": "querystring", "locationName": "nextToken", "documentation": "\n            <p>The paginated results marker. When the result of the operation is truncated, the call returns NextToken in the response. To get the next batch, provide this token in your next request.</p>"}}}, "ListKafkaVersionsResponse": {"type": "structure", "members": {"KafkaVersions": {"shape": "__listOfKafkaVersion", "locationName": "kafkaVersions"}, "NextToken": {"shape": "__string", "locationName": "nextToken"}}}, "ListNodesRequest": {"type": "structure", "members": {"ClusterArn": {"shape": "__string", "location": "uri", "locationName": "clusterArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) that uniquely identifies the cluster.</p>\n         "}, "MaxResults": {"shape": "MaxResults", "location": "querystring", "locationName": "maxResults", "documentation": "\n            <p>The maximum number of results to return in the response. If there are more results, the response includes a NextToken parameter.</p>\n         "}, "NextToken": {"shape": "__string", "location": "querystring", "locationName": "nextToken", "documentation": "\n            <p>The paginated results marker. When the result of the operation is truncated, the call returns NextToken in the response. \n            To get the next batch, provide this token in your next request.</p>\n         "}}, "required": ["ClusterArn"]}, "ListNodesResponse": {"type": "structure", "members": {"NextToken": {"shape": "__string", "locationName": "nextToken", "documentation": "\n            <p>The paginated results marker. When the result of a ListNodes operation is truncated, the call returns NextToken in the response. \n               To get another batch of nodes, provide this token in your next request.</p>\n         "}, "NodeInfoList": {"shape": "__listOfNodeInfo", "locationName": "nodeInfoList", "documentation": "\n            <p>List containing a NodeInfo object.</p>\n         "}}}, "ListReplicatorsRequest": {"type": "structure", "members": {"MaxResults": {"shape": "MaxResults", "location": "querystring", "locationName": "maxResults", "documentation": "<p>The maximum number of results to return in the response. If there are more results, the response includes a NextToken parameter.</p>"}, "NextToken": {"shape": "__string", "location": "querystring", "locationName": "nextToken", "documentation": "<p>If the response of ListReplicators is truncated, it returns a NextToken in the response. This NextToken should be sent in the subsequent request to ListReplicators.</p>"}, "ReplicatorNameFilter": {"shape": "__string", "location": "querystring", "locationName": "replicator<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>Returns replicators starting with given name.</p>"}}}, "ListReplicatorsResponse": {"type": "structure", "members": {"NextToken": {"shape": "__string", "locationName": "nextToken", "documentation": "<p>If the response of ListReplicators is truncated, it returns a NextToken in the response. This NextToken should be sent in the subsequent request to ListReplicators.</p>"}, "Replicators": {"shape": "__listOfReplicatorSummary", "locationName": "replicators", "documentation": "<p>List containing information of each of the replicators in the account.</p>"}}}, "ListScramSecretsRequest": {"type": "structure", "members": {"ClusterArn": {"shape": "__string", "location": "uri", "locationName": "clusterArn", "documentation": "\n            <p>The arn of the cluster.</p>\n         "}, "MaxResults": {"shape": "MaxResults", "location": "querystring", "locationName": "maxResults", "documentation": "\n            <p>The maxResults of the query.</p>\n         "}, "NextToken": {"shape": "__string", "location": "querystring", "locationName": "nextToken", "documentation": "\n            <p>The nextToken of the query.</p>\n         "}}, "required": ["ClusterArn"]}, "ListScramSecretsResponse": {"type": "structure", "members": {"NextToken": {"shape": "__string", "locationName": "nextToken", "documentation": "\n            <p>Paginated results marker.</p>\n         "}, "SecretArnList": {"shape": "__listOf__string", "locationName": "secretArnList", "documentation": "\n            <p>The list of scram secrets associated with the cluster.</p>\n         "}}}, "ListTagsForResourceRequest": {"type": "structure", "members": {"ResourceArn": {"shape": "__string", "location": "uri", "locationName": "resourceArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) that uniquely identifies the resource that's associated with the tags.</p>\n         "}}, "required": ["ResourceArn"]}, "ListTagsForResourceResponse": {"type": "structure", "members": {"Tags": {"shape": "__mapOf__string", "locationName": "tags", "documentation": "\n            <p>The key-value pair for the resource tag.</p>\n         "}}}, "ListClientVpcConnectionsRequest": {"type": "structure", "members": {"ClusterArn": {"shape": "__string", "location": "uri", "locationName": "clusterArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the cluster.</p>\n         "}, "MaxResults": {"shape": "MaxResults", "location": "querystring", "locationName": "maxResults", "documentation": "\n            <p>The maximum number of results to return in the response. If there are more results, the response includes a NextToken parameter.</p>\n         "}, "NextToken": {"shape": "__string", "location": "querystring", "locationName": "nextToken", "documentation": "\n            <p>The paginated results marker. When the result of the operation is truncated, the call returns NextToken in the response. \n            To get the next batch, provide this token in your next request.</p>\n         "}}, "required": ["ClusterArn"]}, "ListClientVpcConnectionsResponse": {"type": "structure", "members": {"ClientVpcConnections": {"shape": "__listOfClientVpcConnection", "locationName": "clientVpcConnections", "documentation": "\n            <p>List of client VPC connections.</p>\n         "}, "NextToken": {"shape": "__string", "locationName": "nextToken", "documentation": "\n            <p>The paginated results marker. When the result of a ListClientVpcConnections operation is truncated, the call returns NextToken in the response. \n               To get another batch of configurations, provide this token in your next request.</p>\n         "}}}, "ListVpcConnectionsRequest": {"type": "structure", "members": {"MaxResults": {"shape": "MaxResults", "location": "querystring", "locationName": "maxResults", "documentation": "\n            <p>The maximum number of results to return in the response. If there are more results, the response includes a NextToken parameter.</p>\n         "}, "NextToken": {"shape": "__string", "location": "querystring", "locationName": "nextToken", "documentation": "\n            <p>The paginated results marker. When the result of the operation is truncated, the call returns NextToken in the response. \n            To get the next batch, provide this token in your next request.</p>\n         "}}}, "ListVpcConnectionsResponse": {"type": "structure", "members": {"VpcConnections": {"shape": "__listOfVpcConnection", "locationName": "vpcConnections", "documentation": "\n            <p>List of VPC connections.</p>\n         "}, "NextToken": {"shape": "__string", "locationName": "nextToken", "documentation": "\n            <p>The paginated results marker. When the result of a ListClientVpcConnections operation is truncated, the call returns NextToken in the response. \n               To get another batch of configurations, provide this token in your next request.</p>\n         "}}}, "RejectClientVpcConnectionRequest": {"type": "structure", "members": {"ClusterArn": {"shape": "__string", "location": "uri", "locationName": "clusterArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the cluster.</p>\n         "}, "VpcConnectionArn": {"shape": "__string", "locationName": "vpcConnectionArn", "documentation": "\n            <p>The VPC connection ARN.</p>\n         "}}, "required": ["VpcConnectionArn", "ClusterArn"]}, "RejectClientVpcConnectionResponse": {"type": "structure", "members": {}}, "MaxResults": {"type": "integer", "min": 1, "max": 100}, "LoggingInfo": {"type": "structure", "members": {"BrokerLogs": {"shape": "Broker<PERSON><PERSON>s", "locationName": "brokerLogs"}}, "required": ["Broker<PERSON><PERSON>s"]}, "MutableClusterInfo": {"type": "structure", "members": {"BrokerEBSVolumeInfo": {"shape": "__listOfBrokerEBSVolumeInfo", "locationName": "brokerEBSVolumeInfo", "documentation": "\n            <p>Specifies the size of the EBS volume and the ID of the associated broker.</p>\n         "}, "ConfigurationInfo": {"shape": "ConfigurationInfo", "locationName": "configurationInfo", "documentation": "\n            <p>Information about the changes in the configuration of the brokers.</p>\n         "}, "NumberOfBrokerNodes": {"shape": "__integer", "locationName": "numberOfBrokerNodes", "documentation": "\n            <p>The number of broker nodes in the cluster.</p>\n         "}, "EnhancedMonitoring": {"shape": "EnhancedMonitoring", "locationName": "enhancedMonitoring", "documentation": "\n            <p>Specifies which Apache Kafka metrics Amazon MSK gathers and sends to Amazon CloudWatch for this cluster.</p>\n         "}, "OpenMonitoring": {"shape": "OpenMonitoring", "locationName": "openMonitoring", "documentation": "\n            <p>The settings for open monitoring.</p>\n         "}, "KafkaVersion": {"shape": "__string", "locationName": "kafkaVersion", "documentation": "\n            <p>The Apache Kafka version.</p>\n            "}, "LoggingInfo": {"shape": "LoggingInfo", "locationName": "loggingInfo", "documentation": "\n            <p>You can configure your MSK cluster to send broker logs to different destination types. This is a container for the configuration details related to broker logs.</p>\n            "}, "InstanceType": {"shape": "__stringMin5Max32", "locationName": "instanceType", "documentation": "\n            <p>Information about the Amazon MSK broker type.</p>\n            "}, "ClientAuthentication": {"shape": "ClientAuthentication", "locationName": "clientAuthentication", "documentation": "\n            <p>Includes all client authentication information.</p>\n         "}, "EncryptionInfo": {"shape": "EncryptionInfo", "locationName": "encryptionInfo", "documentation": "\n            <p>Includes all encryption-related information.</p>\n         "}, "ConnectivityInfo": {"shape": "ConnectivityInfo", "locationName": "connectivityInfo", "documentation": "\n            <p>Information about the broker access configuration.</p>\n         "}, "StorageMode": {"shape": "StorageMode", "locationName": "storageMode", "documentation": "\n            <p>This controls storage mode for supported storage tiers.</p>\n         "}, "BrokerCountUpdateInfo": {"shape": "BrokerCountUpdateInfo", "locationName": "brokerCountUpdateInfo", "documentation": "\n            <p>Describes brokers being changed during a broker count update.</p>\n         "}}, "documentation": "\n            <p>Information about cluster attributes that can be updated via update APIs.</p>\n         "}, "NodeExporter": {"type": "structure", "members": {"EnabledInBroker": {"shape": "__boolean", "locationName": "enabledInBroker", "documentation": "\n            <p>Indicates whether you want to turn on or turn off the Node Exporter.</p>\n         "}}, "documentation": "\n            <p>Indicates whether you want to turn on or turn off the Node Exporter.</p>\n         ", "required": ["EnabledInBroker"]}, "NodeExporterInfo": {"type": "structure", "members": {"EnabledInBroker": {"shape": "__boolean", "locationName": "enabledInBroker", "documentation": "\n            <p>Indicates whether you want to turn on or turn off the Node Exporter.</p>\n         "}}, "documentation": "\n            <p>Indicates whether you want to turn on or turn off the Node Exporter.</p>\n         ", "required": ["EnabledInBroker"]}, "JmxExporter": {"type": "structure", "members": {"EnabledInBroker": {"shape": "__boolean", "locationName": "enabledInBroker", "documentation": "\n            <p>Indicates whether you want to turn on or turn off the JMX Exporter.</p>\n         "}}, "documentation": "\n            <p>Indicates whether you want to turn on or turn off the JMX Exporter.</p>\n         ", "required": ["EnabledInBroker"]}, "JmxExporterInfo": {"type": "structure", "members": {"EnabledInBroker": {"shape": "__boolean", "locationName": "enabledInBroker", "documentation": "\n            <p>Indicates whether you want to turn on or turn off the JMX Exporter.</p>\n         "}}, "documentation": "\n            <p>Indicates whether you want to turn on or turn off the JMX Exporter.</p>\n         ", "required": ["EnabledInBroker"]}, "OpenMonitoring": {"type": "structure", "members": {"Prometheus": {"shape": "Prometheus", "locationName": "prometheus", "documentation": "\n            <p>Prometheus settings.</p>\n         "}}, "documentation": "\n            <p>JMX and Node monitoring for the MSK cluster.</p>\n         ", "required": ["Prometheus"]}, "OpenMonitoringInfo": {"type": "structure", "members": {"Prometheus": {"shape": "PrometheusInfo", "locationName": "prometheus", "documentation": "\n            <p>Prometheus settings.</p>\n         "}}, "documentation": "\n            <p>JMX and Node monitoring for the MSK cluster.</p>\n         ", "required": ["Prometheus"]}, "Prometheus": {"type": "structure", "members": {"JmxExporter": {"shape": "JmxExporter", "locationName": "jmxExporter", "documentation": "\n            <p>Indicates whether you want to turn on or turn off the JMX Exporter.</p>\n         "}, "NodeExporter": {"shape": "NodeExporter", "locationName": "nodeExporter", "documentation": "\n            <p>Indicates whether you want to turn on or turn off the Node Exporter.</p>\n         "}}, "documentation": "\n            <p>Prometheus settings.</p>\n         "}, "PrometheusInfo": {"type": "structure", "members": {"JmxExporter": {"shape": "JmxExporterInfo", "locationName": "jmxExporter", "documentation": "\n            <p>Indicates whether you want to turn on or turn off the JMX Exporter.</p>\n         "}, "NodeExporter": {"shape": "NodeExporterInfo", "locationName": "nodeExporter", "documentation": "\n            <p>Indicates whether you want to turn on or turn off the Node Exporter.</p>\n         "}}, "documentation": "\n            <p>Prometheus settings.</p>\n         "}, "ProvisionedThroughput": {"type": "structure", "members": {"Enabled": {"shape": "__boolean", "locationName": "enabled", "documentation": "\n            <p>Provisioned throughput is enabled or not.</p>\n         "}, "VolumeThroughput": {"shape": "__integer", "locationName": "volumeThroughput", "documentation": "\n            <p>Throughput value of the EBS volumes for the data drive on each kafka broker node in MiB per second.</p>\n         "}}, "documentation": "\n            <p>Contains information about provisioned throughput for EBS storage volumes attached to kafka broker nodes.</p>\n         "}, "PublicAccess": {"type": "structure", "members": {"Type": {"shape": "__string", "locationName": "type", "documentation": "\n            <p>The value DISABLED indicates that public access is turned off. SERVICE_PROVIDED_EIPS indicates that public access is turned on.</p>\n         "}}, "documentation": "Public access control for brokers."}, "PutClusterPolicyRequest": {"type": "structure", "members": {"ClusterArn": {"shape": "__string", "location": "uri", "locationName": "clusterArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the cluster.</p>\n         "}, "CurrentVersion": {"shape": "__string", "locationName": "currentVersion", "documentation": "\n            <p>The policy version.</p>\n         "}, "Policy": {"shape": "__string", "locationName": "policy", "documentation": "\n            <p>The policy.</p>\n         "}}, "required": ["ClusterArn", "Policy"]}, "PutClusterPolicyResponse": {"type": "structure", "members": {"CurrentVersion": {"shape": "__string", "locationName": "currentVersion", "documentation": "\n            <p>The policy version.</p>\n         "}}}, "RebootBrokerRequest": {"type": "structure", "members": {"BrokerIds": {"shape": "__listOf__string", "locationName": "brokerIds", "documentation": "\n            <p>The list of broker IDs to be rebooted. The reboot-broker operation supports rebooting one broker at a time.</p>\n         "}, "ClusterArn": {"shape": "__string", "location": "uri", "locationName": "clusterArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the cluster to be updated.</p>\n         "}}, "documentation": "Reboots a node.", "required": ["ClusterArn", "BrokerIds"]}, "RebootBrokerResponse": {"type": "structure", "members": {"ClusterArn": {"shape": "__string", "locationName": "clusterArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the cluster.</p>\n         "}, "ClusterOperationArn": {"shape": "__string", "locationName": "clusterOperationArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the cluster operation.</p>\n         "}}}, "S3": {"type": "structure", "members": {"Bucket": {"shape": "__string", "locationName": "bucket"}, "Enabled": {"shape": "__boolean", "locationName": "enabled"}, "Prefix": {"shape": "__string", "locationName": "prefix"}}, "required": ["Enabled"]}, "ServerlessSasl": {"type": "structure", "members": {"Iam": {"shape": "<PERSON>am", "locationName": "iam", "documentation": "\n            <p>Indicates whether IAM access control is enabled.</p>\n         "}}, "documentation": "\n            <p>Details for client authentication using SASL.</p>\n         "}, "Sasl": {"type": "structure", "members": {"Scram": {"shape": "<PERSON><PERSON>", "locationName": "scram", "documentation": "\n            <p>Details for SASL/SCRAM client authentication.</p>\n         "}, "Iam": {"shape": "<PERSON>am", "locationName": "iam", "documentation": "\n            <p>Indicates whether IAM access control is enabled.</p>\n         "}}, "documentation": "\n            <p>Details for client authentication using SASL.</p>\n         "}, "VpcConnectivitySasl": {"type": "structure", "members": {"Scram": {"shape": "VpcConnectivityScram", "locationName": "scram", "documentation": "\n            <p>Details for SASL/SCRAM client authentication for VPC connectivity.</p>\n         "}, "Iam": {"shape": "VpcConnectivityIam", "locationName": "iam", "documentation": "\n            <p>Details for SASL/IAM client authentication for VPC connectivity.</p>\n         "}}, "documentation": "\n            <p>Details for SASL client authentication for VPC connectivity.</p>\n         "}, "Scram": {"type": "structure", "members": {"Enabled": {"shape": "__boolean", "locationName": "enabled", "documentation": "\n            <p>SASL/SCRAM authentication is enabled or not.</p>\n         "}}, "documentation": "\n            <p>Details for SASL/SCRAM client authentication.</p>\n         "}, "VpcConnectivityScram": {"type": "structure", "members": {"Enabled": {"shape": "__boolean", "locationName": "enabled", "documentation": "\n            <p>SASL/SCRAM authentication is on or off for VPC connectivity.</p>\n         "}}, "documentation": "\n            <p>Details for SASL/SCRAM client authentication for VPC connectivity.</p>\n         "}, "Iam": {"type": "structure", "members": {"Enabled": {"shape": "__boolean", "locationName": "enabled", "documentation": "\n            <p>Indicates whether IAM access control is enabled.</p>\n         "}}, "documentation": "\n            <p>Details for IAM access control.</p>\n         "}, "VpcConnectivityIam": {"type": "structure", "members": {"Enabled": {"shape": "__boolean", "locationName": "enabled", "documentation": "\n            <p>SASL/IAM authentication is on or off for VPC connectivity.</p>\n         "}}, "documentation": "\n            <p>Details for IAM access control for VPC connectivity.</p>\n         "}, "NodeInfo": {"type": "structure", "members": {"AddedToClusterTime": {"shape": "__string", "locationName": "addedToClusterTime", "documentation": "\n            <p>The start time.</p>\n         "}, "BrokerNodeInfo": {"shape": "BrokerNodeInfo", "locationName": "brokerNodeInfo", "documentation": "\n            <p>The broker node info.</p>\n         "}, "ControllerNodeInfo": {"shape": "ControllerNodeInfo", "locationName": "controllerNodeInfo", "documentation": "\n            <p>The ControllerNodeInfo.</p>\n         "}, "InstanceType": {"shape": "__string", "locationName": "instanceType", "documentation": "\n            <p>The instance type.</p>\n         "}, "NodeARN": {"shape": "__string", "locationName": "nodeARN", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the node.</p>\n         "}, "NodeType": {"shape": "NodeType", "locationName": "nodeType", "documentation": "\n            <p>The node type.</p>\n         "}, "ZookeeperNodeInfo": {"shape": "ZookeeperNodeInfo", "locationName": "zookeeperNodeInfo", "documentation": "\n            <p>The ZookeeperNodeInfo.</p>\n         "}}, "documentation": "\n            <p>The node information object.</p>\n         "}, "NodeType": {"type": "string", "documentation": "\n            <p>The broker or Zookeeper node.</p>\n         ", "enum": ["BROKER"]}, "NotFoundException": {"type": "structure", "members": {"InvalidParameter": {"shape": "__string", "locationName": "invalidParameter", "documentation": "\n            <p>The parameter that caused the error.</p>\n         "}, "Message": {"shape": "__string", "locationName": "message", "documentation": "\n            <p>The description of the error.</p>\n         "}}, "documentation": "\n            <p>Returns information about an error.</p>\n         ", "exception": true, "error": {"httpStatusCode": 404}}, "ReplicationInfo": {"type": "structure", "members": {"ConsumerGroupReplication": {"shape": "ConsumerGroupReplication", "locationName": "consumerGroupReplication", "documentation": "<p>Configuration relating to consumer group replication.</p>"}, "SourceKafkaClusterArn": {"shape": "__string", "locationName": "sourceKafkaClusterArn", "documentation": "<p>The ARN of the source Kafka cluster.</p>"}, "TargetCompressionType": {"shape": "TargetCompressionType", "locationName": "targetCompressionType", "documentation": "<p>The compression type to use when producing records to target cluster.</p>"}, "TargetKafkaClusterArn": {"shape": "__string", "locationName": "targetKafkaClusterArn", "documentation": "<p>The ARN of the target Kafka cluster.</p>"}, "TopicReplication": {"shape": "TopicReplication", "locationName": "topicReplication", "documentation": "<p>Configuration relating to topic replication.</p>"}}, "documentation": "<p>Specifies configuration for replication between a source and target Kafka cluster.</p>", "required": ["TargetCompressionType", "TopicReplication", "ConsumerGroupReplication", "SourceKafkaClusterArn", "TargetKafkaClusterArn"]}, "ReplicationInfoDescription": {"type": "structure", "members": {"ConsumerGroupReplication": {"shape": "ConsumerGroupReplication", "locationName": "consumerGroupReplication", "documentation": "<p>Configuration relating to consumer group replication.</p>"}, "SourceKafkaClusterAlias": {"shape": "__string", "locationName": "sourceKafkaClusterAlias", "documentation": "<p>The alias of the source Kafka cluster.</p>"}, "TargetCompressionType": {"shape": "TargetCompressionType", "locationName": "targetCompressionType", "documentation": "<p>The compression type to use when producing records to target cluster.</p>"}, "TargetKafkaClusterAlias": {"shape": "__string", "locationName": "targetKafkaClusterAlias", "documentation": "<p>The alias of the target Kafka cluster.</p>"}, "TopicReplication": {"shape": "TopicReplication", "locationName": "topicReplication", "documentation": "<p>Configuration relating to topic replication.</p>"}}, "documentation": "<p>Specifies configuration for replication between a source and target Kafka cluster (sourceKafkaClusterAlias -> targetKafkaClusterAlias)</p>"}, "ReplicationInfoSummary": {"type": "structure", "members": {"SourceKafkaClusterAlias": {"shape": "__string", "locationName": "sourceKafkaClusterAlias", "documentation": "<p>The alias of the source Kafka cluster.</p>"}, "TargetKafkaClusterAlias": {"shape": "__string", "locationName": "targetKafkaClusterAlias", "documentation": "<p>The alias of the target Kafka cluster.</p>"}}, "documentation": "<p>Summarized information of replication between clusters.</p>"}, "ReplicationStartingPosition": {"type": "structure", "members": {"Type": {"shape": "ReplicationStartingPositionType", "locationName": "type", "documentation": "<p>The type of replication starting position.</p>"}}, "documentation": "<p>Configuration for specifying the position in the topics to start replicating from.</p>"}, "ReplicationStartingPositionType": {"type": "string", "enum": ["LATEST", "EARLIEST"], "documentation": "<p>The type of replication starting position.</p>"}, "ReplicationTopicNameConfiguration": {"type": "structure", "members": {"Type": {"shape": "ReplicationTopicNameConfigurationType", "locationName": "type", "documentation": "<p>The type of replicated topic name.</p>"}}, "documentation": "<p>Configuration for specifying replicated topic names should be the same as their corresponding upstream topics or prefixed with source cluster alias.</p>"}, "ReplicationTopicNameConfigurationType": {"type": "string", "enum": ["PREFIXED_WITH_SOURCE_CLUSTER_ALIAS", "IDENTICAL"], "documentation": "<p>The type of replicated topic name.</p>"}, "ReplicationStateInfo": {"type": "structure", "members": {"Code": {"shape": "__string", "locationName": "code", "documentation": "Code that describes the current state of the replicator."}, "Message": {"shape": "__string", "locationName": "message", "documentation": "Message that describes the state of the replicator."}}, "documentation": "Details about the state of a replicator"}, "ReplicatorState": {"type": "string", "documentation": "<p>The state of a replicator.</p>", "enum": ["RUNNING", "CREATING", "UPDATING", "DELETING", "FAILED"]}, "ReplicatorSummary": {"type": "structure", "members": {"CreationTime": {"shape": "__timestampIso8601", "locationName": "creationTime", "documentation": "<p>The time the replicator was created.</p>"}, "CurrentVersion": {"shape": "__string", "locationName": "currentVersion", "documentation": "<p>The current version of the replicator.</p>"}, "IsReplicatorReference": {"shape": "__boolean", "locationName": "isReplicatorReference", "documentation": "<p>Whether this resource is a replicator reference.</p>"}, "KafkaClustersSummary": {"shape": "__listOfKafkaClusterSummary", "locationName": "kafkaClustersSummary", "documentation": "<p>Kafka Clusters used in setting up sources / targets for replication.</p>"}, "ReplicationInfoSummaryList": {"shape": "__listOfReplicationInfoSummary", "locationName": "replicationInfoSummaryList", "documentation": "<p>A list of summarized information of replications between clusters.</p>"}, "ReplicatorArn": {"shape": "__string", "locationName": "replicatorArn", "documentation": "<p>The Amazon Resource Name (ARN) of the replicator.</p>"}, "ReplicatorName": {"shape": "__string", "locationName": "replicatorName", "documentation": "<p>The name of the replicator.</p>"}, "ReplicatorResourceArn": {"shape": "__string", "locationName": "replicatorResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the replicator resource in the region where the replicator was created.</p>"}, "ReplicatorState": {"shape": "ReplicatorState", "locationName": "replicatorState", "documentation": "<p>State of the replicator.</p>"}}, "documentation": "<p>Information about a replicator.</p>"}, "ServiceUnavailableException": {"type": "structure", "members": {"InvalidParameter": {"shape": "__string", "locationName": "invalidParameter", "documentation": "\n            <p>The parameter that caused the error.</p>\n         "}, "Message": {"shape": "__string", "locationName": "message", "documentation": "\n            <p>The description of the error.</p>\n         "}}, "documentation": "\n            <p>Returns information about an error.</p>\n         ", "exception": true, "error": {"httpStatusCode": 503}}, "StateInfo": {"type": "structure", "members": {"Code": {"shape": "__string", "locationName": "code"}, "Message": {"shape": "__string", "locationName": "message"}}}, "StorageInfo": {"type": "structure", "members": {"EbsStorageInfo": {"shape": "EBSStorageInfo", "locationName": "ebsStorageInfo", "documentation": "\n            <p>EBS volume information.</p>\n         "}}, "documentation": "\n            <p>Contains information about storage volumes attached to MSK broker nodes.</p>\n         "}, "StorageMode": {"type": "string", "documentation": "Controls storage mode for various supported storage tiers.", "enum": ["LOCAL", "TIERED"]}, "TagResourceRequest": {"type": "structure", "members": {"ResourceArn": {"shape": "__string", "location": "uri", "locationName": "resourceArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) that uniquely identifies the resource that's associated with the tags.</p>\n         "}, "Tags": {"shape": "__mapOf__string", "locationName": "tags", "documentation": "\n            <p>The key-value pair for the resource tag.</p>\n         "}}, "required": ["ResourceArn", "Tags"]}, "TargetCompressionType": {"type": "string", "documentation": "<p>The type of compression to use producing records to the target cluster.</p>", "enum": ["NONE", "GZIP", "SNAPPY", "LZ4", "ZSTD"]}, "Tls": {"type": "structure", "members": {"CertificateAuthorityArnList": {"shape": "__listOf__string", "locationName": "certificateAuthorityArnList", "documentation": "\n            <p>List of ACM Certificate Authority ARNs.</p>\n         "}, "Enabled": {"shape": "__boolean", "locationName": "enabled", "documentation": "\n            <p>Specifies whether you want to turn on or turn off TLS authentication.</p>\n         "}}, "documentation": "\n            <p>Details for client authentication using TLS.</p>\n         "}, "VpcConnectivityTls": {"type": "structure", "members": {"Enabled": {"shape": "__boolean", "locationName": "enabled", "documentation": "\n            <p>TLS authentication is on or off for VPC connectivity.</p>\n         "}}, "documentation": "\n            <p>Details for TLS client authentication for VPC connectivity.</p>\n         "}, "TooManyRequestsException": {"type": "structure", "members": {"InvalidParameter": {"shape": "__string", "locationName": "invalidParameter", "documentation": "\n            <p>The parameter that caused the error.</p>\n         "}, "Message": {"shape": "__string", "locationName": "message", "documentation": "\n            <p>The description of the error.</p>\n         "}}, "documentation": "\n            <p>Returns information about an error.</p>\n         ", "exception": true, "error": {"httpStatusCode": 429}}, "TopicReplication": {"type": "structure", "members": {"CopyAccessControlListsForTopics": {"shape": "__boolean", "locationName": "copyAccessControlListsForTopics", "documentation": "<p>Whether to periodically configure remote topic ACLs to match their corresponding upstream topics.</p>"}, "CopyTopicConfigurations": {"shape": "__boolean", "locationName": "copyTopicConfigurations", "documentation": "<p>Whether to periodically configure remote topics to match their corresponding upstream topics.</p>"}, "DetectAndCopyNewTopics": {"shape": "__boolean", "locationName": "detectAndCopyNewTopics", "documentation": "<p>Whether to periodically check for new topics and partitions.</p>"}, "StartingPosition": {"shape": "ReplicationStartingPosition", "locationName": "startingPosition", "documentation": "<p>Configuration for specifying the position in the topics to start replicating from.</p>"}, "TopicNameConfiguration": {"shape": "ReplicationTopicNameConfiguration", "locationName": "topicNameConfiguration", "documentation": "<p>Configuration for specifying replicated topic names should be the same as their corresponding upstream topics or prefixed with source cluster alias.</p>"}, "TopicsToExclude": {"shape": "__listOf__stringMax249", "locationName": "topicsToExclude", "documentation": "<p>List of regular expression patterns indicating the topics that should not be replicated.</p>"}, "TopicsToReplicate": {"shape": "__listOf__stringMax249", "locationName": "topicsToReplicate", "documentation": "<p>List of regular expression patterns indicating the topics to copy.</p>"}}, "documentation": "<p>Details about topic replication.</p>", "required": ["TopicsToReplicate"]}, "TopicReplicationUpdate": {"type": "structure", "members": {"CopyAccessControlListsForTopics": {"shape": "__boolean", "locationName": "copyAccessControlListsForTopics", "documentation": "<p>Whether to periodically configure remote topic ACLs to match their corresponding upstream topics.</p>"}, "CopyTopicConfigurations": {"shape": "__boolean", "locationName": "copyTopicConfigurations", "documentation": "<p>Whether to periodically configure remote topics to match their corresponding upstream topics.</p>"}, "DetectAndCopyNewTopics": {"shape": "__boolean", "locationName": "detectAndCopyNewTopics", "documentation": "<p>Whether to periodically check for new topics and partitions.</p>"}, "TopicsToExclude": {"shape": "__listOf__stringMax249", "locationName": "topicsToExclude", "documentation": "<p>List of regular expression patterns indicating the topics that should not be replicated.</p>"}, "TopicsToReplicate": {"shape": "__listOf__stringMax249", "locationName": "topicsToReplicate", "documentation": "<p>List of regular expression patterns indicating the topics to copy.</p>"}}, "documentation": "<p>Details for updating the topic replication of a replicator.</p>", "required": ["TopicsToReplicate", "TopicsToExclude", "CopyTopicConfigurations", "DetectAndCopyNewTopics", "CopyAccessControlListsForTopics"]}, "Unauthenticated": {"type": "structure", "members": {"Enabled": {"shape": "__boolean", "locationName": "enabled", "documentation": "\n            <p>Specifies whether you want to turn on or turn off unauthenticated traffic to your cluster.</p>\n         "}}}, "UnauthorizedException": {"type": "structure", "members": {"InvalidParameter": {"shape": "__string", "locationName": "invalidParameter", "documentation": "\n            <p>The parameter that caused the error.</p>\n         "}, "Message": {"shape": "__string", "locationName": "message", "documentation": "\n            <p>The description of the error.</p>\n         "}}, "documentation": "\n            <p>Returns information about an error.</p>\n         ", "exception": true, "error": {"httpStatusCode": 401}}, "UnprocessedScramSecret": {"type": "structure", "members": {"ErrorCode": {"shape": "__string", "locationName": "errorCode", "documentation": "\n            <p>Error code for associate/disassociate failure.</p>\n         "}, "ErrorMessage": {"shape": "__string", "locationName": "errorMessage", "documentation": "\n            <p>Error message for associate/disassociate failure.</p>\n         "}, "SecretArn": {"shape": "__string", "locationName": "secretArn", "documentation": "\n            <p>AWS Secrets Manager secret ARN.</p>\n         "}}, "documentation": "\n            <p>Error info for scram secret associate/disassociate failure.</p>\n         "}, "UntagResourceRequest": {"type": "structure", "members": {"ResourceArn": {"shape": "__string", "location": "uri", "locationName": "resourceArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) that uniquely identifies the resource that's associated with the tags.</p>\n         "}, "TagKeys": {"shape": "__listOf__string", "location": "querystring", "locationName": "tagKeys", "documentation": "\n            <p>Tag keys must be unique for a given cluster. In addition, the following restrictions apply:</p>\n            <ul>\n               <li>\n                  <p>Each tag key must be unique. If you add a tag with a key that's already in\n                  use, your new tag overwrites the existing key-value pair. </p>\n               </li>\n               <li>\n                  <p>You can't start a tag key with aws: because this prefix is reserved for use\n                  by  AWS.  AWS creates tags that begin with this prefix on your behalf, but\n                  you can't edit or delete them.</p>\n               </li>\n               <li>\n                  <p>Tag keys must be between 1 and 128 Unicode characters in length.</p>\n               </li>\n               <li>\n                  <p>Tag keys must consist of the following characters: Unicode letters, digits,\n                  white space, and the following special characters: _ . / = + -\n                     @.</p>\n               </li>\n            </ul>\n         "}}, "required": ["TagKeys", "ResourceArn"]}, "UpdateBrokerCountRequest": {"type": "structure", "members": {"ClusterArn": {"shape": "__string", "location": "uri", "locationName": "clusterArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) that uniquely identifies the cluster.</p>\n         "}, "CurrentVersion": {"shape": "__string", "locationName": "currentVersion", "documentation": "\n            <p>The version of cluster to update from. A successful operation will then generate a new version.</p>\n         "}, "TargetNumberOfBrokerNodes": {"shape": "__integerMin1Max15", "locationName": "targetNumberOfBrokerNodes", "documentation": "\n            <p>The number of broker nodes that you want the cluster to have after this operation completes successfully.</p>\n         "}}, "required": ["ClusterArn", "CurrentVersion", "TargetNumberOfBrokerNodes"]}, "UpdateBrokerCountResponse": {"type": "structure", "members": {"ClusterArn": {"shape": "__string", "locationName": "clusterArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the cluster.</p>\n         "}, "ClusterOperationArn": {"shape": "__string", "locationName": "clusterOperationArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the cluster operation.</p>\n         "}}}, "UpdateBrokerTypeRequest": {"type": "structure", "members": {"ClusterArn": {"shape": "__string", "location": "uri", "locationName": "clusterArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) that uniquely identifies the cluster.</p>\n         "}, "CurrentVersion": {"shape": "__string", "locationName": "currentVersion", "documentation": "\n            <p>The cluster version that you want to change. After this operation completes successfully, the cluster will have a new version.</p>\n         "}, "TargetInstanceType": {"shape": "__string", "locationName": "targetInstanceType", "documentation": "\n            <p>The Amazon MSK broker type that you want all of the brokers in this cluster to be.</p>\n         "}}, "required": ["ClusterArn", "CurrentVersion", "TargetInstanceType"]}, "UpdateBrokerTypeResponse": {"type": "structure", "members": {"ClusterArn": {"shape": "__string", "locationName": "clusterArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the cluster.</p>\n         "}, "ClusterOperationArn": {"shape": "__string", "locationName": "clusterOperationArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the cluster operation.</p>\n         "}}}, "UpdateBrokerStorageRequest": {"type": "structure", "members": {"ClusterArn": {"shape": "__string", "location": "uri", "locationName": "clusterArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) that uniquely identifies the cluster.</p>\n         "}, "CurrentVersion": {"shape": "__string", "locationName": "currentVersion", "documentation": "\n            <p>The version of cluster to update from. A successful operation will then generate a new version.</p>\n         "}, "TargetBrokerEBSVolumeInfo": {"shape": "__listOfBrokerEBSVolumeInfo", "locationName": "targetBrokerEBSVolumeInfo", "documentation": "\n            <p>Describes the target volume size and the ID of the broker to apply the update to.</p>\n         "}}, "required": ["ClusterArn", "TargetBrokerEBSVolumeInfo", "CurrentVersion"]}, "UpdateBrokerStorageResponse": {"type": "structure", "members": {"ClusterArn": {"shape": "__string", "locationName": "clusterArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the cluster.</p>\n         "}, "ClusterOperationArn": {"shape": "__string", "locationName": "clusterOperationArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the cluster operation.</p>\n         "}}}, "UpdateClusterConfigurationRequest": {"type": "structure", "members": {"ClusterArn": {"shape": "__string", "location": "uri", "locationName": "clusterArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) that uniquely identifies the cluster.</p>\n         "}, "ConfigurationInfo": {"shape": "ConfigurationInfo", "locationName": "configurationInfo", "documentation": "\n            <p>Represents the configuration that you want MSK to use for the brokers in a cluster.</p>\n         "}, "CurrentVersion": {"shape": "__string", "locationName": "currentVersion", "documentation": "\n            <p>The version of the cluster that needs to be updated.</p>\n         "}}, "required": ["ClusterArn", "CurrentVersion", "ConfigurationInfo"]}, "UpdateClusterConfigurationResponse": {"type": "structure", "members": {"ClusterArn": {"shape": "__string", "locationName": "clusterArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the cluster.</p>\n         "}, "ClusterOperationArn": {"shape": "__string", "locationName": "clusterOperationArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the cluster operation.</p>\n         "}}}, "UpdateClusterKafkaVersionRequest": {"type": "structure", "members": {"ClusterArn": {"shape": "__string", "location": "uri", "locationName": "clusterArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the cluster to be updated.</p>\n            "}, "ConfigurationInfo": {"shape": "ConfigurationInfo", "locationName": "configurationInfo", "documentation": "\n            <p>The custom configuration that should be applied on the new version of cluster.</p>\n            "}, "CurrentVersion": {"shape": "__string", "locationName": "currentVersion", "documentation": "\n            <p>Current cluster version.</p>\n            "}, "TargetKafkaVersion": {"shape": "__string", "locationName": "targetKafkaVersion", "documentation": "\n            <p>Target Kafka version.</p>\n            "}}, "required": ["ClusterArn", "TargetKafkaVersion", "CurrentVersion"]}, "UpdateClusterKafkaVersionResponse": {"type": "structure", "members": {"ClusterArn": {"shape": "__string", "locationName": "clusterArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the cluster.</p>\n            "}, "ClusterOperationArn": {"shape": "__string", "locationName": "clusterOperationArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the cluster operation.</p>\n            "}}}, "UpdateMonitoringRequest": {"type": "structure", "members": {"ClusterArn": {"shape": "__string", "location": "uri", "locationName": "clusterArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) that uniquely identifies the cluster.</p>\n         "}, "CurrentVersion": {"shape": "__string", "locationName": "currentVersion", "documentation": "\n            <p>The version of the MSK cluster to update. Cluster versions aren't simple numbers. You can describe an MSK cluster to find its version. When this update operation is successful, it generates a new cluster version.</p>\n         "}, "EnhancedMonitoring": {"shape": "EnhancedMonitoring", "locationName": "enhancedMonitoring", "documentation": "\n            <p>Specifies which Apache Kafka metrics Amazon MSK gathers and sends to Amazon CloudWatch for this cluster.</p>\n         "}, "OpenMonitoring": {"shape": "OpenMonitoringInfo", "locationName": "openMonitoring", "documentation": "\n            <p>The settings for open monitoring.</p>\n         "}, "LoggingInfo": {"shape": "LoggingInfo", "locationName": "loggingInfo"}}, "documentation": "Request body for UpdateMonitoring.", "required": ["ClusterArn", "CurrentVersion"]}, "UpdateMonitoringResponse": {"type": "structure", "members": {"ClusterArn": {"shape": "__string", "locationName": "clusterArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the cluster.</p>\n         "}, "ClusterOperationArn": {"shape": "__string", "locationName": "clusterOperationArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the cluster operation.</p>\n         "}}}, "UpdateReplicationInfoRequest": {"type": "structure", "members": {"ConsumerGroupReplication": {"shape": "ConsumerGroupReplicationUpdate", "locationName": "consumerGroupReplication", "documentation": "<p>Updated consumer group replication information.</p>"}, "CurrentVersion": {"shape": "__string", "locationName": "currentVersion", "documentation": "<p>Current replicator version.</p>"}, "ReplicatorArn": {"shape": "__string", "location": "uri", "locationName": "replicatorArn", "documentation": "<p>The Amazon Resource Name (ARN) of the replicator to be updated.</p>"}, "SourceKafkaClusterArn": {"shape": "__string", "locationName": "sourceKafkaClusterArn", "documentation": "<p>The ARN of the source Kafka cluster.</p>"}, "TargetKafkaClusterArn": {"shape": "__string", "locationName": "targetKafkaClusterArn", "documentation": "<p>The ARN of the target Kafka cluster.</p>"}, "TopicReplication": {"shape": "TopicReplicationUpdate", "locationName": "topicReplication", "documentation": "<p>Updated topic replication information.</p>"}}, "documentation": "<p>Update information relating to replication between a given source and target Kafka cluster.</p>", "required": ["ReplicatorArn", "SourceKafkaClusterArn", "CurrentVersion", "TargetKafkaClusterArn"]}, "UpdateReplicationInfoResponse": {"type": "structure", "members": {"ReplicatorArn": {"shape": "__string", "locationName": "replicatorArn", "documentation": "<p>The Amazon Resource Name (ARN) of the replicator.</p>"}, "ReplicatorState": {"shape": "ReplicatorState", "locationName": "replicatorState", "documentation": "<p>State of the replicator.</p>"}}}, "UpdateSecurityRequest": {"type": "structure", "members": {"ClientAuthentication": {"shape": "ClientAuthentication", "locationName": "clientAuthentication", "documentation": "\n            <p>Includes all client authentication related information.</p>\n         "}, "ClusterArn": {"shape": "__string", "location": "uri", "locationName": "clusterArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) that uniquely identifies the cluster.</p>\n         "}, "CurrentVersion": {"shape": "__string", "locationName": "currentVersion", "documentation": "\n            <p>The version of the MSK cluster to update. Cluster versions aren't simple numbers. You can describe an MSK cluster to find its version. When this update operation is successful, it generates a new cluster version.</p>\n         "}, "EncryptionInfo": {"shape": "EncryptionInfo", "locationName": "encryptionInfo", "documentation": "\n            <p>Includes all encryption-related information.</p>\n         "}}, "required": ["ClusterArn", "CurrentVersion"]}, "UpdateSecurityResponse": {"type": "structure", "members": {"ClusterArn": {"shape": "__string", "locationName": "clusterArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the cluster.</p>\n         "}, "ClusterOperationArn": {"shape": "__string", "locationName": "clusterOperationArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the cluster operation.</p>\n         "}}}, "UpdateStorageRequest": {"type": "structure", "members": {"ClusterArn": {"shape": "__string", "location": "uri", "locationName": "clusterArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the cluster to be updated.</p>\n         "}, "CurrentVersion": {"shape": "__string", "locationName": "currentVersion", "documentation": "\n            <p>The version of cluster to update from. A successful operation will then generate a new version.</p>\n         "}, "ProvisionedThroughput": {"shape": "ProvisionedThroughput", "locationName": "provisionedThroughput", "documentation": "\n            <p>EBS volume provisioned throughput information.</p>\n         "}, "StorageMode": {"shape": "StorageMode", "locationName": "storageMode", "documentation": "\n            <p>Controls storage mode for supported storage tiers.</p>\n         "}, "VolumeSizeGB": {"shape": "__integer", "locationName": "volumeSizeGB", "documentation": "\n            <p>size of the EBS volume to update.</p>\n         "}}, "documentation": "\n            <p>Request object for UpdateStorage api. Its used to update the storage attributes for the cluster.</p>\n         ", "required": ["ClusterArn", "CurrentVersion"]}, "UpdateStorageResponse": {"type": "structure", "members": {"ClusterArn": {"shape": "__string", "locationName": "clusterArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the cluster.</p>\n         "}, "ClusterOperationArn": {"shape": "__string", "locationName": "clusterOperationArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the cluster operation.</p>\n         "}}}, "UpdateConfigurationRequest": {"type": "structure", "members": {"Arn": {"shape": "__string", "location": "uri", "locationName": "arn", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the configuration.</p>\n         "}, "Description": {"shape": "__string", "locationName": "description", "documentation": "\n            <p>The description of the configuration revision.</p>\n         "}, "ServerProperties": {"shape": "__blob", "locationName": "serverProperties", "documentation": "\n            <p>Contents of the <filename>server.properties</filename> file. When using the API, you must ensure that the contents of the file are base64 encoded. \n               When using the AWS Management Console, the SDK, or the AWS CLI, the contents of <filename>server.properties</filename> can be in plaintext.</p>\n         "}}, "required": ["<PERSON><PERSON>", "ServerProperties"]}, "UpdateConfigurationResponse": {"type": "structure", "members": {"Arn": {"shape": "__string", "locationName": "arn", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the configuration.</p>\n         "}, "LatestRevision": {"shape": "ConfigurationRevision", "locationName": "latestRevision", "documentation": "\n            <p>Latest revision of the configuration.</p>\n         "}}}, "UpdateConnectivityRequest": {"type": "structure", "members": {"ClusterArn": {"shape": "__string", "location": "uri", "locationName": "clusterArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the configuration.</p>\n         "}, "ConnectivityInfo": {"shape": "ConnectivityInfo", "locationName": "connectivityInfo", "documentation": "\n            <p>Information about the broker access configuration.</p>\n         "}, "CurrentVersion": {"shape": "__string", "locationName": "currentVersion", "documentation": "\n            <p>The version of the MSK cluster to update. Cluster versions aren't simple numbers. You can describe an MSK cluster to find its version. When this update operation is successful, it generates a new cluster version.</p>\n         "}}, "documentation": "Request body for UpdateConnectivity.", "required": ["ClusterArn", "ConnectivityInfo", "CurrentVersion"]}, "UpdateConnectivityResponse": {"type": "structure", "members": {"ClusterArn": {"shape": "__string", "locationName": "clusterArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the cluster.</p>\n         "}, "ClusterOperationArn": {"shape": "__string", "locationName": "clusterOperationArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the cluster operation.</p>\n         "}}}, "UserIdentity": {"type": "structure", "members": {"Type": {"shape": "UserIdentityType", "locationName": "type", "documentation": "\n            <p>The identity type of the requester that calls the API operation.</p>\n         "}, "PrincipalId": {"shape": "__string", "locationName": "principalId", "documentation": "\n            <p>A unique identifier for the requester that calls the API operation.</p>\n         "}}, "documentation": "\n            <p>Description of the requester that calls the API operation.</p>\n         "}, "UserIdentityType": {"type": "string", "documentation": "\n            <p>The identity type of the requester that calls the API operation.</p>\n         ", "enum": ["AWSACCOUNT", "AWSSERVICE"]}, "VpcConnectionInfo": {"type": "structure", "members": {"VpcConnectionArn": {"shape": "__string", "locationName": "vpcConnectionArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the VPC connection.</p>\n         "}, "Owner": {"shape": "__string", "locationName": "owner", "documentation": "\n            <p>The owner of the VPC Connection.</p>\n         "}, "UserIdentity": {"shape": "UserIdentity", "locationName": "userIdentity", "documentation": "\n            <p>Description of the requester that calls the API operation.</p>\n         "}, "CreationTime": {"shape": "__timestampIso8601", "locationName": "creationTime", "documentation": "\n            <p>The time when Amazon MSK creates the VPC Connnection.</p>\n         "}}, "documentation": "\n            <p>Description of the VPC connection.</p>\n         "}, "VpcConnectionInfoServerless": {"type": "structure", "members": {"CreationTime": {"shape": "__timestampIso8601", "locationName": "creationTime", "documentation": "\n            <p>The time when Amazon MSK creates the VPC Connnection.</p>"}, "Owner": {"shape": "__string", "locationName": "owner", "documentation": "\n            <p>The owner of the VPC Connection.</p>"}, "UserIdentity": {"shape": "UserIdentity", "locationName": "userIdentity", "documentation": "\n            <p>Description of the requester that calls the API operation.</p>"}, "VpcConnectionArn": {"shape": "__string", "locationName": "vpcConnectionArn", "documentation": "\n            <p>The Amazon Resource Name (ARN) of the VPC connection.</p>"}}, "documentation": "Description of the VPC connection."}, "VpcConnectionState": {"type": "string", "documentation": "\n            <p>The state of a VPC connection.</p>\n         ", "enum": ["CREATING", "AVAILABLE", "INACTIVE", "DEACTIVATING", "DELETING", "FAILED", "REJECTED", "REJECTING"]}, "VpcConnectivity": {"type": "structure", "members": {"ClientAuthentication": {"shape": "VpcConnectivityClientAuthentication", "locationName": "clientAuthentication", "documentation": "\n            <p>Includes all client authentication information for VPC connectivity.</p>\n         "}}, "documentation": "VPC connectivity access control for brokers."}, "ZookeeperNodeInfo": {"type": "structure", "members": {"AttachedENIId": {"shape": "__string", "locationName": "attachedENIId", "documentation": "\n            <p>The attached elastic network interface of the broker.</p>\n         "}, "ClientVpcIpAddress": {"shape": "__string", "locationName": "clientVpcIpAddress", "documentation": "\n            <p>The virtual private cloud (VPC) IP address of the client.</p>\n         "}, "Endpoints": {"shape": "__listOf__string", "locationName": "endpoints", "documentation": "\n            <p>Endpoints for accessing the ZooKeeper.</p>\n         "}, "ZookeeperId": {"shape": "__double", "locationName": "zookeeper<PERSON>d", "documentation": "\n            <p>The role-specific ID for Zookeeper.</p>\n         "}, "ZookeeperVersion": {"shape": "__string", "locationName": "zookeeper<PERSON><PERSON><PERSON>", "documentation": "\n            <p>The version of Zookeeper.</p>\n         "}}, "documentation": "\n            <p>Zookeeper node information.</p>\n         "}, "__boolean": {"type": "boolean"}, "__blob": {"type": "blob"}, "__double": {"type": "double"}, "__integer": {"type": "integer"}, "__integerMin1Max15": {"type": "integer", "min": 1, "max": 15}, "__integerMin1Max16384": {"type": "integer", "min": 1, "max": 16384}, "__listOfBrokerEBSVolumeInfo": {"type": "list", "member": {"shape": "BrokerEBSVolumeInfo"}}, "__listOfClusterInfo": {"type": "list", "member": {"shape": "ClusterInfo"}}, "__listOfCluster": {"type": "list", "member": {"shape": "Cluster"}}, "__listOfClusterOperationInfo": {"type": "list", "member": {"shape": "ClusterOperationInfo"}}, "__listOfClusterOperationV2Summary": {"type": "list", "member": {"shape": "ClusterOperationV2Summary"}}, "__listOfClusterOperationStep": {"type": "list", "member": {"shape": "ClusterOperationStep"}}, "__listOfCompatibleKafkaVersion": {"type": "list", "member": {"shape": "CompatibleKafkaVersion"}}, "__listOfVpcConfig": {"type": "list", "member": {"shape": "VpcConfig"}}, "__listOfConfiguration": {"type": "list", "member": {"shape": "Configuration"}}, "__listOfConfigurationRevision": {"type": "list", "member": {"shape": "ConfigurationRevision"}}, "__listOfKafkaVersion": {"type": "list", "member": {"shape": "KafkaVersion"}}, "__listOfKafkaCluster": {"type": "list", "member": {"shape": "KafkaCluster"}}, "__listOfKafkaClusterDescription": {"type": "list", "member": {"shape": "KafkaClusterDescription"}}, "__listOfKafkaClusterSummary": {"type": "list", "member": {"shape": "KafkaClusterSummary"}}, "__listOfNodeInfo": {"type": "list", "member": {"shape": "NodeInfo"}}, "__listOfClientVpcConnection": {"type": "list", "member": {"shape": "ClientVpcConnection"}}, "__listOfReplicationInfo": {"type": "list", "member": {"shape": "ReplicationInfo"}}, "__listOfReplicationInfoDescription": {"type": "list", "member": {"shape": "ReplicationInfoDescription"}}, "__listOfReplicationInfoSummary": {"type": "list", "member": {"shape": "ReplicationInfoSummary"}}, "__listOfReplicatorSummary": {"type": "list", "member": {"shape": "ReplicatorSummary"}}, "__listOfVpcConnection": {"type": "list", "member": {"shape": "VpcConnection"}}, "__listOfUnprocessedScramSecret": {"type": "list", "member": {"shape": "UnprocessedScramSecret"}}, "__listOf__double": {"type": "list", "member": {"shape": "__double"}}, "__listOf__string": {"type": "list", "member": {"shape": "__string"}}, "__long": {"type": "long"}, "__mapOf__string": {"type": "map", "key": {"shape": "__string"}, "value": {"shape": "__string"}}, "__listOf__stringMax249": {"type": "list", "member": {"shape": "__stringMax249"}}, "__listOf__stringMax256": {"type": "list", "member": {"shape": "__stringMax256"}}, "__string": {"type": "string"}, "__stringMax1024": {"type": "string", "max": 1024}, "__stringMax249": {"type": "string", "max": 249}, "__stringMax256": {"type": "string", "max": 256}, "__stringMin1Max128": {"type": "string", "min": 1, "max": 128}, "__stringMin1Max64": {"type": "string", "min": 1, "max": 64}, "__stringMin5Max32": {"type": "string", "min": 5, "max": 32}, "__stringMin1Max128Pattern09AZaZ09AZaZ0": {"type": "string", "min": 1, "max": 128, "pattern": "^[0-9A-Za-z][0-9A-Za-z-]{0,}$"}, "__timestampIso8601": {"type": "timestamp", "timestampFormat": "iso8601"}, "__timestampUnix": {"type": "timestamp", "timestampFormat": "unixTimestamp"}}, "documentation": "\n               <p>The operations for managing an Amazon MSK cluster.</p>\n            "}