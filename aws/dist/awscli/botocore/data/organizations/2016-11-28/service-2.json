{"version": "2.0", "metadata": {"apiVersion": "2016-11-28", "endpointPrefix": "organizations", "jsonVersion": "1.1", "protocol": "json", "protocols": ["json"], "serviceAbbreviation": "Organizations", "serviceFullName": "AWS Organizations", "serviceId": "Organizations", "signatureVersion": "v4", "targetPrefix": "AWSOrganizationsV20161128", "uid": "organizations-2016-11-28", "auth": ["aws.auth#sigv4"]}, "operations": {"AcceptHandshake": {"name": "AcceptHandshake", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AcceptHandshakeRequest"}, "output": {"shape": "AcceptHandshakeResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "HandshakeConstraintViolationException"}, {"shape": "HandshakeNotFoundException"}, {"shape": "InvalidHandshakeTransitionException"}, {"shape": "HandshakeAlreadyInStateException"}, {"shape": "InvalidInputException"}, {"shape": "ConcurrentModificationException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}, {"shape": "AccessDeniedForDependencyException"}], "documentation": "<p>Sends a response to the originator of a handshake agreeing to the action proposed by the handshake request.</p> <p>You can only call this operation by the following principals when they also have the relevant IAM permissions:</p> <ul> <li> <p> <b>Invitation to join</b> or <b>Approve all features request</b> handshakes: only a principal from the member account.</p> <p>The user who calls the API for an invitation to join must have the <code>organizations:AcceptHandshake</code> permission. If you enabled all features in the organization, the user must also have the <code>iam:CreateServiceLinkedRole</code> permission so that Organizations can create the required service-linked role named <code>AWSServiceRoleForOrganizations</code>. For more information, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_integration_services.html#orgs_integrate_services-using_slrs\">Organizations and service-linked roles</a> in the <i>Organizations User Guide</i>.</p> </li> <li> <p> <b>Enable all features final confirmation</b> handshake: only a principal from the management account.</p> <p>For more information about invitations, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_accounts_invites.html\">Inviting an Amazon Web Services account to join your organization</a> in the <i>Organizations User Guide</i>. For more information about requests to enable all features in the organization, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_org_support-all-features.html\">Enabling all features in your organization</a> in the <i>Organizations User Guide</i>.</p> </li> </ul> <p>After you accept a handshake, it continues to appear in the results of relevant APIs for only 30 days. After that, it's deleted.</p>"}, "AttachPolicy": {"name": "AttachPolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AttachPolicyRequest"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "ConcurrentModificationException"}, {"shape": "ConstraintViolationException"}, {"shape": "DuplicatePolicyAttachmentException"}, {"shape": "InvalidInputException"}, {"shape": "PolicyNotFoundException"}, {"shape": "PolicyTypeNotEnabledException"}, {"shape": "ServiceException"}, {"shape": "TargetNotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnsupportedAPIEndpointException"}, {"shape": "PolicyChangesInProgressException"}], "documentation": "<p>Attaches a policy to a root, an organizational unit (OU), or an individual account. How the policy affects accounts depends on the type of policy. Refer to the <i>Organizations User Guide</i> for information about each policy type:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_scp.html\">SERVICE_CONTROL_POLICY</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_rcps.html\">RESOURCE_CONTROL_POLICY</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_declarative.html\">DECLARATIVE_POLICY_EC2</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_backup.html\">BACKUP_POLICY</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_tag-policies.html\">TAG_POLICY</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_chatbot.html\">CHATBOT_POLICY</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_ai-opt-out.html\">AISERVICES_OPT_OUT_POLICY</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_security_hub.html\">SECURITYHUB_POLICY</a> </p> </li> </ul> <p>This operation can be called only from the organization's management account or by a member account that is a delegated administrator.</p>"}, "CancelHandshake": {"name": "CancelHandshake", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CancelHandshakeRequest"}, "output": {"shape": "CancelHandshakeResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ConcurrentModificationException"}, {"shape": "HandshakeNotFoundException"}, {"shape": "InvalidHandshakeTransitionException"}, {"shape": "HandshakeAlreadyInStateException"}, {"shape": "InvalidInputException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Cancels a handshake. Canceling a handshake sets the handshake state to <code>CANCELED</code>.</p> <p>This operation can be called only from the account that originated the handshake. The recipient of the handshake can't cancel it, but can use <a>DeclineHandshake</a> instead. After a handshake is canceled, the recipient can no longer respond to that handshake.</p> <p>After you cancel a handshake, it continues to appear in the results of relevant APIs for only 30 days. After that, it's deleted.</p>"}, "CloseAccount": {"name": "CloseAccount", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CloseAccountRequest"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "AccountAlreadyClosedException"}, {"shape": "AccountNotFoundException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "ConcurrentModificationException"}, {"shape": "ConflictException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidInputException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnsupportedAPIEndpointException"}], "documentation": "<p>Closes an Amazon Web Services member account within an organization. You can close an account when <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_org_support-all-features.html\">all features are enabled </a>. You can't close the management account with this API. This is an asynchronous request that Amazon Web Services performs in the background. Because <code>CloseAccount</code> operates asynchronously, it can return a successful completion message even though account closure might still be in progress. You need to wait a few minutes before the account is fully closed. To check the status of the request, do one of the following:</p> <ul> <li> <p>Use the <code>AccountId</code> that you sent in the <code>CloseAccount</code> request to provide as a parameter to the <a>DescribeAccount</a> operation. </p> <p>While the close account request is in progress, Account status will indicate PENDING_CLOSURE. When the close account request completes, the status will change to SUSPENDED. </p> </li> <li> <p>Check the CloudTrail log for the <code>CloseAccountResult</code> event that gets published after the account closes successfully. For information on using CloudTrail with Organizations, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_security_incident-response.html#orgs_cloudtrail-integration\">Logging and monitoring in Organizations</a> in the <i>Organizations User Guide</i>.</p> </li> </ul> <note> <ul> <li> <p>You can close only 10% of member accounts, between 10 and 1000, within a rolling 30 day period. This quota is not bound by a calendar month, but starts when you close an account. After you reach this limit, you can't close additional accounts. For more information, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_accounts_close.html\">Closing a member account in your organization</a> and <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_reference_limits.html\">Quotas for Organizations</a> in the <i>Organizations User Guide</i>. </p> </li> <li> <p>To reinstate a closed account, contact Amazon Web Services Support within the 90-day grace period while the account is in SUSPENDED status. </p> </li> <li> <p>If the Amazon Web Services account you attempt to close is linked to an Amazon Web Services GovCloud (US) account, the <code>CloseAccount</code> request will close both accounts. To learn important pre-closure details, see <a href=\"https://docs.aws.amazon.com/govcloud-us/latest/UserGuide/Closing-govcloud-account.html\"> Closing an Amazon Web Services GovCloud (US) account</a> in the <i> Amazon Web Services GovCloud User Guide</i>.</p> </li> </ul> </note>"}, "CreateAccount": {"name": "CreateAccount", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateAccountRequest"}, "output": {"shape": "CreateAccountResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "ConcurrentModificationException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidInputException"}, {"shape": "FinalizingOrganizationException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnsupportedAPIEndpointException"}], "documentation": "<p>Creates an Amazon Web Services account that is automatically a member of the organization whose credentials made the request. This is an asynchronous request that Amazon Web Services performs in the background. Because <code>CreateAccount</code> operates asynchronously, it can return a successful completion message even though account initialization might still be in progress. You might need to wait a few minutes before you can successfully access the account. To check the status of the request, do one of the following:</p> <ul> <li> <p>Use the <code>Id</code> value of the <code>CreateAccountStatus</code> response element from this operation to provide as a parameter to the <a>DescribeCreateAccountStatus</a> operation.</p> </li> <li> <p>Check the CloudTrail log for the <code>CreateAccountResult</code> event. For information on using CloudTrail with Organizations, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_security_incident-response.html#orgs_cloudtrail-integration\">Logging and monitoring in Organizations</a> in the <i>Organizations User Guide</i>.</p> </li> </ul> <p>The user who calls the API to create an account must have the <code>organizations:CreateAccount</code> permission. If you enabled all features in the organization, Organizations creates the required service-linked role named <code>AWSServiceRoleForOrganizations</code>. For more information, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_integrate_services.html#orgs_integrate_services-using_slrs\">Organizations and service-linked roles</a> in the <i>Organizations User Guide</i>.</p> <p>If the request includes tags, then the requester must have the <code>organizations:TagResource</code> permission.</p> <p>Organizations preconfigures the new member account with a role (named <code>OrganizationAccountAccessRole</code> by default) that grants users in the management account administrator permissions in the new member account. Principals in the management account can assume the role. Organizations clones the company name and address information for the new account from the organization's management account.</p> <p>This operation can be called only from the organization's management account.</p> <p>For more information about creating accounts, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_accounts_create.html\">Creating a member account in your organization</a> in the <i>Organizations User Guide</i>.</p> <important> <ul> <li> <p>When you create an account in an organization using the Organizations console, API, or CLI commands, the information required for the account to operate as a standalone account, such as a payment method is <i>not</i> automatically collected. If you must remove an account from your organization later, you can do so only after you provide the missing information. For more information, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_account-before-remove.html\">Considerations before removing an account from an organization</a> in the <i>Organizations User Guide</i>.</p> </li> <li> <p>If you get an exception that indicates that you exceeded your account limits for the organization, contact <a href=\"https://console.aws.amazon.com/support/home#/\">Amazon Web Services Support</a>.</p> </li> <li> <p>If you get an exception that indicates that the operation failed because your organization is still initializing, wait one hour and then try again. If the error persists, contact <a href=\"https://console.aws.amazon.com/support/home#/\">Amazon Web Services Support</a>.</p> </li> <li> <p>It isn't recommended to use <code>CreateAccount</code> to create multiple temporary accounts, and using the <code>CreateAccount</code> API to close accounts is subject to a 30-day usage quota. For information on the requirements and process for closing an account, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_accounts_close.html\">Closing a member account in your organization</a> in the <i>Organizations User Guide</i>.</p> </li> </ul> </important> <note> <p>When you create a member account with this operation, you can choose whether to create the account with the <b>IAM User and Role Access to Billing Information</b> switch enabled. If you enable it, IAM users and roles that have appropriate permissions can view billing information for the account. If you disable it, only the account root user can access billing information. For information about how to disable this switch for an account, see <a href=\"https://docs.aws.amazon.com/awsaccountbilling/latest/aboutv2/control-access-billing.html#grantaccess\">Granting access to your billing information and tools</a>.</p> </note>"}, "CreateGovCloudAccount": {"name": "CreateGovCloudAccount", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateGovCloudAccountRequest"}, "output": {"shape": "CreateGovCloudAccountResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "ConcurrentModificationException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidInputException"}, {"shape": "FinalizingOrganizationException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnsupportedAPIEndpointException"}], "documentation": "<p>This action is available if all of the following are true:</p> <ul> <li> <p>You're authorized to create accounts in the Amazon Web Services GovCloud (US) Region. For more information on the Amazon Web Services GovCloud (US) Region, see the <a href=\"https://docs.aws.amazon.com/govcloud-us/latest/UserGuide/welcome.html\"> <i>Amazon Web Services GovCloud User Guide</i>.</a> </p> </li> <li> <p>You already have an account in the Amazon Web Services GovCloud (US) Region that is paired with a management account of an organization in the commercial Region.</p> </li> <li> <p>You call this action from the management account of your organization in the commercial Region.</p> </li> <li> <p>You have the <code>organizations:CreateGovCloudAccount</code> permission. </p> </li> </ul> <p>Organizations automatically creates the required service-linked role named <code>AWSServiceRoleForOrganizations</code>. For more information, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_integrate_services.html#orgs_integrate_services-using_slrs\">Organizations and service-linked roles</a> in the <i>Organizations User Guide</i>.</p> <p>Amazon Web Services automatically enables CloudTrail for Amazon Web Services GovCloud (US) accounts, but you should also do the following:</p> <ul> <li> <p>Verify that CloudTrail is enabled to store logs.</p> </li> <li> <p>Create an Amazon S3 bucket for CloudTrail log storage.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/govcloud-us/latest/UserGuide/verifying-cloudtrail.html\">Verifying CloudTrail Is Enabled</a> in the <i>Amazon Web Services GovCloud User Guide</i>. </p> </li> </ul> <p>If the request includes tags, then the requester must have the <code>organizations:TagResource</code> permission. The tags are attached to the commercial account associated with the GovCloud account, rather than the GovCloud account itself. To add tags to the GovCloud account, call the <a>TagResource</a> operation in the GovCloud Region after the new GovCloud account exists.</p> <p>You call this action from the management account of your organization in the commercial Region to create a standalone Amazon Web Services account in the Amazon Web Services GovCloud (US) Region. After the account is created, the management account of an organization in the Amazon Web Services GovCloud (US) Region can invite it to that organization. For more information on inviting standalone accounts in the Amazon Web Services GovCloud (US) to join an organization, see <a href=\"https://docs.aws.amazon.com/govcloud-us/latest/UserGuide/govcloud-organizations.html\">Organizations</a> in the <i>Amazon Web Services GovCloud User Guide</i>.</p> <p>Calling <code>CreateGovCloudAccount</code> is an asynchronous request that Amazon Web Services performs in the background. Because <code>CreateGovCloudAccount</code> operates asynchronously, it can return a successful completion message even though account initialization might still be in progress. You might need to wait a few minutes before you can successfully access the account. To check the status of the request, do one of the following:</p> <ul> <li> <p>Use the <code>OperationId</code> response element from this operation to provide as a parameter to the <a>DescribeCreateAccountStatus</a> operation.</p> </li> <li> <p>Check the CloudTrail log for the <code>CreateAccountResult</code> event. For information on using CloudTrail with Organizations, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_security_incident-response.html\">Logging and monitoring in Organizations</a> in the <i>Organizations User Guide</i>.</p> </li> </ul> <p/> <p>When you call the <code>CreateGovCloudAccount</code> action, you create two accounts: a standalone account in the Amazon Web Services GovCloud (US) Region and an associated account in the commercial Region for billing and support purposes. The account in the commercial Region is automatically a member of the organization whose credentials made the request. Both accounts are associated with the same email address.</p> <p>A role is created in the new account in the commercial Region that allows the management account in the organization in the commercial Region to assume it. An Amazon Web Services GovCloud (US) account is then created and associated with the commercial account that you just created. A role is also created in the new Amazon Web Services GovCloud (US) account that can be assumed by the Amazon Web Services GovCloud (US) account that is associated with the management account of the commercial organization. For more information and to view a diagram that explains how account access works, see <a href=\"https://docs.aws.amazon.com/govcloud-us/latest/UserGuide/govcloud-organizations.html\">Organizations</a> in the <i>Amazon Web Services GovCloud User Guide</i>.</p> <p>For more information about creating accounts, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_accounts_create.html\">Creating a member account in your organization</a> in the <i>Organizations User Guide</i>.</p> <important> <ul> <li> <p>When you create an account in an organization using the Organizations console, API, or CLI commands, the information required for the account to operate as a standalone account is <i>not</i> automatically collected. This includes a payment method and signing the end user license agreement (EULA). If you must remove an account from your organization later, you can do so only after you provide the missing information. For more information, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_account-before-remove.html\">Considerations before removing an account from an organization</a> in the <i>Organizations User Guide</i>.</p> </li> <li> <p>If you get an exception that indicates that you exceeded your account limits for the organization, contact <a href=\"https://console.aws.amazon.com/support/home#/\">Amazon Web Services Support</a>.</p> </li> <li> <p>If you get an exception that indicates that the operation failed because your organization is still initializing, wait one hour and then try again. If the error persists, contact <a href=\"https://console.aws.amazon.com/support/home#/\">Amazon Web Services Support</a>.</p> </li> <li> <p>Using <code>CreateGovCloudAccount</code> to create multiple temporary accounts isn't recommended. You can only close an account from the Amazon Web Services Billing and Cost Management console, and you must be signed in as the root user. For information on the requirements and process for closing an account, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_accounts_close.html\">Closing a member account in your organization</a> in the <i>Organizations User Guide</i>.</p> </li> </ul> </important> <note> <p>When you create a member account with this operation, you can choose whether to create the account with the <b>IAM User and Role Access to Billing Information</b> switch enabled. If you enable it, IAM users and roles that have appropriate permissions can view billing information for the account. If you disable it, only the account root user can access billing information. For information about how to disable this switch for an account, see <a href=\"https://docs.aws.amazon.com/awsaccountbilling/latest/aboutv2/grantaccess.html\">Granting access to your billing information and tools</a>.</p> </note>"}, "CreateOrganization": {"name": "CreateOrganization", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateOrganizationRequest"}, "output": {"shape": "CreateOrganizationResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "AlreadyInOrganizationException"}, {"shape": "ConcurrentModificationException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidInputException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}, {"shape": "AccessDeniedForDependencyException"}], "documentation": "<p>Creates an Amazon Web Services organization. The account whose user is calling the <code>CreateOrganization</code> operation automatically becomes the <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_getting-started_concepts.html#account\">management account</a> of the new organization.</p> <p>This operation must be called using credentials from the account that is to become the new organization's management account. The principal must also have the relevant IAM permissions.</p> <p>By default (or if you set the <code>FeatureSet</code> parameter to <code>ALL</code>), the new organization is created with all features enabled and service control policies automatically enabled in the root. If you instead choose to create the organization supporting only the consolidated billing features by setting the <code>FeatureSet</code> parameter to <code>CONSOLIDATED_BILLING</code>, no policy types are enabled by default and you can't use organization policies.</p>"}, "CreateOrganizationalUnit": {"name": "CreateOrganizationalUnit", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateOrganizationalUnitRequest"}, "output": {"shape": "CreateOrganizationalUnitResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "ConcurrentModificationException"}, {"shape": "ConstraintViolationException"}, {"shape": "DuplicateOrganizationalUnitException"}, {"shape": "InvalidInputException"}, {"shape": "ParentNotFoundException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Creates an organizational unit (OU) within a root or parent OU. An OU is a container for accounts that enables you to organize your accounts to apply policies according to your business requirements. The number of levels deep that you can nest OUs is dependent upon the policy types enabled for that root. For service control policies, the limit is five.</p> <p>For more information about OUs, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_ous.html\">Managing organizational units (OUs)</a> in the <i>Organizations User Guide</i>.</p> <p>If the request includes tags, then the requester must have the <code>organizations:TagResource</code> permission.</p> <p>This operation can be called only from the organization's management account.</p>"}, "CreatePolicy": {"name": "CreatePolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreatePolicyRequest"}, "output": {"shape": "CreatePolicyResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "ConcurrentModificationException"}, {"shape": "ConstraintViolationException"}, {"shape": "DuplicatePolicyException"}, {"shape": "InvalidInputException"}, {"shape": "MalformedPolicyDocumentException"}, {"shape": "PolicyTypeNotAvailableForOrganizationException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnsupportedAPIEndpointException"}], "documentation": "<p>Creates a policy of a specified type that you can attach to a root, an organizational unit (OU), or an individual Amazon Web Services account.</p> <p>For more information about policies and their use, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies.html\">Managing Organizations policies</a>.</p> <p>If the request includes tags, then the requester must have the <code>organizations:TagResource</code> permission.</p> <p>This operation can be called only from the organization's management account or by a member account that is a delegated administrator.</p>"}, "DeclineHandshake": {"name": "DeclineHandshake", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeclineHandshakeRequest"}, "output": {"shape": "DeclineHandshakeResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ConcurrentModificationException"}, {"shape": "HandshakeNotFoundException"}, {"shape": "InvalidHandshakeTransitionException"}, {"shape": "HandshakeAlreadyInStateException"}, {"shape": "InvalidInputException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Declines a handshake request. This sets the handshake state to <code>DECLINED</code> and effectively deactivates the request.</p> <p>This operation can be called only from the account that received the handshake. The originator of the handshake can use <a>CancelHandshake</a> instead. The originator can't reactivate a declined request, but can reinitiate the process with a new handshake request.</p> <p>After you decline a handshake, it continues to appear in the results of relevant APIs for only 30 days. After that, it's deleted.</p>"}, "DeleteOrganization": {"name": "DeleteOrganization", "http": {"method": "POST", "requestUri": "/"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "ConcurrentModificationException"}, {"shape": "InvalidInputException"}, {"shape": "OrganizationNotEmptyException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Deletes the organization. You can delete an organization only by using credentials from the management account. The organization must be empty of member accounts.</p>"}, "DeleteOrganizationalUnit": {"name": "DeleteOrganizationalUnit", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteOrganizationalUnitRequest"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "ConcurrentModificationException"}, {"shape": "InvalidInputException"}, {"shape": "OrganizationalUnitNotEmptyException"}, {"shape": "OrganizationalUnitNotFoundException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Deletes an organizational unit (OU) from a root or another OU. You must first remove all accounts and child OUs from the OU that you want to delete.</p> <p>This operation can be called only from the organization's management account.</p>"}, "DeletePolicy": {"name": "DeletePolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeletePolicyRequest"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "ConcurrentModificationException"}, {"shape": "InvalidInputException"}, {"shape": "PolicyInUseException"}, {"shape": "PolicyNotFoundException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnsupportedAPIEndpointException"}], "documentation": "<p>Deletes the specified policy from your organization. Before you perform this operation, you must first detach the policy from all organizational units (OUs), roots, and accounts.</p> <p>This operation can be called only from the organization's management account or by a member account that is a delegated administrator.</p>"}, "DeleteResourcePolicy": {"name": "DeleteResourcePolicy", "http": {"method": "POST", "requestUri": "/"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ServiceException"}, {"shape": "UnsupportedAPIEndpointException"}, {"shape": "TooManyRequestsException"}, {"shape": "ConcurrentModificationException"}, {"shape": "ConstraintViolationException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "ResourcePolicyNotFoundException"}], "documentation": "<p>Deletes the resource policy from your organization.</p> <p>This operation can be called only from the organization's management account.</p>"}, "DeregisterDelegatedAdministrator": {"name": "DeregisterDelegatedAdministrator", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeregisterDelegatedAdministratorRequest"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "AccountNotFoundException"}, {"shape": "AccountNotRegisteredException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "ConcurrentModificationException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidInputException"}, {"shape": "TooManyRequestsException"}, {"shape": "ServiceException"}, {"shape": "UnsupportedAPIEndpointException"}], "documentation": "<p>Removes the specified member Amazon Web Services account as a delegated administrator for the specified Amazon Web Services service.</p> <important> <p>Deregistering a delegated administrator can have unintended impacts on the functionality of the enabled Amazon Web Services service. See the documentation for the enabled service before you deregister a delegated administrator so that you understand any potential impacts.</p> </important> <p>You can run this action only for Amazon Web Services services that support this feature. For a current list of services that support it, see the column <i>Supports Delegated Administrator</i> in the table at <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_integrate_services_list.html\">Amazon Web Services Services that you can use with Organizations</a> in the <i>Organizations User Guide.</i> </p> <p>This operation can be called only from the organization's management account.</p>"}, "DescribeAccount": {"name": "DescribeAccount", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeAccountRequest"}, "output": {"shape": "DescribeAccountResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "AccountNotFoundException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "InvalidInputException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Retrieves Organizations-related information about the specified account.</p> <p>This operation can be called only from the organization's management account or by a member account that is a delegated administrator.</p>"}, "DescribeCreateAccountStatus": {"name": "DescribeCreateAccountStatus", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeCreateAccountStatusRequest"}, "output": {"shape": "DescribeCreateAccountStatusResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "CreateAccountStatusNotFoundException"}, {"shape": "InvalidInputException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnsupportedAPIEndpointException"}], "documentation": "<p>Retrieves the current status of an asynchronous request to create an account.</p> <p>This operation can be called only from the organization's management account or by a member account that is a delegated administrator.</p>"}, "DescribeEffectivePolicy": {"name": "DescribeEffectivePolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeEffectivePolicyRequest"}, "output": {"shape": "DescribeEffectivePolicyResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "ConstraintViolationException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}, {"shape": "TargetNotFoundException"}, {"shape": "EffectivePolicyNotFoundException"}, {"shape": "InvalidInputException"}, {"shape": "UnsupportedAPIEndpointException"}], "documentation": "<p>Returns the contents of the effective policy for specified policy type and account. The effective policy is the aggregation of any policies of the specified type that the account inherits, plus any policy of that type that is directly attached to the account.</p> <p>This operation applies only to management policies. It does not apply to authorization policies: service control policies (SCPs) and resource control policies (RCPs).</p> <p>For more information about policy inheritance, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_inheritance_mgmt.html\">Understanding management policy inheritance</a> in the <i>Organizations User Guide</i>.</p> <p>This operation can be called from any account in the organization.</p>"}, "DescribeHandshake": {"name": "DescribeHandshake", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeHandshakeRequest"}, "output": {"shape": "DescribeHandshakeResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ConcurrentModificationException"}, {"shape": "HandshakeNotFoundException"}, {"shape": "InvalidInputException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Retrieves information about a previously requested handshake. The handshake ID comes from the response to the original <a>InviteAccountToOrganization</a> operation that generated the handshake.</p> <p>You can access handshakes that are <code>ACCEPTED</code>, <code>DECLINED</code>, or <code>CANCELED</code> for only 30 days after they change to that state. They're then deleted and no longer accessible.</p> <p>This operation can be called from any account in the organization.</p>"}, "DescribeOrganization": {"name": "DescribeOrganization", "http": {"method": "POST", "requestUri": "/"}, "output": {"shape": "DescribeOrganizationResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "ConcurrentModificationException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Retrieves information about the organization that the user's account belongs to.</p> <p>This operation can be called from any account in the organization.</p> <note> <p>Even if a policy type is shown as available in the organization, you can disable it separately at the root level with <a>DisablePolicyType</a>. Use <a>ListRoots</a> to see the status of policy types for a specified root.</p> </note>"}, "DescribeOrganizationalUnit": {"name": "DescribeOrganizationalUnit", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeOrganizationalUnitRequest"}, "output": {"shape": "DescribeOrganizationalUnitResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "InvalidInputException"}, {"shape": "OrganizationalUnitNotFoundException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Retrieves information about an organizational unit (OU).</p> <p>This operation can be called only from the organization's management account or by a member account that is a delegated administrator.</p>"}, "DescribePolicy": {"name": "DescribePolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribePolicyRequest"}, "output": {"shape": "DescribePolicyResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "InvalidInputException"}, {"shape": "PolicyNotFoundException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnsupportedAPIEndpointException"}], "documentation": "<p>Retrieves information about a policy.</p> <p>This operation can be called only from the organization's management account or by a member account that is a delegated administrator.</p>"}, "DescribeResourcePolicy": {"name": "DescribeResourcePolicy", "http": {"method": "POST", "requestUri": "/"}, "output": {"shape": "DescribeResourcePolicyResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ServiceException"}, {"shape": "UnsupportedAPIEndpointException"}, {"shape": "TooManyRequestsException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "ResourcePolicyNotFoundException"}, {"shape": "ConstraintViolationException"}], "documentation": "<p>Retrieves information about a resource policy.</p> <p>This operation can be called only from the organization's management account or by a member account that is a delegated administrator.</p>"}, "DetachPolicy": {"name": "DetachPolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DetachPolicyRequest"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "ConcurrentModificationException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidInputException"}, {"shape": "PolicyNotAttachedException"}, {"shape": "PolicyNotFoundException"}, {"shape": "ServiceException"}, {"shape": "TargetNotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnsupportedAPIEndpointException"}, {"shape": "PolicyChangesInProgressException"}], "documentation": "<p>Detaches a policy from a target root, organizational unit (OU), or account.</p> <important> <p>If the policy being detached is a service control policy (SCP), the changes to permissions for Identity and Access Management (IAM) users and roles in affected accounts are immediate.</p> </important> <p>Every root, OU, and account must have at least one SCP attached. If you want to replace the default <code>FullAWSAccess</code> policy with an SCP that limits the permissions that can be delegated, you must attach the replacement SCP before you can remove the default SCP. This is the authorization strategy of an \"<a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/SCP_strategies.html#orgs_policies_allowlist\">allow list</a>\". If you instead attach a second SCP and leave the <code>FullAWSAccess</code> SCP still attached, and specify <code>\"Effect\": \"Deny\"</code> in the second SCP to override the <code>\"Effect\": \"Allow\"</code> in the <code>FullAWSAccess</code> policy (or any other attached SCP), you're using the authorization strategy of a \"<a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/SCP_strategies.html#orgs_policies_denylist\">deny list</a>\".</p> <p>This operation can be called only from the organization's management account or by a member account that is a delegated administrator.</p>"}, "DisableAWSServiceAccess": {"name": "DisableAWSServiceAccess", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DisableAWSServiceAccessRequest"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "ConcurrentModificationException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidInputException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnsupportedAPIEndpointException"}], "documentation": "<p>Disables the integration of an Amazon Web Services service (the service that is specified by <code>ServicePrincipal</code>) with Organizations. When you disable integration, the specified service no longer can create a <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/using-service-linked-roles.html\">service-linked role</a> in <i>new</i> accounts in your organization. This means the service can't perform operations on your behalf on any new accounts in your organization. The service can still perform operations in older accounts until the service completes its clean-up from Organizations.</p> <important> <p>We <b> <i>strongly recommend</i> </b> that you don't use this command to disable integration between Organizations and the specified Amazon Web Services service. Instead, use the console or commands that are provided by the specified service. This lets the trusted service perform any required initialization when enabling trusted access, such as creating any required resources and any required clean up of resources when disabling trusted access. </p> <p>For information about how to disable trusted service access to your organization using the trusted service, see the <b>Learn more</b> link under the <b>Supports Trusted Access</b> column at <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_integrate_services_list.html\">Amazon Web Services services that you can use with Organizations</a>. on this page.</p> <p>If you disable access by using this command, it causes the following actions to occur:</p> <ul> <li> <p>The service can no longer create a service-linked role in the accounts in your organization. This means that the service can't perform operations on your behalf on any new accounts in your organization. The service can still perform operations in older accounts until the service completes its clean-up from Organizations. </p> </li> <li> <p>The service can no longer perform tasks in the member accounts in the organization, unless those operations are explicitly permitted by the IAM policies that are attached to your roles. This includes any data aggregation from the member accounts to the management account, or to a delegated administrator account, where relevant.</p> </li> <li> <p>Some services detect this and clean up any remaining data or resources related to the integration, while other services stop accessing the organization but leave any historical data and configuration in place to support a possible re-enabling of the integration.</p> </li> </ul> <p>Using the other service's console or commands to disable the integration ensures that the other service is aware that it can clean up any resources that are required only for the integration. How the service cleans up its resources in the organization's accounts depends on that service. For more information, see the documentation for the other Amazon Web Services service. </p> </important> <p>After you perform the <code>DisableAWSServiceAccess</code> operation, the specified service can no longer perform operations in your organization's accounts </p> <p>For more information about integrating other services with Organizations, including the list of services that work with Organizations, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_integrate_services.html\">Using Organizations with other Amazon Web Services services</a> in the <i>Organizations User Guide</i>.</p> <p>This operation can be called only from the organization's management account.</p>"}, "DisablePolicyType": {"name": "DisablePolicyType", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DisablePolicyTypeRequest"}, "output": {"shape": "DisablePolicyTypeResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "ConcurrentModificationException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidInputException"}, {"shape": "PolicyTypeNotEnabledException"}, {"shape": "RootNotFoundException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnsupportedAPIEndpointException"}, {"shape": "PolicyChangesInProgressException"}], "documentation": "<p>Disables an organizational policy type in a root. A policy of a certain type can be attached to entities in a root only if that type is enabled in the root. After you perform this operation, you no longer can attach policies of the specified type to that root or to any organizational unit (OU) or account in that root. You can undo this by using the <a>EnablePolicyType</a> operation.</p> <p>This is an asynchronous request that Amazon Web Services performs in the background. If you disable a policy type for a root, it still appears enabled for the organization if <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_org_support-all-features.html\">all features</a> are enabled for the organization. Amazon Web Services recommends that you first use <a>ListRoots</a> to see the status of policy types for a specified root, and then use this operation.</p> <p>This operation can be called only from the organization's management account or by a member account that is a delegated administrator.</p> <p> To view the status of available policy types in the organization, use <a>DescribeOrganization</a>.</p>"}, "EnableAWSServiceAccess": {"name": "EnableAWSServiceAccess", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "EnableAWSServiceAccessRequest"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "ConcurrentModificationException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidInputException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnsupportedAPIEndpointException"}], "documentation": "<p>Provides an Amazon Web Services service (the service that is specified by <code>ServicePrincipal</code>) with permissions to view the structure of an organization, create a <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/using-service-linked-roles.html\">service-linked role</a> in all the accounts in the organization, and allow the service to perform operations on behalf of the organization and its accounts. Establishing these permissions can be a first step in enabling the integration of an Amazon Web Services service with Organizations.</p> <important> <p>We recommend that you enable integration between Organizations and the specified Amazon Web Services service by using the console or commands that are provided by the specified service. Doing so ensures that the service is aware that it can create the resources that are required for the integration. How the service creates those resources in the organization's accounts depends on that service. For more information, see the documentation for the other Amazon Web Services service.</p> </important> <p>For more information about enabling services to integrate with Organizations, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_integrate_services.html\">Using Organizations with other Amazon Web Services services</a> in the <i>Organizations User Guide</i>.</p> <p>This operation can be called only from the organization's management account.</p>"}, "EnableAllFeatures": {"name": "EnableAllFeatures", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "EnableAllFeaturesRequest"}, "output": {"shape": "EnableAllFeaturesResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "ConcurrentModificationException"}, {"shape": "ConstraintViolationException"}, {"shape": "HandshakeConstraintViolationException"}, {"shape": "InvalidInputException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Enables all features in an organization. This enables the use of organization policies that can restrict the services and actions that can be called in each account. Until you enable all features, you have access only to consolidated billing, and you can't use any of the advanced account administration features that Organizations supports. For more information, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_org_support-all-features.html\">Enabling all features in your organization</a> in the <i>Organizations User Guide</i>.</p> <important> <p>This operation is required only for organizations that were created explicitly with only the consolidated billing features enabled. Calling this operation sends a handshake to every invited account in the organization. The feature set change can be finalized and the additional features enabled only after all administrators in the invited accounts approve the change by accepting the handshake.</p> </important> <p>After you enable all features, you can separately enable or disable individual policy types in a root using <a>EnablePolicyType</a> and <a>DisablePolicyType</a>. To see the status of policy types in a root, use <a>ListRoots</a>.</p> <p>After all invited member accounts accept the handshake, you finalize the feature set change by accepting the handshake that contains <code>\"Action\": \"ENABLE_ALL_FEATURES\"</code>. This completes the change.</p> <p>After you enable all features in your organization, the management account in the organization can apply policies on all member accounts. These policies can restrict what users and even administrators in those accounts can do. The management account can apply policies that prevent accounts from leaving the organization. Ensure that your account administrators are aware of this.</p> <p>This operation can be called only from the organization's management account.</p>"}, "EnablePolicyType": {"name": "EnablePolicyType", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "EnablePolicyTypeRequest"}, "output": {"shape": "EnablePolicyTypeResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "ConcurrentModificationException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidInputException"}, {"shape": "PolicyTypeAlreadyEnabledException"}, {"shape": "RootNotFoundException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}, {"shape": "PolicyTypeNotAvailableForOrganizationException"}, {"shape": "UnsupportedAPIEndpointException"}, {"shape": "PolicyChangesInProgressException"}], "documentation": "<p>Enables a policy type in a root. After you enable a policy type in a root, you can attach policies of that type to the root, any organizational unit (OU), or account in that root. You can undo this by using the <a>DisablePolicyType</a> operation.</p> <p>This is an asynchronous request that Amazon Web Services performs in the background. Amazon Web Services recommends that you first use <a>ListRoots</a> to see the status of policy types for a specified root, and then use this operation.</p> <p>This operation can be called only from the organization's management account or by a member account that is a delegated administrator.</p> <p>You can enable a policy type in a root only if that policy type is available in the organization. To view the status of available policy types in the organization, use <a>DescribeOrganization</a>.</p>"}, "InviteAccountToOrganization": {"name": "InviteAccountToOrganization", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "InviteAccountToOrganizationRequest"}, "output": {"shape": "InviteAccountToOrganizationResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "AccountOwnerNotVerifiedException"}, {"shape": "ConcurrentModificationException"}, {"shape": "HandshakeConstraintViolationException"}, {"shape": "DuplicateHandshakeException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidInputException"}, {"shape": "FinalizingOrganizationException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Sends an invitation to another account to join your organization as a member account. Organizations sends email on your behalf to the email address that is associated with the other account's owner. The invitation is implemented as a <a>Handshake</a> whose details are in the response.</p> <important> <p>If you receive an exception that indicates that you exceeded your account limits for the organization or that the operation failed because your organization is still initializing, wait one hour and then try again. If the error persists after an hour, contact <a href=\"https://console.aws.amazon.com/support/home#/\">Amazon Web Services Support</a>.</p> </important> <p>If the request includes tags, then the requester must have the <code>organizations:TagResource</code> permission.</p> <p>This operation can be called only from the organization's management account.</p>"}, "LeaveOrganization": {"name": "LeaveOrganization", "http": {"method": "POST", "requestUri": "/"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "AccountNotFoundException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "ConcurrentModificationException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidInputException"}, {"shape": "MasterCannotLeaveOrganizationException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Removes a member account from its parent organization. This version of the operation is performed by the account that wants to leave. To remove a member account as a user in the management account, use <a>RemoveAccountFromOrganization</a> instead.</p> <p>This operation can be called only from a member account in the organization.</p> <important> <ul> <li> <p>The management account in an organization with all features enabled can set service control policies (SCPs) that can restrict what administrators of member accounts can do. This includes preventing them from successfully calling <code>LeaveOrganization</code> and leaving the organization.</p> </li> <li> <p>You can leave an organization as a member account only if the account is configured with the information required to operate as a standalone account. When you create an account in an organization using the Organizations console, API, or CLI commands, the information required of standalone accounts is <i>not</i> automatically collected. For each account that you want to make standalone, you must perform the following steps. If any of the steps are already completed for this account, that step doesn't appear.</p> <ul> <li> <p>Choose a support plan</p> </li> <li> <p>Provide and verify the required contact information</p> </li> <li> <p>Provide a current payment method</p> </li> </ul> <p>Amazon Web Services uses the payment method to charge for any billable (not free tier) Amazon Web Services activity that occurs while the account isn't attached to an organization. For more information, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_account-before-remove.html\">Considerations before removing an account from an organization</a> in the <i>Organizations User Guide</i>.</p> </li> <li> <p>The account that you want to leave must not be a delegated administrator account for any Amazon Web Services service enabled for your organization. If the account is a delegated administrator, you must first change the delegated administrator account to another account that is remaining in the organization.</p> </li> <li> <p>After the account leaves the organization, all tags that were attached to the account object in the organization are deleted. Amazon Web Services accounts outside of an organization do not support tags.</p> </li> <li> <p>A newly created account has a waiting period before it can be removed from its organization. You must wait until at least seven days after the account was created. Invited accounts aren't subject to this waiting period.</p> </li> <li> <p>If you are using an organization principal to call <code>LeaveOrganization</code> across multiple accounts, you can only do this up to 5 accounts per second in a single organization.</p> </li> </ul> </important>"}, "ListAWSServiceAccessForOrganization": {"name": "ListAWSServiceAccessForOrganization", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListAWSServiceAccessForOrganizationRequest"}, "output": {"shape": "ListAWSServiceAccessForOrganizationResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidInputException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnsupportedAPIEndpointException"}], "documentation": "<p>Returns a list of the Amazon Web Services services that you enabled to integrate with your organization. After a service on this list creates the resources that it requires for the integration, it can perform operations on your organization and its accounts.</p> <p>For more information about integrating other services with Organizations, including the list of services that currently work with Organizations, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_integrate_services.html\">Using Organizations with other Amazon Web Services services</a> in the <i>Organizations User Guide</i>.</p> <p>This operation can be called only from the organization's management account or by a member account that is a delegated administrator.</p>"}, "ListAccounts": {"name": "ListAccounts", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListAccountsRequest"}, "output": {"shape": "ListAccountsResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "InvalidInputException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Lists all the accounts in the organization. To request only the accounts in a specified root or organizational unit (OU), use the <a>ListAccountsForParent</a> operation instead.</p> <note> <p>Always check the <code>NextToken</code> response parameter for a <code>null</code> value when calling a <code>List*</code> operation. These operations can occasionally return an empty set of results even when there are more results available. The <code>NextToken</code> response parameter value is <code>null</code> <i>only</i> when there are no more results to display.</p> </note> <p>This operation can be called only from the organization's management account or by a member account that is a delegated administrator.</p>"}, "ListAccountsForParent": {"name": "ListAccountsForParent", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListAccountsForParentRequest"}, "output": {"shape": "ListAccountsForParentResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "InvalidInputException"}, {"shape": "ParentNotFoundException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Lists the accounts in an organization that are contained by the specified target root or organizational unit (OU). If you specify the root, you get a list of all the accounts that aren't in any OU. If you specify an OU, you get a list of all the accounts in only that OU and not in any child OUs. To get a list of all accounts in the organization, use the <a>ListAccounts</a> operation.</p> <note> <p>Always check the <code>NextToken</code> response parameter for a <code>null</code> value when calling a <code>List*</code> operation. These operations can occasionally return an empty set of results even when there are more results available. The <code>NextToken</code> response parameter value is <code>null</code> <i>only</i> when there are no more results to display.</p> </note> <p>This operation can be called only from the organization's management account or by a member account that is a delegated administrator.</p>"}, "ListChildren": {"name": "ListChildren", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListChildrenRequest"}, "output": {"shape": "ListChildrenResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "InvalidInputException"}, {"shape": "ParentNotFoundException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Lists all of the organizational units (OUs) or accounts that are contained in the specified parent OU or root. This operation, along with <a>ListParents</a> enables you to traverse the tree structure that makes up this root.</p> <note> <p>Always check the <code>NextToken</code> response parameter for a <code>null</code> value when calling a <code>List*</code> operation. These operations can occasionally return an empty set of results even when there are more results available. The <code>NextToken</code> response parameter value is <code>null</code> <i>only</i> when there are no more results to display.</p> </note> <p>This operation can be called only from the organization's management account or by a member account that is a delegated administrator.</p>"}, "ListCreateAccountStatus": {"name": "ListCreateAccountStatus", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListCreateAccountStatusRequest"}, "output": {"shape": "ListCreateAccountStatusResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "InvalidInputException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnsupportedAPIEndpointException"}], "documentation": "<p>Lists the account creation requests that match the specified status that is currently being tracked for the organization.</p> <note> <p>Always check the <code>NextToken</code> response parameter for a <code>null</code> value when calling a <code>List*</code> operation. These operations can occasionally return an empty set of results even when there are more results available. The <code>NextToken</code> response parameter value is <code>null</code> <i>only</i> when there are no more results to display.</p> </note> <p>This operation can be called only from the organization's management account or by a member account that is a delegated administrator.</p>"}, "ListDelegatedAdministrators": {"name": "ListDelegatedAdministrators", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListDelegatedAdministratorsRequest"}, "output": {"shape": "ListDelegatedAdministratorsResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidInputException"}, {"shape": "TooManyRequestsException"}, {"shape": "ServiceException"}, {"shape": "UnsupportedAPIEndpointException"}], "documentation": "<p>Lists the Amazon Web Services accounts that are designated as delegated administrators in this organization.</p> <p>This operation can be called only from the organization's management account or by a member account that is a delegated administrator.</p>"}, "ListDelegatedServicesForAccount": {"name": "ListDelegatedServicesForAccount", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListDelegatedServicesForAccountRequest"}, "output": {"shape": "ListDelegatedServicesForAccountResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "AccountNotFoundException"}, {"shape": "AccountNotRegisteredException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidInputException"}, {"shape": "TooManyRequestsException"}, {"shape": "ServiceException"}, {"shape": "UnsupportedAPIEndpointException"}], "documentation": "<p>List the Amazon Web Services services for which the specified account is a delegated administrator.</p> <p>This operation can be called only from the organization's management account or by a member account that is a delegated administrator.</p>"}, "ListHandshakesForAccount": {"name": "ListHandshakesForAccount", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListHandshakesForAccountRequest"}, "output": {"shape": "ListHandshakesForAccountResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ConcurrentModificationException"}, {"shape": "InvalidInputException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Lists the current handshakes that are associated with the account of the requesting user.</p> <p>Handshakes that are <code>ACCEPTED</code>, <code>DECLINED</code>, <code>CANCELED</code>, or <code>EXPIRED</code> appear in the results of this API for only 30 days after changing to that state. After that, they're deleted and no longer accessible.</p> <note> <p>Always check the <code>NextToken</code> response parameter for a <code>null</code> value when calling a <code>List*</code> operation. These operations can occasionally return an empty set of results even when there are more results available. The <code>NextToken</code> response parameter value is <code>null</code> <i>only</i> when there are no more results to display.</p> </note> <p>This operation can be called from any account in the organization.</p>"}, "ListHandshakesForOrganization": {"name": "ListHandshakesForOrganization", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListHandshakesForOrganizationRequest"}, "output": {"shape": "ListHandshakesForOrganizationResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "ConcurrentModificationException"}, {"shape": "InvalidInputException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Lists the handshakes that are associated with the organization that the requesting user is part of. The <code>ListHandshakesForOrganization</code> operation returns a list of handshake structures. Each structure contains details and status about a handshake.</p> <p>Handshakes that are <code>ACCEPTED</code>, <code>DECLINED</code>, <code>CANCELED</code>, or <code>EXPIRED</code> appear in the results of this API for only 30 days after changing to that state. After that, they're deleted and no longer accessible.</p> <note> <p>Always check the <code>NextToken</code> response parameter for a <code>null</code> value when calling a <code>List*</code> operation. These operations can occasionally return an empty set of results even when there are more results available. The <code>NextToken</code> response parameter value is <code>null</code> <i>only</i> when there are no more results to display.</p> </note> <p>This operation can be called only from the organization's management account or by a member account that is a delegated administrator.</p>"}, "ListOrganizationalUnitsForParent": {"name": "ListOrganizationalUnitsForParent", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListOrganizationalUnitsForParentRequest"}, "output": {"shape": "ListOrganizationalUnitsForParentResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "InvalidInputException"}, {"shape": "ParentNotFoundException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Lists the organizational units (OUs) in a parent organizational unit or root.</p> <note> <p>Always check the <code>NextToken</code> response parameter for a <code>null</code> value when calling a <code>List*</code> operation. These operations can occasionally return an empty set of results even when there are more results available. The <code>NextToken</code> response parameter value is <code>null</code> <i>only</i> when there are no more results to display.</p> </note> <p>This operation can be called only from the organization's management account or by a member account that is a delegated administrator.</p>"}, "ListParents": {"name": "ListParents", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListParentsRequest"}, "output": {"shape": "ListParentsResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "ChildNotFoundException"}, {"shape": "InvalidInputException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Lists the root or organizational units (OUs) that serve as the immediate parent of the specified child OU or account. This operation, along with <a>ListChildren</a> enables you to traverse the tree structure that makes up this root.</p> <note> <p>Always check the <code>NextToken</code> response parameter for a <code>null</code> value when calling a <code>List*</code> operation. These operations can occasionally return an empty set of results even when there are more results available. The <code>NextToken</code> response parameter value is <code>null</code> <i>only</i> when there are no more results to display.</p> </note> <p>This operation can be called only from the organization's management account or by a member account that is a delegated administrator.</p> <note> <p>In the current release, a child can have only a single parent.</p> </note>"}, "ListPolicies": {"name": "ListPolicies", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListPoliciesRequest"}, "output": {"shape": "ListPoliciesResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "InvalidInputException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnsupportedAPIEndpointException"}], "documentation": "<p>Retrieves the list of all policies in an organization of a specified type.</p> <note> <p>Always check the <code>NextToken</code> response parameter for a <code>null</code> value when calling a <code>List*</code> operation. These operations can occasionally return an empty set of results even when there are more results available. The <code>NextToken</code> response parameter value is <code>null</code> <i>only</i> when there are no more results to display.</p> </note> <p>This operation can be called only from the organization's management account or by a member account that is a delegated administrator.</p>"}, "ListPoliciesForTarget": {"name": "ListPoliciesForTarget", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListPoliciesForTargetRequest"}, "output": {"shape": "ListPoliciesForTargetResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "InvalidInputException"}, {"shape": "ServiceException"}, {"shape": "TargetNotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnsupportedAPIEndpointException"}], "documentation": "<p>Lists the policies that are directly attached to the specified target root, organizational unit (OU), or account. You must specify the policy type that you want included in the returned list.</p> <note> <p>Always check the <code>NextToken</code> response parameter for a <code>null</code> value when calling a <code>List*</code> operation. These operations can occasionally return an empty set of results even when there are more results available. The <code>NextToken</code> response parameter value is <code>null</code> <i>only</i> when there are no more results to display.</p> </note> <p>This operation can be called only from the organization's management account or by a member account that is a delegated administrator.</p>"}, "ListRoots": {"name": "ListRoots", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListRootsRequest"}, "output": {"shape": "ListRootsResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "InvalidInputException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Lists the roots that are defined in the current organization.</p> <note> <p>Always check the <code>NextToken</code> response parameter for a <code>null</code> value when calling a <code>List*</code> operation. These operations can occasionally return an empty set of results even when there are more results available. The <code>NextToken</code> response parameter value is <code>null</code> <i>only</i> when there are no more results to display.</p> </note> <p>This operation can be called only from the organization's management account or by a member account that is a delegated administrator.</p> <note> <p>Policy types can be enabled and disabled in roots. This is distinct from whether they're available in the organization. When you enable all features, you make policy types available for use in that organization. Individual policy types can then be enabled and disabled in a root. To see the availability of a policy type in an organization, use <a>DescribeOrganization</a>.</p> </note>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "TargetNotFoundException"}, {"shape": "InvalidInputException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Lists tags that are attached to the specified resource.</p> <p>You can attach tags to the following resources in Organizations.</p> <ul> <li> <p>Amazon Web Services account</p> </li> <li> <p>Organization root</p> </li> <li> <p>Organizational unit (OU)</p> </li> <li> <p>Policy (any type)</p> </li> </ul> <p>This operation can be called only from the organization's management account or by a member account that is a delegated administrator.</p>"}, "ListTargetsForPolicy": {"name": "ListTargetsForPolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTargetsForPolicyRequest"}, "output": {"shape": "ListTargetsForPolicyResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "InvalidInputException"}, {"shape": "PolicyNotFoundException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnsupportedAPIEndpointException"}], "documentation": "<p>Lists all the roots, organizational units (OUs), and accounts that the specified policy is attached to.</p> <note> <p>Always check the <code>NextToken</code> response parameter for a <code>null</code> value when calling a <code>List*</code> operation. These operations can occasionally return an empty set of results even when there are more results available. The <code>NextToken</code> response parameter value is <code>null</code> <i>only</i> when there are no more results to display.</p> </note> <p>This operation can be called only from the organization's management account or by a member account that is a delegated administrator.</p>"}, "MoveAccount": {"name": "MoveAccount", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "MoveAccountRequest"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "InvalidInputException"}, {"shape": "SourceParentNotFoundException"}, {"shape": "DestinationParentNotFoundException"}, {"shape": "DuplicateAccountException"}, {"shape": "AccountNotFoundException"}, {"shape": "TooManyRequestsException"}, {"shape": "ConcurrentModificationException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "ServiceException"}], "documentation": "<p>Moves an account from its current source parent root or organizational unit (OU) to the specified destination parent root or OU.</p> <p>This operation can be called only from the organization's management account.</p>"}, "PutResourcePolicy": {"name": "PutResourcePolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutResourcePolicyRequest"}, "output": {"shape": "PutResourcePolicyResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ServiceException"}, {"shape": "UnsupportedAPIEndpointException"}, {"shape": "TooManyRequestsException"}, {"shape": "ConcurrentModificationException"}, {"shape": "InvalidInputException"}, {"shape": "ConstraintViolationException"}, {"shape": "AWSOrganizationsNotInUseException"}], "documentation": "<p>Creates or updates a resource policy.</p> <p>This operation can be called only from the organization's management account..</p>"}, "RegisterDelegatedAdministrator": {"name": "RegisterDelegatedAdministrator", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "RegisterDelegatedAdministratorRequest"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "AccountAlreadyRegisteredException"}, {"shape": "AccountNotFoundException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "ConcurrentModificationException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidInputException"}, {"shape": "TooManyRequestsException"}, {"shape": "ServiceException"}, {"shape": "UnsupportedAPIEndpointException"}], "documentation": "<p>Enables the specified member account to administer the Organizations features of the specified Amazon Web Services service. It grants read-only access to Organizations service data. The account still requires IAM permissions to access and administer the Amazon Web Services service.</p> <p>You can run this action only for Amazon Web Services services that support this feature. For a current list of services that support it, see the column <i>Supports Delegated Administrator</i> in the table at <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_integrate_services_list.html\">Amazon Web Services Services that you can use with Organizations</a> in the <i>Organizations User Guide.</i> </p> <p>This operation can be called only from the organization's management account.</p>"}, "RemoveAccountFromOrganization": {"name": "RemoveAccountFromOrganization", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "RemoveAccountFromOrganizationRequest"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "AccountNotFoundException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "ConcurrentModificationException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidInputException"}, {"shape": "MasterCannotLeaveOrganizationException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Removes the specified account from the organization.</p> <p>The removed account becomes a standalone account that isn't a member of any organization. It's no longer subject to any policies and is responsible for its own bill payments. The organization's management account is no longer charged for any expenses accrued by the member account after it's removed from the organization.</p> <p>This operation can be called only from the organization's management account. Member accounts can remove themselves with <a>LeaveOrganization</a> instead.</p> <important> <ul> <li> <p>You can remove an account from your organization only if the account is configured with the information required to operate as a standalone account. When you create an account in an organization using the Organizations console, API, or CLI commands, the information required of standalone accounts is <i>not</i> automatically collected. For more information, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_account-before-remove.html\">Considerations before removing an account from an organization</a> in the <i>Organizations User Guide</i>.</p> </li> <li> <p>The account that you want to leave must not be a delegated administrator account for any Amazon Web Services service enabled for your organization. If the account is a delegated administrator, you must first change the delegated administrator account to another account that is remaining in the organization.</p> </li> <li> <p>After the account leaves the organization, all tags that were attached to the account object in the organization are deleted. Amazon Web Services accounts outside of an organization do not support tags.</p> </li> </ul> </important>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "TagResourceRequest"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ConcurrentModificationException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "TargetNotFoundException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidInputException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Adds one or more tags to the specified resource.</p> <p>Currently, you can attach tags to the following resources in Organizations.</p> <ul> <li> <p>Amazon Web Services account</p> </li> <li> <p>Organization root</p> </li> <li> <p>Organizational unit (OU)</p> </li> <li> <p>Policy (any type)</p> </li> </ul> <p>This operation can be called only from the organization's management account or by a member account that is a delegated administrator.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UntagResourceRequest"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ConcurrentModificationException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "TargetNotFoundException"}, {"shape": "ConstraintViolationException"}, {"shape": "InvalidInputException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Removes any tags with the specified keys from the specified resource.</p> <p>You can attach tags to the following resources in Organizations.</p> <ul> <li> <p>Amazon Web Services account</p> </li> <li> <p>Organization root</p> </li> <li> <p>Organizational unit (OU)</p> </li> <li> <p>Policy (any type)</p> </li> </ul> <p>This operation can be called only from the organization's management account or by a member account that is a delegated administrator.</p>"}, "UpdateOrganizationalUnit": {"name": "UpdateOrganizationalUnit", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateOrganizationalUnitRequest"}, "output": {"shape": "UpdateOrganizationalUnitResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "ConcurrentModificationException"}, {"shape": "DuplicateOrganizationalUnitException"}, {"shape": "InvalidInputException"}, {"shape": "OrganizationalUnitNotFoundException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Renames the specified organizational unit (OU). The ID and ARN don't change. The child OUs and accounts remain in place, and any attached policies of the OU remain attached.</p> <p>This operation can be called only from the organization's management account.</p>"}, "UpdatePolicy": {"name": "UpdatePolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdatePolicyRequest"}, "output": {"shape": "UpdatePolicyResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "AWSOrganizationsNotInUseException"}, {"shape": "ConcurrentModificationException"}, {"shape": "ConstraintViolationException"}, {"shape": "DuplicatePolicyException"}, {"shape": "InvalidInputException"}, {"shape": "MalformedPolicyDocumentException"}, {"shape": "PolicyNotFoundException"}, {"shape": "ServiceException"}, {"shape": "TooManyRequestsException"}, {"shape": "UnsupportedAPIEndpointException"}, {"shape": "PolicyChangesInProgressException"}], "documentation": "<p>Updates an existing policy with a new name, description, or content. If you don't supply any parameter, that value remains unchanged. You can't change a policy's type.</p> <p>This operation can be called only from the organization's management account or by a member account that is a delegated administrator.</p>"}}, "shapes": {"AWSOrganizationsNotInUseException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>Your account isn't a member of an organization. To make this request, you must use the credentials of an account that belongs to an organization.</p>", "exception": true}, "AcceptHandshakeRequest": {"type": "structure", "required": ["HandshakeId"], "members": {"HandshakeId": {"shape": "HandshakeId", "documentation": "<p>The unique identifier (ID) of the handshake that you want to accept.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> for handshake ID string requires \"h-\" followed by from 8 to 32 lowercase letters or digits.</p>"}}}, "AcceptHandshakeResponse": {"type": "structure", "members": {"Handshake": {"shape": "Handshake", "documentation": "<p>A structure that contains details about the accepted handshake.</p>"}}}, "AccessDeniedException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>You don't have permissions to perform the requested operation. The user or role that is making the request must have at least one IAM permissions policy attached that grants the required permissions. For more information, see <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/access.html\">Access Management</a> in the <i>IAM User Guide</i>.</p>", "exception": true}, "AccessDeniedForDependencyException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}, "Reason": {"shape": "AccessDeniedForDependencyExceptionReason"}}, "documentation": "<p>The operation that you attempted requires you to have the <code>iam:CreateServiceLinkedRole</code> for <code>organizations.amazonaws.com</code> permission so that Organizations can create the required service-linked role. You don't have that permission.</p>", "exception": true}, "AccessDeniedForDependencyExceptionReason": {"type": "string", "enum": ["ACCESS_DENIED_DURING_CREATE_SERVICE_LINKED_ROLE"]}, "Account": {"type": "structure", "members": {"Id": {"shape": "AccountId", "documentation": "<p>The unique identifier (ID) of the account.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> for an account ID string requires exactly 12 digits.</p>"}, "Arn": {"shape": "AccountArn", "documentation": "<p>The Amazon Resource Name (ARN) of the account.</p> <p>For more information about ARNs in Organizations, see <a href=\"https://docs.aws.amazon.com/service-authorization/latest/reference/list_awsorganizations.html#awsorganizations-resources-for-iam-policies\">ARN Formats Supported by Organizations</a> in the <i>Amazon Web Services Service Authorization Reference</i>.</p>"}, "Email": {"shape": "Email", "documentation": "<p>The email address associated with the Amazon Web Services account.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> for this parameter is a string of characters that represents a standard internet email address.</p>"}, "Name": {"shape": "Account<PERSON><PERSON>", "documentation": "<p>The friendly name of the account.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> that is used to validate this parameter is a string of any of the characters in the ASCII character range.</p>"}, "Status": {"shape": "Account<PERSON><PERSON><PERSON>", "documentation": "<p>The status of the account in the organization.</p>"}, "JoinedMethod": {"shape": "AccountJoinedMethod", "documentation": "<p>The method by which the account joined the organization.</p>"}, "JoinedTimestamp": {"shape": "Timestamp", "documentation": "<p>The date the account became a part of the organization.</p>"}}, "documentation": "<p>Contains information about an Amazon Web Services account that is a member of an organization.</p>"}, "AccountAlreadyClosedException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>You attempted to close an account that is already closed.</p>", "exception": true}, "AccountAlreadyRegisteredException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>The specified account is already a delegated administrator for this Amazon Web Services service.</p>", "exception": true}, "AccountArn": {"type": "string", "pattern": "^arn:aws:organizations::\\d{12}:account\\/o-[a-z0-9]{10,32}\\/\\d{12}"}, "AccountId": {"type": "string", "max": 12, "pattern": "^\\d{12}$"}, "AccountJoinedMethod": {"type": "string", "enum": ["INVITED", "CREATED"]}, "AccountName": {"type": "string", "max": 128, "min": 1, "pattern": "[\\s\\S]*", "sensitive": true}, "AccountNotFoundException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p> We can't find an Amazon Web Services account with the <code>AccountId</code> that you specified, or the account whose credentials you used to make this request isn't a member of an organization.</p>", "exception": true}, "AccountNotRegisteredException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>The specified account is not a delegated administrator for this Amazon Web Services service. </p>", "exception": true}, "AccountOwnerNotVerifiedException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>You can't invite an existing account to your organization until you verify that you own the email address associated with the management account. For more information, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_create.html#about-email-verification\">Email address verification</a> in the <i>Organizations User Guide</i>.</p>", "exception": true}, "AccountStatus": {"type": "string", "enum": ["ACTIVE", "SUSPENDED", "PENDING_CLOSURE"]}, "Accounts": {"type": "list", "member": {"shape": "Account"}}, "ActionType": {"type": "string", "enum": ["INVITE", "ENABLE_ALL_FEATURES", "APPROVE_ALL_FEATURES", "ADD_ORGANIZATIONS_SERVICE_LINKED_ROLE"]}, "AlreadyInOrganizationException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>This account is already a member of an organization. An account can belong to only one organization at a time.</p>", "exception": true}, "AttachPolicyRequest": {"type": "structure", "required": ["PolicyId", "TargetId"], "members": {"PolicyId": {"shape": "PolicyId", "documentation": "<p>The unique identifier (ID) of the policy that you want to attach to the target. You can get the ID for the policy by calling the <a>ListPolicies</a> operation.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> for a policy ID string requires \"p-\" followed by from 8 to 128 lowercase or uppercase letters, digits, or the underscore character (_).</p>"}, "TargetId": {"shape": "PolicyTargetId", "documentation": "<p>The unique identifier (ID) of the root, OU, or account that you want to attach the policy to. You can get the ID by calling the <a>ListRoots</a>, <a>ListOrganizationalUnitsForParent</a>, or <a>ListAccounts</a> operations.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> for a target ID string requires one of the following:</p> <ul> <li> <p> <b>Root</b> - A string that begins with \"r-\" followed by from 4 to 32 lowercase letters or digits.</p> </li> <li> <p> <b>Account</b> - A string that consists of exactly 12 digits.</p> </li> <li> <p> <b>Organizational unit (OU)</b> - A string that begins with \"ou-\" followed by from 4 to 32 lowercase letters or digits (the ID of the root that the OU is in). This string is followed by a second \"-\" dash and from 8 to 32 additional lowercase letters or digits.</p> </li> </ul>"}}}, "AwsManagedPolicy": {"type": "boolean"}, "CancelHandshakeRequest": {"type": "structure", "required": ["HandshakeId"], "members": {"HandshakeId": {"shape": "HandshakeId", "documentation": "<p>The unique identifier (ID) of the handshake that you want to cancel. You can get the ID from the <a>ListHandshakesForOrganization</a> operation.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> for handshake ID string requires \"h-\" followed by from 8 to 32 lowercase letters or digits.</p>"}}}, "CancelHandshakeResponse": {"type": "structure", "members": {"Handshake": {"shape": "Handshake", "documentation": "<p>A structure that contains details about the handshake that you canceled.</p>"}}}, "Child": {"type": "structure", "members": {"Id": {"shape": "ChildId", "documentation": "<p>The unique identifier (ID) of this child entity.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> for a child ID string requires one of the following:</p> <ul> <li> <p> <b>Account</b> - A string that consists of exactly 12 digits.</p> </li> <li> <p> <b>Organizational unit (OU)</b> - A string that begins with \"ou-\" followed by from 4 to 32 lowercase letters or digits (the ID of the root that contains the OU). This string is followed by a second \"-\" dash and from 8 to 32 additional lowercase letters or digits.</p> </li> </ul>"}, "Type": {"shape": "ChildType", "documentation": "<p>The type of this child entity.</p>"}}, "documentation": "<p>Contains a list of child entities, either OUs or accounts.</p>"}, "ChildId": {"type": "string", "max": 100, "pattern": "^(\\d{12})|(ou-[0-9a-z]{4,32}-[a-z0-9]{8,32})$"}, "ChildNotFoundException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>We can't find an organizational unit (OU) or Amazon Web Services account with the <code>ChildId</code> that you specified.</p>", "exception": true}, "ChildType": {"type": "string", "enum": ["ACCOUNT", "ORGANIZATIONAL_UNIT"]}, "Children": {"type": "list", "member": {"shape": "Child"}}, "CloseAccountRequest": {"type": "structure", "required": ["AccountId"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>Retrieves the Amazon Web Services account Id for the current <code>CloseAccount</code> API request. </p>"}}}, "ConcurrentModificationException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>The target of the operation is currently being modified by a different request. Try again later.</p>", "exception": true}, "ConflictException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>The request failed because it conflicts with the current state of the specified resource.</p>", "exception": true}, "ConstraintViolationException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}, "Reason": {"shape": "ConstraintViolationExceptionReason"}}, "documentation": "<p>Performing this operation violates a minimum or maximum value limit. For example, attempting to remove the last service control policy (SCP) from an OU or root, inviting or creating too many accounts to the organization, or attaching too many policies to an account, OU, or root. This exception includes a reason that contains additional information about the violated limit:</p> <note> <p>Some of the reasons in the following list might not be applicable to this specific API or operation.</p> </note> <ul> <li> <p>ACCOUNT_CANNOT_LEAVE_ORGANIZATION: You attempted to remove the management account from the organization. You can't remove the management account. Instead, after you remove all member accounts, delete the organization itself.</p> </li> <li> <p>ACCOUNT_CANNOT_LEAVE_WITHOUT_PHONE_VERIFICATION: You attempted to remove an account from the organization that doesn't yet have enough information to exist as a standalone account. This account requires you to first complete phone verification. Follow the steps at <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_accounts_remove.html#orgs_manage_accounts_remove-from-master\">Removing a member account from your organization</a> in the <i>Organizations User Guide</i>.</p> </li> <li> <p>ACCOUNT_CREATION_RATE_LIMIT_EXCEEDED: You attempted to exceed the number of accounts that you can create in one day.</p> </li> <li> <p>ACCOUNT_CREATION_NOT_COMPLETE: Your account setup isn't complete or your account isn't fully active. You must complete the account setup before you create an organization.</p> </li> <li> <p>ACCOUNT_NUMBER_LIMIT_EXCEEDED: You attempted to exceed the limit on the number of accounts in an organization. If you need more accounts, contact <a href=\"https://console.aws.amazon.com/support/home#/\">Amazon Web Services Support</a> to request an increase in your limit. </p> <p>Or the number of invitations that you tried to send would cause you to exceed the limit of accounts in your organization. Send fewer invitations or contact Amazon Web Services Support to request an increase in the number of accounts.</p> <note> <p>Deleted and closed accounts still count toward your limit.</p> </note> <important> <p>If you get this exception when running a command immediately after creating the organization, wait one hour and try again. After an hour, if the command continues to fail with this error, contact <a href=\"https://console.aws.amazon.com/support/home#/\">Amazon Web Services Support</a>.</p> </important> </li> <li> <p>ALL_FEATURES_MIGRATION_ORGANIZATION_SIZE_LIMIT_EXCEEDED: Your organization has more than 5000 accounts, and you can only use the standard migration process for organizations with less than 5000 accounts. Use the assisted migration process to enable all features mode, or create a support case for assistance if you are unable to use assisted migration.</p> </li> <li> <p>CANNOT_REGISTER_SUSPENDED_ACCOUNT_AS_DELEGATED_ADMINISTRATOR: You cannot register a suspended account as a delegated administrator.</p> </li> <li> <p>CANNOT_REGISTER_MASTER_AS_DELEGATED_ADMINISTRATOR: You attempted to register the management account of the organization as a delegated administrator for an Amazon Web Services service integrated with Organizations. You can designate only a member account as a delegated administrator.</p> </li> <li> <p>CANNOT_CLOSE_MANAGEMENT_ACCOUNT: You attempted to close the management account. To close the management account for the organization, you must first either remove or close all member accounts in the organization. Follow standard account closure process using root credentials.​ </p> </li> <li> <p>CANNOT_REMOVE_DELEGATED_ADMINISTRATOR_FROM_ORG: You attempted to remove an account that is registered as a delegated administrator for a service integrated with your organization. To complete this operation, you must first deregister this account as a delegated administrator. </p> </li> <li> <p>CLOSE_ACCOUNT_QUOTA_EXCEEDED: You have exceeded close account quota for the past 30 days. </p> </li> <li> <p>CLOSE_ACCOUNT_REQUESTS_LIMIT_EXCEEDED: You attempted to exceed the number of accounts that you can close at a time. ​ </p> </li> <li> <p>CREATE_ORGANIZATION_IN_BILLING_MODE_UNSUPPORTED_REGION: To create an organization in the specified region, you must enable all features mode.</p> </li> <li> <p>DELEGATED_ADMINISTRATOR_EXISTS_FOR_THIS_SERVICE: You attempted to register an Amazon Web Services account as a delegated administrator for an Amazon Web Services service that already has a delegated administrator. To complete this operation, you must first deregister any existing delegated administrators for this service.</p> </li> <li> <p>EMAIL_VERIFICATION_CODE_EXPIRED: The email verification code is only valid for a limited period of time. You must resubmit the request and generate a new verfication code.</p> </li> <li> <p>HANDSHAKE_RATE_LIMIT_EXCEEDED: You attempted to exceed the number of handshakes that you can send in one day.</p> </li> <li> <p>INVALID_PAYMENT_INSTRUMENT: You cannot remove an account because no supported payment method is associated with the account. Amazon Web Services does not support cards issued by financial institutions in Russia or Belarus. For more information, see <a href=\"https://docs.aws.amazon.com/awsaccountbilling/latest/aboutv2/manage-general.html\">Managing your Amazon Web Services payments</a>.</p> </li> <li> <p>MASTER_ACCOUNT_ADDRESS_DOES_NOT_MATCH_MARKETPLACE: To create an account in this organization, you first must migrate the organization's management account to the marketplace that corresponds to the management account's address. All accounts in an organization must be associated with the same marketplace.</p> </li> <li> <p>MASTER_ACCOUNT_MISSING_BUSINESS_LICENSE: Applies only to the Amazon Web Services Regions in China. To create an organization, the master must have a valid business license. For more information, contact customer support.</p> </li> <li> <p>MASTER_ACCOUNT_MISSING_CONTACT_INFO: To complete this operation, you must first provide a valid contact address and phone number for the management account. Then try the operation again.</p> </li> <li> <p>MASTER_ACCOUNT_NOT_GOVCLOUD_ENABLED: To complete this operation, the management account must have an associated account in the Amazon Web Services GovCloud (US-West) Region. For more information, see <a href=\"https://docs.aws.amazon.com/govcloud-us/latest/UserGuide/govcloud-organizations.html\">Organizations</a> in the <i>Amazon Web Services GovCloud User Guide</i>.</p> </li> <li> <p>MASTER_ACCOUNT_PAYMENT_INSTRUMENT_REQUIRED: To create an organization with this management account, you first must associate a valid payment instrument, such as a credit card, with the account. For more information, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_account-before-remove.html\">Considerations before removing an account from an organization</a> in the <i>Organizations User Guide</i>.</p> </li> <li> <p>MAX_DELEGATED_ADMINISTRATORS_FOR_SERVICE_LIMIT_EXCEEDED: You attempted to register more delegated administrators than allowed for the service principal. </p> </li> <li> <p>MAX_POLICY_TYPE_ATTACHMENT_LIMIT_EXCEEDED: You attempted to exceed the number of policies of a certain type that can be attached to an entity at one time.</p> </li> <li> <p>MAX_TAG_LIMIT_EXCEEDED: You have exceeded the number of tags allowed on this resource. </p> </li> <li> <p>MEMBER_ACCOUNT_PAYMENT_INSTRUMENT_REQUIRED: To complete this operation with this member account, you first must associate a valid payment instrument, such as a credit card, with the account. For more information, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_account-before-remove.html\">Considerations before removing an account from an organization</a> in the <i>Organizations User Guide</i>.</p> </li> <li> <p>MIN_POLICY_TYPE_ATTACHMENT_LIMIT_EXCEEDED: You attempted to detach a policy from an entity that would cause the entity to have fewer than the minimum number of policies of a certain type required.</p> </li> <li> <p>ORGANIZATION_NOT_IN_ALL_FEATURES_MODE: You attempted to perform an operation that requires the organization to be configured to support all features. An organization that supports only consolidated billing features can't perform this operation.</p> </li> <li> <p>OU_DEPTH_LIMIT_EXCEEDED: You attempted to create an OU tree that is too many levels deep.</p> </li> <li> <p>OU_NUMBER_LIMIT_EXCEEDED: You attempted to exceed the number of OUs that you can have in an organization.</p> </li> <li> <p>POLICY_CONTENT_LIMIT_EXCEEDED: You attempted to create a policy that is larger than the maximum size.</p> </li> <li> <p>POLICY_NUMBER_LIMIT_EXCEEDED: You attempted to exceed the number of policies that you can have in an organization.</p> </li> <li> <p>POLICY_TYPE_ENABLED_FOR_THIS_SERVICE: You attempted to disable service access before you disabled the policy type (for example, SECURITYHUB_POLICY). To complete this operation, you must first disable the policy type.</p> </li> <li> <p>SERVICE_ACCESS_NOT_ENABLED:</p> <ul> <li> <p>You attempted to register a delegated administrator before you enabled service access. Call the <code>EnableAWSServiceAccess</code> API first.</p> </li> <li> <p>You attempted to enable a policy type before you enabled service access. Call the <code>EnableAWSServiceAccess</code> API first.</p> </li> </ul> </li> <li> <p>TAG_POLICY_VIOLATION: You attempted to create or update a resource with tags that are not compliant with the tag policy requirements for this account.</p> </li> <li> <p>WAIT_PERIOD_ACTIVE: After you create an Amazon Web Services account, you must wait until at least seven days after the account was created. Invited accounts aren't subject to this waiting period.</p> </li> </ul>", "exception": true}, "ConstraintViolationExceptionReason": {"type": "string", "enum": ["ACCOUNT_NUMBER_LIMIT_EXCEEDED", "HANDSHAKE_RATE_LIMIT_EXCEEDED", "OU_NUMBER_LIMIT_EXCEEDED", "OU_DEPTH_LIMIT_EXCEEDED", "POLICY_NUMBER_LIMIT_EXCEEDED", "POLICY_CONTENT_LIMIT_EXCEEDED", "MAX_POLICY_TYPE_ATTACHMENT_LIMIT_EXCEEDED", "MIN_POLICY_TYPE_ATTACHMENT_LIMIT_EXCEEDED", "ACCOUNT_CANNOT_LEAVE_ORGANIZATION", "ACCOUNT_CANNOT_LEAVE_WITHOUT_EULA", "ACCOUNT_CANNOT_LEAVE_WITHOUT_PHONE_VERIFICATION", "MASTER_ACCOUNT_PAYMENT_INSTRUMENT_REQUIRED", "MEMBER_ACCOUNT_PAYMENT_INSTRUMENT_REQUIRED", "ACCOUNT_CREATION_RATE_LIMIT_EXCEEDED", "MASTER_ACCOUNT_ADDRESS_DOES_NOT_MATCH_MARKETPLACE", "MASTER_ACCOUNT_MISSING_CONTACT_INFO", "MASTER_ACCOUNT_NOT_GOVCLOUD_ENABLED", "ORGANIZATION_NOT_IN_ALL_FEATURES_MODE", "CREATE_ORGANIZATION_IN_BILLING_MODE_UNSUPPORTED_REGION", "EMAIL_VERIFICATION_CODE_EXPIRED", "WAIT_PERIOD_ACTIVE", "MAX_TAG_LIMIT_EXCEEDED", "TAG_POLICY_VIOLATION", "MAX_DELEGATED_ADMINISTRATORS_FOR_SERVICE_LIMIT_EXCEEDED", "CANNOT_REGISTER_MASTER_AS_DELEGATED_ADMINISTRATOR", "CANNOT_REMOVE_DELEGATED_ADMINISTRATOR_FROM_ORG", "DELEGATED_ADMINISTRATOR_EXISTS_FOR_THIS_SERVICE", "POLICY_TYPE_ENABLED_FOR_THIS_SERVICE", "MASTER_ACCOUNT_MISSING_BUSINESS_LICENSE", "CANNOT_CLOSE_MANAGEMENT_ACCOUNT", "CLOSE_ACCOUNT_QUOTA_EXCEEDED", "CLOSE_ACCOUNT_REQUESTS_LIMIT_EXCEEDED", "SERVICE_ACCESS_NOT_ENABLED", "INVALID_PAYMENT_INSTRUMENT", "ACCOUNT_CREATION_NOT_COMPLETE", "CANNOT_REGISTER_SUSPENDED_ACCOUNT_AS_DELEGATED_ADMINISTRATOR", "ALL_FEATURES_MIGRATION_ORGANIZATION_SIZE_LIMIT_EXCEEDED"]}, "CreateAccountFailureReason": {"type": "string", "enum": ["ACCOUNT_LIMIT_EXCEEDED", "EMAIL_ALREADY_EXISTS", "INVALID_ADDRESS", "INVALID_EMAIL", "CONCURRENT_ACCOUNT_MODIFICATION", "INTERNAL_FAILURE", "GOVCLOUD_ACCOUNT_ALREADY_EXISTS", "MISSING_BUSINESS_VALIDATION", "FAILED_BUSINESS_VALIDATION", "PENDING_BUSINESS_VALIDATION", "INVALID_IDENTITY_FOR_BUSINESS_VALIDATION", "UNKNOWN_BUSINESS_VALIDATION", "MISSING_PAYMENT_INSTRUMENT", "INVALID_PAYMENT_INSTRUMENT", "UPDATE_EXISTING_RESOURCE_POLICY_WITH_TAGS_NOT_SUPPORTED"]}, "CreateAccountName": {"type": "string", "max": 50, "min": 1, "pattern": "[\\u0020-\\u007E]+", "sensitive": true}, "CreateAccountRequest": {"type": "structure", "required": ["Email", "Account<PERSON><PERSON>"], "members": {"Email": {"shape": "Email", "documentation": "<p>The email address of the owner to assign to the new member account. This email address must not already be associated with another Amazon Web Services account. You must use a valid email address to complete account creation.</p> <p>The rules for a valid email address:</p> <ul> <li> <p>The address must be a minimum of 6 and a maximum of 64 characters long.</p> </li> <li> <p>All characters must be 7-bit ASCII characters.</p> </li> <li> <p>There must be one and only one @ symbol, which separates the local name from the domain name.</p> </li> <li> <p>The local name can't contain any of the following characters:</p> <p>whitespace, \" ' ( ) &lt; &gt; [ ] : ; , \\ | % &amp;</p> </li> <li> <p>The local name can't begin with a dot (.)</p> </li> <li> <p>The domain name can consist of only the characters [a-z],[A-Z],[0-9], hyphen (-), or dot (.)</p> </li> <li> <p>The domain name can't begin or end with a hyphen (-) or dot (.)</p> </li> <li> <p>The domain name must contain at least one dot</p> </li> </ul> <p>You can't access the root user of the account or remove an account that was created with an invalid email address.</p>"}, "AccountName": {"shape": "CreateAccountName", "documentation": "<p>The friendly name of the member account.</p>"}, "RoleName": {"shape": "RoleName", "documentation": "<p>The name of an IAM role that Organizations automatically preconfigures in the new member account. This role trusts the management account, allowing users in the management account to assume the role, as permitted by the management account administrator. The role has administrator permissions in the new member account.</p> <p>If you don't specify this parameter, the role name defaults to <code>OrganizationAccountAccessRole</code>.</p> <p>For more information about how to use this role to access the member account, see the following links:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_accounts_access.html#orgs_manage_accounts_create-cross-account-role\">Creating the OrganizationAccountAccessRole in an invited member account</a> in the <i>Organizations User Guide</i> </p> </li> <li> <p>Steps 2 and 3 in <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/tutorial_cross-account-with-roles.html\">IAM Tutorial: Delegate access across Amazon Web Services accounts using IAM roles</a> in the <i>IAM User Guide</i> </p> </li> </ul> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> that is used to validate this parameter. The pattern can include uppercase letters, lowercase letters, digits with no spaces, and any of the following characters: =,.@-</p>"}, "IamUserAccessToBilling": {"shape": "IAMUserAccessToBilling", "documentation": "<p>If set to <code>ALLOW</code>, the new account enables IAM users to access account billing information <i>if</i> they have the required permissions. If set to <code>DENY</code>, only the root user of the new account can access account billing information. For more information, see <a href=\"https://docs.aws.amazon.com/awsaccountbilling/latest/aboutv2/grantaccess.html#ControllingAccessWebsite-Activate\">About IAM access to the Billing and Cost Management console</a> in the <i>Amazon Web Services Billing and Cost Management User Guide</i>.</p> <p>If you don't specify this parameter, the value defaults to <code>ALLOW</code>, and IAM users and roles with the required permissions can access billing information for the new account.</p>"}, "Tags": {"shape": "Tags", "documentation": "<p>A list of tags that you want to attach to the newly created account. For each tag in the list, you must specify both a tag key and a value. You can set the value to an empty string, but you can't set it to <code>null</code>. For more information about tagging, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_tagging.html\">Tagging Organizations resources</a> in the Organizations User Guide.</p> <note> <p>If any one of the tags is not valid or if you exceed the maximum allowed number of tags for an account, then the entire request fails and the account is not created.</p> </note>"}}}, "CreateAccountRequestId": {"type": "string", "max": 36, "pattern": "^car-[a-z0-9]{8,32}$"}, "CreateAccountResponse": {"type": "structure", "members": {"CreateAccountStatus": {"shape": "CreateAccountStatus", "documentation": "<p>A structure that contains details about the request to create an account. This response structure might not be fully populated when you first receive it because account creation is an asynchronous process. You can pass the returned <code>CreateAccountStatus</code> ID as a parameter to <a>DescribeCreateAccountStatus</a> to get status about the progress of the request at later times. You can also check the CloudTrail log for the <code>CreateAccountResult</code> event. For more information, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_security_incident-response.html\">Logging and monitoring in Organizations</a> in the <i>Organizations User Guide</i>.</p>"}}}, "CreateAccountState": {"type": "string", "enum": ["IN_PROGRESS", "SUCCEEDED", "FAILED"]}, "CreateAccountStates": {"type": "list", "member": {"shape": "CreateAccountState"}}, "CreateAccountStatus": {"type": "structure", "members": {"Id": {"shape": "CreateAccountRequestId", "documentation": "<p>The unique identifier (ID) that references this request. You get this value from the response of the initial <a>CreateAccount</a> request to create the account.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> for a create account request ID string requires \"car-\" followed by from 8 to 32 lowercase letters or digits.</p>"}, "AccountName": {"shape": "CreateAccountName", "documentation": "<p>The account name given to the account when it was created.</p>"}, "State": {"shape": "CreateAccountState", "documentation": "<p>The status of the asynchronous request to create an Amazon Web Services account.</p>"}, "RequestedTimestamp": {"shape": "Timestamp", "documentation": "<p>The date and time that the request was made for the account creation.</p>"}, "CompletedTimestamp": {"shape": "Timestamp", "documentation": "<p>The date and time that the account was created and the request completed.</p>"}, "AccountId": {"shape": "AccountId", "documentation": "<p>If the account was created successfully, the unique identifier (ID) of the new account.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> for an account ID string requires exactly 12 digits.</p>"}, "GovCloudAccountId": {"shape": "AccountId", "documentation": "<p>If the account was created successfully, the unique identifier (ID) of the new account in the Amazon Web Services GovCloud (US) Region.</p>"}, "FailureReason": {"shape": "CreateAccountFailureReason", "documentation": "<p>If the request failed, a description of the reason for the failure.</p> <ul> <li> <p>ACCOUNT_LIMIT_EXCEEDED: The account couldn't be created because you reached the limit on the number of accounts in your organization.</p> </li> <li> <p>CONCURRENT_ACCOUNT_MODIFICATION: You already submitted a request with the same information.</p> </li> <li> <p>EMAIL_ALREADY_EXISTS: The account could not be created because another Amazon Web Services account with that email address already exists.</p> </li> <li> <p>FAILED_BUSINESS_VALIDATION: The Amazon Web Services account that owns your organization failed to receive business license validation.</p> </li> <li> <p>GOVCLOUD_ACCOUNT_ALREADY_EXISTS: The account in the Amazon Web Services GovCloud (US) Region could not be created because this Region already includes an account with that email address.</p> </li> <li> <p>IDENTITY_INVALID_BUSINESS_VALIDATION: The Amazon Web Services account that owns your organization can't complete business license validation because it doesn't have valid identity data.</p> </li> <li> <p>INVALID_ADDRESS: The account could not be created because the address you provided is not valid.</p> </li> <li> <p>INVALID_EMAIL: The account could not be created because the email address you provided is not valid.</p> </li> <li> <p>INVALID_PAYMENT_INSTRUMENT: The Amazon Web Services account that owns your organization does not have a supported payment method associated with the account. Amazon Web Services does not support cards issued by financial institutions in Russia or Belarus. For more information, see <a href=\"https://docs.aws.amazon.com/awsaccountbilling/latest/aboutv2/manage-general.html\">Managing your Amazon Web Services payments</a>.</p> </li> <li> <p>INTERNAL_FAILURE: The account could not be created because of an internal failure. Try again later. If the problem persists, contact Amazon Web Services Customer Support.</p> </li> <li> <p>MISSING_BUSINESS_VALIDATION: The Amazon Web Services account that owns your organization has not received Business Validation.</p> </li> <li> <p> MISSING_PAYMENT_INSTRUMENT: You must configure the management account with a valid payment method, such as a credit card.</p> </li> <li> <p>PENDING_BUSINESS_VALIDATION: The Amazon Web Services account that owns your organization is still in the process of completing business license validation.</p> </li> <li> <p>UNKNOWN_BUSINESS_VALIDATION: The Amazon Web Services account that owns your organization has an unknown issue with business license validation.</p> </li> </ul>"}}, "documentation": "<p>Contains the status about a <a>CreateAccount</a> or <a>CreateGovCloudAccount</a> request to create an Amazon Web Services account or an Amazon Web Services GovCloud (US) account in an organization.</p>"}, "CreateAccountStatusNotFoundException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>We can't find an create account request with the <code>CreateAccountRequestId</code> that you specified.</p>", "exception": true}, "CreateAccountStatuses": {"type": "list", "member": {"shape": "CreateAccountStatus"}}, "CreateGovCloudAccountRequest": {"type": "structure", "required": ["Email", "Account<PERSON><PERSON>"], "members": {"Email": {"shape": "Email", "documentation": "<p>Specifies the email address of the owner to assign to the new member account in the commercial Region. This email address must not already be associated with another Amazon Web Services account. You must use a valid email address to complete account creation.</p> <p>The rules for a valid email address:</p> <ul> <li> <p>The address must be a minimum of 6 and a maximum of 64 characters long.</p> </li> <li> <p>All characters must be 7-bit ASCII characters.</p> </li> <li> <p>There must be one and only one @ symbol, which separates the local name from the domain name.</p> </li> <li> <p>The local name can't contain any of the following characters:</p> <p>whitespace, \" ' ( ) &lt; &gt; [ ] : ; , \\ | % &amp;</p> </li> <li> <p>The local name can't begin with a dot (.)</p> </li> <li> <p>The domain name can consist of only the characters [a-z],[A-Z],[0-9], hyphen (-), or dot (.)</p> </li> <li> <p>The domain name can't begin or end with a hyphen (-) or dot (.)</p> </li> <li> <p>The domain name must contain at least one dot</p> </li> </ul> <p>You can't access the root user of the account or remove an account that was created with an invalid email address. Like all request parameters for <code>CreateGovCloudAccount</code>, the request for the email address for the Amazon Web Services GovCloud (US) account originates from the commercial Region, not from the Amazon Web Services GovCloud (US) Region.</p>"}, "AccountName": {"shape": "CreateAccountName", "documentation": "<p>The friendly name of the member account. </p> <p>The account name can consist of only the characters [a-z],[A-Z],[0-9], hyphen (-), or dot (.) You can't separate characters with a dash (–).</p>"}, "RoleName": {"shape": "RoleName", "documentation": "<p>(Optional)</p> <p>The name of an IAM role that Organizations automatically preconfigures in the new member accounts in both the Amazon Web Services GovCloud (US) Region and in the commercial Region. This role trusts the management account, allowing users in the management account to assume the role, as permitted by the management account administrator. The role has administrator permissions in the new member account.</p> <p>If you don't specify this parameter, the role name defaults to <code>OrganizationAccountAccessRole</code>.</p> <p>For more information about how to use this role to access the member account, see the following links:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_accounts_access.html#orgs_manage_accounts_create-cross-account-role\">Creating the OrganizationAccountAccessRole in an invited member account</a> in the <i>Organizations User Guide</i> </p> </li> <li> <p>Steps 2 and 3 in <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/tutorial_cross-account-with-roles.html\">IAM Tutorial: Delegate access across Amazon Web Services accounts using IAM roles</a> in the <i>IAM User Guide</i> </p> </li> </ul> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> that is used to validate this parameter. The pattern can include uppercase letters, lowercase letters, digits with no spaces, and any of the following characters: =,.@-</p>"}, "IamUserAccessToBilling": {"shape": "IAMUserAccessToBilling", "documentation": "<p>If set to <code>ALLOW</code>, the new linked account in the commercial Region enables IAM users to access account billing information <i>if</i> they have the required permissions. If set to <code>DENY</code>, only the root user of the new account can access account billing information. For more information, see <a href=\"https://docs.aws.amazon.com/awsaccountbilling/latest/aboutv2/grantaccess.html#ControllingAccessWebsite-Activate\">About IAM access to the Billing and Cost Management console</a> in the <i>Amazon Web Services Billing and Cost Management User Guide</i>.</p> <p>If you don't specify this parameter, the value defaults to <code>ALLOW</code>, and IAM users and roles with the required permissions can access billing information for the new account.</p>"}, "Tags": {"shape": "Tags", "documentation": "<p>A list of tags that you want to attach to the newly created account. These tags are attached to the commercial account associated with the GovCloud account, and not to the GovCloud account itself. To add tags to the actual GovCloud account, call the <a>TagResource</a> operation in the GovCloud region after the new GovCloud account exists.</p> <p>For each tag in the list, you must specify both a tag key and a value. You can set the value to an empty string, but you can't set it to <code>null</code>. For more information about tagging, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_tagging.html\">Tagging Organizations resources</a> in the Organizations User Guide.</p> <note> <p>If any one of the tags is not valid or if you exceed the maximum allowed number of tags for an account, then the entire request fails and the account is not created.</p> </note>"}}}, "CreateGovCloudAccountResponse": {"type": "structure", "members": {"CreateAccountStatus": {"shape": "CreateAccountStatus"}}}, "CreateOrganizationRequest": {"type": "structure", "members": {"FeatureSet": {"shape": "OrganizationFeatureSet", "documentation": "<p>Specifies the feature set supported by the new organization. Each feature set supports different levels of functionality.</p> <ul> <li> <p> <code>CONSOLIDATED_BILLING</code>: All member accounts have their bills consolidated to and paid by the management account. For more information, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_getting-started_concepts.html#feature-set-cb-only\">Consolidated billing</a> in the <i>Organizations User Guide</i>.</p> <p> The consolidated billing feature subset isn't available for organizations in the Amazon Web Services GovCloud (US) Region.</p> </li> <li> <p> <code>ALL</code>: In addition to all the features supported by the consolidated billing feature set, the management account can also apply any policy type to any member account in the organization. For more information, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_getting-started_concepts.html#feature-set-all\">All features</a> in the <i>Organizations User Guide</i>.</p> </li> </ul>"}}}, "CreateOrganizationResponse": {"type": "structure", "members": {"Organization": {"shape": "Organization", "documentation": "<p>A structure that contains details about the newly created organization.</p>"}}}, "CreateOrganizationalUnitRequest": {"type": "structure", "required": ["ParentId", "Name"], "members": {"ParentId": {"shape": "ParentId", "documentation": "<p>The unique identifier (ID) of the parent root or OU that you want to create the new OU in.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> for a parent ID string requires one of the following:</p> <ul> <li> <p> <b>Root</b> - A string that begins with \"r-\" followed by from 4 to 32 lowercase letters or digits.</p> </li> <li> <p> <b>Organizational unit (OU)</b> - A string that begins with \"ou-\" followed by from 4 to 32 lowercase letters or digits (the ID of the root that the OU is in). This string is followed by a second \"-\" dash and from 8 to 32 additional lowercase letters or digits.</p> </li> </ul>"}, "Name": {"shape": "OrganizationalUnitName", "documentation": "<p>The friendly name to assign to the new OU.</p>"}, "Tags": {"shape": "Tags", "documentation": "<p>A list of tags that you want to attach to the newly created OU. For each tag in the list, you must specify both a tag key and a value. You can set the value to an empty string, but you can't set it to <code>null</code>. For more information about tagging, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_tagging.html\">Tagging Organizations resources</a> in the Organizations User Guide.</p> <note> <p>If any one of the tags is not valid or if you exceed the allowed number of tags for an OU, then the entire request fails and the OU is not created.</p> </note>"}}}, "CreateOrganizationalUnitResponse": {"type": "structure", "members": {"OrganizationalUnit": {"shape": "OrganizationalUnit", "documentation": "<p>A structure that contains details about the newly created OU.</p>"}}}, "CreatePolicyRequest": {"type": "structure", "required": ["Content", "Description", "Name", "Type"], "members": {"Content": {"shape": "PolicyContent", "documentation": "<p>The policy text content to add to the new policy. The text that you supply must adhere to the rules of the policy type you specify in the <code>Type</code> parameter. </p> <p>The maximum size of a policy document depends on the policy's type. For more information, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_reference_limits.html#min-max-values\">Maximum and minimum values</a> in the <i>Organizations User Guide</i>.</p>"}, "Description": {"shape": "PolicyDescription", "documentation": "<p>An optional description to assign to the policy.</p>"}, "Name": {"shape": "PolicyName", "documentation": "<p>The friendly name to assign to the policy.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> that is used to validate this parameter is a string of any of the characters in the ASCII character range.</p>"}, "Type": {"shape": "PolicyType", "documentation": "<p>The type of policy to create. You can specify one of the following values:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_scp.html\">SERVICE_CONTROL_POLICY</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_rcps.html\">RESOURCE_CONTROL_POLICY</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_declarative.html\">DECLARATIVE_POLICY_EC2</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_backup.html\">BACKUP_POLICY</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_tag-policies.html\">TAG_POLICY</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_chatbot.html\">CHATBOT_POLICY</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_ai-opt-out.html\">AISERVICES_OPT_OUT_POLICY</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_security_hub.html\">SECURITYHUB_POLICY</a> </p> </li> </ul>"}, "Tags": {"shape": "Tags", "documentation": "<p>A list of tags that you want to attach to the newly created policy. For each tag in the list, you must specify both a tag key and a value. You can set the value to an empty string, but you can't set it to <code>null</code>. For more information about tagging, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_tagging.html\">Tagging Organizations resources</a> in the Organizations User Guide.</p> <note> <p>If any one of the tags is not valid or if you exceed the allowed number of tags for a policy, then the entire request fails and the policy is not created.</p> </note>"}}}, "CreatePolicyResponse": {"type": "structure", "members": {"Policy": {"shape": "Policy", "documentation": "<p>A structure that contains details about the newly created policy.</p>"}}}, "DeclineHandshakeRequest": {"type": "structure", "required": ["HandshakeId"], "members": {"HandshakeId": {"shape": "HandshakeId", "documentation": "<p>The unique identifier (ID) of the handshake that you want to decline. You can get the ID from the <a>ListHandshakesForAccount</a> operation.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> for handshake ID string requires \"h-\" followed by from 8 to 32 lowercase letters or digits.</p>"}}}, "DeclineHandshakeResponse": {"type": "structure", "members": {"Handshake": {"shape": "Handshake", "documentation": "<p>A structure that contains details about the declined handshake. The state is updated to show the value <code>DECLINED</code>.</p>"}}}, "DelegatedAdministrator": {"type": "structure", "members": {"Id": {"shape": "AccountId", "documentation": "<p>The unique identifier (ID) of the delegated administrator's account.</p>"}, "Arn": {"shape": "AccountArn", "documentation": "<p>The Amazon Resource Name (ARN) of the delegated administrator's account.</p>"}, "Email": {"shape": "Email", "documentation": "<p>The email address that is associated with the delegated administrator's Amazon Web Services account.</p>"}, "Name": {"shape": "Account<PERSON><PERSON>", "documentation": "<p>The friendly name of the delegated administrator's account.</p>"}, "Status": {"shape": "Account<PERSON><PERSON><PERSON>", "documentation": "<p>The status of the delegated administrator's account in the organization.</p>"}, "JoinedMethod": {"shape": "AccountJoinedMethod", "documentation": "<p>The method by which the delegated administrator's account joined the organization.</p>"}, "JoinedTimestamp": {"shape": "Timestamp", "documentation": "<p>The date when the delegated administrator's account became a part of the organization.</p>"}, "DelegationEnabledDate": {"shape": "Timestamp", "documentation": "<p>The date when the account was made a delegated administrator.</p>"}}, "documentation": "<p>Contains information about the delegated administrator.</p>"}, "DelegatedAdministrators": {"type": "list", "member": {"shape": "DelegatedAdministrator"}}, "DelegatedService": {"type": "structure", "members": {"ServicePrincipal": {"shape": "ServicePrincipal", "documentation": "<p>The name of an Amazon Web Services service that can request an operation for the specified service. This is typically in the form of a URL, such as: <code> <i>servicename</i>.amazonaws.com</code>.</p>"}, "DelegationEnabledDate": {"shape": "Timestamp", "documentation": "<p>The date that the account became a delegated administrator for this service. </p>"}}, "documentation": "<p>Contains information about the Amazon Web Services service for which the account is a delegated administrator.</p>"}, "DelegatedServices": {"type": "list", "member": {"shape": "DelegatedService"}}, "DeleteOrganizationalUnitRequest": {"type": "structure", "required": ["OrganizationalUnitId"], "members": {"OrganizationalUnitId": {"shape": "OrganizationalUnitId", "documentation": "<p>The unique identifier (ID) of the organizational unit that you want to delete. You can get the ID from the <a>ListOrganizationalUnitsForParent</a> operation.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> for an organizational unit ID string requires \"ou-\" followed by from 4 to 32 lowercase letters or digits (the ID of the root that contains the OU). This string is followed by a second \"-\" dash and from 8 to 32 additional lowercase letters or digits.</p>"}}}, "DeletePolicyRequest": {"type": "structure", "required": ["PolicyId"], "members": {"PolicyId": {"shape": "PolicyId", "documentation": "<p>The unique identifier (ID) of the policy that you want to delete. You can get the ID from the <a>ListPolicies</a> or <a>ListPoliciesForTarget</a> operations.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> for a policy ID string requires \"p-\" followed by from 8 to 128 lowercase or uppercase letters, digits, or the underscore character (_).</p>"}}}, "DeregisterDelegatedAdministratorRequest": {"type": "structure", "required": ["AccountId", "ServicePrincipal"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The account ID number of the member account in the organization that you want to deregister as a delegated administrator.</p>"}, "ServicePrincipal": {"shape": "ServicePrincipal", "documentation": "<p>The service principal name of an Amazon Web Services service for which the account is a delegated administrator.</p> <p>Delegated administrator privileges are revoked for only the specified Amazon Web Services service from the member account. If the specified service is the only service for which the member account is a delegated administrator, the operation also revokes Organizations read action permissions.</p>"}}}, "DescribeAccountRequest": {"type": "structure", "required": ["AccountId"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The unique identifier (ID) of the Amazon Web Services account that you want information about. You can get the ID from the <a>ListAccounts</a> or <a>ListAccountsForParent</a> operations.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> for an account ID string requires exactly 12 digits.</p>"}}}, "DescribeAccountResponse": {"type": "structure", "members": {"Account": {"shape": "Account", "documentation": "<p>A structure that contains information about the requested account.</p>"}}}, "DescribeCreateAccountStatusRequest": {"type": "structure", "required": ["CreateAccountRequestId"], "members": {"CreateAccountRequestId": {"shape": "CreateAccountRequestId", "documentation": "<p>Specifies the <code>Id</code> value that uniquely identifies the <code>CreateAccount</code> request. You can get the value from the <code>CreateAccountStatus.Id</code> response in an earlier <a>CreateAccount</a> request, or from the <a>ListCreateAccountStatus</a> operation.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> for a create account request ID string requires \"car-\" followed by from 8 to 32 lowercase letters or digits.</p>"}}}, "DescribeCreateAccountStatusResponse": {"type": "structure", "members": {"CreateAccountStatus": {"shape": "CreateAccountStatus", "documentation": "<p>A structure that contains the current status of an account creation request.</p>"}}}, "DescribeEffectivePolicyRequest": {"type": "structure", "required": ["PolicyType"], "members": {"PolicyType": {"shape": "EffectivePolicyType", "documentation": "<p>The type of policy that you want information about. You can specify one of the following values:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_declarative.html\">DECLARATIVE_POLICY_EC2</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_backup.html\">BACKUP_POLICY</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_tag-policies.html\">TAG_POLICY</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_chatbot.html\">CHATBOT_POLICY</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_ai-opt-out.html\">AISERVICES_OPT_OUT_POLICY</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_security_hub.html\">SECURITYHUB_POLICY</a> </p> </li> </ul>"}, "TargetId": {"shape": "PolicyTargetId", "documentation": "<p>When you're signed in as the management account, specify the ID of the account that you want details about. Specifying an organization root or organizational unit (OU) as the target is not supported.</p>"}}}, "DescribeEffectivePolicyResponse": {"type": "structure", "members": {"EffectivePolicy": {"shape": "EffectivePolicy", "documentation": "<p>The contents of the effective policy.</p>"}}}, "DescribeHandshakeRequest": {"type": "structure", "required": ["HandshakeId"], "members": {"HandshakeId": {"shape": "HandshakeId", "documentation": "<p>The unique identifier (ID) of the handshake that you want information about. You can get the ID from the original call to <a>InviteAccountToOrganization</a>, or from a call to <a>ListHandshakesForAccount</a> or <a>ListHandshakesForOrganization</a>.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> for handshake ID string requires \"h-\" followed by from 8 to 32 lowercase letters or digits.</p>"}}}, "DescribeHandshakeResponse": {"type": "structure", "members": {"Handshake": {"shape": "Handshake", "documentation": "<p>A structure that contains information about the specified handshake.</p>"}}}, "DescribeOrganizationResponse": {"type": "structure", "members": {"Organization": {"shape": "Organization", "documentation": "<p>A structure that contains information about the organization.</p> <important> <p>The <code>AvailablePolicyTypes</code> part of the response is deprecated, and you shouldn't use it in your apps. It doesn't include any policy type supported by Organizations other than SCPs. In the China (Ningxia) Region, no policy type is included. To determine which policy types are enabled in your organization, use the <code> <a>ListRoots</a> </code> operation.</p> </important>"}}}, "DescribeOrganizationalUnitRequest": {"type": "structure", "required": ["OrganizationalUnitId"], "members": {"OrganizationalUnitId": {"shape": "OrganizationalUnitId", "documentation": "<p>The unique identifier (ID) of the organizational unit that you want details about. You can get the ID from the <a>ListOrganizationalUnitsForParent</a> operation.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> for an organizational unit ID string requires \"ou-\" followed by from 4 to 32 lowercase letters or digits (the ID of the root that contains the OU). This string is followed by a second \"-\" dash and from 8 to 32 additional lowercase letters or digits.</p>"}}}, "DescribeOrganizationalUnitResponse": {"type": "structure", "members": {"OrganizationalUnit": {"shape": "OrganizationalUnit", "documentation": "<p>A structure that contains details about the specified OU.</p>"}}}, "DescribePolicyRequest": {"type": "structure", "required": ["PolicyId"], "members": {"PolicyId": {"shape": "PolicyId", "documentation": "<p>The unique identifier (ID) of the policy that you want details about. You can get the ID from the <a>ListPolicies</a> or <a>ListPoliciesForTarget</a> operations.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> for a policy ID string requires \"p-\" followed by from 8 to 128 lowercase or uppercase letters, digits, or the underscore character (_).</p>"}}}, "DescribePolicyResponse": {"type": "structure", "members": {"Policy": {"shape": "Policy", "documentation": "<p>A structure that contains details about the specified policy.</p>"}}}, "DescribeResourcePolicyResponse": {"type": "structure", "members": {"ResourcePolicy": {"shape": "ResourcePolicy", "documentation": "<p>A structure that contains details about the resource policy.</p>"}}}, "DestinationParentNotFoundException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>We can't find the destination container (a root or OU) with the <code>ParentId</code> that you specified.</p>", "exception": true}, "DetachPolicyRequest": {"type": "structure", "required": ["PolicyId", "TargetId"], "members": {"PolicyId": {"shape": "PolicyId", "documentation": "<p>The unique identifier (ID) of the policy you want to detach. You can get the ID from the <a>ListPolicies</a> or <a>ListPoliciesForTarget</a> operations.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> for a policy ID string requires \"p-\" followed by from 8 to 128 lowercase or uppercase letters, digits, or the underscore character (_).</p>"}, "TargetId": {"shape": "PolicyTargetId", "documentation": "<p>The unique identifier (ID) of the root, OU, or account that you want to detach the policy from. You can get the ID from the <a>ListRoots</a>, <a>ListOrganizationalUnitsForParent</a>, or <a>ListAccounts</a> operations.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> for a target ID string requires one of the following:</p> <ul> <li> <p> <b>Root</b> - A string that begins with \"r-\" followed by from 4 to 32 lowercase letters or digits.</p> </li> <li> <p> <b>Account</b> - A string that consists of exactly 12 digits.</p> </li> <li> <p> <b>Organizational unit (OU)</b> - A string that begins with \"ou-\" followed by from 4 to 32 lowercase letters or digits (the ID of the root that the OU is in). This string is followed by a second \"-\" dash and from 8 to 32 additional lowercase letters or digits.</p> </li> </ul>"}}}, "DisableAWSServiceAccessRequest": {"type": "structure", "required": ["ServicePrincipal"], "members": {"ServicePrincipal": {"shape": "ServicePrincipal", "documentation": "<p>The service principal name of the Amazon Web Services service for which you want to disable integration with your organization. This is typically in the form of a URL, such as <code> <i>service-abbreviation</i>.amazonaws.com</code>.</p>"}}}, "DisablePolicyTypeRequest": {"type": "structure", "required": ["RootId", "PolicyType"], "members": {"RootId": {"shape": "RootId", "documentation": "<p>The unique identifier (ID) of the root in which you want to disable a policy type. You can get the ID from the <a>ListRoots</a> operation.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> for a root ID string requires \"r-\" followed by from 4 to 32 lowercase letters or digits.</p>"}, "PolicyType": {"shape": "PolicyType", "documentation": "<p>The policy type that you want to disable in this root. You can specify one of the following values:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_scp.html\">SERVICE_CONTROL_POLICY</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_rcps.html\">RESOURCE_CONTROL_POLICY</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_declarative.html\">DECLARATIVE_POLICY_EC2</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_backup.html\">BACKUP_POLICY</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_tag-policies.html\">TAG_POLICY</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_chatbot.html\">CHATBOT_POLICY</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_ai-opt-out.html\">AISERVICES_OPT_OUT_POLICY</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_security_hub.html\">SECURITYHUB_POLICY</a> </p> </li> </ul>"}}}, "DisablePolicyTypeResponse": {"type": "structure", "members": {"Root": {"shape": "Root", "documentation": "<p>A structure that shows the root with the updated list of enabled policy types.</p>"}}}, "DuplicateAccountException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>That account is already present in the specified destination.</p>", "exception": true}, "DuplicateHandshakeException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>A handshake with the same action and target already exists. For example, if you invited an account to join your organization, the invited account might already have a pending invitation from this organization. If you intend to resend an invitation to an account, ensure that existing handshakes that might be considered duplicates are canceled or declined.</p>", "exception": true}, "DuplicateOrganizationalUnitException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>An OU with the same name already exists.</p>", "exception": true}, "DuplicatePolicyAttachmentException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>The selected policy is already attached to the specified target.</p>", "exception": true}, "DuplicatePolicyException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>A policy with the same name already exists.</p>", "exception": true}, "EffectivePolicy": {"type": "structure", "members": {"PolicyContent": {"shape": "PolicyContent", "documentation": "<p>The text content of the policy.</p>"}, "LastUpdatedTimestamp": {"shape": "Timestamp", "documentation": "<p>The time of the last update to this policy.</p>"}, "TargetId": {"shape": "PolicyTargetId", "documentation": "<p>The account ID of the policy target. </p>"}, "PolicyType": {"shape": "EffectivePolicyType", "documentation": "<p>The policy type.</p>"}}, "documentation": "<p>Contains rules to be applied to the affected accounts. The effective policy is the aggregation of any policies the account inherits, plus any policy directly attached to the account.</p>"}, "EffectivePolicyNotFoundException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>If you ran this action on the management account, this policy type is not enabled. If you ran the action on a member account, the account doesn't have an effective policy of this type. Contact the administrator of your organization about attaching a policy of this type to the account. </p>", "exception": true}, "EffectivePolicyType": {"type": "string", "enum": ["TAG_POLICY", "BACKUP_POLICY", "AISERVICES_OPT_OUT_POLICY", "CHATBOT_POLICY", "DECLARATIVE_POLICY_EC2", "SECURITYHUB_POLICY"]}, "Email": {"type": "string", "max": 64, "min": 6, "pattern": "[^\\s@]+@[^\\s@]+\\.[^\\s@]+", "sensitive": true}, "EnableAWSServiceAccessRequest": {"type": "structure", "required": ["ServicePrincipal"], "members": {"ServicePrincipal": {"shape": "ServicePrincipal", "documentation": "<p>The service principal name of the Amazon Web Services service for which you want to enable integration with your organization. This is typically in the form of a URL, such as <code> <i>service-abbreviation</i>.amazonaws.com</code>.</p>"}}}, "EnableAllFeaturesRequest": {"type": "structure", "members": {}}, "EnableAllFeaturesResponse": {"type": "structure", "members": {"Handshake": {"shape": "Handshake", "documentation": "<p>A structure that contains details about the handshake created to support this request to enable all features in the organization.</p>"}}}, "EnablePolicyTypeRequest": {"type": "structure", "required": ["RootId", "PolicyType"], "members": {"RootId": {"shape": "RootId", "documentation": "<p>The unique identifier (ID) of the root in which you want to enable a policy type. You can get the ID from the <a>ListRoots</a> operation.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> for a root ID string requires \"r-\" followed by from 4 to 32 lowercase letters or digits.</p>"}, "PolicyType": {"shape": "PolicyType", "documentation": "<p>The policy type that you want to enable. You can specify one of the following values:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_scp.html\">SERVICE_CONTROL_POLICY</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_rcps.html\">RESOURCE_CONTROL_POLICY</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_declarative.html\">DECLARATIVE_POLICY_EC2</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_backup.html\">BACKUP_POLICY</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_tag-policies.html\">TAG_POLICY</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_chatbot.html\">CHATBOT_POLICY</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_ai-opt-out.html\">AISERVICES_OPT_OUT_POLICY</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_security_hub.html\">SECURITYHUB_POLICY</a> </p> </li> </ul>"}}}, "EnablePolicyTypeResponse": {"type": "structure", "members": {"Root": {"shape": "Root", "documentation": "<p>A structure that shows the root with the updated list of enabled policy types.</p>"}}}, "EnabledServicePrincipal": {"type": "structure", "members": {"ServicePrincipal": {"shape": "ServicePrincipal", "documentation": "<p>The name of the service principal. This is typically in the form of a URL, such as: <code> <i>servicename</i>.amazonaws.com</code>.</p>"}, "DateEnabled": {"shape": "Timestamp", "documentation": "<p>The date that the service principal was enabled for integration with Organizations.</p>"}}, "documentation": "<p>A structure that contains details of a service principal that represents an Amazon Web Services service that is enabled to integrate with Organizations.</p>"}, "EnabledServicePrincipals": {"type": "list", "member": {"shape": "EnabledServicePrincipal"}}, "ExceptionMessage": {"type": "string"}, "ExceptionType": {"type": "string"}, "FinalizingOrganizationException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>Organizations couldn't perform the operation because your organization hasn't finished initializing. This can take up to an hour. Try again later. If after one hour you continue to receive this error, contact <a href=\"https://console.aws.amazon.com/support/home#/\">Amazon Web Services Support</a>.</p>", "exception": true}, "GenericArn": {"type": "string", "pattern": "^arn:aws:organizations::.+:.+"}, "Handshake": {"type": "structure", "members": {"Id": {"shape": "HandshakeId", "documentation": "<p>The unique identifier (ID) of a handshake. The originating account creates the ID when it initiates the handshake.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> for handshake ID string requires \"h-\" followed by from 8 to 32 lowercase letters or digits.</p>"}, "Arn": {"shape": "Handshak<PERSON><PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of a handshake.</p> <p>For more information about ARNs in Organizations, see <a href=\"https://docs.aws.amazon.com/service-authorization/latest/reference/list_awsorganizations.html#awsorganizations-resources-for-iam-policies\">ARN Formats Supported by Organizations</a> in the <i>Amazon Web Services Service Authorization Reference</i>.</p>"}, "Parties": {"shape": "HandshakeParties", "documentation": "<p>Information about the two accounts that are participating in the handshake.</p>"}, "State": {"shape": "HandshakeState", "documentation": "<p>The current state of the handshake. Use the state to trace the flow of the handshake through the process from its creation to its acceptance. The meaning of each of the valid values is as follows:</p> <ul> <li> <p> <b>REQUESTED</b>: This handshake was sent to multiple recipients (applicable to only some handshake types) and not all recipients have responded yet. The request stays in this state until all recipients respond.</p> </li> <li> <p> <b>OPEN</b>: This handshake was sent to multiple recipients (applicable to only some policy types) and all recipients have responded, allowing the originator to complete the handshake action.</p> </li> <li> <p> <b>CANCELED</b>: This handshake is no longer active because it was canceled by the originating account.</p> </li> <li> <p> <b>ACCEPTED</b>: This handshake is complete because it has been accepted by the recipient.</p> </li> <li> <p> <b>DECLINED</b>: This handshake is no longer active because it was declined by the recipient account.</p> </li> <li> <p> <b>EXPIRED</b>: This handshake is no longer active because the originator did not receive a response of any kind from the recipient before the expiration time (15 days).</p> </li> </ul>"}, "RequestedTimestamp": {"shape": "Timestamp", "documentation": "<p>The date and time that the handshake request was made.</p>"}, "ExpirationTimestamp": {"shape": "Timestamp", "documentation": "<p>The date and time that the handshake expires. If the recipient of the handshake request fails to respond before the specified date and time, the handshake becomes inactive and is no longer valid.</p>"}, "Action": {"shape": "ActionType", "documentation": "<p>The type of handshake, indicating what action occurs when the recipient accepts the handshake. The following handshake types are supported:</p> <ul> <li> <p> <b>INVITE</b>: This type of handshake represents a request to join an organization. It is always sent from the management account to only non-member accounts.</p> </li> <li> <p> <b>ENABLE_ALL_FEATURES</b>: This type of handshake represents a request to enable all features in an organization. It is always sent from the management account to only <i>invited</i> member accounts. Created accounts do not receive this because those accounts were created by the organization's management account and approval is inferred.</p> </li> <li> <p> <b>APPROVE_ALL_FEATURES</b>: This type of handshake is sent from the Organizations service when all member accounts have approved the <code>ENABLE_ALL_FEATURES</code> invitation. It is sent only to the management account and signals the master that it can finalize the process to enable all features.</p> </li> </ul>"}, "Resources": {"shape": "HandshakeResources", "documentation": "<p>Additional information that is needed to process the handshake.</p>"}}, "documentation": "<p>Contains information that must be exchanged to securely establish a relationship between two accounts (an <i>originator</i> and a <i>recipient</i>). For example, when a management account (the originator) invites another account (the recipient) to join its organization, the two accounts exchange information as a series of handshake requests and responses.</p> <p> <b>Note:</b> Handshakes that are <code>CANCELED</code>, <code>ACCEPTED</code>, <code>DECLINED</code>, or <code>EXPIRED</code> show up in lists for only 30 days after entering that state After that they are deleted.</p>"}, "HandshakeAlreadyInStateException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>The specified handshake is already in the requested state. For example, you can't accept a handshake that was already accepted.</p>", "exception": true}, "HandshakeArn": {"type": "string", "pattern": "^arn:aws:organizations::\\d{12}:handshake\\/o-[a-z0-9]{10,32}\\/[a-z_]{1,32}\\/h-[0-9a-z]{8,32}"}, "HandshakeConstraintViolationException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}, "Reason": {"shape": "HandshakeConstraintViolationExceptionReason"}}, "documentation": "<p>The requested operation would violate the constraint identified in the reason code.</p> <note> <p>Some of the reasons in the following list might not be applicable to this specific API or operation:</p> </note> <ul> <li> <p>ACCOUNT_NUMBER_LIMIT_EXCEEDED: You attempted to exceed the limit on the number of accounts in an organization. Note that deleted and closed accounts still count toward your limit.</p> <important> <p>If you get this exception immediately after creating the organization, wait one hour and try again. If after an hour it continues to fail with this error, contact <a href=\"https://console.aws.amazon.com/support/home#/\">Amazon Web Services Support</a>.</p> </important> </li> <li> <p>ALREADY_IN_AN_ORGANIZATION: The handshake request is invalid because the invited account is already a member of an organization.</p> </li> <li> <p>HANDSHAKE_RATE_LIMIT_EXCEEDED: You attempted to exceed the number of handshakes that you can send in one day.</p> </li> <li> <p>INVITE_DISABLED_DURING_ENABLE_ALL_FEATURES: You can't issue new invitations to join an organization while it's in the process of enabling all features. You can resume inviting accounts after you finalize the process when all accounts have agreed to the change.</p> </li> <li> <p>ORGANIZATION_ALREADY_HAS_ALL_FEATURES: The handshake request is invalid because the organization has already enabled all features.</p> </li> <li> <p>ORGANIZATION_IS_ALREADY_PENDING_ALL_FEATURES_MIGRATION: The handshake request is invalid because the organization has already started the process to enable all features.</p> </li> <li> <p>ORGANIZATION_FROM_DIFFERENT_SELLER_OF_RECORD: The request failed because the account is from a different marketplace than the accounts in the organization.</p> </li> <li> <p>ORGANIZATION_MEMBERSHIP_CHANGE_RATE_LIMIT_EXCEEDED: You attempted to change the membership of an account too quickly after its previous change.</p> </li> <li> <p>PAYMENT_INSTRUMENT_REQUIRED: You can't complete the operation with an account that doesn't have a payment instrument, such as a credit card, associated with it.</p> </li> </ul>", "exception": true}, "HandshakeConstraintViolationExceptionReason": {"type": "string", "enum": ["ACCOUNT_NUMBER_LIMIT_EXCEEDED", "HANDSHAKE_RATE_LIMIT_EXCEEDED", "ALREADY_IN_AN_ORGANIZATION", "ORGANIZATION_ALREADY_HAS_ALL_FEATURES", "ORGANIZATION_IS_ALREADY_PENDING_ALL_FEATURES_MIGRATION", "INVITE_DISABLED_DURING_ENABLE_ALL_FEATURES", "PAYMENT_INSTRUMENT_REQUIRED", "ORGANIZATION_FROM_DIFFERENT_SELLER_OF_RECORD", "ORGANIZATION_MEMBERSHIP_CHANGE_RATE_LIMIT_EXCEEDED", "MANAGEMENT_ACCOUNT_EMAIL_NOT_VERIFIED"]}, "HandshakeFilter": {"type": "structure", "members": {"ActionType": {"shape": "ActionType", "documentation": "<p>Specifies the type of handshake action.</p> <p>If you specify <code>ActionType</code>, you cannot also specify <code>ParentHandshakeId</code>.</p>"}, "ParentHandshakeId": {"shape": "HandshakeId", "documentation": "<p>Specifies the parent handshake. Only used for handshake types that are a child of another type.</p> <p>If you specify <code>ParentHandshakeId</code>, you cannot also specify <code>ActionType</code>.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> for handshake ID string requires \"h-\" followed by from 8 to 32 lowercase letters or digits.</p>"}}, "documentation": "<p>Specifies the criteria that are used to select the handshakes for the operation.</p>"}, "HandshakeId": {"type": "string", "max": 34, "pattern": "^h-[0-9a-z]{8,32}$"}, "HandshakeNotFoundException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>We can't find a handshake with the <code>HandshakeId</code> that you specified.</p>", "exception": true}, "HandshakeNotes": {"type": "string", "max": 1024, "pattern": "[\\s\\S]*", "sensitive": true}, "HandshakeParties": {"type": "list", "member": {"shape": "<PERSON>hak<PERSON><PERSON><PERSON><PERSON>"}}, "HandshakeParty": {"type": "structure", "required": ["Id", "Type"], "members": {"Id": {"shape": "HandshakePartyId", "documentation": "<p>The unique identifier (ID) for the party.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> for handshake ID string requires \"h-\" followed by from 8 to 32 lowercase letters or digits.</p>"}, "Type": {"shape": "HandshakePartyType", "documentation": "<p>The type of party.</p>"}}, "documentation": "<p>Identifies a participant in a handshake.</p>"}, "HandshakePartyId": {"type": "string", "max": 64, "min": 1, "pattern": "[\\s\\S]*", "sensitive": true}, "HandshakePartyType": {"type": "string", "enum": ["ACCOUNT", "ORGANIZATION", "EMAIL"]}, "HandshakeResource": {"type": "structure", "members": {"Value": {"shape": "HandshakeResourceValue", "documentation": "<p>The information that is passed to the other party in the handshake. The format of the value string must match the requirements of the specified type.</p>"}, "Type": {"shape": "HandshakeResourceType", "documentation": "<p>The type of information being passed, specifying how the value is to be interpreted by the other party:</p> <ul> <li> <p> <code>ACCOUNT</code> - Specifies an Amazon Web Services account ID number.</p> </li> <li> <p> <code>ORGANIZATION</code> - Specifies an organization ID number.</p> </li> <li> <p> <code>EMAIL</code> - Specifies the email address that is associated with the account that receives the handshake. </p> </li> <li> <p> <code>OWNER_EMAIL</code> - Specifies the email address associated with the management account. Included as information about an organization. </p> </li> <li> <p> <code>OWNER_NAME</code> - Specifies the name associated with the management account. Included as information about an organization. </p> </li> <li> <p> <code>NOTES</code> - Additional text provided by the handshake initiator and intended for the recipient to read.</p> </li> </ul>"}, "Resources": {"shape": "HandshakeResources", "documentation": "<p>When needed, contains an additional array of <code>HandshakeResource</code> objects.</p>"}}, "documentation": "<p>Contains additional data that is needed to process a handshake.</p>"}, "HandshakeResourceType": {"type": "string", "enum": ["ACCOUNT", "ORGANIZATION", "ORGANIZATION_FEATURE_SET", "EMAIL", "MASTER_EMAIL", "MASTER_NAME", "NOTES", "PARENT_HANDSHAKE"]}, "HandshakeResourceValue": {"type": "string", "sensitive": true}, "HandshakeResources": {"type": "list", "member": {"shape": "HandshakeResource"}}, "HandshakeState": {"type": "string", "enum": ["REQUESTED", "OPEN", "CANCELED", "ACCEPTED", "DECLINED", "EXPIRED"]}, "Handshakes": {"type": "list", "member": {"shape": "Handshake"}}, "IAMUserAccessToBilling": {"type": "string", "enum": ["ALLOW", "DENY"]}, "InvalidHandshakeTransitionException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>You can't perform the operation on the handshake in its current state. For example, you can't cancel a handshake that was already accepted or accept a handshake that was already declined.</p>", "exception": true}, "InvalidInputException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}, "Reason": {"shape": "InvalidInputExceptionReason"}}, "documentation": "<p>The requested operation failed because you provided invalid values for one or more of the request parameters. This exception includes a reason that contains additional information about the violated limit:</p> <note> <p>Some of the reasons in the following list might not be applicable to this specific API or operation.</p> </note> <ul> <li> <p>DUPLICATE_TAG_KEY: Tag keys must be unique among the tags attached to the same entity.</p> </li> <li> <p>IMMUTABLE_POLICY: You specified a policy that is managed by Amazon Web Services and can't be modified.</p> </li> <li> <p>INPUT_REQUIRED: You must include a value for all required parameters.</p> </li> <li> <p>INVALID_EMAIL_ADDRESS_TARGET: You specified an invalid email address for the invited account owner.</p> </li> <li> <p>INVALID_ENUM: You specified an invalid value.</p> </li> <li> <p>INVALID_ENUM_POLICY_TYPE: You specified an invalid policy type string.</p> </li> <li> <p>INVALID_FULL_NAME_TARGET: You specified a full name that contains invalid characters.</p> </li> <li> <p>INVALID_LIST_MEMBER: You provided a list to a parameter that contains at least one invalid value.</p> </li> <li> <p>INVALID_PAGINATION_TOKEN: Get the value for the <code>NextToken</code> parameter from the response to a previous call of the operation.</p> </li> <li> <p>INVALID_PARTY_TYPE_TARGET: You specified the wrong type of entity (account, organization, or email) as a party.</p> </li> <li> <p>INVALID_PATTERN: You provided a value that doesn't match the required pattern.</p> </li> <li> <p>INVALID_PATTERN_TARGET_ID: You specified a policy target ID that doesn't match the required pattern.</p> </li> <li> <p>INVALID_PRINCIPAL: You specified an invalid principal element in the policy.</p> </li> <li> <p>INVALID_ROLE_NAME: You provided a role name that isn't valid. A role name can't begin with the reserved prefix <code>AWSServiceRoleFor</code>.</p> </li> <li> <p>INVALID_SYNTAX_ORGANIZATION_ARN: You specified an invalid Amazon Resource Name (ARN) for the organization.</p> </li> <li> <p>INVALID_SYNTAX_POLICY_ID: You specified an invalid policy ID. </p> </li> <li> <p>INVALID_SYSTEM_TAGS_PARAMETER: You specified a tag key that is a system tag. You can’t add, edit, or delete system tag keys because they're reserved for Amazon Web Services use. System tags don’t count against your tags per resource limit.</p> </li> <li> <p>MAX_FILTER_LIMIT_EXCEEDED: You can specify only one filter parameter for the operation.</p> </li> <li> <p>MAX_LENGTH_EXCEEDED: You provided a string parameter that is longer than allowed.</p> </li> <li> <p>MAX_VALUE_EXCEEDED: You provided a numeric parameter that has a larger value than allowed.</p> </li> <li> <p>MIN_LENGTH_EXCEEDED: You provided a string parameter that is shorter than allowed.</p> </li> <li> <p>MIN_VALUE_EXCEEDED: You provided a numeric parameter that has a smaller value than allowed.</p> </li> <li> <p>MOVING_ACCOUNT_BETWEEN_DIFFERENT_ROOTS: You can move an account only between entities in the same root.</p> </li> <li> <p>NON_DETACHABLE_POLICY: You can't detach this Amazon Web Services Managed Policy.</p> </li> <li> <p>TARGET_NOT_SUPPORTED: You can't perform the specified operation on that target entity.</p> </li> <li> <p>UNRECOGNIZED_SERVICE_PRINCIPAL: You specified a service principal that isn't recognized.</p> </li> </ul>", "exception": true}, "InvalidInputExceptionReason": {"type": "string", "enum": ["INVALID_PARTY_TYPE_TARGET", "INVALID_SYNTAX_ORGANIZATION_ARN", "INVALID_SYNTAX_POLICY_ID", "INVALID_ENUM", "INVALID_ENUM_POLICY_TYPE", "INVALID_LIST_MEMBER", "MAX_LENGTH_EXCEEDED", "MAX_VALUE_EXCEEDED", "MIN_LENGTH_EXCEEDED", "MIN_VALUE_EXCEEDED", "IMMUTABLE_POLICY", "INVALID_PATTERN", "INVALID_PATTERN_TARGET_ID", "INPUT_REQUIRED", "INVALID_NEXT_TOKEN", "MAX_LIMIT_EXCEEDED_FILTER", "MOVING_ACCOUNT_BETWEEN_DIFFERENT_ROOTS", "INVALID_FULL_NAME_TARGET", "UNRECOGNIZED_SERVICE_PRINCIPAL", "INVALID_ROLE_NAME", "INVALID_SYSTEM_TAGS_PARAMETER", "DUPLICATE_TAG_KEY", "TARGET_NOT_SUPPORTED", "INVALID_EMAIL_ADDRESS_TARGET", "INVALID_RESOURCE_POLICY_JSON", "INVALID_PRINCIPAL", "UNSUPPORTED_ACTION_IN_RESOURCE_POLICY", "UNSUPPORTED_POLICY_TYPE_IN_RESOURCE_POLICY", "UNSUPPORTED_RESOURCE_IN_RESOURCE_POLICY", "NON_DETACHABLE_POLICY"]}, "InviteAccountToOrganizationRequest": {"type": "structure", "required": ["Target"], "members": {"Target": {"shape": "<PERSON>hak<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The identifier (ID) of the Amazon Web Services account that you want to invite to join your organization. This is a JSON object that contains the following elements:</p> <p> <code>{ \"Type\": \"ACCOUNT\", \"Id\": \"&lt;<i> <b>account id number</b> </i>&gt;\" }</code> </p> <p>If you use the CLI, you can submit this as a single string, similar to the following example:</p> <p> <code>--target Id=************,Type=ACCOUNT</code> </p> <p>If you specify <code>\"Type\": \"ACCOUNT\"</code>, you must provide the Amazon Web Services account ID number as the <code>Id</code>. If you specify <code>\"Type\": \"EMAIL\"</code>, you must specify the email address that is associated with the account.</p> <p> <code>--target Id=<EMAIL>,Type=EMAIL</code> </p>"}, "Notes": {"shape": "Handshak<PERSON><PERSON><PERSON>", "documentation": "<p>Additional information that you want to include in the generated email to the recipient account owner.</p>"}, "Tags": {"shape": "Tags", "documentation": "<p>A list of tags that you want to attach to the account when it becomes a member of the organization. For each tag in the list, you must specify both a tag key and a value. You can set the value to an empty string, but you can't set it to <code>null</code>. For more information about tagging, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_tagging.html\">Tagging Organizations resources</a> in the Organizations User Guide.</p> <important> <p>Any tags in the request are checked for compliance with any applicable tag policies when the request is made. The request is rejected if the tags in the request don't match the requirements of the policy at that time. Tag policy compliance is <i> <b>not</b> </i> checked again when the invitation is accepted and the tags are actually attached to the account. That means that if the tag policy changes between the invitation and the acceptance, then that tags could potentially be non-compliant.</p> </important> <note> <p>If any one of the tags is not valid or if you exceed the allowed number of tags for an account, then the entire request fails and invitations are not sent.</p> </note>"}}}, "InviteAccountToOrganizationResponse": {"type": "structure", "members": {"Handshake": {"shape": "Handshake", "documentation": "<p>A structure that contains details about the handshake that is created to support this invitation request.</p>"}}}, "ListAWSServiceAccessForOrganizationRequest": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>The parameter for receiving additional results if you receive a <code>NextToken</code> response in a previous request. A <code>NextToken</code> response indicates that more output is available. Set this parameter to the value of the previous call's <code>NextToken</code> response to indicate where the output should continue from.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The total number of results that you want included on each page of the response. If you do not include this parameter, it defaults to a value that is specific to the operation. If additional items exist beyond the maximum you specify, the <code>NextToken</code> response element is present and has a value (is not null). Include that value as the <code>NextToken</code> request parameter in the next call to the operation to get the next part of the results. Note that Organizations might return fewer results than the maximum even when there are more results available. You should check <code>NextToken</code> after every operation to ensure that you receive all of the results.</p>"}}}, "ListAWSServiceAccessForOrganizationResponse": {"type": "structure", "members": {"EnabledServicePrincipals": {"shape": "EnabledServicePrincipals", "documentation": "<p>A list of the service principals for the services that are enabled to integrate with your organization. Each principal is a structure that includes the name and the date that it was enabled for integration with Organizations.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If present, indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>.</p>"}}}, "ListAccountsForParentRequest": {"type": "structure", "required": ["ParentId"], "members": {"ParentId": {"shape": "ParentId", "documentation": "<p>The unique identifier (ID) for the parent root or organization unit (OU) whose accounts you want to list.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The parameter for receiving additional results if you receive a <code>NextToken</code> response in a previous request. A <code>NextToken</code> response indicates that more output is available. Set this parameter to the value of the previous call's <code>NextToken</code> response to indicate where the output should continue from.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The total number of results that you want included on each page of the response. If you do not include this parameter, it defaults to a value that is specific to the operation. If additional items exist beyond the maximum you specify, the <code>NextToken</code> response element is present and has a value (is not null). Include that value as the <code>NextToken</code> request parameter in the next call to the operation to get the next part of the results. Note that Organizations might return fewer results than the maximum even when there are more results available. You should check <code>NextToken</code> after every operation to ensure that you receive all of the results.</p>"}}}, "ListAccountsForParentResponse": {"type": "structure", "members": {"Accounts": {"shape": "Accounts", "documentation": "<p>A list of the accounts in the specified root or OU.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If present, indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>.</p>"}}}, "ListAccountsRequest": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>The parameter for receiving additional results if you receive a <code>NextToken</code> response in a previous request. A <code>NextToken</code> response indicates that more output is available. Set this parameter to the value of the previous call's <code>NextToken</code> response to indicate where the output should continue from.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The total number of results that you want included on each page of the response. If you do not include this parameter, it defaults to a value that is specific to the operation. If additional items exist beyond the maximum you specify, the <code>NextToken</code> response element is present and has a value (is not null). Include that value as the <code>NextToken</code> request parameter in the next call to the operation to get the next part of the results. Note that Organizations might return fewer results than the maximum even when there are more results available. You should check <code>NextToken</code> after every operation to ensure that you receive all of the results.</p>"}}}, "ListAccountsResponse": {"type": "structure", "members": {"Accounts": {"shape": "Accounts", "documentation": "<p>A list of objects in the organization.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If present, indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>.</p>"}}}, "ListChildrenRequest": {"type": "structure", "required": ["ParentId", "ChildType"], "members": {"ParentId": {"shape": "ParentId", "documentation": "<p>The unique identifier (ID) for the parent root or OU whose children you want to list.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> for a parent ID string requires one of the following:</p> <ul> <li> <p> <b>Root</b> - A string that begins with \"r-\" followed by from 4 to 32 lowercase letters or digits.</p> </li> <li> <p> <b>Organizational unit (OU)</b> - A string that begins with \"ou-\" followed by from 4 to 32 lowercase letters or digits (the ID of the root that the OU is in). This string is followed by a second \"-\" dash and from 8 to 32 additional lowercase letters or digits.</p> </li> </ul>"}, "ChildType": {"shape": "ChildType", "documentation": "<p>Filters the output to include only the specified child type.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The parameter for receiving additional results if you receive a <code>NextToken</code> response in a previous request. A <code>NextToken</code> response indicates that more output is available. Set this parameter to the value of the previous call's <code>NextToken</code> response to indicate where the output should continue from.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The total number of results that you want included on each page of the response. If you do not include this parameter, it defaults to a value that is specific to the operation. If additional items exist beyond the maximum you specify, the <code>NextToken</code> response element is present and has a value (is not null). Include that value as the <code>NextToken</code> request parameter in the next call to the operation to get the next part of the results. Note that Organizations might return fewer results than the maximum even when there are more results available. You should check <code>NextToken</code> after every operation to ensure that you receive all of the results.</p>"}}}, "ListChildrenResponse": {"type": "structure", "members": {"Children": {"shape": "Children", "documentation": "<p>The list of children of the specified parent container.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If present, indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>.</p>"}}}, "ListCreateAccountStatusRequest": {"type": "structure", "members": {"States": {"shape": "CreateAccountStates", "documentation": "<p>A list of one or more states that you want included in the response. If this parameter isn't present, all requests are included in the response.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The parameter for receiving additional results if you receive a <code>NextToken</code> response in a previous request. A <code>NextToken</code> response indicates that more output is available. Set this parameter to the value of the previous call's <code>NextToken</code> response to indicate where the output should continue from.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The total number of results that you want included on each page of the response. If you do not include this parameter, it defaults to a value that is specific to the operation. If additional items exist beyond the maximum you specify, the <code>NextToken</code> response element is present and has a value (is not null). Include that value as the <code>NextToken</code> request parameter in the next call to the operation to get the next part of the results. Note that Organizations might return fewer results than the maximum even when there are more results available. You should check <code>NextToken</code> after every operation to ensure that you receive all of the results.</p>"}}}, "ListCreateAccountStatusResponse": {"type": "structure", "members": {"CreateAccountStatuses": {"shape": "CreateAccountStatuses", "documentation": "<p>A list of objects with details about the requests. Certain elements, such as the accountId number, are present in the output only after the account has been successfully created.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If present, indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>.</p>"}}}, "ListDelegatedAdministratorsRequest": {"type": "structure", "members": {"ServicePrincipal": {"shape": "ServicePrincipal", "documentation": "<p>Specifies a service principal name. If specified, then the operation lists the delegated administrators only for the specified service.</p> <p>If you don't specify a service principal, the operation lists all delegated administrators for all services in your organization.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The parameter for receiving additional results if you receive a <code>NextToken</code> response in a previous request. A <code>NextToken</code> response indicates that more output is available. Set this parameter to the value of the previous call's <code>NextToken</code> response to indicate where the output should continue from.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The total number of results that you want included on each page of the response. If you do not include this parameter, it defaults to a value that is specific to the operation. If additional items exist beyond the maximum you specify, the <code>NextToken</code> response element is present and has a value (is not null). Include that value as the <code>NextToken</code> request parameter in the next call to the operation to get the next part of the results. Note that Organizations might return fewer results than the maximum even when there are more results available. You should check <code>NextToken</code> after every operation to ensure that you receive all of the results.</p>"}}}, "ListDelegatedAdministratorsResponse": {"type": "structure", "members": {"DelegatedAdministrators": {"shape": "DelegatedAdministrators", "documentation": "<p>The list of delegated administrators in your organization.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If present, indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>.</p>"}}}, "ListDelegatedServicesForAccountRequest": {"type": "structure", "required": ["AccountId"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The account ID number of a delegated administrator account in the organization.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The parameter for receiving additional results if you receive a <code>NextToken</code> response in a previous request. A <code>NextToken</code> response indicates that more output is available. Set this parameter to the value of the previous call's <code>NextToken</code> response to indicate where the output should continue from.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The total number of results that you want included on each page of the response. If you do not include this parameter, it defaults to a value that is specific to the operation. If additional items exist beyond the maximum you specify, the <code>NextToken</code> response element is present and has a value (is not null). Include that value as the <code>NextToken</code> request parameter in the next call to the operation to get the next part of the results. Note that Organizations might return fewer results than the maximum even when there are more results available. You should check <code>NextToken</code> after every operation to ensure that you receive all of the results.</p>"}}}, "ListDelegatedServicesForAccountResponse": {"type": "structure", "members": {"DelegatedServices": {"shape": "DelegatedServices", "documentation": "<p>The services for which the account is a delegated administrator.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If present, indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>.</p>"}}}, "ListHandshakesForAccountRequest": {"type": "structure", "members": {"Filter": {"shape": "Handshake<PERSON><PERSON><PERSON>", "documentation": "<p>Filters the handshakes that you want included in the response. The default is all types. Use the <code>ActionType</code> element to limit the output to only a specified type, such as <code>INVITE</code>, <code>ENABLE_ALL_FEATURES</code>, or <code>APPROVE_ALL_FEATURES</code>. Alternatively, for the <code>ENABLE_ALL_FEATURES</code> handshake that generates a separate child handshake for each member account, you can specify <code>ParentHandshakeId</code> to see only the handshakes that were generated by that parent request.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The parameter for receiving additional results if you receive a <code>NextToken</code> response in a previous request. A <code>NextToken</code> response indicates that more output is available. Set this parameter to the value of the previous call's <code>NextToken</code> response to indicate where the output should continue from.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The total number of results that you want included on each page of the response. If you do not include this parameter, it defaults to a value that is specific to the operation. If additional items exist beyond the maximum you specify, the <code>NextToken</code> response element is present and has a value (is not null). Include that value as the <code>NextToken</code> request parameter in the next call to the operation to get the next part of the results. Note that Organizations might return fewer results than the maximum even when there are more results available. You should check <code>NextToken</code> after every operation to ensure that you receive all of the results.</p>"}}}, "ListHandshakesForAccountResponse": {"type": "structure", "members": {"Handshakes": {"shape": "Handshakes", "documentation": "<p>A list of <a>Handshake</a> objects with details about each of the handshakes that is associated with the specified account.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If present, indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>.</p>"}}}, "ListHandshakesForOrganizationRequest": {"type": "structure", "members": {"Filter": {"shape": "Handshake<PERSON><PERSON><PERSON>", "documentation": "<p>A filter of the handshakes that you want included in the response. The default is all types. Use the <code>ActionType</code> element to limit the output to only a specified type, such as <code>INVITE</code>, <code>ENABLE-ALL-FEATURES</code>, or <code>APPROVE-ALL-FEATURES</code>. Alternatively, for the <code>ENABLE-ALL-FEATURES</code> handshake that generates a separate child handshake for each member account, you can specify the <code>ParentHandshakeId</code> to see only the handshakes that were generated by that parent request.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The parameter for receiving additional results if you receive a <code>NextToken</code> response in a previous request. A <code>NextToken</code> response indicates that more output is available. Set this parameter to the value of the previous call's <code>NextToken</code> response to indicate where the output should continue from.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The total number of results that you want included on each page of the response. If you do not include this parameter, it defaults to a value that is specific to the operation. If additional items exist beyond the maximum you specify, the <code>NextToken</code> response element is present and has a value (is not null). Include that value as the <code>NextToken</code> request parameter in the next call to the operation to get the next part of the results. Note that Organizations might return fewer results than the maximum even when there are more results available. You should check <code>NextToken</code> after every operation to ensure that you receive all of the results.</p>"}}}, "ListHandshakesForOrganizationResponse": {"type": "structure", "members": {"Handshakes": {"shape": "Handshakes", "documentation": "<p>A list of <a>Handshake</a> objects with details about each of the handshakes that are associated with an organization.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If present, indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>.</p>"}}}, "ListOrganizationalUnitsForParentRequest": {"type": "structure", "required": ["ParentId"], "members": {"ParentId": {"shape": "ParentId", "documentation": "<p>The unique identifier (ID) of the root or OU whose child OUs you want to list.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> for a parent ID string requires one of the following:</p> <ul> <li> <p> <b>Root</b> - A string that begins with \"r-\" followed by from 4 to 32 lowercase letters or digits.</p> </li> <li> <p> <b>Organizational unit (OU)</b> - A string that begins with \"ou-\" followed by from 4 to 32 lowercase letters or digits (the ID of the root that the OU is in). This string is followed by a second \"-\" dash and from 8 to 32 additional lowercase letters or digits.</p> </li> </ul>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The parameter for receiving additional results if you receive a <code>NextToken</code> response in a previous request. A <code>NextToken</code> response indicates that more output is available. Set this parameter to the value of the previous call's <code>NextToken</code> response to indicate where the output should continue from.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The total number of results that you want included on each page of the response. If you do not include this parameter, it defaults to a value that is specific to the operation. If additional items exist beyond the maximum you specify, the <code>NextToken</code> response element is present and has a value (is not null). Include that value as the <code>NextToken</code> request parameter in the next call to the operation to get the next part of the results. Note that Organizations might return fewer results than the maximum even when there are more results available. You should check <code>NextToken</code> after every operation to ensure that you receive all of the results.</p>"}}}, "ListOrganizationalUnitsForParentResponse": {"type": "structure", "members": {"OrganizationalUnits": {"shape": "OrganizationalUnits", "documentation": "<p>A list of the OUs in the specified root or parent OU.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If present, indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>.</p>"}}}, "ListParentsRequest": {"type": "structure", "required": ["ChildId"], "members": {"ChildId": {"shape": "ChildId", "documentation": "<p>The unique identifier (ID) of the OU or account whose parent containers you want to list. Don't specify a root.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> for a child ID string requires one of the following:</p> <ul> <li> <p> <b>Account</b> - A string that consists of exactly 12 digits.</p> </li> <li> <p> <b>Organizational unit (OU)</b> - A string that begins with \"ou-\" followed by from 4 to 32 lowercase letters or digits (the ID of the root that contains the OU). This string is followed by a second \"-\" dash and from 8 to 32 additional lowercase letters or digits.</p> </li> </ul>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The parameter for receiving additional results if you receive a <code>NextToken</code> response in a previous request. A <code>NextToken</code> response indicates that more output is available. Set this parameter to the value of the previous call's <code>NextToken</code> response to indicate where the output should continue from.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The total number of results that you want included on each page of the response. If you do not include this parameter, it defaults to a value that is specific to the operation. If additional items exist beyond the maximum you specify, the <code>NextToken</code> response element is present and has a value (is not null). Include that value as the <code>NextToken</code> request parameter in the next call to the operation to get the next part of the results. Note that Organizations might return fewer results than the maximum even when there are more results available. You should check <code>NextToken</code> after every operation to ensure that you receive all of the results.</p>"}}}, "ListParentsResponse": {"type": "structure", "members": {"Parents": {"shape": "Parents", "documentation": "<p>A list of parents for the specified child account or OU.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If present, indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>.</p>"}}}, "ListPoliciesForTargetRequest": {"type": "structure", "required": ["TargetId", "Filter"], "members": {"TargetId": {"shape": "PolicyTargetId", "documentation": "<p>The unique identifier (ID) of the root, organizational unit, or account whose policies you want to list.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> for a target ID string requires one of the following:</p> <ul> <li> <p> <b>Root</b> - A string that begins with \"r-\" followed by from 4 to 32 lowercase letters or digits.</p> </li> <li> <p> <b>Account</b> - A string that consists of exactly 12 digits.</p> </li> <li> <p> <b>Organizational unit (OU)</b> - A string that begins with \"ou-\" followed by from 4 to 32 lowercase letters or digits (the ID of the root that the OU is in). This string is followed by a second \"-\" dash and from 8 to 32 additional lowercase letters or digits.</p> </li> </ul>"}, "Filter": {"shape": "PolicyType", "documentation": "<p>The type of policy that you want to include in the returned list. You must specify one of the following values:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_scp.html\">SERVICE_CONTROL_POLICY</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_rcps.html\">RESOURCE_CONTROL_POLICY</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_declarative.html\">DECLARATIVE_POLICY_EC2</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_backup.html\">BACKUP_POLICY</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_tag-policies.html\">TAG_POLICY</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_chatbot.html\">CHATBOT_POLICY</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_ai-opt-out.html\">AISERVICES_OPT_OUT_POLICY</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_security_hub.html\">SECURITYHUB_POLICY</a> </p> </li> </ul>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The parameter for receiving additional results if you receive a <code>NextToken</code> response in a previous request. A <code>NextToken</code> response indicates that more output is available. Set this parameter to the value of the previous call's <code>NextToken</code> response to indicate where the output should continue from.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The total number of results that you want included on each page of the response. If you do not include this parameter, it defaults to a value that is specific to the operation. If additional items exist beyond the maximum you specify, the <code>NextToken</code> response element is present and has a value (is not null). Include that value as the <code>NextToken</code> request parameter in the next call to the operation to get the next part of the results. Note that Organizations might return fewer results than the maximum even when there are more results available. You should check <code>NextToken</code> after every operation to ensure that you receive all of the results.</p>"}}}, "ListPoliciesForTargetResponse": {"type": "structure", "members": {"Policies": {"shape": "Policies", "documentation": "<p>The list of policies that match the criteria in the request.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If present, indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>.</p>"}}}, "ListPoliciesRequest": {"type": "structure", "required": ["Filter"], "members": {"Filter": {"shape": "PolicyType", "documentation": "<p>Specifies the type of policy that you want to include in the response. You must specify one of the following values:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_scp.html\">SERVICE_CONTROL_POLICY</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_rcps.html\">RESOURCE_CONTROL_POLICY</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_declarative.html\">DECLARATIVE_POLICY_EC2</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_backup.html\">BACKUP_POLICY</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_tag-policies.html\">TAG_POLICY</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_chatbot.html\">CHATBOT_POLICY</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_ai-opt-out.html\">AISERVICES_OPT_OUT_POLICY</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_security_hub.html\">SECURITYHUB_POLICY</a> </p> </li> </ul>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The parameter for receiving additional results if you receive a <code>NextToken</code> response in a previous request. A <code>NextToken</code> response indicates that more output is available. Set this parameter to the value of the previous call's <code>NextToken</code> response to indicate where the output should continue from.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The total number of results that you want included on each page of the response. If you do not include this parameter, it defaults to a value that is specific to the operation. If additional items exist beyond the maximum you specify, the <code>NextToken</code> response element is present and has a value (is not null). Include that value as the <code>NextToken</code> request parameter in the next call to the operation to get the next part of the results. Note that Organizations might return fewer results than the maximum even when there are more results available. You should check <code>NextToken</code> after every operation to ensure that you receive all of the results.</p>"}}}, "ListPoliciesResponse": {"type": "structure", "members": {"Policies": {"shape": "Policies", "documentation": "<p>A list of policies that match the filter criteria in the request. The output list doesn't include the policy contents. To see the content for a policy, see <a>DescribePolicy</a>.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If present, indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>.</p>"}}}, "ListRootsRequest": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>The parameter for receiving additional results if you receive a <code>NextToken</code> response in a previous request. A <code>NextToken</code> response indicates that more output is available. Set this parameter to the value of the previous call's <code>NextToken</code> response to indicate where the output should continue from.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The total number of results that you want included on each page of the response. If you do not include this parameter, it defaults to a value that is specific to the operation. If additional items exist beyond the maximum you specify, the <code>NextToken</code> response element is present and has a value (is not null). Include that value as the <code>NextToken</code> request parameter in the next call to the operation to get the next part of the results. Note that Organizations might return fewer results than the maximum even when there are more results available. You should check <code>NextToken</code> after every operation to ensure that you receive all of the results.</p>"}}}, "ListRootsResponse": {"type": "structure", "members": {"Roots": {"shape": "Roots", "documentation": "<p>A list of roots that are defined in an organization.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If present, indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["ResourceId"], "members": {"ResourceId": {"shape": "TaggableResourceId", "documentation": "<p>The ID of the resource with the tags to list.</p> <p>You can specify any of the following taggable resources.</p> <ul> <li> <p>Amazon Web Services account – specify the account ID number.</p> </li> <li> <p>Organizational unit – specify the OU ID that begins with <code>ou-</code> and looks similar to: <code>ou-<i>1a2b-34uvwxyz</i> </code> </p> </li> <li> <p>Root – specify the root ID that begins with <code>r-</code> and looks similar to: <code>r-<i>1a2b</i> </code> </p> </li> <li> <p>Policy – specify the policy ID that begins with <code>p-</code> andlooks similar to: <code>p-<i>12abcdefg3</i> </code> </p> </li> </ul>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The parameter for receiving additional results if you receive a <code>NextToken</code> response in a previous request. A <code>NextToken</code> response indicates that more output is available. Set this parameter to the value of the previous call's <code>NextToken</code> response to indicate where the output should continue from.</p>"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"Tags": {"shape": "Tags", "documentation": "<p>The tags that are assigned to the resource.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If present, indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>.</p>"}}}, "ListTargetsForPolicyRequest": {"type": "structure", "required": ["PolicyId"], "members": {"PolicyId": {"shape": "PolicyId", "documentation": "<p>The unique identifier (ID) of the policy whose attachments you want to know.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> for a policy ID string requires \"p-\" followed by from 8 to 128 lowercase or uppercase letters, digits, or the underscore character (_).</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The parameter for receiving additional results if you receive a <code>NextToken</code> response in a previous request. A <code>NextToken</code> response indicates that more output is available. Set this parameter to the value of the previous call's <code>NextToken</code> response to indicate where the output should continue from.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The total number of results that you want included on each page of the response. If you do not include this parameter, it defaults to a value that is specific to the operation. If additional items exist beyond the maximum you specify, the <code>NextToken</code> response element is present and has a value (is not null). Include that value as the <code>NextToken</code> request parameter in the next call to the operation to get the next part of the results. Note that Organizations might return fewer results than the maximum even when there are more results available. You should check <code>NextToken</code> after every operation to ensure that you receive all of the results.</p>"}}}, "ListTargetsForPolicyResponse": {"type": "structure", "members": {"Targets": {"shape": "PolicyTargets", "documentation": "<p>A list of structures, each of which contains details about one of the entities to which the specified policy is attached.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If present, indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>.</p>"}}}, "MalformedPolicyDocumentException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>The provided policy document doesn't meet the requirements of the specified policy type. For example, the syntax might be incorrect. For details about service control policy syntax, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_scps_syntax.html\">SCP syntax</a> in the <i>Organizations User Guide</i>.</p>", "exception": true}, "MasterCannotLeaveOrganizationException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>You can't remove a management account from an organization. If you want the management account to become a member account in another organization, you must first delete the current organization of the management account.</p>", "exception": true}, "MaxResults": {"type": "integer", "box": true, "max": 20, "min": 1}, "MoveAccountRequest": {"type": "structure", "required": ["AccountId", "SourceParentId", "DestinationParentId"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The unique identifier (ID) of the account that you want to move.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> for an account ID string requires exactly 12 digits.</p>"}, "SourceParentId": {"shape": "ParentId", "documentation": "<p>The unique identifier (ID) of the root or organizational unit that you want to move the account from.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> for a parent ID string requires one of the following:</p> <ul> <li> <p> <b>Root</b> - A string that begins with \"r-\" followed by from 4 to 32 lowercase letters or digits.</p> </li> <li> <p> <b>Organizational unit (OU)</b> - A string that begins with \"ou-\" followed by from 4 to 32 lowercase letters or digits (the ID of the root that the OU is in). This string is followed by a second \"-\" dash and from 8 to 32 additional lowercase letters or digits.</p> </li> </ul>"}, "DestinationParentId": {"shape": "ParentId", "documentation": "<p>The unique identifier (ID) of the root or organizational unit that you want to move the account to.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> for a parent ID string requires one of the following:</p> <ul> <li> <p> <b>Root</b> - A string that begins with \"r-\" followed by from 4 to 32 lowercase letters or digits.</p> </li> <li> <p> <b>Organizational unit (OU)</b> - A string that begins with \"ou-\" followed by from 4 to 32 lowercase letters or digits (the ID of the root that the OU is in). This string is followed by a second \"-\" dash and from 8 to 32 additional lowercase letters or digits.</p> </li> </ul>"}}}, "NextToken": {"type": "string", "max": 100000, "pattern": "[\\s\\S]*"}, "Organization": {"type": "structure", "members": {"Id": {"shape": "OrganizationId", "documentation": "<p>The unique identifier (ID) of an organization.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> for an organization ID string requires \"o-\" followed by from 10 to 32 lowercase letters or digits.</p>"}, "Arn": {"shape": "OrganizationArn", "documentation": "<p>The Amazon Resource Name (ARN) of an organization.</p> <p>For more information about ARNs in Organizations, see <a href=\"https://docs.aws.amazon.com/service-authorization/latest/reference/list_awsorganizations.html#awsorganizations-resources-for-iam-policies\">ARN Formats Supported by Organizations</a> in the <i>Amazon Web Services Service Authorization Reference</i>.</p>"}, "FeatureSet": {"shape": "OrganizationFeatureSet", "documentation": "<p>Specifies the functionality that currently is available to the organization. If set to \"ALL\", then all features are enabled and policies can be applied to accounts in the organization. If set to \"CONSOLIDATED_BILLING\", then only consolidated billing functionality is available. For more information, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_org_support-all-features.html\">Enabling all features in your organization</a> in the <i>Organizations User Guide</i>.</p>"}, "MasterAccountArn": {"shape": "AccountArn", "documentation": "<p>The Amazon Resource Name (ARN) of the account that is designated as the management account for the organization.</p> <p>For more information about ARNs in Organizations, see <a href=\"https://docs.aws.amazon.com/service-authorization/latest/reference/list_awsorganizations.html#awsorganizations-resources-for-iam-policies\">ARN Formats Supported by Organizations</a> in the <i>Amazon Web Services Service Authorization Reference</i>.</p>"}, "MasterAccountId": {"shape": "AccountId", "documentation": "<p>The unique identifier (ID) of the management account of an organization.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> for an account ID string requires exactly 12 digits.</p>"}, "MasterAccountEmail": {"shape": "Email", "documentation": "<p>The email address that is associated with the Amazon Web Services account that is designated as the management account for the organization.</p>"}, "AvailablePolicyTypes": {"shape": "PolicyTypes", "documentation": "<important> <p>Do not use. This field is deprecated and doesn't provide complete information about the policies in your organization.</p> </important> <p>To determine the policies that are enabled and available for use in your organization, use the <a>ListRoots</a> operation instead.</p>"}}, "documentation": "<p>Contains details about an organization. An organization is a collection of accounts that are centrally managed together using consolidated billing, organized hierarchically with organizational units (OUs), and controlled with policies .</p>"}, "OrganizationArn": {"type": "string", "pattern": "^arn:aws:organizations::\\d{12}:organization\\/o-[a-z0-9]{10,32}"}, "OrganizationFeatureSet": {"type": "string", "enum": ["ALL", "CONSOLIDATED_BILLING"]}, "OrganizationId": {"type": "string", "pattern": "^o-[a-z0-9]{10,32}$"}, "OrganizationNotEmptyException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>The organization isn't empty. To delete an organization, you must first remove all accounts except the management account.</p>", "exception": true}, "OrganizationalUnit": {"type": "structure", "members": {"Id": {"shape": "OrganizationalUnitId", "documentation": "<p>The unique identifier (ID) associated with this OU. The ID is unique to the organization only.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> for an organizational unit ID string requires \"ou-\" followed by from 4 to 32 lowercase letters or digits (the ID of the root that contains the OU). This string is followed by a second \"-\" dash and from 8 to 32 additional lowercase letters or digits.</p>"}, "Arn": {"shape": "OrganizationalUnitArn", "documentation": "<p>The Amazon Resource Name (ARN) of this OU.</p> <p>For more information about ARNs in Organizations, see <a href=\"https://docs.aws.amazon.com/service-authorization/latest/reference/list_awsorganizations.html#awsorganizations-resources-for-iam-policies\">ARN Formats Supported by Organizations</a> in the <i>Amazon Web Services Service Authorization Reference</i>.</p>"}, "Name": {"shape": "OrganizationalUnitName", "documentation": "<p>The friendly name of this OU.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> that is used to validate this parameter is a string of any of the characters in the ASCII character range.</p>"}}, "documentation": "<p>Contains details about an organizational unit (OU). An OU is a container of Amazon Web Services accounts within a root of an organization. Policies that are attached to an OU apply to all accounts contained in that OU and in any child OUs.</p>"}, "OrganizationalUnitArn": {"type": "string", "pattern": "^arn:aws:organizations::\\d{12}:ou\\/o-[a-z0-9]{10,32}\\/ou-[0-9a-z]{4,32}-[0-9a-z]{8,32}"}, "OrganizationalUnitId": {"type": "string", "max": 68, "pattern": "^ou-[0-9a-z]{4,32}-[a-z0-9]{8,32}$"}, "OrganizationalUnitName": {"type": "string", "max": 128, "min": 1, "pattern": "[\\s\\S]*"}, "OrganizationalUnitNotEmptyException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>The specified OU is not empty. Move all accounts to another root or to other OUs, remove all child OUs, and try the operation again.</p>", "exception": true}, "OrganizationalUnitNotFoundException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>We can't find an OU with the <code>OrganizationalUnitId</code> that you specified.</p>", "exception": true}, "OrganizationalUnits": {"type": "list", "member": {"shape": "OrganizationalUnit"}}, "Parent": {"type": "structure", "members": {"Id": {"shape": "ParentId", "documentation": "<p>The unique identifier (ID) of the parent entity.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> for a parent ID string requires one of the following:</p> <ul> <li> <p> <b>Root</b> - A string that begins with \"r-\" followed by from 4 to 32 lowercase letters or digits.</p> </li> <li> <p> <b>Organizational unit (OU)</b> - A string that begins with \"ou-\" followed by from 4 to 32 lowercase letters or digits (the ID of the root that the OU is in). This string is followed by a second \"-\" dash and from 8 to 32 additional lowercase letters or digits.</p> </li> </ul>"}, "Type": {"shape": "ParentType", "documentation": "<p>The type of the parent entity.</p>"}}, "documentation": "<p>Contains information about either a root or an organizational unit (OU) that can contain OUs or accounts in an organization.</p>"}, "ParentId": {"type": "string", "max": 100, "pattern": "^(r-[0-9a-z]{4,32})|(ou-[0-9a-z]{4,32}-[a-z0-9]{8,32})$"}, "ParentNotFoundException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>We can't find a root or OU with the <code>ParentId</code> that you specified.</p>", "exception": true}, "ParentType": {"type": "string", "enum": ["ROOT", "ORGANIZATIONAL_UNIT"]}, "Parents": {"type": "list", "member": {"shape": "Parent"}}, "Policies": {"type": "list", "member": {"shape": "PolicySummary"}}, "Policy": {"type": "structure", "members": {"PolicySummary": {"shape": "PolicySummary", "documentation": "<p>A structure that contains additional details about the policy.</p>"}, "Content": {"shape": "PolicyContent", "documentation": "<p>The text content of the policy.</p>"}}, "documentation": "<p>Contains rules to be applied to the affected accounts. Policies can be attached directly to accounts, or to roots and OUs to affect all accounts in those hierarchies.</p>"}, "PolicyArn": {"type": "string", "pattern": "^(arn:aws:organizations::\\d{12}:policy\\/o-[a-z0-9]{10,32}\\/[0-9a-z_]+\\/p-[0-9a-z]{10,32})|(arn:aws:organizations::aws:policy\\/[0-9a-z_]+\\/p-[0-9a-zA-Z_]{10,128})"}, "PolicyChangesInProgressException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>Changes to the effective policy are in progress, and its contents can't be returned. Try the operation again later. </p>", "exception": true}, "PolicyContent": {"type": "string", "min": 1, "pattern": "[\\s\\S]*"}, "PolicyDescription": {"type": "string", "max": 512, "pattern": "[\\s\\S]*"}, "PolicyId": {"type": "string", "max": 130, "pattern": "^p-[0-9a-zA-Z_]{8,128}$"}, "PolicyInUseException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>The policy is attached to one or more entities. You must detach it from all roots, OUs, and accounts before performing this operation.</p>", "exception": true}, "PolicyName": {"type": "string", "max": 128, "min": 1, "pattern": "[\\s\\S]*"}, "PolicyNotAttachedException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>The policy isn't attached to the specified target in the specified root.</p>", "exception": true}, "PolicyNotFoundException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>We can't find a policy with the <code>PolicyId</code> that you specified.</p>", "exception": true}, "PolicySummary": {"type": "structure", "members": {"Id": {"shape": "PolicyId", "documentation": "<p>The unique identifier (ID) of the policy.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> for a policy ID string requires \"p-\" followed by from 8 to 128 lowercase or uppercase letters, digits, or the underscore character (_).</p>"}, "Arn": {"shape": "PolicyArn", "documentation": "<p>The Amazon Resource Name (ARN) of the policy.</p> <p>For more information about ARNs in Organizations, see <a href=\"https://docs.aws.amazon.com/service-authorization/latest/reference/list_awsorganizations.html#awsorganizations-resources-for-iam-policies\">ARN Formats Supported by Organizations</a> in the <i>Amazon Web Services Service Authorization Reference</i>.</p>"}, "Name": {"shape": "PolicyName", "documentation": "<p>The friendly name of the policy.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> that is used to validate this parameter is a string of any of the characters in the ASCII character range.</p>"}, "Description": {"shape": "PolicyDescription", "documentation": "<p>The description of the policy.</p>"}, "Type": {"shape": "PolicyType", "documentation": "<p>The type of policy.</p>"}, "AwsManaged": {"shape": "AwsManagedPolicy", "documentation": "<p>A boolean value that indicates whether the specified policy is an Amazon Web Services managed policy. If true, then you can attach the policy to roots, OUs, or accounts, but you cannot edit it.</p>"}}, "documentation": "<p>Contains information about a policy, but does not include the content. To see the content of a policy, see <a>DescribePolicy</a>.</p>"}, "PolicyTargetId": {"type": "string", "max": 100, "pattern": "^(r-[0-9a-z]{4,32})|(\\d{12})|(ou-[0-9a-z]{4,32}-[a-z0-9]{8,32})$"}, "PolicyTargetSummary": {"type": "structure", "members": {"TargetId": {"shape": "PolicyTargetId", "documentation": "<p>The unique identifier (ID) of the policy target.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> for a target ID string requires one of the following:</p> <ul> <li> <p> <b>Root</b> - A string that begins with \"r-\" followed by from 4 to 32 lowercase letters or digits.</p> </li> <li> <p> <b>Account</b> - A string that consists of exactly 12 digits.</p> </li> <li> <p> <b>Organizational unit (OU)</b> - A string that begins with \"ou-\" followed by from 4 to 32 lowercase letters or digits (the ID of the root that the OU is in). This string is followed by a second \"-\" dash and from 8 to 32 additional lowercase letters or digits.</p> </li> </ul>"}, "Arn": {"shape": "GenericArn", "documentation": "<p>The Amazon Resource Name (ARN) of the policy target.</p> <p>For more information about ARNs in Organizations, see <a href=\"https://docs.aws.amazon.com/service-authorization/latest/reference/list_awsorganizations.html#awsorganizations-resources-for-iam-policies\">ARN Formats Supported by Organizations</a> in the <i>Amazon Web Services Service Authorization Reference</i>.</p>"}, "Name": {"shape": "TargetName", "documentation": "<p>The friendly name of the policy target.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> that is used to validate this parameter is a string of any of the characters in the ASCII character range.</p>"}, "Type": {"shape": "TargetType", "documentation": "<p>The type of the policy target.</p>"}}, "documentation": "<p>Contains information about a root, OU, or account that a policy is attached to.</p>"}, "PolicyTargets": {"type": "list", "member": {"shape": "PolicyTargetSummary"}}, "PolicyType": {"type": "string", "enum": ["SERVICE_CONTROL_POLICY", "RESOURCE_CONTROL_POLICY", "TAG_POLICY", "BACKUP_POLICY", "AISERVICES_OPT_OUT_POLICY", "CHATBOT_POLICY", "DECLARATIVE_POLICY_EC2", "SECURITYHUB_POLICY"]}, "PolicyTypeAlreadyEnabledException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>The specified policy type is already enabled in the specified root.</p>", "exception": true}, "PolicyTypeNotAvailableForOrganizationException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>You can't use the specified policy type with the feature set currently enabled for this organization. For example, you can enable SCPs only after you enable all features in the organization. For more information, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies.html#enable_policies_on_root\">Managing Organizations policies</a>in the <i>Organizations User Guide</i>.</p>", "exception": true}, "PolicyTypeNotEnabledException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>The specified policy type isn't currently enabled in this root. You can't attach policies of the specified type to entities in a root until you enable that type in the root. For more information, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_org_support-all-features.html\">Enabling all features in your organization</a> in the <i>Organizations User Guide</i>.</p>", "exception": true}, "PolicyTypeStatus": {"type": "string", "enum": ["ENABLED", "PENDING_ENABLE", "PENDING_DISABLE"]}, "PolicyTypeSummary": {"type": "structure", "members": {"Type": {"shape": "PolicyType", "documentation": "<p>The name of the policy type.</p>"}, "Status": {"shape": "PolicyTypeStatus", "documentation": "<p>The status of the policy type as it relates to the associated root. To attach a policy of the specified type to a root or to an OU or account in that root, it must be available in the organization and enabled for that root.</p>"}}, "documentation": "<p>Contains information about a policy type and its status in the associated root.</p>"}, "PolicyTypes": {"type": "list", "member": {"shape": "PolicyTypeSummary"}}, "PutResourcePolicyRequest": {"type": "structure", "required": ["Content"], "members": {"Content": {"shape": "ResourcePolicyContent", "documentation": "<p>If provided, the new content for the resource policy. The text must be correctly formatted JSON that complies with the syntax for the resource policy's type. For more information, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_scps_syntax.html\">SCP syntax</a> in the <i>Organizations User Guide</i>.</p>"}, "Tags": {"shape": "Tags", "documentation": "<p>A list of tags that you want to attach to the newly created resource policy. For each tag in the list, you must specify both a tag key and a value. You can set the value to an empty string, but you can't set it to <code>null</code>. For more information about tagging, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_tagging.html\">Tagging Organizations resources</a> in the Organizations User Guide.</p> <note> <p>Calls with tags apply to the initial creation of the resource policy, otherwise an exception is thrown. If any one of the tags is not valid or if you exceed the allowed number of tags for the resource policy, then the entire request fails and the resource policy is not created. </p> </note>"}}}, "PutResourcePolicyResponse": {"type": "structure", "members": {"ResourcePolicy": {"shape": "ResourcePolicy", "documentation": "<p>A structure that contains details about the resource policy.</p>"}}}, "RegisterDelegatedAdministratorRequest": {"type": "structure", "required": ["AccountId", "ServicePrincipal"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The account ID number of the member account in the organization to register as a delegated administrator.</p>"}, "ServicePrincipal": {"shape": "ServicePrincipal", "documentation": "<p>The service principal of the Amazon Web Services service for which you want to make the member account a delegated administrator.</p>"}}}, "RemoveAccountFromOrganizationRequest": {"type": "structure", "required": ["AccountId"], "members": {"AccountId": {"shape": "AccountId", "documentation": "<p>The unique identifier (ID) of the member account that you want to remove from the organization.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> for an account ID string requires exactly 12 digits.</p>"}}}, "ResourcePolicy": {"type": "structure", "members": {"ResourcePolicySummary": {"shape": "ResourcePolicySummary", "documentation": "<p>A structure that contains resource policy ID and Amazon Resource Name (ARN).</p>"}, "Content": {"shape": "ResourcePolicyContent", "documentation": "<p>The policy text of the resource policy.</p>"}}, "documentation": "<p>A structure that contains details about a resource policy.</p>"}, "ResourcePolicyArn": {"type": "string", "pattern": "^arn:[a-z0-9][a-z0-9-.]{0,62}:organizations::\\d{12}:resourcepolicy\\/o-[a-z0-9]{10,32}\\/rp-[0-9a-zA-Z_]{4,128}"}, "ResourcePolicyContent": {"type": "string", "max": 40000, "min": 1, "pattern": "[\\s\\S]*"}, "ResourcePolicyId": {"type": "string", "max": 131, "pattern": "^rp-[0-9a-zA-Z_]{4,128}$"}, "ResourcePolicyNotFoundException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>We can't find a resource policy request with the parameter that you specified.</p>", "exception": true}, "ResourcePolicySummary": {"type": "structure", "members": {"Id": {"shape": "ResourcePolicyId", "documentation": "<p>The unique identifier (ID) of the resource policy.</p>"}, "Arn": {"shape": "ResourcePolicyArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource policy.</p>"}}, "documentation": "<p>A structure that contains resource policy ID and Amazon Resource Name (ARN).</p>"}, "RoleName": {"type": "string", "max": 64, "pattern": "[\\w+=,.@-]{1,64}"}, "Root": {"type": "structure", "members": {"Id": {"shape": "RootId", "documentation": "<p>The unique identifier (ID) for the root. The ID is unique to the organization only.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> for a root ID string requires \"r-\" followed by from 4 to 32 lowercase letters or digits.</p>"}, "Arn": {"shape": "RootArn", "documentation": "<p>The Amazon Resource Name (ARN) of the root.</p> <p>For more information about ARNs in Organizations, see <a href=\"https://docs.aws.amazon.com/service-authorization/latest/reference/list_awsorganizations.html#awsorganizations-resources-for-iam-policies\">ARN Formats Supported by Organizations</a> in the <i>Amazon Web Services Service Authorization Reference</i>.</p>"}, "Name": {"shape": "RootName", "documentation": "<p>The friendly name of the root.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> that is used to validate this parameter is a string of any of the characters in the ASCII character range.</p>"}, "PolicyTypes": {"shape": "PolicyTypes", "documentation": "<p>The types of policies that are currently enabled for the root and therefore can be attached to the root or to its OUs or accounts.</p> <note> <p>Even if a policy type is shown as available in the organization, you can separately enable and disable them at the root level by using <a>EnablePolicyType</a> and <a>DisablePolicyType</a>. Use <a>DescribeOrganization</a> to see the availability of the policy types in that organization.</p> </note>"}}, "documentation": "<p>Contains details about a root. A root is a top-level parent node in the hierarchy of an organization that can contain organizational units (OUs) and accounts. The root contains every Amazon Web Services account in the organization.</p>"}, "RootArn": {"type": "string", "pattern": "^arn:aws:organizations::\\d{12}:root\\/o-[a-z0-9]{10,32}\\/r-[0-9a-z]{4,32}"}, "RootId": {"type": "string", "max": 34, "pattern": "^r-[0-9a-z]{4,32}$"}, "RootName": {"type": "string", "max": 128, "min": 1}, "RootNotFoundException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>We can't find a root with the <code>RootId</code> that you specified.</p>", "exception": true}, "Roots": {"type": "list", "member": {"shape": "Root"}}, "ServiceException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>Organizations can't complete your request because of an internal service error. Try again later.</p>", "exception": true, "fault": true}, "ServicePrincipal": {"type": "string", "max": 128, "min": 1, "pattern": "[\\w+=,.@-]*"}, "SourceParentNotFoundException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>We can't find a source root or OU with the <code>ParentId</code> that you specified.</p>", "exception": true}, "Tag": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {"shape": "TagKey", "documentation": "<p>The key identifier, or name, of the tag.</p>"}, "Value": {"shape": "TagValue", "documentation": "<p>The string value that's associated with the key of the tag. You can set the value of a tag to an empty string, but you can't set the value of a tag to null.</p>"}}, "documentation": "<p>A custom key-value pair associated with a resource within your organization.</p> <p>You can attach tags to any of the following organization resources.</p> <ul> <li> <p>Amazon Web Services account</p> </li> <li> <p>Organizational unit (OU)</p> </li> <li> <p>Organization root</p> </li> <li> <p>Policy</p> </li> </ul>"}, "TagKey": {"type": "string", "max": 128, "min": 1, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "TagKeys": {"type": "list", "member": {"shape": "TagKey"}}, "TagResourceRequest": {"type": "structure", "required": ["ResourceId", "Tags"], "members": {"ResourceId": {"shape": "TaggableResourceId", "documentation": "<p>The ID of the resource to add a tag to.</p> <p>You can specify any of the following taggable resources.</p> <ul> <li> <p>Amazon Web Services account – specify the account ID number.</p> </li> <li> <p>Organizational unit – specify the OU ID that begins with <code>ou-</code> and looks similar to: <code>ou-<i>1a2b-34uvwxyz</i> </code> </p> </li> <li> <p>Root – specify the root ID that begins with <code>r-</code> and looks similar to: <code>r-<i>1a2b</i> </code> </p> </li> <li> <p>Policy – specify the policy ID that begins with <code>p-</code> andlooks similar to: <code>p-<i>12abcdefg3</i> </code> </p> </li> </ul>"}, "Tags": {"shape": "Tags", "documentation": "<p>A list of tags to add to the specified resource.</p> <p>For each tag in the list, you must specify both a tag key and a value. The value can be an empty string, but you can't set it to <code>null</code>.</p> <note> <p>If any one of the tags is not valid or if you exceed the maximum allowed number of tags for a resource, then the entire request fails.</p> </note>"}}}, "TagValue": {"type": "string", "max": 256, "min": 0, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "TaggableResourceId": {"type": "string", "max": 130, "pattern": "^(r-[0-9a-z]{4,32})|(\\d{12})|(ou-[0-9a-z]{4,32}-[a-z0-9]{8,32})|(^p-[0-9a-zA-Z_]{8,128})|(^rp-[0-9a-zA-Z_]{4,128})$"}, "Tags": {"type": "list", "member": {"shape": "Tag"}}, "TargetName": {"type": "string", "max": 128, "min": 1}, "TargetNotFoundException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>We can't find a root, OU, account, or policy with the <code>TargetId</code> that you specified.</p>", "exception": true}, "TargetType": {"type": "string", "enum": ["ACCOUNT", "ORGANIZATIONAL_UNIT", "ROOT"]}, "Timestamp": {"type": "timestamp"}, "TooManyRequestsException": {"type": "structure", "members": {"Type": {"shape": "ExceptionType"}, "Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>You have sent too many requests in too short a period of time. The quota helps protect against denial-of-service attacks. Try again later.</p> <p>For information about quotas that affect Organizations, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_reference_limits.html\">Quotas for Organizations</a> in the <i>Organizations User Guide</i>.</p>", "exception": true}, "UnsupportedAPIEndpointException": {"type": "structure", "members": {"Message": {"shape": "ExceptionMessage"}}, "documentation": "<p>This action isn't available in the current Amazon Web Services Region.</p>", "exception": true}, "UntagResourceRequest": {"type": "structure", "required": ["ResourceId", "TagKeys"], "members": {"ResourceId": {"shape": "TaggableResourceId", "documentation": "<p>The ID of the resource to remove a tag from.</p> <p>You can specify any of the following taggable resources.</p> <ul> <li> <p>Amazon Web Services account – specify the account ID number.</p> </li> <li> <p>Organizational unit – specify the OU ID that begins with <code>ou-</code> and looks similar to: <code>ou-<i>1a2b-34uvwxyz</i> </code> </p> </li> <li> <p>Root – specify the root ID that begins with <code>r-</code> and looks similar to: <code>r-<i>1a2b</i> </code> </p> </li> <li> <p>Policy – specify the policy ID that begins with <code>p-</code> andlooks similar to: <code>p-<i>12abcdefg3</i> </code> </p> </li> </ul>"}, "TagKeys": {"shape": "TagKeys", "documentation": "<p>The list of keys for tags to remove from the specified resource.</p>"}}}, "UpdateOrganizationalUnitRequest": {"type": "structure", "required": ["OrganizationalUnitId"], "members": {"OrganizationalUnitId": {"shape": "OrganizationalUnitId", "documentation": "<p>The unique identifier (ID) of the OU that you want to rename. You can get the ID from the <a>ListOrganizationalUnitsForParent</a> operation.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> for an organizational unit ID string requires \"ou-\" followed by from 4 to 32 lowercase letters or digits (the ID of the root that contains the OU). This string is followed by a second \"-\" dash and from 8 to 32 additional lowercase letters or digits.</p>"}, "Name": {"shape": "OrganizationalUnitName", "documentation": "<p>The new name that you want to assign to the OU.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> that is used to validate this parameter is a string of any of the characters in the ASCII character range.</p>"}}}, "UpdateOrganizationalUnitResponse": {"type": "structure", "members": {"OrganizationalUnit": {"shape": "OrganizationalUnit", "documentation": "<p>A structure that contains the details about the specified OU, including its new name.</p>"}}}, "UpdatePolicyRequest": {"type": "structure", "required": ["PolicyId"], "members": {"PolicyId": {"shape": "PolicyId", "documentation": "<p>The unique identifier (ID) of the policy that you want to update.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> for a policy ID string requires \"p-\" followed by from 8 to 128 lowercase or uppercase letters, digits, or the underscore character (_).</p>"}, "Name": {"shape": "PolicyName", "documentation": "<p>If provided, the new name for the policy.</p> <p>The <a href=\"http://wikipedia.org/wiki/regex\">regex pattern</a> that is used to validate this parameter is a string of any of the characters in the ASCII character range.</p>"}, "Description": {"shape": "PolicyDescription", "documentation": "<p>If provided, the new description for the policy.</p>"}, "Content": {"shape": "PolicyContent", "documentation": "<p>If provided, the new content for the policy. The text must be correctly formatted JSON that complies with the syntax for the policy's type. For more information, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_scps_syntax.html\">SCP syntax</a> in the <i>Organizations User Guide</i>.</p> <p>The maximum size of a policy document depends on the policy's type. For more information, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_reference_limits.html#min-max-values\">Maximum and minimum values</a> in the <i>Organizations User Guide</i>.</p>"}}}, "UpdatePolicyResponse": {"type": "structure", "members": {"Policy": {"shape": "Policy", "documentation": "<p>A structure that contains details about the updated policy, showing the requested changes.</p>"}}}}, "documentation": "<p>Organizations is a web service that enables you to consolidate your multiple Amazon Web Services accounts into an <i>organization</i> and centrally manage your accounts and their resources.</p> <p>This guide provides descriptions of the Organizations operations. For more information about using this service, see the <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_introduction.html\">Organizations User Guide</a>.</p> <p> <b>Support and feedback for Organizations</b> </p> <p>We welcome your feedback. Send your comments to <a href=\"mailto:<EMAIL>\"><EMAIL></a> or post your feedback and questions in the <a href=\"http://forums.aws.amazon.com/forum.jspa?forumID=219\">Organizations support forum</a>. For more information about the Amazon Web Services support forums, see <a href=\"http://forums.aws.amazon.com/help.jspa\">Forums Help</a>.</p> <p> <b>Endpoint to call When using the CLI or the Amazon Web Services SDK</b> </p> <p>For the current release of Organizations, specify the <code>us-east-1</code> region for all Amazon Web Services API and CLI calls made from the commercial Amazon Web Services Regions outside of China. If calling from one of the Amazon Web Services Regions in China, then specify <code>cn-northwest-1</code>. You can do this in the CLI by using these parameters and commands:</p> <ul> <li> <p>Use the following parameter with each command to specify both the endpoint and its region:</p> <p> <code>--endpoint-url https://organizations.us-east-1.amazonaws.com</code> <i>(from commercial Amazon Web Services Regions outside of China)</i> </p> <p>or</p> <p> <code>--endpoint-url https://organizations.cn-northwest-1.amazonaws.com.cn</code> <i>(from Amazon Web Services Regions in China)</i> </p> </li> <li> <p>Use the default endpoint, but configure your default region with this command:</p> <p> <code>aws configure set default.region us-east-1</code> <i>(from commercial Amazon Web Services Regions outside of China)</i> </p> <p>or</p> <p> <code>aws configure set default.region cn-northwest-1</code> <i>(from Amazon Web Services Regions in China)</i> </p> </li> <li> <p>Use the following parameter with each command to specify the endpoint:</p> <p> <code>--region us-east-1</code> <i>(from commercial Amazon Web Services Regions outside of China)</i> </p> <p>or</p> <p> <code>--region cn-northwest-1</code> <i>(from Amazon Web Services Regions in China)</i> </p> </li> </ul> <p> <b>Recording API Requests</b> </p> <p>Organizations supports CloudTrail, a service that records Amazon Web Services API calls for your Amazon Web Services account and delivers log files to an Amazon S3 bucket. By using information collected by CloudTrail, you can determine which requests the Organizations service received, who made the request and when, and so on. For more about Organizations and its support for CloudTrail, see <a href=\"https://docs.aws.amazon.com/organizations/latest/userguide/orgs_incident-response.html#orgs_cloudtrail-integration\">Logging Organizations API calls with CloudTrail</a> in the <i>Organizations User Guide</i>. To learn more about CloudTrail, including how to turn it on and find your log files, see the <a href=\"https://docs.aws.amazon.com/awscloudtrail/latest/userguide/what_is_cloud_trail_top_level.html\">CloudTrail User Guide</a>.</p>"}