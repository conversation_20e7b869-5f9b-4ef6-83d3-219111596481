{"version": "2.0", "metadata": {"apiVersion": "2022-08-03", "endpointPrefix": "voice-chime", "protocol": "rest-json", "protocols": ["rest-json"], "serviceFullName": "Amazon Chime SDK Voice", "serviceId": "Chime SDK Voice", "signatureVersion": "v4", "signingName": "chime", "uid": "chime-sdk-voice-2022-08-03", "auth": ["aws.auth#sigv4"]}, "operations": {"AssociatePhoneNumbersWithVoiceConnector": {"name": "AssociatePhoneNumbersWithVoiceConnector", "http": {"method": "POST", "requestUri": "/voice-connectors/{voiceConnectorId}?operation=associate-phone-numbers", "responseCode": 200}, "input": {"shape": "AssociatePhoneNumbersWithVoiceConnectorRequest"}, "output": {"shape": "AssociatePhoneNumbersWithVoiceConnectorResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Associates phone numbers with the specified Amazon Chime SDK Voice Connector.</p>"}, "AssociatePhoneNumbersWithVoiceConnectorGroup": {"name": "AssociatePhoneNumbersWithVoiceConnectorGroup", "http": {"method": "POST", "requestUri": "/voice-connector-groups/{voiceConnectorGroupId}?operation=associate-phone-numbers", "responseCode": 200}, "input": {"shape": "AssociatePhoneNumbersWithVoiceConnectorGroupRequest"}, "output": {"shape": "AssociatePhoneNumbersWithVoiceConnectorGroupResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Associates phone numbers with the specified Amazon Chime SDK Voice Connector group.</p>"}, "BatchDeletePhoneNumber": {"name": "BatchDeletePhoneNumber", "http": {"method": "POST", "requestUri": "/phone-numbers?operation=batch-delete", "responseCode": 200}, "input": {"shape": "BatchDeletePhoneNumberRequest"}, "output": {"shape": "BatchDeletePhoneNumberResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p> Moves phone numbers into the <b>Deletion queue</b>. Phone numbers must be disassociated from any users or Amazon Chime SDK Voice Connectors before they can be deleted. </p> <p> Phone numbers remain in the <b>Deletion queue</b> for 7 days before they are deleted permanently. </p>"}, "BatchUpdatePhoneNumber": {"name": "BatchUpdatePhoneNumber", "http": {"method": "POST", "requestUri": "/phone-numbers?operation=batch-update", "responseCode": 200}, "input": {"shape": "BatchUpdatePhoneNumberRequest"}, "output": {"shape": "BatchUpdatePhoneNumberResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Updates phone number product types, calling names, or phone number names. You can update one attribute at a time for each <code>UpdatePhoneNumberRequestItem</code>. For example, you can update the product type, the calling name, or phone name. </p> <note> <p>You cannot have a duplicate <code>phoneNumberId</code> in a request.</p> </note>"}, "CreatePhoneNumberOrder": {"name": "CreatePhoneNumberOrder", "http": {"method": "POST", "requestUri": "/phone-number-orders", "responseCode": 201}, "input": {"shape": "CreatePhoneNumberOrderRequest"}, "output": {"shape": "CreatePhoneNumberOrderResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ForbiddenException"}, {"shape": "AccessDeniedException"}, {"shape": "UnauthorizedClientException"}, {"shape": "ThrottledClientException"}, {"shape": "ResourceLimitExceededException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Creates an order for phone numbers to be provisioned. For numbers outside the U.S., you must use the Amazon Chime SDK SIP media application dial-in product type.</p>"}, "CreateProxySession": {"name": "CreateProxySession", "http": {"method": "POST", "requestUri": "/voice-connectors/{voiceConnectorId}/proxy-sessions", "responseCode": 201}, "input": {"shape": "CreateProxySessionRequest"}, "output": {"shape": "CreateProxySessionResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Creates a proxy session for the specified Amazon Chime SDK Voice Connector for the specified participant phone numbers.</p>"}, "CreateSipMediaApplication": {"name": "CreateSipMediaApplication", "http": {"method": "POST", "requestUri": "/sip-media-applications", "responseCode": 201}, "input": {"shape": "CreateSipMediaApplicationRequest"}, "output": {"shape": "CreateSipMediaApplicationResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ForbiddenException"}, {"shape": "AccessDeniedException"}, {"shape": "UnauthorizedClientException"}, {"shape": "ThrottledClientException"}, {"shape": "ResourceLimitExceededException"}, {"shape": "ConflictException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Creates a SIP media application. For more information about SIP media applications, see <a href=\"https://docs.aws.amazon.com/chime-sdk/latest/ag/manage-sip-applications.html\">Managing SIP media applications and rules</a> in the <i>Amazon Chime SDK Administrator Guide</i>.</p>"}, "CreateSipMediaApplicationCall": {"name": "CreateSipMediaApplicationCall", "http": {"method": "POST", "requestUri": "/sip-media-applications/{sipMediaApplicationId}/calls", "responseCode": 201}, "input": {"shape": "CreateSipMediaApplicationCallRequest"}, "output": {"shape": "CreateSipMediaApplicationCallResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ForbiddenException"}, {"shape": "ResourceLimitExceededException"}, {"shape": "ThrottledClientException"}, {"shape": "UnauthorizedClientException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Creates an outbound call to a phone number from the phone number specified in the request, and it invokes the endpoint of the specified <code>sipMediaApplicationId</code>.</p>"}, "CreateSipRule": {"name": "CreateSipRule", "http": {"method": "POST", "requestUri": "/sip-rules", "responseCode": 201}, "input": {"shape": "CreateSipRuleRequest"}, "output": {"shape": "CreateSipRuleResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ForbiddenException"}, {"shape": "AccessDeniedException"}, {"shape": "UnauthorizedClientException"}, {"shape": "ThrottledClientException"}, {"shape": "ResourceLimitExceededException"}, {"shape": "ConflictException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Creates a SIP rule, which can be used to run a SIP media application as a target for a specific trigger type. For more information about SIP rules, see <a href=\"https://docs.aws.amazon.com/chime-sdk/latest/ag/manage-sip-applications.html\">Managing SIP media applications and rules</a> in the <i>Amazon Chime SDK Administrator Guide</i>.</p>"}, "CreateVoiceConnector": {"name": "CreateVoiceConnector", "http": {"method": "POST", "requestUri": "/voice-connectors", "responseCode": 201}, "input": {"shape": "CreateVoiceConnectorRequest"}, "output": {"shape": "CreateVoiceConnectorResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ForbiddenException"}, {"shape": "AccessDeniedException"}, {"shape": "UnauthorizedClientException"}, {"shape": "ThrottledClientException"}, {"shape": "ResourceLimitExceededException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Creates an Amazon Chime SDK Voice Connector. For more information about Voice Connectors, see <a href=\"https://docs.aws.amazon.com/chime-sdk/latest/ag/voice-connector-groups.html\">Managing Amazon Chime SDK Voice Connector groups</a> in the <i>Amazon Chime SDK Administrator Guide</i>.</p>"}, "CreateVoiceConnectorGroup": {"name": "CreateVoiceConnectorGroup", "http": {"method": "POST", "requestUri": "/voice-connector-groups", "responseCode": 201}, "input": {"shape": "CreateVoiceConnectorGroupRequest"}, "output": {"shape": "CreateVoiceConnectorGroupResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ForbiddenException"}, {"shape": "AccessDeniedException"}, {"shape": "UnauthorizedClientException"}, {"shape": "ThrottledClientException"}, {"shape": "ResourceLimitExceededException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Creates an Amazon Chime SDK Voice Connector group under the administrator's AWS account. You can associate Amazon Chime SDK Voice Connectors with the Voice Connector group by including <code>VoiceConnectorItems</code> in the request. </p> <p>You can include Voice Connectors from different AWS Regions in your group. This creates a fault tolerant mechanism for fallback in case of availability events.</p>"}, "CreateVoiceProfile": {"name": "CreateVoiceProfile", "http": {"method": "POST", "requestUri": "/voice-profiles", "responseCode": 201}, "input": {"shape": "CreateVoiceProfileRequest"}, "output": {"shape": "CreateVoiceProfileResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ResourceLimitExceededException"}, {"shape": "UnauthorizedClientException"}, {"shape": "ForbiddenException"}, {"shape": "AccessDeniedException"}, {"shape": "NotFoundException"}, {"shape": "ConflictException"}, {"shape": "GoneException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Creates a voice profile, which consists of an enrolled user and their latest voice print.</p> <important> <p>Before creating any voice profiles, you must provide all notices and obtain all consents from the speaker as required under applicable privacy and biometrics laws, and as required under the <a href=\"https://aws.amazon.com/service-terms/\">AWS service terms</a> for the Amazon Chime SDK.</p> </important> <p>For more information about voice profiles and voice analytics, see <a href=\"https://docs.aws.amazon.com/chime-sdk/latest/dg/pstn-voice-analytics.html\">Using Amazon Chime SDK Voice Analytics</a> in the <i>Amazon Chime SDK Developer Guide</i>.</p>"}, "CreateVoiceProfileDomain": {"name": "CreateVoiceProfileDomain", "http": {"method": "POST", "requestUri": "/voice-profile-domains", "responseCode": 201}, "input": {"shape": "CreateVoiceProfileDomainRequest"}, "output": {"shape": "CreateVoiceProfileDomainResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ForbiddenException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "UnauthorizedClientException"}, {"shape": "ThrottledClientException"}, {"shape": "ResourceLimitExceededException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Creates a voice profile domain, a collection of voice profiles, their voice prints, and encrypted enrollment audio.</p> <important> <p>Before creating any voice profiles, you must provide all notices and obtain all consents from the speaker as required under applicable privacy and biometrics laws, and as required under the <a href=\"https://aws.amazon.com/service-terms/\">AWS service terms</a> for the Amazon Chime SDK.</p> </important> <p>For more information about voice profile domains, see <a href=\"https://docs.aws.amazon.com/chime-sdk/latest/dg/pstn-voice-analytics.html\">Using Amazon Chime SDK Voice Analytics</a> in the <i>Amazon Chime SDK Developer Guide</i>.</p>"}, "DeletePhoneNumber": {"name": "DeletePhoneNumber", "http": {"method": "DELETE", "requestUri": "/phone-numbers/{phoneNumberId}", "responseCode": 204}, "input": {"shape": "DeletePhoneNumberRequest"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Moves the specified phone number into the <b>Deletion queue</b>. A phone number must be disassociated from any users or Amazon Chime SDK Voice Connectors before it can be deleted.</p> <p>Deleted phone numbers remain in the <b>Deletion queue</b> queue for 7 days before they are deleted permanently.</p>"}, "DeleteProxySession": {"name": "DeleteProxySession", "http": {"method": "DELETE", "requestUri": "/voice-connectors/{voiceConnectorId}/proxy-sessions/{proxySessionId}", "responseCode": 204}, "input": {"shape": "DeleteProxySessionRequest"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Deletes the specified proxy session from the specified Amazon Chime SDK Voice Connector.</p>"}, "DeleteSipMediaApplication": {"name": "DeleteSipMediaApplication", "http": {"method": "DELETE", "requestUri": "/sip-media-applications/{sipMediaApplicationId}", "responseCode": 204}, "input": {"shape": "DeleteSipMediaApplicationRequest"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ConflictException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Deletes a SIP media application.</p>"}, "DeleteSipRule": {"name": "DeleteSipRule", "http": {"method": "DELETE", "requestUri": "/sip-rules/{sipRuleId}", "responseCode": 204}, "input": {"shape": "DeleteSipRuleRequest"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ConflictException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Deletes a SIP rule.</p>"}, "DeleteVoiceConnector": {"name": "DeleteVoiceConnector", "http": {"method": "DELETE", "requestUri": "/voice-connectors/{voiceConnectorId}", "responseCode": 204}, "input": {"shape": "DeleteVoiceConnectorRequest"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ConflictException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Deletes an Amazon Chime SDK Voice Connector. Any phone numbers associated with the Amazon Chime SDK Voice Connector must be disassociated from it before it can be deleted.</p>"}, "DeleteVoiceConnectorEmergencyCallingConfiguration": {"name": "DeleteVoiceConnectorEmergencyCallingConfiguration", "http": {"method": "DELETE", "requestUri": "/voice-connectors/{voiceConnectorId}/emergency-calling-configuration", "responseCode": 204}, "input": {"shape": "DeleteVoiceConnectorEmergencyCallingConfigurationRequest"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Deletes the emergency calling details from the specified Amazon Chime SDK Voice Connector.</p>"}, "DeleteVoiceConnectorExternalSystemsConfiguration": {"name": "DeleteVoiceConnectorExternalSystemsConfiguration", "http": {"method": "DELETE", "requestUri": "/voice-connectors/{voiceConnectorId}/external-systems-configuration", "responseCode": 204}, "input": {"shape": "DeleteVoiceConnectorExternalSystemsConfigurationRequest"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Deletes the external systems configuration for a Voice Connector.</p>", "idempotent": true}, "DeleteVoiceConnectorGroup": {"name": "DeleteVoiceConnectorGroup", "http": {"method": "DELETE", "requestUri": "/voice-connector-groups/{voiceConnectorGroupId}", "responseCode": 204}, "input": {"shape": "DeleteVoiceConnectorGroupRequest"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ConflictException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Deletes an Amazon Chime SDK Voice Connector group. Any <code>VoiceConnectorItems</code> and phone numbers associated with the group must be removed before it can be deleted.</p>"}, "DeleteVoiceConnectorOrigination": {"name": "DeleteVoiceConnectorOrigination", "http": {"method": "DELETE", "requestUri": "/voice-connectors/{voiceConnectorId}/origination", "responseCode": 204}, "input": {"shape": "DeleteVoiceConnectorOriginationRequest"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Deletes the origination settings for the specified Amazon Chime SDK Voice Connector. </p> <note> <p>If emergency calling is configured for the Voice Connector, it must be deleted prior to deleting the origination settings.</p> </note>"}, "DeleteVoiceConnectorProxy": {"name": "DeleteVoiceConnectorProxy", "http": {"method": "DELETE", "requestUri": "/voice-connectors/{voiceConnectorId}/programmable-numbers/proxy", "responseCode": 204}, "input": {"shape": "DeleteVoiceConnectorProxyRequest"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Deletes the proxy configuration from the specified Amazon Chime SDK Voice Connector.</p>"}, "DeleteVoiceConnectorStreamingConfiguration": {"name": "DeleteVoiceConnectorStreamingConfiguration", "http": {"method": "DELETE", "requestUri": "/voice-connectors/{voiceConnectorId}/streaming-configuration", "responseCode": 204}, "input": {"shape": "DeleteVoiceConnectorStreamingConfigurationRequest"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Deletes a Voice Connector's streaming configuration.</p>"}, "DeleteVoiceConnectorTermination": {"name": "DeleteVoiceConnectorTermination", "http": {"method": "DELETE", "requestUri": "/voice-connectors/{voiceConnectorId}/termination", "responseCode": 204}, "input": {"shape": "DeleteVoiceConnectorTerminationRequest"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Deletes the termination settings for the specified Amazon Chime SDK Voice Connector.</p> <note> <p>If emergency calling is configured for the Voice Connector, it must be deleted prior to deleting the termination settings.</p> </note>"}, "DeleteVoiceConnectorTerminationCredentials": {"name": "DeleteVoiceConnectorTerminationCredentials", "http": {"method": "POST", "requestUri": "/voice-connectors/{voiceConnectorId}/termination/credentials?operation=delete", "responseCode": 204}, "input": {"shape": "DeleteVoiceConnectorTerminationCredentialsRequest"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Deletes the specified SIP credentials used by your equipment to authenticate during call termination.</p>"}, "DeleteVoiceProfile": {"name": "DeleteVoiceProfile", "http": {"method": "DELETE", "requestUri": "/voice-profiles/{VoiceProfileId}", "responseCode": 204}, "input": {"shape": "DeleteVoiceProfileRequest"}, "errors": [{"shape": "BadRequestException"}, {"shape": "UnauthorizedClientException"}, {"shape": "ForbiddenException"}, {"shape": "AccessDeniedException"}, {"shape": "NotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Deletes a voice profile, including its voice print and enrollment data. WARNING: This action is not reversible.</p>"}, "DeleteVoiceProfileDomain": {"name": "DeleteVoiceProfileDomain", "http": {"method": "DELETE", "requestUri": "/voice-profile-domains/{VoiceProfileDomainId}", "responseCode": 204}, "input": {"shape": "DeleteVoiceProfileDomainRequest"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "AccessDeniedException"}, {"shape": "BadRequestException"}, {"shape": "ConflictException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Deletes all voice profiles in the domain. WARNING: This action is not reversible.</p>"}, "DisassociatePhoneNumbersFromVoiceConnector": {"name": "DisassociatePhoneNumbersFromVoiceConnector", "http": {"method": "POST", "requestUri": "/voice-connectors/{voiceConnectorId}?operation=disassociate-phone-numbers", "responseCode": 200}, "input": {"shape": "DisassociatePhoneNumbersFromVoiceConnectorRequest"}, "output": {"shape": "DisassociatePhoneNumbersFromVoiceConnectorResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Disassociates the specified phone numbers from the specified Amazon Chime SDK Voice Connector.</p>"}, "DisassociatePhoneNumbersFromVoiceConnectorGroup": {"name": "DisassociatePhoneNumbersFromVoiceConnectorGroup", "http": {"method": "POST", "requestUri": "/voice-connector-groups/{voiceConnectorGroupId}?operation=disassociate-phone-numbers", "responseCode": 200}, "input": {"shape": "DisassociatePhoneNumbersFromVoiceConnectorGroupRequest"}, "output": {"shape": "DisassociatePhoneNumbersFromVoiceConnectorGroupResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Disassociates the specified phone numbers from the specified Amazon Chime SDK Voice Connector group.</p>"}, "GetGlobalSettings": {"name": "GetGlobalSettings", "http": {"method": "GET", "requestUri": "/settings", "responseCode": 200}, "output": {"shape": "GetGlobalSettingsResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Retrieves the global settings for the Amazon Chime SDK Voice Connectors in an AWS account.</p>"}, "GetPhoneNumber": {"name": "GetPhoneNumber", "http": {"method": "GET", "requestUri": "/phone-numbers/{phoneNumberId}"}, "input": {"shape": "GetPhoneNumberRequest"}, "output": {"shape": "GetPhoneNumberResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Retrieves details for the specified phone number ID, such as associations, capabilities, and product type.</p>"}, "GetPhoneNumberOrder": {"name": "GetPhoneNumberOrder", "http": {"method": "GET", "requestUri": "/phone-number-orders/{phoneNumberOrderId}", "responseCode": 200}, "input": {"shape": "GetPhoneNumberOrderRequest"}, "output": {"shape": "GetPhoneNumberOrderResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Retrieves details for the specified phone number order, such as the order creation timestamp, phone numbers in E.164 format, product type, and order status.</p>"}, "GetPhoneNumberSettings": {"name": "GetPhoneNumberSettings", "http": {"method": "GET", "requestUri": "/settings/phone-number", "responseCode": 200}, "output": {"shape": "GetPhoneNumberSettingsResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Retrieves the phone number settings for the administrator's AWS account, such as the default outbound calling name.</p>"}, "GetProxySession": {"name": "GetProxySession", "http": {"method": "GET", "requestUri": "/voice-connectors/{voiceConnectorId}/proxy-sessions/{proxySessionId}", "responseCode": 200}, "input": {"shape": "GetProxySessionRequest"}, "output": {"shape": "GetProxySessionResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Retrieves the specified proxy session details for the specified Amazon Chime SDK Voice Connector.</p>"}, "GetSipMediaApplication": {"name": "GetSipMediaApplication", "http": {"method": "GET", "requestUri": "/sip-media-applications/{sipMediaApplicationId}", "responseCode": 200}, "input": {"shape": "GetSipMediaApplicationRequest"}, "output": {"shape": "GetSipMediaApplicationResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Retrieves the information for a SIP media application, including name, AWS Region, and endpoints.</p>"}, "GetSipMediaApplicationAlexaSkillConfiguration": {"name": "GetSipMediaApplicationAlexaSkillConfiguration", "http": {"method": "GET", "requestUri": "/sip-media-applications/{sipMediaApplicationId}/alexa-skill-configuration", "responseCode": 200}, "input": {"shape": "GetSipMediaApplicationAlexaSkillConfigurationRequest"}, "output": {"shape": "GetSipMediaApplicationAlexaSkillConfigurationResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Gets the Alexa Skill configuration for the SIP media application.</p> <important> <p>Due to changes made by the Amazon Alexa service, this API is no longer available for use. For more information, refer to the <a href=\"https://developer.amazon.com/en-US/alexa/alexasmartproperties\">Alexa Smart Properties</a> page.</p> </important>", "deprecated": true, "deprecatedMessage": "Due to changes made by the Amazon Alexa service, this API is no longer available for use. For more information, refer to the Alexa Smart Properties page(https://developer.amazon.com/en-US/alexa/alexasmartproperties)."}, "GetSipMediaApplicationLoggingConfiguration": {"name": "GetSipMediaApplicationLoggingConfiguration", "http": {"method": "GET", "requestUri": "/sip-media-applications/{sipMediaApplicationId}/logging-configuration", "responseCode": 200}, "input": {"shape": "GetSipMediaApplicationLoggingConfigurationRequest"}, "output": {"shape": "GetSipMediaApplicationLoggingConfigurationResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Retrieves the logging configuration for the specified SIP media application.</p>"}, "GetSipRule": {"name": "GetSipRule", "http": {"method": "GET", "requestUri": "/sip-rules/{sipRuleId}", "responseCode": 200}, "input": {"shape": "GetSipRuleRequest"}, "output": {"shape": "GetSipRuleResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Retrieves the details of a SIP rule, such as the rule ID, name, triggers, and target endpoints.</p>"}, "GetSpeakerSearchTask": {"name": "GetSpeakerSearchTask", "http": {"method": "GET", "requestUri": "/voice-connectors/{VoiceConnectorId}/speaker-search-tasks/{SpeakerSearchTaskId}", "responseCode": 200}, "input": {"shape": "GetSpeakerSearchTaskRequest"}, "output": {"shape": "GetSpeakerSearchTaskResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ForbiddenException"}, {"shape": "NotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottledClientException"}, {"shape": "AccessDeniedException"}, {"shape": "UnauthorizedClientException"}, {"shape": "ServiceFailureException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Retrieves the details of the specified speaker search task.</p>"}, "GetVoiceConnector": {"name": "GetVoiceConnector", "http": {"method": "GET", "requestUri": "/voice-connectors/{voiceConnectorId}", "responseCode": 200}, "input": {"shape": "GetVoiceConnectorRequest"}, "output": {"shape": "GetVoiceConnectorResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Retrieves details for the specified Amazon Chime SDK Voice Connector, such as timestamps,name, outbound host, and encryption requirements.</p>"}, "GetVoiceConnectorEmergencyCallingConfiguration": {"name": "GetVoiceConnectorEmergencyCallingConfiguration", "http": {"method": "GET", "requestUri": "/voice-connectors/{voiceConnectorId}/emergency-calling-configuration", "responseCode": 200}, "input": {"shape": "GetVoiceConnectorEmergencyCallingConfigurationRequest"}, "output": {"shape": "GetVoiceConnectorEmergencyCallingConfigurationResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Retrieves the emergency calling configuration details for the specified Voice Connector.</p>"}, "GetVoiceConnectorExternalSystemsConfiguration": {"name": "GetVoiceConnectorExternalSystemsConfiguration", "http": {"method": "GET", "requestUri": "/voice-connectors/{voiceConnectorId}/external-systems-configuration", "responseCode": 200}, "input": {"shape": "GetVoiceConnectorExternalSystemsConfigurationRequest"}, "output": {"shape": "GetVoiceConnectorExternalSystemsConfigurationResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Gets information about an external systems configuration for a Voice Connector.</p>"}, "GetVoiceConnectorGroup": {"name": "GetVoiceConnectorGroup", "http": {"method": "GET", "requestUri": "/voice-connector-groups/{voiceConnectorGroupId}", "responseCode": 200}, "input": {"shape": "GetVoiceConnectorGroupRequest"}, "output": {"shape": "GetVoiceConnectorGroupResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Retrieves details for the specified Amazon Chime SDK Voice Connector group, such as timestamps,name, and associated <code>VoiceConnectorItems</code>.</p>"}, "GetVoiceConnectorLoggingConfiguration": {"name": "GetVoiceConnectorLoggingConfiguration", "http": {"method": "GET", "requestUri": "/voice-connectors/{voiceConnectorId}/logging-configuration", "responseCode": 200}, "input": {"shape": "GetVoiceConnectorLoggingConfigurationRequest"}, "output": {"shape": "GetVoiceConnectorLoggingConfigurationResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Retrieves the logging configuration settings for the specified Voice Connector. Shows whether SIP message logs are enabled for sending to Amazon CloudWatch Logs.</p>"}, "GetVoiceConnectorOrigination": {"name": "GetVoiceConnectorOrigination", "http": {"method": "GET", "requestUri": "/voice-connectors/{voiceConnectorId}/origination", "responseCode": 200}, "input": {"shape": "GetVoiceConnectorOriginationRequest"}, "output": {"shape": "GetVoiceConnectorOriginationResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Retrieves the origination settings for the specified Voice Connector.</p>"}, "GetVoiceConnectorProxy": {"name": "GetVoiceConnectorProxy", "http": {"method": "GET", "requestUri": "/voice-connectors/{voiceConnectorId}/programmable-numbers/proxy", "responseCode": 200}, "input": {"shape": "GetVoiceConnectorProxyRequest"}, "output": {"shape": "GetVoiceConnectorProxyResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Retrieves the proxy configuration details for the specified Amazon Chime SDK Voice Connector.</p>"}, "GetVoiceConnectorStreamingConfiguration": {"name": "GetVoiceConnectorStreamingConfiguration", "http": {"method": "GET", "requestUri": "/voice-connectors/{voiceConnectorId}/streaming-configuration", "responseCode": 200}, "input": {"shape": "GetVoiceConnectorStreamingConfigurationRequest"}, "output": {"shape": "GetVoiceConnectorStreamingConfigurationResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Retrieves the streaming configuration details for the specified Amazon Chime SDK Voice Connector. Shows whether media streaming is enabled for sending to Amazon Kinesis. It also shows the retention period, in hours, for the Amazon Kinesis data.</p>"}, "GetVoiceConnectorTermination": {"name": "GetVoiceConnectorTermination", "http": {"method": "GET", "requestUri": "/voice-connectors/{voiceConnectorId}/termination", "responseCode": 200}, "input": {"shape": "GetVoiceConnectorTerminationRequest"}, "output": {"shape": "GetVoiceConnectorTerminationResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Retrieves the termination setting details for the specified Voice Connector.</p>"}, "GetVoiceConnectorTerminationHealth": {"name": "GetVoiceConnectorTerminationHealth", "http": {"method": "GET", "requestUri": "/voice-connectors/{voiceConnectorId}/termination/health", "responseCode": 200}, "input": {"shape": "GetVoiceConnectorTerminationHealthRequest"}, "output": {"shape": "GetVoiceConnectorTerminationHealthResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Retrieves information about the last time a <code>SIP OPTIONS</code> ping was received from your SIP infrastructure for the specified Amazon Chime SDK Voice Connector.</p>"}, "GetVoiceProfile": {"name": "GetVoiceProfile", "http": {"method": "GET", "requestUri": "/voice-profiles/{VoiceProfileId}", "responseCode": 200}, "input": {"shape": "GetVoiceProfileRequest"}, "output": {"shape": "GetVoiceProfileResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "UnauthorizedClientException"}, {"shape": "ForbiddenException"}, {"shape": "AccessDeniedException"}, {"shape": "NotFoundException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Retrieves the details of the specified voice profile.</p>"}, "GetVoiceProfileDomain": {"name": "GetVoiceProfileDomain", "http": {"method": "GET", "requestUri": "/voice-profile-domains/{VoiceProfileDomainId}", "responseCode": 200}, "input": {"shape": "GetVoiceProfileDomainRequest"}, "output": {"shape": "GetVoiceProfileDomainResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "AccessDeniedException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Retrieves the details of the specified voice profile domain.</p>"}, "GetVoiceToneAnalysisTask": {"name": "GetVoiceToneAnalysisTask", "http": {"method": "GET", "requestUri": "/voice-connectors/{VoiceConnectorId}/voice-tone-analysis-tasks/{VoiceToneAnalysisTaskId}", "responseCode": 200}, "input": {"shape": "GetVoiceToneAnalysisTaskRequest"}, "output": {"shape": "GetVoiceToneAnalysisTaskResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ForbiddenException"}, {"shape": "NotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottledClientException"}, {"shape": "AccessDeniedException"}, {"shape": "UnauthorizedClientException"}, {"shape": "ServiceFailureException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Retrieves the details of a voice tone analysis task.</p>"}, "ListAvailableVoiceConnectorRegions": {"name": "ListAvailableVoiceConnectorRegions", "http": {"method": "GET", "requestUri": "/voice-connector-regions", "responseCode": 200}, "output": {"shape": "ListAvailableVoiceConnectorRegionsResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Lists the available AWS Regions in which you can create an Amazon Chime SDK Voice Connector.</p>"}, "ListPhoneNumberOrders": {"name": "ListPhoneNumberOrders", "http": {"method": "GET", "requestUri": "/phone-number-orders", "responseCode": 200}, "input": {"shape": "ListPhoneNumberOrdersRequest"}, "output": {"shape": "ListPhoneNumberOrdersResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Lists the phone numbers for an administrator's Amazon Chime SDK account.</p>"}, "ListPhoneNumbers": {"name": "ListPhoneNumbers", "http": {"method": "GET", "requestUri": "/phone-numbers"}, "input": {"shape": "ListPhoneNumbersRequest"}, "output": {"shape": "ListPhoneNumbersResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "NotFoundException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Lists the phone numbers for the specified Amazon Chime SDK account, Amazon Chime SDK user, Amazon Chime SDK Voice Connector, or Amazon Chime SDK Voice Connector group.</p>"}, "ListProxySessions": {"name": "ListProxySessions", "http": {"method": "GET", "requestUri": "/voice-connectors/{voiceConnectorId}/proxy-sessions", "responseCode": 200}, "input": {"shape": "ListProxySessionsRequest"}, "output": {"shape": "ListProxySessionsResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Lists the proxy sessions for the specified Amazon Chime SDK Voice Connector.</p>"}, "ListSipMediaApplications": {"name": "ListSipMediaApplications", "http": {"method": "GET", "requestUri": "/sip-media-applications", "responseCode": 200}, "input": {"shape": "ListSipMediaApplicationsRequest"}, "output": {"shape": "ListSipMediaApplicationsResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Lists the SIP media applications under the administrator's AWS account.</p>"}, "ListSipRules": {"name": "ListSipRules", "http": {"method": "GET", "requestUri": "/sip-rules", "responseCode": 200}, "input": {"shape": "ListSipRulesRequest"}, "output": {"shape": "ListSipRulesResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Lists the SIP rules under the administrator's AWS account.</p>"}, "ListSupportedPhoneNumberCountries": {"name": "ListSupportedPhoneNumberCountries", "http": {"method": "GET", "requestUri": "/phone-number-countries", "responseCode": 200}, "input": {"shape": "ListSupportedPhoneNumberCountriesRequest"}, "output": {"shape": "ListSupportedPhoneNumberCountriesResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ForbiddenException"}, {"shape": "AccessDeniedException"}, {"shape": "UnauthorizedClientException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Lists the countries that you can order phone numbers from.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ForbiddenException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Returns a list of the tags in a given resource.</p>"}, "ListVoiceConnectorGroups": {"name": "ListVoiceConnectorGroups", "http": {"method": "GET", "requestUri": "/voice-connector-groups", "responseCode": 200}, "input": {"shape": "ListVoiceConnectorGroupsRequest"}, "output": {"shape": "ListVoiceConnectorGroupsResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Lists the Amazon Chime SDK Voice Connector groups in the administrator's AWS account.</p>"}, "ListVoiceConnectorTerminationCredentials": {"name": "ListVoiceConnectorTerminationCredentials", "http": {"method": "GET", "requestUri": "/voice-connectors/{voiceConnectorId}/termination/credentials", "responseCode": 200}, "input": {"shape": "ListVoiceConnectorTerminationCredentialsRequest"}, "output": {"shape": "ListVoiceConnectorTerminationCredentialsResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Lists the SIP credentials for the specified Amazon Chime SDK Voice Connector.</p>"}, "ListVoiceConnectors": {"name": "ListVoiceConnectors", "http": {"method": "GET", "requestUri": "/voice-connectors", "responseCode": 200}, "input": {"shape": "ListVoiceConnectorsRequest"}, "output": {"shape": "ListVoiceConnectorsResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Lists the Amazon Chime SDK Voice Connectors in the administrators AWS account.</p>"}, "ListVoiceProfileDomains": {"name": "ListVoiceProfileDomains", "http": {"method": "GET", "requestUri": "/voice-profile-domains", "responseCode": 200}, "input": {"shape": "ListVoiceProfileDomainsRequest"}, "output": {"shape": "ListVoiceProfileDomainsResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "UnauthorizedClientException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Lists the specified voice profile domains in the administrator's AWS account. </p>"}, "ListVoiceProfiles": {"name": "ListVoiceProfiles", "http": {"method": "GET", "requestUri": "/voice-profiles", "responseCode": 200}, "input": {"shape": "ListVoiceProfilesRequest"}, "output": {"shape": "ListVoiceProfilesResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "UnauthorizedClientException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Lists the voice profiles in a voice profile domain.</p>"}, "PutSipMediaApplicationAlexaSkillConfiguration": {"name": "PutSipMediaApplicationAlexaSkillConfiguration", "http": {"method": "PUT", "requestUri": "/sip-media-applications/{sipMediaApplicationId}/alexa-skill-configuration", "responseCode": 200}, "input": {"shape": "PutSipMediaApplicationAlexaSkillConfigurationRequest"}, "output": {"shape": "PutSipMediaApplicationAlexaSkillConfigurationResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Updates the Alexa Skill configuration for the SIP media application.</p> <important> <p>Due to changes made by the Amazon Alexa service, this API is no longer available for use. For more information, refer to the <a href=\"https://developer.amazon.com/en-US/alexa/alexasmartproperties\">Alexa Smart Properties</a> page.</p> </important>", "deprecated": true, "deprecatedMessage": "Due to changes made by the Amazon Alexa service, this API is no longer available for use. For more information, refer to the Alexa Smart Properties page(https://developer.amazon.com/en-US/alexa/alexasmartproperties)."}, "PutSipMediaApplicationLoggingConfiguration": {"name": "PutSipMediaApplicationLoggingConfiguration", "http": {"method": "PUT", "requestUri": "/sip-media-applications/{sipMediaApplicationId}/logging-configuration", "responseCode": 200}, "input": {"shape": "PutSipMediaApplicationLoggingConfigurationRequest"}, "output": {"shape": "PutSipMediaApplicationLoggingConfigurationResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Updates the logging configuration for the specified SIP media application.</p>"}, "PutVoiceConnectorEmergencyCallingConfiguration": {"name": "PutVoiceConnectorEmergencyCallingConfiguration", "http": {"method": "PUT", "requestUri": "/voice-connectors/{voiceConnectorId}/emergency-calling-configuration", "responseCode": 200}, "input": {"shape": "PutVoiceConnectorEmergencyCallingConfigurationRequest"}, "output": {"shape": "PutVoiceConnectorEmergencyCallingConfigurationResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Updates a Voice Connector's emergency calling configuration.</p>"}, "PutVoiceConnectorExternalSystemsConfiguration": {"name": "PutVoiceConnectorExternalSystemsConfiguration", "http": {"method": "PUT", "requestUri": "/voice-connectors/{voiceConnectorId}/external-systems-configuration", "responseCode": 200}, "input": {"shape": "PutVoiceConnectorExternalSystemsConfigurationRequest"}, "output": {"shape": "PutVoiceConnectorExternalSystemsConfigurationResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ConflictException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Adds an external systems configuration to a Voice Connector.</p>", "idempotent": true}, "PutVoiceConnectorLoggingConfiguration": {"name": "PutVoiceConnectorLoggingConfiguration", "http": {"method": "PUT", "requestUri": "/voice-connectors/{voiceConnectorId}/logging-configuration", "responseCode": 200}, "input": {"shape": "PutVoiceConnectorLoggingConfigurationRequest"}, "output": {"shape": "PutVoiceConnectorLoggingConfigurationResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Updates a Voice Connector's logging configuration.</p>"}, "PutVoiceConnectorOrigination": {"name": "PutVoiceConnectorOrigination", "http": {"method": "PUT", "requestUri": "/voice-connectors/{voiceConnectorId}/origination", "responseCode": 200}, "input": {"shape": "PutVoiceConnectorOriginationRequest"}, "output": {"shape": "PutVoiceConnectorOriginationResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Updates a Voice Connector's origination settings.</p>"}, "PutVoiceConnectorProxy": {"name": "PutVoiceConnectorProxy", "http": {"method": "PUT", "requestUri": "/voice-connectors/{voiceConnectorId}/programmable-numbers/proxy"}, "input": {"shape": "PutVoiceConnectorProxyRequest"}, "output": {"shape": "PutVoiceConnectorProxyResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "AccessDeniedException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Puts the specified proxy configuration to the specified Amazon Chime SDK Voice Connector.</p>"}, "PutVoiceConnectorStreamingConfiguration": {"name": "PutVoiceConnectorStreamingConfiguration", "http": {"method": "PUT", "requestUri": "/voice-connectors/{voiceConnectorId}/streaming-configuration", "responseCode": 200}, "input": {"shape": "PutVoiceConnectorStreamingConfigurationRequest"}, "output": {"shape": "PutVoiceConnectorStreamingConfigurationResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Updates a Voice Connector's streaming configuration settings.</p>"}, "PutVoiceConnectorTermination": {"name": "PutVoiceConnectorTermination", "http": {"method": "PUT", "requestUri": "/voice-connectors/{voiceConnectorId}/termination", "responseCode": 200}, "input": {"shape": "PutVoiceConnectorTerminationRequest"}, "output": {"shape": "PutVoiceConnectorTerminationResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Updates a Voice Connector's termination settings.</p>"}, "PutVoiceConnectorTerminationCredentials": {"name": "PutVoiceConnectorTerminationCredentials", "http": {"method": "POST", "requestUri": "/voice-connectors/{voiceConnectorId}/termination/credentials?operation=put", "responseCode": 204}, "input": {"shape": "PutVoiceConnectorTerminationCredentialsRequest"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Updates a Voice Connector's termination credentials.</p>"}, "RestorePhoneNumber": {"name": "RestorePhoneNumber", "http": {"method": "POST", "requestUri": "/phone-numbers/{phoneNumberId}?operation=restore", "responseCode": 200}, "input": {"shape": "RestorePhoneNumberRequest"}, "output": {"shape": "RestorePhoneNumberResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ResourceLimitExceededException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p><PERSON><PERSON> a deleted phone number.</p>"}, "SearchAvailablePhoneNumbers": {"name": "SearchAvailablePhoneNumbers", "http": {"method": "GET", "requestUri": "/search?type=phone-numbers"}, "input": {"shape": "SearchAvailablePhoneNumbersRequest"}, "output": {"shape": "SearchAvailablePhoneNumbersResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ForbiddenException"}, {"shape": "AccessDeniedException"}, {"shape": "UnauthorizedClientException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Searches the provisioned phone numbers in an organization.</p>"}, "StartSpeakerSearchTask": {"name": "StartSpeakerSearchTask", "http": {"method": "POST", "requestUri": "/voice-connectors/{VoiceConnectorId}/speaker-search-tasks", "responseCode": 201}, "input": {"shape": "StartSpeakerSearchTaskRequest"}, "output": {"shape": "StartSpeakerSearchTaskResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ResourceLimitExceededException"}, {"shape": "UnauthorizedClientException"}, {"shape": "ForbiddenException"}, {"shape": "AccessDeniedException"}, {"shape": "NotFoundException"}, {"shape": "ConflictException"}, {"shape": "GoneException"}, {"shape": "UnprocessableEntityException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Starts a speaker search task.</p> <important> <p>Before starting any speaker search tasks, you must provide all notices and obtain all consents from the speaker as required under applicable privacy and biometrics laws, and as required under the <a href=\"https://aws.amazon.com/service-terms/\">AWS service terms</a> for the Amazon Chime SDK.</p> </important>"}, "StartVoiceToneAnalysisTask": {"name": "StartVoiceToneAnalysisTask", "http": {"method": "POST", "requestUri": "/voice-connectors/{VoiceConnectorId}/voice-tone-analysis-tasks", "responseCode": 201}, "input": {"shape": "StartVoiceToneAnalysisTaskRequest"}, "output": {"shape": "StartVoiceToneAnalysisTaskResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ResourceLimitExceededException"}, {"shape": "UnauthorizedClientException"}, {"shape": "ForbiddenException"}, {"shape": "AccessDeniedException"}, {"shape": "NotFoundException"}, {"shape": "ConflictException"}, {"shape": "GoneException"}, {"shape": "UnprocessableEntityException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Starts a voice tone analysis task. For more information about voice tone analysis, see <a href=\"https://docs.aws.amazon.com/chime-sdk/latest/dg/pstn-voice-analytics.html\">Using Amazon Chime SDK voice analytics</a> in the <i>Amazon Chime SDK Developer Guide</i>.</p> <important> <p>Before starting any voice tone analysis tasks, you must provide all notices and obtain all consents from the speaker as required under applicable privacy and biometrics laws, and as required under the <a href=\"https://aws.amazon.com/service-terms/\">AWS service terms</a> for the Amazon Chime SDK.</p> </important>"}, "StopSpeakerSearchTask": {"name": "StopSpeakerSearchTask", "http": {"method": "POST", "requestUri": "/voice-connectors/{VoiceConnectorId}/speaker-search-tasks/{SpeakerSearchTaskId}?operation=stop", "responseCode": 204}, "input": {"shape": "StopSpeakerSearchTaskRequest"}, "errors": [{"shape": "BadRequestException"}, {"shape": "UnauthorizedClientException"}, {"shape": "ForbiddenException"}, {"shape": "AccessDeniedException"}, {"shape": "NotFoundException"}, {"shape": "ConflictException"}, {"shape": "UnprocessableEntityException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Stops a speaker search task.</p>"}, "StopVoiceToneAnalysisTask": {"name": "StopVoiceToneAnalysisTask", "http": {"method": "POST", "requestUri": "/voice-connectors/{VoiceConnectorId}/voice-tone-analysis-tasks/{VoiceToneAnalysisTaskId}?operation=stop", "responseCode": 204}, "input": {"shape": "StopVoiceToneAnalysisTaskRequest"}, "errors": [{"shape": "BadRequestException"}, {"shape": "UnauthorizedClientException"}, {"shape": "ForbiddenException"}, {"shape": "AccessDeniedException"}, {"shape": "NotFoundException"}, {"shape": "ConflictException"}, {"shape": "UnprocessableEntityException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Stops a voice tone analysis task.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags?operation=tag-resource", "responseCode": 204}, "input": {"shape": "TagResourceRequest"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ForbiddenException"}, {"shape": "NotFoundException"}, {"shape": "ResourceLimitExceededException"}, {"shape": "UnauthorizedClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Adds a tag to the specified resource.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "POST", "requestUri": "/tags?operation=untag-resource", "responseCode": 204}, "input": {"shape": "UntagResourceRequest"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ForbiddenException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Removes tags from a resource.</p>"}, "UpdateGlobalSettings": {"name": "UpdateGlobalSettings", "http": {"method": "PUT", "requestUri": "/settings", "responseCode": 204}, "input": {"shape": "UpdateGlobalSettingsRequest"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Updates global settings for the Amazon Chime SDK Voice Connectors in an AWS account.</p>"}, "UpdatePhoneNumber": {"name": "UpdatePhoneNumber", "http": {"method": "POST", "requestUri": "/phone-numbers/{phoneNumberId}", "responseCode": 200}, "input": {"shape": "UpdatePhoneNumberRequest"}, "output": {"shape": "UpdatePhoneNumberResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ConflictException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Updates phone number details, such as product type, calling name, or phone number name for the specified phone number ID. You can update one phone number detail at a time. For example, you can update either the product type, calling name, or phone number name in one action.</p> <p>For numbers outside the U.S., you must use the Amazon Chime SDK SIP Media Application Dial-In product type.</p> <p>Updates to outbound calling names can take 72 hours to complete. Pending updates to outbound calling names must be complete before you can request another update.</p>"}, "UpdatePhoneNumberSettings": {"name": "UpdatePhoneNumberSettings", "http": {"method": "PUT", "requestUri": "/settings/phone-number", "responseCode": 204}, "input": {"shape": "UpdatePhoneNumberSettingsRequest"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Updates the phone number settings for the administrator's AWS account, such as the default outbound calling name. You can update the default outbound calling name once every seven days. Outbound calling names can take up to 72 hours to update.</p>"}, "UpdateProxySession": {"name": "UpdateProxySession", "http": {"method": "POST", "requestUri": "/voice-connectors/{voiceConnectorId}/proxy-sessions/{proxySessionId}", "responseCode": 201}, "input": {"shape": "UpdateProxySessionRequest"}, "output": {"shape": "UpdateProxySessionResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Updates the specified proxy session details, such as voice or SMS capabilities.</p>"}, "UpdateSipMediaApplication": {"name": "UpdateSipMediaApplication", "http": {"method": "PUT", "requestUri": "/sip-media-applications/{sipMediaApplicationId}", "responseCode": 200}, "input": {"shape": "UpdateSipMediaApplicationRequest"}, "output": {"shape": "UpdateSipMediaApplicationResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ConflictException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Updates the details of the specified SIP media application.</p>"}, "UpdateSipMediaApplicationCall": {"name": "UpdateSipMediaApplicationCall", "http": {"method": "POST", "requestUri": "/sip-media-applications/{sipMediaApplicationId}/calls/{transactionId}", "responseCode": 202}, "input": {"shape": "UpdateSipMediaApplicationCallRequest"}, "output": {"shape": "UpdateSipMediaApplicationCallResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "ResourceLimitExceededException"}, {"shape": "ThrottledClientException"}, {"shape": "UnauthorizedClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Invokes the AWS Lambda function associated with the SIP media application and transaction ID in an update request. The Lambda function can then return a new set of actions.</p>"}, "UpdateSipRule": {"name": "UpdateSipRule", "http": {"method": "PUT", "requestUri": "/sip-rules/{sipRuleId}", "responseCode": 202}, "input": {"shape": "UpdateSipRuleRequest"}, "output": {"shape": "UpdateSipRuleResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ConflictException"}, {"shape": "ThrottledClientException"}, {"shape": "ResourceLimitExceededException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Updates the details of the specified SIP rule.</p>"}, "UpdateVoiceConnector": {"name": "UpdateVoiceConnector", "http": {"method": "PUT", "requestUri": "/voice-connectors/{voiceConnectorId}", "responseCode": 200}, "input": {"shape": "UpdateVoiceConnectorRequest"}, "output": {"shape": "UpdateVoiceConnectorResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Updates the details for the specified Amazon Chime SDK Voice Connector.</p>"}, "UpdateVoiceConnectorGroup": {"name": "UpdateVoiceConnectorGroup", "http": {"method": "PUT", "requestUri": "/voice-connector-groups/{voiceConnectorGroupId}", "responseCode": 202}, "input": {"shape": "UpdateVoiceConnectorGroupRequest"}, "output": {"shape": "UpdateVoiceConnectorGroupResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ConflictException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Updates the settings for the specified Amazon Chime SDK Voice Connector group.</p>"}, "UpdateVoiceProfile": {"name": "UpdateVoiceProfile", "http": {"method": "PUT", "requestUri": "/voice-profiles/{VoiceProfileId}", "responseCode": 200}, "input": {"shape": "UpdateVoiceProfileRequest"}, "output": {"shape": "UpdateVoiceProfileResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "UnauthorizedClientException"}, {"shape": "ForbiddenException"}, {"shape": "AccessDeniedException"}, {"shape": "NotFoundException"}, {"shape": "ConflictException"}, {"shape": "GoneException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Updates the specified voice profile’s voice print and refreshes its expiration timestamp.</p> <important> <p>As a condition of using this feature, you acknowledge that the collection, use, storage, and retention of your caller’s biometric identifiers and biometric information (“biometric data”) in the form of a digital voiceprint requires the caller’s informed consent via a written release. Such consent is required under various state laws, including biometrics laws in Illinois, Texas, Washington and other state privacy laws.</p> <p>You must provide a written release to each caller through a process that clearly reflects each caller’s informed consent before using Amazon Chime SDK Voice Insights service, as required under the terms of your agreement with AWS governing your use of the service.</p> </important>"}, "UpdateVoiceProfileDomain": {"name": "UpdateVoiceProfileDomain", "http": {"method": "PUT", "requestUri": "/voice-profile-domains/{VoiceProfileDomainId}", "responseCode": 200}, "input": {"shape": "UpdateVoiceProfileDomainRequest"}, "output": {"shape": "UpdateVoiceProfileDomainResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "AccessDeniedException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Updates the settings for the specified voice profile domain.</p>"}, "ValidateE911Address": {"name": "ValidateE911Address", "http": {"method": "POST", "requestUri": "/emergency-calling/address", "responseCode": 202}, "input": {"shape": "ValidateE911AddressRequest"}, "output": {"shape": "ValidateE911AddressResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Validates an address to be used for 911 calls made with Amazon Chime SDK Voice Connectors. You can use validated addresses in a Presence Information Data Format Location Object file that you include in SIP requests. That helps ensure that addresses are routed to the appropriate Public Safety Answering Point.</p>"}}, "shapes": {"AccessDeniedException": {"type": "structure", "members": {}, "documentation": "<p>You don't have the permissions needed to run this action.</p>", "error": {"httpStatusCode": 403}, "exception": true}, "Address": {"type": "structure", "members": {"streetName": {"shape": "SensitiveNonEmptyString", "documentation": "<p>The address street, such as <code>8th Avenue</code>.</p>"}, "streetSuffix": {"shape": "SensitiveNonEmptyString", "documentation": "<p>The address suffix, such as the <code>N</code> in <code>8th Avenue N</code>.</p>"}, "postDirectional": {"shape": "SensitiveNonEmptyString", "documentation": "<p>An address suffix location, such as the <code>S. Unit A</code> in <code>Central Park S. Unit A</code>.</p>"}, "preDirectional": {"shape": "SensitiveNonEmptyString", "documentation": "<p>An address prefix location, such as the <code>N</code> in <code>N. Third St.</code> </p>"}, "streetNumber": {"shape": "SensitiveNonEmptyString", "documentation": "<p>The numeric portion of an address.</p>"}, "city": {"shape": "SensitiveNonEmptyString", "documentation": "<p>The city of an address.</p>"}, "state": {"shape": "SensitiveNonEmptyString", "documentation": "<p>The state of an address.</p>"}, "postalCode": {"shape": "SensitiveNonEmptyString", "documentation": "<p>The postal code of an address.</p>"}, "postalCodePlus4": {"shape": "SensitiveNonEmptyString", "documentation": "<p>The zip + 4 or postal code + 4 of an address.</p>"}, "country": {"shape": "SensitiveNonEmptyString", "documentation": "<p>The country of an address.</p>"}}, "documentation": "<p>A validated address.</p>"}, "AlexaSkillId": {"type": "string", "max": 64, "pattern": "amzn1\\.application-oa2-client\\.[0-9a-fA-F]{32}", "sensitive": true}, "AlexaSkillIdList": {"type": "list", "member": {"shape": "AlexaSkillId"}, "max": 1, "min": 1}, "AlexaSkillStatus": {"type": "string", "enum": ["ACTIVE", "INACTIVE"]}, "Alpha2CountryCode": {"type": "string", "pattern": "[A-Z]{2}"}, "AreaCode": {"type": "string", "pattern": "^$|^[0-9]{3,3}$"}, "Arn": {"type": "string", "max": 1024, "min": 1, "pattern": "^arn[\\/\\:\\-\\_\\.a-zA-Z0-9]+$", "sensitive": true}, "AssociatePhoneNumbersWithVoiceConnectorGroupRequest": {"type": "structure", "required": ["VoiceConnectorGroupId", "E164PhoneNumbers"], "members": {"VoiceConnectorGroupId": {"shape": "NonEmptyString", "documentation": "<p>The Amazon Chime SDK Voice Connector group ID.</p>", "location": "uri", "locationName": "voiceConnectorGroupId"}, "E164PhoneNumbers": {"shape": "E164PhoneNumberList", "documentation": "<p>List of phone numbers, in E.164 format.</p>"}, "ForceAssociate": {"shape": "NullableBoolean", "documentation": "<p>If true, associates the provided phone numbers with the provided Amazon Chime SDK Voice Connector Group and removes any previously existing associations. If false, does not associate any phone numbers that have previously existing associations.</p>"}}}, "AssociatePhoneNumbersWithVoiceConnectorGroupResponse": {"type": "structure", "members": {"PhoneNumberErrors": {"shape": "PhoneNumberErrorList", "documentation": "<p>If the action fails for one or more of the phone numbers in the request, a list of the phone numbers is returned, along with error codes and error messages.</p>"}}}, "AssociatePhoneNumbersWithVoiceConnectorRequest": {"type": "structure", "required": ["VoiceConnectorId", "E164PhoneNumbers"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "documentation": "<p>The Voice Connector ID.</p>", "location": "uri", "locationName": "voiceConnectorId"}, "E164PhoneNumbers": {"shape": "E164PhoneNumberList", "documentation": "<p>List of phone numbers, in E.164 format.</p>"}, "ForceAssociate": {"shape": "NullableBoolean", "documentation": "<p>If true, associates the provided phone numbers with the provided Amazon Chime SDK Voice Connector and removes any previously existing associations. If false, does not associate any phone numbers that have previously existing associations.</p>"}}}, "AssociatePhoneNumbersWithVoiceConnectorResponse": {"type": "structure", "members": {"PhoneNumberErrors": {"shape": "PhoneNumberErrorList", "documentation": "<p>If the action fails for one or more of the phone numbers in the request, a list of the phone numbers is returned, along with error codes and error messages.</p>"}}}, "BadRequestException": {"type": "structure", "members": {}, "documentation": "<p>The input parameters don't match the service's restrictions.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "BatchDeletePhoneNumberRequest": {"type": "structure", "required": ["PhoneNumberIds"], "members": {"PhoneNumberIds": {"shape": "NonEmptyStringList", "documentation": "<p>List of phone number IDs.</p>"}}}, "BatchDeletePhoneNumberResponse": {"type": "structure", "members": {"PhoneNumberErrors": {"shape": "PhoneNumberErrorList", "documentation": "<p>If the action fails for one or more of the phone numbers in the request, a list of the phone numbers is returned, along with error codes and error messages.</p>"}}}, "BatchUpdatePhoneNumberRequest": {"type": "structure", "required": ["UpdatePhoneNumberRequestItems"], "members": {"UpdatePhoneNumberRequestItems": {"shape": "UpdatePhoneNumberRequestItemList", "documentation": "<p>Lists the phone numbers in the update request.</p>"}}}, "BatchUpdatePhoneNumberResponse": {"type": "structure", "members": {"PhoneNumberErrors": {"shape": "PhoneNumberErrorList", "documentation": "<p>A list of failed phone numbers and their error messages.</p>"}}}, "Boolean": {"type": "boolean"}, "CallDetails": {"type": "structure", "members": {"VoiceConnectorId": {"shape": "NonEmptyString128", "documentation": "<p>The Voice Connector ID.</p>"}, "TransactionId": {"shape": "NonEmptyString256", "documentation": "<p>The transaction ID of a Voice Connector call.</p>"}, "IsCaller": {"shape": "Boolean", "documentation": "<p>Identifies a person as the caller or the callee.</p>"}}, "documentation": "<p>The details of an Amazon Chime SDK Voice Connector call.</p>"}, "CallLegType": {"type": "string", "enum": ["Caller", "<PERSON><PERSON>"]}, "CallingName": {"type": "string", "pattern": "^$|^[a-zA-Z0-9 ]{2,15}$", "sensitive": true}, "CallingNameStatus": {"type": "string", "enum": ["Unassigned", "UpdateInProgress", "UpdateSucceeded", "UpdateFailed"]}, "CallingRegion": {"type": "string"}, "CallingRegionList": {"type": "list", "member": {"shape": "CallingRegion"}}, "CandidateAddress": {"type": "structure", "members": {"streetInfo": {"shape": "SensitiveNonEmptyString", "documentation": "<p>The street information of the candidate address.</p>"}, "streetNumber": {"shape": "SensitiveNonEmptyString", "documentation": "<p>The numeric portion of the candidate address.</p>"}, "city": {"shape": "SensitiveNonEmptyString", "documentation": "<p>The city of the candidate address.</p>"}, "state": {"shape": "SensitiveNonEmptyString", "documentation": "<p>The state of the candidate address.</p>"}, "postalCode": {"shape": "SensitiveNonEmptyString", "documentation": "<p>The postal code of the candidate address.</p>"}, "postalCodePlus4": {"shape": "SensitiveNonEmptyString", "documentation": "<p>The zip + 4 or postal code +4 of the candidate address.</p>"}, "country": {"shape": "SensitiveNonEmptyString", "documentation": "<p>The country of the candidate address.</p>"}}, "documentation": "<p>A suggested address.</p>"}, "CandidateAddressList": {"type": "list", "member": {"shape": "Candidate<PERSON><PERSON><PERSON>"}}, "Capability": {"type": "string", "enum": ["Voice", "SMS"]}, "CapabilityList": {"type": "list", "member": {"shape": "Capability"}}, "ClientRequestId": {"type": "string", "pattern": "^[-_a-zA-Z0-9]*${2,64}$"}, "ConfidenceScore": {"type": "float", "max": 1, "min": 0}, "ConflictException": {"type": "structure", "members": {}, "documentation": "<p>Multiple instances of the same request were made simultaneously.</p>", "error": {"httpStatusCode": 409}, "exception": true}, "ContactCenterSystemType": {"type": "string", "enum": ["GENESYS_ENGAGE_ON_PREMISES", "AVAYA_AURA_CALL_CENTER_ELITE", "AVAYA_AURA_CONTACT_CENTER", "CISCO_UNIFIED_CONTACT_CENTER_ENTERPRISE"]}, "ContactCenterSystemTypeList": {"type": "list", "member": {"shape": "ContactCenterSystemType"}}, "Country": {"type": "string", "pattern": "^$|^[A-Z]{2,2}$"}, "CountryList": {"type": "list", "member": {"shape": "Country"}, "max": 100, "min": 1}, "CpsLimit": {"type": "integer", "min": 1}, "CreatePhoneNumberOrderRequest": {"type": "structure", "required": ["ProductType", "E164PhoneNumbers"], "members": {"ProductType": {"shape": "PhoneNumberProductType", "documentation": "<p>The phone number product type.</p>"}, "E164PhoneNumbers": {"shape": "E164PhoneNumberList", "documentation": "<p>List of phone numbers, in E.164 format.</p>"}, "Name": {"shape": "PhoneNumberName", "documentation": "<p>Specifies the name assigned to one or more phone numbers.</p>"}}}, "CreatePhoneNumberOrderResponse": {"type": "structure", "members": {"PhoneNumberOrder": {"shape": "PhoneNumberOrder", "documentation": "<p>The phone number order details.</p>"}}}, "CreateProxySessionRequest": {"type": "structure", "required": ["ParticipantPhoneNumbers", "Capabilities", "VoiceConnectorId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString128", "documentation": "<p>The Voice Connector ID.</p>", "location": "uri", "locationName": "voiceConnectorId"}, "ParticipantPhoneNumbers": {"shape": "ParticipantPhoneNumberList", "documentation": "<p>The participant phone numbers.</p>"}, "Name": {"shape": "ProxySessionNameString", "documentation": "<p>The name of the proxy session.</p>"}, "ExpiryMinutes": {"shape": "PositiveInteger", "documentation": "<p>The number of minutes allowed for the proxy session.</p>"}, "Capabilities": {"shape": "CapabilityList", "documentation": "<p>The proxy session's capabilities.</p>"}, "NumberSelectionBehavior": {"shape": "NumberSelectionBehavior", "documentation": "<p>The preference for proxy phone number reuse, or stickiness, between the same participants across sessions.</p>"}, "GeoMatchLevel": {"shape": "GeoMatchLevel", "documentation": "<p>The preference for matching the country or area code of the proxy phone number with that of the first participant.</p>"}, "GeoMatchParams": {"shape": "GeoMatchParams", "documentation": "<p>The country and area code for the proxy phone number.</p>"}}}, "CreateProxySessionResponse": {"type": "structure", "members": {"ProxySession": {"shape": "ProxySession", "documentation": "<p>The proxy session details.</p>"}}}, "CreateSipMediaApplicationCallRequest": {"type": "structure", "required": ["FromPhoneNumber", "ToPhoneNumber", "SipMediaApplicationId"], "members": {"FromPhoneNumber": {"shape": "E164PhoneNumber", "documentation": "<p>The phone number that a user calls from. This is a phone number in your Amazon Chime SDK phone number inventory.</p>"}, "ToPhoneNumber": {"shape": "E164PhoneNumber", "documentation": "<p>The phone number that the service should call.</p>"}, "SipMediaApplicationId": {"shape": "NonEmptyString", "documentation": "<p>The ID of the SIP media application.</p>", "location": "uri", "locationName": "sipMediaApplicationId"}, "SipHeaders": {"shape": "SipHeadersMap", "documentation": "<p>The SIP headers added to an outbound call leg.</p>"}, "ArgumentsMap": {"shape": "SMACreateCallArgumentsMap", "documentation": "<p>Context passed to a CreateSipMediaApplication API call. For example, you could pass key-value pairs such as: <code>\"FirstName\": \"<PERSON>\", \"LastName\": \"<PERSON><PERSON>\"</code> </p>"}}}, "CreateSipMediaApplicationCallResponse": {"type": "structure", "members": {"SipMediaApplicationCall": {"shape": "SipMediaApplicationCall", "documentation": "<p>The actual call.</p>"}}}, "CreateSipMediaApplicationRequest": {"type": "structure", "required": ["AwsRegion", "Name", "Endpoints"], "members": {"AwsRegion": {"shape": "String", "documentation": "<p>The AWS Region assigned to the SIP media application.</p>"}, "Name": {"shape": "SipMediaApplicationName", "documentation": "<p>The SIP media application's name.</p>"}, "Endpoints": {"shape": "SipMediaApplicationEndpointList", "documentation": "<p>List of endpoints (Lambda ARNs) specified for the SIP media application.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The tags assigned to the SIP media application.</p>"}}}, "CreateSipMediaApplicationResponse": {"type": "structure", "members": {"SipMediaApplication": {"shape": "SipMediaApplication", "documentation": "<p>The SIP media application details.</p>"}}}, "CreateSipRuleRequest": {"type": "structure", "required": ["Name", "TriggerType", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "members": {"Name": {"shape": "SipRuleName", "documentation": "<p>The name of the SIP rule.</p>"}, "TriggerType": {"shape": "SipRuleTriggerType", "documentation": "<p>The type of trigger assigned to the SIP rule in <code>TriggerValue</code>, currently <code>RequestUriHostname</code> or <code>ToPhoneNumber</code>.</p>"}, "TriggerValue": {"shape": "NonEmptyString", "documentation": "<p>If <code>TriggerType</code> is <code>RequestUriHostname</code>, the value can be the outbound host name of a Voice Connector. If <code>TriggerType</code> is <code>ToPhoneNumber</code>, the value can be a customer-owned phone number in the E164 format. The <code>SipMediaApplication</code> specified in the <code>SipRule</code> is triggered if the request URI in an incoming SIP request matches the <code>RequestUriHostname</code>, or if the <code>To</code> header in the incoming SIP request matches the <code>ToPhoneNumber</code> value.</p>"}, "Disabled": {"shape": "NullableBoolean", "documentation": "<p>Disables or enables a SIP rule. You must disable SIP rules before you can delete them.</p>"}, "TargetApplications": {"shape": "SipRuleTargetApplicationList", "documentation": "<p>List of SIP media applications, with priority and AWS Region. Only one SIP application per AWS Region can be used.</p>"}}}, "CreateSipRuleResponse": {"type": "structure", "members": {"SipRule": {"shape": "SipRule", "documentation": "<p>The SIP rule information, including the rule ID, triggers, and target applications.</p>"}}}, "CreateVoiceConnectorGroupRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "VoiceConnectorGroupName", "documentation": "<p>The name of the Voice Connector group.</p>"}, "VoiceConnectorItems": {"shape": "VoiceConnectorItemList", "documentation": "<p>Lists the Voice Connectors that inbound calls are routed to.</p>"}}}, "CreateVoiceConnectorGroupResponse": {"type": "structure", "members": {"VoiceConnectorGroup": {"shape": "VoiceConnectorGroup", "documentation": "<p>The details of the Voice Connector group.</p>"}}}, "CreateVoiceConnectorRequest": {"type": "structure", "required": ["Name", "RequireEncryption"], "members": {"Name": {"shape": "VoiceConnectorName", "documentation": "<p>The name of the Voice Connector.</p>"}, "AwsRegion": {"shape": "VoiceConnectorAwsRegion", "documentation": "<p>The AWS Region in which the Amazon Chime SDK Voice Connector is created. Default value: <code>us-east-1</code> .</p>"}, "RequireEncryption": {"shape": "Boolean", "documentation": "<p>Enables or disables encryption for the Voice Connector.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The tags assigned to the Voice Connector.</p>"}, "IntegrationType": {"shape": "VoiceConnectorIntegrationType", "documentation": "<p>The connectors for use with Amazon Connect.</p> <p>The following options are available:</p> <ul> <li> <p> <code>CONNECT_CALL_TRANSFER_CONNECTOR</code> - Enables enterprises to integrate Amazon Connect with other voice systems to directly transfer voice calls and metadata without using the public telephone network. They can use Amazon Connect telephony and Interactive Voice Response (IVR) with their existing voice systems to modernize the IVR experience of their existing contact center and their enterprise and branch voice systems. Additionally, enterprises migrating their contact center to Amazon Connect can start with Connect telephony and IVR for immediate modernization ahead of agent migration.</p> </li> <li> <p> <code>CONNECT_ANALYTICS_CONNECTOR</code> - Enables enterprises to integrate Amazon Connect with other voice systems for real-time and post-call analytics. They can use Amazon Connect Contact Lens with their existing voice systems to provides call recordings, conversational analytics (including contact transcript, sensitive data redaction, content categorization, theme detection, sentiment analysis, real-time alerts, and post-contact summary), and agent performance evaluations (including evaluation forms, automated evaluation, supervisor review) with a rich user experience to display, search and filter customer interactions, and programmatic access to data streams and the data lake. Additionally, enterprises migrating their contact center to Amazon Connect can start with Contact Lens analytics and performance insights ahead of agent migration.</p> </li> </ul>"}}}, "CreateVoiceConnectorResponse": {"type": "structure", "members": {"VoiceConnector": {"shape": "VoiceConnector", "documentation": "<p>The details of the Voice Connector.</p>"}}}, "CreateVoiceProfileDomainRequest": {"type": "structure", "required": ["Name", "ServerSideEncryptionConfiguration"], "members": {"Name": {"shape": "VoiceProfileDomainName", "documentation": "<p>The name of the voice profile domain.</p>"}, "Description": {"shape": "VoiceProfileDomainDescription", "documentation": "<p>A description of the voice profile domain.</p>"}, "ServerSideEncryptionConfiguration": {"shape": "ServerSideEncryptionConfiguration", "documentation": "<p>The server-side encryption configuration for the request.</p>"}, "ClientRequestToken": {"shape": "ClientRequestId", "documentation": "<p>The unique identifier for the client request. Use a different token for different domain creation requests.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The tags assigned to the domain.</p>"}}}, "CreateVoiceProfileDomainResponse": {"type": "structure", "members": {"VoiceProfileDomain": {"shape": "VoiceProfileDomain", "documentation": "<p>The requested voice profile domain.</p>"}}}, "CreateVoiceProfileRequest": {"type": "structure", "required": ["SpeakerSearchTaskId"], "members": {"SpeakerSearchTaskId": {"shape": "NonEmptyString256", "documentation": "<p>The ID of the speaker search task.</p>"}}}, "CreateVoiceProfileResponse": {"type": "structure", "members": {"VoiceProfile": {"shape": "VoiceProfile", "documentation": "<p>The requested voice profile.</p>"}}}, "Credential": {"type": "structure", "members": {"Username": {"shape": "SensitiveString", "documentation": "<p>The RFC2617 compliant user name associated with the SIP credentials, in US-ASCII format.</p>"}, "Password": {"shape": "SensitiveString", "documentation": "<p>The RFC2617 compliant password associated with the SIP credentials, in US-ASCII format.</p>"}}, "documentation": "<p>The SIP credentials used to authenticate requests to an Amazon Chime SDK Voice Connector.</p>"}, "CredentialList": {"type": "list", "member": {"shape": "Credential"}}, "DNISEmergencyCallingConfiguration": {"type": "structure", "required": ["EmergencyPhoneNumber", "CallingCountry"], "members": {"EmergencyPhoneNumber": {"shape": "E164PhoneNumber", "documentation": "<p>The DNIS phone number that you route emergency calls to, in E.164 format.</p>"}, "TestPhoneNumber": {"shape": "E164PhoneNumber", "documentation": "<p>The DNIS phone number for routing test emergency calls to, in E.164 format.</p>"}, "CallingCountry": {"shape": "Alpha2CountryCode", "documentation": "<p>The country from which emergency calls are allowed, in ISO 3166-1 alpha-2 format.</p>"}}, "documentation": "<p>The Dialed Number Identification Service (DNIS) emergency calling configuration details associated with an Amazon Chime SDK Voice Connector's emergency calling configuration.</p>"}, "DNISEmergencyCallingConfigurationList": {"type": "list", "member": {"shape": "DNISEmergencyCallingConfiguration"}}, "DataRetentionInHours": {"type": "integer", "min": 0}, "DeletePhoneNumberRequest": {"type": "structure", "required": ["PhoneNumberId"], "members": {"PhoneNumberId": {"shape": "SensitiveNonEmptyString", "documentation": "<p>The phone number ID.</p>", "location": "uri", "locationName": "phoneNumberId"}}}, "DeleteProxySessionRequest": {"type": "structure", "required": ["VoiceConnectorId", "ProxySessionId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString128", "documentation": "<p>The Voice Connector ID.</p>", "location": "uri", "locationName": "voiceConnectorId"}, "ProxySessionId": {"shape": "NonEmptyString128", "documentation": "<p>The proxy session ID.</p>", "location": "uri", "locationName": "proxySessionId"}}}, "DeleteSipMediaApplicationRequest": {"type": "structure", "required": ["SipMediaApplicationId"], "members": {"SipMediaApplicationId": {"shape": "NonEmptyString", "documentation": "<p>The SIP media application ID.</p>", "location": "uri", "locationName": "sipMediaApplicationId"}}}, "DeleteSipRuleRequest": {"type": "structure", "required": ["SipRuleId"], "members": {"SipRuleId": {"shape": "NonEmptyString", "documentation": "<p>The SIP rule ID.</p>", "location": "uri", "locationName": "sipRuleId"}}}, "DeleteVoiceConnectorEmergencyCallingConfigurationRequest": {"type": "structure", "required": ["VoiceConnectorId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "documentation": "<p>The Voice Connector ID.</p>", "location": "uri", "locationName": "voiceConnectorId"}}}, "DeleteVoiceConnectorExternalSystemsConfigurationRequest": {"type": "structure", "required": ["VoiceConnectorId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "documentation": "<p>The ID of the Voice Connector for which to delete the external system configuration.</p>", "location": "uri", "locationName": "voiceConnectorId"}}}, "DeleteVoiceConnectorGroupRequest": {"type": "structure", "required": ["VoiceConnectorGroupId"], "members": {"VoiceConnectorGroupId": {"shape": "NonEmptyString", "documentation": "<p>The Voice Connector Group ID.</p>", "location": "uri", "locationName": "voiceConnectorGroupId"}}}, "DeleteVoiceConnectorOriginationRequest": {"type": "structure", "required": ["VoiceConnectorId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "documentation": "<p>The Voice Connector ID.</p>", "location": "uri", "locationName": "voiceConnectorId"}}}, "DeleteVoiceConnectorProxyRequest": {"type": "structure", "required": ["VoiceConnectorId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString128", "documentation": "<p>The Voice Connector ID.</p>", "location": "uri", "locationName": "voiceConnectorId"}}}, "DeleteVoiceConnectorRequest": {"type": "structure", "required": ["VoiceConnectorId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "documentation": "<p>The Voice Connector ID.</p>", "location": "uri", "locationName": "voiceConnectorId"}}}, "DeleteVoiceConnectorStreamingConfigurationRequest": {"type": "structure", "required": ["VoiceConnectorId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "documentation": "<p>The Voice Connector ID.</p>", "location": "uri", "locationName": "voiceConnectorId"}}}, "DeleteVoiceConnectorTerminationCredentialsRequest": {"type": "structure", "required": ["Usernames", "VoiceConnectorId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "documentation": "<p>The Voice Connector ID.</p>", "location": "uri", "locationName": "voiceConnectorId"}, "Usernames": {"shape": "SensitiveStringList", "documentation": "<p>The RFC2617 compliant username associated with the SIP credentials, in US-ASCII format.</p>"}}}, "DeleteVoiceConnectorTerminationRequest": {"type": "structure", "required": ["VoiceConnectorId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "documentation": "<p>The Voice Connector ID.</p>", "location": "uri", "locationName": "voiceConnectorId"}}}, "DeleteVoiceProfileDomainRequest": {"type": "structure", "required": ["VoiceProfileDomainId"], "members": {"VoiceProfileDomainId": {"shape": "NonEmptyString256", "documentation": "<p>The voice profile domain ID.</p>", "location": "uri", "locationName": "VoiceProfileDomainId"}}}, "DeleteVoiceProfileRequest": {"type": "structure", "required": ["VoiceProfileId"], "members": {"VoiceProfileId": {"shape": "NonEmptyString256", "documentation": "<p>The voice profile ID.</p>", "location": "uri", "locationName": "VoiceProfileId"}}}, "DisassociatePhoneNumbersFromVoiceConnectorGroupRequest": {"type": "structure", "required": ["VoiceConnectorGroupId", "E164PhoneNumbers"], "members": {"VoiceConnectorGroupId": {"shape": "NonEmptyString", "documentation": "<p>The Voice Connector group ID.</p>", "location": "uri", "locationName": "voiceConnectorGroupId"}, "E164PhoneNumbers": {"shape": "E164PhoneNumberList", "documentation": "<p>The list of phone numbers, in E.164 format.</p>"}}}, "DisassociatePhoneNumbersFromVoiceConnectorGroupResponse": {"type": "structure", "members": {"PhoneNumberErrors": {"shape": "PhoneNumberErrorList", "documentation": "<p>If the action fails for one or more of the phone numbers in the request, a list of the phone numbers is returned, along with error codes and error messages.</p>"}}}, "DisassociatePhoneNumbersFromVoiceConnectorRequest": {"type": "structure", "required": ["VoiceConnectorId", "E164PhoneNumbers"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "documentation": "<p>The Voice Connector ID.</p>", "location": "uri", "locationName": "voiceConnectorId"}, "E164PhoneNumbers": {"shape": "E164PhoneNumberList", "documentation": "<p>List of phone numbers, in E.164 format.</p>"}}}, "DisassociatePhoneNumbersFromVoiceConnectorResponse": {"type": "structure", "members": {"PhoneNumberErrors": {"shape": "PhoneNumberErrorList", "documentation": "<p>If the action fails for one or more of the phone numbers in the request, a list of the phone numbers is returned, along with error codes and error messages.</p>"}}}, "E164PhoneNumber": {"type": "string", "pattern": "^\\+?[1-9]\\d{1,14}$", "sensitive": true}, "E164PhoneNumberList": {"type": "list", "member": {"shape": "E164PhoneNumber"}}, "EmergencyCallingConfiguration": {"type": "structure", "members": {"DNIS": {"shape": "DNISEmergencyCallingConfigurationList", "documentation": "<p>The Dialed Number Identification Service (DNIS) emergency calling configuration details.</p>"}}, "documentation": "<p>The emergency calling configuration details associated with an Amazon Chime SDK Voice Connector.</p>"}, "ErrorCode": {"type": "string", "enum": ["BadRequest", "Conflict", "Forbidden", "NotFound", "PreconditionFailed", "ResourceLimitExceeded", "ServiceFailure", "AccessDenied", "ServiceUnavailable", "Throttled", "Throttling", "Unauthorized", "Unprocessable", "VoiceConnectorGroupAssociationsExist", "PhoneNumberAssociationsExist", "Gone"]}, "ExternalSystemsConfiguration": {"type": "structure", "members": {"SessionBorderControllerTypes": {"shape": "SessionBorderControllerTypeList", "documentation": "<p>The session border controllers.</p>"}, "ContactCenterSystemTypes": {"shape": "ContactCenterSystemTypeList", "documentation": "<p>The contact center system.</p>"}}, "documentation": "<p>Contains information about an external systems configuration for a Voice Connector.</p>"}, "ForbiddenException": {"type": "structure", "members": {}, "documentation": "<p>The client is permanently forbidden from making the request.</p>", "error": {"httpStatusCode": 403}, "exception": true}, "FunctionArn": {"type": "string", "max": 10000, "pattern": "arn:(aws[a-zA-Z-]*)?:lambda:[a-z]{2}((-gov)|(-iso(b?)))?-[a-z]+-\\d{1}:\\d{12}:function:[a-zA-Z0-9-_]+(:(\\$LATEST|[a-zA-Z0-9-_]+))?", "sensitive": true}, "GeoMatchLevel": {"type": "string", "enum": ["Country", "AreaCode"]}, "GeoMatchParams": {"type": "structure", "required": ["Country", "AreaCode"], "members": {"Country": {"shape": "Country", "documentation": "<p>The country.</p>"}, "AreaCode": {"shape": "AreaCode", "documentation": "<p>The area code.</p>"}}, "documentation": "<p>The country and area code for a proxy phone number in a proxy phone session.</p>"}, "GetGlobalSettingsResponse": {"type": "structure", "members": {"VoiceConnector": {"shape": "VoiceConnectorSettings", "documentation": "<p>The Voice Connector settings.</p>"}}}, "GetPhoneNumberOrderRequest": {"type": "structure", "required": ["PhoneNumberOrderId"], "members": {"PhoneNumberOrderId": {"shape": "GuidString", "documentation": "<p>The ID of the phone number order .</p>", "location": "uri", "locationName": "phoneNumberOrderId"}}}, "GetPhoneNumberOrderResponse": {"type": "structure", "members": {"PhoneNumberOrder": {"shape": "PhoneNumberOrder", "documentation": "<p>The phone number order details.</p>"}}}, "GetPhoneNumberRequest": {"type": "structure", "required": ["PhoneNumberId"], "members": {"PhoneNumberId": {"shape": "SensitiveNonEmptyString", "documentation": "<p>The phone number ID.</p>", "location": "uri", "locationName": "phoneNumberId"}}}, "GetPhoneNumberResponse": {"type": "structure", "members": {"PhoneNumber": {"shape": "PhoneNumber", "documentation": "<p>The phone number details.</p>"}}}, "GetPhoneNumberSettingsResponse": {"type": "structure", "members": {"CallingName": {"shape": "CallingName", "documentation": "<p>The default outbound calling name for the account.</p>"}, "CallingNameUpdatedTimestamp": {"shape": "Iso8601Timestamp", "documentation": "<p>The updated outbound calling name timestamp, in ISO 8601 format.</p>"}}}, "GetProxySessionRequest": {"type": "structure", "required": ["VoiceConnectorId", "ProxySessionId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString128", "documentation": "<p>The Voice Connector ID.</p>", "location": "uri", "locationName": "voiceConnectorId"}, "ProxySessionId": {"shape": "NonEmptyString128", "documentation": "<p>The proxy session ID.</p>", "location": "uri", "locationName": "proxySessionId"}}}, "GetProxySessionResponse": {"type": "structure", "members": {"ProxySession": {"shape": "ProxySession", "documentation": "<p>The proxy session details.</p>"}}}, "GetSipMediaApplicationAlexaSkillConfigurationRequest": {"type": "structure", "required": ["SipMediaApplicationId"], "members": {"SipMediaApplicationId": {"shape": "NonEmptyString", "documentation": "<p>The SIP media application ID.</p>", "location": "uri", "locationName": "sipMediaApplicationId"}}}, "GetSipMediaApplicationAlexaSkillConfigurationResponse": {"type": "structure", "members": {"SipMediaApplicationAlexaSkillConfiguration": {"shape": "SipMediaApplicationAlexaSkillConfiguration", "documentation": "<p>Returns the Alexa Skill configuration.</p>"}}}, "GetSipMediaApplicationLoggingConfigurationRequest": {"type": "structure", "required": ["SipMediaApplicationId"], "members": {"SipMediaApplicationId": {"shape": "NonEmptyString", "documentation": "<p>The SIP media application ID.</p>", "location": "uri", "locationName": "sipMediaApplicationId"}}}, "GetSipMediaApplicationLoggingConfigurationResponse": {"type": "structure", "members": {"SipMediaApplicationLoggingConfiguration": {"shape": "SipMediaApplicationLoggingConfiguration", "documentation": "<p>The actual logging configuration.</p>"}}}, "GetSipMediaApplicationRequest": {"type": "structure", "required": ["SipMediaApplicationId"], "members": {"SipMediaApplicationId": {"shape": "NonEmptyString", "documentation": "<p>The SIP media application ID .</p>", "location": "uri", "locationName": "sipMediaApplicationId"}}}, "GetSipMediaApplicationResponse": {"type": "structure", "members": {"SipMediaApplication": {"shape": "SipMediaApplication", "documentation": "<p>The details of the SIP media application.</p>"}}}, "GetSipRuleRequest": {"type": "structure", "required": ["SipRuleId"], "members": {"SipRuleId": {"shape": "NonEmptyString", "documentation": "<p>The SIP rule ID.</p>", "location": "uri", "locationName": "sipRuleId"}}}, "GetSipRuleResponse": {"type": "structure", "members": {"SipRule": {"shape": "SipRule", "documentation": "<p>The SIP rule details.</p>"}}}, "GetSpeakerSearchTaskRequest": {"type": "structure", "required": ["VoiceConnectorId", "SpeakerSearchTaskId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString128", "documentation": "<p>The Voice Connector ID.</p>", "location": "uri", "locationName": "VoiceConnectorId"}, "SpeakerSearchTaskId": {"shape": "NonEmptyString256", "documentation": "<p>The ID of the speaker search task.</p>", "location": "uri", "locationName": "SpeakerSearchTaskId"}}}, "GetSpeakerSearchTaskResponse": {"type": "structure", "members": {"SpeakerSearchTask": {"shape": "SpeakerSearchTask", "documentation": "<p>The details of the speaker search task.</p>"}}}, "GetVoiceConnectorEmergencyCallingConfigurationRequest": {"type": "structure", "required": ["VoiceConnectorId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "documentation": "<p>The Voice Connector ID.</p>", "location": "uri", "locationName": "voiceConnectorId"}}}, "GetVoiceConnectorEmergencyCallingConfigurationResponse": {"type": "structure", "members": {"EmergencyCallingConfiguration": {"shape": "EmergencyCallingConfiguration", "documentation": "<p>The details of the emergency calling configuration.</p>"}}}, "GetVoiceConnectorExternalSystemsConfigurationRequest": {"type": "structure", "required": ["VoiceConnectorId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "documentation": "<p>The ID of the Voice Connector for which to return information about the external system configuration.</p>", "location": "uri", "locationName": "voiceConnectorId"}}}, "GetVoiceConnectorExternalSystemsConfigurationResponse": {"type": "structure", "members": {"ExternalSystemsConfiguration": {"shape": "ExternalSystemsConfiguration", "documentation": "<p>An object that contains information about an external systems configuration for a Voice Connector.</p>"}}}, "GetVoiceConnectorGroupRequest": {"type": "structure", "required": ["VoiceConnectorGroupId"], "members": {"VoiceConnectorGroupId": {"shape": "NonEmptyString", "documentation": "<p>The Voice Connector group ID.</p>", "location": "uri", "locationName": "voiceConnectorGroupId"}}}, "GetVoiceConnectorGroupResponse": {"type": "structure", "members": {"VoiceConnectorGroup": {"shape": "VoiceConnectorGroup", "documentation": "<p>The details of the Voice Connector group.</p>"}}}, "GetVoiceConnectorLoggingConfigurationRequest": {"type": "structure", "required": ["VoiceConnectorId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "documentation": "<p>The Voice Connector ID.</p>", "location": "uri", "locationName": "voiceConnectorId"}}}, "GetVoiceConnectorLoggingConfigurationResponse": {"type": "structure", "members": {"LoggingConfiguration": {"shape": "LoggingConfiguration", "documentation": "<p>The logging configuration details .</p>"}}}, "GetVoiceConnectorOriginationRequest": {"type": "structure", "required": ["VoiceConnectorId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "documentation": "<p>The Voice Connector ID.</p>", "location": "uri", "locationName": "voiceConnectorId"}}}, "GetVoiceConnectorOriginationResponse": {"type": "structure", "members": {"Origination": {"shape": "Origination", "documentation": "<p>The origination setting details.</p>"}}}, "GetVoiceConnectorProxyRequest": {"type": "structure", "required": ["VoiceConnectorId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString128", "documentation": "<p>The Voice Connector ID.</p>", "location": "uri", "locationName": "voiceConnectorId"}}}, "GetVoiceConnectorProxyResponse": {"type": "structure", "members": {"Proxy": {"shape": "Proxy", "documentation": "<p>The proxy configuration details.</p>"}}}, "GetVoiceConnectorRequest": {"type": "structure", "required": ["VoiceConnectorId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "documentation": "<p>The Voice Connector ID.</p>", "location": "uri", "locationName": "voiceConnectorId"}}}, "GetVoiceConnectorResponse": {"type": "structure", "members": {"VoiceConnector": {"shape": "VoiceConnector", "documentation": "<p>The Voice Connector details.</p>"}}}, "GetVoiceConnectorStreamingConfigurationRequest": {"type": "structure", "required": ["VoiceConnectorId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "documentation": "<p>The Voice Connector ID.</p>", "location": "uri", "locationName": "voiceConnectorId"}}}, "GetVoiceConnectorStreamingConfigurationResponse": {"type": "structure", "members": {"StreamingConfiguration": {"shape": "StreamingConfiguration", "documentation": "<p>The details of the streaming configuration.</p>"}}}, "GetVoiceConnectorTerminationHealthRequest": {"type": "structure", "required": ["VoiceConnectorId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "documentation": "<p>The Voice Connector ID.</p>", "location": "uri", "locationName": "voiceConnectorId"}}}, "GetVoiceConnectorTerminationHealthResponse": {"type": "structure", "members": {"TerminationHealth": {"shape": "TerminationHealth", "documentation": "<p>The termination health details.</p>"}}}, "GetVoiceConnectorTerminationRequest": {"type": "structure", "required": ["VoiceConnectorId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "documentation": "<p>The Voice Connector ID.</p>", "location": "uri", "locationName": "voiceConnectorId"}}}, "GetVoiceConnectorTerminationResponse": {"type": "structure", "members": {"Termination": {"shape": "Termination", "documentation": "<p>The termination setting details.</p>"}}}, "GetVoiceProfileDomainRequest": {"type": "structure", "required": ["VoiceProfileDomainId"], "members": {"VoiceProfileDomainId": {"shape": "NonEmptyString256", "documentation": "<p>The voice profile domain ID.</p>", "location": "uri", "locationName": "VoiceProfileDomainId"}}}, "GetVoiceProfileDomainResponse": {"type": "structure", "members": {"VoiceProfileDomain": {"shape": "VoiceProfileDomain", "documentation": "<p>The details of the voice profile domain.</p>"}}}, "GetVoiceProfileRequest": {"type": "structure", "required": ["VoiceProfileId"], "members": {"VoiceProfileId": {"shape": "NonEmptyString256", "documentation": "<p>The voice profile ID.</p>", "location": "uri", "locationName": "VoiceProfileId"}}}, "GetVoiceProfileResponse": {"type": "structure", "members": {"VoiceProfile": {"shape": "VoiceProfile", "documentation": "<p>The voice profile details.</p>"}}}, "GetVoiceToneAnalysisTaskRequest": {"type": "structure", "required": ["VoiceConnectorId", "VoiceToneAnalysisTaskId", "IsCaller"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString128", "documentation": "<p>The Voice Connector ID.</p>", "location": "uri", "locationName": "VoiceConnectorId"}, "VoiceToneAnalysisTaskId": {"shape": "NonEmptyString256", "documentation": "<p>The ID of the voice tone analysis task.</p>", "location": "uri", "locationName": "VoiceToneAnalysisTaskId"}, "IsCaller": {"shape": "Boolean", "documentation": "<p>Specifies whether the voice being analyzed is the caller (originator) or the callee (responder).</p>", "location": "querystring", "locationName": "isCaller"}}}, "GetVoiceToneAnalysisTaskResponse": {"type": "structure", "members": {"VoiceToneAnalysisTask": {"shape": "VoiceToneAnalysisTask", "documentation": "<p>The details of the voice tone analysis task.</p>"}}}, "GoneException": {"type": "structure", "members": {}, "documentation": "<p>Access to the target resource is no longer available at the origin server. This condition is likely to be permanent.</p>", "error": {"httpStatusCode": 410}, "exception": true}, "GuidString": {"type": "string", "pattern": "[a-fA-F0-9]{8}(?:-[a-fA-F0-9]{4}){3}-[a-fA-F0-9]{12}"}, "Integer": {"type": "integer"}, "Iso8601Timestamp": {"type": "timestamp", "timestampFormat": "iso8601"}, "LanguageCode": {"type": "string", "enum": ["en-US"]}, "ListAvailableVoiceConnectorRegionsResponse": {"type": "structure", "members": {"VoiceConnectorRegions": {"shape": "VoiceConnectorAwsRegionList", "documentation": "<p>The list of AWS Regions.</p>"}}}, "ListPhoneNumberOrdersRequest": {"type": "structure", "members": {"NextToken": {"shape": "String", "documentation": "<p>The token used to retrieve the next page of results.</p>", "location": "querystring", "locationName": "next-token"}, "MaxResults": {"shape": "ResultMax", "documentation": "<p>The maximum number of results to return in a single call.</p>", "location": "querystring", "locationName": "max-results"}}}, "ListPhoneNumberOrdersResponse": {"type": "structure", "members": {"PhoneNumberOrders": {"shape": "PhoneNumberOrderList", "documentation": "<p>The phone number order details.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The token used to retrieve the next page of results.</p>"}}}, "ListPhoneNumbersRequest": {"type": "structure", "members": {"Status": {"shape": "String", "documentation": "<p>The status of your organization's phone numbers.</p>", "location": "querystring", "locationName": "status"}, "ProductType": {"shape": "PhoneNumberProductType", "documentation": "<p>The phone number product types.</p>", "location": "querystring", "locationName": "product-type"}, "FilterName": {"shape": "PhoneNumberAssociationName", "documentation": "<p>The filter to limit the number of results.</p>", "location": "querystring", "locationName": "filter-name"}, "FilterValue": {"shape": "String", "documentation": "<p>The filter value.</p>", "location": "querystring", "locationName": "filter-value"}, "MaxResults": {"shape": "ResultMax", "documentation": "<p>The maximum number of results to return in a single call.</p>", "location": "querystring", "locationName": "max-results"}, "NextToken": {"shape": "String", "documentation": "<p>The token used to return the next page of results.</p>", "location": "querystring", "locationName": "next-token"}}}, "ListPhoneNumbersResponse": {"type": "structure", "members": {"PhoneNumbers": {"shape": "PhoneNumberList", "documentation": "<p>The phone number details.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The token used to return the next page of results.</p>"}}}, "ListProxySessionsRequest": {"type": "structure", "required": ["VoiceConnectorId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString128", "documentation": "<p>The Voice Connector ID.</p>", "location": "uri", "locationName": "voiceConnectorId"}, "Status": {"shape": "ProxySessionStatus", "documentation": "<p>The proxy session status.</p>", "location": "querystring", "locationName": "status"}, "NextToken": {"shape": "NextTokenString", "documentation": "<p>The token used to retrieve the next page of results.</p>", "location": "querystring", "locationName": "next-token"}, "MaxResults": {"shape": "ResultMax", "documentation": "<p>The maximum number of results to return in a single call.</p>", "location": "querystring", "locationName": "max-results"}}}, "ListProxySessionsResponse": {"type": "structure", "members": {"ProxySessions": {"shape": "ProxySessions", "documentation": "<p>The proxy sessions' details.</p>"}, "NextToken": {"shape": "NextTokenString", "documentation": "<p>The token used to retrieve the next page of results.</p>"}}}, "ListSipMediaApplicationsRequest": {"type": "structure", "members": {"MaxResults": {"shape": "ResultMax", "documentation": "<p>The maximum number of results to return in a single call. Defaults to 100.</p>", "location": "querystring", "locationName": "max-results"}, "NextToken": {"shape": "NextTokenString", "documentation": "<p>The token used to return the next page of results.</p>", "location": "querystring", "locationName": "next-token"}}}, "ListSipMediaApplicationsResponse": {"type": "structure", "members": {"SipMediaApplications": {"shape": "SipMediaApplicationList", "documentation": "<p>The list of SIP media applications and application details.</p>"}, "NextToken": {"shape": "NextTokenString", "documentation": "<p>The token used to return the next page of results.</p>"}}}, "ListSipRulesRequest": {"type": "structure", "members": {"SipMediaApplicationId": {"shape": "NonEmptyString", "documentation": "<p>The SIP media application ID.</p>", "location": "querystring", "locationName": "sip-media-application"}, "MaxResults": {"shape": "ResultMax", "documentation": "<p>The maximum number of results to return in a single call. Defaults to 100.</p>", "location": "querystring", "locationName": "max-results"}, "NextToken": {"shape": "NextTokenString", "documentation": "<p>The token used to return the next page of results.</p>", "location": "querystring", "locationName": "next-token"}}}, "ListSipRulesResponse": {"type": "structure", "members": {"SipRules": {"shape": "SipRuleList", "documentation": "<p>The list of SIP rules and details.</p>"}, "NextToken": {"shape": "NextTokenString", "documentation": "<p>The token used to return the next page of results.</p>"}}}, "ListSupportedPhoneNumberCountriesRequest": {"type": "structure", "required": ["ProductType"], "members": {"ProductType": {"shape": "PhoneNumberProductType", "documentation": "<p>The phone number product type.</p>", "location": "querystring", "locationName": "product-type"}}}, "ListSupportedPhoneNumberCountriesResponse": {"type": "structure", "members": {"PhoneNumberCountries": {"shape": "PhoneNumberCountriesList", "documentation": "<p>The supported phone number countries.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["ResourceARN"], "members": {"ResourceARN": {"shape": "<PERSON><PERSON>", "documentation": "<p>The resource ARN.</p>", "location": "querystring", "locationName": "arn"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"Tags": {"shape": "TagList", "documentation": "<p>The tags in the list.</p>"}}}, "ListVoiceConnectorGroupsRequest": {"type": "structure", "members": {"NextToken": {"shape": "String", "documentation": "<p>The token used to return the next page of results.</p>", "location": "querystring", "locationName": "next-token"}, "MaxResults": {"shape": "ResultMax", "documentation": "<p>The maximum number of results to return in a single call. </p>", "location": "querystring", "locationName": "max-results"}}}, "ListVoiceConnectorGroupsResponse": {"type": "structure", "members": {"VoiceConnectorGroups": {"shape": "VoiceConnectorGroupList", "documentation": "<p>The details of the Voice Connector groups.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The token used to return the next page of results.</p>"}}}, "ListVoiceConnectorTerminationCredentialsRequest": {"type": "structure", "required": ["VoiceConnectorId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "documentation": "<p>The Voice Connector ID.</p>", "location": "uri", "locationName": "voiceConnectorId"}}}, "ListVoiceConnectorTerminationCredentialsResponse": {"type": "structure", "members": {"Usernames": {"shape": "SensitiveStringList", "documentation": "<p>A list of user names.</p>"}}}, "ListVoiceConnectorsRequest": {"type": "structure", "members": {"NextToken": {"shape": "String", "documentation": "<p>The token used to return the next page of results.</p>", "location": "querystring", "locationName": "next-token"}, "MaxResults": {"shape": "ResultMax", "documentation": "<p>The maximum number of results to return in a single call.</p>", "location": "querystring", "locationName": "max-results"}}}, "ListVoiceConnectorsResponse": {"type": "structure", "members": {"VoiceConnectors": {"shape": "VoiceConnectorList", "documentation": "<p>The details of the Voice Connectors.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The token used to return the next page of results.</p>"}}}, "ListVoiceProfileDomainsRequest": {"type": "structure", "members": {"NextToken": {"shape": "String", "documentation": "<p>The token used to return the next page of results.</p>", "location": "querystring", "locationName": "next-token"}, "MaxResults": {"shape": "ResultMax", "documentation": "<p>The maximum number of results to return in a single call.</p>", "location": "querystring", "locationName": "max-results"}}}, "ListVoiceProfileDomainsResponse": {"type": "structure", "members": {"VoiceProfileDomains": {"shape": "VoiceProfileDomainSummaryList", "documentation": "<p>The list of voice profile domains.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The token used to return the next page of results.</p>"}}}, "ListVoiceProfilesRequest": {"type": "structure", "required": ["VoiceProfileDomainId"], "members": {"VoiceProfileDomainId": {"shape": "NonEmptyString256", "documentation": "<p>The ID of the voice profile domain.</p>", "location": "querystring", "locationName": "voice-profile-domain-id"}, "NextToken": {"shape": "String", "documentation": "<p>The token used to retrieve the next page of results.</p>", "location": "querystring", "locationName": "next-token"}, "MaxResults": {"shape": "ResultMax", "documentation": "<p>The maximum number of results in the request.</p>", "location": "querystring", "locationName": "max-results"}}}, "ListVoiceProfilesResponse": {"type": "structure", "members": {"VoiceProfiles": {"shape": "VoiceProfileSummaryList", "documentation": "<p>The list of voice profiles.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The token used to retrieve the next page of results.</p>"}}}, "LoggingConfiguration": {"type": "structure", "members": {"EnableSIPLogs": {"shape": "Boolean", "documentation": "<p>Boolean that enables sending SIP message logs to Amazon CloudWatch.</p>"}, "EnableMediaMetricLogs": {"shape": "Boolean", "documentation": "<p>Enables or disables media metrics logging.</p>"}}, "documentation": "<p>The logging configuration associated with an Amazon Chime SDK Voice Connector. Specifies whether SIP message logs can be sent to Amazon CloudWatch Logs.</p>"}, "MediaInsightsConfiguration": {"type": "structure", "members": {"Disabled": {"shape": "Boolean", "documentation": "<p>Denotes the configuration as enabled or disabled.</p>"}, "ConfigurationArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The configuration's ARN.</p>"}}, "documentation": "<p>The configuration for a call analytics task.</p>"}, "NextTokenString": {"type": "string", "max": 65535}, "NonEmptyString": {"type": "string", "pattern": ".*\\S.*"}, "NonEmptyString128": {"type": "string", "max": 128, "min": 1, "pattern": ".*\\S.*"}, "NonEmptyString256": {"type": "string", "max": 256, "min": 1, "pattern": ".*\\S.*"}, "NonEmptyStringList": {"type": "list", "member": {"shape": "String"}, "min": 1}, "NotFoundException": {"type": "structure", "members": {}, "documentation": "<p>The requested resource couldn't be found.</p>", "error": {"httpStatusCode": 404}, "exception": true}, "NotificationTarget": {"type": "string", "enum": ["EventBridge", "SNS", "SQS"]}, "NullableBoolean": {"type": "boolean"}, "NumberSelectionBehavior": {"type": "string", "enum": ["PreferSticky", "AvoidSticky"]}, "OrderedPhoneNumber": {"type": "structure", "members": {"E164PhoneNumber": {"shape": "E164PhoneNumber", "documentation": "<p>The phone number, in E.164 format.</p>"}, "Status": {"shape": "OrderedPhoneNumberStatus", "documentation": "<p>The phone number status.</p>"}}, "documentation": "<p>A phone number for which an order has been placed.</p>"}, "OrderedPhoneNumberList": {"type": "list", "member": {"shape": "OrderedPhoneNumber"}}, "OrderedPhoneNumberStatus": {"type": "string", "enum": ["Processing", "Acquired", "Failed"]}, "Origination": {"type": "structure", "members": {"Routes": {"shape": "OriginationRouteList", "documentation": "<p>The call distribution properties defined for your SIP hosts. Valid range: Minimum value of 1. Maximum value of 20. This parameter is not required, but you must specify this parameter or <code>Disabled</code>.</p>"}, "Disabled": {"shape": "Boolean", "documentation": "<p>When origination settings are disabled, inbound calls are not enabled for your Amazon Chime SDK Voice Connector. This parameter is not required, but you must specify this parameter or <code>Routes</code>.</p>"}}, "documentation": "<p>Origination settings enable your SIP hosts to receive inbound calls using your Amazon Chime SDK Voice Connector.</p> <note> <p>The parameters listed below are not required, but you must use at least one.</p> </note>"}, "OriginationRoute": {"type": "structure", "members": {"Host": {"shape": "String", "documentation": "<p>The FQDN or IP address to contact for origination traffic.</p>"}, "Port": {"shape": "Port", "documentation": "<p>The designated origination route port. Defaults to 5060.</p>"}, "Protocol": {"shape": "OriginationRouteProtocol", "documentation": "<p>The protocol to use for the origination route. Encryption-enabled Amazon Chime SDK Voice Connectors use TCP protocol by default.</p>"}, "Priority": {"shape": "OriginationRoutePriority", "documentation": "<p>The priority associated with the host, with 1 being the highest priority. Higher priority hosts are attempted first.</p>"}, "Weight": {"shape": "OriginationRouteWeight", "documentation": "<p>The weight assigned to an origination route. When hosts have equal priority, calls are distributed between them based on their relative weights.</p>"}}, "documentation": "<p>Origination routes define call distribution properties for your SIP hosts to receive inbound calls using an Amazon Chime SDK Voice Connector. Limit: Ten origination routes for each Voice Connector.</p> <note> <p>The parameters listed below are not required, but you must use at least one.</p> </note>"}, "OriginationRouteList": {"type": "list", "member": {"shape": "OriginationRoute"}}, "OriginationRoutePriority": {"type": "integer", "max": 100, "min": 1}, "OriginationRouteProtocol": {"type": "string", "enum": ["TCP", "UDP"]}, "OriginationRouteWeight": {"type": "integer", "max": 100, "min": 1}, "Participant": {"type": "structure", "members": {"PhoneNumber": {"shape": "E164PhoneNumber", "documentation": "<p>The participant's phone number.</p>"}, "ProxyPhoneNumber": {"shape": "E164PhoneNumber", "documentation": "<p>The participant's proxy phone number.</p>"}}, "documentation": "<p>The phone number and proxy phone number for a participant in an Amazon Chime SDK Voice Connector proxy session.</p>"}, "ParticipantPhoneNumberList": {"type": "list", "member": {"shape": "E164PhoneNumber"}, "max": 2, "min": 2}, "Participants": {"type": "list", "member": {"shape": "Participant"}}, "PhoneNumber": {"type": "structure", "members": {"PhoneNumberId": {"shape": "SensitiveNonEmptyString", "documentation": "<p>The phone number's ID.</p>"}, "E164PhoneNumber": {"shape": "E164PhoneNumber", "documentation": "<p>The phone number, in E.164 format.</p>"}, "Country": {"shape": "Alpha2CountryCode", "documentation": "<p>The phone number's country. Format: ISO 3166-1 alpha-2.</p>"}, "Type": {"shape": "PhoneNumberType", "documentation": "<p>The phone number's type.</p>"}, "ProductType": {"shape": "PhoneNumberProductType", "documentation": "<p>The phone number's product type.</p>"}, "Status": {"shape": "PhoneNumberStatus", "documentation": "<p>The phone number's status.</p>"}, "Capabilities": {"shape": "PhoneNumberCapabilities", "documentation": "<p>The phone number's capabilities.</p>"}, "Associations": {"shape": "PhoneNumberAssociationList", "documentation": "<p>The phone number's associations.</p>"}, "CallingName": {"shape": "CallingName", "documentation": "<p>The outbound calling name associated with the phone number.</p>"}, "CallingNameStatus": {"shape": "CallingNameStatus", "documentation": "<p>The outbound calling name status.</p>"}, "CreatedTimestamp": {"shape": "Iso8601Timestamp", "documentation": "<p>The phone number creation timestamp, in ISO 8601 format.</p>"}, "UpdatedTimestamp": {"shape": "Iso8601Timestamp", "documentation": "<p>The updated phone number timestamp, in ISO 8601 format.</p>"}, "DeletionTimestamp": {"shape": "Iso8601Timestamp", "documentation": "<p>The deleted phone number timestamp, in ISO 8601 format.</p>"}, "OrderId": {"shape": "GuidString", "documentation": "<p>The phone number's order ID.</p>"}, "Name": {"shape": "PhoneNumberName", "documentation": "<p>The name of the phone number.</p>"}}, "documentation": "<p>A phone number used to call an Amazon Chime SDK Voice Connector.</p>"}, "PhoneNumberAssociation": {"type": "structure", "members": {"Value": {"shape": "String", "documentation": "<p>Contains the ID for the entity specified in Name.</p>"}, "Name": {"shape": "PhoneNumberAssociationName", "documentation": "<p>Defines the association with an Amazon Chime SDK account ID, user ID, Voice Connector ID, or Voice Connector group ID.</p>"}, "AssociatedTimestamp": {"shape": "Iso8601Timestamp", "documentation": "<p>The timestamp of the phone number association, in ISO 8601 format.</p>"}}, "documentation": "<p>The phone number associations, such as an Amazon Chime SDK account ID, user ID, Voice Connector ID, or Voice Connector group ID.</p>"}, "PhoneNumberAssociationList": {"type": "list", "member": {"shape": "PhoneNumberAssociation"}}, "PhoneNumberAssociationName": {"type": "string", "enum": ["VoiceConnectorId", "VoiceConnectorGroupId", "SipRuleId"]}, "PhoneNumberCapabilities": {"type": "structure", "members": {"InboundCall": {"shape": "NullableBoolean", "documentation": "<p>Allows or denies inbound calling for the specified phone number.</p>"}, "OutboundCall": {"shape": "NullableBoolean", "documentation": "<p>Allows or denies outbound calling for the specified phone number.</p>"}, "InboundSMS": {"shape": "NullableBoolean", "documentation": "<p>Allows or denies inbound SMS messaging for the specified phone number.</p>"}, "OutboundSMS": {"shape": "NullableBoolean", "documentation": "<p>Allows or denies outbound SMS messaging for the specified phone number.</p>"}, "InboundMMS": {"shape": "NullableBoolean", "documentation": "<p>Allows or denies inbound MMS messaging for the specified phone number.</p>"}, "OutboundMMS": {"shape": "NullableBoolean", "documentation": "<p>Allows or denies inbound MMS messaging for the specified phone number.</p>"}}, "documentation": "<p>The phone number capabilities for Amazon Chime SDK phone numbers, such as enabled inbound and outbound calling, and text messaging.</p>"}, "PhoneNumberCountriesList": {"type": "list", "member": {"shape": "PhoneNumberCountry"}}, "PhoneNumberCountry": {"type": "structure", "members": {"CountryCode": {"shape": "Alpha2CountryCode", "documentation": "<p>The phone number country code. Format: ISO 3166-1 alpha-2.</p>"}, "SupportedPhoneNumberTypes": {"shape": "PhoneNumberTypeList", "documentation": "<p>The supported phone number types.</p>"}}, "documentation": "<p>The phone number's country.</p>"}, "PhoneNumberError": {"type": "structure", "members": {"PhoneNumberId": {"shape": "SensitiveNonEmptyString", "documentation": "<p>The phone number ID for which the action failed.</p>"}, "ErrorCode": {"shape": "ErrorCode", "documentation": "<p>The error code.</p>"}, "ErrorMessage": {"shape": "String", "documentation": "<p>The error message.</p>"}}, "documentation": "<p>If a phone number action fails for one or more of the phone numbers in a request, a list of the failed phone numbers is returned, along with error codes and error messages.</p>"}, "PhoneNumberErrorList": {"type": "list", "member": {"shape": "PhoneNumberError"}}, "PhoneNumberList": {"type": "list", "member": {"shape": "PhoneNumber"}}, "PhoneNumberMaxResults": {"type": "integer", "max": 500, "min": 1}, "PhoneNumberName": {"type": "string", "max": 256, "min": 0, "pattern": "^$|^[a-zA-Z0-9\\,\\.\\_\\-]+(\\s+[a-zA-Z0-9\\,\\.\\_\\-]+)*$", "sensitive": true}, "PhoneNumberOrder": {"type": "structure", "members": {"PhoneNumberOrderId": {"shape": "GuidString", "documentation": "<p>The ID of the phone order.</p>"}, "ProductType": {"shape": "PhoneNumberProductType", "documentation": "<p>The phone number order product type.</p>"}, "Status": {"shape": "PhoneNumberOrderStatus", "documentation": "<p>The status of the phone number order.</p>"}, "OrderType": {"shape": "PhoneNumberOrderType", "documentation": "<p>The type of phone number being ordered, local or toll-free.</p>"}, "OrderedPhoneNumbers": {"shape": "OrderedPhoneNumberList", "documentation": "<p>The ordered phone number details, such as the phone number in E.164 format and the phone number status.</p>"}, "CreatedTimestamp": {"shape": "Iso8601Timestamp", "documentation": "<p>The phone number order creation time stamp, in ISO 8601 format.</p>"}, "UpdatedTimestamp": {"shape": "Iso8601Timestamp", "documentation": "<p>The updated phone number order time stamp, in ISO 8601 format.</p>"}, "FocDate": {"shape": "Iso8601Timestamp", "documentation": "<p>The Firm Order Commitment (FOC) date for phone number porting orders. This field is null if a phone number order is not a porting order.</p>"}}, "documentation": "<p>The details of an Amazon Chime SDK phone number order.</p>"}, "PhoneNumberOrderList": {"type": "list", "member": {"shape": "PhoneNumberOrder"}}, "PhoneNumberOrderStatus": {"type": "string", "enum": ["Processing", "Successful", "Failed", "Partial", "PendingDocuments", "Submitted", "FOC", "ChangeRequested", "Exception", "CancelRequested", "Cancelled"]}, "PhoneNumberOrderType": {"type": "string", "enum": ["New", "Porting"]}, "PhoneNumberProductType": {"type": "string", "enum": ["VoiceConnector", "SipMediaApplicationDialIn"]}, "PhoneNumberStatus": {"type": "string", "enum": ["Cancelled", "PortinCancelRequested", "PortinInProgress", "AcquireInProgress", "AcquireFailed", "Unassigned", "Assigned", "ReleaseInProgress", "DeleteInProgress", "ReleaseFailed", "DeleteFailed"]}, "PhoneNumberType": {"type": "string", "enum": ["Local", "<PERSON>ll<PERSON><PERSON>"]}, "PhoneNumberTypeList": {"type": "list", "member": {"shape": "PhoneNumberType"}}, "Port": {"type": "integer", "max": 65535, "min": 0}, "PositiveInteger": {"type": "integer", "min": 1}, "Proxy": {"type": "structure", "members": {"DefaultSessionExpiryMinutes": {"shape": "Integer", "documentation": "<p>The default number of minutes allowed for proxy sessions.</p>"}, "Disabled": {"shape": "Boolean", "documentation": "<p>When true, stops proxy sessions from being created on the specified Amazon Chime SDK Voice Connector.</p>"}, "FallBackPhoneNumber": {"shape": "E164PhoneNumber", "documentation": "<p>The phone number to route calls to after a proxy session expires.</p>"}, "PhoneNumberCountries": {"shape": "StringList", "documentation": "<p>The countries for proxy phone numbers to be selected from.</p>"}}, "documentation": "<p>The proxy configuration for an Amazon Chime SDK Voice Connector.</p>"}, "ProxySession": {"type": "structure", "members": {"VoiceConnectorId": {"shape": "NonEmptyString128", "documentation": "<p>The Voice Connector ID.</p>"}, "ProxySessionId": {"shape": "NonEmptyString128", "documentation": "<p>The proxy session ID.</p>"}, "Name": {"shape": "String128", "documentation": "<p>The proxy session name.</p>"}, "Status": {"shape": "ProxySessionStatus", "documentation": "<p>The proxy session status.</p>"}, "ExpiryMinutes": {"shape": "PositiveInteger", "documentation": "<p>The number of minutes allowed for the proxy session.</p>"}, "Capabilities": {"shape": "CapabilityList", "documentation": "<p>The proxy session capabilities.</p>"}, "CreatedTimestamp": {"shape": "Iso8601Timestamp", "documentation": "<p>The created time stamp, in ISO 8601 format.</p>"}, "UpdatedTimestamp": {"shape": "Iso8601Timestamp", "documentation": "<p>The updated time stamp, in ISO 8601 format.</p>"}, "EndedTimestamp": {"shape": "Iso8601Timestamp", "documentation": "<p>The ended time stamp, in ISO 8601 format.</p>"}, "Participants": {"shape": "Participants", "documentation": "<p>The proxy session participants.</p>"}, "NumberSelectionBehavior": {"shape": "NumberSelectionBehavior", "documentation": "<p>The preference for proxy phone number reuse, or stickiness, between the same participants across sessions.</p>"}, "GeoMatchLevel": {"shape": "GeoMatchLevel", "documentation": "<p>The preference for matching the country or area code of the proxy phone number with that of the first participant.</p>"}, "GeoMatchParams": {"shape": "GeoMatchParams", "documentation": "<p>The country and area code for the proxy phone number.</p>"}}, "documentation": "<p>The proxy session for an Amazon Chime SDK Voice Connector.</p>"}, "ProxySessionNameString": {"type": "string", "pattern": "^$|^[a-zA-Z0-9 ]{0,30}$", "sensitive": true}, "ProxySessionStatus": {"type": "string", "enum": ["Open", "InProgress", "Closed"]}, "ProxySessions": {"type": "list", "member": {"shape": "ProxySession"}}, "PutSipMediaApplicationAlexaSkillConfigurationRequest": {"type": "structure", "required": ["SipMediaApplicationId"], "members": {"SipMediaApplicationId": {"shape": "NonEmptyString", "documentation": "<p>The SIP media application ID.</p>", "location": "uri", "locationName": "sipMediaApplicationId"}, "SipMediaApplicationAlexaSkillConfiguration": {"shape": "SipMediaApplicationAlexaSkillConfiguration", "documentation": "<p>The Alexa Skill configuration.</p>"}}}, "PutSipMediaApplicationAlexaSkillConfigurationResponse": {"type": "structure", "members": {"SipMediaApplicationAlexaSkillConfiguration": {"shape": "SipMediaApplicationAlexaSkillConfiguration", "documentation": "<p>Returns the Alexa Skill configuration.</p>"}}}, "PutSipMediaApplicationLoggingConfigurationRequest": {"type": "structure", "required": ["SipMediaApplicationId"], "members": {"SipMediaApplicationId": {"shape": "NonEmptyString", "documentation": "<p>The SIP media application ID.</p>", "location": "uri", "locationName": "sipMediaApplicationId"}, "SipMediaApplicationLoggingConfiguration": {"shape": "SipMediaApplicationLoggingConfiguration", "documentation": "<p>The logging configuration for the specified SIP media application.</p>"}}}, "PutSipMediaApplicationLoggingConfigurationResponse": {"type": "structure", "members": {"SipMediaApplicationLoggingConfiguration": {"shape": "SipMediaApplicationLoggingConfiguration", "documentation": "<p>The updated logging configuration for the specified SIP media application.</p>"}}}, "PutVoiceConnectorEmergencyCallingConfigurationRequest": {"type": "structure", "required": ["VoiceConnectorId", "EmergencyCallingConfiguration"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "documentation": "<p>The Voice Connector ID.</p>", "location": "uri", "locationName": "voiceConnectorId"}, "EmergencyCallingConfiguration": {"shape": "EmergencyCallingConfiguration", "documentation": "<p>The configuration being updated.</p>"}}}, "PutVoiceConnectorEmergencyCallingConfigurationResponse": {"type": "structure", "members": {"EmergencyCallingConfiguration": {"shape": "EmergencyCallingConfiguration", "documentation": "<p>The updated configuration.</p>"}}}, "PutVoiceConnectorExternalSystemsConfigurationRequest": {"type": "structure", "required": ["VoiceConnectorId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString128", "documentation": "<p>The ID of the Voice Connector for which to add the external system configuration.</p>", "location": "uri", "locationName": "voiceConnectorId"}, "SessionBorderControllerTypes": {"shape": "SessionBorderControllerTypeList", "documentation": "<p>The session border controllers to use.</p>"}, "ContactCenterSystemTypes": {"shape": "ContactCenterSystemTypeList", "documentation": "<p>The contact center system to use.</p>"}}}, "PutVoiceConnectorExternalSystemsConfigurationResponse": {"type": "structure", "members": {"ExternalSystemsConfiguration": {"shape": "ExternalSystemsConfiguration", "documentation": "<p>An object that contains information about an external systems configuration for a Voice Connector.</p>"}}}, "PutVoiceConnectorLoggingConfigurationRequest": {"type": "structure", "required": ["VoiceConnectorId", "LoggingConfiguration"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "documentation": "<p>The Voice Connector ID.</p>", "location": "uri", "locationName": "voiceConnectorId"}, "LoggingConfiguration": {"shape": "LoggingConfiguration", "documentation": "<p>The logging configuration being updated.</p>"}}}, "PutVoiceConnectorLoggingConfigurationResponse": {"type": "structure", "members": {"LoggingConfiguration": {"shape": "LoggingConfiguration", "documentation": "<p>The updated logging configuration.</p>"}}}, "PutVoiceConnectorOriginationRequest": {"type": "structure", "required": ["VoiceConnectorId", "Origination"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "documentation": "<p>The Voice Connector ID.</p>", "location": "uri", "locationName": "voiceConnectorId"}, "Origination": {"shape": "Origination", "documentation": "<p>The origination settings being updated.</p>"}}}, "PutVoiceConnectorOriginationResponse": {"type": "structure", "members": {"Origination": {"shape": "Origination", "documentation": "<p>The updated origination settings.</p>"}}}, "PutVoiceConnectorProxyRequest": {"type": "structure", "required": ["DefaultSessionExpiryMinutes", "PhoneNumberPoolCountries", "VoiceConnectorId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString128", "documentation": "<p>The Voice Connector ID.</p>", "location": "uri", "locationName": "voiceConnectorId"}, "DefaultSessionExpiryMinutes": {"shape": "Integer", "documentation": "<p>The default number of minutes allowed for proxy session.</p>"}, "PhoneNumberPoolCountries": {"shape": "CountryList", "documentation": "<p>The countries for proxy phone numbers to be selected from.</p>"}, "FallBackPhoneNumber": {"shape": "E164PhoneNumber", "documentation": "<p>The phone number to route calls to after a proxy session expires.</p>"}, "Disabled": {"shape": "Boolean", "documentation": "<p>When true, stops proxy sessions from being created on the specified Amazon Chime SDK Voice Connector.</p>"}}}, "PutVoiceConnectorProxyResponse": {"type": "structure", "members": {"Proxy": {"shape": "Proxy", "documentation": "<p>The proxy configuration details.</p>"}}}, "PutVoiceConnectorStreamingConfigurationRequest": {"type": "structure", "required": ["VoiceConnectorId", "StreamingConfiguration"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "documentation": "<p>The Voice Connector ID.</p>", "location": "uri", "locationName": "voiceConnectorId"}, "StreamingConfiguration": {"shape": "StreamingConfiguration", "documentation": "<p>The streaming settings being updated.</p>"}}}, "PutVoiceConnectorStreamingConfigurationResponse": {"type": "structure", "members": {"StreamingConfiguration": {"shape": "StreamingConfiguration", "documentation": "<p>The updated streaming settings.</p>"}}}, "PutVoiceConnectorTerminationCredentialsRequest": {"type": "structure", "required": ["VoiceConnectorId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "documentation": "<p>The Voice Connector ID.</p>", "location": "uri", "locationName": "voiceConnectorId"}, "Credentials": {"shape": "CredentialList", "documentation": "<p>The termination credentials being updated.</p>"}}}, "PutVoiceConnectorTerminationRequest": {"type": "structure", "required": ["VoiceConnectorId", "Termination"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "documentation": "<p>The Voice Connector ID.</p>", "location": "uri", "locationName": "voiceConnectorId"}, "Termination": {"shape": "Termination", "documentation": "<p>The termination settings to be updated.</p>"}}}, "PutVoiceConnectorTerminationResponse": {"type": "structure", "members": {"Termination": {"shape": "Termination", "documentation": "<p>The updated termination settings.</p>"}}}, "ResourceLimitExceededException": {"type": "structure", "members": {}, "documentation": "<p>The request exceeds the resource limit.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "RestorePhoneNumberRequest": {"type": "structure", "required": ["PhoneNumberId"], "members": {"PhoneNumberId": {"shape": "SensitiveNonEmptyString", "documentation": "<p>The ID of the phone number being restored.</p>", "location": "uri", "locationName": "phoneNumberId"}}}, "RestorePhoneNumberResponse": {"type": "structure", "members": {"PhoneNumber": {"shape": "PhoneNumber", "documentation": "<p>The restored phone number.</p>"}}}, "ResultMax": {"type": "integer", "max": 100, "min": 1}, "SMACreateCallArgumentsMap": {"type": "map", "key": {"shape": "SensitiveString"}, "value": {"shape": "SensitiveString"}, "max": 20, "min": 0}, "SMAUpdateCallArgumentsMap": {"type": "map", "key": {"shape": "SensitiveString"}, "value": {"shape": "SensitiveString"}, "max": 20, "min": 0}, "SearchAvailablePhoneNumbersRequest": {"type": "structure", "members": {"AreaCode": {"shape": "String", "documentation": "<p>Confines a search to just the phone numbers associated with the specified area code.</p>", "location": "querystring", "locationName": "area-code"}, "City": {"shape": "String", "documentation": "<p>Confines a search to just the phone numbers associated with the specified city.</p>", "location": "querystring", "locationName": "city"}, "Country": {"shape": "Alpha2CountryCode", "documentation": "<p>Confines a search to just the phone numbers associated with the specified country.</p>", "location": "querystring", "locationName": "country"}, "State": {"shape": "String", "documentation": "<p>Confines a search to just the phone numbers associated with the specified state.</p>", "location": "querystring", "locationName": "state"}, "TollFreePrefix": {"shape": "TollFreePrefix", "documentation": "<p>Confines a search to just the phone numbers associated with the specified toll-free prefix.</p>", "location": "querystring", "locationName": "toll-free-prefix"}, "PhoneNumberType": {"shape": "PhoneNumberType", "documentation": "<p>Confines a search to just the phone numbers associated with the specified phone number type, either <b>local</b> or <b>toll-free</b>.</p>", "location": "querystring", "locationName": "phone-number-type"}, "MaxResults": {"shape": "PhoneNumberMaxResults", "documentation": "<p>The maximum number of results to return.</p>", "location": "querystring", "locationName": "max-results"}, "NextToken": {"shape": "String", "documentation": "<p>The token used to return the next page of results.</p>", "location": "querystring", "locationName": "next-token"}}}, "SearchAvailablePhoneNumbersResponse": {"type": "structure", "members": {"E164PhoneNumbers": {"shape": "E164PhoneNumberList", "documentation": "<p>Confines a search to just the phone numbers in the E.164 format.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The token used to return the next page of results.</p>"}}}, "SensitiveNonEmptyString": {"type": "string", "pattern": ".*\\S.*", "sensitive": true}, "SensitiveString": {"type": "string", "sensitive": true}, "SensitiveStringList": {"type": "list", "member": {"shape": "SensitiveString"}}, "ServerSideEncryptionConfiguration": {"type": "structure", "required": ["KmsKeyArn"], "members": {"KmsKeyArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the KMS key used to encrypt the enrollment data in a voice profile domain. Asymmetric customer managed keys are not supported.</p>"}}, "documentation": "<p>A structure that contains the configuration settings for server-side encryption.</p> <note> <p>We only support symmetric keys. Do not use asymmetric or HMAC keys, or KMS aliases.</p> </note>"}, "ServiceFailureException": {"type": "structure", "members": {}, "documentation": "<p>The service encountered an unexpected error.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true}, "ServiceUnavailableException": {"type": "structure", "members": {}, "documentation": "<p>The service is currently unavailable.</p>", "error": {"httpStatusCode": 503}, "exception": true, "fault": true}, "SessionBorderControllerType": {"type": "string", "enum": ["RIBBON_SBC", "ORACLE_ACME_PACKET_SBC", "AVAYA_SBCE", "CISCO_UNIFIED_BORDER_ELEMENT", "AUDIOCODES_MEDIANT_SBC"]}, "SessionBorderControllerTypeList": {"type": "list", "member": {"shape": "SessionBorderControllerType"}}, "SipApplicationPriority": {"type": "integer", "min": 1}, "SipHeadersMap": {"type": "map", "key": {"shape": "SensitiveString"}, "value": {"shape": "SensitiveString"}, "max": 20, "min": 0}, "SipMediaApplication": {"type": "structure", "members": {"SipMediaApplicationId": {"shape": "NonEmptyString", "documentation": "<p>A SIP media application's ID.</p>"}, "AwsRegion": {"shape": "String", "documentation": "<p>The AWS Region in which the SIP media application is created.</p>"}, "Name": {"shape": "SipMediaApplicationName", "documentation": "<p>The SIP media application's name.</p>"}, "Endpoints": {"shape": "SipMediaApplicationEndpointList", "documentation": "<p>List of endpoints for a SIP media application. Currently, only one endpoint per SIP media application is permitted.</p>"}, "CreatedTimestamp": {"shape": "Iso8601Timestamp", "documentation": "<p>The SIP media application creation timestamp, in ISO 8601 format.</p>"}, "UpdatedTimestamp": {"shape": "Iso8601Timestamp", "documentation": "<p>The time at which the SIP media application was updated.</p>"}, "SipMediaApplicationArn": {"shape": "NonEmptyString", "documentation": "<p>The ARN of the SIP media application.</p>"}}, "documentation": "<p>The details of the SIP media application, including name and endpoints. An AWS account can have multiple SIP media applications.</p>"}, "SipMediaApplicationAlexaSkillConfiguration": {"type": "structure", "required": ["AlexaSkillStatus", "AlexaSkillIds"], "members": {"AlexaSkillStatus": {"shape": "AlexaSkillStatus", "documentation": "<p>The status of the Alexa Skill configuration.</p>"}, "AlexaSkillIds": {"shape": "AlexaSkillIdList", "documentation": "<p>The ID of the Alexa Skill configuration.</p>"}}, "documentation": "<p>The Alexa Skill configuration of a SIP media application.</p> <important> <p>Due to changes made by the Amazon Alexa service, this data type is no longer available for use. For more information, refer to the <a href=\"https://developer.amazon.com/en-US/alexa/alexasmartproperties\">Alexa Smart Properties</a> page.</p> </important>"}, "SipMediaApplicationCall": {"type": "structure", "members": {"TransactionId": {"shape": "GuidString", "documentation": "<p>The call's transaction ID.</p>"}}, "documentation": "<p>A <code>Call</code> instance for a SIP media application.</p>"}, "SipMediaApplicationEndpoint": {"type": "structure", "members": {"LambdaArn": {"shape": "FunctionArn", "documentation": "<p>Valid Amazon Resource Name (ARN) of the Lambda function, version, or alias. The function must be created in the same AWS Region as the SIP media application.</p>"}}, "documentation": "<p>The endpoint assigned to a SIP media application.</p>"}, "SipMediaApplicationEndpointList": {"type": "list", "member": {"shape": "SipMediaApplicationEndpoint"}, "max": 1, "min": 1}, "SipMediaApplicationList": {"type": "list", "member": {"shape": "SipMediaApplication"}}, "SipMediaApplicationLoggingConfiguration": {"type": "structure", "members": {"EnableSipMediaApplicationMessageLogs": {"shape": "Boolean", "documentation": "<p>Enables message logging for the specified SIP media application.</p>"}}, "documentation": "<p>The logging configuration of a SIP media application.</p>"}, "SipMediaApplicationName": {"type": "string", "max": 256, "min": 1, "pattern": "[a-zA-Z0-9 _.-]+"}, "SipRule": {"type": "structure", "members": {"SipRuleId": {"shape": "NonEmptyString", "documentation": "<p>A SIP rule's ID.</p>"}, "Name": {"shape": "SipRuleName", "documentation": "<p>A SIP rule's name.</p>"}, "Disabled": {"shape": "Boolean", "documentation": "<p>Indicates whether the SIP rule is enabled or disabled. You must disable a rule before you can delete it.</p>"}, "TriggerType": {"shape": "SipRuleTriggerType", "documentation": "<p>The type of trigger set for a SIP rule, either a phone number or a URI request host name.</p>"}, "TriggerValue": {"shape": "NonEmptyString", "documentation": "<p>The value set for a SIP rule's trigger type. Either a phone number or a URI hostname.</p>"}, "TargetApplications": {"shape": "SipRuleTargetApplicationList", "documentation": "<p>The target SIP media application and other details, such as priority and AWS Region, to be specified in the SIP rule. Only one SIP rule per AWS Region can be provided.</p>"}, "CreatedTimestamp": {"shape": "Iso8601Timestamp", "documentation": "<p>The time at which the SIP rule was created, in ISO 8601 format.</p>"}, "UpdatedTimestamp": {"shape": "Iso8601Timestamp", "documentation": "<p>The time at which the SIP rule was updated, in ISO 8601 format.</p>"}}, "documentation": "<p>The details of a SIP rule, including name, triggers, and target applications. An AWS account can have multiple SIP rules.</p>"}, "SipRuleList": {"type": "list", "member": {"shape": "SipRule"}}, "SipRuleName": {"type": "string", "max": 256, "min": 1, "pattern": "[a-zA-Z0-9 _.-]+"}, "SipRuleTargetApplication": {"type": "structure", "members": {"SipMediaApplicationId": {"shape": "NonEmptyString", "documentation": "<p>The ID of a rule's target SIP media application.</p>"}, "Priority": {"shape": "SipApplicationPriority", "documentation": "<p>The priority setting of a rule's target SIP media application.</p>"}, "AwsRegion": {"shape": "String", "documentation": "<p>The AWS Region of a rule's target SIP media application.</p>"}}, "documentation": "<p>A target SIP media application and other details, such as priority and AWS Region, to be specified in the SIP rule. Only one SIP rule per AWS Region can be provided.</p>"}, "SipRuleTargetApplicationList": {"type": "list", "member": {"shape": "SipRuleTargetApplication"}, "max": 25, "min": 1}, "SipRuleTriggerType": {"type": "string", "enum": ["ToPhoneNumber", "RequestUriHostname"]}, "SpeakerSearchDetails": {"type": "structure", "members": {"Results": {"shape": "SpeakerSearchResultList", "documentation": "<p>The result value in the speaker search details.</p>"}, "VoiceprintGenerationStatus": {"shape": "NonEmptyString256", "documentation": "<p>The status of a voice print generation operation, <code>VoiceprintGenerationSuccess</code> or <code>VoiceprintGenerationFailure</code>..</p>"}}, "documentation": "<p>The details of a speaker search task.</p>"}, "SpeakerSearchResult": {"type": "structure", "members": {"ConfidenceScore": {"shape": "ConfidenceScore", "documentation": "<p>The confidence score in the speaker search analysis.</p>"}, "VoiceProfileId": {"shape": "NonEmptyString256", "documentation": "<p>The voice profile ID.</p>"}}, "documentation": "<p>The result of a speaker search analysis.</p>"}, "SpeakerSearchResultList": {"type": "list", "member": {"shape": "Speaker<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "SpeakerSearchTask": {"type": "structure", "members": {"SpeakerSearchTaskId": {"shape": "NonEmptyString256", "documentation": "<p>The speaker search task ID.</p>"}, "SpeakerSearchTaskStatus": {"shape": "NonEmptyString", "documentation": "<p>The status of the speaker search task, <code>IN_QUEUE</code>, <code>IN_PROGRESS</code>, <code>PARTIAL_SUCCESS</code>, <code>SUCCEEDED</code>, <code>FAILED</code>, or <code>STOPPED</code>.</p>"}, "CallDetails": {"shape": "CallDetails", "documentation": "<p>The call details of a speaker search task.</p>"}, "SpeakerSearchDetails": {"shape": "SpeakerSearchDetails", "documentation": "<p>The details of a speaker search task.</p>"}, "CreatedTimestamp": {"shape": "Iso8601Timestamp", "documentation": "<p>The time at which a speaker search task was created.</p>"}, "UpdatedTimestamp": {"shape": "Iso8601Timestamp", "documentation": "<p>The time at which a speaker search task was updated.</p>"}, "StartedTimestamp": {"shape": "Iso8601Timestamp", "documentation": "<p>The time at which the speaker search task began.</p>"}, "StatusMessage": {"shape": "String", "documentation": "<p>A detailed message about the status of a speaker search.</p>"}}, "documentation": "<p>A representation of an asynchronous request to perform speaker search analysis on a Voice Connector call.</p>"}, "StartSpeakerSearchTaskRequest": {"type": "structure", "required": ["VoiceConnectorId", "TransactionId", "VoiceProfileDomainId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString128", "documentation": "<p>The Voice Connector ID.</p>", "location": "uri", "locationName": "VoiceConnectorId"}, "TransactionId": {"shape": "NonEmptyString256", "documentation": "<p>The transaction ID of the call being analyzed.</p>"}, "VoiceProfileDomainId": {"shape": "NonEmptyString256", "documentation": "<p>The ID of the voice profile domain that will store the voice profile.</p>"}, "ClientRequestToken": {"shape": "ClientRequestId", "documentation": "<p>The unique identifier for the client request. Use a different token for different speaker search tasks.</p>"}, "CallLeg": {"shape": "CallLegType", "documentation": "<p>Specifies which call leg to stream for speaker search.</p>"}}}, "StartSpeakerSearchTaskResponse": {"type": "structure", "members": {"SpeakerSearchTask": {"shape": "SpeakerSearchTask", "documentation": "<p>The details of the speaker search task.</p>"}}}, "StartVoiceToneAnalysisTaskRequest": {"type": "structure", "required": ["VoiceConnectorId", "TransactionId", "LanguageCode"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString128", "documentation": "<p>The Voice Connector ID.</p>", "location": "uri", "locationName": "VoiceConnectorId"}, "TransactionId": {"shape": "NonEmptyString256", "documentation": "<p>The transaction ID.</p>"}, "LanguageCode": {"shape": "LanguageCode", "documentation": "<p>The language code.</p>"}, "ClientRequestToken": {"shape": "ClientRequestId", "documentation": "<p>The unique identifier for the client request. Use a different token for different voice tone analysis tasks.</p>"}}}, "StartVoiceToneAnalysisTaskResponse": {"type": "structure", "members": {"VoiceToneAnalysisTask": {"shape": "VoiceToneAnalysisTask", "documentation": "<p>The details of the voice tone analysis task.</p>"}}}, "StopSpeakerSearchTaskRequest": {"type": "structure", "required": ["VoiceConnectorId", "SpeakerSearchTaskId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString128", "documentation": "<p>The Voice Connector ID.</p>", "location": "uri", "locationName": "VoiceConnectorId"}, "SpeakerSearchTaskId": {"shape": "NonEmptyString256", "documentation": "<p>The speaker search task ID.</p>", "location": "uri", "locationName": "SpeakerSearchTaskId"}}}, "StopVoiceToneAnalysisTaskRequest": {"type": "structure", "required": ["VoiceConnectorId", "VoiceToneAnalysisTaskId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString128", "documentation": "<p>The Voice Connector ID.</p>", "location": "uri", "locationName": "VoiceConnectorId"}, "VoiceToneAnalysisTaskId": {"shape": "NonEmptyString256", "documentation": "<p>The ID of the voice tone analysis task.</p>", "location": "uri", "locationName": "VoiceToneAnalysisTaskId"}}}, "StreamingConfiguration": {"type": "structure", "required": ["DataRetentionInHours", "Disabled"], "members": {"DataRetentionInHours": {"shape": "DataRetentionInHours", "documentation": "<p>The amount of time, in hours, to the Kinesis data.</p>"}, "Disabled": {"shape": "Boolean", "documentation": "<p>When true, streaming to Kinesis is off.</p>"}, "StreamingNotificationTargets": {"shape": "StreamingNotificationTargetList", "documentation": "<p>The streaming notification targets.</p>"}, "MediaInsightsConfiguration": {"shape": "MediaInsightsConfiguration", "documentation": "<p>The call analytics configuration.</p>"}}, "documentation": "<p>The streaming configuration associated with an Amazon Chime SDK Voice Connector. Specifies whether media streaming is enabled for sending to Amazon Kinesis, and shows the retention period for the Amazon Kinesis data, in hours.</p>"}, "StreamingNotificationTarget": {"type": "structure", "members": {"NotificationTarget": {"shape": "NotificationTarget", "documentation": "<p>The streaming notification target.</p>"}}, "documentation": "<p>The target recipient for a streaming configuration notification.</p>"}, "StreamingNotificationTargetList": {"type": "list", "member": {"shape": "StreamingNotificationTarget"}, "max": 3, "min": 1}, "String": {"type": "string"}, "String128": {"type": "string", "max": 128}, "StringList": {"type": "list", "member": {"shape": "String"}}, "Tag": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {"shape": "TagKey", "documentation": "<p>The tag's key.</p>"}, "Value": {"shape": "TagValue", "documentation": "<p>The tag's value.</p>"}}, "documentation": "<p>Describes a tag applied to a resource.</p>"}, "TagKey": {"type": "string", "max": 128, "min": 1, "sensitive": true}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 50, "min": 1}, "TagList": {"type": "list", "member": {"shape": "Tag"}, "max": 50, "min": 1}, "TagResourceRequest": {"type": "structure", "required": ["ResourceARN", "Tags"], "members": {"ResourceARN": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the resource being tagged. </p>"}, "Tags": {"shape": "TagList", "documentation": "<p>A list of the tags being added to the resource.</p>"}}}, "TagValue": {"type": "string", "max": 256, "min": 0, "sensitive": true}, "Termination": {"type": "structure", "members": {"CpsLimit": {"shape": "CpsLimit", "documentation": "<p>The limit on calls per second. Max value based on account service quota. Default value of 1.</p>"}, "DefaultPhoneNumber": {"shape": "E164PhoneNumber", "documentation": "<p>The default outbound calling number.</p>"}, "CallingRegions": {"shape": "CallingRegionList", "documentation": "<p>The countries to which calls are allowed, in ISO 3166-1 alpha-2 format. Required.</p>"}, "CidrAllowedList": {"shape": "StringList", "documentation": "<p>The IP addresses allowed to make calls, in CIDR format.</p>"}, "Disabled": {"shape": "Boolean", "documentation": "<p>When termination is disabled, outbound calls cannot be made.</p>"}}, "documentation": "<p>Termination settings enable SIP hosts to make outbound calls using an Amazon Chime SDK Voice Connector.</p>"}, "TerminationHealth": {"type": "structure", "members": {"Timestamp": {"shape": "Iso8601Timestamp", "documentation": "<p>The timestamp, in ISO 8601 format.</p>"}, "Source": {"shape": "String", "documentation": "<p>The source IP address.</p>"}}, "documentation": "<p>The termination health details, including the source IP address and timestamp of the last successful <code>SIP OPTIONS</code> message from your SIP infrastructure.</p>"}, "ThrottledClientException": {"type": "structure", "members": {}, "documentation": "<p>The number of customer requests exceeds the request rate limit.</p>", "error": {"httpStatusCode": 429}, "exception": true}, "TollFreePrefix": {"type": "string", "max": 3, "min": 3, "pattern": "^8(00|33|44|55|66|77|88)$"}, "UnauthorizedClientException": {"type": "structure", "members": {}, "documentation": "<p>The client isn't authorized to request a resource.</p>", "error": {"httpStatusCode": 401}, "exception": true}, "UnprocessableEntityException": {"type": "structure", "members": {}, "documentation": "<p>A well-formed request couldn't be followed due to semantic errors.</p>", "error": {"httpStatusCode": 422}, "exception": true}, "UntagResourceRequest": {"type": "structure", "required": ["ResourceARN", "TagKeys"], "members": {"ResourceARN": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the resource having its tags removed.</p>"}, "TagKeys": {"shape": "TagKeyList", "documentation": "<p>The keys of the tags being removed from the resource.</p>"}}}, "UpdateGlobalSettingsRequest": {"type": "structure", "members": {"VoiceConnector": {"shape": "VoiceConnectorSettings", "documentation": "<p>The Voice Connector settings.</p>"}}}, "UpdatePhoneNumberRequest": {"type": "structure", "required": ["PhoneNumberId"], "members": {"PhoneNumberId": {"shape": "SensitiveNonEmptyString", "documentation": "<p>The phone number ID.</p>", "location": "uri", "locationName": "phoneNumberId"}, "ProductType": {"shape": "PhoneNumberProductType", "documentation": "<p>The product type.</p>"}, "CallingName": {"shape": "CallingName", "documentation": "<p>The outbound calling name associated with the phone number.</p>"}, "Name": {"shape": "PhoneNumberName", "documentation": "<p>Specifies the updated name assigned to one or more phone numbers.</p>"}}}, "UpdatePhoneNumberRequestItem": {"type": "structure", "required": ["PhoneNumberId"], "members": {"PhoneNumberId": {"shape": "SensitiveNonEmptyString", "documentation": "<p>The phone number ID to update.</p>"}, "ProductType": {"shape": "PhoneNumberProductType", "documentation": "<p>The product type to update.</p>"}, "CallingName": {"shape": "CallingName", "documentation": "<p>The outbound calling name to update.</p>"}, "Name": {"shape": "PhoneNumberName", "documentation": "<p>The name of the phone number.</p>"}}, "documentation": "<p>The phone number ID, product type, or calling name fields to update, used with the <a>BatchUpdatePhoneNumber</a> and <a>UpdatePhoneNumber</a> actions.</p>"}, "UpdatePhoneNumberRequestItemList": {"type": "list", "member": {"shape": "UpdatePhoneNumberRequestItem"}}, "UpdatePhoneNumberResponse": {"type": "structure", "members": {"PhoneNumber": {"shape": "PhoneNumber", "documentation": "<p>The updated phone number details.</p>"}}}, "UpdatePhoneNumberSettingsRequest": {"type": "structure", "required": ["CallingName"], "members": {"CallingName": {"shape": "CallingName", "documentation": "<p>The default outbound calling name for the account.</p>"}}}, "UpdateProxySessionRequest": {"type": "structure", "required": ["Capabilities", "VoiceConnectorId", "ProxySessionId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString128", "documentation": "<p>The Voice Connector ID.</p>", "location": "uri", "locationName": "voiceConnectorId"}, "ProxySessionId": {"shape": "NonEmptyString128", "documentation": "<p>The proxy session ID.</p>", "location": "uri", "locationName": "proxySessionId"}, "Capabilities": {"shape": "CapabilityList", "documentation": "<p>The proxy session capabilities.</p>"}, "ExpiryMinutes": {"shape": "PositiveInteger", "documentation": "<p>The number of minutes allowed for the proxy session.</p>"}}}, "UpdateProxySessionResponse": {"type": "structure", "members": {"ProxySession": {"shape": "ProxySession", "documentation": "<p>The updated proxy session details.</p>"}}}, "UpdateSipMediaApplicationCallRequest": {"type": "structure", "required": ["SipMediaApplicationId", "TransactionId", "Arguments"], "members": {"SipMediaApplicationId": {"shape": "NonEmptyString", "documentation": "<p>The ID of the SIP media application handling the call.</p>", "location": "uri", "locationName": "sipMediaApplicationId"}, "TransactionId": {"shape": "NonEmptyString", "documentation": "<p>The ID of the call transaction.</p>", "location": "uri", "locationName": "transactionId"}, "Arguments": {"shape": "SMAUpdateCallArgumentsMap", "documentation": "<p>Arguments made available to the Lambda function as part of the <code>CALL_UPDATE_REQUESTED</code> event. Can contain 0-20 key-value pairs.</p>"}}}, "UpdateSipMediaApplicationCallResponse": {"type": "structure", "members": {"SipMediaApplicationCall": {"shape": "SipMediaApplicationCall", "documentation": "<p>A <code>Call</code> instance for a SIP media application.</p>"}}}, "UpdateSipMediaApplicationRequest": {"type": "structure", "required": ["SipMediaApplicationId"], "members": {"SipMediaApplicationId": {"shape": "NonEmptyString", "documentation": "<p>The SIP media application ID.</p>", "location": "uri", "locationName": "sipMediaApplicationId"}, "Name": {"shape": "SipMediaApplicationName", "documentation": "<p>The new name for the specified SIP media application.</p>"}, "Endpoints": {"shape": "SipMediaApplicationEndpointList", "documentation": "<p>The new set of endpoints for the specified SIP media application.</p>"}}}, "UpdateSipMediaApplicationResponse": {"type": "structure", "members": {"SipMediaApplication": {"shape": "SipMediaApplication", "documentation": "<p>The updated SIP media application’s details.</p>"}}}, "UpdateSipRuleRequest": {"type": "structure", "required": ["SipRuleId", "Name"], "members": {"SipRuleId": {"shape": "NonEmptyString", "documentation": "<p>The SIP rule ID.</p>", "location": "uri", "locationName": "sipRuleId"}, "Name": {"shape": "SipRuleName", "documentation": "<p>The new name for the specified SIP rule.</p>"}, "Disabled": {"shape": "NullableBoolean", "documentation": "<p>The new value that indicates whether the rule is disabled.</p>"}, "TargetApplications": {"shape": "SipRuleTargetApplicationList", "documentation": "<p>The new list of target applications.</p>"}}}, "UpdateSipRuleResponse": {"type": "structure", "members": {"SipRule": {"shape": "SipRule", "documentation": "<p>The updated SIP rule details.</p>"}}}, "UpdateVoiceConnectorGroupRequest": {"type": "structure", "required": ["VoiceConnectorGroupId", "Name", "VoiceConnectorItems"], "members": {"VoiceConnectorGroupId": {"shape": "NonEmptyString", "documentation": "<p>The Voice Connector ID.</p>", "location": "uri", "locationName": "voiceConnectorGroupId"}, "Name": {"shape": "VoiceConnectorGroupName", "documentation": "<p>The name of the Voice Connector group.</p>"}, "VoiceConnectorItems": {"shape": "VoiceConnectorItemList", "documentation": "<p>The <code>VoiceConnectorItems</code> to associate with the Voice Connector group.</p>"}}}, "UpdateVoiceConnectorGroupResponse": {"type": "structure", "members": {"VoiceConnectorGroup": {"shape": "VoiceConnectorGroup", "documentation": "<p>The updated Voice Connector group.</p>"}}}, "UpdateVoiceConnectorRequest": {"type": "structure", "required": ["VoiceConnectorId", "Name", "RequireEncryption"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "documentation": "<p>The Voice Connector ID.</p>", "location": "uri", "locationName": "voiceConnectorId"}, "Name": {"shape": "VoiceConnectorName", "documentation": "<p>The name of the Voice Connector.</p>"}, "RequireEncryption": {"shape": "Boolean", "documentation": "<p>When enabled, requires encryption for the Voice Connector.</p>"}}}, "UpdateVoiceConnectorResponse": {"type": "structure", "members": {"VoiceConnector": {"shape": "VoiceConnector", "documentation": "<p>The updated Voice Connector details.</p>"}}}, "UpdateVoiceProfileDomainRequest": {"type": "structure", "required": ["VoiceProfileDomainId"], "members": {"VoiceProfileDomainId": {"shape": "NonEmptyString256", "documentation": "<p>The domain ID.</p>", "location": "uri", "locationName": "VoiceProfileDomainId"}, "Name": {"shape": "VoiceProfileDomainName", "documentation": "<p>The name of the voice profile domain.</p>"}, "Description": {"shape": "VoiceProfileDomainDescription", "documentation": "<p>The description of the voice profile domain.</p>"}}}, "UpdateVoiceProfileDomainResponse": {"type": "structure", "members": {"VoiceProfileDomain": {"shape": "VoiceProfileDomain", "documentation": "<p>The updated details of the voice profile domain.</p>"}}}, "UpdateVoiceProfileRequest": {"type": "structure", "required": ["VoiceProfileId", "SpeakerSearchTaskId"], "members": {"VoiceProfileId": {"shape": "NonEmptyString256", "documentation": "<p>The profile ID.</p>", "location": "uri", "locationName": "VoiceProfileId"}, "SpeakerSearchTaskId": {"shape": "NonEmptyString256", "documentation": "<p>The ID of the speaker search task.</p>"}}}, "UpdateVoiceProfileResponse": {"type": "structure", "members": {"VoiceProfile": {"shape": "VoiceProfile", "documentation": "<p>The updated voice profile settings.</p>"}}}, "ValidateE911AddressRequest": {"type": "structure", "required": ["AwsAccountId", "StreetNumber", "StreetInfo", "City", "State", "Country", "PostalCode"], "members": {"AwsAccountId": {"shape": "NonEmptyString", "documentation": "<p>The AWS account ID.</p>"}, "StreetNumber": {"shape": "SensitiveNonEmptyString", "documentation": "<p>The address street number, such as <code>200</code> or <code>2121</code>.</p>"}, "StreetInfo": {"shape": "SensitiveNonEmptyString", "documentation": "<p>The address street information, such as <code>8th Avenue</code>.</p>"}, "City": {"shape": "SensitiveNonEmptyString", "documentation": "<p>The address city, such as <code>Portland</code>.</p>"}, "State": {"shape": "SensitiveNonEmptyString", "documentation": "<p>The address state, such as <code>ME</code>.</p>"}, "Country": {"shape": "SensitiveNonEmptyString", "documentation": "<p>The country in the address being validated as two-letter country code in ISO 3166-1 alpha-2 format, such as <code>US</code>. For more information, see <a href=\"https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2\">ISO 3166-1 alpha-2</a> in Wikipedia.</p>"}, "PostalCode": {"shape": "SensitiveNonEmptyString", "documentation": "<p>The dress postal code, such <code>04352</code>.</p>"}}}, "ValidateE911AddressResponse": {"type": "structure", "members": {"ValidationResult": {"shape": "ValidationResult", "documentation": "<p>Number indicating the result of address validation.</p> <p>Each possible result is defined as follows:</p> <ul> <li> <p> <code>0</code> - Address validation succeeded.</p> </li> <li> <p> <code>1</code> - Address validation succeeded. The address was a close enough match and has been corrected as part of the address object.</p> </li> <li> <p> <code>2</code> - Address validation failed. You should re-submit the validation request with candidates from the <code>CandidateAddressList</code> result, if it's a close match.</p> </li> </ul>"}, "AddressExternalId": {"shape": "String", "documentation": "<p>The ID that represents the address.</p>"}, "Address": {"shape": "Address", "documentation": "<p>The validated address.</p>"}, "CandidateAddressList": {"shape": "CandidateAddressList", "documentation": "<p>The list of address suggestions..</p>"}}}, "ValidationResult": {"type": "integer", "max": 2, "min": 0}, "VoiceConnector": {"type": "structure", "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "documentation": "<p>The Voice Connector's ID.</p>"}, "AwsRegion": {"shape": "VoiceConnectorAwsRegion", "documentation": "<p>The AWS Region in which the Voice Connector is created. Default: us-east-1.</p>"}, "Name": {"shape": "VoiceConnectorName", "documentation": "<p>The Voice Connector's name.</p>"}, "OutboundHostName": {"shape": "String", "documentation": "<p>The outbound host name for the Voice Connector.</p>"}, "RequireEncryption": {"shape": "Boolean", "documentation": "<p>Enables or disables encryption for the Voice Connector.</p>"}, "CreatedTimestamp": {"shape": "Iso8601Timestamp", "documentation": "<p>The Voice Connector's creation timestamp, in ISO 8601 format.</p>"}, "UpdatedTimestamp": {"shape": "Iso8601Timestamp", "documentation": "<p>The Voice Connector's updated timestamp, in ISO 8601 format.</p>"}, "VoiceConnectorArn": {"shape": "NonEmptyString", "documentation": "<p>The ARN of the Voice Connector.</p>"}, "IntegrationType": {"shape": "VoiceConnectorIntegrationType", "documentation": "<p>The connectors for use with Amazon Connect.</p>"}}, "documentation": "<p>The Amazon Chime SDK Voice Connector configuration, including outbound host name and encryption settings.</p>"}, "VoiceConnectorAwsRegion": {"type": "string", "enum": ["us-east-1", "us-west-2", "ca-central-1", "eu-central-1", "eu-west-1", "eu-west-2", "ap-northeast-2", "ap-northeast-1", "ap-southeast-1", "ap-southeast-2"]}, "VoiceConnectorAwsRegionList": {"type": "list", "member": {"shape": "VoiceConnectorAwsRegion"}}, "VoiceConnectorGroup": {"type": "structure", "members": {"VoiceConnectorGroupId": {"shape": "NonEmptyString", "documentation": "<p>The ID of a Voice Connector group.</p>"}, "Name": {"shape": "VoiceConnectorGroupName", "documentation": "<p>The name of a Voice Connector group.</p>"}, "VoiceConnectorItems": {"shape": "VoiceConnectorItemList", "documentation": "<p>The Voice Connectors to which you route inbound calls.</p>"}, "CreatedTimestamp": {"shape": "Iso8601Timestamp", "documentation": "<p>The Voice Connector group's creation time stamp, in ISO 8601 format.</p>"}, "UpdatedTimestamp": {"shape": "Iso8601Timestamp", "documentation": "<p>The Voice Connector group's creation time stamp, in ISO 8601 format.</p>"}, "VoiceConnectorGroupArn": {"shape": "NonEmptyString", "documentation": "<p>The ARN of the Voice Connector group.</p>"}}, "documentation": "<p>The Amazon Chime SDK Voice Connector group configuration, including associated Voice Connectors. You can include Voice Connectors from different AWS Regions in a group. This creates a fault tolerant mechanism for fallback in case of availability events.</p>"}, "VoiceConnectorGroupList": {"type": "list", "member": {"shape": "VoiceConnectorGroup"}}, "VoiceConnectorGroupName": {"type": "string", "max": 256, "min": 1, "pattern": "[a-zA-Z0-9 _.-]+"}, "VoiceConnectorIntegrationType": {"type": "string", "enum": ["CONNECT_CALL_TRANSFER_CONNECTOR", "CONNECT_ANALYTICS_CONNECTOR"]}, "VoiceConnectorItem": {"type": "structure", "required": ["VoiceConnectorId", "Priority"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "documentation": "<p>The Voice Connector ID.</p>"}, "Priority": {"shape": "VoiceConnectorItemPriority", "documentation": "<p>The priority setting of a Voice Connector item. Calls are routed to hosts in priority order, with 1 as the highest priority. When hosts have equal priority, the system distributes calls among them based on their relative weight.</p>"}}, "documentation": "<p>For Amazon Chime SDK Voice Connector groups, the Amazon Chime SDK Voice Connectors to which you route inbound calls. Includes priority configuration settings. Limit: 3 VoiceConnectorItems per Voice Connector group.</p>"}, "VoiceConnectorItemList": {"type": "list", "member": {"shape": "VoiceConnectorItem"}}, "VoiceConnectorItemPriority": {"type": "integer", "max": 99, "min": 1}, "VoiceConnectorList": {"type": "list", "member": {"shape": "VoiceConnector"}}, "VoiceConnectorName": {"type": "string", "max": 256, "min": 1, "pattern": "[a-zA-Z0-9 _.-]+"}, "VoiceConnectorSettings": {"type": "structure", "members": {"CdrBucket": {"shape": "String", "documentation": "<p>The S3 bucket that stores the Voice Connector's call detail records.</p>"}}, "documentation": "<p>The Amazon Chime SDK Voice Connector settings. Includes any Amazon S3 buckets designated for storing call detail records.</p>"}, "VoiceProfile": {"type": "structure", "members": {"VoiceProfileId": {"shape": "NonEmptyString256", "documentation": "<p>The ID of the voice profile.</p>"}, "VoiceProfileArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the voice profile.</p>"}, "VoiceProfileDomainId": {"shape": "NonEmptyString256", "documentation": "<p>The ID of the domain that contains the voice profile.</p>"}, "CreatedTimestamp": {"shape": "Iso8601Timestamp", "documentation": "<p>The time at which the voice profile was created and enrolled.</p>"}, "UpdatedTimestamp": {"shape": "Iso8601Timestamp", "documentation": "<p>The time at which the voice profile was last updated.</p>"}, "ExpirationTimestamp": {"shape": "Iso8601Timestamp", "documentation": "<p>The time at which a voice profile expires unless you re-enroll the caller via the <code>UpdateVoiceProfile</code> API.</p>"}}, "documentation": "<p>The combination of a voice print and caller ID.</p>"}, "VoiceProfileDomain": {"type": "structure", "members": {"VoiceProfileDomainId": {"shape": "NonEmptyString256", "documentation": "<p>The ID of the voice profile domain.</p>"}, "VoiceProfileDomainArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The voice profile domain's Amazon Resource Number (ARN).</p>"}, "Name": {"shape": "VoiceProfileDomainName", "documentation": "<p>The name of the voice profile domain.</p>"}, "Description": {"shape": "VoiceProfileDomainDescription", "documentation": "<p>The description of the voice profile domain.</p>"}, "ServerSideEncryptionConfiguration": {"shape": "ServerSideEncryptionConfiguration", "documentation": "<p>A structure that contains the configuration settings for server-side encryption.</p>"}, "CreatedTimestamp": {"shape": "Iso8601Timestamp", "documentation": "<p>The time at which the voice profile domain was created.</p>"}, "UpdatedTimestamp": {"shape": "Iso8601Timestamp", "documentation": "<p>The time at which the voice profile was last updated.</p>"}}, "documentation": "<p>A collection of voice profiles.</p>"}, "VoiceProfileDomainDescription": {"type": "string", "max": 1024, "min": 0}, "VoiceProfileDomainName": {"type": "string", "max": 256, "min": 1, "pattern": "[a-zA-Z0-9 _.-]+"}, "VoiceProfileDomainSummary": {"type": "structure", "members": {"VoiceProfileDomainId": {"shape": "NonEmptyString256", "documentation": "<p>The ID of the voice profile domain summary.</p>"}, "VoiceProfileDomainArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of a voice profile in a voice profile domain summary.</p>"}, "Name": {"shape": "VoiceProfileDomainName", "documentation": "<p>The name of the voice profile domain summary.</p>"}, "Description": {"shape": "VoiceProfileDomainDescription", "documentation": "<p>Describes the voice profile domain summary.</p>"}, "CreatedTimestamp": {"shape": "Iso8601Timestamp", "documentation": "<p>The time at which the voice profile domain summary was created.</p>"}, "UpdatedTimestamp": {"shape": "Iso8601Timestamp", "documentation": "<p>The time at which the voice profile domain summary was last updated.</p>"}}, "documentation": "<p>A high-level overview of a voice profile domain.</p>"}, "VoiceProfileDomainSummaryList": {"type": "list", "member": {"shape": "VoiceProfileDomainSummary"}}, "VoiceProfileSummary": {"type": "structure", "members": {"VoiceProfileId": {"shape": "NonEmptyString256", "documentation": "<p>The ID of the voice profile in a voice profile summary.</p>"}, "VoiceProfileArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the voice profile in a voice profile summary.</p>"}, "VoiceProfileDomainId": {"shape": "NonEmptyString256", "documentation": "<p>The ID of the voice profile domain in a voice profile summary.</p>"}, "CreatedTimestamp": {"shape": "Iso8601Timestamp", "documentation": "<p>The time at which a voice profile summary was created.</p>"}, "UpdatedTimestamp": {"shape": "Iso8601Timestamp", "documentation": "<p>The time at which a voice profile summary was last updated.</p>"}, "ExpirationTimestamp": {"shape": "Iso8601Timestamp", "documentation": "<p>Extends the life of the voice profile. You can use <code>UpdateVoiceProfile</code> to refresh an existing voice profile's voice print and extend the life of the summary.</p>"}}, "documentation": "<p>A high-level summary of a voice profile.</p>"}, "VoiceProfileSummaryList": {"type": "list", "member": {"shape": "VoiceProfileSummary"}}, "VoiceToneAnalysisTask": {"type": "structure", "members": {"VoiceToneAnalysisTaskId": {"shape": "NonEmptyString256", "documentation": "<p>The ID of the voice tone analysis task.</p>"}, "VoiceToneAnalysisTaskStatus": {"shape": "NonEmptyString", "documentation": "<p>The status of a voice tone analysis task, <code>IN_QUEUE</code>, <code>IN_PROGRESS</code>, <code>PARTIAL_SUCCESS</code>, <code>SUCCEEDED</code>, <code>FAILED</code>, or <code>STOPPED</code>.</p>"}, "CallDetails": {"shape": "CallDetails", "documentation": "<p>The call details of a voice tone analysis task.</p>"}, "CreatedTimestamp": {"shape": "Iso8601Timestamp", "documentation": "<p>The time at which a voice tone analysis task was created.</p>"}, "UpdatedTimestamp": {"shape": "Iso8601Timestamp", "documentation": "<p>The time at which a voice tone analysis task was updated.</p>"}, "StartedTimestamp": {"shape": "Iso8601Timestamp", "documentation": "<p>The time at which a voice tone analysis task started.</p>"}, "StatusMessage": {"shape": "String", "documentation": "<p>The status of a voice tone analysis task.</p>"}}, "documentation": "<p>A representation of an asynchronous request to perform voice tone analysis on a Voice Connector call.</p>"}}, "documentation": "<p>The Amazon Chime SDK telephony APIs in this section enable developers to create PSTN calling solutions that use Amazon Chime SDK Voice Connectors, and Amazon Chime SDK SIP media applications. Developers can also order and manage phone numbers, create and manage Voice Connectors and SIP media applications, and run voice analytics.</p>"}