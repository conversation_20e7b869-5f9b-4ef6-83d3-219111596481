{"version": "1.0", "resources": {"ClusterDbRevision": {"operation": "DescribeClusterDbRevisions", "resourceIdentifier": {"RevisionTargets": "ClusterDbRevisions[].RevisionTargets"}}, "ClusterParameterGroup": {"operation": "DescribeClusterParameterGroups", "resourceIdentifier": {"ParameterGroupName": "ParameterGroups[].ParameterGroupName", "ParameterGroupFamily": "ParameterGroups[].ParameterGroupFamily"}}, "ClusterSecurityGroup": {"operation": "DescribeClusterSecurityGroups", "resourceIdentifier": {"ClusterSecurityGroupName": "ClusterSecurityGroups[].ClusterSecurityGroupName"}}, "ClusterSnapshot": {"operation": "DescribeClusterSnapshots", "resourceIdentifier": {"Port": "Snapshots[].Port", "SnapshotType": "Snapshots[].SnapshotType", "AccountsWithRestoreAccess": "Snapshots[].AccountsWithRestoreAccess", "OwnerAccount": "Snapshots[].OwnerAccount"}}, "ClusterVersion": {"operation": "DescribeClusterVersions", "resourceIdentifier": {"ClusterParameterGroupFamily": "ClusterVersions[].ClusterParameterGroupFamily"}}, "Cluster": {"operation": "DescribeClusters", "resourceIdentifier": {"AutomatedSnapshotRetentionPeriod": "Clusters[].AutomatedSnapshotRetentionPeriod", "ClusterSecurityGroups": "Clusters[].ClusterSecurityGroups", "VpcSecurityGroups": "Clusters[].VpcSecurityGroups", "ClusterSubnetGroupName": "Clusters[].ClusterSubnetGroupName", "AvailabilityZone": "Clusters[].AvailabilityZone", "PreferredMaintenanceWindow": "Clusters[].PreferredMaintenanceWindow", "AllowVersionUpgrade": "Clusters[].AllowVersionUpgrade", "NumberOfNodes": "Clusters[].NumberOfNodes", "PubliclyAccessible": "Clusters[].PubliclyAccessible", "Encrypted": "Clusters[].Encrypted", "EnhancedVpcRouting": "Clusters[].EnhancedVpcRouting", "IamRoles": "Clusters[].IamRoles", "MaintenanceTrackName": "Clusters[].MaintenanceTrackName"}}, "EventSubscription": {"operation": "DescribeEventSubscriptions", "resourceIdentifier": {"SnsTopicArn": "EventSubscriptionsList[].SnsTopicArn", "Enabled": "EventSubscriptionsList[].Enabled"}}, "Event": {"operation": "DescribeEvents", "resourceIdentifier": {"SourceIdentifier": "Events[].SourceIdentifier", "SourceType": "Events[].SourceType", "EventCategories": "Events[].EventCategories", "Severity": "Events[].Severity"}}, "HsmClientCertificate": {"operation": "DescribeHsmClientCertificates", "resourceIdentifier": {"HsmClientCertificateIdentifier": "HsmClientCertificates[].HsmClientCertificateIdentifier"}}, "HsmConfiguration": {"operation": "DescribeHsmConfigurations", "resourceIdentifier": {"HsmConfigurationIdentifier": "HsmConfigurations[].HsmConfigurationIdentifier", "Description": "HsmConfigurations[].Description"}}, "OrderableClusterOption": {"operation": "DescribeOrderableClusterOptions", "resourceIdentifier": {"ClusterVersion": "OrderableClusterOptions[].ClusterVersion", "ClusterType": "OrderableClusterOptions[].ClusterType"}}, "ReservedNode": {"operation": "DescribeReservedNodes", "resourceIdentifier": {"ReservedNodeId": "ReservedNodes[].ReservedNodeId", "ReservedNodeOfferingId": "ReservedNodes[].ReservedNodeOfferingId", "NodeType": "ReservedNodes[].NodeType", "StartTime": "ReservedNodes[].StartTime", "Duration": "ReservedNodes[].Duration", "NodeCount": "ReservedNodes[].NodeCount"}}, "SnapshotCopyGrant": {"operation": "DescribeSnapshotCopyGrants", "resourceIdentifier": {"SnapshotCopyGrantName": "SnapshotCopyGrants[].SnapshotCopyGrantName", "KmsKeyId": "SnapshotCopyGrants[].KmsKeyId"}}, "TableRestoreStatu": {"operation": "DescribeTableRestoreStatus", "resourceIdentifier": {"TableRestoreRequestId": "TableRestoreStatusDetails[].TableRestoreRequestId", "ClusterIdentifier": "TableRestoreStatusDetails[].ClusterIdentifier", "SnapshotIdentifier": "TableRestoreStatusDetails[].SnapshotIdentifier", "SourceDatabaseName": "TableRestoreStatusDetails[].SourceDatabaseName", "SourceSchemaName": "TableRestoreStatusDetails[].SourceSchemaName", "SourceTableName": "TableRestoreStatusDetails[].SourceTableName", "TargetDatabaseName": "TableRestoreStatusDetails[].TargetDatabaseName", "TargetSchemaName": "TableRestoreStatusDetails[].TargetSchemaName", "NewTableName": "TableRestoreStatusDetails[].NewTableName"}}, "Tag": {"operation": "DescribeTags", "resourceIdentifier": {"ResourceName": "TaggedResources[].ResourceName", "ResourceType": "TaggedResources[].ResourceType"}}}, "operations": {"AcceptReservedNodeExchange": {"ReservedNodeId": {"completions": [{"parameters": {}, "resourceName": "ReservedNode", "resourceIdentifier": "ReservedNodeId"}]}}, "AuthorizeClusterSecurityGroupIngress": {"ClusterSecurityGroupName": {"completions": [{"parameters": {}, "resourceName": "ClusterSecurityGroup", "resourceIdentifier": "ClusterSecurityGroupName"}]}}, "AuthorizeSnapshotAccess": {"SnapshotIdentifier": {"completions": [{"parameters": {}, "resourceName": "TableRestoreStatu", "resourceIdentifier": "SnapshotIdentifier"}]}, "AccountWithRestoreAccess": {"completions": [{"parameters": {}, "resourceName": "ClusterSnapshot", "resourceIdentifier": "AccountsWithRestoreAccess"}]}}, "DeleteCluster": {"ClusterIdentifier": {"completions": [{"parameters": {}, "resourceName": "TableRestoreStatu", "resourceIdentifier": "ClusterIdentifier"}]}}, "DeleteClusterParameterGroup": {"ParameterGroupName": {"completions": [{"parameters": {}, "resourceName": "ClusterParameterGroup", "resourceIdentifier": "ParameterGroupName"}]}}, "DeleteClusterSecurityGroup": {"ClusterSecurityGroupName": {"completions": [{"parameters": {}, "resourceName": "ClusterSecurityGroup", "resourceIdentifier": "ClusterSecurityGroupName"}]}}, "DeleteClusterSnapshot": {"SnapshotIdentifier": {"completions": [{"parameters": {}, "resourceName": "TableRestoreStatu", "resourceIdentifier": "SnapshotIdentifier"}]}}, "DeleteClusterSubnetGroup": {"ClusterSubnetGroupName": {"completions": [{"parameters": {}, "resourceName": "Cluster", "resourceIdentifier": "ClusterSubnetGroupName"}]}}, "DeleteHsmClientCertificate": {"HsmClientCertificateIdentifier": {"completions": [{"parameters": {}, "resourceName": "HsmClientCertificate", "resourceIdentifier": "HsmClientCertificateIdentifier"}]}}, "DeleteHsmConfiguration": {"HsmConfigurationIdentifier": {"completions": [{"parameters": {}, "resourceName": "HsmConfiguration", "resourceIdentifier": "HsmConfigurationIdentifier"}]}}, "DeleteSnapshotCopyGrant": {"SnapshotCopyGrantName": {"completions": [{"parameters": {}, "resourceName": "SnapshotCopyGrant", "resourceIdentifier": "SnapshotCopyGrantName"}]}}, "DeleteTags": {"ResourceName": {"completions": [{"parameters": {}, "resourceName": "Tag", "resourceIdentifier": "ResourceName"}]}}, "DescribeClusterDbRevisions": {"ClusterIdentifier": {"completions": [{"parameters": {}, "resourceName": "TableRestoreStatu", "resourceIdentifier": "ClusterIdentifier"}]}}, "DescribeClusterParameterGroups": {"ParameterGroupName": {"completions": [{"parameters": {}, "resourceName": "ClusterParameterGroup", "resourceIdentifier": "ParameterGroupName"}]}}, "DescribeClusterParameters": {"ParameterGroupName": {"completions": [{"parameters": {}, "resourceName": "ClusterParameterGroup", "resourceIdentifier": "ParameterGroupName"}]}}, "DescribeClusterSecurityGroups": {"ClusterSecurityGroupName": {"completions": [{"parameters": {}, "resourceName": "ClusterSecurityGroup", "resourceIdentifier": "ClusterSecurityGroupName"}]}}, "DescribeClusterSnapshots": {"ClusterIdentifier": {"completions": [{"parameters": {}, "resourceName": "TableRestoreStatu", "resourceIdentifier": "ClusterIdentifier"}]}, "SnapshotIdentifier": {"completions": [{"parameters": {}, "resourceName": "TableRestoreStatu", "resourceIdentifier": "SnapshotIdentifier"}]}, "SnapshotType": {"completions": [{"parameters": {}, "resourceName": "ClusterSnapshot", "resourceIdentifier": "SnapshotType"}]}, "StartTime": {"completions": [{"parameters": {}, "resourceName": "ReservedNode", "resourceIdentifier": "StartTime"}]}, "OwnerAccount": {"completions": [{"parameters": {}, "resourceName": "ClusterSnapshot", "resourceIdentifier": "OwnerAccount"}]}}, "DescribeClusterSubnetGroups": {"ClusterSubnetGroupName": {"completions": [{"parameters": {}, "resourceName": "Cluster", "resourceIdentifier": "ClusterSubnetGroupName"}]}}, "DescribeClusterTracks": {"MaintenanceTrackName": {"completions": [{"parameters": {}, "resourceName": "Cluster", "resourceIdentifier": "MaintenanceTrackName"}]}}, "DescribeClusterVersions": {"ClusterVersion": {"completions": [{"parameters": {}, "resourceName": "OrderableClusterOption", "resourceIdentifier": "ClusterVersion"}]}, "ClusterParameterGroupFamily": {"completions": [{"parameters": {}, "resourceName": "ClusterVersion", "resourceIdentifier": "ClusterParameterGroupFamily"}]}}, "DescribeClusters": {"ClusterIdentifier": {"completions": [{"parameters": {}, "resourceName": "TableRestoreStatu", "resourceIdentifier": "ClusterIdentifier"}]}}, "DescribeDefaultClusterParameters": {"ParameterGroupFamily": {"completions": [{"parameters": {}, "resourceName": "ClusterParameterGroup", "resourceIdentifier": "ParameterGroupFamily"}]}}, "DescribeEventCategories": {"SourceType": {"completions": [{"parameters": {}, "resourceName": "Event", "resourceIdentifier": "SourceType"}]}}, "DescribeEvents": {"SourceIdentifier": {"completions": [{"parameters": {}, "resourceName": "Event", "resourceIdentifier": "SourceIdentifier"}]}, "SourceType": {"completions": [{"parameters": {}, "resourceName": "Event", "resourceIdentifier": "SourceType"}]}, "StartTime": {"completions": [{"parameters": {}, "resourceName": "ReservedNode", "resourceIdentifier": "StartTime"}]}, "Duration": {"completions": [{"parameters": {}, "resourceName": "ReservedNode", "resourceIdentifier": "Duration"}]}}, "DescribeHsmClientCertificates": {"HsmClientCertificateIdentifier": {"completions": [{"parameters": {}, "resourceName": "HsmClientCertificate", "resourceIdentifier": "HsmClientCertificateIdentifier"}]}}, "DescribeHsmConfigurations": {"HsmConfigurationIdentifier": {"completions": [{"parameters": {}, "resourceName": "HsmConfiguration", "resourceIdentifier": "HsmConfigurationIdentifier"}]}}, "DescribeLoggingStatus": {"ClusterIdentifier": {"completions": [{"parameters": {}, "resourceName": "TableRestoreStatu", "resourceIdentifier": "ClusterIdentifier"}]}}, "DescribeOrderableClusterOptions": {"ClusterVersion": {"completions": [{"parameters": {}, "resourceName": "OrderableClusterOption", "resourceIdentifier": "ClusterVersion"}]}, "NodeType": {"completions": [{"parameters": {}, "resourceName": "ReservedNode", "resourceIdentifier": "NodeType"}]}}, "DescribeReservedNodeOfferings": {"ReservedNodeOfferingId": {"completions": [{"parameters": {}, "resourceName": "ReservedNode", "resourceIdentifier": "ReservedNodeOfferingId"}]}}, "DescribeReservedNodes": {"ReservedNodeId": {"completions": [{"parameters": {}, "resourceName": "ReservedNode", "resourceIdentifier": "ReservedNodeId"}]}}, "DescribeResize": {"ClusterIdentifier": {"completions": [{"parameters": {}, "resourceName": "TableRestoreStatu", "resourceIdentifier": "ClusterIdentifier"}]}}, "DescribeSnapshotCopyGrants": {"SnapshotCopyGrantName": {"completions": [{"parameters": {}, "resourceName": "SnapshotCopyGrant", "resourceIdentifier": "SnapshotCopyGrantName"}]}}, "DescribeTableRestoreStatus": {"ClusterIdentifier": {"completions": [{"parameters": {}, "resourceName": "TableRestoreStatu", "resourceIdentifier": "ClusterIdentifier"}]}, "TableRestoreRequestId": {"completions": [{"parameters": {}, "resourceName": "TableRestoreStatu", "resourceIdentifier": "TableRestoreRequestId"}]}}, "DescribeTags": {"ResourceName": {"completions": [{"parameters": {}, "resourceName": "Tag", "resourceIdentifier": "ResourceName"}]}, "ResourceType": {"completions": [{"parameters": {}, "resourceName": "Tag", "resourceIdentifier": "ResourceType"}]}}, "DisableLogging": {"ClusterIdentifier": {"completions": [{"parameters": {}, "resourceName": "TableRestoreStatu", "resourceIdentifier": "ClusterIdentifier"}]}}, "DisableSnapshotCopy": {"ClusterIdentifier": {"completions": [{"parameters": {}, "resourceName": "TableRestoreStatu", "resourceIdentifier": "ClusterIdentifier"}]}}, "EnableLogging": {"ClusterIdentifier": {"completions": [{"parameters": {}, "resourceName": "TableRestoreStatu", "resourceIdentifier": "ClusterIdentifier"}]}}, "EnableSnapshotCopy": {"ClusterIdentifier": {"completions": [{"parameters": {}, "resourceName": "TableRestoreStatu", "resourceIdentifier": "ClusterIdentifier"}]}, "SnapshotCopyGrantName": {"completions": [{"parameters": {}, "resourceName": "SnapshotCopyGrant", "resourceIdentifier": "SnapshotCopyGrantName"}]}}, "GetClusterCredentials": {"ClusterIdentifier": {"completions": [{"parameters": {}, "resourceName": "TableRestoreStatu", "resourceIdentifier": "ClusterIdentifier"}]}}, "GetReservedNodeExchangeOfferings": {"ReservedNodeId": {"completions": [{"parameters": {}, "resourceName": "ReservedNode", "resourceIdentifier": "ReservedNodeId"}]}}, "ModifyCluster": {"ClusterIdentifier": {"completions": [{"parameters": {}, "resourceName": "TableRestoreStatu", "resourceIdentifier": "ClusterIdentifier"}]}, "ClusterType": {"completions": [{"parameters": {}, "resourceName": "OrderableClusterOption", "resourceIdentifier": "ClusterType"}]}, "NodeType": {"completions": [{"parameters": {}, "resourceName": "ReservedNode", "resourceIdentifier": "NodeType"}]}, "NumberOfNodes": {"completions": [{"parameters": {}, "resourceName": "Cluster", "resourceIdentifier": "NumberOfNodes"}]}, "ClusterSecurityGroups": {"completions": [{"parameters": {}, "resourceName": "Cluster", "resourceIdentifier": "ClusterSecurityGroups"}]}, "VpcSecurityGroupIds": {"completions": [{"parameters": {}, "resourceName": "Cluster", "resourceIdentifier": "VpcSecurityGroups"}]}, "AutomatedSnapshotRetentionPeriod": {"completions": [{"parameters": {}, "resourceName": "Cluster", "resourceIdentifier": "AutomatedSnapshotRetentionPeriod"}]}, "PreferredMaintenanceWindow": {"completions": [{"parameters": {}, "resourceName": "Cluster", "resourceIdentifier": "PreferredMaintenanceWindow"}]}, "ClusterVersion": {"completions": [{"parameters": {}, "resourceName": "OrderableClusterOption", "resourceIdentifier": "ClusterVersion"}]}, "AllowVersionUpgrade": {"completions": [{"parameters": {}, "resourceName": "Cluster", "resourceIdentifier": "AllowVersionUpgrade"}]}, "HsmClientCertificateIdentifier": {"completions": [{"parameters": {}, "resourceName": "HsmClientCertificate", "resourceIdentifier": "HsmClientCertificateIdentifier"}]}, "HsmConfigurationIdentifier": {"completions": [{"parameters": {}, "resourceName": "HsmConfiguration", "resourceIdentifier": "HsmConfigurationIdentifier"}]}, "NewClusterIdentifier": {"completions": [{"parameters": {}, "resourceName": "TableRestoreStatu", "resourceIdentifier": "ClusterIdentifier"}]}, "PubliclyAccessible": {"completions": [{"parameters": {}, "resourceName": "Cluster", "resourceIdentifier": "PubliclyAccessible"}]}, "EnhancedVpcRouting": {"completions": [{"parameters": {}, "resourceName": "Cluster", "resourceIdentifier": "EnhancedVpcRouting"}]}, "MaintenanceTrackName": {"completions": [{"parameters": {}, "resourceName": "Cluster", "resourceIdentifier": "MaintenanceTrackName"}]}, "Encrypted": {"completions": [{"parameters": {}, "resourceName": "Cluster", "resourceIdentifier": "Encrypted"}]}, "KmsKeyId": {"completions": [{"parameters": {}, "resourceName": "SnapshotCopyGrant", "resourceIdentifier": "KmsKeyId"}]}}, "ModifyClusterDbRevision": {"ClusterIdentifier": {"completions": [{"parameters": {}, "resourceName": "TableRestoreStatu", "resourceIdentifier": "ClusterIdentifier"}]}, "RevisionTarget": {"completions": [{"parameters": {}, "resourceName": "ClusterDbRevision", "resourceIdentifier": "RevisionTargets"}]}}, "ModifyClusterIamRoles": {"ClusterIdentifier": {"completions": [{"parameters": {}, "resourceName": "TableRestoreStatu", "resourceIdentifier": "ClusterIdentifier"}]}}, "ModifyClusterParameterGroup": {"ParameterGroupName": {"completions": [{"parameters": {}, "resourceName": "ClusterParameterGroup", "resourceIdentifier": "ParameterGroupName"}]}}, "ModifyClusterSubnetGroup": {"ClusterSubnetGroupName": {"completions": [{"parameters": {}, "resourceName": "Cluster", "resourceIdentifier": "ClusterSubnetGroupName"}]}, "Description": {"completions": [{"parameters": {}, "resourceName": "HsmConfiguration", "resourceIdentifier": "Description"}]}}, "ModifyEventSubscription": {"SnsTopicArn": {"completions": [{"parameters": {}, "resourceName": "EventSubscription", "resourceIdentifier": "SnsTopicArn"}]}, "SourceType": {"completions": [{"parameters": {}, "resourceName": "Event", "resourceIdentifier": "SourceType"}]}, "EventCategories": {"completions": [{"parameters": {}, "resourceName": "Event", "resourceIdentifier": "EventCategories"}]}, "Severity": {"completions": [{"parameters": {}, "resourceName": "Event", "resourceIdentifier": "Severity"}]}, "Enabled": {"completions": [{"parameters": {}, "resourceName": "EventSubscription", "resourceIdentifier": "Enabled"}]}}, "ModifySnapshotCopyRetentionPeriod": {"ClusterIdentifier": {"completions": [{"parameters": {}, "resourceName": "TableRestoreStatu", "resourceIdentifier": "ClusterIdentifier"}]}}, "PurchaseReservedNodeOffering": {"ReservedNodeOfferingId": {"completions": [{"parameters": {}, "resourceName": "ReservedNode", "resourceIdentifier": "ReservedNodeOfferingId"}]}, "NodeCount": {"completions": [{"parameters": {}, "resourceName": "ReservedNode", "resourceIdentifier": "NodeCount"}]}}, "RebootCluster": {"ClusterIdentifier": {"completions": [{"parameters": {}, "resourceName": "TableRestoreStatu", "resourceIdentifier": "ClusterIdentifier"}]}}, "ResetClusterParameterGroup": {"ParameterGroupName": {"completions": [{"parameters": {}, "resourceName": "ClusterParameterGroup", "resourceIdentifier": "ParameterGroupName"}]}}, "ResizeCluster": {"ClusterIdentifier": {"completions": [{"parameters": {}, "resourceName": "TableRestoreStatu", "resourceIdentifier": "ClusterIdentifier"}]}, "ClusterType": {"completions": [{"parameters": {}, "resourceName": "OrderableClusterOption", "resourceIdentifier": "ClusterType"}]}, "NodeType": {"completions": [{"parameters": {}, "resourceName": "ReservedNode", "resourceIdentifier": "NodeType"}]}, "NumberOfNodes": {"completions": [{"parameters": {}, "resourceName": "Cluster", "resourceIdentifier": "NumberOfNodes"}]}}, "RestoreFromClusterSnapshot": {"ClusterIdentifier": {"completions": [{"parameters": {}, "resourceName": "TableRestoreStatu", "resourceIdentifier": "ClusterIdentifier"}]}, "SnapshotIdentifier": {"completions": [{"parameters": {}, "resourceName": "TableRestoreStatu", "resourceIdentifier": "SnapshotIdentifier"}]}, "Port": {"completions": [{"parameters": {}, "resourceName": "ClusterSnapshot", "resourceIdentifier": "Port"}]}, "AvailabilityZone": {"completions": [{"parameters": {}, "resourceName": "Cluster", "resourceIdentifier": "AvailabilityZone"}]}, "AllowVersionUpgrade": {"completions": [{"parameters": {}, "resourceName": "Cluster", "resourceIdentifier": "AllowVersionUpgrade"}]}, "ClusterSubnetGroupName": {"completions": [{"parameters": {}, "resourceName": "Cluster", "resourceIdentifier": "ClusterSubnetGroupName"}]}, "PubliclyAccessible": {"completions": [{"parameters": {}, "resourceName": "Cluster", "resourceIdentifier": "PubliclyAccessible"}]}, "OwnerAccount": {"completions": [{"parameters": {}, "resourceName": "ClusterSnapshot", "resourceIdentifier": "OwnerAccount"}]}, "HsmClientCertificateIdentifier": {"completions": [{"parameters": {}, "resourceName": "HsmClientCertificate", "resourceIdentifier": "HsmClientCertificateIdentifier"}]}, "HsmConfigurationIdentifier": {"completions": [{"parameters": {}, "resourceName": "HsmConfiguration", "resourceIdentifier": "HsmConfigurationIdentifier"}]}, "ClusterSecurityGroups": {"completions": [{"parameters": {}, "resourceName": "Cluster", "resourceIdentifier": "ClusterSecurityGroups"}]}, "VpcSecurityGroupIds": {"completions": [{"parameters": {}, "resourceName": "Cluster", "resourceIdentifier": "VpcSecurityGroups"}]}, "PreferredMaintenanceWindow": {"completions": [{"parameters": {}, "resourceName": "Cluster", "resourceIdentifier": "PreferredMaintenanceWindow"}]}, "AutomatedSnapshotRetentionPeriod": {"completions": [{"parameters": {}, "resourceName": "Cluster", "resourceIdentifier": "AutomatedSnapshotRetentionPeriod"}]}, "KmsKeyId": {"completions": [{"parameters": {}, "resourceName": "SnapshotCopyGrant", "resourceIdentifier": "KmsKeyId"}]}, "NodeType": {"completions": [{"parameters": {}, "resourceName": "ReservedNode", "resourceIdentifier": "NodeType"}]}, "EnhancedVpcRouting": {"completions": [{"parameters": {}, "resourceName": "Cluster", "resourceIdentifier": "EnhancedVpcRouting"}]}, "IamRoles": {"completions": [{"parameters": {}, "resourceName": "Cluster", "resourceIdentifier": "IamRoles"}]}, "MaintenanceTrackName": {"completions": [{"parameters": {}, "resourceName": "Cluster", "resourceIdentifier": "MaintenanceTrackName"}]}}, "RestoreTableFromClusterSnapshot": {"ClusterIdentifier": {"completions": [{"parameters": {}, "resourceName": "TableRestoreStatu", "resourceIdentifier": "ClusterIdentifier"}]}, "SnapshotIdentifier": {"completions": [{"parameters": {}, "resourceName": "TableRestoreStatu", "resourceIdentifier": "SnapshotIdentifier"}]}, "SourceDatabaseName": {"completions": [{"parameters": {}, "resourceName": "TableRestoreStatu", "resourceIdentifier": "SourceDatabaseName"}]}, "SourceSchemaName": {"completions": [{"parameters": {}, "resourceName": "TableRestoreStatu", "resourceIdentifier": "SourceSchemaName"}]}, "SourceTableName": {"completions": [{"parameters": {}, "resourceName": "TableRestoreStatu", "resourceIdentifier": "SourceTableName"}]}, "TargetDatabaseName": {"completions": [{"parameters": {}, "resourceName": "TableRestoreStatu", "resourceIdentifier": "TargetDatabaseName"}]}, "TargetSchemaName": {"completions": [{"parameters": {}, "resourceName": "TableRestoreStatu", "resourceIdentifier": "TargetSchemaName"}]}, "NewTableName": {"completions": [{"parameters": {}, "resourceName": "TableRestoreStatu", "resourceIdentifier": "NewTableName"}]}}, "RevokeClusterSecurityGroupIngress": {"ClusterSecurityGroupName": {"completions": [{"parameters": {}, "resourceName": "ClusterSecurityGroup", "resourceIdentifier": "ClusterSecurityGroupName"}]}}, "RevokeSnapshotAccess": {"SnapshotIdentifier": {"completions": [{"parameters": {}, "resourceName": "TableRestoreStatu", "resourceIdentifier": "SnapshotIdentifier"}]}, "AccountWithRestoreAccess": {"completions": [{"parameters": {}, "resourceName": "ClusterSnapshot", "resourceIdentifier": "AccountsWithRestoreAccess"}]}}, "RotateEncryptionKey": {"ClusterIdentifier": {"completions": [{"parameters": {}, "resourceName": "TableRestoreStatu", "resourceIdentifier": "ClusterIdentifier"}]}}}}