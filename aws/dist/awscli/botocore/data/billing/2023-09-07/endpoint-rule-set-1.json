{"version": "1.0", "parameters": {"UseFIPS": {"builtIn": "AWS::UseFIPS", "required": true, "default": false, "documentation": "When true, send this request to the FIPS-compliant regional endpoint. If the configured endpoint does not have a FIPS compliant endpoint, dispatching the request will return an error.", "type": "Boolean"}, "Endpoint": {"builtIn": "SDK::Endpoint", "required": false, "documentation": "Override the endpoint used to send this request", "type": "String"}, "Region": {"builtIn": "AWS::Region", "required": false, "documentation": "The AWS region used to dispatch the request.", "type": "String"}}, "rules": [{"conditions": [{"fn": "isSet", "argv": [{"ref": "Endpoint"}]}], "rules": [{"conditions": [{"fn": "booleanEquals", "argv": [{"ref": "UseFIPS"}, true]}], "error": "Invalid Configuration: FIPS and custom endpoint are not supported", "type": "error"}, {"conditions": [], "endpoint": {"url": {"ref": "Endpoint"}, "properties": {}, "headers": {}}, "type": "endpoint"}], "type": "tree"}, {"conditions": [], "rules": [{"conditions": [{"fn": "isSet", "argv": [{"ref": "Region"}]}], "rules": [{"conditions": [{"fn": "aws.partition", "argv": [{"ref": "Region"}], "assign": "PartitionResult"}], "rules": [{"conditions": [{"fn": "booleanEquals", "argv": [{"ref": "UseFIPS"}, true]}], "endpoint": {"url": "https://billing-fips.{PartitionResult#implicitGlobalRegion}.{PartitionResult#dualStackDnsSuffix}", "properties": {"authSchemes": [{"name": "sigv4", "signingRegion": "{PartitionResult#implicitGlobalRegion}"}]}, "headers": {}}, "type": "endpoint"}, {"conditions": [], "endpoint": {"url": "https://billing.{PartitionResult#implicitGlobalRegion}.{PartitionResult#dualStackDnsSuffix}", "properties": {"authSchemes": [{"name": "sigv4", "signingRegion": "{PartitionResult#implicitGlobalRegion}"}]}, "headers": {}}, "type": "endpoint"}], "type": "tree"}], "type": "tree"}, {"conditions": [], "error": "Invalid Configuration: Missing Region", "type": "error"}], "type": "tree"}]}