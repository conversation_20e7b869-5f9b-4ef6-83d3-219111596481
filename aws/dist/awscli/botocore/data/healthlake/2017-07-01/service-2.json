{"version": "2.0", "metadata": {"apiVersion": "2017-07-01", "endpointPrefix": "healthlake", "jsonVersion": "1.0", "protocol": "json", "protocols": ["json"], "serviceAbbreviation": "HealthLake", "serviceFullName": "Amazon HealthLake", "serviceId": "HealthLake", "signatureVersion": "v4", "signingName": "healthlake", "targetPrefix": "HealthLake", "uid": "healthlake-2017-07-01", "auth": ["aws.auth#sigv4"]}, "operations": {"CreateFHIRDatastore": {"name": "CreateFHIRDatastore", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateFHIRDatastoreRequest"}, "output": {"shape": "CreateFHIRDatastoreResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates a data store that can ingest and export FHIR formatted data.</p>"}, "DeleteFHIRDatastore": {"name": "DeleteFHIRDatastore", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteFHIRDatastoreRequest"}, "output": {"shape": "DeleteFHIRDatastoreResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes a data store. </p>"}, "DescribeFHIRDatastore": {"name": "DescribeFHIRDatastore", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeFHIRDatastoreRequest"}, "output": {"shape": "DescribeFHIRDatastoreResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets the properties associated with the FHIR data store, including the data store ID, data store ARN, data store name, data store status, when the data store was created, data store type version, and the data store's endpoint.</p>"}, "DescribeFHIRExportJob": {"name": "DescribeFHIRExportJob", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeFHIRExportJobRequest"}, "output": {"shape": "DescribeFHIRExportJobResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Displays the properties of a FHIR export job, including the ID, ARN, name, and the status of the job.</p>"}, "DescribeFHIRImportJob": {"name": "DescribeFHIRImportJob", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeFHIRImportJobRequest"}, "output": {"shape": "DescribeFHIRImportJobResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Displays the properties of a FHIR import job, including the ID, ARN, name, and the status of the job. </p>"}, "ListFHIRDatastores": {"name": "ListFHIRDatastores", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListFHIRDatastoresRequest"}, "output": {"shape": "ListFHIRDatastoresResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists all FHIR data stores that are in the user’s account, regardless of data store status.</p>"}, "ListFHIRExportJobs": {"name": "ListFHIRExportJobs", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListFHIRExportJobsRequest"}, "output": {"shape": "ListFHIRExportJobsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p> Lists all FHIR export jobs associated with an account and their statuses. </p>"}, "ListFHIRImportJobs": {"name": "ListFHIRImportJobs", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListFHIRImportJobsRequest"}, "output": {"shape": "ListFHIRImportJobsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p> Lists all FHIR import jobs associated with an account and their statuses. </p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p> Returns a list of all existing tags associated with a data store. </p>"}, "StartFHIRExportJob": {"name": "StartFHIRExportJob", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StartFHIRExportJobRequest"}, "output": {"shape": "StartFHIRExportJobResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p><PERSON>gins a FHIR export job.</p>"}, "StartFHIRImportJob": {"name": "StartFHIRImportJob", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StartFHIRImportJobRequest"}, "output": {"shape": "StartFHIRImportJobResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p><PERSON>gins a FHIR Import job.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p> Adds a user specified key and value tag to a data store. </p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p> Removes tags from a data store. </p>"}}, "shapes": {"AccessDeniedException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>Access is denied. Your account is not authorized to perform this operation.</p>", "exception": true}, "AmazonResourceName": {"type": "string", "max": 1011, "min": 1, "pattern": "^arn:aws((-us-gov)|(-iso)|(-iso-b)|(-cn))?:healthlake:[a-z0-9-]+:\\d{12}:datastore\\/fhir\\/.{32}"}, "AuthorizationStrategy": {"type": "string", "enum": ["SMART_ON_FHIR_V1", "SMART_ON_FHIR", "AWS_AUTH"]}, "Boolean": {"type": "boolean"}, "BoundedLengthString": {"type": "string", "max": 5000, "min": 1, "pattern": "[\\P{M}\\p{M}]{1,5000}"}, "ClientTokenString": {"type": "string", "max": 64, "min": 1, "pattern": "^[a-zA-Z0-9-]+$"}, "CmkType": {"type": "string", "enum": ["CUSTOMER_MANAGED_KMS_KEY", "AWS_OWNED_KMS_KEY"]}, "ConfigurationMetadata": {"type": "string"}, "ConflictException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>The data store is in a transition state and the user requested action can not be performed.</p>", "exception": true}, "CreateFHIRDatastoreRequest": {"type": "structure", "required": ["DatastoreTypeVersion"], "members": {"DatastoreName": {"shape": "DatastoreName", "documentation": "<p>The user generated name for the data store.</p>"}, "DatastoreTypeVersion": {"shape": "FHIRVersion", "documentation": "<p>The FHIR version of the data store. The only supported version is R4.</p>"}, "SseConfiguration": {"shape": "SseConfiguration", "documentation": "<p> The server-side encryption key configuration for a customer provided encryption key specified for creating a data store. </p>"}, "PreloadDataConfig": {"shape": "PreloadDataConfig", "documentation": "<p>Optional parameter to preload data upon creation of the data store. Currently, the only supported preloaded data is synthetic data generated from Synthea.</p>"}, "ClientToken": {"shape": "ClientTokenString", "documentation": "<p>Optional user provided token used for ensuring idempotency.</p>", "idempotencyToken": true}, "Tags": {"shape": "TagList", "documentation": "<p> Resource tags that are applied to a data store when it is created. </p>"}, "IdentityProviderConfiguration": {"shape": "IdentityProviderConfiguration", "documentation": "<p>The configuration of the identity provider that you want to use for your data store.</p>"}}}, "CreateFHIRDatastoreResponse": {"type": "structure", "required": ["DatastoreId", "DatastoreArn", "DatastoreStatus", "DatastoreEndpoint"], "members": {"DatastoreId": {"shape": "DatastoreId", "documentation": "<p>The AWS-generated data store id. This id is in the output from the initial data store creation call.</p>"}, "DatastoreArn": {"shape": "DatastoreArn", "documentation": "<p>The data store ARN is generated during the creation of the data store and can be found in the output from the initial data store creation call.</p>"}, "DatastoreStatus": {"shape": "DatastoreStatus", "documentation": "<p>The status of the FHIR data store.</p>"}, "DatastoreEndpoint": {"shape": "BoundedLengthString", "documentation": "<p>The AWS endpoint for the created data store.</p>"}}}, "DatastoreArn": {"type": "string", "pattern": "^arn:aws((-us-gov)|(-iso)|(-iso-b)|(-cn))?:healthlake:[a-zA-Z0-9-]+:[0-9]{12}:datastore/.+?"}, "DatastoreFilter": {"type": "structure", "members": {"DatastoreName": {"shape": "DatastoreName", "documentation": "<p>Allows the user to filter data store results by name.</p>"}, "DatastoreStatus": {"shape": "DatastoreStatus", "documentation": "<p>Allows the user to filter data store results by status.</p>"}, "CreatedBefore": {"shape": "Timestamp", "documentation": "<p>A filter that allows the user to set cutoff dates for records. All data stores created before the specified date will be included in the results. </p>"}, "CreatedAfter": {"shape": "Timestamp", "documentation": "<p>A filter that allows the user to set cutoff dates for records. All data stores created after the specified date will be included in the results.</p>"}}, "documentation": "<p>The filters applied to data store query.</p>"}, "DatastoreId": {"type": "string", "max": 32, "min": 1, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-%@]*)$"}, "DatastoreName": {"type": "string", "max": 256, "min": 1, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-%@]*)$"}, "DatastoreProperties": {"type": "structure", "required": ["DatastoreId", "DatastoreArn", "DatastoreStatus", "DatastoreTypeVersion", "DatastoreEndpoint"], "members": {"DatastoreId": {"shape": "DatastoreId", "documentation": "<p>The AWS-generated ID number for the data store.</p>"}, "DatastoreArn": {"shape": "DatastoreArn", "documentation": "<p>The Amazon Resource Name used in the creation of the data store.</p>"}, "DatastoreName": {"shape": "DatastoreName", "documentation": "<p>The user-generated name for the data store.</p>"}, "DatastoreStatus": {"shape": "DatastoreStatus", "documentation": "<p>The status of the data store.</p>"}, "CreatedAt": {"shape": "Timestamp", "documentation": "<p>The time that a data store was created. </p>"}, "DatastoreTypeVersion": {"shape": "FHIRVersion", "documentation": "<p>The FHIR version. Only R4 version data is supported.</p>"}, "DatastoreEndpoint": {"shape": "String", "documentation": "<p>The AWS endpoint for the data store. Each data store will have it's own endpoint with data store ID in the endpoint URL.</p>"}, "SseConfiguration": {"shape": "SseConfiguration", "documentation": "<p> The server-side encryption key configuration for a customer provided encryption key (CMK). </p>"}, "PreloadDataConfig": {"shape": "PreloadDataConfig", "documentation": "<p>The preloaded data configuration for the data store. Only data preloaded from Synthea is supported.</p>"}, "IdentityProviderConfiguration": {"shape": "IdentityProviderConfiguration", "documentation": "<p>The identity provider that you selected when you created the data store.</p>"}, "ErrorCause": {"shape": "Error<PERSON><PERSON><PERSON>", "documentation": "<p>The error cause for the current data store operation.</p>"}}, "documentation": "<p>Displays the properties of the data store, including the ID, ARN, name, and the status of the data store.</p>"}, "DatastorePropertiesList": {"type": "list", "member": {"shape": "DatastoreProperties"}}, "DatastoreStatus": {"type": "string", "enum": ["CREATING", "ACTIVE", "DELETING", "DELETED", "CREATE_FAILED"]}, "DeleteFHIRDatastoreRequest": {"type": "structure", "required": ["DatastoreId"], "members": {"DatastoreId": {"shape": "DatastoreId", "documentation": "<p> The AWS-generated ID for the data store to be deleted.</p>"}}}, "DeleteFHIRDatastoreResponse": {"type": "structure", "required": ["DatastoreId", "DatastoreArn", "DatastoreStatus", "DatastoreEndpoint"], "members": {"DatastoreId": {"shape": "DatastoreId", "documentation": "<p>The AWS-generated ID for the data store to be deleted.</p>"}, "DatastoreArn": {"shape": "DatastoreArn", "documentation": "<p>The Amazon Resource Name (ARN) that gives AWS HealthLake access permission.</p>"}, "DatastoreStatus": {"shape": "DatastoreStatus", "documentation": "<p>The status of the data store that the user has requested to be deleted. </p>"}, "DatastoreEndpoint": {"shape": "BoundedLengthString", "documentation": "<p>The AWS endpoint for the data store the user has requested to be deleted.</p>"}}}, "DescribeFHIRDatastoreRequest": {"type": "structure", "required": ["DatastoreId"], "members": {"DatastoreId": {"shape": "DatastoreId", "documentation": "<p>The AWS-generated data store ID.</p>"}}}, "DescribeFHIRDatastoreResponse": {"type": "structure", "required": ["DatastoreProperties"], "members": {"DatastoreProperties": {"shape": "DatastoreProperties", "documentation": "<p>All properties associated with a data store, including the data store ID, data store ARN, data store name, data store status, when the data store was created, data store type version, and the data store's endpoint.</p>"}}}, "DescribeFHIRExportJobRequest": {"type": "structure", "required": ["DatastoreId", "JobId"], "members": {"DatastoreId": {"shape": "DatastoreId", "documentation": "<p>The AWS generated ID for the data store from which files are being exported from for an export job.</p>"}, "JobId": {"shape": "JobId", "documentation": "<p>The AWS generated ID for an export job.</p>"}}}, "DescribeFHIRExportJobResponse": {"type": "structure", "required": ["ExportJobProperties"], "members": {"ExportJobProperties": {"shape": "ExportJobProperties", "documentation": "<p>Displays the properties of the export job, including the ID, Arn, Name, and the status of the job. </p>"}}}, "DescribeFHIRImportJobRequest": {"type": "structure", "required": ["DatastoreId", "JobId"], "members": {"DatastoreId": {"shape": "DatastoreId", "documentation": "<p>The AWS-generated ID of the data store.</p>"}, "JobId": {"shape": "JobId", "documentation": "<p>The AWS-generated job ID.</p>"}}}, "DescribeFHIRImportJobResponse": {"type": "structure", "required": ["ImportJobProperties"], "members": {"ImportJobProperties": {"shape": "ImportJobProperties", "documentation": "<p>The properties of the Import job request, including the ID, ARN, name, status of the job, and the progress report of the job.</p>"}}}, "EncryptionKeyID": {"type": "string", "max": 400, "min": 1, "pattern": "(arn:aws((-us-gov)|(-iso)|(-iso-b)|(-cn))?:kms:)?([a-z]{2}-[a-z]+(-[a-z]+)?-\\d:)?(\\d{12}:)?(((key/)?[a-zA-Z0-9-_]+)|(alias/[a-zA-Z0-9:/_-]+))"}, "ErrorCategory": {"type": "string", "enum": ["RETRYABLE_ERROR", "NON_RETRYABLE_ERROR"]}, "ErrorCause": {"type": "structure", "members": {"ErrorMessage": {"shape": "ErrorMessage", "documentation": "<p>The text of the error message.</p>"}, "ErrorCategory": {"shape": "Error<PERSON>ate<PERSON><PERSON>", "documentation": "<p>The error category of the create/delete data store operation. Possible statuses are RETRYABLE_ERROR or NON_RETRYABLE_ERROR.</p>"}}, "documentation": "<p>The error info of the create/delete data store operation.</p>"}, "ErrorMessage": {"type": "string", "max": 4096, "min": 1}, "ExportJobProperties": {"type": "structure", "required": ["JobId", "JobStatus", "SubmitTime", "DatastoreId", "OutputDataConfig"], "members": {"JobId": {"shape": "JobId", "documentation": "<p>The AWS generated ID for an export job.</p>"}, "JobName": {"shape": "JobName", "documentation": "<p>The user generated name for an export job.</p>"}, "JobStatus": {"shape": "JobStatus", "documentation": "<p>The status of a FHIR export job. Possible statuses are SUBMITTED, IN_PROGRESS, COMPLETED, or FAILED.</p>"}, "SubmitTime": {"shape": "Timestamp", "documentation": "<p>The time an export job was initiated.</p>"}, "EndTime": {"shape": "Timestamp", "documentation": "<p>The time an export job completed.</p>"}, "DatastoreId": {"shape": "DatastoreId", "documentation": "<p>The AWS generated ID for the data store from which files are being exported for an export job.</p>"}, "OutputDataConfig": {"shape": "OutputDataConfig", "documentation": "<p>The output data configuration that was supplied when the export job was created.</p>"}, "DataAccessRoleArn": {"shape": "IamRoleArn", "documentation": "<p>The Amazon Resource Name used during the initiation of the job.</p>"}, "Message": {"shape": "Message", "documentation": "<p>An explanation of any errors that may have occurred during the export job.</p>"}}, "documentation": "<p>The properties of a FHIR export job, including the ID, ARN, name, and the status of the job.</p>"}, "ExportJobPropertiesList": {"type": "list", "member": {"shape": "ExportJobProperties"}}, "FHIRVersion": {"type": "string", "enum": ["R4"]}, "GenericDouble": {"type": "double"}, "GenericLong": {"type": "long"}, "IamRoleArn": {"type": "string", "max": 2048, "min": 20, "pattern": "arn:aws(-[^:]+)?:iam::[0-9]{12}:role/.+"}, "IdentityProviderConfiguration": {"type": "structure", "required": ["AuthorizationStrategy"], "members": {"AuthorizationStrategy": {"shape": "AuthorizationStrategy", "documentation": "<p>The authorization strategy that you selected when you created the data store.</p>"}, "FineGrainedAuthorizationEnabled": {"shape": "Boolean", "documentation": "<p>If you enabled fine-grained authorization when you created the data store.</p>"}, "Metadata": {"shape": "ConfigurationMetadata", "documentation": "<p>The JSON metadata elements that you want to use in your identity provider configuration. Required elements are listed based on the launch specification of the SMART application. For more information on all possible elements, see <a href=\"https://build.fhir.org/ig/HL7/smart-app-launch/conformance.html#metadata\">Metadata</a> in SMART's App Launch specification.</p> <p> <code>authorization_endpoint</code>: The URL to the OAuth2 authorization endpoint.</p> <p> <code>grant_types_supported</code>: An array of grant types that are supported at the token endpoint. You must provide at least one grant type option. Valid options are <code>authorization_code</code> and <code>client_credentials</code>.</p> <p> <code>token_endpoint</code>: The URL to the OAuth2 token endpoint.</p> <p> <code>capabilities</code>: An array of strings of the SMART capabilities that the authorization server supports.</p> <p> <code>code_challenge_methods_supported</code>: An array of strings of supported PKCE code challenge methods. You must include the <code>S256</code> method in the array of PKCE code challenge methods.</p>"}, "IdpLambdaArn": {"shape": "LambdaArn", "documentation": "<p>The Amazon Resource Name (ARN) of the Lambda function that you want to use to decode the access token created by the authorization server.</p>"}}, "documentation": "<p>The identity provider configuration that you gave when the data store was created.</p>"}, "ImportJobProperties": {"type": "structure", "required": ["JobId", "JobStatus", "SubmitTime", "DatastoreId", "InputDataConfig"], "members": {"JobId": {"shape": "JobId", "documentation": "<p>The AWS-generated id number for the Import job.</p>"}, "JobName": {"shape": "JobName", "documentation": "<p>The user-generated name for an Import job.</p>"}, "JobStatus": {"shape": "JobStatus", "documentation": "<p>The job status for an Import job. Possible statuses are SUBMITTED, IN_PROGRESS, COMPLETED_WITH_ERRORS, COMPLETED, FAILED.</p>"}, "SubmitTime": {"shape": "Timestamp", "documentation": "<p>The time that the Import job was submitted for processing.</p>"}, "EndTime": {"shape": "Timestamp", "documentation": "<p>The time that the Import job was completed.</p>"}, "DatastoreId": {"shape": "DatastoreId", "documentation": "<p>The datastore id used when the Import job was created. </p>"}, "InputDataConfig": {"shape": "InputDataConfig", "documentation": "<p>The input data configuration that was supplied when the Import job was created.</p>"}, "JobOutputDataConfig": {"shape": "OutputDataConfig"}, "JobProgressReport": {"shape": "JobProgressReport", "documentation": "<p>Displays the progress of the import job, including total resources scanned, total resources ingested, and total size of data ingested.</p>"}, "DataAccessRoleArn": {"shape": "IamRoleArn", "documentation": "<p>The Amazon Resource Name (ARN) that gives AWS HealthLake access to your input data.</p>"}, "Message": {"shape": "Message", "documentation": "<p>An explanation of any errors that may have occurred during the FHIR import job. </p>"}}, "documentation": "<p>Displays the properties of the import job, including the ID, Arn, Name, the status of the job, and the progress report of the job.</p>"}, "ImportJobPropertiesList": {"type": "list", "member": {"shape": "ImportJobProperties"}}, "InputDataConfig": {"type": "structure", "members": {"S3Uri": {"shape": "S3Uri", "documentation": "<p>The S3Uri is the user specified S3 location of the FHIR data to be imported into AWS HealthLake. </p>"}}, "documentation": "<p> The input properties for an import job.</p>", "union": true}, "InternalServerException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>Unknown error occurs in the service.</p>", "exception": true, "fault": true}, "JobId": {"type": "string", "max": 32, "min": 1, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-%@]*)$"}, "JobName": {"type": "string", "max": 64, "min": 1, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-%@]*)$"}, "JobProgressReport": {"type": "structure", "members": {"TotalNumberOfScannedFiles": {"shape": "GenericLong", "documentation": "<p>The number of files scanned from input S3 bucket.</p>"}, "TotalSizeOfScannedFilesInMB": {"shape": "GenericDouble", "documentation": "<p>The size (in MB) of the files scanned from the input S3 bucket.</p>"}, "TotalNumberOfImportedFiles": {"shape": "GenericLong", "documentation": "<p>The number of files imported so far.</p>"}, "TotalNumberOfResourcesScanned": {"shape": "GenericLong", "documentation": "<p>The number of resources scanned from the input S3 bucket.</p>"}, "TotalNumberOfResourcesImported": {"shape": "GenericLong", "documentation": "<p>The number of resources imported so far.</p>"}, "TotalNumberOfResourcesWithCustomerError": {"shape": "GenericLong", "documentation": "<p>The number of resources that failed due to customer error.</p>"}, "TotalNumberOfFilesReadWithCustomerError": {"shape": "GenericLong", "documentation": "<p>The number of files that failed to be read from the input S3 bucket due to customer error.</p>"}, "Throughput": {"shape": "GenericDouble", "documentation": "<p>The throughput (in MB/sec) of the import job.</p>"}}, "documentation": "<p>The progress report of an import job.</p>"}, "JobStatus": {"type": "string", "enum": ["SUBMITTED", "QUEUED", "IN_PROGRESS", "COMPLETED_WITH_ERRORS", "COMPLETED", "FAILED", "CANCEL_SUBMITTED", "CANCEL_IN_PROGRESS", "CANCEL_COMPLETED", "CANCEL_FAILED"]}, "KmsEncryptionConfig": {"type": "structure", "required": ["CmkType"], "members": {"CmkType": {"shape": "CmkType", "documentation": "<p> The type of customer-managed-key(CMK) used for encryption. The two types of supported CMKs are customer owned CMKs and AWS owned CMKs. </p>"}, "KmsKeyId": {"shape": "EncryptionKeyID", "documentation": "<p> The KMS encryption key id/alias used to encrypt the data store contents at rest. </p>"}}, "documentation": "<p> The customer-managed-key(CMK) used when creating a data store. If a customer owned key is not specified, an AWS owned key will be used for encryption. </p>"}, "LambdaArn": {"type": "string", "max": 256, "min": 49, "pattern": "arn:aws:lambda:[a-z]{2}-[a-z]+-\\d{1}:\\d{12}:function:[a-zA-Z0-9\\-_\\.]+(:(\\$LATEST|[a-zA-Z0-9\\-_]+))?"}, "ListFHIRDatastoresRequest": {"type": "structure", "members": {"Filter": {"shape": "DatastoreFilter", "documentation": "<p>Lists all filters associated with a FHIR data store request.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>Fetches the next page of data stores when results are paginated.</p>"}, "MaxResults": {"shape": "MaxResultsInteger", "documentation": "<p>The maximum number of data stores returned in a single page of a ListFHIRDatastoresRequest call.</p>"}}}, "ListFHIRDatastoresResponse": {"type": "structure", "required": ["DatastorePropertiesList"], "members": {"DatastorePropertiesList": {"shape": "DatastorePropertiesList", "documentation": "<p>All properties associated with the listed data stores.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>Pagination token that can be used to retrieve the next page of results.</p>"}}}, "ListFHIRExportJobsRequest": {"type": "structure", "required": ["DatastoreId"], "members": {"DatastoreId": {"shape": "DatastoreId", "documentation": "<p> This parameter limits the response to the export job with the specified data store ID. </p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p> A pagination token used to identify the next page of results to return for a ListFHIRExportJobs query. </p>"}, "MaxResults": {"shape": "MaxResultsInteger", "documentation": "<p> This parameter limits the number of results returned for a ListFHIRExportJobs to a maximum quantity specified by the user. </p>"}, "JobName": {"shape": "JobName", "documentation": "<p> This parameter limits the response to the export job with the specified job name. </p>"}, "JobStatus": {"shape": "JobStatus", "documentation": "<p> This parameter limits the response to the export jobs with the specified job status. </p>"}, "SubmittedBefore": {"shape": "Timestamp", "documentation": "<p> This parameter limits the response to FHIR export jobs submitted before a user specified date. </p>"}, "SubmittedAfter": {"shape": "Timestamp", "documentation": "<p> This parameter limits the response to FHIR export jobs submitted after a user specified date. </p>"}}}, "ListFHIRExportJobsResponse": {"type": "structure", "required": ["ExportJobPropertiesList"], "members": {"ExportJobPropertiesList": {"shape": "ExportJobPropertiesList", "documentation": "<p> The properties of listed FHIR export jobs, including the ID, ARN, name, and the status of the job. </p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p> A pagination token used to identify the next page of results to return for a ListFHIRExportJobs query. </p>"}}}, "ListFHIRImportJobsRequest": {"type": "structure", "required": ["DatastoreId"], "members": {"DatastoreId": {"shape": "DatastoreId", "documentation": "<p> This parameter limits the response to the import job with the specified data store ID. </p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p> A pagination token used to identify the next page of results to return for a ListFHIRImportJobs query. </p>"}, "MaxResults": {"shape": "MaxResultsInteger", "documentation": "<p> This parameter limits the number of results returned for a ListFHIRImportJobs to a maximum quantity specified by the user. </p>"}, "JobName": {"shape": "JobName", "documentation": "<p> This parameter limits the response to the import job with the specified job name. </p>"}, "JobStatus": {"shape": "JobStatus", "documentation": "<p> This parameter limits the response to the import job with the specified job status. </p>"}, "SubmittedBefore": {"shape": "Timestamp", "documentation": "<p> This parameter limits the response to FHIR import jobs submitted before a user specified date. </p>"}, "SubmittedAfter": {"shape": "Timestamp", "documentation": "<p> This parameter limits the response to FHIR import jobs submitted after a user specified date. </p>"}}}, "ListFHIRImportJobsResponse": {"type": "structure", "required": ["ImportJobPropertiesList"], "members": {"ImportJobPropertiesList": {"shape": "ImportJobPropertiesList", "documentation": "<p> The properties of a listed FHIR import jobs, including the ID, ARN, name, the status of the job, and the progress report of the job. </p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p> A pagination token used to identify the next page of results to return for a ListFHIRImportJobs query. </p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["ResourceARN"], "members": {"ResourceARN": {"shape": "AmazonResourceName", "documentation": "<p> The Amazon Resource Name(ARN) of the data store for which tags are being added. </p>"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"Tags": {"shape": "TagList", "documentation": "<p> Returns a list of tags associated with a data store. </p>"}}}, "MaxResultsInteger": {"type": "integer", "max": 500, "min": 1}, "Message": {"type": "string", "max": 2048, "min": 1, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-%@]*)$"}, "NextToken": {"type": "string", "max": 8192, "pattern": "\\p{ASCII}{0,8192}"}, "OutputDataConfig": {"type": "structure", "members": {"S3Configuration": {"shape": "S3Configuration", "documentation": "<p> The output data configuration that was supplied when the export job was created. </p>"}}, "documentation": "<p>The output data configuration that was supplied when the export job was created.</p>", "union": true}, "PreloadDataConfig": {"type": "structure", "required": ["PreloadDataType"], "members": {"PreloadDataType": {"shape": "PreloadDataType", "documentation": "<p>The type of preloaded data. Only Synthea preloaded data is supported.</p>"}}, "documentation": "<p> The input properties for the preloaded data store. Only data preloaded from Synthea is supported.</p>"}, "PreloadDataType": {"type": "string", "enum": ["SYNTHEA"]}, "ResourceNotFoundException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p> The requested data store was not found.</p>", "exception": true}, "S3Configuration": {"type": "structure", "required": ["S3Uri", "KmsKeyId"], "members": {"S3Uri": {"shape": "S3Uri", "documentation": "<p> The S3Uri is the user specified S3 location of the FHIR data to be imported into AWS HealthLake. </p>"}, "KmsKeyId": {"shape": "EncryptionKeyID", "documentation": "<p> The KMS key ID used to access the S3 bucket. </p>"}}, "documentation": "<p> The configuration of the S3 bucket for either an import or export job. This includes assigning permissions for access. </p>"}, "S3Uri": {"type": "string", "max": 1024, "pattern": "s3://[a-z0-9][\\.\\-a-z0-9]{1,61}[a-z0-9](/.*)?"}, "SseConfiguration": {"type": "structure", "required": ["KmsEncryptionConfig"], "members": {"KmsEncryptionConfig": {"shape": "KmsEncryptionConfig", "documentation": "<p> The KMS encryption configuration used to provide details for data encryption. </p>"}}, "documentation": "<p> The server-side encryption key configuration for a customer provided encryption key. </p>"}, "StartFHIRExportJobRequest": {"type": "structure", "required": ["OutputDataConfig", "DatastoreId", "DataAccessRoleArn"], "members": {"JobName": {"shape": "JobName", "documentation": "<p>The user generated name for an export job.</p>"}, "OutputDataConfig": {"shape": "OutputDataConfig", "documentation": "<p>The output data configuration that was supplied when the export job was created.</p>"}, "DatastoreId": {"shape": "DatastoreId", "documentation": "<p>The AWS generated ID for the data store from which files are being exported for an export job.</p>"}, "DataAccessRoleArn": {"shape": "IamRoleArn", "documentation": "<p>The Amazon Resource Name used during the initiation of the job.</p>"}, "ClientToken": {"shape": "ClientTokenString", "documentation": "<p>An optional user provided token used for ensuring idempotency.</p>", "idempotencyToken": true}}}, "StartFHIRExportJobResponse": {"type": "structure", "required": ["JobId", "JobStatus"], "members": {"JobId": {"shape": "JobId", "documentation": "<p>The AWS generated ID for an export job.</p>"}, "JobStatus": {"shape": "JobStatus", "documentation": "<p>The status of a FHIR export job. Possible statuses are SUBMITTED, IN_PROGRESS, COMPLETED, or FAILED.</p>"}, "DatastoreId": {"shape": "DatastoreId", "documentation": "<p>The AWS generated ID for the data store from which files are being exported for an export job.</p>"}}}, "StartFHIRImportJobRequest": {"type": "structure", "required": ["InputDataConfig", "JobOutputDataConfig", "DatastoreId", "DataAccessRoleArn"], "members": {"JobName": {"shape": "JobName", "documentation": "<p>The name of the FHIR Import job in the StartFHIRImport job request.</p>"}, "InputDataConfig": {"shape": "InputDataConfig", "documentation": "<p>The input properties of the FHIR Import job in the StartFHIRImport job request.</p>"}, "JobOutputDataConfig": {"shape": "OutputDataConfig"}, "DatastoreId": {"shape": "DatastoreId", "documentation": "<p>The AWS-generated data store ID.</p>"}, "DataAccessRoleArn": {"shape": "IamRoleArn", "documentation": "<p>The Amazon Resource Name (ARN) that gives AWS HealthLake access permission.</p>"}, "ClientToken": {"shape": "ClientTokenString", "documentation": "<p>Optional user provided token used for ensuring idempotency.</p>", "idempotencyToken": true}}}, "StartFHIRImportJobResponse": {"type": "structure", "required": ["JobId", "JobStatus"], "members": {"JobId": {"shape": "JobId", "documentation": "<p>The AWS-generated job ID.</p>"}, "JobStatus": {"shape": "JobStatus", "documentation": "<p>The status of an import job.</p>"}, "DatastoreId": {"shape": "DatastoreId", "documentation": "<p>The AWS-generated data store ID.</p>"}}}, "String": {"type": "string", "max": 10000, "pattern": "[\\P{M}\\p{M}]{0,10000}"}, "Tag": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {"shape": "TagKey", "documentation": "<p> The key portion of a tag. Tag keys are case sensitive. </p>"}, "Value": {"shape": "TagValue", "documentation": "<p> The value portion of a tag. Tag values are case sensitive. </p>"}}, "documentation": "<p> A tag is a label consisting of a user-defined key and value. The form for tags is {\"Key\", \"Value\"} </p>"}, "TagKey": {"type": "string", "max": 128, "min": 1, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 200, "min": 0}, "TagList": {"type": "list", "member": {"shape": "Tag"}, "max": 200, "min": 0}, "TagResourceRequest": {"type": "structure", "required": ["ResourceARN", "Tags"], "members": {"ResourceARN": {"shape": "AmazonResourceName", "documentation": "<p> The Amazon Resource Name(ARN)that gives AWS HealthLake access to the data store which tags are being added to. </p>"}, "Tags": {"shape": "TagList", "documentation": "<p> The user specified key and value pair tags being added to a data store. </p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "ThrottlingException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>The user has exceeded their maximum number of allowed calls to the given API. </p>", "exception": true}, "Timestamp": {"type": "timestamp"}, "UntagResourceRequest": {"type": "structure", "required": ["ResourceARN", "TagKeys"], "members": {"ResourceARN": {"shape": "AmazonResourceName", "documentation": "<p>The Amazon Resource Name(ARN) of the data store for which tags are being removed.</p>"}, "TagKeys": {"shape": "TagKeyList", "documentation": "<p> The keys for the tags to be removed from the HealthLake data store. </p>"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "ValidationException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>The user input parameter was invalid.</p>", "exception": true}}, "documentation": "<p>AWS HealthLake is a HIPAA eligibile service that allows customers to store, transform, query, and analyze their FHIR-formatted data in a consistent fashion in the cloud.</p>"}