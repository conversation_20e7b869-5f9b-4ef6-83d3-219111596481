{"version": "2.0", "metadata": {"apiVersion": "2023-07-26", "auth": ["aws.auth#sigv4"], "endpointPrefix": "bedrock-data-automation", "protocol": "rest-json", "protocols": ["rest-json"], "serviceFullName": "Data Automation for Amazon Bedrock", "serviceId": "Bedrock Data Automation", "signatureVersion": "v4", "signingName": "bedrock", "uid": "bedrock-data-automation-2023-07-26"}, "operations": {"CreateBlueprint": {"name": "CreateBlueprint", "http": {"method": "PUT", "requestUri": "/blueprints/", "responseCode": 201}, "input": {"shape": "CreateBlueprintRequest"}, "output": {"shape": "CreateBlueprintResponse"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Creates an Amazon Bedrock Data Automation Blueprint</p>", "idempotent": true}, "CreateBlueprintVersion": {"name": "CreateBlueprintVersion", "http": {"method": "POST", "requestUri": "/blueprints/{blueprintArn}/versions/", "responseCode": 201}, "input": {"shape": "CreateBlueprintVersionRequest"}, "output": {"shape": "CreateBlueprintVersionResponse"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Creates a new version of an existing Amazon Bedrock Data Automation Blueprint</p>", "idempotent": true}, "CreateDataAutomationProject": {"name": "CreateDataAutomationProject", "http": {"method": "PUT", "requestUri": "/data-automation-projects/", "responseCode": 201}, "input": {"shape": "CreateDataAutomationProjectRequest"}, "output": {"shape": "CreateDataAutomationProjectResponse"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Creates an Amazon Bedrock Data Automation Project</p>", "idempotent": true}, "DeleteBlueprint": {"name": "DeleteBlueprint", "http": {"method": "DELETE", "requestUri": "/blueprints/{blueprintArn}/", "responseCode": 200}, "input": {"shape": "DeleteBlueprintRequest"}, "output": {"shape": "DeleteBlueprintResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Deletes an existing Amazon Bedrock Data Automation Blueprint</p>", "idempotent": true}, "DeleteDataAutomationProject": {"name": "DeleteDataAutomationProject", "http": {"method": "DELETE", "requestUri": "/data-automation-projects/{projectArn}/", "responseCode": 200}, "input": {"shape": "DeleteDataAutomationProjectRequest"}, "output": {"shape": "DeleteDataAutomationProjectResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Deletes an existing Amazon Bedrock Data Automation Project</p>", "idempotent": true}, "GetBlueprint": {"name": "GetBlueprint", "http": {"method": "POST", "requestUri": "/blueprints/{blueprintArn}/", "responseCode": 200}, "input": {"shape": "GetBlueprintRequest"}, "output": {"shape": "GetBlueprintResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Gets an existing Amazon Bedrock Data Automation Blueprint</p>"}, "GetDataAutomationProject": {"name": "GetDataAutomationProject", "http": {"method": "POST", "requestUri": "/data-automation-projects/{projectArn}/", "responseCode": 200}, "input": {"shape": "GetDataAutomationProjectRequest"}, "output": {"shape": "GetDataAutomationProjectResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Gets an existing Amazon Bedrock Data Automation Project</p>"}, "ListBlueprints": {"name": "ListBlueprints", "http": {"method": "POST", "requestUri": "/blueprints/", "responseCode": 200}, "input": {"shape": "ListBlueprintsRequest"}, "output": {"shape": "ListBlueprintsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Lists all existing Amazon Bedrock Data Automation Blueprints</p>"}, "ListDataAutomationProjects": {"name": "ListDataAutomationProjects", "http": {"method": "POST", "requestUri": "/data-automation-projects/", "responseCode": 200}, "input": {"shape": "ListDataAutomationProjectsRequest"}, "output": {"shape": "ListDataAutomationProjectsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Lists all existing Amazon Bedrock Data Automation Projects</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "POST", "requestUri": "/listTagsForResource", "responseCode": 200}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>List tags for an Amazon Bedrock Data Automation resource</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tagResource", "responseCode": 200}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Tag an Amazon Bedrock Data Automation resource</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "POST", "requestUri": "/untagResource", "responseCode": 200}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Untag an Amazon Bedrock Data Automation resource</p>"}, "UpdateBlueprint": {"name": "UpdateBlueprint", "http": {"method": "PUT", "requestUri": "/blueprints/{blueprintArn}/", "responseCode": 200}, "input": {"shape": "UpdateBlueprintRequest"}, "output": {"shape": "UpdateBlueprintResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Updates an existing Amazon Bedrock Data Automation Blueprint</p>", "idempotent": true}, "UpdateDataAutomationProject": {"name": "UpdateDataAutomationProject", "http": {"method": "PUT", "requestUri": "/data-automation-projects/{projectArn}/", "responseCode": 200}, "input": {"shape": "UpdateDataAutomationProjectRequest"}, "output": {"shape": "UpdateDataAutomationProjectResponse"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Updates an existing Amazon Bedrock Data Automation Project</p>", "idempotent": true}}, "shapes": {"AccessDeniedException": {"type": "structure", "members": {"message": {"shape": "NonBlankString"}}, "documentation": "<p>This exception is thrown when a request is denied per access permissions</p>", "error": {"httpStatusCode": 403, "senderFault": true}, "exception": true}, "AudioExtractionCategory": {"type": "structure", "required": ["state"], "members": {"state": {"shape": "State"}, "types": {"shape": "AudioExtractionCategoryTypes"}}, "documentation": "<p>Category of Audio Extraction</p>"}, "AudioExtractionCategoryType": {"type": "string", "enum": ["AUDIO_CONTENT_MODERATION", "TRANSCRIPT", "TOPIC_CONTENT_MODERATION"]}, "AudioExtractionCategoryTypes": {"type": "list", "member": {"shape": "AudioExtractionCategoryType"}, "documentation": "<p>List of Audio Extraction Category Type</p>"}, "AudioOverrideConfiguration": {"type": "structure", "members": {"modalityProcessing": {"shape": "ModalityProcessingConfiguration"}}, "documentation": "<p>Override Configuration of Audio</p>"}, "AudioStandardExtraction": {"type": "structure", "required": ["category"], "members": {"category": {"shape": "AudioExtractionCategory"}}, "documentation": "<p>Standard Extraction Configuration of Audio</p>"}, "AudioStandardGenerativeField": {"type": "structure", "required": ["state"], "members": {"state": {"shape": "State"}, "types": {"shape": "AudioStandardGenerativeFieldTypes"}}, "documentation": "<p>Standard Generative Field Configuration of Audio</p>"}, "AudioStandardGenerativeFieldType": {"type": "string", "enum": ["AUDIO_SUMMARY", "IAB", "TOPIC_SUMMARY"]}, "AudioStandardGenerativeFieldTypes": {"type": "list", "member": {"shape": "AudioStandardGenerativeFieldType"}, "documentation": "<p>List of Audio Standard Generative Field Type</p>"}, "AudioStandardOutputConfiguration": {"type": "structure", "members": {"extraction": {"shape": "AudioStandardExtraction"}, "generativeField": {"shape": "AudioStandardGenerativeField"}}, "documentation": "<p>Standard Output Configuration of Audio</p>"}, "Blueprint": {"type": "structure", "required": ["blueprintArn", "schema", "type", "creationTime", "lastModifiedTime", "blueprintName"], "members": {"blueprintArn": {"shape": "BlueprintArn"}, "schema": {"shape": "BlueprintSchema"}, "type": {"shape": "Type"}, "creationTime": {"shape": "DateTimestamp"}, "lastModifiedTime": {"shape": "DateTimestamp"}, "blueprintName": {"shape": "BlueprintName"}, "blueprintVersion": {"shape": "BlueprintVersion"}, "blueprintStage": {"shape": "BlueprintStage"}, "kmsKeyId": {"shape": "KmsKeyId"}, "kmsEncryptionContext": {"shape": "KmsEncryptionContext"}}, "documentation": "<p>Contains the information of a Blueprint.</p>"}, "BlueprintArn": {"type": "string", "documentation": "<p>ARN of a Blueprint</p>", "max": 128, "min": 0, "pattern": "arn:aws(|-cn|-us-gov):bedrock:[a-zA-Z0-9-]*:(aws|[0-9]{12}):blueprint/(bedrock-data-automation-public-[a-zA-Z0-9-_]{1,30}|[a-zA-Z0-9-]{12,36})"}, "BlueprintFilter": {"type": "structure", "required": ["blueprintArn"], "members": {"blueprintArn": {"shape": "BlueprintArn"}, "blueprintVersion": {"shape": "BlueprintVersion"}, "blueprintStage": {"shape": "BlueprintStage"}}, "documentation": "<p>Blueprint Filter</p>"}, "BlueprintItem": {"type": "structure", "required": ["blueprintArn"], "members": {"blueprintArn": {"shape": "BlueprintArn"}, "blueprintVersion": {"shape": "BlueprintVersion"}, "blueprintStage": {"shape": "BlueprintStage"}}, "documentation": "<p>Blueprint Item</p>"}, "BlueprintItems": {"type": "list", "member": {"shape": "BlueprintItem"}, "documentation": "<p>List of Blueprint Item</p>"}, "BlueprintName": {"type": "string", "documentation": "<p>Name of the Blueprint</p>", "max": 128, "min": 1, "pattern": "[a-zA-Z0-9-_]+", "sensitive": true}, "BlueprintSchema": {"type": "string", "documentation": "<p>Schema of the blueprint</p>", "max": 100000, "min": 1, "sensitive": true}, "BlueprintStage": {"type": "string", "documentation": "<p>Stage of the Blueprint</p>", "enum": ["DEVELOPMENT", "LIVE"]}, "BlueprintStageFilter": {"type": "string", "documentation": "<p>Blueprint Stage filter</p>", "enum": ["DEVELOPMENT", "LIVE", "ALL"]}, "BlueprintSummary": {"type": "structure", "required": ["blueprintArn", "creationTime"], "members": {"blueprintArn": {"shape": "BlueprintArn"}, "blueprintVersion": {"shape": "BlueprintVersion"}, "blueprintStage": {"shape": "BlueprintStage"}, "blueprintName": {"shape": "BlueprintName"}, "creationTime": {"shape": "DateTimestamp"}, "lastModifiedTime": {"shape": "DateTimestamp"}}, "documentation": "<p>Summary of a Blueprint</p>"}, "BlueprintVersion": {"type": "string", "documentation": "<p>Blueprint Version</p>", "max": 128, "min": 1, "pattern": "[0-9]*"}, "Blueprints": {"type": "list", "member": {"shape": "BlueprintSummary"}, "documentation": "<p>List of Blueprints</p>"}, "ClientToken": {"type": "string", "documentation": "<p>Client specified token used for idempotency checks</p>", "max": 256, "min": 33, "pattern": "[a-zA-Z0-9](-*[a-zA-Z0-9]){0,256}"}, "ConflictException": {"type": "structure", "members": {"message": {"shape": "NonBlankString"}}, "documentation": "<p>This exception is thrown when there is a conflict performing an operation</p>", "error": {"httpStatusCode": 409, "senderFault": true}, "exception": true}, "CreateBlueprintRequest": {"type": "structure", "required": ["blueprintName", "type", "schema"], "members": {"blueprintName": {"shape": "BlueprintName"}, "type": {"shape": "Type"}, "blueprintStage": {"shape": "BlueprintStage"}, "schema": {"shape": "BlueprintSchema"}, "clientToken": {"shape": "ClientToken", "idempotencyToken": true}, "encryptionConfiguration": {"shape": "EncryptionConfiguration"}, "tags": {"shape": "TagList"}}, "documentation": "<p>Create Blueprint Request</p>"}, "CreateBlueprintResponse": {"type": "structure", "required": ["blueprint"], "members": {"blueprint": {"shape": "Blueprint"}}, "documentation": "<p>Create Blueprint Response</p>"}, "CreateBlueprintVersionRequest": {"type": "structure", "required": ["blueprintArn"], "members": {"blueprintArn": {"shape": "BlueprintArn", "documentation": "<p>ARN generated at the server side when a Blueprint is created</p>", "location": "uri", "locationName": "blueprintArn"}, "clientToken": {"shape": "ClientToken", "idempotencyToken": true}}, "documentation": "<p>Create Blueprint Version Request</p>"}, "CreateBlueprintVersionResponse": {"type": "structure", "required": ["blueprint"], "members": {"blueprint": {"shape": "Blueprint"}}, "documentation": "<p>Create Blueprint Version Response</p>"}, "CreateDataAutomationProjectRequest": {"type": "structure", "required": ["projectName", "standardOutputConfiguration"], "members": {"projectName": {"shape": "DataAutomationProjectName"}, "projectDescription": {"shape": "DataAutomationProjectDescription"}, "projectStage": {"shape": "DataAutomationProjectStage"}, "standardOutputConfiguration": {"shape": "StandardOutputConfiguration"}, "customOutputConfiguration": {"shape": "CustomOutputConfiguration"}, "overrideConfiguration": {"shape": "OverrideConfiguration"}, "clientToken": {"shape": "ClientToken", "idempotencyToken": true}, "encryptionConfiguration": {"shape": "EncryptionConfiguration"}, "tags": {"shape": "TagList"}}, "documentation": "<p>Create DataAutomationProject Request</p>"}, "CreateDataAutomationProjectResponse": {"type": "structure", "required": ["projectArn"], "members": {"projectArn": {"shape": "DataAutomationProjectArn"}, "projectStage": {"shape": "DataAutomationProjectStage"}, "status": {"shape": "DataAutomationProjectStatus"}}, "documentation": "<p>Create DataAutomationProject Response</p>"}, "CustomOutputConfiguration": {"type": "structure", "members": {"blueprints": {"shape": "BlueprintItems"}}, "documentation": "<p>Custom output configuration</p>"}, "DataAutomationProject": {"type": "structure", "required": ["projectArn", "creationTime", "lastModifiedTime", "projectName", "status"], "members": {"projectArn": {"shape": "DataAutomationProjectArn"}, "creationTime": {"shape": "DateTimestamp"}, "lastModifiedTime": {"shape": "DateTimestamp"}, "projectName": {"shape": "DataAutomationProjectName"}, "projectStage": {"shape": "DataAutomationProjectStage"}, "projectDescription": {"shape": "DataAutomationProjectDescription"}, "standardOutputConfiguration": {"shape": "StandardOutputConfiguration"}, "customOutputConfiguration": {"shape": "CustomOutputConfiguration"}, "overrideConfiguration": {"shape": "OverrideConfiguration"}, "status": {"shape": "DataAutomationProjectStatus"}, "kmsKeyId": {"shape": "KmsKeyId"}, "kmsEncryptionContext": {"shape": "KmsEncryptionContext"}}, "documentation": "<p>Contains the information of a DataAutomationProject.</p>"}, "DataAutomationProjectArn": {"type": "string", "documentation": "<p>ARN of a DataAutomationProject</p>", "max": 128, "min": 0, "pattern": "arn:aws(|-cn|-us-gov):bedrock:[a-zA-Z0-9-]*:(aws|[0-9]{12}):data-automation-project/[a-zA-Z0-9-]{12,36}"}, "DataAutomationProjectDescription": {"type": "string", "documentation": "<p>Description of the DataAutomationProject</p>", "max": 300, "min": 0, "sensitive": true}, "DataAutomationProjectFilter": {"type": "structure", "required": ["projectArn"], "members": {"projectArn": {"shape": "DataAutomationProjectArn"}, "projectStage": {"shape": "DataAutomationProjectStage"}}, "documentation": "<p>Data Automation Project Filter</p>"}, "DataAutomationProjectName": {"type": "string", "documentation": "<p>Name of the DataAutomationProject</p>", "max": 128, "min": 1, "pattern": "[a-zA-Z0-9-_]+", "sensitive": true}, "DataAutomationProjectStage": {"type": "string", "documentation": "<p>Stage of the Project</p>", "enum": ["DEVELOPMENT", "LIVE"]}, "DataAutomationProjectStageFilter": {"type": "string", "documentation": "<p>Project Stage filter</p>", "enum": ["DEVELOPMENT", "LIVE", "ALL"]}, "DataAutomationProjectStatus": {"type": "string", "documentation": "<p>Status of Data Automation Project</p>", "enum": ["COMPLETED", "IN_PROGRESS", "FAILED"]}, "DataAutomationProjectSummaries": {"type": "list", "member": {"shape": "DataAutomationProjectSummary"}, "documentation": "<p>List of DataAutomationProjectSummary</p>"}, "DataAutomationProjectSummary": {"type": "structure", "required": ["projectArn", "creationTime"], "members": {"projectArn": {"shape": "DataAutomationProjectArn"}, "projectStage": {"shape": "DataAutomationProjectStage"}, "projectName": {"shape": "DataAutomationProjectName"}, "creationTime": {"shape": "DateTimestamp"}}, "documentation": "<p>Summary of a DataAutomationProject</p>"}, "DateTimestamp": {"type": "timestamp", "documentation": "<p>Time Stamp</p>", "timestampFormat": "iso8601"}, "DeleteBlueprintRequest": {"type": "structure", "required": ["blueprintArn"], "members": {"blueprintArn": {"shape": "BlueprintArn", "documentation": "<p>ARN generated at the server side when a Blueprint is created</p>", "location": "uri", "locationName": "blueprintArn"}, "blueprintVersion": {"shape": "BlueprintVersion", "documentation": "<p>Optional field to delete a specific Blueprint version</p>", "location": "querystring", "locationName": "blueprintVersion"}}, "documentation": "<p>Delete Blueprint Request</p>"}, "DeleteBlueprintResponse": {"type": "structure", "members": {}, "documentation": "<p>Delete Blueprint Response</p>"}, "DeleteDataAutomationProjectRequest": {"type": "structure", "required": ["projectArn"], "members": {"projectArn": {"shape": "DataAutomationProjectArn", "documentation": "<p>ARN generated at the server side when a DataAutomationProject is created</p>", "location": "uri", "locationName": "projectArn"}}, "documentation": "<p>Delete DataAutomationProject Request</p>"}, "DeleteDataAutomationProjectResponse": {"type": "structure", "required": ["projectArn"], "members": {"projectArn": {"shape": "DataAutomationProjectArn"}, "status": {"shape": "DataAutomationProjectStatus"}}, "documentation": "<p>Delete DataAutomationProject Response</p>"}, "DesiredModality": {"type": "string", "documentation": "<p>Desired Modality types</p>", "enum": ["IMAGE", "DOCUMENT", "AUDIO", "VIDEO"]}, "DocumentBoundingBox": {"type": "structure", "required": ["state"], "members": {"state": {"shape": "State"}}, "documentation": "<p>Bounding Box Configuration of Document Extraction</p>"}, "DocumentExtractionGranularity": {"type": "structure", "members": {"types": {"shape": "DocumentExtractionGranularityTypes"}}, "documentation": "<p>Granularity of Document Extraction</p>"}, "DocumentExtractionGranularityType": {"type": "string", "enum": ["DOCUMENT", "PAGE", "ELEMENT", "WORD", "LINE"]}, "DocumentExtractionGranularityTypes": {"type": "list", "member": {"shape": "DocumentExtractionGranularityType"}, "documentation": "<p>List of Document Extraction Granularity Type</p>"}, "DocumentOutputAdditionalFileFormat": {"type": "structure", "required": ["state"], "members": {"state": {"shape": "State"}}, "documentation": "<p>Additional File Format of Document Output</p>"}, "DocumentOutputFormat": {"type": "structure", "required": ["textFormat", "additionalFileFormat"], "members": {"textFormat": {"shape": "DocumentOutputTextFormat"}, "additionalFileFormat": {"shape": "DocumentOutputAdditionalFileFormat"}}, "documentation": "<p>Output Format of Document</p>"}, "DocumentOutputTextFormat": {"type": "structure", "members": {"types": {"shape": "DocumentOutputTextFormatTypes"}}, "documentation": "<p>Text Format of Document Output</p>"}, "DocumentOutputTextFormatType": {"type": "string", "enum": ["PLAIN_TEXT", "MARKDOWN", "HTML", "CSV"]}, "DocumentOutputTextFormatTypes": {"type": "list", "member": {"shape": "DocumentOutputTextFormatType"}, "documentation": "<p>List of Document Output Text Format Type</p>"}, "DocumentOverrideConfiguration": {"type": "structure", "members": {"splitter": {"shape": "SplitterConfiguration"}, "modalityProcessing": {"shape": "ModalityProcessingConfiguration"}}, "documentation": "<p>Override Configuration of Document</p>"}, "DocumentStandardExtraction": {"type": "structure", "required": ["granularity", "boundingBox"], "members": {"granularity": {"shape": "DocumentExtractionGranularity"}, "boundingBox": {"shape": "DocumentBoundingBox"}}, "documentation": "<p>Standard Extraction Configuration of Document</p>"}, "DocumentStandardGenerativeField": {"type": "structure", "required": ["state"], "members": {"state": {"shape": "State"}}, "documentation": "<p>Standard Generative Field Configuration of Document</p>"}, "DocumentStandardOutputConfiguration": {"type": "structure", "members": {"extraction": {"shape": "DocumentStandardExtraction"}, "generativeField": {"shape": "DocumentStandardGenerativeField"}, "outputFormat": {"shape": "DocumentOutputFormat"}}, "documentation": "<p>Standard Output Configuration of Document</p>"}, "EncryptionConfiguration": {"type": "structure", "required": ["kmsKeyId"], "members": {"kmsKeyId": {"shape": "KmsKeyId"}, "kmsEncryptionContext": {"shape": "KmsEncryptionContext"}}, "documentation": "<p>KMS Encryption Configuration</p>"}, "EncryptionContextKey": {"type": "string", "documentation": "<p>Encryption context key.</p>", "max": 2000, "min": 1, "pattern": ".*\\S.*"}, "EncryptionContextValue": {"type": "string", "documentation": "<p>Encryption context value.</p>", "max": 2000, "min": 1, "pattern": ".*\\S.*"}, "GetBlueprintRequest": {"type": "structure", "required": ["blueprintArn"], "members": {"blueprintArn": {"shape": "BlueprintArn", "documentation": "<p>ARN generated at the server side when a Blueprint is created</p>", "location": "uri", "locationName": "blueprintArn"}, "blueprintVersion": {"shape": "BlueprintVersion", "documentation": "<p>Optional field to get a specific Blueprint version</p>"}, "blueprintStage": {"shape": "BlueprintStage", "documentation": "<p>Optional field to get a specific Blueprint stage</p>"}}, "documentation": "<p>Get Blueprint Request</p>"}, "GetBlueprintResponse": {"type": "structure", "required": ["blueprint"], "members": {"blueprint": {"shape": "Blueprint"}}, "documentation": "<p>Get Blueprint Response</p>"}, "GetDataAutomationProjectRequest": {"type": "structure", "required": ["projectArn"], "members": {"projectArn": {"shape": "DataAutomationProjectArn", "documentation": "<p>ARN generated at the server side when a DataAutomationProject is created</p>", "location": "uri", "locationName": "projectArn"}, "projectStage": {"shape": "DataAutomationProjectStage", "documentation": "<p>Optional field to delete a specific DataAutomationProject stage</p>"}}, "documentation": "<p>Get DataAutomationProject Request</p>"}, "GetDataAutomationProjectResponse": {"type": "structure", "required": ["project"], "members": {"project": {"shape": "DataAutomationProject"}}, "documentation": "<p>Get DataAutomationProject Response</p>"}, "ImageBoundingBox": {"type": "structure", "required": ["state"], "members": {"state": {"shape": "State"}}, "documentation": "<p>Bounding Box Configuration of Image Extraction</p>"}, "ImageExtractionCategory": {"type": "structure", "required": ["state"], "members": {"state": {"shape": "State"}, "types": {"shape": "ImageExtractionCategoryTypes"}}, "documentation": "<p>Category of Image Extraction</p>"}, "ImageExtractionCategoryType": {"type": "string", "enum": ["CONTENT_MODERATION", "TEXT_DETECTION", "LOGOS"]}, "ImageExtractionCategoryTypes": {"type": "list", "member": {"shape": "ImageExtractionCategoryType"}, "documentation": "<p>List of Image Extraction Category</p>"}, "ImageOverrideConfiguration": {"type": "structure", "members": {"modalityProcessing": {"shape": "ModalityProcessingConfiguration"}}, "documentation": "<p>Override Configuration of Image</p>"}, "ImageStandardExtraction": {"type": "structure", "required": ["category", "boundingBox"], "members": {"category": {"shape": "ImageExtractionCategory"}, "boundingBox": {"shape": "ImageBoundingBox"}}, "documentation": "<p>Standard Extraction Configuration of Image</p>"}, "ImageStandardGenerativeField": {"type": "structure", "required": ["state"], "members": {"state": {"shape": "State"}, "types": {"shape": "ImageStandardGenerativeFieldTypes"}}, "documentation": "<p>Standard Generative Field Configuration of Image</p>"}, "ImageStandardGenerativeFieldType": {"type": "string", "enum": ["IMAGE_SUMMARY", "IAB"]}, "ImageStandardGenerativeFieldTypes": {"type": "list", "member": {"shape": "ImageStandardGenerativeFieldType"}, "documentation": "<p>List of Image Standard Generative Field Type</p>"}, "ImageStandardOutputConfiguration": {"type": "structure", "members": {"extraction": {"shape": "ImageStandardExtraction"}, "generativeField": {"shape": "ImageStandardGenerativeField"}}, "documentation": "<p>Standard Output Configuration of Image</p>"}, "InternalServerException": {"type": "structure", "members": {"message": {"shape": "NonBlankString"}}, "documentation": "<p>This exception is thrown if there was an unexpected error during processing of request</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true}, "KmsEncryptionContext": {"type": "map", "key": {"shape": "EncryptionContextKey"}, "value": {"shape": "EncryptionContextValue"}, "documentation": "<p>KMS Encryption Context</p>", "min": 1}, "KmsKeyId": {"type": "string", "documentation": "<p>KMS Key Identifier</p>", "max": 2048, "min": 1, "pattern": "[A-Za-z0-9][A-Za-z0-9:_/+=,@.-]+"}, "ListBlueprintsRequest": {"type": "structure", "members": {"blueprintArn": {"shape": "BlueprintArn"}, "resourceOwner": {"shape": "ResourceOwner"}, "blueprintStageFilter": {"shape": "BlueprintStageFilter"}, "maxResults": {"shape": "MaxResults"}, "nextToken": {"shape": "NextToken"}, "projectFilter": {"shape": "DataAutomationProjectFilter"}}, "documentation": "<p>List Blueprint Request</p>"}, "ListBlueprintsResponse": {"type": "structure", "required": ["blueprints"], "members": {"blueprints": {"shape": "Blueprints"}, "nextToken": {"shape": "NextToken"}}, "documentation": "<p>List Blueprint Response</p>"}, "ListDataAutomationProjectsRequest": {"type": "structure", "members": {"maxResults": {"shape": "MaxResults"}, "nextToken": {"shape": "NextToken"}, "projectStageFilter": {"shape": "DataAutomationProjectStageFilter"}, "blueprintFilter": {"shape": "BlueprintFilter"}, "resourceOwner": {"shape": "ResourceOwner"}}, "documentation": "<p>List DataAutomationProject Request</p>"}, "ListDataAutomationProjectsResponse": {"type": "structure", "required": ["projects"], "members": {"projects": {"shape": "DataAutomationProjectSummaries"}, "nextToken": {"shape": "NextToken"}}, "documentation": "<p>List DataAutomationProject Response</p>"}, "ListTagsForResourceRequest": {"type": "structure", "required": ["resourceARN"], "members": {"resourceARN": {"shape": "TaggableResourceArn"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"tags": {"shape": "TagList"}}}, "MaxResults": {"type": "integer", "documentation": "<p>Max Results</p>", "box": true, "max": 1000, "min": 1}, "ModalityProcessingConfiguration": {"type": "structure", "members": {"state": {"shape": "State"}}, "documentation": "<p>Configuration to enable/disable processing of modality</p>"}, "ModalityRoutingConfiguration": {"type": "structure", "members": {"jpeg": {"shape": "DesiredModality"}, "png": {"shape": "DesiredModality"}, "mp4": {"shape": "DesiredModality"}, "mov": {"shape": "DesiredModality"}}, "documentation": "<p>Configuration for routing file type to desired modality</p>"}, "NextToken": {"type": "string", "documentation": "<p>Pagination token</p>", "max": 2048, "min": 1, "pattern": "\\S*"}, "NonBlankString": {"type": "string", "documentation": "<p>Non Blank String</p>", "pattern": "[\\s\\S]+"}, "OverrideConfiguration": {"type": "structure", "members": {"document": {"shape": "DocumentOverrideConfiguration"}, "image": {"shape": "ImageOverrideConfiguration"}, "video": {"shape": "VideoOverrideConfiguration"}, "audio": {"shape": "AudioOverrideConfiguration"}, "modalityRouting": {"shape": "ModalityRoutingConfiguration"}}, "documentation": "<p>Override configuration</p>"}, "ResourceNotFoundException": {"type": "structure", "members": {"message": {"shape": "NonBlankString"}}, "documentation": "<p>This exception is thrown when a resource referenced by the operation does not exist</p>", "error": {"httpStatusCode": 404, "senderFault": true}, "exception": true}, "ResourceOwner": {"type": "string", "documentation": "<p>Resource Owner</p>", "enum": ["SERVICE", "ACCOUNT"]}, "ServiceQuotaExceededException": {"type": "structure", "members": {"message": {"shape": "NonBlankString"}}, "documentation": "<p>This exception is thrown when a request is made beyond the service quota</p>", "error": {"httpStatusCode": 402, "senderFault": true}, "exception": true}, "SplitterConfiguration": {"type": "structure", "members": {"state": {"shape": "State"}}, "documentation": "<p>Configuration of Splitter</p>"}, "StandardOutputConfiguration": {"type": "structure", "members": {"document": {"shape": "DocumentStandardOutputConfiguration"}, "image": {"shape": "ImageStandardOutputConfiguration"}, "video": {"shape": "VideoStandardOutputConfiguration"}, "audio": {"shape": "AudioStandardOutputConfiguration"}}, "documentation": "<p>Standard output configuration</p>"}, "State": {"type": "string", "documentation": "<p>State</p>", "enum": ["ENABLED", "DISABLED"]}, "Tag": {"type": "structure", "required": ["key", "value"], "members": {"key": {"shape": "TagKey"}, "value": {"shape": "TagValue"}}, "documentation": "<p>Key value pair of a tag</p>"}, "TagKey": {"type": "string", "documentation": "<p>Defines the context of the tag.</p>", "max": 128, "min": 1}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "documentation": "<p>List of tag keys</p>", "max": 200, "min": 0}, "TagList": {"type": "list", "member": {"shape": "Tag"}, "documentation": "<p>List of tags</p>", "max": 200, "min": 0}, "TagResourceRequest": {"type": "structure", "required": ["resourceARN", "tags"], "members": {"resourceARN": {"shape": "TaggableResourceArn"}, "tags": {"shape": "TagList"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "documentation": "<p>Defines the value within the context. e.g. &lt;key=reason, value=training&gt;.</p>", "max": 256, "min": 0}, "TaggableResourceArn": {"type": "string", "documentation": "<p>ARN of a taggable resource</p>", "max": 1011, "min": 20, "pattern": "arn:aws(|-cn|-us-gov):bedrock:[a-z0-9-]*:[0-9]{12}:(blueprint|data-automation-project)/[a-zA-Z0-9-]{12,36}"}, "ThrottlingException": {"type": "structure", "members": {"message": {"shape": "NonBlankString"}}, "documentation": "<p>This exception is thrown when the number of requests exceeds the limit</p>", "error": {"httpStatusCode": 429, "senderFault": true}, "exception": true}, "Type": {"type": "string", "documentation": "<p>Type</p>", "enum": ["DOCUMENT", "IMAGE", "AUDIO", "VIDEO"]}, "UntagResourceRequest": {"type": "structure", "required": ["resourceARN", "tagKeys"], "members": {"resourceARN": {"shape": "TaggableResourceArn"}, "tagKeys": {"shape": "TagKeyList"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateBlueprintRequest": {"type": "structure", "required": ["blueprintArn", "schema"], "members": {"blueprintArn": {"shape": "BlueprintArn", "documentation": "<p>ARN generated at the server side when a Blueprint is created</p>", "location": "uri", "locationName": "blueprintArn"}, "schema": {"shape": "BlueprintSchema"}, "blueprintStage": {"shape": "BlueprintStage"}, "encryptionConfiguration": {"shape": "EncryptionConfiguration"}}, "documentation": "<p>Update Blueprint Request</p>"}, "UpdateBlueprintResponse": {"type": "structure", "required": ["blueprint"], "members": {"blueprint": {"shape": "Blueprint"}}, "documentation": "<p>Update Blueprint Response</p>"}, "UpdateDataAutomationProjectRequest": {"type": "structure", "required": ["projectArn", "standardOutputConfiguration"], "members": {"projectArn": {"shape": "DataAutomationProjectArn", "documentation": "<p>ARN generated at the server side when a DataAutomationProject is created</p>", "location": "uri", "locationName": "projectArn"}, "projectStage": {"shape": "DataAutomationProjectStage"}, "projectDescription": {"shape": "DataAutomationProjectDescription"}, "standardOutputConfiguration": {"shape": "StandardOutputConfiguration"}, "customOutputConfiguration": {"shape": "CustomOutputConfiguration"}, "overrideConfiguration": {"shape": "OverrideConfiguration"}, "encryptionConfiguration": {"shape": "EncryptionConfiguration"}}, "documentation": "<p>Update DataAutomationProject Request</p>"}, "UpdateDataAutomationProjectResponse": {"type": "structure", "required": ["projectArn"], "members": {"projectArn": {"shape": "DataAutomationProjectArn"}, "projectStage": {"shape": "DataAutomationProjectStage"}, "status": {"shape": "DataAutomationProjectStatus"}}, "documentation": "<p>Update DataAutomationProject Response</p>"}, "ValidationException": {"type": "structure", "members": {"message": {"shape": "NonBlankString"}, "fieldList": {"shape": "ValidationExceptionFieldList"}}, "documentation": "<p>This exception is thrown when the request's input validation fails</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "ValidationExceptionField": {"type": "structure", "required": ["name", "message"], "members": {"name": {"shape": "NonBlankString"}, "message": {"shape": "NonBlankString"}}, "documentation": "<p>Stores information about a field passed inside a request that resulted in an exception</p>"}, "ValidationExceptionFieldList": {"type": "list", "member": {"shape": "ValidationExceptionField"}, "documentation": "<p>list of ValidationExceptionField</p>"}, "VideoBoundingBox": {"type": "structure", "required": ["state"], "members": {"state": {"shape": "State"}}, "documentation": "<p>Bounding Box Configuration of Video Extraction</p>"}, "VideoExtractionCategory": {"type": "structure", "required": ["state"], "members": {"state": {"shape": "State"}, "types": {"shape": "VideoExtractionCategoryTypes"}}, "documentation": "<p>Category of Video Extraction</p>"}, "VideoExtractionCategoryType": {"type": "string", "enum": ["CONTENT_MODERATION", "TEXT_DETECTION", "TRANSCRIPT", "LOGOS"]}, "VideoExtractionCategoryTypes": {"type": "list", "member": {"shape": "VideoExtractionCategoryType"}, "documentation": "<p>List of Video Extraction Category Type</p>"}, "VideoOverrideConfiguration": {"type": "structure", "members": {"modalityProcessing": {"shape": "ModalityProcessingConfiguration"}}, "documentation": "<p>Override Configuration of Video</p>"}, "VideoStandardExtraction": {"type": "structure", "required": ["category", "boundingBox"], "members": {"category": {"shape": "VideoExtractionCategory"}, "boundingBox": {"shape": "VideoBoundingBox"}}, "documentation": "<p>Standard Extraction Configuration of Video</p>"}, "VideoStandardGenerativeField": {"type": "structure", "required": ["state"], "members": {"state": {"shape": "State"}, "types": {"shape": "VideoStandardGenerativeFieldTypes"}}, "documentation": "<p>Standard Generative Field Configuration of Video</p>"}, "VideoStandardGenerativeFieldType": {"type": "string", "enum": ["VIDEO_SUMMARY", "IAB", "CHAPTER_SUMMARY"]}, "VideoStandardGenerativeFieldTypes": {"type": "list", "member": {"shape": "VideoStandardGenerativeFieldType"}, "documentation": "<p>List of Video Standard Generative Field Type</p>"}, "VideoStandardOutputConfiguration": {"type": "structure", "members": {"extraction": {"shape": "VideoStandardExtraction"}, "generativeField": {"shape": "VideoStandardGenerativeField"}}, "documentation": "<p>Standard Output Configuration of Video</p>"}}, "documentation": "<p>Amazon Bedrock Data Automation BuildTime</p>"}