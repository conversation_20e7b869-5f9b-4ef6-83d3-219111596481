{"version": "2.0", "metadata": {"apiVersion": "2020-07-01", "auth": ["aws.auth#sigv4"], "endpointPrefix": "airflow", "protocol": "rest-json", "protocols": ["rest-json"], "serviceFullName": "AmazonMWAA", "serviceId": "MWAA", "signatureVersion": "v4", "signingName": "airflow", "uid": "mwaa-2020-07-01"}, "operations": {"CreateCliToken": {"name": "CreateCliToken", "http": {"method": "POST", "requestUri": "/clitoken/{Name}", "responseCode": 200}, "input": {"shape": "CreateCliTokenRequest"}, "output": {"shape": "CreateCliTokenResponse"}, "errors": [{"shape": "ResourceNotFoundException"}], "documentation": "<p>Creates a CLI token for the Airflow CLI. To learn more, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/call-mwaa-apis-cli.html\">Creating an Apache Airflow CLI token</a>.</p>", "endpoint": {"hostPrefix": "env."}}, "CreateEnvironment": {"name": "CreateEnvironment", "http": {"method": "PUT", "requestUri": "/environments/{Name}", "responseCode": 200}, "input": {"shape": "CreateEnvironmentInput"}, "output": {"shape": "CreateEnvironmentOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates an Amazon Managed Workflows for Apache Airflow (Amazon MWAA) environment.</p>", "endpoint": {"hostPrefix": "api."}, "idempotent": true}, "CreateWebLoginToken": {"name": "CreateWebLoginToken", "http": {"method": "POST", "requestUri": "/webtoken/{Name}", "responseCode": 200}, "input": {"shape": "CreateWebLoginTokenRequest"}, "output": {"shape": "CreateWebLoginTokenResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates a web login token for the Airflow Web UI. To learn more, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/call-mwaa-apis-web.html\">Creating an Apache Airflow web login token</a>.</p>", "endpoint": {"hostPrefix": "env."}, "idempotent": true}, "DeleteEnvironment": {"name": "DeleteEnvironment", "http": {"method": "DELETE", "requestUri": "/environments/{Name}", "responseCode": 200}, "input": {"shape": "DeleteEnvironmentInput"}, "output": {"shape": "DeleteEnvironmentOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes an Amazon Managed Workflows for Apache Airflow (Amazon MWAA) environment.</p>", "endpoint": {"hostPrefix": "api."}, "idempotent": true}, "GetEnvironment": {"name": "GetEnvironment", "http": {"method": "GET", "requestUri": "/environments/{Name}", "responseCode": 200}, "input": {"shape": "GetEnvironmentInput"}, "output": {"shape": "GetEnvironmentOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Describes an Amazon Managed Workflows for Apache Airflow (MWAA) environment.</p>", "endpoint": {"hostPrefix": "api."}}, "InvokeRestApi": {"name": "InvokeRestApi", "http": {"method": "POST", "requestUri": "/restapi/{Name}", "responseCode": 200}, "input": {"shape": "InvokeRestApiRequest"}, "output": {"shape": "InvokeRestApiResponse"}, "errors": [{"shape": "RestApiClientException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "RestApiServerException"}], "documentation": "<p>Invokes the Apache Airflow REST API on the webserver with the specified inputs. To learn more, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/access-mwaa-apache-airflow-rest-api.html\">Using the Apache Airflow REST API</a> </p>", "endpoint": {"hostPrefix": "env."}}, "ListEnvironments": {"name": "ListEnvironments", "http": {"method": "GET", "requestUri": "/environments", "responseCode": 200}, "input": {"shape": "ListEnvironmentsInput"}, "output": {"shape": "ListEnvironmentsOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists the Amazon Managed Workflows for Apache Airflow (MWAA) environments.</p>", "endpoint": {"hostPrefix": "api."}}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags/{ResourceArn}", "responseCode": 200}, "input": {"shape": "ListTagsForResourceInput"}, "output": {"shape": "ListTagsForResourceOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists the key-value tag pairs associated to the Amazon Managed Workflows for Apache Airflow (MWAA) environment. For example, <code>\"Environment\": \"Staging\"</code>. </p>", "endpoint": {"hostPrefix": "api."}}, "PublishMetrics": {"name": "PublishMetrics", "http": {"method": "POST", "requestUri": "/metrics/environments/{EnvironmentName}", "responseCode": 200}, "input": {"shape": "PublishMetricsInput"}, "output": {"shape": "PublishMetricsOutput"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p> <b>Internal only</b>. Publishes environment health metrics to Amazon CloudWatch.</p>", "deprecated": true, "deprecatedMessage": "This API is for internal use and not meant for public use, and is no longer available.", "endpoint": {"hostPrefix": "ops."}}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags/{ResourceArn}", "responseCode": 200}, "input": {"shape": "TagResourceInput"}, "output": {"shape": "TagResourceOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Associates key-value tag pairs to your Amazon Managed Workflows for Apache Airflow (MWAA) environment. </p>", "endpoint": {"hostPrefix": "api."}}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags/{ResourceArn}", "responseCode": 200}, "input": {"shape": "UntagResourceInput"}, "output": {"shape": "UntagResourceOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Removes key-value tag pairs associated to your Amazon Managed Workflows for Apache Airflow (MWAA) environment. For example, <code>\"Environment\": \"Staging\"</code>.</p>", "endpoint": {"hostPrefix": "api."}, "idempotent": true}, "UpdateEnvironment": {"name": "UpdateEnvironment", "http": {"method": "PATCH", "requestUri": "/environments/{Name}", "responseCode": 200}, "input": {"shape": "UpdateEnvironmentInput"}, "output": {"shape": "UpdateEnvironmentOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Updates an Amazon Managed Workflows for Apache Airflow (MWAA) environment.</p>", "endpoint": {"hostPrefix": "api."}}}, "shapes": {"AccessDeniedException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>Access to the Apache Airflow Web UI or CLI has been denied due to insufficient permissions. To learn more, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/access-policies.html\">Accessing an Amazon MWAA environment</a>.</p>", "error": {"httpStatusCode": 403, "senderFault": true}, "exception": true}, "AirflowConfigurationOptions": {"type": "map", "key": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "value": {"shape": "ConfigValue"}, "sensitive": true}, "AirflowIdentity": {"type": "string", "max": 64, "min": 1}, "AirflowVersion": {"type": "string", "max": 32, "min": 1, "pattern": "[0-9a-z.]+"}, "CeleryExecutorQueue": {"type": "string", "max": 1224, "min": 1, "pattern": "arn:aws(-[a-z]+)?:sqs:[a-z0-9\\-]+:\\d{12}:[a-zA-Z_0-9+=,.@\\-_/]+"}, "CloudWatchLogGroupArn": {"type": "string", "max": 1224, "min": 1, "pattern": "arn:aws(-[a-z]+)?:logs:[a-z0-9\\-]+:\\d{12}:log-group:\\w+.*"}, "ConfigKey": {"type": "string", "max": 64, "min": 1, "pattern": "[a-z]+([a-z0-9._]*[a-z0-9_]+)?"}, "ConfigValue": {"type": "string", "max": 65536, "min": 1, "pattern": "[ -~]+", "sensitive": true}, "CreateCliTokenRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "EnvironmentName", "documentation": "<p>The name of the Amazon MWAA environment. For example, <code>MyMWAAEnvironment</code>.</p>", "location": "uri", "locationName": "Name"}}}, "CreateCliTokenResponse": {"type": "structure", "members": {"CliToken": {"shape": "Token", "documentation": "<p>An Airflow CLI login token.</p>"}, "WebServerHostname": {"shape": "Hostname", "documentation": "<p>The Airflow web server hostname for the environment.</p>"}}}, "CreateEnvironmentInput": {"type": "structure", "required": ["Name", "ExecutionRoleArn", "SourceBucketArn", "DagS3Path", "NetworkConfiguration"], "members": {"Name": {"shape": "EnvironmentName", "documentation": "<p>The name of the Amazon MWAA environment. For example, <code>MyMWAAEnvironment</code>.</p>", "location": "uri", "locationName": "Name"}, "ExecutionRoleArn": {"shape": "IamRoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the execution role for your environment. An execution role is an Amazon Web Services Identity and Access Management (IAM) role that grants MWAA permission to access Amazon Web Services services and resources used by your environment. For example, <code>arn:aws:iam::123456789:role/my-execution-role</code>. For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/mwaa-create-role.html\">Amazon MWAA Execution role</a>.</p>"}, "SourceBucketArn": {"shape": "S3BucketArn", "documentation": "<p>The Amazon Resource Name (ARN) of the Amazon S3 bucket where your DAG code and supporting files are stored. For example, <code>arn:aws:s3:::my-airflow-bucket-unique-name</code>. For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/mwaa-s3-bucket.html\">Create an Amazon S3 bucket for Amazon MWAA</a>.</p>"}, "DagS3Path": {"shape": "Re<PERSON><PERSON><PERSON>", "documentation": "<p>The relative path to the DAGs folder on your Amazon S3 bucket. For example, <code>dags</code>. For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/configuring-dag-folder.html\">Adding or updating DAGs</a>.</p>"}, "NetworkConfiguration": {"shape": "NetworkConfiguration", "documentation": "<p>The VPC networking components used to secure and enable network traffic between the Amazon Web Services resources for your environment. For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/networking-about.html\">About networking on Amazon MWAA</a>.</p>"}, "PluginsS3Path": {"shape": "Re<PERSON><PERSON><PERSON>", "documentation": "<p>The relative path to the <code>plugins.zip</code> file on your Amazon S3 bucket. For example, <code>plugins.zip</code>. If specified, then the <code>plugins.zip</code> version is required. For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/configuring-dag-import-plugins.html\">Installing custom plugins</a>.</p>"}, "PluginsS3ObjectVersion": {"shape": "S3ObjectVersion", "documentation": "<p>The version of the plugins.zip file on your Amazon S3 bucket. You must specify a version each time a plugins.zip file is updated. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/versioning-workflows.html\">How S3 Versioning works</a>.</p>"}, "RequirementsS3Path": {"shape": "Re<PERSON><PERSON><PERSON>", "documentation": "<p>The relative path to the <code>requirements.txt</code> file on your Amazon S3 bucket. For example, <code>requirements.txt</code>. If specified, then a version is required. For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/working-dags-dependencies.html\">Installing Python dependencies</a>.</p>"}, "RequirementsS3ObjectVersion": {"shape": "S3ObjectVersion", "documentation": "<p>The version of the <code>requirements.txt</code> file on your Amazon S3 bucket. You must specify a version each time a requirements.txt file is updated. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/versioning-workflows.html\">How S3 Versioning works</a>.</p>"}, "StartupScriptS3Path": {"shape": "Re<PERSON><PERSON><PERSON>", "documentation": "<p>The relative path to the startup shell script in your Amazon S3 bucket. For example, <code>s3://mwaa-environment/startup.sh</code>.</p> <p> Amazon MWAA runs the script as your environment starts, and before running the Apache Airflow process. You can use this script to install dependencies, modify Apache Airflow configuration options, and set environment variables. For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/using-startup-script.html\">Using a startup script</a>. </p>"}, "StartupScriptS3ObjectVersion": {"shape": "S3ObjectVersion", "documentation": "<p>The version of the startup shell script in your Amazon S3 bucket. You must specify the <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/versioning-workflows.html\">version ID</a> that Amazon S3 assigns to the file every time you update the script. </p> <p> Version IDs are Unicode, UTF-8 encoded, URL-ready, opaque strings that are no more than 1,024 bytes long. The following is an example: </p> <p> <code>3sL4kqtJlcpXroDTDmJ+rmSpXd3dIbrHY+MTRCxf3vjVBH40Nr8X8gdRQBpUMLUo</code> </p> <p> For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/using-startup-script.html\">Using a startup script</a>. </p>"}, "AirflowConfigurationOptions": {"shape": "AirflowConfigurationOptions", "documentation": "<p>A list of key-value pairs containing the Apache Airflow configuration options you want to attach to your environment. For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/configuring-env-variables.html\">Apache Airflow configuration options</a>.</p>"}, "EnvironmentClass": {"shape": "EnvironmentClass", "documentation": "<p>The environment class type. Valid values: <code>mw1.micro</code>, <code>mw1.small</code>, <code>mw1.medium</code>, <code>mw1.large</code>, <code>mw1.xlarge</code>, and <code>mw1.2xlarge</code>. For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/environment-class.html\">Amazon MWAA environment class</a>.</p>"}, "MaxWorkers": {"shape": "MaxWorkers", "documentation": "<p>The maximum number of workers that you want to run in your environment. MWAA scales the number of Apache Airflow workers up to the number you specify in the <code>MaxWorkers</code> field. For example, <code>20</code>. When there are no more tasks running, and no more in the queue, MWAA disposes of the extra workers leaving the one worker that is included with your environment, or the number you specify in <code>MinWorkers</code>.</p>"}, "KmsKey": {"shape": "KmsKey", "documentation": "<p>The Amazon Web Services Key Management Service (KMS) key to encrypt the data in your environment. You can use an Amazon Web Services owned CMK, or a Customer managed CMK (advanced). For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/create-environment.html\">Create an Amazon MWAA environment</a>.</p>"}, "AirflowVersion": {"shape": "AirflowVersion", "documentation": "<p>The Apache Airflow version for your environment. If no value is specified, it defaults to the latest version. For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/airflow-versions.html\">Apache Airflow versions on Amazon Managed Workflows for Apache Airflow (Amazon MWAA)</a>.</p> <p>Valid values: <code>1.10.12</code>, <code>2.0.2</code>, <code>2.2.2</code>, <code>2.4.3</code>, <code>2.5.1</code>, <code>2.6.3</code>, <code>2.7.2</code>, <code>2.8.1</code>, <code>2.9.2</code>, <code>2.10.1</code>, and <code>2.10.3</code>.</p>"}, "LoggingConfiguration": {"shape": "LoggingConfigurationInput", "documentation": "<p>Defines the Apache Airflow logs to send to CloudWatch Logs.</p>"}, "WeeklyMaintenanceWindowStart": {"shape": "WeeklyMaintenanceWindowStart", "documentation": "<p>The day and time of the week in Coordinated Universal Time (UTC) 24-hour standard time to start weekly maintenance updates of your environment in the following format: <code>DAY:HH:MM</code>. For example: <code>TUE:03:30</code>. You can specify a start time in 30 minute increments only.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>The key-value tag pairs you want to associate to your environment. For example, <code>\"Environment\": \"Staging\"</code>. For more information, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws_tagging.html\">Tagging Amazon Web Services resources</a>.</p>"}, "WebserverAccessMode": {"shape": "WebserverAccessMode", "documentation": "<p>Defines the access mode for the Apache Airflow <i>web server</i>. For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/configuring-networking.html\">Apache Airflow access modes</a>.</p>"}, "MinWorkers": {"shape": "MinWorkers", "documentation": "<p>The minimum number of workers that you want to run in your environment. MWAA scales the number of Apache Airflow workers up to the number you specify in the <code>MaxWorkers</code> field. When there are no more tasks running, and no more in the queue, MWAA disposes of the extra workers leaving the worker count you specify in the <code>MinWorkers</code> field. For example, <code>2</code>.</p>"}, "Schedulers": {"shape": "Schedulers", "documentation": "<p>The number of Apache Airflow schedulers to run in your environment. Valid values:</p> <ul> <li> <p>v2 - For environments larger than mw1.micro, accepts values from <code>2</code> to <code>5</code>. Defaults to <code>2</code> for all environment sizes except mw1.micro, which defaults to <code>1</code>.</p> </li> <li> <p>v1 - Accepts <code>1</code>.</p> </li> </ul>"}, "EndpointManagement": {"shape": "EndpointManagement", "documentation": "<p>Defines whether the VPC endpoints configured for the environment are created, and managed, by the customer or by Amazon MWAA. If set to <code>SERVICE</code>, Amazon MWAA will create and manage the required VPC endpoints in your VPC. If set to <code>CUSTOMER</code>, you must create, and manage, the VPC endpoints for your VPC. If you choose to create an environment in a shared VPC, you must set this value to <code>CUSTOMER</code>. In a shared VPC deployment, the environment will remain in <code>PENDING</code> status until you create the VPC endpoints. If you do not take action to create the endpoints within 72 hours, the status will change to <code>CREATE_FAILED</code>. You can delete the failed environment and create a new one.</p>"}, "MinWebservers": {"shape": "MinWebservers", "documentation": "<p> The minimum number of web servers that you want to run in your environment. Amazon MWAA scales the number of Apache Airflow web servers up to the number you specify for <code>MaxWebservers</code> when you interact with your Apache Airflow environment using Apache Airflow REST API, or the Apache Airflow CLI. As the transaction-per-second rate, and the network load, decrease, Amazon MWAA disposes of the additional web servers, and scales down to the number set in <code>MinxWebserers</code>. </p> <p>Valid values: For environments larger than mw1.micro, accepts values from <code>2</code> to <code>5</code>. Defaults to <code>2</code> for all environment sizes except mw1.micro, which defaults to <code>1</code>.</p>"}, "MaxWebservers": {"shape": "MaxWebservers", "documentation": "<p> The maximum number of web servers that you want to run in your environment. Amazon MWAA scales the number of Apache Airflow web servers up to the number you specify for <code>MaxWebservers</code> when you interact with your Apache Airflow environment using Apache Airflow REST API, or the Apache Airflow CLI. For example, in scenarios where your workload requires network calls to the Apache Airflow REST API with a high transaction-per-second (TPS) rate, Amazon MWAA will increase the number of web servers up to the number set in <code>MaxWebserers</code>. As TPS rates decrease Amazon MWAA disposes of the additional web servers, and scales down to the number set in <code>MinxWebserers</code>. </p> <p>Valid values: For environments larger than mw1.micro, accepts values from <code>2</code> to <code>5</code>. Defaults to <code>2</code> for all environment sizes except mw1.micro, which defaults to <code>1</code>.</p>"}}, "documentation": "<p>This section contains the Amazon Managed Workflows for Apache Airflow (Amazon MWAA) API reference documentation to create an environment. For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/get-started.html\">Get started with Amazon Managed Workflows for Apache Airflow</a>.</p>"}, "CreateEnvironmentOutput": {"type": "structure", "members": {"Arn": {"shape": "EnvironmentArn", "documentation": "<p>The Amazon Resource Name (ARN) returned in the response for the environment.</p>"}}}, "CreateWebLoginTokenRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "EnvironmentName", "documentation": "<p>The name of the Amazon MWAA environment. For example, <code>MyMWAAEnvironment</code>.</p>", "location": "uri", "locationName": "Name"}}}, "CreateWebLoginTokenResponse": {"type": "structure", "members": {"WebToken": {"shape": "Token", "documentation": "<p>An Airflow web server login token.</p>"}, "WebServerHostname": {"shape": "Hostname", "documentation": "<p>The Airflow web server hostname for the environment.</p>"}, "IamIdentity": {"shape": "IamIdentity", "documentation": "<p>The name of the IAM identity creating the web login token. This might be an IAM user, or an assumed or federated identity. For example, <code>assumed-role/Admin/your-name</code>.</p>"}, "AirflowIdentity": {"shape": "AirflowIdentity", "documentation": "<p>The user name of the Apache Airflow identity creating the web login token.</p>"}}}, "CreatedAt": {"type": "timestamp"}, "DeleteEnvironmentInput": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "EnvironmentName", "documentation": "<p>The name of the Amazon MWAA environment. For example, <code>MyMWAAEnvironment</code>.</p>", "location": "uri", "locationName": "Name"}}}, "DeleteEnvironmentOutput": {"type": "structure", "members": {}}, "Dimension": {"type": "structure", "required": ["Name", "Value"], "members": {"Name": {"shape": "String", "documentation": "<p> <b>Internal only</b>. The name of the dimension.</p>"}, "Value": {"shape": "String", "documentation": "<p> <b>Internal only</b>. The value of the dimension.</p>"}}, "documentation": "<p> <b>Internal only</b>. Represents the dimensions of a metric. To learn more about the metrics published to Amazon CloudWatch, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/cw-metrics.html\">Amazon MWAA performance metrics in Amazon CloudWatch</a>.</p>", "deprecated": true, "deprecatedMessage": "This type is for internal use and not meant for public use. Data set for this type will be ignored."}, "Dimensions": {"type": "list", "member": {"shape": "Dimension"}, "deprecated": true, "deprecatedMessage": "This type is for internal use and not meant for public use. Data set for this type will be ignored."}, "Document": {"type": "structure", "members": {}, "document": true}, "Double": {"type": "double", "box": true}, "EndpointManagement": {"type": "string", "enum": ["CUSTOMER", "SERVICE"]}, "Environment": {"type": "structure", "members": {"Name": {"shape": "EnvironmentName", "documentation": "<p>The name of the Amazon MWAA environment. For example, <code>MyMWAAEnvironment</code>.</p>"}, "Status": {"shape": "EnvironmentStatus", "documentation": "<p>The status of the Amazon MWAA environment.</p> <p>Valid values:</p> <ul> <li> <p> <code>CREATING</code> - Indicates the request to create the environment is in progress.</p> </li> <li> <p> <code>CREATING_SNAPSHOT</code> - Indicates the request to update environment details, or upgrade the environment version, is in progress and Amazon MWAA is creating a storage volume snapshot of the Amazon RDS database cluster associated with the environment. A database snapshot is a backup created at a specific point in time. Amazon MWAA uses snapshots to recover environment metadata if the process to update or upgrade an environment fails.</p> </li> <li> <p> <code>CREATE_FAILED</code> - Indicates the request to create the environment failed, and the environment could not be created.</p> </li> <li> <p> <code>AVAILABLE</code> - Indicates the request was successful and the environment is ready to use.</p> </li> <li> <p> <code>PENDING</code> - Indicates the request was successful, but the process to create the environment is paused until you create the required VPC endpoints in your VPC. After you create the VPC endpoints, the process resumes.</p> </li> <li> <p> <code>UPDATING</code> - Indicates the request to update the environment is in progress.</p> </li> <li> <p> <code>ROLLING_BACK</code> - Indicates the request to update environment details, or upgrade the environment version, failed and Amazon MWAA is restoring the environment using the latest storage volume snapshot.</p> </li> <li> <p> <code>DELETING</code> - Indicates the request to delete the environment is in progress.</p> </li> <li> <p> <code>DELETED</code> - Indicates the request to delete the environment is complete, and the environment has been deleted.</p> </li> <li> <p> <code>UNAVAILABLE</code> - Indicates the request failed, but the environment did not return to its previous state and is not stable.</p> </li> <li> <p> <code>UPDATE_FAILED</code> - Indicates the request to update the environment failed, and the environment was restored to its previous state successfully and is ready to use.</p> </li> <li> <p> <code>MAINTENANCE</code> - Indicates that the environment is undergoing maintenance. Depending on the type of work Amazon MWAA is performing, your environment might become unavailable during this process. After all operations are done, your environment will return to its status prior to mainteneace operations. </p> </li> </ul> <p>We recommend reviewing our troubleshooting guide for a list of common errors and their solutions. For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/troubleshooting.html\">Amazon MWAA troubleshooting</a>.</p>"}, "Arn": {"shape": "EnvironmentArn", "documentation": "<p>The Amazon Resource Name (ARN) of the Amazon MWAA environment.</p>"}, "CreatedAt": {"shape": "CreatedAt", "documentation": "<p>The day and time the environment was created.</p>"}, "WebserverUrl": {"shape": "WebserverUrl", "documentation": "<p>The Apache Airflow <i>web server</i> host name for the Amazon MWAA environment. For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/access-airflow-ui.html\">Accessing the Apache Airflow UI</a>.</p>"}, "ExecutionRoleArn": {"shape": "IamRoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the execution role in IAM that allows MWAA to access Amazon Web Services resources in your environment. For example, <code>arn:aws:iam::123456789:role/my-execution-role</code>. For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/mwaa-create-role.html\">Amazon MWAA Execution role</a>.</p>"}, "ServiceRoleArn": {"shape": "IamRoleArn", "documentation": "<p>The Amazon Resource Name (ARN) for the service-linked role of the environment. For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/mwaa-slr.html\">Amazon MWAA Service-linked role</a>.</p>"}, "KmsKey": {"shape": "KmsKey", "documentation": "<p>The KMS encryption key used to encrypt the data in your environment.</p>"}, "AirflowVersion": {"shape": "AirflowVersion", "documentation": "<p>The Apache Airflow version on your environment.</p> <p>Valid values: <code>1.10.12</code>, <code>2.0.2</code>, <code>2.2.2</code>, <code>2.4.3</code>, <code>2.5.1</code>, <code>2.6.3</code>, <code>2.7.2</code>, <code>2.8.1</code>, <code>2.9.2</code>, <code>2.10.1</code>, and <code>2.10.3</code>.</p>"}, "SourceBucketArn": {"shape": "S3BucketArn", "documentation": "<p>The Amazon Resource Name (ARN) of the Amazon S3 bucket where your DAG code and supporting files are stored. For example, <code>arn:aws:s3:::my-airflow-bucket-unique-name</code>. For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/mwaa-s3-bucket.html\">Create an Amazon S3 bucket for Amazon MWAA</a>.</p>"}, "DagS3Path": {"shape": "Re<PERSON><PERSON><PERSON>", "documentation": "<p>The relative path to the DAGs folder in your Amazon S3 bucket. For example, <code>s3://mwaa-environment/dags</code>. For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/configuring-dag-folder.html\">Adding or updating DAGs</a>.</p>"}, "PluginsS3Path": {"shape": "Re<PERSON><PERSON><PERSON>", "documentation": "<p>The relative path to the file in your Amazon S3 bucket. For example, <code>s3://mwaa-environment/plugins.zip</code>. For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/configuring-dag-import-plugins.html\">Installing custom plugins</a>.</p>"}, "PluginsS3ObjectVersion": {"shape": "S3ObjectVersion", "documentation": "<p>The version of the <code>plugins.zip</code> file in your Amazon S3 bucket. You must specify the <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/versioning-workflows.html\">version ID</a> that Amazon S3 assigns to the file.</p> <p> Version IDs are Unicode, UTF-8 encoded, URL-ready, opaque strings that are no more than 1,024 bytes long. The following is an example: </p> <p> <code>3sL4kqtJlcpXroDTDmJ+rmSpXd3dIbrHY+MTRCxf3vjVBH40Nr8X8gdRQBpUMLUo</code> </p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/configuring-dag-import-plugins.html\">Installing custom plugins</a>.</p>"}, "RequirementsS3Path": {"shape": "Re<PERSON><PERSON><PERSON>", "documentation": "<p>The relative path to the <code>requirements.txt</code> file in your Amazon S3 bucket. For example, <code>s3://mwaa-environment/requirements.txt</code>. For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/working-dags-dependencies.html\">Installing Python dependencies</a>.</p>"}, "RequirementsS3ObjectVersion": {"shape": "S3ObjectVersion", "documentation": "<p>The version of the <code>requirements.txt </code> file on your Amazon S3 bucket. You must specify the <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/versioning-workflows.html\">version ID</a> that Amazon S3 assigns to the file.</p> <p> Version IDs are Unicode, UTF-8 encoded, URL-ready, opaque strings that are no more than 1,024 bytes long. The following is an example: </p> <p> <code>3sL4kqtJlcpXroDTDmJ+rmSpXd3dIbrHY+MTRCxf3vjVBH40Nr8X8gdRQBpUMLUo</code> </p> <p> For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/working-dags-dependencies.html\">Installing Python dependencies</a>. </p>"}, "StartupScriptS3Path": {"shape": "String", "documentation": "<p>The relative path to the startup shell script in your Amazon S3 bucket. For example, <code>s3://mwaa-environment/startup.sh</code>.</p> <p> Amazon MWAA runs the script as your environment starts, and before running the Apache Airflow process. You can use this script to install dependencies, modify Apache Airflow configuration options, and set environment variables. For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/using-startup-script.html\">Using a startup script</a>. </p>"}, "StartupScriptS3ObjectVersion": {"shape": "String", "documentation": "<p>The version of the startup shell script in your Amazon S3 bucket. You must specify the <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/versioning-workflows.html\">version ID</a> that Amazon S3 assigns to the file.</p> <p> Version IDs are Unicode, UTF-8 encoded, URL-ready, opaque strings that are no more than 1,024 bytes long. The following is an example: </p> <p> <code>3sL4kqtJlcpXroDTDmJ+rmSpXd3dIbrHY+MTRCxf3vjVBH40Nr8X8gdRQBpUMLUo</code> </p> <p> For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/using-startup-script.html\">Using a startup script</a>. </p>"}, "AirflowConfigurationOptions": {"shape": "AirflowConfigurationOptions", "documentation": "<p>A list of key-value pairs containing the Apache Airflow configuration options attached to your environment. For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/configuring-env-variables.html\">Apache Airflow configuration options</a>.</p>"}, "EnvironmentClass": {"shape": "EnvironmentClass", "documentation": "<p>The environment class type. Valid values: <code>mw1.micro</code>, <code>mw1.small</code>, <code>mw1.medium</code>, <code>mw1.large</code>, <code>mw1.xlarge</code>, and <code>mw1.2xlarge</code>. For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/environment-class.html\">Amazon MWAA environment class</a>.</p>"}, "MaxWorkers": {"shape": "MaxWorkers", "documentation": "<p>The maximum number of workers that run in your environment. For example, <code>20</code>.</p>"}, "NetworkConfiguration": {"shape": "NetworkConfiguration", "documentation": "<p>Describes the VPC networking components used to secure and enable network traffic between the Amazon Web Services resources for your environment. For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/networking-about.html\">About networking on Amazon MWAA</a>.</p>"}, "LoggingConfiguration": {"shape": "LoggingConfiguration", "documentation": "<p>The Apache Airflow logs published to CloudWatch Logs.</p>"}, "LastUpdate": {"shape": "LastUpdate", "documentation": "<p>The status of the last update on the environment.</p>"}, "WeeklyMaintenanceWindowStart": {"shape": "WeeklyMaintenanceWindowStart", "documentation": "<p>The day and time of the week in Coordinated Universal Time (UTC) 24-hour standard time that weekly maintenance updates are scheduled. For example: <code>TUE:03:30</code>.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>The key-value tag pairs associated to your environment. For example, <code>\"Environment\": \"Staging\"</code>. For more information, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws_tagging.html\">Tagging Amazon Web Services resources</a>.</p>"}, "WebserverAccessMode": {"shape": "WebserverAccessMode", "documentation": "<p>The Apache Airflow <i>web server</i> access mode. For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/configuring-networking.html\">Apache Airflow access modes</a>.</p>"}, "MinWorkers": {"shape": "MinWorkers", "documentation": "<p>The minimum number of workers that run in your environment. For example, <code>2</code>.</p>"}, "Schedulers": {"shape": "Schedulers", "documentation": "<p>The number of Apache Airflow schedulers that run in your Amazon MWAA environment.</p>"}, "WebserverVpcEndpointService": {"shape": "VpcEndpointServiceName", "documentation": "<p>The VPC endpoint for the environment's web server.</p>"}, "DatabaseVpcEndpointService": {"shape": "VpcEndpointServiceName", "documentation": "<p>The VPC endpoint for the environment's Amazon RDS database.</p>"}, "CeleryExecutorQueue": {"shape": "CeleryExecutorQueue", "documentation": "<p>The queue ARN for the environment's <a href=\"https://airflow.apache.org/docs/apache-airflow/stable/core-concepts/executor/celery.html\">Celery Executor</a>. Amazon MWAA uses a Celery Executor to distribute tasks across multiple workers. When you create an environment in a shared VPC, you must provide access to the Celery Executor queue from your VPC.</p>"}, "EndpointManagement": {"shape": "EndpointManagement", "documentation": "<p>Defines whether the VPC endpoints configured for the environment are created, and managed, by the customer or by Amazon MWAA. If set to <code>SERVICE</code>, Amazon MWAA will create and manage the required VPC endpoints in your VPC. If set to <code>CUSTOMER</code>, you must create, and manage, the VPC endpoints in your VPC.</p>"}, "MinWebservers": {"shape": "MinWebservers", "documentation": "<p> The minimum number of web servers that you want to run in your environment. Amazon MWAA scales the number of Apache Airflow web servers up to the number you specify for <code>MaxWebservers</code> when you interact with your Apache Airflow environment using Apache Airflow REST API, or the Apache Airflow CLI. As the transaction-per-second rate, and the network load, decrease, Amazon MWAA disposes of the additional web servers, and scales down to the number set in <code>MinxWebserers</code>. </p> <p>Valid values: For environments larger than mw1.micro, accepts values from <code>2</code> to <code>5</code>. Defaults to <code>2</code> for all environment sizes except mw1.micro, which defaults to <code>1</code>.</p>"}, "MaxWebservers": {"shape": "MaxWebservers", "documentation": "<p> The maximum number of web servers that you want to run in your environment. Amazon MWAA scales the number of Apache Airflow web servers up to the number you specify for <code>MaxWebservers</code> when you interact with your Apache Airflow environment using Apache Airflow REST API, or the Apache Airflow CLI. For example, in scenarios where your workload requires network calls to the Apache Airflow REST API with a high transaction-per-second (TPS) rate, Amazon MWAA will increase the number of web servers up to the number set in <code>MaxWebserers</code>. As TPS rates decrease Amazon MWAA disposes of the additional web servers, and scales down to the number set in <code>MinxWebserers</code>. </p> <p>Valid values: For environments larger than mw1.micro, accepts values from <code>2</code> to <code>5</code>. Defaults to <code>2</code> for all environment sizes except mw1.micro, which defaults to <code>1</code>.</p>"}}, "documentation": "<p>Describes an Amazon Managed Workflows for Apache Airflow (MWAA) environment.</p>"}, "EnvironmentArn": {"type": "string", "max": 1224, "min": 1, "pattern": "arn:aws(-[a-z]+)?:airflow:[a-z0-9\\-]+:\\d{12}:environment/\\w+.*"}, "EnvironmentClass": {"type": "string", "max": 1024, "min": 1}, "EnvironmentList": {"type": "list", "member": {"shape": "EnvironmentName"}}, "EnvironmentName": {"type": "string", "max": 80, "min": 1, "pattern": "[a-zA-Z][0-9a-zA-Z-_]*"}, "EnvironmentStatus": {"type": "string", "enum": ["CREATING", "CREATE_FAILED", "AVAILABLE", "UPDATING", "DELETING", "DELETED", "UNAVAILABLE", "UPDATE_FAILED", "ROLLING_BACK", "CREATING_SNAPSHOT", "PENDING", "MAINTENANCE"]}, "ErrorCode": {"type": "string"}, "ErrorMessage": {"type": "string", "max": 1024, "min": 1, "pattern": ".+"}, "GetEnvironmentInput": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "EnvironmentName", "documentation": "<p>The name of the Amazon MWAA environment. For example, <code>MyMWAAEnvironment</code>.</p>", "location": "uri", "locationName": "Name"}}}, "GetEnvironmentOutput": {"type": "structure", "members": {"Environment": {"shape": "Environment", "documentation": "<p>An object containing all available details about the environment.</p>"}}}, "Hostname": {"type": "string", "max": 255, "min": 1, "pattern": "(([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9\\-]*[a-zA-Z0-9])\\.)*([A-Za-z0-9]|[A-Za-z0-9][A-Za-z0-9\\-]*[A-Za-z0-9])"}, "IamIdentity": {"type": "string"}, "IamRoleArn": {"type": "string", "max": 1224, "min": 1, "pattern": "arn:aws(-[a-z]+)?:iam::\\d{12}:role/?[a-zA-Z_0-9+=,.@\\-_/]+"}, "Integer": {"type": "integer", "box": true}, "InternalServerException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>InternalServerException: An internal error has occurred.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true}, "InvokeRestApiRequest": {"type": "structure", "required": ["Name", "Path", "Method"], "members": {"Name": {"shape": "EnvironmentName", "documentation": "<p>The name of the Amazon MWAA environment. For example, <code>MyMWAAEnvironment</code>.</p>", "location": "uri", "locationName": "Name"}, "Path": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The Apache Airflow REST API endpoint path to be called. For example, <code>/dags/123456/clearTaskInstances</code>. For more information, see <a href=\"https://airflow.apache.org/docs/apache-airflow/stable/stable-rest-api-ref.html\">Apache Airflow API</a> </p>"}, "Method": {"shape": "RestApiMethod", "documentation": "<p>The HTTP method used for making Airflow REST API calls. For example, <code>POST</code>. </p>"}, "QueryParameters": {"shape": "Document", "documentation": "<p>Query parameters to be included in the Apache Airflow REST API call, provided as a JSON object. </p>"}, "Body": {"shape": "RestApiRequestBody", "documentation": "<p>The request body for the Apache Airflow REST API call, provided as a JSON object.</p>"}}}, "InvokeRestApiResponse": {"type": "structure", "members": {"RestApiStatusCode": {"shape": "Integer", "documentation": "<p>The HTTP status code returned by the Apache Airflow REST API call.</p>"}, "RestApiResponse": {"shape": "RestApiResponse", "documentation": "<p>The response data from the Apache Airflow REST API call, provided as a JSON object.</p>"}}}, "KmsKey": {"type": "string", "max": 1224, "min": 1, "pattern": "(((arn:aws(-[a-z]+)?:kms:[a-z]{2}-[a-z]+-\\d:\\d+:)?key\\/)?[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}|(arn:aws(-[a-z]+)?:kms:[a-z]{2}-[a-z]+-\\d:\\d+:)?alias/.+)"}, "LastUpdate": {"type": "structure", "members": {"Status": {"shape": "UpdateStatus", "documentation": "<p>The status of the last update on the environment.</p>"}, "CreatedAt": {"shape": "UpdateCreatedAt", "documentation": "<p>The day and time of the last update on the environment.</p>"}, "Error": {"shape": "UpdateError", "documentation": "<p>The error that was encountered during the last update of the environment.</p>"}, "Source": {"shape": "UpdateSource", "documentation": "<p>The source of the last update to the environment. Includes internal processes by Amazon MWAA, such as an environment maintenance update.</p>"}, "WorkerReplacementStrategy": {"shape": "WorkerReplacementStrategy", "documentation": "<p>The worker replacement strategy used in the last update of the environment.</p>"}}, "documentation": "<p>Describes the status of the last update on the environment, and any errors that were encountered.</p>"}, "ListEnvironmentsInput": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>Retrieves the next page of the results.</p>", "location": "querystring", "locationName": "NextToken"}, "MaxResults": {"shape": "ListEnvironmentsInputMaxResultsInteger", "documentation": "<p>The maximum number of results to retrieve per page. For example, <code>5</code> environments per page.</p>", "location": "querystring", "locationName": "MaxResults"}}}, "ListEnvironmentsInputMaxResultsInteger": {"type": "integer", "box": true, "max": 25, "min": 1}, "ListEnvironmentsOutput": {"type": "structure", "required": ["Environments"], "members": {"Environments": {"shape": "EnvironmentList", "documentation": "<p>Returns a list of Amazon MWAA environments.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>Retrieves the next page of the results.</p>"}}}, "ListTagsForResourceInput": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "EnvironmentArn", "documentation": "<p>The Amazon Resource Name (ARN) of the Amazon MWAA environment. For example, <code>arn:aws:airflow:us-east-1:123456789012:environment/MyMWAAEnvironment</code>.</p>", "location": "uri", "locationName": "ResourceArn"}}}, "ListTagsForResourceOutput": {"type": "structure", "members": {"Tags": {"shape": "TagMap", "documentation": "<p>The key-value tag pairs associated to your environment. For more information, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws_tagging.html\">Tagging Amazon Web Services resources</a>.</p>"}}}, "LoggingConfiguration": {"type": "structure", "members": {"DagProcessingLogs": {"shape": "ModuleLoggingConfiguration", "documentation": "<p>The Airflow DAG processing logs published to CloudWatch Logs and the log level.</p>"}, "SchedulerLogs": {"shape": "ModuleLoggingConfiguration", "documentation": "<p>The Airflow scheduler logs published to CloudWatch Logs and the log level.</p>"}, "WebserverLogs": {"shape": "ModuleLoggingConfiguration", "documentation": "<p>The Airflow web server logs published to CloudWatch Logs and the log level.</p>"}, "WorkerLogs": {"shape": "ModuleLoggingConfiguration", "documentation": "<p>The Airflow worker logs published to CloudWatch Logs and the log level.</p>"}, "TaskLogs": {"shape": "ModuleLoggingConfiguration", "documentation": "<p>The Airflow task logs published to CloudWatch Logs and the log level.</p>"}}, "documentation": "<p>Describes the Apache Airflow log types that are published to CloudWatch Logs.</p>"}, "LoggingConfigurationInput": {"type": "structure", "members": {"DagProcessingLogs": {"shape": "ModuleLoggingConfigurationInput", "documentation": "<p>Publishes Airflow DAG processing logs to CloudWatch Logs.</p>"}, "SchedulerLogs": {"shape": "ModuleLoggingConfigurationInput", "documentation": "<p>Publishes Airflow scheduler logs to CloudWatch Logs.</p>"}, "WebserverLogs": {"shape": "ModuleLoggingConfigurationInput", "documentation": "<p>Publishes Airflow web server logs to CloudWatch Logs.</p>"}, "WorkerLogs": {"shape": "ModuleLoggingConfigurationInput", "documentation": "<p>Publishes Airflow worker logs to CloudWatch Logs.</p>"}, "TaskLogs": {"shape": "ModuleLoggingConfigurationInput", "documentation": "<p>Publishes Airflow task logs to CloudWatch Logs.</p>"}}, "documentation": "<p>Defines the Apache Airflow log types to send to CloudWatch Logs.</p>"}, "LoggingEnabled": {"type": "boolean", "box": true}, "LoggingLevel": {"type": "string", "enum": ["CRITICAL", "ERROR", "WARNING", "INFO", "DEBUG"]}, "MaxWebservers": {"type": "integer", "box": true, "min": 1}, "MaxWorkers": {"type": "integer", "box": true, "min": 1}, "MetricData": {"type": "list", "member": {"shape": "MetricDatum"}, "deprecated": true, "deprecatedMessage": "This type is for internal use and not meant for public use. Data set for this type will be ignored."}, "MetricDatum": {"type": "structure", "required": ["MetricName", "Timestamp"], "members": {"MetricName": {"shape": "String", "documentation": "<p> <b>Internal only</b>. The name of the metric.</p>"}, "Timestamp": {"shape": "Timestamp", "documentation": "<p> <b>Internal only</b>. The time the metric data was received.</p>"}, "Dimensions": {"shape": "Dimensions", "documentation": "<p> <b>Internal only</b>. The dimensions associated with the metric.</p>"}, "Value": {"shape": "Double", "documentation": "<p> <b>Internal only</b>. The value for the metric.</p>"}, "Unit": {"shape": "Unit", "documentation": "<p> <b>Internal only</b>. The unit used to store the metric.</p>"}, "StatisticValues": {"shape": "StatisticSet", "documentation": "<p> <b>Internal only</b>. The statistical values for the metric.</p>"}}, "documentation": "<p> <b>Internal only</b>. Collects Apache Airflow metrics. To learn more about the metrics published to Amazon CloudWatch, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/cw-metrics.html\">Amazon MWAA performance metrics in Amazon CloudWatch</a>.</p>", "deprecated": true, "deprecatedMessage": "This type is for internal use and not meant for public use. Data set for this type will be ignored."}, "MinWebservers": {"type": "integer", "box": true, "min": 1}, "MinWorkers": {"type": "integer", "box": true, "min": 1}, "ModuleLoggingConfiguration": {"type": "structure", "members": {"Enabled": {"shape": "LoggingEnabled", "documentation": "<p>Indicates whether the Apache Airflow log type (e.g. <code>DagProcessingLogs</code>) is enabled.</p>"}, "LogLevel": {"shape": "LoggingLevel", "documentation": "<p>The Apache Airflow log level for the log type (e.g. <code>DagProcessingLogs</code>). </p>"}, "CloudWatchLogGroupArn": {"shape": "CloudWatchLogGroupArn", "documentation": "<p>The Amazon Resource Name (ARN) for the CloudWatch Logs group where the Apache Airflow log type (e.g. <code>DagProcessingLogs</code>) is published. For example, <code>arn:aws:logs:us-east-1:123456789012:log-group:airflow-MyMWAAEnvironment-MwaaEnvironment-DAGProcessing:*</code>.</p>"}}, "documentation": "<p>Describes the Apache Airflow log details for the log type (e.g. <code>DagProcessingLogs</code>).</p>"}, "ModuleLoggingConfigurationInput": {"type": "structure", "required": ["Enabled", "LogLevel"], "members": {"Enabled": {"shape": "LoggingEnabled", "documentation": "<p>Indicates whether to enable the Apache Airflow log type (e.g. <code>DagProcessingLogs</code>).</p>"}, "LogLevel": {"shape": "LoggingLevel", "documentation": "<p>Defines the Apache Airflow log level (e.g. <code>INFO</code>) to send to CloudWatch Logs.</p>"}}, "documentation": "<p>Enables the Apache Airflow log type (e.g. <code>DagProcessingLogs</code>) and defines the log level to send to CloudWatch Logs (e.g. <code>INFO</code>).</p>"}, "NetworkConfiguration": {"type": "structure", "members": {"SubnetIds": {"shape": "SubnetList", "documentation": "<p>A list of subnet IDs. For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/networking-about.html\">About networking on Amazon MWAA</a>.</p>"}, "SecurityGroupIds": {"shape": "SecurityGroupList", "documentation": "<p>A list of security group IDs. For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/vpc-security.html\">Security in your VPC on Amazon MWAA</a>.</p>"}}, "documentation": "<p>Describes the VPC networking components used to secure and enable network traffic between the Amazon Web Services resources for your environment. For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/networking-about.html\">About networking on Amazon MWAA</a>.</p>"}, "NextToken": {"type": "string", "max": 2048, "min": 0}, "PublishMetricsInput": {"type": "structure", "required": ["EnvironmentName", "MetricData"], "members": {"EnvironmentName": {"shape": "EnvironmentName", "documentation": "<p> <b>Internal only</b>. The name of the environment.</p>", "location": "uri", "locationName": "EnvironmentName"}, "MetricData": {"shape": "MetricData", "documentation": "<p> <b>Internal only</b>. Publishes metrics to Amazon CloudWatch. To learn more about the metrics published to Amazon CloudWatch, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/cw-metrics.html\">Amazon MWAA performance metrics in Amazon CloudWatch</a>.</p>"}}, "deprecated": true, "deprecatedMessage": "This type is for internal use and not meant for public use. Data set for this type will be ignored."}, "PublishMetricsOutput": {"type": "structure", "members": {}, "deprecated": true, "deprecatedMessage": "This type is for internal use and not meant for public use. Data set for this type will be ignored."}, "RelativePath": {"type": "string", "max": 1024, "min": 1, "pattern": ".*"}, "ResourceNotFoundException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>ResourceNotFoundException: The resource is not available.</p>", "error": {"httpStatusCode": 404, "senderFault": true}, "exception": true}, "RestApiClientException": {"type": "structure", "members": {"RestApiStatusCode": {"shape": "Integer", "documentation": "<p>The HTTP status code returned by the Apache Airflow REST API call.</p>"}, "RestApiResponse": {"shape": "RestApiResponse", "documentation": "<p>The error response data from the Apache Airflow REST API call, provided as a JSON object.</p>"}}, "documentation": "<p>An exception indicating that a client-side error occurred during the Apache Airflow REST API call.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "RestApiMethod": {"type": "string", "enum": ["GET", "PUT", "POST", "PATCH", "DELETE"]}, "RestApiPath": {"type": "string", "max": 64, "min": 1}, "RestApiRequestBody": {"type": "structure", "members": {}, "document": true, "sensitive": true}, "RestApiResponse": {"type": "structure", "members": {}, "document": true, "sensitive": true}, "RestApiServerException": {"type": "structure", "members": {"RestApiStatusCode": {"shape": "Integer", "documentation": "<p>The HTTP status code returned by the Apache Airflow REST API call.</p>"}, "RestApiResponse": {"shape": "RestApiResponse", "documentation": "<p>The error response data from the Apache Airflow REST API call, provided as a JSON object.</p>"}}, "documentation": "<p>An exception indicating that a server-side error occurred during the Apache Airflow REST API call.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "S3BucketArn": {"type": "string", "max": 1224, "min": 1, "pattern": "arn:aws(-[a-z]+)?:s3:::[a-z0-9.\\-]+"}, "S3ObjectVersion": {"type": "string", "max": 1024, "min": 1}, "Schedulers": {"type": "integer", "box": true, "max": 5}, "SecurityGroupId": {"type": "string", "max": 1024, "min": 1, "pattern": "sg-[a-zA-Z0-9\\-._]+"}, "SecurityGroupList": {"type": "list", "member": {"shape": "SecurityGroupId"}, "max": 5, "min": 1}, "StatisticSet": {"type": "structure", "members": {"SampleCount": {"shape": "Integer", "documentation": "<p> <b>Internal only</b>. The number of samples used for the statistic set.</p>"}, "Sum": {"shape": "Double", "documentation": "<p> <b>Internal only</b>. The sum of values for the sample set.</p>"}, "Minimum": {"shape": "Double", "documentation": "<p> <b>Internal only</b>. The minimum value of the sample set.</p>"}, "Maximum": {"shape": "Double", "documentation": "<p> <b>Internal only</b>. The maximum value of the sample set.</p>"}}, "documentation": "<p> <b>Internal only</b>. Represents a set of statistics that describe a specific metric. To learn more about the metrics published to Amazon CloudWatch, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/cw-metrics.html\">Amazon MWAA performance metrics in Amazon CloudWatch</a>.</p>", "deprecated": true, "deprecatedMessage": "This type is for internal use and not meant for public use. Data set for this type will be ignored."}, "String": {"type": "string"}, "SubnetId": {"type": "string", "max": 1024, "min": 1, "pattern": "subnet-[a-zA-Z0-9\\-._]+"}, "SubnetList": {"type": "list", "member": {"shape": "SubnetId"}, "max": 2, "min": 2}, "TagKey": {"type": "string", "max": 128, "min": 1, "pattern": "([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)"}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 50, "min": 0}, "TagMap": {"type": "map", "key": {"shape": "TagKey"}, "value": {"shape": "TagValue"}, "max": 50, "min": 1}, "TagResourceInput": {"type": "structure", "required": ["ResourceArn", "Tags"], "members": {"ResourceArn": {"shape": "EnvironmentArn", "documentation": "<p>The Amazon Resource Name (ARN) of the Amazon MWAA environment. For example, <code>arn:aws:airflow:us-east-1:123456789012:environment/MyMWAAEnvironment</code>.</p>", "location": "uri", "locationName": "ResourceArn"}, "Tags": {"shape": "TagMap", "documentation": "<p>The key-value tag pairs you want to associate to your environment. For example, <code>\"Environment\": \"Staging\"</code>. For more information, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws_tagging.html\">Tagging Amazon Web Services resources</a>.</p>"}}}, "TagResourceOutput": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 1, "pattern": "([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)"}, "Timestamp": {"type": "timestamp"}, "Token": {"type": "string", "sensitive": true}, "Unit": {"type": "string", "enum": ["Seconds", "Microseconds", "Milliseconds", "Bytes", "Kilobytes", "Megabytes", "Gigabytes", "Terabytes", "Bits", "Kilobits", "Megabits", "Gigabits", "Terabits", "Percent", "Count", "Bytes/Second", "Kilobytes/Second", "Megabytes/Second", "Gigabytes/Second", "Terabytes/Second", "Bits/Second", "Kilobits/Second", "Megabits/Second", "Gigabits/Second", "Terabits/Second", "Count/Second", "None"]}, "UntagResourceInput": {"type": "structure", "required": ["ResourceArn", "tagKeys"], "members": {"ResourceArn": {"shape": "EnvironmentArn", "documentation": "<p>The Amazon Resource Name (ARN) of the Amazon MWAA environment. For example, <code>arn:aws:airflow:us-east-1:123456789012:environment/MyMWAAEnvironment</code>.</p>", "location": "uri", "locationName": "ResourceArn"}, "tagKeys": {"shape": "TagKeyList", "documentation": "<p>The key-value tag pair you want to remove. For example, <code>\"Environment\": \"Staging\"</code>. </p>", "location": "querystring", "locationName": "tagKeys"}}}, "UntagResourceOutput": {"type": "structure", "members": {}}, "UpdateCreatedAt": {"type": "timestamp"}, "UpdateEnvironmentInput": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "EnvironmentName", "documentation": "<p>The name of your Amazon MWAA environment. For example, <code>MyMWAAEnvironment</code>.</p>", "location": "uri", "locationName": "Name"}, "ExecutionRoleArn": {"shape": "IamRoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the execution role in IAM that allows MWAA to access Amazon Web Services resources in your environment. For example, <code>arn:aws:iam::123456789:role/my-execution-role</code>. For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/mwaa-create-role.html\">Amazon MWAA Execution role</a>.</p>"}, "AirflowConfigurationOptions": {"shape": "AirflowConfigurationOptions", "documentation": "<p>A list of key-value pairs containing the Apache Airflow configuration options you want to attach to your environment. For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/configuring-env-variables.html\">Apache Airflow configuration options</a>.</p>"}, "AirflowVersion": {"shape": "AirflowVersion", "documentation": "<p>The Apache Airflow version for your environment. To upgrade your environment, specify a newer version of Apache Airflow supported by Amazon MWAA.</p> <p>Before you upgrade an environment, make sure your requirements, DAGs, plugins, and other resources used in your workflows are compatible with the new Apache Airflow version. For more information about updating your resources, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/upgrading-environment.html\">Upgrading an Amazon MWAA environment</a>.</p> <p>Valid values: <code>1.10.12</code>, <code>2.0.2</code>, <code>2.2.2</code>, <code>2.4.3</code>, <code>2.5.1</code>, <code>2.6.3</code>, <code>2.7.2</code>, <code>2.8.1</code>, <code>2.9.2</code>, <code>2.10.1</code>, and <code>2.10.3</code>.</p>"}, "DagS3Path": {"shape": "Re<PERSON><PERSON><PERSON>", "documentation": "<p>The relative path to the DAGs folder on your Amazon S3 bucket. For example, <code>dags</code>. For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/configuring-dag-folder.html\">Adding or updating DAGs</a>.</p>"}, "EnvironmentClass": {"shape": "EnvironmentClass", "documentation": "<p>The environment class type. Valid values: <code>mw1.micro</code>, <code>mw1.small</code>, <code>mw1.medium</code>, <code>mw1.large</code>, <code>mw1.xlarge</code>, and <code>mw1.2xlarge</code>. For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/environment-class.html\">Amazon MWAA environment class</a>. </p>"}, "LoggingConfiguration": {"shape": "LoggingConfigurationInput", "documentation": "<p>The Apache Airflow log types to send to CloudWatch Logs.</p>"}, "MaxWorkers": {"shape": "MaxWorkers", "documentation": "<p>The maximum number of workers that you want to run in your environment. MWAA scales the number of Apache Airflow workers up to the number you specify in the <code>MaxWorkers</code> field. For example, <code>20</code>. When there are no more tasks running, and no more in the queue, MWAA disposes of the extra workers leaving the one worker that is included with your environment, or the number you specify in <code>MinWorkers</code>.</p>"}, "MinWorkers": {"shape": "MinWorkers", "documentation": "<p>The minimum number of workers that you want to run in your environment. MWAA scales the number of Apache Airflow workers up to the number you specify in the <code>MaxWorkers</code> field. When there are no more tasks running, and no more in the queue, MWAA disposes of the extra workers leaving the worker count you specify in the <code>MinWorkers</code> field. For example, <code>2</code>.</p>"}, "MaxWebservers": {"shape": "MaxWebservers", "documentation": "<p> The maximum number of web servers that you want to run in your environment. Amazon MWAA scales the number of Apache Airflow web servers up to the number you specify for <code>MaxWebservers</code> when you interact with your Apache Airflow environment using Apache Airflow REST API, or the Apache Airflow CLI. For example, in scenarios where your workload requires network calls to the Apache Airflow REST API with a high transaction-per-second (TPS) rate, Amazon MWAA will increase the number of web servers up to the number set in <code>MaxWebserers</code>. As TPS rates decrease Amazon MWAA disposes of the additional web servers, and scales down to the number set in <code>MinxWebserers</code>. </p> <p>Valid values: For environments larger than mw1.micro, accepts values from <code>2</code> to <code>5</code>. Defaults to <code>2</code> for all environment sizes except mw1.micro, which defaults to <code>1</code>.</p>"}, "MinWebservers": {"shape": "MinWebservers", "documentation": "<p> The minimum number of web servers that you want to run in your environment. Amazon MWAA scales the number of Apache Airflow web servers up to the number you specify for <code>MaxWebservers</code> when you interact with your Apache Airflow environment using Apache Airflow REST API, or the Apache Airflow CLI. As the transaction-per-second rate, and the network load, decrease, Amazon MWAA disposes of the additional web servers, and scales down to the number set in <code>MinxWebserers</code>. </p> <p>Valid values: For environments larger than mw1.micro, accepts values from <code>2</code> to <code>5</code>. Defaults to <code>2</code> for all environment sizes except mw1.micro, which defaults to <code>1</code>.</p>"}, "WorkerReplacementStrategy": {"shape": "WorkerReplacementStrategy", "documentation": "<p>The worker replacement strategy to use when updating the environment.</p> <p>You can select one of the following strategies:</p> <ul> <li> <p> <b>Forced -</b> Stops and replaces Apache Airflow workers without waiting for tasks to complete before an update.</p> </li> <li> <p> <b>Graceful -</b> Allows Apache Airflow workers to complete running tasks for up to 12 hours during an update before they're stopped and replaced.</p> </li> </ul>"}, "NetworkConfiguration": {"shape": "UpdateNetworkConfigurationInput", "documentation": "<p>The VPC networking components used to secure and enable network traffic between the Amazon Web Services resources for your environment. For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/networking-about.html\">About networking on Amazon MWAA</a>.</p>"}, "PluginsS3Path": {"shape": "Re<PERSON><PERSON><PERSON>", "documentation": "<p>The relative path to the <code>plugins.zip</code> file on your Amazon S3 bucket. For example, <code>plugins.zip</code>. If specified, then the plugins.zip version is required. For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/configuring-dag-import-plugins.html\">Installing custom plugins</a>.</p>"}, "PluginsS3ObjectVersion": {"shape": "S3ObjectVersion", "documentation": "<p>The version of the plugins.zip file on your Amazon S3 bucket. You must specify a version each time a <code>plugins.zip</code> file is updated. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/versioning-workflows.html\">How S3 Versioning works</a>.</p>"}, "RequirementsS3Path": {"shape": "Re<PERSON><PERSON><PERSON>", "documentation": "<p>The relative path to the <code>requirements.txt</code> file on your Amazon S3 bucket. For example, <code>requirements.txt</code>. If specified, then a file version is required. For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/working-dags-dependencies.html\">Installing Python dependencies</a>.</p>"}, "RequirementsS3ObjectVersion": {"shape": "S3ObjectVersion", "documentation": "<p>The version of the requirements.txt file on your Amazon S3 bucket. You must specify a version each time a <code>requirements.txt</code> file is updated. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/versioning-workflows.html\">How S3 Versioning works</a>.</p>"}, "Schedulers": {"shape": "Schedulers", "documentation": "<p>The number of Apache Airflow schedulers to run in your Amazon MWAA environment.</p>"}, "SourceBucketArn": {"shape": "S3BucketArn", "documentation": "<p>The Amazon Resource Name (ARN) of the Amazon S3 bucket where your DAG code and supporting files are stored. For example, <code>arn:aws:s3:::my-airflow-bucket-unique-name</code>. For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/mwaa-s3-bucket.html\">Create an Amazon S3 bucket for Amazon MWAA</a>.</p>"}, "StartupScriptS3Path": {"shape": "Re<PERSON><PERSON><PERSON>", "documentation": "<p>The relative path to the startup shell script in your Amazon S3 bucket. For example, <code>s3://mwaa-environment/startup.sh</code>.</p> <p> Amazon MWAA runs the script as your environment starts, and before running the Apache Airflow process. You can use this script to install dependencies, modify Apache Airflow configuration options, and set environment variables. For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/using-startup-script.html\">Using a startup script</a>. </p>"}, "StartupScriptS3ObjectVersion": {"shape": "S3ObjectVersion", "documentation": "<p> The version of the startup shell script in your Amazon S3 bucket. You must specify the <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/versioning-workflows.html\">version ID</a> that Amazon S3 assigns to the file every time you update the script. </p> <p> Version IDs are Unicode, UTF-8 encoded, URL-ready, opaque strings that are no more than 1,024 bytes long. The following is an example: </p> <p> <code>3sL4kqtJlcpXroDTDmJ+rmSpXd3dIbrHY+MTRCxf3vjVBH40Nr8X8gdRQBpUMLUo</code> </p> <p> For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/using-startup-script.html\">Using a startup script</a>. </p>"}, "WebserverAccessMode": {"shape": "WebserverAccessMode", "documentation": "<p>The Apache Airflow <i>Web server</i> access mode. For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/configuring-networking.html\">Apache Airflow access modes</a>.</p>"}, "WeeklyMaintenanceWindowStart": {"shape": "WeeklyMaintenanceWindowStart", "documentation": "<p>The day and time of the week in Coordinated Universal Time (UTC) 24-hour standard time to start weekly maintenance updates of your environment in the following format: <code>DAY:HH:MM</code>. For example: <code>TUE:03:30</code>. You can specify a start time in 30 minute increments only.</p>"}}}, "UpdateEnvironmentOutput": {"type": "structure", "members": {"Arn": {"shape": "EnvironmentArn", "documentation": "<p>The Amazon Resource Name (ARN) of the Amazon MWAA environment. For example, <code>arn:aws:airflow:us-east-1:123456789012:environment/MyMWAAEnvironment</code>.</p>"}}}, "UpdateError": {"type": "structure", "members": {"ErrorCode": {"shape": "ErrorCode", "documentation": "<p>The error code that corresponds to the error with the last update.</p>"}, "ErrorMessage": {"shape": "ErrorMessage", "documentation": "<p>The error message that corresponds to the error code.</p>"}}, "documentation": "<p>Describes the error(s) encountered with the last update of the environment.</p>"}, "UpdateNetworkConfigurationInput": {"type": "structure", "required": ["SecurityGroupIds"], "members": {"SecurityGroupIds": {"shape": "SecurityGroupList", "documentation": "<p>A list of security group IDs. A security group must be attached to the same VPC as the subnets. For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/vpc-security.html\">Security in your VPC on Amazon MWAA</a>.</p>"}}, "documentation": "<p>Defines the VPC networking components used to secure and enable network traffic between the Amazon Web Services resources for your environment. For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/networking-about.html\">About networking on Amazon MWAA</a>.</p>"}, "UpdateSource": {"type": "string", "max": 256, "min": 1, "pattern": ".+"}, "UpdateStatus": {"type": "string", "enum": ["SUCCESS", "PENDING", "FAILED"]}, "ValidationException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>ValidationException: The provided input is not valid.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "VpcEndpointServiceName": {"type": "string", "max": 1224, "min": 1, "pattern": "([a-z.-]+)?com\\.amazonaws\\.vpce\\.[a-z0-9\\-]+\\.[a-zA-Z_0-9+=,.@\\-_/]+"}, "WebserverAccessMode": {"type": "string", "enum": ["PRIVATE_ONLY", "PUBLIC_ONLY"]}, "WebserverUrl": {"type": "string", "max": 256, "min": 1, "pattern": "https://.+"}, "WeeklyMaintenanceWindowStart": {"type": "string", "max": 9, "min": 1, "pattern": ".*(MON|TUE|WED|THU|FRI|SAT|SUN):([01]\\d|2[0-3]):(00|30).*"}, "WorkerReplacementStrategy": {"type": "string", "enum": ["FORCED", "GRACEFUL"]}}, "documentation": "<p><fullname>Amazon Managed Workflows for Apache Airflow</fullname> <p>This section contains the Amazon Managed Workflows for Apache Airflow (MWAA) API reference documentation. For more information, see <a href=\"https://docs.aws.amazon.com/mwaa/latest/userguide/what-is-mwaa.html\">What is Amazon MWAA?</a>.</p> <p> <b>Endpoints</b> </p> <ul> <li> <p> <code>api.airflow.{region}.amazonaws.com</code> - This endpoint is used for environment management.</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/mwaa/latest/API/API_CreateEnvironment.html\">CreateEnvironment</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/mwaa/latest/API/API_DeleteEnvironment.html\">DeleteEnvironment</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/mwaa/latest/API/API_GetEnvironment.html\">GetEnvironment</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/mwaa/latest/API/API_ListEnvironments.html\">ListEnvironments</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/mwaa/latest/API/API_ListTagsForResource.html\">ListTagsForResource</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/mwaa/latest/API/API_TagResource.html\">TagResource</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/mwaa/latest/API/API_UntagResource.html\">UntagResource</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/mwaa/latest/API/API_UpdateEnvironment.html\">UpdateEnvironment</a> </p> </li> </ul> </li> <li> <p> <code>env.airflow.{region}.amazonaws.com</code> - This endpoint is used to operate the Airflow environment.</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/mwaa/latest/API/API_CreateCliToken.html \">CreateCliToken</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/mwaa/latest/API/API_CreateWebLoginToken.html\">CreateWebLoginToken</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/mwaa/latest/API/API_InvokeRestApi.html\">InvokeRestApi</a> </p> </li> </ul> </li> </ul> <p> <b>Regions</b> </p> <p>For a list of supported regions, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/mwaa.html\">Amazon MWAA endpoints and quotas</a> in the <i>Amazon Web Services General Reference</i>.</p></p>"}