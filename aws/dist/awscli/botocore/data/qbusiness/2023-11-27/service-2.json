{"version": "2.0", "metadata": {"apiVersion": "2023-11-27", "auth": ["aws.auth#sigv4"], "endpointPrefix": "qbusiness", "protocol": "rest-json", "protocolSettings": {"h2": "eventstream"}, "protocols": ["rest-json"], "serviceFullName": "QBusiness", "serviceId": "QBusiness", "signatureVersion": "v4", "signingName": "qbusiness", "uid": "qbusiness-2023-11-27"}, "operations": {"AssociatePermission": {"name": "AssociatePermission", "http": {"method": "POST", "requestUri": "/applications/{applicationId}/policy", "responseCode": 200}, "input": {"shape": "AssociatePermissionRequest"}, "output": {"shape": "AssociatePermissionResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Adds or updates a permission policy for a Amazon Q Business application, allowing cross-account access for an ISV. This operation creates a new policy statement for the specified Amazon Q Business application. The policy statement defines the IAM actions that the ISV is allowed to perform on the Amazon Q Business application's resources.</p>"}, "BatchDeleteDocument": {"name": "BatchDeleteDocument", "http": {"method": "POST", "requestUri": "/applications/{applicationId}/indices/{indexId}/documents/delete", "responseCode": 200}, "input": {"shape": "BatchDeleteDocumentRequest"}, "output": {"shape": "BatchDeleteDocumentResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Asynchronously deletes one or more documents added using the <code>BatchPutDocument</code> API from an Amazon Q Business index.</p> <p>You can see the progress of the deletion, and any error messages related to the process, by using CloudWatch.</p>"}, "BatchPutDocument": {"name": "BatchPutDocument", "http": {"method": "POST", "requestUri": "/applications/{applicationId}/indices/{indexId}/documents", "responseCode": 200}, "input": {"shape": "BatchPutDocumentRequest"}, "output": {"shape": "BatchPutDocumentResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Adds one or more documents to an Amazon Q Business index.</p> <p>You use this API to:</p> <ul> <li> <p>ingest your structured and unstructured documents and documents stored in an Amazon S3 bucket into an Amazon Q Business index.</p> </li> <li> <p>add custom attributes to documents in an Amazon Q Business index.</p> </li> <li> <p>attach an access control list to the documents added to an Amazon Q Business index.</p> </li> </ul> <p>You can see the progress of the deletion, and any error messages related to the process, by using CloudWatch.</p>"}, "CancelSubscription": {"name": "CancelSubscription", "http": {"method": "DELETE", "requestUri": "/applications/{applicationId}/subscriptions/{subscriptionId}", "responseCode": 200}, "input": {"shape": "CancelSubscriptionRequest"}, "output": {"shape": "CancelSubscriptionResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Unsubscribes a user or a group from their pricing tier in an Amazon Q Business application. An unsubscribed user or group loses all Amazon Q Business feature access at the start of next month. </p>", "idempotent": true}, "Chat": {"name": "Cha<PERSON>", "http": {"method": "POST", "requestUri": "/applications/{applicationId}/conversations", "responseCode": 200}, "input": {"shape": "ChatInput"}, "output": {"shape": "ChatOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "LicenseNotFoundException"}, {"shape": "ExternalResourceException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Starts or continues a streaming Amazon Q Business conversation.</p>"}, "ChatSync": {"name": "ChatSync", "http": {"method": "POST", "requestUri": "/applications/{applicationId}/conversations?sync", "responseCode": 200}, "input": {"shape": "ChatSyncInput"}, "output": {"shape": "ChatSyncOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "LicenseNotFoundException"}, {"shape": "ExternalResourceException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Starts or continues a non-streaming Amazon Q Business conversation.</p>"}, "CheckDocumentAccess": {"name": "CheckDocumentAccess", "http": {"method": "GET", "requestUri": "/applications/{applicationId}/index/{indexId}/users/{userId}/documents/{documentId}/check-document-access", "responseCode": 200}, "input": {"shape": "CheckDocumentAccessRequest"}, "output": {"shape": "CheckDocumentAccessResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Verifies if a user has access permissions for a specified document and returns the actual ACL attached to the document. Resolves user access on the document via user aliases and groups when verifying user access.</p>"}, "CreateAnonymousWebExperienceUrl": {"name": "CreateAnonymousWebExperienceUrl", "http": {"method": "POST", "requestUri": "/applications/{applicationId}/experiences/{webExperienceId}/anonymous-url", "responseCode": 200}, "input": {"shape": "CreateAnonymousWebExperienceUrlRequest"}, "output": {"shape": "CreateAnonymousWebExperienceUrlResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates a unique URL for anonymous Amazon Q Business web experience. This URL can only be used once and must be used within 5 minutes after it's generated.</p>"}, "CreateApplication": {"name": "CreateApplication", "http": {"method": "POST", "requestUri": "/applications", "responseCode": 200}, "input": {"shape": "CreateApplicationRequest"}, "output": {"shape": "CreateApplicationResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates an Amazon Q Business application.</p> <note> <p>There are new tiers for Amazon Q Business. Not all features in Amazon Q Business Pro are also available in Amazon Q Business Lite. For information on what's included in Amazon Q Business Lite and what's included in Amazon Q Business Pro, see <a href=\"https://docs.aws.amazon.com/amazonq/latest/qbusiness-ug/tiers.html#user-sub-tiers\">Amazon Q Business tiers</a>. You must use the Amazon Q Business console to assign subscription tiers to users. </p> <p>An Amazon Q Apps service linked role will be created if it's absent in the Amazon Web Services account when <code>QAppsConfiguration</code> is enabled in the request. For more information, see <a href=\"https://docs.aws.amazon.com/amazonq/latest/qbusiness-ug/using-service-linked-roles-qapps.html\"> Using service-linked roles for Q Apps</a>.</p> <p>When you create an application, Amazon Q Business may securely transmit data for processing from your selected Amazon Web Services region, but within your geography. For more information, see <a href=\"https://docs.aws.amazon.com/amazonq/latest/qbusiness-ug/cross-region-inference.html\">Cross region inference in Amazon Q Business</a>.</p> </note>", "idempotent": true}, "CreateChatResponseConfiguration": {"name": "CreateChatResponseConfiguration", "http": {"method": "POST", "requestUri": "/applications/{applicationId}/chatresponseconfigurations", "responseCode": 200}, "input": {"shape": "CreateChatResponseConfigurationRequest"}, "output": {"shape": "CreateChatResponseConfigurationResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates a new chat response configuration for an Amazon Q Business application. This operation establishes a set of parameters that define how the system generates and formats responses to user queries in chat interactions.</p>", "idempotent": true}, "CreateDataAccessor": {"name": "CreateDataAccessor", "http": {"method": "POST", "requestUri": "/applications/{applicationId}/dataaccessors", "responseCode": 200}, "input": {"shape": "CreateDataAccessorRequest"}, "output": {"shape": "CreateDataAccessorResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates a new data accessor for an ISV to access data from a Amazon Q Business application. The data accessor is an entity that represents the ISV's access to the Amazon Q Business application's data. It includes the IAM role ARN for the ISV, a friendly name, and a set of action configurations that define the specific actions the ISV is allowed to perform and any associated data filters. When the data accessor is created, an IAM Identity Center application is also created to manage the ISV's identity and authentication for accessing the Amazon Q Business application.</p>", "idempotent": true}, "CreateDataSource": {"name": "CreateDataSource", "http": {"method": "POST", "requestUri": "/applications/{applicationId}/indices/{indexId}/datasources", "responseCode": 200}, "input": {"shape": "CreateDataSourceRequest"}, "output": {"shape": "CreateDataSourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates a data source connector for an Amazon Q Business application.</p> <p> <code>CreateDataSource</code> is a synchronous operation. The operation returns 200 if the data source was successfully created. Otherwise, an exception is raised.</p>", "idempotent": true}, "CreateIndex": {"name": "CreateIndex", "http": {"method": "POST", "requestUri": "/applications/{applicationId}/indices", "responseCode": 200}, "input": {"shape": "CreateIndexRequest"}, "output": {"shape": "CreateIndexResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates an Amazon Q Business index.</p> <p>To determine if index creation has completed, check the <code>Status</code> field returned from a call to <code>DescribeIndex</code>. The <code>Status</code> field is set to <code>ACTIVE</code> when the index is ready to use.</p> <p>Once the index is active, you can index your documents using the <a href=\"https://docs.aws.amazon.com/amazonq/latest/api-reference/API_BatchPutDocument.html\"> <code>BatchPutDocument</code> </a> API or the <a href=\"https://docs.aws.amazon.com/amazonq/latest/api-reference/API_CreateDataSource.html\"> <code>CreateDataSource</code> </a> API.</p>"}, "CreatePlugin": {"name": "CreatePlugin", "http": {"method": "POST", "requestUri": "/applications/{applicationId}/plugins", "responseCode": 200}, "input": {"shape": "CreatePluginRequest"}, "output": {"shape": "CreatePluginResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates an Amazon Q Business plugin.</p>", "idempotent": true}, "CreateRetriever": {"name": "CreateRetriever", "http": {"method": "POST", "requestUri": "/applications/{applicationId}/retrievers", "responseCode": 200}, "input": {"shape": "CreateRetrieverRequest"}, "output": {"shape": "CreateRetrieverResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Adds a retriever to your Amazon Q Business application.</p>"}, "CreateSubscription": {"name": "CreateSubscription", "http": {"method": "POST", "requestUri": "/applications/{applicationId}/subscriptions", "responseCode": 200}, "input": {"shape": "CreateSubscriptionRequest"}, "output": {"shape": "CreateSubscriptionResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Subscribes an IAM Identity Center user or a group to a pricing tier for an Amazon Q Business application.</p> <p>Amazon Q Business offers two subscription tiers: <code>Q_LITE</code> and <code>Q_BUSINESS</code>. Subscription tier determines feature access for the user. For more information on subscriptions and pricing tiers, see <a href=\"https://aws.amazon.com/q/business/pricing/\">Amazon Q Business pricing</a>.</p>", "idempotent": true}, "CreateUser": {"name": "CreateUser", "http": {"method": "POST", "requestUri": "/applications/{applicationId}/users", "responseCode": 200}, "input": {"shape": "CreateUserRequest"}, "output": {"shape": "CreateUserResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates a universally unique identifier (UUID) mapped to a list of local user ids within an application.</p>", "idempotent": true}, "CreateWebExperience": {"name": "CreateWebExperience", "http": {"method": "POST", "requestUri": "/applications/{applicationId}/experiences", "responseCode": 200}, "input": {"shape": "CreateWebExperienceRequest"}, "output": {"shape": "CreateWebExperienceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates an Amazon Q Business web experience.</p>"}, "DeleteApplication": {"name": "DeleteApplication", "http": {"method": "DELETE", "requestUri": "/applications/{applicationId}", "responseCode": 200}, "input": {"shape": "DeleteApplicationRequest"}, "output": {"shape": "DeleteApplicationResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Deletes an Amazon Q Business application.</p>", "idempotent": true}, "DeleteAttachment": {"name": "DeleteAttachment", "http": {"method": "DELETE", "requestUri": "/applications/{applicationId}/conversations/{conversationId}/attachments/{attachmentId}", "responseCode": 200}, "input": {"shape": "DeleteAttachmentRequest"}, "output": {"shape": "DeleteAttachmentResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "LicenseNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Deletes an attachment associated with a specific Amazon Q Business conversation.</p>", "idempotent": true}, "DeleteChatControlsConfiguration": {"name": "DeleteChatControlsConfiguration", "http": {"method": "DELETE", "requestUri": "/applications/{applicationId}/chatcontrols", "responseCode": 200}, "input": {"shape": "DeleteChatControlsConfigurationRequest"}, "output": {"shape": "DeleteChatControlsConfigurationResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Deletes chat controls configured for an existing Amazon Q Business application.</p>", "idempotent": true}, "DeleteChatResponseConfiguration": {"name": "DeleteChatResponseConfiguration", "http": {"method": "DELETE", "requestUri": "/applications/{applicationId}/chatresponseconfigurations/{chatResponseConfigurationId}", "responseCode": 200}, "input": {"shape": "DeleteChatResponseConfigurationRequest"}, "output": {"shape": "DeleteChatResponseConfigurationResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Deletes a specified chat response configuration from an Amazon Q Business application.</p>", "idempotent": true}, "DeleteConversation": {"name": "DeleteConversation", "http": {"method": "DELETE", "requestUri": "/applications/{applicationId}/conversations/{conversationId}", "responseCode": 200}, "input": {"shape": "DeleteConversationRequest"}, "output": {"shape": "DeleteConversationResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "LicenseNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Deletes an Amazon Q Business web experience conversation.</p>", "idempotent": true}, "DeleteDataAccessor": {"name": "DeleteDataAccessor", "http": {"method": "DELETE", "requestUri": "/applications/{applicationId}/dataaccessors/{dataAccessorId}", "responseCode": 200}, "input": {"shape": "DeleteDataAccessorRequest"}, "output": {"shape": "DeleteDataAccessorResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Deletes a specified data accessor. This operation permanently removes the data accessor and its associated IAM Identity Center application. Any access granted to the ISV through this data accessor will be revoked.</p>", "idempotent": true}, "DeleteDataSource": {"name": "DeleteDataSource", "http": {"method": "DELETE", "requestUri": "/applications/{applicationId}/indices/{indexId}/datasources/{dataSourceId}", "responseCode": 200}, "input": {"shape": "DeleteDataSourceRequest"}, "output": {"shape": "DeleteDataSourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Deletes an Amazon Q Business data source connector. While the data source is being deleted, the <code>Status</code> field returned by a call to the <code>DescribeDataSource</code> API is set to <code>DELETING</code>. </p>", "idempotent": true}, "DeleteGroup": {"name": "DeleteGroup", "http": {"method": "DELETE", "requestUri": "/applications/{applicationId}/indices/{indexId}/groups/{groupName}", "responseCode": 200}, "input": {"shape": "DeleteGroupRequest"}, "output": {"shape": "DeleteGroupResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Deletes a group so that all users and sub groups that belong to the group can no longer access documents only available to that group. For example, after deleting the group \"Summer Interns\", all interns who belonged to that group no longer see intern-only documents in their chat results. </p> <p>If you want to delete, update, or replace users or sub groups of a group, you need to use the <code>PutGroup</code> operation. For example, if a user in the group \"Engineering\" leaves the engineering team and another user takes their place, you provide an updated list of users or sub groups that belong to the \"Engineering\" group when calling <code>PutGroup</code>.</p>", "idempotent": true}, "DeleteIndex": {"name": "DeleteIndex", "http": {"method": "DELETE", "requestUri": "/applications/{applicationId}/indices/{indexId}", "responseCode": 200}, "input": {"shape": "DeleteIndexRequest"}, "output": {"shape": "DeleteIndexResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Deletes an Amazon Q Business index.</p>", "idempotent": true}, "DeletePlugin": {"name": "DeletePlugin", "http": {"method": "DELETE", "requestUri": "/applications/{applicationId}/plugins/{pluginId}", "responseCode": 200}, "input": {"shape": "DeletePluginRequest"}, "output": {"shape": "DeletePluginResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Deletes an Amazon Q Business plugin.</p>", "idempotent": true}, "DeleteRetriever": {"name": "DeleteRetriever", "http": {"method": "DELETE", "requestUri": "/applications/{applicationId}/retrievers/{retrieverId}", "responseCode": 200}, "input": {"shape": "DeleteRetrieverRequest"}, "output": {"shape": "DeleteRetrieverResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Deletes the retriever used by an Amazon Q Business application.</p>", "idempotent": true}, "DeleteUser": {"name": "DeleteUser", "http": {"method": "DELETE", "requestUri": "/applications/{applicationId}/users/{userId}", "responseCode": 200}, "input": {"shape": "DeleteUserRequest"}, "output": {"shape": "DeleteUserResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Deletes a user by email id.</p>", "idempotent": true}, "DeleteWebExperience": {"name": "DeleteWebExperience", "http": {"method": "DELETE", "requestUri": "/applications/{applicationId}/experiences/{webExperienceId}", "responseCode": 200}, "input": {"shape": "DeleteWebExperienceRequest"}, "output": {"shape": "DeleteWebExperienceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Deletes an Amazon Q Business web experience.</p>", "idempotent": true}, "DisassociatePermission": {"name": "DisassociatePermission", "http": {"method": "DELETE", "requestUri": "/applications/{applicationId}/policy/{statementId}", "responseCode": 200}, "input": {"shape": "DisassociatePermissionRequest"}, "output": {"shape": "DisassociatePermissionResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Removes a permission policy from a Amazon Q Business application, revoking the cross-account access that was previously granted to an ISV. This operation deletes the specified policy statement from the application's permission policy.</p>", "idempotent": true}, "GetApplication": {"name": "GetApplication", "http": {"method": "GET", "requestUri": "/applications/{applicationId}", "responseCode": 200}, "input": {"shape": "GetApplicationRequest"}, "output": {"shape": "GetApplicationResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Gets information about an existing Amazon Q Business application.</p>"}, "GetChatControlsConfiguration": {"name": "GetChatControlsConfiguration", "http": {"method": "GET", "requestUri": "/applications/{applicationId}/chatcontrols", "responseCode": 200}, "input": {"shape": "GetChatControlsConfigurationRequest"}, "output": {"shape": "GetChatControlsConfigurationResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Gets information about chat controls configured for an existing Amazon Q Business application.</p>"}, "GetChatResponseConfiguration": {"name": "GetChatResponseConfiguration", "http": {"method": "GET", "requestUri": "/applications/{applicationId}/chatresponseconfigurations/{chatResponseConfigurationId}", "responseCode": 200}, "input": {"shape": "GetChatResponseConfigurationRequest"}, "output": {"shape": "GetChatResponseConfigurationResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves detailed information about a specific chat response configuration from an Amazon Q Business application. This operation returns the complete configuration settings and metadata.</p>"}, "GetDataAccessor": {"name": "GetDataAccessor", "http": {"method": "GET", "requestUri": "/applications/{applicationId}/dataaccessors/{dataAccessorId}", "responseCode": 200}, "input": {"shape": "GetDataAccessorRequest"}, "output": {"shape": "GetDataAccessorResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves information about a specified data accessor. This operation returns details about the data accessor, including its display name, unique identifier, Amazon Resource Name (ARN), the associated Amazon Q Business application and IAM Identity Center application, the IAM role for the ISV, the action configurations, and the timestamps for when the data accessor was created and last updated.</p>"}, "GetDataSource": {"name": "GetDataSource", "http": {"method": "GET", "requestUri": "/applications/{applicationId}/indices/{indexId}/datasources/{dataSourceId}", "responseCode": 200}, "input": {"shape": "GetDataSourceRequest"}, "output": {"shape": "GetDataSourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Gets information about an existing Amazon Q Business data source connector.</p>"}, "GetGroup": {"name": "GetGroup", "http": {"method": "GET", "requestUri": "/applications/{applicationId}/indices/{indexId}/groups/{groupName}", "responseCode": 200}, "input": {"shape": "GetGroupRequest"}, "output": {"shape": "GetGroupResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Describes a group by group name.</p>"}, "GetIndex": {"name": "GetIndex", "http": {"method": "GET", "requestUri": "/applications/{applicationId}/indices/{indexId}", "responseCode": 200}, "input": {"shape": "GetIndexRequest"}, "output": {"shape": "GetIndexResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Gets information about an existing Amazon Q Business index.</p>"}, "GetMedia": {"name": "GetMedia", "http": {"method": "GET", "requestUri": "/applications/{applicationId}/conversations/{conversationId}/messages/{messageId}/media/{mediaId}", "responseCode": 200}, "input": {"shape": "GetMediaRequest"}, "output": {"shape": "GetMediaResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "LicenseNotFoundException"}, {"shape": "MediaTooLargeException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Returns the image bytes corresponding to a media object. If you have implemented your own application with the Chat and ChatSync APIs, and have enabled content extraction from visual data in Amazon Q Business, you use the GetMedia API operation to download the images so you can show them in your UI with responses.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/amazonq/latest/qbusiness-ug/extracting-meaning-from-images.html\">Extracting semantic meaning from images and visuals</a>.</p>"}, "GetPlugin": {"name": "GetPlugin", "http": {"method": "GET", "requestUri": "/applications/{applicationId}/plugins/{pluginId}", "responseCode": 200}, "input": {"shape": "GetPluginRequest"}, "output": {"shape": "GetPluginResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Gets information about an existing Amazon Q Business plugin.</p>"}, "GetPolicy": {"name": "GetPolicy", "http": {"method": "GET", "requestUri": "/applications/{applicationId}/policy", "responseCode": 200}, "input": {"shape": "GetPolicyRequest"}, "output": {"shape": "GetPolicyResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves the current permission policy for a Amazon Q Business application. The policy is returned as a JSON-formatted string and defines the IAM actions that are allowed or denied for the application's resources.</p>"}, "GetRetriever": {"name": "GetRetriever", "http": {"method": "GET", "requestUri": "/applications/{applicationId}/retrievers/{retrieverId}", "responseCode": 200}, "input": {"shape": "GetRetrieverRequest"}, "output": {"shape": "GetRetrieverResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Gets information about an existing retriever used by an Amazon Q Business application.</p>"}, "GetUser": {"name": "GetUser", "http": {"method": "GET", "requestUri": "/applications/{applicationId}/users/{userId}", "responseCode": 200}, "input": {"shape": "GetUserRequest"}, "output": {"shape": "GetUserResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Describes the universally unique identifier (UUID) associated with a local user in a data source.</p>"}, "GetWebExperience": {"name": "GetWebExperience", "http": {"method": "GET", "requestUri": "/applications/{applicationId}/experiences/{webExperienceId}", "responseCode": 200}, "input": {"shape": "GetWebExperienceRequest"}, "output": {"shape": "GetWebExperienceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Gets information about an existing Amazon Q Business web experience.</p>"}, "ListApplications": {"name": "ListApplications", "http": {"method": "GET", "requestUri": "/applications", "responseCode": 200}, "input": {"shape": "ListApplicationsRequest"}, "output": {"shape": "ListApplicationsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists Amazon Q Business applications.</p> <note> <p>Amazon Q Business applications may securely transmit data for processing across Amazon Web Services Regions within your geography. For more information, see <a href=\"https://docs.aws.amazon.com/amazonq/latest/qbusiness-ug/cross-region-inference.html\">Cross region inference in Amazon Q Business</a>.</p> </note>"}, "ListAttachments": {"name": "ListAttachments", "http": {"method": "GET", "requestUri": "/applications/{applicationId}/attachments", "responseCode": 200}, "input": {"shape": "ListAttachmentsRequest"}, "output": {"shape": "ListAttachmentsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "LicenseNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Gets a list of attachments associated with an Amazon Q Business web experience or a list of attachements associated with a specific Amazon Q Business conversation.</p>"}, "ListChatResponseConfigurations": {"name": "ListChatResponseConfigurations", "http": {"method": "GET", "requestUri": "/applications/{applicationId}/chatresponseconfigurations", "responseCode": 200}, "input": {"shape": "ListChatResponseConfigurationsRequest"}, "output": {"shape": "ListChatResponseConfigurationsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves a list of all chat response configurations available in a specified Amazon Q Business application. This operation returns summary information about each configuration to help administrators manage and select appropriate response settings.</p>"}, "ListConversations": {"name": "ListConversations", "http": {"method": "GET", "requestUri": "/applications/{applicationId}/conversations", "responseCode": 200}, "input": {"shape": "ListConversationsRequest"}, "output": {"shape": "ListConversationsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "LicenseNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists one or more Amazon Q Business conversations.</p>"}, "ListDataAccessors": {"name": "ListDataAccessors", "http": {"method": "GET", "requestUri": "/applications/{applicationId}/dataaccessors", "responseCode": 200}, "input": {"shape": "ListDataAccessorsRequest"}, "output": {"shape": "ListDataAccessorsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists the data accessors for a Amazon Q Business application. This operation returns a paginated list of data accessor summaries, including the friendly name, unique identifier, ARN, associated IAM role, and creation/update timestamps for each data accessor.</p>"}, "ListDataSourceSyncJobs": {"name": "ListDataSourceSyncJobs", "http": {"method": "GET", "requestUri": "/applications/{applicationId}/indices/{indexId}/datasources/{dataSourceId}/syncjobs", "responseCode": 200}, "input": {"shape": "ListDataSourceSyncJobsRequest"}, "output": {"shape": "ListDataSourceSyncJobsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Get information about an Amazon Q Business data source connector synchronization.</p>"}, "ListDataSources": {"name": "ListDataSources", "http": {"method": "GET", "requestUri": "/applications/{applicationId}/indices/{indexId}/datasources", "responseCode": 200}, "input": {"shape": "ListDataSourcesRequest"}, "output": {"shape": "ListDataSourcesResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists the Amazon Q Business data source connectors that you have created.</p>"}, "ListDocuments": {"name": "ListDocuments", "http": {"method": "GET", "requestUri": "/applications/{applicationId}/index/{indexId}/documents", "responseCode": 200}, "input": {"shape": "ListDocumentsRequest"}, "output": {"shape": "ListDocumentsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>A list of documents attached to an index.</p>"}, "ListGroups": {"name": "ListGroups", "http": {"method": "GET", "requestUri": "/applications/{applicationId}/indices/{indexId}/groups", "responseCode": 200}, "input": {"shape": "ListGroupsRequest"}, "output": {"shape": "ListGroupsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Provides a list of groups that are mapped to users.</p>"}, "ListIndices": {"name": "ListIndices", "http": {"method": "GET", "requestUri": "/applications/{applicationId}/indices", "responseCode": 200}, "input": {"shape": "ListIndicesRequest"}, "output": {"shape": "ListIndicesResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists the Amazon Q Business indices you have created.</p>"}, "ListMessages": {"name": "ListMessages", "http": {"method": "GET", "requestUri": "/applications/{applicationId}/conversations/{conversationId}", "responseCode": 200}, "input": {"shape": "ListMessagesRequest"}, "output": {"shape": "ListMessagesResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "LicenseNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Gets a list of messages associated with an Amazon Q Business web experience.</p>"}, "ListPluginActions": {"name": "ListPluginActions", "http": {"method": "GET", "requestUri": "/applications/{applicationId}/plugins/{pluginId}/actions", "responseCode": 200}, "input": {"shape": "ListPluginActionsRequest"}, "output": {"shape": "ListPluginActionsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists configured Amazon Q Business actions for a specific plugin in an Amazon Q Business application.</p>"}, "ListPluginTypeActions": {"name": "ListPluginTypeActions", "http": {"method": "GET", "requestUri": "/pluginTypes/{pluginType}/actions", "responseCode": 200}, "input": {"shape": "ListPluginTypeActionsRequest"}, "output": {"shape": "ListPluginTypeActionsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists configured Amazon Q Business actions for any plugin type—both built-in and custom.</p>"}, "ListPluginTypeMetadata": {"name": "ListPluginTypeMetadata", "http": {"method": "GET", "requestUri": "/pluginTypeMetadata", "responseCode": 200}, "input": {"shape": "ListPluginTypeMetadataRequest"}, "output": {"shape": "ListPluginTypeMetadataResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists metadata for all Amazon Q Business plugin types.</p>"}, "ListPlugins": {"name": "ListPlugins", "http": {"method": "GET", "requestUri": "/applications/{applicationId}/plugins", "responseCode": 200}, "input": {"shape": "ListPluginsRequest"}, "output": {"shape": "ListPluginsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists configured Amazon Q Business plugins.</p>"}, "ListRetrievers": {"name": "ListRetrievers", "http": {"method": "GET", "requestUri": "/applications/{applicationId}/retrievers", "responseCode": 200}, "input": {"shape": "ListRetrieversRequest"}, "output": {"shape": "ListRetrieversResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists the retriever used by an Amazon Q Business application.</p>"}, "ListSubscriptions": {"name": "ListSubscriptions", "http": {"method": "GET", "requestUri": "/applications/{applicationId}/subscriptions", "responseCode": 200}, "input": {"shape": "ListSubscriptionsRequest"}, "output": {"shape": "ListSubscriptionsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p> Lists all subscriptions created in an Amazon Q Business application. </p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/v1/tags/{resourceARN}", "responseCode": 200}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Gets a list of tags associated with a specified resource. Amazon Q Business applications and data sources can have tags associated with them.</p>"}, "ListWebExperiences": {"name": "ListWebExperiences", "http": {"method": "GET", "requestUri": "/applications/{applicationId}/experiences", "responseCode": 200}, "input": {"shape": "ListWebExperiencesRequest"}, "output": {"shape": "ListWebExperiencesResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists one or more Amazon Q Business Web Experiences.</p>"}, "PutFeedback": {"name": "PutFeedback", "http": {"method": "POST", "requestUri": "/applications/{applicationId}/conversations/{conversationId}/messages/{messageId}/feedback", "responseCode": 200}, "input": {"shape": "PutFeedbackRequest"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Enables your end user to provide feedback on their Amazon Q Business generated chat responses.</p>"}, "PutGroup": {"name": "PutGroup", "http": {"method": "PUT", "requestUri": "/applications/{applicationId}/indices/{indexId}/groups", "responseCode": 200}, "input": {"shape": "PutGroupRequest"}, "output": {"shape": "PutGroupResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Create, or updates, a mapping of users—who have access to a document—to groups.</p> <p>You can also map sub groups to groups. For example, the group \"Company Intellectual Property Teams\" includes sub groups \"Research\" and \"Engineering\". These sub groups include their own list of users or people who work in these teams. Only users who work in research and engineering, and therefore belong in the intellectual property group, can see top-secret company documents in their Amazon Q Business chat results.</p> <p>There are two options for creating groups, either passing group members inline or using an S3 file via the S3PathForGroupMembers field. For inline groups, there is a limit of 1000 members per group and for provided S3 files there is a limit of 100 thousand members. When creating a group using an S3 file, you provide both an S3 file and a <code>RoleArn</code> for Amazon Q Buisness to access the file.</p>", "idempotent": true}, "SearchRelevantContent": {"name": "SearchRelevantContent", "http": {"method": "POST", "requestUri": "/applications/{applicationId}/relevant-content", "responseCode": 200}, "input": {"shape": "SearchRelevantContentRequest"}, "output": {"shape": "SearchRelevantContentResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "LicenseNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Searches for relevant content in a Amazon Q Business application based on a query. This operation takes a search query text, the Amazon Q Business application identifier, and optional filters (such as content source and maximum results) as input. It returns a list of relevant content items, where each item includes the content text, the unique document identifier, the document title, the document URI, any relevant document attributes, and score attributes indicating the confidence level of the relevance.</p>"}, "StartDataSourceSyncJob": {"name": "StartDataSourceSyncJob", "http": {"method": "POST", "requestUri": "/applications/{applicationId}/indices/{indexId}/datasources/{dataSourceId}/startsync", "responseCode": 200}, "input": {"shape": "StartDataSourceSyncJobRequest"}, "output": {"shape": "StartDataSourceSyncJobResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Starts a data source connector synchronization job. If a synchronization job is already in progress, Amazon Q Business returns a <code>ConflictException</code>.</p>"}, "StopDataSourceSyncJob": {"name": "StopDataSourceSyncJob", "http": {"method": "POST", "requestUri": "/applications/{applicationId}/indices/{indexId}/datasources/{dataSourceId}/stopsync", "responseCode": 200}, "input": {"shape": "StopDataSourceSyncJobRequest"}, "output": {"shape": "StopDataSourceSyncJobResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Stops an Amazon Q Business data source connector synchronization job already in progress.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/v1/tags/{resourceARN}", "responseCode": 200}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Adds the specified tag to the specified Amazon Q Business application or data source resource. If the tag already exists, the existing value is replaced with the new value.</p>", "idempotent": true}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/v1/tags/{resourceARN}", "responseCode": 200}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Removes a tag from an Amazon Q Business application or a data source.</p>", "idempotent": true}, "UpdateApplication": {"name": "UpdateApplication", "http": {"method": "PUT", "requestUri": "/applications/{applicationId}", "responseCode": 200}, "input": {"shape": "UpdateApplicationRequest"}, "output": {"shape": "UpdateApplicationResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Updates an existing Amazon Q Business application.</p> <note> <p>Amazon Q Business applications may securely transmit data for processing across Amazon Web Services Regions within your geography. For more information, see <a href=\"https://docs.aws.amazon.com/amazonq/latest/qbusiness-ug/cross-region-inference.html\">Cross region inference in Amazon Q Business</a>.</p> </note> <note> <p>An Amazon Q Apps service-linked role will be created if it's absent in the Amazon Web Services account when <code>QAppsConfiguration</code> is enabled in the request. For more information, see <a href=\"https://docs.aws.amazon.com/amazonq/latest/qbusiness-ug/using-service-linked-roles-qapps.html\">Using service-linked roles for Q Apps</a>. </p> </note>", "idempotent": true}, "UpdateChatControlsConfiguration": {"name": "UpdateChatControlsConfiguration", "http": {"method": "PATCH", "requestUri": "/applications/{applicationId}/chatcontrols", "responseCode": 200}, "input": {"shape": "UpdateChatControlsConfigurationRequest"}, "output": {"shape": "UpdateChatControlsConfigurationResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Updates a set of chat controls configured for an existing Amazon Q Business application.</p>", "idempotent": true}, "UpdateChatResponseConfiguration": {"name": "UpdateChatResponseConfiguration", "http": {"method": "PUT", "requestUri": "/applications/{applicationId}/chatresponseconfigurations/{chatResponseConfigurationId}", "responseCode": 200}, "input": {"shape": "UpdateChatResponseConfigurationRequest"}, "output": {"shape": "UpdateChatResponseConfigurationResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Updates an existing chat response configuration in an Amazon Q Business application. This operation allows administrators to modify configuration settings, display name, and response parameters to refine how the system generates responses.</p>", "idempotent": true}, "UpdateDataAccessor": {"name": "UpdateDataAccessor", "http": {"method": "PUT", "requestUri": "/applications/{applicationId}/dataaccessors/{dataAccessorId}", "responseCode": 200}, "input": {"shape": "UpdateDataAccessorRequest"}, "output": {"shape": "UpdateDataAccessorResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Updates an existing data accessor. This operation allows modifying the action configurations (the allowed actions and associated filters) and the display name of the data accessor. It does not allow changing the IAM role associated with the data accessor or other core properties of the data accessor.</p>", "idempotent": true}, "UpdateDataSource": {"name": "UpdateDataSource", "http": {"method": "PUT", "requestUri": "/applications/{applicationId}/indices/{indexId}/datasources/{dataSourceId}", "responseCode": 200}, "input": {"shape": "UpdateDataSourceRequest"}, "output": {"shape": "UpdateDataSourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Updates an existing Amazon Q Business data source connector.</p>", "idempotent": true}, "UpdateIndex": {"name": "UpdateIndex", "http": {"method": "PUT", "requestUri": "/applications/{applicationId}/indices/{indexId}", "responseCode": 200}, "input": {"shape": "UpdateIndexRequest"}, "output": {"shape": "UpdateIndexResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Updates an Amazon Q Business index.</p>", "idempotent": true}, "UpdatePlugin": {"name": "UpdatePlugin", "http": {"method": "PUT", "requestUri": "/applications/{applicationId}/plugins/{pluginId}", "responseCode": 200}, "input": {"shape": "UpdatePluginRequest"}, "output": {"shape": "UpdatePluginResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Updates an Amazon Q Business plugin.</p>", "idempotent": true}, "UpdateRetriever": {"name": "UpdateRetriever", "http": {"method": "PUT", "requestUri": "/applications/{applicationId}/retrievers/{retrieverId}", "responseCode": 200}, "input": {"shape": "UpdateRetrieverRequest"}, "output": {"shape": "UpdateRetrieverResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Updates the retriever used for your Amazon Q Business application.</p>", "idempotent": true}, "UpdateSubscription": {"name": "UpdateSubscription", "http": {"method": "PUT", "requestUri": "/applications/{applicationId}/subscriptions/{subscriptionId}", "responseCode": 200}, "input": {"shape": "UpdateSubscriptionRequest"}, "output": {"shape": "UpdateSubscriptionResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Updates the pricing tier for an Amazon Q Business subscription. Upgrades are instant. Downgrades apply at the start of the next month. Subscription tier determines feature access for the user. For more information on subscriptions and pricing tiers, see <a href=\"https://aws.amazon.com/q/business/pricing/\">Amazon Q Business pricing</a>.</p>", "idempotent": true}, "UpdateUser": {"name": "UpdateUser", "http": {"method": "PUT", "requestUri": "/applications/{applicationId}/users/{userId}", "responseCode": 200}, "input": {"shape": "UpdateUserRequest"}, "output": {"shape": "UpdateUserResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Updates a information associated with a user id.</p>", "idempotent": true}, "UpdateWebExperience": {"name": "UpdateWebExperience", "http": {"method": "PUT", "requestUri": "/applications/{applicationId}/experiences/{webExperienceId}", "responseCode": 200}, "input": {"shape": "UpdateWebExperienceRequest"}, "output": {"shape": "UpdateWebExperienceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Updates an Amazon Q Business web experience. </p>", "idempotent": true}}, "shapes": {"APISchema": {"type": "structure", "members": {"payload": {"shape": "Payload", "documentation": "<p>The JSON or YAML-formatted payload defining the OpenAPI schema for a custom plugin. </p>"}, "s3": {"shape": "S3", "documentation": "<p>Contains details about the S3 object containing the OpenAPI schema for a custom plugin. The schema could be in either JSON or YAML format.</p>"}}, "documentation": "<p>Contains details about the OpenAPI schema for a custom plugin. For more information, see <a href=\"https://docs.aws.amazon.com/amazonq/latest/qbusiness-ug/custom-plugin.html#plugins-api-schema\">custom plugin OpenAPI schemas</a>. You can either include the schema directly in the payload field or you can upload it to an S3 bucket and specify the S3 bucket location in the <code>s3</code> field. </p>", "union": true}, "APISchemaType": {"type": "string", "enum": ["OPEN_API_V3"]}, "AccessConfiguration": {"type": "structure", "required": ["accessControls"], "members": {"accessControls": {"shape": "AccessControls", "documentation": "<p>A list of <code>AccessControlList</code> objects.</p>"}, "memberRelation": {"shape": "MemberRelation", "documentation": "<p>Describes the member relation within the <code>AccessControlList</code> object.</p>"}}, "documentation": "<p>Used to configure access permissions for a document.</p>"}, "AccessControl": {"type": "structure", "required": ["principals"], "members": {"principals": {"shape": "Principals", "documentation": "<p>Contains a list of principals, where a principal can be either a <code>USER</code> or a <code>GROUP</code>. Each principal can be have the following type of document access: <code>ALLOW</code> or <code>DENY</code>.</p>"}, "memberRelation": {"shape": "MemberRelation", "documentation": "<p>Describes the member relation within a principal list.</p>"}}, "documentation": "<p>A list of principals. Each principal can be either a <code>USER</code> or a <code>GROUP</code> and can be designated document access permissions of either <code>ALLOW</code> or <code>DENY</code>.</p>"}, "AccessControls": {"type": "list", "member": {"shape": "AccessControl"}}, "AccessDeniedException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p> You don't have access to perform this action. Make sure you have the required permission policies and user accounts and try again.</p>", "error": {"httpStatusCode": 403, "senderFault": true}, "exception": true}, "ActionConfiguration": {"type": "structure", "required": ["action"], "members": {"action": {"shape": "QIamAction", "documentation": "<p>The Amazon Q Business action that is allowed.</p>"}, "filterConfiguration": {"shape": "ActionFilterConfiguration", "documentation": "<p>The filter configuration for the action, if any.</p>"}}, "documentation": "<p>Specifies an allowed action and its associated filter configuration.</p>"}, "ActionConfigurationList": {"type": "list", "member": {"shape": "ActionConfiguration"}, "max": 10, "min": 1}, "ActionExecution": {"type": "structure", "required": ["pluginId", "payload", "payloadFieldNameSeparator"], "members": {"pluginId": {"shape": "PluginId", "documentation": "<p>The identifier of the plugin the action is attached to.</p>"}, "payload": {"shape": "ActionExecutionPayload", "documentation": "<p>A mapping of field names to the field values in input that an end user provides to Amazon Q Business requests to perform their plugin action. </p>"}, "payloadFieldNameSeparator": {"shape": "ActionPayloadFieldNameSeparator", "documentation": "<p>A string used to retain information about the hierarchical contexts within an action execution event payload.</p>"}}, "documentation": "<p>Performs an Amazon Q Business plugin action during a non-streaming chat conversation.</p>"}, "ActionExecutionEvent": {"type": "structure", "required": ["pluginId", "payload", "payloadFieldNameSeparator"], "members": {"pluginId": {"shape": "PluginId", "documentation": "<p>The identifier of the plugin for which the action is being requested.</p>"}, "payload": {"shape": "ActionExecutionPayload", "documentation": "<p>A mapping of field names to the field values in input that an end user provides to Amazon Q Business requests to perform their plugin action. </p>"}, "payloadFieldNameSeparator": {"shape": "ActionPayloadFieldNameSeparator", "documentation": "<p>A string used to retain information about the hierarchical contexts within a action execution event payload.</p>"}}, "documentation": "<p>A request from an end user signalling an intent to perform an Amazon Q Business plugin action during a streaming chat.</p>", "event": true}, "ActionExecutionPayload": {"type": "map", "key": {"shape": "ActionPayloadFieldKey"}, "value": {"shape": "ActionExecutionPayloadField"}}, "ActionExecutionPayloadField": {"type": "structure", "required": ["value"], "members": {"value": {"shape": "ActionPayloadFieldValue", "documentation": "<p>The content of a user input field in an plugin action execution payload.</p>"}}, "documentation": "<p>A user input field in an plugin action execution payload.</p>"}, "ActionFilterConfiguration": {"type": "structure", "required": ["documentAttributeFilter"], "members": {"documentAttributeFilter": {"shape": "Attribute<PERSON>ilter"}}, "documentation": "<p>Specifies filters to apply to an allowed action.</p>"}, "ActionPayloadFieldKey": {"type": "string", "min": 1}, "ActionPayloadFieldNameSeparator": {"type": "string", "max": 1, "min": 1}, "ActionPayloadFieldType": {"type": "string", "enum": ["STRING", "NUMBER", "ARRAY", "BOOLEAN"]}, "ActionPayloadFieldValue": {"type": "structure", "members": {}, "document": true}, "ActionReview": {"type": "structure", "members": {"pluginId": {"shape": "PluginId", "documentation": "<p>The identifier of the plugin associated with the action review.</p>"}, "pluginType": {"shape": "PluginType", "documentation": "<p>The type of plugin.</p>"}, "payload": {"shape": "ActionReviewPayload", "documentation": "<p>Field values that an end user needs to provide to Amazon Q Business for Amazon Q Business to perform the requested plugin action.</p>"}, "payloadFieldNameSeparator": {"shape": "ActionPayloadFieldNameSeparator", "documentation": "<p>A string used to retain information about the hierarchical contexts within an action review payload.</p>"}}, "documentation": "<p>An output event that Amazon Q Business returns to an user who wants to perform a plugin action during a non-streaming chat conversation. It contains information about the selected action with a list of possible user input fields, some pre-populated by Amazon Q Business.</p>"}, "ActionReviewEvent": {"type": "structure", "members": {"conversationId": {"shape": "ConversationId", "documentation": "<p>The identifier of the conversation with which the action review event is associated.</p>"}, "userMessageId": {"shape": "MessageId", "documentation": "<p>The identifier of the conversation with which the plugin action is associated.</p>"}, "systemMessageId": {"shape": "MessageId", "documentation": "<p>The identifier of an Amazon Q Business AI generated associated with the action review event.</p>"}, "pluginId": {"shape": "PluginId", "documentation": "<p>The identifier of the plugin associated with the action review event.</p>"}, "pluginType": {"shape": "PluginType", "documentation": "<p>The type of plugin.</p>"}, "payload": {"shape": "ActionReviewPayload", "documentation": "<p>Field values that an end user needs to provide to Amazon Q Business for Amazon Q Business to perform the requested plugin action.</p>"}, "payloadFieldNameSeparator": {"shape": "ActionPayloadFieldNameSeparator", "documentation": "<p>A string used to retain information about the hierarchical contexts within an action review event payload.</p>"}}, "documentation": "<p>An output event that Amazon Q Business returns to an user who wants to perform a plugin action during a streaming chat conversation. It contains information about the selected action with a list of possible user input fields, some pre-populated by Amazon Q Business. </p>", "event": true}, "ActionReviewPayload": {"type": "map", "key": {"shape": "ActionPayloadFieldKey"}, "value": {"shape": "ActionReviewPayloadField"}}, "ActionReviewPayloadField": {"type": "structure", "members": {"displayName": {"shape": "String", "documentation": "<p> The name of the field. </p>"}, "displayOrder": {"shape": "Integer", "documentation": "<p>The display order of fields in a payload.</p>"}, "displayDescription": {"shape": "String", "documentation": "<p>The field level description of each action review input field. This could be an explanation of the field. In the Amazon Q Business web experience, these descriptions could be used to display as tool tips to help users understand the field. </p>"}, "type": {"shape": "ActionPayloadFieldType", "documentation": "<p>The type of field. </p>"}, "value": {"shape": "ActionPayloadFieldValue", "documentation": "<p>The field value.</p>"}, "allowedValues": {"shape": "ActionReviewPayloadFieldAllowedValues", "documentation": "<p>Information about the field values that an end user can use to provide to Amazon Q Business for Amazon Q Business to perform the requested plugin action.</p>"}, "allowedFormat": {"shape": "String", "documentation": "<p>The expected data format for the action review input field value. For example, in PTO request, <code>from</code> and <code>to</code> would be of <code>datetime</code> allowed format. </p>"}, "arrayItemJsonSchema": {"shape": "ActionReviewPayloadFieldArrayItemJsonSchema", "documentation": "<p>Use to create a custom form with array fields (fields with nested objects inside an array).</p>"}, "required": {"shape": "Boolean", "documentation": "<p>Information about whether the field is required.</p>"}}, "documentation": "<p>A user input field in an plugin action review payload.</p>"}, "ActionReviewPayloadFieldAllowedValue": {"type": "structure", "members": {"value": {"shape": "ActionPayloadFieldValue", "documentation": "<p>The field value.</p>"}, "displayValue": {"shape": "ActionPayloadFieldValue", "documentation": "<p>The name of the field.</p>"}}, "documentation": "<p>Information about the field values that an end user can use to provide to Amazon Q Business for Amazon Q Business to perform the requested plugin action.</p>"}, "ActionReviewPayloadFieldAllowedValues": {"type": "list", "member": {"shape": "ActionReviewPayloadFieldAllowedValue"}}, "ActionReviewPayloadFieldArrayItemJsonSchema": {"type": "structure", "members": {}, "document": true}, "ActionSummary": {"type": "structure", "members": {"actionIdentifier": {"shape": "String", "documentation": "<p>The identifier of an Amazon Q Business plugin action.</p>"}, "displayName": {"shape": "String", "documentation": "<p>The display name assigned by Amazon Q Business to a plugin action. You can't modify this value.</p>"}, "instructionExample": {"shape": "String", "documentation": "<p>An Amazon Q Business suggested prompt and end user can use to invoke a plugin action. This value can be modified and sent as input to initiate an action. For example:</p> <ul> <li> <p>Create a Jira task</p> </li> <li> <p>Create a chat assistant task to find the root cause of a specific incident</p> </li> </ul>"}, "description": {"shape": "String", "documentation": "<p>The description of an Amazon Q Business plugin action.</p>"}}, "documentation": "<p>Summary information for an Amazon Q Business plugin action.</p>"}, "Actions": {"type": "list", "member": {"shape": "ActionSummary"}}, "AmazonResourceName": {"type": "string", "max": 1011, "min": 1}, "Application": {"type": "structure", "members": {"displayName": {"shape": "ApplicationName", "documentation": "<p>The name of the Amazon Q Business application.</p>"}, "applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier for the Amazon Q Business application.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when the Amazon Q Business application was created.</p>"}, "updatedAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when the Amazon Q Business application was last updated. </p>"}, "status": {"shape": "ApplicationStatus", "documentation": "<p>The status of the Amazon Q Business application. The application is ready to use when the status is <code>ACTIVE</code>.</p>"}, "identityType": {"shape": "IdentityType", "documentation": "<p>The authentication type being used by a Amazon Q Business application.</p>"}, "quickSightConfiguration": {"shape": "QuickSightConfiguration", "documentation": "<p>The Amazon QuickSight configuration for an Amazon Q Business application that uses QuickSight as the identity provider.</p>"}}, "documentation": "<p>Summary information for an Amazon Q Business application.</p>"}, "ApplicationArn": {"type": "string", "max": 1284, "min": 0, "pattern": "arn:[a-z0-9-\\.]{1,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[^/].{0,1023}"}, "ApplicationId": {"type": "string", "max": 36, "min": 36, "pattern": "[a-zA-Z0-9][a-zA-Z0-9-]{35}"}, "ApplicationName": {"type": "string", "max": 1000, "min": 1, "pattern": "[a-zA-Z0-9][a-zA-Z0-9_-]*"}, "ApplicationStatus": {"type": "string", "enum": ["CREATING", "ACTIVE", "DELETING", "FAILED", "UPDATING"]}, "Applications": {"type": "list", "member": {"shape": "Application"}}, "AppliedAttachmentsConfiguration": {"type": "structure", "members": {"attachmentsControlMode": {"shape": "AttachmentsControlMode", "documentation": "<p>Information about whether file upload during chat functionality is activated for your application.</p>"}}, "documentation": "<p>Configuration information about the file upload during chat feature for your application.</p>"}, "AppliedCreatorModeConfiguration": {"type": "structure", "required": ["creatorModeControl"], "members": {"creatorModeControl": {"shape": "CreatorModeControl", "documentation": "<p> Information about whether creator mode is enabled or disabled for an Amazon Q Business application. </p>"}}, "documentation": "<p>The creator mode specific admin controls configured for an Amazon Q Business application. Determines whether an end user can generate LLM-only responses when they use the web experience.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/amazonq/latest/qbusiness-ug/guardrails.html\">Admin controls and guardrails</a> and <a href=\"https://docs.aws.amazon.com/amazonq/latest/business-use-dg/using-web-experience.html#chat-source-scope\">Conversation settings</a>.</p>"}, "AppliedOrchestrationConfiguration": {"type": "structure", "required": ["control"], "members": {"control": {"shape": "OrchestrationControl", "documentation": "<p> Information about whether chat orchestration is enabled or disabled for an Amazon Q Business application. </p>"}}, "documentation": "<p>The chat orchestration specific admin controls configured for an Amazon Q Business application. Determines whether Amazon Q Business automatically routes chat requests across configured plugins and data sources in your Amazon Q Business application.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/amazonq/latest/qbusiness-ug/guardrails-global-controls.html#guardrails-global-orchestration\">Chat orchestration settings</a>.</p>"}, "AssociatePermissionRequest": {"type": "structure", "required": ["applicationId", "statementId", "actions", "principal"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The unique identifier of the Amazon Q Business application.</p>", "location": "uri", "locationName": "applicationId"}, "statementId": {"shape": "StatementId", "documentation": "<p>A unique identifier for the policy statement.</p>"}, "actions": {"shape": "QIamActions", "documentation": "<p>The list of Amazon Q Business actions that the ISV is allowed to perform.</p>"}, "conditions": {"shape": "PermissionConditions", "documentation": "<p>The conditions that restrict when the permission is effective. These conditions can be used to limit the permission based on specific attributes of the request.</p>"}, "principal": {"shape": "PrincipalRoleArn", "documentation": "<p>The Amazon Resource Name of the IAM role for the ISV that is being granted permission.</p>"}}}, "AssociatePermissionResponse": {"type": "structure", "members": {"statement": {"shape": "String", "documentation": "<p>The JSON representation of the added permission statement.</p>"}}}, "AssociatedGroup": {"type": "structure", "members": {"name": {"shape": "GroupName", "documentation": "<p>The name of the group associated with the user. This is used to identify the group in access control decisions.</p>"}, "type": {"shape": "MembershipType", "documentation": "<p>The type of the associated group. This indicates the scope of the group's applicability.</p>"}}, "documentation": "<p>Represents a group associated with a given user in the access control system.</p>"}, "AssociatedGroups": {"type": "list", "member": {"shape": "AssociatedGroup"}}, "AssociatedUser": {"type": "structure", "members": {"id": {"shape": "String", "documentation": "<p>The unique identifier of the associated user. This is used to identify the user in access control decisions.</p>"}, "type": {"shape": "MembershipType", "documentation": "<p>The type of the associated user. This indicates the scope of the user's association.</p>"}}, "documentation": "<p>Represents an associated user in the access control system.</p>"}, "AssociatedUsers": {"type": "list", "member": {"shape": "AssociatedUser"}}, "Attachment": {"type": "structure", "members": {"attachmentId": {"shape": "AttachmentId", "documentation": "<p>The identifier of the Amazon Q Business attachment.</p>"}, "conversationId": {"shape": "ConversationId", "documentation": "<p>The identifier of the Amazon Q Business conversation the attachment is associated with.</p>"}, "name": {"shape": "AttachmentName", "documentation": "<p>Filename of the Amazon Q Business attachment.</p>"}, "copyFrom": {"shape": "CopyFromSource", "documentation": "<p>A CopyFromSource containing a reference to the original source of the Amazon Q Business attachment.</p>"}, "fileType": {"shape": "String", "documentation": "<p>Filetype of the Amazon Q Business attachment.</p>"}, "fileSize": {"shape": "Integer", "documentation": "<p>Size in bytes of the Amazon Q Business attachment.</p>"}, "md5chksum": {"shape": "String", "documentation": "<p>MD5 checksum of the Amazon Q Business attachment contents.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when the Amazon Q Business attachment was created.</p>"}, "status": {"shape": "AttachmentStatus", "documentation": "<p>AttachmentStatus of the Amazon Q Business attachment.</p>"}, "error": {"shape": "ErrorDetail", "documentation": "<p>ErrorDetail providing information about a Amazon Q Business attachment error. </p>"}}, "documentation": "<p>An attachment in an Amazon Q Business conversation.</p>"}, "AttachmentId": {"type": "string", "pattern": "[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}"}, "AttachmentInput": {"type": "structure", "members": {"data": {"shape": "Blob", "documentation": "<p>The contents of the attachment.</p>"}, "name": {"shape": "AttachmentName", "documentation": "<p>The filename of the attachment.</p>"}, "copyFrom": {"shape": "CopyFromSource", "documentation": "<p>A reference to an existing attachment.</p>"}}, "documentation": "<p>This is either a file directly uploaded into a web experience chat or a reference to an existing attachment that is part of a web experience chat.</p>"}, "AttachmentInputEvent": {"type": "structure", "members": {"attachment": {"shape": "AttachmentInput"}}, "documentation": "<p>A file input event activated by a end user request to upload files into their web experience chat.</p>", "event": true}, "AttachmentList": {"type": "list", "member": {"shape": "Attachment"}}, "AttachmentName": {"type": "string", "max": 1000, "min": 1, "pattern": "\\P{C}*"}, "AttachmentOutput": {"type": "structure", "members": {"name": {"shape": "AttachmentName", "documentation": "<p>The name of a file uploaded during chat.</p>"}, "status": {"shape": "AttachmentStatus", "documentation": "<p>The status of a file uploaded during chat.</p>"}, "error": {"shape": "ErrorDetail", "documentation": "<p>An error associated with a file uploaded during chat.</p>"}, "attachmentId": {"shape": "AttachmentId", "documentation": "<p>The unique identifier of the Amazon Q Business attachment.</p>"}, "conversationId": {"shape": "ConversationId", "documentation": "<p>The unique identifier of the Amazon Q Business conversation.</p>"}}, "documentation": "<p>The details of a file uploaded during chat.</p>"}, "AttachmentStatus": {"type": "string", "enum": ["FAILED", "SUCCESS"]}, "AttachmentsConfiguration": {"type": "structure", "required": ["attachmentsControlMode"], "members": {"attachmentsControlMode": {"shape": "AttachmentsControlMode", "documentation": "<p>Status information about whether file upload functionality is activated or deactivated for your end user.</p>"}}, "documentation": "<p>Configuration information for the file upload during chat feature.</p>"}, "AttachmentsControlMode": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "AttachmentsInput": {"type": "list", "member": {"shape": "AttachmentInput"}, "min": 1}, "AttachmentsOutput": {"type": "list", "member": {"shape": "AttachmentOutput"}}, "AttributeFilter": {"type": "structure", "members": {"andAllFilters": {"shape": "AttributeFilters", "documentation": "<p>Performs a logical <code>AND</code> operation on all supplied filters.</p>"}, "orAllFilters": {"shape": "AttributeFilters", "documentation": "<p> Performs a logical <code>OR</code> operation on all supplied filters. </p>"}, "notFilter": {"shape": "Attribute<PERSON>ilter", "documentation": "<p>Performs a logical <code>NOT</code> operation on all supplied filters. </p>"}, "equalsTo": {"shape": "DocumentAttribute", "documentation": "<p>Performs an equals operation on two document attributes or metadata fields. Supported for the following <a href=\"https://docs.aws.amazon.com/amazonq/latest/api-reference/API_DocumentAttributeValue.html\">document attribute value types</a>: <code>dateValue</code>, <code>longValue</code>, <code>stringListValue</code> and <code>stringValue</code>.</p>"}, "containsAll": {"shape": "DocumentAttribute", "documentation": "<p>Returns <code>true</code> when a document contains all the specified document attributes or metadata fields. Supported for the following <a href=\"https://docs.aws.amazon.com/amazonq/latest/api-reference/API_DocumentAttributeValue.html\">document attribute value types</a>: <code>stringListValue</code>.</p>"}, "containsAny": {"shape": "DocumentAttribute", "documentation": "<p>Returns <code>true</code> when a document contains any of the specified document attributes or metadata fields. Supported for the following <a href=\"https://docs.aws.amazon.com/amazonq/latest/api-reference/API_DocumentAttributeValue.html\">document attribute value types</a>: <code>stringListValue</code>.</p>"}, "greaterThan": {"shape": "DocumentAttribute", "documentation": "<p>Performs a greater than operation on two document attributes or metadata fields. Supported for the following <a href=\"https://docs.aws.amazon.com/amazonq/latest/api-reference/API_DocumentAttributeValue.html\">document attribute value types</a>: <code>dateValue</code> and <code>longValue</code>.</p>"}, "greaterThanOrEquals": {"shape": "DocumentAttribute", "documentation": "<p>Performs a greater or equals than operation on two document attributes or metadata fields. Supported for the following <a href=\"https://docs.aws.amazon.com/amazonq/latest/api-reference/API_DocumentAttributeValue.html\">document attribute value types</a>: <code>dateValue</code> and <code>longValue</code>. </p>"}, "lessThan": {"shape": "DocumentAttribute", "documentation": "<p>Performs a less than operation on two document attributes or metadata fields. Supported for the following <a href=\"https://docs.aws.amazon.com/amazonq/latest/api-reference/API_DocumentAttributeValue.html\">document attribute value types</a>: <code>dateValue</code> and <code>longValue</code>.</p>"}, "lessThanOrEquals": {"shape": "DocumentAttribute", "documentation": "<p>Performs a less than or equals operation on two document attributes or metadata fields.Supported for the following <a href=\"https://docs.aws.amazon.com/amazonq/latest/api-reference/API_DocumentAttributeValue.html\">document attribute value type</a>: <code>dateValue</code> and <code>longValue</code>. </p>"}}, "documentation": "<p>Enables filtering of responses based on document attributes or metadata fields.</p>"}, "AttributeFilters": {"type": "list", "member": {"shape": "Attribute<PERSON>ilter"}}, "AttributeType": {"type": "string", "enum": ["STRING", "STRING_LIST", "NUMBER", "DATE"]}, "AttributeValueOperator": {"type": "string", "enum": ["DELETE"]}, "AudioExtractionConfiguration": {"type": "structure", "required": ["audioExtractionStatus"], "members": {"audioExtractionStatus": {"shape": "AudioExtractionStatus", "documentation": "<p>The status of audio extraction (ENABLED or DISABLED) for processing audio content from files.</p>"}}, "documentation": "<p>Configuration settings for audio content extraction and processing.</p>"}, "AudioExtractionStatus": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "AudioExtractionType": {"type": "string", "enum": ["TRANSCRIPT", "SUMMARY"]}, "AudioSourceDetails": {"type": "structure", "members": {"mediaId": {"shape": "MediaId", "documentation": "<p>Unique identifier for the audio media file.</p>"}, "mediaMimeType": {"shape": "String", "documentation": "<p>The MIME type of the audio file (e.g., audio/mp3, audio/wav).</p>"}, "startTimeMilliseconds": {"shape": "<PERSON>", "documentation": "<p>The starting timestamp in milliseconds for the relevant audio segment.</p>"}, "endTimeMilliseconds": {"shape": "<PERSON>", "documentation": "<p>The ending timestamp in milliseconds for the relevant audio segment.</p>"}, "audioExtractionType": {"shape": "AudioExtractionType", "documentation": "<p>The type of audio extraction performed on the content.</p>"}}, "documentation": "<p>Details about an audio source, including its identifier, format, and time information.</p>"}, "AuthChallengeRequest": {"type": "structure", "required": ["authorizationUrl"], "members": {"authorizationUrl": {"shape": "Url", "documentation": "<p>The URL sent by Amazon Q Business to the third party authentication server to authenticate a custom plugin user through an OAuth protocol.</p>"}}, "documentation": "<p>A request made by Amazon Q Business to a third paty authentication server to authenticate a custom plugin user.</p>"}, "AuthChallengeRequestEvent": {"type": "structure", "required": ["authorizationUrl"], "members": {"authorizationUrl": {"shape": "Url", "documentation": "<p>The URL sent by Amazon Q Business to a third party authentication server in response to an authentication verification event activated by an end user request to use a custom plugin. </p>"}}, "documentation": "<p>An authentication verification event activated by an end user request to use a custom plugin.</p>", "event": true}, "AuthChallengeResponse": {"type": "structure", "required": ["responseMap"], "members": {"responseMap": {"shape": "AuthorizationResponseMap", "documentation": "<p>The mapping of key-value pairs in an authentication challenge response.</p>"}}, "documentation": "<p>Contains details of the authentication information received from a third party authentication server in response to an authentication challenge.</p>"}, "AuthChallengeResponseEvent": {"type": "structure", "required": ["responseMap"], "members": {"responseMap": {"shape": "AuthorizationResponseMap", "documentation": "<p>The mapping of key-value pairs in an authentication challenge response.</p>"}}, "documentation": "<p>An authentication verification event response by a third party authentication server to Amazon Q Business.</p>", "event": true}, "AuthResponseKey": {"type": "string", "max": 100, "min": 1}, "AuthResponseValue": {"type": "string", "max": 2048, "min": 1}, "AuthorizationResponseMap": {"type": "map", "key": {"shape": "AuthResponseKey"}, "value": {"shape": "AuthResponseValue"}}, "AutoSubscriptionConfiguration": {"type": "structure", "required": ["autoSubscribe"], "members": {"autoSubscribe": {"shape": "AutoSubscriptionStatus", "documentation": "<p>Describes whether automatic subscriptions are enabled for an Amazon Q Business application using IAM identity federation for user management.</p>"}, "defaultSubscriptionType": {"shape": "SubscriptionType", "documentation": "<p>Describes the default subscription type assigned to an Amazon Q Business application using IAM identity federation for user management. If the value for <code>autoSubscribe</code> is set to <code>ENABLED</code> you must select a value for this field.</p>"}}, "documentation": "<p>Subscription configuration information for an Amazon Q Business application using IAM identity federation for user management. </p>"}, "AutoSubscriptionStatus": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "BasicAuthConfiguration": {"type": "structure", "required": ["secretArn", "roleArn"], "members": {"secretArn": {"shape": "SecretArn", "documentation": "<p>The ARN of the Secrets Manager secret that stores the basic authentication credentials used for plugin configuration..</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The ARN of an IAM role used by Amazon Q Business to access the basic authentication credentials stored in a Secrets Manager secret.</p>"}}, "documentation": "<p>Information about the basic authentication credentials used to configure a plugin.</p>"}, "BatchDeleteDocumentRequest": {"type": "structure", "required": ["applicationId", "indexId", "documents"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the Amazon Q Business application.</p>", "location": "uri", "locationName": "applicationId"}, "indexId": {"shape": "IndexId", "documentation": "<p>The identifier of the Amazon Q Business index that contains the documents to delete.</p>", "location": "uri", "locationName": "indexId"}, "documents": {"shape": "DeleteDocuments", "documentation": "<p>Documents deleted from the Amazon Q Business index.</p>"}, "dataSourceSyncId": {"shape": "ExecutionId", "documentation": "<p>The identifier of the data source sync during which the documents were deleted.</p>"}}}, "BatchDeleteDocumentResponse": {"type": "structure", "members": {"failedDocuments": {"shape": "FailedDocuments", "documentation": "<p>A list of documents that couldn't be removed from the Amazon Q Business index. Each entry contains an error message that indicates why the document couldn't be removed from the index. </p>"}}}, "BatchPutDocumentRequest": {"type": "structure", "required": ["applicationId", "indexId", "documents"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the Amazon Q Business application.</p>", "location": "uri", "locationName": "applicationId"}, "indexId": {"shape": "IndexId", "documentation": "<p>The identifier of the Amazon Q Business index to add the documents to. </p>", "location": "uri", "locationName": "indexId"}, "documents": {"shape": "Documents", "documentation": "<p>One or more documents to add to the index.</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of an IAM role with permission to access your S3 bucket.</p>"}, "dataSourceSyncId": {"shape": "ExecutionId", "documentation": "<p>The identifier of the data source sync during which the documents were added.</p>"}}}, "BatchPutDocumentResponse": {"type": "structure", "members": {"failedDocuments": {"shape": "FailedDocuments", "documentation": "<p> A list of documents that were not added to the Amazon Q Business index because the document failed a validation check. Each document contains an error message that indicates why the document couldn't be added to the index. </p>"}}}, "Blob": {"type": "blob"}, "BlockedPhrase": {"type": "string", "max": 36, "min": 0, "pattern": "\\P{C}*"}, "BlockedPhrases": {"type": "list", "member": {"shape": "BlockedPhrase"}}, "BlockedPhrasesConfiguration": {"type": "structure", "members": {"blockedPhrases": {"shape": "BlockedPhrases", "documentation": "<p>A list of phrases blocked from a Amazon Q Business web experience chat.</p>"}, "systemMessageOverride": {"shape": "SystemMessageOverride", "documentation": "<p>The configured custom message displayed to an end user informing them that they've used a blocked phrase during chat.</p>"}}, "documentation": "<p>Provides information about the phrases blocked from chat by your chat control configuration.</p>"}, "BlockedPhrasesConfigurationUpdate": {"type": "structure", "members": {"blockedPhrasesToCreateOrUpdate": {"shape": "BlockedPhrases", "documentation": "<p>Creates or updates a blocked phrases configuration in your Amazon Q Business application.</p>"}, "blockedPhrasesToDelete": {"shape": "BlockedPhrases", "documentation": "<p>Deletes a blocked phrases configuration in your Amazon Q Business application.</p>"}, "systemMessageOverride": {"shape": "SystemMessageOverride", "documentation": "<p>The configured custom message displayed to your end user when they use blocked phrase during chat.</p>"}}, "documentation": "<p>Updates a blocked phrases configuration in your Amazon Q Business application.</p>"}, "Boolean": {"type": "boolean", "box": true}, "BoostingDurationInSeconds": {"type": "long", "box": true, "max": 999999999, "min": 0}, "BrowserExtension": {"type": "string", "enum": ["FIREFOX", "CHROME"]}, "BrowserExtensionConfiguration": {"type": "structure", "required": ["enabledBrowserExtensions"], "members": {"enabledBrowserExtensions": {"shape": "BrowserExtensionList", "documentation": "<p>Specify the browser extensions allowed for your Amazon Q web experience.</p> <ul> <li> <p> <code>CHROME</code> — Enables the extension for Chromium-based browsers (Google Chrome, Microsoft Edge, Opera, etc.).</p> </li> <li> <p> <code>FIREFOX</code> — Enables the extension for Mozilla Firefox.</p> </li> <li> <p> <code>CHROME</code> and <code>FIREFOX</code> — Enable the extension for Chromium-based browsers and Mozilla Firefox.</p> </li> </ul>"}}, "documentation": "<p>The container for browser extension configuration for an Amazon Q Business web experience.</p>"}, "BrowserExtensionList": {"type": "list", "member": {"shape": "BrowserExtension"}, "max": 2, "min": 0}, "CancelSubscriptionRequest": {"type": "structure", "required": ["applicationId", "subscriptionId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the Amazon Q Business application for which the subscription is being cancelled.</p>", "location": "uri", "locationName": "applicationId"}, "subscriptionId": {"shape": "SubscriptionId", "documentation": "<p>The identifier of the Amazon Q Business subscription being cancelled.</p>", "location": "uri", "locationName": "subscriptionId"}}}, "CancelSubscriptionResponse": {"type": "structure", "members": {"subscriptionArn": {"shape": "SubscriptionArn", "documentation": "<p>The Amazon Resource Name (ARN) of the Amazon Q Business subscription being cancelled.</p>"}, "currentSubscription": {"shape": "SubscriptionDetails", "documentation": "<p>The type of your current Amazon Q Business subscription.</p>"}, "nextSubscription": {"shape": "SubscriptionDetails", "documentation": "<p>The type of the Amazon Q Business subscription for the next month.</p>"}}}, "ChatInput": {"type": "structure", "required": ["applicationId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the Amazon Q Business application linked to a streaming Amazon Q Business conversation.</p>", "location": "uri", "locationName": "applicationId"}, "userId": {"shape": "UserId", "documentation": "<p>The identifier of the user attached to the chat input. </p>", "location": "querystring", "locationName": "userId"}, "userGroups": {"shape": "UserGroups", "documentation": "<p>The group names that a user associated with the chat input belongs to.</p>", "location": "querystring", "locationName": "userGroups"}, "conversationId": {"shape": "ConversationId", "documentation": "<p>The identifier of the Amazon Q Business conversation.</p>", "location": "querystring", "locationName": "conversationId"}, "parentMessageId": {"shape": "MessageId", "documentation": "<p>The identifier used to associate a user message with a AI generated response.</p>", "location": "querystring", "locationName": "parentMessageId"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A token that you provide to identify the chat input.</p>", "idempotencyToken": true, "location": "querystring", "locationName": "clientToken"}, "inputStream": {"shape": "ChatInputStream", "documentation": "<p>The streaming input for the <code>Chat</code> API.</p>"}}, "payload": "inputStream"}, "ChatInputStream": {"type": "structure", "members": {"configurationEvent": {"shape": "ConfigurationEvent", "documentation": "<p>A configuration event activated by an end user request to select a specific chat mode.</p>"}, "textEvent": {"shape": "TextInputEvent", "documentation": "<p>Information about the payload of the <code>ChatInputStream</code> event containing the end user message input.</p>"}, "attachmentEvent": {"shape": "AttachmentInputEvent", "documentation": "<p>A request by an end user to upload a file during chat.</p>"}, "actionExecutionEvent": {"shape": "ActionExecutionEvent", "documentation": "<p>A request from an end user to perform an Amazon Q Business plugin action.</p>"}, "endOfInputEvent": {"shape": "EndOfInputEvent", "documentation": "<p>The end of the streaming input for the <code>Chat</code> API.</p>"}, "authChallengeResponseEvent": {"shape": "AuthChallengeResponseEvent", "documentation": "<p>An authentication verification event response by a third party authentication server to Amazon Q Business.</p>"}}, "documentation": "<p>The streaming input for the <code>Chat</code> API.</p>", "eventstream": true}, "ChatMode": {"type": "string", "enum": ["RETRIEVAL_MODE", "CREATOR_MODE", "PLUGIN_MODE"]}, "ChatModeConfiguration": {"type": "structure", "members": {"pluginConfiguration": {"shape": "PluginConfiguration", "documentation": "<p>Configuration information required to invoke chat in <code>PLUGIN_MODE</code>.</p>"}}, "documentation": "<p>Configuration information for Amazon Q Business conversation modes.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/amazonq/latest/qbusiness-ug/guardrails.html\">Admin controls and guardrails</a> and <a href=\"https://docs.aws.amazon.com/amazonq/latest/business-use-dg/using-web-experience.html#chat-source-scope\">Conversation settings</a>.</p>", "union": true}, "ChatOutput": {"type": "structure", "members": {"outputStream": {"shape": "ChatOutputStream", "documentation": "<p>The streaming output for the <code>Chat</code> API.</p>"}}, "payload": "outputStream"}, "ChatOutputStream": {"type": "structure", "members": {"textEvent": {"shape": "TextOutputEvent", "documentation": "<p>Information about the payload of the <code>ChatOutputStream</code> event containing the AI-generated message output.</p>"}, "metadataEvent": {"shape": "MetadataEvent", "documentation": "<p>A metadata event for a AI-generated text output message in a Amazon Q Business conversation. </p>"}, "actionReviewEvent": {"shape": "ActionReviewEvent", "documentation": "<p>A request from Amazon Q Business to the end user for information Amazon Q Business needs to successfully complete a requested plugin action.</p>"}, "failedAttachmentEvent": {"shape": "FailedAttachmentEvent", "documentation": "<p>A failed file upload event during a web experience chat.</p>"}, "authChallengeRequestEvent": {"shape": "AuthChallengeRequestEvent", "documentation": "<p>An authentication verification event activated by an end user request to use a custom plugin.</p>"}}, "documentation": "<p>The streaming output for the <code>Chat</code> API.</p>", "eventstream": true}, "ChatResponseConfiguration": {"type": "structure", "required": ["chatResponseConfigurationId", "chatResponseConfigurationArn", "displayName", "status"], "members": {"chatResponseConfigurationId": {"shape": "ChatResponseConfigurationId", "documentation": "<p>A unique identifier for your chat response configuration settings, used to reference and manage the configuration within the Amazon Q Business service.</p>"}, "chatResponseConfigurationArn": {"shape": "ChatResponseConfigurationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the chat response configuration, which uniquely identifies the resource across all Amazon Web Services services and accounts.</p>"}, "displayName": {"shape": "DisplayName", "documentation": "<p>A human-readable name for the chat response configuration, making it easier to identify and manage multiple configurations within an organization.</p>"}, "responseConfigurationSummary": {"shape": "ResponseConfigurationSummary", "documentation": "<p>A summary of the response configuration settings, providing a concise overview of the key parameters that define how responses are generated and formatted.</p>"}, "status": {"shape": "ChatResponseConfigurationStatus", "documentation": "<p>The current status of the chat response configuration, indicating whether it is active, pending, or in another state that affects its availability for use in chat interactions.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The timestamp indicating when the chat response configuration was initially created, useful for tracking the lifecycle of configuration resources.</p>"}, "updatedAt": {"shape": "Timestamp", "documentation": "<p>The timestamp indicating when the chat response configuration was last modified, helping administrators track changes and maintain version awareness.</p>"}}, "documentation": "<p>Configuration details that define how Amazon Q Business generates and formats responses to user queries in chat interactions. This configuration allows administrators to customize response characteristics to meet specific organizational needs and communication standards.</p>"}, "ChatResponseConfigurationArn": {"type": "string", "max": 1284, "min": 1, "pattern": "arn:[a-z0-9-\\.]{1,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[^/].{0,1023}"}, "ChatResponseConfigurationDetail": {"type": "structure", "members": {"responseConfigurations": {"shape": "ResponseConfigurations", "documentation": "<p>A collection of specific response configuration settings that collectively define how responses are generated, formatted, and presented to users in chat interactions.</p>"}, "responseConfigurationSummary": {"shape": "String", "documentation": "<p>A summary of the response configuration details, providing a concise overview of the key parameters and settings that define the response generation behavior.</p>"}, "status": {"shape": "ChatResponseConfigurationStatus", "documentation": "<p>The current status of the chat response configuration, indicating whether it is active, pending, or in another state that affects its availability for use.</p>"}, "error": {"shape": "ErrorDetail"}, "updatedAt": {"shape": "Timestamp", "documentation": "<p>The timestamp indicating when the detailed chat response configuration was last modified, helping administrators track changes and maintain version awareness.</p>"}}, "documentation": "<p>Detailed information about a chat response configuration, including comprehensive settings and parameters that define how Amazon Q Business generates and formats responses.</p>"}, "ChatResponseConfigurationId": {"type": "string", "max": 36, "min": 36, "pattern": "[a-zA-Z0-9][a-zA-Z0-9-]{35}"}, "ChatResponseConfigurationStatus": {"type": "string", "enum": ["CREATING", "UPDATING", "FAILED", "ACTIVE"]}, "ChatResponseConfigurations": {"type": "list", "member": {"shape": "ChatResponseConfiguration"}}, "ChatSyncInput": {"type": "structure", "required": ["applicationId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the Amazon Q Business application linked to the Amazon Q Business conversation.</p>", "location": "uri", "locationName": "applicationId"}, "userId": {"shape": "UserId", "documentation": "<p>The identifier of the user attached to the chat input.</p>", "location": "querystring", "locationName": "userId"}, "userGroups": {"shape": "UserGroups", "documentation": "<p>The group names that a user associated with the chat input belongs to.</p>", "location": "querystring", "locationName": "userGroups"}, "userMessage": {"shape": "UserMessage", "documentation": "<p>A end user message in a conversation.</p>"}, "attachments": {"shape": "AttachmentsInput", "documentation": "<p>A list of files uploaded directly during chat. You can upload a maximum of 5 files of upto 10 MB each.</p>"}, "actionExecution": {"shape": "ActionExecution", "documentation": "<p>A request from an end user to perform an Amazon Q Business plugin action.</p>"}, "authChallengeResponse": {"shape": "AuthChallengeResponse", "documentation": "<p>An authentication verification event response by a third party authentication server to Amazon Q Business.</p>"}, "conversationId": {"shape": "ConversationId", "documentation": "<p>The identifier of the Amazon Q Business conversation.</p>"}, "parentMessageId": {"shape": "MessageId", "documentation": "<p>The identifier of the previous system message in a conversation.</p>"}, "attributeFilter": {"shape": "Attribute<PERSON>ilter", "documentation": "<p>Enables filtering of Amazon Q Business web experience responses based on document attributes or metadata fields.</p>"}, "chatMode": {"shape": "ChatMode", "documentation": "<p>The <code>chatMode</code> parameter determines the chat modes available to Amazon Q Business users:</p> <ul> <li> <p> <code>RETRIEVAL_MODE</code> - If you choose this mode, Amazon Q generates responses solely from the data sources connected and indexed by the application. If an answer is not found in the data sources or there are no data sources available, Amazon Q will respond with a \"<i>No Answer Found</i>\" message, unless LLM knowledge has been enabled. In that case, Amazon Q will generate a response from the LLM knowledge</p> </li> <li> <p> <code>CREATOR_MODE</code> - By selecting this mode, you can choose to generate responses only from the LLM knowledge. You can also attach files and have Amazon Q generate a response based on the data in those files. If the attached files do not contain an answer for the query, Amazon Q will automatically fall back to generating a response from the LLM knowledge.</p> </li> <li> <p> <code>PLUGIN_MODE</code> - By selecting this mode, users can choose to use plugins in chat to get their responses.</p> </li> </ul> <note> <p>If none of the modes are selected, Amazon Q will only respond using the information from the attached files.</p> </note> <p>For more information, see <a href=\"https://docs.aws.amazon.com/amazonq/latest/qbusiness-ug/guardrails.html\">Admin controls and guardrails</a>, <a href=\"https://docs.aws.amazon.com/amazonq/latest/qbusiness-ug/plugins.html\">Plugins</a>, and <a href=\"https://docs.aws.amazon.com/amazonq/latest/business-use-dg/using-web-experience.html#chat-source-scope\">Response sources</a>.</p>"}, "chatModeConfiguration": {"shape": "ChatModeConfiguration", "documentation": "<p>The chat mode configuration for an Amazon Q Business application.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A token that you provide to identify a chat request.</p>", "idempotencyToken": true}}}, "ChatSyncOutput": {"type": "structure", "members": {"conversationId": {"shape": "ConversationId", "documentation": "<p>The identifier of the Amazon Q Business conversation.</p>"}, "systemMessage": {"shape": "String", "documentation": "<p>An AI-generated message in a conversation.</p>"}, "systemMessageId": {"shape": "MessageId", "documentation": "<p>The identifier of an Amazon Q Business AI generated message within the conversation.</p>"}, "userMessageId": {"shape": "MessageId", "documentation": "<p>The identifier of an Amazon Q Business end user text input message within the conversation.</p>"}, "actionReview": {"shape": "ActionReview", "documentation": "<p>A request from Amazon Q Business to the end user for information Amazon Q Business needs to successfully complete a requested plugin action.</p>"}, "authChallengeRequest": {"shape": "AuthChallengeRequest", "documentation": "<p>An authentication verification event activated by an end user request to use a custom plugin.</p>"}, "sourceAttributions": {"shape": "SourceAttributions", "documentation": "<p>The source documents used to generate the conversation response.</p>"}, "failedAttachments": {"shape": "AttachmentsOutput", "documentation": "<p>A list of files which failed to upload during chat.</p>"}}}, "CheckDocumentAccessRequest": {"type": "structure", "required": ["applicationId", "indexId", "userId", "documentId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The unique identifier of the application. This is required to identify the specific Amazon Q Business application context for the document access check.</p>", "location": "uri", "locationName": "applicationId"}, "indexId": {"shape": "IndexId", "documentation": "<p>The unique identifier of the index. Used to locate the correct index within the application where the document is stored.</p>", "location": "uri", "locationName": "indexId"}, "userId": {"shape": "String", "documentation": "<p>The unique identifier of the user. Used to check the access permissions for this specific user against the document's ACL.</p>", "location": "uri", "locationName": "userId"}, "documentId": {"shape": "DocumentId", "documentation": "<p>The unique identifier of the document. Specifies which document's access permissions are being checked.</p>", "location": "uri", "locationName": "documentId"}, "dataSourceId": {"shape": "DataSourceId", "documentation": "<p>The unique identifier of the data source. Identifies the specific data source from which the document originates. Should not be used when a document is uploaded directly with BatchPutDocument, as no dataSourceId is available or necessary. </p>", "location": "querystring", "locationName": "dataSourceId"}}}, "CheckDocumentAccessResponse": {"type": "structure", "members": {"userGroups": {"shape": "AssociatedGroups", "documentation": "<p>An array of groups the user is part of for the specified data source. Each group has a name and type.</p>"}, "userAliases": {"shape": "AssociatedUsers", "documentation": "<p>An array of aliases associated with the user. This includes both global and local aliases, each with a name and type.</p>"}, "hasAccess": {"shape": "Boolean", "documentation": "<p>A boolean value indicating whether the specified user has access to the document, either direct access or transitive access via groups and aliases attached to the document.</p>"}, "documentAcl": {"shape": "DocumentAcl", "documentation": "<p>The Access Control List (ACL) associated with the document. Includes allowlist and denylist conditions that determine user access.</p>"}}}, "ClientIdForOIDC": {"type": "string", "max": 255, "min": 1, "pattern": "[a-zA-Z0-9_.:/()*?=-]*"}, "ClientIdsForOIDC": {"type": "list", "member": {"shape": "ClientIdForOIDC"}}, "ClientNamespace": {"type": "string", "max": 64, "min": 1, "pattern": "[a-zA-Z0-9._-]*"}, "ClientToken": {"type": "string", "max": 100, "min": 1}, "ConfigurationEvent": {"type": "structure", "members": {"chatMode": {"shape": "ChatMode", "documentation": "<p>The chat modes available to an Amazon Q Business end user.</p> <ul> <li> <p> <code>RETRIEVAL_MODE</code> - The default chat mode for an Amazon Q Business application. When this mode is enabled, Amazon Q Business generates responses only from data sources connected to an Amazon Q Business application.</p> </li> <li> <p> <code>CREATOR_MODE</code> - By selecting this mode, users can choose to generate responses only from the LLM knowledge, without consulting connected data sources, for a chat request.</p> </li> <li> <p> <code>PLUGIN_MODE</code> - By selecting this mode, users can choose to use plugins in chat.</p> </li> </ul> <p>For more information, see <a href=\"https://docs.aws.amazon.com/amazonq/latest/qbusiness-ug/guardrails.html\">Admin controls and guardrails</a>, <a href=\"https://docs.aws.amazon.com/amazonq/latest/qbusiness-ug/plugins.html\">Plugins</a>, and <a href=\"https://docs.aws.amazon.com/amazonq/latest/business-use-dg/using-web-experience.html#chat-source-scope\">Conversation settings</a>.</p>"}, "chatModeConfiguration": {"shape": "ChatModeConfiguration"}, "attributeFilter": {"shape": "Attribute<PERSON>ilter"}}, "documentation": "<p>A configuration event activated by an end user request to select a specific chat mode.</p>", "event": true}, "ConflictException": {"type": "structure", "required": ["message", "resourceId", "resourceType"], "members": {"message": {"shape": "ErrorMessage", "documentation": "<p>The message describing a <code>ConflictException</code>.</p>"}, "resourceId": {"shape": "String", "documentation": "<p>The identifier of the resource affected.</p>"}, "resourceType": {"shape": "String", "documentation": "<p>The type of the resource affected.</p>"}}, "documentation": "<p>You are trying to perform an action that conflicts with the current status of your resource. Fix any inconsistencies with your resources and try again.</p>", "error": {"httpStatusCode": 409, "senderFault": true}, "exception": true}, "ContentBlockerRule": {"type": "structure", "members": {"systemMessageOverride": {"shape": "SystemMessageOverride", "documentation": "<p>The configured custom message displayed to an end user informing them that they've used a blocked phrase during chat.</p>"}}, "documentation": "<p>A rule for configuring how Amazon Q Business responds when it encounters a a blocked topic. You can configure a custom message to inform your end users that they have asked about a restricted topic and suggest any next steps they should take.</p>"}, "ContentRetrievalRule": {"type": "structure", "members": {"eligibleDataSources": {"shape": "EligibleDataSources", "documentation": "<p>Specifies data sources in a Amazon Q Business application to use for content generation.</p>"}}, "documentation": "<p>Rules for retrieving content from data sources connected to a Amazon Q Business application for a specific topic control configuration.</p>"}, "ContentSource": {"type": "structure", "members": {"retriever": {"shape": "RetrieverContentSource", "documentation": "<p>The retriever to use as the content source.</p>"}}, "documentation": "<p>Specifies the source of content to search in.</p>", "union": true}, "ContentType": {"type": "string", "enum": ["PDF", "HTML", "MS_WORD", "PLAIN_TEXT", "PPT", "RTF", "XML", "XSLT", "MS_EXCEL", "CSV", "JSON", "MD"]}, "Conversation": {"type": "structure", "members": {"conversationId": {"shape": "ConversationId", "documentation": "<p>The identifier of the Amazon Q Business conversation.</p>"}, "title": {"shape": "ConversationTitle", "documentation": "<p>The title of the conversation.</p>"}, "startTime": {"shape": "Timestamp", "documentation": "<p>The start time of the conversation.</p>"}}, "documentation": "<p>A conversation in an Amazon Q Business application.</p>"}, "ConversationId": {"type": "string", "max": 36, "min": 36, "pattern": "[a-zA-Z0-9][a-zA-Z0-9-]{35}"}, "ConversationSource": {"type": "structure", "required": ["conversationId", "attachmentId"], "members": {"conversationId": {"shape": "ConversationId", "documentation": "<p>The unique identifier of the Amazon Q Business conversation.</p>"}, "attachmentId": {"shape": "AttachmentId", "documentation": "<p>The unique identifier of the Amazon Q Business attachment.</p>"}}, "documentation": "<p>The source reference for an existing attachment in an existing conversation.</p>"}, "ConversationTitle": {"type": "string"}, "Conversations": {"type": "list", "member": {"shape": "Conversation"}}, "CopyFromSource": {"type": "structure", "members": {"conversation": {"shape": "ConversationSource", "documentation": "<p>A reference to an attachment in an existing conversation.</p>"}}, "documentation": "<p>The source reference for an existing attachment.</p>", "union": true}, "CreateAnonymousWebExperienceUrlRequest": {"type": "structure", "required": ["applicationId", "webExperienceId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the Amazon Q Business application environment attached to the web experience.</p>", "location": "uri", "locationName": "applicationId"}, "webExperienceId": {"shape": "WebExperienceId", "documentation": "<p>The identifier of the web experience.</p>", "location": "uri", "locationName": "webExperienceId"}, "sessionDurationInMinutes": {"shape": "SessionDurationInMinutes", "documentation": "<p>The duration of the session associated with the unique URL for the web experience.</p>"}}}, "CreateAnonymousWebExperienceUrlResponse": {"type": "structure", "members": {"anonymousUrl": {"shape": "Url", "documentation": "<p>The unique URL for accessing the web experience.</p> <important> <p>This URL can only be used once and must be used within 5 minutes after it's generated.</p> </important>"}}}, "CreateApplicationRequest": {"type": "structure", "required": ["displayName"], "members": {"displayName": {"shape": "ApplicationName", "documentation": "<p>A name for the Amazon Q Business application. </p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p> The Amazon Resource Name (ARN) of an IAM role with permissions to access your Amazon CloudWatch logs and metrics. If this property is not specified, Amazon Q Business will create a <a href=\"https://docs.aws.amazon.com/amazonq/latest/qbusiness-ug/using-service-linked-roles.html#slr-permissions\">service linked role (SLR)</a> and use it as the application's role.</p>"}, "identityType": {"shape": "IdentityType", "documentation": "<p>The authentication type being used by a Amazon Q Business application.</p>"}, "iamIdentityProviderArn": {"shape": "IAMIdentityProviderArn", "documentation": "<p>The Amazon Resource Name (ARN) of an identity provider being used by an Amazon Q Business application.</p>"}, "identityCenterInstanceArn": {"shape": "InstanceArn", "documentation": "<p> The Amazon Resource Name (ARN) of the IAM Identity Center instance you are either creating for—or connecting to—your Amazon Q Business application.</p>"}, "clientIdsForOIDC": {"shape": "ClientIdsForOIDC", "documentation": "<p>The OIDC client ID for a Amazon Q Business application.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description for the Amazon Q Business application. </p>"}, "encryptionConfiguration": {"shape": "EncryptionConfiguration", "documentation": "<p>The identifier of the KMS key that is used to encrypt your data. Amazon Q Business doesn't support asymmetric keys.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>A list of key-value pairs that identify or categorize your Amazon Q Business application. You can also use tags to help control access to the application. Tag keys and values can consist of Unicode letters, digits, white space, and any of the following symbols: _ . : / = + - @.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A token that you provide to identify the request to create your Amazon Q Business application.</p>", "idempotencyToken": true}, "attachmentsConfiguration": {"shape": "AttachmentsConfiguration", "documentation": "<p>An option to allow end users to upload files directly during chat.</p>"}, "qAppsConfiguration": {"shape": "QAppsConfiguration", "documentation": "<p>An option to allow end users to create and use Amazon Q Apps in the web experience.</p>"}, "personalizationConfiguration": {"shape": "PersonalizationConfiguration", "documentation": "<p>Configuration information about chat response personalization. For more information, see <a href=\"https://docs.aws.amazon.com/amazonq/latest/qbusiness-ug/personalizing-chat-responses.html\">Personalizing chat responses</a> </p>"}, "quickSightConfiguration": {"shape": "QuickSightConfiguration", "documentation": "<p>The Amazon QuickSight configuration for an Amazon Q Business application that uses QuickSight for authentication. This configuration is required if your application uses QuickSight as the identity provider. For more information, see <a href=\"https://docs.aws.amazon.com/amazonq/latest/qbusiness-ug/create-quicksight-integrated-application.html\">Creating an Amazon QuickSight integrated application</a>.</p>"}}}, "CreateApplicationResponse": {"type": "structure", "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the Amazon Q Business application.</p>"}, "applicationArn": {"shape": "ApplicationArn", "documentation": "<p> The Amazon Resource Name (ARN) of the Amazon Q Business application. </p>"}}}, "CreateChatResponseConfigurationRequest": {"type": "structure", "required": ["applicationId", "displayName", "responseConfigurations"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The unique identifier of the Amazon Q Business application for which to create the new chat response configuration.</p>", "location": "uri", "locationName": "applicationId"}, "displayName": {"shape": "DisplayName", "documentation": "<p>A human-readable name for the new chat response configuration, making it easier to identify and manage among multiple configurations.</p>"}, "clientToken": {"shape": "String", "documentation": "<p>A unique, case-sensitive identifier to ensure idempotency of the request. This helps prevent the same configuration from being created multiple times if retries occur.</p>", "idempotencyToken": true}, "responseConfigurations": {"shape": "ResponseConfigurations", "documentation": "<p>A collection of response configuration settings that define how Amazon Q Business will generate and format responses to user queries in chat interactions.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>A list of key-value pairs to apply as tags to the new chat response configuration, enabling categorization and management of resources across Amazon Web Services services.</p>"}}}, "CreateChatResponseConfigurationResponse": {"type": "structure", "required": ["chatResponseConfigurationId", "chatResponseConfigurationArn"], "members": {"chatResponseConfigurationId": {"shape": "ChatResponseConfigurationId", "documentation": "<p>The unique identifier assigned to a newly created chat response configuration, used for subsequent operations on this resource.</p>"}, "chatResponseConfigurationArn": {"shape": "ChatResponseConfigurationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the newly created chat response configuration, which uniquely identifies the resource across all Amazon Web Services services. </p>"}}}, "CreateDataAccessorRequest": {"type": "structure", "required": ["applicationId", "principal", "actionConfigurations", "displayName"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The unique identifier of the Amazon Q Business application.</p>", "location": "uri", "locationName": "applicationId"}, "principal": {"shape": "PrincipalRoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role for the ISV that will be accessing the data.</p>"}, "actionConfigurations": {"shape": "ActionConfigurationList", "documentation": "<p>A list of action configurations specifying the allowed actions and any associated filters.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A unique, case-sensitive identifier you provide to ensure idempotency of the request.</p>", "idempotencyToken": true}, "displayName": {"shape": "DataAccessorName", "documentation": "<p>A friendly name for the data accessor.</p>"}, "authenticationDetail": {"shape": "DataAccessorAuthenticationDetail", "documentation": "<p>The authentication configuration details for the data accessor. This specifies how the ISV will authenticate when accessing data through this data accessor.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>The tags to associate with the data accessor.</p>"}}}, "CreateDataAccessorResponse": {"type": "structure", "required": ["dataAccessorId", "idcApplicationArn", "dataAccessorArn"], "members": {"dataAccessorId": {"shape": "DataAccessorId", "documentation": "<p>The unique identifier of the created data accessor.</p>"}, "idcApplicationArn": {"shape": "IdcApplicationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM Identity Center application created for this data accessor.</p>"}, "dataAccessorArn": {"shape": "DataAccessorArn", "documentation": "<p>The Amazon Resource Name (ARN) of the created data accessor.</p>"}}}, "CreateDataSourceRequest": {"type": "structure", "required": ["applicationId", "indexId", "displayName", "configuration"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p> The identifier of the Amazon Q Business application the data source will be attached to.</p>", "location": "uri", "locationName": "applicationId"}, "indexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index that you want to use with the data source connector.</p>", "location": "uri", "locationName": "indexId"}, "displayName": {"shape": "DataSourceName", "documentation": "<p>A name for the data source connector.</p>"}, "configuration": {"shape": "DataSourceConfiguration", "documentation": "<p>Configuration information to connect your data source repository to Amazon Q Business. Use this parameter to provide a JSON schema with configuration information specific to your data source connector.</p> <p>Each data source has a JSON schema provided by Amazon Q Business that you must use. For example, the Amazon S3 and Web Crawler connectors require the following JSON schemas:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/amazonq/latest/qbusiness-ug/s3-api.html\">Amazon S3 JSON schema</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/amazonq/latest/qbusiness-ug/web-crawler-api.html\">Web Crawler JSON schema</a> </p> </li> </ul> <p>You can find configuration templates for your specific data source using the following steps:</p> <ol> <li> <p>Navigate to the <a href=\"https://docs.aws.amazon.com/amazonq/latest/business-use-dg/connectors-list.html\">Supported connectors</a> page in the Amazon Q Business User Guide, and select the data source of your choice.</p> </li> <li> <p>Then, from your specific data source connector page, select <b>Using the API</b>. You will find the JSON schema for your data source, including parameter descriptions, in this section.</p> </li> </ol>"}, "vpcConfiguration": {"shape": "DataSourceVpcConfiguration", "documentation": "<p>Configuration information for an Amazon VPC (Virtual Private Cloud) to connect to your data source. For more information, see <a href=\"https://docs.aws.amazon.com/amazonq/latest/business-use-dg/connector-vpc.html\">Using Amazon VPC with Amazon Q Business connectors</a>.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description for the data source connector.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>A list of key-value pairs that identify or categorize the data source connector. You can also use tags to help control access to the data source connector. Tag keys and values can consist of Unicode letters, digits, white space, and any of the following symbols: _ . : / = + - @.</p>"}, "syncSchedule": {"shape": "SyncSchedule", "documentation": "<p>Sets the frequency for Amazon Q Business to check the documents in your data source repository and update your index. If you don't set a schedule, Amazon Q Business won't periodically update the index.</p> <p>Specify a <code>cron-</code> format schedule string or an empty string to indicate that the index is updated on demand. You can't specify the <code>Schedule</code> parameter when the <code>Type</code> parameter is set to <code>CUSTOM</code>. If you do, you receive a <code>ValidationException</code> exception. </p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of an IAM role with permission to access the data source and required resources.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A token you provide to identify a request to create a data source connector. Multiple calls to the <code>CreateDataSource</code> API with the same client token will create only one data source connector. </p>", "idempotencyToken": true}, "documentEnrichmentConfiguration": {"shape": "DocumentEnrichmentConfiguration"}, "mediaExtractionConfiguration": {"shape": "MediaExtractionConfiguration", "documentation": "<p>The configuration for extracting information from media in documents during ingestion.</p>"}}}, "CreateDataSourceResponse": {"type": "structure", "members": {"dataSourceId": {"shape": "DataSourceId", "documentation": "<p>The identifier of the data source connector.</p>"}, "dataSourceArn": {"shape": "DataSourceArn", "documentation": "<p> The Amazon Resource Name (ARN) of a data source in an Amazon Q Business application. </p>"}}}, "CreateIndexRequest": {"type": "structure", "required": ["applicationId", "displayName"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the Amazon Q Business application using the index.</p>", "location": "uri", "locationName": "applicationId"}, "displayName": {"shape": "IndexName", "documentation": "<p>A name for the Amazon Q Business index.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description for the Amazon Q Business index.</p>"}, "type": {"shape": "IndexType", "documentation": "<p>The index type that's suitable for your needs. For more information on what's included in each type of index, see <a href=\"https://docs.aws.amazon.com/amazonq/latest/qbusiness-ug/tiers.html#index-tiers\">Amazon Q Business tiers</a>.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>A list of key-value pairs that identify or categorize the index. You can also use tags to help control access to the index. Tag keys and values can consist of Unicode letters, digits, white space, and any of the following symbols: _ . : / = + - @.</p>"}, "capacityConfiguration": {"shape": "IndexCapacityConfiguration", "documentation": "<p>The capacity units you want to provision for your index. You can add and remove capacity to fit your usage needs.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A token that you provide to identify the request to create an index. Multiple calls to the <code>CreateIndex</code> API with the same client token will create only one index.</p>", "idempotencyToken": true}}}, "CreateIndexResponse": {"type": "structure", "members": {"indexId": {"shape": "IndexId", "documentation": "<p>The identifier for the Amazon Q Business index.</p>"}, "indexArn": {"shape": "IndexArn", "documentation": "<p> The Amazon Resource Name (ARN) of an Amazon Q Business index.</p>"}}}, "CreatePluginRequest": {"type": "structure", "required": ["applicationId", "displayName", "type", "authConfiguration"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the application that will contain the plugin.</p>", "location": "uri", "locationName": "applicationId"}, "displayName": {"shape": "PluginName", "documentation": "<p>A the name for your plugin.</p>"}, "type": {"shape": "PluginType", "documentation": "<p>The type of plugin you want to create.</p>"}, "authConfiguration": {"shape": "PluginAuthConfiguration"}, "serverUrl": {"shape": "Url", "documentation": "<p>The source URL used for plugin configuration.</p>"}, "customPluginConfiguration": {"shape": "CustomPluginConfiguration", "documentation": "<p>Contains configuration for a custom plugin.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>A list of key-value pairs that identify or categorize the data source connector. You can also use tags to help control access to the data source connector. Tag keys and values can consist of Unicode letters, digits, white space, and any of the following symbols: _ . : / = + - @.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A token that you provide to identify the request to create your Amazon Q Business plugin.</p>", "idempotencyToken": true}}}, "CreatePluginResponse": {"type": "structure", "members": {"pluginId": {"shape": "PluginId", "documentation": "<p>The identifier of the plugin created.</p>"}, "pluginArn": {"shape": "PluginArn", "documentation": "<p>The Amazon Resource Name (ARN) of a plugin.</p>"}, "buildStatus": {"shape": "PluginBuildStatus", "documentation": "<p>The current status of a plugin. A plugin is modified asynchronously.</p>"}}}, "CreateRetrieverRequest": {"type": "structure", "required": ["applicationId", "type", "displayName", "configuration"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of your Amazon Q Business application.</p>", "location": "uri", "locationName": "applicationId"}, "type": {"shape": "RetrieverType", "documentation": "<p>The type of retriever you are using.</p>"}, "displayName": {"shape": "RetrieverName", "documentation": "<p>The name of your retriever.</p>"}, "configuration": {"shape": "RetrieverConfiguration"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The ARN of an IAM role used by Amazon Q Business to access the basic authentication credentials stored in a Secrets Manager secret.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A token that you provide to identify the request to create your Amazon Q Business application retriever.</p>", "idempotencyToken": true}, "tags": {"shape": "Tags", "documentation": "<p>A list of key-value pairs that identify or categorize the retriever. You can also use tags to help control access to the retriever. Tag keys and values can consist of Unicode letters, digits, white space, and any of the following symbols: _ . : / = + - @.</p>"}}}, "CreateRetrieverResponse": {"type": "structure", "members": {"retrieverId": {"shape": "RetrieverId", "documentation": "<p>The identifier of the retriever you are using.</p>"}, "retrieverArn": {"shape": "RetrieverArn", "documentation": "<p>The Amazon Resource Name (ARN) of an IAM role associated with a retriever.</p>"}}}, "CreateSubscriptionRequest": {"type": "structure", "required": ["applicationId", "principal", "type"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the Amazon Q Business application the subscription should be added to.</p>", "location": "uri", "locationName": "applicationId"}, "principal": {"shape": "SubscriptionPrincipal", "documentation": "<p>The IAM Identity Center <code>UserId</code> or <code>GroupId</code> of a user or group in the IAM Identity Center instance connected to the Amazon Q Business application.</p>"}, "type": {"shape": "SubscriptionType", "documentation": "<p>The type of Amazon Q Business subscription you want to create.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A token that you provide to identify the request to create a subscription for your Amazon Q Business application.</p>", "idempotencyToken": true}}}, "CreateSubscriptionResponse": {"type": "structure", "members": {"subscriptionId": {"shape": "SubscriptionId", "documentation": "<p>The identifier of the Amazon Q Business subscription created.</p>"}, "subscriptionArn": {"shape": "SubscriptionArn", "documentation": "<p>The Amazon Resource Name (ARN) of the Amazon Q Business subscription created.</p>"}, "currentSubscription": {"shape": "SubscriptionDetails", "documentation": "<p>The type of your current Amazon Q Business subscription.</p>"}, "nextSubscription": {"shape": "SubscriptionDetails", "documentation": "<p>The type of the Amazon Q Business subscription for the next month.</p>"}}}, "CreateUserRequest": {"type": "structure", "required": ["applicationId", "userId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the application for which the user mapping will be created.</p>", "location": "uri", "locationName": "applicationId"}, "userId": {"shape": "String", "documentation": "<p>The user emails attached to a user mapping.</p>"}, "userAliases": {"shape": "CreateUserRequestUserAliasesList", "documentation": "<p>The list of user aliases in the mapping.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A token that you provide to identify the request to create your Amazon Q Business user mapping.</p>", "idempotencyToken": true}}}, "CreateUserRequestUserAliasesList": {"type": "list", "member": {"shape": "UserAlias"}, "max": 100, "min": 0}, "CreateUserResponse": {"type": "structure", "members": {}}, "CreateWebExperienceRequest": {"type": "structure", "required": ["applicationId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the Amazon Q Business web experience.</p>", "location": "uri", "locationName": "applicationId"}, "title": {"shape": "WebExperienceTitle", "documentation": "<p>The title for your Amazon Q Business web experience.</p>"}, "subtitle": {"shape": "WebExperienceSubtitle", "documentation": "<p>A subtitle to personalize your Amazon Q Business web experience.</p>"}, "welcomeMessage": {"shape": "WebExperienceWelcomeMessage", "documentation": "<p>The customized welcome message for end users of an Amazon Q Business web experience.</p>"}, "samplePromptsControlMode": {"shape": "WebExperienceSamplePromptsControlMode", "documentation": "<p>Determines whether sample prompts are enabled in the web experience for an end user.</p>"}, "origins": {"shape": "WebExperienceOrigins", "documentation": "<p>Sets the website domain origins that are allowed to embed the Amazon Q Business web experience. The <i>domain origin</i> refers to the base URL for accessing a website including the protocol (<code>http/https</code>), the domain name, and the port number (if specified). </p> <note> <p>You must only submit a <i>base URL</i> and not a full path. For example, <code>https://docs.aws.amazon.com</code>.</p> </note>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the service role attached to your web experience.</p> <note> <p>You must provide this value if you're using IAM Identity Center to manage end user access to your application. If you're using legacy identity management to manage user access, you don't need to provide this value.</p> </note>"}, "tags": {"shape": "Tags", "documentation": "<p>A list of key-value pairs that identify or categorize your Amazon Q Business web experience. You can also use tags to help control access to the web experience. Tag keys and values can consist of Unicode letters, digits, white space, and any of the following symbols: _ . : / = + - @.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A token you provide to identify a request to create an Amazon Q Business web experience. </p>", "idempotencyToken": true}, "identityProviderConfiguration": {"shape": "IdentityProviderConfiguration", "documentation": "<p>Information about the identity provider (IdP) used to authenticate end users of an Amazon Q Business web experience.</p>"}, "browserExtensionConfiguration": {"shape": "BrowserExtensionConfiguration", "documentation": "<p>The browser extension configuration for an Amazon Q Business web experience.</p> <note> <p> For Amazon Q Business application using external OIDC-compliant identity providers (IdPs). The IdP administrator must add the browser extension sign-in redirect URLs to the IdP application. For more information, see <a href=\"https://docs.aws.amazon.com/amazonq/latest/qbusiness-ug/browser-extensions.html\">Configure external OIDC identity provider for your browser extensions.</a>. </p> </note>"}, "customizationConfiguration": {"shape": "CustomizationConfiguration", "documentation": "<p>Sets the custom logo, favicon, font, and color used in the Amazon Q web experience. </p>"}}}, "CreateWebExperienceResponse": {"type": "structure", "members": {"webExperienceId": {"shape": "WebExperienceId", "documentation": "<p>The identifier of the Amazon Q Business web experience.</p>"}, "webExperienceArn": {"shape": "WebExperienceArn", "documentation": "<p> The Amazon Resource Name (ARN) of an Amazon Q Business web experience.</p>"}}}, "CreatorModeConfiguration": {"type": "structure", "required": ["creatorModeControl"], "members": {"creatorModeControl": {"shape": "CreatorModeControl", "documentation": "<p>Status information about whether <code>CREATOR_MODE</code> has been enabled or disabled. The default status is <code>DISABLED</code>.</p>"}}, "documentation": "<p>Configuration information required to invoke chat in <code>CREATOR_MODE</code>.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/amazonq/latest/qbusiness-ug/guardrails.html\">Admin controls and guardrails</a> and <a href=\"https://docs.aws.amazon.com/amazonq/latest/business-use-dg/using-web-experience.html#chat-source-scope\">Conversation settings</a>.</p>"}, "CreatorModeControl": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "CustomCSSUrl": {"type": "string", "max": 1284, "min": 0, "pattern": "(https?://[a-zA-Z0-9-_.+%/]+\\.css)?"}, "CustomPluginConfiguration": {"type": "structure", "required": ["description", "apiSchemaType"], "members": {"description": {"shape": "PluginDescription", "documentation": "<p>A description for your custom plugin configuration.</p>"}, "apiSchemaType": {"shape": "APISchemaType", "documentation": "<p>The type of OpenAPI schema to use.</p>"}, "apiSchema": {"shape": "APISchema", "documentation": "<p>Contains either details about the S3 object containing the OpenAPI schema for the action group or the JSON or YAML-formatted payload defining the schema.</p>"}}, "documentation": "<p> Configuration information required to create a custom plugin.</p>"}, "CustomizationConfiguration": {"type": "structure", "members": {"customCSSUrl": {"shape": "CustomCSSUrl", "documentation": "<p>Provides the URL where the custom CSS file is hosted for an Amazon Q web experience.</p>"}, "logoUrl": {"shape": "LogoUrl", "documentation": "<p>Provides the URL where the custom logo file is hosted for an Amazon Q web experience.</p>"}, "fontUrl": {"shape": "FontUrl", "documentation": "<p>Provides the URL where the custom font file is hosted for an Amazon Q web experience.</p>"}, "faviconUrl": {"shape": "FaviconUrl", "documentation": "<p>Provides the URL where the custom favicon file is hosted for an Amazon Q web experience.</p>"}}, "documentation": "<p>Contains the configuration information to customize the logo, font, and color of an Amazon Q Business web experience with individual files for each property or a CSS file for them all.</p>"}, "DataAccessor": {"type": "structure", "members": {"displayName": {"shape": "DataAccessorName", "documentation": "<p>The friendly name of the data accessor.</p>"}, "dataAccessorId": {"shape": "DataAccessorId", "documentation": "<p>The unique identifier of the data accessor.</p>"}, "dataAccessorArn": {"shape": "DataAccessorArn", "documentation": "<p>The Amazon Resource Name (ARN) of the data accessor.</p>"}, "idcApplicationArn": {"shape": "IdcApplicationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the associated IAM Identity Center application.</p>"}, "principal": {"shape": "PrincipalRoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role for the ISV associated with this data accessor.</p>"}, "authenticationDetail": {"shape": "DataAccessorAuthenticationDetail", "documentation": "<p>The authentication configuration details for the data accessor. This specifies how the ISV authenticates when accessing data through this data accessor.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The timestamp when the data accessor was created.</p>"}, "updatedAt": {"shape": "Timestamp", "documentation": "<p>The timestamp when the data accessor was last updated.</p>"}}, "documentation": "<p>Provides summary information about a data accessor.</p>"}, "DataAccessorArn": {"type": "string", "max": 1284, "min": 0, "pattern": "arn:[a-z0-9-\\.]{1,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[^/].{0,1023}"}, "DataAccessorAuthenticationConfiguration": {"type": "structure", "members": {"idcTrustedTokenIssuerConfiguration": {"shape": "DataAccessorIdcTrustedTokenIssuerConfiguration", "documentation": "<p>Configuration for IAM Identity Center Trusted Token Issuer (TTI) authentication used when the authentication type is <code>AWS_IAM_IDC_TTI</code>.</p>"}}, "documentation": "<p>A union type that contains the specific authentication configuration based on the authentication type selected.</p>", "union": true}, "DataAccessorAuthenticationDetail": {"type": "structure", "required": ["authenticationType"], "members": {"authenticationType": {"shape": "DataAccessorAuthenticationType", "documentation": "<p>The type of authentication to use for the data accessor. This determines how the ISV authenticates when accessing data. You can use one of two authentication types:</p> <ul> <li> <p> <code>AWS_IAM_IDC_TTI</code> - Authentication using IAM Identity Center Trusted Token Issuer (TTI). This authentication type allows the ISV to use a trusted token issuer to generate tokens for accessing the data.</p> </li> <li> <p> <code>AWS_IAM_IDC_AUTH_CODE</code> - Authentication using IAM Identity Center authorization code flow. This authentication type uses the standard OAuth 2.0 authorization code flow for authentication.</p> </li> </ul>"}, "authenticationConfiguration": {"shape": "DataAccessorAuthenticationConfiguration", "documentation": "<p>The specific authentication configuration based on the authentication type.</p>"}, "externalIds": {"shape": "DataAccessorExternalIds", "documentation": "<p>A list of external identifiers associated with this authentication configuration. These are used to correlate the data accessor with external systems.</p>"}}, "documentation": "<p>Contains the authentication configuration details for a data accessor. This structure defines how the ISV authenticates when accessing data through the data accessor.</p>"}, "DataAccessorAuthenticationType": {"type": "string", "documentation": "<p>The type of authentication mechanism used by the data accessor.</p>", "enum": ["AWS_IAM_IDC_TTI", "AWS_IAM_IDC_AUTH_CODE"]}, "DataAccessorExternalId": {"type": "string", "max": 1000, "min": 1, "pattern": "[a-zA-Z0-9][a-zA-Z0-9_-]*"}, "DataAccessorExternalIds": {"type": "list", "member": {"shape": "DataAccessorExternalId"}, "max": 1, "min": 1}, "DataAccessorId": {"type": "string", "max": 36, "min": 36, "pattern": "[a-zA-Z0-9][a-zA-Z0-9-]{35}"}, "DataAccessorIdcTrustedTokenIssuerConfiguration": {"type": "structure", "required": ["idcTrustedTokenIssuerArn"], "members": {"idcTrustedTokenIssuerArn": {"shape": "IdcTrustedTokenIssuerArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM Identity Center Trusted Token Issuer that will be used for authentication.</p>"}}, "documentation": "<p>Configuration details for IAM Identity Center Trusted Token Issuer (TTI) authentication.</p>"}, "DataAccessorName": {"type": "string", "max": 100, "min": 1, "pattern": "[a-zA-Z0-9][a-zA-Z0-9_-]*", "sensitive": true}, "DataAccessors": {"type": "list", "member": {"shape": "DataAccessor"}}, "DataSource": {"type": "structure", "members": {"displayName": {"shape": "DataSourceName", "documentation": "<p>The name of the Amazon Q Business data source.</p>"}, "dataSourceId": {"shape": "DataSourceId", "documentation": "<p>The identifier of the Amazon Q Business data source.</p>"}, "type": {"shape": "String", "documentation": "<p>The type of the Amazon Q Business data source.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when the Amazon Q Business data source was created.</p>"}, "updatedAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when the Amazon Q Business data source was last updated. </p>"}, "status": {"shape": "DataSourceStatus", "documentation": "<p>The status of the Amazon Q Business data source.</p>"}}, "documentation": "<p>A data source in an Amazon Q Business application.</p>"}, "DataSourceArn": {"type": "string", "max": 1284, "min": 0, "pattern": "arn:[a-z0-9-\\.]{1,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[^/].{0,1023}"}, "DataSourceConfiguration": {"type": "structure", "members": {}, "documentation": "<p>Provides the configuration information for an Amazon Q Business data source.</p>", "document": true}, "DataSourceId": {"type": "string", "max": 36, "min": 36, "pattern": "[a-zA-Z0-9][a-zA-Z0-9-]{35}"}, "DataSourceIds": {"type": "list", "member": {"shape": "DataSourceId"}, "max": 1, "min": 1}, "DataSourceName": {"type": "string", "max": 1000, "min": 1, "pattern": "[a-zA-Z0-9][a-zA-Z0-9_-]*"}, "DataSourceStatus": {"type": "string", "enum": ["PENDING_CREATION", "CREATING", "ACTIVE", "DELETING", "FAILED", "UPDATING"]}, "DataSourceSyncJob": {"type": "structure", "members": {"executionId": {"shape": "ExecutionId", "documentation": "<p>The identifier of a data source synchronization job.</p>"}, "startTime": {"shape": "Timestamp", "documentation": "<p>The Unix time stamp when the data source synchronization job started.</p>"}, "endTime": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when the synchronization job completed.</p>"}, "status": {"shape": "DataSourceSyncJobStatus", "documentation": "<p>The status of the synchronization job. When the <code>Status</code> field is set to <code>SUCCEEDED</code>, the synchronization job is done. If the status code is <code>FAILED</code>, the <code>ErrorCode</code> and <code>ErrorMessage</code> fields give you the reason for the failure.</p>"}, "error": {"shape": "ErrorDetail", "documentation": "<p>If the <code>Status</code> field is set to <code>FAILED</code>, the <code>ErrorCode</code> field indicates the reason the synchronization failed. </p>"}, "dataSourceErrorCode": {"shape": "String", "documentation": "<p>If the reason that the synchronization failed is due to an error with the underlying data source, this field contains a code that identifies the error.</p>"}, "metrics": {"shape": "DataSourceSyncJobMetrics", "documentation": "<p>Maps a batch delete document request to a specific data source sync job. This is optional and should only be supplied when documents are deleted by a data source connector.</p>"}}, "documentation": "<p>Provides information about an Amazon Q Business data source connector synchronization job.</p>"}, "DataSourceSyncJobMetrics": {"type": "structure", "members": {"documentsAdded": {"shape": "MetricValue", "documentation": "<p>The current count of documents added from the data source during the data source sync.</p>"}, "documentsModified": {"shape": "MetricValue", "documentation": "<p>The current count of documents modified in the data source during the data source sync.</p>"}, "documentsDeleted": {"shape": "MetricValue", "documentation": "<p>The current count of documents deleted from the data source during the data source sync.</p>"}, "documentsFailed": {"shape": "MetricValue", "documentation": "<p>The current count of documents that failed to sync from the data source during the data source sync.</p>"}, "documentsScanned": {"shape": "MetricValue", "documentation": "<p>The current count of documents crawled by the ongoing sync job in the data source.</p>"}}, "documentation": "<p>Maps a batch delete document request to a specific Amazon Q Business data source connector sync job.</p>"}, "DataSourceSyncJobStatus": {"type": "string", "enum": ["FAILED", "SUCCEEDED", "SYNCING", "INCOMPLETE", "STOPPING", "ABORTED", "SYNCING_INDEXING"]}, "DataSourceSyncJobs": {"type": "list", "member": {"shape": "DataSourceSyncJob"}}, "DataSourceUserId": {"type": "string", "max": 1024, "min": 1, "pattern": "\\P{C}*"}, "DataSourceVpcConfiguration": {"type": "structure", "required": ["subnetIds", "securityGroupIds"], "members": {"subnetIds": {"shape": "SubnetIds", "documentation": "<p>A list of identifiers for subnets within your Amazon VPC. The subnets should be able to connect to each other in the VPC, and they should have outgoing access to the Internet through a NAT device.</p>"}, "securityGroupIds": {"shape": "SecurityGroupIds", "documentation": "<p>A list of identifiers of security groups within your Amazon VPC. The security groups should enable Amazon Q Business to connect to the data source.</p>"}}, "documentation": "<p>Provides configuration information needed to connect to an Amazon VPC (Virtual Private Cloud).</p>"}, "DataSources": {"type": "list", "member": {"shape": "DataSource"}}, "DateAttributeBoostingConfiguration": {"type": "structure", "required": ["boostingLevel"], "members": {"boostingLevel": {"shape": "DocumentAttributeBoostingLevel", "documentation": "<p>Specifies the priority tier ranking of boosting applied to document attributes. For version 2, this parameter indicates the relative ranking between boosted fields (ONE being highest priority, TWO being second highest, etc.) and determines the order in which attributes influence document ranking in search results. For version 1, this parameter specifies the boosting intensity. For version 2, boosting intensity (VERY HIGH, HIGH, MEDIUM, LOW, NONE) are not supported. Note that in version 2, you are not allowed to boost on only one field and make this value TWO.</p>"}, "boostingDurationInSeconds": {"shape": "BoostingDurationInSeconds", "documentation": "<p>Specifies the duration, in seconds, of a boost applies to a <code>DATE</code> type document attribute.</p>"}}, "documentation": "<p>Provides information on boosting <code>DATE</code> type document attributes.</p> <p>For more information on how boosting document attributes work in Amazon Q Business, see <a href=\"https://docs.aws.amazon.com/amazonq/latest/business-use-dg/metadata-boosting.html\">Boosting using document attributes</a>.</p>"}, "DeleteApplicationRequest": {"type": "structure", "required": ["applicationId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the Amazon Q Business application.</p>", "location": "uri", "locationName": "applicationId"}}}, "DeleteApplicationResponse": {"type": "structure", "members": {}}, "DeleteAttachmentRequest": {"type": "structure", "required": ["applicationId", "conversationId", "attachmentId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The unique identifier for the Amazon Q Business application environment.</p>", "location": "uri", "locationName": "applicationId"}, "conversationId": {"shape": "ConversationId", "documentation": "<p>The unique identifier of the conversation.</p>", "location": "uri", "locationName": "conversationId"}, "attachmentId": {"shape": "AttachmentId", "documentation": "<p>The unique identifier for the attachment.</p>", "location": "uri", "locationName": "attachmentId"}, "userId": {"shape": "UserId", "documentation": "<p>The unique identifier of the user involved in the conversation.</p>", "location": "querystring", "locationName": "userId"}}}, "DeleteAttachmentResponse": {"type": "structure", "members": {}}, "DeleteChatControlsConfigurationRequest": {"type": "structure", "required": ["applicationId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the application the chat controls have been configured for.</p>", "location": "uri", "locationName": "applicationId"}}}, "DeleteChatControlsConfigurationResponse": {"type": "structure", "members": {}}, "DeleteChatResponseConfigurationRequest": {"type": "structure", "required": ["applicationId", "chatResponseConfigurationId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The unique identifier of theAmazon Q Business application from which to delete the chat response configuration.</p>", "location": "uri", "locationName": "applicationId"}, "chatResponseConfigurationId": {"shape": "ChatResponseConfigurationId", "documentation": "<p>The unique identifier of the chat response configuration to delete from the specified application. </p>", "location": "uri", "locationName": "chatResponseConfigurationId"}}}, "DeleteChatResponseConfigurationResponse": {"type": "structure", "members": {}}, "DeleteConversationRequest": {"type": "structure", "required": ["conversationId", "applicationId"], "members": {"conversationId": {"shape": "ConversationId", "documentation": "<p>The identifier of the Amazon Q Business web experience conversation being deleted.</p>", "location": "uri", "locationName": "conversationId"}, "applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the Amazon Q Business application associated with the conversation.</p>", "location": "uri", "locationName": "applicationId"}, "userId": {"shape": "UserId", "documentation": "<p>The identifier of the user who is deleting the conversation.</p>", "location": "querystring", "locationName": "userId"}}}, "DeleteConversationResponse": {"type": "structure", "members": {}}, "DeleteDataAccessorRequest": {"type": "structure", "required": ["applicationId", "dataAccessorId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The unique identifier of the Amazon Q Business application.</p>", "location": "uri", "locationName": "applicationId"}, "dataAccessorId": {"shape": "DataAccessorId", "documentation": "<p>The unique identifier of the data accessor to delete.</p>", "location": "uri", "locationName": "dataAccessorId"}}}, "DeleteDataAccessorResponse": {"type": "structure", "members": {}}, "DeleteDataSourceRequest": {"type": "structure", "required": ["applicationId", "indexId", "dataSourceId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the Amazon Q Business application used with the data source connector.</p>", "location": "uri", "locationName": "applicationId"}, "indexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index used with the data source connector.</p>", "location": "uri", "locationName": "indexId"}, "dataSourceId": {"shape": "DataSourceId", "documentation": "<p>The identifier of the data source connector that you want to delete. </p>", "location": "uri", "locationName": "dataSourceId"}}}, "DeleteDataSourceResponse": {"type": "structure", "members": {}}, "DeleteDocument": {"type": "structure", "required": ["documentId"], "members": {"documentId": {"shape": "DocumentId", "documentation": "<p>The identifier of the deleted document.</p>"}}, "documentation": "<p>A document deleted from an Amazon Q Business data source connector.</p>"}, "DeleteDocuments": {"type": "list", "member": {"shape": "DeleteDocument"}}, "DeleteGroupRequest": {"type": "structure", "required": ["applicationId", "indexId", "groupName"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the application in which the group mapping belongs.</p>", "location": "uri", "locationName": "applicationId"}, "indexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index you want to delete the group from.</p>", "location": "uri", "locationName": "indexId"}, "groupName": {"shape": "GroupName", "documentation": "<p>The name of the group you want to delete.</p>", "location": "uri", "locationName": "groupName"}, "dataSourceId": {"shape": "DataSourceId", "documentation": "<p>The identifier of the data source linked to the group</p> <p>A group can be tied to multiple data sources. You can delete a group from accessing documents in a certain data source. For example, the groups \"Research\", \"Engineering\", and \"Sales and Marketing\" are all tied to the company's documents stored in the data sources Confluence and Salesforce. You want to delete \"Research\" and \"Engineering\" groups from Salesforce, so that these groups cannot access customer-related documents stored in Salesforce. Only \"Sales and Marketing\" should access documents in the Salesforce data source.</p>", "location": "querystring", "locationName": "dataSourceId"}}}, "DeleteGroupResponse": {"type": "structure", "members": {}}, "DeleteIndexRequest": {"type": "structure", "required": ["applicationId", "indexId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the Amazon Q Business application the Amazon Q Business index is linked to.</p>", "location": "uri", "locationName": "applicationId"}, "indexId": {"shape": "IndexId", "documentation": "<p>The identifier of the Amazon Q Business index.</p>", "location": "uri", "locationName": "indexId"}}}, "DeleteIndexResponse": {"type": "structure", "members": {}}, "DeletePluginRequest": {"type": "structure", "required": ["applicationId", "pluginId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier the application attached to the Amazon Q Business plugin.</p>", "location": "uri", "locationName": "applicationId"}, "pluginId": {"shape": "PluginId", "documentation": "<p>The identifier of the plugin being deleted.</p>", "location": "uri", "locationName": "pluginId"}}}, "DeletePluginResponse": {"type": "structure", "members": {}}, "DeleteRetrieverRequest": {"type": "structure", "required": ["applicationId", "retrieverId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the Amazon Q Business application using the retriever.</p>", "location": "uri", "locationName": "applicationId"}, "retrieverId": {"shape": "RetrieverId", "documentation": "<p>The identifier of the retriever being deleted.</p>", "location": "uri", "locationName": "retrieverId"}}}, "DeleteRetrieverResponse": {"type": "structure", "members": {}}, "DeleteUserRequest": {"type": "structure", "required": ["applicationId", "userId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the application from which the user is being deleted.</p>", "location": "uri", "locationName": "applicationId"}, "userId": {"shape": "String", "documentation": "<p>The user email being deleted.</p>", "location": "uri", "locationName": "userId"}}}, "DeleteUserResponse": {"type": "structure", "members": {}}, "DeleteWebExperienceRequest": {"type": "structure", "required": ["applicationId", "webExperienceId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the Amazon Q Business application linked to the Amazon Q Business web experience.</p>", "location": "uri", "locationName": "applicationId"}, "webExperienceId": {"shape": "WebExperienceId", "documentation": "<p>The identifier of the Amazon Q Business web experience being deleted.</p>", "location": "uri", "locationName": "webExperienceId"}}}, "DeleteWebExperienceResponse": {"type": "structure", "members": {}}, "Description": {"type": "string", "max": 1000, "min": 0, "pattern": "[\\s\\S]*"}, "DisassociatePermissionRequest": {"type": "structure", "required": ["applicationId", "statementId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The unique identifier of the Amazon Q Business application.</p>", "location": "uri", "locationName": "applicationId"}, "statementId": {"shape": "String", "documentation": "<p>The statement ID of the permission to remove.</p>", "location": "uri", "locationName": "statementId"}}}, "DisassociatePermissionResponse": {"type": "structure", "members": {}}, "DisplayName": {"type": "string", "max": 100, "min": 1}, "Document": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "DocumentId", "documentation": "<p>The identifier of the document.</p>"}, "attributes": {"shape": "DocumentAttributes", "documentation": "<p>Custom attributes to apply to the document for refining Amazon Q Business web experience responses.</p>"}, "content": {"shape": "DocumentContent", "documentation": "<p>The contents of the document.</p>"}, "contentType": {"shape": "ContentType", "documentation": "<p>The file type of the document in the Blob field.</p> <p>If you want to index snippets or subsets of HTML documents instead of the entirety of the HTML documents, you add the <code>HTML</code> start and closing tags (<code>&lt;HTML&gt;content&lt;/HTML&gt;</code>) around the content.</p>"}, "title": {"shape": "Title", "documentation": "<p>The title of the document.</p>"}, "accessConfiguration": {"shape": "AccessConfiguration", "documentation": "<p>Configuration information for access permission to a document.</p>"}, "documentEnrichmentConfiguration": {"shape": "DocumentEnrichmentConfiguration", "documentation": "<p>The configuration information for altering document metadata and content during the document ingestion process.</p>"}, "mediaExtractionConfiguration": {"shape": "MediaExtractionConfiguration", "documentation": "<p>The configuration for extracting information from media in the document.</p>"}}, "documentation": "<p>A document in an Amazon Q Business application.</p>"}, "DocumentAcl": {"type": "structure", "members": {"allowlist": {"shape": "DocumentAclMembership", "documentation": "<p>The allowlist conditions for the document. Users or groups matching these conditions are granted access to the document.</p>"}, "denyList": {"shape": "DocumentAclMembership", "documentation": "<p>The denylist conditions for the document. Users or groups matching these conditions are denied access to the document, overriding allowlist permissions.</p>"}}, "documentation": "<p>Represents the Access Control List (ACL) for a document, containing both allowlist and denylist conditions.</p>"}, "DocumentAclCondition": {"type": "structure", "members": {"memberRelation": {"shape": "MemberRelation", "documentation": "<p>The logical relation between members in the condition, determining how multiple user or group conditions are combined.</p>"}, "users": {"shape": "DocumentAclUsers", "documentation": "<p>An array of user identifiers that this condition applies to. Users listed here are subject to the access rule defined by this condition.</p>"}, "groups": {"shape": "DocumentAclGroups", "documentation": "<p>An array of group identifiers that this condition applies to. Groups listed here are subject to the access rule defined by this condition.</p>"}}, "documentation": "<p>Represents a condition in the document's ACL, specifying access rules for users and groups.</p>"}, "DocumentAclConditions": {"type": "list", "member": {"shape": "DocumentAclCondition"}}, "DocumentAclGroup": {"type": "structure", "members": {"name": {"shape": "GroupName", "documentation": "<p>The name of the group in the document's ACL. This is used to identify the group when applying access rules.</p>"}, "type": {"shape": "MembershipType", "documentation": "<p>The type of the group. This indicates the scope of the group's applicability in access control.</p>"}}, "documentation": "<p>Represents a group in the document's ACL, used to define access permissions for multiple users collectively.</p>"}, "DocumentAclGroups": {"type": "list", "member": {"shape": "DocumentAclGroup"}}, "DocumentAclMembership": {"type": "structure", "members": {"memberRelation": {"shape": "MemberRelation", "documentation": "<p>The logical relation between members in the membership rule, determining how multiple conditions are combined.</p>"}, "conditions": {"shape": "DocumentAclConditions", "documentation": "<p>An array of conditions that define the membership rules. Each condition specifies criteria for users or groups to be included in this membership.</p>"}}, "documentation": "<p>Represents membership rules in the document's ACL, defining how users or groups are associated with access permissions.</p>"}, "DocumentAclUser": {"type": "structure", "members": {"id": {"shape": "String", "documentation": "<p>The unique identifier of the user in the document's ACL. This is used to identify the user when applying access rules.</p>"}, "type": {"shape": "MembershipType", "documentation": "<p>The type of the user. This indicates the scope of the user's applicability in access control.</p>"}}, "documentation": "<p>Represents a user in the document's ACL, used to define access permissions for individual users.</p>"}, "DocumentAclUsers": {"type": "list", "member": {"shape": "DocumentAclUser"}}, "DocumentAttribute": {"type": "structure", "required": ["name", "value"], "members": {"name": {"shape": "DocumentAttributeKey", "documentation": "<p>The identifier for the attribute.</p>"}, "value": {"shape": "DocumentAttributeValue", "documentation": "<p>The value of the attribute. </p>"}}, "documentation": "<p>A document attribute or metadata field.</p>"}, "DocumentAttributeBoostingConfiguration": {"type": "structure", "members": {"numberConfiguration": {"shape": "NumberAttributeBoostingConfiguration", "documentation": "<p>Provides information on boosting <code>NUMBER</code> type document attributes.</p> <p> <code>NUMBER</code> attributes are not supported when using <code>NativeIndexConfiguration</code> version 2, which focuses on <code>DATE</code> attributes for recency and <code>STRING</code> attributes for source prioritization.</p>"}, "stringConfiguration": {"shape": "StringAttributeBoostingConfiguration", "documentation": "<p>Provides information on boosting <code>STRING</code> type document attributes.</p> <p>Version 2 assigns priority tiers to <code>STRING</code> attributes, establishing clear hierarchical relationships with other boosted attributes.</p>"}, "dateConfiguration": {"shape": "DateAttributeBoostingConfiguration", "documentation": "<p>Provides information on boosting <code>DATE</code> type document attributes.</p> <p>Version 2 assigns priority tiers to <code>DATE</code> attributes, establishing clear hierarchical relationships with other boosted attributes.</p>"}, "stringListConfiguration": {"shape": "StringListAttributeBoostingConfiguration", "documentation": "<p>Provides information on boosting <code>STRING_LIST</code> type document attributes.</p> <p> <code>STRING_LIST</code> attributes are not supported when using <code>NativeIndexConfiguration</code> version 2, which focuses on <code>DATE</code> attributes for recency and <code>STRING</code> attributes for source prioritization.</p>"}}, "documentation": "<p>Provides information on boosting supported Amazon Q Business document attribute types. When an end user chat query matches document attributes that have been boosted, Amazon Q Business prioritizes generating responses from content that matches the boosted document attributes.</p> <p>In version 2, boosting uses numeric values (ONE, TWO) to indicate priority tiers that establish clear hierarchical relationships between boosted attributes. This allows for more precise control over how different attributes influence search results.</p> <note> <p>For <code>STRING</code> and <code>STRING_LIST</code> type document attributes to be used for boosting on the console and the API, they must be enabled for search using the <a href=\"https://docs.aws.amazon.com/amazonq/latest/api-reference/API_DocumentAttributeConfiguration.html\">DocumentAttributeConfiguration</a> object of the <a href=\"https://docs.aws.amazon.com/amazonq/latest/api-reference/API_UpdateIndex.html\">UpdateIndex</a> API. If you haven't enabled searching on these attributes, you can't boost attributes of these data types on either the console or the API.</p> </note> <p>For more information on how boosting document attributes work in Amazon Q Business, see <a href=\"https://docs.aws.amazon.com/amazonq/latest/business-use-dg/metadata-boosting.html\">Boosting using document attributes</a>.</p>", "union": true}, "DocumentAttributeBoostingLevel": {"type": "string", "enum": ["NONE", "LOW", "MEDIUM", "HIGH", "VERY_HIGH", "ONE", "TWO"]}, "DocumentAttributeBoostingOverrideMap": {"type": "map", "key": {"shape": "DocumentAttributeKey"}, "value": {"shape": "DocumentAttributeBoostingConfiguration"}, "min": 1}, "DocumentAttributeCondition": {"type": "structure", "required": ["key", "operator"], "members": {"key": {"shape": "DocumentAttributeKey", "documentation": "<p>The identifier of the document attribute used for the condition.</p> <p>For example, 'Source_URI' could be an identifier for the attribute or metadata field that contains source URIs associated with the documents.</p> <p>Amazon Q Business currently doesn't support <code>_document_body</code> as an attribute key used for the condition.</p>"}, "operator": {"shape": "DocumentEnrichmentConditionOperator", "documentation": "<p>The identifier of the document attribute used for the condition.</p> <p>For example, 'Source_URI' could be an identifier for the attribute or metadata field that contains source URIs associated with the documents.</p> <p>Amazon Q Business currently does not support <code>_document_body</code> as an attribute key used for the condition.</p>"}, "value": {"shape": "DocumentAttributeValue"}}, "documentation": "<p>The condition used for the target document attribute or metadata field when ingesting documents into Amazon Q Business. You use this with <a href=\"https://docs.aws.amazon.com/amazonq/latest/api-reference/API_DocumentAttributeTarget.html\"> <code>DocumentAttributeTarget</code> </a> to apply the condition.</p> <p>For example, you can create the 'Department' target field and have it prefill department names associated with the documents based on information in the 'Source_URI' field. Set the condition that if the 'Source_URI' field contains 'financial' in its URI value, then prefill the target field 'Department' with the target value 'Finance' for the document.</p> <p>Amazon Q Business can't create a target field if it has not already been created as an index field. After you create your index field, you can create a document metadata field using <code>DocumentAttributeTarget</code>. Amazon Q Business then will map your newly created metadata field to your index field.</p>"}, "DocumentAttributeConfiguration": {"type": "structure", "members": {"name": {"shape": "DocumentMetadataConfigurationName", "documentation": "<p>The name of the document attribute.</p>"}, "type": {"shape": "AttributeType", "documentation": "<p>The type of document attribute.</p>"}, "search": {"shape": "Status", "documentation": "<p>Information about whether the document attribute can be used by an end user to search for information on their web experience.</p>"}}, "documentation": "<p>Configuration information for document attributes. Document attributes are metadata or fields associated with your documents. For example, the company department name associated with each document.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/amazonq/latest/business-use-dg/doc-attributes.html\">Understanding document attributes</a>.</p>"}, "DocumentAttributeConfigurations": {"type": "list", "member": {"shape": "DocumentAttributeConfiguration"}, "max": 500, "min": 1}, "DocumentAttributeKey": {"type": "string", "max": 200, "min": 1, "pattern": "[a-zA-Z0-9_][a-zA-Z0-9_-]*"}, "DocumentAttributeStringListValue": {"type": "list", "member": {"shape": "String"}}, "DocumentAttributeTarget": {"type": "structure", "required": ["key"], "members": {"key": {"shape": "DocumentAttributeKey", "documentation": "<p>The identifier of the target document attribute or metadata field. For example, 'Department' could be an identifier for the target attribute or metadata field that includes the department names associated with the documents.</p>"}, "value": {"shape": "DocumentAttributeValue"}, "attributeValueOperator": {"shape": "AttributeValueOperator", "documentation": "<p> <code>TRUE</code> to delete the existing target value for your specified target attribute key. You cannot create a target value and set this to <code>TRUE</code>.</p>"}}, "documentation": "<p>The target document attribute or metadata field you want to alter when ingesting documents into Amazon Q Business.</p> <p>For example, you can delete all customer identification numbers associated with the documents, stored in the document metadata field called 'Customer_ID' by setting the target key as 'Customer_ID' and the deletion flag to <code>TRUE</code>. This removes all customer ID values in the field 'Customer_ID'. This would scrub personally identifiable information from each document's metadata.</p> <p>Amazon Q Business can't create a target field if it has not already been created as an index field. After you create your index field, you can create a document metadata field using <a href=\"https://docs.aws.amazon.com/amazonq/latest/api-reference/API_DocumentAttributeTarget.html\"> <code>DocumentAttributeTarget</code> </a>. Amazon Q Business will then map your newly created document attribute to your index field.</p> <p>You can also use this with <a href=\"https://docs.aws.amazon.com/amazonq/latest/api-reference/API_DocumentAttributeCondition.html\"> <code>DocumentAttributeCondition</code> </a>.</p>"}, "DocumentAttributeValue": {"type": "structure", "members": {"stringValue": {"shape": "DocumentAttributeValueStringValueString", "documentation": "<p>A string.</p>"}, "stringListValue": {"shape": "DocumentAttributeStringListValue", "documentation": "<p>A list of strings.</p>"}, "longValue": {"shape": "<PERSON>", "documentation": "<p>A long integer value. </p>"}, "dateValue": {"shape": "Timestamp", "documentation": "<p>A date expressed as an ISO 8601 string.</p> <p>It's important for the time zone to be included in the ISO 8601 date-time format. For example, 2012-03-25T12:30:10+01:00 is the ISO 8601 date-time format for March 25th 2012 at 12:30PM (plus 10 seconds) in Central European Time. </p>"}}, "documentation": "<p>The value of a document attribute. You can only provide one value for a document attribute.</p>", "union": true}, "DocumentAttributeValueStringValueString": {"type": "string", "max": 2048, "min": 0}, "DocumentAttributes": {"type": "list", "member": {"shape": "DocumentAttribute"}, "max": 500, "min": 1}, "DocumentContent": {"type": "structure", "members": {"blob": {"shape": "Blob", "documentation": "<p>The contents of the document. Documents passed to the <code>blob</code> parameter must be base64 encoded. Your code might not need to encode the document file bytes if you're using an Amazon Web Services SDK to call Amazon Q Business APIs. If you are calling the Amazon Q Business endpoint directly using REST, you must base64 encode the contents before sending.</p>"}, "s3": {"shape": "S3", "documentation": "<p>The path to the document in an Amazon S3 bucket.</p>"}}, "documentation": "<p>The contents of a document.</p>", "union": true}, "DocumentContentOperator": {"type": "string", "enum": ["DELETE"]}, "DocumentDetailList": {"type": "list", "member": {"shape": "DocumentDetails"}}, "DocumentDetails": {"type": "structure", "members": {"documentId": {"shape": "DocumentId", "documentation": "<p>The identifier of the document.</p>"}, "status": {"shape": "DocumentStatus", "documentation": "<p>The current status of the document.</p>"}, "error": {"shape": "ErrorDetail", "documentation": "<p>An error message associated with the document.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The timestamp for when the document was created.</p>"}, "updatedAt": {"shape": "Timestamp", "documentation": "<p>The timestamp for when the document was last updated.</p>"}}, "documentation": "<p>The details of a document within an Amazon Q Business index.</p>"}, "DocumentEnrichmentConditionOperator": {"type": "string", "enum": ["GREATER_THAN", "GREATER_THAN_OR_EQUALS", "LESS_THAN", "LESS_THAN_OR_EQUALS", "EQUALS", "NOT_EQUALS", "CONTAINS", "NOT_CONTAINS", "EXISTS", "NOT_EXISTS", "BEGINS_WITH"]}, "DocumentEnrichmentConfiguration": {"type": "structure", "members": {"inlineConfigurations": {"shape": "InlineDocumentEnrichmentConfigurations", "documentation": "<p>Configuration information to alter document attributes or metadata fields and content when ingesting documents into Amazon Q Business.</p>"}, "preExtractionHookConfiguration": {"shape": "HookConfiguration"}, "postExtractionHookConfiguration": {"shape": "HookConfiguration"}}, "documentation": "<p>Provides the configuration information for altering document metadata and content during the document ingestion process.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/amazonq/latest/business-use-dg/custom-document-enrichment.html\">Custom document enrichment</a>.</p>"}, "DocumentId": {"type": "string", "max": 1825, "min": 1, "pattern": "\\P{C}*"}, "DocumentMetadataConfigurationName": {"type": "string", "max": 30, "min": 1, "pattern": "[a-zA-Z0-9_][a-zA-Z0-9_-]*"}, "DocumentStatus": {"type": "string", "enum": ["RECEIVED", "PROCESSING", "INDEXED", "UPDATED", "FAILED", "DELETING", "DELETED", "DOCUMENT_FAILED_TO_INDEX"]}, "Documents": {"type": "list", "member": {"shape": "Document"}, "max": 10, "min": 1}, "EligibleDataSource": {"type": "structure", "members": {"indexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index the data source is attached to.</p>"}, "dataSourceId": {"shape": "DataSourceId", "documentation": "<p>The identifier of the data source.</p>"}}, "documentation": "<p>The identifier of the data source Amazon Q Business will generate responses from.</p>"}, "EligibleDataSources": {"type": "list", "member": {"shape": "EligibleDataSource"}, "max": 5, "min": 0}, "EncryptionConfiguration": {"type": "structure", "members": {"kmsKeyId": {"shape": "KmsKeyId", "documentation": "<p>The identifier of the KMS key. Amazon Q Business doesn't support asymmetric keys.</p>"}}, "documentation": "<p>Provides the identifier of the KMS key used to encrypt data indexed by Amazon Q Business. Amazon Q Business doesn't support asymmetric keys.</p>"}, "EndOfInputEvent": {"type": "structure", "members": {}, "documentation": "<p>The end of the streaming input for the <code>Chat</code> API.</p>", "event": true}, "ErrorCode": {"type": "string", "enum": ["InternalError", "InvalidRequest", "ResourceInactive", "ResourceNotFound"]}, "ErrorDetail": {"type": "structure", "members": {"errorMessage": {"shape": "ErrorMessage", "documentation": "<p>The message explaining the Amazon Q Business request error.</p>"}, "errorCode": {"shape": "ErrorCode", "documentation": "<p>The code associated with the Amazon Q Business request error.</p>"}}, "documentation": "<p>Provides information about a Amazon Q Business request error.</p>"}, "ErrorMessage": {"type": "string", "max": 2048, "min": 1, "pattern": "[\\s\\S]*"}, "ExampleChatMessage": {"type": "string", "max": 350, "min": 0, "pattern": "\\P{C}*"}, "ExampleChatMessages": {"type": "list", "member": {"shape": "ExampleChatMessage"}, "max": 5, "min": 0}, "ExecutionId": {"type": "string", "max": 36, "min": 36, "pattern": "[a-zA-Z0-9][a-zA-Z0-9-]{35}"}, "ExternalResourceException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>An external resource that you configured with your application is returning errors and preventing this operation from succeeding. Fix those errors and try again. </p>", "error": {"httpStatusCode": 424, "senderFault": true}, "exception": true}, "FailedAttachmentEvent": {"type": "structure", "members": {"conversationId": {"shape": "ConversationId", "documentation": "<p> The identifier of the conversation associated with the failed file upload.</p>"}, "userMessageId": {"shape": "MessageId", "documentation": "<p>The identifier of the end user chat message associated with the file upload.</p>"}, "systemMessageId": {"shape": "MessageId", "documentation": "<p>The identifier of the AI-generated message associated with the file upload.</p>"}, "attachment": {"shape": "AttachmentOutput"}}, "documentation": "<p>A failed file upload during web experience chat.</p>", "event": true}, "FailedDocument": {"type": "structure", "members": {"id": {"shape": "DocumentId", "documentation": "<p>The identifier of the document that couldn't be removed from the Amazon Q Business index.</p>"}, "error": {"shape": "ErrorDetail", "documentation": "<p>An explanation for why the document couldn't be removed from the index.</p>"}, "dataSourceId": {"shape": "DataSourceId", "documentation": "<p>The identifier of the Amazon Q Business data source connector that contains the failed document.</p>"}}, "documentation": "<p>A list of documents that could not be removed from an Amazon Q Business index. Each entry contains an error message that indicates why the document couldn't be removed from the index.</p>"}, "FailedDocuments": {"type": "list", "member": {"shape": "FailedDocument"}}, "FaviconUrl": {"type": "string", "max": 1284, "min": 0, "pattern": "(https?://[a-zA-Z0-9-_.+%/]+\\.(svg|ico))?"}, "FontUrl": {"type": "string", "max": 1284, "min": 0, "pattern": "(https?://[a-zA-Z0-9-_.+%/]+\\.(ttf|woff|woff2|otf))?"}, "GetApplicationRequest": {"type": "structure", "required": ["applicationId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the Amazon Q Business application.</p>", "location": "uri", "locationName": "applicationId"}}}, "GetApplicationResponse": {"type": "structure", "members": {"displayName": {"shape": "ApplicationName", "documentation": "<p>The name of the Amazon Q Business application.</p>"}, "applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the Amazon Q Business application.</p>"}, "applicationArn": {"shape": "ApplicationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the Amazon Q Business application.</p>"}, "identityType": {"shape": "IdentityType", "documentation": "<p>The authentication type being used by a Amazon Q Business application.</p>"}, "iamIdentityProviderArn": {"shape": "IAMIdentityProviderArn", "documentation": "<p>The Amazon Resource Name (ARN) of an identity provider being used by an Amazon Q Business application.</p>"}, "identityCenterApplicationArn": {"shape": "IdcApplicationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the AWS IAM Identity Center instance attached to your Amazon Q Business application.</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM with permissions to access your CloudWatch logs and metrics.</p>"}, "status": {"shape": "ApplicationStatus", "documentation": "<p>The status of the Amazon Q Business application.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description for the Amazon Q Business application.</p>"}, "encryptionConfiguration": {"shape": "EncryptionConfiguration", "documentation": "<p>The identifier of the Amazon Web Services KMS key that is used to encrypt your data. Amazon Q Business doesn't support asymmetric keys.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when the Amazon Q Business application was last updated.</p>"}, "updatedAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when the Amazon Q Business application was last updated.</p>"}, "error": {"shape": "ErrorDetail", "documentation": "<p>If the <code>Status</code> field is set to <code>ERROR</code>, the <code>ErrorMessage</code> field contains a description of the error that caused the synchronization to fail.</p>"}, "attachmentsConfiguration": {"shape": "AppliedAttachmentsConfiguration", "documentation": "<p>Settings for whether end users can upload files directly during chat.</p>"}, "qAppsConfiguration": {"shape": "QAppsConfiguration", "documentation": "<p>Settings for whether end users can create and use Amazon Q Apps in the web experience.</p>"}, "personalizationConfiguration": {"shape": "PersonalizationConfiguration", "documentation": "<p>Configuration information about chat response personalization. For more information, see <a href=\"https://docs.aws.amazon.com/amazonq/latest/qbusiness-ug/personalizing-chat-responses.html\">Personalizing chat responses</a>.</p>"}, "autoSubscriptionConfiguration": {"shape": "AutoSubscriptionConfiguration", "documentation": "<p>Settings for auto-subscription behavior for this application. This is only applicable to SAML and OIDC applications.</p>"}, "clientIdsForOIDC": {"shape": "ClientIdsForOIDC", "documentation": "<p>The OIDC client ID for a Amazon Q Business application.</p>"}, "quickSightConfiguration": {"shape": "QuickSightConfiguration", "documentation": "<p>The Amazon QuickSight authentication configuration for the Amazon Q Business application.</p>"}}}, "GetChatControlsConfigurationRequest": {"type": "structure", "required": ["applicationId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the application for which the chat controls are configured.</p>", "location": "uri", "locationName": "applicationId"}, "maxResults": {"shape": "MaxResultsIntegerForGetTopicConfigurations", "documentation": "<p>The maximum number of configured chat controls to return.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the <code>maxResults</code> response was incomplete because there is more data to retrieve, Amazon Q Business returns a pagination token in the response. You can use this pagination token to retrieve the next set of Amazon Q Business chat controls configured.</p>", "location": "querystring", "locationName": "nextToken"}}}, "GetChatControlsConfigurationResponse": {"type": "structure", "members": {"responseScope": {"shape": "ResponseScope", "documentation": "<p>The response scope configured for a Amazon Q Business application. This determines whether your application uses its retrieval augmented generation (RAG) system to generate answers only from your enterprise data, or also uses the large language models (LLM) knowledge to respons to end user questions in chat.</p>"}, "orchestrationConfiguration": {"shape": "AppliedOrchestrationConfiguration", "documentation": "<p> The chat response orchestration settings for your application.</p> <note> <p>Chat orchestration is optimized to work for English language content. For more details on language support in Amazon Q Business, see <a href=\"https://docs.aws.amazon.com/amazonq/latest/qbusiness-ug/supported-languages.html\">Supported languages</a>.</p> </note>"}, "blockedPhrases": {"shape": "BlockedPhrasesConfiguration", "documentation": "<p>The phrases blocked from chat by your chat control configuration.</p>"}, "topicConfigurations": {"shape": "TopicConfigurations", "documentation": "<p>The topic specific controls configured for a Amazon Q Business application.</p>"}, "creatorModeConfiguration": {"shape": "AppliedCreatorModeConfiguration", "documentation": "<p>The configuration details for <code>CREATOR_MODE</code>.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the <code>maxResults</code> response was incomplete because there is more data to retrieve, Amazon Q Business returns a pagination token in the response. You can use this pagination token to retrieve the next set of Amazon Q Business chat controls configured.</p>"}, "hallucinationReductionConfiguration": {"shape": "HallucinationReductionConfiguration", "documentation": "<p> The hallucination reduction settings for your application.</p>"}}}, "GetChatResponseConfigurationRequest": {"type": "structure", "required": ["applicationId", "chatResponseConfigurationId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The unique identifier of the Amazon Q Business application containing the chat response configuration to retrieve.</p>", "location": "uri", "locationName": "applicationId"}, "chatResponseConfigurationId": {"shape": "ChatResponseConfigurationId", "documentation": "<p>The unique identifier of the chat response configuration to retrieve from the specified application.</p>", "location": "uri", "locationName": "chatResponseConfigurationId"}}}, "GetChatResponseConfigurationResponse": {"type": "structure", "members": {"chatResponseConfigurationId": {"shape": "ChatResponseConfigurationId", "documentation": "<p>The unique identifier of the retrieved chat response configuration.</p>"}, "chatResponseConfigurationArn": {"shape": "ChatResponseConfigurationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the retrieved chat response configuration, which uniquely identifies the resource across all Amazon Web Services services. </p>"}, "displayName": {"shape": "DisplayName", "documentation": "<p>The human-readable name of the retrieved chat response configuration, making it easier to identify among multiple configurations.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The timestamp indicating when the chat response configuration was initially created.</p>"}, "inUseConfiguration": {"shape": "ChatResponseConfigurationDetail", "documentation": "<p>The currently active configuration settings that are being used to generate responses in the Amazon Q Business application.</p>"}, "lastUpdateConfiguration": {"shape": "ChatResponseConfigurationDetail", "documentation": "<p>Information about the most recent update to the configuration, including timestamp and modification details.</p>"}}}, "GetDataAccessorRequest": {"type": "structure", "required": ["applicationId", "dataAccessorId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The unique identifier of the Amazon Q Business application.</p>", "location": "uri", "locationName": "applicationId"}, "dataAccessorId": {"shape": "DataAccessorId", "documentation": "<p>The unique identifier of the data accessor to retrieve.</p>", "location": "uri", "locationName": "dataAccessorId"}}}, "GetDataAccessorResponse": {"type": "structure", "members": {"displayName": {"shape": "DataAccessorName", "documentation": "<p>The friendly name of the data accessor.</p>"}, "dataAccessorId": {"shape": "DataAccessorId", "documentation": "<p>The unique identifier of the data accessor.</p>"}, "dataAccessorArn": {"shape": "DataAccessorArn", "documentation": "<p>The Amazon Resource Name (ARN) of the data accessor.</p>"}, "applicationId": {"shape": "ApplicationId", "documentation": "<p>The unique identifier of the Amazon Q Business application associated with this data accessor.</p>"}, "idcApplicationArn": {"shape": "IdcApplicationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM Identity Center application associated with this data accessor.</p>"}, "principal": {"shape": "PrincipalRoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role for the ISV associated with this data accessor.</p>"}, "actionConfigurations": {"shape": "ActionConfigurationList", "documentation": "<p>The list of action configurations specifying the allowed actions and any associated filters.</p>"}, "authenticationDetail": {"shape": "DataAccessorAuthenticationDetail", "documentation": "<p>The authentication configuration details for the data accessor. This specifies how the ISV authenticates when accessing data through this data accessor.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The timestamp when the data accessor was created.</p>"}, "updatedAt": {"shape": "Timestamp", "documentation": "<p>The timestamp when the data accessor was last updated.</p>"}}}, "GetDataSourceRequest": {"type": "structure", "required": ["applicationId", "indexId", "dataSourceId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the Amazon Q Business application.</p>", "location": "uri", "locationName": "applicationId"}, "indexId": {"shape": "IndexId", "documentation": "<p>The identfier of the index used with the data source connector.</p>", "location": "uri", "locationName": "indexId"}, "dataSourceId": {"shape": "DataSourceId", "documentation": "<p>The identifier of the data source connector.</p>", "location": "uri", "locationName": "dataSourceId"}}}, "GetDataSourceResponse": {"type": "structure", "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the Amazon Q Business application.</p>"}, "indexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index linked to the data source connector.</p>"}, "dataSourceId": {"shape": "DataSourceId", "documentation": "<p>The identifier of the data source connector.</p>"}, "dataSourceArn": {"shape": "DataSourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the data source.</p>"}, "displayName": {"shape": "DataSourceName", "documentation": "<p>The name for the data source connector.</p>"}, "type": {"shape": "String", "documentation": "<p>The type of the data source connector. For example, <code>S3</code>.</p>"}, "configuration": {"shape": "DataSourceConfiguration", "documentation": "<p>The details of how the data source connector is configured.</p>"}, "vpcConfiguration": {"shape": "DataSourceVpcConfiguration", "documentation": "<p>Configuration information for an Amazon VPC (Virtual Private Cloud) to connect to your data source.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when the data source connector was created.</p>"}, "updatedAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when the data source connector was last updated.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description for the data source connector.</p>"}, "status": {"shape": "DataSourceStatus", "documentation": "<p>The current status of the data source connector. When the <code>Status</code> field value is <code>FAILED</code>, the <code>ErrorMessage</code> field contains a description of the error that caused the data source connector to fail.</p>"}, "syncSchedule": {"shape": "SyncSchedule", "documentation": "<p>The schedule for Amazon Q Business to update the index.</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the role with permission to access the data source and required resources.</p>"}, "error": {"shape": "ErrorDetail", "documentation": "<p>When the <code>Status</code> field value is <code>FAILED</code>, the <code>ErrorMessage</code> field contains a description of the error that caused the data source connector to fail.</p>"}, "documentEnrichmentConfiguration": {"shape": "DocumentEnrichmentConfiguration"}, "mediaExtractionConfiguration": {"shape": "MediaExtractionConfiguration", "documentation": "<p>The configuration for extracting information from media in documents for the data source. </p>"}}}, "GetGroupRequest": {"type": "structure", "required": ["applicationId", "indexId", "groupName"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the application id the group is attached to.</p>", "location": "uri", "locationName": "applicationId"}, "indexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index the group is attached to.</p>", "location": "uri", "locationName": "indexId"}, "groupName": {"shape": "GroupName", "documentation": "<p>The name of the group.</p>", "location": "uri", "locationName": "groupName"}, "dataSourceId": {"shape": "DataSourceId", "documentation": "<p>The identifier of the data source the group is attached to.</p>", "location": "querystring", "locationName": "dataSourceId"}}}, "GetGroupResponse": {"type": "structure", "members": {"status": {"shape": "GroupStatusDetail", "documentation": "<p>The current status of the group.</p>"}, "statusHistory": {"shape": "GroupStatusDetails", "documentation": "<p>The status history of the group.</p>"}}}, "GetIndexRequest": {"type": "structure", "required": ["applicationId", "indexId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the Amazon Q Business application connected to the index.</p>", "location": "uri", "locationName": "applicationId"}, "indexId": {"shape": "IndexId", "documentation": "<p>The identifier of the Amazon Q Business index you want information on.</p>", "location": "uri", "locationName": "indexId"}}}, "GetIndexResponse": {"type": "structure", "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the Amazon Q Business application associated with the index.</p>"}, "indexId": {"shape": "IndexId", "documentation": "<p>The identifier of the Amazon Q Business index.</p>"}, "displayName": {"shape": "IndexName", "documentation": "<p>The name of the Amazon Q Business index.</p>"}, "indexArn": {"shape": "IndexArn", "documentation": "<p> The Amazon Resource Name (ARN) of the Amazon Q Business index. </p>"}, "status": {"shape": "IndexStatus", "documentation": "<p>The current status of the index. When the value is <code>ACTIVE</code>, the index is ready for use. If the <code>Status</code> field value is <code>FAILED</code>, the <code>ErrorMessage</code> field contains a message that explains why.</p>"}, "type": {"shape": "IndexType", "documentation": "<p>The type of index attached to your Amazon Q Business application.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description for the Amazon Q Business index.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when the Amazon Q Business index was created.</p>"}, "updatedAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when the Amazon Q Business index was last updated.</p>"}, "capacityConfiguration": {"shape": "IndexCapacityConfiguration", "documentation": "<p>The storage capacity units chosen for your Amazon Q Business index.</p>"}, "documentAttributeConfigurations": {"shape": "DocumentAttributeConfigurations", "documentation": "<p>Configuration information for document attributes or metadata. Document metadata are fields associated with your documents. For example, the company department name associated with each document. For more information, see <a href=\"https://docs.aws.amazon.com/amazonq/latest/business-use-dg/doc-attributes-types.html#doc-attributes\">Understanding document attributes</a>.</p>"}, "error": {"shape": "ErrorDetail", "documentation": "<p>When the <code>Status</code> field value is <code>FAILED</code>, the <code>ErrorMessage</code> field contains a message that explains why.</p>"}, "indexStatistics": {"shape": "IndexStatistics", "documentation": "<p>Provides information about the number of documents indexed.</p>"}}}, "GetMediaRequest": {"type": "structure", "required": ["applicationId", "conversationId", "messageId", "mediaId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the Amazon Q Business which contains the media object.</p>", "location": "uri", "locationName": "applicationId"}, "conversationId": {"shape": "ConversationId", "documentation": "<p>The identifier of the Amazon Q Business conversation.</p>", "location": "uri", "locationName": "conversationId"}, "messageId": {"shape": "MessageId", "documentation": "<p>The identifier of the Amazon Q Business message.</p>", "location": "uri", "locationName": "messageId"}, "mediaId": {"shape": "MediaId", "documentation": "<p>The identifier of the media object. You can find this in the <code>sourceAttributions</code> returned by the <code>Chat</code>, <code>ChatSync</code>, and <code>ListMessages</code> API responses.</p>", "location": "uri", "locationName": "mediaId"}}}, "GetMediaResponse": {"type": "structure", "members": {"mediaBytes": {"shape": "Blob", "documentation": "<p>The base64-encoded bytes of the media object.</p>"}, "mediaMimeType": {"shape": "String", "documentation": "<p>The MIME type of the media object (image/png).</p>"}}}, "GetPluginRequest": {"type": "structure", "required": ["applicationId", "pluginId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the application which contains the plugin.</p>", "location": "uri", "locationName": "applicationId"}, "pluginId": {"shape": "PluginId", "documentation": "<p>The identifier of the plugin.</p>", "location": "uri", "locationName": "pluginId"}}}, "GetPluginResponse": {"type": "structure", "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the application which contains the plugin.</p>"}, "pluginId": {"shape": "PluginId", "documentation": "<p>The identifier of the plugin.</p>"}, "displayName": {"shape": "PluginName", "documentation": "<p>The name of the plugin.</p>"}, "type": {"shape": "PluginType", "documentation": "<p>The type of the plugin.</p>"}, "serverUrl": {"shape": "Url", "documentation": "<p>The source URL used for plugin configuration.</p>"}, "authConfiguration": {"shape": "PluginAuthConfiguration"}, "customPluginConfiguration": {"shape": "CustomPluginConfiguration", "documentation": "<p>Configuration information required to create a custom plugin.</p>"}, "buildStatus": {"shape": "PluginBuildStatus", "documentation": "<p>The current status of a plugin. A plugin is modified asynchronously.</p>"}, "pluginArn": {"shape": "PluginArn", "documentation": "<p>The Amazon Resource Name (ARN) of the role with permission to access resources needed to create the plugin.</p>"}, "state": {"shape": "PluginState", "documentation": "<p>The current state of the plugin.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The timestamp for when the plugin was created.</p>"}, "updatedAt": {"shape": "Timestamp", "documentation": "<p>The timestamp for when the plugin was last updated.</p>"}}}, "GetPolicyRequest": {"type": "structure", "required": ["applicationId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The unique identifier of the Amazon Q Business application.</p>", "location": "uri", "locationName": "applicationId"}}}, "GetPolicyResponse": {"type": "structure", "members": {"policy": {"shape": "String", "documentation": "<p>The JSON representation of the permission policy.</p>"}}}, "GetRetrieverRequest": {"type": "structure", "required": ["applicationId", "retrieverId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the Amazon Q Business application using the retriever.</p>", "location": "uri", "locationName": "applicationId"}, "retrieverId": {"shape": "RetrieverId", "documentation": "<p>The identifier of the retriever.</p>", "location": "uri", "locationName": "retrieverId"}}}, "GetRetrieverResponse": {"type": "structure", "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the Amazon Q Business application using the retriever. </p>"}, "retrieverId": {"shape": "RetrieverId", "documentation": "<p>The identifier of the retriever.</p>"}, "retrieverArn": {"shape": "RetrieverArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role associated with the retriever.</p>"}, "type": {"shape": "RetrieverType", "documentation": "<p>The type of the retriever.</p>"}, "status": {"shape": "RetrieverStatus", "documentation": "<p>The status of the retriever.</p>"}, "displayName": {"shape": "RetrieverName", "documentation": "<p>The name of the retriever.</p>"}, "configuration": {"shape": "RetrieverConfiguration"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the role with the permission to access the retriever and required resources.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when the retriever was created.</p>"}, "updatedAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when the retriever was last updated.</p>"}}}, "GetUserRequest": {"type": "structure", "required": ["applicationId", "userId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the application connected to the user.</p>", "location": "uri", "locationName": "applicationId"}, "userId": {"shape": "String", "documentation": "<p>The user email address attached to the user.</p>", "location": "uri", "locationName": "userId"}}}, "GetUserResponse": {"type": "structure", "members": {"userAliases": {"shape": "UserAliases", "documentation": "<p>A list of user aliases attached to a user.</p>"}}}, "GetWebExperienceRequest": {"type": "structure", "required": ["applicationId", "webExperienceId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the Amazon Q Business application linked to the web experience.</p>", "location": "uri", "locationName": "applicationId"}, "webExperienceId": {"shape": "WebExperienceId", "documentation": "<p>The identifier of the Amazon Q Business web experience. </p>", "location": "uri", "locationName": "webExperienceId"}}}, "GetWebExperienceResponse": {"type": "structure", "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the Amazon Q Business application linked to the web experience.</p>"}, "webExperienceId": {"shape": "WebExperienceId", "documentation": "<p>The identifier of the Amazon Q Business web experience.</p>"}, "webExperienceArn": {"shape": "WebExperienceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the role with the permission to access the Amazon Q Business web experience and required resources.</p>"}, "defaultEndpoint": {"shape": "Url", "documentation": "<p>The endpoint of your Amazon Q Business web experience.</p>"}, "status": {"shape": "WebExperienceStatus", "documentation": "<p>The current status of the Amazon Q Business web experience. When the <code>Status</code> field value is <code>FAILED</code>, the <code>ErrorMessage</code> field contains a description of the error that caused the data source connector to fail. </p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when the Amazon Q Business web experience was last created.</p>"}, "updatedAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when the Amazon Q Business web experience was last updated.</p>"}, "title": {"shape": "WebExperienceTitle", "documentation": "<p>The title for your Amazon Q Business web experience. </p>"}, "subtitle": {"shape": "WebExperienceSubtitle", "documentation": "<p>The subtitle for your Amazon Q Business web experience. </p>"}, "welcomeMessage": {"shape": "WebExperienceWelcomeMessage", "documentation": "<p>The customized welcome message for end users of an Amazon Q Business web experience.</p>"}, "samplePromptsControlMode": {"shape": "WebExperienceSamplePromptsControlMode", "documentation": "<p>Determines whether sample prompts are enabled in the web experience for an end user.</p>"}, "origins": {"shape": "WebExperienceOrigins", "documentation": "<p>Gets the website domain origins that are allowed to embed the Amazon Q Business web experience. The <i>domain origin</i> refers to the base URL for accessing a website including the protocol (<code>http/https</code>), the domain name, and the port number (if specified). </p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p> The Amazon Resource Name (ARN) of the service role attached to your web experience.</p>"}, "identityProviderConfiguration": {"shape": "IdentityProviderConfiguration", "documentation": "<p>Information about the identity provider (IdP) used to authenticate end users of an Amazon Q Business web experience.</p>"}, "authenticationConfiguration": {"shape": "WebExperienceAuthConfiguration", "documentation": "<p>The authentication configuration information for your Amazon Q Business web experience.</p>", "deprecated": true, "deprecatedMessage": "Property associated with legacy SAML IdP flow. Deprecated in favor of using AWS IAM Identity Center for user management."}, "error": {"shape": "ErrorDetail", "documentation": "<p>When the <code>Status</code> field value is <code>FAILED</code>, the <code>ErrorMessage</code> field contains a description of the error that caused the data source connector to fail.</p>"}, "browserExtensionConfiguration": {"shape": "BrowserExtensionConfiguration", "documentation": "<p>The browser extension configuration for an Amazon Q Business web experience.</p>"}, "customizationConfiguration": {"shape": "CustomizationConfiguration", "documentation": "<p>Gets the custom logo, favicon, font, and color used in the Amazon Q web experience. </p>"}}}, "GroupIdentifier": {"type": "string", "max": 47, "min": 1, "pattern": "([0-9a-f]{10}-|)[A-Fa-f0-9]{8}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{12}"}, "GroupMembers": {"type": "structure", "members": {"memberGroups": {"shape": "MemberGroups", "documentation": "<p>A list of sub groups that belong to a group. For example, the sub groups \"Research\", \"Engineering\", and \"Sales and Marketing\" all belong to the group \"Company\".</p>"}, "memberUsers": {"shape": "MemberUsers", "documentation": "<p>A list of users that belong to a group. For example, a list of interns all belong to the \"Interns\" group.</p>"}, "s3PathForGroupMembers": {"shape": "S3"}}, "documentation": "<p>A list of users or sub groups that belong to a group. This is for generating Amazon Q Business chat results only from document a user has access to.</p>"}, "GroupName": {"type": "string", "max": 1024, "min": 1, "pattern": "\\P{C}*"}, "GroupStatus": {"type": "string", "enum": ["FAILED", "SUCCEEDED", "PROCESSING", "DELETING", "DELETED"]}, "GroupStatusDetail": {"type": "structure", "members": {"status": {"shape": "GroupStatus", "documentation": "<p>The status of a group.</p>"}, "lastUpdatedAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when the Amazon Q Business application was last updated.</p>"}, "errorDetail": {"shape": "ErrorDetail", "documentation": "<p>The details of an error associated a group status.</p>"}}, "documentation": "<p>Provides the details of a group's status.</p>"}, "GroupStatusDetails": {"type": "list", "member": {"shape": "GroupStatusDetail"}}, "GroupSummary": {"type": "structure", "members": {"groupName": {"shape": "GroupName", "documentation": "<p>The name of the group the summary information is for.</p>"}}, "documentation": "<p>Summary information for groups.</p>"}, "GroupSummaryList": {"type": "list", "member": {"shape": "GroupSummary"}}, "HallucinationReductionConfiguration": {"type": "structure", "members": {"hallucinationReductionControl": {"shape": "HallucinationReductionControl", "documentation": "<p>Controls whether hallucination reduction has been enabled or disabled for your application. The default status is <code>DISABLED</code>. </p>"}}, "documentation": "<p>Configuration information required to setup hallucination reduction. For more information, see <a href=\"https://docs.aws.amazon.com/amazonq/latest/qbusiness-ug/hallucination-reduction.html\"> hallucination reduction</a>.</p> <note> <p>The hallucination reduction feature won't work if chat orchestration controls are enabled for your application.</p> </note>"}, "HallucinationReductionControl": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "HookConfiguration": {"type": "structure", "members": {"invocationCondition": {"shape": "DocumentAttributeCondition", "documentation": "<p>The condition used for when a Lambda function should be invoked.</p> <p>For example, you can specify a condition that if there are empty date-time values, then Amazon Q Business should invoke a function that inserts the current date-time.</p>"}, "lambdaArn": {"shape": "LambdaArn", "documentation": "<p>The Amazon Resource Name (ARN) of the Lambda function during ingestion. For more information, see <a href=\"https://docs.aws.amazon.com/amazonq/latest/qbusiness-ug/cde-lambda-operations.html\">Using Lambda functions for Amazon Q Business document enrichment</a>.</p>"}, "s3BucketName": {"shape": "S3BucketName", "documentation": "<p>Stores the original, raw documents or the structured, parsed documents before and after altering them. For more information, see <a href=\"https://docs.aws.amazon.com/amazonq/latest/business-use-dg/cde-lambda-operations.html#cde-lambda-operations-data-contracts\">Data contracts for Lambda functions</a>.</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of a role with permission to run <code>PreExtractionHookConfiguration</code> and <code>PostExtractionHookConfiguration</code> for altering document metadata and content during the document ingestion process.</p>"}}, "documentation": "<p>Provides the configuration information for invoking a Lambda function in Lambda to alter document metadata and content when ingesting documents into Amazon Q Business.</p> <p>You can configure your Lambda function using the <code>PreExtractionHookConfiguration</code> parameter if you want to apply advanced alterations on the original or raw documents.</p> <p>If you want to apply advanced alterations on the Amazon Q Business structured documents, you must configure your Lambda function using <code>PostExtractionHookConfiguration</code>.</p> <p>You can only invoke one Lambda function. However, this function can invoke other functions it requires.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/amazonq/latest/business-use-dg/custom-document-enrichment.html\">Custom document enrichment</a>. </p>"}, "IAMIdentityProviderArn": {"type": "string", "max": 2048, "min": 20, "pattern": "arn:aws:iam::\\d{12}:(oidc-provider|saml-provider)/[a-zA-Z0-9_\\.\\/@\\-]+"}, "IdcApplicationArn": {"type": "string", "max": 1224, "min": 10, "pattern": "arn:(aws|aws-us-gov|aws-cn|aws-iso|aws-iso-b):sso::\\d{12}:application/(sso)?ins-[a-zA-Z0-9-.]{16}/apl-[a-zA-Z0-9]{16}"}, "IdcAuthConfiguration": {"type": "structure", "required": ["idcApplicationArn", "roleArn"], "members": {"idcApplicationArn": {"shape": "IdcApplicationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM Identity Center Application used to configure authentication.</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role with permissions to perform actions on Amazon Web Services services on your behalf.</p>"}}, "documentation": "<p>Information about the IAM Identity Center Application used to configure authentication for a plugin.</p>"}, "IdcTrustedTokenIssuerArn": {"type": "string", "max": 1284, "min": 0, "pattern": "arn:aws:sso::[0-9]{12}:trustedTokenIssuer/(sso)?ins-[a-zA-Z0-9-.]{16}/tti-[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}"}, "IdentityProviderConfiguration": {"type": "structure", "members": {"samlConfiguration": {"shape": "SamlProviderConfiguration"}, "openIDConnectConfiguration": {"shape": "OpenIDConnectProviderConfiguration"}}, "documentation": "<p>Provides information about the identity provider (IdP) used to authenticate end users of an Amazon Q Business web experience.</p>", "union": true}, "IdentityType": {"type": "string", "enum": ["AWS_IAM_IDP_SAML", "AWS_IAM_IDP_OIDC", "AWS_IAM_IDC", "AWS_QUICKSIGHT_IDP", "ANONYMOUS"]}, "ImageExtractionConfiguration": {"type": "structure", "required": ["imageExtractionStatus"], "members": {"imageExtractionStatus": {"shape": "ImageExtractionStatus", "documentation": "<p>Specify whether to extract semantic meaning from images and visuals from documents.</p>"}}, "documentation": "<p>The configuration for extracting semantic meaning from images in documents. For more information, see <a href=\"https://docs.aws.amazon.com/amazonq/latest/qbusiness-ug/extracting-meaning-from-images.html\">Extracting semantic meaning from images and visuals</a>.</p>"}, "ImageExtractionStatus": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "ImageSourceDetails": {"type": "structure", "members": {"mediaId": {"shape": "MediaId", "documentation": "<p>Unique identifier for the image file.</p>"}, "mediaMimeType": {"shape": "String", "documentation": "<p>The MIME type of the image file.</p>"}}, "documentation": "<p>Details about an image source, including its identifier and format.</p>"}, "Index": {"type": "structure", "members": {"displayName": {"shape": "IndexName", "documentation": "<p>The name of the index.</p>"}, "indexId": {"shape": "IndexId", "documentation": "<p>The identifier for the index.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when the index was created.</p>"}, "updatedAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when the index was last updated.</p>"}, "status": {"shape": "IndexStatus", "documentation": "<p>The current status of the index. When the status is <code>ACTIVE</code>, the index is ready.</p>"}}, "documentation": "<p>Summary information for your Amazon Q Business index.</p>"}, "IndexArn": {"type": "string", "max": 1284, "min": 0, "pattern": "arn:[a-z0-9-\\.]{1,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[^/].{0,1023}"}, "IndexCapacityConfiguration": {"type": "structure", "members": {"units": {"shape": "IndexCapacityInteger", "documentation": "<p>The number of storage units configured for an Amazon Q Business index.</p>"}}, "documentation": "<p>Provides information about index capacity configuration.</p>"}, "IndexCapacityInteger": {"type": "integer", "box": true, "min": 1}, "IndexId": {"type": "string", "max": 36, "min": 36, "pattern": "[a-zA-Z0-9][a-zA-Z0-9-]{35}"}, "IndexName": {"type": "string", "max": 1000, "min": 1, "pattern": "[a-zA-Z0-9][a-zA-Z0-9_-]*"}, "IndexStatistics": {"type": "structure", "members": {"textDocumentStatistics": {"shape": "TextDocumentStatistics", "documentation": "<p>The number of documents indexed.</p>"}}, "documentation": "<p>Provides information about the number of documents in an index.</p>"}, "IndexStatus": {"type": "string", "enum": ["CREATING", "ACTIVE", "DELETING", "FAILED", "UPDATING"]}, "IndexType": {"type": "string", "enum": ["ENTERPRISE", "STARTER"]}, "IndexedTextBytes": {"type": "long", "box": true, "min": 0}, "IndexedTextDocument": {"type": "integer", "box": true, "min": 0}, "Indices": {"type": "list", "member": {"shape": "Index"}}, "InlineDocumentEnrichmentConfiguration": {"type": "structure", "members": {"condition": {"shape": "DocumentAttributeCondition"}, "target": {"shape": "DocumentAttributeTarget"}, "documentContentOperator": {"shape": "DocumentContentOperator", "documentation": "<p> <code>TRUE</code> to delete content if the condition used for the target attribute is met.</p>"}}, "documentation": "<p>Provides the configuration information for applying basic logic to alter document metadata and content when ingesting documents into Amazon Q Business.</p> <p>To apply advanced logic, to go beyond what you can do with basic logic, see <a href=\"https://docs.aws.amazon.com/amazonq/latest/api-reference/API_HookConfiguration.html\"> <code>HookConfiguration</code> </a>.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/amazonq/latest/business-use-dg/custom-document-enrichment.html\">Custom document enrichment</a>. </p>"}, "InlineDocumentEnrichmentConfigurations": {"type": "list", "member": {"shape": "InlineDocumentEnrichmentConfiguration"}, "max": 100, "min": 1}, "InstanceArn": {"type": "string", "max": 1224, "min": 10, "pattern": "arn:(aws|aws-us-gov|aws-cn|aws-iso|aws-iso-b):sso:::instance/(sso)?ins-[a-zA-Z0-9-.]{16}"}, "Instruction": {"type": "string", "max": 1000, "min": 5, "pattern": "[\\s\\S]*"}, "InstructionCollection": {"type": "structure", "members": {"responseLength": {"shape": "Instruction", "documentation": "<p>Specifies the desired length of responses generated by Amazon Q Business. This parameter allows administrators to control whether responses are concise and brief or more detailed and comprehensive.</p>"}, "targetAudience": {"shape": "Instruction", "documentation": "<p>Defines the intended audience for the responses, allowing Amazon Q Business to tailor its language, terminology, and explanations appropriately. This could range from technical experts to general users with varying levels of domain knowledge.</p>"}, "perspective": {"shape": "Instruction", "documentation": "<p>Determines the point of view or perspective from which Amazon Q Business generates responses, such as first-person, second-person, or third-person perspective, affecting how information is presented to users.</p>"}, "outputStyle": {"shape": "Instruction", "documentation": "<p>Specifies the formatting and structural style of responses, such as bullet points, paragraphs, step-by-step instructions, or other organizational formats that enhance readability and comprehension.</p>"}, "identity": {"shape": "Instruction", "documentation": "<p>Defines the persona or identity that Amazon Q Business should adopt when responding to users, allowing for customization of the assistant's character, role, or representation within an organization.</p>"}, "tone": {"shape": "Instruction", "documentation": "<p>Controls the emotional tone and communication style of responses, such as formal, casual, technical, friendly, or professional, to align with organizational communication standards and user expectations.</p>"}, "customInstructions": {"shape": "Instruction", "documentation": "<p>Allows administrators to provide specific, custom instructions that guide how Amazon Q Business should respond in particular scenarios or to certain types of queries, enabling fine-grained control over response generation.</p>"}, "examples": {"shape": "Instruction", "documentation": "<p>Provides sample responses or templates that Amazon Q Business can reference when generating responses, helping to establish consistent patterns and formats for different types of user queries.</p>"}}, "documentation": "<p>A set of instructions that define how Amazon Q Business should generate and format responses to user queries. This collection includes parameters for controlling response characteristics such as length, audience targeting, perspective, style, identity, tone, and custom instructions.</p>"}, "Integer": {"type": "integer", "box": true}, "InternalServerException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>An issue occurred with the internal server used for your Amazon Q Business service. Wait some minutes and try again, or contact <a href=\"http://aws.amazon.com/contact-us/\">Support</a> for help.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true}, "KendraIndexConfiguration": {"type": "structure", "required": ["indexId"], "members": {"indexId": {"shape": "KendraIndexId", "documentation": "<p>The identifier of the Amazon Kendra index.</p>"}}, "documentation": "<p>Stores an Amazon Kendra index as a retriever.</p>"}, "KendraIndexId": {"type": "string", "max": 36, "min": 36, "pattern": "[a-zA-Z0-9][a-zA-Z0-9-]{35}"}, "KmsKeyId": {"type": "string", "max": 2048, "min": 1, "sensitive": true}, "LambdaArn": {"type": "string", "max": 2048, "min": 1, "pattern": "arn:aws[a-zA-Z-]*:lambda:[a-z-]*-[0-9]:[0-9]{12}:function:[a-zA-Z0-9-_]+(/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})?(:[a-zA-Z0-9-_]+)?"}, "LicenseNotFoundException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>You don't have permissions to perform the action because your license is inactive. Ask your admin to activate your license and try again after your licence is active.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "ListApplicationsRequest": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>If the <code>maxResults</code> response was incomplete because there is more data to retrieve, Amazon Q Business returns a pagination token in the response. You can use this pagination token to retrieve the next set of Amazon Q Business applications.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResultsIntegerForListApplications", "documentation": "<p>The maximum number of Amazon Q Business applications to return.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListApplicationsResponse": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>If the response is truncated, Amazon Q Business returns this token. You can use this token in a subsequent request to retrieve the next set of applications.</p>"}, "applications": {"shape": "Applications", "documentation": "<p>An array of summary information on the configuration of one or more Amazon Q Business applications.</p>"}}}, "ListAttachmentsRequest": {"type": "structure", "required": ["applicationId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The unique identifier for the Amazon Q Business application.</p>", "location": "uri", "locationName": "applicationId"}, "conversationId": {"shape": "ConversationId", "documentation": "<p>The unique identifier of the Amazon Q Business web experience conversation.</p>", "location": "querystring", "locationName": "conversationId"}, "userId": {"shape": "UserId", "documentation": "<p>The unique identifier of the user involved in the Amazon Q Business web experience conversation.</p>", "location": "querystring", "locationName": "userId"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the number of attachments returned exceeds <code>maxResults</code>, Amazon Q Business returns a next token as a pagination token to retrieve the next set of attachments.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResultsIntegerForListAttachments", "documentation": "<p>The maximum number of attachements to return.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListAttachmentsResponse": {"type": "structure", "members": {"attachments": {"shape": "AttachmentList", "documentation": "<p>An array of information on one or more attachments.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the response is truncated, Amazon Q Business returns this token, which you can use in a later request to list the next set of attachments.</p>"}}}, "ListChatResponseConfigurationsRequest": {"type": "structure", "required": ["applicationId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The unique identifier of the Amazon Q Business application for which to list available chat response configurations.</p>", "location": "uri", "locationName": "applicationId"}, "maxResults": {"shape": "Integer", "documentation": "<p>The maximum number of chat response configurations to return in a single response. This parameter helps control pagination of results when many configurations exist.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A pagination token used to retrieve the next set of results when the number of configurations exceeds the specified <code>maxResults</code> value.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListChatResponseConfigurationsResponse": {"type": "structure", "members": {"chatResponseConfigurations": {"shape": "ChatResponseConfigurations", "documentation": "<p>A list of chat response configuration summaries, each containing key information about an available configuration in the specified application.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A pagination token that can be used in a subsequent request to retrieve additional chat response configurations if the results were truncated due to the <code>maxResults</code> parameter.</p>"}}}, "ListConversationsRequest": {"type": "structure", "required": ["applicationId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the Amazon Q Business application.</p>", "location": "uri", "locationName": "applicationId"}, "userId": {"shape": "UserId", "documentation": "<p>The identifier of the user involved in the Amazon Q Business web experience conversation. </p>", "location": "querystring", "locationName": "userId"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the <code>maxResults</code> response was incomplete because there is more data to retrieve, Amazon Q Business returns a pagination token in the response. You can use this pagination token to retrieve the next set of Amazon Q Business conversations.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResultsIntegerForListConversations", "documentation": "<p>The maximum number of Amazon Q Business conversations to return.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListConversationsResponse": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>If the response is truncated, Amazon Q Business returns this token, which you can use in a later request to list the next set of messages.</p>"}, "conversations": {"shape": "Conversations", "documentation": "<p>An array of summary information on the configuration of one or more Amazon Q Business web experiences.</p>"}}}, "ListDataAccessorsRequest": {"type": "structure", "required": ["applicationId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The unique identifier of the Amazon Q Business application.</p>", "location": "uri", "locationName": "applicationId"}, "nextToken": {"shape": "NextToken1500", "documentation": "<p>The token for the next set of results. (You received this token from a previous call.)</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResultsIntegerForListDataAccessors", "documentation": "<p>The maximum number of results to return in a single call.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListDataAccessorsResponse": {"type": "structure", "members": {"dataAccessors": {"shape": "DataAccessors", "documentation": "<p>The list of data accessors.</p>"}, "nextToken": {"shape": "NextToken1500", "documentation": "<p>The token to use to retrieve the next set of results, if there are any.</p>"}}}, "ListDataSourceSyncJobsRequest": {"type": "structure", "required": ["dataSourceId", "applicationId", "indexId"], "members": {"dataSourceId": {"shape": "DataSourceId", "documentation": "<p> The identifier of the data source connector.</p>", "location": "uri", "locationName": "dataSourceId"}, "applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the Amazon Q Business application connected to the data source.</p>", "location": "uri", "locationName": "applicationId"}, "indexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index used with the Amazon Q Business data source connector.</p>", "location": "uri", "locationName": "indexId"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the <code>maxResults</code> response was incpmplete because there is more data to retriever, Amazon Q Business returns a pagination token in the response. You can use this pagination token to retrieve the next set of responses.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResultsIntegerForListDataSourcesSyncJobs", "documentation": "<p>The maximum number of synchronization jobs to return in the response.</p>", "location": "querystring", "locationName": "maxResults"}, "startTime": {"shape": "Timestamp", "documentation": "<p> The start time of the data source connector sync. </p>", "location": "querystring", "locationName": "startTime"}, "endTime": {"shape": "Timestamp", "documentation": "<p> The end time of the data source connector sync.</p>", "location": "querystring", "locationName": "endTime"}, "statusFilter": {"shape": "DataSourceSyncJobStatus", "documentation": "<p>Only returns synchronization jobs with the <code>Status</code> field equal to the specified status.</p>", "location": "querystring", "locationName": "syncStatus"}}}, "ListDataSourceSyncJobsResponse": {"type": "structure", "members": {"history": {"shape": "DataSourceSyncJobs", "documentation": "<p>A history of synchronization jobs for the data source connector.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the response is truncated, Amazon Q Business returns this token. You can use this token in any subsequent request to retrieve the next set of jobs.</p>"}}}, "ListDataSourcesRequest": {"type": "structure", "required": ["applicationId", "indexId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the Amazon Q Business application linked to the data source connectors.</p>", "location": "uri", "locationName": "applicationId"}, "indexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index used with one or more data source connectors.</p>", "location": "uri", "locationName": "indexId"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the <code>maxResults</code> response was incomplete because there is more data to retrieve, Amazon Q Business returns a pagination token in the response. You can use this pagination token to retrieve the next set of Amazon Q Business data source connectors.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResultsIntegerForListDataSources", "documentation": "<p>The maximum number of data source connectors to return.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListDataSourcesResponse": {"type": "structure", "members": {"dataSources": {"shape": "DataSources", "documentation": "<p>An array of summary information for one or more data source connector.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the response is truncated, Amazon Q Business returns this token. You can use this token in a subsequent request to retrieve the next set of data source connectors.</p>"}}}, "ListDocumentsRequest": {"type": "structure", "required": ["applicationId", "indexId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the application id the documents are attached to.</p>", "location": "uri", "locationName": "applicationId"}, "indexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index the documents are attached to.</p>", "location": "uri", "locationName": "indexId"}, "dataSourceIds": {"shape": "DataSourceIds", "documentation": "<p>The identifier of the data sources the documents are attached to.</p>", "location": "querystring", "locationName": "dataSourceIds"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the <code>maxResults</code> response was incomplete because there is more data to retrieve, Amazon Q Business returns a pagination token in the response. You can use this pagination token to retrieve the next set of documents.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResultsIntegerForListDocuments", "documentation": "<p>The maximum number of documents to return.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListDocumentsResponse": {"type": "structure", "members": {"documentDetailList": {"shape": "DocumentDetailList", "documentation": "<p>A list of document details.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the <code>maxResults</code> response was incomplete because there is more data to retrieve, Amazon Q Business returns a pagination token in the response. You can use this pagination token to retrieve the next set of documents.</p>"}}}, "ListGroupsRequest": {"type": "structure", "required": ["applicationId", "indexId", "updatedEar<PERSON><PERSON>han"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the application for getting a list of groups mapped to users.</p>", "location": "uri", "locationName": "applicationId"}, "indexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index for getting a list of groups mapped to users.</p>", "location": "uri", "locationName": "indexId"}, "updatedEarlierThan": {"shape": "Timestamp", "documentation": "<p>The timestamp identifier used for the latest <code>PUT</code> or <code>DELETE</code> action for mapping users to their groups.</p>", "location": "querystring", "locationName": "updatedEar<PERSON><PERSON>han"}, "dataSourceId": {"shape": "DataSourceId", "documentation": "<p>The identifier of the data source for getting a list of groups mapped to users.</p>", "location": "querystring", "locationName": "dataSourceId"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the previous response was incomplete (because there is more data to retrieve), Amazon Q Business returns a pagination token in the response. You can use this pagination token to retrieve the next set of groups that are mapped to users.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResultsIntegerForListGroupsRequest", "documentation": "<p>The maximum number of returned groups that are mapped to users.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListGroupsResponse": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>If the response is truncated, Amazon Q Business returns this token that you can use in the subsequent request to retrieve the next set of groups that are mapped to users.</p>"}, "items": {"shape": "GroupSummaryList", "documentation": "<p>Summary information for list of groups that are mapped to users.</p>"}}}, "ListIndicesRequest": {"type": "structure", "required": ["applicationId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the Amazon Q Business application connected to the index.</p>", "location": "uri", "locationName": "applicationId"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the maxResults response was incomplete because there is more data to retrieve, Amazon Q Business returns a pagination token in the response. You can use this pagination token to retrieve the next set of Amazon Q Business indices.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResultsIntegerForListIndices", "documentation": "<p>The maximum number of indices to return.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListIndicesResponse": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>If the response is truncated, Amazon Q Business returns this token that you can use in the subsequent request to retrieve the next set of indexes.</p>"}, "indices": {"shape": "Indices", "documentation": "<p>An array of information on the items in one or more indexes.</p>"}}}, "ListMessagesRequest": {"type": "structure", "required": ["conversationId", "applicationId"], "members": {"conversationId": {"shape": "ConversationId", "documentation": "<p>The identifier of the Amazon Q Business web experience conversation.</p>", "location": "uri", "locationName": "conversationId"}, "applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier for the Amazon Q Business application.</p>", "location": "uri", "locationName": "applicationId"}, "userId": {"shape": "UserId", "documentation": "<p>The identifier of the user involved in the Amazon Q Business web experience conversation.</p>", "location": "querystring", "locationName": "userId"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the number of messages returned exceeds <code>maxResults</code>, Amazon Q Business returns a next token as a pagination token to retrieve the next set of messages.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResultsIntegerForListMessages", "documentation": "<p>The maximum number of messages to return.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListMessagesResponse": {"type": "structure", "members": {"messages": {"shape": "Messages", "documentation": "<p>An array of information on one or more messages.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the response is truncated, Amazon Q Business returns this token, which you can use in a later request to list the next set of messages.</p>"}}}, "ListPluginActionsRequest": {"type": "structure", "required": ["applicationId", "pluginId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the Amazon Q Business application the plugin is attached to.</p>", "location": "uri", "locationName": "applicationId"}, "pluginId": {"shape": "PluginId", "documentation": "<p>The identifier of the Amazon Q Business plugin.</p>", "location": "uri", "locationName": "pluginId"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the number of plugin actions returned exceeds <code>maxResults</code>, Amazon Q Business returns a next token as a pagination token to retrieve the next set of plugin actions.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResultsIntegerForListPluginActions", "documentation": "<p>The maximum number of plugin actions to return.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListPluginActionsResponse": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>If the response is truncated, Amazon Q Business returns this token, which you can use in a later request to list the next set of plugin actions.</p>"}, "items": {"shape": "Actions", "documentation": "<p>An array of information on one or more plugin actions.</p>"}}}, "ListPluginTypeActionsRequest": {"type": "structure", "required": ["pluginType"], "members": {"pluginType": {"shape": "PluginType", "documentation": "<p>The type of the plugin.</p>", "location": "uri", "locationName": "pluginType"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the number of plugins returned exceeds <code>maxResults</code>, Amazon Q Business returns a next token as a pagination token to retrieve the next set of plugins.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResultsIntegerForListPluginTypeActions", "documentation": "<p>The maximum number of plugins to return.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListPluginTypeActionsResponse": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>If the response is truncated, Amazon Q Business returns this token, which you can use in a later request to list the next set of plugins.</p>"}, "items": {"shape": "Actions", "documentation": "<p>An array of information on one or more plugins.</p>"}}}, "ListPluginTypeMetadataRequest": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>If the metadata returned exceeds <code>maxResults</code>, Amazon Q Business returns a next token as a pagination token to retrieve the next set of metadata.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResultsIntegerForListPluginTypeMetadata", "documentation": "<p>The maximum number of plugin metadata items to return.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListPluginTypeMetadataResponse": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>If the response is truncated, Amazon Q Business returns this token, which you can use in a later request to list the next set of plugin metadata.</p>"}, "items": {"shape": "ListPluginTypeMetadataSummaries", "documentation": "<p>An array of information on plugin metadata.</p>"}}}, "ListPluginTypeMetadataSummaries": {"type": "list", "member": {"shape": "PluginTypeMetadataSummary"}}, "ListPluginsRequest": {"type": "structure", "required": ["applicationId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the application the plugin is attached to.</p>", "location": "uri", "locationName": "applicationId"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the <code>maxResults</code> response was incomplete because there is more data to retrieve, Amazon Q Business returns a pagination token in the response. You can use this pagination token to retrieve the next set of plugins.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResultsIntegerForListPlugins", "documentation": "<p>The maximum number of documents to return.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListPluginsResponse": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>If the <code>maxResults</code> response was incomplete because there is more data to retrieve, Amazon Q Business returns a pagination token in the response. You can use this pagination token to retrieve the next set of plugins.</p>"}, "plugins": {"shape": "Plugins", "documentation": "<p>Information about a configured plugin.</p>"}}}, "ListRetrieversRequest": {"type": "structure", "required": ["applicationId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the Amazon Q Business application using the retriever.</p>", "location": "uri", "locationName": "applicationId"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the number of retrievers returned exceeds <code>maxResults</code>, Amazon Q Business returns a next token as a pagination token to retrieve the next set of retrievers.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResultsIntegerForListRetrieversRequest", "documentation": "<p>The maximum number of retrievers returned.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListRetrieversResponse": {"type": "structure", "members": {"retrievers": {"shape": "Retrievers", "documentation": "<p>An array of summary information for one or more retrievers.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the response is truncated, Amazon Q Business returns this token, which you can use in a later request to list the next set of retrievers.</p>"}}}, "ListSubscriptionsRequest": {"type": "structure", "required": ["applicationId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the Amazon Q Business application linked to the subscription.</p>", "location": "uri", "locationName": "applicationId"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the <code>maxResults</code> response was incomplete because there is more data to retrieve, Amazon Q Business returns a pagination token in the response. You can use this pagination token to retrieve the next set of Amazon Q Business subscriptions.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResultsIntegerForListSubscriptions", "documentation": "<p>The maximum number of Amazon Q Business subscriptions to return.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListSubscriptionsResponse": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>If the response is truncated, Amazon Q Business returns this token. You can use this token in a subsequent request to retrieve the next set of subscriptions.</p>"}, "subscriptions": {"shape": "Subscriptions", "documentation": "<p>An array of summary information on the subscriptions configured for an Amazon Q Business application.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["resourceARN"], "members": {"resourceARN": {"shape": "AmazonResourceName", "documentation": "<p>The Amazon Resource Name (ARN) of the Amazon Q Business application or data source to get a list of tags for.</p>", "location": "uri", "locationName": "resourceARN"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"tags": {"shape": "Tags", "documentation": "<p>A list of tags associated with the Amazon Q Business application or data source.</p>"}}}, "ListWebExperiencesRequest": {"type": "structure", "required": ["applicationId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the Amazon Q Business application linked to the listed web experiences.</p>", "location": "uri", "locationName": "applicationId"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the <code>maxResults</code> response was incomplete because there is more data to retrieve, Amazon Q Business returns a pagination token in the response. You can use this pagination token to retrieve the next set of Amazon Q Business conversations.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResultsIntegerForListWebExperiencesRequest", "documentation": "<p>The maximum number of Amazon Q Business Web Experiences to return.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListWebExperiencesResponse": {"type": "structure", "members": {"webExperiences": {"shape": "WebExperiences", "documentation": "<p>An array of summary information for one or more Amazon Q Business experiences.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the response is truncated, Amazon Q Business returns this token, which you can use in a later request to list the next set of messages.</p>"}}}, "LogoUrl": {"type": "string", "max": 1284, "min": 0, "pattern": "(https?://[a-zA-Z0-9-_.+%/]+\\.(svg|png))?"}, "Long": {"type": "long", "box": true}, "MaxResults": {"type": "integer", "box": true}, "MaxResultsIntegerForGetTopicConfigurations": {"type": "integer", "box": true, "max": 50, "min": 1}, "MaxResultsIntegerForListApplications": {"type": "integer", "box": true, "max": 100, "min": 1}, "MaxResultsIntegerForListAttachments": {"type": "integer", "box": true, "max": 100, "min": 1}, "MaxResultsIntegerForListConversations": {"type": "integer", "box": true, "max": 100, "min": 1}, "MaxResultsIntegerForListDataAccessors": {"type": "integer", "box": true, "max": 10, "min": 1}, "MaxResultsIntegerForListDataSources": {"type": "integer", "box": true, "max": 10, "min": 1}, "MaxResultsIntegerForListDataSourcesSyncJobs": {"type": "integer", "box": true, "max": 10, "min": 1}, "MaxResultsIntegerForListDocuments": {"type": "integer", "box": true, "max": 100, "min": 1}, "MaxResultsIntegerForListGroupsRequest": {"type": "integer", "box": true, "max": 10, "min": 1}, "MaxResultsIntegerForListIndices": {"type": "integer", "box": true, "max": 100, "min": 1}, "MaxResultsIntegerForListMessages": {"type": "integer", "box": true, "max": 100, "min": 1}, "MaxResultsIntegerForListPluginActions": {"type": "integer", "box": true, "max": 50, "min": 1}, "MaxResultsIntegerForListPluginTypeActions": {"type": "integer", "box": true, "max": 50, "min": 1}, "MaxResultsIntegerForListPluginTypeMetadata": {"type": "integer", "box": true, "max": 50, "min": 1}, "MaxResultsIntegerForListPlugins": {"type": "integer", "box": true, "max": 50, "min": 1}, "MaxResultsIntegerForListRetrieversRequest": {"type": "integer", "box": true, "max": 50, "min": 1}, "MaxResultsIntegerForListSubscriptions": {"type": "integer", "box": true, "max": 100, "min": 1}, "MaxResultsIntegerForListWebExperiencesRequest": {"type": "integer", "box": true, "max": 100, "min": 1}, "MediaExtractionConfiguration": {"type": "structure", "members": {"imageExtractionConfiguration": {"shape": "ImageExtractionConfiguration", "documentation": "<p>The configuration for extracting semantic meaning from images in documents. For more information, see <a href=\"https://docs.aws.amazon.com/amazonq/latest/qbusiness-ug/extracting-meaning-from-images.html\">Extracting semantic meaning from images and visuals</a>. </p>"}, "audioExtractionConfiguration": {"shape": "AudioExtractionConfiguration", "documentation": "<p>Configuration settings for extracting and processing audio content from media files.</p>"}, "videoExtractionConfiguration": {"shape": "VideoExtractionConfiguration", "documentation": "<p>Configuration settings for extracting and processing video content from media files.</p>"}}, "documentation": "<p>The configuration for extracting information from media in documents.</p>"}, "MediaId": {"type": "string", "max": 36, "min": 36, "pattern": "[a-zA-Z0-9][a-zA-Z0-9-]{35}"}, "MediaTooLargeException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The requested media object is too large to be returned.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "MemberGroup": {"type": "structure", "required": ["groupName"], "members": {"groupName": {"shape": "GroupName", "documentation": "<p>The name of the sub group.</p>"}, "type": {"shape": "MembershipType", "documentation": "<p>The type of the sub group.</p>"}}, "documentation": "<p>The sub groups that belong to a group.</p>"}, "MemberGroups": {"type": "list", "member": {"shape": "MemberGroup"}}, "MemberRelation": {"type": "string", "enum": ["AND", "OR"]}, "MemberUser": {"type": "structure", "required": ["userId"], "members": {"userId": {"shape": "DataSourceUserId", "documentation": "<p>The identifier of the user you want to map to a group.</p>"}, "type": {"shape": "MembershipType", "documentation": "<p>The type of the user.</p>"}}, "documentation": "<p>The users that belong to a group.</p>"}, "MemberUsers": {"type": "list", "member": {"shape": "MemberUser"}}, "MembershipType": {"type": "string", "enum": ["INDEX", "DATASOURCE"]}, "Message": {"type": "structure", "members": {"messageId": {"shape": "String", "documentation": "<p>The identifier of the Amazon Q Business web experience message.</p>"}, "body": {"shape": "MessageBody", "documentation": "<p>The content of the Amazon Q Business web experience message.</p>"}, "time": {"shape": "Timestamp", "documentation": "<p>The timestamp of the first Amazon Q Business web experience message.</p>"}, "type": {"shape": "MessageType", "documentation": "<p>The type of Amazon Q Business message, whether <code>HUMAN</code> or <code>AI</code> generated.</p>"}, "attachments": {"shape": "AttachmentsOutput", "documentation": "<p>A file directly uploaded into an Amazon Q Business web experience chat.</p>"}, "sourceAttribution": {"shape": "SourceAttributions", "documentation": "<p>The source documents used to generate Amazon Q Business web experience message.</p>"}, "actionReview": {"shape": "ActionReview"}, "actionExecution": {"shape": "ActionExecution"}}, "documentation": "<p>A message in an Amazon Q Business web experience.</p>"}, "MessageBody": {"type": "string", "max": 1000, "min": 0, "pattern": "\\P{C}*$}"}, "MessageId": {"type": "string", "max": 36, "min": 36, "pattern": "[a-zA-Z0-9][a-zA-Z0-9-]{35}"}, "MessageType": {"type": "string", "enum": ["USER", "SYSTEM"]}, "MessageUsefulness": {"type": "string", "enum": ["USEFUL", "NOT_USEFUL"]}, "MessageUsefulnessComment": {"type": "string", "max": 1000, "min": 0, "pattern": "\\P{C}*"}, "MessageUsefulnessFeedback": {"type": "structure", "required": ["usefulness", "submittedAt"], "members": {"usefulness": {"shape": "MessageUsefulness", "documentation": "<p>The usefulness value assigned by an end user to a message.</p>"}, "reason": {"shape": "MessageUsefulnessReason", "documentation": "<p>The reason for a usefulness rating.</p>"}, "comment": {"shape": "MessageUsefulnessComment", "documentation": "<p>A comment given by an end user on the usefulness of an AI-generated chat message.</p>"}, "submittedAt": {"shape": "Timestamp", "documentation": "<p>The timestamp for when the feedback was submitted.</p>"}}, "documentation": "<p>End user feedback on an AI-generated web experience chat message usefulness.</p>"}, "MessageUsefulnessReason": {"type": "string", "enum": ["NOT_FACTUALLY_CORRECT", "HARMFUL_OR_UNSAFE", "INCORRECT_OR_MISSING_SOURCES", "NOT_HELPFUL", "FACTUALLY_CORRECT", "COMPLETE", "RELEVANT_SOURCES", "HELPFUL", "NOT_BASED_ON_DOCUMENTS", "NOT_COMPLETE", "NOT_CONCISE", "OTHER"]}, "Messages": {"type": "list", "member": {"shape": "Message"}}, "MetadataEvent": {"type": "structure", "members": {"conversationId": {"shape": "ConversationId", "documentation": "<p>The identifier of the conversation with which the generated metadata is associated.</p>"}, "userMessageId": {"shape": "MessageId", "documentation": "<p>The identifier of an Amazon Q Business end user text input message within the conversation.</p>"}, "systemMessageId": {"shape": "MessageId", "documentation": "<p>The identifier of an Amazon Q Business AI generated message within the conversation.</p>"}, "sourceAttributions": {"shape": "SourceAttributions", "documentation": "<p>The source documents used to generate the conversation response.</p>"}, "finalTextMessage": {"shape": "String", "documentation": "<p>The final text output message generated by the system.</p>"}}, "documentation": "<p>A metadata event for a AI-generated text output message in a Amazon Q Business conversation, containing associated metadata generated.</p>", "event": true}, "MetricValue": {"type": "string", "pattern": "(([1-9][0-9]*)|0)"}, "NativeIndexConfiguration": {"type": "structure", "required": ["indexId"], "members": {"indexId": {"shape": "IndexId", "documentation": "<p>The identifier for the Amazon Q Business index.</p>"}, "version": {"shape": "<PERSON>", "documentation": "<p>A read-only field that specifies the version of the <code>NativeIndexConfiguration</code>.</p> <p>Amazon Q Business introduces enhanced document retrieval capabilities in version 2 of <code>NativeIndexConfiguration</code>, focusing on streamlined metadata boosting that prioritizes recency and source relevance to deliver more accurate responses to your queries. Version 2 has the following differences from version 1:</p> <ul> <li> <p>Version 2 supports a single Date field (created_at OR last_updated_at) for recency boosting</p> </li> <li> <p>Version 2 supports a single String field with an ordered list of up to 5 values</p> </li> <li> <p>Version 2 introduces number-based boost levels (ONE, TWO) alongside the text-based levels</p> </li> <li> <p>Version 2 allows specifying prioritization between Date and String fields</p> </li> <li> <p>Version 2 maintains backward compatibility with existing configurations</p> </li> </ul>"}, "boostingOverride": {"shape": "DocumentAttributeBoostingOverrideMap", "documentation": "<p>Overrides the default boosts applied by Amazon Q Business to supported document attribute data types.</p>"}}, "documentation": "<p>Configuration information for an Amazon Q Business index.</p>"}, "NextToken": {"type": "string", "max": 800, "min": 1}, "NextToken1500": {"type": "string", "max": 1500, "min": 1}, "NoAuthConfiguration": {"type": "structure", "members": {}, "documentation": "<p>Information about invoking a custom plugin without any authentication or authorization requirement.</p>"}, "NumberAttributeBoostingConfiguration": {"type": "structure", "required": ["boostingLevel"], "members": {"boostingLevel": {"shape": "DocumentAttributeBoostingLevel", "documentation": "<p>Specifies the priority of boosted document attributes in relation to other boosted attributes. This parameter determines how strongly the attribute influences document ranking in search results. <code>NUMBER</code> attributes can serve as additional boosting factors when needed, but are not supported when using <code>NativeIndexConfiguration</code> version 2.</p>"}, "boostingType": {"shape": "NumberAttributeBoostingType", "documentation": "<p>Specifies whether higher or lower numeric values should be prioritized when boosting. Valid values are ASCENDING (higher numbers are more important) and DESCENDING (lower numbers are more important).</p>"}}, "documentation": "<p>Provides information on boosting <code>NUMBER</code> type document attributes.</p> <p>In the current boosting implementation, boosting focuses primarily on <code>DATE</code> attributes for recency and <code>STRING</code> attributes for source prioritization. <code>NUMBER</code> attributes can serve as additional boosting factors when needed, but are not supported when using <code>NativeIndexConfiguration</code> version 2.</p> <p>For more information on how boosting document attributes work in Amazon Q Business, see <a href=\"https://docs.aws.amazon.com/amazonq/latest/business-use-dg/metadata-boosting.html\">Boosting using document attributes</a>.</p>"}, "NumberAttributeBoostingType": {"type": "string", "enum": ["PRIORITIZE_LARGER_VALUES", "PRIORITIZE_SMALLER_VALUES"]}, "OAuth2ClientCredentialConfiguration": {"type": "structure", "required": ["secretArn", "roleArn"], "members": {"secretArn": {"shape": "SecretArn", "documentation": "<p>The ARN of the Secrets Manager secret that stores the OAuth 2.0 credentials/token used for plugin configuration.</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The ARN of an IAM role used by Amazon Q Business to access the OAuth 2.0 authentication credentials stored in a Secrets Manager secret.</p>"}, "authorizationUrl": {"shape": "Url", "documentation": "<p>The redirect URL required by the OAuth 2.0 protocol for Amazon Q Business to authenticate a plugin user through a third party authentication server.</p>"}, "tokenUrl": {"shape": "Url", "documentation": "<p>The URL required by the OAuth 2.0 protocol to exchange an end user authorization code for an access token.</p>"}}, "documentation": "<p>Information about the OAuth 2.0 authentication credential/token used to configure a plugin.</p>"}, "OpenIDConnectProviderConfiguration": {"type": "structure", "required": ["secretsArn", "secretsRole"], "members": {"secretsArn": {"shape": "SecretArn", "documentation": "<p>The Amazon Resource Name (ARN) of a Secrets Manager secret containing the OIDC client secret.</p>"}, "secretsRole": {"shape": "RoleArn", "documentation": "<p>An IAM role with permissions to access KMS to decrypt the Secrets Manager secret containing your OIDC client secret.</p>"}}, "documentation": "<p>Information about the OIDC-compliant identity provider (IdP) used to authenticate end users of an Amazon Q Business web experience.</p>"}, "OrchestrationConfiguration": {"type": "structure", "required": ["control"], "members": {"control": {"shape": "OrchestrationControl", "documentation": "<p> Status information about whether chat orchestration is activated or deactivated for your Amazon Q Business application.</p>"}}, "documentation": "<p>Configuration information required to enable chat orchestration for your Amazon Q Business application.</p> <note> <p>Chat orchestration is optimized to work for English language content. For more details on language support in Amazon Q Business, see <a href=\"https://docs.aws.amazon.com/amazonq/latest/qbusiness-ug/supported-languages.html\">Supported languages</a>.</p> </note>"}, "OrchestrationControl": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "Origin": {"type": "string", "max": 256, "min": 1, "pattern": "(http://|https://)[a-zA-Z0-9-_.]+(?::[0-9]{1,5})?"}, "Payload": {"type": "string", "sensitive": true}, "PermissionCondition": {"type": "structure", "required": ["conditionOperator", "<PERSON><PERSON><PERSON>", "conditionV<PERSON>ues"], "members": {"conditionOperator": {"shape": "PermissionConditionOperator", "documentation": "<p>The operator to use for the condition evaluation. This determines how the condition values are compared.</p>"}, "conditionKey": {"shape": "PermissionConditionKey", "documentation": "<p>The key for the condition. This identifies the attribute that the condition applies to.</p>"}, "conditionValues": {"shape": "PermissionConditionValues", "documentation": "<p>The values to compare against using the specified condition operator.</p>"}}, "documentation": "<p>Defines a condition that restricts when a permission is effective. Conditions allow you to control access based on specific attributes of the request.</p>"}, "PermissionConditionKey": {"type": "string", "pattern": "aws:PrincipalTag/qbusiness-dataaccessor:[a-zA-Z]+.*"}, "PermissionConditionOperator": {"type": "string", "enum": ["StringEquals"]}, "PermissionConditionValue": {"type": "string", "max": 1000, "min": 1, "pattern": "[a-zA-Z0-9][a-zA-Z0-9_-]*"}, "PermissionConditionValues": {"type": "list", "member": {"shape": "PermissionConditionValue"}, "max": 1, "min": 1}, "PermissionConditions": {"type": "list", "member": {"shape": "PermissionCondition"}, "max": 10, "min": 1}, "PersonalizationConfiguration": {"type": "structure", "required": ["personalizationControlMode"], "members": {"personalizationControlMode": {"shape": "PersonalizationControlMode", "documentation": "<p>An option to allow Amazon Q Business to customize chat responses using user specific metadata—specifically, location and job information—in your IAM Identity Center instance.</p>"}}, "documentation": "<p>Configuration information about chat response personalization. For more information, see <a href=\"https://docs.aws.amazon.com/amazonq/latest/qbusiness-ug/personalizing-chat-responses.html\">Personalizing chat responses</a>.</p>"}, "PersonalizationControlMode": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "Plugin": {"type": "structure", "members": {"pluginId": {"shape": "PluginId", "documentation": "<p>The identifier of the plugin.</p>"}, "displayName": {"shape": "PluginName", "documentation": "<p>The name of the plugin.</p>"}, "type": {"shape": "PluginType", "documentation": "<p>The type of the plugin.</p>"}, "serverUrl": {"shape": "Url", "documentation": "<p>The plugin server URL used for configuration.</p>"}, "state": {"shape": "PluginState", "documentation": "<p>The current status of the plugin.</p>"}, "buildStatus": {"shape": "PluginBuildStatus", "documentation": "<p>The status of the plugin.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The timestamp for when the plugin was created.</p>"}, "updatedAt": {"shape": "Timestamp", "documentation": "<p>The timestamp for when the plugin was last updated.</p>"}}, "documentation": "<p>Information about an Amazon Q Business plugin and its configuration.</p>"}, "PluginArn": {"type": "string", "max": 1284, "min": 0, "pattern": "arn:[a-z0-9-\\.]{1,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[^/].{0,1023}"}, "PluginAuthConfiguration": {"type": "structure", "members": {"basicAuthConfiguration": {"shape": "BasicAuthConfiguration", "documentation": "<p>Information about the basic authentication credentials used to configure a plugin.</p>"}, "oAuth2ClientCredentialConfiguration": {"shape": "OAuth2ClientCredentialConfiguration", "documentation": "<p>Information about the OAuth 2.0 authentication credential/token used to configure a plugin.</p>"}, "noAuthConfiguration": {"shape": "NoAuthConfiguration", "documentation": "<p>Information about invoking a custom plugin without any authentication.</p>"}, "idcAuthConfiguration": {"shape": "IdcAuthConfiguration", "documentation": "<p>Information about the IAM Identity Center Application used to configure authentication for a plugin.</p>"}}, "documentation": "<p>Authentication configuration information for an Amazon Q Business plugin.</p>", "union": true}, "PluginBuildStatus": {"type": "string", "enum": ["READY", "CREATE_IN_PROGRESS", "CREATE_FAILED", "UPDATE_IN_PROGRESS", "UPDATE_FAILED", "DELETE_IN_PROGRESS", "DELETE_FAILED"]}, "PluginConfiguration": {"type": "structure", "required": ["pluginId"], "members": {"pluginId": {"shape": "PluginId", "documentation": "<p> The identifier of the plugin you want to use.</p>"}}, "documentation": "<p>Configuration information required to invoke chat in <code>PLUGIN_MODE</code>.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/amazonq/latest/qbusiness-ug/guardrails.html\">Admin controls and guardrails</a>, <a href=\"https://docs.aws.amazon.com/amazonq/latest/qbusiness-ug/plugins.html\">Plugins</a>, and <a href=\"https://docs.aws.amazon.com/amazonq/latest/business-use-dg/using-web-experience.html#chat-source-scope\">Conversation settings</a>.</p>"}, "PluginDescription": {"type": "string", "max": 200, "min": 1}, "PluginId": {"type": "string", "max": 36, "min": 36, "pattern": "[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}"}, "PluginName": {"type": "string", "max": 100, "min": 1, "pattern": "[a-zA-Z0-9][a-zA-Z0-9_-]*"}, "PluginState": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "PluginType": {"type": "string", "enum": ["SERVICE_NOW", "SALESFORCE", "JIRA", "ZENDESK", "CUSTOM", "QUICKSIGHT", "SERVICENOW_NOW_PLATFORM", "JIRA_CLOUD", "SALESFORCE_CRM", "ZENDESK_SUITE", "ATLASSIAN_CONFLUENCE", "GOOGLE_CALENDAR", "MICROSOFT_TEAMS", "MICROSOFT_EXCHANGE", "PAGERDUTY_ADVANCE", "SMARTSHEET", "ASANA"]}, "PluginTypeCategory": {"type": "string", "enum": ["Customer relationship management (CRM)", "Project management", "Communication", "Productivity", "Ticketing and incident management"]}, "PluginTypeMetadataSummary": {"type": "structure", "members": {"type": {"shape": "PluginType", "documentation": "<p>The type of the plugin.</p>"}, "category": {"shape": "PluginTypeCategory", "documentation": "<p>The category of the plugin type.</p>"}, "description": {"shape": "String", "documentation": "<p>The description assigned by Amazon Q Business to a plugin. You can't modify this value.</p>"}}, "documentation": "<p>Summary metadata information for a Amazon Q Business plugin.</p>"}, "Plugins": {"type": "list", "member": {"shape": "Plugin"}}, "Principal": {"type": "structure", "members": {"user": {"shape": "Principal<PERSON>ser", "documentation": "<p>The user associated with the principal.</p>"}, "group": {"shape": "PrincipalGroup", "documentation": "<p> The group associated with the principal.</p>"}}, "documentation": "<p>Provides user and group information used for filtering documents to use for generating Amazon Q Business conversation responses.</p>", "union": true}, "PrincipalGroup": {"type": "structure", "required": ["access"], "members": {"name": {"shape": "GroupName", "documentation": "<p>The name of the group.</p>"}, "access": {"shape": "ReadAccessType", "documentation": "<p>Provides information about whether to allow or deny access to the principal.</p>"}, "membershipType": {"shape": "MembershipType", "documentation": "<p>The type of group.</p>"}}, "documentation": "<p>Provides information about a group associated with the principal.</p>"}, "PrincipalRoleArn": {"type": "string", "max": 1284, "min": 1, "pattern": "arn:aws:iam::[0-9]{12}:role/[a-zA-Z0-9_/+=,.@-]+"}, "PrincipalUser": {"type": "structure", "required": ["access"], "members": {"id": {"shape": "UserId", "documentation": "<p> The identifier of the user. </p>"}, "access": {"shape": "ReadAccessType", "documentation": "<p>Provides information about whether to allow or deny access to the principal.</p>"}, "membershipType": {"shape": "MembershipType", "documentation": "<p>The type of group.</p>"}}, "documentation": "<p>Provides information about a user associated with a principal.</p>"}, "Principals": {"type": "list", "member": {"shape": "Principal"}}, "PutFeedbackRequest": {"type": "structure", "required": ["applicationId", "conversationId", "messageId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the application associated with the feedback.</p>", "location": "uri", "locationName": "applicationId"}, "userId": {"shape": "UserId", "documentation": "<p>The identifier of the user giving the feedback.</p>", "location": "querystring", "locationName": "userId"}, "conversationId": {"shape": "ConversationId", "documentation": "<p>The identifier of the conversation the feedback is attached to.</p>", "location": "uri", "locationName": "conversationId"}, "messageId": {"shape": "SystemMessageId", "documentation": "<p>The identifier of the chat message that the feedback was given for.</p>", "location": "uri", "locationName": "messageId"}, "messageCopiedAt": {"shape": "Timestamp", "documentation": "<p>The timestamp for when the feedback was recorded.</p>"}, "messageUsefulness": {"shape": "MessageUsefulnessFeedback", "documentation": "<p>The feedback usefulness value given by the user to the chat message.</p>"}}}, "PutGroupRequest": {"type": "structure", "required": ["applicationId", "indexId", "groupName", "type", "groupMembers"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the application in which the user and group mapping belongs.</p>", "location": "uri", "locationName": "applicationId"}, "indexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index in which you want to map users to their groups.</p>", "location": "uri", "locationName": "indexId"}, "groupName": {"shape": "GroupName", "documentation": "<p>The list that contains your users or sub groups that belong the same group. For example, the group \"Company\" includes the user \"CEO\" and the sub groups \"Research\", \"Engineering\", and \"Sales and Marketing\".</p>"}, "dataSourceId": {"shape": "DataSourceId", "documentation": "<p>The identifier of the data source for which you want to map users to their groups. This is useful if a group is tied to multiple data sources, but you only want the group to access documents of a certain data source. For example, the groups \"Research\", \"Engineering\", and \"Sales and Marketing\" are all tied to the company's documents stored in the data sources Confluence and Salesforce. However, \"Sales and Marketing\" team only needs access to customer-related documents stored in Salesforce.</p>"}, "type": {"shape": "MembershipType", "documentation": "<p>The type of the group.</p>"}, "groupMembers": {"shape": "GroupMembers"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of an IAM role that has access to the S3 file that contains your list of users that belong to a group.</p>"}}}, "PutGroupResponse": {"type": "structure", "members": {}}, "QAppsConfiguration": {"type": "structure", "required": ["qAppsControlMode"], "members": {"qAppsControlMode": {"shape": "QAppsControlMode", "documentation": "<p>Status information about whether end users can create and use Amazon Q Apps in the web experience.</p>"}}, "documentation": "<p>Configuration information about Amazon Q Apps.</p>"}, "QAppsControlMode": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "QIamAction": {"type": "string", "pattern": "qbusiness:[a-zA-Z]+"}, "QIamActions": {"type": "list", "member": {"shape": "QIamAction"}, "max": 10, "min": 1}, "QueryText": {"type": "string"}, "QuickSightConfiguration": {"type": "structure", "required": ["clientNamespace"], "members": {"clientNamespace": {"shape": "ClientNamespace", "documentation": "<p>The Amazon QuickSight namespace that is used as the identity provider. For more information about QuickSight namespaces, see <a href=\"https://docs.aws.amazon.com/quicksight/latest/developerguide/namespace-operations.html\">Namespace operations</a>. </p>"}}, "documentation": "<p>The Amazon QuickSight configuration for an Amazon Q Business application that uses QuickSight as the identity provider. For more information, see <a href=\"https://docs.aws.amazon.com/amazonq/latest/qbusiness-ug/create-quicksight-integrated-application.html\">Creating an Amazon QuickSight integrated application</a>.</p>"}, "ReadAccessType": {"type": "string", "enum": ["ALLOW", "DENY"]}, "RelevantContent": {"type": "structure", "members": {"content": {"shape": "String", "documentation": "<p>The actual content of the relevant item.</p>"}, "documentId": {"shape": "DocumentId", "documentation": "<p>The unique identifier of the document containing the relevant content.</p>"}, "documentTitle": {"shape": "Title", "documentation": "<p>The title of the document containing the relevant content.</p>"}, "documentUri": {"shape": "Url", "documentation": "<p>The URI of the document containing the relevant content.</p>"}, "documentAttributes": {"shape": "DocumentAttributes", "documentation": "<p>Additional attributes of the document containing the relevant content.</p>"}, "scoreAttributes": {"shape": "ScoreAttributes", "documentation": "<p>Attributes related to the relevance score of the content.</p>"}}, "documentation": "<p>Represents a piece of content that is relevant to a search query.</p>"}, "RelevantContentList": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "ResourceNotFoundException": {"type": "structure", "required": ["message", "resourceId", "resourceType"], "members": {"message": {"shape": "ErrorMessage", "documentation": "<p>The message describing a <code>ResourceNotFoundException</code>.</p>"}, "resourceId": {"shape": "String", "documentation": "<p>The identifier of the resource affected.</p>"}, "resourceType": {"shape": "String", "documentation": "<p>The type of the resource affected.</p>"}}, "documentation": "<p>The application or plugin resource you want to use doesn’t exist. Make sure you have provided the correct resource and try again.</p>", "error": {"httpStatusCode": 404, "senderFault": true}, "exception": true}, "ResponseConfiguration": {"type": "structure", "members": {"instructionCollection": {"shape": "InstructionCollection", "documentation": "<p>A collection of instructions that guide how Amazon Q Business generates responses, including parameters for response length, target audience, perspective, output style, identity, tone, and custom instructions.</p>"}}, "documentation": "<p>Configuration settings to define how Amazon Q Business generates and formats responses to user queries. This includes customization options for response style, tone, length, and other characteristics.</p>"}, "ResponseConfigurationSummary": {"type": "string", "max": 1000, "min": 1}, "ResponseConfigurationType": {"type": "string", "enum": ["ALL"]}, "ResponseConfigurations": {"type": "map", "key": {"shape": "ResponseConfigurationType"}, "value": {"shape": "ResponseConfiguration"}, "max": 1, "min": 1}, "ResponseScope": {"type": "string", "enum": ["ENTERPRISE_CONTENT_ONLY", "EXTENDED_KNOWLEDGE_ENABLED"]}, "Retriever": {"type": "structure", "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the Amazon Q Business application using the retriever.</p>"}, "retrieverId": {"shape": "RetrieverId", "documentation": "<p>The identifier of the retriever used by your Amazon Q Business application.</p>"}, "type": {"shape": "RetrieverType", "documentation": "<p>The type of your retriever.</p>"}, "status": {"shape": "RetrieverStatus", "documentation": "<p>The status of your retriever.</p>"}, "displayName": {"shape": "RetrieverName", "documentation": "<p>The name of your retriever.</p>"}}, "documentation": "<p>Summary information for the retriever used for your Amazon Q Business application.</p>"}, "RetrieverArn": {"type": "string", "max": 1284, "min": 0, "pattern": "arn:[a-z0-9-\\.]{1,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[^/].{0,1023}"}, "RetrieverConfiguration": {"type": "structure", "members": {"nativeIndexConfiguration": {"shape": "NativeIndexConfiguration", "documentation": "<p>Provides information on how a Amazon Q Business index used as a retriever for your Amazon Q Business application is configured.</p>"}, "kendraIndexConfiguration": {"shape": "KendraIndexConfiguration", "documentation": "<p>Provides information on how the Amazon Kendra index used as a retriever for your Amazon Q Business application is configured.</p>"}}, "documentation": "<p>Provides information on how the retriever used for your Amazon Q Business application is configured.</p>", "union": true}, "RetrieverContentSource": {"type": "structure", "required": ["retrieverId"], "members": {"retrieverId": {"shape": "RetrieverId", "documentation": "<p>The unique identifier of the retriever to use as the content source.</p>"}}, "documentation": "<p>Specifies a retriever as the content source for a search.</p>"}, "RetrieverId": {"type": "string", "max": 36, "min": 36, "pattern": "[a-zA-Z0-9][a-zA-Z0-9-]{35}"}, "RetrieverName": {"type": "string", "max": 1000, "min": 1, "pattern": "[a-zA-Z0-9][a-zA-Z0-9_-]*"}, "RetrieverStatus": {"type": "string", "enum": ["CREATING", "ACTIVE", "FAILED"]}, "RetrieverType": {"type": "string", "enum": ["NATIVE_INDEX", "KENDRA_INDEX"]}, "Retrievers": {"type": "list", "member": {"shape": "Retriever"}}, "RoleArn": {"type": "string", "max": 1284, "min": 0, "pattern": "arn:[a-z0-9-\\.]{1,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[^/].{0,1023}"}, "Rule": {"type": "structure", "required": ["ruleType"], "members": {"includedUsersAndGroups": {"shape": "UsersAndGroups", "documentation": "<p>Users and groups to be included in a rule.</p>"}, "excludedUsersAndGroups": {"shape": "UsersAndGroups", "documentation": "<p>Users and groups to be excluded from a rule.</p>"}, "ruleType": {"shape": "RuleType", "documentation": "<p>The type of rule.</p>"}, "ruleConfiguration": {"shape": "RuleConfiguration", "documentation": "<p>The configuration information for a rule.</p>"}}, "documentation": "<p>Guardrail rules for an Amazon Q Business application. Amazon Q Business supports only one rule at a time.</p>"}, "RuleConfiguration": {"type": "structure", "members": {"contentBlockerRule": {"shape": "ContentBlockerRule", "documentation": "<p>A rule for configuring how Amazon Q Business responds when it encounters a a blocked topic.</p>"}, "contentRetrievalRule": {"shape": "ContentRetrievalRule"}}, "documentation": "<p>Provides configuration information about a rule.</p>", "union": true}, "RuleType": {"type": "string", "enum": ["CONTENT_BLOCKER_RULE", "CONTENT_RETRIEVAL_RULE"]}, "Rules": {"type": "list", "member": {"shape": "Rule"}, "max": 10, "min": 0}, "S3": {"type": "structure", "required": ["bucket", "key"], "members": {"bucket": {"shape": "S3BucketName", "documentation": "<p>The name of the S3 bucket that contains the file.</p>"}, "key": {"shape": "S3ObjectKey", "documentation": "<p>The name of the file.</p>"}}, "documentation": "<p>Information required for Amazon Q Business to find a specific file in an Amazon S3 bucket.</p>"}, "S3BucketName": {"type": "string", "max": 63, "min": 1, "pattern": "[a-z0-9][\\.\\-a-z0-9]{1,61}[a-z0-9]"}, "S3ObjectKey": {"type": "string", "max": 1024, "min": 1}, "SamlAttribute": {"type": "string", "max": 256, "min": 1}, "SamlAuthenticationUrl": {"type": "string", "max": 1284, "min": 1, "pattern": "https://.*"}, "SamlConfiguration": {"type": "structure", "required": ["metadataXML", "roleArn", "userIdAttribute"], "members": {"metadataXML": {"shape": "SamlMetadataXML", "documentation": "<p>The metadata XML that your IdP generated.</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of an IAM role assumed by users when they authenticate into their Amazon Q Business web experience, containing the relevant Amazon Q Business permissions for conversing with Amazon Q Business.</p>"}, "userIdAttribute": {"shape": "SamlAttribute", "documentation": "<p>The user attribute name in your IdP that maps to the user email.</p>"}, "userGroupAttribute": {"shape": "SamlAttribute", "documentation": "<p>The group attribute name in your IdP that maps to user groups.</p>"}}, "documentation": "<p>Provides the SAML 2.0 compliant identity provider (IdP) configuration information Amazon Q Business needs to deploy a Amazon Q Business web experience.</p>"}, "SamlMetadataXML": {"type": "string", "max": 10000000, "min": 1000, "pattern": ".*"}, "SamlProviderConfiguration": {"type": "structure", "required": ["authenticationUrl"], "members": {"authenticationUrl": {"shape": "SamlAuthenticationUrl", "documentation": "<p>The URL where Amazon Q Business end users will be redirected for authentication. </p>"}}, "documentation": "<p>Information about the SAML 2.0-compliant identity provider (IdP) used to authenticate end users of an Amazon Q Business web experience.</p>"}, "ScoreAttributes": {"type": "structure", "members": {"scoreConfidence": {"shape": "ScoreConfidence", "documentation": "<p>The confidence level of the relevance score.</p>"}}, "documentation": "<p>Provides information about the relevance score of content.</p>"}, "ScoreConfidence": {"type": "string", "enum": ["VERY_HIGH", "HIGH", "MEDIUM", "LOW", "NOT_AVAILABLE"]}, "SearchRelevantContentRequest": {"type": "structure", "required": ["applicationId", "queryText", "contentSource"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The unique identifier of the Amazon Q Business application to search.</p>", "location": "uri", "locationName": "applicationId"}, "queryText": {"shape": "QueryText", "documentation": "<p>The text to search for.</p>"}, "contentSource": {"shape": "ContentSource", "documentation": "<p>The source of content to search in.</p>"}, "attributeFilter": {"shape": "Attribute<PERSON>ilter"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results. (You received this token from a previous call.)</p>"}}}, "SearchRelevantContentResponse": {"type": "structure", "members": {"relevantContent": {"shape": "RelevantContentList", "documentation": "<p>The list of relevant content items found.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token to use to retrieve the next set of results, if there are any.</p>"}}}, "SecretArn": {"type": "string", "max": 1284, "min": 0, "pattern": "arn:[a-z0-9-\\.]{1,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[^/].{0,1023}"}, "SecurityGroupId": {"type": "string", "max": 200, "min": 1, "pattern": "[-0-9a-zA-Z]+"}, "SecurityGroupIds": {"type": "list", "member": {"shape": "SecurityGroupId"}, "max": 10, "min": 1}, "ServiceQuotaExceededException": {"type": "structure", "required": ["message", "resourceId", "resourceType"], "members": {"message": {"shape": "ErrorMessage", "documentation": "<p>The message describing a <code>ServiceQuotaExceededException</code>.</p>"}, "resourceId": {"shape": "String", "documentation": "<p>The identifier of the resource affected.</p>"}, "resourceType": {"shape": "String", "documentation": "<p>The type of the resource affected.</p>"}}, "documentation": "<p>You have exceeded the set limits for your Amazon Q Business service. </p>", "error": {"httpStatusCode": 402, "senderFault": true}, "exception": true}, "SessionDurationInMinutes": {"type": "integer", "box": true, "max": 60, "min": 15}, "SnippetExcerpt": {"type": "structure", "members": {"text": {"shape": "SnippetExcerptText", "documentation": "<p>The relevant text excerpt from a source that was used to generate a citation text segment in an Amazon Q chat response.</p>"}}, "documentation": "<p>Contains the relevant text excerpt from a source that was used to generate a citation text segment in an Amazon Q Business chat response.</p>"}, "SnippetExcerptText": {"type": "string"}, "SourceAttribution": {"type": "structure", "members": {"title": {"shape": "String", "documentation": "<p>The title of the document which is the source for the Amazon Q Business generated response. </p>"}, "snippet": {"shape": "String", "documentation": "<p>The content extract from the document on which the generated response is based. </p>"}, "url": {"shape": "String", "documentation": "<p>The URL of the document which is the source for the Amazon Q Business generated response. </p>"}, "citationNumber": {"shape": "Integer", "documentation": "<p>The number attached to a citation in an Amazon Q Business generated response.</p>"}, "updatedAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when the Amazon Q Business application was last updated.</p>"}, "textMessageSegments": {"shape": "TextSegmentList", "documentation": "<p>A text extract from a source document that is used for source attribution.</p>"}}, "documentation": "<p>The documents used to generate an Amazon Q Business web experience response.</p>"}, "SourceAttributionMediaId": {"type": "string", "max": 36, "min": 36, "pattern": "[a-zA-Z0-9][a-zA-Z0-9-]{35}"}, "SourceAttributions": {"type": "list", "member": {"shape": "SourceAttribution"}}, "SourceDetails": {"type": "structure", "members": {"imageSourceDetails": {"shape": "ImageSourceDetails", "documentation": "<p>Details specific to image content within the source.</p>"}, "audioSourceDetails": {"shape": "AudioSourceDetails", "documentation": "<p>Details specific to audio content within the source.</p>"}, "videoSourceDetails": {"shape": "VideoSourceDetails", "documentation": "<p>Details specific to video content within the source.</p>"}}, "documentation": "<p>Container for details about different types of media sources (image, audio, or video).</p>", "union": true}, "StartDataSourceSyncJobRequest": {"type": "structure", "required": ["dataSourceId", "applicationId", "indexId"], "members": {"dataSourceId": {"shape": "DataSourceId", "documentation": "<p> The identifier of the data source connector. </p>", "location": "uri", "locationName": "dataSourceId"}, "applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of Amazon Q Business application the data source is connected to.</p>", "location": "uri", "locationName": "applicationId"}, "indexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index used with the data source connector.</p>", "location": "uri", "locationName": "indexId"}}}, "StartDataSourceSyncJobResponse": {"type": "structure", "members": {"executionId": {"shape": "ExecutionId", "documentation": "<p>The identifier for a particular synchronization job.</p>"}}}, "StatementId": {"type": "string", "max": 100, "min": 1, "pattern": "[a-zA-Z0-9_-]+"}, "Status": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "StopDataSourceSyncJobRequest": {"type": "structure", "required": ["dataSourceId", "applicationId", "indexId"], "members": {"dataSourceId": {"shape": "DataSourceId", "documentation": "<p> The identifier of the data source connector. </p>", "location": "uri", "locationName": "dataSourceId"}, "applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the Amazon Q Business application that the data source is connected to.</p>", "location": "uri", "locationName": "applicationId"}, "indexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index used with the Amazon Q Business data source connector.</p>", "location": "uri", "locationName": "indexId"}}}, "StopDataSourceSyncJobResponse": {"type": "structure", "members": {}}, "String": {"type": "string", "max": 2048, "min": 1}, "StringAttributeBoostingConfiguration": {"type": "structure", "required": ["boostingLevel"], "members": {"boostingLevel": {"shape": "DocumentAttributeBoostingLevel", "documentation": "<p>Specifies the priority tier ranking of boosting applied to document attributes. For version 2, this parameter indicates the relative ranking between boosted fields (ONE being highest priority, TWO being second highest, etc.) and determines the order in which attributes influence document ranking in search results. For version 1, this parameter specifies the boosting intensity. For version 2, boosting intensity (VERY HIGH, HIGH, MEDIUM, LOW, NONE) are not supported. Note that in version 2, you are not allowed to boost on only one field and make this value TWO.</p>"}, "attributeValueBoosting": {"shape": "StringAttributeValueBoosting", "documentation": "<p>Specifies specific values of a <code>STRING</code> type document attribute being boosted. When using <code>NativeIndexConfiguration</code> version 2, you can specify up to five values in order of priority.</p>"}}, "documentation": "<p>Provides information on boosting <code>STRING</code> type document attributes.</p> <note> <p>For <code>STRING</code> and <code>STRING_LIST</code> type document attributes to be used for boosting on the console and the API, they must be enabled for search using the <a href=\"https://docs.aws.amazon.com/amazonq/latest/api-reference/API_DocumentAttributeConfiguration.html\">DocumentAttributeConfiguration</a> object of the <a href=\"https://docs.aws.amazon.com/amazonq/latest/api-reference/API_UpdateIndex.html\">UpdateIndex</a> API. If you haven't enabled searching on these attributes, you can't boost attributes of these data types on either the console or the API.</p> </note> <p>For more information on how boosting document attributes work in Amazon Q Business, see <a href=\"https://docs.aws.amazon.com/amazonq/latest/business-use-dg/metadata-boosting.html\">Boosting using document attributes</a>.</p>"}, "StringAttributeValueBoosting": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "StringAttributeValueBoostingLevel"}, "max": 10, "min": 1}, "StringAttributeValueBoostingLevel": {"type": "string", "enum": ["LOW", "MEDIUM", "HIGH", "VERY_HIGH", "ONE", "TWO", "THREE", "FOUR", "FIVE"]}, "StringListAttributeBoostingConfiguration": {"type": "structure", "required": ["boostingLevel"], "members": {"boostingLevel": {"shape": "DocumentAttributeBoostingLevel", "documentation": "<p>Specifies the priority of boosted document attributes in relation to other boosted attributes. This parameter determines how strongly the attribute influences document ranking in search results. <code>STRING_LIST</code> attributes can serve as additional boosting factors when needed, but are not supported when using <code>NativeIndexConfiguration</code> version 2.</p>"}}, "documentation": "<p>Provides information on boosting <code>STRING_LIST</code> type document attributes.</p> <p>In the current boosting implementation, boosting focuses primarily on <code>DATE</code> attributes for recency and <code>STRING</code> attributes for source prioritization. <code>STRING_LIST</code> attributes can serve as additional boosting factors when needed, but are not supported when using <code>NativeIndexConfiguration</code> version 2.</p> <note> <p>For <code>STRING</code> and <code>STRING_LIST</code> type document attributes to be used for boosting on the console and the API, they must be enabled for search using the <a href=\"https://docs.aws.amazon.com/amazonq/latest/api-reference/API_DocumentAttributeConfiguration.html\">DocumentAttributeConfiguration</a> object of the <a href=\"https://docs.aws.amazon.com/amazonq/latest/api-reference/API_UpdateIndex.html\">UpdateIndex</a> API. If you haven't enabled searching on these attributes, you can't boost attributes of these data types on either the console or the API.</p> </note> <p>For more information on how boosting document attributes work in Amazon Q Business, see <a href=\"https://docs.aws.amazon.com/amazonq/latest/business-use-dg/metadata-boosting.html\">Boosting using document attributes</a>.</p>"}, "SubnetId": {"type": "string", "max": 200, "min": 1, "pattern": "[-0-9a-zA-Z]+"}, "SubnetIds": {"type": "list", "member": {"shape": "SubnetId"}}, "Subscription": {"type": "structure", "members": {"subscriptionId": {"shape": "SubscriptionId", "documentation": "<p>The identifier of the Amazon Q Business subscription to be updated.</p>"}, "subscriptionArn": {"shape": "SubscriptionArn", "documentation": "<p>The Amazon Resource Name (ARN) of the Amazon Q Business subscription that was updated.</p>"}, "principal": {"shape": "SubscriptionPrincipal", "documentation": "<p>The IAM Identity Center <code>UserId</code> or <code>GroupId</code> of a user or group in the IAM Identity Center instance connected to the Amazon Q Business application.</p>"}, "currentSubscription": {"shape": "SubscriptionDetails", "documentation": "<p>The type of your current Amazon Q Business subscription.</p>"}, "nextSubscription": {"shape": "SubscriptionDetails", "documentation": "<p>The type of the Amazon Q Business subscription for the next month.</p>"}}, "documentation": "<p>Information about an Amazon Q Business subscription.</p> <p>Subscriptions are used to provide access for an IAM Identity Center user or a group to an Amazon Q Business application.</p> <p>Amazon Q Business offers two subscription tiers: <code>Q_LITE</code> and <code>Q_BUSINESS</code>. Subscription tier determines feature access for the user. For more information on subscriptions and pricing tiers, see <a href=\"https://aws.amazon.com/q/business/pricing/\">Amazon Q Business pricing</a>.</p>"}, "SubscriptionArn": {"type": "string", "max": 1224, "min": 10, "pattern": "arn:[a-z0-9-\\.]{1,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[^/].{0,1023}"}, "SubscriptionDetails": {"type": "structure", "members": {"type": {"shape": "SubscriptionType", "documentation": "<p> The type of an Amazon Q Business subscription. </p>"}}, "documentation": "<p> The details of an Amazon Q Business subscription. </p>"}, "SubscriptionId": {"type": "string", "max": 1224, "min": 0}, "SubscriptionPrincipal": {"type": "structure", "members": {"user": {"shape": "UserIdentifier", "documentation": "<p>The identifier of a user in the IAM Identity Center instance connected to the Amazon Q Business application.</p>"}, "group": {"shape": "GroupIdentifier", "documentation": "<p>The identifier of a group in the IAM Identity Center instance connected to the Amazon Q Business application.</p>"}}, "documentation": "<p>A user or group in the IAM Identity Center instance connected to the Amazon Q Business application.</p>", "union": true}, "SubscriptionType": {"type": "string", "enum": ["Q_LITE", "Q_BUSINESS"]}, "Subscriptions": {"type": "list", "member": {"shape": "Subscription"}}, "SyncSchedule": {"type": "string", "max": 998, "min": 0, "pattern": "[\\s\\S]*"}, "SystemMessageId": {"type": "string", "max": 36, "min": 36, "pattern": "[a-zA-Z0-9][a-zA-Z0-9-]{35}"}, "SystemMessageOverride": {"type": "string", "max": 350, "min": 0, "pattern": "\\P{C}*"}, "SystemMessageType": {"type": "string", "enum": ["RESPONSE", "GROUNDED_RESPONSE"]}, "Tag": {"type": "structure", "required": ["key", "value"], "members": {"key": {"shape": "TagKey", "documentation": "<p> The key for the tag. Keys are not case sensitive and must be unique for the Amazon Q Business application or data source.</p>"}, "value": {"shape": "TagValue", "documentation": "<p>The value associated with the tag. The value may be an empty string but it can't be null.</p>"}}, "documentation": "<p>A list of key/value pairs that identify an index, FAQ, or data source. Tag keys and values can consist of Unicode letters, digits, white space, and any of the following symbols: _ . : / = + - @.</p>"}, "TagKey": {"type": "string", "max": 128, "min": 1}, "TagKeys": {"type": "list", "member": {"shape": "TagKey"}, "max": 200, "min": 0}, "TagResourceRequest": {"type": "structure", "required": ["resourceARN", "tags"], "members": {"resourceARN": {"shape": "AmazonResourceName", "documentation": "<p>The Amazon Resource Name (ARN) of the Amazon Q Business application or data source to tag.</p>", "location": "uri", "locationName": "resourceARN"}, "tags": {"shape": "Tags", "documentation": "<p>A list of tag keys to add to the Amazon Q Business application or data source. If a tag already exists, the existing value is replaced with the new value.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0}, "Tags": {"type": "list", "member": {"shape": "Tag"}, "max": 200, "min": 0}, "TextDocumentStatistics": {"type": "structure", "members": {"indexedTextBytes": {"shape": "IndexedTextBytes", "documentation": "<p>The total size, in bytes, of the indexed documents.</p>"}, "indexedTextDocumentCount": {"shape": "IndexedTextDocument", "documentation": "<p>The number of text documents indexed.</p>"}}, "documentation": "<p>Provides information about text documents in an index.</p>"}, "TextInputEvent": {"type": "structure", "required": ["userMessage"], "members": {"userMessage": {"shape": "UserMessage", "documentation": "<p>A user message in a text message input event.</p>"}}, "documentation": "<p>An input event for a end user message in an Amazon Q Business web experience. </p>", "event": true}, "TextOutputEvent": {"type": "structure", "members": {"systemMessageType": {"shape": "SystemMessageType", "documentation": "<p>The type of AI-generated message in a <code>TextOutputEvent</code>. Amazon Q Business currently supports two types of messages:</p> <ul> <li> <p> <code>RESPONSE</code> - The Amazon Q Business system response.</p> </li> <li> <p> <code>GROUNDED_RESPONSE</code> - The corrected, hallucination-reduced, response returned by Amazon Q Business. Available only if hallucination reduction is supported and configured for the application and detected in the end user chat query by Amazon Q Business.</p> </li> </ul>"}, "conversationId": {"shape": "ConversationId", "documentation": "<p>The identifier of the conversation with which the text output event is associated.</p>"}, "userMessageId": {"shape": "MessageId", "documentation": "<p>The identifier of an end user message in a <code>TextOutputEvent</code>.</p>"}, "systemMessageId": {"shape": "MessageId", "documentation": "<p>The identifier of an AI-generated message in a <code>TextOutputEvent</code>.</p>"}, "systemMessage": {"shape": "String", "documentation": "<p>An AI-generated message in a <code>TextOutputEvent</code>.</p>"}}, "documentation": "<p>An output event for an AI-generated response in an Amazon Q Business web experience.</p>", "event": true}, "TextSegment": {"type": "structure", "members": {"beginOffset": {"shape": "Integer", "documentation": "<p>The zero-based location in the response string where the source attribution starts.</p>"}, "endOffset": {"shape": "Integer", "documentation": "<p>The zero-based location in the response string where the source attribution ends.</p>"}, "snippetExcerpt": {"shape": "SnippetExcerpt", "documentation": "<p>The relevant text excerpt from a source that was used to generate a citation text segment in an Amazon Q Business chat response.</p>"}, "mediaId": {"shape": "SourceAttributionMediaId", "documentation": "<p>The identifier of the media object associated with the text segment in the source attribution.</p>", "deprecated": true, "deprecatedMessage": "Deprecated in favor of using mediaId within the respective sourceDetails field.", "deprecatedSince": "2025-02-28"}, "mediaMimeType": {"shape": "String", "documentation": "<p>The MIME type (image/png) of the media object associated with the text segment in the source attribution.</p>", "deprecated": true, "deprecatedMessage": "Deprecated in favor of using mediaMimeType within the respective sourceDetails field.", "deprecatedSince": "2025-02-28"}, "sourceDetails": {"shape": "SourceDetails", "documentation": "<p>Source information for a segment of extracted text, including its media type.</p>"}}, "documentation": "<p>Provides information about a text extract in a chat response that can be attributed to a source document.</p>"}, "TextSegmentList": {"type": "list", "member": {"shape": "TextSegment"}}, "ThrottlingException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The request was denied due to throttling. Reduce the number of requests and try again.</p>", "error": {"httpStatusCode": 429, "senderFault": true}, "exception": true}, "Timestamp": {"type": "timestamp"}, "Title": {"type": "string", "max": 1024, "min": 1}, "TopicConfiguration": {"type": "structure", "required": ["name", "rules"], "members": {"name": {"shape": "TopicConfigurationName", "documentation": "<p>A name for your topic control configuration.</p>"}, "description": {"shape": "TopicDescription", "documentation": "<p>A description for your topic control configuration. Use this to outline how the large language model (LLM) should use this topic control configuration.</p>"}, "exampleChatMessages": {"shape": "ExampleChatMessages", "documentation": "<p>A list of example phrases that you expect the end user to use in relation to the topic.</p>"}, "rules": {"shape": "Rules", "documentation": "<p>Rules defined for a topic configuration.</p>"}}, "documentation": "<p>The topic specific controls configured for an Amazon Q Business application.</p>"}, "TopicConfigurationName": {"type": "string", "max": 36, "min": 1, "pattern": "[a-zA-Z0-9][a-zA-Z0-9-]{0,35}"}, "TopicConfigurations": {"type": "list", "member": {"shape": "TopicConfiguration"}, "max": 10, "min": 0}, "TopicDescription": {"type": "string", "max": 350, "min": 0, "pattern": "\\P{C}*"}, "UntagResourceRequest": {"type": "structure", "required": ["resourceARN", "tagKeys"], "members": {"resourceARN": {"shape": "AmazonResourceName", "documentation": "<p>The Amazon Resource Name (ARN) of the Amazon Q Business application, or data source to remove the tag from.</p>", "location": "uri", "locationName": "resourceARN"}, "tagKeys": {"shape": "TagKeys", "documentation": "<p>A list of tag keys to remove from the Amazon Q Business application or data source. If a tag key does not exist on the resource, it is ignored.</p>", "location": "querystring", "locationName": "tagKeys"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateApplicationRequest": {"type": "structure", "required": ["applicationId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the Amazon Q Business application.</p>", "location": "uri", "locationName": "applicationId"}, "identityCenterInstanceArn": {"shape": "InstanceArn", "documentation": "<p> The Amazon Resource Name (ARN) of the IAM Identity Center instance you are either creating for—or connecting to—your Amazon Q Business application.</p>"}, "displayName": {"shape": "ApplicationName", "documentation": "<p>A name for the Amazon Q Business application.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description for the Amazon Q Business application.</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>An Amazon Web Services Identity and Access Management (IAM) role that gives Amazon Q Business permission to access Amazon CloudWatch logs and metrics.</p>"}, "attachmentsConfiguration": {"shape": "AttachmentsConfiguration", "documentation": "<p>An option to allow end users to upload files directly during chat.</p>"}, "qAppsConfiguration": {"shape": "QAppsConfiguration", "documentation": "<p>An option to allow end users to create and use Amazon Q Apps in the web experience.</p>"}, "personalizationConfiguration": {"shape": "PersonalizationConfiguration", "documentation": "<p>Configuration information about chat response personalization. For more information, see <a href=\"https://docs.aws.amazon.com/amazonq/latest/qbusiness-ug/personalizing-chat-responses.html\">Personalizing chat responses</a>.</p>"}, "autoSubscriptionConfiguration": {"shape": "AutoSubscriptionConfiguration", "documentation": "<p>An option to enable updating the default subscription type assigned to an Amazon Q Business application using IAM identity federation for user management.</p>"}}}, "UpdateApplicationResponse": {"type": "structure", "members": {}}, "UpdateChatControlsConfigurationRequest": {"type": "structure", "required": ["applicationId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the application for which the chat controls are configured.</p>", "location": "uri", "locationName": "applicationId"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>A token that you provide to identify the request to update a Amazon Q Business application chat configuration.</p>", "idempotencyToken": true}, "responseScope": {"shape": "ResponseScope", "documentation": "<p>The response scope configured for your application. This determines whether your application uses its retrieval augmented generation (RAG) system to generate answers only from your enterprise data, or also uses the large language models (LLM) knowledge to respons to end user questions in chat.</p>"}, "orchestrationConfiguration": {"shape": "OrchestrationConfiguration", "documentation": "<p> The chat response orchestration settings for your application.</p>"}, "blockedPhrasesConfigurationUpdate": {"shape": "BlockedPhrasesConfigurationUpdate", "documentation": "<p>The phrases blocked from chat by your chat control configuration.</p>"}, "topicConfigurationsToCreateOrUpdate": {"shape": "TopicConfigurations", "documentation": "<p>The configured topic specific chat controls you want to update.</p>"}, "topicConfigurationsToDelete": {"shape": "TopicConfigurations", "documentation": "<p>The configured topic specific chat controls you want to delete.</p>"}, "creatorModeConfiguration": {"shape": "CreatorModeConfiguration", "documentation": "<p>The configuration details for <code>CREATOR_MODE</code>.</p>"}, "hallucinationReductionConfiguration": {"shape": "HallucinationReductionConfiguration", "documentation": "<p> The hallucination reduction settings for your application.</p>"}}}, "UpdateChatControlsConfigurationResponse": {"type": "structure", "members": {}}, "UpdateChatResponseConfigurationRequest": {"type": "structure", "required": ["applicationId", "chatResponseConfigurationId", "responseConfigurations"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The unique identifier of the Amazon Q Business application containing the chat response configuration to update.</p>", "location": "uri", "locationName": "applicationId"}, "chatResponseConfigurationId": {"shape": "ChatResponseConfigurationId", "documentation": "<p>The unique identifier of the chat response configuration to update within the specified application.</p>", "location": "uri", "locationName": "chatResponseConfigurationId"}, "displayName": {"shape": "DisplayName", "documentation": "<p>The new human-readable name to assign to the chat response configuration, making it easier to identify among multiple configurations.</p>"}, "responseConfigurations": {"shape": "ResponseConfigurations", "documentation": "<p>The updated collection of response configuration settings that define how Amazon Q Business generates and formats responses to user queries.</p>"}, "clientToken": {"shape": "String", "documentation": "<p>A unique, case-sensitive identifier to ensure idempotency of the request. This helps prevent the same update from being processed multiple times if retries occur.</p>", "idempotencyToken": true}}}, "UpdateChatResponseConfigurationResponse": {"type": "structure", "members": {}}, "UpdateDataAccessorRequest": {"type": "structure", "required": ["applicationId", "dataAccessorId", "actionConfigurations"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The unique identifier of the Amazon Q Business application.</p>", "location": "uri", "locationName": "applicationId"}, "dataAccessorId": {"shape": "DataAccessorId", "documentation": "<p>The unique identifier of the data accessor to update.</p>", "location": "uri", "locationName": "dataAccessorId"}, "actionConfigurations": {"shape": "ActionConfigurationList", "documentation": "<p>The updated list of action configurations specifying the allowed actions and any associated filters.</p>"}, "authenticationDetail": {"shape": "DataAccessorAuthenticationDetail", "documentation": "<p>The updated authentication configuration details for the data accessor. This specifies how the ISV will authenticate when accessing data through this data accessor.</p>"}, "displayName": {"shape": "DataAccessorName", "documentation": "<p>The updated friendly name for the data accessor.</p>"}}}, "UpdateDataAccessorResponse": {"type": "structure", "members": {}}, "UpdateDataSourceRequest": {"type": "structure", "required": ["applicationId", "indexId", "dataSourceId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p> The identifier of the Amazon Q Business application the data source is attached to.</p>", "location": "uri", "locationName": "applicationId"}, "indexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index attached to the data source connector.</p>", "location": "uri", "locationName": "indexId"}, "dataSourceId": {"shape": "DataSourceId", "documentation": "<p>The identifier of the data source connector.</p>", "location": "uri", "locationName": "dataSourceId"}, "displayName": {"shape": "DataSourceName", "documentation": "<p>A name of the data source connector.</p>"}, "configuration": {"shape": "DataSourceConfiguration"}, "vpcConfiguration": {"shape": "DataSourceVpcConfiguration"}, "description": {"shape": "Description", "documentation": "<p>The description of the data source connector.</p>"}, "syncSchedule": {"shape": "SyncSchedule", "documentation": "<p>The chosen update frequency for your data source.</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of an IAM role with permission to access the data source and required resources.</p>"}, "documentEnrichmentConfiguration": {"shape": "DocumentEnrichmentConfiguration"}, "mediaExtractionConfiguration": {"shape": "MediaExtractionConfiguration", "documentation": "<p>The configuration for extracting information from media in documents for your data source.</p>"}}}, "UpdateDataSourceResponse": {"type": "structure", "members": {}}, "UpdateIndexRequest": {"type": "structure", "required": ["applicationId", "indexId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the Amazon Q Business application connected to the index.</p>", "location": "uri", "locationName": "applicationId"}, "indexId": {"shape": "IndexId", "documentation": "<p>The identifier of the Amazon Q Business index.</p>", "location": "uri", "locationName": "indexId"}, "displayName": {"shape": "ApplicationName", "documentation": "<p>The name of the Amazon Q Business index.</p>"}, "description": {"shape": "Description", "documentation": "<p>The description of the Amazon Q Business index.</p>"}, "capacityConfiguration": {"shape": "IndexCapacityConfiguration", "documentation": "<p>The storage capacity units you want to provision for your Amazon Q Business index. You can add and remove capacity to fit your usage needs.</p>"}, "documentAttributeConfigurations": {"shape": "DocumentAttributeConfigurations", "documentation": "<p>Configuration information for document metadata or fields. Document metadata are fields or attributes associated with your documents. For example, the company department name associated with each document. For more information, see <a href=\"https://docs.aws.amazon.com/amazonq/latest/business-use-dg/doc-attributes-types.html#doc-attributes\">Understanding document attributes</a>.</p>"}}}, "UpdateIndexResponse": {"type": "structure", "members": {}}, "UpdatePluginRequest": {"type": "structure", "required": ["applicationId", "pluginId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the application the plugin is attached to.</p>", "location": "uri", "locationName": "applicationId"}, "pluginId": {"shape": "PluginId", "documentation": "<p>The identifier of the plugin.</p>", "location": "uri", "locationName": "pluginId"}, "displayName": {"shape": "PluginName", "documentation": "<p>The name of the plugin.</p>"}, "state": {"shape": "PluginState", "documentation": "<p>The status of the plugin. </p>"}, "serverUrl": {"shape": "Url", "documentation": "<p>The source URL used for plugin configuration.</p>"}, "customPluginConfiguration": {"shape": "CustomPluginConfiguration", "documentation": "<p>The configuration for a custom plugin.</p>"}, "authConfiguration": {"shape": "PluginAuthConfiguration", "documentation": "<p>The authentication configuration the plugin is using.</p>"}}}, "UpdatePluginResponse": {"type": "structure", "members": {}}, "UpdateRetrieverRequest": {"type": "structure", "required": ["applicationId", "retrieverId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of your Amazon Q Business application.</p>", "location": "uri", "locationName": "applicationId"}, "retrieverId": {"shape": "RetrieverId", "documentation": "<p>The identifier of your retriever.</p>", "location": "uri", "locationName": "retrieverId"}, "configuration": {"shape": "RetrieverConfiguration"}, "displayName": {"shape": "RetrieverName", "documentation": "<p>The name of your retriever.</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of an IAM role with permission to access the retriever and required resources. </p>"}}}, "UpdateRetrieverResponse": {"type": "structure", "members": {}}, "UpdateSubscriptionRequest": {"type": "structure", "required": ["applicationId", "subscriptionId", "type"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the Amazon Q Business application where the subscription update should take effect.</p>", "location": "uri", "locationName": "applicationId"}, "subscriptionId": {"shape": "SubscriptionId", "documentation": "<p>The identifier of the Amazon Q Business subscription to be updated.</p>", "location": "uri", "locationName": "subscriptionId"}, "type": {"shape": "SubscriptionType", "documentation": "<p>The type of the Amazon Q Business subscription to be updated.</p>"}}}, "UpdateSubscriptionResponse": {"type": "structure", "members": {"subscriptionArn": {"shape": "SubscriptionArn", "documentation": "<p>The Amazon Resource Name (ARN) of the Amazon Q Business subscription that was updated.</p>"}, "currentSubscription": {"shape": "SubscriptionDetails", "documentation": "<p>The type of your current Amazon Q Business subscription.</p>"}, "nextSubscription": {"shape": "SubscriptionDetails", "documentation": "<p>The type of the Amazon Q Business subscription for the next month.</p>"}}}, "UpdateUserRequest": {"type": "structure", "required": ["applicationId", "userId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the application the user is attached to.</p>", "location": "uri", "locationName": "applicationId"}, "userId": {"shape": "String", "documentation": "<p>The email id attached to the user.</p>", "location": "uri", "locationName": "userId"}, "userAliasesToUpdate": {"shape": "UserAliases", "documentation": "<p>The user aliases attached to the user id that are to be updated.</p>"}, "userAliasesToDelete": {"shape": "UserAliases", "documentation": "<p>The user aliases attached to the user id that are to be deleted.</p>"}}}, "UpdateUserResponse": {"type": "structure", "members": {"userAliasesAdded": {"shape": "UserAliases", "documentation": "<p>The user aliases that have been to be added to a user id.</p>"}, "userAliasesUpdated": {"shape": "UserAliases", "documentation": "<p>The user aliases attached to a user id that have been updated.</p>"}, "userAliasesDeleted": {"shape": "UserAliases", "documentation": "<p>The user aliases that have been deleted from a user id.</p>"}}}, "UpdateWebExperienceRequest": {"type": "structure", "required": ["applicationId", "webExperienceId"], "members": {"applicationId": {"shape": "ApplicationId", "documentation": "<p>The identifier of the Amazon Q Business application attached to the web experience.</p>", "location": "uri", "locationName": "applicationId"}, "webExperienceId": {"shape": "WebExperienceId", "documentation": "<p>The identifier of the Amazon Q Business web experience.</p>", "location": "uri", "locationName": "webExperienceId"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the role with permission to access the Amazon Q Business web experience and required resources.</p>"}, "authenticationConfiguration": {"shape": "WebExperienceAuthConfiguration", "documentation": "<p>The authentication configuration of the Amazon Q Business web experience.</p>", "deprecated": true, "deprecatedMessage": "Property associated with legacy SAML IdP flow. Deprecated in favor of using AWS IAM Identity Center for user management."}, "title": {"shape": "WebExperienceTitle", "documentation": "<p>The title of the Amazon Q Business web experience.</p>"}, "subtitle": {"shape": "WebExperienceSubtitle", "documentation": "<p>The subtitle of the Amazon Q Business web experience.</p>"}, "welcomeMessage": {"shape": "WebExperienceWelcomeMessage", "documentation": "<p>A customized welcome message for an end user in an Amazon Q Business web experience.</p>"}, "samplePromptsControlMode": {"shape": "WebExperienceSamplePromptsControlMode", "documentation": "<p>Determines whether sample prompts are enabled in the web experience for an end user.</p>"}, "identityProviderConfiguration": {"shape": "IdentityProviderConfiguration", "documentation": "<p>Information about the identity provider (IdP) used to authenticate end users of an Amazon Q Business web experience.</p>"}, "origins": {"shape": "WebExperienceOrigins", "documentation": "<p>Updates the website domain origins that are allowed to embed the Amazon Q Business web experience. The <i>domain origin</i> refers to the <i>base URL</i> for accessing a website including the protocol (<code>http/https</code>), the domain name, and the port number (if specified).</p> <note> <ul> <li> <p>Any values except <code>null</code> submitted as part of this update will replace all previous values.</p> </li> <li> <p>You must only submit a <i>base URL</i> and not a full path. For example, <code>https://docs.aws.amazon.com</code>.</p> </li> </ul> </note>"}, "browserExtensionConfiguration": {"shape": "BrowserExtensionConfiguration", "documentation": "<p>The browser extension configuration for an Amazon Q Business web experience.</p> <note> <p> For Amazon Q Business application using external OIDC-compliant identity providers (IdPs). The IdP administrator must add the browser extension sign-in redirect URLs to the IdP application. For more information, see <a href=\"https://docs.aws.amazon.com/amazonq/latest/qbusiness-ug/browser-extensions.html\">Configure external OIDC identity provider for your browser extensions.</a>. </p> </note>"}, "customizationConfiguration": {"shape": "CustomizationConfiguration", "documentation": "<p>Updates the custom logo, favicon, font, and color used in the Amazon Q web experience. </p>"}}}, "UpdateWebExperienceResponse": {"type": "structure", "members": {}}, "Url": {"type": "string", "max": 2048, "min": 1, "pattern": "(https?|ftp|file)://([^\\s]*)"}, "UserAlias": {"type": "structure", "required": ["userId"], "members": {"indexId": {"shape": "IndexId", "documentation": "<p>The identifier of the index that the user aliases are associated with.</p>"}, "dataSourceId": {"shape": "DataSourceId", "documentation": "<p>The identifier of the data source that the user aliases are associated with.</p>"}, "userId": {"shape": "String", "documentation": "<p>The identifier of the user id associated with the user aliases.</p>"}}, "documentation": "<p>Aliases attached to a user id within an Amazon Q Business application.</p>"}, "UserAliases": {"type": "list", "member": {"shape": "UserAlias"}}, "UserGroups": {"type": "list", "member": {"shape": "String"}}, "UserId": {"type": "string", "max": 1024, "min": 1, "pattern": "\\P{C}*"}, "UserIdentifier": {"type": "string", "max": 47, "min": 1, "pattern": "([0-9a-f]{10}-|)[A-Fa-f0-9]{8}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{12}"}, "UserIds": {"type": "list", "member": {"shape": "String"}}, "UserMessage": {"type": "string"}, "UsersAndGroups": {"type": "structure", "members": {"userIds": {"shape": "UserIds", "documentation": "<p>The user ids associated with a topic control rule.</p>"}, "userGroups": {"shape": "UserGroups", "documentation": "<p>The user group names associated with a topic control rule.</p>"}}, "documentation": "<p>Provides information about users and group names associated with a topic control rule.</p>"}, "ValidationException": {"type": "structure", "required": ["message", "reason"], "members": {"message": {"shape": "ErrorMessage", "documentation": "<p>The message describing the <code>ValidationException</code>.</p>"}, "reason": {"shape": "ValidationExceptionReason", "documentation": "<p>The reason for the <code>ValidationException</code>.</p>"}, "fields": {"shape": "ValidationExceptionFields", "documentation": "<p>The input field(s) that failed validation.</p>"}}, "documentation": "<p>The input doesn't meet the constraints set by the Amazon Q Business service. Provide the correct input and try again.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "ValidationExceptionField": {"type": "structure", "required": ["name", "message"], "members": {"name": {"shape": "String", "documentation": "<p>The field name where the invalid entry was detected.</p>"}, "message": {"shape": "String", "documentation": "<p>A message about the validation exception.</p>"}}, "documentation": "<p>The input failed to meet the constraints specified by Amazon Q Business in a specified field.</p>"}, "ValidationExceptionFields": {"type": "list", "member": {"shape": "ValidationExceptionField"}}, "ValidationExceptionReason": {"type": "string", "enum": ["CANNOT_PARSE", "FIELD_VALIDATION_FAILED", "UNKNOWN_OPERATION"]}, "VideoExtractionConfiguration": {"type": "structure", "required": ["videoExtractionStatus"], "members": {"videoExtractionStatus": {"shape": "VideoExtractionStatus", "documentation": "<p>The status of video extraction (ENABLED or DISABLED) for processing video content from files.</p>"}}, "documentation": "<p>Configuration settings for video content extraction and processing.</p>"}, "VideoExtractionStatus": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "VideoExtractionType": {"type": "string", "enum": ["TRANSCRIPT", "SUMMARY"]}, "VideoSourceDetails": {"type": "structure", "members": {"mediaId": {"shape": "MediaId", "documentation": "<p>Unique identifier for the video media file.</p>"}, "mediaMimeType": {"shape": "String", "documentation": "<p>The MIME type of the video file (e.g., video/mp4, video/avi).</p>"}, "startTimeMilliseconds": {"shape": "<PERSON>", "documentation": "<p>The starting timestamp in milliseconds for the relevant video segment.</p>"}, "endTimeMilliseconds": {"shape": "<PERSON>", "documentation": "<p>The ending timestamp in milliseconds for the relevant video segment.</p>"}, "videoExtractionType": {"shape": "VideoExtractionType", "documentation": "<p>The type of video extraction performed on the content.</p>"}}, "documentation": "<p>Details about a video source, including its identifier, format, and time information.</p>"}, "WebExperience": {"type": "structure", "members": {"webExperienceId": {"shape": "WebExperienceId", "documentation": "<p>The identifier of your Amazon Q Business web experience.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when the Amazon Q Business application was last updated.</p>"}, "updatedAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp when your Amazon Q Business web experience was updated.</p>"}, "defaultEndpoint": {"shape": "Url", "documentation": "<p>The endpoint URLs for your Amazon Q Business web experience. The URLs are unique and fully hosted by Amazon Web Services.</p>"}, "status": {"shape": "WebExperienceStatus", "documentation": "<p>The status of your Amazon Q Business web experience.</p>"}}, "documentation": "<p>Provides information for an Amazon Q Business web experience.</p>"}, "WebExperienceArn": {"type": "string", "max": 1284, "min": 0, "pattern": "arn:[a-z0-9-\\.]{1,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[^/].{0,1023}"}, "WebExperienceAuthConfiguration": {"type": "structure", "members": {"samlConfiguration": {"shape": "SamlConfiguration"}}, "documentation": "<p>Provides the authorization configuration information needed to deploy a Amazon Q Business web experience to end users.</p>", "union": true}, "WebExperienceId": {"type": "string", "max": 36, "min": 36, "pattern": "[a-zA-Z0-9][a-zA-Z0-9-]*"}, "WebExperienceOrigins": {"type": "list", "member": {"shape": "Origin"}, "max": 10, "min": 0}, "WebExperienceSamplePromptsControlMode": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "WebExperienceStatus": {"type": "string", "enum": ["CREATING", "ACTIVE", "DELETING", "FAILED", "PENDING_AUTH_CONFIG"]}, "WebExperienceSubtitle": {"type": "string", "max": 500, "min": 0, "pattern": "[\\s\\S]*"}, "WebExperienceTitle": {"type": "string", "max": 500, "min": 0, "pattern": "[\\s\\S]*"}, "WebExperienceWelcomeMessage": {"type": "string", "max": 300, "min": 0}, "WebExperiences": {"type": "list", "member": {"shape": "WebExperience"}}}, "documentation": "<p>This is the <i>Amazon Q Business</i> API Reference. Amazon Q Business is a fully managed, generative-AI powered enterprise chat assistant that you can deploy within your organization. Amazon Q Business enhances employee productivity by supporting key tasks such as question-answering, knowledge discovery, writing email messages, summarizing text, drafting document outlines, and brainstorming ideas. Users ask questions of Amazon Q Business and get answers that are presented in a conversational manner. For an introduction to the service, see the <a href=\"https://docs.aws.amazon.com/amazonq/latest/business-use-dg/what-is.html\"> <i>Amazon Q Business User Guide</i> </a>.</p> <p>For an overview of the Amazon Q Business APIs, see <a href=\"https://docs.aws.amazon.com/amazonq/latest/business-use-dg/api-ref.html#api-overview\">Overview of Amazon Q Business API operations</a>.</p> <p>For information about the IAM access control permissions you need to use this API, see <a href=\"https://docs.aws.amazon.com/amazonq/latest/business-use-dg/iam-roles.html\">IAM roles for Amazon Q Business</a> in the <i>Amazon Q Business User Guide</i>.</p> <p>The following resources provide additional information about using the Amazon Q Business API:</p> <ul> <li> <p> <i> <a href=\"https://docs.aws.amazon.com/amazonq/latest/business-use-dg/setting-up.html\">Setting up for Amazon Q Business</a> </i> </p> </li> <li> <p> <i> <a href=\"https://awscli.amazonaws.com/v2/documentation/api/latest/reference/qbusiness/index.html\">Amazon Q Business CLI Reference</a> </i> </p> </li> <li> <p> <i> <a href=\"https://docs.aws.amazon.com/general/latest/gr/amazonq.html\">Amazon Web Services General Reference</a> </i> </p> </li> </ul>"}