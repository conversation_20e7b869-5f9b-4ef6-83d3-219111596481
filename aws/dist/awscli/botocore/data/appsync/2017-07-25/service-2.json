{"version": "2.0", "metadata": {"apiVersion": "2017-07-25", "endpointPrefix": "appsync", "jsonVersion": "1.1", "protocol": "rest-json", "protocols": ["rest-json"], "serviceAbbreviation": "AWSAppSync", "serviceFullName": "AWS AppSync", "serviceId": "AppSync", "signatureVersion": "v4", "signingName": "appsync", "uid": "appsync-2017-07-25", "auth": ["aws.auth#sigv4"]}, "operations": {"AssociateApi": {"name": "AssociateApi", "http": {"method": "POST", "requestUri": "/v1/domainnames/{domainName}/apiassociation"}, "input": {"shape": "AssociateApiRequest"}, "output": {"shape": "AssociateApiResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "BadRequestException"}, {"shape": "InternalFailureException"}, {"shape": "NotFoundException"}], "documentation": "<p>Maps an endpoint to your custom domain.</p>"}, "AssociateMergedGraphqlApi": {"name": "AssociateMergedGraphqlApi", "http": {"method": "POST", "requestUri": "/v1/sourceApis/{sourceApiIdentifier}/mergedApiAssociations"}, "input": {"shape": "AssociateMergedGraphqlApiRequest"}, "output": {"shape": "AssociateMergedGraphqlApiResponse"}, "errors": [{"shape": "UnauthorizedException"}, {"shape": "BadRequestException"}, {"shape": "InternalFailureException"}, {"shape": "NotFoundException"}, {"shape": "LimitExceededException"}, {"shape": "ConcurrentModificationException"}], "documentation": "<p>Creates an association between a Merged API and source API using the source API's identifier.</p>"}, "AssociateSourceGraphqlApi": {"name": "AssociateSourceGraphqlApi", "http": {"method": "POST", "requestUri": "/v1/mergedApis/{mergedApiIdentifier}/sourceApiAssociations"}, "input": {"shape": "AssociateSourceGraphqlApiRequest"}, "output": {"shape": "AssociateSourceGraphqlApiResponse"}, "errors": [{"shape": "UnauthorizedException"}, {"shape": "BadRequestException"}, {"shape": "InternalFailureException"}, {"shape": "NotFoundException"}, {"shape": "LimitExceededException"}, {"shape": "ConcurrentModificationException"}], "documentation": "<p>Creates an association between a Merged API and source API using the Merged API's identifier.</p>"}, "CreateApi": {"name": "CreateApi", "http": {"method": "POST", "requestUri": "/v2/apis"}, "input": {"shape": "CreateApiRequest"}, "output": {"shape": "CreateApiResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ConcurrentModificationException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates an <code>Api</code> object. Use this operation to create an AppSync API with your preferred configuration, such as an Event API that provides real-time message publishing and message subscriptions over WebSockets.</p>"}, "CreateApiCache": {"name": "CreateApiCache", "http": {"method": "POST", "requestUri": "/v1/apis/{apiId}/ApiCaches"}, "input": {"shape": "CreateApiCacheRequest"}, "output": {"shape": "CreateApiCacheResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ConcurrentModificationException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}], "documentation": "<p>Creates a cache for the GraphQL API.</p>"}, "CreateApiKey": {"name": "CreateApiKey", "http": {"method": "POST", "requestUri": "/v1/apis/{apiId}/apikeys"}, "input": {"shape": "CreateApiKeyRequest"}, "output": {"shape": "CreateApiKeyResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "NotFoundException"}, {"shape": "LimitExceededException"}, {"shape": "UnauthorizedException"}, {"shape": "LimitExceededException"}, {"shape": "InternalFailureException"}, {"shape": "ApiKeyLimitExceededException"}, {"shape": "ApiKeyValidityOutOfBoundsException"}], "documentation": "<p>Creates a unique key that you can distribute to clients who invoke your API.</p>"}, "CreateChannelNamespace": {"name": "CreateChannelNamespace", "http": {"method": "POST", "requestUri": "/v2/apis/{apiId}/channelNamespaces"}, "input": {"shape": "CreateChannelNamespaceRequest"}, "output": {"shape": "CreateChannelNamespaceResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ConcurrentModificationException"}, {"shape": "ConflictException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates a <code>ChannelNamespace</code> for an <code>Api</code>.</p>"}, "CreateDataSource": {"name": "CreateDataSource", "http": {"method": "POST", "requestUri": "/v1/apis/{apiId}/datasources"}, "input": {"shape": "CreateDataSourceRequest"}, "output": {"shape": "CreateDataSourceResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ConcurrentModificationException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}], "documentation": "<p>Creates a <code>DataSource</code> object.</p>"}, "CreateDomainName": {"name": "CreateDomainName", "http": {"method": "POST", "requestUri": "/v1/domainnames"}, "input": {"shape": "CreateDomainNameRequest"}, "output": {"shape": "CreateDomainNameResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "BadRequestException"}, {"shape": "InternalFailureException"}], "documentation": "<p>Creates a custom <code>DomainName</code> object.</p>"}, "CreateFunction": {"name": "CreateFunction", "http": {"method": "POST", "requestUri": "/v1/apis/{apiId}/functions"}, "input": {"shape": "CreateFunctionRequest"}, "output": {"shape": "CreateFunctionResponse"}, "errors": [{"shape": "ConcurrentModificationException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}, {"shape": "BadRequestException"}], "documentation": "<p>Creates a <code>Function</code> object.</p> <p>A function is a reusable entity. You can use multiple functions to compose the resolver logic.</p>"}, "CreateGraphqlApi": {"name": "CreateGraphqlApi", "http": {"method": "POST", "requestUri": "/v1/apis"}, "input": {"shape": "CreateGraphqlApiRequest"}, "output": {"shape": "CreateGraphqlApiResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "LimitExceededException"}, {"shape": "ConcurrentModificationException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}, {"shape": "ApiLimitExceededException"}], "documentation": "<p>Creates a <code>GraphqlApi</code> object.</p>"}, "CreateResolver": {"name": "CreateResolver", "http": {"method": "POST", "requestUri": "/v1/apis/{apiId}/types/{typeName}/resolvers"}, "input": {"shape": "CreateResolverRequest"}, "output": {"shape": "CreateResolverResponse"}, "errors": [{"shape": "ConcurrentModificationException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}, {"shape": "BadRequestException"}], "documentation": "<p>Creates a <code>Resolver</code> object.</p> <p>A resolver converts incoming requests into a format that a data source can understand, and converts the data source's responses into GraphQL.</p>"}, "CreateType": {"name": "CreateType", "http": {"method": "POST", "requestUri": "/v1/apis/{apiId}/types"}, "input": {"shape": "CreateTypeRequest"}, "output": {"shape": "CreateTypeResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ConcurrentModificationException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}], "documentation": "<p>Creates a <code>Type</code> object.</p>"}, "DeleteApi": {"name": "DeleteApi", "http": {"method": "DELETE", "requestUri": "/v2/apis/{apiId}"}, "input": {"shape": "DeleteApiRequest"}, "output": {"shape": "DeleteApiResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ConcurrentModificationException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Deletes an <code>Api</code> object</p>", "idempotent": true}, "DeleteApiCache": {"name": "DeleteApiCache", "http": {"method": "DELETE", "requestUri": "/v1/apis/{apiId}/ApiCaches"}, "input": {"shape": "DeleteApiCacheRequest"}, "output": {"shape": "DeleteApiCacheResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ConcurrentModificationException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}], "documentation": "<p>Deletes an <code>ApiCache</code> object.</p>"}, "DeleteApiKey": {"name": "DeleteApiKey", "http": {"method": "DELETE", "requestUri": "/v1/apis/{apiId}/apikeys/{id}"}, "input": {"shape": "DeleteApiKeyRequest"}, "output": {"shape": "DeleteApiKeyResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}], "documentation": "<p>Deletes an API key.</p>"}, "DeleteChannelNamespace": {"name": "DeleteChannelNamespace", "http": {"method": "DELETE", "requestUri": "/v2/apis/{apiId}/channelNamespaces/{name}"}, "input": {"shape": "DeleteChannelNamespaceRequest"}, "output": {"shape": "DeleteChannelNamespaceResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ConcurrentModificationException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Deletes a <code>ChannelNamespace</code>.</p>", "idempotent": true}, "DeleteDataSource": {"name": "DeleteDataSource", "http": {"method": "DELETE", "requestUri": "/v1/apis/{apiId}/datasources/{name}"}, "input": {"shape": "DeleteDataSourceRequest"}, "output": {"shape": "DeleteDataSourceResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ConcurrentModificationException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}], "documentation": "<p>Deletes a <code>DataSource</code> object.</p>"}, "DeleteDomainName": {"name": "DeleteDomainName", "http": {"method": "DELETE", "requestUri": "/v1/domainnames/{domainName}"}, "input": {"shape": "DeleteDomainNameRequest"}, "output": {"shape": "DeleteDomainNameResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "BadRequestException"}, {"shape": "ConcurrentModificationException"}, {"shape": "InternalFailureException"}, {"shape": "NotFoundException"}], "documentation": "<p>Deletes a custom <code>DomainName</code> object.</p>"}, "DeleteFunction": {"name": "DeleteFunction", "http": {"method": "DELETE", "requestUri": "/v1/apis/{apiId}/functions/{functionId}"}, "input": {"shape": "DeleteFunctionRequest"}, "output": {"shape": "DeleteFunctionResponse"}, "errors": [{"shape": "ConcurrentModificationException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}, {"shape": "BadRequestException"}], "documentation": "<p>Deletes a <code>Function</code>.</p>"}, "DeleteGraphqlApi": {"name": "DeleteGraphqlApi", "http": {"method": "DELETE", "requestUri": "/v1/apis/{apiId}"}, "input": {"shape": "DeleteGraphqlApiRequest"}, "output": {"shape": "DeleteGraphqlApiResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ConcurrentModificationException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Deletes a <code>GraphqlApi</code> object.</p>"}, "DeleteResolver": {"name": "DeleteResolver", "http": {"method": "DELETE", "requestUri": "/v1/apis/{apiId}/types/{typeName}/resolvers/{fieldName}"}, "input": {"shape": "DeleteResolverRequest"}, "output": {"shape": "DeleteResolverResponse"}, "errors": [{"shape": "ConcurrentModificationException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}, {"shape": "BadRequestException"}], "documentation": "<p>Deletes a <code>Resolver</code> object.</p>"}, "DeleteType": {"name": "DeleteType", "http": {"method": "DELETE", "requestUri": "/v1/apis/{apiId}/types/{typeName}"}, "input": {"shape": "DeleteTypeRequest"}, "output": {"shape": "DeleteTypeResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ConcurrentModificationException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}], "documentation": "<p>Deletes a <code>Type</code> object.</p>"}, "DisassociateApi": {"name": "DisassociateApi", "http": {"method": "DELETE", "requestUri": "/v1/domainnames/{domainName}/apiassociation"}, "input": {"shape": "DisassociateApiRequest"}, "output": {"shape": "DisassociateApiResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "BadRequestException"}, {"shape": "ConcurrentModificationException"}, {"shape": "InternalFailureException"}, {"shape": "NotFoundException"}], "documentation": "<p>Removes an <code>ApiAssociation</code> object from a custom domain.</p>"}, "DisassociateMergedGraphqlApi": {"name": "DisassociateMergedGraphqlApi", "http": {"method": "DELETE", "requestUri": "/v1/sourceApis/{sourceApiIdentifier}/mergedApiAssociations/{associationId}"}, "input": {"shape": "DisassociateMergedGraphqlApiRequest"}, "output": {"shape": "DisassociateMergedGraphqlApiResponse"}, "errors": [{"shape": "UnauthorizedException"}, {"shape": "BadRequestException"}, {"shape": "InternalFailureException"}, {"shape": "NotFoundException"}, {"shape": "ConcurrentModificationException"}], "documentation": "<p>Deletes an association between a Merged API and source API using the source API's identifier and the association ID.</p>"}, "DisassociateSourceGraphqlApi": {"name": "DisassociateSourceGraphqlApi", "http": {"method": "DELETE", "requestUri": "/v1/mergedApis/{mergedApiIdentifier}/sourceApiAssociations/{associationId}"}, "input": {"shape": "DisassociateSourceGraphqlApiRequest"}, "output": {"shape": "DisassociateSourceGraphqlApiResponse"}, "errors": [{"shape": "UnauthorizedException"}, {"shape": "BadRequestException"}, {"shape": "InternalFailureException"}, {"shape": "NotFoundException"}, {"shape": "ConcurrentModificationException"}], "documentation": "<p>Deletes an association between a Merged API and source API using the Merged API's identifier and the association ID.</p>"}, "EvaluateCode": {"name": "EvaluateCode", "http": {"method": "POST", "requestUri": "/v1/dataplane-evaluatecode"}, "input": {"shape": "EvaluateCodeRequest"}, "output": {"shape": "EvaluateCodeResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "InternalFailureException"}, {"shape": "BadRequestException"}], "documentation": "<p>Evaluates the given code and returns the response. The code definition requirements depend on the specified runtime. For <code>APPSYNC_JS</code> runtimes, the code defines the request and response functions. The request function takes the incoming request after a GraphQL operation is parsed and converts it into a request configuration for the selected data source operation. The response function interprets responses from the data source and maps it to the shape of the GraphQL field output type. </p>"}, "EvaluateMappingTemplate": {"name": "EvaluateMappingTemplate", "http": {"method": "POST", "requestUri": "/v1/dataplane-evaluatetemplate"}, "input": {"shape": "EvaluateMappingTemplateRequest"}, "output": {"shape": "EvaluateMappingTemplateResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "InternalFailureException"}, {"shape": "BadRequestException"}], "documentation": "<p>Evaluates a given template and returns the response. The mapping template can be a request or response template.</p> <p>Request templates take the incoming request after a GraphQL operation is parsed and convert it into a request configuration for the selected data source operation. Response templates interpret responses from the data source and map it to the shape of the GraphQL field output type.</p> <p>Mapping templates are written in the Apache Velocity Template Language (VTL).</p>"}, "FlushApiCache": {"name": "FlushApiCache", "http": {"method": "DELETE", "requestUri": "/v1/apis/{apiId}/FlushCache"}, "input": {"shape": "FlushApiCacheRequest"}, "output": {"shape": "FlushApiCacheResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ConcurrentModificationException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}], "documentation": "<p>Flushes an <code>ApiCache</code> object.</p>"}, "GetApi": {"name": "GetApi", "http": {"method": "GET", "requestUri": "/v2/apis/{apiId}"}, "input": {"shape": "GetApiRequest"}, "output": {"shape": "GetApiResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves an <code>Api</code> object.</p>"}, "GetApiAssociation": {"name": "GetApiAssociation", "http": {"method": "GET", "requestUri": "/v1/domainnames/{domainName}/apiassociation"}, "input": {"shape": "GetApiAssociationRequest"}, "output": {"shape": "GetApiAssociationResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "BadRequestException"}, {"shape": "InternalFailureException"}, {"shape": "NotFoundException"}], "documentation": "<p>Retrieves an <code>ApiAssociation</code> object.</p>"}, "GetApiCache": {"name": "GetApiCache", "http": {"method": "GET", "requestUri": "/v1/apis/{apiId}/ApiCaches"}, "input": {"shape": "GetApiCacheRequest"}, "output": {"shape": "GetApiCacheResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ConcurrentModificationException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}], "documentation": "<p>Retrieves an <code>ApiCache</code> object.</p>"}, "GetChannelNamespace": {"name": "GetChannelNamespace", "http": {"method": "GET", "requestUri": "/v2/apis/{apiId}/channelNamespaces/{name}"}, "input": {"shape": "GetChannelNamespaceRequest"}, "output": {"shape": "GetChannelNamespaceResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves the channel namespace for a specified <code>Api</code>.</p>"}, "GetDataSource": {"name": "GetDataSource", "http": {"method": "GET", "requestUri": "/v1/apis/{apiId}/datasources/{name}"}, "input": {"shape": "GetDataSourceRequest"}, "output": {"shape": "GetDataSourceResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ConcurrentModificationException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}], "documentation": "<p>Retrieves a <code>DataSource</code> object.</p>"}, "GetDataSourceIntrospection": {"name": "GetDataSourceIntrospection", "http": {"method": "GET", "requestUri": "/v1/datasources/introspections/{introspectionId}"}, "input": {"shape": "GetDataSourceIntrospectionRequest"}, "output": {"shape": "GetDataSourceIntrospectionResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "NotFoundException"}, {"shape": "InternalFailureException"}], "documentation": "<p>Retrieves the record of an existing introspection. If the retrieval is successful, the result of the instrospection will also be returned. If the retrieval fails the operation, an error message will be returned instead.</p>"}, "GetDomainName": {"name": "GetDomainName", "http": {"method": "GET", "requestUri": "/v1/domainnames/{domainName}"}, "input": {"shape": "GetDomainNameRequest"}, "output": {"shape": "GetDomainNameResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "BadRequestException"}, {"shape": "InternalFailureException"}, {"shape": "NotFoundException"}], "documentation": "<p>Retrieves a custom <code>DomainName</code> object.</p>"}, "GetFunction": {"name": "GetFunction", "http": {"method": "GET", "requestUri": "/v1/apis/{apiId}/functions/{functionId}"}, "input": {"shape": "GetFunctionRequest"}, "output": {"shape": "GetFunctionResponse"}, "errors": [{"shape": "ConcurrentModificationException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}], "documentation": "<p>Get a <code>Function</code>.</p>"}, "GetGraphqlApi": {"name": "GetGraphqlApi", "http": {"method": "GET", "requestUri": "/v1/apis/{apiId}"}, "input": {"shape": "GetGraphqlApiRequest"}, "output": {"shape": "GetGraphqlApiResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves a <code>GraphqlApi</code> object.</p>"}, "GetGraphqlApiEnvironmentVariables": {"name": "GetGraphqlApiEnvironmentVariables", "http": {"method": "GET", "requestUri": "/v1/apis/{apiId}/environmentVariables"}, "input": {"shape": "GetGraphqlApiEnvironmentVariablesRequest"}, "output": {"shape": "GetGraphqlApiEnvironmentVariablesResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves the list of environmental variable key-value pairs associated with an API by its ID value.</p>"}, "GetIntrospectionSchema": {"name": "GetIntrospectionSchema", "http": {"method": "GET", "requestUri": "/v1/apis/{apiId}/schema"}, "input": {"shape": "GetIntrospectionSchemaRequest"}, "output": {"shape": "GetIntrospectionSchemaResponse"}, "errors": [{"shape": "GraphQLSchemaException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}], "documentation": "<p>Retrieves the introspection schema for a GraphQL API.</p>"}, "GetResolver": {"name": "GetResolver", "http": {"method": "GET", "requestUri": "/v1/apis/{apiId}/types/{typeName}/resolvers/{fieldName}"}, "input": {"shape": "GetResolverRequest"}, "output": {"shape": "GetResolverResponse"}, "errors": [{"shape": "ConcurrentModificationException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}], "documentation": "<p>Retrieves a <code>Resolver</code> object.</p>"}, "GetSchemaCreationStatus": {"name": "GetSchemaCreationStatus", "http": {"method": "GET", "requestUri": "/v1/apis/{apiId}/schemacreation"}, "input": {"shape": "GetSchemaCreationStatusRequest"}, "output": {"shape": "GetSchemaCreationStatusResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}], "documentation": "<p>Retrieves the current status of a schema creation operation.</p>"}, "GetSourceApiAssociation": {"name": "GetSourceApiAssociation", "http": {"method": "GET", "requestUri": "/v1/mergedApis/{mergedApiIdentifier}/sourceApiAssociations/{associationId}"}, "input": {"shape": "GetSourceApiAssociationRequest"}, "output": {"shape": "GetSourceApiAssociationResponse"}, "errors": [{"shape": "UnauthorizedException"}, {"shape": "BadRequestException"}, {"shape": "InternalFailureException"}, {"shape": "NotFoundException"}], "documentation": "<p>Retrieves a <code>SourceApiAssociation</code> object.</p>"}, "GetType": {"name": "GetType", "http": {"method": "GET", "requestUri": "/v1/apis/{apiId}/types/{typeName}"}, "input": {"shape": "GetTypeRequest"}, "output": {"shape": "GetTypeResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ConcurrentModificationException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}], "documentation": "<p>Retrieves a <code>Type</code> object.</p>"}, "ListApiKeys": {"name": "ListApiKeys", "http": {"method": "GET", "requestUri": "/v1/apis/{apiId}/apikeys"}, "input": {"shape": "ListApiKeysRequest"}, "output": {"shape": "ListApiKeysResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}], "documentation": "<p>Lists the API keys for a given API.</p> <note> <p>API keys are deleted automatically 60 days after they expire. However, they may still be included in the response until they have actually been deleted. You can safely call <code>DeleteApi<PERSON>ey</code> to manually delete a key before it's automatically deleted.</p> </note>"}, "ListApis": {"name": "ListApis", "http": {"method": "GET", "requestUri": "/v2/apis"}, "input": {"shape": "ListApisRequest"}, "output": {"shape": "ListApisResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}], "documentation": "<p>Lists the APIs in your AppSync account.</p> <p> <code>ListApis</code> returns only the high level API details. For more detailed information about an API, use <code>GetApi</code>.</p>"}, "ListChannelNamespaces": {"name": "ListChannelNamespaces", "http": {"method": "GET", "requestUri": "/v2/apis/{apiId}/channelNamespaces"}, "input": {"shape": "ListChannelNamespacesRequest"}, "output": {"shape": "ListChannelNamespacesResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}], "documentation": "<p>Lists the channel namespaces for a specified <code>Api</code>.</p> <p> <code>ListChannelNamespaces</code> returns only high level details for the channel namespace. To retrieve code handlers, use <code>GetChannelNamespace</code>.</p>"}, "ListDataSources": {"name": "ListDataSources", "http": {"method": "GET", "requestUri": "/v1/apis/{apiId}/datasources"}, "input": {"shape": "ListDataSourcesRequest"}, "output": {"shape": "ListDataSourcesResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}], "documentation": "<p>Lists the data sources for a given API.</p>"}, "ListDomainNames": {"name": "ListDomainNames", "http": {"method": "GET", "requestUri": "/v1/domainnames"}, "input": {"shape": "ListDomainNamesRequest"}, "output": {"shape": "ListDomainNamesResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "BadRequestException"}, {"shape": "InternalFailureException"}], "documentation": "<p>Lists multiple custom domain names.</p>"}, "ListFunctions": {"name": "ListFunctions", "http": {"method": "GET", "requestUri": "/v1/apis/{apiId}/functions"}, "input": {"shape": "ListFunctionsRequest"}, "output": {"shape": "ListFunctionsResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}], "documentation": "<p>List multiple functions.</p>"}, "ListGraphqlApis": {"name": "ListGraphqlApis", "http": {"method": "GET", "requestUri": "/v1/apis"}, "input": {"shape": "ListGraphqlApisRequest"}, "output": {"shape": "ListGraphqlApisResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}], "documentation": "<p>Lists your GraphQL APIs.</p>"}, "ListResolvers": {"name": "ListResolvers", "http": {"method": "GET", "requestUri": "/v1/apis/{apiId}/types/{typeName}/resolvers"}, "input": {"shape": "ListResolversRequest"}, "output": {"shape": "ListResolversResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}], "documentation": "<p>Lists the resolvers for a given API and type.</p>"}, "ListResolversByFunction": {"name": "ListResolversByFunction", "http": {"method": "GET", "requestUri": "/v1/apis/{apiId}/functions/{functionId}/resolvers"}, "input": {"shape": "ListResolversByFunctionRequest"}, "output": {"shape": "ListResolversByFunctionResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}], "documentation": "<p>List the resolvers that are associated with a specific function.</p>"}, "ListSourceApiAssociations": {"name": "ListSourceApiAssociations", "http": {"method": "GET", "requestUri": "/v1/apis/{apiId}/sourceApiAssociations"}, "input": {"shape": "ListSourceApiAssociationsRequest"}, "output": {"shape": "ListSourceApiAssociationsResponse"}, "errors": [{"shape": "UnauthorizedException"}, {"shape": "BadRequestException"}, {"shape": "InternalFailureException"}, {"shape": "NotFoundException"}], "documentation": "<p>Lists the <code>SourceApiAssociationSummary</code> data.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/v1/tags/{resourceArn}"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "NotFoundException"}, {"shape": "LimitExceededException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists the tags for a resource.</p>"}, "ListTypes": {"name": "ListTypes", "http": {"method": "GET", "requestUri": "/v1/apis/{apiId}/types"}, "input": {"shape": "ListTypesRequest"}, "output": {"shape": "ListTypesResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ConcurrentModificationException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}], "documentation": "<p>Lists the types for a given API.</p>"}, "ListTypesByAssociation": {"name": "ListTypesByAssociation", "http": {"method": "GET", "requestUri": "/v1/mergedApis/{mergedApiIdentifier}/sourceApiAssociations/{associationId}/types"}, "input": {"shape": "ListTypesByAssociationRequest"}, "output": {"shape": "ListTypesByAssociationResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ConcurrentModificationException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}], "documentation": "<p>Lists <code>Type</code> objects by the source API association ID.</p>"}, "PutGraphqlApiEnvironmentVariables": {"name": "PutGraphqlApiEnvironmentVariables", "http": {"method": "PUT", "requestUri": "/v1/apis/{apiId}/environmentVariables"}, "input": {"shape": "PutGraphqlApiEnvironmentVariablesRequest"}, "output": {"shape": "PutGraphqlApiEnvironmentVariablesResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ConcurrentModificationException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Creates a list of environmental variables in an API by its ID value. </p> <p>When creating an environmental variable, it must follow the constraints below:</p> <ul> <li> <p>Both JavaScript and VTL templates support environmental variables.</p> </li> <li> <p>Environmental variables are not evaluated before function invocation.</p> </li> <li> <p>Environmental variables only support string values.</p> </li> <li> <p>Any defined value in an environmental variable is considered a string literal and not expanded.</p> </li> <li> <p>Variable evaluations should ideally be performed in the function code.</p> </li> </ul> <p>When creating an environmental variable key-value pair, it must follow the additional constraints below:</p> <ul> <li> <p>Keys must begin with a letter.</p> </li> <li> <p>Keys must be at least two characters long.</p> </li> <li> <p>Keys can only contain letters, numbers, and the underscore character (_).</p> </li> <li> <p>Values can be up to 512 characters long.</p> </li> <li> <p>You can configure up to 50 key-value pairs in a GraphQL API.</p> </li> </ul> <p>You can create a list of environmental variables by adding it to the <code>environmentVariables</code> payload as a list in the format <code>{\"key1\":\"value1\",\"key2\":\"value2\", …}</code>. Note that each call of the <code>PutGraphqlApiEnvironmentVariables</code> action will result in the overwriting of the existing environmental variable list of that API. This means the existing environmental variables will be lost. To avoid this, you must include all existing and new environmental variables in the list each time you call this action.</p>"}, "StartDataSourceIntrospection": {"name": "StartDataSourceIntrospection", "http": {"method": "POST", "requestUri": "/v1/datasources/introspections"}, "input": {"shape": "StartDataSourceIntrospectionRequest"}, "output": {"shape": "StartDataSourceIntrospectionResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}, {"shape": "BadRequestException"}], "documentation": "<p>Creates a new introspection. Returns the <code>introspectionId</code> of the new introspection after its creation. </p>"}, "StartSchemaCreation": {"name": "StartSchemaCreation", "http": {"method": "POST", "requestUri": "/v1/apis/{apiId}/schemacreation"}, "input": {"shape": "StartSchemaCreationRequest"}, "output": {"shape": "StartSchemaCreationResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ConcurrentModificationException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}], "documentation": "<p>Adds a new schema to your GraphQL API.</p> <p>This operation is asynchronous. Use to determine when it has completed.</p>"}, "StartSchemaMerge": {"name": "StartSchemaMerge", "http": {"method": "POST", "requestUri": "/v1/mergedApis/{mergedApiIdentifier}/sourceApiAssociations/{associationId}/merge"}, "input": {"shape": "StartSchemaMergeRequest"}, "output": {"shape": "StartSchemaMergeResponse"}, "errors": [{"shape": "UnauthorizedException"}, {"shape": "BadRequestException"}, {"shape": "InternalFailureException"}, {"shape": "NotFoundException"}, {"shape": "ConcurrentModificationException"}], "documentation": "<p>Initiates a merge operation. Returns a status that shows the result of the merge operation.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/v1/tags/{resourceArn}"}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "NotFoundException"}, {"shape": "LimitExceededException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Tags a resource with user-supplied tags.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/v1/tags/{resourceArn}"}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "NotFoundException"}, {"shape": "LimitExceededException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Untags a resource.</p>"}, "UpdateApi": {"name": "UpdateA<PERSON>", "http": {"method": "POST", "requestUri": "/v2/apis/{apiId}"}, "input": {"shape": "UpdateApiRequest"}, "output": {"shape": "UpdateApiResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ConcurrentModificationException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Updates an <code>Api</code>.</p>"}, "UpdateApiCache": {"name": "UpdateApiCache", "http": {"method": "POST", "requestUri": "/v1/apis/{apiId}/ApiCaches/update"}, "input": {"shape": "UpdateApiCacheRequest"}, "output": {"shape": "UpdateApiCacheResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ConcurrentModificationException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}], "documentation": "<p>Updates the cache for the GraphQL API.</p>"}, "UpdateApiKey": {"name": "UpdateApi<PERSON>ey", "http": {"method": "POST", "requestUri": "/v1/apis/{apiId}/apikeys/{id}"}, "input": {"shape": "UpdateApiKeyRequest"}, "output": {"shape": "UpdateApiKeyResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "LimitExceededException"}, {"shape": "InternalFailureException"}, {"shape": "ApiKeyValidityOutOfBoundsException"}], "documentation": "<p>Updates an API key. You can update the key as long as it's not deleted.</p>"}, "UpdateChannelNamespace": {"name": "UpdateChannelNamespace", "http": {"method": "POST", "requestUri": "/v2/apis/{apiId}/channelNamespaces/{name}"}, "input": {"shape": "UpdateChannelNamespaceRequest"}, "output": {"shape": "UpdateChannelNamespaceResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ConcurrentModificationException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Updates a <code>ChannelNamespace</code> associated with an <code>Api</code>.</p>"}, "UpdateDataSource": {"name": "UpdateDataSource", "http": {"method": "POST", "requestUri": "/v1/apis/{apiId}/datasources/{name}"}, "input": {"shape": "UpdateDataSourceRequest"}, "output": {"shape": "UpdateDataSourceResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ConcurrentModificationException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}], "documentation": "<p>Updates a <code>DataSource</code> object.</p>"}, "UpdateDomainName": {"name": "UpdateDomainName", "http": {"method": "POST", "requestUri": "/v1/domainnames/{domainName}"}, "input": {"shape": "UpdateDomainNameRequest"}, "output": {"shape": "UpdateDomainNameResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "BadRequestException"}, {"shape": "ConcurrentModificationException"}, {"shape": "InternalFailureException"}, {"shape": "NotFoundException"}], "documentation": "<p>Updates a custom <code>DomainName</code> object.</p>"}, "UpdateFunction": {"name": "UpdateFunction", "http": {"method": "POST", "requestUri": "/v1/apis/{apiId}/functions/{functionId}"}, "input": {"shape": "UpdateFunctionRequest"}, "output": {"shape": "UpdateFunctionResponse"}, "errors": [{"shape": "ConcurrentModificationException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}, {"shape": "BadRequestException"}], "documentation": "<p>Updates a <code>Function</code> object.</p>"}, "UpdateGraphqlApi": {"name": "UpdateGraphqlApi", "http": {"method": "POST", "requestUri": "/v1/apis/{apiId}"}, "input": {"shape": "UpdateGraphqlApiRequest"}, "output": {"shape": "UpdateGraphqlApiResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ConcurrentModificationException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Updates a <code>GraphqlApi</code> object.</p>"}, "UpdateResolver": {"name": "UpdateResolver", "http": {"method": "POST", "requestUri": "/v1/apis/{apiId}/types/{typeName}/resolvers/{fieldName}"}, "input": {"shape": "UpdateResolverRequest"}, "output": {"shape": "UpdateResolverResponse"}, "errors": [{"shape": "ConcurrentModificationException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}, {"shape": "BadRequestException"}], "documentation": "<p>Updates a <code>Resolver</code> object.</p>"}, "UpdateSourceApiAssociation": {"name": "UpdateSourceApiAssociation", "http": {"method": "POST", "requestUri": "/v1/mergedApis/{mergedApiIdentifier}/sourceApiAssociations/{associationId}"}, "input": {"shape": "UpdateSourceApiAssociationRequest"}, "output": {"shape": "UpdateSourceApiAssociationResponse"}, "errors": [{"shape": "UnauthorizedException"}, {"shape": "BadRequestException"}, {"shape": "InternalFailureException"}, {"shape": "NotFoundException"}, {"shape": "ConcurrentModificationException"}], "documentation": "<p>Updates some of the configuration choices of a particular source API association.</p>"}, "UpdateType": {"name": "UpdateType", "http": {"method": "POST", "requestUri": "/v1/apis/{apiId}/types/{typeName}"}, "input": {"shape": "UpdateTypeRequest"}, "output": {"shape": "UpdateTypeResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ConcurrentModificationException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "InternalFailureException"}], "documentation": "<p>Updates a <code>Type</code> object.</p>"}}, "shapes": {"AccessDeniedException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>You don't have access to perform this operation on this resource.</p>", "error": {"httpStatusCode": 403}, "exception": true}, "AdditionalAuthenticationProvider": {"type": "structure", "members": {"authenticationType": {"shape": "AuthenticationType", "documentation": "<p>The authentication type: API key, Identity and Access Management (IAM), OpenID Connect (OIDC), Amazon Cognito user pools, or Lambda.</p>"}, "openIDConnectConfig": {"shape": "OpenIDConnectConfig", "documentation": "<p>The OIDC configuration.</p>"}, "userPoolConfig": {"shape": "CognitoUserPoolConfig", "documentation": "<p>The Amazon Cognito user pool configuration.</p>"}, "lambdaAuthorizerConfig": {"shape": "LambdaAuthorizerConfig", "documentation": "<p>Configuration for Lambda function authorization.</p>"}}, "documentation": "<p>Describes an additional authentication provider.</p>"}, "AdditionalAuthenticationProviders": {"type": "list", "member": {"shape": "AdditionalAuthenticationProvider"}}, "Api": {"type": "structure", "members": {"apiId": {"shape": "String", "documentation": "<p>The <code>Api</code> ID.</p>"}, "name": {"shape": "ApiName", "documentation": "<p>The name of the <code>Api</code>.</p>"}, "ownerContact": {"shape": "OwnerContact", "documentation": "<p>The owner contact information for the <code>Api</code> </p>"}, "tags": {"shape": "TagMap"}, "dns": {"shape": "MapOfStringToString", "documentation": "<p>The DNS records for the API. This will include an HTTP and a real-time endpoint.</p>"}, "apiArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) for the <code>Api</code>.</p>"}, "created": {"shape": "Timestamp", "documentation": "<p>The date and time that the <code>Api</code> was created.</p>"}, "xrayEnabled": {"shape": "Boolean", "documentation": "<p>A flag indicating whether to use X-Ray tracing for this <code>Api</code>.</p>"}, "wafWebAclArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the WAF web access control list (web ACL) associated with this <code>Api</code>, if one exists.</p>"}, "eventConfig": {"shape": "EventConfig", "documentation": "<p>The Event API configuration. This includes the default authorization configuration for connecting, publishing, and subscribing to an Event API.</p>"}}, "documentation": "<p>Describes an AppSync API. You can use <code>Api</code> for an AppSync API with your preferred configuration, such as an Event API that provides real-time message publishing and message subscriptions over WebSockets.</p>"}, "ApiAssociation": {"type": "structure", "members": {"domainName": {"shape": "DomainName", "documentation": "<p>The domain name.</p>"}, "apiId": {"shape": "String", "documentation": "<p>The API ID.</p>"}, "associationStatus": {"shape": "AssociationStatus", "documentation": "<p>Identifies the status of an association.</p> <ul> <li> <p> <b>PROCESSING</b>: The API association is being created. You cannot modify association requests during processing.</p> </li> <li> <p> <b>SUCCESS</b>: The API association was successful. You can modify associations after success.</p> </li> <li> <p> <b>FAILED</b>: The API association has failed. You can modify associations after failure.</p> </li> </ul>"}, "deploymentDetail": {"shape": "String", "documentation": "<p>Details about the last deployment status.</p>"}}, "documentation": "<p>Describes an <code>ApiAssociation</code> object.</p>"}, "ApiCache": {"type": "structure", "members": {"ttl": {"shape": "<PERSON>", "documentation": "<p>TTL in seconds for cache entries.</p> <p>Valid values are 1–3,600 seconds.</p>"}, "apiCachingBehavior": {"shape": "ApiCachingBehavior", "documentation": "<p>Caching behavior.</p> <ul> <li> <p> <b>FULL_REQUEST_CACHING</b>: All requests from the same user are cached. Individual resolvers are automatically cached. All API calls will try to return responses from the cache.</p> </li> <li> <p> <b>PER_RESOLVER_CACHING</b>: Individual resolvers that you specify are cached.</p> </li> <li> <p> <b>OPERATION_LEVEL_CACHING</b>: Full requests are cached together and returned without executing resolvers.</p> </li> </ul>"}, "transitEncryptionEnabled": {"shape": "Boolean", "documentation": "<p>Transit encryption flag when connecting to cache. You cannot update this setting after creation.</p>"}, "atRestEncryptionEnabled": {"shape": "Boolean", "documentation": "<p>At-rest encryption flag for cache. You cannot update this setting after creation.</p>"}, "type": {"shape": "ApiCacheType", "documentation": "<p>The cache instance type. Valid values are </p> <ul> <li> <p> <code>SMALL</code> </p> </li> <li> <p> <code>MEDIUM</code> </p> </li> <li> <p> <code>LARGE</code> </p> </li> <li> <p> <code>XLARGE</code> </p> </li> <li> <p> <code>LARGE_2X</code> </p> </li> <li> <p> <code>LARGE_4X</code> </p> </li> <li> <p> <code>LARGE_8X</code> (not available in all regions)</p> </li> <li> <p> <code>LARGE_12X</code> </p> </li> </ul> <p>Historically, instance types were identified by an EC2-style value. As of July 2020, this is deprecated, and the generic identifiers above should be used.</p> <p>The following legacy instance types are available, but their use is discouraged:</p> <ul> <li> <p> <b>T2_SMALL</b>: A t2.small instance type.</p> </li> <li> <p> <b>T2_MEDIUM</b>: A t2.medium instance type.</p> </li> <li> <p> <b>R4_LARGE</b>: A r4.large instance type.</p> </li> <li> <p> <b>R4_XLARGE</b>: A r4.xlarge instance type.</p> </li> <li> <p> <b>R4_2XLARGE</b>: A r4.2xlarge instance type.</p> </li> <li> <p> <b>R4_4XLARGE</b>: A r4.4xlarge instance type.</p> </li> <li> <p> <b>R4_8XLARGE</b>: A r4.8xlarge instance type.</p> </li> </ul>"}, "status": {"shape": "ApiCacheStatus", "documentation": "<p>The cache instance status.</p> <ul> <li> <p> <b>AVAILABLE</b>: The instance is available for use.</p> </li> <li> <p> <b>CREATING</b>: The instance is currently creating.</p> </li> <li> <p> <b>DELETING</b>: The instance is currently deleting.</p> </li> <li> <p> <b>MODIFYING</b>: The instance is currently modifying.</p> </li> <li> <p> <b>FAILED</b>: The instance has failed creation.</p> </li> </ul>"}, "healthMetricsConfig": {"shape": "CacheHealthMetricsConfig", "documentation": "<p>Controls how cache health metrics will be emitted to CloudWatch. Cache health metrics include:</p> <ul> <li> <p>NetworkBandwidthOutAllowanceExceeded: The network packets dropped because the throughput exceeded the aggregated bandwidth limit. This is useful for diagnosing bottlenecks in a cache configuration.</p> </li> <li> <p>EngineCPUUtilization: The CPU utilization (percentage) allocated to the Redis process. This is useful for diagnosing bottlenecks in a cache configuration.</p> </li> </ul> <p>Metrics will be recorded by API ID. You can set the value to <code>ENABLED</code> or <code>DISABLED</code>.</p>"}}, "documentation": "<p>The <code>ApiCache</code> object.</p>"}, "ApiCacheStatus": {"type": "string", "enum": ["AVAILABLE", "CREATING", "DELETING", "MODIFYING", "FAILED"]}, "ApiCacheType": {"type": "string", "enum": ["T2_SMALL", "T2_MEDIUM", "R4_LARGE", "R4_XLARGE", "R4_2XLARGE", "R4_4XLARGE", "R4_8XLARGE", "SMALL", "MEDIUM", "LARGE", "XLARGE", "LARGE_2X", "LARGE_4X", "LARGE_8X", "LARGE_12X"]}, "ApiCachingBehavior": {"type": "string", "enum": ["FULL_REQUEST_CACHING", "PER_RESOLVER_CACHING", "OPERATION_LEVEL_CACHING"]}, "ApiKey": {"type": "structure", "members": {"id": {"shape": "String", "documentation": "<p>The API key ID.</p>"}, "description": {"shape": "String", "documentation": "<p>A description of the purpose of the API key.</p>"}, "expires": {"shape": "<PERSON>", "documentation": "<p>The time after which the API key expires. The date is represented as seconds since the epoch, rounded down to the nearest hour.</p>"}, "deletes": {"shape": "<PERSON>", "documentation": "<p>The time after which the API key is deleted. The date is represented as seconds since the epoch, rounded down to the nearest hour.</p>"}}, "documentation": "<p>Describes an API key.</p> <p>Customers invoke AppSync GraphQL API operations with API keys as an identity mechanism. There are two key versions:</p> <p> <b>da1</b>: We introduced this version at launch in November 2017. These keys always expire after 7 days. Amazon DynamoDB TTL manages key expiration. These keys ceased to be valid after February 21, 2018, and they should no longer be used.</p> <ul> <li> <p> <code>ListApiKeys</code> returns the expiration time in milliseconds.</p> </li> <li> <p> <code>CreateApiKey</code> returns the expiration time in milliseconds.</p> </li> <li> <p> <code>UpdateApiKey</code> is not available for this key version.</p> </li> <li> <p> <code>DeleteApiKey</code> deletes the item from the table.</p> </li> <li> <p>Expiration is stored in DynamoDB as milliseconds. This results in a bug where keys are not automatically deleted because DynamoDB expects the TTL to be stored in seconds. As a one-time action, we deleted these keys from the table on February 21, 2018.</p> </li> </ul> <p> <b>da2</b>: We introduced this version in February 2018 when AppSync added support to extend key expiration.</p> <ul> <li> <p> <code>ListApiKeys</code> returns the expiration time and deletion time in seconds.</p> </li> <li> <p> <code>CreateApiKey</code> returns the expiration time and deletion time in seconds and accepts a user-provided expiration time in seconds.</p> </li> <li> <p> <code>UpdateApiKey</code> returns the expiration time and and deletion time in seconds and accepts a user-provided expiration time in seconds. Expired API keys are kept for 60 days after the expiration time. You can update the key expiration time as long as the key isn't deleted.</p> </li> <li> <p> <code>DeleteApiKey</code> deletes the item from the table.</p> </li> <li> <p>Expiration is stored in DynamoDB as seconds. After the expiration time, using the key to authenticate will fail. However, you can reinstate the key before deletion.</p> </li> <li> <p>Deletion is stored in DynamoDB as seconds. The key is deleted after deletion time.</p> </li> </ul>"}, "ApiKeyLimitExceededException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>The API key exceeded a limit. Try your request again.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "ApiKeyValidityOutOfBoundsException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>The API key expiration must be set to a value between 1 and 365 days from creation (for <code>CreateApiKey</code>) or from update (for <code>UpdateApiKey</code>).</p>", "error": {"httpStatusCode": 400}, "exception": true}, "ApiKeys": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON><PERSON>"}}, "ApiLimitExceededException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>The GraphQL API exceeded a limit. Try your request again.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "ApiName": {"type": "string", "max": 50, "min": 1, "pattern": "[A-Za-z0-9_\\-\\ ]+"}, "Apis": {"type": "list", "member": {"shape": "Api"}}, "AppSyncRuntime": {"type": "structure", "required": ["name", "runtimeVersion"], "members": {"name": {"shape": "RuntimeName", "documentation": "<p>The <code>name</code> of the runtime to use. Currently, the only allowed value is <code>APPSYNC_JS</code>.</p>"}, "runtimeVersion": {"shape": "String", "documentation": "<p>The <code>version</code> of the runtime to use. Currently, the only allowed version is <code>1.0.0</code>.</p>"}}, "documentation": "<p>Describes a runtime used by an Amazon Web Services AppSync pipeline resolver or Amazon Web Services AppSync function. Specifies the name and version of the runtime to use. Note that if a runtime is specified, code must also be specified.</p>"}, "AssociateApiRequest": {"type": "structure", "required": ["domainName", "apiId"], "members": {"domainName": {"shape": "DomainName", "documentation": "<p>The domain name.</p>", "location": "uri", "locationName": "domainName"}, "apiId": {"shape": "String", "documentation": "<p>The API ID. Private APIs can not be associated with custom domains.</p>"}}}, "AssociateApiResponse": {"type": "structure", "members": {"apiAssociation": {"shape": "ApiAssociation", "documentation": "<p>The <code>ApiAssociation</code> object.</p>"}}}, "AssociateMergedGraphqlApiRequest": {"type": "structure", "required": ["sourceApiIdentifier", "mergedApiIdentifier"], "members": {"sourceApiIdentifier": {"shape": "String", "documentation": "<p>The identifier of the AppSync Source API. This is generated by the AppSync service. In most cases, source APIs (especially in your account) only require the API ID value or ARN of the source API. However, source APIs from other accounts (cross-account use cases) strictly require the full resource ARN of the source API.</p>", "location": "uri", "locationName": "sourceApiIdentifier"}, "mergedApiIdentifier": {"shape": "String", "documentation": "<p>The identifier of the AppSync Merged API. This is generated by the AppSync service. In most cases, Merged APIs (especially in your account) only require the API ID value or ARN of the merged API. However, Merged APIs in other accounts (cross-account use cases) strictly require the full resource ARN of the merged API.</p>"}, "description": {"shape": "String", "documentation": "<p>The description field.</p>"}, "sourceApiAssociationConfig": {"shape": "SourceApiAssociationConfig", "documentation": "<p>The <code>SourceApiAssociationConfig</code> object data.</p>"}}}, "AssociateMergedGraphqlApiResponse": {"type": "structure", "members": {"sourceApiAssociation": {"shape": "SourceApiAssociation", "documentation": "<p>The <code>SourceApiAssociation</code> object data.</p>"}}}, "AssociateSourceGraphqlApiRequest": {"type": "structure", "required": ["mergedApiIdentifier", "sourceApiIdentifier"], "members": {"mergedApiIdentifier": {"shape": "String", "documentation": "<p>The identifier of the AppSync Merged API. This is generated by the AppSync service. In most cases, Merged APIs (especially in your account) only require the API ID value or ARN of the merged API. However, Merged APIs in other accounts (cross-account use cases) strictly require the full resource ARN of the merged API.</p>", "location": "uri", "locationName": "mergedApiIdentifier"}, "sourceApiIdentifier": {"shape": "String", "documentation": "<p>The identifier of the AppSync Source API. This is generated by the AppSync service. In most cases, source APIs (especially in your account) only require the API ID value or ARN of the source API. However, source APIs from other accounts (cross-account use cases) strictly require the full resource ARN of the source API.</p>"}, "description": {"shape": "String", "documentation": "<p>The description field.</p>"}, "sourceApiAssociationConfig": {"shape": "SourceApiAssociationConfig", "documentation": "<p>The <code>SourceApiAssociationConfig</code> object data.</p>"}}}, "AssociateSourceGraphqlApiResponse": {"type": "structure", "members": {"sourceApiAssociation": {"shape": "SourceApiAssociation", "documentation": "<p>The <code>SourceApiAssociation</code> object data.</p>"}}}, "AssociationStatus": {"type": "string", "enum": ["PROCESSING", "FAILED", "SUCCESS"]}, "AuthMode": {"type": "structure", "required": ["authType"], "members": {"authType": {"shape": "AuthenticationType", "documentation": "<p>The authorization type.</p>"}}, "documentation": "<p>Describes an authorization configuration. Use <code>AuthMode</code> to specify the publishing and subscription authorization configuration for an Event API.</p>"}, "AuthModes": {"type": "list", "member": {"shape": "AuthMode"}}, "AuthProvider": {"type": "structure", "required": ["authType"], "members": {"authType": {"shape": "AuthenticationType", "documentation": "<p>The authorization type.</p>"}, "cognitoConfig": {"shape": "CognitoConfig", "documentation": "<p>Describes an Amazon Cognito user pool configuration.</p>"}, "openIDConnectConfig": {"shape": "OpenIDConnectConfig"}, "lambdaAuthorizerConfig": {"shape": "LambdaAuthorizerConfig"}}, "documentation": "<p>Describes an authorization provider.</p>"}, "AuthProviders": {"type": "list", "member": {"shape": "<PERSON>th<PERSON><PERSON><PERSON>"}}, "AuthenticationType": {"type": "string", "enum": ["API_KEY", "AWS_IAM", "AMAZON_COGNITO_USER_POOLS", "OPENID_CONNECT", "AWS_LAMBDA"]}, "AuthorizationConfig": {"type": "structure", "required": ["authorizationType"], "members": {"authorizationType": {"shape": "AuthorizationType", "documentation": "<p>The authorization type that the HTTP endpoint requires.</p> <ul> <li> <p> <b>AWS_IAM</b>: The authorization type is Signature Version 4 (SigV4).</p> </li> </ul>"}, "awsIamConfig": {"shape": "AwsIamConfig", "documentation": "<p>The Identity and Access Management (IAM) settings.</p>"}}, "documentation": "<p>The authorization configuration in case the HTTP endpoint requires authorization.</p>"}, "AuthorizationType": {"type": "string", "enum": ["AWS_IAM"]}, "AwsIamConfig": {"type": "structure", "members": {"signingRegion": {"shape": "String", "documentation": "<p>The signing Amazon Web Services Region for IAM authorization.</p>"}, "signingServiceName": {"shape": "String", "documentation": "<p>The signing service name for IAM authorization.</p>"}}, "documentation": "<p>The Identity and Access Management (IAM) configuration.</p>"}, "BadRequestDetail": {"type": "structure", "members": {"codeErrors": {"shape": "CodeErrors", "documentation": "<p>Contains the list of errors in the request.</p>"}}, "documentation": "<p>Provides further details for the reason behind the bad request. For reason type <code>CODE_ERROR</code>, the detail will contain a list of code errors.</p>"}, "BadRequestException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}, "reason": {"shape": "BadRequestReason"}, "detail": {"shape": "BadRequestDetail"}}, "documentation": "<p>The request is not well formed. For example, a value is invalid or a required field is missing. Check the field values, and then try again.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "BadRequestReason": {"type": "string", "documentation": "<p>Provides context for the cause of the bad request. The only supported value is <code>CODE_ERROR</code>.</p>", "enum": ["CODE_ERROR"]}, "Blob": {"type": "blob"}, "Boolean": {"type": "boolean"}, "BooleanValue": {"type": "boolean"}, "CacheHealthMetricsConfig": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "CachingConfig": {"type": "structure", "required": ["ttl"], "members": {"ttl": {"shape": "<PERSON>", "documentation": "<p>The TTL in seconds for a resolver that has caching activated.</p> <p>Valid values are 1–3,600 seconds.</p>"}, "cachingKeys": {"shape": "CachingKeys", "documentation": "<p>The caching keys for a resolver that has caching activated.</p> <p>Valid values are entries from the <code>$context.arguments</code>, <code>$context.source</code>, and <code>$context.identity</code> maps.</p>"}}, "documentation": "<p>The caching configuration for a resolver that has caching activated.</p>"}, "CachingKeys": {"type": "list", "member": {"shape": "String"}}, "CertificateArn": {"type": "string", "max": 2048, "min": 20, "pattern": "^arn:[a-z-]*:(acm|iam):[a-z0-9-]*:\\d{12}:(certificate|server-certificate)/[0-9A-Za-z_/-]*$"}, "ChannelNamespace": {"type": "structure", "members": {"apiId": {"shape": "String", "documentation": "<p>The <code>Api</code> ID.</p>"}, "name": {"shape": "Namespace", "documentation": "<p>The name of the channel namespace. This name must be unique within the <code>Api</code>.</p>"}, "subscribeAuthModes": {"shape": "AuthModes", "documentation": "<p>The authorization mode to use for subscribing to messages on the channel namespace. This configuration overrides the default <code>Api</code>authorization configuration.</p>"}, "publishAuthModes": {"shape": "AuthModes", "documentation": "<p>The authorization mode to use for publishing messages on the channel namespace. This configuration overrides the default <code>Api</code>authorization configuration.</p>"}, "codeHandlers": {"shape": "Code", "documentation": "<p>The event handler functions that run custom business logic to process published events and subscribe requests.</p>"}, "tags": {"shape": "TagMap"}, "channelNamespaceArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) for the <code>ChannelNamespace</code>.</p>"}, "created": {"shape": "Timestamp", "documentation": "<p>The date and time that the <code>ChannelNamespace</code> was created.</p>"}, "lastModified": {"shape": "Timestamp", "documentation": "<p>The date and time that the <code>ChannelNamespace</code> was last changed.</p>"}, "handlerConfigs": {"shape": "HandlerConfigs", "documentation": "<p>The configuration for the <code>OnPublish</code> and <code>OnSubscribe</code> handlers.</p>"}}, "documentation": "<p>Describes a channel namespace associated with an <code>Api</code>. The <code>ChannelNamespace</code> contains the definitions for code handlers for the <code>Api</code>.</p>"}, "ChannelNamespaces": {"type": "list", "member": {"shape": "ChannelNamespace"}}, "Code": {"type": "string", "max": 32768, "min": 1}, "CodeError": {"type": "structure", "members": {"errorType": {"shape": "String", "documentation": "<p>The type of code error. </p> <p>Examples include, but aren't limited to: <code>LINT_ERROR</code>, <code>PARSER_ERROR</code>.</p>"}, "value": {"shape": "String", "documentation": "<p>A user presentable error.</p> <p>Examples include, but aren't limited to: <code>Parsing error: Unterminated string literal</code>.</p>"}, "location": {"shape": "CodeErrorLocation", "documentation": "<p>The line, column, and span location of the error in the code.</p>"}}, "documentation": "<p>Describes an AppSync error.</p>"}, "CodeErrorColumn": {"type": "integer"}, "CodeErrorLine": {"type": "integer"}, "CodeErrorLocation": {"type": "structure", "members": {"line": {"shape": "CodeErrorLine", "documentation": "<p>The line number in the code. Defaults to <code>0</code> if unknown.</p>"}, "column": {"shape": "CodeErrorColumn", "documentation": "<p>The column number in the code. Defaults to <code>0</code> if unknown.</p>"}, "span": {"shape": "CodeErrorSpan", "documentation": "<p>The span/length of the error. Defaults to <code>-1</code> if unknown.</p>"}}, "documentation": "<p>Describes the location of the error in a code sample.</p>"}, "CodeErrorSpan": {"type": "integer"}, "CodeErrors": {"type": "list", "member": {"shape": "CodeError"}}, "CognitoConfig": {"type": "structure", "required": ["userPoolId", "awsRegion"], "members": {"userPoolId": {"shape": "String", "documentation": "<p>The user pool ID.</p>"}, "awsRegion": {"shape": "String", "documentation": "<p>The Amazon Web Services Region in which the user pool was created.</p>"}, "appIdClientRegex": {"shape": "String", "documentation": "<p>A regular expression for validating the incoming Amazon Cognito user pool app client ID. If this value isn't set, no filtering is applied.</p>"}}, "documentation": "<p>Describes an Amazon Cognito configuration.</p>"}, "CognitoUserPoolConfig": {"type": "structure", "required": ["userPoolId", "awsRegion"], "members": {"userPoolId": {"shape": "String", "documentation": "<p>The user pool ID.</p>"}, "awsRegion": {"shape": "String", "documentation": "<p>The Amazon Web Services Region in which the user pool was created.</p>"}, "appIdClientRegex": {"shape": "String", "documentation": "<p>A regular expression for validating the incoming Amazon Cognito user pool app client ID. If this value isn't set, no filtering is applied.</p>"}}, "documentation": "<p>Describes an Amazon Cognito user pool configuration.</p>"}, "ConcurrentModificationException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>Another modification is in progress at this time and it must complete before you can make your change.</p>", "error": {"httpStatusCode": 409}, "exception": true}, "ConflictDetectionType": {"type": "string", "enum": ["VERSION", "NONE"]}, "ConflictException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>A conflict with a previous successful update is detected. This typically occurs when the previous update did not have time to propagate before the next update was made. A retry (with appropriate backoff logic) is the recommended response to this exception.</p>", "error": {"httpStatusCode": 409}, "exception": true}, "ConflictHandlerType": {"type": "string", "enum": ["OPTIMISTIC_CONCURRENCY", "LAMBDA", "AUTOMERGE", "NONE"]}, "Context": {"type": "string", "max": 28000, "min": 2, "pattern": "^[\\s\\S]*$"}, "CreateApiCacheRequest": {"type": "structure", "required": ["apiId", "ttl", "apiCachingBehavior", "type"], "members": {"apiId": {"shape": "String", "documentation": "<p>The GraphQL API ID.</p>", "location": "uri", "locationName": "apiId"}, "ttl": {"shape": "<PERSON>", "documentation": "<p>TTL in seconds for cache entries.</p> <p>Valid values are 1–3,600 seconds.</p>"}, "transitEncryptionEnabled": {"shape": "Boolean", "documentation": "<p>Transit encryption flag when connecting to cache. You cannot update this setting after creation.</p>", "deprecated": true, "deprecatedMessage": "transitEncryptionEnabled attribute is deprecated. Encryption in transit is always enabled.", "deprecatedSince": "5/15/2025"}, "atRestEncryptionEnabled": {"shape": "Boolean", "documentation": "<p>At-rest encryption flag for cache. You cannot update this setting after creation.</p>", "deprecated": true, "deprecatedMessage": "atRestEncryptionEnabled attribute is deprecated. Encryption at rest is always enabled.", "deprecatedSince": "5/15/2025"}, "apiCachingBehavior": {"shape": "ApiCachingBehavior", "documentation": "<p>Caching behavior.</p> <ul> <li> <p> <b>FULL_REQUEST_CACHING</b>: All requests from the same user are cached. Individual resolvers are automatically cached. All API calls will try to return responses from the cache.</p> </li> <li> <p> <b>PER_RESOLVER_CACHING</b>: Individual resolvers that you specify are cached.</p> </li> <li> <p> <b>OPERATION_LEVEL_CACHING</b>: Full requests are cached together and returned without executing resolvers.</p> </li> </ul>"}, "type": {"shape": "ApiCacheType", "documentation": "<p>The cache instance type. Valid values are </p> <ul> <li> <p> <code>SMALL</code> </p> </li> <li> <p> <code>MEDIUM</code> </p> </li> <li> <p> <code>LARGE</code> </p> </li> <li> <p> <code>XLARGE</code> </p> </li> <li> <p> <code>LARGE_2X</code> </p> </li> <li> <p> <code>LARGE_4X</code> </p> </li> <li> <p> <code>LARGE_8X</code> (not available in all regions)</p> </li> <li> <p> <code>LARGE_12X</code> </p> </li> </ul> <p>Historically, instance types were identified by an EC2-style value. As of July 2020, this is deprecated, and the generic identifiers above should be used.</p> <p>The following legacy instance types are available, but their use is discouraged:</p> <ul> <li> <p> <b>T2_SMALL</b>: A t2.small instance type.</p> </li> <li> <p> <b>T2_MEDIUM</b>: A t2.medium instance type.</p> </li> <li> <p> <b>R4_LARGE</b>: A r4.large instance type.</p> </li> <li> <p> <b>R4_XLARGE</b>: A r4.xlarge instance type.</p> </li> <li> <p> <b>R4_2XLARGE</b>: A r4.2xlarge instance type.</p> </li> <li> <p> <b>R4_4XLARGE</b>: A r4.4xlarge instance type.</p> </li> <li> <p> <b>R4_8XLARGE</b>: A r4.8xlarge instance type.</p> </li> </ul>"}, "healthMetricsConfig": {"shape": "CacheHealthMetricsConfig", "documentation": "<p>Controls how cache health metrics will be emitted to CloudWatch. Cache health metrics include:</p> <ul> <li> <p>NetworkBandwidthOutAllowanceExceeded: The network packets dropped because the throughput exceeded the aggregated bandwidth limit. This is useful for diagnosing bottlenecks in a cache configuration.</p> </li> <li> <p>EngineCPUUtilization: The CPU utilization (percentage) allocated to the Redis process. This is useful for diagnosing bottlenecks in a cache configuration.</p> </li> </ul> <p>Metrics will be recorded by API ID. You can set the value to <code>ENABLED</code> or <code>DISABLED</code>.</p>"}}, "documentation": "<p>Represents the input of a <code>CreateApiCache</code> operation.</p>"}, "CreateApiCacheResponse": {"type": "structure", "members": {"apiCache": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The <code>ApiCache</code> object.</p>"}}, "documentation": "<p>Represents the output of a <code>CreateApiCache</code> operation.</p>"}, "CreateApiKeyRequest": {"type": "structure", "required": ["apiId"], "members": {"apiId": {"shape": "String", "documentation": "<p>The ID for your GraphQL API.</p>", "location": "uri", "locationName": "apiId"}, "description": {"shape": "String", "documentation": "<p>A description of the purpose of the API key.</p>"}, "expires": {"shape": "<PERSON>", "documentation": "<p>From the creation time, the time after which the API key expires. The date is represented as seconds since the epoch, rounded down to the nearest hour. The default value for this parameter is 7 days from creation time. For more information, see .</p>"}}}, "CreateApiKeyResponse": {"type": "structure", "members": {"apiKey": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The API key.</p>"}}}, "CreateApiRequest": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "ApiName", "documentation": "<p>The name for the <code>Api</code>.</p>"}, "ownerContact": {"shape": "String", "documentation": "<p>The owner contact information for the <code>Api</code>.</p>"}, "tags": {"shape": "TagMap"}, "eventConfig": {"shape": "EventConfig", "documentation": "<p>The Event API configuration. This includes the default authorization configuration for connecting, publishing, and subscribing to an Event API.</p>"}}}, "CreateApiResponse": {"type": "structure", "members": {"api": {"shape": "Api", "documentation": "<p>The <code>Api</code> object.</p>"}}}, "CreateChannelNamespaceRequest": {"type": "structure", "required": ["apiId", "name"], "members": {"apiId": {"shape": "String", "documentation": "<p>The <code>Api</code> ID.</p>", "location": "uri", "locationName": "apiId"}, "name": {"shape": "Namespace", "documentation": "<p>The name of the <code>ChannelNamespace</code>. This name must be unique within the <code>Api</code> </p>"}, "subscribeAuthModes": {"shape": "AuthModes", "documentation": "<p>The authorization mode to use for subscribing to messages on the channel namespace. This configuration overrides the default <code>Api</code> authorization configuration.</p>"}, "publishAuthModes": {"shape": "AuthModes", "documentation": "<p>The authorization mode to use for publishing messages on the channel namespace. This configuration overrides the default <code>Api</code> authorization configuration.</p>"}, "codeHandlers": {"shape": "Code", "documentation": "<p>The event handler functions that run custom business logic to process published events and subscribe requests.</p>"}, "tags": {"shape": "TagMap"}, "handlerConfigs": {"shape": "HandlerConfigs", "documentation": "<p>The configuration for the <code>OnPublish</code> and <code>OnSubscribe</code> handlers.</p>"}}}, "CreateChannelNamespaceResponse": {"type": "structure", "members": {"channelNamespace": {"shape": "ChannelNamespace", "documentation": "<p>The <code>ChannelNamespace</code> object.</p>"}}}, "CreateDataSourceRequest": {"type": "structure", "required": ["apiId", "name", "type"], "members": {"apiId": {"shape": "String", "documentation": "<p>The API ID for the GraphQL API for the <code>DataSource</code>.</p>", "location": "uri", "locationName": "apiId"}, "name": {"shape": "ResourceName", "documentation": "<p>A user-supplied name for the <code>DataSource</code>.</p>"}, "description": {"shape": "String", "documentation": "<p>A description of the <code>DataSource</code>.</p>"}, "type": {"shape": "DataSourceType", "documentation": "<p>The type of the <code>DataSource</code>.</p>"}, "serviceRoleArn": {"shape": "String", "documentation": "<p>The Identity and Access Management (IAM) service role Amazon Resource Name (ARN) for the data source. The system assumes this role when accessing the data source.</p>"}, "dynamodbConfig": {"shape": "DynamodbDataSourceConfig", "documentation": "<p>Amazon DynamoDB settings.</p>"}, "lambdaConfig": {"shape": "LambdaDataSourceConfig", "documentation": "<p>Lambda settings.</p>"}, "elasticsearchConfig": {"shape": "ElasticsearchDataSourceConfig", "documentation": "<p>Amazon OpenSearch Service settings.</p> <p>As of September 2021, Amazon Elasticsearch service is Amazon OpenSearch Service. This configuration is deprecated. For new data sources, use <a>CreateDataSourceRequest$openSearchServiceConfig</a> to create an OpenSearch data source.</p>"}, "openSearchServiceConfig": {"shape": "OpenSearchServiceDataSourceConfig", "documentation": "<p>Amazon OpenSearch Service settings.</p>"}, "httpConfig": {"shape": "HttpDataSourceConfig", "documentation": "<p>HTTP endpoint settings.</p>"}, "relationalDatabaseConfig": {"shape": "RelationalDatabaseDataSourceConfig", "documentation": "<p>Relational database settings.</p>"}, "eventBridgeConfig": {"shape": "EventBridgeDataSourceConfig", "documentation": "<p>Amazon EventBridge settings.</p>"}, "metricsConfig": {"shape": "DataSourceLevelMetricsConfig", "documentation": "<p>Enables or disables enhanced data source metrics for specified data sources. Note that <code>metricsConfig</code> won't be used unless the <code>dataSourceLevelMetricsBehavior</code> value is set to <code>PER_DATA_SOURCE_METRICS</code>. If the <code>dataSourceLevelMetricsBehavior</code> is set to <code>FULL_REQUEST_DATA_SOURCE_METRICS</code> instead, <code>metricsConfig</code> will be ignored. However, you can still set its value.</p> <p> <code>metricsConfig</code> can be <code>ENABLED</code> or <code>DISABLED</code>.</p>"}}}, "CreateDataSourceResponse": {"type": "structure", "members": {"dataSource": {"shape": "DataSource", "documentation": "<p>The <code>DataSource</code> object.</p>"}}}, "CreateDomainNameRequest": {"type": "structure", "required": ["domainName", "certificateArn"], "members": {"domainName": {"shape": "DomainName", "documentation": "<p>The domain name.</p>"}, "certificateArn": {"shape": "CertificateArn", "documentation": "<p>The Amazon Resource Name (ARN) of the certificate. This can be an Certificate Manager (ACM) certificate or an Identity and Access Management (IAM) server certificate.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description of the <code>DomainName</code>.</p>"}, "tags": {"shape": "TagMap"}}}, "CreateDomainNameResponse": {"type": "structure", "members": {"domainNameConfig": {"shape": "DomainNameConfig", "documentation": "<p>The configuration for the <code>DomainName</code>.</p>"}}}, "CreateFunctionRequest": {"type": "structure", "required": ["apiId", "name", "dataSourceName"], "members": {"apiId": {"shape": "String", "documentation": "<p>The GraphQL API ID.</p>", "location": "uri", "locationName": "apiId"}, "name": {"shape": "ResourceName", "documentation": "<p>The <code>Function</code> name. The function name does not have to be unique.</p>"}, "description": {"shape": "String", "documentation": "<p>The <code>Function</code> description.</p>"}, "dataSourceName": {"shape": "ResourceName", "documentation": "<p>The <code>Function</code> <code>DataSource</code> name.</p>"}, "requestMappingTemplate": {"shape": "MappingTemplate", "documentation": "<p>The <code>Function</code> request mapping template. Functions support only the 2018-05-29 version of the request mapping template.</p>"}, "responseMappingTemplate": {"shape": "MappingTemplate", "documentation": "<p>The <code>Function</code> response mapping template.</p>"}, "functionVersion": {"shape": "String", "documentation": "<p>The <code>version</code> of the request mapping template. Currently, the supported value is 2018-05-29. Note that when using VTL and mapping templates, the <code>functionVersion</code> is required.</p>"}, "syncConfig": {"shape": "SyncConfig"}, "maxBatchSize": {"shape": "MaxBatchSize", "documentation": "<p>The maximum batching size for a resolver.</p>"}, "runtime": {"shape": "AppSyncRuntime"}, "code": {"shape": "Code", "documentation": "<p>The <code>function</code> code that contains the request and response functions. When code is used, the <code>runtime</code> is required. The <code>runtime</code> value must be <code>APPSYNC_JS</code>.</p>"}}}, "CreateFunctionResponse": {"type": "structure", "members": {"functionConfiguration": {"shape": "FunctionConfiguration", "documentation": "<p>The <code>Function</code> object.</p>"}}}, "CreateGraphqlApiRequest": {"type": "structure", "required": ["name", "authenticationType"], "members": {"name": {"shape": "String", "documentation": "<p>A user-supplied name for the <code>GraphqlApi</code>.</p>"}, "logConfig": {"shape": "LogConfig", "documentation": "<p>The Amazon CloudWatch Logs configuration.</p>"}, "authenticationType": {"shape": "AuthenticationType", "documentation": "<p>The authentication type: API key, Identity and Access Management (IAM), OpenID Connect (OIDC), Amazon Cognito user pools, or Lambda.</p>"}, "userPoolConfig": {"shape": "UserPoolConfig", "documentation": "<p>The Amazon Cognito user pool configuration.</p>"}, "openIDConnectConfig": {"shape": "OpenIDConnectConfig", "documentation": "<p>The OIDC configuration.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>A <code>TagMap</code> object.</p>"}, "additionalAuthenticationProviders": {"shape": "AdditionalAuthenticationProviders", "documentation": "<p>A list of additional authentication providers for the <code>GraphqlApi</code> API.</p>"}, "xrayEnabled": {"shape": "Boolean", "documentation": "<p>A flag indicating whether to use X-Ray tracing for the <code>GraphqlApi</code>.</p>"}, "lambdaAuthorizerConfig": {"shape": "LambdaAuthorizerConfig", "documentation": "<p>Configuration for Lambda function authorization.</p>"}, "apiType": {"shape": "GraphQLApiType", "documentation": "<p>The value that indicates whether the GraphQL API is a standard API (<code>GRAPHQL</code>) or merged API (<code>MERGED</code>).</p>"}, "mergedApiExecutionRoleArn": {"shape": "String", "documentation": "<p>The Identity and Access Management service role ARN for a merged API. The AppSync service assumes this role on behalf of the Merged API to validate access to source APIs at runtime and to prompt the <code>AUTO_MERGE</code> to update the merged API endpoint with the source API changes automatically.</p>"}, "visibility": {"shape": "GraphQLApiVisibility", "documentation": "<p>Sets the value of the GraphQL API to public (<code>GLOBAL</code>) or private (<code>PRIVATE</code>). If no value is provided, the visibility will be set to <code>GLOBAL</code> by default. This value cannot be changed once the API has been created.</p>"}, "ownerContact": {"shape": "String", "documentation": "<p>The owner contact information for an API resource.</p> <p>This field accepts any string input with a length of 0 - 256 characters.</p>"}, "introspectionConfig": {"shape": "GraphQLApiIntrospectionConfig", "documentation": "<p>Sets the value of the GraphQL API to enable (<code>ENABLED</code>) or disable (<code>DISABLED</code>) introspection. If no value is provided, the introspection configuration will be set to <code>ENABLED</code> by default. This field will produce an error if the operation attempts to use the introspection feature while this field is disabled.</p> <p>For more information about introspection, see <a href=\"https://graphql.org/learn/introspection/\">GraphQL introspection</a>.</p>"}, "queryDepthLimit": {"shape": "QueryDepthLimit", "documentation": "<p>The maximum depth a query can have in a single request. Depth refers to the amount of nested levels allowed in the body of query. The default value is <code>0</code> (or unspecified), which indicates there's no depth limit. If you set a limit, it can be between <code>1</code> and <code>75</code> nested levels. This field will produce a limit error if the operation falls out of bounds.</p> <p>Note that fields can still be set to nullable or non-nullable. If a non-nullable field produces an error, the error will be thrown upwards to the first nullable field available.</p>"}, "resolverCountLimit": {"shape": "ResolverCountLimit", "documentation": "<p>The maximum number of resolvers that can be invoked in a single request. The default value is <code>0</code> (or unspecified), which will set the limit to <code>10000</code>. When specified, the limit value can be between <code>1</code> and <code>10000</code>. This field will produce a limit error if the operation falls out of bounds.</p>"}, "enhancedMetricsConfig": {"shape": "EnhancedMetricsConfig", "documentation": "<p>The <code>enhancedMetricsConfig</code> object.</p>"}}}, "CreateGraphqlApiResponse": {"type": "structure", "members": {"graphqlApi": {"shape": "GraphqlApi", "documentation": "<p>The <code>GraphqlApi</code>.</p>"}}}, "CreateResolverRequest": {"type": "structure", "required": ["apiId", "typeName", "fieldName"], "members": {"apiId": {"shape": "String", "documentation": "<p>The ID for the GraphQL API for which the resolver is being created.</p>", "location": "uri", "locationName": "apiId"}, "typeName": {"shape": "ResourceName", "documentation": "<p>The name of the <code>Type</code>.</p>", "location": "uri", "locationName": "typeName"}, "fieldName": {"shape": "ResourceName", "documentation": "<p>The name of the field to attach the resolver to.</p>"}, "dataSourceName": {"shape": "ResourceName", "documentation": "<p>The name of the data source for which the resolver is being created.</p>"}, "requestMappingTemplate": {"shape": "MappingTemplate", "documentation": "<p>The mapping template to use for requests.</p> <p>A resolver uses a request mapping template to convert a GraphQL expression into a format that a data source can understand. Mapping templates are written in Apache Velocity Template Language (VTL).</p> <p>VTL request mapping templates are optional when using an Lambda data source. For all other data sources, VTL request and response mapping templates are required.</p>"}, "responseMappingTemplate": {"shape": "MappingTemplate", "documentation": "<p>The mapping template to use for responses from the data source.</p>"}, "kind": {"shape": "ResolverKind", "documentation": "<p>The resolver type.</p> <ul> <li> <p> <b>UNIT</b>: A UNIT resolver type. A UNIT resolver is the default resolver type. You can use a UNIT resolver to run a GraphQL query against a single data source.</p> </li> <li> <p> <b>PIPELINE</b>: A PIPELINE resolver type. You can use a PIPELINE resolver to invoke a series of <code>Function</code> objects in a serial manner. You can use a pipeline resolver to run a GraphQL query against multiple data sources.</p> </li> </ul>"}, "pipelineConfig": {"shape": "PipelineConfig", "documentation": "<p>The <code>PipelineConfig</code>.</p>"}, "syncConfig": {"shape": "SyncConfig", "documentation": "<p>The <code>SyncConfig</code> for a resolver attached to a versioned data source.</p>"}, "cachingConfig": {"shape": "CachingConfig", "documentation": "<p>The caching configuration for the resolver.</p>"}, "maxBatchSize": {"shape": "MaxBatchSize", "documentation": "<p>The maximum batching size for a resolver.</p>"}, "runtime": {"shape": "AppSyncRuntime"}, "code": {"shape": "Code", "documentation": "<p>The <code>resolver</code> code that contains the request and response functions. When code is used, the <code>runtime</code> is required. The <code>runtime</code> value must be <code>APPSYNC_JS</code>.</p>"}, "metricsConfig": {"shape": "ResolverLevelMetricsConfig", "documentation": "<p>Enables or disables enhanced resolver metrics for specified resolvers. Note that <code>metricsConfig</code> won't be used unless the <code>resolverLevelMetricsBehavior</code> value is set to <code>PER_RESOLVER_METRICS</code>. If the <code>resolverLevelMetricsBehavior</code> is set to <code>FULL_REQUEST_RESOLVER_METRICS</code> instead, <code>metricsConfig</code> will be ignored. However, you can still set its value.</p> <p> <code>metricsConfig</code> can be <code>ENABLED</code> or <code>DISABLED</code>.</p>"}}}, "CreateResolverResponse": {"type": "structure", "members": {"resolver": {"shape": "Resolver", "documentation": "<p>The <code>Resolver</code> object.</p>"}}}, "CreateTypeRequest": {"type": "structure", "required": ["apiId", "definition", "format"], "members": {"apiId": {"shape": "String", "documentation": "<p>The API ID.</p>", "location": "uri", "locationName": "apiId"}, "definition": {"shape": "String", "documentation": "<p>The type definition, in GraphQL Schema Definition Language (SDL) format.</p> <p>For more information, see the <a href=\"http://graphql.org/learn/schema/\">GraphQL SDL documentation</a>.</p>"}, "format": {"shape": "TypeDefinitionFormat", "documentation": "<p>The type format: SDL or JSON.</p>"}}}, "CreateTypeResponse": {"type": "structure", "members": {"type": {"shape": "Type", "documentation": "<p>The <code>Type</code> object.</p>"}}}, "DataSource": {"type": "structure", "members": {"dataSourceArn": {"shape": "String", "documentation": "<p>The data source Amazon Resource Name (ARN).</p>"}, "name": {"shape": "ResourceName", "documentation": "<p>The name of the data source.</p>"}, "description": {"shape": "String", "documentation": "<p>The description of the data source.</p>"}, "type": {"shape": "DataSourceType", "documentation": "<p>The type of the data source.</p> <ul> <li> <p> <b>AWS_LAMBDA</b>: The data source is an Lambda function.</p> </li> <li> <p> <b>AMAZON_DYNAMODB</b>: The data source is an Amazon DynamoDB table.</p> </li> <li> <p> <b>AMAZON_ELASTICSEARCH</b>: The data source is an Amazon OpenSearch Service domain.</p> </li> <li> <p> <b>AMAZON_OPENSEARCH_SERVICE</b>: The data source is an Amazon OpenSearch Service domain.</p> </li> <li> <p> <b>AMAZON_EVENTBRIDGE</b>: The data source is an Amazon EventBridge configuration.</p> </li> <li> <p> <b>AMAZON_BEDROCK_RUNTIME</b>: The data source is the Amazon Bedrock runtime.</p> </li> <li> <p> <b>NONE</b>: There is no data source. Use this type when you want to invoke a GraphQL operation without connecting to a data source, such as when you're performing data transformation with resolvers or invoking a subscription from a mutation.</p> </li> <li> <p> <b>HTTP</b>: The data source is an HTTP endpoint.</p> </li> <li> <p> <b>RELATIONAL_DATABASE</b>: The data source is a relational database.</p> </li> </ul>"}, "serviceRoleArn": {"shape": "String", "documentation": "<p>The Identity and Access Management (IAM) service role Amazon Resource Name (ARN) for the data source. The system assumes this role when accessing the data source.</p>"}, "dynamodbConfig": {"shape": "DynamodbDataSourceConfig", "documentation": "<p>DynamoDB settings.</p>"}, "lambdaConfig": {"shape": "LambdaDataSourceConfig", "documentation": "<p>Lambda settings.</p>"}, "elasticsearchConfig": {"shape": "ElasticsearchDataSourceConfig", "documentation": "<p>Amazon OpenSearch Service settings.</p>"}, "openSearchServiceConfig": {"shape": "OpenSearchServiceDataSourceConfig", "documentation": "<p>Amazon OpenSearch Service settings.</p>"}, "httpConfig": {"shape": "HttpDataSourceConfig", "documentation": "<p>HTTP endpoint settings.</p>"}, "relationalDatabaseConfig": {"shape": "RelationalDatabaseDataSourceConfig", "documentation": "<p>Relational database settings.</p>"}, "eventBridgeConfig": {"shape": "EventBridgeDataSourceConfig", "documentation": "<p>Amazon EventBridge settings.</p>"}, "metricsConfig": {"shape": "DataSourceLevelMetricsConfig", "documentation": "<p>Enables or disables enhanced data source metrics for specified data sources. Note that <code>metricsConfig</code> won't be used unless the <code>dataSourceLevelMetricsBehavior</code> value is set to <code>PER_DATA_SOURCE_METRICS</code>. If the <code>dataSourceLevelMetricsBehavior</code> is set to <code>FULL_REQUEST_DATA_SOURCE_METRICS</code> instead, <code>metricsConfig</code> will be ignored. However, you can still set its value.</p> <p> <code>metricsConfig</code> can be <code>ENABLED</code> or <code>DISABLED</code>.</p>"}}, "documentation": "<p>Describes a data source.</p>"}, "DataSourceIntrospectionModel": {"type": "structure", "members": {"name": {"shape": "String", "documentation": "<p>The name of the model. For example, this could be the name of a single table in a database.</p>"}, "fields": {"shape": "DataSourceIntrospectionModelFields", "documentation": "<p>The <code>DataSourceIntrospectionModelField</code> object data.</p>"}, "primaryKey": {"shape": "DataSourceIntrospectionModelIndex", "documentation": "<p>The primary key stored as a <code>DataSourceIntrospectionModelIndex</code> object.</p>"}, "indexes": {"shape": "DataSourceIntrospectionModelIndexes", "documentation": "<p>The array of <code>DataSourceIntrospectionModelIndex</code> objects.</p>"}, "sdl": {"shape": "String", "documentation": "<p>Contains the output of the SDL that was generated from the introspected types. This is controlled by the <code>includeModelsSDL</code> parameter of the <code>GetDataSourceIntrospection</code> operation.</p>"}}, "documentation": "<p>Contains the introspected data that was retrieved from the data source.</p>"}, "DataSourceIntrospectionModelField": {"type": "structure", "members": {"name": {"shape": "String", "documentation": "<p>The name of the field that was retrieved from the introspected data.</p>"}, "type": {"shape": "DataSourceIntrospectionModelFieldType", "documentation": "<p>The <code>DataSourceIntrospectionModelFieldType</code> object data.</p>"}, "length": {"shape": "<PERSON>", "documentation": "<p>The length value of the introspected field.</p>"}}, "documentation": "<p>Represents the fields that were retrieved from the introspected data.</p>"}, "DataSourceIntrospectionModelFieldType": {"type": "structure", "members": {"kind": {"shape": "String", "documentation": "<p>Specifies the classification of data. For example, this could be set to values like <code>Scalar</code> or <code>NonNull</code> to indicate a fundamental property of the field.</p> <p>Valid values include:</p> <ul> <li> <p> <code>Scalar</code>: Indicates the value is a primitive type (scalar).</p> </li> <li> <p> <code>NonNull</code>: Indicates the field cannot be <code>null</code>.</p> </li> <li> <p> <code>List</code>: Indicates the field contains a list.</p> </li> </ul>"}, "name": {"shape": "String", "documentation": "<p>The name of the data type that represents the field. For example, <code>String</code> is a valid <code>name</code> value.</p>"}, "type": {"shape": "DataSourceIntrospectionModelFieldType", "documentation": "<p>The <code>DataSourceIntrospectionModelFieldType</code> object data. The <code>type</code> is only present if <code>DataSourceIntrospectionModelFieldType.kind</code> is set to <code>NonNull</code> or <code>List</code>. </p> <p>The <code>type</code> typically contains its own <code>kind</code> and <code>name</code> fields to represent the actual type data. For instance, <code>type</code> could contain a <code>kind</code> value of <code>Scalar</code> with a <code>name</code> value of <code>String</code>. The values <code>Scalar</code> and <code>String</code> will be collectively stored in the <code>values</code> field.</p>"}, "values": {"shape": "DataSourceIntrospectionModelFieldTypeValues", "documentation": "<p>The values of the <code>type</code> field. This field represents the AppSync data type equivalent of the introspected field.</p>"}}, "documentation": "<p>Represents the type data for each field retrieved from the introspection.</p>"}, "DataSourceIntrospectionModelFieldTypeValues": {"type": "list", "member": {"shape": "String"}}, "DataSourceIntrospectionModelFields": {"type": "list", "member": {"shape": "DataSourceIntrospectionModelField"}}, "DataSourceIntrospectionModelIndex": {"type": "structure", "members": {"name": {"shape": "String", "documentation": "<p>The name of the index.</p>"}, "fields": {"shape": "DataSourceIntrospectionModelIndexFields", "documentation": "<p>The fields of the index.</p>"}}, "documentation": "<p>The index that was retrieved from the introspected data.</p>"}, "DataSourceIntrospectionModelIndexFields": {"type": "list", "member": {"shape": "String"}}, "DataSourceIntrospectionModelIndexes": {"type": "list", "member": {"shape": "DataSourceIntrospectionModelIndex"}}, "DataSourceIntrospectionModels": {"type": "list", "member": {"shape": "DataSourceIntrospectionModel"}}, "DataSourceIntrospectionResult": {"type": "structure", "members": {"models": {"shape": "DataSourceIntrospectionModels", "documentation": "<p>The array of <code>DataSourceIntrospectionModel</code> objects.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>Determines the number of types to be returned in a single response before paginating. This value is typically taken from <code>nextToken</code> value from the previous response.</p>"}}, "documentation": "<p>Represents the output of a <code>DataSourceIntrospectionResult</code>. This is the populated result of a <code>GetDataSourceIntrospection</code> operation.</p>"}, "DataSourceIntrospectionStatus": {"type": "string", "enum": ["PROCESSING", "FAILED", "SUCCESS"]}, "DataSourceLevelMetricsBehavior": {"type": "string", "enum": ["FULL_REQUEST_DATA_SOURCE_METRICS", "PER_DATA_SOURCE_METRICS"]}, "DataSourceLevelMetricsConfig": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "DataSourceType": {"type": "string", "enum": ["AWS_LAMBDA", "AMAZON_DYNAMODB", "AMAZON_ELASTICSEARCH", "NONE", "HTTP", "RELATIONAL_DATABASE", "AMAZON_OPENSEARCH_SERVICE", "AMAZON_EVENTBRIDGE", "AMAZON_BEDROCK_RUNTIME"]}, "DataSources": {"type": "list", "member": {"shape": "DataSource"}}, "Date": {"type": "timestamp"}, "DefaultAction": {"type": "string", "enum": ["ALLOW", "DENY"]}, "DeleteApiCacheRequest": {"type": "structure", "required": ["apiId"], "members": {"apiId": {"shape": "String", "documentation": "<p>The API ID.</p>", "location": "uri", "locationName": "apiId"}}, "documentation": "<p>Represents the input of a <code>DeleteApiCache</code> operation.</p>"}, "DeleteApiCacheResponse": {"type": "structure", "members": {}, "documentation": "<p>Represents the output of a <code>DeleteApiCache</code> operation.</p>"}, "DeleteApiKeyRequest": {"type": "structure", "required": ["apiId", "id"], "members": {"apiId": {"shape": "String", "documentation": "<p>The API ID.</p>", "location": "uri", "locationName": "apiId"}, "id": {"shape": "String", "documentation": "<p>The ID for the API key.</p>", "location": "uri", "locationName": "id"}}}, "DeleteApiKeyResponse": {"type": "structure", "members": {}}, "DeleteApiRequest": {"type": "structure", "required": ["apiId"], "members": {"apiId": {"shape": "String", "documentation": "<p>The <code>Api</code> ID.</p>", "location": "uri", "locationName": "apiId"}}}, "DeleteApiResponse": {"type": "structure", "members": {}}, "DeleteChannelNamespaceRequest": {"type": "structure", "required": ["apiId", "name"], "members": {"apiId": {"shape": "String", "documentation": "<p>The ID of the <code>Api</code> associated with the <code>ChannelNamespace</code>.</p>", "location": "uri", "locationName": "apiId"}, "name": {"shape": "Namespace", "documentation": "<p>The name of the <code>ChannelNamespace</code>.</p>", "location": "uri", "locationName": "name"}}}, "DeleteChannelNamespaceResponse": {"type": "structure", "members": {}}, "DeleteDataSourceRequest": {"type": "structure", "required": ["apiId", "name"], "members": {"apiId": {"shape": "String", "documentation": "<p>The API ID.</p>", "location": "uri", "locationName": "apiId"}, "name": {"shape": "ResourceName", "documentation": "<p>The name of the data source.</p>", "location": "uri", "locationName": "name"}}}, "DeleteDataSourceResponse": {"type": "structure", "members": {}}, "DeleteDomainNameRequest": {"type": "structure", "required": ["domainName"], "members": {"domainName": {"shape": "DomainName", "documentation": "<p>The domain name.</p>", "location": "uri", "locationName": "domainName"}}}, "DeleteDomainNameResponse": {"type": "structure", "members": {}}, "DeleteFunctionRequest": {"type": "structure", "required": ["apiId", "functionId"], "members": {"apiId": {"shape": "String", "documentation": "<p>The GraphQL API ID.</p>", "location": "uri", "locationName": "apiId"}, "functionId": {"shape": "ResourceName", "documentation": "<p>The <code>Function</code> ID.</p>", "location": "uri", "locationName": "functionId"}}}, "DeleteFunctionResponse": {"type": "structure", "members": {}}, "DeleteGraphqlApiRequest": {"type": "structure", "required": ["apiId"], "members": {"apiId": {"shape": "String", "documentation": "<p>The API ID.</p>", "location": "uri", "locationName": "apiId"}}}, "DeleteGraphqlApiResponse": {"type": "structure", "members": {}}, "DeleteResolverRequest": {"type": "structure", "required": ["apiId", "typeName", "fieldName"], "members": {"apiId": {"shape": "String", "documentation": "<p>The API ID.</p>", "location": "uri", "locationName": "apiId"}, "typeName": {"shape": "ResourceName", "documentation": "<p>The name of the resolver type.</p>", "location": "uri", "locationName": "typeName"}, "fieldName": {"shape": "ResourceName", "documentation": "<p>The resolver field name.</p>", "location": "uri", "locationName": "fieldName"}}}, "DeleteResolverResponse": {"type": "structure", "members": {}}, "DeleteTypeRequest": {"type": "structure", "required": ["apiId", "typeName"], "members": {"apiId": {"shape": "String", "documentation": "<p>The API ID.</p>", "location": "uri", "locationName": "apiId"}, "typeName": {"shape": "ResourceName", "documentation": "<p>The type name.</p>", "location": "uri", "locationName": "typeName"}}}, "DeleteTypeResponse": {"type": "structure", "members": {}}, "DeltaSyncConfig": {"type": "structure", "members": {"baseTableTTL": {"shape": "<PERSON>", "documentation": "<p>The number of minutes that an Item is stored in the data source.</p>"}, "deltaSyncTableName": {"shape": "String", "documentation": "<p>The Delta Sync table name.</p>"}, "deltaSyncTableTTL": {"shape": "<PERSON>", "documentation": "<p>The number of minutes that a Delta Sync log entry is stored in the Delta Sync table.</p>"}}, "documentation": "<p>Describes a Delta Sync configuration.</p>"}, "Description": {"type": "string", "max": 255, "min": 0, "pattern": "^.*$"}, "DisassociateApiRequest": {"type": "structure", "required": ["domainName"], "members": {"domainName": {"shape": "DomainName", "documentation": "<p>The domain name.</p>", "location": "uri", "locationName": "domainName"}}}, "DisassociateApiResponse": {"type": "structure", "members": {}}, "DisassociateMergedGraphqlApiRequest": {"type": "structure", "required": ["sourceApiIdentifier", "associationId"], "members": {"sourceApiIdentifier": {"shape": "String", "documentation": "<p>The identifier of the AppSync Source API. This is generated by the AppSync service. In most cases, source APIs (especially in your account) only require the API ID value or ARN of the source API. However, source APIs from other accounts (cross-account use cases) strictly require the full resource ARN of the source API.</p>", "location": "uri", "locationName": "sourceApiIdentifier"}, "associationId": {"shape": "String", "documentation": "<p>The ID generated by the AppSync service for the source API association.</p>", "location": "uri", "locationName": "associationId"}}}, "DisassociateMergedGraphqlApiResponse": {"type": "structure", "members": {"sourceApiAssociationStatus": {"shape": "SourceApiAssociationStatus", "documentation": "<p>The state of the source API association.</p>"}}}, "DisassociateSourceGraphqlApiRequest": {"type": "structure", "required": ["mergedApiIdentifier", "associationId"], "members": {"mergedApiIdentifier": {"shape": "String", "documentation": "<p>The identifier of the AppSync Merged API. This is generated by the AppSync service. In most cases, Merged APIs (especially in your account) only require the API ID value or ARN of the merged API. However, Merged APIs in other accounts (cross-account use cases) strictly require the full resource ARN of the merged API.</p>", "location": "uri", "locationName": "mergedApiIdentifier"}, "associationId": {"shape": "String", "documentation": "<p>The ID generated by the AppSync service for the source API association.</p>", "location": "uri", "locationName": "associationId"}}}, "DisassociateSourceGraphqlApiResponse": {"type": "structure", "members": {"sourceApiAssociationStatus": {"shape": "SourceApiAssociationStatus", "documentation": "<p>The state of the source API association.</p>"}}}, "DomainName": {"type": "string", "max": 253, "min": 1, "pattern": "^(\\*[\\w\\d-]*\\.)?([\\w\\d-]+\\.)+[\\w\\d-]+$"}, "DomainNameConfig": {"type": "structure", "members": {"domainName": {"shape": "DomainName", "documentation": "<p>The domain name.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description of the <code>DomainName</code> configuration.</p>"}, "certificateArn": {"shape": "CertificateArn", "documentation": "<p>The Amazon Resource Name (ARN) of the certificate. This can be an Certificate Manager (ACM) certificate or an Identity and Access Management (IAM) server certificate.</p>"}, "appsyncDomainName": {"shape": "String", "documentation": "<p>The domain name that AppSync provides.</p>"}, "hostedZoneId": {"shape": "String", "documentation": "<p>The ID of your Amazon Route 53 hosted zone.</p>"}, "tags": {"shape": "TagMap"}, "domainNameArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the domain name.</p>"}}, "documentation": "<p>Describes a configuration for a custom domain.</p>"}, "DomainNameConfigs": {"type": "list", "member": {"shape": "DomainNameConfig"}}, "DynamodbDataSourceConfig": {"type": "structure", "required": ["tableName", "awsRegion"], "members": {"tableName": {"shape": "String", "documentation": "<p>The table name.</p>"}, "awsRegion": {"shape": "String", "documentation": "<p>The Amazon Web Services Region.</p>"}, "useCallerCredentials": {"shape": "Boolean", "documentation": "<p>Set to TRUE to use Amazon Cognito credentials with this data source.</p>"}, "deltaSyncConfig": {"shape": "DeltaSyncConfig", "documentation": "<p>The <code>DeltaSyncConfig</code> for a versioned data source.</p>"}, "versioned": {"shape": "Boolean", "documentation": "<p>Set to TRUE to use Conflict Detection and Resolution with this data source.</p>"}}, "documentation": "<p>Describes an Amazon DynamoDB data source configuration.</p>"}, "ElasticsearchDataSourceConfig": {"type": "structure", "required": ["endpoint", "awsRegion"], "members": {"endpoint": {"shape": "String", "documentation": "<p>The endpoint.</p>"}, "awsRegion": {"shape": "String", "documentation": "<p>The Amazon Web Services Region.</p>"}}, "documentation": "<p>Describes an OpenSearch data source configuration.</p> <p>As of September 2021, Amazon Elasticsearch service is Amazon OpenSearch Service. This configuration is deprecated. For new data sources, use <a>OpenSearchServiceDataSourceConfig</a> to specify an OpenSearch data source.</p>"}, "EnhancedMetricsConfig": {"type": "structure", "required": ["resolverLevelMetricsBehavior", "dataSourceLevelMetricsBehavior", "operationLevelMetricsConfig"], "members": {"resolverLevelMetricsBehavior": {"shape": "ResolverLevelMetricsBehavior", "documentation": "<p>Controls how resolver metrics will be emitted to CloudWatch. Resolver metrics include:</p> <ul> <li> <p>GraphQL errors: The number of GraphQL errors that occurred.</p> </li> <li> <p>Requests: The number of invocations that occurred during a request. </p> </li> <li> <p>Latency: The time to complete a resolver invocation.</p> </li> <li> <p>Cache hits: The number of cache hits during a request.</p> </li> <li> <p>Cache misses: The number of cache misses during a request.</p> </li> </ul> <p>These metrics can be emitted to CloudWatch per resolver or for all resolvers in the request. Metrics will be recorded by API ID and resolver name. <code>resolverLevelMetricsBehavior</code> accepts one of these values at a time:</p> <ul> <li> <p> <code>FULL_REQUEST_RESOLVER_METRICS</code>: Records and emits metric data for all resolvers in the request.</p> </li> <li> <p> <code>PER_RESOLVER_METRICS</code>: Records and emits metric data for resolvers that have the <code>metricsConfig</code> value set to <code>ENABLED</code>.</p> </li> </ul>"}, "dataSourceLevelMetricsBehavior": {"shape": "DataSourceLevelMetricsBehavior", "documentation": "<p>Controls how data source metrics will be emitted to CloudWatch. Data source metrics include:</p> <ul> <li> <p>Requests: The number of invocations that occured during a request.</p> </li> <li> <p>Latency: The time to complete a data source invocation.</p> </li> <li> <p>Errors: The number of errors that occurred during a data source invocation.</p> </li> </ul> <p>These metrics can be emitted to CloudWatch per data source or for all data sources in the request. Metrics will be recorded by API ID and data source name. <code>dataSourceLevelMetricsBehavior</code> accepts one of these values at a time:</p> <ul> <li> <p> <code>FULL_REQUEST_DATA_SOURCE_METRICS</code>: Records and emits metric data for all data sources in the request.</p> </li> <li> <p> <code>PER_DATA_SOURCE_METRICS</code>: Records and emits metric data for data sources that have the <code>metricsConfig</code> value set to <code>ENABLED</code>.</p> </li> </ul>"}, "operationLevelMetricsConfig": {"shape": "OperationLevelMetricsConfig", "documentation": "<p> Controls how operation metrics will be emitted to CloudWatch. Operation metrics include:</p> <ul> <li> <p>Requests: The number of times a specified GraphQL operation was called.</p> </li> <li> <p>GraphQL errors: The number of GraphQL errors that occurred during a specified GraphQL operation.</p> </li> </ul> <p>Metrics will be recorded by API ID and operation name. You can set the value to <code>ENABLED</code> or <code>DISABLED</code>.</p>"}}, "documentation": "<p>Enables and controls the enhanced metrics feature. Enhanced metrics emit granular data on API usage and performance such as AppSync request and error counts, latency, and cache hits/misses. All enhanced metric data is sent to your CloudWatch account, and you can configure the types of data that will be sent. </p> <p>Enhanced metrics can be configured at the resolver, data source, and operation levels. <code>EnhancedMetricsConfig</code> contains three required parameters, each controlling one of these categories:</p> <ol> <li> <p> <code>resolverLevelMetricsBehavior</code>: Controls how resolver metrics will be emitted to CloudWatch. Resolver metrics include:</p> <ul> <li> <p>GraphQL errors: The number of GraphQL errors that occurred.</p> </li> <li> <p>Requests: The number of invocations that occurred during a request. </p> </li> <li> <p>Latency: The time to complete a resolver invocation.</p> </li> <li> <p>Cache hits: The number of cache hits during a request.</p> </li> <li> <p>Cache misses: The number of cache misses during a request.</p> </li> </ul> <p>These metrics can be emitted to CloudWatch per resolver or for all resolvers in the request. Metrics will be recorded by API ID and resolver name. <code>resolverLevelMetricsBehavior</code> accepts one of these values at a time:</p> <ul> <li> <p> <code>FULL_REQUEST_RESOLVER_METRICS</code>: Records and emits metric data for all resolvers in the request.</p> </li> <li> <p> <code>PER_RESOLVER_METRICS</code>: Records and emits metric data for resolvers that have the <code>metricsConfig</code> value set to <code>ENABLED</code>.</p> </li> </ul> </li> <li> <p> <code>dataSourceLevelMetricsBehavior</code>: Controls how data source metrics will be emitted to CloudWatch. Data source metrics include:</p> <ul> <li> <p>Requests: The number of invocations that occured during a request.</p> </li> <li> <p>Latency: The time to complete a data source invocation.</p> </li> <li> <p>Errors: The number of errors that occurred during a data source invocation.</p> </li> </ul> <p>These metrics can be emitted to CloudWatch per data source or for all data sources in the request. Metrics will be recorded by API ID and data source name. <code>dataSourceLevelMetricsBehavior</code> accepts one of these values at a time:</p> <ul> <li> <p> <code>FULL_REQUEST_DATA_SOURCE_METRICS</code>: Records and emits metric data for all data sources in the request.</p> </li> <li> <p> <code>PER_DATA_SOURCE_METRICS</code>: Records and emits metric data for data sources that have the <code>metricsConfig</code> value set to <code>ENABLED</code>.</p> </li> </ul> </li> <li> <p> <code>operationLevelMetricsConfig</code>: Controls how operation metrics will be emitted to CloudWatch. Operation metrics include:</p> <ul> <li> <p>Requests: The number of times a specified GraphQL operation was called.</p> </li> <li> <p>GraphQL errors: The number of GraphQL errors that occurred during a specified GraphQL operation.</p> </li> </ul> <p>Metrics will be recorded by API ID and operation name. You can set the value to <code>ENABLED</code> or <code>DISABLED</code>.</p> </li> </ol>"}, "EnvironmentVariableKey": {"type": "string", "max": 64, "min": 2, "pattern": "^[A-Za-z]+\\w*$"}, "EnvironmentVariableMap": {"type": "map", "key": {"shape": "EnvironmentVariableKey"}, "value": {"shape": "EnvironmentVariableValue"}, "max": 50, "min": 0}, "EnvironmentVariableValue": {"type": "string", "max": 512, "min": 0}, "ErrorDetail": {"type": "structure", "members": {"message": {"shape": "ErrorMessage", "documentation": "<p>The error payload.</p>"}}, "documentation": "<p>Contains the list of errors generated. When using JavaScript, this will apply to the request or response function evaluation.</p>"}, "ErrorMessage": {"type": "string"}, "EvaluateCodeErrorDetail": {"type": "structure", "members": {"message": {"shape": "ErrorMessage", "documentation": "<p>The error payload.</p>"}, "codeErrors": {"shape": "CodeErrors", "documentation": "<p>Contains the list of <code>CodeError</code> objects.</p>"}}, "documentation": "<p>Contains the list of errors from a code evaluation response.</p>"}, "EvaluateCodeRequest": {"type": "structure", "required": ["runtime", "code", "context"], "members": {"runtime": {"shape": "AppSyncRuntime", "documentation": "<p>The runtime to be used when evaluating the code. Currently, only the <code>APPSYNC_JS</code> runtime is supported.</p>"}, "code": {"shape": "Code", "documentation": "<p>The code definition to be evaluated. Note that <code>code</code> and <code>runtime</code> are both required for this action. The <code>runtime</code> value must be <code>APPSYNC_JS</code>.</p>"}, "context": {"shape": "Context", "documentation": "<p>The map that holds all of the contextual information for your resolver invocation. A <code>context</code> is required for this action.</p>"}, "function": {"shape": "String", "documentation": "<p>The function within the code to be evaluated. If provided, the valid values are <code>request</code> and <code>response</code>.</p>"}}}, "EvaluateCodeResponse": {"type": "structure", "members": {"evaluationResult": {"shape": "EvaluationResult", "documentation": "<p>The result of the evaluation operation.</p>"}, "error": {"shape": "EvaluateCodeErrorDetail", "documentation": "<p>Contains the payload of the response error.</p>"}, "logs": {"shape": "Logs", "documentation": "<p>A list of logs that were generated by calls to <code>util.log.info</code> and <code>util.log.error</code> in the evaluated code.</p>"}, "stash": {"shape": "Stash", "documentation": "<p>An object available inside each resolver and function handler. A single <code>stash</code> object lives through a single resolver run. Therefore, you can use the stash to pass arbitrary data across request and response handlers and across functions in a pipeline resolver.</p>"}, "outErrors": {"shape": "OutErrors", "documentation": "<p>The list of runtime errors that are added to the GraphQL operation response.</p>"}}}, "EvaluateMappingTemplateRequest": {"type": "structure", "required": ["template", "context"], "members": {"template": {"shape": "Template", "documentation": "<p>The mapping template; this can be a request or response template. A <code>template</code> is required for this action.</p>"}, "context": {"shape": "Context", "documentation": "<p>The map that holds all of the contextual information for your resolver invocation. A <code>context</code> is required for this action.</p>"}}}, "EvaluateMappingTemplateResponse": {"type": "structure", "members": {"evaluationResult": {"shape": "EvaluationResult", "documentation": "<p>The mapping template; this can be a request or response template.</p>"}, "error": {"shape": "ErrorDetail", "documentation": "<p>The <code>ErrorDetail</code> object.</p>"}, "logs": {"shape": "Logs", "documentation": "<p>A list of logs that were generated by calls to <code>util.log.info</code> and <code>util.log.error</code> in the evaluated code.</p>"}, "stash": {"shape": "Stash", "documentation": "<p>An object available inside each resolver and function handler. A single <code>stash</code> object lives through a single resolver run. Therefore, you can use the stash to pass arbitrary data across request and response handlers and across functions in a pipeline resolver.</p>"}, "outErrors": {"shape": "OutErrors", "documentation": "<p>The list of runtime errors that are added to the GraphQL operation response.</p>"}}}, "EvaluationResult": {"type": "string", "max": 65536, "min": 0, "pattern": "^[\\s\\S]*$"}, "EventBridgeDataSourceConfig": {"type": "structure", "required": ["eventBusArn"], "members": {"eventBusArn": {"shape": "String", "documentation": "<p>The ARN of the event bus. For more information about event buses, see <a href=\"https://docs.aws.amazon.com/eventbridge/latest/userguide/eb-event-bus.html\">Amazon EventBridge event buses</a>.</p>"}}, "documentation": "<p>Describes an Amazon EventBridge bus data source configuration.</p>"}, "EventConfig": {"type": "structure", "required": ["authProviders", "connectionAuthModes", "defaultPublishAuthModes", "defaultSubscribeAuthModes"], "members": {"authProviders": {"shape": "AuthProviders", "documentation": "<p>A list of authorization providers.</p>"}, "connectionAuthModes": {"shape": "AuthModes", "documentation": "<p>A list of valid authorization modes for the Event API connections.</p>"}, "defaultPublishAuthModes": {"shape": "AuthModes", "documentation": "<p>A list of valid authorization modes for the Event API publishing.</p>"}, "defaultSubscribeAuthModes": {"shape": "AuthModes", "documentation": "<p>A list of valid authorization modes for the Event API subscriptions.</p>"}, "logConfig": {"shape": "EventLogConfig", "documentation": "<p>The CloudWatch Logs configuration for the Event API.</p>"}}, "documentation": "<p>Describes the authorization configuration for connections, message publishing, message subscriptions, and logging for an Event API.</p>"}, "EventLogConfig": {"type": "structure", "required": ["logLevel", "cloudWatchLogsRoleArn"], "members": {"logLevel": {"shape": "EventLogLevel", "documentation": "<p>The type of information to log for the Event API. </p>"}, "cloudWatchLogsRoleArn": {"shape": "String", "documentation": "<p>The IAM service role that AppSync assumes to publish CloudWatch Logs in your account.</p>"}}, "documentation": "<p>Describes the CloudWatch Logs configuration for the Event API.</p>"}, "EventLogLevel": {"type": "string", "enum": ["NONE", "ERROR", "ALL", "INFO", "DEBUG"]}, "FieldLogLevel": {"type": "string", "enum": ["NONE", "ERROR", "ALL", "INFO", "DEBUG"]}, "FlushApiCacheRequest": {"type": "structure", "required": ["apiId"], "members": {"apiId": {"shape": "String", "documentation": "<p>The API ID.</p>", "location": "uri", "locationName": "apiId"}}, "documentation": "<p>Represents the input of a <code>FlushApiCache</code> operation.</p>"}, "FlushApiCacheResponse": {"type": "structure", "members": {}, "documentation": "<p>Represents the output of a <code>FlushApiCache</code> operation.</p>"}, "FunctionConfiguration": {"type": "structure", "members": {"functionId": {"shape": "String", "documentation": "<p>A unique ID representing the <code>Function</code> object.</p>"}, "functionArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the <code>Function</code> object.</p>"}, "name": {"shape": "ResourceName", "documentation": "<p>The name of the <code>Function</code> object.</p>"}, "description": {"shape": "String", "documentation": "<p>The <code>Function</code> description.</p>"}, "dataSourceName": {"shape": "ResourceName", "documentation": "<p>The name of the <code>DataSource</code>.</p>"}, "requestMappingTemplate": {"shape": "MappingTemplate", "documentation": "<p>The <code>Function</code> request mapping template. Functions support only the 2018-05-29 version of the request mapping template.</p>"}, "responseMappingTemplate": {"shape": "MappingTemplate", "documentation": "<p>The <code>Function</code> response mapping template.</p>"}, "functionVersion": {"shape": "String", "documentation": "<p>The version of the request mapping template. Currently, only the 2018-05-29 version of the template is supported.</p>"}, "syncConfig": {"shape": "SyncConfig"}, "maxBatchSize": {"shape": "MaxBatchSize", "documentation": "<p>The maximum batching size for a resolver.</p>"}, "runtime": {"shape": "AppSyncRuntime"}, "code": {"shape": "Code", "documentation": "<p>The <code>function</code> code that contains the request and response functions. When code is used, the <code>runtime</code> is required. The <code>runtime</code> value must be <code>APPSYNC_JS</code>.</p>"}}, "documentation": "<p>A function is a reusable entity. You can use multiple functions to compose the resolver logic.</p>"}, "Functions": {"type": "list", "member": {"shape": "FunctionConfiguration"}}, "FunctionsIds": {"type": "list", "member": {"shape": "String"}}, "GetApiAssociationRequest": {"type": "structure", "required": ["domainName"], "members": {"domainName": {"shape": "DomainName", "documentation": "<p>The domain name.</p>", "location": "uri", "locationName": "domainName"}}}, "GetApiAssociationResponse": {"type": "structure", "members": {"apiAssociation": {"shape": "ApiAssociation", "documentation": "<p>The <code>ApiAssociation</code> object.</p>"}}}, "GetApiCacheRequest": {"type": "structure", "required": ["apiId"], "members": {"apiId": {"shape": "String", "documentation": "<p>The API ID.</p>", "location": "uri", "locationName": "apiId"}}, "documentation": "<p>Represents the input of a <code>GetApiCache</code> operation.</p>"}, "GetApiCacheResponse": {"type": "structure", "members": {"apiCache": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The <code>ApiCache</code> object.</p>"}}, "documentation": "<p>Represents the output of a <code>GetApiCache</code> operation.</p>"}, "GetApiRequest": {"type": "structure", "required": ["apiId"], "members": {"apiId": {"shape": "String", "documentation": "<p>The <code>Api</code> ID.</p>", "location": "uri", "locationName": "apiId"}}}, "GetApiResponse": {"type": "structure", "members": {"api": {"shape": "Api", "documentation": "<p>The <code>Api</code> object.</p>"}}}, "GetChannelNamespaceRequest": {"type": "structure", "required": ["apiId", "name"], "members": {"apiId": {"shape": "String", "documentation": "<p>The <code>Api</code> ID.</p>", "location": "uri", "locationName": "apiId"}, "name": {"shape": "Namespace", "documentation": "<p>The name of the <code>ChannelNamespace</code>.</p>", "location": "uri", "locationName": "name"}}}, "GetChannelNamespaceResponse": {"type": "structure", "members": {"channelNamespace": {"shape": "ChannelNamespace", "documentation": "<p>The <code>ChannelNamespace</code> object.</p>"}}}, "GetDataSourceIntrospectionRequest": {"type": "structure", "required": ["introspectionId"], "members": {"introspectionId": {"shape": "String", "documentation": "<p>The introspection ID. Each introspection contains a unique ID that can be used to reference the instrospection record.</p>", "location": "uri", "locationName": "introspectionId"}, "includeModelsSDL": {"shape": "Boolean", "documentation": "<p>A boolean flag that determines whether SDL should be generated for introspected types. If set to <code>true</code>, each model will contain an <code>sdl</code> property that contains the SDL for that type. The SDL only contains the type data and no additional metadata or directives. </p>", "location": "querystring", "locationName": "includeModelsSDL"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>Determines the number of types to be returned in a single response before paginating. This value is typically taken from <code>nextToken</code> value from the previous response.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of introspected types that will be returned in a single response.</p>", "location": "querystring", "locationName": "maxResults"}}}, "GetDataSourceIntrospectionResponse": {"type": "structure", "members": {"introspectionId": {"shape": "String", "documentation": "<p>The introspection ID. Each introspection contains a unique ID that can be used to reference the instrospection record.</p>"}, "introspectionStatus": {"shape": "DataSourceIntrospectionStatus", "documentation": "<p>The status of the introspection during retrieval. By default, when a new instrospection is being retrieved, the status will be set to <code>PROCESSING</code>. Once the operation has been completed, the status will change to <code>SUCCESS</code> or <code>FAILED</code> depending on how the data was parsed. A <code>FAILED</code> operation will return an error and its details as an <code>introspectionStatusDetail</code>.</p>"}, "introspectionStatusDetail": {"shape": "String", "documentation": "<p>The error detail field. When a <code>FAILED</code> <code>introspectionStatus</code> is returned, the <code>introspectionStatusDetail</code> will also return the exact error that was generated during the operation.</p>"}, "introspectionResult": {"shape": "DataSourceIntrospectionResult", "documentation": "<p>The <code>DataSourceIntrospectionResult</code> object data.</p>"}}}, "GetDataSourceRequest": {"type": "structure", "required": ["apiId", "name"], "members": {"apiId": {"shape": "String", "documentation": "<p>The API ID.</p>", "location": "uri", "locationName": "apiId"}, "name": {"shape": "ResourceName", "documentation": "<p>The name of the data source.</p>", "location": "uri", "locationName": "name"}}}, "GetDataSourceResponse": {"type": "structure", "members": {"dataSource": {"shape": "DataSource", "documentation": "<p>The <code>DataSource</code> object.</p>"}}}, "GetDomainNameRequest": {"type": "structure", "required": ["domainName"], "members": {"domainName": {"shape": "DomainName", "documentation": "<p>The domain name.</p>", "location": "uri", "locationName": "domainName"}}}, "GetDomainNameResponse": {"type": "structure", "members": {"domainNameConfig": {"shape": "DomainNameConfig", "documentation": "<p>The configuration for the <code>DomainName</code>.</p>"}}}, "GetFunctionRequest": {"type": "structure", "required": ["apiId", "functionId"], "members": {"apiId": {"shape": "String", "documentation": "<p>The GraphQL API ID.</p>", "location": "uri", "locationName": "apiId"}, "functionId": {"shape": "ResourceName", "documentation": "<p>The <code>Function</code> ID.</p>", "location": "uri", "locationName": "functionId"}}}, "GetFunctionResponse": {"type": "structure", "members": {"functionConfiguration": {"shape": "FunctionConfiguration", "documentation": "<p>The <code>Function</code> object.</p>"}}}, "GetGraphqlApiEnvironmentVariablesRequest": {"type": "structure", "required": ["apiId"], "members": {"apiId": {"shape": "String", "documentation": "<p>The ID of the API from which the environmental variable list will be retrieved.</p>", "location": "uri", "locationName": "apiId"}}}, "GetGraphqlApiEnvironmentVariablesResponse": {"type": "structure", "members": {"environmentVariables": {"shape": "EnvironmentVariableMap", "documentation": "<p>The payload containing each environmental variable in the <code>\"key\" : \"value\"</code> format.</p>"}}}, "GetGraphqlApiRequest": {"type": "structure", "required": ["apiId"], "members": {"apiId": {"shape": "String", "documentation": "<p>The API ID for the GraphQL API.</p>", "location": "uri", "locationName": "apiId"}}}, "GetGraphqlApiResponse": {"type": "structure", "members": {"graphqlApi": {"shape": "GraphqlApi", "documentation": "<p>The <code>GraphqlApi</code> object.</p>"}}}, "GetIntrospectionSchemaRequest": {"type": "structure", "required": ["apiId", "format"], "members": {"apiId": {"shape": "String", "documentation": "<p>The API ID.</p>", "location": "uri", "locationName": "apiId"}, "format": {"shape": "OutputType", "documentation": "<p>The schema format: SDL or JSON.</p>", "location": "querystring", "locationName": "format"}, "includeDirectives": {"shape": "BooleanValue", "documentation": "<p>A flag that specifies whether the schema introspection should contain directives.</p>", "location": "querystring", "locationName": "includeDirectives"}}}, "GetIntrospectionSchemaResponse": {"type": "structure", "members": {"schema": {"shape": "Blob", "documentation": "<p>The schema, in GraphQL Schema Definition Language (SDL) format.</p> <p>For more information, see the <a href=\"http://graphql.org/learn/schema/\">GraphQL SDL documentation</a>.</p>"}}, "payload": "schema"}, "GetResolverRequest": {"type": "structure", "required": ["apiId", "typeName", "fieldName"], "members": {"apiId": {"shape": "String", "documentation": "<p>The API ID.</p>", "location": "uri", "locationName": "apiId"}, "typeName": {"shape": "ResourceName", "documentation": "<p>The resolver type name.</p>", "location": "uri", "locationName": "typeName"}, "fieldName": {"shape": "ResourceName", "documentation": "<p>The resolver field name.</p>", "location": "uri", "locationName": "fieldName"}}}, "GetResolverResponse": {"type": "structure", "members": {"resolver": {"shape": "Resolver", "documentation": "<p>The <code>Resolver</code> object.</p>"}}}, "GetSchemaCreationStatusRequest": {"type": "structure", "required": ["apiId"], "members": {"apiId": {"shape": "String", "documentation": "<p>The API ID.</p>", "location": "uri", "locationName": "apiId"}}}, "GetSchemaCreationStatusResponse": {"type": "structure", "members": {"status": {"shape": "SchemaStatus", "documentation": "<p>The current state of the schema (PROCESSING, FAILED, SUCCESS, or NOT_APPLICABLE). When the schema is in the ACTIVE state, you can add data.</p>"}, "details": {"shape": "String", "documentation": "<p>Detailed information about the status of the schema creation operation.</p>"}}}, "GetSourceApiAssociationRequest": {"type": "structure", "required": ["mergedApiIdentifier", "associationId"], "members": {"mergedApiIdentifier": {"shape": "String", "documentation": "<p>The identifier of the AppSync Merged API. This is generated by the AppSync service. In most cases, Merged APIs (especially in your account) only require the API ID value or ARN of the merged API. However, Merged APIs in other accounts (cross-account use cases) strictly require the full resource ARN of the merged API.</p>", "location": "uri", "locationName": "mergedApiIdentifier"}, "associationId": {"shape": "String", "documentation": "<p>The ID generated by the AppSync service for the source API association.</p>", "location": "uri", "locationName": "associationId"}}}, "GetSourceApiAssociationResponse": {"type": "structure", "members": {"sourceApiAssociation": {"shape": "SourceApiAssociation", "documentation": "<p>The <code>SourceApiAssociation</code> object data.</p>"}}}, "GetTypeRequest": {"type": "structure", "required": ["apiId", "typeName", "format"], "members": {"apiId": {"shape": "String", "documentation": "<p>The API ID.</p>", "location": "uri", "locationName": "apiId"}, "typeName": {"shape": "ResourceName", "documentation": "<p>The type name.</p>", "location": "uri", "locationName": "typeName"}, "format": {"shape": "TypeDefinitionFormat", "documentation": "<p>The type format: SDL or JSON.</p>", "location": "querystring", "locationName": "format"}}}, "GetTypeResponse": {"type": "structure", "members": {"type": {"shape": "Type", "documentation": "<p>The <code>Type</code> object.</p>"}}}, "GraphQLApiIntrospectionConfig": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "GraphQLApiType": {"type": "string", "enum": ["GRAPHQL", "MERGED"]}, "GraphQLApiVisibility": {"type": "string", "enum": ["GLOBAL", "PRIVATE"]}, "GraphQLSchemaException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The GraphQL schema is not valid.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "GraphqlApi": {"type": "structure", "members": {"name": {"shape": "ResourceName", "documentation": "<p>The API name.</p>"}, "apiId": {"shape": "String", "documentation": "<p>The API ID.</p>"}, "authenticationType": {"shape": "AuthenticationType", "documentation": "<p>The authentication type.</p>"}, "logConfig": {"shape": "LogConfig", "documentation": "<p>The Amazon CloudWatch Logs configuration.</p>"}, "userPoolConfig": {"shape": "UserPoolConfig", "documentation": "<p>The Amazon Cognito user pool configuration.</p>"}, "openIDConnectConfig": {"shape": "OpenIDConnectConfig", "documentation": "<p>The OpenID Connect configuration.</p>"}, "arn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN).</p>"}, "uris": {"shape": "MapOfStringToString", "documentation": "<p>The URIs.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags.</p>"}, "additionalAuthenticationProviders": {"shape": "AdditionalAuthenticationProviders", "documentation": "<p>A list of additional authentication providers for the <code>GraphqlApi</code> API.</p>"}, "xrayEnabled": {"shape": "Boolean", "documentation": "<p>A flag indicating whether to use X-Ray tracing for this <code>GraphqlApi</code>.</p>"}, "wafWebAclArn": {"shape": "String", "documentation": "<p>The ARN of the WAF access control list (ACL) associated with this <code>GraphqlApi</code>, if one exists.</p>"}, "lambdaAuthorizerConfig": {"shape": "LambdaAuthorizerConfig", "documentation": "<p>Configuration for Lambda function authorization.</p>"}, "dns": {"shape": "MapOfStringToString", "documentation": "<p>The DNS records for the API.</p>"}, "visibility": {"shape": "GraphQLApiVisibility", "documentation": "<p>Sets the value of the GraphQL API to public (<code>GLOBAL</code>) or private (<code>PRIVATE</code>). If no value is provided, the visibility will be set to <code>GLOBAL</code> by default. This value cannot be changed once the API has been created.</p>"}, "apiType": {"shape": "GraphQLApiType", "documentation": "<p>The value that indicates whether the GraphQL API is a standard API (<code>GRAPHQL</code>) or merged API (<code>MERGED</code>).</p>"}, "mergedApiExecutionRoleArn": {"shape": "String", "documentation": "<p>The Identity and Access Management service role ARN for a merged API. The AppSync service assumes this role on behalf of the Merged API to validate access to source APIs at runtime and to prompt the <code>AUTO_MERGE</code> to update the merged API endpoint with the source API changes automatically.</p>"}, "owner": {"shape": "String", "documentation": "<p>The account owner of the GraphQL API.</p>"}, "ownerContact": {"shape": "String", "documentation": "<p>The owner contact information for an API resource.</p> <p>This field accepts any string input with a length of 0 - 256 characters.</p>"}, "introspectionConfig": {"shape": "GraphQLApiIntrospectionConfig", "documentation": "<p>Sets the value of the GraphQL API to enable (<code>ENABLED</code>) or disable (<code>DISABLED</code>) introspection. If no value is provided, the introspection configuration will be set to <code>ENABLED</code> by default. This field will produce an error if the operation attempts to use the introspection feature while this field is disabled.</p> <p>For more information about introspection, see <a href=\"https://graphql.org/learn/introspection/\">GraphQL introspection</a>.</p>"}, "queryDepthLimit": {"shape": "QueryDepthLimit", "documentation": "<p>The maximum depth a query can have in a single request. Depth refers to the amount of nested levels allowed in the body of query. The default value is <code>0</code> (or unspecified), which indicates there's no depth limit. If you set a limit, it can be between <code>1</code> and <code>75</code> nested levels. This field will produce a limit error if the operation falls out of bounds.</p> <p>Note that fields can still be set to nullable or non-nullable. If a non-nullable field produces an error, the error will be thrown upwards to the first nullable field available.</p>"}, "resolverCountLimit": {"shape": "ResolverCountLimit", "documentation": "<p>The maximum number of resolvers that can be invoked in a single request. The default value is <code>0</code> (or unspecified), which will set the limit to <code>10000</code>. When specified, the limit value can be between <code>1</code> and <code>10000</code>. This field will produce a limit error if the operation falls out of bounds.</p>"}, "enhancedMetricsConfig": {"shape": "EnhancedMetricsConfig", "documentation": "<p>The <code>enhancedMetricsConfig</code> object.</p>"}}, "documentation": "<p>Describes a GraphQL API.</p>"}, "GraphqlApis": {"type": "list", "member": {"shape": "GraphqlApi"}}, "HandlerBehavior": {"type": "string", "enum": ["CODE", "DIRECT"]}, "HandlerConfig": {"type": "structure", "required": ["behavior", "integration"], "members": {"behavior": {"shape": "Handler<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The behavior for the handler.</p>"}, "integration": {"shape": "Integration", "documentation": "<p>The integration data source configuration for the handler.</p>"}}, "documentation": "<p>The configuration for a handler.</p>"}, "HandlerConfigs": {"type": "structure", "members": {"onPublish": {"shape": "HandlerConfig", "documentation": "<p>The configuration for the <code>OnPublish</code> handler.</p>"}, "onSubscribe": {"shape": "HandlerConfig", "documentation": "<p>The configuration for the <code>OnSubscribe</code> handler.</p>"}}, "documentation": "<p>The configuration for the <code>OnPublish</code> and <code>OnSubscribe</code> handlers.</p>"}, "HttpDataSourceConfig": {"type": "structure", "members": {"endpoint": {"shape": "String", "documentation": "<p>The HTTP URL endpoint. You can specify either the domain name or IP, and port combination, and the URL scheme must be HTTP or HTTPS. If you don't specify the port, AppSync uses the default port 80 for the HTTP endpoint and port 443 for HTTPS endpoints.</p>"}, "authorizationConfig": {"shape": "AuthorizationConfig", "documentation": "<p>The authorization configuration in case the HTTP endpoint requires authorization.</p>"}}, "documentation": "<p>Describes an HTTP data source configuration.</p>"}, "Integration": {"type": "structure", "required": ["dataSourceName"], "members": {"dataSourceName": {"shape": "String", "documentation": "<p>The unique name of the data source that has been configured on the API.</p>"}, "lambdaConfig": {"shape": "LambdaConfig", "documentation": "<p>The configuration for a Lambda data source.</p>"}}, "documentation": "<p>The integration data source configuration for the handler.</p>"}, "InternalFailureException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>An internal AppSync error occurred. Try your request again.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true}, "InvokeType": {"type": "string", "enum": ["REQUEST_RESPONSE", "EVENT"]}, "LambdaAuthorizerConfig": {"type": "structure", "required": ["authorizer<PERSON><PERSON>"], "members": {"authorizerResultTtlInSeconds": {"shape": "TTL", "documentation": "<p>The number of seconds a response should be cached for. The default is 0 seconds, which disables caching. If you don't specify a value for <code>authorizerResultTtlInSeconds</code>, the default value is used. The maximum value is one hour (3600 seconds). The Lambda function can override this by returning a <code>ttlOverride</code> key in its response.</p>"}, "authorizerUri": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the Lambda function to be called for authorization. This can be a standard Lambda ARN, a version ARN (<code>.../v3</code>), or an alias ARN. </p> <p> <b>Note</b>: This Lambda function must have the following resource-based policy assigned to it. When configuring Lambda authorizers in the console, this is done for you. To use the Command Line Interface (CLI), run the following:</p> <p> <code>aws lambda add-permission --function-name \"arn:aws:lambda:us-east-2:111122223333:function:my-function\" --statement-id \"appsync\" --principal appsync.amazonaws.com --action lambda:InvokeFunction</code> </p>"}, "identityValidationExpression": {"shape": "String", "documentation": "<p>A regular expression for validation of tokens before the Lambda function is called.</p>"}}, "documentation": "<p>A <code>LambdaAuthorizerConfig</code> specifies how to authorize AppSync API access when using the <code>AWS_LAMBDA</code> authorizer mode. Be aware that an AppSync API can have only one Lambda authorizer configured at a time.</p>"}, "LambdaConfig": {"type": "structure", "members": {"invokeType": {"shape": "InvokeType", "documentation": "<p>The invocation type for a Lambda data source.</p>"}}, "documentation": "<p>The configuration for a Lambda data source.</p>"}, "LambdaConflictHandlerConfig": {"type": "structure", "members": {"lambdaConflictHandlerArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) for the Lambda function to use as the Conflict Handler.</p>"}}, "documentation": "<p>The <code>LambdaConflictHandlerConfig</code> object when configuring <code>LAMBDA</code> as the Conflict Handler.</p>"}, "LambdaDataSourceConfig": {"type": "structure", "required": ["lambdaFunctionArn"], "members": {"lambdaFunctionArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) for the Lambda function.</p>"}}, "documentation": "<p>Describes an Lambda data source configuration.</p>"}, "LimitExceededException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>The request exceeded a limit. Try your request again.</p>", "error": {"httpStatusCode": 429}, "exception": true}, "ListApiKeysRequest": {"type": "structure", "required": ["apiId"], "members": {"apiId": {"shape": "String", "documentation": "<p>The API ID.</p>", "location": "uri", "locationName": "apiId"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>An identifier that was returned from the previous call to this operation, which you can use to return the next set of items in the list.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results that you want the request to return.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListApiKeysResponse": {"type": "structure", "members": {"apiKeys": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The <code>ApiKey</code> objects.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>An identifier to pass in the next request to this operation to return the next set of items in the list.</p>"}}}, "ListApisRequest": {"type": "structure", "members": {"nextToken": {"shape": "PaginationToken", "documentation": "<p>An identifier that was returned from the previous call to this operation, which you can use to return the next set of items in the list.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results that you want the request to return.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListApisResponse": {"type": "structure", "members": {"apis": {"shape": "Apis", "documentation": "<p>The <code>Api</code> objects.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>An identifier that was returned from the previous call to this operation, which you can use to return the next set of items in the list.</p>"}}}, "ListChannelNamespacesRequest": {"type": "structure", "required": ["apiId"], "members": {"apiId": {"shape": "String", "documentation": "<p>The <code>Api</code> ID.</p>", "location": "uri", "locationName": "apiId"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>An identifier that was returned from the previous call to this operation, which you can use to return the next set of items in the list.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results that you want the request to return.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListChannelNamespacesResponse": {"type": "structure", "members": {"channelNamespaces": {"shape": "ChannelNamespaces", "documentation": "<p>The <code>ChannelNamespace</code> objects.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>An identifier that was returned from the previous call to this operation, which you can use to return the next set of items in the list.</p>"}}}, "ListDataSourcesRequest": {"type": "structure", "required": ["apiId"], "members": {"apiId": {"shape": "String", "documentation": "<p>The API ID.</p>", "location": "uri", "locationName": "apiId"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>An identifier that was returned from the previous call to this operation, which you can use to return the next set of items in the list.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results that you want the request to return.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListDataSourcesResponse": {"type": "structure", "members": {"dataSources": {"shape": "DataSources", "documentation": "<p>The <code>DataSource</code> objects.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>An identifier to pass in the next request to this operation to return the next set of items in the list.</p>"}}}, "ListDomainNamesRequest": {"type": "structure", "members": {"nextToken": {"shape": "PaginationToken", "documentation": "<p>An identifier that was returned from the previous call to this operation, which you can use to return the next set of items in the list.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results that you want the request to return.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListDomainNamesResponse": {"type": "structure", "members": {"domainNameConfigs": {"shape": "DomainNameConfigs", "documentation": "<p>Lists configurations for multiple domain names.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>An identifier that was returned from the previous call to this operation, which you can use to return the next set of items in the list.</p>"}}}, "ListFunctionsRequest": {"type": "structure", "required": ["apiId"], "members": {"apiId": {"shape": "String", "documentation": "<p>The GraphQL API ID.</p>", "location": "uri", "locationName": "apiId"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>An identifier that was returned from the previous call to this operation, which you can use to return the next set of items in the list.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results that you want the request to return.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListFunctionsResponse": {"type": "structure", "members": {"functions": {"shape": "Functions", "documentation": "<p>A list of <code>Function</code> objects.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>An identifier that was returned from the previous call to this operation, which you can use to return the next set of items in the list.</p>"}}}, "ListGraphqlApisRequest": {"type": "structure", "members": {"nextToken": {"shape": "PaginationToken", "documentation": "<p>An identifier that was returned from the previous call to this operation, which you can use to return the next set of items in the list.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results that you want the request to return.</p>", "location": "querystring", "locationName": "maxResults"}, "apiType": {"shape": "GraphQLApiType", "documentation": "<p>The value that indicates whether the GraphQL API is a standard API (<code>GRAPHQL</code>) or merged API (<code>MERGED</code>).</p>", "location": "querystring", "locationName": "apiType"}, "owner": {"shape": "Ownership", "documentation": "<p>The account owner of the GraphQL API.</p>", "location": "querystring", "locationName": "owner"}}}, "ListGraphqlApisResponse": {"type": "structure", "members": {"graphqlApis": {"shape": "GraphqlApis", "documentation": "<p>The <code>GraphqlApi</code> objects.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>An identifier to pass in the next request to this operation to return the next set of items in the list.</p>"}}}, "ListResolversByFunctionRequest": {"type": "structure", "required": ["apiId", "functionId"], "members": {"apiId": {"shape": "String", "documentation": "<p>The API ID.</p>", "location": "uri", "locationName": "apiId"}, "functionId": {"shape": "String", "documentation": "<p>The function ID.</p>", "location": "uri", "locationName": "functionId"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>An identifier that was returned from the previous call to this operation, which you can use to return the next set of items in the list.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results that you want the request to return.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListResolversByFunctionResponse": {"type": "structure", "members": {"resolvers": {"shape": "Resolvers", "documentation": "<p>The list of resolvers.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>An identifier that you can use to return the next set of items in the list.</p>"}}}, "ListResolversRequest": {"type": "structure", "required": ["apiId", "typeName"], "members": {"apiId": {"shape": "String", "documentation": "<p>The API ID.</p>", "location": "uri", "locationName": "apiId"}, "typeName": {"shape": "String", "documentation": "<p>The type name.</p>", "location": "uri", "locationName": "typeName"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>An identifier that was returned from the previous call to this operation, which you can use to return the next set of items in the list.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results that you want the request to return.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListResolversResponse": {"type": "structure", "members": {"resolvers": {"shape": "Resolvers", "documentation": "<p>The <code>Resolver</code> objects.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>An identifier to pass in the next request to this operation to return the next set of items in the list.</p>"}}}, "ListSourceApiAssociationsRequest": {"type": "structure", "required": ["apiId"], "members": {"apiId": {"shape": "String", "documentation": "<p>The API ID.</p>", "location": "uri", "locationName": "apiId"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>An identifier that was returned from the previous call to this operation, which you can use to return the next set of items in the list.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results that you want the request to return.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListSourceApiAssociationsResponse": {"type": "structure", "members": {"sourceApiAssociationSummaries": {"shape": "SourceApiAssociationSummaryList", "documentation": "<p>The <code>SourceApiAssociationSummary</code> object data.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>An identifier that was returned from the previous call to this operation, which you can use to return the next set of items in the list.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "ResourceArn", "documentation": "<p>The <code>GraphqlApi</code> Amazon Resource Name (ARN).</p>", "location": "uri", "locationName": "resourceArn"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"tags": {"shape": "TagMap", "documentation": "<p>A <code>TagMap</code> object.</p>"}}}, "ListTypesByAssociationRequest": {"type": "structure", "required": ["mergedApiIdentifier", "associationId", "format"], "members": {"mergedApiIdentifier": {"shape": "String", "documentation": "<p>The identifier of the AppSync Merged API. This is generated by the AppSync service. In most cases, Merged APIs (especially in your account) only require the API ID value or ARN of the merged API. However, Merged APIs in other accounts (cross-account use cases) strictly require the full resource ARN of the merged API.</p>", "location": "uri", "locationName": "mergedApiIdentifier"}, "associationId": {"shape": "String", "documentation": "<p>The ID generated by the AppSync service for the source API association.</p>", "location": "uri", "locationName": "associationId"}, "format": {"shape": "TypeDefinitionFormat", "documentation": "<p>The format type.</p>", "location": "querystring", "locationName": "format"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>An identifier that was returned from the previous call to this operation, which you can use to return the next set of items in the list.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results that you want the request to return.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListTypesByAssociationResponse": {"type": "structure", "members": {"types": {"shape": "TypeList", "documentation": "<p>The <code>Type</code> objects.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>An identifier that was returned from the previous call to this operation, which you can use to return the next set of items in the list.</p>"}}}, "ListTypesRequest": {"type": "structure", "required": ["apiId", "format"], "members": {"apiId": {"shape": "String", "documentation": "<p>The API ID.</p>", "location": "uri", "locationName": "apiId"}, "format": {"shape": "TypeDefinitionFormat", "documentation": "<p>The type format: SDL or JSON.</p>", "location": "querystring", "locationName": "format"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>An identifier that was returned from the previous call to this operation, which you can use to return the next set of items in the list.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results that you want the request to return.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListTypesResponse": {"type": "structure", "members": {"types": {"shape": "TypeList", "documentation": "<p>The <code>Type</code> objects.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>An identifier to pass in the next request to this operation to return the next set of items in the list.</p>"}}}, "LogConfig": {"type": "structure", "required": ["fieldLogLevel", "cloudWatchLogsRoleArn"], "members": {"fieldLogLevel": {"shape": "FieldLogLevel", "documentation": "<p>The field logging level. Values can be NONE, ERROR, or ALL.</p> <ul> <li> <p> <b>NONE</b>: No field-level logs are captured.</p> </li> <li> <p> <b>ERROR</b>: Logs the following information only for the fields that are in error:</p> <ul> <li> <p>The error section in the server response.</p> </li> <li> <p>Field-level errors.</p> </li> <li> <p>The generated request/response functions that got resolved for error fields.</p> </li> </ul> </li> <li> <p> <b>ALL</b>: The following information is logged for all fields in the query:</p> <ul> <li> <p>Field-level tracing information.</p> </li> <li> <p>The generated request/response functions that got resolved for each field.</p> </li> </ul> </li> </ul>"}, "cloudWatchLogsRoleArn": {"shape": "String", "documentation": "<p>The service role that AppSync assumes to publish to CloudWatch logs in your account.</p>"}, "excludeVerboseContent": {"shape": "Boolean", "documentation": "<p>Set to TRUE to exclude sections that contain information such as headers, context, and evaluated mapping templates, regardless of logging level.</p>"}}, "documentation": "<p>The Amazon CloudWatch Logs configuration.</p>"}, "Logs": {"type": "list", "member": {"shape": "String"}}, "Long": {"type": "long"}, "MapOfStringToString": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "String"}}, "MappingTemplate": {"type": "string", "max": 65536, "min": 1, "pattern": "^.*$"}, "MaxBatchSize": {"type": "integer", "max": 2000, "min": 0}, "MaxResults": {"type": "integer", "max": 25, "min": 0}, "MergeType": {"type": "string", "enum": ["MANUAL_MERGE", "AUTO_MERGE"]}, "Namespace": {"type": "string", "max": 50, "min": 1, "pattern": "([A-Za-z0-9](?:[A-Za-z0-9\\-]{0,48}[A-Za-z0-9])?)"}, "NotFoundException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>The resource specified in the request was not found. Check the resource, and then try again.</p>", "error": {"httpStatusCode": 404}, "exception": true}, "OpenIDConnectConfig": {"type": "structure", "required": ["issuer"], "members": {"issuer": {"shape": "String", "documentation": "<p>The issuer for the OIDC configuration. The issuer returned by discovery must exactly match the value of <code>iss</code> in the ID token.</p>"}, "clientId": {"shape": "String", "documentation": "<p>The client identifier of the relying party at the OpenID identity provider. This identifier is typically obtained when the relying party is registered with the OpenID identity provider. You can specify a regular expression so that AppSync can validate against multiple client identifiers at a time.</p>"}, "iatTTL": {"shape": "<PERSON>", "documentation": "<p>The number of milliseconds that a token is valid after it's issued to a user.</p>"}, "authTTL": {"shape": "<PERSON>", "documentation": "<p>The number of milliseconds that a token is valid after being authenticated.</p>"}}, "documentation": "<p>Describes an OpenID Connect (OIDC) configuration.</p>"}, "OpenSearchServiceDataSourceConfig": {"type": "structure", "required": ["endpoint", "awsRegion"], "members": {"endpoint": {"shape": "String", "documentation": "<p>The endpoint.</p>"}, "awsRegion": {"shape": "String", "documentation": "<p>The Amazon Web Services Region.</p>"}}, "documentation": "<p>Describes an OpenSearch data source configuration.</p>"}, "OperationLevelMetricsConfig": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "OutErrors": {"type": "string", "pattern": "^[\\s\\S]*$"}, "OutputType": {"type": "string", "enum": ["SDL", "JSON"]}, "OwnerContact": {"type": "string", "max": 250, "min": 0, "pattern": "[A-Za-z0-9_\\-\\ \\.]+"}, "Ownership": {"type": "string", "enum": ["CURRENT_ACCOUNT", "OTHER_ACCOUNTS"]}, "PaginationToken": {"type": "string", "max": 65536, "min": 1, "pattern": "[\\S]+"}, "PipelineConfig": {"type": "structure", "members": {"functions": {"shape": "FunctionsIds", "documentation": "<p>A list of <code>Function</code> objects.</p>"}}, "documentation": "<p>The pipeline configuration for a resolver of kind <code>PIPELINE</code>.</p>"}, "PutGraphqlApiEnvironmentVariablesRequest": {"type": "structure", "required": ["apiId", "environmentVariables"], "members": {"apiId": {"shape": "String", "documentation": "<p>The ID of the API to which the environmental variable list will be written.</p>", "location": "uri", "locationName": "apiId"}, "environmentVariables": {"shape": "EnvironmentVariableMap", "documentation": "<p>The list of environmental variables to add to the API.</p> <p>When creating an environmental variable key-value pair, it must follow the additional constraints below:</p> <ul> <li> <p>Keys must begin with a letter.</p> </li> <li> <p>Keys must be at least two characters long.</p> </li> <li> <p>Keys can only contain letters, numbers, and the underscore character (_).</p> </li> <li> <p>Values can be up to 512 characters long.</p> </li> <li> <p>You can configure up to 50 key-value pairs in a GraphQL API.</p> </li> </ul> <p>You can create a list of environmental variables by adding it to the <code>environmentVariables</code> payload as a list in the format <code>{\"key1\":\"value1\",\"key2\":\"value2\", …}</code>. Note that each call of the <code>PutGraphqlApiEnvironmentVariables</code> action will result in the overwriting of the existing environmental variable list of that API. This means the existing environmental variables will be lost. To avoid this, you must include all existing and new environmental variables in the list each time you call this action.</p>"}}}, "PutGraphqlApiEnvironmentVariablesResponse": {"type": "structure", "members": {"environmentVariables": {"shape": "EnvironmentVariableMap", "documentation": "<p>The payload containing each environmental variable in the <code>\"key\" : \"value\"</code> format.</p>"}}}, "QueryDepthLimit": {"type": "integer", "max": 75, "min": 0}, "RdsDataApiConfig": {"type": "structure", "required": ["resourceArn", "secretArn", "databaseName"], "members": {"resourceArn": {"shape": "RdsDataApiConfigResourceArn", "documentation": "<p>The resource ARN of the RDS cluster.</p>"}, "secretArn": {"shape": "RdsDataApiConfigSecretArn", "documentation": "<p>The secret's ARN that was obtained from Secrets Manager. A secret consists of secret information, the secret value, plus metadata about the secret. A secret value can be a string or binary. It typically includes the ARN, secret name and description, policies, tags, encryption key from the Key Management Service, and key rotation data.</p>"}, "databaseName": {"shape": "RdsDataApiConfigDatabaseName", "documentation": "<p>The name of the database in the cluster.</p>"}}, "documentation": "<p>Contains the metadata required to introspect the RDS cluster.</p>"}, "RdsDataApiConfigDatabaseName": {"type": "string", "max": 128, "min": 1}, "RdsDataApiConfigResourceArn": {"type": "string", "max": 2048, "min": 20, "pattern": "^arn:[a-z-]*:rds:[a-z0-9-]*:\\d{12}:cluster:[0-9A-Za-z_/-]*$"}, "RdsDataApiConfigSecretArn": {"type": "string", "max": 2048, "min": 20, "pattern": "^arn:[a-z-]*:secretsmanager:[a-z0-9-]*:\\d{12}:secret:[0-9A-Za-z_/+=.@!-]*$"}, "RdsHttpEndpointConfig": {"type": "structure", "members": {"awsRegion": {"shape": "String", "documentation": "<p>Amazon Web Services Region for Amazon RDS HTTP endpoint.</p>"}, "dbClusterIdentifier": {"shape": "String", "documentation": "<p>Amazon RDS cluster Amazon Resource Name (ARN).</p>"}, "databaseName": {"shape": "String", "documentation": "<p>Logical database name.</p>"}, "schema": {"shape": "String", "documentation": "<p>Logical schema name.</p>"}, "awsSecretStoreArn": {"shape": "String", "documentation": "<p>Amazon Web Services secret store Amazon Resource Name (ARN) for database credentials.</p>"}}, "documentation": "<p>The Amazon Relational Database Service (Amazon RDS) HTTP endpoint configuration.</p>"}, "RelationalDatabaseDataSourceConfig": {"type": "structure", "members": {"relationalDatabaseSourceType": {"shape": "RelationalDatabaseSourceType", "documentation": "<p>Source type for the relational database.</p> <ul> <li> <p> <b>RDS_HTTP_ENDPOINT</b>: The relational database source type is an Amazon Relational Database Service (Amazon RDS) HTTP endpoint.</p> </li> </ul>"}, "rdsHttpEndpointConfig": {"shape": "RdsHttpEndpointConfig", "documentation": "<p>Amazon RDS HTTP endpoint settings.</p>"}}, "documentation": "<p>Describes a relational database data source configuration.</p>"}, "RelationalDatabaseSourceType": {"type": "string", "enum": ["RDS_HTTP_ENDPOINT"]}, "Resolver": {"type": "structure", "members": {"typeName": {"shape": "ResourceName", "documentation": "<p>The resolver type name.</p>"}, "fieldName": {"shape": "ResourceName", "documentation": "<p>The resolver field name.</p>"}, "dataSourceName": {"shape": "ResourceName", "documentation": "<p>The resolver data source name.</p>"}, "resolverArn": {"shape": "String", "documentation": "<p>The resolver Amazon Resource Name (ARN).</p>"}, "requestMappingTemplate": {"shape": "MappingTemplate", "documentation": "<p>The request mapping template.</p>"}, "responseMappingTemplate": {"shape": "MappingTemplate", "documentation": "<p>The response mapping template.</p>"}, "kind": {"shape": "ResolverKind", "documentation": "<p>The resolver type.</p> <ul> <li> <p> <b>UNIT</b>: A UNIT resolver type. A UNIT resolver is the default resolver type. You can use a UNIT resolver to run a GraphQL query against a single data source.</p> </li> <li> <p> <b>PIPELINE</b>: A PIPELINE resolver type. You can use a PIPELINE resolver to invoke a series of <code>Function</code> objects in a serial manner. You can use a pipeline resolver to run a GraphQL query against multiple data sources.</p> </li> </ul>"}, "pipelineConfig": {"shape": "PipelineConfig", "documentation": "<p>The <code>PipelineConfig</code>.</p>"}, "syncConfig": {"shape": "SyncConfig", "documentation": "<p>The <code>SyncConfig</code> for a resolver attached to a versioned data source.</p>"}, "cachingConfig": {"shape": "CachingConfig", "documentation": "<p>The caching configuration for the resolver.</p>"}, "maxBatchSize": {"shape": "MaxBatchSize", "documentation": "<p>The maximum batching size for a resolver.</p>"}, "runtime": {"shape": "AppSyncRuntime"}, "code": {"shape": "Code", "documentation": "<p>The <code>resolver</code> code that contains the request and response functions. When code is used, the <code>runtime</code> is required. The <code>runtime</code> value must be <code>APPSYNC_JS</code>.</p>"}, "metricsConfig": {"shape": "ResolverLevelMetricsConfig", "documentation": "<p>Enables or disables enhanced resolver metrics for specified resolvers. Note that <code>metricsConfig</code> won't be used unless the <code>resolverLevelMetricsBehavior</code> value is set to <code>PER_RESOLVER_METRICS</code>. If the <code>resolverLevelMetricsBehavior</code> is set to <code>FULL_REQUEST_RESOLVER_METRICS</code> instead, <code>metricsConfig</code> will be ignored. However, you can still set its value.</p> <p> <code>metricsConfig</code> can be <code>ENABLED</code> or <code>DISABLED</code>.</p>"}}, "documentation": "<p>Describes a resolver.</p>"}, "ResolverCountLimit": {"type": "integer", "max": 10000, "min": 0}, "ResolverKind": {"type": "string", "enum": ["UNIT", "PIPELINE"]}, "ResolverLevelMetricsBehavior": {"type": "string", "enum": ["FULL_REQUEST_RESOLVER_METRICS", "PER_RESOLVER_METRICS"]}, "ResolverLevelMetricsConfig": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "Resolvers": {"type": "list", "member": {"shape": "Resolver"}}, "ResourceArn": {"type": "string", "max": 75, "min": 70, "pattern": "^arn:aws:appsync:[A-Za-z0-9_/.-]{0,63}:\\d{12}:apis/[0-9A-Za-z_-]{26}$"}, "ResourceName": {"type": "string", "max": 65536, "min": 1, "pattern": "[_A-Za-z][_0-9A-Za-z]*"}, "RuntimeName": {"type": "string", "enum": ["APPSYNC_JS"]}, "SchemaStatus": {"type": "string", "enum": ["PROCESSING", "ACTIVE", "DELETING", "FAILED", "SUCCESS", "NOT_APPLICABLE"]}, "ServiceQuotaExceededException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>The operation exceeded the service quota for this resource.</p>", "error": {"httpStatusCode": 402}, "exception": true}, "SourceApiAssociation": {"type": "structure", "members": {"associationId": {"shape": "String", "documentation": "<p>The ID generated by the AppSync service for the source API association.</p>"}, "associationArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the source API association.</p>"}, "sourceApiId": {"shape": "String", "documentation": "<p>The ID of the AppSync source API.</p>"}, "sourceApiArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the AppSync source API.</p>"}, "mergedApiArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the AppSync Merged API.</p>"}, "mergedApiId": {"shape": "String", "documentation": "<p>The ID of the AppSync Merged API.</p>"}, "description": {"shape": "String", "documentation": "<p>The description field.</p>"}, "sourceApiAssociationConfig": {"shape": "SourceApiAssociationConfig", "documentation": "<p>The <code>SourceApiAssociationConfig</code> object data.</p>"}, "sourceApiAssociationStatus": {"shape": "SourceApiAssociationStatus", "documentation": "<p>The state of the source API association.</p>"}, "sourceApiAssociationStatusDetail": {"shape": "String", "documentation": "<p>The detailed message related to the current state of the source API association.</p>"}, "lastSuccessfulMergeDate": {"shape": "Date", "documentation": "<p>The datetime value of the last successful merge of the source API association. The result will be in UTC format and your local time zone.</p>"}}, "documentation": "<p>Describes the configuration of a source API. A source API is a GraphQL API that is linked to a merged API. There can be multiple source APIs attached to each merged API. When linked to a merged API, the source API's schema, data sources, and resolvers will be combined with other linked source API data to form a new, singular API. </p> <p>Source APIs can originate from your account or from other accounts via Amazon Web Services Resource Access Manager. For more information about sharing resources from other accounts, see <a href=\"https://docs.aws.amazon.com/ram/latest/userguide/what-is.html\">What is Amazon Web Services Resource Access Manager?</a> in the <i>Amazon Web Services Resource Access Manager</i> guide.</p>"}, "SourceApiAssociationConfig": {"type": "structure", "members": {"mergeType": {"shape": "MergeType", "documentation": "<p>The property that indicates which merging option is enabled in the source API association.</p> <p>Valid merge types are <code>MANUAL_MERGE</code> (default) and <code>AUTO_MERGE</code>. Manual merges are the default behavior and require the user to trigger any changes from the source APIs to the merged API manually. Auto merges subscribe the merged API to the changes performed on the source APIs so that any change in the source APIs are also made to the merged API. Auto merges use <code>MergedApiExecutionRoleArn</code> to perform merge operations.</p>"}}, "documentation": "<p>Describes properties used to specify configurations related to a source API.</p>"}, "SourceApiAssociationStatus": {"type": "string", "enum": ["MERGE_SCHEDULED", "MERGE_FAILED", "MERGE_SUCCESS", "MERGE_IN_PROGRESS", "AUTO_MERGE_SCHEDULE_FAILED", "DELETION_SCHEDULED", "DELETION_IN_PROGRESS", "DELETION_FAILED"]}, "SourceApiAssociationSummary": {"type": "structure", "members": {"associationId": {"shape": "String", "documentation": "<p>The ID generated by the AppSync service for the source API association.</p>"}, "associationArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the source API association.</p>"}, "sourceApiId": {"shape": "String", "documentation": "<p>The ID of the AppSync source API.</p>"}, "sourceApiArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the AppSync Source API.</p>"}, "mergedApiId": {"shape": "String", "documentation": "<p>The ID of the AppSync Merged API.</p>"}, "mergedApiArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the AppSync Merged API.</p>"}, "description": {"shape": "String", "documentation": "<p>The description field.</p>"}}, "documentation": "<p>Describes the ARNs and IDs of associations, Merged APIs, and source APIs.</p>"}, "SourceApiAssociationSummaryList": {"type": "list", "member": {"shape": "SourceApiAssociationSummary"}}, "StartDataSourceIntrospectionRequest": {"type": "structure", "members": {"rdsDataApiConfig": {"shape": "RdsDataApiConfig", "documentation": "<p>The <code>rdsDataApiConfig</code> object data.</p>"}}}, "StartDataSourceIntrospectionResponse": {"type": "structure", "members": {"introspectionId": {"shape": "String", "documentation": "<p>The introspection ID. Each introspection contains a unique ID that can be used to reference the instrospection record.</p>"}, "introspectionStatus": {"shape": "DataSourceIntrospectionStatus", "documentation": "<p>The status of the introspection during creation. By default, when a new instrospection has been created, the status will be set to <code>PROCESSING</code>. Once the operation has been completed, the status will change to <code>SUCCESS</code> or <code>FAILED</code> depending on how the data was parsed. A <code>FAILED</code> operation will return an error and its details as an <code>introspectionStatusDetail</code>.</p>"}, "introspectionStatusDetail": {"shape": "String", "documentation": "<p>The error detail field. When a <code>FAILED</code> <code>introspectionStatus</code> is returned, the <code>introspectionStatusDetail</code> will also return the exact error that was generated during the operation.</p>"}}}, "StartSchemaCreationRequest": {"type": "structure", "required": ["apiId", "definition"], "members": {"apiId": {"shape": "String", "documentation": "<p>The API ID.</p>", "location": "uri", "locationName": "apiId"}, "definition": {"shape": "Blob", "documentation": "<p>The schema definition, in GraphQL schema language format.</p>"}}}, "StartSchemaCreationResponse": {"type": "structure", "members": {"status": {"shape": "SchemaStatus", "documentation": "<p>The current state of the schema (PROCESSING, FAILED, SUCCESS, or NOT_APPLICABLE). When the schema is in the ACTIVE state, you can add data.</p>"}}}, "StartSchemaMergeRequest": {"type": "structure", "required": ["associationId", "mergedApiIdentifier"], "members": {"associationId": {"shape": "String", "documentation": "<p>The ID generated by the AppSync service for the source API association.</p>", "location": "uri", "locationName": "associationId"}, "mergedApiIdentifier": {"shape": "String", "documentation": "<p>The identifier of the AppSync Merged API. This is generated by the AppSync service. In most cases, Merged APIs (especially in your account) only require the API ID value or ARN of the merged API. However, Merged APIs in other accounts (cross-account use cases) strictly require the full resource ARN of the merged API.</p>", "location": "uri", "locationName": "mergedApiIdentifier"}}}, "StartSchemaMergeResponse": {"type": "structure", "members": {"sourceApiAssociationStatus": {"shape": "SourceApiAssociationStatus", "documentation": "<p>The state of the source API association.</p>"}}}, "Stash": {"type": "string", "pattern": "^[\\s\\S]*$"}, "String": {"type": "string"}, "SyncConfig": {"type": "structure", "members": {"conflictHandler": {"shape": "ConflictHandlerType", "documentation": "<p>The Conflict Resolution strategy to perform in the event of a conflict.</p> <ul> <li> <p> <b>OPTIMISTIC_CONCURRENCY</b>: Resolve conflicts by rejecting mutations when versions don't match the latest version at the server.</p> </li> <li> <p> <b>AUTOMERGE</b>: Resolve conflicts with the Automerge conflict resolution strategy.</p> </li> <li> <p> <b>LAMBDA</b>: Resolve conflicts with an Lambda function supplied in the <code>LambdaConflictHandlerConfig</code>.</p> </li> </ul>"}, "conflictDetection": {"shape": "ConflictDetectionType", "documentation": "<p>The Conflict Detection strategy to use.</p> <ul> <li> <p> <b>VERSION</b>: Detect conflicts based on object versions for this resolver.</p> </li> <li> <p> <b>NONE</b>: Do not detect conflicts when invoking this resolver.</p> </li> </ul>"}, "lambdaConflictHandlerConfig": {"shape": "LambdaConflictHandlerConfig", "documentation": "<p>The <code>LambdaConflictHandlerConfig</code> when configuring <code>LAMBDA</code> as the Conflict Handler.</p>"}}, "documentation": "<p>Describes a Sync configuration for a resolver.</p> <p>Specifies which Conflict Detection strategy and Resolution strategy to use when the resolver is invoked.</p>"}, "TTL": {"type": "integer", "max": 3600, "min": 0}, "TagKey": {"type": "string", "documentation": "<p>The key for the tag.</p>", "max": 128, "min": 1, "pattern": "^(?!aws:)[ a-zA-Z+-=._:/]+$"}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 50, "min": 1}, "TagMap": {"type": "map", "key": {"shape": "TagKey"}, "value": {"shape": "TagValue"}, "documentation": "<p>A map with keys of <code>TagKey</code> objects and values of <code>TagValue</code> objects.</p>", "max": 50, "min": 1}, "TagResourceRequest": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {"shape": "ResourceArn", "documentation": "<p>The <code>GraphqlApi</code> Amazon Resource Name (ARN).</p>", "location": "uri", "locationName": "resourceArn"}, "tags": {"shape": "TagMap", "documentation": "<p>A <code>TagMap</code> object.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "documentation": "<p>The value for the tag.</p>", "max": 256, "pattern": "^[\\s\\w+-=\\.:/@]*$"}, "Template": {"type": "string", "max": 65536, "min": 2, "pattern": "^[\\s\\S]*$"}, "Timestamp": {"type": "timestamp"}, "Type": {"type": "structure", "members": {"name": {"shape": "ResourceName", "documentation": "<p>The type name.</p>"}, "description": {"shape": "String", "documentation": "<p>The type description.</p>"}, "arn": {"shape": "String", "documentation": "<p>The type Amazon Resource Name (ARN).</p>"}, "definition": {"shape": "String", "documentation": "<p>The type definition.</p>"}, "format": {"shape": "TypeDefinitionFormat", "documentation": "<p>The type format: SDL or JSON.</p>"}}, "documentation": "<p>Describes a type.</p>"}, "TypeDefinitionFormat": {"type": "string", "enum": ["SDL", "JSON"]}, "TypeList": {"type": "list", "member": {"shape": "Type"}}, "UnauthorizedException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>You aren't authorized to perform this operation.</p>", "error": {"httpStatusCode": 401}, "exception": true}, "UntagResourceRequest": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {"shape": "ResourceArn", "documentation": "<p>The <code>GraphqlApi</code> Amazon Resource Name (ARN).</p>", "location": "uri", "locationName": "resourceArn"}, "tagKeys": {"shape": "TagKeyList", "documentation": "<p>A list of <code>TagKey</code> objects.</p>", "location": "querystring", "locationName": "tagKeys"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateApiCacheRequest": {"type": "structure", "required": ["apiId", "ttl", "apiCachingBehavior", "type"], "members": {"apiId": {"shape": "String", "documentation": "<p>The GraphQL API ID.</p>", "location": "uri", "locationName": "apiId"}, "ttl": {"shape": "<PERSON>", "documentation": "<p>TTL in seconds for cache entries.</p> <p>Valid values are 1–3,600 seconds.</p>"}, "apiCachingBehavior": {"shape": "ApiCachingBehavior", "documentation": "<p>Caching behavior.</p> <ul> <li> <p> <b>FULL_REQUEST_CACHING</b>: All requests from the same user are cached. Individual resolvers are automatically cached. All API calls will try to return responses from the cache.</p> </li> <li> <p> <b>PER_RESOLVER_CACHING</b>: Individual resolvers that you specify are cached.</p> </li> <li> <p> <b>OPERATION_LEVEL_CACHING</b>: Full requests are cached together and returned without executing resolvers.</p> </li> </ul>"}, "type": {"shape": "ApiCacheType", "documentation": "<p>The cache instance type. Valid values are </p> <ul> <li> <p> <code>SMALL</code> </p> </li> <li> <p> <code>MEDIUM</code> </p> </li> <li> <p> <code>LARGE</code> </p> </li> <li> <p> <code>XLARGE</code> </p> </li> <li> <p> <code>LARGE_2X</code> </p> </li> <li> <p> <code>LARGE_4X</code> </p> </li> <li> <p> <code>LARGE_8X</code> (not available in all regions)</p> </li> <li> <p> <code>LARGE_12X</code> </p> </li> </ul> <p>Historically, instance types were identified by an EC2-style value. As of July 2020, this is deprecated, and the generic identifiers above should be used.</p> <p>The following legacy instance types are available, but their use is discouraged:</p> <ul> <li> <p> <b>T2_SMALL</b>: A t2.small instance type.</p> </li> <li> <p> <b>T2_MEDIUM</b>: A t2.medium instance type.</p> </li> <li> <p> <b>R4_LARGE</b>: A r4.large instance type.</p> </li> <li> <p> <b>R4_XLARGE</b>: A r4.xlarge instance type.</p> </li> <li> <p> <b>R4_2XLARGE</b>: A r4.2xlarge instance type.</p> </li> <li> <p> <b>R4_4XLARGE</b>: A r4.4xlarge instance type.</p> </li> <li> <p> <b>R4_8XLARGE</b>: A r4.8xlarge instance type.</p> </li> </ul>"}, "healthMetricsConfig": {"shape": "CacheHealthMetricsConfig", "documentation": "<p>Controls how cache health metrics will be emitted to CloudWatch. Cache health metrics include:</p> <ul> <li> <p>NetworkBandwidthOutAllowanceExceeded: The network packets dropped because the throughput exceeded the aggregated bandwidth limit. This is useful for diagnosing bottlenecks in a cache configuration.</p> </li> <li> <p>EngineCPUUtilization: The CPU utilization (percentage) allocated to the Redis process. This is useful for diagnosing bottlenecks in a cache configuration.</p> </li> </ul> <p>Metrics will be recorded by API ID. You can set the value to <code>ENABLED</code> or <code>DISABLED</code>.</p>"}}, "documentation": "<p>Represents the input of a <code>UpdateApiCache</code> operation.</p>"}, "UpdateApiCacheResponse": {"type": "structure", "members": {"apiCache": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The <code>ApiCache</code> object.</p>"}}, "documentation": "<p>Represents the output of a <code>UpdateApiCache</code> operation.</p>"}, "UpdateApiKeyRequest": {"type": "structure", "required": ["apiId", "id"], "members": {"apiId": {"shape": "String", "documentation": "<p>The ID for the GraphQL API.</p>", "location": "uri", "locationName": "apiId"}, "id": {"shape": "String", "documentation": "<p>The API key ID.</p>", "location": "uri", "locationName": "id"}, "description": {"shape": "String", "documentation": "<p>A description of the purpose of the API key.</p>"}, "expires": {"shape": "<PERSON>", "documentation": "<p>From the update time, the time after which the API key expires. The date is represented as seconds since the epoch. For more information, see .</p>"}}}, "UpdateApiKeyResponse": {"type": "structure", "members": {"apiKey": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The API key.</p>"}}}, "UpdateApiRequest": {"type": "structure", "required": ["apiId", "name"], "members": {"apiId": {"shape": "String", "documentation": "<p>The <code>Api</code> ID.</p>", "location": "uri", "locationName": "apiId"}, "name": {"shape": "ApiName", "documentation": "<p>The name of the Api.</p>"}, "ownerContact": {"shape": "String", "documentation": "<p>The owner contact information for the <code>Api</code>.</p>"}, "eventConfig": {"shape": "EventConfig", "documentation": "<p>The new event configuration. This includes the default authorization configuration for connecting, publishing, and subscribing to an Event API.</p>"}}}, "UpdateApiResponse": {"type": "structure", "members": {"api": {"shape": "Api", "documentation": "<p>The <code>Api</code> object.</p>"}}}, "UpdateChannelNamespaceRequest": {"type": "structure", "required": ["apiId", "name"], "members": {"apiId": {"shape": "String", "documentation": "<p>The <code>Api</code> ID.</p>", "location": "uri", "locationName": "apiId"}, "name": {"shape": "Namespace", "documentation": "<p>The name of the <code>ChannelNamespace</code>.</p>", "location": "uri", "locationName": "name"}, "subscribeAuthModes": {"shape": "AuthModes", "documentation": "<p>The authorization mode to use for subscribing to messages on the channel namespace. This configuration overrides the default <code>Api</code> authorization configuration.</p>"}, "publishAuthModes": {"shape": "AuthModes", "documentation": "<p>The authorization mode to use for publishing messages on the channel namespace. This configuration overrides the default <code>Api</code> authorization configuration.</p>"}, "codeHandlers": {"shape": "Code", "documentation": "<p>The event handler functions that run custom business logic to process published events and subscribe requests.</p>"}, "handlerConfigs": {"shape": "HandlerConfigs", "documentation": "<p>The configuration for the <code>OnPublish</code> and <code>OnSubscribe</code> handlers.</p>"}}}, "UpdateChannelNamespaceResponse": {"type": "structure", "members": {"channelNamespace": {"shape": "ChannelNamespace", "documentation": "<p>The <code>ChannelNamespace</code> object.</p>"}}}, "UpdateDataSourceRequest": {"type": "structure", "required": ["apiId", "name", "type"], "members": {"apiId": {"shape": "String", "documentation": "<p>The API ID.</p>", "location": "uri", "locationName": "apiId"}, "name": {"shape": "ResourceName", "documentation": "<p>The new name for the data source.</p>", "location": "uri", "locationName": "name"}, "description": {"shape": "String", "documentation": "<p>The new description for the data source.</p>"}, "type": {"shape": "DataSourceType", "documentation": "<p>The new data source type.</p>"}, "serviceRoleArn": {"shape": "String", "documentation": "<p>The new service role Amazon Resource Name (ARN) for the data source.</p>"}, "dynamodbConfig": {"shape": "DynamodbDataSourceConfig", "documentation": "<p>The new Amazon DynamoDB configuration.</p>"}, "lambdaConfig": {"shape": "LambdaDataSourceConfig", "documentation": "<p>The new Lambda configuration.</p>"}, "elasticsearchConfig": {"shape": "ElasticsearchDataSourceConfig", "documentation": "<p>The new OpenSearch configuration.</p> <p>As of September 2021, Amazon Elasticsearch service is Amazon OpenSearch Service. This configuration is deprecated. Instead, use <a>UpdateDataSourceRequest$openSearchServiceConfig</a> to update an OpenSearch data source.</p>"}, "openSearchServiceConfig": {"shape": "OpenSearchServiceDataSourceConfig", "documentation": "<p>The new OpenSearch configuration.</p>"}, "httpConfig": {"shape": "HttpDataSourceConfig", "documentation": "<p>The new HTTP endpoint configuration.</p>"}, "relationalDatabaseConfig": {"shape": "RelationalDatabaseDataSourceConfig", "documentation": "<p>The new relational database configuration.</p>"}, "eventBridgeConfig": {"shape": "EventBridgeDataSourceConfig", "documentation": "<p>The new Amazon EventBridge settings.</p>"}, "metricsConfig": {"shape": "DataSourceLevelMetricsConfig", "documentation": "<p>Enables or disables enhanced data source metrics for specified data sources. Note that <code>metricsConfig</code> won't be used unless the <code>dataSourceLevelMetricsBehavior</code> value is set to <code>PER_DATA_SOURCE_METRICS</code>. If the <code>dataSourceLevelMetricsBehavior</code> is set to <code>FULL_REQUEST_DATA_SOURCE_METRICS</code> instead, <code>metricsConfig</code> will be ignored. However, you can still set its value.</p> <p> <code>metricsConfig</code> can be <code>ENABLED</code> or <code>DISABLED</code>.</p>"}}}, "UpdateDataSourceResponse": {"type": "structure", "members": {"dataSource": {"shape": "DataSource", "documentation": "<p>The updated <code>DataSource</code> object.</p>"}}}, "UpdateDomainNameRequest": {"type": "structure", "required": ["domainName"], "members": {"domainName": {"shape": "DomainName", "documentation": "<p>The domain name.</p>", "location": "uri", "locationName": "domainName"}, "description": {"shape": "Description", "documentation": "<p>A description of the <code>DomainName</code>.</p>"}}}, "UpdateDomainNameResponse": {"type": "structure", "members": {"domainNameConfig": {"shape": "DomainNameConfig", "documentation": "<p>The configuration for the <code>DomainName</code>.</p>"}}}, "UpdateFunctionRequest": {"type": "structure", "required": ["apiId", "name", "functionId", "dataSourceName"], "members": {"apiId": {"shape": "String", "documentation": "<p>The GraphQL API ID.</p>", "location": "uri", "locationName": "apiId"}, "name": {"shape": "ResourceName", "documentation": "<p>The <code>Function</code> name.</p>"}, "description": {"shape": "String", "documentation": "<p>The <code>Function</code> description.</p>"}, "functionId": {"shape": "ResourceName", "documentation": "<p>The function ID.</p>", "location": "uri", "locationName": "functionId"}, "dataSourceName": {"shape": "ResourceName", "documentation": "<p>The <code>Function</code> <code>DataSource</code> name.</p>"}, "requestMappingTemplate": {"shape": "MappingTemplate", "documentation": "<p>The <code>Function</code> request mapping template. Functions support only the 2018-05-29 version of the request mapping template.</p>"}, "responseMappingTemplate": {"shape": "MappingTemplate", "documentation": "<p>The <code>Function</code> request mapping template.</p>"}, "functionVersion": {"shape": "String", "documentation": "<p>The <code>version</code> of the request mapping template. Currently, the supported value is 2018-05-29. Note that when using VTL and mapping templates, the <code>functionVersion</code> is required.</p>"}, "syncConfig": {"shape": "SyncConfig"}, "maxBatchSize": {"shape": "MaxBatchSize", "documentation": "<p>The maximum batching size for a resolver.</p>"}, "runtime": {"shape": "AppSyncRuntime"}, "code": {"shape": "Code", "documentation": "<p>The <code>function</code> code that contains the request and response functions. When code is used, the <code>runtime</code> is required. The <code>runtime</code> value must be <code>APPSYNC_JS</code>.</p>"}}}, "UpdateFunctionResponse": {"type": "structure", "members": {"functionConfiguration": {"shape": "FunctionConfiguration", "documentation": "<p>The <code>Function</code> object.</p>"}}}, "UpdateGraphqlApiRequest": {"type": "structure", "required": ["apiId", "name", "authenticationType"], "members": {"apiId": {"shape": "String", "documentation": "<p>The API ID.</p>", "location": "uri", "locationName": "apiId"}, "name": {"shape": "String", "documentation": "<p>The new name for the <code>GraphqlApi</code> object.</p>"}, "logConfig": {"shape": "LogConfig", "documentation": "<p>The Amazon CloudWatch Logs configuration for the <code>GraphqlApi</code> object.</p>"}, "authenticationType": {"shape": "AuthenticationType", "documentation": "<p>The new authentication type for the <code>GraphqlApi</code> object.</p>"}, "userPoolConfig": {"shape": "UserPoolConfig", "documentation": "<p>The new Amazon Cognito user pool configuration for the <code>~GraphqlApi</code> object.</p>"}, "openIDConnectConfig": {"shape": "OpenIDConnectConfig", "documentation": "<p>The OpenID Connect configuration for the <code>GraphqlApi</code> object.</p>"}, "additionalAuthenticationProviders": {"shape": "AdditionalAuthenticationProviders", "documentation": "<p>A list of additional authentication providers for the <code>GraphqlApi</code> API.</p>"}, "xrayEnabled": {"shape": "Boolean", "documentation": "<p>A flag indicating whether to use X-Ray tracing for the <code>GraphqlApi</code>.</p>"}, "lambdaAuthorizerConfig": {"shape": "LambdaAuthorizerConfig", "documentation": "<p>Configuration for Lambda function authorization.</p>"}, "mergedApiExecutionRoleArn": {"shape": "String", "documentation": "<p>The Identity and Access Management service role ARN for a merged API. The AppSync service assumes this role on behalf of the Merged API to validate access to source APIs at runtime and to prompt the <code>AUTO_MERGE</code> to update the merged API endpoint with the source API changes automatically.</p>"}, "ownerContact": {"shape": "String", "documentation": "<p>The owner contact information for an API resource.</p> <p>This field accepts any string input with a length of 0 - 256 characters.</p>"}, "introspectionConfig": {"shape": "GraphQLApiIntrospectionConfig", "documentation": "<p>Sets the value of the GraphQL API to enable (<code>ENABLED</code>) or disable (<code>DISABLED</code>) introspection. If no value is provided, the introspection configuration will be set to <code>ENABLED</code> by default. This field will produce an error if the operation attempts to use the introspection feature while this field is disabled.</p> <p>For more information about introspection, see <a href=\"https://graphql.org/learn/introspection/\">GraphQL introspection</a>.</p>"}, "queryDepthLimit": {"shape": "QueryDepthLimit", "documentation": "<p>The maximum depth a query can have in a single request. Depth refers to the amount of nested levels allowed in the body of query. The default value is <code>0</code> (or unspecified), which indicates there's no depth limit. If you set a limit, it can be between <code>1</code> and <code>75</code> nested levels. This field will produce a limit error if the operation falls out of bounds.</p> <p>Note that fields can still be set to nullable or non-nullable. If a non-nullable field produces an error, the error will be thrown upwards to the first nullable field available.</p>"}, "resolverCountLimit": {"shape": "ResolverCountLimit", "documentation": "<p>The maximum number of resolvers that can be invoked in a single request. The default value is <code>0</code> (or unspecified), which will set the limit to <code>10000</code>. When specified, the limit value can be between <code>1</code> and <code>10000</code>. This field will produce a limit error if the operation falls out of bounds.</p>"}, "enhancedMetricsConfig": {"shape": "EnhancedMetricsConfig", "documentation": "<p>The <code>enhancedMetricsConfig</code> object.</p>"}}}, "UpdateGraphqlApiResponse": {"type": "structure", "members": {"graphqlApi": {"shape": "GraphqlApi", "documentation": "<p>The updated <code>GraphqlApi</code> object.</p>"}}}, "UpdateResolverRequest": {"type": "structure", "required": ["apiId", "typeName", "fieldName"], "members": {"apiId": {"shape": "String", "documentation": "<p>The API ID.</p>", "location": "uri", "locationName": "apiId"}, "typeName": {"shape": "ResourceName", "documentation": "<p>The new type name.</p>", "location": "uri", "locationName": "typeName"}, "fieldName": {"shape": "ResourceName", "documentation": "<p>The new field name.</p>", "location": "uri", "locationName": "fieldName"}, "dataSourceName": {"shape": "ResourceName", "documentation": "<p>The new data source name.</p>"}, "requestMappingTemplate": {"shape": "MappingTemplate", "documentation": "<p>The new request mapping template.</p> <p>A resolver uses a request mapping template to convert a GraphQL expression into a format that a data source can understand. Mapping templates are written in Apache Velocity Template Language (VTL).</p> <p>VTL request mapping templates are optional when using an Lambda data source. For all other data sources, VTL request and response mapping templates are required.</p>"}, "responseMappingTemplate": {"shape": "MappingTemplate", "documentation": "<p>The new response mapping template.</p>"}, "kind": {"shape": "ResolverKind", "documentation": "<p>The resolver type.</p> <ul> <li> <p> <b>UNIT</b>: A UNIT resolver type. A UNIT resolver is the default resolver type. You can use a UNIT resolver to run a GraphQL query against a single data source.</p> </li> <li> <p> <b>PIPELINE</b>: A PIPELINE resolver type. You can use a PIPELINE resolver to invoke a series of <code>Function</code> objects in a serial manner. You can use a pipeline resolver to run a GraphQL query against multiple data sources.</p> </li> </ul>"}, "pipelineConfig": {"shape": "PipelineConfig", "documentation": "<p>The <code>PipelineConfig</code>.</p>"}, "syncConfig": {"shape": "SyncConfig", "documentation": "<p>The <code>SyncConfig</code> for a resolver attached to a versioned data source.</p>"}, "cachingConfig": {"shape": "CachingConfig", "documentation": "<p>The caching configuration for the resolver.</p>"}, "maxBatchSize": {"shape": "MaxBatchSize", "documentation": "<p>The maximum batching size for a resolver.</p>"}, "runtime": {"shape": "AppSyncRuntime"}, "code": {"shape": "Code", "documentation": "<p>The <code>resolver</code> code that contains the request and response functions. When code is used, the <code>runtime</code> is required. The <code>runtime</code> value must be <code>APPSYNC_JS</code>.</p>"}, "metricsConfig": {"shape": "ResolverLevelMetricsConfig", "documentation": "<p>Enables or disables enhanced resolver metrics for specified resolvers. Note that <code>metricsConfig</code> won't be used unless the <code>resolverLevelMetricsBehavior</code> value is set to <code>PER_RESOLVER_METRICS</code>. If the <code>resolverLevelMetricsBehavior</code> is set to <code>FULL_REQUEST_RESOLVER_METRICS</code> instead, <code>metricsConfig</code> will be ignored. However, you can still set its value.</p> <p> <code>metricsConfig</code> can be <code>ENABLED</code> or <code>DISABLED</code>.</p>"}}}, "UpdateResolverResponse": {"type": "structure", "members": {"resolver": {"shape": "Resolver", "documentation": "<p>The updated <code>Resolver</code> object.</p>"}}}, "UpdateSourceApiAssociationRequest": {"type": "structure", "required": ["associationId", "mergedApiIdentifier"], "members": {"associationId": {"shape": "String", "documentation": "<p>The ID generated by the AppSync service for the source API association.</p>", "location": "uri", "locationName": "associationId"}, "mergedApiIdentifier": {"shape": "String", "documentation": "<p>The identifier of the AppSync Merged API. This is generated by the AppSync service. In most cases, Merged APIs (especially in your account) only require the API ID value or ARN of the merged API. However, Merged APIs in other accounts (cross-account use cases) strictly require the full resource ARN of the merged API.</p>", "location": "uri", "locationName": "mergedApiIdentifier"}, "description": {"shape": "String", "documentation": "<p>The description field.</p>"}, "sourceApiAssociationConfig": {"shape": "SourceApiAssociationConfig", "documentation": "<p>The <code>SourceApiAssociationConfig</code> object data.</p>"}}}, "UpdateSourceApiAssociationResponse": {"type": "structure", "members": {"sourceApiAssociation": {"shape": "SourceApiAssociation", "documentation": "<p>The <code>SourceApiAssociation</code> object data.</p>"}}}, "UpdateTypeRequest": {"type": "structure", "required": ["apiId", "typeName", "format"], "members": {"apiId": {"shape": "String", "documentation": "<p>The API ID.</p>", "location": "uri", "locationName": "apiId"}, "typeName": {"shape": "ResourceName", "documentation": "<p>The new type name.</p>", "location": "uri", "locationName": "typeName"}, "definition": {"shape": "String", "documentation": "<p>The new definition.</p>"}, "format": {"shape": "TypeDefinitionFormat", "documentation": "<p>The new type format: SDL or JSON.</p>"}}}, "UpdateTypeResponse": {"type": "structure", "members": {"type": {"shape": "Type", "documentation": "<p>The updated <code>Type</code> object.</p>"}}}, "UserPoolConfig": {"type": "structure", "required": ["userPoolId", "awsRegion", "defaultAction"], "members": {"userPoolId": {"shape": "String", "documentation": "<p>The user pool ID.</p>"}, "awsRegion": {"shape": "String", "documentation": "<p>The Amazon Web Services Region in which the user pool was created.</p>"}, "defaultAction": {"shape": "DefaultAction", "documentation": "<p>The action that you want your GraphQL API to take when a request that uses Amazon Cognito user pool authentication doesn't match the Amazon Cognito user pool configuration.</p>"}, "appIdClientRegex": {"shape": "String", "documentation": "<p>A regular expression for validating the incoming Amazon Cognito user pool app client ID. If this value isn't set, no filtering is applied.</p>"}}, "documentation": "<p>Describes an Amazon Cognito user pool configuration.</p>"}}, "documentation": "<p>AppSync provides API actions for creating and interacting with data sources using GraphQL from your application.</p>"}