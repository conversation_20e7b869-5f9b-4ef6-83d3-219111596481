{"version": "2.0", "metadata": {"apiVersion": "2018-01-04", "endpointPrefix": "ram", "jsonVersion": "1.1", "protocol": "rest-json", "serviceAbbreviation": "RAM", "serviceFullName": "AWS Resource Access Manager", "serviceId": "RAM", "signatureVersion": "v4", "uid": "ram-2018-01-04"}, "operations": {"AcceptResourceShareInvitation": {"name": "AcceptResourceShareInvitation", "http": {"method": "POST", "requestUri": "/acceptresourceshareinvitation"}, "input": {"shape": "AcceptResourceShareInvitationRequest"}, "output": {"shape": "AcceptResourceShareInvitationResponse"}, "errors": [{"shape": "MalformedArnException"}, {"shape": "OperationNotPermittedException"}, {"shape": "ResourceShareInvitationArnNotFoundException"}, {"shape": "ResourceShareInvitationAlreadyAcceptedException"}, {"shape": "ResourceShareInvitationAlreadyRejectedException"}, {"shape": "ResourceShareInvitationExpiredException"}, {"shape": "ServerInternalException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidClientTokenException"}, {"shape": "IdempotentParameterMismatchException"}], "documentation": "<p>Accepts an invitation to a resource share from another Amazon Web Services account. After you accept the invitation, the resources included in the resource share are available to interact with in the relevant Amazon Web Services Management Consoles and tools.</p>"}, "AssociateResourceShare": {"name": "AssociateResourceShare", "http": {"method": "POST", "requestUri": "/associateresourceshare"}, "input": {"shape": "AssociateResourceShareRequest"}, "output": {"shape": "AssociateResourceShareResponse"}, "errors": [{"shape": "IdempotentParameterMismatchException"}, {"shape": "UnknownResourceException"}, {"shape": "InvalidStateTransitionException"}, {"shape": "ResourceShareLimitExceededException"}, {"shape": "MalformedArnException"}, {"shape": "InvalidStateTransitionException"}, {"shape": "InvalidClientTokenException"}, {"shape": "InvalidParameterException"}, {"shape": "OperationNotPermittedException"}, {"shape": "ServerInternalException"}, {"shape": "ServiceUnavailableException"}, {"shape": "UnknownResourceException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Adds the specified list of principals and list of resources to a resource share. Principals that already have access to this resource share immediately receive access to the added resources. Newly added principals immediately receive access to the resources shared in this resource share. </p>"}, "AssociateResourceSharePermission": {"name": "AssociateResourceSharePermission", "http": {"method": "POST", "requestUri": "/associateresourcesharepermission"}, "input": {"shape": "AssociateResourceSharePermissionRequest"}, "output": {"shape": "AssociateResourceSharePermissionResponse"}, "errors": [{"shape": "MalformedArnException"}, {"shape": "UnknownResourceException"}, {"shape": "InvalidParameterException"}, {"shape": "InvalidClientTokenException"}, {"shape": "ServerInternalException"}, {"shape": "ServiceUnavailableException"}, {"shape": "OperationNotPermittedException"}], "documentation": "<p>Adds or replaces the RAM permission for a resource type included in a resource share. You can have exactly one permission associated with each resource type in the resource share. You can add a new RAM permission only if there are currently no resources of that resource type currently in the resource share.</p>"}, "CreatePermission": {"name": "CreatePermission", "http": {"method": "POST", "requestUri": "/createpermission"}, "input": {"shape": "CreatePermissionRequest"}, "output": {"shape": "CreatePermissionResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "InvalidPolicyException"}, {"shape": "OperationNotPermittedException"}, {"shape": "ServerInternalException"}, {"shape": "ServiceUnavailableException"}, {"shape": "PermissionAlreadyExistsException"}, {"shape": "MalformedPolicyTemplateException"}, {"shape": "InvalidClientTokenException"}, {"shape": "PermissionLimitExceededException"}, {"shape": "IdempotentParameterMismatchException"}], "documentation": "<p>Creates a customer managed permission for a specified resource type that you can attach to resource shares. It is created in the Amazon Web Services Region in which you call the operation.</p>"}, "CreatePermissionVersion": {"name": "CreatePermissionVersion", "http": {"method": "POST", "requestUri": "/createpermissionversion"}, "input": {"shape": "CreatePermissionVersionRequest"}, "output": {"shape": "CreatePermissionVersionResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "InvalidPolicyException"}, {"shape": "ServerInternalException"}, {"shape": "ServiceUnavailableException"}, {"shape": "UnknownResourceException"}, {"shape": "MalformedPolicyTemplateException"}, {"shape": "MalformedArnException"}, {"shape": "InvalidClientTokenException"}, {"shape": "IdempotentParameterMismatchException"}, {"shape": "PermissionVersionsLimitExceededException"}], "documentation": "<p>Creates a new version of the specified customer managed permission. The new version is automatically set as the default version of the customer managed permission. New resource shares automatically use the default permission. Existing resource shares continue to use their original permission versions, but you can use <a>ReplacePermissionAssociations</a> to update them.</p> <p>If the specified customer managed permission already has the maximum of 5 versions, then you must delete one of the existing versions before you can create a new one.</p>"}, "CreateResourceShare": {"name": "CreateResourceShare", "http": {"method": "POST", "requestUri": "/createresourceshare"}, "input": {"shape": "CreateResourceShareRequest"}, "output": {"shape": "CreateResourceShareResponse"}, "errors": [{"shape": "IdempotentParameterMismatchException"}, {"shape": "InvalidStateTransitionException"}, {"shape": "UnknownResourceException"}, {"shape": "MalformedArnException"}, {"shape": "InvalidClientTokenException"}, {"shape": "InvalidParameterException"}, {"shape": "OperationNotPermittedException"}, {"shape": "ResourceShareLimitExceededException"}, {"shape": "TagPolicyViolationException"}, {"shape": "TagLimitExceededException"}, {"shape": "ServerInternalException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Creates a resource share. You can provide a list of the <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs)</a> for the resources that you want to share, a list of principals you want to share the resources with, and the permissions to grant those principals.</p> <note> <p>Sharing a resource makes it available for use by principals outside of the Amazon Web Services account that created the resource. Sharing doesn't change any permissions or quotas that apply to the resource in the account that created it.</p> </note>"}, "DeletePermission": {"name": "DeletePermission", "http": {"method": "DELETE", "requestUri": "/deletepermission"}, "input": {"shape": "DeletePermissionRequest"}, "output": {"shape": "DeletePermissionResponse"}, "errors": [{"shape": "MalformedArnException"}, {"shape": "ServerInternalException"}, {"shape": "ServiceUnavailableException"}, {"shape": "OperationNotPermittedException"}, {"shape": "UnknownResourceException"}, {"shape": "InvalidClientTokenException"}, {"shape": "IdempotentParameterMismatchException"}], "documentation": "<p>Deletes the specified customer managed permission in the Amazon Web Services Region in which you call this operation. You can delete a customer managed permission only if it isn't attached to any resource share. The operation deletes all versions associated with the customer managed permission.</p>"}, "DeletePermissionVersion": {"name": "DeletePermissionVersion", "http": {"method": "DELETE", "requestUri": "/deletepermissionversion"}, "input": {"shape": "DeletePermissionVersionRequest"}, "output": {"shape": "DeletePermissionVersionResponse"}, "errors": [{"shape": "MalformedArnException"}, {"shape": "InvalidParameterException"}, {"shape": "ServerInternalException"}, {"shape": "ServiceUnavailableException"}, {"shape": "OperationNotPermittedException"}, {"shape": "UnknownResourceException"}, {"shape": "InvalidClientTokenException"}, {"shape": "IdempotentParameterMismatchException"}], "documentation": "<p>Deletes one version of a customer managed permission. The version you specify must not be attached to any resource share and must not be the default version for the permission.</p> <p>If a customer managed permission has the maximum of 5 versions, then you must delete at least one version before you can create another.</p>"}, "DeleteResourceShare": {"name": "DeleteResourceShare", "http": {"method": "DELETE", "requestUri": "/deleteresourceshare"}, "input": {"shape": "DeleteResourceShareRequest"}, "output": {"shape": "DeleteResourceShareResponse"}, "errors": [{"shape": "OperationNotPermittedException"}, {"shape": "IdempotentParameterMismatchException"}, {"shape": "InvalidStateTransitionException"}, {"shape": "UnknownResourceException"}, {"shape": "MalformedArnException"}, {"shape": "InvalidClientTokenException"}, {"shape": "InvalidParameterException"}, {"shape": "ServerInternalException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Deletes the specified resource share.</p> <important> <p>This doesn't delete any of the resources that were associated with the resource share; it only stops the sharing of those resources through this resource share.</p> </important>"}, "DisassociateResourceShare": {"name": "DisassociateResourceShare", "http": {"method": "POST", "requestUri": "/disassociateresourceshare"}, "input": {"shape": "DisassociateResourceShareRequest"}, "output": {"shape": "DisassociateResourceShareResponse"}, "errors": [{"shape": "IdempotentParameterMismatchException"}, {"shape": "ResourceShareLimitExceededException"}, {"shape": "MalformedArnException"}, {"shape": "InvalidStateTransitionException"}, {"shape": "InvalidClientTokenException"}, {"shape": "InvalidParameterException"}, {"shape": "OperationNotPermittedException"}, {"shape": "ServerInternalException"}, {"shape": "ServiceUnavailableException"}, {"shape": "UnknownResourceException"}], "documentation": "<p>Removes the specified principals or resources from participating in the specified resource share.</p>"}, "DisassociateResourceSharePermission": {"name": "DisassociateResourceSharePermission", "http": {"method": "POST", "requestUri": "/disassociateresourcesharepermission"}, "input": {"shape": "DisassociateResourceSharePermissionRequest"}, "output": {"shape": "DisassociateResourceSharePermissionResponse"}, "errors": [{"shape": "MalformedArnException"}, {"shape": "UnknownResourceException"}, {"shape": "InvalidParameterException"}, {"shape": "InvalidClientTokenException"}, {"shape": "ServerInternalException"}, {"shape": "ServiceUnavailableException"}, {"shape": "OperationNotPermittedException"}, {"shape": "InvalidStateTransitionException"}], "documentation": "<p>Removes a managed permission from a resource share. Permission changes take effect immediately. You can remove a managed permission from a resource share only if there are currently no resources of the relevant resource type currently attached to the resource share.</p>"}, "EnableSharingWithAwsOrganization": {"name": "EnableSharingWithAwsOrganization", "http": {"method": "POST", "requestUri": "/enablesharingwithawsorganization"}, "input": {"shape": "EnableSharingWithAwsOrganizationRequest"}, "output": {"shape": "EnableSharingWithAwsOrganizationResponse"}, "errors": [{"shape": "OperationNotPermittedException"}, {"shape": "ServerInternalException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Enables resource sharing within your organization in Organizations. This operation creates a service-linked role called <code>AWSServiceRoleForResourceAccessManager</code> that has the IAM managed policy named AWSResourceAccessManagerServiceRolePolicy attached. This role permits RAM to retrieve information about the organization and its structure. This lets you share resources with all of the accounts in the calling account's organization by specifying the organization ID, or all of the accounts in an organizational unit (OU) by specifying the OU ID. Until you enable sharing within the organization, you can specify only individual Amazon Web Services accounts, or for supported resource types, IAM roles and users.</p> <p>You must call this operation from an IAM role or user in the organization's management account.</p> <p/>"}, "GetPermission": {"name": "GetPermission", "http": {"method": "POST", "requestUri": "/getpermission"}, "input": {"shape": "GetPermissionRequest"}, "output": {"shape": "GetPermissionResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "MalformedArnException"}, {"shape": "UnknownResourceException"}, {"shape": "ServerInternalException"}, {"shape": "ServiceUnavailableException"}, {"shape": "OperationNotPermittedException"}], "documentation": "<p>Retrieves the contents of a managed permission in JSON format.</p>"}, "GetResourcePolicies": {"name": "GetResourcePolicies", "http": {"method": "POST", "requestUri": "/getresourcepolicies"}, "input": {"shape": "GetResourcePoliciesRequest"}, "output": {"shape": "GetResourcePoliciesResponse"}, "errors": [{"shape": "MalformedArnException"}, {"shape": "InvalidNextTokenException"}, {"shape": "InvalidParameterException"}, {"shape": "ResourceArnNotFoundException"}, {"shape": "ServerInternalException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Retrieves the resource policies for the specified resources that you own and have shared.</p>"}, "GetResourceShareAssociations": {"name": "GetResourceShareAssociations", "http": {"method": "POST", "requestUri": "/getresourceshareassociations"}, "input": {"shape": "GetResourceShareAssociationsRequest"}, "output": {"shape": "GetResourceShareAssociationsResponse"}, "errors": [{"shape": "UnknownResourceException"}, {"shape": "MalformedArnException"}, {"shape": "InvalidNextTokenException"}, {"shape": "InvalidParameterException"}, {"shape": "OperationNotPermittedException"}, {"shape": "ServerInternalException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Retrieves the lists of resources and principals that associated for resource shares that you own.</p>"}, "GetResourceShareInvitations": {"name": "GetResourceShareInvitations", "http": {"method": "POST", "requestUri": "/getresourceshareinvitations"}, "input": {"shape": "GetResourceShareInvitationsRequest"}, "output": {"shape": "GetResourceShareInvitationsResponse"}, "errors": [{"shape": "ResourceShareInvitationArnNotFoundException"}, {"shape": "InvalidMaxResultsException"}, {"shape": "MalformedArnException"}, {"shape": "UnknownResourceException"}, {"shape": "InvalidNextTokenException"}, {"shape": "InvalidParameterException"}, {"shape": "ServerInternalException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Retrieves details about invitations that you have received for resource shares.</p>"}, "GetResourceShares": {"name": "GetResourceShares", "http": {"method": "POST", "requestUri": "/getresourceshares"}, "input": {"shape": "GetResourceSharesRequest"}, "output": {"shape": "GetResourceSharesResponse"}, "errors": [{"shape": "UnknownResourceException"}, {"shape": "MalformedArnException"}, {"shape": "InvalidNextTokenException"}, {"shape": "InvalidParameterException"}, {"shape": "ServerInternalException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Retrieves details about the resource shares that you own or that are shared with you.</p>"}, "ListPendingInvitationResources": {"name": "ListPendingInvitationResources", "http": {"method": "POST", "requestUri": "/listpendinginvitationresources"}, "input": {"shape": "ListPendingInvitationResourcesRequest"}, "output": {"shape": "ListPendingInvitationResourcesResponse"}, "errors": [{"shape": "MalformedArnException"}, {"shape": "InvalidNextTokenException"}, {"shape": "InvalidParameterException"}, {"shape": "ServerInternalException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ResourceShareInvitationArnNotFoundException"}, {"shape": "MissingRequiredParameterException"}, {"shape": "ResourceShareInvitationAlreadyRejectedException"}, {"shape": "ResourceShareInvitationExpiredException"}], "documentation": "<p>Lists the resources in a resource share that is shared with you but for which the invitation is still <code>PENDING</code>. That means that you haven't accepted or rejected the invitation and the invitation hasn't expired.</p>"}, "ListPermissionAssociations": {"name": "ListPermissionAssociations", "http": {"method": "POST", "requestUri": "/listpermissionassociations"}, "input": {"shape": "ListPermissionAssociationsRequest"}, "output": {"shape": "ListPermissionAssociationsResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "MalformedArnException"}, {"shape": "InvalidNextTokenException"}, {"shape": "ServerInternalException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Lists information about the managed permission and its associations to any resource shares that use this managed permission. This lets you see which resource shares use which versions of the specified managed permission.</p>"}, "ListPermissionVersions": {"name": "ListPermissionVersions", "http": {"method": "POST", "requestUri": "/listpermissionversions"}, "input": {"shape": "ListPermissionVersionsRequest"}, "output": {"shape": "ListPermissionVersionsResponse"}, "errors": [{"shape": "MalformedArnException"}, {"shape": "UnknownResourceException"}, {"shape": "InvalidNextTokenException"}, {"shape": "ServerInternalException"}, {"shape": "ServiceUnavailableException"}, {"shape": "OperationNotPermittedException"}, {"shape": "InvalidParameterException"}], "documentation": "<p>Lists the available versions of the specified RAM permission.</p>"}, "ListPermissions": {"name": "ListPermissions", "http": {"method": "POST", "requestUri": "/listpermissions"}, "input": {"shape": "ListPermissionsRequest"}, "output": {"shape": "ListPermissionsResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "InvalidNextTokenException"}, {"shape": "ServerInternalException"}, {"shape": "ServiceUnavailableException"}, {"shape": "OperationNotPermittedException"}], "documentation": "<p>Retrieves a list of available RAM permissions that you can use for the supported resource types. </p>"}, "ListPrincipals": {"name": "ListPrincipals", "http": {"method": "POST", "requestUri": "/listprincipals"}, "input": {"shape": "ListPrincipalsRequest"}, "output": {"shape": "ListPrincipalsResponse"}, "errors": [{"shape": "MalformedArnException"}, {"shape": "UnknownResourceException"}, {"shape": "InvalidNextTokenException"}, {"shape": "InvalidParameterException"}, {"shape": "ServerInternalException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Lists the principals that you are sharing resources with or that are sharing resources with you.</p>"}, "ListReplacePermissionAssociationsWork": {"name": "ListReplacePermissionAssociationsWork", "http": {"method": "POST", "requestUri": "/listreplacepermissionassociationswork"}, "input": {"shape": "ListReplacePermissionAssociationsWorkRequest"}, "output": {"shape": "ListReplacePermissionAssociationsWorkResponse"}, "errors": [{"shape": "ServerInternalException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidNextTokenException"}, {"shape": "InvalidParameterException"}], "documentation": "<p>Retrieves the current status of the asynchronous tasks performed by RAM when you perform the <a>ReplacePermissionAssociationsWork</a> operation.</p>"}, "ListResourceSharePermissions": {"name": "ListResourceSharePermissions", "http": {"method": "POST", "requestUri": "/listresourcesharepermissions"}, "input": {"shape": "ListResourceSharePermissionsRequest"}, "output": {"shape": "ListResourceSharePermissionsResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "MalformedArnException"}, {"shape": "UnknownResourceException"}, {"shape": "InvalidNextTokenException"}, {"shape": "ServerInternalException"}, {"shape": "ServiceUnavailableException"}, {"shape": "OperationNotPermittedException"}], "documentation": "<p>Lists the RAM permissions that are associated with a resource share.</p>"}, "ListResourceTypes": {"name": "ListResourceTypes", "http": {"method": "POST", "requestUri": "/listresourcetypes"}, "input": {"shape": "ListResourceTypesRequest"}, "output": {"shape": "ListResourceTypesResponse"}, "errors": [{"shape": "InvalidNextTokenException"}, {"shape": "InvalidParameterException"}, {"shape": "ServerInternalException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Lists the resource types that can be shared by RAM.</p>"}, "ListResources": {"name": "ListResources", "http": {"method": "POST", "requestUri": "/listresources"}, "input": {"shape": "ListResourcesRequest"}, "output": {"shape": "ListResourcesResponse"}, "errors": [{"shape": "InvalidResourceTypeException"}, {"shape": "UnknownResourceException"}, {"shape": "MalformedArnException"}, {"shape": "InvalidNextTokenException"}, {"shape": "InvalidParameterException"}, {"shape": "ServerInternalException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Lists the resources that you added to a resource share or the resources that are shared with you.</p>"}, "PromotePermissionCreatedFromPolicy": {"name": "PromotePermissionCreatedFromPolicy", "http": {"method": "POST", "requestUri": "/promotepermissioncreatedfrompolicy"}, "input": {"shape": "PromotePermissionCreatedFromPolicyRequest"}, "output": {"shape": "PromotePermissionCreatedFromPolicyResponse"}, "errors": [{"shape": "MalformedArnException"}, {"shape": "OperationNotPermittedException"}, {"shape": "InvalidParameterException"}, {"shape": "MissingRequiredParameterException"}, {"shape": "ServerInternalException"}, {"shape": "ServiceUnavailableException"}, {"shape": "UnknownResourceException"}], "documentation": "<p>When you attach a resource-based policy to a resource, RAM automatically creates a resource share of <code>featureSet</code>=<code>CREATED_FROM_POLICY</code> with a managed permission that has the same IAM permissions as the original resource-based policy. However, this type of managed permission is visible to only the resource share owner, and the associated resource share can't be modified by using RAM.</p> <p>This operation creates a separate, fully manageable customer managed permission that has the same IAM permissions as the original resource-based policy. You can associate this customer managed permission to any resource shares.</p> <p>Before you use <a>PromoteResourceShareCreatedFromPolicy</a>, you should first run this operation to ensure that you have an appropriate customer managed permission that can be associated with the promoted resource share.</p> <note> <ul> <li> <p>The original <code>CREATED_FROM_POLICY</code> policy isn't deleted, and resource shares using that original policy aren't automatically updated.</p> </li> <li> <p>You can't modify a <code>CREATED_FROM_POLICY</code> resource share so you can't associate the new customer managed permission by using <code>ReplacePermsissionAssociations</code>. However, if you use <a>PromoteResourceShareCreatedFromPolicy</a>, that operation automatically associates the fully manageable customer managed permission to the newly promoted <code>STANDARD</code> resource share.</p> </li> <li> <p>After you promote a resource share, if the original <code>CREATED_FROM_POLICY</code> managed permission has no other associations to A resource share, then RAM automatically deletes it.</p> </li> </ul> </note>"}, "PromoteResourceShareCreatedFromPolicy": {"name": "PromoteResourceShareCreatedFromPolicy", "http": {"method": "POST", "requestUri": "/promoteresourcesharecreatedfrompolicy"}, "input": {"shape": "PromoteResourceShareCreatedFromPolicyRequest"}, "output": {"shape": "PromoteResourceShareCreatedFromPolicyResponse"}, "errors": [{"shape": "MalformedArnException"}, {"shape": "ResourceShareLimitExceededException"}, {"shape": "OperationNotPermittedException"}, {"shape": "InvalidParameterException"}, {"shape": "MissingRequiredParameterException"}, {"shape": "ServerInternalException"}, {"shape": "ServiceUnavailableException"}, {"shape": "UnknownResourceException"}, {"shape": "InvalidStateTransitionException"}, {"shape": "UnmatchedPolicyPermissionException"}], "documentation": "<p>When you attach a resource-based policy to a resource, RAM automatically creates a resource share of <code>featureSet</code>=<code>CREATED_FROM_POLICY</code> with a managed permission that has the same IAM permissions as the original resource-based policy. However, this type of managed permission is visible to only the resource share owner, and the associated resource share can't be modified by using RAM.</p> <p>This operation promotes the resource share to a <code>STANDARD</code> resource share that is fully manageable in RAM. When you promote a resource share, you can then manage the resource share in RAM and it becomes visible to all of the principals you shared it with.</p> <important> <p>Before you perform this operation, you should first run <a>PromotePermissionCreatedFromPolicy</a>to ensure that you have an appropriate customer managed permission that can be associated with this resource share after its is promoted. If this operation can't find a managed permission that exactly matches the existing <code>CREATED_FROM_POLICY</code> permission, then this operation fails.</p> </important>"}, "RejectResourceShareInvitation": {"name": "RejectResourceShareInvitation", "http": {"method": "POST", "requestUri": "/rejectresourceshareinvitation"}, "input": {"shape": "RejectResourceShareInvitationRequest"}, "output": {"shape": "RejectResourceShareInvitationResponse"}, "errors": [{"shape": "MalformedArnException"}, {"shape": "OperationNotPermittedException"}, {"shape": "ResourceShareInvitationArnNotFoundException"}, {"shape": "ResourceShareInvitationAlreadyAcceptedException"}, {"shape": "ResourceShareInvitationAlreadyRejectedException"}, {"shape": "ResourceShareInvitationExpiredException"}, {"shape": "ServerInternalException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidClientTokenException"}, {"shape": "IdempotentParameterMismatchException"}], "documentation": "<p>Rejects an invitation to a resource share from another Amazon Web Services account.</p>"}, "ReplacePermissionAssociations": {"name": "ReplacePermissionAssociations", "http": {"method": "POST", "requestUri": "/replacepermissionassociations"}, "input": {"shape": "ReplacePermissionAssociationsRequest"}, "output": {"shape": "ReplacePermissionAssociationsResponse"}, "errors": [{"shape": "MalformedArnException"}, {"shape": "InvalidParameterException"}, {"shape": "ServerInternalException"}, {"shape": "ServiceUnavailableException"}, {"shape": "OperationNotPermittedException"}, {"shape": "UnknownResourceException"}, {"shape": "InvalidClientTokenException"}, {"shape": "IdempotentParameterMismatchException"}], "documentation": "<p>Updates all resource shares that use a managed permission to a different managed permission. This operation always applies the default version of the target managed permission. You can optionally specify that the update applies to only resource shares that currently use a specified version. This enables you to update to the latest version, without changing the which managed permission is used.</p> <p>You can use this operation to update all of your resource shares to use the current default version of the permission by specifying the same value for the <code>fromPermissionArn</code> and <code>toPermissionArn</code> parameters.</p> <p>You can use the optional <code>fromPermissionVersion</code> parameter to update only those resources that use a specified version of the managed permission to the new managed permission.</p> <important> <p>To successfully perform this operation, you must have permission to update the resource-based policy on all affected resource types.</p> </important>"}, "SetDefaultPermissionVersion": {"name": "SetDefaultPermissionVersion", "http": {"method": "POST", "requestUri": "/setdefaultpermissionversion"}, "input": {"shape": "SetDefaultPermissionVersionRequest"}, "output": {"shape": "SetDefaultPermissionVersionResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "MalformedArnException"}, {"shape": "ServerInternalException"}, {"shape": "ServiceUnavailableException"}, {"shape": "UnknownResourceException"}, {"shape": "InvalidClientTokenException"}, {"shape": "IdempotentParameterMismatchException"}], "documentation": "<p>Designates the specified version number as the default version for the specified customer managed permission. New resource shares automatically use this new default permission. Existing resource shares continue to use their original permission version, but you can use <a>ReplacePermissionAssociations</a> to update them.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tagresource"}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "MalformedArnException"}, {"shape": "UnknownResourceException"}, {"shape": "TagLimitExceededException"}, {"shape": "ResourceArnNotFoundException"}, {"shape": "TagPolicyViolationException"}, {"shape": "ServerInternalException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Adds the specified tag keys and values to a resource share or managed permission. If you choose a resource share, the tags are attached to only the resource share, not to the resources that are in the resource share.</p> <p>The tags on a managed permission are the same for all versions of the managed permission.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "POST", "requestUri": "/untagresource"}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "UnknownResourceException"}, {"shape": "InvalidParameterException"}, {"shape": "MalformedArnException"}, {"shape": "ServerInternalException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Removes the specified tag key and value pairs from the specified resource share or managed permission.</p>"}, "UpdateResourceShare": {"name": "UpdateResourceShare", "http": {"method": "POST", "requestUri": "/updateresourceshare"}, "input": {"shape": "UpdateResourceShareRequest"}, "output": {"shape": "UpdateResourceShareResponse"}, "errors": [{"shape": "IdempotentParameterMismatchException"}, {"shape": "MissingRequiredParameterException"}, {"shape": "UnknownResourceException"}, {"shape": "MalformedArnException"}, {"shape": "InvalidClientTokenException"}, {"shape": "InvalidParameterException"}, {"shape": "OperationNotPermittedException"}, {"shape": "ServerInternalException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Modifies some of the properties of the specified resource share.</p>"}}, "shapes": {"AcceptResourceShareInvitationRequest": {"type": "structure", "required": ["resourceShareInvitationArn"], "members": {"resourceShareInvitationArn": {"shape": "String", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of the invitation that you want to accept.</p>"}, "clientToken": {"shape": "String", "documentation": "<p>Specifies a unique, case-sensitive identifier that you provide to ensure the idempotency of the request. This lets you safely retry the request without accidentally performing the same operation a second time. Passing the same value to a later call to an operation requires that you also pass the same value for all other parameters. We recommend that you use a <a href=\"https://wikipedia.org/wiki/Universally_unique_identifier\">UUID type of value.</a>.</p> <p>If you don't provide this value, then Amazon Web Services generates a random one for you.</p> <p>If you retry the operation with the same <code>ClientToken</code>, but with different parameters, the retry fails with an <code>IdempotentParameterMismatch</code> error.</p>"}}}, "AcceptResourceShareInvitationResponse": {"type": "structure", "members": {"resourceShareInvitation": {"shape": "ResourceShareInvitation", "documentation": "<p>An object that contains information about the specified invitation.</p>"}, "clientToken": {"shape": "String", "documentation": "<p>The idempotency identifier associated with this request. If you want to repeat the same operation in an idempotent manner then you must include this value in the <code>clientToken</code> request parameter of that later call. All other parameters must also have the same values that you used in the first call.</p>"}}}, "AssociateResourceSharePermissionRequest": {"type": "structure", "required": ["resourceShareArn", "permissionArn"], "members": {"resourceShareArn": {"shape": "String", "documentation": "<p>Specifies the <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of the resource share to which you want to add or replace permissions.</p>"}, "permissionArn": {"shape": "String", "documentation": "<p>Specifies the <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of the RAM permission to associate with the resource share. To find the ARN for a permission, use either the <a>ListPermissions</a> operation or go to the <a href=\"https://console.aws.amazon.com/ram/home#Permissions:\">Permissions library</a> page in the RAM console and then choose the name of the permission. The ARN is displayed on the detail page.</p>"}, "replace": {"shape": "Boolean", "documentation": "<p>Specifies whether the specified permission should replace the existing permission associated with the resource share. Use <code>true</code> to replace the current permissions. Use <code>false</code> to add the permission to a resource share that currently doesn't have a permission. The default value is <code>false</code>.</p> <note> <p>A resource share can have only one permission per resource type. If a resource share already has a permission for the specified resource type and you don't set <code>replace</code> to <code>true</code> then the operation returns an error. This helps prevent accidental overwriting of a permission.</p> </note>"}, "clientToken": {"shape": "String", "documentation": "<p>Specifies a unique, case-sensitive identifier that you provide to ensure the idempotency of the request. This lets you safely retry the request without accidentally performing the same operation a second time. Passing the same value to a later call to an operation requires that you also pass the same value for all other parameters. We recommend that you use a <a href=\"https://wikipedia.org/wiki/Universally_unique_identifier\">UUID type of value.</a>.</p> <p>If you don't provide this value, then Amazon Web Services generates a random one for you.</p> <p>If you retry the operation with the same <code>ClientToken</code>, but with different parameters, the retry fails with an <code>IdempotentParameterMismatch</code> error.</p>"}, "permissionVersion": {"shape": "Integer", "documentation": "<p>Specifies the version of the RAM permission to associate with the resource share. You can specify <i>only</i> the version that is currently set as the default version for the permission. If you also set the <code>replace</code> pararameter to <code>true</code>, then this operation updates an outdated version of the permission to the current default version.</p> <note> <p>You don't need to specify this parameter because the default behavior is to use the version that is currently set as the default version for the permission. This parameter is supported for backwards compatibility.</p> </note>"}}}, "AssociateResourceSharePermissionResponse": {"type": "structure", "members": {"returnValue": {"shape": "Boolean", "documentation": "<p>A return value of <code>true</code> indicates that the request succeeded. A value of <code>false</code> indicates that the request failed.</p>"}, "clientToken": {"shape": "String", "documentation": "<p>The idempotency identifier associated with this request. If you want to repeat the same operation in an idempotent manner then you must include this value in the <code>clientToken</code> request parameter of that later call. All other parameters must also have the same values that you used in the first call.</p>"}}}, "AssociateResourceShareRequest": {"type": "structure", "required": ["resourceShareArn"], "members": {"resourceShareArn": {"shape": "String", "documentation": "<p>Specifies the <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of the resource share that you want to add principals or resources to.</p>"}, "resourceArns": {"shape": "ResourceArnList", "documentation": "<p>Specifies a list of <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs)</a> of the resources that you want to share. This can be <code>null</code> if you want to add only principals.</p>"}, "principals": {"shape": "PrincipalArnOrIdList", "documentation": "<p>Specifies a list of principals to whom you want to the resource share. This can be <code>null</code> if you want to add only resources.</p> <p>What the principals can do with the resources in the share is determined by the RAM permissions that you associate with the resource share. See <a>AssociateResourceSharePermission</a>.</p> <p>You can include the following values:</p> <ul> <li> <p>An Amazon Web Services account ID, for example: <code>************</code> </p> </li> <li> <p>An <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of an organization in Organizations, for example: <code>organizations::************:organization/o-exampleorgid</code> </p> </li> <li> <p>An ARN of an organizational unit (OU) in Organizations, for example: <code>organizations::************:ou/o-exampleorgid/ou-examplerootid-exampleouid123</code> </p> </li> <li> <p>An ARN of an IAM role, for example: <code>iam::************:role/rolename</code> </p> </li> <li> <p>An ARN of an IAM user, for example: <code>iam::************user/username</code> </p> </li> </ul> <note> <p>Not all resource types can be shared with IAM roles and users. For more information, see <a href=\"https://docs.aws.amazon.com/ram/latest/userguide/permissions.html#permissions-rbp-supported-resource-types\">Sharing with IAM roles and users</a> in the <i>Resource Access Manager User Guide</i>.</p> </note>"}, "clientToken": {"shape": "String", "documentation": "<p>Specifies a unique, case-sensitive identifier that you provide to ensure the idempotency of the request. This lets you safely retry the request without accidentally performing the same operation a second time. Passing the same value to a later call to an operation requires that you also pass the same value for all other parameters. We recommend that you use a <a href=\"https://wikipedia.org/wiki/Universally_unique_identifier\">UUID type of value.</a>.</p> <p>If you don't provide this value, then Amazon Web Services generates a random one for you.</p> <p>If you retry the operation with the same <code>ClientToken</code>, but with different parameters, the retry fails with an <code>IdempotentParameterMismatch</code> error.</p>"}, "sources": {"shape": "SourceArnOrAccountList", "documentation": "<p>Specifies from which source accounts the service principal has access to the resources in this resource share.</p>"}}}, "AssociateResourceShareResponse": {"type": "structure", "members": {"resourceShareAssociations": {"shape": "ResourceShareAssociationList", "documentation": "<p>An array of objects that contain information about the associations.</p>"}, "clientToken": {"shape": "String", "documentation": "<p>The idempotency identifier associated with this request. If you want to repeat the same operation in an idempotent manner then you must include this value in the <code>clientToken</code> request parameter of that later call. All other parameters must also have the same values that you used in the first call.</p>"}}}, "AssociatedPermission": {"type": "structure", "members": {"arn": {"shape": "String", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of the associated managed permission.</p>"}, "permissionVersion": {"shape": "String", "documentation": "<p>The version of the permission currently associated with the resource share.</p>"}, "defaultVersion": {"shape": "Boolean", "documentation": "<p>Indicates whether the associated resource share is using the default version of the permission.</p>"}, "resourceType": {"shape": "String", "documentation": "<p>The resource type to which this permission applies.</p>"}, "status": {"shape": "String", "documentation": "<p>The current status of the association between the permission and the resource share. The following are the possible values:</p> <ul> <li> <p> <code>ATTACHABLE</code> – This permission or version can be associated with resource shares.</p> </li> <li> <p> <code>UNATTACHABLE</code> – This permission or version can't currently be associated with resource shares.</p> </li> <li> <p> <code>DELETING</code> – This permission or version is in the process of being deleted.</p> </li> <li> <p> <code>DELETED</code> – This permission or version is deleted.</p> </li> </ul>"}, "featureSet": {"shape": "PermissionFeatureSet", "documentation": "<p>Indicates what features are available for this resource share. This parameter can have one of the following values:</p> <ul> <li> <p> <b>STANDARD</b> – A resource share that supports all functionality. These resource shares are visible to all principals you share the resource share with. You can modify these resource shares in RAM using the console or APIs. This resource share might have been created by RAM, or it might have been <b>CREATED_FROM_POLICY</b> and then promoted.</p> </li> <li> <p> <b>CREATED_FROM_POLICY</b> – The customer manually shared a resource by attaching a resource-based policy. That policy did not match any existing managed permissions, so RAM created this customer managed permission automatically on the customer's behalf based on the attached policy document. This type of resource share is visible only to the Amazon Web Services account that created it. You can't modify it in RAM unless you promote it. For more information, see <a>PromoteResourceShareCreatedFromPolicy</a>.</p> </li> <li> <p> <b>PROMOTING_TO_STANDARD</b> – This resource share was originally <code>CREATED_FROM_POLICY</code>, but the customer ran the <a>PromoteResourceShareCreatedFromPolicy</a> and that operation is still in progress. This value changes to <code>STANDARD</code> when complete.</p> </li> </ul>"}, "lastUpdatedTime": {"shape": "DateTime", "documentation": "<p>The date and time when the association between the permission and the resource share was last updated.</p>"}, "resourceShareArn": {"shape": "String", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of a resource share associated with this permission.</p>"}}, "documentation": "<p>An object that describes a managed permission associated with a resource share.</p>"}, "AssociatedPermissionList": {"type": "list", "member": {"shape": "AssociatedPermission"}}, "Boolean": {"type": "boolean"}, "CreatePermissionRequest": {"type": "structure", "required": ["name", "resourceType", "policyTemplate"], "members": {"name": {"shape": "PermissionName", "documentation": "<p>Specifies the name of the customer managed permission. The name must be unique within the Amazon Web Services Region.</p>"}, "resourceType": {"shape": "String", "documentation": "<p>Specifies the name of the resource type that this customer managed permission applies to.</p> <p>The format is <code> <i>&lt;service-code&gt;</i>:<i>&lt;resource-type&gt;</i> </code> and is not case sensitive. For example, to specify an Amazon EC2 Subnet, you can use the string <code>ec2:subnet</code>. To see the list of valid values for this parameter, query the <a>ListResourceTypes</a> operation.</p>"}, "policyTemplate": {"shape": "Policy", "documentation": "<p>A string in JSON format string that contains the following elements of a resource-based policy:</p> <ul> <li> <p> <b>Effect</b>: must be set to <code>ALLOW</code>.</p> </li> <li> <p> <b>Action</b>: specifies the actions that are allowed by this customer managed permission. The list must contain only actions that are supported by the specified resource type. For a list of all actions supported by each resource type, see <a href=\"https://docs.aws.amazon.com/service-authorization/latest/reference/reference_policies_actions-resources-contextkeys.html\">Actions, resources, and condition keys for Amazon Web Services services</a> in the <i>Identity and Access Management User Guide</i>.</p> </li> <li> <p> <b>Condition</b>: (optional) specifies conditional parameters that must evaluate to true when a user attempts an action for that action to be allowed. For more information about the Condition element, see <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/reference_policies_elements_condition.html\">IAM policies: Condition element</a> in the <i>Identity and Access Management User Guide</i>.</p> </li> </ul> <p>This template can't include either the <code>Resource</code> or <code>Principal</code> elements. Those are both filled in by RAM when it instantiates the resource-based policy on each resource shared using this managed permission. The <code>Resource</code> comes from the ARN of the specific resource that you are sharing. The <code>Principal</code> comes from the list of identities added to the resource share.</p>"}, "clientToken": {"shape": "String", "documentation": "<p>Specifies a unique, case-sensitive identifier that you provide to ensure the idempotency of the request. This lets you safely retry the request without accidentally performing the same operation a second time. Passing the same value to a later call to an operation requires that you also pass the same value for all other parameters. We recommend that you use a <a href=\"https://wikipedia.org/wiki/Universally_unique_identifier\">UUID type of value.</a>.</p> <p>If you don't provide this value, then Amazon Web Services generates a random one for you.</p> <p>If you retry the operation with the same <code>ClientToken</code>, but with different parameters, the retry fails with an <code>IdempotentParameterMismatch</code> error.</p>"}, "tags": {"shape": "TagList", "documentation": "<p>Specifies a list of one or more tag key and value pairs to attach to the permission.</p>"}}}, "CreatePermissionResponse": {"type": "structure", "members": {"permission": {"shape": "ResourceSharePermissionSummary", "documentation": "<p>A structure with information about this customer managed permission.</p>"}, "clientToken": {"shape": "String", "documentation": "<p>The idempotency identifier associated with this request. If you want to repeat the same operation in an idempotent manner then you must include this value in the <code>clientToken</code> request parameter of that later call. All other parameters must also have the same values that you used in the first call.</p>"}}}, "CreatePermissionVersionRequest": {"type": "structure", "required": ["permissionArn", "policyTemplate"], "members": {"permissionArn": {"shape": "String", "documentation": "<p>Specifies the <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of the customer managed permission you're creating a new version for.</p>"}, "policyTemplate": {"shape": "Policy", "documentation": "<p>A string in JSON format string that contains the following elements of a resource-based policy:</p> <ul> <li> <p> <b>Effect</b>: must be set to <code>ALLOW</code>.</p> </li> <li> <p> <b>Action</b>: specifies the actions that are allowed by this customer managed permission. The list must contain only actions that are supported by the specified resource type. For a list of all actions supported by each resource type, see <a href=\"https://docs.aws.amazon.com/service-authorization/latest/reference/reference_policies_actions-resources-contextkeys.html\">Actions, resources, and condition keys for Amazon Web Services services</a> in the <i>Identity and Access Management User Guide</i>.</p> </li> <li> <p> <b>Condition</b>: (optional) specifies conditional parameters that must evaluate to true when a user attempts an action for that action to be allowed. For more information about the Condition element, see <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/reference_policies_elements_condition.html\">IAM policies: Condition element</a> in the <i>Identity and Access Management User Guide</i>.</p> </li> </ul> <p>This template can't include either the <code>Resource</code> or <code>Principal</code> elements. Those are both filled in by RAM when it instantiates the resource-based policy on each resource shared using this managed permission. The <code>Resource</code> comes from the ARN of the specific resource that you are sharing. The <code>Principal</code> comes from the list of identities added to the resource share.</p>"}, "clientToken": {"shape": "String", "documentation": "<p>Specifies a unique, case-sensitive identifier that you provide to ensure the idempotency of the request. This lets you safely retry the request without accidentally performing the same operation a second time. Passing the same value to a later call to an operation requires that you also pass the same value for all other parameters. We recommend that you use a <a href=\"https://wikipedia.org/wiki/Universally_unique_identifier\">UUID type of value.</a>.</p> <p>If you don't provide this value, then Amazon Web Services generates a random one for you.</p> <p>If you retry the operation with the same <code>ClientToken</code>, but with different parameters, the retry fails with an <code>IdempotentParameterMismatch</code> error.</p>"}}}, "CreatePermissionVersionResponse": {"type": "structure", "members": {"permission": {"shape": "ResourceSharePermissionDetail"}, "clientToken": {"shape": "String", "documentation": "<p>The idempotency identifier associated with this request. If you want to repeat the same operation in an idempotent manner then you must include this value in the <code>clientToken</code> request parameter of that later call. All other parameters must also have the same values that you used in the first call.</p>"}}}, "CreateResourceShareRequest": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "String", "documentation": "<p>Specifies the name of the resource share.</p>"}, "resourceArns": {"shape": "ResourceArnList", "documentation": "<p>Specifies a list of one or more ARNs of the resources to associate with the resource share.</p>"}, "principals": {"shape": "PrincipalArnOrIdList", "documentation": "<p>Specifies a list of one or more principals to associate with the resource share.</p> <p>You can include the following values:</p> <ul> <li> <p>An Amazon Web Services account ID, for example: <code>************</code> </p> </li> <li> <p>An <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of an organization in Organizations, for example: <code>organizations::************:organization/o-exampleorgid</code> </p> </li> <li> <p>An ARN of an organizational unit (OU) in Organizations, for example: <code>organizations::************:ou/o-exampleorgid/ou-examplerootid-exampleouid123</code> </p> </li> <li> <p>An ARN of an IAM role, for example: <code>iam::************:role/rolename</code> </p> </li> <li> <p>An ARN of an IAM user, for example: <code>iam::************user/username</code> </p> </li> </ul> <note> <p>Not all resource types can be shared with IAM roles and users. For more information, see <a href=\"https://docs.aws.amazon.com/ram/latest/userguide/permissions.html#permissions-rbp-supported-resource-types\">Sharing with IAM roles and users</a> in the <i>Resource Access Manager User Guide</i>.</p> </note>"}, "tags": {"shape": "TagList", "documentation": "<p>Specifies one or more tags to attach to the resource share itself. It doesn't attach the tags to the resources associated with the resource share.</p>"}, "allowExternalPrincipals": {"shape": "Boolean", "documentation": "<p>Specifies whether principals outside your organization in Organizations can be associated with a resource share. A value of <code>true</code> lets you share with individual Amazon Web Services accounts that are <i>not</i> in your organization. A value of <code>false</code> only has meaning if your account is a member of an Amazon Web Services Organization. The default value is <code>true</code>.</p>"}, "clientToken": {"shape": "String", "documentation": "<p>Specifies a unique, case-sensitive identifier that you provide to ensure the idempotency of the request. This lets you safely retry the request without accidentally performing the same operation a second time. Passing the same value to a later call to an operation requires that you also pass the same value for all other parameters. We recommend that you use a <a href=\"https://wikipedia.org/wiki/Universally_unique_identifier\">UUID type of value.</a>.</p> <p>If you don't provide this value, then Amazon Web Services generates a random one for you.</p> <p>If you retry the operation with the same <code>ClientToken</code>, but with different parameters, the retry fails with an <code>IdempotentParameterMismatch</code> error.</p>"}, "permissionArns": {"shape": "PermissionArnList", "documentation": "<p>Specifies the <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs)</a> of the RAM permission to associate with the resource share. If you do not specify an ARN for the permission, RAM automatically attaches the default version of the permission for each resource type. You can associate only one permission with each resource type included in the resource share.</p>"}, "sources": {"shape": "SourceArnOrAccountList", "documentation": "<p>Specifies from which source accounts the service principal has access to the resources in this resource share.</p>"}}}, "CreateResourceShareResponse": {"type": "structure", "members": {"resourceShare": {"shape": "ResourceShare", "documentation": "<p>An object with information about the new resource share.</p>"}, "clientToken": {"shape": "String", "documentation": "<p>The idempotency identifier associated with this request. If you want to repeat the same operation in an idempotent manner then you must include this value in the <code>clientToken</code> request parameter of that later call. All other parameters must also have the same values that you used in the first call.</p>"}}}, "DateTime": {"type": "timestamp"}, "DeletePermissionRequest": {"type": "structure", "required": ["permissionArn"], "members": {"permissionArn": {"shape": "String", "documentation": "<p>Specifies the <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of the customer managed permission that you want to delete.</p>", "location": "querystring", "locationName": "permissionArn"}, "clientToken": {"shape": "String", "documentation": "<p>Specifies a unique, case-sensitive identifier that you provide to ensure the idempotency of the request. This lets you safely retry the request without accidentally performing the same operation a second time. Passing the same value to a later call to an operation requires that you also pass the same value for all other parameters. We recommend that you use a <a href=\"https://wikipedia.org/wiki/Universally_unique_identifier\">UUID type of value.</a>.</p> <p>If you don't provide this value, then Amazon Web Services generates a random one for you.</p> <p>If you retry the operation with the same <code>ClientToken</code>, but with different parameters, the retry fails with an <code>IdempotentParameterMismatch</code> error.</p>", "location": "querystring", "locationName": "clientToken"}}}, "DeletePermissionResponse": {"type": "structure", "members": {"returnValue": {"shape": "Boolean", "documentation": "<p>A boolean that indicates whether the delete operations succeeded.</p>"}, "clientToken": {"shape": "String", "documentation": "<p>The idempotency identifier associated with this request. If you want to repeat the same operation in an idempotent manner then you must include this value in the <code>clientToken</code> request parameter of that later call. All other parameters must also have the same values that you used in the first call.</p>"}, "permissionStatus": {"shape": "PermissionStatus", "documentation": "<p>This operation is performed asynchronously, and this response parameter indicates the current status.</p>"}}}, "DeletePermissionVersionRequest": {"type": "structure", "required": ["permissionArn", "permissionVersion"], "members": {"permissionArn": {"shape": "String", "documentation": "<p>Specifies the <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of the permission with the version you want to delete.</p>", "location": "querystring", "locationName": "permissionArn"}, "permissionVersion": {"shape": "Integer", "documentation": "<p>Specifies the version number to delete.</p> <p>You can't delete the default version for a customer managed permission.</p> <p>You can't delete a version if it's the only version of the permission. You must either first create another version, or delete the permission completely.</p> <p>You can't delete a version if it is attached to any resource shares. If the version is the default, you must first use <a>SetDefaultPermissionVersion</a> to set a different version as the default for the customer managed permission, and then use <a>AssociateResourceSharePermission</a> to update your resource shares to use the new default version.</p>", "location": "querystring", "locationName": "permissionVersion"}, "clientToken": {"shape": "String", "documentation": "<p>Specifies a unique, case-sensitive identifier that you provide to ensure the idempotency of the request. This lets you safely retry the request without accidentally performing the same operation a second time. Passing the same value to a later call to an operation requires that you also pass the same value for all other parameters. We recommend that you use a <a href=\"https://wikipedia.org/wiki/Universally_unique_identifier\">UUID type of value.</a>.</p> <p>If you don't provide this value, then Amazon Web Services generates a random one for you.</p> <p>If you retry the operation with the same <code>ClientToken</code>, but with different parameters, the retry fails with an <code>IdempotentParameterMismatch</code> error.</p>", "location": "querystring", "locationName": "clientToken"}}}, "DeletePermissionVersionResponse": {"type": "structure", "members": {"returnValue": {"shape": "Boolean", "documentation": "<p>A boolean value that indicates whether the operation is successful.</p>"}, "clientToken": {"shape": "String", "documentation": "<p>The idempotency identifier associated with this request. If you want to repeat the same operation in an idempotent manner then you must include this value in the <code>clientToken</code> request parameter of that later call. All other parameters must also have the same values that you used in the first call.</p>"}, "permissionStatus": {"shape": "PermissionStatus", "documentation": "<p>This operation is performed asynchronously, and this response parameter indicates the current status.</p>"}}}, "DeleteResourceShareRequest": {"type": "structure", "required": ["resourceShareArn"], "members": {"resourceShareArn": {"shape": "String", "documentation": "<p>Specifies the <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of the resource share to delete.</p>", "location": "querystring", "locationName": "resourceShareArn"}, "clientToken": {"shape": "String", "documentation": "<p>Specifies a unique, case-sensitive identifier that you provide to ensure the idempotency of the request. This lets you safely retry the request without accidentally performing the same operation a second time. Passing the same value to a later call to an operation requires that you also pass the same value for all other parameters. We recommend that you use a <a href=\"https://wikipedia.org/wiki/Universally_unique_identifier\">UUID type of value.</a>.</p> <p>If you don't provide this value, then Amazon Web Services generates a random one for you.</p> <p>If you retry the operation with the same <code>ClientToken</code>, but with different parameters, the retry fails with an <code>IdempotentParameterMismatch</code> error.</p>", "location": "querystring", "locationName": "clientToken"}}}, "DeleteResourceShareResponse": {"type": "structure", "members": {"returnValue": {"shape": "Boolean", "documentation": "<p>A return value of <code>true</code> indicates that the request succeeded. A value of <code>false</code> indicates that the request failed.</p>"}, "clientToken": {"shape": "String", "documentation": "<p>The idempotency identifier associated with this request. If you want to repeat the same operation in an idempotent manner then you must include this value in the <code>clientToken</code> request parameter of that later call. All other parameters must also have the same values that you used in the first call.</p>"}}}, "DisassociateResourceSharePermissionRequest": {"type": "structure", "required": ["resourceShareArn", "permissionArn"], "members": {"resourceShareArn": {"shape": "String", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of the resource share that you want to remove the managed permission from.</p>"}, "permissionArn": {"shape": "String", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of the managed permission to disassociate from the resource share. Changes to permissions take effect immediately.</p>"}, "clientToken": {"shape": "String", "documentation": "<p>Specifies a unique, case-sensitive identifier that you provide to ensure the idempotency of the request. This lets you safely retry the request without accidentally performing the same operation a second time. Passing the same value to a later call to an operation requires that you also pass the same value for all other parameters. We recommend that you use a <a href=\"https://wikipedia.org/wiki/Universally_unique_identifier\">UUID type of value.</a>.</p> <p>If you don't provide this value, then Amazon Web Services generates a random one for you.</p> <p>If you retry the operation with the same <code>ClientToken</code>, but with different parameters, the retry fails with an <code>IdempotentParameterMismatch</code> error.</p>"}}}, "DisassociateResourceSharePermissionResponse": {"type": "structure", "members": {"returnValue": {"shape": "Boolean", "documentation": "<p>A return value of <code>true</code> indicates that the request succeeded. A value of <code>false</code> indicates that the request failed.</p>"}, "clientToken": {"shape": "String", "documentation": "<p>The idempotency identifier associated with this request. If you want to repeat the same operation in an idempotent manner then you must include this value in the <code>clientToken</code> request parameter of that later call. All other parameters must also have the same values that you used in the first call.</p>"}}}, "DisassociateResourceShareRequest": {"type": "structure", "required": ["resourceShareArn"], "members": {"resourceShareArn": {"shape": "String", "documentation": "<p>Specifies <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of the resource share that you want to remove resources or principals from.</p>"}, "resourceArns": {"shape": "ResourceArnList", "documentation": "<p>Specifies a list of <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs)</a> for one or more resources that you want to remove from the resource share. After the operation runs, these resources are no longer shared with principals associated with the resource share.</p>"}, "principals": {"shape": "PrincipalArnOrIdList", "documentation": "<p>Specifies a list of one or more principals that no longer are to have access to the resources in this resource share.</p> <p>You can include the following values:</p> <ul> <li> <p>An Amazon Web Services account ID, for example: <code>************</code> </p> </li> <li> <p>An <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of an organization in Organizations, for example: <code>organizations::************:organization/o-exampleorgid</code> </p> </li> <li> <p>An ARN of an organizational unit (OU) in Organizations, for example: <code>organizations::************:ou/o-exampleorgid/ou-examplerootid-exampleouid123</code> </p> </li> <li> <p>An ARN of an IAM role, for example: <code>iam::************:role/rolename</code> </p> </li> <li> <p>An ARN of an IAM user, for example: <code>iam::************user/username</code> </p> </li> </ul> <note> <p>Not all resource types can be shared with IAM roles and users. For more information, see <a href=\"https://docs.aws.amazon.com/ram/latest/userguide/permissions.html#permissions-rbp-supported-resource-types\">Sharing with IAM roles and users</a> in the <i>Resource Access Manager User Guide</i>.</p> </note>"}, "clientToken": {"shape": "String", "documentation": "<p>Specifies a unique, case-sensitive identifier that you provide to ensure the idempotency of the request. This lets you safely retry the request without accidentally performing the same operation a second time. Passing the same value to a later call to an operation requires that you also pass the same value for all other parameters. We recommend that you use a <a href=\"https://wikipedia.org/wiki/Universally_unique_identifier\">UUID type of value.</a>.</p> <p>If you don't provide this value, then Amazon Web Services generates a random one for you.</p> <p>If you retry the operation with the same <code>ClientToken</code>, but with different parameters, the retry fails with an <code>IdempotentParameterMismatch</code> error.</p>"}, "sources": {"shape": "SourceArnOrAccountList", "documentation": "<p>Specifies from which source accounts the service principal no longer has access to the resources in this resource share.</p>"}}}, "DisassociateResourceShareResponse": {"type": "structure", "members": {"resourceShareAssociations": {"shape": "ResourceShareAssociationList", "documentation": "<p>An array of objects with information about the updated associations for this resource share.</p>"}, "clientToken": {"shape": "String", "documentation": "<p>The idempotency identifier associated with this request. If you want to repeat the same operation in an idempotent manner then you must include this value in the <code>clientToken</code> request parameter of that later call. All other parameters must also have the same values that you used in the first call.</p>"}}}, "EnableSharingWithAwsOrganizationRequest": {"type": "structure", "members": {}}, "EnableSharingWithAwsOrganizationResponse": {"type": "structure", "members": {"returnValue": {"shape": "Boolean", "documentation": "<p>A return value of <code>true</code> indicates that the request succeeded. A value of <code>false</code> indicates that the request failed.</p>"}}}, "GetPermissionRequest": {"type": "structure", "required": ["permissionArn"], "members": {"permissionArn": {"shape": "String", "documentation": "<p>Specifies the <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of the permission whose contents you want to retrieve. To find the ARN for a permission, use either the <a>ListPermissions</a> operation or go to the <a href=\"https://console.aws.amazon.com/ram/home#Permissions:\">Permissions library</a> page in the RAM console and then choose the name of the permission. The ARN is displayed on the detail page.</p>"}, "permissionVersion": {"shape": "Integer", "documentation": "<p>Specifies the version number of the RAM permission to retrieve. If you don't specify this parameter, the operation retrieves the default version.</p> <p>To see the list of available versions, use <a>ListPermissionVersions</a>.</p>"}}}, "GetPermissionResponse": {"type": "structure", "members": {"permission": {"shape": "ResourceSharePermissionDetail", "documentation": "<p>An object with details about the permission.</p>"}}}, "GetResourcePoliciesRequest": {"type": "structure", "required": ["resourceArns"], "members": {"resourceArns": {"shape": "ResourceArnList", "documentation": "<p>Specifies the <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs)</a> of the resources whose policies you want to retrieve.</p>"}, "principal": {"shape": "String", "documentation": "<p>Specifies the principal.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>Specifies that you want to receive the next page of results. Valid only if you received a <code>NextToken</code> response in the previous request. If you did, it indicates that more output is available. Set this parameter to the value provided by the previous call's <code>NextToken</code> response to request the next page of results.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>Specifies the total number of results that you want included on each page of the response. If you do not include this parameter, it defaults to a value that is specific to the operation. If additional items exist beyond the number you specify, the <code>NextToken</code> response element is returned with a value (not null). Include the specified value as the <code>NextToken</code> request parameter in the next call to the operation to get the next part of the results. Note that the service might return fewer results than the maximum even when there are more results available. You should check <code>NextToken</code> after every operation to ensure that you receive all of the results.</p>"}}}, "GetResourcePoliciesResponse": {"type": "structure", "members": {"policies": {"shape": "PolicyList", "documentation": "<p>An array of resource policy documents in JSON format.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>If present, this value indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>. This indicates that this is the last page of results.</p>"}}}, "GetResourceShareAssociationsRequest": {"type": "structure", "required": ["associationType"], "members": {"associationType": {"shape": "ResourceShareAssociationType", "documentation": "<p>Specifies whether you want to retrieve the associations that involve a specified resource or principal.</p> <ul> <li> <p> <code>PRINCIPAL</code> – list the principals whose associations you want to see.</p> </li> <li> <p> <code>RESOURCE</code> – list the resources whose associations you want to see.</p> </li> </ul>"}, "resourceShareArns": {"shape": "ResourceShareArnList", "documentation": "<p>Specifies a list of <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs)</a> of the resource share whose associations you want to retrieve.</p>"}, "resourceArn": {"shape": "String", "documentation": "<p>Specifies the <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of a resource whose resource shares you want to retrieve.</p> <p>You cannot specify this parameter if the association type is <code>PRINCIPAL</code>.</p>"}, "principal": {"shape": "String", "documentation": "<p>Specifies the ID of the principal whose resource shares you want to retrieve. This can be an Amazon Web Services account ID, an organization ID, an organizational unit ID, or the <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of an individual IAM role or user.</p> <p>You cannot specify this parameter if the association type is <code>RESOURCE</code>.</p>"}, "associationStatus": {"shape": "ResourceShareAssociationStatus", "documentation": "<p>Specifies that you want to retrieve only associations that have this status.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>Specifies that you want to receive the next page of results. Valid only if you received a <code>NextToken</code> response in the previous request. If you did, it indicates that more output is available. Set this parameter to the value provided by the previous call's <code>NextToken</code> response to request the next page of results.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>Specifies the total number of results that you want included on each page of the response. If you do not include this parameter, it defaults to a value that is specific to the operation. If additional items exist beyond the number you specify, the <code>NextToken</code> response element is returned with a value (not null). Include the specified value as the <code>NextToken</code> request parameter in the next call to the operation to get the next part of the results. Note that the service might return fewer results than the maximum even when there are more results available. You should check <code>NextToken</code> after every operation to ensure that you receive all of the results.</p>"}}}, "GetResourceShareAssociationsResponse": {"type": "structure", "members": {"resourceShareAssociations": {"shape": "ResourceShareAssociationList", "documentation": "<p>An array of objects that contain the details about the associations.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>If present, this value indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>. This indicates that this is the last page of results.</p>"}}}, "GetResourceShareInvitationsRequest": {"type": "structure", "members": {"resourceShareInvitationArns": {"shape": "ResourceShareInvitationArnList", "documentation": "<p>Specifies the <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs)</a> of the resource share invitations you want information about.</p>"}, "resourceShareArns": {"shape": "ResourceShareArnList", "documentation": "<p>Specifies that you want details about invitations only for the resource shares described by this list of <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs)</a> </p>"}, "nextToken": {"shape": "String", "documentation": "<p>Specifies that you want to receive the next page of results. Valid only if you received a <code>NextToken</code> response in the previous request. If you did, it indicates that more output is available. Set this parameter to the value provided by the previous call's <code>NextToken</code> response to request the next page of results.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>Specifies the total number of results that you want included on each page of the response. If you do not include this parameter, it defaults to a value that is specific to the operation. If additional items exist beyond the number you specify, the <code>NextToken</code> response element is returned with a value (not null). Include the specified value as the <code>NextToken</code> request parameter in the next call to the operation to get the next part of the results. Note that the service might return fewer results than the maximum even when there are more results available. You should check <code>NextToken</code> after every operation to ensure that you receive all of the results.</p>"}}}, "GetResourceShareInvitationsResponse": {"type": "structure", "members": {"resourceShareInvitations": {"shape": "ResourceShareInvitationList", "documentation": "<p>An array of objects that contain the details about the invitations.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>If present, this value indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>. This indicates that this is the last page of results.</p>"}}}, "GetResourceSharesRequest": {"type": "structure", "required": ["resourceOwner"], "members": {"resourceShareArns": {"shape": "ResourceShareArnList", "documentation": "<p>Specifies the <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs)</a> of individual resource shares that you want information about.</p>"}, "resourceShareStatus": {"shape": "ResourceShareStatus", "documentation": "<p>Specifies that you want to retrieve details of only those resource shares that have this status.</p>"}, "resourceOwner": {"shape": "ResourceOwner", "documentation": "<p>Specifies that you want to retrieve details of only those resource shares that match the following:</p> <ul> <li> <p> <b> <code>SELF</code> </b> – resource shares that your account shares with other accounts</p> </li> <li> <p> <b> <code>OTHER-ACCOUNTS</code> </b> – resource shares that other accounts share with your account</p> </li> </ul>"}, "name": {"shape": "String", "documentation": "<p>Specifies the name of an individual resource share that you want to retrieve details about.</p>"}, "tagFilters": {"shape": "TagFilters", "documentation": "<p>Specifies that you want to retrieve details of only those resource shares that match the specified tag keys and values.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>Specifies that you want to receive the next page of results. Valid only if you received a <code>NextToken</code> response in the previous request. If you did, it indicates that more output is available. Set this parameter to the value provided by the previous call's <code>NextToken</code> response to request the next page of results.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>Specifies the total number of results that you want included on each page of the response. If you do not include this parameter, it defaults to a value that is specific to the operation. If additional items exist beyond the number you specify, the <code>NextToken</code> response element is returned with a value (not null). Include the specified value as the <code>NextToken</code> request parameter in the next call to the operation to get the next part of the results. Note that the service might return fewer results than the maximum even when there are more results available. You should check <code>NextToken</code> after every operation to ensure that you receive all of the results.</p>"}, "permissionArn": {"shape": "String", "documentation": "<p>Specifies that you want to retrieve details of only those resource shares that use the managed permission with this <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a>.</p>"}, "permissionVersion": {"shape": "Integer", "documentation": "<p>Specifies that you want to retrieve details for only those resource shares that use the specified version of the managed permission.</p>"}}}, "GetResourceSharesResponse": {"type": "structure", "members": {"resourceShares": {"shape": "ResourceShareList", "documentation": "<p>An array of objects that contain the information about the resource shares.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>If present, this value indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>. This indicates that this is the last page of results.</p>"}}}, "IdempotentParameterMismatchException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The operation failed because the client token input parameter matched one that was used with a previous call to the operation, but at least one of the other input parameters is different from the previous call.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "Integer": {"type": "integer"}, "InvalidClientTokenException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The operation failed because the specified client token isn't valid.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "InvalidMaxResultsException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The operation failed because the specified value for <code>MaxResults</code> isn't valid.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "InvalidNextTokenException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The operation failed because the specified value for <code>NextToken</code> isn't valid. You must specify a value you received in the <code>NextToken</code> response of a previous call to this operation.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "InvalidParameterException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The operation failed because a parameter you specified isn't valid.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "InvalidPolicyException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The operation failed because a policy you specified isn't valid.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "InvalidResourceTypeException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The operation failed because the specified resource type isn't valid.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "InvalidStateTransitionException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The operation failed because the requested operation isn't valid for the resource share in its current state.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "ListPendingInvitationResourcesRequest": {"type": "structure", "required": ["resourceShareInvitationArn"], "members": {"resourceShareInvitationArn": {"shape": "String", "documentation": "<p>Specifies the <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of the invitation. You can use <a>GetResourceShareInvitations</a> to find the ARN of the invitation.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>Specifies that you want to receive the next page of results. Valid only if you received a <code>NextToken</code> response in the previous request. If you did, it indicates that more output is available. Set this parameter to the value provided by the previous call's <code>NextToken</code> response to request the next page of results.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>Specifies the total number of results that you want included on each page of the response. If you do not include this parameter, it defaults to a value that is specific to the operation. If additional items exist beyond the number you specify, the <code>NextToken</code> response element is returned with a value (not null). Include the specified value as the <code>NextToken</code> request parameter in the next call to the operation to get the next part of the results. Note that the service might return fewer results than the maximum even when there are more results available. You should check <code>NextToken</code> after every operation to ensure that you receive all of the results.</p>"}, "resourceRegionScope": {"shape": "ResourceRegionScopeFilter", "documentation": "<p>Specifies that you want the results to include only resources that have the specified scope.</p> <ul> <li> <p> <code>ALL</code> – the results include both global and regional resources or resource types.</p> </li> <li> <p> <code>GLOBAL</code> – the results include only global resources or resource types.</p> </li> <li> <p> <code>REGIONAL</code> – the results include only regional resources or resource types.</p> </li> </ul> <p>The default value is <code>ALL</code>.</p>"}}}, "ListPendingInvitationResourcesResponse": {"type": "structure", "members": {"resources": {"shape": "ResourceList", "documentation": "<p>An array of objects that contain the information about the resources included the specified resource share.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>If present, this value indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>. This indicates that this is the last page of results.</p>"}}}, "ListPermissionAssociationsRequest": {"type": "structure", "members": {"permissionArn": {"shape": "String", "documentation": "<p>Specifies the <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of the managed permission.</p>"}, "permissionVersion": {"shape": "Integer", "documentation": "<p>Specifies that you want to list only those associations with resource shares that use this version of the managed permission. If you don't provide a value for this parameter, then the operation returns information about associations with resource shares that use any version of the managed permission.</p>"}, "associationStatus": {"shape": "ResourceShareAssociationStatus", "documentation": "<p>Specifies that you want to list only those associations with resource shares that match this status.</p>"}, "resourceType": {"shape": "String", "documentation": "<p>Specifies that you want to list only those associations with resource shares that include at least one resource of this resource type.</p>"}, "featureSet": {"shape": "PermissionFeatureSet", "documentation": "<p>Specifies that you want to list only those associations with resource shares that have a <code>featureSet</code> with this value.</p>"}, "defaultVersion": {"shape": "Boolean", "documentation": "<p>When <code>true</code>, specifies that you want to list only those associations with resource shares that use the default version of the specified managed permission.</p> <p>When <code>false</code> (the default value), lists associations with resource shares that use any version of the specified managed permission.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>Specifies that you want to receive the next page of results. Valid only if you received a <code>NextToken</code> response in the previous request. If you did, it indicates that more output is available. Set this parameter to the value provided by the previous call's <code>NextToken</code> response to request the next page of results.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>Specifies the total number of results that you want included on each page of the response. If you do not include this parameter, it defaults to a value that is specific to the operation. If additional items exist beyond the number you specify, the <code>NextToken</code> response element is returned with a value (not null). Include the specified value as the <code>NextToken</code> request parameter in the next call to the operation to get the next part of the results. Note that the service might return fewer results than the maximum even when there are more results available. You should check <code>NextToken</code> after every operation to ensure that you receive all of the results.</p>"}}}, "ListPermissionAssociationsResponse": {"type": "structure", "members": {"permissions": {"shape": "AssociatedPermissionList", "documentation": "<p>A structure with information about this customer managed permission.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>If present, this value indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>. This indicates that this is the last page of results.</p>"}}}, "ListPermissionVersionsRequest": {"type": "structure", "required": ["permissionArn"], "members": {"permissionArn": {"shape": "String", "documentation": "<p>Specifies the <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of the RAM permission whose versions you want to list. You can use the <code>permissionVersion</code> parameter on the <a>AssociateResourceSharePermission</a> operation to specify a non-default version to attach.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>Specifies that you want to receive the next page of results. Valid only if you received a <code>NextToken</code> response in the previous request. If you did, it indicates that more output is available. Set this parameter to the value provided by the previous call's <code>NextToken</code> response to request the next page of results.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>Specifies the total number of results that you want included on each page of the response. If you do not include this parameter, it defaults to a value that is specific to the operation. If additional items exist beyond the number you specify, the <code>NextToken</code> response element is returned with a value (not null). Include the specified value as the <code>NextToken</code> request parameter in the next call to the operation to get the next part of the results. Note that the service might return fewer results than the maximum even when there are more results available. You should check <code>NextToken</code> after every operation to ensure that you receive all of the results.</p>"}}}, "ListPermissionVersionsResponse": {"type": "structure", "members": {"permissions": {"shape": "ResourceSharePermissionList", "documentation": "<p>An array of objects that contain details for each of the available versions.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>If present, this value indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>. This indicates that this is the last page of results.</p>"}}}, "ListPermissionsRequest": {"type": "structure", "members": {"resourceType": {"shape": "String", "documentation": "<p>Specifies that you want to list only those permissions that apply to the specified resource type. This parameter is not case sensitive.</p> <p>For example, to list only permissions that apply to Amazon EC2 subnets, specify <code>ec2:subnet</code>. You can use the <a>ListResourceTypes</a> operation to get the specific string required.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>Specifies that you want to receive the next page of results. Valid only if you received a <code>NextToken</code> response in the previous request. If you did, it indicates that more output is available. Set this parameter to the value provided by the previous call's <code>NextToken</code> response to request the next page of results.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>Specifies the total number of results that you want included on each page of the response. If you do not include this parameter, it defaults to a value that is specific to the operation. If additional items exist beyond the number you specify, the <code>NextToken</code> response element is returned with a value (not null). Include the specified value as the <code>NextToken</code> request parameter in the next call to the operation to get the next part of the results. Note that the service might return fewer results than the maximum even when there are more results available. You should check <code>NextToken</code> after every operation to ensure that you receive all of the results.</p>"}, "permissionType": {"shape": "PermissionTypeFilter", "documentation": "<p>Specifies that you want to list only permissions of this type:</p> <ul> <li> <p> <code>AWS</code> – returns only Amazon Web Services managed permissions.</p> </li> <li> <p> <code>LOCAL</code> – returns only customer managed permissions</p> </li> <li> <p> <code>ALL</code> – returns both Amazon Web Services managed permissions and customer managed permissions.</p> </li> </ul> <p>If you don't specify this parameter, the default is <code>All</code>.</p>"}}}, "ListPermissionsResponse": {"type": "structure", "members": {"permissions": {"shape": "ResourceSharePermissionList", "documentation": "<p>An array of objects with information about the permissions.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>If present, this value indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>. This indicates that this is the last page of results.</p>"}}}, "ListPrincipalsRequest": {"type": "structure", "required": ["resourceOwner"], "members": {"resourceOwner": {"shape": "ResourceOwner", "documentation": "<p>Specifies that you want to list information for only resource shares that match the following:</p> <ul> <li> <p> <b> <code>SELF</code> </b> – principals that your account is sharing resources with</p> </li> <li> <p> <b> <code>OTHER-ACCOUNTS</code> </b> – principals that are sharing resources with your account</p> </li> </ul>"}, "resourceArn": {"shape": "String", "documentation": "<p>Specifies that you want to list principal information for the resource share with the specified <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a>.</p>"}, "principals": {"shape": "PrincipalArnOrIdList", "documentation": "<p>Specifies that you want to list information for only the listed principals.</p> <p>You can include the following values:</p> <ul> <li> <p>An Amazon Web Services account ID, for example: <code>************</code> </p> </li> <li> <p>An <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of an organization in Organizations, for example: <code>organizations::************:organization/o-exampleorgid</code> </p> </li> <li> <p>An ARN of an organizational unit (OU) in Organizations, for example: <code>organizations::************:ou/o-exampleorgid/ou-examplerootid-exampleouid123</code> </p> </li> <li> <p>An ARN of an IAM role, for example: <code>iam::************:role/rolename</code> </p> </li> <li> <p>An ARN of an IAM user, for example: <code>iam::************user/username</code> </p> </li> </ul> <note> <p>Not all resource types can be shared with IAM roles and users. For more information, see <a href=\"https://docs.aws.amazon.com/ram/latest/userguide/permissions.html#permissions-rbp-supported-resource-types\">Sharing with IAM roles and users</a> in the <i>Resource Access Manager User Guide</i>.</p> </note>"}, "resourceType": {"shape": "String", "documentation": "<p>Specifies that you want to list information for only principals associated with resource shares that include the specified resource type.</p> <p>For a list of valid values, query the <a>ListResourceTypes</a> operation.</p>"}, "resourceShareArns": {"shape": "ResourceShareArnList", "documentation": "<p>Specifies that you want to list information for only principals associated with the resource shares specified by a list the <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs)</a>.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>Specifies that you want to receive the next page of results. Valid only if you received a <code>NextToken</code> response in the previous request. If you did, it indicates that more output is available. Set this parameter to the value provided by the previous call's <code>NextToken</code> response to request the next page of results.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>Specifies the total number of results that you want included on each page of the response. If you do not include this parameter, it defaults to a value that is specific to the operation. If additional items exist beyond the number you specify, the <code>NextToken</code> response element is returned with a value (not null). Include the specified value as the <code>NextToken</code> request parameter in the next call to the operation to get the next part of the results. Note that the service might return fewer results than the maximum even when there are more results available. You should check <code>NextToken</code> after every operation to ensure that you receive all of the results.</p>"}}}, "ListPrincipalsResponse": {"type": "structure", "members": {"principals": {"shape": "PrincipalList", "documentation": "<p>An array of objects that contain the details about the principals.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>If present, this value indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>. This indicates that this is the last page of results.</p>"}}}, "ListReplacePermissionAssociationsWorkRequest": {"type": "structure", "members": {"workIds": {"shape": "ReplacePermissionAssociationsWorkIdList", "documentation": "<p>A list of IDs. These values come from the <code>id</code>field of the <code>replacePermissionAssociationsWork</code>structure returned by the <a>ReplacePermissionAssociations</a> operation. </p>"}, "status": {"shape": "ReplacePermissionAssociationsWorkStatus", "documentation": "<p>Specifies that you want to see only the details about requests with a status that matches this value.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>Specifies that you want to receive the next page of results. Valid only if you received a <code>NextToken</code> response in the previous request. If you did, it indicates that more output is available. Set this parameter to the value provided by the previous call's <code>NextToken</code> response to request the next page of results.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>Specifies the total number of results that you want included on each page of the response. If you do not include this parameter, it defaults to a value that is specific to the operation. If additional items exist beyond the number you specify, the <code>NextToken</code> response element is returned with a value (not null). Include the specified value as the <code>NextToken</code> request parameter in the next call to the operation to get the next part of the results. Note that the service might return fewer results than the maximum even when there are more results available. You should check <code>NextToken</code> after every operation to ensure that you receive all of the results.</p>"}}}, "ListReplacePermissionAssociationsWorkResponse": {"type": "structure", "members": {"replacePermissionAssociationsWorks": {"shape": "ReplacePermissionAssociationsWorkList", "documentation": "<p>An array of data structures that provide details of the matching work IDs.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>If present, this value indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>. This indicates that this is the last page of results.</p>"}}}, "ListResourceSharePermissionsRequest": {"type": "structure", "required": ["resourceShareArn"], "members": {"resourceShareArn": {"shape": "String", "documentation": "<p>Specifies the <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of the resource share for which you want to retrieve the associated permissions.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>Specifies that you want to receive the next page of results. Valid only if you received a <code>NextToken</code> response in the previous request. If you did, it indicates that more output is available. Set this parameter to the value provided by the previous call's <code>NextToken</code> response to request the next page of results.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>Specifies the total number of results that you want included on each page of the response. If you do not include this parameter, it defaults to a value that is specific to the operation. If additional items exist beyond the number you specify, the <code>NextToken</code> response element is returned with a value (not null). Include the specified value as the <code>NextToken</code> request parameter in the next call to the operation to get the next part of the results. Note that the service might return fewer results than the maximum even when there are more results available. You should check <code>NextToken</code> after every operation to ensure that you receive all of the results.</p>"}}}, "ListResourceSharePermissionsResponse": {"type": "structure", "members": {"permissions": {"shape": "ResourceSharePermissionList", "documentation": "<p>An array of objects that describe the permissions associated with the resource share.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>If present, this value indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>. This indicates that this is the last page of results.</p>"}}}, "ListResourceTypesRequest": {"type": "structure", "members": {"nextToken": {"shape": "String", "documentation": "<p>Specifies that you want to receive the next page of results. Valid only if you received a <code>NextToken</code> response in the previous request. If you did, it indicates that more output is available. Set this parameter to the value provided by the previous call's <code>NextToken</code> response to request the next page of results.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>Specifies the total number of results that you want included on each page of the response. If you do not include this parameter, it defaults to a value that is specific to the operation. If additional items exist beyond the number you specify, the <code>NextToken</code> response element is returned with a value (not null). Include the specified value as the <code>NextToken</code> request parameter in the next call to the operation to get the next part of the results. Note that the service might return fewer results than the maximum even when there are more results available. You should check <code>NextToken</code> after every operation to ensure that you receive all of the results.</p>"}, "resourceRegionScope": {"shape": "ResourceRegionScopeFilter", "documentation": "<p>Specifies that you want the results to include only resources that have the specified scope.</p> <ul> <li> <p> <code>ALL</code> – the results include both global and regional resources or resource types.</p> </li> <li> <p> <code>GLOBAL</code> – the results include only global resources or resource types.</p> </li> <li> <p> <code>REGIONAL</code> – the results include only regional resources or resource types.</p> </li> </ul> <p>The default value is <code>ALL</code>.</p>"}}}, "ListResourceTypesResponse": {"type": "structure", "members": {"resourceTypes": {"shape": "ServiceNameAndResourceTypeList", "documentation": "<p>An array of objects that contain information about the resource types that can be shared using RAM.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>If present, this value indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>. This indicates that this is the last page of results.</p>"}}}, "ListResourcesRequest": {"type": "structure", "required": ["resourceOwner"], "members": {"resourceOwner": {"shape": "ResourceOwner", "documentation": "<p>Specifies that you want to list only the resource shares that match the following:</p> <ul> <li> <p> <b> <code>SELF</code> </b> – resources that your account shares with other accounts</p> </li> <li> <p> <b> <code>OTHER-ACCOUNTS</code> </b> – resources that other accounts share with your account</p> </li> </ul>"}, "principal": {"shape": "String", "documentation": "<p>Specifies that you want to list only the resource shares that are associated with the specified principal.</p>"}, "resourceType": {"shape": "String", "documentation": "<p>Specifies that you want to list only the resource shares that include resources of the specified resource type.</p> <p>For valid values, query the <a>ListResourceTypes</a> operation.</p>"}, "resourceArns": {"shape": "ResourceArnList", "documentation": "<p>Specifies that you want to list only the resource shares that include resources with the specified <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs)</a>.</p>"}, "resourceShareArns": {"shape": "ResourceShareArnList", "documentation": "<p>Specifies that you want to list only resources in the resource shares identified by the specified <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Names (ARNs)</a>.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>Specifies that you want to receive the next page of results. Valid only if you received a <code>NextToken</code> response in the previous request. If you did, it indicates that more output is available. Set this parameter to the value provided by the previous call's <code>NextToken</code> response to request the next page of results.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>Specifies the total number of results that you want included on each page of the response. If you do not include this parameter, it defaults to a value that is specific to the operation. If additional items exist beyond the number you specify, the <code>NextToken</code> response element is returned with a value (not null). Include the specified value as the <code>NextToken</code> request parameter in the next call to the operation to get the next part of the results. Note that the service might return fewer results than the maximum even when there are more results available. You should check <code>NextToken</code> after every operation to ensure that you receive all of the results.</p>"}, "resourceRegionScope": {"shape": "ResourceRegionScopeFilter", "documentation": "<p>Specifies that you want the results to include only resources that have the specified scope.</p> <ul> <li> <p> <code>ALL</code> – the results include both global and regional resources or resource types.</p> </li> <li> <p> <code>GLOBAL</code> – the results include only global resources or resource types.</p> </li> <li> <p> <code>REGIONAL</code> – the results include only regional resources or resource types.</p> </li> </ul> <p>The default value is <code>ALL</code>.</p>"}}}, "ListResourcesResponse": {"type": "structure", "members": {"resources": {"shape": "ResourceList", "documentation": "<p>An array of objects that contain information about the resources.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>If present, this value indicates that more output is available than is included in the current response. Use this value in the <code>NextToken</code> request parameter in a subsequent call to the operation to get the next part of the output. You should repeat this until the <code>NextToken</code> response element comes back as <code>null</code>. This indicates that this is the last page of results.</p>"}}}, "MalformedArnException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The operation failed because the specified <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> has a format that isn't valid.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "MalformedPolicyTemplateException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The operation failed because the policy template that you provided isn't valid.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "MaxResults": {"type": "integer", "max": 500, "min": 1}, "MissingRequiredParameterException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The operation failed because a required input parameter is missing.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "OperationNotPermittedException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The operation failed because the requested operation isn't permitted.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "PermissionAlreadyExistsException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The operation failed because a permission with the specified name already exists in the requested Amazon Web Services Region. Choose a different name.</p>", "error": {"httpStatusCode": 409}, "exception": true}, "PermissionArnList": {"type": "list", "member": {"shape": "String"}}, "PermissionFeatureSet": {"type": "string", "enum": ["CREATED_FROM_POLICY", "PROMOTING_TO_STANDARD", "STANDARD"]}, "PermissionLimitExceededException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The operation failed because it would exceed the maximum number of permissions you can create in each Amazon Web Services Region. To view the limits for your Amazon Web Services account, see the <a href=\"https://console.aws.amazon.com/servicequotas/home/<USER>/ram/quotas\">RAM page in the Service Quotas console</a>.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "PermissionName": {"type": "string", "max": 36, "min": 1, "pattern": "[\\w.-]*"}, "PermissionStatus": {"type": "string", "enum": ["ATTACHABLE", "UNATTACHABLE", "DELETING", "DELETED"]}, "PermissionType": {"type": "string", "enum": ["CUSTOMER_MANAGED", "AWS_MANAGED"]}, "PermissionTypeFilter": {"type": "string", "enum": ["ALL", "AWS_MANAGED", "CUSTOMER_MANAGED"]}, "PermissionVersionsLimitExceededException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The operation failed because it would exceed the limit for the number of versions you can have for a permission. To view the limits for your Amazon Web Services account, see the <a href=\"https://console.aws.amazon.com/servicequotas/home/<USER>/ram/quotas\">RAM page in the Service Quotas console</a>.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "Policy": {"type": "string"}, "PolicyList": {"type": "list", "member": {"shape": "Policy"}}, "Principal": {"type": "structure", "members": {"id": {"shape": "String", "documentation": "<p>The ID of the principal that can be associated with a resource share.</p>"}, "resourceShareArn": {"shape": "String", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of a resource share the principal is associated with.</p>"}, "creationTime": {"shape": "DateTime", "documentation": "<p>The date and time when the principal was associated with the resource share.</p>"}, "lastUpdatedTime": {"shape": "DateTime", "documentation": "<p>The date and time when the association between the resource share and the principal was last updated.</p>"}, "external": {"shape": "Boolean", "documentation": "<p>Indicates the relationship between the Amazon Web Services account the principal belongs to and the account that owns the resource share:</p> <ul> <li> <p> <code>True</code> – The two accounts belong to same organization.</p> </li> <li> <p> <code>False</code> – The two accounts do not belong to the same organization.</p> </li> </ul>"}}, "documentation": "<p>Describes a principal for use with Resource Access Manager.</p>"}, "PrincipalArnOrIdList": {"type": "list", "member": {"shape": "String"}}, "PrincipalList": {"type": "list", "member": {"shape": "Principal"}}, "PromotePermissionCreatedFromPolicyRequest": {"type": "structure", "required": ["permissionArn", "name"], "members": {"permissionArn": {"shape": "String", "documentation": "<p>Specifies the <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of the <code>CREATED_FROM_POLICY</code> permission that you want to promote. You can get this <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> by calling the <a>ListResourceSharePermissions</a> operation.</p>"}, "name": {"shape": "String", "documentation": "<p>Specifies a name for the promoted customer managed permission.</p>"}, "clientToken": {"shape": "String", "documentation": "<p>Specifies a unique, case-sensitive identifier that you provide to ensure the idempotency of the request. This lets you safely retry the request without accidentally performing the same operation a second time. Passing the same value to a later call to an operation requires that you also pass the same value for all other parameters. We recommend that you use a <a href=\"https://wikipedia.org/wiki/Universally_unique_identifier\">UUID type of value.</a>.</p> <p>If you don't provide this value, then Amazon Web Services generates a random one for you.</p> <p>If you retry the operation with the same <code>ClientToken</code>, but with different parameters, the retry fails with an <code>IdempotentParameterMismatch</code> error.</p>"}}}, "PromotePermissionCreatedFromPolicyResponse": {"type": "structure", "members": {"permission": {"shape": "ResourceSharePermissionSummary"}, "clientToken": {"shape": "String", "documentation": "<p>The idempotency identifier associated with this request. If you want to repeat the same operation in an idempotent manner then you must include this value in the <code>clientToken</code> request parameter of that later call. All other parameters must also have the same values that you used in the first call.</p>"}}}, "PromoteResourceShareCreatedFromPolicyRequest": {"type": "structure", "required": ["resourceShareArn"], "members": {"resourceShareArn": {"shape": "String", "documentation": "<p>Specifies the <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of the resource share to promote.</p>", "location": "querystring", "locationName": "resourceShareArn"}}}, "PromoteResourceShareCreatedFromPolicyResponse": {"type": "structure", "members": {"returnValue": {"shape": "Boolean", "documentation": "<p>A return value of <code>true</code> indicates that the request succeeded. A value of <code>false</code> indicates that the request failed.</p>"}}}, "RejectResourceShareInvitationRequest": {"type": "structure", "required": ["resourceShareInvitationArn"], "members": {"resourceShareInvitationArn": {"shape": "String", "documentation": "<p>Specifies the <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of the invitation that you want to reject.</p>"}, "clientToken": {"shape": "String", "documentation": "<p>Specifies a unique, case-sensitive identifier that you provide to ensure the idempotency of the request. This lets you safely retry the request without accidentally performing the same operation a second time. Passing the same value to a later call to an operation requires that you also pass the same value for all other parameters. We recommend that you use a <a href=\"https://wikipedia.org/wiki/Universally_unique_identifier\">UUID type of value.</a>.</p> <p>If you don't provide this value, then Amazon Web Services generates a random one for you.</p> <p>If you retry the operation with the same <code>ClientToken</code>, but with different parameters, the retry fails with an <code>IdempotentParameterMismatch</code> error.</p>"}}}, "RejectResourceShareInvitationResponse": {"type": "structure", "members": {"resourceShareInvitation": {"shape": "ResourceShareInvitation", "documentation": "<p>An object that contains the details about the rejected invitation.</p>"}, "clientToken": {"shape": "String", "documentation": "<p>The idempotency identifier associated with this request. If you want to repeat the same operation in an idempotent manner then you must include this value in the <code>clientToken</code> request parameter of that later call. All other parameters must also have the same values that you used in the first call.</p>"}}}, "ReplacePermissionAssociationsRequest": {"type": "structure", "required": ["fromPermissionArn", "toPermissionArn"], "members": {"fromPermissionArn": {"shape": "String", "documentation": "<p>Specifies the <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of the managed permission that you want to replace.</p>"}, "fromPermissionVersion": {"shape": "Integer", "documentation": "<p>Specifies that you want to updated the permissions for only those resource shares that use the specified version of the managed permission.</p>"}, "toPermissionArn": {"shape": "String", "documentation": "<p>Specifies the ARN of the managed permission that you want to associate with resource shares in place of the one specified by <code>fromPerssionArn</code> and <code>fromPermissionVersion</code>.</p> <p>The operation always associates the version that is currently the default for the specified managed permission.</p>"}, "clientToken": {"shape": "String", "documentation": "<p>Specifies a unique, case-sensitive identifier that you provide to ensure the idempotency of the request. This lets you safely retry the request without accidentally performing the same operation a second time. Passing the same value to a later call to an operation requires that you also pass the same value for all other parameters. We recommend that you use a <a href=\"https://wikipedia.org/wiki/Universally_unique_identifier\">UUID type of value.</a>.</p> <p>If you don't provide this value, then Amazon Web Services generates a random one for you.</p> <p>If you retry the operation with the same <code>ClientToken</code>, but with different parameters, the retry fails with an <code>IdempotentParameterMismatch</code> error.</p>"}}}, "ReplacePermissionAssociationsResponse": {"type": "structure", "members": {"replacePermissionAssociationsWork": {"shape": "ReplacePermissionAssociationsWork", "documentation": "<p>Specifies a data structure that you can use to track the asynchronous tasks that RAM performs to complete this operation. You can use the <a>ListReplacePermissionAssociationsWork</a> operation and pass the <code>id</code> value returned in this structure.</p>"}, "clientToken": {"shape": "String", "documentation": "<p>The idempotency identifier associated with this request. If you want to repeat the same operation in an idempotent manner then you must include this value in the <code>clientToken</code> request parameter of that later call. All other parameters must also have the same values that you used in the first call.</p>"}}}, "ReplacePermissionAssociationsWork": {"type": "structure", "members": {"id": {"shape": "String", "documentation": "<p>The unique identifier for the background task associated with one <a>ReplacePermissionAssociations</a> request.</p>"}, "fromPermissionArn": {"shape": "String", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of the managed permission that this background task is replacing.</p>"}, "fromPermissionVersion": {"shape": "String", "documentation": "<p>The version of the managed permission that this background task is replacing.</p>"}, "toPermissionArn": {"shape": "String", "documentation": "<p>The ARN of the managed permission that this background task is associating with the resource shares in place of the managed permission and version specified in <code>fromPermissionArn</code> and <code>fromPermissionVersion</code>.</p>"}, "toPermissionVersion": {"shape": "String", "documentation": "<p>The version of the managed permission that this background task is associating with the resource shares. This is always the version that is currently the default for this managed permission.</p>"}, "status": {"shape": "ReplacePermissionAssociationsWorkStatus", "documentation": "<p>Specifies the current status of the background tasks for the specified ID. The output is one of the following strings:</p> <ul> <li> <p> <code>IN_PROGRESS</code> </p> </li> <li> <p> <code>COMPLETED</code> </p> </li> <li> <p> <code>FAILED</code> </p> </li> </ul>"}, "statusMessage": {"shape": "String", "documentation": "<p>Specifies the reason for a <code>FAILED</code> status. This field is present only when there <code>status</code> is <code>FAILED</code>.</p>"}, "creationTime": {"shape": "DateTime", "documentation": "<p>The date and time when this asynchronous background task was created.</p>"}, "lastUpdatedTime": {"shape": "DateTime", "documentation": "<p>The date and time when the status of this background task was last updated.</p>"}}, "documentation": "<p>A structure that represents the background work that RAM performs when you invoke the <a>ReplacePermissionAssociations</a> operation.</p>"}, "ReplacePermissionAssociationsWorkIdList": {"type": "list", "member": {"shape": "String"}}, "ReplacePermissionAssociationsWorkList": {"type": "list", "member": {"shape": "ReplacePermissionAssociationsWork"}}, "ReplacePermissionAssociationsWorkStatus": {"type": "string", "enum": ["IN_PROGRESS", "COMPLETED", "FAILED"]}, "Resource": {"type": "structure", "members": {"arn": {"shape": "String", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of the resource.</p>"}, "type": {"shape": "String", "documentation": "<p>The resource type. This takes the form of: <code>service-code</code>:<code>resource-code</code>, and is case-insensitive. For example, an Amazon EC2 Subnet would be represented by the string <code>ec2:subnet</code>.</p>"}, "resourceShareArn": {"shape": "String", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of the resource share this resource is associated with.</p>"}, "resourceGroupArn": {"shape": "String", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of the resource group. This value is available only if the resource is part of a resource group.</p>"}, "status": {"shape": "ResourceStatus", "documentation": "<p>The current status of the resource.</p>"}, "statusMessage": {"shape": "String", "documentation": "<p>A message about the status of the resource.</p>"}, "creationTime": {"shape": "DateTime", "documentation": "<p>The date and time when the resource was associated with the resource share.</p>"}, "lastUpdatedTime": {"shape": "DateTime", "documentation": "<p>The date an time when the association between the resource and the resource share was last updated.</p>"}, "resourceRegionScope": {"shape": "ResourceRegionScope", "documentation": "<p>Specifies the scope of visibility of this resource:</p> <ul> <li> <p> <b>REGIONAL</b> – The resource can be accessed only by using requests that target the Amazon Web Services Region in which the resource exists.</p> </li> <li> <p> <b>GLOBAL</b> – The resource can be accessed from any Amazon Web Services Region.</p> </li> </ul>"}}, "documentation": "<p>Describes a resource associated with a resource share in RAM.</p>"}, "ResourceArnList": {"type": "list", "member": {"shape": "String"}}, "ResourceArnNotFoundException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The operation failed because the specified <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> was not found.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "ResourceList": {"type": "list", "member": {"shape": "Resource"}}, "ResourceOwner": {"type": "string", "enum": ["SELF", "OTHER-ACCOUNTS"]}, "ResourceRegionScope": {"type": "string", "enum": ["REGIONAL", "GLOBAL"]}, "ResourceRegionScopeFilter": {"type": "string", "enum": ["ALL", "REGIONAL", "GLOBAL"]}, "ResourceShare": {"type": "structure", "members": {"resourceShareArn": {"shape": "String", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of the resource share</p>"}, "name": {"shape": "String", "documentation": "<p>The name of the resource share.</p>"}, "owningAccountId": {"shape": "String", "documentation": "<p>The ID of the Amazon Web Services account that owns the resource share.</p>"}, "allowExternalPrincipals": {"shape": "Boolean", "documentation": "<p>Indicates whether principals outside your organization in Organizations can be associated with a resource share.</p> <ul> <li> <p> <code>True</code> – the resource share can be shared with any Amazon Web Services account.</p> </li> <li> <p> <code>False</code> – the resource share can be shared with only accounts in the same organization as the account that owns the resource share.</p> </li> </ul>"}, "status": {"shape": "ResourceShareStatus", "documentation": "<p>The current status of the resource share.</p>"}, "statusMessage": {"shape": "String", "documentation": "<p>A message about the status of the resource share.</p>"}, "tags": {"shape": "TagList", "documentation": "<p>The tag key and value pairs attached to the resource share.</p>"}, "creationTime": {"shape": "DateTime", "documentation": "<p>The date and time when the resource share was created.</p>"}, "lastUpdatedTime": {"shape": "DateTime", "documentation": "<p>The date and time when the resource share was last updated.</p>"}, "featureSet": {"shape": "ResourceShareFeatureSet", "documentation": "<p>Indicates what features are available for this resource share. This parameter can have one of the following values:</p> <ul> <li> <p> <b>STANDARD</b> – A resource share that supports all functionality. These resource shares are visible to all principals you share the resource share with. You can modify these resource shares in RAM using the console or APIs. This resource share might have been created by RAM, or it might have been <b>CREATED_FROM_POLICY</b> and then promoted.</p> </li> <li> <p> <b>CREATED_FROM_POLICY</b> – The customer manually shared a resource by attaching a resource-based policy. That policy did not match any existing managed permissions, so RAM created this customer managed permission automatically on the customer's behalf based on the attached policy document. This type of resource share is visible only to the Amazon Web Services account that created it. You can't modify it in RAM unless you promote it. For more information, see <a>PromoteResourceShareCreatedFromPolicy</a>.</p> </li> <li> <p> <b>PROMOTING_TO_STANDARD</b> – This resource share was originally <code>CREATED_FROM_POLICY</code>, but the customer ran the <a>PromoteResourceShareCreatedFromPolicy</a> and that operation is still in progress. This value changes to <code>STANDARD</code> when complete.</p> </li> </ul>"}}, "documentation": "<p>Describes a resource share in RAM.</p>"}, "ResourceShareArnList": {"type": "list", "member": {"shape": "String"}}, "ResourceShareAssociation": {"type": "structure", "members": {"resourceShareArn": {"shape": "String", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of the resource share.</p>"}, "resourceShareName": {"shape": "String", "documentation": "<p>The name of the resource share.</p>"}, "associatedEntity": {"shape": "String", "documentation": "<p>The associated entity. This can be either of the following:</p> <ul> <li> <p>For a resource association, this is the <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of the resource.</p> </li> <li> <p>For principal associations, this is one of the following:</p> <ul> <li> <p>The ID of an Amazon Web Services account</p> </li> <li> <p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of an organization in Organizations</p> </li> <li> <p>The ARN of an organizational unit (OU) in Organizations</p> </li> <li> <p>The ARN of an IAM role</p> </li> <li> <p>The ARN of an IAM user</p> </li> </ul> </li> </ul>"}, "associationType": {"shape": "ResourceShareAssociationType", "documentation": "<p>The type of entity included in this association.</p>"}, "status": {"shape": "ResourceShareAssociationStatus", "documentation": "<p>The current status of the association.</p>"}, "statusMessage": {"shape": "String", "documentation": "<p>A message about the status of the association.</p>"}, "creationTime": {"shape": "DateTime", "documentation": "<p>The date and time when the association was created.</p>"}, "lastUpdatedTime": {"shape": "DateTime", "documentation": "<p>The date and time when the association was last updated.</p>"}, "external": {"shape": "Boolean", "documentation": "<p>Indicates whether the principal belongs to the same organization in Organizations as the Amazon Web Services account that owns the resource share.</p>"}}, "documentation": "<p>Describes an association between a resource share and either a principal or a resource.</p>"}, "ResourceShareAssociationList": {"type": "list", "member": {"shape": "ResourceShareAssociation"}}, "ResourceShareAssociationStatus": {"type": "string", "enum": ["ASSOCIATING", "ASSOCIATED", "FAILED", "DISASSOCIATING", "DISASSOCIATED"]}, "ResourceShareAssociationType": {"type": "string", "enum": ["PRINCIPAL", "RESOURCE"]}, "ResourceShareFeatureSet": {"type": "string", "enum": ["CREATED_FROM_POLICY", "PROMOTING_TO_STANDARD", "STANDARD"]}, "ResourceShareInvitation": {"type": "structure", "members": {"resourceShareInvitationArn": {"shape": "String", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of the invitation.</p>"}, "resourceShareName": {"shape": "String", "documentation": "<p>The name of the resource share.</p>"}, "resourceShareArn": {"shape": "String", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of the resource share</p>"}, "senderAccountId": {"shape": "String", "documentation": "<p>The ID of the Amazon Web Services account that sent the invitation.</p>"}, "receiverAccountId": {"shape": "String", "documentation": "<p>The ID of the Amazon Web Services account that received the invitation.</p>"}, "invitationTimestamp": {"shape": "DateTime", "documentation": "<p>The date and time when the invitation was sent.</p>"}, "status": {"shape": "ResourceShareInvitationStatus", "documentation": "<p>The current status of the invitation.</p>"}, "resourceShareAssociations": {"shape": "ResourceShareAssociationList", "documentation": "<p>To view the resources associated with a pending resource share invitation, use <a>ListPendingInvitationResources</a>.</p>", "deprecated": true, "deprecatedMessage": "This member has been deprecated. Use ListPendingInvitationResources."}, "receiverArn": {"shape": "String", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of the IAM user or role that received the invitation.</p>"}}, "documentation": "<p>Describes an invitation for an Amazon Web Services account to join a resource share.</p>"}, "ResourceShareInvitationAlreadyAcceptedException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The operation failed because the specified invitation was already accepted.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "ResourceShareInvitationAlreadyRejectedException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The operation failed because the specified invitation was already rejected.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "ResourceShareInvitationArnList": {"type": "list", "member": {"shape": "String"}}, "ResourceShareInvitationArnNotFoundException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The operation failed because the specified <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> for an invitation was not found.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "ResourceShareInvitationExpiredException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The operation failed because the specified invitation is past its expiration date and time.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "ResourceShareInvitationList": {"type": "list", "member": {"shape": "ResourceShareInvitation"}}, "ResourceShareInvitationStatus": {"type": "string", "enum": ["PENDING", "ACCEPTED", "REJECTED", "EXPIRED"]}, "ResourceShareLimitExceededException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The operation failed because it would exceed the limit for resource shares for your account. To view the limits for your Amazon Web Services account, see the <a href=\"https://console.aws.amazon.com/servicequotas/home/<USER>/ram/quotas\">RAM page in the Service Quotas console</a>.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "ResourceShareList": {"type": "list", "member": {"shape": "ResourceShare"}}, "ResourceSharePermissionDetail": {"type": "structure", "members": {"arn": {"shape": "String", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of this RAM managed permission.</p>"}, "version": {"shape": "String", "documentation": "<p>The version of the permission described in this response.</p>"}, "defaultVersion": {"shape": "Boolean", "documentation": "<p>Specifies whether the version of the permission represented in this response is the default version for this permission.</p>"}, "name": {"shape": "String", "documentation": "<p>The name of this permission.</p>"}, "resourceType": {"shape": "String", "documentation": "<p>The resource type to which this permission applies.</p>"}, "permission": {"shape": "String", "documentation": "<p>The permission's effect and actions in JSON format. The <code>effect</code> indicates whether the specified actions are allowed or denied. The <code>actions</code> list the operations to which the principal is granted or denied access.</p>"}, "creationTime": {"shape": "DateTime", "documentation": "<p>The date and time when the permission was created.</p>"}, "lastUpdatedTime": {"shape": "DateTime", "documentation": "<p>The date and time when the permission was last updated.</p>"}, "isResourceTypeDefault": {"shape": "Boolean", "documentation": "<p>Specifies whether the version of the permission represented in this response is the default version for all resources of this resource type.</p>"}, "permissionType": {"shape": "PermissionType", "documentation": "<p>The type of managed permission. This can be one of the following values:</p> <ul> <li> <p> <code>AWS_MANAGED</code> – Amazon Web Services created and manages this managed permission. You can associate it with your resource shares, but you can't modify it.</p> </li> <li> <p> <code>CUSTOMER_MANAGED</code> – You, or another principal in your account created this managed permission. You can associate it with your resource shares and create new versions that have different permissions.</p> </li> </ul>"}, "featureSet": {"shape": "PermissionFeatureSet", "documentation": "<p>Indicates what features are available for this resource share. This parameter can have one of the following values:</p> <ul> <li> <p> <b>STANDARD</b> – A resource share that supports all functionality. These resource shares are visible to all principals you share the resource share with. You can modify these resource shares in RAM using the console or APIs. This resource share might have been created by RAM, or it might have been <b>CREATED_FROM_POLICY</b> and then promoted.</p> </li> <li> <p> <b>CREATED_FROM_POLICY</b> – The customer manually shared a resource by attaching a resource-based policy. That policy did not match any existing managed permissions, so RAM created this customer managed permission automatically on the customer's behalf based on the attached policy document. This type of resource share is visible only to the Amazon Web Services account that created it. You can't modify it in RAM unless you promote it. For more information, see <a>PromoteResourceShareCreatedFromPolicy</a>.</p> </li> <li> <p> <b>PROMOTING_TO_STANDARD</b> – This resource share was originally <code>CREATED_FROM_POLICY</code>, but the customer ran the <a>PromoteResourceShareCreatedFromPolicy</a> and that operation is still in progress. This value changes to <code>STANDARD</code> when complete.</p> </li> </ul>"}, "status": {"shape": "PermissionStatus", "documentation": "<p>The current status of the association between the permission and the resource share. The following are the possible values:</p> <ul> <li> <p> <code>ATTACHABLE</code> – This permission or version can be associated with resource shares.</p> </li> <li> <p> <code>UNATTACHABLE</code> – This permission or version can't currently be associated with resource shares.</p> </li> <li> <p> <code>DELETING</code> – This permission or version is in the process of being deleted.</p> </li> <li> <p> <code>DELETED</code> – This permission or version is deleted.</p> </li> </ul>"}, "tags": {"shape": "TagList", "documentation": "<p>The tag key and value pairs attached to the resource share.</p>"}}, "documentation": "<p>Information about a RAM managed permission.</p>"}, "ResourceSharePermissionList": {"type": "list", "member": {"shape": "ResourceSharePermissionSummary"}}, "ResourceSharePermissionSummary": {"type": "structure", "members": {"arn": {"shape": "String", "documentation": "<p>The <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of the permission you want information about.</p>"}, "version": {"shape": "String", "documentation": "<p>The version of the permission associated with this resource share.</p>"}, "defaultVersion": {"shape": "Boolean", "documentation": "<p>Specifies whether the version of the managed permission used by this resource share is the default version for this managed permission.</p>"}, "name": {"shape": "String", "documentation": "<p>The name of this managed permission.</p>"}, "resourceType": {"shape": "String", "documentation": "<p>The type of resource to which this permission applies. This takes the form of: <code>service-code</code>:<code>resource-code</code>, and is case-insensitive. For example, an Amazon EC2 Subnet would be represented by the string <code>ec2:subnet</code>.</p>"}, "status": {"shape": "String", "documentation": "<p>The current status of the permission.</p>"}, "creationTime": {"shape": "DateTime", "documentation": "<p>The date and time when the permission was created.</p>"}, "lastUpdatedTime": {"shape": "DateTime", "documentation": "<p>The date and time when the permission was last updated.</p>"}, "isResourceTypeDefault": {"shape": "Boolean", "documentation": "<p>Specifies whether the managed permission associated with this resource share is the default managed permission for all resources of this resource type.</p>"}, "permissionType": {"shape": "PermissionType", "documentation": "<p>The type of managed permission. This can be one of the following values:</p> <ul> <li> <p> <code>AWS_MANAGED</code> – Amazon Web Services created and manages this managed permission. You can associate it with your resource shares, but you can't modify it.</p> </li> <li> <p> <code>CUSTOMER_MANAGED</code> – You, or another principal in your account created this managed permission. You can associate it with your resource shares and create new versions that have different permissions.</p> </li> </ul>"}, "featureSet": {"shape": "PermissionFeatureSet", "documentation": "<p>Indicates what features are available for this resource share. This parameter can have one of the following values:</p> <ul> <li> <p> <b>STANDARD</b> – A resource share that supports all functionality. These resource shares are visible to all principals you share the resource share with. You can modify these resource shares in RAM using the console or APIs. This resource share might have been created by RAM, or it might have been <b>CREATED_FROM_POLICY</b> and then promoted.</p> </li> <li> <p> <b>CREATED_FROM_POLICY</b> – The customer manually shared a resource by attaching a resource-based policy. That policy did not match any existing managed permissions, so RAM created this customer managed permission automatically on the customer's behalf based on the attached policy document. This type of resource share is visible only to the Amazon Web Services account that created it. You can't modify it in RAM unless you promote it. For more information, see <a>PromoteResourceShareCreatedFromPolicy</a>.</p> </li> <li> <p> <b>PROMOTING_TO_STANDARD</b> – This resource share was originally <code>CREATED_FROM_POLICY</code>, but the customer ran the <a>PromoteResourceShareCreatedFromPolicy</a> and that operation is still in progress. This value changes to <code>STANDARD</code> when complete.</p> </li> </ul>"}, "tags": {"shape": "TagList", "documentation": "<p>A list of the tag key value pairs currently attached to the permission.</p>"}}, "documentation": "<p>Information about an RAM permission.</p>"}, "ResourceShareStatus": {"type": "string", "enum": ["PENDING", "ACTIVE", "FAILED", "DELETING", "DELETED"]}, "ResourceStatus": {"type": "string", "enum": ["AVAILABLE", "ZONAL_RESOURCE_INACCESSIBLE", "LIMIT_EXCEEDED", "UNAVAILABLE", "PENDING"]}, "ServerInternalException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The operation failed because the service could not respond to the request due to an internal problem. Try again later.</p>", "error": {"httpStatusCode": 500}, "exception": true}, "ServiceNameAndResourceType": {"type": "structure", "members": {"resourceType": {"shape": "String", "documentation": "<p>The type of the resource. This takes the form of: <code>service-code</code>:<code>resource-code</code>, and is case-insensitive. For example, an Amazon EC2 Subnet would be represented by the string <code>ec2:subnet</code>.</p>"}, "serviceName": {"shape": "String", "documentation": "<p>The name of the Amazon Web Services service to which resources of this type belong.</p>"}, "resourceRegionScope": {"shape": "ResourceRegionScope", "documentation": "<p>Specifies the scope of visibility of resources of this type:</p> <ul> <li> <p> <b>REGIONAL</b> – The resource can be accessed only by using requests that target the Amazon Web Services Region in which the resource exists.</p> </li> <li> <p> <b>GLOBAL</b> – The resource can be accessed from any Amazon Web Services Region.</p> </li> </ul>"}}, "documentation": "<p>Information about a shareable resource type and the Amazon Web Services service to which resources of that type belong.</p>"}, "ServiceNameAndResourceTypeList": {"type": "list", "member": {"shape": "ServiceNameAndResourceType"}}, "ServiceUnavailableException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The operation failed because the service isn't available. Try again later.</p>", "error": {"httpStatusCode": 503}, "exception": true}, "SetDefaultPermissionVersionRequest": {"type": "structure", "required": ["permissionArn", "permissionVersion"], "members": {"permissionArn": {"shape": "String", "documentation": "<p>Specifies the <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of the customer managed permission whose default version you want to change.</p>"}, "permissionVersion": {"shape": "Integer", "documentation": "<p>Specifies the version number that you want to designate as the default for customer managed permission. To see a list of all available version numbers, use <a>ListPermissionVersions</a>.</p>"}, "clientToken": {"shape": "String", "documentation": "<p>Specifies a unique, case-sensitive identifier that you provide to ensure the idempotency of the request. This lets you safely retry the request without accidentally performing the same operation a second time. Passing the same value to a later call to an operation requires that you also pass the same value for all other parameters. We recommend that you use a <a href=\"https://wikipedia.org/wiki/Universally_unique_identifier\">UUID type of value.</a>.</p> <p>If you don't provide this value, then Amazon Web Services generates a random one for you.</p> <p>If you retry the operation with the same <code>ClientToken</code>, but with different parameters, the retry fails with an <code>IdempotentParameterMismatch</code> error.</p>"}}}, "SetDefaultPermissionVersionResponse": {"type": "structure", "members": {"returnValue": {"shape": "Boolean", "documentation": "<p>A boolean value that indicates whether the operation was successful.</p>"}, "clientToken": {"shape": "String", "documentation": "<p>The idempotency identifier associated with this request. If you want to repeat the same operation in an idempotent manner then you must include this value in the <code>clientToken</code> request parameter of that later call. All other parameters must also have the same values that you used in the first call.</p>"}}}, "SourceArnOrAccountList": {"type": "list", "member": {"shape": "String"}}, "String": {"type": "string"}, "Tag": {"type": "structure", "members": {"key": {"shape": "TagKey", "documentation": "<p>The key, or name, attached to the tag. Every tag must have a key. Key names are case sensitive.</p>"}, "value": {"shape": "TagValue", "documentation": "<p>The string value attached to the tag. The value can be an empty string. Key values are case sensitive.</p>"}}, "documentation": "<p>A structure containing a tag. A tag is metadata that you can attach to your resources to help organize and categorize them. You can also use them to help you secure your resources. For more information, see <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/access_tags.html\">Controlling access to Amazon Web Services resources using tags</a>.</p> <p>For more information about tags, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws_tagging.html\">Tagging Amazon Web Services resources</a> in the <i>Amazon Web Services General Reference Guide</i>.</p>"}, "TagFilter": {"type": "structure", "members": {"tagKey": {"shape": "TagKey", "documentation": "<p>The tag key. This must have a valid string value and can't be empty.</p>"}, "tagValues": {"shape": "TagValueList", "documentation": "<p>A list of zero or more tag values. If no values are provided, then the filter matches any tag with the specified key, regardless of its value.</p>"}}, "documentation": "<p>A tag key and optional list of possible values that you can use to filter results for tagged resources.</p>"}, "TagFilters": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON><PERSON>"}}, "TagKey": {"type": "string"}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}}, "TagLimitExceededException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The operation failed because it would exceed the limit for tags for your Amazon Web Services account.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "TagList": {"type": "list", "member": {"shape": "Tag"}}, "TagPolicyViolationException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The operation failed because the specified tag key is a reserved word and can't be used.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "TagResourceRequest": {"type": "structure", "required": ["tags"], "members": {"resourceShareArn": {"shape": "String", "documentation": "<p>Specifies the <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of the resource share that you want to add tags to. You must specify <i>either</i> <code>resourceShareArn</code>, or <code>resourceArn</code>, but not both.</p>"}, "tags": {"shape": "TagList", "documentation": "<p>A list of one or more tag key and value pairs. The tag key must be present and not be an empty string. The tag value must be present but can be an empty string.</p>"}, "resourceArn": {"shape": "String", "documentation": "<p>Specifies the <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of the managed permission that you want to add tags to. You must specify <i>either</i> <code>resourceArn</code>, or <code>resourceShareArn</code>, but not both.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string"}, "TagValueList": {"type": "list", "member": {"shape": "TagValue"}}, "ThrottlingException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The operation failed because it exceeded the rate at which you are allowed to perform this operation. Please try again later.</p>", "error": {"httpStatusCode": 429}, "exception": true}, "UnknownResourceException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The operation failed because a specified resource couldn't be found.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "UnmatchedPolicyPermissionException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>There isn't an existing managed permission defined in RAM that has the same IAM permissions as the resource-based policy attached to the resource. You should first run <a>PromotePermissionCreatedFromPolicy</a> to create that managed permission.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "UntagResourceRequest": {"type": "structure", "required": ["tagKeys"], "members": {"resourceShareArn": {"shape": "String", "documentation": "<p>Specifies the <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of the resource share that you want to remove tags from. The tags are removed from the resource share, not the resources in the resource share. You must specify either <code>resourceShareArn</code>, or <code>resourceArn</code>, but not both.</p>"}, "tagKeys": {"shape": "TagKeyList", "documentation": "<p>Specifies a list of one or more tag keys that you want to remove.</p>"}, "resourceArn": {"shape": "String", "documentation": "<p>Specifies the <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of the managed permission that you want to remove tags from. You must specify either <code>resourceArn</code>, or <code>resourceShareArn</code>, but not both.</p>"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateResourceShareRequest": {"type": "structure", "required": ["resourceShareArn"], "members": {"resourceShareArn": {"shape": "String", "documentation": "<p>Specifies the <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html\">Amazon Resource Name (ARN)</a> of the resource share that you want to modify.</p>"}, "name": {"shape": "String", "documentation": "<p>If specified, the new name that you want to attach to the resource share.</p>"}, "allowExternalPrincipals": {"shape": "Boolean", "documentation": "<p>Specifies whether principals outside your organization in Organizations can be associated with a resource share.</p>"}, "clientToken": {"shape": "String", "documentation": "<p>Specifies a unique, case-sensitive identifier that you provide to ensure the idempotency of the request. This lets you safely retry the request without accidentally performing the same operation a second time. Passing the same value to a later call to an operation requires that you also pass the same value for all other parameters. We recommend that you use a <a href=\"https://wikipedia.org/wiki/Universally_unique_identifier\">UUID type of value.</a>.</p> <p>If you don't provide this value, then Amazon Web Services generates a random one for you.</p> <p>If you retry the operation with the same <code>ClientToken</code>, but with different parameters, the retry fails with an <code>IdempotentParameterMismatch</code> error.</p>"}}}, "UpdateResourceShareResponse": {"type": "structure", "members": {"resourceShare": {"shape": "ResourceShare", "documentation": "<p>Information about the resource share.</p>"}, "clientToken": {"shape": "String", "documentation": "<p>The idempotency identifier associated with this request. If you want to repeat the same operation in an idempotent manner then you must include this value in the <code>clientToken</code> request parameter of that later call. All other parameters must also have the same values that you used in the first call.</p>"}}}}, "documentation": "<p>This is the <i>Resource Access Manager API Reference</i>. This documentation provides descriptions and syntax for each of the actions and data types in RAM. RAM is a service that helps you securely share your Amazon Web Services resources to other Amazon Web Services accounts. If you use Organizations to manage your accounts, then you can share your resources with your entire organization or to organizational units (OUs). For supported resource types, you can also share resources with individual Identity and Access Management (IAM) roles and users. </p> <p>To learn more about RAM, see the following resources:</p> <ul> <li> <p> <a href=\"http://aws.amazon.com/ram\">Resource Access Manager product page</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/ram/latest/userguide/\">Resource Access Manager User Guide</a> </p> </li> </ul>"}