{"version": "2.0", "metadata": {"apiVersion": "2020-11-12", "endpointPrefix": "network-firewall", "jsonVersion": "1.0", "protocol": "json", "protocols": ["json"], "serviceAbbreviation": "Network Firewall", "serviceFullName": "AWS Network Firewall", "serviceId": "Network Firewall", "signatureVersion": "v4", "signingName": "network-firewall", "targetPrefix": "NetworkFirewall_20201112", "uid": "network-firewall-2020-11-12", "auth": ["aws.auth#sigv4"]}, "operations": {"AcceptNetworkFirewallTransitGatewayAttachment": {"name": "AcceptNetworkFirewallTransitGatewayAttachment", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AcceptNetworkFirewallTransitGatewayAttachmentRequest"}, "output": {"shape": "AcceptNetworkFirewallTransitGatewayAttachmentResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServerError"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Accepts a transit gateway attachment request for Network Firewall. When you accept the attachment request, Network Firewall creates the necessary routing components to enable traffic flow between the transit gateway and firewall endpoints.</p> <p>You must accept a transit gateway attachment to complete the creation of a transit gateway-attached firewall, unless auto-accept is enabled on the transit gateway. After acceptance, use <a>DescribeFirewall</a> to verify the firewall status.</p> <p>To reject an attachment instead of accepting it, use <a>RejectNetworkFirewallTransitGatewayAttachment</a>.</p> <note> <p>It can take several minutes for the attachment acceptance to complete and the firewall to become available.</p> </note>"}, "AssociateAvailabilityZones": {"name": "AssociateAvailabilityZones", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AssociateAvailabilityZonesRequest"}, "output": {"shape": "AssociateAvailabilityZonesResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServerError"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InvalidTokenException"}, {"shape": "InvalidOperationException"}, {"shape": "InsufficientCapacityException"}], "documentation": "<p>Associates the specified Availability Zones with a transit gateway-attached firewall. For each Availability Zone, Network Firewall creates a firewall endpoint to process traffic. You can specify one or more Availability Zones where you want to deploy the firewall.</p> <p>After adding Availability Zones, you must update your transit gateway route tables to direct traffic through the new firewall endpoints. Use <a>DescribeFirewall</a> to monitor the status of the new endpoints.</p>"}, "AssociateFirewallPolicy": {"name": "AssociateFirewallPolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AssociateFirewallPolicyRequest"}, "output": {"shape": "AssociateFirewallPolicyResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServerError"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InvalidTokenException"}, {"shape": "InvalidOperationException"}], "documentation": "<p>Associates a <a>FirewallPolicy</a> to a <a>Firewall</a>. </p> <p>A firewall policy defines how to monitor and manage your VPC network traffic, using a collection of inspection rule groups and other settings. Each firewall requires one firewall policy association, and you can use the same firewall policy for multiple firewalls. </p>"}, "AssociateSubnets": {"name": "AssociateSubnets", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AssociateSubnetsRequest"}, "output": {"shape": "AssociateSubnetsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServerError"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InvalidTokenException"}, {"shape": "InvalidOperationException"}, {"shape": "InsufficientCapacityException"}], "documentation": "<p>Associates the specified subnets in the Amazon VPC to the firewall. You can specify one subnet for each of the Availability Zones that the VPC spans. </p> <p>This request creates an Network Firewall firewall endpoint in each of the subnets. To enable the firewall's protections, you must also modify the VPC's route tables for each subnet's Availability Zone, to redirect the traffic that's coming into and going out of the zone through the firewall endpoint. </p>"}, "CreateFirewall": {"name": "CreateFirewall", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateFirewallRequest"}, "output": {"shape": "CreateFirewallResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "LimitExceededException"}, {"shape": "InternalServerError"}, {"shape": "ThrottlingException"}, {"shape": "InsufficientCapacityException"}, {"shape": "InvalidOperationException"}], "documentation": "<p>Creates an Network Firewall <a>Firewall</a> and accompanying <a>FirewallStatus</a> for a VPC. </p> <p>The firewall defines the configuration settings for an Network Firewall firewall. The settings that you can define at creation include the firewall policy, the subnets in your VPC to use for the firewall endpoints, and any tags that are attached to the firewall Amazon Web Services resource. </p> <p>After you create a firewall, you can provide additional settings, like the logging configuration. </p> <p>To update the settings for a firewall, you use the operations that apply to the settings themselves, for example <a>UpdateLoggingConfiguration</a>, <a>AssociateSubnets</a>, and <a>UpdateFirewallDeleteProtection</a>. </p> <p>To manage a firewall's tags, use the standard Amazon Web Services resource tagging operations, <a>ListTagsForResource</a>, <a>TagResource</a>, and <a>UntagResource</a>.</p> <p>To retrieve information about firewalls, use <a>ListFirewalls</a> and <a>DescribeFirewall</a>.</p> <p>To generate a report on the last 30 days of traffic monitored by a firewall, use <a>StartAnalysisReport</a>.</p>"}, "CreateFirewallPolicy": {"name": "CreateFirewallPolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateFirewallPolicyRequest"}, "output": {"shape": "CreateFirewallPolicyResponse"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "InvalidRequestException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerError"}, {"shape": "InsufficientCapacityException"}], "documentation": "<p>Creates the firewall policy for the firewall according to the specifications. </p> <p>An Network Firewall firewall policy defines the behavior of a firewall, in a collection of stateless and stateful rule groups and other settings. You can use one firewall policy for multiple firewalls. </p>"}, "CreateRuleGroup": {"name": "CreateRuleGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateRuleGroupRequest"}, "output": {"shape": "CreateRuleGroupResponse"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "InvalidRequestException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerError"}, {"shape": "InsufficientCapacityException"}], "documentation": "<p>Creates the specified stateless or stateful rule group, which includes the rules for network traffic inspection, a capacity setting, and tags. </p> <p>You provide your rule group specification in your request using either <code>RuleGroup</code> or <code>Rules</code>.</p>"}, "CreateTLSInspectionConfiguration": {"name": "CreateTLSInspectionConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateTLSInspectionConfigurationRequest"}, "output": {"shape": "CreateTLSInspectionConfigurationResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerError"}, {"shape": "LimitExceededException"}, {"shape": "InsufficientCapacityException"}], "documentation": "<p>Creates an Network Firewall TLS inspection configuration. Network Firewall uses TLS inspection configurations to decrypt your firewall's inbound and outbound SSL/TLS traffic. After decryption, Network Firewall inspects the traffic according to your firewall policy's stateful rules, and then re-encrypts it before sending it to its destination. You can enable inspection of your firewall's inbound traffic, outbound traffic, or both. To use TLS inspection with your firewall, you must first import or provision certificates using ACM, create a TLS inspection configuration, add that configuration to a new firewall policy, and then associate that policy with your firewall.</p> <p>To update the settings for a TLS inspection configuration, use <a>UpdateTLSInspectionConfiguration</a>.</p> <p>To manage a TLS inspection configuration's tags, use the standard Amazon Web Services resource tagging operations, <a>ListTagsForResource</a>, <a>TagResource</a>, and <a>UntagResource</a>.</p> <p>To retrieve information about TLS inspection configurations, use <a>ListTLSInspectionConfigurations</a> and <a>DescribeTLSInspectionConfiguration</a>.</p> <p> For more information about TLS inspection configurations, see <a href=\"https://docs.aws.amazon.com/network-firewall/latest/developerguide/tls-inspection.html\">Inspecting SSL/TLS traffic with TLS inspection configurations</a> in the <i>Network Firewall Developer Guide</i>. </p>"}, "CreateVpcEndpointAssociation": {"name": "CreateVpcEndpointAssociation", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateVpcEndpointAssociationRequest"}, "output": {"shape": "CreateVpcEndpointAssociationResponse"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "InvalidRequestException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerError"}, {"shape": "InsufficientCapacityException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidOperationException"}], "documentation": "<p>Creates a firewall endpoint for an Network Firewall firewall. This type of firewall endpoint is independent of the firewall endpoints that you specify in the <code>Firewall</code> itself, and you define it in addition to those endpoints after the firewall has been created. You can define a VPC endpoint association using a different VPC than the one you used in the firewall specifications. </p>"}, "DeleteFirewall": {"name": "DeleteFirewall", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteFirewallRequest"}, "output": {"shape": "DeleteFirewallResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServerError"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "UnsupportedOperationException"}, {"shape": "InvalidOperationException"}], "documentation": "<p>Deletes the specified <a>Firewall</a> and its <a>FirewallStatus</a>. This operation requires the firewall's <code>DeleteProtection</code> flag to be <code>FALSE</code>. You can't revert this operation. </p> <p>You can check whether a firewall is in use by reviewing the route tables for the Availability Zones where you have firewall subnet mappings. Retrieve the subnet mappings by calling <a>DescribeFirewall</a>. You define and update the route tables through Amazon VPC. As needed, update the route tables for the zones to remove the firewall endpoints. When the route tables no longer use the firewall endpoints, you can remove the firewall safely.</p> <p>To delete a firewall, remove the delete protection if you need to using <a>UpdateFirewallDeleteProtection</a>, then delete the firewall by calling <a>DeleteFirewall</a>. </p>"}, "DeleteFirewallPolicy": {"name": "DeleteFirewallPolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteFirewallPolicyRequest"}, "output": {"shape": "DeleteFirewallPolicyResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerError"}, {"shape": "UnsupportedOperationException"}, {"shape": "InvalidOperationException"}], "documentation": "<p>Deletes the specified <a>FirewallPolicy</a>. </p>"}, "DeleteNetworkFirewallTransitGatewayAttachment": {"name": "DeleteNetworkFirewallTransitGatewayAttachment", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteNetworkFirewallTransitGatewayAttachmentRequest"}, "output": {"shape": "DeleteNetworkFirewallTransitGatewayAttachmentResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServerError"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Deletes a transit gateway attachment from a Network Firewall. Either the firewall owner or the transit gateway owner can delete the attachment.</p> <important> <p>After you delete a transit gateway attachment, raffic will no longer flow through the firewall endpoints.</p> </important> <p>After you initiate the delete operation, use <a>DescribeFirewall</a> to monitor the deletion status.</p>"}, "DeleteResourcePolicy": {"name": "DeleteResourcePolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteResourcePolicyRequest"}, "output": {"shape": "DeleteResourcePolicyResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServerError"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InvalidResourcePolicyException"}], "documentation": "<p>Deletes a resource policy that you created in a <a>PutResourcePolicy</a> request. </p>"}, "DeleteRuleGroup": {"name": "DeleteRuleGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteRuleGroupRequest"}, "output": {"shape": "DeleteRuleGroupResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerError"}, {"shape": "UnsupportedOperationException"}, {"shape": "InvalidOperationException"}], "documentation": "<p>Deletes the specified <a>RuleGroup</a>. </p>"}, "DeleteTLSInspectionConfiguration": {"name": "DeleteTLSInspectionConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteTLSInspectionConfigurationRequest"}, "output": {"shape": "DeleteTLSInspectionConfigurationResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServerError"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InvalidOperationException"}], "documentation": "<p>Deletes the specified <a>TLSInspectionConfiguration</a>.</p>"}, "DeleteVpcEndpointAssociation": {"name": "DeleteVpcEndpointAssociation", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteVpcEndpointAssociationRequest"}, "output": {"shape": "DeleteVpcEndpointAssociationResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServerError"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InvalidOperationException"}], "documentation": "<p>Deletes the specified <a>VpcEndpointAssociation</a>.</p> <p>You can check whether an endpoint association is in use by reviewing the route tables for the Availability Zones where you have the endpoint subnet mapping. You can retrieve the subnet mapping by calling <a>DescribeVpcEndpointAssociation</a>. You define and update the route tables through Amazon VPC. As needed, update the route tables for the Availability Zone to remove the firewall endpoint for the association. When the route tables no longer use the firewall endpoint, you can remove the endpoint association safely.</p>"}, "DescribeFirewall": {"name": "DescribeFirewall", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeFirewallRequest"}, "output": {"shape": "DescribeFirewallResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServerError"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Returns the data objects for the specified firewall. </p>"}, "DescribeFirewallMetadata": {"name": "DescribeFirewallMetadata", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeFirewallMetadataRequest"}, "output": {"shape": "DescribeFirewallMetadataResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServerError"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Returns the high-level information about a firewall, including the Availability Zones where the Firewall is currently in use. </p>"}, "DescribeFirewallPolicy": {"name": "DescribeFirewallPolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeFirewallPolicyRequest"}, "output": {"shape": "DescribeFirewallPolicyResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerError"}], "documentation": "<p>Returns the data objects for the specified firewall policy. </p>"}, "DescribeFlowOperation": {"name": "DescribeFlowOperation", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeFlowOperationRequest"}, "output": {"shape": "DescribeFlowOperationResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServerError"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Returns key information about a specific flow operation.</p>"}, "DescribeLoggingConfiguration": {"name": "DescribeLoggingConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeLoggingConfigurationRequest"}, "output": {"shape": "DescribeLoggingConfigurationResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServerError"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Returns the logging configuration for the specified firewall. </p>"}, "DescribeResourcePolicy": {"name": "DescribeResourcePolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeResourcePolicyRequest"}, "output": {"shape": "DescribeResourcePolicyResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServerError"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Retrieves a resource policy that you created in a <a>PutResourcePolicy</a> request. </p>"}, "DescribeRuleGroup": {"name": "DescribeRuleGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeRuleGroupRequest"}, "output": {"shape": "DescribeRuleGroupResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerError"}], "documentation": "<p>Returns the data objects for the specified rule group. </p>"}, "DescribeRuleGroupMetadata": {"name": "DescribeRuleGroupMetadata", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeRuleGroupMetadataRequest"}, "output": {"shape": "DescribeRuleGroupMetadataResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerError"}], "documentation": "<p>High-level information about a rule group, returned by operations like create and describe. You can use the information provided in the metadata to retrieve and manage a rule group. You can retrieve all objects for a rule group by calling <a>DescribeRuleGroup</a>. </p>"}, "DescribeRuleGroupSummary": {"name": "DescribeRuleGroupSummary", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeRuleGroupSummaryRequest"}, "output": {"shape": "DescribeRuleGroupSummaryResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerError"}], "documentation": "<p>Returns detailed information for a stateful rule group.</p> <p>For active threat defense Amazon Web Services managed rule groups, this operation provides insight into the protections enabled by the rule group, based on Suricata rule metadata fields. Summaries are available for rule groups you manage and for active threat defense Amazon Web Services managed rule groups.</p> <p>To modify how threat information appears in summaries, use the <code>SummaryConfiguration</code> parameter in <a>UpdateRuleGroup</a>.</p>"}, "DescribeTLSInspectionConfiguration": {"name": "DescribeTLSInspectionConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeTLSInspectionConfigurationRequest"}, "output": {"shape": "DescribeTLSInspectionConfigurationResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServerError"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Returns the data objects for the specified TLS inspection configuration.</p>"}, "DescribeVpcEndpointAssociation": {"name": "DescribeVpcEndpointAssociation", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeVpcEndpointAssociationRequest"}, "output": {"shape": "DescribeVpcEndpointAssociationResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServerError"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Returns the data object for the specified VPC endpoint association. </p>"}, "DisassociateAvailabilityZones": {"name": "DisassociateAvailabilityZones", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DisassociateAvailabilityZonesRequest"}, "output": {"shape": "DisassociateAvailabilityZonesResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServerError"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InvalidTokenException"}, {"shape": "InvalidOperationException"}], "documentation": "<p>Removes the specified Availability Zone associations from a transit gateway-attached firewall. This removes the firewall endpoints from these Availability Zones and stops traffic filtering in those zones. Before removing an Availability Zone, ensure you've updated your transit gateway route tables to redirect traffic appropriately.</p> <note> <p>If <code>AvailabilityZoneChangeProtection</code> is enabled, you must first disable it using <a>UpdateAvailabilityZoneChangeProtection</a>.</p> </note> <p>To verify the status of your Availability Zone changes, use <a>DescribeFirewall</a>.</p>"}, "DisassociateSubnets": {"name": "DisassociateSubnets", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DisassociateSubnetsRequest"}, "output": {"shape": "DisassociateSubnetsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServerError"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InvalidTokenException"}, {"shape": "InvalidOperationException"}], "documentation": "<p>Removes the specified subnet associations from the firewall. This removes the firewall endpoints from the subnets and removes any network filtering protections that the endpoints were providing. </p>"}, "GetAnalysisReportResults": {"name": "GetAnalysisReportResults", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetAnalysisReportResultsRequest"}, "output": {"shape": "GetAnalysisReportResultsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServerError"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>The results of a <code>COMPLETED</code> analysis report generated with <a>StartAnalysisReport</a>.</p> <p>For more information, see <a>AnalysisTypeReportResult</a>. </p>"}, "ListAnalysisReports": {"name": "ListAnalysisReports", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListAnalysisReportsRequest"}, "output": {"shape": "ListAnalysisReportsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServerError"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Returns a list of all traffic analysis reports generated within the last 30 days.</p>"}, "ListFirewallPolicies": {"name": "ListFirewallPolicies", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListFirewallPoliciesRequest"}, "output": {"shape": "ListFirewallPoliciesResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerError"}], "documentation": "<p>Retrieves the metadata for the firewall policies that you have defined. Depending on your setting for max results and the number of firewall policies, a single call might not return the full list. </p>"}, "ListFirewalls": {"name": "ListFirewalls", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListFirewallsRequest"}, "output": {"shape": "ListFirewallsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServerError"}, {"shape": "ThrottlingException"}], "documentation": "<p>Retrieves the metadata for the firewalls that you have defined. If you provide VPC identifiers in your request, this returns only the firewalls for those VPCs.</p> <p>Depending on your setting for max results and the number of firewalls, a single call might not return the full list. </p>"}, "ListFlowOperationResults": {"name": "ListFlowOperationResults", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListFlowOperationResultsRequest"}, "output": {"shape": "ListFlowOperationResultsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServerError"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Returns the results of a specific flow operation. </p> <p>Flow operations let you manage the flows tracked in the flow table, also known as the firewall table.</p> <p>A flow is network traffic that is monitored by a firewall, either by stateful or stateless rules. For traffic to be considered part of a flow, it must share Destination, DestinationPort, Direction, Protocol, Source, and SourcePort. </p>"}, "ListFlowOperations": {"name": "ListFlowOperations", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListFlowOperationsRequest"}, "output": {"shape": "ListFlowOperationsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServerError"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Returns a list of all flow operations ran in a specific firewall. You can optionally narrow the request scope by specifying the operation type or Availability Zone associated with a firewall's flow operations. </p> <p>Flow operations let you manage the flows tracked in the flow table, also known as the firewall table.</p> <p>A flow is network traffic that is monitored by a firewall, either by stateful or stateless rules. For traffic to be considered part of a flow, it must share Destination, DestinationPort, Direction, Protocol, Source, and SourcePort. </p>"}, "ListRuleGroups": {"name": "ListRuleGroups", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListRuleGroupsRequest"}, "output": {"shape": "ListRuleGroupsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerError"}], "documentation": "<p>Retrieves the metadata for the rule groups that you have defined. Depending on your setting for max results and the number of rule groups, a single call might not return the full list. </p>"}, "ListTLSInspectionConfigurations": {"name": "ListTLSInspectionConfigurations", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTLSInspectionConfigurationsRequest"}, "output": {"shape": "ListTLSInspectionConfigurationsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServerError"}, {"shape": "ThrottlingException"}], "documentation": "<p>Retrieves the metadata for the TLS inspection configurations that you have defined. Depending on your setting for max results and the number of TLS inspection configurations, a single call might not return the full list.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerError"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Retrieves the tags associated with the specified resource. Tags are key:value pairs that you can use to categorize and manage your resources, for purposes like billing. For example, you might set the tag key to \"customer\" and the value to the customer name or ID. You can specify one or more tags to add to each Amazon Web Services resource, up to 50 tags for a resource.</p> <p>You can tag the Amazon Web Services resources that you manage through Network Firewall: firewalls, firewall policies, and rule groups. </p>"}, "ListVpcEndpointAssociations": {"name": "ListVpcEndpointAssociations", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListVpcEndpointAssociationsRequest"}, "output": {"shape": "ListVpcEndpointAssociationsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerError"}], "documentation": "<p>Retrieves the metadata for the VPC endpoint associations that you have defined. If you specify a fireawll, this returns only the endpoint associations for that firewall. </p> <p>Depending on your setting for max results and the number of associations, a single call might not return the full list. </p>"}, "PutResourcePolicy": {"name": "PutResourcePolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutResourcePolicyRequest"}, "output": {"shape": "PutResourcePolicyResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServerError"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InvalidResourcePolicyException"}], "documentation": "<p>Creates or updates an IAM policy for your rule group, firewall policy, or firewall. Use this to share these resources between accounts. This operation works in conjunction with the Amazon Web Services Resource Access Manager (RAM) service to manage resource sharing for Network Firewall. </p> <p>For information about using sharing with Network Firewall resources, see <a href=\"https://docs.aws.amazon.com/network-firewall/latest/developerguide/sharing.html\">Sharing Network Firewall resources</a> in the <i>Network Firewall Developer Guide</i>.</p> <p>Use this operation to create or update a resource policy for your Network Firewall rule group, firewall policy, or firewall. In the resource policy, you specify the accounts that you want to share the Network Firewall resource with and the operations that you want the accounts to be able to perform. </p> <p>When you add an account in the resource policy, you then run the following Resource Access Manager (RAM) operations to access and accept the shared resource. </p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/ram/latest/APIReference/API_GetResourceShareInvitations.html\">GetResourceShareInvitations</a> - Returns the Amazon Resource Names (ARNs) of the resource share invitations. </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/ram/latest/APIReference/API_AcceptResourceShareInvitation.html\">AcceptResourceShareInvitation</a> - Accepts the share invitation for a specified resource share. </p> </li> </ul> <p>For additional information about resource sharing using RAM, see <a href=\"https://docs.aws.amazon.com/ram/latest/userguide/what-is.html\">Resource Access Manager User Guide</a>.</p>"}, "RejectNetworkFirewallTransitGatewayAttachment": {"name": "RejectNetworkFirewallTransitGatewayAttachment", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "RejectNetworkFirewallTransitGatewayAttachmentRequest"}, "output": {"shape": "RejectNetworkFirewallTransitGatewayAttachmentResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServerError"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Rejects a transit gateway attachment request for Network Firewall. When you reject the attachment request, Network Firewall cancels the creation of routing components between the transit gateway and firewall endpoints.</p> <p>Only the firewall owner can reject the attachment. After rejection, no traffic will flow through the firewall endpoints for this attachment.</p> <p>Use <a>DescribeFirewall</a> to monitor the rejection status. To accept the attachment instead of rejecting it, use <a>AcceptNetworkFirewallTransitGatewayAttachment</a>.</p> <note> <p>Once rejected, you cannot reverse this action. To establish connectivity, you must create a new transit gateway-attached firewall.</p> </note>"}, "StartAnalysisReport": {"name": "StartAnalysisReport", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StartAnalysisReportRequest"}, "output": {"shape": "StartAnalysisReportResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServerError"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Generates a traffic analysis report for the timeframe and traffic type you specify.</p> <p>For information on the contents of a traffic analysis report, see <a>AnalysisReport</a>.</p>"}, "StartFlowCapture": {"name": "StartFlowCapture", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StartFlowCaptureRequest"}, "output": {"shape": "StartFlowCaptureResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServerError"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Begins capturing the flows in a firewall, according to the filters you define. Captures are similar, but not identical to snapshots. Capture operations provide visibility into flows that are not closed and are tracked by a firewall's flow table. Unlike snapshots, captures are a time-boxed view. </p> <p>A flow is network traffic that is monitored by a firewall, either by stateful or stateless rules. For traffic to be considered part of a flow, it must share Destination, DestinationPort, Direction, Protocol, Source, and SourcePort. </p> <note> <p>To avoid encountering operation limits, you should avoid starting captures with broad filters, like wide IP ranges. Instead, we recommend you define more specific criteria with <code>FlowFilters</code>, like narrow IP ranges, ports, or protocols.</p> </note>"}, "StartFlowFlush": {"name": "StartFlowFlush", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StartFlowFlushRequest"}, "output": {"shape": "StartFlowFlushResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServerError"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Begins the flushing of traffic from the firewall, according to the filters you define. When the operation starts, impacted flows are temporarily marked as timed out before the Suricata engine prunes, or flushes, the flows from the firewall table.</p> <important> <p>While the flush completes, impacted flows are processed as midstream traffic. This may result in a temporary increase in midstream traffic metrics. We recommend that you double check your stream exception policy before you perform a flush operation.</p> </important>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerError"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Adds the specified tags to the specified resource. Tags are key:value pairs that you can use to categorize and manage your resources, for purposes like billing. For example, you might set the tag key to \"customer\" and the value to the customer name or ID. You can specify one or more tags to add to each Amazon Web Services resource, up to 50 tags for a resource.</p> <p>You can tag the Amazon Web Services resources that you manage through Network Firewall: firewalls, firewall policies, and rule groups. </p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "InternalServerError"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Removes the tags with the specified keys from the specified resource. Tags are key:value pairs that you can use to categorize and manage your resources, for purposes like billing. For example, you might set the tag key to \"customer\" and the value to the customer name or ID. You can specify one or more tags to add to each Amazon Web Services resource, up to 50 tags for a resource.</p> <p>You can manage tags for the Amazon Web Services resources that you manage through Network Firewall: firewalls, firewall policies, and rule groups. </p>"}, "UpdateAvailabilityZoneChangeProtection": {"name": "UpdateAvailabilityZoneChangeProtection", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateAvailabilityZoneChangeProtectionRequest"}, "output": {"shape": "UpdateAvailabilityZoneChangeProtectionResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServerError"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InvalidTokenException"}, {"shape": "ResourceOwnerCheckException"}], "documentation": "<p>Modifies the <code>AvailabilityZoneChangeProtection</code> setting for a transit gateway-attached firewall. When enabled, this setting prevents accidental changes to the firewall's Availability Zone configuration. This helps protect against disrupting traffic flow in production environments.</p> <p>When enabled, you must disable this protection before using <a>AssociateAvailabilityZones</a> or <a>DisassociateAvailabilityZones</a> to modify the firewall's Availability Zone configuration.</p>"}, "UpdateFirewallAnalysisSettings": {"name": "UpdateFirewallAnalysisSettings", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateFirewallAnalysisSettingsRequest"}, "output": {"shape": "UpdateFirewallAnalysisSettingsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServerError"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Enables specific types of firewall analysis on a specific firewall you define.</p>"}, "UpdateFirewallDeleteProtection": {"name": "UpdateFirewallDeleteProtection", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateFirewallDeleteProtectionRequest"}, "output": {"shape": "UpdateFirewallDeleteProtectionResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServerError"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InvalidTokenException"}, {"shape": "ResourceOwnerCheckException"}], "documentation": "<p>Modifies the flag, <code>DeleteProtection</code>, which indicates whether it is possible to delete the firewall. If the flag is set to <code>TRUE</code>, the firewall is protected against deletion. This setting helps protect against accidentally deleting a firewall that's in use. </p>"}, "UpdateFirewallDescription": {"name": "UpdateFirewallDescription", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateFirewallDescriptionRequest"}, "output": {"shape": "UpdateFirewallDescriptionResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServerError"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InvalidTokenException"}], "documentation": "<p>Modifies the description for the specified firewall. Use the description to help you identify the firewall when you're working with it. </p>"}, "UpdateFirewallEncryptionConfiguration": {"name": "UpdateFirewallEncryptionConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateFirewallEncryptionConfigurationRequest"}, "output": {"shape": "UpdateFirewallEncryptionConfigurationResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServerError"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InvalidTokenException"}, {"shape": "ResourceOwnerCheckException"}], "documentation": "<p>A complex type that contains settings for encryption of your firewall resources.</p>"}, "UpdateFirewallPolicy": {"name": "UpdateFirewallPolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateFirewallPolicyRequest"}, "output": {"shape": "UpdateFirewallPolicyResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerError"}, {"shape": "InvalidTokenException"}], "documentation": "<p>Updates the properties of the specified firewall policy.</p>"}, "UpdateFirewallPolicyChangeProtection": {"name": "UpdateFirewallPolicyChangeProtection", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateFirewallPolicyChangeProtectionRequest"}, "output": {"shape": "UpdateFirewallPolicyChangeProtectionResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServerError"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InvalidTokenException"}, {"shape": "ResourceOwnerCheckException"}], "documentation": "<p>Modifies the flag, <code>ChangeProtection</code>, which indicates whether it is possible to change the firewall. If the flag is set to <code>TRUE</code>, the firewall is protected from changes. This setting helps protect against accidentally changing a firewall that's in use.</p>"}, "UpdateLoggingConfiguration": {"name": "UpdateLoggingConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateLoggingConfigurationRequest"}, "output": {"shape": "UpdateLoggingConfigurationResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServerError"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InvalidTokenException"}, {"shape": "LogDestinationPermissionException"}], "documentation": "<p>Sets the logging configuration for the specified firewall. </p> <p>To change the logging configuration, retrieve the <a>LoggingConfiguration</a> by calling <a>DescribeLoggingConfiguration</a>, then change it and provide the modified object to this update call. You must change the logging configuration one <a>LogDestinationConfig</a> at a time inside the retrieved <a>LoggingConfiguration</a> object. </p> <p>You can perform only one of the following actions in any call to <code>UpdateLoggingConfiguration</code>: </p> <ul> <li> <p>Create a new log destination object by adding a single <code>LogDestinationConfig</code> array element to <code>LogDestinationConfigs</code>.</p> </li> <li> <p>Delete a log destination object by removing a single <code>LogDestinationConfig</code> array element from <code>LogDestinationConfigs</code>.</p> </li> <li> <p>Change the <code>LogDestination</code> setting in a single <code>LogDestinationConfig</code> array element.</p> </li> </ul> <p>You can't change the <code>LogDestinationType</code> or <code>LogType</code> in a <code>LogDestinationConfig</code>. To change these settings, delete the existing <code>LogDestinationConfig</code> object and create a new one, using two separate calls to this update operation.</p>"}, "UpdateRuleGroup": {"name": "UpdateRuleGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateRuleGroupRequest"}, "output": {"shape": "UpdateRuleGroupResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidRequestException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerError"}, {"shape": "InvalidTokenException"}], "documentation": "<p>Updates the rule settings for the specified rule group. You use a rule group by reference in one or more firewall policies. When you modify a rule group, you modify all firewall policies that use the rule group. </p> <p>To update a rule group, first call <a>DescribeRuleGroup</a> to retrieve the current <a>RuleGroup</a> object, update the object as needed, and then provide the updated object to this call. </p>"}, "UpdateSubnetChangeProtection": {"name": "UpdateSubnetChangeProtection", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateSubnetChangeProtectionRequest"}, "output": {"shape": "UpdateSubnetChangeProtectionResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServerError"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InvalidTokenException"}, {"shape": "ResourceOwnerCheckException"}], "documentation": "<p/>"}, "UpdateTLSInspectionConfiguration": {"name": "UpdateTLSInspectionConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateTLSInspectionConfigurationRequest"}, "output": {"shape": "UpdateTLSInspectionConfigurationResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerError"}, {"shape": "InvalidTokenException"}], "documentation": "<p>Updates the TLS inspection configuration settings for the specified TLS inspection configuration. You use a TLS inspection configuration by referencing it in one or more firewall policies. When you modify a TLS inspection configuration, you modify all firewall policies that use the TLS inspection configuration. </p> <p>To update a TLS inspection configuration, first call <a>DescribeTLSInspectionConfiguration</a> to retrieve the current <a>TLSInspectionConfiguration</a> object, update the object as needed, and then provide the updated object to this call. </p>"}}, "shapes": {"AWSAccountId": {"type": "string", "max": 12, "min": 12, "pattern": "^\\d{12}$"}, "AZSyncState": {"type": "structure", "members": {"Attachment": {"shape": "Attachment"}}, "documentation": "<p>The status of the firewall endpoint defined by a <code>VpcEndpointAssociation</code>. </p>"}, "AcceptNetworkFirewallTransitGatewayAttachmentRequest": {"type": "structure", "required": ["TransitGatewayAttachmentId"], "members": {"TransitGatewayAttachmentId": {"shape": "TransitGatewayAttachmentId", "documentation": "<p>Required. The unique identifier of the transit gateway attachment to accept. This ID is returned in the response when creating a transit gateway-attached firewall.</p>"}}}, "AcceptNetworkFirewallTransitGatewayAttachmentResponse": {"type": "structure", "required": ["TransitGatewayAttachmentId", "TransitGatewayAttachmentStatus"], "members": {"TransitGatewayAttachmentId": {"shape": "TransitGatewayAttachmentId", "documentation": "<p>The unique identifier of the transit gateway attachment that was accepted.</p>"}, "TransitGatewayAttachmentStatus": {"shape": "TransitGatewayAttachmentStatus", "documentation": "<p>The current status of the transit gateway attachment. Valid values are:</p> <ul> <li> <p> <code>CREATING</code> - The attachment is being created</p> </li> <li> <p> <code>DELETING</code> - The attachment is being deleted</p> </li> <li> <p> <code>DELETED</code> - The attachment has been deleted</p> </li> <li> <p> <code>FAILED</code> - The attachment creation has failed and cannot be recovered</p> </li> <li> <p> <code>ERROR</code> - The attachment is in an error state that might be recoverable</p> </li> <li> <p> <code>READY</code> - The attachment is active and processing traffic</p> </li> <li> <p> <code>PENDING_ACCEPTANCE</code> - The attachment is waiting to be accepted</p> </li> <li> <p> <code>REJECTING</code> - The attachment is in the process of being rejected</p> </li> <li> <p> <code>REJECTED</code> - The attachment has been rejected</p> </li> </ul>"}}}, "ActionDefinition": {"type": "structure", "members": {"PublishMetricAction": {"shape": "PublishMetricAction", "documentation": "<p>Stateless inspection criteria that publishes the specified metrics to Amazon CloudWatch for the matching packet. This setting defines a CloudWatch dimension value to be published.</p> <p>You can pair this custom action with any of the standard stateless rule actions. For example, you could pair this in a rule action with the standard action that forwards the packet for stateful inspection. Then, when a packet matches the rule, Network Firewall publishes metrics for the packet and forwards it. </p>"}}, "documentation": "<p>A custom action to use in stateless rule actions settings. This is used in <a>CustomAction</a>.</p>"}, "ActionName": {"type": "string", "max": 128, "min": 1, "pattern": "^[a-zA-Z0-9]+$"}, "Address": {"type": "structure", "required": ["AddressDefinition"], "members": {"AddressDefinition": {"shape": "AddressDefinition", "documentation": "<p>Specify an IP address or a block of IP addresses in Classless Inter-Domain Routing (CIDR) notation. Network Firewall supports all address ranges for IPv4 and IPv6. </p> <p>Examples: </p> <ul> <li> <p>To configure Network Firewall to inspect for the IP address **********, specify <code>**********/32</code>.</p> </li> <li> <p>To configure Network Firewall to inspect for IP addresses from ********* to ***********, specify <code>*********/24</code>.</p> </li> <li> <p>To configure Network Firewall to inspect for the IP address 1111:0000:0000:0000:0000:0000:0000:0111, specify <code>1111:0000:0000:0000:0000:0000:0000:0111/128</code>.</p> </li> <li> <p>To configure Network Firewall to inspect for IP addresses from 1111:0000:0000:0000:0000:0000:0000:0000 to 1111:0000:0000:0000:ffff:ffff:ffff:ffff, specify <code>1111:0000:0000:0000:0000:0000:0000:0000/64</code>.</p> </li> </ul> <p>For more information about CIDR notation, see the Wikipedia entry <a href=\"https://en.wikipedia.org/wiki/Classless_Inter-Domain_Routing\">Classless Inter-Domain Routing</a>.</p>"}}, "documentation": "<p>A single IP address specification. This is used in the <a>MatchAttributes</a> source and destination specifications.</p>"}, "AddressDefinition": {"type": "string", "max": 255, "min": 1, "pattern": "^([a-fA-F\\d:\\.]+($|/\\d{1,3}))$"}, "Addresses": {"type": "list", "member": {"shape": "Address"}}, "Age": {"type": "integer"}, "AnalysisReport": {"type": "structure", "members": {"AnalysisReportId": {"shape": "AnalysisReportId", "documentation": "<p>The unique ID of the query that ran when you requested an analysis report. </p>"}, "AnalysisType": {"shape": "EnabledAnalysisType", "documentation": "<p>The type of traffic that will be used to generate a report. </p>"}, "ReportTime": {"shape": "ReportTime", "documentation": "<p>The date and time the analysis report was ran. </p>"}, "Status": {"shape": "Status", "documentation": "<p>The status of the analysis report you specify. Statuses include <code>RUNNING</code>, <code>COMPLETED</code>, or <code>FAILED</code>.</p>"}}, "documentation": "<p>A report that captures key activity from the last 30 days of network traffic monitored by your firewall.</p> <p>You can generate up to one report per traffic type, per 30 day period. For example, when you successfully create an HTTP traffic report, you cannot create another HTTP traffic report until 30 days pass. Alternatively, if you generate a report that combines metrics on both HTTP and HTTPS traffic, you cannot create another report for either traffic type until 30 days pass.</p>"}, "AnalysisReportId": {"type": "string", "max": 128, "min": 1, "pattern": "\\S+"}, "AnalysisReportNextToken": {"type": "string", "max": 1024, "min": 1}, "AnalysisReportResults": {"type": "list", "member": {"shape": "AnalysisTypeReportResult"}}, "AnalysisReports": {"type": "list", "member": {"shape": "AnalysisReport"}}, "AnalysisResult": {"type": "structure", "members": {"IdentifiedRuleIds": {"shape": "RuleIdList", "documentation": "<p>The priority number of the stateless rules identified in the analysis.</p>"}, "IdentifiedType": {"shape": "IdentifiedType", "documentation": "<p>The types of rule configurations that Network Firewall analyzes your rule groups for. Network Firewall analyzes stateless rule groups for the following types of rule configurations:</p> <ul> <li> <p> <code>STATELESS_RULE_FORWARDING_ASYMMETRICALLY</code> </p> <p>Cause: One or more stateless rules with the action <code>pass</code> or <code>forward</code> are forwarding traffic asymmetrically. Specifically, the rule's set of source IP addresses or their associated port numbers, don't match the set of destination IP addresses or their associated port numbers.</p> <p>To mitigate: Make sure that there's an existing return path. For example, if the rule allows traffic from source ********/24 to destination ********/24, you should allow return traffic from source ********/24 to destination ********/24.</p> </li> <li> <p> <code>STATELESS_RULE_CONTAINS_TCP_FLAGS</code> </p> <p>Cause: At least one stateless rule with the action <code>pass</code> or<code>forward</code> contains TCP flags that are inconsistent in the forward and return directions.</p> <p>To mitigate: Prevent asymmetric routing issues caused by TCP flags by following these actions:</p> <ul> <li> <p>Remove unnecessary TCP flag inspections from the rules.</p> </li> <li> <p>If you need to inspect TCP flags, check that the rules correctly account for changes in TCP flags throughout the TCP connection cycle, for example <code>SYN</code> and <code>ACK</code> flags used in a 3-way TCP handshake.</p> </li> </ul> </li> </ul>"}, "AnalysisDetail": {"shape": "CollectionMember_String", "documentation": "<p>Provides analysis details for the identified rule.</p>"}}, "documentation": "<p>The analysis result for Network Firewall's stateless rule group analyzer. Every time you call <a>CreateRuleGroup</a>, <a>UpdateRuleGroup</a>, or <a>DescribeRuleGroup</a> on a stateless rule group, Network Firewall analyzes the stateless rule groups in your account and identifies the rules that might adversely effect your firewall's functionality. For example, if Network Firewall detects a rule that's routing traffic asymmetrically, which impacts the service's ability to properly process traffic, the service includes the rule in a list of analysis results.</p> <p>The <code>AnalysisResult</code> data type is not related to traffic analysis reports you generate using <a>StartAnalysisReport</a>. For information on traffic analysis report results, see <a>AnalysisTypeReportResult</a>.</p>"}, "AnalysisResultList": {"type": "list", "member": {"shape": "AnalysisResult"}}, "AnalysisTypeReportResult": {"type": "structure", "members": {"Protocol": {"shape": "CollectionMember_String", "documentation": "<p>The type of traffic captured by the analysis report.</p>"}, "FirstAccessed": {"shape": "FirstAccessed", "documentation": "<p>The date and time any domain was first accessed (within the last 30 day period).</p>"}, "LastAccessed": {"shape": "LastAccessed", "documentation": "<p>The date and time any domain was last accessed (within the last 30 day period).</p>"}, "Domain": {"shape": "Domain", "documentation": "<p>The most frequently accessed domains.</p>"}, "Hits": {"shape": "Hits", "documentation": "<p>The number of attempts made to access a observed domain.</p>"}, "UniqueSources": {"shape": "UniqueSources", "documentation": "<p>The number of unique source IP addresses that connected to a domain.</p>"}}, "documentation": "<p>The results of a <code>COMPLETED</code> analysis report generated with <a>StartAnalysisReport</a>.</p> <p>For an example of traffic analysis report results, see the response syntax of <a>GetAnalysisReportResults</a>.</p>"}, "AssociateAvailabilityZonesRequest": {"type": "structure", "required": ["AvailabilityZoneMappings"], "members": {"UpdateToken": {"shape": "UpdateToken", "documentation": "<p>An optional token that you can use for optimistic locking. Network Firewall returns a token to your requests that access the firewall. The token marks the state of the firewall resource at the time of the request. </p> <p>To make an unconditional change to the firewall, omit the token in your update request. Without the token, Network Firewall performs your updates regardless of whether the firewall has changed since you last retrieved it.</p> <p>To make a conditional change to the firewall, provide the token in your update request. Network Firewall uses the token to ensure that the firewall hasn't changed since you last retrieved it. If it has changed, the operation fails with an <code>InvalidTokenException</code>. If this happens, retrieve the firewall again to get a current copy of it with a new token. Reapply your changes as needed, then try the operation again using the new token. </p>"}, "FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "FirewallName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the firewall. You can't change the name of a firewall after you create it.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "AvailabilityZoneMappings": {"shape": "AvailabilityZoneMappings", "documentation": "<p>Required. The Availability Zones where you want to create firewall endpoints. You must specify at least one Availability Zone.</p>"}}}, "AssociateAvailabilityZonesResponse": {"type": "structure", "members": {"FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p>"}, "FirewallName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the firewall. You can't change the name of a firewall after you create it.</p>"}, "AvailabilityZoneMappings": {"shape": "AvailabilityZoneMappings", "documentation": "<p>The Availability Zones where Network Firewall created firewall endpoints. Each mapping specifies an Availability Zone where the firewall processes traffic.</p>"}, "UpdateToken": {"shape": "UpdateToken", "documentation": "<p>An optional token that you can use for optimistic locking. Network Firewall returns a token to your requests that access the firewall. The token marks the state of the firewall resource at the time of the request. </p> <p>To make an unconditional change to the firewall, omit the token in your update request. Without the token, Network Firewall performs your updates regardless of whether the firewall has changed since you last retrieved it.</p> <p>To make a conditional change to the firewall, provide the token in your update request. Network Firewall uses the token to ensure that the firewall hasn't changed since you last retrieved it. If it has changed, the operation fails with an <code>InvalidTokenException</code>. If this happens, retrieve the firewall again to get a current copy of it with a new token. Reapply your changes as needed, then try the operation again using the new token. </p>"}}}, "AssociateFirewallPolicyRequest": {"type": "structure", "required": ["FirewallPolicyArn"], "members": {"UpdateToken": {"shape": "UpdateToken", "documentation": "<p>An optional token that you can use for optimistic locking. Network Firewall returns a token to your requests that access the firewall. The token marks the state of the firewall resource at the time of the request. </p> <p>To make an unconditional change to the firewall, omit the token in your update request. Without the token, Network Firewall performs your updates regardless of whether the firewall has changed since you last retrieved it.</p> <p>To make a conditional change to the firewall, provide the token in your update request. Network Firewall uses the token to ensure that the firewall hasn't changed since you last retrieved it. If it has changed, the operation fails with an <code>InvalidTokenException</code>. If this happens, retrieve the firewall again to get a current copy of it with a new token. Reapply your changes as needed, then try the operation again using the new token. </p>"}, "FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "FirewallName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the firewall. You can't change the name of a firewall after you create it.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "FirewallPolicyArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall policy.</p>"}}}, "AssociateFirewallPolicyResponse": {"type": "structure", "members": {"FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p>"}, "FirewallName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the firewall. You can't change the name of a firewall after you create it.</p>"}, "FirewallPolicyArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall policy.</p>"}, "UpdateToken": {"shape": "UpdateToken", "documentation": "<p>An optional token that you can use for optimistic locking. Network Firewall returns a token to your requests that access the firewall. The token marks the state of the firewall resource at the time of the request. </p> <p>To make an unconditional change to the firewall, omit the token in your update request. Without the token, Network Firewall performs your updates regardless of whether the firewall has changed since you last retrieved it.</p> <p>To make a conditional change to the firewall, provide the token in your update request. Network Firewall uses the token to ensure that the firewall hasn't changed since you last retrieved it. If it has changed, the operation fails with an <code>InvalidTokenException</code>. If this happens, retrieve the firewall again to get a current copy of it with a new token. Reapply your changes as needed, then try the operation again using the new token. </p>"}}}, "AssociateSubnetsRequest": {"type": "structure", "required": ["SubnetMappings"], "members": {"UpdateToken": {"shape": "UpdateToken", "documentation": "<p>An optional token that you can use for optimistic locking. Network Firewall returns a token to your requests that access the firewall. The token marks the state of the firewall resource at the time of the request. </p> <p>To make an unconditional change to the firewall, omit the token in your update request. Without the token, Network Firewall performs your updates regardless of whether the firewall has changed since you last retrieved it.</p> <p>To make a conditional change to the firewall, provide the token in your update request. Network Firewall uses the token to ensure that the firewall hasn't changed since you last retrieved it. If it has changed, the operation fails with an <code>InvalidTokenException</code>. If this happens, retrieve the firewall again to get a current copy of it with a new token. Reapply your changes as needed, then try the operation again using the new token. </p>"}, "FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "FirewallName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the firewall. You can't change the name of a firewall after you create it.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "SubnetMappings": {"shape": "SubnetMappings", "documentation": "<p>The IDs of the subnets that you want to associate with the firewall. </p>"}}}, "AssociateSubnetsResponse": {"type": "structure", "members": {"FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p>"}, "FirewallName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the firewall. You can't change the name of a firewall after you create it.</p>"}, "SubnetMappings": {"shape": "SubnetMappings", "documentation": "<p>The IDs of the subnets that are associated with the firewall. </p>"}, "UpdateToken": {"shape": "UpdateToken", "documentation": "<p>An optional token that you can use for optimistic locking. Network Firewall returns a token to your requests that access the firewall. The token marks the state of the firewall resource at the time of the request. </p> <p>To make an unconditional change to the firewall, omit the token in your update request. Without the token, Network Firewall performs your updates regardless of whether the firewall has changed since you last retrieved it.</p> <p>To make a conditional change to the firewall, provide the token in your update request. Network Firewall uses the token to ensure that the firewall hasn't changed since you last retrieved it. If it has changed, the operation fails with an <code>InvalidTokenException</code>. If this happens, retrieve the firewall again to get a current copy of it with a new token. Reapply your changes as needed, then try the operation again using the new token. </p>"}}}, "AssociationSyncState": {"type": "map", "key": {"shape": "AvailabilityZone"}, "value": {"shape": "AZSyncState"}}, "Attachment": {"type": "structure", "members": {"SubnetId": {"shape": "AzSubnet", "documentation": "<p>The unique identifier of the subnet that you've specified to be used for a firewall endpoint. </p>"}, "EndpointId": {"shape": "EndpointId", "documentation": "<p>The identifier of the firewall endpoint that Network Firewall has instantiated in the subnet. You use this to identify the firewall endpoint in the VPC route tables, when you redirect the VPC traffic through the endpoint. </p>"}, "Status": {"shape": "AttachmentStatus", "documentation": "<p>The current status of the firewall endpoint instantiation in the subnet. </p> <p>When this value is <code>READY</code>, the endpoint is available to handle network traffic. Otherwise, this value reflects its state, for example <code>CREATING</code> or <code>DELETING</code>.</p>"}, "StatusMessage": {"shape": "StatusMessage", "documentation": "<p>If Network Firewall fails to create or delete the firewall endpoint in the subnet, it populates this with the reason for the error or failure and how to resolve it. A <code>FAILED</code> status indicates a non-recoverable state, and a <code>ERROR</code> status indicates an issue that you can fix. Depending on the error, it can take as many as 15 minutes to populate this field. For more information about the causes for failiure or errors and solutions available for this field, see <a href=\"https://docs.aws.amazon.com/network-firewall/latest/developerguide/firewall-troubleshooting-endpoint-failures.html\">Troubleshooting firewall endpoint failures</a> in the <i>Network Firewall Developer Guide</i>.</p>"}}, "documentation": "<p>The definition and status of the firewall endpoint for a single subnet. In each configured subnet, Network Firewall instantiates a firewall endpoint to handle network traffic. </p> <p>This data type is used for any firewall endpoint type: </p> <ul> <li> <p>For <code>Firewall.SubnetMappings</code>, this <code>Attachment</code> is part of the <code>FirewallStatus</code> sync states information. You define firewall subnets using <code>CreateFirewall</code> and <code>AssociateSubnets</code>. </p> </li> <li> <p>For <code>VpcEndpointAssociation</code>, this <code>Attachment</code> is part of the <code>VpcEndpointAssociationStatus</code> sync states information. You define these subnets using <code>CreateVpcEndpointAssociation</code>. </p> </li> </ul>"}, "AttachmentId": {"type": "string"}, "AttachmentStatus": {"type": "string", "enum": ["CREATING", "DELETING", "FAILED", "ERROR", "SCALING", "READY"]}, "AvailabilityZone": {"type": "string"}, "AvailabilityZoneMapping": {"type": "structure", "required": ["AvailabilityZone"], "members": {"AvailabilityZone": {"shape": "AvailabilityZoneMappingString", "documentation": "<p>The ID of the Availability Zone where the firewall endpoint is located. For example, <code>us-east-2a</code>. The Availability Zone must be in the same Region as the transit gateway.</p>"}}, "documentation": "<p>Defines the mapping between an Availability Zone and a firewall endpoint for a transit gateway-attached firewall. Each mapping represents where the firewall can process traffic. You use these mappings when calling <a>CreateFirewall</a>, <a>AssociateAvailabilityZones</a>, and <a>DisassociateAvailabilityZones</a>.</p> <p>To retrieve the current Availability Zone mappings for a firewall, use <a>DescribeFirewall</a>.</p>"}, "AvailabilityZoneMappingString": {"type": "string", "max": 128, "min": 1, "pattern": "\\S+"}, "AvailabilityZoneMappings": {"type": "list", "member": {"shape": "AvailabilityZoneMapping"}}, "AvailabilityZoneMetadata": {"type": "structure", "members": {"IPAddressType": {"shape": "IPAddressType", "documentation": "<p>The IP address type of the Firewall subnet in the Availability Zone. You can't change the IP address type after you create the subnet.</p>"}}, "documentation": "<p>High-level information about an Availability Zone where the firewall has an endpoint defined. </p>"}, "AzSubnet": {"type": "string", "max": 128, "min": 1, "pattern": "^subnet-[0-9a-f]+$"}, "AzSubnets": {"type": "list", "member": {"shape": "AzSubnet"}}, "Boolean": {"type": "boolean"}, "ByteCount": {"type": "long"}, "CIDRCount": {"type": "integer", "max": 1000000, "min": 0}, "CIDRSummary": {"type": "structure", "members": {"AvailableCIDRCount": {"shape": "CIDRCount", "documentation": "<p>The number of CIDR blocks available for use by the IP set references in a firewall.</p>"}, "UtilizedCIDRCount": {"shape": "CIDRCount", "documentation": "<p>The number of CIDR blocks used by the IP set references in a firewall.</p>"}, "IPSetReferences": {"shape": "IPSetMetadataMap", "documentation": "<p>The list of the IP set references used by a firewall.</p>"}}, "documentation": "<p>Summarizes the CIDR blocks used by the IP set references in a firewall. Network Firewall calculates the number of CIDRs by taking an aggregated count of all CIDRs used by the IP sets you are referencing.</p>"}, "CapacityUsageSummary": {"type": "structure", "members": {"CIDRs": {"shape": "CIDRSummary", "documentation": "<p>Describes the capacity usage of the CIDR blocks used by the IP set references in a firewall.</p>"}}, "documentation": "<p>The capacity usage summary of the resources used by the <a>ReferenceSets</a> in a firewall.</p>"}, "Certificates": {"type": "list", "member": {"shape": "TlsCertificateData"}}, "CheckCertificateRevocationStatusActions": {"type": "structure", "members": {"RevokedStatusAction": {"shape": "RevocationCheckAction", "documentation": "<p>Configures how Network Firewall processes traffic when it determines that the certificate presented by the server in the SSL/TLS connection has a revoked status.</p> <ul> <li> <p> <b>PASS</b> - Allow the connection to continue, and pass subsequent packets to the stateful engine for inspection.</p> </li> <li> <p> <b>DROP</b> - Network Firewall closes the connection and drops subsequent packets for that connection.</p> </li> <li> <p> <b>REJECT</b> - Network Firewall sends a TCP reject packet back to your client. The service closes the connection and drops subsequent packets for that connection. <code>REJECT</code> is available only for TCP traffic.</p> </li> </ul>"}, "UnknownStatusAction": {"shape": "RevocationCheckAction", "documentation": "<p>Configures how Network Firewall processes traffic when it determines that the certificate presented by the server in the SSL/TLS connection has an unknown status, or a status that cannot be determined for any other reason, including when the service is unable to connect to the OCSP and CRL endpoints for the certificate.</p> <ul> <li> <p> <b>PASS</b> - Allow the connection to continue, and pass subsequent packets to the stateful engine for inspection.</p> </li> <li> <p> <b>DROP</b> - Network Firewall closes the connection and drops subsequent packets for that connection.</p> </li> <li> <p> <b>REJECT</b> - Network Firewall sends a TCP reject packet back to your client. The service closes the connection and drops subsequent packets for that connection. <code>REJECT</code> is available only for TCP traffic.</p> </li> </ul>"}}, "documentation": "<p>Defines the actions to take on the SSL/TLS connection if the certificate presented by the server in the connection has a revoked or unknown status.</p>"}, "CollectionMember_String": {"type": "string"}, "ConfigurationSyncState": {"type": "string", "enum": ["PENDING", "IN_SYNC", "CAPACITY_CONSTRAINED"]}, "Count": {"type": "integer"}, "CreateFirewallPolicyRequest": {"type": "structure", "required": ["FirewallPolicyName", "FirewallPolicy"], "members": {"FirewallPolicyName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the firewall policy. You can't change the name of a firewall policy after you create it.</p>"}, "FirewallPolicy": {"shape": "FirewallPolicy", "documentation": "<p>The rule groups and policy actions to use in the firewall policy.</p>"}, "Description": {"shape": "Description", "documentation": "<p>A description of the firewall policy.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The key:value pairs to associate with the resource.</p>"}, "DryRun": {"shape": "Boolean", "documentation": "<p>Indicates whether you want Network Firewall to just check the validity of the request, rather than run the request. </p> <p>If set to <code>TRUE</code>, Network Firewall checks whether the request can run successfully, but doesn't actually make the requested changes. The call returns the value that the request would return if you ran it with dry run set to <code>FALSE</code>, but doesn't make additions or changes to your resources. This option allows you to make sure that you have the required permissions to run the request and that your request parameters are valid. </p> <p>If set to <code>FALSE</code>, Network Firewall makes the requested changes to your resources. </p>"}, "EncryptionConfiguration": {"shape": "EncryptionConfiguration", "documentation": "<p>A complex type that contains settings for encryption of your firewall policy resources.</p>"}}}, "CreateFirewallPolicyResponse": {"type": "structure", "required": ["UpdateToken", "FirewallPolicyResponse"], "members": {"UpdateToken": {"shape": "UpdateToken", "documentation": "<p>A token used for optimistic locking. Network Firewall returns a token to your requests that access the firewall policy. The token marks the state of the policy resource at the time of the request. </p> <p>To make changes to the policy, you provide the token in your request. Network Firewall uses the token to ensure that the policy hasn't changed since you last retrieved it. If it has changed, the operation fails with an <code>InvalidTokenException</code>. If this happens, retrieve the firewall policy again to get a current copy of it with current token. Reapply your changes as needed, then try the operation again using the new token. </p>"}, "FirewallPolicyResponse": {"shape": "FirewallPolicyResponse", "documentation": "<p>The high-level properties of a firewall policy. This, along with the <a>FirewallPolicy</a>, define the policy. You can retrieve all objects for a firewall policy by calling <a>DescribeFirewallPolicy</a>. </p>"}}}, "CreateFirewallRequest": {"type": "structure", "required": ["FirewallName", "FirewallPolicyArn"], "members": {"FirewallName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the firewall. You can't change the name of a firewall after you create it.</p>"}, "FirewallPolicyArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the <a>FirewallPolicy</a> that you want to use for the firewall.</p>"}, "VpcId": {"shape": "VpcId", "documentation": "<p>The unique identifier of the VPC where Network Firewall should create the firewall. </p> <p>You can't change this setting after you create the firewall. </p>"}, "SubnetMappings": {"shape": "SubnetMappings", "documentation": "<p>The public subnets to use for your Network Firewall firewalls. Each subnet must belong to a different Availability Zone in the VPC. Network Firewall creates a firewall endpoint in each subnet. </p>"}, "DeleteProtection": {"shape": "Boolean", "documentation": "<p>A flag indicating whether it is possible to delete the firewall. A setting of <code>TRUE</code> indicates that the firewall is protected against deletion. Use this setting to protect against accidentally deleting a firewall that is in use. When you create a firewall, the operation initializes this flag to <code>TRUE</code>.</p>"}, "SubnetChangeProtection": {"shape": "Boolean", "documentation": "<p>A setting indicating whether the firewall is protected against changes to the subnet associations. Use this setting to protect against accidentally modifying the subnet associations for a firewall that is in use. When you create a firewall, the operation initializes this setting to <code>TRUE</code>.</p>"}, "FirewallPolicyChangeProtection": {"shape": "Boolean", "documentation": "<p>A setting indicating whether the firewall is protected against a change to the firewall policy association. Use this setting to protect against accidentally modifying the firewall policy for a firewall that is in use. When you create a firewall, the operation initializes this setting to <code>TRUE</code>.</p>"}, "Description": {"shape": "Description", "documentation": "<p>A description of the firewall.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The key:value pairs to associate with the resource.</p>"}, "EncryptionConfiguration": {"shape": "EncryptionConfiguration", "documentation": "<p>A complex type that contains settings for encryption of your firewall resources.</p>"}, "EnabledAnalysisTypes": {"shape": "EnabledAnalysisTypes", "documentation": "<p>An optional setting indicating the specific traffic analysis types to enable on the firewall. </p>"}, "TransitGatewayId": {"shape": "TransitGatewayId", "documentation": "<p>Required when creating a transit gateway-attached firewall. The unique identifier of the transit gateway to attach to this firewall. You can provide either a transit gateway from your account or one that has been shared with you through Resource Access Manager.</p> <important> <p>After creating the firewall, you cannot change the transit gateway association. To use a different transit gateway, you must create a new firewall.</p> </important> <p>For information about creating firewalls, see <a>CreateFirewall</a>. For specific guidance about transit gateway-attached firewalls, see <a href=\"https://docs.aws.amazon.com/network-firewall/latest/developerguide/tgw-firewall-considerations.html\">Considerations for transit gateway-attached firewalls</a> in the <i>Network Firewall Developer Guide</i>.</p>"}, "AvailabilityZoneMappings": {"shape": "AvailabilityZoneMappings", "documentation": "<p>Required. The Availability Zones where you want to create firewall endpoints for a transit gateway-attached firewall. You must specify at least one Availability Zone. Consider enabling the firewall in every Availability Zone where you have workloads to maintain Availability Zone independence.</p> <p>You can modify Availability Zones later using <a>AssociateAvailabilityZones</a> or <a>DisassociateAvailabilityZones</a>, but this may briefly disrupt traffic. The <code>AvailabilityZoneChangeProtection</code> setting controls whether you can make these modifications.</p>"}, "AvailabilityZoneChangeProtection": {"shape": "Boolean", "documentation": "<p>Optional. A setting indicating whether the firewall is protected against changes to its Availability Zone configuration. When set to <code>TRUE</code>, you cannot add or remove Availability Zones without first disabling this protection using <a>UpdateAvailabilityZoneChangeProtection</a>.</p> <p>Default value: <code>FALSE</code> </p>"}}}, "CreateFirewallResponse": {"type": "structure", "members": {"Firewall": {"shape": "Firewall", "documentation": "<p>The configuration settings for the firewall. These settings include the firewall policy and the subnets in your VPC to use for the firewall endpoints. </p>"}, "FirewallStatus": {"shape": "FirewallStatus", "documentation": "<p>Detailed information about the current status of a <a>Firewall</a>. You can retrieve this for a firewall by calling <a>DescribeFirewall</a> and providing the firewall name and ARN.</p> <p>The firewall status indicates a combined status. It indicates whether all subnets are up-to-date with the latest firewall configurations, which is based on the sync states config values, and also whether all subnets have their endpoints fully enabled, based on their sync states attachment values. </p>"}}}, "CreateRuleGroupRequest": {"type": "structure", "required": ["RuleGroupName", "Type", "Capacity"], "members": {"RuleGroupName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the rule group. You can't change the name of a rule group after you create it.</p>"}, "RuleGroup": {"shape": "RuleGroup", "documentation": "<p>An object that defines the rule group rules. </p> <note> <p>You must provide either this rule group setting or a <code>Rules</code> setting, but not both. </p> </note>"}, "Rules": {"shape": "RulesString", "documentation": "<p>A string containing stateful rule group rules specifications in Suricata flat format, with one rule per line. Use this to import your existing Suricata compatible rule groups. </p> <note> <p>You must provide either this rules setting or a populated <code>RuleGroup</code> setting, but not both. </p> </note> <p>You can provide your rule group specification in Suricata flat format through this setting when you create or update your rule group. The call response returns a <a>RuleGroup</a> object that Network Firewall has populated from your string. </p>"}, "Type": {"shape": "RuleGroupType", "documentation": "<p>Indicates whether the rule group is stateless or stateful. If the rule group is stateless, it contains stateless rules. If it is stateful, it contains stateful rules. </p>"}, "Description": {"shape": "Description", "documentation": "<p>A description of the rule group. </p>"}, "Capacity": {"shape": "RuleCapacity", "documentation": "<p>The maximum operating resources that this rule group can use. Rule group capacity is fixed at creation. When you update a rule group, you are limited to this capacity. When you reference a rule group from a firewall policy, Network Firewall reserves this capacity for the rule group. </p> <p>You can retrieve the capacity that would be required for a rule group before you create the rule group by calling <a>CreateRuleGroup</a> with <code>DryRun</code> set to <code>TRUE</code>. </p> <note> <p>You can't change or exceed this capacity when you update the rule group, so leave room for your rule group to grow. </p> </note> <p> <b>Capacity for a stateless rule group</b> </p> <p>For a stateless rule group, the capacity required is the sum of the capacity requirements of the individual rules that you expect to have in the rule group. </p> <p>To calculate the capacity requirement of a single rule, multiply the capacity requirement values of each of the rule's match settings:</p> <ul> <li> <p>A match setting with no criteria specified has a value of 1. </p> </li> <li> <p>A match setting with <code>Any</code> specified has a value of 1. </p> </li> <li> <p>All other match settings have a value equal to the number of elements provided in the setting. For example, a protocol setting [\"UDP\"] and a source setting [\"10.0.0.0/24\"] each have a value of 1. A protocol setting [\"UDP\",\"TCP\"] has a value of 2. A source setting [\"10.0.0.0/24\",\"********/24\",\"********/24\"] has a value of 3. </p> </li> </ul> <p>A rule with no criteria specified in any of its match settings has a capacity requirement of 1. A rule with protocol setting [\"UDP\",\"TCP\"], source setting [\"10.0.0.0/24\",\"********/24\",\"********/24\"], and a single specification or no specification for each of the other match settings has a capacity requirement of 6. </p> <p> <b>Capacity for a stateful rule group</b> </p> <p>For a stateful rule group, the minimum capacity required is the number of individual rules that you expect to have in the rule group. </p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The key:value pairs to associate with the resource.</p>"}, "DryRun": {"shape": "Boolean", "documentation": "<p>Indicates whether you want Network Firewall to just check the validity of the request, rather than run the request. </p> <p>If set to <code>TRUE</code>, Network Firewall checks whether the request can run successfully, but doesn't actually make the requested changes. The call returns the value that the request would return if you ran it with dry run set to <code>FALSE</code>, but doesn't make additions or changes to your resources. This option allows you to make sure that you have the required permissions to run the request and that your request parameters are valid. </p> <p>If set to <code>FALSE</code>, Network Firewall makes the requested changes to your resources. </p>"}, "EncryptionConfiguration": {"shape": "EncryptionConfiguration", "documentation": "<p>A complex type that contains settings for encryption of your rule group resources.</p>"}, "SourceMetadata": {"shape": "SourceMetadata", "documentation": "<p>A complex type that contains metadata about the rule group that your own rule group is copied from. You can use the metadata to keep track of updates made to the originating rule group.</p>"}, "AnalyzeRuleGroup": {"shape": "Boolean", "documentation": "<p>Indicates whether you want Network Firewall to analyze the stateless rules in the rule group for rule behavior such as asymmetric routing. If set to <code>TRUE</code>, Network Firewall runs the analysis and then creates the rule group for you. To run the stateless rule group analyzer without creating the rule group, set <code>DryRun</code> to <code>TRUE</code>.</p>"}, "SummaryConfiguration": {"shape": "SummaryConfiguration", "documentation": "<p>An object that contains a <code>RuleOptions</code> array of strings. You use <code>RuleOptions</code> to determine which of the following <a>RuleSummary</a> values are returned in response to <code>DescribeRuleGroupSummary</code>.</p> <ul> <li> <p> <code>Metadata</code> - returns</p> </li> <li> <p> <code>Msg</code> </p> </li> <li> <p> <code>SID</code> </p> </li> </ul>"}}}, "CreateRuleGroupResponse": {"type": "structure", "required": ["UpdateToken", "RuleGroupResponse"], "members": {"UpdateToken": {"shape": "UpdateToken", "documentation": "<p>A token used for optimistic locking. Network Firewall returns a token to your requests that access the rule group. The token marks the state of the rule group resource at the time of the request. </p> <p>To make changes to the rule group, you provide the token in your request. Network Firewall uses the token to ensure that the rule group hasn't changed since you last retrieved it. If it has changed, the operation fails with an <code>InvalidTokenException</code>. If this happens, retrieve the rule group again to get a current copy of it with a current token. Reapply your changes as needed, then try the operation again using the new token. </p>"}, "RuleGroupResponse": {"shape": "RuleGroupResponse", "documentation": "<p>The high-level properties of a rule group. This, along with the <a>RuleGroup</a>, define the rule group. You can retrieve all objects for a rule group by calling <a>DescribeRuleGroup</a>. </p>"}}}, "CreateTLSInspectionConfigurationRequest": {"type": "structure", "required": ["TLSInspectionConfigurationName", "TLSInspectionConfiguration"], "members": {"TLSInspectionConfigurationName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the TLS inspection configuration. You can't change the name of a TLS inspection configuration after you create it.</p>"}, "TLSInspectionConfiguration": {"shape": "TLSInspectionConfiguration", "documentation": "<p>The object that defines a TLS inspection configuration. This, along with <a>TLSInspectionConfigurationResponse</a>, define the TLS inspection configuration. You can retrieve all objects for a TLS inspection configuration by calling <a>DescribeTLSInspectionConfiguration</a>. </p> <p>Network Firewall uses a TLS inspection configuration to decrypt traffic. Network Firewall re-encrypts the traffic before sending it to its destination.</p> <p>To use a TLS inspection configuration, you add it to a new Network Firewall firewall policy, then you apply the firewall policy to a firewall. Network Firewall acts as a proxy service to decrypt and inspect the traffic traveling through your firewalls. You can reference a TLS inspection configuration from more than one firewall policy, and you can use a firewall policy in more than one firewall. For more information about using TLS inspection configurations, see <a href=\"https://docs.aws.amazon.com/network-firewall/latest/developerguide/tls-inspection.html\">Inspecting SSL/TLS traffic with TLS inspection configurations</a> in the <i>Network Firewall Developer Guide</i>.</p>"}, "Description": {"shape": "Description", "documentation": "<p>A description of the TLS inspection configuration. </p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The key:value pairs to associate with the resource.</p>"}, "EncryptionConfiguration": {"shape": "EncryptionConfiguration"}}}, "CreateTLSInspectionConfigurationResponse": {"type": "structure", "required": ["UpdateToken", "TLSInspectionConfigurationResponse"], "members": {"UpdateToken": {"shape": "UpdateToken", "documentation": "<p>A token used for optimistic locking. Network Firewall returns a token to your requests that access the TLS inspection configuration. The token marks the state of the TLS inspection configuration resource at the time of the request. </p> <p>To make changes to the TLS inspection configuration, you provide the token in your request. Network Firewall uses the token to ensure that the TLS inspection configuration hasn't changed since you last retrieved it. If it has changed, the operation fails with an <code>InvalidTokenException</code>. If this happens, retrieve the TLS inspection configuration again to get a current copy of it with a current token. Reapply your changes as needed, then try the operation again using the new token. </p>"}, "TLSInspectionConfigurationResponse": {"shape": "TLSInspectionConfigurationResponse", "documentation": "<p>The high-level properties of a TLS inspection configuration. This, along with the <a>TLSInspectionConfiguration</a>, define the TLS inspection configuration. You can retrieve all objects for a TLS inspection configuration by calling <a>DescribeTLSInspectionConfiguration</a>. </p>"}}}, "CreateVpcEndpointAssociationRequest": {"type": "structure", "required": ["FirewallArn", "VpcId", "SubnetMapping"], "members": {"FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p>"}, "VpcId": {"shape": "VpcId", "documentation": "<p>The unique identifier of the VPC where you want to create a firewall endpoint. </p>"}, "SubnetMapping": {"shape": "SubnetMapping"}, "Description": {"shape": "Description", "documentation": "<p>A description of the VPC endpoint association. </p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The key:value pairs to associate with the resource.</p>"}}}, "CreateVpcEndpointAssociationResponse": {"type": "structure", "members": {"VpcEndpointAssociation": {"shape": "VpcEndpointAssociation", "documentation": "<p>The configuration settings for the VPC endpoint association. These settings include the firewall and the VPC and subnet to use for the firewall endpoint. </p>"}, "VpcEndpointAssociationStatus": {"shape": "VpcEndpointAssociationStatus", "documentation": "<p>Detailed information about the current status of a <a>VpcEndpointAssociation</a>. You can retrieve this by calling <a>DescribeVpcEndpointAssociation</a> and providing the VPC endpoint association ARN.</p>"}}}, "CustomAction": {"type": "structure", "required": ["ActionName", "ActionDefinition"], "members": {"ActionName": {"shape": "ActionName", "documentation": "<p>The descriptive name of the custom action. You can't change the name of a custom action after you create it.</p>"}, "ActionDefinition": {"shape": "ActionDefinition", "documentation": "<p>The custom action associated with the action name.</p>"}}, "documentation": "<p>An optional, non-standard action to use for stateless packet handling. You can define this in addition to the standard action that you must specify. </p> <p>You define and name the custom actions that you want to be able to use, and then you reference them by name in your actions settings. </p> <p>You can use custom actions in the following places: </p> <ul> <li> <p>In a rule group's <a>StatelessRulesAndCustomActions</a> specification. The custom actions are available for use by name inside the <code>StatelessRulesAndCustomActions</code> where you define them. You can use them for your stateless rule actions to specify what to do with a packet that matches the rule's match attributes. </p> </li> <li> <p>In a <a>FirewallPolicy</a> specification, in <code>StatelessCustomActions</code>. The custom actions are available for use inside the policy where you define them. You can use them for the policy's default stateless actions settings to specify what to do with packets that don't match any of the policy's stateless rules. </p> </li> </ul>"}, "CustomActions": {"type": "list", "member": {"shape": "CustomAction"}}, "DeepThreatInspection": {"type": "boolean"}, "DeleteFirewallPolicyRequest": {"type": "structure", "members": {"FirewallPolicyName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the firewall policy. You can't change the name of a firewall policy after you create it.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "FirewallPolicyArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall policy.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}}}, "DeleteFirewallPolicyResponse": {"type": "structure", "required": ["FirewallPolicyResponse"], "members": {"FirewallPolicyResponse": {"shape": "FirewallPolicyResponse", "documentation": "<p>The object containing the definition of the <a>FirewallPolicyResponse</a> that you asked to delete. </p>"}}}, "DeleteFirewallRequest": {"type": "structure", "members": {"FirewallName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the firewall. You can't change the name of a firewall after you create it.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}}}, "DeleteFirewallResponse": {"type": "structure", "members": {"Firewall": {"shape": "Firewall"}, "FirewallStatus": {"shape": "FirewallStatus"}}}, "DeleteNetworkFirewallTransitGatewayAttachmentRequest": {"type": "structure", "required": ["TransitGatewayAttachmentId"], "members": {"TransitGatewayAttachmentId": {"shape": "TransitGatewayAttachmentId", "documentation": "<p>Required. The unique identifier of the transit gateway attachment to delete.</p>"}}}, "DeleteNetworkFirewallTransitGatewayAttachmentResponse": {"type": "structure", "required": ["TransitGatewayAttachmentId", "TransitGatewayAttachmentStatus"], "members": {"TransitGatewayAttachmentId": {"shape": "TransitGatewayAttachmentId", "documentation": "<p>The ID of the transit gateway attachment that was deleted.</p>"}, "TransitGatewayAttachmentStatus": {"shape": "TransitGatewayAttachmentStatus", "documentation": "<p>The current status of the transit gateway attachment deletion process.</p> <p>Valid values are:</p> <ul> <li> <p> <code>CREATING</code> - The attachment is being created</p> </li> <li> <p> <code>DELETING</code> - The attachment is being deleted</p> </li> <li> <p> <code>DELETED</code> - The attachment has been deleted</p> </li> <li> <p> <code>FAILED</code> - The attachment creation has failed and cannot be recovered</p> </li> <li> <p> <code>ERROR</code> - The attachment is in an error state that might be recoverable</p> </li> <li> <p> <code>READY</code> - The attachment is active and processing traffic</p> </li> <li> <p> <code>PENDING_ACCEPTANCE</code> - The attachment is waiting to be accepted</p> </li> <li> <p> <code>REJECTING</code> - The attachment is in the process of being rejected</p> </li> <li> <p> <code>REJECTED</code> - The attachment has been rejected</p> </li> </ul>"}}}, "DeleteResourcePolicyRequest": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the rule group or firewall policy whose resource policy you want to delete. </p>"}}}, "DeleteResourcePolicyResponse": {"type": "structure", "members": {}}, "DeleteRuleGroupRequest": {"type": "structure", "members": {"RuleGroupName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the rule group. You can't change the name of a rule group after you create it.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "RuleGroupArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the rule group.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "Type": {"shape": "RuleGroupType", "documentation": "<p>Indicates whether the rule group is stateless or stateful. If the rule group is stateless, it contains stateless rules. If it is stateful, it contains stateful rules. </p> <note> <p>This setting is required for requests that do not include the <code>RuleGroupARN</code>.</p> </note>"}}}, "DeleteRuleGroupResponse": {"type": "structure", "required": ["RuleGroupResponse"], "members": {"RuleGroupResponse": {"shape": "RuleGroupResponse", "documentation": "<p>The high-level properties of a rule group. This, along with the <a>RuleGroup</a>, define the rule group. You can retrieve all objects for a rule group by calling <a>DescribeRuleGroup</a>. </p>"}}}, "DeleteTLSInspectionConfigurationRequest": {"type": "structure", "members": {"TLSInspectionConfigurationArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the TLS inspection configuration.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "TLSInspectionConfigurationName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the TLS inspection configuration. You can't change the name of a TLS inspection configuration after you create it.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}}}, "DeleteTLSInspectionConfigurationResponse": {"type": "structure", "required": ["TLSInspectionConfigurationResponse"], "members": {"TLSInspectionConfigurationResponse": {"shape": "TLSInspectionConfigurationResponse", "documentation": "<p>The high-level properties of a TLS inspection configuration. This, along with the <a>TLSInspectionConfiguration</a>, define the TLS inspection configuration. You can retrieve all objects for a TLS inspection configuration by calling <a>DescribeTLSInspectionConfiguration</a>. </p>"}}}, "DeleteVpcEndpointAssociationRequest": {"type": "structure", "required": ["VpcEndpointAssociationArn"], "members": {"VpcEndpointAssociationArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of a VPC endpoint association.</p>"}}}, "DeleteVpcEndpointAssociationResponse": {"type": "structure", "members": {"VpcEndpointAssociation": {"shape": "VpcEndpointAssociation", "documentation": "<p>The configuration settings for the VPC endpoint association. These settings include the firewall and the VPC and subnet to use for the firewall endpoint. </p>"}, "VpcEndpointAssociationStatus": {"shape": "VpcEndpointAssociationStatus", "documentation": "<p>Detailed information about the current status of a <a>VpcEndpointAssociation</a>. You can retrieve this by calling <a>DescribeVpcEndpointAssociation</a> and providing the VPC endpoint association ARN.</p>"}}}, "DescribeFirewallMetadataRequest": {"type": "structure", "members": {"FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p>"}}}, "DescribeFirewallMetadataResponse": {"type": "structure", "members": {"FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p>"}, "FirewallPolicyArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall policy.</p>"}, "Description": {"shape": "Description", "documentation": "<p>A description of the firewall.</p>"}, "Status": {"shape": "FirewallStatusValue", "documentation": "<p>The readiness of the configured firewall to handle network traffic across all of the Availability Zones where you have it configured. This setting is <code>READY</code> only when the <code>ConfigurationSyncStateSummary</code> value is <code>IN_SYNC</code> and the <code>Attachment</code> <code>Status</code> values for all of the configured subnets are <code>READY</code>. </p>"}, "SupportedAvailabilityZones": {"shape": "SupportedAvailabilityZones", "documentation": "<p>The Availability Zones that the firewall currently supports. This includes all Availability Zones for which the firewall has a subnet defined. </p>"}, "TransitGatewayAttachmentId": {"shape": "TransitGatewayAttachmentId", "documentation": "<p>The unique identifier of the transit gateway attachment associated with this firewall. This field is only present for transit gateway-attached firewalls.</p>"}}}, "DescribeFirewallPolicyRequest": {"type": "structure", "members": {"FirewallPolicyName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the firewall policy. You can't change the name of a firewall policy after you create it.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "FirewallPolicyArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall policy.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}}}, "DescribeFirewallPolicyResponse": {"type": "structure", "required": ["UpdateToken", "FirewallPolicyResponse"], "members": {"UpdateToken": {"shape": "UpdateToken", "documentation": "<p>A token used for optimistic locking. Network Firewall returns a token to your requests that access the firewall policy. The token marks the state of the policy resource at the time of the request. </p> <p>To make changes to the policy, you provide the token in your request. Network Firewall uses the token to ensure that the policy hasn't changed since you last retrieved it. If it has changed, the operation fails with an <code>InvalidTokenException</code>. If this happens, retrieve the firewall policy again to get a current copy of it with current token. Reapply your changes as needed, then try the operation again using the new token. </p>"}, "FirewallPolicyResponse": {"shape": "FirewallPolicyResponse", "documentation": "<p>The high-level properties of a firewall policy. This, along with the <a>FirewallPolicy</a>, define the policy. You can retrieve all objects for a firewall policy by calling <a>DescribeFirewallPolicy</a>. </p>"}, "FirewallPolicy": {"shape": "FirewallPolicy", "documentation": "<p>The policy for the specified firewall policy. </p>"}}}, "DescribeFirewallRequest": {"type": "structure", "members": {"FirewallName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the firewall. You can't change the name of a firewall after you create it.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}}}, "DescribeFirewallResponse": {"type": "structure", "members": {"UpdateToken": {"shape": "UpdateToken", "documentation": "<p>An optional token that you can use for optimistic locking. Network Firewall returns a token to your requests that access the firewall. The token marks the state of the firewall resource at the time of the request. </p> <p>To make an unconditional change to the firewall, omit the token in your update request. Without the token, Network Firewall performs your updates regardless of whether the firewall has changed since you last retrieved it.</p> <p>To make a conditional change to the firewall, provide the token in your update request. Network Firewall uses the token to ensure that the firewall hasn't changed since you last retrieved it. If it has changed, the operation fails with an <code>InvalidTokenException</code>. If this happens, retrieve the firewall again to get a current copy of it with a new token. Reapply your changes as needed, then try the operation again using the new token. </p>"}, "Firewall": {"shape": "Firewall", "documentation": "<p>The configuration settings for the firewall. These settings include the firewall policy and the subnets in your VPC to use for the firewall endpoints. </p>"}, "FirewallStatus": {"shape": "FirewallStatus", "documentation": "<p>Detailed information about the current status of a <a>Firewall</a>. You can retrieve this for a firewall by calling <a>DescribeFirewall</a> and providing the firewall name and ARN.</p> <p>The firewall status indicates a combined status. It indicates whether all subnets are up-to-date with the latest firewall configurations, which is based on the sync states config values, and also whether all subnets have their endpoints fully enabled, based on their sync states attachment values. </p>"}}}, "DescribeFlowOperationRequest": {"type": "structure", "required": ["FirewallArn", "FlowOperationId"], "members": {"FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p>"}, "AvailabilityZone": {"shape": "AvailabilityZone", "documentation": "<p>The ID of the Availability Zone where the firewall is located. For example, <code>us-east-2a</code>.</p> <p>Defines the scope a flow operation. You can use up to 20 filters to configure a single flow operation.</p>"}, "VpcEndpointAssociationArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of a VPC endpoint association.</p>"}, "VpcEndpointId": {"shape": "VpcEndpointId", "documentation": "<p>A unique identifier for the primary endpoint associated with a firewall.</p>"}, "FlowOperationId": {"shape": "FlowOperationId", "documentation": "<p>A unique identifier for the flow operation. This ID is returned in the responses to start and list commands. You provide to describe commands.</p>"}}}, "DescribeFlowOperationResponse": {"type": "structure", "members": {"FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p>"}, "AvailabilityZone": {"shape": "AvailabilityZone", "documentation": "<p>The ID of the Availability Zone where the firewall is located. For example, <code>us-east-2a</code>.</p> <p>Defines the scope a flow operation. You can use up to 20 filters to configure a single flow operation.</p>"}, "VpcEndpointAssociationArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of a VPC endpoint association.</p>"}, "VpcEndpointId": {"shape": "VpcEndpointId", "documentation": "<p>A unique identifier for the primary endpoint associated with a firewall.</p>"}, "FlowOperationId": {"shape": "FlowOperationId", "documentation": "<p>A unique identifier for the flow operation. This ID is returned in the responses to start and list commands. You provide to describe commands.</p>"}, "FlowOperationType": {"shape": "FlowOperationType", "documentation": "<p>Defines the type of <code>FlowOperation</code>.</p>"}, "FlowOperationStatus": {"shape": "FlowOperationStatus", "documentation": "<p>Returns the status of the flow operation. This string is returned in the responses to start, list, and describe commands.</p> <p>If the status is <code>COMPLETED_WITH_ERRORS</code>, results may be returned with any number of <code>Flows</code> missing from the response. If the status is <code>FAILED</code>, <code>Flows</code> returned will be empty.</p>"}, "StatusMessage": {"shape": "StatusReason", "documentation": "<p>If the asynchronous operation fails, Network Firewall populates this with the reason for the error or failure. Options include <code>Flow operation error</code> and <code>Flow timeout</code>.</p>"}, "FlowRequestTimestamp": {"shape": "FlowRequestTimestamp", "documentation": "<p>A timestamp indicating when the Suricata engine identified flows impacted by an operation. </p>"}, "FlowOperation": {"shape": "FlowOperation", "documentation": "<p>Returns key information about a flow operation, such as related statuses, unique identifiers, and all filters defined in the operation.</p>"}}}, "DescribeLoggingConfigurationRequest": {"type": "structure", "members": {"FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "FirewallName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the firewall. You can't change the name of a firewall after you create it.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}}}, "DescribeLoggingConfigurationResponse": {"type": "structure", "members": {"FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p>"}, "LoggingConfiguration": {"shape": "LoggingConfiguration"}, "EnableMonitoringDashboard": {"shape": "EnableMonitoringDashboard", "documentation": "<p>A boolean that reflects whether or not the firewall monitoring dashboard is enabled on a firewall.</p> <p> Returns <code>TRUE</code> when the firewall monitoring dashboard is enabled on the firewall. Returns <code>FALSE</code> when the firewall monitoring dashboard is not enabled on the firewall. </p>"}}}, "DescribeResourcePolicyRequest": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the rule group or firewall policy whose resource policy you want to retrieve. </p>"}}}, "DescribeResourcePolicyResponse": {"type": "structure", "members": {"Policy": {"shape": "PolicyString", "documentation": "<p>The IAM policy for the resource. </p>"}}}, "DescribeRuleGroupMetadataRequest": {"type": "structure", "members": {"RuleGroupName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the rule group. You can't change the name of a rule group after you create it.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "RuleGroupArn": {"shape": "ResourceArn", "documentation": "<p>The descriptive name of the rule group. You can't change the name of a rule group after you create it.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "Type": {"shape": "RuleGroupType", "documentation": "<p>Indicates whether the rule group is stateless or stateful. If the rule group is stateless, it contains stateless rules. If it is stateful, it contains stateful rules. </p> <note> <p>This setting is required for requests that do not include the <code>RuleGroupARN</code>.</p> </note>"}}}, "DescribeRuleGroupMetadataResponse": {"type": "structure", "required": ["RuleGroupArn", "RuleGroupName"], "members": {"RuleGroupArn": {"shape": "ResourceArn", "documentation": "<p>The descriptive name of the rule group. You can't change the name of a rule group after you create it.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "RuleGroupName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the rule group. You can't change the name of a rule group after you create it.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "Description": {"shape": "Description", "documentation": "<p>Returns the metadata objects for the specified rule group. </p>"}, "Type": {"shape": "RuleGroupType", "documentation": "<p>Indicates whether the rule group is stateless or stateful. If the rule group is stateless, it contains stateless rules. If it is stateful, it contains stateful rules. </p> <note> <p>This setting is required for requests that do not include the <code>RuleGroupARN</code>.</p> </note>"}, "Capacity": {"shape": "RuleCapacity", "documentation": "<p>The maximum operating resources that this rule group can use. Rule group capacity is fixed at creation. When you update a rule group, you are limited to this capacity. When you reference a rule group from a firewall policy, Network Firewall reserves this capacity for the rule group. </p> <p>You can retrieve the capacity that would be required for a rule group before you create the rule group by calling <a>CreateRuleGroup</a> with <code>DryRun</code> set to <code>TRUE</code>. </p>"}, "StatefulRuleOptions": {"shape": "StatefulRuleOptions"}, "LastModifiedTime": {"shape": "LastUpdateTime", "documentation": "<p>A timestamp indicating when the rule group was last modified.</p>"}}}, "DescribeRuleGroupRequest": {"type": "structure", "members": {"RuleGroupName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the rule group. You can't change the name of a rule group after you create it.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "RuleGroupArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the rule group.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "Type": {"shape": "RuleGroupType", "documentation": "<p>Indicates whether the rule group is stateless or stateful. If the rule group is stateless, it contains stateless rules. If it is stateful, it contains stateful rules. </p> <note> <p>This setting is required for requests that do not include the <code>RuleGroupARN</code>.</p> </note>"}, "AnalyzeRuleGroup": {"shape": "Boolean", "documentation": "<p>Indicates whether you want Network Firewall to analyze the stateless rules in the rule group for rule behavior such as asymmetric routing. If set to <code>TRUE</code>, Network Firewall runs the analysis.</p>"}}}, "DescribeRuleGroupResponse": {"type": "structure", "required": ["UpdateToken", "RuleGroupResponse"], "members": {"UpdateToken": {"shape": "UpdateToken", "documentation": "<p>A token used for optimistic locking. Network Firewall returns a token to your requests that access the rule group. The token marks the state of the rule group resource at the time of the request. </p> <p>To make changes to the rule group, you provide the token in your request. Network Firewall uses the token to ensure that the rule group hasn't changed since you last retrieved it. If it has changed, the operation fails with an <code>InvalidTokenException</code>. If this happens, retrieve the rule group again to get a current copy of it with a current token. Reapply your changes as needed, then try the operation again using the new token. </p>"}, "RuleGroup": {"shape": "RuleGroup", "documentation": "<p>The object that defines the rules in a rule group. This, along with <a>RuleGroupResponse</a>, define the rule group. You can retrieve all objects for a rule group by calling <a>DescribeRuleGroup</a>. </p> <p>Network Firewall uses a rule group to inspect and control network traffic. You define stateless rule groups to inspect individual packets and you define stateful rule groups to inspect packets in the context of their traffic flow. </p> <p>To use a rule group, you include it by reference in an Network Firewall firewall policy, then you use the policy in a firewall. You can reference a rule group from more than one firewall policy, and you can use a firewall policy in more than one firewall. </p>"}, "RuleGroupResponse": {"shape": "RuleGroupResponse", "documentation": "<p>The high-level properties of a rule group. This, along with the <a>RuleGroup</a>, define the rule group. You can retrieve all objects for a rule group by calling <a>DescribeRuleGroup</a>. </p>"}}}, "DescribeRuleGroupSummaryRequest": {"type": "structure", "members": {"RuleGroupName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the rule group. You can't change the name of a rule group after you create it.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "RuleGroupArn": {"shape": "ResourceArn", "documentation": "<p>Required. The Amazon Resource Name (ARN) of the rule group.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "Type": {"shape": "RuleGroupType", "documentation": "<p>The type of rule group you want a summary for. This is a required field.</p> <p>Valid value: <code>STATEFUL</code> </p> <p>Note that <code>STATELESS</code> exists but is not currently supported. If you provide <code>STATELESS</code>, an exception is returned.</p>"}}}, "DescribeRuleGroupSummaryResponse": {"type": "structure", "required": ["RuleGroupName"], "members": {"RuleGroupName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the rule group. You can't change the name of a rule group after you create it.</p>"}, "Description": {"shape": "Description", "documentation": "<p>A description of the rule group. </p>"}, "Summary": {"shape": "Summary", "documentation": "<p>A complex type that contains rule information based on the rule group's configured summary settings. The content varies depending on the fields that you specified to extract in your SummaryConfiguration. When you haven't configured any summary settings, this returns an empty array. The response might include:</p> <ul> <li> <p>Rule identifiers</p> </li> <li> <p>Rule descriptions</p> </li> <li> <p>Any metadata fields that you specified in your SummaryConfiguration</p> </li> </ul>"}}}, "DescribeTLSInspectionConfigurationRequest": {"type": "structure", "members": {"TLSInspectionConfigurationArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the TLS inspection configuration.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "TLSInspectionConfigurationName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the TLS inspection configuration. You can't change the name of a TLS inspection configuration after you create it.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}}}, "DescribeTLSInspectionConfigurationResponse": {"type": "structure", "required": ["UpdateToken", "TLSInspectionConfigurationResponse"], "members": {"UpdateToken": {"shape": "UpdateToken", "documentation": "<p>A token used for optimistic locking. Network Firewall returns a token to your requests that access the TLS inspection configuration. The token marks the state of the TLS inspection configuration resource at the time of the request. </p> <p>To make changes to the TLS inspection configuration, you provide the token in your request. Network Firewall uses the token to ensure that the TLS inspection configuration hasn't changed since you last retrieved it. If it has changed, the operation fails with an <code>InvalidTokenException</code>. If this happens, retrieve the TLS inspection configuration again to get a current copy of it with a current token. Reapply your changes as needed, then try the operation again using the new token. </p>"}, "TLSInspectionConfiguration": {"shape": "TLSInspectionConfiguration", "documentation": "<p>The object that defines a TLS inspection configuration. This, along with <a>TLSInspectionConfigurationResponse</a>, define the TLS inspection configuration. You can retrieve all objects for a TLS inspection configuration by calling <a>DescribeTLSInspectionConfiguration</a>. </p> <p>Network Firewall uses a TLS inspection configuration to decrypt traffic. Network Firewall re-encrypts the traffic before sending it to its destination.</p> <p>To use a TLS inspection configuration, you add it to a new Network Firewall firewall policy, then you apply the firewall policy to a firewall. Network Firewall acts as a proxy service to decrypt and inspect the traffic traveling through your firewalls. You can reference a TLS inspection configuration from more than one firewall policy, and you can use a firewall policy in more than one firewall. For more information about using TLS inspection configurations, see <a href=\"https://docs.aws.amazon.com/network-firewall/latest/developerguide/tls-inspection.html\">Inspecting SSL/TLS traffic with TLS inspection configurations</a> in the <i>Network Firewall Developer Guide</i>.</p>"}, "TLSInspectionConfigurationResponse": {"shape": "TLSInspectionConfigurationResponse", "documentation": "<p>The high-level properties of a TLS inspection configuration. This, along with the <a>TLSInspectionConfiguration</a>, define the TLS inspection configuration. You can retrieve all objects for a TLS inspection configuration by calling <a>DescribeTLSInspectionConfiguration</a>. </p>"}}}, "DescribeVpcEndpointAssociationRequest": {"type": "structure", "required": ["VpcEndpointAssociationArn"], "members": {"VpcEndpointAssociationArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of a VPC endpoint association.</p>"}}}, "DescribeVpcEndpointAssociationResponse": {"type": "structure", "members": {"VpcEndpointAssociation": {"shape": "VpcEndpointAssociation", "documentation": "<p>The configuration settings for the VPC endpoint association. These settings include the firewall and the VPC and subnet to use for the firewall endpoint. </p>"}, "VpcEndpointAssociationStatus": {"shape": "VpcEndpointAssociationStatus", "documentation": "<p>Detailed information about the current status of a <a>VpcEndpointAssociation</a>. You can retrieve this by calling <a>DescribeVpcEndpointAssociation</a> and providing the VPC endpoint association ARN.</p>"}}}, "Description": {"type": "string", "max": 512, "pattern": "^.*$"}, "Destination": {"type": "string", "max": 1024, "min": 1, "pattern": "^.*$"}, "Dimension": {"type": "structure", "required": ["Value"], "members": {"Value": {"shape": "DimensionValue", "documentation": "<p>The value to use in the custom metric dimension.</p>"}}, "documentation": "<p>The value to use in an Amazon CloudWatch custom metric dimension. This is used in the <code>PublishMetrics</code> <a>CustomAction</a>. A CloudWatch custom metric dimension is a name/value pair that's part of the identity of a metric. </p> <p>Network Firewall sets the dimension name to <code>CustomAction</code> and you provide the dimension value. </p> <p>For more information about CloudWatch custom metric dimensions, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/publishingMetrics.html#usingDimensions\">Publishing Custom Metrics</a> in the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/WhatIsCloudWatch.html\">Amazon CloudWatch User Guide</a>.</p>"}, "DimensionValue": {"type": "string", "max": 128, "min": 1, "pattern": "^[a-zA-Z0-9-_ ]+$"}, "Dimensions": {"type": "list", "member": {"shape": "Dimension"}, "max": 1, "min": 1}, "DisassociateAvailabilityZonesRequest": {"type": "structure", "required": ["AvailabilityZoneMappings"], "members": {"UpdateToken": {"shape": "UpdateToken", "documentation": "<p>An optional token that you can use for optimistic locking. Network Firewall returns a token to your requests that access the firewall. The token marks the state of the firewall resource at the time of the request. </p> <p>To make an unconditional change to the firewall, omit the token in your update request. Without the token, Network Firewall performs your updates regardless of whether the firewall has changed since you last retrieved it.</p> <p>To make a conditional change to the firewall, provide the token in your update request. Network Firewall uses the token to ensure that the firewall hasn't changed since you last retrieved it. If it has changed, the operation fails with an <code>InvalidTokenException</code>. If this happens, retrieve the firewall again to get a current copy of it with a new token. Reapply your changes as needed, then try the operation again using the new token. </p>"}, "FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "FirewallName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the firewall. You can't change the name of a firewall after you create it.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "AvailabilityZoneMappings": {"shape": "AvailabilityZoneMappings", "documentation": "<p>Required. The Availability Zones to remove from the firewall's configuration.</p>"}}}, "DisassociateAvailabilityZonesResponse": {"type": "structure", "members": {"FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p>"}, "FirewallName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the firewall. You can't change the name of a firewall after you create it.</p>"}, "AvailabilityZoneMappings": {"shape": "AvailabilityZoneMappings", "documentation": "<p>The remaining Availability Zones where the firewall has endpoints after the disassociation.</p>"}, "UpdateToken": {"shape": "UpdateToken", "documentation": "<p>An optional token that you can use for optimistic locking. Network Firewall returns a token to your requests that access the firewall. The token marks the state of the firewall resource at the time of the request. </p> <p>To make an unconditional change to the firewall, omit the token in your update request. Without the token, Network Firewall performs your updates regardless of whether the firewall has changed since you last retrieved it.</p> <p>To make a conditional change to the firewall, provide the token in your update request. Network Firewall uses the token to ensure that the firewall hasn't changed since you last retrieved it. If it has changed, the operation fails with an <code>InvalidTokenException</code>. If this happens, retrieve the firewall again to get a current copy of it with a new token. Reapply your changes as needed, then try the operation again using the new token. </p>"}}}, "DisassociateSubnetsRequest": {"type": "structure", "required": ["SubnetIds"], "members": {"UpdateToken": {"shape": "UpdateToken", "documentation": "<p>An optional token that you can use for optimistic locking. Network Firewall returns a token to your requests that access the firewall. The token marks the state of the firewall resource at the time of the request. </p> <p>To make an unconditional change to the firewall, omit the token in your update request. Without the token, Network Firewall performs your updates regardless of whether the firewall has changed since you last retrieved it.</p> <p>To make a conditional change to the firewall, provide the token in your update request. Network Firewall uses the token to ensure that the firewall hasn't changed since you last retrieved it. If it has changed, the operation fails with an <code>InvalidTokenException</code>. If this happens, retrieve the firewall again to get a current copy of it with a new token. Reapply your changes as needed, then try the operation again using the new token. </p>"}, "FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "FirewallName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the firewall. You can't change the name of a firewall after you create it.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "SubnetIds": {"shape": "AzSubnets", "documentation": "<p>The unique identifiers for the subnets that you want to disassociate. </p>"}}}, "DisassociateSubnetsResponse": {"type": "structure", "members": {"FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p>"}, "FirewallName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the firewall. You can't change the name of a firewall after you create it.</p>"}, "SubnetMappings": {"shape": "SubnetMappings", "documentation": "<p>The IDs of the subnets that are associated with the firewall. </p>"}, "UpdateToken": {"shape": "UpdateToken", "documentation": "<p>An optional token that you can use for optimistic locking. Network Firewall returns a token to your requests that access the firewall. The token marks the state of the firewall resource at the time of the request. </p> <p>To make an unconditional change to the firewall, omit the token in your update request. Without the token, Network Firewall performs your updates regardless of whether the firewall has changed since you last retrieved it.</p> <p>To make a conditional change to the firewall, provide the token in your update request. Network Firewall uses the token to ensure that the firewall hasn't changed since you last retrieved it. If it has changed, the operation fails with an <code>InvalidTokenException</code>. If this happens, retrieve the firewall again to get a current copy of it with a new token. Reapply your changes as needed, then try the operation again using the new token. </p>"}}}, "Domain": {"type": "string"}, "EnableMonitoringDashboard": {"type": "boolean"}, "EnabledAnalysisType": {"type": "string", "enum": ["TLS_SNI", "HTTP_HOST"]}, "EnabledAnalysisTypes": {"type": "list", "member": {"shape": "EnabledAnalysisType"}}, "EncryptionConfiguration": {"type": "structure", "required": ["Type"], "members": {"KeyId": {"shape": "KeyId", "documentation": "<p>The ID of the Amazon Web Services Key Management Service (KMS) customer managed key. You can use any of the key identifiers that KMS supports, unless you're using a key that's managed by another account. If you're using a key managed by another account, then specify the key ARN. For more information, see <a href=\"https://docs.aws.amazon.com/kms/latest/developerguide/concepts.html#key-id\">Key ID</a> in the <i>Amazon Web Services KMS Developer Guide</i>.</p>"}, "Type": {"shape": "EncryptionType", "documentation": "<p>The type of Amazon Web Services KMS key to use for encryption of your Network Firewall resources.</p>"}}, "documentation": "<p>A complex type that contains optional Amazon Web Services Key Management Service (KMS) encryption settings for your Network Firewall resources. Your data is encrypted by default with an Amazon Web Services owned key that Amazon Web Services owns and manages for you. You can use either the Amazon Web Services owned key, or provide your own customer managed key. To learn more about KMS encryption of your Network Firewall resources, see <a href=\"https://docs.aws.amazon.com/kms/latest/developerguide/kms-encryption-at-rest.html\">Encryption at rest with Amazon Web Services Key Managment Service</a> in the <i>Network Firewall Developer Guide</i>.</p>"}, "EncryptionType": {"type": "string", "enum": ["CUSTOMER_KMS", "AWS_OWNED_KMS_KEY"]}, "EndTime": {"type": "timestamp"}, "EndpointId": {"type": "string"}, "ErrorMessage": {"type": "string"}, "Firewall": {"type": "structure", "required": ["FirewallPolicyArn", "VpcId", "SubnetMappings", "FirewallId"], "members": {"FirewallName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the firewall. You can't change the name of a firewall after you create it.</p>"}, "FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p>"}, "FirewallPolicyArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall policy.</p> <p>The relationship of firewall to firewall policy is many to one. Each firewall requires one firewall policy association, and you can use the same firewall policy for multiple firewalls. </p>"}, "VpcId": {"shape": "VpcId", "documentation": "<p>The unique identifier of the VPC where the firewall is in use. </p>"}, "SubnetMappings": {"shape": "SubnetMappings", "documentation": "<p>The primary public subnets that Network Firewall is using for the firewall. Network Firewall creates a firewall endpoint in each subnet. Create a subnet mapping for each Availability Zone where you want to use the firewall.</p> <p>These subnets are all defined for a single, primary VPC, and each must belong to a different Availability Zone. Each of these subnets establishes the availability of the firewall in its Availability Zone. </p> <p>In addition to these subnets, you can define other endpoints for the firewall in <code>VpcEndpointAssociation</code> resources. You can define these additional endpoints for any VPC, and for any of the Availability Zones where the firewall resource already has a subnet mapping. VPC endpoint associations give you the ability to protect multiple VPCs using a single firewall, and to define multiple firewall endpoints for a VPC in a single Availability Zone. </p>"}, "DeleteProtection": {"shape": "Boolean", "documentation": "<p>A flag indicating whether it is possible to delete the firewall. A setting of <code>TRUE</code> indicates that the firewall is protected against deletion. Use this setting to protect against accidentally deleting a firewall that is in use. When you create a firewall, the operation initializes this flag to <code>TRUE</code>.</p>"}, "SubnetChangeProtection": {"shape": "Boolean", "documentation": "<p>A setting indicating whether the firewall is protected against changes to the subnet associations. Use this setting to protect against accidentally modifying the subnet associations for a firewall that is in use. When you create a firewall, the operation initializes this setting to <code>TRUE</code>.</p>"}, "FirewallPolicyChangeProtection": {"shape": "Boolean", "documentation": "<p>A setting indicating whether the firewall is protected against a change to the firewall policy association. Use this setting to protect against accidentally modifying the firewall policy for a firewall that is in use. When you create a firewall, the operation initializes this setting to <code>TRUE</code>.</p>"}, "Description": {"shape": "Description", "documentation": "<p>A description of the firewall.</p>"}, "FirewallId": {"shape": "ResourceId", "documentation": "<p>The unique identifier for the firewall. </p>"}, "Tags": {"shape": "TagList", "documentation": "<p/>"}, "EncryptionConfiguration": {"shape": "EncryptionConfiguration", "documentation": "<p>A complex type that contains the Amazon Web Services KMS encryption configuration settings for your firewall.</p>"}, "NumberOfAssociations": {"shape": "NumberOfAssociations", "documentation": "<p>The number of <code>VpcEndpointAssociation</code> resources that use this firewall. </p>"}, "EnabledAnalysisTypes": {"shape": "EnabledAnalysisTypes", "documentation": "<p>An optional setting indicating the specific traffic analysis types to enable on the firewall. </p>"}, "TransitGatewayId": {"shape": "TransitGatewayId", "documentation": "<p>The unique identifier of the transit gateway associated with this firewall. This field is only present for transit gateway-attached firewalls.</p>"}, "TransitGatewayOwnerAccountId": {"shape": "AWSAccountId", "documentation": "<p>The Amazon Web Services account ID that owns the transit gateway. This may be different from the firewall owner's account ID when using a shared transit gateway.</p>"}, "AvailabilityZoneMappings": {"shape": "AvailabilityZoneMappings", "documentation": "<p>The Availability Zones where the firewall endpoints are created for a transit gateway-attached firewall. Each mapping specifies an Availability Zone where the firewall processes traffic.</p>"}, "AvailabilityZoneChangeProtection": {"shape": "Boolean", "documentation": "<p>A setting indicating whether the firewall is protected against changes to its Availability Zone configuration. When set to <code>TRUE</code>, you must first disable this protection before adding or removing Availability Zones.</p>"}}, "documentation": "<p>A firewall defines the behavior of a firewall, the main VPC where the firewall is used, the Availability Zones where the firewall can be used, and one subnet to use for a firewall endpoint within each of the Availability Zones. The Availability Zones are defined implicitly in the subnet specifications.</p> <p>In addition to the firewall endpoints that you define in this <code>Firewall</code> specification, you can create firewall endpoints in <code>VpcEndpointAssociation</code> resources for any VPC, in any Availability Zone where the firewall is already in use. </p> <p>The status of the firewall, for example whether it's ready to filter network traffic, is provided in the corresponding <a>FirewallStatus</a>. You can retrieve both the firewall and firewall status by calling <a>DescribeFirewall</a>.</p>"}, "FirewallMetadata": {"type": "structure", "members": {"FirewallName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the firewall. You can't change the name of a firewall after you create it.</p>"}, "FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p>"}, "TransitGatewayAttachmentId": {"shape": "TransitGatewayAttachmentId", "documentation": "<p>The unique identifier of the transit gateway attachment associated with this firewall. This field is only present for transit gateway-attached firewalls.</p>"}}, "documentation": "<p>High-level information about a firewall, returned by operations like create and describe. You can use the information provided in the metadata to retrieve and manage a firewall.</p>"}, "FirewallPolicies": {"type": "list", "member": {"shape": "FirewallPolicyMetadata"}}, "FirewallPolicy": {"type": "structure", "required": ["StatelessDefaultActions", "StatelessFragmentDefaultActions"], "members": {"StatelessRuleGroupReferences": {"shape": "StatelessRuleGroupReferences", "documentation": "<p>References to the stateless rule groups that are used in the policy. These define the matching criteria in stateless rules. </p>"}, "StatelessDefaultActions": {"shape": "StatelessActions", "documentation": "<p>The actions to take on a packet if it doesn't match any of the stateless rules in the policy. If you want non-matching packets to be forwarded for stateful inspection, specify <code>aws:forward_to_sfe</code>. </p> <p>You must specify one of the standard actions: <code>aws:pass</code>, <code>aws:drop</code>, or <code>aws:forward_to_sfe</code>. In addition, you can specify custom actions that are compatible with your standard section choice.</p> <p>For example, you could specify <code>[\"aws:pass\"]</code> or you could specify <code>[\"aws:pass\", “customActionName”]</code>. For information about compatibility, see the custom action descriptions under <a>CustomAction</a>.</p>"}, "StatelessFragmentDefaultActions": {"shape": "StatelessActions", "documentation": "<p>The actions to take on a fragmented UDP packet if it doesn't match any of the stateless rules in the policy. Network Firewall only manages UDP packet fragments and silently drops packet fragments for other protocols. If you want non-matching fragmented UDP packets to be forwarded for stateful inspection, specify <code>aws:forward_to_sfe</code>. </p> <p>You must specify one of the standard actions: <code>aws:pass</code>, <code>aws:drop</code>, or <code>aws:forward_to_sfe</code>. In addition, you can specify custom actions that are compatible with your standard section choice.</p> <p>For example, you could specify <code>[\"aws:pass\"]</code> or you could specify <code>[\"aws:pass\", “customActionName”]</code>. For information about compatibility, see the custom action descriptions under <a>CustomAction</a>.</p>"}, "StatelessCustomActions": {"shape": "CustomActions", "documentation": "<p>The custom action definitions that are available for use in the firewall policy's <code>StatelessDefaultActions</code> setting. You name each custom action that you define, and then you can use it by name in your default actions specifications.</p>"}, "StatefulRuleGroupReferences": {"shape": "StatefulRuleGroupReferences", "documentation": "<p>References to the stateful rule groups that are used in the policy. These define the inspection criteria in stateful rules. </p>"}, "StatefulDefaultActions": {"shape": "StatefulActions", "documentation": "<p>The default actions to take on a packet that doesn't match any stateful rules. The stateful default action is optional, and is only valid when using the strict rule order.</p> <p>Valid values of the stateful default action:</p> <ul> <li> <p>aws:drop_strict</p> </li> <li> <p>aws:drop_established</p> </li> <li> <p>aws:alert_strict</p> </li> <li> <p>aws:alert_established</p> </li> </ul> <p>For more information, see <a href=\"https://docs.aws.amazon.com/network-firewall/latest/developerguide/suricata-rule-evaluation-order.html#suricata-strict-rule-evaluation-order.html\">Strict evaluation order</a> in the <i>Network Firewall Developer Guide</i>. </p>"}, "StatefulEngineOptions": {"shape": "StatefulEngineOptions", "documentation": "<p>Additional options governing how Network Firewall handles stateful rules. The stateful rule groups that you use in your policy must have stateful rule options settings that are compatible with these settings.</p>"}, "TLSInspectionConfigurationArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the TLS inspection configuration.</p>"}, "PolicyVariables": {"shape": "PolicyVariables", "documentation": "<p>Contains variables that you can use to override default Suricata settings in your firewall policy.</p>"}}, "documentation": "<p>The firewall policy defines the behavior of a firewall using a collection of stateless and stateful rule groups and other settings. You can use one firewall policy for multiple firewalls. </p> <p>This, along with <a>FirewallPolicyResponse</a>, define the policy. You can retrieve all objects for a firewall policy by calling <a>DescribeFirewallPolicy</a>.</p>"}, "FirewallPolicyMetadata": {"type": "structure", "members": {"Name": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the firewall policy. You can't change the name of a firewall policy after you create it.</p>"}, "Arn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall policy.</p>"}}, "documentation": "<p>High-level information about a firewall policy, returned by operations like create and describe. You can use the information provided in the metadata to retrieve and manage a firewall policy. You can retrieve all objects for a firewall policy by calling <a>DescribeFirewallPolicy</a>. </p>"}, "FirewallPolicyResponse": {"type": "structure", "required": ["FirewallPolicyName", "FirewallPolicyArn", "FirewallPolicyId"], "members": {"FirewallPolicyName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the firewall policy. You can't change the name of a firewall policy after you create it.</p>"}, "FirewallPolicyArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall policy.</p> <note> <p>If this response is for a create request that had <code>DryRun</code> set to <code>TRUE</code>, then this ARN is a placeholder that isn't attached to a valid resource.</p> </note>"}, "FirewallPolicyId": {"shape": "ResourceId", "documentation": "<p>The unique identifier for the firewall policy. </p>"}, "Description": {"shape": "Description", "documentation": "<p>A description of the firewall policy.</p>"}, "FirewallPolicyStatus": {"shape": "ResourceStatus", "documentation": "<p>The current status of the firewall policy. You can retrieve this for a firewall policy by calling <a>DescribeFirewallPolicy</a> and providing the firewall policy's name or ARN.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The key:value pairs to associate with the resource.</p>"}, "ConsumedStatelessRuleCapacity": {"shape": "RuleCapacity", "documentation": "<p>The number of capacity units currently consumed by the policy's stateless rules.</p>"}, "ConsumedStatefulRuleCapacity": {"shape": "RuleCapacity", "documentation": "<p>The number of capacity units currently consumed by the policy's stateful rules.</p>"}, "NumberOfAssociations": {"shape": "NumberOfAssociations", "documentation": "<p>The number of firewalls that are associated with this firewall policy.</p>"}, "EncryptionConfiguration": {"shape": "EncryptionConfiguration", "documentation": "<p>A complex type that contains the Amazon Web Services KMS encryption configuration settings for your firewall policy.</p>"}, "LastModifiedTime": {"shape": "LastUpdateTime", "documentation": "<p>The last time that the firewall policy was changed.</p>"}}, "documentation": "<p>The high-level properties of a firewall policy. This, along with the <a>FirewallPolicy</a>, define the policy. You can retrieve all objects for a firewall policy by calling <a>DescribeFirewallPolicy</a>. </p>"}, "FirewallStatus": {"type": "structure", "required": ["Status", "ConfigurationSyncStateSummary"], "members": {"Status": {"shape": "FirewallStatusValue", "documentation": "<p>The readiness of the configured firewall to handle network traffic across all of the Availability Zones where you have it configured. This setting is <code>READY</code> only when the <code>ConfigurationSyncStateSummary</code> value is <code>IN_SYNC</code> and the <code>Attachment</code> <code>Status</code> values for all of the configured subnets are <code>READY</code>. </p>"}, "ConfigurationSyncStateSummary": {"shape": "ConfigurationSyncState", "documentation": "<p>The configuration sync state for the firewall. This summarizes the <code>Config</code> settings in the <code>SyncStates</code> for this firewall status object. </p> <p>When you create a firewall or update its configuration, for example by adding a rule group to its firewall policy, Network Firewall distributes the configuration changes to all Availability Zones that have subnets defined for the firewall. This summary indicates whether the configuration changes have been applied everywhere. </p> <p>This status must be <code>IN_SYNC</code> for the firewall to be ready for use, but it doesn't indicate that the firewall is ready. The <code>Status</code> setting indicates firewall readiness. It's based on this setting and the readiness of the firewall endpoints to take traffic. </p>"}, "SyncStates": {"shape": "SyncStates", "documentation": "<p>Status for the subnets that you've configured in the firewall. This contains one array element per Availability Zone where you've configured a subnet in the firewall. </p> <p>These objects provide detailed information for the settings <code>ConfigurationSyncStateSummary</code> and <code>Status</code>. </p>"}, "CapacityUsageSummary": {"shape": "CapacityUsageSummary", "documentation": "<p>Describes the capacity usage of the resources contained in a firewall's reference sets. Network Firewall calculates the capacity usage by taking an aggregated count of all of the resources used by all of the reference sets in a firewall.</p>"}, "TransitGatewayAttachmentSyncState": {"shape": "TransitGatewayAttachmentSyncState", "documentation": "<p>The synchronization state of the transit gateway attachment. This indicates whether the firewall's transit gateway configuration is properly synchronized and operational. Use this to verify that your transit gateway configuration changes have been applied.</p>"}}, "documentation": "<p>Detailed information about the current status of a <a>Firewall</a>. You can retrieve this for a firewall by calling <a>DescribeFirewall</a> and providing the firewall name and ARN.</p> <p>The firewall status indicates a combined status. It indicates whether all subnets are up-to-date with the latest firewall configurations, which is based on the sync states config values, and also whether all subnets have their endpoints fully enabled, based on their sync states attachment values. </p>"}, "FirewallStatusValue": {"type": "string", "enum": ["PROVISIONING", "DELETING", "READY"]}, "Firewalls": {"type": "list", "member": {"shape": "FirewallMetadata"}}, "FirstAccessed": {"type": "timestamp"}, "Flags": {"type": "list", "member": {"shape": "TCPFlag"}}, "Flow": {"type": "structure", "members": {"SourceAddress": {"shape": "Address"}, "DestinationAddress": {"shape": "Address"}, "SourcePort": {"shape": "Port", "documentation": "<p>The source port to inspect for. You can specify an individual port, for example <code>1994</code> and you can specify a port range, for example <code>1990:1994</code>. To match with any port, specify <code>ANY</code>.</p>"}, "DestinationPort": {"shape": "Port", "documentation": "<p>The destination port to inspect for. You can specify an individual port, for example <code>1994</code> and you can specify a port range, for example <code>1990:1994</code>. To match with any port, specify <code>ANY</code>.</p>"}, "Protocol": {"shape": "ProtocolString", "documentation": "<p>The protocols to inspect for, specified using the assigned internet protocol number (IANA) for each protocol. If not specified, this matches with any protocol.</p>"}, "Age": {"shape": "Age", "documentation": "<p>Returned as info about age of the flows identified by the flow operation.</p>"}, "PacketCount": {"shape": "PacketCount", "documentation": "<p>Returns the total number of data packets received or transmitted in a flow.</p>"}, "ByteCount": {"shape": "ByteCount", "documentation": "<p>Returns the number of bytes received or transmitted in a specific flow.</p>"}}, "documentation": "<p>Any number of arrays, where each array is a single flow identified in the scope of the operation. If multiple flows were in the scope of the operation, multiple <code>Flows</code> arrays are returned.</p>"}, "FlowFilter": {"type": "structure", "members": {"SourceAddress": {"shape": "Address"}, "DestinationAddress": {"shape": "Address"}, "SourcePort": {"shape": "Port", "documentation": "<p>The source port to inspect for. You can specify an individual port, for example <code>1994</code> and you can specify a port range, for example <code>1990:1994</code>. To match with any port, specify <code>ANY</code>.</p>"}, "DestinationPort": {"shape": "Port", "documentation": "<p>The destination port to inspect for. You can specify an individual port, for example <code>1994</code> and you can specify a port range, for example <code>1990:1994</code>. To match with any port, specify <code>ANY</code>.</p>"}, "Protocols": {"shape": "ProtocolStrings", "documentation": "<p>The protocols to inspect for, specified using the assigned internet protocol number (IANA) for each protocol. If not specified, this matches with any protocol.</p>"}}, "documentation": "<p>Defines the scope a flow operation. You can use up to 20 filters to configure a single flow operation.</p>"}, "FlowFilters": {"type": "list", "member": {"shape": "FlowFilter"}}, "FlowOperation": {"type": "structure", "members": {"MinimumFlowAgeInSeconds": {"shape": "Age", "documentation": "<p>The reqested <code>FlowOperation</code> ignores flows with an age (in seconds) lower than <code>MinimumFlowAgeInSeconds</code>. You provide this for start commands.</p>"}, "FlowFilters": {"shape": "FlowFilters", "documentation": "<p>Defines the scope a flow operation. You can use up to 20 filters to configure a single flow operation.</p>"}}, "documentation": "<p>Contains information about a flow operation, such as related statuses, unique identifiers, and all filters defined in the operation.</p> <p>Flow operations let you manage the flows tracked in the flow table, also known as the firewall table.</p> <p>A flow is network traffic that is monitored by a firewall, either by stateful or stateless rules. For traffic to be considered part of a flow, it must share Destination, DestinationPort, Direction, Protocol, Source, and SourcePort. </p>"}, "FlowOperationId": {"type": "string", "max": 36, "min": 36, "pattern": "^([0-9a-f]{8})-([0-9a-f]{4}-){3}([0-9a-f]{12})$"}, "FlowOperationMetadata": {"type": "structure", "members": {"FlowOperationId": {"shape": "FlowOperationId", "documentation": "<p>A unique identifier for the flow operation. This ID is returned in the responses to start and list commands. You provide to describe commands.</p>"}, "FlowOperationType": {"shape": "FlowOperationType", "documentation": "<p>Defines the type of <code>FlowOperation</code>.</p>"}, "FlowRequestTimestamp": {"shape": "FlowRequestTimestamp", "documentation": "<p>A timestamp indicating when the Suricata engine identified flows impacted by an operation. </p>"}, "FlowOperationStatus": {"shape": "FlowOperationStatus", "documentation": "<p>Returns the status of the flow operation. This string is returned in the responses to start, list, and describe commands.</p> <p>If the status is <code>COMPLETED_WITH_ERRORS</code>, results may be returned with any number of <code>Flows</code> missing from the response. If the status is <code>FAILED</code>, <code>Flows</code> returned will be empty.</p>"}}, "documentation": "<p>An array of objects with metadata about the requested <code>FlowOperation</code>.</p>"}, "FlowOperationStatus": {"type": "string", "enum": ["COMPLETED", "IN_PROGRESS", "FAILED", "COMPLETED_WITH_ERRORS"]}, "FlowOperationType": {"type": "string", "enum": ["FLOW_FLUSH", "FLOW_CAPTURE"]}, "FlowOperations": {"type": "list", "member": {"shape": "FlowOperationMetadata"}}, "FlowRequestTimestamp": {"type": "timestamp"}, "FlowTimeouts": {"type": "structure", "members": {"TcpIdleTimeoutSeconds": {"shape": "TcpIdleTimeoutRangeBound", "documentation": "<p>The number of seconds that can pass without any TCP traffic sent through the firewall before the firewall determines that the connection is idle. After the idle timeout passes, data packets are dropped, however, the next TCP SYN packet is considered a new flow and is processed by the firewall. Clients or targets can use TCP keepalive packets to reset the idle timeout. </p> <p>You can define the <code>TcpIdleTimeoutSeconds</code> value to be between 60 and 6000 seconds. If no value is provided, it defaults to 350 seconds. </p>"}}, "documentation": "<p>Describes the amount of time that can pass without any traffic sent through the firewall before the firewall determines that the connection is idle and Network Firewall removes the flow entry from its flow table. Existing connections and flows are not impacted when you update this value. Only new connections after you update this value are impacted. </p>"}, "Flows": {"type": "list", "member": {"shape": "Flow"}}, "GeneratedRulesType": {"type": "string", "enum": ["ALLOWLIST", "DENYLIST"]}, "GetAnalysisReportResultsRequest": {"type": "structure", "required": ["AnalysisReportId"], "members": {"FirewallName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the firewall. You can't change the name of a firewall after you create it.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "AnalysisReportId": {"shape": "AnalysisReportId", "documentation": "<p>The unique ID of the query that ran when you requested an analysis report. </p>"}, "FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "NextToken": {"shape": "AnalysisReportNextToken", "documentation": "<p>When you request a list of objects with a <code>MaxResults</code> setting, if the number of objects that are still available for retrieval exceeds the maximum you requested, Network Firewall returns a <code>NextToken</code> value in the response. To retrieve the next batch of objects, use the token returned from the prior request in your next request.</p>"}, "MaxResults": {"shape": "PaginationMaxResults", "documentation": "<p>The maximum number of objects that you want Network Firewall to return for this request. If more objects are available, in the response, Network Firewall provides a <code>NextToken</code> value that you can use in a subsequent call to get the next batch of objects.</p>"}}}, "GetAnalysisReportResultsResponse": {"type": "structure", "members": {"Status": {"shape": "Status", "documentation": "<p>The status of the analysis report you specify. Statuses include <code>RUNNING</code>, <code>COMPLETED</code>, or <code>FAILED</code>.</p>"}, "StartTime": {"shape": "StartTime", "documentation": "<p> The date and time within the last 30 days from which to start retrieving analysis data, in UTC format (for example, <code>YYYY-MM-DDTHH:MM:SSZ</code>. </p>"}, "EndTime": {"shape": "EndTime", "documentation": "<p>The date and time, up to the current date, from which to stop retrieving analysis data, in UTC format (for example, <code>YYYY-MM-DDTHH:MM:SSZ</code>). </p>"}, "ReportTime": {"shape": "ReportTime", "documentation": "<p>The date and time the analysis report was ran. </p>"}, "AnalysisType": {"shape": "EnabledAnalysisType", "documentation": "<p>The type of traffic that will be used to generate a report. </p>"}, "NextToken": {"shape": "AnalysisReportNextToken", "documentation": "<p>When you request a list of objects with a <code>MaxResults</code> setting, if the number of objects that are still available for retrieval exceeds the maximum you requested, Network Firewall returns a <code>NextToken</code> value in the response. To retrieve the next batch of objects, use the token returned from the prior request in your next request.</p>"}, "AnalysisReportResults": {"shape": "AnalysisReportResults", "documentation": "<p>Retrieves the results of a traffic analysis report.</p>"}}}, "HashMapKey": {"type": "string", "max": 50, "min": 3, "pattern": "^[0-9A-Za-z.\\-_@\\/]+$"}, "HashMapValue": {"type": "string", "max": 1024, "min": 1, "pattern": "[\\s\\S]*$"}, "Header": {"type": "structure", "required": ["Protocol", "Source", "SourcePort", "Direction", "Destination", "DestinationPort"], "members": {"Protocol": {"shape": "StatefulRuleProtocol", "documentation": "<p>The protocol to inspect for. To specify all, you can use <code>IP</code>, because all traffic on Amazon Web Services and on the internet is IP.</p>"}, "Source": {"shape": "Source", "documentation": "<p>The source IP address or address range to inspect for, in CIDR notation. To match with any address, specify <code>ANY</code>. </p> <p>Specify an IP address or a block of IP addresses in Classless Inter-Domain Routing (CIDR) notation. Network Firewall supports all address ranges for IPv4 and IPv6. </p> <p>Examples: </p> <ul> <li> <p>To configure Network Firewall to inspect for the IP address **********, specify <code>**********/32</code>.</p> </li> <li> <p>To configure Network Firewall to inspect for IP addresses from ********* to ***********, specify <code>*********/24</code>.</p> </li> <li> <p>To configure Network Firewall to inspect for the IP address 1111:0000:0000:0000:0000:0000:0000:0111, specify <code>1111:0000:0000:0000:0000:0000:0000:0111/128</code>.</p> </li> <li> <p>To configure Network Firewall to inspect for IP addresses from 1111:0000:0000:0000:0000:0000:0000:0000 to 1111:0000:0000:0000:ffff:ffff:ffff:ffff, specify <code>1111:0000:0000:0000:0000:0000:0000:0000/64</code>.</p> </li> </ul> <p>For more information about CIDR notation, see the Wikipedia entry <a href=\"https://en.wikipedia.org/wiki/Classless_Inter-Domain_Routing\">Classless Inter-Domain Routing</a>.</p>"}, "SourcePort": {"shape": "Port", "documentation": "<p>The source port to inspect for. You can specify an individual port, for example <code>1994</code> and you can specify a port range, for example <code>1990:1994</code>. To match with any port, specify <code>ANY</code>.</p>"}, "Direction": {"shape": "StatefulRuleDirection", "documentation": "<p>The direction of traffic flow to inspect. If set to <code>ANY</code>, the inspection matches bidirectional traffic, both from the source to the destination and from the destination to the source. If set to <code>FORWARD</code>, the inspection only matches traffic going from the source to the destination. </p>"}, "Destination": {"shape": "Destination", "documentation": "<p>The destination IP address or address range to inspect for, in CIDR notation. To match with any address, specify <code>ANY</code>. </p> <p>Specify an IP address or a block of IP addresses in Classless Inter-Domain Routing (CIDR) notation. Network Firewall supports all address ranges for IPv4 and IPv6. </p> <p>Examples: </p> <ul> <li> <p>To configure Network Firewall to inspect for the IP address **********, specify <code>**********/32</code>.</p> </li> <li> <p>To configure Network Firewall to inspect for IP addresses from ********* to ***********, specify <code>*********/24</code>.</p> </li> <li> <p>To configure Network Firewall to inspect for the IP address 1111:0000:0000:0000:0000:0000:0000:0111, specify <code>1111:0000:0000:0000:0000:0000:0000:0111/128</code>.</p> </li> <li> <p>To configure Network Firewall to inspect for IP addresses from 1111:0000:0000:0000:0000:0000:0000:0000 to 1111:0000:0000:0000:ffff:ffff:ffff:ffff, specify <code>1111:0000:0000:0000:0000:0000:0000:0000/64</code>.</p> </li> </ul> <p>For more information about CIDR notation, see the Wikipedia entry <a href=\"https://en.wikipedia.org/wiki/Classless_Inter-Domain_Routing\">Classless Inter-Domain Routing</a>.</p>"}, "DestinationPort": {"shape": "Port", "documentation": "<p>The destination port to inspect for. You can specify an individual port, for example <code>1994</code> and you can specify a port range, for example <code>1990:1994</code>. To match with any port, specify <code>ANY</code>.</p>"}}, "documentation": "<p>The basic rule criteria for Network Firewall to use to inspect packet headers in stateful traffic flow inspection. Traffic flows that match the criteria are a match for the corresponding <a>StatefulRule</a>. </p>"}, "Hits": {"type": "structure", "members": {"Count": {"shape": "Count", "documentation": "<p>The number of attempts made to access a domain.</p>"}}, "documentation": "<p>Attempts made to a access domain.</p>"}, "IPAddressType": {"type": "string", "enum": ["DUALSTACK", "IPV4", "IPV6"]}, "IPSet": {"type": "structure", "required": ["Definition"], "members": {"Definition": {"shape": "VariableDefinitionList", "documentation": "<p>The list of IP addresses and address ranges, in CIDR notation. </p>"}}, "documentation": "<p>A list of IP addresses and address ranges, in CIDR notation. This is part of a <a>RuleVariables</a>. </p>"}, "IPSetArn": {"type": "string"}, "IPSetMetadata": {"type": "structure", "members": {"ResolvedCIDRCount": {"shape": "CIDRCount", "documentation": "<p>Describes the total number of CIDR blocks currently in use by the IP set references in a firewall. To determine how many CIDR blocks are available for you to use in a firewall, you can call <code>AvailableCIDRCount</code>.</p>"}}, "documentation": "<p>General information about the IP set.</p>"}, "IPSetMetadataMap": {"type": "map", "key": {"shape": "IPSetArn"}, "value": {"shape": "IPSetMetadata"}}, "IPSetReference": {"type": "structure", "members": {"ReferenceArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource that you are referencing in your rule group.</p>"}}, "documentation": "<p>Configures one or more IP set references for a Suricata-compatible rule group. This is used in <a>CreateRuleGroup</a> or <a>UpdateRuleGroup</a>. An IP set reference is a rule variable that references resources that you create and manage in another Amazon Web Services service, such as an Amazon VPC prefix list. Network Firewall IP set references enable you to dynamically update the contents of your rules. When you create, update, or delete the resource you are referencing in your rule, Network Firewall automatically updates the rule's content with the changes. For more information about IP set references in Network Firewall, see <a href=\"https://docs.aws.amazon.com/network-firewall/latest/developerguide/rule-groups-ip-set-references\">Using IP set references</a> in the <i>Network Firewall Developer Guide</i>.</p> <p> Network Firewall currently supports <a href=\"https://docs.aws.amazon.com/vpc/latest/userguide/managed-prefix-lists.html\">Amazon VPC prefix lists</a> and <a href=\"https://docs.aws.amazon.com/network-firewall/latest/developerguide/rule-groups-ip-set-references.html#rule-groups-referencing-resource-groups\">resource groups</a> in IP set references. </p>"}, "IPSetReferenceMap": {"type": "map", "key": {"shape": "IPSetReferenceName"}, "value": {"shape": "IPSetReference"}}, "IPSetReferenceName": {"type": "string", "max": 32, "min": 1, "pattern": "^[A-Za-z][A-Za-z0-9_]*$"}, "IPSets": {"type": "map", "key": {"shape": "RuleVariableName"}, "value": {"shape": "IPSet"}}, "IdentifiedType": {"type": "string", "enum": ["STATELESS_RULE_FORWARDING_ASYMMETRICALLY", "STATELESS_RULE_CONTAINS_TCP_FLAGS"]}, "InsufficientCapacityException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>Amazon Web Services doesn't currently have enough available capacity to fulfill your request. Try your request later. </p>", "exception": true, "fault": true}, "InternalServerError": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>Your request is valid, but Network Firewall couldn't perform the operation because of a system problem. Retry your request. </p>", "exception": true, "fault": true}, "InvalidOperationException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The operation failed because it's not valid. For example, you might have tried to delete a rule group or firewall policy that's in use.</p>", "exception": true}, "InvalidRequestException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The operation failed because of a problem with your request. Examples include: </p> <ul> <li> <p>You specified an unsupported parameter name or value.</p> </li> <li> <p>You tried to update a property with a value that isn't among the available types.</p> </li> <li> <p>Your request references an ARN that is malformed, or corresponds to a resource that isn't valid in the context of the request.</p> </li> </ul>", "exception": true}, "InvalidResourcePolicyException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The policy statement failed validation.</p>", "exception": true}, "InvalidTokenException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The token you provided is stale or isn't valid for the operation. </p>", "exception": true}, "KeyId": {"type": "string", "max": 2048, "min": 1, "pattern": ".*\\S.*"}, "Keyword": {"type": "string", "max": 128, "min": 1, "pattern": ".*"}, "LastAccessed": {"type": "timestamp"}, "LastUpdateTime": {"type": "timestamp"}, "LimitExceededException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>Unable to perform the operation because doing so would violate a limit setting. </p>", "exception": true}, "ListAnalysisReportsRequest": {"type": "structure", "members": {"FirewallName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the firewall. You can't change the name of a firewall after you create it.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "NextToken": {"shape": "PaginationToken", "documentation": "<p>When you request a list of objects with a <code>MaxResults</code> setting, if the number of objects that are still available for retrieval exceeds the maximum you requested, Network Firewall returns a <code>NextToken</code> value in the response. To retrieve the next batch of objects, use the token returned from the prior request in your next request.</p>"}, "MaxResults": {"shape": "PaginationMaxResults", "documentation": "<p>The maximum number of objects that you want Network Firewall to return for this request. If more objects are available, in the response, Network Firewall provides a <code>NextToken</code> value that you can use in a subsequent call to get the next batch of objects.</p>"}}}, "ListAnalysisReportsResponse": {"type": "structure", "members": {"AnalysisReports": {"shape": "AnalysisReports", "documentation": "<p>The <code>id</code> and <code>ReportTime</code> associated with a requested analysis report. Does not provide the status of the analysis report. </p>"}, "NextToken": {"shape": "PaginationToken", "documentation": "<p>When you request a list of objects with a <code>MaxResults</code> setting, if the number of objects that are still available for retrieval exceeds the maximum you requested, Network Firewall returns a <code>NextToken</code> value in the response. To retrieve the next batch of objects, use the token returned from the prior request in your next request.</p>"}}}, "ListFirewallPoliciesRequest": {"type": "structure", "members": {"NextToken": {"shape": "PaginationToken", "documentation": "<p>When you request a list of objects with a <code>MaxResults</code> setting, if the number of objects that are still available for retrieval exceeds the maximum you requested, Network Firewall returns a <code>NextToken</code> value in the response. To retrieve the next batch of objects, use the token returned from the prior request in your next request.</p>"}, "MaxResults": {"shape": "PaginationMaxResults", "documentation": "<p>The maximum number of objects that you want Network Firewall to return for this request. If more objects are available, in the response, Network Firewall provides a <code>NextToken</code> value that you can use in a subsequent call to get the next batch of objects.</p>"}}}, "ListFirewallPoliciesResponse": {"type": "structure", "members": {"NextToken": {"shape": "PaginationToken", "documentation": "<p>When you request a list of objects with a <code>MaxResults</code> setting, if the number of objects that are still available for retrieval exceeds the maximum you requested, Network Firewall returns a <code>NextToken</code> value in the response. To retrieve the next batch of objects, use the token returned from the prior request in your next request.</p>"}, "FirewallPolicies": {"shape": "FirewallPolicies", "documentation": "<p>The metadata for the firewall policies. Depending on your setting for max results and the number of firewall policies that you have, this might not be the full list. </p>"}}}, "ListFirewallsRequest": {"type": "structure", "members": {"NextToken": {"shape": "PaginationToken", "documentation": "<p>When you request a list of objects with a <code>MaxResults</code> setting, if the number of objects that are still available for retrieval exceeds the maximum you requested, Network Firewall returns a <code>NextToken</code> value in the response. To retrieve the next batch of objects, use the token returned from the prior request in your next request.</p>"}, "VpcIds": {"shape": "VpcIds", "documentation": "<p>The unique identifiers of the VPCs that you want Network Firewall to retrieve the firewalls for. Leave this blank to retrieve all firewalls that you have defined.</p>"}, "MaxResults": {"shape": "PaginationMaxResults", "documentation": "<p>The maximum number of objects that you want Network Firewall to return for this request. If more objects are available, in the response, Network Firewall provides a <code>NextToken</code> value that you can use in a subsequent call to get the next batch of objects.</p>"}}}, "ListFirewallsResponse": {"type": "structure", "members": {"NextToken": {"shape": "PaginationToken", "documentation": "<p>When you request a list of objects with a <code>MaxResults</code> setting, if the number of objects that are still available for retrieval exceeds the maximum you requested, Network Firewall returns a <code>NextToken</code> value in the response. To retrieve the next batch of objects, use the token returned from the prior request in your next request.</p>"}, "Firewalls": {"shape": "Firewalls", "documentation": "<p>The firewall metadata objects for the VPCs that you specified. Depending on your setting for max results and the number of firewalls you have, a single call might not be the full list. </p>"}}}, "ListFlowOperationResultsRequest": {"type": "structure", "required": ["FirewallArn", "FlowOperationId"], "members": {"FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p>"}, "FlowOperationId": {"shape": "FlowOperationId", "documentation": "<p>A unique identifier for the flow operation. This ID is returned in the responses to start and list commands. You provide to describe commands.</p>"}, "NextToken": {"shape": "PaginationToken", "documentation": "<p>When you request a list of objects with a <code>MaxResults</code> setting, if the number of objects that are still available for retrieval exceeds the maximum you requested, Network Firewall returns a <code>NextToken</code> value in the response. To retrieve the next batch of objects, use the token returned from the prior request in your next request.</p>"}, "MaxResults": {"shape": "PaginationMaxResults", "documentation": "<p>The maximum number of objects that you want Network Firewall to return for this request. If more objects are available, in the response, Network Firewall provides a <code>NextToken</code> value that you can use in a subsequent call to get the next batch of objects.</p>"}, "AvailabilityZone": {"shape": "AvailabilityZone", "documentation": "<p>The ID of the Availability Zone where the firewall is located. For example, <code>us-east-2a</code>.</p> <p>Defines the scope a flow operation. You can use up to 20 filters to configure a single flow operation.</p>"}, "VpcEndpointId": {"shape": "VpcEndpointId", "documentation": "<p>A unique identifier for the primary endpoint associated with a firewall.</p>"}, "VpcEndpointAssociationArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of a VPC endpoint association.</p>"}}}, "ListFlowOperationResultsResponse": {"type": "structure", "members": {"FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p>"}, "AvailabilityZone": {"shape": "AvailabilityZone", "documentation": "<p>The ID of the Availability Zone where the firewall is located. For example, <code>us-east-2a</code>.</p> <p>Defines the scope a flow operation. You can use up to 20 filters to configure a single flow operation.</p>"}, "VpcEndpointAssociationArn": {"shape": "ResourceArn", "documentation": "<p/>"}, "VpcEndpointId": {"shape": "VpcEndpointId", "documentation": "<p/>"}, "FlowOperationId": {"shape": "FlowOperationId", "documentation": "<p>A unique identifier for the flow operation. This ID is returned in the responses to start and list commands. You provide to describe commands.</p>"}, "FlowOperationStatus": {"shape": "FlowOperationStatus", "documentation": "<p>Returns the status of the flow operation. This string is returned in the responses to start, list, and describe commands.</p> <p>If the status is <code>COMPLETED_WITH_ERRORS</code>, results may be returned with any number of <code>Flows</code> missing from the response. If the status is <code>FAILED</code>, <code>Flows</code> returned will be empty.</p>"}, "StatusMessage": {"shape": "StatusReason", "documentation": "<p>If the asynchronous operation fails, Network Firewall populates this with the reason for the error or failure. Options include <code>Flow operation error</code> and <code>Flow timeout</code>.</p>"}, "FlowRequestTimestamp": {"shape": "FlowRequestTimestamp", "documentation": "<p>A timestamp indicating when the Suricata engine identified flows impacted by an operation. </p>"}, "Flows": {"shape": "Flows", "documentation": "<p>Any number of arrays, where each array is a single flow identified in the scope of the operation. If multiple flows were in the scope of the operation, multiple <code>Flows</code> arrays are returned.</p>"}, "NextToken": {"shape": "PaginationToken", "documentation": "<p>When you request a list of objects with a <code>MaxResults</code> setting, if the number of objects that are still available for retrieval exceeds the maximum you requested, Network Firewall returns a <code>NextToken</code> value in the response. To retrieve the next batch of objects, use the token returned from the prior request in your next request.</p>"}}}, "ListFlowOperationsRequest": {"type": "structure", "required": ["FirewallArn"], "members": {"FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p>"}, "AvailabilityZone": {"shape": "AvailabilityZone", "documentation": "<p>The ID of the Availability Zone where the firewall is located. For example, <code>us-east-2a</code>.</p> <p>Defines the scope a flow operation. You can use up to 20 filters to configure a single flow operation.</p>"}, "VpcEndpointAssociationArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of a VPC endpoint association.</p>"}, "VpcEndpointId": {"shape": "VpcEndpointId", "documentation": "<p>A unique identifier for the primary endpoint associated with a firewall.</p>"}, "FlowOperationType": {"shape": "FlowOperationType", "documentation": "<p>An optional string that defines whether any or all operation types are returned.</p>"}, "NextToken": {"shape": "PaginationToken", "documentation": "<p>When you request a list of objects with a <code>MaxResults</code> setting, if the number of objects that are still available for retrieval exceeds the maximum you requested, Network Firewall returns a <code>NextToken</code> value in the response. To retrieve the next batch of objects, use the token returned from the prior request in your next request.</p>"}, "MaxResults": {"shape": "PaginationMaxResults", "documentation": "<p>The maximum number of objects that you want Network Firewall to return for this request. If more objects are available, in the response, Network Firewall provides a <code>NextToken</code> value that you can use in a subsequent call to get the next batch of objects.</p>"}}}, "ListFlowOperationsResponse": {"type": "structure", "members": {"FlowOperations": {"shape": "FlowOperations", "documentation": "<p>Flow operations let you manage the flows tracked in the flow table, also known as the firewall table.</p> <p>A flow is network traffic that is monitored by a firewall, either by stateful or stateless rules. For traffic to be considered part of a flow, it must share Destination, DestinationPort, Direction, Protocol, Source, and SourcePort. </p>"}, "NextToken": {"shape": "PaginationToken", "documentation": "<p>When you request a list of objects with a <code>MaxResults</code> setting, if the number of objects that are still available for retrieval exceeds the maximum you requested, Network Firewall returns a <code>NextToken</code> value in the response. To retrieve the next batch of objects, use the token returned from the prior request in your next request.</p>"}}}, "ListRuleGroupsRequest": {"type": "structure", "members": {"NextToken": {"shape": "PaginationToken", "documentation": "<p>When you request a list of objects with a <code>MaxResults</code> setting, if the number of objects that are still available for retrieval exceeds the maximum you requested, Network Firewall returns a <code>NextToken</code> value in the response. To retrieve the next batch of objects, use the token returned from the prior request in your next request.</p>"}, "MaxResults": {"shape": "PaginationMaxResults", "documentation": "<p>The maximum number of objects that you want Network Firewall to return for this request. If more objects are available, in the response, Network Firewall provides a <code>NextToken</code> value that you can use in a subsequent call to get the next batch of objects.</p>"}, "Scope": {"shape": "ResourceManagedStatus", "documentation": "<p>The scope of the request. The default setting of <code>ACCOUNT</code> or a setting of <code>NULL</code> returns all of the rule groups in your account. A setting of <code>MANAGED</code> returns all available managed rule groups.</p>"}, "ManagedType": {"shape": "ResourceManagedType", "documentation": "<p>Indicates the general category of the Amazon Web Services managed rule group.</p>"}, "Type": {"shape": "RuleGroupType", "documentation": "<p>Indicates whether the rule group is stateless or stateful. If the rule group is stateless, it contains stateless rules. If it is stateful, it contains stateful rules.</p>"}}}, "ListRuleGroupsResponse": {"type": "structure", "members": {"NextToken": {"shape": "PaginationToken", "documentation": "<p>When you request a list of objects with a <code>MaxResults</code> setting, if the number of objects that are still available for retrieval exceeds the maximum you requested, Network Firewall returns a <code>NextToken</code> value in the response. To retrieve the next batch of objects, use the token returned from the prior request in your next request.</p>"}, "RuleGroups": {"shape": "RuleGroups", "documentation": "<p>The rule group metadata objects that you've defined. Depending on your setting for max results and the number of rule groups, this might not be the full list. </p>"}}}, "ListTLSInspectionConfigurationsRequest": {"type": "structure", "members": {"NextToken": {"shape": "PaginationToken", "documentation": "<p>When you request a list of objects with a <code>MaxResults</code> setting, if the number of objects that are still available for retrieval exceeds the maximum you requested, Network Firewall returns a <code>NextToken</code> value in the response. To retrieve the next batch of objects, use the token returned from the prior request in your next request.</p>"}, "MaxResults": {"shape": "PaginationMaxResults", "documentation": "<p>The maximum number of objects that you want Network Firewall to return for this request. If more objects are available, in the response, Network Firewall provides a <code>NextToken</code> value that you can use in a subsequent call to get the next batch of objects.</p>"}}}, "ListTLSInspectionConfigurationsResponse": {"type": "structure", "members": {"NextToken": {"shape": "PaginationToken", "documentation": "<p>When you request a list of objects with a <code>MaxResults</code> setting, if the number of objects that are still available for retrieval exceeds the maximum you requested, Network Firewall returns a <code>NextToken</code> value in the response. To retrieve the next batch of objects, use the token returned from the prior request in your next request.</p>"}, "TLSInspectionConfigurations": {"shape": "TLSInspectionConfigurations", "documentation": "<p>The TLS inspection configuration metadata objects that you've defined. Depending on your setting for max results and the number of TLS inspection configurations, this might not be the full list.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["ResourceArn"], "members": {"NextToken": {"shape": "PaginationToken", "documentation": "<p>When you request a list of objects with a <code>MaxResults</code> setting, if the number of objects that are still available for retrieval exceeds the maximum you requested, Network Firewall returns a <code>NextToken</code> value in the response. To retrieve the next batch of objects, use the token returned from the prior request in your next request.</p>"}, "MaxResults": {"shape": "TagsPaginationMaxResults", "documentation": "<p>The maximum number of objects that you want Network Firewall to return for this request. If more objects are available, in the response, Network Firewall provides a <code>NextToken</code> value that you can use in a subsequent call to get the next batch of objects.</p>"}, "ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource.</p>"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"NextToken": {"shape": "PaginationToken", "documentation": "<p>When you request a list of objects with a <code>MaxResults</code> setting, if the number of objects that are still available for retrieval exceeds the maximum you requested, Network Firewall returns a <code>NextToken</code> value in the response. To retrieve the next batch of objects, use the token returned from the prior request in your next request.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The tags that are associated with the resource. </p>"}}}, "ListVpcEndpointAssociationsRequest": {"type": "structure", "members": {"NextToken": {"shape": "PaginationToken", "documentation": "<p>When you request a list of objects with a <code>MaxResults</code> setting, if the number of objects that are still available for retrieval exceeds the maximum you requested, Network Firewall returns a <code>NextToken</code> value in the response. To retrieve the next batch of objects, use the token returned from the prior request in your next request.</p>"}, "MaxResults": {"shape": "PaginationMaxResults", "documentation": "<p>The maximum number of objects that you want Network Firewall to return for this request. If more objects are available, in the response, Network Firewall provides a <code>NextToken</code> value that you can use in a subsequent call to get the next batch of objects.</p>"}, "FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p> <p>If you don't specify this, Network Firewall retrieves all VPC endpoint associations that you have defined.</p>"}}}, "ListVpcEndpointAssociationsResponse": {"type": "structure", "members": {"NextToken": {"shape": "PaginationToken", "documentation": "<p>When you request a list of objects with a <code>MaxResults</code> setting, if the number of objects that are still available for retrieval exceeds the maximum you requested, Network Firewall returns a <code>NextToken</code> value in the response. To retrieve the next batch of objects, use the token returned from the prior request in your next request.</p>"}, "VpcEndpointAssociations": {"shape": "VpcEndpointAssociations", "documentation": "<p>The VPC endpoint assocation metadata objects for the firewall that you specified. If you didn't specify a firewall, this is all VPC endpoint associations that you have defined. </p> <p>Depending on your setting for max results and the number of firewalls you have, a single call might not be the full list. </p>"}}}, "LogDestinationConfig": {"type": "structure", "required": ["LogType", "LogDestinationType", "LogDestination"], "members": {"LogType": {"shape": "LogType", "documentation": "<p>The type of log to record. You can record the following types of logs from your Network Firewall stateful engine.</p> <ul> <li> <p> <code>ALERT</code> - Logs for traffic that matches your stateful rules and that have an action that sends an alert. A stateful rule sends alerts for the rule actions DROP, ALERT, and REJECT. For more information, see <a>StatefulRule</a>.</p> </li> <li> <p> <code>FLOW</code> - Standard network traffic flow logs. The stateful rules engine records flow logs for all network traffic that it receives. Each flow log record captures the network flow for a specific standard stateless rule group.</p> </li> <li> <p> <code>TLS</code> - Logs for events that are related to TLS inspection. For more information, see <a href=\"https://docs.aws.amazon.com/network-firewall/latest/developerguide/tls-inspection-configurations.html\">Inspecting SSL/TLS traffic with TLS inspection configurations</a> in the <i>Network Firewall Developer Guide</i>.</p> </li> </ul>"}, "LogDestinationType": {"shape": "LogDestinationType", "documentation": "<p>The type of storage destination to send these logs to. You can send logs to an Amazon S3 bucket, a CloudWatch log group, or a Firehose delivery stream.</p>"}, "LogDestination": {"shape": "LogDestinationMap", "documentation": "<p>The named location for the logs, provided in a key:value mapping that is specific to the chosen destination type. </p> <ul> <li> <p>For an Amazon S3 bucket, provide the name of the bucket, with key <code>bucketName</code>, and optionally provide a prefix, with key <code>prefix</code>. </p> <p>The following example specifies an Amazon S3 bucket named <code>DOC-EXAMPLE-BUCKET</code> and the prefix <code>alerts</code>: </p> <p> <code>\"LogDestination\": { \"bucketName\": \"DOC-EXAMPLE-BUCKET\", \"prefix\": \"alerts\" }</code> </p> </li> <li> <p>For a CloudWatch log group, provide the name of the CloudWatch log group, with key <code>logGroup</code>. The following example specifies a log group named <code>alert-log-group</code>: </p> <p> <code>\"LogDestination\": { \"logGroup\": \"alert-log-group\" }</code> </p> </li> <li> <p>For a Firehose delivery stream, provide the name of the delivery stream, with key <code>deliveryStream</code>. The following example specifies a delivery stream named <code>alert-delivery-stream</code>: </p> <p> <code>\"LogDestination\": { \"deliveryStream\": \"alert-delivery-stream\" }</code> </p> </li> </ul>"}}, "documentation": "<p>Defines where Network Firewall sends logs for the firewall for one log type. This is used in <a>LoggingConfiguration</a>. You can send each type of log to an Amazon S3 bucket, a CloudWatch log group, or a Firehose delivery stream.</p> <p>Network Firewall generates logs for stateful rule groups. You can save alert, flow, and TLS log types. </p>"}, "LogDestinationConfigs": {"type": "list", "member": {"shape": "LogDestinationConfig"}}, "LogDestinationMap": {"type": "map", "key": {"shape": "HashMapKey"}, "value": {"shape": "HashMapValue"}}, "LogDestinationPermissionException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>Unable to send logs to a configured logging destination. </p>", "exception": true}, "LogDestinationType": {"type": "string", "enum": ["S3", "CloudWatchLogs", "KinesisDataFirehose"], "max": 30, "min": 2, "pattern": "[0-9A-Za-z]+"}, "LogType": {"type": "string", "enum": ["ALERT", "FLOW", "TLS"]}, "LoggingConfiguration": {"type": "structure", "required": ["LogDestinationConfigs"], "members": {"LogDestinationConfigs": {"shape": "LogDestinationConfigs", "documentation": "<p>Defines the logging destinations for the logs for a firewall. Network Firewall generates logs for stateful rule groups. </p>"}}, "documentation": "<p>Defines how Network Firewall performs logging for a <a>Firewall</a>. </p>"}, "MatchAttributes": {"type": "structure", "members": {"Sources": {"shape": "Addresses", "documentation": "<p>The source IP addresses and address ranges to inspect for, in CIDR notation. If not specified, this matches with any source address. </p>"}, "Destinations": {"shape": "Addresses", "documentation": "<p>The destination IP addresses and address ranges to inspect for, in CIDR notation. If not specified, this matches with any destination address. </p>"}, "SourcePorts": {"shape": "PortRanges", "documentation": "<p>The source port to inspect for. You can specify an individual port, for example <code>1994</code> and you can specify a port range, for example <code>1990:1994</code>. To match with any port, specify <code>ANY</code>.</p> <p> If not specified, this matches with any source port.</p> <p>This setting is only used for protocols 6 (TCP) and 17 (UDP).</p>"}, "DestinationPorts": {"shape": "PortRanges", "documentation": "<p>The destination port to inspect for. You can specify an individual port, for example <code>1994</code> and you can specify a port range, for example <code>1990:1994</code>. To match with any port, specify <code>ANY</code>.</p> <p>This setting is only used for protocols 6 (TCP) and 17 (UDP). </p>"}, "Protocols": {"shape": "ProtocolNumbers", "documentation": "<p>The protocols to inspect for, specified using the assigned internet protocol number (IANA) for each protocol. If not specified, this matches with any protocol.</p>"}, "TCPFlags": {"shape": "TCPFlags", "documentation": "<p>The TCP flags and masks to inspect for. If not specified, this matches with any settings. This setting is only used for protocol 6 (TCP).</p>"}}, "documentation": "<p>Criteria for Network Firewall to use to inspect an individual packet in stateless rule inspection. Each match attributes set can include one or more items such as IP address, CIDR range, port number, protocol, and TCP flags. </p>"}, "NumberOfAssociations": {"type": "integer"}, "OverrideAction": {"type": "string", "enum": ["DROP_TO_ALERT"]}, "PacketCount": {"type": "integer"}, "PaginationMaxResults": {"type": "integer", "max": 100, "min": 1}, "PaginationToken": {"type": "string", "max": 4096, "min": 1, "pattern": "[0-9A-Za-z:\\/+=]+$"}, "PerObjectStatus": {"type": "structure", "members": {"SyncStatus": {"shape": "PerObjectSyncStatus", "documentation": "<p>Indicates whether this object is in sync with the version indicated in the update token.</p>"}, "UpdateToken": {"shape": "UpdateToken", "documentation": "<p>The current version of the object that is either in sync or pending synchronization. </p>"}}, "documentation": "<p>Provides configuration status for a single policy or rule group that is used for a firewall endpoint. Network Firewall provides each endpoint with the rules that are configured in the firewall policy. Each time you add a subnet or modify the associated firewall policy, Network Firewall synchronizes the rules in the endpoint, so it can properly filter network traffic. This is part of a <a>SyncState</a> for a firewall.</p>"}, "PerObjectSyncStatus": {"type": "string", "enum": ["PENDING", "IN_SYNC", "CAPACITY_CONSTRAINED"]}, "PolicyString": {"type": "string", "max": 395000, "min": 1, "pattern": ".*\\S.*"}, "PolicyVariables": {"type": "structure", "members": {"RuleVariables": {"shape": "IPSets", "documentation": "<p>The IPv4 or IPv6 addresses in CIDR notation to use for the Suricata <code>HOME_NET</code> variable. If your firewall uses an inspection VPC, you might want to override the <code>HOME_NET</code> variable with the CIDRs of your home networks. If you don't override <code>HOME_NET</code> with your own CIDRs, Network Firewall by default uses the CIDR of your inspection VPC.</p>"}}, "documentation": "<p>Contains variables that you can use to override default Suricata settings in your firewall policy.</p>"}, "Port": {"type": "string", "max": 1024, "min": 1, "pattern": "^.*$"}, "PortRange": {"type": "structure", "required": ["FromPort", "ToPort"], "members": {"FromPort": {"shape": "PortRangeBound", "documentation": "<p>The lower limit of the port range. This must be less than or equal to the <code>ToPort</code> specification. </p>"}, "ToPort": {"shape": "PortRangeBound", "documentation": "<p>The upper limit of the port range. This must be greater than or equal to the <code>FromPort</code> specification. </p>"}}, "documentation": "<p>A single port range specification. This is used for source and destination port ranges in the stateless rule <a>MatchAttributes</a>, <code>SourcePorts</code>, and <code>DestinationPorts</code> settings. </p>"}, "PortRangeBound": {"type": "integer", "max": 65535, "min": 0}, "PortRanges": {"type": "list", "member": {"shape": "PortRange"}}, "PortSet": {"type": "structure", "members": {"Definition": {"shape": "VariableDefinitionList", "documentation": "<p>The set of port ranges. </p>"}}, "documentation": "<p>A set of port ranges for use in the rules in a rule group. </p>"}, "PortSets": {"type": "map", "key": {"shape": "RuleVariableName"}, "value": {"shape": "PortSet"}}, "Priority": {"type": "integer", "max": 65535, "min": 1}, "ProtocolNumber": {"type": "integer", "max": 255, "min": 0}, "ProtocolNumbers": {"type": "list", "member": {"shape": "ProtocolNumber"}}, "ProtocolString": {"type": "string", "max": 12, "min": 1, "pattern": "^.*$"}, "ProtocolStrings": {"type": "list", "member": {"shape": "ProtocolString"}}, "PublishMetricAction": {"type": "structure", "required": ["Dimensions"], "members": {"Dimensions": {"shape": "Dimensions", "documentation": "<p/>"}}, "documentation": "<p>Stateless inspection criteria that publishes the specified metrics to Amazon CloudWatch for the matching packet. This setting defines a CloudWatch dimension value to be published.</p>"}, "PutResourcePolicyRequest": {"type": "structure", "required": ["ResourceArn", "Policy"], "members": {"ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the account that you want to share your Network Firewall resources with.</p>"}, "Policy": {"shape": "PolicyString", "documentation": "<p>The IAM policy statement that lists the accounts that you want to share your Network Firewall resources with and the operations that you want the accounts to be able to perform. </p> <p>For a rule group resource, you can specify the following operations in the Actions section of the statement:</p> <ul> <li> <p>network-firewall:CreateFirewallPolicy</p> </li> <li> <p>network-firewall:UpdateFirewallPolicy</p> </li> <li> <p>network-firewall:ListRuleGroups</p> </li> </ul> <p>For a firewall policy resource, you can specify the following operations in the Actions section of the statement:</p> <ul> <li> <p>network-firewall:AssociateFirewallPolicy</p> </li> <li> <p>network-firewall:ListFirewallPolicies</p> </li> </ul> <p>For a firewall resource, you can specify the following operations in the Actions section of the statement:</p> <ul> <li> <p>network-firewall:CreateVpcEndpointAssociation</p> </li> <li> <p>network-firewall:DescribeFirewallMetadata</p> </li> <li> <p>network-firewall:ListFirewalls</p> </li> </ul> <p>In the Resource section of the statement, you specify the ARNs for the Network Firewall resources that you want to share with the account that you specified in <code>Arn</code>.</p>"}}}, "PutResourcePolicyResponse": {"type": "structure", "members": {}}, "ReferenceSets": {"type": "structure", "members": {"IPSetReferences": {"shape": "IPSetReferenceMap", "documentation": "<p>The list of IP set references.</p>"}}, "documentation": "<p>Contains a set of IP set references.</p>"}, "RejectNetworkFirewallTransitGatewayAttachmentRequest": {"type": "structure", "required": ["TransitGatewayAttachmentId"], "members": {"TransitGatewayAttachmentId": {"shape": "TransitGatewayAttachmentId", "documentation": "<p>Required. The unique identifier of the transit gateway attachment to reject. This ID is returned in the response when creating a transit gateway-attached firewall.</p>"}}}, "RejectNetworkFirewallTransitGatewayAttachmentResponse": {"type": "structure", "required": ["TransitGatewayAttachmentId", "TransitGatewayAttachmentStatus"], "members": {"TransitGatewayAttachmentId": {"shape": "TransitGatewayAttachmentId", "documentation": "<p>The unique identifier of the transit gateway attachment that was rejected.</p>"}, "TransitGatewayAttachmentStatus": {"shape": "TransitGatewayAttachmentStatus", "documentation": "<p>The current status of the transit gateway attachment. Valid values are:</p> <ul> <li> <p> <code>CREATING</code> - The attachment is being created</p> </li> <li> <p> <code>DELETING</code> - The attachment is being deleted</p> </li> <li> <p> <code>DELETED</code> - The attachment has been deleted</p> </li> <li> <p> <code>FAILED</code> - The attachment creation has failed and cannot be recovered</p> </li> <li> <p> <code>ERROR</code> - The attachment is in an error state that might be recoverable</p> </li> <li> <p> <code>READY</code> - The attachment is active and processing traffic</p> </li> <li> <p> <code>PENDING_ACCEPTANCE</code> - The attachment is waiting to be accepted</p> </li> <li> <p> <code>REJECTING</code> - The attachment is in the process of being rejected</p> </li> <li> <p> <code>REJECTED</code> - The attachment has been rejected</p> </li> </ul> <p>For information about troubleshooting endpoint failures, see <a href=\"https://docs.aws.amazon.com/network-firewall/latest/developerguide/firewall-troubleshooting-endpoint-failures.html\">Troubleshooting firewall endpoint failures</a> in the <i>Network Firewall Developer Guide</i>.</p>"}}}, "ReportTime": {"type": "timestamp"}, "ResourceArn": {"type": "string", "max": 256, "min": 1, "pattern": "^arn:aws.*"}, "ResourceId": {"type": "string", "max": 36, "min": 36, "pattern": "^([0-9a-f]{8})-([0-9a-f]{4}-){3}([0-9a-f]{12})$"}, "ResourceManagedStatus": {"type": "string", "enum": ["MANAGED", "ACCOUNT"]}, "ResourceManagedType": {"type": "string", "enum": ["AWS_MANAGED_THREAT_SIGNATURES", "AWS_MANAGED_DOMAIN_LISTS", "ACTIVE_THREAT_DEFENSE"]}, "ResourceName": {"type": "string", "max": 128, "min": 1, "pattern": "^[a-zA-Z0-9-]+$"}, "ResourceNotFoundException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>Unable to locate a resource using the parameters that you provided.</p>", "exception": true}, "ResourceOwnerCheckException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>Unable to change the resource because your account doesn't own it. </p>", "exception": true}, "ResourceStatus": {"type": "string", "enum": ["ACTIVE", "DELETING", "ERROR"]}, "RevocationCheckAction": {"type": "string", "enum": ["PASS", "DROP", "REJECT"]}, "RuleCapacity": {"type": "integer"}, "RuleDefinition": {"type": "structure", "required": ["MatchAttributes", "Actions"], "members": {"MatchAttributes": {"shape": "MatchAttributes", "documentation": "<p>Criteria for Network Firewall to use to inspect an individual packet in stateless rule inspection. Each match attributes set can include one or more items such as IP address, CIDR range, port number, protocol, and TCP flags. </p>"}, "Actions": {"shape": "StatelessActions", "documentation": "<p>The actions to take on a packet that matches one of the stateless rule definition's match attributes. You must specify a standard action and you can add custom actions. </p> <note> <p>Network Firewall only forwards a packet for stateful rule inspection if you specify <code>aws:forward_to_sfe</code> for a rule that the packet matches, or if the packet doesn't match any stateless rule and you specify <code>aws:forward_to_sfe</code> for the <code>StatelessDefaultActions</code> setting for the <a>FirewallPolicy</a>.</p> </note> <p>For every rule, you must specify exactly one of the following standard actions. </p> <ul> <li> <p> <b>aws:pass</b> - Discontinues all inspection of the packet and permits it to go to its intended destination.</p> </li> <li> <p> <b>aws:drop</b> - Discontinues all inspection of the packet and blocks it from going to its intended destination.</p> </li> <li> <p> <b>aws:forward_to_sfe</b> - Discontinues stateless inspection of the packet and forwards it to the stateful rule engine for inspection. </p> </li> </ul> <p>Additionally, you can specify a custom action. To do this, you define a custom action by name and type, then provide the name you've assigned to the action in this <code>Actions</code> setting. For information about the options, see <a>CustomAction</a>. </p> <p>To provide more than one action in this setting, separate the settings with a comma. For example, if you have a custom <code>PublishMetrics</code> action that you've named <code>MyMetricsAction</code>, then you could specify the standard action <code>aws:pass</code> and the custom action with <code>[“aws:pass”, “MyMetricsAction”]</code>. </p>"}}, "documentation": "<p>The inspection criteria and action for a single stateless rule. Network Firewall inspects each packet for the specified matching criteria. When a packet matches the criteria, Network Firewall performs the rule's actions on the packet.</p>"}, "RuleGroup": {"type": "structure", "required": ["RulesSource"], "members": {"RuleVariables": {"shape": "RuleVariables", "documentation": "<p>Settings that are available for use in the rules in the rule group. You can only use these for stateful rule groups. </p>"}, "ReferenceSets": {"shape": "ReferenceSets", "documentation": "<p>The list of a rule group's reference sets.</p>"}, "RulesSource": {"shape": "RulesSource", "documentation": "<p>The stateful rules or stateless rules for the rule group. </p>"}, "StatefulRuleOptions": {"shape": "StatefulRuleOptions", "documentation": "<p>Additional options governing how Network Firewall handles stateful rules. The policies where you use your stateful rule group must have stateful rule options settings that are compatible with these settings. Some limitations apply; for more information, see <a href=\"https://docs.aws.amazon.com/network-firewall/latest/developerguide/suricata-limitations-caveats.html\">Strict evaluation order</a> in the <i>Network Firewall Developer Guide</i>.</p>"}}, "documentation": "<p>The object that defines the rules in a rule group. This, along with <a>RuleGroupResponse</a>, define the rule group. You can retrieve all objects for a rule group by calling <a>DescribeRuleGroup</a>. </p> <p>Network Firewall uses a rule group to inspect and control network traffic. You define stateless rule groups to inspect individual packets and you define stateful rule groups to inspect packets in the context of their traffic flow. </p> <p>To use a rule group, you include it by reference in an Network Firewall firewall policy, then you use the policy in a firewall. You can reference a rule group from more than one firewall policy, and you can use a firewall policy in more than one firewall. </p>"}, "RuleGroupMetadata": {"type": "structure", "members": {"Name": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the rule group. You can't change the name of a rule group after you create it.</p>"}, "Arn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the rule group.</p>"}}, "documentation": "<p>High-level information about a rule group, returned by <a>ListRuleGroups</a>. You can use the information provided in the metadata to retrieve and manage a rule group.</p>"}, "RuleGroupResponse": {"type": "structure", "required": ["RuleGroupArn", "RuleGroupName", "RuleGroupId"], "members": {"RuleGroupArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the rule group.</p> <note> <p>If this response is for a create request that had <code>DryRun</code> set to <code>TRUE</code>, then this ARN is a placeholder that isn't attached to a valid resource.</p> </note>"}, "RuleGroupName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the rule group. You can't change the name of a rule group after you create it.</p>"}, "RuleGroupId": {"shape": "ResourceId", "documentation": "<p>The unique identifier for the rule group. </p>"}, "Description": {"shape": "Description", "documentation": "<p>A description of the rule group. </p>"}, "Type": {"shape": "RuleGroupType", "documentation": "<p>Indicates whether the rule group is stateless or stateful. If the rule group is stateless, it contains stateless rules. If it is stateful, it contains stateful rules. </p>"}, "Capacity": {"shape": "RuleCapacity", "documentation": "<p>The maximum operating resources that this rule group can use. Rule group capacity is fixed at creation. When you update a rule group, you are limited to this capacity. When you reference a rule group from a firewall policy, Network Firewall reserves this capacity for the rule group. </p> <p>You can retrieve the capacity that would be required for a rule group before you create the rule group by calling <a>CreateRuleGroup</a> with <code>DryRun</code> set to <code>TRUE</code>. </p>"}, "RuleGroupStatus": {"shape": "ResourceStatus", "documentation": "<p>Detailed information about the current status of a rule group. </p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The key:value pairs to associate with the resource.</p>"}, "ConsumedCapacity": {"shape": "RuleCapacity", "documentation": "<p>The number of capacity units currently consumed by the rule group rules. </p>"}, "NumberOfAssociations": {"shape": "NumberOfAssociations", "documentation": "<p>The number of firewall policies that use this rule group.</p>"}, "EncryptionConfiguration": {"shape": "EncryptionConfiguration", "documentation": "<p>A complex type that contains the Amazon Web Services KMS encryption configuration settings for your rule group.</p>"}, "SourceMetadata": {"shape": "SourceMetadata", "documentation": "<p>A complex type that contains metadata about the rule group that your own rule group is copied from. You can use the metadata to track the version updates made to the originating rule group.</p>"}, "SnsTopic": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the Amazon Simple Notification Service SNS topic that's used to record changes to the managed rule group. You can subscribe to the SNS topic to receive notifications when the managed rule group is modified, such as for new versions and for version expiration. For more information, see the <a href=\"https://docs.aws.amazon.com/sns/latest/dg/welcome.html\">Amazon Simple Notification Service Developer Guide.</a>.</p>"}, "LastModifiedTime": {"shape": "LastUpdateTime", "documentation": "<p>The last time that the rule group was changed.</p>"}, "AnalysisResults": {"shape": "AnalysisResultList", "documentation": "<p>The list of analysis results for <code>AnalyzeRuleGroup</code>. If you set <code>AnalyzeRuleGroup</code> to <code>TRUE</code> in <a>CreateRuleGroup</a>, <a>UpdateRuleGroup</a>, or <a>DescribeRuleGroup</a>, Network Firewall analyzes the rule group and identifies the rules that might adversely effect your firewall's functionality. For example, if Network Firewall detects a rule that's routing traffic asymmetrically, which impacts the service's ability to properly process traffic, the service includes the rule in the list of analysis results.</p>"}, "SummaryConfiguration": {"shape": "SummaryConfiguration", "documentation": "<p>A complex type containing the currently selected rule option fields that will be displayed for rule summarization returned by <a>DescribeRuleGroupSummary</a>.</p> <ul> <li> <p>The <code>RuleOptions</code> specified in <a>SummaryConfiguration</a> </p> </li> <li> <p>Rule metadata organization preferences</p> </li> </ul>"}}, "documentation": "<p>The high-level properties of a rule group. This, along with the <a>RuleGroup</a>, define the rule group. You can retrieve all objects for a rule group by calling <a>DescribeRuleGroup</a>. </p>"}, "RuleGroupType": {"type": "string", "enum": ["STATELESS", "STATEFUL"]}, "RuleGroups": {"type": "list", "member": {"shape": "RuleGroupMetadata"}}, "RuleIdList": {"type": "list", "member": {"shape": "CollectionMember_String"}}, "RuleOption": {"type": "structure", "required": ["Keyword"], "members": {"Keyword": {"shape": "Keyword", "documentation": "<p>The keyword for the Suricata compatible rule option. You must include a <code>sid</code> (signature ID), and can optionally include other keywords. For information about Suricata compatible keywords, see <a href=\"https://suricata.readthedocs.io/en/suricata-7.0.3/rules/intro.html#rule-options\">Rule options</a> in the Suricata documentation.</p>"}, "Settings": {"shape": "Settings", "documentation": "<p>The settings of the Suricata compatible rule option. Rule options have zero or more setting values, and the number of possible and required settings depends on the <code>Keyword</code>. For more information about the settings for specific options, see <a href=\"https://suricata.readthedocs.io/en/suricata-7.0.3/rules/intro.html#rule-options\">Rule options</a>.</p>"}}, "documentation": "<p>Additional settings for a stateful rule. This is part of the <a>StatefulRule</a> configuration.</p>"}, "RuleOptions": {"type": "list", "member": {"shape": "RuleOption"}}, "RuleOrder": {"type": "string", "enum": ["DEFAULT_ACTION_ORDER", "STRICT_ORDER"]}, "RuleSummaries": {"type": "list", "member": {"shape": "RuleSummary"}}, "RuleSummary": {"type": "structure", "members": {"SID": {"shape": "CollectionMember_String", "documentation": "<p>The unique identifier (Signature ID) of the Suricata rule.</p>"}, "Msg": {"shape": "CollectionMember_String", "documentation": "<p>The contents taken from the rule's msg field.</p>"}, "Metadata": {"shape": "CollectionMember_String", "documentation": "<p>The contents of the rule's metadata.</p>"}}, "documentation": "<p>A complex type containing details about a Suricata rule. Contains:</p> <ul> <li> <p> <code>SID</code> </p> </li> <li> <p> <code>Msg</code> </p> </li> <li> <p> <code>Metadata</code> </p> </li> </ul> <p>Summaries are available for rule groups you manage and for active threat defense Amazon Web Services managed rule groups.</p>"}, "RuleTargets": {"type": "list", "member": {"shape": "CollectionMember_String"}}, "RuleVariableName": {"type": "string", "max": 32, "min": 1, "pattern": "^[A-Za-z][A-Za-z0-9_]*$"}, "RuleVariables": {"type": "structure", "members": {"IPSets": {"shape": "IPSets", "documentation": "<p>A list of IP addresses and address ranges, in CIDR notation. </p>"}, "PortSets": {"shape": "PortSets", "documentation": "<p>A list of port ranges. </p>"}}, "documentation": "<p>Settings that are available for use in the rules in the <a>RuleGroup</a> where this is defined. See <a>CreateRuleGroup</a> or <a>UpdateRuleGroup</a> for usage.</p>"}, "RulesSource": {"type": "structure", "members": {"RulesString": {"shape": "RulesString", "documentation": "<p>Stateful inspection criteria, provided in Suricata compatible rules. Suricata is an open-source threat detection framework that includes a standard rule-based language for network traffic inspection.</p> <p>These rules contain the inspection criteria and the action to take for traffic that matches the criteria, so this type of rule group doesn't have a separate action setting.</p> <note> <p>You can't use the <code>priority</code> keyword if the <code>RuleOrder</code> option in <a>StatefulRuleOptions</a> is set to <code>STRICT_ORDER</code>.</p> </note>"}, "RulesSourceList": {"shape": "RulesSourceList", "documentation": "<p>Stateful inspection criteria for a domain list rule group. </p>"}, "StatefulRules": {"shape": "StatefulRules", "documentation": "<p>An array of individual stateful rules inspection criteria to be used together in a stateful rule group. Use this option to specify simple Suricata rules with protocol, source and destination, ports, direction, and rule options. For information about the Suricata <code>Rules</code> format, see <a href=\"https://suricata.readthedocs.io/en/suricata-7.0.3/rules/intro.html\">Rules Format</a>. </p>"}, "StatelessRulesAndCustomActions": {"shape": "StatelessRulesAndCustomActions", "documentation": "<p>Stateless inspection criteria to be used in a stateless rule group. </p>"}}, "documentation": "<p>The stateless or stateful rules definitions for use in a single rule group. Each rule group requires a single <code>RulesSource</code>. You can use an instance of this for either stateless rules or stateful rules. </p>"}, "RulesSourceList": {"type": "structure", "required": ["Targets", "TargetTypes", "GeneratedRulesType"], "members": {"Targets": {"shape": "RuleTargets", "documentation": "<p>The domains that you want to inspect for in your traffic flows. Valid domain specifications are the following:</p> <ul> <li> <p>Explicit names. For example, <code>abc.example.com</code> matches only the domain <code>abc.example.com</code>.</p> </li> <li> <p>Names that use a domain wildcard, which you indicate with an initial '<code>.</code>'. For example,<code>.example.com</code> matches <code>example.com</code> and matches all subdomains of <code>example.com</code>, such as <code>abc.example.com</code> and <code>www.example.com</code>. </p> </li> </ul>"}, "TargetTypes": {"shape": "TargetTypes", "documentation": "<p>The protocols you want to inspect. Specify <code>TLS_SNI</code> for <code>HTTPS</code>. Specify <code>HTTP_HOST</code> for <code>HTTP</code>. You can specify either or both. </p>"}, "GeneratedRulesType": {"shape": "GeneratedRulesType", "documentation": "<p>Whether you want to allow or deny access to the domains in your target list.</p>"}}, "documentation": "<p>Stateful inspection criteria for a domain list rule group. </p> <p>For HTTPS traffic, domain filtering is SNI-based. It uses the server name indicator extension of the TLS handshake.</p> <p>By default, Network Firewall domain list inspection only includes traffic coming from the VPC where you deploy the firewall. To inspect traffic from IP addresses outside of the deployment VPC, you set the <code>HOME_NET</code> rule variable to include the CIDR range of the deployment VPC plus the other CIDR ranges. For more information, see <a>RuleVariables</a> in this guide and <a href=\"https://docs.aws.amazon.com/network-firewall/latest/developerguide/stateful-rule-groups-domain-names.html\">Stateful domain list rule groups in Network Firewall</a> in the <i>Network Firewall Developer Guide</i>.</p>"}, "RulesString": {"type": "string", "max": 2000000, "min": 0}, "ServerCertificate": {"type": "structure", "members": {"ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the Certificate Manager SSL/TLS server certificate that's used for inbound SSL/TLS inspection.</p>"}}, "documentation": "<p>Any Certificate Manager (ACM) Secure Sockets Layer/Transport Layer Security (SSL/TLS) server certificate that's associated with a <a>ServerCertificateConfiguration</a>. Used in a <a>TLSInspectionConfiguration</a> for inspection of inbound traffic to your firewall. You must request or import a SSL/TLS certificate into ACM for each domain Network Firewall needs to decrypt and inspect. Network Firewall uses the SSL/TLS certificates to decrypt specified inbound SSL/TLS traffic going to your firewall. For information about working with certificates in Certificate Manager, see <a href=\"https://docs.aws.amazon.com/acm/latest/userguide/gs-acm-request-public.html\">Request a public certificate </a> or <a href=\"https://docs.aws.amazon.com/acm/latest/userguide/import-certificate.html\">Importing certificates</a> in the <i>Certificate Manager User Guide</i>.</p>"}, "ServerCertificateConfiguration": {"type": "structure", "members": {"ServerCertificates": {"shape": "ServerCertificates", "documentation": "<p>The list of server certificates to use for inbound SSL/TLS inspection.</p>"}, "Scopes": {"shape": "ServerCertificateScopes", "documentation": "<p>A list of scopes.</p>"}, "CertificateAuthorityArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the imported certificate authority (CA) certificate within Certificate Manager (ACM) to use for outbound SSL/TLS inspection.</p> <p>The following limitations apply:</p> <ul> <li> <p>You can use CA certificates that you imported into ACM, but you can't generate CA certificates with ACM.</p> </li> <li> <p>You can't use certificates issued by Private Certificate Authority.</p> </li> </ul> <p>For more information about configuring certificates for outbound inspection, see <a href=\"https://docs.aws.amazon.com/network-firewall/latest/developerguide/tls-inspection-certificate-requirements.html\">Using SSL/TLS certificates with TLS inspection configurations</a> in the <i>Network Firewall Developer Guide</i>. </p> <p>For information about working with certificates in ACM, see <a href=\"https://docs.aws.amazon.com/acm/latest/userguide/import-certificate.html\">Importing certificates</a> in the <i>Certificate Manager User Guide</i>.</p>"}, "CheckCertificateRevocationStatus": {"shape": "CheckCertificateRevocationStatusActions", "documentation": "<p>When enabled, Network Firewall checks if the server certificate presented by the server in the SSL/TLS connection has a revoked or unkown status. If the certificate has an unknown or revoked status, you must specify the actions that Network Firewall takes on outbound traffic. To check the certificate revocation status, you must also specify a <code>CertificateAuthorityArn</code> in <a>ServerCertificateConfiguration</a>.</p>"}}, "documentation": "<p>Configures the Certificate Manager certificates and scope that Network Firewall uses to decrypt and re-encrypt traffic using a <a>TLSInspectionConfiguration</a>. You can configure <code>ServerCertificates</code> for inbound SSL/TLS inspection, a <code>CertificateAuthorityArn</code> for outbound SSL/TLS inspection, or both. For information about working with certificates for TLS inspection, see <a href=\"https://docs.aws.amazon.com/network-firewall/latest/developerguide/tls-inspection-certificate-requirements.html\"> Using SSL/TLS server certficiates with TLS inspection configurations</a> in the <i>Network Firewall Developer Guide</i>.</p> <note> <p>If a server certificate that's associated with your <a>TLSInspectionConfiguration</a> is revoked, deleted, or expired it can result in client-side TLS errors.</p> </note>"}, "ServerCertificateConfigurations": {"type": "list", "member": {"shape": "ServerCertificateConfiguration"}}, "ServerCertificateScope": {"type": "structure", "members": {"Sources": {"shape": "Addresses", "documentation": "<p>The source IP addresses and address ranges to decrypt for inspection, in CIDR notation. If not specified, this matches with any source address.</p>"}, "Destinations": {"shape": "Addresses", "documentation": "<p>The destination IP addresses and address ranges to decrypt for inspection, in CIDR notation. If not specified, this matches with any destination address.</p>"}, "SourcePorts": {"shape": "PortRanges", "documentation": "<p>The source ports to decrypt for inspection, in Transmission Control Protocol (TCP) format. If not specified, this matches with any source port.</p> <p>You can specify individual ports, for example <code>1994</code>, and you can specify port ranges, such as <code>1990:1994</code>.</p>"}, "DestinationPorts": {"shape": "PortRanges", "documentation": "<p>The destination ports to decrypt for inspection, in Transmission Control Protocol (TCP) format. If not specified, this matches with any destination port.</p> <p>You can specify individual ports, for example <code>1994</code>, and you can specify port ranges, such as <code>1990:1994</code>.</p>"}, "Protocols": {"shape": "ProtocolNumbers", "documentation": "<p>The protocols to inspect for, specified using the assigned internet protocol number (IANA) for each protocol. If not specified, this matches with any protocol.</p> <p>Network Firewall currently supports only TCP.</p>"}}, "documentation": "<p>Settings that define the Secure Sockets Layer/Transport Layer Security (SSL/TLS) traffic that Network Firewall should decrypt for inspection by the stateful rule engine.</p>"}, "ServerCertificateScopes": {"type": "list", "member": {"shape": "ServerCertificateScope"}}, "ServerCertificates": {"type": "list", "member": {"shape": "ServerCertificate"}}, "Setting": {"type": "string", "max": 8192, "min": 1, "pattern": ".*"}, "Settings": {"type": "list", "member": {"shape": "Setting"}}, "Source": {"type": "string", "max": 1024, "min": 1, "pattern": "^.*$"}, "SourceMetadata": {"type": "structure", "members": {"SourceArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the rule group that your own rule group is copied from.</p>"}, "SourceUpdateToken": {"shape": "UpdateToken", "documentation": "<p>The update token of the Amazon Web Services managed rule group that your own rule group is copied from. To determine the update token for the managed rule group, call <a href=\"https://docs.aws.amazon.com/network-firewall/latest/APIReference/API_DescribeRuleGroup.html#networkfirewall-DescribeRuleGroup-response-UpdateToken\">DescribeRuleGroup</a>.</p>"}}, "documentation": "<p>High-level information about the managed rule group that your own rule group is copied from. You can use the the metadata to track version updates made to the originating rule group. You can retrieve all objects for a rule group by calling <a href=\"https://docs.aws.amazon.com/network-firewall/latest/APIReference/API_DescribeRuleGroup.html\">DescribeRuleGroup</a>.</p>"}, "StartAnalysisReportRequest": {"type": "structure", "required": ["AnalysisType"], "members": {"FirewallName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the firewall. You can't change the name of a firewall after you create it.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "AnalysisType": {"shape": "EnabledAnalysisType", "documentation": "<p>The type of traffic that will be used to generate a report. </p>"}}}, "StartAnalysisReportResponse": {"type": "structure", "required": ["AnalysisReportId"], "members": {"AnalysisReportId": {"shape": "AnalysisReportId", "documentation": "<p>The unique ID of the query that ran when you requested an analysis report. </p>"}}}, "StartFlowCaptureRequest": {"type": "structure", "required": ["FirewallArn", "FlowFilters"], "members": {"FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p>"}, "AvailabilityZone": {"shape": "AvailabilityZone", "documentation": "<p>The ID of the Availability Zone where the firewall is located. For example, <code>us-east-2a</code>.</p> <p>Defines the scope a flow operation. You can use up to 20 filters to configure a single flow operation.</p>"}, "VpcEndpointAssociationArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of a VPC endpoint association.</p>"}, "VpcEndpointId": {"shape": "VpcEndpointId", "documentation": "<p>A unique identifier for the primary endpoint associated with a firewall.</p>"}, "MinimumFlowAgeInSeconds": {"shape": "Age", "documentation": "<p>The reqested <code>FlowOperation</code> ignores flows with an age (in seconds) lower than <code>MinimumFlowAgeInSeconds</code>. You provide this for start commands.</p> <note> <p>We recommend setting this value to at least 1 minute (60 seconds) to reduce chance of capturing flows that are not yet established.</p> </note>"}, "FlowFilters": {"shape": "FlowFilters", "documentation": "<p>Defines the scope a flow operation. You can use up to 20 filters to configure a single flow operation.</p>"}}}, "StartFlowCaptureResponse": {"type": "structure", "members": {"FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p>"}, "FlowOperationId": {"shape": "FlowOperationId", "documentation": "<p>A unique identifier for the flow operation. This ID is returned in the responses to start and list commands. You provide to describe commands.</p>"}, "FlowOperationStatus": {"shape": "FlowOperationStatus", "documentation": "<p>Returns the status of the flow operation. This string is returned in the responses to start, list, and describe commands.</p> <p>If the status is <code>COMPLETED_WITH_ERRORS</code>, results may be returned with any number of <code>Flows</code> missing from the response. If the status is <code>FAILED</code>, <code>Flows</code> returned will be empty.</p>"}}}, "StartFlowFlushRequest": {"type": "structure", "required": ["FirewallArn", "FlowFilters"], "members": {"FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p>"}, "AvailabilityZone": {"shape": "AvailabilityZone", "documentation": "<p>The ID of the Availability Zone where the firewall is located. For example, <code>us-east-2a</code>.</p> <p>Defines the scope a flow operation. You can use up to 20 filters to configure a single flow operation.</p>"}, "VpcEndpointAssociationArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of a VPC endpoint association.</p>"}, "VpcEndpointId": {"shape": "VpcEndpointId", "documentation": "<p>A unique identifier for the primary endpoint associated with a firewall.</p>"}, "MinimumFlowAgeInSeconds": {"shape": "Age", "documentation": "<p>The reqested <code>FlowOperation</code> ignores flows with an age (in seconds) lower than <code>MinimumFlowAgeInSeconds</code>. You provide this for start commands.</p>"}, "FlowFilters": {"shape": "FlowFilters", "documentation": "<p>Defines the scope a flow operation. You can use up to 20 filters to configure a single flow operation.</p>"}}}, "StartFlowFlushResponse": {"type": "structure", "members": {"FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p>"}, "FlowOperationId": {"shape": "FlowOperationId", "documentation": "<p>A unique identifier for the flow operation. This ID is returned in the responses to start and list commands. You provide to describe commands.</p>"}, "FlowOperationStatus": {"shape": "FlowOperationStatus", "documentation": "<p>Returns the status of the flow operation. This string is returned in the responses to start, list, and describe commands.</p> <p>If the status is <code>COMPLETED_WITH_ERRORS</code>, results may be returned with any number of <code>Flows</code> missing from the response. If the status is <code>FAILED</code>, <code>Flows</code> returned will be empty.</p>"}}}, "StartTime": {"type": "timestamp"}, "StatefulAction": {"type": "string", "enum": ["PASS", "DROP", "ALERT", "REJECT"]}, "StatefulActions": {"type": "list", "member": {"shape": "CollectionMember_String"}}, "StatefulEngineOptions": {"type": "structure", "members": {"RuleOrder": {"shape": "RuleOrder", "documentation": "<p>Indicates how to manage the order of stateful rule evaluation for the policy. <code>STRICT_ORDER</code> is the recommended option, but <code>DEFAULT_ACTION_ORDER</code> is the default option. With <code>STRICT_ORDER</code>, provide your rules in the order that you want them to be evaluated. You can then choose one or more default actions for packets that don't match any rules. Choose <code>STRICT_ORDER</code> to have the stateful rules engine determine the evaluation order of your rules. The default action for this rule order is <code>PASS</code>, followed by <code>DROP</code>, <code>REJECT</code>, and <code>ALERT</code> actions. Stateful rules are provided to the rule engine as Suricata compatible strings, and Suricata evaluates them based on your settings. For more information, see <a href=\"https://docs.aws.amazon.com/network-firewall/latest/developerguide/suricata-rule-evaluation-order.html\">Evaluation order for stateful rules</a> in the <i>Network Firewall Developer Guide</i>. </p>"}, "StreamExceptionPolicy": {"shape": "StreamExceptionPolicy", "documentation": "<p>Configures how Network Firewall processes traffic when a network connection breaks midstream. Network connections can break due to disruptions in external networks or within the firewall itself.</p> <ul> <li> <p> <code>DROP</code> - Network Firewall fails closed and drops all subsequent traffic going to the firewall. This is the default behavior.</p> </li> <li> <p> <code>CONTINUE</code> - Network Firewall continues to apply rules to the subsequent traffic without context from traffic before the break. This impacts the behavior of rules that depend on this context. For example, if you have a stateful rule to <code>drop http</code> traffic, Network Firewall won't match the traffic for this rule because the service won't have the context from session initialization defining the application layer protocol as HTTP. However, this behavior is rule dependent—a TCP-layer rule using a <code>flow:stateless</code> rule would still match, as would the <code>aws:drop_strict</code> default action.</p> </li> <li> <p> <code>REJECT</code> - Network Firewall fails closed and drops all subsequent traffic going to the firewall. Network Firewall also sends a TCP reject packet back to your client so that the client can immediately establish a new session. Network Firewall will have context about the new session and will apply rules to the subsequent traffic.</p> </li> </ul>"}, "FlowTimeouts": {"shape": "FlowTimeouts", "documentation": "<p>Configures the amount of time that can pass without any traffic sent through the firewall before the firewall determines that the connection is idle. </p>"}}, "documentation": "<p>Configuration settings for the handling of the stateful rule groups in a firewall policy. </p>"}, "StatefulRule": {"type": "structure", "required": ["Action", "Header", "RuleOptions"], "members": {"Action": {"shape": "StatefulAction", "documentation": "<p>Defines what Network Firewall should do with the packets in a traffic flow when the flow matches the stateful rule criteria. For all actions, Network Firewall performs the specified action and discontinues stateful inspection of the traffic flow. </p> <p>The actions for a stateful rule are defined as follows: </p> <ul> <li> <p> <b>PASS</b> - Permits the packets to go to the intended destination.</p> </li> <li> <p> <b>DROP</b> - Blocks the packets from going to the intended destination and sends an alert log message, if alert logging is configured in the <a>Firewall</a> <a>LoggingConfiguration</a>. </p> </li> <li> <p> <b>ALERT</b> - Sends an alert log message, if alert logging is configured in the <a>Firewall</a> <a>LoggingConfiguration</a>. </p> <p>You can use this action to test a rule that you intend to use to drop traffic. You can enable the rule with <code>ALERT</code> action, verify in the logs that the rule is filtering as you want, then change the action to <code>DROP</code>.</p> </li> <li> <p> <b>REJECT</b> - Drops traffic that matches the conditions of the stateful rule, and sends a TCP reset packet back to sender of the packet. A TCP reset packet is a packet with no payload and an RST bit contained in the TCP header flags. REJECT is available only for TCP traffic. This option doesn't support FTP or IMAP protocols.</p> </li> </ul>"}, "Header": {"shape": "Header", "documentation": "<p>The stateful inspection criteria for this rule, used to inspect traffic flows. </p>"}, "RuleOptions": {"shape": "RuleOptions", "documentation": "<p>Additional options for the rule. These are the Suricata <code>RuleOptions</code> settings.</p>"}}, "documentation": "<p>A single Suricata rules specification, for use in a stateful rule group. Use this option to specify a simple Suricata rule with protocol, source and destination, ports, direction, and rule options. For information about the Suricata <code>Rules</code> format, see <a href=\"https://suricata.readthedocs.io/en/suricata-7.0.3/rules/intro.html\">Rules Format</a>. </p>"}, "StatefulRuleDirection": {"type": "string", "enum": ["FORWARD", "ANY"]}, "StatefulRuleGroupOverride": {"type": "structure", "members": {"Action": {"shape": "OverrideAction", "documentation": "<p>The action that changes the rule group from <code>DROP</code> to <code>ALERT</code>. This only applies to managed rule groups.</p>"}}, "documentation": "<p>The setting that allows the policy owner to change the behavior of the rule group within a policy. </p>"}, "StatefulRuleGroupReference": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the stateful rule group.</p>"}, "Priority": {"shape": "Priority", "documentation": "<p>An integer setting that indicates the order in which to run the stateful rule groups in a single <a>FirewallPolicy</a>. This setting only applies to firewall policies that specify the <code>STRICT_ORDER</code> rule order in the stateful engine options settings.</p> <p>Network Firewall evalutes each stateful rule group against a packet starting with the group that has the lowest priority setting. You must ensure that the priority settings are unique within each policy.</p> <p>You can change the priority settings of your rule groups at any time. To make it easier to insert rule groups later, number them so there's a wide range in between, for example use 100, 200, and so on. </p>", "box": true}, "Override": {"shape": "StatefulRuleGroupOverride", "documentation": "<p>The action that allows the policy owner to override the behavior of the rule group within a policy.</p>"}, "DeepThreatInspection": {"shape": "DeepThreatInspection", "documentation": "<p>Network Firewall plans to augment the active threat defense managed rule group with an additional deep threat inspection capability. When this capability is released, Amazon Web Services will analyze service logs of network traffic processed by these rule groups to identify threat indicators across customers. Amazon Web Services will use these threat indicators to improve the active threat defense managed rule groups and protect the security of Amazon Web Services customers and services.</p> <note> <p>Customers can opt-out of deep threat inspection at any time through the Network Firewall console or API. When customers opt out, Network Firewall will not use the network traffic processed by those customers' active threat defense rule groups for rule group improvement.</p> </note>"}}, "documentation": "<p>Identifier for a single stateful rule group, used in a firewall policy to refer to a rule group. </p>"}, "StatefulRuleGroupReferences": {"type": "list", "member": {"shape": "StatefulRuleGroupReference"}}, "StatefulRuleOptions": {"type": "structure", "members": {"RuleOrder": {"shape": "RuleOrder", "documentation": "<p>Indicates how to manage the order of the rule evaluation for the rule group. <code>DEFAULT_ACTION_ORDER</code> is the default behavior. Stateful rules are provided to the rule engine as Suricata compatible strings, and Suricata evaluates them based on certain settings. For more information, see <a href=\"https://docs.aws.amazon.com/network-firewall/latest/developerguide/suricata-rule-evaluation-order.html\">Evaluation order for stateful rules</a> in the <i>Network Firewall Developer Guide</i>. </p>"}}, "documentation": "<p>Additional options governing how Network Firewall handles the rule group. You can only use these for stateful rule groups.</p>"}, "StatefulRuleProtocol": {"type": "string", "enum": ["IP", "TCP", "UDP", "ICMP", "HTTP", "FTP", "TLS", "SMB", "DNS", "DCERPC", "SSH", "SMTP", "IMAP", "MSN", "KRB5", "IKEV2", "TFTP", "NTP", "DHCP", "HTTP2", "QUIC"]}, "StatefulRules": {"type": "list", "member": {"shape": "StatefulRule"}}, "StatelessActions": {"type": "list", "member": {"shape": "CollectionMember_String"}}, "StatelessRule": {"type": "structure", "required": ["RuleDefinition", "Priority"], "members": {"RuleDefinition": {"shape": "RuleDefinition", "documentation": "<p>Defines the stateless 5-tuple packet inspection criteria and the action to take on a packet that matches the criteria. </p>"}, "Priority": {"shape": "Priority", "documentation": "<p>Indicates the order in which to run this rule relative to all of the rules that are defined for a stateless rule group. Network Firewall evaluates the rules in a rule group starting with the lowest priority setting. You must ensure that the priority settings are unique for the rule group. </p> <p>Each stateless rule group uses exactly one <code>StatelessRulesAndCustomActions</code> object, and each <code>StatelessRulesAndCustomActions</code> contains exactly one <code>StatelessRules</code> object. To ensure unique priority settings for your rule groups, set unique priorities for the stateless rules that you define inside any single <code>StatelessRules</code> object.</p> <p>You can change the priority settings of your rules at any time. To make it easier to insert rules later, number them so there's a wide range in between, for example use 100, 200, and so on. </p>"}}, "documentation": "<p>A single stateless rule. This is used in <a>StatelessRulesAndCustomActions</a>.</p>"}, "StatelessRuleGroupReference": {"type": "structure", "required": ["ResourceArn", "Priority"], "members": {"ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the stateless rule group.</p>"}, "Priority": {"shape": "Priority", "documentation": "<p>An integer setting that indicates the order in which to run the stateless rule groups in a single <a>FirewallPolicy</a>. Network Firewall applies each stateless rule group to a packet starting with the group that has the lowest priority setting. You must ensure that the priority settings are unique within each policy.</p>"}}, "documentation": "<p>Identifier for a single stateless rule group, used in a firewall policy to refer to the rule group. </p>"}, "StatelessRuleGroupReferences": {"type": "list", "member": {"shape": "StatelessRuleGroupReference"}}, "StatelessRules": {"type": "list", "member": {"shape": "StatelessRule"}}, "StatelessRulesAndCustomActions": {"type": "structure", "required": ["StatelessRules"], "members": {"StatelessRules": {"shape": "StatelessRules", "documentation": "<p>Defines the set of stateless rules for use in a stateless rule group. </p>"}, "CustomActions": {"shape": "CustomActions", "documentation": "<p>Defines an array of individual custom action definitions that are available for use by the stateless rules in this <code>StatelessRulesAndCustomActions</code> specification. You name each custom action that you define, and then you can use it by name in your <a>StatelessRule</a> <a>RuleDefinition</a> <code>Actions</code> specification.</p>"}}, "documentation": "<p>Stateless inspection criteria. Each stateless rule group uses exactly one of these data types to define its stateless rules. </p>"}, "Status": {"type": "string"}, "StatusMessage": {"type": "string"}, "StatusReason": {"type": "string", "max": 256, "min": 1, "pattern": "^[a-zA-Z0-9- ]+$"}, "StreamExceptionPolicy": {"type": "string", "enum": ["DROP", "CONTINUE", "REJECT"]}, "SubnetMapping": {"type": "structure", "required": ["SubnetId"], "members": {"SubnetId": {"shape": "CollectionMember_String", "documentation": "<p>The unique identifier for the subnet. </p>"}, "IPAddressType": {"shape": "IPAddressType", "documentation": "<p>The subnet's IP address type. You can't change the IP address type after you create the subnet.</p>"}}, "documentation": "<p>The ID for a subnet that's used in an association with a firewall. This is used in <a>CreateFirewall</a>, <a>AssociateSubnets</a>, and <a>CreateVpcEndpointAssociation</a>. Network Firewall creates an instance of the associated firewall in each subnet that you specify, to filter traffic in the subnet's Availability Zone.</p>"}, "SubnetMappings": {"type": "list", "member": {"shape": "SubnetMapping"}}, "Summary": {"type": "structure", "members": {"RuleSummaries": {"shape": "RuleSummaries", "documentation": "<p>An array of <a>RuleSummary</a> objects containing individual rule details that had been configured by the rulegroup's SummaryConfiguration.</p>"}}, "documentation": "<p>A complex type containing summaries of security protections provided by a rule group.</p> <p>Network Firewall extracts this information from selected fields in the rule group's Suricata rules, based on your <a>SummaryConfiguration</a> settings.</p>"}, "SummaryConfiguration": {"type": "structure", "members": {"RuleOptions": {"shape": "SummaryRuleOptions", "documentation": "<p>Specifies the selected rule options returned by <a>DescribeRuleGroupSummary</a>.</p>"}}, "documentation": "<p>A complex type that specifies which Suricata rule metadata fields to use when displaying threat information. Contains:</p> <ul> <li> <p> <code>RuleOptions</code> - The Suricata rule options fields to extract and display</p> </li> </ul> <p>These settings affect how threat information appears in both the console and API responses. Summaries are available for rule groups you manage and for active threat defense Amazon Web Services managed rule groups.</p>"}, "SummaryRuleOption": {"type": "string", "enum": ["SID", "MSG", "METADATA"]}, "SummaryRuleOptions": {"type": "list", "member": {"shape": "SummaryRuleOption"}}, "SupportedAvailabilityZones": {"type": "map", "key": {"shape": "AvailabilityZone"}, "value": {"shape": "AvailabilityZoneMetadata"}}, "SyncState": {"type": "structure", "members": {"Attachment": {"shape": "Attachment", "documentation": "<p>The configuration and status for a single firewall subnet. For each configured subnet, Network Firewall creates the attachment by instantiating the firewall endpoint in the subnet so that it's ready to take traffic. </p>"}, "Config": {"shape": "SyncStateConfig", "documentation": "<p>The configuration status of the firewall endpoint in a single VPC subnet. Network Firewall provides each endpoint with the rules that are configured in the firewall policy. Each time you add a subnet or modify the associated firewall policy, Network Firewall synchronizes the rules in the endpoint, so it can properly filter network traffic. </p>"}}, "documentation": "<p>The status of the firewall endpoint and firewall policy configuration for a single VPC subnet. This is part of the <a>FirewallStatus</a>. </p> <p>For each VPC subnet that you associate with a firewall, Network Firewall does the following: </p> <ul> <li> <p>Instantiates a firewall endpoint in the subnet, ready to take traffic.</p> </li> <li> <p>Configures the endpoint with the current firewall policy settings, to provide the filtering behavior for the endpoint.</p> </li> </ul> <p>When you update a firewall, for example to add a subnet association or change a rule group in the firewall policy, the affected sync states reflect out-of-sync or not ready status until the changes are complete. </p>"}, "SyncStateConfig": {"type": "map", "key": {"shape": "ResourceName"}, "value": {"shape": "PerObjectStatus"}}, "SyncStates": {"type": "map", "key": {"shape": "AvailabilityZone"}, "value": {"shape": "SyncState"}}, "TCPFlag": {"type": "string", "enum": ["FIN", "SYN", "RST", "PSH", "ACK", "URG", "ECE", "CWR"]}, "TCPFlagField": {"type": "structure", "required": ["Flags"], "members": {"Flags": {"shape": "Flags", "documentation": "<p>Used in conjunction with the <code>Masks</code> setting to define the flags that must be set and flags that must not be set in order for the packet to match. This setting can only specify values that are also specified in the <code>Masks</code> setting.</p> <p>For the flags that are specified in the masks setting, the following must be true for the packet to match: </p> <ul> <li> <p>The ones that are set in this flags setting must be set in the packet. </p> </li> <li> <p>The ones that are not set in this flags setting must also not be set in the packet. </p> </li> </ul>"}, "Masks": {"shape": "Flags", "documentation": "<p>The set of flags to consider in the inspection. To inspect all flags in the valid values list, leave this with no setting.</p>"}}, "documentation": "<p>TCP flags and masks to inspect packets for, used in stateless rules <a>MatchAttributes</a> settings.</p>"}, "TCPFlags": {"type": "list", "member": {"shape": "TCPFlagField"}}, "TLSInspectionConfiguration": {"type": "structure", "members": {"ServerCertificateConfigurations": {"shape": "ServerCertificateConfigurations", "documentation": "<p>Lists the server certificate configurations that are associated with the TLS configuration.</p>"}}, "documentation": "<p>The object that defines a TLS inspection configuration. This, along with <a>TLSInspectionConfigurationResponse</a>, define the TLS inspection configuration. You can retrieve all objects for a TLS inspection configuration by calling <a>DescribeTLSInspectionConfiguration</a>. </p> <p>Network Firewall uses a TLS inspection configuration to decrypt traffic. Network Firewall re-encrypts the traffic before sending it to its destination.</p> <p>To use a TLS inspection configuration, you add it to a new Network Firewall firewall policy, then you apply the firewall policy to a firewall. Network Firewall acts as a proxy service to decrypt and inspect the traffic traveling through your firewalls. You can reference a TLS inspection configuration from more than one firewall policy, and you can use a firewall policy in more than one firewall. For more information about using TLS inspection configurations, see <a href=\"https://docs.aws.amazon.com/network-firewall/latest/developerguide/tls-inspection.html\">Inspecting SSL/TLS traffic with TLS inspection configurations</a> in the <i>Network Firewall Developer Guide</i>.</p>"}, "TLSInspectionConfigurationMetadata": {"type": "structure", "members": {"Name": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the TLS inspection configuration. You can't change the name of a TLS inspection configuration after you create it.</p>"}, "Arn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the TLS inspection configuration.</p>"}}, "documentation": "<p>High-level information about a TLS inspection configuration, returned by <code>ListTLSInspectionConfigurations</code>. You can use the information provided in the metadata to retrieve and manage a TLS configuration.</p>"}, "TLSInspectionConfigurationResponse": {"type": "structure", "required": ["TLSInspectionConfigurationArn", "TLSInspectionConfigurationName", "TLSInspectionConfigurationId"], "members": {"TLSInspectionConfigurationArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the TLS inspection configuration.</p>"}, "TLSInspectionConfigurationName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the TLS inspection configuration. You can't change the name of a TLS inspection configuration after you create it.</p>"}, "TLSInspectionConfigurationId": {"shape": "ResourceId", "documentation": "<p>A unique identifier for the TLS inspection configuration. This ID is returned in the responses to create and list commands. You provide it to operations such as update and delete.</p>"}, "TLSInspectionConfigurationStatus": {"shape": "ResourceStatus", "documentation": "<p>Detailed information about the current status of a <a>TLSInspectionConfiguration</a>. You can retrieve this for a TLS inspection configuration by calling <a>DescribeTLSInspectionConfiguration</a> and providing the TLS inspection configuration name and ARN.</p>"}, "Description": {"shape": "Description", "documentation": "<p>A description of the TLS inspection configuration. </p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The key:value pairs to associate with the resource.</p>"}, "LastModifiedTime": {"shape": "LastUpdateTime", "documentation": "<p>The last time that the TLS inspection configuration was changed.</p>"}, "NumberOfAssociations": {"shape": "NumberOfAssociations", "documentation": "<p>The number of firewall policies that use this TLS inspection configuration.</p>"}, "EncryptionConfiguration": {"shape": "EncryptionConfiguration", "documentation": "<p>A complex type that contains the Amazon Web Services KMS encryption configuration settings for your TLS inspection configuration.</p>"}, "Certificates": {"shape": "Certificates", "documentation": "<p>A list of the certificates associated with the TLS inspection configuration.</p>"}, "CertificateAuthority": {"shape": "TlsCertificateData"}}, "documentation": "<p>The high-level properties of a TLS inspection configuration. This, along with the <code>TLSInspectionConfiguration</code>, define the TLS inspection configuration. You can retrieve all objects for a TLS inspection configuration by calling <code>DescribeTLSInspectionConfiguration</code>.</p>"}, "TLSInspectionConfigurations": {"type": "list", "member": {"shape": "TLSInspectionConfigurationMetadata"}}, "Tag": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {"shape": "TagKey", "documentation": "<p>The part of the key:value pair that defines a tag. You can use a tag key to describe a category of information, such as \"customer.\" Tag keys are case-sensitive.</p>"}, "Value": {"shape": "TagValue", "documentation": "<p>The part of the key:value pair that defines a tag. You can use a tag value to describe a specific value within a category, such as \"companyA\" or \"companyB.\" Tag values are case-sensitive.</p>"}}, "documentation": "<p>A key:value pair associated with an Amazon Web Services resource. The key:value pair can be anything you define. Typically, the tag key represents a category (such as \"environment\") and the tag value represents a specific value within that category (such as \"test,\" \"development,\" or \"production\"). You can add up to 50 tags to each Amazon Web Services resource. </p>"}, "TagKey": {"type": "string", "max": 128, "min": 1, "pattern": "^.*$"}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 200, "min": 1}, "TagList": {"type": "list", "member": {"shape": "Tag"}, "max": 200, "min": 1}, "TagResourceRequest": {"type": "structure", "required": ["ResourceArn", "Tags"], "members": {"ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p/>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0, "pattern": "^.*$"}, "TagsPaginationMaxResults": {"type": "integer", "max": 100, "min": 0}, "TargetType": {"type": "string", "enum": ["TLS_SNI", "HTTP_HOST"]}, "TargetTypes": {"type": "list", "member": {"shape": "TargetType"}}, "TcpIdleTimeoutRangeBound": {"type": "integer"}, "ThrottlingException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>Unable to process the request due to throttling limitations.</p>", "exception": true}, "TlsCertificateData": {"type": "structure", "members": {"CertificateArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the certificate.</p>"}, "CertificateSerial": {"shape": "CollectionMember_String", "documentation": "<p>The serial number of the certificate.</p>"}, "Status": {"shape": "CollectionMember_String", "documentation": "<p>The status of the certificate.</p>"}, "StatusMessage": {"shape": "StatusReason", "documentation": "<p>Contains details about the certificate status, including information about certificate errors.</p>"}}, "documentation": "<p>Contains metadata about an Certificate Manager certificate.</p>"}, "TransitGatewayAttachmentId": {"type": "string", "max": 128, "min": 1, "pattern": "^tgw-attach-[0-9a-z]+$"}, "TransitGatewayAttachmentStatus": {"type": "string", "enum": ["CREATING", "DELETING", "DELETED", "FAILED", "ERROR", "READY", "PENDING_ACCEPTANCE", "REJECTING", "REJECTED"]}, "TransitGatewayAttachmentSyncState": {"type": "structure", "members": {"AttachmentId": {"shape": "AttachmentId", "documentation": "<p>The unique identifier of the transit gateway attachment.</p>"}, "TransitGatewayAttachmentStatus": {"shape": "TransitGatewayAttachmentStatus", "documentation": "<p>The current status of the transit gateway attachment.</p> <p>Valid values are:</p> <ul> <li> <p> <code>CREATING</code> - The attachment is being created</p> </li> <li> <p> <code>DELETING</code> - The attachment is being deleted</p> </li> <li> <p> <code>DELETED</code> - The attachment has been deleted</p> </li> <li> <p> <code>FAILED</code> - The attachment creation has failed and cannot be recovered</p> </li> <li> <p> <code>ERROR</code> - The attachment is in an error state that might be recoverable</p> </li> <li> <p> <code>READY</code> - The attachment is active and processing traffic</p> </li> <li> <p> <code>PENDING_ACCEPTANCE</code> - The attachment is waiting to be accepted</p> </li> <li> <p> <code>REJECTING</code> - The attachment is in the process of being rejected</p> </li> <li> <p> <code>REJECTED</code> - The attachment has been rejected</p> </li> </ul>"}, "StatusMessage": {"shape": "TransitGatewayAttachmentSyncStateMessage", "documentation": "<p>A message providing additional information about the current status, particularly useful when the transit gateway attachment is in a non-<code>READY</code> state.</p> <p>Valid values are:</p> <ul> <li> <p> <code>CREATING</code> - The attachment is being created</p> </li> <li> <p> <code>DELETING</code> - The attachment is being deleted</p> </li> <li> <p> <code>DELETED</code> - The attachment has been deleted</p> </li> <li> <p> <code>FAILED</code> - The attachment creation has failed and cannot be recovered</p> </li> <li> <p> <code>ERROR</code> - The attachment is in an error state that might be recoverable</p> </li> <li> <p> <code>READY</code> - The attachment is active and processing traffic</p> </li> <li> <p> <code>PENDING_ACCEPTANCE</code> - The attachment is waiting to be accepted</p> </li> <li> <p> <code>REJECTING</code> - The attachment is in the process of being rejected</p> </li> <li> <p> <code>REJECTED</code> - The attachment has been rejected</p> </li> </ul> <p>For information about troubleshooting endpoint failures, see <a href=\"https://docs.aws.amazon.com/network-firewall/latest/developerguide/firewall-troubleshooting-endpoint-failures.html\">Troubleshooting firewall endpoint failures</a> in the <i>Network Firewall Developer Guide</i>.</p>"}}, "documentation": "<p>Contains information about the synchronization state of a transit gateway attachment, including its current status and any error messages. Network Firewall uses this to track the state of your transit gateway configuration changes.</p>"}, "TransitGatewayAttachmentSyncStateMessage": {"type": "string"}, "TransitGatewayId": {"type": "string", "max": 128, "min": 1, "pattern": "^tgw-[0-9a-z]+$"}, "UniqueSources": {"type": "structure", "members": {"Count": {"shape": "Count", "documentation": "<p>The number of unique source IP addresses that connected to a domain.</p>"}}, "documentation": "<p>A unique source IP address that connected to a domain.</p>"}, "UnsupportedOperationException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The operation you requested isn't supported by Network Firewall. </p>", "exception": true}, "UntagResourceRequest": {"type": "structure", "required": ["ResourceArn", "TagKeys"], "members": {"ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource.</p>"}, "TagKeys": {"shape": "TagKeyList", "documentation": "<p/>"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateAvailabilityZoneChangeProtectionRequest": {"type": "structure", "required": ["AvailabilityZoneChangeProtection"], "members": {"UpdateToken": {"shape": "UpdateToken", "documentation": "<p>An optional token that you can use for optimistic locking. Network Firewall returns a token to your requests that access the firewall. The token marks the state of the firewall resource at the time of the request. </p> <p>To make an unconditional change to the firewall, omit the token in your update request. Without the token, Network Firewall performs your updates regardless of whether the firewall has changed since you last retrieved it.</p> <p>To make a conditional change to the firewall, provide the token in your update request. Network Firewall uses the token to ensure that the firewall hasn't changed since you last retrieved it. If it has changed, the operation fails with an <code>InvalidTokenException</code>. If this happens, retrieve the firewall again to get a current copy of it with a new token. Reapply your changes as needed, then try the operation again using the new token. </p>"}, "FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "FirewallName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the firewall. You can't change the name of a firewall after you create it.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "AvailabilityZoneChangeProtection": {"shape": "Boolean", "documentation": "<p>A setting indicating whether the firewall is protected against changes to the subnet associations. Use this setting to protect against accidentally modifying the subnet associations for a firewall that is in use. When you create a firewall, the operation initializes this setting to <code>TRUE</code>.</p>"}}}, "UpdateAvailabilityZoneChangeProtectionResponse": {"type": "structure", "members": {"UpdateToken": {"shape": "UpdateToken", "documentation": "<p>An optional token that you can use for optimistic locking. Network Firewall returns a token to your requests that access the firewall. The token marks the state of the firewall resource at the time of the request. </p> <p>To make an unconditional change to the firewall, omit the token in your update request. Without the token, Network Firewall performs your updates regardless of whether the firewall has changed since you last retrieved it.</p> <p>To make a conditional change to the firewall, provide the token in your update request. Network Firewall uses the token to ensure that the firewall hasn't changed since you last retrieved it. If it has changed, the operation fails with an <code>InvalidTokenException</code>. If this happens, retrieve the firewall again to get a current copy of it with a new token. Reapply your changes as needed, then try the operation again using the new token. </p>"}, "FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p>"}, "FirewallName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the firewall. You can't change the name of a firewall after you create it.</p>"}, "AvailabilityZoneChangeProtection": {"shape": "Boolean", "documentation": "<p>A setting indicating whether the firewall is protected against changes to the subnet associations. Use this setting to protect against accidentally modifying the subnet associations for a firewall that is in use. When you create a firewall, the operation initializes this setting to <code>TRUE</code>.</p>"}}}, "UpdateFirewallAnalysisSettingsRequest": {"type": "structure", "members": {"EnabledAnalysisTypes": {"shape": "EnabledAnalysisTypes", "documentation": "<p>An optional setting indicating the specific traffic analysis types to enable on the firewall. </p>"}, "FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "FirewallName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the firewall. You can't change the name of a firewall after you create it.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "UpdateToken": {"shape": "UpdateToken", "documentation": "<p>An optional token that you can use for optimistic locking. Network Firewall returns a token to your requests that access the firewall. The token marks the state of the firewall resource at the time of the request. </p> <p>To make an unconditional change to the firewall, omit the token in your update request. Without the token, Network Firewall performs your updates regardless of whether the firewall has changed since you last retrieved it.</p> <p>To make a conditional change to the firewall, provide the token in your update request. Network Firewall uses the token to ensure that the firewall hasn't changed since you last retrieved it. If it has changed, the operation fails with an <code>InvalidTokenException</code>. If this happens, retrieve the firewall again to get a current copy of it with a new token. Reapply your changes as needed, then try the operation again using the new token. </p>"}}}, "UpdateFirewallAnalysisSettingsResponse": {"type": "structure", "members": {"EnabledAnalysisTypes": {"shape": "EnabledAnalysisTypes", "documentation": "<p>An optional setting indicating the specific traffic analysis types to enable on the firewall. </p>"}, "FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "FirewallName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the firewall. You can't change the name of a firewall after you create it.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "UpdateToken": {"shape": "UpdateToken", "documentation": "<p>An optional token that you can use for optimistic locking. Network Firewall returns a token to your requests that access the firewall. The token marks the state of the firewall resource at the time of the request. </p> <p>To make an unconditional change to the firewall, omit the token in your update request. Without the token, Network Firewall performs your updates regardless of whether the firewall has changed since you last retrieved it.</p> <p>To make a conditional change to the firewall, provide the token in your update request. Network Firewall uses the token to ensure that the firewall hasn't changed since you last retrieved it. If it has changed, the operation fails with an <code>InvalidTokenException</code>. If this happens, retrieve the firewall again to get a current copy of it with a new token. Reapply your changes as needed, then try the operation again using the new token. </p>"}}}, "UpdateFirewallDeleteProtectionRequest": {"type": "structure", "required": ["DeleteProtection"], "members": {"UpdateToken": {"shape": "UpdateToken", "documentation": "<p>An optional token that you can use for optimistic locking. Network Firewall returns a token to your requests that access the firewall. The token marks the state of the firewall resource at the time of the request. </p> <p>To make an unconditional change to the firewall, omit the token in your update request. Without the token, Network Firewall performs your updates regardless of whether the firewall has changed since you last retrieved it.</p> <p>To make a conditional change to the firewall, provide the token in your update request. Network Firewall uses the token to ensure that the firewall hasn't changed since you last retrieved it. If it has changed, the operation fails with an <code>InvalidTokenException</code>. If this happens, retrieve the firewall again to get a current copy of it with a new token. Reapply your changes as needed, then try the operation again using the new token. </p>"}, "FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "FirewallName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the firewall. You can't change the name of a firewall after you create it.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "DeleteProtection": {"shape": "Boolean", "documentation": "<p>A flag indicating whether it is possible to delete the firewall. A setting of <code>TRUE</code> indicates that the firewall is protected against deletion. Use this setting to protect against accidentally deleting a firewall that is in use. When you create a firewall, the operation initializes this flag to <code>TRUE</code>.</p>"}}}, "UpdateFirewallDeleteProtectionResponse": {"type": "structure", "members": {"FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p>"}, "FirewallName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the firewall. You can't change the name of a firewall after you create it.</p>"}, "DeleteProtection": {"shape": "Boolean", "documentation": "<p>A flag indicating whether it is possible to delete the firewall. A setting of <code>TRUE</code> indicates that the firewall is protected against deletion. Use this setting to protect against accidentally deleting a firewall that is in use. When you create a firewall, the operation initializes this flag to <code>TRUE</code>.</p>"}, "UpdateToken": {"shape": "UpdateToken", "documentation": "<p>An optional token that you can use for optimistic locking. Network Firewall returns a token to your requests that access the firewall. The token marks the state of the firewall resource at the time of the request. </p> <p>To make an unconditional change to the firewall, omit the token in your update request. Without the token, Network Firewall performs your updates regardless of whether the firewall has changed since you last retrieved it.</p> <p>To make a conditional change to the firewall, provide the token in your update request. Network Firewall uses the token to ensure that the firewall hasn't changed since you last retrieved it. If it has changed, the operation fails with an <code>InvalidTokenException</code>. If this happens, retrieve the firewall again to get a current copy of it with a new token. Reapply your changes as needed, then try the operation again using the new token. </p>"}}}, "UpdateFirewallDescriptionRequest": {"type": "structure", "members": {"UpdateToken": {"shape": "UpdateToken", "documentation": "<p>An optional token that you can use for optimistic locking. Network Firewall returns a token to your requests that access the firewall. The token marks the state of the firewall resource at the time of the request. </p> <p>To make an unconditional change to the firewall, omit the token in your update request. Without the token, Network Firewall performs your updates regardless of whether the firewall has changed since you last retrieved it.</p> <p>To make a conditional change to the firewall, provide the token in your update request. Network Firewall uses the token to ensure that the firewall hasn't changed since you last retrieved it. If it has changed, the operation fails with an <code>InvalidTokenException</code>. If this happens, retrieve the firewall again to get a current copy of it with a new token. Reapply your changes as needed, then try the operation again using the new token. </p>"}, "FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "FirewallName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the firewall. You can't change the name of a firewall after you create it.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "Description": {"shape": "Description", "documentation": "<p>The new description for the firewall. If you omit this setting, Network Firewall removes the description for the firewall.</p>"}}}, "UpdateFirewallDescriptionResponse": {"type": "structure", "members": {"FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p>"}, "FirewallName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the firewall. You can't change the name of a firewall after you create it.</p>"}, "Description": {"shape": "Description", "documentation": "<p>A description of the firewall.</p>"}, "UpdateToken": {"shape": "UpdateToken", "documentation": "<p>An optional token that you can use for optimistic locking. Network Firewall returns a token to your requests that access the firewall. The token marks the state of the firewall resource at the time of the request. </p> <p>To make an unconditional change to the firewall, omit the token in your update request. Without the token, Network Firewall performs your updates regardless of whether the firewall has changed since you last retrieved it.</p> <p>To make a conditional change to the firewall, provide the token in your update request. Network Firewall uses the token to ensure that the firewall hasn't changed since you last retrieved it. If it has changed, the operation fails with an <code>InvalidTokenException</code>. If this happens, retrieve the firewall again to get a current copy of it with a new token. Reapply your changes as needed, then try the operation again using the new token. </p>"}}}, "UpdateFirewallEncryptionConfigurationRequest": {"type": "structure", "members": {"UpdateToken": {"shape": "UpdateToken", "documentation": "<p>An optional token that you can use for optimistic locking. Network Firewall returns a token to your requests that access the firewall. The token marks the state of the firewall resource at the time of the request. </p> <p>To make an unconditional change to the firewall, omit the token in your update request. Without the token, Network Firewall performs your updates regardless of whether the firewall has changed since you last retrieved it.</p> <p>To make a conditional change to the firewall, provide the token in your update request. Network Firewall uses the token to ensure that the firewall hasn't changed since you last retrieved it. If it has changed, the operation fails with an <code>InvalidTokenException</code>. If this happens, retrieve the firewall again to get a current copy of it with a new token. Reapply your changes as needed, then try the operation again using the new token. </p>"}, "FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p>"}, "FirewallName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the firewall. You can't change the name of a firewall after you create it.</p>"}, "EncryptionConfiguration": {"shape": "EncryptionConfiguration"}}}, "UpdateFirewallEncryptionConfigurationResponse": {"type": "structure", "members": {"FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p>"}, "FirewallName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the firewall. You can't change the name of a firewall after you create it.</p>"}, "UpdateToken": {"shape": "UpdateToken", "documentation": "<p>An optional token that you can use for optimistic locking. Network Firewall returns a token to your requests that access the firewall. The token marks the state of the firewall resource at the time of the request. </p> <p>To make an unconditional change to the firewall, omit the token in your update request. Without the token, Network Firewall performs your updates regardless of whether the firewall has changed since you last retrieved it.</p> <p>To make a conditional change to the firewall, provide the token in your update request. Network Firewall uses the token to ensure that the firewall hasn't changed since you last retrieved it. If it has changed, the operation fails with an <code>InvalidTokenException</code>. If this happens, retrieve the firewall again to get a current copy of it with a new token. Reapply your changes as needed, then try the operation again using the new token. </p>"}, "EncryptionConfiguration": {"shape": "EncryptionConfiguration"}}}, "UpdateFirewallPolicyChangeProtectionRequest": {"type": "structure", "required": ["FirewallPolicyChangeProtection"], "members": {"UpdateToken": {"shape": "UpdateToken", "documentation": "<p>An optional token that you can use for optimistic locking. Network Firewall returns a token to your requests that access the firewall. The token marks the state of the firewall resource at the time of the request. </p> <p>To make an unconditional change to the firewall, omit the token in your update request. Without the token, Network Firewall performs your updates regardless of whether the firewall has changed since you last retrieved it.</p> <p>To make a conditional change to the firewall, provide the token in your update request. Network Firewall uses the token to ensure that the firewall hasn't changed since you last retrieved it. If it has changed, the operation fails with an <code>InvalidTokenException</code>. If this happens, retrieve the firewall again to get a current copy of it with a new token. Reapply your changes as needed, then try the operation again using the new token. </p>"}, "FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "FirewallName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the firewall. You can't change the name of a firewall after you create it.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "FirewallPolicyChangeProtection": {"shape": "Boolean", "documentation": "<p>A setting indicating whether the firewall is protected against a change to the firewall policy association. Use this setting to protect against accidentally modifying the firewall policy for a firewall that is in use. When you create a firewall, the operation initializes this setting to <code>TRUE</code>.</p>"}}}, "UpdateFirewallPolicyChangeProtectionResponse": {"type": "structure", "members": {"UpdateToken": {"shape": "UpdateToken", "documentation": "<p>An optional token that you can use for optimistic locking. Network Firewall returns a token to your requests that access the firewall. The token marks the state of the firewall resource at the time of the request. </p> <p>To make an unconditional change to the firewall, omit the token in your update request. Without the token, Network Firewall performs your updates regardless of whether the firewall has changed since you last retrieved it.</p> <p>To make a conditional change to the firewall, provide the token in your update request. Network Firewall uses the token to ensure that the firewall hasn't changed since you last retrieved it. If it has changed, the operation fails with an <code>InvalidTokenException</code>. If this happens, retrieve the firewall again to get a current copy of it with a new token. Reapply your changes as needed, then try the operation again using the new token. </p>"}, "FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p>"}, "FirewallName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the firewall. You can't change the name of a firewall after you create it.</p>"}, "FirewallPolicyChangeProtection": {"shape": "Boolean", "documentation": "<p>A setting indicating whether the firewall is protected against a change to the firewall policy association. Use this setting to protect against accidentally modifying the firewall policy for a firewall that is in use. When you create a firewall, the operation initializes this setting to <code>TRUE</code>.</p>"}}}, "UpdateFirewallPolicyRequest": {"type": "structure", "required": ["UpdateToken", "FirewallPolicy"], "members": {"UpdateToken": {"shape": "UpdateToken", "documentation": "<p>A token used for optimistic locking. Network Firewall returns a token to your requests that access the firewall policy. The token marks the state of the policy resource at the time of the request. </p> <p>To make changes to the policy, you provide the token in your request. Network Firewall uses the token to ensure that the policy hasn't changed since you last retrieved it. If it has changed, the operation fails with an <code>InvalidTokenException</code>. If this happens, retrieve the firewall policy again to get a current copy of it with current token. Reapply your changes as needed, then try the operation again using the new token. </p>"}, "FirewallPolicyArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall policy.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "FirewallPolicyName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the firewall policy. You can't change the name of a firewall policy after you create it.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "FirewallPolicy": {"shape": "FirewallPolicy", "documentation": "<p>The updated firewall policy to use for the firewall. You can't add or remove a <a>TLSInspectionConfiguration</a> after you create a firewall policy. However, you can replace an existing TLS inspection configuration with another <code>TLSInspectionConfiguration</code>.</p>"}, "Description": {"shape": "Description", "documentation": "<p>A description of the firewall policy.</p>"}, "DryRun": {"shape": "Boolean", "documentation": "<p>Indicates whether you want Network Firewall to just check the validity of the request, rather than run the request. </p> <p>If set to <code>TRUE</code>, Network Firewall checks whether the request can run successfully, but doesn't actually make the requested changes. The call returns the value that the request would return if you ran it with dry run set to <code>FALSE</code>, but doesn't make additions or changes to your resources. This option allows you to make sure that you have the required permissions to run the request and that your request parameters are valid. </p> <p>If set to <code>FALSE</code>, Network Firewall makes the requested changes to your resources. </p>"}, "EncryptionConfiguration": {"shape": "EncryptionConfiguration", "documentation": "<p>A complex type that contains settings for encryption of your firewall policy resources.</p>"}}}, "UpdateFirewallPolicyResponse": {"type": "structure", "required": ["UpdateToken", "FirewallPolicyResponse"], "members": {"UpdateToken": {"shape": "UpdateToken", "documentation": "<p>A token used for optimistic locking. Network Firewall returns a token to your requests that access the firewall policy. The token marks the state of the policy resource at the time of the request. </p> <p>To make changes to the policy, you provide the token in your request. Network Firewall uses the token to ensure that the policy hasn't changed since you last retrieved it. If it has changed, the operation fails with an <code>InvalidTokenException</code>. If this happens, retrieve the firewall policy again to get a current copy of it with current token. Reapply your changes as needed, then try the operation again using the new token. </p>"}, "FirewallPolicyResponse": {"shape": "FirewallPolicyResponse", "documentation": "<p>The high-level properties of a firewall policy. This, along with the <a>FirewallPolicy</a>, define the policy. You can retrieve all objects for a firewall policy by calling <a>DescribeFirewallPolicy</a>. </p>"}}}, "UpdateLoggingConfigurationRequest": {"type": "structure", "members": {"FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "FirewallName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the firewall. You can't change the name of a firewall after you create it.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "LoggingConfiguration": {"shape": "LoggingConfiguration", "documentation": "<p>Defines how Network Firewall performs logging for a firewall. If you omit this setting, Network Firewall disables logging for the firewall.</p>"}, "EnableMonitoringDashboard": {"shape": "EnableMonitoringDashboard", "documentation": "<p>A boolean that lets you enable or disable the detailed firewall monitoring dashboard on the firewall. </p> <p>The monitoring dashboard provides comprehensive visibility into your firewall's flow logs and alert logs. After you enable detailed monitoring, you can access these dashboards directly from the <b>Monitoring</b> page of the Network Firewall console.</p> <p> Specify <code>TRUE</code> to enable the the detailed monitoring dashboard on the firewall. Specify <code>FALSE</code> to disable the the detailed monitoring dashboard on the firewall. </p>"}}}, "UpdateLoggingConfigurationResponse": {"type": "structure", "members": {"FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p>"}, "FirewallName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the firewall. You can't change the name of a firewall after you create it.</p>"}, "LoggingConfiguration": {"shape": "LoggingConfiguration"}, "EnableMonitoringDashboard": {"shape": "EnableMonitoringDashboard", "documentation": "<p>A boolean that reflects whether or not the firewall monitoring dashboard is enabled on a firewall.</p> <p> Returns <code>TRUE</code> when the firewall monitoring dashboard is enabled on the firewall. Returns <code>FALSE</code> when the firewall monitoring dashboard is not enabled on the firewall. </p>"}}}, "UpdateRuleGroupRequest": {"type": "structure", "required": ["UpdateToken"], "members": {"UpdateToken": {"shape": "UpdateToken", "documentation": "<p>A token used for optimistic locking. Network Firewall returns a token to your requests that access the rule group. The token marks the state of the rule group resource at the time of the request. </p> <p>To make changes to the rule group, you provide the token in your request. Network Firewall uses the token to ensure that the rule group hasn't changed since you last retrieved it. If it has changed, the operation fails with an <code>InvalidTokenException</code>. If this happens, retrieve the rule group again to get a current copy of it with a current token. Reapply your changes as needed, then try the operation again using the new token. </p>"}, "RuleGroupArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the rule group.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "RuleGroupName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the rule group. You can't change the name of a rule group after you create it.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "RuleGroup": {"shape": "RuleGroup", "documentation": "<p>An object that defines the rule group rules. </p> <note> <p>You must provide either this rule group setting or a <code>Rules</code> setting, but not both. </p> </note>"}, "Rules": {"shape": "RulesString", "documentation": "<p>A string containing stateful rule group rules specifications in Suricata flat format, with one rule per line. Use this to import your existing Suricata compatible rule groups. </p> <note> <p>You must provide either this rules setting or a populated <code>RuleGroup</code> setting, but not both. </p> </note> <p>You can provide your rule group specification in Suricata flat format through this setting when you create or update your rule group. The call response returns a <a>RuleGroup</a> object that Network Firewall has populated from your string. </p>"}, "Type": {"shape": "RuleGroupType", "documentation": "<p>Indicates whether the rule group is stateless or stateful. If the rule group is stateless, it contains stateless rules. If it is stateful, it contains stateful rules. </p> <note> <p>This setting is required for requests that do not include the <code>RuleGroupARN</code>.</p> </note>"}, "Description": {"shape": "Description", "documentation": "<p>A description of the rule group. </p>"}, "DryRun": {"shape": "Boolean", "documentation": "<p>Indicates whether you want Network Firewall to just check the validity of the request, rather than run the request. </p> <p>If set to <code>TRUE</code>, Network Firewall checks whether the request can run successfully, but doesn't actually make the requested changes. The call returns the value that the request would return if you ran it with dry run set to <code>FALSE</code>, but doesn't make additions or changes to your resources. This option allows you to make sure that you have the required permissions to run the request and that your request parameters are valid. </p> <p>If set to <code>FALSE</code>, Network Firewall makes the requested changes to your resources. </p>"}, "EncryptionConfiguration": {"shape": "EncryptionConfiguration", "documentation": "<p>A complex type that contains settings for encryption of your rule group resources.</p>"}, "SourceMetadata": {"shape": "SourceMetadata", "documentation": "<p>A complex type that contains metadata about the rule group that your own rule group is copied from. You can use the metadata to keep track of updates made to the originating rule group.</p>"}, "AnalyzeRuleGroup": {"shape": "Boolean", "documentation": "<p>Indicates whether you want Network Firewall to analyze the stateless rules in the rule group for rule behavior such as asymmetric routing. If set to <code>TRUE</code>, Network Firewall runs the analysis and then updates the rule group for you. To run the stateless rule group analyzer without updating the rule group, set <code>DryRun</code> to <code>TRUE</code>. </p>"}, "SummaryConfiguration": {"shape": "SummaryConfiguration", "documentation": "<p>Updates the selected summary configuration for a rule group.</p> <p>Changes affect subsequent responses from <a>DescribeRuleGroupSummary</a>.</p>"}}}, "UpdateRuleGroupResponse": {"type": "structure", "required": ["UpdateToken", "RuleGroupResponse"], "members": {"UpdateToken": {"shape": "UpdateToken", "documentation": "<p>A token used for optimistic locking. Network Firewall returns a token to your requests that access the rule group. The token marks the state of the rule group resource at the time of the request. </p> <p>To make changes to the rule group, you provide the token in your request. Network Firewall uses the token to ensure that the rule group hasn't changed since you last retrieved it. If it has changed, the operation fails with an <code>InvalidTokenException</code>. If this happens, retrieve the rule group again to get a current copy of it with a current token. Reapply your changes as needed, then try the operation again using the new token. </p>"}, "RuleGroupResponse": {"shape": "RuleGroupResponse", "documentation": "<p>The high-level properties of a rule group. This, along with the <a>RuleGroup</a>, define the rule group. You can retrieve all objects for a rule group by calling <a>DescribeRuleGroup</a>. </p>"}}}, "UpdateSubnetChangeProtectionRequest": {"type": "structure", "required": ["SubnetChangeProtection"], "members": {"UpdateToken": {"shape": "UpdateToken", "documentation": "<p>An optional token that you can use for optimistic locking. Network Firewall returns a token to your requests that access the firewall. The token marks the state of the firewall resource at the time of the request. </p> <p>To make an unconditional change to the firewall, omit the token in your update request. Without the token, Network Firewall performs your updates regardless of whether the firewall has changed since you last retrieved it.</p> <p>To make a conditional change to the firewall, provide the token in your update request. Network Firewall uses the token to ensure that the firewall hasn't changed since you last retrieved it. If it has changed, the operation fails with an <code>InvalidTokenException</code>. If this happens, retrieve the firewall again to get a current copy of it with a new token. Reapply your changes as needed, then try the operation again using the new token. </p>"}, "FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "FirewallName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the firewall. You can't change the name of a firewall after you create it.</p> <p>You must specify the ARN or the name, and you can specify both. </p>"}, "SubnetChangeProtection": {"shape": "Boolean", "documentation": "<p>A setting indicating whether the firewall is protected against changes to the subnet associations. Use this setting to protect against accidentally modifying the subnet associations for a firewall that is in use. When you create a firewall, the operation initializes this setting to <code>TRUE</code>.</p>"}}}, "UpdateSubnetChangeProtectionResponse": {"type": "structure", "members": {"UpdateToken": {"shape": "UpdateToken", "documentation": "<p>An optional token that you can use for optimistic locking. Network Firewall returns a token to your requests that access the firewall. The token marks the state of the firewall resource at the time of the request. </p> <p>To make an unconditional change to the firewall, omit the token in your update request. Without the token, Network Firewall performs your updates regardless of whether the firewall has changed since you last retrieved it.</p> <p>To make a conditional change to the firewall, provide the token in your update request. Network Firewall uses the token to ensure that the firewall hasn't changed since you last retrieved it. If it has changed, the operation fails with an <code>InvalidTokenException</code>. If this happens, retrieve the firewall again to get a current copy of it with a new token. Reapply your changes as needed, then try the operation again using the new token. </p>"}, "FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p>"}, "FirewallName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the firewall. You can't change the name of a firewall after you create it.</p>"}, "SubnetChangeProtection": {"shape": "Boolean", "documentation": "<p>A setting indicating whether the firewall is protected against changes to the subnet associations. Use this setting to protect against accidentally modifying the subnet associations for a firewall that is in use. When you create a firewall, the operation initializes this setting to <code>TRUE</code>.</p>"}}}, "UpdateTLSInspectionConfigurationRequest": {"type": "structure", "required": ["TLSInspectionConfiguration", "UpdateToken"], "members": {"TLSInspectionConfigurationArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the TLS inspection configuration.</p>"}, "TLSInspectionConfigurationName": {"shape": "ResourceName", "documentation": "<p>The descriptive name of the TLS inspection configuration. You can't change the name of a TLS inspection configuration after you create it.</p>"}, "TLSInspectionConfiguration": {"shape": "TLSInspectionConfiguration", "documentation": "<p>The object that defines a TLS inspection configuration. This, along with <a>TLSInspectionConfigurationResponse</a>, define the TLS inspection configuration. You can retrieve all objects for a TLS inspection configuration by calling <a>DescribeTLSInspectionConfiguration</a>. </p> <p>Network Firewall uses a TLS inspection configuration to decrypt traffic. Network Firewall re-encrypts the traffic before sending it to its destination.</p> <p>To use a TLS inspection configuration, you add it to a new Network Firewall firewall policy, then you apply the firewall policy to a firewall. Network Firewall acts as a proxy service to decrypt and inspect the traffic traveling through your firewalls. You can reference a TLS inspection configuration from more than one firewall policy, and you can use a firewall policy in more than one firewall. For more information about using TLS inspection configurations, see <a href=\"https://docs.aws.amazon.com/network-firewall/latest/developerguide/tls-inspection.html\">Inspecting SSL/TLS traffic with TLS inspection configurations</a> in the <i>Network Firewall Developer Guide</i>.</p>"}, "Description": {"shape": "Description", "documentation": "<p>A description of the TLS inspection configuration. </p>"}, "EncryptionConfiguration": {"shape": "EncryptionConfiguration", "documentation": "<p>A complex type that contains the Amazon Web Services KMS encryption configuration settings for your TLS inspection configuration.</p>"}, "UpdateToken": {"shape": "UpdateToken", "documentation": "<p>A token used for optimistic locking. Network Firewall returns a token to your requests that access the TLS inspection configuration. The token marks the state of the TLS inspection configuration resource at the time of the request. </p> <p>To make changes to the TLS inspection configuration, you provide the token in your request. Network Firewall uses the token to ensure that the TLS inspection configuration hasn't changed since you last retrieved it. If it has changed, the operation fails with an <code>InvalidTokenException</code>. If this happens, retrieve the TLS inspection configuration again to get a current copy of it with a current token. Reapply your changes as needed, then try the operation again using the new token. </p>"}}}, "UpdateTLSInspectionConfigurationResponse": {"type": "structure", "required": ["UpdateToken", "TLSInspectionConfigurationResponse"], "members": {"UpdateToken": {"shape": "UpdateToken", "documentation": "<p>A token used for optimistic locking. Network Firewall returns a token to your requests that access the TLS inspection configuration. The token marks the state of the TLS inspection configuration resource at the time of the request. </p> <p>To make changes to the TLS inspection configuration, you provide the token in your request. Network Firewall uses the token to ensure that the TLS inspection configuration hasn't changed since you last retrieved it. If it has changed, the operation fails with an <code>InvalidTokenException</code>. If this happens, retrieve the TLS inspection configuration again to get a current copy of it with a current token. Reapply your changes as needed, then try the operation again using the new token. </p>"}, "TLSInspectionConfigurationResponse": {"shape": "TLSInspectionConfigurationResponse", "documentation": "<p>The high-level properties of a TLS inspection configuration. This, along with the <a>TLSInspectionConfiguration</a>, define the TLS inspection configuration. You can retrieve all objects for a TLS inspection configuration by calling <a>DescribeTLSInspectionConfiguration</a>. </p>"}}}, "UpdateToken": {"type": "string", "max": 1024, "min": 1, "pattern": "^([0-9a-f]{8})-([0-9a-f]{4}-){3}([0-9a-f]{12})$"}, "VariableDefinition": {"type": "string", "min": 1, "pattern": "^.*$"}, "VariableDefinitionList": {"type": "list", "member": {"shape": "VariableDefinition"}}, "VpcEndpointAssociation": {"type": "structure", "required": ["VpcEndpointAssociationArn", "FirewallArn", "VpcId", "SubnetMapping"], "members": {"VpcEndpointAssociationId": {"shape": "ResourceId", "documentation": "<p>The unique identifier of the VPC endpoint association. </p>"}, "VpcEndpointAssociationArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of a VPC endpoint association.</p>"}, "FirewallArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the firewall.</p>"}, "VpcId": {"shape": "VpcId", "documentation": "<p>The unique identifier of the VPC for the endpoint association. </p>"}, "SubnetMapping": {"shape": "SubnetMapping"}, "Description": {"shape": "Description", "documentation": "<p>A description of the VPC endpoint association. </p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The key:value pairs to associate with the resource.</p>"}}, "documentation": "<p>A VPC endpoint association defines a single subnet to use for a firewall endpoint for a <code>Firewall</code>. You can define VPC endpoint associations only in the Availability Zones that already have a subnet mapping defined in the <code>Firewall</code> resource. </p> <note> <p>You can retrieve the list of Availability Zones that are available for use by calling <code>DescribeFirewallMetadata</code>.</p> </note> <p>To manage firewall endpoints, first, in the <code>Firewall</code> specification, you specify a single VPC and one subnet for each of the Availability Zones where you want to use the firewall. Then you can define additional endpoints as VPC endpoint associations. </p> <p>You can use VPC endpoint associations to expand the protections of the firewall as follows: </p> <ul> <li> <p> <b>Protect multiple VPCs with a single firewall</b> - You can use the firewall to protect other VPCs, either in your account or in accounts where the firewall is shared. You can only specify Availability Zones that already have a firewall endpoint defined in the <code>Firewall</code> subnet mappings.</p> </li> <li> <p> <b>Define multiple firewall endpoints for a VPC in an Availability Zone</b> - You can create additional firewall endpoints for the VPC that you have defined in the firewall, in any Availability Zone that already has an endpoint defined in the <code>Firewall</code> subnet mappings. You can create multiple VPC endpoint associations for any other VPC where you use the firewall.</p> </li> </ul> <p>You can use Resource Access Manager to share a <code>Firewall</code> that you own with other accounts, which gives them the ability to use the firewall to create VPC endpoint associations. For information about sharing a firewall, see <code>PutResourcePolicy</code> in this guide and see <a href=\"https://docs.aws.amazon.com/network-firewall/latest/developerguide/sharing.html\">Sharing Network Firewall resources</a> in the <i>Network Firewall Developer Guide</i>.</p> <p>The status of the VPC endpoint association, which indicates whether it's ready to filter network traffic, is provided in the corresponding <a>VpcEndpointAssociationStatus</a>. You can retrieve both the association and its status by calling <a>DescribeVpcEndpointAssociation</a>.</p>"}, "VpcEndpointAssociationMetadata": {"type": "structure", "members": {"VpcEndpointAssociationArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of a VPC endpoint association.</p>"}}, "documentation": "<p>High-level information about a VPC endpoint association, returned by <code>ListVpcEndpointAssociations</code>. You can use the information provided in the metadata to retrieve and manage a VPC endpoint association.</p>"}, "VpcEndpointAssociationStatus": {"type": "structure", "required": ["Status"], "members": {"Status": {"shape": "FirewallStatusValue", "documentation": "<p>The readiness of the configured firewall endpoint to handle network traffic. </p>"}, "AssociationSyncState": {"shape": "AssociationSyncState", "documentation": "<p>The list of the Availability Zone sync states for all subnets that are defined by the firewall. </p>"}}, "documentation": "<p>Detailed information about the current status of a <a>VpcEndpointAssociation</a>. You can retrieve this by calling <a>DescribeVpcEndpointAssociation</a> and providing the VPC endpoint association ARN.</p>"}, "VpcEndpointAssociations": {"type": "list", "member": {"shape": "VpcEndpointAssociationMetadata"}}, "VpcEndpointId": {"type": "string", "max": 256, "min": 5, "pattern": "^vpce-[a-zA-Z0-9]*$"}, "VpcId": {"type": "string", "max": 128, "min": 1, "pattern": "^vpc-[0-9a-f]+$"}, "VpcIds": {"type": "list", "member": {"shape": "VpcId"}}}, "documentation": "<p>This is the API Reference for Network Firewall. This guide is for developers who need detailed information about the Network Firewall API actions, data types, and errors. </p> <p>The REST API requires you to handle connection details, such as calculating signatures, handling request retries, and error handling. For general information about using the Amazon Web Services REST APIs, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-apis.html\">Amazon Web Services APIs</a>. </p> <p>To view the complete list of Amazon Web Services Regions where Network Firewall is available, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/network-firewall.html\">Service endpoints and quotas</a> in the <i>Amazon Web Services General Reference</i>. </p> <p>To access Network Firewall using the IPv4 REST API endpoint: <code>https://network-firewall.&lt;region&gt;.amazonaws.com </code> </p> <p>To access Network Firewall using the Dualstack (IPv4 and IPv6) REST API endpoint: <code>https://network-firewall.&lt;region&gt;.aws.api </code> </p> <p>Alternatively, you can use one of the Amazon Web Services SDKs to access an API that's tailored to the programming language or platform that you're using. For more information, see <a href=\"http://aws.amazon.com/tools/#SDKs\">Amazon Web Services SDKs</a>.</p> <p>For descriptions of Network Firewall features, including and step-by-step instructions on how to use them through the Network Firewall console, see the <a href=\"https://docs.aws.amazon.com/network-firewall/latest/developerguide/\">Network Firewall Developer Guide</a>.</p> <p>Network Firewall is a stateful, managed, network firewall and intrusion detection and prevention service for Amazon Virtual Private Cloud (Amazon VPC). With Network Firewall, you can filter traffic at the perimeter of your VPC. This includes filtering traffic going to and coming from an internet gateway, NAT gateway, or over VPN or Direct Connect. Network Firewall uses rules that are compatible with Suricata, a free, open source network analysis and threat detection engine. Network Firewall supports Suricata version 7.0.3. For information about Suricata, see the <a href=\"https://suricata.io/\">Suricata website</a> and the <a href=\"https://suricata.readthedocs.io/en/suricata-7.0.3/\">Suricata User Guide</a>. </p> <p>You can use Network Firewall to monitor and protect your VPC traffic in a number of ways. The following are just a few examples: </p> <ul> <li> <p>Allow domains or IP addresses for known Amazon Web Services service endpoints, such as Amazon S3, and block all other forms of traffic.</p> </li> <li> <p>Use custom lists of known bad domains to limit the types of domain names that your applications can access.</p> </li> <li> <p>Perform deep packet inspection on traffic entering or leaving your VPC.</p> </li> <li> <p>Use stateful protocol detection to filter protocols like HTTPS, regardless of the port used.</p> </li> </ul> <p>To enable Network Firewall for your VPCs, you perform steps in both Amazon VPC and in Network Firewall. For information about using Amazon VPC, see <a href=\"https://docs.aws.amazon.com/vpc/latest/userguide/\">Amazon VPC User Guide</a>.</p> <p>To start using Network Firewall, do the following: </p> <ol> <li> <p>(Optional) If you don't already have a VPC that you want to protect, create it in Amazon VPC. </p> </li> <li> <p>In Amazon VPC, in each Availability Zone where you want to have a firewall endpoint, create a subnet for the sole use of Network Firewall. </p> </li> <li> <p>In Network Firewall, define the firewall behavior as follows: </p> <ol> <li> <p>Create stateless and stateful rule groups, to define the components of the network traffic filtering behavior that you want your firewall to have. </p> </li> <li> <p>Create a firewall policy that uses your rule groups and specifies additional default traffic filtering behavior. </p> </li> </ol> </li> <li> <p>In Network Firewall, create a firewall and specify your new firewall policy and VPC subnets. Network Firewall creates a firewall endpoint in each subnet that you specify, with the behavior that's defined in the firewall policy.</p> </li> <li> <p>In Amazon VPC, use ingress routing enhancements to route traffic through the new firewall endpoints.</p> </li> </ol> <p>After your firewall is established, you can add firewall endpoints for new Availability Zones by following the prior steps for the Amazon VPC setup and firewall subnet definitions. You can also add endpoints to Availability Zones that you're using in the firewall, either for the same VPC or for another VPC, by following the prior steps for the Amazon VPC setup, and defining the new VPC subnets as VPC endpoint associations. </p>"}