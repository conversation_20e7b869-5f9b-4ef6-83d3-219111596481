{"pagination": {"ListFirewallPolicies": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "FirewallPolicies"}, "ListFirewalls": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Firewalls"}, "ListRuleGroups": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "RuleGroups"}, "ListTagsForResource": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Tags"}, "ListTLSInspectionConfigurations": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "TLSInspectionConfigurations"}, "GetAnalysisReportResults": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "AnalysisReportResults"}, "ListAnalysisReports": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "AnalysisReports"}, "ListFlowOperationResults": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Flows"}, "ListFlowOperations": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "FlowOperations"}, "ListVpcEndpointAssociations": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "VpcEndpointAssociations"}}}