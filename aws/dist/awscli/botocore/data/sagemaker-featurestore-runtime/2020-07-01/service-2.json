{"version": "2.0", "metadata": {"apiVersion": "2020-07-01", "endpointPrefix": "featurestore-runtime.sagemaker", "jsonVersion": "1.1", "protocol": "rest-json", "serviceFullName": "Amazon SageMaker Feature Store Runtime", "serviceId": "SageMaker FeatureStore Runtime", "signatureVersion": "v4", "signingName": "sagemaker", "uid": "sagemaker-featurestore-runtime-2020-07-01"}, "operations": {"BatchGetRecord": {"name": "BatchGetRecord", "http": {"method": "POST", "requestUri": "/BatchGetRecord"}, "input": {"shape": "BatchGetRecordRequest"}, "output": {"shape": "BatchGetRecordResponse"}, "errors": [{"shape": "ValidationError"}, {"shape": "InternalFailure"}, {"shape": "ServiceUnavailable"}, {"shape": "AccessForbidden"}], "documentation": "<p>Retrieves a batch of <code>Records</code> from a <code>FeatureGroup</code>.</p>"}, "DeleteRecord": {"name": "DeleteRecord", "http": {"method": "DELETE", "requestUri": "/FeatureGroup/{FeatureGroupName}"}, "input": {"shape": "DeleteRecordRequest"}, "errors": [{"shape": "ValidationError"}, {"shape": "InternalFailure"}, {"shape": "ServiceUnavailable"}, {"shape": "AccessForbidden"}], "documentation": "<p>Deletes a <code>Record</code> from a <code>FeatureGroup</code> in the <code>OnlineStore</code>. Feature Store supports both <code>SoftDelete</code> and <code>HardDelete</code>. For <code>SoftDelete</code> (default), feature columns are set to <code>null</code> and the record is no longer retrievable by <code>GetRecord</code> or <code>BatchGetRecord</code>. For <code>HardDelete</code>, the complete <code>Record</code> is removed from the <code>OnlineStore</code>. In both cases, Feature Store appends the deleted record marker to the <code>OfflineStore</code>. The deleted record marker is a record with the same <code>RecordIdentifer</code> as the original, but with <code>is_deleted</code> value set to <code>True</code>, <code>EventTime</code> set to the delete input <code>EventTime</code>, and other feature values set to <code>null</code>.</p> <p>Note that the <code>EventTime</code> specified in <code>DeleteRecord</code> should be set later than the <code>EventTime</code> of the existing record in the <code>OnlineStore</code> for that <code>RecordIdentifer</code>. If it is not, the deletion does not occur:</p> <ul> <li> <p>For <code>SoftDelete</code>, the existing (not deleted) record remains in the <code>OnlineStore</code>, though the delete record marker is still written to the <code>OfflineStore</code>.</p> </li> <li> <p> <code>HardDelete</code> returns <code>EventTime</code>: <code>400 ValidationException</code> to indicate that the delete operation failed. No delete record marker is written to the <code>OfflineStore</code>.</p> </li> </ul> <p>When a record is deleted from the <code>OnlineStore</code>, the deleted record marker is appended to the <code>OfflineStore</code>. If you have the Iceberg table format enabled for your <code>OfflineStore</code>, you can remove all history of a record from the <code>OfflineStore</code> using Amazon Athena or Apache Spark. For information on how to hard delete a record from the <code>OfflineStore</code> with the Iceberg table format enabled, see <a href=\"https://docs.aws.amazon.com/sagemaker/latest/dg/feature-store-delete-records-offline-store.html#feature-store-delete-records-offline-store\">Delete records from the offline store</a>.</p>"}, "GetRecord": {"name": "GetRecord", "http": {"method": "GET", "requestUri": "/FeatureGroup/{FeatureGroupName}"}, "input": {"shape": "GetRecordRequest"}, "output": {"shape": "GetRecordResponse"}, "errors": [{"shape": "ValidationError"}, {"shape": "ResourceNotFound"}, {"shape": "InternalFailure"}, {"shape": "ServiceUnavailable"}, {"shape": "AccessForbidden"}], "documentation": "<p>Use for <code>OnlineStore</code> serving from a <code>FeatureStore</code>. Only the latest records stored in the <code>OnlineStore</code> can be retrieved. If no Record with <code>RecordIdentifierValue</code> is found, then an empty result is returned. </p>"}, "PutRecord": {"name": "PutRecord", "http": {"method": "PUT", "requestUri": "/FeatureGroup/{FeatureGroupName}"}, "input": {"shape": "PutRecordRequest"}, "errors": [{"shape": "ValidationError"}, {"shape": "InternalFailure"}, {"shape": "ServiceUnavailable"}, {"shape": "AccessForbidden"}], "documentation": "<p>The <code>PutRecord</code> API is used to ingest a list of <code>Records</code> into your feature group. </p> <p>If a new record’s <code>EventTime</code> is greater, the new record is written to both the <code>OnlineStore</code> and <code>OfflineStore</code>. Otherwise, the record is a historic record and it is written only to the <code>OfflineStore</code>. </p> <p>You can specify the ingestion to be applied to the <code>OnlineStore</code>, <code>OfflineStore</code>, or both by using the <code>TargetStores</code> request parameter. </p> <p>You can set the ingested record to expire at a given time to live (TTL) duration after the record’s event time, <code>ExpiresAt</code> = <code>EventTime</code> + <code>TtlDuration</code>, by specifying the <code>TtlDuration</code> parameter. A record level <code>TtlDuration</code> is set when specifying the <code>TtlDuration</code> parameter using the <code>PutRecord</code> API call. If the input <code>TtlDuration</code> is <code>null</code> or unspecified, <code>TtlDuration</code> is set to the default feature group level <code>TtlDuration</code>. A record level <code>TtlDuration</code> supersedes the group level <code>TtlDuration</code>.</p>"}}, "shapes": {"AccessForbidden": {"type": "structure", "members": {"Message": {"shape": "Message"}}, "documentation": "<p>You do not have permission to perform an action.</p>", "error": {"httpStatusCode": 403}, "exception": true, "synthetic": true}, "BatchGetRecordError": {"type": "structure", "required": ["FeatureGroupName", "RecordIdentifierValueAsString", "ErrorCode", "ErrorMessage"], "members": {"FeatureGroupName": {"shape": "ValueAsString", "documentation": "<p>The name of the feature group that the record belongs to.</p>"}, "RecordIdentifierValueAsString": {"shape": "ValueAsString", "documentation": "<p>The value for the <code>RecordIdentifier</code> in string format of a Record from a <code>FeatureGroup</code> that is causing an error when attempting to be retrieved.</p>"}, "ErrorCode": {"shape": "ValueAsString", "documentation": "<p>The error code of an error that has occurred when attempting to retrieve a batch of Records. For more information on errors, see <a href=\"https://docs.aws.amazon.com/sagemaker/latest/APIReference/API_feature_store_GetRecord.html#API_feature_store_GetRecord_Errors\">Errors</a>.</p>"}, "ErrorMessage": {"shape": "Message", "documentation": "<p>The error message of an error that has occurred when attempting to retrieve a record in the batch.</p>"}}, "documentation": "<p>The error that has occurred when attempting to retrieve a batch of Records.</p>"}, "BatchGetRecordErrors": {"type": "list", "member": {"shape": "BatchGetRecordError"}, "min": 0}, "BatchGetRecordIdentifier": {"type": "structure", "required": ["FeatureGroupName", "RecordIdentifiersValueAsString"], "members": {"FeatureGroupName": {"shape": "FeatureGroupNameOrArn", "documentation": "<p>The name or Amazon Resource Name (ARN) of the <code>FeatureGroup</code> containing the records you are retrieving in a batch.</p>"}, "RecordIdentifiersValueAsString": {"shape": "RecordIdentifiers", "documentation": "<p>The value for a list of record identifiers in string format.</p>"}, "FeatureNames": {"shape": "FeatureNames", "documentation": "<p>List of names of Features to be retrieved. If not specified, the latest value for all the Features are returned.</p>"}}, "documentation": "<p>The identifier that identifies the batch of Records you are retrieving in a batch.</p>"}, "BatchGetRecordIdentifiers": {"type": "list", "member": {"shape": "BatchGetRecordIdentifier"}, "max": 100, "min": 1}, "BatchGetRecordRequest": {"type": "structure", "required": ["Identifiers"], "members": {"Identifiers": {"shape": "BatchGetRecordIdentifiers", "documentation": "<p>A list containing the name or Amazon Resource Name (ARN) of the <code>FeatureGroup</code>, the list of names of <code>Feature</code>s to be retrieved, and the corresponding <code>RecordIdentifier</code> values as strings.</p>"}, "ExpirationTimeResponse": {"shape": "ExpirationTimeResponse", "documentation": "<p>Parameter to request <code>ExpiresAt</code> in response. If <code>Enabled</code>, <code>BatchGetRecord</code> will return the value of <code>ExpiresAt</code>, if it is not null. If <code>Disabled</code> and null, <code>BatchGetRecord</code> will return null.</p>"}}}, "BatchGetRecordResponse": {"type": "structure", "required": ["Records", "Errors", "UnprocessedIdentifiers"], "members": {"Records": {"shape": "BatchGetRecordResultDetails", "documentation": "<p>A list of Records you requested to be retrieved in batch.</p>"}, "Errors": {"shape": "BatchGetRecordErrors", "documentation": "<p>A list of errors that have occurred when retrieving a batch of Records.</p>"}, "UnprocessedIdentifiers": {"shape": "UnprocessedIdentifiers", "documentation": "<p>A unprocessed list of <code>FeatureGroup</code> names, with their corresponding <code>RecordIdentifier</code> value, and Feature name.</p>"}}}, "BatchGetRecordResultDetail": {"type": "structure", "required": ["FeatureGroupName", "RecordIdentifierValueAsString", "Record"], "members": {"FeatureGroupName": {"shape": "ValueAsString", "documentation": "<p>The <code>FeatureGroupName</code> containing Records you retrieved in a batch.</p>"}, "RecordIdentifierValueAsString": {"shape": "ValueAsString", "documentation": "<p>The value of the record identifier in string format.</p>"}, "Record": {"shape": "Record", "documentation": "<p>The <code>Record</code> retrieved.</p>"}, "ExpiresAt": {"shape": "ExpiresAt", "documentation": "<p>The <code>ExpiresAt</code> ISO string of the requested record.</p>"}}, "documentation": "<p>The output of records that have been retrieved in a batch.</p>"}, "BatchGetRecordResultDetails": {"type": "list", "member": {"shape": "BatchGetRecordResultDetail"}, "min": 0}, "DeleteRecordRequest": {"type": "structure", "required": ["FeatureGroupName", "RecordIdentifierValueAsString", "EventTime"], "members": {"FeatureGroupName": {"shape": "FeatureGroupNameOrArn", "documentation": "<p>The name or Amazon Resource Name (ARN) of the feature group to delete the record from. </p>", "location": "uri", "locationName": "FeatureGroupName"}, "RecordIdentifierValueAsString": {"shape": "ValueAsString", "documentation": "<p>The value for the <code>RecordIdentifier</code> that uniquely identifies the record, in string format. </p>", "location": "querystring", "locationName": "RecordIdentifierValueAsString"}, "EventTime": {"shape": "ValueAsString", "documentation": "<p>Timestamp indicating when the deletion event occurred. <code>EventTime</code> can be used to query data at a certain point in time.</p>", "location": "querystring", "locationName": "EventTime"}, "TargetStores": {"shape": "TargetStores", "documentation": "<p>A list of stores from which you're deleting the record. By default, Feature Store deletes the record from all of the stores that you're using for the <code>FeatureGroup</code>.</p>", "location": "querystring", "locationName": "TargetStores"}, "DeletionMode": {"shape": "DeletionMode", "documentation": "<p>The name of the deletion mode for deleting the record. By default, the deletion mode is set to <code>SoftDelete</code>.</p>", "location": "querystring", "locationName": "DeletionMode"}}}, "DeletionMode": {"type": "string", "enum": ["SoftDelete", "HardDelete"]}, "ExpirationTimeResponse": {"type": "string", "enum": ["Enabled", "Disabled"]}, "ExpiresAt": {"type": "string"}, "FeatureGroupNameOrArn": {"type": "string", "max": 150, "min": 1, "pattern": "(arn:aws[a-z\\-]*:sagemaker:[a-z0-9\\-]*:[0-9]{12}:feature-group/)?([a-zA-Z0-9]([-_]*[a-zA-Z0-9]){0,63})"}, "FeatureName": {"type": "string", "max": 64, "min": 1, "pattern": "^[a-zA-Z0-9]([-_]*[a-zA-Z0-9]){0,63}"}, "FeatureNames": {"type": "list", "member": {"shape": "FeatureName"}, "min": 1}, "FeatureValue": {"type": "structure", "required": ["FeatureName"], "members": {"FeatureName": {"shape": "FeatureName", "documentation": "<p>The name of a feature that a feature value corresponds to.</p>"}, "ValueAsString": {"shape": "ValueAsString", "documentation": "<p>The value in string format associated with a feature. Used when your <code>CollectionType</code> is <code>None</code>. Note that features types can be <code>String</code>, <code>Integral</code>, or <code>Fractional</code>. This value represents all three types as a string.</p>"}, "ValueAsStringList": {"shape": "ValueAsStringList", "documentation": "<p>The list of values in string format associated with a feature. Used when your <code>CollectionType</code> is a <code>List</code>, <code>Set</code>, or <code>Vector</code>. Note that features types can be <code>String</code>, <code>Integral</code>, or <code>Fractional</code>. These values represents all three types as a string.</p>"}}, "documentation": "<p>The value associated with a feature.</p>"}, "GetRecordRequest": {"type": "structure", "required": ["FeatureGroupName", "RecordIdentifierValueAsString"], "members": {"FeatureGroupName": {"shape": "FeatureGroupNameOrArn", "documentation": "<p>The name or Amazon Resource Name (ARN) of the feature group from which you want to retrieve a record.</p>", "location": "uri", "locationName": "FeatureGroupName"}, "RecordIdentifierValueAsString": {"shape": "ValueAsString", "documentation": "<p>The value that corresponds to <code>RecordIdentifier</code> type and uniquely identifies the record in the <code>FeatureGroup</code>. </p>", "location": "querystring", "locationName": "RecordIdentifierValueAsString"}, "FeatureNames": {"shape": "FeatureNames", "documentation": "<p>List of names of Features to be retrieved. If not specified, the latest value for all the Features are returned.</p>", "location": "querystring", "locationName": "FeatureName"}, "ExpirationTimeResponse": {"shape": "ExpirationTimeResponse", "documentation": "<p>Parameter to request <code>ExpiresAt</code> in response. If <code>Enabled</code>, <code>GetRecord</code> will return the value of <code>ExpiresAt</code>, if it is not null. If <code>Disabled</code> and null, <code>GetRecord</code> will return null.</p>", "location": "querystring", "locationName": "ExpirationTimeResponse"}}}, "GetRecordResponse": {"type": "structure", "members": {"Record": {"shape": "Record", "documentation": "<p>The record you requested. A list of <code>FeatureValues</code>.</p>"}, "ExpiresAt": {"shape": "ExpiresAt", "documentation": "<p>The <code>ExpiresAt</code> ISO string of the requested record.</p>"}}}, "InternalFailure": {"type": "structure", "members": {"Message": {"shape": "Message"}}, "documentation": "<p>An internal failure occurred. Try your request again. If the problem persists, contact Amazon Web Services customer support.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true, "synthetic": true}, "Message": {"type": "string", "max": 2048}, "PutRecordRequest": {"type": "structure", "required": ["FeatureGroupName", "Record"], "members": {"FeatureGroupName": {"shape": "FeatureGroupNameOrArn", "documentation": "<p>The name or Amazon Resource Name (ARN) of the feature group that you want to insert the record into.</p>", "location": "uri", "locationName": "FeatureGroupName"}, "Record": {"shape": "Record", "documentation": "<p>List of FeatureValues to be inserted. This will be a full over-write. If you only want to update few of the feature values, do the following:</p> <ul> <li> <p>Use <code>GetRecord</code> to retrieve the latest record.</p> </li> <li> <p>Update the record returned from <code>GetRecord</code>. </p> </li> <li> <p>Use <code>PutRecord</code> to update feature values.</p> </li> </ul>"}, "TargetStores": {"shape": "TargetStores", "documentation": "<p>A list of stores to which you're adding the record. By default, Feature Store adds the record to all of the stores that you're using for the <code>FeatureGroup</code>.</p>"}, "TtlDuration": {"shape": "TtlDuration", "documentation": "<p>Time to live duration, where the record is hard deleted after the expiration time is reached; <code>ExpiresAt</code> = <code>EventTime</code> + <code>TtlDuration</code>. For information on HardDelete, see the <a href=\"https://docs.aws.amazon.com/sagemaker/latest/APIReference/API_feature_store_DeleteRecord.html\">DeleteRecord</a> API in the Amazon SageMaker API Reference guide.</p>"}}}, "Record": {"type": "list", "member": {"shape": "FeatureValue"}, "min": 1}, "RecordIdentifiers": {"type": "list", "member": {"shape": "ValueAsString"}, "max": 100, "min": 1}, "ResourceNotFound": {"type": "structure", "members": {"Message": {"shape": "Message"}}, "documentation": "<p>A resource that is required to perform an action was not found.</p>", "error": {"httpStatusCode": 404}, "exception": true}, "ServiceUnavailable": {"type": "structure", "members": {"Message": {"shape": "Message"}}, "documentation": "<p>The service is currently unavailable.</p>", "error": {"httpStatusCode": 503}, "exception": true, "fault": true, "synthetic": true}, "TargetStore": {"type": "string", "enum": ["OnlineStore", "OfflineStore"]}, "TargetStores": {"type": "list", "member": {"shape": "TargetStore"}, "max": 2, "min": 1}, "TtlDuration": {"type": "structure", "required": ["Unit", "Value"], "members": {"Unit": {"shape": "TtlDurationUnit", "documentation": "<p> <code>TtlDuration</code> time unit.</p>"}, "Value": {"shape": "TtlDurationValue", "documentation": "<p> <code>TtlDuration</code> time value.</p>"}}, "documentation": "<p>Time to live duration, where the record is hard deleted after the expiration time is reached; <code>ExpiresAt</code> = <code>EventTime</code> + <code>TtlDuration</code>. For information on HardDelete, see the <a href=\"https://docs.aws.amazon.com/sagemaker/latest/APIReference/API_feature_store_DeleteRecord.html\">DeleteRecord</a> API in the Amazon SageMaker API Reference guide.</p>"}, "TtlDurationUnit": {"type": "string", "enum": ["Seconds", "Minutes", "Hours", "Days", "Weeks"]}, "TtlDurationValue": {"type": "integer", "min": 1}, "UnprocessedIdentifiers": {"type": "list", "member": {"shape": "BatchGetRecordIdentifier"}, "min": 0}, "ValidationError": {"type": "structure", "members": {"Message": {"shape": "Message"}}, "documentation": "<p>There was an error validating your request.</p>", "error": {"httpStatusCode": 400}, "exception": true, "synthetic": true}, "ValueAsString": {"type": "string", "max": 358400, "pattern": ".*"}, "ValueAsStringList": {"type": "list", "member": {"shape": "ValueAsString"}, "max": 358400, "min": 0}}, "documentation": "<p>Contains all data plane API operations and data types for the Amazon SageMaker Feature Store. Use this API to put, delete, and retrieve (get) features from a feature store.</p> <p>Use the following operations to configure your <code>OnlineStore</code> and <code>OfflineStore</code> features, and to create and manage feature groups:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/sagemaker/latest/APIReference/API_CreateFeatureGroup.html\">CreateFeatureGroup</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/sagemaker/latest/APIReference/API_DeleteFeatureGroup.html\">DeleteFeatureGroup</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/sagemaker/latest/APIReference/API_DescribeFeatureGroup.html\">DescribeFeatureGroup</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/sagemaker/latest/APIReference/API_ListFeatureGroups.html\">ListFeatureGroups</a> </p> </li> </ul>"}