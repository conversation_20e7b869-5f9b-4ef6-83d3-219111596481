{"version": "2.0", "metadata": {"apiVersion": "2017-05-18", "endpointPrefix": "athena", "jsonVersion": "1.1", "protocol": "json", "protocols": ["json"], "serviceFullName": "Amazon Athena", "serviceId": "Athena", "signatureVersion": "v4", "targetPrefix": "AmazonAthena", "uid": "athena-2017-05-18", "auth": ["aws.auth#sigv4"]}, "operations": {"BatchGetNamedQuery": {"name": "BatchGetNamedQuery", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "BatchGetNamedQueryInput"}, "output": {"shape": "BatchGetNamedQueryOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Returns the details of a single named query or a list of up to 50 queries, which you provide as an array of query ID strings. Requires you to have access to the workgroup in which the queries were saved. Use <a>ListNamedQueriesInput</a> to get the list of named query IDs in the specified workgroup. If information could not be retrieved for a submitted query ID, information about the query ID submitted is listed under <a>UnprocessedNamedQueryId</a>. Named queries differ from executed queries. Use <a>BatchGetQueryExecutionInput</a> to get details about each unique query execution, and <a>ListQueryExecutionsInput</a> to get a list of query execution IDs.</p>"}, "BatchGetPreparedStatement": {"name": "BatchGetPreparedStatement", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "BatchGetPreparedStatementInput"}, "output": {"shape": "BatchGetPreparedStatementOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Returns the details of a single prepared statement or a list of up to 256 prepared statements for the array of prepared statement names that you provide. Requires you to have access to the workgroup to which the prepared statements belong. If a prepared statement cannot be retrieved for the name specified, the statement is listed in <code>UnprocessedPreparedStatementNames</code>.</p>"}, "BatchGetQueryExecution": {"name": "BatchGetQueryExecution", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "BatchGetQueryExecutionInput"}, "output": {"shape": "BatchGetQueryExecutionOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Returns the details of a single query execution or a list of up to 50 query executions, which you provide as an array of query execution ID strings. Requires you to have access to the workgroup in which the queries ran. To get a list of query execution IDs, use <a>ListQueryExecutionsInput$WorkGroup</a>. Query executions differ from named (saved) queries. Use <a>BatchGetNamedQueryInput</a> to get details about named queries.</p>"}, "CancelCapacityReservation": {"name": "CancelCapacityReservation", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CancelCapacityReservationInput"}, "output": {"shape": "CancelCapacityReservationOutput"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServerException"}], "documentation": "<p>Cancels the capacity reservation with the specified name. Cancelled reservations remain in your account and will be deleted 45 days after cancellation. During the 45 days, you cannot re-purpose or reuse a reservation that has been cancelled, but you can refer to its tags and view it for historical reference. </p>", "idempotent": true}, "CreateCapacityReservation": {"name": "CreateCapacityReservation", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateCapacityReservationInput"}, "output": {"shape": "CreateCapacityReservationOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Creates a capacity reservation with the specified name and number of requested data processing units.</p>", "idempotent": true}, "CreateDataCatalog": {"name": "CreateDataCatalog", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateDataCatalogInput"}, "output": {"shape": "CreateDataCatalogOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Creates (registers) a data catalog with the specified name and properties. Catalogs created are visible to all users of the same Amazon Web Services account.</p> <p>For a <code>FEDERATED</code> catalog, this API operation creates the following resources.</p> <ul> <li> <p>CFN Stack Name with a maximum length of 128 characters and prefix <code>athenafederatedcatalog-CATALOG_NAME_SANITIZED</code> with length 23 characters.</p> </li> <li> <p>Lambda Function Name with a maximum length of 64 characters and prefix <code>athenafederatedcatalog_CATALOG_NAME_SANITIZED</code> with length 23 characters.</p> </li> <li> <p>Glue Connection Name with a maximum length of 255 characters and a prefix <code>athenafederatedcatalog_CATALOG_NAME_SANITIZED</code> with length 23 characters. </p> </li> </ul>"}, "CreateNamedQuery": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateNamedQueryInput"}, "output": {"shape": "CreateNamedQueryOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Creates a named query in the specified workgroup. Requires that you have access to the workgroup.</p>", "idempotent": true}, "CreateNotebook": {"name": "CreateNotebook", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateNotebookInput"}, "output": {"shape": "CreateNotebookOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Creates an empty <code>ipynb</code> file in the specified Apache Spark enabled workgroup. Throws an error if a file in the workgroup with the same name already exists.</p>"}, "CreatePreparedStatement": {"name": "CreatePreparedStatement", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreatePreparedStatementInput"}, "output": {"shape": "CreatePreparedStatementOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Creates a prepared statement for use with SQL queries in Athena.</p>"}, "CreatePresignedNotebookUrl": {"name": "CreatePresignedNotebookUrl", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreatePresignedNotebookUrlRequest"}, "output": {"shape": "CreatePresignedNotebookUrlResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Gets an authentication token and the URL at which the notebook can be accessed. During programmatic access, <code>CreatePresignedNotebookUrl</code> must be called every 10 minutes to refresh the authentication token. For information about granting programmatic access, see <a href=\"https://docs.aws.amazon.com/athena/latest/ug/setting-up.html#setting-up-grant-programmatic-access\">Grant programmatic access</a>.</p>"}, "CreateWorkGroup": {"name": "CreateWorkGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateWorkGroupInput"}, "output": {"shape": "CreateWorkGroupOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Creates a workgroup with the specified name. A workgroup can be an Apache Spark enabled workgroup or an Athena SQL workgroup.</p>"}, "DeleteCapacityReservation": {"name": "DeleteCapacityReservation", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteCapacityReservationInput"}, "output": {"shape": "DeleteCapacityReservationOutput"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes a cancelled capacity reservation. A reservation must be cancelled before it can be deleted. A deleted reservation is immediately removed from your account and can no longer be referenced, including by its ARN. A deleted reservation cannot be called by <code>GetCapacityReservation</code>, and deleted reservations do not appear in the output of <code>ListCapacityReservations</code>.</p>", "idempotent": true}, "DeleteDataCatalog": {"name": "DeleteDataCatalog", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteDataCatalogInput"}, "output": {"shape": "DeleteDataCatalogOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Deletes a data catalog.</p>"}, "DeleteNamedQuery": {"name": "DeleteNamed<PERSON><PERSON><PERSON>", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteNamedQueryInput"}, "output": {"shape": "DeleteNamedQueryOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Deletes the named query if you have access to the workgroup in which the query was saved.</p>", "idempotent": true}, "DeleteNotebook": {"name": "DeleteNotebook", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteNotebookInput"}, "output": {"shape": "DeleteNotebookOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Deletes the specified notebook.</p>"}, "DeletePreparedStatement": {"name": "DeletePreparedStatement", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeletePreparedStatementInput"}, "output": {"shape": "DeletePreparedStatementOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Deletes the prepared statement with the specified name from the specified workgroup.</p>"}, "DeleteWorkGroup": {"name": "DeleteWorkGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteWorkGroupInput"}, "output": {"shape": "DeleteWorkGroupOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Deletes the workgroup with the specified name. The primary workgroup cannot be deleted.</p>", "idempotent": true}, "ExportNotebook": {"name": "ExportNotebook", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ExportNotebookInput"}, "output": {"shape": "ExportNotebookOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Exports the specified notebook and its metadata.</p>"}, "GetCalculationExecution": {"name": "GetCalculationExecution", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetCalculationExecutionRequest"}, "output": {"shape": "GetCalculationExecutionResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Describes a previously submitted calculation execution.</p>"}, "GetCalculationExecutionCode": {"name": "GetCalculationExecutionCode", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetCalculationExecutionCodeRequest"}, "output": {"shape": "GetCalculationExecutionCodeResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves the unencrypted code that was executed for the calculation.</p>"}, "GetCalculationExecutionStatus": {"name": "GetCalculationExecutionStatus", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetCalculationExecutionStatusRequest"}, "output": {"shape": "GetCalculationExecutionStatusResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Gets the status of a current calculation.</p>"}, "GetCapacityAssignmentConfiguration": {"name": "GetCapacityAssignmentConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetCapacityAssignmentConfigurationInput"}, "output": {"shape": "GetCapacityAssignmentConfigurationOutput"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServerException"}], "documentation": "<p>Gets the capacity assignment configuration for a capacity reservation, if one exists.</p>"}, "GetCapacityReservation": {"name": "GetCapacityReservation", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetCapacityReservationInput"}, "output": {"shape": "GetCapacityReservationOutput"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServerException"}], "documentation": "<p>Returns information about the capacity reservation with the specified name.</p>"}, "GetDataCatalog": {"name": "GetDataCatalog", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetDataCatalogInput"}, "output": {"shape": "GetDataCatalogOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Returns the specified data catalog.</p>"}, "GetDatabase": {"name": "GetDatabase", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetDatabaseInput"}, "output": {"shape": "GetDatabaseOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}, {"shape": "MetadataException"}], "documentation": "<p>Returns a database object for the specified database and data catalog.</p>"}, "GetNamedQuery": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetNamedQueryInput"}, "output": {"shape": "GetNamedQueryOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Returns information about a single query. Requires that you have access to the workgroup in which the query was saved.</p>"}, "GetNotebookMetadata": {"name": "GetNotebookMetadata", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetNotebookMetadataInput"}, "output": {"shape": "GetNotebookMetadataOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Retrieves notebook metadata for the specified notebook ID.</p>"}, "GetPreparedStatement": {"name": "GetPreparedStatement", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetPreparedStatementInput"}, "output": {"shape": "GetPreparedStatementOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves the prepared statement with the specified name from the specified workgroup.</p>"}, "GetQueryExecution": {"name": "GetQueryExecution", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetQueryExecutionInput"}, "output": {"shape": "GetQueryExecutionOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Returns information about a single execution of a query if you have access to the workgroup in which the query ran. Each time a query executes, information about the query execution is saved with a unique ID.</p>"}, "GetQueryResults": {"name": "GetQueryResults", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetQueryResultsInput"}, "output": {"shape": "GetQueryResultsOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Streams the results of a single query execution specified by <code>QueryExecutionId</code> from the Athena query results location in Amazon S3. For more information, see <a href=\"https://docs.aws.amazon.com/athena/latest/ug/querying.html\">Working with query results, recent queries, and output files</a> in the <i>Amazon Athena User Guide</i>. This request does not execute the query but returns results. Use <a>StartQueryExecution</a> to run a query.</p> <p>To stream query results successfully, the IAM principal with permission to call <code>GetQueryResults</code> also must have permissions to the Amazon S3 <code>GetObject</code> action for the Athena query results location.</p> <important> <p>IAM principals with permission to the Amazon S3 <code>GetObject</code> action for the query results location are able to retrieve query results from Amazon S3 even if permission to the <code>GetQueryResults</code> action is denied. To restrict user or role access, ensure that Amazon S3 permissions to the Athena query location are denied.</p> </important>"}, "GetQueryRuntimeStatistics": {"name": "GetQueryRuntimeStatistics", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetQueryRuntimeStatisticsInput"}, "output": {"shape": "GetQueryRuntimeStatisticsOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Returns query execution runtime statistics related to a single execution of a query if you have access to the workgroup in which the query ran. Statistics from the <code>Timeline</code> section of the response object are available as soon as <a>QueryExecutionStatus$State</a> is in a SUCCEEDED or FAILED state. The remaining non-timeline statistics in the response (like stage-level input and output row count and data size) are updated asynchronously and may not be available immediately after a query completes. The non-timeline statistics are also not included when a query has row-level filters defined in Lake Formation.</p>"}, "GetSession": {"name": "GetSession", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetSessionRequest"}, "output": {"shape": "GetSessionResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Gets the full details of a previously created session, including the session status and configuration.</p>"}, "GetSessionStatus": {"name": "GetSessionStatus", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetSessionStatusRequest"}, "output": {"shape": "GetSessionStatusResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Gets the current status of a session.</p>"}, "GetTableMetadata": {"name": "GetTableMetadata", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetTableMetadataInput"}, "output": {"shape": "GetTableMetadataOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}, {"shape": "MetadataException"}], "documentation": "<p>Returns table metadata for the specified catalog, database, and table.</p>"}, "GetWorkGroup": {"name": "GetWorkGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetWorkGroupInput"}, "output": {"shape": "GetWorkGroupOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Returns information about the workgroup with the specified name.</p>"}, "ImportNotebook": {"name": "ImportNotebook", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ImportNotebookInput"}, "output": {"shape": "ImportNotebookOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Imports a single <code>ipynb</code> file to a Spark enabled workgroup. To import the notebook, the request must specify a value for either <code>Payload</code> or <code>NoteBookS3LocationUri</code>. If neither is specified or both are specified, an <code>InvalidRequestException</code> occurs. The maximum file size that can be imported is 10 megabytes. If an <code>ipynb</code> file with the same name already exists in the workgroup, throws an error.</p>"}, "ListApplicationDPUSizes": {"name": "ListApplicationDPUSizes", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListApplicationDPUSizesInput"}, "output": {"shape": "ListApplicationDPUSizesOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Returns the supported DPU sizes for the supported application runtimes (for example, <code>Athena notebook version 1</code>). </p>"}, "ListCalculationExecutions": {"name": "ListCalculationExecutions", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListCalculationExecutionsRequest"}, "output": {"shape": "ListCalculationExecutionsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Lists the calculations that have been submitted to a session in descending order. Newer calculations are listed first; older calculations are listed later.</p>"}, "ListCapacityReservations": {"name": "ListCapacityReservations", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListCapacityReservationsInput"}, "output": {"shape": "ListCapacityReservationsOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Lists the capacity reservations for the current account.</p>"}, "ListDataCatalogs": {"name": "ListDataCatalogs", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListDataCatalogsInput"}, "output": {"shape": "ListDataCatalogsOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Lists the data catalogs in the current Amazon Web Services account.</p> <note> <p>In the Athena console, data catalogs are listed as \"data sources\" on the <b>Data sources</b> page under the <b>Data source name</b> column.</p> </note>"}, "ListDatabases": {"name": "ListDatabases", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListDatabasesInput"}, "output": {"shape": "ListDatabasesOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}, {"shape": "MetadataException"}], "documentation": "<p>Lists the databases in the specified data catalog.</p>"}, "ListEngineVersions": {"name": "ListEngineVersions", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListEngineVersionsInput"}, "output": {"shape": "ListEngineVersionsOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Returns a list of engine versions that are available to choose from, including the Auto option.</p>"}, "ListExecutors": {"name": "ListExecutors", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListExecutorsRequest"}, "output": {"shape": "ListExecutorsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Lists, in descending order, the executors that joined a session. Newer executors are listed first; older executors are listed later. The result can be optionally filtered by state.</p>"}, "ListNamedQueries": {"name": "ListNamedQueries", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListNamedQueriesInput"}, "output": {"shape": "ListNamedQueriesOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Provides a list of available query IDs only for queries saved in the specified workgroup. Requires that you have access to the specified workgroup. If a workgroup is not specified, lists the saved queries for the primary workgroup.</p>"}, "ListNotebookMetadata": {"name": "ListNotebookMetadata", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListNotebookMetadataInput"}, "output": {"shape": "ListNotebookMetadataOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Displays the notebook files for the specified workgroup in paginated format.</p>"}, "ListNotebookSessions": {"name": "ListNotebookSessions", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListNotebookSessionsRequest"}, "output": {"shape": "ListNotebookSessionsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Lists, in descending order, the sessions that have been created in a notebook that are in an active state like <code>CREATING</code>, <code>CREATED</code>, <code>IDLE</code> or <code>BUSY</code>. Newer sessions are listed first; older sessions are listed later.</p>"}, "ListPreparedStatements": {"name": "ListPreparedStatements", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListPreparedStatementsInput"}, "output": {"shape": "ListPreparedStatementsOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Lists the prepared statements in the specified workgroup.</p>"}, "ListQueryExecutions": {"name": "ListQueryExecutions", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListQueryExecutionsInput"}, "output": {"shape": "ListQueryExecutionsOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Provides a list of available query execution IDs for the queries in the specified workgroup. Athena keeps a query history for 45 days. If a workgroup is not specified, returns a list of query execution IDs for the primary workgroup. Requires you to have access to the workgroup in which the queries ran.</p>"}, "ListSessions": {"name": "ListSessions", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListSessionsRequest"}, "output": {"shape": "ListSessionsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Lists the sessions in a workgroup that are in an active state like <code>CREATING</code>, <code>CREATED</code>, <code>IDLE</code>, or <code>BUSY</code>. Newer sessions are listed first; older sessions are listed later.</p>"}, "ListTableMetadata": {"name": "ListTableMetadata", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTableMetadataInput"}, "output": {"shape": "ListTableMetadataOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}, {"shape": "MetadataException"}], "documentation": "<p>Lists the metadata for the tables in the specified data catalog database.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTagsForResourceInput"}, "output": {"shape": "ListTagsForResourceOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Lists the tags associated with an Athena resource.</p>"}, "ListWorkGroups": {"name": "ListWorkGroups", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListWorkGroupsInput"}, "output": {"shape": "ListWorkGroupsOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Lists available workgroups for the account.</p>"}, "PutCapacityAssignmentConfiguration": {"name": "PutCapacityAssignmentConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutCapacityAssignmentConfigurationInput"}, "output": {"shape": "PutCapacityAssignmentConfigurationOutput"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServerException"}], "documentation": "<p>Puts a new capacity assignment configuration for a specified capacity reservation. If a capacity assignment configuration already exists for the capacity reservation, replaces the existing capacity assignment configuration.</p>", "idempotent": true}, "StartCalculationExecution": {"name": "StartCalculationExecution", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StartCalculationExecutionRequest"}, "output": {"shape": "StartCalculationExecutionResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Submits calculations for execution within a session. You can supply the code to run as an inline code block within the request.</p> <note> <p>The request syntax requires the <a>StartCalculationExecutionRequest$CodeBlock</a> parameter or the <a>CalculationConfiguration$CodeBlock</a> parameter, but not both. Because <a>CalculationConfiguration$CodeBlock</a> is deprecated, use the <a>StartCalculationExecutionRequest$CodeBlock</a> parameter instead.</p> </note>"}, "StartQueryExecution": {"name": "StartQueryExecution", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StartQueryExecutionInput"}, "output": {"shape": "StartQueryExecutionOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Runs the SQL query statements contained in the <code>Query</code>. Requires you to have access to the workgroup in which the query ran. Running queries against an external catalog requires <a>GetDataCatalog</a> permission to the catalog. For code samples using the Amazon Web Services SDK for Java, see <a href=\"http://docs.aws.amazon.com/athena/latest/ug/code-samples.html\">Examples and Code Samples</a> in the <i>Amazon Athena User Guide</i>.</p>", "idempotent": true}, "StartSession": {"name": "StartSession", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StartSessionRequest"}, "output": {"shape": "StartSessionResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "SessionAlreadyExistsException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Creates a session for running calculations within a workgroup. The session is ready when it reaches an <code>IDLE</code> state.</p>"}, "StopCalculationExecution": {"name": "StopCalculationExecution", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StopCalculationExecutionRequest"}, "output": {"shape": "StopCalculationExecutionResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Requests the cancellation of a calculation. A <code>StopCalculationExecution</code> call on a calculation that is already in a terminal state (for example, <code>STOPPED</code>, <code>FAILED</code>, or <code>COMPLETED</code>) succeeds but has no effect.</p> <note> <p>Cancelling a calculation is done on a best effort basis. If a calculation cannot be cancelled, you can be charged for its completion. If you are concerned about being charged for a calculation that cannot be cancelled, consider terminating the session in which the calculation is running.</p> </note>"}, "StopQueryExecution": {"name": "StopQueryExecution", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StopQueryExecutionInput"}, "output": {"shape": "StopQueryExecutionOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Stops a query execution. Requires you to have access to the workgroup in which the query ran.</p>", "idempotent": true}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "TagResourceInput"}, "output": {"shape": "TagResourceOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Adds one or more tags to an Athena resource. A tag is a label that you assign to a resource. Each tag consists of a key and an optional value, both of which you define. For example, you can use tags to categorize Athena workgroups, data catalogs, or capacity reservations by purpose, owner, or environment. Use a consistent set of tag keys to make it easier to search and filter the resources in your account. For best practices, see <a href=\"https://docs.aws.amazon.com/whitepapers/latest/tagging-best-practices/tagging-best-practices.html\">Tagging Best Practices</a>. Tag keys can be from 1 to 128 UTF-8 Unicode characters, and tag values can be from 0 to 256 UTF-8 Unicode characters. Tags can use letters and numbers representable in UTF-8, and the following characters: + - = . _ : / @. Tag keys and values are case-sensitive. Tag keys must be unique per resource. If you specify more than one tag, separate them by commas.</p>"}, "TerminateSession": {"name": "TerminateSession", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "TerminateSessionRequest"}, "output": {"shape": "TerminateSessionResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Terminates an active session. A <code>TerminateSession</code> call on a session that is already inactive (for example, in a <code>FAILED</code>, <code>TERMINATED</code> or <code>TERMINATING</code> state) succeeds but has no effect. Calculations running in the session when <code>TerminateSession</code> is called are forcefully stopped, but may display as <code>FAILED</code> instead of <code>STOPPED</code>.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UntagResourceInput"}, "output": {"shape": "UntagResourceOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Removes one or more tags from an Athena resource.</p>"}, "UpdateCapacityReservation": {"name": "UpdateCapacityReservation", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateCapacityReservationInput"}, "output": {"shape": "UpdateCapacityReservationOutput"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServerException"}], "documentation": "<p>Updates the number of requested data processing units for the capacity reservation with the specified name.</p>"}, "UpdateDataCatalog": {"name": "UpdateDataCatalog", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateDataCatalogInput"}, "output": {"shape": "UpdateDataCatalogOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Updates the data catalog that has the specified name.</p>"}, "UpdateNamedQuery": {"name": "Update<PERSON><PERSON><PERSON><PERSON><PERSON>", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateNamedQueryInput"}, "output": {"shape": "UpdateNamedQueryOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Updates a <a>NamedQuery</a> object. The database or workgroup cannot be updated.</p>", "idempotent": true}, "UpdateNotebook": {"name": "UpdateNotebook", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateNotebookInput"}, "output": {"shape": "UpdateNotebookOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Updates the contents of a Spark notebook.</p>"}, "UpdateNotebookMetadata": {"name": "UpdateNotebookMetadata", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateNotebookMetadataInput"}, "output": {"shape": "UpdateNotebookMetadataOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}, {"shape": "TooManyRequestsException"}], "documentation": "<p>Updates the metadata for a notebook.</p>"}, "UpdatePreparedStatement": {"name": "UpdatePreparedStatement", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdatePreparedStatementInput"}, "output": {"shape": "UpdatePreparedStatementOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Updates a prepared statement.</p>"}, "UpdateWorkGroup": {"name": "UpdateWorkGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateWorkGroupInput"}, "output": {"shape": "UpdateWorkGroupOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Updates the workgroup with the specified name. The workgroup's name cannot be changed. Only <code>ConfigurationUpdates</code> can be specified.</p>"}}, "shapes": {"AclConfiguration": {"type": "structure", "required": ["S3AclOption"], "members": {"S3AclOption": {"shape": "S3AclOption", "documentation": "<p>The Amazon S3 canned ACL that <PERSON> should specify when storing query results, including data files inserted by <PERSON> as the result of statements like CTAS or INSERT INTO. Currently the only supported canned ACL is <code>BUCKET_OWNER_FULL_CONTROL</code>. If a query runs in a workgroup and the workgroup overrides client-side settings, then the Amazon S3 canned ACL specified in the workgroup's settings is used for all queries that run in the workgroup. For more information about Amazon S3 canned ACLs, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/acl-overview.html#canned-acl\">Canned ACL</a> in the <i>Amazon S3 User Guide</i>.</p>"}}, "documentation": "<p>Indicates that an Amazon S3 canned ACL should be set to control ownership of stored query results, including data files inserted by Athena as the result of statements like CTAS or INSERT INTO. When Athena stores query results in Amazon S3, the canned ACL is set with the <code>x-amz-acl</code> request header. For more information about S3 Object Ownership, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/about-object-ownership.html#object-ownership-overview\">Object Ownership settings</a> in the <i>Amazon S3 User Guide</i>.</p>"}, "Age": {"type": "integer", "max": 10080, "min": 0}, "AllocatedDpusInteger": {"type": "integer", "box": true, "min": 0}, "AmazonResourceName": {"type": "string", "max": 1011, "min": 1}, "ApplicationDPUSizes": {"type": "structure", "members": {"ApplicationRuntimeId": {"shape": "NameString", "documentation": "<p>The name of the supported application runtime (for example, <code>Athena notebook version 1</code>).</p>"}, "SupportedDPUSizes": {"shape": "SupportedDPUSizeList", "documentation": "<p>A list of the supported DPU sizes that the application runtime supports.</p>"}}, "documentation": "<p>Contains the application runtime IDs and their supported DPU sizes.</p>"}, "ApplicationDPUSizesList": {"type": "list", "member": {"shape": "ApplicationDPUSizes"}}, "AthenaError": {"type": "structure", "members": {"ErrorCategory": {"shape": "Error<PERSON>ate<PERSON><PERSON>", "documentation": "<p>An integer value that specifies the category of a query failure error. The following list shows the category for each integer value.</p> <p> <b>1</b> - System</p> <p> <b>2</b> - User</p> <p> <b>3</b> - Other</p>"}, "ErrorType": {"shape": "ErrorType", "documentation": "<p>An integer value that provides specific information about an Athena query error. For the meaning of specific values, see the <a href=\"https://docs.aws.amazon.com/athena/latest/ug/error-reference.html#error-reference-error-type-reference\">Error Type Reference</a> in the <i>Amazon Athena User Guide</i>.</p>"}, "Retryable": {"shape": "Boolean", "documentation": "<p>True if the query might succeed if resubmitted.</p>"}, "ErrorMessage": {"shape": "String", "documentation": "<p>Contains a short description of the error that occurred.</p>"}}, "documentation": "<p>Provides information about an Athena query error. The <code>AthenaError</code> feature provides standardized error information to help you understand failed queries and take steps after a query failure occurs. <code>AthenaError</code> includes an <code>ErrorCategory</code> field that specifies whether the cause of the failed query is due to system error, user error, or other error.</p>"}, "AuthToken": {"type": "string", "max": 2048}, "AuthenticationType": {"type": "string", "enum": ["DIRECTORY_IDENTITY"]}, "AwsAccountId": {"type": "string", "max": 12, "min": 12, "pattern": "^[0-9]+$"}, "BatchGetNamedQueryInput": {"type": "structure", "required": ["NamedQueryIds"], "members": {"NamedQueryIds": {"shape": "NamedQueryIdList", "documentation": "<p>An array of query IDs.</p>"}}, "documentation": "<p>Contains an array of named query IDs.</p>"}, "BatchGetNamedQueryOutput": {"type": "structure", "members": {"NamedQueries": {"shape": "NamedQueryList", "documentation": "<p>Information about the named query IDs submitted.</p>"}, "UnprocessedNamedQueryIds": {"shape": "UnprocessedNamedQueryIdList", "documentation": "<p>Information about provided query IDs.</p>"}}}, "BatchGetPreparedStatementInput": {"type": "structure", "required": ["PreparedStatementNames", "WorkGroup"], "members": {"PreparedStatementNames": {"shape": "PreparedStatementNameList", "documentation": "<p>A list of prepared statement names to return.</p>"}, "WorkGroup": {"shape": "WorkGroupName", "documentation": "<p>The name of the workgroup to which the prepared statements belong.</p>"}}}, "BatchGetPreparedStatementOutput": {"type": "structure", "members": {"PreparedStatements": {"shape": "PreparedStatementDetailsList", "documentation": "<p>The list of prepared statements returned.</p>"}, "UnprocessedPreparedStatementNames": {"shape": "UnprocessedPreparedStatementNameList", "documentation": "<p>A list of one or more prepared statements that were requested but could not be returned.</p>"}}}, "BatchGetQueryExecutionInput": {"type": "structure", "required": ["QueryExecutionIds"], "members": {"QueryExecutionIds": {"shape": "QueryExecutionIdList", "documentation": "<p>An array of query execution IDs.</p>"}}, "documentation": "<p>Contains an array of query execution IDs.</p>"}, "BatchGetQueryExecutionOutput": {"type": "structure", "members": {"QueryExecutions": {"shape": "QueryExecutionList", "documentation": "<p>Information about a query execution.</p>"}, "UnprocessedQueryExecutionIds": {"shape": "UnprocessedQueryExecutionIdList", "documentation": "<p>Information about the query executions that failed to run.</p>"}}}, "Boolean": {"type": "boolean"}, "BoxedBoolean": {"type": "boolean"}, "BytesScannedCutoffValue": {"type": "long", "min": 10000000}, "CalculationConfiguration": {"type": "structure", "members": {"CodeBlock": {"shape": "CodeBlock", "documentation": "<p>A string that contains the code for the calculation.</p>"}}, "documentation": "<p>Contains configuration information for the calculation.</p>"}, "CalculationExecutionId": {"type": "string", "max": 36, "min": 1}, "CalculationExecutionState": {"type": "string", "enum": ["CREATING", "CREATED", "QUEUED", "RUNNING", "CANCELING", "CANCELED", "COMPLETED", "FAILED"]}, "CalculationResult": {"type": "structure", "members": {"StdOutS3Uri": {"shape": "S3Uri", "documentation": "<p>The Amazon S3 location of the <code>stdout</code> file for the calculation.</p>"}, "StdErrorS3Uri": {"shape": "S3Uri", "documentation": "<p>The Amazon S3 location of the <code>stderr</code> error messages file for the calculation.</p>"}, "ResultS3Uri": {"shape": "S3Uri", "documentation": "<p>The Amazon S3 location of the folder for the calculation results.</p>"}, "ResultType": {"shape": "CalculationResultType", "documentation": "<p>The data format of the calculation result.</p>"}}, "documentation": "<p>Contains information about an application-specific calculation result.</p>"}, "CalculationResultType": {"type": "string", "max": 256, "min": 1, "pattern": "\\w+\\/[-+.\\w]+"}, "CalculationStatistics": {"type": "structure", "members": {"DpuExecutionInMillis": {"shape": "<PERSON>", "documentation": "<p>The data processing unit execution time in milliseconds for the calculation.</p>"}, "Progress": {"shape": "DescriptionString", "documentation": "<p>The progress of the calculation.</p>"}}, "documentation": "<p>Contains statistics for a notebook calculation.</p>"}, "CalculationStatus": {"type": "structure", "members": {"SubmissionDateTime": {"shape": "Date", "documentation": "<p>The date and time the calculation was submitted for processing.</p>"}, "CompletionDateTime": {"shape": "Date", "documentation": "<p>The date and time the calculation completed processing.</p>"}, "State": {"shape": "CalculationExecutionState", "documentation": "<p>The state of the calculation execution. A description of each state follows.</p> <p> <code>CREATING</code> - The calculation is in the process of being created.</p> <p> <code>CREATED</code> - The calculation has been created and is ready to run.</p> <p> <code>QUEUED</code> - The calculation has been queued for processing.</p> <p> <code>RUNNING</code> - The calculation is running.</p> <p> <code>CANCELING</code> - A request to cancel the calculation has been received and the system is working to stop it.</p> <p> <code>CANCELED</code> - The calculation is no longer running as the result of a cancel request.</p> <p> <code>COMPLETED</code> - The calculation has completed without error.</p> <p> <code>FAILED</code> - The calculation failed and is no longer running.</p>"}, "StateChangeReason": {"shape": "DescriptionString", "documentation": "<p>The reason for the calculation state change (for example, the calculation was canceled because the session was terminated).</p>"}}, "documentation": "<p>Contains information about the status of a notebook calculation.</p>"}, "CalculationSummary": {"type": "structure", "members": {"CalculationExecutionId": {"shape": "CalculationExecutionId", "documentation": "<p>The calculation execution UUID.</p>"}, "Description": {"shape": "DescriptionString", "documentation": "<p>A description of the calculation.</p>"}, "Status": {"shape": "CalculationStatus", "documentation": "<p>Contains information about the status of the calculation.</p>"}}, "documentation": "<p>Summary information for a notebook calculation.</p>"}, "CalculationsList": {"type": "list", "member": {"shape": "CalculationSummary"}, "max": 100, "min": 0}, "CancelCapacityReservationInput": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "CapacityReservationName", "documentation": "<p>The name of the capacity reservation to cancel.</p>"}}}, "CancelCapacityReservationOutput": {"type": "structure", "members": {}}, "CapacityAllocation": {"type": "structure", "required": ["Status", "RequestTime"], "members": {"Status": {"shape": "CapacityAllocationStatus", "documentation": "<p>The status of the capacity allocation.</p>"}, "StatusMessage": {"shape": "String", "documentation": "<p>The status message of the capacity allocation.</p>"}, "RequestTime": {"shape": "Timestamp", "documentation": "<p>The time when the capacity allocation was requested.</p>"}, "RequestCompletionTime": {"shape": "Timestamp", "documentation": "<p>The time when the capacity allocation request was completed.</p>"}}, "documentation": "<p>Contains the submission time of a single allocation request for a capacity reservation and the most recent status of the attempted allocation.</p>"}, "CapacityAllocationStatus": {"type": "string", "enum": ["PENDING", "SUCCEEDED", "FAILED"]}, "CapacityAssignment": {"type": "structure", "members": {"WorkGroupNames": {"shape": "WorkGroupNamesList", "documentation": "<p>The list of workgroup names for the capacity assignment.</p>"}}, "documentation": "<p>A mapping between one or more workgroups and a capacity reservation.</p>"}, "CapacityAssignmentConfiguration": {"type": "structure", "members": {"CapacityReservationName": {"shape": "CapacityReservationName", "documentation": "<p>The name of the reservation that the capacity assignment configuration is for.</p>"}, "CapacityAssignments": {"shape": "CapacityAssignmentsList", "documentation": "<p>The list of assignments that make up the capacity assignment configuration.</p>"}}, "documentation": "<p>Assigns Athena workgroups (and hence their queries) to capacity reservations. A capacity reservation can have only one capacity assignment configuration, but the capacity assignment configuration can be made up of multiple individual assignments. Each assignment specifies how Athena queries can consume capacity from the capacity reservation that their workgroup is mapped to.</p>"}, "CapacityAssignmentsList": {"type": "list", "member": {"shape": "CapacityAssignment"}}, "CapacityReservation": {"type": "structure", "required": ["Name", "Status", "TargetDpus", "AllocatedDpus", "CreationTime"], "members": {"Name": {"shape": "CapacityReservationName", "documentation": "<p>The name of the capacity reservation.</p>"}, "Status": {"shape": "CapacityReservationStatus", "documentation": "<p>The status of the capacity reservation.</p>"}, "TargetDpus": {"shape": "TargetDpusInteger", "documentation": "<p>The number of data processing units requested.</p>"}, "AllocatedDpus": {"shape": "AllocatedDpusInteger", "documentation": "<p>The number of data processing units currently allocated.</p>"}, "LastAllocation": {"shape": "CapacityAllocation"}, "LastSuccessfulAllocationTime": {"shape": "Timestamp", "documentation": "<p>The time of the most recent capacity allocation that succeeded.</p>"}, "CreationTime": {"shape": "Timestamp", "documentation": "<p>The time in UTC epoch millis when the capacity reservation was created.</p>"}}, "documentation": "<p>A reservation for a specified number of data processing units (DPUs). When a reservation is initially created, it has no DPUs. Athena allocates DPUs until the allocated amount equals the requested amount.</p>"}, "CapacityReservationName": {"type": "string", "max": 128, "min": 1, "pattern": "[a-zA-Z0-9._-]+"}, "CapacityReservationStatus": {"type": "string", "enum": ["PENDING", "ACTIVE", "CANCELLING", "CANCELLED", "FAILED", "UPDATE_PENDING"]}, "CapacityReservationsList": {"type": "list", "member": {"shape": "CapacityReservation"}}, "CatalogNameString": {"type": "string", "max": 256, "min": 1, "pattern": "[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*"}, "ClientRequestToken": {"type": "string", "max": 36, "min": 1, "pattern": "[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}"}, "CodeBlock": {"type": "string", "max": 68000}, "Column": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "NameString", "documentation": "<p>The name of the column.</p>"}, "Type": {"shape": "TypeString", "documentation": "<p>The data type of the column.</p>"}, "Comment": {"shape": "CommentString", "documentation": "<p>Optional information about the column.</p>"}}, "documentation": "<p>Contains metadata for a column in a table.</p>"}, "ColumnInfo": {"type": "structure", "required": ["Name", "Type"], "members": {"CatalogName": {"shape": "String", "documentation": "<p>The catalog to which the query results belong.</p>"}, "SchemaName": {"shape": "String", "documentation": "<p>The schema name (database name) to which the query results belong.</p>"}, "TableName": {"shape": "String", "documentation": "<p>The table name for the query results.</p>"}, "Name": {"shape": "String", "documentation": "<p>The name of the column.</p>"}, "Label": {"shape": "String", "documentation": "<p>A column label.</p>"}, "Type": {"shape": "String", "documentation": "<p>The data type of the column.</p>"}, "Precision": {"shape": "Integer", "documentation": "<p>For <code>DECIMAL</code> data types, specifies the total number of digits, up to 38. For performance reasons, we recommend up to 18 digits.</p>"}, "Scale": {"shape": "Integer", "documentation": "<p>For <code>DECIMAL</code> data types, specifies the total number of digits in the fractional part of the value. Defaults to 0.</p>"}, "Nullable": {"shape": "ColumnNullable", "documentation": "<p>Unsupported constraint. This value always shows as <code>UNKNOWN</code>.</p>"}, "CaseSensitive": {"shape": "Boolean", "documentation": "<p>Indicates whether values in the column are case-sensitive.</p>"}}, "documentation": "<p>Information about the columns in a query execution result.</p>"}, "ColumnInfoList": {"type": "list", "member": {"shape": "ColumnInfo"}}, "ColumnList": {"type": "list", "member": {"shape": "Column"}}, "ColumnNullable": {"type": "string", "enum": ["NOT_NULL", "NULLABLE", "UNKNOWN"]}, "CommentString": {"type": "string", "max": 255, "min": 0, "pattern": "[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*"}, "ConnectionType": {"type": "string", "enum": ["DYNAMODB", "MYSQL", "POSTGRESQL", "REDSHIFT", "ORACLE", "SYNAPSE", "SQLSERVER", "DB2", "OPENSEARCH", "BIGQUERY", "GOOGLECLOUDSTORAGE", "HBASE", "DOCUMENTDB", "CMDB", "TPCDS", "TIMESTREAM", "SAPHANA", "SNOWFLAKE", "DATALAKEGEN2", "DB2AS400"]}, "CoordinatorDpuSize": {"type": "integer", "box": true, "max": 1, "min": 1}, "CreateCapacityReservationInput": {"type": "structure", "required": ["TargetDpus", "Name"], "members": {"TargetDpus": {"shape": "TargetDpusInteger", "documentation": "<p>The number of requested data processing units.</p>"}, "Name": {"shape": "CapacityReservationName", "documentation": "<p>The name of the capacity reservation to create.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The tags for the capacity reservation.</p>"}}}, "CreateCapacityReservationOutput": {"type": "structure", "members": {}}, "CreateDataCatalogInput": {"type": "structure", "required": ["Name", "Type"], "members": {"Name": {"shape": "CatalogNameString", "documentation": "<p>The name of the data catalog to create. The catalog name must be unique for the Amazon Web Services account and can use a maximum of 127 alphanumeric, underscore, at sign, or hyphen characters. The remainder of the length constraint of 256 is reserved for use by Athena.</p> <p>For <code>FEDERATED</code> type the catalog name has following considerations and limits:</p> <ul> <li> <p>The catalog name allows special characters such as <code>_ , @ , \\ , - </code>. These characters are replaced with a hyphen (-) when creating the CFN Stack Name and with an underscore (_) when creating the Lambda Function and Glue Connection Name.</p> </li> <li> <p>The catalog name has a theoretical limit of 128 characters. However, since we use it to create other resources that allow less characters and we prepend a prefix to it, the actual catalog name limit for <code>FEDERATED</code> catalog is 64 - 23 = 41 characters.</p> </li> </ul>"}, "Type": {"shape": "DataCatalogType", "documentation": "<p>The type of data catalog to create: <code>LAMBDA</code> for a federated catalog, <code>GLUE</code> for an Glue Data Catalog, and <code>HIVE</code> for an external Apache Hive metastore. <code>FEDERATED</code> is a federated catalog for which <PERSON> creates the connection and the Lambda function for you based on the parameters that you pass.</p> <p>For <code>FEDERATED</code> type, we do not support IAM identity center.</p>"}, "Description": {"shape": "DescriptionString", "documentation": "<p>A description of the data catalog to be created.</p>"}, "Parameters": {"shape": "ParametersMap", "documentation": "<p>Specifies the Lambda function or functions to use for creating the data catalog. This is a mapping whose values depend on the catalog type. </p> <ul> <li> <p>For the <code>HIVE</code> data catalog type, use the following syntax. The <code>metadata-function</code> parameter is required. <code>The sdk-version</code> parameter is optional and defaults to the currently supported version.</p> <p> <code>metadata-function=<i>lambda_arn</i>, sdk-version=<i>version_number</i> </code> </p> </li> <li> <p>For the <code>LAMBDA</code> data catalog type, use one of the following sets of required parameters, but not both.</p> <ul> <li> <p>If you have one Lambda function that processes metadata and another for reading the actual data, use the following syntax. Both parameters are required.</p> <p> <code>metadata-function=<i>lambda_arn</i>, record-function=<i>lambda_arn</i> </code> </p> </li> <li> <p> If you have a composite Lambda function that processes both metadata and data, use the following syntax to specify your Lambda function.</p> <p> <code>function=<i>lambda_arn</i> </code> </p> </li> </ul> </li> <li> <p>The <code>GLUE</code> type takes a catalog ID parameter and is required. The <code> <i>catalog_id</i> </code> is the account ID of the Amazon Web Services account to which the Glue Data Catalog belongs.</p> <p> <code>catalog-id=<i>catalog_id</i> </code> </p> <ul> <li> <p>The <code>GLUE</code> data catalog type also applies to the default <code>AwsDataCatalog</code> that already exists in your account, of which you can have only one and cannot modify.</p> </li> </ul> </li> <li> <p>The <code>FEDERATED</code> data catalog type uses one of the following parameters, but not both. Use <code>connection-arn</code> for an existing Glue connection. Use <code>connection-type</code> and <code>connection-properties</code> to specify the configuration setting for a new connection.</p> <ul> <li> <p> <code>connection-arn:<i>&lt;glue_connection_arn_to_reuse&gt;</i> </code> </p> </li> <li> <p> <code>lambda-role-arn</code> (optional): The execution role to use for the Lambda function. If not provided, one is created.</p> </li> <li> <p> <code>connection-type:MYSQL|REDSHIFT|...., connection-properties:\"<i>&lt;json_string&gt;</i>\"</code> </p> <p>For <i> <code>&lt;json_string&gt;</code> </i>, use escaped JSON text, as in the following example.</p> <p> <code>\"{\\\"spill_bucket\\\":\\\"my_spill\\\",\\\"spill_prefix\\\":\\\"athena-spill\\\",\\\"host\\\":\\\"abc12345.snowflakecomputing.com\\\",\\\"port\\\":\\\"1234\\\",\\\"warehouse\\\":\\\"DEV_WH\\\",\\\"database\\\":\\\"TEST\\\",\\\"schema\\\":\\\"PUBLIC\\\",\\\"SecretArn\\\":\\\"arn:aws:secretsmanager:ap-south-1:111122223333:secret:snowflake-XHb67j\\\"}\"</code> </p> </li> </ul> </li> </ul>"}, "Tags": {"shape": "TagList", "documentation": "<p>A list of comma separated tags to add to the data catalog that is created. All the resources that are created by the <code>CreateDataCatalog</code> API operation with <code>FEDERATED</code> type will have the tag <code>federated_athena_datacatalog=\"true\"</code>. This includes the CFN Stack, Glue Connection, Athena DataCatalog, and all the resources created as part of the CFN Stack (Lambda Function, IAM policies/roles).</p>"}}}, "CreateDataCatalogOutput": {"type": "structure", "members": {"DataCatalog": {"shape": "DataCatalog"}}}, "CreateNamedQueryInput": {"type": "structure", "required": ["Name", "Database", "QueryString"], "members": {"Name": {"shape": "NameString", "documentation": "<p>The query name.</p>"}, "Description": {"shape": "DescriptionString", "documentation": "<p>The query description.</p>"}, "Database": {"shape": "DatabaseString", "documentation": "<p>The database to which the query belongs.</p>"}, "QueryString": {"shape": "QueryString", "documentation": "<p>The contents of the query with all query statements.</p>"}, "ClientRequestToken": {"shape": "IdempotencyToken", "documentation": "<p>A unique case-sensitive string used to ensure the request to create the query is idempotent (executes only once). If another <code>CreateNamedQuery</code> request is received, the same response is returned and another query is not created. If a parameter has changed, for example, the <code>QueryString</code>, an error is returned.</p> <important> <p>This token is listed as not required because Amazon Web Services SDKs (for example the Amazon Web Services SDK for Java) auto-generate the token for users. If you are not using the Amazon Web Services SDK or the Amazon Web Services CLI, you must provide this token or the action will fail.</p> </important>", "idempotencyToken": true}, "WorkGroup": {"shape": "WorkGroupName", "documentation": "<p>The name of the workgroup in which the named query is being created.</p>"}}}, "CreateNamedQueryOutput": {"type": "structure", "members": {"NamedQueryId": {"shape": "NamedQueryId", "documentation": "<p>The unique ID of the query.</p>"}}}, "CreateNotebookInput": {"type": "structure", "required": ["WorkGroup", "Name"], "members": {"WorkGroup": {"shape": "WorkGroupName", "documentation": "<p>The name of the Spark enabled workgroup in which the notebook will be created.</p>"}, "Name": {"shape": "NotebookName", "documentation": "<p>The name of the <code>ipynb</code> file to be created in the Spark workgroup, without the <code>.ipynb</code> extension.</p>"}, "ClientRequestToken": {"shape": "ClientRequestToken", "documentation": "<p>A unique case-sensitive string used to ensure the request to create the notebook is idempotent (executes only once).</p> <important> <p>This token is listed as not required because Amazon Web Services SDKs (for example the Amazon Web Services SDK for Java) auto-generate the token for you. If you are not using the Amazon Web Services SDK or the Amazon Web Services CLI, you must provide this token or the action will fail.</p> </important>"}}}, "CreateNotebookOutput": {"type": "structure", "members": {"NotebookId": {"shape": "NotebookId", "documentation": "<p>A unique identifier for the notebook.</p>"}}}, "CreatePreparedStatementInput": {"type": "structure", "required": ["StatementName", "WorkGroup", "QueryStatement"], "members": {"StatementName": {"shape": "StatementName", "documentation": "<p>The name of the prepared statement.</p>"}, "WorkGroup": {"shape": "WorkGroupName", "documentation": "<p>The name of the workgroup to which the prepared statement belongs.</p>"}, "QueryStatement": {"shape": "QueryString", "documentation": "<p>The query string for the prepared statement.</p>"}, "Description": {"shape": "DescriptionString", "documentation": "<p>The description of the prepared statement.</p>"}}}, "CreatePreparedStatementOutput": {"type": "structure", "members": {}}, "CreatePresignedNotebookUrlRequest": {"type": "structure", "required": ["SessionId"], "members": {"SessionId": {"shape": "SessionId", "documentation": "<p>The session ID.</p>"}}}, "CreatePresignedNotebookUrlResponse": {"type": "structure", "required": ["NotebookUrl", "AuthToken", "AuthTokenExpirationTime"], "members": {"NotebookUrl": {"shape": "String", "documentation": "<p>The URL of the notebook. The URL includes the authentication token and notebook file name and points directly to the opened notebook.</p>"}, "AuthToken": {"shape": "AuthToken", "documentation": "<p>The authentication token for the notebook.</p>"}, "AuthTokenExpirationTime": {"shape": "<PERSON>", "documentation": "<p>The UTC epoch time when the authentication token expires.</p>"}}}, "CreateWorkGroupInput": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "WorkGroupName", "documentation": "<p>The workgroup name.</p>"}, "Configuration": {"shape": "WorkGroupConfiguration", "documentation": "<p>Contains configuration information for creating an Athena SQL workgroup or Spark enabled Athena workgroup. Athena SQL workgroup configuration includes the location in Amazon S3 where query and calculation results are stored, the encryption configuration, if any, used for encrypting query results, whether the Amazon CloudWatch Metrics are enabled for the workgroup, the limit for the amount of bytes scanned (cutoff) per query, if it is specified, and whether workgroup's settings (specified with <code>EnforceWorkGroupConfiguration</code>) in the <code>WorkGroupConfiguration</code> override client-side settings. See <a>WorkGroupConfiguration$EnforceWorkGroupConfiguration</a>.</p>"}, "Description": {"shape": "WorkGroupDescriptionString", "documentation": "<p>The workgroup description.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>A list of comma separated tags to add to the workgroup that is created.</p>"}}}, "CreateWorkGroupOutput": {"type": "structure", "members": {}}, "CustomerContentEncryptionConfiguration": {"type": "structure", "required": ["KmsKey"], "members": {"KmsKey": {"shape": "KmsKey", "documentation": "<p>The customer managed KMS key that is used to encrypt the user's data stores in Athena.</p>"}}, "documentation": "<p>Specifies the customer managed KMS key that is used to encrypt the user's data stores in Athena. When an Amazon Web Services managed key is used, this value is null. This setting does not apply to Athena SQL workgroups.</p>"}, "DataCatalog": {"type": "structure", "required": ["Name", "Type"], "members": {"Name": {"shape": "CatalogNameString", "documentation": "<p>The name of the data catalog. The catalog name must be unique for the Amazon Web Services account and can use a maximum of 127 alphanumeric, underscore, at sign, or hyphen characters. The remainder of the length constraint of 256 is reserved for use by Athena.</p>"}, "Description": {"shape": "DescriptionString", "documentation": "<p>An optional description of the data catalog.</p>"}, "Type": {"shape": "DataCatalogType", "documentation": "<p>The type of data catalog to create: <code>LAMBDA</code> for a federated catalog, <code>GLUE</code> for an Glue Data Catalog, and <code>HIVE</code> for an external Apache Hive metastore. <code>FEDERATED</code> is a federated catalog for which <PERSON> creates the connection and the Lambda function for you based on the parameters that you pass.</p>"}, "Parameters": {"shape": "ParametersMap", "documentation": "<p>Specifies the Lambda function or functions to use for the data catalog. This is a mapping whose values depend on the catalog type. </p> <ul> <li> <p>For the <code>HIVE</code> data catalog type, use the following syntax. The <code>metadata-function</code> parameter is required. <code>The sdk-version</code> parameter is optional and defaults to the currently supported version.</p> <p> <code>metadata-function=<i>lambda_arn</i>, sdk-version=<i>version_number</i> </code> </p> </li> <li> <p>For the <code>LAMBDA</code> data catalog type, use one of the following sets of required parameters, but not both.</p> <ul> <li> <p>If you have one Lambda function that processes metadata and another for reading the actual data, use the following syntax. Both parameters are required.</p> <p> <code>metadata-function=<i>lambda_arn</i>, record-function=<i>lambda_arn</i> </code> </p> </li> <li> <p> If you have a composite Lambda function that processes both metadata and data, use the following syntax to specify your Lambda function.</p> <p> <code>function=<i>lambda_arn</i> </code> </p> </li> </ul> </li> <li> <p>The <code>GLUE</code> type takes a catalog ID parameter and is required. The <code> <i>catalog_id</i> </code> is the account ID of the Amazon Web Services account to which the Glue catalog belongs.</p> <p> <code>catalog-id=<i>catalog_id</i> </code> </p> <ul> <li> <p>The <code>GLUE</code> data catalog type also applies to the default <code>AwsDataCatalog</code> that already exists in your account, of which you can have only one and cannot modify.</p> </li> </ul> </li> <li> <p>The <code>FEDERATED</code> data catalog type uses one of the following parameters, but not both. Use <code>connection-arn</code> for an existing Glue connection. Use <code>connection-type</code> and <code>connection-properties</code> to specify the configuration setting for a new connection.</p> <ul> <li> <p> <code>connection-arn:<i>&lt;glue_connection_arn_to_reuse&gt;</i> </code> </p> </li> <li> <p> <code>connection-type:MYSQL|REDSHIFT|...., connection-properties:\"<i>&lt;json_string&gt;</i>\"</code> </p> <p>For <i> <code>&lt;json_string&gt;</code> </i>, use escaped JSON text, as in the following example.</p> <p> <code>\"{\\\"spill_bucket\\\":\\\"my_spill\\\",\\\"spill_prefix\\\":\\\"athena-spill\\\",\\\"host\\\":\\\"abc12345.snowflakecomputing.com\\\",\\\"port\\\":\\\"1234\\\",\\\"warehouse\\\":\\\"DEV_WH\\\",\\\"database\\\":\\\"TEST\\\",\\\"schema\\\":\\\"PUBLIC\\\",\\\"SecretArn\\\":\\\"arn:aws:secretsmanager:ap-south-1:111122223333:secret:snowflake-XHb67j\\\"}\"</code> </p> </li> </ul> </li> </ul>"}, "Status": {"shape": "DataCatalogStatus", "documentation": "<p>The status of the creation or deletion of the data catalog.</p> <ul> <li> <p>The <code>LAMBDA</code>, <code>GLUE</code>, and <code>HIVE</code> data catalog types are created synchronously. Their status is either <code>CREATE_COMPLETE</code> or <code>CREATE_FAILED</code>.</p> </li> <li> <p>The <code>FEDERATED</code> data catalog type is created asynchronously.</p> </li> </ul> <p>Data catalog creation status:</p> <ul> <li> <p> <code>CREATE_IN_PROGRESS</code>: Federated data catalog creation in progress.</p> </li> <li> <p> <code>CREATE_COMPLETE</code>: Data catalog creation complete.</p> </li> <li> <p> <code>CREATE_FAILED</code>: Data catalog could not be created.</p> </li> <li> <p> <code>CREATE_FAILED_CLEANUP_IN_PROGRESS</code>: Federated data catalog creation failed and is being removed.</p> </li> <li> <p> <code>CREATE_FAILED_CLEANUP_COMPLETE</code>: Federated data catalog creation failed and was removed.</p> </li> <li> <p> <code>CREATE_FAILED_CLEANUP_FAILED</code>: Federated data catalog creation failed but could not be removed.</p> </li> </ul> <p>Data catalog deletion status:</p> <ul> <li> <p> <code>DELETE_IN_PROGRESS</code>: Federated data catalog deletion in progress.</p> </li> <li> <p> <code>DELETE_COMPLETE</code>: Federated data catalog deleted.</p> </li> <li> <p> <code>DELETE_FAILED</code>: Federated data catalog could not be deleted.</p> </li> </ul>"}, "ConnectionType": {"shape": "ConnectionType", "documentation": "<p>The type of connection for a <code>FEDERATED</code> data catalog (for example, <code>REDSHIFT</code>, <code>MYSQL</code>, or <code>SQLSERVER</code>). For information about individual connectors, see <a href=\"https://docs.aws.amazon.com/athena/latest/ug/connectors-available.html\">Available data source connectors</a>.</p>"}, "Error": {"shape": "ErrorMessage", "documentation": "<p>Text of the error that occurred during data catalog creation or deletion.</p>"}}, "documentation": "<p>Contains information about a data catalog in an Amazon Web Services account.</p> <note> <p>In the Athena console, data catalogs are listed as \"data sources\" on the <b>Data sources</b> page under the <b>Data source name</b> column.</p> </note>"}, "DataCatalogStatus": {"type": "string", "enum": ["CREATE_IN_PROGRESS", "CREATE_COMPLETE", "CREATE_FAILED", "CREATE_FAILED_CLEANUP_IN_PROGRESS", "CREATE_FAILED_CLEANUP_COMPLETE", "CREATE_FAILED_CLEANUP_FAILED", "DELETE_IN_PROGRESS", "DELETE_COMPLETE", "DELETE_FAILED"]}, "DataCatalogSummary": {"type": "structure", "members": {"CatalogName": {"shape": "CatalogNameString", "documentation": "<p>The name of the data catalog. The catalog name is unique for the Amazon Web Services account and can use a maximum of 127 alphanumeric, underscore, at sign, or hyphen characters. The remainder of the length constraint of 256 is reserved for use by Athena.</p>"}, "Type": {"shape": "DataCatalogType", "documentation": "<p>The data catalog type.</p>"}, "Status": {"shape": "DataCatalogStatus", "documentation": "<p>The status of the creation or deletion of the data catalog.</p> <ul> <li> <p>The <code>LAMBDA</code>, <code>GLUE</code>, and <code>HIVE</code> data catalog types are created synchronously. Their status is either <code>CREATE_COMPLETE</code> or <code>CREATE_FAILED</code>.</p> </li> <li> <p>The <code>FEDERATED</code> data catalog type is created asynchronously.</p> </li> </ul> <p>Data catalog creation status:</p> <ul> <li> <p> <code>CREATE_IN_PROGRESS</code>: Federated data catalog creation in progress.</p> </li> <li> <p> <code>CREATE_COMPLETE</code>: Data catalog creation complete.</p> </li> <li> <p> <code>CREATE_FAILED</code>: Data catalog could not be created.</p> </li> <li> <p> <code>CREATE_FAILED_CLEANUP_IN_PROGRESS</code>: Federated data catalog creation failed and is being removed.</p> </li> <li> <p> <code>CREATE_FAILED_CLEANUP_COMPLETE</code>: Federated data catalog creation failed and was removed.</p> </li> <li> <p> <code>CREATE_FAILED_CLEANUP_FAILED</code>: Federated data catalog creation failed but could not be removed.</p> </li> </ul> <p>Data catalog deletion status:</p> <ul> <li> <p> <code>DELETE_IN_PROGRESS</code>: Federated data catalog deletion in progress.</p> </li> <li> <p> <code>DELETE_COMPLETE</code>: Federated data catalog deleted.</p> </li> <li> <p> <code>DELETE_FAILED</code>: Federated data catalog could not be deleted.</p> </li> </ul>"}, "ConnectionType": {"shape": "ConnectionType", "documentation": "<p>The type of connection for a <code>FEDERATED</code> data catalog (for example, <code>REDSHIFT</code>, <code>MYSQL</code>, or <code>SQLSERVER</code>). For information about individual connectors, see <a href=\"https://docs.aws.amazon.com/athena/latest/ug/connectors-available.html\">Available data source connectors</a>.</p>"}, "Error": {"shape": "ErrorMessage", "documentation": "<p>Text of the error that occurred during data catalog creation or deletion.</p>"}}, "documentation": "<p>The summary information for the data catalog, which includes its name and type.</p>"}, "DataCatalogSummaryList": {"type": "list", "member": {"shape": "DataCatalogSummary"}}, "DataCatalogType": {"type": "string", "enum": ["LAMBDA", "GLUE", "HIVE", "FEDERATED"]}, "Database": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "NameString", "documentation": "<p>The name of the database.</p>"}, "Description": {"shape": "DescriptionString", "documentation": "<p>An optional description of the database.</p>"}, "Parameters": {"shape": "ParametersMap", "documentation": "<p>A set of custom key/value pairs.</p>"}}, "documentation": "<p>Contains metadata information for a database in a data catalog.</p>"}, "DatabaseList": {"type": "list", "member": {"shape": "Database"}}, "DatabaseString": {"type": "string", "max": 255, "min": 1}, "Date": {"type": "timestamp"}, "Datum": {"type": "structure", "members": {"VarCharValue": {"shape": "datumString", "documentation": "<p>The value of the datum.</p>"}}, "documentation": "<p>A piece of data (a field in the table).</p>"}, "DefaultExecutorDpuSize": {"type": "integer", "box": true, "max": 1, "min": 1}, "DeleteCapacityReservationInput": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "CapacityReservationName", "documentation": "<p>The name of the capacity reservation to delete.</p>"}}}, "DeleteCapacityReservationOutput": {"type": "structure", "members": {}}, "DeleteDataCatalogInput": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "CatalogNameString", "documentation": "<p>The name of the data catalog to delete.</p>"}, "DeleteCatalogOnly": {"shape": "Boolean", "documentation": "<p>Deletes the Athena Data Catalog. You can only use this with the <code>FEDERATED</code> catalogs. You usually perform this before registering the connector with Glue Data Catalog. After deletion, you will have to manage the Glue Connection and Lambda function. </p>"}}}, "DeleteDataCatalogOutput": {"type": "structure", "members": {"DataCatalog": {"shape": "DataCatalog"}}}, "DeleteNamedQueryInput": {"type": "structure", "required": ["NamedQueryId"], "members": {"NamedQueryId": {"shape": "NamedQueryId", "documentation": "<p>The unique ID of the query to delete.</p>", "idempotencyToken": true}}}, "DeleteNamedQueryOutput": {"type": "structure", "members": {}}, "DeleteNotebookInput": {"type": "structure", "required": ["NotebookId"], "members": {"NotebookId": {"shape": "NotebookId", "documentation": "<p>The ID of the notebook to delete.</p>"}}}, "DeleteNotebookOutput": {"type": "structure", "members": {}}, "DeletePreparedStatementInput": {"type": "structure", "required": ["StatementName", "WorkGroup"], "members": {"StatementName": {"shape": "StatementName", "documentation": "<p>The name of the prepared statement to delete.</p>"}, "WorkGroup": {"shape": "WorkGroupName", "documentation": "<p>The workgroup to which the statement to be deleted belongs.</p>"}}}, "DeletePreparedStatementOutput": {"type": "structure", "members": {}}, "DeleteWorkGroupInput": {"type": "structure", "required": ["WorkGroup"], "members": {"WorkGroup": {"shape": "WorkGroupName", "documentation": "<p>The unique name of the workgroup to delete.</p>"}, "RecursiveDeleteOption": {"shape": "BoxedBoolean", "documentation": "<p>The option to delete the workgroup and its contents even if the workgroup contains any named queries, query executions, or notebooks.</p>"}}}, "DeleteWorkGroupOutput": {"type": "structure", "members": {}}, "DescriptionString": {"type": "string", "max": 1024, "min": 1}, "EncryptionConfiguration": {"type": "structure", "required": ["EncryptionOption"], "members": {"EncryptionOption": {"shape": "EncryptionOption", "documentation": "<p>Indicates whether Amazon S3 server-side encryption with Amazon S3-managed keys (<code>SSE_S3</code>), server-side encryption with KMS-managed keys (<code>SSE_KMS</code>), or client-side encryption with KMS-managed keys (<code>CSE_KMS</code>) is used.</p> <p>If a query runs in a workgroup and the workgroup overrides client-side settings, then the workgroup's setting for encryption is used. It specifies whether query results must be encrypted, for all queries that run in this workgroup. </p>"}, "KmsKey": {"shape": "String", "documentation": "<p>For <code>SSE_KMS</code> and <code>CSE_KMS</code>, this is the KMS key ARN or ID.</p>"}}, "documentation": "<p>If query and calculation results are encrypted in Amazon S3, indicates the encryption option used (for example, <code>SSE_KMS</code> or <code>CSE_KMS</code>) and key information.</p>"}, "EncryptionOption": {"type": "string", "enum": ["SSE_S3", "SSE_KMS", "CSE_KMS"]}, "EngineConfiguration": {"type": "structure", "required": ["MaxConcurrentDpus"], "members": {"CoordinatorDpuSize": {"shape": "CoordinatorDpuSize", "documentation": "<p>The number of DPUs to use for the coordinator. A coordinator is a special executor that orchestrates processing work and manages other executors in a notebook session. The default is 1.</p>"}, "MaxConcurrentDpus": {"shape": "MaxConcurrentDpus", "documentation": "<p>The maximum number of DPUs that can run concurrently.</p>"}, "DefaultExecutorDpuSize": {"shape": "DefaultExecutorDpuSize", "documentation": "<p>The default number of DPUs to use for executors. An executor is the smallest unit of compute that a notebook session can request from Athena. The default is 1.</p>"}, "AdditionalConfigs": {"shape": "ParametersMap", "documentation": "<p>Contains additional notebook engine <code>MAP&lt;string, string&gt;</code> parameter mappings in the form of key-value pairs. To specify an Athena notebook that the Jupyter server will download and serve, specify a value for the <a>StartSessionRequest$NotebookVersion</a> field, and then add a key named <code>NotebookId</code> to <code>AdditionalConfigs</code> that has the value of the Athena notebook ID.</p>"}, "SparkProperties": {"shape": "ParametersMap", "documentation": "<p>Specifies custom jar files and Spark properties for use cases like cluster encryption, table formats, and general Spark tuning.</p>"}}, "documentation": "<p>Contains data processing unit (DPU) configuration settings and parameter mappings for a notebook engine.</p>"}, "EngineVersion": {"type": "structure", "members": {"SelectedEngineVersion": {"shape": "NameString", "documentation": "<p>The engine version requested by the user. Possible values are determined by the output of <code>ListEngineVersions</code>, including AUTO. The default is AUTO.</p>"}, "EffectiveEngineVersion": {"shape": "NameString", "documentation": "<p>Read only. The engine version on which the query runs. If the user requests a valid engine version other than Auto, the effective engine version is the same as the engine version that the user requested. If the user requests Auto, the effective engine version is chosen by Athena. When a request to update the engine version is made by a <code>CreateWorkGroup</code> or <code>UpdateWorkGroup</code> operation, the <code>EffectiveEngineVersion</code> field is ignored.</p>"}}, "documentation": "<p>The Athena engine version for running queries, or the PySpark engine version for running sessions.</p>"}, "EngineVersionsList": {"type": "list", "member": {"shape": "EngineVersion"}, "max": 10, "min": 0}, "ErrorCategory": {"type": "integer", "box": true, "max": 3, "min": 1}, "ErrorCode": {"type": "string", "documentation": "<p>The error code returned when the query execution failed to process, or when the processing request for the named query failed.</p>", "max": 256, "min": 1}, "ErrorMessage": {"type": "string"}, "ErrorType": {"type": "integer", "box": true, "max": 9999, "min": 0}, "ExecutionParameter": {"type": "string", "max": 1024, "min": 1}, "ExecutionParameters": {"type": "list", "member": {"shape": "ExecutionParameter"}, "min": 1}, "ExecutorId": {"type": "string", "max": 100000, "pattern": ".*"}, "ExecutorState": {"type": "string", "enum": ["CREATING", "CREATED", "REGISTERED", "TERMINATING", "TERMINATED", "FAILED"]}, "ExecutorType": {"type": "string", "enum": ["COORDINATOR", "GATEWAY", "WORKER"]}, "ExecutorsSummary": {"type": "structure", "required": ["ExecutorId"], "members": {"ExecutorId": {"shape": "ExecutorId", "documentation": "<p>The UUID of the executor.</p>"}, "ExecutorType": {"shape": "ExecutorType", "documentation": "<p>The type of executor used for the application (<code>COORDINATOR</code>, <code>GATEWAY</code>, or <code>WORKER</code>).</p>"}, "StartDateTime": {"shape": "<PERSON>", "documentation": "<p>The date and time that the executor started.</p>"}, "TerminationDateTime": {"shape": "<PERSON>", "documentation": "<p>The date and time that the executor was terminated.</p>"}, "ExecutorState": {"shape": "ExecutorState", "documentation": "<p>The processing state of the executor. A description of each state follows.</p> <p> <code>CREATING</code> - The executor is being started, including acquiring resources.</p> <p> <code>CREATED</code> - The executor has been started.</p> <p> <code>REGISTERED</code> - The executor has been registered.</p> <p> <code>TERMINATING</code> - The executor is in the process of shutting down.</p> <p> <code>TERMINATED</code> - The executor is no longer running.</p> <p> <code>FAILED</code> - Due to a failure, the executor is no longer running.</p>"}, "ExecutorSize": {"shape": "<PERSON>", "documentation": "<p>The smallest unit of compute that a session can request from Athena. Size is measured in data processing unit (DPU) values, a relative measure of processing power.</p>"}}, "documentation": "<p>Contains summary information about an executor.</p>"}, "ExecutorsSummaryList": {"type": "list", "member": {"shape": "ExecutorsSummary"}}, "ExportNotebookInput": {"type": "structure", "required": ["NotebookId"], "members": {"NotebookId": {"shape": "NotebookId", "documentation": "<p>The ID of the notebook to export.</p>"}}}, "ExportNotebookOutput": {"type": "structure", "members": {"NotebookMetadata": {"shape": "NotebookMetadata", "documentation": "<p>The notebook metadata, including notebook ID, notebook name, and workgroup name.</p>"}, "Payload": {"shape": "Payload", "documentation": "<p>The content of the exported notebook.</p>"}}}, "ExpressionString": {"type": "string", "max": 256, "min": 0}, "FilterDefinition": {"type": "structure", "members": {"Name": {"shape": "NotebookName", "documentation": "<p>The name of the notebook to search for.</p>"}}, "documentation": "<p>A string for searching notebook names.</p>"}, "GetCalculationExecutionCodeRequest": {"type": "structure", "required": ["CalculationExecutionId"], "members": {"CalculationExecutionId": {"shape": "CalculationExecutionId", "documentation": "<p>The calculation execution UUID.</p>"}}}, "GetCalculationExecutionCodeResponse": {"type": "structure", "members": {"CodeBlock": {"shape": "CodeBlock", "documentation": "<p>The unencrypted code that was executed for the calculation.</p>"}}}, "GetCalculationExecutionRequest": {"type": "structure", "required": ["CalculationExecutionId"], "members": {"CalculationExecutionId": {"shape": "CalculationExecutionId", "documentation": "<p>The calculation execution UUID.</p>"}}}, "GetCalculationExecutionResponse": {"type": "structure", "members": {"CalculationExecutionId": {"shape": "CalculationExecutionId", "documentation": "<p>The calculation execution UUID.</p>"}, "SessionId": {"shape": "SessionId", "documentation": "<p>The session ID that the calculation ran in.</p>"}, "Description": {"shape": "DescriptionString", "documentation": "<p>The description of the calculation execution.</p>"}, "WorkingDirectory": {"shape": "S3Uri", "documentation": "<p>The Amazon S3 location in which calculation results are stored.</p>"}, "Status": {"shape": "CalculationStatus", "documentation": "<p>Contains information about the status of the calculation.</p>"}, "Statistics": {"shape": "CalculationStatistics", "documentation": "<p>Contains information about the data processing unit (DPU) execution time and progress. This field is populated only when statistics are available.</p>"}, "Result": {"shape": "CalculationResult", "documentation": "<p>Contains result information. This field is populated only if the calculation is completed.</p>"}}}, "GetCalculationExecutionStatusRequest": {"type": "structure", "required": ["CalculationExecutionId"], "members": {"CalculationExecutionId": {"shape": "CalculationExecutionId", "documentation": "<p>The calculation execution UUID.</p>"}}}, "GetCalculationExecutionStatusResponse": {"type": "structure", "members": {"Status": {"shape": "CalculationStatus", "documentation": "<p>Contains information about the calculation execution status.</p>"}, "Statistics": {"shape": "CalculationStatistics", "documentation": "<p>Contains information about the DPU execution time and progress.</p>"}}}, "GetCapacityAssignmentConfigurationInput": {"type": "structure", "required": ["CapacityReservationName"], "members": {"CapacityReservationName": {"shape": "CapacityReservationName", "documentation": "<p>The name of the capacity reservation to retrieve the capacity assignment configuration for.</p>"}}}, "GetCapacityAssignmentConfigurationOutput": {"type": "structure", "required": ["CapacityAssignmentConfiguration"], "members": {"CapacityAssignmentConfiguration": {"shape": "CapacityAssignmentConfiguration", "documentation": "<p>The requested capacity assignment configuration for the specified capacity reservation.</p>"}}}, "GetCapacityReservationInput": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "CapacityReservationName", "documentation": "<p>The name of the capacity reservation.</p>"}}}, "GetCapacityReservationOutput": {"type": "structure", "required": ["CapacityReservation"], "members": {"CapacityReservation": {"shape": "CapacityReservation", "documentation": "<p>The requested capacity reservation structure.</p>"}}}, "GetDataCatalogInput": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "CatalogNameString", "documentation": "<p>The name of the data catalog to return.</p>"}, "WorkGroup": {"shape": "WorkGroupName", "documentation": "<p>The name of the workgroup. Required if making an IAM Identity Center request.</p>"}}}, "GetDataCatalogOutput": {"type": "structure", "members": {"DataCatalog": {"shape": "DataCatalog", "documentation": "<p>The data catalog returned.</p>"}}}, "GetDatabaseInput": {"type": "structure", "required": ["CatalogName", "DatabaseName"], "members": {"CatalogName": {"shape": "CatalogNameString", "documentation": "<p>The name of the data catalog that contains the database to return.</p>"}, "DatabaseName": {"shape": "NameString", "documentation": "<p>The name of the database to return.</p>"}, "WorkGroup": {"shape": "WorkGroupName", "documentation": "<p>The name of the workgroup for which the metadata is being fetched. Required if requesting an IAM Identity Center enabled Glue Data Catalog.</p>"}}}, "GetDatabaseOutput": {"type": "structure", "members": {"Database": {"shape": "Database", "documentation": "<p>The database returned.</p>"}}}, "GetNamedQueryInput": {"type": "structure", "required": ["NamedQueryId"], "members": {"NamedQueryId": {"shape": "NamedQueryId", "documentation": "<p>The unique ID of the query. Use <a>ListNamedQueries</a> to get query IDs.</p>"}}}, "GetNamedQueryOutput": {"type": "structure", "members": {"NamedQuery": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>Information about the query.</p>"}}}, "GetNotebookMetadataInput": {"type": "structure", "required": ["NotebookId"], "members": {"NotebookId": {"shape": "NotebookId", "documentation": "<p>The ID of the notebook whose metadata is to be retrieved.</p>"}}}, "GetNotebookMetadataOutput": {"type": "structure", "members": {"NotebookMetadata": {"shape": "NotebookMetadata", "documentation": "<p>The metadata that is returned for the specified notebook ID.</p>"}}}, "GetPreparedStatementInput": {"type": "structure", "required": ["StatementName", "WorkGroup"], "members": {"StatementName": {"shape": "StatementName", "documentation": "<p>The name of the prepared statement to retrieve.</p>"}, "WorkGroup": {"shape": "WorkGroupName", "documentation": "<p>The workgroup to which the statement to be retrieved belongs.</p>"}}}, "GetPreparedStatementOutput": {"type": "structure", "members": {"PreparedStatement": {"shape": "PreparedStatement", "documentation": "<p>The name of the prepared statement that was retrieved.</p>"}}}, "GetQueryExecutionInput": {"type": "structure", "required": ["QueryExecutionId"], "members": {"QueryExecutionId": {"shape": "QueryExecutionId", "documentation": "<p>The unique ID of the query execution.</p>"}}}, "GetQueryExecutionOutput": {"type": "structure", "members": {"QueryExecution": {"shape": "QueryExecution", "documentation": "<p>Information about the query execution.</p>"}}}, "GetQueryResultsInput": {"type": "structure", "required": ["QueryExecutionId"], "members": {"QueryExecutionId": {"shape": "QueryExecutionId", "documentation": "<p>The unique ID of the query execution.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>A token generated by the Athena service that specifies where to continue pagination if a previous request was truncated. To obtain the next set of pages, pass in the <code>NextToken</code> from the response object of the previous page call.</p>"}, "MaxResults": {"shape": "MaxQueryResults", "documentation": "<p>The maximum number of results (rows) to return in this request.</p>"}, "QueryResultType": {"shape": "QueryResultType", "documentation": "<p> When you set this to <code>DATA_ROWS</code> or empty, <code>GetQueryResults</code> returns the query results in rows. If set to <code>DATA_MANIFEST</code>, it returns the manifest file in rows. Only the query types <code>CREATE TABLE AS SELECT</code>, <code>UNLOAD</code>, and <code>INSERT</code> can generate a manifest file. If you use <code>DATA_MANIFEST</code> for other query types, the query will fail. </p>"}}}, "GetQueryResultsOutput": {"type": "structure", "members": {"UpdateCount": {"shape": "<PERSON>", "documentation": "<p>The number of rows inserted with a <code>CREATE TABLE AS SELECT</code>, <code>INSERT INTO</code>, or <code>UPDATE</code> statement. </p>"}, "ResultSet": {"shape": "ResultSet", "documentation": "<p>The results of the query execution.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>A token generated by the Athena service that specifies where to continue pagination if a previous request was truncated. To obtain the next set of pages, pass in the <code>NextToken</code> from the response object of the previous page call.</p>"}}}, "GetQueryRuntimeStatisticsInput": {"type": "structure", "required": ["QueryExecutionId"], "members": {"QueryExecutionId": {"shape": "QueryExecutionId", "documentation": "<p>The unique ID of the query execution.</p>"}}}, "GetQueryRuntimeStatisticsOutput": {"type": "structure", "members": {"QueryRuntimeStatistics": {"shape": "QueryRuntimeStatistics", "documentation": "<p>Runtime statistics about the query execution.</p>"}}}, "GetSessionRequest": {"type": "structure", "required": ["SessionId"], "members": {"SessionId": {"shape": "SessionId", "documentation": "<p>The session ID.</p>"}}}, "GetSessionResponse": {"type": "structure", "members": {"SessionId": {"shape": "SessionId", "documentation": "<p>The session ID.</p>"}, "Description": {"shape": "DescriptionString", "documentation": "<p>The session description.</p>"}, "WorkGroup": {"shape": "WorkGroupName", "documentation": "<p>The workgroup to which the session belongs.</p>"}, "EngineVersion": {"shape": "NameString", "documentation": "<p>The engine version used by the session (for example, <code>PySpark engine version 3</code>). You can get a list of engine versions by calling <a>ListEngineVersions</a>.</p>"}, "EngineConfiguration": {"shape": "EngineConfiguration", "documentation": "<p>Contains engine configuration information like DPU usage.</p>"}, "NotebookVersion": {"shape": "NameString", "documentation": "<p>The notebook version.</p>"}, "SessionConfiguration": {"shape": "SessionConfiguration", "documentation": "<p>Contains the workgroup configuration information used by the session.</p>"}, "Status": {"shape": "SessionStatus", "documentation": "<p>Contains information about the status of the session.</p>"}, "Statistics": {"shape": "SessionStatistics", "documentation": "<p>Contains the DPU execution time.</p>"}}}, "GetSessionStatusRequest": {"type": "structure", "required": ["SessionId"], "members": {"SessionId": {"shape": "SessionId", "documentation": "<p>The session ID.</p>"}}}, "GetSessionStatusResponse": {"type": "structure", "members": {"SessionId": {"shape": "SessionId", "documentation": "<p>The session ID.</p>"}, "Status": {"shape": "SessionStatus", "documentation": "<p>Contains information about the status of the session.</p>"}}}, "GetTableMetadataInput": {"type": "structure", "required": ["CatalogName", "DatabaseName", "TableName"], "members": {"CatalogName": {"shape": "CatalogNameString", "documentation": "<p>The name of the data catalog that contains the database and table metadata to return.</p>"}, "DatabaseName": {"shape": "NameString", "documentation": "<p>The name of the database that contains the table metadata to return.</p>"}, "TableName": {"shape": "NameString", "documentation": "<p>The name of the table for which metadata is returned.</p>"}, "WorkGroup": {"shape": "WorkGroupName", "documentation": "<p>The name of the workgroup for which the metadata is being fetched. Required if requesting an IAM Identity Center enabled Glue Data Catalog.</p>"}}}, "GetTableMetadataOutput": {"type": "structure", "members": {"TableMetadata": {"shape": "TableMetadata", "documentation": "<p>An object that contains table metadata.</p>"}}}, "GetWorkGroupInput": {"type": "structure", "required": ["WorkGroup"], "members": {"WorkGroup": {"shape": "WorkGroupName", "documentation": "<p>The name of the workgroup.</p>"}}}, "GetWorkGroupOutput": {"type": "structure", "members": {"WorkGroup": {"shape": "WorkGroup", "documentation": "<p>Information about the workgroup.</p>"}}}, "IdempotencyToken": {"type": "string", "max": 128, "min": 32}, "IdentityCenterApplicationArn": {"type": "string", "max": 255, "min": 0, "pattern": "^arn:(aws|aws-us-gov|aws-cn|aws-iso|aws-iso-b):sso::\\d{12}:application/(sso)?ins-[a-zA-Z0-9-.]{16}/apl-[a-zA-Z0-9]{16}$"}, "IdentityCenterConfiguration": {"type": "structure", "members": {"EnableIdentityCenter": {"shape": "BoxedBoolean", "documentation": "<p>Specifies whether the workgroup is IAM Identity Center supported.</p>"}, "IdentityCenterInstanceArn": {"shape": "IdentityCenterInstanceArn", "documentation": "<p>The IAM Identity Center instance ARN that the workgroup associates to.</p>"}}, "documentation": "<p>Specifies whether the workgroup is IAM Identity Center supported.</p>"}, "IdentityCenterInstanceArn": {"type": "string", "max": 255, "min": 0, "pattern": "^arn:(aws|aws-us-gov|aws-cn|aws-iso|aws-iso-b):sso:::instance/(sso)?ins-[a-zA-Z0-9-.]{16}$"}, "ImportNotebookInput": {"type": "structure", "required": ["WorkGroup", "Name", "Type"], "members": {"WorkGroup": {"shape": "WorkGroupName", "documentation": "<p>The name of the Spark enabled workgroup to import the notebook to.</p>"}, "Name": {"shape": "NotebookName", "documentation": "<p>The name of the notebook to import.</p>"}, "Payload": {"shape": "Payload", "documentation": "<p>The notebook content to be imported. The payload must be in <code>ipynb</code> format.</p>"}, "Type": {"shape": "NotebookType", "documentation": "<p>The notebook content type. Currently, the only valid type is <code>IPYNB</code>.</p>"}, "NotebookS3LocationUri": {"shape": "S3Uri", "documentation": "<p>A URI that specifies the Amazon S3 location of a notebook file in <code>ipynb</code> format.</p>"}, "ClientRequestToken": {"shape": "ClientRequestToken", "documentation": "<p>A unique case-sensitive string used to ensure the request to import the notebook is idempotent (executes only once).</p> <important> <p>This token is listed as not required because Amazon Web Services SDKs (for example the Amazon Web Services SDK for Java) auto-generate the token for you. If you are not using the Amazon Web Services SDK or the Amazon Web Services CLI, you must provide this token or the action will fail.</p> </important>"}}}, "ImportNotebookOutput": {"type": "structure", "members": {"NotebookId": {"shape": "NotebookId", "documentation": "<p>The ID assigned to the imported notebook.</p>"}}}, "Integer": {"type": "integer"}, "InternalServerException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>Indicates a platform issue, which may be due to a transient condition or outage.</p>", "exception": true, "fault": true}, "InvalidRequestException": {"type": "structure", "members": {"AthenaErrorCode": {"shape": "ErrorCode"}, "Message": {"shape": "ErrorMessage"}}, "documentation": "<p>Indicates that something is wrong with the input to the request. For example, a required parameter may be missing or out of range.</p>", "exception": true}, "KeyString": {"type": "string", "max": 255, "min": 1, "pattern": "[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*"}, "KmsKey": {"type": "string", "max": 2048, "min": 1, "pattern": "^arn:aws[a-z\\-]*:kms:([a-z0-9\\-]+):\\d{12}:key/?[a-zA-Z_0-9+=,.@\\-_/]+$|^arn:aws[a-z\\-]*:kms:([a-z0-9\\-]+):\\d{12}:alias/?[a-zA-Z_0-9+=,.@\\-_/]+$|^alias/[a-zA-Z0-9/_-]+$|[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}"}, "ListApplicationDPUSizesInput": {"type": "structure", "members": {"MaxResults": {"shape": "MaxApplicationDPUSizesCount", "documentation": "<p>Specifies the maximum number of results to return.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>A token generated by the Athena service that specifies where to continue pagination if a previous request was truncated.</p>"}}}, "ListApplicationDPUSizesOutput": {"type": "structure", "members": {"ApplicationDPUSizes": {"shape": "ApplicationDPUSizesList", "documentation": "<p>A list of the supported DPU sizes that the application runtime supports.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>A token generated by the Athena service that specifies where to continue pagination if a previous request was truncated. To obtain the next set of pages, pass in the <code>NextToken</code> from the response object of the previous page call.</p>"}}}, "ListCalculationExecutionsRequest": {"type": "structure", "required": ["SessionId"], "members": {"SessionId": {"shape": "SessionId", "documentation": "<p>The session ID.</p>"}, "StateFilter": {"shape": "CalculationExecutionState", "documentation": "<p>A filter for a specific calculation execution state. A description of each state follows.</p> <p> <code>CREATING</code> - The calculation is in the process of being created.</p> <p> <code>CREATED</code> - The calculation has been created and is ready to run.</p> <p> <code>QUEUED</code> - The calculation has been queued for processing.</p> <p> <code>RUNNING</code> - The calculation is running.</p> <p> <code>CANCELING</code> - A request to cancel the calculation has been received and the system is working to stop it.</p> <p> <code>CANCELED</code> - The calculation is no longer running as the result of a cancel request.</p> <p> <code>COMPLETED</code> - The calculation has completed without error.</p> <p> <code>FAILED</code> - The calculation failed and is no longer running.</p>"}, "MaxResults": {"shape": "MaxCalculationsCount", "documentation": "<p>The maximum number of calculation executions to return.</p>"}, "NextToken": {"shape": "SessionManagerToken", "documentation": "<p>A token generated by the Athena service that specifies where to continue pagination if a previous request was truncated. To obtain the next set of pages, pass in the <code>NextToken</code> from the response object of the previous page call.</p>"}}}, "ListCalculationExecutionsResponse": {"type": "structure", "members": {"NextToken": {"shape": "SessionManagerToken", "documentation": "<p>A token generated by the Athena service that specifies where to continue pagination if a previous request was truncated. To obtain the next set of pages, pass in the <code>NextToken</code> from the response object of the previous page call.</p>"}, "Calculations": {"shape": "CalculationsList", "documentation": "<p>A list of <a>CalculationSummary</a> objects.</p>"}}}, "ListCapacityReservationsInput": {"type": "structure", "members": {"NextToken": {"shape": "Token", "documentation": "<p>A token generated by the Athena service that specifies where to continue pagination if a previous request was truncated.</p>"}, "MaxResults": {"shape": "MaxCapacityReservationsCount", "documentation": "<p>Specifies the maximum number of results to return.</p>"}}}, "ListCapacityReservationsOutput": {"type": "structure", "required": ["CapacityReservations"], "members": {"NextToken": {"shape": "Token", "documentation": "<p>A token generated by the Athena service that specifies where to continue pagination if a previous request was truncated. To obtain the next set of pages, pass in the NextToken from the response object of the previous page call.</p>"}, "CapacityReservations": {"shape": "CapacityReservationsList", "documentation": "<p>The capacity reservations for the current account.</p>"}}}, "ListDataCatalogsInput": {"type": "structure", "members": {"NextToken": {"shape": "Token", "documentation": "<p>A token generated by the Athena service that specifies where to continue pagination if a previous request was truncated. To obtain the next set of pages, pass in the NextToken from the response object of the previous page call.</p>"}, "MaxResults": {"shape": "MaxDataCatalogsCount", "documentation": "<p>Specifies the maximum number of data catalogs to return.</p>"}, "WorkGroup": {"shape": "WorkGroupName", "documentation": "<p>The name of the workgroup. Required if making an IAM Identity Center request.</p>"}}}, "ListDataCatalogsOutput": {"type": "structure", "members": {"DataCatalogsSummary": {"shape": "DataCatalogSummaryList", "documentation": "<p>A summary list of data catalogs.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>A token generated by the Athena service that specifies where to continue pagination if a previous request was truncated. To obtain the next set of pages, pass in the NextToken from the response object of the previous page call.</p>"}}}, "ListDatabasesInput": {"type": "structure", "required": ["CatalogName"], "members": {"CatalogName": {"shape": "CatalogNameString", "documentation": "<p>The name of the data catalog that contains the databases to return.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>A token generated by the Athena service that specifies where to continue pagination if a previous request was truncated. To obtain the next set of pages, pass in the <code>NextToken</code> from the response object of the previous page call.</p>"}, "MaxResults": {"shape": "MaxDatabasesCount", "documentation": "<p>Specifies the maximum number of results to return.</p>"}, "WorkGroup": {"shape": "WorkGroupName", "documentation": "<p>The name of the workgroup for which the metadata is being fetched. Required if requesting an IAM Identity Center enabled Glue Data Catalog.</p>"}}}, "ListDatabasesOutput": {"type": "structure", "members": {"DatabaseList": {"shape": "DatabaseList", "documentation": "<p>A list of databases from a data catalog.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>A token generated by the Athena service that specifies where to continue pagination if a previous request was truncated. To obtain the next set of pages, pass in the NextToken from the response object of the previous page call.</p>"}}}, "ListEngineVersionsInput": {"type": "structure", "members": {"NextToken": {"shape": "Token", "documentation": "<p>A token generated by the Athena service that specifies where to continue pagination if a previous request was truncated. To obtain the next set of pages, pass in the <code>NextToken</code> from the response object of the previous page call.</p>"}, "MaxResults": {"shape": "MaxEngineVersionsCount", "documentation": "<p>The maximum number of engine versions to return in this request.</p>"}}}, "ListEngineVersionsOutput": {"type": "structure", "members": {"EngineVersions": {"shape": "EngineVersionsList", "documentation": "<p>A list of engine versions that are available to choose from.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>A token generated by the Athena service that specifies where to continue pagination if a previous request was truncated. To obtain the next set of pages, pass in the <code>NextToken</code> from the response object of the previous page call.</p>"}}}, "ListExecutorsRequest": {"type": "structure", "required": ["SessionId"], "members": {"SessionId": {"shape": "SessionId", "documentation": "<p>The session ID.</p>"}, "ExecutorStateFilter": {"shape": "ExecutorState", "documentation": "<p>A filter for a specific executor state. A description of each state follows.</p> <p> <code>CREATING</code> - The executor is being started, including acquiring resources.</p> <p> <code>CREATED</code> - The executor has been started.</p> <p> <code>REGISTERED</code> - The executor has been registered.</p> <p> <code>TERMINATING</code> - The executor is in the process of shutting down.</p> <p> <code>TERMINATED</code> - The executor is no longer running.</p> <p> <code>FAILED</code> - Due to a failure, the executor is no longer running.</p>"}, "MaxResults": {"shape": "MaxListExecutorsCount", "documentation": "<p>The maximum number of executors to return.</p>"}, "NextToken": {"shape": "SessionManagerToken", "documentation": "<p>A token generated by the Athena service that specifies where to continue pagination if a previous request was truncated. To obtain the next set of pages, pass in the <code>NextToken</code> from the response object of the previous page call.</p>"}}}, "ListExecutorsResponse": {"type": "structure", "required": ["SessionId"], "members": {"SessionId": {"shape": "SessionId", "documentation": "<p>The session ID.</p>"}, "NextToken": {"shape": "SessionManagerToken", "documentation": "<p>A token generated by the Athena service that specifies where to continue pagination if a previous request was truncated. To obtain the next set of pages, pass in the <code>NextToken</code> from the response object of the previous page call.</p>"}, "ExecutorsSummary": {"shape": "ExecutorsSummaryList", "documentation": "<p>Contains summary information about the executor.</p>"}}}, "ListNamedQueriesInput": {"type": "structure", "members": {"NextToken": {"shape": "Token", "documentation": "<p>A token generated by the Athena service that specifies where to continue pagination if a previous request was truncated. To obtain the next set of pages, pass in the <code>NextToken</code> from the response object of the previous page call.</p>"}, "MaxResults": {"shape": "MaxNamedQueriesCount", "documentation": "<p>The maximum number of queries to return in this request.</p>"}, "WorkGroup": {"shape": "WorkGroupName", "documentation": "<p>The name of the workgroup from which the named queries are being returned. If a workgroup is not specified, the saved queries for the primary workgroup are returned.</p>"}}}, "ListNamedQueriesOutput": {"type": "structure", "members": {"NamedQueryIds": {"shape": "NamedQueryIdList", "documentation": "<p>The list of unique query IDs.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>A token generated by the Athena service that specifies where to continue pagination if a previous request was truncated. To obtain the next set of pages, pass in the <code>NextToken</code> from the response object of the previous page call.</p>"}}}, "ListNotebookMetadataInput": {"type": "structure", "required": ["WorkGroup"], "members": {"Filters": {"shape": "FilterDefinition", "documentation": "<p>Search filter string.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>A token generated by the Athena service that specifies where to continue pagination if a previous request was truncated.</p>"}, "MaxResults": {"shape": "MaxNotebooksCount", "documentation": "<p>Specifies the maximum number of results to return.</p>"}, "WorkGroup": {"shape": "WorkGroupName", "documentation": "<p>The name of the Spark enabled workgroup to retrieve notebook metadata for.</p>"}}}, "ListNotebookMetadataOutput": {"type": "structure", "members": {"NextToken": {"shape": "Token", "documentation": "<p>A token generated by the Athena service that specifies where to continue pagination if a previous request was truncated. To obtain the next set of pages, pass in the <code>NextToken</code> from the response object of the previous page call.</p>"}, "NotebookMetadataList": {"shape": "NotebookMetadataArray", "documentation": "<p>The list of notebook metadata for the specified workgroup.</p>"}}}, "ListNotebookSessionsRequest": {"type": "structure", "required": ["NotebookId"], "members": {"NotebookId": {"shape": "NotebookId", "documentation": "<p>The ID of the notebook to list sessions for.</p>"}, "MaxResults": {"shape": "MaxSessionsCount", "documentation": "<p>The maximum number of notebook sessions to return.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>A token generated by the Athena service that specifies where to continue pagination if a previous request was truncated. To obtain the next set of pages, pass in the <code>NextToken</code> from the response object of the previous page call.</p>"}}}, "ListNotebookSessionsResponse": {"type": "structure", "required": ["NotebookSessionsList"], "members": {"NotebookSessionsList": {"shape": "NotebookSessionsList", "documentation": "<p>A list of the sessions belonging to the notebook.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>A token generated by the Athena service that specifies where to continue pagination if a previous request was truncated. To obtain the next set of pages, pass in the <code>NextToken</code> from the response object of the previous page call.</p>"}}}, "ListPreparedStatementsInput": {"type": "structure", "required": ["WorkGroup"], "members": {"WorkGroup": {"shape": "WorkGroupName", "documentation": "<p>The workgroup to list the prepared statements for.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>A token generated by the Athena service that specifies where to continue pagination if a previous request was truncated. To obtain the next set of pages, pass in the <code>NextToken</code> from the response object of the previous page call.</p>"}, "MaxResults": {"shape": "MaxPreparedStatementsCount", "documentation": "<p>The maximum number of results to return in this request.</p>"}}}, "ListPreparedStatementsOutput": {"type": "structure", "members": {"PreparedStatements": {"shape": "PreparedStatementsList", "documentation": "<p>The list of prepared statements for the workgroup.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>A token generated by the Athena service that specifies where to continue pagination if a previous request was truncated. To obtain the next set of pages, pass in the <code>NextToken</code> from the response object of the previous page call.</p>"}}}, "ListQueryExecutionsInput": {"type": "structure", "members": {"NextToken": {"shape": "Token", "documentation": "<p>A token generated by the Athena service that specifies where to continue pagination if a previous request was truncated. To obtain the next set of pages, pass in the <code>NextToken</code> from the response object of the previous page call.</p>"}, "MaxResults": {"shape": "MaxQueryExecutionsCount", "documentation": "<p>The maximum number of query executions to return in this request.</p>"}, "WorkGroup": {"shape": "WorkGroupName", "documentation": "<p>The name of the workgroup from which queries are being returned. If a workgroup is not specified, a list of available query execution IDs for the queries in the primary workgroup is returned.</p>"}}}, "ListQueryExecutionsOutput": {"type": "structure", "members": {"QueryExecutionIds": {"shape": "QueryExecutionIdList", "documentation": "<p>The unique IDs of each query execution as an array of strings.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>A token to be used by the next request if this request is truncated.</p>"}}}, "ListSessionsRequest": {"type": "structure", "required": ["WorkGroup"], "members": {"WorkGroup": {"shape": "WorkGroupName", "documentation": "<p>The workgroup to which the session belongs.</p>"}, "StateFilter": {"shape": "SessionState", "documentation": "<p>A filter for a specific session state. A description of each state follows.</p> <p> <code>CREATING</code> - The session is being started, including acquiring resources.</p> <p> <code>CREATED</code> - The session has been started.</p> <p> <code>IDLE</code> - The session is able to accept a calculation.</p> <p> <code>BUSY</code> - The session is processing another task and is unable to accept a calculation.</p> <p> <code>TERMINATING</code> - The session is in the process of shutting down.</p> <p> <code>TERMINATED</code> - The session and its resources are no longer running.</p> <p> <code>DEGRADED</code> - The session has no healthy coordinators.</p> <p> <code>FAILED</code> - Due to a failure, the session and its resources are no longer running.</p>"}, "MaxResults": {"shape": "MaxSessionsCount", "documentation": "<p>The maximum number of sessions to return.</p>"}, "NextToken": {"shape": "SessionManagerToken", "documentation": "<p>A token generated by the Athena service that specifies where to continue pagination if a previous request was truncated. To obtain the next set of pages, pass in the <code>NextToken</code> from the response object of the previous page call.</p>"}}}, "ListSessionsResponse": {"type": "structure", "members": {"NextToken": {"shape": "SessionManagerToken", "documentation": "<p>A token generated by the Athena service that specifies where to continue pagination if a previous request was truncated. To obtain the next set of pages, pass in the <code>NextToken</code> from the response object of the previous page call.</p>"}, "Sessions": {"shape": "SessionsList", "documentation": "<p>A list of sessions.</p>"}}}, "ListTableMetadataInput": {"type": "structure", "required": ["CatalogName", "DatabaseName"], "members": {"CatalogName": {"shape": "CatalogNameString", "documentation": "<p>The name of the data catalog for which table metadata should be returned.</p>"}, "DatabaseName": {"shape": "NameString", "documentation": "<p>The name of the database for which table metadata should be returned.</p>"}, "Expression": {"shape": "ExpressionString", "documentation": "<p>A regex filter that pattern-matches table names. If no expression is supplied, metadata for all tables are listed.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>A token generated by the Athena service that specifies where to continue pagination if a previous request was truncated. To obtain the next set of pages, pass in the NextToken from the response object of the previous page call.</p>"}, "MaxResults": {"shape": "MaxTableMetadataCount", "documentation": "<p>Specifies the maximum number of results to return.</p>"}, "WorkGroup": {"shape": "WorkGroupName", "documentation": "<p>The name of the workgroup for which the metadata is being fetched. Required if requesting an IAM Identity Center enabled Glue Data Catalog.</p>"}}}, "ListTableMetadataOutput": {"type": "structure", "members": {"TableMetadataList": {"shape": "TableMetadataList", "documentation": "<p>A list of table metadata.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>A token generated by the Athena service that specifies where to continue pagination if a previous request was truncated. To obtain the next set of pages, pass in the NextToken from the response object of the previous page call.</p>"}}}, "ListTagsForResourceInput": {"type": "structure", "required": ["ResourceARN"], "members": {"ResourceARN": {"shape": "AmazonResourceName", "documentation": "<p>Lists the tags for the resource with the specified ARN.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>The token for the next set of results, or null if there are no additional results for this request, where the request lists the tags for the resource with the specified ARN.</p>"}, "MaxResults": {"shape": "MaxTagsCount", "documentation": "<p>The maximum number of results to be returned per request that lists the tags for the resource.</p>"}}}, "ListTagsForResourceOutput": {"type": "structure", "members": {"Tags": {"shape": "TagList", "documentation": "<p>The list of tags associated with the specified resource.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>A token to be used by the next request if this request is truncated.</p>"}}}, "ListWorkGroupsInput": {"type": "structure", "members": {"NextToken": {"shape": "Token", "documentation": "<p>A token generated by the Athena service that specifies where to continue pagination if a previous request was truncated. To obtain the next set of pages, pass in the <code>NextToken</code> from the response object of the previous page call.</p>"}, "MaxResults": {"shape": "MaxWorkGroupsCount", "documentation": "<p>The maximum number of workgroups to return in this request.</p>"}}}, "ListWorkGroupsOutput": {"type": "structure", "members": {"WorkGroups": {"shape": "WorkGroupsList", "documentation": "<p>A list of <a>WorkGroupSummary</a> objects that include the names, descriptions, creation times, and states for each workgroup.</p>"}, "NextToken": {"shape": "Token", "documentation": "<p>A token generated by the Athena service that specifies where to continue pagination if a previous request was truncated. To obtain the next set of pages, pass in the <code>NextToken</code> from the response object of the previous page call.</p>"}}}, "Long": {"type": "long"}, "ManagedQueryResultsConfiguration": {"type": "structure", "required": ["Enabled"], "members": {"Enabled": {"shape": "Boolean", "documentation": "<p>If set to true, allows you to store query results in Athena owned storage. If set to false, workgroup member stores query results in location specified under <code>ResultConfiguration$OutputLocation</code>. The default is false. A workgroup cannot have the <code>ResultConfiguration$OutputLocation</code> parameter when you set this field to true. </p>"}, "EncryptionConfiguration": {"shape": "ManagedQueryResultsEncryptionConfiguration", "documentation": "<p>If you encrypt query and calculation results in Athena owned storage, this field indicates the encryption option (for example, SSE_KMS or CSE_KMS) and key information.</p>"}}, "documentation": "<p> The configuration for storing results in Athena owned storage, which includes whether this feature is enabled; whether encryption configuration, if any, is used for encrypting query results. </p>"}, "ManagedQueryResultsConfigurationUpdates": {"type": "structure", "members": {"Enabled": {"shape": "BoxedBoolean", "documentation": "<p>If set to true, specifies that <PERSON> manages query results in Athena owned storage.</p>"}, "EncryptionConfiguration": {"shape": "ManagedQueryResultsEncryptionConfiguration", "documentation": "<p>If you encrypt query and calculation results in Athena owned storage, this field indicates the encryption option (for example, SSE_KMS or CSE_KMS) and key information.</p>"}, "RemoveEncryptionConfiguration": {"shape": "BoxedBoolean", "documentation": "<p>If set to true, it removes workgroup from Athena owned storage. The existing query results are cleaned up after 24hrs. You must provide query results in location specified under <code>ResultConfiguration$OutputLocation</code>.</p>"}}, "documentation": "<p>Updates the configuration for managed query results.</p>"}, "ManagedQueryResultsEncryptionConfiguration": {"type": "structure", "required": ["KmsKey"], "members": {"KmsKey": {"shape": "KmsKey", "documentation": "<p>The ARN of an KMS key for encrypting managed query results.</p>"}}, "documentation": "<p>If you encrypt query and calculation results in Athena owned storage, this field indicates the encryption option (for example, SSE_KMS or CSE_KMS) and key information.</p>"}, "MaxApplicationDPUSizesCount": {"type": "integer", "max": 100, "min": 1}, "MaxCalculationsCount": {"type": "integer", "box": true, "max": 100, "min": 1}, "MaxCapacityReservationsCount": {"type": "integer", "box": true, "max": 50, "min": 1}, "MaxConcurrentDpus": {"type": "integer", "max": 5000, "min": 2}, "MaxDataCatalogsCount": {"type": "integer", "box": true, "max": 50, "min": 2}, "MaxDatabasesCount": {"type": "integer", "box": true, "max": 50, "min": 1}, "MaxEngineVersionsCount": {"type": "integer", "box": true, "max": 10, "min": 1}, "MaxListExecutorsCount": {"type": "integer", "box": true, "max": 100, "min": 1}, "MaxNamedQueriesCount": {"type": "integer", "box": true, "max": 50, "min": 0}, "MaxNotebooksCount": {"type": "integer", "box": true, "max": 50, "min": 1}, "MaxPreparedStatementsCount": {"type": "integer", "box": true, "max": 50, "min": 1}, "MaxQueryExecutionsCount": {"type": "integer", "box": true, "max": 50, "min": 0}, "MaxQueryResults": {"type": "integer", "box": true, "max": 1000, "min": 1}, "MaxSessionsCount": {"type": "integer", "box": true, "max": 100, "min": 1}, "MaxTableMetadataCount": {"type": "integer", "box": true, "max": 50, "min": 1}, "MaxTagsCount": {"type": "integer", "box": true, "min": 75}, "MaxWorkGroupsCount": {"type": "integer", "box": true, "max": 50, "min": 1}, "MetadataException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>An exception that Athena received when it called a custom metastore. Occurs if the error is not caused by user input (<code>InvalidRequestException</code>) or from the Athena platform (<code>InternalServerException</code>). For example, if a user-created Lambda function is missing permissions, the Lambda <code>4XX</code> exception is returned in a <code>MetadataException</code>.</p>", "exception": true}, "NameString": {"type": "string", "max": 128, "min": 1}, "NamedQuery": {"type": "structure", "required": ["Name", "Database", "QueryString"], "members": {"Name": {"shape": "NameString", "documentation": "<p>The query name.</p>"}, "Description": {"shape": "DescriptionString", "documentation": "<p>The query description.</p>"}, "Database": {"shape": "DatabaseString", "documentation": "<p>The database to which the query belongs.</p>"}, "QueryString": {"shape": "QueryString", "documentation": "<p>The SQL statements that make up the query.</p>"}, "NamedQueryId": {"shape": "NamedQueryId", "documentation": "<p>The unique identifier of the query.</p>"}, "WorkGroup": {"shape": "WorkGroupName", "documentation": "<p>The name of the workgroup that contains the named query.</p>"}}, "documentation": "<p>A query, where <code>QueryString</code> contains the SQL statements that make up the query.</p>"}, "NamedQueryDescriptionString": {"type": "string", "max": 1024, "min": 0}, "NamedQueryId": {"type": "string", "max": 128, "min": 1, "pattern": "\\S+"}, "NamedQueryIdList": {"type": "list", "member": {"shape": "NamedQueryId"}, "max": 50, "min": 1}, "NamedQueryList": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON><PERSON>"}}, "NotebookId": {"type": "string", "max": 36, "min": 1, "pattern": "[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}"}, "NotebookMetadata": {"type": "structure", "members": {"NotebookId": {"shape": "NotebookId", "documentation": "<p>The notebook ID.</p>"}, "Name": {"shape": "NotebookName", "documentation": "<p>The name of the notebook.</p>"}, "WorkGroup": {"shape": "WorkGroupName", "documentation": "<p>The name of the Spark enabled workgroup to which the notebook belongs.</p>"}, "CreationTime": {"shape": "Date", "documentation": "<p>The time when the notebook was created.</p>"}, "Type": {"shape": "NotebookType", "documentation": "<p>The type of notebook. Currently, the only valid type is <code>IPYNB</code>.</p>"}, "LastModifiedTime": {"shape": "Date", "documentation": "<p>The time when the notebook was last modified.</p>"}}, "documentation": "<p>Contains metadata for notebook, including the notebook name, ID, workgroup, and time created.</p>"}, "NotebookMetadataArray": {"type": "list", "member": {"shape": "NotebookMetadata"}}, "NotebookName": {"type": "string", "max": 255, "min": 1, "pattern": "(?!.*[/:\\\\])[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]+"}, "NotebookSessionSummary": {"type": "structure", "members": {"SessionId": {"shape": "SessionId", "documentation": "<p>The notebook session ID.</p>"}, "CreationTime": {"shape": "Date", "documentation": "<p>The time when the notebook session was created.</p>"}}, "documentation": "<p>Contains the notebook session ID and notebook session creation time.</p>"}, "NotebookSessionsList": {"type": "list", "member": {"shape": "NotebookSessionSummary"}, "documentation": "<p>A list of notebook sessions.</p>", "max": 10, "min": 0}, "NotebookType": {"type": "string", "enum": ["IPYNB"]}, "ParametersMap": {"type": "map", "key": {"shape": "KeyString"}, "value": {"shape": "ParametersMapValue"}}, "ParametersMapValue": {"type": "string", "max": 51200}, "Payload": {"type": "string", "max": 10485760, "min": 1}, "PreparedStatement": {"type": "structure", "members": {"StatementName": {"shape": "StatementName", "documentation": "<p>The name of the prepared statement.</p>"}, "QueryStatement": {"shape": "QueryString", "documentation": "<p>The query string for the prepared statement.</p>"}, "WorkGroupName": {"shape": "WorkGroupName", "documentation": "<p>The name of the workgroup to which the prepared statement belongs.</p>"}, "Description": {"shape": "DescriptionString", "documentation": "<p>The description of the prepared statement.</p>"}, "LastModifiedTime": {"shape": "Date", "documentation": "<p>The last modified time of the prepared statement.</p>"}}, "documentation": "<p>A prepared SQL statement for use with Athena.</p>"}, "PreparedStatementDetailsList": {"type": "list", "member": {"shape": "PreparedStatement"}}, "PreparedStatementNameList": {"type": "list", "member": {"shape": "StatementName"}}, "PreparedStatementSummary": {"type": "structure", "members": {"StatementName": {"shape": "StatementName", "documentation": "<p>The name of the prepared statement.</p>"}, "LastModifiedTime": {"shape": "Date", "documentation": "<p>The last modified time of the prepared statement.</p>"}}, "documentation": "<p>The name and last modified time of the prepared statement.</p>"}, "PreparedStatementsList": {"type": "list", "member": {"shape": "PreparedStatementSummary"}, "max": 50, "min": 0}, "PutCapacityAssignmentConfigurationInput": {"type": "structure", "required": ["CapacityReservationName", "CapacityAssignments"], "members": {"CapacityReservationName": {"shape": "CapacityReservationName", "documentation": "<p>The name of the capacity reservation to put a capacity assignment configuration for.</p>"}, "CapacityAssignments": {"shape": "CapacityAssignmentsList", "documentation": "<p>The list of assignments for the capacity assignment configuration.</p>"}}}, "PutCapacityAssignmentConfigurationOutput": {"type": "structure", "members": {}}, "QueryExecution": {"type": "structure", "members": {"QueryExecutionId": {"shape": "QueryExecutionId", "documentation": "<p>The unique identifier for each query execution.</p>"}, "Query": {"shape": "QueryString", "documentation": "<p>The SQL query statements which the query execution ran.</p>"}, "StatementType": {"shape": "StatementType", "documentation": "<p>The type of query statement that was run. <code>DDL</code> indicates DDL query statements. <code>DML</code> indicates DML (Data Manipulation Language) query statements, such as <code>CREATE TABLE AS SELECT</code>. <code>UTILITY</code> indicates query statements other than DDL and DML, such as <code>SHOW CREATE TABLE</code>, or <code>DESCRIBE TABLE</code>.</p>"}, "ManagedQueryResultsConfiguration": {"shape": "ManagedQueryResultsConfiguration", "documentation": "<p> The configuration for storing results in Athena owned storage, which includes whether this feature is enabled; whether encryption configuration, if any, is used for encrypting query results. </p>"}, "ResultConfiguration": {"shape": "ResultConfiguration", "documentation": "<p>The location in Amazon S3 where query and calculation results are stored and the encryption option, if any, used for query results. These are known as \"client-side settings\". If workgroup settings override client-side settings, then the query uses the location for the query results and the encryption configuration that are specified for the workgroup.</p>"}, "ResultReuseConfiguration": {"shape": "ResultReuseConfiguration", "documentation": "<p>Specifies the query result reuse behavior that was used for the query.</p>"}, "QueryExecutionContext": {"shape": "QueryExecutionContext", "documentation": "<p>The database in which the query execution occurred.</p>"}, "Status": {"shape": "QueryExecutionStatus", "documentation": "<p>The completion date, current state, submission time, and state change reason (if applicable) for the query execution.</p>"}, "Statistics": {"shape": "QueryExecutionStatistics", "documentation": "<p>Query execution statistics, such as the amount of data scanned, the amount of time that the query took to process, and the type of statement that was run.</p>"}, "WorkGroup": {"shape": "WorkGroupName", "documentation": "<p>The name of the workgroup in which the query ran.</p>"}, "EngineVersion": {"shape": "EngineVersion", "documentation": "<p>The engine version that executed the query.</p>"}, "ExecutionParameters": {"shape": "ExecutionParameters", "documentation": "<p>A list of values for the parameters in a query. The values are applied sequentially to the parameters in the query in the order in which the parameters occur. The list of parameters is not returned in the response.</p>"}, "SubstatementType": {"shape": "String", "documentation": "<p>The kind of query statement that was run.</p>"}, "QueryResultsS3AccessGrantsConfiguration": {"shape": "QueryResultsS3AccessGrantsConfiguration", "documentation": "<p>Specifies whether Amazon S3 access grants are enabled for query results.</p>"}}, "documentation": "<p>Information about a single instance of a query execution.</p>"}, "QueryExecutionContext": {"type": "structure", "members": {"Database": {"shape": "DatabaseString", "documentation": "<p>The name of the database used in the query execution. The database must exist in the catalog.</p>"}, "Catalog": {"shape": "CatalogNameString", "documentation": "<p>The name of the data catalog used in the query execution.</p>"}}, "documentation": "<p>The database and data catalog context in which the query execution occurs.</p>"}, "QueryExecutionId": {"type": "string", "max": 128, "min": 1, "pattern": "\\S+"}, "QueryExecutionIdList": {"type": "list", "member": {"shape": "QueryExecutionId"}, "max": 50, "min": 1}, "QueryExecutionList": {"type": "list", "member": {"shape": "QueryExecution"}}, "QueryExecutionState": {"type": "string", "enum": ["QUEUED", "RUNNING", "SUCCEEDED", "FAILED", "CANCELLED"]}, "QueryExecutionStatistics": {"type": "structure", "members": {"EngineExecutionTimeInMillis": {"shape": "<PERSON>", "documentation": "<p>The number of milliseconds that the query took to execute.</p>"}, "DataScannedInBytes": {"shape": "<PERSON>", "documentation": "<p>The number of bytes in the data that was queried.</p>"}, "DataManifestLocation": {"shape": "String", "documentation": "<p>The location and file name of a data manifest file. The manifest file is saved to the Athena query results location in Amazon S3. The manifest file tracks files that the query wrote to Amazon S3. If the query fails, the manifest file also tracks files that the query intended to write. The manifest is useful for identifying orphaned files resulting from a failed query. For more information, see <a href=\"https://docs.aws.amazon.com/athena/latest/ug/querying.html\">Working with Query Results, Output Files, and Query History</a> in the <i>Amazon Athena User Guide</i>.</p>"}, "TotalExecutionTimeInMillis": {"shape": "<PERSON>", "documentation": "<p>The number of milliseconds that <PERSON> took to run the query.</p>"}, "QueryQueueTimeInMillis": {"shape": "<PERSON>", "documentation": "<p>The number of milliseconds that the query was in your query queue waiting for resources. Note that if transient errors occur, <PERSON> might automatically add the query back to the queue.</p>"}, "ServicePreProcessingTimeInMillis": {"shape": "<PERSON>", "documentation": "<p>The number of milliseconds that <PERSON> took to preprocess the query before submitting the query to the query engine.</p>"}, "QueryPlanningTimeInMillis": {"shape": "<PERSON>", "documentation": "<p>The number of milliseconds that <PERSON> took to plan the query processing flow. This includes the time spent retrieving table partitions from the data source. Note that because the query engine performs the query planning, query planning time is a subset of engine processing time.</p>"}, "ServiceProcessingTimeInMillis": {"shape": "<PERSON>", "documentation": "<p>The number of milliseconds that <PERSON> took to finalize and publish the query results after the query engine finished running the query.</p>"}, "ResultReuseInformation": {"shape": "ResultReuseInformation", "documentation": "<p>Contains information about whether previous query results were reused for the query.</p>"}}, "documentation": "<p>The amount of data scanned during the query execution and the amount of time that it took to execute, and the type of statement that was run.</p>"}, "QueryExecutionStatus": {"type": "structure", "members": {"State": {"shape": "QueryExecutionState", "documentation": "<p>The state of query execution. <code>QUEUED</code> indicates that the query has been submitted to the service, and Athena will execute the query as soon as resources are available. <code>RUNNING</code> indicates that the query is in execution phase. <code>SUCCEEDED</code> indicates that the query completed without errors. <code>FAILED</code> indicates that the query experienced an error and did not complete processing. <code>CANCELLED</code> indicates that a user input interrupted query execution.</p> <note> <p>Athena automatically retries your queries in cases of certain transient errors. As a result, you may see the query state transition from <code>RUNNING</code> or <code>FAILED</code> to <code>QUEUED</code>. </p> </note>"}, "StateChangeReason": {"shape": "String", "documentation": "<p>Further detail about the status of the query.</p>"}, "SubmissionDateTime": {"shape": "Date", "documentation": "<p>The date and time that the query was submitted.</p>"}, "CompletionDateTime": {"shape": "Date", "documentation": "<p>The date and time that the query completed.</p>"}, "AthenaError": {"shape": "AthenaError", "documentation": "<p>Provides information about an Athena query error.</p>"}}, "documentation": "<p>The completion date, current state, submission time, and state change reason (if applicable) for the query execution.</p>"}, "QueryResultType": {"type": "string", "enum": ["DATA_MANIFEST", "DATA_ROWS"]}, "QueryResultsS3AccessGrantsConfiguration": {"type": "structure", "required": ["EnableS3AccessGrants", "AuthenticationType"], "members": {"EnableS3AccessGrants": {"shape": "BoxedBoolean", "documentation": "<p>Specifies whether Amazon S3 access grants are enabled for query results.</p>"}, "CreateUserLevelPrefix": {"shape": "BoxedBoolean", "documentation": "<p>When enabled, appends the user ID as an Amazon S3 path prefix to the query result output location.</p>"}, "AuthenticationType": {"shape": "AuthenticationType", "documentation": "<p>The authentication type used for Amazon S3 access grants. Currently, only <code>DIRECTORY_IDENTITY</code> is supported.</p>"}}, "documentation": "<p>Specifies whether Amazon S3 access grants are enabled for query results.</p>"}, "QueryRuntimeStatistics": {"type": "structure", "members": {"Timeline": {"shape": "QueryRuntimeStatisticsTimeline"}, "Rows": {"shape": "QueryRuntimeStatisticsRows"}, "OutputStage": {"shape": "QueryStage", "documentation": "<p>Stage statistics such as input and output rows and bytes, execution time, and stage state. This information also includes substages and the query stage plan.</p>"}}, "documentation": "<p>The query execution timeline, statistics on input and output rows and bytes, and the different query stages that form the query execution plan.</p>"}, "QueryRuntimeStatisticsRows": {"type": "structure", "members": {"InputRows": {"shape": "<PERSON>", "documentation": "<p>The number of rows read to execute the query.</p>"}, "InputBytes": {"shape": "<PERSON>", "documentation": "<p>The number of bytes read to execute the query.</p>"}, "OutputBytes": {"shape": "<PERSON>", "documentation": "<p>The number of bytes returned by the query.</p>"}, "OutputRows": {"shape": "<PERSON>", "documentation": "<p>The number of rows returned by the query.</p>"}}, "documentation": "<p>Statistics such as input rows and bytes read by the query, rows and bytes output by the query, and the number of rows written by the query.</p>"}, "QueryRuntimeStatisticsTimeline": {"type": "structure", "members": {"QueryQueueTimeInMillis": {"shape": "<PERSON>", "documentation": "<p>The number of milliseconds that the query was in your query queue waiting for resources. Note that if transient errors occur, <PERSON> might automatically add the query back to the queue.</p>"}, "ServicePreProcessingTimeInMillis": {"shape": "<PERSON>", "documentation": "<p> The number of milliseconds that <PERSON> spends on preprocessing before it submits the query to the engine. </p>"}, "QueryPlanningTimeInMillis": {"shape": "<PERSON>", "documentation": "<p>The number of milliseconds that <PERSON> took to plan the query processing flow. This includes the time spent retrieving table partitions from the data source. Note that because the query engine performs the query planning, query planning time is a subset of engine processing time.</p>"}, "EngineExecutionTimeInMillis": {"shape": "<PERSON>", "documentation": "<p>The number of milliseconds that the query took to execute.</p>"}, "ServiceProcessingTimeInMillis": {"shape": "<PERSON>", "documentation": "<p>The number of milliseconds that <PERSON> took to finalize and publish the query results after the query engine finished running the query.</p>"}, "TotalExecutionTimeInMillis": {"shape": "<PERSON>", "documentation": "<p>The number of milliseconds that <PERSON> took to run the query.</p>"}}, "documentation": "<p>Timeline statistics such as query queue time, planning time, execution time, service processing time, and total execution time.</p>"}, "QueryStage": {"type": "structure", "members": {"StageId": {"shape": "<PERSON>", "documentation": "<p>The identifier for a stage.</p>"}, "State": {"shape": "String", "documentation": "<p>State of the stage after query execution.</p>"}, "OutputBytes": {"shape": "<PERSON>", "documentation": "<p>The number of bytes output from the stage after execution.</p>"}, "OutputRows": {"shape": "<PERSON>", "documentation": "<p>The number of rows output from the stage after execution.</p>"}, "InputBytes": {"shape": "<PERSON>", "documentation": "<p>The number of bytes input into the stage for execution.</p>"}, "InputRows": {"shape": "<PERSON>", "documentation": "<p>The number of rows input into the stage for execution.</p>"}, "ExecutionTime": {"shape": "<PERSON>", "documentation": "<p>Time taken to execute this stage.</p>"}, "QueryStagePlan": {"shape": "QueryStagePlanNode", "documentation": "<p>Stage plan information such as name, identifier, sub plans, and source stages.</p>"}, "SubStages": {"shape": "QueryStages", "documentation": "<p>List of sub query stages that form this stage execution plan.</p>"}}, "documentation": "<p>Stage statistics such as input and output rows and bytes, execution time and stage state. This information also includes substages and the query stage plan.</p>"}, "QueryStagePlanNode": {"type": "structure", "members": {"Name": {"shape": "String", "documentation": "<p>Name of the query stage plan that describes the operation this stage is performing as part of query execution.</p>"}, "Identifier": {"shape": "String", "documentation": "<p>Information about the operation this query stage plan node is performing.</p>"}, "Children": {"shape": "QueryStagePlanNodes", "documentation": "<p>Stage plan information such as name, identifier, sub plans, and remote sources of child plan nodes/</p>"}, "RemoteSources": {"shape": "StringList", "documentation": "<p>Source plan node IDs.</p>"}}, "documentation": "<p>Stage plan information such as name, identifier, sub plans, and remote sources.</p>"}, "QueryStagePlanNodes": {"type": "list", "member": {"shape": "QueryStagePlanNode"}}, "QueryStages": {"type": "list", "member": {"shape": "QueryStage"}}, "QueryString": {"type": "string", "max": 262144, "min": 1}, "ResourceNotFoundException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}, "ResourceName": {"shape": "AmazonResourceName", "documentation": "<p>The name of the Amazon resource.</p>"}}, "documentation": "<p>A resource, such as a workgroup, was not found.</p>", "exception": true}, "ResultConfiguration": {"type": "structure", "members": {"OutputLocation": {"shape": "ResultOutputLocation", "documentation": "<p>The location in Amazon S3 where your query and calculation results are stored, such as <code>s3://path/to/query/bucket/</code>. To run the query, you must specify the query results location using one of the ways: either for individual queries using either this setting (client-side), or in the workgroup, using <a>WorkGroupConfiguration</a>. If none of them is set, Athena issues an error that no output location is provided. If workgroup settings override client-side settings, then the query uses the settings specified for the workgroup. See <a>WorkGroupConfiguration$EnforceWorkGroupConfiguration</a>.</p>"}, "EncryptionConfiguration": {"shape": "EncryptionConfiguration", "documentation": "<p>If query and calculation results are encrypted in Amazon S3, indicates the encryption option used (for example, <code>SSE_KMS</code> or <code>CSE_KMS</code>) and key information. This is a client-side setting. If workgroup settings override client-side settings, then the query uses the encryption configuration that is specified for the workgroup, and also uses the location for storing query results specified in the workgroup. See <a>WorkGroupConfiguration$EnforceWorkGroupConfiguration</a> and <a href=\"https://docs.aws.amazon.com/athena/latest/ug/workgroups-settings-override.html\">Workgroup Settings Override Client-Side Settings</a>.</p>"}, "ExpectedBucketOwner": {"shape": "AwsAccountId", "documentation": "<p>The Amazon Web Services account ID that you expect to be the owner of the Amazon S3 bucket specified by <a>ResultConfiguration$OutputLocation</a>. If set, <PERSON> uses the value for <code>ExpectedBucketOwner</code> when it makes Amazon S3 calls to your specified output location. If the <code>ExpectedBucketOwner</code> Amazon Web Services account ID does not match the actual owner of the Amazon S3 bucket, the call fails with a permissions error.</p> <p>This is a client-side setting. If workgroup settings override client-side settings, then the query uses the <code>ExpectedBucketOwner</code> setting that is specified for the workgroup, and also uses the location for storing query results specified in the workgroup. See <a>WorkGroupConfiguration$EnforceWorkGroupConfiguration</a> and <a href=\"https://docs.aws.amazon.com/athena/latest/ug/workgroups-settings-override.html\">Workgroup Settings Override Client-Side Settings</a>.</p>"}, "AclConfiguration": {"shape": "AclConfiguration", "documentation": "<p>Indicates that an Amazon S3 canned ACL should be set to control ownership of stored query results. Currently the only supported canned ACL is <code>BUCKET_OWNER_FULL_CONTROL</code>. This is a client-side setting. If workgroup settings override client-side settings, then the query uses the ACL configuration that is specified for the workgroup, and also uses the location for storing query results specified in the workgroup. For more information, see <a>WorkGroupConfiguration$EnforceWorkGroupConfiguration</a> and <a href=\"https://docs.aws.amazon.com/athena/latest/ug/workgroups-settings-override.html\">Workgroup Settings Override Client-Side Settings</a>.</p>"}}, "documentation": "<p>The location in Amazon S3 where query and calculation results are stored and the encryption option, if any, used for query and calculation results. These are known as \"client-side settings\". If workgroup settings override client-side settings, then the query uses the workgroup settings.</p>"}, "ResultConfigurationUpdates": {"type": "structure", "members": {"OutputLocation": {"shape": "ResultOutputLocation", "documentation": "<p>The location in Amazon S3 where your query and calculation results are stored, such as <code>s3://path/to/query/bucket/</code>. If workgroup settings override client-side settings, then the query uses the location for the query results and the encryption configuration that are specified for the workgroup. The \"workgroup settings override\" is specified in <code>EnforceWorkGroupConfiguration</code> (true/false) in the <code>WorkGroupConfiguration</code>. See <a>WorkGroupConfiguration$EnforceWorkGroupConfiguration</a>.</p>"}, "RemoveOutputLocation": {"shape": "BoxedBoolean", "documentation": "<p>If set to \"true\", indicates that the previously-specified query results location (also known as a client-side setting) for queries in this workgroup should be ignored and set to null. If set to \"false\" or not set, and a value is present in the <code>OutputLocation</code> in <code>ResultConfigurationUpdates</code> (the client-side setting), the <code>OutputLocation</code> in the workgroup's <code>ResultConfiguration</code> will be updated with the new value. For more information, see <a href=\"https://docs.aws.amazon.com/athena/latest/ug/workgroups-settings-override.html\">Workgroup Settings Override Client-Side Settings</a>.</p>"}, "EncryptionConfiguration": {"shape": "EncryptionConfiguration", "documentation": "<p>The encryption configuration for query and calculation results.</p>"}, "RemoveEncryptionConfiguration": {"shape": "BoxedBoolean", "documentation": "<p>If set to \"true\", indicates that the previously-specified encryption configuration (also known as the client-side setting) for queries in this workgroup should be ignored and set to null. If set to \"false\" or not set, and a value is present in the <code>EncryptionConfiguration</code> in <code>ResultConfigurationUpdates</code> (the client-side setting), the <code>EncryptionConfiguration</code> in the workgroup's <code>ResultConfiguration</code> will be updated with the new value. For more information, see <a href=\"https://docs.aws.amazon.com/athena/latest/ug/workgroups-settings-override.html\">Workgroup Settings Override Client-Side Settings</a>.</p>"}, "ExpectedBucketOwner": {"shape": "AwsAccountId", "documentation": "<p>The Amazon Web Services account ID that you expect to be the owner of the Amazon S3 bucket specified by <a>ResultConfiguration$OutputLocation</a>. If set, <PERSON> uses the value for <code>ExpectedBucketOwner</code> when it makes Amazon S3 calls to your specified output location. If the <code>ExpectedBucketOwner</code> Amazon Web Services account ID does not match the actual owner of the Amazon S3 bucket, the call fails with a permissions error.</p> <p>If workgroup settings override client-side settings, then the query uses the <code>ExpectedBucketOwner</code> setting that is specified for the workgroup, and also uses the location for storing query results specified in the workgroup. See <a>WorkGroupConfiguration$EnforceWorkGroupConfiguration</a> and <a href=\"https://docs.aws.amazon.com/athena/latest/ug/workgroups-settings-override.html\">Workgroup Settings Override Client-Side Settings</a>.</p>"}, "RemoveExpectedBucketOwner": {"shape": "BoxedBoolean", "documentation": "<p>If set to \"true\", removes the Amazon Web Services account ID previously specified for <a>ResultConfiguration$ExpectedBucketOwner</a>. If set to \"false\" or not set, and a value is present in the <code>ExpectedBucketOwner</code> in <code>ResultConfigurationUpdates</code> (the client-side setting), the <code>ExpectedBucketOwner</code> in the workgroup's <code>ResultConfiguration</code> is updated with the new value. For more information, see <a href=\"https://docs.aws.amazon.com/athena/latest/ug/workgroups-settings-override.html\">Workgroup Settings Override Client-Side Settings</a>.</p>"}, "AclConfiguration": {"shape": "AclConfiguration", "documentation": "<p>The ACL configuration for the query results.</p>"}, "RemoveAclConfiguration": {"shape": "BoxedBoolean", "documentation": "<p>If set to <code>true</code>, indicates that the previously-specified ACL configuration for queries in this workgroup should be ignored and set to null. If set to <code>false</code> or not set, and a value is present in the <code>AclConfiguration</code> of <code>ResultConfigurationUpdates</code>, the <code>AclConfiguration</code> in the workgroup's <code>ResultConfiguration</code> is updated with the new value. For more information, see <a href=\"https://docs.aws.amazon.com/athena/latest/ug/workgroups-settings-override.html\">Workgroup Settings Override Client-Side Settings</a>.</p>"}}, "documentation": "<p>The information about the updates in the query results, such as output location and encryption configuration for the query results.</p>"}, "ResultOutputLocation": {"type": "string"}, "ResultReuseByAgeConfiguration": {"type": "structure", "required": ["Enabled"], "members": {"Enabled": {"shape": "Boolean", "documentation": "<p>True if previous query results can be reused when the query is run; otherwise, false. The default is false.</p>"}, "MaxAgeInMinutes": {"shape": "Age", "documentation": "<p>Specifies, in minutes, the maximum age of a previous query result that <PERSON> should consider for reuse. The default is 60.</p>"}}, "documentation": "<p>Specifies whether previous query results are reused, and if so, their maximum age.</p>"}, "ResultReuseConfiguration": {"type": "structure", "members": {"ResultReuseByAgeConfiguration": {"shape": "ResultReuseByAgeConfiguration", "documentation": "<p>Specifies whether previous query results are reused, and if so, their maximum age.</p>"}}, "documentation": "<p>Specifies the query result reuse behavior for the query.</p>"}, "ResultReuseInformation": {"type": "structure", "required": ["ReusedPreviousResult"], "members": {"ReusedPreviousResult": {"shape": "Boolean", "documentation": "<p>True if a previous query result was reused; false if the result was generated from a new run of the query.</p>"}}, "documentation": "<p>Contains information about whether the result of a previous query was reused.</p>"}, "ResultSet": {"type": "structure", "members": {"Rows": {"shape": "RowList", "documentation": "<p>The rows in the table.</p>"}, "ResultSetMetadata": {"shape": "ResultSetMetadata", "documentation": "<p>The metadata that describes the column structure and data types of a table of query results.</p>"}}, "documentation": "<p>The metadata and rows that make up a query result set. The metadata describes the column structure and data types. To return a <code>ResultSet</code> object, use <a>GetQueryResults</a>.</p>"}, "ResultSetMetadata": {"type": "structure", "members": {"ColumnInfo": {"shape": "ColumnInfoList", "documentation": "<p>Information about the columns returned in a query result metadata.</p>"}}, "documentation": "<p>The metadata that describes the column structure and data types of a table of query results. To return a <code>ResultSetMetadata</code> object, use <a>GetQueryResults</a>.</p>"}, "RoleArn": {"type": "string", "max": 2048, "min": 20, "pattern": "^arn:aws[a-z\\-]*:iam::\\d{12}:role/?[a-zA-Z_0-9+=,.@\\-_/]+$"}, "Row": {"type": "structure", "members": {"Data": {"shape": "datumList", "documentation": "<p>The data that populates a row in a query result table.</p>"}}, "documentation": "<p>The rows that make up a query result table.</p>"}, "RowList": {"type": "list", "member": {"shape": "Row"}}, "S3AclOption": {"type": "string", "enum": ["BUCKET_OWNER_FULL_CONTROL"]}, "S3Uri": {"type": "string", "max": 1024, "pattern": "^(https|s3|S3)://([^/]+)/?(.*)$"}, "SessionAlreadyExistsException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The specified session already exists.</p>", "exception": true}, "SessionConfiguration": {"type": "structure", "members": {"ExecutionRole": {"shape": "RoleArn", "documentation": "<p>The ARN of the execution role used to access user resources for Spark sessions and Identity Center enabled workgroups. This property applies only to Spark enabled workgroups and Identity Center enabled workgroups.</p>"}, "WorkingDirectory": {"shape": "ResultOutputLocation", "documentation": "<p>The Amazon S3 location that stores information for the notebook.</p>"}, "IdleTimeoutSeconds": {"shape": "<PERSON>", "documentation": "<p>The idle timeout in seconds for the session.</p>"}, "EncryptionConfiguration": {"shape": "EncryptionConfiguration"}}, "documentation": "<p>Contains session configuration information.</p>"}, "SessionId": {"type": "string", "max": 256, "min": 1}, "SessionIdleTimeoutInMinutes": {"type": "integer", "box": true, "max": 480, "min": 1}, "SessionManagerToken": {"type": "string", "max": 2048}, "SessionState": {"type": "string", "enum": ["CREATING", "CREATED", "IDLE", "BUSY", "TERMINATING", "TERMINATED", "DEGRADED", "FAILED"]}, "SessionStatistics": {"type": "structure", "members": {"DpuExecutionInMillis": {"shape": "<PERSON>", "documentation": "<p>The data processing unit execution time for a session in milliseconds.</p>"}}, "documentation": "<p>Contains statistics for a session.</p>"}, "SessionStatus": {"type": "structure", "members": {"StartDateTime": {"shape": "Date", "documentation": "<p>The date and time that the session started.</p>"}, "LastModifiedDateTime": {"shape": "Date", "documentation": "<p>The most recent date and time that the session was modified.</p>"}, "EndDateTime": {"shape": "Date", "documentation": "<p>The date and time that the session ended.</p>"}, "IdleSinceDateTime": {"shape": "Date", "documentation": "<p>The date and time starting at which the session became idle. Can be empty if the session is not currently idle.</p>"}, "State": {"shape": "SessionState", "documentation": "<p>The state of the session. A description of each state follows.</p> <p> <code>CREATING</code> - The session is being started, including acquiring resources.</p> <p> <code>CREATED</code> - The session has been started.</p> <p> <code>IDLE</code> - The session is able to accept a calculation.</p> <p> <code>BUSY</code> - The session is processing another task and is unable to accept a calculation.</p> <p> <code>TERMINATING</code> - The session is in the process of shutting down.</p> <p> <code>TERMINATED</code> - The session and its resources are no longer running.</p> <p> <code>DEGRADED</code> - The session has no healthy coordinators.</p> <p> <code>FAILED</code> - Due to a failure, the session and its resources are no longer running.</p>"}, "StateChangeReason": {"shape": "DescriptionString", "documentation": "<p>The reason for the session state change (for example, canceled because the session was terminated).</p>"}}, "documentation": "<p>Contains information about the status of a session.</p>"}, "SessionSummary": {"type": "structure", "members": {"SessionId": {"shape": "SessionId", "documentation": "<p>The session ID.</p>"}, "Description": {"shape": "DescriptionString", "documentation": "<p>The session description.</p>"}, "EngineVersion": {"shape": "EngineVersion", "documentation": "<p>The engine version used by the session (for example, <code>PySpark engine version 3</code>).</p>"}, "NotebookVersion": {"shape": "NameString", "documentation": "<p>The notebook version.</p>"}, "Status": {"shape": "SessionStatus", "documentation": "<p>Contains information about the session status.</p>"}}, "documentation": "<p>Contains summary information about a session.</p>"}, "SessionsList": {"type": "list", "member": {"shape": "SessionSummary"}, "max": 100, "min": 0}, "StartCalculationExecutionRequest": {"type": "structure", "required": ["SessionId"], "members": {"SessionId": {"shape": "SessionId", "documentation": "<p>The session ID.</p>"}, "Description": {"shape": "DescriptionString", "documentation": "<p>A description of the calculation.</p>"}, "CalculationConfiguration": {"shape": "CalculationConfiguration", "documentation": "<p>Contains configuration information for the calculation.</p>", "deprecated": true, "deprecatedMessage": "Structure is deprecated."}, "CodeBlock": {"shape": "CodeBlock", "documentation": "<p>A string that contains the code of the calculation. Use this parameter instead of <a>CalculationConfiguration$CodeBlock</a>, which is deprecated.</p>"}, "ClientRequestToken": {"shape": "IdempotencyToken", "documentation": "<p>A unique case-sensitive string used to ensure the request to create the calculation is idempotent (executes only once). If another <code>StartCalculationExecutionRequest</code> is received, the same response is returned and another calculation is not created. If a parameter has changed, an error is returned.</p> <important> <p>This token is listed as not required because Amazon Web Services SDKs (for example the Amazon Web Services SDK for Java) auto-generate the token for users. If you are not using the Amazon Web Services SDK or the Amazon Web Services CLI, you must provide this token or the action will fail.</p> </important>"}}}, "StartCalculationExecutionResponse": {"type": "structure", "members": {"CalculationExecutionId": {"shape": "CalculationExecutionId", "documentation": "<p>The calculation execution UUID.</p>"}, "State": {"shape": "CalculationExecutionState", "documentation": "<p> <code>CREATING</code> - The calculation is in the process of being created.</p> <p> <code>CREATED</code> - The calculation has been created and is ready to run.</p> <p> <code>QUEUED</code> - The calculation has been queued for processing.</p> <p> <code>RUNNING</code> - The calculation is running.</p> <p> <code>CANCELING</code> - A request to cancel the calculation has been received and the system is working to stop it.</p> <p> <code>CANCELED</code> - The calculation is no longer running as the result of a cancel request.</p> <p> <code>COMPLETED</code> - The calculation has completed without error.</p> <p> <code>FAILED</code> - The calculation failed and is no longer running.</p>"}}}, "StartQueryExecutionInput": {"type": "structure", "required": ["QueryString"], "members": {"QueryString": {"shape": "QueryString", "documentation": "<p>The SQL query statements to be executed.</p>"}, "ClientRequestToken": {"shape": "IdempotencyToken", "documentation": "<p>A unique case-sensitive string used to ensure the request to create the query is idempotent (executes only once). If another <code>StartQueryExecution</code> request is received, the same response is returned and another query is not created. An error is returned if a parameter, such as <code>QueryString</code>, has changed. A call to <code>StartQueryExecution</code> that uses a previous client request token returns the same <code>QueryExecutionId</code> even if the requester doesn't have permission on the tables specified in <code>QueryString</code>.</p> <important> <p>This token is listed as not required because Amazon Web Services SDKs (for example the Amazon Web Services SDK for Java) auto-generate the token for users. If you are not using the Amazon Web Services SDK or the Amazon Web Services CLI, you must provide this token or the action will fail.</p> </important>", "idempotencyToken": true}, "QueryExecutionContext": {"shape": "QueryExecutionContext", "documentation": "<p>The database within which the query executes.</p>"}, "ResultConfiguration": {"shape": "ResultConfiguration", "documentation": "<p>Specifies information about where and how to save the results of the query execution. If the query runs in a workgroup, then workgroup's settings may override query settings. This affects the query results location. The workgroup settings override is specified in EnforceWorkGroupConfiguration (true/false) in the WorkGroupConfiguration. See <a>WorkGroupConfiguration$EnforceWorkGroupConfiguration</a>.</p>"}, "WorkGroup": {"shape": "WorkGroupName", "documentation": "<p>The name of the workgroup in which the query is being started.</p>"}, "ExecutionParameters": {"shape": "ExecutionParameters", "documentation": "<p>A list of values for the parameters in a query. The values are applied sequentially to the parameters in the query in the order in which the parameters occur.</p>"}, "ResultReuseConfiguration": {"shape": "ResultReuseConfiguration", "documentation": "<p>Specifies the query result reuse behavior for the query.</p>"}}}, "StartQueryExecutionOutput": {"type": "structure", "members": {"QueryExecutionId": {"shape": "QueryExecutionId", "documentation": "<p>The unique ID of the query that ran as a result of this request.</p>"}}}, "StartSessionRequest": {"type": "structure", "required": ["WorkGroup", "EngineConfiguration"], "members": {"Description": {"shape": "DescriptionString", "documentation": "<p>The session description.</p>"}, "WorkGroup": {"shape": "WorkGroupName", "documentation": "<p>The workgroup to which the session belongs.</p>"}, "EngineConfiguration": {"shape": "EngineConfiguration", "documentation": "<p>Contains engine data processing unit (DPU) configuration settings and parameter mappings.</p>"}, "NotebookVersion": {"shape": "NameString", "documentation": "<p>The notebook version. This value is supplied automatically for notebook sessions in the Athena console and is not required for programmatic session access. The only valid notebook version is <code>Athena notebook version 1</code>. If you specify a value for <code>NotebookVersion</code>, you must also specify a value for <code>NotebookId</code>. See <a>EngineConfiguration$AdditionalConfigs</a>.</p>"}, "SessionIdleTimeoutInMinutes": {"shape": "SessionIdleTimeoutInMinutes", "documentation": "<p>The idle timeout in minutes for the session.</p>"}, "ClientRequestToken": {"shape": "IdempotencyToken", "documentation": "<p>A unique case-sensitive string used to ensure the request to create the session is idempotent (executes only once). If another <code>StartSessionRequest</code> is received, the same response is returned and another session is not created. If a parameter has changed, an error is returned.</p> <important> <p>This token is listed as not required because Amazon Web Services SDKs (for example the Amazon Web Services SDK for Java) auto-generate the token for users. If you are not using the Amazon Web Services SDK or the Amazon Web Services CLI, you must provide this token or the action will fail.</p> </important>"}}}, "StartSessionResponse": {"type": "structure", "members": {"SessionId": {"shape": "SessionId", "documentation": "<p>The session ID.</p>"}, "State": {"shape": "SessionState", "documentation": "<p>The state of the session. A description of each state follows.</p> <p> <code>CREATING</code> - The session is being started, including acquiring resources.</p> <p> <code>CREATED</code> - The session has been started.</p> <p> <code>IDLE</code> - The session is able to accept a calculation.</p> <p> <code>BUSY</code> - The session is processing another task and is unable to accept a calculation.</p> <p> <code>TERMINATING</code> - The session is in the process of shutting down.</p> <p> <code>TERMINATED</code> - The session and its resources are no longer running.</p> <p> <code>DEGRADED</code> - The session has no healthy coordinators.</p> <p> <code>FAILED</code> - Due to a failure, the session and its resources are no longer running.</p>"}}}, "StatementName": {"type": "string", "max": 256, "min": 1, "pattern": "[a-zA-Z_][a-zA-Z0-9_@:]{1,256}"}, "StatementType": {"type": "string", "enum": ["DDL", "DML", "UTILITY"]}, "StopCalculationExecutionRequest": {"type": "structure", "required": ["CalculationExecutionId"], "members": {"CalculationExecutionId": {"shape": "CalculationExecutionId", "documentation": "<p>The calculation execution UUID.</p>"}}}, "StopCalculationExecutionResponse": {"type": "structure", "members": {"State": {"shape": "CalculationExecutionState", "documentation": "<p> <code>CREATING</code> - The calculation is in the process of being created.</p> <p> <code>CREATED</code> - The calculation has been created and is ready to run.</p> <p> <code>QUEUED</code> - The calculation has been queued for processing.</p> <p> <code>RUNNING</code> - The calculation is running.</p> <p> <code>CANCELING</code> - A request to cancel the calculation has been received and the system is working to stop it.</p> <p> <code>CANCELED</code> - The calculation is no longer running as the result of a cancel request.</p> <p> <code>COMPLETED</code> - The calculation has completed without error.</p> <p> <code>FAILED</code> - The calculation failed and is no longer running.</p>"}}}, "StopQueryExecutionInput": {"type": "structure", "required": ["QueryExecutionId"], "members": {"QueryExecutionId": {"shape": "QueryExecutionId", "documentation": "<p>The unique ID of the query execution to stop.</p>", "idempotencyToken": true}}}, "StopQueryExecutionOutput": {"type": "structure", "members": {}}, "String": {"type": "string"}, "StringList": {"type": "list", "member": {"shape": "String"}}, "SupportedDPUSizeList": {"type": "list", "member": {"shape": "Integer"}}, "TableMetadata": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "NameString", "documentation": "<p>The name of the table.</p>"}, "CreateTime": {"shape": "Timestamp", "documentation": "<p>The time that the table was created.</p>"}, "LastAccessTime": {"shape": "Timestamp", "documentation": "<p>The last time the table was accessed.</p>"}, "TableType": {"shape": "TableTypeString", "documentation": "<p>The type of table. In Athena, only <code>EXTERNAL_TABLE</code> is supported.</p>"}, "Columns": {"shape": "ColumnList", "documentation": "<p>A list of the columns in the table.</p>"}, "PartitionKeys": {"shape": "ColumnList", "documentation": "<p>A list of the partition keys in the table.</p>"}, "Parameters": {"shape": "ParametersMap", "documentation": "<p>A set of custom key/value pairs for table properties.</p>"}}, "documentation": "<p>Contains metadata for a table.</p>"}, "TableMetadataList": {"type": "list", "member": {"shape": "TableMetadata"}}, "TableTypeString": {"type": "string", "max": 255}, "Tag": {"type": "structure", "members": {"Key": {"shape": "TagKey", "documentation": "<p>A tag key. The tag key length is from 1 to 128 Unicode characters in UTF-8. You can use letters and numbers representable in UTF-8, and the following characters: + - = . _ : / @. Tag keys are case-sensitive and must be unique per resource. </p>"}, "Value": {"shape": "TagValue", "documentation": "<p>A tag value. The tag value length is from 0 to 256 Unicode characters in UTF-8. You can use letters and numbers representable in UTF-8, and the following characters: + - = . _ : / @. Tag values are case-sensitive. </p>"}}, "documentation": "<p>A label that you assign to a resource. Athena resources include workgroups, data catalogs, and capacity reservations. Each tag consists of a key and an optional value, both of which you define. For example, you can use tags to categorize Athena resources by purpose, owner, or environment. Use a consistent set of tag keys to make it easier to search and filter the resources in your account. For best practices, see <a href=\"https://docs.aws.amazon.com/whitepapers/latest/tagging-best-practices/tagging-best-practices.html\">Tagging Best Practices</a>. Tag keys can be from 1 to 128 UTF-8 Unicode characters, and tag values can be from 0 to 256 UTF-8 Unicode characters. Tags can use letters and numbers representable in UTF-8, and the following characters: + - = . _ : / @. Tag keys and values are case-sensitive. Tag keys must be unique per resource. If you specify more than one tag, separate them by commas. </p>"}, "TagKey": {"type": "string", "max": 128, "min": 1}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}}, "TagList": {"type": "list", "member": {"shape": "Tag"}}, "TagResourceInput": {"type": "structure", "required": ["ResourceARN", "Tags"], "members": {"ResourceARN": {"shape": "AmazonResourceName", "documentation": "<p>Specifies the ARN of the Athena resource to which tags are to be added.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>A collection of one or more tags, separated by commas, to be added to an Athena resource.</p>"}}}, "TagResourceOutput": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0}, "TargetDpusInteger": {"type": "integer", "box": true, "min": 24}, "TerminateSessionRequest": {"type": "structure", "required": ["SessionId"], "members": {"SessionId": {"shape": "SessionId", "documentation": "<p>The session ID.</p>"}}}, "TerminateSessionResponse": {"type": "structure", "members": {"State": {"shape": "SessionState", "documentation": "<p>The state of the session. A description of each state follows.</p> <p> <code>CREATING</code> - The session is being started, including acquiring resources.</p> <p> <code>CREATED</code> - The session has been started.</p> <p> <code>IDLE</code> - The session is able to accept a calculation.</p> <p> <code>BUSY</code> - The session is processing another task and is unable to accept a calculation.</p> <p> <code>TERMINATING</code> - The session is in the process of shutting down.</p> <p> <code>TERMINATED</code> - The session and its resources are no longer running.</p> <p> <code>DEGRADED</code> - The session has no healthy coordinators.</p> <p> <code>FAILED</code> - Due to a failure, the session and its resources are no longer running.</p>"}}}, "ThrottleReason": {"type": "string", "documentation": "<p>The reason for the query throttling, for example, when it exceeds the concurrent query limit.</p>", "enum": ["CONCURRENT_QUERY_LIMIT_EXCEEDED"]}, "Timestamp": {"type": "timestamp"}, "Token": {"type": "string", "max": 1024, "min": 1}, "TooManyRequestsException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}, "Reason": {"shape": "ThrottleReason"}}, "documentation": "<p>Indicates that the request was throttled.</p>", "exception": true}, "TypeString": {"type": "string", "max": 4096, "min": 0, "pattern": "[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*"}, "UnprocessedNamedQueryId": {"type": "structure", "members": {"NamedQueryId": {"shape": "NamedQueryId", "documentation": "<p>The unique identifier of the named query.</p>"}, "ErrorCode": {"shape": "ErrorCode", "documentation": "<p>The error code returned when the processing request for the named query failed, if applicable.</p>"}, "ErrorMessage": {"shape": "ErrorMessage", "documentation": "<p>The error message returned when the processing request for the named query failed, if applicable.</p>"}}, "documentation": "<p>Information about a named query ID that could not be processed.</p>"}, "UnprocessedNamedQueryIdList": {"type": "list", "member": {"shape": "UnprocessedNamedQueryId"}}, "UnprocessedPreparedStatementName": {"type": "structure", "members": {"StatementName": {"shape": "StatementName", "documentation": "<p>The name of a prepared statement that could not be returned due to an error.</p>"}, "ErrorCode": {"shape": "ErrorCode", "documentation": "<p>The error code returned when the request for the prepared statement failed.</p>"}, "ErrorMessage": {"shape": "ErrorMessage", "documentation": "<p>The error message containing the reason why the prepared statement could not be returned. The following error messages are possible:</p> <ul> <li> <p> <code>INVALID_INPUT</code> - The name of the prepared statement that was provided is not valid (for example, the name is too long).</p> </li> <li> <p> <code>STATEMENT_NOT_FOUND</code> - A prepared statement with the name provided could not be found.</p> </li> <li> <p> <code>UNAUTHORIZED</code> - The requester does not have permission to access the workgroup that contains the prepared statement.</p> </li> </ul>"}}, "documentation": "<p>The name of a prepared statement that could not be returned.</p>"}, "UnprocessedPreparedStatementNameList": {"type": "list", "member": {"shape": "UnprocessedPreparedStatementName"}}, "UnprocessedQueryExecutionId": {"type": "structure", "members": {"QueryExecutionId": {"shape": "QueryExecutionId", "documentation": "<p>The unique identifier of the query execution.</p>"}, "ErrorCode": {"shape": "ErrorCode", "documentation": "<p>The error code returned when the query execution failed to process, if applicable.</p>"}, "ErrorMessage": {"shape": "ErrorMessage", "documentation": "<p>The error message returned when the query execution failed to process, if applicable.</p>"}}, "documentation": "<p>Describes a query execution that failed to process.</p>"}, "UnprocessedQueryExecutionIdList": {"type": "list", "member": {"shape": "UnprocessedQueryExecutionId"}}, "UntagResourceInput": {"type": "structure", "required": ["ResourceARN", "TagKeys"], "members": {"ResourceARN": {"shape": "AmazonResourceName", "documentation": "<p>Specifies the ARN of the resource from which tags are to be removed.</p>"}, "TagKeys": {"shape": "TagKeyList", "documentation": "<p>A comma-separated list of one or more tag keys whose tags are to be removed from the specified resource.</p>"}}}, "UntagResourceOutput": {"type": "structure", "members": {}}, "UpdateCapacityReservationInput": {"type": "structure", "required": ["TargetDpus", "Name"], "members": {"TargetDpus": {"shape": "TargetDpusInteger", "documentation": "<p>The new number of requested data processing units.</p>"}, "Name": {"shape": "CapacityReservationName", "documentation": "<p>The name of the capacity reservation.</p>"}}}, "UpdateCapacityReservationOutput": {"type": "structure", "members": {}}, "UpdateDataCatalogInput": {"type": "structure", "required": ["Name", "Type"], "members": {"Name": {"shape": "CatalogNameString", "documentation": "<p>The name of the data catalog to update. The catalog name must be unique for the Amazon Web Services account and can use a maximum of 127 alphanumeric, underscore, at sign, or hyphen characters. The remainder of the length constraint of 256 is reserved for use by Athena.</p>"}, "Type": {"shape": "DataCatalogType", "documentation": "<p>Specifies the type of data catalog to update. Specify <code>LAMBDA</code> for a federated catalog, <code>HIVE</code> for an external hive metastore, or <code>GLUE</code> for an Glue Data Catalog.</p>"}, "Description": {"shape": "DescriptionString", "documentation": "<p>New or modified text that describes the data catalog.</p>"}, "Parameters": {"shape": "ParametersMap", "documentation": "<p>Specifies the Lambda function or functions to use for updating the data catalog. This is a mapping whose values depend on the catalog type. </p> <ul> <li> <p>For the <code>HIVE</code> data catalog type, use the following syntax. The <code>metadata-function</code> parameter is required. <code>The sdk-version</code> parameter is optional and defaults to the currently supported version.</p> <p> <code>metadata-function=<i>lambda_arn</i>, sdk-version=<i>version_number</i> </code> </p> </li> <li> <p>For the <code>LAMBDA</code> data catalog type, use one of the following sets of required parameters, but not both.</p> <ul> <li> <p>If you have one Lambda function that processes metadata and another for reading the actual data, use the following syntax. Both parameters are required.</p> <p> <code>metadata-function=<i>lambda_arn</i>, record-function=<i>lambda_arn</i> </code> </p> </li> <li> <p> If you have a composite Lambda function that processes both metadata and data, use the following syntax to specify your Lambda function.</p> <p> <code>function=<i>lambda_arn</i> </code> </p> </li> </ul> </li> </ul>"}}}, "UpdateDataCatalogOutput": {"type": "structure", "members": {}}, "UpdateNamedQueryInput": {"type": "structure", "required": ["NamedQueryId", "Name", "QueryString"], "members": {"NamedQueryId": {"shape": "NamedQueryId", "documentation": "<p>The unique identifier (UUID) of the query.</p>"}, "Name": {"shape": "NameString", "documentation": "<p>The name of the query.</p>"}, "Description": {"shape": "NamedQueryDescriptionString", "documentation": "<p>The query description.</p>"}, "QueryString": {"shape": "QueryString", "documentation": "<p>The contents of the query with all query statements.</p>"}}}, "UpdateNamedQueryOutput": {"type": "structure", "members": {}}, "UpdateNotebookInput": {"type": "structure", "required": ["NotebookId", "Payload", "Type"], "members": {"NotebookId": {"shape": "NotebookId", "documentation": "<p>The ID of the notebook to update.</p>"}, "Payload": {"shape": "Payload", "documentation": "<p>The updated content for the notebook.</p>"}, "Type": {"shape": "NotebookType", "documentation": "<p>The notebook content type. Currently, the only valid type is <code>IPYNB</code>.</p>"}, "SessionId": {"shape": "SessionId", "documentation": "<p>The active notebook session ID. Required if the notebook has an active session.</p>"}, "ClientRequestToken": {"shape": "ClientRequestToken", "documentation": "<p>A unique case-sensitive string used to ensure the request to create the notebook is idempotent (executes only once).</p> <important> <p>This token is listed as not required because Amazon Web Services SDKs (for example the Amazon Web Services SDK for Java) auto-generate the token for you. If you are not using the Amazon Web Services SDK or the Amazon Web Services CLI, you must provide this token or the action will fail.</p> </important>"}}}, "UpdateNotebookMetadataInput": {"type": "structure", "required": ["NotebookId", "Name"], "members": {"NotebookId": {"shape": "NotebookId", "documentation": "<p>The ID of the notebook to update the metadata for.</p>"}, "ClientRequestToken": {"shape": "ClientRequestToken", "documentation": "<p>A unique case-sensitive string used to ensure the request to create the notebook is idempotent (executes only once).</p> <important> <p>This token is listed as not required because Amazon Web Services SDKs (for example the Amazon Web Services SDK for Java) auto-generate the token for you. If you are not using the Amazon Web Services SDK or the Amazon Web Services CLI, you must provide this token or the action will fail.</p> </important>"}, "Name": {"shape": "NotebookName", "documentation": "<p>The name to update the notebook to.</p>"}}}, "UpdateNotebookMetadataOutput": {"type": "structure", "members": {}}, "UpdateNotebookOutput": {"type": "structure", "members": {}}, "UpdatePreparedStatementInput": {"type": "structure", "required": ["StatementName", "WorkGroup", "QueryStatement"], "members": {"StatementName": {"shape": "StatementName", "documentation": "<p>The name of the prepared statement.</p>"}, "WorkGroup": {"shape": "WorkGroupName", "documentation": "<p>The workgroup for the prepared statement.</p>"}, "QueryStatement": {"shape": "QueryString", "documentation": "<p>The query string for the prepared statement.</p>"}, "Description": {"shape": "DescriptionString", "documentation": "<p>The description of the prepared statement.</p>"}}}, "UpdatePreparedStatementOutput": {"type": "structure", "members": {}}, "UpdateWorkGroupInput": {"type": "structure", "required": ["WorkGroup"], "members": {"WorkGroup": {"shape": "WorkGroupName", "documentation": "<p>The specified workgroup that will be updated.</p>"}, "Description": {"shape": "WorkGroupDescriptionString", "documentation": "<p>The workgroup description.</p>"}, "ConfigurationUpdates": {"shape": "WorkGroupConfigurationUpdates", "documentation": "<p>Contains configuration updates for an Athena SQL workgroup.</p>"}, "State": {"shape": "WorkGroupState", "documentation": "<p>The workgroup state that will be updated for the given workgroup.</p>"}}}, "UpdateWorkGroupOutput": {"type": "structure", "members": {}}, "WorkGroup": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "WorkGroupName", "documentation": "<p>The workgroup name.</p>"}, "State": {"shape": "WorkGroupState", "documentation": "<p>The state of the workgroup: ENABLED or DISABLED.</p>"}, "Configuration": {"shape": "WorkGroupConfiguration", "documentation": "<p>The configuration of the workgroup, which includes the location in Amazon S3 where query and calculation results are stored, the encryption configuration, if any, used for query and calculation results; whether the Amazon CloudWatch Metrics are enabled for the workgroup; whether workgroup settings override client-side settings; and the data usage limits for the amount of data scanned per query or per workgroup. The workgroup settings override is specified in <code>EnforceWorkGroupConfiguration</code> (true/false) in the <code>WorkGroupConfiguration</code>. See <a>WorkGroupConfiguration$EnforceWorkGroupConfiguration</a>.</p>"}, "Description": {"shape": "WorkGroupDescriptionString", "documentation": "<p>The workgroup description.</p>"}, "CreationTime": {"shape": "Date", "documentation": "<p>The date and time the workgroup was created.</p>"}, "IdentityCenterApplicationArn": {"shape": "IdentityCenterApplicationArn", "documentation": "<p>The ARN of the IAM Identity Center enabled application associated with the workgroup.</p>"}}, "documentation": "<p>A workgroup, which contains a name, description, creation time, state, and other configuration, listed under <a>WorkGroup$Configuration</a>. Each workgroup enables you to isolate queries for you or your group of users from other queries in the same account, to configure the query results location and the encryption configuration (known as workgroup settings), to enable sending query metrics to Amazon CloudWatch, and to establish per-query data usage control limits for all queries in a workgroup. The workgroup settings override is specified in <code>EnforceWorkGroupConfiguration</code> (true/false) in the <code>WorkGroupConfiguration</code>. See <a>WorkGroupConfiguration$EnforceWorkGroupConfiguration</a>.</p>"}, "WorkGroupConfiguration": {"type": "structure", "members": {"ResultConfiguration": {"shape": "ResultConfiguration", "documentation": "<p>The configuration for the workgroup, which includes the location in Amazon S3 where query and calculation results are stored and the encryption option, if any, used for query and calculation results. To run the query, you must specify the query results location using one of the ways: either in the workgroup using this setting, or for individual queries (client-side), using <a>ResultConfiguration$OutputLocation</a>. If none of them is set, Athena issues an error that no output location is provided.</p>"}, "ManagedQueryResultsConfiguration": {"shape": "ManagedQueryResultsConfiguration", "documentation": "<p> The configuration for storing results in Athena owned storage, which includes whether this feature is enabled; whether encryption configuration, if any, is used for encrypting query results. </p>"}, "EnforceWorkGroupConfiguration": {"shape": "BoxedBoolean", "documentation": "<p>If set to \"true\", the settings for the workgroup override client-side settings. If set to \"false\", client-side settings are used. For more information, see <a href=\"https://docs.aws.amazon.com/athena/latest/ug/workgroups-settings-override.html\">Workgroup Settings Override Client-Side Settings</a>.</p>"}, "PublishCloudWatchMetricsEnabled": {"shape": "BoxedBoolean", "documentation": "<p>Indicates that the Amazon CloudWatch metrics are enabled for the workgroup.</p>"}, "BytesScannedCutoffPerQuery": {"shape": "BytesScannedCutoffValue", "documentation": "<p>The upper data usage limit (cutoff) for the amount of bytes a single query in a workgroup is allowed to scan.</p>"}, "RequesterPaysEnabled": {"shape": "BoxedBoolean", "documentation": "<p>If set to <code>true</code>, allows members assigned to a workgroup to reference Amazon S3 Requester Pays buckets in queries. If set to <code>false</code>, workgroup members cannot query data from Requester Pays buckets, and queries that retrieve data from Requester Pays buckets cause an error. The default is <code>false</code>. For more information about Requester Pays buckets, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/RequesterPaysBuckets.html\">Requester Pays Buckets</a> in the <i>Amazon Simple Storage Service Developer Guide</i>.</p>"}, "EngineVersion": {"shape": "EngineVersion", "documentation": "<p>The engine version that all queries running on the workgroup use. Queries on the <code>AmazonAthenaPreviewFunctionality</code> workgroup run on the preview engine regardless of this setting.</p>"}, "AdditionalConfiguration": {"shape": "NameString", "documentation": "<p>Specifies a user defined JSON string that is passed to the notebook engine.</p>"}, "ExecutionRole": {"shape": "RoleArn", "documentation": "<p>The ARN of the execution role used to access user resources for Spark sessions and IAM Identity Center enabled workgroups. This property applies only to Spark enabled workgroups and IAM Identity Center enabled workgroups. The property is required for IAM Identity Center enabled workgroups.</p>"}, "CustomerContentEncryptionConfiguration": {"shape": "CustomerContentEncryptionConfiguration", "documentation": "<p>Specifies the KMS key that is used to encrypt the user's data stores in Athena. This setting does not apply to Athena SQL workgroups.</p>"}, "EnableMinimumEncryptionConfiguration": {"shape": "BoxedBoolean", "documentation": "<p>Enforces a minimal level of encryption for the workgroup for query and calculation results that are written to Amazon S3. When enabled, workgroup users can set encryption only to the minimum level set by the administrator or higher when they submit queries.</p> <p>The <code>EnforceWorkGroupConfiguration</code> setting takes precedence over the <code>EnableMinimumEncryptionConfiguration</code> flag. This means that if <code>EnforceWorkGroupConfiguration</code> is true, the <code>EnableMinimumEncryptionConfiguration</code> flag is ignored, and the workgroup configuration for encryption is used.</p>"}, "IdentityCenterConfiguration": {"shape": "IdentityCenterConfiguration", "documentation": "<p>Specifies whether the workgroup is IAM Identity Center supported.</p>"}, "QueryResultsS3AccessGrantsConfiguration": {"shape": "QueryResultsS3AccessGrantsConfiguration", "documentation": "<p>Specifies whether Amazon S3 access grants are enabled for query results.</p>"}}, "documentation": "<p>The configuration of the workgroup, which includes the location in Amazon S3 where query and calculation results are stored, the encryption option, if any, used for query and calculation results, whether the Amazon CloudWatch Metrics are enabled for the workgroup and whether workgroup settings override query settings, and the data usage limits for the amount of data scanned per query or per workgroup. The workgroup settings override is specified in <code>EnforceWorkGroupConfiguration</code> (true/false) in the <code>WorkGroupConfiguration</code>. See <a>WorkGroupConfiguration$EnforceWorkGroupConfiguration</a>. </p>"}, "WorkGroupConfigurationUpdates": {"type": "structure", "members": {"EnforceWorkGroupConfiguration": {"shape": "BoxedBoolean", "documentation": "<p>If set to \"true\", the settings for the workgroup override client-side settings. If set to \"false\" client-side settings are used. For more information, see <a href=\"https://docs.aws.amazon.com/athena/latest/ug/workgroups-settings-override.html\">Workgroup Settings Override Client-Side Settings</a>.</p>"}, "ResultConfigurationUpdates": {"shape": "ResultConfigurationUpdates", "documentation": "<p>The result configuration information about the queries in this workgroup that will be updated. Includes the updated results location and an updated option for encrypting query results.</p>"}, "ManagedQueryResultsConfigurationUpdates": {"shape": "ManagedQueryResultsConfigurationUpdates", "documentation": "<p>Updates configuration information for managed query results in the workgroup.</p>"}, "PublishCloudWatchMetricsEnabled": {"shape": "BoxedBoolean", "documentation": "<p>Indicates whether this workgroup enables publishing metrics to Amazon CloudWatch.</p>"}, "BytesScannedCutoffPerQuery": {"shape": "BytesScannedCutoffValue", "documentation": "<p>The upper limit (cutoff) for the amount of bytes a single query in a workgroup is allowed to scan.</p>"}, "RemoveBytesScannedCutoffPerQuery": {"shape": "BoxedBoolean", "documentation": "<p>Indicates that the data usage control limit per query is removed. <a>WorkGroupConfiguration$BytesScannedCutoffPerQuery</a> </p>"}, "RequesterPaysEnabled": {"shape": "BoxedBoolean", "documentation": "<p>If set to <code>true</code>, allows members assigned to a workgroup to specify Amazon S3 Requester Pays buckets in queries. If set to <code>false</code>, workgroup members cannot query data from Requester Pays buckets, and queries that retrieve data from Requester Pays buckets cause an error. The default is <code>false</code>. For more information about Requester Pays buckets, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/RequesterPaysBuckets.html\">Requester Pays Buckets</a> in the <i>Amazon Simple Storage Service Developer Guide</i>.</p>"}, "EngineVersion": {"shape": "EngineVersion", "documentation": "<p>The engine version requested when a workgroup is updated. After the update, all queries on the workgroup run on the requested engine version. If no value was previously set, the default is Auto. Queries on the <code>AmazonAthenaPreviewFunctionality</code> workgroup run on the preview engine regardless of this setting.</p>"}, "RemoveCustomerContentEncryptionConfiguration": {"shape": "BoxedBoolean", "documentation": "<p>Removes content encryption configuration from an Apache Spark-enabled Athena workgroup.</p>"}, "AdditionalConfiguration": {"shape": "NameString", "documentation": "<p>Contains a user defined string in JSON format for a Spark-enabled workgroup.</p>"}, "ExecutionRole": {"shape": "RoleArn", "documentation": "<p>The ARN of the execution role used to access user resources for Spark sessions and Identity Center enabled workgroups. This property applies only to Spark enabled workgroups and Identity Center enabled workgroups.</p>"}, "CustomerContentEncryptionConfiguration": {"shape": "CustomerContentEncryptionConfiguration"}, "EnableMinimumEncryptionConfiguration": {"shape": "BoxedBoolean", "documentation": "<p>Enforces a minimal level of encryption for the workgroup for query and calculation results that are written to Amazon S3. When enabled, workgroup users can set encryption only to the minimum level set by the administrator or higher when they submit queries. This setting does not apply to Spark-enabled workgroups.</p> <p>The <code>EnforceWorkGroupConfiguration</code> setting takes precedence over the <code>EnableMinimumEncryptionConfiguration</code> flag. This means that if <code>EnforceWorkGroupConfiguration</code> is true, the <code>EnableMinimumEncryptionConfiguration</code> flag is ignored, and the workgroup configuration for encryption is used.</p>"}, "QueryResultsS3AccessGrantsConfiguration": {"shape": "QueryResultsS3AccessGrantsConfiguration", "documentation": "<p>Specifies whether Amazon S3 access grants are enabled for query results.</p>"}}, "documentation": "<p>The configuration information that will be updated for this workgroup, which includes the location in Amazon S3 where query and calculation results are stored, the encryption option, if any, used for query results, whether the Amazon CloudWatch Metrics are enabled for the workgroup, whether the workgroup settings override the client-side settings, and the data usage limit for the amount of bytes scanned per query, if it is specified.</p>"}, "WorkGroupDescriptionString": {"type": "string", "max": 1024, "min": 0}, "WorkGroupName": {"type": "string", "pattern": "[a-zA-Z0-9._-]{1,128}"}, "WorkGroupNamesList": {"type": "list", "member": {"shape": "WorkGroupName"}}, "WorkGroupState": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "WorkGroupSummary": {"type": "structure", "members": {"Name": {"shape": "WorkGroupName", "documentation": "<p>The name of the workgroup.</p>"}, "State": {"shape": "WorkGroupState", "documentation": "<p>The state of the workgroup.</p>"}, "Description": {"shape": "WorkGroupDescriptionString", "documentation": "<p>The workgroup description.</p>"}, "CreationTime": {"shape": "Date", "documentation": "<p>The workgroup creation date and time.</p>"}, "EngineVersion": {"shape": "EngineVersion", "documentation": "<p>The engine version setting for all queries on the workgroup. Queries on the <code>AmazonAthenaPreviewFunctionality</code> workgroup run on the preview engine regardless of this setting.</p>"}, "IdentityCenterApplicationArn": {"shape": "IdentityCenterApplicationArn", "documentation": "<p>The ARN of the IAM Identity Center enabled application associated with the workgroup.</p>"}}, "documentation": "<p>The summary information for the workgroup, which includes its name, state, description, and the date and time it was created.</p>"}, "WorkGroupsList": {"type": "list", "member": {"shape": "WorkGroupSummary"}, "max": 50, "min": 0}, "datumList": {"type": "list", "member": {"shape": "Datum"}}, "datumString": {"type": "string"}}, "documentation": "<p>Amazon Athena is an interactive query service that lets you use standard SQL to analyze data directly in Amazon S3. You can point <PERSON> at your data in Amazon S3 and run ad-hoc queries and get results in seconds. Athena is serverless, so there is no infrastructure to set up or manage. You pay only for the queries you run. Athena scales automatically—executing queries in parallel—so results are fast, even with large datasets and complex queries. For more information, see <a href=\"http://docs.aws.amazon.com/athena/latest/ug/what-is.html\">What is Amazon Athena</a> in the <i>Amazon Athena User Guide</i>.</p> <p>If you connect to Athena using the JDBC driver, use version 1.1.0 of the driver or later with the Amazon Athena API. Earlier version drivers do not support the API. For more information and to download the driver, see <a href=\"https://docs.aws.amazon.com/athena/latest/ug/connect-with-jdbc.html\">Accessing Amazon Athena with JDBC</a>.</p>"}