{"version": "1.0", "resources": {"SigningJob": {"operation": "ListSigningJobs", "resourceIdentifier": {"jobId": "jobs[].jobId", "source": "jobs[].source"}}, "SigningPlatform": {"operation": "ListSigningPlatforms", "resourceIdentifier": {"partner": "platforms[].partner", "target": "platforms[].target", "category": "platforms[].category"}}, "SigningProfile": {"operation": "ListSigningProfiles", "resourceIdentifier": {"profileName": "profiles[].profileName", "signingMaterial": "profiles[].signingMaterial", "platformId": "profiles[].platformId", "signingParameters": "profiles[].signingParameters", "status": "profiles[].status"}}}, "operations": {"CancelSigningProfile": {"profileName": {"completions": [{"parameters": {}, "resourceName": "SigningProfile", "resourceIdentifier": "profileName"}]}}, "DescribeSigningJob": {"jobId": {"completions": [{"parameters": {}, "resourceName": "SigningJob", "resourceIdentifier": "jobId"}]}}, "GetSigningPlatform": {"platformId": {"completions": [{"parameters": {}, "resourceName": "SigningProfile", "resourceIdentifier": "platformId"}]}}, "GetSigningProfile": {"profileName": {"completions": [{"parameters": {}, "resourceName": "SigningProfile", "resourceIdentifier": "profileName"}]}}, "ListSigningJobs": {"status": {"completions": [{"parameters": {}, "resourceName": "SigningProfile", "resourceIdentifier": "status"}]}, "platformId": {"completions": [{"parameters": {}, "resourceName": "SigningProfile", "resourceIdentifier": "platformId"}]}}, "ListSigningPlatforms": {"category": {"completions": [{"parameters": {}, "resourceName": "SigningPlatform", "resourceIdentifier": "category"}]}, "partner": {"completions": [{"parameters": {}, "resourceName": "SigningPlatform", "resourceIdentifier": "partner"}]}, "target": {"completions": [{"parameters": {}, "resourceName": "SigningPlatform", "resourceIdentifier": "target"}]}}, "PutSigningProfile": {"profileName": {"completions": [{"parameters": {}, "resourceName": "SigningProfile", "resourceIdentifier": "profileName"}]}, "signingMaterial": {"completions": [{"parameters": {}, "resourceName": "SigningProfile", "resourceIdentifier": "signingMaterial"}]}, "platformId": {"completions": [{"parameters": {}, "resourceName": "SigningProfile", "resourceIdentifier": "platformId"}]}, "signingParameters": {"completions": [{"parameters": {}, "resourceName": "SigningProfile", "resourceIdentifier": "signingParameters"}]}}, "StartSigningJob": {"source": {"completions": [{"parameters": {}, "resourceName": "SigningJob", "resourceIdentifier": "source"}]}, "profileName": {"completions": [{"parameters": {}, "resourceName": "SigningProfile", "resourceIdentifier": "profileName"}]}}}}