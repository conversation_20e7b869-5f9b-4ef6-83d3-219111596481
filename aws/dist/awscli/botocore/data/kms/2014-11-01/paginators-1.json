{"pagination": {"ListAliases": {"limit_key": "Limit", "input_token": "<PERSON><PERSON>", "output_token": "NextMarker", "more_results": "Truncated", "result_key": "Aliases"}, "ListGrants": {"limit_key": "Limit", "input_token": "<PERSON><PERSON>", "output_token": "NextMarker", "more_results": "Truncated", "result_key": "<PERSON>s"}, "ListKeyPolicies": {"limit_key": "Limit", "input_token": "<PERSON><PERSON>", "output_token": "NextMarker", "more_results": "Truncated", "result_key": "PolicyNames"}, "ListKeys": {"limit_key": "Limit", "input_token": "<PERSON><PERSON>", "output_token": "NextMarker", "more_results": "Truncated", "result_key": "Keys"}, "DescribeCustomKeyStores": {"input_token": "<PERSON><PERSON>", "limit_key": "Limit", "more_results": "Truncated", "output_token": "NextMarker", "result_key": "CustomKeyStores"}, "ListResourceTags": {"input_token": "<PERSON><PERSON>", "limit_key": "Limit", "more_results": "Truncated", "output_token": "NextMarker", "result_key": "Tags"}, "ListRetirableGrants": {"input_token": "<PERSON><PERSON>", "limit_key": "Limit", "more_results": "Truncated", "output_token": "NextMarker", "result_key": "<PERSON>s"}, "ListKeyRotations": {"input_token": "<PERSON><PERSON>", "limit_key": "Limit", "more_results": "Truncated", "output_token": "NextMarker", "result_key": "Rotations"}}}