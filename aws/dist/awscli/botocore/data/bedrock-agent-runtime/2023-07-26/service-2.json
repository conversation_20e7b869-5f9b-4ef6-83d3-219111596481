{"version": "2.0", "metadata": {"apiVersion": "2023-07-26", "endpointPrefix": "bedrock-agent-runtime", "jsonVersion": "1.1", "protocol": "rest-json", "protocols": ["rest-json"], "serviceFullName": "Agents for Amazon Bedrock Runtime", "serviceId": "Bedrock Agent Runtime", "signatureVersion": "v4", "signingName": "bedrock", "uid": "bedrock-agent-runtime-2023-07-26", "auth": ["aws.auth#sigv4"]}, "operations": {"CreateInvocation": {"name": "CreateInvocation", "http": {"method": "PUT", "requestUri": "/sessions/{sessionIdentifier}/invocations/", "responseCode": 201}, "input": {"shape": "CreateInvocationRequest"}, "output": {"shape": "CreateInvocationResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates a new invocation within a session. An invocation groups the related invocation steps that store the content from a conversation. For more information about sessions, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/sessions.html\">Store and retrieve conversation history and context with Amazon Bedrock sessions</a>.</p> <p>Related APIs</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_ListInvocations.html\">ListInvocations</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_ListSessions.html\">ListSessions</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_GetSession.html\">GetSession</a> </p> </li> </ul>", "idempotent": true}, "CreateSession": {"name": "CreateSession", "http": {"method": "PUT", "requestUri": "/sessions/", "responseCode": 201}, "input": {"shape": "CreateSessionRequest"}, "output": {"shape": "CreateSessionResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates a session to temporarily store conversations for generative AI (GenAI) applications built with open-source frameworks such as LangGraph and LlamaIndex. Sessions enable you to save the state of conversations at checkpoints, with the added security and infrastructure of Amazon Web Services. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/sessions.html\">Store and retrieve conversation history and context with Amazon Bedrock sessions</a>.</p> <p>By default, Amazon Bedrock uses Amazon Web Services-managed keys for session encryption, including session metadata, or you can use your own KMS key. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/session-encryption.html\">Amazon Bedrock session encryption</a>.</p> <note> <p> You use a session to store state and conversation history for generative AI applications built with open-source frameworks. For Amazon Bedrock Agents, the service automatically manages conversation context and associates them with the agent-specific sessionId you specify in the <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_InvokeAgent.html\">InvokeAgent</a> API operation. </p> </note> <p>Related APIs:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_ListSessions.html\">ListSessions</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_GetSession.html\">GetSession</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_EndSession.html\">EndSession</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_DeleteSession.html\">DeleteSession</a> </p> </li> </ul>", "idempotent": true}, "DeleteAgentMemory": {"name": "DeleteAgentMemory", "http": {"method": "DELETE", "requestUri": "/agents/{agentId}/agentAliases/{agentAliasId}/memories", "responseCode": 202}, "input": {"shape": "DeleteAgentMemoryRequest"}, "output": {"shape": "DeleteAgentMemoryResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "DependencyFailedException"}, {"shape": "BadGatewayException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Deletes memory from the specified memory identifier.</p>", "idempotent": true}, "DeleteSession": {"name": "DeleteSession", "http": {"method": "DELETE", "requestUri": "/sessions/{sessionIdentifier}/", "responseCode": 200}, "input": {"shape": "DeleteSessionRequest"}, "output": {"shape": "DeleteSessionResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Deletes a session that you ended. You can't delete a session with an <code>ACTIVE</code> status. To delete an active session, you must first end it with the <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_EndSession.html\">EndSession</a> API operation. For more information about sessions, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/sessions.html\">Store and retrieve conversation history and context with Amazon Bedrock sessions</a>.</p>", "idempotent": true}, "EndSession": {"name": "EndSession", "http": {"method": "PATCH", "requestUri": "/sessions/{sessionIdentifier}", "responseCode": 200}, "input": {"shape": "EndSessionRequest"}, "output": {"shape": "EndSessionResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Ends the session. After you end a session, you can still access its content but you can’t add to it. To delete the session and it's content, you use the DeleteSession API operation. For more information about sessions, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/sessions.html\">Store and retrieve conversation history and context with Amazon Bedrock sessions</a>.</p>", "idempotent": true}, "GenerateQuery": {"name": "GenerateQuery", "http": {"method": "POST", "requestUri": "/generateQuery", "responseCode": 200}, "input": {"shape": "GenerateQueryRequest"}, "output": {"shape": "GenerateQueryResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "DependencyFailedException"}, {"shape": "BadGatewayException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Generates an SQL query from a natural language query. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/knowledge-base-generate-query.html\">Generate a query for structured data</a> in the Amazon Bedrock User Guide.</p>"}, "GetAgentMemory": {"name": "GetAgentMemory", "http": {"method": "GET", "requestUri": "/agents/{agentId}/agentAliases/{agentAliasId}/memories", "responseCode": 200}, "input": {"shape": "GetAgentMemoryRequest"}, "output": {"shape": "GetAgentMemoryResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "DependencyFailedException"}, {"shape": "BadGatewayException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Gets the sessions stored in the memory of the agent.</p>"}, "GetExecutionFlowSnapshot": {"name": "GetExecutionFlowSnapshot", "http": {"method": "GET", "requestUri": "/flows/{flowIdentifier}/aliases/{flowAliasIdentifier}/executions/{executionIdentifier}/flowsnapshot", "responseCode": 200}, "input": {"shape": "GetExecutionFlowSnapshotRequest"}, "output": {"shape": "GetExecutionFlowSnapshotResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves the flow definition snapshot used for a flow execution. The snapshot represents the flow metadata and definition as it existed at the time the execution was started. Note that even if the flow is edited after an execution starts, the snapshot connected to the execution remains unchanged.</p> <note> <p>Flow executions is in preview release for Amazon Bedrock and is subject to change.</p> </note>"}, "GetFlowExecution": {"name": "GetFlowExecution", "http": {"method": "GET", "requestUri": "/flows/{flowIdentifier}/aliases/{flowAliasIdentifier}/executions/{executionIdentifier}", "responseCode": 200}, "input": {"shape": "GetFlowExecutionRequest"}, "output": {"shape": "GetFlowExecutionResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves details about a specific flow execution, including its status, start and end times, and any errors that occurred during execution.</p>"}, "GetInvocationStep": {"name": "GetInvocationStep", "http": {"method": "POST", "requestUri": "/sessions/{sessionIdentifier}/invocationSteps/{invocationStepId}", "responseCode": 200}, "input": {"shape": "GetInvocationStepRequest"}, "output": {"shape": "GetInvocationStepResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves the details of a specific invocation step within an invocation in a session. For more information about sessions, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/sessions.html\">Store and retrieve conversation history and context with Amazon Bedrock sessions</a>.</p>"}, "GetSession": {"name": "GetSession", "http": {"method": "GET", "requestUri": "/sessions/{sessionIdentifier}/", "responseCode": 200}, "input": {"shape": "GetSessionRequest"}, "output": {"shape": "GetSessionResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves details about a specific session. For more information about sessions, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/sessions.html\">Store and retrieve conversation history and context with Amazon Bedrock sessions</a>.</p>"}, "InvokeAgent": {"name": "InvokeAgent", "http": {"method": "POST", "requestUri": "/agents/{agentId}/agentAliases/{agentAliasId}/sessions/{sessionId}/text", "responseCode": 200}, "input": {"shape": "InvokeAgentRequest"}, "output": {"shape": "InvokeAgentResponse"}, "errors": [{"shape": "ModelNotReadyException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "DependencyFailedException"}, {"shape": "BadGatewayException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p><note> </note> <p>Sends a prompt for the agent to process and respond to. Note the following fields for the request:</p> <ul> <li> <p>To continue the same conversation with an agent, use the same <code>sessionId</code> value in the request.</p> </li> <li> <p>To activate trace enablement, turn <code>enableTrace</code> to <code>true</code>. Trace enablement helps you follow the agent's reasoning process that led it to the information it processed, the actions it took, and the final result it yielded. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/agents-test.html#trace-events\">Trace enablement</a>.</p> </li> <li> <p>End a conversation by setting <code>endSession</code> to <code>true</code>.</p> </li> <li> <p>In the <code>sessionState</code> object, you can include attributes for the session or prompt or, if you configured an action group to return control, results from invocation of the action group.</p> </li> </ul> <p>The response contains both <b>chunk</b> and <b>trace</b> attributes.</p> <p>The final response is returned in the <code>bytes</code> field of the <code>chunk</code> object. The <code>InvokeAgent</code> returns one chunk for the entire interaction.</p> <ul> <li> <p>The <code>attribution</code> object contains citations for parts of the response.</p> </li> <li> <p>If you set <code>enableTrace</code> to <code>true</code> in the request, you can trace the agent's steps and reasoning process that led it to the response.</p> </li> <li> <p>If the action predicted was configured to return control, the response returns parameters for the action, elicited from the user, in the <code>returnControl</code> field.</p> </li> <li> <p>Errors are also surfaced in the response.</p> </li> </ul></p>"}, "InvokeFlow": {"name": "InvokeFlow", "http": {"method": "POST", "requestUri": "/flows/{flowIdentifier}/aliases/{flowAliasIdentifier}", "responseCode": 200}, "input": {"shape": "InvokeFlowRequest"}, "output": {"shape": "InvokeFlowResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "DependencyFailedException"}, {"shape": "BadGatewayException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Invokes an alias of a flow to run the inputs that you specify and return the output of each node as a stream. If there's an error, the error is returned. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/flows-test.html\">Test a flow in Amazon Bedrock</a> in the <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/what-is-service.html\">Amazon Bedrock User Guide</a>.</p> <note> <p>The CLI doesn't support streaming operations in Amazon Bedrock, including <code>InvokeFlow</code>.</p> </note>"}, "InvokeInlineAgent": {"name": "InvokeInlineAgent", "http": {"method": "POST", "requestUri": "/agents/{sessionId}", "responseCode": 200}, "input": {"shape": "InvokeInlineAgentRequest"}, "output": {"shape": "InvokeInlineAgentResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "DependencyFailedException"}, {"shape": "BadGatewayException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p> Invokes an inline Amazon Bedrock agent using the configurations you provide with the request. </p> <ul> <li> <p>Specify the following fields for security purposes.</p> <ul> <li> <p>(Optional) <code>customerEncryptionKeyArn</code> – The Amazon Resource Name (ARN) of a KMS key to encrypt the creation of the agent.</p> </li> <li> <p>(Optional) <code>idleSessionTTLinSeconds</code> – Specify the number of seconds for which the agent should maintain session information. After this time expires, the subsequent <code>InvokeInlineAgent</code> request begins a new session.</p> </li> </ul> </li> <li> <p>To override the default prompt behavior for agent orchestration and to use advanced prompts, include a <code>promptOverrideConfiguration</code> object. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/advanced-prompts.html\">Advanced prompts</a>.</p> </li> <li> <p>The agent instructions will not be honored if your agent has only one knowledge base, uses default prompts, has no action group, and user input is disabled.</p> </li> </ul> <note> </note>"}, "ListFlowExecutionEvents": {"name": "ListFlowExecutionEvents", "http": {"method": "GET", "requestUri": "/flows/{flowIdentifier}/aliases/{flowAliasIdentifier}/executions/{executionIdentifier}/events", "responseCode": 200}, "input": {"shape": "ListFlowExecutionEventsRequest"}, "output": {"shape": "ListFlowExecutionEventsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists events that occurred during a flow execution. Events provide detailed information about the execution progress, including node inputs and outputs, flow inputs and outputs, condition results, and failure events.</p> <note> <p>Flow executions is in preview release for Amazon Bedrock and is subject to change.</p> </note>"}, "ListFlowExecutions": {"name": "ListFlowExecutions", "http": {"method": "GET", "requestUri": "/flows/{flowIdentifier}/executions", "responseCode": 200}, "input": {"shape": "ListFlowExecutionsRequest"}, "output": {"shape": "ListFlowExecutionsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists all executions of a flow. Results can be paginated and include summary information about each execution, such as status, start and end times, and the execution's Amazon Resource Name (ARN).</p> <note> <p>Flow executions is in preview release for Amazon Bedrock and is subject to change.</p> </note>"}, "ListInvocationSteps": {"name": "ListInvocationSteps", "http": {"method": "POST", "requestUri": "/sessions/{sessionIdentifier}/invocationSteps/", "responseCode": 200}, "input": {"shape": "ListInvocationStepsRequest"}, "output": {"shape": "ListInvocationStepsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists all invocation steps associated with a session and optionally, an invocation within the session. For more information about sessions, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/sessions.html\">Store and retrieve conversation history and context with Amazon Bedrock sessions</a>.</p>"}, "ListInvocations": {"name": "ListInvocations", "http": {"method": "POST", "requestUri": "/sessions/{sessionIdentifier}/invocations/", "responseCode": 200}, "input": {"shape": "ListInvocationsRequest"}, "output": {"shape": "ListInvocationsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists all invocations associated with a specific session. For more information about sessions, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/sessions.html\">Store and retrieve conversation history and context with Amazon Bedrock sessions</a>.</p>"}, "ListSessions": {"name": "ListSessions", "http": {"method": "POST", "requestUri": "/sessions/", "responseCode": 200}, "input": {"shape": "ListSessionsRequest"}, "output": {"shape": "ListSessionsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists all sessions in your Amazon Web Services account. For more information about sessions, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/sessions.html\">Store and retrieve conversation history and context with Amazon Bedrock sessions</a>.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>List all the tags for the resource you specify.</p>"}, "OptimizePrompt": {"name": "OptimizePrompt", "http": {"method": "POST", "requestUri": "/optimize-prompt", "responseCode": 200}, "input": {"shape": "OptimizePromptRequest"}, "output": {"shape": "OptimizePromptResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "DependencyFailedException"}, {"shape": "BadGatewayException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Optimizes a prompt for the task that you specify. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/prompt-management-optimize.html\">Optimize a prompt</a> in the <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/what-is-service.html\">Amazon Bedrock User Guide</a>.</p>"}, "PutInvocationStep": {"name": "PutInvocationStep", "http": {"method": "PUT", "requestUri": "/sessions/{sessionIdentifier}/invocationSteps/", "responseCode": 201}, "input": {"shape": "PutInvocationStepRequest"}, "output": {"shape": "PutInvocationStepResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Add an invocation step to an invocation in a session. An invocation step stores fine-grained state checkpoints, including text and images, for each interaction. For more information about sessions, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/sessions.html\">Store and retrieve conversation history and context with Amazon Bedrock sessions</a>.</p> <p>Related APIs:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_GetInvocationStep.html\">GetInvocationStep</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_ListInvocationSteps.html\">ListInvocationSteps</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_ListInvocations.html\">ListInvocations</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_ListInvocations.html\">ListSessions</a> </p> </li> </ul>", "idempotent": true}, "Rerank": {"name": "<PERSON><PERSON>", "http": {"method": "POST", "requestUri": "/rerank", "responseCode": 200}, "input": {"shape": "RerankRequest"}, "output": {"shape": "RerankResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "DependencyFailedException"}, {"shape": "BadGatewayException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Reranks the relevance of sources based on queries. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/rerank.html\">Improve the relevance of query responses with a reranker model</a>.</p>"}, "Retrieve": {"name": "Retrieve", "http": {"method": "POST", "requestUri": "/knowledgebases/{knowledgeBaseId}/retrieve", "responseCode": 200}, "input": {"shape": "RetrieveRequest"}, "output": {"shape": "RetrieveResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "DependencyFailedException"}, {"shape": "BadGatewayException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Queries a knowledge base and retrieves information from it.</p>"}, "RetrieveAndGenerate": {"name": "RetrieveAndGenerate", "http": {"method": "POST", "requestUri": "/retrieveAndGenerate", "responseCode": 200}, "input": {"shape": "RetrieveAndGenerateRequest"}, "output": {"shape": "RetrieveAndGenerateResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "DependencyFailedException"}, {"shape": "BadGatewayException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Queries a knowledge base and generates responses based on the retrieved results and using the specified foundation model or <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/cross-region-inference.html\">inference profile</a>. The response only cites sources that are relevant to the query.</p>"}, "RetrieveAndGenerateStream": {"name": "RetrieveAndGenerateStream", "http": {"method": "POST", "requestUri": "/retrieveAndGenerateStream", "responseCode": 200}, "input": {"shape": "RetrieveAndGenerateStreamRequest"}, "output": {"shape": "RetrieveAndGenerateStreamResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "DependencyFailedException"}, {"shape": "BadGatewayException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Queries a knowledge base and generates responses based on the retrieved results, with output in streaming format.</p> <note> <p>The CLI doesn't support streaming operations in Amazon Bedrock, including <code>InvokeModelWithResponseStream</code>.</p> </note> <p>This operation requires permission for the <code> bedrock:RetrieveAndGenerate</code> action.</p>"}, "StartFlowExecution": {"name": "StartFlowExecution", "http": {"method": "POST", "requestUri": "/flows/{flowIdentifier}/aliases/{flowAliasIdentifier}/executions", "responseCode": 200}, "input": {"shape": "StartFlowExecutionRequest"}, "output": {"shape": "StartFlowExecutionResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "DependencyFailedException"}, {"shape": "BadGatewayException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Starts an execution of an Amazon Bedrock flow. Unlike flows that run until completion or time out after five minutes, flow executions let you run flows asynchronously for longer durations. Flow executions also yield control so that your application can perform other tasks.</p> <p>This operation returns an Amazon Resource Name (ARN) that you can use to track and manage your flow execution.</p> <note> <p>Flow executions is in preview release for Amazon Bedrock and is subject to change.</p> </note>"}, "StopFlowExecution": {"name": "StopFlowExecution", "http": {"method": "POST", "requestUri": "/flows/{flowIdentifier}/aliases/{flowAliasIdentifier}/executions/{executionIdentifier}/stop", "responseCode": 200}, "input": {"shape": "StopFlowExecutionRequest"}, "output": {"shape": "StopFlowExecutionResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "DependencyFailedException"}, {"shape": "BadGatewayException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Stops an Amazon Bedrock flow's execution. This operation prevents further processing of the flow and changes the execution status to <code>Aborted</code>.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Associate tags with a resource. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/what-is-service.html\">Tagging resources</a> in the Amazon Bedrock User Guide.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Remove tags from a resource.</p>", "idempotent": true}, "UpdateSession": {"name": "UpdateSession", "http": {"method": "PUT", "requestUri": "/sessions/{sessionIdentifier}/", "responseCode": 200}, "input": {"shape": "UpdateSessionRequest"}, "output": {"shape": "UpdateSessionResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Updates the metadata or encryption settings of a session. For more information about sessions, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/sessions.html\">Store and retrieve conversation history and context with Amazon Bedrock sessions</a>.</p>", "idempotent": true}}, "shapes": {"APISchema": {"type": "structure", "members": {"payload": {"shape": "Payload", "documentation": "<p> The JSON or YAML-formatted payload defining the OpenAPI schema for the action group. </p>"}, "s3": {"shape": "S3Identifier", "documentation": "<p> Contains details about the S3 object containing the OpenAPI schema for the action group. </p>"}}, "documentation": "<p> Contains details about the OpenAPI schema for the action group. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/agents-api-schema.html\">Action group OpenAPI schemas</a>. You can either include the schema directly in the payload field or you can upload it to an S3 bucket and specify the S3 bucket location in the s3 field. </p>", "union": true}, "AWSResourceARN": {"type": "string", "max": 2048, "min": 0, "pattern": "^arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:agent/[0-9a-zA-Z]{10}$"}, "AccessDeniedException": {"type": "structure", "members": {"message": {"shape": "NonBlankString"}}, "documentation": "<p>The request is denied because of missing access permissions. Check your permissions and retry your request.</p>", "error": {"httpStatusCode": 403, "senderFault": true}, "exception": true}, "ActionGroupExecutor": {"type": "structure", "members": {"customControl": {"shape": "CustomControlMethod", "documentation": "<p> To return the action group invocation results directly in the <code>InvokeInlineAgent</code> response, specify <code>RETURN_CONTROL</code>. </p>"}, "lambda": {"shape": "LambdaResourceArn", "documentation": "<p> The Amazon Resource Name (ARN) of the Lambda function containing the business logic that is carried out upon invoking the action. </p>"}}, "documentation": "<p> Contains details about the Lambda function containing the business logic that is carried out upon invoking the action or the custom control method for handling the information elicited from the user. </p>", "union": true}, "ActionGroupInvocationInput": {"type": "structure", "members": {"actionGroupName": {"shape": "ActionGroupName", "documentation": "<p>The name of the action group.</p>"}, "apiPath": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The path to the API to call, based off the action group.</p>"}, "executionType": {"shape": "ExecutionType", "documentation": "<p>How fulfillment of the action is handled. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/action-handle.html\">Handling fulfillment of the action</a>.</p>"}, "function": {"shape": "Function", "documentation": "<p>The function in the action group to call.</p>"}, "invocationId": {"shape": "String", "documentation": "<p>The unique identifier of the invocation. Only returned if the <code>executionType</code> is <code>RETURN_CONTROL</code>.</p>"}, "parameters": {"shape": "Parameters", "documentation": "<p>The parameters in the Lambda input event.</p>"}, "requestBody": {"shape": "RequestBody", "documentation": "<p>The parameters in the request body for the Lambda input event.</p>"}, "verb": {"shape": "Verb", "documentation": "<p>The API method being used, based off the action group.</p>"}}, "documentation": "<p>Contains information about the action group being invoked. For more information about the possible structures, see the InvocationInput tab in <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/trace-orchestration.html\">OrchestrationTrace</a> in the <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/what-is-service.html\">Amazon Bedrock User Guide</a>.</p>"}, "ActionGroupInvocationOutput": {"type": "structure", "members": {"metadata": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>Contains information about the action group output.</p>"}, "text": {"shape": "ActionGroupOutputString", "documentation": "<p>The JSON-formatted string returned by the API invoked by the action group.</p>"}}, "documentation": "<p>Contains the JSON-formatted string returned by the API invoked by the action group.</p>"}, "ActionGroupName": {"type": "string", "sensitive": true}, "ActionGroupOutputString": {"type": "string", "sensitive": true}, "ActionGroupSignature": {"type": "string", "enum": ["AMAZON.UserInput", "AMAZON.CodeInterpreter", "ANTHROPIC.Computer", "ANTHROPIC.Bash", "ANTHROPIC.TextEditor"]}, "ActionGroupSignatureParams": {"type": "map", "key": {"shape": "ActionGroupSignatureParamsKeyString"}, "value": {"shape": "ActionGroupSignatureParamsValueString"}}, "ActionGroupSignatureParamsKeyString": {"type": "string", "max": 100, "min": 0}, "ActionGroupSignatureParamsValueString": {"type": "string", "max": 100, "min": 0}, "ActionInvocationType": {"type": "string", "enum": ["RESULT", "USER_CONFIRMATION", "USER_CONFIRMATION_AND_RESULT"]}, "AdditionalModelRequestFields": {"type": "map", "key": {"shape": "AdditionalModelRequestFieldsKey"}, "value": {"shape": "AdditionalModelRequestFieldsValue"}}, "AdditionalModelRequestFieldsKey": {"type": "string", "max": 100, "min": 1}, "AdditionalModelRequestFieldsValue": {"type": "structure", "members": {}, "document": true}, "AgentActionGroup": {"type": "structure", "required": ["actionGroupName"], "members": {"actionGroupExecutor": {"shape": "ActionGroupExecutor", "documentation": "<p> The Amazon Resource Name (ARN) of the Lambda function containing the business logic that is carried out upon invoking the action or the custom control method for handling the information elicited from the user. </p>"}, "actionGroupName": {"shape": "ResourceName", "documentation": "<p> The name of the action group. </p>"}, "apiSchema": {"shape": "APISchema", "documentation": "<p> Contains either details about the S3 object containing the OpenAPI schema for the action group or the JSON or YAML-formatted payload defining the schema. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/agents-api-schema.html\">Action group OpenAPI schemas</a>. </p>"}, "description": {"shape": "ResourceDescription", "documentation": "<p> A description of the action group. </p>"}, "functionSchema": {"shape": "FunctionSchema", "documentation": "<p> Contains details about the function schema for the action group or the JSON or YAML-formatted payload defining the schema. </p>"}, "parentActionGroupSignature": {"shape": "ActionGroupSignature", "documentation": "<p>Specify a built-in or computer use action for this action group. If you specify a value, you must leave the <code>description</code>, <code>apiSchema</code>, and <code>actionGroupExecutor</code> fields empty for this action group. </p> <ul> <li> <p>To allow your agent to request the user for additional information when trying to complete a task, set this field to <code>AMAZON.UserInput</code>. </p> </li> <li> <p>To allow your agent to generate, run, and troubleshoot code when trying to complete a task, set this field to <code>AMAZON.CodeInterpreter</code>.</p> </li> <li> <p>To allow your agent to use an Anthropic computer use tool, specify one of the following values. </p> <important> <p> Computer use is a new Anthropic Claude model capability (in beta) available with Anthropic Claude 3.7 Sonnet and Claude 3.5 Sonnet v2 only. When operating computer use functionality, we recommend taking additional security precautions, such as executing computer actions in virtual environments with restricted data access and limited internet connectivity. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/agent-computer-use.html\">Configure an Amazon Bedrock Agent to complete tasks with computer use tools</a>. </p> </important> <ul> <li> <p> <code>ANTHROPIC.Computer</code> - Gives the agent permission to use the mouse and keyboard and take screenshots.</p> </li> <li> <p> <code>ANTHROPIC.TextEditor</code> - Gives the agent permission to view, create and edit files.</p> </li> <li> <p> <code>ANTHROPIC.Bash</code> - Gives the agent permission to run commands in a bash shell.</p> </li> </ul> </li> </ul>"}, "parentActionGroupSignatureParams": {"shape": "ActionGroupSignatureParams", "documentation": "<p> The configuration settings for a computer use action. </p> <important> <p>Computer use is a new Anthropic Claude model capability (in beta) available with Claude 3.7 Sonnet and Claude 3.5 Sonnet v2 only. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/agent-computer-use.html\">Configure an Amazon Bedrock Agent to complete tasks with computer use tools</a>.</p> </important>"}}, "documentation": "<p> Contains details of the inline agent's action group. </p>"}, "AgentActionGroups": {"type": "list", "member": {"shape": "AgentActionGroup"}}, "AgentAliasArn": {"type": "string", "max": 2048, "min": 0, "pattern": "^arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:agent-alias/[0-9a-zA-Z]{10}/[0-9a-zA-Z]{10}$"}, "AgentAliasId": {"type": "string", "max": 10, "min": 0, "pattern": "^[0-9a-zA-Z]+$"}, "AgentCollaboration": {"type": "string", "enum": ["SUPERVISOR", "SUPERVISOR_ROUTER", "DISABLED"]}, "AgentCollaboratorInputPayload": {"type": "structure", "members": {"returnControlResults": {"shape": "ReturnControlResults", "documentation": "<p>An action invocation result.</p>"}, "text": {"shape": "AgentCollaboratorPayloadString", "documentation": "<p>Input text.</p>"}, "type": {"shape": "PayloadType", "documentation": "<p>The input type.</p>"}}, "documentation": "<p>Input for an agent collaborator. The input can be text or an action invocation result.</p>"}, "AgentCollaboratorInvocationInput": {"type": "structure", "members": {"agentCollaboratorAliasArn": {"shape": "AgentAliasArn", "documentation": "<p>The collaborator's alias <PERSON><PERSON>.</p>"}, "agentCollaboratorName": {"shape": "String", "documentation": "<p>The collaborator's name.</p>"}, "input": {"shape": "AgentCollaboratorInputPayload", "documentation": "<p>Text or action invocation result input for the collaborator.</p>"}}, "documentation": "<p>An agent collaborator invocation input.</p>"}, "AgentCollaboratorInvocationOutput": {"type": "structure", "members": {"agentCollaboratorAliasArn": {"shape": "AgentAliasArn", "documentation": "<p>The output's agent collaborator alias <PERSON><PERSON>.</p>"}, "agentCollaboratorName": {"shape": "String", "documentation": "<p>The output's agent collaborator name.</p>"}, "metadata": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>Contains information about the output from the agent collaborator.</p>"}, "output": {"shape": "AgentCollaboratorOutputPayload", "documentation": "<p>The output's output.</p>"}}, "documentation": "<p>Output from an agent collaborator.</p>"}, "AgentCollaboratorOutputPayload": {"type": "structure", "members": {"returnControlPayload": {"shape": "ReturnControlPayload", "documentation": "<p>An action invocation result.</p>"}, "text": {"shape": "AgentCollaboratorPayloadString", "documentation": "<p>Text output.</p>"}, "type": {"shape": "PayloadType", "documentation": "<p>The type of output.</p>"}}, "documentation": "<p>Output from an agent collaborator. The output can be text or an action invocation result.</p>"}, "AgentCollaboratorPayloadString": {"type": "string", "sensitive": true}, "AgentId": {"type": "string", "max": 10, "min": 0, "pattern": "^[0-9a-zA-Z]+$"}, "AgentVersion": {"type": "string", "max": 5, "min": 1, "pattern": "^(DRAFT|[0-9]{0,4}[1-9][0-9]{0,4})$"}, "AnalyzePromptEvent": {"type": "structure", "members": {"message": {"shape": "String", "documentation": "<p>A message describing the analysis of the prompt.</p>"}}, "documentation": "<p>An event in which the prompt was analyzed in preparation for optimization.</p>", "event": true, "sensitive": true}, "ApiContentMap": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "PropertyParameters"}}, "ApiInvocationInput": {"type": "structure", "required": ["actionGroup"], "members": {"actionGroup": {"shape": "String", "documentation": "<p>The action group that the API operation belongs to.</p>"}, "actionInvocationType": {"shape": "ActionInvocationType", "documentation": "<p>Contains information about the API operation to invoke.</p>"}, "agentId": {"shape": "String", "documentation": "<p>The agent's ID.</p>"}, "apiPath": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The path to the API operation.</p>"}, "collaboratorName": {"shape": "Name", "documentation": "<p>The agent collaborator's name.</p>"}, "httpMethod": {"shape": "String", "documentation": "<p>The HTTP method of the API operation.</p>"}, "parameters": {"shape": "ApiParameters", "documentation": "<p>The parameters to provide for the API request, as the agent elicited from the user.</p>"}, "requestBody": {"shape": "ApiRequestBody", "documentation": "<p>The request body to provide for the API request, as the agent elicited from the user.</p>"}}, "documentation": "<p>Contains information about the API operation that the agent predicts should be called.</p> <p>This data type is used in the following API operations:</p> <ul> <li> <p>In the <code>returnControl</code> field of the <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_InvokeAgent.html#API_agent-runtime_InvokeAgent_ResponseSyntax\">InvokeAgent response</a> </p> </li> </ul>"}, "ApiParameter": {"type": "structure", "members": {"name": {"shape": "String", "documentation": "<p>The name of the parameter.</p>"}, "type": {"shape": "String", "documentation": "<p>The data type for the parameter.</p>"}, "value": {"shape": "String", "documentation": "<p>The value of the parameter.</p>"}}, "documentation": "<p>Information about a parameter to provide to the API request.</p> <p>This data type is used in the following API operations:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_InvokeAgent.html#API_agent-runtime_InvokeAgent_ResponseSyntax\">InvokeAgent response</a> </p> </li> </ul>"}, "ApiParameters": {"type": "list", "member": {"shape": "ApiParameter"}}, "ApiPath": {"type": "string", "sensitive": true}, "ApiRequestBody": {"type": "structure", "members": {"content": {"shape": "ApiContentMap", "documentation": "<p>The content of the request body. The key of the object in this field is a media type defining the format of the request body.</p>"}}, "documentation": "<p>The request body to provide for the API request, as the agent elicited from the user.</p> <p>This data type is used in the following API operations:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_InvokeAgent.html#API_agent-runtime_InvokeAgent_ResponseSyntax\">InvokeAgent response</a> </p> </li> </ul>"}, "ApiResult": {"type": "structure", "required": ["actionGroup"], "members": {"actionGroup": {"shape": "String", "documentation": "<p>The action group that the API operation belongs to.</p>"}, "agentId": {"shape": "String", "documentation": "<p>The agent's ID.</p>"}, "apiPath": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The path to the API operation.</p>"}, "confirmationState": {"shape": "ConfirmationState", "documentation": "<p>Controls the API operations or functions to invoke based on the user confirmation.</p>"}, "httpMethod": {"shape": "String", "documentation": "<p>The HTTP method for the API operation.</p>"}, "httpStatusCode": {"shape": "Integer", "documentation": "<p>http status code from API execution response (for example: 200, 400, 500).</p>"}, "responseBody": {"shape": "ResponseBody", "documentation": "<p>The response body from the API operation. The key of the object is the content type (currently, only <code>TEXT</code> is supported). The response may be returned directly or from the Lambda function.</p>"}, "responseState": {"shape": "ResponseState", "documentation": "<p>Controls the final response state returned to end user when API/Function execution failed. When this state is FAILURE, the request would fail with dependency failure exception. When this state is REPROMPT, the API/function response will be sent to model for re-prompt</p>"}}, "documentation": "<p>Contains information about the API operation that was called from the action group and the response body that was returned.</p> <p>This data type is used in the following API operations:</p> <ul> <li> <p>In the <code>returnControlInvocationResults</code> of the <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_InvokeAgent.html#API_agent-runtime_InvokeAgent_RequestSyntax\">InvokeAgent request</a> </p> </li> </ul>"}, "AttributeType": {"type": "string", "enum": ["STRING", "NUMBER", "BOOLEAN", "STRING_LIST"]}, "Attribution": {"type": "structure", "members": {"citations": {"shape": "Citations", "documentation": "<p>A list of citations and related information for a part of an agent response.</p>"}}, "documentation": "<p>Contains citations for a part of an agent response.</p>"}, "BadGatewayException": {"type": "structure", "members": {"message": {"shape": "NonBlankString"}, "resourceName": {"shape": "NonBlankString", "documentation": "<p>The name of the dependency that caused the issue, such as Amazon Bedrock, Lambda, or STS.</p>"}}, "documentation": "<p>There was an issue with a dependency due to a server issue. Retry your request.</p>", "error": {"httpStatusCode": 502}, "exception": true, "fault": true}, "BasePromptTemplate": {"type": "string", "max": 100000, "min": 1, "sensitive": true}, "BedrockModelArn": {"type": "string", "max": 2048, "min": 1, "pattern": "^(arn:aws(-[^:]+)?:(bedrock|sagemaker):[a-z0-9-]{1,20}:([0-9]{12})?:([a-z-]+/)?)?([a-z0-9.-]{1,63}){0,2}(([:][a-z0-9-]{1,63}){0,2})?(/[a-z0-9]{1,12})?$"}, "BedrockModelConfigurations": {"type": "structure", "members": {"performanceConfig": {"shape": "PerformanceConfiguration", "documentation": "<p>The performance configuration for the model.</p>"}}, "documentation": "<p>Settings for a model called with <a>InvokeAgent</a>.</p>"}, "BedrockRerankingConfiguration": {"type": "structure", "required": ["modelConfiguration"], "members": {"modelConfiguration": {"shape": "BedrockRerankingModelConfiguration", "documentation": "<p>Contains configurations for a reranker model.</p>"}, "numberOfResults": {"shape": "BedrockRerankingConfigurationNumberOfResultsInteger", "documentation": "<p>The number of results to return after reranking.</p>"}}, "documentation": "<p>Contains configurations for an Amazon Bedrock reranker model.</p>"}, "BedrockRerankingConfigurationNumberOfResultsInteger": {"type": "integer", "box": true, "max": 1000, "min": 1}, "BedrockRerankingModelArn": {"type": "string", "max": 2048, "min": 1, "pattern": "^(arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}::foundation-model/(.*))?$"}, "BedrockRerankingModelConfiguration": {"type": "structure", "required": ["modelArn"], "members": {"additionalModelRequestFields": {"shape": "AdditionalModelRequestFields", "documentation": "<p>A JSON object whose keys are request fields for the model and whose values are values for those fields.</p>"}, "modelArn": {"shape": "BedrockModelArn", "documentation": "<p>The ARN of the reranker model.</p>"}}, "documentation": "<p>Contains configurations for a reranker model.</p>"}, "BedrockSessionContentBlock": {"type": "structure", "members": {"image": {"shape": "ImageBlock", "documentation": "<p>The image in the invocation step.</p>"}, "text": {"shape": "BedrockSessionContentBlockTextString", "documentation": "<p>The text in the invocation step.</p>"}}, "documentation": "<p>A block of content that you pass to, or receive from, a Amazon Bedrock session in an invocation step. You pass the content to a session in the <code>payLoad</code> of the <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_PutInvocationStep.html\">PutInvocationStep</a> API operation. You retrieve the content with the <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_GetInvocationStep.html\">GetInvocationStep</a> API operation.</p> <p>For more information about sessions, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/sessions.html\">Store and retrieve conversation history and context with Amazon Bedrock sessions</a>.</p>", "sensitive": true, "union": true}, "BedrockSessionContentBlockTextString": {"type": "string", "min": 1}, "BedrockSessionContentBlocks": {"type": "list", "member": {"shape": "BedrockSessionContentBlock"}, "min": 1}, "Blob": {"type": "blob"}, "Boolean": {"type": "boolean", "box": true}, "ByteContentBlob": {"type": "blob", "max": 10485760, "min": 1, "sensitive": true}, "ByteContentDoc": {"type": "structure", "required": ["contentType", "data", "identifier"], "members": {"contentType": {"shape": "ContentType", "documentation": "<p>The MIME type of the document contained in the wrapper object.</p>"}, "data": {"shape": "ByteContentBlob", "documentation": "<p>The byte value of the file to upload, encoded as a Base-64 string.</p>"}, "identifier": {"shape": "Identifier", "documentation": "<p>The file name of the document contained in the wrapper object.</p>"}}, "documentation": "<p>This property contains the document to chat with, along with its attributes.</p>"}, "ByteContentFile": {"type": "structure", "required": ["data", "mediaType"], "members": {"data": {"shape": "ByteContentBlob", "documentation": "<p>The raw bytes of the file to attach. The maximum size of all files that is attached is 10MB. You can attach a maximum of 5 files. </p>"}, "mediaType": {"shape": "MimeType", "documentation": "<p>The MIME type of data contained in the file used for chat.</p>"}}, "documentation": "<p>The property contains the file to chat with, along with its attributes.</p>"}, "Caller": {"type": "structure", "members": {"agentAliasArn": {"shape": "AgentAliasArn", "documentation": "<p>The caller's agent alias <PERSON><PERSON>.</p>"}}, "documentation": "<p>Details about a caller.</p>", "union": true}, "CallerChain": {"type": "list", "member": {"shape": "Caller"}}, "Citation": {"type": "structure", "members": {"generatedResponsePart": {"shape": "GeneratedResponsePart", "documentation": "<p>Contains the generated response and metadata </p>"}, "retrievedReferences": {"shape": "RetrievedReferences", "documentation": "<p>Contains metadata about the sources cited for the generated response.</p>"}}, "documentation": "<p>An object containing a segment of the generated response that is based on a source in the knowledge base, alongside information about the source.</p> <p>This data type is used in the following API operations:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_InvokeAgent.html#API_agent-runtime_InvokeAgent_ResponseSyntax\">InvokeAgent response</a> – in the <code>citations</code> field</p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_RetrieveAndGenerate.html#API_agent-runtime_RetrieveAndGenerate_ResponseSyntax\">RetrieveAndGenerate response</a> – in the <code>citations</code> field</p> </li> </ul>"}, "CitationEvent": {"type": "structure", "members": {"citation": {"shape": "Citation", "documentation": "<p>The citation.</p>", "deprecated": true, "deprecatedMessage": "Citation is deprecated. Please use GeneratedResponsePart and RetrievedReferences for citation event."}, "generatedResponsePart": {"shape": "GeneratedResponsePart", "documentation": "<p>The generated response to the citation event.</p>"}, "retrievedReferences": {"shape": "RetrievedReferences", "documentation": "<p>The retrieved references of the citation event.</p>"}}, "documentation": "<p>A citation event.</p>", "event": true}, "Citations": {"type": "list", "member": {"shape": "Citation"}}, "CodeInterpreterInvocationInput": {"type": "structure", "members": {"code": {"shape": "String", "documentation": "<p>The code for the code interpreter to use.</p>"}, "files": {"shape": "Files", "documentation": "<p>Files that are uploaded for code interpreter to use.</p>"}}, "documentation": "<p>Contains information about the code interpreter being invoked.</p>"}, "CodeInterpreterInvocationOutput": {"type": "structure", "members": {"executionError": {"shape": "String", "documentation": "<p>Contains the error returned from code execution.</p>"}, "executionOutput": {"shape": "String", "documentation": "<p>Contains the successful output returned from code execution</p>"}, "executionTimeout": {"shape": "Boolean", "documentation": "<p>Indicates if the execution of the code timed out.</p>"}, "files": {"shape": "Files", "documentation": "<p>Contains output files, if generated by code execution.</p>"}, "metadata": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>Contains information about the output from the code interpreter.</p>"}}, "documentation": "<p>Contains the JSON-formatted string returned by the API invoked by the code interpreter.</p>"}, "CollaborationInstruction": {"type": "string", "max": 4000, "min": 1, "sensitive": true}, "Collaborator": {"type": "structure", "required": ["foundationModel", "instruction"], "members": {"actionGroups": {"shape": "AgentActionGroups", "documentation": "<p> List of action groups with each action group defining tasks the inline collaborator agent needs to carry out. </p>"}, "agentCollaboration": {"shape": "AgentCollaboration", "documentation": "<p> Defines how the inline supervisor agent handles information across multiple collaborator agents to coordinate a final response. </p>"}, "agentName": {"shape": "Name", "documentation": "<p> Name of the inline collaborator agent which must be the same name as specified for <code>collaboratorName</code>. </p>"}, "collaboratorConfigurations": {"shape": "CollaboratorConfigurations", "documentation": "<p> Settings of the collaborator agent. </p>"}, "customerEncryptionKeyArn": {"shape": "KmsKeyArn", "documentation": "<p> The Amazon Resource Name (ARN) of the AWS KMS key that encrypts the inline collaborator. </p>"}, "foundationModel": {"shape": "ModelIdentifier", "documentation": "<p> The foundation model used by the inline collaborator agent. </p>"}, "guardrailConfiguration": {"shape": "GuardrailConfigurationWithArn", "documentation": "<p> Details of the guardwrail associated with the inline collaborator. </p>"}, "idleSessionTTLInSeconds": {"shape": "SessionTTL", "documentation": "<p> The number of seconds for which the Amazon Bedrock keeps information about the user's conversation with the inline collaborator agent.</p> <p>A user interaction remains active for the amount of time specified. If no conversation occurs during this time, the session expires and Amazon Bedrock deletes any data provided before the timeout. </p>"}, "instruction": {"shape": "Instruction", "documentation": "<p> Instruction that tell the inline collaborator agent what it should do and how it should interact with users. </p>"}, "knowledgeBases": {"shape": "KnowledgeBases", "documentation": "<p> Knowledge base associated with the inline collaborator agent. </p>"}, "promptOverrideConfiguration": {"shape": "PromptOverrideConfiguration", "documentation": "<p> Contains configurations to override prompt templates in different parts of an inline collaborator sequence. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/advanced-prompts.html\">Advanced prompts</a>. </p>"}}, "documentation": "<p> List of inline collaborators. </p>"}, "CollaboratorConfiguration": {"type": "structure", "required": ["collaboratorInstruction", "<PERSON><PERSON><PERSON>"], "members": {"agentAliasArn": {"shape": "AgentAliasArn", "documentation": "<p> The Amazon Resource Name (ARN) of the inline collaborator agent. </p>"}, "collaboratorInstruction": {"shape": "CollaborationInstruction", "documentation": "<p> Instructions that tell the inline collaborator agent what it should do and how it should interact with users. </p>"}, "collaboratorName": {"shape": "Name", "documentation": "<p> Name of the inline collaborator agent which must be the same name as specified for <code>agent<PERSON>ame</code>. </p>"}, "relayConversationHistory": {"shape": "RelayConversationHistory", "documentation": "<p> A relay conversation history for the inline collaborator agent. </p>"}}, "documentation": "<p> Settings of an inline collaborator agent. </p>"}, "CollaboratorConfigurations": {"type": "list", "member": {"shape": "CollaboratorConfiguration"}}, "Collaborators": {"type": "list", "member": {"shape": "Collaborator"}}, "ConditionResultEvent": {"type": "structure", "required": ["nodeName", "satisfiedConditions", "timestamp"], "members": {"nodeName": {"shape": "NodeName", "documentation": "<p>The name of the condition node that evaluated the conditions.</p>"}, "satisfiedConditions": {"shape": "SatisfiedConditions", "documentation": "<p>A list of conditions that were satisfied during the evaluation.</p>"}, "timestamp": {"shape": "DateTimestamp", "documentation": "<p>The timestamp when the condition evaluation occurred.</p>"}}, "documentation": "<p>Contains information about a condition evaluation result during a flow execution. This event is generated when a condition node in the flow evaluates its conditions.</p> <note> <p>Flow executions is in preview release for Amazon Bedrock and is subject to change.</p> </note>", "sensitive": true}, "ConfirmationState": {"type": "string", "enum": ["CONFIRM", "DENY"]}, "ConflictException": {"type": "structure", "members": {"message": {"shape": "NonBlankString"}}, "documentation": "<p>There was a conflict performing an operation. Resolve the conflict and retry your request.</p>", "error": {"httpStatusCode": 409, "senderFault": true}, "exception": true}, "ContentBlock": {"type": "structure", "members": {"text": {"shape": "String", "documentation": "<p>The block's text.</p>"}}, "documentation": "<p>A content block.</p>", "sensitive": true, "union": true}, "ContentBlocks": {"type": "list", "member": {"shape": "ContentBlock"}}, "ContentBody": {"type": "structure", "members": {"body": {"shape": "String", "documentation": "<p>The body of the API response.</p>"}, "images": {"shape": "ImageInputs", "documentation": "<p>Lists details, including format and source, for the image in the response from the function call. You can specify only one image and the function in the <code>returnControlInvocationResults</code> must be a computer use action. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/agent-computer-use.html\">Configure an Amazon Bedrock Agent to complete tasks with computer use tools</a>. </p>"}}, "documentation": "<p>Contains the body of the API response.</p> <p>This data type is used in the following API operations:</p> <ul> <li> <p>In the <code>returnControlInvocationResults</code> field of the <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_InvokeAgent.html#API_agent-runtime_InvokeAgent_RequestSyntax\">InvokeAgent request</a> </p> </li> </ul>"}, "ContentMap": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "Parameters"}}, "ContentType": {"type": "string", "pattern": "[a-z]{1,20}/.{1,20}"}, "ConversationHistory": {"type": "structure", "members": {"messages": {"shape": "Messages", "documentation": "<p>The conversation's messages.</p>"}}, "documentation": "<p>A conversation history.</p>"}, "ConversationRole": {"type": "string", "enum": ["user", "assistant"]}, "CreateInvocationRequest": {"type": "structure", "required": ["sessionIdentifier"], "members": {"description": {"shape": "InvocationDescription", "documentation": "<p>A description for the interactions in the invocation. For example, \"User asking about weather in Seattle\".</p>"}, "invocationId": {"shape": "<PERSON><PERSON>", "documentation": "<p>A unique identifier for the invocation in UUID format.</p>"}, "sessionIdentifier": {"shape": "SessionIdentifier", "documentation": "<p>The unique identifier for the associated session for the invocation. You can specify either the session's <code>sessionId</code> or its Amazon Resource Name (ARN). </p>", "location": "uri", "locationName": "sessionIdentifier"}}}, "CreateInvocationResponse": {"type": "structure", "required": ["createdAt", "invocationId", "sessionId"], "members": {"createdAt": {"shape": "DateTimestamp", "documentation": "<p>The timestamp for when the invocation was created.</p>"}, "invocationId": {"shape": "<PERSON><PERSON>", "documentation": "<p>The unique identifier for the invocation.</p>"}, "sessionId": {"shape": "<PERSON><PERSON>", "documentation": "<p>The unique identifier for the session associated with the invocation.</p>"}}}, "CreateSessionRequest": {"type": "structure", "members": {"encryptionKeyArn": {"shape": "KmsKeyArn", "documentation": "<p>The Amazon Resource Name (ARN) of the KMS key to use to encrypt the session data. The user or role creating the session must have permission to use the key. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/session-encryption.html\">Amazon Bedrock session encryption</a>. </p>"}, "sessionMetadata": {"shape": "SessionMetadataMap", "documentation": "<p>A map of key-value pairs containing attributes to be persisted across the session. For example, the user's ID, their language preference, and the type of device they are using.</p>"}, "tags": {"shape": "TagsMap", "documentation": "<p>Specify the key-value pairs for the tags that you want to attach to the session.</p>"}}}, "CreateSessionResponse": {"type": "structure", "required": ["createdAt", "sessionArn", "sessionId", "sessionStatus"], "members": {"createdAt": {"shape": "DateTimestamp", "documentation": "<p>The timestamp for when the session was created.</p>"}, "sessionArn": {"shape": "SessionArn", "documentation": "<p>The Amazon Resource Name (ARN) of the created session.</p>"}, "sessionId": {"shape": "<PERSON><PERSON>", "documentation": "<p>The unique identifier for the session.</p>"}, "sessionStatus": {"shape": "SessionStatus", "documentation": "<p>The current status of the session.</p>"}}}, "CreationMode": {"type": "string", "enum": ["DEFAULT", "OVERRIDDEN"]}, "CustomControlMethod": {"type": "string", "enum": ["RETURN_CONTROL"]}, "CustomOrchestration": {"type": "structure", "members": {"executor": {"shape": "OrchestrationExecutor", "documentation": "<p>The structure of the executor invoking the actions in custom orchestration. </p>"}}, "documentation": "<p>Contains details of the custom orchestration configured for the agent. </p>"}, "CustomOrchestrationTrace": {"type": "structure", "members": {"event": {"shape": "CustomOrchestrationTraceEvent", "documentation": "<p> The event details used with the custom orchestration. </p>"}, "traceId": {"shape": "TraceId", "documentation": "<p> The unique identifier of the trace. </p>"}}, "documentation": "<p> The trace behavior for the custom orchestration. </p>", "sensitive": true}, "CustomOrchestrationTraceEvent": {"type": "structure", "members": {"text": {"shape": "String", "documentation": "<p> The text that prompted the event at this step. </p>"}}, "documentation": "<p> The event in the custom orchestration sequence. Events are the responses which the custom orchestration Lambda function sends as response to the agent. </p>", "sensitive": true}, "DateTimestamp": {"type": "timestamp", "documentation": "<p>Time Stamp.</p>", "timestampFormat": "iso8601"}, "DeleteAgentMemoryRequest": {"type": "structure", "required": ["agentAliasId", "agentId"], "members": {"agentAliasId": {"shape": "AgentAliasId", "documentation": "<p>The unique identifier of an alias of an agent.</p>", "location": "uri", "locationName": "agentAliasId"}, "agentId": {"shape": "AgentId", "documentation": "<p>The unique identifier of the agent to which the alias belongs.</p>", "location": "uri", "locationName": "agentId"}, "memoryId": {"shape": "MemoryId", "documentation": "<p>The unique identifier of the memory.</p>", "location": "querystring", "locationName": "memoryId"}, "sessionId": {"shape": "SessionId", "documentation": "<p>The unique session identifier of the memory.</p>", "location": "querystring", "locationName": "sessionId"}}}, "DeleteAgentMemoryResponse": {"type": "structure", "members": {}}, "DeleteSessionRequest": {"type": "structure", "required": ["sessionIdentifier"], "members": {"sessionIdentifier": {"shape": "SessionIdentifier", "documentation": "<p>The unique identifier for the session to be deleted. You can specify either the session's <code>sessionId</code> or its Amazon Resource Name (ARN).</p>", "location": "uri", "locationName": "sessionIdentifier"}}}, "DeleteSessionResponse": {"type": "structure", "members": {}}, "DependencyFailedException": {"type": "structure", "members": {"message": {"shape": "NonBlankString"}, "resourceName": {"shape": "NonBlankString", "documentation": "<p>The name of the dependency that caused the issue, such as Amazon Bedrock, Lambda, or STS.</p>"}}, "documentation": "<p>There was an issue with a dependency. Check the resource configurations and retry the request.</p>", "error": {"httpStatusCode": 424, "senderFault": true}, "exception": true}, "Document": {"type": "structure", "members": {}, "document": true}, "Double": {"type": "double", "box": true}, "EndSessionRequest": {"type": "structure", "required": ["sessionIdentifier"], "members": {"sessionIdentifier": {"shape": "SessionIdentifier", "documentation": "<p>The unique identifier for the session to end. You can specify either the session's <code>sessionId</code> or its Amazon Resource Name (ARN).</p>", "location": "uri", "locationName": "sessionIdentifier"}}}, "EndSessionResponse": {"type": "structure", "required": ["sessionArn", "sessionId", "sessionStatus"], "members": {"sessionArn": {"shape": "SessionArn", "documentation": "<p>The Amazon Resource Name (ARN) of the session you ended.</p>"}, "sessionId": {"shape": "<PERSON><PERSON>", "documentation": "<p>The unique identifier of the session you ended.</p>"}, "sessionStatus": {"shape": "SessionStatus", "documentation": "<p>The current status of the session you ended.</p>"}}}, "ExecutionType": {"type": "string", "enum": ["LAMBDA", "RETURN_CONTROL"]}, "ExternalSource": {"type": "structure", "required": ["sourceType"], "members": {"byteContent": {"shape": "ByteContentDoc", "documentation": "<p>The identifier, contentType, and data of the external source wrapper object.</p>"}, "s3Location": {"shape": "S3ObjectDoc", "documentation": "<p>The S3 location of the external source wrapper object.</p>"}, "sourceType": {"shape": "ExternalSourceType", "documentation": "<p>The source type of the external source wrapper object.</p>"}}, "documentation": "<p>The unique external source of the content contained in the wrapper object.</p>"}, "ExternalSourceType": {"type": "string", "enum": ["S3", "BYTE_CONTENT"]}, "ExternalSources": {"type": "list", "member": {"shape": "ExternalSource"}, "max": 1, "min": 1}, "ExternalSourcesGenerationConfiguration": {"type": "structure", "members": {"additionalModelRequestFields": {"shape": "AdditionalModelRequestFields", "documentation": "<p> Additional model parameters and their corresponding values not included in the textInferenceConfig structure for an external source. Takes in custom model parameters specific to the language model being used. </p>"}, "guardrailConfiguration": {"shape": "GuardrailConfiguration", "documentation": "<p>The configuration details for the guardrail.</p>"}, "inferenceConfig": {"shape": "InferenceConfig", "documentation": "<p> Configuration settings for inference when using RetrieveAndGenerate to generate responses while using an external source.</p>"}, "performanceConfig": {"shape": "PerformanceConfiguration", "documentation": "<p>The latency configuration for the model.</p>"}, "promptTemplate": {"shape": "PromptTemplate", "documentation": "<p>Contain the textPromptTemplate string for the external source wrapper object.</p>"}}, "documentation": "<p>Contains the generation configuration of the external source wrapper object.</p>"}, "ExternalSourcesRetrieveAndGenerateConfiguration": {"type": "structure", "required": ["modelArn", "sources"], "members": {"generationConfiguration": {"shape": "ExternalSourcesGenerationConfiguration", "documentation": "<p>The prompt used with the external source wrapper object with the <code>retrieveAndGenerate</code> function.</p>"}, "modelArn": {"shape": "BedrockModelArn", "documentation": "<p>The model Amazon Resource Name (ARN) for the external source wrapper object in the <code>retrieveAndGenerate</code> function.</p>"}, "sources": {"shape": "ExternalSources", "documentation": "<p>The document for the external source wrapper object in the <code>retrieveAndGenerate</code> function.</p>"}}, "documentation": "<p>The configurations of the external source wrapper object in the <code>retrieveAndGenerate</code> function.</p>"}, "FailureReasonString": {"type": "string", "sensitive": true}, "FailureTrace": {"type": "structure", "members": {"failureCode": {"shape": "Integer", "documentation": "<p>The failure code for the trace.</p>"}, "failureReason": {"shape": "FailureReasonString", "documentation": "<p>The reason the interaction failed.</p>"}, "metadata": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>Information about the failure that occurred.</p>"}, "traceId": {"shape": "TraceId", "documentation": "<p>The unique identifier of the trace.</p>"}}, "documentation": "<p>Contains information about the failure of the interaction.</p>", "sensitive": true}, "FieldForReranking": {"type": "structure", "required": ["fieldName"], "members": {"fieldName": {"shape": "FieldForRerankingFieldNameString", "documentation": "<p>The name of a metadata field to include in or exclude from consideration when reranking.</p>"}}, "documentation": "<p>Contains information for a metadata field to include in or exclude from consideration when reranking.</p>"}, "FieldForRerankingFieldNameString": {"type": "string", "max": 2000, "min": 1}, "FieldsForReranking": {"type": "list", "member": {"shape": "FieldForReranking"}, "max": 100, "min": 1, "sensitive": true}, "FileBody": {"type": "blob", "max": 1000000, "min": 0, "sensitive": true}, "FilePart": {"type": "structure", "members": {"files": {"shape": "OutputFiles", "documentation": "<p>Files containing intermediate response for the user.</p>"}}, "documentation": "<p>Contains intermediate response for code interpreter if any files have been generated.</p>", "event": true}, "FileSource": {"type": "structure", "required": ["sourceType"], "members": {"byteContent": {"shape": "ByteContentFile", "documentation": "<p>The data and the text of the attached files.</p>"}, "s3Location": {"shape": "S3ObjectFile", "documentation": "<p>The s3 location of the files to attach.</p>"}, "sourceType": {"shape": "FileSourceType", "documentation": "<p>The source type of the files to attach.</p>"}}, "documentation": "<p>The source file of the content contained in the wrapper object.</p>"}, "FileSourceType": {"type": "string", "enum": ["S3", "BYTE_CONTENT"]}, "FileUseCase": {"type": "string", "enum": ["CODE_INTERPRETER", "CHAT"]}, "Files": {"type": "list", "member": {"shape": "String"}}, "FilterAttribute": {"type": "structure", "required": ["key", "value"], "members": {"key": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The name that the metadata attribute must match.</p>"}, "value": {"shape": "FilterValue", "documentation": "<p>The value to whcih to compare the value of the metadata attribute.</p>"}}, "documentation": "<p>Specifies the name that the metadata attribute must match and the value to which to compare the value of the metadata attribute. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/kb-test-config.html\">Query configurations</a>.</p> <p>This data type is used in the following API operations:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_RetrieveAndGenerate.html#API_agent-runtime_RetrieveAndGenerate_RequestSyntax\">RetrieveAndGenerate request</a> </p> </li> </ul>"}, "FilterKey": {"type": "string", "max": 100, "min": 1}, "FilterValue": {"type": "structure", "members": {}, "document": true}, "FinalResponse": {"type": "structure", "members": {"metadata": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>Contains information about the invoke agent operation.</p>"}, "text": {"shape": "FinalResponseString", "documentation": "<p>The text in the response to the user.</p>"}}, "documentation": "<p>Contains details about the response to the user.</p>"}, "FinalResponseString": {"type": "string", "sensitive": true}, "Float": {"type": "float", "box": true}, "FlowAliasIdentifier": {"type": "string", "max": 2048, "min": 0, "pattern": "^(arn:aws:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:flow/[0-9a-zA-Z]{10}/alias/[0-9a-zA-Z]{10})|(\\bTSTALIASID\\b|[0-9a-zA-Z]+)$"}, "FlowCompletionEvent": {"type": "structure", "required": ["completionReason"], "members": {"completionReason": {"shape": "FlowCompletionReason", "documentation": "<p>The reason that the flow completed.</p>"}}, "documentation": "<p>Contains information about why a flow completed.</p>", "event": true, "sensitive": true}, "FlowCompletionReason": {"type": "string", "enum": ["SUCCESS", "INPUT_REQUIRED"]}, "FlowErrorCode": {"type": "string", "enum": ["VALIDATION", "INTERNAL_SERVER", "NODE_EXECUTION_FAILED"]}, "FlowExecutionContent": {"type": "structure", "members": {"document": {"shape": "Document", "documentation": "<p>The document content of the field, which can contain text or structured data.</p>"}}, "documentation": "<p>Contains the content of an flow execution input or output field.</p> <note> <p>Flow executions is in preview release for Amazon Bedrock and is subject to change.</p> </note>", "sensitive": true, "union": true}, "FlowExecutionError": {"type": "structure", "members": {"error": {"shape": "FlowExecutionErrorType", "documentation": "<p>The error code for the type of error that occurred.</p>"}, "message": {"shape": "String", "documentation": "<p>A descriptive message that provides details about the error.</p>"}, "nodeName": {"shape": "NodeName", "documentation": "<p>The name of the node in the flow where the error occurred (if applicable).</p>"}}, "documentation": "<p>Contains information about an error that occurred during an flow execution.</p> <note> <p>Flow executions is in preview release for Amazon Bedrock and is subject to change.</p> </note>"}, "FlowExecutionErrorType": {"type": "string", "enum": ["ExecutionTimedOut"]}, "FlowExecutionErrors": {"type": "list", "member": {"shape": "FlowExecutionError"}}, "FlowExecutionEvent": {"type": "structure", "members": {"conditionResultEvent": {"shape": "ConditionResultEvent", "documentation": "<p>Contains information about a condition evaluation result during the flow execution. This event is generated when a condition node in the flow evaluates its conditions.</p>"}, "flowFailureEvent": {"shape": "FlowFailureEvent", "documentation": "<p>Contains information about a failure that occurred at the flow level during execution.</p>"}, "flowInputEvent": {"shape": "FlowExecutionInputEvent", "documentation": "<p>Contains information about the inputs provided to the flow at the start of execution.</p>"}, "flowOutputEvent": {"shape": "FlowExecutionOutputEvent", "documentation": "<p>Contains information about the outputs produced by the flow at the end of execution.</p>"}, "nodeFailureEvent": {"shape": "NodeFailureEvent", "documentation": "<p>Contains information about a failure that occurred at a specific node during execution.</p>"}, "nodeInputEvent": {"shape": "NodeInputEvent", "documentation": "<p>Contains information about the inputs provided to a specific node during execution.</p>"}, "nodeOutputEvent": {"shape": "NodeOutputEvent", "documentation": "<p>Contains information about the outputs produced by a specific node during execution.</p>"}}, "documentation": "<p>Represents an event that occurred during an flow execution. This is a union type that can contain one of several event types, such as node input and output events; flow input and output events; condition node result events, or failure events.</p> <note> <p>Flow executions is in preview release for Amazon Bedrock and is subject to change.</p> </note>", "union": true}, "FlowExecutionEventType": {"type": "string", "enum": ["Node", "Flow"]}, "FlowExecutionEvents": {"type": "list", "member": {"shape": "FlowExecutionEvent"}, "max": 10, "min": 0}, "FlowExecutionId": {"type": "string", "max": 100, "min": 2, "pattern": "^[0-9a-zA-Z._:-]+$"}, "FlowExecutionIdentifier": {"type": "string", "max": 2048, "min": 0, "pattern": "^[a-zA-Z0-9-]{1,36}$|^(arn:aws:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:flow/[0-9a-zA-Z]{10}/alias/[0-9a-zA-Z]{10}/execution/[a-zA-Z0-9-]{1,36})$"}, "FlowExecutionInputEvent": {"type": "structure", "required": ["fields", "nodeName", "timestamp"], "members": {"fields": {"shape": "FlowInputFields", "documentation": "<p>A list of input fields provided to the flow.</p>"}, "nodeName": {"shape": "NodeName", "documentation": "<p>The name of the node that receives the inputs.</p>"}, "timestamp": {"shape": "DateTimestamp", "documentation": "<p>The timestamp when the inputs are provided.</p>"}}, "documentation": "<p>Contains information about the inputs provided to the flow at the start of a flow execution.</p> <note> <p>Flow executions is in preview release for Amazon Bedrock and is subject to change.</p> </note>", "sensitive": true}, "FlowExecutionName": {"type": "string", "max": 36, "min": 0, "pattern": "^[a-zA-Z0-9-]{1,36}$"}, "FlowExecutionOutputEvent": {"type": "structure", "required": ["fields", "nodeName", "timestamp"], "members": {"fields": {"shape": "FlowOutputFields", "documentation": "<p>A list of output fields produced by the flow.</p>"}, "nodeName": {"shape": "NodeName", "documentation": "<p>The name of the node that produces the outputs.</p>"}, "timestamp": {"shape": "DateTimestamp", "documentation": "<p>The timestamp when the outputs are produced.</p>"}}, "documentation": "<p>Contains information about the outputs produced by the flow during a flow execution.</p> <note> <p>Flow executions is in preview release for Amazon Bedrock and is subject to change.</p> </note>", "sensitive": true}, "FlowExecutionRoleArn": {"type": "string", "max": 2048, "min": 0, "pattern": "^arn:aws(-[^:]+)?:iam::([0-9]{12})?:role/(service-role/)?.+$"}, "FlowExecutionStatus": {"type": "string", "enum": ["Running", "Succeeded", "Failed", "TimedOut", "Aborted"]}, "FlowExecutionSummaries": {"type": "list", "member": {"shape": "FlowExecutionSummary"}, "max": 10, "min": 0}, "FlowExecutionSummary": {"type": "structure", "required": ["createdAt", "executionArn", "flowAliasIdentifier", "flowIdentifier", "flowVersion", "status"], "members": {"createdAt": {"shape": "DateTimestamp", "documentation": "<p>The timestamp when the flow execution was created.</p>"}, "endedAt": {"shape": "DateTimestamp", "documentation": "<p>The timestamp when the flow execution ended. This field is only populated when the execution has completed, failed, timed out, or been aborted.</p>"}, "executionArn": {"shape": "FlowExecutionIdentifier", "documentation": "<p>The Amazon Resource Name (ARN) that uniquely identifies the flow execution.</p>"}, "flowAliasIdentifier": {"shape": "FlowAliasIdentifier", "documentation": "<p>The unique identifier of the flow alias used for the execution.</p>"}, "flowIdentifier": {"shape": "FlowIdentifier", "documentation": "<p>The unique identifier of the flow.</p>"}, "flowVersion": {"shape": "Version", "documentation": "<p>The version of the flow used for the execution.</p>"}, "status": {"shape": "FlowExecutionStatus", "documentation": "<p>The current status of the flow execution.</p> <p>Flow executions time out after 24 hours.</p>"}}, "documentation": "<p>Contains summary information about a flow execution, including its status, timestamps, and identifiers.</p> <note> <p>Flow executions is in preview release for Amazon Bedrock and is subject to change.</p> </note>"}, "FlowFailureEvent": {"type": "structure", "required": ["errorCode", "errorMessage", "timestamp"], "members": {"errorCode": {"shape": "FlowErrorCode", "documentation": "<p>The error code that identifies the type of failure that occurred.</p>"}, "errorMessage": {"shape": "String", "documentation": "<p>A descriptive message that provides details about the failure.</p>"}, "timestamp": {"shape": "DateTimestamp", "documentation": "<p>The timestamp when the failure occurred.</p>"}}, "documentation": "<p>Contains information about a failure that occurred at the flow level during a flow execution.</p> <note> <p>Flow executions is in preview release for Amazon Bedrock and is subject to change.</p> </note>", "sensitive": true}, "FlowIdentifier": {"type": "string", "max": 2048, "min": 0, "pattern": "^(arn:aws:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:flow/[0-9a-zA-Z]{10})|([0-9a-zA-Z]{10})$"}, "FlowInput": {"type": "structure", "required": ["content", "nodeName"], "members": {"content": {"shape": "FlowInputContent", "documentation": "<p>Contains information about an input into the prompt flow.</p>"}, "nodeInputName": {"shape": "NodeInputName", "documentation": "<p>The name of the input from the flow input node.</p>"}, "nodeName": {"shape": "NodeName", "documentation": "<p>The name of the flow input node that begins the prompt flow.</p>"}, "nodeOutputName": {"shape": "NodeOutputName", "documentation": "<p>The name of the output from the flow input node that begins the prompt flow.</p>"}}, "documentation": "<p>Contains information about an input into the prompt flow and where to send it.</p>"}, "FlowInputContent": {"type": "structure", "members": {"document": {"shape": "Document", "documentation": "<p>The input to send to the prompt flow input node.</p>"}}, "documentation": "<p>Contains information about an input into the flow.</p>", "sensitive": true, "union": true}, "FlowInputField": {"type": "structure", "required": ["content", "name"], "members": {"content": {"shape": "FlowExecutionContent", "documentation": "<p>The content of the input field, which can contain text or structured data.</p>"}, "name": {"shape": "NodeInputName", "documentation": "<p>The name of the input field as defined in the flow's input schema.</p>"}}, "documentation": "<p>Represents an input field provided to a flow during a flow execution.</p> <note> <p>Flow executions is in preview release for Amazon Bedrock and is subject to change.</p> </note>", "sensitive": true}, "FlowInputFields": {"type": "list", "member": {"shape": "FlowInputField"}, "max": 5, "min": 1}, "FlowInputs": {"type": "list", "member": {"shape": "FlowInput"}, "max": 1, "min": 1}, "FlowMultiTurnInputContent": {"type": "structure", "members": {"document": {"shape": "Document", "documentation": "<p>The requested additional input to send back to the multi-turn flow node.</p>"}}, "documentation": "<p>The content structure containing input information for multi-turn flow interactions.</p>", "union": true}, "FlowMultiTurnInputRequestEvent": {"type": "structure", "required": ["content", "nodeName", "nodeType"], "members": {"content": {"shape": "FlowMultiTurnInputContent", "documentation": "<p>The content payload containing the input request details for the multi-turn interaction.</p>"}, "nodeName": {"shape": "NodeName", "documentation": "<p>The name of the node in the flow that is requesting the input.</p>"}, "nodeType": {"shape": "NodeType", "documentation": "<p>The type of the node in the flow that is requesting the input.</p>"}}, "documentation": "<p>Response object from the flow multi-turn node requesting additional information.</p>", "event": true, "sensitive": true}, "FlowOutputContent": {"type": "structure", "members": {"document": {"shape": "Document", "documentation": "<p>The content in the output.</p>"}}, "documentation": "<p>Contains information about the content in an output from prompt flow invocation.</p>", "union": true}, "FlowOutputEvent": {"type": "structure", "required": ["content", "nodeName", "nodeType"], "members": {"content": {"shape": "FlowOutputContent", "documentation": "<p>The content in the output.</p>"}, "nodeName": {"shape": "NodeName", "documentation": "<p>The name of the flow output node that the output is from.</p>"}, "nodeType": {"shape": "NodeType", "documentation": "<p>The type of the node that the output is from.</p>"}}, "documentation": "<p>Contains information about an output from prompt flow invoction.</p>", "event": true, "sensitive": true}, "FlowOutputField": {"type": "structure", "required": ["content", "name"], "members": {"content": {"shape": "FlowExecutionContent", "documentation": "<p>The content of the output field, which can contain text or structured data.</p>"}, "name": {"shape": "NodeOutputName", "documentation": "<p>The name of the output field as defined in the flow's output schema.</p>"}}, "documentation": "<p>Represents an output field produced by a flow during a flow execution.</p> <note> <p>Flow executions is in preview release for Amazon Bedrock and is subject to change.</p> </note>", "sensitive": true}, "FlowOutputFields": {"type": "list", "member": {"shape": "FlowOutputField"}, "max": 5, "min": 1}, "FlowResponseStream": {"type": "structure", "members": {"accessDeniedException": {"shape": "AccessDeniedException", "documentation": "<p>The request is denied because of missing access permissions. Check your permissions and retry your request.</p>"}, "badGatewayException": {"shape": "BadGatewayException", "documentation": "<p>There was an issue with a dependency due to a server issue. Retry your request.</p>"}, "conflictException": {"shape": "ConflictException", "documentation": "<p>There was a conflict performing an operation. Resolve the conflict and retry your request.</p>"}, "dependencyFailedException": {"shape": "DependencyFailedException", "documentation": "<p>There was an issue with a dependency. Check the resource configurations and retry the request.</p>"}, "flowCompletionEvent": {"shape": "FlowCompletionEvent", "documentation": "<p>Contains information about why the flow completed.</p>"}, "flowMultiTurnInputRequestEvent": {"shape": "FlowMultiTurnInputRequestEvent", "documentation": "<p>The event stream containing the multi-turn input request information from the flow.</p>"}, "flowOutputEvent": {"shape": "FlowOutputEvent", "documentation": "<p>Contains information about an output from flow invocation.</p>"}, "flowTraceEvent": {"shape": "FlowTraceEvent", "documentation": "<p>Contains information about a trace, which tracks an input or output for a node in the flow.</p>"}, "internalServerException": {"shape": "InternalServerException", "documentation": "<p>An internal server error occurred. Retry your request.</p>"}, "resourceNotFoundException": {"shape": "ResourceNotFoundException", "documentation": "<p>The specified resource Amazon Resource Name (ARN) was not found. Check the Amazon Resource Name (ARN) and try your request again.</p>"}, "serviceQuotaExceededException": {"shape": "ServiceQuotaExceededException", "documentation": "<p>The number of requests exceeds the service quota. Resubmit your request later.</p>"}, "throttlingException": {"shape": "ThrottlingException", "documentation": "<p>The number of requests exceeds the limit. Resubmit your request later.</p>"}, "validationException": {"shape": "ValidationException", "documentation": "<p>Input validation failed. Check your request parameters and retry the request.</p>"}}, "documentation": "<p>The output of the flow.</p>", "eventstream": true}, "FlowTrace": {"type": "structure", "members": {"conditionNodeResultTrace": {"shape": "FlowTraceConditionNodeResultEvent", "documentation": "<p>Contains information about an output from a condition node.</p>"}, "nodeActionTrace": {"shape": "FlowTraceNodeActionEvent", "documentation": "<p>Contains information about an action (operation) called by a node. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/flows-trace.html\">Track each step in your prompt flow by viewing its trace in Amazon Bedrock</a>.</p>"}, "nodeInputTrace": {"shape": "FlowTraceNodeInputEvent", "documentation": "<p>Contains information about the input into a node.</p>"}, "nodeOutputTrace": {"shape": "FlowTraceNodeOutputEvent", "documentation": "<p>Contains information about the output from a node.</p>"}}, "documentation": "<p>Contains information about an input or output for a node in the flow. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/flows-trace.html\">Track each step in your prompt flow by viewing its trace in Amazon Bedrock</a>.</p>", "sensitive": true, "union": true}, "FlowTraceCondition": {"type": "structure", "required": ["conditionName"], "members": {"conditionName": {"shape": "String", "documentation": "<p>The name of the condition.</p>"}}, "documentation": "<p>Contains information about a condition that was satisfied. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/flows-trace.html\">Track each step in your prompt flow by viewing its trace in Amazon Bedrock</a>.</p>", "sensitive": true}, "FlowTraceConditionNodeResultEvent": {"type": "structure", "required": ["nodeName", "satisfiedConditions", "timestamp"], "members": {"nodeName": {"shape": "NodeName", "documentation": "<p>The name of the condition node.</p>"}, "satisfiedConditions": {"shape": "FlowTraceConditions", "documentation": "<p>An array of objects containing information about the conditions that were satisfied.</p>"}, "timestamp": {"shape": "DateTimestamp", "documentation": "<p>The date and time that the trace was returned.</p>"}}, "documentation": "<p>Contains information about an output from a condition node. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/flows-trace.html\">Track each step in your prompt flow by viewing its trace in Amazon Bedrock</a>.</p>", "sensitive": true}, "FlowTraceConditions": {"type": "list", "member": {"shape": "FlowTraceCondition"}, "max": 5, "min": 1}, "FlowTraceEvent": {"type": "structure", "required": ["trace"], "members": {"trace": {"shape": "FlowTrace", "documentation": "<p>The trace object containing information about an input or output for a node in the flow.</p>"}}, "documentation": "<p>Contains information about a trace, which tracks an input or output for a node in the flow. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/flows-trace.html\">Track each step in your prompt flow by viewing its trace in Amazon Bedrock</a>.</p>", "event": true}, "FlowTraceNodeActionEvent": {"type": "structure", "required": ["nodeName", "operationName", "requestId", "serviceName", "timestamp"], "members": {"nodeName": {"shape": "NodeName", "documentation": "<p>The name of the node that called the operation.</p>"}, "operationName": {"shape": "String", "documentation": "<p>The name of the operation that the node called.</p>"}, "requestId": {"shape": "String", "documentation": "<p>The ID of the request that the node made to the operation.</p>"}, "serviceName": {"shape": "String", "documentation": "<p>The name of the service that the node called.</p>"}, "timestamp": {"shape": "DateTimestamp", "documentation": "<p>The date and time that the operation was called.</p>"}}, "documentation": "<p>Contains information about an action (operation) called by a node in an Amazon Bedrock flow. The service generates action events for calls made by prompt nodes, agent nodes, and Amazon Web Services Lambda nodes. </p>", "sensitive": true}, "FlowTraceNodeInputContent": {"type": "structure", "members": {"document": {"shape": "Document", "documentation": "<p>The content of the node input.</p>"}}, "documentation": "<p>Contains the content of the node input. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/flows-trace.html\">Track each step in your prompt flow by viewing its trace in Amazon Bedrock</a>.</p>", "sensitive": true, "union": true}, "FlowTraceNodeInputEvent": {"type": "structure", "required": ["fields", "nodeName", "timestamp"], "members": {"fields": {"shape": "FlowTraceNodeInputFields", "documentation": "<p>An array of objects containing information about each field in the input.</p>"}, "nodeName": {"shape": "NodeName", "documentation": "<p>The name of the node that received the input.</p>"}, "timestamp": {"shape": "DateTimestamp", "documentation": "<p>The date and time that the trace was returned.</p>"}}, "documentation": "<p>Contains information about the input into a node. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/flows-trace.html\">Track each step in your prompt flow by viewing its trace in Amazon Bedrock</a>.</p>", "sensitive": true}, "FlowTraceNodeInputField": {"type": "structure", "required": ["content", "nodeInputName"], "members": {"content": {"shape": "FlowTraceNodeInputContent", "documentation": "<p>The content of the node input.</p>"}, "nodeInputName": {"shape": "NodeInputName", "documentation": "<p>The name of the node input.</p>"}}, "documentation": "<p>Contains information about a field in the input into a node. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/flows-trace.html\">Track each step in your prompt flow by viewing its trace in Amazon Bedrock</a>.</p>", "sensitive": true}, "FlowTraceNodeInputFields": {"type": "list", "member": {"shape": "FlowTraceNodeInputField"}, "max": 5, "min": 1}, "FlowTraceNodeOutputContent": {"type": "structure", "members": {"document": {"shape": "Document", "documentation": "<p>The content of the node output.</p>"}}, "documentation": "<p>Contains the content of the node output. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/flows-trace.html\">Track each step in your prompt flow by viewing its trace in Amazon Bedrock</a>.</p>", "union": true}, "FlowTraceNodeOutputEvent": {"type": "structure", "required": ["fields", "nodeName", "timestamp"], "members": {"fields": {"shape": "FlowTraceNodeOutputFields", "documentation": "<p>An array of objects containing information about each field in the output.</p>"}, "nodeName": {"shape": "NodeName", "documentation": "<p>The name of the node that yielded the output.</p>"}, "timestamp": {"shape": "DateTimestamp", "documentation": "<p>The date and time that the trace was returned.</p>"}}, "documentation": "<p>Contains information about the output from a node. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/flows-trace.html\">Track each step in your prompt flow by viewing its trace in Amazon Bedrock</a>.</p>", "sensitive": true}, "FlowTraceNodeOutputField": {"type": "structure", "required": ["content", "nodeOutputName"], "members": {"content": {"shape": "FlowTraceNodeOutputContent", "documentation": "<p>The content of the node output.</p>"}, "nodeOutputName": {"shape": "NodeOutputName", "documentation": "<p>The name of the node output.</p>"}}, "documentation": "<p>Contains information about a field in the output from a node. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/flows-trace.html\">Track each step in your prompt flow by viewing its trace in Amazon Bedrock</a>.</p>", "sensitive": true}, "FlowTraceNodeOutputFields": {"type": "list", "member": {"shape": "FlowTraceNodeOutputField"}, "max": 2, "min": 1}, "Function": {"type": "string", "sensitive": true}, "FunctionDefinition": {"type": "structure", "required": ["name"], "members": {"description": {"shape": "FunctionDescription", "documentation": "<p> A description of the function and its purpose. </p>"}, "name": {"shape": "ResourceName", "documentation": "<p> A name for the function. </p>"}, "parameters": {"shape": "ParameterMap", "documentation": "<p> The parameters that the agent elicits from the user to fulfill the function. </p>"}, "requireConfirmation": {"shape": "RequireConfirmation", "documentation": "<p> Contains information if user confirmation is required to invoke the function. </p>"}}, "documentation": "<p> Defines parameters that the agent needs to invoke from the user to complete the function. Corresponds to an action in an action group. </p>"}, "FunctionDescription": {"type": "string", "max": 1200, "min": 1}, "FunctionInvocationInput": {"type": "structure", "required": ["actionGroup"], "members": {"actionGroup": {"shape": "String", "documentation": "<p>The action group that the function belongs to.</p>"}, "actionInvocationType": {"shape": "ActionInvocationType", "documentation": "<p>Contains information about the function to invoke,</p>"}, "agentId": {"shape": "String", "documentation": "<p>The agent's ID.</p>"}, "collaboratorName": {"shape": "Name", "documentation": "<p>The collaborator's name.</p>"}, "function": {"shape": "String", "documentation": "<p>The name of the function.</p>"}, "parameters": {"shape": "FunctionParameters", "documentation": "<p>A list of parameters of the function.</p>"}}, "documentation": "<p>Contains information about the function that the agent predicts should be called.</p> <p>This data type is used in the following API operations:</p> <ul> <li> <p>In the <code>returnControl</code> field of the <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_InvokeAgent.html#API_agent-runtime_InvokeAgent_ResponseSyntax\">InvokeAgent response</a> </p> </li> </ul>"}, "FunctionParameter": {"type": "structure", "members": {"name": {"shape": "String", "documentation": "<p>The name of the parameter.</p>"}, "type": {"shape": "String", "documentation": "<p>The data type of the parameter.</p>"}, "value": {"shape": "String", "documentation": "<p>The value of the parameter.</p>"}}, "documentation": "<p>Contains information about a parameter of the function.</p> <p>This data type is used in the following API operations:</p> <ul> <li> <p>In the <code>returnControl</code> field of the <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_InvokeAgent.html#API_agent-runtime_InvokeAgent_ResponseSyntax\">InvokeAgent response</a> </p> </li> </ul>"}, "FunctionParameters": {"type": "list", "member": {"shape": "FunctionParameter"}}, "FunctionResult": {"type": "structure", "required": ["actionGroup"], "members": {"actionGroup": {"shape": "String", "documentation": "<p>The action group that the function belongs to.</p>"}, "agentId": {"shape": "String", "documentation": "<p>The agent's ID.</p>"}, "confirmationState": {"shape": "ConfirmationState", "documentation": "<p>Contains the user confirmation information about the function that was called.</p>"}, "function": {"shape": "String", "documentation": "<p>The name of the function that was called.</p>"}, "responseBody": {"shape": "ResponseBody", "documentation": "<p>The response from the function call using the parameters. The response might be returned directly or from the Lambda function. Specify <code>TEXT</code> or <code>IMAGES</code>. The key of the object is the content type. You can only specify one type. If you specify <code>IMAGES</code>, you can specify only one image. You can specify images only when the function in the <code>returnControlInvocationResults</code> is a computer use action. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/agent-computer-use.html\">Configure an Amazon Bedrock Agent to complete tasks with computer use tools</a>.</p>"}, "responseState": {"shape": "ResponseState", "documentation": "<p>Controls the final response state returned to end user when API/Function execution failed. When this state is FAILURE, the request would fail with dependency failure exception. When this state is REPROMPT, the API/function response will be sent to model for re-prompt</p>"}}, "documentation": "<p>Contains information about the function that was called from the action group and the response that was returned.</p> <p>This data type is used in the following API operations:</p> <ul> <li> <p>In the <code>returnControlInvocationResults</code> of the <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_InvokeAgent.html#API_agent-runtime_InvokeAgent_RequestSyntax\">InvokeAgent request</a> </p> </li> </ul>"}, "FunctionSchema": {"type": "structure", "members": {"functions": {"shape": "Functions", "documentation": "<p> A list of functions that each define an action in the action group. </p>"}}, "documentation": "<p> Contains details about the function schema for the action group or the JSON or YAML-formatted payload defining the schema. </p>", "union": true}, "Functions": {"type": "list", "member": {"shape": "FunctionDefinition"}}, "GenerateQueryRequest": {"type": "structure", "required": ["queryGenerationInput", "transformationConfiguration"], "members": {"queryGenerationInput": {"shape": "QueryGenerationInput", "documentation": "<p>Specifies information about a natural language query to transform into SQL.</p>"}, "transformationConfiguration": {"shape": "TransformationConfiguration", "documentation": "<p>Specifies configurations for transforming the natural language query into SQL.</p>"}}}, "GenerateQueryResponse": {"type": "structure", "members": {"queries": {"shape": "GeneratedQueries", "documentation": "<p>A list of objects, each of which defines a generated query that can correspond to the natural language queries.</p>"}}}, "GeneratedQueries": {"type": "list", "member": {"shape": "GeneratedQuery"}, "min": 0}, "GeneratedQuery": {"type": "structure", "members": {"sql": {"shape": "String", "documentation": "<p>An SQL query that corresponds to the natural language query.</p>"}, "type": {"shape": "GeneratedQueryType", "documentation": "<p>The type of transformed query.</p>"}}, "documentation": "<p>Contains information about a query generated for a natural language query.</p>", "sensitive": true}, "GeneratedQueryType": {"type": "string", "enum": ["REDSHIFT_SQL"]}, "GeneratedResponsePart": {"type": "structure", "members": {"textResponsePart": {"shape": "TextResponsePart", "documentation": "<p>Contains metadata about a textual part of the generated response that is accompanied by a citation.</p>"}}, "documentation": "<p>Contains metadata about a part of the generated response that is accompanied by a citation.</p> <p>This data type is used in the following API operations:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_InvokeAgent.html#API_agent-runtime_InvokeAgent_ResponseSyntax\">InvokeAgent response</a> – in the <code>generatedResponsePart</code> field</p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_RetrieveAndGenerate.html#API_agent-runtime_RetrieveAndGenerate_ResponseSyntax\">RetrieveAndGenerate response</a> – in the <code>generatedResponsePart</code> field</p> </li> </ul>"}, "GenerationConfiguration": {"type": "structure", "members": {"additionalModelRequestFields": {"shape": "AdditionalModelRequestFields", "documentation": "<p> Additional model parameters and corresponding values not included in the textInferenceConfig structure for a knowledge base. This allows users to provide custom model parameters specific to the language model being used. </p>"}, "guardrailConfiguration": {"shape": "GuardrailConfiguration", "documentation": "<p>The configuration details for the guardrail.</p>"}, "inferenceConfig": {"shape": "InferenceConfig", "documentation": "<p> Configuration settings for inference when using RetrieveAndGenerate to generate responses while using a knowledge base as a source. </p>"}, "performanceConfig": {"shape": "PerformanceConfiguration", "documentation": "<p>The latency configuration for the model.</p>"}, "promptTemplate": {"shape": "PromptTemplate", "documentation": "<p>Contains the template for the prompt that's sent to the model for response generation. Generation prompts must include the <code>$search_results$</code> variable. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/prompt-placeholders.html\">Use placeholder variables</a> in the user guide.</p>"}}, "documentation": "<p>Contains configurations for response generation based on the knowledge base query results.</p> <p>This data type is used in the following API operations:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_RetrieveAndGenerate.html#API_agent-runtime_RetrieveAndGenerate_RequestSyntax\">RetrieveAndGenerate request</a> </p> </li> </ul>"}, "GetAgentMemoryRequest": {"type": "structure", "required": ["agentAliasId", "agentId", "memoryId", "memoryType"], "members": {"agentAliasId": {"shape": "AgentAliasId", "documentation": "<p>The unique identifier of an alias of an agent.</p>", "location": "uri", "locationName": "agentAliasId"}, "agentId": {"shape": "AgentId", "documentation": "<p>The unique identifier of the agent to which the alias belongs.</p>", "location": "uri", "locationName": "agentId"}, "maxItems": {"shape": "MaxResults", "documentation": "<p>The maximum number of items to return in the response. If the total number of results is greater than this value, use the token returned in the response in the <code>nextToken</code> field when making another request to return the next batch of results.</p>", "location": "querystring", "locationName": "maxItems"}, "memoryId": {"shape": "MemoryId", "documentation": "<p>The unique identifier of the memory. </p>", "location": "querystring", "locationName": "memoryId"}, "memoryType": {"shape": "MemoryType", "documentation": "<p>The type of memory.</p>", "location": "querystring", "locationName": "memoryType"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the total number of results is greater than the maxItems value provided in the request, enter the token returned in the <code>nextToken</code> field in the response in this field to return the next batch of results.</p>", "location": "querystring", "locationName": "nextToken"}}}, "GetAgentMemoryResponse": {"type": "structure", "members": {"memoryContents": {"shape": "Memories", "documentation": "<p>Contains details of the sessions stored in the memory</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the total number of results is greater than the maxItems value provided in the request, use this token when making another request in the <code>nextToken</code> field to return the next batch of results.</p>"}}}, "GetExecutionFlowSnapshotRequest": {"type": "structure", "required": ["executionIdentifier", "flowAliasIdentifier", "flowIdentifier"], "members": {"executionIdentifier": {"shape": "FlowExecutionIdentifier", "documentation": "<p>The unique identifier of the flow execution.</p>", "location": "uri", "locationName": "executionIdentifier"}, "flowAliasIdentifier": {"shape": "FlowAliasIdentifier", "documentation": "<p>The unique identifier of the flow alias used for the flow execution.</p>", "location": "uri", "locationName": "flowAliasIdentifier"}, "flowIdentifier": {"shape": "FlowIdentifier", "documentation": "<p>The unique identifier of the flow.</p>", "location": "uri", "locationName": "flowIdentifier"}}}, "GetExecutionFlowSnapshotResponse": {"type": "structure", "required": ["definition", "executionRoleArn", "flowAliasIdentifier", "flowIdentifier", "flowVersion"], "members": {"customerEncryptionKeyArn": {"shape": "KmsKeyArn", "documentation": "<p>The Amazon Resource Name (ARN) of the customer managed KMS key that's used to encrypt the flow snapshot.</p>"}, "definition": {"shape": "String", "documentation": "<p>The flow definition used for the flow execution, including the nodes, connections, and configuration at the time when the execution started.</p> <p>The definition returns as a string that follows the structure of a <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent_FlowDefinition.html\">FlowDefinition</a> object.</p>"}, "executionRoleArn": {"shape": "FlowExecutionRoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM service role that's used by the flow execution.</p>"}, "flowAliasIdentifier": {"shape": "FlowAliasIdentifier", "documentation": "<p>The unique identifier of the flow alias used for the flow execution.</p>"}, "flowIdentifier": {"shape": "FlowIdentifier", "documentation": "<p>The unique identifier of the flow.</p>"}, "flowVersion": {"shape": "Version", "documentation": "<p>The version of the flow used for the flow execution.</p>"}}}, "GetFlowExecutionRequest": {"type": "structure", "required": ["executionIdentifier", "flowAliasIdentifier", "flowIdentifier"], "members": {"executionIdentifier": {"shape": "FlowExecutionIdentifier", "documentation": "<p>The unique identifier of the flow execution to retrieve.</p>", "location": "uri", "locationName": "executionIdentifier"}, "flowAliasIdentifier": {"shape": "FlowAliasIdentifier", "documentation": "<p>The unique identifier of the flow alias used for the execution.</p>", "location": "uri", "locationName": "flowAliasIdentifier"}, "flowIdentifier": {"shape": "FlowIdentifier", "documentation": "<p>The unique identifier of the flow.</p>", "location": "uri", "locationName": "flowIdentifier"}}}, "GetFlowExecutionResponse": {"type": "structure", "required": ["executionArn", "flowAliasIdentifier", "flowIdentifier", "flowVersion", "startedAt", "status"], "members": {"endedAt": {"shape": "DateTimestamp", "documentation": "<p>The timestamp when the flow execution ended. This field is only populated when the execution has completed, failed, timed out, or been aborted.</p>"}, "errors": {"shape": "FlowExecutionErrors", "documentation": "<p>A list of errors that occurred during the flow execution. Each error includes an error code, message, and the node where the error occurred, if applicable.</p>"}, "executionArn": {"shape": "FlowExecutionIdentifier", "documentation": "<p>The Amazon Resource Name (ARN) that uniquely identifies the flow execution.</p>"}, "flowAliasIdentifier": {"shape": "FlowAliasIdentifier", "documentation": "<p>The unique identifier of the flow alias used for the execution.</p>"}, "flowIdentifier": {"shape": "FlowIdentifier", "documentation": "<p>The unique identifier of the flow.</p>"}, "flowVersion": {"shape": "Version", "documentation": "<p>The version of the flow used for the execution.</p>"}, "startedAt": {"shape": "DateTimestamp", "documentation": "<p>The timestamp when the flow execution started.</p>"}, "status": {"shape": "FlowExecutionStatus", "documentation": "<p>The current status of the flow execution.</p> <p>Flow executions time out after 24 hours.</p>"}}}, "GetInvocationStepRequest": {"type": "structure", "required": ["invocationIdentifier", "invocationStepId", "sessionIdentifier"], "members": {"invocationIdentifier": {"shape": "InvocationIdentifier", "documentation": "<p>The unique identifier for the invocation in UUID format.</p>"}, "invocationStepId": {"shape": "<PERSON><PERSON>", "documentation": "<p>The unique identifier (in UUID format) for the specific invocation step to retrieve.</p>", "location": "uri", "locationName": "invocationStepId"}, "sessionIdentifier": {"shape": "SessionIdentifier", "documentation": "<p>The unique identifier for the invocation step's associated session. You can specify either the session's <code>sessionId</code> or its Amazon Resource Name (ARN).</p>", "location": "uri", "locationName": "sessionIdentifier"}}}, "GetInvocationStepResponse": {"type": "structure", "required": ["invocationStep"], "members": {"invocationStep": {"shape": "InvocationStep", "documentation": "<p>The complete details of the requested invocation step.</p>"}}}, "GetSessionRequest": {"type": "structure", "required": ["sessionIdentifier"], "members": {"sessionIdentifier": {"shape": "SessionIdentifier", "documentation": "<p>A unique identifier for the session to retrieve. You can specify either the session's <code>sessionId</code> or its Amazon Resource Name (ARN).</p>", "location": "uri", "locationName": "sessionIdentifier"}}}, "GetSessionResponse": {"type": "structure", "required": ["createdAt", "lastUpdatedAt", "sessionArn", "sessionId", "sessionStatus"], "members": {"createdAt": {"shape": "DateTimestamp", "documentation": "<p>The timestamp for when the session was created.</p>"}, "encryptionKeyArn": {"shape": "KmsKeyArn", "documentation": "<p>The Amazon Resource Name (ARN) of the Key Management Service key used to encrypt the session data. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/session-encryption.html\">Amazon Bedrock session encryption</a>.</p>"}, "lastUpdatedAt": {"shape": "DateTimestamp", "documentation": "<p>The timestamp for when the session was last modified.</p>"}, "sessionArn": {"shape": "SessionArn", "documentation": "<p>The Amazon Resource Name (ARN) of the session.</p>"}, "sessionId": {"shape": "<PERSON><PERSON>", "documentation": "<p>The unique identifier for the session in UUID format.</p>"}, "sessionMetadata": {"shape": "SessionMetadataMap", "documentation": "<p>A map of key-value pairs containing attributes persisted across the session.</p>"}, "sessionStatus": {"shape": "SessionStatus", "documentation": "<p>The current status of the session.</p>"}}}, "GuadrailAction": {"type": "string", "enum": ["INTERVENED", "NONE"]}, "GuardrailAction": {"type": "string", "enum": ["INTERVENED", "NONE"]}, "GuardrailAssessment": {"type": "structure", "members": {"contentPolicy": {"shape": "GuardrailContentPolicyAssessment", "documentation": "<p>Content policy details of the Guardrail.</p>"}, "sensitiveInformationPolicy": {"shape": "GuardrailSensitiveInformationPolicyAssessment", "documentation": "<p>Sensitive Information policy details of Guardrail.</p>"}, "topicPolicy": {"shape": "GuardrailTopicPolicyAssessment", "documentation": "<p>Topic policy details of the Guardrail.</p>"}, "wordPolicy": {"shape": "GuardrailWordPolicyAssessment", "documentation": "<p>Word policy details of the Guardrail.</p>"}}, "documentation": "<p>Assessment details of the content analyzed by Guardrails.</p>", "sensitive": true}, "GuardrailAssessmentList": {"type": "list", "member": {"shape": "GuardrailAssessment"}}, "GuardrailConfiguration": {"type": "structure", "required": ["guardrailId", "guardrailVersion"], "members": {"guardrailId": {"shape": "GuardrailConfigurationGuardrailIdString", "documentation": "<p>The unique identifier for the guardrail.</p>"}, "guardrailVersion": {"shape": "GuardrailConfigurationGuardrailVersionString", "documentation": "<p>The version of the guardrail.</p>"}}, "documentation": "<p>The configuration details for the guardrail.</p>"}, "GuardrailConfigurationGuardrailIdString": {"type": "string", "max": 64, "min": 0, "pattern": "^[a-z0-9]+$"}, "GuardrailConfigurationGuardrailVersionString": {"type": "string", "max": 5, "min": 1, "pattern": "^(([1-9][0-9]{0,7})|(DRAFT))$"}, "GuardrailConfigurationWithArn": {"type": "structure", "required": ["guardrailIdentifier", "guardrailVersion"], "members": {"guardrailIdentifier": {"shape": "GuardrailIdentifierWithArn", "documentation": "<p> The unique identifier for the guardrail. </p>"}, "guardrailVersion": {"shape": "GuardrailVersion", "documentation": "<p> The version of the guardrail. </p>"}}, "documentation": "<p> The configuration details for the guardrail. </p>"}, "GuardrailContentFilter": {"type": "structure", "members": {"action": {"shape": "GuardrailContentPolicyAction", "documentation": "<p>The action placed on the content by the Guardrail filter.</p>"}, "confidence": {"shape": "GuardrailContentFilterConfidence", "documentation": "<p>The confidence level regarding the content detected in the filter by the Guardrail.</p>"}, "type": {"shape": "GuardrailContentFilterType", "documentation": "<p>The type of content detected in the filter by the Guardrail.</p>"}}, "documentation": "<p>Details of the content filter used in the Guardrail.</p>", "sensitive": true}, "GuardrailContentFilterConfidence": {"type": "string", "enum": ["NONE", "LOW", "MEDIUM", "HIGH"]}, "GuardrailContentFilterList": {"type": "list", "member": {"shape": "GuardrailContentFilter"}, "sensitive": true}, "GuardrailContentFilterType": {"type": "string", "enum": ["INSULTS", "HATE", "SEXUAL", "VIOLENCE", "MISCONDUCT", "PROMPT_ATTACK"]}, "GuardrailContentPolicyAction": {"type": "string", "enum": ["BLOCKED"]}, "GuardrailContentPolicyAssessment": {"type": "structure", "members": {"filters": {"shape": "GuardrailContentFilterList", "documentation": "<p>The filter details of the policy assessment used in the Guardrails filter.</p>"}}, "documentation": "<p>The details of the policy assessment in the Guardrails filter.</p>", "sensitive": true}, "GuardrailCustomWord": {"type": "structure", "members": {"action": {"shape": "GuardrailWordPolicyAction", "documentation": "<p>The action details for the custom word filter in the Guardrail.</p>"}, "match": {"shape": "String", "documentation": "<p>The match details for the custom word filter in the Guardrail.</p>"}}, "documentation": "<p>The custom word details for the filter in the Guardrail.</p>", "sensitive": true}, "GuardrailCustomWordList": {"type": "list", "member": {"shape": "GuardrailCustomWord"}, "sensitive": true}, "GuardrailEvent": {"type": "structure", "members": {"action": {"shape": "GuadrailAction", "documentation": "<p>The guardrail action.</p>"}}, "documentation": "<p>A guardrail event.</p>", "event": true}, "GuardrailIdentifierWithArn": {"type": "string", "max": 2048, "min": 0, "pattern": "^(([a-z0-9]+)|(arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:guardrail/[a-z0-9]+))$"}, "GuardrailManagedWord": {"type": "structure", "members": {"action": {"shape": "GuardrailWordPolicyAction", "documentation": "<p>The action details for the managed word filter in the Guardrail.</p>"}, "match": {"shape": "String", "documentation": "<p>The match details for the managed word filter in the Guardrail.</p>"}, "type": {"shape": "GuardrailManagedWordType", "documentation": "<p>The type details for the managed word filter in the Guardrail.</p>"}}, "documentation": "<p>The managed word details for the filter in the Guardrail.</p>", "sensitive": true}, "GuardrailManagedWordList": {"type": "list", "member": {"shape": "GuardrailManagedWord"}, "sensitive": true}, "GuardrailManagedWordType": {"type": "string", "enum": ["PROFANITY"]}, "GuardrailPiiEntityFilter": {"type": "structure", "members": {"action": {"shape": "GuardrailSensitiveInformationPolicyAction", "documentation": "<p>The action of the Guardrail filter to identify and remove PII.</p>"}, "match": {"shape": "String", "documentation": "<p>The match to settings in the Guardrail filter to identify and remove PII.</p>"}, "type": {"shape": "GuardrailPiiEntityType", "documentation": "<p>The type of PII the Guardrail filter has identified and removed.</p>"}}, "documentation": "<p>The Guardrail filter to identify and remove personally identifiable information (PII).</p>", "sensitive": true}, "GuardrailPiiEntityFilterList": {"type": "list", "member": {"shape": "GuardrailPiiEntityFilter"}, "sensitive": true}, "GuardrailPiiEntityType": {"type": "string", "enum": ["ADDRESS", "AGE", "AWS_ACCESS_KEY", "AWS_SECRET_KEY", "CA_HEALTH_NUMBER", "CA_SOCIAL_INSURANCE_NUMBER", "CREDIT_DEBIT_CARD_CVV", "CREDIT_DEBIT_CARD_EXPIRY", "CREDIT_DEBIT_CARD_NUMBER", "DRIVER_ID", "EMAIL", "INTERNATIONAL_BANK_ACCOUNT_NUMBER", "IP_ADDRESS", "LICENSE_PLATE", "MAC_ADDRESS", "NAME", "PASSWORD", "PHONE", "PIN", "SWIFT_CODE", "UK_NATIONAL_HEALTH_SERVICE_NUMBER", "UK_NATIONAL_INSURANCE_NUMBER", "UK_UNIQUE_TAXPAYER_REFERENCE_NUMBER", "URL", "USERNAME", "US_BANK_ACCOUNT_NUMBER", "US_BANK_ROUTING_NUMBER", "US_INDIVIDUAL_TAX_IDENTIFICATION_NUMBER", "US_PASSPORT_NUMBER", "US_SOCIAL_SECURITY_NUMBER", "VEHICLE_IDENTIFICATION_NUMBER"]}, "GuardrailRegexFilter": {"type": "structure", "members": {"action": {"shape": "GuardrailSensitiveInformationPolicyAction", "documentation": "<p>The action details for the regex filter used in the Guardrail.</p>"}, "match": {"shape": "String", "documentation": "<p>The match details for the regex filter used in the Guardrail.</p>"}, "name": {"shape": "String", "documentation": "<p>The name details for the regex filter used in the Guardrail.</p>"}, "regex": {"shape": "String", "documentation": "<p>The regex details for the regex filter used in the Guardrail.</p>"}}, "documentation": "<p>The details for the regex filter used in the Guardrail.</p>", "sensitive": true}, "GuardrailRegexFilterList": {"type": "list", "member": {"shape": "GuardrailRegexFilter"}, "sensitive": true}, "GuardrailSensitiveInformationPolicyAction": {"type": "string", "enum": ["BLOCKED", "ANONYMIZED"]}, "GuardrailSensitiveInformationPolicyAssessment": {"type": "structure", "members": {"piiEntities": {"shape": "GuardrailPiiEntityFilterList", "documentation": "<p>The details of the PII entities used in the sensitive policy assessment for the Guardrail.</p>"}, "regexes": {"shape": "GuardrailRegexFilterList", "documentation": "<p>The details of the regexes used in the sensitive policy assessment for the Guardrail.</p>"}}, "documentation": "<p>The details of the sensitive policy assessment used in the Guardrail.</p>", "sensitive": true}, "GuardrailTopic": {"type": "structure", "members": {"action": {"shape": "GuardrailTopicPolicyAction", "documentation": "<p>The action details on a specific topic in the Guardrail.</p>"}, "name": {"shape": "String", "documentation": "<p>The name details on a specific topic in the Guardrail.</p>"}, "type": {"shape": "GuardrailTopicType", "documentation": "<p>The type details on a specific topic in the Guardrail.</p>"}}, "documentation": "<p>The details for a specific topic defined in the Guardrail.</p>", "sensitive": true}, "GuardrailTopicList": {"type": "list", "member": {"shape": "GuardrailTopic"}, "sensitive": true}, "GuardrailTopicPolicyAction": {"type": "string", "enum": ["BLOCKED"]}, "GuardrailTopicPolicyAssessment": {"type": "structure", "members": {"topics": {"shape": "GuardrailTopicList", "documentation": "<p>The topic details of the policy assessment used in the Guardrail.</p>"}}, "documentation": "<p>The details of the policy assessment used in the Guardrail.</p>", "sensitive": true}, "GuardrailTopicType": {"type": "string", "enum": ["DENY"]}, "GuardrailTrace": {"type": "structure", "members": {"action": {"shape": "GuardrailAction", "documentation": "<p>The trace action details used with the Guardrail.</p>"}, "inputAssessments": {"shape": "GuardrailAssessmentList", "documentation": "<p>The details of the input assessments used in the Guardrail Trace.</p>"}, "metadata": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>Contains information about the Guardrail output.</p>"}, "outputAssessments": {"shape": "GuardrailAssessmentList", "documentation": "<p>The details of the output assessments used in the Guardrail Trace.</p>"}, "traceId": {"shape": "TraceId", "documentation": "<p>The details of the trace Id used in the Guardrail Trace.</p>"}}, "documentation": "<p>The trace details used in the Guardrail.</p>", "sensitive": true}, "GuardrailVersion": {"type": "string", "max": 5, "min": 1, "pattern": "^(([1-9][0-9]{0,7})|(DRAFT))$"}, "GuardrailWordPolicyAction": {"type": "string", "enum": ["BLOCKED"]}, "GuardrailWordPolicyAssessment": {"type": "structure", "members": {"customWords": {"shape": "GuardrailCustomWordList", "documentation": "<p>The custom word details for words defined in the Guardrail filter.</p>"}, "managedWordLists": {"shape": "GuardrailManagedWordList", "documentation": "<p>The managed word lists for words defined in the Guardrail filter.</p>"}}, "documentation": "<p>The assessment details for words defined in the Guardrail filter.</p>", "sensitive": true}, "Identifier": {"type": "string", "max": 1024, "min": 1, "sensitive": true}, "ImageBlock": {"type": "structure", "required": ["format", "source"], "members": {"format": {"shape": "ImageFormat", "documentation": "<p>The format of the image.</p>"}, "source": {"shape": "ImageSource", "documentation": "<p>The source for the image.</p>"}}, "documentation": "<p>Image content for an invocation step.</p>"}, "ImageFormat": {"type": "string", "enum": ["png", "jpeg", "gif", "webp"]}, "ImageInput": {"type": "structure", "required": ["format", "source"], "members": {"format": {"shape": "ImageInputFormat", "documentation": "<p>The type of image in the result.</p>"}, "source": {"shape": "ImageInputSource", "documentation": "<p>The source of the image in the result.</p>"}}, "documentation": "<p>Details about an image in the result from a function in the action group invocation. You can specify images only when the function is a computer use action. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/agent-computer-use.html\">Configure an Amazon Bedrock Agent to complete tasks with computer use tools</a>.</p>"}, "ImageInputFormat": {"type": "string", "enum": ["png", "jpeg", "gif", "webp"]}, "ImageInputSource": {"type": "structure", "members": {"bytes": {"shape": "ImageInputSourceBytesBlob", "documentation": "<p> The raw image bytes for the image. If you use an Amazon Web Services SDK, you don't need to encode the image bytes in base64.</p>"}}, "documentation": "<p>Details about the source of an input image in the result from a function in the action group invocation.</p>", "union": true}, "ImageInputSourceBytesBlob": {"type": "blob", "min": 1}, "ImageInputs": {"type": "list", "member": {"shape": "ImageInput"}}, "ImageSource": {"type": "structure", "members": {"bytes": {"shape": "ImageSourceBytesBlob", "documentation": "<p> The raw image bytes for the image. If you use an Amazon Web Services SDK, you don't need to encode the image bytes in base64.</p>"}, "s3Location": {"shape": "S3Location", "documentation": "<p>The path to the Amazon S3 bucket where the image is stored.</p>"}}, "documentation": "<p>The source for an image.</p>", "union": true}, "ImageSourceBytesBlob": {"type": "blob", "min": 1}, "ImplicitFilterConfiguration": {"type": "structure", "required": ["metadataAttributes", "modelArn"], "members": {"metadataAttributes": {"shape": "MetadataAttributeSchemaList", "documentation": "<p>Metadata that can be used in a filter.</p>"}, "modelArn": {"shape": "BedrockModelArn", "documentation": "<p>The model that generates the filter.</p>"}}, "documentation": "<p>Settings for implicit filtering, where a model generates a metadata filter based on the prompt.</p>"}, "InferenceConfig": {"type": "structure", "members": {"textInferenceConfig": {"shape": "TextInferenceConfig", "documentation": "<p> Configuration settings specific to text generation while generating responses using RetrieveAndGenerate. </p>"}}, "documentation": "<p> The configuration for inference settings when generating responses using RetrieveAndGenerate. </p>"}, "InferenceConfiguration": {"type": "structure", "members": {"maximumLength": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The maximum number of tokens allowed in the generated response.</p>"}, "stopSequences": {"shape": "StopSequences", "documentation": "<p>A list of stop sequences. A stop sequence is a sequence of characters that causes the model to stop generating the response.</p>"}, "temperature": {"shape": "Temperature", "documentation": "<p>The likelihood of the model selecting higher-probability options while generating a response. A lower value makes the model more likely to choose higher-probability options, while a higher value makes the model more likely to choose lower-probability options.</p>"}, "topK": {"shape": "TopK", "documentation": "<p>While generating a response, the model determines the probability of the following token at each point of generation. The value that you set for <code>topK</code> is the number of most-likely candidates from which the model chooses the next token in the sequence. For example, if you set <code>topK</code> to 50, the model selects the next token from among the top 50 most likely choices.</p>"}, "topP": {"shape": "TopP", "documentation": "<p>While generating a response, the model determines the probability of the following token at each point of generation. The value that you set for <code>Top P</code> determines the number of most-likely candidates from which the model chooses the next token in the sequence. For example, if you set <code>topP</code> to 0.8, the model only selects the next token from the top 80% of the probability distribution of next tokens.</p>"}}, "documentation": "<p>Specifications about the inference parameters that were provided alongside the prompt. These are specified in the <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent_PromptOverrideConfiguration.html\">PromptOverrideConfiguration</a> object that was set when the agent was created or updated. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/model-parameters.html\">Inference parameters for foundation models</a>.</p>"}, "InlineAgentFilePart": {"type": "structure", "members": {"files": {"shape": "OutputFiles", "documentation": "<p>Files containing intermediate response for the user.</p>"}}, "documentation": "<p>Contains intermediate response for code interpreter if any files have been generated.</p>", "event": true}, "InlineAgentPayloadPart": {"type": "structure", "members": {"attribution": {"shape": "Attribution", "documentation": "<p>Contains citations for a part of an agent response.</p>"}, "bytes": {"shape": "PartBody", "documentation": "<p>A part of the agent response in bytes.</p>"}}, "documentation": "<p>Contains a part of an agent response and citations for it. </p>", "event": true, "sensitive": true}, "InlineAgentResponseStream": {"type": "structure", "members": {"accessDeniedException": {"shape": "AccessDeniedException", "documentation": "<p>The request is denied because of missing access permissions. Check your permissions and retry your request.</p>"}, "badGatewayException": {"shape": "BadGatewayException", "documentation": "<p>There was an issue with a dependency due to a server issue. Retry your request. </p>"}, "chunk": {"shape": "InlineAgentPayloadPart", "documentation": "<p>Contains a part of an agent response and citations for it.</p>"}, "conflictException": {"shape": "ConflictException", "documentation": "<p>There was a conflict performing an operation. Resolve the conflict and retry your request. </p>"}, "dependencyFailedException": {"shape": "DependencyFailedException", "documentation": "<p>There was an issue with a dependency. Check the resource configurations and retry the request.</p>"}, "files": {"shape": "InlineAgentFilePart", "documentation": "<p>Contains intermediate response for code interpreter if any files have been generated.</p>"}, "internalServerException": {"shape": "InternalServerException", "documentation": "<p>An internal server error occurred. Retry your request.</p>"}, "resourceNotFoundException": {"shape": "ResourceNotFoundException", "documentation": "<p>The specified resource Amazon Resource Name (ARN) was not found. Check the Amazon Resource Name (ARN) and try your request again. </p>"}, "returnControl": {"shape": "InlineAgentReturnControlPayload", "documentation": "<p>Contains the parameters and information that the agent elicited from the customer to carry out an action. This information is returned to the system and can be used in your own setup for fulfilling the action.</p>"}, "serviceQuotaExceededException": {"shape": "ServiceQuotaExceededException", "documentation": "<p>The number of requests exceeds the service quota. Resubmit your request later.</p>"}, "throttlingException": {"shape": "ThrottlingException", "documentation": "<p>The number of requests exceeds the limit. Resubmit your request later.</p>"}, "trace": {"shape": "InlineAgentTracePart", "documentation": "<p>Contains information about the agent and session, alongside the agent's reasoning process and results from calling actions and querying knowledge bases and metadata about the trace. You can use the trace to understand how the agent arrived at the response it provided the customer. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/trace-events.html\">Trace events</a>. </p>"}, "validationException": {"shape": "ValidationException", "documentation": "<p>Input validation failed. Check your request parameters and retry the request.</p>"}}, "documentation": "<p>The response from invoking the agent and associated citations and trace information.</p>", "eventstream": true}, "InlineAgentReturnControlPayload": {"type": "structure", "members": {"invocationId": {"shape": "String", "documentation": "<p>The identifier of the action group invocation. </p>"}, "invocationInputs": {"shape": "InvocationInputs", "documentation": "<p>A list of objects that contain information about the parameters and inputs that need to be sent into the API operation or function, based on what the agent determines from its session with the user.</p>"}}, "documentation": "<p>Contains information to return from the action group that the agent has predicted to invoke.</p> <p>This data type is used in the <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_InvokeAgent.html#API_agent-runtime_InvokeAgent_ResponseSyntax\">InvokeAgent response</a> API operation.</p>", "event": true, "sensitive": true}, "InlineAgentTracePart": {"type": "structure", "members": {"callerChain": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The caller chain for the trace part.</p>"}, "collaboratorName": {"shape": "Name", "documentation": "<p>The collaborator name for the trace part.</p>"}, "eventTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time that trace occurred. </p>"}, "sessionId": {"shape": "SessionId", "documentation": "<p>The unique identifier of the session with the agent.</p>"}, "trace": {"shape": "Trace", "documentation": "<p>Contains one part of the agent's reasoning process and results from calling API actions and querying knowledge bases. You can use the trace to understand how the agent arrived at the response it provided the customer. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/agents-test.html#trace-enablement\">Trace enablement</a>. </p>"}}, "documentation": "<p>Contains information about the agent and session, alongside the agent's reasoning process and results from calling API actions and querying knowledge bases and metadata about the trace. You can use the trace to understand how the agent arrived at the response it provided the customer. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/agents-test.html#trace-enablement\">Trace enablement</a>.</p>", "event": true, "sensitive": true}, "InlineBedrockModelConfigurations": {"type": "structure", "members": {"performanceConfig": {"shape": "PerformanceConfiguration", "documentation": "<p>The latency configuration for the model.</p>"}}, "documentation": "<p>Settings for a model called with <a>InvokeInlineAgent</a>.</p>"}, "InlineSessionState": {"type": "structure", "members": {"conversationHistory": {"shape": "ConversationHistory", "documentation": "<p> Contains the conversation history that persist across sessions. </p>"}, "files": {"shape": "InputFiles", "documentation": "<p> Contains information about the files used by code interpreter. </p>"}, "invocationId": {"shape": "String", "documentation": "<p> The identifier of the invocation of an action. This value must match the <code>invocationId</code> returned in the <code>InvokeInlineAgent</code> response for the action whose results are provided in the <code>returnControlInvocationResults</code> field. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/agents-returncontrol.html\">Return control to the agent developer</a>. </p>"}, "promptSessionAttributes": {"shape": "PromptSessionAttributesMap", "documentation": "<p> Contains attributes that persist across a session and the values of those attributes. </p>"}, "returnControlInvocationResults": {"shape": "ReturnControlInvocationResults", "documentation": "<p> Contains information about the results from the action group invocation. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/agents-returncontrol.html\">Return control to the agent developer</a>. </p> <note> <p>If you include this field in the <code>sessionState</code> field, the <code>inputText</code> field will be ignored.</p> </note>"}, "sessionAttributes": {"shape": "SessionAttributesMap", "documentation": "<p> Contains attributes that persist across a session and the values of those attributes. </p>"}}, "documentation": "<p> Contains parameters that specify various attributes that persist across a session or prompt. You can define session state attributes as key-value pairs when writing a <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/agents-lambda.html\">Lambda function</a> for an action group or pass them when making an <code>InvokeInlineAgent</code> request. Use session state attributes to control and provide conversational context for your inline agent and to help customize your agent's behavior. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/agents-session-state.html\">Control session context</a> </p>"}, "InputFile": {"type": "structure", "required": ["name", "source", "useCase"], "members": {"name": {"shape": "String", "documentation": "<p>The name of the source file.</p>"}, "source": {"shape": "FileSource", "documentation": "<p>Specifies where the files are located.</p>"}, "useCase": {"shape": "FileUseCase", "documentation": "<p>Specifies how the source files will be used by the code interpreter.</p>"}}, "documentation": "<p>Contains details of the source files.</p>"}, "InputFiles": {"type": "list", "member": {"shape": "InputFile"}}, "InputPrompt": {"type": "structure", "members": {"textPrompt": {"shape": "TextPrompt", "documentation": "<p>Contains information about the text prompt to optimize.</p>"}}, "documentation": "<p>Contains information about the prompt to optimize.</p>", "union": true}, "InputQueryType": {"type": "string", "enum": ["TEXT"]}, "InputText": {"type": "string", "max": 25000000, "min": 0, "sensitive": true}, "Instruction": {"type": "string", "min": 40, "sensitive": true}, "Integer": {"type": "integer", "box": true}, "InternalServerException": {"type": "structure", "members": {"message": {"shape": "NonBlankString"}, "reason": {"shape": "String", "documentation": "<p>The reason for the exception. If the reason is <code>BEDROCK_MODEL_INVOCATION_SERVICE_UNAVAILABLE</code>, the model invocation service is unavailable. Retry your request.</p>"}}, "documentation": "<p>An internal server error occurred. Retry your request.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true}, "InvocationDescription": {"type": "string", "max": 200, "min": 1}, "InvocationIdentifier": {"type": "string", "pattern": "^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$"}, "InvocationInput": {"type": "structure", "members": {"actionGroupInvocationInput": {"shape": "ActionGroupInvocationInput", "documentation": "<p>Contains information about the action group to be invoked.</p>"}, "agentCollaboratorInvocationInput": {"shape": "AgentCollaboratorInvocationInput", "documentation": "<p>The collaborator's invocation input.</p>"}, "codeInterpreterInvocationInput": {"shape": "CodeInterpreterInvocationInput", "documentation": "<p>Contains information about the code interpreter to be invoked.</p>"}, "invocationType": {"shape": "InvocationType", "documentation": "<p>Specifies whether the agent is invoking an action group or a knowledge base.</p>"}, "knowledgeBaseLookupInput": {"shape": "KnowledgeBaseLookupInput", "documentation": "<p>Contains details about the knowledge base to look up and the query to be made.</p>"}, "traceId": {"shape": "TraceId", "documentation": "<p>The unique identifier of the trace.</p>"}}, "documentation": "<p>Contains information pertaining to the action group or knowledge base that is being invoked.</p>", "sensitive": true}, "InvocationInputMember": {"type": "structure", "members": {"apiInvocationInput": {"shape": "ApiInvocationInput", "documentation": "<p>Contains information about the API operation that the agent predicts should be called.</p>"}, "functionInvocationInput": {"shape": "FunctionInvocationInput", "documentation": "<p>Contains information about the function that the agent predicts should be called.</p>"}}, "documentation": "<p>Contains details about the API operation or function that the agent predicts should be called. </p> <p>This data type is used in the following API operations:</p> <ul> <li> <p>In the <code>returnControl</code> field of the <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_InvokeAgent.html#API_agent-runtime_InvokeAgent_ResponseSyntax\">InvokeAgent response</a> </p> </li> </ul>", "union": true}, "InvocationInputs": {"type": "list", "member": {"shape": "InvocationInputMember"}, "max": 5, "min": 1}, "InvocationResultMember": {"type": "structure", "members": {"apiResult": {"shape": "ApiResult", "documentation": "<p>The result from the API response from the action group invocation.</p>"}, "functionResult": {"shape": "FunctionResult", "documentation": "<p>The result from the function from the action group invocation.</p>"}}, "documentation": "<p>A result from the invocation of an action. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/agents-returncontrol.html\">Return control to the agent developer</a> and <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/agents-session-state.html\">Control session context</a>.</p> <p>This data type is used in the following API operations:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_InvokeAgent.html#API_agent-runtime_InvokeAgent_RequestSyntax\">InvokeAgent request</a> </p> </li> </ul>", "union": true}, "InvocationStep": {"type": "structure", "required": ["invocationId", "invocationStepId", "invocationStepTime", "payload", "sessionId"], "members": {"invocationId": {"shape": "<PERSON><PERSON>", "documentation": "<p>The unique identifier (in UUID format) for the invocation that includes the invocation step.</p>"}, "invocationStepId": {"shape": "<PERSON><PERSON>", "documentation": "<p>The unique identifier (in UUID format) for the invocation step.</p>"}, "invocationStepTime": {"shape": "DateTimestamp", "documentation": "<p>The timestamp for when the invocation step was created.</p>"}, "payload": {"shape": "InvocationStepPayload", "documentation": "<p>Payload content, such as text and images, for the invocation step.</p>"}, "sessionId": {"shape": "<PERSON><PERSON>", "documentation": "<p>The unique identifier of the session containing the invocation step.</p>"}}, "documentation": "<p>Stores fine-grained state checkpoints, including text and images, for each interaction in an invocation in a session. For more information about sessions, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/sessions.html\">Store and retrieve conversation history and context with Amazon Bedrock sessions</a>. </p>"}, "InvocationStepPayload": {"type": "structure", "members": {"contentBlocks": {"shape": "BedrockSessionContentBlocks", "documentation": "<p>The content for the invocation step.</p>"}}, "documentation": "<p>Payload content, such as text and images, for the invocation step.</p>", "union": true}, "InvocationStepSummaries": {"type": "list", "member": {"shape": "InvocationStepSummary"}}, "InvocationStepSummary": {"type": "structure", "required": ["invocationId", "invocationStepId", "invocationStepTime", "sessionId"], "members": {"invocationId": {"shape": "<PERSON><PERSON>", "documentation": "<p>A unique identifier for the invocation in UUID format.</p>"}, "invocationStepId": {"shape": "<PERSON><PERSON>", "documentation": "<p>The unique identifier (in UUID format) for the invocation step.</p>"}, "invocationStepTime": {"shape": "DateTimestamp", "documentation": "<p>The timestamp for when the invocation step was created.</p>"}, "sessionId": {"shape": "<PERSON><PERSON>", "documentation": "<p>The unique identifier for the session associated with the invocation step.</p>"}}, "documentation": "<p>Contains details about an invocation step within an invocation in a session. For more information about sessions, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/sessions.html\">Store and retrieve conversation history and context with Amazon Bedrock sessions</a>.</p>"}, "InvocationSummaries": {"type": "list", "member": {"shape": "Invocation<PERSON>um<PERSON>y"}}, "InvocationSummary": {"type": "structure", "required": ["createdAt", "invocationId", "sessionId"], "members": {"createdAt": {"shape": "DateTimestamp", "documentation": "<p>The timestamp for when the invocation was created.</p>"}, "invocationId": {"shape": "<PERSON><PERSON>", "documentation": "<p>A unique identifier for the invocation in UUID format.</p>"}, "sessionId": {"shape": "<PERSON><PERSON>", "documentation": "<p>The unique identifier for the session associated with the invocation.</p>"}}, "documentation": "<p>Contains details about an invocation in a session. For more information about sessions, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/sessions.html\">Store and retrieve conversation history and context with Amazon Bedrock sessions</a>.</p>"}, "InvocationType": {"type": "string", "enum": ["ACTION_GROUP", "KNOWLEDGE_BASE", "FINISH", "ACTION_GROUP_CODE_INTERPRETER", "AGENT_COLLABORATOR"]}, "InvokeAgentRequest": {"type": "structure", "required": ["agentAliasId", "agentId", "sessionId"], "members": {"agentAliasId": {"shape": "AgentAliasId", "documentation": "<p>The alias of the agent to use.</p>", "location": "uri", "locationName": "agentAliasId"}, "agentId": {"shape": "AgentId", "documentation": "<p>The unique identifier of the agent to use.</p>", "location": "uri", "locationName": "agentId"}, "bedrockModelConfigurations": {"shape": "BedrockModelConfigurations", "documentation": "<p>Model performance settings for the request.</p>"}, "enableTrace": {"shape": "Boolean", "documentation": "<p>Specifies whether to turn on the trace or not to track the agent's reasoning process. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/agents-test.html#trace-events\">Trace enablement</a>.</p>"}, "endSession": {"shape": "Boolean", "documentation": "<p>Specifies whether to end the session with the agent or not.</p>"}, "inputText": {"shape": "InputText", "documentation": "<p>The prompt text to send the agent.</p> <note> <p>If you include <code>returnControlInvocationResults</code> in the <code>sessionState</code> field, the <code>inputText</code> field will be ignored.</p> </note>"}, "memoryId": {"shape": "MemoryId", "documentation": "<p>The unique identifier of the agent memory.</p>"}, "promptCreationConfigurations": {"shape": "PromptCreationConfigurations", "documentation": "<p>Specifies parameters that control how the service populates the agent prompt for an <code>InvokeAgent</code> request. You can control which aspects of previous invocations in the same agent session the service uses to populate the agent prompt. This gives you more granular control over the contextual history that is used to process the current request.</p>"}, "sessionId": {"shape": "SessionId", "documentation": "<p>The unique identifier of the session. Use the same value across requests to continue the same conversation.</p>", "location": "uri", "locationName": "sessionId"}, "sessionState": {"shape": "SessionState", "documentation": "<p>Contains parameters that specify various attributes of the session. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/agents-session-state.html\">Control session context</a>.</p> <note> <p>If you include <code>returnControlInvocationResults</code> in the <code>sessionState</code> field, the <code>inputText</code> field will be ignored.</p> </note>"}, "sourceArn": {"shape": "AWSResourceARN", "documentation": "<p>The ARN of the resource making the request.</p>", "location": "header", "locationName": "x-amz-source-arn"}, "streamingConfigurations": {"shape": "StreamingConfigurations", "documentation": "<p> Specifies the configurations for streaming. </p> <note> <p>To use agent streaming, you need permissions to perform the <code>bedrock:InvokeModelWithResponseStream</code> action.</p> </note>"}}}, "InvokeAgentResponse": {"type": "structure", "required": ["completion", "contentType", "sessionId"], "members": {"completion": {"shape": "ResponseStream", "documentation": "<p>The agent's response to the user prompt.</p>"}, "contentType": {"shape": "MimeType", "documentation": "<p>The MIME type of the input data in the request. The default value is <code>application/json</code>.</p>", "location": "header", "locationName": "x-amzn-bedrock-agent-content-type"}, "memoryId": {"shape": "MemoryId", "documentation": "<p>The unique identifier of the agent memory.</p>", "location": "header", "locationName": "x-amz-bedrock-agent-memory-id"}, "sessionId": {"shape": "SessionId", "documentation": "<p>The unique identifier of the session with the agent.</p>", "location": "header", "locationName": "x-amz-bedrock-agent-session-id"}}, "payload": "completion"}, "InvokeFlowRequest": {"type": "structure", "required": ["flowAliasIdentifier", "flowIdentifier", "inputs"], "members": {"enableTrace": {"shape": "Boolean", "documentation": "<p>Specifies whether to return the trace for the flow or not. Traces track inputs and outputs for nodes in the flow. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/flows-trace.html\">Track each step in your prompt flow by viewing its trace in Amazon Bedrock</a>.</p>"}, "executionId": {"shape": "FlowExecutionId", "documentation": "<p>The unique identifier for the current flow execution. If you don't provide a value, Amazon Bedrock creates the identifier for you. </p>"}, "flowAliasIdentifier": {"shape": "FlowAliasIdentifier", "documentation": "<p>The unique identifier of the flow alias.</p>", "location": "uri", "locationName": "flowAliasIdentifier"}, "flowIdentifier": {"shape": "FlowIdentifier", "documentation": "<p>The unique identifier of the flow.</p>", "location": "uri", "locationName": "flowIdentifier"}, "inputs": {"shape": "FlowInputs", "documentation": "<p>A list of objects, each containing information about an input into the flow.</p>"}, "modelPerformanceConfiguration": {"shape": "ModelPerformanceConfiguration", "documentation": "<p>Model performance settings for the request.</p>"}}}, "InvokeFlowResponse": {"type": "structure", "required": ["responseStream"], "members": {"executionId": {"shape": "FlowExecutionId", "documentation": "<p>The unique identifier for the current flow execution.</p>", "location": "header", "locationName": "x-amz-bedrock-flow-execution-id"}, "responseStream": {"shape": "FlowResponseStream", "documentation": "<p>The output of the flow, returned as a stream. If there's an error, the error is returned.</p>"}}, "payload": "responseStream"}, "InvokeInlineAgentRequest": {"type": "structure", "required": ["foundationModel", "instruction", "sessionId"], "members": {"actionGroups": {"shape": "AgentActionGroups", "documentation": "<p> A list of action groups with each action group defining the action the inline agent needs to carry out. </p>"}, "agentCollaboration": {"shape": "AgentCollaboration", "documentation": "<p> Defines how the inline collaborator agent handles information across multiple collaborator agents to coordinate a final response. The inline collaborator agent can also be the supervisor. </p>"}, "agentName": {"shape": "Name", "documentation": "<p>The name for the agent.</p>"}, "bedrockModelConfigurations": {"shape": "InlineBedrockModelConfigurations", "documentation": "<p>Model settings for the request.</p>"}, "collaboratorConfigurations": {"shape": "CollaboratorConfigurations", "documentation": "<p> Settings for an inline agent collaborator called with <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_InvokeInlineAgent.html\">InvokeInlineAgent</a>. </p>"}, "collaborators": {"shape": "Collaborators", "documentation": "<p> List of collaborator inline agents. </p>"}, "customOrchestration": {"shape": "CustomOrchestration", "documentation": "<p>Contains details of the custom orchestration configured for the agent. </p>"}, "customerEncryptionKeyArn": {"shape": "KmsKeyArn", "documentation": "<p> The Amazon Resource Name (ARN) of the Amazon Web Services KMS key to use to encrypt your inline agent. </p>"}, "enableTrace": {"shape": "Boolean", "documentation": "<p> Specifies whether to turn on the trace or not to track the agent's reasoning process. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/trace-events.html\">Using trace</a>. </p>"}, "endSession": {"shape": "Boolean", "documentation": "<p> Specifies whether to end the session with the inline agent or not. </p>"}, "foundationModel": {"shape": "ModelIdentifier", "documentation": "<p> The <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/model-ids.html#model-ids-arns\">model identifier (ID)</a> of the model to use for orchestration by the inline agent. For example, <code>meta.llama3-1-70b-instruct-v1:0</code>. </p>"}, "guardrailConfiguration": {"shape": "GuardrailConfigurationWithArn", "documentation": "<p> The <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/guardrails.html\">guardrails</a> to assign to the inline agent. </p>"}, "idleSessionTTLInSeconds": {"shape": "SessionTTL", "documentation": "<p> The number of seconds for which the inline agent should maintain session information. After this time expires, the subsequent <code>InvokeInlineAgent</code> request begins a new session. </p> <p>A user interaction remains active for the amount of time specified. If no conversation occurs during this time, the session expires and the data provided before the timeout is deleted.</p>"}, "inlineSessionState": {"shape": "InlineSessionState", "documentation": "<p> Parameters that specify the various attributes of a sessions. You can include attributes for the session or prompt or, if you configured an action group to return control, results from invocation of the action group. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/agents-session-state.html\">Control session context</a>. </p> <note> <p>If you include <code>returnControlInvocationResults</code> in the <code>sessionState</code> field, the <code>inputText</code> field will be ignored.</p> </note>"}, "inputText": {"shape": "InputText", "documentation": "<p> The prompt text to send to the agent. </p> <note> <p>If you include <code>returnControlInvocationResults</code> in the <code>sessionState</code> field, the <code>inputText</code> field will be ignored.</p> </note>"}, "instruction": {"shape": "Instruction", "documentation": "<p> The instructions that tell the inline agent what it should do and how it should interact with users. </p>"}, "knowledgeBases": {"shape": "KnowledgeBases", "documentation": "<p> Contains information of the knowledge bases to associate with. </p>"}, "orchestrationType": {"shape": "OrchestrationType", "documentation": "<p>Specifies the type of orchestration strategy for the agent. This is set to DEFAULT orchestration type, by default. </p>"}, "promptCreationConfigurations": {"shape": "PromptCreationConfigurations", "documentation": "<p>Specifies parameters that control how the service populates the agent prompt for an <code>InvokeInlineAgent</code> request. You can control which aspects of previous invocations in the same agent session the service uses to populate the agent prompt. This gives you more granular control over the contextual history that is used to process the current request.</p>"}, "promptOverrideConfiguration": {"shape": "PromptOverrideConfiguration", "documentation": "<p> Configurations for advanced prompts used to override the default prompts to enhance the accuracy of the inline agent. </p>"}, "sessionId": {"shape": "SessionId", "documentation": "<p> The unique identifier of the session. Use the same value across requests to continue the same conversation. </p>", "location": "uri", "locationName": "sessionId"}, "streamingConfigurations": {"shape": "StreamingConfigurations", "documentation": "<p> Specifies the configurations for streaming. </p> <note> <p>To use agent streaming, you need permissions to perform the <code>bedrock:InvokeModelWithResponseStream</code> action.</p> </note>"}}}, "InvokeInlineAgentResponse": {"type": "structure", "required": ["completion", "contentType", "sessionId"], "members": {"completion": {"shape": "InlineAgentResponseStream", "documentation": "<p>The inline agent's response to the user prompt. </p>"}, "contentType": {"shape": "MimeType", "documentation": "<p> The MIME type of the input data in the request. The default value is application/json. </p>", "location": "header", "locationName": "x-amzn-bedrock-agent-content-type"}, "sessionId": {"shape": "SessionId", "documentation": "<p> The unique identifier of the session with the agent. </p>", "location": "header", "locationName": "x-amz-bedrock-agent-session-id"}}, "payload": "completion"}, "KmsKeyArn": {"type": "string", "max": 2048, "min": 1, "pattern": "^arn:aws(|-cn|-us-gov):kms:[a-zA-Z0-9-]*:[0-9]{12}:key/[a-zA-Z0-9-]{36}$"}, "KnowledgeBase": {"type": "structure", "required": ["description", "knowledgeBaseId"], "members": {"description": {"shape": "ResourceDescription", "documentation": "<p> The description of the knowledge base associated with the inline agent. </p>"}, "knowledgeBaseId": {"shape": "KnowledgeBaseId", "documentation": "<p> The unique identifier for a knowledge base associated with the inline agent. </p>"}, "retrievalConfiguration": {"shape": "KnowledgeBaseRetrievalConfiguration", "documentation": "<p> The configurations to apply to the knowledge base during query. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/kb-test-config.html\">Query configurations</a>. </p>"}}, "documentation": "<p> Details of the knowledge base associated withe inline agent. </p>"}, "KnowledgeBaseArn": {"type": "string", "max": 128, "min": 0, "pattern": "^arn:aws(|-cn|-us-gov):bedrock:[a-zA-Z0-9-]*:[0-9]{12}:knowledge-base/[0-9a-zA-Z]+$"}, "KnowledgeBaseConfiguration": {"type": "structure", "required": ["knowledgeBaseId", "retrievalConfiguration"], "members": {"knowledgeBaseId": {"shape": "KnowledgeBaseId", "documentation": "<p>The unique identifier for a knowledge base attached to the agent.</p>"}, "retrievalConfiguration": {"shape": "KnowledgeBaseRetrievalConfiguration", "documentation": "<p>The configurations to apply to the knowledge base during query. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/kb-test-config.html\">Query configurations</a>.</p>"}}, "documentation": "<p>Configurations to apply to a knowledge base attached to the agent during query. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/agents-session-state.html#session-state-kb\">Knowledge base retrieval configurations</a>.</p>"}, "KnowledgeBaseConfigurations": {"type": "list", "member": {"shape": "KnowledgeBaseConfiguration"}, "min": 1}, "KnowledgeBaseId": {"type": "string", "max": 10, "min": 0, "pattern": "^[0-9a-zA-Z]+$"}, "KnowledgeBaseLookupInput": {"type": "structure", "members": {"knowledgeBaseId": {"shape": "TraceKnowledgeBaseId", "documentation": "<p>The unique identifier of the knowledge base to look up.</p>"}, "text": {"shape": "KnowledgeBaseLookupInputString", "documentation": "<p>The query made to the knowledge base.</p>"}}, "documentation": "<p>Contains details about the knowledge base to look up and the query to be made.</p>"}, "KnowledgeBaseLookupInputString": {"type": "string", "sensitive": true}, "KnowledgeBaseLookupOutput": {"type": "structure", "members": {"metadata": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>Contains information about the knowledge base output.</p>"}, "retrievedReferences": {"shape": "RetrievedReferences", "documentation": "<p>Contains metadata about the sources cited for the generated response.</p>"}}, "documentation": "<p>Contains details about the results from looking up the knowledge base.</p>"}, "KnowledgeBaseQuery": {"type": "structure", "required": ["text"], "members": {"text": {"shape": "KnowledgeBaseQueryTextString", "documentation": "<p>The text of the query made to the knowledge base.</p>"}}, "documentation": "<p>Contains the query made to the knowledge base.</p> <p>This data type is used in the following API operations:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_Retrieve.html#API_agent-runtime_Retrieve_RequestSyntax\">Retrieve request</a> – in the <code>retrievalQuery</code> field</p> </li> </ul>", "sensitive": true}, "KnowledgeBaseQueryTextString": {"type": "string", "max": 1000, "min": 0}, "KnowledgeBaseRetrievalConfiguration": {"type": "structure", "required": ["vectorSearchConfiguration"], "members": {"vectorSearchConfiguration": {"shape": "KnowledgeBaseVectorSearchConfiguration", "documentation": "<p>Contains details about how the results from the vector search should be returned. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/kb-test-config.html\">Query configurations</a>.</p>"}}, "documentation": "<p>Contains configurations for knowledge base query. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/kb-test-config.html\">Query configurations</a>.</p> <p>This data type is used in the following API operations:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_Retrieve.html#API_agent-runtime_Retrieve_RequestSyntax\">Retrieve request</a> – in the <code>retrievalConfiguration</code> field</p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_RetrieveAndGenerate.html#API_agent-runtime_RetrieveAndGenerate_RequestSyntax\">RetrieveAndGenerate request</a> – in the <code>retrievalConfiguration</code> field</p> </li> </ul>"}, "KnowledgeBaseRetrievalResult": {"type": "structure", "required": ["content"], "members": {"content": {"shape": "RetrievalR<PERSON>ultContent", "documentation": "<p>Contains information about the content of the chunk.</p>"}, "location": {"shape": "RetrievalResultLocation", "documentation": "<p>Contains information about the location of the data source.</p>"}, "metadata": {"shape": "RetrievalResultMetadata", "documentation": "<p>Contains metadata attributes and their values for the file in the data source. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/knowledge-base-ds.html#kb-ds-metadata\">Metadata and filtering</a>.</p>"}, "score": {"shape": "Double", "documentation": "<p>The level of relevance of the result to the query.</p>"}}, "documentation": "<p>Details about a result from querying the knowledge base.</p> <p>This data type is used in the following API operations:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_Retrieve.html#API_agent-runtime_Retrieve_ResponseSyntax\">Retrieve response</a> – in the <code>retrievalResults</code> field</p> </li> </ul>"}, "KnowledgeBaseRetrievalResults": {"type": "list", "member": {"shape": "KnowledgeBaseRetrievalResult"}, "sensitive": true}, "KnowledgeBaseRetrieveAndGenerateConfiguration": {"type": "structure", "required": ["knowledgeBaseId", "modelArn"], "members": {"generationConfiguration": {"shape": "GenerationConfiguration", "documentation": "<p>Contains configurations for response generation based on the knowledge base query results.</p>"}, "knowledgeBaseId": {"shape": "KnowledgeBaseId", "documentation": "<p>The unique identifier of the knowledge base that is queried.</p>"}, "modelArn": {"shape": "BedrockModelArn", "documentation": "<p>The ARN of the foundation model or <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/cross-region-inference.html\">inference profile</a> used to generate a response.</p>"}, "orchestrationConfiguration": {"shape": "OrchestrationConfiguration", "documentation": "<p>Settings for how the model processes the prompt prior to retrieval and generation.</p>"}, "retrievalConfiguration": {"shape": "KnowledgeBaseRetrievalConfiguration", "documentation": "<p>Contains configurations for how to retrieve and return the knowledge base query.</p>"}}, "documentation": "<p>Contains details about the resource being queried.</p> <p>This data type is used in the following API operations:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_Retrieve.html#API_agent-runtime_Retrieve_RequestSyntax\">Retrieve request</a> – in the <code>knowledgeBaseConfiguration</code> field</p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_RetrieveAndGenerate.html#API_agent-runtime_RetrieveAndGenerate_RequestSyntax\">RetrieveAndGenerate request</a> – in the <code>knowledgeBaseConfiguration</code> field</p> </li> </ul>"}, "KnowledgeBaseVectorSearchConfiguration": {"type": "structure", "members": {"filter": {"shape": "Retrieval<PERSON><PERSON>er", "documentation": "<p>Specifies the filters to use on the metadata in the knowledge base data sources before returning results. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/kb-test-config.html\">Query configurations</a>.</p>"}, "implicitFilterConfiguration": {"shape": "ImplicitFilterConfiguration", "documentation": "<p>Settings for implicit filtering.</p>"}, "numberOfResults": {"shape": "KnowledgeBaseVectorSearchConfigurationNumberOfResultsInteger", "documentation": "<p>The number of source chunks to retrieve.</p>", "box": true}, "overrideSearchType": {"shape": "SearchType", "documentation": "<p>By default, Amazon Bedrock decides a search strategy for you. If you're using an Amazon OpenSearch Serverless vector store that contains a filterable text field, you can specify whether to query the knowledge base with a <code>HYBRID</code> search using both vector embeddings and raw text, or <code>SEMANTIC</code> search using only vector embeddings. For other vector store configurations, only <code>SEMANTIC</code> search is available. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/knowledge-base-test.html\">Test a knowledge base</a>.</p>"}, "rerankingConfiguration": {"shape": "VectorSearchRerankingConfiguration", "documentation": "<p>Contains configurations for reranking the retrieved results. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/rerank.html\">Improve the relevance of query responses with a reranker model</a>.</p>"}}, "documentation": "<p>Configurations for how to perform the search query and return results. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/kb-test-config.html\">Query configurations</a>.</p> <p>This data type is used in the following API operations:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_Retrieve.html#API_agent-runtime_Retrieve_RequestSyntax\">Retrieve request</a> – in the <code>vectorSearchConfiguration</code> field</p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_RetrieveAndGenerate.html#API_agent-runtime_RetrieveAndGenerate_RequestSyntax\">RetrieveAndGenerate request</a> – in the <code>vectorSearchConfiguration</code> field</p> </li> </ul>"}, "KnowledgeBaseVectorSearchConfigurationNumberOfResultsInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "KnowledgeBases": {"type": "list", "member": {"shape": "KnowledgeBase"}}, "LambdaArn": {"type": "string"}, "LambdaResourceArn": {"type": "string", "max": 2048, "min": 0, "pattern": "^arn:(aws[a-zA-Z-]*)?:lambda:[a-z]{2}(-gov)?-[a-z]+-\\d{1}:\\d{12}:function:[a-zA-Z0-9-_\\.]+(:(\\$LATEST|[a-zA-Z0-9-_]+))?$"}, "ListFlowExecutionEventsRequest": {"type": "structure", "required": ["eventType", "executionIdentifier", "flowAliasIdentifier", "flowIdentifier"], "members": {"eventType": {"shape": "FlowExecutionEventType", "documentation": "<p>The type of events to retrieve. Specify <code>Node</code> for node-level events or <code>Flow</code> for flow-level events.</p>", "location": "querystring", "locationName": "eventType"}, "executionIdentifier": {"shape": "FlowExecutionIdentifier", "documentation": "<p>The unique identifier of the flow execution.</p>", "location": "uri", "locationName": "executionIdentifier"}, "flowAliasIdentifier": {"shape": "FlowAliasIdentifier", "documentation": "<p>The unique identifier of the flow alias used for the execution.</p>", "location": "uri", "locationName": "flowAliasIdentifier"}, "flowIdentifier": {"shape": "FlowIdentifier", "documentation": "<p>The unique identifier of the flow.</p>", "location": "uri", "locationName": "flowIdentifier"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of events to return in a single response. If more events exist than the specified maxResults value, a token is included in the response so that the remaining results can be retrieved.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token to retrieve the next set of results. This value is returned in the response if more results are available.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListFlowExecutionEventsResponse": {"type": "structure", "required": ["flowExecutionEvents"], "members": {"flowExecutionEvents": {"shape": "FlowExecutionEvents", "documentation": "<p>A list of events that occurred during the flow execution. Events can include node inputs and outputs, flow inputs and outputs, condition results, and failure events.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token to retrieve the next set of results. This value is returned if more results are available.</p>"}}}, "ListFlowExecutionsRequest": {"type": "structure", "required": ["flowIdentifier"], "members": {"flowAliasIdentifier": {"shape": "FlowAliasIdentifier", "documentation": "<p>The unique identifier of the flow alias to list executions for.</p>", "location": "querystring", "locationName": "flowAliasIdentifier"}, "flowIdentifier": {"shape": "FlowIdentifier", "documentation": "<p>The unique identifier of the flow to list executions for.</p>", "location": "uri", "locationName": "flowIdentifier"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of flow executions to return in a single response. If more executions exist than the specified <code>maxResults</code> value, a token is included in the response so that the remaining results can be retrieved.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token to retrieve the next set of results. This value is returned in the response if more results are available.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListFlowExecutionsResponse": {"type": "structure", "required": ["flowExecutionSummaries"], "members": {"flowExecutionSummaries": {"shape": "FlowExecutionSummaries", "documentation": "<p>A list of flow execution summaries. Each summary includes the execution ARN, flow identifier, flow alias identifier, flow version, status, and timestamps.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token to retrieve the next set of results. This value is returned if more results are available.</p>"}}}, "ListInvocationStepsRequest": {"type": "structure", "required": ["sessionIdentifier"], "members": {"invocationIdentifier": {"shape": "InvocationIdentifier", "documentation": "<p>The unique identifier (in UUID format) for the invocation to list invocation steps for.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in the response. If the total number of results is greater than this value, use the token returned in the response in the <code>nextToken</code> field when making another request to return the next batch of results.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the total number of results is greater than the <code>maxResults</code> value provided in the request, enter the token returned in the <code>nextToken</code> field in the response in this field to return the next batch of results. </p>", "location": "querystring", "locationName": "nextToken"}, "sessionIdentifier": {"shape": "SessionIdentifier", "documentation": "<p>The unique identifier for the session associated with the invocation steps. You can specify either the session's <code>sessionId</code> or its Amazon Resource Name (ARN).</p>", "location": "uri", "locationName": "sessionIdentifier"}}}, "ListInvocationStepsResponse": {"type": "structure", "required": ["invocationStepSummaries"], "members": {"invocationStepSummaries": {"shape": "InvocationStepSummaries", "documentation": "<p>A list of summaries for each invocation step associated with a session and if you specified it, an invocation within the session.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the total number of results is greater than the <code>maxResults</code> value provided in the request, use this token when making another request in the <code>nextToken</code> field to return the next batch of results.</p>"}}}, "ListInvocationsRequest": {"type": "structure", "required": ["sessionIdentifier"], "members": {"maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in the response. If the total number of results is greater than this value, use the token returned in the response in the <code>nextToken</code> field when making another request to return the next batch of results.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the total number of results is greater than the <code>maxResults</code> value provided in the request, enter the token returned in the <code>nextToken</code> field in the response in this field to return the next batch of results. </p>", "location": "querystring", "locationName": "nextToken"}, "sessionIdentifier": {"shape": "SessionIdentifier", "documentation": "<p>The unique identifier for the session to list invocations for. You can specify either the session's <code>sessionId</code> or its Amazon Resource Name (ARN).</p>", "location": "uri", "locationName": "sessionIdentifier"}}}, "ListInvocationsResponse": {"type": "structure", "required": ["invocationSummaries"], "members": {"invocationSummaries": {"shape": "InvocationSummaries", "documentation": "<p>A list of invocation summaries associated with the session.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the total number of results is greater than the <code>maxResults</code> value provided in the request, use this token when making another request in the <code>nextToken</code> field to return the next batch of results.</p>"}}}, "ListSessionsRequest": {"type": "structure", "members": {"maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in the response. If the total number of results is greater than this value, use the token returned in the response in the <code>nextToken</code> field when making another request to return the next batch of results.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If the total number of results is greater than the <code>maxResults</code> value provided in the request, enter the token returned in the <code>nextToken</code> field in the response in this field to return the next batch of results. </p>", "location": "querystring", "locationName": "nextToken"}}}, "ListSessionsResponse": {"type": "structure", "required": ["sessionSummaries"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>If the total number of results is greater than the <code>maxResults</code> value provided in the request, use this token when making another request in the <code>nextToken</code> field to return the next batch of results.</p>"}, "sessionSummaries": {"shape": "SessionSummaries", "documentation": "<p>A list of summaries for each session in your Amazon Web Services account.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "TaggableResourcesArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource for which to list tags.</p>", "location": "uri", "locationName": "resourceArn"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"tags": {"shape": "TagsMap", "documentation": "<p>The key-value pairs for the tags associated with the resource.</p>"}}}, "Long": {"type": "long", "box": true}, "MaxResults": {"type": "integer", "documentation": "<p>Max Results.</p>", "box": true, "max": 1000, "min": 1}, "MaxTokens": {"type": "integer", "box": true, "max": 65536, "min": 0}, "MaximumLength": {"type": "integer", "box": true, "max": 8192, "min": 0}, "Memories": {"type": "list", "member": {"shape": "Memory"}}, "Memory": {"type": "structure", "members": {"sessionSummary": {"shape": "MemorySessionSummary", "documentation": "<p>Contains summary of a session.</p>"}}, "documentation": "<p>Contains sessions summaries.</p>", "union": true}, "MemoryId": {"type": "string", "max": 100, "min": 2, "pattern": "^[0-9a-zA-Z._:-]+$"}, "MemorySessionSummary": {"type": "structure", "members": {"memoryId": {"shape": "MemoryId", "documentation": "<p>The unique identifier of the memory where the session summary is stored.</p>"}, "sessionExpiryTime": {"shape": "DateTimestamp", "documentation": "<p>The time when the memory duration for the session is set to end.</p>"}, "sessionId": {"shape": "SessionId", "documentation": "<p>The identifier for this session.</p>"}, "sessionStartTime": {"shape": "DateTimestamp", "documentation": "<p>The start time for this session.</p>"}, "summaryText": {"shape": "SummaryText", "documentation": "<p>The summarized text for this session.</p>"}}, "documentation": "<p>Contains details of a session summary.</p>"}, "MemoryType": {"type": "string", "enum": ["SESSION_SUMMARY"]}, "Message": {"type": "structure", "required": ["content", "role"], "members": {"content": {"shape": "ContentBlocks", "documentation": "<p>The message's content.</p>"}, "role": {"shape": "ConversationRole", "documentation": "<p>The message's role.</p>"}}, "documentation": "<p>Details about a message.</p>"}, "Messages": {"type": "list", "member": {"shape": "Message"}}, "Metadata": {"type": "structure", "members": {"clientRequestId": {"shape": "String", "documentation": "<p>A unique identifier associated with the downstream invocation. This ID can be used for tracing, debugging, and identifying specific invocations in customer logs or systems.</p>"}, "endTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>In the final response, <code>endTime</code> is the end time of the agent invocation operation.</p>"}, "operationTotalTimeMs": {"shape": "<PERSON>", "documentation": "<p>The total time it took for the agent to complete execution. This field is only set for the final response.</p>"}, "startTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>In the final response, <code>startTime</code> is the start time of the agent invocation operation.</p>"}, "totalTimeMs": {"shape": "<PERSON>", "documentation": "<p> The total execution time for the specific invocation being processed (model, knowledge base, guardrail, agent collaborator, or code interpreter). It represents how long the individual invocation took.</p>"}, "usage": {"shape": "Usage", "documentation": "<p>Specific to model invocation and contains details about the usage of a foundation model.</p>"}}, "documentation": "<p>Provides information about the execution process for different types of invocations, such as model invocation, knowledge base invocation, agent collaborator invocation, guardrail invocation, and code interpreter Invocation.</p>", "sensitive": true}, "MetadataAttributeSchema": {"type": "structure", "required": ["description", "key", "type"], "members": {"description": {"shape": "MetadataAttributeSchemaDescriptionString", "documentation": "<p>The attribute's description.</p>"}, "key": {"shape": "MetadataAttributeSchemaKeyString", "documentation": "<p>The attribute's key.</p>"}, "type": {"shape": "AttributeType", "documentation": "<p>The attribute's type.</p>"}}, "documentation": "<p>Details about a metadata attribute.</p>", "sensitive": true}, "MetadataAttributeSchemaDescriptionString": {"type": "string", "max": 1024, "min": 1, "pattern": "^[\\s\\S]+$"}, "MetadataAttributeSchemaKeyString": {"type": "string", "max": 256, "min": 1, "pattern": "^[\\s\\S]+$"}, "MetadataAttributeSchemaList": {"type": "list", "member": {"shape": "MetadataAttributeSchema"}, "max": 25, "min": 1}, "MetadataConfigurationForReranking": {"type": "structure", "required": ["selectionMode"], "members": {"selectionMode": {"shape": "RerankingMetadataSelectionMode", "documentation": "<p>Specifies whether to consider all metadata when reranking, or only the metadata that you select. If you specify <code>SELECTIVE</code>, include the <code>selectiveModeConfiguration</code> field.</p>"}, "selectiveModeConfiguration": {"shape": "RerankingMetadataSelectiveModeConfiguration", "documentation": "<p>Contains configurations for the metadata fields to include or exclude when considering reranking.</p>"}}, "documentation": "<p>Contains configurations for the metadata to use in reranking.</p>"}, "MimeType": {"type": "string"}, "ModelIdentifier": {"type": "string", "max": 2048, "min": 1, "pattern": "(^arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:(([0-9]{12}:custom-model/[a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}(([:][a-z0-9-]{1,63}){0,2})?/[a-z0-9]{12})|(:foundation-model/([a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}([.]?[a-z0-9-]{1,63})([:][a-z0-9-]{1,63}){0,2})))|(([a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}([.]?[a-z0-9-]{1,63})([:][a-z0-9-]{1,63}){0,2}))|(([0-9a-zA-Z][_-]?)+))$|(^arn:aws(|-us-gov|-cn|-iso|-iso-b):bedrock:(|[0-9a-z-]{1,20}):(|[0-9]{12}):inference-profile/[a-zA-Z0-9-:.]+)$"}, "ModelInvocationInput": {"type": "structure", "members": {"foundationModel": {"shape": "ModelIdentifier", "documentation": "<p>The identifier of a foundation model.</p>"}, "inferenceConfiguration": {"shape": "InferenceConfiguration", "documentation": "<p>Specifications about the inference parameters that were provided alongside the prompt. These are specified in the <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent_PromptOverrideConfiguration.html\">PromptOverrideConfiguration</a> object that was set when the agent was created or updated. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/model-parameters.html\">Inference parameters for foundation models</a>.</p>"}, "overrideLambda": {"shape": "LambdaArn", "documentation": "<p>The ARN of the Lambda function to use when parsing the raw foundation model output in parts of the agent sequence.</p>"}, "parserMode": {"shape": "CreationMode", "documentation": "<p>Specifies whether to override the default parser Lambda function when parsing the raw foundation model output in the part of the agent sequence defined by the <code>promptType</code>.</p>"}, "promptCreationMode": {"shape": "CreationMode", "documentation": "<p>Specifies whether the default prompt template was <code>OVERRIDDEN</code>. If it was, the <code>basePromptTemplate</code> that was set in the <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent_PromptOverrideConfiguration.html\">PromptOverrideConfiguration</a> object when the agent was created or updated is used instead.</p>"}, "text": {"shape": "PromptText", "documentation": "<p>The text that prompted the agent at this step.</p>"}, "traceId": {"shape": "TraceId", "documentation": "<p>The unique identifier of the trace.</p>"}, "type": {"shape": "PromptType", "documentation": "<p>The step in the agent sequence.</p>"}}, "documentation": "<p>The input for the pre-processing step.</p> <ul> <li> <p>The <code>type</code> matches the agent step.</p> </li> <li> <p>The <code>text</code> contains the prompt.</p> </li> <li> <p>The <code>inferenceConfiguration</code>, <code>parserMode</code>, and <code>overrideLambda</code> values are set in the <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent_PromptOverrideConfiguration.html\">PromptOverrideConfiguration</a> object that was set when the agent was created or updated.</p> </li> </ul>", "sensitive": true}, "ModelNotReadyException": {"type": "structure", "members": {"message": {"shape": "NonBlankString"}}, "documentation": "<p> The model specified in the request is not ready to serve inference requests. The AWS SDK will automatically retry the operation up to 5 times. For information about configuring automatic retries, see <a href=\"https://docs.aws.amazon.com/sdkref/latest/guide/feature-retry-behavior.html\">Retry behavior</a> in the <i>AWS SDKs and Tools</i> reference guide. </p>", "error": {"httpStatusCode": 424, "senderFault": true}, "exception": true}, "ModelPerformanceConfiguration": {"type": "structure", "members": {"performanceConfig": {"shape": "PerformanceConfiguration", "documentation": "<p>The latency configuration for the model.</p>"}}, "documentation": "<p>The performance configuration for a model called with <a>InvokeFlow</a>.</p>"}, "Name": {"type": "string", "pattern": "^([0-9a-zA-Z][_-]?){1,100}$", "sensitive": true}, "NextToken": {"type": "string", "max": 2048, "min": 1, "pattern": "^\\S*$"}, "NodeErrorCode": {"type": "string", "enum": ["VALIDATION", "DEPENDENCY_FAILED", "BAD_GATEWAY", "INTERNAL_SERVER"]}, "NodeExecutionContent": {"type": "structure", "members": {"document": {"shape": "Document", "documentation": "<p>The document content of the field, which can contain text or structured data.</p>"}}, "documentation": "<p>Contains the content of a flow node's input or output field for a flow execution.</p> <note> <p>Flow executions is in preview release for Amazon Bedrock and is subject to change.</p> </note>", "sensitive": true, "union": true}, "NodeFailureEvent": {"type": "structure", "required": ["errorCode", "errorMessage", "nodeName", "timestamp"], "members": {"errorCode": {"shape": "NodeErrorCode", "documentation": "<p>The error code that identifies the type of failure that occurred at the node.</p>"}, "errorMessage": {"shape": "String", "documentation": "<p>A descriptive message that provides details about the node failure.</p>"}, "nodeName": {"shape": "NodeName", "documentation": "<p>The name of the node where the failure occurred.</p>"}, "timestamp": {"shape": "DateTimestamp", "documentation": "<p>The timestamp when the node failure occurred.</p>"}}, "documentation": "<p>Contains information about a failure that occurred at a specific node during a flow execution.</p> <note> <p>Flow executions is in preview release for Amazon Bedrock and is subject to change.</p> </note>", "sensitive": true}, "NodeInputEvent": {"type": "structure", "required": ["fields", "nodeName", "timestamp"], "members": {"fields": {"shape": "NodeInputFields", "documentation": "<p>A list of input fields provided to the node.</p>"}, "nodeName": {"shape": "NodeName", "documentation": "<p>The name of the node that received the inputs.</p>"}, "timestamp": {"shape": "DateTimestamp", "documentation": "<p>The timestamp when the inputs were provided to the node.</p>"}}, "documentation": "<p>Contains information about the inputs provided to a specific node during a flow execution.</p> <note> <p>Flow executions is in preview release for Amazon Bedrock and is subject to change.</p> </note>", "sensitive": true}, "NodeInputField": {"type": "structure", "required": ["content", "name"], "members": {"content": {"shape": "NodeExecutionContent", "documentation": "<p>The content of the input field, which can contain text or structured data.</p>"}, "name": {"shape": "NodeInputName", "documentation": "<p>The name of the input field as defined in the node's input schema.</p>"}}, "documentation": "<p>Represents an input field provided to a node during a flow execution.</p>", "sensitive": true}, "NodeInputFields": {"type": "list", "member": {"shape": "NodeInputField"}, "max": 5, "min": 1}, "NodeInputName": {"type": "string", "pattern": "^[a-zA-Z]([_]?[0-9a-zA-Z]){0,99}$"}, "NodeName": {"type": "string", "pattern": "^[a-zA-Z]([_]?[0-9a-zA-Z]){0,99}$"}, "NodeOutputEvent": {"type": "structure", "required": ["fields", "nodeName", "timestamp"], "members": {"fields": {"shape": "NodeOutputFields", "documentation": "<p>A list of output fields produced by the node.</p>"}, "nodeName": {"shape": "NodeName", "documentation": "<p>The name of the node that produced the outputs.</p>"}, "timestamp": {"shape": "DateTimestamp", "documentation": "<p>The timestamp when the outputs were produced by the node.</p>"}}, "documentation": "<p>Contains information about the outputs produced by a specific node during a flow execution.</p> <note> <p>Flow executions is in preview release for Amazon Bedrock and is subject to change.</p> </note>", "sensitive": true}, "NodeOutputField": {"type": "structure", "required": ["content", "name"], "members": {"content": {"shape": "NodeExecutionContent", "documentation": "<p>The content of the output field, which can contain text or structured data.</p>"}, "name": {"shape": "NodeOutputName", "documentation": "<p>The name of the output field as defined in the node's output schema.</p>"}}, "documentation": "<p>Represents an output field produced by a node during a flow execution.</p> <note> <p>Flow executions is in preview release for Amazon Bedrock and is subject to change.</p> </note>", "sensitive": true}, "NodeOutputFields": {"type": "list", "member": {"shape": "NodeOutputField"}, "max": 2, "min": 1}, "NodeOutputName": {"type": "string", "pattern": "^[a-zA-Z]([_]?[0-9a-zA-Z]){0,99}$"}, "NodeType": {"type": "string", "enum": ["FlowInputNode", "FlowOutputNode", "LambdaFunctionNode", "KnowledgeBaseNode", "PromptNode", "ConditionNode", "LexNode"]}, "NonBlankString": {"type": "string", "pattern": "^[\\s\\S]*$"}, "Observation": {"type": "structure", "members": {"actionGroupInvocationOutput": {"shape": "ActionGroupInvocationOutput", "documentation": "<p>Contains the JSON-formatted string returned by the API invoked by the action group.</p>"}, "agentCollaboratorInvocationOutput": {"shape": "AgentCollaboratorInvocationOutput", "documentation": "<p>A collaborator's invocation output.</p>"}, "codeInterpreterInvocationOutput": {"shape": "CodeInterpreterInvocationOutput", "documentation": "<p>Contains the JSON-formatted string returned by the API invoked by the code interpreter.</p>"}, "finalResponse": {"shape": "FinalResponse", "documentation": "<p>Contains details about the response to the user.</p>"}, "knowledgeBaseLookupOutput": {"shape": "KnowledgeBaseLookupOutput", "documentation": "<p>Contains details about the results from looking up the knowledge base.</p>"}, "repromptResponse": {"shape": "RepromptResponse", "documentation": "<p>Contains details about the response to reprompt the input.</p>"}, "traceId": {"shape": "TraceId", "documentation": "<p>The unique identifier of the trace.</p>"}, "type": {"shape": "Type", "documentation": "<p>Specifies what kind of information the agent returns in the observation. The following values are possible.</p> <ul> <li> <p> <code>ACTION_GROUP</code> – The agent returns the result of an action group.</p> </li> <li> <p> <code>KNOWLEDGE_BASE</code> – The agent returns information from a knowledge base.</p> </li> <li> <p> <code>FINISH</code> – The agent returns a final response to the user with no follow-up.</p> </li> <li> <p> <code>ASK_USER</code> – The agent asks the user a question.</p> </li> <li> <p> <code>REPROMPT</code> – The agent prompts the user again for the same information.</p> </li> </ul>"}}, "documentation": "<p>Contains the result or output of an action group or knowledge base, or the response to the user.</p>", "sensitive": true}, "OptimizePromptRequest": {"type": "structure", "required": ["input", "targetModelId"], "members": {"input": {"shape": "InputPrompt", "documentation": "<p>Contains the prompt to optimize.</p>"}, "targetModelId": {"shape": "OptimizePromptRequestTargetModelIdString", "documentation": "<p>The unique identifier of the model that you want to optimize the prompt for.</p>"}}}, "OptimizePromptRequestTargetModelIdString": {"type": "string", "max": 2048, "min": 1, "pattern": "^(arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:(([0-9]{12}:custom-model/[a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}/[a-z0-9]{12})|(:foundation-model/[a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}([.:]?[a-z0-9-]{1,63}))|([0-9]{12}:provisioned-model/[a-z0-9]{12})))|([a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}([.:]?[a-z0-9-]{1,63}))|(([0-9a-zA-Z][_-]?)+)$"}, "OptimizePromptResponse": {"type": "structure", "required": ["optimizedPrompt"], "members": {"optimizedPrompt": {"shape": "OptimizedPromptStream", "documentation": "<p>The prompt after being optimized for the task.</p>"}}, "payload": "optimizedPrompt"}, "OptimizedPrompt": {"type": "structure", "members": {"textPrompt": {"shape": "TextPrompt", "documentation": "<p>Contains information about the text in the prompt that was optimized.</p>"}}, "documentation": "<p>Contains information about the optimized prompt.</p>", "union": true}, "OptimizedPromptEvent": {"type": "structure", "members": {"optimizedPrompt": {"shape": "OptimizedPrompt", "documentation": "<p>Contains information about the optimized prompt.</p>"}}, "documentation": "<p>An event in which the prompt was optimized.</p>", "event": true, "sensitive": true}, "OptimizedPromptStream": {"type": "structure", "members": {"accessDeniedException": {"shape": "AccessDeniedException", "documentation": "<p>The request is denied because of missing access permissions. Check your permissions and retry your request.</p>"}, "analyzePromptEvent": {"shape": "AnalyzePromptEvent", "documentation": "<p>An event in which the prompt was analyzed in preparation for optimization.</p>"}, "badGatewayException": {"shape": "BadGatewayException", "documentation": "<p>There was an issue with a dependency due to a server issue. Retry your request.</p>"}, "dependencyFailedException": {"shape": "DependencyFailedException", "documentation": "<p>There was an issue with a dependency. Check the resource configurations and retry the request.</p>"}, "internalServerException": {"shape": "InternalServerException", "documentation": "<p>An internal server error occurred. Retry your request.</p>"}, "optimizedPromptEvent": {"shape": "OptimizedPromptEvent", "documentation": "<p>An event in which the prompt was optimized.</p>"}, "throttlingException": {"shape": "ThrottlingException", "documentation": "<p>Your request was throttled because of service-wide limitations. Resubmit your request later or in a different region. You can also purchase <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/prov-throughput.html\">Provisioned Throughput</a> to increase the rate or number of tokens you can process.</p>"}, "validationException": {"shape": "ValidationException", "documentation": "<p>Input validation failed. Check your request parameters and retry the request.</p>"}}, "documentation": "<p>The stream containing events in the prompt optimization process.</p>", "eventstream": true}, "OrchestrationConfiguration": {"type": "structure", "members": {"additionalModelRequestFields": {"shape": "AdditionalModelRequestFields", "documentation": "<p> Additional model parameters and corresponding values not included in the textInferenceConfig structure for a knowledge base. This allows users to provide custom model parameters specific to the language model being used. </p>"}, "inferenceConfig": {"shape": "InferenceConfig", "documentation": "<p> Configuration settings for inference when using RetrieveAndGenerate to generate responses while using a knowledge base as a source. </p>"}, "performanceConfig": {"shape": "PerformanceConfiguration", "documentation": "<p>The latency configuration for the model.</p>"}, "promptTemplate": {"shape": "PromptTemplate", "documentation": "<p>Contains the template for the prompt that's sent to the model. Orchestration prompts must include the <code>$conversation_history$</code> and <code>$output_format_instructions$</code> variables. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/prompt-placeholders.html\">Use placeholder variables</a> in the user guide.</p>"}, "queryTransformationConfiguration": {"shape": "QueryTransformationConfiguration", "documentation": "<p>To split up the prompt and retrieve multiple sources, set the transformation type to <code>QUERY_DECOMPOSITION</code>.</p>"}}, "documentation": "<p>Settings for how the model processes the prompt prior to retrieval and generation.</p>"}, "OrchestrationExecutor": {"type": "structure", "members": {"lambda": {"shape": "LambdaArn", "documentation": "<p>The Amazon Resource Name (ARN) of the Lambda function containing the business logic that is carried out upon invoking the action. </p>"}}, "documentation": "<p>The structure of the executor invoking the actions in custom orchestration.</p>", "union": true}, "OrchestrationModelInvocationOutput": {"type": "structure", "members": {"metadata": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>Contains information about the foundation model output from the orchestration step.</p>"}, "rawResponse": {"shape": "RawResponse", "documentation": "<p>Contains details of the raw response from the foundation model output.</p>"}, "reasoningContent": {"shape": "ReasoningContentBlock", "documentation": "<p>Contains content about the reasoning that the model made during the orchestration step. </p>"}, "traceId": {"shape": "TraceId", "documentation": "<p>The unique identifier of the trace.</p>"}}, "documentation": "<p>The foundation model output from the orchestration step.</p>", "sensitive": true}, "OrchestrationTrace": {"type": "structure", "members": {"invocationInput": {"shape": "InvocationInput", "documentation": "<p>Contains information pertaining to the action group or knowledge base that is being invoked.</p>"}, "modelInvocationInput": {"shape": "ModelInvocationInput", "documentation": "<p>The input for the orchestration step.</p> <ul> <li> <p>The <code>type</code> is <code>ORCHESTRATION</code>.</p> </li> <li> <p>The <code>text</code> contains the prompt.</p> </li> <li> <p>The <code>inferenceConfiguration</code>, <code>parserMode</code>, and <code>overrideLambda</code> values are set in the <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent_PromptOverrideConfiguration.html\">PromptOverrideConfiguration</a> object that was set when the agent was created or updated.</p> </li> </ul>"}, "modelInvocationOutput": {"shape": "OrchestrationModelInvocationOutput", "documentation": "<p>Contains information pertaining to the output from the foundation model that is being invoked.</p>"}, "observation": {"shape": "Observation", "documentation": "<p>Details about the observation (the output of the action group Lambda or knowledge base) made by the agent.</p>"}, "rationale": {"shape": "Rationale", "documentation": "<p>Details about the reasoning, based on the input, that the agent uses to justify carrying out an action group or getting information from a knowledge base.</p>"}}, "documentation": "<p>Details about the orchestration step, in which the agent determines the order in which actions are executed and which knowledge bases are retrieved.</p>", "sensitive": true, "union": true}, "OrchestrationType": {"type": "string", "enum": ["DEFAULT", "CUSTOM_ORCHESTRATION"]}, "OutputFile": {"type": "structure", "members": {"bytes": {"shape": "FileBody", "documentation": "<p>The byte count of files that contains response from code interpreter.</p>"}, "name": {"shape": "String", "documentation": "<p>The name of the file containing response from code interpreter.</p>"}, "type": {"shape": "MimeType", "documentation": "<p>The type of file that contains response from the code interpreter.</p>"}}, "documentation": "<p>Contains details of the response from code interpreter.</p>", "sensitive": true}, "OutputFiles": {"type": "list", "member": {"shape": "OutputFile"}, "max": 5, "min": 0}, "OutputString": {"type": "string", "sensitive": true}, "Parameter": {"type": "structure", "members": {"name": {"shape": "String", "documentation": "<p>The name of the parameter.</p>"}, "type": {"shape": "String", "documentation": "<p>The type of the parameter.</p>"}, "value": {"shape": "String", "documentation": "<p>The value of the parameter.</p>"}}, "documentation": "<p>A parameter for the API request or function.</p>"}, "ParameterDescription": {"type": "string", "max": 500, "min": 1}, "ParameterDetail": {"type": "structure", "required": ["type"], "members": {"description": {"shape": "ParameterDescription", "documentation": "<p> A description of the parameter. Helps the foundation model determine how to elicit the parameters from the user. </p>"}, "required": {"shape": "Boolean", "documentation": "<p> Whether the parameter is required for the agent to complete the function for action group invocation. </p>"}, "type": {"shape": "ParameterType", "documentation": "<p> The data type of the parameter. </p>"}}, "documentation": "<p> Contains details about a parameter in a function for an action group. </p>"}, "ParameterList": {"type": "list", "member": {"shape": "Parameter"}}, "ParameterMap": {"type": "map", "key": {"shape": "ParameterName"}, "value": {"shape": "ParameterDetail"}}, "ParameterName": {"type": "string", "pattern": "^([0-9a-zA-Z][_-]?){1,100}$"}, "ParameterType": {"type": "string", "enum": ["string", "number", "integer", "boolean", "array"]}, "Parameters": {"type": "list", "member": {"shape": "Parameter"}}, "PartBody": {"type": "blob", "max": 1000000, "min": 0, "sensitive": true}, "Payload": {"type": "string", "sensitive": true}, "PayloadPart": {"type": "structure", "members": {"attribution": {"shape": "Attribution", "documentation": "<p>Contains citations for a part of an agent response.</p>"}, "bytes": {"shape": "PartBody", "documentation": "<p>A part of the agent response in bytes.</p>"}}, "documentation": "<p>Contains a part of an agent response and citations for it.</p>", "event": true, "sensitive": true}, "PayloadType": {"type": "string", "enum": ["TEXT", "RETURN_CONTROL"]}, "PerformanceConfigLatency": {"type": "string", "enum": ["standard", "optimized"]}, "PerformanceConfiguration": {"type": "structure", "members": {"latency": {"shape": "PerformanceConfigLatency", "documentation": "<p>To use a latency-optimized version of the model, set to <code>optimized</code>.</p>"}}, "documentation": "<p>Performance settings for a model.</p>"}, "PostProcessingModelInvocationOutput": {"type": "structure", "members": {"metadata": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p> Contains information about the foundation model output from the post-processing step. </p>"}, "parsedResponse": {"shape": "PostProcessingParsedResponse", "documentation": "<p>Details about the response from the Lambda parsing of the output of the post-processing step.</p>"}, "rawResponse": {"shape": "RawResponse", "documentation": "<p> Details of the raw response from the foundation model output. </p>"}, "reasoningContent": {"shape": "ReasoningContentBlock", "documentation": "<p>Contains content about the reasoning that the model made during the post-processing step.</p>"}, "traceId": {"shape": "TraceId", "documentation": "<p>The unique identifier of the trace.</p>"}}, "documentation": "<p>The foundation model output from the post-processing step.</p>", "sensitive": true}, "PostProcessingParsedResponse": {"type": "structure", "members": {"text": {"shape": "OutputString", "documentation": "<p>The text returned by the parser.</p>"}}, "documentation": "<p>Details about the response from the Lambda parsing of the output from the post-processing step.</p>", "sensitive": true}, "PostProcessingTrace": {"type": "structure", "members": {"modelInvocationInput": {"shape": "ModelInvocationInput", "documentation": "<p>The input for the post-processing step.</p> <ul> <li> <p>The <code>type</code> is <code>POST_PROCESSING</code>.</p> </li> <li> <p>The <code>text</code> contains the prompt.</p> </li> <li> <p>The <code>inferenceConfiguration</code>, <code>parserMode</code>, and <code>overrideLambda</code> values are set in the <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent_PromptOverrideConfiguration.html\">PromptOverrideConfiguration</a> object that was set when the agent was created or updated.</p> </li> </ul>"}, "modelInvocationOutput": {"shape": "PostProcessingModelInvocationOutput", "documentation": "<p>The foundation model output from the post-processing step.</p>"}}, "documentation": "<p>Details about the post-processing step, in which the agent shapes the response.</p>", "sensitive": true, "union": true}, "PreProcessingModelInvocationOutput": {"type": "structure", "members": {"metadata": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p> Contains information about the foundation model output from the pre-processing step. </p>"}, "parsedResponse": {"shape": "PreProcessingParsedResponse", "documentation": "<p>Details about the response from the Lambda parsing of the output of the pre-processing step.</p>"}, "rawResponse": {"shape": "RawResponse", "documentation": "<p> Details of the raw response from the foundation model output. </p>"}, "reasoningContent": {"shape": "ReasoningContentBlock", "documentation": "<p>Contains content about the reasoning that the model made during the pre-processing step. </p>"}, "traceId": {"shape": "TraceId", "documentation": "<p>The unique identifier of the trace.</p>"}}, "documentation": "<p>The foundation model output from the pre-processing step.</p>", "sensitive": true}, "PreProcessingParsedResponse": {"type": "structure", "members": {"isValid": {"shape": "Boolean", "documentation": "<p>Whether the user input is valid or not. If <code>false</code>, the agent doesn't proceed to orchestration.</p>"}, "rationale": {"shape": "RationaleString", "documentation": "<p>The text returned by the parsing of the pre-processing step, explaining the steps that the agent plans to take in orchestration, if the user input is valid.</p>"}}, "documentation": "<p>Details about the response from the Lambda parsing of the output from the pre-processing step.</p>", "sensitive": true}, "PreProcessingTrace": {"type": "structure", "members": {"modelInvocationInput": {"shape": "ModelInvocationInput", "documentation": "<p>The input for the pre-processing step.</p> <ul> <li> <p>The <code>type</code> is <code>PRE_PROCESSING</code>.</p> </li> <li> <p>The <code>text</code> contains the prompt.</p> </li> <li> <p>The <code>inferenceConfiguration</code>, <code>parserMode</code>, and <code>overrideLambda</code> values are set in the <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent_PromptOverrideConfiguration.html\">PromptOverrideConfiguration</a> object that was set when the agent was created or updated.</p> </li> </ul>"}, "modelInvocationOutput": {"shape": "PreProcessingModelInvocationOutput", "documentation": "<p>The foundation model output from the pre-processing step.</p>"}}, "documentation": "<p>Details about the pre-processing step, in which the agent contextualizes and categorizes user inputs.</p>", "sensitive": true, "union": true}, "PromptConfiguration": {"type": "structure", "members": {"additionalModelRequestFields": {"shape": "Document", "documentation": "<p>If the Converse or ConverseStream operations support the model, <code>additionalModelRequestFields</code> contains additional inference parameters, beyond the base set of inference parameters in the <code>inferenceConfiguration</code> field. </p> <p>For more information, see <i>Inference request parameters and response fields for foundation models</i> in the Amazon Bedrock user guide.</p>"}, "basePromptTemplate": {"shape": "BasePromptTemplate", "documentation": "<p>Defines the prompt template with which to replace the default prompt template. You can use placeholder variables in the base prompt template to customize the prompt. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/prompt-placeholders.html\">Prompt template placeholder variables</a>. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/advanced-prompts-configure.html\">Configure the prompt templates</a>.</p>"}, "foundationModel": {"shape": "ModelIdentifier", "documentation": "<p> The foundation model to use. </p>"}, "inferenceConfiguration": {"shape": "InferenceConfiguration", "documentation": "<p>Contains inference parameters to use when the agent invokes a foundation model in the part of the agent sequence defined by the <code>promptType</code>. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/model-parameters.html\">Inference parameters for foundation models</a>.</p>"}, "parserMode": {"shape": "CreationMode", "documentation": "<p>Specifies whether to override the default parser Lambda function when parsing the raw foundation model output in the part of the agent sequence defined by the <code>promptType</code>. If you set the field as <code>OVERRIDDEN</code>, the <code>overrideLambda</code> field in the <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent_PromptOverrideConfiguration.html\">PromptOverrideConfiguration</a> must be specified with the ARN of a Lambda function.</p>"}, "promptCreationMode": {"shape": "CreationMode", "documentation": "<p>Specifies whether to override the default prompt template for this <code>promptType</code>. Set this value to <code>OVERRIDDEN</code> to use the prompt that you provide in the <code>basePromptTemplate</code>. If you leave it as <code>DEFAULT</code>, the agent uses a default prompt template.</p>"}, "promptState": {"shape": "PromptState", "documentation": "<p>Specifies whether to allow the inline agent to carry out the step specified in the <code>promptType</code>. If you set this value to <code>DISABLED</code>, the agent skips that step. The default state for each <code>promptType</code> is as follows.</p> <ul> <li> <p> <code>PRE_PROCESSING</code> – <code>ENABLED</code> </p> </li> <li> <p> <code>ORCHESTRATION</code> – <code>ENABLED</code> </p> </li> <li> <p> <code>KNOWLEDGE_BASE_RESPONSE_GENERATION</code> – <code>ENABLED</code> </p> </li> <li> <p> <code>POST_PROCESSING</code> – <code>DISABLED</code> </p> </li> </ul>"}, "promptType": {"shape": "PromptType", "documentation": "<p> The step in the agent sequence that this prompt configuration applies to. </p>"}}, "documentation": "<p> Contains configurations to override a prompt template in one part of an agent sequence. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/advanced-prompts.html\">Advanced prompts</a>. </p>"}, "PromptConfigurations": {"type": "list", "member": {"shape": "PromptConfiguration"}, "max": 10, "min": 0}, "PromptCreationConfigurations": {"type": "structure", "members": {"excludePreviousThinkingSteps": {"shape": "Boolean", "documentation": "<p>If <code>true</code>, the service removes any content between <code>&lt;thinking&gt;</code> tags from previous conversations in an agent session. The service will only remove content from already processed turns. This helps you remove content which might not be useful for current and subsequent invocations. This can reduce the input token count and potentially save costs. The default value is <code>false</code>.</p>"}, "previousConversationTurnsToInclude": {"shape": "PromptCreationConfigurationsPreviousConversationTurnsToIncludeInteger", "documentation": "<p>The number of previous conversations from the ongoing agent session to include in the conversation history of the agent prompt, during the current invocation. This gives you more granular control over the context that the model is made aware of, and helps the model remove older context which is no longer useful during the ongoing agent session.</p>"}}, "documentation": "<p>Specifies parameters that control how the service populates the agent prompt for an <code>InvokeAgent</code> or <code>InvokeInlineAgent</code> request. You can control which aspects of previous invocations in the same agent session the service uses to populate the agent prompt. This gives you more granular control over the contextual history that is used to process the current request.</p>"}, "PromptCreationConfigurationsPreviousConversationTurnsToIncludeInteger": {"type": "integer", "box": true, "min": 0}, "PromptOverrideConfiguration": {"type": "structure", "required": ["promptConfigurations"], "members": {"overrideLambda": {"shape": "LambdaResourceArn", "documentation": "<p>The ARN of the Lambda function to use when parsing the raw foundation model output in parts of the agent sequence. If you specify this field, at least one of the <code>promptConfigurations</code> must contain a <code>parserMode</code> value that is set to <code>OVERRIDDEN</code>. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/lambda-parser.html\">Parser Lambda function in Amazon Bedrock Agents</a>. </p>"}, "promptConfigurations": {"shape": "PromptConfigurations", "documentation": "<p>Contains configurations to override a prompt template in one part of an agent sequence. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/advanced-prompts.html\">Advanced prompts</a>. </p>"}}, "documentation": "<p>Contains configurations to override prompts in different parts of an agent sequence. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/advanced-prompts.html\">Advanced prompts</a>. </p>", "sensitive": true}, "PromptSessionAttributesMap": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "String"}}, "PromptState": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "PromptTemplate": {"type": "structure", "members": {"textPromptTemplate": {"shape": "TextPromptTemplate", "documentation": "<p>The template for the prompt that's sent to the model for response generation. You can include prompt placeholders, which become replaced before the prompt is sent to the model to provide instructions and context to the model. In addition, you can include XML tags to delineate meaningful sections of the prompt template.</p> <p>For more information, see the following resources:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/kb-test-config.html#kb-test-config-sysprompt\">Knowledge base prompt templates</a> </p> </li> <li> <p> <a href=\"https://docs.anthropic.com/claude/docs/use-xml-tags\">Use XML tags with Anthropic Claude models</a> </p> </li> </ul>"}}, "documentation": "<p>Contains the template for the prompt that's sent to the model for response generation. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/kb-test-config.html#kb-test-config-sysprompt\">Knowledge base prompt templates</a>.</p> <p>This data type is used in the following API operations:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_RetrieveAndGenerate.html#API_agent-runtime_RetrieveAndGenerate_RequestSyntax\">RetrieveAndGenerate request</a> – in the <code>filter</code> field</p> </li> </ul>"}, "PromptText": {"type": "string", "sensitive": true}, "PromptType": {"type": "string", "enum": ["PRE_PROCESSING", "ORCHESTRATION", "KNOWLEDGE_BASE_RESPONSE_GENERATION", "POST_PROCESSING", "ROUTING_CLASSIFIER"]}, "PropertyParameters": {"type": "structure", "members": {"properties": {"shape": "ParameterList", "documentation": "<p>A list of parameters in the request body.</p>"}}, "documentation": "<p>Contains the parameters in the request body.</p>"}, "PutInvocationStepRequest": {"type": "structure", "required": ["invocationIdentifier", "invocationStepTime", "payload", "sessionIdentifier"], "members": {"invocationIdentifier": {"shape": "InvocationIdentifier", "documentation": "<p>The unique identifier (in UUID format) of the invocation to add the invocation step to.</p>"}, "invocationStepId": {"shape": "<PERSON><PERSON>", "documentation": "<p>The unique identifier of the invocation step in UUID format.</p>"}, "invocationStepTime": {"shape": "DateTimestamp", "documentation": "<p>The timestamp for when the invocation step occurred.</p>"}, "payload": {"shape": "InvocationStepPayload", "documentation": "<p>The payload for the invocation step, including text and images for the interaction.</p>"}, "sessionIdentifier": {"shape": "SessionIdentifier", "documentation": "<p>The unique identifier for the session to add the invocation step to. You can specify either the session's <code>sessionId</code> or its Amazon Resource Name (ARN).</p>", "location": "uri", "locationName": "sessionIdentifier"}}}, "PutInvocationStepResponse": {"type": "structure", "required": ["invocationStepId"], "members": {"invocationStepId": {"shape": "<PERSON><PERSON>", "documentation": "<p>The unique identifier of the invocation step in UUID format.</p>"}}}, "QueryGenerationInput": {"type": "structure", "required": ["text", "type"], "members": {"text": {"shape": "QueryGenerationInputTextString", "documentation": "<p>The text of the query.</p>"}, "type": {"shape": "InputQueryType", "documentation": "<p>The type of the query.</p>"}}, "documentation": "<p>Contains information about a natural language query to transform into SQL.</p>", "sensitive": true}, "QueryGenerationInputTextString": {"type": "string", "max": 20000, "min": 1}, "QueryTransformationConfiguration": {"type": "structure", "required": ["type"], "members": {"type": {"shape": "QueryTransformationType", "documentation": "<p>The type of transformation to apply to the prompt.</p>"}}, "documentation": "<p>To split up the prompt and retrieve multiple sources, set the transformation type to <code>QUERY_DECOMPOSITION</code>.</p>"}, "QueryTransformationMode": {"type": "string", "enum": ["TEXT_TO_SQL"]}, "QueryTransformationType": {"type": "string", "enum": ["QUERY_DECOMPOSITION"]}, "RAGStopSequences": {"type": "list", "member": {"shape": "RAGStopSequencesMemberString"}, "max": 4, "min": 0}, "RAGStopSequencesMemberString": {"type": "string", "max": 1000, "min": 1}, "Rationale": {"type": "structure", "members": {"text": {"shape": "RationaleString", "documentation": "<p>The reasoning or thought process of the agent, based on the input.</p>"}, "traceId": {"shape": "TraceId", "documentation": "<p>The unique identifier of the trace step.</p>"}}, "documentation": "<p>Contains the reasoning, based on the input, that the agent uses to justify carrying out an action group or getting information from a knowledge base.</p>", "sensitive": true}, "RationaleString": {"type": "string", "sensitive": true}, "RawResponse": {"type": "structure", "members": {"content": {"shape": "String", "documentation": "<p>The foundation model's raw output content.</p>"}}, "documentation": "<p>Contains the raw output from the foundation model.</p>", "sensitive": true}, "ReasoningContentBlock": {"type": "structure", "members": {"reasoningText": {"shape": "ReasoningTextBlock", "documentation": "<p>Contains information about the reasoning that the model used to return the content in the content block.</p>"}, "redactedContent": {"shape": "Blob", "documentation": "<p>The content in the reasoning that was encrypted by the model provider for trust and safety reasons.</p>"}}, "documentation": "<p>Contains content regarding the reasoning that the foundation model made with respect to the content in the content block. Reasoning refers to a Chain of Thought (CoT) that the model generates to enhance the accuracy of its final response.</p>", "sensitive": true, "union": true}, "ReasoningTextBlock": {"type": "structure", "required": ["text"], "members": {"signature": {"shape": "String", "documentation": "<p>A hash of all the messages in the conversation to ensure that the content in the reasoning text block isn't tampered with. You must submit the signature in subsequent <code>Converse</code> requests, in addition to the previous messages. If the previous messages are tampered with, the response throws an error.</p>"}, "text": {"shape": "String", "documentation": "<p>Text describing the reasoning that the model used to return the content in the content block.</p>"}}, "documentation": "<p>Contains information about the reasoning that the model used to return the content in the content block.</p>", "sensitive": true}, "RelayConversationHistory": {"type": "string", "enum": ["TO_COLLABORATOR", "DISABLED"]}, "RepromptResponse": {"type": "structure", "members": {"source": {"shape": "Source", "documentation": "<p>Specifies what output is prompting the agent to reprompt the input.</p>"}, "text": {"shape": "String", "documentation": "<p>The text reprompting the input.</p>"}}, "documentation": "<p>Contains details about the agent's response to reprompt the input.</p>", "sensitive": true}, "RequestBody": {"type": "structure", "members": {"content": {"shape": "ContentMap", "documentation": "<p>The content in the request body.</p>"}}, "documentation": "<p>The parameters in the API request body.</p>"}, "RequireConfirmation": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "RerankDocument": {"type": "structure", "required": ["type"], "members": {"jsonDocument": {"shape": "Document", "documentation": "<p>Contains a JSON document to rerank.</p>"}, "textDocument": {"shape": "RerankTextDocument", "documentation": "<p>Contains information about a text document to rerank.</p>"}, "type": {"shape": "RerankDocumentType", "documentation": "<p>The type of document to rerank.</p>"}}, "documentation": "<p>Contains information about a document to rerank. Choose the <code>type</code> to define and include the field that corresponds to the type.</p>", "sensitive": true}, "RerankDocumentType": {"type": "string", "enum": ["TEXT", "JSON"]}, "RerankQueriesList": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "max": 1, "min": 1, "sensitive": true}, "RerankQuery": {"type": "structure", "required": ["textQuery", "type"], "members": {"textQuery": {"shape": "RerankTextDocument", "documentation": "<p>Contains information about a text query.</p>"}, "type": {"shape": "RerankQueryContentType", "documentation": "<p>The type of the query.</p>"}}, "documentation": "<p>Contains information about a query to submit to the reranker model.</p>", "sensitive": true}, "RerankQueryContentType": {"type": "string", "enum": ["TEXT"]}, "RerankRequest": {"type": "structure", "required": ["queries", "rerankingConfiguration", "sources"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>If the total number of results was greater than could fit in a response, a token is returned in the <code>nextToken</code> field. You can enter that token in this field to return the next batch of results.</p>"}, "queries": {"shape": "RerankQueriesList", "documentation": "<p>An array of objects, each of which contains information about a query to submit to the reranker model.</p>"}, "rerankingConfiguration": {"shape": "RerankingConfiguration", "documentation": "<p>Contains configurations for reranking.</p>"}, "sources": {"shape": "RerankSourcesList", "documentation": "<p>An array of objects, each of which contains information about the sources to rerank.</p>"}}}, "RerankResponse": {"type": "structure", "required": ["results"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>If the total number of results is greater than can fit in the response, use this token in the <code>nextToken</code> field when making another request to return the next batch of results.</p>"}, "results": {"shape": "RerankResultsList", "documentation": "<p>An array of objects, each of which contains information about the results of reranking.</p>"}}}, "RerankResult": {"type": "structure", "required": ["index", "relevanceScore"], "members": {"document": {"shape": "RerankDocument", "documentation": "<p>Contains information about the document.</p>"}, "index": {"shape": "RerankResultIndexInteger", "documentation": "<p>The ranking of the document. The lower a number, the higher the document is ranked.</p>"}, "relevanceScore": {"shape": "Float", "documentation": "<p>The relevance score of the document.</p>"}}, "documentation": "<p>Contains information about a document that was reranked.</p>"}, "RerankResultIndexInteger": {"type": "integer", "box": true, "max": 1000, "min": 0}, "RerankResultsList": {"type": "list", "member": {"shape": "RerankResult"}}, "RerankSource": {"type": "structure", "required": ["inlineDocumentSource", "type"], "members": {"inlineDocumentSource": {"shape": "RerankDocument", "documentation": "<p>Contains an inline definition of a source for reranking.</p>"}, "type": {"shape": "RerankSourceType", "documentation": "<p>The type of the source.</p>"}}, "documentation": "<p>Contains information about a source for reranking.</p>", "sensitive": true}, "RerankSourceType": {"type": "string", "enum": ["INLINE"]}, "RerankSourcesList": {"type": "list", "member": {"shape": "RerankSource"}, "max": 1000, "min": 1, "sensitive": true}, "RerankTextDocument": {"type": "structure", "members": {"text": {"shape": "RerankTextDocumentTextString", "documentation": "<p>The text of the document.</p>"}}, "documentation": "<p>Contains information about a text document to rerank.</p>", "sensitive": true}, "RerankTextDocumentTextString": {"type": "string", "max": 32000, "min": 1}, "RerankingConfiguration": {"type": "structure", "required": ["bedrockRerankingConfiguration", "type"], "members": {"bedrockRerankingConfiguration": {"shape": "BedrockRerankingConfiguration", "documentation": "<p>Contains configurations for an Amazon Bedrock reranker.</p>"}, "type": {"shape": "RerankingConfigurationType", "documentation": "<p>The type of reranker that the configurations apply to.</p>"}}, "documentation": "<p>Contains configurations for reranking.</p>"}, "RerankingConfigurationType": {"type": "string", "enum": ["BEDROCK_RERANKING_MODEL"]}, "RerankingMetadataSelectionMode": {"type": "string", "enum": ["SELECTIVE", "ALL"]}, "RerankingMetadataSelectiveModeConfiguration": {"type": "structure", "members": {"fieldsToExclude": {"shape": "FieldsForReranking", "documentation": "<p>An array of objects, each of which specifies a metadata field to exclude from consideration when reranking.</p>"}, "fieldsToInclude": {"shape": "FieldsForReranking", "documentation": "<p>An array of objects, each of which specifies a metadata field to include in consideration when reranking. The remaining metadata fields are ignored.</p>"}}, "documentation": "<p>Contains configurations for the metadata fields to include or exclude when considering reranking. If you include the <code>fieldsToExclude</code> field, the reranker ignores all the metadata fields that you specify. If you include the <code>fieldsToInclude</code> field, the reranker uses only the metadata fields that you specify and ignores all others. You can include only one of these fields.</p>", "union": true}, "ResourceDescription": {"type": "string", "documentation": "<p>/ @documentation(&quot;Description of the using the resource.&quot;)</p>", "max": 200, "min": 1, "sensitive": true}, "ResourceName": {"type": "string", "pattern": "^([0-9a-zA-Z][_-]?){1,100}$", "sensitive": true}, "ResourceNotFoundException": {"type": "structure", "members": {"message": {"shape": "NonBlankString"}}, "documentation": "<p>The specified resource Amazon Resource Name (ARN) was not found. Check the Amazon Resource Name (ARN) and try your request again.</p>", "error": {"httpStatusCode": 404, "senderFault": true}, "exception": true}, "ResponseBody": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "ContentBody"}}, "ResponseState": {"type": "string", "enum": ["FAILURE", "REPROMPT"]}, "ResponseStream": {"type": "structure", "members": {"accessDeniedException": {"shape": "AccessDeniedException", "documentation": "<p>The request is denied because of missing access permissions. Check your permissions and retry your request.</p>"}, "badGatewayException": {"shape": "BadGatewayException", "documentation": "<p>There was an issue with a dependency due to a server issue. Retry your request.</p>"}, "chunk": {"shape": "PayloadPart", "documentation": "<p>Contains a part of an agent response and citations for it.</p>"}, "conflictException": {"shape": "ConflictException", "documentation": "<p>There was a conflict performing an operation. Resolve the conflict and retry your request.</p>"}, "dependencyFailedException": {"shape": "DependencyFailedException", "documentation": "<p>There was an issue with a dependency. Check the resource configurations and retry the request.</p>"}, "files": {"shape": "FilePart", "documentation": "<p>Contains intermediate response for code interpreter if any files have been generated.</p>"}, "internalServerException": {"shape": "InternalServerException", "documentation": "<p>An internal server error occurred. Retry your request.</p>"}, "modelNotReadyException": {"shape": "ModelNotReadyException", "documentation": "<p> The model specified in the request is not ready to serve Inference requests. The AWS SDK will automatically retry the operation up to 5 times. For information about configuring automatic retries, see <a href=\"https://docs.aws.amazon.com/sdkref/latest/guide/feature-retry-behavior.html\">Retry behavior</a> in the <i>AWS SDKs and Tools</i> reference guide. </p>"}, "resourceNotFoundException": {"shape": "ResourceNotFoundException", "documentation": "<p>The specified resource Amazon Resource Name (ARN) was not found. Check the Amazon Resource Name (ARN) and try your request again.</p>"}, "returnControl": {"shape": "ReturnControlPayload", "documentation": "<p>Contains the parameters and information that the agent elicited from the customer to carry out an action. This information is returned to the system and can be used in your own setup for fulfilling the action.</p>"}, "serviceQuotaExceededException": {"shape": "ServiceQuotaExceededException", "documentation": "<p>The number of requests exceeds the service quota. Resubmit your request later.</p>"}, "throttlingException": {"shape": "ThrottlingException", "documentation": "<p>The number of requests exceeds the limit. Resubmit your request later.</p>"}, "trace": {"shape": "TracePart", "documentation": "<p>Contains information about the agent and session, alongside the agent's reasoning process and results from calling actions and querying knowledge bases and metadata about the trace. You can use the trace to understand how the agent arrived at the response it provided the customer. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/trace-events.html\">Trace events</a>.</p>"}, "validationException": {"shape": "ValidationException", "documentation": "<p>Input validation failed. Check your request parameters and retry the request.</p>"}}, "documentation": "<p>The response from invoking the agent and associated citations and trace information.</p>", "eventstream": true}, "RetrievalFilter": {"type": "structure", "members": {"andAll": {"shape": "RetrievalFilterList", "documentation": "<p>Knowledge base data sources are returned if their metadata attributes fulfill all the filter conditions inside this list.</p>"}, "equals": {"shape": "FilterAttribute", "documentation": "<p>Knowledge base data sources are returned if they contain a metadata attribute whose name matches the <code>key</code> and whose value matches the <code>value</code> in this object.</p> <p>The following example would return data sources with an <code>animal</code> attribute whose value is <code>cat</code>:</p> <p> <code>\"equals\": { \"key\": \"animal\", \"value\": \"cat\" }</code> </p>"}, "greaterThan": {"shape": "FilterAttribute", "documentation": "<p>Knowledge base data sources are returned if they contain a metadata attribute whose name matches the <code>key</code> and whose value is greater than the <code>value</code> in this object.</p> <p>The following example would return data sources with an <code>year</code> attribute whose value is greater than <code>1989</code>:</p> <p> <code>\"greaterThan\": { \"key\": \"year\", \"value\": 1989 }</code> </p>"}, "greaterThanOrEquals": {"shape": "FilterAttribute", "documentation": "<p>Knowledge base data sources are returned if they contain a metadata attribute whose name matches the <code>key</code> and whose value is greater than or equal to the <code>value</code> in this object.</p> <p>The following example would return data sources with an <code>year</code> attribute whose value is greater than or equal to <code>1989</code>:</p> <p> <code>\"greaterThanOrEquals\": { \"key\": \"year\", \"value\": 1989 }</code> </p>"}, "in": {"shape": "FilterAttribute", "documentation": "<p>Knowledge base data sources are returned if they contain a metadata attribute whose name matches the <code>key</code> and whose value is in the list specified in the <code>value</code> in this object.</p> <p>The following example would return data sources with an <code>animal</code> attribute that is either <code>cat</code> or <code>dog</code>:</p> <p> <code>\"in\": { \"key\": \"animal\", \"value\": [\"cat\", \"dog\"] }</code> </p>"}, "lessThan": {"shape": "FilterAttribute", "documentation": "<p>Knowledge base data sources are returned if they contain a metadata attribute whose name matches the <code>key</code> and whose value is less than the <code>value</code> in this object.</p> <p>The following example would return data sources with an <code>year</code> attribute whose value is less than to <code>1989</code>.</p> <p> <code>\"lessThan\": { \"key\": \"year\", \"value\": 1989 }</code> </p>"}, "lessThanOrEquals": {"shape": "FilterAttribute", "documentation": "<p>Knowledge base data sources are returned if they contain a metadata attribute whose name matches the <code>key</code> and whose value is less than or equal to the <code>value</code> in this object.</p> <p>The following example would return data sources with an <code>year</code> attribute whose value is less than or equal to <code>1989</code>.</p> <p> <code>\"lessThanOrEquals\": { \"key\": \"year\", \"value\": 1989 }</code> </p>"}, "listContains": {"shape": "FilterAttribute", "documentation": "<p>Knowledge base data sources are returned if they contain a metadata attribute whose name matches the <code>key</code> and whose value is a list that contains the <code>value</code> as one of its members.</p> <p>The following example would return data sources with an <code>animals</code> attribute that is a list containing a <code>cat</code> member (for example <code>[\"dog\", \"cat\"]</code>).</p> <p> <code>\"listContains\": { \"key\": \"animals\", \"value\": \"cat\" }</code> </p>"}, "notEquals": {"shape": "FilterAttribute", "documentation": "<p>Knowledge base data sources are returned when:</p> <ul> <li> <p>It contains a metadata attribute whose name matches the <code>key</code> and whose value doesn't match the <code>value</code> in this object.</p> </li> <li> <p>The key is not present in the document.</p> </li> </ul> <p>The following example would return data sources that don't contain an <code>animal</code> attribute whose value is <code>cat</code>.</p> <p> <code>\"notEquals\": { \"key\": \"animal\", \"value\": \"cat\" }</code> </p>"}, "notIn": {"shape": "FilterAttribute", "documentation": "<p>Knowledge base data sources are returned if they contain a metadata attribute whose name matches the <code>key</code> and whose value isn't in the list specified in the <code>value</code> in this object.</p> <p>The following example would return data sources whose <code>animal</code> attribute is neither <code>cat</code> nor <code>dog</code>.</p> <p> <code>\"notIn\": { \"key\": \"animal\", \"value\": [\"cat\", \"dog\"] }</code> </p>"}, "orAll": {"shape": "RetrievalFilterList", "documentation": "<p>Knowledge base data sources are returned if their metadata attributes fulfill at least one of the filter conditions inside this list.</p>"}, "startsWith": {"shape": "FilterAttribute", "documentation": "<p>Knowledge base data sources are returned if they contain a metadata attribute whose name matches the <code>key</code> and whose value starts with the <code>value</code> in this object. This filter is currently only supported for Amazon OpenSearch Serverless vector stores.</p> <p>The following example would return data sources with an <code>animal</code> attribute starts with <code>ca</code> (for example, <code>cat</code> or <code>camel</code>).</p> <p> <code>\"startsWith\": { \"key\": \"animal\", \"value\": \"ca\" }</code> </p>"}, "stringContains": {"shape": "FilterAttribute", "documentation": "<p>Knowledge base data sources are returned if they contain a metadata attribute whose name matches the <code>key</code> and whose value is one of the following:</p> <ul> <li> <p>A string that contains the <code>value</code> as a substring. The following example would return data sources with an <code>animal</code> attribute that contains the substring <code>at</code> (for example <code>cat</code>).</p> <p> <code>\"stringContains\": { \"key\": \"animal\", \"value\": \"at\" }</code> </p> </li> <li> <p>A list with a member that contains the <code>value</code> as a substring. The following example would return data sources with an <code>animals</code> attribute that is a list containing a member that contains the substring <code>at</code> (for example <code>[\"dog\", \"cat\"]</code>).</p> <p> <code>\"stringContains\": { \"key\": \"animals\", \"value\": \"at\" }</code> </p> </li> </ul>"}}, "documentation": "<p>Specifies the filters to use on the metadata attributes in the knowledge base data sources before returning results. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/kb-test-config.html\">Query configurations</a>. See the examples below to see how to use these filters.</p> <p>This data type is used in the following API operations:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_Retrieve.html#API_agent-runtime_Retrieve_RequestSyntax\">Retrieve request</a> – in the <code>filter</code> field</p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_RetrieveAndGenerate.html#API_agent-runtime_RetrieveAndGenerate_RequestSyntax\">RetrieveAndGenerate request</a> – in the <code>filter</code> field</p> </li> </ul>", "sensitive": true, "union": true}, "RetrievalFilterList": {"type": "list", "member": {"shape": "Retrieval<PERSON><PERSON>er"}, "min": 2}, "RetrievalResultConfluenceLocation": {"type": "structure", "members": {"url": {"shape": "String", "documentation": "<p>The Confluence host URL for the data source location.</p>"}}, "documentation": "<p>The Confluence data source location.</p>"}, "RetrievalResultContent": {"type": "structure", "members": {"byteContent": {"shape": "String", "documentation": "<p>A data URI with base64-encoded content from the data source. The URI is in the following format: returned in the following format: <code>data:image/jpeg;base64,${base64-encoded string}</code>.</p>"}, "row": {"shape": "RetrievalResultContentRow", "documentation": "<p>Specifies information about the rows with the cells to return in retrieval.</p>"}, "text": {"shape": "String", "documentation": "<p>The cited text from the data source.</p>"}, "type": {"shape": "RetrievalResultContentType", "documentation": "<p>The type of content in the retrieval result.</p>"}}, "documentation": "<p>Contains information about a chunk of text from a data source in the knowledge base. If the result is from a structured data source, the cell in the database and the type of the value is also identified.</p> <p>This data type is used in the following API operations:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_Retrieve.html#API_agent-runtime_Retrieve_ResponseSyntax\">Retrieve response</a> – in the <code>content</code> field</p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_RetrieveAndGenerate.html#API_agent-runtime_RetrieveAndGenerate_ResponseSyntax\">RetrieveAndGenerate response</a> – in the <code>content</code> field</p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_InvokeAgent.html#API_agent-runtime_InvokeAgent_ResponseSyntax\">InvokeAgent response</a> – in the <code>content</code> field</p> </li> </ul>", "sensitive": true}, "RetrievalResultContentColumn": {"type": "structure", "members": {"columnName": {"shape": "String", "documentation": "<p>The name of the column.</p>"}, "columnValue": {"shape": "String", "documentation": "<p>The value in the column.</p>"}, "type": {"shape": "RetrievalResultContentColumnType", "documentation": "<p>The data type of the value.</p>"}}, "documentation": "<p>Contains information about a column with a cell to return in retrieval.</p>", "sensitive": true}, "RetrievalResultContentColumnType": {"type": "string", "enum": ["BLOB", "BOOLEAN", "DOUBLE", "NULL", "LONG", "STRING"]}, "RetrievalResultContentRow": {"type": "list", "member": {"shape": "RetrievalResultContentColumn"}, "sensitive": true}, "RetrievalResultContentType": {"type": "string", "enum": ["TEXT", "IMAGE", "ROW"]}, "RetrievalResultCustomDocumentLocation": {"type": "structure", "members": {"id": {"shape": "String", "documentation": "<p>The ID of the document.</p>"}}, "documentation": "<p>Contains information about the location of a document in a custom data source.</p>"}, "RetrievalResultKendraDocumentLocation": {"type": "structure", "members": {"uri": {"shape": "String", "documentation": "<p>The document's uri.</p>"}}, "documentation": "<p>The location of a result in Amazon Kendra.</p>"}, "RetrievalResultLocation": {"type": "structure", "required": ["type"], "members": {"confluenceLocation": {"shape": "RetrievalResultConfluenceLocation", "documentation": "<p>The Confluence data source location.</p>"}, "customDocumentLocation": {"shape": "RetrievalResultCustomDocumentLocation", "documentation": "<p>Specifies the location of a document in a custom data source.</p>"}, "kendraDocumentLocation": {"shape": "RetrievalResultKendraDocumentLocation", "documentation": "<p>The location of a document in Amazon Kendra.</p>"}, "s3Location": {"shape": "RetrievalResultS3Location", "documentation": "<p>The S3 data source location.</p>"}, "salesforceLocation": {"shape": "RetrievalResultSalesforceLocation", "documentation": "<p>The Salesforce data source location.</p>"}, "sharePointLocation": {"shape": "RetrievalResultSharePointLocation", "documentation": "<p>The SharePoint data source location.</p>"}, "sqlLocation": {"shape": "RetrievalResultSqlLocation", "documentation": "<p>Specifies information about the SQL query used to retrieve the result.</p>"}, "type": {"shape": "RetrievalResultLocationType", "documentation": "<p>The type of data source location.</p>"}, "webLocation": {"shape": "RetrievalResultWebLocation", "documentation": "<p>The web URL/URLs data source location.</p>"}}, "documentation": "<p>Contains information about the data source location.</p> <p>This data type is used in the following API operations:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_Retrieve.html#API_agent-runtime_Retrieve_ResponseSyntax\">Retrieve response</a> – in the <code>location</code> field</p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_RetrieveAndGenerate.html#API_agent-runtime_RetrieveAndGenerate_ResponseSyntax\">RetrieveAndGenerate response</a> – in the <code>location</code> field</p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_InvokeAgent.html#API_agent-runtime_InvokeAgent_ResponseSyntax\">InvokeAgent response</a> – in the <code>location</code> field</p> </li> </ul>", "sensitive": true}, "RetrievalResultLocationType": {"type": "string", "enum": ["S3", "WEB", "CONFLUENCE", "SALESFORCE", "SHAREPOINT", "CUSTOM", "KENDRA", "SQL"]}, "RetrievalResultMetadata": {"type": "map", "key": {"shape": "RetrievalResultMetadataKey"}, "value": {"shape": "RetrievalResultMetadataValue"}, "min": 1, "sensitive": true}, "RetrievalResultMetadataKey": {"type": "string", "max": 100, "min": 1}, "RetrievalResultMetadataValue": {"type": "structure", "members": {}, "document": true}, "RetrievalResultS3Location": {"type": "structure", "members": {"uri": {"shape": "String", "documentation": "<p>The S3 URI for the data source location.</p>"}}, "documentation": "<p>The S3 data source location.</p> <p>This data type is used in the following API operations:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_Retrieve.html#API_agent-runtime_Retrieve_ResponseSyntax\">Retrieve response</a> – in the <code>s3Location</code> field</p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_RetrieveAndGenerate.html#API_agent-runtime_RetrieveAndGenerate_ResponseSyntax\">RetrieveAndGenerate response</a> – in the <code>s3Location</code> field</p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_InvokeAgent.html#API_agent-runtime_InvokeAgent_ResponseSyntax\">InvokeAgent response</a> – in the <code>s3Location</code> field</p> </li> </ul>"}, "RetrievalResultSalesforceLocation": {"type": "structure", "members": {"url": {"shape": "String", "documentation": "<p>The Salesforce host URL for the data source location.</p>"}}, "documentation": "<p>The Salesforce data source location.</p>"}, "RetrievalResultSharePointLocation": {"type": "structure", "members": {"url": {"shape": "String", "documentation": "<p>The SharePoint site URL for the data source location.</p>"}}, "documentation": "<p>The SharePoint data source location.</p>"}, "RetrievalResultSqlLocation": {"type": "structure", "members": {"query": {"shape": "String", "documentation": "<p>The SQL query used to retrieve the result.</p>"}}, "documentation": "<p>Contains information about the SQL query used to retrieve the result.</p>"}, "RetrievalResultWebLocation": {"type": "structure", "members": {"url": {"shape": "String", "documentation": "<p>The web URL/URLs for the data source location.</p>"}}, "documentation": "<p>The web URL/URLs data source location.</p>"}, "RetrieveAndGenerateConfiguration": {"type": "structure", "required": ["type"], "members": {"externalSourcesConfiguration": {"shape": "ExternalSourcesRetrieveAndGenerateConfiguration", "documentation": "<p>The configuration for the external source wrapper object in the <code>retrieveAndGenerate</code> function.</p>"}, "knowledgeBaseConfiguration": {"shape": "KnowledgeBaseRetrieveAndGenerateConfiguration", "documentation": "<p>Contains details about the knowledge base for retrieving information and generating responses.</p>"}, "type": {"shape": "RetrieveAndGenerateType", "documentation": "<p>The type of resource that contains your data for retrieving information and generating responses.</p> <note> <p>If you choose to use <code>EXTERNAL_SOURCES</code>, then currently only Anthropic Claude 3 Sonnet models for knowledge bases are supported.</p> </note>"}}, "documentation": "<p>Contains details about the resource being queried.</p> <p>This data type is used in the following API operations:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_RetrieveAndGenerate.html#API_agent-runtime_RetrieveAndGenerate_RequestSyntax\">RetrieveAndGenerate request</a> – in the <code>retrieveAndGenerateConfiguration</code> field</p> </li> </ul>"}, "RetrieveAndGenerateInput": {"type": "structure", "required": ["text"], "members": {"text": {"shape": "RetrieveAndGenerateInputTextString", "documentation": "<p>The query made to the knowledge base.</p>"}}, "documentation": "<p>Contains the query made to the knowledge base.</p> <p>This data type is used in the following API operations:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_RetrieveAndGenerate.html#API_agent-runtime_RetrieveAndGenerate_RequestSyntax\">RetrieveAndGenerate request</a> – in the <code>input</code> field</p> </li> </ul>", "sensitive": true}, "RetrieveAndGenerateInputTextString": {"type": "string", "max": 1000, "min": 0}, "RetrieveAndGenerateOutput": {"type": "structure", "required": ["text"], "members": {"text": {"shape": "String", "documentation": "<p>The response generated from querying the knowledge base.</p>"}}, "documentation": "<p>Contains the response generated from querying the knowledge base.</p> <p>This data type is used in the following API operations:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_RetrieveAndGenerate.html#API_agent-runtime_RetrieveAndGenerate_ResponseSyntax\">RetrieveAndGenerate response</a> – in the <code>output</code> field</p> </li> </ul>", "sensitive": true}, "RetrieveAndGenerateOutputEvent": {"type": "structure", "required": ["text"], "members": {"text": {"shape": "String", "documentation": "<p>A text response.</p>"}}, "documentation": "<p>A retrieve and generate output event.</p>", "event": true, "sensitive": true}, "RetrieveAndGenerateRequest": {"type": "structure", "required": ["input"], "members": {"input": {"shape": "RetrieveAndGenerateInput", "documentation": "<p>Contains the query to be made to the knowledge base.</p>"}, "retrieveAndGenerateConfiguration": {"shape": "RetrieveAndGenerateConfiguration", "documentation": "<p>Contains configurations for the knowledge base query and retrieval process. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/kb-test-config.html\">Query configurations</a>.</p>"}, "sessionConfiguration": {"shape": "RetrieveAndGenerateSessionConfiguration", "documentation": "<p>Contains details about the session with the knowledge base.</p>"}, "sessionId": {"shape": "SessionId", "documentation": "<p>The unique identifier of the session. When you first make a <code>RetrieveAndGenerate</code> request, Amazon Bedrock automatically generates this value. You must reuse this value for all subsequent requests in the same conversational session. This value allows Amazon Bedrock to maintain context and knowledge from previous interactions. You can't explicitly set the <code>sessionId</code> yourself.</p>"}}}, "RetrieveAndGenerateResponse": {"type": "structure", "required": ["output", "sessionId"], "members": {"citations": {"shape": "Citations", "documentation": "<p>A list of segments of the generated response that are based on sources in the knowledge base, alongside information about the sources.</p>"}, "guardrailAction": {"shape": "GuadrailAction", "documentation": "<p>Specifies if there is a guardrail intervention in the response.</p>"}, "output": {"shape": "RetrieveAndGenerateOutput", "documentation": "<p>Contains the response generated from querying the knowledge base.</p>"}, "sessionId": {"shape": "SessionId", "documentation": "<p>The unique identifier of the session. When you first make a <code>RetrieveAndGenerate</code> request, Amazon Bedrock automatically generates this value. You must reuse this value for all subsequent requests in the same conversational session. This value allows Amazon Bedrock to maintain context and knowledge from previous interactions. You can't explicitly set the <code>sessionId</code> yourself.</p>"}}}, "RetrieveAndGenerateSessionConfiguration": {"type": "structure", "required": ["kmsKeyArn"], "members": {"kmsKeyArn": {"shape": "KmsKeyArn", "documentation": "<p>The ARN of the KMS key encrypting the session.</p>"}}, "documentation": "<p>Contains configuration about the session with the knowledge base.</p> <p>This data type is used in the following API operations:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_RetrieveAndGenerate.html#API_agent-runtime_RetrieveAndGenerate_RequestSyntax\">RetrieveAndGenerate request</a> – in the <code>sessionConfiguration</code> field</p> </li> </ul>"}, "RetrieveAndGenerateStreamRequest": {"type": "structure", "required": ["input"], "members": {"input": {"shape": "RetrieveAndGenerateInput", "documentation": "<p>Contains the query to be made to the knowledge base.</p>"}, "retrieveAndGenerateConfiguration": {"shape": "RetrieveAndGenerateConfiguration", "documentation": "<p>Contains configurations for the knowledge base query and retrieval process. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/kb-test-config.html\">Query configurations</a>.</p>"}, "sessionConfiguration": {"shape": "RetrieveAndGenerateSessionConfiguration", "documentation": "<p>Contains details about the session with the knowledge base.</p>"}, "sessionId": {"shape": "SessionId", "documentation": "<p>The unique identifier of the session. When you first make a <code>RetrieveAndGenerate</code> request, Amazon Bedrock automatically generates this value. You must reuse this value for all subsequent requests in the same conversational session. This value allows Amazon Bedrock to maintain context and knowledge from previous interactions. You can't explicitly set the <code>sessionId</code> yourself.</p>"}}}, "RetrieveAndGenerateStreamResponse": {"type": "structure", "required": ["sessionId", "stream"], "members": {"sessionId": {"shape": "SessionId", "documentation": "<p>The session ID.</p>", "location": "header", "locationName": "x-amzn-bedrock-knowledge-base-session-id"}, "stream": {"shape": "RetrieveAndGenerateStreamResponseOutput", "documentation": "<p>A stream of events from the model.</p>"}}, "payload": "stream"}, "RetrieveAndGenerateStreamResponseOutput": {"type": "structure", "members": {"accessDeniedException": {"shape": "AccessDeniedException", "documentation": "<p>The request is denied because you do not have sufficient permissions to perform the requested action. For troubleshooting this error, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/troubleshooting-api-error-codes.html#ts-access-denied\">AccessDeniedException</a> in the Amazon Bedrock User Guide.</p>"}, "badGatewayException": {"shape": "BadGatewayException", "documentation": "<p>The request failed due to a bad gateway error.</p>"}, "citation": {"shape": "CitationEvent", "documentation": "<p>A citation event.</p>"}, "conflictException": {"shape": "ConflictException", "documentation": "<p>Error occurred because of a conflict while performing an operation.</p>"}, "dependencyFailedException": {"shape": "DependencyFailedException", "documentation": "<p>The request failed due to a dependency error.</p>"}, "guardrail": {"shape": "GuardrailEvent", "documentation": "<p>A guardrail event.</p>"}, "internalServerException": {"shape": "InternalServerException", "documentation": "<p>An internal server error occurred. Retry your request.</p>"}, "output": {"shape": "RetrieveAndGenerateOutputEvent", "documentation": "<p>An output event.</p>"}, "resourceNotFoundException": {"shape": "ResourceNotFoundException", "documentation": "<p>The specified resource ARN was not found. For troubleshooting this error, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/troubleshooting-api-error-codes.html#ts-resource-not-found\">ResourceNotFound</a> in the Amazon Bedrock User Guide.</p>"}, "serviceQuotaExceededException": {"shape": "ServiceQuotaExceededException", "documentation": "<p>Your request exceeds the service quota for your account. You can view your quotas at <a href=\"https://docs.aws.amazon.com/servicequotas/latest/userguide/gs-request-quota.html\">Viewing service quotas</a>. You can resubmit your request later.</p>"}, "throttlingException": {"shape": "ThrottlingException", "documentation": "<p>Your request was denied due to exceeding the account quotas for <i>Amazon Bedrock</i>. For troubleshooting this error, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/troubleshooting-api-error-codes.html#ts-throttling-exception\">ThrottlingException</a> in the Amazon Bedrock User Guide.</p>"}, "validationException": {"shape": "ValidationException", "documentation": "<p>The input fails to satisfy the constraints specified by <i>Amazon Bedrock</i>. For troubleshooting this error, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/troubleshooting-api-error-codes.html#ts-validation-error\">ValidationError</a> in the Amazon Bedrock User Guide.</p>"}}, "documentation": "<p>A retrieve and generate stream response output.</p>", "eventstream": true}, "RetrieveAndGenerateType": {"type": "string", "enum": ["KNOWLEDGE_BASE", "EXTERNAL_SOURCES"]}, "RetrieveRequest": {"type": "structure", "required": ["knowledgeBaseId", "retrieval<PERSON><PERSON>y"], "members": {"guardrailConfiguration": {"shape": "GuardrailConfiguration", "documentation": "<p>Guardrail settings.</p>"}, "knowledgeBaseId": {"shape": "KnowledgeBaseId", "documentation": "<p>The unique identifier of the knowledge base to query.</p>", "location": "uri", "locationName": "knowledgeBaseId"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If there are more results than can fit in the response, the response returns a <code>nextToken</code>. Use this token in the <code>nextToken</code> field of another request to retrieve the next batch of results.</p>"}, "retrievalConfiguration": {"shape": "KnowledgeBaseRetrievalConfiguration", "documentation": "<p>Contains configurations for the knowledge base query and retrieval process. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/kb-test-config.html\">Query configurations</a>.</p>"}, "retrievalQuery": {"shape": "KnowledgeBaseQuery", "documentation": "<p>Contains the query to send the knowledge base.</p>"}}}, "RetrieveResponse": {"type": "structure", "required": ["retrievalResults"], "members": {"guardrailAction": {"shape": "GuadrailAction", "documentation": "<p>Specifies if there is a guardrail intervention in the response.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>If there are more results than can fit in the response, the response returns a <code>nextToken</code>. Use this token in the <code>nextToken</code> field of another request to retrieve the next batch of results.</p>"}, "retrievalResults": {"shape": "KnowledgeBaseRetrievalResults", "documentation": "<p>A list of results from querying the knowledge base.</p>"}}}, "RetrievedReference": {"type": "structure", "members": {"content": {"shape": "RetrievalR<PERSON>ultContent", "documentation": "<p>Contains the cited text from the data source.</p>"}, "location": {"shape": "RetrievalResultLocation", "documentation": "<p>Contains information about the location of the data source.</p>"}, "metadata": {"shape": "RetrievalResultMetadata", "documentation": "<p>Contains metadata attributes and their values for the file in the data source. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/knowledge-base-ds.html#kb-ds-metadata\">Metadata and filtering</a>.</p>"}}, "documentation": "<p>Contains metadata about a source cited for the generated response.</p> <p>This data type is used in the following API operations:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_RetrieveAndGenerate.html#API_agent-runtime_RetrieveAndGenerate_ResponseSyntax\">RetrieveAndGenerate response</a> – in the <code>retrievedReferences</code> field</p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_InvokeAgent.html#API_agent-runtime_InvokeAgent_ResponseSyntax\">InvokeAgent response</a> – in the <code>retrievedReferences</code> field</p> </li> </ul>"}, "RetrievedReferences": {"type": "list", "member": {"shape": "RetrievedReference"}}, "ReturnControlInvocationResults": {"type": "list", "member": {"shape": "InvocationResultMember"}, "max": 5, "min": 1}, "ReturnControlPayload": {"type": "structure", "members": {"invocationId": {"shape": "String", "documentation": "<p>The identifier of the action group invocation.</p>"}, "invocationInputs": {"shape": "InvocationInputs", "documentation": "<p>A list of objects that contain information about the parameters and inputs that need to be sent into the API operation or function, based on what the agent determines from its session with the user.</p>"}}, "documentation": "<p>Contains information to return from the action group that the agent has predicted to invoke.</p> <p>This data type is used in the following API operations:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_InvokeAgent.html#API_agent-runtime_InvokeAgent_ResponseSyntax\">InvokeAgent response</a> </p> </li> </ul>", "event": true, "sensitive": true}, "ReturnControlResults": {"type": "structure", "members": {"invocationId": {"shape": "String", "documentation": "<p>The action's invocation ID.</p>"}, "returnControlInvocationResults": {"shape": "ReturnControlInvocationResults", "documentation": "<p>The action invocation result.</p>"}}, "documentation": "<p>An action invocation result.</p>"}, "RoutingClassifierModelInvocationOutput": {"type": "structure", "members": {"metadata": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>The invocation's metadata.</p>"}, "rawResponse": {"shape": "RawResponse", "documentation": "<p>The invocation's raw response.</p>"}, "traceId": {"shape": "TraceId", "documentation": "<p>The invocation's trace ID.</p>"}}, "documentation": "<p>Invocation output from a routing classifier model.</p>", "sensitive": true}, "RoutingClassifierTrace": {"type": "structure", "members": {"invocationInput": {"shape": "InvocationInput", "documentation": "<p>The classifier's invocation input.</p>"}, "modelInvocationInput": {"shape": "ModelInvocationInput", "documentation": "<p>The classifier's model invocation input.</p>"}, "modelInvocationOutput": {"shape": "RoutingClassifierModelInvocationOutput", "documentation": "<p>The classifier's model invocation output.</p>"}, "observation": {"shape": "Observation", "documentation": "<p>The classifier's observation.</p>"}}, "documentation": "<p>A trace for a routing classifier.</p>", "sensitive": true, "union": true}, "S3BucketName": {"type": "string", "max": 63, "min": 3, "pattern": "^[a-z0-9][\\.\\-a-z0-9]{1,61}[a-z0-9]$"}, "S3Identifier": {"type": "structure", "members": {"s3BucketName": {"shape": "S3BucketName", "documentation": "<p> The name of the S3 bucket. </p>"}, "s3ObjectKey": {"shape": "S3ObjectKey", "documentation": "<p> The S3 object key for the S3 resource. </p>"}}, "documentation": "<p> The identifier information for an Amazon S3 bucket. </p>"}, "S3Location": {"type": "structure", "required": ["uri"], "members": {"uri": {"shape": "S3Uri", "documentation": "<p>The path to the Amazon S3 bucket where the image is stored.</p>"}}, "documentation": "<p>Information about the Amazon S3 bucket where the image is stored.</p>"}, "S3ObjectDoc": {"type": "structure", "required": ["uri"], "members": {"uri": {"shape": "S3Uri", "documentation": "<p>The file location of the S3 wrapper object.</p>"}}, "documentation": "<p>The unique wrapper object of the document from the S3 location.</p>"}, "S3ObjectFile": {"type": "structure", "required": ["uri"], "members": {"uri": {"shape": "S3Uri", "documentation": "<p>The uri of the s3 object.</p>"}}, "documentation": "<p>Contains details of the s3 object where the source file is located.</p>"}, "S3ObjectKey": {"type": "string", "max": 1024, "min": 1, "pattern": "^[\\.\\-\\!\\*\\_\\'\\(\\)a-zA-Z0-9][\\.\\-\\!\\*\\_\\'\\(\\)\\/a-zA-Z0-9]*$"}, "S3Uri": {"type": "string", "max": 1024, "min": 1, "pattern": "^s3://[a-z0-9][a-z0-9.-]{1,61}[a-z0-9]/.{1,1024}$"}, "SatisfiedCondition": {"type": "structure", "required": ["conditionName"], "members": {"conditionName": {"shape": "String", "documentation": "<p>The name of the condition that was satisfied.</p>"}}, "documentation": "<p>Represents a condition that was satisfied during a condition node evaluation in a flow execution.</p> <note> <p>Flow executions is in preview release for Amazon Bedrock and is subject to change.</p> </note>", "sensitive": true}, "SatisfiedConditions": {"type": "list", "member": {"shape": "SatisfiedCondition"}, "max": 5, "min": 1}, "SearchType": {"type": "string", "enum": ["HYBRID", "SEMANTIC"]}, "ServiceQuotaExceededException": {"type": "structure", "members": {"message": {"shape": "NonBlankString"}}, "documentation": "<p>The number of requests exceeds the service quota. Resubmit your request later.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "SessionArn": {"type": "string", "pattern": "^arn:aws(-[^:]+)?:bedrock:[a-z0-9-]+:[0-9]{12}:session/[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$"}, "SessionAttributesMap": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "String"}}, "SessionId": {"type": "string", "max": 100, "min": 2, "pattern": "^[0-9a-zA-Z._:-]+$"}, "SessionIdentifier": {"type": "string", "pattern": "^(arn:aws(-[^:]+)?:bedrock:[a-z0-9-]+:[0-9]{12}:session/[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12})|([a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12})$"}, "SessionMetadataKey": {"type": "string", "max": 100, "min": 1}, "SessionMetadataMap": {"type": "map", "key": {"shape": "SessionMetadataKey"}, "value": {"shape": "SessionMetadataValue"}, "max": 50, "min": 0}, "SessionMetadataValue": {"type": "string", "max": 5000, "min": 0}, "SessionState": {"type": "structure", "members": {"conversationHistory": {"shape": "ConversationHistory", "documentation": "<p>The state's conversation history.</p>"}, "files": {"shape": "InputFiles", "documentation": "<p>Contains information about the files used by code interpreter.</p>"}, "invocationId": {"shape": "String", "documentation": "<p>The identifier of the invocation of an action. This value must match the <code>invocationId</code> returned in the <code>InvokeAgent</code> response for the action whose results are provided in the <code>returnControlInvocationResults</code> field. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/agents-returncontrol.html\">Return control to the agent developer</a> and <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/agents-session-state.html\">Control session context</a>.</p>"}, "knowledgeBaseConfigurations": {"shape": "KnowledgeBaseConfigurations", "documentation": "<p>An array of configurations, each of which applies to a knowledge base attached to the agent.</p>"}, "promptSessionAttributes": {"shape": "PromptSessionAttributesMap", "documentation": "<p>Contains attributes that persist across a prompt and the values of those attributes. </p> <ul> <li> <p>In orchestration prompt template, these attributes replace the $prompt_session_attributes$ placeholder variable. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/prompt-placeholders.html\">Prompt template placeholder variables</a>.</p> </li> <li> <p>In <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/agents-multi-agent-collaboration.html\">multi-agent collaboration</a>, the <code>promptSessionAttributes</code> will only be used by supervisor agent when $prompt_session_attributes$ is present in prompt template. </p> </li> </ul>"}, "returnControlInvocationResults": {"shape": "ReturnControlInvocationResults", "documentation": "<p>Contains information about the results from the action group invocation. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/agents-returncontrol.html\">Return control to the agent developer</a> and <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/agents-session-state.html\">Control session context</a>.</p> <note> <p>If you include this field, the <code>inputText</code> field will be ignored.</p> </note>"}, "sessionAttributes": {"shape": "SessionAttributesMap", "documentation": "<p>Contains attributes that persist across a session and the values of those attributes. If <code>sessionAttributes</code> are passed to a supervisor agent in <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/agents-multi-agent-collaboration.html\">multi-agent collaboration</a>, it will be forwarded to all agent collaborators.</p>"}}, "documentation": "<p>Contains parameters that specify various attributes that persist across a session or prompt. You can define session state attributes as key-value pairs when writing a <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/agents-lambda.html\">Lambda function</a> for an action group or pass them when making an <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_InvokeAgent.html\">InvokeAgent</a> request. Use session state attributes to control and provide conversational context for your agent and to help customize your agent's behavior. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/agents-session-state.html\">Control session context</a>.</p>"}, "SessionStatus": {"type": "string", "enum": ["ACTIVE", "EXPIRED", "ENDED"]}, "SessionSummaries": {"type": "list", "member": {"shape": "SessionSummary"}}, "SessionSummary": {"type": "structure", "required": ["createdAt", "lastUpdatedAt", "sessionArn", "sessionId", "sessionStatus"], "members": {"createdAt": {"shape": "DateTimestamp", "documentation": "<p>The timestamp for when the session was created.</p>"}, "lastUpdatedAt": {"shape": "DateTimestamp", "documentation": "<p>The timestamp for when the session was last modified.</p>"}, "sessionArn": {"shape": "SessionArn", "documentation": "<p>The Amazon Resource Name (ARN) of the session.</p>"}, "sessionId": {"shape": "<PERSON><PERSON>", "documentation": "<p>The unique identifier for the session.</p>"}, "sessionStatus": {"shape": "SessionStatus", "documentation": "<p>The current status of the session.</p>"}}, "documentation": "<p>Contains details about a session. For more information about sessions, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/sessions.html\">Store and retrieve conversation history and context with Amazon Bedrock sessions</a>.</p>"}, "SessionTTL": {"type": "integer", "box": true, "max": 3600, "min": 60}, "Source": {"type": "string", "enum": ["ACTION_GROUP", "KNOWLEDGE_BASE", "PARSER"], "sensitive": true}, "Span": {"type": "structure", "members": {"end": {"shape": "SpanEndInteger", "documentation": "<p>Where the text with a citation ends in the generated output.</p>"}, "start": {"shape": "SpanStartInteger", "documentation": "<p>Where the text with a citation starts in the generated output.</p>"}}, "documentation": "<p>Contains information about where the text with a citation begins and ends in the generated output.</p> <p>This data type is used in the following API operations:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_RetrieveAndGenerate.html#API_agent-runtime_RetrieveAndGenerate_ResponseSyntax\">RetrieveAndGenerate response</a> – in the <code>span</code> field</p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_InvokeAgent.html#API_agent-runtime_InvokeAgent_ResponseSyntax\">InvokeAgent response</a> – in the <code>span</code> field</p> </li> </ul>"}, "SpanEndInteger": {"type": "integer", "box": true, "min": 0}, "SpanStartInteger": {"type": "integer", "box": true, "min": 0}, "StartFlowExecutionRequest": {"type": "structure", "required": ["flowAliasIdentifier", "flowIdentifier", "inputs"], "members": {"flowAliasIdentifier": {"shape": "FlowAliasIdentifier", "documentation": "<p>The unique identifier of the flow alias to use for the flow execution.</p>", "location": "uri", "locationName": "flowAliasIdentifier"}, "flowExecutionName": {"shape": "FlowExecutionName", "documentation": "<p>The unique name for the flow execution. If you don't provide one, a system-generated name is used.</p>"}, "flowIdentifier": {"shape": "FlowIdentifier", "documentation": "<p>The unique identifier of the flow to execute.</p>", "location": "uri", "locationName": "flowIdentifier"}, "inputs": {"shape": "FlowInputs", "documentation": "<p>The input data required for the flow execution. This must match the input schema defined in the flow.</p>"}, "modelPerformanceConfiguration": {"shape": "ModelPerformanceConfiguration", "documentation": "<p>The performance settings for the foundation model used in the flow execution.</p>"}}}, "StartFlowExecutionResponse": {"type": "structure", "members": {"executionArn": {"shape": "FlowExecutionIdentifier", "documentation": "<p>The Amazon Resource Name (ARN) that uniquely identifies the flow execution.</p>"}}}, "StopFlowExecutionRequest": {"type": "structure", "required": ["executionIdentifier", "flowAliasIdentifier", "flowIdentifier"], "members": {"executionIdentifier": {"shape": "FlowExecutionIdentifier", "documentation": "<p>The unique identifier of the flow execution to stop.</p>", "location": "uri", "locationName": "executionIdentifier"}, "flowAliasIdentifier": {"shape": "FlowAliasIdentifier", "documentation": "<p>The unique identifier of the flow alias used for the execution.</p>", "location": "uri", "locationName": "flowAliasIdentifier"}, "flowIdentifier": {"shape": "FlowIdentifier", "documentation": "<p>The unique identifier of the flow.</p>", "location": "uri", "locationName": "flowIdentifier"}}}, "StopFlowExecutionResponse": {"type": "structure", "required": ["status"], "members": {"executionArn": {"shape": "FlowExecutionIdentifier", "documentation": "<p>The Amazon Resource Name (ARN) that uniquely identifies the flow execution that was stopped.</p>"}, "status": {"shape": "FlowExecutionStatus", "documentation": "<p>The updated status of the flow execution after the stop request. This will typically be ABORTED if the execution was successfully stopped.</p>"}}}, "StopSequences": {"type": "list", "member": {"shape": "String"}, "max": 4, "min": 0}, "StreamingConfigurations": {"type": "structure", "members": {"applyGuardrailInterval": {"shape": "StreamingConfigurationsApplyGuardrailIntervalInteger", "documentation": "<p> The guardrail interval to apply as response is generated. By default, the guardrail interval is set to 50 characters. If a larger interval is specified, the response will be generated in larger chunks with fewer <code>ApplyGuardrail</code> calls. The following examples show the response generated for <i>Hello, I am an agent</i> input string.</p> <p> <b>Example response in chunks: Interval set to 3 characters</b> </p> <p> <code>'Hel', 'lo, ','I am', ' an', ' Age', 'nt'</code> </p> <p>Each chunk has at least 3 characters except for the last chunk</p> <p> <b>Example response in chunks: Interval set to 20 or more characters</b> </p> <p> <code>Hello, I am an Agent</code> </p>"}, "streamFinalResponse": {"shape": "Boolean", "documentation": "<p> Specifies whether to enable streaming for the final response. This is set to <code>false</code> by default. </p>"}}, "documentation": "<p>Configurations for streaming.</p>"}, "StreamingConfigurationsApplyGuardrailIntervalInteger": {"type": "integer", "box": true, "min": 1}, "String": {"type": "string"}, "SummaryText": {"type": "string", "max": 25000000, "min": 0}, "SyntheticTimestamp_date_time": {"type": "timestamp", "timestampFormat": "iso8601"}, "TagKey": {"type": "string", "documentation": "<p>Key of a tag</p>", "max": 128, "min": 1, "pattern": "^[a-zA-Z0-9\\s._:/=+@-]*$"}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "documentation": "<p>List of Tag Keys</p>", "max": 200, "min": 1}, "TagResourceRequest": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {"shape": "TaggableResourcesArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource to tag.</p>", "location": "uri", "locationName": "resourceArn"}, "tags": {"shape": "TagsMap", "documentation": "<p>An object containing key-value pairs that define the tags to attach to the resource.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "documentation": "<p>Value of a tag</p>", "max": 256, "min": 0, "pattern": "^[a-zA-Z0-9\\s._:/=+@-]*$"}, "TaggableResourcesArn": {"type": "string", "documentation": "<p>ARN of Taggable resources: [session]</p>", "max": 1011, "min": 20, "pattern": "(^arn:aws(-[^:]+)?:bedrock:[a-zA-Z0-9-]+:[0-9]{12}:(session)/[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$)"}, "TagsMap": {"type": "map", "key": {"shape": "TagKey"}, "value": {"shape": "TagValue"}, "documentation": "<p>A map of tag keys and values</p>", "max": 200, "min": 1}, "Temperature": {"type": "float", "box": true, "max": 1, "min": 0}, "TextInferenceConfig": {"type": "structure", "members": {"maxTokens": {"shape": "MaxTokens", "documentation": "<p>The maximum number of tokens to generate in the output text. Do not use the minimum of 0 or the maximum of 65536. The limit values described here are arbitary values, for actual values consult the limits defined by your specific model.</p>"}, "stopSequences": {"shape": "RAGStopSequences", "documentation": "<p>A list of sequences of characters that, if generated, will cause the model to stop generating further tokens. Do not use a minimum length of 1 or a maximum length of 1000. The limit values described here are arbitary values, for actual values consult the limits defined by your specific model.</p>"}, "temperature": {"shape": "Temperature", "documentation": "<p> Controls the random-ness of text generated by the language model, influencing how much the model sticks to the most predictable next words versus exploring more surprising options. A lower temperature value (e.g. 0.2 or 0.3) makes model outputs more deterministic or predictable, while a higher temperature (e.g. 0.8 or 0.9) makes the outputs more creative or unpredictable. </p>"}, "topP": {"shape": "TopP", "documentation": "<p> A probability distribution threshold which controls what the model considers for the set of possible next tokens. The model will only consider the top p% of the probability distribution when generating the next token. </p>"}}, "documentation": "<p>Configuration settings for text generation using a language model via the RetrieveAndGenerate operation. Includes parameters like temperature, top-p, maximum token count, and stop sequences. </p> <note> <p>The valid range of <code>maxTokens</code> depends on the accepted values for your chosen model's inference parameters. To see the inference parameters for your model, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/model-parameters.html\">Inference parameters for foundation models.</a> </p> </note>"}, "TextPrompt": {"type": "structure", "required": ["text"], "members": {"text": {"shape": "TextPromptTextString", "documentation": "<p>The text in the text prompt to optimize.</p>"}}, "documentation": "<p>Contains information about the text prompt to optimize.</p>", "sensitive": true}, "TextPromptTemplate": {"type": "string", "max": 4000, "min": 1, "sensitive": true}, "TextPromptTextString": {"type": "string", "max": 200000, "min": 1}, "TextResponsePart": {"type": "structure", "members": {"span": {"shape": "Span", "documentation": "<p>Contains information about where the text with a citation begins and ends in the generated output.</p>"}, "text": {"shape": "String", "documentation": "<p>The part of the generated text that contains a citation.</p>"}}, "documentation": "<p>Contains the part of the generated text that contains a citation, alongside where it begins and ends.</p> <p>This data type is used in the following API operations:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_RetrieveAndGenerate.html#API_agent-runtime_RetrieveAndGenerate_ResponseSyntax\">RetrieveAndGenerate response</a> – in the <code>textResponsePart</code> field</p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_agent-runtime_InvokeAgent.html#API_agent-runtime_InvokeAgent_ResponseSyntax\">InvokeAgent response</a> – in the <code>textResponsePart</code> field</p> </li> </ul>", "sensitive": true}, "TextToSqlConfiguration": {"type": "structure", "required": ["type"], "members": {"knowledgeBaseConfiguration": {"shape": "TextToSqlKnowledgeBaseConfiguration", "documentation": "<p>Specifies configurations for a knowledge base to use in transformation.</p>"}, "type": {"shape": "TextToSqlConfigurationType", "documentation": "<p>The type of resource to use in transformation.</p>"}}, "documentation": "<p>Contains configurations for transforming text to SQL.</p>"}, "TextToSqlConfigurationType": {"type": "string", "enum": ["KNOWLEDGE_BASE"]}, "TextToSqlKnowledgeBaseConfiguration": {"type": "structure", "required": ["knowledgeBaseArn"], "members": {"knowledgeBaseArn": {"shape": "KnowledgeBaseArn", "documentation": "<p>The ARN of the knowledge base</p>"}}, "documentation": "<p>Contains configurations for a knowledge base to use in transformation.</p>"}, "ThrottlingException": {"type": "structure", "members": {"message": {"shape": "NonBlankString"}}, "documentation": "<p>The number of requests exceeds the limit. Resubmit your request later.</p>", "error": {"httpStatusCode": 429, "senderFault": true}, "exception": true}, "TopK": {"type": "integer", "box": true, "max": 500, "min": 0}, "TopP": {"type": "float", "box": true, "max": 1, "min": 0}, "Trace": {"type": "structure", "members": {"customOrchestrationTrace": {"shape": "CustomOrchestrationTrace", "documentation": "<p> Details about the custom orchestration step in which the agent determines the order in which actions are executed. </p>"}, "failureTrace": {"shape": "FailureTrace", "documentation": "<p>Contains information about the failure of the interaction.</p>"}, "guardrailTrace": {"shape": "GuardrailTrace", "documentation": "<p>The trace details for a trace defined in the Guardrail filter.</p>"}, "orchestrationTrace": {"shape": "OrchestrationTrace", "documentation": "<p>Details about the orchestration step, in which the agent determines the order in which actions are executed and which knowledge bases are retrieved.</p>"}, "postProcessingTrace": {"shape": "PostProcessingTrace", "documentation": "<p>Details about the post-processing step, in which the agent shapes the response..</p>"}, "preProcessingTrace": {"shape": "PreProcessingTrace", "documentation": "<p>Details about the pre-processing step, in which the agent contextualizes and categorizes user inputs.</p>"}, "routingClassifierTrace": {"shape": "RoutingClassifierTrace", "documentation": "<p>A routing classifier's trace.</p>"}}, "documentation": "<p>Contains one part of the agent's reasoning process and results from calling API actions and querying knowledge bases. You can use the trace to understand how the agent arrived at the response it provided the customer. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/agents-test.html#trace-enablement\">Trace enablement</a>.</p>", "sensitive": true, "union": true}, "TraceId": {"type": "string", "max": 16, "min": 2}, "TraceKnowledgeBaseId": {"type": "string", "sensitive": true}, "TracePart": {"type": "structure", "members": {"agentAliasId": {"shape": "AgentAliasId", "documentation": "<p>The unique identifier of the alias of the agent.</p>"}, "agentId": {"shape": "AgentId", "documentation": "<p>The unique identifier of the agent.</p>"}, "agentVersion": {"shape": "AgentVersion", "documentation": "<p>The version of the agent.</p>"}, "callerChain": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The part's caller chain.</p>"}, "collaboratorName": {"shape": "Name", "documentation": "<p>The part's collaborator name.</p>"}, "eventTime": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p> The time of the trace. </p>"}, "sessionId": {"shape": "SessionId", "documentation": "<p>The unique identifier of the session with the agent.</p>"}, "trace": {"shape": "Trace", "documentation": "<p>Contains one part of the agent's reasoning process and results from calling API actions and querying knowledge bases. You can use the trace to understand how the agent arrived at the response it provided the customer. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/agents-test.html#trace-enablement\">Trace enablement</a>.</p>"}}, "documentation": "<p>Contains information about the agent and session, alongside the agent's reasoning process and results from calling API actions and querying knowledge bases and metadata about the trace. You can use the trace to understand how the agent arrived at the response it provided the customer. For more information, see <a href=\"https://docs.aws.amazon.com/bedrock/latest/userguide/agents-test.html#trace-enablement\">Trace enablement</a>.</p>", "event": true, "sensitive": true}, "TransformationConfiguration": {"type": "structure", "required": ["mode"], "members": {"mode": {"shape": "QueryTransformationMode", "documentation": "<p>The mode of the transformation.</p>"}, "textToSqlConfiguration": {"shape": "TextToSqlConfiguration", "documentation": "<p>Specifies configurations for transforming text to SQL.</p>"}}, "documentation": "<p>Contains configurations for transforming the natural language query into SQL.</p>"}, "Type": {"type": "string", "enum": ["ACTION_GROUP", "AGENT_COLLABORATOR", "KNOWLEDGE_BASE", "FINISH", "ASK_USER", "REPROMPT"]}, "UntagResourceRequest": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {"shape": "TaggableResourcesArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource from which to remove tags.</p>", "location": "uri", "locationName": "resourceArn"}, "tagKeys": {"shape": "TagKeyList", "documentation": "<p>A list of keys of the tags to remove from the resource.</p>", "location": "querystring", "locationName": "tagKeys"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateSessionRequest": {"type": "structure", "required": ["sessionIdentifier"], "members": {"sessionIdentifier": {"shape": "SessionIdentifier", "documentation": "<p>The unique identifier of the session to modify. You can specify either the session's <code>sessionId</code> or its Amazon Resource Name (ARN).</p>", "location": "uri", "locationName": "sessionIdentifier"}, "sessionMetadata": {"shape": "SessionMetadataMap", "documentation": "<p>A map of key-value pairs containing attributes to be persisted across the session. For example the user's ID, their language preference, and the type of device they are using.</p>"}}}, "UpdateSessionResponse": {"type": "structure", "required": ["createdAt", "lastUpdatedAt", "sessionArn", "sessionId", "sessionStatus"], "members": {"createdAt": {"shape": "DateTimestamp", "documentation": "<p>The timestamp for when the session was created.</p>"}, "lastUpdatedAt": {"shape": "DateTimestamp", "documentation": "<p>The timestamp for when the session was last modified.</p>"}, "sessionArn": {"shape": "SessionArn", "documentation": "<p>The Amazon Resource Name (ARN) of the session that was updated.</p>"}, "sessionId": {"shape": "<PERSON><PERSON>", "documentation": "<p>The unique identifier of the session you updated.</p>"}, "sessionStatus": {"shape": "SessionStatus", "documentation": "<p>The status of the session you updated.</p>"}}}, "Usage": {"type": "structure", "members": {"inputTokens": {"shape": "Integer", "documentation": "<p>Contains information about the input tokens from the foundation model usage.</p>"}, "outputTokens": {"shape": "Integer", "documentation": "<p>Contains information about the output tokens from the foundation model usage.</p>"}}, "documentation": "<p>Contains information of the usage of the foundation model.</p>", "sensitive": true}, "Uuid": {"type": "string", "pattern": "^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$"}, "ValidationException": {"type": "structure", "members": {"message": {"shape": "NonBlankString"}}, "documentation": "<p>Input validation failed. Check your request parameters and retry the request.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "VectorSearchBedrockRerankingConfiguration": {"type": "structure", "required": ["modelConfiguration"], "members": {"metadataConfiguration": {"shape": "MetadataConfigurationForReranking", "documentation": "<p>Contains configurations for the metadata to use in reranking.</p>"}, "modelConfiguration": {"shape": "VectorSearchBedrockRerankingModelConfiguration", "documentation": "<p>Contains configurations for the reranker model.</p>"}, "numberOfRerankedResults": {"shape": "VectorSearchBedrockRerankingConfigurationNumberOfRerankedResultsInteger", "documentation": "<p>The number of results to return after reranking.</p>"}}, "documentation": "<p>Contains configurations for reranking with an Amazon Bedrock reranker model.</p>"}, "VectorSearchBedrockRerankingConfigurationNumberOfRerankedResultsInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "VectorSearchBedrockRerankingModelConfiguration": {"type": "structure", "required": ["modelArn"], "members": {"additionalModelRequestFields": {"shape": "AdditionalModelRequestFields", "documentation": "<p>A JSON object whose keys are request fields for the model and whose values are values for those fields.</p>"}, "modelArn": {"shape": "BedrockRerankingModelArn", "documentation": "<p>The ARN of the reranker model to use.</p>"}}, "documentation": "<p>Contains configurations for an Amazon Bedrock reranker model.</p>"}, "VectorSearchRerankingConfiguration": {"type": "structure", "required": ["type"], "members": {"bedrockRerankingConfiguration": {"shape": "VectorSearchBedrockRerankingConfiguration", "documentation": "<p>Contains configurations for an Amazon Bedrock reranker model.</p>"}, "type": {"shape": "VectorSearchRerankingConfigurationType", "documentation": "<p>The type of reranker model.</p>"}}, "documentation": "<p>Contains configurations for reranking the retrieved results.</p>"}, "VectorSearchRerankingConfigurationType": {"type": "string", "enum": ["BEDROCK_RERANKING_MODEL"]}, "Verb": {"type": "string", "sensitive": true}, "Version": {"type": "string", "max": 5, "min": 1, "pattern": "^(DRAFT|[0-9]{0,4}[1-9][0-9]{0,4})$"}}, "documentation": "<p>Contains APIs related to model invocation and querying of knowledge bases.</p>"}