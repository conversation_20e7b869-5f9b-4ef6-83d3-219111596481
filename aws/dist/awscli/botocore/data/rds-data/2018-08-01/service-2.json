{"version": "2.0", "metadata": {"apiVersion": "2018-08-01", "auth": ["aws.auth#sigv4"], "endpointPrefix": "rds-data", "protocol": "rest-json", "protocols": ["rest-json"], "serviceFullName": "AWS RDS DataService", "serviceId": "RDS Data", "signatureVersion": "v4", "signingName": "rds-data", "uid": "rds-data-2018-08-01"}, "operations": {"BatchExecuteStatement": {"name": "BatchExecuteStatement", "http": {"method": "POST", "requestUri": "/BatchExecute", "responseCode": 200}, "input": {"shape": "BatchExecuteStatementRequest"}, "output": {"shape": "BatchExecuteStatementResponse"}, "errors": [{"shape": "SecretsErrorException"}, {"shape": "HttpEndpointNotEnabledException"}, {"shape": "DatabaseErrorException"}, {"shape": "DatabaseResumingException"}, {"shape": "DatabaseUnavailableException"}, {"shape": "TransactionNotFoundException"}, {"shape": "InvalidSecretException"}, {"shape": "InvalidResourceStateException"}, {"shape": "ServiceUnavailableError"}, {"shape": "ForbiddenException"}, {"shape": "DatabaseNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "BadRequestException"}, {"shape": "StatementTimeoutException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Runs a batch SQL statement over an array of data.</p> <p>You can run bulk update and insert operations for multiple records using a DML statement with different parameter sets. Bulk operations can provide a significant performance improvement over individual insert and update operations.</p> <note> <p>If a call isn't part of a transaction because it doesn't include the <code>transactionID</code> parameter, changes that result from the call are committed automatically.</p> <p>There isn't a fixed upper limit on the number of parameter sets. However, the maximum size of the HTTP request submitted through the Data API is 4 MiB. If the request exceeds this limit, the Data API returns an error and doesn't process the request. This 4-MiB limit includes the size of the HTTP headers and the JSON notation in the request. Thus, the number of parameter sets that you can include depends on a combination of factors, such as the size of the SQL statement and the size of each parameter set.</p> <p>The response size limit is 1 MiB. If the call returns more than 1 MiB of response data, the call is terminated.</p> </note>"}, "BeginTransaction": {"name": "BeginTransaction", "http": {"method": "POST", "requestUri": "/BeginTransaction", "responseCode": 200}, "input": {"shape": "BeginTransactionRequest"}, "output": {"shape": "BeginTransactionResponse"}, "errors": [{"shape": "SecretsErrorException"}, {"shape": "HttpEndpointNotEnabledException"}, {"shape": "DatabaseErrorException"}, {"shape": "DatabaseResumingException"}, {"shape": "DatabaseUnavailableException"}, {"shape": "TransactionNotFoundException"}, {"shape": "InvalidSecretException"}, {"shape": "InvalidResourceStateException"}, {"shape": "ServiceUnavailableError"}, {"shape": "ForbiddenException"}, {"shape": "DatabaseNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "BadRequestException"}, {"shape": "StatementTimeoutException"}, {"shape": "InternalServerErrorException"}], "documentation": "<p>Starts a SQL transaction.</p> <note> <p>A transaction can run for a maximum of 24 hours. A transaction is terminated and rolled back automatically after 24 hours.</p> <p>A transaction times out if no calls use its transaction ID in three minutes. If a transaction times out before it's committed, it's rolled back automatically.</p> <p>For Aurora MySQL, DDL statements inside a transaction cause an implicit commit. We recommend that you run each MySQL DDL statement in a separate <code>ExecuteStatement</code> call with <code>continueAfterTimeout</code> enabled.</p> </note>"}, "CommitTransaction": {"name": "CommitTransaction", "http": {"method": "POST", "requestUri": "/CommitTransaction", "responseCode": 200}, "input": {"shape": "CommitTransactionRequest"}, "output": {"shape": "CommitTransactionResponse"}, "errors": [{"shape": "SecretsErrorException"}, {"shape": "HttpEndpointNotEnabledException"}, {"shape": "DatabaseErrorException"}, {"shape": "DatabaseUnavailableException"}, {"shape": "TransactionNotFoundException"}, {"shape": "InvalidSecretException"}, {"shape": "InvalidResourceStateException"}, {"shape": "ServiceUnavailableError"}, {"shape": "ForbiddenException"}, {"shape": "DatabaseNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "BadRequestException"}, {"shape": "StatementTimeoutException"}, {"shape": "InternalServerErrorException"}, {"shape": "NotFoundException"}], "documentation": "<p>Ends a SQL transaction started with the <code>BeginTransaction</code> operation and commits the changes.</p>"}, "ExecuteSql": {"name": "ExecuteSql", "http": {"method": "POST", "requestUri": "/ExecuteSql", "responseCode": 200}, "input": {"shape": "ExecuteSqlRequest"}, "output": {"shape": "ExecuteSqlResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "BadRequestException"}, {"shape": "InternalServerErrorException"}, {"shape": "ForbiddenException"}, {"shape": "ServiceUnavailableError"}], "documentation": "<p>Runs one or more SQL statements.</p> <note> <p>This operation isn't supported for Aurora Serverless v2 and provisioned DB clusters. For Aurora Serverless v1 DB clusters, the operation is deprecated. Use the <code>BatchExecuteStatement</code> or <code>ExecuteStatement</code> operation.</p> </note>", "deprecated": true, "deprecatedMessage": "The ExecuteSql API is deprecated, please use the ExecuteStatement API.", "deprecatedSince": "2019-03-21"}, "ExecuteStatement": {"name": "ExecuteStatement", "http": {"method": "POST", "requestUri": "/Execute", "responseCode": 200}, "input": {"shape": "ExecuteStatementRequest"}, "output": {"shape": "ExecuteStatementResponse"}, "errors": [{"shape": "SecretsErrorException"}, {"shape": "HttpEndpointNotEnabledException"}, {"shape": "DatabaseErrorException"}, {"shape": "DatabaseResumingException"}, {"shape": "DatabaseUnavailableException"}, {"shape": "TransactionNotFoundException"}, {"shape": "InvalidSecretException"}, {"shape": "InvalidResourceStateException"}, {"shape": "ServiceUnavailableError"}, {"shape": "ForbiddenException"}, {"shape": "DatabaseNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "BadRequestException"}, {"shape": "StatementTimeoutException"}, {"shape": "InternalServerErrorException"}, {"shape": "UnsupportedResultException"}], "documentation": "<p>Runs a SQL statement against a database.</p> <note> <p>If a call isn't part of a transaction because it doesn't include the <code>transactionID</code> parameter, changes that result from the call are committed automatically.</p> <p>If the binary response data from the database is more than 1 MB, the call is terminated.</p> </note>"}, "RollbackTransaction": {"name": "RollbackTransaction", "http": {"method": "POST", "requestUri": "/RollbackTransaction", "responseCode": 200}, "input": {"shape": "RollbackTransactionRequest"}, "output": {"shape": "RollbackTransactionResponse"}, "errors": [{"shape": "SecretsErrorException"}, {"shape": "HttpEndpointNotEnabledException"}, {"shape": "DatabaseErrorException"}, {"shape": "DatabaseUnavailableException"}, {"shape": "TransactionNotFoundException"}, {"shape": "InvalidSecretException"}, {"shape": "InvalidResourceStateException"}, {"shape": "ServiceUnavailableError"}, {"shape": "ForbiddenException"}, {"shape": "DatabaseNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "BadRequestException"}, {"shape": "StatementTimeoutException"}, {"shape": "InternalServerErrorException"}, {"shape": "NotFoundException"}], "documentation": "<p>Performs a rollback of a transaction. Rolling back a transaction cancels its changes.</p>"}}, "shapes": {"AccessDeniedException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>You don't have sufficient access to perform this action.</p>", "error": {"httpStatusCode": 403, "senderFault": true}, "exception": true}, "Arn": {"type": "string", "max": 100, "min": 11}, "ArrayOfArray": {"type": "list", "member": {"shape": "ArrayValue"}, "documentation": "<p>An array of arrays.</p> <note> <p>Some array entries can be null.</p> </note>"}, "ArrayValue": {"type": "structure", "members": {"booleanValues": {"shape": "BooleanArray", "documentation": "<p>An array of Boolean values.</p>"}, "longValues": {"shape": "LongArray", "documentation": "<p>An array of integers.</p>"}, "doubleValues": {"shape": "DoubleArray", "documentation": "<p>An array of floating-point numbers.</p>"}, "stringValues": {"shape": "StringArray", "documentation": "<p>An array of strings.</p>"}, "arrayValues": {"shape": "ArrayOfArray", "documentation": "<p>An array of arrays.</p>"}}, "documentation": "<p>Contains an array.</p>", "union": true}, "ArrayValueList": {"type": "list", "member": {"shape": "Value"}}, "BadRequestException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage", "documentation": "<p>The error message returned by this <code>BadRequestException</code> error.</p>"}}, "documentation": "<p>There is an error in the call or in a SQL statement. (This error only appears in calls from Aurora Serverless v1 databases.)</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "BatchExecuteStatementRequest": {"type": "structure", "required": ["resourceArn", "secretArn", "sql"], "members": {"resourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the Aurora Serverless DB cluster.</p>"}, "secretArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the secret that enables access to the DB cluster. Enter the database user name and password for the credentials in the secret.</p> <p>For information about creating the secret, see <a href=\"https://docs.aws.amazon.com/secretsmanager/latest/userguide/create_database_secret.html\">Create a database secret</a>.</p>"}, "sql": {"shape": "SqlStatement", "documentation": "<p>The SQL statement to run. Don't include a semicolon (;) at the end of the SQL statement.</p>"}, "database": {"shape": "DbName", "documentation": "<p>The name of the database.</p>"}, "schema": {"shape": "DbName", "documentation": "<p>The name of the database schema.</p> <note> <p>Currently, the <code>schema</code> parameter isn't supported.</p> </note>"}, "parameterSets": {"shape": "SqlParameterSets", "documentation": "<p>The parameter set for the batch operation.</p> <p>The SQL statement is executed as many times as the number of parameter sets provided. To execute a SQL statement with no parameters, use one of the following options:</p> <ul> <li> <p>Specify one or more empty parameter sets.</p> </li> <li> <p>Use the <code>ExecuteStatement</code> operation instead of the <code>BatchExecuteStatement</code> operation.</p> </li> </ul> <note> <p>Array parameters are not supported.</p> </note>"}, "transactionId": {"shape": "Id", "documentation": "<p>The identifier of a transaction that was started by using the <code>BeginTransaction</code> operation. Specify the transaction ID of the transaction that you want to include the SQL statement in.</p> <p>If the SQL statement is not part of a transaction, don't set this parameter.</p>"}}, "documentation": "<p>The request parameters represent the input of a SQL statement over an array of data.</p>"}, "BatchExecuteStatementResponse": {"type": "structure", "members": {"updateResults": {"shape": "UpdateResults", "documentation": "<p>The execution results of each batch entry.</p>"}}, "documentation": "<p>The response elements represent the output of a SQL statement over an array of data.</p>"}, "BeginTransactionRequest": {"type": "structure", "required": ["resourceArn", "secretArn"], "members": {"resourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the Aurora Serverless DB cluster.</p>"}, "secretArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The name or ARN of the secret that enables access to the DB cluster.</p>"}, "database": {"shape": "DbName", "documentation": "<p>The name of the database.</p>"}, "schema": {"shape": "DbName", "documentation": "<p>The name of the database schema.</p>"}}, "documentation": "<p>The request parameters represent the input of a request to start a SQL transaction.</p>"}, "BeginTransactionResponse": {"type": "structure", "members": {"transactionId": {"shape": "Id", "documentation": "<p>The transaction ID of the transaction started by the call.</p>"}}, "documentation": "<p>The response elements represent the output of a request to start a SQL transaction.</p>"}, "Blob": {"type": "blob"}, "Boolean": {"type": "boolean"}, "BooleanArray": {"type": "list", "member": {"shape": "BoxedBoolean"}, "documentation": "<p>An array of Boolean values.</p> <note> <p>Some array entries can be null.</p> </note>"}, "BoxedBoolean": {"type": "boolean", "box": true}, "BoxedDouble": {"type": "double", "box": true}, "BoxedFloat": {"type": "float", "box": true}, "BoxedInteger": {"type": "integer", "box": true}, "BoxedLong": {"type": "long", "box": true}, "ColumnMetadata": {"type": "structure", "members": {"name": {"shape": "String", "documentation": "<p>The name of the column.</p>"}, "type": {"shape": "Integer", "documentation": "<p>The type of the column.</p>"}, "typeName": {"shape": "String", "documentation": "<p>The database-specific data type of the column.</p>"}, "label": {"shape": "String", "documentation": "<p>The label for the column.</p>"}, "schemaName": {"shape": "String", "documentation": "<p>The name of the schema that owns the table that includes the column.</p>"}, "tableName": {"shape": "String", "documentation": "<p>The name of the table that includes the column.</p>"}, "isAutoIncrement": {"shape": "Boolean", "documentation": "<p>A value that indicates whether the column increments automatically.</p>"}, "isSigned": {"shape": "Boolean", "documentation": "<p>A value that indicates whether an integer column is signed.</p>"}, "isCurrency": {"shape": "Boolean", "documentation": "<p>A value that indicates whether the column contains currency values.</p>"}, "isCaseSensitive": {"shape": "Boolean", "documentation": "<p>A value that indicates whether the column is case-sensitive.</p>"}, "nullable": {"shape": "Integer", "documentation": "<p>A value that indicates whether the column is nullable.</p>"}, "precision": {"shape": "Integer", "documentation": "<p>The precision value of a decimal number column.</p>"}, "scale": {"shape": "Integer", "documentation": "<p>The scale value of a decimal number column.</p>"}, "arrayBaseColumnType": {"shape": "Integer", "documentation": "<p>The type of the column.</p>"}}, "documentation": "<p>Contains the metadata for a column.</p>"}, "CommitTransactionRequest": {"type": "structure", "required": ["resourceArn", "secretArn", "transactionId"], "members": {"resourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the Aurora Serverless DB cluster.</p>"}, "secretArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The name or ARN of the secret that enables access to the DB cluster.</p>"}, "transactionId": {"shape": "Id", "documentation": "<p>The identifier of the transaction to end and commit.</p>"}}, "documentation": "<p>The request parameters represent the input of a commit transaction request.</p>"}, "CommitTransactionResponse": {"type": "structure", "members": {"transactionStatus": {"shape": "TransactionStatus", "documentation": "<p>The status of the commit operation.</p>"}}, "documentation": "<p>The response elements represent the output of a commit transaction request.</p>"}, "DatabaseErrorException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>There was an error in processing the SQL statement.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "DatabaseNotFoundException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The DB cluster doesn't have a DB instance.</p>", "error": {"httpStatusCode": 404, "senderFault": true}, "exception": true}, "DatabaseResumingException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>A request was cancelled because the Aurora Serverless v2 DB instance was paused. The Data API request automatically resumes the DB instance. Wait a few seconds and try again.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "DatabaseUnavailableException": {"type": "structure", "members": {}, "documentation": "<p>The writer instance in the DB cluster isn't available.</p>", "error": {"httpStatusCode": 504}, "exception": true, "fault": true}, "DbName": {"type": "string", "max": 64, "min": 0}, "DecimalReturnType": {"type": "string", "enum": ["STRING", "DOUBLE_OR_LONG"]}, "DoubleArray": {"type": "list", "member": {"shape": "BoxedDouble"}, "documentation": "<p>An array of floating-point numbers.</p> <note> <p>Some array entries can be null.</p> </note>"}, "ErrorMessage": {"type": "string"}, "ExecuteSqlRequest": {"type": "structure", "required": ["dbClusterOrInstanceArn", "awsSecretStoreArn", "sqlStatements"], "members": {"dbClusterOrInstanceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the Aurora Serverless DB cluster.</p>"}, "awsSecretStoreArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the secret that enables access to the DB cluster. Enter the database user name and password for the credentials in the secret.</p> <p>For information about creating the secret, see <a href=\"https://docs.aws.amazon.com/secretsmanager/latest/userguide/create_database_secret.html\">Create a database secret</a>.</p>"}, "sqlStatements": {"shape": "SqlStatement", "documentation": "<p>One or more SQL statements to run on the DB cluster.</p> <p>You can separate SQL statements from each other with a semicolon (;). Any valid SQL statement is permitted, including data definition, data manipulation, and commit statements. </p>"}, "database": {"shape": "DbName", "documentation": "<p>The name of the database.</p>"}, "schema": {"shape": "DbName", "documentation": "<p>The name of the database schema.</p>"}}, "documentation": "<p>The request parameters represent the input of a request to run one or more SQL statements.</p>"}, "ExecuteSqlResponse": {"type": "structure", "members": {"sqlStatementResults": {"shape": "SqlStatementResults", "documentation": "<p>The results of the SQL statement or statements.</p>"}}, "documentation": "<p>The response elements represent the output of a request to run one or more SQL statements.</p>"}, "ExecuteStatementRequest": {"type": "structure", "required": ["resourceArn", "secretArn", "sql"], "members": {"resourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the Aurora Serverless DB cluster.</p>"}, "secretArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the secret that enables access to the DB cluster. Enter the database user name and password for the credentials in the secret.</p> <p>For information about creating the secret, see <a href=\"https://docs.aws.amazon.com/secretsmanager/latest/userguide/create_database_secret.html\">Create a database secret</a>.</p>"}, "sql": {"shape": "SqlStatement", "documentation": "<p>The SQL statement to run.</p>"}, "database": {"shape": "DbName", "documentation": "<p>The name of the database.</p>"}, "schema": {"shape": "DbName", "documentation": "<p>The name of the database schema.</p> <note> <p>Currently, the <code>schema</code> parameter isn't supported.</p> </note>"}, "parameters": {"shape": "SqlParametersList", "documentation": "<p>The parameters for the SQL statement.</p> <note> <p>Array parameters are not supported.</p> </note>"}, "transactionId": {"shape": "Id", "documentation": "<p>The identifier of a transaction that was started by using the <code>BeginTransaction</code> operation. Specify the transaction ID of the transaction that you want to include the SQL statement in.</p> <p>If the SQL statement is not part of a transaction, don't set this parameter.</p>"}, "includeResultMetadata": {"shape": "Boolean", "documentation": "<p>A value that indicates whether to include metadata in the results.</p>"}, "continueAfterTimeout": {"shape": "Boolean", "documentation": "<p>A value that indicates whether to continue running the statement after the call times out. By default, the statement stops running when the call times out.</p> <note> <p>For DDL statements, we recommend continuing to run the statement after the call times out. When a DDL statement terminates before it is finished running, it can result in errors and possibly corrupted data structures.</p> </note>"}, "resultSetOptions": {"shape": "ResultSetOptions", "documentation": "<p>Options that control how the result set is returned.</p>"}, "formatRecordsAs": {"shape": "RecordsFormatType", "documentation": "<p>A value that indicates whether to format the result set as a single JSON string. This parameter only applies to <code>SELECT</code> statements and is ignored for other types of statements. Allowed values are <code>NONE</code> and <code>JSON</code>. The default value is <code>NONE</code>. The result is returned in the <code>formattedRecords</code> field.</p> <p>For usage information about the JSON format for result sets, see <a href=\"https://docs.aws.amazon.com/AmazonRDS/latest/AuroraUserGuide/data-api.html\">Using the Data API</a> in the <i>Amazon Aurora User Guide</i>.</p>"}}, "documentation": "<p>The request parameters represent the input of a request to run a SQL statement against a database.</p>"}, "ExecuteStatementResponse": {"type": "structure", "members": {"records": {"shape": "SqlRecords", "documentation": "<p>The records returned by the SQL statement. This field is blank if the <code>formatRecordsAs</code> parameter is set to <code>JSON</code>.</p>"}, "columnMetadata": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>Metadata for the columns included in the results. This field is blank if the <code>formatRecordsAs</code> parameter is set to <code>JSON</code>.</p>"}, "numberOfRecordsUpdated": {"shape": "RecordsUpdated", "documentation": "<p>The number of records updated by the request.</p>"}, "generatedFields": {"shape": "FieldList", "documentation": "<p>Values for fields generated during a DML request.</p> <note> <p>The <code>generatedFields</code> data isn't supported by Aurora PostgreSQL. To get the values of generated fields, use the <code>RETURNING</code> clause. For more information, see <a href=\"https://www.postgresql.org/docs/10/dml-returning.html\">Returning Data From Modified Rows</a> in the PostgreSQL documentation.</p> </note>"}, "formattedRecords": {"shape": "FormattedSqlRecords", "documentation": "<p>A string value that represents the result set of a <code>SELECT</code> statement in JSON format. This value is only present when the <code>formatRecordsAs</code> parameter is set to <code>JSON</code>.</p> <p>The size limit for this field is currently 10 MB. If the JSON-formatted string representing the result set requires more than 10 MB, the call returns an error.</p>"}}, "documentation": "<p>The response elements represent the output of a request to run a SQL statement against a database.</p>"}, "Field": {"type": "structure", "members": {"isNull": {"shape": "BoxedBoolean", "documentation": "<p>A NULL value.</p>"}, "booleanValue": {"shape": "BoxedBoolean", "documentation": "<p>A value of Boolean data type.</p>"}, "longValue": {"shape": "BoxedLong", "documentation": "<p>A value of long data type.</p>"}, "doubleValue": {"shape": "BoxedDouble", "documentation": "<p>A value of double data type.</p>"}, "stringValue": {"shape": "String", "documentation": "<p>A value of string data type.</p>"}, "blobValue": {"shape": "Blob", "documentation": "<p>A value of BLOB data type.</p>"}, "arrayValue": {"shape": "ArrayValue", "documentation": "<p>An array of values.</p>"}}, "documentation": "<p>Contains a value.</p>", "union": true}, "FieldList": {"type": "list", "member": {"shape": "Field"}}, "ForbiddenException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage", "documentation": "<p>The error message returned by this <code>ForbiddenException</code> error.</p>"}}, "documentation": "<p>There are insufficient privileges to make the call.</p>", "error": {"httpStatusCode": 403, "senderFault": true}, "exception": true}, "FormattedSqlRecords": {"type": "string"}, "HttpEndpointNotEnabledException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The HTTP endpoint for using RDS Data API isn't enabled for the DB cluster.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "Id": {"type": "string", "max": 192, "min": 0}, "Integer": {"type": "integer"}, "InternalServerErrorException": {"type": "structure", "members": {}, "documentation": "<p>An internal error occurred.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true}, "InvalidResourceStateException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The resource is in an invalid state.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "InvalidSecretException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The Secrets Manager secret used with the request isn't valid.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "Long": {"type": "long"}, "LongArray": {"type": "list", "member": {"shape": "BoxedLong"}, "documentation": "<p>An array of integers.</p> <note> <p>Some array entries can be null.</p> </note>"}, "LongReturnType": {"type": "string", "enum": ["STRING", "LONG"]}, "Metadata": {"type": "list", "member": {"shape": "ColumnMetadata"}}, "NotFoundException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage", "documentation": "<p>The error message returned by this <code>NotFoundException</code> error.</p>"}}, "documentation": "<p>The <code>resourceArn</code>, <code>secretArn</code>, or <code>transactionId</code> value can't be found.</p>", "error": {"httpStatusCode": 404, "senderFault": true}, "exception": true}, "ParameterName": {"type": "string"}, "Record": {"type": "structure", "members": {"values": {"shape": "Row", "documentation": "<p>The values returned in the record.</p>"}}, "documentation": "<p>A record returned by a call.</p> <note> <p>This data structure is only used with the deprecated <code>ExecuteSql</code> operation. Use the <code>BatchExecuteStatement</code> or <code>ExecuteStatement</code> operation instead.</p> </note>"}, "Records": {"type": "list", "member": {"shape": "Record"}}, "RecordsFormatType": {"type": "string", "enum": ["NONE", "JSON"]}, "RecordsUpdated": {"type": "long"}, "ResultFrame": {"type": "structure", "members": {"resultSetMetadata": {"shape": "ResultSetMetadata", "documentation": "<p>The result-set metadata in the result set.</p>"}, "records": {"shape": "Records", "documentation": "<p>The records in the result set.</p>"}}, "documentation": "<p>The result set returned by a SQL statement.</p> <note> <p>This data structure is only used with the deprecated <code>ExecuteSql</code> operation. Use the <code>BatchExecuteStatement</code> or <code>ExecuteStatement</code> operation instead.</p> </note>"}, "ResultSetMetadata": {"type": "structure", "members": {"columnCount": {"shape": "<PERSON>", "documentation": "<p>The number of columns in the result set.</p>"}, "columnMetadata": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>The metadata of the columns in the result set.</p>"}}, "documentation": "<p>The metadata of the result set returned by a SQL statement.</p>"}, "ResultSetOptions": {"type": "structure", "members": {"decimalReturnType": {"shape": "DecimalReturnType", "documentation": "<p>A value that indicates how a field of <code>DECIMAL</code> type is represented in the response. The value of <code>STRING</code>, the default, specifies that it is converted to a String value. The value of <code>DOUBLE_OR_LONG</code> specifies that it is converted to a Long value if its scale is 0, or to a Double value otherwise.</p> <note> <p>Conversion to Double or Long can result in roundoff errors due to precision loss. We recommend converting to String, especially when working with currency values.</p> </note>"}, "longReturnType": {"shape": "LongReturnType", "documentation": "<p>A value that indicates how a field of <code>LONG</code> type is represented. Allowed values are <code>LONG</code> and <code>STRING</code>. The default is <code>LONG</code>. Specify <code>STRING</code> if the length or precision of numeric values might cause truncation or rounding errors. </p>"}}, "documentation": "<p>Options that control how the result set is returned.</p>"}, "RollbackTransactionRequest": {"type": "structure", "required": ["resourceArn", "secretArn", "transactionId"], "members": {"resourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the Aurora Serverless DB cluster.</p>"}, "secretArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The name or ARN of the secret that enables access to the DB cluster.</p>"}, "transactionId": {"shape": "Id", "documentation": "<p>The identifier of the transaction to roll back.</p>"}}, "documentation": "<p>The request parameters represent the input of a request to perform a rollback of a transaction.</p>"}, "RollbackTransactionResponse": {"type": "structure", "members": {"transactionStatus": {"shape": "TransactionStatus", "documentation": "<p>The status of the rollback operation.</p>"}}, "documentation": "<p>The response elements represent the output of a request to perform a rollback of a transaction.</p>"}, "Row": {"type": "list", "member": {"shape": "Value"}}, "SecretsErrorException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>There was a problem with the Secrets Manager secret used with the request, caused by one of the following conditions:</p> <ul> <li> <p>RDS Data API timed out retrieving the secret.</p> </li> <li> <p>The secret provided wasn't found.</p> </li> <li> <p>The secret couldn't be decrypted.</p> </li> </ul>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "ServiceUnavailableError": {"type": "structure", "members": {}, "documentation": "<p>The service specified by the <code>resourceArn</code> parameter isn't available.</p>", "error": {"httpStatusCode": 503}, "exception": true, "fault": true}, "SqlParameter": {"type": "structure", "members": {"name": {"shape": "ParameterName", "documentation": "<p>The name of the parameter.</p>"}, "value": {"shape": "Field", "documentation": "<p>The value of the parameter.</p>"}, "typeHint": {"shape": "TypeHint", "documentation": "<p>A hint that specifies the correct object type for data type mapping. Possible values are as follows:</p> <ul> <li> <p> <code>DATE</code> - The corresponding <code>String</code> parameter value is sent as an object of <code>DATE</code> type to the database. The accepted format is <code>YYYY-MM-DD</code>.</p> </li> <li> <p> <code>DECIMAL</code> - The corresponding <code>String</code> parameter value is sent as an object of <code>DECIMAL</code> type to the database.</p> </li> <li> <p> <code>JSON</code> - The corresponding <code>String</code> parameter value is sent as an object of <code>JSON</code> type to the database.</p> </li> <li> <p> <code>TIME</code> - The corresponding <code>String</code> parameter value is sent as an object of <code>TIME</code> type to the database. The accepted format is <code>HH:MM:SS[.FFF]</code>.</p> </li> <li> <p> <code>TIMESTAMP</code> - The corresponding <code>String</code> parameter value is sent as an object of <code>TIMESTAMP</code> type to the database. The accepted format is <code>YYYY-MM-DD HH:MM:SS[.FFF]</code>.</p> </li> <li> <p> <code>UUID</code> - The corresponding <code>String</code> parameter value is sent as an object of <code>UUID</code> type to the database. </p> </li> </ul>"}}, "documentation": "<p>A parameter used in a SQL statement.</p>"}, "SqlParameterSets": {"type": "list", "member": {"shape": "SqlParametersList"}}, "SqlParametersList": {"type": "list", "member": {"shape": "SqlParameter"}}, "SqlRecords": {"type": "list", "member": {"shape": "FieldList"}}, "SqlStatement": {"type": "string", "max": 65536, "min": 0}, "SqlStatementResult": {"type": "structure", "members": {"resultFrame": {"shape": "ResultFrame", "documentation": "<p>The result set of the SQL statement.</p>"}, "numberOfRecordsUpdated": {"shape": "RecordsUpdated", "documentation": "<p>The number of records updated by a SQL statement.</p>"}}, "documentation": "<p>The result of a SQL statement.</p> <note> <p>This data structure is only used with the deprecated <code>ExecuteSql</code> operation. Use the <code>BatchExecuteStatement</code> or <code>ExecuteStatement</code> operation instead.</p> </note>"}, "SqlStatementResults": {"type": "list", "member": {"shape": "SqlStatementResult"}}, "StatementTimeoutException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage", "documentation": "<p>The error message returned by this <code>StatementTimeoutException</code> error.</p>"}, "dbConnectionId": {"shape": "<PERSON>", "documentation": "<p>The database connection ID that executed the SQL statement.</p>"}}, "documentation": "<p>The execution of the SQL statement timed out.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "String": {"type": "string"}, "StringArray": {"type": "list", "member": {"shape": "String"}, "documentation": "<p>An array of strings.</p> <note> <p>Some array entries can be null.</p> </note>"}, "StructValue": {"type": "structure", "members": {"attributes": {"shape": "ArrayValueList", "documentation": "<p>The attributes returned in the record.</p>"}}, "documentation": "<p>A structure value returned by a call.</p> <note> <p>This data structure is only used with the deprecated <code>ExecuteSql</code> operation. Use the <code>BatchExecuteStatement</code> or <code>ExecuteStatement</code> operation instead.</p> </note>"}, "TransactionNotFoundException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The transaction ID wasn't found.</p>", "error": {"httpStatusCode": 404, "senderFault": true}, "exception": true}, "TransactionStatus": {"type": "string", "max": 128, "min": 0}, "TypeHint": {"type": "string", "enum": ["JSON", "UUID", "TIMESTAMP", "DATE", "TIME", "DECIMAL"]}, "UnsupportedResultException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>There was a problem with the result because of one of the following conditions:</p> <ul> <li> <p>It contained an unsupported data type.</p> </li> <li> <p>It contained a multidimensional array.</p> </li> <li> <p>The size was too large.</p> </li> </ul>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "UpdateResult": {"type": "structure", "members": {"generatedFields": {"shape": "FieldList", "documentation": "<p>Values for fields generated during the request.</p>"}}, "documentation": "<p>The response elements represent the results of an update.</p>"}, "UpdateResults": {"type": "list", "member": {"shape": "UpdateResult"}}, "Value": {"type": "structure", "members": {"isNull": {"shape": "BoxedBoolean", "documentation": "<p>A NULL value.</p>"}, "bitValue": {"shape": "BoxedBoolean", "documentation": "<p>A value for a column of BIT data type.</p>"}, "bigIntValue": {"shape": "BoxedLong", "documentation": "<p>A value for a column of big integer data type.</p>"}, "intValue": {"shape": "BoxedInteger", "documentation": "<p>A value for a column of integer data type.</p>"}, "doubleValue": {"shape": "BoxedDouble", "documentation": "<p>A value for a column of double data type.</p>"}, "realValue": {"shape": "BoxedFloat", "documentation": "<p>A value for a column of real data type.</p>"}, "stringValue": {"shape": "String", "documentation": "<p>A value for a column of string data type.</p>"}, "blobValue": {"shape": "Blob", "documentation": "<p>A value for a column of BLOB data type.</p>"}, "arrayValues": {"shape": "ArrayValueList", "documentation": "<p>An array of column values.</p>"}, "structValue": {"shape": "StructValue", "documentation": "<p>A value for a column of STRUCT data type.</p>"}}, "documentation": "<p>Contains the value of a column.</p> <note> <p>This data structure is only used with the deprecated <code>ExecuteSql</code> operation. Use the <code>BatchExecuteStatement</code> or <code>ExecuteStatement</code> operation instead.</p> </note>", "union": true}}, "documentation": "<p><fullname>RDS Data API</fullname> <p>Amazon RDS provides an HTTP endpoint to run SQL statements on an Amazon Aurora DB cluster. To run these statements, you use the RDS Data API (Data API).</p> <p>Data API is available with the following types of Aurora databases:</p> <ul> <li> <p>Aurora PostgreSQL - Serverless v2, provisioned, and Serverless v1</p> </li> <li> <p>Aurora MySQL - Serverless v2, provisioned, and Serverless v1</p> </li> </ul> <p>For more information about the Data API, see <a href=\"https://docs.aws.amazon.com/AmazonRDS/latest/AuroraUserGuide/data-api.html\">Using RDS Data API</a> in the <i>Amazon Aurora User Guide</i>.</p></p>"}