{"version": "2.0", "metadata": {"apiVersion": "2020-11-19", "auth": ["aws.auth#sigv4"], "endpointPrefix": "geo-maps", "protocol": "rest-json", "protocols": ["rest-json"], "serviceFullName": "Amazon Location Service Maps V2", "serviceId": "Geo Maps", "signatureVersion": "v4", "signingName": "geo-maps", "uid": "geo-maps-2020-11-19"}, "operations": {"GetGlyphs": {"name": "GetGlyphs", "http": {"method": "GET", "requestUri": "/glyphs/{FontStack}/{FontUnicodeRange}", "responseCode": 200}, "input": {"shape": "GetGlyphsRequest"}, "output": {"shape": "GetGlyphsResponse"}, "documentation": "<p> <code>GetGlyphs</code> returns the map's glyphs.</p>"}, "GetSprites": {"name": "GetSprites", "http": {"method": "GET", "requestUri": "/styles/{Style}/{ColorScheme}/{Variant}/sprites/{FileName}", "responseCode": 200}, "input": {"shape": "GetSpritesRequest"}, "output": {"shape": "GetSpritesResponse"}, "documentation": "<p> <code>GetSprites</code> returns the map's sprites.</p>"}, "GetStaticMap": {"name": "GetStaticMap", "http": {"method": "GET", "requestUri": "/static/{FileName}", "responseCode": 200}, "input": {"shape": "GetStaticMapRequest"}, "output": {"shape": "GetStaticMapResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}], "documentation": "<p> <code>GetStaticMap</code> provides high-quality static map images with customizable options. You can modify the map's appearance and overlay additional information. It's an ideal solution for applications requiring tailored static map snapshots.</p>"}, "GetStyleDescriptor": {"name": "GetStyleDescriptor", "http": {"method": "GET", "requestUri": "/styles/{Style}/descriptor", "responseCode": 200}, "input": {"shape": "GetStyleDescriptorRequest"}, "output": {"shape": "GetStyleDescriptorResponse"}, "documentation": "<p> <code>GetStyleDescriptor</code> returns information about the style.</p>"}, "GetTile": {"name": "GetTile", "http": {"method": "GET", "requestUri": "/tiles/{Tileset}/{Z}/{X}/{Y}", "responseCode": 200}, "input": {"shape": "GetTileRequest"}, "output": {"shape": "GetTileResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}], "documentation": "<p> <code>GetTile</code> returns a tile. Map tiles are used by clients to render a map. they're addressed using a grid arrangement with an X coordinate, Y coordinate, and Z (zoom) level.</p>"}}, "shapes": {"AccessDeniedException": {"type": "structure", "required": ["Message"], "members": {"Message": {"shape": "String", "locationName": "message"}}, "documentation": "<p>The request was denied because of insufficient access or permissions. Check with an administrator to verify your permissions.</p>", "error": {"httpStatusCode": 403, "senderFault": true}, "exception": true}, "ApiKey": {"type": "string", "max": 1000, "min": 0, "sensitive": true}, "Blob": {"type": "blob"}, "Boolean": {"type": "boolean", "box": true}, "ColorScheme": {"type": "string", "enum": ["Light", "Dark"]}, "CompactOverlay": {"type": "string", "max": 7000, "min": 1}, "CountryCode": {"type": "string", "max": 3, "min": 2, "pattern": "([A-Z]{2}|[A-Z]{3})"}, "DistanceMeters": {"type": "long", "max": 4294967295, "min": 0}, "GeoJsonOverlay": {"type": "string", "max": 7000, "min": 1}, "GetGlyphsRequest": {"type": "structure", "required": ["FontStack", "FontUnicodeRange"], "members": {"FontStack": {"shape": "GetGlyphsRequestFontStackString", "documentation": "<p>Name of the <code>FontStack</code> to retrieve. </p> <p>Example: <code>Amazon Ember Bold,Noto Sans Bold</code>.</p> <p>The supported font stacks are as follows:</p> <ul> <li> <p>Amazon Ember Bold</p> </li> <li> <p>Amazon Ember Bold Italic</p> </li> <li> <p>Amazon Ember Bold,Noto Sans Bold</p> </li> <li> <p>Amazon Ember Bold,Noto Sans Bold,Noto Sans Arabic Bold</p> </li> <li> <p>Amazon Ember Condensed RC BdItalic</p> </li> <li> <p>Amazon Ember Condensed RC Bold</p> </li> <li> <p>Amazon Ember Condensed RC Bold Italic</p> </li> <li> <p>Amazon Ember Condensed RC Bold,Noto Sans Bold</p> </li> <li> <p>Amazon Ember Condensed RC Bold,Noto Sans Bold,Noto Sans Arabic Condensed Bold</p> </li> <li> <p>Amazon Ember Condensed RC Light</p> </li> <li> <p>Amazon Ember Condensed RC Light Italic</p> </li> <li> <p>Amazon Ember Condensed RC LtItalic</p> </li> <li> <p>Amazon Ember Condensed RC Regular</p> </li> <li> <p>Amazon Ember Condensed RC Regular Italic</p> </li> <li> <p>Amazon Ember Condensed RC Regular,Noto Sans Regular</p> </li> <li> <p>Amazon Ember Condensed RC Regular,Noto Sans Regular,Noto Sans Arabic Condensed Regular</p> </li> <li> <p>Amazon Ember Condensed RC RgItalic</p> </li> <li> <p>Amazon Ember Condensed RC ThItalic</p> </li> <li> <p>Amazon Ember Condensed RC Thin</p> </li> <li> <p>Amazon Ember Condensed RC Thin Italic</p> </li> <li> <p>Amazon Ember Heavy</p> </li> <li> <p>Amazon Ember Heavy Italic</p> </li> <li> <p>Amazon Ember Light</p> </li> <li> <p>Amazon Ember Light Italic</p> </li> <li> <p>Amazon Ember Medium</p> </li> <li> <p>Amazon Ember Medium Italic</p> </li> <li> <p>Amazon Ember Medium,Noto Sans Medium</p> </li> <li> <p>Amazon Ember Medium,Noto Sans Medium,Noto Sans Arabic Medium</p> </li> <li> <p>Amazon Ember Regular</p> </li> <li> <p>Amazon Ember Regular Italic</p> </li> <li> <p>Amazon Ember Regular Italic,Noto Sans Italic</p> </li> <li> <p>Amazon Ember Regular Italic,Noto Sans Italic,Noto Sans Arabic Regular</p> </li> <li> <p>Amazon Ember Regular,Noto Sans Regular</p> </li> <li> <p>Amazon Ember Regular,Noto Sans Regular,Noto Sans Arabic Regular</p> </li> <li> <p>Amazon Ember Thin</p> </li> <li> <p>Amazon Ember Thin Italic</p> </li> <li> <p>AmazonEmberCdRC_Bd</p> </li> <li> <p>AmazonEmberCdRC_BdIt</p> </li> <li> <p>AmazonEmberCdRC_Lt</p> </li> <li> <p>AmazonEmberCdRC_LtIt</p> </li> <li> <p>AmazonEmberCdRC_Rg</p> </li> <li> <p>AmazonEmberCdRC_RgIt</p> </li> <li> <p>AmazonEmberCdRC_Th</p> </li> <li> <p>AmazonEmberCdRC_ThIt</p> </li> <li> <p>AmazonEmber_Bd</p> </li> <li> <p>AmazonEmber_BdIt</p> </li> <li> <p>AmazonEmber_He</p> </li> <li> <p>AmazonEmber_HeIt</p> </li> <li> <p>AmazonEmber_Lt</p> </li> <li> <p>AmazonEmber_LtIt</p> </li> <li> <p>AmazonEmber_Md</p> </li> <li> <p>AmazonEmber_MdIt</p> </li> <li> <p>AmazonEmber_Rg</p> </li> <li> <p>AmazonEmber_RgIt</p> </li> <li> <p>AmazonEmber_Th</p> </li> <li> <p>AmazonEmber_ThIt</p> </li> <li> <p>Noto Sans Black</p> </li> <li> <p>Noto Sans Black Italic</p> </li> <li> <p>Noto Sans Bold</p> </li> <li> <p>Noto Sans Bold Italic</p> </li> <li> <p>Noto Sans Extra Bold</p> </li> <li> <p>Noto Sans Extra Bold Italic</p> </li> <li> <p>Noto Sans Extra Light</p> </li> <li> <p>Noto Sans Extra Light Italic</p> </li> <li> <p>Noto Sans Italic</p> </li> <li> <p>Noto Sans Light</p> </li> <li> <p>Noto Sans Light Italic</p> </li> <li> <p>Noto Sans Medium</p> </li> <li> <p>Noto Sans Medium Italic</p> </li> <li> <p>Noto Sans Regular</p> </li> <li> <p>Noto Sans Semi Bold</p> </li> <li> <p>Noto Sans Semi Bold Italic</p> </li> <li> <p>Noto Sans Thin</p> </li> <li> <p>Noto Sans Thin Italic</p> </li> <li> <p>NotoSans-Bold</p> </li> <li> <p>NotoSans-Italic</p> </li> <li> <p>NotoSans-Medium</p> </li> <li> <p>NotoSans-Regular</p> </li> <li> <p>Open Sans Regular,Arial Unicode MS Regular</p> </li> </ul>", "location": "uri", "locationName": "FontStack"}, "FontUnicodeRange": {"shape": "GetGlyphsRequestFontUnicodeRangeString", "documentation": "<p>A Unicode range of characters to download glyphs for. This must be aligned to multiples of 256. </p> <p>Example: <code>0-255.pdf</code> </p>", "location": "uri", "locationName": "FontUnicodeRange"}}}, "GetGlyphsRequestFontStackString": {"type": "string", "max": 1000, "min": 0}, "GetGlyphsRequestFontUnicodeRangeString": {"type": "string", "pattern": "[0-9]+-[0-9]+\\.pbf"}, "GetGlyphsResponse": {"type": "structure", "members": {"Blob": {"shape": "Blob", "documentation": "<p>The Glyph, as a binary blob.</p>"}, "ContentType": {"shape": "String", "documentation": "<p>Header that represents the format of the response. The response returns the following as the HTTP body.</p>", "location": "header", "locationName": "Content-Type"}, "CacheControl": {"shape": "String", "documentation": "<p>Header that instructs caching configuration for the client.</p>", "location": "header", "locationName": "Cache-Control"}, "ETag": {"shape": "String", "documentation": "<p>The glyph's Etag.</p>", "location": "header", "locationName": "ETag"}}, "payload": "Blob"}, "GetSpritesRequest": {"type": "structure", "required": ["FileName", "Style", "ColorScheme", "<PERSON><PERSON><PERSON>"], "members": {"FileName": {"shape": "GetSpritesRequestFileNameString", "documentation": "<p> <code>Sprites</code> API: The name of the sprite ﬁle to retrieve, following pattern <code>sprites(@2x)?\\.(png|json)</code>.</p> <p>Example: <code>sprites.png</code> </p>", "location": "uri", "locationName": "FileName"}, "Style": {"shape": "MapStyle", "documentation": "<p>Style specifies the desired map style for the <code>Sprites</code> APIs.</p>", "location": "uri", "locationName": "Style"}, "ColorScheme": {"shape": "ColorScheme", "documentation": "<p>Sets color tone for map such as dark and light for specific map styles. It applies to only vector map styles such as Standard and Monochrome.</p> <p>Example: <code>Light</code> </p> <p>Default value: <code>Light</code> </p> <note> <p>Valid values for ColorScheme are case sensitive.</p> </note>", "location": "uri", "locationName": "ColorScheme"}, "Variant": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>Optimizes map styles for specific use case or industry. You can choose allowed variant only with Standard map style.</p> <p>Example: <code>Default</code> </p> <note> <p>Valid values for Variant are case sensitive.</p> </note>", "location": "uri", "locationName": "<PERSON><PERSON><PERSON>"}}}, "GetSpritesRequestFileNameString": {"type": "string", "pattern": "sprites(@2x)?\\.(png|json)"}, "GetSpritesResponse": {"type": "structure", "members": {"Blob": {"shape": "Blob", "documentation": "<p>The body of the sprite sheet or JSON offset file (image/png or application/json, depending on input).</p>"}, "ContentType": {"shape": "String", "documentation": "<p>Header that represents the format of the response. The response returns the following as the HTTP body.</p>", "location": "header", "locationName": "Content-Type"}, "CacheControl": {"shape": "String", "documentation": "<p>Header that instructs caching configuration for the client.</p>", "location": "header", "locationName": "Cache-Control"}, "ETag": {"shape": "String", "documentation": "<p>The sprite's Etag.</p>", "location": "header", "locationName": "ETag"}}, "payload": "Blob"}, "GetStaticMapRequest": {"type": "structure", "required": ["Height", "FileName", "<PERSON><PERSON><PERSON>"], "members": {"BoundingBox": {"shape": "PositionListString", "documentation": "<p>Takes in two pairs of coordinates, [Lon, Lat], denoting south-westerly and north-easterly edges of the image. The underlying area becomes the view of the image. </p> <p>Example: -123.17075,49.26959,-123.08125,49.31429</p>", "location": "querystring", "locationName": "bounding-box"}, "BoundedPositions": {"shape": "PositionListString", "documentation": "<p>Takes in two or more pair of coordinates, [Lon, Lat], with each coordinate separated by a comma. The API will generate an image to encompass all of the provided coordinates. </p> <note> <p>Cannot be used with <code>Zoom</code> and or <code>Radius</code> </p> </note> <p>Example: 97.170451,78.039098,99.045536,27.176178</p>", "location": "querystring", "locationName": "bounded-positions"}, "Center": {"shape": "PositionString", "documentation": "<p>Takes in a pair of coordinates, [Lon, Lat], which becomes the center point of the image. This parameter requires that either zoom or radius is set.</p> <note> <p>Cannot be used with <code>Zoom</code> and or <code>Radius</code> </p> </note> <p>Example: 49.295,-123.108</p>", "location": "querystring", "locationName": "center"}, "ColorScheme": {"shape": "ColorScheme", "documentation": "<p>Sets color tone for map, such as dark and light for specific map styles. It only applies to vector map styles, such as Standard.</p> <p>Example: <code>Light</code> </p> <p>Default value: <code>Light</code> </p> <note> <p>Valid values for <code>ColorScheme</code> are case sensitive.</p> </note>", "location": "querystring", "locationName": "color-scheme"}, "CompactOverlay": {"shape": "CompactOverlay", "documentation": "<p>Takes in a string to draw geometries on the image. The input is a comma separated format as follows format: <code>[Lon, Lat]</code> </p> <p>Example: <code>line:-122.407653,37.798557,-122.413291,37.802443;color=%23DD0000;width=7;outline-color=#00DD00;outline-width=5yd|point:-122.40572,37.80004;label=Fog Hill Market;size=large;text-color=%23DD0000;color=#EE4B2B</code> </p> <note> <p>Currently it supports the following geometry types: point, line and polygon. It does not support multiPoint , multiLine and multiPolgyon.</p> </note>", "location": "querystring", "locationName": "compact-overlay"}, "CropLabels": {"shape": "Boolean", "documentation": "<p>It is a flag that takes in true or false. It prevents the labels that are on the edge of the image from being cut or obscured.</p>", "location": "querystring", "locationName": "crop-labels"}, "GeoJsonOverlay": {"shape": "GeoJsonOverlay", "documentation": "<p>Takes in a string to draw geometries on the image. The input is a valid GeoJSON collection object. </p> <p>Example: <code>{\"type\":\"FeatureCollection\",\"features\": [{\"type\":\"Feature\",\"geometry\":{\"type\":\"MultiPoint\",\"coordinates\": [[-90.076345,51.504107],[-0.074451,51.506892]]},\"properties\": {\"color\":\"#00DD00\"}}]}</code> </p>", "location": "querystring", "locationName": "geojson-overlay"}, "Height": {"shape": "GetStaticMapRequestHeightInteger", "documentation": "<p>Specifies the height of the map image.</p>", "location": "querystring", "locationName": "height"}, "Key": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>Optional: The API key to be used for authorization. Either an API key or valid SigV4 signature must be provided when making a request. </p>", "location": "querystring", "locationName": "key"}, "LabelSize": {"shape": "LabelSize", "documentation": "<p>Overrides the label size auto-calculated by <code>FileName</code>. Takes in one of the values - <code>Small</code> or <code>Large</code>.</p>", "location": "querystring", "locationName": "label-size"}, "Language": {"shape": "LanguageTag", "documentation": "<p>Specifies the language on the map labels using the BCP 47 language tag, limited to ISO 639-1 two-letter language codes. If the specified language data isn't available for the map image, the labels will default to the regional primary language.</p> <p>Supported codes:</p> <ul> <li> <p> <code>ar</code> </p> </li> <li> <p> <code>as</code> </p> </li> <li> <p> <code>az</code> </p> </li> <li> <p> <code>be</code> </p> </li> <li> <p> <code>bg</code> </p> </li> <li> <p> <code>bn</code> </p> </li> <li> <p> <code>bs</code> </p> </li> <li> <p> <code>ca</code> </p> </li> <li> <p> <code>cs</code> </p> </li> <li> <p> <code>cy</code> </p> </li> <li> <p> <code>da</code> </p> </li> <li> <p> <code>de</code> </p> </li> <li> <p> <code>el</code> </p> </li> <li> <p> <code>en</code> </p> </li> <li> <p> <code>es</code> </p> </li> <li> <p> <code>et</code> </p> </li> <li> <p> <code>eu</code> </p> </li> <li> <p> <code>fi</code> </p> </li> <li> <p> <code>fo</code> </p> </li> <li> <p> <code>fr</code> </p> </li> <li> <p> <code>ga</code> </p> </li> <li> <p> <code>gl</code> </p> </li> <li> <p> <code>gn</code> </p> </li> <li> <p> <code>gu</code> </p> </li> <li> <p> <code>he</code> </p> </li> <li> <p> <code>hi</code> </p> </li> <li> <p> <code>hr</code> </p> </li> <li> <p> <code>hu</code> </p> </li> <li> <p> <code>hy</code> </p> </li> <li> <p> <code>id</code> </p> </li> <li> <p> <code>is</code> </p> </li> <li> <p> <code>it</code> </p> </li> <li> <p> <code>ja</code> </p> </li> <li> <p> <code>ka</code> </p> </li> <li> <p> <code>kk</code> </p> </li> <li> <p> <code>km</code> </p> </li> <li> <p> <code>kn</code> </p> </li> <li> <p> <code>ko</code> </p> </li> <li> <p> <code>ky</code> </p> </li> <li> <p> <code>lt</code> </p> </li> <li> <p> <code>lv</code> </p> </li> <li> <p> <code>mk</code> </p> </li> <li> <p> <code>ml</code> </p> </li> <li> <p> <code>mr</code> </p> </li> <li> <p> <code>ms</code> </p> </li> <li> <p> <code>mt</code> </p> </li> <li> <p> <code>my</code> </p> </li> <li> <p> <code>nl</code> </p> </li> <li> <p> <code>no</code> </p> </li> <li> <p> <code>or</code> </p> </li> <li> <p> <code>pa</code> </p> </li> <li> <p> <code>pl</code> </p> </li> <li> <p> <code>pt</code> </p> </li> <li> <p> <code>ro</code> </p> </li> <li> <p> <code>ru</code> </p> </li> <li> <p> <code>sk</code> </p> </li> <li> <p> <code>sl</code> </p> </li> <li> <p> <code>sq</code> </p> </li> <li> <p> <code>sr</code> </p> </li> <li> <p> <code>sv</code> </p> </li> <li> <p> <code>ta</code> </p> </li> <li> <p> <code>te</code> </p> </li> <li> <p> <code>th</code> </p> </li> <li> <p> <code>tr</code> </p> </li> <li> <p> <code>uk</code> </p> </li> <li> <p> <code>uz</code> </p> </li> <li> <p> <code>vi</code> </p> </li> <li> <p> <code>zh</code> </p> </li> </ul>", "location": "querystring", "locationName": "lang"}, "Padding": {"shape": "Integer", "documentation": "<p>Applies additional space (in pixels) around overlay feature to prevent them from being cut or obscured.</p> <note> <p>Value for max and min is determined by:</p> <p>Min: <code>1</code> </p> <p>Max: <code>min(height, width)/4</code> </p> </note> <p>Example: <code>100</code> </p>", "location": "querystring", "locationName": "padding"}, "PoliticalView": {"shape": "CountryCode", "documentation": "<p>Specifies the political view, using ISO 3166-2 or ISO 3166-3 country code format.</p> <p>The following political views are currently supported:</p> <ul> <li> <p> <code>ARG</code>: Argentina's view on the Southern Patagonian Ice Field and Tierra Del Fuego, including the Falkland Islands, South Georgia, and South Sandwich Islands</p> </li> <li> <p> <code>EGY</code>: Egypt's view on Bir Tawil</p> </li> <li> <p> <code>IND</code>: India's view on Gilgit-Baltistan</p> </li> <li> <p> <code>KEN</code>: Kenya's view on the Ilemi Triangle</p> </li> <li> <p> <code>MAR</code>: Morocco's view on Western Sahara</p> </li> <li> <p> <code>RUS</code>: Russia's view on Crimea</p> </li> <li> <p> <code>SDN</code>: Sudan's view on the Halaib Triangle</p> </li> <li> <p> <code>SRB</code>: Serbia's view on Kosovo, Vukovar, and Sarengrad Islands</p> </li> <li> <p> <code>SUR</code>: Suriname's view on the Courantyne Headwaters and Lawa Headwaters</p> </li> <li> <p> <code>SYR</code>: Syria's view on the Golan Heights</p> </li> <li> <p> <code>TUR</code>: Turkey's view on Cyprus and Northern Cyprus</p> </li> <li> <p> <code>TZA</code>: Tanzania's view on Lake Malawi</p> </li> <li> <p> <code>URY</code>: Uruguay's view on Rincon de Artigas</p> </li> <li> <p> <code>VNM</code>: Vietnam's view on the Paracel Islands and Spratly Islands</p> </li> </ul>", "location": "querystring", "locationName": "political-view"}, "PointsOfInterests": {"shape": "MapFeatureMode", "documentation": "<p>Determines if the result image will display icons representing points of interest on the map.</p>", "location": "querystring", "locationName": "pois"}, "Radius": {"shape": "DistanceMeters", "documentation": "<p>Used with center parameter, it specifies the zoom of the image where you can control it on a granular level. Takes in any value <code>&gt;= 1</code>. </p> <p>Example: <code>1500</code> </p> <note> <p>Cannot be used with <code>Zoom</code>.</p> </note> <p> <b>Unit</b>: <code>Meters</code> </p> <p/>", "box": true, "location": "querystring", "locationName": "radius"}, "FileName": {"shape": "GetStaticMapRequestFileNameString", "documentation": "<p>The map scaling parameter to size the image, icons, and labels. It follows the pattern of <code>^map(@2x)?$</code>.</p> <p>Example: <code>map, map@2x</code> </p>", "location": "uri", "locationName": "FileName"}, "ScaleBarUnit": {"shape": "ScaleBarUnit", "documentation": "<p>Displays a scale on the bottom right of the map image with the unit specified in the input. </p> <p>Example: <code>KilometersMiles, Miles, Kilometers, MilesKilometers</code> </p>", "location": "querystring", "locationName": "scale-unit"}, "Style": {"shape": "StaticMapStyle", "documentation": "<p> <code>Style</code> specifies the desired map style.</p>", "location": "querystring", "locationName": "style"}, "Width": {"shape": "GetStaticMapRequestWidthInteger", "documentation": "<p>Specifies the width of the map image.</p>", "location": "querystring", "locationName": "width"}, "Zoom": {"shape": "GetStaticMapRequestZoomFloat", "documentation": "<p>Specifies the zoom level of the map image.</p> <note> <p>Cannot be used with <code>Radius</code>.</p> </note>", "location": "querystring", "locationName": "zoom"}}}, "GetStaticMapRequestFileNameString": {"type": "string", "pattern": "map(@2x)?"}, "GetStaticMapRequestHeightInteger": {"type": "integer", "box": true, "max": 1400, "min": 64}, "GetStaticMapRequestWidthInteger": {"type": "integer", "box": true, "max": 1400, "min": 64}, "GetStaticMapRequestZoomFloat": {"type": "float", "box": true, "max": 20, "min": 0}, "GetStaticMapResponse": {"type": "structure", "required": ["PricingBucket"], "members": {"Blob": {"shape": "Blob", "documentation": "<p>The blob represents a map image as a <code>jpeg</code> for the <code>GetStaticMap</code> API.</p>"}, "ContentType": {"shape": "String", "documentation": "<p>Header that represents the format of the response. The response returns the following as the HTTP body.</p>", "location": "header", "locationName": "Content-Type"}, "CacheControl": {"shape": "String", "documentation": "<p>Header that instructs caching configuration for the client.</p>", "location": "header", "locationName": "Cache-Control"}, "ETag": {"shape": "String", "documentation": "<p>The static map's Etag.</p>", "location": "header", "locationName": "ETag"}, "PricingBucket": {"shape": "String", "documentation": "<p>The pricing bucket for which the request is charged at.</p>", "location": "header", "locationName": "x-amz-geo-pricing-bucket"}}, "payload": "Blob"}, "GetStyleDescriptorRequest": {"type": "structure", "required": ["Style"], "members": {"Style": {"shape": "MapStyle", "documentation": "<p>Style specifies the desired map style.</p>", "location": "uri", "locationName": "Style"}, "ColorScheme": {"shape": "ColorScheme", "documentation": "<p>Sets color tone for map such as dark and light for specific map styles. It applies to only vector map styles such as Standard and Monochrome.</p> <p>Example: <code>Light</code> </p> <p>Default value: <code>Light</code> </p> <note> <p>Valid values for ColorScheme are case sensitive.</p> </note>", "location": "querystring", "locationName": "color-scheme"}, "PoliticalView": {"shape": "CountryCode", "documentation": "<p>Specifies the political view using ISO 3166-2 or ISO 3166-3 country code format.</p> <p>The following political views are currently supported:</p> <ul> <li> <p> <code>ARG</code>: Argentina's view on the Southern Patagonian Ice Field and Tierra Del Fuego, including the Falkland Islands, South Georgia, and South Sandwich Islands</p> </li> <li> <p> <code>EGY</code>: Egypt's view on Bir Tawil</p> </li> <li> <p> <code>IND</code>: India's view on Gilgit-Baltistan</p> </li> <li> <p> <code>KEN</code>: Kenya's view on the Ilemi Triangle</p> </li> <li> <p> <code>MAR</code>: Morocco's view on Western Sahara</p> </li> <li> <p> <code>RUS</code>: Russia's view on Crimea</p> </li> <li> <p> <code>SDN</code>: Sudan's view on the Halaib Triangle</p> </li> <li> <p> <code>SRB</code>: Serbia's view on Kosovo, Vukovar, and Sarengrad Islands</p> </li> <li> <p> <code>SUR</code>: Suriname's view on the Courantyne Headwaters and Lawa Headwaters</p> </li> <li> <p> <code>SYR</code>: Syria's view on the Golan Heights</p> </li> <li> <p> <code>TUR</code>: Turkey's view on Cyprus and Northern Cyprus</p> </li> <li> <p> <code>TZA</code>: Tanzania's view on Lake Malawi</p> </li> <li> <p> <code>URY</code>: Uruguay's view on Rincon de Artigas</p> </li> <li> <p> <code>VNM</code>: Vietnam's view on the Paracel Islands and Spratly Islands</p> </li> </ul>", "location": "querystring", "locationName": "political-view"}, "Key": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>Optional: The API key to be used for authorization. Either an API key or valid SigV4 signature must be provided when making a request. </p>", "location": "querystring", "locationName": "key"}}}, "GetStyleDescriptorResponse": {"type": "structure", "members": {"Blob": {"shape": "Blob", "documentation": "<p>This Blob contains the body of the style descriptor which is in application/json format.</p>"}, "ContentType": {"shape": "String", "documentation": "<p>Header that represents the format of the response. The response returns the following as the HTTP body.</p>", "location": "header", "locationName": "Content-Type"}, "CacheControl": {"shape": "String", "documentation": "<p>Header that instructs caching configuration for the client.</p>", "location": "header", "locationName": "Cache-Control"}, "ETag": {"shape": "String", "documentation": "<p>The style descriptor's Etag.</p>", "location": "header", "locationName": "ETag"}}, "payload": "Blob"}, "GetTileRequest": {"type": "structure", "required": ["Tileset", "Z", "X", "Y"], "members": {"Tileset": {"shape": "Tileset", "documentation": "<p>Specifies the desired tile set.</p> <p>Valid Values: <code>raster.satellite | vector.basemap</code> </p>", "location": "uri", "locationName": "Tileset"}, "Z": {"shape": "GetTileRequestZString", "documentation": "<p>The zoom value for the map tile.</p>", "location": "uri", "locationName": "Z"}, "X": {"shape": "GetTileRequestXString", "documentation": "<p>The X axis value for the map tile. Must be between 0 and 19.</p>", "location": "uri", "locationName": "X"}, "Y": {"shape": "GetTileRequestYString", "documentation": "<p>The Y axis value for the map tile.</p>", "location": "uri", "locationName": "Y"}, "Key": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>Optional: The API key to be used for authorization. Either an API key or valid SigV4 signature must be provided when making a request. </p>", "location": "querystring", "locationName": "key"}}}, "GetTileRequestXString": {"type": "string", "pattern": ".*\\d+.*"}, "GetTileRequestYString": {"type": "string", "pattern": ".*\\d+.*"}, "GetTileRequestZString": {"type": "string", "pattern": ".*\\d+.*"}, "GetTileResponse": {"type": "structure", "required": ["PricingBucket"], "members": {"Blob": {"shape": "Blob", "documentation": "<p>The blob represents a vector tile in <code>mvt</code> or a raster tile in an image format.</p>"}, "ContentType": {"shape": "String", "documentation": "<p>Header that represents the format of the response. The response returns the following as the HTTP body.</p>", "location": "header", "locationName": "Content-Type"}, "CacheControl": {"shape": "String", "documentation": "<p>Header that instructs caching configuration for the client.</p>", "location": "header", "locationName": "Cache-Control"}, "ETag": {"shape": "String", "documentation": "<p>The pricing bucket for which the request is charged at.</p>", "location": "header", "locationName": "ETag"}, "PricingBucket": {"shape": "String", "documentation": "<p>The pricing bucket for which the request is charged at.</p>", "location": "header", "locationName": "x-amz-geo-pricing-bucket"}}, "payload": "Blob"}, "Integer": {"type": "integer", "box": true}, "InternalServerException": {"type": "structure", "required": ["Message"], "members": {"Message": {"shape": "String", "locationName": "message"}}, "documentation": "<p>The request processing has failed because of an unknown error, exception or failure.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true, "retryable": {"throttling": false}}, "LabelSize": {"type": "string", "enum": ["Small", "Large"]}, "LanguageTag": {"type": "string", "max": 35, "min": 2}, "MapFeatureMode": {"type": "string", "enum": ["Enabled", "Disabled"]}, "MapStyle": {"type": "string", "enum": ["Standard", "Monochrome", "Hybrid", "Satellite"]}, "PositionListString": {"type": "string", "min": 7, "pattern": "(-?\\d{1,3}(\\.\\d{1,14})?,-?\\d{1,2}(\\.\\d{1,14})?)(,(-?\\d{1,3}(\\.\\d{1,14})?,-?\\d{1,2}(\\.\\d{1,14})?))*"}, "PositionString": {"type": "string", "max": 36, "min": 3, "pattern": "-?\\d{1,3}(\\.\\d{1,14})?,-?\\d{1,2}(\\.\\d{1,14})?"}, "ScaleBarUnit": {"type": "string", "enum": ["Kilometers", "KilometersMiles", "<PERSON>", "MilesKilometers"]}, "StaticMapStyle": {"type": "string", "enum": ["Satellite", "Standard"]}, "String": {"type": "string"}, "ThrottlingException": {"type": "structure", "required": ["Message"], "members": {"Message": {"shape": "String", "locationName": "message"}}, "documentation": "<p>The request was denied due to request throttling.</p>", "error": {"httpStatusCode": 429, "senderFault": true}, "exception": true, "retryable": {"throttling": false}}, "Tileset": {"type": "string", "max": 100, "min": 1, "pattern": "[-.\\w]+"}, "ValidationException": {"type": "structure", "required": ["Message", "Reason", "FieldList"], "members": {"Message": {"shape": "String", "locationName": "message"}, "Reason": {"shape": "ValidationExceptionReason", "documentation": "<p>The field where the invalid entry was detected.</p>", "locationName": "reason"}, "FieldList": {"shape": "ValidationExceptionFieldList", "documentation": "<p>A message with the reason for the validation exception error.</p>", "locationName": "fieldList"}}, "documentation": "<p>The input fails to satisfy the constraints specified by an AWS service.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "ValidationExceptionField": {"type": "structure", "required": ["Name", "Message"], "members": {"Name": {"shape": "String", "documentation": "<p>The name of the resource.</p>", "locationName": "name"}, "Message": {"shape": "String", "documentation": "<p>The error message.</p>", "locationName": "message"}}, "documentation": "<p>The input fails to satisfy the constraints specified by the Amazon Location service.</p>"}, "ValidationExceptionFieldList": {"type": "list", "member": {"shape": "ValidationExceptionField"}}, "ValidationExceptionReason": {"type": "string", "enum": ["UnknownOperation", "Missing", "CannotParse", "FieldValidationFailed", "Other", "<PERSON><PERSON><PERSON>"]}, "Variant": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>"]}}, "documentation": "<p> Integrate high-quality base map data into your applications using <a href=\"https://maplibre.org\">MapLibre</a>. Capabilities include: </p> <ul> <li> <p>Access to comprehensive base map data, allowing you to tailor the map display to your specific needs.</p> </li> <li> <p>Multiple pre-designed map styles suited for various application types, such as navigation, logistics, or data visualization.</p> </li> <li> <p>Generation of static map images for scenarios where interactive maps aren't suitable, such as:</p> <ul> <li> <p>Embedding in emails or documents</p> </li> <li> <p>Displaying in low-bandwidth environments</p> </li> <li> <p>Creating printable maps</p> </li> <li> <p>Enhancing application performance by reducing client-side rendering</p> </li> </ul> </li> </ul>"}