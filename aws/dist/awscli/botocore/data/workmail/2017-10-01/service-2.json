{"version": "2.0", "metadata": {"apiVersion": "2017-10-01", "endpointPrefix": "workmail", "jsonVersion": "1.1", "protocol": "json", "protocols": ["json"], "serviceFullName": "Amazon WorkMail", "serviceId": "WorkMail", "signatureVersion": "v4", "targetPrefix": "WorkMailService", "uid": "workmail-2017-10-01", "auth": ["aws.auth#sigv4"]}, "operations": {"AssociateDelegateToResource": {"name": "AssociateDelegateToResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AssociateDelegateToResourceRequest"}, "output": {"shape": "AssociateDelegateToResourceResponse"}, "errors": [{"shape": "EntityNotFoundException"}, {"shape": "EntityStateException"}, {"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}, {"shape": "UnsupportedOperationException"}], "documentation": "<p>Adds a member (user or group) to the resource's set of delegates.</p>", "idempotent": true}, "AssociateMemberToGroup": {"name": "AssociateMemberToGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AssociateMemberToGroupRequest"}, "output": {"shape": "AssociateMemberToGroupResponse"}, "errors": [{"shape": "DirectoryServiceAuthenticationFailedException"}, {"shape": "DirectoryUnavailableException"}, {"shape": "EntityNotFoundException"}, {"shape": "EntityStateException"}, {"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}, {"shape": "UnsupportedOperationException"}], "documentation": "<p>Adds a member (user or group) to the group's set.</p>", "idempotent": true}, "AssumeImpersonationRole": {"name": "AssumeImpersonationRole", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AssumeImpersonationRoleRequest"}, "output": {"shape": "AssumeImpersonationRoleResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Assumes an impersonation role for the given WorkMail organization. This method returns an authentication token you can use to make impersonated calls.</p>"}, "CancelMailboxExportJob": {"name": "CancelMailboxExportJob", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CancelMailboxExportJobRequest"}, "output": {"shape": "CancelMailboxExportJobResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}, {"shape": "EntityNotFoundException"}], "documentation": "<p>Cancels a mailbox export job.</p> <note> <p>If the mailbox export job is near completion, it might not be possible to cancel it.</p> </note>", "idempotent": true}, "CreateAlias": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateAliasRequest"}, "output": {"shape": "CreateAliasResponse"}, "errors": [{"shape": "EmailAddressInUseException"}, {"shape": "EntityNotFoundException"}, {"shape": "EntityStateException"}, {"shape": "InvalidParameterException"}, {"shape": "MailDomainNotFoundException"}, {"shape": "MailDomainStateException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Adds an alias to the set of a given member (user or group) of WorkMail.</p>", "idempotent": true}, "CreateAvailabilityConfiguration": {"name": "CreateAvailabilityConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateAvailabilityConfigurationRequest"}, "output": {"shape": "CreateAvailabilityConfigurationResponse"}, "errors": [{"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}, {"shape": "NameAvailabilityException"}, {"shape": "InvalidParameterException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Creates an <code>AvailabilityConfiguration</code> for the given WorkMail organization and domain.</p>", "idempotent": true}, "CreateGroup": {"name": "CreateGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateGroupRequest"}, "output": {"shape": "CreateGroupResponse"}, "errors": [{"shape": "DirectoryServiceAuthenticationFailedException"}, {"shape": "DirectoryUnavailableException"}, {"shape": "InvalidParameterException"}, {"shape": "NameAvailabilityException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}, {"shape": "ReservedNameException"}, {"shape": "UnsupportedOperationException"}], "documentation": "<p>Creates a group that can be used in WorkMail by calling the <a>RegisterToWorkMail</a> operation.</p>", "idempotent": true}, "CreateIdentityCenterApplication": {"name": "CreateIdentityCenterApplication", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateIdentityCenterApplicationRequest"}, "output": {"shape": "CreateIdentityCenterApplicationResponse"}, "errors": [{"shape": "InvalidParameterException"}], "documentation": "<p> Creates the WorkMail application in IAM Identity Center that can be used later in the WorkMail - IdC integration. For more information, see PutIdentityProviderConfiguration. This action does not affect the authentication settings for any WorkMail organizations. </p>", "idempotent": true}, "CreateImpersonationRole": {"name": "CreateImpersonationRole", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateImpersonationRoleRequest"}, "output": {"shape": "CreateImpersonationRoleResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}, {"shape": "EntityNotFoundException"}, {"shape": "EntityStateException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Creates an impersonation role for the given WorkMail organization.</p> <p> <i>Idempotency</i> ensures that an API request completes no more than one time. With an idempotent request, if the original request completes successfully, any subsequent retries also complete successfully without performing any further actions.</p>"}, "CreateMobileDeviceAccessRule": {"name": "CreateMobileDeviceAccessRule", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateMobileDeviceAccessRuleRequest"}, "output": {"shape": "CreateMobileDeviceAccessRuleResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "LimitExceededException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}], "documentation": "<p>Creates a new mobile device access rule for the specified WorkMail organization.</p>"}, "CreateOrganization": {"name": "CreateOrganization", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateOrganizationRequest"}, "output": {"shape": "CreateOrganizationResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "DirectoryInUseException"}, {"shape": "DirectoryUnavailableException"}, {"shape": "LimitExceededException"}, {"shape": "NameAvailabilityException"}], "documentation": "<p>Creates a new WorkMail organization. Optionally, you can choose to associate an existing AWS Directory Service directory with your organization. If an AWS Directory Service directory ID is specified, the organization alias must match the directory alias. If you choose not to associate an existing directory with your organization, then we create a new WorkMail directory for you. For more information, see <a href=\"https://docs.aws.amazon.com/workmail/latest/adminguide/add_new_organization.html\">Adding an organization</a> in the <i>WorkMail Administrator Guide</i>.</p> <p>You can associate multiple email domains with an organization, then choose your default email domain from the WorkMail console. You can also associate a domain that is managed in an Amazon Route 53 public hosted zone. For more information, see <a href=\"https://docs.aws.amazon.com/workmail/latest/adminguide/add_domain.html\">Adding a domain</a> and <a href=\"https://docs.aws.amazon.com/workmail/latest/adminguide/default_domain.html\">Choosing the default domain</a> in the <i>WorkMail Administrator Guide</i>.</p> <p>Optionally, you can use a customer managed key from AWS Key Management Service (AWS KMS) to encrypt email for your organization. If you don't associate an AWS KMS key, WorkMail creates a default, AWS managed key for you.</p>", "idempotent": true}, "CreateResource": {"name": "CreateResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateResourceRequest"}, "output": {"shape": "CreateResourceResponse"}, "errors": [{"shape": "DirectoryServiceAuthenticationFailedException"}, {"shape": "DirectoryUnavailableException"}, {"shape": "InvalidParameterException"}, {"shape": "NameAvailabilityException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}, {"shape": "ReservedNameException"}, {"shape": "UnsupportedOperationException"}], "documentation": "<p>Creates a new WorkMail resource.</p>", "idempotent": true}, "CreateUser": {"name": "CreateUser", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateUserRequest"}, "output": {"shape": "CreateUserResponse"}, "errors": [{"shape": "DirectoryServiceAuthenticationFailedException"}, {"shape": "DirectoryUnavailableException"}, {"shape": "InvalidParameterException"}, {"shape": "InvalidPasswordException"}, {"shape": "NameAvailabilityException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}, {"shape": "ReservedNameException"}, {"shape": "UnsupportedOperationException"}], "documentation": "<p>Creates a user who can be used in WorkMail by calling the <a>RegisterToWorkMail</a> operation.</p>", "idempotent": true}, "DeleteAccessControlRule": {"name": "DeleteAccessControlRule", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteAccessControlRuleRequest"}, "output": {"shape": "DeleteAccessControlRuleResponse"}, "errors": [{"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}], "documentation": "<p>Deletes an access control rule for the specified WorkMail organization.</p> <note> <p>Deleting already deleted and non-existing rules does not produce an error. In those cases, the service sends back an HTTP 200 response with an empty HTTP body.</p> </note>"}, "DeleteAlias": {"name": "DeleteAlias", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteAliasRequest"}, "output": {"shape": "DeleteAliasResponse"}, "errors": [{"shape": "EntityNotFoundException"}, {"shape": "EntityStateException"}, {"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}], "documentation": "<p>Remove one or more specified aliases from a set of aliases for a given user.</p>", "idempotent": true}, "DeleteAvailabilityConfiguration": {"name": "DeleteAvailabilityConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteAvailabilityConfigurationRequest"}, "output": {"shape": "DeleteAvailabilityConfigurationResponse"}, "errors": [{"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}], "documentation": "<p>Deletes the <code>AvailabilityConfiguration</code> for the given WorkMail organization and domain.</p>", "idempotent": true}, "DeleteEmailMonitoringConfiguration": {"name": "DeleteEmailMonitoringConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteEmailMonitoringConfigurationRequest"}, "output": {"shape": "DeleteEmailMonitoringConfigurationResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}], "documentation": "<p>Deletes the email monitoring configuration for a specified organization.</p>", "idempotent": true}, "DeleteGroup": {"name": "DeleteGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteGroupRequest"}, "output": {"shape": "DeleteGroupResponse"}, "errors": [{"shape": "DirectoryServiceAuthenticationFailedException"}, {"shape": "DirectoryUnavailableException"}, {"shape": "EntityStateException"}, {"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}, {"shape": "UnsupportedOperationException"}], "documentation": "<p>Deletes a group from WorkMail.</p>", "idempotent": true}, "DeleteIdentityCenterApplication": {"name": "DeleteIdentityCenterApplication", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteIdentityCenterApplicationRequest"}, "output": {"shape": "DeleteIdentityCenterApplicationResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "OrganizationStateException"}], "documentation": "<p> Deletes the IAM Identity Center application from WorkMail. This action does not affect the authentication settings for any WorkMail organizations. </p>", "idempotent": true}, "DeleteIdentityProviderConfiguration": {"name": "DeleteIdentityProviderConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteIdentityProviderConfigurationRequest"}, "output": {"shape": "DeleteIdentityProviderConfigurationResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}], "documentation": "<p> Disables the integration between IdC and WorkMail. Authentication will continue with the directory as it was before the IdC integration. You might have to reset your directory passwords and reconfigure your desktop and mobile email clients. </p>", "idempotent": true}, "DeleteImpersonationRole": {"name": "DeleteImpersonationRole", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteImpersonationRoleRequest"}, "output": {"shape": "DeleteImpersonationRoleResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}], "documentation": "<p>Deletes an impersonation role for the given WorkMail organization.</p>"}, "DeleteMailboxPermissions": {"name": "DeleteMailboxPermissions", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteMailboxPermissionsRequest"}, "output": {"shape": "DeleteMailboxPermissionsResponse"}, "errors": [{"shape": "EntityNotFoundException"}, {"shape": "EntityStateException"}, {"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}], "documentation": "<p>Deletes permissions granted to a member (user or group).</p>", "idempotent": true}, "DeleteMobileDeviceAccessOverride": {"name": "DeleteMobileDeviceAccessOverride", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteMobileDeviceAccessOverrideRequest"}, "output": {"shape": "DeleteMobileDeviceAccessOverrideResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}, {"shape": "EntityNotFoundException"}], "documentation": "<p>Deletes the mobile device access override for the given WorkMail organization, user, and device.</p> <note> <p>Deleting already deleted and non-existing overrides does not produce an error. In those cases, the service sends back an HTTP 200 response with an empty HTTP body.</p> </note>"}, "DeleteMobileDeviceAccessRule": {"name": "DeleteMobileDeviceAccessRule", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteMobileDeviceAccessRuleRequest"}, "output": {"shape": "DeleteMobileDeviceAccessRuleResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}], "documentation": "<p>Deletes a mobile device access rule for the specified WorkMail organization.</p> <note> <p>Deleting already deleted and non-existing rules does not produce an error. In those cases, the service sends back an HTTP 200 response with an empty HTTP body.</p> </note>"}, "DeleteOrganization": {"name": "DeleteOrganization", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteOrganizationRequest"}, "output": {"shape": "DeleteOrganizationResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}], "documentation": "<p>Deletes an WorkMail organization and all underlying AWS resources managed by WorkMail as part of the organization. You can choose whether to delete the associated directory. For more information, see <a href=\"https://docs.aws.amazon.com/workmail/latest/adminguide/remove_organization.html\">Removing an organization</a> in the <i>WorkMail Administrator Guide</i>.</p>", "idempotent": true}, "DeletePersonalAccessToken": {"name": "DeletePersonalAccessToken", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeletePersonalAccessTokenRequest"}, "output": {"shape": "DeletePersonalAccessTokenResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}], "documentation": "<p> Deletes the Personal Access Token from the provided WorkMail Organization. </p>", "idempotent": true}, "DeleteResource": {"name": "DeleteResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteResourceRequest"}, "output": {"shape": "DeleteResourceResponse"}, "errors": [{"shape": "EntityStateException"}, {"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}, {"shape": "UnsupportedOperationException"}], "documentation": "<p>Deletes the specified resource.</p>", "idempotent": true}, "DeleteRetentionPolicy": {"name": "DeleteRetentionPolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteRetentionPolicyRequest"}, "output": {"shape": "DeleteRetentionPolicyResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}], "documentation": "<p>Deletes the specified retention policy from the specified organization.</p>", "idempotent": true}, "DeleteUser": {"name": "DeleteUser", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteUserRequest"}, "output": {"shape": "DeleteUserResponse"}, "errors": [{"shape": "DirectoryServiceAuthenticationFailedException"}, {"shape": "DirectoryUnavailableException"}, {"shape": "EntityStateException"}, {"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}, {"shape": "UnsupportedOperationException"}], "documentation": "<p>Deletes a user from WorkMail and all subsequent systems. Before you can delete a user, the user state must be <code>DISABLED</code>. Use the <a>DescribeUser</a> action to confirm the user state.</p> <p>Deleting a user is permanent and cannot be undone. WorkMail archives user mailboxes for 30 days before they are permanently removed.</p>", "idempotent": true}, "DeregisterFromWorkMail": {"name": "DeregisterFromWorkMail", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeregisterFromWorkMailRequest"}, "output": {"shape": "DeregisterFromWorkMailResponse"}, "errors": [{"shape": "EntityNotFoundException"}, {"shape": "EntityStateException"}, {"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}], "documentation": "<p>Mark a user, group, or resource as no longer used in WorkMail. This action disassociates the mailbox and schedules it for clean-up. WorkMail keeps mailboxes for 30 days before they are permanently removed. The functionality in the console is <i>Disable</i>.</p>", "idempotent": true}, "DeregisterMailDomain": {"name": "DeregisterMailDomain", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeregisterMailDomainRequest"}, "output": {"shape": "DeregisterMailDomainResponse"}, "errors": [{"shape": "MailDomainInUseException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}, {"shape": "InvalidParameterException"}, {"shape": "InvalidCustomSesConfigurationException"}], "documentation": "<p>Removes a domain from WorkMail, stops email routing to WorkMail, and removes the authorization allowing WorkMail use. SES keeps the domain because other applications may use it. You must first remove any email address used by WorkMail entities before you remove the domain.</p>", "idempotent": true}, "DescribeEmailMonitoringConfiguration": {"name": "DescribeEmailMonitoringConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeEmailMonitoringConfigurationRequest"}, "output": {"shape": "DescribeEmailMonitoringConfigurationResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}], "documentation": "<p>Describes the current email monitoring configuration for a specified organization.</p>", "idempotent": true}, "DescribeEntity": {"name": "DescribeEntity", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeEntityRequest"}, "output": {"shape": "DescribeEntityResponse"}, "errors": [{"shape": "EntityNotFoundException"}, {"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}], "documentation": "<p>Returns basic details about an entity in WorkMail. </p>", "idempotent": true}, "DescribeGroup": {"name": "DescribeGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeGroupRequest"}, "output": {"shape": "DescribeGroupResponse"}, "errors": [{"shape": "EntityNotFoundException"}, {"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}], "documentation": "<p>Returns the data available for the group.</p>", "idempotent": true}, "DescribeIdentityProviderConfiguration": {"name": "DescribeIdentityProviderConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeIdentityProviderConfigurationRequest"}, "output": {"shape": "DescribeIdentityProviderConfigurationResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p> Returns detailed information on the current IdC setup for the WorkMail organization. </p>", "idempotent": true}, "DescribeInboundDmarcSettings": {"name": "DescribeInboundDmarcSettings", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeInboundDmarcSettingsRequest"}, "output": {"shape": "DescribeInboundDmarcSettingsResponse"}, "errors": [{"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}], "documentation": "<p>Lists the settings in a DMARC policy for a specified organization.</p>", "idempotent": true}, "DescribeMailboxExportJob": {"name": "DescribeMailboxExportJob", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeMailboxExportJobRequest"}, "output": {"shape": "DescribeMailboxExportJobResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}, {"shape": "EntityNotFoundException"}], "documentation": "<p>Describes the current status of a mailbox export job.</p>", "idempotent": true}, "DescribeOrganization": {"name": "DescribeOrganization", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeOrganizationRequest"}, "output": {"shape": "DescribeOrganizationResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}], "documentation": "<p>Provides more information regarding a given organization based on its identifier.</p>", "idempotent": true}, "DescribeResource": {"name": "DescribeResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeResourceRequest"}, "output": {"shape": "DescribeResourceResponse"}, "errors": [{"shape": "EntityNotFoundException"}, {"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}, {"shape": "UnsupportedOperationException"}], "documentation": "<p>Returns the data available for the resource.</p>", "idempotent": true}, "DescribeUser": {"name": "DescribeUser", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeUserRequest"}, "output": {"shape": "DescribeUserResponse"}, "errors": [{"shape": "DirectoryServiceAuthenticationFailedException"}, {"shape": "DirectoryUnavailableException"}, {"shape": "EntityNotFoundException"}, {"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}], "documentation": "<p>Provides information regarding the user.</p>", "idempotent": true}, "DisassociateDelegateFromResource": {"name": "DisassociateDelegateFromResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DisassociateDelegateFromResourceRequest"}, "output": {"shape": "DisassociateDelegateFromResourceResponse"}, "errors": [{"shape": "EntityNotFoundException"}, {"shape": "EntityStateException"}, {"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}, {"shape": "UnsupportedOperationException"}], "documentation": "<p>Removes a member from the resource's set of delegates.</p>", "idempotent": true}, "DisassociateMemberFromGroup": {"name": "DisassociateMemberFromGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DisassociateMemberFromGroupRequest"}, "output": {"shape": "DisassociateMemberFromGroupResponse"}, "errors": [{"shape": "DirectoryServiceAuthenticationFailedException"}, {"shape": "DirectoryUnavailableException"}, {"shape": "EntityNotFoundException"}, {"shape": "EntityStateException"}, {"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}, {"shape": "UnsupportedOperationException"}], "documentation": "<p>Removes a member from a group.</p>", "idempotent": true}, "GetAccessControlEffect": {"name": "GetAccessControlEffect", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetAccessControlEffectRequest"}, "output": {"shape": "GetAccessControlEffectResponse"}, "errors": [{"shape": "EntityNotFoundException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}], "documentation": "<p>Gets the effects of an organization's access control rules as they apply to a specified IPv4 address, access protocol action, and user ID or impersonation role ID. You must provide either the user ID or impersonation role ID. Impersonation role ID can only be used with Action EWS.</p>"}, "GetDefaultRetentionPolicy": {"name": "GetDefaultRetentionPolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetDefaultRetentionPolicyRequest"}, "output": {"shape": "GetDefaultRetentionPolicyResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}, {"shape": "EntityNotFoundException"}], "documentation": "<p>Gets the default retention policy details for the specified organization.</p>", "idempotent": true}, "GetImpersonationRole": {"name": "GetImpersonationRole", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetImpersonationRoleRequest"}, "output": {"shape": "GetImpersonationRoleResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Gets the impersonation role details for the given WorkMail organization.</p>"}, "GetImpersonationRoleEffect": {"name": "GetImpersonationRoleEffect", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetImpersonationRoleEffectRequest"}, "output": {"shape": "GetImpersonationRoleEffectResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}, {"shape": "ResourceNotFoundException"}, {"shape": "EntityNotFoundException"}, {"shape": "EntityStateException"}], "documentation": "<p>Tests whether the given impersonation role can impersonate a target user.</p>"}, "GetMailDomain": {"name": "GetMailDomain", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetMailDomainRequest"}, "output": {"shape": "GetMailDomainResponse"}, "errors": [{"shape": "MailDomainNotFoundException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}, {"shape": "InvalidParameterException"}], "documentation": "<p>Gets details for a mail domain, including domain records required to configure your domain with recommended security.</p>", "idempotent": true}, "GetMailboxDetails": {"name": "GetMailboxDetails", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetMailboxDetailsRequest"}, "output": {"shape": "GetMailboxDetailsResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}, {"shape": "EntityNotFoundException"}], "documentation": "<p>Requests a user's mailbox details for a specified organization and user.</p>", "idempotent": true}, "GetMobileDeviceAccessEffect": {"name": "GetMobileDeviceAccessEffect", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetMobileDeviceAccessEffectRequest"}, "output": {"shape": "GetMobileDeviceAccessEffectResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}], "documentation": "<p>Simulates the effect of the mobile device access rules for the given attributes of a sample access event. Use this method to test the effects of the current set of mobile device access rules for the WorkMail organization for a particular user's attributes.</p>"}, "GetMobileDeviceAccessOverride": {"name": "GetMobileDeviceAccessOverride", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetMobileDeviceAccessOverrideRequest"}, "output": {"shape": "GetMobileDeviceAccessOverrideResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}, {"shape": "EntityNotFoundException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Gets the mobile device access override for the given WorkMail organization, user, and device.</p>"}, "GetPersonalAccessTokenMetadata": {"name": "GetPersonalAccessTokenMetadata", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetPersonalAccessTokenMetadataRequest"}, "output": {"shape": "GetPersonalAccessTokenMetadataResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}], "documentation": "<p> Requests details of a specific Personal Access Token within the WorkMail organization. </p>", "idempotent": true}, "ListAccessControlRules": {"name": "ListAccessControlRules", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListAccessControlRulesRequest"}, "output": {"shape": "ListAccessControlRulesResponse"}, "errors": [{"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}], "documentation": "<p>Lists the access control rules for the specified organization.</p>"}, "ListAliases": {"name": "ListAliases", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListAliasesRequest"}, "output": {"shape": "ListAliasesResponse"}, "errors": [{"shape": "EntityNotFoundException"}, {"shape": "EntityStateException"}, {"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}], "documentation": "<p>Creates a paginated call to list the aliases associated with a given entity.</p>", "idempotent": true}, "ListAvailabilityConfigurations": {"name": "ListAvailabilityConfigurations", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListAvailabilityConfigurationsRequest"}, "output": {"shape": "ListAvailabilityConfigurationsResponse"}, "errors": [{"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}, {"shape": "InvalidParameterException"}], "documentation": "<p>List all the <code>AvailabilityConfiguration</code>'s for the given WorkMail organization.</p>"}, "ListGroupMembers": {"name": "ListGroupMembers", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListGroupMembersRequest"}, "output": {"shape": "ListGroupMembersResponse"}, "errors": [{"shape": "EntityNotFoundException"}, {"shape": "EntityStateException"}, {"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}], "documentation": "<p>Returns an overview of the members of a group. Users and groups can be members of a group.</p>", "idempotent": true}, "ListGroups": {"name": "ListGroups", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListGroupsRequest"}, "output": {"shape": "ListGroupsResponse"}, "errors": [{"shape": "EntityNotFoundException"}, {"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}], "documentation": "<p>Returns summaries of the organization's groups.</p>", "idempotent": true}, "ListGroupsForEntity": {"name": "ListGroupsForEntity", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListGroupsForEntityRequest"}, "output": {"shape": "ListGroupsForEntityResponse"}, "errors": [{"shape": "EntityNotFoundException"}, {"shape": "EntityStateException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}, {"shape": "InvalidParameterException"}], "documentation": "<p>Returns all the groups to which an entity belongs.</p>", "idempotent": true}, "ListImpersonationRoles": {"name": "ListImpersonationRoles", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListImpersonationRolesRequest"}, "output": {"shape": "ListImpersonationRolesResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}], "documentation": "<p>Lists all the impersonation roles for the given WorkMail organization.</p>"}, "ListMailDomains": {"name": "ListMailDomains", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListMailDomainsRequest"}, "output": {"shape": "ListMailDomainsResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}], "documentation": "<p>Lists the mail domains in a given WorkMail organization.</p>", "idempotent": true}, "ListMailboxExportJobs": {"name": "ListMailboxExportJobs", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListMailboxExportJobsRequest"}, "output": {"shape": "ListMailboxExportJobsResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}], "documentation": "<p>Lists the mailbox export jobs started for the specified organization within the last seven days.</p>", "idempotent": true}, "ListMailboxPermissions": {"name": "ListMailboxPermissions", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListMailboxPermissionsRequest"}, "output": {"shape": "ListMailboxPermissionsResponse"}, "errors": [{"shape": "EntityNotFoundException"}, {"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}], "documentation": "<p>Lists the mailbox permissions associated with a user, group, or resource mailbox.</p>", "idempotent": true}, "ListMobileDeviceAccessOverrides": {"name": "ListMobileDeviceAccessOverrides", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListMobileDeviceAccessOverridesRequest"}, "output": {"shape": "ListMobileDeviceAccessOverridesResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}, {"shape": "EntityNotFoundException"}], "documentation": "<p>Lists all the mobile device access overrides for any given combination of WorkMail organization, user, or device.</p>"}, "ListMobileDeviceAccessRules": {"name": "ListMobileDeviceAccessRules", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListMobileDeviceAccessRulesRequest"}, "output": {"shape": "ListMobileDeviceAccessRulesResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}], "documentation": "<p>Lists the mobile device access rules for the specified WorkMail organization.</p>"}, "ListOrganizations": {"name": "ListOrganizations", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListOrganizationsRequest"}, "output": {"shape": "ListOrganizationsResponse"}, "errors": [{"shape": "InvalidParameterException"}], "documentation": "<p>Returns summaries of the customer's organizations.</p>", "idempotent": true}, "ListPersonalAccessTokens": {"name": "ListPersonalAccessTokens", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListPersonalAccessTokensRequest"}, "output": {"shape": "ListPersonalAccessTokensResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "EntityNotFoundException"}, {"shape": "EntityStateException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}], "documentation": "<p> Returns a summary of your Personal Access Tokens. </p>", "idempotent": true}, "ListResourceDelegates": {"name": "ListResourceDelegates", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListResourceDelegatesRequest"}, "output": {"shape": "ListResourceDelegatesResponse"}, "errors": [{"shape": "EntityNotFoundException"}, {"shape": "EntityStateException"}, {"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}, {"shape": "UnsupportedOperationException"}], "documentation": "<p>Lists the delegates associated with a resource. Users and groups can be resource delegates and answer requests on behalf of the resource.</p>", "idempotent": true}, "ListResources": {"name": "ListResources", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListResourcesRequest"}, "output": {"shape": "ListResourcesResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}, {"shape": "UnsupportedOperationException"}], "documentation": "<p>Returns summaries of the organization's resources.</p>", "idempotent": true}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}], "documentation": "<p>Lists the tags applied to an WorkMail organization resource.</p>"}, "ListUsers": {"name": "ListUsers", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListUsersRequest"}, "output": {"shape": "ListUsersResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}], "documentation": "<p>Returns summaries of the organization's users.</p>", "idempotent": true}, "PutAccessControlRule": {"name": "PutAccessControlRule", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutAccessControlRuleRequest"}, "output": {"shape": "PutAccessControlRuleResponse"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "InvalidParameterException"}, {"shape": "EntityNotFoundException"}, {"shape": "ResourceNotFoundException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}], "documentation": "<p>Adds a new access control rule for the specified organization. The rule allows or denies access to the organization for the specified IPv4 addresses, access protocol actions, user IDs and impersonation IDs. Adding a new rule with the same name as an existing rule replaces the older rule.</p>"}, "PutEmailMonitoringConfiguration": {"name": "PutEmailMonitoringConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutEmailMonitoringConfigurationRequest"}, "output": {"shape": "PutEmailMonitoringConfigurationResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}], "documentation": "<p>Creates or updates the email monitoring configuration for a specified organization.</p>", "idempotent": true}, "PutIdentityProviderConfiguration": {"name": "PutIdentityProviderConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutIdentityProviderConfigurationRequest"}, "output": {"shape": "PutIdentityProviderConfigurationResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p> Enables integration between IAM Identity Center (IdC) and WorkMail to proxy authentication requests for mailbox users. You can connect your IdC directory or your external directory to WorkMail through IdC and manage access to WorkMail mailboxes in a single place. For enhanced protection, you could enable Multifactor Authentication (MFA) and Personal Access Tokens. </p>", "idempotent": true}, "PutInboundDmarcSettings": {"name": "PutInboundDmarcSettings", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutInboundDmarcSettingsRequest"}, "output": {"shape": "PutInboundDmarcSettingsResponse"}, "errors": [{"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}], "documentation": "<p>Enables or disables a DMARC policy for a given organization.</p>", "idempotent": true}, "PutMailboxPermissions": {"name": "PutMailboxPermissions", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutMailboxPermissionsRequest"}, "output": {"shape": "PutMailboxPermissionsResponse"}, "errors": [{"shape": "EntityNotFoundException"}, {"shape": "EntityStateException"}, {"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}], "documentation": "<p>Sets permissions for a user, group, or resource. This replaces any pre-existing permissions.</p>", "idempotent": true}, "PutMobileDeviceAccessOverride": {"name": "PutMobileDeviceAccessOverride", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutMobileDeviceAccessOverrideRequest"}, "output": {"shape": "PutMobileDeviceAccessOverrideResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}, {"shape": "EntityNotFoundException"}, {"shape": "EntityStateException"}], "documentation": "<p>Creates or updates a mobile device access override for the given WorkMail organization, user, and device.</p>"}, "PutRetentionPolicy": {"name": "PutRetentionPolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutRetentionPolicyRequest"}, "output": {"shape": "PutRetentionPolicyResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Puts a retention policy to the specified organization.</p>", "idempotent": true}, "RegisterMailDomain": {"name": "RegisterMailDomain", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "RegisterMailDomainRequest"}, "output": {"shape": "RegisterMailDomainResponse"}, "errors": [{"shape": "MailDomainInUseException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}, {"shape": "LimitExceededException"}, {"shape": "InvalidParameterException"}], "documentation": "<p>Registers a new domain in WorkMail and SES, and configures it for use by WorkMail. Emails received by SES for this domain are routed to the specified WorkMail organization, and WorkMail has permanent permission to use the specified domain for sending your users' emails.</p>", "idempotent": true}, "RegisterToWorkMail": {"name": "RegisterToWorkMail", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "RegisterToWorkMailRequest"}, "output": {"shape": "RegisterToWorkMailResponse"}, "errors": [{"shape": "DirectoryServiceAuthenticationFailedException"}, {"shape": "DirectoryUnavailableException"}, {"shape": "EmailAddressInUseException"}, {"shape": "EntityNotFoundException"}, {"shape": "EntityStateException"}, {"shape": "EntityAlreadyRegisteredException"}, {"shape": "InvalidParameterException"}, {"shape": "MailDomainNotFoundException"}, {"shape": "MailDomainStateException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}], "documentation": "<p>Registers an existing and disabled user, group, or resource for WorkMail use by associating a mailbox and calendaring capabilities. It performs no change if the user, group, or resource is enabled and fails if the user, group, or resource is deleted. This operation results in the accumulation of costs. For more information, see <a href=\"https://aws.amazon.com/workmail/pricing\">Pricing</a>. The equivalent console functionality for this operation is <i>Enable</i>.</p> <p>Users can either be created by calling the <a>CreateUser</a> API operation or they can be synchronized from your directory. For more information, see <a>DeregisterFromWorkMail</a>.</p>", "idempotent": true}, "ResetPassword": {"name": "ResetPassword", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ResetPasswordRequest"}, "output": {"shape": "ResetPasswordResponse"}, "errors": [{"shape": "DirectoryServiceAuthenticationFailedException"}, {"shape": "DirectoryUnavailableException"}, {"shape": "EntityNotFoundException"}, {"shape": "EntityStateException"}, {"shape": "InvalidParameterException"}, {"shape": "InvalidPasswordException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}, {"shape": "UnsupportedOperationException"}], "documentation": "<p>Allows the administrator to reset the password for a user.</p>", "idempotent": true}, "StartMailboxExportJob": {"name": "StartMailboxExportJob", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StartMailboxExportJobRequest"}, "output": {"shape": "StartMailboxExportJobResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}, {"shape": "EntityNotFoundException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Starts a mailbox export job to export MIME-format email messages and calendar items from the specified mailbox to the specified Amazon Simple Storage Service (Amazon S3) bucket. For more information, see <a href=\"https://docs.aws.amazon.com/workmail/latest/adminguide/mail-export.html\">Exporting mailbox content</a> in the <i>WorkMail Administrator Guide</i>.</p>", "idempotent": true}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}, {"shape": "TooManyTagsException"}, {"shape": "OrganizationStateException"}], "documentation": "<p>Applies the specified tags to the specified WorkMailorganization resource.</p>"}, "TestAvailabilityConfiguration": {"name": "TestAvailabilityConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "TestAvailabilityConfigurationRequest"}, "output": {"shape": "TestAvailabilityConfigurationResponse"}, "errors": [{"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterException"}], "documentation": "<p>Performs a test on an availability provider to ensure that access is allowed. For EWS, it verifies the provided credentials can be used to successfully log in. For Lambda, it verifies that the Lambda function can be invoked and that the resource access policy was configured to deny anonymous access. An anonymous invocation is one done without providing either a <code>SourceArn</code> or <code>SourceAccount</code> header.</p> <note> <p>The request must contain either one provider definition (<code>EwsProvider</code> or <code>LambdaProvider</code>) or the <code>DomainName</code> parameter. If the <code>DomainName</code> parameter is provided, the configuration stored under the <code>DomainName</code> will be tested.</p> </note>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}], "documentation": "<p>Untags the specified tags from the specified WorkMail organization resource.</p>"}, "UpdateAvailabilityConfiguration": {"name": "UpdateAvailabilityConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateAvailabilityConfigurationRequest"}, "output": {"shape": "UpdateAvailabilityConfigurationResponse"}, "errors": [{"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterException"}], "documentation": "<p>Updates an existing <code>AvailabilityConfiguration</code> for the given WorkMail organization and domain.</p>", "idempotent": true}, "UpdateDefaultMailDomain": {"name": "UpdateDefaultMailDomain", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateDefaultMailDomainRequest"}, "output": {"shape": "UpdateDefaultMailDomainResponse"}, "errors": [{"shape": "MailDomainNotFoundException"}, {"shape": "MailDomainStateException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}, {"shape": "InvalidParameterException"}], "documentation": "<p>Updates the default mail domain for an organization. The default mail domain is used by the WorkMail AWS Console to suggest an email address when enabling a mail user. You can only have one default domain.</p>", "idempotent": true}, "UpdateGroup": {"name": "UpdateGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateGroupRequest"}, "output": {"shape": "UpdateGroupResponse"}, "errors": [{"shape": "EntityNotFoundException"}, {"shape": "EntityStateException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}, {"shape": "UnsupportedOperationException"}, {"shape": "InvalidParameterException"}], "documentation": "<p>Updates attributes in a group.</p>", "idempotent": true}, "UpdateImpersonationRole": {"name": "UpdateImpersonationRole", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateImpersonationRoleRequest"}, "output": {"shape": "UpdateImpersonationRoleResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}, {"shape": "ResourceNotFoundException"}, {"shape": "EntityNotFoundException"}, {"shape": "EntityStateException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Updates an impersonation role for the given WorkMail organization.</p>"}, "UpdateMailboxQuota": {"name": "UpdateMailboxQuota", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateMailboxQuotaRequest"}, "output": {"shape": "UpdateMailboxQuotaResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}, {"shape": "EntityNotFoundException"}, {"shape": "EntityStateException"}], "documentation": "<p>Updates a user's current mailbox quota for a specified organization and user.</p>", "idempotent": true}, "UpdateMobileDeviceAccessRule": {"name": "UpdateMobileDeviceAccessRule", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateMobileDeviceAccessRuleRequest"}, "output": {"shape": "UpdateMobileDeviceAccessRuleResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "EntityNotFoundException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}], "documentation": "<p>Updates a mobile device access rule for the specified WorkMail organization.</p>"}, "UpdatePrimaryEmailAddress": {"name": "UpdatePrimaryEmailAddress", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdatePrimaryEmailAddressRequest"}, "output": {"shape": "UpdatePrimaryEmailAddressResponse"}, "errors": [{"shape": "DirectoryServiceAuthenticationFailedException"}, {"shape": "DirectoryUnavailableException"}, {"shape": "EmailAddressInUseException"}, {"shape": "EntityNotFoundException"}, {"shape": "EntityStateException"}, {"shape": "InvalidParameterException"}, {"shape": "MailDomainNotFoundException"}, {"shape": "MailDomainStateException"}, {"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}, {"shape": "UnsupportedOperationException"}], "documentation": "<p>Updates the primary email for a user, group, or resource. The current email is moved into the list of aliases (or swapped between an existing alias and the current primary email), and the email provided in the input is promoted as the primary.</p>", "idempotent": true}, "UpdateResource": {"name": "UpdateResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateResourceRequest"}, "output": {"shape": "UpdateResourceResponse"}, "errors": [{"shape": "DirectoryUnavailableException"}, {"shape": "EntityNotFoundException"}, {"shape": "EntityStateException"}, {"shape": "InvalidConfigurationException"}, {"shape": "EmailAddressInUseException"}, {"shape": "MailDomainNotFoundException"}, {"shape": "MailDomainStateException"}, {"shape": "NameAvailabilityException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}, {"shape": "UnsupportedOperationException"}, {"shape": "InvalidParameterException"}], "documentation": "<p>Updates data for the resource. To have the latest information, it must be preceded by a <a>DescribeResource</a> call. The dataset in the request should be the one expected when performing another <code>DescribeResource</code> call.</p>", "idempotent": true}, "UpdateUser": {"name": "UpdateUser", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateUserRequest"}, "output": {"shape": "UpdateUserResponse"}, "errors": [{"shape": "DirectoryServiceAuthenticationFailedException"}, {"shape": "DirectoryUnavailableException"}, {"shape": "EntityNotFoundException"}, {"shape": "InvalidParameterException"}, {"shape": "OrganizationNotFoundException"}, {"shape": "OrganizationStateException"}, {"shape": "UnsupportedOperationException"}, {"shape": "EntityStateException"}], "documentation": "<p>Updates data for the user. To have the latest information, it must be preceded by a <a>DescribeUser</a> call. The dataset in the request should be the one expected when performing another <code>DescribeUser</code> call.</p>", "idempotent": true}}, "shapes": {"AccessControlRule": {"type": "structure", "members": {"Name": {"shape": "AccessControlRuleName", "documentation": "<p>The rule name.</p>"}, "Effect": {"shape": "AccessControlRuleEffect", "documentation": "<p>The rule effect.</p>"}, "Description": {"shape": "AccessControlRuleDescription", "documentation": "<p>The rule description.</p>"}, "IpRanges": {"shape": "IpRangeList", "documentation": "<p>IPv4 CIDR ranges to include in the rule.</p>"}, "NotIpRanges": {"shape": "IpRangeList", "documentation": "<p>IPv4 CIDR ranges to exclude from the rule.</p>"}, "Actions": {"shape": "ActionsList", "documentation": "<p>Access protocol actions to include in the rule. Valid values include <code>ActiveSync</code>, <code>AutoDiscover</code>, <code>EWS</code>, <code>IMAP</code>, <code>SMTP</code>, <code>WindowsOutlook</code>, and <code>WebMail</code>.</p>"}, "NotActions": {"shape": "ActionsList", "documentation": "<p>Access protocol actions to exclude from the rule. Valid values include <code>ActiveSync</code>, <code>AutoDiscover</code>, <code>EWS</code>, <code>IMAP</code>, <code>SMTP</code>, <code>WindowsOutlook</code>, and <code>WebMail</code>.</p>"}, "UserIds": {"shape": "UserIdList", "documentation": "<p>User IDs to include in the rule.</p>"}, "NotUserIds": {"shape": "UserIdList", "documentation": "<p>User IDs to exclude from the rule.</p>"}, "DateCreated": {"shape": "Timestamp", "documentation": "<p>The date that the rule was created.</p>"}, "DateModified": {"shape": "Timestamp", "documentation": "<p>The date that the rule was modified.</p>"}, "ImpersonationRoleIds": {"shape": "ImpersonationRoleIdList", "documentation": "<p>Impersonation role IDs to include in the rule.</p>"}, "NotImpersonationRoleIds": {"shape": "ImpersonationRoleIdList", "documentation": "<p>Impersonation role IDs to exclude from the rule.</p>"}}, "documentation": "<p>A rule that controls access to an WorkMail organization.</p>"}, "AccessControlRuleAction": {"type": "string", "max": 64, "min": 1, "pattern": "[a-zA-Z]+"}, "AccessControlRuleDescription": {"type": "string", "max": 255, "min": 0, "pattern": "[\\u0020-\\u00FF]+"}, "AccessControlRuleEffect": {"type": "string", "enum": ["ALLOW", "DENY"]}, "AccessControlRuleName": {"type": "string", "max": 64, "min": 1, "pattern": "[a-zA-Z0-9_-]+"}, "AccessControlRuleNameList": {"type": "list", "member": {"shape": "AccessControlRuleName"}, "max": 10, "min": 0}, "AccessControlRulesList": {"type": "list", "member": {"shape": "AccessControlRule"}, "max": 10, "min": 0}, "AccessEffect": {"type": "string", "enum": ["ALLOW", "DENY"]}, "ActionsList": {"type": "list", "member": {"shape": "AccessControlRuleAction"}, "max": 10, "min": 0}, "Aliases": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "AmazonResourceName": {"type": "string", "max": 1011, "min": 1, "pattern": "arn:aws:workmail:[a-z0-9-]*:[a-z0-9-]+:[A-Za-z0-9][A-Za-z0-9:_/+=,@.-]{0,1023}"}, "ApplicationArn": {"type": "string", "max": 1224, "min": 10, "pattern": "^arn:(aws|aws-us-gov|aws-cn|aws-iso|aws-iso-b):sso::\\d{12}:application/(sso)?ins-[a-zA-Z0-9-.]{16}/apl-[a-zA-Z0-9]{16}$"}, "AssociateDelegateToResourceRequest": {"type": "structure", "required": ["OrganizationId", "ResourceId", "EntityId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The organization under which the resource exists.</p>"}, "ResourceId": {"shape": "EntityIdentifier", "documentation": "<p>The resource for which members (users or groups) are associated.</p> <p>The identifier can accept <i>ResourceId</i>, <i>Resourcename</i>, or <i>email</i>. The following identity formats are available:</p> <ul> <li> <p>Resource ID: r-0123456789a0123456789b0123456789</p> </li> <li> <p>Email address: <EMAIL></p> </li> <li> <p>Resource name: resource</p> </li> </ul>"}, "EntityId": {"shape": "EntityIdentifier", "documentation": "<p>The member (user or group) to associate to the resource.</p> <p>The entity ID can accept <i>UserId or GroupID</i>, <i>Username or Groupname</i>, or <i>email</i>.</p> <ul> <li> <p>Entity: 12345678-1234-1234-1234-**********12 or S-1-1-12-**********-123456789-123456789-1234</p> </li> <li> <p>Email address: <EMAIL></p> </li> <li> <p>Entity: entity</p> </li> </ul>"}}}, "AssociateDelegateToResourceResponse": {"type": "structure", "members": {}}, "AssociateMemberToGroupRequest": {"type": "structure", "required": ["OrganizationId", "GroupId", "MemberId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The organization under which the group exists.</p>"}, "GroupId": {"shape": "EntityIdentifier", "documentation": "<p>The group to which the member (user or group) is associated.</p> <p>The identifier can accept <i>GroupId</i>, <i>Groupname</i>, or <i>email</i>. The following identity formats are available:</p> <ul> <li> <p>Group ID: 12345678-1234-1234-1234-**********12 or S-1-1-12-**********-123456789-123456789-1234</p> </li> <li> <p>Email address: <EMAIL></p> </li> <li> <p>Group name: group</p> </li> </ul>"}, "MemberId": {"shape": "EntityIdentifier", "documentation": "<p>The member (user or group) to associate to the group.</p> <p>The member ID can accept <i>UserID or GroupId</i>, <i>Username or Groupname</i>, or <i>email</i>.</p> <ul> <li> <p>Member: 12345678-1234-1234-1234-**********12 or S-1-1-12-**********-123456789-123456789-1234</p> </li> <li> <p>Email address: <EMAIL></p> </li> <li> <p>Member name: member</p> </li> </ul>"}}}, "AssociateMemberToGroupResponse": {"type": "structure", "members": {}}, "AssumeImpersonationRoleRequest": {"type": "structure", "required": ["OrganizationId", "ImpersonationRoleId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The WorkMail organization under which the impersonation role will be assumed.</p>"}, "ImpersonationRoleId": {"shape": "ImpersonationRoleId", "documentation": "<p>The impersonation role ID to assume.</p>"}}}, "AssumeImpersonationRoleResponse": {"type": "structure", "members": {"Token": {"shape": "ImpersonationToken", "documentation": "<p>The authentication token for the impersonation role.</p>"}, "ExpiresIn": {"shape": "ExpiresIn", "documentation": "<p>The authentication token's validity, in seconds.</p>"}}}, "AvailabilityConfiguration": {"type": "structure", "members": {"DomainName": {"shape": "DomainName", "documentation": "<p>Displays the domain to which the provider applies.</p>"}, "ProviderType": {"shape": "AvailabilityProviderType", "documentation": "<p>Displays the provider type that applies to this domain.</p>"}, "EwsProvider": {"shape": "RedactedEwsAvailabilityProvider", "documentation": "<p>If <code>ProviderType</code> is <code>EWS</code>, then this field contains <code>RedactedEwsAvailabilityProvider</code>. Otherwise, it is not required.</p>"}, "LambdaProvider": {"shape": "LambdaAvailabilityProvider", "documentation": "<p>If ProviderType is <code>LAMBDA</code> then this field contains <code>LambdaAvailabilityProvider</code>. Otherwise, it is not required.</p>"}, "DateCreated": {"shape": "Timestamp", "documentation": "<p>The date and time at which the availability configuration was created.</p>"}, "DateModified": {"shape": "Timestamp", "documentation": "<p>The date and time at which the availability configuration was last modified.</p>"}}, "documentation": "<p>List all the <code>AvailabilityConfiguration</code>'s for the given WorkMail organization.</p>"}, "AvailabilityConfigurationList": {"type": "list", "member": {"shape": "AvailabilityConfiguration"}}, "AvailabilityProviderType": {"type": "string", "enum": ["EWS", "LAMBDA"]}, "BookingOptions": {"type": "structure", "members": {"AutoAcceptRequests": {"shape": "Boolean", "documentation": "<p>The resource's ability to automatically reply to requests. If disabled, delegates must be associated to the resource.</p>"}, "AutoDeclineRecurringRequests": {"shape": "Boolean", "documentation": "<p>The resource's ability to automatically decline any recurring requests.</p>"}, "AutoDeclineConflictingRequests": {"shape": "Boolean", "documentation": "<p>The resource's ability to automatically decline any conflicting requests.</p>"}}, "documentation": "<p>At least one delegate must be associated to the resource to disable automatic replies from the resource.</p>"}, "Boolean": {"type": "boolean"}, "BooleanObject": {"type": "boolean"}, "CancelMailboxExportJobRequest": {"type": "structure", "required": ["ClientToken", "JobId", "OrganizationId"], "members": {"ClientToken": {"shape": "IdempotencyClientToken", "documentation": "<p>The idempotency token for the client request.</p>", "idempotencyToken": true}, "JobId": {"shape": "MailboxExportJobId", "documentation": "<p>The job ID.</p>"}, "OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The organization ID.</p>"}}}, "CancelMailboxExportJobResponse": {"type": "structure", "members": {}}, "CreateAliasRequest": {"type": "structure", "required": ["OrganizationId", "EntityId", "<PERSON><PERSON>"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The organization under which the member (user or group) exists.</p>"}, "EntityId": {"shape": "WorkMailIdentifier", "documentation": "<p>The member (user or group) to which this alias is added.</p>"}, "Alias": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The alias to add to the member set.</p>"}}}, "CreateAliasResponse": {"type": "structure", "members": {}}, "CreateAvailabilityConfigurationRequest": {"type": "structure", "required": ["OrganizationId", "DomainName"], "members": {"ClientToken": {"shape": "IdempotencyClientToken", "documentation": "<p>An idempotent token that ensures that an API request is executed only once.</p>", "idempotencyToken": true}, "OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The WorkMail organization for which the <code>AvailabilityConfiguration</code> will be created.</p>"}, "DomainName": {"shape": "DomainName", "documentation": "<p>The domain to which the provider applies.</p>"}, "EwsProvider": {"shape": "EwsAvailabilityProvider", "documentation": "<p>Exchange Web Services (EWS) availability provider definition. The request must contain exactly one provider definition, either <code>EwsProvider</code> or <code>LambdaProvider</code>.</p>"}, "LambdaProvider": {"shape": "LambdaAvailabilityProvider", "documentation": "<p>Lambda availability provider definition. The request must contain exactly one provider definition, either <code>EwsProvider</code> or <code>LambdaProvider</code>.</p>"}}}, "CreateAvailabilityConfigurationResponse": {"type": "structure", "members": {}}, "CreateGroupRequest": {"type": "structure", "required": ["OrganizationId", "Name"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The organization under which the group is to be created.</p>"}, "Name": {"shape": "GroupName", "documentation": "<p>The name of the group.</p>"}, "HiddenFromGlobalAddressList": {"shape": "Boolean", "documentation": "<p>If this parameter is enabled, the group will be hidden from the address book.</p>"}}}, "CreateGroupResponse": {"type": "structure", "members": {"GroupId": {"shape": "WorkMailIdentifier", "documentation": "<p>The identifier of the group.</p>"}}}, "CreateIdentityCenterApplicationRequest": {"type": "structure", "required": ["Name", "InstanceArn"], "members": {"Name": {"shape": "IdentityCenterApplicationName", "documentation": "<p> The name of the IAM Identity Center application. </p>"}, "InstanceArn": {"shape": "InstanceArn", "documentation": "<p> The Amazon Resource Name (ARN) of the instance. </p>"}, "ClientToken": {"shape": "IdempotencyClientToken", "documentation": "<p> The idempotency token associated with the request. </p>", "idempotencyToken": true}}}, "CreateIdentityCenterApplicationResponse": {"type": "structure", "members": {"ApplicationArn": {"shape": "ApplicationArn", "documentation": "<p> The Amazon Resource Name (ARN) of the application. </p>"}}}, "CreateImpersonationRoleRequest": {"type": "structure", "required": ["OrganizationId", "Name", "Type", "Rules"], "members": {"ClientToken": {"shape": "IdempotencyClientToken", "documentation": "<p>The idempotency token for the client request.</p>", "idempotencyToken": true}, "OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The WorkMail organization to create the new impersonation role within.</p>"}, "Name": {"shape": "ImpersonationRoleName", "documentation": "<p>The name of the new impersonation role.</p>"}, "Type": {"shape": "ImpersonationRoleType", "documentation": "<p>The impersonation role's type. The available impersonation role types are <code>READ_ONLY</code> or <code>FULL_ACCESS</code>.</p>"}, "Description": {"shape": "ImpersonationRoleDescription", "documentation": "<p>The description of the new impersonation role.</p>"}, "Rules": {"shape": "ImpersonationRuleList", "documentation": "<p>The list of rules for the impersonation role.</p>"}}}, "CreateImpersonationRoleResponse": {"type": "structure", "members": {"ImpersonationRoleId": {"shape": "ImpersonationRoleId", "documentation": "<p>The new impersonation role ID.</p>"}}}, "CreateMobileDeviceAccessRuleRequest": {"type": "structure", "required": ["OrganizationId", "Name", "Effect"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The WorkMail organization under which the rule will be created.</p>"}, "ClientToken": {"shape": "IdempotencyClientToken", "documentation": "<p>The idempotency token for the client request.</p>", "idempotencyToken": true}, "Name": {"shape": "MobileDeviceAccessRuleName", "documentation": "<p>The rule name.</p>"}, "Description": {"shape": "MobileDeviceAccessRuleDescription", "documentation": "<p>The rule description.</p>"}, "Effect": {"shape": "MobileDeviceAccessRuleEffect", "documentation": "<p>The effect of the rule when it matches. Allowed values are <code>ALLOW</code> or <code>DENY</code>.</p>"}, "DeviceTypes": {"shape": "DeviceTypeList", "documentation": "<p>Devi<PERSON> types that the rule will match.</p>"}, "NotDeviceTypes": {"shape": "DeviceTypeList", "documentation": "<p>Device types that the rule <b>will not</b> match. All other device types will match.</p>"}, "DeviceModels": {"shape": "DeviceModelList", "documentation": "<p>Device models that the rule will match.</p>"}, "NotDeviceModels": {"shape": "DeviceModelList", "documentation": "<p>Device models that the rule <b>will not</b> match. All other device models will match.</p>"}, "DeviceOperatingSystems": {"shape": "DeviceOperatingSystemList", "documentation": "<p>Device operating systems that the rule will match.</p>"}, "NotDeviceOperatingSystems": {"shape": "DeviceOperatingSystemList", "documentation": "<p>Device operating systems that the rule <b>will not</b> match. All other device operating systems will match.</p>"}, "DeviceUserAgents": {"shape": "DeviceUserAgentList", "documentation": "<p>Device user agents that the rule will match.</p>"}, "NotDeviceUserAgents": {"shape": "DeviceUserAgentList", "documentation": "<p>Device user agents that the rule <b>will not</b> match. All other device user agents will match.</p>"}}}, "CreateMobileDeviceAccessRuleResponse": {"type": "structure", "members": {"MobileDeviceAccessRuleId": {"shape": "MobileDeviceAccessRuleId", "documentation": "<p>The identifier for the newly created mobile device access rule.</p>"}}}, "CreateOrganizationRequest": {"type": "structure", "required": ["<PERSON><PERSON>"], "members": {"DirectoryId": {"shape": "DirectoryId", "documentation": "<p>The AWS Directory Service directory ID.</p>"}, "Alias": {"shape": "OrganizationName", "documentation": "<p>The organization alias.</p>"}, "ClientToken": {"shape": "IdempotencyClientToken", "documentation": "<p>The idempotency token associated with the request.</p>", "idempotencyToken": true}, "Domains": {"shape": "Domains", "documentation": "<p>The email domains to associate with the organization.</p>"}, "KmsKeyArn": {"shape": "KmsKeyArn", "documentation": "<p>The Amazon Resource Name (ARN) of a customer managed key from AWS KMS.</p>"}, "EnableInteroperability": {"shape": "Boolean", "documentation": "<p>When <code>true</code>, allows organization interoperability between WorkMail and Microsoft Exchange. If <code>true</code>, you must include a AD Connector directory ID in the request.</p>"}}}, "CreateOrganizationResponse": {"type": "structure", "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The organization ID.</p>"}}}, "CreateResourceRequest": {"type": "structure", "required": ["OrganizationId", "Name", "Type"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The identifier associated with the organization for which the resource is created.</p>"}, "Name": {"shape": "ResourceName", "documentation": "<p>The name of the new resource.</p>"}, "Type": {"shape": "ResourceType", "documentation": "<p>The type of the new resource. The available types are <code>equipment</code> and <code>room</code>.</p>"}, "Description": {"shape": "ResourceDescription", "documentation": "<p>Resource description.</p>"}, "HiddenFromGlobalAddressList": {"shape": "Boolean", "documentation": "<p>If this parameter is enabled, the resource will be hidden from the address book.</p>"}}}, "CreateResourceResponse": {"type": "structure", "members": {"ResourceId": {"shape": "ResourceId", "documentation": "<p>The identifier of the new resource.</p>"}}}, "CreateUserRequest": {"type": "structure", "required": ["OrganizationId", "Name", "DisplayName"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The identifier of the organization for which the user is created.</p>"}, "Name": {"shape": "UserName", "documentation": "<p>The name for the new user. WorkMail directory user names have a maximum length of 64. All others have a maximum length of 20.</p>"}, "DisplayName": {"shape": "UserAttribute", "documentation": "<p>The display name for the new user.</p>"}, "Password": {"shape": "Password", "documentation": "<p>The password for the new user.</p>"}, "Role": {"shape": "UserRole", "documentation": "<p>The role of the new user.</p> <p>You cannot pass <i>SYSTEM_USER</i> or <i>RESOURCE</i> role in a single request. When a user role is not selected, the default role of <i>USER</i> is selected.</p>"}, "FirstName": {"shape": "UserAttribute", "documentation": "<p>The first name of the new user.</p>"}, "LastName": {"shape": "UserAttribute", "documentation": "<p>The last name of the new user. </p>"}, "HiddenFromGlobalAddressList": {"shape": "Boolean", "documentation": "<p>If this parameter is enabled, the user will be hidden from the address book.</p>"}, "IdentityProviderUserId": {"shape": "IdentityProviderUserId", "documentation": "<p>User ID from the IAM Identity Center. If this parameter is empty it will be updated automatically when the user logs in for the first time to the mailbox associated with WorkMail.</p>"}}}, "CreateUserResponse": {"type": "structure", "members": {"UserId": {"shape": "WorkMailIdentifier", "documentation": "<p>The identifier for the new user.</p>"}}}, "Delegate": {"type": "structure", "required": ["Id", "Type"], "members": {"Id": {"shape": "String", "documentation": "<p>The identifier for the user or group associated as the resource's delegate.</p>"}, "Type": {"shape": "MemberType", "documentation": "<p>The type of the delegate: user or group.</p>"}}, "documentation": "<p>The name of the attribute, which is one of the values defined in the UserAttribute enumeration.</p>"}, "DeleteAccessControlRuleRequest": {"type": "structure", "required": ["OrganizationId", "Name"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The identifier for the organization.</p>"}, "Name": {"shape": "AccessControlRuleName", "documentation": "<p>The name of the access control rule.</p>"}}}, "DeleteAccessControlRuleResponse": {"type": "structure", "members": {}}, "DeleteAliasRequest": {"type": "structure", "required": ["OrganizationId", "EntityId", "<PERSON><PERSON>"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The identifier for the organization under which the user exists.</p>"}, "EntityId": {"shape": "WorkMailIdentifier", "documentation": "<p>The identifier for the member (user or group) from which to have the aliases removed.</p>"}, "Alias": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The aliases to be removed from the user's set of aliases. Duplicate entries in the list are collapsed into single entries (the list is transformed into a set).</p>"}}}, "DeleteAliasResponse": {"type": "structure", "members": {}}, "DeleteAvailabilityConfigurationRequest": {"type": "structure", "required": ["OrganizationId", "DomainName"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The WorkMail organization for which the <code>AvailabilityConfiguration</code> will be deleted.</p>"}, "DomainName": {"shape": "DomainName", "documentation": "<p>The domain for which the <code>AvailabilityConfiguration</code> will be deleted.</p>"}}}, "DeleteAvailabilityConfigurationResponse": {"type": "structure", "members": {}}, "DeleteEmailMonitoringConfigurationRequest": {"type": "structure", "required": ["OrganizationId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The ID of the organization from which the email monitoring configuration is deleted.</p>"}}}, "DeleteEmailMonitoringConfigurationResponse": {"type": "structure", "members": {}}, "DeleteGroupRequest": {"type": "structure", "required": ["OrganizationId", "GroupId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The organization that contains the group.</p>"}, "GroupId": {"shape": "EntityIdentifier", "documentation": "<p>The identifier of the group to be deleted.</p> <p>The identifier can be the <i>GroupId</i>, or <i>Groupname</i>. The following identity formats are available:</p> <ul> <li> <p>Group ID: 12345678-1234-1234-1234-**********12 or S-1-1-12-**********-123456789-123456789-1234</p> </li> <li> <p>Group name: group</p> </li> </ul>"}}}, "DeleteGroupResponse": {"type": "structure", "members": {}}, "DeleteIdentityCenterApplicationRequest": {"type": "structure", "required": ["ApplicationArn"], "members": {"ApplicationArn": {"shape": "ApplicationArn", "documentation": "<p> The Amazon Resource Name (ARN) of the application. </p>"}}}, "DeleteIdentityCenterApplicationResponse": {"type": "structure", "members": {}}, "DeleteIdentityProviderConfigurationRequest": {"type": "structure", "required": ["OrganizationId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p> The Organization ID. </p>"}}}, "DeleteIdentityProviderConfigurationResponse": {"type": "structure", "members": {}}, "DeleteImpersonationRoleRequest": {"type": "structure", "required": ["OrganizationId", "ImpersonationRoleId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The WorkMail organization from which to delete the impersonation role.</p>"}, "ImpersonationRoleId": {"shape": "ImpersonationRoleId", "documentation": "<p>The ID of the impersonation role to delete.</p>"}}}, "DeleteImpersonationRoleResponse": {"type": "structure", "members": {}}, "DeleteMailboxPermissionsRequest": {"type": "structure", "required": ["OrganizationId", "EntityId", "GranteeId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The identifier of the organization under which the member (user or group) exists.</p>"}, "EntityId": {"shape": "EntityIdentifier", "documentation": "<p>The identifier of the entity that owns the mailbox.</p> <p>The identifier can be <i>UserId or Group Id</i>, <i>Username or Groupname</i>, or <i>email</i>.</p> <ul> <li> <p>Entity ID: 12345678-1234-1234-1234-**********12, r-0123456789a0123456789b0123456789, or S-1-1-12-**********-123456789-123456789-1234</p> </li> <li> <p>Email address: <EMAIL></p> </li> <li> <p>Entity name: entity</p> </li> </ul>"}, "GranteeId": {"shape": "EntityIdentifier", "documentation": "<p>The identifier of the entity for which to delete granted permissions.</p> <p>The identifier can be <i>UserId, ResourceID, or Group Id</i>, <i>Username or Groupname</i>, or <i>email</i>.</p> <ul> <li> <p>Grantee ID: 12345678-1234-1234-1234-**********12,r-0123456789a0123456789b0123456789, or S-1-1-12-**********-123456789-123456789-1234</p> </li> <li> <p>Email address: <EMAIL></p> </li> <li> <p>Grantee name: grantee</p> </li> </ul>"}}}, "DeleteMailboxPermissionsResponse": {"type": "structure", "members": {}}, "DeleteMobileDeviceAccessOverrideRequest": {"type": "structure", "required": ["OrganizationId", "UserId", "DeviceId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The WorkMail organization for which the access override will be deleted.</p>"}, "UserId": {"shape": "EntityIdentifier", "documentation": "<p>The WorkMail user for which you want to delete the override. Accepts the following types of user identities:</p> <ul> <li> <p>User ID: <code>12345678-1234-1234-1234-**********12</code> or <code>S-1-1-12-**********-123456789-123456789-1234</code> </p> </li> <li> <p>Email address: <code><EMAIL></code> </p> </li> <li> <p>User name: <code>user</code> </p> </li> </ul>"}, "DeviceId": {"shape": "DeviceId", "documentation": "<p>The mobile device for which you delete the override. <code>DeviceId</code> is case insensitive.</p>"}}}, "DeleteMobileDeviceAccessOverrideResponse": {"type": "structure", "members": {}}, "DeleteMobileDeviceAccessRuleRequest": {"type": "structure", "required": ["OrganizationId", "MobileDeviceAccessRuleId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The WorkMail organization under which the rule will be deleted.</p>"}, "MobileDeviceAccessRuleId": {"shape": "MobileDeviceAccessRuleId", "documentation": "<p>The identifier of the rule to be deleted.</p>"}}}, "DeleteMobileDeviceAccessRuleResponse": {"type": "structure", "members": {}}, "DeleteOrganizationRequest": {"type": "structure", "required": ["OrganizationId", "DeleteDirectory"], "members": {"ClientToken": {"shape": "IdempotencyClientToken", "documentation": "<p>The idempotency token associated with the request.</p>", "idempotencyToken": true}, "OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The organization ID.</p>"}, "DeleteDirectory": {"shape": "Boolean", "documentation": "<p>If true, deletes the AWS Directory Service directory associated with the organization.</p>"}, "ForceDelete": {"shape": "Boolean", "documentation": "<p>Deletes a WorkMail organization even if the organization has enabled users.</p>"}, "DeleteIdentityCenterApplication": {"shape": "Boolean", "documentation": "<p>Deletes IAM Identity Center application for WorkMail. This action does not affect authentication settings for any organization.</p>"}}}, "DeleteOrganizationResponse": {"type": "structure", "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The organization ID.</p>"}, "State": {"shape": "String", "documentation": "<p>The state of the organization.</p>"}}}, "DeletePersonalAccessTokenRequest": {"type": "structure", "required": ["OrganizationId", "PersonalAccessTokenId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p> The Organization ID. </p>"}, "PersonalAccessTokenId": {"shape": "PersonalAccessTokenId", "documentation": "<p> The Personal Access Token ID.</p>"}}}, "DeletePersonalAccessTokenResponse": {"type": "structure", "members": {}}, "DeleteResourceRequest": {"type": "structure", "required": ["OrganizationId", "ResourceId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The identifier associated with the organization from which the resource is deleted.</p>"}, "ResourceId": {"shape": "EntityIdentifier", "documentation": "<p>The identifier of the resource to be deleted.</p> <p>The identifier can accept <i>ResourceId</i>, or <i>Resourcename</i>. The following identity formats are available:</p> <ul> <li> <p>Resource ID: r-0123456789a0123456789b0123456789</p> </li> <li> <p>Resource name: resource</p> </li> </ul>"}}}, "DeleteResourceResponse": {"type": "structure", "members": {}}, "DeleteRetentionPolicyRequest": {"type": "structure", "required": ["OrganizationId", "Id"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The organization ID.</p>"}, "Id": {"shape": "ShortString", "documentation": "<p>The retention policy ID.</p>"}}}, "DeleteRetentionPolicyResponse": {"type": "structure", "members": {}}, "DeleteUserRequest": {"type": "structure", "required": ["OrganizationId", "UserId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The organization that contains the user to be deleted.</p>"}, "UserId": {"shape": "EntityIdentifier", "documentation": "<p>The identifier of the user to be deleted.</p> <p>The identifier can be the <i>UserId</i> or <i>Username</i>. The following identity formats are available:</p> <ul> <li> <p>User ID: 12345678-1234-1234-1234-**********12 or S-1-1-12-**********-123456789-123456789-1234</p> </li> <li> <p>User name: user</p> </li> </ul>"}}}, "DeleteUserResponse": {"type": "structure", "members": {}}, "DeregisterFromWorkMailRequest": {"type": "structure", "required": ["OrganizationId", "EntityId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The identifier for the organization under which the WorkMail entity exists.</p>"}, "EntityId": {"shape": "EntityIdentifier", "documentation": "<p>The identifier for the member to be updated.</p> <p>The identifier can be <i>UserId, ResourceId, or Group Id</i>, <i>Username, Resourcename, or Groupname</i>, or <i>email</i>.</p> <ul> <li> <p>Entity ID: 12345678-1234-1234-1234-**********12, r-0123456789a0123456789b0123456789, or S-1-1-12-**********-123456789-123456789-1234</p> </li> <li> <p>Email address: <EMAIL></p> </li> <li> <p>Entity name: entity</p> </li> </ul>"}}}, "DeregisterFromWorkMailResponse": {"type": "structure", "members": {}}, "DeregisterMailDomainRequest": {"type": "structure", "required": ["OrganizationId", "DomainName"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The WorkMail organization for which the domain will be deregistered.</p>"}, "DomainName": {"shape": "WorkMailDomainName", "documentation": "<p>The domain to deregister in WorkMail and SES.</p>"}}}, "DeregisterMailDomainResponse": {"type": "structure", "members": {}}, "DescribeEmailMonitoringConfigurationRequest": {"type": "structure", "required": ["OrganizationId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The ID of the organization for which the email monitoring configuration is described.</p>"}}}, "DescribeEmailMonitoringConfigurationResponse": {"type": "structure", "members": {"RoleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM Role associated with the email monitoring configuration.</p>"}, "LogGroupArn": {"shape": "LogGroupArn", "documentation": "<p>The Amazon Resource Name (ARN) of the CloudWatch Log group associated with the email monitoring configuration.</p>"}}}, "DescribeEntityRequest": {"type": "structure", "required": ["OrganizationId", "Email"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The identifier for the organization under which the entity exists.</p>"}, "Email": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The email under which the entity exists.</p>"}}}, "DescribeEntityResponse": {"type": "structure", "members": {"EntityId": {"shape": "WorkMailIdentifier", "documentation": "<p>The entity ID under which the entity exists.</p>"}, "Name": {"shape": "String", "documentation": "<p>Username, GroupName, or ResourceName based on entity type.</p>"}, "Type": {"shape": "EntityType", "documentation": "<p>Entity type.</p>"}}}, "DescribeGroupRequest": {"type": "structure", "required": ["OrganizationId", "GroupId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The identifier for the organization under which the group exists.</p>"}, "GroupId": {"shape": "EntityIdentifier", "documentation": "<p>The identifier for the group to be described.</p> <p>The identifier can accept <i>GroupId</i>, <i>Groupname</i>, or <i>email</i>. The following identity formats are available:</p> <ul> <li> <p>Group ID: 12345678-1234-1234-1234-**********12 or S-1-1-12-**********-123456789-123456789-1234</p> </li> <li> <p>Email address: <EMAIL></p> </li> <li> <p>Group name: group</p> </li> </ul>"}}}, "DescribeGroupResponse": {"type": "structure", "members": {"GroupId": {"shape": "WorkMailIdentifier", "documentation": "<p>The identifier of the described group.</p>"}, "Name": {"shape": "GroupName", "documentation": "<p>The name of the described group.</p>"}, "Email": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The email of the described group.</p>"}, "State": {"shape": "EntityState", "documentation": "<p>The state of the user: enabled (registered to WorkMail) or disabled (deregistered or never registered to WorkMail).</p>"}, "EnabledDate": {"shape": "Timestamp", "documentation": "<p>The date and time when a user was registered to WorkMail, in UNIX epoch time format.</p>"}, "DisabledDate": {"shape": "Timestamp", "documentation": "<p>The date and time when a user was deregistered from WorkMail, in UNIX epoch time format.</p>"}, "HiddenFromGlobalAddressList": {"shape": "Boolean", "documentation": "<p>If the value is set to <i>true</i>, the group is hidden from the address book.</p>"}}}, "DescribeIdentityProviderConfigurationRequest": {"type": "structure", "required": ["OrganizationId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p> The Organization ID. </p>"}}}, "DescribeIdentityProviderConfigurationResponse": {"type": "structure", "members": {"AuthenticationMode": {"shape": "IdentityProviderAuthenticationMode", "documentation": "<p> The authentication mode used in WorkMail.</p>"}, "IdentityCenterConfiguration": {"shape": "IdentityCenterConfiguration", "documentation": "<p> The details of the IAM Identity Center configuration. </p>"}, "PersonalAccessTokenConfiguration": {"shape": "PersonalAccessTokenConfiguration", "documentation": "<p> The details of the Personal Access Token configuration. </p>"}}}, "DescribeInboundDmarcSettingsRequest": {"type": "structure", "required": ["OrganizationId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>Lists the ID of the given organization.</p>"}}}, "DescribeInboundDmarcSettingsResponse": {"type": "structure", "members": {"Enforced": {"shape": "Boolean", "documentation": "<p>Lists the enforcement setting of the applied policy.</p>"}}}, "DescribeMailboxExportJobRequest": {"type": "structure", "required": ["JobId", "OrganizationId"], "members": {"JobId": {"shape": "MailboxExportJobId", "documentation": "<p>The mailbox export job ID.</p>"}, "OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The organization ID.</p>"}}}, "DescribeMailboxExportJobResponse": {"type": "structure", "members": {"EntityId": {"shape": "WorkMailIdentifier", "documentation": "<p>The identifier of the user or resource associated with the mailbox.</p>"}, "Description": {"shape": "Description", "documentation": "<p>The mailbox export job description.</p>"}, "RoleArn": {"shape": "RoleArn", "documentation": "<p>The ARN of the AWS Identity and Access Management (IAM) role that grants write permission to the Amazon Simple Storage Service (Amazon S3) bucket.</p>"}, "KmsKeyArn": {"shape": "KmsKeyArn", "documentation": "<p>The Amazon Resource Name (ARN) of the symmetric AWS Key Management Service (AWS KMS) key that encrypts the exported mailbox content.</p>"}, "S3BucketName": {"shape": "S3BucketName", "documentation": "<p>The name of the S3 bucket.</p>"}, "S3Prefix": {"shape": "S3ObjectKey", "documentation": "<p>The S3 bucket prefix.</p>"}, "S3Path": {"shape": "S3ObjectKey", "documentation": "<p>The path to the S3 bucket and file that the mailbox export job is exporting to.</p>"}, "EstimatedProgress": {"shape": "Percentage", "documentation": "<p>The estimated progress of the mailbox export job, in percentage points.</p>"}, "State": {"shape": "MailboxExportJobState", "documentation": "<p>The state of the mailbox export job.</p>"}, "ErrorInfo": {"shape": "MailboxExportErrorInfo", "documentation": "<p>Error information for failed mailbox export jobs.</p>"}, "StartTime": {"shape": "Timestamp", "documentation": "<p>The mailbox export job start timestamp.</p>"}, "EndTime": {"shape": "Timestamp", "documentation": "<p>The mailbox export job end timestamp.</p>"}}}, "DescribeOrganizationRequest": {"type": "structure", "required": ["OrganizationId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The identifier for the organization to be described.</p>"}}}, "DescribeOrganizationResponse": {"type": "structure", "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The identifier of an organization.</p>"}, "Alias": {"shape": "OrganizationName", "documentation": "<p>The alias for an organization.</p>"}, "State": {"shape": "String", "documentation": "<p>The state of an organization.</p>"}, "DirectoryId": {"shape": "String", "documentation": "<p>The identifier for the directory associated with an WorkMail organization.</p>"}, "DirectoryType": {"shape": "String", "documentation": "<p>The type of directory associated with the WorkMail organization.</p>"}, "DefaultMailDomain": {"shape": "String", "documentation": "<p>The default mail domain associated with the organization.</p>"}, "CompletedDate": {"shape": "Timestamp", "documentation": "<p>The date at which the organization became usable in the WorkMail context, in UNIX epoch time format.</p>"}, "ErrorMessage": {"shape": "String", "documentation": "<p>(Optional) The error message indicating if unexpected behavior was encountered with regards to the organization.</p>"}, "ARN": {"shape": "AmazonResourceName", "documentation": "<p>The Amazon Resource Name (ARN) of the organization.</p>"}, "MigrationAdmin": {"shape": "WorkMailIdentifier", "documentation": "<p>The user ID of the migration admin if migration is enabled for the organization.</p>"}, "InteroperabilityEnabled": {"shape": "Boolean", "documentation": "<p>Indicates if interoperability is enabled for this organization.</p>"}}}, "DescribeResourceRequest": {"type": "structure", "required": ["OrganizationId", "ResourceId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The identifier associated with the organization for which the resource is described.</p>"}, "ResourceId": {"shape": "EntityIdentifier", "documentation": "<p>The identifier of the resource to be described.</p> <p>The identifier can accept <i>ResourceId</i>, <i>Resourcename</i>, or <i>email</i>. The following identity formats are available:</p> <ul> <li> <p>Resource ID: r-0123456789a0123456789b0123456789</p> </li> <li> <p>Email address: <EMAIL></p> </li> <li> <p>Resource name: resource</p> </li> </ul>"}}}, "DescribeResourceResponse": {"type": "structure", "members": {"ResourceId": {"shape": "ResourceId", "documentation": "<p>The identifier of the described resource.</p>"}, "Email": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The email of the described resource.</p>"}, "Name": {"shape": "ResourceName", "documentation": "<p>The name of the described resource.</p>"}, "Type": {"shape": "ResourceType", "documentation": "<p>The type of the described resource.</p>"}, "BookingOptions": {"shape": "BookingOptions", "documentation": "<p>The booking options for the described resource.</p>"}, "State": {"shape": "EntityState", "documentation": "<p>The state of the resource: enabled (registered to WorkMail), disabled (deregistered or never registered to WorkMail), or deleted.</p>"}, "EnabledDate": {"shape": "Timestamp", "documentation": "<p>The date and time when a resource was enabled for WorkMail, in UNIX epoch time format.</p>"}, "DisabledDate": {"shape": "Timestamp", "documentation": "<p>The date and time when a resource was disabled from WorkMail, in UNIX epoch time format.</p>"}, "Description": {"shape": "ResourceDescription", "documentation": "<p>Description of the resource.</p>"}, "HiddenFromGlobalAddressList": {"shape": "Boolean", "documentation": "<p>If enabled, the resource is hidden from the global address list.</p>"}}}, "DescribeUserRequest": {"type": "structure", "required": ["OrganizationId", "UserId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The identifier for the organization under which the user exists.</p>"}, "UserId": {"shape": "EntityIdentifier", "documentation": "<p>The identifier for the user to be described.</p> <p>The identifier can be the <i>UserId</i>, <i>Username</i>, or <i>email</i>. The following identity formats are available:</p> <ul> <li> <p>User ID: 12345678-1234-1234-1234-**********12 or S-1-1-12-**********-123456789-123456789-1234</p> </li> <li> <p>Email address: <EMAIL></p> </li> <li> <p>User name: user</p> </li> </ul> <p/>"}}}, "DescribeUserResponse": {"type": "structure", "members": {"UserId": {"shape": "WorkMailIdentifier", "documentation": "<p>The identifier for the described user.</p>"}, "Name": {"shape": "UserName", "documentation": "<p>The name for the user.</p>"}, "Email": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The email of the user.</p>"}, "DisplayName": {"shape": "UserAttribute", "documentation": "<p>The display name of the user.</p>"}, "State": {"shape": "EntityState", "documentation": "<p>The state of a user: enabled (registered to WorkMail) or disabled (deregistered or never registered to WorkMail).</p>"}, "UserRole": {"shape": "UserRole", "documentation": "<p>In certain cases, other entities are modeled as users. If interoperability is enabled, resources are imported into WorkMail as users. Because different WorkMail organizations rely on different directory types, administrators can distinguish between an unregistered user (account is disabled and has a user role) and the directory administrators. The values are USER, RESOURCE, SYSTEM_USER, and REMOTE_USER.</p>"}, "EnabledDate": {"shape": "Timestamp", "documentation": "<p>The date and time at which the user was enabled for WorkMailusage, in UNIX epoch time format.</p>"}, "DisabledDate": {"shape": "Timestamp", "documentation": "<p>The date and time at which the user was disabled for WorkMail usage, in UNIX epoch time format.</p>"}, "MailboxProvisionedDate": {"shape": "Timestamp", "documentation": "<p>The date when the mailbox was created for the user.</p>"}, "MailboxDeprovisionedDate": {"shape": "Timestamp", "documentation": "<p>The date when the mailbox was removed for the user.</p>"}, "FirstName": {"shape": "UserAttribute", "documentation": "<p>First name of the user.</p>"}, "LastName": {"shape": "UserAttribute", "documentation": "<p>Last name of the user.</p>"}, "HiddenFromGlobalAddressList": {"shape": "Boolean", "documentation": "<p>If enabled, the user is hidden from the global address list.</p>"}, "Initials": {"shape": "UserAttribute", "documentation": "<p>Initials of the user.</p>"}, "Telephone": {"shape": "UserAttribute", "documentation": "<p>User's contact number.</p>"}, "Street": {"shape": "UserAttribute", "documentation": "<p>Street where the user is located.</p>"}, "JobTitle": {"shape": "UserAttribute", "documentation": "<p>Job title of the user.</p>"}, "City": {"shape": "UserAttribute", "documentation": "<p>City where the user is located.</p>"}, "Company": {"shape": "UserAttribute", "documentation": "<p>Company of the user.</p>"}, "ZipCode": {"shape": "UserAttribute", "documentation": "<p>Zip code of the user.</p>"}, "Department": {"shape": "UserAttribute", "documentation": "<p>Department of the user.</p>"}, "Country": {"shape": "UserAttribute", "documentation": "<p>Country where the user is located.</p>"}, "Office": {"shape": "UserAttribute", "documentation": "<p>Office where the user is located.</p>"}, "IdentityProviderUserId": {"shape": "IdentityProviderUserId", "documentation": "<p>User ID from the IAM Identity Center. If this parameter is empty it will be updated automatically when the user logs in for the first time to the mailbox associated with WorkMail.</p>"}, "IdentityProviderIdentityStoreId": {"shape": "IdentityProviderIdentityStoreId", "documentation": "<p> Identity Store ID from the IAM Identity Center. If this parameter is empty it will be updated automatically when the user logs in for the first time to the mailbox associated with WorkMail. </p>"}}}, "Description": {"type": "string", "max": 1023, "min": 0, "pattern": "[\\S\\s]*"}, "DeviceId": {"type": "string", "max": 32, "min": 1, "pattern": "[A-Za-z0-9]+"}, "DeviceModel": {"type": "string", "max": 256, "min": 1, "pattern": "[\\u0020-\\u00FF]+"}, "DeviceModelList": {"type": "list", "member": {"shape": "DeviceModel"}, "max": 10, "min": 1}, "DeviceOperatingSystem": {"type": "string", "max": 256, "min": 1, "pattern": "[\\u0020-\\u00FF]+"}, "DeviceOperatingSystemList": {"type": "list", "member": {"shape": "DeviceOperatingSystem"}, "max": 10, "min": 1}, "DeviceType": {"type": "string", "max": 256, "min": 1, "pattern": "[\\u0020-\\u00FF]+"}, "DeviceTypeList": {"type": "list", "member": {"shape": "DeviceType"}, "max": 10, "min": 1}, "DeviceUserAgent": {"type": "string", "max": 256, "min": 1, "pattern": "[\\u0020-\\u00FF]+"}, "DeviceUserAgentList": {"type": "list", "member": {"shape": "DeviceUserAgent"}, "max": 10, "min": 1}, "DirectoryId": {"type": "string", "max": 12, "min": 12, "pattern": "^d-[0-9a-f]{10}$"}, "DirectoryInUseException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>The directory is already in use by another WorkMail organization in the same account and Region.</p>", "exception": true}, "DirectoryServiceAuthenticationFailedException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>The directory service doesn't recognize the credentials supplied by WorkMail.</p>", "exception": true}, "DirectoryUnavailableException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>The directory is unavailable. It might be located in another Region or deleted.</p>", "exception": true}, "DisassociateDelegateFromResourceRequest": {"type": "structure", "required": ["OrganizationId", "ResourceId", "EntityId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The identifier for the organization under which the resource exists.</p>"}, "ResourceId": {"shape": "EntityIdentifier", "documentation": "<p>The identifier of the resource from which delegates' set members are removed. </p> <p>The identifier can accept <i>ResourceId</i>, <i>Resourcename</i>, or <i>email</i>. The following identity formats are available:</p> <ul> <li> <p>Resource ID: r-0123456789a0123456789b0123456789</p> </li> <li> <p>Email address: <EMAIL></p> </li> <li> <p>Resource name: resource</p> </li> </ul>"}, "EntityId": {"shape": "EntityIdentifier", "documentation": "<p>The identifier for the member (user, group) to be removed from the resource's delegates.</p> <p>The entity ID can accept <i>UserId or GroupID</i>, <i>Username or Groupname</i>, or <i>email</i>.</p> <ul> <li> <p>Entity: 12345678-1234-1234-1234-**********12 or S-1-1-12-**********-123456789-123456789-1234</p> </li> <li> <p>Email address: <EMAIL></p> </li> <li> <p>Entity: entity</p> </li> </ul>"}}}, "DisassociateDelegateFromResourceResponse": {"type": "structure", "members": {}}, "DisassociateMemberFromGroupRequest": {"type": "structure", "required": ["OrganizationId", "GroupId", "MemberId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The identifier for the organization under which the group exists.</p>"}, "GroupId": {"shape": "EntityIdentifier", "documentation": "<p>The identifier for the group from which members are removed.</p> <p>The identifier can accept <i>GroupId</i>, <i>Groupname</i>, or <i>email</i>. The following identity formats are available:</p> <ul> <li> <p>Group ID: 12345678-1234-1234-1234-**********12 or S-1-1-12-**********-123456789-123456789-1234</p> </li> <li> <p>Email address: <EMAIL></p> </li> <li> <p>Group name: group</p> </li> </ul>"}, "MemberId": {"shape": "EntityIdentifier", "documentation": "<p>The identifier for the member to be removed from the group.</p> <p>The member ID can accept <i>UserID or GroupId</i>, <i>Username or Groupname</i>, or <i>email</i>.</p> <ul> <li> <p>Member ID: 12345678-1234-1234-1234-**********12 or S-1-1-12-**********-123456789-123456789-1234</p> </li> <li> <p>Email address: <EMAIL></p> </li> <li> <p>Member name: member</p> </li> </ul>"}}}, "DisassociateMemberFromGroupResponse": {"type": "structure", "members": {}}, "DnsRecord": {"type": "structure", "members": {"Type": {"shape": "String", "documentation": "<p>The RFC 1035 record type. Possible values: <code>CNAME</code>, <code>A</code>, <code>MX</code>.</p>"}, "Hostname": {"shape": "String", "documentation": "<p>The DNS hostname.- For example, <code>domain.example.com</code>.</p>"}, "Value": {"shape": "String", "documentation": "<p>The value returned by the DNS for a query to that hostname and record type.</p>"}}, "documentation": "<p>A DNS record uploaded to your DNS provider.</p>"}, "DnsRecordVerificationStatus": {"type": "string", "enum": ["PENDING", "VERIFIED", "FAILED"]}, "DnsRecords": {"type": "list", "member": {"shape": "DnsRecord"}}, "Domain": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"shape": "DomainName", "documentation": "<p>The fully qualified domain name.</p>"}, "HostedZoneId": {"shape": "HostedZoneId", "documentation": "<p>The hosted zone ID for a domain hosted in Route 53. Required when configuring a domain hosted in Route 53.</p>"}}, "documentation": "<p>The domain to associate with an WorkMail organization.</p> <p>When you configure a domain hosted in Amazon Route 53 (Route 53), all recommended DNS records are added to the organization when you create it. For more information, see <a href=\"https://docs.aws.amazon.com/workmail/latest/adminguide/add_domain.html\">Adding a domain</a> in the <i>WorkMail Administrator Guide</i>.</p>"}, "DomainName": {"type": "string", "max": 255, "min": 3, "pattern": "[a-zA-Z0-9.-]+"}, "Domains": {"type": "list", "member": {"shape": "Domain"}, "max": 5, "min": 0}, "EmailAddress": {"type": "string", "max": 254, "min": 1, "pattern": "[a-zA-Z0-9._%+-]{1,64}@[a-zA-Z0-9.-]+\\.[a-zA-Z-]{2,}"}, "EmailAddressInUseException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>The email address that you're trying to assign is already created for a different user, group, or resource.</p>", "exception": true}, "EntityAlreadyRegisteredException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>The user, group, or resource that you're trying to register is already registered.</p>", "exception": true}, "EntityIdentifier": {"type": "string", "max": 256, "min": 1, "pattern": "[a-zA-Z0-9._%+@-]+"}, "EntityNotFoundException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>The identifier supplied for the user, group, or resource does not exist in your organization.</p>", "exception": true}, "EntityState": {"type": "string", "enum": ["ENABLED", "DISABLED", "DELETED"]}, "EntityStateException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>You are performing an operation on a user, group, or resource that isn't in the expected state, such as trying to delete an active user.</p>", "exception": true}, "EntityType": {"type": "string", "enum": ["GROUP", "USER", "RESOURCE"]}, "EwsAvailabilityProvider": {"type": "structure", "required": ["EwsEndpoint", "EwsUsername", "EwsPassword"], "members": {"EwsEndpoint": {"shape": "Url", "documentation": "<p>The endpoint of the remote EWS server.</p>"}, "EwsUsername": {"shape": "ExternalUserName", "documentation": "<p>The username used to authenticate the remote EWS server.</p>"}, "EwsPassword": {"shape": "Password", "documentation": "<p>The password used to authenticate the remote EWS server.</p>"}}, "documentation": "<p>Describes an EWS based availability provider. This is only used as input to the service.</p>"}, "ExpiresIn": {"type": "long", "box": true}, "ExternalUserName": {"type": "string", "max": 256, "pattern": "[\\u0020-\\u00FF]+"}, "FolderConfiguration": {"type": "structure", "required": ["Name", "Action"], "members": {"Name": {"shape": "FolderName", "documentation": "<p>The folder name.</p>"}, "Action": {"shape": "RetentionAction", "documentation": "<p>The action to take on the folder contents at the end of the folder configuration period.</p>"}, "Period": {"shape": "RetentionPeriod", "documentation": "<p>The number of days for which the folder-configuration action applies.</p>"}}, "documentation": "<p>The configuration applied to an organization's folders by its retention policy.</p>"}, "FolderConfigurations": {"type": "list", "member": {"shape": "FolderConfiguration"}}, "FolderName": {"type": "string", "enum": ["INBOX", "DELETED_ITEMS", "SENT_ITEMS", "DRAFTS", "JUNK_EMAIL"]}, "GetAccessControlEffectRequest": {"type": "structure", "required": ["OrganizationId", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Action"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The identifier for the organization.</p>"}, "IpAddress": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The IPv4 address.</p>"}, "Action": {"shape": "AccessControlRuleAction", "documentation": "<p>The access protocol action. Valid values include <code>ActiveSync</code>, <code>AutoDiscover</code>, <code>EWS</code>, <code>IMAP</code>, <code>SMTP</code>, <code>WindowsOutlook</code>, and <code>WebMail</code>.</p>"}, "UserId": {"shape": "WorkMailIdentifier", "documentation": "<p>The user ID.</p>"}, "ImpersonationRoleId": {"shape": "ImpersonationRoleId", "documentation": "<p>The impersonation role ID.</p>"}}}, "GetAccessControlEffectResponse": {"type": "structure", "members": {"Effect": {"shape": "AccessControlRuleEffect", "documentation": "<p>The rule effect.</p>"}, "MatchedRules": {"shape": "AccessControlRuleNameList", "documentation": "<p>The rules that match the given parameters, resulting in an effect.</p>"}}}, "GetDefaultRetentionPolicyRequest": {"type": "structure", "required": ["OrganizationId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The organization ID.</p>"}}}, "GetDefaultRetentionPolicyResponse": {"type": "structure", "members": {"Id": {"shape": "ShortString", "documentation": "<p>The retention policy ID.</p>"}, "Name": {"shape": "ShortString", "documentation": "<p>The retention policy name.</p>"}, "Description": {"shape": "String", "documentation": "<p>The retention policy description.</p>"}, "FolderConfigurations": {"shape": "FolderConfigurations", "documentation": "<p>The retention policy folder configurations.</p>"}}}, "GetImpersonationRoleEffectRequest": {"type": "structure", "required": ["OrganizationId", "ImpersonationRoleId", "TargetUser"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The WorkMail organization where the impersonation role is defined.</p>"}, "ImpersonationRoleId": {"shape": "ImpersonationRoleId", "documentation": "<p>The impersonation role ID to test.</p>"}, "TargetUser": {"shape": "EntityIdentifier", "documentation": "<p>The WorkMail organization user chosen to test the impersonation role. The following identity formats are available:</p> <ul> <li> <p>User ID: <code>12345678-1234-1234-1234-**********12</code> or <code>S-1-1-12-**********-123456789-123456789-1234</code> </p> </li> <li> <p>Email address: <code><EMAIL></code> </p> </li> <li> <p>User name: <code>user</code> </p> </li> </ul>"}}}, "GetImpersonationRoleEffectResponse": {"type": "structure", "members": {"Type": {"shape": "ImpersonationRoleType", "documentation": "<p>The impersonation role type.</p>"}, "Effect": {"shape": "AccessEffect", "documentation": "<p> <code/>Effect of the impersonation role on the target user based on its rules. Available effects are <code>ALLOW</code> or <code>DENY</code>.</p>"}, "MatchedRules": {"shape": "ImpersonationMatchedRuleList", "documentation": "<p>A list of the rules that match the input and produce the configured effect.</p>"}}}, "GetImpersonationRoleRequest": {"type": "structure", "required": ["OrganizationId", "ImpersonationRoleId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The WorkMail organization from which to retrieve the impersonation role.</p>"}, "ImpersonationRoleId": {"shape": "ImpersonationRoleId", "documentation": "<p>The impersonation role ID to retrieve.</p>"}}}, "GetImpersonationRoleResponse": {"type": "structure", "members": {"ImpersonationRoleId": {"shape": "ImpersonationRoleId", "documentation": "<p>The impersonation role ID.</p>"}, "Name": {"shape": "ImpersonationRoleName", "documentation": "<p>The impersonation role name.</p>"}, "Type": {"shape": "ImpersonationRoleType", "documentation": "<p>The impersonation role type.</p>"}, "Description": {"shape": "ImpersonationRoleDescription", "documentation": "<p>The impersonation role description.</p>"}, "Rules": {"shape": "ImpersonationRuleList", "documentation": "<p>The list of rules for the given impersonation role.</p>"}, "DateCreated": {"shape": "Timestamp", "documentation": "<p>The date when the impersonation role was created.</p>"}, "DateModified": {"shape": "Timestamp", "documentation": "<p>The date when the impersonation role was last modified.</p>"}}}, "GetMailDomainRequest": {"type": "structure", "required": ["OrganizationId", "DomainName"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The WorkMail organization for which the domain is retrieved.</p>"}, "DomainName": {"shape": "WorkMailDomainName", "documentation": "<p>The domain from which you want to retrieve details.</p>"}}}, "GetMailDomainResponse": {"type": "structure", "members": {"Records": {"shape": "DnsRecords", "documentation": "<p>A list of the DNS records that WorkMail recommends adding in your DNS provider for the best user experience. The records configure your domain with DMARC, SPF, DKIM, and direct incoming email traffic to SES. See admin guide for more details.</p>"}, "IsTestDomain": {"shape": "Boolean", "documentation": "<p>Specifies whether the domain is a test domain provided by WorkMail, or a custom domain.</p>"}, "IsDefault": {"shape": "Boolean", "documentation": "<p>Specifies whether the domain is the default domain for your organization.</p>"}, "OwnershipVerificationStatus": {"shape": "DnsRecordVerificationStatus", "documentation": "<p> Indicates the status of the domain ownership verification.</p>"}, "DkimVerificationStatus": {"shape": "DnsRecordVerificationStatus", "documentation": "<p>Indicates the status of a DKIM verification.</p>"}}}, "GetMailboxDetailsRequest": {"type": "structure", "required": ["OrganizationId", "UserId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The identifier for the organization that contains the user whose mailbox details are being requested.</p>"}, "UserId": {"shape": "EntityIdentifier", "documentation": "<p>The identifier for the user whose mailbox details are being requested.</p> <p>The identifier can be the <i>UserId</i>, <i>Username</i>, or <i>email</i>. The following identity formats are available:</p> <ul> <li> <p>User ID: 12345678-1234-1234-1234-**********12 or S-1-1-12-**********-123456789-123456789-1234</p> </li> <li> <p>Email address: <EMAIL></p> </li> <li> <p>User name: user</p> </li> </ul>"}}}, "GetMailboxDetailsResponse": {"type": "structure", "members": {"MailboxQuota": {"shape": "MailboxQuota", "documentation": "<p>The maximum allowed mailbox size, in MB, for the specified user.</p>"}, "MailboxSize": {"shape": "MailboxSize", "documentation": "<p>The current mailbox size, in MB, for the specified user.</p>"}}}, "GetMobileDeviceAccessEffectRequest": {"type": "structure", "required": ["OrganizationId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The WorkMail organization to simulate the access effect for.</p>"}, "DeviceType": {"shape": "DeviceType", "documentation": "<p>Device type the simulated user will report.</p>"}, "DeviceModel": {"shape": "DeviceModel", "documentation": "<p>Device model the simulated user will report.</p>"}, "DeviceOperatingSystem": {"shape": "DeviceOperatingSystem", "documentation": "<p>Device operating system the simulated user will report.</p>"}, "DeviceUserAgent": {"shape": "DeviceUserAgent", "documentation": "<p>Device user agent the simulated user will report.</p>"}}}, "GetMobileDeviceAccessEffectResponse": {"type": "structure", "members": {"Effect": {"shape": "MobileDeviceAccessRuleEffect", "documentation": "<p>The effect of the simulated access, <code>ALLOW</code> or <code>DENY</code>, after evaluating mobile device access rules in the WorkMail organization for the simulated user parameters.</p>"}, "MatchedRules": {"shape": "MobileDeviceAccessMatchedRuleList", "documentation": "<p>A list of the rules which matched the simulated user input and produced the effect.</p>"}}}, "GetMobileDeviceAccessOverrideRequest": {"type": "structure", "required": ["OrganizationId", "UserId", "DeviceId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The WorkMail organization to which you want to apply the override.</p>"}, "UserId": {"shape": "EntityIdentifier", "documentation": "<p>Identifies the WorkMail user for the override. Accepts the following types of user identities: </p> <ul> <li> <p>User ID: <code>12345678-1234-1234-1234-**********12</code> or <code>S-1-1-12-**********-123456789-123456789-1234</code> </p> </li> <li> <p>Email address: <code><EMAIL></code> </p> </li> <li> <p>User name: <code>user</code> </p> </li> </ul>"}, "DeviceId": {"shape": "DeviceId", "documentation": "<p>The mobile device to which the override applies. <code>DeviceId</code> is case insensitive.</p>"}}}, "GetMobileDeviceAccessOverrideResponse": {"type": "structure", "members": {"UserId": {"shape": "WorkMailIdentifier", "documentation": "<p>The WorkMail user to which the access override applies.</p>"}, "DeviceId": {"shape": "DeviceId", "documentation": "<p>The device to which the access override applies.</p>"}, "Effect": {"shape": "MobileDeviceAccessRuleEffect", "documentation": "<p>The effect of the override, <code>ALLOW</code> or <code>DENY</code>.</p>"}, "Description": {"shape": "MobileDeviceAccessRuleDescription", "documentation": "<p>A description of the override.</p>"}, "DateCreated": {"shape": "Timestamp", "documentation": "<p>The date the override was first created.</p>"}, "DateModified": {"shape": "Timestamp", "documentation": "<p>The date the description was last modified.</p>"}}}, "GetPersonalAccessTokenMetadataRequest": {"type": "structure", "required": ["OrganizationId", "PersonalAccessTokenId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p> The Organization ID. </p>"}, "PersonalAccessTokenId": {"shape": "PersonalAccessTokenId", "documentation": "<p> The Personal Access Token ID.</p>"}}}, "GetPersonalAccessTokenMetadataResponse": {"type": "structure", "members": {"PersonalAccessTokenId": {"shape": "PersonalAccessTokenId", "documentation": "<p> The Personal Access Token ID.</p>"}, "UserId": {"shape": "WorkMailIdentifier", "documentation": "<p> The WorkMail User ID. </p>"}, "Name": {"shape": "PersonalAccessTokenName", "documentation": "<p> The Personal Access Token name. </p>"}, "DateCreated": {"shape": "Timestamp", "documentation": "<p> The date when the Personal Access Token ID was created. </p>"}, "DateLastUsed": {"shape": "Timestamp", "documentation": "<p> The date when the Personal Access Token ID was last used. </p>"}, "ExpiresTime": {"shape": "Timestamp", "documentation": "<p> The time when the Personal Access Token ID will expire. </p>"}, "Scopes": {"shape": "PersonalAccessTokenScopeList", "documentation": "<p> Lists all the Personal Access Token permissions for a mailbox. </p>"}}}, "Group": {"type": "structure", "members": {"Id": {"shape": "WorkMailIdentifier", "documentation": "<p>The identifier of the group.</p>"}, "Email": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The email of the group.</p>"}, "Name": {"shape": "GroupName", "documentation": "<p>The name of the group.</p>"}, "State": {"shape": "EntityState", "documentation": "<p>The state of the group, which can be ENABLED, DISABLED, or DELETED.</p>"}, "EnabledDate": {"shape": "Timestamp", "documentation": "<p>The date indicating when the group was enabled for WorkMail use.</p>"}, "DisabledDate": {"shape": "Timestamp", "documentation": "<p>The date indicating when the group was disabled from WorkMail use.</p>"}}, "documentation": "<p>The representation of an WorkMail group.</p>"}, "GroupIdentifier": {"type": "structure", "members": {"GroupId": {"shape": "WorkMailIdentifier", "documentation": "<p>Group ID that matched the group.</p>"}, "GroupName": {"shape": "GroupName", "documentation": "<p>Group name that matched the group.</p>"}}, "documentation": "<p>The identifier that contains the Group ID and name of a group.</p>"}, "GroupIdentifiers": {"type": "list", "member": {"shape": "GroupIdentifier"}}, "GroupName": {"type": "string", "max": 256, "min": 1, "pattern": "[\\u0020-\\u00FF]+"}, "Groups": {"type": "list", "member": {"shape": "Group"}}, "HostedZoneId": {"type": "string", "max": 32, "min": 1, "pattern": "[^/\\\\]*"}, "IdempotencyClientToken": {"type": "string", "max": 128, "min": 1, "pattern": "[\\x21-\\x7e]+"}, "IdentityCenterApplicationName": {"type": "string", "max": 255, "min": 0, "pattern": "^[\\w+=,.@-]+$"}, "IdentityCenterConfiguration": {"type": "structure", "required": ["InstanceArn", "ApplicationArn"], "members": {"InstanceArn": {"shape": "InstanceArn", "documentation": "<p> The Amazon Resource Name (ARN) of the of IAM Identity Center instance. Must be in the same AWS account and region as WorkMail organization.</p>"}, "ApplicationArn": {"shape": "ApplicationArn", "documentation": "<p> The Amazon Resource Name (ARN) of IAMIdentity Center Application for WorkMail. Must be created by the WorkMail API, see CreateIdentityCenterApplication.</p>"}}, "documentation": "<p> The IAM Identity Center configuration. </p>"}, "IdentityProviderAuthenticationMode": {"type": "string", "enum": ["IDENTITY_PROVIDER_ONLY", "IDENTITY_PROVIDER_AND_DIRECTORY"]}, "IdentityProviderIdentityStoreId": {"type": "string", "max": 36, "min": 1, "pattern": "^d-[0-9a-f]{10}$|^[0-9a-f]{8}\\\\b-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-\\\\b[0-9a-f]{12}$"}, "IdentityProviderUserId": {"type": "string", "max": 47, "min": 1, "pattern": "^([0-9a-f]{10}-|)[A-Fa-f0-9]{8}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{12}$"}, "IdentityProviderUserIdForUpdate": {"type": "string", "max": 47, "min": 0, "pattern": "^$|^([0-9a-f]{10}-|)[A-Fa-f0-9]{8}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{12}$"}, "IdentityProviderUserIdPrefix": {"type": "string", "max": 47, "min": 1, "pattern": "^[A-Fa-f0-9-]+$"}, "ImpersonationMatchedRule": {"type": "structure", "members": {"ImpersonationRuleId": {"shape": "ImpersonationRuleId", "documentation": "<p>The ID of the rule that matched the input</p>"}, "Name": {"shape": "ImpersonationRuleName", "documentation": "<p>The name of the rule that matched the input.</p>"}}, "documentation": "<p>The impersonation rule that matched the input.</p>"}, "ImpersonationMatchedRuleList": {"type": "list", "member": {"shape": "ImpersonationMatchedRule"}, "max": 10, "min": 0}, "ImpersonationRole": {"type": "structure", "members": {"ImpersonationRoleId": {"shape": "ImpersonationRoleId", "documentation": "<p>The identifier of the impersonation role.</p>"}, "Name": {"shape": "ImpersonationRoleName", "documentation": "<p>The impersonation role name.</p>"}, "Type": {"shape": "ImpersonationRoleType", "documentation": "<p>The impersonation role type.</p>"}, "DateCreated": {"shape": "Timestamp", "documentation": "<p>The date when the impersonation role was created.</p>"}, "DateModified": {"shape": "Timestamp", "documentation": "<p>The date when the impersonation role was last modified.</p>"}}, "documentation": "<p>An impersonation role for the given WorkMail organization.</p>"}, "ImpersonationRoleDescription": {"type": "string", "max": 256, "min": 1, "pattern": "[^\\x00-\\x09\\x0B\\x0C\\x0E-\\x1F\\x7F\\x3C\\x3E\\x5C]+"}, "ImpersonationRoleId": {"type": "string", "max": 64, "min": 1, "pattern": "[a-zA-Z0-9_-]+"}, "ImpersonationRoleIdList": {"type": "list", "member": {"shape": "ImpersonationRoleId"}, "max": 10, "min": 0}, "ImpersonationRoleList": {"type": "list", "member": {"shape": "ImpersonationRole"}, "max": 20, "min": 0}, "ImpersonationRoleName": {"type": "string", "max": 64, "min": 1, "pattern": "[^\\x00-\\x1F\\x7F\\x3C\\x3E\\x5C]+"}, "ImpersonationRoleType": {"type": "string", "enum": ["FULL_ACCESS", "READ_ONLY"]}, "ImpersonationRule": {"type": "structure", "required": ["ImpersonationRuleId", "Effect"], "members": {"ImpersonationRuleId": {"shape": "ImpersonationRuleId", "documentation": "<p>The identifier of the rule.</p>"}, "Name": {"shape": "ImpersonationRuleName", "documentation": "<p>The rule name.</p>"}, "Description": {"shape": "ImpersonationRuleDescription", "documentation": "<p>The rule description.</p>"}, "Effect": {"shape": "AccessEffect", "documentation": "<p>The effect of the rule when it matches the input. Allowed effect values are <code>ALLOW</code> or <code>DENY</code>.</p>"}, "TargetUsers": {"shape": "TargetUsers", "documentation": "<p>A list of user IDs that match the rule.</p>"}, "NotTargetUsers": {"shape": "TargetUsers", "documentation": "<p>A list of user IDs that don't match the rule.</p>"}}, "documentation": "<p>The rules for the given impersonation role.</p>"}, "ImpersonationRuleDescription": {"type": "string", "max": 256, "min": 1, "pattern": "[^\\x00-\\x09\\x0B\\x0C\\x0E-\\x1F\\x7F\\x3C\\x3E\\x5C]+"}, "ImpersonationRuleId": {"type": "string", "max": 64, "min": 1, "pattern": "[a-zA-Z0-9_-]+"}, "ImpersonationRuleList": {"type": "list", "member": {"shape": "ImpersonationRule"}, "max": 10, "min": 0}, "ImpersonationRuleName": {"type": "string", "max": 64, "min": 1, "pattern": "[^\\x00-\\x1F\\x7F\\x3C\\x3E\\x5C]+"}, "ImpersonationToken": {"type": "string", "max": 256, "min": 1, "pattern": "[\\x21-\\x7e]+"}, "InstanceArn": {"type": "string", "max": 1124, "min": 10, "pattern": "^arn:(aws|aws-us-gov|aws-cn|aws-iso|aws-iso-b):sso:::instance/(sso)?ins-[a-zA-Z0-9-.]{16}$"}, "InvalidConfigurationException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>The configuration for a resource isn't valid. A resource must either be able to auto-respond to requests or have at least one delegate associated that can do so on its behalf.</p>", "exception": true}, "InvalidCustomSesConfigurationException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>You SES configuration has customizations that WorkMail cannot save. The error message lists the invalid setting. For examples of invalid settings, refer to <a href=\"https://docs.aws.amazon.com/ses/latest/APIReference/API_CreateReceiptRule.html\">CreateReceiptRule</a>.</p>", "exception": true}, "InvalidParameterException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>One or more of the input parameters don't match the service's restrictions.</p>", "exception": true}, "InvalidPasswordException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>The supplied password doesn't match the minimum security constraints, such as length or use of special characters.</p>", "exception": true}, "IpAddress": {"type": "string", "max": 15, "min": 1, "pattern": "^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$"}, "IpRange": {"type": "string", "max": 18, "min": 1, "pattern": "^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])/([0-9]|[12][0-9]|3[0-2])$"}, "IpRangeList": {"type": "list", "member": {"shape": "IpRange"}, "max": 1024, "min": 0}, "Jobs": {"type": "list", "member": {"shape": "MailboxExportJob"}}, "KmsKeyArn": {"type": "string", "max": 2048, "min": 20, "pattern": "arn:aws:kms:[a-z0-9-]*:[a-z0-9-]+:[A-Za-z0-9][A-Za-z0-9:_/+=,@.-]{0,1023}"}, "LambdaArn": {"type": "string", "max": 256, "min": 49, "pattern": "arn:aws:lambda:[a-z]{2}-[a-z]+-\\d{1}:\\d{12}:function:[a-zA-Z0-9\\-_\\.]+(:(\\$LATEST|[a-zA-Z0-9\\-_]+))?"}, "LambdaAvailabilityProvider": {"type": "structure", "required": ["LambdaArn"], "members": {"LambdaArn": {"shape": "LambdaArn", "documentation": "<p>The Amazon Resource Name (ARN) of the Lambda that acts as the availability provider.</p>"}}, "documentation": "<p>Describes a Lambda based availability provider.</p>"}, "LimitExceededException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>The request exceeds the limit of the resource.</p>", "exception": true}, "ListAccessControlRulesRequest": {"type": "structure", "required": ["OrganizationId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The identifier for the organization.</p>"}}}, "ListAccessControlRulesResponse": {"type": "structure", "members": {"Rules": {"shape": "AccessControlRulesList", "documentation": "<p>The access control rules.</p>"}}}, "ListAliasesRequest": {"type": "structure", "required": ["OrganizationId", "EntityId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The identifier for the organization under which the entity exists.</p>"}, "EntityId": {"shape": "WorkMailIdentifier", "documentation": "<p>The identifier for the entity for which to list the aliases.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to use to retrieve the next page of results. The first call does not contain any tokens.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in a single call.</p>"}}}, "ListAliasesResponse": {"type": "structure", "members": {"Aliases": {"shape": "Aliases", "documentation": "<p>The entity's paginated aliases.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to use to retrieve the next page of results. The value is \"null\" when there are no more results to return.</p>"}}}, "ListAvailabilityConfigurationsRequest": {"type": "structure", "required": ["OrganizationId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The WorkMail organization for which the <code>AvailabilityConfiguration</code>'s will be listed.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in a single call.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to use to retrieve the next page of results. The first call does not require a token.</p>"}}}, "ListAvailabilityConfigurationsResponse": {"type": "structure", "members": {"AvailabilityConfigurations": {"shape": "AvailabilityConfigurationList", "documentation": "<p>The list of <code>AvailabilityConfiguration</code>'s that exist for the specified WorkMail organization.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to use to retrieve the next page of results. The value is <code>null</code> when there are no further results to return.</p>"}}}, "ListGroupMembersRequest": {"type": "structure", "required": ["OrganizationId", "GroupId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The identifier for the organization under which the group exists.</p>"}, "GroupId": {"shape": "EntityIdentifier", "documentation": "<p>The identifier for the group to which the members (users or groups) are associated.</p> <p>The identifier can accept <i>GroupId</i>, <i>Groupname</i>, or <i>email</i>. The following identity formats are available:</p> <ul> <li> <p>Group ID: 12345678-1234-1234-1234-**********12 or S-1-1-12-**********-123456789-123456789-1234</p> </li> <li> <p>Email address: <EMAIL></p> </li> <li> <p>Group name: group</p> </li> </ul>"}, "NextToken": {"shape": "NextToken", "documentation": "<p> The token to use to retrieve the next page of results. The first call does not contain any tokens.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in a single call.</p>"}}}, "ListGroupMembersResponse": {"type": "structure", "members": {"Members": {"shape": "Members", "documentation": "<p>The members associated to the group.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to use to retrieve the next page of results. The first call does not contain any tokens.</p>"}}}, "ListGroupsFilters": {"type": "structure", "members": {"NamePrefix": {"shape": "String", "documentation": "<p>Filters only groups with the provided name prefix.</p>"}, "PrimaryEmailPrefix": {"shape": "String", "documentation": "<p>Filters only groups with the provided primary email prefix.</p>"}, "State": {"shape": "EntityState", "documentation": "<p>Filters only groups with the provided state.</p>"}}, "documentation": "<p> Filtering options for <i>ListGroups</i> operation. This is only used as input to Operation.</p>"}, "ListGroupsForEntityFilters": {"type": "structure", "members": {"GroupNamePrefix": {"shape": "String", "documentation": "<p>Filters only group names that start with the provided name prefix.</p>"}}, "documentation": "<p> Filtering options for <i>ListGroupsForEntity</i> operation. This is only used as input to Operation.</p>"}, "ListGroupsForEntityRequest": {"type": "structure", "required": ["OrganizationId", "EntityId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The identifier for the organization under which the entity exists.</p>"}, "EntityId": {"shape": "EntityIdentifier", "documentation": "<p>The identifier for the entity.</p> <p>The entity ID can accept <i>UserId or GroupID</i>, <i>Username or Groupname</i>, or <i>email</i>.</p> <ul> <li> <p>Entity ID: 12345678-1234-1234-1234-**********12 or S-1-1-12-**********-123456789-123456789-1234</p> </li> <li> <p>Email address: <EMAIL></p> </li> <li> <p>Entity name: entity</p> </li> </ul>"}, "Filters": {"shape": "ListGroupsForEntityFilters", "documentation": "<p>Limit the search results based on the filter criteria.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to use to retrieve the next page of results. The first call does not contain any tokens.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in a single call.</p>"}}}, "ListGroupsForEntityResponse": {"type": "structure", "members": {"Groups": {"shape": "GroupIdentifiers", "documentation": "<p>The overview of groups in an organization.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to use to retrieve the next page of results. This value is `null` when there are no more results to return.</p>"}}}, "ListGroupsRequest": {"type": "structure", "required": ["OrganizationId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The identifier for the organization under which the groups exist.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to use to retrieve the next page of results. The first call does not contain any tokens.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in a single call.</p>"}, "Filters": {"shape": "ListGroupsFilters", "documentation": "<p>Limit the search results based on the filter criteria. Only one filter per request is supported.</p>"}}}, "ListGroupsResponse": {"type": "structure", "members": {"Groups": {"shape": "Groups", "documentation": "<p>The overview of groups for an organization.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to use to retrieve the next page of results. The value is \"null\" when there are no more results to return.</p>"}}}, "ListImpersonationRolesRequest": {"type": "structure", "required": ["OrganizationId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The WorkMail organization to which the listed impersonation roles belong.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token used to retrieve the next page of results. The first call doesn't require a token.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results returned in a single call.</p>"}}}, "ListImpersonationRolesResponse": {"type": "structure", "members": {"Roles": {"shape": "ImpersonationRoleList", "documentation": "<p>The list of impersonation roles under the given WorkMail organization.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to retrieve the next page of results. The value is <code>null</code> when there are no results to return.</p>"}}}, "ListMailDomainsRequest": {"type": "structure", "required": ["OrganizationId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The WorkMail organization for which to list domains.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in a single call.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to use to retrieve the next page of results. The first call does not require a token.</p>"}}}, "ListMailDomainsResponse": {"type": "structure", "members": {"MailDomains": {"shape": "MailDomains", "documentation": "<p>The list of mail domain summaries, specifying domains that exist in the specified WorkMail organization, along with the information about whether the domain is or isn't the default.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to use to retrieve the next page of results. The value becomes <code>null</code> when there are no more results to return.</p>"}}}, "ListMailboxExportJobsRequest": {"type": "structure", "required": ["OrganizationId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The organization ID.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to use to retrieve the next page of results.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in a single call.</p>"}}}, "ListMailboxExportJobsResponse": {"type": "structure", "members": {"Jobs": {"shape": "Jobs", "documentation": "<p>The mailbox export job details.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to use to retrieve the next page of results.</p>"}}}, "ListMailboxPermissionsRequest": {"type": "structure", "required": ["OrganizationId", "EntityId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The identifier of the organization under which the user, group, or resource exists.</p>"}, "EntityId": {"shape": "EntityIdentifier", "documentation": "<p>The identifier of the user, or resource for which to list mailbox permissions.</p> <p>The entity ID can accept <i>UserId or ResourceId</i>, <i>Username or Resourcename</i>, or <i>email</i>.</p> <ul> <li> <p>Entity ID: 12345678-1234-1234-1234-**********12, or r-0123456789a0123456789b0123456789</p> </li> <li> <p>Email address: <EMAIL></p> </li> <li> <p>Entity name: entity</p> </li> </ul>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to use to retrieve the next page of results. The first call does not contain any tokens.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in a single call.</p>"}}}, "ListMailboxPermissionsResponse": {"type": "structure", "members": {"Permissions": {"shape": "Permissions", "documentation": "<p>One page of the user, group, or resource mailbox permissions.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to use to retrieve the next page of results. The value is \"null\" when there are no more results to return.</p>"}}}, "ListMobileDeviceAccessOverridesRequest": {"type": "structure", "required": ["OrganizationId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The WorkMail organization under which to list mobile device access overrides.</p>"}, "UserId": {"shape": "EntityIdentifier", "documentation": "<p>The WorkMail user under which you list the mobile device access overrides. Accepts the following types of user identities:</p> <ul> <li> <p>User ID: <code>12345678-1234-1234-1234-**********12</code> or <code>S-1-1-12-**********-123456789-123456789-1234</code> </p> </li> <li> <p>Email address: <code><EMAIL></code> </p> </li> <li> <p>User name: <code>user</code> </p> </li> </ul>"}, "DeviceId": {"shape": "DeviceId", "documentation": "<p>The mobile device to which the access override applies.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to use to retrieve the next page of results. The first call does not require a token.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in a single call.</p>"}}}, "ListMobileDeviceAccessOverridesResponse": {"type": "structure", "members": {"Overrides": {"shape": "MobileDeviceAccessOverridesList", "documentation": "<p>The list of mobile device access overrides that exist for the specified WorkMail organization and user.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to use to retrieve the next page of results. The value is “null” when there are no more results to return.</p>"}}}, "ListMobileDeviceAccessRulesRequest": {"type": "structure", "required": ["OrganizationId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The WorkMail organization for which to list the rules.</p>"}}}, "ListMobileDeviceAccessRulesResponse": {"type": "structure", "members": {"Rules": {"shape": "MobileDeviceAccessRulesList", "documentation": "<p>The list of mobile device access rules that exist under the specified WorkMail organization.</p>"}}}, "ListOrganizationsRequest": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>The token to use to retrieve the next page of results. The first call does not contain any tokens.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in a single call.</p>"}}}, "ListOrganizationsResponse": {"type": "structure", "members": {"OrganizationSummaries": {"shape": "OrganizationSummaries", "documentation": "<p>The overview of owned organizations presented as a list of organization summaries.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to use to retrieve the next page of results. The value is \"null\" when there are no more results to return.</p>"}}}, "ListPersonalAccessTokensRequest": {"type": "structure", "required": ["OrganizationId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p> The Organization ID. </p>"}, "UserId": {"shape": "EntityIdentifier", "documentation": "<p> The WorkMail User ID. </p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p> The token from the previous response to query the next page.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p> The maximum amount of items that should be returned in a response. </p>"}}}, "ListPersonalAccessTokensResponse": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p> The token from the previous response to query the next page.</p>"}, "PersonalAccessTokenSummaries": {"shape": "PersonalAccessTokenSummaryList", "documentation": "<p> Lists all the personal tokens in an organization or user, if user ID is provided. </p>"}}}, "ListResourceDelegatesRequest": {"type": "structure", "required": ["OrganizationId", "ResourceId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The identifier for the organization that contains the resource for which delegates are listed.</p>"}, "ResourceId": {"shape": "EntityIdentifier", "documentation": "<p>The identifier for the resource whose delegates are listed.</p> <p>The identifier can accept <i>ResourceId</i>, <i>Resourcename</i>, or <i>email</i>. The following identity formats are available:</p> <ul> <li> <p>Resource ID: r-0123456789a0123456789b0123456789</p> </li> <li> <p>Email address: <EMAIL></p> </li> <li> <p>Resource name: resource</p> </li> </ul>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token used to paginate through the delegates associated with a resource.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The number of maximum results in a page.</p>"}}}, "ListResourceDelegatesResponse": {"type": "structure", "members": {"Delegates": {"shape": "ResourceDelegates", "documentation": "<p>One page of the resource's delegates.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token used to paginate through the delegates associated with a resource. While results are still available, it has an associated value. When the last page is reached, the token is empty.</p>"}}}, "ListResourcesFilters": {"type": "structure", "members": {"NamePrefix": {"shape": "String", "documentation": "<p>Filters only resource that start with the entered name prefix .</p>"}, "PrimaryEmailPrefix": {"shape": "String", "documentation": "<p>Filters only resource with the provided primary email prefix.</p>"}, "State": {"shape": "EntityState", "documentation": "<p>Filters only resource with the provided state.</p>"}}, "documentation": "<p>Filtering options for <i>ListResources</i> operation. This is only used as input to Operation.</p>"}, "ListResourcesRequest": {"type": "structure", "required": ["OrganizationId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The identifier for the organization under which the resources exist.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to use to retrieve the next page of results. The first call does not contain any tokens.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in a single call.</p>"}, "Filters": {"shape": "ListResourcesFilters", "documentation": "<p>Limit the resource search results based on the filter criteria. You can only use one filter per request.</p>"}}}, "ListResourcesResponse": {"type": "structure", "members": {"Resources": {"shape": "Resources", "documentation": "<p>One page of the organization's resource representation.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p> The token used to paginate through all the organization's resources. While results are still available, it has an associated value. When the last page is reached, the token is empty.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["ResourceARN"], "members": {"ResourceARN": {"shape": "AmazonResourceName", "documentation": "<p>The resource ARN.</p>"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"Tags": {"shape": "TagList", "documentation": "<p>A list of tag key-value pairs.</p>"}}}, "ListUsersFilters": {"type": "structure", "members": {"UsernamePrefix": {"shape": "String", "documentation": "<p>Filters only users with the provided username prefix.</p>"}, "DisplayNamePrefix": {"shape": "UserAttribute", "documentation": "<p>Filters only users with the provided display name prefix.</p>"}, "PrimaryEmailPrefix": {"shape": "String", "documentation": "<p>Filters only users with the provided email prefix.</p>"}, "State": {"shape": "EntityState", "documentation": "<p>Filters only users with the provided state.</p>"}, "IdentityProviderUserIdPrefix": {"shape": "IdentityProviderUserIdPrefix", "documentation": "<p>Filters only users with the ID from the IAM Identity Center.</p>"}}, "documentation": "<p> Filtering options for <i>ListUsers</i> operation. This is only used as input to Operation.</p>"}, "ListUsersRequest": {"type": "structure", "required": ["OrganizationId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The identifier for the organization under which the users exist.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token to use to retrieve the next page of results. The first call does not contain any tokens.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return in a single call.</p>"}, "Filters": {"shape": "ListUsersFilters", "documentation": "<p>Limit the user search results based on the filter criteria. You can only use one filter per request.</p>"}}}, "ListUsersResponse": {"type": "structure", "members": {"Users": {"shape": "Users", "documentation": "<p>The overview of users for an organization.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p> The token to use to retrieve the next page of results. This value is `null` when there are no more results to return.</p>"}}}, "LogGroupArn": {"type": "string", "max": 562, "min": 47, "pattern": "arn:aws:logs:[a-z\\-0-9]*:[0-9]{12}:log-group:([\\.\\-_/#A-Za-z0-9]+):\\*$"}, "MailDomainInUseException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>The domain you're trying to change is in use by another user or organization in your account. See the error message for details.</p>", "exception": true}, "MailDomainNotFoundException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>The domain specified is not found in your organization.</p>", "exception": true}, "MailDomainStateException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>After a domain has been added to the organization, it must be verified. The domain is not yet verified.</p>", "exception": true}, "MailDomainSummary": {"type": "structure", "members": {"DomainName": {"shape": "DomainName", "documentation": "<p>The domain name.</p>"}, "DefaultDomain": {"shape": "Boolean", "documentation": "<p>Whether the domain is default or not.</p>"}}, "documentation": "<p>The data for a given domain.</p>"}, "MailDomains": {"type": "list", "member": {"shape": "MailDomainSummary"}}, "MailboxExportErrorInfo": {"type": "string", "max": 1024, "min": 1, "pattern": "[\\S\\s]*"}, "MailboxExportJob": {"type": "structure", "members": {"JobId": {"shape": "MailboxExportJobId", "documentation": "<p>The identifier of the mailbox export job.</p>"}, "EntityId": {"shape": "WorkMailIdentifier", "documentation": "<p>The identifier of the user or resource associated with the mailbox.</p>"}, "Description": {"shape": "Description", "documentation": "<p>The mailbox export job description.</p>"}, "S3BucketName": {"shape": "S3BucketName", "documentation": "<p>The name of the S3 bucket.</p>"}, "S3Path": {"shape": "S3ObjectKey", "documentation": "<p>The path to the S3 bucket and file that the mailbox export job exports to.</p>"}, "EstimatedProgress": {"shape": "Percentage", "documentation": "<p>The estimated progress of the mailbox export job, in percentage points.</p>"}, "State": {"shape": "MailboxExportJobState", "documentation": "<p>The state of the mailbox export job.</p>"}, "StartTime": {"shape": "Timestamp", "documentation": "<p>The mailbox export job start timestamp.</p>"}, "EndTime": {"shape": "Timestamp", "documentation": "<p>The mailbox export job end timestamp.</p>"}}, "documentation": "<p>The details of a mailbox export job, including the user or resource ID associated with the mailbox and the S3 bucket that the mailbox contents are exported to.</p>"}, "MailboxExportJobId": {"type": "string", "max": 63, "min": 1, "pattern": "[A-Za-z0-9-]+"}, "MailboxExportJobState": {"type": "string", "enum": ["RUNNING", "COMPLETED", "FAILED", "CANCELLED"]}, "MailboxQuota": {"type": "integer", "box": true, "min": 1}, "MailboxSize": {"type": "double", "min": 0}, "MaxResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "Member": {"type": "structure", "members": {"Id": {"shape": "String", "documentation": "<p>The identifier of the member.</p>"}, "Name": {"shape": "String", "documentation": "<p>The name of the member.</p>"}, "Type": {"shape": "MemberType", "documentation": "<p>A member can be a user or group.</p>"}, "State": {"shape": "EntityState", "documentation": "<p>The state of the member, which can be ENABLED, DISABLED, or DELETED.</p>"}, "EnabledDate": {"shape": "Timestamp", "documentation": "<p>The date indicating when the member was enabled for WorkMail use.</p>"}, "DisabledDate": {"shape": "Timestamp", "documentation": "<p>The date indicating when the member was disabled from WorkMail use.</p>"}}, "documentation": "<p>The representation of a user or group.</p>"}, "MemberType": {"type": "string", "enum": ["GROUP", "USER"]}, "Members": {"type": "list", "member": {"shape": "Member"}}, "MobileDeviceAccessMatchedRule": {"type": "structure", "members": {"MobileDeviceAccessRuleId": {"shape": "MobileDeviceAccessRuleId", "documentation": "<p>Identifier of the rule that a simulated user matches.</p>"}, "Name": {"shape": "MobileDeviceAccessRuleName", "documentation": "<p>Name of a rule that a simulated user matches.</p>"}}, "documentation": "<p>The rule that a simulated user matches.</p>"}, "MobileDeviceAccessMatchedRuleList": {"type": "list", "member": {"shape": "MobileDeviceAccessMatchedRule"}, "max": 10, "min": 0}, "MobileDeviceAccessOverride": {"type": "structure", "members": {"UserId": {"shape": "WorkMailIdentifier", "documentation": "<p>The WorkMail user to which the access override applies.</p>"}, "DeviceId": {"shape": "DeviceId", "documentation": "<p>The device to which the override applies.</p>"}, "Effect": {"shape": "MobileDeviceAccessRuleEffect", "documentation": "<p>The effect of the override, <code>ALLOW</code> or <code>DENY</code>.</p>"}, "Description": {"shape": "MobileDeviceAccessRuleDescription", "documentation": "<p>A description of the override.</p>"}, "DateCreated": {"shape": "Timestamp", "documentation": "<p>The date the override was first created.</p>"}, "DateModified": {"shape": "Timestamp", "documentation": "<p>The date the override was last modified.</p>"}}, "documentation": "<p>The override object.</p>"}, "MobileDeviceAccessOverridesList": {"type": "list", "member": {"shape": "MobileDeviceAccessOverride"}}, "MobileDeviceAccessRule": {"type": "structure", "members": {"MobileDeviceAccessRuleId": {"shape": "MobileDeviceAccessRuleId", "documentation": "<p>The ID assigned to a mobile access rule.</p>"}, "Name": {"shape": "MobileDeviceAccessRuleName", "documentation": "<p>The name of a mobile access rule.</p>"}, "Description": {"shape": "MobileDeviceAccessRuleDescription", "documentation": "<p>The description of a mobile access rule.</p>"}, "Effect": {"shape": "MobileDeviceAccessRuleEffect", "documentation": "<p>The effect of the rule when it matches. Allowed values are <code>ALLOW</code> or <code>DENY</code>.</p>"}, "DeviceTypes": {"shape": "DeviceTypeList", "documentation": "<p>Devi<PERSON> types that a rule will match.</p>"}, "NotDeviceTypes": {"shape": "DeviceTypeList", "documentation": "<p>Device types that a rule <b>will not</b> match. All other device types will match.</p>"}, "DeviceModels": {"shape": "DeviceModelList", "documentation": "<p>Device models that a rule will match.</p>"}, "NotDeviceModels": {"shape": "DeviceModelList", "documentation": "<p>Device models that a rule <b>will not</b> match. All other device models will match.</p>"}, "DeviceOperatingSystems": {"shape": "DeviceOperatingSystemList", "documentation": "<p>Device operating systems that a rule will match.</p>"}, "NotDeviceOperatingSystems": {"shape": "DeviceOperatingSystemList", "documentation": "<p>Device operating systems that a rule <b>will not</b> match. All other device types will match.</p>"}, "DeviceUserAgents": {"shape": "DeviceUserAgentList", "documentation": "<p>Device user agents that a rule will match.</p>"}, "NotDeviceUserAgents": {"shape": "DeviceUserAgentList", "documentation": "<p>Device user agents that a rule <b>will not</b> match. All other device user agents will match.</p>"}, "DateCreated": {"shape": "Timestamp", "documentation": "<p>The date and time at which an access rule was created.</p>"}, "DateModified": {"shape": "Timestamp", "documentation": "<p>The date and time at which an access rule was modified.</p>"}}, "documentation": "<p>A rule that controls access to mobile devices for an WorkMail group.</p>"}, "MobileDeviceAccessRuleDescription": {"type": "string", "max": 256, "min": 1, "pattern": "[\\S\\s]+"}, "MobileDeviceAccessRuleEffect": {"type": "string", "enum": ["ALLOW", "DENY"]}, "MobileDeviceAccessRuleId": {"type": "string", "max": 64, "min": 1, "pattern": "[a-zA-Z0-9_-]+"}, "MobileDeviceAccessRuleName": {"type": "string", "max": 64, "min": 1, "pattern": "[\\S\\s]+"}, "MobileDeviceAccessRulesList": {"type": "list", "member": {"shape": "MobileDeviceAccessRule"}, "max": 10, "min": 0}, "NameAvailabilityException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>The user, group, or resource name isn't unique in WorkMail.</p>", "exception": true}, "NewResourceDescription": {"type": "string", "max": 64, "min": 0, "sensitive": true}, "NextToken": {"type": "string", "max": 1024, "min": 1, "pattern": "[\\S\\s]*|[a-zA-Z0-9/+=]{1,1024}"}, "OrganizationId": {"type": "string", "max": 34, "min": 34, "pattern": "^m-[0-9a-f]{32}$"}, "OrganizationName": {"type": "string", "max": 62, "min": 1, "pattern": "^(?!d-)([\\da-zA-Z]+)([-][\\da-zA-Z]+)*"}, "OrganizationNotFoundException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>An operation received a valid organization identifier that either doesn't belong or exist in the system.</p>", "exception": true}, "OrganizationStateException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>The organization must have a valid state to perform certain operations on the organization or its members.</p>", "exception": true}, "OrganizationSummaries": {"type": "list", "member": {"shape": "OrganizationSummary"}}, "OrganizationSummary": {"type": "structure", "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The identifier associated with the organization.</p>"}, "Alias": {"shape": "OrganizationName", "documentation": "<p>The alias associated with the organization.</p>"}, "DefaultMailDomain": {"shape": "DomainName", "documentation": "<p>The default email domain associated with the organization.</p>"}, "ErrorMessage": {"shape": "String", "documentation": "<p>The error message associated with the organization. It is only present if unexpected behavior has occurred with regards to the organization. It provides insight or solutions regarding unexpected behavior.</p>"}, "State": {"shape": "String", "documentation": "<p>The state associated with the organization.</p>"}}, "documentation": "<p>The representation of an organization.</p>"}, "Password": {"type": "string", "max": 256, "pattern": "[\\u0020-\\u00FF]+", "sensitive": true}, "Percentage": {"type": "integer", "max": 100, "min": 0}, "Permission": {"type": "structure", "required": ["GranteeId", "GranteeType", "PermissionValues"], "members": {"GranteeId": {"shape": "WorkMailIdentifier", "documentation": "<p>The identifier of the user, group, or resource to which the permissions are granted.</p>"}, "GranteeType": {"shape": "MemberType", "documentation": "<p>The type of user, group, or resource referred to in GranteeId.</p>"}, "PermissionValues": {"shape": "PermissionValues", "documentation": "<p>The permissions granted to the grantee. SEND_AS allows the grantee to send email as the owner of the mailbox (the grantee is not mentioned on these emails). SEND_ON_BEHALF allows the grantee to send email on behalf of the owner of the mailbox (the grantee is not mentioned as the physical sender of these emails). FULL_ACCESS allows the grantee full access to the mailbox, irrespective of other folder-level permissions set on the mailbox.</p>"}}, "documentation": "<p>Permission granted to a user, group, or resource to access a certain aspect of another user, group, or resource mailbox.</p>"}, "PermissionType": {"type": "string", "enum": ["FULL_ACCESS", "SEND_AS", "SEND_ON_BEHALF"]}, "PermissionValues": {"type": "list", "member": {"shape": "PermissionType"}}, "Permissions": {"type": "list", "member": {"shape": "Permission"}}, "PersonalAccessTokenConfiguration": {"type": "structure", "required": ["Status"], "members": {"Status": {"shape": "PersonalAccessTokenConfigurationStatus", "documentation": "<p> The status of the Personal Access Token allowed for the organization. </p> <ul> <li> <p> <i>Active</i> - Mailbox users can login to the web application and choose <i>Settings</i> to see the new <i>Personal Access Tokens</i> page to create and delete the Personal Access Tokens. Mailbox users can use the Personal Access Tokens to set up mailbox connection from desktop or mobile email clients.</p> </li> <li> <p> <i>Inactive</i> - Personal Access Tokens are disabled for your organization. Mailbox users can’t create, list, or delete Personal Access Tokens and can’t use them to connect to their mailboxes from desktop or mobile email clients.</p> </li> </ul>"}, "LifetimeInDays": {"shape": "PersonalAccessTokenLifetimeInDays", "documentation": "<p> The validity of the Personal Access Token status in days. </p>"}}, "documentation": "<p> Displays the Personal Access Token status. </p>"}, "PersonalAccessTokenConfigurationStatus": {"type": "string", "enum": ["ACTIVE", "INACTIVE"]}, "PersonalAccessTokenId": {"type": "string", "max": 64, "min": 1, "pattern": "[a-zA-Z0-9_-]+"}, "PersonalAccessTokenLifetimeInDays": {"type": "integer", "box": true, "max": 3653, "min": 1}, "PersonalAccessTokenName": {"type": "string", "max": 64, "min": 1, "pattern": "[^\\x00-\\x1F\\x7F\\x3C\\x3E\\x5C]+"}, "PersonalAccessTokenScope": {"type": "string", "max": 256, "min": 1, "pattern": "[^\\x00-\\x1F\\x7F\\x3C\\x3E\\x5C]+"}, "PersonalAccessTokenScopeList": {"type": "list", "member": {"shape": "PersonalAccessTokenScope"}, "max": 10, "min": 1}, "PersonalAccessTokenSummary": {"type": "structure", "members": {"PersonalAccessTokenId": {"shape": "PersonalAccessTokenId", "documentation": "<p> The ID of the Personal Access Token. </p>"}, "UserId": {"shape": "WorkMailIdentifier", "documentation": "<p> The user ID of the WorkMail user associated with the Personal Access Token. </p>"}, "Name": {"shape": "PersonalAccessTokenName", "documentation": "<p> The name of the Personal Access Token. </p>"}, "DateCreated": {"shape": "Timestamp", "documentation": "<p> The date when the Personal Access Token was created. </p>"}, "DateLastUsed": {"shape": "Timestamp", "documentation": "<p> The date when the Personal Access Token was last used. </p>"}, "ExpiresTime": {"shape": "Timestamp", "documentation": "<p> The date when the Personal Access Token will expire. </p>"}, "Scopes": {"shape": "PersonalAccessTokenScopeList", "documentation": "<p> Lists all the Personal Access Token permissions for a mailbox. </p>"}}, "documentation": "<p> The summary of the Personal Access Token. </p>"}, "PersonalAccessTokenSummaryList": {"type": "list", "member": {"shape": "PersonalAccessTokenSummary"}}, "PolicyDescription": {"type": "string", "max": 256, "pattern": "[\\w\\d\\s\\S\\-!?=,.;:'_]+", "sensitive": true}, "PutAccessControlRuleRequest": {"type": "structure", "required": ["Name", "Effect", "Description", "OrganizationId"], "members": {"Name": {"shape": "AccessControlRuleName", "documentation": "<p>The rule name.</p>"}, "Effect": {"shape": "AccessControlRuleEffect", "documentation": "<p>The rule effect.</p>"}, "Description": {"shape": "AccessControlRuleDescription", "documentation": "<p>The rule description.</p>"}, "IpRanges": {"shape": "IpRangeList", "documentation": "<p>IPv4 CIDR ranges to include in the rule.</p>"}, "NotIpRanges": {"shape": "IpRangeList", "documentation": "<p>IPv4 CIDR ranges to exclude from the rule.</p>"}, "Actions": {"shape": "ActionsList", "documentation": "<p>Access protocol actions to include in the rule. Valid values include <code>ActiveSync</code>, <code>AutoDiscover</code>, <code>EWS</code>, <code>IMAP</code>, <code>SMTP</code>, <code>WindowsOutlook</code>, and <code>WebMail</code>.</p>"}, "NotActions": {"shape": "ActionsList", "documentation": "<p>Access protocol actions to exclude from the rule. Valid values include <code>ActiveSync</code>, <code>AutoDiscover</code>, <code>EWS</code>, <code>IMAP</code>, <code>SMTP</code>, <code>WindowsOutlook</code>, and <code>WebMail</code>.</p>"}, "UserIds": {"shape": "UserIdList", "documentation": "<p>User IDs to include in the rule.</p>"}, "NotUserIds": {"shape": "UserIdList", "documentation": "<p>User IDs to exclude from the rule.</p>"}, "OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The identifier of the organization.</p>"}, "ImpersonationRoleIds": {"shape": "ImpersonationRoleIdList", "documentation": "<p>Impersonation role IDs to include in the rule.</p>"}, "NotImpersonationRoleIds": {"shape": "ImpersonationRoleIdList", "documentation": "<p>Impersonation role IDs to exclude from the rule.</p>"}}}, "PutAccessControlRuleResponse": {"type": "structure", "members": {}}, "PutEmailMonitoringConfigurationRequest": {"type": "structure", "required": ["OrganizationId", "RoleArn", "LogGroupArn"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The ID of the organization for which the email monitoring configuration is set.</p>"}, "RoleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM Role associated with the email monitoring configuration.</p>"}, "LogGroupArn": {"shape": "LogGroupArn", "documentation": "<p>The Amazon Resource Name (ARN) of the CloudWatch Log group associated with the email monitoring configuration.</p>"}}}, "PutEmailMonitoringConfigurationResponse": {"type": "structure", "members": {}}, "PutIdentityProviderConfigurationRequest": {"type": "structure", "required": ["OrganizationId", "AuthenticationMode", "IdentityCenterConfiguration", "PersonalAccessTokenConfiguration"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p> The ID of the WorkMail Organization. </p>"}, "AuthenticationMode": {"shape": "IdentityProviderAuthenticationMode", "documentation": "<p> The authentication mode used in WorkMail.</p>"}, "IdentityCenterConfiguration": {"shape": "IdentityCenterConfiguration", "documentation": "<p> The details of the IAM Identity Center configuration.</p>"}, "PersonalAccessTokenConfiguration": {"shape": "PersonalAccessTokenConfiguration", "documentation": "<p> The details of the Personal Access Token configuration. </p>"}}}, "PutIdentityProviderConfigurationResponse": {"type": "structure", "members": {}}, "PutInboundDmarcSettingsRequest": {"type": "structure", "required": ["OrganizationId", "Enforced"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The ID of the organization that you are applying the DMARC policy to.</p>"}, "Enforced": {"shape": "BooleanObject", "documentation": "<p>Enforces or suspends a policy after it's applied.</p>"}}}, "PutInboundDmarcSettingsResponse": {"type": "structure", "members": {}}, "PutMailboxPermissionsRequest": {"type": "structure", "required": ["OrganizationId", "EntityId", "GranteeId", "PermissionValues"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The identifier of the organization under which the user, group, or resource exists.</p>"}, "EntityId": {"shape": "EntityIdentifier", "documentation": "<p>The identifier of the user or resource for which to update mailbox permissions.</p> <p>The identifier can be <i>UserId, ResourceID, or Group Id</i>, <i>Username, Resourcename, or Groupname</i>, or <i>email</i>.</p> <ul> <li> <p>Entity ID: 12345678-1234-1234-1234-**********12, r-0123456789a0123456789b0123456789, or S-1-1-12-**********-123456789-123456789-1234</p> </li> <li> <p>Email address: <EMAIL></p> </li> <li> <p>Entity name: entity</p> </li> </ul>"}, "GranteeId": {"shape": "EntityIdentifier", "documentation": "<p>The identifier of the user, group, or resource to which to grant the permissions.</p> <p>The identifier can be <i>UserId, ResourceID, or Group Id</i>, <i>Username, Resourcename, or Groupname</i>, or <i>email</i>.</p> <ul> <li> <p>Grantee ID: 12345678-1234-1234-1234-**********12, r-0123456789a0123456789b0123456789, or S-1-1-12-**********-123456789-123456789-1234</p> </li> <li> <p>Email address: <EMAIL></p> </li> <li> <p>Grantee name: grantee</p> </li> </ul>"}, "PermissionValues": {"shape": "PermissionValues", "documentation": "<p>The permissions granted to the grantee. SEND_AS allows the grantee to send email as the owner of the mailbox (the grantee is not mentioned on these emails). SEND_ON_BEHALF allows the grantee to send email on behalf of the owner of the mailbox (the grantee is not mentioned as the physical sender of these emails). FULL_ACCESS allows the grantee full access to the mailbox, irrespective of other folder-level permissions set on the mailbox.</p>"}}}, "PutMailboxPermissionsResponse": {"type": "structure", "members": {}}, "PutMobileDeviceAccessOverrideRequest": {"type": "structure", "required": ["OrganizationId", "UserId", "DeviceId", "Effect"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>Identifies the WorkMail organization for which you create the override.</p>"}, "UserId": {"shape": "EntityIdentifier", "documentation": "<p>The WorkMail user for which you create the override. Accepts the following types of user identities:</p> <ul> <li> <p>User ID: <code>12345678-1234-1234-1234-**********12</code> or <code>S-1-1-12-**********-123456789-123456789-1234</code> </p> </li> <li> <p>Email address: <code><EMAIL></code> </p> </li> <li> <p>User name: <code>user</code> </p> </li> </ul>"}, "DeviceId": {"shape": "DeviceId", "documentation": "<p>The mobile device for which you create the override. <code>DeviceId</code> is case insensitive.</p>"}, "Effect": {"shape": "MobileDeviceAccessRuleEffect", "documentation": "<p>The effect of the override, <code>ALLOW</code> or <code>DENY</code>.</p>"}, "Description": {"shape": "MobileDeviceAccessRuleDescription", "documentation": "<p>A description of the override.</p>"}}}, "PutMobileDeviceAccessOverrideResponse": {"type": "structure", "members": {}}, "PutRetentionPolicyRequest": {"type": "structure", "required": ["OrganizationId", "Name", "FolderConfigurations"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The organization ID.</p>"}, "Id": {"shape": "ShortString", "documentation": "<p>The retention policy ID.</p>"}, "Name": {"shape": "ShortString", "documentation": "<p>The retention policy name.</p>"}, "Description": {"shape": "PolicyDescription", "documentation": "<p>The retention policy description.</p>"}, "FolderConfigurations": {"shape": "FolderConfigurations", "documentation": "<p>The retention policy folder configurations.</p>"}}}, "PutRetentionPolicyResponse": {"type": "structure", "members": {}}, "RedactedEwsAvailabilityProvider": {"type": "structure", "members": {"EwsEndpoint": {"shape": "Url", "documentation": "<p>The endpoint of the remote EWS server.</p>"}, "EwsUsername": {"shape": "ExternalUserName", "documentation": "<p>The username used to authenticate the remote EWS server.</p>"}}, "documentation": "<p>Describes an EWS based availability provider when returned from the service. It does not contain the password of the endpoint.</p>"}, "RegisterMailDomainRequest": {"type": "structure", "required": ["OrganizationId", "DomainName"], "members": {"ClientToken": {"shape": "IdempotencyClientToken", "documentation": "<p>Idempotency token used when retrying requests.</p>", "idempotencyToken": true}, "OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The WorkMail organization under which you're creating the domain.</p>"}, "DomainName": {"shape": "WorkMailDomainName", "documentation": "<p>The name of the mail domain to create in WorkMail and SES.</p>"}}}, "RegisterMailDomainResponse": {"type": "structure", "members": {}}, "RegisterToWorkMailRequest": {"type": "structure", "required": ["OrganizationId", "EntityId", "Email"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The identifier for the organization under which the user, group, or resource exists.</p>"}, "EntityId": {"shape": "EntityIdentifier", "documentation": "<p>The identifier for the user, group, or resource to be updated.</p> <p>The identifier can accept <i>UserId, ResourceId, or GroupId</i>, or <i>Username, Resourcename, or Groupname</i>. The following identity formats are available:</p> <ul> <li> <p>Entity ID: 12345678-1234-1234-1234-**********12, r-0123456789a0123456789b0123456789, or S-1-1-12-**********-123456789-123456789-1234</p> </li> <li> <p>Entity name: entity</p> </li> </ul>"}, "Email": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The email for the user, group, or resource to be updated.</p>"}}}, "RegisterToWorkMailResponse": {"type": "structure", "members": {}}, "ReservedNameException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>This user, group, or resource name is not allowed in WorkMail.</p>", "exception": true}, "ResetPasswordRequest": {"type": "structure", "required": ["OrganizationId", "UserId", "Password"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The identifier of the organization that contains the user for which the password is reset.</p>"}, "UserId": {"shape": "WorkMailIdentifier", "documentation": "<p>The identifier of the user for whom the password is reset.</p>"}, "Password": {"shape": "Password", "documentation": "<p>The new password for the user.</p>"}}}, "ResetPasswordResponse": {"type": "structure", "members": {}}, "Resource": {"type": "structure", "members": {"Id": {"shape": "WorkMailIdentifier", "documentation": "<p>The identifier of the resource.</p>"}, "Email": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The email of the resource.</p>"}, "Name": {"shape": "ResourceName", "documentation": "<p>The name of the resource.</p>"}, "Type": {"shape": "ResourceType", "documentation": "<p>The type of the resource: equipment or room.</p>"}, "State": {"shape": "EntityState", "documentation": "<p>The state of the resource, which can be ENABLED, DISABLED, or DELETED.</p>"}, "EnabledDate": {"shape": "Timestamp", "documentation": "<p>The date indicating when the resource was enabled for WorkMail use.</p>"}, "DisabledDate": {"shape": "Timestamp", "documentation": "<p>The date indicating when the resource was disabled from WorkMail use.</p>"}, "Description": {"shape": "ResourceDescription", "documentation": "<p>Resource description.</p>"}}, "documentation": "<p>The representation of a resource.</p>"}, "ResourceDelegates": {"type": "list", "member": {"shape": "Delegate"}}, "ResourceDescription": {"type": "string", "max": 64, "min": 1, "sensitive": true}, "ResourceId": {"type": "string", "max": 34, "min": 34, "pattern": "^r-[0-9a-f]{32}$"}, "ResourceName": {"type": "string", "max": 20, "min": 1, "pattern": "[\\w\\-.]+(@[a-zA-Z0-9.\\-]+\\.[a-zA-Z0-9-]{2,})?"}, "ResourceNotFoundException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>The resource cannot be found.</p>", "exception": true}, "ResourceType": {"type": "string", "enum": ["ROOM", "EQUIPMENT"]}, "Resources": {"type": "list", "member": {"shape": "Resource"}}, "RetentionAction": {"type": "string", "enum": ["NONE", "DELETE", "PERMANENTLY_DELETE"]}, "RetentionPeriod": {"type": "integer", "box": true, "max": 730, "min": 1}, "RoleArn": {"type": "string", "max": 2048, "min": 20, "pattern": "arn:aws:iam:[a-z0-9-]*:[a-z0-9-]+:[A-Za-z0-9][A-Za-z0-9:_/+=,@.-]{0,1023}"}, "S3BucketName": {"type": "string", "max": 63, "min": 1, "pattern": "[A-Za-z0-9.-]+"}, "S3ObjectKey": {"type": "string", "max": 1023, "min": 1, "pattern": "[A-Za-z0-9!_.*'()/-]+"}, "ShortString": {"type": "string", "max": 64, "min": 1, "pattern": "[a-zA-Z0-9_-]+"}, "StartMailboxExportJobRequest": {"type": "structure", "required": ["ClientToken", "OrganizationId", "EntityId", "RoleArn", "KmsKeyArn", "S3BucketName", "S3Prefix"], "members": {"ClientToken": {"shape": "IdempotencyClientToken", "documentation": "<p>The idempotency token for the client request.</p>", "idempotencyToken": true}, "OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The identifier associated with the organization.</p>"}, "EntityId": {"shape": "EntityIdentifier", "documentation": "<p>The identifier of the user or resource associated with the mailbox.</p> <p>The identifier can accept <i>UserId or ResourceId</i>, <i>Username or Resourcename</i>, or <i>email</i>. The following identity formats are available:</p> <ul> <li> <p>Entity ID: 12345678-1234-1234-1234-**********12, r-0123456789a0123456789b0123456789 , or S-1-1-12-**********-123456789-123456789-1234</p> </li> <li> <p>Email address: <EMAIL></p> </li> <li> <p>Entity name: entity</p> </li> </ul>"}, "Description": {"shape": "Description", "documentation": "<p>The mailbox export job description.</p>"}, "RoleArn": {"shape": "RoleArn", "documentation": "<p>The ARN of the AWS Identity and Access Management (IAM) role that grants write permission to the S3 bucket.</p>"}, "KmsKeyArn": {"shape": "KmsKeyArn", "documentation": "<p>The Amazon Resource Name (ARN) of the symmetric AWS Key Management Service (AWS KMS) key that encrypts the exported mailbox content.</p>"}, "S3BucketName": {"shape": "S3BucketName", "documentation": "<p>The name of the S3 bucket.</p>"}, "S3Prefix": {"shape": "S3ObjectKey", "documentation": "<p>The S3 bucket prefix.</p>"}}}, "StartMailboxExportJobResponse": {"type": "structure", "members": {"JobId": {"shape": "MailboxExportJobId", "documentation": "<p>The job ID.</p>"}}}, "String": {"type": "string", "max": 256}, "Tag": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {"shape": "TagKey", "documentation": "<p>The key of the tag.</p>"}, "Value": {"shape": "TagValue", "documentation": "<p>The value of the tag.</p>"}}, "documentation": "<p>Describes a tag applied to a resource.</p>"}, "TagKey": {"type": "string", "max": 128, "min": 1}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 50, "min": 0}, "TagList": {"type": "list", "member": {"shape": "Tag"}, "max": 50, "min": 0}, "TagResourceRequest": {"type": "structure", "required": ["ResourceARN", "Tags"], "members": {"ResourceARN": {"shape": "AmazonResourceName", "documentation": "<p>The resource ARN.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The tag key-value pairs.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0}, "TargetUsers": {"type": "list", "member": {"shape": "EntityIdentifier"}, "max": 10, "min": 1}, "TestAvailabilityConfigurationRequest": {"type": "structure", "required": ["OrganizationId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The WorkMail organization where the availability provider will be tested.</p>"}, "DomainName": {"shape": "DomainName", "documentation": "<p>The domain to which the provider applies. If this field is provided, a stored availability provider associated to this domain name will be tested.</p>"}, "EwsProvider": {"shape": "EwsAvailabilityProvider"}, "LambdaProvider": {"shape": "LambdaAvailabilityProvider"}}}, "TestAvailabilityConfigurationResponse": {"type": "structure", "members": {"TestPassed": {"shape": "Boolean", "documentation": "<p><PERSON><PERSON><PERSON> indicating whether the test passed or failed.</p>"}, "FailureReason": {"shape": "String", "documentation": "<p>String containing the reason for a failed test if <code>TestPassed</code> is false.</p>"}}}, "Timestamp": {"type": "timestamp"}, "TooManyTagsException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>The resource can have up to 50 user-applied tags.</p>", "exception": true}, "UnsupportedOperationException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>You can't perform a write operation against a read-only directory.</p>", "exception": true}, "UntagResourceRequest": {"type": "structure", "required": ["ResourceARN", "TagKeys"], "members": {"ResourceARN": {"shape": "AmazonResourceName", "documentation": "<p>The resource ARN.</p>"}, "TagKeys": {"shape": "TagKeyList", "documentation": "<p>The tag keys.</p>"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateAvailabilityConfigurationRequest": {"type": "structure", "required": ["OrganizationId", "DomainName"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The WorkMail organization for which the <code>AvailabilityConfiguration</code> will be updated.</p>"}, "DomainName": {"shape": "DomainName", "documentation": "<p>The domain to which the provider applies the availability configuration.</p>"}, "EwsProvider": {"shape": "EwsAvailabilityProvider", "documentation": "<p>The EWS availability provider definition. The request must contain exactly one provider definition, either <code>EwsProvider</code> or <code>LambdaProvider</code>. The previously stored provider will be overridden by the one provided.</p>"}, "LambdaProvider": {"shape": "LambdaAvailabilityProvider", "documentation": "<p>The Lambda availability provider definition. The request must contain exactly one provider definition, either <code>EwsProvider</code> or <code>LambdaProvider</code>. The previously stored provider will be overridden by the one provided.</p>"}}}, "UpdateAvailabilityConfigurationResponse": {"type": "structure", "members": {}}, "UpdateDefaultMailDomainRequest": {"type": "structure", "required": ["OrganizationId", "DomainName"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The WorkMail organization for which to list domains.</p>"}, "DomainName": {"shape": "WorkMailDomainName", "documentation": "<p>The domain name that will become the default domain.</p>"}}}, "UpdateDefaultMailDomainResponse": {"type": "structure", "members": {}}, "UpdateGroupRequest": {"type": "structure", "required": ["OrganizationId", "GroupId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The identifier for the organization under which the group exists.</p>"}, "GroupId": {"shape": "EntityIdentifier", "documentation": "<p>The identifier for the group to be updated.</p> <p>The identifier can accept <i>GroupId</i>, <i>Groupname</i>, or <i>email</i>. The following identity formats are available:</p> <ul> <li> <p>Group ID: 12345678-1234-1234-1234-**********12 or S-1-1-12-**********-123456789-123456789-1234</p> </li> <li> <p>Email address: <EMAIL></p> </li> <li> <p>Group name: group</p> </li> </ul>"}, "HiddenFromGlobalAddressList": {"shape": "BooleanObject", "documentation": "<p>If enabled, the group is hidden from the global address list.</p>"}}}, "UpdateGroupResponse": {"type": "structure", "members": {}}, "UpdateImpersonationRoleRequest": {"type": "structure", "required": ["OrganizationId", "ImpersonationRoleId", "Name", "Type", "Rules"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The WorkMail organization that contains the impersonation role to update.</p>"}, "ImpersonationRoleId": {"shape": "ImpersonationRoleId", "documentation": "<p>The ID of the impersonation role to update.</p>"}, "Name": {"shape": "ImpersonationRoleName", "documentation": "<p>The updated impersonation role name.</p>"}, "Type": {"shape": "ImpersonationRoleType", "documentation": "<p>The updated impersonation role type.</p>"}, "Description": {"shape": "ImpersonationRoleDescription", "documentation": "<p>The updated impersonation role description.</p>"}, "Rules": {"shape": "ImpersonationRuleList", "documentation": "<p>The updated list of rules.</p>"}}}, "UpdateImpersonationRoleResponse": {"type": "structure", "members": {}}, "UpdateMailboxQuotaRequest": {"type": "structure", "required": ["OrganizationId", "UserId", "MailboxQuota"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The identifier for the organization that contains the user for whom to update the mailbox quota.</p>"}, "UserId": {"shape": "EntityIdentifier", "documentation": "<p>The identifer for the user for whom to update the mailbox quota.</p> <p>The identifier can be the <i>UserId</i>, <i>Username</i>, or <i>email</i>. The following identity formats are available:</p> <ul> <li> <p>User ID: 12345678-1234-1234-1234-**********12 or S-1-1-12-**********-123456789-123456789-1234</p> </li> <li> <p>Email address: <EMAIL></p> </li> <li> <p>User name: user</p> </li> </ul>"}, "MailboxQuota": {"shape": "MailboxQuota", "documentation": "<p>The updated mailbox quota, in MB, for the specified user.</p>"}}}, "UpdateMailboxQuotaResponse": {"type": "structure", "members": {}}, "UpdateMobileDeviceAccessRuleRequest": {"type": "structure", "required": ["OrganizationId", "MobileDeviceAccessRuleId", "Name", "Effect"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The WorkMail organization under which the rule will be updated.</p>"}, "MobileDeviceAccessRuleId": {"shape": "MobileDeviceAccessRuleId", "documentation": "<p>The identifier of the rule to be updated.</p>"}, "Name": {"shape": "MobileDeviceAccessRuleName", "documentation": "<p>The updated rule name.</p>"}, "Description": {"shape": "MobileDeviceAccessRuleDescription", "documentation": "<p>The updated rule description.</p>"}, "Effect": {"shape": "MobileDeviceAccessRuleEffect", "documentation": "<p>The effect of the rule when it matches. Allowed values are <code>ALLOW</code> or <code>DENY</code>.</p>"}, "DeviceTypes": {"shape": "DeviceTypeList", "documentation": "<p>Devi<PERSON> types that the updated rule will match.</p>"}, "NotDeviceTypes": {"shape": "DeviceTypeList", "documentation": "<p>Device types that the updated rule <b>will not</b> match. All other device types will match.</p>"}, "DeviceModels": {"shape": "DeviceModelList", "documentation": "<p>Device models that the updated rule will match.</p>"}, "NotDeviceModels": {"shape": "DeviceModelList", "documentation": "<p>Device models that the updated rule <b>will not</b> match. All other device models will match.</p>"}, "DeviceOperatingSystems": {"shape": "DeviceOperatingSystemList", "documentation": "<p>Device operating systems that the updated rule will match.</p>"}, "NotDeviceOperatingSystems": {"shape": "DeviceOperatingSystemList", "documentation": "<p>Device operating systems that the updated rule <b>will not</b> match. All other device operating systems will match.</p>"}, "DeviceUserAgents": {"shape": "DeviceUserAgentList", "documentation": "<p>User agents that the updated rule will match.</p>"}, "NotDeviceUserAgents": {"shape": "DeviceUserAgentList", "documentation": "<p>User agents that the updated rule <b>will not</b> match. All other user agents will match.</p>"}}}, "UpdateMobileDeviceAccessRuleResponse": {"type": "structure", "members": {}}, "UpdatePrimaryEmailAddressRequest": {"type": "structure", "required": ["OrganizationId", "EntityId", "Email"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The organization that contains the user, group, or resource to update.</p>"}, "EntityId": {"shape": "EntityIdentifier", "documentation": "<p>The user, group, or resource to update.</p> <p>The identifier can accept <i>UseriD, ResourceId, or GroupId</i>, <i>Username, Resourcename, or Groupname</i>, or <i>email</i>. The following identity formats are available:</p> <ul> <li> <p>Entity ID: 12345678-1234-1234-1234-**********12, r-0123456789a0123456789b0123456789, or S-1-1-12-**********-123456789-123456789-1234</p> </li> <li> <p>Email address: <EMAIL></p> </li> <li> <p>Entity name: entity</p> </li> </ul>"}, "Email": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The value of the email to be updated as primary.</p>"}}}, "UpdatePrimaryEmailAddressResponse": {"type": "structure", "members": {}}, "UpdateResourceRequest": {"type": "structure", "required": ["OrganizationId", "ResourceId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The identifier associated with the organization for which the resource is updated.</p>"}, "ResourceId": {"shape": "EntityIdentifier", "documentation": "<p>The identifier of the resource to be updated.</p> <p>The identifier can accept <i>ResourceId</i>, <i>Resourcename</i>, or <i>email</i>. The following identity formats are available:</p> <ul> <li> <p>Resource ID: r-0123456789a0123456789b0123456789</p> </li> <li> <p>Email address: <EMAIL></p> </li> <li> <p>Resource name: resource</p> </li> </ul>"}, "Name": {"shape": "ResourceName", "documentation": "<p>The name of the resource to be updated.</p>"}, "BookingOptions": {"shape": "BookingOptions", "documentation": "<p>The resource's booking options to be updated.</p>"}, "Description": {"shape": "NewResourceDescription", "documentation": "<p>Updates the resource description.</p>"}, "Type": {"shape": "ResourceType", "documentation": "<p>Updates the resource type.</p>"}, "HiddenFromGlobalAddressList": {"shape": "BooleanObject", "documentation": "<p>If enabled, the resource is hidden from the global address list.</p>"}}}, "UpdateResourceResponse": {"type": "structure", "members": {}}, "UpdateUserRequest": {"type": "structure", "required": ["OrganizationId", "UserId"], "members": {"OrganizationId": {"shape": "OrganizationId", "documentation": "<p>The identifier for the organization under which the user exists.</p>"}, "UserId": {"shape": "EntityIdentifier", "documentation": "<p>The identifier for the user to be updated.</p> <p>The identifier can be the <i>UserId</i>, <i>Username</i>, or <i>email</i>. The following identity formats are available:</p> <ul> <li> <p>User ID: 12345678-1234-1234-1234-**********12 or S-1-1-12-**********-123456789-123456789-1234</p> </li> <li> <p>Email address: <EMAIL></p> </li> <li> <p>User name: user</p> </li> </ul>"}, "Role": {"shape": "UserRole", "documentation": "<p>Updates the user role.</p> <p>You cannot pass <i>SYSTEM_USER</i> or <i>RESOURCE</i>.</p>"}, "DisplayName": {"shape": "UserAttribute", "documentation": "<p>Updates the display name of the user.</p>"}, "FirstName": {"shape": "UserAttribute", "documentation": "<p>Updates the user's first name.</p>"}, "LastName": {"shape": "UserAttribute", "documentation": "<p>Updates the user's last name.</p>"}, "HiddenFromGlobalAddressList": {"shape": "BooleanObject", "documentation": "<p>If enabled, the user is hidden from the global address list.</p>"}, "Initials": {"shape": "UserAttribute", "documentation": "<p>Updates the user's initials.</p>"}, "Telephone": {"shape": "UserAttribute", "documentation": "<p>Updates the user's contact details.</p>"}, "Street": {"shape": "UserAttribute", "documentation": "<p>Updates the user's street address.</p>"}, "JobTitle": {"shape": "UserAttribute", "documentation": "<p>Updates the user's job title.</p>"}, "City": {"shape": "UserAttribute", "documentation": "<p>Updates the user's city.</p>"}, "Company": {"shape": "UserAttribute", "documentation": "<p>Updates the user's company.</p>"}, "ZipCode": {"shape": "UserAttribute", "documentation": "<p>Updates the user's zip code.</p>"}, "Department": {"shape": "UserAttribute", "documentation": "<p>Updates the user's department.</p>"}, "Country": {"shape": "UserAttribute", "documentation": "<p>Updates the user's country.</p>"}, "Office": {"shape": "UserAttribute", "documentation": "<p>Updates the user's office.</p>"}, "IdentityProviderUserId": {"shape": "IdentityProviderUserIdForUpdate", "documentation": "<p>User ID from the IAM Identity Center. If this parameter is empty it will be updated automatically when the user logs in for the first time to the mailbox associated with WorkMail.</p>"}}}, "UpdateUserResponse": {"type": "structure", "members": {}}, "Url": {"type": "string", "max": 256, "pattern": "https?://[A-Za-z0-9.-]+(:[0-9]+)?/.*"}, "User": {"type": "structure", "members": {"Id": {"shape": "WorkMailIdentifier", "documentation": "<p>The identifier of the user.</p>"}, "Email": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The email of the user.</p>"}, "Name": {"shape": "UserName", "documentation": "<p>The name of the user.</p>"}, "DisplayName": {"shape": "String", "documentation": "<p>The display name of the user.</p>"}, "State": {"shape": "EntityState", "documentation": "<p>The state of the user, which can be ENABLED, DISABLED, or DELETED.</p>"}, "UserRole": {"shape": "UserRole", "documentation": "<p>The role of the user.</p>"}, "EnabledDate": {"shape": "Timestamp", "documentation": "<p>The date indicating when the user was enabled for WorkMail use.</p>"}, "DisabledDate": {"shape": "Timestamp", "documentation": "<p>The date indicating when the user was disabled from WorkMail use.</p>"}, "IdentityProviderUserId": {"shape": "IdentityProviderUserId", "documentation": "<p>User ID from the IAM Identity Center. If this parameter is empty it will be updated automatically when the user logs in for the first time to the mailbox associated with WorkMail.</p>"}, "IdentityProviderIdentityStoreId": {"shape": "IdentityProviderIdentityStoreId", "documentation": "<p>Identity store ID from the IAM Identity Center. If this parameter is empty it will be updated automatically when the user logs in for the first time to the mailbox associated with WorkMail.</p>"}}, "documentation": "<p>The representation of an WorkMail user.</p>"}, "UserAttribute": {"type": "string", "max": 256, "sensitive": true}, "UserIdList": {"type": "list", "member": {"shape": "WorkMailIdentifier"}, "max": 10, "min": 0}, "UserName": {"type": "string", "max": 64, "min": 1, "pattern": "[\\w\\-.]+(@[a-zA-Z0-9.\\-]+\\.[a-zA-Z0-9-]{2,})?"}, "UserRole": {"type": "string", "enum": ["USER", "RESOURCE", "SYSTEM_USER", "REMOTE_USER"]}, "Users": {"type": "list", "member": {"shape": "User"}}, "WorkMailDomainName": {"type": "string", "max": 209, "min": 3, "pattern": "[a-zA-Z0-9.-]+"}, "WorkMailIdentifier": {"type": "string", "max": 256, "min": 12}}, "documentation": "<p>WorkMail is a secure, managed business email and calendaring service with support for existing desktop and mobile email clients. You can access your email, contacts, and calendars using Microsoft Outlook, your browser, or other native iOS and Android email applications. You can integrate WorkMail with your existing corporate directory and control both the keys that encrypt your data and the location in which your data is stored.</p> <p>The WorkMail API is designed for the following scenarios:</p> <ul> <li> <p>Listing and describing organizations</p> </li> </ul> <ul> <li> <p>Managing users</p> </li> </ul> <ul> <li> <p>Managing groups</p> </li> </ul> <ul> <li> <p>Managing resources</p> </li> </ul> <p>All WorkMail API operations are Amazon-authenticated and certificate-signed. They not only require the use of the AWS SDK, but also allow for the exclusive use of AWS Identity and Access Management users and roles to help facilitate access, trust, and permission policies. By creating a role and allowing an IAM user to access the WorkMail site, the IAM user gains full administrative visibility into the entire WorkMail organization (or as set in the IAM policy). This includes, but is not limited to, the ability to create, update, and delete users, groups, and resources. This allows developers to perform the scenarios listed above, as well as give users the ability to grant access on a selective basis using the IAM model.</p>"}