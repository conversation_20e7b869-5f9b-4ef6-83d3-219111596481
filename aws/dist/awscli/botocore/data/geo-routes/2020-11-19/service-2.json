{"version": "2.0", "metadata": {"apiVersion": "2020-11-19", "auth": ["aws.auth#sigv4"], "endpointPrefix": "geo-routes", "protocol": "rest-json", "protocols": ["rest-json"], "serviceFullName": "Amazon Location Service Routes V2", "serviceId": "Geo Routes", "signatureVersion": "v4", "signingName": "geo-routes", "uid": "geo-routes-2020-11-19"}, "operations": {"CalculateIsolines": {"name": "CalculateIsolines", "http": {"method": "POST", "requestUri": "/isolines", "responseCode": 200}, "input": {"shape": "CalculateIsolinesRequest"}, "output": {"shape": "CalculateIsolinesResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Use the <code>CalculateIsolines</code> action to find service areas that can be reached in a given threshold of time, distance.</p>"}, "CalculateRouteMatrix": {"name": "CalculateRouteMatrix", "http": {"method": "POST", "requestUri": "/route-matrix", "responseCode": 200}, "input": {"shape": "CalculateRouteMatrixRequest"}, "output": {"shape": "CalculateRouteMatrixResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}], "documentation": "<p> Use <code>CalculateRouteMatrix</code> to compute results for all pairs of Origins to Destinations. Each row corresponds to one entry in Origins. Each entry in the row corresponds to the route from that entry in Origins to an entry in Destinations positions.</p>"}, "CalculateRoutes": {"name": "CalculateRoutes", "http": {"method": "POST", "requestUri": "/routes", "responseCode": 200}, "input": {"shape": "CalculateRoutesRequest"}, "output": {"shape": "CalculateRoutesResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}], "documentation": "<p> <code>CalculateRoutes</code> computes routes given the following required parameters: <code>Origin</code> and <code>Destination</code>.</p>"}, "OptimizeWaypoints": {"name": "OptimizeWaypoints", "http": {"method": "POST", "requestUri": "/optimize-waypoints", "responseCode": 200}, "input": {"shape": "OptimizeWaypointsRequest"}, "output": {"shape": "OptimizeWaypointsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}], "documentation": "<p> <code>OptimizeWaypoints</code> calculates the optimal order to travel between a set of waypoints to minimize either the travel time or the distance travelled during the journey, based on road network restrictions and the traffic pattern data.</p>"}, "SnapToRoads": {"name": "SnapToRoads", "http": {"method": "POST", "requestUri": "/snap-to-roads", "responseCode": 200}, "input": {"shape": "SnapToRoadsRequest"}, "output": {"shape": "SnapToRoadsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}], "documentation": "<p> <code>SnapToRoads</code> matches GPS trace to roads most likely traveled on.</p>"}}, "shapes": {"AccessDeniedException": {"type": "structure", "required": ["Message"], "members": {"Message": {"shape": "String", "locationName": "message"}}, "documentation": "<p>You don't have sufficient access to perform this action.</p>", "error": {"httpStatusCode": 403, "senderFault": true}, "exception": true}, "ApiKey": {"type": "string", "max": 1000, "min": 0, "sensitive": true}, "BeforeWaypointsList": {"type": "list", "member": {"shape": "WaypointIndex"}}, "Boolean": {"type": "boolean", "box": true}, "BoundingBox": {"type": "list", "member": {"shape": "Double"}, "max": 4, "min": 4, "sensitive": true}, "CalculateIsolinesRequest": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "members": {"Allow": {"shape": "IsolineAllowOptions", "documentation": "<p>Features that are allowed while calculating an isoline.</p>"}, "ArrivalTime": {"shape": "TimestampWithTimezoneOffset", "documentation": "<p>Time of arrival at the destination.</p> <p>Time format: <code>YYYY-MM-DDThh:mm:ss.sssZ | YYYY-MM-DDThh:mm:ss.sss+hh:mm</code> </p> <p>Examples:</p> <p> <code>2020-04-22T17:57:24Z</code> </p> <p> <code>2020-04-22T17:57:24+02:00</code> </p>"}, "Avoid": {"shape": "IsolineAvoidanceOptions", "documentation": "<p>Features that are avoided while calculating a route. Avoidance is on a best-case basis. If an avoidance can't be satisfied for a particular case, it violates the avoidance and the returned response produces a notice for the violation.</p>"}, "DepartNow": {"shape": "Boolean", "documentation": "<p>Uses the current time as the time of departure.</p>"}, "DepartureTime": {"shape": "TimestampWithTimezoneOffset", "documentation": "<p>Time of departure from thr origin.</p> <p>Time format:<code>YYYY-MM-DDThh:mm:ss.sssZ | YYYY-MM-DDThh:mm:ss.sss+hh:mm</code> </p> <p>Examples:</p> <p> <code>2020-04-22T17:57:24Z</code> </p> <p> <code>2020-04-22T17:57:24+02:00</code> </p>"}, "Destination": {"shape": "Position", "documentation": "<p>The final position for the route. In the World Geodetic System (WGS 84) format: <code>[longitude, latitude]</code>.</p>"}, "DestinationOptions": {"shape": "IsolineDestinationOptions", "documentation": "<p>Destination related options.</p>"}, "IsolineGeometryFormat": {"shape": "GeometryFormat", "documentation": "<p>The format of the returned IsolineGeometry. </p> <p>Default Value:<code>FlexiblePolyline</code> </p>"}, "IsolineGranularity": {"shape": "IsolineGranularityOptions", "documentation": "<p>Defines the granularity of the returned Isoline.</p>"}, "Key": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>Optional: The API key to be used for authorization. Either an API key or valid SigV4 signature must be provided when making a request. </p>", "location": "querystring", "locationName": "key"}, "OptimizeIsolineFor": {"shape": "IsolineOptimizationObjective", "documentation": "<p>Specifies the optimization criteria for when calculating an isoline. AccurateCalculation generates an isoline of higher granularity that is more precise. FastCalculation generates an isoline faster by reducing the granularity, and in turn the quality of the isoline. BalancedCalculation generates an isoline by balancing between quality and performance. </p> <p>Default Value: <code>BalancedCalculation</code> </p>"}, "OptimizeRoutingFor": {"shape": "RoutingObjective", "documentation": "<p>Specifies the optimization criteria for calculating a route.</p> <p>Default Value: <code>FastestRoute</code> </p>"}, "Origin": {"shape": "Position", "documentation": "<p>The start position for the route.</p>"}, "OriginOptions": {"shape": "IsolineOriginOptions", "documentation": "<p>Origin related options.</p>"}, "Thresholds": {"shape": "IsolineThresholds", "documentation": "<p>Threshold to be used for the isoline calculation. Up to 3 thresholds per provided type can be requested.</p> <p> You incur a calculation charge for each threshold. Using a large amount of thresholds in a request can lead you to incur unexpected charges. See <a href=\"https://docs.aws.amazon.com/location/latest/developerguide/routes-pricing.html`\"> Amazon Location's pricing page</a> for more information.</p>"}, "Traffic": {"shape": "IsolineTrafficOptions", "documentation": "<p>Traffic related options.</p>"}, "TravelMode": {"shape": "IsolineTravelMode", "documentation": "<p>Specifies the mode of transport when calculating a route. Used in estimating the speed of travel and road compatibility.</p> <note> <p> The mode <code>Scooter</code> also applies to motorcycles, set to <code>Scooter</code> when wanted to calculate options for motorcycles.</p> </note> <p>Default Value: <code>Car</code> </p>"}, "TravelModeOptions": {"shape": "IsolineTravelModeOptions", "documentation": "<p>Travel mode related options for the provided travel mode.</p>"}}}, "CalculateIsolinesResponse": {"type": "structure", "required": ["IsolineGeometryFormat", "Isolines", "PricingBucket"], "members": {"ArrivalTime": {"shape": "TimestampWithTimezoneOffset", "documentation": "<p>Time of arrival at the destination. This parameter is returned only if the Destination parameters was provided in the request. </p> <p>Time format:<code>YYYY-MM-DDThh:mm:ss.sssZ | YYYY-MM-DDThh:mm:ss.sss+hh:mm</code> </p> <p>Examples:</p> <p> <code>2020-04-22T17:57:24Z</code> </p> <p> <code>2020-04-22T17:57:24+02:00</code> </p>"}, "DepartureTime": {"shape": "TimestampWithTimezoneOffset", "documentation": "<p>Time of departure from thr origin.</p> <p>Time format:<code>YYYY-MM-DDThh:mm:ss.sssZ | YYYY-MM-DDThh:mm:ss.sss+hh:mm</code> </p> <p>Examples:</p> <p> <code>2020-04-22T17:57:24Z</code> </p> <p> <code>2020-04-22T17:57:24+02:00</code> </p>"}, "IsolineGeometryFormat": {"shape": "GeometryFormat", "documentation": "<p>The format of the returned IsolineGeometry. </p> <p>Default Value:<code>FlexiblePolyline</code> </p>"}, "Isolines": {"shape": "IsolineList", "documentation": "<p>Calculated isolines and associated properties.</p>"}, "PricingBucket": {"shape": "String", "documentation": "<p>The pricing bucket for which the query is charged at.</p>", "location": "header", "locationName": "x-amz-geo-pricing-bucket"}, "SnappedDestination": {"shape": "Position", "documentation": "<p>Snapped destination that was used for the Isoline calculation.</p>"}, "SnappedOrigin": {"shape": "Position", "documentation": "<p>Snapped origin that was used for the Isoline calculation.</p>"}}}, "CalculateRouteMatrixRequest": {"type": "structure", "required": ["Destinations", "Origins", "RoutingBoundary"], "members": {"Allow": {"shape": "RouteMatrixAllowOptions", "documentation": "<p>Features that are allowed while calculating a route.</p>"}, "Avoid": {"shape": "RouteMatrixAvoidanceOptions", "documentation": "<p>Features that are avoided while calculating a route. Avoidance is on a best-case basis. If an avoidance can't be satisfied for a particular case, it violates the avoidance and the returned response produces a notice for the violation.</p>"}, "DepartNow": {"shape": "Boolean", "documentation": "<p>Uses the current time as the time of departure.</p>"}, "DepartureTime": {"shape": "TimestampWithTimezoneOffset", "documentation": "<p>Time of departure from thr origin.</p> <p>Time format:<code>YYYY-MM-DDThh:mm:ss.sssZ | YYYY-MM-DDThh:mm:ss.sss+hh:mm</code> </p> <p>Examples:</p> <p> <code>2020-04-22T17:57:24Z</code> </p> <p> <code>2020-04-22T17:57:24+02:00</code> </p>"}, "Destinations": {"shape": "CalculateRouteMatrixRequestDestinationsList", "documentation": "<p>List of destinations for the route.</p> <note> <p>Route calculations are billed for each origin and destination pair. If you use a large matrix of origins and destinations, your costs will increase accordingly. See <a href=\"https://docs.aws.amazon.com/location/latest/developerguide/routes-pricing.html`\"> Amazon Location's pricing page</a> for more information.</p> </note>"}, "Exclude": {"shape": "RouteMatrixExclusionOptions", "documentation": "<p>Features to be strictly excluded while calculating the route.</p>"}, "Key": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>Optional: The API key to be used for authorization. Either an API key or valid SigV4 signature must be provided when making a request. </p>", "location": "querystring", "locationName": "key"}, "OptimizeRoutingFor": {"shape": "RoutingObjective", "documentation": "<p>Specifies the optimization criteria for calculating a route.</p> <p>Default Value: <code>FastestRoute</code> </p>"}, "Origins": {"shape": "CalculateRouteMatrixRequestOriginsList", "documentation": "<p>The position in longitude and latitude for the origin.</p> <note> <p>Route calculations are billed for each origin and destination pair. Using a large amount of Origins in a request can lead you to incur unexpected charges. See <a href=\"https://docs.aws.amazon.com/location/latest/developerguide/routes-pricing.html`\"> Amazon Location's pricing page</a> for more information.</p> </note>"}, "RoutingBoundary": {"shape": "RouteMatrixBoundary", "documentation": "<p>Boundary within which the matrix is to be calculated. All data, origins and destinations outside the boundary are considered invalid.</p> <note> <p>When request routing boundary was set as AutoCircle, the response routing boundary will return Circle derived from the AutoCircle settings.</p> </note>"}, "Traffic": {"shape": "RouteMatrixTrafficOptions", "documentation": "<p>Traffic related options.</p>"}, "TravelMode": {"shape": "RouteMatrixTravelMode", "documentation": "<p>Specifies the mode of transport when calculating a route. Used in estimating the speed of travel and road compatibility.</p> <p>Default Value: <code>Car</code> </p>"}, "TravelModeOptions": {"shape": "RouteMatrixTravelModeOptions", "documentation": "<p>Travel mode related options for the provided travel mode.</p>"}}}, "CalculateRouteMatrixRequestDestinationsList": {"type": "list", "member": {"shape": "RouteMatrixDestination"}, "min": 1}, "CalculateRouteMatrixRequestOriginsList": {"type": "list", "member": {"shape": "RouteMatrixOrigin"}, "min": 1}, "CalculateRouteMatrixResponse": {"type": "structure", "required": ["ErrorCount", "PricingBucket", "RouteMatrix", "RoutingBoundary"], "members": {"ErrorCount": {"shape": "CalculateRouteMatrixResponseErrorCountInteger", "documentation": "<p>The count of error results in the route matrix. If this number is 0, all routes were calculated successfully.</p>"}, "PricingBucket": {"shape": "String", "documentation": "<p>The pricing bucket for which the query is charged at.</p>", "location": "header", "locationName": "x-amz-geo-pricing-bucket"}, "RouteMatrix": {"shape": "RouteMatrix", "documentation": "<p>The calculated route matrix containing the results for all pairs of Origins to Destination positions. Each row corresponds to one entry in Origins. Each entry in the row corresponds to the route from that entry in Origins to an entry in Destination positions.</p>"}, "RoutingBoundary": {"shape": "RouteMatrixBoundary", "documentation": "<p>Boundary within which the matrix is to be calculated. All data, origins and destinations outside the boundary are considered invalid.</p> <note> <p>When request routing boundary was set as AutoCircle, the response routing boundary will return Circle derived from the AutoCircle settings.</p> </note>"}}}, "CalculateRouteMatrixResponseErrorCountInteger": {"type": "integer", "box": true, "min": 0}, "CalculateRoutesRequest": {"type": "structure", "required": ["Destination", "Origin"], "members": {"Allow": {"shape": "RouteAllowOptions", "documentation": "<p>Features that are allowed while calculating a route.</p>"}, "ArrivalTime": {"shape": "TimestampWithTimezoneOffset", "documentation": "<p>Time of arrival at the destination.</p> <p>Time format:<code>YYYY-MM-DDThh:mm:ss.sssZ | YYYY-MM-DDThh:mm:ss.sss+hh:mm</code> </p> <p>Examples:</p> <p> <code>2020-04-22T17:57:24Z</code> </p> <p> <code>2020-04-22T17:57:24+02:00</code> </p>"}, "Avoid": {"shape": "RouteAvoidanceOptions", "documentation": "<p>Features that are avoided while calculating a route. Avoidance is on a best-case basis. If an avoidance can't be satisfied for a particular case, it violates the avoidance and the returned response produces a notice for the violation.</p>"}, "DepartNow": {"shape": "Boolean", "documentation": "<p>Uses the current time as the time of departure.</p>"}, "DepartureTime": {"shape": "TimestampWithTimezoneOffset", "documentation": "<p>Time of departure from thr origin.</p> <p>Time format:<code>YYYY-MM-DDThh:mm:ss.sssZ | YYYY-MM-DDThh:mm:ss.sss+hh:mm</code> </p> <p>Examples:</p> <p> <code>2020-04-22T17:57:24Z</code> </p> <p> <code>2020-04-22T17:57:24+02:00</code> </p>"}, "Destination": {"shape": "Position", "documentation": "<p>The final position for the route. In the World Geodetic System (WGS 84) format: <code>[longitude, latitude]</code>.</p>"}, "DestinationOptions": {"shape": "RouteDestinationOptions", "documentation": "<p>Destination related options.</p>"}, "Driver": {"shape": "RouteDriverOptions", "documentation": "<p>Driver related options.</p>"}, "Exclude": {"shape": "RouteExclusionOptions", "documentation": "<p>Features to be strictly excluded while calculating the route.</p>"}, "InstructionsMeasurementSystem": {"shape": "MeasurementSystem", "documentation": "<p>Measurement system to be used for instructions within steps in the response.</p>"}, "Key": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>Optional: The API key to be used for authorization. Either an API key or valid SigV4 signature must be provided when making a request. </p>", "location": "querystring", "locationName": "key"}, "Languages": {"shape": "CalculateRoutesRequestLanguagesList", "documentation": "<p>List of languages for instructions within steps in the response.</p> <note> <p>Instructions in the requested language are returned only if they are available.</p> </note>"}, "LegAdditionalFeatures": {"shape": "RouteLegAdditionalFeatureList", "documentation": "<p>A list of optional additional parameters such as timezone that can be requested for each result.</p> <ul> <li> <p> <code>Elevation</code>: Retrieves the elevation information for each location.</p> </li> <li> <p> <code>Incidents</code>: Provides information on traffic incidents along the route.</p> </li> <li> <p> <code>PassThroughWaypoints</code>: Indicates waypoints that are passed through without stopping.</p> </li> <li> <p> <code>Summary</code>: Returns a summary of the route, including distance and duration.</p> </li> <li> <p> <code>Tolls</code>: Supplies toll cost information along the route.</p> </li> <li> <p> <code>TravelStepInstructions</code>: Provides step-by-step instructions for travel along the route.</p> </li> <li> <p> <code>TruckRoadTypes</code>: Returns information about road types suitable for trucks.</p> </li> <li> <p> <code>TypicalDuration</code>: Gives typical travel duration based on historical data.</p> </li> <li> <p> <code>Zones</code>: Specifies the time zone information for each waypoint.</p> </li> </ul>"}, "LegGeometryFormat": {"shape": "GeometryFormat", "documentation": "<p>Specifies the format of the geometry returned for each leg of the route. You can choose between two different geometry encoding formats.</p> <p> <code>FlexiblePolyline</code>: A compact and precise encoding format for the leg geometry. For more information on the format, see the GitHub repository for <a href=\"https://github.com/heremaps/flexible-polyline\"> <code>FlexiblePolyline</code> </a>.</p> <p> <code>Simple</code>: A less compact encoding, which is easier to decode but may be less precise and result in larger payloads.</p>"}, "MaxAlternatives": {"shape": "CalculateRoutesRequestMaxAlternativesInteger", "documentation": "<p>Maximum number of alternative routes to be provided in the response, if available.</p>"}, "OptimizeRoutingFor": {"shape": "RoutingObjective", "documentation": "<p>Specifies the optimization criteria for calculating a route.</p> <p>Default Value: <code>FastestRoute</code> </p>"}, "Origin": {"shape": "Position", "documentation": "<p>The start position for the route.</p>"}, "OriginOptions": {"shape": "RouteOriginOptions", "documentation": "<p>Origin related options.</p>"}, "SpanAdditionalFeatures": {"shape": "RouteSpanAdditionalFeatureList", "documentation": "<p>A list of optional features such as SpeedLimit that can be requested for a Span. A span is a section of a Leg for which the requested features have the same values.</p>"}, "Tolls": {"shape": "RouteTollOptions", "documentation": "<p>Toll related options.</p>"}, "Traffic": {"shape": "RouteTrafficOptions", "documentation": "<p>Traffic related options.</p>"}, "TravelMode": {"shape": "RouteTravelMode", "documentation": "<p>Specifies the mode of transport when calculating a route. Used in estimating the speed of travel and road compatibility.</p> <p>Default Value: <code>Car</code> </p>"}, "TravelModeOptions": {"shape": "RouteTravelModeOptions", "documentation": "<p>Travel mode related options for the provided travel mode.</p>"}, "TravelStepType": {"shape": "RouteTravelStepType", "documentation": "<p>Type of step returned by the response. Default provides basic steps intended for web based applications. TurnByTurn provides detailed instructions with more granularity intended for a turn based navigation system.</p>"}, "Waypoints": {"shape": "RouteWaypointList", "documentation": "<p>List of waypoints between the Origin and Destination.</p>"}}}, "CalculateRoutesRequestLanguagesList": {"type": "list", "member": {"shape": "LanguageTag"}, "max": 10, "min": 0}, "CalculateRoutesRequestMaxAlternativesInteger": {"type": "integer", "box": true, "max": 5, "min": 0}, "CalculateRoutesResponse": {"type": "structure", "required": ["LegGeometryFormat", "Notices", "PricingBucket", "Routes"], "members": {"LegGeometryFormat": {"shape": "GeometryFormat", "documentation": "<p>Specifies the format of the geometry returned for each leg of the route.</p>"}, "Notices": {"shape": "RouteResponseNoticeList", "documentation": "<p>Notices are additional information returned that indicate issues that occurred during route calculation.</p>"}, "PricingBucket": {"shape": "String", "documentation": "<p>The pricing bucket for which the query is charged at.</p>", "location": "header", "locationName": "x-amz-geo-pricing-bucket"}, "Routes": {"shape": "RouteList", "documentation": "<p>The path from the origin to the destination.</p>"}}}, "Circle": {"type": "structure", "required": ["Center", "<PERSON><PERSON>"], "members": {"Center": {"shape": "Position", "documentation": "<p>Center of the Circle defined in longitude and latitude coordinates.</p> <p>Example: <code>[-123.1174, 49.2847]</code> represents the position with longitude <code>-123.1174</code> and latitude <code>49.2847</code>. </p>"}, "Radius": {"shape": "Double", "documentation": "<p>Radius of the Circle.</p> <p> <b>Unit</b>: <code>meters</code> </p>"}}, "documentation": "<p>Geometry defined as a circle. When request routing boundary was set as <code>AutoCircle</code>, the response routing boundary will return <code>Circle</code> derived from the <code>AutoCircle</code> settings.</p>", "sensitive": true}, "ClusterIndex": {"type": "integer", "box": true, "min": 0}, "Corridor": {"type": "structure", "required": ["LineString", "<PERSON><PERSON>"], "members": {"LineString": {"shape": "LineString", "documentation": "<p>An ordered list of positions used to plot a route on a map.</p> <note> <p>LineString and Polyline are mutually exclusive properties.</p> </note>"}, "Radius": {"shape": "Integer", "documentation": "<p>Radius that defines the width of the corridor.</p>"}}, "documentation": "<p>Geometry defined as a corridor - a LineString with a radius that defines the width of the corridor.</p>", "sensitive": true}, "CountryCode": {"type": "string", "max": 3, "min": 2, "pattern": "([A-Z]{2}|[A-Z]{3})"}, "CountryCode3": {"type": "string", "max": 3, "min": 3, "pattern": "[A-Z]{3}"}, "CountryCodeList": {"type": "list", "member": {"shape": "CountryCode"}, "max": 100, "min": 1}, "CurrencyCode": {"type": "string", "max": 3, "min": 3, "pattern": "[A-Z]{3}"}, "DayOfWeek": {"type": "string", "enum": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]}, "DimensionCentimeters": {"type": "long", "max": 4294967295, "min": 0}, "DistanceMeters": {"type": "long", "max": 4294967295, "min": 0}, "DistanceThresholdList": {"type": "list", "member": {"shape": "DistanceThresholdListMemberLong"}, "max": 5, "min": 1}, "DistanceThresholdListMemberLong": {"type": "long", "max": 300000, "min": 0}, "Double": {"type": "double", "box": true}, "DurationSeconds": {"type": "long", "max": 4294967295, "min": 0}, "GeometryFormat": {"type": "string", "enum": ["FlexiblePolyline", "Simple"]}, "Heading": {"type": "double", "max": 360.0, "min": 0.0}, "IndexList": {"type": "list", "member": {"shape": "Integer"}}, "Integer": {"type": "integer", "box": true}, "InternalServerException": {"type": "structure", "required": ["Message"], "members": {"Message": {"shape": "String", "locationName": "message"}}, "documentation": "<p>The request processing has failed because of an unknown error, exception or failure.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true, "retryable": {"throttling": false}}, "Isoline": {"type": "structure", "required": ["Connections", "Geometries"], "members": {"Connections": {"shape": "IsolineConnectionList", "documentation": "<p>Isolines may contain multiple components, if these components are connected by ferry links. These components are returned as separate polygons while the ferry links are returned as connections.</p>"}, "DistanceThreshold": {"shape": "DistanceMeters", "documentation": "<p>Distance threshold corresponding to the calculated Isoline.</p>"}, "Geometries": {"shape": "IsolineShapeGeometryList", "documentation": "<p>Geometries for the Calculated isolines.</p>"}, "TimeThreshold": {"shape": "DurationSeconds", "documentation": "<p>Time threshold corresponding to the calculated isoline.</p>"}}, "documentation": "<p>Calculated isolines and associated properties.</p>"}, "IsolineAllowOptions": {"type": "structure", "members": {"Hot": {"shape": "Boolean", "documentation": "<p>Allow Hot (High Occupancy Toll) lanes while calculating an isoline.</p> <p>Default value: <code>false</code> </p>"}, "Hov": {"shape": "Boolean", "documentation": "<p>Allow Hov (High Occupancy vehicle) lanes while calculating an isoline.</p> <p>Default value: <code>false</code> </p>"}}, "documentation": "<p>Features that are allowed while calculating an isoline.</p>"}, "IsolineAvoidanceArea": {"type": "structure", "required": ["Geometry"], "members": {"Except": {"shape": "IsolineAvoidanceAreaGeometryList", "documentation": "<p>Exceptions to the provided avoidance geometry, to be included while calculating an isoline.</p>"}, "Geometry": {"shape": "IsolineAvoidanceAreaGeometry", "documentation": "<p>Geometry of the area to be avoided.</p>"}}, "documentation": "<p>The area to be avoided.</p>"}, "IsolineAvoidanceAreaGeometry": {"type": "structure", "members": {"BoundingBox": {"shape": "BoundingBox", "documentation": "<p>Geometry defined as a bounding box. The first pair represents the X and Y coordinates (longitude and latitude,) of the southwest corner of the bounding box; the second pair represents the X and Y coordinates (longitude and latitude) of the northeast corner.</p>"}, "Corridor": {"shape": "Corridor", "documentation": "<p>Geometry defined as a corridor - a LineString with a radius that defines the width of the corridor.</p>"}, "Polygon": {"shape": "IsolineAvoidanceAreaGeometryPolygonList", "documentation": "<p>A list of Polygon will be excluded for calculating isolines, the list can only contain 1 polygon.</p>"}, "PolylineCorridor": {"shape": "PolylineCorridor", "documentation": "<p>Geometry defined as an encoded corridor – a polyline with a radius that defines the width of the corridor. For more information on polyline encoding, see <a href=\"https://github.com/heremaps/flexiblepolyline/blob/master/README.md\">https://github.com/heremaps/flexiblepolyline/blob/master/README.md</a>.</p>"}, "PolylinePolygon": {"shape": "IsolineAvoidanceAreaGeometryPolylinePolygonList", "documentation": "<p>A list of PolylinePolygon's that are excluded for calculating isolines, the list can only contain 1 polygon. For more information on polyline encoding, see <a href=\"https://github.com/heremaps/flexiblepolyline/blob/master/README.md\">https://github.com/heremaps/flexiblepolyline/blob/master/README.md</a>. </p>"}}, "documentation": "<p>The avoidance geometry, to be included while calculating an isoline.</p>"}, "IsolineAvoidanceAreaGeometryList": {"type": "list", "member": {"shape": "IsolineAvoidanceAreaGeometry"}}, "IsolineAvoidanceAreaGeometryPolygonList": {"type": "list", "member": {"shape": "LinearRing"}, "max": 1, "min": 1}, "IsolineAvoidanceAreaGeometryPolylinePolygonList": {"type": "list", "member": {"shape": "PolylineRing"}, "max": 1, "min": 1}, "IsolineAvoidanceAreaList": {"type": "list", "member": {"shape": "IsolineAvoidanceArea"}}, "IsolineAvoidanceOptions": {"type": "structure", "members": {"Areas": {"shape": "IsolineAvoidanceAreaList", "documentation": "<p>Areas to be avoided.</p>"}, "CarShuttleTrains": {"shape": "Boolean", "documentation": "<p>Avoid car-shuttle-trains while calculating an isoline.</p>"}, "ControlledAccessHighways": {"shape": "Boolean", "documentation": "<p>Avoid controlled access highways while calculating an isoline.</p>"}, "DirtRoads": {"shape": "Boolean", "documentation": "<p>Avoid dirt roads while calculating an isoline.</p>"}, "Ferries": {"shape": "Boolean", "documentation": "<p>Avoid ferries while calculating an isoline.</p>"}, "SeasonalClosure": {"shape": "Boolean", "documentation": "<p>Avoid roads that have seasonal closure while calculating an isoline.</p>"}, "TollRoads": {"shape": "Boolean", "documentation": "<p>Avoids roads where the specified toll transponders are the only mode of payment.</p>"}, "TollTransponders": {"shape": "Boolean", "documentation": "<p>Avoids roads where the specified toll transponders are the only mode of payment.</p>"}, "TruckRoadTypes": {"shape": "TruckRoadTypeList", "documentation": "<p>Truck road type identifiers. <code>BK1</code> through <code>BK4</code> apply only to Sweden. <code>A2,A4,B2,B4,C,D,ET2,ET4</code> apply only to Mexico.</p> <note> <p>There are currently no other supported values as of 26th April 2024.</p> </note>"}, "Tunnels": {"shape": "Boolean", "documentation": "<p>Avoid tunnels while calculating an isoline.</p>"}, "UTurns": {"shape": "Boolean", "documentation": "<p>Avoid U-turns for calculation on highways and motorways.</p>"}, "ZoneCategories": {"shape": "IsolineAvoidanceZoneCategoryList", "documentation": "<p>Zone categories to be avoided.</p>"}}, "documentation": "<p>Features that are avoided while calculating isolines. Avoidance is on a best-case basis. If an avoidance can't be satisfied for a particular case, it violates the avoidance and the returned response produces a notice for the violation.</p>"}, "IsolineAvoidanceZoneCategory": {"type": "structure", "members": {"Category": {"shape": "IsolineZoneCategory", "documentation": "<p>Zone category to be avoided.</p>"}}, "documentation": "<p>Zone category to be avoided.</p>"}, "IsolineAvoidanceZoneCategoryList": {"type": "list", "member": {"shape": "IsolineAvoidanceZoneCategory"}, "max": 3, "min": 0}, "IsolineCarOptions": {"type": "structure", "members": {"EngineType": {"shape": "IsolineEngineType", "documentation": "<p>Engine type of the vehicle.</p>"}, "LicensePlate": {"shape": "IsolineVehicleLicensePlate", "documentation": "<p>The vehicle License Plate.</p>"}, "MaxSpeed": {"shape": "IsolineCarOptionsMaxSpeedDouble", "documentation": "<p>Maximum speed.</p> <p> <b>Unit</b>: <code>KilometersPerHour</code> </p>", "box": true}, "Occupancy": {"shape": "IsolineCarOptionsOccupancyInteger", "documentation": "<p>The number of occupants in the vehicle.</p> <p>Default Value: <code>1</code> </p>"}}, "documentation": "<p>Travel mode options when the provided travel mode is <code>Car</code>.</p>"}, "IsolineCarOptionsMaxSpeedDouble": {"type": "double", "max": 252.0, "min": 3.6}, "IsolineCarOptionsOccupancyInteger": {"type": "integer", "box": true, "min": 1}, "IsolineConnection": {"type": "structure", "required": ["FromPolygonIndex", "Geometry", "ToPolygonIndex"], "members": {"FromPolygonIndex": {"shape": "IsolineConnectionFromPolygonIndexInteger", "documentation": "<p>Index of the polygon corresponding to the \"from\" component of the connection. The polygon is available from <code>Isoline[].Geometries</code>.</p>"}, "Geometry": {"shape": "IsolineConnectionGeometry", "documentation": "<p>The isoline geometry.</p>"}, "ToPolygonIndex": {"shape": "IsolineConnectionToPolygonIndexInteger", "documentation": "<p>Index of the polygon corresponding to the \"to\" component of the connection. The polygon is available from <code>Isoline[].Geometries</code>.</p>"}}, "documentation": "<p>Isolines may contain multiple components, if these components are connected by ferry links. These components are returned as separate polygons while the ferry links are returned as connections.</p>"}, "IsolineConnectionFromPolygonIndexInteger": {"type": "integer", "box": true, "min": 0}, "IsolineConnectionGeometry": {"type": "structure", "members": {"LineString": {"shape": "LineString", "documentation": "<p>An ordered list of positions used to plot a route on a map.</p> <note> <p>LineString and Polyline are mutually exclusive properties.</p> </note>"}, "Polyline": {"shape": "Polyline", "documentation": "<p>An ordered list of positions used to plot a route on a map in a lossy compression format.</p> <note> <p>LineString and Polyline are mutually exclusive properties.</p> </note>"}}, "documentation": "<p>Geometry of the connection between different isoline components.</p>"}, "IsolineConnectionList": {"type": "list", "member": {"shape": "IsolineConnection"}}, "IsolineConnectionToPolygonIndexInteger": {"type": "integer", "box": true, "min": 0}, "IsolineDestinationOptions": {"type": "structure", "members": {"AvoidActionsForDistance": {"shape": "DistanceMeters", "documentation": "<p>Avoids actions for the provided distance. This is typically to consider for users in moving vehicles who may not have sufficient time to make an action at an origin or a destination.</p>"}, "Heading": {"shape": "Heading", "documentation": "<p>GPS Heading at the position.</p>"}, "Matching": {"shape": "IsolineMatchingOptions", "documentation": "<p>Options to configure matching the provided position to the road network.</p>"}, "SideOfStreet": {"shape": "IsolineSideOfStreetOptions", "documentation": "<p>Options to configure matching the provided position to a side of the street.</p>"}}, "documentation": "<p>Destination related options.</p>"}, "IsolineEngineType": {"type": "string", "enum": ["Electric", "InternalCombustion", "PluginHybrid"]}, "IsolineGranularityOptions": {"type": "structure", "members": {"MaxPoints": {"shape": "IsolineGranularityOptionsMaxPointsInteger", "documentation": "<p>Maximum number of points of returned Isoline.</p>"}, "MaxResolution": {"shape": "DistanceMeters", "documentation": "<p>Maximum resolution of the returned isoline.</p> <p> <b>Unit</b>: <code>meters</code> </p>"}}, "documentation": "<p>Isoline granularity related options.</p>"}, "IsolineGranularityOptionsMaxPointsInteger": {"type": "integer", "box": true, "min": 31}, "IsolineHazardousCargoType": {"type": "string", "enum": ["Combustible", "Corrosive", "Explosive", "Flammable", "Gas", "HarmfulToWater", "Organic", "Other", "Poison", "PoisonousInhalation", "Radioactive"]}, "IsolineHazardousCargoTypeList": {"type": "list", "member": {"shape": "IsolineHazardousCargoType"}, "max": 11, "min": 0}, "IsolineList": {"type": "list", "member": {"shape": "Isoline"}, "max": 5, "min": 1}, "IsolineMatchingOptions": {"type": "structure", "members": {"NameHint": {"shape": "SensitiveString", "documentation": "<p>Attempts to match the provided position to a road similar to the provided name.</p>"}, "OnRoadThreshold": {"shape": "DistanceMeters", "documentation": "<p>If the distance to a highway/bridge/tunnel/sliproad is within threshold, the waypoint will be snapped to the highway/bridge/tunnel/sliproad.</p> <p> <b>Unit</b>: <code>meters</code> </p>"}, "Radius": {"shape": "DistanceMeters", "documentation": "<p>Considers all roads within the provided radius to match the provided destination to. The roads that are considered are determined by the provided Strategy.</p> <p> <b>Unit</b>: <code>Meters</code> </p>"}, "Strategy": {"shape": "MatchingStrategy", "documentation": "<p>Strategy that defines matching of the position onto the road network. MatchAny considers all roads possible, whereas MatchMostSignificantRoad matches to the most significant road.</p>"}}, "documentation": "<p>Isoline matching related options.</p>"}, "IsolineOptimizationObjective": {"type": "string", "enum": ["AccurateCalculation", "BalancedCalculation", "FastCalculation"]}, "IsolineOriginOptions": {"type": "structure", "members": {"AvoidActionsForDistance": {"shape": "DistanceMeters", "documentation": "<p>Avoids actions for the provided distance. This is typically to consider for users in moving vehicles who may not have sufficient time to make an action at an origin or a destination.</p>"}, "Heading": {"shape": "Heading", "documentation": "<p>GPS Heading at the position.</p>"}, "Matching": {"shape": "IsolineMatchingOptions", "documentation": "<p>Options to configure matching the provided position to the road network.</p>"}, "SideOfStreet": {"shape": "IsolineSideOfStreetOptions", "documentation": "<p>Options to configure matching the provided position to a side of the street.</p>"}}, "documentation": "<p>Origin related options.</p>"}, "IsolineScooterOptions": {"type": "structure", "members": {"EngineType": {"shape": "IsolineEngineType", "documentation": "<p>Engine type of the vehicle.</p>"}, "LicensePlate": {"shape": "IsolineVehicleLicensePlate", "documentation": "<p>The vehicle License Plate.</p>"}, "MaxSpeed": {"shape": "IsolineScooterOptionsMaxSpeedDouble", "documentation": "<p>Maximum speed specified.</p> <p> <b>Unit</b>: <code>KilometersPerHour</code> </p>", "box": true}, "Occupancy": {"shape": "IsolineScooterOptionsOccupancyInteger", "documentation": "<p>The number of occupants in the vehicle.</p> <p>Default Value: <code>1</code> </p>"}}, "documentation": "<p>Travel mode options when the provided travel mode is <code>Scooter</code> </p>"}, "IsolineScooterOptionsMaxSpeedDouble": {"type": "double", "max": 252.0, "min": 3.6}, "IsolineScooterOptionsOccupancyInteger": {"type": "integer", "box": true, "min": 1}, "IsolineShapeGeometry": {"type": "structure", "members": {"Polygon": {"shape": "LinearRings", "documentation": "<p>A list of Isoline Polygons, for each isoline polygon, it contains polygons of the first linear ring (the outer ring) and from 2nd item to the last item (the inner rings).</p>"}, "PolylinePolygon": {"shape": "PolylineRingList", "documentation": "<p>A list of Isoline PolylinePolygon, for each isoline PolylinePolygon, it contains PolylinePolygon of the first linear ring (the outer ring) and from 2nd item to the last item (the inner rings). For more information on polyline encoding, see <a href=\"https://github.com/heremaps/flexiblepolyline/blob/master/README.md\">https://github.com/heremaps/flexiblepolyline/blob/master/README.md</a>.</p>"}}, "documentation": "<p>Geometry of the connection between different Isoline components.</p>"}, "IsolineShapeGeometryList": {"type": "list", "member": {"shape": "IsolineShapeGeometry"}}, "IsolineSideOfStreetOptions": {"type": "structure", "required": ["Position"], "members": {"Position": {"shape": "Position", "documentation": "<p>Position defined as <code>[longitude, latitude]</code>.</p>"}, "UseWith": {"shape": "SideOfStreetMatchingStrategy", "documentation": "<p>Strategy that defines when the side of street position should be used. AnyStreet will always use the provided position.</p> <p>Default Value: <code>DividedStreetOnly</code> </p>"}}, "documentation": "<p>Options to configure matching the provided position to a side of the street.</p>"}, "IsolineThresholds": {"type": "structure", "members": {"Distance": {"shape": "DistanceThresholdList", "documentation": "<p>Distance to be used for the isoline calculation.</p>"}, "Time": {"shape": "TimeThresholdList", "documentation": "<p>Time to be used for the isoline calculation.</p>"}}, "documentation": "<p>Threshold to be used for the isoline calculation. Up to 5 thresholds per provided type can be requested.</p>"}, "IsolineTrafficOptions": {"type": "structure", "members": {"FlowEventThresholdOverride": {"shape": "DurationSeconds", "documentation": "<p>Duration for which flow traffic is considered valid. For this period, the flow traffic is used over historical traffic data. Flow traffic refers to congestion, which changes very quickly. Duration in seconds for which flow traffic event would be considered valid. While flow traffic event is valid it will be used over the historical traffic data. </p> <p> <b>Unit</b>: <code>seconds</code> </p>"}, "Usage": {"shape": "TrafficUsage", "documentation": "<p>Determines if traffic should be used or ignored while calculating the route.</p> <p>Default Value: <code>UseTrafficData</code> </p>"}}, "documentation": "<p>Options related to traffic.</p>"}, "IsolineTrailerOptions": {"type": "structure", "members": {"AxleCount": {"shape": "IsolineTrailerOptionsAxleCountInteger", "documentation": "<p>Total number of axles of the vehicle.</p>"}, "TrailerCount": {"shape": "IsolineTrailerOptionsTrailerCountInteger", "documentation": "<p>Number of trailers attached to the vehicle.</p> <p>Default Value: <code>0</code> </p>"}}, "documentation": "<p>Trailer options corresponding to the vehicle.</p>"}, "IsolineTrailerOptionsAxleCountInteger": {"type": "integer", "box": true, "min": 1}, "IsolineTrailerOptionsTrailerCountInteger": {"type": "integer", "box": true, "max": 255, "min": 1}, "IsolineTravelMode": {"type": "string", "enum": ["Car", "Pedestrian", "<PERSON>ooter", "Truck"]}, "IsolineTravelModeOptions": {"type": "structure", "members": {"Car": {"shape": "IsolineCarOptions", "documentation": "<p>Travel mode options when the provided travel mode is \"Car\"</p>"}, "Scooter": {"shape": "IsolineScooterOptions", "documentation": "<p>Travel mode options when the provided travel mode is <code>Scooter</code> </p> <note> <p>When travel mode is set to <code>Scooter</code>, then the avoidance option <code>ControlledAccessHighways</code> defaults to <code>true</code>.</p> </note>"}, "Truck": {"shape": "IsolineTruckOptions", "documentation": "<p>Travel mode options when the provided travel mode is \"Truck\"</p>"}}, "documentation": "<p>Travel mode related options for the provided travel mode.</p>"}, "IsolineTruckOptions": {"type": "structure", "members": {"AxleCount": {"shape": "IsolineTruckOptionsAxleCountInteger", "documentation": "<p>Total number of axles of the vehicle.</p>"}, "EngineType": {"shape": "IsolineEngineType", "documentation": "<p>Engine type of the vehicle.</p>"}, "GrossWeight": {"shape": "WeightKilograms", "documentation": "<p>Gross weight of the vehicle including trailers, and goods at capacity.</p> <p> <b>Unit</b>: <code>Kilograms</code> </p>"}, "HazardousCargos": {"shape": "IsolineHazardousCargoTypeList", "documentation": "<p>List of Hazardous cargo contained in the vehicle.</p>"}, "Height": {"shape": "IsolineTruckOptionsHeightLong", "documentation": "<p>Height of the vehicle.</p> <p> <b>Unit</b>: <code>centimeters</code> </p>"}, "HeightAboveFirstAxle": {"shape": "IsolineTruckOptionsHeightAboveFirstAxleLong", "documentation": "<p>Height of the vehicle above its first axle.</p> <p> <b>Unit</b>: <code>centimeters</code> </p>"}, "KpraLength": {"shape": "DimensionCentimeters", "documentation": "<p>Kingpin to rear axle length of the vehicle.</p> <p> <b>Unit</b>: <code>centimeters</code> </p>"}, "Length": {"shape": "IsolineTruckOptionsLengthLong", "documentation": "<p>Length of the vehicle.</p> <p> <b>Unit</b>: <code>centimeters</code> </p>"}, "LicensePlate": {"shape": "IsolineVehicleLicensePlate", "documentation": "<p>The vehicle License Plate.</p>"}, "MaxSpeed": {"shape": "IsolineTruckOptionsMaxSpeedDouble", "documentation": "<p>Maximum speed specified.</p> <p> <b>Unit</b>: <code>KilometersPerHour</code> </p>", "box": true}, "Occupancy": {"shape": "IsolineTruckOptionsOccupancyInteger", "documentation": "<p>The number of occupants in the vehicle.</p> <p>Default Value: <code>1</code> </p>"}, "PayloadCapacity": {"shape": "WeightKilograms", "documentation": "<p>Payload capacity of the vehicle and trailers attached.</p> <p> <b>Unit</b>: <code>kilograms</code> </p>"}, "TireCount": {"shape": "IsolineTruckOptionsTireCountInteger", "documentation": "<p>Number of tires on the vehicle.</p>"}, "Trailer": {"shape": "IsolineTrailerOptions", "documentation": "<p>Trailer options corresponding to the vehicle.</p>"}, "TruckType": {"shape": "IsolineTruckType", "documentation": "<p>Type of the truck.</p>"}, "TunnelRestrictionCode": {"shape": "TunnelRestrictionCode", "documentation": "<p>The tunnel restriction code.</p> <p>Tunnel categories in this list indicate the restrictions which apply to certain tunnels in Great Britain. They relate to the types of dangerous goods that can be transported through them.</p> <ul> <li> <p> <i>Tunnel Category B</i> </p> <ul> <li> <p> <i>Risk Level</i>: Limited risk</p> </li> <li> <p> <i>Restrictions</i>: Few restrictions</p> </li> </ul> </li> <li> <p> <i>Tunnel Category C</i> </p> <ul> <li> <p> <i>Risk Level</i>: Medium risk</p> </li> <li> <p> <i>Restrictions</i>: Some restrictions</p> </li> </ul> </li> <li> <p> <i>Tunnel Category D</i> </p> <ul> <li> <p> <i>Risk Level</i>: High risk</p> </li> <li> <p> <i>Restrictions</i>: Many restrictions occur</p> </li> </ul> </li> <li> <p> <i>Tunnel Category E</i> </p> <ul> <li> <p> <i>Risk Level</i>: Very high risk</p> </li> <li> <p> <i>Restrictions</i>: Restricted tunnel</p> </li> </ul> </li> </ul>"}, "WeightPerAxle": {"shape": "WeightKilograms", "documentation": "<p>Heaviest weight per axle irrespective of the axle type or the axle group. Meant for usage in countries where the differences in axle types or axle groups are not distinguished.</p> <p> <b>Unit</b>: <code>Kilograms</code> </p>"}, "WeightPerAxleGroup": {"shape": "WeightPerAxleGroup", "documentation": "<p>Specifies the total weight for the specified axle group. Meant for usage in countries that have different regulations based on the axle group type.</p> <p> <b>Unit</b>: <code>Kilograms</code> </p>"}, "Width": {"shape": "IsolineTruckOptionsWidthLong", "documentation": "<p>Width of the vehicle.</p> <p> <b>Unit</b>: <code>centimeters</code> </p>"}}, "documentation": "<p>Travel mode options when the provided travel mode is \"Truck\"</p>"}, "IsolineTruckOptionsAxleCountInteger": {"type": "integer", "box": true, "max": 255, "min": 2}, "IsolineTruckOptionsHeightAboveFirstAxleLong": {"type": "long", "max": 5000, "min": 0}, "IsolineTruckOptionsHeightLong": {"type": "long", "max": 5000, "min": 0}, "IsolineTruckOptionsLengthLong": {"type": "long", "max": 30000, "min": 0}, "IsolineTruckOptionsMaxSpeedDouble": {"type": "double", "max": 252.0, "min": 3.6}, "IsolineTruckOptionsOccupancyInteger": {"type": "integer", "box": true, "min": 1}, "IsolineTruckOptionsTireCountInteger": {"type": "integer", "box": true, "max": 255, "min": 1}, "IsolineTruckOptionsWidthLong": {"type": "long", "max": 5000, "min": 0}, "IsolineTruckType": {"type": "string", "enum": ["LightTruck", "StraightTruck", "Tractor"]}, "IsolineVehicleLicensePlate": {"type": "structure", "members": {"LastCharacter": {"shape": "IsolineVehicleLicensePlateLastCharacterString", "documentation": "<p>The last character of the License Plate.</p>"}}, "documentation": "<p>The vehicle license plate.</p>"}, "IsolineVehicleLicensePlateLastCharacterString": {"type": "string", "max": 1, "min": 1}, "IsolineZoneCategory": {"type": "string", "enum": ["CongestionPricing", "Environmental", "Vignette"]}, "LanguageTag": {"type": "string", "max": 35, "min": 2}, "LineString": {"type": "list", "member": {"shape": "Position"}, "min": 2}, "LinearRing": {"type": "list", "member": {"shape": "Position"}, "min": 4}, "LinearRings": {"type": "list", "member": {"shape": "LinearRing"}, "min": 1}, "LocalizedString": {"type": "structure", "required": ["Value"], "members": {"Language": {"shape": "LanguageTag", "documentation": "<p>A list of BCP 47 compliant language codes for the results to be rendered in. The request uses the regional default as the fallback if the requested language can't be provided.</p>"}, "Value": {"shape": "String", "documentation": "<p>The value of the localized string.</p>"}}, "documentation": "<p>The localized string.</p>"}, "LocalizedStringList": {"type": "list", "member": {"shape": "LocalizedString"}}, "MatchingStrategy": {"type": "string", "enum": ["MatchAny", "MatchMostSignificantRoad"]}, "MeasurementSystem": {"type": "string", "enum": ["Metric", "Imperial"]}, "OptimizeWaypointsRequest": {"type": "structure", "required": ["Origin"], "members": {"Avoid": {"shape": "WaypointOptimizationAvoidanceOptions", "documentation": "<p>Features that are avoided. Avoidance is on a best-case basis. If an avoidance can't be satisfied for a particular case, this setting is ignored.</p>"}, "Clustering": {"shape": "WaypointOptimizationClusteringOptions", "documentation": "<p>Clustering allows you to specify how nearby waypoints can be clustered to improve the optimized sequence.</p>"}, "DepartureTime": {"shape": "TimestampWithTimezoneOffset", "documentation": "<p>Departure time from the waypoint.</p> <p>Time format:<code>YYYY-MM-DDThh:mm:ss.sssZ | YYYY-MM-DDThh:mm:ss.sss+hh:mm</code> </p> <p>Examples:</p> <p> <code>2020-04-22T17:57:24Z</code> </p> <p> <code>2020-04-22T17:57:24+02:00</code> </p>"}, "Destination": {"shape": "Position", "documentation": "<p>The final position for the route in the World Geodetic System (WGS 84) format: <code>[longitude, latitude]</code>.</p>"}, "DestinationOptions": {"shape": "WaypointOptimizationDestinationOptions", "documentation": "<p>Destination related options.</p>"}, "Driver": {"shape": "WaypointOptimizationDriverOptions", "documentation": "<p>Driver related options.</p>"}, "Exclude": {"shape": "WaypointOptimizationExclusionOptions", "documentation": "<p>Features to be strictly excluded while calculating the route.</p>"}, "Key": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>Optional: The API key to be used for authorization. Either an API key or valid SigV4 signature must be provided when making a request. </p>", "location": "querystring", "locationName": "key"}, "OptimizeSequencingFor": {"shape": "WaypointOptimizationSequencingObjective", "documentation": "<p>Specifies the optimization criteria for the calculated sequence.</p> <p>Default Value: <code>FastestRoute</code>.</p>"}, "Origin": {"shape": "Position", "documentation": "<p>The start position for the route.</p>"}, "OriginOptions": {"shape": "WaypointOptimizationOriginOptions", "documentation": "<p>Origin related options.</p>"}, "Traffic": {"shape": "WaypointOptimizationTrafficOptions", "documentation": "<p>Traffic-related options.</p>"}, "TravelMode": {"shape": "WaypointOptimizationTravelMode", "documentation": "<p>Specifies the mode of transport when calculating a route. Used in estimating the speed of travel and road compatibility.</p> <p>Default Value: <code>Car</code> </p>"}, "TravelModeOptions": {"shape": "WaypointOptimizationTravelModeOptions", "documentation": "<p>Travel mode related options for the provided travel mode.</p>"}, "Waypoints": {"shape": "WaypointOptimizationWaypointList", "documentation": "<p>List of waypoints between the <code>Origin</code> and <code>Destination</code>.</p>"}}}, "OptimizeWaypointsResponse": {"type": "structure", "required": ["Connections", "Distance", "Duration", "ImpedingWaypoints", "OptimizedWaypoints", "PricingBucket", "TimeBreakdown"], "members": {"Connections": {"shape": "WaypointOptimizationConnectionList", "documentation": "<p>Details about the connection from one waypoint to the next, within the optimized sequence.</p>"}, "Distance": {"shape": "DistanceMeters", "documentation": "<p>Overall distance to travel the whole sequence.</p>"}, "Duration": {"shape": "DurationSeconds", "documentation": "<p>Overall duration to travel the whole sequence.</p> <p> <b>Unit</b>: <code>seconds</code> </p>"}, "ImpedingWaypoints": {"shape": "WaypointOptimizationImpedingWaypointList", "documentation": "<p>Returns waypoints that caused the optimization problem to fail, and the constraints that were unsatisfied leading to the failure.</p>"}, "OptimizedWaypoints": {"shape": "WaypointOptimizationOptimizedWaypointList", "documentation": "<p>Waypoints in the order of the optimized sequence.</p>"}, "PricingBucket": {"shape": "String", "documentation": "<p>The pricing bucket for which the query is charged at.</p>", "location": "header", "locationName": "x-amz-geo-pricing-bucket"}, "TimeBreakdown": {"shape": "WaypointOptimizationTimeBreakdown", "documentation": "<p>Time breakdown for the sequence.</p>"}}}, "Polyline": {"type": "string", "min": 1, "sensitive": true}, "PolylineCorridor": {"type": "structure", "required": ["Polyline", "<PERSON><PERSON>"], "members": {"Polyline": {"shape": "Polyline", "documentation": "<p>An ordered list of positions used to plot a route on a map in a lossy compression format.</p> <note> <p>LineString and Polyline are mutually exclusive properties.</p> </note>"}, "Radius": {"shape": "Integer", "documentation": "<p>Considers all roads within the provided radius to match the provided destination to. The roads that are considered are determined by the provided Strategy.</p> <p> <b>Unit</b>: <code>Meters</code> </p>"}}, "documentation": "<p>Geometry defined as an encoded corridor - an encoded polyline with a radius that defines the width of the corridor.</p>", "sensitive": true}, "PolylineRing": {"type": "string", "min": 1, "sensitive": true}, "PolylineRingList": {"type": "list", "member": {"shape": "PolylineRing"}, "min": 1}, "Position": {"type": "list", "member": {"shape": "Double"}, "max": 2, "min": 2, "sensitive": true}, "Position23": {"type": "list", "member": {"shape": "Double"}, "max": 3, "min": 2, "sensitive": true}, "RoadSnapHazardousCargoType": {"type": "string", "enum": ["Combustible", "Corrosive", "Explosive", "Flammable", "Gas", "HarmfulToWater", "Organic", "Other", "Poison", "PoisonousInhalation", "Radioactive"]}, "RoadSnapHazardousCargoTypeList": {"type": "list", "member": {"shape": "RoadSnapHazardousCargoType"}, "max": 11, "min": 0}, "RoadSnapNotice": {"type": "structure", "required": ["Code", "Title", "TracePointIndexes"], "members": {"Code": {"shape": "RoadSnapNoticeCode", "documentation": "<p>Code corresponding to the issue.</p>"}, "Title": {"shape": "String", "documentation": "<p>The notice title.</p>"}, "TracePointIndexes": {"shape": "RoadSnapTracePointIndexList", "documentation": "<p>TracePoint indices for which the provided notice code corresponds to.</p>"}}, "documentation": "<p>Notices provide information around factors that may have influenced snapping in a manner atypical to the standard use cases.</p>"}, "RoadSnapNoticeCode": {"type": "string", "enum": ["TracePointsHeadingIgnored", "TracePointsIgnored", "TracePointsMovedByLargeDistance", "TracePointsNotMatched", "TracePointsOutOfSequence", "TracePointsSpeedEstimated", "TracePointsSpeedIgnored"]}, "RoadSnapNoticeList": {"type": "list", "member": {"shape": "RoadSnapNotice"}}, "RoadSnapSnappedGeometry": {"type": "structure", "members": {"LineString": {"shape": "LineString", "documentation": "<p>An ordered list of positions used to plot a route on a map.</p> <note> <p>LineString and Polyline are mutually exclusive properties.</p> </note>"}, "Polyline": {"shape": "Polyline", "documentation": "<p>An ordered list of positions used to plot a route on a map in a lossy compression format.</p> <note> <p>LineString and Polyline are mutually exclusive properties.</p> </note>"}}, "documentation": "<p>Interpolated geometry for the snapped route that is overlay-able onto a map.</p>"}, "RoadSnapSnappedTracePoint": {"type": "structure", "required": ["Confidence", "OriginalPosition", "SnappedPosition"], "members": {"Confidence": {"shape": "RoadSnapSnappedTracePointConfidenceDouble", "documentation": "<p>Confidence value for the correctness of this point match.</p>"}, "OriginalPosition": {"shape": "Position", "documentation": "<p>Position of the TracePoint provided within the request, at the same index.</p>"}, "SnappedPosition": {"shape": "Position", "documentation": "<p>Snapped position of the TracePoint provided within the request, at the same index. </p>"}}, "documentation": "<p>TracePoints snapped onto the road network.</p>"}, "RoadSnapSnappedTracePointConfidenceDouble": {"type": "double", "box": true, "max": 1, "min": 0}, "RoadSnapSnappedTracePointList": {"type": "list", "member": {"shape": "RoadSnapSnappedTracePoint"}}, "RoadSnapTracePoint": {"type": "structure", "required": ["Position"], "members": {"Heading": {"shape": "Heading", "documentation": "<p>GPS Heading at the position.</p>"}, "Position": {"shape": "Position", "documentation": "<p>Position defined as <code>[longitude, latitude]</code>.</p>"}, "Speed": {"shape": "SpeedKilometersPerHour", "documentation": "<p>Speed at the specified trace point .</p> <p> <b>Unit</b>: <code>KilometersPerHour</code> </p>"}, "Timestamp": {"shape": "TimestampWithTimezoneOffset", "documentation": "<p>Timestamp of the event.</p>"}}, "documentation": "<p>TracePoint indices for which the provided notice code corresponds to.</p>"}, "RoadSnapTracePointIndexList": {"type": "list", "member": {"shape": "Integer"}, "max": 1000, "min": 1}, "RoadSnapTrailerOptions": {"type": "structure", "members": {"TrailerCount": {"shape": "RoadSnapTrailerOptionsTrailerCountInteger", "documentation": "<p>Number of trailers attached to the vehicle.</p> <p>Default Value: <code>0</code> </p>"}}, "documentation": "<p>Trailer options corresponding to the vehicle.</p>"}, "RoadSnapTrailerOptionsTrailerCountInteger": {"type": "integer", "box": true, "max": 255, "min": 0}, "RoadSnapTravelMode": {"type": "string", "enum": ["Car", "Pedestrian", "<PERSON>ooter", "Truck"]}, "RoadSnapTravelModeOptions": {"type": "structure", "members": {"Truck": {"shape": "RoadSnapTruckOptions", "documentation": "<p>Travel mode options when the provided travel mode is \"Truck\".</p>"}}, "documentation": "<p>Travel mode related options for the provided travel mode.</p>"}, "RoadSnapTruckOptions": {"type": "structure", "members": {"GrossWeight": {"shape": "WeightKilograms", "documentation": "<p>Gross weight of the vehicle including trailers, and goods at capacity.</p> <p> <b>Unit</b>: <code>Kilograms</code> </p>"}, "HazardousCargos": {"shape": "RoadSnapHazardousCargoTypeList", "documentation": "<p>List of Hazardous cargos contained in the vehicle.</p>"}, "Height": {"shape": "RoadSnapTruckOptionsHeightLong", "documentation": "<p>Height of the vehicle.</p> <p> <b>Unit</b>: <code>centimeters</code> </p>"}, "Length": {"shape": "RoadSnapTruckOptionsLengthLong", "documentation": "<p>Length of the vehicle.</p> <p> <b>Unit</b>: <code>centimeters</code> </p>"}, "Trailer": {"shape": "RoadSnapTrailerOptions", "documentation": "<p>Trailer options corresponding to the vehicle.</p>"}, "TunnelRestrictionCode": {"shape": "TunnelRestrictionCode", "documentation": "<p>The tunnel restriction code.</p> <p>Tunnel categories in this list indicate the restrictions which apply to certain tunnels in Great Britain. They relate to the types of dangerous goods that can be transported through them.</p> <ul> <li> <p> <i>Tunnel Category B</i> </p> <ul> <li> <p> <i>Risk Level</i>: Limited risk</p> </li> <li> <p> <i>Restrictions</i>: Few restrictions</p> </li> </ul> </li> <li> <p> <i>Tunnel Category C</i> </p> <ul> <li> <p> <i>Risk Level</i>: Medium risk</p> </li> <li> <p> <i>Restrictions</i>: Some restrictions</p> </li> </ul> </li> <li> <p> <i>Tunnel Category D</i> </p> <ul> <li> <p> <i>Risk Level</i>: High risk</p> </li> <li> <p> <i>Restrictions</i>: Many restrictions occur</p> </li> </ul> </li> <li> <p> <i>Tunnel Category E</i> </p> <ul> <li> <p> <i>Risk Level</i>: Very high risk</p> </li> <li> <p> <i>Restrictions</i>: Restricted tunnel</p> </li> </ul> </li> </ul>"}, "Width": {"shape": "RoadSnapTruckOptionsWidthLong", "documentation": "<p>Width of the vehicle in centimenters.</p>"}}, "documentation": "<p>Travel mode options when the provided travel mode is \"Truck\".</p>"}, "RoadSnapTruckOptionsHeightLong": {"type": "long", "max": 5000, "min": 0}, "RoadSnapTruckOptionsLengthLong": {"type": "long", "max": 30000, "min": 0}, "RoadSnapTruckOptionsWidthLong": {"type": "long", "max": 5000, "min": 0}, "RoundaboutAngle": {"type": "double", "max": 360, "min": -360}, "Route": {"type": "structure", "required": ["Legs", "MajorRoadLabels"], "members": {"Legs": {"shape": "RouteLegList", "documentation": "<p>A leg is a section of a route from one waypoint to the next. A leg could be of type Vehicle, Pedestrian or Ferry. Legs of different types could occur together within a single route. For example, a car employing the use of a Ferry will contain Vehicle legs corresponding to journey on land, and Ferry legs corresponding to the journey via Ferry.</p>"}, "MajorRoadLabels": {"shape": "RouteMajorRoadLabelsList", "documentation": "<p>Important labels including names and route numbers that differentiate the current route from the alternatives presented.</p>"}, "Summary": {"shape": "RouteSummary", "documentation": "<p>Summarized details of the leg.</p>"}}, "documentation": "<p>The route.</p>"}, "RouteAllowOptions": {"type": "structure", "members": {"Hot": {"shape": "Boolean", "documentation": "<p>Allow Hot (High Occupancy Toll) lanes while calculating the route.</p> <p>Default value: <code>false</code> </p>"}, "Hov": {"shape": "Boolean", "documentation": "<p>Allow Hov (High Occupancy vehicle) lanes while calculating the route.</p> <p>Default value: <code>false</code> </p>"}}, "documentation": "<p>Features that are allowed while calculating a route.</p>"}, "RouteAvoidanceArea": {"type": "structure", "required": ["Geometry"], "members": {"Except": {"shape": "RouteAvoidanceAreaGeometryList", "documentation": "<p>Exceptions to the provided avoidance geometry, to be included while calculating the route.</p>"}, "Geometry": {"shape": "RouteAvoidanceAreaGeometry"}}, "documentation": "<p>Areas to be avoided.</p>"}, "RouteAvoidanceAreaGeometry": {"type": "structure", "members": {"Corridor": {"shape": "Corridor", "documentation": "<p>Geometry defined as a corridor - a LineString with a radius that defines the width of the corridor.</p>"}, "BoundingBox": {"shape": "BoundingBox", "documentation": "<p>Geometry defined as a bounding box. The first pair represents the X and Y coordinates (longitude and latitude,) of the southwest corner of the bounding box; the second pair represents the X and Y coordinates (longitude and latitude) of the northeast corner.</p>"}, "Polygon": {"shape": "RouteAvoidanceAreaGeometryPolygonList", "documentation": "<p>Geometry defined as a polygon with only one linear ring.</p>"}, "PolylineCorridor": {"shape": "PolylineCorridor", "documentation": "<p>Geometry defined as an encoded corridor - an encoded polyline with a radius that defines the width of the corridor.</p>"}, "PolylinePolygon": {"shape": "RouteAvoidanceAreaGeometryPolylinePolygonList", "documentation": "<p>A list of Isoline PolylinePolygon, for each isoline PolylinePolygon, it contains PolylinePolygon of the first linear ring (the outer ring) and from 2nd item to the last item (the inner rings). For more information on polyline encoding, see <a href=\"https://github.com/heremaps/flexiblepolyline/blob/master/README.md\">https://github.com/heremaps/flexiblepolyline/blob/master/README.md</a>.</p>"}}, "documentation": "<p>Geometry of the area to be avoided.</p>"}, "RouteAvoidanceAreaGeometryList": {"type": "list", "member": {"shape": "RouteAvoidanceAreaGeometry"}}, "RouteAvoidanceAreaGeometryPolygonList": {"type": "list", "member": {"shape": "LinearRing"}, "max": 1, "min": 1}, "RouteAvoidanceAreaGeometryPolylinePolygonList": {"type": "list", "member": {"shape": "PolylineRing"}, "max": 1, "min": 1}, "RouteAvoidanceAreaList": {"type": "list", "member": {"shape": "RouteAvoidanceArea"}}, "RouteAvoidanceOptions": {"type": "structure", "members": {"Areas": {"shape": "RouteAvoidanceAreaList", "documentation": "<p>Areas to be avoided.</p>"}, "CarShuttleTrains": {"shape": "Boolean", "documentation": "<p>Avoid car-shuttle-trains while calculating the route.</p>"}, "ControlledAccessHighways": {"shape": "Boolean", "documentation": "<p>Avoid controlled access highways while calculating the route.</p>"}, "DirtRoads": {"shape": "Boolean", "documentation": "<p>Avoid dirt roads while calculating the route.</p>"}, "Ferries": {"shape": "Boolean", "documentation": "<p>Avoid ferries while calculating the route.</p>"}, "SeasonalClosure": {"shape": "Boolean", "documentation": "<p>Avoid roads that have seasonal closure while calculating the route.</p>"}, "TollRoads": {"shape": "Boolean", "documentation": "<p>Avoids roads where the specified toll transponders are the only mode of payment.</p>"}, "TollTransponders": {"shape": "Boolean", "documentation": "<p>Avoids roads where the specified toll transponders are the only mode of payment.</p>"}, "TruckRoadTypes": {"shape": "TruckRoadTypeList", "documentation": "<p>Truck road type identifiers. <code>BK1</code> through <code>BK4</code> apply only to Sweden. <code>A2,A4,B2,B4,C,D,ET2,ET4</code> apply only to Mexico.</p> <note> <p>There are currently no other supported values as of 26th April 2024.</p> </note>"}, "Tunnels": {"shape": "Boolean", "documentation": "<p>Avoid tunnels while calculating the route.</p>"}, "UTurns": {"shape": "Boolean", "documentation": "<p>Avoid U-turns for calculation on highways and motorways.</p>"}, "ZoneCategories": {"shape": "RouteAvoidanceZoneCategoryList", "documentation": "<p>Zone categories to be avoided.</p>"}}, "documentation": "<p>Specifies options for areas to avoid when calculating the route. This is a best-effort avoidance setting, meaning the router will try to honor the avoidance preferences but may still include restricted areas if no feasible alternative route exists. If avoidance options are not followed, the response will indicate that the avoidance criteria were violated.</p>"}, "RouteAvoidanceZoneCategory": {"type": "structure", "required": ["Category"], "members": {"Category": {"shape": "RouteZoneCategory", "documentation": "<p>Zone category to be avoided.</p>"}}, "documentation": "<p>Zone categories to be avoided.</p>"}, "RouteAvoidanceZoneCategoryList": {"type": "list", "member": {"shape": "RouteAvoidanceZoneCategory"}, "max": 3, "min": 0}, "RouteCarOptions": {"type": "structure", "members": {"EngineType": {"shape": "RouteEngineType", "documentation": "<p>Engine type of the vehicle.</p>"}, "LicensePlate": {"shape": "RouteVehicleLicensePlate", "documentation": "<p>The vehicle License Plate.</p>"}, "MaxSpeed": {"shape": "RouteCarOptionsMaxSpeedDouble", "documentation": "<p>Maximum speed specified.</p> <p> <b>Unit</b>: <code>KilometersPerHour</code> </p>", "box": true}, "Occupancy": {"shape": "RouteCarOptionsOccupancyInteger", "documentation": "<p>The number of occupants in the vehicle.</p> <p>Default Value: <code>1</code> </p>"}}, "documentation": "<p>Travel mode options when the provided travel mode is <code>Car</code>.</p>"}, "RouteCarOptionsMaxSpeedDouble": {"type": "double", "max": 252.0, "min": 3.6}, "RouteCarOptionsOccupancyInteger": {"type": "integer", "box": true, "min": 1}, "RouteContinueHighwayStepDetails": {"type": "structure", "required": ["Intersection"], "members": {"Intersection": {"shape": "LocalizedStringList", "documentation": "<p>Name of the intersection, if applicable to the step.</p>"}, "SteeringDirection": {"shape": "RouteSteeringDirection", "documentation": "<p>Steering direction for the step.</p>"}, "TurnAngle": {"shape": "TurnAngle", "documentation": "<p><PERSON><PERSON> of the turn.</p>"}, "TurnIntensity": {"shape": "RouteTurnIntensity", "documentation": "<p>Intensity of the turn.</p>"}}, "documentation": "<p>Details related to the continue highway step.</p>"}, "RouteContinueStepDetails": {"type": "structure", "required": ["Intersection"], "members": {"Intersection": {"shape": "LocalizedStringList", "documentation": "<p>Name of the intersection, if applicable to the step.</p>"}}, "documentation": "<p>Details related to the continue step.</p>"}, "RouteDestinationOptions": {"type": "structure", "members": {"AvoidActionsForDistance": {"shape": "RouteDestinationOptionsAvoidActionsForDistanceLong", "documentation": "<p>Avoids actions for the provided distance. This is typically to consider for users in moving vehicles who may not have sufficient time to make an action at an origin or a destination.</p>"}, "AvoidUTurns": {"shape": "Boolean", "documentation": "<p>Avoid U-turns for calculation on highways and motorways.</p>"}, "Heading": {"shape": "Heading", "documentation": "<p>GPS Heading at the position.</p>"}, "Matching": {"shape": "RouteMatchingOptions", "documentation": "<p>Options to configure matching the provided position to the road network.</p>"}, "SideOfStreet": {"shape": "RouteSideOfStreetOptions", "documentation": "<p>Options to configure matching the provided position to a side of the street.</p>"}, "StopDuration": {"shape": "DurationSeconds", "documentation": "<p>Duration of the stop.</p> <p> <b>Unit</b>: <code>seconds</code> </p>"}}, "documentation": "<p>Options related to the destination.</p>"}, "RouteDestinationOptionsAvoidActionsForDistanceLong": {"type": "long", "max": 2000}, "RouteDirection": {"type": "string", "enum": ["East", "North", "South", "West"]}, "RouteDriverOptions": {"type": "structure", "members": {"Schedule": {"shape": "RouteDriverScheduleIntervalList", "documentation": "<p>Driver work-rest schedule. Stops are added to fulfil the provided rest schedule.</p>"}}, "documentation": "<p>Driver related options.</p>"}, "RouteDriverScheduleInterval": {"type": "structure", "required": ["DriveDuration", "RestDuration"], "members": {"DriveDuration": {"shape": "DurationSeconds", "documentation": "<p>Maximum allowed driving time before stopping to rest.</p> <p> <b>Unit</b>: <code>seconds</code> </p>"}, "RestDuration": {"shape": "DurationSeconds", "documentation": "<p>Resting time before the driver can continue driving.</p> <p> <b>Unit</b>: <code>seconds</code> </p>"}}, "documentation": "<p>Interval of the driver work-rest schedule. Stops are added to fulfil the provided rest schedule.</p>"}, "RouteDriverScheduleIntervalList": {"type": "list", "member": {"shape": "RouteDriverScheduleInterval"}}, "RouteEmissionType": {"type": "structure", "required": ["Type"], "members": {"Co2EmissionClass": {"shape": "String", "documentation": "<p>The CO 2 emission classes.</p>"}, "Type": {"shape": "String", "documentation": "<p>Type of the emission.</p> <p> <b>Valid values</b>: <code>Euro1, Euro2, Euro3, Euro4, Euro5, Euro6, EuroEev</code> </p>"}}, "documentation": "<p>Type of the emission.</p> <p> <b>Valid values</b>: <code>Euro1, Euro2, Euro3, Euro4, Euro5, Euro6, EuroEev</code> </p>"}, "RouteEngineType": {"type": "string", "enum": ["Electric", "InternalCombustion", "PluginHybrid"]}, "RouteEnterHighwayStepDetails": {"type": "structure", "required": ["Intersection"], "members": {"Intersection": {"shape": "LocalizedStringList", "documentation": "<p>Name of the intersection, if applicable to the step.</p>"}, "SteeringDirection": {"shape": "RouteSteeringDirection", "documentation": "<p>Steering direction for the step.</p>"}, "TurnAngle": {"shape": "TurnAngle", "documentation": "<p><PERSON><PERSON> of the turn.</p>"}, "TurnIntensity": {"shape": "RouteTurnIntensity", "documentation": "<p>Intensity of the turn.</p>"}}, "documentation": "<p>Details related to the enter highway step.</p>"}, "RouteExclusionOptions": {"type": "structure", "required": ["Countries"], "members": {"Countries": {"shape": "CountryCodeList", "documentation": "<p>List of countries to be avoided defined by two-letter or three-letter country codes.</p>"}}, "documentation": "<p>Specifies strict exclusion options for the route calculation. This setting mandates that the router will avoid any routes that include the specified options, rather than merely attempting to minimize them.</p>"}, "RouteExitStepDetails": {"type": "structure", "required": ["Intersection"], "members": {"Intersection": {"shape": "LocalizedStringList", "documentation": "<p>Name of the intersection, if applicable to the step.</p>"}, "RelativeExit": {"shape": "RouteExitStepDetailsRelativeExitInteger", "documentation": "<p>Exit to be taken.</p>"}, "SteeringDirection": {"shape": "RouteSteeringDirection", "documentation": "<p>Steering direction for the step.</p>"}, "TurnAngle": {"shape": "TurnAngle", "documentation": "<p><PERSON><PERSON> of the turn.</p>"}, "TurnIntensity": {"shape": "RouteTurnIntensity", "documentation": "<p>Intensity of the turn.</p>"}}, "documentation": "<p>Details related to the exit step.</p>"}, "RouteExitStepDetailsRelativeExitInteger": {"type": "integer", "box": true, "max": 12, "min": 1}, "RouteFerryAfterTravelStep": {"type": "structure", "required": ["Duration", "Type"], "members": {"Duration": {"shape": "DurationSeconds", "documentation": "<p>Duration of the step.</p> <p> <b>Unit</b>: <code>seconds</code> </p>"}, "Instruction": {"shape": "String", "documentation": "<p>Brief description of the step in the requested language.</p> <note> <p>Only available when the TravelStepType is Default.</p> </note>"}, "Type": {"shape": "RouteFerryAfterTravelStepType", "documentation": "<p>Type of the step.</p>"}}, "documentation": "<p>Steps of a leg that must be performed after the travel portion of the leg.</p>"}, "RouteFerryAfterTravelStepList": {"type": "list", "member": {"shape": "RouteFerryAfterTravelStep"}}, "RouteFerryAfterTravelStepType": {"type": "string", "enum": ["Deboard"]}, "RouteFerryArrival": {"type": "structure", "required": ["Place"], "members": {"Place": {"shape": "Route<PERSON>erry<PERSON>lace", "documentation": "<p>The place details.</p>"}, "Time": {"shape": "TimestampWithTimezoneOffset", "documentation": "<p>The time.</p>"}}, "documentation": "<p>Details corresponding to the arrival for the leg.</p>"}, "RouteFerryBeforeTravelStep": {"type": "structure", "required": ["Duration", "Type"], "members": {"Duration": {"shape": "DurationSeconds", "documentation": "<p>Duration of the step.</p> <p> <b>Unit</b>: <code>seconds</code> </p>"}, "Instruction": {"shape": "String", "documentation": "<p>Brief description of the step in the requested language.</p> <note> <p>Only available when the TravelStepType is Default.</p> </note>"}, "Type": {"shape": "RouteFerryBeforeTravelStepType", "documentation": "<p>Type of the step.</p>"}}, "documentation": "<p>Steps of a leg that must be performed before the travel portion of the leg.</p>"}, "RouteFerryBeforeTravelStepList": {"type": "list", "member": {"shape": "RouteFerryBeforeTravelStep"}}, "RouteFerryBeforeTravelStepType": {"type": "string", "enum": ["Board"]}, "RouteFerryDeparture": {"type": "structure", "required": ["Place"], "members": {"Place": {"shape": "Route<PERSON>erry<PERSON>lace", "documentation": "<p>The place details.</p>"}, "Time": {"shape": "TimestampWithTimezoneOffset", "documentation": "<p>The time.</p>"}}, "documentation": "<p>Details corresponding to the departure for the leg.</p>"}, "RouteFerryLegDetails": {"type": "structure", "required": ["AfterTravelSteps", "Arrival", "BeforeTravelSteps", "Departure", "Notices", "PassThroughWaypoints", "Spans", "TravelSteps"], "members": {"AfterTravelSteps": {"shape": "RouteFerryAfterTravelStepList", "documentation": "<p>Steps of a leg that must be performed after the travel portion of the leg.</p>"}, "Arrival": {"shape": "RouteFerryArrival", "documentation": "<p>Details corresponding to the arrival for the leg.</p>"}, "BeforeTravelSteps": {"shape": "RouteFerryBeforeTravelStepList", "documentation": "<p>Steps of a leg that must be performed before the travel portion of the leg.</p>"}, "Departure": {"shape": "RouteFerryDeparture", "documentation": "<p>Details corresponding to the departure for the leg.</p>"}, "Notices": {"shape": "RouteFerryNoticeList", "documentation": "<p>Notices are additional information returned that indicate issues that occurred during route calculation.</p>"}, "PassThroughWaypoints": {"shape": "RoutePassThroughWaypointList", "documentation": "<p>Waypoints that were passed through during the leg. This includes the waypoints that were configured with the PassThrough option.</p>"}, "RouteName": {"shape": "String", "documentation": "<p>Route name of the ferry line.</p>"}, "Spans": {"shape": "RouteFerrySpanList", "documentation": "<p>Spans that were computed for the requested SpanAdditionalFeatures.</p>"}, "Summary": {"shape": "Route<PERSON>erry<PERSON>", "documentation": "<p>Summarized details of the leg.</p>"}, "TravelSteps": {"shape": "RouteFerryTravelStepList", "documentation": "<p>Steps of a leg that must be performed before the travel portion of the leg.</p>"}}, "documentation": "<p>FerryLegDetails is populated when the Leg type is Ferry, and provides additional information that is specific</p>"}, "RouteFerryNotice": {"type": "structure", "required": ["Code"], "members": {"Code": {"shape": "RouteFerryNoticeCode", "documentation": "<p>Code corresponding to the issue.</p>"}, "Impact": {"shape": "RouteNoticeImpact", "documentation": "<p>Impact corresponding to the issue. While Low impact notices can be safely ignored, High impact notices must be evaluated further to determine the impact.</p>"}}, "documentation": "<p>Notices are additional information returned that indicate issues that occurred during route calculation.</p>"}, "RouteFerryNoticeCode": {"type": "string", "enum": ["AccuratePolylineUnavailable", "NoSchedule", "Other", "ViolatedAvoidFerry", "ViolatedAvoidRailFerry", "SeasonalClosure"]}, "RouteFerryNoticeList": {"type": "list", "member": {"shape": "RouteFerryNotice"}}, "RouteFerryOverviewSummary": {"type": "structure", "required": ["Distance", "Duration"], "members": {"Distance": {"shape": "DistanceMeters", "documentation": "<p>Distance of the step.</p>"}, "Duration": {"shape": "DurationSeconds", "documentation": "<p>Duration of the step.</p> <p> <b>Unit</b>: <code>seconds</code> </p>"}}, "documentation": "<p>Summarized details of the leg.</p>"}, "RouteFerryPlace": {"type": "structure", "required": ["Position"], "members": {"Name": {"shape": "String", "documentation": "<p>The name of the place.</p>"}, "OriginalPosition": {"shape": "Position23", "documentation": "<p>Position provided in the request.</p>"}, "Position": {"shape": "Position23", "documentation": "<p>Position defined as <code>[longitude, latitude]</code>.</p>"}, "WaypointIndex": {"shape": "RouteFerryPlaceWaypointIndexInteger", "documentation": "<p>Index of the waypoint in the request.</p>"}}, "documentation": "<p>Position provided in the request.</p>"}, "RouteFerryPlaceWaypointIndexInteger": {"type": "integer", "box": true, "min": 0}, "RouteFerrySpan": {"type": "structure", "members": {"Country": {"shape": "CountryCode3", "documentation": "<p>3 letter Country code corresponding to the Span.</p>"}, "Distance": {"shape": "DistanceMeters", "documentation": "<p>Distance of the computed span. This feature doesn't split a span, but is always computed on a span split by other properties.</p> <p> <b>Unit</b>: <code>meters</code> </p>"}, "Duration": {"shape": "DurationSeconds", "documentation": "<p>Duration of the computed span. This feature doesn't split a span, but is always computed on a span split by other properties.</p> <p> <b>Unit</b>: <code>seconds</code> </p>"}, "GeometryOffset": {"shape": "RouteFerrySpanGeometryOffsetInteger", "documentation": "<p>Offset in the leg geometry corresponding to the start of this span.</p>"}, "Names": {"shape": "LocalizedStringList", "documentation": "<p>Provides an array of names of the ferry span in available languages.</p>"}, "Region": {"shape": "RouteFerrySpanRegionString", "documentation": "<p>2-3 letter Region code corresponding to the Span. This is either a province or a state.</p>"}}, "documentation": "<p>Span computed for the requested SpanAdditionalFeatures.</p>"}, "RouteFerrySpanGeometryOffsetInteger": {"type": "integer", "box": true, "min": 0}, "RouteFerrySpanList": {"type": "list", "member": {"shape": "RouteFerrySpan"}}, "RouteFerrySpanRegionString": {"type": "string", "max": 3, "min": 0}, "RouteFerrySummary": {"type": "structure", "members": {"Overview": {"shape": "RouteFerryOverviewSummary", "documentation": "<p>Summarized details for the leg including before travel, travel and after travel steps.</p>"}, "TravelOnly": {"shape": "RouteFerryTravelOnlySummary", "documentation": "<p>Summarized details for the leg including travel steps only. The Distance for the travel only portion of the journey is in meters</p>"}}, "documentation": "<p>Summarized details for the leg including travel steps only. The Distance for the travel only portion of the journey is the same as the Distance within the Overview summary.</p>"}, "RouteFerryTravelOnlySummary": {"type": "structure", "required": ["Duration"], "members": {"Duration": {"shape": "DurationSeconds", "documentation": "<p>Total duration in free flowing traffic, which is the best case or shortest duration possible to cover the leg.</p> <p> <b>Unit</b>: <code>seconds</code> </p>"}}, "documentation": "<p>Summarized details for the leg including travel steps only. The Distance for the travel only portion of the journey is the same as the Distance within the Overview summary.</p>"}, "RouteFerryTravelStep": {"type": "structure", "required": ["Duration", "Type"], "members": {"Distance": {"shape": "DistanceMeters", "documentation": "<p>Distance of the step.</p>"}, "Duration": {"shape": "DurationSeconds", "documentation": "<p>Duration of the step.</p> <p> <b>Unit</b>: <code>seconds</code> </p>"}, "GeometryOffset": {"shape": "RouteFerryTravelStepGeometryOffsetInteger", "documentation": "<p>Offset in the leg geometry corresponding to the start of this step.</p>"}, "Instruction": {"shape": "String", "documentation": "<p>Brief description of the step in the requested language.</p> <note> <p>Only available when the TravelStepType is Default.</p> </note>"}, "Type": {"shape": "RouteFerryTravelStepType", "documentation": "<p>Type of the step.</p>"}}, "documentation": "<p>Steps of a leg that must be performed during the travel portion of the leg.</p>"}, "RouteFerryTravelStepGeometryOffsetInteger": {"type": "integer", "box": true, "min": 0}, "RouteFerryTravelStepList": {"type": "list", "member": {"shape": "RouteFerryTravelStep"}}, "RouteFerryTravelStepType": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "Continue", "Arrive"]}, "RouteHazardousCargoType": {"type": "string", "enum": ["Combustible", "Corrosive", "Explosive", "Flammable", "Gas", "HarmfulToWater", "Organic", "Other", "Poison", "PoisonousInhalation", "Radioactive"]}, "RouteHazardousCargoTypeList": {"type": "list", "member": {"shape": "RouteHazardousCargoType"}, "max": 11, "min": 0}, "RouteKeepStepDetails": {"type": "structure", "required": ["Intersection"], "members": {"Intersection": {"shape": "LocalizedStringList", "documentation": "<p>Name of the intersection, if applicable to the step.</p>"}, "SteeringDirection": {"shape": "RouteSteeringDirection", "documentation": "<p>Steering direction for the step.</p>"}, "TurnAngle": {"shape": "TurnAngle", "documentation": "<p><PERSON><PERSON> of the turn.</p>"}, "TurnIntensity": {"shape": "RouteTurnIntensity", "documentation": "<p>Intensity of the turn.</p>"}}, "documentation": "<p>Details that are specific to a Keep step.</p>"}, "RouteLeg": {"type": "structure", "required": ["Geometry", "TravelMode", "Type"], "members": {"FerryLegDetails": {"shape": "RouteFerryLegDetails", "documentation": "<p>FerryLegDetails is populated when the Leg type is Ferry, and provides additional information that is specific</p>"}, "Geometry": {"shape": "RouteLegGeometry", "documentation": "<p>Geometry of the area to be avoided.</p>"}, "Language": {"shape": "LanguageTag", "documentation": "<p>List of languages for instructions within steps in the response.</p>"}, "PedestrianLegDetails": {"shape": "RoutePedestrianLegDetails", "documentation": "<p>Details related to the pedestrian leg.</p>"}, "TravelMode": {"shape": "RouteLegTravelMode", "documentation": "<p>Specifies the mode of transport when calculating a route. Used in estimating the speed of travel and road compatibility.</p> <p>Default Value: <code>Car</code> </p>"}, "Type": {"shape": "RouteLegType", "documentation": "<p>Type of the leg.</p>"}, "VehicleLegDetails": {"shape": "RouteVehicleLegDetails", "documentation": "<p>Details related to the vehicle leg.</p>"}}, "documentation": "<p>A leg is a section of a route from one waypoint to the next. A leg could be of type Vehicle, Pedestrian or Ferry. Legs of different types could occur together within a single route. For example, a car employing the use of a Ferry will contain Vehicle legs corresponding to journey on land, and Ferry legs corresponding to the journey via Ferry.</p>"}, "RouteLegAdditionalFeature": {"type": "string", "enum": ["Elevation", "Incidents", "PassThroughWaypoints", "Summary", "Tolls", "TravelStepInstructions", "TruckRoadTypes", "TypicalDuration", "Zones"]}, "RouteLegAdditionalFeatureList": {"type": "list", "member": {"shape": "RouteLegAdditionalFeature"}, "max": 9, "min": 0}, "RouteLegGeometry": {"type": "structure", "members": {"LineString": {"shape": "LineString", "documentation": "<p>An ordered list of positions used to plot a route on a map.</p> <note> <p>LineString and Polyline are mutually exclusive properties.</p> </note>"}, "Polyline": {"shape": "Polyline", "documentation": "<p>An ordered list of positions used to plot a route on a map in a lossy compression format.</p> <note> <p>LineString and Polyline are mutually exclusive properties.</p> </note>"}}, "documentation": "<p>The returned Route leg geometry.</p>"}, "RouteLegList": {"type": "list", "member": {"shape": "RouteLeg"}}, "RouteLegTravelMode": {"type": "string", "enum": ["Car", "Ferry", "Pedestrian", "<PERSON>ooter", "Truck", "CarShuttleTrain"]}, "RouteLegType": {"type": "string", "enum": ["Ferry", "Pedestrian", "Vehicle"]}, "RouteList": {"type": "list", "member": {"shape": "Route"}}, "RouteMajorRoadLabel": {"type": "structure", "members": {"RoadName": {"shape": "LocalizedString", "documentation": "<p>Name of the road (localized).</p>"}, "RouteNumber": {"shape": "RouteNumber", "documentation": "<p>Route number of the road.</p>"}}, "documentation": "<p>Important labels including names and route numbers that differentiate the current route from the alternatives presented.</p>"}, "RouteMajorRoadLabelsList": {"type": "list", "member": {"shape": "RouteMajorRoadLabel"}, "max": 2, "min": 0}, "RouteMatchingOptions": {"type": "structure", "members": {"NameHint": {"shape": "RouteMatchingOptionsNameHintString", "documentation": "<p>Attempts to match the provided position to a road similar to the provided name.</p>"}, "OnRoadThreshold": {"shape": "DistanceMeters", "documentation": "<p>If the distance to a highway/bridge/tunnel/sliproad is within threshold, the waypoint will be snapped to the highway/bridge/tunnel/sliproad.</p> <p> <b>Unit</b>: <code>meters</code> </p>"}, "Radius": {"shape": "DistanceMeters", "documentation": "<p>Considers all roads within the provided radius to match the provided destination to. The roads that are considered are determined by the provided Strategy.</p> <p> <b>Unit</b>: <code>Meters</code> </p>"}, "Strategy": {"shape": "MatchingStrategy", "documentation": "<p>Strategy that defines matching of the position onto the road network. MatchAny considers all roads possible, whereas MatchMostSignificantRoad matches to the most significant road.</p>"}}, "documentation": "<p>Options related to route matching.</p>"}, "RouteMatchingOptionsNameHintString": {"type": "string", "max": 100, "min": 0, "sensitive": true}, "RouteMatrix": {"type": "list", "member": {"shape": "RouteMatrixRow"}}, "RouteMatrixAllowOptions": {"type": "structure", "members": {"Hot": {"shape": "Boolean", "documentation": "<p>Allow Hot (High Occupancy Toll) lanes while calculating the route.</p> <p>Default value: <code>false</code> </p>"}, "Hov": {"shape": "Boolean", "documentation": "<p>Allow Hov (High Occupancy vehicle) lanes while calculating the route.</p> <p>Default value: <code>false</code> </p>"}}, "documentation": "<p>Allow Options related to the route matrix.</p>"}, "RouteMatrixAutoCircle": {"type": "structure", "members": {"Margin": {"shape": "RouteMatrixAutoCircleMarginLong", "documentation": "<p>The margin provided for the calculation.</p>"}, "MaxRadius": {"shape": "RouteMatrixAutoCircleMaxRadiusLong", "documentation": "<p>The maximum size of the radius provided for the calculation.</p>"}}, "documentation": "<p>Provides the circle that was used while calculating the route.</p>"}, "RouteMatrixAutoCircleMarginLong": {"type": "long", "max": 200000, "min": 0}, "RouteMatrixAutoCircleMaxRadiusLong": {"type": "long", "max": 200000, "min": 0}, "RouteMatrixAvoidanceArea": {"type": "structure", "required": ["Geometry"], "members": {"Geometry": {"shape": "RouteMatrixAvoidanceAreaGeometry", "documentation": "<p>Geometry of the area to be avoided.</p>"}}, "documentation": "<p>Area to be avoided.</p>"}, "RouteMatrixAvoidanceAreaGeometry": {"type": "structure", "members": {"BoundingBox": {"shape": "BoundingBox", "documentation": "<p>Geometry defined as a bounding box. The first pair represents the X and Y coordinates (longitude and latitude,) of the southwest corner of the bounding box; the second pair represents the X and Y coordinates (longitude and latitude) of the northeast corner.</p>"}, "Polygon": {"shape": "RouteMatrixAvoidanceAreaGeometryPolygonList", "documentation": "<p>Geometry defined as a polygon with only one linear ring.</p>"}, "PolylinePolygon": {"shape": "RouteMatrixAvoidanceAreaGeometryPolylinePolygonList", "documentation": "<p>A list of Isoline PolylinePolygon, for each isoline PolylinePolygon, it contains PolylinePolygon of the first linear ring (the outer ring) and from second item to the last item (the inner rings). For more information on polyline encoding, see <a href=\"https://github.com/heremaps/flexiblepolyline/blob/master/README.md\">https://github.com/heremaps/flexiblepolyline/blob/master/README.md</a>.</p>"}}, "documentation": "<p>Geometry of the area to be avoided.</p>"}, "RouteMatrixAvoidanceAreaGeometryPolygonList": {"type": "list", "member": {"shape": "LinearRing"}, "max": 1, "min": 1}, "RouteMatrixAvoidanceAreaGeometryPolylinePolygonList": {"type": "list", "member": {"shape": "PolylineRing"}, "max": 1, "min": 1}, "RouteMatrixAvoidanceOptions": {"type": "structure", "members": {"Areas": {"shape": "RouteMatrixAvoidanceOptionsAreasList", "documentation": "<p>Areas to be avoided.</p>"}, "CarShuttleTrains": {"shape": "Boolean", "documentation": "<p>Avoid car-shuttle-trains while calculating the route.</p>"}, "ControlledAccessHighways": {"shape": "Boolean", "documentation": "<p>Avoid controlled access highways while calculating the route.</p>"}, "DirtRoads": {"shape": "Boolean", "documentation": "<p>Avoid dirt roads while calculating the route.</p>"}, "Ferries": {"shape": "Boolean", "documentation": "<p>Avoid ferries while calculating the route.</p>"}, "TollRoads": {"shape": "Boolean", "documentation": "<p>Avoids roads where the specified toll transponders are the only mode of payment.</p>"}, "TollTransponders": {"shape": "Boolean", "documentation": "<p>Avoids roads where the specified toll transponders are the only mode of payment.</p>"}, "TruckRoadTypes": {"shape": "TruckRoadTypeList", "documentation": "<p>Truck road type identifiers. <code>BK1</code> through <code>BK4</code> apply only to Sweden. <code>A2,A4,B2,B4,C,D,ET2,ET4</code> apply only to Mexico.</p> <note> <p>There are currently no other supported values as of 26th April 2024.</p> </note>"}, "Tunnels": {"shape": "Boolean", "documentation": "<p>Avoid tunnels while calculating the route.</p>"}, "UTurns": {"shape": "Boolean", "documentation": "<p>Avoid U-turns for calculation on highways and motorways.</p>"}, "ZoneCategories": {"shape": "RouteMatrixAvoidanceZoneCategoryList", "documentation": "<p>Zone categories to be avoided.</p>"}}, "documentation": "<p>Specifies options for areas to avoid when calculating the route. This is a best-effort avoidance setting, meaning the router will try to honor the avoidance preferences but may still include restricted areas if no feasible alternative route exists. If avoidance options are not followed, the response will indicate that the avoidance criteria were violated.</p>"}, "RouteMatrixAvoidanceOptionsAreasList": {"type": "list", "member": {"shape": "RouteMatrixAvoidanceArea"}, "max": 250, "min": 0}, "RouteMatrixAvoidanceZoneCategory": {"type": "structure", "members": {"Category": {"shape": "RouteMatrixZoneCategory", "documentation": "<p>Zone category to be avoided.</p>"}}, "documentation": "<p>Zone categories to be avoided.</p>"}, "RouteMatrixAvoidanceZoneCategoryList": {"type": "list", "member": {"shape": "RouteMatrixAvoidanceZoneCategory"}, "max": 3, "min": 0}, "RouteMatrixBoundary": {"type": "structure", "members": {"Geometry": {"shape": "RouteMatrixBoundaryGeometry", "documentation": "<p>Geometry of the area to be avoided.</p>"}, "Unbounded": {"shape": "Boolean", "documentation": "<p>No restrictions in terms of a routing boundary, and is typically used for longer routes.</p>"}}, "documentation": "<p>Boundary within which the matrix is to be calculated. All data, origins and destinations outside the boundary are considered invalid.</p>"}, "RouteMatrixBoundaryGeometry": {"type": "structure", "members": {"AutoCircle": {"shape": "RouteMatrixAutoCircle", "documentation": "<p>Provides the circle that was used while calculating the route.</p>"}, "Circle": {"shape": "Circle", "documentation": "<p>Geometry defined as a circle. When request routing boundary was set as <code>AutoCircle</code>, the response routing boundary will return <code>Circle</code> derived from the <code>AutoCircle</code> settings.</p>"}, "BoundingBox": {"shape": "BoundingBox", "documentation": "<p>Geometry defined as a bounding box. The first pair represents the X and Y coordinates (longitude and latitude,) of the southwest corner of the bounding box; the second pair represents the X and Y coordinates (longitude and latitude) of the northeast corner.</p>"}, "Polygon": {"shape": "RouteMatrixBoundaryGeometryPolygonList", "documentation": "<p>Geometry defined as a polygon with only one linear ring.</p>"}}, "documentation": "<p>Geometry of the routing boundary.</p>"}, "RouteMatrixBoundaryGeometryPolygonList": {"type": "list", "member": {"shape": "LinearRing"}, "max": 1, "min": 1}, "RouteMatrixCarOptions": {"type": "structure", "members": {"LicensePlate": {"shape": "RouteMatrixVehicleLicensePlate", "documentation": "<p>The vehicle License Plate.</p>"}, "MaxSpeed": {"shape": "RouteMatrixCarOptionsMaxSpeedDouble", "documentation": "<p>Maximum speed</p> <p> <b>Unit</b>: <code>KilometersPerHour</code> </p>", "box": true}, "Occupancy": {"shape": "RouteMatrixCarOptionsOccupancyInteger", "documentation": "<p>The number of occupants in the vehicle.</p> <p>Default Value: <code>1</code> </p>"}}, "documentation": "<p>Travel mode options when the provided travel mode is <code>Car</code>.</p>"}, "RouteMatrixCarOptionsMaxSpeedDouble": {"type": "double", "max": 252.0, "min": 3.6}, "RouteMatrixCarOptionsOccupancyInteger": {"type": "integer", "box": true, "min": 1}, "RouteMatrixDestination": {"type": "structure", "required": ["Position"], "members": {"Options": {"shape": "RouteMatrixDestinationOptions", "documentation": "<p>Destination related options.</p>"}, "Position": {"shape": "Position", "documentation": "<p>Position defined as <code>[longitude, latitude]</code>.</p>"}}, "documentation": "<p>The route destination.</p>"}, "RouteMatrixDestinationOptions": {"type": "structure", "members": {"AvoidActionsForDistance": {"shape": "RouteMatrixDestinationOptionsAvoidActionsForDistanceLong", "documentation": "<p>Avoids actions for the provided distance. This is typically to consider for users in moving vehicles who may not have sufficient time to make an action at an origin or a destination.</p>"}, "Heading": {"shape": "Heading", "documentation": "<p>GPS Heading at the position.</p>"}, "Matching": {"shape": "RouteMatrixMatchingOptions", "documentation": "<p>Options to configure matching the provided position to the road network.</p>"}, "SideOfStreet": {"shape": "RouteMatrixSideOfStreetOptions", "documentation": "<p>Options to configure matching the provided position to a side of the street.</p>"}}, "documentation": "<p>Options related to the destination.</p>"}, "RouteMatrixDestinationOptionsAvoidActionsForDistanceLong": {"type": "long", "min": 0}, "RouteMatrixEntry": {"type": "structure", "required": ["Distance", "Duration"], "members": {"Distance": {"shape": "DistanceMeters", "documentation": "<p>The total distance of travel for the route.</p>"}, "Duration": {"shape": "DurationSeconds", "documentation": "<p>The expected duration of travel for the route.</p> <p> <b>Unit</b>: <code>seconds</code> </p>"}, "Error": {"shape": "RouteMatrixErrorCode", "documentation": "<p>Error code that occurred during calculation of the route.</p>"}}, "documentation": "<p>The calculated route matrix containing the results for all pairs of Origins to Destination positions. Each row corresponds to one entry in Origins. Each entry in the row corresponds to the route from that entry in Origins to an entry in Destination positions.</p>"}, "RouteMatrixErrorCode": {"type": "string", "enum": ["NoMatch", "NoMatchDestination", "NoMatchOrigin", "NoRoute", "OutOfBounds", "OutOfBoundsDestination", "OutOfBoundsOrigin", "Other", "Violation"]}, "RouteMatrixExclusionOptions": {"type": "structure", "required": ["Countries"], "members": {"Countries": {"shape": "CountryCodeList", "documentation": "<p>List of countries to be avoided defined by two-letter or three-letter country codes.</p>"}}, "documentation": "<p>Specifies strict exclusion options for the route calculation. This setting mandates that the router will avoid any routes that include the specified options, rather than merely attempting to minimize them.</p>"}, "RouteMatrixHazardousCargoType": {"type": "string", "enum": ["Combustible", "Corrosive", "Explosive", "Flammable", "Gas", "HarmfulToWater", "Organic", "Other", "Poison", "PoisonousInhalation", "Radioactive"]}, "RouteMatrixHazardousCargoTypeList": {"type": "list", "member": {"shape": "RouteMatrixHazardousCargoType"}, "max": 11, "min": 0}, "RouteMatrixMatchingOptions": {"type": "structure", "members": {"NameHint": {"shape": "SensitiveString", "documentation": "<p>Attempts to match the provided position to a road similar to the provided name.</p>"}, "OnRoadThreshold": {"shape": "RouteMatrixMatchingOptionsOnRoadThresholdLong", "documentation": "<p>If the distance to a highway/bridge/tunnel/sliproad is within threshold, the waypoint will be snapped to the highway/bridge/tunnel/sliproad.</p> <p> <b>Unit</b>: <code>meters</code> </p>"}, "Radius": {"shape": "DistanceMeters", "documentation": "<p>Considers all roads within the provided radius to match the provided destination to. The roads that are considered are determined by the provided Strategy.</p> <p> <b>Unit</b>: <code>Meters</code> </p>"}, "Strategy": {"shape": "MatchingStrategy", "documentation": "<p>Strategy that defines matching of the position onto the road network. MatchAny considers all roads possible, whereas MatchMostSignificantRoad matches to the most significant road.</p>"}}, "documentation": "<p>Matching options.</p>"}, "RouteMatrixMatchingOptionsOnRoadThresholdLong": {"type": "long", "min": 0}, "RouteMatrixOrigin": {"type": "structure", "required": ["Position"], "members": {"Options": {"shape": "RouteMatrixOriginOptions", "documentation": "<p>Origin related options.</p>"}, "Position": {"shape": "Position", "documentation": "<p>Position defined as <code>[longitude, latitude]</code>.</p>"}}, "documentation": "<p>The start position for the route.</p>"}, "RouteMatrixOriginOptions": {"type": "structure", "members": {"AvoidActionsForDistance": {"shape": "RouteMatrixOriginOptionsAvoidActionsForDistanceLong", "documentation": "<p>Avoids actions for the provided distance. This is typically to consider for users in moving vehicles who may not have sufficient time to make an action at an origin or a destination.</p>"}, "Heading": {"shape": "Heading", "documentation": "<p>GPS Heading at the position.</p>"}, "Matching": {"shape": "RouteMatrixMatchingOptions", "documentation": "<p>Options to configure matching the provided position to the road network.</p>"}, "SideOfStreet": {"shape": "RouteMatrixSideOfStreetOptions", "documentation": "<p>Options to configure matching the provided position to a side of the street.</p>"}}, "documentation": "<p>Origin related options.</p>"}, "RouteMatrixOriginOptionsAvoidActionsForDistanceLong": {"type": "long", "min": 0}, "RouteMatrixRow": {"type": "list", "member": {"shape": "RouteMatrixEntry"}}, "RouteMatrixScooterOptions": {"type": "structure", "members": {"LicensePlate": {"shape": "RouteMatrixVehicleLicensePlate", "documentation": "<p>The vehicle License Plate.</p>"}, "MaxSpeed": {"shape": "RouteMatrixScooterOptionsMaxSpeedDouble", "documentation": "<p>Maximum speed.</p> <p> <b>Unit</b>: <code>KilometersPerHour</code> </p>", "box": true}, "Occupancy": {"shape": "RouteMatrixScooterOptionsOccupancyInteger", "documentation": "<p>The number of occupants in the vehicle.</p> <p>Default Value: <code>1</code> </p>"}}, "documentation": "<p>Travel mode options when the provided travel mode is <code>Scooter</code> </p>"}, "RouteMatrixScooterOptionsMaxSpeedDouble": {"type": "double", "max": 252.0, "min": 3.6}, "RouteMatrixScooterOptionsOccupancyInteger": {"type": "integer", "box": true, "min": 1}, "RouteMatrixSideOfStreetOptions": {"type": "structure", "required": ["Position"], "members": {"Position": {"shape": "Position", "documentation": "<p>Position defined as <code>[longitude, latitude]</code>.</p>"}, "UseWith": {"shape": "SideOfStreetMatchingStrategy", "documentation": "<p>Strategy that defines when the side of street position should be used. AnyStreet will always use the provided position.</p> <p>Default Value: <code>DividedStreetOnly</code> </p>"}}, "documentation": "<p>Options to configure matching the provided position to a side of the street.</p>"}, "RouteMatrixTrafficOptions": {"type": "structure", "members": {"FlowEventThresholdOverride": {"shape": "DurationSeconds", "documentation": "<p>Duration for which flow traffic is considered valid. For this period, the flow traffic is used over historical traffic data. Flow traffic refers to congestion, which changes very quickly. Duration in seconds for which flow traffic event would be considered valid. While flow traffic event is valid it will be used over the historical traffic data. </p>"}, "Usage": {"shape": "TrafficUsage", "documentation": "<p>Determines if traffic should be used or ignored while calculating the route.</p> <p>Default Value: <code>UseTrafficData</code> </p>"}}, "documentation": "<p>Traffic related options.</p>"}, "RouteMatrixTrailerOptions": {"type": "structure", "members": {"TrailerCount": {"shape": "RouteMatrixTrailerOptionsTrailerCountInteger", "documentation": "<p>Number of trailers attached to the vehicle.</p> <p>Default Value: <code>0</code> </p>"}}, "documentation": "<p>Trailer options corresponding to the vehicle.</p>"}, "RouteMatrixTrailerOptionsTrailerCountInteger": {"type": "integer", "box": true, "max": 255, "min": 0}, "RouteMatrixTravelMode": {"type": "string", "enum": ["Car", "Pedestrian", "<PERSON>ooter", "Truck"]}, "RouteMatrixTravelModeOptions": {"type": "structure", "members": {"Car": {"shape": "RouteMatrixCarOptions", "documentation": "<p>Travel mode options when the provided travel mode is \"Car\"</p>"}, "Scooter": {"shape": "RouteMatrixScooterOptions", "documentation": "<p>Travel mode options when the provided travel mode is <code>Scooter</code> </p> <note> <p>When travel mode is set to <code>Scooter</code>, then the avoidance option <code>ControlledAccessHighways</code> defaults to <code>true</code>.</p> </note>"}, "Truck": {"shape": "RouteMatrixTruckOptions", "documentation": "<p>Travel mode options when the provided travel mode is \"Truck\"</p>"}}, "documentation": "<p>Travel mode related options for the provided travel mode.</p>"}, "RouteMatrixTruckOptions": {"type": "structure", "members": {"AxleCount": {"shape": "RouteMatrixTruckOptionsAxleCountInteger", "documentation": "<p>Total number of axles of the vehicle.</p>"}, "GrossWeight": {"shape": "WeightKilograms", "documentation": "<p>Gross weight of the vehicle including trailers, and goods at capacity.</p> <p> <b>Unit</b>: <code>Kilograms</code> </p>"}, "HazardousCargos": {"shape": "RouteMatrixHazardousCargoTypeList", "documentation": "<p>List of Hazardous cargo contained in the vehicle.</p>"}, "Height": {"shape": "RouteMatrixTruckOptionsHeightLong", "documentation": "<p>Height of the vehicle.</p> <p> <b>Unit</b>: <code>centimeters</code> </p>"}, "KpraLength": {"shape": "DimensionCentimeters", "documentation": "<p>Kingpin to rear axle length of the vehicle</p> <p> <b>Unit</b>: <code>centimeters</code> </p>"}, "Length": {"shape": "RouteMatrixTruckOptionsLengthLong", "documentation": "<p>Length of the vehicle.</p> <p> <b>Unit</b>: <code>centimeters</code> </p>"}, "LicensePlate": {"shape": "RouteMatrixVehicleLicensePlate", "documentation": "<p>The vehicle License Plate.</p>"}, "MaxSpeed": {"shape": "RouteMatrixTruckOptionsMaxSpeedDouble", "documentation": "<p>Maximum speed</p> <p> <b>Unit</b>: <code>KilometersPerHour</code> </p>", "box": true}, "Occupancy": {"shape": "RouteMatrixTruckOptionsOccupancyInteger", "documentation": "<p>The number of occupants in the vehicle.</p> <p>Default Value: <code>1</code> </p>"}, "PayloadCapacity": {"shape": "WeightKilograms", "documentation": "<p>Payload capacity of the vehicle and trailers attached.</p> <p> <b>Unit</b>: <code>kilograms</code> </p>"}, "Trailer": {"shape": "RouteMatrixTrailerOptions", "documentation": "<p>Trailer options corresponding to the vehicle.</p>"}, "TruckType": {"shape": "RouteMatrixTruckType", "documentation": "<p>Type of the truck.</p>"}, "TunnelRestrictionCode": {"shape": "TunnelRestrictionCode", "documentation": "<p>The tunnel restriction code.</p> <p>Tunnel categories in this list indicate the restrictions which apply to certain tunnels in Great Britain. They relate to the types of dangerous goods that can be transported through them.</p> <ul> <li> <p> <i>Tunnel Category B</i> </p> <ul> <li> <p> <i>Risk Level</i>: Limited risk</p> </li> <li> <p> <i>Restrictions</i>: Few restrictions</p> </li> </ul> </li> <li> <p> <i>Tunnel Category C</i> </p> <ul> <li> <p> <i>Risk Level</i>: Medium risk</p> </li> <li> <p> <i>Restrictions</i>: Some restrictions</p> </li> </ul> </li> <li> <p> <i>Tunnel Category D</i> </p> <ul> <li> <p> <i>Risk Level</i>: High risk</p> </li> <li> <p> <i>Restrictions</i>: Many restrictions occur</p> </li> </ul> </li> <li> <p> <i>Tunnel Category E</i> </p> <ul> <li> <p> <i>Risk Level</i>: Very high risk</p> </li> <li> <p> <i>Restrictions</i>: Restricted tunnel</p> </li> </ul> </li> </ul>"}, "WeightPerAxle": {"shape": "WeightKilograms", "documentation": "<p>Heaviest weight per axle irrespective of the axle type or the axle group. Meant for usage in countries where the differences in axle types or axle groups are not distinguished.</p> <p> <b>Unit</b>: <code>Kilograms</code> </p>"}, "WeightPerAxleGroup": {"shape": "WeightPerAxleGroup", "documentation": "<p>Specifies the total weight for the specified axle group. Meant for usage in countries that have different regulations based on the axle group type.</p>"}, "Width": {"shape": "RouteMatrixTruckOptionsWidthLong", "documentation": "<p>Width of the vehicle.</p> <p> <b>Unit</b>: <code>centimeters</code> </p>"}}, "documentation": "<p>Travel mode options when the provided travel mode is \"Truck\"</p>"}, "RouteMatrixTruckOptionsAxleCountInteger": {"type": "integer", "box": true, "max": 255, "min": 2}, "RouteMatrixTruckOptionsHeightLong": {"type": "long", "max": 5000, "min": 0}, "RouteMatrixTruckOptionsLengthLong": {"type": "long", "max": 30000, "min": 0}, "RouteMatrixTruckOptionsMaxSpeedDouble": {"type": "double", "max": 252.0, "min": 3.6}, "RouteMatrixTruckOptionsOccupancyInteger": {"type": "integer", "box": true, "min": 1}, "RouteMatrixTruckOptionsWidthLong": {"type": "long", "max": 5000, "min": 0}, "RouteMatrixTruckType": {"type": "string", "enum": ["LightTruck", "StraightTruck", "Tractor"]}, "RouteMatrixVehicleLicensePlate": {"type": "structure", "members": {"LastCharacter": {"shape": "RouteMatrixVehicleLicensePlateLastCharacterString", "documentation": "<p>The last character of the License Plate.</p>"}}, "documentation": "<p>The vehicle License Plate.</p>"}, "RouteMatrixVehicleLicensePlateLastCharacterString": {"type": "string", "max": 1, "min": 1}, "RouteMatrixZoneCategory": {"type": "string", "enum": ["CongestionPricing", "Environmental", "Vignette"]}, "RouteNoticeDetailRange": {"type": "structure", "members": {"Min": {"shape": "RouteNoticeDetailRangeMinInteger", "documentation": "<p>Minimum value for the range.</p>"}, "Max": {"shape": "RouteNoticeDetailRangeMaxInteger", "documentation": "<p>Maximum value for the range.</p>"}}, "documentation": "<p>Notice Detail that is a range.</p>"}, "RouteNoticeDetailRangeMaxInteger": {"type": "integer", "box": true, "min": 0}, "RouteNoticeDetailRangeMinInteger": {"type": "integer", "box": true, "min": 0}, "RouteNoticeImpact": {"type": "string", "enum": ["High", "Low"]}, "RouteNumber": {"type": "structure", "required": ["Value"], "members": {"Direction": {"shape": "RouteDirection", "documentation": "<p>Directional identifier of the route.</p>"}, "Language": {"shape": "LanguageTag", "documentation": "<p>List of languages for instructions corresponding to the route number.</p>"}, "Value": {"shape": "String", "documentation": "<p>The route number.</p>"}}, "documentation": "<p>The route number.</p>"}, "RouteNumberList": {"type": "list", "member": {"shape": "RouteNumber"}}, "RouteOriginOptions": {"type": "structure", "members": {"AvoidActionsForDistance": {"shape": "RouteOriginOptionsAvoidActionsForDistanceLong", "documentation": "<p>Avoids actions for the provided distance. This is typically to consider for users in moving vehicles who may not have sufficient time to make an action at an origin or a destination.</p>"}, "AvoidUTurns": {"shape": "Boolean", "documentation": "<p>Avoid U-turns for calculation on highways and motorways.</p>"}, "Heading": {"shape": "Heading", "documentation": "<p>GPS Heading at the position.</p>"}, "Matching": {"shape": "RouteMatchingOptions", "documentation": "<p>Options to configure matching the provided position to the road network.</p>"}, "SideOfStreet": {"shape": "RouteSideOfStreetOptions", "documentation": "<p>Options to configure matching the provided position to a side of the street.</p>"}}, "documentation": "<p>Origin related options.</p>"}, "RouteOriginOptionsAvoidActionsForDistanceLong": {"type": "long", "max": 2000}, "RoutePassThroughPlace": {"type": "structure", "required": ["Position"], "members": {"OriginalPosition": {"shape": "Position23", "documentation": "<p>Position provided in the request.</p>"}, "Position": {"shape": "Position23", "documentation": "<p>Position defined as <code>[longitude, latitude]</code>.</p>"}, "WaypointIndex": {"shape": "RoutePassThroughPlaceWaypointIndexInteger", "documentation": "<p>Index of the waypoint in the request.</p>"}}, "documentation": "<p>The place where the waypoint is passed through and not treated as a stop.</p>"}, "RoutePassThroughPlaceWaypointIndexInteger": {"type": "integer", "box": true, "min": 0}, "RoutePassThroughWaypoint": {"type": "structure", "required": ["Place"], "members": {"GeometryOffset": {"shape": "RoutePassThroughWaypointGeometryOffsetInteger", "documentation": "<p>Offset in the leg geometry corresponding to the start of this step.</p>"}, "Place": {"shape": "RoutePassThroughPlace", "documentation": "<p>The place details.</p>"}}, "documentation": "<p>If the waypoint should be treated as a stop. If yes, the route is split up into different legs around the stop.</p>"}, "RoutePassThroughWaypointGeometryOffsetInteger": {"type": "integer", "box": true, "min": 0}, "RoutePassThroughWaypointList": {"type": "list", "member": {"shape": "RoutePassThroughWaypoint"}}, "RoutePedestrianArrival": {"type": "structure", "required": ["Place"], "members": {"Place": {"shape": "RoutePedestrian<PERSON>lace", "documentation": "<p>The place details.</p>"}, "Time": {"shape": "TimestampWithTimezoneOffset", "documentation": "<p>The time.</p>"}}, "documentation": "<p>Details corresponding to the arrival for a leg.</p> <p>Time format:<code>YYYY-MM-DDThh:mm:ss.sssZ | YYYY-MM-DDThh:mm:ss.sss+hh:mm</code> </p> <p>Examples:</p> <p> <code>2020-04-22T17:57:24Z</code> </p> <p> <code>2020-04-22T17:57:24+02:00</code> </p>"}, "RoutePedestrianDeparture": {"type": "structure", "required": ["Place"], "members": {"Place": {"shape": "RoutePedestrian<PERSON>lace", "documentation": "<p>The place details.</p>"}, "Time": {"shape": "TimestampWithTimezoneOffset", "documentation": "<p>The time.</p>"}}, "documentation": "<p>Details corresponding to the departure for a leg.</p> <p>Time format:<code>YYYY-MM-DDThh:mm:ss.sssZ | YYYY-MM-DDThh:mm:ss.sss+hh:mm</code> </p> <p>Examples:</p> <p> <code>2020-04-22T17:57:24Z</code> </p> <p> <code>2020-04-22T17:57:24+02:00</code> </p>"}, "RoutePedestrianLegDetails": {"type": "structure", "required": ["Arrival", "Departure", "Notices", "PassThroughWaypoints", "Spans", "TravelSteps"], "members": {"Arrival": {"shape": "RoutePedestrianArrival", "documentation": "<p>Details corresponding to the arrival for the leg.</p>"}, "Departure": {"shape": "RoutePedestrianDeparture", "documentation": "<p>Details corresponding to the departure for the leg.</p>"}, "Notices": {"shape": "RoutePedestrianNoticeList", "documentation": "<p>Notices are additional information returned that indicate issues that occurred during route calculation.</p>"}, "PassThroughWaypoints": {"shape": "RoutePassThroughWaypointList", "documentation": "<p>Waypoints that were passed through during the leg. This includes the waypoints that were configured with the PassThrough option.</p>"}, "Spans": {"shape": "RoutePedestrianSpanList", "documentation": "<p>Spans that were computed for the requested SpanAdditionalFeatures.</p>"}, "Summary": {"shape": "RoutePedestrianSummary", "documentation": "<p>Summarized details of the leg.</p>"}, "TravelSteps": {"shape": "RoutePedestrianTravelStepList", "documentation": "<p>Steps of a leg that must be performed before the travel portion of the leg.</p>"}}, "documentation": "<p>Details that are specific to a pedestrian leg.</p>"}, "RoutePedestrianNotice": {"type": "structure", "required": ["Code"], "members": {"Code": {"shape": "RoutePedestrianNoticeCode", "documentation": "<p>Code corresponding to the issue.</p>"}, "Impact": {"shape": "RouteNoticeImpact", "documentation": "<p>Impact corresponding to the issue. While Low impact notices can be safely ignored, High impact notices must be evaluated further to determine the impact.</p>"}}, "documentation": "<p>Notices are additional information returned that indicate issues that occurred during route calculation.</p>"}, "RoutePedestrianNoticeCode": {"type": "string", "enum": ["AccuratePolylineUnavailable", "Other", "ViolatedAvoidDirtRoad", "ViolatedAvoidTunnel", "ViolatedPedestrianOption"]}, "RoutePedestrianNoticeList": {"type": "list", "member": {"shape": "RoutePedestrianNotice"}}, "RoutePedestrianOptions": {"type": "structure", "members": {"Speed": {"shape": "RoutePedestrianOptionsSpeedDouble", "documentation": "<p>Walking speed in Kilometers per hour.</p>", "box": true}}, "documentation": "<p>Options related to the pedestrian.</p>"}, "RoutePedestrianOptionsSpeedDouble": {"type": "double", "max": 7.2, "min": 1.8}, "RoutePedestrianOverviewSummary": {"type": "structure", "required": ["Distance", "Duration"], "members": {"Distance": {"shape": "DistanceMeters", "documentation": "<p>Distance of the step.</p>"}, "Duration": {"shape": "DurationSeconds", "documentation": "<p>Duration of the step.</p>"}}, "documentation": "<p>Provides a summary of a pedestrian route step.</p>"}, "RoutePedestrianPlace": {"type": "structure", "required": ["Position"], "members": {"Name": {"shape": "String", "documentation": "<p>The name of the place.</p>"}, "OriginalPosition": {"shape": "Position23", "documentation": "<p>Position provided in the request.</p>"}, "Position": {"shape": "Position23", "documentation": "<p>Position defined as <code>[longitude, latitude]</code>.</p>"}, "SideOfStreet": {"shape": "RouteSideOfStreet", "documentation": "<p>Options to configure matching the provided position to a side of the street.</p>"}, "WaypointIndex": {"shape": "RoutePedestrianPlaceWaypointIndexInteger", "documentation": "<p>Index of the waypoint in the request.</p>"}}, "documentation": "<p>Place details corresponding to the arrival or departure.</p>"}, "RoutePedestrianPlaceWaypointIndexInteger": {"type": "integer", "box": true, "min": 0}, "RoutePedestrianSpan": {"type": "structure", "members": {"BestCaseDuration": {"shape": "DurationSeconds", "documentation": "<p>Duration of the computed span without traffic congestion.</p> <p> <b>Unit</b>: <code>seconds</code> </p>"}, "Country": {"shape": "CountryCode3", "documentation": "<p>3 letter Country code corresponding to the Span.</p>"}, "Distance": {"shape": "DistanceMeters", "documentation": "<p>Distance of the computed span. This feature doesn't split a span, but is always computed on a span split by other properties.</p>"}, "Duration": {"shape": "DurationSeconds", "documentation": "<p>Duration of the computed span. This feature doesn't split a span, but is always computed on a span split by other properties.</p> <p> <b>Unit</b>: <code>seconds</code> </p>"}, "DynamicSpeed": {"shape": "RouteSpanDynamicSpeedDetails", "documentation": "<p>Dynamic speed details corresponding to the span.</p> <p> <b>Unit</b>: <code>KilometersPerHour</code> </p>"}, "FunctionalClassification": {"shape": "RoutePedestrianSpanFunctionalClassificationInteger", "documentation": "<p>Functional classification of the road segment corresponding to the span.</p>"}, "GeometryOffset": {"shape": "RoutePedestrianSpanGeometryOffsetInteger", "documentation": "<p>Offset in the leg geometry corresponding to the start of this span.</p>"}, "Incidents": {"shape": "IndexList", "documentation": "<p>Incidents corresponding to the span. These index into the Incidents in the parent Leg.</p>"}, "Names": {"shape": "LocalizedStringList", "documentation": "<p>Provides an array of names of the pedestrian span in available languages.</p>"}, "PedestrianAccess": {"shape": "RouteSpanPedestrianAccessAttributeList", "documentation": "<p>Access attributes for a pedestrian corresponding to the span.</p>"}, "Region": {"shape": "RoutePedestrianSpanRegionString", "documentation": "<p>2-3 letter Region code corresponding to the Span. This is either a province or a state.</p>"}, "RoadAttributes": {"shape": "RouteSpanRoadAttributeList", "documentation": "<p>Attributes for the road segment corresponding to the span. </p>"}, "RouteNumbers": {"shape": "RouteNumberList", "documentation": "<p>Designated route name or number corresponding to the span.</p>"}, "SpeedLimit": {"shape": "RouteSpanSpeedLimitDetails", "documentation": "<p>Speed limit details corresponding to the span.</p> <p> <b>Unit</b>: <code>KilometersPerHour</code> </p>"}, "TypicalDuration": {"shape": "DurationSeconds", "documentation": "<p>Duration of the computed span under typical traffic congestion.</p> <p> <b>Unit</b>: <code>seconds</code> </p>"}}, "documentation": "<p>Span computed for the requested SpanAdditionalFeatures.</p>"}, "RoutePedestrianSpanFunctionalClassificationInteger": {"type": "integer", "box": true, "max": 5, "min": 1}, "RoutePedestrianSpanGeometryOffsetInteger": {"type": "integer", "box": true, "min": 0}, "RoutePedestrianSpanList": {"type": "list", "member": {"shape": "RoutePedestrianSpan"}}, "RoutePedestrianSpanRegionString": {"type": "string", "max": 3, "min": 0}, "RoutePedestrianSummary": {"type": "structure", "members": {"Overview": {"shape": "RoutePedestrianOverviewSummary", "documentation": "<p>Summarized details for the leg including before travel, travel and after travel steps.</p>"}, "TravelOnly": {"shape": "RoutePedestrianTravelOnlySummary", "documentation": "<p>Summarized details for the leg including travel steps only. The Distance for the travel only portion of the journey is in meters</p>"}}, "documentation": "<p>Summarized details for the leg including before travel, travel and after travel steps.</p>"}, "RoutePedestrianTravelOnlySummary": {"type": "structure", "required": ["Duration"], "members": {"Duration": {"shape": "DurationSeconds", "documentation": "<p>Duration of the step.</p> <p> <b>Unit</b>: <code>seconds</code> </p>"}}, "documentation": "<p>Summarized details for the leg including travel steps.</p>"}, "RoutePedestrianTravelStep": {"type": "structure", "required": ["Duration", "Type"], "members": {"ContinueStepDetails": {"shape": "RouteContinueStepDetails", "documentation": "<p>Details related to the continue step.</p>"}, "CurrentRoad": {"shape": "RouteRoad", "documentation": "<p>Details of the current road. See RouteRoad for details of sub-attributes.</p>"}, "Distance": {"shape": "DistanceMeters", "documentation": "<p>Distance of the step.</p>"}, "Duration": {"shape": "DurationSeconds", "documentation": "<p>Duration of the step.</p> <p> <b>Unit</b>: <code>seconds</code> </p>"}, "ExitNumber": {"shape": "LocalizedStringList", "documentation": "<p>Exit number of the road exit, if applicable.</p>"}, "GeometryOffset": {"shape": "RoutePedestrianTravelStepGeometryOffsetInteger", "documentation": "<p>Offset in the leg geometry corresponding to the start of this step.</p>"}, "Instruction": {"shape": "String", "documentation": "<p>Brief description of the step in the requested language.</p> <note> <p>Only available when the TravelStepType is Default.</p> </note>"}, "KeepStepDetails": {"shape": "RouteKeepStepDetails", "documentation": "<p>Details that are specific to a Keep step.</p>"}, "NextRoad": {"shape": "RouteRoad", "documentation": "<p>Details of the next road. See RouteRoad for details of sub-attributes.</p>"}, "RoundaboutEnterStepDetails": {"shape": "RouteRoundaboutEnterStepDetails", "documentation": "<p>Details that are specific to a Roundabout Enter step.</p>"}, "RoundaboutExitStepDetails": {"shape": "RouteRoundaboutExitStepDetails", "documentation": "<p>Details that are specific to a Roundabout Exit step.</p>"}, "RoundaboutPassStepDetails": {"shape": "RouteRoundaboutPassStepDetails", "documentation": "<p>Details that are specific to a Roundabout Pass step.</p>"}, "Signpost": {"shape": "RouteSignpost", "documentation": "<p>Sign post information of the action, applicable only for TurnByTurn steps. See RouteSignpost for details of sub-attributes.</p>"}, "TurnStepDetails": {"shape": "RouteTurnStepDetails", "documentation": "<p>Details that are specific to a turn step.</p>"}, "Type": {"shape": "RoutePedestrianTravelStepType", "documentation": "<p>Type of the step.</p>"}}, "documentation": "<p>Steps of a leg that must be performed during the travel portion of the leg.</p>"}, "RoutePedestrianTravelStepGeometryOffsetInteger": {"type": "integer", "box": true, "min": 0}, "RoutePedestrianTravelStepList": {"type": "list", "member": {"shape": "RoutePedestrianTravelStep"}}, "RoutePedestrianTravelStepType": {"type": "string", "enum": ["Arrive", "Continue", "<PERSON><PERSON><PERSON>", "Keep", "RoundaboutEnter", "RoundaboutExit", "RoundaboutPass", "Turn", "Exit", "<PERSON><PERSON>", "UTurn"]}, "RouteRampStepDetails": {"type": "structure", "required": ["Intersection"], "members": {"Intersection": {"shape": "LocalizedStringList", "documentation": "<p>Name of the intersection, if applicable to the step.</p>"}, "SteeringDirection": {"shape": "RouteSteeringDirection", "documentation": "<p>Steering direction for the step.</p>"}, "TurnAngle": {"shape": "TurnAngle", "documentation": "<p><PERSON><PERSON> of the turn.</p>"}, "TurnIntensity": {"shape": "RouteTurnIntensity", "documentation": "<p>Intensity of the turn.</p>"}}, "documentation": "<p>Details that are specific to a ramp step.</p>"}, "RouteResponseNotice": {"type": "structure", "required": ["Code"], "members": {"Code": {"shape": "RouteResponseNoticeCode", "documentation": "<p>Code corresponding to the issue.</p>"}, "Impact": {"shape": "RouteNoticeImpact", "documentation": "<p>Impact corresponding to the issue. While Low impact notices can be safely ignored, High impact notices must be evaluated further to determine the impact.</p>"}}, "documentation": "<p>Notices are additional information returned that indicate issues that occurred during route calculation.</p>"}, "RouteResponseNoticeCode": {"type": "string", "enum": ["MainLanguageNotFound", "Other", "TravelTimeExceedsDriverWorkHours"]}, "RouteResponseNoticeList": {"type": "list", "member": {"shape": "RouteResponseNotice"}}, "RouteRoad": {"type": "structure", "required": ["RoadName", "RouteNumber", "Towards"], "members": {"RoadName": {"shape": "LocalizedStringList", "documentation": "<p>Name of the road (localized).</p>"}, "RouteNumber": {"shape": "RouteNumberList", "documentation": "<p>Route number of the road.</p>"}, "Towards": {"shape": "LocalizedStringList", "documentation": "<p>Names of destinations that can be reached when traveling on the road.</p>"}, "Type": {"shape": "RouteRoadType", "documentation": "<p>The type of road.</p>"}}, "documentation": "<p>The road on the route.</p>"}, "RouteRoadType": {"type": "string", "enum": ["Highway", "Rural", "Urban"]}, "RouteRoundaboutEnterStepDetails": {"type": "structure", "required": ["Intersection"], "members": {"Intersection": {"shape": "LocalizedStringList", "documentation": "<p>Name of the intersection, if applicable to the step.</p>"}, "SteeringDirection": {"shape": "RouteSteeringDirection", "documentation": "<p>Steering direction for the step.</p>"}, "TurnAngle": {"shape": "TurnAngle", "documentation": "<p><PERSON><PERSON> of the turn.</p>"}, "TurnIntensity": {"shape": "RouteTurnIntensity", "documentation": "<p>Intensity of the turn.</p>"}}, "documentation": "<p>Details about the roundabout leg.</p>"}, "RouteRoundaboutExitStepDetails": {"type": "structure", "required": ["Intersection"], "members": {"Intersection": {"shape": "LocalizedStringList", "documentation": "<p>Name of the intersection, if applicable to the step.</p>"}, "RelativeExit": {"shape": "RouteRoundaboutExitStepDetailsRelativeExitInteger", "documentation": "<p>Exit to be taken.</p>"}, "RoundaboutAngle": {"shape": "RoundaboutAngle", "documentation": "<p><PERSON><PERSON> of the roundabout.</p>"}, "SteeringDirection": {"shape": "RouteSteeringDirection", "documentation": "<p>Steering direction for the step.</p>"}}, "documentation": "<p>Details about the roundabout step.</p>"}, "RouteRoundaboutExitStepDetailsRelativeExitInteger": {"type": "integer", "box": true, "max": 12, "min": 1}, "RouteRoundaboutPassStepDetails": {"type": "structure", "required": ["Intersection"], "members": {"Intersection": {"shape": "LocalizedStringList", "documentation": "<p>Name of the intersection, if applicable to the step.</p>"}, "SteeringDirection": {"shape": "RouteSteeringDirection", "documentation": "<p>Steering direction for the step.</p>"}, "TurnAngle": {"shape": "TurnAngle", "documentation": "<p><PERSON><PERSON> of the turn.</p>"}, "TurnIntensity": {"shape": "RouteTurnIntensity", "documentation": "<p>Intensity of the turn.</p>"}}, "documentation": "<p>Details about the step.</p>"}, "RouteScooterOptions": {"type": "structure", "members": {"EngineType": {"shape": "RouteEngineType", "documentation": "<p>Engine type of the vehicle.</p>"}, "LicensePlate": {"shape": "RouteVehicleLicensePlate", "documentation": "<p>The vehicle License Plate.</p>"}, "MaxSpeed": {"shape": "RouteScooterOptionsMaxSpeedDouble", "documentation": "<p>Maximum speed</p> <p> <b>Unit</b>: <code>KilometersPerHour</code> </p>", "box": true}, "Occupancy": {"shape": "RouteScooterOptionsOccupancyInteger", "documentation": "<p>The number of occupants in the vehicle.</p> <p>Default Value: <code>1</code> </p>"}}, "documentation": "<p>Travel mode options when the provided travel mode is <code>Scooter</code> </p>"}, "RouteScooterOptionsMaxSpeedDouble": {"type": "double", "max": 252.0, "min": 3.6}, "RouteScooterOptionsOccupancyInteger": {"type": "integer", "box": true, "min": 1}, "RouteSideOfStreet": {"type": "string", "enum": ["Left", "Right"]}, "RouteSideOfStreetOptions": {"type": "structure", "required": ["Position"], "members": {"Position": {"shape": "Position", "documentation": "<p>Position defined as <code>[longitude, latitude]</code>.</p>"}, "UseWith": {"shape": "SideOfStreetMatchingStrategy", "documentation": "<p>Strategy that defines when the side of street position should be used.</p> <p>Default Value: <code>DividedStreetOnly</code> </p>"}}, "documentation": "<p>Options to configure matching the provided position to a side of the street.</p>"}, "RouteSignpost": {"type": "structure", "required": ["Labels"], "members": {"Labels": {"shape": "RouteSignpostLabelList", "documentation": "<p>Labels present on the sign post.</p>"}}, "documentation": "<p>Sign post information of the action, applicable only for TurnByTurn steps. See RouteSignpost for details of sub-attributes.</p>"}, "RouteSignpostLabel": {"type": "structure", "members": {"RouteNumber": {"shape": "RouteNumber", "documentation": "<p>Route number of the road.</p>"}, "Text": {"shape": "LocalizedString", "documentation": "<p>The Signpost text.</p>"}}, "documentation": "<p>Labels presented on the sign post.</p>"}, "RouteSignpostLabelList": {"type": "list", "member": {"shape": "RouteSignpostLabel"}}, "RouteSpanAdditionalFeature": {"type": "string", "enum": ["BestCaseDuration", "CarAccess", "Country", "Distance", "Duration", "DynamicSpeed", "FunctionalClassification", "Gates", "Incidents", "Names", "Notices", "PedestrianAccess", "RailwayCrossings", "Region", "RoadAttributes", "RouteNumbers", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SpeedLimit", "TollSystems", "TruckAccess", "TruckRoadTypes", "TypicalDuration", "Zones", "Consumption"]}, "RouteSpanAdditionalFeatureList": {"type": "list", "member": {"shape": "RouteSpanAdditionalFeature"}, "max": 24, "min": 0}, "RouteSpanCarAccessAttribute": {"type": "string", "enum": ["Allowed", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TollRoad"]}, "RouteSpanCarAccessAttributeList": {"type": "list", "member": {"shape": "RouteSpanCarAccessAttribute"}, "max": 3, "min": 0}, "RouteSpanDynamicSpeedDetails": {"type": "structure", "members": {"BestCaseSpeed": {"shape": "SpeedKilometersPerHour", "documentation": "<p>Estimated speed while traversing the span without traffic congestion.</p> <p> <b>Unit</b>: <code>KilometersPerHour</code> </p>"}, "TurnDuration": {"shape": "DurationSeconds", "documentation": "<p>Estimated time to turn from this span into the next. </p> <p> <b>Unit</b>: <code>seconds</code> </p>"}, "TypicalSpeed": {"shape": "SpeedKilometersPerHour", "documentation": "<p>Estimated speed while traversing the span under typical traffic congestion.</p> <p> <b>Unit</b>: <code>KilometersPerHour</code> </p>"}}, "documentation": "<p>Details about the dynamic speed.</p> <p> <b>Unit</b>: <code>KilometersPerHour</code> </p>"}, "RouteSpanGateAttribute": {"type": "string", "enum": ["Emergency", "KeyAccess", "PermissionRequired"]}, "RouteSpanPedestrianAccessAttribute": {"type": "string", "enum": ["Allowed", "Indoors", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Park", "Stairs", "TollRoad"]}, "RouteSpanPedestrianAccessAttributeList": {"type": "list", "member": {"shape": "RouteSpanPedestrianAccessAttribute"}, "max": 6, "min": 0}, "RouteSpanRailwayCrossingAttribute": {"type": "string", "enum": ["Protected", "Unprotected"]}, "RouteSpanRoadAttribute": {"type": "string", "enum": ["Bridge", "BuiltUpArea", "ControlledAccessHighway", "DirtRoad", "DividedRoad", "Motorway", "PrivateRoad", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Roundabout", "Tunnel", "UnderConstruction"]}, "RouteSpanRoadAttributeList": {"type": "list", "member": {"shape": "RouteSpanRoadAttribute"}, "max": 12, "min": 0}, "RouteSpanScooterAccessAttribute": {"type": "string", "enum": ["Allowed", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TollRoad"]}, "RouteSpanScooterAccessAttributeList": {"type": "list", "member": {"shape": "RouteSpanScooterAccessAttribute"}, "max": 3, "min": 0}, "RouteSpanSpeedLimitDetails": {"type": "structure", "members": {"MaxSpeed": {"shape": "SpeedKilometersPerHour", "documentation": "<p>Maximum speed.</p> <p> <b>Unit</b>: <code>KilometersPerHour</code> </p>"}, "Unlimited": {"shape": "Boolean", "documentation": "<p>If the span doesn't have a speed limit like the Autobahn.</p>"}}, "documentation": "<p>Details about the speed limit corresponding to the span.</p> <p> <b>Unit</b>: <code>KilometersPerHour</code> </p>"}, "RouteSpanTruckAccessAttribute": {"type": "string", "enum": ["Allowed", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TollRoad"]}, "RouteSpanTruckAccessAttributeList": {"type": "list", "member": {"shape": "RouteSpanTruckAccessAttribute"}, "max": 3, "min": 0}, "RouteSteeringDirection": {"type": "string", "enum": ["Left", "Right", "Straight"]}, "RouteSummary": {"type": "structure", "members": {"Distance": {"shape": "DistanceMeters", "documentation": "<p>Distance of the route.</p>"}, "Duration": {"shape": "DurationSeconds", "documentation": "<p>Duration of the route.</p> <p> <b>Unit</b>: <code>seconds</code> </p>"}, "Tolls": {"shape": "RouteTollSummary", "documentation": "<p>Toll summary for the complete route.</p>"}}, "documentation": "<p>Summarized details for the leg including travel steps only. The Distance for the travel only portion of the journey is the same as the Distance within the Overview summary.</p>"}, "RouteToll": {"type": "structure", "required": ["PaymentSites", "Rates", "Systems"], "members": {"Country": {"shape": "CountryCode3", "documentation": "<p>The alpha-2 or alpha-3 character code for the country.</p>"}, "PaymentSites": {"shape": "RouteTollPaymentSiteList", "documentation": "<p>Locations or sites where the toll fare is collected.</p>"}, "Rates": {"shape": "RouteTollRateList", "documentation": "<p>Toll rates that need to be paid to travel this leg of the route.</p>"}, "Systems": {"shape": "IndexList", "documentation": "<p>Toll systems are authorities that collect payments for the toll.</p>"}}, "documentation": "<p>Provides details about toll information along a route, including the payment sites, applicable toll rates, toll systems, and the country associated with the toll collection.</p>"}, "RouteTollList": {"type": "list", "member": {"shape": "RouteToll"}}, "RouteTollOptions": {"type": "structure", "members": {"AllTransponders": {"shape": "Boolean", "documentation": "<p>Specifies if the user has valid transponder with access to all toll systems. This impacts toll calculation, and if true the price with transponders is used.</p>"}, "AllVignettes": {"shape": "Boolean", "documentation": "<p>Specifies if the user has valid vignettes with access for all toll roads. If a user has a vignette for a toll road, then toll cost for that road is omitted since no further payment is necessary.</p>"}, "Currency": {"shape": "CurrencyCode", "documentation": "<p>Currency code corresponding to the price. This is the same as Currency specified in the request.</p>"}, "EmissionType": {"shape": "RouteEmissionType", "documentation": "<p>Emission type of the vehicle for toll cost calculation.</p> <p> <b>Valid values</b>: <code>Euro1, Euro2, Euro3, Euro4, Euro5, Euro6, EuroEev</code> </p>"}, "VehicleCategory": {"shape": "RouteTollVehicleCategory", "documentation": "<p>Vehicle category for toll cost calculation.</p>"}}, "documentation": "<p>Options related to Tolls on a route.</p>"}, "RouteTollPass": {"type": "structure", "members": {"IncludesReturnTrip": {"shape": "Boolean", "documentation": "<p>If the pass includes the rate for the return leg of the trip.</p>"}, "SeniorPass": {"shape": "Boolean", "documentation": "<p>If the pass is only valid for senior persons.</p>"}, "TransferCount": {"shape": "RouteTollPassTransferCountInteger", "documentation": "<p>If the toll pass can be transferred, and how many times.</p>"}, "TripCount": {"shape": "RouteTollPassTripCountInteger", "documentation": "<p>Number of trips the pass is valid for.</p>"}, "ValidityPeriod": {"shape": "RouteTollPassValidityPeriod", "documentation": "<p>Period for which the pass is valid.</p>"}}, "documentation": "<p>Details if the toll rate can be a pass that supports multiple trips.</p>"}, "RouteTollPassTransferCountInteger": {"type": "integer", "box": true, "min": 0}, "RouteTollPassTripCountInteger": {"type": "integer", "box": true, "min": 0}, "RouteTollPassValidityPeriod": {"type": "structure", "required": ["Period"], "members": {"Period": {"shape": "RouteTollPassValidityPeriodType", "documentation": "<p>Validity period.</p>"}, "PeriodCount": {"shape": "RouteTollPassValidityPeriodPeriodCountInteger", "documentation": "<p>Counts for the validity period.</p>"}}, "documentation": "<p>Period for which the pass is valid.</p>"}, "RouteTollPassValidityPeriodPeriodCountInteger": {"type": "integer", "box": true, "min": 0}, "RouteTollPassValidityPeriodType": {"type": "string", "enum": ["Annual", "Days", "ExtendedAnnual", "Minutes", "Months"]}, "RouteTollPaymentMethod": {"type": "string", "enum": ["BankCard", "Cash", "CashExact", "CreditCard", "PassSubscription", "TravelCard", "Transponder", "VideoToll"]}, "RouteTollPaymentMethodList": {"type": "list", "member": {"shape": "RouteTollPaymentMethod"}, "max": 8, "min": 0}, "RouteTollPaymentSite": {"type": "structure", "required": ["Position"], "members": {"Name": {"shape": "String", "documentation": "<p>Name of the payment site.</p>"}, "Position": {"shape": "Position23", "documentation": "<p>Position defined as <code>[longitude, latitude]</code>.</p>"}}, "documentation": "<p>Locations or sites where the toll fare is collected.</p>"}, "RouteTollPaymentSiteList": {"type": "list", "member": {"shape": "RouteTollPaymentSite"}}, "RouteTollPrice": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON>", "Estimate", "Range", "Value"], "members": {"Currency": {"shape": "CurrencyCode", "documentation": "<p>Currency code corresponding to the price. This is the same as Currency specified in the request.</p>"}, "Estimate": {"shape": "Boolean", "documentation": "<p>If the price is an estimate or an exact value. </p>"}, "PerDuration": {"shape": "DurationSeconds", "documentation": "<p>Duration for which the price corresponds to.</p> <p> <b>Unit</b>: <code>seconds</code> </p>"}, "Range": {"shape": "Boolean", "documentation": "<p>If the price is a range or an exact value. If any of the toll fares making up the route is a range, the overall price is also a range.</p>"}, "RangeValue": {"shape": "RouteTollPriceValueRange", "documentation": "<p>Price range with a minimum and maximum value, if a range.</p>"}, "Value": {"shape": "RouteTollPriceValueDouble", "documentation": "<p>Exact price, if not a range.</p>"}}, "documentation": "<p>The toll price.</p>"}, "RouteTollPriceSummary": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON>", "Estimate", "Range", "Value"], "members": {"Currency": {"shape": "CurrencyCode", "documentation": "<p>Currency code corresponding to the price. This is the same as Currency specified in the request.</p>"}, "Estimate": {"shape": "Boolean", "documentation": "<p>If the price is an estimate or an exact value. </p>"}, "Range": {"shape": "Boolean", "documentation": "<p>If the price is a range or an exact value. If any of the toll fares making up the route is a range, the overall price is also a range.</p>"}, "RangeValue": {"shape": "RouteTollPriceValueRange", "documentation": "<p>Price range with a minimum and maximum value, if a range.</p>"}, "Value": {"shape": "RouteTollPriceSummaryValueDouble", "documentation": "<p>Exact price, if not a range.</p>"}}, "documentation": "<p>Summary of the route and toll price.</p>"}, "RouteTollPriceSummaryValueDouble": {"type": "double", "box": true, "min": 0.0}, "RouteTollPriceValueDouble": {"type": "double", "box": true, "min": 0.0}, "RouteTollPriceValueRange": {"type": "structure", "required": ["Min", "Max"], "members": {"Min": {"shape": "RouteTollPriceValueRangeMinDouble", "documentation": "<p>Minimum price.</p>"}, "Max": {"shape": "RouteTollPriceValueRangeMaxDouble", "documentation": "<p>Maximum price.</p>"}}, "documentation": "<p>Price range with a minimum and maximum value, if a range.</p>"}, "RouteTollPriceValueRangeMaxDouble": {"type": "double", "box": true, "min": 0.0}, "RouteTollPriceValueRangeMinDouble": {"type": "double", "box": true, "min": 0.0}, "RouteTollRate": {"type": "structure", "required": ["Id", "LocalPrice", "Name", "PaymentMethods", "Transponders"], "members": {"ApplicableTimes": {"shape": "String", "documentation": "<p>Time when the rate is valid.</p>"}, "ConvertedPrice": {"shape": "RouteTollPrice", "documentation": "<p>Price in the converted currency as specified in the request.</p>"}, "Id": {"shape": "String", "documentation": "<p>The Toll rate Id.</p>"}, "LocalPrice": {"shape": "RouteTollPrice", "documentation": "<p>Price in the local regional currency.</p>"}, "Name": {"shape": "String", "documentation": "<p>The name of the toll.</p>"}, "Pass": {"shape": "RouteTollPass", "documentation": "<p>Details if the toll rate can be a pass that supports multiple trips.</p>"}, "PaymentMethods": {"shape": "RouteTollPaymentMethodList", "documentation": "<p>Accepted payment methods at the toll.</p>"}, "Transponders": {"shape": "RouteTransponderList", "documentation": "<p>Transponders for which this toll can be applied.</p>"}}, "documentation": "<p>The toll rate.</p>"}, "RouteTollRateList": {"type": "list", "member": {"shape": "RouteTollRate"}}, "RouteTollSummary": {"type": "structure", "members": {"Total": {"shape": "RouteTollPriceSummary", "documentation": "<p>Total toll summary for the complete route. Total is the only summary available today.</p>"}}, "documentation": "<p>The toll summary for the complete route.</p>"}, "RouteTollSystem": {"type": "structure", "members": {"Name": {"shape": "String", "documentation": "<p>The toll system name.</p>"}}, "documentation": "<p>Toll systems are authorities that collect payments for the toll.</p>"}, "RouteTollSystemList": {"type": "list", "member": {"shape": "RouteTollSystem"}}, "RouteTollVehicleCategory": {"type": "string", "enum": ["Minibus"]}, "RouteTrafficOptions": {"type": "structure", "members": {"FlowEventThresholdOverride": {"shape": "DurationSeconds", "documentation": "<p>Duration for which flow traffic is considered valid. For this period, the flow traffic is used over historical traffic data. Flow traffic refers to congestion, which changes very quickly. Duration in seconds for which flow traffic event would be considered valid. While flow traffic event is valid it will be used over the historical traffic data. </p>"}, "Usage": {"shape": "TrafficUsage", "documentation": "<p>Determines if traffic should be used or ignored while calculating the route.</p> <p>Default Value: <code>UseTrafficData</code> </p>"}}, "documentation": "<p>Traffic options for the route.</p>"}, "RouteTrailerOptions": {"type": "structure", "members": {"AxleCount": {"shape": "RouteTrailerOptionsAxleCountInteger", "documentation": "<p>Total number of axles of the vehicle.</p>"}, "TrailerCount": {"shape": "RouteTrailerOptionsTrailerCountInteger", "documentation": "<p>Number of trailers attached to the vehicle.</p> <p>Default Value: <code>0</code> </p>"}}, "documentation": "<p>Trailer options corresponding to the vehicle.</p>"}, "RouteTrailerOptionsAxleCountInteger": {"type": "integer", "box": true, "min": 1}, "RouteTrailerOptionsTrailerCountInteger": {"type": "integer", "box": true, "max": 255, "min": 1}, "RouteTransponder": {"type": "structure", "members": {"SystemName": {"shape": "String", "documentation": "<p>Names of the toll system collecting the toll.</p>"}}, "documentation": "<p>Transponders for which this toll can be applied.</p>"}, "RouteTransponderList": {"type": "list", "member": {"shape": "RouteTransponder"}}, "RouteTravelMode": {"type": "string", "enum": ["Car", "Pedestrian", "<PERSON>ooter", "Truck"]}, "RouteTravelModeOptions": {"type": "structure", "members": {"Car": {"shape": "RouteCarOptions", "documentation": "<p>Travel mode options when the provided travel mode is \"Car\"</p>"}, "Pedestrian": {"shape": "RoutePedestrianOptions", "documentation": "<p>Travel mode options when the provided travel mode is \"Pedestrian\"</p>"}, "Scooter": {"shape": "RouteScooterOptions", "documentation": "<p>Travel mode options when the provided travel mode is <code>Scooter</code> </p> <note> <p>When travel mode is set to <code>Scooter</code>, then the avoidance option <code>ControlledAccessHighways</code> defaults to <code>true</code>.</p> </note>"}, "Truck": {"shape": "RouteTruckOptions", "documentation": "<p>Travel mode options when the provided travel mode is \"Truck\"</p>"}}, "documentation": "<p>Travel mode related options for the provided travel mode.</p>"}, "RouteTravelStepType": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "TurnByTurn"]}, "RouteTruckOptions": {"type": "structure", "members": {"AxleCount": {"shape": "RouteTruckOptionsAxleCountInteger", "documentation": "<p>Total number of axles of the vehicle.</p>"}, "EngineType": {"shape": "RouteEngineType", "documentation": "<p>Engine type of the vehicle.</p>"}, "GrossWeight": {"shape": "WeightKilograms", "documentation": "<p>Gross weight of the vehicle including trailers, and goods at capacity.</p> <p> <b>Unit</b>: <code>Kilograms</code> </p>"}, "HazardousCargos": {"shape": "RouteHazardousCargoTypeList", "documentation": "<p>List of Hazardous cargo contained in the vehicle.</p>"}, "Height": {"shape": "RouteTruckOptionsHeightLong", "documentation": "<p>Height of the vehicle.</p> <p> <b>Unit</b>: <code>centimeters</code> </p>"}, "HeightAboveFirstAxle": {"shape": "RouteTruckOptionsHeightAboveFirstAxleLong", "documentation": "<p>Height of the vehicle above its first axle.</p> <p> <b>Unit</b>: <code>centimeters</code> </p>"}, "KpraLength": {"shape": "DimensionCentimeters", "documentation": "<p>Kingpin to rear axle length of the vehicle.</p> <p> <b>Unit</b>: <code>centimeters</code> </p>"}, "Length": {"shape": "RouteTruckOptionsLengthLong", "documentation": "<p>Length of the vehicle.</p> <p> <b>Unit</b>: <code>c</code> </p>"}, "LicensePlate": {"shape": "RouteVehicleLicensePlate", "documentation": "<p>The vehicle License Plate.</p>"}, "MaxSpeed": {"shape": "RouteTruckOptionsMaxSpeedDouble", "documentation": "<p>Maximum speed</p> <p> <b>Unit</b>: <code>KilometersPerHour</code> </p>", "box": true}, "Occupancy": {"shape": "RouteTruckOptionsOccupancyInteger", "documentation": "<p>The number of occupants in the vehicle.</p> <p>Default Value: <code>1</code> </p>"}, "PayloadCapacity": {"shape": "WeightKilograms", "documentation": "<p>Payload capacity of the vehicle and trailers attached.</p> <p> <b>Unit</b>: <code>kilograms</code> </p>"}, "TireCount": {"shape": "RouteTruckOptionsTireCountInteger", "documentation": "<p>Number of tires on the vehicle.</p>"}, "Trailer": {"shape": "RouteTrailerOptions", "documentation": "<p>Trailer options corresponding to the vehicle.</p>"}, "TruckType": {"shape": "RouteTruckType", "documentation": "<p>Type of the truck.</p>"}, "TunnelRestrictionCode": {"shape": "RouteTruckOptionsTunnelRestrictionCodeString", "documentation": "<p>The tunnel restriction code.</p> <p>Tunnel categories in this list indicate the restrictions which apply to certain tunnels in Great Britain. They relate to the types of dangerous goods that can be transported through them.</p> <ul> <li> <p> <i>Tunnel Category B</i> </p> <ul> <li> <p> <i>Risk Level</i>: Limited risk</p> </li> <li> <p> <i>Restrictions</i>: Few restrictions</p> </li> </ul> </li> <li> <p> <i>Tunnel Category C</i> </p> <ul> <li> <p> <i>Risk Level</i>: Medium risk</p> </li> <li> <p> <i>Restrictions</i>: Some restrictions</p> </li> </ul> </li> <li> <p> <i>Tunnel Category D</i> </p> <ul> <li> <p> <i>Risk Level</i>: High risk</p> </li> <li> <p> <i>Restrictions</i>: Many restrictions occur</p> </li> </ul> </li> <li> <p> <i>Tunnel Category E</i> </p> <ul> <li> <p> <i>Risk Level</i>: Very high risk</p> </li> <li> <p> <i>Restrictions</i>: Restricted tunnel</p> </li> </ul> </li> </ul>"}, "WeightPerAxle": {"shape": "WeightKilograms", "documentation": "<p>Heaviest weight per axle irrespective of the axle type or the axle group. Meant for usage in countries where the differences in axle types or axle groups are not distinguished.</p> <p> <b>Unit</b>: <code>Kilograms</code> </p>"}, "WeightPerAxleGroup": {"shape": "WeightPerAxleGroup", "documentation": "<p>Specifies the total weight for the specified axle group. Meant for usage in countries that have different regulations based on the axle group type.</p> <p> <b>Unit</b>: <code>Kilograms</code> </p>"}, "Width": {"shape": "RouteTruckOptionsWidthLong", "documentation": "<p>Width of the vehicle.</p> <p> <b>Unit</b>: <code>centimeters</code> </p>"}}, "documentation": "<p>Travel mode options when the provided travel mode is \"Truck\"</p>"}, "RouteTruckOptionsAxleCountInteger": {"type": "integer", "box": true, "max": 255, "min": 2}, "RouteTruckOptionsHeightAboveFirstAxleLong": {"type": "long", "max": 5000, "min": 0}, "RouteTruckOptionsHeightLong": {"type": "long", "max": 5000, "min": 0}, "RouteTruckOptionsLengthLong": {"type": "long", "max": 30000, "min": 0}, "RouteTruckOptionsMaxSpeedDouble": {"type": "double", "max": 252.0, "min": 3.6}, "RouteTruckOptionsOccupancyInteger": {"type": "integer", "box": true, "min": 1}, "RouteTruckOptionsTireCountInteger": {"type": "integer", "box": true, "max": 255, "min": 1}, "RouteTruckOptionsTunnelRestrictionCodeString": {"type": "string", "max": 20, "min": 0}, "RouteTruckOptionsWidthLong": {"type": "long", "max": 5000, "min": 0}, "RouteTruckType": {"type": "string", "enum": ["LightTruck", "StraightTruck", "Tractor"]}, "RouteTurnIntensity": {"type": "string", "enum": ["<PERSON>", "Slight", "Typical"]}, "RouteTurnStepDetails": {"type": "structure", "required": ["Intersection"], "members": {"Intersection": {"shape": "LocalizedStringList", "documentation": "<p>Name of the intersection, if applicable to the step.</p>"}, "SteeringDirection": {"shape": "RouteSteeringDirection", "documentation": "<p>Steering direction for the step.</p>"}, "TurnAngle": {"shape": "TurnAngle", "documentation": "<p><PERSON><PERSON> of the turn.</p>"}, "TurnIntensity": {"shape": "RouteTurnIntensity", "documentation": "<p>Intensity of the turn.</p>"}}, "documentation": "<p>Details related to the turn step.</p>"}, "RouteUTurnStepDetails": {"type": "structure", "required": ["Intersection"], "members": {"Intersection": {"shape": "LocalizedStringList", "documentation": "<p>Name of the intersection, if applicable to the step.</p>"}, "SteeringDirection": {"shape": "RouteSteeringDirection", "documentation": "<p>Steering direction for the step.</p>"}, "TurnAngle": {"shape": "TurnAngle", "documentation": "<p><PERSON><PERSON> of the turn.</p>"}, "TurnIntensity": {"shape": "RouteTurnIntensity", "documentation": "<p>Intensity of the turn.</p>"}}, "documentation": "<p>Details related to the U-turn step.</p>"}, "RouteVehicleArrival": {"type": "structure", "required": ["Place"], "members": {"Place": {"shape": "RouteVehiclePlace", "documentation": "<p>The place details.</p>"}, "Time": {"shape": "TimestampWithTimezoneOffset", "documentation": "<p>The time.</p>"}}, "documentation": "<p>Details corresponding to the arrival for a leg.</p>"}, "RouteVehicleDeparture": {"type": "structure", "required": ["Place"], "members": {"Place": {"shape": "RouteVehiclePlace", "documentation": "<p>The place details.</p>"}, "Time": {"shape": "TimestampWithTimezoneOffset", "documentation": "<p>The departure time.</p>"}}, "documentation": "<p>Details corresponding to the departure for the leg.</p>"}, "RouteVehicleIncident": {"type": "structure", "members": {"Description": {"shape": "String", "documentation": "<p>Brief readable description of the incident.</p>"}, "EndTime": {"shape": "TimestampWithTimezoneOffset", "documentation": "<p>End timestamp of the incident.</p>"}, "Severity": {"shape": "RouteVehicleIncidentSeverity", "documentation": "<p>Severity of the incident Critical - The part of the route the incident affects is unusable. Major- Major impact on the leg duration, for example stop and go Minor- Minor impact on the leg duration, for example traffic jam Low - Low on duration, for example slightly increased traffic</p>"}, "StartTime": {"shape": "TimestampWithTimezoneOffset", "documentation": "<p>Start time of the incident.</p>"}, "Type": {"shape": "RouteVehicleIncidentType", "documentation": "<p>Type of the incident.</p>"}}, "documentation": "<p>Incidents corresponding to this leg of the route.</p>"}, "RouteVehicleIncidentList": {"type": "list", "member": {"shape": "RouteVehicleIncident"}}, "RouteVehicleIncidentSeverity": {"type": "string", "enum": ["Critical", "High", "Medium", "Low"]}, "RouteVehicleIncidentType": {"type": "string", "enum": ["Accident", "Congestion", "Construction", "DisabledVehicle", "LaneRestriction", "MassTransit", "Other", "PlannedEvent", "RoadClosure", "RoadHazard", "Weather"]}, "RouteVehicleLegDetails": {"type": "structure", "required": ["Arrival", "Departure", "Incidents", "Notices", "PassThroughWaypoints", "Spans", "Tolls", "TollSystems", "TravelSteps", "TruckRoadTypes", "Zones"], "members": {"Arrival": {"shape": "RouteVehicleArrival", "documentation": "<p>Details corresponding to the arrival for the leg.</p>"}, "Departure": {"shape": "RouteVehicleDeparture", "documentation": "<p>Details corresponding to the departure for the leg.</p>"}, "Incidents": {"shape": "RouteVehicleIncidentList", "documentation": "<p>Incidents corresponding to this leg of the route.</p>"}, "Notices": {"shape": "RouteVehicleNoticeList", "documentation": "<p>Notices are additional information returned that indicate issues that occurred during route calculation.</p>"}, "PassThroughWaypoints": {"shape": "RoutePassThroughWaypointList", "documentation": "<p>Waypoints that were passed through during the leg. This includes the waypoints that were configured with the PassThrough option.</p>"}, "Spans": {"shape": "RouteVehicleSpanList", "documentation": "<p>Spans that were computed for the requested SpanAdditionalFeatures.</p>"}, "Summary": {"shape": "RouteVehicleSummary", "documentation": "<p>Summarized details of the leg.</p>"}, "Tolls": {"shape": "RouteTollList", "documentation": "<p>Toll related options.</p>"}, "TollSystems": {"shape": "RouteTollSystemList", "documentation": "<p>Toll systems are authorities that collect payments for the toll.</p>"}, "TravelSteps": {"shape": "RouteVehicleTravelStepList", "documentation": "<p>Steps of a leg that must be performed before the travel portion of the leg.</p>"}, "TruckRoadTypes": {"shape": "TruckRoadTypeList", "documentation": "<p>Truck road type identifiers. <code>BK1</code> through <code>BK4</code> apply only to Sweden. <code>A2,A4,B2,B4,C,D,ET2,ET4</code> apply only to Mexico.</p> <note> <p>There are currently no other supported values as of 26th April 2024.</p> </note>"}, "Zones": {"shape": "RouteZoneList", "documentation": "<p>Zones corresponding to this leg of the route.</p>"}}, "documentation": "<p>Steps of a leg that correspond to the travel portion of the leg.</p>"}, "RouteVehicleLicensePlate": {"type": "structure", "members": {"LastCharacter": {"shape": "RouteVehicleLicensePlateLastCharacterString", "documentation": "<p>The last character of the License Plate.</p>"}}, "documentation": "<p>License plate information of the vehicle. Currently, only the last character is used where license plate based controlled access is enforced.</p>"}, "RouteVehicleLicensePlateLastCharacterString": {"type": "string", "max": 1, "min": 1}, "RouteVehicleNotice": {"type": "structure", "required": ["Code", "Details"], "members": {"Code": {"shape": "RouteVehicleNoticeCode", "documentation": "<p>Code corresponding to the issue.</p>"}, "Details": {"shape": "RouteVehicleNoticeDetailList", "documentation": "<p>Additional details of the notice.</p>"}, "Impact": {"shape": "RouteNoticeImpact", "documentation": "<p>Impact corresponding to the issue. While Low impact notices can be safely ignored, High impact notices must be evaluated further to determine the impact.</p>"}}, "documentation": "<p>Notices are additional information returned that indicate issues that occurred during route calculation.</p>"}, "RouteVehicleNoticeCode": {"type": "string", "enum": ["AccuratePolylineUnavailable", "Other", "PotentialViolatedAvoidTollRoadUsage", "PotentialViolatedCarpoolUsage", "PotentialViolatedTurnRestrictionUsage", "PotentialViolatedVehicleRestrictionUsage", "PotentialViolatedZoneRestrictionUsage", "SeasonalClosure", "TollsDataTemporarilyUnavailable", "TollsDataUnavailable", "Toll<PERSON>ran<PERSON>ond<PERSON>", "ViolatedAvoidControlledAccessHighway", "ViolatedAvoidDifficultTurns", "ViolatedAvoidDirtRoad", "ViolatedAvoidSeasonalClosure", "ViolatedAvoidTollRoad", "ViolatedAvoidTollTransponder", "ViolatedAvoidTruckRoadType", "ViolatedAvoidTunnel", "ViolatedAvoidUTurns", "ViolatedBlockedRoad", "ViolatedCarpool", "ViolatedEmergencyGate", "ViolatedStartDirection", "ViolatedTurnRestriction", "ViolatedVehicleRestriction", "ViolatedZoneRestriction"]}, "RouteVehicleNoticeDetail": {"type": "structure", "members": {"Title": {"shape": "String", "documentation": "<p>The notice title.</p>"}, "ViolatedConstraints": {"shape": "RouteViolatedConstraints", "documentation": "<p>Any violated constraints.</p>"}}, "documentation": "<p>Additional details of the notice.</p>"}, "RouteVehicleNoticeDetailList": {"type": "list", "member": {"shape": "RouteVehicleNoticeDetail"}}, "RouteVehicleNoticeList": {"type": "list", "member": {"shape": "RouteVehicleNotice"}}, "RouteVehicleOverviewSummary": {"type": "structure", "required": ["Distance", "Duration"], "members": {"BestCaseDuration": {"shape": "DurationSeconds", "documentation": "<p>Total duration in free flowing traffic, which is the best case or shortest duration possible to cover the leg.</p> <p> <b>Unit</b>: <code>seconds</code> </p>"}, "Distance": {"shape": "DistanceMeters", "documentation": "<p>Distance of the step.</p>"}, "Duration": {"shape": "DurationSeconds", "documentation": "<p>Duration of the step.</p> <p> <b>Unit</b>: <code>seconds</code> </p>"}, "TypicalDuration": {"shape": "DurationSeconds", "documentation": "<p>Duration of the computed span under typical traffic congestion. </p> <p> <b>Unit</b>: <code>seconds</code> </p>"}}, "documentation": "<p>Summarized details of the leg.</p>"}, "RouteVehiclePlace": {"type": "structure", "required": ["Position"], "members": {"Name": {"shape": "String", "documentation": "<p>The name of the place.</p>"}, "OriginalPosition": {"shape": "Position23", "documentation": "<p>Position provided in the request.</p>"}, "Position": {"shape": "Position23", "documentation": "<p>Position defined as <code>[longitude, latitude]</code>.</p>"}, "SideOfStreet": {"shape": "RouteSideOfStreet", "documentation": "<p>Options to configure matching the provided position to a side of the street.</p>"}, "WaypointIndex": {"shape": "RouteVehiclePlaceWaypointIndexInteger", "documentation": "<p>Index of the waypoint in the request.</p>"}}, "documentation": "<p>Place details corresponding to the arrival or departure.</p>"}, "RouteVehiclePlaceWaypointIndexInteger": {"type": "integer", "box": true, "min": 0}, "RouteVehicleSpan": {"type": "structure", "members": {"BestCaseDuration": {"shape": "DurationSeconds", "documentation": "<p>Duration of the computed span without traffic congestion.</p> <p> <b>Unit</b>: <code>seconds</code> </p>"}, "CarAccess": {"shape": "RouteSpanCarAccessAttributeList", "documentation": "<p>Access attributes for a car corresponding to the span.</p>"}, "Country": {"shape": "CountryCode3", "documentation": "<p>3 letter Country code corresponding to the Span.</p>"}, "Distance": {"shape": "DistanceMeters", "documentation": "<p>Distance of the computed span. This feature doesn't split a span, but is always computed on a span split by other properties.</p>"}, "Duration": {"shape": "DurationSeconds", "documentation": "<p>Duration of the computed span. This feature doesn't split a span, but is always computed on a span split by other properties.</p> <p> <b>Unit</b>: <code>seconds</code> </p>"}, "DynamicSpeed": {"shape": "RouteSpanDynamicSpeedDetails", "documentation": "<p>Dynamic speed details corresponding to the span.</p> <p> <b>Unit</b>: <code>KilometersPerHour</code> </p>"}, "FunctionalClassification": {"shape": "RouteVehicleSpanFunctionalClassificationInteger", "documentation": "<p>Functional classification of the road segment corresponding to the span.</p>"}, "Gate": {"shape": "RouteSpanGateAttribute", "documentation": "<p>Attributes corresponding to a gate. The gate is present at the end of the returned span.</p>"}, "GeometryOffset": {"shape": "RouteVehicleSpanGeometryOffsetInteger", "documentation": "<p>Offset in the leg geometry corresponding to the start of this span.</p>"}, "Incidents": {"shape": "IndexList", "documentation": "<p>Incidents corresponding to the span. These index into the Incidents in the parent Leg.</p>"}, "Names": {"shape": "LocalizedStringList", "documentation": "<p>Provides an array of names of the vehicle span in available languages.</p>"}, "Notices": {"shape": "IndexList", "documentation": "<p>Notices are additional information returned that indicate issues that occurred during route calculation.</p>"}, "RailwayCrossing": {"shape": "RouteSpanRailwayCrossingAttribute", "documentation": "<p>Attributes corresponding to a railway crossing. The gate is present at the end of the returned span.</p>"}, "Region": {"shape": "RouteVehicleSpanRegionString", "documentation": "<p>2-3 letter Region code corresponding to the Span. This is either a province or a state.</p>"}, "RoadAttributes": {"shape": "RouteSpanRoadAttributeList", "documentation": "<p>Attributes for the road segment corresponding to the span. </p>"}, "RouteNumbers": {"shape": "RouteNumberList", "documentation": "<p>Designated route name or number corresponding to the span.</p>"}, "ScooterAccess": {"shape": "RouteSpanScooterAccessAttributeList", "documentation": "<p>Access attributes for a scooter corresponding to the span.</p>"}, "SpeedLimit": {"shape": "RouteSpanSpeedLimitDetails", "documentation": "<p>Speed limit details corresponding to the span.</p> <p> <b>Unit</b>: <code>KilometersPerHour</code> </p>"}, "TollSystems": {"shape": "IndexList", "documentation": "<p>Toll systems are authorities that collect payments for the toll.</p>"}, "TruckAccess": {"shape": "RouteSpanTruckAccessAttributeList", "documentation": "<p>Access attributes for a truck corresponding to the span.</p>"}, "TruckRoadTypes": {"shape": "IndexList", "documentation": "<p>Truck road type identifiers. <code>BK1</code> through <code>BK4</code> apply only to Sweden. <code>A2,A4,B2,B4,C,D,ET2,ET4</code> apply only to Mexico.</p> <note> <p>There are currently no other supported values as of 26th April 2024.</p> </note>"}, "TypicalDuration": {"shape": "DurationSeconds", "documentation": "<p>Duration of the computed span under typical traffic congestion. </p> <p> <b>Unit</b>: <code>seconds</code> </p>"}, "Zones": {"shape": "IndexList", "documentation": "<p>Zones corresponding to this leg of the route.</p>"}}, "documentation": "<p>Span computed for the requested SpanAdditionalFeatures.</p>"}, "RouteVehicleSpanFunctionalClassificationInteger": {"type": "integer", "box": true, "max": 5, "min": 1}, "RouteVehicleSpanGeometryOffsetInteger": {"type": "integer", "box": true, "min": 0}, "RouteVehicleSpanList": {"type": "list", "member": {"shape": "RouteVehicleSpan"}}, "RouteVehicleSpanRegionString": {"type": "string", "max": 3, "min": 0}, "RouteVehicleSummary": {"type": "structure", "members": {"Overview": {"shape": "RouteVehicleOverviewSummary", "documentation": "<p>Summarized details for the leg including before travel, travel and after travel steps.</p>"}, "TravelOnly": {"shape": "RouteVehicleTravelOnlySummary", "documentation": "<p>Summarized details for the leg including travel steps only. The Distance for the travel only portion of the journey is in meters</p>"}}, "documentation": "<p>Summarized details of the route.</p>"}, "RouteVehicleTravelOnlySummary": {"type": "structure", "required": ["Duration"], "members": {"BestCaseDuration": {"shape": "DurationSeconds", "documentation": "<p>Total duration in free flowing traffic, which is the best case or shortest duration possible to cover the leg.</p> <p> <b>Unit</b>: <code>seconds</code> </p>"}, "Duration": {"shape": "DurationSeconds", "documentation": "<p>Duration of the step.</p> <p> <b>Unit</b>: <code>seconds</code> </p>"}, "TypicalDuration": {"shape": "DurationSeconds", "documentation": "<p>Duration of the computed span under typical traffic congestion.</p> <p> <b>Unit</b>: <code>seconds</code> </p>"}}, "documentation": "<p>Summarized details of the route.</p>"}, "RouteVehicleTravelStep": {"type": "structure", "required": ["Duration", "Type"], "members": {"ContinueHighwayStepDetails": {"shape": "RouteContinueHighwayStepDetails", "documentation": "<p>Details that are specific to a Continue Highway step.</p>"}, "ContinueStepDetails": {"shape": "RouteContinueStepDetails", "documentation": "<p>Details that are specific to a Continue step.</p>"}, "CurrentRoad": {"shape": "RouteRoad", "documentation": "<p>Details of the current road.</p>"}, "Distance": {"shape": "DistanceMeters", "documentation": "<p>Distance of the step.</p>"}, "Duration": {"shape": "DurationSeconds", "documentation": "<p>Duration of the step.</p> <p> <b>Unit</b>: <code>seconds</code> </p>"}, "EnterHighwayStepDetails": {"shape": "RouteEnterHighwayStepDetails", "documentation": "<p>Details that are specific to a Enter Highway step.</p>"}, "ExitNumber": {"shape": "LocalizedStringList", "documentation": "<p>Exit number of the road exit, if applicable.</p>"}, "ExitStepDetails": {"shape": "RouteExitStepDetails", "documentation": "<p>Details that are specific to a Roundabout Exit step.</p>"}, "GeometryOffset": {"shape": "RouteVehicleTravelStepGeometryOffsetInteger", "documentation": "<p>Offset in the leg geometry corresponding to the start of this step.</p>"}, "Instruction": {"shape": "String", "documentation": "<p>Brief description of the step in the requested language.</p> <note> <p>Only available when the TravelStepType is Default.</p> </note>"}, "KeepStepDetails": {"shape": "RouteKeepStepDetails", "documentation": "<p>Details that are specific to a Keep step.</p>"}, "NextRoad": {"shape": "RouteRoad", "documentation": "<p>Details of the next road. See RouteRoad for details of sub-attributes.</p>"}, "RampStepDetails": {"shape": "RouteRampStepDetails", "documentation": "<p>Details that are specific to a Ramp step.</p>"}, "RoundaboutEnterStepDetails": {"shape": "RouteRoundaboutEnterStepDetails", "documentation": "<p>Details that are specific to a Roundabout Enter step.</p>"}, "RoundaboutExitStepDetails": {"shape": "RouteRoundaboutExitStepDetails", "documentation": "<p>Details that are specific to a Roundabout Exit step.</p>"}, "RoundaboutPassStepDetails": {"shape": "RouteRoundaboutPassStepDetails", "documentation": "<p>Details that are specific to a Roundabout Pass step.</p>"}, "Signpost": {"shape": "RouteSignpost", "documentation": "<p>Sign post information of the action, applicable only for TurnByTurn steps. See RouteSignpost for details of sub-attributes.</p>"}, "TurnStepDetails": {"shape": "RouteTurnStepDetails", "documentation": "<p>Details that are specific to a Turn step.</p>"}, "Type": {"shape": "RouteVehicleTravelStepType", "documentation": "<p>Type of the step.</p>"}, "UTurnStepDetails": {"shape": "RouteUTurnStepDetails", "documentation": "<p>Details that are specific to a Turn step.</p>"}}, "documentation": "<p>Steps of a leg that correspond to the travel portion of the leg.</p>"}, "RouteVehicleTravelStepGeometryOffsetInteger": {"type": "integer", "box": true, "min": 0}, "RouteVehicleTravelStepList": {"type": "list", "member": {"shape": "RouteVehicleTravelStep"}}, "RouteVehicleTravelStepType": {"type": "string", "enum": ["Arrive", "Continue", "ContinueHighway", "<PERSON><PERSON><PERSON>", "EnterHighway", "Exit", "Keep", "<PERSON><PERSON>", "RoundaboutEnter", "RoundaboutExit", "RoundaboutPass", "Turn", "UTurn"]}, "RouteViolatedConstraints": {"type": "structure", "required": ["HazardousCargos"], "members": {"AllHazardsRestricted": {"shape": "Boolean", "documentation": "<p>This restriction applies to truck cargo, where the resulting route excludes roads on which hazardous materials are prohibited from being transported.</p>"}, "AxleCount": {"shape": "RouteNoticeDetailRange", "documentation": "<p>Total number of axles of the vehicle.</p>"}, "HazardousCargos": {"shape": "RouteHazardousCargoTypeList", "documentation": "<p>List of Hazardous cargo contained in the vehicle.</p>"}, "MaxHeight": {"shape": "DimensionCentimeters", "documentation": "<p>The maximum height of the vehicle.</p>"}, "MaxKpraLength": {"shape": "DimensionCentimeters", "documentation": "<p>The maximum Kpra length of the vehicle.</p> <p> <b>Unit</b>: <code>centimeters</code> </p>"}, "MaxLength": {"shape": "DimensionCentimeters", "documentation": "<p>The maximum length of the vehicle.</p>"}, "MaxPayloadCapacity": {"shape": "WeightKilograms", "documentation": "<p>The maximum load capacity of the vehicle.</p> <p> <b>Unit</b>: <code>kilograms</code> </p>"}, "MaxWeight": {"shape": "RouteWeightConstraint", "documentation": "<p>The maximum weight of the route.</p> <p> <b>Unit</b>: <code>Kilograms</code> </p>"}, "MaxWeightPerAxle": {"shape": "WeightKilograms", "documentation": "<p>The maximum weight per axle of the vehicle.</p> <p> <b>Unit</b>: <code>Kilograms</code> </p>"}, "MaxWeightPerAxleGroup": {"shape": "WeightPerAxleGroup", "documentation": "<p>The maximum weight per axle group of the vehicle.</p> <p> <b>Unit</b>: <code>Kilograms</code> </p>"}, "MaxWidth": {"shape": "DimensionCentimeters", "documentation": "<p>The maximum width of the vehicle.</p>"}, "Occupancy": {"shape": "RouteNoticeDetailRange", "documentation": "<p>The number of occupants in the vehicle.</p> <p>Default Value: <code>1</code> </p>"}, "RestrictedTimes": {"shape": "String", "documentation": "<p>Access radius restrictions based on time.</p>"}, "TimeDependent": {"shape": "Boolean", "documentation": "<p>The time dependent constraint.</p>"}, "TrailerCount": {"shape": "RouteNoticeDetailRange", "documentation": "<p>Number of trailers attached to the vehicle.</p> <p>Default Value: <code>0</code> </p>"}, "TravelMode": {"shape": "Boolean", "documentation": "<p>Travel mode corresponding to the leg.</p>"}, "TruckRoadType": {"shape": "String", "documentation": "<p>Truck road type identifiers. <code>BK1</code> through <code>BK4</code> apply only to Sweden. <code>A2,A4,B2,B4,C,D,ET2,ET4</code> apply only to Mexico.</p> <note> <p>There are currently no other supported values as of 26th April 2024.</p> </note>"}, "TruckType": {"shape": "RouteTruckType", "documentation": "<p>Type of the truck.</p>"}, "TunnelRestrictionCode": {"shape": "TunnelRestrictionCode", "documentation": "<p>The tunnel restriction code.</p> <p>Tunnel categories in this list indicate the restrictions which apply to certain tunnels in Great Britain. They relate to the types of dangerous goods that can be transported through them.</p> <ul> <li> <p> <i>Tunnel Category B</i> </p> <ul> <li> <p> <i>Risk Level</i>: Limited risk</p> </li> <li> <p> <i>Restrictions</i>: Few restrictions</p> </li> </ul> </li> <li> <p> <i>Tunnel Category C</i> </p> <ul> <li> <p> <i>Risk Level</i>: Medium risk</p> </li> <li> <p> <i>Restrictions</i>: Some restrictions</p> </li> </ul> </li> <li> <p> <i>Tunnel Category D</i> </p> <ul> <li> <p> <i>Risk Level</i>: High risk</p> </li> <li> <p> <i>Restrictions</i>: Many restrictions occur</p> </li> </ul> </li> <li> <p> <i>Tunnel Category E</i> </p> <ul> <li> <p> <i>Risk Level</i>: Very high risk</p> </li> <li> <p> <i>Restrictions</i>: Restricted tunnel</p> </li> </ul> </li> </ul>"}}, "documentation": "<p>This property contains a summary of violated constraints.</p>"}, "RouteWaypoint": {"type": "structure", "required": ["Position"], "members": {"AvoidActionsForDistance": {"shape": "RouteWaypointAvoidActionsForDistanceLong", "documentation": "<p>Avoids actions for the provided distance. This is typically to consider for users in moving vehicles who may not have sufficient time to make an action at an origin or a destination.</p>"}, "AvoidUTurns": {"shape": "Boolean", "documentation": "<p>Avoid U-turns for calculation on highways and motorways.</p>"}, "Heading": {"shape": "Heading", "documentation": "<p>GPS Heading at the position.</p>"}, "Matching": {"shape": "RouteMatchingOptions", "documentation": "<p>Options to configure matching the provided position to the road network.</p>"}, "PassThrough": {"shape": "Boolean", "documentation": "<p>If the waypoint should not be treated as a stop. If yes, the waypoint is passed through and doesn't split the route into different legs.</p>"}, "Position": {"shape": "Position", "documentation": "<p>Position defined as <code>[longitude, latitude]</code>.</p>"}, "SideOfStreet": {"shape": "RouteSideOfStreetOptions", "documentation": "<p>Options to configure matching the provided position to a side of the street.</p>"}, "StopDuration": {"shape": "DurationSeconds", "documentation": "<p>Duration of the stop.</p> <p> <b>Unit</b>: <code>seconds</code> </p>"}}, "documentation": "<p>Waypoint between the Origin and Destination.</p>"}, "RouteWaypointAvoidActionsForDistanceLong": {"type": "long", "max": 2000}, "RouteWaypointList": {"type": "list", "member": {"shape": "RouteWaypoint"}}, "RouteWeightConstraint": {"type": "structure", "required": ["Type", "Value"], "members": {"Type": {"shape": "RouteWeightConstraintType", "documentation": "<p>The type of constraint.</p>"}, "Value": {"shape": "WeightKilograms", "documentation": "<p>The constraint value.</p> <p> <b>Unit</b>: <code>Kilograms</code> </p>"}}, "documentation": "<p>The weight constraint for the route.</p> <p> <b>Unit</b>: <code>Kilograms</code> </p>"}, "RouteWeightConstraintType": {"type": "string", "enum": ["Current", "Gross", "Unknown"]}, "RouteZone": {"type": "structure", "members": {"Category": {"shape": "RouteZoneCategory", "documentation": "<p>The zone category.</p>"}, "Name": {"shape": "String", "documentation": "<p>The name of the zone.</p>"}}, "documentation": "<p>The zone.</p>"}, "RouteZoneCategory": {"type": "string", "enum": ["CongestionPricing", "Environmental", "Vignette"]}, "RouteZoneList": {"type": "list", "member": {"shape": "RouteZone"}}, "RoutingObjective": {"type": "string", "enum": ["FastestRoute", "ShortestRoute"]}, "SensitiveString": {"type": "string", "sensitive": true}, "SideOfStreetMatchingStrategy": {"type": "string", "enum": ["AnyStreet", "DividedStreetOnly"]}, "SnapToRoadsRequest": {"type": "structure", "required": ["TracePoints"], "members": {"Key": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>Optional: The API key to be used for authorization. Either an API key or valid SigV4 signature must be provided when making a request. </p>", "location": "querystring", "locationName": "key"}, "SnappedGeometryFormat": {"shape": "GeometryFormat", "documentation": "<p>Chooses what the returned SnappedGeometry format should be.</p> <p>Default Value: <code>FlexiblePolyline</code> </p>"}, "SnapRadius": {"shape": "SnapToRoadsRequestSnapRadiusLong", "documentation": "<p>The radius around the provided tracepoint that is considered for snapping.</p> <p> <b>Unit</b>: <code>meters</code> </p> <p>Default value: <code>300</code> </p>"}, "TracePoints": {"shape": "SnapToRoadsRequestTracePointsList", "documentation": "<p>List of trace points to be snapped onto the road network.</p>"}, "TravelMode": {"shape": "RoadSnapTravelMode", "documentation": "<p>Specifies the mode of transport when calculating a route. Used in estimating the speed of travel and road compatibility.</p> <p>Default Value: <code>Car</code> </p>"}, "TravelModeOptions": {"shape": "RoadSnapTravelModeOptions", "documentation": "<p>Travel mode related options for the provided travel mode.</p>"}}}, "SnapToRoadsRequestSnapRadiusLong": {"type": "long", "max": 10000, "min": 0}, "SnapToRoadsRequestTracePointsList": {"type": "list", "member": {"shape": "RoadSnapTracePoint"}, "max": 5000, "min": 2}, "SnapToRoadsResponse": {"type": "structure", "required": ["Notices", "PricingBucket", "SnappedGeometryFormat", "SnappedTracePoints"], "members": {"Notices": {"shape": "RoadSnapNoticeList", "documentation": "<p>Notices are additional information returned that indicate issues that occurred during route calculation.</p>"}, "PricingBucket": {"shape": "String", "documentation": "<p>The pricing bucket for which the query is charged at.</p>", "location": "header", "locationName": "x-amz-geo-pricing-bucket"}, "SnappedGeometry": {"shape": "RoadSnapSnappedGeometry", "documentation": "<p>The interpolated geometry for the snapped route onto the road network.</p>"}, "SnappedGeometryFormat": {"shape": "GeometryFormat", "documentation": "<p>Specifies the format of the geometry returned for each leg of the route.</p>"}, "SnappedTracePoints": {"shape": "RoadSnapSnappedTracePointList", "documentation": "<p>The trace points snapped onto the road network. </p>"}}}, "SpeedKilometersPerHour": {"type": "double", "min": 0.0}, "String": {"type": "string"}, "ThrottlingException": {"type": "structure", "required": ["Message"], "members": {"Message": {"shape": "String", "locationName": "message"}}, "documentation": "<p>The request was denied due to request throttling.</p>", "error": {"httpStatusCode": 429, "senderFault": true}, "exception": true, "retryable": {"throttling": false}}, "TimeOfDay": {"type": "string", "pattern": "([0-1]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9](Z|[+-]([0-1]?[0-9]|2[0-3]):[0-5][0-9])"}, "TimeThresholdList": {"type": "list", "member": {"shape": "TimeThresholdListMemberLong"}, "max": 5, "min": 1}, "TimeThresholdListMemberLong": {"type": "long", "max": 10800, "min": 0}, "TimestampWithTimezoneOffset": {"type": "string", "pattern": "([1-2][0-9]{3})-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([01][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9]|60)(\\.[0-9]{0,9})?(Z|[+-]([01][0-9]|2[0-3]):[0-5][0-9])"}, "TrafficUsage": {"type": "string", "enum": ["IgnoreTrafficData", "UseTrafficData"]}, "TruckRoadType": {"type": "string", "max": 3, "min": 1}, "TruckRoadTypeList": {"type": "list", "member": {"shape": "TruckRoadType"}, "max": 12, "min": 1}, "TunnelRestrictionCode": {"type": "string", "max": 1, "min": 1}, "TurnAngle": {"type": "double", "max": 180, "min": -180}, "ValidationException": {"type": "structure", "required": ["Message", "Reason", "FieldList"], "members": {"Message": {"shape": "String", "locationName": "message"}, "Reason": {"shape": "ValidationExceptionReason", "documentation": "<p>A message with the reason for the validation exception error.</p>", "locationName": "reason"}, "FieldList": {"shape": "ValidationExceptionFieldList", "documentation": "<p>The field where the invalid entry was detected.</p>", "locationName": "fieldList"}}, "documentation": "<p>The input fails to satisfy the constraints specified by an AWS service.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "ValidationExceptionField": {"type": "structure", "required": ["Name", "Message"], "members": {"Name": {"shape": "String", "documentation": "<p>The name of the Validation Exception Field.</p>", "locationName": "name"}, "Message": {"shape": "String", "documentation": "<p>The error message.</p>", "locationName": "message"}}, "documentation": "<p>The input fails to satisfy the constraints specified by the Amazon Location service.</p>"}, "ValidationExceptionFieldList": {"type": "list", "member": {"shape": "ValidationExceptionField"}}, "ValidationExceptionReason": {"type": "string", "enum": ["UnknownOperation", "Missing", "CannotParse", "FieldValidationFailed", "Other", "<PERSON><PERSON><PERSON>"]}, "WaypointId": {"type": "string", "max": 100, "min": 1}, "WaypointIndex": {"type": "integer"}, "WaypointOptimizationAccessHours": {"type": "structure", "required": ["From", "To"], "members": {"From": {"shape": "WaypointOptimizationAccessHoursEntry", "documentation": "<p>Contains the ID of the starting waypoint in this connection.</p>"}, "To": {"shape": "WaypointOptimizationAccessHoursEntry", "documentation": "<p>Contains the ID of the ending waypoint in this connection.</p>"}}, "documentation": "<p>Access hours corresponding to when a destination can be visited.</p>"}, "WaypointOptimizationAccessHoursEntry": {"type": "structure", "required": ["DayOfWeek", "TimeOfDay"], "members": {"DayOfWeek": {"shape": "DayOfWeek", "documentation": "<p>Day of the week.</p>"}, "TimeOfDay": {"shape": "TimeOfDay", "documentation": "<p>Time of the day.</p>"}}, "documentation": "<p>Hours of entry.</p>"}, "WaypointOptimizationAvoidanceArea": {"type": "structure", "required": ["Geometry"], "members": {"Geometry": {"shape": "WaypointOptimizationAvoidanceAreaGeometry", "documentation": "<p>Geometry of the area to be avoided.</p>"}}, "documentation": "<p>The area to be avoided.</p>"}, "WaypointOptimizationAvoidanceAreaGeometry": {"type": "structure", "members": {"BoundingBox": {"shape": "BoundingBox", "documentation": "<p>Geometry defined as a bounding box. The first pair represents the X and Y coordinates (longitude and latitude,) of the southwest corner of the bounding box; the second pair represents the X and Y coordinates (longitude and latitude) of the northeast corner.</p>"}}, "documentation": "<p>Geometry of the area to be avoided.</p>"}, "WaypointOptimizationAvoidanceOptions": {"type": "structure", "members": {"Areas": {"shape": "WaypointOptimizationAvoidanceOptionsAreasList", "documentation": "<p>Areas to be avoided.</p>"}, "CarShuttleTrains": {"shape": "Boolean", "documentation": "<p>Avoidance options for cars-shuttles-trains.</p>"}, "ControlledAccessHighways": {"shape": "Boolean", "documentation": "<p>Avoid controlled access highways while calculating the route.</p>"}, "DirtRoads": {"shape": "Boolean", "documentation": "<p>Avoid dirt roads while calculating the route.</p>"}, "Ferries": {"shape": "Boolean", "documentation": "<p>Avoidance options for ferries.</p>"}, "TollRoads": {"shape": "Boolean", "documentation": "<p>Avoids roads where the specified toll transponders are the only mode of payment.</p>"}, "Tunnels": {"shape": "Boolean", "documentation": "<p>Avoid tunnels while calculating the route.</p>"}, "UTurns": {"shape": "Boolean", "documentation": "<p>Avoid U-turns for calculation on highways and motorways.</p>"}}, "documentation": "<p>Specifies options for areas to avoid. This is a best-effort avoidance setting, meaning the router will try to honor the avoidance preferences but may still include restricted areas if no feasible alternative route exists. If avoidance options are not followed, the response will indicate that the avoidance criteria were violated.</p>"}, "WaypointOptimizationAvoidanceOptionsAreasList": {"type": "list", "member": {"shape": "WaypointOptimizationAvoidanceArea"}, "max": 20, "min": 0}, "WaypointOptimizationClusteringAlgorithm": {"type": "string", "enum": ["DrivingDistance", "TopologySegment"]}, "WaypointOptimizationClusteringOptions": {"type": "structure", "required": ["Algorithm"], "members": {"Algorithm": {"shape": "WaypointOptimizationClusteringAlgorithm", "documentation": "<p>The algorithm to be used. <code>DrivingDistance</code> assigns all the waypoints that are within driving distance of each other into a single cluster. <code>TopologySegment</code> assigns all the waypoints that are within the same topology segment into a single cluster. A Topology segment is a linear stretch of road between two junctions.</p>"}, "DrivingDistanceOptions": {"shape": "WaypointOptimizationDrivingDistanceOptions", "documentation": "<p>Driving distance options to be used when the clustering algorithm is DrivingDistance.</p>"}}, "documentation": "<p>Options for WaypointOptimizationClustering.</p>"}, "WaypointOptimizationConnection": {"type": "structure", "required": ["Distance", "From", "RestDuration", "To", "TravelDuration", "WaitDuration"], "members": {"Distance": {"shape": "DistanceMeters", "documentation": "<p>Distance of the step.</p>"}, "From": {"shape": "WaypointId", "documentation": "<p>contains the ID of the starting waypoint in this connection.</p>"}, "RestDuration": {"shape": "DurationSeconds", "documentation": "<p>Resting time before the driver can continue driving.</p>"}, "To": {"shape": "WaypointId", "documentation": "<p>Contains the ID of the ending waypoint in this connection.</p>"}, "TravelDuration": {"shape": "DurationSeconds", "documentation": "<p>Total duration.</p> <p> <b>Unit</b>: <code>seconds</code> </p>"}, "WaitDuration": {"shape": "DurationSeconds", "documentation": "<p>Duration of a wait step.</p> <p> <b>Unit</b>: <code>seconds</code> </p>"}}, "documentation": "<p>This contains information such as distance and duration from one waypoint to the next waypoint in the sequence.</p>"}, "WaypointOptimizationConnectionList": {"type": "list", "member": {"shape": "WaypointOptimizationConnection"}}, "WaypointOptimizationConstraint": {"type": "string", "enum": ["AccessHours", "AppointmentTime", "Before", "Heading", "ServiceDuration", "SideOfStreet"]}, "WaypointOptimizationDestinationOptions": {"type": "structure", "members": {"AccessHours": {"shape": "WaypointOptimizationAccessHours", "documentation": "<p>Access hours corresponding to when a waypoint can be visited.</p>"}, "AppointmentTime": {"shape": "TimestampWithTimezoneOffset", "documentation": "<p>Appointment time at the destination.</p>"}, "Heading": {"shape": "Heading", "documentation": "<p>GPS Heading at the position.</p>"}, "Id": {"shape": "WaypointId", "documentation": "<p>The waypoint Id.</p>"}, "ServiceDuration": {"shape": "DurationSeconds", "documentation": "<p>Service time spent at the destination. At an appointment, the service time should be the appointment duration.</p> <p> <b>Unit</b>: <code>seconds</code> </p>"}, "SideOfStreet": {"shape": "WaypointOptimizationSideOfStreetOptions", "documentation": "<p>Options to configure matching the provided position to a side of the street.</p>"}}, "documentation": "<p>Destination related options.</p>"}, "WaypointOptimizationDriverOptions": {"type": "structure", "members": {"RestCycles": {"shape": "WaypointOptimizationRestCycles", "documentation": "<p>Driver work-rest schedules defined by a short and long cycle. A rest needs to be taken after the short work duration. The short cycle can be repeated until you hit the long work duration, at which point the long rest duration should be taken before restarting.</p>"}, "RestProfile": {"shape": "WaypointOptimizationRestProfile", "documentation": "<p>Pre defined rest profiles for a driver schedule. The only currently supported profile is EU.</p>"}, "TreatServiceTimeAs": {"shape": "WaypointOptimizationServiceTimeTreatment", "documentation": "<p>If the service time provided at a waypoint/destination should be considered as rest or work. This contributes to the total time breakdown returned within the response.</p>"}}, "documentation": "<p>Driver related options.</p>"}, "WaypointOptimizationDrivingDistanceOptions": {"type": "structure", "required": ["DrivingDistance"], "members": {"DrivingDistance": {"shape": "DistanceMeters", "documentation": "<p>DrivingDistance assigns all the waypoints that are within driving distance of each other into a single cluster.</p>"}}, "documentation": "<p>Driving distance related options.</p>"}, "WaypointOptimizationExclusionOptions": {"type": "structure", "required": ["Countries"], "members": {"Countries": {"shape": "CountryCodeList", "documentation": "<p>List of countries to be avoided defined by two-letter or three-letter country codes.</p>"}}, "documentation": "<p>Specifies strict exclusion options for the route calculation. This setting mandates that the router will avoid any routes that include the specified options, rather than merely attempting to minimize them.</p>"}, "WaypointOptimizationFailedConstraint": {"type": "structure", "members": {"Constraint": {"shape": "WaypointOptimizationConstraint", "documentation": "<p>The failed constraint.</p>"}, "Reason": {"shape": "String", "documentation": "<p>Reason for the failed constraint.</p>"}}, "documentation": "<p>The failed constraint.</p>"}, "WaypointOptimizationFailedConstraintList": {"type": "list", "member": {"shape": "WaypointOptimizationFailedConstraint"}}, "WaypointOptimizationHazardousCargoType": {"type": "string", "enum": ["Combustible", "Corrosive", "Explosive", "Flammable", "Gas", "HarmfulToWater", "Organic", "Other", "Poison", "PoisonousInhalation", "Radioactive"]}, "WaypointOptimizationHazardousCargoTypeList": {"type": "list", "member": {"shape": "WaypointOptimizationHazardousCargoType"}}, "WaypointOptimizationImpedingWaypoint": {"type": "structure", "required": ["FailedConstraints", "Id", "Position"], "members": {"FailedConstraints": {"shape": "WaypointOptimizationFailedConstraintList", "documentation": "<p>Failed constraints for an impeding waypoint.</p>"}, "Id": {"shape": "WaypointId", "documentation": "<p>The waypoint Id.</p>"}, "Position": {"shape": "Position", "documentation": "<p>Position defined as <code>[longitude, latitude]</code>.</p>"}}, "documentation": "<p>The impeding waypoint.</p>"}, "WaypointOptimizationImpedingWaypointList": {"type": "list", "member": {"shape": "WaypointOptimizationImpedingWaypoint"}}, "WaypointOptimizationOptimizedWaypoint": {"type": "structure", "required": ["DepartureTime", "Id", "Position"], "members": {"ArrivalTime": {"shape": "TimestampWithTimezoneOffset", "documentation": "<p>Estimated time of arrival at the destination.</p> <p>Time format:<code>YYYY-MM-DDThh:mm:ss.sssZ | YYYY-MM-DDThh:mm:ss.sss+hh:mm</code> </p> <p>Examples:</p> <p> <code>2020-04-22T17:57:24Z</code> </p> <p> <code>2020-04-22T17:57:24+02:00</code> </p>"}, "ClusterIndex": {"shape": "ClusterIndex", "documentation": "<p>Index of the cluster the waypoint is associated with. The index is included in the response only if clustering was performed while processing the request.</p>"}, "DepartureTime": {"shape": "TimestampWithTimezoneOffset", "documentation": "<p>Estimated time of departure from thr origin.</p> <p>Time format:<code>YYYY-MM-DDThh:mm:ss.sssZ | YYYY-MM-DDThh:mm:ss.sss+hh:mm</code> </p> <p>Examples:</p> <p> <code>2020-04-22T17:57:24Z</code> </p> <p> <code>2020-04-22T17:57:24+02:00</code> </p>"}, "Id": {"shape": "WaypointId", "documentation": "<p>The waypoint Id.</p>"}, "Position": {"shape": "Position", "documentation": "<p>Position defined as <code>[longitude, latitude]</code>.</p>"}}, "documentation": "<p>The optimized waypoint.</p>"}, "WaypointOptimizationOptimizedWaypointList": {"type": "list", "member": {"shape": "WaypointOptimizationOptimizedWaypoint"}}, "WaypointOptimizationOriginOptions": {"type": "structure", "members": {"Id": {"shape": "WaypointId", "documentation": "<p>The Origin Id.</p>"}}, "documentation": "<p>Origin related options.</p>"}, "WaypointOptimizationPedestrianOptions": {"type": "structure", "members": {"Speed": {"shape": "WaypointOptimizationPedestrianOptionsSpeedDouble", "documentation": "<p>Walking speed.</p> <p> <b>Unit</b>: <code>KilometersPerHour</code> </p>", "box": true}}, "documentation": "<p>Options related to a pedestrian.</p>"}, "WaypointOptimizationPedestrianOptionsSpeedDouble": {"type": "double", "max": 7.2, "min": 1.8}, "WaypointOptimizationRestCycleDurations": {"type": "structure", "required": ["RestDuration", "WorkDuration"], "members": {"RestDuration": {"shape": "DurationSeconds", "documentation": "<p>Resting phase of the cycle.</p> <p> <b>Unit</b>: <code>seconds</code> </p>"}, "WorkDuration": {"shape": "DurationSeconds", "documentation": "<p>Working phase of the cycle.</p> <p> <b>Unit</b>: <code>seconds</code> </p>"}}, "documentation": "<p>Driver work-rest schedules defined by a short and long cycle. A rest needs to be taken after the short work duration. The short cycle can be repeated until you hit the long work duration, at which point the long rest duration should be taken before restarting.</p> <p> <b>Unit</b>: <code>seconds</code> </p>"}, "WaypointOptimizationRestCycles": {"type": "structure", "required": ["LongCycle", "ShortCycle"], "members": {"LongCycle": {"shape": "WaypointOptimizationRestCycleDurations", "documentation": "<p>Long cycle for a driver work-rest schedule.</p>"}, "ShortCycle": {"shape": "WaypointOptimizationRestCycleDurations", "documentation": "<p>Short cycle for a driver work-rest schedule</p>"}}, "documentation": "<p>Resting phase of the cycle.</p>"}, "WaypointOptimizationRestProfile": {"type": "structure", "required": ["Profile"], "members": {"Profile": {"shape": "WaypointOptimizationRestProfileProfileString", "documentation": "<p>Pre defined rest profiles for a driver schedule. The only currently supported profile is EU.</p>"}}, "documentation": "<p>Pre defined rest profiles for a driver schedule. The only currently supported profile is EU.</p>"}, "WaypointOptimizationRestProfileProfileString": {"type": "string", "max": 2, "min": 2}, "WaypointOptimizationSequencingObjective": {"type": "string", "enum": ["FastestRoute", "ShortestRoute"]}, "WaypointOptimizationServiceTimeTreatment": {"type": "string", "enum": ["Rest", "Work"]}, "WaypointOptimizationSideOfStreetOptions": {"type": "structure", "required": ["Position"], "members": {"Position": {"shape": "Position", "documentation": "<p>Position defined as <code>[longitude, latitude]</code>.</p>"}, "UseWith": {"shape": "SideOfStreetMatchingStrategy", "documentation": "<p>Strategy that defines when the side of street position should be used. AnyStreet will always use the provided position.</p> <p>Default Value: <code>DividedStreetOnly</code> </p>"}}, "documentation": "<p>Options to configure matching the provided position to a side of the street.</p>"}, "WaypointOptimizationTimeBreakdown": {"type": "structure", "required": ["RestDuration", "ServiceDuration", "TravelDuration", "WaitDuration"], "members": {"RestDuration": {"shape": "DurationSeconds", "documentation": "<p>Resting phase of the cycle.</p> <p> <b>Unit</b>: <code>seconds</code> </p>"}, "ServiceDuration": {"shape": "DurationSeconds", "documentation": "<p>Service time spent at the destination. At an appointment, the service time should be the appointment duration.</p> <p> <b>Unit</b>: <code>seconds</code> </p>"}, "TravelDuration": {"shape": "DurationSeconds", "documentation": "<p>Traveling phase of the cycle.</p> <p> <b>Unit</b>: <code>seconds</code> </p>"}, "WaitDuration": {"shape": "DurationSeconds", "documentation": "<p>Waiting phase of the cycle.</p> <p> <b>Unit</b>: <code>seconds</code> </p>"}}, "documentation": "<p>Time breakdown for the sequence.</p>"}, "WaypointOptimizationTrafficOptions": {"type": "structure", "members": {"Usage": {"shape": "TrafficUsage", "documentation": "<p>Determines if traffic should be used or ignored while calculating the route.</p> <p>Default Value: <code>UseTrafficData</code> </p>"}}, "documentation": "<p>Options related to traffic.</p>"}, "WaypointOptimizationTrailerOptions": {"type": "structure", "members": {"TrailerCount": {"shape": "WaypointOptimizationTrailerOptionsTrailerCountInteger", "documentation": "<p>Number of trailers attached to the vehicle.</p> <p>Default Value: <code>0</code> </p>"}}, "documentation": "<p>Trailer options corresponding to the vehicle.</p>"}, "WaypointOptimizationTrailerOptionsTrailerCountInteger": {"type": "integer", "box": true, "max": 255, "min": 0}, "WaypointOptimizationTravelMode": {"type": "string", "enum": ["Car", "Pedestrian", "<PERSON>ooter", "Truck"]}, "WaypointOptimizationTravelModeOptions": {"type": "structure", "members": {"Pedestrian": {"shape": "WaypointOptimizationPedestrianOptions", "documentation": "<p>Travel mode options when the provided travel mode is \"Pedestrian\"</p>"}, "Truck": {"shape": "WaypointOptimizationTruckOptions", "documentation": "<p>Travel mode options when the provided travel mode is \"Truck\"</p>"}}, "documentation": "<p>Travel mode related options for the provided travel mode.</p>"}, "WaypointOptimizationTruckOptions": {"type": "structure", "members": {"GrossWeight": {"shape": "WeightKilograms", "documentation": "<p>Gross weight of the vehicle including trailers, and goods at capacity.</p> <p> <b>Unit</b>: <code>Kilograms</code> </p>"}, "HazardousCargos": {"shape": "WaypointOptimizationHazardousCargoTypeList", "documentation": "<p>List of Hazardous cargo contained in the vehicle.</p>"}, "Height": {"shape": "WaypointOptimizationTruckOptionsHeightLong", "documentation": "<p>Height of the vehicle.</p> <p> <b>Unit</b>: <code>centimeters</code> </p>"}, "Length": {"shape": "WaypointOptimizationTruckOptionsLengthLong", "documentation": "<p>Length of the vehicle.</p> <p> <b>Unit</b>: <code>centimeters</code> </p>"}, "Trailer": {"shape": "WaypointOptimizationTrailerOptions", "documentation": "<p>Trailer options corresponding to the vehicle.</p>"}, "TruckType": {"shape": "WaypointOptimizationTruckType", "documentation": "<p>Type of the truck.</p>"}, "TunnelRestrictionCode": {"shape": "TunnelRestrictionCode", "documentation": "<p>The tunnel restriction code.</p> <p>Tunnel categories in this list indicate the restrictions which apply to certain tunnels in Great Britain. They relate to the types of dangerous goods that can be transported through them.</p> <ul> <li> <p> <i>Tunnel Category B</i> </p> <ul> <li> <p> <i>Risk Level</i>: Limited risk</p> </li> <li> <p> <i>Restrictions</i>: Few restrictions</p> </li> </ul> </li> <li> <p> <i>Tunnel Category C</i> </p> <ul> <li> <p> <i>Risk Level</i>: Medium risk</p> </li> <li> <p> <i>Restrictions</i>: Some restrictions</p> </li> </ul> </li> <li> <p> <i>Tunnel Category D</i> </p> <ul> <li> <p> <i>Risk Level</i>: High risk</p> </li> <li> <p> <i>Restrictions</i>: Many restrictions occur</p> </li> </ul> </li> <li> <p> <i>Tunnel Category E</i> </p> <ul> <li> <p> <i>Risk Level</i>: Very high risk</p> </li> <li> <p> <i>Restrictions</i>: Restricted tunnel</p> </li> </ul> </li> </ul>"}, "WeightPerAxle": {"shape": "WeightKilograms", "documentation": "<p>Heaviest weight per axle irrespective of the axle type or the axle group. Meant for usage in countries where the differences in axle types or axle groups are not distinguished.</p> <p> <b>Unit</b>: <code>Kilograms</code> </p>"}, "Width": {"shape": "WaypointOptimizationTruckOptionsWidthLong", "documentation": "<p>Width of the vehicle.</p> <p> <b>Unit</b>: <code>centimeters</code> </p>"}}, "documentation": "<p>Travel mode options when the provided travel mode is \"Truck\"</p>"}, "WaypointOptimizationTruckOptionsHeightLong": {"type": "long", "max": 5000, "min": 0}, "WaypointOptimizationTruckOptionsLengthLong": {"type": "long", "max": 30000, "min": 0}, "WaypointOptimizationTruckOptionsWidthLong": {"type": "long", "max": 5000, "min": 0}, "WaypointOptimizationTruckType": {"type": "string", "enum": ["StraightTruck", "Tractor"]}, "WaypointOptimizationWaypoint": {"type": "structure", "required": ["Position"], "members": {"AccessHours": {"shape": "WaypointOptimizationAccessHours", "documentation": "<p>Access hours corresponding to when a waypoint can be visited.</p>"}, "AppointmentTime": {"shape": "TimestampWithTimezoneOffset", "documentation": "<p>Appointment time at the waypoint.</p>"}, "Before": {"shape": "BeforeWaypointsList", "documentation": "<p>Constraint defining what waypoints are to be visited after this waypoint.</p>"}, "Heading": {"shape": "Heading", "documentation": "<p>GPS Heading at the position.</p>"}, "Id": {"shape": "WaypointId", "documentation": "<p>The waypoint Id.</p>"}, "Position": {"shape": "Position", "documentation": "<p>Position defined as <code>[longitude, latitude]</code>.</p>"}, "ServiceDuration": {"shape": "DurationSeconds", "documentation": "<p>Service time spent at the waypoint. At an appointment, the service time should be the appointment duration.</p> <p> <b>Unit</b>: <code>seconds</code> </p>"}, "SideOfStreet": {"shape": "WaypointOptimizationSideOfStreetOptions", "documentation": "<p>Options to configure matching the provided position to a side of the street.</p>"}}, "documentation": "<p>Waypoint between the Origin and Destination.</p>"}, "WaypointOptimizationWaypointList": {"type": "list", "member": {"shape": "WaypointOptimizationWaypoint"}}, "WeightKilograms": {"type": "long", "max": 4294967295, "min": 0}, "WeightPerAxleGroup": {"type": "structure", "members": {"Single": {"shape": "WeightKilograms", "documentation": "<p>Weight for single axle group.</p> <p> <b>Unit</b>: <code>Kilograms</code> </p>"}, "Tandem": {"shape": "WeightKilograms", "documentation": "<p>Weight for tandem axle group.</p> <p> <b>Unit</b>: <code>Kilograms</code> </p>"}, "Triple": {"shape": "WeightKilograms", "documentation": "<p>Weight for triple axle group.</p> <p> <b>Unit</b>: <code>Kilograms</code> </p>"}, "Quad": {"shape": "WeightKilograms", "documentation": "<p>Weight for quad axle group.</p> <p> <b>Unit</b>: <code>Kilograms</code> </p>"}, "Quint": {"shape": "WeightKilograms", "documentation": "<p>Weight for quad quint group.</p> <p> <b>Unit</b>: <code>Kilograms</code> </p>"}}, "documentation": "<p>Specifies the total weight for the specified axle group. Meant for usage in countries that have different regulations based on the axle group type.</p> <p> <b>Unit</b>: <code>Kilograms</code> </p>"}}, "documentation": "<p>With the Amazon Location Routes API you can calculate routes and estimate travel time based on up-to-date road network and live traffic information.</p> <p>Calculate optimal travel routes and estimate travel times using up-to-date road network and traffic data. Key features include:</p> <ul> <li> <p>Point-to-point routing with estimated travel time, distance, and turn-by-turn directions</p> </li> <li> <p>Multi-point route optimization to minimize travel time or distance</p> </li> <li> <p>Route matrices for efficient multi-destination planning</p> </li> <li> <p>Isoline calculations to determine reachable areas within specified time or distance thresholds</p> </li> <li> <p>Map-matching to align GPS traces with the road network</p> </li> </ul>"}