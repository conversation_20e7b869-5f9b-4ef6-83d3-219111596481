{"version": "2.0", "metadata": {"apiVersion": "2022-07-26", "endpointPrefix": "partnercentral-selling", "jsonVersion": "1.0", "protocol": "json", "protocols": ["json"], "serviceFullName": "Partner Central Selling API", "serviceId": "PartnerCentral Selling", "signatureVersion": "v4", "signingName": "partnercentral-selling", "targetPrefix": "AWSPartnerCentralSelling", "uid": "partnercentral-selling-2022-07-26", "auth": ["aws.auth#sigv4"]}, "operations": {"AcceptEngagementInvitation": {"name": "AcceptEngagementInvitation", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AcceptEngagementInvitationRequest"}, "errors": [{"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Use the <code>AcceptEngagementInvitation</code> action to accept an engagement invitation shared by AWS. Accepting the invitation indicates your willingness to participate in the engagement, granting you access to all engagement-related data.</p>"}, "AssignOpportunity": {"name": "AssignOpportunity", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AssignOpportunityRequest"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Enables you to reassign an existing <code>Opportunity</code> to another user within your Partner Central account. The specified user receives the opportunity, and it appears on their Partner Central dashboard, allowing them to take necessary actions or proceed with the opportunity.</p> <p>This is useful for distributing opportunities to the appropriate team members or departments within your organization, ensuring that each opportunity is handled by the right person. By default, the opportunity owner is the one who creates it. Currently, there's no API to enumerate the list of available users.</p>"}, "AssociateOpportunity": {"name": "AssociateOpportunity", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AssociateOpportunityRequest"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Enables you to create a formal association between an <code>Opportunity</code> and various related entities, enriching the context and details of the opportunity for better collaboration and decision making. You can associate an opportunity with the following entity types:</p> <ul> <li> <p>Partner Solution: A software product or consulting practice created and delivered by Partners. Partner Solutions help customers address business challenges using Amazon Web Services services.</p> </li> <li> <p>Amazon Web Services Products: Amazon Web Services offers many products and services that provide scalable, reliable, and cost-effective infrastructure solutions. For the latest list of Amazon Web Services products, see <a href=\"https://github.com/aws-samples/partner-crm-integration-samples/blob/main/resources/aws_products.json\">Amazon Web Services products</a>.</p> </li> <li> <p>Amazon Web Services Marketplace private offer: Allows Amazon Web Services Marketplace sellers to extend custom pricing and terms to individual Amazon Web Services customers. Sellers can negotiate custom prices, payment schedules, and end user license terms through private offers, enabling Amazon Web Services customers to acquire software solutions tailored to their specific needs. For more information, see <a href=\"https://docs.aws.amazon.com/marketplace/latest/buyerguide/buyer-private-offers.html\">Private offers in Amazon Web Services Marketplace</a>.</p> </li> </ul> <p>To obtain identifiers for these entities, use the following methods:</p> <ul> <li> <p>Solution: Use the <code>ListSolutions</code> operation.</p> </li> <li> <p>AWS Products: For the latest list of Amazon Web Services products, see <a href=\"https://github.com/aws-samples/partner-crm-integration-samples/blob/main/resources/aws_products.json\">Amazon Web Services products</a>.</p> </li> <li> <p>Amazon Web Services Marketplace private offer: Use the <a href=\"https://docs.aws.amazon.com/marketplace/latest/APIReference/catalog-apis.html\">Using the Amazon Web Services Marketplace Catalog API</a> to list entities. Specifically, use the <code>ListEntities</code> operation to retrieve a list of private offers. The request returns the details of available private offers. For more information, see <a href=\"https://docs.aws.amazon.com/marketplace-catalog/latest/api-reference/API_ListEntities.html\">ListEntities</a>.</p> </li> </ul>"}, "CreateEngagement": {"name": "CreateEngagement", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateEngagementRequest"}, "output": {"shape": "CreateEngagementResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>The <code>CreateEngagement</code> action allows you to create an <code>Engagement</code>, which serves as a collaborative space between different parties such as AWS Partners and AWS Sellers. This action automatically adds the caller's AWS account as an active member of the newly created <code>Engagement</code>.</p>", "idempotent": true}, "CreateEngagementInvitation": {"name": "CreateEngagementInvitation", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateEngagementInvitationRequest"}, "output": {"shape": "CreateEngagementInvitationResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p> This action creates an invitation from a sender to a single receiver to join an engagement. </p>", "idempotent": true}, "CreateOpportunity": {"name": "CreateOpportunity", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateOpportunityRequest"}, "output": {"shape": "CreateOpportunityResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Creates an <code>Opportunity</code> record in Partner Central. Use this operation to create a potential business opportunity for submission to Amazon Web Services. Creating an opportunity sets <code>Lifecycle.ReviewStatus</code> to <code>Pending Submission</code>.</p> <p>To submit an opportunity, follow these steps:</p> <ol> <li> <p>To create the opportunity, use <code>CreateOpportunity</code>.</p> </li> <li> <p>To associate a solution with the opportunity, use <code>AssociateOpportunity</code>.</p> </li> <li> <p>To start the engagement with AWS, use <code>StartEngagementFromOpportunity</code>.</p> </li> </ol> <p>After submission, you can't edit the opportunity until the review is complete. But opportunities in the <code>Pending Submission</code> state must have complete details. You can update the opportunity while it's in the <code>Pending Submission</code> state.</p> <p>There's a set of mandatory fields to create opportunities, but consider providing optional fields to enrich the opportunity record.</p>", "idempotent": true}, "CreateResourceSnapshot": {"name": "CreateResourceSnapshot", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateResourceSnapshotRequest"}, "output": {"shape": "CreateResourceSnapshotResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p> This action allows you to create an immutable snapshot of a specific resource, such as an opportunity, within the context of an engagement. The snapshot captures a subset of the resource's data based on the schema defined by the provided template.</p>", "idempotent": true}, "CreateResourceSnapshotJob": {"name": "CreateResourceSnapshotJob", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateResourceSnapshotJobRequest"}, "output": {"shape": "CreateResourceSnapshotJobResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Use this action to create a job to generate a snapshot of the specified resource within an engagement. It initiates an asynchronous process to create a resource snapshot. The job creates a new snapshot only if the resource state has changed, adhering to the same access control and immutability rules as direct snapshot creation.</p>", "idempotent": true}, "DeleteResourceSnapshotJob": {"name": "DeleteResourceSnapshotJob", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteResourceSnapshotJobRequest"}, "errors": [{"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p> Use this action to deletes a previously created resource snapshot job. The job must be in a stopped state before it can be deleted. </p>", "idempotent": true}, "DisassociateOpportunity": {"name": "DisassociateOpportunity", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DisassociateOpportunityRequest"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Allows you to remove an existing association between an <code>Opportunity</code> and related entities, such as a Partner Solution, Amazon Web Services product, or an Amazon Web Services Marketplace offer. This operation is the counterpart to <code>AssociateOpportunity</code>, and it provides flexibility to manage associations as business needs change.</p> <p>Use this operation to update the associations of an <code>Opportunity</code> due to changes in the related entities, or if an association was made in error. Ensuring accurate associations helps maintain clarity and accuracy to track and manage business opportunities. When you replace an entity, first attach the new entity and then disassociate the one to be removed, especially if it's the last remaining entity that's required.</p>"}, "GetAwsOpportunitySummary": {"name": "GetAwsOpportunitySummary", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetAwsOpportunitySummaryRequest"}, "output": {"shape": "GetAwsOpportunitySummaryResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves a summary of an AWS Opportunity. This summary includes high-level details about the opportunity sourced from AWS, such as lifecycle information, customer details, and involvement type. It is useful for tracking updates on the AWS opportunity corresponding to an opportunity in the partner's account.</p>"}, "GetEngagement": {"name": "GetEngagement", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetEngagementRequest"}, "output": {"shape": "GetEngagementResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Use this action to retrieve the engagement record for a given <code>EngagementIdentifier</code>.</p>"}, "GetEngagementInvitation": {"name": "GetEngagementInvitation", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetEngagementInvitationRequest"}, "output": {"shape": "GetEngagementInvitationResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves the details of an engagement invitation shared by AWS with a partner. The information includes aspects such as customer, project details, and lifecycle information. To connect an engagement invitation with an opportunity, match the invitation’s <code>Payload.Project.Title</code> with opportunity <code>Project.Title</code>.</p>"}, "GetOpportunity": {"name": "GetOpportunity", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetOpportunityRequest"}, "output": {"shape": "GetOpportunityResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Fetches the <code>Opportunity</code> record from Partner Central by a given <code>Identifier</code>.</p> <p>Use the <code>ListOpportunities</code> action or the event notification (from Amazon EventBridge) to obtain this identifier.</p>"}, "GetResourceSnapshot": {"name": "GetResourceSnapshot", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetResourceSnapshotRequest"}, "output": {"shape": "GetResourceSnapshotResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Use this action to retrieve a specific snapshot record.</p>"}, "GetResourceSnapshotJob": {"name": "GetResourceSnapshotJob", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetResourceSnapshotJobRequest"}, "output": {"shape": "GetResourceSnapshotJobResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Use this action to retrieves information about a specific resource snapshot job.</p>"}, "GetSellingSystemSettings": {"name": "GetSellingSystemSettings", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetSellingSystemSettingsRequest"}, "output": {"shape": "GetSellingSystemSettingsResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves the currently set system settings, which include the IAM Role used for resource snapshot jobs.</p>"}, "ListEngagementByAcceptingInvitationTasks": {"name": "ListEngagementByAcceptingInvitationTasks", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListEngagementByAcceptingInvitationTasksRequest"}, "output": {"shape": "ListEngagementByAcceptingInvitationTasksResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p> Lists all in-progress, completed, or failed StartEngagementByAcceptingInvitationTask tasks that were initiated by the caller's account. </p>"}, "ListEngagementFromOpportunityTasks": {"name": "ListEngagementFromOpportunityTasks", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListEngagementFromOpportunityTasksRequest"}, "output": {"shape": "ListEngagementFromOpportunityTasksResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p> Lists all in-progress, completed, or failed <code>EngagementFromOpportunity</code> tasks that were initiated by the caller's account. </p>"}, "ListEngagementInvitations": {"name": "ListEngagementInvitations", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListEngagementInvitationsRequest"}, "output": {"shape": "ListEngagementInvitationsResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves a list of engagement invitations sent to the partner. This allows partners to view all pending or past engagement invitations, helping them track opportunities shared by AWS.</p>"}, "ListEngagementMembers": {"name": "ListEngagementMembers", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListEngagementMembersRequest"}, "output": {"shape": "ListEngagementMembersResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves the details of member partners in an Engagement. This operation can only be invoked by members of the Engagement. The <code>ListEngagementMembers</code> operation allows you to fetch information about the members of a specific Engagement. This action is restricted to members of the Engagement being queried. </p>"}, "ListEngagementResourceAssociations": {"name": "ListEngagementResourceAssociations", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListEngagementResourceAssociationsRequest"}, "output": {"shape": "ListEngagementResourceAssociationsResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Lists the associations between resources and engagements where the caller is a member and has at least one snapshot in the engagement.</p>"}, "ListEngagements": {"name": "ListEngagements", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListEngagementsRequest"}, "output": {"shape": "ListEngagementsResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>This action allows users to retrieve a list of Engagement records from Partner Central. This action can be used to manage and track various engagements across different stages of the partner selling process. </p>"}, "ListOpportunities": {"name": "ListOpportunities", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListOpportunitiesRequest"}, "output": {"shape": "ListOpportunitiesResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>This request accepts a list of filters that retrieve opportunity subsets as well as sort options. This feature is available to partners from <a href=\"https://partnercentral.awspartner.com/\">Partner Central</a> using the <code>ListOpportunities</code> API action.</p> <p>To synchronize your system with Amazon Web Services, only list the opportunities that were newly created or updated. We recommend you rely on events emitted by the service into your Amazon Web Services account’s Amazon EventBridge default event bus, you can also use the <code>ListOpportunities</code> action.</p> <p>We recommend the following approach:</p> <ol> <li> <p>Find the latest <code>LastModifiedDate</code> that you stored, and only use the values that came from Amazon Web Services. Don’t use values generated by your system.</p> </li> <li> <p>When you send a <code>ListOpportunities</code> request, submit the date in ISO 8601 format in the <code>AfterLastModifiedDate</code> filter.</p> </li> <li> <p>Amazon Web Services only returns opportunities created or updated on or after that date and time. Use <code>NextToken</code> to iterate over all pages.</p> </li> </ol>"}, "ListResourceSnapshotJobs": {"name": "ListResourceSnapshotJobs", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListResourceSnapshotJobsRequest"}, "output": {"shape": "ListResourceSnapshotJobsResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p> Lists resource snapshot jobs owned by the customer. This operation supports various filtering scenarios, including listing all jobs owned by the caller, jobs for a specific engagement, jobs with a specific status, or any combination of these filters. </p>"}, "ListResourceSnapshots": {"name": "ListResourceSnapshots", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListResourceSnapshotsRequest"}, "output": {"shape": "ListResourceSnapshotsResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves a list of resource view snapshots based on specified criteria. This operation supports various use cases, including: </p> <ul> <li> <p>Fetching all snapshots associated with an engagement.</p> </li> <li> <p>Retrieving snapshots of a specific resource type within an engagement.</p> </li> <li> <p>Obtaining snapshots for a particular resource using a specified template.</p> </li> <li> <p>Accessing the latest snapshot of a resource within an engagement.</p> </li> <li> <p>Filtering snapshots by resource owner.</p> </li> </ul>"}, "ListSolutions": {"name": "ListSolutions", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListSolutionsRequest"}, "output": {"shape": "ListSolutionsResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves a list of Partner Solutions that the partner registered on Partner Central. This API is used to generate a list of solutions that an end user selects from for association with an opportunity.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns a list of tags for a resource.</p>"}, "PutSellingSystemSettings": {"name": "PutSellingSystemSettings", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutSellingSystemSettingsRequest"}, "output": {"shape": "PutSellingSystemSettingsResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Updates the currently set system settings, which include the IAM Role used for resource snapshot jobs.</p>", "idempotent": true}, "RejectEngagementInvitation": {"name": "RejectEngagementInvitation", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "RejectEngagementInvitationRequest"}, "errors": [{"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>This action rejects an <code>EngagementInvitation</code> that AWS shared. Rejecting an invitation indicates that the partner doesn't want to pursue the opportunity, and all related data will become inaccessible thereafter.</p>"}, "StartEngagementByAcceptingInvitationTask": {"name": "StartEngagementByAcceptingInvitationTask", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StartEngagementByAcceptingInvitationTaskRequest"}, "output": {"shape": "StartEngagementByAcceptingInvitationTaskResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>This action starts the engagement by accepting an <code>EngagementInvitation</code>. The task is asynchronous and involves the following steps: accepting the invitation, creating an opportunity in the partner’s account from the AWS opportunity, and copying details for tracking. When completed, an <code>Opportunity Created</code> event is generated, indicating that the opportunity has been successfully created in the partner's account.</p>"}, "StartEngagementFromOpportunityTask": {"name": "StartEngagementFromOpportunityTask", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StartEngagementFromOpportunityTaskRequest"}, "output": {"shape": "StartEngagementFromOpportunityTaskResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>This action initiates the engagement process from an existing opportunity by accepting the engagement invitation and creating a corresponding opportunity in the partner’s system. Similar to <code>StartEngagementByAcceptingInvitationTask</code>, this action is asynchronous and performs multiple steps before completion.</p>"}, "StartResourceSnapshotJob": {"name": "StartResourceSnapshotJob", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StartResourceSnapshotJobRequest"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Starts a resource snapshot job that has been previously created.</p>", "idempotent": true}, "StopResourceSnapshotJob": {"name": "StopResourceSnapshotJob", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StopResourceSnapshotJobRequest"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Stops a resource snapshot job. The job must be started prior to being stopped.</p>", "idempotent": true}, "SubmitOpportunity": {"name": "SubmitOpportunity", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "SubmitOpportunityRequest"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Use this action to submit an Opportunity that was previously created by partner for AWS review. After you perform this action, the Opportunity becomes non-editable until it is reviewed by AWS and has <code> LifeCycle.ReviewStatus </code> as either <code>Approved</code> or <code>Action Required</code>. </p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Assigns one or more tags (key-value pairs) to the specified resource.</p>", "idempotent": true}, "UntagResource": {"name": "UntagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Removes a tag or tags from a resource.</p>", "idempotent": true}, "UpdateOpportunity": {"name": "UpdateOpportunity", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateOpportunityRequest"}, "output": {"shape": "UpdateOpportunityResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Updates the <code>Opportunity</code> record identified by a given <code>Identifier</code>. This operation allows you to modify the details of an existing opportunity to reflect the latest information and progress. Use this action to keep the opportunity record up-to-date and accurate.</p> <p>When you perform updates, include the entire payload with each request. If any field is omitted, the API assumes that the field is set to <code>null</code>. The best practice is to always perform a <code>GetOpportunity</code> to retrieve the latest values, then send the complete payload with the updated values to be changed.</p>"}}, "shapes": {"AcceptEngagementInvitationRequest": {"type": "structure", "required": ["Catalog", "Identifier"], "members": {"Catalog": {"shape": "CatalogIdentifier", "documentation": "<p>The <code>CatalogType</code> parameter specifies the catalog associated with the engagement invitation. Accepted values are <code>AWS</code> and <code>Sandbox</code>, which determine the environment in which the engagement invitation is managed.</p>"}, "Identifier": {"shape": "EngagementInvitationArnOrIdentifier", "documentation": "<p> The <code>Identifier</code> parameter in the <code>AcceptEngagementInvitationRequest</code> specifies the unique identifier of the <code>EngagementInvitation</code> to be accepted. Providing the correct identifier ensures that the intended invitation is accepted. </p>"}}}, "AccessDeniedException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>This error occurs when you don't have permission to perform the requested action.</p> <p>You don’t have access to this action or resource. Review IAM policies or contact your AWS administrator for assistance.</p>", "exception": true}, "Account": {"type": "structure", "required": ["CompanyName"], "members": {"Address": {"shape": "Address", "documentation": "<p>Specifies the end <code>Customer</code>'s address details associated with the <code>Opportunity</code>.</p>"}, "AwsAccountId": {"shape": "AwsAccount", "documentation": "<p>Specifies the <code>Customer</code> Amazon Web Services account ID associated with the <code>Opportunity</code>.</p>"}, "CompanyName": {"shape": "AccountCompanyNameString", "documentation": "<p>Specifies the end <code>Customer</code>'s company name associated with the <code>Opportunity</code>.</p>"}, "Duns": {"shape": "DunsNumber", "documentation": "<p>Indicates the <code>Customer</code> DUNS number, if available.</p>"}, "Industry": {"shape": "Industry", "documentation": "<p>Specifies the industry the end <code>Customer</code> belongs to that's associated with the <code>Opportunity</code>. It refers to the category or sector where the customer's business operates. This is a required field.</p>"}, "OtherIndustry": {"shape": "AccountOtherIndustryString", "documentation": "<p>Specifies the end <code>Customer</code>'s industry associated with the <code>Opportunity</code>, when the selected value in the <code>Industry</code> field is <code>Other</code>.</p>"}, "WebsiteUrl": {"shape": "WebsiteUrl", "documentation": "<p>Specifies the end customer's company website URL associated with the <code>Opportunity</code>. This value is crucial to map the customer within the Amazon Web Services CRM system. This field is required in all cases except when the opportunity is related to national security.</p>"}}, "documentation": "<p>Specifies the <code>Customer</code>'s account details associated with the <code>Opportunity</code>.</p>"}, "AccountCompanyNameString": {"type": "string", "max": 120, "min": 0, "sensitive": true}, "AccountOtherIndustryString": {"type": "string", "max": 255, "min": 0}, "AccountReceiver": {"type": "structure", "required": ["AwsAccountId"], "members": {"Alias": {"shape": "<PERSON><PERSON>", "documentation": "<p>Represents the alias of the partner account receiving the Engagement Invitation, making it easier to identify and track the recipient in reports or logs.</p>"}, "AwsAccountId": {"shape": "AwsAccount", "documentation": "<p>Indicates the AWS account ID of the partner who received the Engagement Invitation. This is a unique identifier for managing engagements with specific AWS accounts.</p>"}}, "documentation": "<p>Contains the account details of the partner who received the Engagement Invitation, including the AWS account ID and company name.</p>"}, "AccountSummary": {"type": "structure", "required": ["CompanyName"], "members": {"Address": {"shape": "AddressSummary", "documentation": "<p>Specifies the end <code>Customer</code>'s address details associated with the <code>Opportunity</code>.</p>"}, "CompanyName": {"shape": "AccountSummaryCompanyNameString", "documentation": "<p>Specifies the end <code>Customer</code>'s company name associated with the <code>Opportunity</code>.</p>"}, "Industry": {"shape": "Industry", "documentation": "<p>Specifies which industry the end <code>Customer</code> belongs to associated with the <code>Opportunity</code>. It refers to the category or sector that the customer's business operates in.</p> <p>To submit a value outside the picklist, use <code>Other</code>.</p> <p>Conditionally mandatory if <code>Other</code> is selected for Industry Vertical in LOVs.</p>"}, "OtherIndustry": {"shape": "AccountSummaryOtherIndustryString", "documentation": "<p>Specifies the end <code>Customer</code>'s industry associated with the <code> Opportunity</code>, when the selected value in the <code>Industry</code> field is <code>Other</code>. This field is relevant when the customer's industry doesn't fall under the predefined picklist values and requires a custom description.</p>"}, "WebsiteUrl": {"shape": "WebsiteUrl", "documentation": "<p>Specifies the end customer's company website URL associated with the <code>Opportunity</code>. This value is crucial to map the customer within the Amazon Web Services CRM system.</p>"}}, "documentation": "<p>An object that contains an <code>Account</code>'s subset of fields.</p>"}, "AccountSummaryCompanyNameString": {"type": "string", "max": 120, "min": 0, "sensitive": true}, "AccountSummaryOtherIndustryString": {"type": "string", "max": 255, "min": 0}, "Address": {"type": "structure", "members": {"City": {"shape": "AddressCityString", "documentation": "<p>Specifies the end <code>Customer</code>'s city associated with the <code>Opportunity</code>.</p>"}, "CountryCode": {"shape": "CountryCode", "documentation": "<p>Specifies the end <code>Customer</code>'s country associated with the <code>Opportunity</code>.</p>"}, "PostalCode": {"shape": "AddressPostalCodeString", "documentation": "<p>Specifies the end <code>Customer</code>'s postal code associated with the <code>Opportunity</code>.</p>"}, "StateOrRegion": {"shape": "AddressPart", "documentation": "<p>Specifies the end <code>Customer</code>'s state or region associated with the <code>Opportunity</code>.</p> <p>Valid values: <code>Alabama | Alaska | American Samoa | Arizona | Arkansas | California | Colorado | Connecticut | Delaware | Dist. of Columbia | Federated States of Micronesia | Florida | Georgia | Guam | Hawaii | Idaho | Illinois | Indiana | Iowa | Kansas | Kentucky | Louisiana | Maine | Marshall Islands | Maryland | Massachusetts | Michigan | Minnesota | Mississippi | Missouri | Montana | Nebraska | Nevada | New Hampshire | New Jersey | New Mexico | New York | North Carolina | North Dakota | Northern Mariana Islands | Ohio | Oklahoma | Oregon | Palau | Pennsylvania | Puerto Rico | Rhode Island | South Carolina | South Dakota | Tennessee | Texas | Utah | Vermont | Virginia | Virgin Islands | Washington | West Virginia | Wisconsin | Wyoming | APO/AE | AFO/FPO | FPO, AP</code> </p>"}, "StreetAddress": {"shape": "AddressStreetAddressString", "documentation": "<p>Specifies the end <code>Customer</code>'s street address associated with the <code>Opportunity</code>.</p>"}}, "documentation": "<p>Specifies the end <code>Customer</code>'s address details associated with the <code>Opportunity</code>.</p>"}, "AddressCityString": {"type": "string", "max": 255, "min": 0, "sensitive": true}, "AddressPart": {"type": "string", "sensitive": true}, "AddressPostalCodeString": {"type": "string", "max": 20, "min": 0, "sensitive": true}, "AddressStreetAddressString": {"type": "string", "max": 255, "min": 0, "sensitive": true}, "AddressSummary": {"type": "structure", "members": {"City": {"shape": "AddressSummaryCityString", "documentation": "<p>Specifies the end <code>Customer</code>'s city associated with the <code>Opportunity</code>.</p>"}, "CountryCode": {"shape": "CountryCode", "documentation": "<p>Specifies the end <code>Customer</code>'s country associated with the <code>Opportunity</code>.</p>"}, "PostalCode": {"shape": "AddressSummaryPostalCodeString", "documentation": "<p>Specifies the end <code>Customer</code>'s postal code associated with the <code>Opportunity</code>.</p>"}, "StateOrRegion": {"shape": "AddressPart", "documentation": "<p>Specifies the end <code>Customer</code>'s state or region associated with the <code>Opportunity</code>.</p> <p>Valid values: <code>Alabama | Alaska | American Samoa | Arizona | Arkansas | California | Colorado | Connecticut | Delaware | Dist. of Columbia | Federated States of Micronesia | Florida | Georgia | Guam | Hawaii | Idaho | Illinois | Indiana | Iowa | Kansas | Kentucky | Louisiana | Maine | Marshall Islands | Maryland | Massachusetts | Michigan | Minnesota | Mississippi | Missouri | Montana | Nebraska | Nevada | New Hampshire | New Jersey | New Mexico | New York | North Carolina | North Dakota | Northern Mariana Islands | Ohio | Oklahoma | Oregon | Palau | Pennsylvania | Puerto Rico | Rhode Island | South Carolina | South Dakota | Tennessee | Texas | Utah | Vermont | Virginia | Virgin Islands | Washington | West Virginia | Wisconsin | Wyoming | APO/AE | AFO/FPO | FPO, AP</code> </p>"}}, "documentation": "<p>An object that contains an <code>Address</code> object's subset of fields.</p>"}, "AddressSummaryCityString": {"type": "string", "max": 255, "min": 0, "sensitive": true}, "AddressSummaryPostalCodeString": {"type": "string", "max": 20, "min": 0, "sensitive": true}, "Alias": {"type": "string", "max": 80, "min": 0, "sensitive": true}, "ApnPrograms": {"type": "list", "member": {"shape": "String"}}, "AssignOpportunityRequest": {"type": "structure", "required": ["Assignee", "Catalog", "Identifier"], "members": {"Assignee": {"shape": "As<PERSON>eeContact", "documentation": "<p>Specifies the user or team member responsible for managing the assigned opportunity. This field identifies the <i>Assignee</i> based on the partner's internal team structure. Ensure that the email address is associated with a registered user in your Partner Central account.</p>"}, "Catalog": {"shape": "CatalogIdentifier", "documentation": "<p>Specifies the catalog associated with the request. This field takes a string value from a predefined list: <code>AWS</code> or <code>Sandbox</code>. The catalog determines which environment the opportunity is assigned in. Use <code>AWS</code> to assign real opportunities in the Amazon Web Services catalog, and <code>Sandbox</code> for testing in secure, isolated environments.</p>"}, "Identifier": {"shape": "OpportunityIdentifier", "documentation": "<p>Requires the <code>Opportunity</code>'s unique identifier when you want to assign it to another user. Provide the correct identifier so the intended opportunity is reassigned.</p>"}}}, "AssigneeContact": {"type": "structure", "required": ["BusinessTitle", "Email", "FirstName", "LastName"], "members": {"BusinessTitle": {"shape": "JobTitle", "documentation": "<p>Specifies the business title of the assignee managing the opportunity. This helps clarify the individual's role and responsibilities within the organization. Use the value <code>PartnerAccountManager</code> to update details of the opportunity owner.</p>"}, "Email": {"shape": "Email", "documentation": "<p>Provides the email address of the assignee. This email is used for communications and notifications related to the opportunity.</p>"}, "FirstName": {"shape": "AssigneeContactFirstNameString", "documentation": "<p>Specifies the first name of the assignee managing the opportunity. The system automatically retrieves this value from the user profile by referencing the associated email address.</p>"}, "LastName": {"shape": "AssigneeContactLastNameString", "documentation": "<p>Specifies the last name of the assignee managing the opportunity. The system automatically retrieves this value from the user profile by referencing the associated email address.</p>"}}, "documentation": "<p>Represents the contact details of the individual assigned to manage the opportunity within the partner organization. This helps to ensure that there is a point of contact for the opportunity's progress.</p>"}, "AssigneeContactFirstNameString": {"type": "string", "max": 80, "min": 0, "sensitive": true}, "AssigneeContactLastNameString": {"type": "string", "max": 80, "min": 0, "sensitive": true}, "AssociateOpportunityRequest": {"type": "structure", "required": ["Catalog", "OpportunityIdentifier", "RelatedEntityIdentifier", "RelatedEntityType"], "members": {"Catalog": {"shape": "CatalogIdentifier", "documentation": "<p>Specifies the catalog associated with the request. This field takes a string value from a predefined list: <code>AWS</code> or <code>Sandbox</code>. The catalog determines which environment the opportunity association is made in. Use <code>AWS</code> to associate opportunities in the Amazon Web Services catalog, and <code>Sandbox</code> for testing in secure, isolated environments.</p>"}, "OpportunityIdentifier": {"shape": "OpportunityIdentifier", "documentation": "<p>Requires the <code>Opportunity</code>'s unique identifier when you want to associate it with a related entity. Provide the correct identifier so the intended opportunity is updated with the association.</p>"}, "RelatedEntityIdentifier": {"shape": "AssociateOpportunityRequestRelatedEntityIdentifierString", "documentation": "<p>Requires the related entity's unique identifier when you want to associate it with the <code> Opportunity</code>. For Amazon Web Services Marketplace entities, provide the Amazon Resource Name (ARN). Use the <a href=\"https://docs.aws.amazon.com/marketplace-catalog/latest/api-reference/welcome.html\"> Amazon Web Services Marketplace API</a> to obtain the ARN.</p>"}, "RelatedEntityType": {"shape": "RelatedEntityType", "documentation": "<p>Specifies the entity type that you're associating with the <code> Opportunity</code>. This helps to categorize and properly process the association.</p>"}}}, "AssociateOpportunityRequestRelatedEntityIdentifierString": {"type": "string", "max": 255, "min": 1}, "AwsAccount": {"type": "string", "pattern": "^([0-9]{12}|\\w{1,12})$", "sensitive": true}, "AwsAccountIdOrAliasList": {"type": "list", "member": {"shape": "AwsAccount"}, "max": 10, "min": 1}, "AwsAccountList": {"type": "list", "member": {"shape": "AwsAccount"}, "max": 10, "min": 1}, "AwsClosedLostReason": {"type": "string", "enum": ["Administrative", "Business Associate Agreement", "Company Acquired/Dissolved", "Competitive Offering", "Customer Data Requirement", "Customer Deficiency", "Customer Experience", "Delay / Cancellation of Project", "Duplicate", "Duplicate Opportunity", "Executive Blocker", "Failed Vetting", "Feature Limitation", "Financial/Commercial", "Insufficient Amazon Value", "Insufficient AWS Value", "International Constraints", "Legal / Tax / Regulatory", "Legal Terms and Conditions", "Lost to Competitor", "Lost to Competitor - Google", "Lost to Competitor - Microsoft", "Lost to Comp<PERSON><PERSON> - Other", "Lost to Competitor - Racks<PERSON>", "Lost to Competitor - SoftLayer", "Lost to Competitor - VMWare", "No Customer Reference", "No Integration Resources", "No Opportunity", "No Perceived Value of MP", "No Response", "Not Committed to AWS", "No Update", "On Premises Deployment", "Other", "Other (Details in Description)", "Partner Gap", "Past Due", "People/Relationship/Governance", "Platform Technology Limitation", "Preference for Competitor", "Price", "Product/Technology", "Product Not on AWS", "Security / Compliance", "Self-Service", "Technical Limitations", "Term Sheet Impasse"]}, "AwsFundingUsed": {"type": "string", "enum": ["Yes", "No"]}, "AwsMarketplaceOfferIdentifier": {"type": "string", "pattern": "^arn:aws:aws-marketplace:[a-z]{1,2}-[a-z]*-\\d+:\\d{12}:AWSMarketplace/Offer/.*$"}, "AwsMarketplaceOfferIdentifiers": {"type": "list", "member": {"shape": "AwsMarketplaceOfferIdentifier"}}, "AwsMemberBusinessTitle": {"type": "string", "enum": ["AWSSalesRep", "AWSAccount<PERSON><PERSON><PERSON>", "WWPSPDM", "PDM", "PSM", "ISVSM"]}, "AwsOpportunityCustomer": {"type": "structure", "members": {"Contacts": {"shape": "CustomerContactsList", "documentation": "<p>Provides a list of customer contacts involved in the opportunity. These contacts may include decision makers, influencers, and other stakeholders within the customer's organization.</p>"}}, "documentation": "<p>Represents the customer associated with the AWS opportunity. This field captures key details about the customer that are necessary for managing the opportunity.</p>"}, "AwsOpportunityInsights": {"type": "structure", "members": {"EngagementScore": {"shape": "EngagementScore", "documentation": "<p>Represents a score assigned by AWS to indicate the level of engagement and potential success for the opportunity. This score helps partners prioritize their efforts.</p>"}, "NextBestActions": {"shape": "String", "documentation": "<p>Provides recommendations from AWS on the next best actions to take in order to move the opportunity forward and increase the likelihood of success.</p>"}}, "documentation": "<p>Contains insights provided by AWS for the opportunity, offering recommendations and analysis that can help the partner optimize their engagement and strategy.</p>"}, "AwsOpportunityLifeCycle": {"type": "structure", "members": {"ClosedLostReason": {"shape": "AwsClosedLostReason", "documentation": "<p>Indicates the reason why an opportunity was marked as <code>Closed Lost</code>. This helps in understanding the context behind the lost opportunity and aids in refining future strategies.</p>"}, "NextSteps": {"shape": "AwsOpportunityLifeCycleNextStepsString", "documentation": "<p>Specifies the immediate next steps required to progress the opportunity. These steps are based on AWS guidance and the current stage of the opportunity.</p>"}, "NextStepsHistory": {"shape": "AwsOpportunityLifeCycleNextStepsHistoryList", "documentation": "<p>Provides a historical log of previous next steps that were taken to move the opportunity forward. This helps in tracking the decision-making process and identifying any delays or obstacles encountered.</p>"}, "Stage": {"shape": "AwsOpportunityStage", "documentation": "<p>Represents the current stage of the opportunity in its lifecycle, such as <code>Qualification</code>, <code>Validation</code>, or <code>Closed Won</code>. This helps in understanding the opportunity's progress.</p>"}, "TargetCloseDate": {"shape": "Date", "documentation": "<p>Indicates the expected date by which the opportunity is projected to close. This field helps in planning resources and timelines for both the partner and AWS.</p>"}}, "documentation": "<p>Tracks the lifecycle of the AWS opportunity, including stages such as qualification, validation, and closure. This field helps partners understand the current status and progression of the opportunity.</p>"}, "AwsOpportunityLifeCycleNextStepsHistoryList": {"type": "list", "member": {"shape": "ProfileNextStepsHistory"}, "max": 50, "min": 0}, "AwsOpportunityLifeCycleNextStepsString": {"type": "string", "max": 255, "min": 0, "sensitive": true}, "AwsOpportunityProject": {"type": "structure", "members": {"ExpectedCustomerSpend": {"shape": "ExpectedCustomerSpendList", "documentation": "<p>Indicates the expected spending by the customer over the course of the project. This value helps partners and AWS estimate the financial impact of the opportunity. Use the <a href=\"https://calculator.aws/#/\">AWS Pricing Calculator</a> to create an estimate of the customer’s total spend. If only annual recurring revenue (ARR) is available, distribute it across 12 months to provide an average monthly value.</p>"}}, "documentation": "<p>Captures details about the project associated with the opportunity, including objectives, scope, and customer requirements.</p>"}, "AwsOpportunityRelatedEntities": {"type": "structure", "members": {"AwsProducts": {"shape": "AwsProductIdentifiers", "documentation": "<p>Specifies the AWS products associated with the opportunity. This field helps track the specific products that are part of the proposed solution.</p>"}, "Solutions": {"shape": "SolutionIdentifiers", "documentation": "<p>Specifies the partner solutions related to the opportunity. These solutions represent the partner's offerings that are being positioned as part of the overall AWS opportunity.</p>"}}, "documentation": "<p>Represents other entities related to the AWS opportunity, such as AWS products, partner solutions, and marketplace offers. These associations help build a complete picture of the solution being sold.</p>"}, "AwsOpportunityStage": {"type": "string", "enum": ["Not Started", "In Progress", "Prospect", "Engaged", "Identified", "Qualify", "Research", "<PERSON><PERSON> Engaged", "Evaluating", "Seller Registered", "Term Sheet Negotiation", "Contract Negotiation", "Onboarding", "Building Integration", "Qualified", "On-hold", "Technical Validation", "Business Validation", "Committed", "Launched", "Deferred to Partner", "Closed Lost", "Completed", "Closed Incomplete"]}, "AwsOpportunityTeamMembersList": {"type": "list", "member": {"shape": "AwsTeamMember"}}, "AwsProductIdentifier": {"type": "string"}, "AwsProductIdentifiers": {"type": "list", "member": {"shape": "AwsProductIdentifier"}}, "AwsSubmission": {"type": "structure", "required": ["InvolvementType"], "members": {"InvolvementType": {"shape": "SalesInvolvementType", "documentation": "<p>Specifies the type of AWS involvement in the opportunity, such as coselling, deal support, or technical consultation. This helps categorize the nature of AWS participation.</p>"}, "Visibility": {"shape": "Visibility", "documentation": "<p>Determines who can view AWS involvement in the opportunity. Typically, this field is set to <code>Full</code> for most cases, but it may be restricted based on special program requirements or confidentiality needs.</p>"}}, "documentation": "<p>Indicates the level of AWS involvement in the opportunity. This field helps track AWS participation throughout the engagement, such as providing technical support, deal assistance, and sales support.</p>"}, "AwsTeamMember": {"type": "structure", "members": {"BusinessTitle": {"shape": "AwsMemberBusinessTitle", "documentation": "<p>Specifies the Amazon Web Services team member's business title and indicates their organizational role.</p>"}, "Email": {"shape": "Email", "documentation": "<p>Provides the Amazon Web Services team member's email address.</p>"}, "FirstName": {"shape": "AwsTeamMemberFirstNameString", "documentation": "<p>Provides the Amazon Web Services team member's first name.</p>"}, "LastName": {"shape": "AwsTeamMemberLastNameString", "documentation": "<p>Provides the Amazon Web Services team member's last name.</p>"}}, "documentation": "<p>Represents an Amazon Web Services team member for the engagement. This structure includes details such as name, email, and business title.</p>"}, "AwsTeamMemberFirstNameString": {"type": "string", "max": 80, "min": 0, "sensitive": true}, "AwsTeamMemberLastNameString": {"type": "string", "max": 80, "min": 0, "sensitive": true}, "CatalogIdentifier": {"type": "string", "pattern": "^[a-zA-Z]+$"}, "Channel": {"type": "string", "enum": ["AWS Marketing Central", "Content Syndication", "Display", "Email", "Live Event", "Out Of Home (OOH)", "Print", "Search", "Social", "Telemarketing", "TV", "Video", "Virtual Event"]}, "Channels": {"type": "list", "member": {"shape": "Channel"}}, "ClientToken": {"type": "string", "pattern": "^[!-~]{1,64}$"}, "ClosedLostReason": {"type": "string", "enum": ["Customer Deficiency", "Delay / Cancellation of Project", "Legal / Tax / Regulatory", "Lost to Competitor - Google", "Lost to Competitor - Microsoft", "Lost to Competitor - SoftLayer", "Lost to Competitor - VMWare", "Lost to Comp<PERSON><PERSON> - Other", "No Opportunity", "On Premises Deployment", "Partner Gap", "Price", "Security / Compliance", "Technical Limitations", "Customer Experience", "Other", "People/Relationship/Governance", "Product/Technology", "Financial/Commercial"]}, "CompanyName": {"type": "string", "max": 120, "min": 1, "sensitive": true}, "CompanyWebsiteUrl": {"type": "string", "max": 255, "min": 4, "pattern": "^((http|https)://)??(www[.])??([a-zA-Z0-9]|-)+?([.][a-zA-Z0-9(-|/|=|?)??]+?)+?$", "sensitive": true}, "CompetitorName": {"type": "string", "enum": ["Oracle Cloud", "On-Prem", "Co-location", "<PERSON><PERSON><PERSON><PERSON>", "AliCloud", "Google Cloud Platform", "IBM Softlayer", "Microsoft Azure", "Other- Cost Optimization", "No Competition", "*Other"]}, "ConflictException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>This error occurs when the request can’t be processed due to a conflict with the target resource's current state, which could result from updating or deleting the resource.</p> <p>Suggested action: Fetch the latest state of the resource, verify the state, and retry the request.</p>", "exception": true}, "Contact": {"type": "structure", "members": {"BusinessTitle": {"shape": "JobTitle", "documentation": "<p>The partner contact's title (job title or role) associated with the <code>Opportunity</code>. <code>BusinessTitle</code> supports either <code>PartnerAccountManager</code> or <code>OpportunityOwner</code>.</p>"}, "Email": {"shape": "Email", "documentation": "<p>The contact's email address associated with the <code>Opportunity</code>.</p>"}, "FirstName": {"shape": "ContactFirstNameString", "documentation": "<p>The contact's first name associated with the <code>Opportunity</code>.</p>"}, "LastName": {"shape": "ContactLastNameString", "documentation": "<p>The contact's last name associated with the <code>Opportunity</code>.</p>"}, "Phone": {"shape": "PhoneNumber", "documentation": "<p>The contact's phone number associated with the <code>Opportunity</code>.</p>"}}, "documentation": "<p>An object that contains a <code>Customer Partner</code>'s contact details.</p>"}, "ContactFirstNameString": {"type": "string", "max": 80, "min": 0, "sensitive": true}, "ContactLastNameString": {"type": "string", "max": 80, "min": 0, "sensitive": true}, "CountryCode": {"type": "string", "enum": ["US", "AF", "AX", "AL", "DZ", "AS", "AD", "AO", "AI", "AQ", "AG", "AR", "AM", "AW", "AU", "AT", "AZ", "BS", "BH", "BD", "BB", "BY", "BE", "BZ", "BJ", "BM", "BT", "BO", "BQ", "BA", "BW", "BV", "BR", "IO", "BN", "BG", "BF", "BI", "KH", "CM", "CA", "CV", "KY", "CF", "TD", "CL", "CN", "CX", "CC", "CO", "KM", "CG", "CK", "CR", "CI", "HR", "CU", "CW", "CY", "CZ", "CD", "DK", "DJ", "DM", "DO", "EC", "EG", "SV", "GQ", "ER", "EE", "ET", "FK", "FO", "FJ", "FI", "FR", "GF", "PF", "TF", "GA", "GM", "GE", "DE", "GH", "GI", "GR", "GL", "GD", "GP", "GU", "GT", "GG", "GN", "GW", "GY", "HT", "HM", "VA", "HN", "HK", "HU", "IS", "IN", "ID", "IR", "IQ", "IE", "IM", "IL", "IT", "JM", "JP", "JE", "JO", "KZ", "KE", "KI", "KR", "KW", "KG", "LA", "LV", "LB", "LS", "LR", "LY", "LI", "LT", "LU", "MO", "MK", "MG", "MW", "MY", "MV", "ML", "MT", "MH", "MQ", "MR", "MU", "YT", "MX", "FM", "MD", "MC", "MN", "ME", "MS", "MA", "MZ", "MM", "NA", "NR", "NP", "NL", "AN", "NC", "NZ", "NI", "NE", "NG", "NU", "NF", "MP", "NO", "OM", "PK", "PW", "PS", "PA", "PG", "PY", "PE", "PH", "PN", "PL", "PT", "PR", "QA", "RE", "RO", "RU", "RW", "BL", "SH", "KN", "LC", "MF", "PM", "VC", "WS", "SM", "ST", "SA", "SN", "RS", "SC", "SL", "SG", "SX", "SK", "SI", "SB", "SO", "ZA", "GS", "SS", "ES", "LK", "SD", "SR", "SJ", "SZ", "SE", "CH", "SY", "TW", "TJ", "TZ", "TH", "TL", "TG", "TK", "TO", "TT", "TN", "TR", "TM", "TC", "TV", "UG", "UA", "AE", "GB", "UM", "UY", "UZ", "VU", "VE", "VN", "VG", "VI", "WF", "EH", "YE", "ZM", "ZW"], "sensitive": true}, "CreateEngagementInvitationRequest": {"type": "structure", "required": ["Catalog", "ClientToken", "EngagementIdentifier", "Invitation"], "members": {"Catalog": {"shape": "CatalogIdentifier", "documentation": "<p> Specifies the catalog related to the engagement. Accepted values are <code>AWS</code> and <code>Sandbox</code>, which determine the environment in which the engagement is managed. </p>"}, "ClientToken": {"shape": "ClientToken", "documentation": "<p> Specifies a unique, client-generated UUID to ensure that the request is handled exactly once. This token helps prevent duplicate invitation creations. </p>", "idempotencyToken": true}, "EngagementIdentifier": {"shape": "EngagementIdentifier", "documentation": "<p> The unique identifier of the <code>Engagement</code> associated with the invitation. This parameter ensures the invitation is created within the correct <code>Engagement</code> context. </p>"}, "Invitation": {"shape": "Invitation", "documentation": "<p> The <code>Invitation</code> object all information necessary to initiate an engagement invitation to a partner. It contains a personalized message from the sender, the invitation's receiver, and a payload. The <code>Payload</code> can be the <code>OpportunityInvitation</code>, which includes detailed structures for sender contacts, partner responsibilities, customer information, and project details. </p>"}}}, "CreateEngagementInvitationResponse": {"type": "structure", "required": ["<PERSON><PERSON>", "Id"], "members": {"Arn": {"shape": "EngagementInvitationArn", "documentation": "<p> The Amazon Resource Name (ARN) that uniquely identifies the engagement invitation. </p>"}, "Id": {"shape": "EngagementInvitationIdentifier", "documentation": "<p> Unique identifier assigned to the newly created engagement invitation. </p>"}}}, "CreateEngagementRequest": {"type": "structure", "required": ["Catalog", "ClientToken", "Description", "Title"], "members": {"Catalog": {"shape": "CatalogIdentifier", "documentation": "<p>The <code>CreateEngagementRequest$Catalog</code> parameter specifies the catalog related to the engagement. Accepted values are <code>AWS</code> and <code>Sandbox</code>, which determine the environment in which the engagement is managed.</p>"}, "ClientToken": {"shape": "CreateEngagementRequestClientTokenString", "documentation": "<p>The <code>CreateEngagementRequest$ClientToken</code> parameter specifies a unique, case-sensitive identifier to ensure that the request is handled exactly once. The value must not exceed sixty-four alphanumeric characters.</p>", "idempotencyToken": true}, "Contexts": {"shape": "EngagementContexts", "documentation": "<p>The <code>Contexts</code> field is a required array of objects, with a maximum of 5 contexts allowed, specifying detailed information about customer projects associated with the Engagement. Each context object contains a <code>Type</code> field indicating the context type, which must be <code>CustomerProject</code> in this version, and a <code>Payload</code> field containing the <code>CustomerProject</code> details. The <code>CustomerProject</code> object is composed of two main components: <code>Customer</code> and <code>Project</code>. The <code>Customer</code> object includes information such as <code>CompanyName</code>, <code>WebsiteUrl</code>, <code>Industry</code>, and <code>CountryCode</code>, providing essential details about the customer. The <code>Project</code> object contains <code>Title</code>, <code>BusinessProblem</code>, and <code>TargetCompletionDate</code>, offering insights into the specific project associated with the customer. This structure allows comprehensive context to be included within the Engagement, facilitating effective collaboration between parties by providing relevant customer and project information.</p>"}, "Description": {"shape": "EngagementDescription", "documentation": "<p>Provides a description of the <code>Engagement</code>.</p>"}, "Title": {"shape": "EngagementTitle", "documentation": "<p>Specifies the title of the <code>Engagement</code>.</p>"}}}, "CreateEngagementRequestClientTokenString": {"type": "string", "pattern": "^[!-~]{1,64}$"}, "CreateEngagementResponse": {"type": "structure", "members": {"Arn": {"shape": "EngagementArn", "documentation": "<p>The Amazon Resource Name (ARN) that identifies the engagement.</p>"}, "Id": {"shape": "EngagementIdentifier", "documentation": "<p>Unique identifier assigned to the newly created engagement.</p>"}}}, "CreateOpportunityRequest": {"type": "structure", "required": ["Catalog", "ClientToken"], "members": {"Catalog": {"shape": "CatalogIdentifier", "documentation": "<p>Specifies the catalog associated with the request. This field takes a string value from a predefined list: <code>AWS</code> or <code>Sandbox</code>. The catalog determines which environment the opportunity is created in. Use <code>AWS</code> to create opportunities in the Amazon Web Services catalog, and <code>Sandbox</code> for testing in secure, isolated environments.</p>"}, "ClientToken": {"shape": "CreateOpportunityRequestClientTokenString", "documentation": "<p>Required to be unique, and should be unchanging, it can be randomly generated or a meaningful string.</p> <p>Default: None</p> <p>Best practice: To help ensure uniqueness and avoid conflicts, use a Universally Unique Identifier (UUID) as the <code>ClientToken</code>. You can use standard libraries from most programming languages to generate this. If you use the same client token, the API returns the following error: \"Conflicting client token submitted for a new request body.\"</p>", "idempotencyToken": true}, "Customer": {"shape": "Customer", "documentation": "<p>Specifies customer details associated with the <code>Opportunity</code>.</p>"}, "LifeCycle": {"shape": "LifeCycle", "documentation": "<p>An object that contains lifecycle details for the <code>Opportunity</code>.</p>"}, "Marketing": {"shape": "Marketing", "documentation": "<p>This object contains marketing details and is optional for an opportunity.</p>"}, "NationalSecurity": {"shape": "NationalSecurity", "documentation": "<p>Indicates whether the <code>Opportunity</code> pertains to a national security project. This field must be set to <code>true</code> only when the customer's industry is <i>Government</i>. Additional privacy and security measures apply during the review and management process for opportunities marked as <code>NationalSecurity</code>.</p>"}, "OpportunityTeam": {"shape": "PartnerOpportunityTeamMembersList", "documentation": "<p>Represents the internal team handling the opportunity. Specify collaborating members of this opportunity who are within the partner's organization.</p>"}, "OpportunityType": {"shape": "OpportunityType", "documentation": "<p>Specifies the opportunity type as a renewal, new, or expansion.</p> <p>Opportunity types:</p> <ul> <li> <p>New opportunity: Represents a new business opportunity with a potential customer that's not previously engaged with your solutions or services.</p> </li> <li> <p>Renewal opportunity: Represents an opportunity to renew an existing contract or subscription with a current customer, ensuring continuity of service.</p> </li> <li> <p>Expansion opportunity: Represents an opportunity to expand the scope of an existing contract or subscription, either by adding new services or increasing the volume of existing services for a current customer.</p> </li> </ul>"}, "Origin": {"shape": "OpportunityOrigin", "documentation": "<p>Specifies the origin of the opportunity, indicating if it was sourced from Amazon Web Services or the partner. For all opportunities created with <code>Catalog: AWS</code>, this field must only be <code>Partner Referral</code>. However, when using <code>Catalog: Sandbox</code>, you can set this field to <code>AWS Referral</code> to simulate Amazon Web Services referral creation. This allows Amazon Web Services-originated flows testing in the sandbox catalog.</p>"}, "PartnerOpportunityIdentifier": {"shape": "CreateOpportunityRequestPartnerOpportunityIdentifierString", "documentation": "<p>Specifies the opportunity's unique identifier in the partner's CRM system. This value is essential to track and reconcile because it's included in the outbound payload to the partner.</p> <p>This field allows partners to link an opportunity to their CRM, which helps to ensure seamless integration and accurate synchronization between the Partner Central API and the partner's internal systems.</p>"}, "PrimaryNeedsFromAws": {"shape": "PrimaryNeedsFromAws", "documentation": "<p>Identifies the type of support the partner needs from Amazon Web Services.</p> <p>Valid values:</p> <ul> <li> <p>Cosell—Architectural Validation: Confirmation from Amazon Web Services that the partner's proposed solution architecture is aligned with Amazon Web Services best practices and poses minimal architectural risks.</p> </li> <li> <p>Cosell—Business Presentation: Request Amazon Web Services seller's participation in a joint customer presentation.</p> </li> <li> <p>Cosell—Competitive Information: Access to Amazon Web Services competitive resources and support for the partner's proposed solution.</p> </li> <li> <p>Cosell—Pricing Assistance: Connect with an Amazon Web Services seller for support situations where a partner may be receiving an upfront discount on a service (for example: EDP deals).</p> </li> <li> <p>Cosell—Technical Consultation: Connect with an Amazon Web Services Solutions Architect to address the partner's questions about the proposed solution.</p> </li> <li> <p>Cosell—Total Cost of Ownership Evaluation: Assistance with quoting different cost savings of proposed solutions on Amazon Web Services versus on-premises or a traditional hosting environment.</p> </li> <li> <p>Cosell—Deal Support: Request Amazon Web Services seller's support to progress the opportunity (for example: joint customer call, strategic positioning).</p> </li> <li> <p>Cosell—Support for Public Tender/RFx: Opportunity related to the public sector where the partner needs Amazon Web Services RFx support.</p> </li> </ul>"}, "Project": {"shape": "Project", "documentation": "<p>An object that contains project details for the <code>Opportunity</code>.</p>"}, "SoftwareRevenue": {"shape": "SoftwareRevenue", "documentation": "<p>Specifies details of a customer's procurement terms. This is required only for partners in eligible programs.</p>"}}}, "CreateOpportunityRequestClientTokenString": {"type": "string", "min": 1}, "CreateOpportunityRequestPartnerOpportunityIdentifierString": {"type": "string", "max": 64, "min": 0}, "CreateOpportunityResponse": {"type": "structure", "required": ["Id"], "members": {"Id": {"shape": "OpportunityIdentifier", "documentation": "<p>Read-only, system-generated <code>Opportunity</code> unique identifier. Amazon Web Services creates this identifier, and it's used for all subsequent opportunity actions, such as updates, associations, and submissions. It helps to ensure that each opportunity is accurately tracked and managed.</p>"}, "LastModifiedDate": {"shape": "DateTime", "documentation": "<p> <code>DateTime</code> when the opportunity was last modified. When the <code>Opportunity</code> is created, its value is <code>CreatedDate</code>.</p>"}, "PartnerOpportunityIdentifier": {"shape": "String", "documentation": "<p>Specifies the opportunity's unique identifier in the partner's CRM system. This value is essential to track and reconcile because it's included in the outbound payload sent back to the partner.</p>"}}}, "CreateResourceSnapshotJobRequest": {"type": "structure", "required": ["Catalog", "ClientToken", "EngagementIdentifier", "ResourceIdentifier", "ResourceSnapshotTemplateIdentifier", "ResourceType"], "members": {"Catalog": {"shape": "CatalogIdentifier", "documentation": "<p>Specifies the catalog in which to create the snapshot job. Valid values are <code>AWS</code> and <code> Sandbox</code>.</p>"}, "ClientToken": {"shape": "CreateResourceSnapshotJobRequestClientTokenString", "documentation": "<p>A client-generated UUID used for idempotency check. The token helps prevent duplicate job creations.</p>", "idempotencyToken": true}, "EngagementIdentifier": {"shape": "EngagementIdentifier", "documentation": "<p>Specifies the identifier of the engagement associated with the resource to be snapshotted.</p>"}, "ResourceIdentifier": {"shape": "ResourceIdentifier", "documentation": "<p>Specifies the identifier of the specific resource to be snapshotted. The format depends on the <code> ResourceType</code>.</p>"}, "ResourceSnapshotTemplateIdentifier": {"shape": "ResourceTemplateName", "documentation": "<p>Specifies the name of the template that defines the schema for the snapshot.</p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>The type of resource for which the snapshot job is being created. Must be one of the supported resource types i.e. <code>Opportunity</code> </p>"}, "Tags": {"shape": "TagList", "documentation": "<p>A map of the key-value pairs of the tag or tags to assign.</p>"}}}, "CreateResourceSnapshotJobRequestClientTokenString": {"type": "string", "pattern": "^[!-~]{1,64}$"}, "CreateResourceSnapshotJobResponse": {"type": "structure", "members": {"Arn": {"shape": "ResourceSnapshotJobArn", "documentation": "<p>The Amazon Resource Name (ARN) of the created snapshot job.</p>"}, "Id": {"shape": "ResourceSnapshotJobIdentifier", "documentation": "<p>The unique identifier for the created snapshot job.</p>"}}}, "CreateResourceSnapshotRequest": {"type": "structure", "required": ["Catalog", "ClientToken", "EngagementIdentifier", "ResourceIdentifier", "ResourceSnapshotTemplateIdentifier", "ResourceType"], "members": {"Catalog": {"shape": "CatalogIdentifier", "documentation": "<p> Specifies the catalog where the snapshot is created. Valid values are <code>AWS</code> and <code>Sandbox</code>. </p>"}, "ClientToken": {"shape": "CreateResourceSnapshotRequestClientTokenString", "documentation": "<p> Specifies a unique, client-generated UUID to ensure that the request is handled exactly once. This token helps prevent duplicate snapshot creations. </p>", "idempotencyToken": true}, "EngagementIdentifier": {"shape": "EngagementIdentifier", "documentation": "<p> The unique identifier of the engagement associated with this snapshot. This field links the snapshot to a specific engagement context. </p>"}, "ResourceIdentifier": {"shape": "ResourceIdentifier", "documentation": "<p> The unique identifier of the specific resource to be snapshotted. The format and constraints of this identifier depend on the <code>ResourceType</code> specified. For example: For <code>Opportunity</code> type, it will be an opportunity ID. </p>"}, "ResourceSnapshotTemplateIdentifier": {"shape": "ResourceTemplateName", "documentation": "<p> The name of the template that defines the schema for the snapshot. This template determines which subset of the resource data will be included in the snapshot. Must correspond to an existing and valid template for the specified <code>ResourceType</code>. </p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p> Specifies the type of resource for which the snapshot is being created. This field determines the structure and content of the snapshot. Must be one of the supported resource types, such as: <code>Opportunity</code>. </p>"}}}, "CreateResourceSnapshotRequestClientTokenString": {"type": "string", "pattern": "^[!-~]{1,64}$"}, "CreateResourceSnapshotResponse": {"type": "structure", "members": {"Arn": {"shape": "ResourceArn", "documentation": "<p> Specifies the Amazon Resource Name (ARN) that uniquely identifies the snapshot created. </p>"}, "Revision": {"shape": "ResourceSnapshotRevision", "documentation": "<p> Specifies the revision number of the created snapshot. This field provides important information about the snapshot's place in the sequence of snapshots for the given resource. </p>"}}}, "CurrencyCode": {"type": "string", "enum": ["USD", "EUR", "GBP", "AUD", "CAD", "CNY", "NZD", "INR", "JPY", "CHF", "SEK", "AED", "AFN", "ALL", "AMD", "ANG", "AOA", "ARS", "AWG", "AZN", "BAM", "BBD", "BDT", "BGN", "BHD", "BIF", "BMD", "BND", "BOB", "BOV", "BRL", "BSD", "BTN", "BWP", "BYN", "BZD", "CDF", "CHE", "CHW", "CLF", "CLP", "COP", "COU", "CRC", "CUC", "CUP", "CVE", "CZK", "DJF", "DKK", "DOP", "DZD", "EGP", "ERN", "ETB", "FJD", "FKP", "GEL", "GHS", "GIP", "GMD", "GNF", "GTQ", "GYD", "HKD", "HNL", "HRK", "HTG", "HUF", "IDR", "ILS", "IQD", "IRR", "ISK", "JMD", "JOD", "KES", "KGS", "KHR", "KMF", "KPW", "KRW", "KWD", "KYD", "KZT", "LAK", "LBP", "LKR", "LRD", "LSL", "LYD", "MAD", "MDL", "MGA", "MKD", "MMK", "MNT", "MOP", "MRU", "MUR", "MVR", "MWK", "MXN", "MXV", "MYR", "MZN", "NAD", "NGN", "NIO", "NOK", "NPR", "OMR", "PAB", "PEN", "PGK", "PHP", "PKR", "PLN", "PYG", "QAR", "RON", "RSD", "RUB", "RWF", "SAR", "SBD", "SCR", "SDG", "SGD", "SHP", "SLL", "SOS", "SRD", "SSP", "STN", "SVC", "SYP", "SZL", "THB", "TJS", "TMT", "TND", "TOP", "TRY", "TTD", "TWD", "TZS", "UAH", "UGX", "USN", "UYI", "UYU", "UZS", "VEF", "VND", "VUV", "WST", "XAF", "XCD", "XDR", "XOF", "XPF", "XSU", "XUA", "YER", "ZAR", "ZMW", "ZWL"], "sensitive": true}, "Customer": {"type": "structure", "members": {"Account": {"shape": "Account", "documentation": "<p>An object that contains the customer's account details.</p>"}, "Contacts": {"shape": "CustomerContactsList", "documentation": "<p>Represents the contact details for individuals associated with the customer of the <code>Opportunity</code>. This field captures relevant contacts, including decision-makers, influencers, and technical stakeholders within the customer organization. These contacts are key to progressing the opportunity.</p>"}}, "documentation": "<p>An object that contains the customer's <code>Account</code> and <code>Contact</code>.</p>"}, "CustomerContactsList": {"type": "list", "member": {"shape": "Contact"}}, "CustomerProjectsContext": {"type": "structure", "members": {"Customer": {"shape": "EngagementCustomer"}, "Project": {"shape": "EngagementCustomerProjectDetails", "documentation": "<p>Information about the customer project associated with the Engagement.</p>"}}, "documentation": "<p>The CustomerProjects structure in Engagements offers a flexible framework for managing customer-project relationships. It supports multiple customers per Engagement and multiple projects per customer, while also allowing for customers without projects and projects without specific customers. </p> <p>All Engagement members have full visibility of customers and their associated projects, enabling the capture of relevant context even when project details are not fully defined. This structure also facilitates targeted invitations, allowing partners to focus on specific customers and their business problems when sending Engagement invitations. </p>"}, "CustomerSummary": {"type": "structure", "members": {"Account": {"shape": "Account<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>An object that contains a customer's account details.</p>"}}, "documentation": "<p>An object that contains a <code>Customer</code> object's subset of fields.</p>"}, "Date": {"type": "string", "pattern": "^[1-9][0-9]{3}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])$"}, "DateTime": {"type": "timestamp", "timestampFormat": "iso8601"}, "DeleteResourceSnapshotJobRequest": {"type": "structure", "required": ["Catalog", "ResourceSnapshotJobIdentifier"], "members": {"Catalog": {"shape": "CatalogIdentifier", "documentation": "<p> Specifies the catalog from which to delete the snapshot job. Valid values are <code>AWS</code> and <code>Sandbox</code>. </p>"}, "ResourceSnapshotJobIdentifier": {"shape": "ResourceSnapshotJobIdentifier", "documentation": "<p> The unique identifier of the resource snapshot job to be deleted. </p>"}}}, "DeliveryModel": {"type": "string", "enum": ["SaaS or PaaS", "BYOL or AMI", "Managed Services", "Professional Services", "Resell", "Other"]}, "DeliveryModels": {"type": "list", "member": {"shape": "DeliveryModel"}}, "DisassociateOpportunityRequest": {"type": "structure", "required": ["Catalog", "OpportunityIdentifier", "RelatedEntityIdentifier", "RelatedEntityType"], "members": {"Catalog": {"shape": "CatalogIdentifier", "documentation": "<p>Specifies the catalog associated with the request. This field takes a string value from a predefined list: <code>AWS</code> or <code>Sandbox</code>. The catalog determines which environment the opportunity disassociation is made in. Use <code>AWS</code> to disassociate opportunities in the Amazon Web Services catalog, and <code>Sandbox</code> for testing in secure, isolated environments.</p>"}, "OpportunityIdentifier": {"shape": "OpportunityIdentifier", "documentation": "<p>The opportunity's unique identifier for when you want to disassociate it from related entities. This identifier helps to ensure that the correct opportunity is updated.</p> <p>Validation: Ensure that the provided identifier corresponds to an existing opportunity in the Amazon Web Services system because incorrect identifiers result in an error and no changes are made.</p>"}, "RelatedEntityIdentifier": {"shape": "DisassociateOpportunityRequestRelatedEntityIdentifierString", "documentation": "<p>The related entity's identifier that you want to disassociate from the opportunity. Depending on the type of entity, this could be a simple identifier or an Amazon Resource Name (ARN) for entities managed through Amazon Web Services Marketplace.</p> <p>For Amazon Web Services Marketplace entities, use the Amazon Web Services Marketplace API to obtain the necessary ARNs. For guidance on retrieving these ARNs, see <a href=\"https://docs.aws.amazon.com/marketplace-catalog/latest/api-reference/welcome.html\"> Amazon Web Services MarketplaceUsing the Amazon Web Services Marketplace Catalog API</a>.</p> <p>Validation: Ensure the identifier or ARN is valid and corresponds to an existing entity. An incorrect or invalid identifier results in an error.</p>"}, "RelatedEntityType": {"shape": "RelatedEntityType", "documentation": "<p>The type of the entity that you're disassociating from the opportunity. When you specify the entity type, it helps the system correctly process the disassociation request to ensure that the right connections are removed.</p> <p>Examples of entity types include Partner Solution, Amazon Web Services product, and Amazon Web Services Marketplaceoffer. Ensure that the value matches one of the expected entity types.</p> <p>Validation: Provide a valid entity type to help ensure successful disassociation. An invalid or incorrect entity type results in an error.</p>"}}}, "DisassociateOpportunityRequestRelatedEntityIdentifierString": {"type": "string", "max": 255, "min": 1}, "DunsNumber": {"type": "string", "pattern": "^[0-9]{9}$", "sensitive": true}, "Email": {"type": "string", "max": 80, "min": 0, "pattern": "^[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?$", "sensitive": true}, "EngagementArn": {"type": "string", "pattern": "^arn:.*"}, "EngagementArnOrIdentifier": {"type": "string", "pattern": "^(arn:.*|eng-[0-9a-z]{14})$"}, "EngagementContextDetails": {"type": "structure", "required": ["Type"], "members": {"Payload": {"shape": "EngagementContextPayload", "documentation": "<p>Contains the specific details of the Engagement context. The structure of this payload varies depending on the Type field. </p>"}, "Type": {"shape": "EngagementContextType", "documentation": "<p>Specifies the type of Engagement context. Valid values are \"CustomerProject\" or \"Document\", indicating whether the context relates to a customer project or a document respectively. </p>"}}, "documentation": "<p>Provides detailed context information for an Engagement. This structure allows for specifying the type of context and its associated payload. </p>"}, "EngagementContextPayload": {"type": "structure", "members": {"CustomerProject": {"shape": "CustomerProjectsContext", "documentation": "<p>Contains detailed information about a customer project when the context type is \"CustomerProject\". This field is present only when the Type in EngagementContextDetails is set to \"CustomerProject\". </p>"}}, "documentation": "<p>Represents the payload of an Engagement context. The structure of this payload varies based on the context type specified in the EngagementContextDetails. </p>", "union": true}, "EngagementContextType": {"type": "string", "enum": ["CustomerProject"]}, "EngagementContexts": {"type": "list", "member": {"shape": "EngagementContextDetails"}, "max": 5, "min": 0}, "EngagementCustomer": {"type": "structure", "required": ["CompanyName", "CountryCode", "Industry", "WebsiteUrl"], "members": {"CompanyName": {"shape": "CompanyName", "documentation": "<p>Represents the name of the customer’s company associated with the Engagement Invitation. This field is used to identify the customer.</p>"}, "CountryCode": {"shape": "CountryCode", "documentation": "<p>Indicates the country in which the customer’s company operates. This field is useful for understanding regional requirements or compliance needs.</p>"}, "Industry": {"shape": "Industry", "documentation": "<p>Specifies the industry to which the customer’s company belongs. This field helps categorize the opportunity based on the customer’s business sector.</p>"}, "WebsiteUrl": {"shape": "CompanyWebsiteUrl", "documentation": "<p>Provides the website URL of the customer’s company. This field helps partners verify the legitimacy and size of the customer organization.</p>"}}, "documentation": "<p>Contains details about the customer associated with the Engagement Invitation, including company information and industry.</p>"}, "EngagementCustomerBusinessProblem": {"type": "string", "max": 255, "min": 20, "sensitive": true}, "EngagementCustomerProjectDetails": {"type": "structure", "required": ["BusinessProblem", "TargetCompletionDate", "Title"], "members": {"BusinessProblem": {"shape": "EngagementCustomerBusinessProblem", "documentation": "<p>A description of the business problem the project aims to solve.</p>"}, "TargetCompletionDate": {"shape": "EngagementCustomerProjectDetailsTargetCompletionDateString", "documentation": "<p>The target completion date for the customer's project.</p>"}, "Title": {"shape": "EngagementCustomerProjectTitle", "documentation": "<p>The title of the project.</p>"}}, "documentation": "<p>Provides comprehensive details about a customer project associated with an Engagement. This may include information such as project goals, timelines, and specific customer requirements. </p>"}, "EngagementCustomerProjectDetailsTargetCompletionDateString": {"type": "string", "pattern": "^[1-9][0-9]{3}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])$"}, "EngagementCustomerProjectTitle": {"type": "string", "max": 255, "min": 1}, "EngagementDescription": {"type": "string", "max": 255, "min": 0}, "EngagementIdentifier": {"type": "string", "pattern": "^eng-[0-9a-z]{14}$"}, "EngagementIdentifiers": {"type": "list", "member": {"shape": "EngagementArnOrIdentifier"}, "max": 10, "min": 1}, "EngagementInvitationArn": {"type": "string", "pattern": "^arn:aws:partnercentral::[0-9]{12}:[a-zA-Z]+/engagement-invitation/engi-[0-9,a-z]{13}$"}, "EngagementInvitationArnOrIdentifier": {"type": "string", "max": 255, "min": 1, "pattern": "^(arn:.*|engi-[0-9a-z]{13})$"}, "EngagementInvitationIdentifier": {"type": "string", "pattern": "^engi-[0-9,a-z]{13}$"}, "EngagementInvitationIdentifiers": {"type": "list", "member": {"shape": "EngagementInvitationArnOrIdentifier"}, "max": 10, "min": 1}, "EngagementInvitationPayloadType": {"type": "string", "enum": ["OpportunityInvitation"]}, "EngagementInvitationSummaries": {"type": "list", "member": {"shape": "EngagementInvitationSummary"}}, "EngagementInvitationSummary": {"type": "structure", "required": ["Catalog", "Id"], "members": {"Arn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the Engagement Invitation. The ARN is a unique identifier that allows partners to reference the invitation in their system and manage its lifecycle.</p>"}, "Catalog": {"shape": "CatalogIdentifier", "documentation": "<p>Specifies the catalog in which the Engagement Invitation resides. This can be either the <code>AWS</code> or <code>Sandbox</code> catalog, indicating whether the opportunity is live or being tested.</p>"}, "EngagementId": {"shape": "EngagementIdentifier", "documentation": "<p> The identifier of the Engagement associated with this invitation. This links the invitation to its parent Engagement. </p>"}, "EngagementTitle": {"shape": "EngagementTitle", "documentation": "<p>Provides a short title or description of the Engagement Invitation. This title helps partners quickly identify and differentiate between multiple engagement opportunities.</p>"}, "ExpirationDate": {"shape": "DateTime", "documentation": "<p>Indicates the date and time when the Engagement Invitation will expire. After this date, the invitation can no longer be accepted, and the opportunity will be unavailable to the partner.</p>"}, "Id": {"shape": "EngagementInvitationArnOrIdentifier", "documentation": "<p>Represents the unique identifier of the Engagement Invitation. This identifier is used to track the invitation and to manage responses like acceptance or rejection.</p>"}, "InvitationDate": {"shape": "DateTime", "documentation": "<p>Indicates the date when the Engagement Invitation was sent to the partner. This provides context for when the opportunity was shared and helps in tracking the timeline for engagement.</p>"}, "ParticipantType": {"shape": "ParticipantType", "documentation": "<p>Identifies the role of the caller in the engagement invitation.</p>"}, "PayloadType": {"shape": "EngagementInvitationPayloadType", "documentation": "<p>Describes the type of payload associated with the Engagement Invitation, such as <code>Opportunity</code> or <code>MarketplaceOffer</code>. This helps partners understand the nature of the engagement request from AWS.</p>"}, "Receiver": {"shape": "Receiver", "documentation": "<p>Specifies the partner company or individual that received the Engagement Invitation. This field is important for tracking who the invitation was sent to within the partner organization.</p>"}, "SenderAwsAccountId": {"shape": "AwsAccount", "documentation": "<p>Specifies the AWS account ID of the sender who initiated the Engagement Invitation. This allows the partner to identify the AWS entity or representative responsible for sharing the opportunity.</p>"}, "SenderCompanyName": {"shape": "EngagementInvitationSummarySenderCompanyNameString", "documentation": "<p>Indicates the name of the company or AWS division that sent the Engagement Invitation. This information is useful for partners to know which part of AWS is requesting engagement.</p>"}, "Status": {"shape": "InvitationStatus", "documentation": "<p>Represents the current status of the Engagement Invitation, such as <code>Pending</code>, <code>Accepted</code>, or <code>Rejected</code>. The status helps track the progress and response to the invitation.</p>"}}, "documentation": "<p>Provides a summarized view of the Engagement Invitation, including details like the identifier, status, and sender. This summary helps partners track and manage AWS originated opportunities.</p>"}, "EngagementInvitationSummarySenderCompanyNameString": {"type": "string", "max": 120, "min": 0}, "EngagementInvitationsPayloadType": {"type": "list", "member": {"shape": "EngagementInvitationPayloadType"}}, "EngagementMember": {"type": "structure", "members": {"AccountId": {"shape": "AwsAccount", "documentation": "<p>This is the unique identifier for the AWS account associated with the member organization. It's used for AWS-related operations and identity verification. </p>"}, "CompanyName": {"shape": "MemberCompanyName", "documentation": "<p>The official name of the member's company or organization.</p>"}, "WebsiteUrl": {"shape": "String", "documentation": "<p>The URL of the member company's website. This offers a way to find more information about the member organization and serves as an additional identifier. </p>"}}, "documentation": "<p>Engagement members are the participants in an Engagement, which is likely a collaborative project or business opportunity within the AWS partner network. Members can be different partner organizations or AWS accounts that are working together on a specific engagement. </p> <p>Each member is represented by their AWS Account ID, Company Name, and associated details. Members have a status within the Engagement (PENDING, ACCEPTED, REJECTED, or WITHDRAWN), indicating their current state of participation. Only existing members of an Engagement can view the list of other members. This implies a level of privacy and access control within the Engagement structure. </p>"}, "EngagementMemberSummaries": {"type": "list", "member": {"shape": "EngagementMemberSummary"}}, "EngagementMemberSummary": {"type": "structure", "members": {"CompanyName": {"shape": "MemberCompanyName", "documentation": "<p>The official name of the member's company or organization.</p>"}, "WebsiteUrl": {"shape": "String", "documentation": "<p>The URL of the member company's website. This offers a way to find more information about the member organization and serves as an additional identifier. </p>"}}, "documentation": "<p>The EngagementMemberSummary provides a snapshot of essential information about participants in an AWS Partner Central Engagement. This compact data structure encapsulates key details of each member, facilitating efficient collaboration and management within the Engagement. </p>"}, "EngagementMembers": {"type": "list", "member": {"shape": "EngagementMember"}, "max": 10, "min": 0}, "EngagementPageSize": {"type": "integer", "box": true, "max": 100, "min": 1}, "EngagementResourceAssociationSummary": {"type": "structure", "required": ["Catalog"], "members": {"Catalog": {"shape": "CatalogIdentifier", "documentation": "<p> Indicates the environment in which the resource and engagement exist. </p>"}, "CreatedBy": {"shape": "AwsAccount", "documentation": "<p>The AWS account ID of the entity that owns the resource. Identifies the account responsible for or having primary control over the resource. </p>"}, "EngagementId": {"shape": "EngagementIdentifier", "documentation": "<p> A unique identifier for the engagement associated with the resource. </p>"}, "ResourceId": {"shape": "ResourceIdentifier", "documentation": "<p> A unique identifier for the specific resource. Varies depending on the resource type. </p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p> Categorizes the type of resource associated with the engagement. </p>"}}, "documentation": "<p> This provide a streamlined view of the relationships between engagements and resources. These summaries offer a crucial link between collaborative engagements and the specific resources involved, such as opportunities.These summaries are particularly valuable for partners navigating complex engagements with multiple resources. They enable quick insights into resource distribution across engagements, support efficient resource management, and help maintain a clear overview of collaborative activities. </p>"}, "EngagementResourceAssociationSummaryList": {"type": "list", "member": {"shape": "EngagementResourceAssociationSummary"}}, "EngagementScore": {"type": "string", "enum": ["High", "Medium", "Low"]}, "EngagementSort": {"type": "structure", "required": ["SortBy", "SortOrder"], "members": {"SortBy": {"shape": "EngagementSortName", "documentation": "<p>The field by which to sort the results.</p>"}, "SortOrder": {"shape": "SortOrder", "documentation": "<p>The order in which to sort the results.</p>"}}, "documentation": "<p>Specifies the sorting parameters for listing Engagements.</p>"}, "EngagementSortName": {"type": "string", "enum": ["CreatedDate"]}, "EngagementSummary": {"type": "structure", "members": {"Arn": {"shape": "EngagementArn", "documentation": "<p>The Amazon Resource Name (ARN) of the created Engagement.</p>"}, "CreatedAt": {"shape": "DateTime", "documentation": "<p>The date and time when the Engagement was created.</p>"}, "CreatedBy": {"shape": "AwsAccount", "documentation": "<p>The AWS Account ID of the Engagement creator.</p>"}, "Id": {"shape": "EngagementIdentifier", "documentation": "<p>The unique identifier for the Engagement.</p>"}, "MemberCount": {"shape": "Integer", "documentation": "<p>The number of members in the Engagement.</p>"}, "Title": {"shape": "EngagementTitle", "documentation": "<p>The title of the Engagement.</p>"}}, "documentation": "<p>An object that contains an <code>Engagement</code>'s subset of fields. </p>"}, "EngagementSummaryList": {"type": "list", "member": {"shape": "EngagementSummary"}}, "EngagementTitle": {"type": "string", "max": 40, "min": 1}, "ExpectedCustomerSpend": {"type": "structure", "required": ["Amount", "CurrencyCode", "Frequency", "TargetCompany"], "members": {"Amount": {"shape": "String", "documentation": "<p>Represents the estimated monthly revenue that the partner expects to earn from the opportunity. This helps in forecasting financial returns.</p>"}, "CurrencyCode": {"shape": "ExpectedCustomerSpendCurrencyCodeEnum", "documentation": "<p>Indicates the currency in which the revenue estimate is provided. This helps in understanding the financial impact across different markets.</p>"}, "EstimationUrl": {"shape": "WebsiteUrl", "documentation": "<p>A URL providing additional information or context about the spend estimation.</p>"}, "Frequency": {"shape": "PaymentFrequency", "documentation": "<p>Indicates how frequently the customer is expected to spend the projected amount. This can include values such as <code>Monthly</code>, <code>Quarterly</code>, or <code>Annually</code>. The default value is <code>Monthly</code>, representing recurring monthly spend.</p>"}, "TargetCompany": {"shape": "ExpectedCustomerSpendTargetCompanyString", "documentation": "<p>Specifies the name of the partner company that is expected to generate revenue from the opportunity. This field helps track the partner’s involvement in the opportunity.</p>"}}, "documentation": "<p>Provides an estimate of the revenue that the partner is expected to generate from the opportunity. This information helps partners assess the financial value of the project.</p>"}, "ExpectedCustomerSpendCurrencyCodeEnum": {"type": "string", "enum": ["USD", "EUR", "GBP", "AUD", "CAD", "CNY", "NZD", "INR", "JPY", "CHF", "SEK", "AED", "AFN", "ALL", "AMD", "ANG", "AOA", "ARS", "AWG", "AZN", "BAM", "BBD", "BDT", "BGN", "BHD", "BIF", "BMD", "BND", "BOB", "BOV", "BRL", "BSD", "BTN", "BWP", "BYN", "BZD", "CDF", "CHE", "CHW", "CLF", "CLP", "COP", "COU", "CRC", "CUC", "CUP", "CVE", "CZK", "DJF", "DKK", "DOP", "DZD", "EGP", "ERN", "ETB", "FJD", "FKP", "GEL", "GHS", "GIP", "GMD", "GNF", "GTQ", "GYD", "HKD", "HNL", "HRK", "HTG", "HUF", "IDR", "ILS", "IQD", "IRR", "ISK", "JMD", "JOD", "KES", "KGS", "KHR", "KMF", "KPW", "KRW", "KWD", "KYD", "KZT", "LAK", "LBP", "LKR", "LRD", "LSL", "LYD", "MAD", "MDL", "MGA", "MKD", "MMK", "MNT", "MOP", "MRU", "MUR", "MVR", "MWK", "MXN", "MXV", "MYR", "MZN", "NAD", "NGN", "NIO", "NOK", "NPR", "OMR", "PAB", "PEN", "PGK", "PHP", "PKR", "PLN", "PYG", "QAR", "RON", "RSD", "RUB", "RWF", "SAR", "SBD", "SCR", "SDG", "SGD", "SHP", "SLL", "SOS", "SRD", "SSP", "STN", "SVC", "SYP", "SZL", "THB", "TJS", "TMT", "TND", "TOP", "TRY", "TTD", "TWD", "TZS", "UAH", "UGX", "USN", "UYI", "UYU", "UZS", "VEF", "VND", "VUV", "WST", "XAF", "XCD", "XDR", "XOF", "XPF", "XSU", "XUA", "YER", "ZAR", "ZMW", "ZWL"], "pattern": "^USD$", "sensitive": true}, "ExpectedCustomerSpendList": {"type": "list", "member": {"shape": "ExpectedCustomerSpend"}, "max": 10, "min": 0}, "ExpectedCustomerSpendTargetCompanyString": {"type": "string", "max": 80, "min": 1}, "GetAwsOpportunitySummaryRequest": {"type": "structure", "required": ["Catalog", "RelatedOpportunityIdentifier"], "members": {"Catalog": {"shape": "CatalogIdentifier", "documentation": "<p>Specifies the catalog in which the AWS Opportunity is located. Accepted values include <code>AWS</code> for production opportunities or <code>Sandbox</code> for testing purposes. The catalog determines which environment the opportunity data is pulled from.</p>"}, "RelatedOpportunityIdentifier": {"shape": "OpportunityIdentifier", "documentation": "<p>The unique identifier for the related partner opportunity. Use this field to correlate an AWS opportunity with its corresponding partner opportunity.</p>"}}}, "GetAwsOpportunitySummaryResponse": {"type": "structure", "required": ["Catalog"], "members": {"Catalog": {"shape": "CatalogIdentifier", "documentation": "<p>Specifies the catalog in which the AWS Opportunity exists. This is the environment (e.g., <code>AWS</code> or <code>Sandbox</code>) where the opportunity is being managed.</p>"}, "Customer": {"shape": "AwsOpportunityCustomer", "documentation": "<p>Provides details about the customer associated with the AWS Opportunity, including account information, industry, and other customer data. These details help partners understand the business context of the opportunity.</p>"}, "Insights": {"shape": "AwsOpportunityInsights", "documentation": "<p>Provides insights into the AWS Opportunity, including engagement score and recommended actions that AWS suggests for the partner.</p>"}, "InvolvementType": {"shape": "SalesInvolvementType", "documentation": "<p>Specifies the type of involvement AWS has in the opportunity, such as direct cosell or advisory support. This field helps partners understand the role AWS plays in advancing the opportunity.</p>"}, "InvolvementTypeChangeReason": {"shape": "InvolvementTypeChangeReason", "documentation": "<p>Provides a reason for any changes in the involvement type of AWS in the opportunity. This field is used to track why the level of AWS engagement has changed from <code>For Visibility Only</code> to <code>Co-sell</code> offering transparency into the partnership dynamics.</p>"}, "LifeCycle": {"shape": "AwsOpportunityLifeCycle", "documentation": "<p>Contains lifecycle information for the AWS Opportunity, including review status, stage, and target close date. This field is crucial for partners to monitor the progression of the opportunity.</p>"}, "OpportunityTeam": {"shape": "AwsOpportunityTeamMembersList", "documentation": "<p>Details the AWS opportunity team, including members involved. This information helps partners know who from AWS is engaged and what their role is.</p>"}, "Origin": {"shape": "OpportunityOrigin", "documentation": "<p>Specifies whether the AWS Opportunity originated from AWS or the partner. This helps distinguish between opportunities that were sourced by AWS and those referred by the partner.</p>"}, "Project": {"shape": "AwsOpportunityProject", "documentation": "<p>Provides details about the project associated with the AWS Opportunity, including the customer’s business problem, expected outcomes, and project scope. This information is crucial for understanding the broader context of the opportunity.</p>"}, "RelatedEntityIds": {"shape": "AwsOpportunityRelatedEntities", "documentation": "<p>Lists related entity identifiers, such as AWS products or partner solutions, associated with the AWS Opportunity. These identifiers provide additional context and help partners understand which AWS services are involved.</p>"}, "RelatedOpportunityId": {"shape": "OpportunityIdentifier", "documentation": "<p>Provides the unique identifier of the related partner opportunity, allowing partners to link the AWS Opportunity to their corresponding opportunity in their CRM system.</p>"}, "Visibility": {"shape": "Visibility", "documentation": "<p>Defines the visibility level for the AWS Opportunity. Use <code>Full</code> visibility for most cases, while <code>Limited</code> visibility is reserved for special programs or sensitive opportunities.</p>"}}}, "GetEngagementInvitationRequest": {"type": "structure", "required": ["Catalog", "Identifier"], "members": {"Catalog": {"shape": "CatalogIdentifier", "documentation": "<p>Specifies the catalog associated with the request. The field accepts values from the predefined set: <code>AWS</code> for live operations or <code>Sandbox</code> for testing environments.</p>"}, "Identifier": {"shape": "EngagementInvitationArnOrIdentifier", "documentation": "<p>Specifies the unique identifier for the retrieved engagement invitation.</p>"}}}, "GetEngagementInvitationResponse": {"type": "structure", "required": ["Catalog", "Id"], "members": {"Arn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) that identifies the engagement invitation.</p>"}, "Catalog": {"shape": "CatalogIdentifier", "documentation": "<p>Indicates the catalog from which the engagement invitation details are retrieved. This field helps in identifying the appropriate catalog (e.g., <code>AWS</code> or <code>Sandbox</code>) used in the request.</p>"}, "EngagementDescription": {"shape": "EngagementDescription", "documentation": "<p>The description of the engagement associated with this invitation.</p>"}, "EngagementId": {"shape": "EngagementIdentifier", "documentation": "<p>The identifier of the engagement associated with this invitation.This ID links the invitation to its corresponding engagement.</p>"}, "EngagementTitle": {"shape": "EngagementTitle", "documentation": "<p>The title of the engagement invitation, summarizing the purpose or objectives of the opportunity shared by AWS.</p>"}, "ExistingMembers": {"shape": "EngagementMemberSummaries", "documentation": "<p>A list of active members currently part of the Engagement. This array contains a maximum of 10 members, each represented by an object with the following properties.</p> <ul> <li> <p>CompanyName: The name of the member's company.</p> </li> <li> <p>WebsiteUrl: The website URL of the member's company.</p> </li> </ul>"}, "ExpirationDate": {"shape": "DateTime", "documentation": "<p>Indicates the date on which the engagement invitation will expire if not accepted by the partner.</p>"}, "Id": {"shape": "EngagementInvitationArnOrIdentifier", "documentation": "<p>Unique identifier assigned to the engagement invitation being retrieved.</p>"}, "InvitationDate": {"shape": "DateTime", "documentation": "<p>The date when the engagement invitation was sent to the partner.</p>"}, "InvitationMessage": {"shape": "InvitationMessage", "documentation": "<p>The message sent to the invited partner when the invitation was created.</p>"}, "Payload": {"shape": "Payload", "documentation": "<p>Details of the engagement invitation payload, including specific data relevant to the invitation's contents, such as customer information and opportunity insights.</p>"}, "PayloadType": {"shape": "EngagementInvitationPayloadType", "documentation": "<p>The type of payload contained in the engagement invitation, indicating what data or context the payload covers.</p>"}, "Receiver": {"shape": "Receiver", "documentation": "<p>Information about the partner organization or team that received the engagement invitation, including contact details and identifiers.</p>"}, "RejectionReason": {"shape": "RejectionReasonString", "documentation": "<p>If the engagement invitation was rejected, this field specifies the reason provided by the partner for the rejection.</p>"}, "SenderAwsAccountId": {"shape": "AwsAccount", "documentation": "<p>Specifies the AWS Account ID of the sender, which identifies the AWS team responsible for sharing the engagement invitation.</p>"}, "SenderCompanyName": {"shape": "GetEngagementInvitationResponseSenderCompanyNameString", "documentation": "<p>The name of the AWS organization or team that sent the engagement invitation.</p>"}, "Status": {"shape": "InvitationStatus", "documentation": "<p>The current status of the engagement invitation.</p>"}}}, "GetEngagementInvitationResponseSenderCompanyNameString": {"type": "string", "max": 120, "min": 0}, "GetEngagementRequest": {"type": "structure", "required": ["Catalog", "Identifier"], "members": {"Catalog": {"shape": "CatalogIdentifier", "documentation": "<p>Specifies the catalog related to the engagement request. Valid values are <code>AWS</code> and <code>Sandbox</code>.</p>"}, "Identifier": {"shape": "EngagementArnOrIdentifier", "documentation": "<p>Specifies the identifier of the Engagement record to retrieve.</p>"}}}, "GetEngagementResponse": {"type": "structure", "members": {"Arn": {"shape": "EngagementArn", "documentation": "<p>The Amazon Resource Name (ARN) of the engagement retrieved.</p>"}, "Contexts": {"shape": "EngagementContexts", "documentation": "<p>A list of context objects associated with the engagement. Each context provides additional information related to the Engagement, such as customer projects or documents.</p>"}, "CreatedAt": {"shape": "DateTime", "documentation": "<p>The date and time when the Engagement was created, presented in ISO 8601 format (UTC). For example: \"2023-05-01T20:37:46Z\". This timestamp helps track the lifecycle of the Engagement.</p>"}, "CreatedBy": {"shape": "AwsAccount", "documentation": "<p>The AWS account ID of the user who originally created the engagement. This field helps in tracking the origin of the engagement.</p>"}, "Description": {"shape": "EngagementDescription", "documentation": "<p>A more detailed description of the engagement. This provides additional context or information about the engagement's purpose or scope.</p>"}, "Id": {"shape": "EngagementIdentifier", "documentation": "<p>The unique resource identifier of the engagement retrieved.</p>"}, "MemberCount": {"shape": "Integer", "documentation": "<p>Specifies the current count of members participating in the Engagement. This count includes all active members regardless of their roles or permissions within the Engagement.</p>"}, "Title": {"shape": "EngagementTitle", "documentation": "<p>The title of the engagement. It provides a brief, descriptive name for the engagement that is meaningful and easily recognizable.</p>"}}}, "GetOpportunityRequest": {"type": "structure", "required": ["Catalog", "Identifier"], "members": {"Catalog": {"shape": "CatalogIdentifier", "documentation": "<p>Specifies the catalog associated with the request. This field takes a string value from a predefined list: <code>AWS</code> or <code>Sandbox</code>. The catalog determines which environment the opportunity is fetched from. Use <code>AWS</code> to retrieve opportunities in the Amazon Web Services catalog, and <code>Sandbox</code> to retrieve opportunities in a secure, isolated testing environment.</p>"}, "Identifier": {"shape": "OpportunityIdentifier", "documentation": "<p>Read-only, system generated <code>Opportunity</code> unique identifier.</p>"}}}, "GetOpportunityResponse": {"type": "structure", "required": ["Catalog", "CreatedDate", "Id", "LastModifiedDate", "RelatedEntityIdentifiers"], "members": {"Arn": {"shape": "OpportunityArn", "documentation": "<p>The Amazon Resource Name (ARN) that uniquely identifies the opportunity.</p>"}, "Catalog": {"shape": "CatalogIdentifier", "documentation": "<p>Specifies the catalog associated with the request. This field takes a string value from a predefined list: <code>AWS</code> or <code>Sandbox</code>. The catalog determines which environment the opportunity information is retrieved from. Use <code>AWS</code> to retrieve opportunities in the Amazon Web Services catalog, and <code>Sandbox</code> to retrieve opportunities in a secure and isolated testing environment.</p>"}, "CreatedDate": {"shape": "DateTime", "documentation": "<p> <code>DateTime</code> when the <code>Opportunity</code> was last created.</p>"}, "Customer": {"shape": "Customer", "documentation": "<p>Specifies details of the customer associated with the <code>Opportunity</code>.</p>"}, "Id": {"shape": "OpportunityIdentifier", "documentation": "<p>Read-only, system generated <code>Opportunity</code> unique identifier.</p>"}, "LastModifiedDate": {"shape": "DateTime", "documentation": "<p> <code>DateTime</code> when the opportunity was last modified.</p>"}, "LifeCycle": {"shape": "LifeCycle", "documentation": "<p>An object that contains lifecycle details for the <code>Opportunity</code>.</p>"}, "Marketing": {"shape": "Marketing", "documentation": "<p>An object that contains marketing details for the <code>Opportunity</code>.</p>"}, "NationalSecurity": {"shape": "NationalSecurity", "documentation": "<p>Indicates whether the <code>Opportunity</code> pertains to a national security project. This field must be set to <code>true</code> only when the customer's industry is <i>Government</i>. Additional privacy and security measures apply during the review and management process for opportunities marked as <code>NationalSecurity</code>.</p>"}, "OpportunityTeam": {"shape": "PartnerOpportunityTeamMembersList", "documentation": "<p>Represents the internal team handling the opportunity. Specify the members involved in collaborating on this opportunity within the partner's organization.</p>"}, "OpportunityType": {"shape": "OpportunityType", "documentation": "<p>Specifies the opportunity type as renewal, new, or expansion.</p> <p>Opportunity types:</p> <ul> <li> <p>New opportunity: Represents a new business opportunity with a potential customer that's not previously engaged with your solutions or services.</p> </li> <li> <p>Renewal opportunity: Represents an opportunity to renew an existing contract or subscription with a current customer, which helps to ensure service continuity.</p> </li> <li> <p>Expansion opportunity: Represents an opportunity to expand the scope of a customer's contract or subscription, either by adding new services or increasing the volume of existing services.</p> </li> </ul>"}, "PartnerOpportunityIdentifier": {"shape": "GetOpportunityResponsePartnerOpportunityIdentifierString", "documentation": "<p>Specifies the opportunity's unique identifier in the partner's CRM system. This value is essential to track and reconcile because it's included in the outbound payload sent back to the partner.</p>"}, "PrimaryNeedsFromAws": {"shape": "PrimaryNeedsFromAws", "documentation": "<p>Identifies the type of support the partner needs from Amazon Web Services.</p> <p>Valid values:</p> <ul> <li> <p>Cosell—Architectural Validation: Confirmation from Amazon Web Services that the partner's proposed solution architecture is aligned with Amazon Web Services best practices and poses minimal architectural risks.</p> </li> <li> <p>Cosell—Business Presentation: Request Amazon Web Services seller's participation in a joint customer presentation.</p> </li> <li> <p>Cosell—Competitive Information: Access to Amazon Web Services competitive resources and support for the partner's proposed solution.</p> </li> <li> <p>Cosell—Pricing Assistance: Connect with an Amazon Web Services seller for support situations where a partner may be receiving an upfront discount on a service (for example: EDP deals).</p> </li> <li> <p>Cosell—Technical Consultation: Connect with an Amazon Web Services Solutions Architect to address the partner's questions about the proposed solution.</p> </li> <li> <p>Cosell—Total Cost of Ownership Evaluation: Assistance with quoting different cost savings of proposed solutions on Amazon Web Services versus on-premises or a traditional hosting environment.</p> </li> <li> <p>Cosell—Deal Support: Request Amazon Web Services seller's support to progress the opportunity (for example: joint customer call, strategic positioning).</p> </li> <li> <p>Cosell—Support for Public Tender/RFx: Opportunity related to the public sector where the partner needs Amazon Web Services RFx support.</p> </li> </ul>"}, "Project": {"shape": "Project", "documentation": "<p>An object that contains project details summary for the <code>Opportunity</code>.</p>"}, "RelatedEntityIdentifiers": {"shape": "RelatedEntityIdentifiers", "documentation": "<p>Provides information about the associations of other entities with the opportunity. These entities include identifiers for <code>AWSProducts</code>, <code>Partner Solutions</code>, and <code>AWSMarketplaceOffers</code>.</p>"}, "SoftwareRevenue": {"shape": "SoftwareRevenue", "documentation": "<p>Specifies details of a customer's procurement terms. Required only for partners in eligible programs.</p>"}}}, "GetOpportunityResponsePartnerOpportunityIdentifierString": {"type": "string", "max": 64, "min": 0}, "GetResourceSnapshotJobRequest": {"type": "structure", "required": ["Catalog", "ResourceSnapshotJobIdentifier"], "members": {"Catalog": {"shape": "CatalogIdentifier", "documentation": "<p>Specifies the catalog related to the request. Valid values are:</p> <ul> <li> <p> AWS: Retrieves the snapshot job from the production AWS environment. </p> </li> <li> <p> Sandbox: Retrieves the snapshot job from a sandbox environment used for testing or development purposes. </p> </li> </ul>"}, "ResourceSnapshotJobIdentifier": {"shape": "ResourceSnapshotJobIdentifier", "documentation": "<p>The unique identifier of the resource snapshot job to be retrieved. This identifier is crucial for pinpointing the specific job you want to query. </p>"}}}, "GetResourceSnapshotJobResponse": {"type": "structure", "required": ["Catalog"], "members": {"Arn": {"shape": "ResourceSnapshotJobArn", "documentation": "<p>The Amazon Resource Name (ARN) of the snapshot job. This globally unique identifier can be used for resource-specific operations across AWS services. </p>"}, "Catalog": {"shape": "CatalogIdentifier", "documentation": "<p>The catalog in which the snapshot job was created. This will match the Catalog specified in the request. </p>"}, "CreatedAt": {"shape": "DateTime", "documentation": "<p>The date and time when the snapshot job was created in ISO 8601 format (UTC). Example: \"2023-05-01T20:37:46Z\" </p>"}, "EngagementId": {"shape": "EngagementIdentifier", "documentation": "<p>The identifier of the engagement associated with this snapshot job. This links the job to a specific engagement context. </p>"}, "Id": {"shape": "ResourceSnapshotJobIdentifier", "documentation": "<p>The unique identifier of the snapshot job. This matches the ResourceSnapshotJobIdentifier provided in the request. </p>"}, "LastFailure": {"shape": "String", "documentation": "<p>If the job has encountered any failures, this field contains the error message from the most recent failure. This can be useful for troubleshooting issues with the job. </p>"}, "LastSuccessfulExecutionDate": {"shape": "DateTime", "documentation": "<p>The date and time of the last successful execution of the job, in ISO 8601 format (UTC). Example: \"2023-05-01T20:37:46Z\" </p>"}, "ResourceArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource being snapshotted. This provides a globally unique identifier for the resource across AWS. </p>"}, "ResourceId": {"shape": "ResourceIdentifier", "documentation": "<p>The identifier of the specific resource being snapshotted. The format might vary depending on the ResourceType. </p>"}, "ResourceSnapshotTemplateName": {"shape": "ResourceTemplateName", "documentation": "<p>The name of the template used for creating the snapshot. This is the same as the template name. It defines the structure and content of the snapshot.</p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>The type of resource being snapshotted. This would have \"Opportunity\" as a value as it is dependent on the supported resource type.</p>"}, "Status": {"shape": "ResourceSnapshotJobStatus", "documentation": "<p>The current status of the snapshot job. Valid values:</p> <ul> <li> <p>STOPPED: The job is not currently running.</p> </li> <li> <p>RUNNING: The job is actively executing.</p> </li> </ul>"}}}, "GetResourceSnapshotRequest": {"type": "structure", "required": ["Catalog", "EngagementIdentifier", "ResourceIdentifier", "ResourceSnapshotTemplateIdentifier", "ResourceType"], "members": {"Catalog": {"shape": "CatalogIdentifier", "documentation": "<p>Specifies the catalog related to the request. Valid values are:</p> <ul> <li> <p>AWS: Retrieves the snapshot from the production AWS environment.</p> </li> <li> <p>Sandbox: Retrieves the snapshot from a sandbox environment used for testing or development purposes.</p> </li> </ul>"}, "EngagementIdentifier": {"shape": "EngagementIdentifier", "documentation": "<p>The unique identifier of the engagement associated with the snapshot. This field links the snapshot to a specific engagement context.</p>"}, "ResourceIdentifier": {"shape": "ResourceIdentifier", "documentation": "<p>The unique identifier of the specific resource that was snapshotted. The format and constraints of this identifier depend on the ResourceType specified. For <code>Opportunity</code> type, it will be an <code>opportunity ID</code> </p>"}, "ResourceSnapshotTemplateIdentifier": {"shape": "ResourceTemplateName", "documentation": "<p>he name of the template that defines the schema for the snapshot. This template determines which subset of the resource data is included in the snapshot and must correspond to an existing and valid template for the specified <code>ResourceType</code>.</p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>Specifies the type of resource that was snapshotted. This field determines the structure and content of the snapshot payload. Valid value includes:<code>Opportunity</code>: For opportunity-related data. </p>"}, "Revision": {"shape": "ResourceSnapshotRevision", "documentation": "<p>Specifies which revision of the snapshot to retrieve. If omitted returns the latest revision.</p>"}}}, "GetResourceSnapshotResponse": {"type": "structure", "required": ["Catalog"], "members": {"Arn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) that uniquely identifies the resource snapshot.</p>"}, "Catalog": {"shape": "CatalogIdentifier", "documentation": "<p>The catalog in which the snapshot was created. Matches the Catalog specified in the request.</p>"}, "CreatedAt": {"shape": "DateTime", "documentation": "<p>The timestamp when the snapshot was created, in ISO 8601 format (e.g., \"2023-06-01T14:30:00Z\"). This allows for precise tracking of when the snapshot was taken. </p>"}, "CreatedBy": {"shape": "AwsAccount", "documentation": "<p>The AWS account ID of the principal (user or role) who created the snapshot. This helps in tracking the origin of the snapshot. </p>"}, "EngagementId": {"shape": "EngagementIdentifier", "documentation": "<p>The identifier of the engagement associated with this snapshot. Matches the EngagementIdentifier specified in the request. </p>"}, "Payload": {"shape": "ResourceSnapshotPayload"}, "ResourceId": {"shape": "ResourceIdentifier", "documentation": "<p>The identifier of the specific resource that was snapshotted. Matches the ResourceIdentifier specified in the request.</p>"}, "ResourceSnapshotTemplateName": {"shape": "ResourceTemplateName", "documentation": "<p>The name of the view used for this snapshot. This is the same as the template name.</p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>The type of the resource that was snapshotted. Matches the ResourceType specified in the request.</p>"}, "Revision": {"shape": "ResourceSnapshotRevision", "documentation": "<p>The revision number of this snapshot. This is a positive integer that is sequential and unique within the context of a resource view.</p>"}}}, "GetSellingSystemSettingsRequest": {"type": "structure", "required": ["Catalog"], "members": {"Catalog": {"shape": "CatalogIdentifier", "documentation": "<p>Specifies the catalog in which the settings are defined. Acceptable values include <code>AWS</code> for production and <code>Sandbox</code> for testing environments.</p>"}}}, "GetSellingSystemSettingsResponse": {"type": "structure", "required": ["Catalog"], "members": {"Catalog": {"shape": "CatalogIdentifier", "documentation": "<p>Specifies the catalog in which the settings are defined. Acceptable values include <code>AWS</code> for production and <code>Sandbox</code> for testing environments.</p>"}, "ResourceSnapshotJobRoleArn": {"shape": "ResourceSnapshotJobRoleArn", "documentation": "<p>Specifies the ARN of the IAM Role used for resource snapshot job executions.</p>"}}}, "Industry": {"type": "string", "enum": ["Aerospace", "Agriculture", "Automotive", "Computers and Electronics", "Consumer Goods", "Education", "Energy - Oil and Gas", "Energy - Power and Utilities", "Financial Services", "Gaming", "Government", "Healthcare", "Hospitality", "Life Sciences", "Manufacturing", "Marketing and Advertising", "Media and Entertainment", "Mining", "Non-Profit Organization", "Professional Services", "Real Estate and Construction", "Retail", "Software and Internet", "Telecommunications", "Transportation and Logistics", "Travel", "Wholesale and Distribution", "Other"]}, "Integer": {"type": "integer", "box": true}, "InternalServerException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>This error occurs when the specified resource can’t be found or doesn't exist. Resource ID and type might be incorrect.</p> <p>Suggested action: This is usually a transient error. Retry after the provided retry delay or a short interval. If the problem persists, contact AWS support.</p>", "exception": true, "fault": true}, "Invitation": {"type": "structure", "required": ["Message", "Payload", "Receiver"], "members": {"Message": {"shape": "InvitationMessage", "documentation": "<p> A message accompanying the invitation. </p>"}, "Payload": {"shape": "Payload"}, "Receiver": {"shape": "Receiver"}}, "documentation": "<p> The Invitation structure represents an invitation exchanged between partners and AWS. It includes a message, receiver information, and a payload providing context for the invitation. </p>"}, "InvitationMessage": {"type": "string", "max": 255, "min": 1, "sensitive": true}, "InvitationStatus": {"type": "string", "enum": ["ACCEPTED", "PENDING", "REJECTED", "EXPIRED"]}, "InvitationStatusList": {"type": "list", "member": {"shape": "InvitationStatus"}, "max": 10, "min": 1}, "InvolvementTypeChangeReason": {"type": "string", "enum": ["Expansion Opportunity", "Change in Deal Information", "Customer Requested", "Technical Complexity", "Risk Mitigation"]}, "JobTitle": {"type": "string", "max": 80, "min": 0, "sensitive": true}, "LastModifiedDate": {"type": "structure", "members": {"AfterLastModifiedDate": {"shape": "DateTime", "documentation": "<p>Specifies the date after which the opportunities were modified. Use this filter to retrieve only those opportunities that were modified after a given timestamp.</p>"}, "BeforeLastModifiedDate": {"shape": "DateTime", "documentation": "<p>Specifies the date before which the opportunities were modified. Use this filter to retrieve only those opportunities that were modified before a given timestamp.</p>"}}, "documentation": "<p>Defines a filter to retrieve opportunities based on the last modified date. This filter is useful for tracking changes or updates to opportunities over time.</p>"}, "LifeCycle": {"type": "structure", "members": {"ClosedLostReason": {"shape": "ClosedLostReason", "documentation": "<p>Specifies the reason code when an opportunity is marked as <i>Closed Lost</i>. When you select an appropriate reason code, you communicate the context for closing the <code>Opportunity</code>, and aid in accurate reports and analysis of opportunity outcomes. The possible values are:</p> <ul> <li> <p>Customer Deficiency: The customer lacked necessary resources or capabilities.</p> </li> <li> <p>Delay/Cancellation of Project: The project was delayed or canceled.</p> </li> <li> <p>Legal/Tax/Regulatory: Legal, tax, or regulatory issues prevented progress.</p> </li> <li> <p>Lost to Competitor—Google: The opportunity was lost to Google.</p> </li> <li> <p>Lost to Competitor—Microsoft: The opportunity was lost to Microsoft.</p> </li> <li> <p>Lost to Competitor—SoftLayer: The opportunity was lost to SoftLayer.</p> </li> <li> <p>Lost to Competitor—VMWare: The opportunity was lost to VMWare.</p> </li> <li> <p>Lost to Competitor—Other: The opportunity was lost to a competitor not listed above.</p> </li> <li> <p>No Opportunity: There was no opportunity to pursue.</p> </li> <li> <p>On Premises Deployment: The customer chose an on-premises solution.</p> </li> <li> <p>Partner Gap: The partner lacked necessary resources or capabilities.</p> </li> <li> <p>Price: The price was not competitive or acceptable to the customer.</p> </li> <li> <p>Security/Compliance: Security or compliance issues prevented progress.</p> </li> <li> <p>Technical Limitations: Technical limitations prevented progress.</p> </li> <li> <p>Customer Experience: Issues related to the customer's experience impacted the decision.</p> </li> <li> <p>Other: Any reason not covered by the other values.</p> </li> <li> <p>People/Relationship/Governance: Issues related to people, relationships, or governance.</p> </li> <li> <p>Product/Technology: Issues related to the product or technology.</p> </li> <li> <p>Financial/Commercial: Financial or commercial issues impacted the decision.</p> </li> </ul>"}, "NextSteps": {"shape": "LifeCycleNextStepsString", "documentation": "<p>Specifies the upcoming actions or tasks for the <code>Opportunity</code>. Use this field to communicate with Amazon Web Services about the next actions required for the <code>Opportunity</code>.</p>"}, "NextStepsHistory": {"shape": "LifeCycleNextStepsHistoryList", "documentation": "<p>Captures a chronological record of the next steps or actions planned or taken for the current opportunity, along with the timestamp.</p>"}, "ReviewComments": {"shape": "String", "documentation": "<p>Indicates why an opportunity was sent back for further details. Partners must take corrective action based on the <code>ReviewComments</code>.</p>"}, "ReviewStatus": {"shape": "ReviewStatus", "documentation": "<p>Indicates the review status of an opportunity referred by a partner. This field is read-only and only applicable for partner referrals. The possible values are:</p> <ul> <li> <p>Pending Submission: Not submitted for validation (editable).</p> </li> <li> <p>Submitted: Submitted for validation, and Amazon Web Services hasn't reviewed it (read-only).</p> </li> <li> <p>In Review: Amazon Web Services is validating (read-only).</p> </li> <li> <p>Action Required: Issues that Amazon Web Services highlights need to be addressed. Partners should use the <code>UpdateOpportunity</code> API action to update the opportunity and helps to ensure that all required changes are made. Only the following fields are editable when the <code>Lifecycle.ReviewStatus</code> is <code>Action Required</code>:</p> <ul> <li> <p>Customer.Account.Address.City</p> </li> <li> <p>Customer.Account.Address.CountryCode</p> </li> <li> <p>Customer.Account.Address.PostalCode</p> </li> <li> <p>Customer.Account.Address.StateOrRegion</p> </li> <li> <p>Customer.Account.Address.StreetAddress</p> </li> <li> <p>Customer.Account.WebsiteUrl</p> </li> <li> <p>LifeCycle.TargetCloseDate</p> </li> <li> <p>Project.ExpectedMonthlyAWSRevenue.Amount</p> </li> <li> <p>Project.ExpectedMonthlyAWSRevenue.CurrencyCode</p> </li> <li> <p>Project.CustomerBusinessProblem</p> </li> <li> <p>PartnerOpportunityIdentifier</p> </li> </ul> <p>After updates, the opportunity re-enters the validation phase. This process repeats until all issues are resolved, and the opportunity's <code>Lifecycle.ReviewStatus</code> is set to <code>Approved</code> or <code>Rejected</code>.</p> </li> <li> <p>Approved: Validated and converted into the Amazon Web Services seller's pipeline (editable).</p> </li> <li> <p>Rejected: Disqualified (read-only).</p> </li> </ul>"}, "ReviewStatusReason": {"shape": "String", "documentation": "<p>Indicates the reason a decision was made during the opportunity review process. This field combines the reasons for both disqualified and action required statuses, and provide clarity for why an opportunity was disqualified or requires further action.</p>"}, "Stage": {"shape": "Stage", "documentation": "<p>Specifies the current stage of the <code>Opportunity</code>'s lifecycle as it maps to Amazon Web Services stages from the current stage in the partner CRM. This field provides a translated value of the stage, and offers insight into the <code>Opportunity</code>'s progression in the sales cycle, according to Amazon Web Services definitions.</p> <note> <p>A lead and a prospect must be further matured to a <code>Qualified</code> opportunity before submission. Opportunities that were closed/lost before submission aren't suitable for submission.</p> </note> <p>The descriptions of each sales stage are:</p> <ul> <li> <p>Prospect: Amazon Web Services identifies the opportunity. It can be active (Comes directly from the end customer through a lead) or latent (Your account team believes it exists based on research, account plans, sales plays).</p> </li> <li> <p>Qualified: Your account team engaged with the customer to discuss viability and requirements. The customer agreed that the opportunity is real, of interest, and may solve business/technical needs.</p> </li> <li> <p>Technical Validation: All parties understand the implementation plan.</p> </li> <li> <p>Business Validation: Pricing was proposed, and all parties agree to the steps to close.</p> </li> <li> <p>Committed: The customer signed the contract, but Amazon Web Services hasn't started billing.</p> </li> <li> <p>Launched: The workload is complete, and Amazon Web Services has started billing.</p> </li> <li> <p>Closed Lost: The opportunity is lost, and there are no steps to move forward.</p> </li> </ul>"}, "TargetCloseDate": {"shape": "Date", "documentation": "<p>Specifies the date when Amazon Web Services expects to start significant billing, when the project finishes, and when it moves into production. This field informs the Amazon Web Services seller about when the opportunity launches and starts to incur Amazon Web Services usage.</p> <p>Ensure the <code>Target Close Date</code> isn't in the past.</p>"}}, "documentation": "<p>An object that contains the <code>Opportunity</code> lifecycle's details.</p>"}, "LifeCycleForView": {"type": "structure", "members": {"NextSteps": {"shape": "LifeCycleForViewNextStepsString", "documentation": "<p> Describes the next steps for the opportunity shared through a snapshot. </p>"}, "ReviewStatus": {"shape": "ReviewStatus", "documentation": "<p> Defines the approval status of the opportunity shared through a snapshot. </p>"}, "Stage": {"shape": "Stage", "documentation": "<p> Defines the current stage of the opportunity shared through a snapshot. </p>"}, "TargetCloseDate": {"shape": "Date", "documentation": "<p> The projected launch date of the opportunity shared through a snapshot. </p>"}}, "documentation": "<p> Provides the lifecycle view of an opportunity resource shared through a snapshot. </p>"}, "LifeCycleForViewNextStepsString": {"type": "string", "max": 255, "min": 0, "sensitive": true}, "LifeCycleNextStepsHistoryList": {"type": "list", "member": {"shape": "NextStepsHistory"}, "max": 50, "min": 0}, "LifeCycleNextStepsString": {"type": "string", "max": 255, "min": 0, "sensitive": true}, "LifeCycleSummary": {"type": "structure", "members": {"ClosedLostReason": {"shape": "ClosedLostReason", "documentation": "<p>Specifies the reason code when an opportunity is marked as <i>Closed Lost</i>. When you select an appropriate reason code, you communicate the context for closing the <code>Opportunity</code>, and aid in accurate reports and analysis of opportunity outcomes.</p>"}, "NextSteps": {"shape": "LifeCycleSummaryNextStepsString", "documentation": "<p>Specifies the upcoming actions or tasks for the <code>Opportunity</code>. This field is utilized to communicate to Amazon Web Services the next actions required for the <code>Opportunity</code>.</p>"}, "ReviewComments": {"shape": "String", "documentation": "<p>Indicates why an opportunity was sent back for further details. Partners must take corrective action based on the <code>ReviewComments</code>.</p>"}, "ReviewStatus": {"shape": "ReviewStatus", "documentation": "<p>Indicates the review status of a partner referred opportunity. This field is read-only and only applicable for partner referrals. Valid values:</p> <ul> <li> <p>Pending Submission: Not submitted for validation (editable).</p> </li> <li> <p>Submitted: Submitted for validation and not yet Amazon Web Services reviewed (read-only).</p> </li> <li> <p>In Review: Undergoing Amazon Web Services validation (read-only).</p> </li> <li> <p>Action Required: Address any issues Amazon Web Services highlights. Use the <code>UpdateOpportunity</code> API action to update the opportunity, and ensure you make all required changes. Only these fields are editable when the <code>Lifecycle.ReviewStatus</code> is <code>Action Required</code>:</p> <ul> <li> <p>Customer.Account.Address.City</p> </li> <li> <p>Customer.Account.Address.CountryCode</p> </li> <li> <p>Customer.Account.Address.PostalCode</p> </li> <li> <p>Customer.Account.Address.StateOrRegion</p> </li> <li> <p>Customer.Account.Address.StreetAddress</p> </li> <li> <p>Customer.Account.WebsiteUrl</p> </li> <li> <p>LifeCycle.TargetCloseDate</p> </li> <li> <p>Project.ExpectedCustomerSpend.Amount</p> </li> <li> <p>Project.ExpectedCustomerSpend.CurrencyCode</p> </li> <li> <p>Project.CustomerBusinessProblem</p> </li> <li> <p>PartnerOpportunityIdentifier</p> </li> </ul> <p>After updates, the opportunity re-enters the validation phase. This process repeats until all issues are resolved, and the opportunity's <code>Lifecycle.ReviewStatus</code> is set to <code>Approved</code> or <code>Rejected</code>.</p> </li> <li> <p>Approved: Validated and converted into the Amazon Web Services seller's pipeline (editable).</p> </li> <li> <p>Rejected: Disqualified (read-only).</p> </li> </ul>"}, "ReviewStatusReason": {"shape": "String", "documentation": "<p>Indicates the reason a specific decision was taken during the opportunity review process. This field combines the reasons for both disqualified and action required statuses, and provides clarity for why an opportunity was disqualified or required further action.</p>"}, "Stage": {"shape": "Stage", "documentation": "<p>Specifies the current stage of the <code>Opportunity</code>'s lifecycle as it maps to Amazon Web Services stages from the current stage in the partner CRM. This field provides a translated value of the stage, and offers insight into the <code>Opportunity</code>'s progression in the sales cycle, according to Amazon Web Services definitions.</p> <note> <p>A lead and a prospect must be further matured to a <code>Qualified</code> opportunity before submission. Opportunities that were closed/lost before submission aren't suitable for submission.</p> </note> <p>The descriptions of each sales stage are:</p> <ul> <li> <p>Prospect: Amazon Web Services identifies the opportunity. It can be active (Comes directly from the end customer through a lead) or latent (Your account team believes it exists based on research, account plans, sales plays).</p> </li> <li> <p>Qualified: Your account team engaged with the customer to discuss viability and understand requirements. The customer agreed that the opportunity is real, of interest, and may solve business/technical needs.</p> </li> <li> <p>Technical Validation: All parties understand the implementation plan.</p> </li> <li> <p>Business Validation: Pricing was proposed, and all parties agree to the steps to close.</p> </li> <li> <p>Committed: The customer signed the contract, but Amazon Web Services hasn't started billing.</p> </li> <li> <p>Launched: The workload is complete, and Amazon Web Services has started billing.</p> </li> <li> <p>Closed Lost: The opportunity is lost, and there are no steps to move forward.</p> </li> </ul>"}, "TargetCloseDate": {"shape": "Date", "documentation": "<p>Specifies the date when Amazon Web Services expects to start significant billing, when the project finishes, and when it moves into production. This field informs the Amazon Web Services seller about when the opportunity launches and starts to incur Amazon Web Services usage.</p> <p>Ensure the <code>Target Close Date</code> isn't in the past.</p>"}}, "documentation": "<p>An object that contains a <code>LifeCycle</code> object's subset of fields.</p>"}, "LifeCycleSummaryNextStepsString": {"type": "string", "max": 255, "min": 0, "sensitive": true}, "ListEngagementByAcceptingInvitationTaskSummaries": {"type": "list", "member": {"shape": "ListEngagementByAcceptingInvitationTaskSummary"}}, "ListEngagementByAcceptingInvitationTaskSummary": {"type": "structure", "members": {"EngagementInvitationId": {"shape": "EngagementInvitationIdentifier", "documentation": "<p> The unique identifier of the engagement invitation that was accepted. </p>"}, "Message": {"shape": "String", "documentation": "<p> Detailed message describing the failure and possible recovery steps. </p>"}, "OpportunityId": {"shape": "OpportunityIdentifier", "documentation": "<p> Unique identifier of opportunity that was created. </p>"}, "ReasonCode": {"shape": "ReasonCode", "documentation": "<p> A code pointing to the specific reason for the failure. </p>"}, "ResourceSnapshotJobId": {"shape": "ResourceSnapshotJobIdentifier", "documentation": "<p> Unique identifier of the resource snapshot job that was created. </p>"}, "StartTime": {"shape": "DateTime", "documentation": "<p> Task start timestamp. </p>"}, "TaskArn": {"shape": "TaskArn", "documentation": "<p> The Amazon Resource Name (ARN) that uniquely identifies the task. </p>"}, "TaskId": {"shape": "TaskIdentifier", "documentation": "<p> Unique identifier of the task. </p>"}, "TaskStatus": {"shape": "TaskStatus", "documentation": "<p> Status of the task. </p>"}}, "documentation": "<p> Specifies a subset of fields associated with tasks related to accepting an engagement invitation. </p>"}, "ListEngagementByAcceptingInvitationTasksRequest": {"type": "structure", "required": ["Catalog"], "members": {"Catalog": {"shape": "CatalogIdentifier", "documentation": "<p> Specifies the catalog related to the request. Valid values are: </p> <ul> <li> <p> AWS: Retrieves the request from the production AWS environment. </p> </li> <li> <p> Sandbox: Retrieves the request from a sandbox environment used for testing or development purposes. </p> </li> </ul>"}, "EngagementInvitationIdentifier": {"shape": "EngagementInvitationIdentifiers", "documentation": "<p> Filters tasks by the identifiers of the engagement invitations they are processing. </p>"}, "MaxResults": {"shape": "ListEngagementByAcceptingInvitationTasksRequestMaxResultsInteger", "documentation": "<p> Use this parameter to control the number of items returned in each request, which can be useful for performance tuning and managing large result sets. </p>"}, "NextToken": {"shape": "ListEngagementByAcceptingInvitationTasksRequestNextTokenString", "documentation": "<p> Use this parameter for pagination when the result set spans multiple pages. This value is obtained from the NextToken field in the response of a previous call to this API. </p>"}, "OpportunityIdentifier": {"shape": "OpportunityIdentifiers", "documentation": "<p> Filters tasks by the identifiers of the opportunities they created or are associated with. </p>"}, "Sort": {"shape": "ListTasksSortBase", "documentation": "<p> Specifies the sorting criteria for the returned results. This allows you to order the tasks based on specific attributes. </p>"}, "TaskIdentifier": {"shape": "TaskIdentifiers", "documentation": "<p> Filters tasks by their unique identifiers. Use this when you want to retrieve information about specific tasks. </p>"}, "TaskStatus": {"shape": "TaskStatuses", "documentation": "<p> Filters the tasks based on their current status. This allows you to focus on tasks in specific states. </p>"}}}, "ListEngagementByAcceptingInvitationTasksRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 1000, "min": 1}, "ListEngagementByAcceptingInvitationTasksRequestNextTokenString": {"type": "string", "max": 2048, "min": 1}, "ListEngagementByAcceptingInvitationTasksResponse": {"type": "structure", "members": {"NextToken": {"shape": "String", "documentation": "<p> A token used for pagination to retrieve the next page of results.If there are more results available, this field will contain a token that can be used in a subsequent API call to retrieve the next page. If there are no more results, this field will be null or an empty string. </p>"}, "TaskSummaries": {"shape": "ListEngagementByAcceptingInvitationTaskSummaries", "documentation": "<p> An array of <code>EngagementByAcceptingInvitationTaskSummary</code> objects, each representing a task that matches the specified filters. The array may be empty if no tasks match the criteria. </p>"}}}, "ListEngagementFromOpportunityTaskSummaries": {"type": "list", "member": {"shape": "ListEngagementFromOpportunityTaskSummary"}}, "ListEngagementFromOpportunityTaskSummary": {"type": "structure", "members": {"EngagementId": {"shape": "EngagementIdentifier", "documentation": "<p> The unique identifier of the engagement created as a result of the task. This field is populated when the task is completed successfully. </p>"}, "EngagementInvitationId": {"shape": "EngagementInvitationIdentifier", "documentation": "<p>The unique identifier of the Engagement Invitation.</p>"}, "Message": {"shape": "String", "documentation": "<p> A detailed message providing additional information about the task, especially useful in case of failures. This field may contain error details or other relevant information about the task's execution </p>"}, "OpportunityId": {"shape": "OpportunityIdentifier", "documentation": "<p> The unique identifier of the original Opportunity from which the Engagement is being created. This field helps track the source of the Engagement creation task. </p>"}, "ReasonCode": {"shape": "ReasonCode", "documentation": "<p> A code indicating the specific reason for a task failure. This field is populated when the task status is FAILED and provides a categorized reason for the failure. </p>"}, "ResourceSnapshotJobId": {"shape": "ResourceSnapshotJobIdentifier", "documentation": "<p> The identifier of the resource snapshot job associated with this task, if a snapshot was created as part of the Engagement creation process. </p>"}, "StartTime": {"shape": "DateTime", "documentation": "<p> The timestamp indicating when the task was initiated, in RFC 3339 5.6 date-time format. </p>"}, "TaskArn": {"shape": "TaskArn", "documentation": "<p> The Amazon Resource Name (ARN) uniquely identifying this task within AWS. This ARN can be used for referencing the task in other AWS services or APIs. </p>"}, "TaskId": {"shape": "TaskIdentifier", "documentation": "<p> A unique identifier for a specific task. </p>"}, "TaskStatus": {"shape": "TaskStatus", "documentation": "<p> The current status of the task. </p>"}}, "documentation": "<p> Provides a summary of a task related to creating an engagement from an opportunity. This structure contains key information about the task's status, associated identifiers, and any failure details. </p>"}, "ListEngagementFromOpportunityTasksRequest": {"type": "structure", "required": ["Catalog"], "members": {"Catalog": {"shape": "CatalogIdentifier", "documentation": "<p> Specifies the catalog related to the request. Valid values are: </p> <ul> <li> <p> AWS: Retrieves the request from the production AWS environment. </p> </li> <li> <p> Sandbox: Retrieves the request from a sandbox environment used for testing or development purposes. </p> </li> </ul>"}, "EngagementIdentifier": {"shape": "EngagementIdentifiers", "documentation": "<p> Filters tasks by the identifiers of the engagements they created or are associated with. </p>"}, "MaxResults": {"shape": "ListEngagementFromOpportunityTasksRequestMaxResultsInteger", "documentation": "<p> Specifies the maximum number of results to return in a single page of the response.Use this parameter to control the number of items returned in each request, which can be useful for performance tuning and managing large result sets. </p>"}, "NextToken": {"shape": "ListEngagementFromOpportunityTasksRequestNextTokenString", "documentation": "<p> The token for requesting the next page of results. This value is obtained from the NextToken field in the response of a previous call to this API. Use this parameter for pagination when the result set spans multiple pages. </p>"}, "OpportunityIdentifier": {"shape": "OpportunityIdentifiers", "documentation": "<p> The identifier of the original opportunity associated with this task. </p>"}, "Sort": {"shape": "ListTasksSortBase", "documentation": "<p> Specifies the sorting criteria for the returned results. This allows you to order the tasks based on specific attributes. </p>"}, "TaskIdentifier": {"shape": "TaskIdentifiers", "documentation": "<p> Filters tasks by their unique identifiers. Use this when you want to retrieve information about specific tasks. </p>"}, "TaskStatus": {"shape": "TaskStatuses", "documentation": "<p> Filters the tasks based on their current status. This allows you to focus on tasks in specific states. </p>"}}}, "ListEngagementFromOpportunityTasksRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 1000, "min": 1}, "ListEngagementFromOpportunityTasksRequestNextTokenString": {"type": "string", "max": 2048, "min": 1}, "ListEngagementFromOpportunityTasksResponse": {"type": "structure", "members": {"NextToken": {"shape": "String", "documentation": "<p> A token used for pagination to retrieve the next page of results. If there are more results available, this field will contain a token that can be used in a subsequent API call to retrieve the next page. If there are no more results, this field will be null or an empty string. </p>"}, "TaskSummaries": {"shape": "ListEngagementFromOpportunityTaskSummaries", "documentation": "<p> TaskSummaries An array of TaskSummary objects containing details about each task. </p>"}}}, "ListEngagementInvitationsRequest": {"type": "structure", "required": ["Catalog", "ParticipantType"], "members": {"Catalog": {"shape": "CatalogIdentifier", "documentation": "<p>Specifies the catalog from which to list the engagement invitations. Use <code>AWS</code> for production invitations or <code>Sandbox</code> for testing environments.</p>"}, "EngagementIdentifier": {"shape": "EngagementIdentifiers", "documentation": "<p> Retrieves a list of engagement invitation summaries based on specified filters. The ListEngagementInvitations operation allows you to view all invitations that you have sent or received. You must specify the ParticipantType to filter invitations where you are either the SENDER or the RECEIVER. Invitations will automatically expire if not accepted within 15 days. </p>"}, "MaxResults": {"shape": "PageSize", "documentation": "<p>Specifies the maximum number of engagement invitations to return in the response. If more results are available, a pagination token will be provided.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>A pagination token used to retrieve additional pages of results when the response to a previous request was truncated. Pass this token to continue listing invitations from where the previous call left off.</p>"}, "ParticipantType": {"shape": "ParticipantType", "documentation": "<p>Specifies the type of participant for which to list engagement invitations. Identifies the role of the participant.</p>"}, "PayloadType": {"shape": "EngagementInvitationsPayloadType", "documentation": "<p>Defines the type of payload associated with the engagement invitations to be listed. The attributes in this payload help decide on acceptance or rejection of the invitation.</p>"}, "SenderAwsAccountId": {"shape": "AwsAccountIdOrAliasList", "documentation": "<p> List of sender AWS account IDs to filter the invitations. </p>"}, "Sort": {"shape": "OpportunityEngagementInvitationSort", "documentation": "<p>Specifies the sorting options for listing engagement invitations. Invitations can be sorted by fields such as <code>InvitationDate</code> or <code>Status</code> to help partners view results in their preferred order.</p>"}, "Status": {"shape": "InvitationStatusList", "documentation": "<p> Status values to filter the invitations. </p>"}}}, "ListEngagementInvitationsResponse": {"type": "structure", "members": {"EngagementInvitationSummaries": {"shape": "EngagementInvitationSummaries", "documentation": "<p>An array containing summaries of engagement invitations. Each summary includes information such as the invitation title, invitation date, and the current status of the invitation.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>A pagination token returned when there are more results available than can be returned in a single call. Use this token to retrieve additional pages of engagement invitation summaries.</p>"}}}, "ListEngagementMembersRequest": {"type": "structure", "required": ["Catalog", "Identifier"], "members": {"Catalog": {"shape": "CatalogIdentifier", "documentation": "<p>The catalog related to the request.</p>"}, "Identifier": {"shape": "EngagementArnOrIdentifier", "documentation": "<p>Identifier of the Engagement record to retrieve members from.</p>"}, "MaxResults": {"shape": "MemberPageSize", "documentation": "<p>The maximum number of results to return in a single call.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The token for the next set of results.</p>"}}}, "ListEngagementMembersResponse": {"type": "structure", "required": ["EngagementMemberList"], "members": {"EngagementMemberList": {"shape": "EngagementMembers", "documentation": "<p> Provides a list of engagement members. </p>"}, "NextToken": {"shape": "String", "documentation": "<p>A pagination token used to retrieve the next set of results. If there are more results available than can be returned in a single response, this token will be present. Use this token in a subsequent request to retrieve the next page of results. If there are no more results, this value will be null. </p>"}}}, "ListEngagementResourceAssociationsRequest": {"type": "structure", "required": ["Catalog"], "members": {"Catalog": {"shape": "CatalogIdentifier", "documentation": "<p>Specifies the catalog in which to search for engagement-resource associations. Valid Values: \"AWS\" or \"Sandbox\"</p> <ul> <li> <p> <code>AWS</code> for production environments.</p> </li> <li> <p> <code>Sandbox</code> for testing and development purposes.</p> </li> </ul>"}, "CreatedBy": {"shape": "AwsAccount", "documentation": "<p>Filters the response to include only snapshots of resources owned by the specified AWS account ID. Use this when you want to find associations related to resources owned by a particular account. </p>"}, "EngagementIdentifier": {"shape": "EngagementIdentifier", "documentation": "<p>Filters the results to include only associations related to the specified engagement. Use this when you want to find all resources associated with a specific engagement.</p>"}, "MaxResults": {"shape": "ListEngagementResourceAssociationsRequestMaxResultsInteger", "documentation": "<p>Limits the number of results returned in a single call. Use this to control the number of results returned, especially useful for pagination.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>A token used for pagination of results. Include this token in subsequent requests to retrieve the next set of results.</p>"}, "ResourceIdentifier": {"shape": "ResourceIdentifier", "documentation": "<p>Filters the results to include only associations with the specified resource. Varies depending on the resource type. Use this when you want to find all engagements associated with a specific resource.</p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p> Filters the results to include only associations with resources of the specified type. </p>"}}}, "ListEngagementResourceAssociationsRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 1000, "min": 1}, "ListEngagementResourceAssociationsResponse": {"type": "structure", "required": ["EngagementResourceAssociationSummaries"], "members": {"EngagementResourceAssociationSummaries": {"shape": "EngagementResourceAssociationSummaryList", "documentation": "<p> A list of engagement-resource association summaries. </p>"}, "NextToken": {"shape": "String", "documentation": "<p> A token to retrieve the next set of results. Use this token in a subsequent request to retrieve additional results if the response was truncated. </p>"}}}, "ListEngagementsRequest": {"type": "structure", "required": ["Catalog"], "members": {"Catalog": {"shape": "CatalogIdentifier", "documentation": "<p> Specifies the catalog related to the request. </p>"}, "CreatedBy": {"shape": "AwsAccountList", "documentation": "<p> A list of AWS account IDs. When specified, the response includes engagements created by these accounts. This filter is useful for finding engagements created by specific team members. </p>"}, "EngagementIdentifier": {"shape": "EngagementIdentifiers", "documentation": "<p>An array of strings representing engagement identifiers to retrieve.</p>"}, "ExcludeCreatedBy": {"shape": "AwsAccountList", "documentation": "<p>An array of strings representing AWS Account IDs. Use this to exclude engagements created by specific users. </p>"}, "MaxResults": {"shape": "EngagementPageSize", "documentation": "<p>The maximum number of results to return in a single call.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The token for the next set of results. This value is returned from a previous call.</p>"}, "Sort": {"shape": "EngagementSort"}}}, "ListEngagementsResponse": {"type": "structure", "required": ["EngagementSummaryList"], "members": {"EngagementSummaryList": {"shape": "EngagementSummaryList", "documentation": "<p>An array of engagement summary objects.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The token to retrieve the next set of results. This field will be null if there are no more results. </p>"}}}, "ListOpportunitiesRequest": {"type": "structure", "required": ["Catalog"], "members": {"Catalog": {"shape": "CatalogIdentifier", "documentation": "<p>Specifies the catalog associated with the request. This field takes a string value from a predefined list: <code>AWS</code> or <code>Sandbox</code>. The catalog determines which environment the opportunities are listed in. Use <code>AWS</code> for listing real opportunities in the Amazon Web Services catalog, and <code>Sandbox</code> for testing in secure, isolated environments.</p>"}, "CustomerCompanyName": {"shape": "ListOpportunitiesRequestCustomerCompanyNameList", "documentation": "<p>Filters the opportunities based on the customer's company name. This allows partners to search for opportunities associated with a specific customer by matching the provided company name string.</p>"}, "Identifier": {"shape": "ListOpportunitiesRequestIdentifierList", "documentation": "<p>Filters the opportunities based on the opportunity identifier. This allows partners to retrieve specific opportunities by providing their unique identifiers, ensuring precise results.</p>"}, "LastModifiedDate": {"shape": "LastModifiedDate", "documentation": "<p>Filters the opportunities based on their last modified date. This filter helps retrieve opportunities that were updated after the specified date, allowing partners to track recent changes or updates.</p>"}, "LifeCycleReviewStatus": {"shape": "ListOpportunitiesRequestLifeCycleReviewStatusList", "documentation": "<p>Filters the opportunities based on their current lifecycle approval status. Use this filter to retrieve opportunities with statuses such as <code>Pending Submission</code>, <code>In Review</code>, <code>Action Required</code>, or <code>Approved</code>.</p>"}, "LifeCycleStage": {"shape": "ListOpportunitiesRequestLifeCycleStageList", "documentation": "<p>Filters the opportunities based on their lifecycle stage. This filter allows partners to retrieve opportunities at various stages in the sales cycle, such as <code>Qualified</code>, <code>Technical Validation</code>, <code>Business Validation</code>, or <code>Closed Won</code>.</p>"}, "MaxResults": {"shape": "PageSize", "documentation": "<p>Specifies the maximum number of results to return in a single call. This limits the number of opportunities returned in the response to avoid providing too many results at once.</p> <p>Default: 20</p>"}, "NextToken": {"shape": "String", "documentation": "<p>A pagination token used to retrieve the next set of results in subsequent calls. This token is included in the response only if there are additional result pages available.</p>"}, "Sort": {"shape": "OpportunitySort", "documentation": "<p>An object that specifies how the response is sorted. The default <code>Sort.SortBy</code> value is <code>LastModifiedDate</code>.</p>"}}}, "ListOpportunitiesRequestCustomerCompanyNameList": {"type": "list", "member": {"shape": "String"}, "max": 10, "min": 0}, "ListOpportunitiesRequestIdentifierList": {"type": "list", "member": {"shape": "OpportunityIdentifier"}, "max": 20, "min": 0}, "ListOpportunitiesRequestLifeCycleReviewStatusList": {"type": "list", "member": {"shape": "ReviewStatus"}, "max": 10, "min": 0}, "ListOpportunitiesRequestLifeCycleStageList": {"type": "list", "member": {"shape": "Stage"}, "max": 10, "min": 0}, "ListOpportunitiesResponse": {"type": "structure", "required": ["OpportunitySummaries"], "members": {"NextToken": {"shape": "String", "documentation": "<p>A pagination token used to retrieve the next set of results in subsequent calls. This token is included in the response only if there are additional result pages available.</p>"}, "OpportunitySummaries": {"shape": "OpportunitySummaries", "documentation": "<p>An array that contains minimal details for opportunities that match the request criteria. This summary view provides a quick overview of relevant opportunities.</p>"}}}, "ListResourceSnapshotJobsRequest": {"type": "structure", "required": ["Catalog"], "members": {"Catalog": {"shape": "CatalogIdentifier", "documentation": "<p> Specifies the catalog related to the request. </p>"}, "EngagementIdentifier": {"shape": "EngagementIdentifier", "documentation": "<p> The identifier of the engagement to filter the response. </p>"}, "MaxResults": {"shape": "ListResourceSnapshotJobsRequestMaxResultsInteger", "documentation": "<p> The maximum number of results to return in a single call. If omitted, defaults to 50. </p>"}, "NextToken": {"shape": "String", "documentation": "<p> The token for the next set of results. </p>"}, "Sort": {"shape": "SortObject", "documentation": "<p> Configures the sorting of the response. If omitted, results are sorted by <code>CreatedDate</code> in descending order. </p>"}, "Status": {"shape": "ResourceSnapshotJobStatus", "documentation": "<p> The status of the jobs to filter the response. </p>"}}}, "ListResourceSnapshotJobsRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 1000, "min": 1}, "ListResourceSnapshotJobsResponse": {"type": "structure", "required": ["ResourceSnapshotJobSummaries"], "members": {"NextToken": {"shape": "String", "documentation": "<p> The token to retrieve the next set of results. If there are no additional results, this value is null. </p>"}, "ResourceSnapshotJobSummaries": {"shape": "ResourceSnapshotJobSummaryList", "documentation": "<p> An array of resource snapshot job summary objects. </p>"}}}, "ListResourceSnapshotsRequest": {"type": "structure", "required": ["Catalog", "EngagementIdentifier"], "members": {"Catalog": {"shape": "CatalogIdentifier", "documentation": "<p> Specifies the catalog related to the request. </p>"}, "CreatedBy": {"shape": "AwsAccount", "documentation": "<p>Filters the response to include only snapshots of resources owned by the specified AWS account. </p>"}, "EngagementIdentifier": {"shape": "EngagementIdentifier", "documentation": "<p> The unique identifier of the engagement associated with the snapshots. </p>"}, "MaxResults": {"shape": "ListResourceSnapshotsRequestMaxResultsInteger", "documentation": "<p> The maximum number of results to return in a single call. </p>"}, "NextToken": {"shape": "String", "documentation": "<p> The token for the next set of results. </p>"}, "ResourceIdentifier": {"shape": "ResourceIdentifier", "documentation": "<p> Filters the response to include only snapshots of the specified resource. </p>"}, "ResourceSnapshotTemplateIdentifier": {"shape": "ResourceTemplateName", "documentation": "<p>Filters the response to include only snapshots created using the specified template.</p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p> Filters the response to include only snapshots of the specified resource type. </p>"}}}, "ListResourceSnapshotsRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 1000, "min": 1}, "ListResourceSnapshotsResponse": {"type": "structure", "required": ["ResourceSnapshotSummaries"], "members": {"NextToken": {"shape": "String", "documentation": "<p> The token to retrieve the next set of results. If there are no additional results, this value is null. </p>"}, "ResourceSnapshotSummaries": {"shape": "ResourceSnapshotSummaryList", "documentation": "<p> An array of resource snapshot summary objects. </p>"}}}, "ListSolutionsRequest": {"type": "structure", "required": ["Catalog"], "members": {"Catalog": {"shape": "CatalogIdentifier", "documentation": "<p>Specifies the catalog associated with the request. This field takes a string value from a predefined list: <code>AWS</code> or <code>Sandbox</code>. The catalog determines which environment the solutions are listed in. Use <code>AWS</code> to list solutions in the Amazon Web Services catalog, and <code>Sandbox</code> to list solutions in a secure and isolated testing environment.</p>"}, "Category": {"shape": "ListSolutionsRequestCategoryList", "documentation": "<p>Filters the solutions based on the category to which they belong. This allows partners to search for solutions within specific categories, such as <code>Software</code>, <code>Consulting</code>, or <code>Managed Services</code>.</p>"}, "Identifier": {"shape": "ListSolutionsRequestIdentifierList", "documentation": "<p>Filters the solutions based on their unique identifier. Use this filter to retrieve specific solutions by providing the solution's identifier for accurate results.</p>"}, "MaxResults": {"shape": "PageSize", "documentation": "<p>The maximum number of results returned by a single call. This value must be provided in the next call to retrieve the next set of results.</p> <p>Default: 20</p>"}, "NextToken": {"shape": "String", "documentation": "<p>A pagination token used to retrieve the next set of results in subsequent calls. This token is included in the response only if there are additional result pages available.</p>"}, "Sort": {"shape": "SolutionSort", "documentation": "<p>Object that configures sorting done on the response. Default <code>Sort.SortBy</code> is <code>Identifier</code>.</p>"}, "Status": {"shape": "ListSolutionsRequestStatusList", "documentation": "<p>Filters solutions based on their status. This filter helps partners manage their solution portfolios effectively.</p>"}}}, "ListSolutionsRequestCategoryList": {"type": "list", "member": {"shape": "String"}, "max": 10, "min": 0}, "ListSolutionsRequestIdentifierList": {"type": "list", "member": {"shape": "SolutionIdentifier"}, "max": 20, "min": 0}, "ListSolutionsRequestStatusList": {"type": "list", "member": {"shape": "SolutionStatus"}, "max": 10, "min": 0}, "ListSolutionsResponse": {"type": "structure", "required": ["SolutionSummaries"], "members": {"NextToken": {"shape": "String", "documentation": "<p>A pagination token used to retrieve the next set of results in subsequent calls. This token is included in the response only if there are additional result pages available.</p>"}, "SolutionSummaries": {"shape": "SolutionList", "documentation": "<p>An array with minimal details for solutions matching the request criteria.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "TaggableResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource for which you want to retrieve tags.</p>"}}}, "ListTagsForResourceResponse": {"type": "structure", "required": ["Tags"], "members": {"Tags": {"shape": "TagList", "documentation": "<p>A map of the key-value pairs for the tag or tags assigned to the specified resource.</p>"}}}, "ListTasksSortBase": {"type": "structure", "required": ["SortBy", "SortOrder"], "members": {"SortBy": {"shape": "ListTasksSortName", "documentation": "<p> Specifies the field by which the task list should be sorted. </p>"}, "SortOrder": {"shape": "SortOrder", "documentation": "<p> Determines the order in which the sorted results are presented. </p>"}}, "documentation": "<p> Defines the sorting parameters for listing tasks. This structure allows for specifying the field to sort by and the order of sorting. </p>"}, "ListTasksSortName": {"type": "string", "enum": ["StartTime"]}, "Marketing": {"type": "structure", "members": {"AwsFundingUsed": {"shape": "AwsFundingUsed", "documentation": "<p>Indicates if the <code>Opportunity</code> is a marketing development fund (MDF) funded activity.</p>"}, "CampaignName": {"shape": "String", "documentation": "<p>Specifies the <code>Opportunity</code> marketing campaign code. The Amazon Web Services campaign code is a reference to specific marketing initiatives, promotions, or activities. This field captures the identifier used to track and categorize the <code>Opportunity</code> within marketing campaigns. If you don't have a campaign code, contact your Amazon Web Services point of contact to obtain one.</p>"}, "Channels": {"shape": "Channels", "documentation": "<p>Specifies the <code>Opportunity</code>'s channel that the marketing activity is associated with or was contacted through. This field provides information about the specific marketing channel that contributed to the generation of the lead or contact.</p>"}, "Source": {"shape": "MarketingSource", "documentation": "<p>Indicates if the <code>Opportunity</code> was sourced from an Amazon Web Services marketing activity. Use the value <code>Marketing Activity</code>. Use <code>None</code> if it's not associated with an Amazon Web Services marketing activity. This field helps Amazon Web Services track the return on marketing investments and enables better distribution of marketing budgets among partners.</p>"}, "UseCases": {"shape": "UseCases", "documentation": "<p>Specifies the marketing activity use case or purpose that led to the <code>Opportunity</code>'s creation or contact. This field captures the context or marketing activity's execution's intention and the direct correlation to the generated opportunity or contact. Must be empty when <code>Marketing.AWSFundingUsed = No</code>.</p> <p>Valid values: <code>AI/ML | Analytics | Application Integration | Blockchain | Business Applications | Cloud Financial Management | Compute | Containers | Customer Engagement | Databases | Developer Tools | End User Computing | Front End Web &amp; Mobile | Game Tech | IoT | Management &amp; Governance | Media Services | Migration &amp; Transfer | Networking &amp; Content Delivery | Quantum Technologies | Robotics | Satellite | Security | Serverless | Storage | VR &amp; AR</code> </p>"}}, "documentation": "<p>An object that contains marketing details for the <code>Opportunity</code>.</p>"}, "MarketingSource": {"type": "string", "enum": ["Marketing Activity", "None"]}, "MemberCompanyName": {"type": "string", "max": 120, "min": 1, "sensitive": true}, "MemberPageSize": {"type": "integer", "box": true, "max": 10, "min": 1}, "MonetaryValue": {"type": "structure", "required": ["Amount", "CurrencyCode"], "members": {"Amount": {"shape": "MonetaryValueAmountString", "documentation": "<p>Specifies the payment amount.</p>"}, "CurrencyCode": {"shape": "CurrencyCode", "documentation": "<p>Specifies the payment currency.</p>"}}, "documentation": "<p>Specifies payments details.</p>"}, "MonetaryValueAmountString": {"type": "string", "pattern": "^(0|([1-9][0-9]{0,30}))(\\.[0-9]{0,2})?$"}, "Name": {"type": "string", "max": 80, "min": 0, "sensitive": true}, "NationalSecurity": {"type": "string", "enum": ["Yes", "No"]}, "NextStepsHistory": {"type": "structure", "required": ["Time", "Value"], "members": {"Time": {"shape": "DateTime", "documentation": "<p>Indicates the step execution time.</p>"}, "Value": {"shape": "String", "documentation": "<p>Indicates the step's execution details.</p>"}}, "documentation": "<p>Read-only; shows the last 50 values and change dates for the <code>NextSteps</code> field.</p>"}, "OpportunityArn": {"type": "string", "pattern": "^arn:.*$"}, "OpportunityEngagementInvitationSort": {"type": "structure", "required": ["SortBy", "SortOrder"], "members": {"SortBy": {"shape": "OpportunityEngagementInvitationSortName", "documentation": "<p>Specifies the field by which the Engagement Invitations are sorted. Common values include <code>InvitationDate</code> and <code>Status</code>.</p>"}, "SortOrder": {"shape": "SortOrder", "documentation": "<p>Defines the order in which the Engagement Invitations are sorted. The values can be <code>ASC</code> (ascending) or <code>DESC</code> (descending).</p>"}}, "documentation": "<p>Defines sorting options for retrieving Engagement Invitations. Sorting can be done based on various criteria like the invitation date or status.</p>"}, "OpportunityEngagementInvitationSortName": {"type": "string", "enum": ["InvitationDate"]}, "OpportunityIdentifier": {"type": "string", "pattern": "^O[0-9]{1,19}$"}, "OpportunityIdentifiers": {"type": "list", "member": {"shape": "OpportunityIdentifier"}, "max": 10, "min": 1}, "OpportunityInvitationPayload": {"type": "structure", "required": ["Customer", "Project", "ReceiverResponsibilities"], "members": {"Customer": {"shape": "EngagementCustomer", "documentation": "<p>Contains information about the customer related to the opportunity in the Engagement Invitation. This data helps partners understand the customer’s profile and requirements.</p>"}, "Project": {"shape": "ProjectDetails", "documentation": "<p>Describes the project details associated with the opportunity, including the customer’s needs and the scope of work expected to be performed.</p>"}, "ReceiverResponsibilities": {"shape": "ReceiverResponsibilityList", "documentation": "<p>Outlines the responsibilities or expectations of the receiver in the context of the invitation.</p>"}, "SenderContacts": {"shape": "SenderContactList", "documentation": "<p>Represents the contact details of the AWS representatives involved in sending the Engagement Invitation. These contacts are opportunity stakeholders.</p>"}}, "documentation": "<p>Represents the data payload of an Engagement Invitation for a specific opportunity. This contains detailed information that partners use to evaluate the engagement.</p>"}, "OpportunityOrigin": {"type": "string", "enum": ["AWS Referral", "Partner Referral"]}, "OpportunitySort": {"type": "structure", "required": ["SortBy", "SortOrder"], "members": {"SortBy": {"shape": "OpportunitySortName", "documentation": "<p>Field name to sort by.</p>"}, "SortOrder": {"shape": "SortOrder", "documentation": "<p>Sort order.</p> <p>Default: <code>Descending</code> </p>"}}, "documentation": "<p>Object that configures response sorting.</p>"}, "OpportunitySortName": {"type": "string", "enum": ["LastModifiedDate", "Identifier", "CustomerCompanyName"]}, "OpportunitySummaries": {"type": "list", "member": {"shape": "OpportunitySummary"}}, "OpportunitySummary": {"type": "structure", "required": ["Catalog"], "members": {"Arn": {"shape": "OpportunityArn", "documentation": "<p> The Amazon Resource Name (ARN) for the opportunity. This globally unique identifier can be used for IAM policies and cross-service references. </p>"}, "Catalog": {"shape": "CatalogIdentifier", "documentation": "<p>Specifies the catalog associated with the opportunity, either <code>AWS</code> or <code>Sandbox</code>. This indicates the environment in which the opportunity is managed.</p>"}, "CreatedDate": {"shape": "DateTime", "documentation": "<p> <code>DateTime</code> when the <code>Opportunity</code> was last created.</p>"}, "Customer": {"shape": "Customer<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>An object that contains the <code>Opportunity</code>'s customer details.</p>"}, "Id": {"shape": "OpportunityIdentifier", "documentation": "<p>Read-only, system-generated <code>Opportunity</code> unique identifier.</p>"}, "LastModifiedDate": {"shape": "DateTime", "documentation": "<p> <code>DateTime</code> when the <code>Opportunity</code> was last modified.</p>"}, "LifeCycle": {"shape": "LifeCycleSummary", "documentation": "<p>An object that contains the <code>Opportunity</code>'s lifecycle details.</p>"}, "OpportunityType": {"shape": "OpportunityType", "documentation": "<p>Specifies opportunity type as a renewal, new, or expansion.</p> <p>Opportunity types:</p> <ul> <li> <p>New Opportunity: Represents a new business opportunity with a potential customer that's not previously engaged with your solutions or services.</p> </li> <li> <p>Renewal Opportunity: Represents an opportunity to renew an existing contract or subscription with a current customer, ensuring continuity of service.</p> </li> <li> <p>Expansion Opportunity: Represents an opportunity to expand the scope of an existing contract or subscription, either by adding new services or increasing the volume of existing services for a current customer.</p> </li> </ul>"}, "PartnerOpportunityIdentifier": {"shape": "String", "documentation": "<p>Specifies the <code>Opportunity</code>'s unique identifier in the partner's CRM system. This value is essential to track and reconcile because it's included in the outbound payload sent back to the partner. It allows partners to link an opportunity to their CRM.</p>"}, "Project": {"shape": "ProjectSummary", "documentation": "<p>An object that contains the <code>Opportunity</code>'s project details summary.</p>"}}, "documentation": "<p>An object that contains an <code>Opportunity</code>'s subset of fields.</p>"}, "OpportunitySummaryView": {"type": "structure", "members": {"Customer": {"shape": "Customer"}, "Lifecycle": {"shape": "LifeCycleForView", "documentation": "<p> Contains information about the opportunity's lifecycle, including its current stage, status, and important dates such as creation and last modification times. </p>"}, "OpportunityTeam": {"shape": "PartnerOpportunityTeamMembersList", "documentation": "<p> Represents the internal team handling the opportunity. Specify the members involved in collaborating on an opportunity within the partner's organization. </p>"}, "OpportunityType": {"shape": "OpportunityType", "documentation": "<p> Specifies the opportunity type. </p>"}, "PrimaryNeedsFromAws": {"shape": "PrimaryNeedsFromAws", "documentation": "<p> Identifies the type of support the partner needs from AWS. </p>"}, "Project": {"shape": "ProjectView", "documentation": "<p> Contains summary information about the project associated with the opportunity, including project name, description, timeline, and other relevant details. </p>"}, "RelatedEntityIdentifiers": {"shape": "RelatedEntityIdentifiers"}}, "documentation": "<p> Provides a comprehensive view of an opportunity summary, including lifecycle information, team details, opportunity type, primary needs from AWS, and associated project information. </p>"}, "OpportunityType": {"type": "string", "enum": ["Net New Business", "Flat Renewal", "Expansion"]}, "PageSize": {"type": "integer", "box": true, "max": 100, "min": 1}, "ParticipantType": {"type": "string", "enum": ["SENDER", "RECEIVER"]}, "PartnerOpportunityTeamMembersList": {"type": "list", "member": {"shape": "Contact"}, "max": 1, "min": 0}, "Payload": {"type": "structure", "members": {"OpportunityInvitation": {"shape": "OpportunityInvitationPayload", "documentation": "<p>Specifies the details of the opportunity invitation within the Engagement Invitation payload. This data helps partners understand the context, scope, and expected involvement for the opportunity from AWS.</p>"}}, "documentation": "<p>Contains the data payload associated with the Engagement Invitation. This payload includes essential details related to the AWS opportunity and is used by partners to evaluate whether to accept or reject the engagement.</p>", "union": true}, "PaymentFrequency": {"type": "string", "enum": ["Monthly"]}, "PhoneNumber": {"type": "string", "max": 40, "min": 0, "pattern": "^\\+[1-9]\\d{1,14}$", "sensitive": true}, "PrimaryNeedFromAws": {"type": "string", "enum": ["Co-Sell - Architectural Validation", "Co-Sell - Business Presentation", "Co-Sell - Competitive Information", "Co-Sell - Pricing Assistance", "Co-Sell - Technical Consultation", "Co-Sell - Total Cost of Ownership Evaluation", "Co-Sell - Deal Support", "Co-Sell - Support for Public Tender / RFx"]}, "PrimaryNeedsFromAws": {"type": "list", "member": {"shape": "PrimaryNeedFromAws"}}, "ProfileNextStepsHistory": {"type": "structure", "required": ["Time", "Value"], "members": {"Time": {"shape": "DateTime", "documentation": "<p>Indicates the date and time when a particular next step was recorded or planned. This helps in managing the timeline for the opportunity.</p>"}, "Value": {"shape": "String", "documentation": "<p>Represents the details of the next step recorded, such as follow-up actions or decisions made. This field helps in tracking progress and ensuring alignment with project goals.</p>"}}, "documentation": "<p>Tracks the history of next steps associated with the opportunity. This field captures the actions planned for the future and their timeline.</p>"}, "Project": {"type": "structure", "members": {"AdditionalComments": {"shape": "ProjectAdditionalCommentsString", "documentation": "<p>Captures additional comments or information for the <code>Opportunity</code> that weren't captured in other fields.</p>"}, "ApnPrograms": {"shape": "ApnPrograms", "documentation": "<p>Specifies the Amazon Partner Network (APN) program that influenced the <code>Opportunity</code>. APN programs refer to specific partner programs or initiatives that can impact the <code>Opportunity</code>.</p> <p>Valid values: <code>APN Immersion Days | APN Solution Space | ATO (Authority to Operate) | AWS Marketplace Campaign | IS Immersion Day SFID Program | ISV Workload Migration | Migration Acceleration Program | P3 | Partner Launch Initiative | Partner Opportunity Acceleration Funded | The Next Smart | VMware Cloud on AWS | Well-Architected | Windows | Workspaces/AppStream Accelerator Program | WWPS NDPP</code> </p>"}, "CompetitorName": {"shape": "CompetitorName", "documentation": "<p>Name of the <code>Opportunity</code>'s competitor (if any). Use <code>Other</code> to submit a value not in the picklist.</p>"}, "CustomerBusinessProblem": {"shape": "ProjectCustomerBusinessProblemString", "documentation": "<p>Describes the problem the end customer has, and how the partner is helping. Utilize this field to provide a concise narrative that outlines the customer's business challenge or issue. Elaborate on how the partner's solution or offerings align to resolve the customer's business problem. Include relevant information about the partner's value proposition, unique selling points, and expertise to tackle the issue. Offer insights on how the proposed solution meets the customer's needs and provides value. Use concise language and precise descriptions to convey the context and significance of the <code>Opportunity</code>. The content in this field helps Amazon Web Services understand the nature of the <code>Opportunity</code> and the strategic fit of the partner's solution.</p>"}, "CustomerUseCase": {"shape": "String", "documentation": "<p>Specifies the proposed solution focus or type of workload for the Opportunity. This field captures the primary use case or objective of the proposed solution, and provides context and clarity to the addressed workload.</p> <p>Valid values: <code>AI Machine Learning and Analytics | Archiving | Big Data: Data Warehouse/Data Integration/ETL/Data Lake/BI | Blockchain | Business Applications: Mainframe Modernization | Business Applications &amp; Contact Center | Business Applications &amp; SAP Production | Centralized Operations Management | Cloud Management Tools | Cloud Management Tools &amp; DevOps with Continuous Integration &amp; Continuous Delivery (CICD) | Configuration, Compliance &amp; Auditing | Connected Services | Containers &amp; Serverless | Content Delivery &amp; Edge Services | Database | Edge Computing/End User Computing | Energy | Enterprise Governance &amp; Controls | Enterprise Resource Planning | Financial Services | Healthcare and Life Sciences | High Performance Computing | Hybrid Application Platform | Industrial Software | IOT | Manufacturing, Supply Chain and Operations | Media &amp; High performance computing (HPC) | Migration/Database Migration | Monitoring, logging and performance | Monitoring &amp; Observability | Networking | Outpost | SAP | Security &amp; Compliance | Storage &amp; Backup | Training | VMC | VMWare | Web development &amp; DevOps</code> </p>"}, "DeliveryModels": {"shape": "DeliveryModels", "documentation": "<p>Specifies the deployment or consumption model for your solution or service in the <code>Opportunity</code>'s context. You can select multiple options.</p> <p>Options' descriptions from the <code>Delivery Model</code> field are:</p> <ul> <li> <p>SaaS or PaaS: Your Amazon Web Services based solution deployed as SaaS or PaaS in your Amazon Web Services environment.</p> </li> <li> <p>BYOL or AMI: Your Amazon Web Services based solution deployed as BYOL or AMI in the end customer's Amazon Web Services environment.</p> </li> <li> <p>Managed Services: The end customer's Amazon Web Services business management (For example: Consulting, design, implementation, billing support, cost optimization, technical support).</p> </li> <li> <p>Professional Services: Offerings to help enterprise end customers achieve specific business outcomes for enterprise cloud adoption (For example: Advisory or transformation planning).</p> </li> <li> <p>Resell: Amazon Web Services accounts and billing management for your customers.</p> </li> <li> <p>Other: Delivery model not described above.</p> </li> </ul>"}, "ExpectedCustomerSpend": {"shape": "ExpectedCustomerSpendList", "documentation": "<p>Represents the estimated amount that the customer is expected to spend on AWS services related to the opportunity. This helps in evaluating the potential financial value of the opportunity for AWS.</p>"}, "OtherCompetitorNames": {"shape": "ProjectOtherCompetitorNamesString", "documentation": "<p>Only allowed when <code>CompetitorNames</code> has <code>Other</code> selected.</p>"}, "OtherSolutionDescription": {"shape": "ProjectOtherSolutionDescriptionString", "documentation": "<p>Specifies the offered solution for the customer's business problem when the <code> RelatedEntityIdentifiers.Solutions</code> field value is <code>Other</code>.</p>"}, "RelatedOpportunityIdentifier": {"shape": "OpportunityIdentifier", "documentation": "<p>Specifies the current opportunity's parent opportunity identifier.</p>"}, "SalesActivities": {"shape": "SalesActivities", "documentation": "<p>Specifies the <code>Opportunity</code>'s sales activities conducted with the end customer. These activities help drive Amazon Web Services assignment priority.</p> <p>Valid values:</p> <ul> <li> <p>Initialized discussions with customer: Initial conversations with the customer to understand their needs and introduce your solution.</p> </li> <li> <p>Customer has shown interest in solution: After initial discussions, the customer is interested in your solution.</p> </li> <li> <p>Conducted POC/demo: You conducted a proof of concept (POC) or demonstration of the solution for the customer.</p> </li> <li> <p>In evaluation/planning stage: The customer is evaluating the solution and planning potential implementation.</p> </li> <li> <p>Agreed on solution to Business Problem: Both parties agree on how the solution addresses the customer's business problem.</p> </li> <li> <p>Completed Action Plan: A detailed action plan is complete and outlines the steps for implementation.</p> </li> <li> <p>Finalized Deployment Need: Both parties agree with and finalized the deployment needs.</p> </li> <li> <p>SOW Signed: Both parties signed a statement of work (SOW), and formalize the agreement and detail the project scope and deliverables.</p> </li> </ul>"}, "Title": {"shape": "ProjectTitleString", "documentation": "<p>Specifies the <code>Opportunity</code>'s title or name.</p>"}}, "documentation": "<p>An object that contains the <code>Opportunity</code>'s project details.</p>"}, "ProjectAdditionalCommentsString": {"type": "string", "max": 255, "min": 1}, "ProjectCustomerBusinessProblemString": {"type": "string", "max": 2000, "min": 20, "sensitive": true}, "ProjectDetails": {"type": "structure", "required": ["BusinessProblem", "ExpectedCustomerSpend", "TargetCompletionDate", "Title"], "members": {"BusinessProblem": {"shape": "EngagementCustomerBusinessProblem", "documentation": "<p>Describes the business problem that the project aims to solve. This information is crucial for understanding the project’s goals and objectives.</p>"}, "ExpectedCustomerSpend": {"shape": "ExpectedCustomerSpendList", "documentation": "<p>Contains revenue estimates for the partner related to the project. This field provides an idea of the financial potential of the opportunity for the partner.</p>"}, "TargetCompletionDate": {"shape": "Date", "documentation": "<p>Specifies the estimated date of project completion. This field helps track the project timeline and manage expectations.</p>"}, "Title": {"shape": "ProjectDetailsTitleString", "documentation": "<p>Specifies the title of the project. This title helps partners quickly identify and understand the focus of the project.</p>"}}, "documentation": "<p>Contains details about the project associated with the Engagement Invitation, including the business problem and expected outcomes.</p>"}, "ProjectDetailsTitleString": {"type": "string", "max": 255, "min": 1}, "ProjectOtherCompetitorNamesString": {"type": "string", "max": 255, "min": 0}, "ProjectOtherSolutionDescriptionString": {"type": "string", "max": 255, "min": 0, "sensitive": true}, "ProjectSummary": {"type": "structure", "members": {"DeliveryModels": {"shape": "DeliveryModels", "documentation": "<p>Specifies your solution or service's deployment or consumption model in the <code>Opportunity</code>'s context. You can select multiple options.</p> <p>Options' descriptions from the <code>Delivery Model</code> field are:</p> <ul> <li> <p>SaaS or PaaS: Your Amazon Web Services based solution deployed as SaaS or PaaS in your Amazon Web Services environment.</p> </li> <li> <p>BYOL or AMI: Your Amazon Web Services based solution deployed as BYOL or AMI in the end customer's Amazon Web Services environment.</p> </li> <li> <p>Managed Services: The end customer's Amazon Web Services business management (For example: Consulting, design, implementation, billing support, cost optimization, technical support).</p> </li> <li> <p>Professional Services: Offerings to help enterprise end customers achieve specific business outcomes for enterprise cloud adoption (For example: Advisory or transformation planning).</p> </li> <li> <p>Resell: Amazon Web Services accounts and billing management for your customers.</p> </li> <li> <p>Other: Delivery model not described above.</p> </li> </ul>"}, "ExpectedCustomerSpend": {"shape": "ExpectedCustomerSpendList", "documentation": "<p>Provides a summary of the expected customer spend for the project, offering a high-level view of the potential financial impact.</p>"}}, "documentation": "<p>An object that contains a <code>Project</code> object's subset of fields.</p>"}, "ProjectTitleString": {"type": "string", "max": 255, "min": 0, "sensitive": true}, "ProjectView": {"type": "structure", "members": {"CustomerUseCase": {"shape": "String", "documentation": "<p> Specifies the proposed solution focus or type of workload for the project. </p>"}, "DeliveryModels": {"shape": "DeliveryModels", "documentation": "<p> Describes the deployment or consumption model for the partner solution or offering. This field indicates how the project's solution will be delivered or implemented for the customer. </p>"}, "ExpectedCustomerSpend": {"shape": "ExpectedCustomerSpendList", "documentation": "<p> Provides information about the anticipated customer spend related to this project. This may include details such as amount, frequency, and currency of expected expenditure. </p>"}, "OtherSolutionDescription": {"shape": "ProjectViewOtherSolutionDescriptionString", "documentation": "<p> Offers a description of other solutions if the standard solutions do not adequately cover the project's scope. </p>"}, "SalesActivities": {"shape": "SalesActivities", "documentation": "<p> Lists the pre-sales activities that have occurred with the end-customer related to the opportunity. This field is conditionally mandatory when the project is qualified for Co-Sell and helps drive assignment priority on the AWS side. It provides insight into the engagement level with the customer. </p>"}}, "documentation": "<p> Provides the project view of an opportunity resource shared through a snapshot. </p>"}, "ProjectViewOtherSolutionDescriptionString": {"type": "string", "max": 255, "min": 0, "sensitive": true}, "PutSellingSystemSettingsRequest": {"type": "structure", "required": ["Catalog"], "members": {"Catalog": {"shape": "CatalogIdentifier", "documentation": "<p>Specifies the catalog in which the settings will be updated. Acceptable values include <code>AWS</code> for production and <code>Sandbox</code> for testing environments.</p>"}, "ResourceSnapshotJobRoleIdentifier": {"shape": "ResourceSnapshotJobRoleIdentifier", "documentation": "<p>Specifies the ARN of the IAM Role used for resource snapshot job executions.</p>"}}}, "PutSellingSystemSettingsResponse": {"type": "structure", "required": ["Catalog"], "members": {"Catalog": {"shape": "CatalogIdentifier", "documentation": "<p>Specifies the catalog in which the settings are defined. Acceptable values include <code>AWS</code> for production and <code>Sandbox</code> for testing environments.</p>"}, "ResourceSnapshotJobRoleArn": {"shape": "ResourceSnapshotJobRoleArn", "documentation": "<p>Specifies the ARN of the IAM Role used for resource snapshot job executions.</p>"}}}, "ReasonCode": {"type": "string", "enum": ["InvitationAccessDenied", "InvitationValidationFailed", "EngagementAccessDenied", "OpportunityAccessDenied", "ResourceSnapshotJobAccessDenied", "ResourceSnapshotJobValidationFailed", "ResourceSnapshotJobConflict", "EngagementValidationFailed", "EngagementConflict", "OpportunitySubmissionFailed", "EngagementInvitationConflict", "InternalError", "OpportunityValidationFailed", "OpportunityConflict", "ResourceSnapshotAccessDenied", "ResourceSnapshotValidationFailed", "ResourceSnapshotConflict", "ServiceQuotaExceeded", "RequestThrottled"]}, "Receiver": {"type": "structure", "members": {"Account": {"shape": "AccountReceiver", "documentation": "<p>Specifies the AWS account of the partner who received the Engagement Invitation. This field is used to track the invitation recipient within the AWS ecosystem.</p>"}}, "documentation": "<p>Represents the entity that received the Engagement Invitation, including account and company details. This field is essential for tracking the partner who is being invited to collaborate.</p>", "union": true}, "ReceiverResponsibility": {"type": "string", "enum": ["Distributor", "Reseller", "Hardware Partner", "Managed Service Provider", "Software Partner", "Services Partner", "Training Partner", "Co-<PERSON><PERSON> Facilitator", "Facilitator"]}, "ReceiverResponsibilityList": {"type": "list", "member": {"shape": "ReceiverResponsibility"}}, "RejectEngagementInvitationRequest": {"type": "structure", "required": ["Catalog", "Identifier"], "members": {"Catalog": {"shape": "CatalogIdentifier", "documentation": "<p>This is the catalog that's associated with the engagement invitation. Acceptable values are <code>AWS</code> or <code>Sandbox</code>, and these values determine the environment in which the opportunity is managed.</p>"}, "Identifier": {"shape": "EngagementInvitationArnOrIdentifier", "documentation": "<p>This is the unique identifier of the rejected <code>EngagementInvitation</code>. Providing the correct identifier helps to ensure that the intended invitation is rejected.</p>"}, "RejectionReason": {"shape": "RejectionReasonString", "documentation": "<p>This describes the reason for rejecting the engagement invitation, which helps AWS track usage patterns. Acceptable values include the following:</p> <ul> <li> <p> <i>Customer problem unclear:</i> The customer's problem isn't understood.</p> </li> <li> <p> <i>Next steps unclear:</i> The next steps required to proceed aren't understood.</p> </li> <li> <p> <i>Unable to support:</i> The partner is unable to provide support due to resource or capability constraints.</p> </li> <li> <p> <i>Duplicate of partner referral:</i> The opportunity is a duplicate of an existing referral.</p> </li> <li> <p> <i>Other:</i> Any reason not covered by other values.</p> </li> </ul>"}}}, "RejectionReasonString": {"type": "string", "pattern": "^[\\u0020-\\u007E\\u00A0-\\uD7FF\\uE000-\\uFFFD]{1,80}$"}, "RelatedEntityIdentifiers": {"type": "structure", "members": {"AwsMarketplaceOffers": {"shape": "AwsMarketplaceOfferIdentifiers", "documentation": "<p>Takes one value per opportunity. Each value is an Amazon Resource Name (ARN), in this format: <code>\"offers\": [\"arn:aws:aws-marketplace:us-east-1:************:AWSMarketplace/Offer/offer-sampleOffer32\"]</code>.</p> <p>Use the <a href=\"https://docs.aws.amazon.com/marketplace-catalog/latest/api-reference/API_ListEntities.html\">ListEntities</a> action in the Marketplace Catalog APIs for a list of offers in the associated Marketplace seller account.</p>"}, "AwsProducts": {"shape": "AwsProductIdentifiers", "documentation": "<p>Enables the association of specific Amazon Web Services products with the <code>Opportunity</code>. Partners can indicate the relevant Amazon Web Services products for the <code>Opportunity</code>'s solution and align with the customer's needs. Returns multiple values separated by commas. For example, <code>\"AWSProducts\" : [\"AmazonRedshift\", \"AWSAppFabric\", \"AWSCleanRooms\"]</code>.</p> <p>Use the file with the list of Amazon Web Services products hosted on GitHub: <a href=\"https://github.com/aws-samples/partner-crm-integration-samples/blob/main/resources/aws_products.json\"> Amazon Web Services products</a>.</p>"}, "Solutions": {"shape": "SolutionIdentifiers", "documentation": "<p>Enables partner solutions or offerings' association with an opportunity. To associate a solution, provide the solution's unique identifier, which you can obtain with the <code>ListSolutions</code> operation.</p> <p>If the specific solution identifier is not available, you can use the value <code>Other</code> and provide details about the solution in the <code>otherSolutionOffered</code> field. But when the opportunity reaches the <code>Committed</code> stage or beyond, the <code>Other</code> value cannot be used, and a valid solution identifier must be provided.</p> <p>By associating the relevant solutions with the opportunity, you can communicate the offerings that are being considered or implemented to address the customer's business problem.</p>"}}, "documentation": "<p>This field provides the associations' information for other entities with the opportunity. These entities include identifiers for <code>AWSProducts</code>, <code>Partner Solutions</code>, and <code>AWSMarketplaceOffers</code>.</p>"}, "RelatedEntityType": {"type": "string", "enum": ["Solutions", "AwsProducts", "AwsMarketplaceOffers"]}, "ResourceArn": {"type": "string", "pattern": "^arn:.*"}, "ResourceIdentifier": {"type": "string", "pattern": "^O[0-9]{1,19}$"}, "ResourceNotFoundException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>This error occurs when the specified resource can't be found. The resource might not exist, or isn't visible with the current credentials.</p> <p>Suggested action: Verify that the resource ID is correct and the resource is in the expected AWS region. Check IAM permissions for accessing the resource.</p>", "exception": true}, "ResourceSnapshotArn": {"type": "string", "pattern": "^arn:.*"}, "ResourceSnapshotJobArn": {"type": "string", "pattern": "^arn:.*"}, "ResourceSnapshotJobIdentifier": {"type": "string", "pattern": "^job-[0-9a-z]{13}$"}, "ResourceSnapshotJobRoleArn": {"type": "string", "max": 2048, "min": 0, "pattern": "^arn:aws:iam::\\d{12}:role/([-+=,.@_a-zA-Z0-9]+/)*[-+=,.@_a-zA-Z0-9]{1,64}$"}, "ResourceSnapshotJobRoleIdentifier": {"type": "string", "max": 2048, "min": 0, "pattern": "^(arn:aws:iam::\\d{12}:role/([-+=,.@_a-zA-Z0-9]+/)*)?[-+=,.@_a-zA-Z0-9]{1,64}$"}, "ResourceSnapshotJobStatus": {"type": "string", "enum": ["Running", "Stopped"]}, "ResourceSnapshotJobSummary": {"type": "structure", "members": {"Arn": {"shape": "ResourceSnapshotJobArn", "documentation": "<p> The Amazon Resource Name (ARN) for the resource snapshot job. </p>"}, "EngagementId": {"shape": "EngagementIdentifier", "documentation": "<p>The unique identifier of the Engagement.</p>"}, "Id": {"shape": "ResourceSnapshotJobIdentifier", "documentation": "<p> The unique identifier for the resource snapshot job within the AWS Partner Central system. This ID is used for direct references to the job within the service. </p>"}, "Status": {"shape": "ResourceSnapshotJobStatus", "documentation": "<p>The current status of the snapshot job.</p> <p>Valid values:</p> <ul> <li> <p> STOPPED: The job is not currently running. </p> </li> <li> <p> RUNNING: The job is actively executing. </p> </li> </ul>"}}, "documentation": "<p> An object that contains a <code>Resource Snapshot Job</code>'s subset of fields. </p>"}, "ResourceSnapshotJobSummaryList": {"type": "list", "member": {"shape": "ResourceSnapshotJobSummary"}}, "ResourceSnapshotPayload": {"type": "structure", "members": {"OpportunitySummary": {"shape": "OpportunitySummaryView", "documentation": "<p> An object that contains an <code>opportunity</code>'s subset of fields. </p>"}}, "documentation": "<p> Represents the payload of a resource snapshot. This structure is designed to accommodate different types of resource snapshots, currently supporting opportunity summaries. </p>", "union": true}, "ResourceSnapshotRevision": {"type": "integer", "box": true, "min": 1}, "ResourceSnapshotSummary": {"type": "structure", "members": {"Arn": {"shape": "ResourceSnapshotArn", "documentation": "<p> The Amazon Resource Name (ARN) of the snapshot. This globally unique identifier can be used for cross-service references and in IAM policies. </p>"}, "CreatedBy": {"shape": "AwsAccount", "documentation": "<p>The AWS account ID of the entity that owns the resource from which the snapshot was created.</p>"}, "ResourceId": {"shape": "ResourceIdentifier", "documentation": "<p>The identifier of the specific resource snapshotted. The format might vary depending on the ResourceType. </p>"}, "ResourceSnapshotTemplateName": {"shape": "ResourceTemplateName", "documentation": "<p>The name of the template used to create the snapshot.</p>"}, "ResourceType": {"shape": "ResourceType", "documentation": "<p>The type of resource snapshotted.</p>"}, "Revision": {"shape": "ResourceSnapshotRevision", "documentation": "<p>The revision number of the snapshot. This integer value is incremented each time the snapshot is updated, allowing for version tracking of the resource snapshot. </p>"}}, "documentation": "<p> Provides a concise summary of a resource snapshot, including its unique identifier and version information. This structure is used to quickly reference and identify specific versions of resource snapshots. </p>"}, "ResourceSnapshotSummaryList": {"type": "list", "member": {"shape": "ResourceSnapshotSummary"}}, "ResourceTemplateName": {"type": "string", "pattern": "^[a-zA-Z0-9]{3,80}$"}, "ResourceType": {"type": "string", "enum": ["Opportunity"]}, "RevenueModel": {"type": "string", "enum": ["Contract", "Pay-as-you-go", "Subscription"]}, "ReviewStatus": {"type": "string", "enum": ["Pending Submission", "Submitted", "In review", "Approved", "Rejected", "Action Required"]}, "SalesActivities": {"type": "list", "member": {"shape": "SalesActivity"}}, "SalesActivity": {"type": "string", "enum": ["Initialized discussions with customer", "Customer has shown interest in solution", "Conducted POC / Demo", "In evaluation / planning stage", "Agreed on solution to Business Problem", "Completed Action Plan", "Finalized Deployment Need", "SOW Signed"]}, "SalesInvolvementType": {"type": "string", "enum": ["For Visibility Only", "Co-<PERSON><PERSON>"]}, "SenderContact": {"type": "structure", "required": ["Email"], "members": {"BusinessTitle": {"shape": "JobTitle", "documentation": "<p>The sender-provided contact's title (job title or role) associated with the <code>EngagementInvitation</code>.</p>"}, "Email": {"shape": "SenderContactEmail", "documentation": "<p>The sender-provided contact's email address associated with the <code>EngagementInvitation</code>.</p>"}, "FirstName": {"shape": "Name", "documentation": "<p>The sender-provided contact's last name associated with the <code>EngagementInvitation</code>.</p>"}, "LastName": {"shape": "Name", "documentation": "<p>The sender-provided contact's first name associated with the <code>EngagementInvitation</code>.</p>"}, "Phone": {"shape": "PhoneNumber", "documentation": "<p>The sender-provided contact's phone number associated with the <code>EngagementInvitation</code>.</p>"}}, "documentation": "<p>An object that contains the details of the sender-provided contact person for the <code>EngagementInvitation</code>.</p>"}, "SenderContactEmail": {"type": "string", "max": 80, "min": 0, "pattern": "^[a-zA-Z0-9.!#$%&'*+/=?^_{|}~-]+@[a-zA-Z0-9-]+(?:.[a-zA-Z0-9-]+)*$", "sensitive": true}, "SenderContactList": {"type": "list", "member": {"shape": "SenderContact"}, "max": 3, "min": 1}, "ServiceQuotaExceededException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>This error occurs when the request would cause a service quota to be exceeded. Service quotas represent the maximum allowed use of a specific resource, and this error indicates that the request would surpass that limit.</p> <p>Suggested action: Review the <a href=\"https://docs.aws.amazon.com/partner-central/latest/selling-api/quotas.html\">Quotas</a> for the resource, and either reduce usage or request a quota increase.</p>", "exception": true}, "SoftwareRevenue": {"type": "structure", "members": {"DeliveryModel": {"shape": "RevenueModel", "documentation": "<p>Specifies the customer's intended payment type agreement or procurement method to acquire the solution or service outlined in the <code>Opportunity</code>.</p>"}, "EffectiveDate": {"shape": "Date", "documentation": "<p>Specifies the <code>Opportunity</code>'s customer engagement start date for the contract's effectiveness.</p>"}, "ExpirationDate": {"shape": "Date", "documentation": "<p>Specifies the expiration date for the contract between the customer and Amazon Web Services partner. It signifies the termination date of the agreed-upon engagement period between both parties.</p>"}, "Value": {"shape": "MonetaryValue", "documentation": "<p>Specifies the payment value (amount and currency).</p>"}}, "documentation": "<p>Specifies a customer's procurement terms details. Required only for partners in eligible programs.</p>"}, "SolutionArn": {"type": "string", "pattern": "^S-[0-9]{1,19}$"}, "SolutionBase": {"type": "structure", "required": ["Catalog", "Category", "CreatedDate", "Id", "Name", "Status"], "members": {"Arn": {"shape": "SolutionArn", "documentation": "<p> The SolutionBase structure provides essential information about a solution. </p>"}, "Catalog": {"shape": "CatalogIdentifier", "documentation": "<p>Specifies the catalog in which the solution is hosted, either <code>AWS</code> or <code>Sandbox</code>. This helps partners differentiate between live solutions and those in testing environments.</p>"}, "Category": {"shape": "String", "documentation": "<p>Specifies the solution category, which helps to categorize and organize the solutions partners offer. Valid values: <code>Software Product</code> | <code>Consulting Service</code> | <code>Hardware Product</code> | <code>Communications Product</code> | <code>Professional Service</code> | <code>Managed Service</code> | <code>Value-Added Resale Amazon Web Services Service</code> | <code>Distribution Service</code> | <code>Training Service</code> | <code>Merger and Acquisition Advising Service</code>.</p>"}, "CreatedDate": {"shape": "DateTime", "documentation": "<p>Indicates the solution creation date. This is useful to track and audit.</p>"}, "Id": {"shape": "SolutionIdentifier", "documentation": "<p>Enables the association of solutions (offerings) to opportunities.</p>"}, "Name": {"shape": "String", "documentation": "<p>Specifies the solution name.</p>"}, "Status": {"shape": "SolutionStatus", "documentation": "<p>Specifies the solution's current status, which indicates its state in the system. Valid values: <code>Active</code> | <code>Inactive</code> | <code>Draft</code>. The status helps partners and Amazon Web Services track the solution's lifecycle and availability. Filter for <code>Active</code> solutions for association to an opportunity.</p>"}}, "documentation": "<p>Specifies minimal information for the solution offered to solve the customer's business problem.</p>"}, "SolutionIdentifier": {"type": "string", "pattern": "^S-[0-9]{1,19}$"}, "SolutionIdentifiers": {"type": "list", "member": {"shape": "SolutionIdentifier"}}, "SolutionList": {"type": "list", "member": {"shape": "SolutionBase"}}, "SolutionSort": {"type": "structure", "required": ["SortBy", "SortOrder"], "members": {"SortBy": {"shape": "SolutionSortName", "documentation": "<p>Specifies the attribute to sort by, such as <code>Name</code>, <code>CreatedDate</code>, or <code>Status</code>.</p>"}, "SortOrder": {"shape": "SortOrder", "documentation": "<p>Specifies the sorting order, either <code>Ascending</code> or <code>Descending</code>. The default is <code>Descending</code>.</p>"}}, "documentation": "<p>Configures the solutions' response sorting that enables partners to order solutions based on specified attributes.</p>"}, "SolutionSortName": {"type": "string", "enum": ["Identifier", "Name", "Status", "Category", "CreatedDate"]}, "SolutionStatus": {"type": "string", "enum": ["Active", "Inactive", "Draft"]}, "SortBy": {"type": "string", "enum": ["CreatedDate"]}, "SortObject": {"type": "structure", "members": {"SortBy": {"shape": "SortBy", "documentation": "<p> Specifies the field by which to sort the resource snapshot jobs. </p>"}, "SortOrder": {"shape": "SortOrder", "documentation": "<p> Determines the order in which the sorted results are presented. </p>"}}, "documentation": "<p> Defines the sorting parameters for listing resource snapshot jobs. This structure allows you to specify the field to sort by and the order of sorting. </p>"}, "SortOrder": {"type": "string", "enum": ["ASCENDING", "DESCENDING"]}, "Stage": {"type": "string", "enum": ["Prospect", "Qualified", "Technical Validation", "Business Validation", "Committed", "Launched", "Closed Lost"]}, "StartEngagementByAcceptingInvitationTaskRequest": {"type": "structure", "required": ["Catalog", "ClientToken", "Identifier"], "members": {"Catalog": {"shape": "CatalogIdentifier", "documentation": "<p>Specifies the catalog related to the task. Use <code>AWS</code> for production engagements and <code>Sandbox</code> for testing scenarios.</p>"}, "ClientToken": {"shape": "StartEngagementByAcceptingInvitationTaskRequestClientTokenString", "documentation": "<p>A unique, case-sensitive identifier provided by the client that helps to ensure the idempotency of the request. This can be a random or meaningful string but must be unique for each request.</p>", "idempotencyToken": true}, "Identifier": {"shape": "EngagementInvitationArnOrIdentifier", "documentation": "<p>Specifies the unique identifier of the <code>EngagementInvitation</code> to be accepted. Providing the correct identifier helps ensure that the correct engagement is processed.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>A map of the key-value pairs of the tag or tags to assign.</p>"}}}, "StartEngagementByAcceptingInvitationTaskRequestClientTokenString": {"type": "string", "min": 1, "pattern": "^[!-~]{1,64}$"}, "StartEngagementByAcceptingInvitationTaskResponse": {"type": "structure", "members": {"EngagementInvitationId": {"shape": "EngagementInvitationIdentifier", "documentation": "<p>Returns the identifier of the engagement invitation that was accepted and used to create the opportunity.</p>"}, "Message": {"shape": "String", "documentation": "<p>If the task fails, this field contains a detailed message describing the failure and possible recovery steps.</p>"}, "OpportunityId": {"shape": "OpportunityIdentifier", "documentation": "<p>Returns the original opportunity identifier passed in the request. This is the unique identifier for the opportunity.</p>"}, "ReasonCode": {"shape": "ReasonCode", "documentation": "<p>Indicates the reason for task failure using an enumerated code.</p>"}, "ResourceSnapshotJobId": {"shape": "ResourceSnapshotJobIdentifier", "documentation": "<p>The identifier of the Resource Snapshot Job created as part of this task.</p>"}, "StartTime": {"shape": "DateTime", "documentation": "<p>The timestamp indicating when the task was initiated. The format follows RFC 3339 section 5.6.</p>"}, "TaskArn": {"shape": "TaskArn", "documentation": "<p>The Amazon Resource Name (ARN) of the task, used for tracking and managing the task within AWS.</p>"}, "TaskId": {"shape": "TaskIdentifier", "documentation": "<p>The unique identifier of the task, used to track the task’s progress.</p>"}, "TaskStatus": {"shape": "TaskStatus", "documentation": "<p>Indicates the current status of the task.</p>"}}}, "StartEngagementFromOpportunityTaskRequest": {"type": "structure", "required": ["AwsSubmission", "Catalog", "ClientToken", "Identifier"], "members": {"AwsSubmission": {"shape": "AwsSubmission"}, "Catalog": {"shape": "CatalogIdentifier", "documentation": "<p>Specifies the catalog in which the engagement is tracked. Acceptable values include <code>AWS</code> for production and <code>Sandbox</code> for testing environments.</p>"}, "ClientToken": {"shape": "StartEngagementFromOpportunityTaskRequestClientTokenString", "documentation": "<p>A unique token provided by the client to help ensure the idempotency of the request. It helps prevent the same task from being performed multiple times.</p>", "idempotencyToken": true}, "Identifier": {"shape": "OpportunityIdentifier", "documentation": "<p>The unique identifier of the opportunity from which the engagement task is to be initiated. This helps ensure that the task is applied to the correct opportunity.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>A map of the key-value pairs of the tag or tags to assign.</p>"}}}, "StartEngagementFromOpportunityTaskRequestClientTokenString": {"type": "string", "min": 1, "pattern": "^[!-~]{1,64}$"}, "StartEngagementFromOpportunityTaskResponse": {"type": "structure", "members": {"EngagementId": {"shape": "EngagementIdentifier", "documentation": "<p>The identifier of the newly created Engagement. Only populated if TaskStatus is COMPLETE.</p>"}, "EngagementInvitationId": {"shape": "EngagementInvitationIdentifier", "documentation": "<p>The identifier of the new Engagement invitation. Only populated if TaskStatus is COMPLETE.</p>"}, "Message": {"shape": "String", "documentation": "<p>If the task fails, this field contains a detailed message describing the failure and possible recovery steps.</p>"}, "OpportunityId": {"shape": "OpportunityIdentifier", "documentation": "<p>Returns the original opportunity identifier passed in the request, which is the unique identifier for the opportunity created in the partner’s system.</p>"}, "ReasonCode": {"shape": "ReasonCode", "documentation": "<p>Indicates the reason for task failure using an enumerated code.</p>"}, "ResourceSnapshotJobId": {"shape": "ResourceSnapshotJobIdentifier", "documentation": "<p>The identifier of the resource snapshot job created to add the opportunity resource snapshot to the Engagement. Only populated if TaskStatus is COMPLETE</p>"}, "StartTime": {"shape": "DateTime", "documentation": "<p>The timestamp indicating when the task was initiated. The format follows RFC 3339 section 5.6.</p>"}, "TaskArn": {"shape": "TaskArn", "documentation": "<p>The Amazon Resource Name (ARN) of the task, used for tracking and managing the task within AWS.</p>"}, "TaskId": {"shape": "TaskIdentifier", "documentation": "<p>The unique identifier of the task, used to track the task’s progress. This value follows a specific pattern: <code>^oit-[0-9a-z]{13}$</code>.</p>"}, "TaskStatus": {"shape": "TaskStatus", "documentation": "<p>Indicates the current status of the task. Valid values include <code>IN_PROGRESS</code>, <code>COMPLETE</code>, and <code>FAILED</code>.</p>"}}}, "StartResourceSnapshotJobRequest": {"type": "structure", "required": ["Catalog", "ResourceSnapshotJobIdentifier"], "members": {"Catalog": {"shape": "CatalogIdentifier", "documentation": "<p>Specifies the catalog related to the request. Valid values are:</p> <ul> <li> <p>AWS: Starts the request from the production AWS environment.</p> </li> <li> <p>Sandbox: Starts the request from a sandbox environment used for testing or development purposes.</p> </li> </ul>"}, "ResourceSnapshotJobIdentifier": {"shape": "ResourceSnapshotJobIdentifier", "documentation": "<p>The identifier of the resource snapshot job to start.</p>"}}}, "StopResourceSnapshotJobRequest": {"type": "structure", "required": ["Catalog", "ResourceSnapshotJobIdentifier"], "members": {"Catalog": {"shape": "CatalogIdentifier", "documentation": "<p>Specifies the catalog related to the request. Valid values are:</p> <ul> <li> <p>AWS: Stops the request from the production AWS environment.</p> </li> <li> <p>Sandbox: Stops the request from a sandbox environment used for testing or development purposes.</p> </li> </ul>"}, "ResourceSnapshotJobIdentifier": {"shape": "ResourceSnapshotJobIdentifier", "documentation": "<p>The identifier of the job to stop.</p>"}}}, "String": {"type": "string"}, "SubmitOpportunityRequest": {"type": "structure", "required": ["Catalog", "Identifier", "InvolvementType"], "members": {"Catalog": {"shape": "CatalogIdentifier", "documentation": "<p>Specifies the catalog related to the request. Valid values are:</p> <ul> <li> <p>AWS: Submits the opportunity request from the production AWS environment.</p> </li> <li> <p>Sandbox: Submits the opportunity request from a sandbox environment used for testing or development purposes.</p> </li> </ul>"}, "Identifier": {"shape": "OpportunityIdentifier", "documentation": "<p>The identifier of the Opportunity previously created by partner and needs to be submitted.</p>"}, "InvolvementType": {"shape": "SalesInvolvementType", "documentation": "<p>Specifies the level of AWS sellers' involvement on the opportunity. Valid values:</p> <ul> <li> <p> <code>Co-sell</code>: Indicates the user wants to co-sell with AWS. Share the opportunity with AWS to receive deal assistance and support.</p> </li> <li> <p> <code>For Visibility Only</code>: Indicates that the user does not need support from AWS Sales Rep. Share this opportunity with AWS for visibility only, you will not receive deal assistance and support.</p> </li> </ul>"}, "Visibility": {"shape": "Visibility", "documentation": "<p>Determines whether to restrict visibility of the opportunity from AWS sales. Default value is Full. Valid values:</p> <ul> <li> <p> <code>Full</code>: The opportunity is fully visible to AWS sales.</p> </li> <li> <p> <code>Limited</code>: The opportunity has restricted visibility to AWS sales.</p> </li> </ul>"}}}, "Tag": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {"shape": "TagKey", "documentation": "<p>The key in the tag.</p>"}, "Value": {"shape": "TagValue", "documentation": "<p>The value in the tag.</p>"}}, "documentation": "<p>The key-value pair assigned to a specified resource.</p>"}, "TagKey": {"type": "string", "max": 128, "min": 1, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 50, "min": 1}, "TagList": {"type": "list", "member": {"shape": "Tag"}, "max": 200, "min": 1}, "TagResourceRequest": {"type": "structure", "required": ["ResourceArn", "Tags"], "members": {"ResourceArn": {"shape": "TaggableResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource that you want to tag.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>A map of the key-value pairs of the tag or tags to assign.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "TaggableResourceArn": {"type": "string", "max": 1000, "min": 1, "pattern": "^arn:[\\w+=/,.@-]+:partnercentral:[\\w+=/,.@-]*:[0-9]{12}:catalog/([a-zA-Z]+)/[\\w+=,.@-]+(/[\\w+=,.@-]+)*$"}, "TaskArn": {"type": "string", "pattern": "^arn:.*"}, "TaskArnOrIdentifier": {"type": "string", "pattern": "^(arn:.*|task-[0-9a-z]{13})$"}, "TaskIdentifier": {"type": "string", "pattern": "task-[0-9a-z]{13}$"}, "TaskIdentifiers": {"type": "list", "member": {"shape": "TaskArnOrIdentifier"}, "max": 10, "min": 1}, "TaskStatus": {"type": "string", "enum": ["IN_PROGRESS", "COMPLETE", "FAILED"]}, "TaskStatuses": {"type": "list", "member": {"shape": "TaskStatus"}, "max": 3, "min": 1}, "ThrottlingException": {"type": "structure", "members": {"Message": {"shape": "String"}}, "documentation": "<p>This error occurs when there are too many requests sent. Review the provided quotas and adapt your usage to avoid throttling.</p> <p>This error occurs when there are too many requests sent. Review the provided <a href=\"https://docs.aws.amazon.com/partner-central/latest/selling-api/quotas.html\">Quotas</a> and retry after the provided delay.</p>", "exception": true}, "UntagResourceRequest": {"type": "structure", "required": ["ResourceArn", "TagKeys"], "members": {"ResourceArn": {"shape": "TaggableResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource that you want to untag.</p>"}, "TagKeys": {"shape": "TagKeyList", "documentation": "<p>The keys of the key-value pairs for the tag or tags you want to remove from the specified resource.</p>"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateOpportunityRequest": {"type": "structure", "required": ["Catalog", "Identifier", "LastModifiedDate"], "members": {"Catalog": {"shape": "CatalogIdentifier", "documentation": "<p>Specifies the catalog associated with the request. This field takes a string value from a predefined list: <code>AWS</code> or <code>Sandbox</code>. The catalog determines which environment the opportunity is updated in. Use <code>AWS</code> to update real opportunities in the production environment, and <code>Sandbox</code> for testing in secure, isolated environments. When you use the <code>Sandbox</code> catalog, it allows you to simulate and validate your interactions with Amazon Web Services services without affecting live data or operations.</p>"}, "Customer": {"shape": "Customer", "documentation": "<p>Specifies details of the customer associated with the <code>Opportunity</code>.</p>"}, "Identifier": {"shape": "OpportunityIdentifier", "documentation": "<p>Read-only, system generated <code>Opportunity</code> unique identifier.</p>"}, "LastModifiedDate": {"shape": "DateTime", "documentation": "<p> <code>DateTime</code> when the opportunity was last modified.</p>"}, "LifeCycle": {"shape": "LifeCycle", "documentation": "<p>An object that contains lifecycle details for the <code>Opportunity</code>.</p>"}, "Marketing": {"shape": "Marketing", "documentation": "<p>An object that contains marketing details for the <code>Opportunity</code>.</p>"}, "NationalSecurity": {"shape": "NationalSecurity", "documentation": "<p>Specifies if the opportunity is associated with national security concerns. This flag is only applicable when the industry is <code>Government</code>. For national-security-related opportunities, validation and compliance rules may apply, impacting the opportunity's visibility and processing.</p>"}, "OpportunityType": {"shape": "OpportunityType", "documentation": "<p>Specifies the opportunity type as a renewal, new, or expansion.</p> <p>Opportunity types:</p> <ul> <li> <p>New opportunity: Represents a new business opportunity with a potential customer that's not previously engaged with your solutions or services.</p> </li> <li> <p>Renewal opportunity: Represents an opportunity to renew an existing contract or subscription with a current customer, ensuring continuity of service.</p> </li> <li> <p>Expansion opportunity: Represents an opportunity to expand the scope of an existing contract or subscription, either by adding new services or increasing the volume of existing services for a current customer.</p> </li> </ul>"}, "PartnerOpportunityIdentifier": {"shape": "UpdateOpportunityRequestPartnerOpportunityIdentifierString", "documentation": "<p>Specifies the opportunity's unique identifier in the partner's CRM system. This value is essential to track and reconcile because it's included in the outbound payload sent back to the partner.</p>"}, "PrimaryNeedsFromAws": {"shape": "PrimaryNeedsFromAws", "documentation": "<p>Identifies the type of support the partner needs from Amazon Web Services.</p> <p>Valid values:</p> <ul> <li> <p>Cosell—Architectural Validation: Confirmation from Amazon Web Services that the partner's proposed solution architecture is aligned with Amazon Web Services best practices and poses minimal architectural risks.</p> </li> <li> <p>Cosell—Business Presentation: Request Amazon Web Services seller's participation in a joint customer presentation.</p> </li> <li> <p>Cosell—Competitive Information: Access to Amazon Web Services competitive resources and support for the partner's proposed solution.</p> </li> <li> <p>Cosell—Pricing Assistance: Connect with an AWS seller for support situations where a partner may be receiving an upfront discount on a service (for example: EDP deals).</p> </li> <li> <p>Cosell—Technical Consultation: Connection with an Amazon Web Services Solutions Architect to address the partner's questions about the proposed solution.</p> </li> <li> <p>Cosell—Total Cost of Ownership Evaluation: Assistance with quoting different cost savings of proposed solutions on Amazon Web Services versus on-premises or a traditional hosting environment.</p> </li> <li> <p>Cosell—Deal Support: Request Amazon Web Services seller's support to progress the opportunity (for example: joint customer call, strategic positioning).</p> </li> <li> <p>Cosell—Support for Public Tender/RFx: Opportunity related to the public sector where the partner needs RFx support from Amazon Web Services.</p> </li> </ul>"}, "Project": {"shape": "Project", "documentation": "<p>An object that contains project details summary for the <code>Opportunity</code>.</p>"}, "SoftwareRevenue": {"shape": "SoftwareRevenue", "documentation": "<p>Specifies details of a customer's procurement terms. Required only for partners in eligible programs.</p>"}}}, "UpdateOpportunityRequestPartnerOpportunityIdentifierString": {"type": "string", "max": 64, "min": 0}, "UpdateOpportunityResponse": {"type": "structure", "required": ["Id", "LastModifiedDate"], "members": {"Id": {"shape": "OpportunityIdentifier", "documentation": "<p>Read-only, system generated <code>Opportunity</code> unique identifier.</p>"}, "LastModifiedDate": {"shape": "DateTime", "documentation": "<p> <code>DateTime</code> when the opportunity was last modified.</p>"}}}, "UseCases": {"type": "list", "member": {"shape": "String"}}, "ValidationException": {"type": "structure", "required": ["Message", "Reason"], "members": {"ErrorList": {"shape": "ValidationExceptionErrorList", "documentation": "<p>A list of issues that were discovered in the submitted request or the resource state.</p>"}, "Message": {"shape": "String"}, "Reason": {"shape": "ValidationExceptionReason", "documentation": "<p>The primary reason for this validation exception to occur.</p> <ul> <li> <p> <i>REQUEST_VALIDATION_FAILED:</i> The request format is not valid.</p> <p>Fix: Verify your request payload includes all required fields, uses correct data types and string formats.</p> </li> <li> <p> <i>BUSINESS_VALIDATION_FAILED:</i> The requested change doesn't pass the business validation rules.</p> <p>Fix: Check that your change aligns with the business rules defined by AWS Partner Central.</p> </li> </ul>"}}, "documentation": "<p>The input fails to satisfy the constraints specified by the service or business validation rules.</p> <p>Suggested action: Review the error message, including the failed fields and reasons, to correct the request payload.</p>", "exception": true}, "ValidationExceptionError": {"type": "structure", "required": ["Code", "Message"], "members": {"Code": {"shape": "ValidationExceptionErrorCode", "documentation": "<p>Specifies the error code for the invalid field value.</p>"}, "FieldName": {"shape": "String", "documentation": "<p>Specifies the field name with the invalid value.</p>"}, "Message": {"shape": "String", "documentation": "<p>Specifies the detailed error message for the invalid field value.</p>"}}, "documentation": "<p>Indicates an invalid value for a field.</p> <ul> <li> <p> <i>REQUIRED_FIELD_MISSING:</i> The request is missing a required field.</p> <p>Fix: Verify your request payload includes all required fields.</p> </li> <li> <p> <i>INVALID_ENUM_VALUE:</i> The enum field value isn't an accepted values.</p> <p>Fix: Check the documentation for the list of valid enum values, and update your request with a valid value.</p> </li> <li> <p> <i>INVALID_STRING_FORMAT:</i> The string format is invalid.</p> <p>Fix: Confirm that the string is in the expected format (For example: email address, date).</p> </li> <li> <p> <i>INVALID_VALUE:</i> The value isn't valid.</p> <p>Fix: Confirm that the value meets the expected criteria and is within the allowable range or set.</p> </li> <li> <p> <i>TOO_MANY_VALUES:</i> There are too many values in a field that expects fewer entries.</p> <p>Fix: Reduce the number of values to match the expected limit.</p> </li> <li> <p> <i>ACTION_NOT_PERMITTED:</i> The action isn't permitted due to current state or permissions.</p> <p>Fix: Verify that the action is appropriate for the current state, and that you have the necessary permissions to perform it.</p> </li> <li> <p> <i>DUPLICATE_KEY_VALUE:</i> The value in a field duplicates a value that must be unique.</p> <p>Fix: Verify that the value is unique and doesn't duplicate an existing value in the system.</p> </li> </ul>"}, "ValidationExceptionErrorCode": {"type": "string", "enum": ["REQUIRED_FIELD_MISSING", "INVALID_ENUM_VALUE", "INVALID_STRING_FORMAT", "INVALID_VALUE", "TOO_MANY_VALUES", "INVALID_RESOURCE_STATE", "DUPLICATE_KEY_VALUE", "VALUE_OUT_OF_RANGE", "ACTION_NOT_PERMITTED"]}, "ValidationExceptionErrorList": {"type": "list", "member": {"shape": "ValidationExceptionError"}}, "ValidationExceptionReason": {"type": "string", "enum": ["REQUEST_VALIDATION_FAILED", "BUSINESS_VALIDATION_FAILED"]}, "Visibility": {"type": "string", "enum": ["Full", "Limited"]}, "WebsiteUrl": {"type": "string", "max": 255, "min": 4, "sensitive": true}}, "documentation": "<p><fullname>AWS Partner Central API for Selling</fullname> <p> <b>AWS Partner Central API for Selling Reference Guide</b> </p> <p>This Amazon Web Services (AWS) Partner Central API reference is designed to help <a href=\"http://aws.amazon.com/partners/programs/\">AWS Partners</a> integrate Customer Relationship Management (CRM) systems with AWS Partner Central. Partners can automate interactions with AWS Partner Central, which helps to ensure effective engagements in joint business activities.</p> <p>The API provides standard AWS API functionality. Access it by either using API <a href=\"https://docs.aws.amazon.com/partner-central/latest/selling-api/API_Operations.html\">Actions</a> or by using an AWS SDK that's tailored to your programming language or platform. For more information, see <a href=\"http://aws.amazon.com/getting-started\">Getting Started with AWS</a> and <a href=\"http://aws.amazon.com/developer/tools/\">Tools to Build on AWS</a>.</p> <p class=\"title\"> <b>Features offered by AWS Partner Central API</b> </p> <ol> <li> <p> <b>Opportunity management:</b> Manages coselling opportunities through API actions such as <code>CreateOpportunity</code>, <code>UpdateOpportunity</code>, <code>ListOpportunities</code>, <code>GetOpportunity</code>, and <code>AssignOpportunity</code>.</p> </li> <li> <p> <b>AWS referral management:</b> Manages referrals shared by AWS using actions such as <code>ListEngagementInvitations</code>, <code>GetEngagementInvitation</code>, <code>StartEngagementByAcceptingInvitation</code>, and <code>RejectEngagementInvitation</code>.</p> </li> <li> <p> <b>Entity association:</b> Associates related entities such as <i>AWS Products</i>, <i>Partner Solutions</i>, and <i>AWS Marketplace Private Offers</i> with opportunities using the actions <code>AssociateOpportunity</code>, and <code>DisassociateOpportunity</code>.</p> </li> <li> <p> <b>View AWS opportunity details:</b> Retrieves real-time summaries of AWS opportunities using the <code>GetAWSOpportunitySummary</code> action.</p> </li> <li> <p> <b>List solutions:</b> Provides list APIs for listing partner offers using <code>ListSolutions</code>.</p> </li> <li> <p> <b>Event subscription:</b> Subscribe to real-time opportunity updates through AWS EventBridge by using actions such as <i>Opportunity Created</i>, <i>Opportunity Updated</i>, <i>Engagement Invitation Accepted</i>, <i>Engagement Invitation Rejected</i>, and <i>Engagement Invitation Created</i>.</p> </li> </ol></p>"}