{"pagination": {"ListAccessPointsForObjectLambda": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "ObjectLambdaAccessPointList"}, "ListCallerAccessGrants": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "CallerAccessGrantsList"}, "ListAccessPointsForDirectoryBuckets": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "AccessPointList"}}}