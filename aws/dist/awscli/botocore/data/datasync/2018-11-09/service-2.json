{"version": "2.0", "metadata": {"apiVersion": "2018-11-09", "endpointPrefix": "datasync", "jsonVersion": "1.1", "protocol": "json", "protocols": ["json"], "serviceAbbreviation": "DataSync", "serviceFullName": "AWS DataSync", "serviceId": "DataSync", "signatureVersion": "v4", "signingName": "datasync", "targetPrefix": "FmrsService", "uid": "datasync-2018-11-09", "auth": ["aws.auth#sigv4"]}, "operations": {"CancelTaskExecution": {"name": "CancelTaskExecution", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CancelTaskExecutionRequest"}, "output": {"shape": "CancelTaskExecutionResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Stops an DataSync task execution that's in progress. The transfer of some files are abruptly interrupted. File contents that're transferred to the destination might be incomplete or inconsistent with the source files.</p> <p>However, if you start a new task execution using the same task and allow it to finish, file content on the destination will be complete and consistent. This applies to other unexpected failures that interrupt a task execution. In all of these cases, DataSync successfully completes the transfer when you start the next task execution.</p>"}, "CreateAgent": {"name": "CreateAgent", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateAgentRequest"}, "output": {"shape": "CreateAgentResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Activates an DataSync agent that you deploy in your storage environment. The activation process associates the agent with your Amazon Web Services account.</p> <p>If you haven't deployed an agent yet, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/do-i-need-datasync-agent.html\">Do I need a DataSync agent?</a> </p>"}, "CreateLocationAzureBlob": {"name": "CreateLocationAzureBlob", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateLocationAzureBlobRequest"}, "output": {"shape": "CreateLocationAzureBlobResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Creates a transfer <i>location</i> for a Microsoft Azure Blob Storage container. DataSync can use this location as a transfer source or destination. You can make transfers with or without a <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/creating-azure-blob-location.html#azure-blob-creating-agent\">DataSync agent</a> that connects to your container.</p> <p>Before you begin, make sure you know <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/creating-azure-blob-location.html#azure-blob-access\">how DataSync accesses Azure Blob Storage</a> and works with <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/creating-azure-blob-location.html#azure-blob-access-tiers\">access tiers</a> and <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/creating-azure-blob-location.html#blob-types\">blob types</a>.</p>"}, "CreateLocationEfs": {"name": "CreateLocationEfs", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateLocationEfsRequest"}, "output": {"shape": "CreateLocationEfsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Creates a transfer <i>location</i> for an Amazon EFS file system. DataSync can use this location as a source or destination for transferring data.</p> <p>Before you begin, make sure that you understand how DataSync <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-efs-location.html#create-efs-location-access\">accesses Amazon EFS file systems</a>.</p>"}, "CreateLocationFsxLustre": {"name": "CreateLocationFsxLustre", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateLocationFsxLustreRequest"}, "output": {"shape": "CreateLocationFsxLustreResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Creates a transfer <i>location</i> for an Amazon FSx for Lustre file system. DataSync can use this location as a source or destination for transferring data.</p> <p>Before you begin, make sure that you understand how DataSync <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-lustre-location.html#create-lustre-location-access\">accesses FSx for Lustre file systems</a>.</p>"}, "CreateLocationFsxOntap": {"name": "CreateLocationFsxOntap", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateLocationFsxOntapRequest"}, "output": {"shape": "CreateLocationFsxOntapResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Creates a transfer <i>location</i> for an Amazon FSx for NetApp ONTAP file system. DataSync can use this location as a source or destination for transferring data.</p> <p>Before you begin, make sure that you understand how DataSync <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-ontap-location.html#create-ontap-location-access\">accesses FSx for ONTAP file systems</a>.</p>"}, "CreateLocationFsxOpenZfs": {"name": "CreateLocationFsxOpenZfs", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateLocationFsxOpenZfsRequest"}, "output": {"shape": "CreateLocationFsxOpenZfsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Creates a transfer <i>location</i> for an Amazon FSx for OpenZFS file system. DataSync can use this location as a source or destination for transferring data.</p> <p>Before you begin, make sure that you understand how DataSync <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-openzfs-location.html#create-openzfs-access\">accesses FSx for OpenZFS file systems</a>.</p> <note> <p>Request parameters related to <code>SMB</code> aren't supported with the <code>CreateLocationFsxOpenZfs</code> operation.</p> </note>"}, "CreateLocationFsxWindows": {"name": "CreateLocationFsxWindows", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateLocationFsxWindowsRequest"}, "output": {"shape": "CreateLocationFsxWindowsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Creates a transfer <i>location</i> for an Amazon FSx for Windows File Server file system. DataSync can use this location as a source or destination for transferring data.</p> <p>Before you begin, make sure that you understand how DataSync <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-fsx-location.html#create-fsx-location-access\">accesses FSx for Windows File Server file systems</a>.</p>"}, "CreateLocationHdfs": {"name": "CreateLocationHdfs", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateLocationHdfsRequest"}, "output": {"shape": "CreateLocationHdfsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Creates a transfer <i>location</i> for a Hadoop Distributed File System (HDFS). DataSync can use this location as a source or destination for transferring data.</p> <p>Before you begin, make sure that you understand how DataSync <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-hdfs-location.html#accessing-hdfs\">accesses HDFS clusters</a>.</p>"}, "CreateLocationNfs": {"name": "CreateLocationNfs", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateLocationNfsRequest"}, "output": {"shape": "CreateLocationNfsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Creates a transfer <i>location</i> for a Network File System (NFS) file server. DataSync can use this location as a source or destination for transferring data.</p> <p>Before you begin, make sure that you understand how DataSync <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-nfs-location.html#accessing-nfs\">accesses NFS file servers</a>.</p>"}, "CreateLocationObjectStorage": {"name": "CreateLocationObjectStorage", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateLocationObjectStorageRequest"}, "output": {"shape": "CreateLocationObjectStorageResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Creates a transfer <i>location</i> for an object storage system. DataSync can use this location as a source or destination for transferring data. You can make transfers with or without a <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/do-i-need-datasync-agent.html#when-agent-required\">DataSync agent</a>.</p> <p>Before you begin, make sure that you understand the <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-object-location.html#create-object-location-prerequisites\">prerequisites</a> for DataSync to work with object storage systems.</p>"}, "CreateLocationS3": {"name": "CreateLocationS3", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateLocationS3Request"}, "output": {"shape": "CreateLocationS3Response"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Creates a transfer <i>location</i> for an Amazon S3 bucket. DataSync can use this location as a source or destination for transferring data.</p> <important> <p>Before you begin, make sure that you read the following topics:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-s3-location.html#using-storage-classes\">Storage class considerations with Amazon S3 locations</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-s3-location.html#create-s3-location-s3-requests\">Evaluating S3 request costs when using DataSync</a> </p> </li> </ul> </important> <p> For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-s3-location.html\">Configuring transfers with Amazon S3</a>.</p>"}, "CreateLocationSmb": {"name": "CreateLocationSmb", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateLocationSmbRequest"}, "output": {"shape": "CreateLocationSmbResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Creates a transfer <i>location</i> for a Server Message Block (SMB) file server. DataSync can use this location as a source or destination for transferring data.</p> <p>Before you begin, make sure that you understand how DataSync accesses SMB file servers. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-smb-location.html#configuring-smb-permissions\">Providing DataSync access to SMB file servers</a>.</p>"}, "CreateTask": {"name": "CreateTask", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateTaskRequest"}, "output": {"shape": "CreateTaskResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Configures a <i>task</i>, which defines where and how DataSync transfers your data.</p> <p>A task includes a source location, destination location, and transfer options (such as bandwidth limits, scheduling, and more).</p> <important> <p>If you're planning to transfer data to or from an Amazon S3 location, review <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-s3-location.html#create-s3-location-s3-requests\">how DataSync can affect your S3 request charges</a> and the <a href=\"http://aws.amazon.com/datasync/pricing/\">DataSync pricing page</a> before you begin.</p> </important>"}, "DeleteAgent": {"name": "DeleteAgent", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteAgentRequest"}, "output": {"shape": "DeleteAgentResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Removes an DataSync agent resource from your Amazon Web Services account.</p> <p>Keep in mind that this operation (which can't be undone) doesn't remove the agent's virtual machine (VM) or Amazon EC2 instance from your storage environment. For next steps, you can delete the VM or instance from your storage environment or reuse it to <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/activate-agent.html\">activate a new agent</a>.</p>"}, "DeleteLocation": {"name": "DeleteLocation", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteLocationRequest"}, "output": {"shape": "DeleteLocationResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Deletes a transfer location resource from DataSync. </p>"}, "DeleteTask": {"name": "DeleteTask", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteTaskRequest"}, "output": {"shape": "DeleteTaskResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Deletes a transfer task resource from DataSync.</p>"}, "DescribeAgent": {"name": "DescribeAgent", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeAgentRequest"}, "output": {"shape": "DescribeAgentResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Returns information about an DataSync agent, such as its name, service endpoint type, and status.</p>"}, "DescribeLocationAzureBlob": {"name": "DescribeLocationAzureBlob", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeLocationAzureBlobRequest"}, "output": {"shape": "DescribeLocationAzureBlobResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Provides details about how an DataSync transfer location for Microsoft Azure Blob Storage is configured.</p>"}, "DescribeLocationEfs": {"name": "DescribeLocationEfs", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeLocationEfsRequest"}, "output": {"shape": "DescribeLocationEfsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Provides details about how an DataSync transfer location for an Amazon EFS file system is configured.</p>"}, "DescribeLocationFsxLustre": {"name": "DescribeLocationFsxLustre", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeLocationFsxLustreRequest"}, "output": {"shape": "DescribeLocationFsxLustreResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Provides details about how an DataSync transfer location for an Amazon FSx for Lustre file system is configured.</p>"}, "DescribeLocationFsxOntap": {"name": "DescribeLocationFsxOntap", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeLocationFsxOntapRequest"}, "output": {"shape": "DescribeLocationFsxOntapResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Provides details about how an DataSync transfer location for an Amazon FSx for NetApp ONTAP file system is configured.</p> <note> <p>If your location uses SMB, the <code>DescribeLocationFsxOntap</code> operation doesn't actually return a <code>Password</code>.</p> </note>"}, "DescribeLocationFsxOpenZfs": {"name": "DescribeLocationFsxOpenZfs", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeLocationFsxOpenZfsRequest"}, "output": {"shape": "DescribeLocationFsxOpenZfsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Provides details about how an DataSync transfer location for an Amazon FSx for OpenZFS file system is configured.</p> <note> <p>Response elements related to <code>SMB</code> aren't supported with the <code>DescribeLocationFsxOpenZfs</code> operation.</p> </note>"}, "DescribeLocationFsxWindows": {"name": "DescribeLocationFsxWindows", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeLocationFsxWindowsRequest"}, "output": {"shape": "DescribeLocationFsxWindowsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Provides details about how an DataSync transfer location for an Amazon FSx for Windows File Server file system is configured.</p>"}, "DescribeLocationHdfs": {"name": "DescribeLocationHdfs", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeLocationHdfsRequest"}, "output": {"shape": "DescribeLocationHdfsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Provides details about how an DataSync transfer location for a Hadoop Distributed File System (HDFS) is configured.</p>"}, "DescribeLocationNfs": {"name": "DescribeLocationNfs", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeLocationNfsRequest"}, "output": {"shape": "DescribeLocationNfsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Provides details about how an DataSync transfer location for a Network File System (NFS) file server is configured.</p>"}, "DescribeLocationObjectStorage": {"name": "DescribeLocationObjectStorage", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeLocationObjectStorageRequest"}, "output": {"shape": "DescribeLocationObjectStorageResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Provides details about how an DataSync transfer location for an object storage system is configured.</p>"}, "DescribeLocationS3": {"name": "DescribeLocationS3", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeLocationS3Request"}, "output": {"shape": "DescribeLocationS3Response"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Provides details about how an DataSync transfer location for an S3 bucket is configured.</p>"}, "DescribeLocationSmb": {"name": "DescribeLocationSmb", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeLocationSmbRequest"}, "output": {"shape": "DescribeLocationSmbResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Provides details about how an DataSync transfer location for a Server Message Block (SMB) file server is configured.</p>"}, "DescribeTask": {"name": "DescribeTask", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeTaskRequest"}, "output": {"shape": "DescribeTaskResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Provides information about a <i>task</i>, which defines where and how DataSync transfers your data.</p>"}, "DescribeTaskExecution": {"name": "DescribeTaskExecution", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeTaskExecutionRequest"}, "output": {"shape": "DescribeTaskExecutionResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Provides information about an execution of your DataSync task. You can use this operation to help monitor the progress of an ongoing data transfer or check the results of the transfer.</p> <note> <p>Some <code>DescribeTaskExecution</code> response elements are only relevant to a specific task mode. For information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/choosing-task-mode.html#task-mode-differences\">Understanding task mode differences</a> and <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/transfer-performance-counters.html\">Understanding data transfer performance counters</a>.</p> </note>"}, "ListAgents": {"name": "ListAgents", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListAgentsRequest"}, "output": {"shape": "ListAgentsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Returns a list of DataSync agents that belong to an Amazon Web Services account in the Amazon Web Services Region specified in the request.</p> <p>With pagination, you can reduce the number of agents returned in a response. If you get a truncated list of agents in a response, the response contains a marker that you can specify in your next request to fetch the next page of agents.</p> <p> <code>ListAgents</code> is eventually consistent. This means the result of running the operation might not reflect that you just created or deleted an agent. For example, if you create an agent with <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/API_CreateAgent.html\">CreateAgent</a> and then immediately run <code>ListAgents</code>, that agent might not show up in the list right away. In situations like this, you can always confirm whether an agent has been created (or deleted) by using <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/API_DescribeAgent.html\">DescribeAgent</a>.</p>"}, "ListLocations": {"name": "ListLocations", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListLocationsRequest"}, "output": {"shape": "ListLocationsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Returns a list of source and destination locations.</p> <p>If you have more locations than are returned in a response (that is, the response returns only a truncated list of your agents), the response contains a token that you can specify in your next request to fetch the next page of locations.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Returns all the tags associated with an Amazon Web Services resource.</p>"}, "ListTaskExecutions": {"name": "ListTaskExecutions", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTaskExecutionsRequest"}, "output": {"shape": "ListTaskExecutionsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Returns a list of executions for an DataSync transfer task.</p>"}, "ListTasks": {"name": "ListTasks", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTasksRequest"}, "output": {"shape": "ListTasksResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Returns a list of the DataSync tasks you created.</p>"}, "StartTaskExecution": {"name": "StartTaskExecution", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StartTaskExecutionRequest"}, "output": {"shape": "StartTaskExecutionResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Starts an DataSync transfer task. For each task, you can only run one task execution at a time.</p> <p>There are several steps to a task execution. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/working-with-task-executions.html#understand-task-execution-statuses\">Task execution statuses</a>.</p> <important> <p>If you're planning to transfer data to or from an Amazon S3 location, review <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-s3-location.html#create-s3-location-s3-requests\">how DataSync can affect your S3 request charges</a> and the <a href=\"http://aws.amazon.com/datasync/pricing/\">DataSync pricing page</a> before you begin.</p> </important>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Applies a <i>tag</i> to an Amazon Web Services resource. Tags are key-value pairs that can help you manage, filter, and search for your resources.</p> <p>These include DataSync resources, such as locations, tasks, and task executions.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Removes tags from an Amazon Web Services resource.</p>"}, "UpdateAgent": {"name": "UpdateAgent", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateAgentRequest"}, "output": {"shape": "UpdateAgentResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Updates the name of an DataSync agent.</p>"}, "UpdateLocationAzureBlob": {"name": "UpdateLocationAzureBlob", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateLocationAzureBlobRequest"}, "output": {"shape": "UpdateLocationAzureBlobResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Modifies the following configurations of the Microsoft Azure Blob Storage transfer location that you're using with DataSync.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/creating-azure-blob-location.html\">Configuring DataSync transfers with Azure Blob Storage</a>.</p>"}, "UpdateLocationEfs": {"name": "UpdateLocationEfs", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateLocationEfsRequest"}, "output": {"shape": "UpdateLocationEfsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Modifies the following configuration parameters of the Amazon EFS transfer location that you're using with DataSync.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-efs-location.html\">Configuring DataSync transfers with Amazon EFS</a>.</p>"}, "UpdateLocationFsxLustre": {"name": "UpdateLocationFsxLustre", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateLocationFsxLustreRequest"}, "output": {"shape": "UpdateLocationFsxLustreResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Modifies the following configuration parameters of the Amazon FSx for Lustre transfer location that you're using with DataSync.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-lustre-location.html\">Configuring DataSync transfers with FSx for Lustre</a>.</p>"}, "UpdateLocationFsxOntap": {"name": "UpdateLocationFsxOntap", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateLocationFsxOntapRequest"}, "output": {"shape": "UpdateLocationFsxOntapResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Modifies the following configuration parameters of the Amazon FSx for NetApp ONTAP transfer location that you're using with DataSync.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-ontap-location.html\">Configuring DataSync transfers with FSx for ONTAP</a>.</p>"}, "UpdateLocationFsxOpenZfs": {"name": "UpdateLocationFsxOpenZfs", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateLocationFsxOpenZfsRequest"}, "output": {"shape": "UpdateLocationFsxOpenZfsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Modifies the following configuration parameters of the Amazon FSx for OpenZFS transfer location that you're using with DataSync.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-openzfs-location.html\">Configuring DataSync transfers with FSx for OpenZFS</a>.</p> <note> <p>Request parameters related to <code>SMB</code> aren't supported with the <code>UpdateLocationFsxOpenZfs</code> operation.</p> </note>"}, "UpdateLocationFsxWindows": {"name": "UpdateLocationFsxWindows", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateLocationFsxWindowsRequest"}, "output": {"shape": "UpdateLocationFsxWindowsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Modifies the following configuration parameters of the Amazon FSx for Windows File Server transfer location that you're using with DataSync.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-fsx-location.html\">Configuring DataSync transfers with FSx for Windows File Server</a>.</p>"}, "UpdateLocationHdfs": {"name": "UpdateLocationHdfs", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateLocationHdfsRequest"}, "output": {"shape": "UpdateLocationHdfsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Modifies the following configuration parameters of the Hadoop Distributed File System (HDFS) transfer location that you're using with DataSync.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-hdfs-location.html\">Configuring DataSync transfers with an HDFS cluster</a>.</p>"}, "UpdateLocationNfs": {"name": "UpdateLocationNfs", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateLocationNfsRequest"}, "output": {"shape": "UpdateLocationNfsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Modifies the following configuration parameters of the Network File System (NFS) transfer location that you're using with DataSync.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-nfs-location.html\">Configuring transfers with an NFS file server</a>.</p>"}, "UpdateLocationObjectStorage": {"name": "UpdateLocationObjectStorage", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateLocationObjectStorageRequest"}, "output": {"shape": "UpdateLocationObjectStorageResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Modifies the following configuration parameters of the object storage transfer location that you're using with DataSync.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-object-location.html\">Configuring DataSync transfers with an object storage system</a>.</p>"}, "UpdateLocationS3": {"name": "UpdateLocationS3", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateLocationS3Request"}, "output": {"shape": "UpdateLocationS3Response"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Modifies the following configuration parameters of the Amazon S3 transfer location that you're using with DataSync.</p> <important> <p>Before you begin, make sure that you read the following topics:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-s3-location.html#using-storage-classes\">Storage class considerations with Amazon S3 locations</a> </p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-s3-location.html#create-s3-location-s3-requests\">Evaluating S3 request costs when using DataSync</a> </p> </li> </ul> </important>"}, "UpdateLocationSmb": {"name": "UpdateLocationSmb", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateLocationSmbRequest"}, "output": {"shape": "UpdateLocationSmbResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Modifies the following configuration parameters of the Server Message Block (SMB) transfer location that you're using with DataSync.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-smb-location.html\">Configuring DataSync transfers with an SMB file server</a>.</p>"}, "UpdateTask": {"name": "UpdateTask", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateTaskRequest"}, "output": {"shape": "UpdateTaskResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Updates the configuration of a <i>task</i>, which defines where and how DataSync transfers your data.</p>"}, "UpdateTaskExecution": {"name": "UpdateTaskExecution", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateTaskExecutionRequest"}, "output": {"shape": "UpdateTaskExecutionResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalException"}], "documentation": "<p>Updates the configuration of a running DataSync task execution.</p> <note> <p>Currently, the only <code>Option</code> that you can modify with <code>UpdateTaskExecution</code> is <code> <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/API_Options.html#DataSync-Type-Options-BytesPerSecond\">BytesPerSecond</a> </code>, which throttles bandwidth for a running or queued task execution.</p> </note>"}}, "shapes": {"ActivationKey": {"type": "string", "max": 29, "pattern": "[A-Z0-9]{5}(-[A-Z0-9]{5}){4}"}, "AgentArn": {"type": "string", "max": 128, "pattern": "^arn:(aws|aws-cn|aws-us-gov|aws-iso|aws-iso-b):datasync:[a-z\\-0-9]+:[0-9]{12}:agent/agent-[0-9a-z]{17}$"}, "AgentArnList": {"type": "list", "member": {"shape": "AgentArn"}, "max": 4, "min": 1}, "AgentList": {"type": "list", "member": {"shape": "AgentListEntry"}}, "AgentListEntry": {"type": "structure", "members": {"AgentArn": {"shape": "AgentArn", "documentation": "<p>The Amazon Resource Name (ARN) of a DataSync agent.</p>"}, "Name": {"shape": "TagValue", "documentation": "<p>The name of an agent.</p>"}, "Status": {"shape": "AgentStatus", "documentation": "<p>The status of an agent.</p> <ul> <li> <p>If the status is <code>ONLINE</code>, the agent is configured properly and ready to use.</p> </li> <li> <p>If the status is <code>OFFLINE</code>, the agent has been out of contact with DataSync for five minutes or longer. This can happen for a few reasons. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/troubleshooting-datasync-agents.html#troubleshoot-agent-offline\">What do I do if my agent is offline?</a> </p> </li> </ul>"}, "Platform": {"shape": "Platform", "documentation": "<p>The platform-related details about the agent, such as the version number.</p>"}}, "documentation": "<p>Represents a single entry in a list (or array) of DataSync agents when you call the <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/API_ListAgents.html\">ListAgents</a> operation.</p>"}, "AgentStatus": {"type": "string", "enum": ["ONLINE", "OFFLINE"]}, "AgentVersion": {"type": "string", "max": 256, "min": 1, "pattern": "^[a-zA-Z0-9\\s+=._:@/-]+$"}, "Atime": {"type": "string", "enum": ["NONE", "BEST_EFFORT"]}, "AzureAccessTier": {"type": "string", "enum": ["HOT", "COOL", "ARCHIVE"]}, "AzureBlobAuthenticationType": {"type": "string", "enum": ["SAS", "NONE"]}, "AzureBlobContainerUrl": {"type": "string", "max": 325, "pattern": "^https:\\/\\/[A-Za-z0-9]((\\.|-+)?[A-Za-z0-9]){0,252}\\/[a-z0-9](-?[a-z0-9]){2,62}$"}, "AzureBlobSasConfiguration": {"type": "structure", "required": ["Token"], "members": {"Token": {"shape": "AzureBlobSasToken", "documentation": "<p>Specifies a SAS token that provides permissions to access your Azure Blob Storage.</p> <p>The token is part of the SAS URI string that comes after the storage resource URI and a question mark. A token looks something like this:</p> <p> <code>sp=r&amp;st=2023-12-20T14:54:52Z&amp;se=2023-12-20T22:54:52Z&amp;spr=https&amp;sv=2021-06-08&amp;sr=c&amp;sig=aBBKDWQvyuVcTPH9EBp%2FXTI9E%2F%2Fmq171%2BZU178wcwqU%3D</code> </p>"}}, "documentation": "<p>The shared access signature (SAS) configuration that allows DataSync to access your Microsoft Azure Blob Storage.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/creating-azure-blob-location.html#azure-blob-sas-tokens\">SAS tokens</a> for accessing your Azure Blob Storage.</p>"}, "AzureBlobSasToken": {"type": "string", "max": 255, "min": 1, "pattern": "^.+$", "sensitive": true}, "AzureBlobSubdirectory": {"type": "string", "max": 1024, "pattern": "^[\\p{L}\\p{M}\\p{Z}\\p{S}\\p{N}\\p{P}\\p{C}]*$"}, "AzureBlobType": {"type": "string", "enum": ["BLOCK"]}, "BytesPerSecond": {"type": "long", "min": -1}, "CancelTaskExecutionRequest": {"type": "structure", "required": ["TaskExecutionArn"], "members": {"TaskExecutionArn": {"shape": "TaskExecutionArn", "documentation": "<p>The Amazon Resource Name (ARN) of the task execution to stop.</p>"}}, "documentation": "<p>CancelTaskExecutionRequest</p>"}, "CancelTaskExecutionResponse": {"type": "structure", "members": {}}, "CmkSecretConfig": {"type": "structure", "members": {"SecretArn": {"shape": "SecretArn", "documentation": "<p>Specifies the ARN for the DataSync-managed Secrets Manager secret that that is used to access a specific storage location. This property is generated by DataSync and is read-only. DataSync encrypts this secret with the KMS key that you specify for <code>KmsKeyArn</code>.</p>"}, "KmsKeyArn": {"shape": "KmsKeyArn", "documentation": "<p>Specifies the ARN for the customer-managed KMS key that DataSync uses to encrypt the DataSync-managed secret stored for <code>SecretArn</code>. DataSync provides this key to Secrets Manager.</p>"}}, "documentation": "<p>Specifies configuration information for a DataSync-managed secret, such as an authentication token or secret key that DataSync uses to access a specific storage location, with a customer-managed KMS key.</p> <note> <p>You can use either <code>CmkSecretConfig</code> or <code>CustomSecretConfig</code> to provide credentials for a <code>CreateLocation</code> request. Do not provide both parameters for the same request.</p> </note>"}, "CreateAgentRequest": {"type": "structure", "required": ["ActivationKey"], "members": {"ActivationKey": {"shape": "ActivationKey", "documentation": "<p>Specifies your DataSync agent's activation key. If you don't have an activation key, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/activate-agent.html\">Activating your agent</a>.</p>"}, "AgentName": {"shape": "TagValue", "documentation": "<p>Specifies a name for your agent. We recommend specifying a name that you can remember.</p>"}, "Tags": {"shape": "InputTagList", "documentation": "<p>Specifies labels that help you categorize, filter, and search for your Amazon Web Services resources. We recommend creating at least one tag for your agent.</p>"}, "VpcEndpointId": {"shape": "VpcEndpointId", "documentation": "<p>Specifies the ID of the <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/choose-service-endpoint.html#datasync-in-vpc\">VPC service endpoint</a> that you're using. For example, a VPC endpoint ID looks like <code>vpce-01234d5aff67890e1</code>.</p> <important> <p>The VPC service endpoint you use must include the DataSync service name (for example, <code>com.amazonaws.us-east-2.datasync</code>).</p> </important>"}, "SubnetArns": {"shape": "PLSubnetArnList", "documentation": "<p>Specifies the ARN of the subnet where your VPC service endpoint is located. You can only specify one ARN.</p>"}, "SecurityGroupArns": {"shape": "PLSecurityGroupArnList", "documentation": "<p>Specifies the Amazon Resource Name (ARN) of the security group that allows traffic between your agent and VPC service endpoint. You can only specify one ARN.</p>"}}, "documentation": "<p>CreateAgentRequest</p>"}, "CreateAgentResponse": {"type": "structure", "members": {"AgentArn": {"shape": "AgentArn", "documentation": "<p>The ARN of the agent that you just activated. Use the <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/API_ListAgents.html\">ListAgents</a> operation to return a list of agents in your Amazon Web Services account and Amazon Web Services Region.</p>"}}, "documentation": "<p>CreateAgentResponse</p>"}, "CreateLocationAzureBlobRequest": {"type": "structure", "required": ["ContainerUrl", "AuthenticationType"], "members": {"ContainerUrl": {"shape": "AzureBlobContainerUrl", "documentation": "<p>Specifies the URL of the Azure Blob Storage container involved in your transfer.</p>"}, "AuthenticationType": {"shape": "AzureBlobAuthenticationType", "documentation": "<p>Specifies the authentication method DataSync uses to access your Azure Blob Storage. DataSync can access blob storage using a shared access signature (SAS).</p>"}, "SasConfiguration": {"shape": "AzureBlobSasConfiguration", "documentation": "<p>Specifies the SAS configuration that allows DataSync to access your Azure Blob Storage.</p> <note> <p>If you provide an authentication token using <code>SasConfiguration</code>, but do not provide secret configuration details using <code>CmkSecretConfig</code> or <code>CustomSecretConfig</code>, then DataSync stores the token using your Amazon Web Services account's secrets manager secret.</p> </note>"}, "BlobType": {"shape": "AzureBlobType", "documentation": "<p>Specifies the type of blob that you want your objects or files to be when transferring them into Azure Blob Storage. Currently, DataSync only supports moving data into Azure Blob Storage as block blobs. For more information on blob types, see the <a href=\"https://learn.microsoft.com/en-us/rest/api/storageservices/understanding-block-blobs--append-blobs--and-page-blobs\">Azure Blob Storage documentation</a>.</p>"}, "AccessTier": {"shape": "AzureAccessTier", "documentation": "<p>Specifies the access tier that you want your objects or files transferred into. This only applies when using the location as a transfer destination. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/creating-azure-blob-location.html#azure-blob-access-tiers\">Access tiers</a>.</p>"}, "Subdirectory": {"shape": "AzureBlobSubdirectory", "documentation": "<p>Specifies path segments if you want to limit your transfer to a virtual directory in your container (for example, <code>/my/images</code>).</p>"}, "AgentArns": {"shape": "AgentArnList", "documentation": "<p>(Optional) Specifies the Amazon Resource Name (ARN) of the DataSync agent that can connect with your Azure Blob Storage container. If you are setting up an agentless cross-cloud transfer, you do not need to specify a value for this parameter.</p> <p>You can specify more than one agent. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/multiple-agents.html\">Using multiple agents for your transfer</a>.</p> <note> <p>Make sure you configure this parameter correctly when you first create your storage location. You cannot add or remove agents from a storage location after you create it.</p> </note>"}, "Tags": {"shape": "InputTagList", "documentation": "<p>Specifies labels that help you categorize, filter, and search for your Amazon Web Services resources. We recommend creating at least a name tag for your transfer location.</p>"}, "CmkSecretConfig": {"shape": "CmkSecretConfig", "documentation": "<p>Specifies configuration information for a DataSync-managed secret, which includes the authentication token that <PERSON><PERSON><PERSON> uses to access a specific AzureBlob storage location, with a customer-managed KMS key.</p> <p>When you include this paramater as part of a <code>CreateLocationAzureBlob</code> request, you provide only the KMS key ARN. DataSync uses this KMS key together with the authentication token you specify for <code>SasConfiguration</code> to create a DataSync-managed secret to store the location access credentials.</p> <p>Make sure the DataSync has permission to access the KMS key that you specify.</p> <note> <p>You can use either <code>CmkSecretConfig</code> (with <code>SasConfiguration</code>) or <code>CustomSecretConfig</code> (without <code>SasConfiguration</code>) to provide credentials for a <code>CreateLocationAzureBlob</code> request. Do not provide both parameters for the same request.</p> </note>"}, "CustomSecretConfig": {"shape": "CustomSecretConfig", "documentation": "<p>Specifies configuration information for a customer-managed Secrets Manager secret where the authentication token for an AzureBlob storage location is stored in plain text. This configuration includes the secret ARN, and the ARN for an IAM role that provides access to the secret.</p> <note> <p>You can use either <code>CmkSecretConfig</code> (with <code>SasConfiguration</code>) or <code>CustomSecretConfig</code> (without <code>SasConfiguration</code>) to provide credentials for a <code>CreateLocationAzureBlob</code> request. Do not provide both parameters for the same request.</p> </note>"}}}, "CreateLocationAzureBlobResponse": {"type": "structure", "members": {"LocationArn": {"shape": "LocationArn", "documentation": "<p>The ARN of the Azure Blob Storage transfer location that you created.</p>"}}}, "CreateLocationEfsRequest": {"type": "structure", "required": ["EfsFilesystemArn", "Ec2Config"], "members": {"Subdirectory": {"shape": "EfsSubdirectory", "documentation": "<p>Specifies a mount path for your Amazon EFS file system. This is where DataSync reads or writes data on your file system (depending on if this is a source or destination location).</p> <p>By default, DataSync uses the root directory (or <a href=\"https://docs.aws.amazon.com/efs/latest/ug/efs-access-points.html\">access point</a> if you provide one by using <code>AccessPointArn</code>). You can also include subdirectories using forward slashes (for example, <code>/path/to/folder</code>).</p>"}, "EfsFilesystemArn": {"shape": "EfsFilesystemArn", "documentation": "<p>Specifies the ARN for your Amazon EFS file system.</p>"}, "Ec2Config": {"shape": "Ec2Config", "documentation": "<p>Specifies the subnet and security groups DataSync uses to connect to one of your Amazon EFS file system's <a href=\"https://docs.aws.amazon.com/efs/latest/ug/accessing-fs.html\">mount targets</a>.</p>"}, "Tags": {"shape": "InputTagList", "documentation": "<p>Specifies the key-value pair that represents a tag that you want to add to the resource. The value can be an empty string. This value helps you manage, filter, and search for your resources. We recommend that you create a name tag for your location.</p>"}, "AccessPointArn": {"shape": "EfsAccessPointArn", "documentation": "<p>Specifies the Amazon Resource Name (ARN) of the access point that DataSync uses to mount your Amazon EFS file system.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-efs-location.html#create-efs-location-iam\">Accessing restricted file systems</a>.</p>"}, "FileSystemAccessRoleArn": {"shape": "IamRoleArn", "documentation": "<p>Specifies an Identity and Access Management (IAM) role that allows DataSync to access your Amazon EFS file system.</p> <p>For information on creating this role, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-efs-location.html#create-efs-location-iam-role\">Creating a DataSync IAM role for file system access</a>.</p>"}, "InTransitEncryption": {"shape": "EfsInTransitEncryption", "documentation": "<p>Specifies whether you want DataSync to use Transport Layer Security (TLS) 1.2 encryption when it transfers data to or from your Amazon EFS file system.</p> <p>If you specify an access point using <code>AccessPointArn</code> or an IAM role using <code>FileSystemAccessRoleArn</code>, you must set this parameter to <code>TLS1_2</code>.</p>"}}, "documentation": "<p>CreateLocationEfsRequest</p>"}, "CreateLocationEfsResponse": {"type": "structure", "members": {"LocationArn": {"shape": "LocationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the Amazon EFS file system location that you create.</p>"}}, "documentation": "<p>CreateLocationEfs</p>"}, "CreateLocationFsxLustreRequest": {"type": "structure", "required": ["FsxFilesystemArn", "SecurityGroupArns"], "members": {"FsxFilesystemArn": {"shape": "FsxFilesystemArn", "documentation": "<p>Specifies the Amazon Resource Name (ARN) of the FSx for Lustre file system.</p>"}, "SecurityGroupArns": {"shape": "Ec2SecurityGroupArnList", "documentation": "<p>Specifies the Amazon Resource Names (ARNs) of up to five security groups that provide access to your FSx for Lustre file system.</p> <p>The security groups must be able to access the file system's ports. The file system must also allow access from the security groups. For information about file system access, see the <a href=\"https://docs.aws.amazon.com/fsx/latest/LustreGuide/limit-access-security-groups.html\"> <i>Amazon FSx for Lustre User Guide</i> </a>.</p>"}, "Subdirectory": {"shape": "FsxLustreSubdirectory", "documentation": "<p>Specifies a mount path for your FSx for Lustre file system. The path can include subdirectories.</p> <p>When the location is used as a source, DataSync reads data from the mount path. When the location is used as a destination, DataSync writes data to the mount path. If you don't include this parameter, DataSync uses the file system's root directory (<code>/</code>).</p>"}, "Tags": {"shape": "InputTagList", "documentation": "<p>Specifies labels that help you categorize, filter, and search for your Amazon Web Services resources. We recommend creating at least a name tag for your location.</p>"}}}, "CreateLocationFsxLustreResponse": {"type": "structure", "members": {"LocationArn": {"shape": "LocationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the FSx for Lustre file system location that you created. </p>"}}}, "CreateLocationFsxOntapRequest": {"type": "structure", "required": ["Protocol", "SecurityGroupArns", "StorageVirtualMachineArn"], "members": {"Protocol": {"shape": "FsxProtocol"}, "SecurityGroupArns": {"shape": "Ec2SecurityGroupArnList", "documentation": "<p>Specifies the Amazon EC2 security groups that provide access to your file system's preferred subnet.</p> <p>The security groups must allow outbound traffic on the following ports (depending on the protocol you use):</p> <ul> <li> <p> <b>Network File System (NFS)</b>: TCP ports 111, 635, and 2049</p> </li> <li> <p> <b>Server Message Block (SMB)</b>: TCP port 445</p> </li> </ul> <p>Your file system's security groups must also allow inbound traffic on the same ports.</p>"}, "StorageVirtualMachineArn": {"shape": "StorageVirtualMachineArn", "documentation": "<p>Specifies the ARN of the storage virtual machine (SVM) in your file system where you want to copy data to or from.</p>"}, "Subdirectory": {"shape": "FsxOntapSubdirectory", "documentation": "<p>Specifies a path to the file share in the SVM where you want to transfer data to or from.</p> <p>You can specify a junction path (also known as a mount point), qtree path (for NFS file shares), or share name (for SMB file shares). For example, your mount path might be <code>/vol1</code>, <code>/vol1/tree1</code>, or <code>/share1</code>.</p> <note> <p>Don't specify a junction path in the SVM's root volume. For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/managing-svms.html\">Managing FSx for ONTAP storage virtual machines</a> in the <i>Amazon FSx for NetApp ONTAP User Guide</i>.</p> </note>"}, "Tags": {"shape": "InputTagList", "documentation": "<p>Specifies labels that help you categorize, filter, and search for your Amazon Web Services resources. We recommend creating at least a name tag for your location.</p>"}}}, "CreateLocationFsxOntapResponse": {"type": "structure", "members": {"LocationArn": {"shape": "LocationArn", "documentation": "<p>Specifies the ARN of the FSx for ONTAP file system location that you create.</p>"}}}, "CreateLocationFsxOpenZfsRequest": {"type": "structure", "required": ["FsxFilesystemArn", "Protocol", "SecurityGroupArns"], "members": {"FsxFilesystemArn": {"shape": "FsxFilesystemArn", "documentation": "<p>The Amazon Resource Name (ARN) of the FSx for OpenZFS file system.</p>"}, "Protocol": {"shape": "FsxProtocol", "documentation": "<p>The type of protocol that DataSync uses to access your file system.</p>"}, "SecurityGroupArns": {"shape": "Ec2SecurityGroupArnList", "documentation": "<p>The ARNs of the security groups that are used to configure the FSx for OpenZFS file system.</p>"}, "Subdirectory": {"shape": "FsxOpenZfsSubdirectory", "documentation": "<p>A subdirectory in the location's path that must begin with <code>/fsx</code>. DataSync uses this subdirectory to read or write data (depending on whether the file system is a source or destination location).</p>"}, "Tags": {"shape": "InputTagList", "documentation": "<p>The key-value pair that represents a tag that you want to add to the resource. The value can be an empty string. This value helps you manage, filter, and search for your resources. We recommend that you create a name tag for your location.</p>"}}}, "CreateLocationFsxOpenZfsResponse": {"type": "structure", "members": {"LocationArn": {"shape": "LocationArn", "documentation": "<p>The ARN of the FSx for OpenZFS file system location that you created.</p>"}}}, "CreateLocationFsxWindowsRequest": {"type": "structure", "required": ["FsxFilesystemArn", "SecurityGroupArns", "User", "Password"], "members": {"Subdirectory": {"shape": "FsxWindowsSubdirectory", "documentation": "<p>Specifies a mount path for your file system using forward slashes. This is where DataSync reads or writes data (depending on if this is a source or destination location).</p>"}, "FsxFilesystemArn": {"shape": "FsxFilesystemArn", "documentation": "<p>Specifies the Amazon Resource Name (ARN) for the FSx for Windows File Server file system.</p>"}, "SecurityGroupArns": {"shape": "Ec2SecurityGroupArnList", "documentation": "<p>Specifies the ARNs of the Amazon EC2 security groups that provide access to your file system's preferred subnet.</p> <p>The security groups that you specify must be able to communicate with your file system's security groups. For information about configuring security groups for file system access, see the <a href=\"https://docs.aws.amazon.com/fsx/latest/WindowsGuide/limit-access-security-groups.html\"> <i>Amazon FSx for Windows File Server User Guide</i> </a>.</p> <note> <p>If you choose a security group that doesn't allow connections from within itself, do one of the following:</p> <ul> <li> <p>Configure the security group to allow it to communicate within itself.</p> </li> <li> <p>Choose a different security group that can communicate with the mount target's security group.</p> </li> </ul> </note>"}, "Tags": {"shape": "InputTagList", "documentation": "<p>Specifies labels that help you categorize, filter, and search for your Amazon Web Services resources. We recommend creating at least a name tag for your location.</p>"}, "User": {"shape": "SmbUser", "documentation": "<p>Specifies the user with the permissions to mount and access the files, folders, and file metadata in your FSx for Windows File Server file system.</p> <p>For information about choosing a user with the right level of access for your transfer, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-fsx-location.html#create-fsx-windows-location-permissions\">required permissions</a> for FSx for Windows File Server locations.</p>"}, "Domain": {"shape": "SmbDomain", "documentation": "<p>Specifies the name of the Windows domain that the FSx for Windows File Server file system belongs to.</p> <p>If you have multiple Active Directory domains in your environment, configuring this parameter makes sure that DataSync connects to the right file system.</p>"}, "Password": {"shape": "SmbPassword", "documentation": "<p>Specifies the password of the user with the permissions to mount and access the files, folders, and file metadata in your FSx for Windows File Server file system.</p>"}}}, "CreateLocationFsxWindowsResponse": {"type": "structure", "members": {"LocationArn": {"shape": "LocationArn", "documentation": "<p>The ARN of the FSx for Windows File Server file system location you created.</p>"}}}, "CreateLocationHdfsRequest": {"type": "structure", "required": ["NameNodes", "AuthenticationType", "AgentArns"], "members": {"Subdirectory": {"shape": "HdfsSubdirectory", "documentation": "<p>A subdirectory in the HDFS cluster. This subdirectory is used to read data from or write data to the HDFS cluster. If the subdirectory isn't specified, it will default to <code>/</code>.</p>"}, "NameNodes": {"shape": "HdfsNameNodeList", "documentation": "<p>The NameNode that manages the HDFS namespace. The NameNode performs operations such as opening, closing, and renaming files and directories. The NameNode contains the information to map blocks of data to the DataNodes. You can use only one NameNode.</p>"}, "BlockSize": {"shape": "HdfsBlockSize", "documentation": "<p>The size of data blocks to write into the HDFS cluster. The block size must be a multiple of 512 bytes. The default block size is 128 mebibytes (MiB).</p>"}, "ReplicationFactor": {"shape": "HdfsReplicationFactor", "documentation": "<p>The number of DataNodes to replicate the data to when writing to the HDFS cluster. By default, data is replicated to three DataNodes.</p>"}, "KmsKeyProviderUri": {"shape": "KmsKeyProviderUri", "documentation": "<p>The URI of the HDFS cluster's Key Management Server (KMS). </p>"}, "QopConfiguration": {"shape": "QopConfiguration", "documentation": "<p>The Quality of Protection (QOP) configuration specifies the Remote Procedure Call (RPC) and data transfer protection settings configured on the Hadoop Distributed File System (HDFS) cluster. If <code>QopConfiguration</code> isn't specified, <code>RpcProtection</code> and <code>DataTransferProtection</code> default to <code>PRIVACY</code>. If you set <code>RpcProtection</code> or <code>DataTransferProtection</code>, the other parameter assumes the same value. </p>"}, "AuthenticationType": {"shape": "HdfsAuthenticationType", "documentation": "<p>The type of authentication used to determine the identity of the user. </p>"}, "SimpleUser": {"shape": "HdfsUser", "documentation": "<p>The user name used to identify the client on the host operating system. </p> <note> <p>If <code>SIMPLE</code> is specified for <code>AuthenticationType</code>, this parameter is required. </p> </note>"}, "KerberosPrincipal": {"shape": "KerberosPrincipal", "documentation": "<p>The Kerberos principal with access to the files and folders on the HDFS cluster. </p> <note> <p>If <code>KERBEROS</code> is specified for <code>AuthenticationType</code>, this parameter is required.</p> </note>"}, "KerberosKeytab": {"shape": "KerberosKeytabFile", "documentation": "<p>The Kerberos key table (keytab) that contains mappings between the defined Kerberos principal and the encrypted keys. You can load the keytab from a file by providing the file's address.</p> <note> <p>If <code>KERBEROS</code> is specified for <code>AuthenticationType</code>, this parameter is required. </p> </note>"}, "KerberosKrb5Conf": {"shape": "KerberosKrb5ConfFile", "documentation": "<p>The <code>krb5.conf</code> file that contains the Kerberos configuration information. You can load the <code>krb5.conf</code> file by providing the file's address. If you're using the CLI, it performs the base64 encoding for you. Otherwise, provide the base64-encoded text. </p> <note> <p>If <code>KERBEROS</code> is specified for <code>AuthenticationType</code>, this parameter is required.</p> </note>"}, "AgentArns": {"shape": "AgentArnList", "documentation": "<p>The Amazon Resource Names (ARNs) of the DataSync agents that can connect to your HDFS cluster.</p>"}, "Tags": {"shape": "InputTagList", "documentation": "<p>The key-value pair that represents the tag that you want to add to the location. The value can be an empty string. We recommend using tags to name your resources. </p>"}}}, "CreateLocationHdfsResponse": {"type": "structure", "members": {"LocationArn": {"shape": "LocationArn", "documentation": "<p>The ARN of the source HDFS cluster location that you create.</p>"}}}, "CreateLocationNfsRequest": {"type": "structure", "required": ["Subdirectory", "ServerHostname", "OnPremConfig"], "members": {"Subdirectory": {"shape": "NfsSubdirectory", "documentation": "<p>Specifies the export path in your NFS file server that you want DataSync to mount.</p> <p>This path (or a subdirectory of the path) is where DataSync transfers data to or from. For information on configuring an export for DataSync, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-nfs-location.html#accessing-nfs\">Accessing NFS file servers</a>.</p>"}, "ServerHostname": {"shape": "ServerHostname", "documentation": "<p>Specifies the DNS name or IP version 4 address of the NFS file server that your DataSync agent connects to.</p>"}, "OnPremConfig": {"shape": "OnPremConfig", "documentation": "<p>Specifies the Amazon Resource Name (ARN) of the DataSync agent that can connect to your NFS file server.</p> <p>You can specify more than one agent. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/do-i-need-datasync-agent.html#multiple-agents\">Using multiple DataSync agents</a>.</p>"}, "MountOptions": {"shape": "NfsMountOptions", "documentation": "<p>Specifies the options that DataSync can use to mount your NFS file server.</p>"}, "Tags": {"shape": "InputTagList", "documentation": "<p>Specifies labels that help you categorize, filter, and search for your Amazon Web Services resources. We recommend creating at least a name tag for your location.</p>"}}, "documentation": "<p>CreateLocationNfsRequest</p>"}, "CreateLocationNfsResponse": {"type": "structure", "members": {"LocationArn": {"shape": "LocationArn", "documentation": "<p>The ARN of the transfer location that you created for your NFS file server.</p>"}}, "documentation": "<p>CreateLocationNfsResponse</p>"}, "CreateLocationObjectStorageRequest": {"type": "structure", "required": ["ServerHostname", "BucketName"], "members": {"ServerHostname": {"shape": "ServerHostname", "documentation": "<p>Specifies the domain name or IP version 4 (IPv4) address of the object storage server that your DataSync agent connects to.</p>"}, "ServerPort": {"shape": "ObjectStorageServerPort", "documentation": "<p>Specifies the port that your object storage server accepts inbound network traffic on (for example, port 443).</p>"}, "ServerProtocol": {"shape": "ObjectStorageServerProtocol", "documentation": "<p>Specifies the protocol that your object storage server uses to communicate.</p>"}, "Subdirectory": {"shape": "S3Subdirectory", "documentation": "<p>Specifies the object prefix for your object storage server. If this is a source location, DataSync only copies objects with this prefix. If this is a destination location, DataSync writes all objects with this prefix. </p>"}, "BucketName": {"shape": "ObjectStorageBucketName", "documentation": "<p>Specifies the name of the object storage bucket involved in the transfer.</p>"}, "AccessKey": {"shape": "ObjectStorageAccessKey", "documentation": "<p>Specifies the access key (for example, a user name) if credentials are required to authenticate with the object storage server.</p>"}, "SecretKey": {"shape": "ObjectStorageSecretKey", "documentation": "<p>Specifies the secret key (for example, a password) if credentials are required to authenticate with the object storage server.</p>"}, "AgentArns": {"shape": "AgentArnList", "documentation": "<p>(Optional) Specifies the Amazon Resource Names (ARNs) of the DataSync agents that can connect with your object storage system. If you are setting up an agentless cross-cloud transfer, you do not need to specify a value for this parameter.</p> <note> <p>Make sure you configure this parameter correctly when you first create your storage location. You cannot add or remove agents from a storage location after you create it.</p> </note>"}, "Tags": {"shape": "InputTagList", "documentation": "<p>Specifies the key-value pair that represents a tag that you want to add to the resource. Tags can help you manage, filter, and search for your resources. We recommend creating a name tag for your location.</p>"}, "ServerCertificate": {"shape": "ObjectStorageCertificate", "documentation": "<p>Specifies a certificate chain for DataSync to authenticate with your object storage system if the system uses a private or self-signed certificate authority (CA). You must specify a single <code>.pem</code> file with a full certificate chain (for example, <code>file:///home/<USER>/.ssh/object_storage_certificates.pem</code>).</p> <p>The certificate chain might include:</p> <ul> <li> <p>The object storage system's certificate</p> </li> <li> <p>All intermediate certificates (if there are any)</p> </li> <li> <p>The root certificate of the signing CA</p> </li> </ul> <p>You can concatenate your certificates into a <code>.pem</code> file (which can be up to 32768 bytes before base64 encoding). The following example <code>cat</code> command creates an <code>object_storage_certificates.pem</code> file that includes three certificates:</p> <p> <code>cat object_server_certificate.pem intermediate_certificate.pem ca_root_certificate.pem &gt; object_storage_certificates.pem</code> </p> <p>To use this parameter, configure <code>ServerProtocol</code> to <code>HTTPS</code>.</p>"}, "CmkSecretConfig": {"shape": "CmkSecretConfig", "documentation": "<p>Specifies configuration information for a DataSync-managed secret, which includes the <code>SecretKey</code> that DataSync uses to access a specific object storage location, with a customer-managed KMS key.</p> <p>When you include this paramater as part of a <code>CreateLocationObjectStorage</code> request, you provide only the KMS key ARN. DataSync uses this KMS key together with the value you specify for the <code>SecretKey</code> parameter to create a DataSync-managed secret to store the location access credentials.</p> <p>Make sure the DataSync has permission to access the KMS key that you specify.</p> <note> <p>You can use either <code>CmkSecretConfig</code> (with <code><PERSON><PERSON><PERSON></code>) or <code>CustomSecretConfig</code> (without <code><PERSON><PERSON><PERSON></code>) to provide credentials for a <code>CreateLocationObjectStorage</code> request. Do not provide both parameters for the same request.</p> </note>"}, "CustomSecretConfig": {"shape": "CustomSecretConfig", "documentation": "<p>Specifies configuration information for a customer-managed Secrets Manager secret where the secret key for a specific object storage location is stored in plain text. This configuration includes the secret ARN, and the ARN for an IAM role that provides access to the secret.</p> <note> <p>You can use either <code>CmkSecretConfig</code> (with <code>SecretKey</code>) or <code>CustomSecretConfig</code> (without <code>SecretKey</code>) to provide credentials for a <code>CreateLocationObjectStorage</code> request. Do not provide both parameters for the same request.</p> </note>"}}, "documentation": "<p>CreateLocationObjectStorageRequest</p>"}, "CreateLocationObjectStorageResponse": {"type": "structure", "members": {"LocationArn": {"shape": "LocationArn", "documentation": "<p>Specifies the ARN of the object storage system location that you create.</p>"}}, "documentation": "<p>CreateLocationObjectStorageResponse</p>"}, "CreateLocationS3Request": {"type": "structure", "required": ["S3BucketArn", "S3Config"], "members": {"Subdirectory": {"shape": "S3Subdirectory", "documentation": "<p>Specifies a prefix in the S3 bucket that DataS<PERSON> reads from or writes to (depending on whether the bucket is a source or destination location).</p> <note> <p>DataSync can't transfer objects with a prefix that begins with a slash (<code>/</code>) or includes <code>//</code>, <code>/./</code>, or <code>/../</code> patterns. For example:</p> <ul> <li> <p> <code>/photos</code> </p> </li> <li> <p> <code>photos//2006/January</code> </p> </li> <li> <p> <code>photos/./2006/February</code> </p> </li> <li> <p> <code>photos/../2006/March</code> </p> </li> </ul> </note>"}, "S3BucketArn": {"shape": "S3BucketArn", "documentation": "<p>Specifies the ARN of the S3 bucket that you want to use as a location. (When creating your DataSync task later, you specify whether this location is a transfer source or destination.) </p> <p>If your S3 bucket is located on an Outposts resource, you must specify an Amazon S3 access point. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/access-points.html\">Managing data access with Amazon S3 access points</a> in the <i>Amazon S3 User Guide</i>.</p>"}, "S3StorageClass": {"shape": "S3StorageClass", "documentation": "<p>Specifies the storage class that you want your objects to use when Amazon S3 is a transfer destination.</p> <p>For buckets in Amazon Web Services Regions, the storage class defaults to <code>STANDARD</code>. For buckets on Outposts, the storage class defaults to <code>OUTPOSTS</code>.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-s3-location.html#using-storage-classes\">Storage class considerations with Amazon S3 transfers</a>.</p>"}, "S3Config": {"shape": "S3Config"}, "AgentArns": {"shape": "AgentArnList", "documentation": "<p>(Amazon S3 on Outposts only) Specifies the Amazon Resource Name (ARN) of the DataSync agent on your Outpost.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/deploy-agents.html#outposts-agent\">Deploy your DataSync agent on Outposts</a>.</p>"}, "Tags": {"shape": "InputTagList", "documentation": "<p>Specifies labels that help you categorize, filter, and search for your Amazon Web Services resources. We recommend creating at least a name tag for your transfer location.</p>"}}, "documentation": "<p>CreateLocationS3Request</p>"}, "CreateLocationS3Response": {"type": "structure", "members": {"LocationArn": {"shape": "LocationArn", "documentation": "<p>The ARN of the S3 location that you created.</p>"}}, "documentation": "<p>CreateLocationS3Response</p>"}, "CreateLocationSmbRequest": {"type": "structure", "required": ["Subdirectory", "ServerHostname", "AgentArns"], "members": {"Subdirectory": {"shape": "SmbSubdirectory", "documentation": "<p>Specifies the name of the share exported by your SMB file server where DataSync will read or write data. You can include a subdirectory in the share path (for example, <code>/path/to/subdirectory</code>). Make sure that other SMB clients in your network can also mount this path.</p> <p>To copy all data in the subdirectory, DataSync must be able to mount the SMB share and access all of its data. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-smb-location.html#configuring-smb-permissions\">Providing DataSync access to SMB file servers</a>.</p>"}, "ServerHostname": {"shape": "ServerHostname", "documentation": "<p>Specifies the domain name or IP address of the SMB file server that your DataSync agent connects to.</p> <p>Remember the following when configuring this parameter:</p> <ul> <li> <p>You can't specify an IP version 6 (IPv6) address.</p> </li> <li> <p>If you're using Kerberos authentication, you must specify a domain name.</p> </li> </ul>"}, "User": {"shape": "SmbUser", "documentation": "<p>Specifies the user that can mount and access the files, folders, and file metadata in your SMB file server. This parameter applies only if <code>AuthenticationType</code> is set to <code>NTLM</code>.</p> <p>For information about choosing a user with the right level of access for your transfer, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-smb-location.html#configuring-smb-permissions\">Providing DataSync access to SMB file servers</a>.</p>"}, "Domain": {"shape": "SmbDomain", "documentation": "<p>Specifies the Windows domain name that your SMB file server belongs to. This parameter applies only if <code>AuthenticationType</code> is set to <code>NTLM</code>.</p> <p>If you have multiple domains in your environment, configuring this parameter makes sure that DataSync connects to the right file server.</p>"}, "Password": {"shape": "SmbPassword", "documentation": "<p>Specifies the password of the user who can mount your SMB file server and has permission to access the files and folders involved in your transfer. This parameter applies only if <code>AuthenticationType</code> is set to <code>NTLM</code>.</p>"}, "AgentArns": {"shape": "AgentArnList", "documentation": "<p>Specifies the DataSync agent (or agents) that can connect to your SMB file server. You specify an agent by using its Amazon Resource Name (ARN).</p>"}, "MountOptions": {"shape": "SmbMountOptions", "documentation": "<p>Specifies the version of the SMB protocol that DataSync uses to access your SMB file server.</p>"}, "Tags": {"shape": "InputTagList", "documentation": "<p>Specifies labels that help you categorize, filter, and search for your Amazon Web Services resources. We recommend creating at least a name tag for your location.</p>"}, "AuthenticationType": {"shape": "SmbAuthenticationType", "documentation": "<p>Specifies the authentication protocol that DataSync uses to connect to your SMB file server. DataSync supports <code>NTLM</code> (default) and <code>KERBEROS</code> authentication.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-smb-location.html#configuring-smb-permissions\">Providing DataSync access to SMB file servers</a>.</p>"}, "DnsIpAddresses": {"shape": "DnsIpList", "documentation": "<p>Specifies the IPv4 addresses for the DNS servers that your SMB file server belongs to. This parameter applies only if <code>AuthenticationType</code> is set to <code>KERBEROS</code>.</p> <p>If you have multiple domains in your environment, configuring this parameter makes sure that DataSync connects to the right SMB file server.</p>"}, "KerberosPrincipal": {"shape": "KerberosPrincipal", "documentation": "<p>Specifies a Kerberos prinicpal, which is an identity in your Kerberos realm that has permission to access the files, folders, and file metadata in your SMB file server.</p> <p>A Kerberos principal might look like <code>HOST/<EMAIL></code>.</p> <p>Principal names are case sensitive. Your DataSync task execution will fail if the principal that you specify for this parameter doesn’t exactly match the principal that you use to create the keytab file.</p>"}, "KerberosKeytab": {"shape": "KerberosKeytabFile", "documentation": "<p>Specifies your Kerberos key table (keytab) file, which includes mappings between your Kerberos principal and encryption keys.</p> <p>To avoid task execution errors, make sure that the Kerberos principal that you use to create the keytab file matches exactly what you specify for <code>KerberosPrincipal</code>. </p>"}, "KerberosKrb5Conf": {"shape": "KerberosKrb5ConfFile", "documentation": "<p>Specifies a Kerberos configuration file (<code>krb5.conf</code>) that defines your Kerberos realm configuration.</p> <p>The file must be base64 encoded. If you're using the CLI, the encoding is done for you.</p>"}}, "documentation": "<p>CreateLocationSmbRequest</p>"}, "CreateLocationSmbResponse": {"type": "structure", "members": {"LocationArn": {"shape": "LocationArn", "documentation": "<p>The ARN of the SMB location that you created.</p>"}}, "documentation": "<p>CreateLocationSmbResponse</p>"}, "CreateTaskRequest": {"type": "structure", "required": ["SourceLocationArn", "DestinationLocationArn"], "members": {"SourceLocationArn": {"shape": "LocationArn", "documentation": "<p>Specifies the ARN of your transfer's source location.</p>"}, "DestinationLocationArn": {"shape": "LocationArn", "documentation": "<p>Specifies the ARN of your transfer's destination location. </p>"}, "CloudWatchLogGroupArn": {"shape": "LogGroupArn", "documentation": "<p>Specifies the Amazon Resource Name (ARN) of an Amazon CloudWatch log group for monitoring your task.</p> <p>For Enhanced mode tasks, you don't need to specify anything. DataSync automatically sends logs to a CloudWatch log group named <code>/aws/datasync</code>.</p>"}, "Name": {"shape": "TagValue", "documentation": "<p>Specifies the name of your task.</p>"}, "Options": {"shape": "Options", "documentation": "<p>Specifies your task's settings, such as preserving file metadata, verifying data integrity, among other options.</p>"}, "Excludes": {"shape": "FilterList", "documentation": "<p>Specifies exclude filters that define the files, objects, and folders in your source location that you don't want DataSync to transfer. For more information and examples, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/filtering.html\">Specifying what DataSync transfers by using filters</a>.</p>"}, "Schedule": {"shape": "TaskSchedule", "documentation": "<p>Specifies a schedule for when you want your task to run. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/task-scheduling.html\">Scheduling your task</a>.</p>"}, "Tags": {"shape": "InputTagList", "documentation": "<p>Specifies the tags that you want to apply to your task.</p> <p> <i>Tags</i> are key-value pairs that help you manage, filter, and search for your DataSync resources.</p>"}, "Includes": {"shape": "FilterList", "documentation": "<p>Specifies include filters that define the files, objects, and folders in your source location that you want DataSync to transfer. For more information and examples, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/filtering.html\">Specifying what DataSync transfers by using filters</a>.</p>"}, "ManifestConfig": {"shape": "ManifestConfig", "documentation": "<p>Configures a manifest, which is a list of files or objects that you want DataSync to transfer. For more information and configuration examples, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/transferring-with-manifest.html\">Specifying what DataSync transfers by using a manifest</a>.</p> <p>When using this parameter, your caller identity (the role that you're using DataSync with) must have the <code>iam:PassRole</code> permission. The <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/security-iam-awsmanpol.html#security-iam-awsmanpol-awsdatasyncfullaccess\">AWSDataSyncFullAccess</a> policy includes this permission.</p>"}, "TaskReportConfig": {"shape": "TaskReportConfig", "documentation": "<p>Specifies how you want to configure a task report, which provides detailed information about your DataSync transfer. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/task-reports.html\">Monitoring your DataSync transfers with task reports</a>.</p> <p>When using this parameter, your caller identity (the role that you're using DataSync with) must have the <code>iam:PassRole</code> permission. The <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/security-iam-awsmanpol.html#security-iam-awsmanpol-awsdatasyncfullaccess\">AWSDataSyncFullAccess</a> policy includes this permission.</p>"}, "TaskMode": {"shape": "TaskMode", "documentation": "<p>Specifies one of the following task modes for your data transfer:</p> <ul> <li> <p> <code>ENHANCED</code> - Transfer virtually unlimited numbers of objects with higher performance than Basic mode. Enhanced mode tasks optimize the data transfer process by listing, preparing, transferring, and verifying data in parallel. Enhanced mode is currently available for transfers between Amazon S3 locations, transfers between Azure Blob and Amazon S3 without an agent, and transfers between other clouds and Amazon S3 without an agent.</p> <note> <p>To create an Enhanced mode task, the IAM role that you use to call the <code>CreateTask</code> operation must have the <code>iam:CreateServiceLinkedRole</code> permission.</p> </note> </li> <li> <p> <code>BASIC</code> (default) - Transfer files or objects between Amazon Web Services storage and all other supported DataSync locations. Basic mode tasks are subject to <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/datasync-limits.html\">quotas</a> on the number of files, objects, and directories in a dataset. Basic mode sequentially prepares, transfers, and verifies data, making it slower than Enhanced mode for most workloads.</p> </li> </ul> <p>For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/choosing-task-mode.html#task-mode-differences\">Understanding task mode differences</a>.</p>"}}, "documentation": "<p>CreateTaskRequest</p>"}, "CreateTaskResponse": {"type": "structure", "members": {"TaskArn": {"shape": "TaskArn", "documentation": "<p>The Amazon Resource Name (ARN) of the task.</p>"}}, "documentation": "<p>CreateTaskResponse</p>"}, "CustomSecretConfig": {"type": "structure", "members": {"SecretArn": {"shape": "SecretArn", "documentation": "<p>Specifies the ARN for an Secrets Manager secret.</p>"}, "SecretAccessRoleArn": {"shape": "IamRoleArnOrEmptyString", "documentation": "<p>Specifies the ARN for the Identity and Access Management role that DataSync uses to access the secret specified for <code>SecretArn</code>.</p>"}}, "documentation": "<p>Specifies configuration information for a customer-managed Secrets Manager secret where a storage location authentication token or secret key is stored in plain text. This configuration includes the secret ARN, and the ARN for an IAM role that provides access to the secret.</p> <note> <p>You can use either <code>CmkSecretConfig</code> or <code>CustomSecretConfig</code> to provide credentials for a <code>CreateLocation</code> request. Do not provide both parameters for the same request.</p> </note>"}, "DeleteAgentRequest": {"type": "structure", "required": ["AgentArn"], "members": {"AgentArn": {"shape": "AgentArn", "documentation": "<p>The Amazon Resource Name (ARN) of the agent to delete. Use the <code>ListAgents</code> operation to return a list of agents for your account and Amazon Web Services Region.</p>"}}, "documentation": "<p>DeleteAgentRequest</p>"}, "DeleteAgentResponse": {"type": "structure", "members": {}}, "DeleteLocationRequest": {"type": "structure", "required": ["LocationArn"], "members": {"LocationArn": {"shape": "LocationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the location to delete.</p>"}}, "documentation": "<p>DeleteLocation</p>"}, "DeleteLocationResponse": {"type": "structure", "members": {}}, "DeleteTaskRequest": {"type": "structure", "required": ["TaskArn"], "members": {"TaskArn": {"shape": "TaskArn", "documentation": "<p>Specifies the Amazon Resource Name (ARN) of the task that you want to delete.</p>"}}, "documentation": "<p>DeleteTask</p>"}, "DeleteTaskResponse": {"type": "structure", "members": {}}, "DescribeAgentRequest": {"type": "structure", "required": ["AgentArn"], "members": {"AgentArn": {"shape": "AgentArn", "documentation": "<p>Specifies the Amazon Resource Name (ARN) of the DataSync agent that you want information about.</p>"}}, "documentation": "<p>DescribeAgent</p>"}, "DescribeAgentResponse": {"type": "structure", "members": {"AgentArn": {"shape": "AgentArn", "documentation": "<p>The ARN of the agent.</p>"}, "Name": {"shape": "TagValue", "documentation": "<p>The name of the agent.</p>"}, "Status": {"shape": "AgentStatus", "documentation": "<p>The status of the agent.</p> <ul> <li> <p>If the status is <code>ONLINE</code>, the agent is configured properly and ready to use.</p> </li> <li> <p>If the status is <code>OFFLINE</code>, the agent has been out of contact with DataSync for five minutes or longer. This can happen for a few reasons. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/troubleshooting-datasync-agents.html#troubleshoot-agent-offline\">What do I do if my agent is offline?</a> </p> </li> </ul>"}, "LastConnectionTime": {"shape": "Time", "documentation": "<p>The last time that the agent was communicating with the DataSync service.</p>"}, "CreationTime": {"shape": "Time", "documentation": "<p>The time that the agent was <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/activate-agent.html\">activated</a>.</p>"}, "EndpointType": {"shape": "EndpointType", "documentation": "<p>The type of <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/choose-service-endpoint.html\">service endpoint</a> that your agent is connected to.</p>"}, "PrivateLinkConfig": {"shape": "PrivateLinkConfig", "documentation": "<p>The network configuration that the agent uses when connecting to a <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/choose-service-endpoint.html#choose-service-endpoint-vpc\">VPC service endpoint</a>.</p>"}, "Platform": {"shape": "Platform", "documentation": "<p>The platform-related details about the agent, such as the version number.</p>"}}, "documentation": "<p>DescribeAgentResponse</p>"}, "DescribeLocationAzureBlobRequest": {"type": "structure", "required": ["LocationArn"], "members": {"LocationArn": {"shape": "LocationArn", "documentation": "<p>Specifies the Amazon Resource Name (ARN) of your Azure Blob Storage transfer location.</p>"}}}, "DescribeLocationAzureBlobResponse": {"type": "structure", "members": {"LocationArn": {"shape": "LocationArn", "documentation": "<p>The ARN of your Azure Blob Storage transfer location.</p>"}, "LocationUri": {"shape": "LocationUri", "documentation": "<p>The URL of the Azure Blob Storage container involved in your transfer.</p>"}, "AuthenticationType": {"shape": "AzureBlobAuthenticationType", "documentation": "<p>The authentication method DataSync uses to access your Azure Blob Storage. DataSync can access blob storage using a shared access signature (SAS).</p>"}, "BlobType": {"shape": "AzureBlobType", "documentation": "<p>The type of blob that you want your objects or files to be when transferring them into Azure Blob Storage. Currently, DataSync only supports moving data into Azure Blob Storage as block blobs. For more information on blob types, see the <a href=\"https://learn.microsoft.com/en-us/rest/api/storageservices/understanding-block-blobs--append-blobs--and-page-blobs\">Azure Blob Storage documentation</a>.</p>"}, "AccessTier": {"shape": "AzureAccessTier", "documentation": "<p>The access tier that you want your objects or files transferred into. This only applies when using the location as a transfer destination. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/creating-azure-blob-location.html#azure-blob-access-tiers\">Access tiers</a>.</p>"}, "AgentArns": {"shape": "AgentArnList", "documentation": "<p>The ARNs of the DataSync agents that can connect with your Azure Blob Storage container.</p>"}, "CreationTime": {"shape": "Time", "documentation": "<p>The time that your Azure Blob Storage transfer location was created.</p>"}, "ManagedSecretConfig": {"shape": "ManagedSecretConfig", "documentation": "<p>Describes configuration information for a DataSync-managed secret, such as an authentication token that <PERSON>Sync uses to access a specific storage location. DataSync uses the default Amazon Web Services-managed KMS key to encrypt this secret in Secrets Manager.</p>"}, "CmkSecretConfig": {"shape": "CmkSecretConfig", "documentation": "<p>Describes configuration information for a DataSync-managed secret, such as an authentication token that DataSync uses to access a specific storage location, with a customer-managed KMS key.</p>"}, "CustomSecretConfig": {"shape": "CustomSecretConfig", "documentation": "<p>Describes configuration information for a customer-managed secret, such as an authentication token that DataSync uses to access a specific storage location, with a customer-managed KMS key.</p>"}}}, "DescribeLocationEfsRequest": {"type": "structure", "required": ["LocationArn"], "members": {"LocationArn": {"shape": "LocationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the Amazon EFS file system location that you want information about.</p>"}}, "documentation": "<p>DescribeLocationEfsRequest</p>"}, "DescribeLocationEfsResponse": {"type": "structure", "members": {"LocationArn": {"shape": "LocationArn", "documentation": "<p>The ARN of the Amazon EFS file system location.</p>"}, "LocationUri": {"shape": "LocationUri", "documentation": "<p>The URL of the Amazon EFS file system location.</p>"}, "Ec2Config": {"shape": "Ec2Config"}, "CreationTime": {"shape": "Time", "documentation": "<p>The time that the location was created.</p>"}, "AccessPointArn": {"shape": "EfsAccessPointArn", "documentation": "<p>The ARN of the access point that DataSync uses to access the Amazon EFS file system.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-efs-location.html#create-efs-location-iam\">Accessing restricted file systems</a>.</p>"}, "FileSystemAccessRoleArn": {"shape": "IamRoleArn", "documentation": "<p>The Identity and Access Management (IAM) role that allows DataSync to access your Amazon EFS file system.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-efs-location.html#create-efs-location-iam-role\">Creating a DataSync IAM role for file system access</a>.</p>"}, "InTransitEncryption": {"shape": "EfsInTransitEncryption", "documentation": "<p>Indicates whether DataSync uses Transport Layer Security (TLS) encryption when transferring data to or from the Amazon EFS file system.</p>"}}, "documentation": "<p>DescribeLocationEfsResponse</p>"}, "DescribeLocationFsxLustreRequest": {"type": "structure", "required": ["LocationArn"], "members": {"LocationArn": {"shape": "LocationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the FSx for Lustre location to describe. </p>"}}}, "DescribeLocationFsxLustreResponse": {"type": "structure", "members": {"LocationArn": {"shape": "LocationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the FSx for Lustre location that was described.</p>"}, "LocationUri": {"shape": "LocationUri", "documentation": "<p>The URI of the FSx for Lustre location that was described.</p>"}, "SecurityGroupArns": {"shape": "Ec2SecurityGroupArnList", "documentation": "<p>The Amazon Resource Names (ARNs) of the security groups that are configured for the FSx for Lustre file system.</p>"}, "CreationTime": {"shape": "Time", "documentation": "<p>The time that the FSx for Lustre location was created.</p>"}}}, "DescribeLocationFsxOntapRequest": {"type": "structure", "required": ["LocationArn"], "members": {"LocationArn": {"shape": "LocationArn", "documentation": "<p>Specifies the Amazon Resource Name (ARN) of the FSx for ONTAP file system location that you want information about.</p>"}}}, "DescribeLocationFsxOntapResponse": {"type": "structure", "members": {"CreationTime": {"shape": "Time", "documentation": "<p>The time that the location was created.</p>"}, "LocationArn": {"shape": "LocationArn", "documentation": "<p>The ARN of the FSx for ONTAP file system location.</p>"}, "LocationUri": {"shape": "LocationUri", "documentation": "<p>The uniform resource identifier (URI) of the FSx for ONTAP file system location.</p>"}, "Protocol": {"shape": "FsxProtocol"}, "SecurityGroupArns": {"shape": "Ec2SecurityGroupArnList", "documentation": "<p>The security groups that DataSync uses to access your FSx for ONTAP file system.</p>"}, "StorageVirtualMachineArn": {"shape": "StorageVirtualMachineArn", "documentation": "<p>The ARN of the storage virtual machine (SVM) on your FSx for ONTAP file system where you're copying data to or from.</p>"}, "FsxFilesystemArn": {"shape": "FsxFilesystemArn", "documentation": "<p>The ARN of the FSx for ONTAP file system.</p>"}}}, "DescribeLocationFsxOpenZfsRequest": {"type": "structure", "required": ["LocationArn"], "members": {"LocationArn": {"shape": "LocationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the FSx for OpenZFS location to describe.</p>"}}}, "DescribeLocationFsxOpenZfsResponse": {"type": "structure", "members": {"LocationArn": {"shape": "LocationArn", "documentation": "<p>The ARN of the FSx for OpenZFS location that was described.</p>"}, "LocationUri": {"shape": "LocationUri", "documentation": "<p>The uniform resource identifier (URI) of the FSx for OpenZFS location that was described.</p> <p>Example: <code>fsxz://us-west-2.fs-1234567890abcdef02/fsx/folderA/folder</code> </p>"}, "SecurityGroupArns": {"shape": "Ec2SecurityGroupArnList", "documentation": "<p>The ARNs of the security groups that are configured for the FSx for OpenZFS file system.</p>"}, "Protocol": {"shape": "FsxProtocol", "documentation": "<p>The type of protocol that DataSync uses to access your file system.</p>"}, "CreationTime": {"shape": "Time", "documentation": "<p>The time that the FSx for OpenZFS location was created.</p>"}}}, "DescribeLocationFsxWindowsRequest": {"type": "structure", "required": ["LocationArn"], "members": {"LocationArn": {"shape": "LocationArn", "documentation": "<p>Specifies the Amazon Resource Name (ARN) of the FSx for Windows File Server location.</p>"}}}, "DescribeLocationFsxWindowsResponse": {"type": "structure", "members": {"LocationArn": {"shape": "LocationArn", "documentation": "<p>The ARN of the FSx for Windows File Server location.</p>"}, "LocationUri": {"shape": "LocationUri", "documentation": "<p>The uniform resource identifier (URI) of the FSx for Windows File Server location.</p>"}, "SecurityGroupArns": {"shape": "Ec2SecurityGroupArnList", "documentation": "<p>The ARNs of the Amazon EC2 security groups that provide access to your file system's preferred subnet.</p> <p>For information about configuring security groups for file system access, see the <a href=\"https://docs.aws.amazon.com/fsx/latest/WindowsGuide/limit-access-security-groups.html\"> <i>Amazon FSx for Windows File Server User Guide</i> </a>.</p>"}, "CreationTime": {"shape": "Time", "documentation": "<p>The time that the FSx for Windows File Server location was created.</p>"}, "User": {"shape": "SmbUser", "documentation": "<p>The user with the permissions to mount and access the FSx for Windows File Server file system.</p>"}, "Domain": {"shape": "SmbDomain", "documentation": "<p>The name of the Microsoft Active Directory domain that the FSx for Windows File Server file system belongs to.</p>"}}}, "DescribeLocationHdfsRequest": {"type": "structure", "required": ["LocationArn"], "members": {"LocationArn": {"shape": "LocationArn", "documentation": "<p>Specifies the Amazon Resource Name (ARN) of the HDFS location.</p>"}}}, "DescribeLocationHdfsResponse": {"type": "structure", "members": {"LocationArn": {"shape": "LocationArn", "documentation": "<p>The ARN of the HDFS location.</p>"}, "LocationUri": {"shape": "LocationUri", "documentation": "<p>The URI of the HDFS location.</p>"}, "NameNodes": {"shape": "HdfsNameNodeList", "documentation": "<p>The NameNode that manages the HDFS namespace. </p>"}, "BlockSize": {"shape": "HdfsBlockSize", "documentation": "<p>The size of the data blocks to write into the HDFS cluster. </p>"}, "ReplicationFactor": {"shape": "HdfsReplicationFactor", "documentation": "<p>The number of DataNodes to replicate the data to when writing to the HDFS cluster. </p>"}, "KmsKeyProviderUri": {"shape": "KmsKeyProviderUri", "documentation": "<p> The URI of the HDFS cluster's Key Management Server (KMS). </p>"}, "QopConfiguration": {"shape": "QopConfiguration", "documentation": "<p>The Quality of Protection (QOP) configuration, which specifies the Remote Procedure Call (RPC) and data transfer protection settings configured on the HDFS cluster. </p>"}, "AuthenticationType": {"shape": "HdfsAuthenticationType", "documentation": "<p>The type of authentication used to determine the identity of the user. </p>"}, "SimpleUser": {"shape": "HdfsUser", "documentation": "<p>The user name to identify the client on the host operating system. This parameter is used if the <code>AuthenticationType</code> is defined as <code>SIMPLE</code>.</p>"}, "KerberosPrincipal": {"shape": "KerberosPrincipal", "documentation": "<p>The Kerberos principal with access to the files and folders on the HDFS cluster. This parameter is used if the <code>AuthenticationType</code> is defined as <code>KERBEROS</code>.</p>"}, "AgentArns": {"shape": "AgentArnList", "documentation": "<p>The ARNs of the DataSync agents that can connect with your HDFS cluster.</p>"}, "CreationTime": {"shape": "Time", "documentation": "<p>The time that the HDFS location was created.</p>"}}}, "DescribeLocationNfsRequest": {"type": "structure", "required": ["LocationArn"], "members": {"LocationArn": {"shape": "LocationArn", "documentation": "<p>Specifies the Amazon Resource Name (ARN) of the NFS location that you want information about.</p>"}}, "documentation": "<p>DescribeLocationNfsRequest</p>"}, "DescribeLocationNfsResponse": {"type": "structure", "members": {"LocationArn": {"shape": "LocationArn", "documentation": "<p>The ARN of the NFS location.</p>"}, "LocationUri": {"shape": "LocationUri", "documentation": "<p>The URI of the NFS location.</p>"}, "OnPremConfig": {"shape": "OnPremConfig"}, "MountOptions": {"shape": "NfsMountOptions", "documentation": "<p>The mount options that DataSync uses to mount your NFS file server.</p>"}, "CreationTime": {"shape": "Time", "documentation": "<p>The time when the NFS location was created.</p>"}}, "documentation": "<p>DescribeLocationNfsResponse</p>"}, "DescribeLocationObjectStorageRequest": {"type": "structure", "required": ["LocationArn"], "members": {"LocationArn": {"shape": "LocationArn", "documentation": "<p>Specifies the Amazon Resource Name (ARN) of the object storage system location.</p>"}}, "documentation": "<p>DescribeLocationObjectStorageRequest</p>"}, "DescribeLocationObjectStorageResponse": {"type": "structure", "members": {"LocationArn": {"shape": "LocationArn", "documentation": "<p>The ARN of the object storage system location.</p>"}, "LocationUri": {"shape": "LocationUri", "documentation": "<p>The URI of the object storage system location.</p>"}, "AccessKey": {"shape": "ObjectStorageAccessKey", "documentation": "<p>The access key (for example, a user name) required to authenticate with the object storage system.</p>"}, "ServerPort": {"shape": "ObjectStorageServerPort", "documentation": "<p>The port that your object storage server accepts inbound network traffic on (for example, port 443).</p>"}, "ServerProtocol": {"shape": "ObjectStorageServerProtocol", "documentation": "<p>The protocol that your object storage system uses to communicate.</p>"}, "AgentArns": {"shape": "AgentArnList", "documentation": "<p>The ARNs of the DataSync agents that can connect with your object storage system.</p>"}, "CreationTime": {"shape": "Time", "documentation": "<p>The time that the location was created.</p>"}, "ServerCertificate": {"shape": "ObjectStorageCertificate", "documentation": "<p>The certificate chain for DataSync to authenticate with your object storage system if the system uses a private or self-signed certificate authority (CA).</p>"}, "ManagedSecretConfig": {"shape": "ManagedSecretConfig", "documentation": "<p>Describes configuration information for a DataSync-managed secret, such as an authentication token or set of credentials that DataSync uses to access a specific transfer location. DataSync uses the default Amazon Web Services-managed KMS key to encrypt this secret in Secrets Manager.</p>"}, "CmkSecretConfig": {"shape": "CmkSecretConfig", "documentation": "<p>Describes configuration information for a DataSync-managed secret, such as an authentication token or set of credentials that DataSync uses to access a specific transfer location, and a customer-managed KMS key.</p>"}, "CustomSecretConfig": {"shape": "CustomSecretConfig", "documentation": "<p>Describes configuration information for a customer-managed secret, such as an authentication token or set of credentials that DataSync uses to access a specific transfer location, and a customer-managed KMS key.</p>"}}, "documentation": "<p>DescribeLocationObjectStorageResponse</p>"}, "DescribeLocationS3Request": {"type": "structure", "required": ["LocationArn"], "members": {"LocationArn": {"shape": "LocationArn", "documentation": "<p>Specifies the Amazon Resource Name (ARN) of the Amazon S3 location.</p>"}}, "documentation": "<p>DescribeLocationS3Request</p>"}, "DescribeLocationS3Response": {"type": "structure", "members": {"LocationArn": {"shape": "LocationArn", "documentation": "<p>The ARN of the Amazon S3 location.</p>"}, "LocationUri": {"shape": "LocationUri", "documentation": "<p>The URL of the Amazon S3 location that was described.</p>"}, "S3StorageClass": {"shape": "S3StorageClass", "documentation": "<p>When Amazon S3 is a destination location, this is the storage class that you chose for your objects.</p> <p>Some storage classes have behaviors that can affect your Amazon S3 storage costs. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-s3-location.html#using-storage-classes\">Storage class considerations with Amazon S3 transfers</a>.</p>"}, "S3Config": {"shape": "S3Config"}, "AgentArns": {"shape": "AgentArnList", "documentation": "<p>The ARNs of the DataSync agents deployed on your Outpost when using working with Amazon S3 on Outposts.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/deploy-agents.html#outposts-agent\">Deploy your DataSync agent on Outposts</a>.</p>"}, "CreationTime": {"shape": "Time", "documentation": "<p>The time that the Amazon S3 location was created.</p>"}}, "documentation": "<p>DescribeLocationS3Response</p>"}, "DescribeLocationSmbRequest": {"type": "structure", "required": ["LocationArn"], "members": {"LocationArn": {"shape": "LocationArn", "documentation": "<p>Specifies the Amazon Resource Name (ARN) of the SMB location that you want information about.</p>"}}, "documentation": "<p>DescribeLocationSmbRequest</p>"}, "DescribeLocationSmbResponse": {"type": "structure", "members": {"LocationArn": {"shape": "LocationArn", "documentation": "<p>The ARN of the SMB location.</p>"}, "LocationUri": {"shape": "LocationUri", "documentation": "<p>The URI of the SMB location.</p>"}, "AgentArns": {"shape": "AgentArnList", "documentation": "<p>The ARNs of the DataSync agents that can connect with your SMB file server.</p>"}, "User": {"shape": "SmbUser", "documentation": "<p>The user that can mount and access the files, folders, and file metadata in your SMB file server. This element applies only if <code>AuthenticationType</code> is set to <code>NTLM</code>.</p>"}, "Domain": {"shape": "SmbDomain", "documentation": "<p>The name of the Windows domain that the SMB file server belongs to. This element applies only if <code>AuthenticationType</code> is set to <code>NTLM</code>.</p>"}, "MountOptions": {"shape": "SmbMountOptions", "documentation": "<p>The SMB protocol version that DataSync uses to access your SMB file server.</p>"}, "CreationTime": {"shape": "Time", "documentation": "<p>The time that the SMB location was created.</p>"}, "DnsIpAddresses": {"shape": "DnsIpList", "documentation": "<p>The IPv4 addresses for the DNS servers that your SMB file server belongs to. This element applies only if <code>AuthenticationType</code> is set to <code>KERBEROS</code>.</p>"}, "KerberosPrincipal": {"shape": "KerberosPrincipal", "documentation": "<p>The Kerberos principal that has permission to access the files, folders, and file metadata in your SMB file server.</p>"}, "AuthenticationType": {"shape": "SmbAuthenticationType", "documentation": "<p>The authentication protocol that DataSync uses to connect to your SMB file server.</p>"}}, "documentation": "<p>DescribeLocationSmbResponse</p>"}, "DescribeTaskExecutionRequest": {"type": "structure", "required": ["TaskExecutionArn"], "members": {"TaskExecutionArn": {"shape": "TaskExecutionArn", "documentation": "<p>Specifies the Amazon Resource Name (ARN) of the task execution that you want information about.</p>"}}, "documentation": "<p>DescribeTaskExecutionRequest</p>"}, "DescribeTaskExecutionResponse": {"type": "structure", "members": {"TaskExecutionArn": {"shape": "TaskExecutionArn", "documentation": "<p>The ARN of the task execution that you wanted information about. <code>TaskExecutionArn</code> is hierarchical and includes <code>TaskArn</code> for the task that was executed. </p> <p>For example, a <code>TaskExecution</code> value with the ARN <code>arn:aws:datasync:us-east-1:111222333444:task/task-0208075f79cedf4a2/execution/exec-08ef1e88ec491019b</code> executed the task with the ARN <code>arn:aws:datasync:us-east-1:111222333444:task/task-0208075f79cedf4a2</code>. </p>"}, "Status": {"shape": "TaskExecutionStatus", "documentation": "<p>The status of the task execution. </p>"}, "Options": {"shape": "Options"}, "Excludes": {"shape": "FilterList", "documentation": "<p>A list of filter rules that exclude specific data during your transfer. For more information and examples, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/filtering.html\">Filtering data transferred by DataSync</a>.</p>"}, "Includes": {"shape": "FilterList", "documentation": "<p>A list of filter rules that include specific data during your transfer. For more information and examples, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/filtering.html\">Filtering data transferred by DataSync</a>.</p>"}, "ManifestConfig": {"shape": "ManifestConfig", "documentation": "<p>The configuration of the manifest that lists the files or objects to transfer. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/transferring-with-manifest.html\">Specifying what DataSync transfers by using a manifest</a>.</p>"}, "StartTime": {"shape": "Time", "documentation": "<p>The time that <PERSON><PERSON><PERSON> sends the request to start the task execution. For non-queued tasks, <code>LaunchTime</code> and <code>StartTime</code> are typically the same. For queued tasks, <code>LaunchTime</code> is typically later than <code>StartTime</code> because previously queued tasks must finish running before newer tasks can begin.</p>"}, "EstimatedFilesToTransfer": {"shape": "long", "documentation": "<p>The number of files, objects, and directories that DataSync expects to transfer over the network. This value is calculated while DataSync <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/run-task.html#understand-task-execution-statuses\">prepares</a> the transfer.</p> <p>How this gets calculated depends primarily on your task’s <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/API_Options.html#DataSync-Type-Options-TransferMode\">transfer mode</a> configuration:</p> <ul> <li> <p>If <code>TranserMode</code> is set to <code>CHANGED</code> - The calculation is based on comparing the content of the source and destination locations and determining the difference that needs to be transferred. The difference can include:</p> <ul> <li> <p>Anything that's added or modified at the source location.</p> </li> <li> <p>Anything that's in both locations and modified at the destination after an initial transfer (unless <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/API_Options.html#DataSync-Type-Options-OverwriteMode\">OverwriteMode</a> is set to <code>NEVER</code>).</p> </li> <li> <p> <b>(Basic task mode only)</b> The number of items that DataSync expects to delete (if <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/API_Options.html#DataSync-Type-Options-PreserveDeletedFiles\">PreserveDeletedFiles</a> is set to <code>REMOVE</code>).</p> </li> </ul> </li> <li> <p>If <code>TranserMode</code> is set to <code>ALL</code> - The calculation is based only on the items that DataSync finds at the source location.</p> </li> </ul>"}, "EstimatedBytesToTransfer": {"shape": "long", "documentation": "<p>The number of logical bytes that DataSync expects to write to the destination location.</p>"}, "FilesTransferred": {"shape": "long", "documentation": "<p>The number of files, objects, and directories that DataSync actually transfers over the network. This value is updated periodically during your task execution when something is read from the source and sent over the network.</p> <p>If DataSync fails to transfer something, this value can be less than <code>EstimatedFilesToTransfer</code>. In some cases, this value can also be greater than <code>EstimatedFilesToTransfer</code>. This element is implementation-specific for some location types, so don't use it as an exact indication of what's transferring or to monitor your task execution.</p>"}, "BytesWritten": {"shape": "long", "documentation": "<p>The number of logical bytes that DataSync actually writes to the destination location.</p>"}, "BytesTransferred": {"shape": "long", "documentation": "<p>The number of bytes that DataSync sends to the network before compression (if compression is possible). For the number of bytes transferred over the network, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/API_DescribeTaskExecution.html#DataSync-DescribeTaskExecution-response-BytesCompressed\">BytesCompressed</a>. </p>"}, "BytesCompressed": {"shape": "long", "documentation": "<p>The number of physical bytes that DataSync transfers over the network after compression (if compression is possible). This number is typically less than <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/API_DescribeTaskExecution.html#DataSync-DescribeTaskExecution-response-BytesTransferred\">BytesTransferred</a> unless the data isn't compressible.</p>"}, "Result": {"shape": "TaskExecutionResultDetail", "documentation": "<p>The result of the task execution.</p>"}, "TaskReportConfig": {"shape": "TaskReportConfig", "documentation": "<p>The configuration of your task report, which provides detailed information about for your DataSync transfer. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/task-reports.html\">Creating a task report</a>.</p>"}, "FilesDeleted": {"shape": "long", "documentation": "<p>The number of files, objects, and directories that DataSync actually deletes in your destination location. If you don't configure your task to <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/configure-metadata.html\">delete data in the destination that isn't in the source</a>, the value is always <code>0</code>.</p>"}, "FilesSkipped": {"shape": "long", "documentation": "<p>The number of files, objects, and directories that DataSync skips during your transfer.</p>"}, "FilesVerified": {"shape": "long", "documentation": "<p>The number of files, objects, and directories that DataSync verifies during your transfer.</p> <note> <p>When you configure your task to <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/configure-data-verification-options.html\">verify only the data that's transferred</a>, DataSync doesn't verify directories in some situations or files that fail to transfer.</p> </note>"}, "ReportResult": {"shape": "ReportResult", "documentation": "<p>Indicates whether DataSync generated a complete <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/task-reports.html\">task report</a> for your transfer.</p>"}, "EstimatedFilesToDelete": {"shape": "long", "documentation": "<p>The number of files, objects, and directories that DataSync expects to delete in your destination location. If you don't configure your task to <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/configure-metadata.html\">delete data in the destination that isn't in the source</a>, the value is always <code>0</code>.</p>"}, "TaskMode": {"shape": "TaskMode", "documentation": "<p>The task mode that you're using. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/choosing-task-mode.html\">Choosing a task mode for your data transfer</a>.</p>"}, "FilesPrepared": {"shape": "long", "documentation": "<p>The number of objects that <PERSON><PERSON><PERSON> will attempt to transfer after comparing your source and destination locations.</p> <note> <p>Applies only to <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/choosing-task-mode.html\">Enhanced mode tasks</a>.</p> </note> <p>This counter isn't applicable if you configure your task to <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/configure-metadata.html#task-option-transfer-mode\">transfer all data</a>. In that scenario, DataSync copies everything from the source to the destination without comparing differences between the locations.</p>"}, "FilesListed": {"shape": "TaskExecutionFilesListedDetail", "documentation": "<p>The number of objects that DataS<PERSON> finds at your locations.</p> <note> <p>Applies only to <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/choosing-task-mode.html\">Enhanced mode tasks</a>.</p> </note>"}, "FilesFailed": {"shape": "TaskExecutionFilesFailedDetail", "documentation": "<p>The number of objects that <PERSON><PERSON><PERSON> fails to prepare, transfer, verify, and delete during your task execution.</p> <note> <p>Applies only to <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/choosing-task-mode.html\">Enhanced mode tasks</a>.</p> </note>"}, "LaunchTime": {"shape": "Time", "documentation": "<p>The time that the task execution actually begins. For non-queued tasks, <code>LaunchTime</code> and <code>StartTime</code> are typically the same. For queued tasks, <code>LaunchTime</code> is typically later than <code>StartTime</code> because previously queued tasks must finish running before newer tasks can begin.</p>"}, "EndTime": {"shape": "Time", "documentation": "<p>The time that the transfer task ends.</p>"}}, "documentation": "<p>DescribeTaskExecutionResponse</p>"}, "DescribeTaskRequest": {"type": "structure", "required": ["TaskArn"], "members": {"TaskArn": {"shape": "TaskArn", "documentation": "<p>Specifies the Amazon Resource Name (ARN) of the transfer task that you want information about.</p>"}}, "documentation": "<p>DescribeTaskRequest</p>"}, "DescribeTaskResponse": {"type": "structure", "members": {"TaskArn": {"shape": "TaskArn", "documentation": "<p>The ARN of your task.</p>"}, "Status": {"shape": "TaskStatus", "documentation": "<p>The status of your task. For information about what each status means, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/understand-task-statuses.html#understand-task-creation-statuses\">Task statuses</a>.</p>"}, "Name": {"shape": "TagValue", "documentation": "<p>The name of your task.</p>"}, "CurrentTaskExecutionArn": {"shape": "TaskExecutionArn", "documentation": "<p>The ARN of the most recent task execution.</p>"}, "SourceLocationArn": {"shape": "LocationArn", "documentation": "<p>The ARN of your transfer's source location.</p>"}, "DestinationLocationArn": {"shape": "LocationArn", "documentation": "<p>The ARN of your transfer's destination location.</p>"}, "CloudWatchLogGroupArn": {"shape": "LogGroupArn", "documentation": "<p>The Amazon Resource Name (ARN) of an Amazon CloudWatch log group for monitoring your task.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/configure-logging.html\">Monitoring data transfers with CloudWatch Logs</a>.</p>"}, "SourceNetworkInterfaceArns": {"shape": "SourceNetworkInterfaceArns", "documentation": "<p>The ARNs of the <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/datasync-network.html#required-network-interfaces\">network interfaces</a> that DataSync created for your source location.</p>"}, "DestinationNetworkInterfaceArns": {"shape": "DestinationNetworkInterfaceArns", "documentation": "<p>The ARNs of the <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/datasync-network.html#required-network-interfaces\">network interfaces</a> that DataSync created for your destination location.</p>"}, "Options": {"shape": "Options", "documentation": "<p>The task's settings. For example, what file metadata gets preserved, how data integrity gets verified at the end of your transfer, bandwidth limits, among other options.</p>"}, "Excludes": {"shape": "FilterList", "documentation": "<p>The exclude filters that define the files, objects, and folders in your source location that you don't want DataSync to transfer. For more information and examples, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/filtering.html\">Specifying what DataSync transfers by using filters</a>.</p>"}, "Schedule": {"shape": "TaskSchedule", "documentation": "<p>The schedule for when you want your task to run. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/task-scheduling.html\">Scheduling your task</a>.</p>"}, "ErrorCode": {"shape": "string", "documentation": "<p>If there's an issue with your task, you can use the error code to help you troubleshoot the problem. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/troubleshooting-datasync-locations-tasks.html\">Troubleshooting issues with DataSync transfers</a>.</p>"}, "ErrorDetail": {"shape": "string", "documentation": "<p>If there's an issue with your task, you can use the error details to help you troubleshoot the problem. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/troubleshooting-datasync-locations-tasks.html\">Troubleshooting issues with DataSync transfers</a>.</p>"}, "CreationTime": {"shape": "Time", "documentation": "<p>The time that the task was created.</p>"}, "Includes": {"shape": "FilterList", "documentation": "<p>The include filters that define the files, objects, and folders in your source location that you want DataSync to transfer. For more information and examples, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/filtering.html\">Specifying what DataSync transfers by using filters</a>.</p>"}, "ManifestConfig": {"shape": "ManifestConfig", "documentation": "<p>The configuration of the manifest that lists the files or objects that you want DataSync to transfer. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/transferring-with-manifest.html\">Specifying what DataSync transfers by using a manifest</a>.</p>"}, "TaskReportConfig": {"shape": "TaskReportConfig", "documentation": "<p>The configuration of your task report, which provides detailed information about your DataSync transfer. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/task-reports.html\">Monitoring your DataSync transfers with task reports</a>.</p>"}, "ScheduleDetails": {"shape": "TaskScheduleDetails", "documentation": "<p>The details about your <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/task-scheduling.html\">task schedule</a>.</p>"}, "TaskMode": {"shape": "TaskMode", "documentation": "<p>The task mode that you're using. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/choosing-task-mode.html\">Choosing a task mode for your data transfer</a>.</p>"}}, "documentation": "<p>DescribeTaskResponse</p>"}, "DestinationNetworkInterfaceArns": {"type": "list", "member": {"shape": "NetworkInterfaceArn"}}, "DnsIpList": {"type": "list", "member": {"shape": "ServerIpAddress"}, "max": 2}, "Duration": {"type": "long", "min": 0}, "Ec2Config": {"type": "structure", "required": ["SubnetArn", "SecurityGroupArns"], "members": {"SubnetArn": {"shape": "Ec2SubnetArn", "documentation": "<p>Specifies the ARN of a subnet where DataSync creates the <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/datasync-network.html#required-network-interfaces\">network interfaces</a> for managing traffic during your transfer.</p> <p>The subnet must be located:</p> <ul> <li> <p>In the same virtual private cloud (VPC) as the Amazon EFS file system.</p> </li> <li> <p>In the same Availability Zone as at least one mount target for the Amazon EFS file system.</p> </li> </ul> <note> <p>You don't need to specify a subnet that includes a file system mount target.</p> </note>"}, "SecurityGroupArns": {"shape": "Ec2SecurityGroupArnList", "documentation": "<p>Specifies the Amazon Resource Names (ARNs) of the security groups associated with an Amazon EFS file system's mount target.</p>"}}, "documentation": "<p>The subnet and security groups that DataSync uses to connect to one of your Amazon EFS file system's <a href=\"https://docs.aws.amazon.com/efs/latest/ug/accessing-fs.html\">mount targets</a>.</p>"}, "Ec2SecurityGroupArn": {"type": "string", "max": 128, "pattern": "^arn:(aws|aws-cn|aws-us-gov|aws-iso|aws-iso-b):ec2:[a-z\\-0-9]*:[0-9]{12}:security-group/sg-[a-f0-9]+$"}, "Ec2SecurityGroupArnList": {"type": "list", "member": {"shape": "Ec2SecurityGroupArn"}, "max": 5, "min": 1}, "Ec2SubnetArn": {"type": "string", "max": 128, "pattern": "^arn:(aws|aws-cn|aws-us-gov|aws-iso|aws-iso-b):ec2:[a-z\\-0-9]*:[0-9]{12}:subnet/.*$"}, "EfsAccessPointArn": {"type": "string", "max": 128, "pattern": "^arn:(aws|aws-cn|aws-us-gov|aws-iso|aws-iso-b):elasticfilesystem:[a-z\\-0-9]+:[0-9]{12}:access-point/fsap-[0-9a-f]{8,40}$"}, "EfsFilesystemArn": {"type": "string", "max": 128, "pattern": "^arn:(aws|aws-cn|aws-us-gov|aws-iso|aws-iso-b):elasticfilesystem:[a-z\\-0-9]*:[0-9]{12}:file-system/fs-.*$"}, "EfsInTransitEncryption": {"type": "string", "enum": ["NONE", "TLS1_2"]}, "EfsSubdirectory": {"type": "string", "max": 4096, "pattern": "^[a-zA-Z0-9_\\-\\+\\./\\(\\)\\p{Zs}]*$"}, "Endpoint": {"type": "string", "max": 15, "min": 7, "pattern": "\\A(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)(\\.(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)){3}\\z"}, "EndpointType": {"type": "string", "enum": ["PUBLIC", "PRIVATE_LINK", "FIPS"]}, "FilterAttributeValue": {"type": "string", "max": 255, "min": 1, "pattern": "^[0-9a-zA-Z_\\ \\-\\:\\*\\.\\\\/\\?-]*$"}, "FilterList": {"type": "list", "member": {"shape": "FilterRule"}, "max": 1, "min": 0}, "FilterRule": {"type": "structure", "members": {"FilterType": {"shape": "FilterType", "documentation": "<p>The type of filter rule to apply. DataSync only supports the SIMPLE_PATTERN rule type.</p>"}, "Value": {"shape": "FilterValue", "documentation": "<p>A single filter string that consists of the patterns to include or exclude. The patterns are delimited by \"|\" (that is, a pipe), for example: <code>/folder1|/folder2</code> </p> <p> </p>"}}, "documentation": "<p>Specifies which files, folders, and objects to include or exclude when transferring files from source to destination.</p>"}, "FilterType": {"type": "string", "enum": ["SIMPLE_PATTERN"], "max": 128, "pattern": "^[A-Z0-9_]+$"}, "FilterValue": {"type": "string", "max": 102400, "pattern": "^[^\\x00]+$"}, "FilterValues": {"type": "list", "member": {"shape": "FilterAttributeValue"}}, "FsxFilesystemArn": {"type": "string", "max": 128, "pattern": "^arn:(aws|aws-cn|aws-us-gov|aws-iso|aws-iso-b):fsx:[a-z\\-0-9]*:[0-9]{12}:file-system/fs-.*$"}, "FsxLustreSubdirectory": {"type": "string", "max": 4096, "pattern": "^[a-zA-Z0-9_\\-\\+\\./\\(\\)\\$\\p{Zs}]+$"}, "FsxOntapSubdirectory": {"type": "string", "max": 255, "pattern": "^[^\\u0000\\u0085\\u2028\\u2029\\r\\n]{1,255}$"}, "FsxOpenZfsSubdirectory": {"type": "string", "max": 4096, "pattern": "^[^\\u0000\\u0085\\u2028\\u2029\\r\\n]{1,4096}$"}, "FsxProtocol": {"type": "structure", "members": {"NFS": {"shape": "FsxProtocolNfs", "documentation": "<p>Specifies the Network File System (NFS) protocol configuration that DataSync uses to access your FSx for OpenZFS file system or FSx for ONTAP file system's storage virtual machine (SVM).</p>"}, "SMB": {"shape": "FsxProtocolSmb", "documentation": "<p>Specifies the Server Message Block (SMB) protocol configuration that DataSync uses to access your FSx for ONTAP file system's SVM.</p>"}}, "documentation": "<p>Specifies the data transfer protocol that DataSync uses to access your Amazon FSx file system.</p>"}, "FsxProtocolNfs": {"type": "structure", "members": {"MountOptions": {"shape": "NfsMountOptions"}}, "documentation": "<p>Specifies the Network File System (NFS) protocol configuration that DataSync uses to access your FSx for OpenZFS file system or FSx for ONTAP file system's storage virtual machine (SVM).</p>"}, "FsxProtocolSmb": {"type": "structure", "required": ["Password", "User"], "members": {"Domain": {"shape": "SmbDomain", "documentation": "<p>Specifies the name of the Windows domain that your storage virtual machine (SVM) belongs to.</p> <p>If you have multiple domains in your environment, configuring this setting makes sure that DataSync connects to the right SVM.</p> <p>If you have multiple Active Directory domains in your environment, configuring this parameter makes sure that DataSync connects to the right SVM.</p>"}, "MountOptions": {"shape": "SmbMountOptions"}, "Password": {"shape": "SmbPassword", "documentation": "<p>Specifies the password of a user who has permission to access your SVM.</p>"}, "User": {"shape": "SmbUser", "documentation": "<p>Specifies a user that can mount and access the files, folders, and metadata in your SVM.</p> <p>For information about choosing a user with the right level of access for your transfer, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-ontap-location.html#create-ontap-location-smb\">Using the SMB protocol</a>.</p>"}}, "documentation": "<p>Specifies the Server Message Block (SMB) protocol configuration that DataSync uses to access your Amazon FSx for NetApp ONTAP file system's storage virtual machine (SVM). For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-ontap-location.html#create-ontap-location-access\">Providing DataSync access to FSx for ONTAP file systems</a>.</p>"}, "FsxUpdateProtocol": {"type": "structure", "members": {"NFS": {"shape": "FsxProtocolNfs"}, "SMB": {"shape": "FsxUpdateProtocolSmb", "documentation": "<p>Specifies the Server Message Block (SMB) protocol configuration that DataSync uses to access your FSx for ONTAP file system's storage virtual machine (SVM).</p>"}}, "documentation": "<p>Specifies the data transfer protocol that DataSync uses to access your Amazon FSx file system.</p> <note> <p>You can't update the Network File System (NFS) protocol configuration for FSx for ONTAP locations. DataSync currently only supports NFS version 3 with this location type.</p> </note>"}, "FsxUpdateProtocolSmb": {"type": "structure", "members": {"Domain": {"shape": "UpdateSmbDomain", "documentation": "<p>Specifies the name of the Windows domain that your storage virtual machine (SVM) belongs to.</p> <p>If you have multiple Active Directory domains in your environment, configuring this parameter makes sure that DataSync connects to the right SVM.</p>"}, "MountOptions": {"shape": "SmbMountOptions"}, "Password": {"shape": "SmbPassword", "documentation": "<p>Specifies the password of a user who has permission to access your SVM.</p>"}, "User": {"shape": "SmbUser", "documentation": "<p>Specifies a user that can mount and access the files, folders, and metadata in your SVM.</p> <p>For information about choosing a user with the right level of access for your transfer, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-ontap-location.html#create-ontap-location-smb\">Using the SMB protocol</a>.</p>"}}, "documentation": "<p>Specifies the Server Message Block (SMB) protocol configuration that DataSync uses to access your Amazon FSx for NetApp ONTAP file system's storage virtual machine (SVM). For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-ontap-location.html#create-ontap-location-access\">Providing DataSync access to FSx for ONTAP file systems</a>.</p>"}, "FsxWindowsSubdirectory": {"type": "string", "max": 4096, "pattern": "^[a-zA-Z0-9_\\-\\+\\./\\(\\)\\$\\p{Zs}]+$"}, "Gid": {"type": "string", "enum": ["NONE", "INT_VALUE", "NAME", "BOTH"]}, "HdfsAuthenticationType": {"type": "string", "enum": ["SIMPLE", "KERBEROS"]}, "HdfsBlockSize": {"type": "integer", "box": true, "max": 1073741824, "min": 1048576}, "HdfsDataTransferProtection": {"type": "string", "enum": ["DISABLED", "AUTHENTICATION", "INTEGRITY", "PRIVACY"]}, "HdfsNameNode": {"type": "structure", "required": ["Hostname", "Port"], "members": {"Hostname": {"shape": "HdfsServerHostname", "documentation": "<p>The hostname of the NameNode in the HDFS cluster. This value is the IP address or Domain Name Service (DNS) name of the NameNode. An agent that's installed on-premises uses this hostname to communicate with the NameNode in the network.</p>"}, "Port": {"shape": "HdfsServerPort", "documentation": "<p>The port that the NameNode uses to listen to client requests.</p>"}}, "documentation": "<p>The NameNode of the Hadoop Distributed File System (HDFS). The NameNode manages the file system's namespace. The NameNode performs operations such as opening, closing, and renaming files and directories. The NameNode contains the information to map blocks of data to the DataNodes.</p>"}, "HdfsNameNodeList": {"type": "list", "member": {"shape": "HdfsNameNode"}, "min": 1}, "HdfsReplicationFactor": {"type": "integer", "box": true, "max": 512, "min": 1}, "HdfsRpcProtection": {"type": "string", "enum": ["DISABLED", "AUTHENTICATION", "INTEGRITY", "PRIVACY"]}, "HdfsServerHostname": {"type": "string", "max": 255, "min": 1, "pattern": "^(([a-zA-Z0-9\\-]*[a-zA-Z0-9])\\.)*([A-Za-z0-9\\-]*[A-Za-z0-9])$"}, "HdfsServerPort": {"type": "integer", "box": true, "max": 65536, "min": 1}, "HdfsSubdirectory": {"type": "string", "max": 4096, "pattern": "^[a-zA-Z0-9_\\-\\+\\./\\(\\)\\$\\p{Zs}]+$"}, "HdfsUser": {"type": "string", "max": 256, "min": 1, "pattern": "^[_.A-Za-z0-9][-_.A-Za-z0-9]*$"}, "IamRoleArn": {"type": "string", "max": 2048, "pattern": "^arn:(aws|aws-cn|aws-us-gov|aws-iso|aws-iso-b):iam::[0-9]{12}:role/.*$"}, "IamRoleArnOrEmptyString": {"type": "string", "max": 2048, "pattern": "^(arn:(aws|aws-cn|aws-us-gov|aws-iso|aws-iso-b):iam::[0-9]{12}:role/[a-zA-Z0-9+=,.@_-]+|)$"}, "InputTagList": {"type": "list", "member": {"shape": "TagListEntry"}, "max": 50, "min": 0}, "InternalException": {"type": "structure", "members": {"message": {"shape": "string"}, "errorCode": {"shape": "string"}}, "documentation": "<p>This exception is thrown when an error occurs in the DataSync service.</p>", "exception": true, "fault": true}, "InvalidRequestException": {"type": "structure", "members": {"message": {"shape": "string"}, "errorCode": {"shape": "string"}, "datasyncErrorCode": {"shape": "string"}}, "documentation": "<p>This exception is thrown when the client submits a malformed request.</p>", "exception": true}, "KerberosKeytabFile": {"type": "blob", "max": 65536}, "KerberosKrb5ConfFile": {"type": "blob", "max": 131072}, "KerberosPrincipal": {"type": "string", "max": 256, "min": 1, "pattern": "^.+$"}, "KmsKeyArn": {"type": "string", "max": 2048, "pattern": "^(arn:(aws|aws-cn|aws-us-gov|aws-iso|aws-iso-b):kms:[a-z\\-0-9]+:[0-9]{12}:key/.*|)$"}, "KmsKeyProviderUri": {"type": "string", "max": 255, "min": 1, "pattern": "^kms:\\/\\/http[s]?@(([a-zA-Z0-9\\-]*[a-zA-Z0-9])\\.)*([A-Za-z0-9\\-]*[A-Za-z0-9])(;(([a-zA-Z0-9\\-]*[a-zA-Z0-9])\\.)*([A-Za-z0-9\\-]*[A-Za-z0-9]))*:[0-9]{1,5}\\/kms$"}, "ListAgentsRequest": {"type": "structure", "members": {"MaxResults": {"shape": "MaxResults", "documentation": "<p>Specifies the maximum number of DataSync agents to list in a response. By default, a response shows a maximum of 100 agents.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>Specifies an opaque string that indicates the position to begin the next list of results in the response.</p>"}}, "documentation": "<p>ListAgentsRequest</p>"}, "ListAgentsResponse": {"type": "structure", "members": {"Agents": {"shape": "AgentList", "documentation": "<p>A list of DataSync agents in your Amazon Web Services account in the Amazon Web Services Region specified in the request. The list is ordered by the agents' Amazon Resource Names (ARNs).</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The opaque string that indicates the position to begin the next list of results in the response.</p>"}}, "documentation": "<p>ListAgentsResponse</p>"}, "ListLocationsRequest": {"type": "structure", "members": {"MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of locations to return.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>An opaque string that indicates the position at which to begin the next list of locations.</p>"}, "Filters": {"shape": "LocationFilters", "documentation": "<p>You can use API filters to narrow down the list of resources returned by <code>ListLocations</code>. For example, to retrieve all tasks on a specific source location, you can use <code>ListLocations</code> with filter name <code>LocationType S3</code> and <code>Operator Equals</code>.</p>"}}, "documentation": "<p>ListLocationsRequest</p>"}, "ListLocationsResponse": {"type": "structure", "members": {"Locations": {"shape": "LocationList", "documentation": "<p>An array that contains a list of locations.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>An opaque string that indicates the position at which to begin returning the next list of locations.</p>"}}, "documentation": "<p>ListLocationsResponse</p>"}, "ListTagsForResourceRequest": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "TaggableResourceArn", "documentation": "<p>Specifies the Amazon Resource Name (ARN) of the resource that you want tag information on.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>Specifies how many results that you want in the response.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>Specifies an opaque string that indicates the position to begin the next list of results in the response.</p>"}}, "documentation": "<p>ListTagsForResourceRequest</p>"}, "ListTagsForResourceResponse": {"type": "structure", "members": {"Tags": {"shape": "OutputTagList", "documentation": "<p>An array of tags applied to the specified resource.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The opaque string that indicates the position to begin the next list of results in the response.</p>"}}, "documentation": "<p>ListTagsForResourceResponse</p>"}, "ListTaskExecutionsRequest": {"type": "structure", "members": {"TaskArn": {"shape": "TaskArn", "documentation": "<p>Specifies the Amazon Resource Name (ARN) of the task that you want execution information about.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>Specifies how many results you want in the response.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>Specifies an opaque string that indicates the position at which to begin the next list of results in the response.</p>"}}, "documentation": "<p>ListTaskExecutions</p>"}, "ListTaskExecutionsResponse": {"type": "structure", "members": {"TaskExecutions": {"shape": "TaskExecutionList", "documentation": "<p>A list of the task's executions.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The opaque string that indicates the position to begin the next list of results in the response.</p>"}}, "documentation": "<p>ListTaskExecutionsResponse</p>"}, "ListTasksRequest": {"type": "structure", "members": {"MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of tasks to return.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>An opaque string that indicates the position at which to begin the next list of tasks.</p>"}, "Filters": {"shape": "TaskFilters", "documentation": "<p>You can use API filters to narrow down the list of resources returned by <code>ListTasks</code>. For example, to retrieve all tasks on a specific source location, you can use <code>ListTasks</code> with filter name <code>LocationId</code> and <code>Operator Equals</code> with the ARN for the location.</p>"}}, "documentation": "<p>ListTasksRequest</p>"}, "ListTasksResponse": {"type": "structure", "members": {"Tasks": {"shape": "TaskList", "documentation": "<p>A list of all the tasks that are returned.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>An opaque string that indicates the position at which to begin returning the next list of tasks.</p>"}}, "documentation": "<p>ListTasksResponse</p>"}, "LocationArn": {"type": "string", "max": 128, "pattern": "^arn:(aws|aws-cn|aws-us-gov|aws-iso|aws-iso-b):datasync:[a-z\\-0-9]+:[0-9]{12}:location/loc-[0-9a-z]{17}$"}, "LocationFilter": {"type": "structure", "required": ["Name", "Values", "Operator"], "members": {"Name": {"shape": "LocationFilterName", "documentation": "<p>The name of the filter being used. Each API call supports a list of filters that are available for it (for example, <code>LocationType</code> for <code>ListLocations</code>).</p>"}, "Values": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The values that you want to filter for. For example, you might want to display only Amazon S3 locations.</p>"}, "Operator": {"shape": "Operator", "documentation": "<p>The operator that is used to compare filter values (for example, <code>Equals</code> or <code>Contains</code>).</p>"}}, "documentation": "<p>Narrow down the list of resources returned by <code>ListLocations</code>. For example, to see all your Amazon S3 locations, create a filter using <code>\"Name\": \"LocationType\"</code>, <code>\"Operator\": \"Equals\"</code>, and <code>\"Values\": \"S3\"</code>.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/query-resources.html\">filtering resources</a>.</p>"}, "LocationFilterName": {"type": "string", "enum": ["LocationUri", "LocationType", "CreationTime"]}, "LocationFilters": {"type": "list", "member": {"shape": "LocationFilter"}}, "LocationList": {"type": "list", "member": {"shape": "LocationListEntry"}}, "LocationListEntry": {"type": "structure", "members": {"LocationArn": {"shape": "LocationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the location. For Network File System (NFS) or Amazon EFS, the location is the export path. For Amazon S3, the location is the prefix path that you want to mount and use as the root of the location.</p>"}, "LocationUri": {"shape": "LocationUri", "documentation": "<p>Represents a list of URIs of a location. <code>LocationUri</code> returns an array that contains a list of locations when the <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/API_ListLocations.html\">ListLocations</a> operation is called.</p> <p>Format: <code>TYPE://GLOBAL_ID/SUBDIR</code>.</p> <p>TYPE designates the type of location (for example, <code>nfs</code> or <code>s3</code>).</p> <p>GLOBAL_ID is the globally unique identifier of the resource that backs the location. An example for EFS is <code>us-east-2.fs-abcd1234</code>. An example for Amazon S3 is the bucket name, such as <code>myBucket</code>. An example for NFS is a valid IPv4 address or a hostname that is compliant with Domain Name Service (DNS).</p> <p>SUBDIR is a valid file system path, delimited by forward slashes as is the *nix convention. For NFS and Amazon EFS, it's the export path to mount the location. For Amazon S3, it's the prefix path that you mount to and treat as the root of the location.</p> <p/>"}}, "documentation": "<p>Represents a single entry in a list of locations. <code>LocationListEntry</code> returns an array that contains a list of locations when the <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/API_ListLocations.html\">ListLocations</a> operation is called.</p>"}, "LocationUri": {"type": "string", "max": 4360, "pattern": "^(efs|nfs|s3|smb|hdfs|fsx[a-z0-9-]+)://[a-zA-Z0-9.:/\\-]+$"}, "LogGroupArn": {"type": "string", "max": 562, "pattern": "^arn:(aws|aws-cn|aws-us-gov|aws-iso|aws-iso-b):logs:[a-z\\-0-9]+:[0-9]{12}:log-group:([^:\\*]*)(:\\*)?$"}, "LogLevel": {"type": "string", "enum": ["OFF", "BASIC", "TRANSFER"]}, "ManagedSecretConfig": {"type": "structure", "members": {"SecretArn": {"shape": "SecretArn", "documentation": "<p>Specifies the ARN for an Secrets Manager secret.</p>"}}, "documentation": "<p>Specifies configuration information for a DataSync-managed secret, such as an authentication token or set of credentials that DataSync uses to access a specific transfer location. DataSync uses the default Amazon Web Services-managed KMS key to encrypt this secret in Secrets Manager.</p>"}, "ManifestAction": {"type": "string", "enum": ["TRANSFER"]}, "ManifestConfig": {"type": "structure", "members": {"Action": {"shape": "ManifestAction", "documentation": "<p>Specifies what DataSync uses the manifest for.</p>"}, "Format": {"shape": "ManifestFormat", "documentation": "<p>Specifies the file format of your manifest. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/transferring-with-manifest.html#transferring-with-manifest-create\">Creating a manifest</a>.</p>"}, "Source": {"shape": "SourceManifestConfig", "documentation": "<p>Specifies the manifest that you want DataSync to use and where it's hosted.</p> <note> <p>You must specify this parameter if you're configuring a new manifest on or after February 7, 2024.</p> <p>If you don't, you'll get a 400 status code and <code>ValidationException</code> error stating that you're missing the IAM role for DataSync to access the S3 bucket where you're hosting your manifest. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/transferring-with-manifest.html#transferring-with-manifest-access\">Providing DataSync access to your manifest</a>.</p> </note>"}}, "documentation": "<p>Configures a manifest, which is a list of files or objects that you want DataSync to transfer. For more information and configuration examples, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/transferring-with-manifest.html\">Specifying what DataSync transfers by using a manifest</a>.</p>"}, "ManifestFormat": {"type": "string", "enum": ["CSV"]}, "MaxResults": {"type": "integer", "max": 100, "min": 0}, "Mtime": {"type": "string", "enum": ["NONE", "PRESERVE"]}, "NetworkInterfaceArn": {"type": "string", "max": 128, "pattern": "^arn:aws[\\-a-z]{0,}:ec2:[a-z\\-0-9]*:[0-9]{12}:network-interface/eni-[0-9a-f]+$"}, "NextToken": {"type": "string", "max": 65535, "pattern": "[a-zA-Z0-9=_-]+"}, "NfsMountOptions": {"type": "structure", "members": {"Version": {"shape": "NfsVersion", "documentation": "<p>Specifies the NFS version that you want DataSync to use when mounting your NFS share. If the server refuses to use the version specified, the task fails.</p> <p>You can specify the following options:</p> <ul> <li> <p> <code>AUTOMATIC</code> (default): DataSync chooses NFS version 4.1.</p> </li> <li> <p> <code>NFS3</code>: Stateless protocol version that allows for asynchronous writes on the server.</p> </li> <li> <p> <code>NFSv4_0</code>: Stateful, firewall-friendly protocol version that supports delegations and pseudo file systems.</p> </li> <li> <p> <code>NFSv4_1</code>: Stateful protocol version that supports sessions, directory delegations, and parallel data processing. NFS version 4.1 also includes all features available in version 4.0.</p> </li> </ul> <note> <p>DataSync currently only supports NFS version 3 with Amazon FSx for NetApp ONTAP locations.</p> </note>"}}, "documentation": "<p>Specifies how DataSync can access a location using the NFS protocol.</p>"}, "NfsSubdirectory": {"type": "string", "max": 4096, "pattern": "^[a-zA-Z0-9_\\-\\+\\./\\(\\)\\p{Zs}]+$"}, "NfsVersion": {"type": "string", "enum": ["AUTOMATIC", "NFS3", "NFS4_0", "NFS4_1"]}, "ObjectStorageAccessKey": {"type": "string", "max": 200, "min": 0, "pattern": "^.*$"}, "ObjectStorageBucketName": {"type": "string", "max": 63, "min": 3, "pattern": "^[a-zA-Z0-9_\\-\\+\\.\\(\\)\\$\\p{Zs}]+$"}, "ObjectStorageCertificate": {"type": "blob", "max": 32768}, "ObjectStorageSecretKey": {"type": "string", "max": 200, "min": 0, "pattern": "^.*$", "sensitive": true}, "ObjectStorageServerPort": {"type": "integer", "box": true, "max": 65536, "min": 1}, "ObjectStorageServerProtocol": {"type": "string", "enum": ["HTTPS", "HTTP"]}, "ObjectTags": {"type": "string", "enum": ["PRESERVE", "NONE"]}, "ObjectVersionIds": {"type": "string", "enum": ["INCLUDE", "NONE"]}, "OnPremConfig": {"type": "structure", "required": ["AgentArns"], "members": {"AgentArns": {"shape": "AgentArnList", "documentation": "<p>The Amazon Resource Names (ARNs) of the DataSync agents that can connect to your NFS file server.</p> <p>You can specify more than one agent. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/do-i-need-datasync-agent.html#multiple-agents\">Using multiple DataSync agents</a>.</p>"}}, "documentation": "<p>The DataSync agents that can connect to your Network File System (NFS) file server.</p>"}, "Operator": {"type": "string", "enum": ["Equals", "NotEquals", "In", "LessThanOrEqual", "<PERSON><PERSON><PERSON>", "GreaterThanOrEqual", "GreaterThan", "Contains", "NotContains", "BeginsWith"]}, "Options": {"type": "structure", "members": {"VerifyMode": {"shape": "VerifyMode", "documentation": "<p>Specifies if and how <PERSON><PERSON><PERSON> checks the integrity of your data at the end of your transfer.</p> <ul> <li> <p> <code>ONLY_FILES_TRANSFERRED</code> (recommended) - DataSync calculates the checksum of transferred data (including metadata) at the source location. At the end of the transfer, <PERSON><PERSON><PERSON> then compares this checksum to the checksum calculated on that data at the destination.</p> <note> <p>This is the default option for <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/choosing-task-mode.html\">Enhanced mode tasks</a>.</p> </note> <p>We recommend this option when transferring to S3 Glacier Flexible Retrieval or S3 Glacier Deep Archive storage classes. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-s3-location.html#using-storage-classes\">Storage class considerations with Amazon S3 locations</a>.</p> </li> <li> <p> <code>POINT_IN_TIME_CONSISTENT</code> - At the end of the transfer, <PERSON><PERSON><PERSON> checks the entire source and destination to verify that both locations are fully synchronized.</p> <note> <p>The is the default option for <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/choosing-task-mode.html\">Basic mode tasks</a> and isn't currently supported with Enhanced mode tasks.</p> </note> <p>If you use a <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/transferring-with-manifest.html\">manifest</a>, DataSync only scans and verifies what's listed in the manifest.</p> <p>You can't use this option when transferring to S3 Glacier Flexible Retrieval or S3 Glacier Deep Archive storage classes. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-s3-location.html#using-storage-classes\">Storage class considerations with Amazon S3 locations</a>.</p> </li> <li> <p> <code>NONE</code> - DataSync performs data integrity checks only during your transfer. Unlike other options, there's no additional verification at the end of your transfer.</p> </li> </ul>"}, "OverwriteMode": {"shape": "OverwriteMode", "documentation": "<p>Specifies whether DataSync should modify or preserve data at the destination location.</p> <ul> <li> <p> <code>ALWAYS</code> (default) - DataSync modifies data in the destination location when source data (including metadata) has changed.</p> <p>If DataSync overwrites objects, you might incur additional charges for certain Amazon S3 storage classes (for example, for retrieval or early deletion). For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-s3-location.html#using-storage-classes\">Storage class considerations with Amazon S3 transfers</a>.</p> </li> <li> <p> <code>NEVER</code> - DataSync doesn't overwrite data in the destination location even if the source data has changed. You can use this option to protect against overwriting changes made to files or objects in the destination.</p> </li> </ul>"}, "Atime": {"shape": "Atime", "documentation": "<p>Specifies whether to preserve metadata indicating the last time a file was read or written to.</p> <note> <p>The behavior of <code>Atime</code> isn't fully standard across platforms, so DataSync can only do this on a best-effort basis.</p> </note> <ul> <li> <p> <code>BEST_EFFORT</code> (default) - DataSync attempts to preserve the original <code>Atime</code> attribute on all source files (that is, the version before the <code>PREPARING</code> steps of the task execution). This option is recommended.</p> </li> <li> <p> <code>NONE</code> - Ignores <code>Atime</code>.</p> </li> </ul> <note> <p>If <code>Atime</code> is set to <code>BEST_EFFORT</code>, <code>Mtime</code> must be set to <code>PRESERVE</code>. </p> <p>If <code>Atime</code> is set to <code>NONE</code>, <code>Mtime</code> must also be <code>NONE</code>. </p> </note>"}, "Mtime": {"shape": "Mtime", "documentation": "<p>Specifies whether to preserve metadata indicating the last time that a file was written to before the <code>PREPARING</code> step of your task execution. This option is required when you need to run the a task more than once.</p> <ul> <li> <p> <code>PRESERVE</code> (default) - Preserves original <code>Mtime</code>, which is recommended.</p> </li> <li> <p> <code>NONE</code> - Ignores <code>Mtime</code>.</p> </li> </ul> <note> <p>If <code>Mtime</code> is set to <code>PRESERVE</code>, <code>Atime</code> must be set to <code>BEST_EFFORT</code>.</p> <p>If <code>Mtime</code> is set to <code>NONE</code>, <code>Atime</code> must also be set to <code>NONE</code>. </p> </note>"}, "Uid": {"shape": "<PERSON><PERSON>", "documentation": "<p>Specifies the POSIX user ID (UID) of the file's owner.</p> <ul> <li> <p> <code>INT_VALUE</code> (default) - Preserves the integer value of UID and group ID (GID), which is recommended.</p> </li> <li> <p> <code>NONE</code> - Ignores UID and GID. </p> </li> </ul> <p>For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/special-files.html#metadata-copied\">Metadata copied by DataSync</a>.</p>"}, "Gid": {"shape": "Gid", "documentation": "<p>Specifies the POSIX group ID (GID) of the file's owners.</p> <ul> <li> <p> <code>INT_VALUE</code> (default) - Preserves the integer value of user ID (UID) and GID, which is recommended.</p> </li> <li> <p> <code>NONE</code> - Ignores UID and GID.</p> </li> </ul> <p>For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/metadata-copied.html\">Understanding how DataSync handles file and object metadata</a>.</p>"}, "PreserveDeletedFiles": {"shape": "PreserveDeletedFiles", "documentation": "<p>Specifies whether files in the destination location that don't exist in the source should be preserved. This option can affect your Amazon S3 storage cost. If your task deletes objects, you might incur minimum storage duration charges for certain storage classes. For detailed information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-s3-location.html#using-storage-classes\">Considerations when working with Amazon S3 storage classes in DataSync</a>.</p> <ul> <li> <p> <code>PRESERVE</code> (default) - Ignores such destination files, which is recommended. </p> </li> <li> <p> <code>REMOVE</code> - Deletes destination files that aren’t present in the source.</p> </li> </ul> <note> <p>If you set this parameter to <code>REMOVE</code>, you can't set <code>TransferMode</code> to <code>ALL</code>. When you transfer all data, DataSync doesn't scan your destination location and doesn't know what to delete.</p> </note>"}, "PreserveDevices": {"shape": "PreserveDevices", "documentation": "<p>Specifies whether DataSync should preserve the metadata of block and character devices in the source location and recreate the files with that device name and metadata on the destination. DataSync copies only the name and metadata of such devices.</p> <note> <p>DataSync can't copy the actual contents of these devices because they're nonterminal and don't return an end-of-file (EOF) marker.</p> </note> <ul> <li> <p> <code>NONE</code> (default) - Ignores special devices (recommended).</p> </li> <li> <p> <code>PRESERVE</code> - Preserves character and block device metadata. This option currently isn't supported for Amazon EFS.</p> </li> </ul>"}, "PosixPermissions": {"shape": "PosixPermissions", "documentation": "<p>Specifies which users or groups can access a file for a specific purpose such as reading, writing, or execution of the file.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/metadata-copied.html\">Understanding how DataSync handles file and object metadata</a>.</p> <ul> <li> <p> <code>PRESERVE</code> (default) - Preserves POSIX-style permissions, which is recommended.</p> </li> <li> <p> <code>NONE</code> - Ignores POSIX-style permissions. </p> </li> </ul> <note> <p>DataSync can preserve extant permissions of a source location.</p> </note>"}, "BytesPerSecond": {"shape": "BytesPerSecond", "documentation": "<p>Limits the bandwidth used by a DataSync task. For example, if you want DataSync to use a maximum of 1 MB, set this value to <code>1048576</code> (<code>=1024*1024</code>).</p> <note> <p>Not applicable to <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/choosing-task-mode.html\">Enhanced mode tasks</a>.</p> </note>"}, "TaskQueueing": {"shape": "TaskQueueing", "documentation": "<p>Specifies whether your transfer tasks should be put into a queue during certain scenarios when <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/run-task.html#running-multiple-tasks\">running multiple tasks</a>. This is <code>ENABLED</code> by default.</p>"}, "LogLevel": {"shape": "LogLevel", "documentation": "<p>Specifies the type of logs that DataSync publishes to a Amazon CloudWatch Logs log group. To specify the log group, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/API_CreateTask.html#DataSync-CreateTask-request-CloudWatchLogGroupArn\">CloudWatchLogGroupArn</a>.</p> <ul> <li> <p> <code>BASIC</code> - Publishes logs with only basic information (such as transfer errors).</p> </li> <li> <p> <code>TRANSFER</code> - Publishes logs for all files or objects that your DataSync task transfers and performs data-integrity checks on.</p> </li> <li> <p> <code>OFF</code> - No logs are published.</p> </li> </ul>"}, "TransferMode": {"shape": "TransferMode", "documentation": "<p>Specifies whether DataSync transfers only the data (including metadata) that differs between locations following an initial copy or transfers all data every time you run the task. If you're planning on recurring transfers, you might only want to transfer what's changed since your previous task execution.</p> <ul> <li> <p> <code>CHANGED</code> (default) - After your initial full transfer, DataSync copies only the data and metadata that differs between the source and destination location.</p> </li> <li> <p> <code>ALL</code> - DataSync copies everything in the source to the destination without comparing differences between the locations.</p> </li> </ul>"}, "SecurityDescriptorCopyFlags": {"shape": "SmbSecurityDescriptorCopyFlags", "documentation": "<p>Specifies which components of the SMB security descriptor are copied from source to destination objects. </p> <p>This value is only used for transfers between SMB and Amazon FSx for Windows File Server locations or between two FSx for Windows File Server locations. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/metadata-copied.html\">Understanding how DataSync handles file and object metadata</a>.</p> <ul> <li> <p> <code>OWNER_DACL</code> (default) - For each copied object, DataSync copies the following metadata:</p> <ul> <li> <p>The object owner.</p> </li> <li> <p>NTFS discretionary access control lists (DACLs), which determine whether to grant access to an object.</p> <p>DataSync won't copy NTFS system access control lists (SACLs) with this option.</p> </li> </ul> </li> <li> <p> <code>OWNER_DACL_SACL</code> - For each copied object, DataSync copies the following metadata:</p> <ul> <li> <p>The object owner.</p> </li> <li> <p>NTFS discretionary access control lists (DACLs), which determine whether to grant access to an object.</p> </li> <li> <p>SACLs, which are used by administrators to log attempts to access a secured object.</p> <p>Copying SACLs requires granting additional permissions to the Windows user that DataSync uses to access your SMB location. For information about choosing a user with the right permissions, see required permissions for <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-smb-location.html#configuring-smb-permissions\">SMB</a>, <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-fsx-location.html#create-fsx-windows-location-permissions\">FSx for Windows File Server</a>, or <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-ontap-location.html#create-ontap-location-smb\">FSx for ONTAP</a> (depending on the type of location in your transfer).</p> </li> </ul> </li> <li> <p> <code>NONE</code> - None of the SMB security descriptor components are copied. Destination objects are owned by the user that was provided for accessing the destination location. DACLs and SACLs are set based on the destination server’s configuration. </p> </li> </ul>"}, "ObjectTags": {"shape": "ObjectTags", "documentation": "<p>Specifies whether you want DataSync to <code>PRESERVE</code> object tags (default behavior) when transferring between object storage systems. If you want your DataSync task to ignore object tags, specify the <code>NONE</code> value.</p>"}}, "documentation": "<p>Indicates how your transfer task is configured. These options include how DataSync handles files, objects, and their associated metadata during your transfer. You also can specify how to verify data integrity, set bandwidth limits for your task, among other options.</p> <p>Each option has a default value. Unless you need to, you don't have to configure any option before calling <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/API_StartTaskExecution.html\">StartTaskExecution</a>.</p> <p>You also can override your task options for each task execution. For example, you might want to adjust the <code>LogLevel</code> for an individual execution.</p>"}, "OutputTagList": {"type": "list", "member": {"shape": "TagListEntry"}, "max": 55, "min": 0}, "OverwriteMode": {"type": "string", "enum": ["ALWAYS", "NEVER"]}, "PLSecurityGroupArnList": {"type": "list", "member": {"shape": "Ec2SecurityGroupArn"}, "max": 1, "min": 1}, "PLSubnetArnList": {"type": "list", "member": {"shape": "Ec2SubnetArn"}, "max": 1, "min": 1}, "PhaseStatus": {"type": "string", "enum": ["PENDING", "SUCCESS", "ERROR"]}, "Platform": {"type": "structure", "members": {"Version": {"shape": "AgentVersion", "documentation": "<p>The version of the DataSync agent.</p>"}}, "documentation": "<p>The platform-related details about the DataSync agent, such as the version number.</p>"}, "PosixPermissions": {"type": "string", "enum": ["NONE", "PRESERVE"]}, "PreserveDeletedFiles": {"type": "string", "enum": ["PRESERVE", "REMOVE"]}, "PreserveDevices": {"type": "string", "enum": ["NONE", "PRESERVE"]}, "PrivateLinkConfig": {"type": "structure", "members": {"VpcEndpointId": {"shape": "VpcEndpointId", "documentation": "<p>Specifies the ID of the VPC endpoint that your agent connects to.</p>"}, "PrivateLinkEndpoint": {"shape": "Endpoint", "documentation": "<p>Specifies the VPC endpoint provided by <a href=\"https://docs.aws.amazon.com/vpc/latest/privatelink/privatelink-share-your-services.html\">Amazon Web Services PrivateLink</a> that your agent connects to.</p>"}, "SubnetArns": {"shape": "PLSubnetArnList", "documentation": "<p>Specifies the ARN of the subnet where your VPC endpoint is located. You can only specify one ARN.</p>"}, "SecurityGroupArns": {"shape": "PLSecurityGroupArnList", "documentation": "<p>Specifies the Amazon Resource Names (ARN) of the security group that provides DataSync access to your VPC endpoint. You can only specify one ARN.</p>"}}, "documentation": "<p>Specifies how your DataSync agent connects to Amazon Web Services using a <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/choose-service-endpoint.html#choose-service-endpoint-vpc\">virtual private cloud (VPC) service endpoint</a>. An agent that uses a VPC endpoint isn't accessible over the public internet.</p>"}, "QopConfiguration": {"type": "structure", "members": {"RpcProtection": {"shape": "HdfsRpcProtection", "documentation": "<p>The RPC protection setting configured on the HDFS cluster. This setting corresponds to your <code>hadoop.rpc.protection</code> setting in your <code>core-site.xml</code> file on your Hadoop cluster.</p>"}, "DataTransferProtection": {"shape": "HdfsDataTransferProtection", "documentation": "<p>The data transfer protection setting configured on the HDFS cluster. This setting corresponds to your <code>dfs.data.transfer.protection</code> setting in the <code>hdfs-site.xml</code> file on your Hadoop cluster.</p>"}}, "documentation": "<p>The Quality of Protection (QOP) configuration specifies the Remote Procedure Call (RPC) and data transfer privacy settings configured on the Hadoop Distributed File System (HDFS) cluster.</p>"}, "ReportDestination": {"type": "structure", "members": {"S3": {"shape": "ReportDestinationS3", "documentation": "<p>Specifies the Amazon S3 bucket where DataSync uploads your task report.</p>"}}, "documentation": "<p>Specifies where DataSync uploads your <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/task-reports.html\">task report</a>.</p>"}, "ReportDestinationS3": {"type": "structure", "required": ["S3BucketArn", "BucketAccessRoleArn"], "members": {"Subdirectory": {"shape": "S3Subdirectory", "documentation": "<p>Specifies a bucket prefix for your report.</p>"}, "S3BucketArn": {"shape": "S3BucketArn", "documentation": "<p>Specifies the ARN of the S3 bucket where DataSync uploads your report.</p>"}, "BucketAccessRoleArn": {"shape": "IamRoleArn", "documentation": "<p>Specifies the Amazon Resource Name (ARN) of the IAM policy that allows DataSync to upload a task report to your S3 bucket. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/task-reports.html\">Allowing DataSync to upload a task report to an Amazon S3 bucket</a>.</p>"}}, "documentation": "<p>Specifies the Amazon S3 bucket where DataSync uploads your <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/task-reports.html\">task report</a>.</p>"}, "ReportLevel": {"type": "string", "enum": ["ERRORS_ONLY", "SUCCESSES_AND_ERRORS"]}, "ReportOutputType": {"type": "string", "enum": ["SUMMARY_ONLY", "STANDARD"]}, "ReportOverride": {"type": "structure", "members": {"ReportLevel": {"shape": "ReportLevel", "documentation": "<p>Specifies whether your task report includes errors only or successes and errors.</p> <p>For example, your report might mostly include only what didn't go well in your transfer (<code>ERRORS_ONLY</code>). At the same time, you want to verify that your <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/filtering.html\">task filter</a> is working correctly. In this situation, you can get a list of what files DataSync successfully skipped and if something transferred that you didn't to transfer (<code>SUCCESSES_AND_ERRORS</code>).</p>"}}, "documentation": "<p>Specifies the level of detail for a particular aspect of your DataSync <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/task-reports.html\">task report</a>.</p>"}, "ReportOverrides": {"type": "structure", "members": {"Transferred": {"shape": "ReportOverride", "documentation": "<p>Specifies the level of reporting for the files, objects, and directories that DataSync attempted to transfer.</p>"}, "Verified": {"shape": "ReportOverride", "documentation": "<p>Specifies the level of reporting for the files, objects, and directories that DataSync attempted to verify at the end of your transfer.</p>"}, "Deleted": {"shape": "ReportOverride", "documentation": "<p>Specifies the level of reporting for the files, objects, and directories that DataSync attempted to delete in your destination location. This only applies if you <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/configure-metadata.html\">configure your task</a> to delete data in the destination that isn't in the source.</p>"}, "Skipped": {"shape": "ReportOverride", "documentation": "<p>Specifies the level of reporting for the files, objects, and directories that DataSync attempted to skip during your transfer.</p>"}}, "documentation": "<p>The level of detail included in each aspect of your DataSync <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/task-reports.html\">task report</a>.</p>"}, "ReportResult": {"type": "structure", "members": {"Status": {"shape": "PhaseStatus", "documentation": "<p>Indicates whether DataSync is still working on your report, created a report, or can't create a complete report.</p>"}, "ErrorCode": {"shape": "string", "documentation": "<p>Indicates the code associated with the error if DataSync can't create a complete report.</p>"}, "ErrorDetail": {"shape": "string", "documentation": "<p>Provides details about issues creating a report.</p>"}}, "documentation": "<p>Indicates whether DataSync created a complete <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/task-reports.html\">task report</a> for your transfer.</p>"}, "S3BucketArn": {"type": "string", "max": 268, "pattern": "^arn:(aws|aws-cn|aws-us-gov|aws-iso|aws-iso-b):s3:[a-z\\-0-9]*:[0-9]{12}:accesspoint[/:][a-zA-Z0-9\\-.]{1,63}$|^arn:(aws|aws-cn|aws-us-gov|aws-iso|aws-iso-b):s3-outposts:[a-z\\-0-9]+:[0-9]{12}:outpost[/:][a-zA-Z0-9\\-]{1,63}[/:]accesspoint[/:][a-zA-Z0-9\\-]{1,63}$|^arn:(aws|aws-cn|aws-us-gov|aws-iso|aws-iso-b):s3:::[a-zA-Z0-9.\\-_]{1,255}$"}, "S3Config": {"type": "structure", "required": ["BucketAccessRoleArn"], "members": {"BucketAccessRoleArn": {"shape": "IamRoleArn", "documentation": "<p>Specifies the ARN of the IAM role that DataSync uses to access your S3 bucket.</p>"}}, "documentation": "<p>Specifies the Amazon Resource Name (ARN) of the Identity and Access Management (IAM) role that DataSync uses to access your S3 bucket.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-s3-location.html#create-s3-location-access\">Providing DataSync access to S3 buckets</a>.</p>"}, "S3ManifestConfig": {"type": "structure", "required": ["ManifestObjectPath", "BucketAccessRoleArn", "S3BucketArn"], "members": {"ManifestObjectPath": {"shape": "S3Subdirectory", "documentation": "<p>Specifies the Amazon S3 object key of your manifest. This can include a prefix (for example, <code>prefix/my-manifest.csv</code>).</p>"}, "BucketAccessRoleArn": {"shape": "IamRoleArn", "documentation": "<p>Specifies the Identity and Access Management (IAM) role that allows DataSync to access your manifest. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/transferring-with-manifest.html#transferring-with-manifest-access\">Providing DataSync access to your manifest</a>.</p>"}, "S3BucketArn": {"shape": "S3BucketArn", "documentation": "<p>Specifies the Amazon Resource Name (ARN) of the S3 bucket where you're hosting your manifest.</p>"}, "ManifestObjectVersionId": {"shape": "S3ObjectVersionId", "documentation": "<p>Specifies the object version ID of the manifest that you want DataSync to use. If you don't set this, DataSync uses the latest version of the object.</p>"}}, "documentation": "<p>Specifies the S3 bucket where you're hosting the manifest that you want DataSync to use. For more information and configuration examples, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/transferring-with-manifest.html\">Specifying what DataSync transfers by using a manifest</a>.</p>"}, "S3ObjectVersionId": {"type": "string", "max": 100, "min": 1, "pattern": "^.+$"}, "S3StorageClass": {"type": "string", "enum": ["STANDARD", "STANDARD_IA", "ONEZONE_IA", "INTELLIGENT_TIERING", "GLACIER", "DEEP_ARCHIVE", "OUTPOSTS", "GLACIER_INSTANT_RETRIEVAL"]}, "S3Subdirectory": {"type": "string", "max": 4096, "pattern": "^[a-zA-Z0-9_\\-\\+\\./\\(\\)\\p{Zs}]*$"}, "ScheduleDisabledBy": {"type": "string", "enum": ["USER", "SERVICE"]}, "ScheduleDisabledReason": {"type": "string", "max": 8192, "pattern": "^[\\w\\s.,'?!:;\\/=|<>()-]*$"}, "ScheduleExpressionCron": {"type": "string", "max": 256, "pattern": "^[a-zA-Z0-9\\ \\_\\*\\?\\,\\|\\^\\-\\/\\#\\s\\(\\)\\+]*$"}, "ScheduleStatus": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "SecretArn": {"type": "string", "max": 2048, "pattern": "^(arn:(aws|aws-cn|aws-us-gov|aws-iso|aws-iso-b):secretsmanager:[a-z\\-0-9]+:[0-9]{12}:secret:.*|)$"}, "ServerHostname": {"type": "string", "max": 255, "pattern": "^(([a-zA-Z0-9\\-]*[a-zA-Z0-9])\\.)*([A-Za-z0-9\\-]*[A-Za-z0-9])$"}, "ServerIpAddress": {"type": "string", "max": 15, "min": 7, "pattern": "\\A(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)(\\.(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)){3}\\z"}, "SmbAuthenticationType": {"type": "string", "enum": ["NTLM", "KERBEROS"]}, "SmbDomain": {"type": "string", "max": 253, "pattern": "^[A-Za-z0-9]((\\.|-+)?[A-Za-z0-9]){0,252}$"}, "SmbMountOptions": {"type": "structure", "members": {"Version": {"shape": "SmbVersion", "documentation": "<p>By default, DataSync automatically chooses an SMB protocol version based on negotiation with your SMB file server. You also can configure DataSync to use a specific SMB version, but we recommend doing this only if DataSync has trouble negotiating with the SMB file server automatically.</p> <p>These are the following options for configuring the SMB version:</p> <ul> <li> <p> <code>AUTOMATIC</code> (default): DataS<PERSON> and the SMB file server negotiate the highest version of SMB that they mutually support between 2.1 and 3.1.1.</p> <p>This is the recommended option. If you instead choose a specific version that your file server doesn't support, you may get an <code>Operation Not Supported</code> error.</p> </li> <li> <p> <code>SMB3</code>: Restricts the protocol negotiation to only SMB version 3.0.2.</p> </li> <li> <p> <code>SMB2</code>: Restricts the protocol negotiation to only SMB version 2.1.</p> </li> <li> <p> <code>SMB2_0</code>: Restricts the protocol negotiation to only SMB version 2.0.</p> </li> <li> <p> <code>SMB1</code>: Restricts the protocol negotiation to only SMB version 1.0.</p> <note> <p>The <code>SMB1</code> option isn't available when <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/API_CreateLocationFsxOntap.html\">creating an Amazon FSx for NetApp ONTAP location</a>.</p> </note> </li> </ul>"}}, "documentation": "<p>Specifies the version of the Server Message Block (SMB) protocol that DataSync uses to access an SMB file server.</p>"}, "SmbPassword": {"type": "string", "max": 104, "pattern": "^.{0,104}$", "sensitive": true}, "SmbSecurityDescriptorCopyFlags": {"type": "string", "enum": ["NONE", "OWNER_DACL", "OWNER_DACL_SACL"]}, "SmbSubdirectory": {"type": "string", "max": 4096, "pattern": "^[a-zA-Z0-9_\\-\\+\\./\\(\\)\\$\\p{Zs}]+$"}, "SmbUser": {"type": "string", "max": 104, "pattern": "^[^\\x22\\x5B\\x5D/\\\\:;|=,+*?\\x3C\\x3E]{1,104}$"}, "SmbVersion": {"type": "string", "enum": ["AUTOMATIC", "SMB2", "SMB3", "SMB1", "SMB2_0"]}, "SourceManifestConfig": {"type": "structure", "required": ["S3"], "members": {"S3": {"shape": "S3ManifestConfig", "documentation": "<p>Specifies the S3 bucket where you're hosting your manifest.</p>"}}, "documentation": "<p>Specifies the manifest that you want DataSync to use and where it's hosted. For more information and configuration examples, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/transferring-with-manifest.html\">Specifying what DataSync transfers by using a manifest</a>.</p>"}, "SourceNetworkInterfaceArns": {"type": "list", "member": {"shape": "NetworkInterfaceArn"}}, "StartTaskExecutionRequest": {"type": "structure", "required": ["TaskArn"], "members": {"TaskArn": {"shape": "TaskArn", "documentation": "<p>Specifies the Amazon Resource Name (ARN) of the task that you want to start.</p>"}, "OverrideOptions": {"shape": "Options"}, "Includes": {"shape": "FilterList", "documentation": "<p>Specifies a list of filter rules that determines which files to include when running a task. The pattern should contain a single filter string that consists of the patterns to include. The patterns are delimited by \"|\" (that is, a pipe), for example, <code>\"/folder1|/folder2\"</code>. </p>"}, "Excludes": {"shape": "FilterList", "documentation": "<p>Specifies a list of filter rules that determines which files to exclude from a task. The list contains a single filter string that consists of the patterns to exclude. The patterns are delimited by \"|\" (that is, a pipe), for example, <code>\"/folder1|/folder2\"</code>. </p>"}, "ManifestConfig": {"shape": "ManifestConfig", "documentation": "<p>Configures a manifest, which is a list of files or objects that you want DataSync to transfer. For more information and configuration examples, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/transferring-with-manifest.html\">Specifying what DataSync transfers by using a manifest</a>.</p> <p>When using this parameter, your caller identity (the role that you're using DataSync with) must have the <code>iam:PassRole</code> permission. The <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/security-iam-awsmanpol.html#security-iam-awsmanpol-awsdatasyncfullaccess\">AWSDataSyncFullAccess</a> policy includes this permission.</p> <p>To remove a manifest configuration, specify this parameter with an empty value.</p>"}, "TaskReportConfig": {"shape": "TaskReportConfig", "documentation": "<p>Specifies how you want to configure a task report, which provides detailed information about your DataSync transfer. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/task-reports.html\">Monitoring your DataSync transfers with task reports</a>.</p> <p>When using this parameter, your caller identity (the role that you're using DataSync with) must have the <code>iam:PassRole</code> permission. The <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/security-iam-awsmanpol.html#security-iam-awsmanpol-awsdatasyncfullaccess\">AWSDataSyncFullAccess</a> policy includes this permission.</p> <p>To remove a task report configuration, specify this parameter as empty.</p>"}, "Tags": {"shape": "InputTagList", "documentation": "<p>Specifies the tags that you want to apply to the Amazon Resource Name (ARN) representing the task execution.</p> <p> <i>Tags</i> are key-value pairs that help you manage, filter, and search for your DataSync resources.</p>"}}, "documentation": "<p>StartTaskExecutionRequest</p>"}, "StartTaskExecutionResponse": {"type": "structure", "members": {"TaskExecutionArn": {"shape": "TaskExecutionArn", "documentation": "<p>The ARN of the running task execution.</p>"}}, "documentation": "<p>StartTaskExecutionResponse</p>"}, "StorageVirtualMachineArn": {"type": "string", "max": 162, "pattern": "^arn:(aws|aws-cn|aws-us-gov|aws-iso|aws-iso-b):fsx:[a-z\\-0-9]+:[0-9]{12}:storage-virtual-machine/fs-[0-9a-f]+/svm-[0-9a-f]{17,}$"}, "TagKey": {"type": "string", "max": 256, "min": 1, "pattern": "^[a-zA-Z0-9\\s+=._:/-]+$"}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 50, "min": 1}, "TagListEntry": {"type": "structure", "required": ["Key"], "members": {"Key": {"shape": "TagKey", "documentation": "<p>The key for an Amazon Web Services resource tag.</p>"}, "Value": {"shape": "TagValue", "documentation": "<p>The value for an Amazon Web Services resource tag.</p>"}}, "documentation": "<p>A key-value pair representing a single tag that's been applied to an Amazon Web Services resource.</p>"}, "TagResourceRequest": {"type": "structure", "required": ["ResourceArn", "Tags"], "members": {"ResourceArn": {"shape": "TaggableResourceArn", "documentation": "<p>Specifies the Amazon Resource Name (ARN) of the resource to apply the tag to.</p>"}, "Tags": {"shape": "InputTagList", "documentation": "<p>Specifies the tags that you want to apply to the resource.</p>"}}, "documentation": "<p>TagResourceRequest</p>"}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0, "pattern": "^[a-zA-Z0-9\\s+=._:@/-]+$"}, "TaggableResourceArn": {"type": "string", "max": 128, "pattern": "^arn:(aws|aws-cn|aws-us-gov|aws-iso|aws-iso-b):datasync:[a-z\\-0-9]+:[0-9]{12}:(agent|task|location|system)/((agent|task|loc)-[a-f0-9]{17}|storage-system-[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12})(/execution/exec-[a-f0-9]{17})?$"}, "TaskArn": {"type": "string", "max": 128, "pattern": "^arn:(aws|aws-cn|aws-us-gov|aws-iso|aws-iso-b):datasync:[a-z\\-0-9]*:[0-9]{12}:task/task-[0-9a-f]{17}$"}, "TaskExecutionArn": {"type": "string", "max": 128, "pattern": "^arn:(aws|aws-cn|aws-us-gov|aws-iso|aws-iso-b):datasync:[a-z\\-0-9]*:[0-9]{12}:task/task-[0-9a-f]{17}/execution/exec-[0-9a-f]{17}$"}, "TaskExecutionFilesFailedDetail": {"type": "structure", "members": {"Prepare": {"shape": "long", "documentation": "<p>The number of objects that DataSync fails to prepare during your task execution.</p>"}, "Transfer": {"shape": "long", "documentation": "<p>The number of objects that DataSync fails to transfer during your task execution.</p>"}, "Verify": {"shape": "long", "documentation": "<p>The number of objects that DataSync fails to verify during your task execution.</p>"}, "Delete": {"shape": "long", "documentation": "<p>The number of objects that DataSync fails to delete during your task execution.</p>"}}, "documentation": "<p>The number of objects that <PERSON><PERSON><PERSON> fails to prepare, transfer, verify, and delete during your task execution.</p> <note> <p>Applies only to <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/choosing-task-mode.html\">Enhanced mode tasks</a>.</p> </note>"}, "TaskExecutionFilesListedDetail": {"type": "structure", "members": {"AtSource": {"shape": "long", "documentation": "<p>The number of objects that DataSync finds at your source location.</p> <ul> <li> <p>With a <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/transferring-with-manifest.html\">manifest</a>, DataSync lists only what's in your manifest (and not everything at your source location).</p> </li> <li> <p>With an include <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/filtering.html\">filter</a>, DataSync lists only what matches the filter at your source location.</p> </li> <li> <p>With an exclude filter, DataSync lists everything at your source location before applying the filter.</p> </li> </ul>"}, "AtDestinationForDelete": {"shape": "long", "documentation": "<p>The number of objects that DataS<PERSON> finds at your destination location. This counter is only applicable if you <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/configure-metadata.html#task-option-file-object-handling\">configure your task</a> to delete data in the destination that isn't in the source.</p>"}}, "documentation": "<p>The number of objects that DataS<PERSON> finds at your locations.</p> <note> <p>Applies only to <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/choosing-task-mode.html\">Enhanced mode tasks</a>.</p> </note>"}, "TaskExecutionList": {"type": "list", "member": {"shape": "TaskExecutionListEntry"}}, "TaskExecutionListEntry": {"type": "structure", "members": {"TaskExecutionArn": {"shape": "TaskExecutionArn", "documentation": "<p>The Amazon Resource Name (ARN) of a task execution.</p>"}, "Status": {"shape": "TaskExecutionStatus", "documentation": "<p>The status of a task execution. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/understand-task-statuses.html#understand-task-execution-statuses\">Task execution statuses</a>.</p>"}, "TaskMode": {"shape": "TaskMode", "documentation": "<p>The task mode that you're using. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/choosing-task-mode.html\">Choosing a task mode for your data transfer</a>.</p>"}}, "documentation": "<p>Represents a single entry in a list of DataSync task executions that's returned with the <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/API_ListTaskExecutions.html\">ListTaskExecutions</a> operation.</p>"}, "TaskExecutionResultDetail": {"type": "structure", "members": {"PrepareDuration": {"shape": "Duration", "documentation": "<p>The time in milliseconds that your task execution was in the <code>PREPARING</code> step. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/run-task.html#understand-task-execution-statuses\">Task execution statuses</a>.</p> <p>For Enhanced mode tasks, the value is always <code>0</code>. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/how-datasync-transfer-works.html#how-datasync-prepares\">How DataSync prepares your data transfer</a>.</p>"}, "PrepareStatus": {"shape": "PhaseStatus", "documentation": "<p>The status of the <code>PREPARING</code> step for your task execution. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/run-task.html#understand-task-execution-statuses\">Task execution statuses</a>.</p>"}, "TotalDuration": {"shape": "Duration", "documentation": "<p>The time in milliseconds that your task execution ran.</p>"}, "TransferDuration": {"shape": "Duration", "documentation": "<p>The time in milliseconds that your task execution was in the <code>TRANSFERRING</code> step. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/run-task.html#understand-task-execution-statuses\">Task execution statuses</a>.</p> <p>For Enhanced mode tasks, the value is always <code>0</code>. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/how-datasync-transfer-works.html#how-datasync-transfers\">How DataSync transfers your data</a>.</p>"}, "TransferStatus": {"shape": "PhaseStatus", "documentation": "<p>The status of the <code>TRANSFERRING</code> step for your task execution. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/run-task.html#understand-task-execution-statuses\">Task execution statuses</a>.</p>"}, "VerifyDuration": {"shape": "Duration", "documentation": "<p>The time in milliseconds that your task execution was in the <code>VERIFYING</code> step. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/run-task.html#understand-task-execution-statuses\">Task execution statuses</a>.</p> <p>For Enhanced mode tasks, the value is always <code>0</code>. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/how-datasync-transfer-works.html#how-verifying-works\">How DataSync verifies your data's integrity</a>.</p>"}, "VerifyStatus": {"shape": "PhaseStatus", "documentation": "<p>The status of the <code>VERIFYING</code> step for your task execution. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/run-task.html#understand-task-execution-statuses\">Task execution statuses</a>.</p>"}, "ErrorCode": {"shape": "string", "documentation": "<p>An error that DataSync encountered during your task execution. You can use this information to help <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/troubleshooting-datasync-locations-tasks.html\">troubleshoot issues</a>.</p>"}, "ErrorDetail": {"shape": "string", "documentation": "<p>The detailed description of an error that <PERSON>S<PERSON> encountered during your task execution. You can use this information to help <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/troubleshooting-datasync-locations-tasks.html\">troubleshoot issues</a>. </p>"}}, "documentation": "<p>Provides detailed information about the result of your DataSync task execution.</p>"}, "TaskExecutionStatus": {"type": "string", "enum": ["QUEUED", "CANCELLING", "LAUNCHING", "PREPARING", "TRANSFERRING", "VERIFYING", "SUCCESS", "ERROR"]}, "TaskFilter": {"type": "structure", "required": ["Name", "Values", "Operator"], "members": {"Name": {"shape": "TaskFilterName", "documentation": "<p>The name of the filter being used. Each API call supports a list of filters that are available for it. For example, <code>LocationId</code> for <code>ListTasks</code>.</p>"}, "Values": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The values that you want to filter for. For example, you might want to display only tasks for a specific destination location.</p>"}, "Operator": {"shape": "Operator", "documentation": "<p>The operator that is used to compare filter values (for example, <code>Equals</code> or <code>Contains</code>).</p>"}}, "documentation": "<p>You can use API filters to narrow down the list of resources returned by <code>ListTasks</code>. For example, to retrieve all tasks on a source location, you can use <code>ListTasks</code> with filter name <code>LocationId</code> and <code>Operator Equals</code> with the ARN for the location.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/query-resources.html\">filtering DataSync resources</a>.</p>"}, "TaskFilterName": {"type": "string", "enum": ["LocationId", "CreationTime"]}, "TaskFilters": {"type": "list", "member": {"shape": "TaskFilter"}}, "TaskList": {"type": "list", "member": {"shape": "TaskListEntry"}}, "TaskListEntry": {"type": "structure", "members": {"TaskArn": {"shape": "TaskArn", "documentation": "<p>The Amazon Resource Name (ARN) of the task.</p>"}, "Status": {"shape": "TaskStatus", "documentation": "<p>The status of the task.</p>"}, "Name": {"shape": "TagValue", "documentation": "<p>The name of the task.</p>"}, "TaskMode": {"shape": "TaskMode", "documentation": "<p>The task mode that you're using. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/choosing-task-mode.html\">Choosing a task mode for your data transfer</a>.</p>"}}, "documentation": "<p>Represents a single entry in a list of tasks. <code>TaskListEntry</code> returns an array that contains a list of tasks when the <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/API_ListTasks.html\">ListTasks</a> operation is called. A task includes the source and destination file systems to sync and the options to use for the tasks.</p>"}, "TaskMode": {"type": "string", "enum": ["BASIC", "ENHANCED"]}, "TaskQueueing": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "TaskReportConfig": {"type": "structure", "members": {"Destination": {"shape": "ReportDestination", "documentation": "<p>Specifies the Amazon S3 bucket where DataSync uploads your task report. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/task-reports.html#task-report-access\">Task reports</a>.</p>"}, "OutputType": {"shape": "ReportOutputType", "documentation": "<p>Specifies the type of task report that you want:</p> <ul> <li> <p> <code>SUMMARY_ONLY</code>: Provides necessary details about your task, including the number of files, objects, and directories transferred and transfer duration.</p> </li> <li> <p> <code>STANDARD</code>: Provides complete details about your task, including a full list of files, objects, and directories that were transferred, skipped, verified, and more.</p> </li> </ul>"}, "ReportLevel": {"shape": "ReportLevel", "documentation": "<p>Specifies whether you want your task report to include only what went wrong with your transfer or a list of what succeeded and didn't.</p> <ul> <li> <p> <code>ERRORS_ONLY</code>: A report shows what DataSync was unable to transfer, skip, verify, and delete.</p> </li> <li> <p> <code>SUCCESSES_AND_ERRORS</code>: A report shows what Data<PERSON><PERSON> was able and unable to transfer, skip, verify, and delete.</p> </li> </ul>"}, "ObjectVersionIds": {"shape": "ObjectVersionIds", "documentation": "<p>Specifies whether your task report includes the new version of each object transferred into an S3 bucket. This only applies if you <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/manage-versioning-examples.html\">enable versioning on your bucket</a>. Keep in mind that setting this to <code>INCLUDE</code> can increase the duration of your task execution.</p>"}, "Overrides": {"shape": "ReportOverrides", "documentation": "<p>Customizes the reporting level for aspects of your task report. For example, your report might generally only include errors, but you could specify that you want a list of successes and errors just for the files that DataSync attempted to delete in your destination location.</p>"}}, "documentation": "<p>Specifies how you want to configure a task report, which provides detailed information about for your DataSync transfer.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/task-reports.html\">Task reports</a>.</p>"}, "TaskSchedule": {"type": "structure", "required": ["ScheduleExpression"], "members": {"ScheduleExpression": {"shape": "ScheduleExpressionCron", "documentation": "<p>Specifies your task schedule by using a cron or rate expression.</p> <p>Use cron expressions for task schedules that run on a specific time and day. For example, the following cron expression creates a task schedule that runs at 8 AM on the first Wednesday of every month:</p> <p> <code>cron(0 8 * * 3#1)</code> </p> <p>Use rate expressions for task schedules that run on a regular interval. For example, the following rate expression creates a task schedule that runs every 12 hours:</p> <p> <code>rate(12 hours)</code> </p> <p>For information about cron and rate expression syntax, see the <a href=\"https://docs.aws.amazon.com/eventbridge/latest/userguide/eb-scheduled-rule-pattern.html\"> <i>Amazon EventBridge User Guide</i> </a>.</p>"}, "Status": {"shape": "ScheduleStatus", "documentation": "<p>Specifies whether to enable or disable your task schedule. Your schedule is enabled by default, but there can be situations where you need to disable it. For example, you might need to pause a recurring transfer to fix an issue with your task or perform maintenance on your storage system.</p> <p>DataSync might disable your schedule automatically if your task fails repeatedly with the same error. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/API_TaskScheduleDetails.html\">TaskScheduleDetails</a>.</p>"}}, "documentation": "<p>Configures your DataSync task to run on a <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/task-scheduling.html\">schedule</a> (at a minimum interval of 1 hour).</p>"}, "TaskScheduleDetails": {"type": "structure", "members": {"StatusUpdateTime": {"shape": "Time", "documentation": "<p>Indicates the last time the status of your task schedule changed. For example, if DataSync automatically disables your schedule because of a repeated error, you can see when the schedule was disabled.</p>"}, "DisabledReason": {"shape": "ScheduleDisabledReason", "documentation": "<p>Provides a reason if the task schedule is disabled.</p> <p>If your schedule is disabled by <code>USER</code>, you see a <code>Manually disabled by user.</code> message.</p> <p>If your schedule is disabled by <code>SERVICE</code>, you see an error message to help you understand why the task keeps failing. For information on resolving DataSync errors, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/troubleshooting-datasync-locations-tasks.html\">Troubleshooting issues with DataSync transfers</a>.</p>"}, "DisabledBy": {"shape": "ScheduleDisabledBy", "documentation": "<p>Indicates how your task schedule was disabled.</p> <ul> <li> <p> <code>USER</code> - Your schedule was manually disabled by using the <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/API_UpdateTask.html\">UpdateTask</a> operation or DataSync console.</p> </li> <li> <p> <code>SERVICE</code> - Your schedule was automatically disabled by DataSync because the task failed repeatedly with the same error.</p> </li> </ul>"}}, "documentation": "<p>Provides information about your DataSync <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/task-scheduling.html\">task schedule</a>.</p>"}, "TaskStatus": {"type": "string", "enum": ["AVAILABLE", "CREATING", "QUEUED", "RUNNING", "UNAVAILABLE"]}, "Time": {"type": "timestamp"}, "TransferMode": {"type": "string", "enum": ["CHANGED", "ALL"]}, "Uid": {"type": "string", "enum": ["NONE", "INT_VALUE", "NAME", "BOTH"]}, "UntagResourceRequest": {"type": "structure", "required": ["ResourceArn", "Keys"], "members": {"ResourceArn": {"shape": "TaggableResourceArn", "documentation": "<p>Specifies the Amazon Resource Name (ARN) of the resource to remove the tags from.</p>"}, "Keys": {"shape": "TagKeyList", "documentation": "<p>Specifies the keys in the tags that you want to remove.</p>"}}, "documentation": "<p>UntagResourceRequest</p>"}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateAgentRequest": {"type": "structure", "required": ["AgentArn"], "members": {"AgentArn": {"shape": "AgentArn", "documentation": "<p>The Amazon Resource Name (ARN) of the agent to update.</p>"}, "Name": {"shape": "TagValue", "documentation": "<p>The name that you want to use to configure the agent.</p>"}}, "documentation": "<p>UpdateAgentRequest</p>"}, "UpdateAgentResponse": {"type": "structure", "members": {}}, "UpdateLocationAzureBlobRequest": {"type": "structure", "required": ["LocationArn"], "members": {"LocationArn": {"shape": "LocationArn", "documentation": "<p>Specifies the ARN of the Azure Blob Storage transfer location that you're updating.</p>"}, "Subdirectory": {"shape": "AzureBlobSubdirectory", "documentation": "<p>Specifies path segments if you want to limit your transfer to a virtual directory in your container (for example, <code>/my/images</code>).</p>"}, "AuthenticationType": {"shape": "AzureBlobAuthenticationType", "documentation": "<p>Specifies the authentication method DataSync uses to access your Azure Blob Storage. DataSync can access blob storage using a shared access signature (SAS).</p>"}, "SasConfiguration": {"shape": "AzureBlobSasConfiguration", "documentation": "<p>Specifies the SAS configuration that allows DataSync to access your Azure Blob Storage.</p>"}, "BlobType": {"shape": "AzureBlobType", "documentation": "<p>Specifies the type of blob that you want your objects or files to be when transferring them into Azure Blob Storage. Currently, DataSync only supports moving data into Azure Blob Storage as block blobs. For more information on blob types, see the <a href=\"https://learn.microsoft.com/en-us/rest/api/storageservices/understanding-block-blobs--append-blobs--and-page-blobs\">Azure Blob Storage documentation</a>.</p>"}, "AccessTier": {"shape": "AzureAccessTier", "documentation": "<p>Specifies the access tier that you want your objects or files transferred into. This only applies when using the location as a transfer destination. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/creating-azure-blob-location.html#azure-blob-access-tiers\">Access tiers</a>.</p>"}, "AgentArns": {"shape": "AgentArnList", "documentation": "<p>(Optional) Specifies the Amazon Resource Name (ARN) of the DataSync agent that can connect with your Azure Blob Storage container. If you are setting up an agentless cross-cloud transfer, you do not need to specify a value for this parameter.</p> <p>You can specify more than one agent. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/multiple-agents.html\">Using multiple agents for your transfer</a>.</p> <note> <p>You cannot add or remove agents from a storage location after you initially create it.</p> </note>"}, "CmkSecretConfig": {"shape": "CmkSecretConfig", "documentation": "<p>Specifies configuration information for a DataSync-managed secret, such as an authentication token or set of credentials that DataSync uses to access a specific transfer location, and a customer-managed KMS key.</p>"}, "CustomSecretConfig": {"shape": "CustomSecretConfig", "documentation": "<p>Specifies configuration information for a customer-managed secret, such as an authentication token or set of credentials that DataSync uses to access a specific transfer location, and a customer-managed KMS key.</p>"}}}, "UpdateLocationAzureBlobResponse": {"type": "structure", "members": {}}, "UpdateLocationEfsRequest": {"type": "structure", "required": ["LocationArn"], "members": {"LocationArn": {"shape": "LocationArn", "documentation": "<p>Specifies the Amazon Resource Name (ARN) of the Amazon EFS transfer location that you're updating.</p>"}, "Subdirectory": {"shape": "EfsSubdirectory", "documentation": "<p>Specifies a mount path for your Amazon EFS file system. This is where DataSync reads or writes data on your file system (depending on if this is a source or destination location).</p> <p>By default, DataSync uses the root directory (or <a href=\"https://docs.aws.amazon.com/efs/latest/ug/efs-access-points.html\">access point</a> if you provide one by using <code>AccessPointArn</code>). You can also include subdirectories using forward slashes (for example, <code>/path/to/folder</code>).</p>"}, "AccessPointArn": {"shape": "UpdatedEfsAccessPointArn", "documentation": "<p>Specifies the Amazon Resource Name (ARN) of the access point that DataSync uses to mount your Amazon EFS file system.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-efs-location.html#create-efs-location-iam\">Accessing restricted Amazon EFS file systems</a>.</p>"}, "FileSystemAccessRoleArn": {"shape": "UpdatedEfsIamRoleArn", "documentation": "<p>Specifies an Identity and Access Management (IAM) role that allows DataSync to access your Amazon EFS file system.</p> <p>For information on creating this role, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-efs-location.html#create-efs-location-iam-role\">Creating a DataSync IAM role for Amazon EFS file system access</a>.</p>"}, "InTransitEncryption": {"shape": "EfsInTransitEncryption", "documentation": "<p>Specifies whether you want DataSync to use Transport Layer Security (TLS) 1.2 encryption when it transfers data to or from your Amazon EFS file system.</p> <p>If you specify an access point using <code>AccessPointArn</code> or an IAM role using <code>FileSystemAccessRoleArn</code>, you must set this parameter to <code>TLS1_2</code>.</p>"}}}, "UpdateLocationEfsResponse": {"type": "structure", "members": {}}, "UpdateLocationFsxLustreRequest": {"type": "structure", "required": ["LocationArn"], "members": {"LocationArn": {"shape": "LocationArn", "documentation": "<p>Specifies the Amazon Resource Name (ARN) of the FSx for Lustre transfer location that you're updating.</p>"}, "Subdirectory": {"shape": "SmbSubdirectory", "documentation": "<p>Specifies a mount path for your FSx for Lustre file system. The path can include subdirectories.</p> <p>When the location is used as a source, DataSync reads data from the mount path. When the location is used as a destination, DataSync writes data to the mount path. If you don't include this parameter, DataSync uses the file system's root directory (<code>/</code>).</p>"}}}, "UpdateLocationFsxLustreResponse": {"type": "structure", "members": {}}, "UpdateLocationFsxOntapRequest": {"type": "structure", "required": ["LocationArn"], "members": {"LocationArn": {"shape": "LocationArn", "documentation": "<p>Specifies the Amazon Resource Name (ARN) of the FSx for ONTAP transfer location that you're updating.</p>"}, "Protocol": {"shape": "FsxUpdateProtocol", "documentation": "<p>Specifies the data transfer protocol that DataSync uses to access your Amazon FSx file system.</p>"}, "Subdirectory": {"shape": "FsxOntapSubdirectory", "documentation": "<p>Specifies a path to the file share in the storage virtual machine (SVM) where you want to transfer data to or from.</p> <p>You can specify a junction path (also known as a mount point), qtree path (for NFS file shares), or share name (for SMB file shares). For example, your mount path might be <code>/vol1</code>, <code>/vol1/tree1</code>, or <code>/share1</code>.</p> <note> <p>Don't specify a junction path in the SVM's root volume. For more information, see <a href=\"https://docs.aws.amazon.com/fsx/latest/ONTAPGuide/managing-svms.html\">Managing FSx for ONTAP storage virtual machines</a> in the <i>Amazon FSx for NetApp ONTAP User Guide</i>.</p> </note>"}}}, "UpdateLocationFsxOntapResponse": {"type": "structure", "members": {}}, "UpdateLocationFsxOpenZfsRequest": {"type": "structure", "required": ["LocationArn"], "members": {"LocationArn": {"shape": "LocationArn", "documentation": "<p>Specifies the Amazon Resource Name (ARN) of the FSx for OpenZFS transfer location that you're updating.</p>"}, "Protocol": {"shape": "FsxProtocol"}, "Subdirectory": {"shape": "SmbSubdirectory", "documentation": "<p>Specifies a subdirectory in the location's path that must begin with <code>/fsx</code>. DataSync uses this subdirectory to read or write data (depending on whether the file system is a source or destination location).</p>"}}}, "UpdateLocationFsxOpenZfsResponse": {"type": "structure", "members": {}}, "UpdateLocationFsxWindowsRequest": {"type": "structure", "required": ["LocationArn"], "members": {"LocationArn": {"shape": "LocationArn", "documentation": "<p>Specifies the ARN of the FSx for Windows File Server transfer location that you're updating.</p>"}, "Subdirectory": {"shape": "FsxWindowsSubdirectory", "documentation": "<p>Specifies a mount path for your file system using forward slashes. DataSync uses this subdirectory to read or write data (depending on whether the file system is a source or destination location).</p>"}, "Domain": {"shape": "UpdateSmbDomain", "documentation": "<p>Specifies the name of the Windows domain that your FSx for Windows File Server file system belongs to.</p> <p>If you have multiple Active Directory domains in your environment, configuring this parameter makes sure that DataSync connects to the right file system.</p>"}, "User": {"shape": "SmbUser", "documentation": "<p>Specifies the user with the permissions to mount and access the files, folders, and file metadata in your FSx for Windows File Server file system.</p> <p>For information about choosing a user with the right level of access for your transfer, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-fsx-location.html#create-fsx-windows-location-permissions\">required permissions</a> for FSx for Windows File Server locations.</p>"}, "Password": {"shape": "SmbPassword", "documentation": "<p>Specifies the password of the user with the permissions to mount and access the files, folders, and file metadata in your FSx for Windows File Server file system.</p>"}}}, "UpdateLocationFsxWindowsResponse": {"type": "structure", "members": {}}, "UpdateLocationHdfsRequest": {"type": "structure", "required": ["LocationArn"], "members": {"LocationArn": {"shape": "LocationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the source HDFS cluster location.</p>"}, "Subdirectory": {"shape": "HdfsSubdirectory", "documentation": "<p>A subdirectory in the HDFS cluster. This subdirectory is used to read data from or write data to the HDFS cluster.</p>"}, "NameNodes": {"shape": "HdfsNameNodeList", "documentation": "<p>The NameNode that manages the HDFS namespace. The NameNode performs operations such as opening, closing, and renaming files and directories. The NameNode contains the information to map blocks of data to the DataNodes. You can use only one NameNode.</p>"}, "BlockSize": {"shape": "HdfsBlockSize", "documentation": "<p>The size of the data blocks to write into the HDFS cluster. </p>"}, "ReplicationFactor": {"shape": "HdfsReplicationFactor", "documentation": "<p>The number of DataNodes to replicate the data to when writing to the HDFS cluster. </p>"}, "KmsKeyProviderUri": {"shape": "KmsKeyProviderUri", "documentation": "<p>The URI of the HDFS cluster's Key Management Server (KMS). </p>"}, "QopConfiguration": {"shape": "QopConfiguration", "documentation": "<p>The Quality of Protection (QOP) configuration specifies the Remote Procedure Call (RPC) and data transfer privacy settings configured on the Hadoop Distributed File System (HDFS) cluster. </p>"}, "AuthenticationType": {"shape": "HdfsAuthenticationType", "documentation": "<p>The type of authentication used to determine the identity of the user. </p>"}, "SimpleUser": {"shape": "HdfsUser", "documentation": "<p>The user name used to identify the client on the host operating system.</p>"}, "KerberosPrincipal": {"shape": "KerberosPrincipal", "documentation": "<p>The Kerberos principal with access to the files and folders on the HDFS cluster. </p>"}, "KerberosKeytab": {"shape": "KerberosKeytabFile", "documentation": "<p>The Kerberos key table (keytab) that contains mappings between the defined Kerberos principal and the encrypted keys. You can load the keytab from a file by providing the file's address.</p>"}, "KerberosKrb5Conf": {"shape": "KerberosKrb5ConfFile", "documentation": "<p>The <code>krb5.conf</code> file that contains the Kerberos configuration information. You can load the <code>krb5.conf</code> file by providing the file's address. If you're using the CLI, it performs the base64 encoding for you. Otherwise, provide the base64-encoded text.</p>"}, "AgentArns": {"shape": "AgentArnList", "documentation": "<p>The Amazon Resource Names (ARNs) of the DataSync agents that can connect to your HDFS cluster.</p>"}}}, "UpdateLocationHdfsResponse": {"type": "structure", "members": {}}, "UpdateLocationNfsRequest": {"type": "structure", "required": ["LocationArn"], "members": {"LocationArn": {"shape": "LocationArn", "documentation": "<p>Specifies the Amazon Resource Name (ARN) of the NFS transfer location that you want to update.</p>"}, "Subdirectory": {"shape": "NfsSubdirectory", "documentation": "<p>Specifies the export path in your NFS file server that you want DataSync to mount.</p> <p>This path (or a subdirectory of the path) is where DataSync transfers data to or from. For information on configuring an export for DataSync, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-nfs-location.html#accessing-nfs\">Accessing NFS file servers</a>.</p>"}, "ServerHostname": {"shape": "ServerHostname", "documentation": "<p>Specifies the DNS name or IP version 4 (IPv4) address of the NFS file server that your DataSync agent connects to.</p>"}, "OnPremConfig": {"shape": "OnPremConfig"}, "MountOptions": {"shape": "NfsMountOptions"}}}, "UpdateLocationNfsResponse": {"type": "structure", "members": {}}, "UpdateLocationObjectStorageRequest": {"type": "structure", "required": ["LocationArn"], "members": {"LocationArn": {"shape": "LocationArn", "documentation": "<p>Specifies the ARN of the object storage system location that you're updating.</p>"}, "ServerPort": {"shape": "ObjectStorageServerPort", "documentation": "<p>Specifies the port that your object storage server accepts inbound network traffic on (for example, port 443).</p>"}, "ServerProtocol": {"shape": "ObjectStorageServerProtocol", "documentation": "<p>Specifies the protocol that your object storage server uses to communicate.</p>"}, "Subdirectory": {"shape": "S3Subdirectory", "documentation": "<p>Specifies the object prefix for your object storage server. If this is a source location, DataSync only copies objects with this prefix. If this is a destination location, DataSync writes all objects with this prefix.</p>"}, "ServerHostname": {"shape": "ServerHostname", "documentation": "<p>Specifies the domain name or IP version 4 (IPv4) address of the object storage server that your DataSync agent connects to.</p>"}, "AccessKey": {"shape": "ObjectStorageAccessKey", "documentation": "<p>Specifies the access key (for example, a user name) if credentials are required to authenticate with the object storage server.</p>"}, "SecretKey": {"shape": "ObjectStorageSecretKey", "documentation": "<p>Specifies the secret key (for example, a password) if credentials are required to authenticate with the object storage server.</p>"}, "AgentArns": {"shape": "AgentArnList", "documentation": "<p>(Optional) Specifies the Amazon Resource Names (ARNs) of the DataSync agents that can connect with your object storage system. If you are setting up an agentless cross-cloud transfer, you do not need to specify a value for this parameter.</p> <note> <p>You cannot add or remove agents from a storage location after you initially create it.</p> </note>"}, "ServerCertificate": {"shape": "ObjectStorageCertificate", "documentation": "<p>Specifies a certificate chain for DataSync to authenticate with your object storage system if the system uses a private or self-signed certificate authority (CA). You must specify a single <code>.pem</code> file with a full certificate chain (for example, <code>file:///home/<USER>/.ssh/object_storage_certificates.pem</code>).</p> <p>The certificate chain might include:</p> <ul> <li> <p>The object storage system's certificate</p> </li> <li> <p>All intermediate certificates (if there are any)</p> </li> <li> <p>The root certificate of the signing CA</p> </li> </ul> <p>You can concatenate your certificates into a <code>.pem</code> file (which can be up to 32768 bytes before base64 encoding). The following example <code>cat</code> command creates an <code>object_storage_certificates.pem</code> file that includes three certificates:</p> <p> <code>cat object_server_certificate.pem intermediate_certificate.pem ca_root_certificate.pem &gt; object_storage_certificates.pem</code> </p> <p>To use this parameter, configure <code>ServerProtocol</code> to <code>HTTPS</code>.</p> <p>Updating this parameter doesn't interfere with tasks that you have in progress.</p>"}, "CmkSecretConfig": {"shape": "CmkSecretConfig", "documentation": "<p>Specifies configuration information for a DataSync-managed secret, such as an authentication token or set of credentials that DataSync uses to access a specific transfer location, and a customer-managed KMS key.</p>"}, "CustomSecretConfig": {"shape": "CustomSecretConfig", "documentation": "<p>Specifies configuration information for a customer-managed secret, such as an authentication token or set of credentials that DataSync uses to access a specific transfer location, and a customer-managed KMS key.</p>"}}}, "UpdateLocationObjectStorageResponse": {"type": "structure", "members": {}}, "UpdateLocationS3Request": {"type": "structure", "required": ["LocationArn"], "members": {"LocationArn": {"shape": "LocationArn", "documentation": "<p>Specifies the Amazon Resource Name (ARN) of the Amazon S3 transfer location that you're updating.</p>"}, "Subdirectory": {"shape": "S3Subdirectory", "documentation": "<p>Specifies a prefix in the S3 bucket that DataS<PERSON> reads from or writes to (depending on whether the bucket is a source or destination location).</p> <note> <p>DataSync can't transfer objects with a prefix that begins with a slash (<code>/</code>) or includes <code>//</code>, <code>/./</code>, or <code>/../</code> patterns. For example:</p> <ul> <li> <p> <code>/photos</code> </p> </li> <li> <p> <code>photos//2006/January</code> </p> </li> <li> <p> <code>photos/./2006/February</code> </p> </li> <li> <p> <code>photos/../2006/March</code> </p> </li> </ul> </note>"}, "S3StorageClass": {"shape": "S3StorageClass", "documentation": "<p>Specifies the storage class that you want your objects to use when Amazon S3 is a transfer destination.</p> <p>For buckets in Amazon Web Services Regions, the storage class defaults to <code>STANDARD</code>. For buckets on Outposts, the storage class defaults to <code>OUTPOSTS</code>.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-s3-location.html#using-storage-classes\">Storage class considerations with Amazon S3 transfers</a>.</p>"}, "S3Config": {"shape": "S3Config"}}}, "UpdateLocationS3Response": {"type": "structure", "members": {}}, "UpdateLocationSmbRequest": {"type": "structure", "required": ["LocationArn"], "members": {"LocationArn": {"shape": "LocationArn", "documentation": "<p>Specifies the ARN of the SMB location that you want to update.</p>"}, "Subdirectory": {"shape": "SmbSubdirectory", "documentation": "<p>Specifies the name of the share exported by your SMB file server where DataSync will read or write data. You can include a subdirectory in the share path (for example, <code>/path/to/subdirectory</code>). Make sure that other SMB clients in your network can also mount this path.</p> <p>To copy all data in the specified subdirectory, DataSync must be able to mount the SMB share and access all of its data. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-smb-location.html#configuring-smb-permissions\">Providing DataSync access to SMB file servers</a>.</p>"}, "ServerHostname": {"shape": "ServerHostname", "documentation": "<p>Specifies the domain name or IP address of the SMB file server that your DataSync agent connects to.</p> <p>Remember the following when configuring this parameter:</p> <ul> <li> <p>You can't specify an IP version 6 (IPv6) address.</p> </li> <li> <p>If you're using Kerberos authentication, you must specify a domain name.</p> </li> </ul>"}, "User": {"shape": "SmbUser", "documentation": "<p>Specifies the user name that can mount your SMB file server and has permission to access the files and folders involved in your transfer. This parameter applies only if <code>AuthenticationType</code> is set to <code>NTLM</code>.</p> <p>For information about choosing a user with the right level of access for your transfer, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-smb-location.html#configuring-smb-permissions\">Providing DataSync access to SMB file servers</a>.</p>"}, "Domain": {"shape": "SmbDomain", "documentation": "<p>Specifies the Windows domain name that your SMB file server belongs to. This parameter applies only if <code>AuthenticationType</code> is set to <code>NTLM</code>.</p> <p>If you have multiple domains in your environment, configuring this parameter makes sure that DataSync connects to the right file server.</p>"}, "Password": {"shape": "SmbPassword", "documentation": "<p>Specifies the password of the user who can mount your SMB file server and has permission to access the files and folders involved in your transfer. This parameter applies only if <code>AuthenticationType</code> is set to <code>NTLM</code>.</p>"}, "AgentArns": {"shape": "AgentArnList", "documentation": "<p>Specifies the DataSync agent (or agents) that can connect to your SMB file server. You specify an agent by using its Amazon Resource Name (ARN).</p>"}, "MountOptions": {"shape": "SmbMountOptions"}, "AuthenticationType": {"shape": "SmbAuthenticationType", "documentation": "<p>Specifies the authentication protocol that DataSync uses to connect to your SMB file server. DataSync supports <code>NTLM</code> (default) and <code>KERBEROS</code> authentication.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/create-smb-location.html#configuring-smb-permissions\">Providing DataSync access to SMB file servers</a>.</p>"}, "DnsIpAddresses": {"shape": "DnsIpList", "documentation": "<p>Specifies the IPv4 addresses for the DNS servers that your SMB file server belongs to. This parameter applies only if <code>AuthenticationType</code> is set to <code>KERBEROS</code>.</p> <p>If you have multiple domains in your environment, configuring this parameter makes sure that DataSync connects to the right SMB file server. </p>"}, "KerberosPrincipal": {"shape": "KerberosPrincipal", "documentation": "<p>Specifies a Kerberos prinicpal, which is an identity in your Kerberos realm that has permission to access the files, folders, and file metadata in your SMB file server.</p> <p>A Kerberos principal might look like <code>HOST/<EMAIL></code>.</p> <p>Principal names are case sensitive. Your DataSync task execution will fail if the principal that you specify for this parameter doesn’t exactly match the principal that you use to create the keytab file.</p>"}, "KerberosKeytab": {"shape": "KerberosKeytabFile", "documentation": "<p>Specifies your Kerberos key table (keytab) file, which includes mappings between your Kerberos principal and encryption keys.</p> <p>To avoid task execution errors, make sure that the Kerberos principal that you use to create the keytab file matches exactly what you specify for <code>KerberosPrincipal</code>.</p>"}, "KerberosKrb5Conf": {"shape": "KerberosKrb5ConfFile", "documentation": "<p>Specifies a Kerberos configuration file (<code>krb5.conf</code>) that defines your Kerberos realm configuration.</p> <p>The file must be base64 encoded. If you're using the CLI, the encoding is done for you.</p>"}}}, "UpdateLocationSmbResponse": {"type": "structure", "members": {}}, "UpdateSmbDomain": {"type": "string", "max": 253, "pattern": "^([A-Za-z0-9]((\\.|-+)?[A-Za-z0-9]){0,252})?$"}, "UpdateTaskExecutionRequest": {"type": "structure", "required": ["TaskExecutionArn", "Options"], "members": {"TaskExecutionArn": {"shape": "TaskExecutionArn", "documentation": "<p>Specifies the Amazon Resource Name (ARN) of the task execution that you're updating.</p>"}, "Options": {"shape": "Options"}}}, "UpdateTaskExecutionResponse": {"type": "structure", "members": {}}, "UpdateTaskRequest": {"type": "structure", "required": ["TaskArn"], "members": {"TaskArn": {"shape": "TaskArn", "documentation": "<p>Specifies the ARN of the task that you want to update.</p>"}, "Options": {"shape": "Options"}, "Excludes": {"shape": "FilterList", "documentation": "<p>Specifies exclude filters that define the files, objects, and folders in your source location that you don't want DataSync to transfer. For more information and examples, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/filtering.html\">Specifying what DataSync transfers by using filters</a>.</p>"}, "Schedule": {"shape": "TaskSchedule", "documentation": "<p>Specifies a schedule for when you want your task to run. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/task-scheduling.html\">Scheduling your task</a>.</p>"}, "Name": {"shape": "TagValue", "documentation": "<p>Specifies the name of your task.</p>"}, "CloudWatchLogGroupArn": {"shape": "LogGroupArn", "documentation": "<p>Specifies the Amazon Resource Name (ARN) of an Amazon CloudWatch log group for monitoring your task.</p> <p>For Enhanced mode tasks, you must use <code>/aws/datasync</code> as your log group name. For example:</p> <p> <code>arn:aws:logs:us-east-1:111222333444:log-group:/aws/datasync:*</code> </p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/configure-logging.html\">Monitoring data transfers with CloudWatch Logs</a>.</p>"}, "Includes": {"shape": "FilterList", "documentation": "<p>Specifies include filters define the files, objects, and folders in your source location that you want DataSync to transfer. For more information and examples, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/filtering.html\">Specifying what DataSync transfers by using filters</a>.</p>"}, "ManifestConfig": {"shape": "ManifestConfig", "documentation": "<p>Configures a manifest, which is a list of files or objects that you want DataSync to transfer. For more information and configuration examples, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/transferring-with-manifest.html\">Specifying what DataSync transfers by using a manifest</a>.</p> <p>When using this parameter, your caller identity (the IAM role that you're using DataSync with) must have the <code>iam:PassRole</code> permission. The <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/security-iam-awsmanpol.html#security-iam-awsmanpol-awsdatasyncfullaccess\">AWSDataSyncFullAccess</a> policy includes this permission.</p> <p>To remove a manifest configuration, specify this parameter as empty.</p>"}, "TaskReportConfig": {"shape": "TaskReportConfig", "documentation": "<p>Specifies how you want to configure a task report, which provides detailed information about your DataSync transfer. For more information, see <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/task-reports.html\">Monitoring your DataSync transfers with task reports</a>.</p> <p>When using this parameter, your caller identity (the IAM role that you're using DataSync with) must have the <code>iam:PassRole</code> permission. The <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/security-iam-awsmanpol.html#security-iam-awsmanpol-awsdatasyncfullaccess\">AWSDataSyncFullAccess</a> policy includes this permission.</p> <p>To remove a task report configuration, specify this parameter as empty.</p>"}}, "documentation": "<p>UpdateTaskResponse</p>"}, "UpdateTaskResponse": {"type": "structure", "members": {}}, "UpdatedEfsAccessPointArn": {"type": "string", "max": 128, "pattern": "(^arn:(aws|aws-cn|aws-us-gov|aws-iso|aws-iso-b):elasticfilesystem:[a-z\\-0-9]+:[0-9]{12}:access-point/fsap-[0-9a-f]{8,40}$)|(^$)"}, "UpdatedEfsIamRoleArn": {"type": "string", "max": 2048, "pattern": "(^arn:(aws|aws-cn|aws-us-gov|aws-iso|aws-iso-b):iam::[0-9]{12}:role/.*$)|(^$)"}, "VerifyMode": {"type": "string", "enum": ["POINT_IN_TIME_CONSISTENT", "ONLY_FILES_TRANSFERRED", "NONE"]}, "VpcEndpointId": {"type": "string", "pattern": "^vpce-[0-9a-f]{17}$"}, "long": {"type": "long"}, "string": {"type": "string"}}, "documentation": "<fullname>DataSync</fullname> <p>DataSync is an online data movement service that simplifies data migration and helps you quickly, easily, and securely transfer your file or object data to, from, and between Amazon Web Services storage services.</p> <p>This API interface reference includes documentation for using DataSync programmatically. For complete information, see the <i> <a href=\"https://docs.aws.amazon.com/datasync/latest/userguide/what-is-datasync.html\">DataSync User Guide</a> </i>.</p>"}