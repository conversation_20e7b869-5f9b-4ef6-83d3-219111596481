{"version": "2.0", "metadata": {"apiVersion": "2017-11-01", "endpointPrefix": "eks", "jsonVersion": "1.1", "protocol": "rest-json", "protocols": ["rest-json"], "serviceAbbreviation": "Amazon EKS", "serviceFullName": "Amazon Elastic Kubernetes Service", "serviceId": "EKS", "signatureVersion": "v4", "signingName": "eks", "uid": "eks-2017-11-01", "auth": ["aws.auth#sigv4"]}, "operations": {"AssociateAccessPolicy": {"name": "AssociateAccessPolicy", "http": {"method": "POST", "requestUri": "/clusters/{name}/access-entries/{principalArn}/access-policies"}, "input": {"shape": "AssociateAccessPolicyRequest"}, "output": {"shape": "AssociateAccessPolicyResponse"}, "errors": [{"shape": "ServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidRequestException"}, {"shape": "InvalidParameterException"}], "documentation": "<p>Associates an access policy and its scope to an access entry. For more information about associating access policies, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/access-policies.html\">Associating and disassociating access policies to and from access entries</a> in the <i>Amazon EKS User Guide</i>.</p>"}, "AssociateEncryptionConfig": {"name": "AssociateEncryptionConfig", "http": {"method": "POST", "requestUri": "/clusters/{name}/encryption-config/associate"}, "input": {"shape": "AssociateEncryptionConfigRequest"}, "output": {"shape": "AssociateEncryptionConfigResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ClientException"}, {"shape": "ServerException"}, {"shape": "ResourceInUseException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidRequestException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Associates an encryption configuration to an existing cluster.</p> <p>Use this API to enable encryption on existing clusters that don't already have encryption enabled. This allows you to implement a defense-in-depth security strategy without migrating applications to new Amazon EKS clusters.</p>"}, "AssociateIdentityProviderConfig": {"name": "AssociateIdentityProviderConfig", "http": {"method": "POST", "requestUri": "/clusters/{name}/identity-provider-configs/associate"}, "input": {"shape": "AssociateIdentityProviderConfigRequest"}, "output": {"shape": "AssociateIdentityProviderConfigResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ClientException"}, {"shape": "ServerException"}, {"shape": "ResourceInUseException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidRequestException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Associates an identity provider configuration to a cluster.</p> <p>If you want to authenticate identities using an identity provider, you can create an identity provider configuration and associate it to your cluster. After configuring authentication to your cluster you can create Kubernetes <code>Role</code> and <code>ClusterRole</code> objects, assign permissions to them, and then bind them to the identities using Kubernetes <code>RoleBinding</code> and <code>ClusterRoleBinding</code> objects. For more information see <a href=\"https://kubernetes.io/docs/reference/access-authn-authz/rbac/\">Using RBAC Authorization</a> in the Kubernetes documentation.</p>"}, "CreateAccessEntry": {"name": "CreateAccessEntry", "http": {"method": "POST", "requestUri": "/clusters/{name}/access-entries"}, "input": {"shape": "CreateAccessEntryRequest"}, "output": {"shape": "CreateAccessEntryResponse"}, "errors": [{"shape": "ServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidRequestException"}, {"shape": "InvalidParameterException"}, {"shape": "ResourceLimitExceededException"}, {"shape": "ResourceInUseException"}], "documentation": "<p>Creates an access entry.</p> <p>An access entry allows an IAM principal to access your cluster. Access entries can replace the need to maintain entries in the <code>aws-auth</code> <code>ConfigMap</code> for authentication. You have the following options for authorizing an IAM principal to access Kubernetes objects on your cluster: Kubernetes role-based access control (RBAC), Amazon EKS, or both. Kubernetes RBAC authorization requires you to create and manage Kubernetes <code>Role</code>, <code>ClusterRole</code>, <code>RoleBinding</code>, and <code>ClusterRoleBinding</code> objects, in addition to managing access entries. If you use Amazon EKS authorization exclusively, you don't need to create and manage Kubernetes <code>Role</code>, <code>ClusterRole</code>, <code>RoleBinding</code>, and <code>ClusterRoleBinding</code> objects.</p> <p>For more information about access entries, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/access-entries.html\">Access entries</a> in the <i>Amazon EKS User Guide</i>.</p>"}, "CreateAddon": {"name": "CreateAddon", "http": {"method": "POST", "requestUri": "/clusters/{name}/addons"}, "input": {"shape": "CreateAddonRequest"}, "output": {"shape": "CreateAddonResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}, {"shape": "ClientException"}, {"shape": "ServerException"}], "documentation": "<p>Creates an Amazon EKS add-on.</p> <p>Amazon EKS add-ons help to automate the provisioning and lifecycle management of common operational software for Amazon EKS clusters. For more information, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/eks-add-ons.html\">Amazon EKS add-ons</a> in the <i>Amazon EKS User Guide</i>.</p>"}, "CreateCluster": {"name": "CreateCluster", "http": {"method": "POST", "requestUri": "/clusters"}, "input": {"shape": "CreateClusterRequest"}, "output": {"shape": "CreateClusterResponse"}, "errors": [{"shape": "ResourceInUseException"}, {"shape": "ResourceLimitExceededException"}, {"shape": "InvalidParameterException"}, {"shape": "ClientException"}, {"shape": "ServerException"}, {"shape": "ServiceUnavailableException"}, {"shape": "UnsupportedAvailabilityZoneException"}], "documentation": "<p>Creates an Amazon EKS control plane.</p> <p>The Amazon EKS control plane consists of control plane instances that run the Kubernetes software, such as <code>etcd</code> and the API server. The control plane runs in an account managed by Amazon Web Services, and the Kubernetes API is exposed by the Amazon EKS API server endpoint. Each Amazon EKS cluster control plane is single tenant and unique. It runs on its own set of Amazon EC2 instances.</p> <p>The cluster control plane is provisioned across multiple Availability Zones and fronted by an Elastic Load Balancing Network Load Balancer. Amazon EKS also provisions elastic network interfaces in your VPC subnets to provide connectivity from the control plane instances to the nodes (for example, to support <code>kubectl exec</code>, <code>logs</code>, and <code>proxy</code> data flows).</p> <p>Amazon EKS nodes run in your Amazon Web Services account and connect to your cluster's control plane over the Kubernetes API server endpoint and a certificate file that is created for your cluster.</p> <p>You can use the <code>endpointPublicAccess</code> and <code>endpointPrivateAccess</code> parameters to enable or disable public and private access to your cluster's Kubernetes API server endpoint. By default, public access is enabled, and private access is disabled. The endpoint domain name and IP address family depends on the value of the <code>ipFamily</code> for the cluster. For more information, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/cluster-endpoint.html\">Amazon EKS Cluster Endpoint Access Control</a> in the <i> <i>Amazon EKS User Guide</i> </i>. </p> <p>You can use the <code>logging</code> parameter to enable or disable exporting the Kubernetes control plane logs for your cluster to CloudWatch Logs. By default, cluster control plane logs aren't exported to CloudWatch Logs. For more information, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/control-plane-logs.html\">Amazon EKS Cluster Control Plane Logs</a> in the <i> <i>Amazon EKS User Guide</i> </i>.</p> <note> <p>CloudWatch Logs ingestion, archive storage, and data scanning rates apply to exported control plane logs. For more information, see <a href=\"http://aws.amazon.com/cloudwatch/pricing/\">CloudWatch Pricing</a>.</p> </note> <p>In most cases, it takes several minutes to create a cluster. After you create an Amazon EKS cluster, you must configure your Kubernetes tooling to communicate with the API server and launch nodes into your cluster. For more information, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/cluster-auth.html\">Allowing users to access your cluster</a> and <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/launch-workers.html\">Launching Amazon EKS nodes</a> in the <i>Amazon EKS User Guide</i>.</p>"}, "CreateEksAnywhereSubscription": {"name": "CreateEksAnywhereSubscription", "http": {"method": "POST", "requestUri": "/eks-anywhere-subscriptions"}, "input": {"shape": "CreateEksAnywhereSubscriptionRequest"}, "output": {"shape": "CreateEksAnywhereSubscriptionResponse"}, "errors": [{"shape": "ResourceLimitExceededException"}, {"shape": "InvalidParameterException"}, {"shape": "ClientException"}, {"shape": "ServerException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Creates an EKS Anywhere subscription. When a subscription is created, it is a contract agreement for the length of the term specified in the request. Licenses that are used to validate support are provisioned in Amazon Web Services License Manager and the caller account is granted access to EKS Anywhere Curated Packages.</p>"}, "CreateFargateProfile": {"name": "CreateFargateProfile", "http": {"method": "POST", "requestUri": "/clusters/{name}/fargate-profiles"}, "input": {"shape": "CreateFargateProfileRequest"}, "output": {"shape": "CreateFargateProfileResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "InvalidRequestException"}, {"shape": "ClientException"}, {"shape": "ServerException"}, {"shape": "ResourceLimitExceededException"}, {"shape": "UnsupportedAvailabilityZoneException"}], "documentation": "<p>Creates an Fargate profile for your Amazon EKS cluster. You must have at least one Fargate profile in a cluster to be able to run pods on Fargate.</p> <p>The Fargate profile allows an administrator to declare which pods run on Fargate and specify which pods run on which Fargate profile. This declaration is done through the profile's selectors. Each profile can have up to five selectors that contain a namespace and labels. A namespace is required for every selector. The label field consists of multiple optional key-value pairs. Pods that match the selectors are scheduled on Fargate. If a to-be-scheduled pod matches any of the selectors in the Fargate profile, then that pod is run on Fargate.</p> <p>When you create a Fargate profile, you must specify a pod execution role to use with the pods that are scheduled with the profile. This role is added to the cluster's Kubernetes <a href=\"https://kubernetes.io/docs/reference/access-authn-authz/rbac/\">Role Based Access Control</a> (RBAC) for authorization so that the <code>kubelet</code> that is running on the Fargate infrastructure can register with your Amazon EKS cluster so that it can appear in your cluster as a node. The pod execution role also provides IAM permissions to the Fargate infrastructure to allow read access to Amazon ECR image repositories. For more information, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/pod-execution-role.html\">Pod Execution Role</a> in the <i>Amazon EKS User Guide</i>.</p> <p>Fargate profiles are immutable. However, you can create a new updated profile to replace an existing profile and then delete the original after the updated profile has finished creating.</p> <p>If any Fargate profiles in a cluster are in the <code>DELETING</code> status, you must wait for that Fargate profile to finish deleting before you can create any other profiles in that cluster.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/fargate-profile.html\">Fargate profile</a> in the <i>Amazon EKS User Guide</i>.</p>"}, "CreateNodegroup": {"name": "CreateNodegroup", "http": {"method": "POST", "requestUri": "/clusters/{name}/node-groups"}, "input": {"shape": "CreateNodegroupRequest"}, "output": {"shape": "CreateNodegroupResponse"}, "errors": [{"shape": "ResourceInUseException"}, {"shape": "ResourceLimitExceededException"}, {"shape": "InvalidRequestException"}, {"shape": "InvalidParameterException"}, {"shape": "ClientException"}, {"shape": "ServerException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Creates a managed node group for an Amazon EKS cluster.</p> <p>You can only create a node group for your cluster that is equal to the current Kubernetes version for the cluster. All node groups are created with the latest AMI release version for the respective minor Kubernetes version of the cluster, unless you deploy a custom AMI using a launch template.</p> <p>For later updates, you will only be able to update a node group using a launch template only if it was originally deployed with a launch template. Additionally, the launch template ID or name must match what was used when the node group was created. You can update the launch template version with necessary changes. For more information about using launch templates, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/launch-templates.html\">Customizing managed nodes with launch templates</a>.</p> <p>An Amazon EKS managed node group is an Amazon EC2 Auto Scaling group and associated Amazon EC2 instances that are managed by Amazon Web Services for an Amazon EKS cluster. For more information, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/managed-node-groups.html\">Managed node groups</a> in the <i>Amazon EKS User Guide</i>.</p> <note> <p>Windows AMI types are only supported for commercial Amazon Web Services Regions that support Windows on Amazon EKS.</p> </note>"}, "CreatePodIdentityAssociation": {"name": "CreatePodIdentityAssociation", "http": {"method": "POST", "requestUri": "/clusters/{name}/pod-identity-associations"}, "input": {"shape": "CreatePodIdentityAssociationRequest"}, "output": {"shape": "CreatePodIdentityAssociationResponse"}, "errors": [{"shape": "ServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidRequestException"}, {"shape": "InvalidParameterException"}, {"shape": "ResourceLimitExceededException"}, {"shape": "ResourceInUseException"}], "documentation": "<p>Creates an EKS Pod Identity association between a service account in an Amazon EKS cluster and an IAM role with <i>EKS Pod Identity</i>. Use EKS Pod Identity to give temporary IAM credentials to Pods and the credentials are rotated automatically.</p> <p>Amazon EKS Pod Identity associations provide the ability to manage credentials for your applications, similar to the way that Amazon EC2 instance profiles provide credentials to Amazon EC2 instances.</p> <p>If a Pod uses a service account that has an association, Amazon EKS sets environment variables in the containers of the Pod. The environment variables configure the Amazon Web Services SDKs, including the Command Line Interface, to use the EKS Pod Identity credentials.</p> <p>EKS Pod Identity is a simpler method than <i>IAM roles for service accounts</i>, as this method doesn't use OIDC identity providers. Additionally, you can configure a role for EKS Pod Identity once, and reuse it across clusters.</p> <p>Similar to Amazon Web Services IAM behavior, EKS Pod Identity associations are eventually consistent, and may take several seconds to be effective after the initial API call returns successfully. You must design your applications to account for these potential delays. We recommend that you don’t include association create/updates in the critical, high-availability code paths of your application. Instead, make changes in a separate initialization or setup routine that you run less frequently.</p> <p>You can set a <i>target IAM role</i> in the same or a different account for advanced scenarios. With a target role, EKS Pod Identity automatically performs two role assumptions in sequence: first assuming the role in the association that is in this account, then using those credentials to assume the target IAM role. This process provides your Pod with temporary credentials that have the permissions defined in the target role, allowing secure access to resources in another Amazon Web Services account.</p>"}, "DeleteAccessEntry": {"name": "DeleteAccessEntry", "http": {"method": "DELETE", "requestUri": "/clusters/{name}/access-entries/{principalArn}"}, "input": {"shape": "DeleteAccessEntryRequest"}, "output": {"shape": "DeleteAccessEntryResponse"}, "errors": [{"shape": "ServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Deletes an access entry.</p> <p>Deleting an access entry of a type other than <code>Standard</code> can cause your cluster to function improperly. If you delete an access entry in error, you can recreate it.</p>"}, "DeleteAddon": {"name": "DeleteAddon", "http": {"method": "DELETE", "requestUri": "/clusters/{name}/addons/{addonName}"}, "input": {"shape": "DeleteAddonRequest"}, "output": {"shape": "DeleteAddonResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ClientException"}, {"shape": "ServerException"}], "documentation": "<p>Deletes an Amazon EKS add-on.</p> <p>When you remove an add-on, it's deleted from the cluster. You can always manually start an add-on on the cluster using the Kubernetes API.</p>"}, "DeleteCluster": {"name": "DeleteCluster", "http": {"method": "DELETE", "requestUri": "/clusters/{name}"}, "input": {"shape": "DeleteClusterRequest"}, "output": {"shape": "DeleteClusterResponse"}, "errors": [{"shape": "ResourceInUseException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ClientException"}, {"shape": "ServerException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Deletes an Amazon EKS cluster control plane.</p> <p>If you have active services in your cluster that are associated with a load balancer, you must delete those services before deleting the cluster so that the load balancers are deleted properly. Otherwise, you can have orphaned resources in your VPC that prevent you from being able to delete the VPC. For more information, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/delete-cluster.html\">Deleting a cluster</a> in the <i>Amazon EKS User Guide</i>.</p> <p>If you have managed node groups or Fargate profiles attached to the cluster, you must delete them first. For more information, see <code>DeleteNodgroup</code> and <code>DeleteFargateProfile</code>.</p>"}, "DeleteEksAnywhereSubscription": {"name": "DeleteEksAnywhereSubscription", "http": {"method": "DELETE", "requestUri": "/eks-anywhere-subscriptions/{id}"}, "input": {"shape": "DeleteEksAnywhereSubscriptionRequest"}, "output": {"shape": "DeleteEksAnywhereSubscriptionResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ClientException"}, {"shape": "InvalidRequestException"}, {"shape": "ServerException"}], "documentation": "<p>Deletes an expired or inactive subscription. Deleting inactive subscriptions removes them from the Amazon Web Services Management Console view and from list/describe API responses. Subscriptions can only be cancelled within 7 days of creation and are cancelled by creating a ticket in the Amazon Web Services Support Center. </p>"}, "DeleteFargateProfile": {"name": "DeleteFargateProfile", "http": {"method": "DELETE", "requestUri": "/clusters/{name}/fargate-profiles/{fargateProfileName}"}, "input": {"shape": "DeleteFargateProfileRequest"}, "output": {"shape": "DeleteFargateProfileResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ClientException"}, {"shape": "ServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Deletes an Fargate profile.</p> <p>When you delete a Fargate profile, any <code>Pod</code> running on Fargate that was created with the profile is deleted. If the <code>Pod</code> matches another Fargate profile, then it is scheduled on Fargate with that profile. If it no longer matches any Fargate profiles, then it's not scheduled on Fargate and may remain in a pending state.</p> <p>Only one Fargate profile in a cluster can be in the <code>DELETING</code> status at a time. You must wait for a Fargate profile to finish deleting before you can delete any other profiles in that cluster.</p>"}, "DeleteNodegroup": {"name": "DeleteNodegroup", "http": {"method": "DELETE", "requestUri": "/clusters/{name}/node-groups/{nodegroupName}"}, "input": {"shape": "DeleteNodegroupRequest"}, "output": {"shape": "DeleteNodegroupResponse"}, "errors": [{"shape": "ResourceInUseException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterException"}, {"shape": "ClientException"}, {"shape": "ServerException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Deletes a managed node group.</p>"}, "DeletePodIdentityAssociation": {"name": "DeletePodIdentityAssociation", "http": {"method": "DELETE", "requestUri": "/clusters/{name}/pod-identity-associations/{associationId}"}, "input": {"shape": "DeletePodIdentityAssociationRequest"}, "output": {"shape": "DeletePodIdentityAssociationResponse"}, "errors": [{"shape": "ServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidRequestException"}, {"shape": "InvalidParameterException"}], "documentation": "<p>Deletes a EKS Pod Identity association.</p> <p>The temporary Amazon Web Services credentials from the previous IAM role session might still be valid until the session expiry. If you need to immediately revoke the temporary session credentials, then go to the role in the IAM console.</p>"}, "DeregisterCluster": {"name": "DeregisterCluster", "http": {"method": "DELETE", "requestUri": "/cluster-registrations/{name}"}, "input": {"shape": "DeregisterClusterRequest"}, "output": {"shape": "DeregisterClusterResponse"}, "errors": [{"shape": "ResourceInUseException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ClientException"}, {"shape": "ServerException"}, {"shape": "ServiceUnavailableException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Deregisters a connected cluster to remove it from the Amazon EKS control plane.</p> <p>A connected cluster is a Kubernetes cluster that you've connected to your control plane using the <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/eks-connector.html\">Amazon EKS Connector</a>.</p>"}, "DescribeAccessEntry": {"name": "DescribeAccessEntry", "http": {"method": "GET", "requestUri": "/clusters/{name}/access-entries/{principalArn}"}, "input": {"shape": "DescribeAccessEntryRequest"}, "output": {"shape": "DescribeAccessEntryResponse"}, "errors": [{"shape": "ServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Describes an access entry.</p>"}, "DescribeAddon": {"name": "DescribeAddon", "http": {"method": "GET", "requestUri": "/clusters/{name}/addons/{addonName}"}, "input": {"shape": "DescribeAddonRequest"}, "output": {"shape": "DescribeAddonResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ClientException"}, {"shape": "ServerException"}], "documentation": "<p>Describes an Amazon EKS add-on.</p>"}, "DescribeAddonConfiguration": {"name": "DescribeAddonConfiguration", "http": {"method": "GET", "requestUri": "/addons/configuration-schemas"}, "input": {"shape": "DescribeAddonConfigurationRequest"}, "output": {"shape": "DescribeAddonConfigurationResponse"}, "errors": [{"shape": "ServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterException"}], "documentation": "<p>Returns configuration options.</p>"}, "DescribeAddonVersions": {"name": "DescribeAddonVersions", "http": {"method": "GET", "requestUri": "/addons/supported-versions"}, "input": {"shape": "DescribeAddonVersionsRequest"}, "output": {"shape": "DescribeAddonVersionsResponse"}, "errors": [{"shape": "ServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterException"}], "documentation": "<p>Describes the versions for an add-on.</p> <p>Information such as the Kubernetes versions that you can use the add-on with, the <code>owner</code>, <code>publisher</code>, and the <code>type</code> of the add-on are returned.</p>"}, "DescribeCluster": {"name": "DescribeCluster", "http": {"method": "GET", "requestUri": "/clusters/{name}"}, "input": {"shape": "DescribeClusterRequest"}, "output": {"shape": "DescribeClusterResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ClientException"}, {"shape": "ServerException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Describes an Amazon EKS cluster.</p> <p>The API server endpoint and certificate authority data returned by this operation are required for <code>kubelet</code> and <code>kubectl</code> to communicate with your Kubernetes API server. For more information, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/create-kubeconfig.html\">Creating or updating a <code>kubeconfig</code> file for an Amazon EKS cluster</a>.</p> <note> <p>The API server endpoint and certificate authority data aren't available until the cluster reaches the <code>ACTIVE</code> state.</p> </note>"}, "DescribeClusterVersions": {"name": "DescribeClusterVersions", "http": {"method": "GET", "requestUri": "/cluster-versions"}, "input": {"shape": "DescribeClusterVersionsRequest"}, "output": {"shape": "DescribeClusterVersionsResponse"}, "errors": [{"shape": "ServerException"}, {"shape": "InvalidParameterException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Lists available Kubernetes versions for Amazon EKS clusters.</p>"}, "DescribeEksAnywhereSubscription": {"name": "DescribeEksAnywhereSubscription", "http": {"method": "GET", "requestUri": "/eks-anywhere-subscriptions/{id}"}, "input": {"shape": "DescribeEksAnywhereSubscriptionRequest"}, "output": {"shape": "DescribeEksAnywhereSubscriptionResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ClientException"}, {"shape": "ServerException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Returns descriptive information about a subscription.</p>"}, "DescribeFargateProfile": {"name": "DescribeFargateProfile", "http": {"method": "GET", "requestUri": "/clusters/{name}/fargate-profiles/{fargateProfileName}"}, "input": {"shape": "DescribeFargateProfileRequest"}, "output": {"shape": "DescribeFargateProfileResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ClientException"}, {"shape": "ServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Describes an Fargate profile.</p>"}, "DescribeIdentityProviderConfig": {"name": "DescribeIdentityProviderConfig", "http": {"method": "POST", "requestUri": "/clusters/{name}/identity-provider-configs/describe"}, "input": {"shape": "DescribeIdentityProviderConfigRequest"}, "output": {"shape": "DescribeIdentityProviderConfigResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ClientException"}, {"shape": "ServerException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Describes an identity provider configuration.</p>"}, "DescribeInsight": {"name": "DescribeInsight", "http": {"method": "GET", "requestUri": "/clusters/{name}/insights/{id}"}, "input": {"shape": "DescribeInsightRequest"}, "output": {"shape": "DescribeInsightResponse"}, "errors": [{"shape": "ServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidRequestException"}, {"shape": "InvalidParameterException"}], "documentation": "<p>Returns details about an insight that you specify using its ID.</p>"}, "DescribeNodegroup": {"name": "DescribeNodegroup", "http": {"method": "GET", "requestUri": "/clusters/{name}/node-groups/{nodegroupName}"}, "input": {"shape": "DescribeNodegroupRequest"}, "output": {"shape": "DescribeNodegroupResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ClientException"}, {"shape": "ServerException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Describes a managed node group.</p>"}, "DescribePodIdentityAssociation": {"name": "DescribePodIdentityAssociation", "http": {"method": "GET", "requestUri": "/clusters/{name}/pod-identity-associations/{associationId}"}, "input": {"shape": "DescribePodIdentityAssociationRequest"}, "output": {"shape": "DescribePodIdentityAssociationResponse"}, "errors": [{"shape": "ServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidRequestException"}, {"shape": "InvalidParameterException"}], "documentation": "<p>Returns descriptive information about an EKS Pod Identity association.</p> <p>This action requires the ID of the association. You can get the ID from the response to the <code>CreatePodIdentityAssocation</code> for newly created associations. Or, you can list the IDs for associations with <code>ListPodIdentityAssociations</code> and filter the list by namespace or service account.</p>"}, "DescribeUpdate": {"name": "DescribeUpdate", "http": {"method": "GET", "requestUri": "/clusters/{name}/updates/{updateId}"}, "input": {"shape": "DescribeUpdateRequest"}, "output": {"shape": "DescribeUpdateResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ClientException"}, {"shape": "ServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Describes an update to an Amazon EKS resource.</p> <p>When the status of the update is <code>Successful</code>, the update is complete. If an update fails, the status is <code>Failed</code>, and an error detail explains the reason for the failure.</p>"}, "DisassociateAccessPolicy": {"name": "DisassociateAccessPolicy", "http": {"method": "DELETE", "requestUri": "/clusters/{name}/access-entries/{principalArn}/access-policies/{policyArn}"}, "input": {"shape": "DisassociateAccessPolicyRequest"}, "output": {"shape": "DisassociateAccessPolicyResponse"}, "errors": [{"shape": "ServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Disassociates an access policy from an access entry.</p>"}, "DisassociateIdentityProviderConfig": {"name": "DisassociateIdentityProviderConfig", "http": {"method": "POST", "requestUri": "/clusters/{name}/identity-provider-configs/disassociate"}, "input": {"shape": "DisassociateIdentityProviderConfigRequest"}, "output": {"shape": "DisassociateIdentityProviderConfigResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ClientException"}, {"shape": "ServerException"}, {"shape": "ResourceInUseException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidRequestException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Disassociates an identity provider configuration from a cluster.</p> <p>If you disassociate an identity provider from your cluster, users included in the provider can no longer access the cluster. However, you can still access the cluster with IAM principals.</p>"}, "ListAccessEntries": {"name": "ListAccessEntries", "http": {"method": "GET", "requestUri": "/clusters/{name}/access-entries"}, "input": {"shape": "ListAccessEntriesRequest"}, "output": {"shape": "ListAccessEntriesResponse"}, "errors": [{"shape": "ServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidRequestException"}, {"shape": "InvalidParameterException"}], "documentation": "<p>Lists the access entries for your cluster.</p>"}, "ListAccessPolicies": {"name": "ListAccessPolicies", "http": {"method": "GET", "requestUri": "/access-policies"}, "input": {"shape": "ListAccessPoliciesRequest"}, "output": {"shape": "ListAccessPoliciesResponse"}, "errors": [{"shape": "ServerException"}], "documentation": "<p>Lists the available access policies. </p>"}, "ListAddons": {"name": "ListAddons", "http": {"method": "GET", "requestUri": "/clusters/{name}/addons"}, "input": {"shape": "ListAddonsRequest"}, "output": {"shape": "ListAddonsResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "InvalidRequestException"}, {"shape": "ClientException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServerException"}], "documentation": "<p>Lists the installed add-ons.</p>"}, "ListAssociatedAccessPolicies": {"name": "ListAssociatedAccessPolicies", "http": {"method": "GET", "requestUri": "/clusters/{name}/access-entries/{principalArn}/access-policies"}, "input": {"shape": "ListAssociatedAccessPoliciesRequest"}, "output": {"shape": "ListAssociatedAccessPoliciesResponse"}, "errors": [{"shape": "ServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Lists the access policies associated with an access entry.</p>"}, "ListClusters": {"name": "ListClusters", "http": {"method": "GET", "requestUri": "/clusters"}, "input": {"shape": "ListClustersRequest"}, "output": {"shape": "ListClustersResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ClientException"}, {"shape": "ServerException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Lists the Amazon EKS clusters in your Amazon Web Services account in the specified Amazon Web Services Region.</p>"}, "ListEksAnywhereSubscriptions": {"name": "ListEksAnywhereSubscriptions", "http": {"method": "GET", "requestUri": "/eks-anywhere-subscriptions"}, "input": {"shape": "ListEksAnywhereSubscriptionsRequest"}, "output": {"shape": "ListEksAnywhereSubscriptionsResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ClientException"}, {"shape": "ServerException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Displays the full description of the subscription.</p>"}, "ListFargateProfiles": {"name": "ListFargateProfiles", "http": {"method": "GET", "requestUri": "/clusters/{name}/fargate-profiles"}, "input": {"shape": "ListFargateProfilesRequest"}, "output": {"shape": "ListFargateProfilesResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ClientException"}, {"shape": "ServerException"}], "documentation": "<p>Lists the Fargate profiles associated with the specified cluster in your Amazon Web Services account in the specified Amazon Web Services Region.</p>"}, "ListIdentityProviderConfigs": {"name": "ListIdentityProviderConfigs", "http": {"method": "GET", "requestUri": "/clusters/{name}/identity-provider-configs"}, "input": {"shape": "ListIdentityProviderConfigsRequest"}, "output": {"shape": "ListIdentityProviderConfigsResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ClientException"}, {"shape": "ServerException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Lists the identity provider configurations for your cluster.</p>"}, "ListInsights": {"name": "ListInsights", "http": {"method": "POST", "requestUri": "/clusters/{name}/insights"}, "input": {"shape": "ListInsightsRequest"}, "output": {"shape": "ListInsightsResponse"}, "errors": [{"shape": "ServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidRequestException"}, {"shape": "InvalidParameterException"}], "documentation": "<p>Returns a list of all insights checked for against the specified cluster. You can filter which insights are returned by category, associated Kubernetes version, and status. The default filter lists all categories and every status.</p> <p>The following lists the available categories:</p> <ul> <li> <p> <code>UPGRADE_READINESS</code>: Amazon EKS identifies issues that could impact your ability to upgrade to new versions of Kubernetes. These are called upgrade insights.</p> </li> <li> <p> <code>MISCONFIGURATION</code>: Amazon EKS identifies misconfiguration in your EKS Hybrid Nodes setup that could impair functionality of your cluster or workloads. These are called configuration insights.</p> </li> </ul>"}, "ListNodegroups": {"name": "ListNodegroups", "http": {"method": "GET", "requestUri": "/clusters/{name}/node-groups"}, "input": {"shape": "ListNodegroupsRequest"}, "output": {"shape": "ListNodegroupsResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ClientException"}, {"shape": "ServerException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Lists the managed node groups associated with the specified cluster in your Amazon Web Services account in the specified Amazon Web Services Region. Self-managed node groups aren't listed.</p>"}, "ListPodIdentityAssociations": {"name": "ListPodIdentityAssociations", "http": {"method": "GET", "requestUri": "/clusters/{name}/pod-identity-associations"}, "input": {"shape": "ListPodIdentityAssociationsRequest"}, "output": {"shape": "ListPodIdentityAssociationsResponse"}, "errors": [{"shape": "ServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidRequestException"}, {"shape": "InvalidParameterException"}], "documentation": "<p>List the EKS Pod Identity associations in a cluster. You can filter the list by the namespace that the association is in or the service account that the association uses.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags/{resourceArn}"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "NotFoundException"}], "documentation": "<p>List the tags for an Amazon EKS resource.</p>"}, "ListUpdates": {"name": "ListUpdates", "http": {"method": "GET", "requestUri": "/clusters/{name}/updates"}, "input": {"shape": "ListUpdatesRequest"}, "output": {"shape": "ListUpdatesResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ClientException"}, {"shape": "ServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Lists the updates associated with an Amazon EKS resource in your Amazon Web Services account, in the specified Amazon Web Services Region.</p>"}, "RegisterCluster": {"name": "RegisterCluster", "http": {"method": "POST", "requestUri": "/cluster-registrations"}, "input": {"shape": "RegisterClusterRequest"}, "output": {"shape": "RegisterClusterResponse"}, "errors": [{"shape": "ResourceLimitExceededException"}, {"shape": "InvalidParameterException"}, {"shape": "ClientException"}, {"shape": "ServerException"}, {"shape": "ServiceUnavailableException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceInUseException"}, {"shape": "ResourcePropagationDelayException"}], "documentation": "<p>Connects a Kubernetes cluster to the Amazon EKS control plane. </p> <p>Any Kubernetes cluster can be connected to the Amazon EKS control plane to view current information about the cluster and its nodes. </p> <p>Cluster connection requires two steps. First, send a <a href=\"https://docs.aws.amazon.com/eks/latest/APIReference/API_RegisterClusterRequest.html\"> <code>RegisterClusterRequest</code> </a> to add it to the Amazon EKS control plane.</p> <p>Second, a <a href=\"https://amazon-eks.s3.us-west-2.amazonaws.com/eks-connector/manifests/eks-connector/latest/eks-connector.yaml\">Manifest</a> containing the <code>activationID</code> and <code>activationCode</code> must be applied to the Kubernetes cluster through it's native provider to provide visibility.</p> <p>After the manifest is updated and applied, the connected cluster is visible to the Amazon EKS control plane. If the manifest isn't applied within three days, the connected cluster will no longer be visible and must be deregistered using <code>DeregisterCluster</code>.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags/{resourceArn}"}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "NotFoundException"}], "documentation": "<p>Associates the specified tags to an Amazon EKS resource with the specified <code>resourceArn</code>. If existing tags on a resource are not specified in the request parameters, they aren't changed. When a resource is deleted, the tags associated with that resource are also deleted. Tags that you create for Amazon EKS resources don't propagate to any other resources associated with the cluster. For example, if you tag a cluster with this operation, that tag doesn't automatically propagate to the subnets and nodes associated with the cluster.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags/{resourceArn}"}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "NotFoundException"}], "documentation": "<p>Deletes specified tags from an Amazon EKS resource.</p>"}, "UpdateAccessEntry": {"name": "UpdateAccessEntry", "http": {"method": "POST", "requestUri": "/clusters/{name}/access-entries/{principalArn}"}, "input": {"shape": "UpdateAccessEntryRequest"}, "output": {"shape": "UpdateAccessEntryResponse"}, "errors": [{"shape": "ServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidRequestException"}, {"shape": "InvalidParameterException"}], "documentation": "<p>Updates an access entry.</p>"}, "UpdateAddon": {"name": "UpdateAddon", "http": {"method": "POST", "requestUri": "/clusters/{name}/addons/{addonName}/update"}, "input": {"shape": "UpdateAddonRequest"}, "output": {"shape": "UpdateAddonResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}, {"shape": "ClientException"}, {"shape": "ServerException"}], "documentation": "<p>Updates an Amazon EKS add-on.</p>"}, "UpdateClusterConfig": {"name": "UpdateClusterConfig", "http": {"method": "POST", "requestUri": "/clusters/{name}/update-config"}, "input": {"shape": "UpdateClusterConfigRequest"}, "output": {"shape": "UpdateClusterConfigResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ClientException"}, {"shape": "ServerException"}, {"shape": "ResourceInUseException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidRequestException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Updates an Amazon EKS cluster configuration. Your cluster continues to function during the update. The response output includes an update ID that you can use to track the status of your cluster update with <code>DescribeUpdate</code>.</p> <p>You can use this operation to do the following actions:</p> <ul> <li> <p>You can use this API operation to enable or disable exporting the Kubernetes control plane logs for your cluster to CloudWatch Logs. By default, cluster control plane logs aren't exported to CloudWatch Logs. For more information, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/control-plane-logs.html\">Amazon EKS Cluster control plane logs</a> in the <i> <i>Amazon EKS User Guide</i> </i>.</p> <note> <p>CloudWatch Logs ingestion, archive storage, and data scanning rates apply to exported control plane logs. For more information, see <a href=\"http://aws.amazon.com/cloudwatch/pricing/\">CloudWatch Pricing</a>.</p> </note> </li> <li> <p>You can also use this API operation to enable or disable public and private access to your cluster's Kubernetes API server endpoint. By default, public access is enabled, and private access is disabled. For more information, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/cluster-endpoint.html\"> Cluster API server endpoint</a> in the <i> <i>Amazon EKS User Guide</i> </i>.</p> </li> <li> <p>You can also use this API operation to choose different subnets and security groups for the cluster. You must specify at least two subnets that are in different Availability Zones. You can't change which VPC the subnets are from, the subnets must be in the same VPC as the subnets that the cluster was created with. For more information about the VPC requirements, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/network_reqs.html\">https://docs.aws.amazon.com/eks/latest/userguide/network_reqs.html</a> in the <i> <i>Amazon EKS User Guide</i> </i>.</p> </li> <li> <p>You can also use this API operation to enable or disable ARC zonal shift. If zonal shift is enabled, Amazon Web Services configures zonal autoshift for the cluster.</p> </li> <li> <p>You can also use this API operation to add, change, or remove the configuration in the cluster for EKS Hybrid Nodes. To remove the configuration, use the <code>remoteNetworkConfig</code> key with an object containing both subkeys with empty arrays for each. Here is an inline example: <code>\"remoteNetworkConfig\": { \"remoteNodeNetworks\": [], \"remotePodNetworks\": [] }</code>.</p> </li> </ul> <p>Cluster updates are asynchronous, and they should finish within a few minutes. During an update, the cluster status moves to <code>UPDATING</code> (this status transition is eventually consistent). When the update is complete (either <code>Failed</code> or <code>Successful</code>), the cluster status moves to <code>Active</code>.</p>"}, "UpdateClusterVersion": {"name": "UpdateClusterVersion", "http": {"method": "POST", "requestUri": "/clusters/{name}/updates"}, "input": {"shape": "UpdateClusterVersionRequest"}, "output": {"shape": "UpdateClusterVersionResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ClientException"}, {"shape": "ServerException"}, {"shape": "ResourceInUseException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidRequestException"}, {"shape": "ThrottlingException"}, {"shape": "InvalidStateException"}], "documentation": "<p>Updates an Amazon EKS cluster to the specified Kubernetes version. Your cluster continues to function during the update. The response output includes an update ID that you can use to track the status of your cluster update with the <a href=\"https://docs.aws.amazon.com/eks/latest/APIReference/API_DescribeUpdate.html\"> <code>DescribeUpdate</code> </a> API operation.</p> <p>Cluster updates are asynchronous, and they should finish within a few minutes. During an update, the cluster status moves to <code>UPDATING</code> (this status transition is eventually consistent). When the update is complete (either <code>Failed</code> or <code>Successful</code>), the cluster status moves to <code>Active</code>.</p> <p>If your cluster has managed node groups attached to it, all of your node groups' Kubernetes versions must match the cluster's Kubernetes version in order to update the cluster to a new Kubernetes version.</p>"}, "UpdateEksAnywhereSubscription": {"name": "UpdateEksAnywhereSubscription", "http": {"method": "POST", "requestUri": "/eks-anywhere-subscriptions/{id}"}, "input": {"shape": "UpdateEksAnywhereSubscriptionRequest"}, "output": {"shape": "UpdateEksAnywhereSubscriptionResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ClientException"}, {"shape": "ServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Update an EKS Anywhere Subscription. Only auto renewal and tags can be updated after subscription creation.</p>"}, "UpdateNodegroupConfig": {"name": "UpdateNodegroupConfig", "http": {"method": "POST", "requestUri": "/clusters/{name}/node-groups/{nodegroupName}/update-config"}, "input": {"shape": "UpdateNodegroupConfigRequest"}, "output": {"shape": "UpdateNodegroupConfigResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ClientException"}, {"shape": "ServerException"}, {"shape": "ResourceInUseException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Updates an Amazon EKS managed node group configuration. Your node group continues to function during the update. The response output includes an update ID that you can use to track the status of your node group update with the <a href=\"https://docs.aws.amazon.com/eks/latest/APIReference/API_DescribeUpdate.html\"> <code>DescribeUpdate</code> </a> API operation. You can update the Kubernetes labels and taints for a node group and the scaling and version update configuration.</p>"}, "UpdateNodegroupVersion": {"name": "UpdateNodegroupVersion", "http": {"method": "POST", "requestUri": "/clusters/{name}/node-groups/{nodegroupName}/update-version"}, "input": {"shape": "UpdateNodegroupVersionRequest"}, "output": {"shape": "UpdateNodegroupVersionResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ClientException"}, {"shape": "ServerException"}, {"shape": "ResourceInUseException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidRequestException"}], "documentation": "<p>Updates the Kubernetes version or AMI version of an Amazon EKS managed node group.</p> <p>You can update a node group using a launch template only if the node group was originally deployed with a launch template. Additionally, the launch template ID or name must match what was used when the node group was created. You can update the launch template version with necessary changes.</p> <p>If you need to update a custom AMI in a node group that was deployed with a launch template, then update your custom AMI, specify the new ID in a new version of the launch template, and then update the node group to the new version of the launch template.</p> <p>If you update without a launch template, then you can update to the latest available AMI version of a node group's current Kubernetes version by not specifying a Kubernetes version in the request. You can update to the latest AMI version of your cluster's current Kubernetes version by specifying your cluster's Kubernetes version in the request. For information about Linux versions, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/eks-linux-ami-versions.html\">Amazon EKS optimized Amazon Linux AMI versions</a> in the <i>Amazon EKS User Guide</i>. For information about Windows versions, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/eks-ami-versions-windows.html\">Amazon EKS optimized Windows AMI versions</a> in the <i>Amazon EKS User Guide</i>. </p> <p>You cannot roll back a node group to an earlier Kubernetes version or AMI version.</p> <p>When a node in a managed node group is terminated due to a scaling action or update, every <code>Pod</code> on that node is drained first. Amazon EKS attempts to drain the nodes gracefully and will fail if it is unable to do so. You can <code>force</code> the update if Amazon EKS is unable to drain the nodes as a result of a <code>Pod</code> disruption budget issue.</p>"}, "UpdatePodIdentityAssociation": {"name": "UpdatePodIdentityAssociation", "http": {"method": "POST", "requestUri": "/clusters/{name}/pod-identity-associations/{associationId}"}, "input": {"shape": "UpdatePodIdentityAssociationRequest"}, "output": {"shape": "UpdatePodIdentityAssociationResponse"}, "errors": [{"shape": "ServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidRequestException"}, {"shape": "InvalidParameterException"}], "documentation": "<p>Updates a EKS Pod Identity association. In an update, you can change the IAM role, the target IAM role, or <code>disableSessionTags</code>. You must change at least one of these in an update. An association can't be moved between clusters, namespaces, or service accounts. If you need to edit the namespace or service account, you need to delete the association and then create a new association with your desired settings.</p> <p>Similar to Amazon Web Services IAM behavior, EKS Pod Identity associations are eventually consistent, and may take several seconds to be effective after the initial API call returns successfully. You must design your applications to account for these potential delays. We recommend that you don’t include association create/updates in the critical, high-availability code paths of your application. Instead, make changes in a separate initialization or setup routine that you run less frequently.</p> <p>You can set a <i>target IAM role</i> in the same or a different account for advanced scenarios. With a target role, EKS Pod Identity automatically performs two role assumptions in sequence: first assuming the role in the association that is in this account, then using those credentials to assume the target IAM role. This process provides your Pod with temporary credentials that have the permissions defined in the target role, allowing secure access to resources in another Amazon Web Services account.</p>"}}, "shapes": {"AMITypes": {"type": "string", "enum": ["AL2_x86_64", "AL2_x86_64_GPU", "AL2_ARM_64", "CUSTOM", "BOTTLEROCKET_ARM_64", "BOTTLEROCKET_x86_64", "BOTTLEROCKET_ARM_64_FIPS", "BOTTLEROCKET_x86_64_FIPS", "BOTTLEROCKET_ARM_64_NVIDIA", "BOTTLEROCKET_x86_64_NVIDIA", "WINDOWS_CORE_2019_x86_64", "WINDOWS_FULL_2019_x86_64", "WINDOWS_CORE_2022_x86_64", "WINDOWS_FULL_2022_x86_64", "AL2023_x86_64_STANDARD", "AL2023_ARM_64_STANDARD", "AL2023_x86_64_NEURON", "AL2023_x86_64_NVIDIA", "AL2023_ARM_64_NVIDIA"]}, "AccessConfigResponse": {"type": "structure", "members": {"bootstrapClusterCreatorAdminPermissions": {"shape": "BoxedBoolean", "documentation": "<p>Specifies whether or not the cluster creator IAM principal was set as a cluster admin access entry during cluster creation time.</p>"}, "authenticationMode": {"shape": "AuthenticationMode", "documentation": "<p>The current authentication mode of the cluster.</p>"}}, "documentation": "<p>The access configuration for the cluster.</p>"}, "AccessDeniedException": {"type": "structure", "members": {"message": {"shape": "String", "documentation": "<p>You do not have sufficient access to perform this action.</p>"}}, "documentation": "<p>You don't have permissions to perform the requested operation. The <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/id_roles_terms-and-concepts.html\">IAM principal</a> making the request must have at least one IAM permissions policy attached that grants the required permissions. For more information, see <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/access.html\">Access management</a> in the <i>IAM User Guide</i>. </p>", "error": {"httpStatusCode": 403}, "exception": true}, "AccessEntry": {"type": "structure", "members": {"clusterName": {"shape": "String", "documentation": "<p>The name of your cluster.</p>"}, "principalArn": {"shape": "String", "documentation": "<p>The ARN of the IAM principal for the access entry. If you ever delete the IAM principal with this ARN, the access entry isn't automatically deleted. We recommend that you delete the access entry with an ARN for an IAM principal that you delete. If you don't delete the access entry and ever recreate the IAM principal, even if it has the same ARN, the access entry won't work. This is because even though the ARN is the same for the recreated IAM principal, the <code>roleID</code> or <code>userID</code> (you can see this with the Security Token Service <code>GetCallerIdentity</code> API) is different for the recreated IAM principal than it was for the original IAM principal. Even though you don't see the IAM principal's <code>roleID</code> or <code>userID</code> for an access entry, Amazon EKS stores it with the access entry.</p>"}, "kubernetesGroups": {"shape": "StringList", "documentation": "<p>A <code>name</code> that you've specified in a Kubernetes <code>RoleBinding</code> or <code>ClusterRoleBinding</code> object so that Kubernetes authorizes the <code>principalARN</code> access to cluster objects.</p>"}, "accessEntryArn": {"shape": "String", "documentation": "<p>The ARN of the access entry.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The Unix epoch timestamp at object creation.</p>"}, "modifiedAt": {"shape": "Timestamp", "documentation": "<p>The Unix epoch timestamp for the last modification to the object.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>Metadata that assists with categorization and organization. Each tag consists of a key and an optional value. You define both. Tags don't propagate to any other cluster or Amazon Web Services resources.</p>"}, "username": {"shape": "String", "documentation": "<p>The <code>name</code> of a user that can authenticate to your cluster.</p>"}, "type": {"shape": "String", "documentation": "<p>The type of the access entry.</p>"}}, "documentation": "<p>An access entry allows an IAM principal (user or role) to access your cluster. Access entries can replace the need to maintain the <code>aws-auth</code> <code>ConfigMap</code> for authentication. For more information about access entries, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/access-entries.html\">Access entries</a> in the <i>Amazon EKS User Guide</i>.</p>"}, "AccessPoliciesList": {"type": "list", "member": {"shape": "AccessPolicy"}}, "AccessPolicy": {"type": "structure", "members": {"name": {"shape": "String", "documentation": "<p>The name of the access policy.</p>"}, "arn": {"shape": "String", "documentation": "<p>The ARN of the access policy.</p>"}}, "documentation": "<p>An access policy includes permissions that allow Amazon EKS to authorize an IAM principal to work with Kubernetes objects on your cluster. The policies are managed by Amazon EKS, but they're not IAM policies. You can't view the permissions in the policies using the API. The permissions for many of the policies are similar to the Kubernetes <code>cluster-admin</code>, <code>admin</code>, <code>edit</code>, and <code>view</code> cluster roles. For more information about these cluster roles, see <a href=\"https://kubernetes.io/docs/reference/access-authn-authz/rbac/#user-facing-roles\">User-facing roles</a> in the Kubernetes documentation. To view the contents of the policies, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/access-policies.html#access-policy-permissions\">Access policy permissions</a> in the <i>Amazon EKS User Guide</i>.</p>"}, "AccessScope": {"type": "structure", "members": {"type": {"shape": "AccessScopeType", "documentation": "<p>The scope type of an access policy.</p>"}, "namespaces": {"shape": "StringList", "documentation": "<p>A Kubernetes <code>namespace</code> that an access policy is scoped to. A value is required if you specified <code>namespace</code> for <code>Type</code>.</p>"}}, "documentation": "<p>The scope of an <code>AccessPolicy</code> that's associated to an <code>AccessEntry</code>.</p>"}, "AccessScopeType": {"type": "string", "enum": ["cluster", "namespace"]}, "AdditionalInfoMap": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "String"}}, "Addon": {"type": "structure", "members": {"addonName": {"shape": "String", "documentation": "<p>The name of the add-on.</p>"}, "clusterName": {"shape": "ClusterName", "documentation": "<p>The name of your cluster.</p>"}, "status": {"shape": "Addon<PERSON><PERSON>us", "documentation": "<p>The status of the add-on.</p>"}, "addonVersion": {"shape": "String", "documentation": "<p>The version of the add-on.</p>"}, "health": {"shape": "AddonHealth", "documentation": "<p>An object that represents the health of the add-on.</p>"}, "addonArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the add-on.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The Unix epoch timestamp at object creation.</p>"}, "modifiedAt": {"shape": "Timestamp", "documentation": "<p>The Unix epoch timestamp for the last modification to the object.</p>"}, "serviceAccountRoleArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role that's bound to the Kubernetes <code>ServiceAccount</code> object that the add-on uses.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>Metadata that assists with categorization and organization. Each tag consists of a key and an optional value. You define both. Tags don't propagate to any other cluster or Amazon Web Services resources.</p>"}, "publisher": {"shape": "String", "documentation": "<p>The publisher of the add-on.</p>"}, "owner": {"shape": "String", "documentation": "<p>The owner of the add-on.</p>"}, "marketplaceInformation": {"shape": "MarketplaceInformation", "documentation": "<p>Information about an Amazon EKS add-on from the Amazon Web Services Marketplace.</p>"}, "configurationValues": {"shape": "String", "documentation": "<p>The configuration values that you provided.</p>"}, "podIdentityAssociations": {"shape": "StringList", "documentation": "<p>An array of EKS Pod Identity associations owned by the add-on. Each association maps a role to a service account in a namespace in the cluster.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/add-ons-iam.html\">Attach an IAM Role to an Amazon EKS add-on using EKS Pod Identity</a> in the <i>Amazon EKS User Guide</i>.</p>"}}, "documentation": "<p>An Amazon EKS add-on. For more information, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/eks-add-ons.html\">Amazon EKS add-ons</a> in the <i>Amazon EKS User Guide</i>.</p>"}, "AddonCompatibilityDetail": {"type": "structure", "members": {"name": {"shape": "String", "documentation": "<p>The name of the Amazon EKS add-on.</p>"}, "compatibleVersions": {"shape": "StringList", "documentation": "<p>The list of compatible Amazon EKS add-on versions for the next Kubernetes version.</p>"}}, "documentation": "<p>The summary information about the Amazon EKS add-on compatibility for the next Kubernetes version for an insight check in the <code>UPGRADE_READINESS</code> category.</p>"}, "AddonCompatibilityDetails": {"type": "list", "member": {"shape": "AddonCompatibilityDetail"}}, "AddonHealth": {"type": "structure", "members": {"issues": {"shape": "AddonIssueList", "documentation": "<p>An object representing the health issues for an add-on.</p>"}}, "documentation": "<p>The health of the add-on.</p>"}, "AddonInfo": {"type": "structure", "members": {"addonName": {"shape": "String", "documentation": "<p>The name of the add-on.</p>"}, "type": {"shape": "String", "documentation": "<p>The type of the add-on.</p>"}, "addonVersions": {"shape": "AddonVersionInfoList", "documentation": "<p>An object representing information about available add-on versions and compatible Kubernetes versions.</p>"}, "publisher": {"shape": "String", "documentation": "<p>The publisher of the add-on.</p>"}, "owner": {"shape": "String", "documentation": "<p>The owner of the add-on.</p>"}, "marketplaceInformation": {"shape": "MarketplaceInformation", "documentation": "<p>Information about the add-on from the Amazon Web Services Marketplace.</p>"}}, "documentation": "<p>Information about an add-on.</p>"}, "AddonIssue": {"type": "structure", "members": {"code": {"shape": "AddonIssueCode", "documentation": "<p>A code that describes the type of issue.</p>"}, "message": {"shape": "String", "documentation": "<p>A message that provides details about the issue and what might cause it.</p>"}, "resourceIds": {"shape": "StringList", "documentation": "<p>The resource IDs of the issue.</p>"}}, "documentation": "<p>An issue related to an add-on.</p>"}, "AddonIssueCode": {"type": "string", "enum": ["AccessDenied", "InternalFailure", "ClusterUnreachable", "InsufficientNumberOfReplicas", "ConfigurationConflict", "AdmissionRequestDenied", "UnsupportedAddonModification", "K8sResourceNotFound", "AddonSubscriptionNeeded", "AddonPermissionFailure"]}, "AddonIssueList": {"type": "list", "member": {"shape": "AddonIssue"}}, "AddonPodIdentityAssociations": {"type": "structure", "required": ["serviceAccount", "roleArn"], "members": {"serviceAccount": {"shape": "String", "documentation": "<p>The name of a Kubernetes Service Account.</p>"}, "roleArn": {"shape": "String", "documentation": "<p>The ARN of an IAM Role.</p>"}}, "documentation": "<p>A type of EKS Pod Identity association owned by an Amazon EKS add-on.</p> <p>Each association maps a role to a service account in a namespace in the cluster.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/add-ons-iam.html\">Attach an IAM Role to an Amazon EKS add-on using EKS Pod Identity</a> in the <i>Amazon EKS User Guide</i>.</p>"}, "AddonPodIdentityAssociationsList": {"type": "list", "member": {"shape": "AddonPodIdentityAssociations"}}, "AddonPodIdentityConfiguration": {"type": "structure", "members": {"serviceAccount": {"shape": "String", "documentation": "<p>The Kubernetes Service Account name used by the add-on.</p>"}, "recommendedManagedPolicies": {"shape": "StringList", "documentation": "<p>A suggested IAM Policy for the add-on.</p>"}}, "documentation": "<p>Information about how to configure IAM for an add-on.</p>"}, "AddonPodIdentityConfigurationList": {"type": "list", "member": {"shape": "AddonPodIdentityConfiguration"}}, "AddonStatus": {"type": "string", "enum": ["CREATING", "ACTIVE", "CREATE_FAILED", "UPDATING", "DELETING", "DELETE_FAILED", "DEGRADED", "UPDATE_FAILED"]}, "AddonVersionInfo": {"type": "structure", "members": {"addonVersion": {"shape": "String", "documentation": "<p>The version of the add-on.</p>"}, "architecture": {"shape": "StringList", "documentation": "<p>The architectures that the version supports.</p>"}, "computeTypes": {"shape": "StringList", "documentation": "<p>Indicates the compute type of the add-on version.</p>"}, "compatibilities": {"shape": "Compatibilities", "documentation": "<p>An object representing the compatibilities of a version.</p>"}, "requiresConfiguration": {"shape": "Boolean", "documentation": "<p>Whether the add-on requires configuration.</p>"}, "requiresIamPermissions": {"shape": "Boolean", "documentation": "<p>Indicates if the add-on requires IAM Permissions to operate, such as networking permissions.</p>"}}, "documentation": "<p>Information about an add-on version.</p>"}, "AddonVersionInfoList": {"type": "list", "member": {"shape": "AddonVersionInfo"}}, "Addons": {"type": "list", "member": {"shape": "AddonInfo"}}, "AssociateAccessPolicyRequest": {"type": "structure", "required": ["clusterName", "principalArn", "policyArn", "accessScope"], "members": {"clusterName": {"shape": "String", "documentation": "<p>The name of your cluster.</p>", "location": "uri", "locationName": "name"}, "principalArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM user or role for the <code>AccessEntry</code> that you're associating the access policy to. </p>", "location": "uri", "locationName": "principalArn"}, "policyArn": {"shape": "String", "documentation": "<p>The ARN of the <code>AccessPolicy</code> that you're associating. For a list of ARNs, use <code>ListAccessPolicies</code>.</p>"}, "accessScope": {"shape": "AccessScope", "documentation": "<p>The scope for the <code>AccessPolicy</code>. You can scope access policies to an entire cluster or to specific Kubernetes namespaces.</p>"}}}, "AssociateAccessPolicyResponse": {"type": "structure", "members": {"clusterName": {"shape": "String", "documentation": "<p>The name of your cluster.</p>"}, "principalArn": {"shape": "String", "documentation": "<p>The ARN of the IAM principal for the <code>AccessEntry</code>.</p>"}, "associatedAccessPolicy": {"shape": "AssociatedAccessPolicy", "documentation": "<p>The <code>AccessPolicy</code> and scope associated to the <code>AccessEntry</code>.</p>"}}}, "AssociateEncryptionConfigRequest": {"type": "structure", "required": ["clusterName", "encryptionConfig"], "members": {"clusterName": {"shape": "String", "documentation": "<p>The name of your cluster.</p>", "location": "uri", "locationName": "name"}, "encryptionConfig": {"shape": "EncryptionConfigList", "documentation": "<p>The configuration you are using for encryption.</p>"}, "clientRequestToken": {"shape": "String", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency of the request.</p>", "idempotencyToken": true}}}, "AssociateEncryptionConfigResponse": {"type": "structure", "members": {"update": {"shape": "Update"}}}, "AssociateIdentityProviderConfigRequest": {"type": "structure", "required": ["clusterName", "oidc"], "members": {"clusterName": {"shape": "String", "documentation": "<p>The name of your cluster.</p>", "location": "uri", "locationName": "name"}, "oidc": {"shape": "OidcIdentityProviderConfigRequest", "documentation": "<p>An object representing an OpenID Connect (OIDC) identity provider configuration.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>Metadata that assists with categorization and organization. Each tag consists of a key and an optional value. You define both. Tags don't propagate to any other cluster or Amazon Web Services resources.</p>"}, "clientRequestToken": {"shape": "String", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency of the request.</p>", "idempotencyToken": true}}}, "AssociateIdentityProviderConfigResponse": {"type": "structure", "members": {"update": {"shape": "Update"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags for the resource.</p>"}}}, "AssociatedAccessPoliciesList": {"type": "list", "member": {"shape": "AssociatedAccessPolicy"}}, "AssociatedAccessPolicy": {"type": "structure", "members": {"policyArn": {"shape": "String", "documentation": "<p>The ARN of the <code>AccessPolicy</code>.</p>"}, "accessScope": {"shape": "AccessScope", "documentation": "<p>The scope of the access policy.</p>"}, "associatedAt": {"shape": "Timestamp", "documentation": "<p>The date and time the <code>AccessPolicy</code> was associated with an <code>AccessEntry</code>.</p>"}, "modifiedAt": {"shape": "Timestamp", "documentation": "<p>The Unix epoch timestamp for the last modification to the object.</p>"}}, "documentation": "<p>An access policy association.</p>"}, "AuthenticationMode": {"type": "string", "enum": ["API", "API_AND_CONFIG_MAP", "CONFIG_MAP"]}, "AutoScalingGroup": {"type": "structure", "members": {"name": {"shape": "String", "documentation": "<p>The name of the Auto Scaling group associated with an Amazon EKS managed node group.</p>"}}, "documentation": "<p>An Auto Scaling group that is associated with an Amazon EKS managed node group.</p>"}, "AutoScalingGroupList": {"type": "list", "member": {"shape": "AutoScalingGroup"}}, "BadRequestException": {"type": "structure", "members": {"message": {"shape": "String", "documentation": "<p>This exception is thrown if the request contains a semantic error. The precise meaning will depend on the API, and will be documented in the error message.</p>"}}, "documentation": "<p>This exception is thrown if the request contains a semantic error. The precise meaning will depend on the API, and will be documented in the error message.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "BlockStorage": {"type": "structure", "members": {"enabled": {"shape": "BoxedBoolean", "documentation": "<p>Indicates if the block storage capability is enabled on your EKS Auto Mode cluster. If the block storage capability is enabled, EKS Auto Mode will create and delete EBS volumes in your Amazon Web Services account.</p>"}}, "documentation": "<p>Indicates the current configuration of the block storage capability on your EKS Auto Mode cluster. For example, if the capability is enabled or disabled. If the block storage capability is enabled, EKS Auto Mode will create and delete EBS volumes in your Amazon Web Services account. For more information, see EKS Auto Mode block storage capability in the <i>Amazon EKS User Guide</i>.</p>"}, "Boolean": {"type": "boolean"}, "BoxedBoolean": {"type": "boolean", "box": true}, "BoxedInteger": {"type": "integer", "box": true}, "Capacity": {"type": "integer", "box": true, "min": 1}, "CapacityTypes": {"type": "string", "enum": ["ON_DEMAND", "SPOT", "CAPACITY_BLOCK"]}, "Category": {"type": "string", "enum": ["UPGRADE_READINESS", "MISCONFIGURATION"]}, "CategoryList": {"type": "list", "member": {"shape": "Category"}}, "Certificate": {"type": "structure", "members": {"data": {"shape": "String", "documentation": "<p>The Base64-encoded certificate data required to communicate with your cluster. Add this to the <code>certificate-authority-data</code> section of the <code>kubeconfig</code> file for your cluster.</p>"}}, "documentation": "<p>An object representing the <code>certificate-authority-data</code> for your cluster.</p>"}, "ClientException": {"type": "structure", "members": {"clusterName": {"shape": "String", "documentation": "<p>The Amazon EKS cluster associated with the exception.</p>"}, "nodegroupName": {"shape": "String", "documentation": "<p>The Amazon EKS managed node group associated with the exception.</p>"}, "addonName": {"shape": "String", "documentation": "<p>The Amazon EKS add-on name associated with the exception.</p>"}, "subscriptionId": {"shape": "String", "documentation": "<p>The Amazon EKS subscription ID with the exception.</p>"}, "message": {"shape": "String", "documentation": "<p>These errors are usually caused by a client action. Actions can include using an action or resource on behalf of an <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/id_roles_terms-and-concepts.html\">IAM principal</a> that doesn't have permissions to use the action or resource or specifying an identifier that is not valid.</p>"}}, "documentation": "<p>These errors are usually caused by a client action. Actions can include using an action or resource on behalf of an <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/id_roles_terms-and-concepts.html\">IAM principal</a> that doesn't have permissions to use the action or resource or specifying an identifier that is not valid.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "ClientStat": {"type": "structure", "members": {"userAgent": {"shape": "String", "documentation": "<p>The user agent of the Kubernetes client using the deprecated resource.</p>"}, "numberOfRequestsLast30Days": {"shape": "Integer", "documentation": "<p>The number of requests from the Kubernetes client seen over the last 30 days.</p>"}, "lastRequestTime": {"shape": "Timestamp", "documentation": "<p>The timestamp of the last request seen from the Kubernetes client.</p>"}}, "documentation": "<p>Details about clients using the deprecated resources.</p>"}, "ClientStats": {"type": "list", "member": {"shape": "ClientStat"}}, "Cluster": {"type": "structure", "members": {"name": {"shape": "String", "documentation": "<p>The name of your cluster.</p>"}, "arn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the cluster.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The Unix epoch timestamp at object creation.</p>"}, "version": {"shape": "String", "documentation": "<p>The Kubernetes server version for the cluster.</p>"}, "endpoint": {"shape": "String", "documentation": "<p>The endpoint for your Kubernetes API server.</p>"}, "roleArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role that provides permissions for the Kubernetes control plane to make calls to Amazon Web Services API operations on your behalf.</p>"}, "resourcesVpcConfig": {"shape": "VpcConfigResponse", "documentation": "<p>The VPC configuration used by the cluster control plane. Amazon EKS VPC resources have specific requirements to work properly with Kubernetes. For more information, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/network_reqs.html\">Cluster VPC considerations</a> and <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/sec-group-reqs.html\">Cluster security group considerations</a> in the <i>Amazon EKS User Guide</i>.</p>"}, "kubernetesNetworkConfig": {"shape": "KubernetesNetworkConfigResponse", "documentation": "<p>The Kubernetes network configuration for the cluster.</p>"}, "logging": {"shape": "Logging", "documentation": "<p>The logging configuration for your cluster.</p>"}, "identity": {"shape": "Identity", "documentation": "<p>The identity provider information for the cluster.</p>"}, "status": {"shape": "ClusterStatus", "documentation": "<p>The current status of the cluster.</p>"}, "certificateAuthority": {"shape": "Certificate", "documentation": "<p>The <code>certificate-authority-data</code> for your cluster.</p>"}, "clientRequestToken": {"shape": "String", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency of the request.</p>"}, "platformVersion": {"shape": "String", "documentation": "<p>The platform version of your Amazon EKS cluster. For more information about clusters deployed on the Amazon Web Services Cloud, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/platform-versions.html\">Platform versions</a> in the <i> <i>Amazon EKS User Guide</i> </i>. For more information about local clusters deployed on an Outpost, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/eks-outposts-platform-versions.html\">Amazon EKS local cluster platform versions</a> in the <i> <i>Amazon EKS User Guide</i> </i>.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>Metadata that assists with categorization and organization. Each tag consists of a key and an optional value. You define both. Tags don't propagate to any other cluster or Amazon Web Services resources.</p>"}, "encryptionConfig": {"shape": "EncryptionConfigList", "documentation": "<p>The encryption configuration for the cluster.</p>"}, "connectorConfig": {"shape": "ConnectorConfigResponse", "documentation": "<p>The configuration used to connect to a cluster for registration.</p>"}, "id": {"shape": "String", "documentation": "<p>The ID of your local Amazon EKS cluster on an Amazon Web Services Outpost. This property isn't available for an Amazon EKS cluster on the Amazon Web Services cloud.</p>"}, "health": {"shape": "ClusterHealth", "documentation": "<p>An object representing the health of your Amazon EKS cluster.</p>"}, "outpostConfig": {"shape": "OutpostConfigResponse", "documentation": "<p>An object representing the configuration of your local Amazon EKS cluster on an Amazon Web Services Outpost. This object isn't available for clusters on the Amazon Web Services cloud.</p>"}, "accessConfig": {"shape": "AccessConfigResponse", "documentation": "<p>The access configuration for the cluster.</p>"}, "upgradePolicy": {"shape": "UpgradePolicyResponse", "documentation": "<p>This value indicates if extended support is enabled or disabled for the cluster.</p> <p> <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/extended-support-control.html\">Learn more about EKS Extended Support in the <i>Amazon EKS User Guide</i>.</a> </p>"}, "zonalShiftConfig": {"shape": "ZonalShiftConfigResponse", "documentation": "<p>The configuration for zonal shift for the cluster.</p>"}, "remoteNetworkConfig": {"shape": "RemoteNetworkConfigResponse", "documentation": "<p>The configuration in the cluster for EKS Hybrid Nodes. You can add, change, or remove this configuration after the cluster is created.</p>"}, "computeConfig": {"shape": "ComputeConfigResponse", "documentation": "<p>Indicates the current configuration of the compute capability on your EKS Auto Mode cluster. For example, if the capability is enabled or disabled. If the compute capability is enabled, EKS Auto Mode will create and delete EC2 Managed Instances in your Amazon Web Services account. For more information, see EKS Auto Mode compute capability in the <i>Amazon EKS User Guide</i>.</p>"}, "storageConfig": {"shape": "StorageConfigResponse", "documentation": "<p>Indicates the current configuration of the block storage capability on your EKS Auto Mode cluster. For example, if the capability is enabled or disabled. If the block storage capability is enabled, EKS Auto Mode will create and delete EBS volumes in your Amazon Web Services account. For more information, see EKS Auto Mode block storage capability in the <i>Amazon EKS User Guide</i>.</p>"}}, "documentation": "<p>An object representing an Amazon EKS cluster.</p>"}, "ClusterHealth": {"type": "structure", "members": {"issues": {"shape": "ClusterIssueList", "documentation": "<p>An object representing the health issues of your Amazon EKS cluster.</p>"}}, "documentation": "<p>An object representing the health of your Amazon EKS cluster.</p>"}, "ClusterIssue": {"type": "structure", "members": {"code": {"shape": "ClusterIssueCode", "documentation": "<p>The error code of the issue.</p>"}, "message": {"shape": "String", "documentation": "<p>A description of the issue.</p>"}, "resourceIds": {"shape": "StringList", "documentation": "<p>The resource IDs that the issue relates to.</p>"}}, "documentation": "<p>An issue with your Amazon EKS cluster.</p>"}, "ClusterIssueCode": {"type": "string", "enum": ["AccessDenied", "ClusterUnreachable", "ConfigurationConflict", "InternalFailure", "ResourceLimitExceeded", "ResourceNotFound", "IamRoleNotFound", "VpcNotFound", "InsufficientFreeAddresses", "Ec2ServiceNotSubscribed", "Ec2SubnetNotFound", "Ec2SecurityGroupNotFound", "KmsGrantRevoked", "KmsKeyNotFound", "KmsKeyMarkedForDeletion", "KmsKeyDisabled", "StsRegionalEndpointDisabled", "UnsupportedVersion", "Other"]}, "ClusterIssueList": {"type": "list", "member": {"shape": "ClusterIssue"}}, "ClusterName": {"type": "string", "max": 100, "min": 1, "pattern": "^[0-9A-Za-z][A-Za-z0-9\\-_]*"}, "ClusterStatus": {"type": "string", "enum": ["CREATING", "ACTIVE", "DELETING", "FAILED", "UPDATING", "PENDING"]}, "ClusterVersionInformation": {"type": "structure", "members": {"clusterVersion": {"shape": "String", "documentation": "<p>The Kubernetes version for the cluster.</p>"}, "clusterType": {"shape": "String", "documentation": "<p>The type of cluster this version is for.</p>"}, "defaultPlatformVersion": {"shape": "String", "documentation": "<p>Default platform version for this Kubernetes version.</p>"}, "defaultVersion": {"shape": "Boolean", "documentation": "<p>Indicates if this is a default version.</p>"}, "releaseDate": {"shape": "Timestamp", "documentation": "<p>The release date of this cluster version.</p>"}, "endOfStandardSupportDate": {"shape": "Timestamp", "documentation": "<p>Date when standard support ends for this version.</p>"}, "endOfExtendedSupportDate": {"shape": "Timestamp", "documentation": "<p>Date when extended support ends for this version.</p>"}, "status": {"shape": "ClusterVersionStatus", "documentation": "<important> <p>This field is deprecated. Use <code>versionStatus</code> instead, as that field matches for input and output of this action.</p> </important> <p>Current status of this cluster version.</p>"}, "versionStatus": {"shape": "VersionStatus", "documentation": "<p>Current status of this cluster version.</p>"}, "kubernetesPatchVersion": {"shape": "String", "documentation": "<p>The patch version of Kubernetes for this cluster version.</p>"}}, "documentation": "<p>Contains details about a specific EKS cluster version.</p>"}, "ClusterVersionList": {"type": "list", "member": {"shape": "ClusterVersionInformation"}}, "ClusterVersionStatus": {"type": "string", "enum": ["unsupported", "standard-support", "extended-support"]}, "Compatibilities": {"type": "list", "member": {"shape": "Compatibility"}}, "Compatibility": {"type": "structure", "members": {"clusterVersion": {"shape": "String", "documentation": "<p>The supported Kubernetes version of the cluster.</p>"}, "platformVersions": {"shape": "StringList", "documentation": "<p>The supported compute platform.</p>"}, "defaultVersion": {"shape": "Boolean", "documentation": "<p>The supported default version.</p>"}}, "documentation": "<p>Compatibility information.</p>"}, "ComputeConfigRequest": {"type": "structure", "members": {"enabled": {"shape": "BoxedBoolean", "documentation": "<p>Request to enable or disable the compute capability on your EKS Auto Mode cluster. If the compute capability is enabled, EKS Auto Mode will create and delete EC2 Managed Instances in your Amazon Web Services account.</p>"}, "nodePools": {"shape": "StringList", "documentation": "<p>Configuration for node pools that defines the compute resources for your EKS Auto Mode cluster. For more information, see EKS Auto Mode Node Pools in the <i>Amazon EKS User Guide</i>.</p>"}, "nodeRoleArn": {"shape": "String", "documentation": "<p>The ARN of the IAM Role EKS will assign to EC2 Managed Instances in your EKS Auto Mode cluster. This value cannot be changed after the compute capability of EKS Auto Mode is enabled. For more information, see the IAM Reference in the <i>Amazon EKS User Guide</i>.</p>"}}, "documentation": "<p>Request to update the configuration of the compute capability of your EKS Auto Mode cluster. For example, enable the capability. For more information, see EKS Auto Mode compute capability in the <i>Amazon EKS User Guide</i>.</p>"}, "ComputeConfigResponse": {"type": "structure", "members": {"enabled": {"shape": "BoxedBoolean", "documentation": "<p>Indicates if the compute capability is enabled on your EKS Auto Mode cluster. If the compute capability is enabled, EKS Auto Mode will create and delete EC2 Managed Instances in your Amazon Web Services account.</p>"}, "nodePools": {"shape": "StringList", "documentation": "<p>Indicates the current configuration of node pools in your EKS Auto Mode cluster. For more information, see EKS Auto Mode Node Pools in the <i>Amazon EKS User Guide</i>.</p>"}, "nodeRoleArn": {"shape": "String", "documentation": "<p>The ARN of the IAM Role EKS will assign to EC2 Managed Instances in your EKS Auto Mode cluster.</p>"}}, "documentation": "<p>Indicates the status of the request to update the compute capability of your EKS Auto Mode cluster.</p>"}, "ConnectorConfigProvider": {"type": "string", "enum": ["EKS_ANYWHERE", "ANTHOS", "GKE", "AKS", "OPENSHIFT", "TANZU", "RANCHER", "EC2", "OTHER"]}, "ConnectorConfigRequest": {"type": "structure", "required": ["roleArn", "provider"], "members": {"roleArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the role that is authorized to request the connector configuration.</p>"}, "provider": {"shape": "ConnectorConfigProvider", "documentation": "<p>The cloud provider for the target cluster to connect.</p>"}}, "documentation": "<p>The configuration sent to a cluster for configuration.</p>"}, "ConnectorConfigResponse": {"type": "structure", "members": {"activationId": {"shape": "String", "documentation": "<p>A unique ID associated with the cluster for registration purposes.</p>"}, "activationCode": {"shape": "String", "documentation": "<p>A unique code associated with the cluster for registration purposes.</p>"}, "activationExpiry": {"shape": "Timestamp", "documentation": "<p>The expiration time of the connected cluster. The cluster's YAML file must be applied through the native provider.</p>"}, "provider": {"shape": "String", "documentation": "<p>The cluster's cloud service provider.</p>"}, "roleArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the role to communicate with services from the connected Kubernetes cluster.</p>"}}, "documentation": "<p>The full description of your connected cluster.</p>"}, "ControlPlanePlacementRequest": {"type": "structure", "members": {"groupName": {"shape": "String", "documentation": "<p>The name of the placement group for the Kubernetes control plane instances. This setting can't be changed after cluster creation. </p>"}}, "documentation": "<p>The placement configuration for all the control plane instances of your local Amazon EKS cluster on an Amazon Web Services Outpost. For more information, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/eks-outposts-capacity-considerations.html\">Capacity considerations</a> in the <i>Amazon EKS User Guide</i>.</p>"}, "ControlPlanePlacementResponse": {"type": "structure", "members": {"groupName": {"shape": "String", "documentation": "<p>The name of the placement group for the Kubernetes control plane instances.</p>"}}, "documentation": "<p>The placement configuration for all the control plane instances of your local Amazon EKS cluster on an Amazon Web Services Outpost. For more information, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/eks-outposts-capacity-considerations.html\">Capacity considerations</a> in the <i>Amazon EKS User Guide</i>.</p>"}, "CreateAccessConfigRequest": {"type": "structure", "members": {"bootstrapClusterCreatorAdminPermissions": {"shape": "BoxedBoolean", "documentation": "<p>Specifies whether or not the cluster creator IAM principal was set as a cluster admin access entry during cluster creation time. The default value is <code>true</code>.</p>"}, "authenticationMode": {"shape": "AuthenticationMode", "documentation": "<p>The desired authentication mode for the cluster. If you create a cluster by using the EKS API, Amazon Web Services SDKs, or CloudFormation, the default is <code>CONFIG_MAP</code>. If you create the cluster by using the Amazon Web Services Management Console, the default value is <code>API_AND_CONFIG_MAP</code>.</p>"}}, "documentation": "<p>The access configuration information for the cluster.</p>"}, "CreateAccessEntryRequest": {"type": "structure", "required": ["clusterName", "principalArn"], "members": {"clusterName": {"shape": "String", "documentation": "<p>The name of your cluster.</p>", "location": "uri", "locationName": "name"}, "principalArn": {"shape": "String", "documentation": "<p>The ARN of the IAM principal for the <code>AccessEntry</code>. You can specify one ARN for each access entry. You can't specify the same ARN in more than one access entry. This value can't be changed after access entry creation.</p> <p>The valid principals differ depending on the type of the access entry in the <code>type</code> field. For <code>STANDARD</code> access entries, you can use every IAM principal type. For nodes (<code>EC2</code> (for EKS Auto Mode), <code>EC2_LINUX</code>, <code>EC2_WINDOWS</code>, <code>FARGATE_LINUX</code>, and <code>HYBRID_LINUX</code>), the only valid ARN is IAM roles. You can't use the STS session principal type with access entries because this is a temporary principal for each session and not a permanent identity that can be assigned permissions.</p> <p> <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/best-practices.html#bp-users-federation-idp\">IAM best practices</a> recommend using IAM roles with temporary credentials, rather than IAM users with long-term credentials. </p>"}, "kubernetesGroups": {"shape": "StringList", "documentation": "<p>The value for <code>name</code> that you've specified for <code>kind: Group</code> as a <code>subject</code> in a Kubernetes <code>RoleBinding</code> or <code>ClusterRoleBinding</code> object. Amazon EKS doesn't confirm that the value for <code>name</code> exists in any bindings on your cluster. You can specify one or more names.</p> <p>Kubernetes authorizes the <code>principalArn</code> of the access entry to access any cluster objects that you've specified in a Kubernetes <code>Role</code> or <code>ClusterRole</code> object that is also specified in a binding's <code>roleRef</code>. For more information about creating Kubernetes <code>RoleBinding</code>, <code>ClusterRoleBinding</code>, <code>Role</code>, or <code>ClusterRole</code> objects, see <a href=\"https://kubernetes.io/docs/reference/access-authn-authz/rbac/\">Using RBAC Authorization in the Kubernetes documentation</a>.</p> <p>If you want Amazon EKS to authorize the <code>principalArn</code> (instead of, or in addition to Kubernetes authorizing the <code>principalArn</code>), you can associate one or more access policies to the access entry using <code>AssociateAccessPolicy</code>. If you associate any access policies, the <code>principalARN</code> has all permissions assigned in the associated access policies and all permissions in any Kubernetes <code>Role</code> or <code>ClusterRole</code> objects that the group names are bound to.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>Metadata that assists with categorization and organization. Each tag consists of a key and an optional value. You define both. Tags don't propagate to any other cluster or Amazon Web Services resources.</p>"}, "clientRequestToken": {"shape": "String", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency of the request.</p>", "idempotencyToken": true}, "username": {"shape": "String", "documentation": "<p>The username to authenticate to Kuber<PERSON><PERSON> with. We recommend not specifying a username and letting Amazon EKS specify it for you. For more information about the value Amazon EKS specifies for you, or constraints before specifying your own username, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/access-entries.html#creating-access-entries\">Creating access entries</a> in the <i>Amazon EKS User Guide</i>.</p>"}, "type": {"shape": "String", "documentation": "<p>The type of the new access entry. Valid values are <code>STANDARD</code>, <code>FARGATE_LINUX</code>, <code>EC2_LINUX</code>, <code>EC2_WINDOWS</code>, <code>EC2</code> (for EKS Auto Mode), <code>HYBRID_LINUX</code>, and <code>HYPERPOD_LINUX</code>. </p> <p>If the <code>principalArn</code> is for an IAM role that's used for self-managed Amazon EC2 nodes, specify <code>EC2_LINUX</code> or <code>EC2_WINDOWS</code>. Amazon EKS grants the necessary permissions to the node for you. If the <code>principalArn</code> is for any other purpose, specify <code>STANDARD</code>. If you don't specify a value, Amazon EKS sets the value to <code>STANDARD</code>. If you have the access mode of the cluster set to <code>API_AND_CONFIG_MAP</code>, it's unnecessary to create access entries for IAM roles used with Fargate profiles or managed Amazon EC2 nodes, because Amazon EKS creates entries in the <code>aws-auth</code> <code>ConfigMap</code> for the roles. You can't change this value once you've created the access entry.</p> <p>If you set the value to <code>EC2_LINUX</code> or <code>EC2_WINDOWS</code>, you can't specify values for <code>kubernetesGroups</code>, or associate an <code>AccessPolicy</code> to the access entry.</p>"}}}, "CreateAccessEntryResponse": {"type": "structure", "members": {"accessEntry": {"shape": "AccessEntry"}}}, "CreateAddonRequest": {"type": "structure", "required": ["clusterName", "addonName"], "members": {"clusterName": {"shape": "ClusterName", "documentation": "<p>The name of your cluster.</p>", "location": "uri", "locationName": "name"}, "addonName": {"shape": "String", "documentation": "<p>The name of the add-on. The name must match one of the names returned by <code>DescribeAddonVersions</code>.</p>"}, "addonVersion": {"shape": "String", "documentation": "<p>The version of the add-on. The version must match one of the versions returned by <a href=\"https://docs.aws.amazon.com/eks/latest/APIReference/API_DescribeAddonVersions.html\"> <code>DescribeAddonVersions</code> </a>.</p>"}, "serviceAccountRoleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of an existing IAM role to bind to the add-on's service account. The role must be assigned the IAM permissions required by the add-on. If you don't specify an existing IAM role, then the add-on uses the permissions assigned to the node IAM role. For more information, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/create-node-role.html\">Amazon EKS node IAM role</a> in the <i>Amazon EKS User Guide</i>.</p> <note> <p>To specify an existing IAM role, you must have an IAM OpenID Connect (OIDC) provider created for your cluster. For more information, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/enable-iam-roles-for-service-accounts.html\">Enabling IAM roles for service accounts on your cluster</a> in the <i>Amazon EKS User Guide</i>.</p> </note>"}, "resolveConflicts": {"shape": "ResolveConflicts", "documentation": "<p>How to resolve field value conflicts for an Amazon EKS add-on. Conflicts are handled based on the value you choose:</p> <ul> <li> <p> <b>None</b> – If the self-managed version of the add-on is installed on your cluster, Amazon EKS doesn't change the value. Creation of the add-on might fail.</p> </li> <li> <p> <b>Overwrite</b> – If the self-managed version of the add-on is installed on your cluster and the Amazon EKS default value is different than the existing value, Amazon EKS changes the value to the Amazon EKS default value.</p> </li> <li> <p> <b>Preserve</b> – This is similar to the NONE option. If the self-managed version of the add-on is installed on your cluster Amazon EKS doesn't change the add-on resource properties. Creation of the add-on might fail if conflicts are detected. This option works differently during the update operation. For more information, see <a href=\"https://docs.aws.amazon.com/eks/latest/APIReference/API_UpdateAddon.html\"> <code>UpdateAddon</code> </a>.</p> </li> </ul> <p>If you don't currently have the self-managed version of the add-on installed on your cluster, the Amazon EKS add-on is installed. Amazon EKS sets all values to default values, regardless of the option that you specify.</p>"}, "clientRequestToken": {"shape": "String", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency of the request.</p>", "idempotencyToken": true}, "tags": {"shape": "TagMap", "documentation": "<p>Metadata that assists with categorization and organization. Each tag consists of a key and an optional value. You define both. Tags don't propagate to any other cluster or Amazon Web Services resources.</p>"}, "configurationValues": {"shape": "String", "documentation": "<p>The set of configuration values for the add-on that's created. The values that you provide are validated against the schema returned by <code>DescribeAddonConfiguration</code>.</p>"}, "podIdentityAssociations": {"shape": "AddonPodIdentityAssociationsList", "documentation": "<p>An array of EKS Pod Identity associations to be created. Each association maps a Kubernetes service account to an IAM role.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/add-ons-iam.html\">Attach an IAM Role to an Amazon EKS add-on using EKS Pod Identity</a> in the <i>Amazon EKS User Guide</i>.</p>"}}}, "CreateAddonResponse": {"type": "structure", "members": {"addon": {"shape": "<PERSON><PERSON>"}}}, "CreateClusterRequest": {"type": "structure", "required": ["name", "roleArn", "resourcesVpcConfig"], "members": {"name": {"shape": "ClusterName", "documentation": "<p>The unique name to give to your cluster. The name can contain only alphanumeric characters (case-sensitive), hyphens, and underscores. It must start with an alphanumeric character and can't be longer than 100 characters. The name must be unique within the Amazon Web Services Region and Amazon Web Services account that you're creating the cluster in.</p>"}, "version": {"shape": "String", "documentation": "<p>The desired Kubernetes version for your cluster. If you don't specify a value here, the default version available in Amazon EKS is used.</p> <note> <p>The default version might not be the latest version available.</p> </note>"}, "roleArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role that provides permissions for the Kubernetes control plane to make calls to Amazon Web Services API operations on your behalf. For more information, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/service_IAM_role.html\">Amazon EKS Service IAM Role</a> in the <i> <i>Amazon EKS User Guide</i> </i>.</p>"}, "resourcesVpcConfig": {"shape": "VpcConfigRequest", "documentation": "<p>The VPC configuration that's used by the cluster control plane. Amazon EKS VPC resources have specific requirements to work properly with Kubernetes. For more information, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/network_reqs.html\">Cluster VPC Considerations</a> and <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/sec-group-reqs.html\">Cluster Security Group Considerations</a> in the <i>Amazon EKS User Guide</i>. You must specify at least two subnets. You can specify up to five security groups. However, we recommend that you use a dedicated security group for your cluster control plane.</p>"}, "kubernetesNetworkConfig": {"shape": "KubernetesNetworkConfigRequest", "documentation": "<p>The Kubernetes network configuration for the cluster.</p>"}, "logging": {"shape": "Logging", "documentation": "<p>Enable or disable exporting the Kubernetes control plane logs for your cluster to CloudWatch Logs . By default, cluster control plane logs aren't exported to CloudWatch Logs . For more information, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/control-plane-logs.html\">Amazon EKS Cluster control plane logs</a> in the <i> <i>Amazon EKS User Guide</i> </i>.</p> <note> <p>CloudWatch Logs ingestion, archive storage, and data scanning rates apply to exported control plane logs. For more information, see <a href=\"http://aws.amazon.com/cloudwatch/pricing/\">CloudWatch Pricing</a>.</p> </note>"}, "clientRequestToken": {"shape": "String", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency of the request.</p>", "idempotencyToken": true}, "tags": {"shape": "TagMap", "documentation": "<p>Metadata that assists with categorization and organization. Each tag consists of a key and an optional value. You define both. Tags don't propagate to any other cluster or Amazon Web Services resources.</p>"}, "encryptionConfig": {"shape": "EncryptionConfigList", "documentation": "<p>The encryption configuration for the cluster.</p>"}, "outpostConfig": {"shape": "OutpostConfigRequest", "documentation": "<p>An object representing the configuration of your local Amazon EKS cluster on an Amazon Web Services Outpost. Before creating a local cluster on an Outpost, review <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/eks-outposts-local-cluster-overview.html\">Local clusters for Amazon EKS on Amazon Web Services Outposts</a> in the <i>Amazon EKS User Guide</i>. This object isn't available for creating Amazon EKS clusters on the Amazon Web Services cloud.</p>"}, "accessConfig": {"shape": "CreateAccessConfigRequest", "documentation": "<p>The access configuration for the cluster.</p>"}, "bootstrapSelfManagedAddons": {"shape": "BoxedBoolean", "documentation": "<p>If you set this value to <code>False</code> when creating a cluster, the default networking add-ons will not be installed.</p> <p>The default networking add-ons include <code>vpc-cni</code>, <code>coredns</code>, and <code>kube-proxy</code>.</p> <p>Use this option when you plan to install third-party alternative add-ons or self-manage the default networking add-ons.</p>"}, "upgradePolicy": {"shape": "UpgradePolicyRequest", "documentation": "<p>New clusters, by default, have extended support enabled. You can disable extended support when creating a cluster by setting this value to <code>STANDARD</code>.</p>"}, "zonalShiftConfig": {"shape": "ZonalShiftConfigRequest", "documentation": "<p>Enable or disable ARC zonal shift for the cluster. If zonal shift is enabled, Amazon Web Services configures zonal autoshift for the cluster.</p> <p>Zonal shift is a feature of Amazon Application Recovery Controller (ARC). ARC zonal shift is designed to be a temporary measure that allows you to move traffic for a resource away from an impaired AZ until the zonal shift expires or you cancel it. You can extend the zonal shift if necessary.</p> <p>You can start a zonal shift for an Amazon EKS cluster, or you can allow Amazon Web Services to do it for you by enabling <i>zonal autoshift</i>. This shift updates the flow of east-to-west network traffic in your cluster to only consider network endpoints for Pods running on worker nodes in healthy AZs. Additionally, any ALB or NLB handling ingress traffic for applications in your Amazon EKS cluster will automatically route traffic to targets in the healthy AZs. For more information about zonal shift in EKS, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/zone-shift.html\">Learn about Amazon Application Recovery Controller (ARC) Zonal Shift in Amazon EKS</a> in the <i> <i>Amazon EKS User Guide</i> </i>.</p>"}, "remoteNetworkConfig": {"shape": "RemoteNetworkConfigRequest", "documentation": "<p>The configuration in the cluster for EKS Hybrid Nodes. You can add, change, or remove this configuration after the cluster is created.</p>"}, "computeConfig": {"shape": "ComputeConfigRequest", "documentation": "<p>Enable or disable the compute capability of EKS Auto Mode when creating your EKS Auto Mode cluster. If the compute capability is enabled, EKS Auto Mode will create and delete EC2 Managed Instances in your Amazon Web Services account</p>"}, "storageConfig": {"shape": "StorageConfigRequest", "documentation": "<p>Enable or disable the block storage capability of EKS Auto Mode when creating your EKS Auto Mode cluster. If the block storage capability is enabled, EKS Auto Mode will create and delete EBS volumes in your Amazon Web Services account.</p>"}}}, "CreateClusterResponse": {"type": "structure", "members": {"cluster": {"shape": "Cluster", "documentation": "<p>The full description of your new cluster.</p>"}}}, "CreateEksAnywhereSubscriptionRequest": {"type": "structure", "required": ["name", "term"], "members": {"name": {"shape": "EksAnywhereSubscriptionName", "documentation": "<p>The unique name for your subscription. It must be unique in your Amazon Web Services account in the Amazon Web Services Region you're creating the subscription in. The name can contain only alphanumeric characters (case-sensitive), hyphens, and underscores. It must start with an alphabetic character and can't be longer than 100 characters.</p>"}, "term": {"shape": "EksAnywhereSubscriptionTerm", "documentation": "<p>An object representing the term duration and term unit type of your subscription. This determines the term length of your subscription. Valid values are MONTHS for term unit and 12 or 36 for term duration, indicating a 12 month or 36 month subscription. This value cannot be changed after creating the subscription.</p>"}, "licenseQuantity": {"shape": "Integer", "documentation": "<p>The number of licenses to purchase with the subscription. Valid values are between 1 and 100. This value can't be changed after creating the subscription.</p>"}, "licenseType": {"shape": "EksAnywhereSubscriptionLicenseType", "documentation": "<p>The license type for all licenses in the subscription. Valid value is CLUSTER. With the CLUSTER license type, each license covers support for a single EKS Anywhere cluster.</p>"}, "autoRenew": {"shape": "Boolean", "documentation": "<p>A boolean indicating whether the subscription auto renews at the end of the term.</p>"}, "clientRequestToken": {"shape": "String", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency of the request.</p>", "idempotencyToken": true}, "tags": {"shape": "TagMap", "documentation": "<p>The metadata for a subscription to assist with categorization and organization. Each tag consists of a key and an optional value. Subscription tags don't propagate to any other resources associated with the subscription.</p>"}}}, "CreateEksAnywhereSubscriptionResponse": {"type": "structure", "members": {"subscription": {"shape": "EksAnywhereSubscription", "documentation": "<p>The full description of the subscription.</p>"}}}, "CreateFargateProfileRequest": {"type": "structure", "required": ["fargateProfileName", "clusterName", "podExecutionRoleArn"], "members": {"fargateProfileName": {"shape": "String", "documentation": "<p>The name of the Fargate profile.</p>"}, "clusterName": {"shape": "String", "documentation": "<p>The name of your cluster.</p>", "location": "uri", "locationName": "name"}, "podExecutionRoleArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the <code>Pod</code> execution role to use for a <code>Pod</code> that matches the selectors in the Fargate profile. The <code>Pod</code> execution role allows Fargate infrastructure to register with your cluster as a node, and it provides read access to Amazon ECR image repositories. For more information, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/pod-execution-role.html\"> <code>Pod</code> execution role</a> in the <i>Amazon EKS User Guide</i>.</p>"}, "subnets": {"shape": "StringList", "documentation": "<p>The IDs of subnets to launch a <code>Pod</code> into. A <code>Pod</code> running on Fargate isn't assigned a public IP address, so only private subnets (with no direct route to an Internet Gateway) are accepted for this parameter.</p>"}, "selectors": {"shape": "FargateProfileSelectors", "documentation": "<p>The selectors to match for a <code>Pod</code> to use this Fargate profile. Each selector must have an associated Kubernetes <code>namespace</code>. Optionally, you can also specify <code>labels</code> for a <code>namespace</code>. You may specify up to five selectors in a Fargate profile.</p>"}, "clientRequestToken": {"shape": "String", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency of the request.</p>", "idempotencyToken": true}, "tags": {"shape": "TagMap", "documentation": "<p>Metadata that assists with categorization and organization. Each tag consists of a key and an optional value. You define both. Tags don't propagate to any other cluster or Amazon Web Services resources.</p>"}}}, "CreateFargateProfileResponse": {"type": "structure", "members": {"fargateProfile": {"shape": "FargateProfile", "documentation": "<p>The full description of your new Fargate profile.</p>"}}}, "CreateNodegroupRequest": {"type": "structure", "required": ["clusterName", "nodegroupName", "subnets", "nodeRole"], "members": {"clusterName": {"shape": "String", "documentation": "<p>The name of your cluster.</p>", "location": "uri", "locationName": "name"}, "nodegroupName": {"shape": "String", "documentation": "<p>The unique name to give your node group.</p>"}, "scalingConfig": {"shape": "NodegroupScalingConfig", "documentation": "<p>The scaling configuration details for the Auto Scaling group that is created for your node group.</p>"}, "diskSize": {"shape": "BoxedInteger", "documentation": "<p>The root device disk size (in GiB) for your node group instances. The default disk size is 20 GiB for Linux and Bottlerocket. The default disk size is 50 GiB for Windows. If you specify <code>launchTemplate</code>, then don't specify <code>diskSize</code>, or the node group deployment will fail. For more information about using launch templates with Amazon EKS, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/launch-templates.html\">Customizing managed nodes with launch templates</a> in the <i>Amazon EKS User Guide</i>.</p>"}, "subnets": {"shape": "StringList", "documentation": "<p>The subnets to use for the Auto Scaling group that is created for your node group. If you specify <code>launchTemplate</code>, then don't specify <code> <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/APIReference/API_CreateNetworkInterface.html\">SubnetId</a> </code> in your launch template, or the node group deployment will fail. For more information about using launch templates with Amazon EKS, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/launch-templates.html\">Customizing managed nodes with launch templates</a> in the <i>Amazon EKS User Guide</i>.</p>"}, "instanceTypes": {"shape": "StringList", "documentation": "<p>Specify the instance types for a node group. If you specify a GPU instance type, make sure to also specify an applicable GPU AMI type with the <code>amiType</code> parameter. If you specify <code>launchTemplate</code>, then you can specify zero or one instance type in your launch template <i>or</i> you can specify 0-20 instance types for <code>instanceTypes</code>. If however, you specify an instance type in your launch template <i>and</i> specify any <code>instanceTypes</code>, the node group deployment will fail. If you don't specify an instance type in a launch template or for <code>instanceTypes</code>, then <code>t3.medium</code> is used, by default. If you specify <code>Spot</code> for <code>capacityType</code>, then we recommend specifying multiple values for <code>instanceTypes</code>. For more information, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/managed-node-groups.html#managed-node-group-capacity-types\">Managed node group capacity types</a> and <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/launch-templates.html\">Customizing managed nodes with launch templates</a> in the <i>Amazon EKS User Guide</i>.</p>"}, "amiType": {"shape": "AMITypes", "documentation": "<p>The AMI type for your node group. If you specify <code>launchTemplate</code>, and your launch template uses a custom AMI, then don't specify <code>amiType</code>, or the node group deployment will fail. If your launch template uses a Windows custom AMI, then add <code>eks:kube-proxy-windows</code> to your Windows nodes <code>rolearn</code> in the <code>aws-auth</code> <code>ConfigMap</code>. For more information about using launch templates with Amazon EKS, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/launch-templates.html\">Customizing managed nodes with launch templates</a> in the <i>Amazon EKS User Guide</i>.</p>"}, "remoteAccess": {"shape": "RemoteAccessConfig", "documentation": "<p>The remote access configuration to use with your node group. For Linux, the protocol is SSH. For Windows, the protocol is RDP. If you specify <code>launchTemplate</code>, then don't specify <code>remoteAccess</code>, or the node group deployment will fail. For more information about using launch templates with Amazon EKS, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/launch-templates.html\">Customizing managed nodes with launch templates</a> in the <i>Amazon EKS User Guide</i>.</p>"}, "nodeRole": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role to associate with your node group. The Amazon EKS worker node <code>kubelet</code> daemon makes calls to Amazon Web Services APIs on your behalf. Nodes receive permissions for these API calls through an IAM instance profile and associated policies. Before you can launch nodes and register them into a cluster, you must create an IAM role for those nodes to use when they are launched. For more information, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/create-node-role.html\">Amazon EKS node IAM role</a> in the <i> <i>Amazon EKS User Guide</i> </i>. If you specify <code>launchTemplate</code>, then don't specify <code> <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/APIReference/API_IamInstanceProfile.html\">IamInstanceProfile</a> </code> in your launch template, or the node group deployment will fail. For more information about using launch templates with Amazon EKS, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/launch-templates.html\">Customizing managed nodes with launch templates</a> in the <i>Amazon EKS User Guide</i>.</p>"}, "labels": {"shape": "labelsMap", "documentation": "<p>The Kubernetes <code>labels</code> to apply to the nodes in the node group when they are created.</p>"}, "taints": {"shape": "taintsList", "documentation": "<p>The Kubernetes taints to be applied to the nodes in the node group. For more information, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/node-taints-managed-node-groups.html\">Node taints on managed node groups</a>.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>Metadata that assists with categorization and organization. Each tag consists of a key and an optional value. You define both. Tags don't propagate to any other cluster or Amazon Web Services resources.</p>"}, "clientRequestToken": {"shape": "String", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency of the request.</p>", "idempotencyToken": true}, "launchTemplate": {"shape": "LaunchTemplateSpecification", "documentation": "<p>An object representing a node group's launch template specification. When using this object, don't directly specify <code>instanceTypes</code>, <code>diskSize</code>, or <code>remoteAccess</code>. You cannot later specify a different launch template ID or name than what was used to create the node group.</p> <p>Make sure that the launch template meets the requirements in <code>launchTemplateSpecification</code>. Also refer to <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/launch-templates.html\">Customizing managed nodes with launch templates</a> in the <i>Amazon EKS User Guide</i>.</p>"}, "updateConfig": {"shape": "NodegroupUpdateConfig", "documentation": "<p>The node group update configuration.</p>"}, "nodeRepairConfig": {"shape": "NodeRepairConfig", "documentation": "<p>The node auto repair configuration for the node group.</p>"}, "capacityType": {"shape": "CapacityTypes", "documentation": "<p>The capacity type for your node group.</p>"}, "version": {"shape": "String", "documentation": "<p>The Kubernetes version to use for your managed nodes. By default, the Kubernetes version of the cluster is used, and this is the only accepted specified value. If you specify <code>launchTemplate</code>, and your launch template uses a custom AMI, then don't specify <code>version</code>, or the node group deployment will fail. For more information about using launch templates with Amazon EKS, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/launch-templates.html\">Customizing managed nodes with launch templates</a> in the <i>Amazon EKS User Guide</i>.</p>"}, "releaseVersion": {"shape": "String", "documentation": "<p>The AMI version of the Amazon EKS optimized AMI to use with your node group. By default, the latest available AMI version for the node group's current Kubernetes version is used. For information about Linux versions, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/eks-linux-ami-versions.html\">Amazon EKS optimized Amazon Linux AMI versions</a> in the <i>Amazon EKS User Guide</i>. Amazon EKS managed node groups support the November 2022 and later releases of the Windows AMIs. For information about Windows versions, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/eks-ami-versions-windows.html\">Amazon EKS optimized Windows AMI versions</a> in the <i>Amazon EKS User Guide</i>.</p> <p>If you specify <code>launchTemplate</code>, and your launch template uses a custom AMI, then don't specify <code>releaseVersion</code>, or the node group deployment will fail. For more information about using launch templates with Amazon EKS, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/launch-templates.html\">Customizing managed nodes with launch templates</a> in the <i>Amazon EKS User Guide</i>.</p>"}}}, "CreateNodegroupResponse": {"type": "structure", "members": {"nodegroup": {"shape": "Nodegroup", "documentation": "<p>The full description of your new node group.</p>"}}}, "CreatePodIdentityAssociationRequest": {"type": "structure", "required": ["clusterName", "namespace", "serviceAccount", "roleArn"], "members": {"clusterName": {"shape": "String", "documentation": "<p>The name of the cluster to create the EKS Pod Identity association in.</p>", "location": "uri", "locationName": "name"}, "namespace": {"shape": "String", "documentation": "<p>The name of the Kubernetes namespace inside the cluster to create the EKS Pod Identity association in. The service account and the Pods that use the service account must be in this namespace.</p>"}, "serviceAccount": {"shape": "String", "documentation": "<p>The name of the Kubernetes service account inside the cluster to associate the IAM credentials with.</p>"}, "roleArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role to associate with the service account. The EKS Pod Identity agent manages credentials to assume this role for applications in the containers in the Pods that use this service account.</p>"}, "clientRequestToken": {"shape": "String", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency of the request.</p>", "idempotencyToken": true}, "tags": {"shape": "TagMap", "documentation": "<p>Metadata that assists with categorization and organization. Each tag consists of a key and an optional value. You define both. Tags don't propagate to any other cluster or Amazon Web Services resources.</p> <p>The following basic restrictions apply to tags:</p> <ul> <li> <p>Maximum number of tags per resource – 50</p> </li> <li> <p>For each resource, each tag key must be unique, and each tag key can have only one value.</p> </li> <li> <p>Maximum key length – 128 Unicode characters in UTF-8</p> </li> <li> <p>Maximum value length – 256 Unicode characters in UTF-8</p> </li> <li> <p>If your tagging schema is used across multiple services and resources, remember that other services may have restrictions on allowed characters. Generally allowed characters are: letters, numbers, and spaces representable in UTF-8, and the following characters: + - = . _ : / @.</p> </li> <li> <p>Tag keys and values are case-sensitive.</p> </li> <li> <p>Do not use <code>aws:</code>, <code>AWS:</code>, or any upper or lowercase combination of such as a prefix for either keys or values as it is reserved for Amazon Web Services use. You cannot edit or delete tag keys or values with this prefix. Tags with this prefix do not count against your tags per resource limit.</p> </li> </ul>"}, "disableSessionTags": {"shape": "BoxedBoolean", "documentation": "<p>Disable the automatic sessions tags that are appended by EKS Pod Identity.</p> <p>EKS Pod Identity adds a pre-defined set of session tags when it assumes the role. You can use these tags to author a single role that can work across resources by allowing access to Amazon Web Services resources based on matching tags. By default, EKS Pod Identity attaches six tags, including tags for cluster name, namespace, and service account name. For the list of tags added by EKS Pod Identity, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/pod-id-abac.html#pod-id-abac-tags\">List of session tags added by EKS Pod Identity</a> in the <i>Amazon EKS User Guide</i>.</p> <p>Amazon Web Services compresses inline session policies, managed policy ARNs, and session tags into a packed binary format that has a separate limit. If you receive a <code>PackedPolicyTooLarge</code> error indicating the packed binary format has exceeded the size limit, you can attempt to reduce the size by disabling the session tags added by EKS Pod Identity.</p>"}, "targetRoleArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the target IAM role to associate with the service account. This role is assumed by using the EKS Pod Identity association role, then the credentials for this role are injected into the Pod.</p> <p>When you run applications on Amazon EKS, your application might need to access Amazon Web Services resources from a different role that exists in the same or different Amazon Web Services account. For example, your application running in “Account A” might need to access resources, such as Amazon S3 buckets in “Account B” or within “Account A” itself. You can create a association to access Amazon Web Services resources in “Account B” by creating two IAM roles: a role in “Account A” and a role in “Account B” (which can be the same or different account), each with the necessary trust and permission policies. After you provide these roles in the <i>IAM role</i> and <i>Target IAM role</i> fields, EKS will perform role chaining to ensure your application gets the required permissions. This means Role A will assume Role B, allowing your Pods to securely access resources like S3 buckets in the target account.</p>"}}}, "CreatePodIdentityAssociationResponse": {"type": "structure", "members": {"association": {"shape": "PodIdentityAssociation", "documentation": "<p>The full description of your new association.</p> <p>The description includes an ID for the association. Use the ID of the association in further actions to manage the association.</p>"}}}, "DeleteAccessEntryRequest": {"type": "structure", "required": ["clusterName", "principalArn"], "members": {"clusterName": {"shape": "String", "documentation": "<p>The name of your cluster.</p>", "location": "uri", "locationName": "name"}, "principalArn": {"shape": "String", "documentation": "<p>The ARN of the IAM principal for the <code>AccessEntry</code>.</p>", "location": "uri", "locationName": "principalArn"}}}, "DeleteAccessEntryResponse": {"type": "structure", "members": {}}, "DeleteAddonRequest": {"type": "structure", "required": ["clusterName", "addonName"], "members": {"clusterName": {"shape": "ClusterName", "documentation": "<p>The name of your cluster.</p>", "location": "uri", "locationName": "name"}, "addonName": {"shape": "String", "documentation": "<p>The name of the add-on. The name must match one of the names returned by <a href=\"https://docs.aws.amazon.com/eks/latest/APIReference/API_ListAddons.html\"> <code>ListAddons</code> </a>.</p>", "location": "uri", "locationName": "addonName"}, "preserve": {"shape": "Boolean", "documentation": "<p>Specifying this option preserves the add-on software on your cluster but Amazon EKS stops managing any settings for the add-on. If an IAM account is associated with the add-on, it isn't removed.</p>", "location": "querystring", "locationName": "preserve"}}}, "DeleteAddonResponse": {"type": "structure", "members": {"addon": {"shape": "<PERSON><PERSON>"}}}, "DeleteClusterRequest": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "String", "documentation": "<p>The name of the cluster to delete.</p>", "location": "uri", "locationName": "name"}}}, "DeleteClusterResponse": {"type": "structure", "members": {"cluster": {"shape": "Cluster", "documentation": "<p>The full description of the cluster to delete.</p>"}}}, "DeleteEksAnywhereSubscriptionRequest": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "String", "documentation": "<p>The ID of the subscription.</p>", "location": "uri", "locationName": "id"}}}, "DeleteEksAnywhereSubscriptionResponse": {"type": "structure", "members": {"subscription": {"shape": "EksAnywhereSubscription", "documentation": "<p>The full description of the subscription to be deleted.</p>"}}}, "DeleteFargateProfileRequest": {"type": "structure", "required": ["clusterName", "fargateProfileName"], "members": {"clusterName": {"shape": "String", "documentation": "<p>The name of your cluster.</p>", "location": "uri", "locationName": "name"}, "fargateProfileName": {"shape": "String", "documentation": "<p>The name of the Fargate profile to delete.</p>", "location": "uri", "locationName": "fargateProfileName"}}}, "DeleteFargateProfileResponse": {"type": "structure", "members": {"fargateProfile": {"shape": "FargateProfile", "documentation": "<p>The deleted Fargate profile.</p>"}}}, "DeleteNodegroupRequest": {"type": "structure", "required": ["clusterName", "nodegroupName"], "members": {"clusterName": {"shape": "String", "documentation": "<p>The name of your cluster.</p>", "location": "uri", "locationName": "name"}, "nodegroupName": {"shape": "String", "documentation": "<p>The name of the node group to delete.</p>", "location": "uri", "locationName": "nodegroupName"}}}, "DeleteNodegroupResponse": {"type": "structure", "members": {"nodegroup": {"shape": "Nodegroup", "documentation": "<p>The full description of your deleted node group.</p>"}}}, "DeletePodIdentityAssociationRequest": {"type": "structure", "required": ["clusterName", "associationId"], "members": {"clusterName": {"shape": "String", "documentation": "<p>The cluster name that</p>", "location": "uri", "locationName": "name"}, "associationId": {"shape": "String", "documentation": "<p>The ID of the association to be deleted.</p>", "location": "uri", "locationName": "associationId"}}}, "DeletePodIdentityAssociationResponse": {"type": "structure", "members": {"association": {"shape": "PodIdentityAssociation", "documentation": "<p>The full description of the EKS Pod Identity association that was deleted.</p>"}}}, "DeprecationDetail": {"type": "structure", "members": {"usage": {"shape": "String", "documentation": "<p>The deprecated version of the resource.</p>"}, "replacedWith": {"shape": "String", "documentation": "<p>The newer version of the resource to migrate to if applicable. </p>"}, "stopServingVersion": {"shape": "String", "documentation": "<p>The version of the software where the deprecated resource version will stop being served.</p>"}, "startServingReplacementVersion": {"shape": "String", "documentation": "<p>The version of the software where the newer resource version became available to migrate to if applicable.</p>"}, "clientStats": {"shape": "ClientStats", "documentation": "<p>Details about Kubernetes clients using the deprecated resources.</p>"}}, "documentation": "<p>The summary information about deprecated resource usage for an insight check in the <code>UPGRADE_READINESS</code> category.</p>"}, "DeprecationDetails": {"type": "list", "member": {"shape": "DeprecationDetail"}}, "DeregisterClusterRequest": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "String", "documentation": "<p>The name of the connected cluster to deregister.</p>", "location": "uri", "locationName": "name"}}}, "DeregisterClusterResponse": {"type": "structure", "members": {"cluster": {"shape": "Cluster"}}}, "DescribeAccessEntryRequest": {"type": "structure", "required": ["clusterName", "principalArn"], "members": {"clusterName": {"shape": "String", "documentation": "<p>The name of your cluster.</p>", "location": "uri", "locationName": "name"}, "principalArn": {"shape": "String", "documentation": "<p>The ARN of the IAM principal for the <code>AccessEntry</code>.</p>", "location": "uri", "locationName": "principalArn"}}}, "DescribeAccessEntryResponse": {"type": "structure", "members": {"accessEntry": {"shape": "AccessEntry", "documentation": "<p>Information about the access entry.</p>"}}}, "DescribeAddonConfigurationRequest": {"type": "structure", "required": ["addonName", "addonVersion"], "members": {"addonName": {"shape": "String", "documentation": "<p>The name of the add-on. The name must match one of the names returned by <code>DescribeAddonVersions</code>.</p>", "location": "querystring", "locationName": "addonName"}, "addonVersion": {"shape": "String", "documentation": "<p>The version of the add-on. The version must match one of the versions returned by <a href=\"https://docs.aws.amazon.com/eks/latest/APIReference/API_DescribeAddonVersions.html\"> <code>DescribeAddonVersions</code> </a>.</p>", "location": "querystring", "locationName": "addonVersion"}}}, "DescribeAddonConfigurationResponse": {"type": "structure", "members": {"addonName": {"shape": "String", "documentation": "<p>The name of the add-on.</p>"}, "addonVersion": {"shape": "String", "documentation": "<p>The version of the add-on. The version must match one of the versions returned by <a href=\"https://docs.aws.amazon.com/eks/latest/APIReference/API_DescribeAddonVersions.html\"> <code>DescribeAddonVersions</code> </a>.</p>"}, "configurationSchema": {"shape": "String", "documentation": "<p>A JSON schema that's used to validate the configuration values you provide when an add-on is created or updated.</p>"}, "podIdentityConfiguration": {"shape": "AddonPodIdentityConfigurationList", "documentation": "<p>The Kubernetes service account name used by the add-on, and any suggested IAM policies. Use this information to create an IAM Role for the add-on.</p>"}}}, "DescribeAddonRequest": {"type": "structure", "required": ["clusterName", "addonName"], "members": {"clusterName": {"shape": "ClusterName", "documentation": "<p>The name of your cluster.</p>", "location": "uri", "locationName": "name"}, "addonName": {"shape": "String", "documentation": "<p>The name of the add-on. The name must match one of the names returned by <a href=\"https://docs.aws.amazon.com/eks/latest/APIReference/API_ListAddons.html\"> <code>ListAddons</code> </a>.</p>", "location": "uri", "locationName": "addonName"}}}, "DescribeAddonResponse": {"type": "structure", "members": {"addon": {"shape": "<PERSON><PERSON>"}}}, "DescribeAddonVersionsRequest": {"type": "structure", "members": {"kubernetesVersion": {"shape": "String", "documentation": "<p>The Kubernetes versions that you can use the add-on with.</p>", "location": "querystring", "locationName": "kubernetesVersion"}, "maxResults": {"shape": "DescribeAddonVersionsRequestMaxResults", "documentation": "<p>The maximum number of results, returned in paginated output. You receive <code>maxResults</code> in a single page, along with a <code>nextToken</code> response element. You can see the remaining results of the initial request by sending another request with the returned <code>nextToken</code> value. This value can be between 1 and 100. If you don't use this parameter, 100 results and a <code>nextToken</code> value, if applicable, are returned.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "String", "documentation": "<p>The <code>nextToken</code> value returned from a previous paginated request, where <code>maxResults</code> was used and the results exceeded the value of that parameter. Pagination continues from the end of the previous results that returned the <code>nextToken</code> value. This value is null when there are no more results to return.</p> <note> <p>This token should be treated as an opaque identifier that is used only to retrieve the next items in a list and not for other programmatic purposes.</p> </note>", "location": "querystring", "locationName": "nextToken"}, "addonName": {"shape": "String", "documentation": "<p>The name of the add-on. The name must match one of the names returned by <a href=\"https://docs.aws.amazon.com/eks/latest/APIReference/API_ListAddons.html\"> <code>ListAddons</code> </a>.</p>", "location": "querystring", "locationName": "addonName"}, "types": {"shape": "StringList", "documentation": "<p>The type of the add-on. For valid <code>types</code>, don't specify a value for this property.</p>", "location": "querystring", "locationName": "types"}, "publishers": {"shape": "StringList", "documentation": "<p>The publisher of the add-on. For valid <code>publishers</code>, don't specify a value for this property.</p>", "location": "querystring", "locationName": "publishers"}, "owners": {"shape": "StringList", "documentation": "<p>The owner of the add-on. For valid <code>owners</code>, don't specify a value for this property.</p>", "location": "querystring", "locationName": "owners"}}}, "DescribeAddonVersionsRequestMaxResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "DescribeAddonVersionsResponse": {"type": "structure", "members": {"addons": {"shape": "Addons", "documentation": "<p>The list of available versions with Kubernetes version compatibility and other properties.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>The <code>nextToken</code> value to include in a future <code>DescribeAddonVersions</code> request. When the results of a <code>DescribeAddonVersions</code> request exceed <code>maxResults</code>, you can use this value to retrieve the next page of results. This value is <code>null</code> when there are no more results to return.</p> <note> <p>This token should be treated as an opaque identifier that is used only to retrieve the next items in a list and not for other programmatic purposes.</p> </note>"}}}, "DescribeClusterRequest": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "String", "documentation": "<p>The name of your cluster.</p>", "location": "uri", "locationName": "name"}}}, "DescribeClusterResponse": {"type": "structure", "members": {"cluster": {"shape": "Cluster", "documentation": "<p>The full description of your specified cluster.</p>"}}}, "DescribeClusterVersionMaxResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "DescribeClusterVersionsRequest": {"type": "structure", "members": {"clusterType": {"shape": "String", "documentation": "<p>The type of cluster to filter versions by.</p>", "location": "querystring", "locationName": "clusterType"}, "maxResults": {"shape": "DescribeClusterVersionMaxResults", "documentation": "<p>Maximum number of results to return.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "String", "documentation": "<p>Pagination token for the next set of results.</p>", "location": "querystring", "locationName": "nextToken"}, "defaultOnly": {"shape": "BoxedBoolean", "documentation": "<p>Filter to show only default versions.</p>", "location": "querystring", "locationName": "defaultOnly"}, "includeAll": {"shape": "BoxedBoolean", "documentation": "<p>Include all available versions in the response.</p>", "location": "querystring", "locationName": "includeAll"}, "clusterVersions": {"shape": "StringList", "documentation": "<p>List of specific cluster versions to describe.</p>", "location": "querystring", "locationName": "clusterVersions"}, "status": {"shape": "ClusterVersionStatus", "documentation": "<important> <p>This field is deprecated. Use <code>versionStatus</code> instead, as that field matches for input and output of this action.</p> </important> <p>Filter versions by their current status.</p>", "deprecated": true, "deprecatedMessage": "status has been replaced by versionStatus", "deprecatedSince": "2025-02-15", "location": "querystring", "locationName": "status"}, "versionStatus": {"shape": "VersionStatus", "documentation": "<p>Filter versions by their current status.</p>", "location": "querystring", "locationName": "versionStatus"}}}, "DescribeClusterVersionsResponse": {"type": "structure", "members": {"nextToken": {"shape": "String", "documentation": "<p>Pagination token for the next set of results.</p>"}, "clusterVersions": {"shape": "ClusterVersionList", "documentation": "<p>List of cluster version information objects.</p>"}}}, "DescribeEksAnywhereSubscriptionRequest": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "String", "documentation": "<p>The ID of the subscription.</p>", "location": "uri", "locationName": "id"}}}, "DescribeEksAnywhereSubscriptionResponse": {"type": "structure", "members": {"subscription": {"shape": "EksAnywhereSubscription", "documentation": "<p>The full description of the subscription.</p>"}}}, "DescribeFargateProfileRequest": {"type": "structure", "required": ["clusterName", "fargateProfileName"], "members": {"clusterName": {"shape": "String", "documentation": "<p>The name of your cluster.</p>", "location": "uri", "locationName": "name"}, "fargateProfileName": {"shape": "String", "documentation": "<p>The name of the Fargate profile to describe.</p>", "location": "uri", "locationName": "fargateProfileName"}}}, "DescribeFargateProfileResponse": {"type": "structure", "members": {"fargateProfile": {"shape": "FargateProfile", "documentation": "<p>The full description of your Fargate profile.</p>"}}}, "DescribeIdentityProviderConfigRequest": {"type": "structure", "required": ["clusterName", "identityProviderConfig"], "members": {"clusterName": {"shape": "String", "documentation": "<p>The name of your cluster.</p>", "location": "uri", "locationName": "name"}, "identityProviderConfig": {"shape": "IdentityProviderConfig", "documentation": "<p>An object representing an identity provider configuration.</p>"}}}, "DescribeIdentityProviderConfigResponse": {"type": "structure", "members": {"identityProviderConfig": {"shape": "IdentityProviderConfigResponse", "documentation": "<p>The object that represents an OpenID Connect (OIDC) identity provider configuration.</p>"}}}, "DescribeInsightRequest": {"type": "structure", "required": ["clusterName", "id"], "members": {"clusterName": {"shape": "String", "documentation": "<p>The name of the cluster to describe the insight for.</p>", "location": "uri", "locationName": "name"}, "id": {"shape": "String", "documentation": "<p>The identity of the insight to describe.</p>", "location": "uri", "locationName": "id"}}}, "DescribeInsightResponse": {"type": "structure", "members": {"insight": {"shape": "Insight", "documentation": "<p>The full description of the insight.</p>"}}}, "DescribeNodegroupRequest": {"type": "structure", "required": ["clusterName", "nodegroupName"], "members": {"clusterName": {"shape": "String", "documentation": "<p>The name of your cluster.</p>", "location": "uri", "locationName": "name"}, "nodegroupName": {"shape": "String", "documentation": "<p>The name of the node group to describe.</p>", "location": "uri", "locationName": "nodegroupName"}}}, "DescribeNodegroupResponse": {"type": "structure", "members": {"nodegroup": {"shape": "Nodegroup", "documentation": "<p>The full description of your node group.</p>"}}}, "DescribePodIdentityAssociationRequest": {"type": "structure", "required": ["clusterName", "associationId"], "members": {"clusterName": {"shape": "String", "documentation": "<p>The name of the cluster that the association is in.</p>", "location": "uri", "locationName": "name"}, "associationId": {"shape": "String", "documentation": "<p>The ID of the association that you want the description of.</p>", "location": "uri", "locationName": "associationId"}}}, "DescribePodIdentityAssociationResponse": {"type": "structure", "members": {"association": {"shape": "PodIdentityAssociation", "documentation": "<p>The full description of the EKS Pod Identity association.</p>"}}}, "DescribeUpdateRequest": {"type": "structure", "required": ["name", "updateId"], "members": {"name": {"shape": "String", "documentation": "<p>The name of the Amazon EKS cluster associated with the update.</p>", "location": "uri", "locationName": "name"}, "updateId": {"shape": "String", "documentation": "<p>The ID of the update to describe.</p>", "location": "uri", "locationName": "updateId"}, "nodegroupName": {"shape": "String", "documentation": "<p>The name of the Amazon EKS node group associated with the update. This parameter is required if the update is a node group update.</p>", "location": "querystring", "locationName": "nodegroupName"}, "addonName": {"shape": "String", "documentation": "<p>The name of the add-on. The name must match one of the names returned by <a href=\"https://docs.aws.amazon.com/eks/latest/APIReference/API_ListAddons.html\"> <code>ListAddons</code> </a>. This parameter is required if the update is an add-on update.</p>", "location": "querystring", "locationName": "addonName"}}, "documentation": "<p>Describes an update request.</p>"}, "DescribeUpdateResponse": {"type": "structure", "members": {"update": {"shape": "Update", "documentation": "<p>The full description of the specified update.</p>"}}}, "DisassociateAccessPolicyRequest": {"type": "structure", "required": ["clusterName", "principalArn", "policyArn"], "members": {"clusterName": {"shape": "String", "documentation": "<p>The name of your cluster.</p>", "location": "uri", "locationName": "name"}, "principalArn": {"shape": "String", "documentation": "<p>The ARN of the IAM principal for the <code>AccessEntry</code>.</p>", "location": "uri", "locationName": "principalArn"}, "policyArn": {"shape": "String", "documentation": "<p>The ARN of the policy to disassociate from the access entry. For a list of associated policies ARNs, use <code>ListAssociatedAccessPolicies</code>.</p>", "location": "uri", "locationName": "policyArn"}}}, "DisassociateAccessPolicyResponse": {"type": "structure", "members": {}}, "DisassociateIdentityProviderConfigRequest": {"type": "structure", "required": ["clusterName", "identityProviderConfig"], "members": {"clusterName": {"shape": "String", "documentation": "<p>The name of your cluster.</p>", "location": "uri", "locationName": "name"}, "identityProviderConfig": {"shape": "IdentityProviderConfig", "documentation": "<p>An object representing an identity provider configuration.</p>"}, "clientRequestToken": {"shape": "String", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency of the request.</p>", "idempotencyToken": true}}}, "DisassociateIdentityProviderConfigResponse": {"type": "structure", "members": {"update": {"shape": "Update"}}}, "EksAnywhereSubscription": {"type": "structure", "members": {"id": {"shape": "String", "documentation": "<p>UUID identifying a subscription.</p>"}, "arn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) for the subscription.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp in seconds for when the subscription was created.</p>"}, "effectiveDate": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp in seconds for when the subscription is effective.</p>"}, "expirationDate": {"shape": "Timestamp", "documentation": "<p>The Unix timestamp in seconds for when the subscription will expire or auto renew, depending on the auto renew configuration of the subscription object.</p>"}, "licenseQuantity": {"shape": "Integer", "documentation": "<p>The number of licenses included in a subscription. Valid values are between 1 and 100.</p>"}, "licenseType": {"shape": "EksAnywhereSubscriptionLicenseType", "documentation": "<p>The type of licenses included in the subscription. Valid value is CLUSTER. With the CLUSTER license type, each license covers support for a single EKS Anywhere cluster.</p>"}, "term": {"shape": "EksAnywhereSubscriptionTerm", "documentation": "<p>An EksAnywhereSubscriptionTerm object. </p>"}, "status": {"shape": "String", "documentation": "<p>The status of a subscription.</p>"}, "autoRenew": {"shape": "Boolean", "documentation": "<p>A boolean indicating whether or not a subscription will auto renew when it expires.</p>"}, "licenseArns": {"shape": "StringList", "documentation": "<p>Amazon Web Services License Manager ARN associated with the subscription.</p>"}, "licenses": {"shape": "LicenseList", "documentation": "<p>Includes all of the claims in the license token necessary to validate the license for extended support.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The metadata for a subscription to assist with categorization and organization. Each tag consists of a key and an optional value. Subscription tags do not propagate to any other resources associated with the subscription.</p>"}}, "documentation": "<p>An EKS Anywhere subscription authorizing the customer to support for licensed clusters and access to EKS Anywhere Curated Packages.</p>"}, "EksAnywhereSubscriptionLicenseType": {"type": "string", "enum": ["Cluster"]}, "EksAnywhereSubscriptionList": {"type": "list", "member": {"shape": "EksAnywhereSubscription"}}, "EksAnywhereSubscriptionName": {"type": "string", "max": 100, "min": 1, "pattern": "^[0-9A-Za-z][A-Za-z0-9\\-_]*"}, "EksAnywhereSubscriptionStatus": {"type": "string", "enum": ["CREATING", "ACTIVE", "UPDATING", "EXPIRING", "EXPIRED", "DELETING"]}, "EksAnywhereSubscriptionStatusValues": {"type": "list", "member": {"shape": "EksAnywhereSubscriptionStatus"}}, "EksAnywhereSubscriptionTerm": {"type": "structure", "members": {"duration": {"shape": "Integer", "documentation": "<p>The duration of the subscription term. Valid values are 12 and 36, indicating a 12 month or 36 month subscription.</p>"}, "unit": {"shape": "EksAnywhereSubscriptionTermUnit", "documentation": "<p>The term unit of the subscription. Valid value is <code>MONTHS</code>.</p>"}}, "documentation": "<p>An object representing the term duration and term unit type of your subscription. This determines the term length of your subscription. Valid values are MONTHS for term unit and 12 or 36 for term duration, indicating a 12 month or 36 month subscription.</p>"}, "EksAnywhereSubscriptionTermUnit": {"type": "string", "enum": ["MONTHS"]}, "ElasticLoadBalancing": {"type": "structure", "members": {"enabled": {"shape": "BoxedBoolean", "documentation": "<p>Indicates if the load balancing capability is enabled on your EKS Auto Mode cluster. If the load balancing capability is enabled, EKS Auto Mode will create and delete load balancers in your Amazon Web Services account.</p>"}}, "documentation": "<p>Indicates the current configuration of the load balancing capability on your EKS Auto Mode cluster. For example, if the capability is enabled or disabled. For more information, see EKS Auto Mode load balancing capability in the <i>Amazon EKS User Guide</i>.</p>"}, "EncryptionConfig": {"type": "structure", "members": {"resources": {"shape": "StringList", "documentation": "<p>Specifies the resources to be encrypted. The only supported value is <code>secrets</code>.</p>"}, "provider": {"shape": "Provider", "documentation": "<p>Key Management Service (KMS) key. Either the ARN or the alias can be used.</p>"}}, "documentation": "<p>The encryption configuration for the cluster.</p>"}, "EncryptionConfigList": {"type": "list", "member": {"shape": "EncryptionConfig"}, "max": 1}, "ErrorCode": {"type": "string", "enum": ["SubnetNotFound", "SecurityGroupNotFound", "EniLimitReached", "IpNotAvailable", "AccessDenied", "OperationNotPermitted", "VpcIdNotFound", "Unknown", "NodeCreationFailure", "PodEvictionFailure", "InsufficientFreeAddresses", "ClusterUnreachable", "InsufficientNumberOfReplicas", "ConfigurationConflict", "AdmissionRequestDenied", "UnsupportedAddonModification", "K8sResourceNotFound"]}, "ErrorDetail": {"type": "structure", "members": {"errorCode": {"shape": "ErrorCode", "documentation": "<p>A brief description of the error. </p> <ul> <li> <p> <b>SubnetNotFound</b>: We couldn't find one of the subnets associated with the cluster.</p> </li> <li> <p> <b>SecurityGroupNotFound</b>: We couldn't find one of the security groups associated with the cluster.</p> </li> <li> <p> <b>EniLimitReached</b>: You have reached the elastic network interface limit for your account.</p> </li> <li> <p> <b>IpNotAvailable</b>: A subnet associated with the cluster doesn't have any available IP addresses.</p> </li> <li> <p> <b>AccessDenied</b>: You don't have permissions to perform the specified operation.</p> </li> <li> <p> <b>OperationNotPermitted</b>: The service role associated with the cluster doesn't have the required access permissions for Amazon EKS.</p> </li> <li> <p> <b>VpcIdNotFound</b>: We couldn't find the VPC associated with the cluster.</p> </li> </ul>"}, "errorMessage": {"shape": "String", "documentation": "<p>A more complete description of the error.</p>"}, "resourceIds": {"shape": "StringList", "documentation": "<p>An optional field that contains the resource IDs associated with the error.</p>"}}, "documentation": "<p>An object representing an error when an asynchronous operation fails.</p>"}, "ErrorDetails": {"type": "list", "member": {"shape": "ErrorDetail"}}, "FargateProfile": {"type": "structure", "members": {"fargateProfileName": {"shape": "String", "documentation": "<p>The name of the Fargate profile.</p>"}, "fargateProfileArn": {"shape": "String", "documentation": "<p>The full Amazon Resource Name (ARN) of the Fargate profile.</p>"}, "clusterName": {"shape": "String", "documentation": "<p>The name of your cluster.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The Unix epoch timestamp at object creation.</p>"}, "podExecutionRoleArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the <code>Pod</code> execution role to use for any <code>Pod</code> that matches the selectors in the Fargate profile. For more information, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/pod-execution-role.html\"> <code>Pod</code> execution role</a> in the <i>Amazon EKS User Guide</i>.</p>"}, "subnets": {"shape": "StringList", "documentation": "<p>The IDs of subnets to launch a <code>Pod</code> into.</p>"}, "selectors": {"shape": "FargateProfileSelectors", "documentation": "<p>The selectors to match for a <code>Pod</code> to use this Fargate profile.</p>"}, "status": {"shape": "FargateProfileStatus", "documentation": "<p>The current status of the Fargate profile.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>Metadata that assists with categorization and organization. Each tag consists of a key and an optional value. You define both. Tags don't propagate to any other cluster or Amazon Web Services resources.</p>"}, "health": {"shape": "FargateProfileHealth", "documentation": "<p>The health status of the Fargate profile. If there are issues with your Fargate profile's health, they are listed here.</p>"}}, "documentation": "<p>An object representing an Fargate profile.</p>"}, "FargateProfileHealth": {"type": "structure", "members": {"issues": {"shape": "FargateProfileIssueList", "documentation": "<p>Any issues that are associated with the Fargate profile.</p>"}}, "documentation": "<p>The health status of the Fargate profile. If there are issues with your Fargate profile's health, they are listed here.</p>"}, "FargateProfileIssue": {"type": "structure", "members": {"code": {"shape": "FargateProfileIssueCode", "documentation": "<p>A brief description of the error.</p>"}, "message": {"shape": "String", "documentation": "<p>The error message associated with the issue.</p>"}, "resourceIds": {"shape": "StringList", "documentation": "<p>The Amazon Web Services resources that are affected by this issue.</p>"}}, "documentation": "<p>An issue that is associated with the Fargate profile.</p>"}, "FargateProfileIssueCode": {"type": "string", "enum": ["PodExecutionRoleAlreadyInUse", "AccessDenied", "ClusterUnreachable", "InternalFailure"]}, "FargateProfileIssueList": {"type": "list", "member": {"shape": "FargateProfileIssue"}}, "FargateProfileLabel": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "String"}}, "FargateProfileSelector": {"type": "structure", "members": {"namespace": {"shape": "String", "documentation": "<p>The Kubernetes <code>namespace</code> that the selector should match.</p>"}, "labels": {"shape": "FargateProfileLabel", "documentation": "<p>The Kubernetes labels that the selector should match. A pod must contain all of the labels that are specified in the selector for it to be considered a match.</p>"}}, "documentation": "<p>An object representing an Fargate profile selector.</p>"}, "FargateProfileSelectors": {"type": "list", "member": {"shape": "FargateProfileSelector"}}, "FargateProfileStatus": {"type": "string", "enum": ["CREATING", "ACTIVE", "DELETING", "CREATE_FAILED", "DELETE_FAILED"]}, "FargateProfilesRequestMaxResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "Identity": {"type": "structure", "members": {"oidc": {"shape": "OIDC", "documentation": "<p>An object representing the <a href=\"https://openid.net/connect/\">OpenID Connect</a> identity provider information.</p>"}}, "documentation": "<p>An object representing an identity provider.</p>"}, "IdentityProviderConfig": {"type": "structure", "required": ["type", "name"], "members": {"type": {"shape": "String", "documentation": "<p>The type of the identity provider configuration. The only type available is <code>oidc</code>.</p>"}, "name": {"shape": "String", "documentation": "<p>The name of the identity provider configuration.</p>"}}, "documentation": "<p>An object representing an identity provider configuration.</p>"}, "IdentityProviderConfigResponse": {"type": "structure", "members": {"oidc": {"shape": "OidcIdentityProviderConfig", "documentation": "<p>An object representing an OpenID Connect (OIDC) identity provider configuration.</p>"}}, "documentation": "<p>The full description of your identity configuration.</p>"}, "IdentityProviderConfigs": {"type": "list", "member": {"shape": "IdentityProviderConfig"}}, "IncludeClustersList": {"type": "list", "member": {"shape": "String"}}, "Insight": {"type": "structure", "members": {"id": {"shape": "String", "documentation": "<p>The ID of the insight.</p>"}, "name": {"shape": "String", "documentation": "<p>The name of the insight.</p>"}, "category": {"shape": "Category", "documentation": "<p>The category of the insight.</p>"}, "kubernetesVersion": {"shape": "String", "documentation": "<p>The Kubernetes minor version associated with an insight if applicable.</p>"}, "lastRefreshTime": {"shape": "Timestamp", "documentation": "<p>The time Amazon EKS last successfully completed a refresh of this insight check on the cluster.</p>"}, "lastTransitionTime": {"shape": "Timestamp", "documentation": "<p>The time the status of the insight last changed.</p>"}, "description": {"shape": "String", "documentation": "<p>The description of the insight which includes alert criteria, remediation recommendation, and additional resources (contains Markdown).</p>"}, "insightStatus": {"shape": "InsightStatus", "documentation": "<p>An object containing more detail on the status of the insight resource.</p>"}, "recommendation": {"shape": "String", "documentation": "<p>A summary of how to remediate the finding of this insight if applicable. </p>"}, "additionalInfo": {"shape": "AdditionalInfoMap", "documentation": "<p>Links to sources that provide additional context on the insight.</p>"}, "resources": {"shape": "InsightResourceDetails", "documentation": "<p>The details about each resource listed in the insight check result.</p>"}, "categorySpecificSummary": {"shape": "InsightCategorySpecificSummary", "documentation": "<p>Summary information that relates to the category of the insight. Currently only returned with certain insights having category <code>UPGRADE_READINESS</code>.</p>"}}, "documentation": "<p>A check that provides recommendations to remedy potential upgrade-impacting issues.</p>"}, "InsightCategorySpecificSummary": {"type": "structure", "members": {"deprecationDetails": {"shape": "DeprecationDetails", "documentation": "<p>The summary information about deprecated resource usage for an insight check in the <code>UPGRADE_READINESS</code> category.</p>"}, "addonCompatibilityDetails": {"shape": "AddonCompatibilityDetails", "documentation": "<p>A list of <code>AddonCompatibilityDetail</code> objects for Amazon EKS add-ons.</p>"}}, "documentation": "<p>Summary information that relates to the category of the insight. Currently only returned with certain insights having category <code>UPGRADE_READINESS</code>.</p>"}, "InsightResourceDetail": {"type": "structure", "members": {"insightStatus": {"shape": "InsightStatus", "documentation": "<p>An object containing more detail on the status of the insight resource.</p>"}, "kubernetesResourceUri": {"shape": "String", "documentation": "<p>The Kubernetes resource URI if applicable.</p>"}, "arn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) if applicable.</p>"}}, "documentation": "<p>Returns information about the resource being evaluated.</p>"}, "InsightResourceDetails": {"type": "list", "member": {"shape": "InsightResourceDetail"}}, "InsightStatus": {"type": "structure", "members": {"status": {"shape": "InsightStatusValue", "documentation": "<p>The status of the resource.</p>"}, "reason": {"shape": "String", "documentation": "<p>Explanation on the reasoning for the status of the resource. </p>"}}, "documentation": "<p>The status of the insight.</p>"}, "InsightStatusValue": {"type": "string", "enum": ["PASSING", "WARNING", "ERROR", "UNKNOWN"]}, "InsightStatusValueList": {"type": "list", "member": {"shape": "InsightStatusValue"}}, "InsightSummaries": {"type": "list", "member": {"shape": "InsightSummary"}}, "InsightSummary": {"type": "structure", "members": {"id": {"shape": "String", "documentation": "<p>The ID of the insight.</p>"}, "name": {"shape": "String", "documentation": "<p>The name of the insight.</p>"}, "category": {"shape": "Category", "documentation": "<p>The category of the insight.</p>"}, "kubernetesVersion": {"shape": "String", "documentation": "<p>The Kubernetes minor version associated with an insight if applicable. </p>"}, "lastRefreshTime": {"shape": "Timestamp", "documentation": "<p>The time Amazon EKS last successfully completed a refresh of this insight check on the cluster.</p>"}, "lastTransitionTime": {"shape": "Timestamp", "documentation": "<p>The time the status of the insight last changed.</p>"}, "description": {"shape": "String", "documentation": "<p>The description of the insight which includes alert criteria, remediation recommendation, and additional resources (contains Markdown).</p>"}, "insightStatus": {"shape": "InsightStatus", "documentation": "<p>An object containing more detail on the status of the insight.</p>"}}, "documentation": "<p>The summarized description of the insight.</p>"}, "InsightsFilter": {"type": "structure", "members": {"categories": {"shape": "CategoryList", "documentation": "<p>The categories to use to filter insights. The following lists the available categories:</p> <ul> <li> <p> <code>UPGRADE_READINESS</code>: Amazon EKS identifies issues that could impact your ability to upgrade to new versions of Kubernetes. These are called upgrade insights.</p> </li> <li> <p> <code>MISCONFIGURATION</code>: Amazon EKS identifies misconfiguration in your EKS Hybrid Nodes setup that could impair functionality of your cluster or workloads. These are called configuration insights.</p> </li> </ul>"}, "kubernetesVersions": {"shape": "StringList", "documentation": "<p>The Kubernetes versions to use to filter the insights.</p>"}, "statuses": {"shape": "InsightStatusValueList", "documentation": "<p>The statuses to use to filter the insights. </p>"}}, "documentation": "<p>The criteria to use for the insights.</p>"}, "Integer": {"type": "integer"}, "InvalidParameterException": {"type": "structure", "members": {"clusterName": {"shape": "String", "documentation": "<p>The Amazon EKS cluster associated with the exception.</p>"}, "nodegroupName": {"shape": "String", "documentation": "<p>The Amazon EKS managed node group associated with the exception.</p>"}, "fargateProfileName": {"shape": "String", "documentation": "<p>The Fargate profile associated with the exception.</p>"}, "addonName": {"shape": "String", "documentation": "<p>The specified parameter for the add-on name is invalid. Review the available parameters for the API request</p>"}, "subscriptionId": {"shape": "String", "documentation": "<p>The Amazon EKS subscription ID with the exception.</p>"}, "message": {"shape": "String", "documentation": "<p>The specified parameter is invalid. Review the available parameters for the API request.</p>"}}, "documentation": "<p>The specified parameter is invalid. Review the available parameters for the API request.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "InvalidRequestException": {"type": "structure", "members": {"clusterName": {"shape": "String", "documentation": "<p>The Amazon EKS cluster associated with the exception.</p>"}, "nodegroupName": {"shape": "String", "documentation": "<p>The Amazon EKS managed node group associated with the exception.</p>"}, "addonName": {"shape": "String", "documentation": "<p>The request is invalid given the state of the add-on name. Check the state of the cluster and the associated operations.</p>"}, "subscriptionId": {"shape": "String", "documentation": "<p>The Amazon EKS subscription ID with the exception.</p>"}, "message": {"shape": "String", "documentation": "<p>The Amazon EKS add-on name associated with the exception.</p>"}}, "documentation": "<p>The request is invalid given the state of the cluster. Check the state of the cluster and the associated operations.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "InvalidStateException": {"type": "structure", "members": {"clusterName": {"shape": "String", "documentation": "<p>The Amazon EKS cluster associated with the exception.</p>"}, "message": {"shape": "String"}}, "documentation": "<p>Amazon EKS detected upgrade readiness issues. Call the <a href=\"https://docs.aws.amazon.com/eks/latest/APIReference/API_ListInsights.html\"> <code>ListInsights</code> </a> API to view detected upgrade blocking issues. Pass the <a href=\"https://docs.aws.amazon.com/eks/latest/APIReference/API_UpdateClusterVersion.html#API_UpdateClusterVersion_RequestBody\"> <code>force</code> </a> flag when updating to override upgrade readiness errors.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "IpFamily": {"type": "string", "enum": ["ipv4", "ipv6"]}, "Issue": {"type": "structure", "members": {"code": {"shape": "NodegroupIssueCode", "documentation": "<p>A brief description of the error.</p> <ul> <li> <p> <b>AccessDenied</b>: Amazon EKS or one or more of your managed nodes is failing to authenticate or authorize with your Kubernetes cluster API server.</p> </li> <li> <p> <b>AsgInstanceLaunchFailures</b>: Your Auto Scaling group is experiencing failures while attempting to launch instances.</p> </li> <li> <p> <b>AutoScalingGroupNotFound</b>: We couldn't find the Auto Scaling group associated with the managed node group. You may be able to recreate an Auto Scaling group with the same settings to recover.</p> </li> <li> <p> <b>ClusterUnreachable</b>: Amazon EKS or one or more of your managed nodes is unable to to communicate with your Kubernetes cluster API server. This can happen if there are network disruptions or if API servers are timing out processing requests. </p> </li> <li> <p> <b>Ec2InstanceTypeDoesNotExist</b>: One or more of the supplied Amazon EC2 instance types do not exist. Amazon EKS checked for the instance types that you provided in this Amazon Web Services Region, and one or more aren't available.</p> </li> <li> <p> <b>Ec2LaunchTemplateNotFound</b>: We couldn't find the Amazon EC2 launch template for your managed node group. You may be able to recreate a launch template with the same settings to recover.</p> </li> <li> <p> <b>Ec2LaunchTemplateVersionMismatch</b>: The Amazon EC2 launch template version for your managed node group does not match the version that Amazon EKS created. You may be able to revert to the version that Amazon EKS created to recover.</p> </li> <li> <p> <b>Ec2SecurityGroupDeletionFailure</b>: We could not delete the remote access security group for your managed node group. Remove any dependencies from the security group.</p> </li> <li> <p> <b>Ec2SecurityGroupNotFound</b>: We couldn't find the cluster security group for the cluster. You must recreate your cluster.</p> </li> <li> <p> <b>Ec2SubnetInvalidConfiguration</b>: One or more Amazon EC2 subnets specified for a node group do not automatically assign public IP addresses to instances launched into it. If you want your instances to be assigned a public IP address, then you need to enable the <code>auto-assign public IP address</code> setting for the subnet. See <a href=\"https://docs.aws.amazon.com/vpc/latest/userguide/vpc-ip-addressing.html#subnet-public-ip\">Modifying the public <code>IPv4</code> addressing attribute for your subnet</a> in the <i>Amazon VPC User Guide</i>.</p> </li> <li> <p> <b>IamInstanceProfileNotFound</b>: We couldn't find the IAM instance profile for your managed node group. You may be able to recreate an instance profile with the same settings to recover.</p> </li> <li> <p> <b>IamNodeRoleNotFound</b>: We couldn't find the IAM role for your managed node group. You may be able to recreate an IAM role with the same settings to recover.</p> </li> <li> <p> <b>InstanceLimitExceeded</b>: Your Amazon Web Services account is unable to launch any more instances of the specified instance type. You may be able to request an Amazon EC2 instance limit increase to recover.</p> </li> <li> <p> <b>InsufficientFreeAddresses</b>: One or more of the subnets associated with your managed node group does not have enough available IP addresses for new nodes.</p> </li> <li> <p> <b>InternalFailure</b>: These errors are usually caused by an Amazon EKS server-side issue.</p> </li> <li> <p> <b>NodeCreationFailure</b>: Your launched instances are unable to register with your Amazon EKS cluster. Common causes of this failure are insufficient <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/create-node-role.html\">node IAM role</a> permissions or lack of outbound internet access for the nodes. </p> </li> </ul>"}, "message": {"shape": "String", "documentation": "<p>The error message associated with the issue.</p>"}, "resourceIds": {"shape": "StringList", "documentation": "<p>The Amazon Web Services resources that are afflicted by this issue.</p>"}}, "documentation": "<p>An object representing an issue with an Amazon EKS resource.</p>"}, "IssueList": {"type": "list", "member": {"shape": "Issue"}}, "KubernetesNetworkConfigRequest": {"type": "structure", "members": {"serviceIpv4Cidr": {"shape": "String", "documentation": "<p>Don't specify a value if you select <code>ipv6</code> for <b>ipFamily</b>. The CIDR block to assign Kubernetes service IP addresses from. If you don't specify a block, Kubernetes assigns addresses from either the <code>**********/16</code> or <code>**********/16</code> CIDR blocks. We recommend that you specify a block that does not overlap with resources in other networks that are peered or connected to your VPC. The block must meet the following requirements:</p> <ul> <li> <p>Within one of the following private IP address blocks: <code>10.0.0.0/8</code>, <code>**********/12</code>, or <code>***********/16</code>.</p> </li> <li> <p>Doesn't overlap with any CIDR block assigned to the VPC that you selected for VPC.</p> </li> <li> <p>Between <code>/24</code> and <code>/12</code>.</p> </li> </ul> <important> <p>You can only specify a custom CIDR block when you create a cluster. You can't change this value after the cluster is created.</p> </important>"}, "ipFamily": {"shape": "IpFamily", "documentation": "<p>Specify which IP family is used to assign Kubernetes pod and service IP addresses. If you don't specify a value, <code>ipv4</code> is used by default. You can only specify an IP family when you create a cluster and can't change this value once the cluster is created. If you specify <code>ipv6</code>, the VPC and subnets that you specify for cluster creation must have both <code>IPv4</code> and <code>IPv6</code> CIDR blocks assigned to them. You can't specify <code>ipv6</code> for clusters in China Regions.</p> <p>You can only specify <code>ipv6</code> for <code>1.21</code> and later clusters that use version <code>1.10.1</code> or later of the Amazon VPC CNI add-on. If you specify <code>ipv6</code>, then ensure that your VPC meets the requirements listed in the considerations listed in <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/cni-ipv6.html\">Assigning IPv6 addresses to pods and services</a> in the <i>Amazon EKS User Guide</i>. Kubernetes assigns services <code>IPv6</code> addresses from the unique local address range <code>(fc00::/7)</code>. You can't specify a custom <code>IPv6</code> CIDR block. Pod addresses are assigned from the subnet's <code>IPv6</code> CIDR.</p>"}, "elasticLoadBalancing": {"shape": "ElasticLoadBalancing", "documentation": "<p>Request to enable or disable the load balancing capability on your EKS Auto Mode cluster. For more information, see EKS Auto Mode load balancing capability in the <i>Amazon EKS User Guide</i>.</p>"}}, "documentation": "<p>The Kubernetes network configuration for the cluster.</p>"}, "KubernetesNetworkConfigResponse": {"type": "structure", "members": {"serviceIpv4Cidr": {"shape": "String", "documentation": "<p>The CIDR block that Kubernetes <code>Pod</code> and <code>Service</code> object IP addresses are assigned from. Kubernetes assigns addresses from an <code>IPv4</code> CIDR block assigned to a subnet that the node is in. If you didn't specify a CIDR block when you created the cluster, then Kubernetes assigns addresses from either the <code>**********/16</code> or <code>**********/16</code> CIDR blocks. If this was specified, then it was specified when the cluster was created and it can't be changed.</p>"}, "serviceIpv6Cidr": {"shape": "String", "documentation": "<p>The CIDR block that Kubernetes pod and service IP addresses are assigned from if you created a 1.21 or later cluster with version 1.10.1 or later of the Amazon VPC CNI add-on and specified <code>ipv6</code> for <b>ipFamily</b> when you created the cluster. Kubernetes assigns service addresses from the unique local address range (<code>fc00::/7</code>) because you can't specify a custom IPv6 CIDR block when you create the cluster.</p>"}, "ipFamily": {"shape": "IpFamily", "documentation": "<p>The IP family used to assign Kubernetes <code>Pod</code> and <code>Service</code> objects IP addresses. The IP family is always <code>ipv4</code>, unless you have a <code>1.21</code> or later cluster running version <code>1.10.1</code> or later of the Amazon VPC CNI plugin for Kubernetes and specified <code>ipv6</code> when you created the cluster. </p>"}, "elasticLoadBalancing": {"shape": "ElasticLoadBalancing", "documentation": "<p>Indicates the current configuration of the load balancing capability on your EKS Auto Mode cluster. For example, if the capability is enabled or disabled.</p>"}}, "documentation": "<p>The Kubernetes network configuration for the cluster. The response contains a value for <b>serviceIpv6Cidr</b> or <b>serviceIpv4Cidr</b>, but not both. </p>"}, "LaunchTemplateSpecification": {"type": "structure", "members": {"name": {"shape": "String", "documentation": "<p>The name of the launch template.</p> <p>You must specify either the launch template name or the launch template ID in the request, but not both. After node group creation, you cannot use a different name.</p>"}, "version": {"shape": "String", "documentation": "<p>The version number of the launch template to use. If no version is specified, then the template's default version is used. You can use a different version for node group updates.</p>"}, "id": {"shape": "String", "documentation": "<p>The ID of the launch template.</p> <p>You must specify either the launch template ID or the launch template name in the request, but not both. After node group creation, you cannot use a different ID.</p>"}}, "documentation": "<p>An object representing a node group launch template specification. The launch template can't include <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/APIReference/API_CreateNetworkInterface.html\"> <code>SubnetId</code> </a>, <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/APIReference/API_IamInstanceProfile.html\"> <code>IamInstanceProfile</code> </a>, <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/APIReference/API_RequestSpotInstances.html\"> <code>RequestSpotInstances</code> </a>, <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/APIReference/API_HibernationOptionsRequest.html\"> <code>HibernationOptions</code> </a>, or <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/APIReference/API_TerminateInstances.html\"> <code>TerminateInstances</code> </a>, or the node group deployment or update will fail. For more information about launch templates, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/APIReference/API_CreateLaunchTemplate.html\"> <code>CreateLaunchTemplate</code> </a> in the Amazon EC2 API Reference. For more information about using launch templates with Amazon EKS, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/launch-templates.html\">Customizing managed nodes with launch templates</a> in the <i>Amazon EKS User Guide</i>.</p> <p>You must specify either the launch template ID or the launch template name in the request, but not both.</p>"}, "License": {"type": "structure", "members": {"id": {"shape": "String", "documentation": "<p>An id associated with an EKS Anywhere subscription license.</p>"}, "token": {"shape": "String", "documentation": "<p>An optional license token that can be used for extended support verification.</p>"}}, "documentation": "<p>An EKS Anywhere license associated with a subscription.</p>"}, "LicenseList": {"type": "list", "member": {"shape": "License"}}, "ListAccessEntriesRequest": {"type": "structure", "required": ["clusterName"], "members": {"clusterName": {"shape": "String", "documentation": "<p>The name of your cluster.</p>", "location": "uri", "locationName": "name"}, "associatedPolicyArn": {"shape": "String", "documentation": "<p>The ARN of an <code>AccessPolicy</code>. When you specify an access policy ARN, only the access entries associated to that access policy are returned. For a list of available policy ARNs, use <code>ListAccessPolicies</code>.</p>", "location": "querystring", "locationName": "associatedPolicyArn"}, "maxResults": {"shape": "ListAccessEntriesRequestMaxResults", "documentation": "<p>The maximum number of results, returned in paginated output. You receive <code>maxResults</code> in a single page, along with a <code>nextToken</code> response element. You can see the remaining results of the initial request by sending another request with the returned <code>nextToken</code> value. This value can be between 1 and 100. If you don't use this parameter, 100 results and a <code>nextToken</code> value, if applicable, are returned.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "String", "documentation": "<p>The <code>nextToken</code> value returned from a previous paginated request, where <code>maxResults</code> was used and the results exceeded the value of that parameter. Pagination continues from the end of the previous results that returned the <code>nextToken</code> value. This value is null when there are no more results to return.</p> <note> <p>This token should be treated as an opaque identifier that is used only to retrieve the next items in a list and not for other programmatic purposes.</p> </note>", "location": "querystring", "locationName": "nextToken"}}}, "ListAccessEntriesRequestMaxResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListAccessEntriesResponse": {"type": "structure", "members": {"accessEntries": {"shape": "StringList", "documentation": "<p>The list of access entries that exist for the cluster.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>The <code>nextToken</code> value returned from a previous paginated request, where <code>maxResults</code> was used and the results exceeded the value of that parameter. Pagination continues from the end of the previous results that returned the <code>nextToken</code> value. This value is null when there are no more results to return.</p> <note> <p>This token should be treated as an opaque identifier that is used only to retrieve the next items in a list and not for other programmatic purposes.</p> </note>"}}}, "ListAccessPoliciesRequest": {"type": "structure", "members": {"maxResults": {"shape": "ListAccessPoliciesRequestMaxResults", "documentation": "<p>The maximum number of results, returned in paginated output. You receive <code>maxResults</code> in a single page, along with a <code>nextToken</code> response element. You can see the remaining results of the initial request by sending another request with the returned <code>nextToken</code> value. This value can be between 1 and 100. If you don't use this parameter, 100 results and a <code>nextToken</code> value, if applicable, are returned.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "String", "documentation": "<p>The <code>nextToken</code> value returned from a previous paginated request, where <code>maxResults</code> was used and the results exceeded the value of that parameter. Pagination continues from the end of the previous results that returned the <code>nextToken</code> value. This value is null when there are no more results to return.</p> <note> <p>This token should be treated as an opaque identifier that is used only to retrieve the next items in a list and not for other programmatic purposes.</p> </note>", "location": "querystring", "locationName": "nextToken"}}}, "ListAccessPoliciesRequestMaxResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListAccessPoliciesResponse": {"type": "structure", "members": {"accessPolicies": {"shape": "AccessPoliciesList", "documentation": "<p>The list of available access policies. You can't view the contents of an access policy using the API. To view the contents, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/access-policies.html#access-policy-permissions\">Access policy permissions</a> in the <i>Amazon EKS User Guide</i>.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>The <code>nextToken</code> value returned from a previous paginated request, where <code>maxResults</code> was used and the results exceeded the value of that parameter. Pagination continues from the end of the previous results that returned the <code>nextToken</code> value. This value is null when there are no more results to return.</p> <note> <p>This token should be treated as an opaque identifier that is used only to retrieve the next items in a list and not for other programmatic purposes.</p> </note>"}}}, "ListAddonsRequest": {"type": "structure", "required": ["clusterName"], "members": {"clusterName": {"shape": "ClusterName", "documentation": "<p>The name of your cluster.</p>", "location": "uri", "locationName": "name"}, "maxResults": {"shape": "ListAddonsRequestMaxResults", "documentation": "<p>The maximum number of results, returned in paginated output. You receive <code>maxResults</code> in a single page, along with a <code>nextToken</code> response element. You can see the remaining results of the initial request by sending another request with the returned <code>nextToken</code> value. This value can be between 1 and 100. If you don't use this parameter, 100 results and a <code>nextToken</code> value, if applicable, are returned.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "String", "documentation": "<p>The <code>nextToken</code> value returned from a previous paginated request, where <code>maxResults</code> was used and the results exceeded the value of that parameter. Pagination continues from the end of the previous results that returned the <code>nextToken</code> value. This value is null when there are no more results to return.</p> <note> <p>This token should be treated as an opaque identifier that is used only to retrieve the next items in a list and not for other programmatic purposes.</p> </note>", "location": "querystring", "locationName": "nextToken"}}}, "ListAddonsRequestMaxResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListAddonsResponse": {"type": "structure", "members": {"addons": {"shape": "StringList", "documentation": "<p>A list of installed add-ons.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>The <code>nextToken</code> value to include in a future <code>ListAddons</code> request. When the results of a <code>ListAddons</code> request exceed <code>maxResults</code>, you can use this value to retrieve the next page of results. This value is <code>null</code> when there are no more results to return.</p> <note> <p>This token should be treated as an opaque identifier that is used only to retrieve the next items in a list and not for other programmatic purposes.</p> </note>"}}}, "ListAssociatedAccessPoliciesRequest": {"type": "structure", "required": ["clusterName", "principalArn"], "members": {"clusterName": {"shape": "String", "documentation": "<p>The name of your cluster.</p>", "location": "uri", "locationName": "name"}, "principalArn": {"shape": "String", "documentation": "<p>The ARN of the IAM principal for the <code>AccessEntry</code>.</p>", "location": "uri", "locationName": "principalArn"}, "maxResults": {"shape": "ListAssociatedAccessPoliciesRequestMaxResults", "documentation": "<p>The maximum number of results, returned in paginated output. You receive <code>maxResults</code> in a single page, along with a <code>nextToken</code> response element. You can see the remaining results of the initial request by sending another request with the returned <code>nextToken</code> value. This value can be between 1 and 100. If you don't use this parameter, 100 results and a <code>nextToken</code> value, if applicable, are returned.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "String", "documentation": "<p>The <code>nextToken</code> value returned from a previous paginated request, where <code>maxResults</code> was used and the results exceeded the value of that parameter. Pagination continues from the end of the previous results that returned the <code>nextToken</code> value. This value is null when there are no more results to return.</p> <note> <p>This token should be treated as an opaque identifier that is used only to retrieve the next items in a list and not for other programmatic purposes.</p> </note>", "location": "querystring", "locationName": "nextToken"}}}, "ListAssociatedAccessPoliciesRequestMaxResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListAssociatedAccessPoliciesResponse": {"type": "structure", "members": {"clusterName": {"shape": "String", "documentation": "<p>The name of your cluster.</p>"}, "principalArn": {"shape": "String", "documentation": "<p>The ARN of the IAM principal for the <code>AccessEntry</code>.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>The <code>nextToken</code> value returned from a previous paginated request, where <code>maxResults</code> was used and the results exceeded the value of that parameter. Pagination continues from the end of the previous results that returned the <code>nextToken</code> value. This value is null when there are no more results to return.</p> <note> <p>This token should be treated as an opaque identifier that is used only to retrieve the next items in a list and not for other programmatic purposes.</p> </note>"}, "associatedAccessPolicies": {"shape": "AssociatedAccessPoliciesList", "documentation": "<p>The list of access policies associated with the access entry.</p>"}}}, "ListClustersRequest": {"type": "structure", "members": {"maxResults": {"shape": "ListClustersRequestMaxResults", "documentation": "<p>The maximum number of results, returned in paginated output. You receive <code>maxResults</code> in a single page, along with a <code>nextToken</code> response element. You can see the remaining results of the initial request by sending another request with the returned <code>nextToken</code> value. This value can be between 1 and 100. If you don't use this parameter, 100 results and a <code>nextToken</code> value, if applicable, are returned.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "String", "documentation": "<p>The <code>nextToken</code> value returned from a previous paginated request, where <code>maxResults</code> was used and the results exceeded the value of that parameter. Pagination continues from the end of the previous results that returned the <code>nextToken</code> value. This value is null when there are no more results to return.</p> <note> <p>This token should be treated as an opaque identifier that is used only to retrieve the next items in a list and not for other programmatic purposes.</p> </note>", "location": "querystring", "locationName": "nextToken"}, "include": {"shape": "IncludeClustersList", "documentation": "<p>Indicates whether external clusters are included in the returned list. Use '<code>all</code>' to return <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/eks-connector.html\">https://docs.aws.amazon.com/eks/latest/userguide/eks-connector.html</a>connected clusters, or blank to return only Amazon EKS clusters. '<code>all</code>' must be in lowercase otherwise an error occurs.</p>", "location": "querystring", "locationName": "include"}}}, "ListClustersRequestMaxResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListClustersResponse": {"type": "structure", "members": {"clusters": {"shape": "StringList", "documentation": "<p>A list of all of the clusters for your account in the specified Amazon Web Services Region .</p>"}, "nextToken": {"shape": "String", "documentation": "<p>The <code>nextToken</code> value returned from a previous paginated request, where <code>maxResults</code> was used and the results exceeded the value of that parameter. Pagination continues from the end of the previous results that returned the <code>nextToken</code> value. This value is null when there are no more results to return.</p> <note> <p>This token should be treated as an opaque identifier that is used only to retrieve the next items in a list and not for other programmatic purposes.</p> </note>"}}}, "ListEksAnywhereSubscriptionsRequest": {"type": "structure", "members": {"maxResults": {"shape": "ListEksAnywhereSubscriptionsRequestMaxResults", "documentation": "<p>The maximum number of cluster results returned by ListEksAnywhereSubscriptions in paginated output. When you use this parameter, ListEksAnywhereSubscriptions returns only maxResults results in a single page along with a nextToken response element. You can see the remaining results of the initial request by sending another ListEksAnywhereSubscriptions request with the returned nextToken value. This value can be between 1 and 100. If you don't use this parameter, ListEksAnywhereSubscriptions returns up to 10 results and a nextToken value if applicable.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "String", "documentation": "<p>The <code>nextToken</code> value returned from a previous paginated <code>ListEksAnywhereSubscriptions</code> request where <code>maxResults</code> was used and the results exceeded the value of that parameter. Pagination continues from the end of the previous results that returned the <code>nextToken</code> value.</p>", "location": "querystring", "locationName": "nextToken"}, "includeStatus": {"shape": "EksAnywhereSubscriptionStatusValues", "documentation": "<p>An array of subscription statuses to filter on.</p>", "location": "querystring", "locationName": "includeStatus"}}}, "ListEksAnywhereSubscriptionsRequestMaxResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListEksAnywhereSubscriptionsResponse": {"type": "structure", "members": {"subscriptions": {"shape": "EksAnywhereSubscriptionList", "documentation": "<p>A list of all subscription objects in the region, filtered by includeStatus and paginated by nextToken and maxResults.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>The nextToken value to include in a future ListEksAnywhereSubscriptions request. When the results of a ListEksAnywhereSubscriptions request exceed maxResults, you can use this value to retrieve the next page of results. This value is null when there are no more results to return.</p>"}}}, "ListFargateProfilesRequest": {"type": "structure", "required": ["clusterName"], "members": {"clusterName": {"shape": "String", "documentation": "<p>The name of your cluster.</p>", "location": "uri", "locationName": "name"}, "maxResults": {"shape": "FargateProfilesRequestMaxResults", "documentation": "<p>The maximum number of results, returned in paginated output. You receive <code>maxResults</code> in a single page, along with a <code>nextToken</code> response element. You can see the remaining results of the initial request by sending another request with the returned <code>nextToken</code> value. This value can be between 1 and 100. If you don't use this parameter, 100 results and a <code>nextToken</code> value, if applicable, are returned.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "String", "documentation": "<p>The <code>nextToken</code> value returned from a previous paginated request, where <code>maxResults</code> was used and the results exceeded the value of that parameter. Pagination continues from the end of the previous results that returned the <code>nextToken</code> value. This value is null when there are no more results to return.</p> <note> <p>This token should be treated as an opaque identifier that is used only to retrieve the next items in a list and not for other programmatic purposes.</p> </note>", "location": "querystring", "locationName": "nextToken"}}}, "ListFargateProfilesResponse": {"type": "structure", "members": {"fargateProfileNames": {"shape": "StringList", "documentation": "<p>A list of all of the Fargate profiles associated with the specified cluster.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>The <code>nextToken</code> value returned from a previous paginated request, where <code>maxResults</code> was used and the results exceeded the value of that parameter. Pagination continues from the end of the previous results that returned the <code>nextToken</code> value. This value is null when there are no more results to return.</p> <note> <p>This token should be treated as an opaque identifier that is used only to retrieve the next items in a list and not for other programmatic purposes.</p> </note>"}}}, "ListIdentityProviderConfigsRequest": {"type": "structure", "required": ["clusterName"], "members": {"clusterName": {"shape": "String", "documentation": "<p>The name of your cluster.</p>", "location": "uri", "locationName": "name"}, "maxResults": {"shape": "ListIdentityProviderConfigsRequestMaxResults", "documentation": "<p>The maximum number of results, returned in paginated output. You receive <code>maxResults</code> in a single page, along with a <code>nextToken</code> response element. You can see the remaining results of the initial request by sending another request with the returned <code>nextToken</code> value. This value can be between 1 and 100. If you don't use this parameter, 100 results and a <code>nextToken</code> value, if applicable, are returned.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "String", "documentation": "<p>The <code>nextToken</code> value returned from a previous paginated request, where <code>maxResults</code> was used and the results exceeded the value of that parameter. Pagination continues from the end of the previous results that returned the <code>nextToken</code> value. This value is null when there are no more results to return.</p> <note> <p>This token should be treated as an opaque identifier that is used only to retrieve the next items in a list and not for other programmatic purposes.</p> </note>", "location": "querystring", "locationName": "nextToken"}}}, "ListIdentityProviderConfigsRequestMaxResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListIdentityProviderConfigsResponse": {"type": "structure", "members": {"identityProviderConfigs": {"shape": "IdentityProviderConfigs", "documentation": "<p>The identity provider configurations for the cluster.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>The <code>nextToken</code> value to include in a future <code>ListIdentityProviderConfigsResponse</code> request. When the results of a <code>ListIdentityProviderConfigsResponse</code> request exceed <code>maxResults</code>, you can use this value to retrieve the next page of results. This value is <code>null</code> when there are no more results to return.</p> <note> <p>This token should be treated as an opaque identifier that is used only to retrieve the next items in a list and not for other programmatic purposes.</p> </note>"}}}, "ListInsightsMaxResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListInsightsRequest": {"type": "structure", "required": ["clusterName"], "members": {"clusterName": {"shape": "String", "documentation": "<p>The name of the Amazon EKS cluster associated with the insights.</p>", "location": "uri", "locationName": "name"}, "filter": {"shape": "InsightsFilter", "documentation": "<p>The criteria to filter your list of insights for your cluster. You can filter which insights are returned by category, associated Kubernetes version, and status.</p>"}, "maxResults": {"shape": "ListInsightsMaxResults", "documentation": "<p>The maximum number of identity provider configurations returned by <code>ListInsights</code> in paginated output. When you use this parameter, <code>ListInsights</code> returns only <code>maxResults</code> results in a single page along with a <code>nextToken</code> response element. You can see the remaining results of the initial request by sending another <code>ListInsights</code> request with the returned <code>nextToken</code> value. This value can be between 1 and 100. If you don't use this parameter, <code>ListInsights</code> returns up to 100 results and a <code>nextToken</code> value, if applicable.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>The <code>nextToken</code> value returned from a previous paginated <code>ListInsights</code> request. When the results of a <code>ListInsights</code> request exceed <code>maxResults</code>, you can use this value to retrieve the next page of results. This value is <code>null</code> when there are no more results to return.</p>"}}}, "ListInsightsResponse": {"type": "structure", "members": {"insights": {"shape": "InsightSummaries", "documentation": "<p>The returned list of insights.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>The <code>nextToken</code> value to include in a future <code>ListInsights</code> request. When the results of a <code>ListInsights</code> request exceed <code>maxResults</code>, you can use this value to retrieve the next page of results. This value is <code>null</code> when there are no more results to return.</p>"}}}, "ListNodegroupsRequest": {"type": "structure", "required": ["clusterName"], "members": {"clusterName": {"shape": "String", "documentation": "<p>The name of your cluster.</p>", "location": "uri", "locationName": "name"}, "maxResults": {"shape": "ListNodegroupsRequestMaxResults", "documentation": "<p>The maximum number of results, returned in paginated output. You receive <code>maxResults</code> in a single page, along with a <code>nextToken</code> response element. You can see the remaining results of the initial request by sending another request with the returned <code>nextToken</code> value. This value can be between 1 and 100. If you don't use this parameter, 100 results and a <code>nextToken</code> value, if applicable, are returned.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "String", "documentation": "<p>The <code>nextToken</code> value returned from a previous paginated request, where <code>maxResults</code> was used and the results exceeded the value of that parameter. Pagination continues from the end of the previous results that returned the <code>nextToken</code> value. This value is null when there are no more results to return.</p> <note> <p>This token should be treated as an opaque identifier that is used only to retrieve the next items in a list and not for other programmatic purposes.</p> </note>", "location": "querystring", "locationName": "nextToken"}}}, "ListNodegroupsRequestMaxResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListNodegroupsResponse": {"type": "structure", "members": {"nodegroups": {"shape": "StringList", "documentation": "<p>A list of all of the node groups associated with the specified cluster.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>The <code>nextToken</code> value returned from a previous paginated request, where <code>maxResults</code> was used and the results exceeded the value of that parameter. Pagination continues from the end of the previous results that returned the <code>nextToken</code> value. This value is null when there are no more results to return.</p> <note> <p>This token should be treated as an opaque identifier that is used only to retrieve the next items in a list and not for other programmatic purposes.</p> </note>"}}}, "ListPodIdentityAssociationsMaxResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListPodIdentityAssociationsRequest": {"type": "structure", "required": ["clusterName"], "members": {"clusterName": {"shape": "String", "documentation": "<p>The name of the cluster that the associations are in.</p>", "location": "uri", "locationName": "name"}, "namespace": {"shape": "String", "documentation": "<p>The name of the Kubernetes namespace inside the cluster that the associations are in.</p>", "location": "querystring", "locationName": "namespace"}, "serviceAccount": {"shape": "String", "documentation": "<p>The name of the Kubernetes service account that the associations use.</p>", "location": "querystring", "locationName": "serviceAccount"}, "maxResults": {"shape": "ListPodIdentityAssociationsMaxResults", "documentation": "<p>The maximum number of EKS Pod Identity association results returned by <code>ListPodIdentityAssociations</code> in paginated output. When you use this parameter, <code>ListPodIdentityAssociations</code> returns only <code>maxResults</code> results in a single page along with a <code>nextToken</code> response element. You can see the remaining results of the initial request by sending another <code>ListPodIdentityAssociations</code> request with the returned <code>nextToken</code> value. This value can be between 1 and 100. If you don't use this parameter, <code>ListPodIdentityAssociations</code> returns up to 100 results and a <code>nextToken</code> value if applicable.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "String", "documentation": "<p>The <code>nextToken</code> value returned from a previous paginated <code>ListUpdates</code> request where <code>maxResults</code> was used and the results exceeded the value of that parameter. Pagination continues from the end of the previous results that returned the <code>nextToken</code> value.</p> <note> <p>This token should be treated as an opaque identifier that is used only to retrieve the next items in a list and not for other programmatic purposes.</p> </note>", "location": "querystring", "locationName": "nextToken"}}}, "ListPodIdentityAssociationsResponse": {"type": "structure", "members": {"associations": {"shape": "PodIdentityAssociationSummaries", "documentation": "<p>The list of summarized descriptions of the associations that are in the cluster and match any filters that you provided.</p> <p>Each summary is simplified by removing these fields compared to the full <a href=\"https://docs.aws.amazon.com/eks/latest/APIReference/API_PodIdentityAssociation.html\"> <code>PodIdentityAssociation</code> </a>:</p> <ul> <li> <p>The IAM role: <code>roleArn</code> </p> </li> <li> <p>The timestamp that the association was created at: <code>createdAt</code> </p> </li> <li> <p>The most recent timestamp that the association was modified at:. <code>modifiedAt</code> </p> </li> <li> <p>The tags on the association: <code>tags</code> </p> </li> </ul>"}, "nextToken": {"shape": "String", "documentation": "<p>The <code>nextToken</code> value to include in a future <code>ListPodIdentityAssociations</code> request. When the results of a <code>ListPodIdentityAssociations</code> request exceed <code>maxResults</code>, you can use this value to retrieve the next page of results. This value is <code>null</code> when there are no more results to return.</p> <note> <p>This token should be treated as an opaque identifier that is used only to retrieve the next items in a list and not for other programmatic purposes.</p> </note>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) that identifies the resource to list tags for.</p>", "location": "uri", "locationName": "resourceArn"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"tags": {"shape": "TagMap", "documentation": "<p>The tags for the resource.</p>"}}}, "ListUpdatesRequest": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "String", "documentation": "<p>The name of the Amazon EKS cluster to list updates for.</p>", "location": "uri", "locationName": "name"}, "nodegroupName": {"shape": "String", "documentation": "<p>The name of the Amazon EKS managed node group to list updates for.</p>", "location": "querystring", "locationName": "nodegroupName"}, "addonName": {"shape": "String", "documentation": "<p>The names of the installed add-ons that have available updates.</p>", "location": "querystring", "locationName": "addonName"}, "nextToken": {"shape": "String", "documentation": "<p>The <code>nextToken</code> value returned from a previous paginated request, where <code>maxResults</code> was used and the results exceeded the value of that parameter. Pagination continues from the end of the previous results that returned the <code>nextToken</code> value. This value is null when there are no more results to return.</p> <note> <p>This token should be treated as an opaque identifier that is used only to retrieve the next items in a list and not for other programmatic purposes.</p> </note>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "ListUpdatesRequestMaxResults", "documentation": "<p>The maximum number of results, returned in paginated output. You receive <code>maxResults</code> in a single page, along with a <code>nextToken</code> response element. You can see the remaining results of the initial request by sending another request with the returned <code>nextToken</code> value. This value can be between 1 and 100. If you don't use this parameter, 100 results and a <code>nextToken</code> value, if applicable, are returned.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListUpdatesRequestMaxResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "ListUpdatesResponse": {"type": "structure", "members": {"updateIds": {"shape": "StringList", "documentation": "<p>A list of all the updates for the specified cluster and Region.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>The <code>nextToken</code> value returned from a previous paginated request, where <code>maxResults</code> was used and the results exceeded the value of that parameter. Pagination continues from the end of the previous results that returned the <code>nextToken</code> value. This value is null when there are no more results to return.</p> <note> <p>This token should be treated as an opaque identifier that is used only to retrieve the next items in a list and not for other programmatic purposes.</p> </note>"}}}, "LogSetup": {"type": "structure", "members": {"types": {"shape": "LogTypes", "documentation": "<p>The available cluster control plane log types.</p>"}, "enabled": {"shape": "BoxedBoolean", "documentation": "<p>If a log type is enabled, that log type exports its control plane logs to CloudWatch Logs . If a log type isn't enabled, that log type doesn't export its control plane logs. Each individual log type can be enabled or disabled independently.</p>"}}, "documentation": "<p>An object representing the enabled or disabled Kubernetes control plane logs for your cluster.</p>"}, "LogSetups": {"type": "list", "member": {"shape": "LogSetup"}}, "LogType": {"type": "string", "enum": ["api", "audit", "authenticator", "controllerManager", "scheduler"]}, "LogTypes": {"type": "list", "member": {"shape": "LogType"}}, "Logging": {"type": "structure", "members": {"clusterLogging": {"shape": "LogSetups", "documentation": "<p>The cluster control plane logging configuration for your cluster.</p>"}}, "documentation": "<p>An object representing the logging configuration for resources in your cluster.</p>"}, "MarketplaceInformation": {"type": "structure", "members": {"productId": {"shape": "String", "documentation": "<p>The product ID from the Amazon Web Services Marketplace.</p>"}, "productUrl": {"shape": "String", "documentation": "<p>The product URL from the Amazon Web Services Marketplace.</p>"}}, "documentation": "<p>Information about an Amazon EKS add-on from the Amazon Web Services Marketplace.</p>"}, "NodeRepairConfig": {"type": "structure", "members": {"enabled": {"shape": "BoxedBoolean", "documentation": "<p>Specifies whether to enable node auto repair for the node group. Node auto repair is disabled by default.</p>"}}, "documentation": "<p>The node auto repair configuration for the node group.</p>"}, "Nodegroup": {"type": "structure", "members": {"nodegroupName": {"shape": "String", "documentation": "<p>The name associated with an Amazon EKS managed node group.</p>"}, "nodegroupArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) associated with the managed node group.</p>"}, "clusterName": {"shape": "String", "documentation": "<p>The name of your cluster.</p>"}, "version": {"shape": "String", "documentation": "<p>The Kubernetes version of the managed node group.</p>"}, "releaseVersion": {"shape": "String", "documentation": "<p>If the node group was deployed using a launch template with a custom AMI, then this is the AMI ID that was specified in the launch template. For node groups that weren't deployed using a launch template, this is the version of the Amazon EKS optimized AMI that the node group was deployed with.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The Unix epoch timestamp at object creation.</p>"}, "modifiedAt": {"shape": "Timestamp", "documentation": "<p>The Unix epoch timestamp for the last modification to the object.</p>"}, "status": {"shape": "NodegroupStatus", "documentation": "<p>The current status of the managed node group.</p>"}, "capacityType": {"shape": "CapacityTypes", "documentation": "<p>The capacity type of your managed node group.</p>"}, "scalingConfig": {"shape": "NodegroupScalingConfig", "documentation": "<p>The scaling configuration details for the Auto Scaling group that is associated with your node group.</p>"}, "instanceTypes": {"shape": "StringList", "documentation": "<p>If the node group wasn't deployed with a launch template, then this is the instance type that is associated with the node group. If the node group was deployed with a launch template, then this is <code>null</code>.</p>"}, "subnets": {"shape": "StringList", "documentation": "<p>The subnets that were specified for the Auto Scaling group that is associated with your node group.</p>"}, "remoteAccess": {"shape": "RemoteAccessConfig", "documentation": "<p>If the node group wasn't deployed with a launch template, then this is the remote access configuration that is associated with the node group. If the node group was deployed with a launch template, then this is <code>null</code>.</p>"}, "amiType": {"shape": "AMITypes", "documentation": "<p>If the node group was deployed using a launch template with a custom AMI, then this is <code>CUSTOM</code>. For node groups that weren't deployed using a launch template, this is the AMI type that was specified in the node group configuration.</p>"}, "nodeRole": {"shape": "String", "documentation": "<p>The IAM role associated with your node group. The Amazon EKS node <code>kubelet</code> daemon makes calls to Amazon Web Services APIs on your behalf. Nodes receive permissions for these API calls through an IAM instance profile and associated policies.</p>"}, "labels": {"shape": "labelsMap", "documentation": "<p>The Kubernetes <code>labels</code> applied to the nodes in the node group.</p> <note> <p>Only <code>labels</code> that are applied with the Amazon EKS API are shown here. There may be other Kubernetes <code>labels</code> applied to the nodes in this group.</p> </note>"}, "taints": {"shape": "taintsList", "documentation": "<p>The Kubernetes taints to be applied to the nodes in the node group when they are created. Effect is one of <code>No_Schedule</code>, <code>Prefer_No_Schedule</code>, or <code>No_Execute</code>. Kubernetes taints can be used together with tolerations to control how workloads are scheduled to your nodes. For more information, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/node-taints-managed-node-groups.html\">Node taints on managed node groups</a>.</p>"}, "resources": {"shape": "NodegroupResources", "documentation": "<p>The resources associated with the node group, such as Auto Scaling groups and security groups for remote access.</p>"}, "diskSize": {"shape": "BoxedInteger", "documentation": "<p>If the node group wasn't deployed with a launch template, then this is the disk size in the node group configuration. If the node group was deployed with a launch template, then this is <code>null</code>.</p>"}, "health": {"shape": "NodegroupHealth", "documentation": "<p>The health status of the node group. If there are issues with your node group's health, they are listed here.</p>"}, "updateConfig": {"shape": "NodegroupUpdateConfig", "documentation": "<p>The node group update configuration.</p>"}, "nodeRepairConfig": {"shape": "NodeRepairConfig", "documentation": "<p>The node auto repair configuration for the node group.</p>"}, "launchTemplate": {"shape": "LaunchTemplateSpecification", "documentation": "<p>If a launch template was used to create the node group, then this is the launch template that was used.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>Metadata that assists with categorization and organization. Each tag consists of a key and an optional value. You define both. Tags don't propagate to any other cluster or Amazon Web Services resources.</p>"}}, "documentation": "<p>An object representing an Amazon EKS managed node group.</p>"}, "NodegroupHealth": {"type": "structure", "members": {"issues": {"shape": "IssueList", "documentation": "<p>Any issues that are associated with the node group. </p>"}}, "documentation": "<p>An object representing the health status of the node group.</p>"}, "NodegroupIssueCode": {"type": "string", "enum": ["AutoScalingGroupNotFound", "AutoScalingGroupInvalidConfiguration", "Ec2SecurityGroupNotFound", "Ec2SecurityGroupDeletionFailure", "Ec2LaunchTemplateNotFound", "Ec2LaunchTemplateVersionMismatch", "Ec2SubnetNotFound", "Ec2SubnetInvalidConfiguration", "IamInstanceProfileNotFound", "Ec2SubnetMissingIpv6Assignment", "IamLimitExceeded", "IamNodeRoleNotFound", "NodeCreationFailure", "AsgInstanceLaunchFailures", "InstanceLimitExceeded", "InsufficientFreeAddresses", "AccessDenied", "InternalFailure", "ClusterUnreachable", "AmiIdNotFound", "AutoScalingGroupOptInRequired", "AutoScalingGroupRateLimitExceeded", "Ec2LaunchTemplateDeletionFailure", "Ec2LaunchTemplateInvalidConfiguration", "Ec2LaunchTemplateMaxLimitExceeded", "Ec2SubnetListTooLong", "IamThrottling", "NodeTerminationFailure", "PodEvictionFailure", "SourceEc2LaunchTemplateNotFound", "LimitExceeded", "Unknown", "AutoScalingGroupInstanceRefreshActive", "KubernetesLabelInvalid", "Ec2LaunchTemplateVersionMaxLimitExceeded", "Ec2InstanceTypeDoesNotExist"]}, "NodegroupResources": {"type": "structure", "members": {"autoScalingGroups": {"shape": "AutoScalingGroupList", "documentation": "<p>The Auto Scaling groups associated with the node group.</p>"}, "remoteAccessSecurityGroup": {"shape": "String", "documentation": "<p>The remote access security group associated with the node group. This security group controls SSH access to the nodes.</p>"}}, "documentation": "<p>An object representing the resources associated with the node group, such as Auto Scaling groups and security groups for remote access.</p>"}, "NodegroupScalingConfig": {"type": "structure", "members": {"minSize": {"shape": "ZeroCapacity", "documentation": "<p>The minimum number of nodes that the managed node group can scale in to.</p>"}, "maxSize": {"shape": "Capacity", "documentation": "<p>The maximum number of nodes that the managed node group can scale out to. For information about the maximum number that you can specify, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/service-quotas.html\">Amazon EKS service quotas</a> in the <i>Amazon EKS User Guide</i>.</p>"}, "desiredSize": {"shape": "ZeroCapacity", "documentation": "<p>The current number of nodes that the managed node group should maintain.</p> <important> <p>If you use the Kubernetes <a href=\"https://github.com/kubernetes/autoscaler#kubernetes-autoscaler\">Cluster Autoscaler</a>, you shouldn't change the <code>desiredSize</code> value directly, as this can cause the Cluster Autoscaler to suddenly scale up or scale down.</p> </important> <p>Whenever this parameter changes, the number of worker nodes in the node group is updated to the specified size. If this parameter is given a value that is smaller than the current number of running worker nodes, the necessary number of worker nodes are terminated to match the given value. When using CloudFormation, no action occurs if you remove this parameter from your CFN template.</p> <p>This parameter can be different from <code>minSize</code> in some cases, such as when starting with extra hosts for testing. This parameter can also be different when you want to start with an estimated number of needed hosts, but let the Cluster Autoscaler reduce the number if there are too many. When the Cluster Autoscaler is used, the <code>desiredSize</code> parameter is altered by the Cluster Autoscaler (but can be out-of-date for short periods of time). the Cluster Autoscaler doesn't scale a managed node group lower than <code>minSize</code> or higher than <code>maxSize</code>.</p>"}}, "documentation": "<p>An object representing the scaling configuration details for the Auto Scaling group that is associated with your node group. When creating a node group, you must specify all or none of the properties. When updating a node group, you can specify any or none of the properties.</p>"}, "NodegroupStatus": {"type": "string", "enum": ["CREATING", "ACTIVE", "UPDATING", "DELETING", "CREATE_FAILED", "DELETE_FAILED", "DEGRADED"]}, "NodegroupUpdateConfig": {"type": "structure", "members": {"maxUnavailable": {"shape": "NonZeroInteger", "documentation": "<p>The maximum number of nodes unavailable at once during a version update. Nodes are updated in parallel. This value or <code>maxUnavailablePercentage</code> is required to have a value.The maximum number is 100.</p>"}, "maxUnavailablePercentage": {"shape": "PercentCapacity", "documentation": "<p>The maximum percentage of nodes unavailable during a version update. This percentage of nodes are updated in parallel, up to 100 nodes at once. This value or <code>maxUnavailable</code> is required to have a value.</p>"}, "updateStrategy": {"shape": "NodegroupUpdateStrategies", "documentation": "<p>The configuration for the behavior to follow during a node group version update of this managed node group. You choose between two possible strategies for replacing nodes during an <a href=\"https://docs.aws.amazon.com/eks/latest/APIReference/API_UpdateNodegroupVersion.html\"> <code>UpdateNodegroupVersion</code> </a> action.</p> <p>An Amazon EKS managed node group updates by replacing nodes with new nodes of newer AMI versions in parallel. The <i>update strategy</i> changes the managed node update behavior of the managed node group for each quantity. The <i>default</i> strategy has guardrails to protect you from misconfiguration and launches the new instances first, before terminating the old instances. The <i>minimal</i> strategy removes the guardrails and terminates the old instances before launching the new instances. This minimal strategy is useful in scenarios where you are constrained to resources or costs (for example, with hardware accelerators such as GPUs).</p>"}}, "documentation": "<p>The node group update configuration. An Amazon EKS managed node group updates by replacing nodes with new nodes of newer AMI versions in parallel. You choose the <i>maximum unavailable</i> and the <i>update strategy</i>.</p>"}, "NodegroupUpdateStrategies": {"type": "string", "enum": ["DEFAULT", "MINIMAL"]}, "NonZeroInteger": {"type": "integer", "box": true, "min": 1}, "NotFoundException": {"type": "structure", "members": {"message": {"shape": "String", "documentation": "<p>A service resource associated with the request could not be found. Clients should not retry such requests.</p>"}}, "documentation": "<p>A service resource associated with the request could not be found. Clients should not retry such requests.</p>", "error": {"httpStatusCode": 404}, "exception": true}, "OIDC": {"type": "structure", "members": {"issuer": {"shape": "String", "documentation": "<p>The issuer URL for the OIDC identity provider.</p>"}}, "documentation": "<p>An object representing the <a href=\"https://openid.net/connect/\">OpenID Connect</a> (OIDC) identity provider information for the cluster.</p>"}, "OidcIdentityProviderConfig": {"type": "structure", "members": {"identityProviderConfigName": {"shape": "String", "documentation": "<p>The name of the configuration.</p>"}, "identityProviderConfigArn": {"shape": "String", "documentation": "<p>The ARN of the configuration.</p>"}, "clusterName": {"shape": "String", "documentation": "<p>The name of your cluster.</p>"}, "issuerUrl": {"shape": "String", "documentation": "<p>The URL of the OIDC identity provider that allows the API server to discover public signing keys for verifying tokens.</p>"}, "clientId": {"shape": "String", "documentation": "<p>This is also known as <i>audience</i>. The ID of the client application that makes authentication requests to the OIDC identity provider.</p>"}, "usernameClaim": {"shape": "String", "documentation": "<p>The JSON Web token (JWT) claim that is used as the username.</p>"}, "usernamePrefix": {"shape": "String", "documentation": "<p>The prefix that is prepended to username claims to prevent clashes with existing names. The prefix can't contain <code>system:</code> </p>"}, "groupsClaim": {"shape": "String", "documentation": "<p>The JSON web token (JWT) claim that the provider uses to return your groups.</p>"}, "groupsPrefix": {"shape": "String", "documentation": "<p>The prefix that is prepended to group claims to prevent clashes with existing names (such as <code>system:</code> groups). For example, the value<code> oidc:</code> creates group names like <code>oidc:engineering</code> and <code>oidc:infra</code>. The prefix can't contain <code>system:</code> </p>"}, "requiredClaims": {"shape": "requiredClaimsMap", "documentation": "<p>The key-value pairs that describe required claims in the identity token. If set, each claim is verified to be present in the token with a matching value.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>Metadata that assists with categorization and organization. Each tag consists of a key and an optional value. You define both. Tags don't propagate to any other cluster or Amazon Web Services resources.</p>"}, "status": {"shape": "config<PERSON><PERSON>us", "documentation": "<p>The status of the OIDC identity provider.</p>"}}, "documentation": "<p>An object representing the configuration for an OpenID Connect (OIDC) identity provider. </p>"}, "OidcIdentityProviderConfigRequest": {"type": "structure", "required": ["identityProviderConfigName", "issuerUrl", "clientId"], "members": {"identityProviderConfigName": {"shape": "String", "documentation": "<p>The name of the OIDC provider configuration.</p>"}, "issuerUrl": {"shape": "String", "documentation": "<p>The URL of the OIDC identity provider that allows the API server to discover public signing keys for verifying tokens. The URL must begin with <code>https://</code> and should correspond to the <code>iss</code> claim in the provider's OIDC ID tokens. Based on the OIDC standard, path components are allowed but query parameters are not. Typically the URL consists of only a hostname, like <code>https://server.example.org</code> or <code>https://example.com</code>. This URL should point to the level below <code>.well-known/openid-configuration</code> and must be publicly accessible over the internet.</p>"}, "clientId": {"shape": "String", "documentation": "<p>This is also known as <i>audience</i>. The ID for the client application that makes authentication requests to the OIDC identity provider.</p>"}, "usernameClaim": {"shape": "String", "documentation": "<p>The JSON Web Token (JWT) claim to use as the username. The default is <code>sub</code>, which is expected to be a unique identifier of the end user. You can choose other claims, such as <code>email</code> or <code>name</code>, depending on the OIDC identity provider. Claims other than <code>email</code> are prefixed with the issuer URL to prevent naming clashes with other plug-ins.</p>"}, "usernamePrefix": {"shape": "String", "documentation": "<p>The prefix that is prepended to username claims to prevent clashes with existing names. If you do not provide this field, and <code>username</code> is a value other than <code>email</code>, the prefix defaults to <code>issuerurl#</code>. You can use the value <code>-</code> to disable all prefixing.</p>"}, "groupsClaim": {"shape": "String", "documentation": "<p>The JWT claim that the provider uses to return your groups.</p>"}, "groupsPrefix": {"shape": "String", "documentation": "<p>The prefix that is prepended to group claims to prevent clashes with existing names (such as <code>system:</code> groups). For example, the value<code> oidc:</code> will create group names like <code>oidc:engineering</code> and <code>oidc:infra</code>.</p>"}, "requiredClaims": {"shape": "requiredClaimsMap", "documentation": "<p>The key value pairs that describe required claims in the identity token. If set, each claim is verified to be present in the token with a matching value. For the maximum number of claims that you can require, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/service-quotas.html\">Amazon EKS service quotas</a> in the <i>Amazon EKS User Guide</i>.</p>"}}, "documentation": "<p>An object representing an OpenID Connect (OIDC) configuration. Before associating an OIDC identity provider to your cluster, review the considerations in <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/authenticate-oidc-identity-provider.html\">Authenticating users for your cluster from an OIDC identity provider</a> in the <i>Amazon EKS User Guide</i>.</p>"}, "OutpostConfigRequest": {"type": "structure", "required": ["outpostArns", "controlPlaneInstanceType"], "members": {"outpostArns": {"shape": "StringList", "documentation": "<p>The ARN of the Outpost that you want to use for your local Amazon EKS cluster on Outposts. Only a single Outpost ARN is supported.</p>"}, "controlPlaneInstanceType": {"shape": "String", "documentation": "<p>The Amazon EC2 instance type that you want to use for your local Amazon EKS cluster on Outposts. Choose an instance type based on the number of nodes that your cluster will have. For more information, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/eks-outposts-capacity-considerations.html\">Capacity considerations</a> in the <i>Amazon EKS User Guide</i>.</p> <p>The instance type that you specify is used for all Kubernetes control plane instances. The instance type can't be changed after cluster creation. The control plane is not automatically scaled by Amazon EKS.</p> <p> </p>"}, "controlPlanePlacement": {"shape": "ControlPlanePlacementRequest", "documentation": "<p>An object representing the placement configuration for all the control plane instances of your local Amazon EKS cluster on an Amazon Web Services Outpost. For more information, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/eks-outposts-capacity-considerations.html\">Capacity considerations</a> in the <i>Amazon EKS User Guide</i>.</p>"}}, "documentation": "<p>The configuration of your local Amazon EKS cluster on an Amazon Web Services Outpost. Before creating a cluster on an Outpost, review <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/eks-outposts-local-cluster-create.html\">Creating a local cluster on an Outpost</a> in the <i>Amazon EKS User Guide</i>. This API isn't available for Amazon EKS clusters on the Amazon Web Services cloud.</p>"}, "OutpostConfigResponse": {"type": "structure", "required": ["outpostArns", "controlPlaneInstanceType"], "members": {"outpostArns": {"shape": "StringList", "documentation": "<p>The ARN of the Outpost that you specified for use with your local Amazon EKS cluster on Outposts.</p>"}, "controlPlaneInstanceType": {"shape": "String", "documentation": "<p>The Amazon EC2 instance type used for the control plane. The instance type is the same for all control plane instances.</p>"}, "controlPlanePlacement": {"shape": "ControlPlanePlacementResponse", "documentation": "<p>An object representing the placement configuration for all the control plane instances of your local Amazon EKS cluster on an Amazon Web Services Outpost. For more information, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/eks-outposts-capacity-considerations.html\">Capacity considerations</a> in the <i>Amazon EKS User Guide</i>.</p>"}}, "documentation": "<p>An object representing the configuration of your local Amazon EKS cluster on an Amazon Web Services Outpost. This API isn't available for Amazon EKS clusters on the Amazon Web Services cloud.</p>"}, "PercentCapacity": {"type": "integer", "box": true, "max": 100, "min": 1}, "PodIdentityAssociation": {"type": "structure", "members": {"clusterName": {"shape": "String", "documentation": "<p>The name of the cluster that the association is in.</p>"}, "namespace": {"shape": "String", "documentation": "<p>The name of the Kubernetes namespace inside the cluster to create the association in. The service account and the Pods that use the service account must be in this namespace.</p>"}, "serviceAccount": {"shape": "String", "documentation": "<p>The name of the Kubernetes service account inside the cluster to associate the IAM credentials with.</p>"}, "roleArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role to associate with the service account. The EKS Pod Identity agent manages credentials to assume this role for applications in the containers in the Pods that use this service account.</p>"}, "associationArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the association.</p>"}, "associationId": {"shape": "String", "documentation": "<p>The ID of the association.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>Metadata that assists with categorization and organization. Each tag consists of a key and an optional value. You define both. Tags don't propagate to any other cluster or Amazon Web Services resources.</p> <p>The following basic restrictions apply to tags:</p> <ul> <li> <p>Maximum number of tags per resource – 50</p> </li> <li> <p>For each resource, each tag key must be unique, and each tag key can have only one value.</p> </li> <li> <p>Maximum key length – 128 Unicode characters in UTF-8</p> </li> <li> <p>Maximum value length – 256 Unicode characters in UTF-8</p> </li> <li> <p>If your tagging schema is used across multiple services and resources, remember that other services may have restrictions on allowed characters. Generally allowed characters are: letters, numbers, and spaces representable in UTF-8, and the following characters: + - = . _ : / @.</p> </li> <li> <p>Tag keys and values are case-sensitive.</p> </li> <li> <p>Do not use <code>aws:</code>, <code>AWS:</code>, or any upper or lowercase combination of such as a prefix for either keys or values as it is reserved for Amazon Web Services use. You cannot edit or delete tag keys or values with this prefix. Tags with this prefix do not count against your tags per resource limit.</p> </li> </ul>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The timestamp that the association was created at.</p>"}, "modifiedAt": {"shape": "Timestamp", "documentation": "<p>The most recent timestamp that the association was modified at.</p>"}, "ownerArn": {"shape": "String", "documentation": "<p>If defined, the EKS Pod Identity association is owned by an Amazon EKS add-on.</p>"}, "disableSessionTags": {"shape": "BoxedBoolean", "documentation": "<p>The state of the automatic sessions tags. The value of <i>true</i> disables these tags.</p> <p>EKS Pod Identity adds a pre-defined set of session tags when it assumes the role. You can use these tags to author a single role that can work across resources by allowing access to Amazon Web Services resources based on matching tags. By default, EKS Pod Identity attaches six tags, including tags for cluster name, namespace, and service account name. For the list of tags added by EKS Pod Identity, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/pod-id-abac.html#pod-id-abac-tags\">List of session tags added by EKS Pod Identity</a> in the <i>Amazon EKS User Guide</i>.</p>"}, "targetRoleArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the target IAM role to associate with the service account. This role is assumed by using the EKS Pod Identity association role, then the credentials for this role are injected into the Pod.</p>"}, "externalId": {"shape": "String", "documentation": "<p>The unique identifier for this EKS Pod Identity association for a target IAM role. You put this value in the trust policy of the target role, in a <code>Condition</code> to match the <code>sts.ExternalId</code>. This ensures that the target role can only be assumed by this association. This prevents the <i>confused deputy problem</i>. For more information about the confused deputy problem, see <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/confused-deputy.html\">The confused deputy problem</a> in the <i>IAM User Guide</i>.</p> <p>If you want to use the same target role with multiple associations or other roles, use independent statements in the trust policy to allow <code>sts:AssumeRole</code> access from each role.</p>"}}, "documentation": "<p>Amazon EKS Pod Identity associations provide the ability to manage credentials for your applications, similar to the way that Amazon EC2 instance profiles provide credentials to Amazon EC2 instances.</p>"}, "PodIdentityAssociationSummaries": {"type": "list", "member": {"shape": "PodIdentityAssociationSummary"}}, "PodIdentityAssociationSummary": {"type": "structure", "members": {"clusterName": {"shape": "String", "documentation": "<p>The name of the cluster that the association is in.</p>"}, "namespace": {"shape": "String", "documentation": "<p>The name of the Kubernetes namespace inside the cluster to create the association in. The service account and the Pods that use the service account must be in this namespace.</p>"}, "serviceAccount": {"shape": "String", "documentation": "<p>The name of the Kubernetes service account inside the cluster to associate the IAM credentials with.</p>"}, "associationArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the association.</p>"}, "associationId": {"shape": "String", "documentation": "<p>The ID of the association.</p>"}, "ownerArn": {"shape": "String", "documentation": "<p>If defined, the association is owned by an Amazon EKS add-on.</p>"}}, "documentation": "<p>The summarized description of the association.</p> <p>Each summary is simplified by removing these fields compared to the full <a href=\"https://docs.aws.amazon.com/eks/latest/APIReference/API_PodIdentityAssociation.html\"> <code>PodIdentityAssociation</code> </a>:</p> <ul> <li> <p>The IAM role: <code>roleArn</code> </p> </li> <li> <p>The timestamp that the association was created at: <code>createdAt</code> </p> </li> <li> <p>The most recent timestamp that the association was modified at:. <code>modifiedAt</code> </p> </li> <li> <p>The tags on the association: <code>tags</code> </p> </li> </ul>"}, "Provider": {"type": "structure", "members": {"keyArn": {"shape": "String", "documentation": "<p>Amazon Resource Name (ARN) or alias of the KMS key. The KMS key must be symmetric and created in the same Amazon Web Services Region as the cluster. If the KMS key was created in a different account, the <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/id_roles_terms-and-concepts.html\">IAM principal</a> must have access to the KMS key. For more information, see <a href=\"https://docs.aws.amazon.com/kms/latest/developerguide/key-policy-modifying-external-accounts.html\">Allowing users in other accounts to use a KMS key</a> in the <i>Key Management Service Developer Guide</i>.</p>"}}, "documentation": "<p>Identifies the Key Management Service (KMS) key used to encrypt the secrets.</p>"}, "RegisterClusterRequest": {"type": "structure", "required": ["name", "connectorConfig"], "members": {"name": {"shape": "ClusterName", "documentation": "<p>A unique name for this cluster in your Amazon Web Services Region.</p>"}, "connectorConfig": {"shape": "ConnectorConfigRequest", "documentation": "<p>The configuration settings required to connect the Kubernetes cluster to the Amazon EKS control plane.</p>"}, "clientRequestToken": {"shape": "String", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency of the request.</p>", "idempotencyToken": true}, "tags": {"shape": "TagMap", "documentation": "<p>Metadata that assists with categorization and organization. Each tag consists of a key and an optional value. You define both. Tags don't propagate to any other cluster or Amazon Web Services resources.</p>"}}}, "RegisterClusterResponse": {"type": "structure", "members": {"cluster": {"shape": "Cluster"}}}, "RemoteAccessConfig": {"type": "structure", "members": {"ec2SshKey": {"shape": "String", "documentation": "<p>The Amazon EC2 SSH key name that provides access for SSH communication with the nodes in the managed node group. For more information, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/ec2-key-pairs.html\">Amazon EC2 key pairs and Linux instances</a> in the <i>Amazon Elastic Compute Cloud User Guide for Linux Instances</i>. For Windows, an Amazon EC2 SSH key is used to obtain the RDP password. For more information, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/WindowsGuide/ec2-key-pairs.html\">Amazon EC2 key pairs and Windows instances</a> in the <i>Amazon Elastic Compute Cloud User Guide for Windows Instances</i>.</p>"}, "sourceSecurityGroups": {"shape": "StringList", "documentation": "<p>The security group IDs that are allowed SSH access (port 22) to the nodes. For Windows, the port is 3389. If you specify an Amazon EC2 SSH key but don't specify a source security group when you create a managed node group, then the port on the nodes is opened to the internet (<code>0.0.0.0/0</code>). For more information, see <a href=\"https://docs.aws.amazon.com/vpc/latest/userguide/VPC_SecurityGroups.html\">Security Groups for Your VPC</a> in the <i>Amazon Virtual Private Cloud User Guide</i>.</p>"}}, "documentation": "<p>An object representing the remote access configuration for the managed node group.</p>"}, "RemoteNetworkConfigRequest": {"type": "structure", "members": {"remoteNodeNetworks": {"shape": "RemoteNodeNetworkList", "documentation": "<p>The list of network CIDRs that can contain hybrid nodes.</p> <p>These CIDR blocks define the expected IP address range of the hybrid nodes that join the cluster. These blocks are typically determined by your network administrator. </p> <p>Enter one or more IPv4 CIDR blocks in decimal dotted-quad notation (for example, <code> ********/16</code>).</p> <p>It must satisfy the following requirements:</p> <ul> <li> <p>Each block must be within an <code>IPv4</code> RFC-1918 network range. Minimum allowed size is /32, maximum allowed size is /8. Publicly-routable addresses aren't supported.</p> </li> <li> <p>Each block cannot overlap with the range of the VPC CIDR blocks for your EKS resources, or the block of the Kubernetes service IP range.</p> </li> <li> <p>Each block must have a route to the VPC that uses the VPC CIDR blocks, not public IPs or Elastic IPs. There are many options including Transit Gateway, Site-to-Site VPN, or Direct Connect.</p> </li> <li> <p>Each host must allow outbound connection to the EKS cluster control plane on TCP ports <code>443</code> and <code>10250</code>.</p> </li> <li> <p>Each host must allow inbound connection from the EKS cluster control plane on TCP port 10250 for logs, exec and port-forward operations.</p> </li> <li> <p> Each host must allow TCP and UDP network connectivity to and from other hosts that are running <code>CoreDNS</code> on UDP port <code>53</code> for service and pod DNS names.</p> </li> </ul>"}, "remotePodNetworks": {"shape": "RemotePodNetworkList", "documentation": "<p>The list of network CIDRs that can contain pods that run Kubernetes webhooks on hybrid nodes.</p> <p>These CIDR blocks are determined by configuring your Container Network Interface (CNI) plugin. We recommend the Calico CNI or Cilium CNI. Note that the Amazon VPC CNI plugin for Kubernetes isn't available for on-premises and edge locations.</p> <p>Enter one or more IPv4 CIDR blocks in decimal dotted-quad notation (for example, <code> ********/16</code>).</p> <p>It must satisfy the following requirements:</p> <ul> <li> <p>Each block must be within an <code>IPv4</code> RFC-1918 network range. Minimum allowed size is /32, maximum allowed size is /8. Publicly-routable addresses aren't supported.</p> </li> <li> <p>Each block cannot overlap with the range of the VPC CIDR blocks for your EKS resources, or the block of the Kubernetes service IP range.</p> </li> </ul>"}}, "documentation": "<p>The configuration in the cluster for EKS Hybrid Nodes. You can add, change, or remove this configuration after the cluster is created.</p>"}, "RemoteNetworkConfigResponse": {"type": "structure", "members": {"remoteNodeNetworks": {"shape": "RemoteNodeNetworkList", "documentation": "<p>The list of network CIDRs that can contain hybrid nodes.</p>"}, "remotePodNetworks": {"shape": "RemotePodNetworkList", "documentation": "<p>The list of network CIDRs that can contain pods that run Kubernetes webhooks on hybrid nodes.</p>"}}, "documentation": "<p>The configuration in the cluster for EKS Hybrid Nodes. You can add, change, or remove this configuration after the cluster is created.</p>"}, "RemoteNodeNetwork": {"type": "structure", "members": {"cidrs": {"shape": "StringList", "documentation": "<p>A network CIDR that can contain hybrid nodes.</p> <p>These CIDR blocks define the expected IP address range of the hybrid nodes that join the cluster. These blocks are typically determined by your network administrator. </p> <p>Enter one or more IPv4 CIDR blocks in decimal dotted-quad notation (for example, <code> ********/16</code>).</p> <p>It must satisfy the following requirements:</p> <ul> <li> <p>Each block must be within an <code>IPv4</code> RFC-1918 network range. Minimum allowed size is /32, maximum allowed size is /8. Publicly-routable addresses aren't supported.</p> </li> <li> <p>Each block cannot overlap with the range of the VPC CIDR blocks for your EKS resources, or the block of the Kubernetes service IP range.</p> </li> <li> <p>Each block must have a route to the VPC that uses the VPC CIDR blocks, not public IPs or Elastic IPs. There are many options including Transit Gateway, Site-to-Site VPN, or Direct Connect.</p> </li> <li> <p>Each host must allow outbound connection to the EKS cluster control plane on TCP ports <code>443</code> and <code>10250</code>.</p> </li> <li> <p>Each host must allow inbound connection from the EKS cluster control plane on TCP port 10250 for logs, exec and port-forward operations.</p> </li> <li> <p> Each host must allow TCP and UDP network connectivity to and from other hosts that are running <code>CoreDNS</code> on UDP port <code>53</code> for service and pod DNS names.</p> </li> </ul>"}}, "documentation": "<p>A network CIDR that can contain hybrid nodes.</p> <p>These CIDR blocks define the expected IP address range of the hybrid nodes that join the cluster. These blocks are typically determined by your network administrator. </p> <p>Enter one or more IPv4 CIDR blocks in decimal dotted-quad notation (for example, <code> ********/16</code>).</p> <p>It must satisfy the following requirements:</p> <ul> <li> <p>Each block must be within an <code>IPv4</code> RFC-1918 network range. Minimum allowed size is /32, maximum allowed size is /8. Publicly-routable addresses aren't supported.</p> </li> <li> <p>Each block cannot overlap with the range of the VPC CIDR blocks for your EKS resources, or the block of the Kubernetes service IP range.</p> </li> <li> <p>Each block must have a route to the VPC that uses the VPC CIDR blocks, not public IPs or Elastic IPs. There are many options including Transit Gateway, Site-to-Site VPN, or Direct Connect.</p> </li> <li> <p>Each host must allow outbound connection to the EKS cluster control plane on TCP ports <code>443</code> and <code>10250</code>.</p> </li> <li> <p>Each host must allow inbound connection from the EKS cluster control plane on TCP port 10250 for logs, exec and port-forward operations.</p> </li> <li> <p> Each host must allow TCP and UDP network connectivity to and from other hosts that are running <code>CoreDNS</code> on UDP port <code>53</code> for service and pod DNS names.</p> </li> </ul>"}, "RemoteNodeNetworkList": {"type": "list", "member": {"shape": "RemoteNodeNetwork"}, "max": 1}, "RemotePodNetwork": {"type": "structure", "members": {"cidrs": {"shape": "StringList", "documentation": "<p>A network CIDR that can contain pods that run Kubernetes webhooks on hybrid nodes.</p> <p>These CIDR blocks are determined by configuring your Container Network Interface (CNI) plugin. We recommend the Calico CNI or Cilium CNI. Note that the Amazon VPC CNI plugin for Kubernetes isn't available for on-premises and edge locations.</p> <p>Enter one or more IPv4 CIDR blocks in decimal dotted-quad notation (for example, <code> ********/16</code>).</p> <p>It must satisfy the following requirements:</p> <ul> <li> <p>Each block must be within an <code>IPv4</code> RFC-1918 network range. Minimum allowed size is /32, maximum allowed size is /8. Publicly-routable addresses aren't supported.</p> </li> <li> <p>Each block cannot overlap with the range of the VPC CIDR blocks for your EKS resources, or the block of the Kubernetes service IP range.</p> </li> </ul>"}}, "documentation": "<p>A network CIDR that can contain pods that run Kubernetes webhooks on hybrid nodes.</p> <p>These CIDR blocks are determined by configuring your Container Network Interface (CNI) plugin. We recommend the Calico CNI or Cilium CNI. Note that the Amazon VPC CNI plugin for Kubernetes isn't available for on-premises and edge locations.</p> <p>Enter one or more IPv4 CIDR blocks in decimal dotted-quad notation (for example, <code> ********/16</code>).</p> <p>It must satisfy the following requirements:</p> <ul> <li> <p>Each block must be within an <code>IPv4</code> RFC-1918 network range. Minimum allowed size is /32, maximum allowed size is /8. Publicly-routable addresses aren't supported.</p> </li> <li> <p>Each block cannot overlap with the range of the VPC CIDR blocks for your EKS resources, or the block of the Kubernetes service IP range.</p> </li> </ul>"}, "RemotePodNetworkList": {"type": "list", "member": {"shape": "RemotePodNetwork"}, "max": 1}, "ResolveConflicts": {"type": "string", "enum": ["OVERWRITE", "NONE", "PRESERVE"]}, "ResourceInUseException": {"type": "structure", "members": {"clusterName": {"shape": "String", "documentation": "<p>The Amazon EKS cluster associated with the exception.</p>"}, "nodegroupName": {"shape": "String", "documentation": "<p>The Amazon EKS managed node group associated with the exception.</p>"}, "addonName": {"shape": "String", "documentation": "<p>The specified add-on name is in use.</p>"}, "message": {"shape": "String", "documentation": "<p>The Amazon EKS message associated with the exception.</p>"}}, "documentation": "<p>The specified resource is in use.</p>", "error": {"httpStatusCode": 409}, "exception": true}, "ResourceLimitExceededException": {"type": "structure", "members": {"clusterName": {"shape": "String", "documentation": "<p>The Amazon EKS cluster associated with the exception.</p>"}, "nodegroupName": {"shape": "String", "documentation": "<p>The Amazon EKS managed node group associated with the exception.</p>"}, "subscriptionId": {"shape": "String", "documentation": "<p>The Amazon EKS subscription ID with the exception.</p>"}, "message": {"shape": "String", "documentation": "<p>The Amazon EKS message associated with the exception.</p>"}}, "documentation": "<p>You have encountered a service limit on the specified resource.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "ResourceNotFoundException": {"type": "structure", "members": {"clusterName": {"shape": "String", "documentation": "<p>The Amazon EKS cluster associated with the exception.</p>"}, "nodegroupName": {"shape": "String", "documentation": "<p>The Amazon EKS managed node group associated with the exception.</p>"}, "fargateProfileName": {"shape": "String", "documentation": "<p>The Fargate profile associated with the exception.</p>"}, "addonName": {"shape": "String", "documentation": "<p>The Amazon EKS add-on name associated with the exception.</p>"}, "subscriptionId": {"shape": "String", "documentation": "<p>The Amazon EKS subscription ID with the exception.</p>"}, "message": {"shape": "String", "documentation": "<p>The Amazon EKS message associated with the exception.</p>"}}, "documentation": "<p>The specified resource could not be found. You can view your available clusters with <code>ListClusters</code>. You can view your available managed node groups with <code>ListNodegroups</code>. Amazon EKS clusters and node groups are Amazon Web Services Region specific.</p>", "error": {"httpStatusCode": 404}, "exception": true}, "ResourcePropagationDelayException": {"type": "structure", "members": {"message": {"shape": "String", "documentation": "<p>Required resources (such as service-linked roles) were created and are still propagating. Retry later.</p>"}}, "documentation": "<p>Required resources (such as service-linked roles) were created and are still propagating. Retry later.</p>", "error": {"httpStatusCode": 428}, "exception": true}, "RoleArn": {"type": "string", "max": 255, "min": 1}, "ServerException": {"type": "structure", "members": {"clusterName": {"shape": "String", "documentation": "<p>The Amazon EKS cluster associated with the exception.</p>"}, "nodegroupName": {"shape": "String", "documentation": "<p>The Amazon EKS managed node group associated with the exception.</p>"}, "addonName": {"shape": "String", "documentation": "<p>The Amazon EKS add-on name associated with the exception.</p>"}, "subscriptionId": {"shape": "String", "documentation": "<p>The Amazon EKS subscription ID with the exception.</p>"}, "message": {"shape": "String", "documentation": "<p>These errors are usually caused by a server-side issue.</p>"}}, "documentation": "<p>These errors are usually caused by a server-side issue.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true}, "ServiceUnavailableException": {"type": "structure", "members": {"message": {"shape": "String", "documentation": "<p>The request has failed due to a temporary failure of the server.</p>"}}, "documentation": "<p>The service is unavailable. Back off and retry the operation.</p>", "error": {"httpStatusCode": 503}, "exception": true, "fault": true}, "StorageConfigRequest": {"type": "structure", "members": {"blockStorage": {"shape": "BlockStorage", "documentation": "<p>Request to configure EBS Block Storage settings for your EKS Auto Mode cluster.</p>"}}, "documentation": "<p>Request to update the configuration of the storage capability of your EKS Auto Mode cluster. For example, enable the capability. For more information, see EKS Auto Mode block storage capability in the <i>Amazon EKS User Guide</i>.</p>"}, "StorageConfigResponse": {"type": "structure", "members": {"blockStorage": {"shape": "BlockStorage", "documentation": "<p>Indicates the current configuration of the block storage capability on your EKS Auto Mode cluster. For example, if the capability is enabled or disabled.</p>"}}, "documentation": "<p>Indicates the status of the request to update the block storage capability of your EKS Auto Mode cluster.</p>"}, "String": {"type": "string"}, "StringList": {"type": "list", "member": {"shape": "String"}}, "SupportType": {"type": "string", "enum": ["STANDARD", "EXTENDED"]}, "TagKey": {"type": "string", "documentation": "<p>One part of a key-value pair that make up a tag. A <code>key</code> is a general label that acts like a category for more specific tag values.</p>", "max": 128, "min": 1}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 50, "min": 1}, "TagMap": {"type": "map", "key": {"shape": "TagKey"}, "value": {"shape": "TagValue"}, "documentation": "<p>The metadata that you apply to a resource to help you categorize and organize them. Each tag consists of a key and an optional value. You define them.</p> <p>The following basic restrictions apply to tags:</p> <ul> <li> <p>Maximum number of tags per resource – 50</p> </li> <li> <p>For each resource, each tag key must be unique, and each tag key can have only one value.</p> </li> <li> <p>Maximum key length – 128 Unicode characters in UTF-8</p> </li> <li> <p>Maximum value length – 256 Unicode characters in UTF-8</p> </li> <li> <p>If your tagging schema is used across multiple services and resources, remember that other services may have restrictions on allowed characters. Generally allowed characters are: letters, numbers, and spaces representable in UTF-8, and the following characters: + - = . _ : / @.</p> </li> <li> <p>Tag keys and values are case-sensitive.</p> </li> <li> <p>Do not use <code>aws:</code>, <code>AWS:</code>, or any upper or lowercase combination of such as a prefix for either keys or values as it is reserved for Amazon Web Services use. You cannot edit or delete tag keys or values with this prefix. Tags with this prefix do not count against your tags per resource limit.</p> </li> </ul>", "max": 50, "min": 1}, "TagResourceRequest": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the resource to add tags to.</p>", "location": "uri", "locationName": "resourceArn"}, "tags": {"shape": "TagMap", "documentation": "<p>Metadata that assists with categorization and organization. Each tag consists of a key and an optional value. You define both. Tags don't propagate to any other cluster or Amazon Web Services resources.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "documentation": "<p>The optional part of a key-value pair that make up a tag. A <code>value</code> acts as a descriptor within a tag category (key).</p>", "max": 256}, "Taint": {"type": "structure", "members": {"key": {"shape": "taint<PERSON>ey", "documentation": "<p>The key of the taint.</p>"}, "value": {"shape": "taintValue", "documentation": "<p>The value of the taint.</p>"}, "effect": {"shape": "TaintEffect", "documentation": "<p>The effect of the taint.</p>"}}, "documentation": "<p>A property that allows a node to repel a <code>Pod</code>. For more information, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/node-taints-managed-node-groups.html\">Node taints on managed node groups</a> in the <i>Amazon EKS User Guide</i>.</p>"}, "TaintEffect": {"type": "string", "enum": ["NO_SCHEDULE", "NO_EXECUTE", "PREFER_NO_SCHEDULE"]}, "ThrottlingException": {"type": "structure", "members": {"clusterName": {"shape": "String", "documentation": "<p>The Amazon EKS cluster associated with the exception.</p>"}, "message": {"shape": "String"}}, "documentation": "<p>The request or operation couldn't be performed because a service is throttling requests.</p>", "error": {"httpStatusCode": 429}, "exception": true}, "Timestamp": {"type": "timestamp"}, "UnsupportedAvailabilityZoneException": {"type": "structure", "members": {"message": {"shape": "String", "documentation": "<p>At least one of your specified cluster subnets is in an Availability Zone that does not support Amazon EKS. The exception output specifies the supported Availability Zones for your account, from which you can choose subnets for your cluster.</p>"}, "clusterName": {"shape": "String", "documentation": "<p>The Amazon EKS cluster associated with the exception.</p>"}, "nodegroupName": {"shape": "String", "documentation": "<p>The Amazon EKS managed node group associated with the exception.</p>"}, "validZones": {"shape": "StringList", "documentation": "<p>The supported Availability Zones for your account. Choose subnets in these Availability Zones for your cluster.</p>"}}, "documentation": "<p>At least one of your specified cluster subnets is in an Availability Zone that does not support Amazon EKS. The exception output specifies the supported Availability Zones for your account, from which you can choose subnets for your cluster.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "UntagResourceRequest": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the resource to delete tags from.</p>", "location": "uri", "locationName": "resourceArn"}, "tagKeys": {"shape": "TagKeyList", "documentation": "<p>The keys of the tags to remove.</p>", "location": "querystring", "locationName": "tagKeys"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "Update": {"type": "structure", "members": {"id": {"shape": "String", "documentation": "<p>A UUID that is used to track the update.</p>"}, "status": {"shape": "UpdateStatus", "documentation": "<p>The current status of the update.</p>"}, "type": {"shape": "UpdateType", "documentation": "<p>The type of the update.</p>"}, "params": {"shape": "UpdateParams", "documentation": "<p>A key-value map that contains the parameters associated with the update.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The Unix epoch timestamp at object creation.</p>"}, "errors": {"shape": "ErrorDetails", "documentation": "<p>Any errors associated with a <code>Failed</code> update.</p>"}}, "documentation": "<p>An object representing an asynchronous update.</p>"}, "UpdateAccessConfigRequest": {"type": "structure", "members": {"authenticationMode": {"shape": "AuthenticationMode", "documentation": "<p>The desired authentication mode for the cluster.</p>"}}, "documentation": "<p>The access configuration information for the cluster.</p>"}, "UpdateAccessEntryRequest": {"type": "structure", "required": ["clusterName", "principalArn"], "members": {"clusterName": {"shape": "String", "documentation": "<p>The name of your cluster.</p>", "location": "uri", "locationName": "name"}, "principalArn": {"shape": "String", "documentation": "<p>The ARN of the IAM principal for the <code>AccessEntry</code>.</p>", "location": "uri", "locationName": "principalArn"}, "kubernetesGroups": {"shape": "StringList", "documentation": "<p>The value for <code>name</code> that you've specified for <code>kind: Group</code> as a <code>subject</code> in a Kubernetes <code>RoleBinding</code> or <code>ClusterRoleBinding</code> object. Amazon EKS doesn't confirm that the value for <code>name</code> exists in any bindings on your cluster. You can specify one or more names.</p> <p>Kubernetes authorizes the <code>principalArn</code> of the access entry to access any cluster objects that you've specified in a Kubernetes <code>Role</code> or <code>ClusterRole</code> object that is also specified in a binding's <code>roleRef</code>. For more information about creating Kubernetes <code>RoleBinding</code>, <code>ClusterRoleBinding</code>, <code>Role</code>, or <code>ClusterRole</code> objects, see <a href=\"https://kubernetes.io/docs/reference/access-authn-authz/rbac/\">Using RBAC Authorization in the Kubernetes documentation</a>.</p> <p>If you want Amazon EKS to authorize the <code>principalArn</code> (instead of, or in addition to Kubernetes authorizing the <code>principalArn</code>), you can associate one or more access policies to the access entry using <code>AssociateAccessPolicy</code>. If you associate any access policies, the <code>principalARN</code> has all permissions assigned in the associated access policies and all permissions in any Kubernetes <code>Role</code> or <code>ClusterRole</code> objects that the group names are bound to.</p>"}, "clientRequestToken": {"shape": "String", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency of the request.</p>", "idempotencyToken": true}, "username": {"shape": "String", "documentation": "<p>The username to authenticate to Kuber<PERSON><PERSON> with. We recommend not specifying a username and letting Amazon EKS specify it for you. For more information about the value Amazon EKS specifies for you, or constraints before specifying your own username, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/access-entries.html#creating-access-entries\">Creating access entries</a> in the <i>Amazon EKS User Guide</i>.</p>"}}}, "UpdateAccessEntryResponse": {"type": "structure", "members": {"accessEntry": {"shape": "AccessEntry", "documentation": "<p>The ARN of the IAM principal for the <code>AccessEntry</code>.</p>"}}}, "UpdateAddonRequest": {"type": "structure", "required": ["clusterName", "addonName"], "members": {"clusterName": {"shape": "ClusterName", "documentation": "<p>The name of your cluster.</p>", "location": "uri", "locationName": "name"}, "addonName": {"shape": "String", "documentation": "<p>The name of the add-on. The name must match one of the names returned by <a href=\"https://docs.aws.amazon.com/eks/latest/APIReference/API_ListAddons.html\"> <code>ListAddons</code> </a>.</p>", "location": "uri", "locationName": "addonName"}, "addonVersion": {"shape": "String", "documentation": "<p>The version of the add-on. The version must match one of the versions returned by <a href=\"https://docs.aws.amazon.com/eks/latest/APIReference/API_DescribeAddonVersions.html\"> <code>DescribeAddonVersions</code> </a>.</p>"}, "serviceAccountRoleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of an existing IAM role to bind to the add-on's service account. The role must be assigned the IAM permissions required by the add-on. If you don't specify an existing IAM role, then the add-on uses the permissions assigned to the node IAM role. For more information, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/create-node-role.html\">Amazon EKS node IAM role</a> in the <i>Amazon EKS User Guide</i>.</p> <note> <p>To specify an existing IAM role, you must have an IAM OpenID Connect (OIDC) provider created for your cluster. For more information, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/enable-iam-roles-for-service-accounts.html\">Enabling IAM roles for service accounts on your cluster</a> in the <i>Amazon EKS User Guide</i>.</p> </note>"}, "resolveConflicts": {"shape": "ResolveConflicts", "documentation": "<p>How to resolve field value conflicts for an Amazon EKS add-on if you've changed a value from the Amazon EKS default value. Conflicts are handled based on the option you choose:</p> <ul> <li> <p> <b>None</b> – Amazon EKS doesn't change the value. The update might fail.</p> </li> <li> <p> <b>Overwrite</b> – Amazon EKS overwrites the changed value back to the Amazon EKS default value.</p> </li> <li> <p> <b>Preserve</b> – Amazon EKS preserves the value. If you choose this option, we recommend that you test any field and value changes on a non-production cluster before updating the add-on on your production cluster.</p> </li> </ul>"}, "clientRequestToken": {"shape": "String", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency of the request.</p>", "idempotencyToken": true}, "configurationValues": {"shape": "String", "documentation": "<p>The set of configuration values for the add-on that's created. The values that you provide are validated against the schema returned by <code>DescribeAddonConfiguration</code>.</p>"}, "podIdentityAssociations": {"shape": "AddonPodIdentityAssociationsList", "documentation": "<p>An array of EKS Pod Identity associations to be updated. Each association maps a Kubernetes service account to an IAM role. If this value is left blank, no change. If an empty array is provided, existing associations owned by the add-on are deleted.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/add-ons-iam.html\">Attach an IAM Role to an Amazon EKS add-on using EKS Pod Identity</a> in the <i>Amazon EKS User Guide</i>.</p>"}}}, "UpdateAddonResponse": {"type": "structure", "members": {"update": {"shape": "Update"}}}, "UpdateClusterConfigRequest": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "String", "documentation": "<p>The name of the Amazon EKS cluster to update.</p>", "location": "uri", "locationName": "name"}, "resourcesVpcConfig": {"shape": "VpcConfigRequest"}, "logging": {"shape": "Logging", "documentation": "<p>Enable or disable exporting the Kubernetes control plane logs for your cluster to CloudWatch Logs . By default, cluster control plane logs aren't exported to CloudWatch Logs . For more information, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/control-plane-logs.html\">Amazon EKS cluster control plane logs</a> in the <i> <i>Amazon EKS User Guide</i> </i>.</p> <note> <p>CloudWatch Logs ingestion, archive storage, and data scanning rates apply to exported control plane logs. For more information, see <a href=\"http://aws.amazon.com/cloudwatch/pricing/\">CloudWatch Pricing</a>.</p> </note>"}, "clientRequestToken": {"shape": "String", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency of the request.</p>", "idempotencyToken": true}, "accessConfig": {"shape": "UpdateAccessConfigRequest", "documentation": "<p>The access configuration for the cluster.</p>"}, "upgradePolicy": {"shape": "UpgradePolicyRequest", "documentation": "<p>You can enable or disable extended support for clusters currently on standard support. You cannot disable extended support once it starts. You must enable extended support before your cluster exits standard support.</p>"}, "zonalShiftConfig": {"shape": "ZonalShiftConfigRequest", "documentation": "<p>Enable or disable ARC zonal shift for the cluster. If zonal shift is enabled, Amazon Web Services configures zonal autoshift for the cluster.</p> <p>Zonal shift is a feature of Amazon Application Recovery Controller (ARC). ARC zonal shift is designed to be a temporary measure that allows you to move traffic for a resource away from an impaired AZ until the zonal shift expires or you cancel it. You can extend the zonal shift if necessary.</p> <p>You can start a zonal shift for an EKS cluster, or you can allow Amazon Web Services to do it for you by enabling <i>zonal autoshift</i>. This shift updates the flow of east-to-west network traffic in your cluster to only consider network endpoints for Pods running on worker nodes in healthy AZs. Additionally, any ALB or NLB handling ingress traffic for applications in your EKS cluster will automatically route traffic to targets in the healthy AZs. For more information about zonal shift in EKS, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/zone-shift.html\">Learn about Amazon Application Recovery Controller (ARC) Zonal Shift in Amazon EKS</a> in the <i> <i>Amazon EKS User Guide</i> </i>.</p>"}, "computeConfig": {"shape": "ComputeConfigRequest", "documentation": "<p>Update the configuration of the compute capability of your EKS Auto Mode cluster. For example, enable the capability.</p>"}, "kubernetesNetworkConfig": {"shape": "KubernetesNetworkConfigRequest"}, "storageConfig": {"shape": "StorageConfigRequest", "documentation": "<p>Update the configuration of the block storage capability of your EKS Auto Mode cluster. For example, enable the capability.</p>"}, "remoteNetworkConfig": {"shape": "RemoteNetworkConfigRequest"}}}, "UpdateClusterConfigResponse": {"type": "structure", "members": {"update": {"shape": "Update"}}}, "UpdateClusterVersionRequest": {"type": "structure", "required": ["name", "version"], "members": {"name": {"shape": "String", "documentation": "<p>The name of the Amazon EKS cluster to update.</p>", "location": "uri", "locationName": "name"}, "version": {"shape": "String", "documentation": "<p>The desired Kubernetes version following a successful update.</p>"}, "clientRequestToken": {"shape": "String", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency of the request.</p>", "idempotencyToken": true}, "force": {"shape": "Boolean", "documentation": "<p>Set this value to <code>true</code> to override upgrade-blocking readiness checks when updating a cluster.</p>"}}}, "UpdateClusterVersionResponse": {"type": "structure", "members": {"update": {"shape": "Update", "documentation": "<p>The full description of the specified update</p>"}}}, "UpdateEksAnywhereSubscriptionRequest": {"type": "structure", "required": ["id", "autoRenew"], "members": {"id": {"shape": "String", "documentation": "<p>The ID of the subscription.</p>", "location": "uri", "locationName": "id"}, "autoRenew": {"shape": "Boolean", "documentation": "<p>A boolean indicating whether or not to automatically renew the subscription.</p>"}, "clientRequestToken": {"shape": "String", "documentation": "<p>Unique, case-sensitive identifier to ensure the idempotency of the request.</p>", "idempotencyToken": true}}}, "UpdateEksAnywhereSubscriptionResponse": {"type": "structure", "members": {"subscription": {"shape": "EksAnywhereSubscription", "documentation": "<p>The full description of the updated subscription.</p>"}}}, "UpdateLabelsPayload": {"type": "structure", "members": {"addOrUpdateLabels": {"shape": "labelsMap", "documentation": "<p>The Kubernetes <code>labels</code> to add or update.</p>"}, "removeLabels": {"shape": "labelsKeyList", "documentation": "<p>The Kubernetes <code>labels</code> to remove.</p>"}}, "documentation": "<p>An object representing a Kubernetes <code>label</code> change for a managed node group.</p>"}, "UpdateNodegroupConfigRequest": {"type": "structure", "required": ["clusterName", "nodegroupName"], "members": {"clusterName": {"shape": "String", "documentation": "<p>The name of your cluster.</p>", "location": "uri", "locationName": "name"}, "nodegroupName": {"shape": "String", "documentation": "<p>The name of the managed node group to update.</p>", "location": "uri", "locationName": "nodegroupName"}, "labels": {"shape": "UpdateLabelsPayload", "documentation": "<p>The Kubernetes <code>labels</code> to apply to the nodes in the node group after the update.</p>"}, "taints": {"shape": "UpdateTaintsPayload", "documentation": "<p>The Kubernetes taints to be applied to the nodes in the node group after the update. For more information, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/node-taints-managed-node-groups.html\">Node taints on managed node groups</a>.</p>"}, "scalingConfig": {"shape": "NodegroupScalingConfig", "documentation": "<p>The scaling configuration details for the Auto Scaling group after the update.</p>"}, "updateConfig": {"shape": "NodegroupUpdateConfig", "documentation": "<p>The node group update configuration.</p>"}, "nodeRepairConfig": {"shape": "NodeRepairConfig", "documentation": "<p>The node auto repair configuration for the node group.</p>"}, "clientRequestToken": {"shape": "String", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency of the request.</p>", "idempotencyToken": true}}}, "UpdateNodegroupConfigResponse": {"type": "structure", "members": {"update": {"shape": "Update"}}}, "UpdateNodegroupVersionRequest": {"type": "structure", "required": ["clusterName", "nodegroupName"], "members": {"clusterName": {"shape": "String", "documentation": "<p>The name of your cluster.</p>", "location": "uri", "locationName": "name"}, "nodegroupName": {"shape": "String", "documentation": "<p>The name of the managed node group to update.</p>", "location": "uri", "locationName": "nodegroupName"}, "version": {"shape": "String", "documentation": "<p>The Kubernetes version to update to. If no version is specified, then the Kubernetes version of the node group does not change. You can specify the Kubernetes version of the cluster to update the node group to the latest AMI version of the cluster's Kubernetes version. If you specify <code>launchTemplate</code>, and your launch template uses a custom AMI, then don't specify <code>version</code>, or the node group update will fail. For more information about using launch templates with Amazon EKS, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/launch-templates.html\">Customizing managed nodes with launch templates</a> in the <i>Amazon EKS User Guide</i>.</p>"}, "releaseVersion": {"shape": "String", "documentation": "<p>The AMI version of the Amazon EKS optimized AMI to use for the update. By default, the latest available AMI version for the node group's Kubernetes version is used. For information about Linux versions, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/eks-linux-ami-versions.html\">Amazon EKS optimized Amazon Linux AMI versions</a> in the <i>Amazon EKS User Guide</i>. Amazon EKS managed node groups support the November 2022 and later releases of the Windows AMIs. For information about Windows versions, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/eks-ami-versions-windows.html\">Amazon EKS optimized Windows AMI versions</a> in the <i>Amazon EKS User Guide</i>.</p> <p>If you specify <code>launchTemplate</code>, and your launch template uses a custom AMI, then don't specify <code>releaseVersion</code>, or the node group update will fail. For more information about using launch templates with Amazon EKS, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/launch-templates.html\">Customizing managed nodes with launch templates</a> in the <i>Amazon EKS User Guide</i>.</p>"}, "launchTemplate": {"shape": "LaunchTemplateSpecification", "documentation": "<p>An object representing a node group's launch template specification. You can only update a node group using a launch template if the node group was originally deployed with a launch template. When updating, you must specify the same launch template ID or name that was used to create the node group.</p>"}, "force": {"shape": "Boolean", "documentation": "<p>Force the update if any <code>Pod</code> on the existing node group can't be drained due to a <code>Pod</code> disruption budget issue. If an update fails because all Pods can't be drained, you can force the update after it fails to terminate the old node whether or not any <code>Pod</code> is running on the node.</p>"}, "clientRequestToken": {"shape": "String", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency of the request.</p>", "idempotencyToken": true}}}, "UpdateNodegroupVersionResponse": {"type": "structure", "members": {"update": {"shape": "Update"}}}, "UpdateParam": {"type": "structure", "members": {"type": {"shape": "UpdateParamType", "documentation": "<p>The keys associated with an update request.</p>"}, "value": {"shape": "String", "documentation": "<p>The value of the keys submitted as part of an update request.</p>"}}, "documentation": "<p>An object representing the details of an update request.</p>"}, "UpdateParamType": {"type": "string", "enum": ["Version", "PlatformVersion", "EndpointPrivateAccess", "EndpointPublicAccess", "ClusterLogging", "DesiredSize", "LabelsToAdd", "LabelsToRemove", "TaintsToAdd", "TaintsToRemove", "MaxSize", "MinSize", "ReleaseVersion", "PublicAccessCidrs", "LaunchTemplateName", "LaunchTemplateVersion", "IdentityProviderConfig", "EncryptionConfig", "AddonVersion", "ServiceAccountRoleArn", "ResolveConflicts", "MaxUnavailable", "MaxUnavailablePercentage", "NodeRepairEnabled", "UpdateStrategy", "ConfigurationValues", "SecurityGroups", "Subnets", "AuthenticationMode", "PodIdentityAssociations", "UpgradePolicy", "ZonalShiftConfig", "ComputeConfig", "StorageConfig", "KubernetesNetworkConfig", "RemoteNetworkConfig"]}, "UpdateParams": {"type": "list", "member": {"shape": "UpdateParam"}}, "UpdatePodIdentityAssociationRequest": {"type": "structure", "required": ["clusterName", "associationId"], "members": {"clusterName": {"shape": "String", "documentation": "<p>The name of the cluster that you want to update the association in.</p>", "location": "uri", "locationName": "name"}, "associationId": {"shape": "String", "documentation": "<p>The ID of the association to be updated.</p>", "location": "uri", "locationName": "associationId"}, "roleArn": {"shape": "String", "documentation": "<p>The new IAM role to change in the association.</p>"}, "clientRequestToken": {"shape": "String", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency of the request.</p>", "idempotencyToken": true}, "disableSessionTags": {"shape": "BoxedBoolean", "documentation": "<p>Disable the automatic sessions tags that are appended by EKS Pod Identity.</p> <p>EKS Pod Identity adds a pre-defined set of session tags when it assumes the role. You can use these tags to author a single role that can work across resources by allowing access to Amazon Web Services resources based on matching tags. By default, EKS Pod Identity attaches six tags, including tags for cluster name, namespace, and service account name. For the list of tags added by EKS Pod Identity, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/pod-id-abac.html#pod-id-abac-tags\">List of session tags added by EKS Pod Identity</a> in the <i>Amazon EKS User Guide</i>.</p> <p>Amazon Web Services compresses inline session policies, managed policy ARNs, and session tags into a packed binary format that has a separate limit. If you receive a <code>PackedPolicyTooLarge</code> error indicating the packed binary format has exceeded the size limit, you can attempt to reduce the size by disabling the session tags added by EKS Pod Identity.</p>"}, "targetRoleArn": {"shape": "String", "documentation": "<p>The Amazon Resource Name (ARN) of the target IAM role to associate with the service account. This role is assumed by using the EKS Pod Identity association role, then the credentials for this role are injected into the Pod.</p> <p>When you run applications on Amazon EKS, your application might need to access Amazon Web Services resources from a different role that exists in the same or different Amazon Web Services account. For example, your application running in “Account A” might need to access resources, such as buckets in “Account B” or within “Account A” itself. You can create a association to access Amazon Web Services resources in “Account B” by creating two IAM roles: a role in “Account A” and a role in “Account B” (which can be the same or different account), each with the necessary trust and permission policies. After you provide these roles in the <i>IAM role</i> and <i>Target IAM role</i> fields, EKS will perform role chaining to ensure your application gets the required permissions. This means Role A will assume Role B, allowing your Pods to securely access resources like S3 buckets in the target account.</p>"}}}, "UpdatePodIdentityAssociationResponse": {"type": "structure", "members": {"association": {"shape": "PodIdentityAssociation", "documentation": "<p>The full description of the association that was updated.</p>"}}}, "UpdateStatus": {"type": "string", "enum": ["InProgress", "Failed", "Cancelled", "Successful"]}, "UpdateTaintsPayload": {"type": "structure", "members": {"addOrUpdateTaints": {"shape": "taintsList", "documentation": "<p>Kubernetes taints to be added or updated.</p>"}, "removeTaints": {"shape": "taintsList", "documentation": "<p>Kubernetes taints to remove.</p>"}}, "documentation": "<p>An object representing the details of an update to a taints payload. For more information, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/node-taints-managed-node-groups.html\">Node taints on managed node groups</a> in the <i>Amazon EKS User Guide</i>.</p>"}, "UpdateType": {"type": "string", "enum": ["VersionUpdate", "EndpointAccessUpdate", "LoggingUpdate", "ConfigUpdate", "AssociateIdentityProviderConfig", "DisassociateIdentityProviderConfig", "AssociateEncryptionConfig", "AddonUpdate", "VpcConfigUpdate", "AccessConfigUpdate", "UpgradePolicyUpdate", "ZonalShiftConfigUpdate", "AutoModeUpdate", "RemoteNetworkConfigUpdate"]}, "UpgradePolicyRequest": {"type": "structure", "members": {"supportType": {"shape": "SupportType", "documentation": "<p>If the cluster is set to <code>EXTENDED</code>, it will enter extended support at the end of standard support. If the cluster is set to <code>STANDARD</code>, it will be automatically upgraded at the end of standard support.</p> <p> <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/extended-support-control.html\">Learn more about EKS Extended Support in the <i>Amazon EKS User Guide</i>.</a> </p>"}}, "documentation": "<p>The support policy to use for the cluster. Extended support allows you to remain on specific Kubernetes versions for longer. Clusters in extended support have higher costs. The default value is <code>EXTENDED</code>. Use <code>STANDARD</code> to disable extended support.</p> <p> <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/extended-support-control.html\">Learn more about EKS Extended Support in the <i>Amazon EKS User Guide</i>.</a> </p>"}, "UpgradePolicyResponse": {"type": "structure", "members": {"supportType": {"shape": "SupportType", "documentation": "<p>If the cluster is set to <code>EXTENDED</code>, it will enter extended support at the end of standard support. If the cluster is set to <code>STANDARD</code>, it will be automatically upgraded at the end of standard support.</p> <p> <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/extended-support-control.html\">Learn more about EKS Extended Support in the <i>Amazon EKS User Guide</i>.</a> </p>"}}, "documentation": "<p>This value indicates if extended support is enabled or disabled for the cluster.</p> <p> <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/extended-support-control.html\">Learn more about EKS Extended Support in the <i>Amazon EKS User Guide</i>.</a> </p>"}, "VersionStatus": {"type": "string", "enum": ["UNSUPPORTED", "STANDARD_SUPPORT", "EXTENDED_SUPPORT"]}, "VpcConfigRequest": {"type": "structure", "members": {"subnetIds": {"shape": "StringList", "documentation": "<p>Specify subnets for your Amazon EKS nodes. Amazon EKS creates cross-account elastic network interfaces in these subnets to allow communication between your nodes and the Kubernetes control plane.</p>"}, "securityGroupIds": {"shape": "StringList", "documentation": "<p>Specify one or more security groups for the cross-account elastic network interfaces that Amazon EKS creates to use that allow communication between your nodes and the Kubernetes control plane. If you don't specify any security groups, then familiarize yourself with the difference between Amazon EKS defaults for clusters deployed with Kubernetes. For more information, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/sec-group-reqs.html\">Amazon EKS security group considerations</a> in the <i> <i>Amazon EKS User Guide</i> </i>.</p>"}, "endpointPublicAccess": {"shape": "BoxedBoolean", "documentation": "<p>Set this value to <code>false</code> to disable public access to your cluster's Kubernetes API server endpoint. If you disable public access, your cluster's Kubernetes API server can only receive requests from within the cluster VPC. The default value for this parameter is <code>true</code>, which enables public access for your Kubernetes API server. The endpoint domain name and IP address family depends on the value of the <code>ipFamily</code> for the cluster. For more information, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/cluster-endpoint.html\">Cluster API server endpoint</a> in the <i> <i>Amazon EKS User Guide</i> </i>.</p>"}, "endpointPrivateAccess": {"shape": "BoxedBoolean", "documentation": "<p>Set this value to <code>true</code> to enable private access for your cluster's Kubernetes API server endpoint. If you enable private access, Kubernetes API requests from within your cluster's VPC use the private VPC endpoint. The default value for this parameter is <code>false</code>, which disables private access for your Kubernetes API server. If you disable private access and you have nodes or Fargate pods in the cluster, then ensure that <code>publicAccessCidrs</code> includes the necessary CIDR blocks for communication with the nodes or Fargate pods. For more information, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/cluster-endpoint.html\">Cluster API server endpoint</a> in the <i> <i>Amazon EKS User Guide</i> </i>.</p>"}, "publicAccessCidrs": {"shape": "StringList", "documentation": "<p>The CIDR blocks that are allowed access to your cluster's public Kubernetes API server endpoint. Communication to the endpoint from addresses outside of the CIDR blocks that you specify is denied. The default value is <code>0.0.0.0/0</code> and additionally <code>::/0</code> for dual-stack `IPv6` clusters. If you've disabled private endpoint access, make sure that you specify the necessary CIDR blocks for every node and Fargate <code>Pod</code> in the cluster. For more information, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/cluster-endpoint.html\">Cluster API server endpoint</a> in the <i> <i>Amazon EKS User Guide</i> </i>.</p> <p>Note that the public endpoints are dual-stack for only <code>IPv6</code> clusters that are made after October 2024. You can't add <code>IPv6</code> CIDR blocks to <code>IPv4</code> clusters or <code>IPv6</code> clusters that were made before October 2024.</p>"}}, "documentation": "<p>An object representing the VPC configuration to use for an Amazon EKS cluster.</p>"}, "VpcConfigResponse": {"type": "structure", "members": {"subnetIds": {"shape": "StringList", "documentation": "<p>The subnets associated with your cluster.</p>"}, "securityGroupIds": {"shape": "StringList", "documentation": "<p>The security groups associated with the cross-account elastic network interfaces that are used to allow communication between your nodes and the Kubernetes control plane.</p>"}, "clusterSecurityGroupId": {"shape": "String", "documentation": "<p>The cluster security group that was created by Amazon EKS for the cluster. Managed node groups use this security group for control-plane-to-data-plane communication.</p>"}, "vpcId": {"shape": "String", "documentation": "<p>The VPC associated with your cluster.</p>"}, "endpointPublicAccess": {"shape": "Boolean", "documentation": "<p>Whether the public API server endpoint is enabled.</p>"}, "endpointPrivateAccess": {"shape": "Boolean", "documentation": "<p>This parameter indicates whether the Amazon EKS private API server endpoint is enabled. If the Amazon EKS private API server endpoint is enabled, Kubernetes API requests that originate from within your cluster's VPC use the private VPC endpoint instead of traversing the internet. If this value is disabled and you have nodes or Fargate pods in the cluster, then ensure that <code>publicAccessCidrs</code> includes the necessary CIDR blocks for communication with the nodes or Fargate pods. For more information, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/cluster-endpoint.html\">Cluster API server endpoint</a> in the <i> <i>Amazon EKS User Guide</i> </i>.</p>"}, "publicAccessCidrs": {"shape": "StringList", "documentation": "<p>The CIDR blocks that are allowed access to your cluster's public Kubernetes API server endpoint. Communication to the endpoint from addresses outside of the CIDR blocks that you specify is denied. The default value is <code>0.0.0.0/0</code> and additionally <code>::/0</code> for dual-stack `IPv6` clusters. If you've disabled private endpoint access, make sure that you specify the necessary CIDR blocks for every node and Fargate <code>Pod</code> in the cluster. For more information, see <a href=\"https://docs.aws.amazon.com/eks/latest/userguide/cluster-endpoint.html\">Cluster API server endpoint</a> in the <i> <i>Amazon EKS User Guide</i> </i>.</p> <p>Note that the public endpoints are dual-stack for only <code>IPv6</code> clusters that are made after October 2024. You can't add <code>IPv6</code> CIDR blocks to <code>IPv4</code> clusters or <code>IPv6</code> clusters that were made before October 2024.</p>"}}, "documentation": "<p>An object representing an Amazon EKS cluster VPC configuration response.</p>"}, "ZeroCapacity": {"type": "integer", "box": true, "min": 0}, "ZonalShiftConfigRequest": {"type": "structure", "members": {"enabled": {"shape": "BoxedBoolean", "documentation": "<p>If zonal shift is enabled, Amazon Web Services configures zonal autoshift for the cluster.</p>"}}, "documentation": "<p>The configuration for zonal shift for the cluster.</p>"}, "ZonalShiftConfigResponse": {"type": "structure", "members": {"enabled": {"shape": "BoxedBoolean", "documentation": "<p>Whether the zonal shift is enabled.</p>"}}, "documentation": "<p>The status of zonal shift configuration for the cluster</p>"}, "configStatus": {"type": "string", "enum": ["CREATING", "DELETING", "ACTIVE"]}, "labelKey": {"type": "string", "max": 63, "min": 1}, "labelValue": {"type": "string", "max": 63, "min": 1}, "labelsKeyList": {"type": "list", "member": {"shape": "String"}}, "labelsMap": {"type": "map", "key": {"shape": "labelKey"}, "value": {"shape": "labelValue"}}, "requiredClaimsKey": {"type": "string", "max": 63, "min": 1}, "requiredClaimsMap": {"type": "map", "key": {"shape": "requiredClaimsKey"}, "value": {"shape": "requiredClaimsValue"}}, "requiredClaimsValue": {"type": "string", "max": 253, "min": 1}, "taintKey": {"type": "string", "max": 63, "min": 1}, "taintValue": {"type": "string", "max": 63, "min": 0}, "taintsList": {"type": "list", "member": {"shape": "<PERSON>nt"}}}, "documentation": "<p>Amazon Elastic Kubernetes Service (Amazon EKS) is a managed service that makes it easy for you to run Kubernetes on Amazon Web Services without needing to setup or maintain your own Kubernetes control plane. Kubernetes is an open-source system for automating the deployment, scaling, and management of containerized applications.</p> <p>Amazon EKS runs up-to-date versions of the open-source Kubernetes software, so you can use all the existing plugins and tooling from the Kubernetes community. Applications running on Amazon EKS are fully compatible with applications running on any standard Kubernetes environment, whether running in on-premises data centers or public clouds. This means that you can easily migrate any standard Kubernetes application to Amazon EKS without any code modification required.</p>"}