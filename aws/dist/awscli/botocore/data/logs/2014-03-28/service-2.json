{"version": "2.0", "metadata": {"apiVersion": "2014-03-28", "endpointPrefix": "logs", "jsonVersion": "1.1", "protocol": "json", "protocols": ["json"], "serviceFullName": "Amazon CloudWatch Logs", "serviceId": "CloudWatch Logs", "signatureVersion": "v4", "targetPrefix": "Logs_20140328", "uid": "logs-2014-03-28", "auth": ["aws.auth#sigv4"]}, "operations": {"AssociateKmsKey": {"name": "Associate<PERSON><PERSON><PERSON><PERSON>", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AssociateKmsKeyRequest"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}, {"shape": "OperationAbortedException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Associates the specified KMS key with either one log group in the account, or with all stored CloudWatch Logs query insights results in the account.</p> <p>When you use <code>AssociateKmsKey</code>, you specify either the <code>logGroupName</code> parameter or the <code>resourceIdentifier</code> parameter. You can't specify both of those parameters in the same operation.</p> <ul> <li> <p>Specify the <code>logGroupName</code> parameter to cause log events ingested into that log group to be encrypted with that key. Only the log events ingested after the key is associated are encrypted with that key.</p> <p>Associating a KMS key with a log group overrides any existing associations between the log group and a KMS key. After a KMS key is associated with a log group, all newly ingested data for the log group is encrypted using the KMS key. This association is stored as long as the data encrypted with the KMS key is still within CloudWatch Logs. This enables CloudWatch Logs to decrypt this data whenever it is requested.</p> <p>Associating a key with a log group does not cause the results of queries of that log group to be encrypted with that key. To have query results encrypted with a KMS key, you must use an <code>AssociateKmsKey</code> operation with the <code>resourceIdentifier</code> parameter that specifies a <code>query-result</code> resource. </p> </li> <li> <p>Specify the <code>resourceIdentifier</code> parameter with a <code>query-result</code> resource, to use that key to encrypt the stored results of all future <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_StartQuery.html\">StartQuery</a> operations in the account. The response from a <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_GetQueryResults.html\">GetQueryResults</a> operation will still return the query results in plain text.</p> <p>Even if you have not associated a key with your query results, the query results are encrypted when stored, using the default CloudWatch Logs method.</p> <p>If you run a query from a monitoring account that queries logs in a source account, the query results key from the monitoring account, if any, is used.</p> </li> </ul> <important> <p>If you delete the key that is used to encrypt log events or log group query results, then all the associated stored log events or query results that were encrypted with that key will be unencryptable and unusable.</p> </important> <note> <p>CloudWatch Logs supports only symmetric KMS keys. Do not associate an asymmetric KMS key with your log group or query results. For more information, see <a href=\"https://docs.aws.amazon.com/kms/latest/developerguide/symmetric-asymmetric.html\">Using Symmetric and Asymmetric Keys</a>.</p> </note> <p>It can take up to 5 minutes for this operation to take effect.</p> <p>If you attempt to associate a KMS key with a log group but the KMS key does not exist or the KMS key is disabled, you receive an <code>InvalidParameterException</code> error. </p>"}, "CancelExportTask": {"name": "CancelExportTask", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CancelExportTaskRequest"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidOperationException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Cancels the specified export task.</p> <p>The task must be in the <code>PENDING</code> or <code>RUNNING</code> state.</p>"}, "CreateDelivery": {"name": "CreateDelivery", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateDeliveryRequest"}, "output": {"shape": "CreateDeliveryResponse"}, "errors": [{"shape": "ServiceUnavailableException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Creates a <i>delivery</i>. A delivery is a connection between a logical <i>delivery source</i> and a logical <i>delivery destination</i> that you have already created.</p> <p>Only some Amazon Web Services services support being configured as a delivery source using this operation. These services are listed as <b>Supported [V2 Permissions]</b> in the table at <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/AWS-logs-and-resource-policy.html\">Enabling logging from Amazon Web Services services.</a> </p> <p>A delivery destination can represent a log group in CloudWatch Logs, an Amazon S3 bucket, or a delivery stream in Firehose.</p> <p>To configure logs delivery between a supported Amazon Web Services service and a destination, you must do the following:</p> <ul> <li> <p>Create a delivery source, which is a logical object that represents the resource that is actually sending the logs. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutDeliverySource.html\">PutDeliverySource</a>.</p> </li> <li> <p>Create a <i>delivery destination</i>, which is a logical object that represents the actual delivery destination. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutDeliveryDestination.html\">PutDeliveryDestination</a>.</p> </li> <li> <p>If you are delivering logs cross-account, you must use <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutDeliveryDestinationPolicy.html\">PutDeliveryDestinationPolicy</a> in the destination account to assign an IAM policy to the destination. This policy allows delivery to that destination. </p> </li> <li> <p>Use <code>CreateDelivery</code> to create a <i>delivery</i> by pairing exactly one delivery source and one delivery destination. </p> </li> </ul> <p>You can configure a single delivery source to send logs to multiple destinations by creating multiple deliveries. You can also create multiple deliveries to configure multiple delivery sources to send logs to the same delivery destination.</p> <p>To update an existing delivery configuration, use <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_UpdateDeliveryConfiguration.html\">UpdateDeliveryConfiguration</a>.</p>"}, "CreateExportTask": {"name": "CreateExportTask", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateExportTaskRequest"}, "output": {"shape": "CreateExportTaskResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "LimitExceededException"}, {"shape": "OperationAbortedException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceAlreadyExistsException"}], "documentation": "<p>Creates an export task so that you can efficiently export data from a log group to an Amazon S3 bucket. When you perform a <code>CreateExportTask</code> operation, you must use credentials that have permission to write to the S3 bucket that you specify as the destination.</p> <p>Exporting log data to S3 buckets that are encrypted by KMS is supported. Exporting log data to Amazon S3 buckets that have S3 Object Lock enabled with a retention period is also supported.</p> <p>Exporting to S3 buckets that are encrypted with AES-256 is supported. </p> <p>This is an asynchronous call. If all the required information is provided, this operation initiates an export task and responds with the ID of the task. After the task has started, you can use <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_DescribeExportTasks.html\">DescribeExportTasks</a> to get the status of the export task. Each account can only have one active (<code>RUNNING</code> or <code>PENDING</code>) export task at a time. To cancel an export task, use <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_CancelExportTask.html\">CancelExportTask</a>.</p> <p>You can export logs from multiple log groups or multiple time ranges to the same S3 bucket. To separate log data for each export task, specify a prefix to be used as the Amazon S3 key prefix for all exported objects.</p> <note> <p>We recommend that you don't regularly export to Amazon S3 as a way to continuously archive your logs. For that use case, we instead recommend that you use subscriptions. For more information about subscriptions, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/Subscriptions.html\">Real-time processing of log data with subscriptions</a>.</p> </note> <note> <p>Time-based sorting on chunks of log data inside an exported file is not guaranteed. You can sort the exported log field data by using Linux utilities.</p> </note>"}, "CreateLogAnomalyDetector": {"name": "CreateLogAnomalyDetector", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateLogAnomalyDetectorRequest"}, "output": {"shape": "CreateLogAnomalyDetectorResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}, {"shape": "OperationAbortedException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Creates an <i>anomaly detector</i> that regularly scans one or more log groups and look for patterns and anomalies in the logs.</p> <p>An anomaly detector can help surface issues by automatically discovering anomalies in your log event traffic. An anomaly detector uses machine learning algorithms to scan log events and find <i>patterns</i>. A pattern is a shared text structure that recurs among your log fields. Patterns provide a useful tool for analyzing large sets of logs because a large number of log events can often be compressed into a few patterns.</p> <p>The anomaly detector uses pattern recognition to find <code>anomalies</code>, which are unusual log events. It uses the <code>evaluationFrequency</code> to compare current log events and patterns with trained baselines. </p> <p>Fields within a pattern are called <i>tokens</i>. Fields that vary within a pattern, such as a request ID or timestamp, are referred to as <i>dynamic tokens</i> and represented by <code>&lt;*&gt;</code>. </p> <p>The following is an example of a pattern:</p> <p> <code>[INFO] Request time: &lt;*&gt; ms</code> </p> <p>This pattern represents log events like <code>[INFO] Request time: 327 ms</code> and other similar log events that differ only by the number, in this csse 327. When the pattern is displayed, the different numbers are replaced by <code>&lt;*&gt;</code> </p> <note> <p>Any parts of log events that are masked as sensitive data are not scanned for anomalies. For more information about masking sensitive data, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/mask-sensitive-log-data.html\">Help protect sensitive log data with masking</a>. </p> </note>"}, "CreateLogGroup": {"name": "CreateLogGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateLogGroupRequest"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "LimitExceededException"}, {"shape": "OperationAbortedException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Creates a log group with the specified name. You can create up to 1,000,000 log groups per Region per account.</p> <p>You must use the following guidelines when naming a log group:</p> <ul> <li> <p>Log group names must be unique within a Region for an Amazon Web Services account.</p> </li> <li> <p>Log group names can be between 1 and 512 characters long.</p> </li> <li> <p>Log group names consist of the following characters: a-z, A-Z, 0-9, '_' (underscore), '-' (hyphen), '/' (forward slash), '.' (period), and '#' (number sign)</p> </li> <li> <p>Log group names can't start with the string <code>aws/</code> </p> </li> </ul> <p>When you create a log group, by default the log events in the log group do not expire. To set a retention policy so that events expire and are deleted after a specified time, use <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutRetentionPolicy.html\">PutRetentionPolicy</a>.</p> <p>If you associate an KMS key with the log group, ingested data is encrypted using the KMS key. This association is stored as long as the data encrypted with the KMS key is still within CloudWatch Logs. This enables CloudWatch Logs to decrypt this data whenever it is requested.</p> <p>If you attempt to associate a KMS key with the log group but the KMS key does not exist or the KMS key is disabled, you receive an <code>InvalidParameterException</code> error. </p> <important> <p>CloudWatch Logs supports only symmetric KMS keys. Do not associate an asymmetric KMS key with your log group. For more information, see <a href=\"https://docs.aws.amazon.com/kms/latest/developerguide/symmetric-asymmetric.html\">Using Symmetric and Asymmetric Keys</a>.</p> </important>"}, "CreateLogStream": {"name": "CreateLogStream", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateLogStreamRequest"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Creates a log stream for the specified log group. A log stream is a sequence of log events that originate from a single source, such as an application instance or a resource that is being monitored.</p> <p>There is no limit on the number of log streams that you can create for a log group. There is a limit of 50 TPS on <code>CreateLogStream</code> operations, after which transactions are throttled.</p> <p>You must use the following guidelines when naming a log stream:</p> <ul> <li> <p>Log stream names must be unique within the log group.</p> </li> <li> <p>Log stream names can be between 1 and 512 characters long.</p> </li> <li> <p>Don't use ':' (colon) or '*' (asterisk) characters.</p> </li> </ul>"}, "DeleteAccountPolicy": {"name": "DeleteAccountPolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteAccountPolicyRequest"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}, {"shape": "OperationAbortedException"}], "documentation": "<p>Deletes a CloudWatch Logs account policy. This stops the account-wide policy from applying to log groups in the account. If you delete a data protection policy or subscription filter policy, any log-group level policies of those types remain in effect.</p> <p>To use this operation, you must be signed on with the correct permissions depending on the type of policy that you are deleting.</p> <ul> <li> <p>To delete a data protection policy, you must have the <code>logs:DeleteDataProtectionPolicy</code> and <code>logs:DeleteAccountPolicy</code> permissions.</p> </li> <li> <p>To delete a subscription filter policy, you must have the <code>logs:DeleteSubscriptionFilter</code> and <code>logs:DeleteAccountPolicy</code> permissions.</p> </li> <li> <p>To delete a transformer policy, you must have the <code>logs:DeleteTransformer</code> and <code>logs:DeleteAccountPolicy</code> permissions.</p> </li> <li> <p>To delete a field index policy, you must have the <code>logs:DeleteIndexPolicy</code> and <code>logs:DeleteAccountPolicy</code> permissions.</p> </li> </ul> <p>If you delete a field index policy, the indexing of the log events that happened before you deleted the policy will still be used for up to 30 days to improve CloudWatch Logs Insights queries.</p>"}, "DeleteDataProtectionPolicy": {"name": "DeleteDataProtectionPolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteDataProtectionPolicyRequest"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "OperationAbortedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Deletes the data protection policy from the specified log group. </p> <p>For more information about data protection policies, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutDataProtectionPolicy.html\">PutDataProtectionPolicy</a>.</p>"}, "DeleteDelivery": {"name": "DeleteDelivery", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteDeliveryRequest"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Deletes a <i>delivery</i>. A delivery is a connection between a logical <i>delivery source</i> and a logical <i>delivery destination</i>. Deleting a delivery only deletes the connection between the delivery source and delivery destination. It does not delete the delivery destination or the delivery source.</p>"}, "DeleteDeliveryDestination": {"name": "DeleteDeliveryDestination", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteDeliveryDestinationRequest"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Deletes a <i>delivery destination</i>. A delivery is a connection between a logical <i>delivery source</i> and a logical <i>delivery destination</i>.</p> <p>You can't delete a delivery destination if any current deliveries are associated with it. To find whether any deliveries are associated with this delivery destination, use the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_DescribeDeliveries.html\">DescribeDeliveries</a> operation and check the <code>deliveryDestinationArn</code> field in the results.</p>"}, "DeleteDeliveryDestinationPolicy": {"name": "DeleteDeliveryDestinationPolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteDeliveryDestinationPolicyRequest"}, "errors": [{"shape": "ServiceUnavailableException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}], "documentation": "<p>Deletes a delivery destination policy. For more information about these policies, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutDeliveryDestinationPolicy.html\">PutDeliveryDestinationPolicy</a>.</p>"}, "DeleteDeliverySource": {"name": "DeleteDeliverySource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteDeliverySourceRequest"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Deletes a <i>delivery source</i>. A delivery is a connection between a logical <i>delivery source</i> and a logical <i>delivery destination</i>.</p> <p>You can't delete a delivery source if any current deliveries are associated with it. To find whether any deliveries are associated with this delivery source, use the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_DescribeDeliveries.html\">DescribeDeliveries</a> operation and check the <code>deliverySourceName</code> field in the results.</p>"}, "DeleteDestination": {"name": "DeleteDestination", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteDestinationRequest"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}, {"shape": "OperationAbortedException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Deletes the specified destination, and eventually disables all the subscription filters that publish to it. This operation does not delete the physical resource encapsulated by the destination.</p>"}, "DeleteIndexPolicy": {"name": "DeleteIndexPolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteIndexPolicyRequest"}, "output": {"shape": "DeleteIndexPolicyResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}, {"shape": "LimitExceededException"}, {"shape": "OperationAbortedException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Deletes a log-group level field index policy that was applied to a single log group. The indexing of the log events that happened before you delete the policy will still be used for as many as 30 days to improve CloudWatch Logs Insights queries.</p> <p>You can't use this operation to delete an account-level index policy. Instead, use <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_DeleteAccountPolicy.html\">DeletAccountPolicy</a>.</p> <p>If you delete a log-group level field index policy and there is an account-level field index policy, in a few minutes the log group begins using that account-wide policy to index new incoming log events. </p>"}, "DeleteIntegration": {"name": "DeleteIntegration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteIntegrationRequest"}, "output": {"shape": "DeleteIntegrationResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ValidationException"}], "documentation": "<p>Deletes the integration between CloudWatch Logs and OpenSearch Service. If your integration has active vended logs dashboards, you must specify <code>true</code> for the <code>force</code> parameter, otherwise the operation will fail. If you delete the integration by setting <code>force</code> to <code>true</code>, all your vended logs dashboards powered by OpenSearch Service will be deleted and the data that was on them will no longer be accessible.</p>"}, "DeleteLogAnomalyDetector": {"name": "DeleteLogAnomalyDetector", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteLogAnomalyDetectorRequest"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}, {"shape": "OperationAbortedException"}], "documentation": "<p>Deletes the specified CloudWatch Logs anomaly detector.</p>"}, "DeleteLogGroup": {"name": "DeleteLogGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteLogGroupRequest"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}, {"shape": "OperationAbortedException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Deletes the specified log group and permanently deletes all the archived log events associated with the log group.</p>"}, "DeleteLogStream": {"name": "DeleteLogStream", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteLogStreamRequest"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}, {"shape": "OperationAbortedException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Deletes the specified log stream and permanently deletes all the archived log events associated with the log stream.</p>"}, "DeleteMetricFilter": {"name": "DeleteMetricFilter", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteMetricFilterRequest"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}, {"shape": "OperationAbortedException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Deletes the specified metric filter.</p>"}, "DeleteQueryDefinition": {"name": "DeleteQueryDefinition", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteQueryDefinitionRequest"}, "output": {"shape": "DeleteQueryDefinitionResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Deletes a saved CloudWatch Logs Insights query definition. A query definition contains details about a saved CloudWatch Logs Insights query.</p> <p>Each <code>DeleteQueryDefinition</code> operation can delete one query definition.</p> <p>You must have the <code>logs:DeleteQueryDefinition</code> permission to be able to perform this operation.</p>"}, "DeleteResourcePolicy": {"name": "DeleteResourcePolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteResourcePolicyRequest"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Deletes a resource policy from this account. This revokes the access of the identities in that policy to put log events to this account.</p>"}, "DeleteRetentionPolicy": {"name": "DeleteRetentionPolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteRetentionPolicyRequest"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}, {"shape": "OperationAbortedException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Deletes the specified retention policy.</p> <p>Log events do not expire if they belong to log groups without a retention policy.</p>"}, "DeleteSubscriptionFilter": {"name": "DeleteSubscriptionFilter", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteSubscriptionFilterRequest"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}, {"shape": "OperationAbortedException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Deletes the specified subscription filter.</p>"}, "DeleteTransformer": {"name": "DeleteTransformer", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteTransformerRequest"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}, {"shape": "OperationAbortedException"}, {"shape": "InvalidOperationException"}], "documentation": "<p>Deletes the log transformer for the specified log group. As soon as you do this, the transformation of incoming log events according to that transformer stops. If this account has an account-level transformer that applies to this log group, the log group begins using that account-level transformer when this log-group level transformer is deleted.</p> <p>After you delete a transformer, be sure to edit any metric filters or subscription filters that relied on the transformed versions of the log events.</p>"}, "DescribeAccountPolicies": {"name": "DescribeAccountPolicies", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeAccountPoliciesRequest"}, "output": {"shape": "DescribeAccountPoliciesResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "OperationAbortedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Returns a list of all CloudWatch Logs account policies in the account.</p> <p>To use this operation, you must be signed on with the correct permissions depending on the type of policy that you are retrieving information for.</p> <ul> <li> <p>To see data protection policies, you must have the <code>logs:GetDataProtectionPolicy</code> and <code>logs:DescribeAccountPolicies</code> permissions.</p> </li> <li> <p>To see subscription filter policies, you must have the <code>logs:DescribeSubscriptionFilters</code> and <code>logs:DescribeAccountPolicies</code> permissions.</p> </li> <li> <p>To see transformer policies, you must have the <code>logs:GetTransformer</code> and <code>logs:DescribeAccountPolicies</code> permissions.</p> </li> <li> <p>To see field index policies, you must have the <code>logs:DescribeIndexPolicies</code> and <code>logs:DescribeAccountPolicies</code> permissions.</p> </li> </ul>"}, "DescribeConfigurationTemplates": {"name": "DescribeConfigurationTemplates", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeConfigurationTemplatesRequest"}, "output": {"shape": "DescribeConfigurationTemplatesResponse"}, "errors": [{"shape": "ServiceUnavailableException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Use this operation to return the valid and default values that are used when creating delivery sources, delivery destinations, and deliveries. For more information about deliveries, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_CreateDelivery.html\">CreateDelivery</a>.</p>"}, "DescribeDeliveries": {"name": "DescribeDeliveries", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeDeliveriesRequest"}, "output": {"shape": "DescribeDeliveriesResponse"}, "errors": [{"shape": "ServiceUnavailableException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Retrieves a list of the deliveries that have been created in the account.</p> <p>A <i>delivery</i> is a connection between a <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutDeliverySource.html\"> <i>delivery source</i> </a> and a <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutDeliveryDestination.html\"> <i>delivery destination</i> </a>.</p> <p>A delivery source represents an Amazon Web Services resource that sends logs to an logs delivery destination. The destination can be CloudWatch Logs, Amazon S3, or Firehose. Only some Amazon Web Services services support being configured as a delivery source. These services are listed in <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/AWS-logs-and-resource-policy.html\">Enable logging from Amazon Web Services services.</a> </p>"}, "DescribeDeliveryDestinations": {"name": "DescribeDeliveryDestinations", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeDeliveryDestinationsRequest"}, "output": {"shape": "DescribeDeliveryDestinationsResponse"}, "errors": [{"shape": "ServiceUnavailableException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Retrieves a list of the delivery destinations that have been created in the account.</p>"}, "DescribeDeliverySources": {"name": "DescribeDeliverySources", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeDeliverySourcesRequest"}, "output": {"shape": "DescribeDeliverySourcesResponse"}, "errors": [{"shape": "ServiceUnavailableException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Retrieves a list of the delivery sources that have been created in the account.</p>"}, "DescribeDestinations": {"name": "DescribeDestinations", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeDestinationsRequest"}, "output": {"shape": "DescribeDestinationsResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Lists all your destinations. The results are ASCII-sorted by destination name.</p>"}, "DescribeExportTasks": {"name": "DescribeExportTasks", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeExportTasksRequest"}, "output": {"shape": "DescribeExportTasksResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Lists the specified export tasks. You can list all your export tasks or filter the results based on task ID or task status.</p>"}, "DescribeFieldIndexes": {"name": "DescribeFieldIndexes", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeFieldIndexesRequest"}, "output": {"shape": "DescribeFieldIndexesResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}, {"shape": "LimitExceededException"}, {"shape": "OperationAbortedException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Returns a list of field indexes listed in the field index policies of one or more log groups. For more information about field index policies, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutIndexPolicy.html\">PutIndexPolicy</a>.</p>"}, "DescribeIndexPolicies": {"name": "DescribeIndexPolicies", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeIndexPoliciesRequest"}, "output": {"shape": "DescribeIndexPoliciesResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}, {"shape": "LimitExceededException"}, {"shape": "OperationAbortedException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Returns the field index policies of one or more log groups. For more information about field index policies, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutIndexPolicy.html\">PutIndexPolicy</a>.</p> <p>If a specified log group has a log-group level index policy, that policy is returned by this operation.</p> <p>If a specified log group doesn't have a log-group level index policy, but an account-wide index policy applies to it, that account-wide policy is returned by this operation.</p> <p>To find information about only account-level policies, use <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_DescribeAccountPolicies.html\">DescribeAccountPolicies</a> instead.</p>"}, "DescribeLogGroups": {"name": "DescribeLogGroups", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeLogGroupsRequest"}, "output": {"shape": "DescribeLogGroupsResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Returns information about log groups. You can return all your log groups or filter the results by prefix. The results are ASCII-sorted by log group name.</p> <p>CloudWatch Logs doesn't support IAM policies that control access to the <code>DescribeLogGroups</code> action by using the <code>aws:ResourceTag/<i>key-name</i> </code> condition key. Other CloudWatch Logs actions do support the use of the <code>aws:ResourceTag/<i>key-name</i> </code> condition key to control access. For more information about using tags to control access, see <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/access_tags.html\">Controlling access to Amazon Web Services resources using tags</a>.</p> <p>If you are using CloudWatch cross-account observability, you can use this operation in a monitoring account and view data from the linked source accounts. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/CloudWatch-Unified-Cross-Account.html\">CloudWatch cross-account observability</a>.</p>"}, "DescribeLogStreams": {"name": "DescribeLogStreams", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeLogStreamsRequest"}, "output": {"shape": "DescribeLogStreamsResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Lists the log streams for the specified log group. You can list all the log streams or filter the results by prefix. You can also control how the results are ordered.</p> <p>You can specify the log group to search by using either <code>logGroupIdentifier</code> or <code>logGroupName</code>. You must include one of these two parameters, but you can't include both. </p> <p>This operation has a limit of 25 transactions per second, after which transactions are throttled.</p> <p>If you are using CloudWatch cross-account observability, you can use this operation in a monitoring account and view data from the linked source accounts. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/CloudWatch-Unified-Cross-Account.html\">CloudWatch cross-account observability</a>.</p>"}, "DescribeMetricFilters": {"name": "DescribeMetricFilters", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeMetricFiltersRequest"}, "output": {"shape": "DescribeMetricFiltersResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Lists the specified metric filters. You can list all of the metric filters or filter the results by log name, prefix, metric name, or metric namespace. The results are ASCII-sorted by filter name.</p>"}, "DescribeQueries": {"name": "DescribeQueries", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeQueriesRequest"}, "output": {"shape": "DescribeQueriesResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Returns a list of CloudWatch Logs Insights queries that are scheduled, running, or have been run recently in this account. You can request all queries or limit it to queries of a specific log group or queries with a certain status.</p>"}, "DescribeQueryDefinitions": {"name": "DescribeQueryDefinitions", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeQueryDefinitionsRequest"}, "output": {"shape": "DescribeQueryDefinitionsResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>This operation returns a paginated list of your saved CloudWatch Logs Insights query definitions. You can retrieve query definitions from the current account or from a source account that is linked to the current account.</p> <p>You can use the <code>queryDefinitionNamePrefix</code> parameter to limit the results to only the query definitions that have names that start with a certain string.</p>"}, "DescribeResourcePolicies": {"name": "DescribeResourcePolicies", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeResourcePoliciesRequest"}, "output": {"shape": "DescribeResourcePoliciesResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Lists the resource policies in this account.</p>"}, "DescribeSubscriptionFilters": {"name": "DescribeSubscriptionFilters", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeSubscriptionFiltersRequest"}, "output": {"shape": "DescribeSubscriptionFiltersResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Lists the subscription filters for the specified log group. You can list all the subscription filters or filter the results by prefix. The results are ASCII-sorted by filter name.</p>"}, "DisassociateKmsKey": {"name": "DisassociateKmsKey", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DisassociateKmsKeyRequest"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}, {"shape": "OperationAbortedException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Disassociates the specified KMS key from the specified log group or from all CloudWatch Logs Insights query results in the account.</p> <p>When you use <code>DisassociateKmsKey</code>, you specify either the <code>logGroupName</code> parameter or the <code>resourceIdentifier</code> parameter. You can't specify both of those parameters in the same operation.</p> <ul> <li> <p>Specify the <code>logGroupName</code> parameter to stop using the KMS key to encrypt future log events ingested and stored in the log group. Instead, they will be encrypted with the default CloudWatch Logs method. The log events that were ingested while the key was associated with the log group are still encrypted with that key. Therefore, CloudWatch Logs will need permissions for the key whenever that data is accessed.</p> </li> <li> <p>Specify the <code>resourceIdentifier</code> parameter with the <code>query-result</code> resource to stop using the KMS key to encrypt the results of all future <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_StartQuery.html\">StartQuery</a> operations in the account. They will instead be encrypted with the default CloudWatch Logs method. The results from queries that ran while the key was associated with the account are still encrypted with that key. Therefore, CloudWatch Logs will need permissions for the key whenever that data is accessed.</p> </li> </ul> <p>It can take up to 5 minutes for this operation to take effect.</p>"}, "FilterLogEvents": {"name": "FilterLogEvents", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "FilterLogEventsRequest"}, "output": {"shape": "FilterLogEventsResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Lists log events from the specified log group. You can list all the log events or filter the results using one or more of the following:</p> <ul> <li> <p>A filter pattern</p> </li> <li> <p>A time range</p> </li> <li> <p>The log stream name, or a log stream name prefix that matches mutltiple log streams</p> </li> </ul> <p>You must have the <code>logs:FilterLogEvents</code> permission to perform this operation.</p> <p>You can specify the log group to search by using either <code>logGroupIdentifier</code> or <code>logGroupName</code>. You must include one of these two parameters, but you can't include both. </p> <p> <code>FilterLogEvents</code> is a paginated operation. Each page returned can contain up to 1 MB of log events or up to 10,000 log events. A returned page might only be partially full, or even empty. For example, if the result of a query would return 15,000 log events, the first page isn't guaranteed to have 10,000 log events even if they all fit into 1 MB.</p> <p>Partially full or empty pages don't necessarily mean that pagination is finished. If the results include a <code>nextToken</code>, there might be more log events available. You can return these additional log events by providing the nextToken in a subsequent <code>FilterLogEvents</code> operation. If the results don't include a <code>nextToken</code>, then pagination is finished. </p> <p>Specifying the <code>limit</code> parameter only guarantees that a single page doesn't return more log events than the specified limit, but it might return fewer events than the limit. This is the expected API behavior.</p> <p>The returned log events are sorted by event timestamp, the timestamp when the event was ingested by CloudWatch Logs, and the ID of the <code>PutLogEvents</code> request.</p> <p>If you are using CloudWatch cross-account observability, you can use this operation in a monitoring account and view data from the linked source accounts. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/CloudWatch-Unified-Cross-Account.html\">CloudWatch cross-account observability</a>.</p> <note> <p>If you are using <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html\">log transformation</a>, the <code>FilterLogEvents</code> operation returns only the original versions of log events, before they were transformed. To view the transformed versions, you must use a <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/AnalyzingLogData.html\">CloudWatch Logs query.</a> </p> </note>"}, "GetDataProtectionPolicy": {"name": "GetDataProtectionPolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetDataProtectionPolicyRequest"}, "output": {"shape": "GetDataProtectionPolicyResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "OperationAbortedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Returns information about a log group data protection policy.</p>"}, "GetDelivery": {"name": "GetDelivery", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetDeliveryRequest"}, "output": {"shape": "GetDeliveryResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Returns complete information about one logical <i>delivery</i>. A delivery is a connection between a <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutDeliverySource.html\"> <i>delivery source</i> </a> and a <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutDeliveryDestination.html\"> <i>delivery destination</i> </a>.</p> <p>A delivery source represents an Amazon Web Services resource that sends logs to an logs delivery destination. The destination can be CloudWatch Logs, Amazon S3, or Firehose. Only some Amazon Web Services services support being configured as a delivery source. These services are listed in <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/AWS-logs-and-resource-policy.html\">Enable logging from Amazon Web Services services.</a> </p> <p>You need to specify the delivery <code>id</code> in this operation. You can find the IDs of the deliveries in your account with the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_DescribeDeliveries.html\">DescribeDeliveries</a> operation.</p>"}, "GetDeliveryDestination": {"name": "GetDeliveryDestination", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetDeliveryDestinationRequest"}, "output": {"shape": "GetDeliveryDestinationResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Retrieves complete information about one delivery destination.</p>"}, "GetDeliveryDestinationPolicy": {"name": "GetDeliveryDestinationPolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetDeliveryDestinationPolicyRequest"}, "output": {"shape": "GetDeliveryDestinationPolicyResponse"}, "errors": [{"shape": "ServiceUnavailableException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves the delivery destination policy assigned to the delivery destination that you specify. For more information about delivery destinations and their policies, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutDeliveryDestinationPolicy.html\">PutDeliveryDestinationPolicy</a>.</p>"}, "GetDeliverySource": {"name": "GetDeliverySource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetDeliverySourceRequest"}, "output": {"shape": "GetDeliverySourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Retrieves complete information about one delivery source.</p>"}, "GetIntegration": {"name": "GetIntegration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetIntegrationRequest"}, "output": {"shape": "GetIntegrationResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns information about one integration between CloudWatch Logs and OpenSearch Service. </p>"}, "GetLogAnomalyDetector": {"name": "GetLogAnomalyDetector", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetLogAnomalyDetectorRequest"}, "output": {"shape": "GetLogAnomalyDetectorResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}, {"shape": "OperationAbortedException"}], "documentation": "<p>Retrieves information about the log anomaly detector that you specify. The KMS key ARN detected is valid.</p>"}, "GetLogEvents": {"name": "GetLogEvents", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetLogEventsRequest"}, "output": {"shape": "GetLogEventsResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Lists log events from the specified log stream. You can list all of the log events or filter using a time range.</p> <p> <code>GetLogEvents</code> is a paginated operation. Each page returned can contain up to 1 MB of log events or up to 10,000 log events. A returned page might only be partially full, or even empty. For example, if the result of a query would return 15,000 log events, the first page isn't guaranteed to have 10,000 log events even if they all fit into 1 MB.</p> <p>Partially full or empty pages don't necessarily mean that pagination is finished. As long as the <code>nextBackwardToken</code> or <code>nextForwardToken</code> returned is NOT equal to the <code>nextToken</code> that you passed into the API call, there might be more log events available. The token that you use depends on the direction you want to move in along the log stream. The returned tokens are never null.</p> <note> <p>If you set <code>startFromHead</code> to <code>true</code> and you don’t include <code>endTime</code> in your request, you can end up in a situation where the pagination doesn't terminate. This can happen when the new log events are being added to the target log streams faster than they are being read. This situation is a good use case for the CloudWatch Logs <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatchLogs_LiveTail.html\">Live Tail</a> feature.</p> </note> <p>If you are using CloudWatch cross-account observability, you can use this operation in a monitoring account and view data from the linked source accounts. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/CloudWatch-Unified-Cross-Account.html\">CloudWatch cross-account observability</a>.</p> <p>You can specify the log group to search by using either <code>logGroupIdentifier</code> or <code>logGroupName</code>. You must include one of these two parameters, but you can't include both. </p> <note> <p>If you are using <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html\">log transformation</a>, the <code>GetLogEvents</code> operation returns only the original versions of log events, before they were transformed. To view the transformed versions, you must use a <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/AnalyzingLogData.html\">CloudWatch Logs query.</a> </p> </note>"}, "GetLogGroupFields": {"name": "GetLogGroupFields", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetLogGroupFieldsRequest"}, "output": {"shape": "GetLogGroupFieldsResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "LimitExceededException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Returns a list of the fields that are included in log events in the specified log group. Includes the percentage of log events that contain each field. The search is limited to a time period that you specify.</p> <p>You can specify the log group to search by using either <code>logGroupIdentifier</code> or <code>logGroupName</code>. You must specify one of these parameters, but you can't specify both. </p> <p>In the results, fields that start with <code>@</code> are fields generated by CloudWatch Logs. For example, <code>@timestamp</code> is the timestamp of each log event. For more information about the fields that are generated by CloudWatch logs, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CWL_AnalyzeLogData-discoverable-fields.html\">Supported Logs and Discovered Fields</a>.</p> <p>The response results are sorted by the frequency percentage, starting with the highest percentage.</p> <p>If you are using CloudWatch cross-account observability, you can use this operation in a monitoring account and view data from the linked source accounts. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/CloudWatch-Unified-Cross-Account.html\">CloudWatch cross-account observability</a>.</p>"}, "GetLogRecord": {"name": "GetLogRecord", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetLogRecordRequest"}, "output": {"shape": "GetLogRecordResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "LimitExceededException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Retrieves all of the fields and values of a single log event. All fields are retrieved, even if the original query that produced the <code>logRecordPointer</code> retrieved only a subset of fields. Fields are returned as field name/field value pairs.</p> <p>The full unparsed log event is returned within <code>@message</code>.</p>"}, "GetQueryResults": {"name": "GetQueryResults", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetQueryResultsRequest"}, "output": {"shape": "GetQueryResultsResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Returns the results from the specified query.</p> <p>Only the fields requested in the query are returned, along with a <code>@ptr</code> field, which is the identifier for the log record. You can use the value of <code>@ptr</code> in a <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_GetLogRecord.html\">GetLogRecord</a> operation to get the full log record.</p> <p> <code>GetQueryResults</code> does not start running a query. To run a query, use <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_StartQuery.html\">StartQuery</a>. For more information about how long results of previous queries are available, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/cloudwatch_limits_cwl.html\">CloudWatch Logs quotas</a>.</p> <p>If the value of the <code>Status</code> field in the output is <code>Running</code>, this operation returns only partial results. If you see a value of <code>Scheduled</code> or <code>Running</code> for the status, you can retry the operation later to see the final results. </p> <p>If you are using CloudWatch cross-account observability, you can use this operation in a monitoring account to start queries in linked source accounts. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/CloudWatch-Unified-Cross-Account.html\">CloudWatch cross-account observability</a>.</p>"}, "GetTransformer": {"name": "GetTransformer", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetTransformerRequest"}, "output": {"shape": "GetTransformerResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidOperationException"}], "documentation": "<p>Returns the information about the log transformer associated with this log group.</p> <p>This operation returns data only for transformers created at the log group level. To get information for an account-level transformer, use <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_DescribeAccountPolicies.html\">DescribeAccountPolicies</a>.</p>"}, "ListAnomalies": {"name": "ListAnomalies", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListAnomaliesRequest"}, "output": {"shape": "ListAnomaliesResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}, {"shape": "OperationAbortedException"}], "documentation": "<p>Returns a list of anomalies that log anomaly detectors have found. For details about the structure format of each anomaly object that is returned, see the example in this section.</p>"}, "ListIntegrations": {"name": "ListIntegrations", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListIntegrationsRequest"}, "output": {"shape": "ListIntegrationsResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Returns a list of integrations between CloudWatch Logs and other services in this account. Currently, only one integration can be created in an account, and this integration must be with OpenSearch Service.</p>"}, "ListLogAnomalyDetectors": {"name": "ListLogAnomalyDetectors", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListLogAnomalyDetectorsRequest"}, "output": {"shape": "ListLogAnomalyDetectorsResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}, {"shape": "OperationAbortedException"}], "documentation": "<p>Retrieves a list of the log anomaly detectors in the account.</p>"}, "ListLogGroups": {"name": "ListLogGroups", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListLogGroupsRequest"}, "output": {"shape": "ListLogGroupsResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Returns a list of log groups in the Region in your account. If you are performing this action in a monitoring account, you can choose to also return log groups from source accounts that are linked to the monitoring account. For more information about using cross-account observability to set up monitoring accounts and source accounts, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/CloudWatch-Unified-Cross-Account.html\"> CloudWatch cross-account observability</a>.</p> <p>You can optionally filter the list by log group class and by using regular expressions in your request to match strings in the log group names.</p> <p>This operation is paginated. By default, your first use of this operation returns 50 results, and includes a token to use in a subsequent operation to return more results.</p>"}, "ListLogGroupsForQuery": {"name": "ListLogGroupsForQuery", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListLogGroupsForQueryRequest"}, "output": {"shape": "ListLogGroupsForQueryResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Returns a list of the log groups that were analyzed during a single CloudWatch Logs Insights query. This can be useful for queries that use log group name prefixes or the <code>filterIndex</code> command, because the log groups are dynamically selected in these cases.</p> <p>For more information about field indexes, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatchLogs-Field-Indexing.html\">Create field indexes to improve query performance and reduce costs</a>.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Displays the tags associated with a CloudWatch Logs resource. Currently, log groups and destinations support tagging.</p>"}, "ListTagsLogGroup": {"name": "ListTagsLogGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTagsLogGroupRequest"}, "output": {"shape": "ListTagsLogGroupResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<important> <p>The ListTagsLogGroup operation is on the path to deprecation. We recommend that you use <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_ListTagsForResource.html\">ListTagsForResource</a> instead.</p> </important> <p>Lists the tags for the specified log group.</p>", "deprecated": true, "deprecatedMessage": "Please use the generic tagging API ListTagsForResource"}, "PutAccountPolicy": {"name": "PutAccountPolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutAccountPolicyRequest"}, "output": {"shape": "PutAccountPolicyResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "OperationAbortedException"}, {"shape": "ServiceUnavailableException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Creates an account-level data protection policy, subscription filter policy, or field index policy that applies to all log groups or a subset of log groups in the account.</p> <p>To use this operation, you must be signed on with the correct permissions depending on the type of policy that you are creating.</p> <ul> <li> <p>To create a data protection policy, you must have the <code>logs:PutDataProtectionPolicy</code> and <code>logs:PutAccountPolicy</code> permissions.</p> </li> <li> <p>To create a subscription filter policy, you must have the <code>logs:PutSubscriptionFilter</code> and <code>logs:PutccountPolicy</code> permissions.</p> </li> <li> <p>To create a transformer policy, you must have the <code>logs:PutTransformer</code> and <code>logs:PutAccountPolicy</code> permissions.</p> </li> <li> <p>To create a field index policy, you must have the <code>logs:PutIndexPolicy</code> and <code>logs:PutAccountPolicy</code> permissions.</p> </li> </ul> <p> <b>Data protection policy</b> </p> <p>A data protection policy can help safeguard sensitive data that's ingested by your log groups by auditing and masking the sensitive log data. Each account can have only one account-level data protection policy.</p> <important> <p>Sensitive data is detected and masked when it is ingested into a log group. When you set a data protection policy, log events ingested into the log groups before that time are not masked.</p> </important> <p>If you use <code>PutAccountPolicy</code> to create a data protection policy for your whole account, it applies to both existing log groups and all log groups that are created later in this account. The account-level policy is applied to existing log groups with eventual consistency. It might take up to 5 minutes before sensitive data in existing log groups begins to be masked.</p> <p>By default, when a user views a log event that includes masked data, the sensitive data is replaced by asterisks. A user who has the <code>logs:Unmask</code> permission can use a <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_GetLogEvents.html\">GetLogEvents</a> or <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_FilterLogEvents.html\">FilterLogEvents</a> operation with the <code>unmask</code> parameter set to <code>true</code> to view the unmasked log events. Users with the <code>logs:Unmask</code> can also view unmasked data in the CloudWatch Logs console by running a CloudWatch Logs Insights query with the <code>unmask</code> query command.</p> <p>For more information, including a list of types of data that can be audited and masked, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/mask-sensitive-log-data.html\">Protect sensitive log data with masking</a>.</p> <p>To use the <code>PutAccountPolicy</code> operation for a data protection policy, you must be signed on with the <code>logs:PutDataProtectionPolicy</code> and <code>logs:PutAccountPolicy</code> permissions.</p> <p>The <code>PutAccountPolicy</code> operation applies to all log groups in the account. You can use <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutDataProtectionPolicy.html\">PutDataProtectionPolicy</a> to create a data protection policy that applies to just one log group. If a log group has its own data protection policy and the account also has an account-level data protection policy, then the two policies are cumulative. Any sensitive term specified in either policy is masked.</p> <p> <b>Subscription filter policy</b> </p> <p>A subscription filter policy sets up a real-time feed of log events from CloudWatch Logs to other Amazon Web Services services. Account-level subscription filter policies apply to both existing log groups and log groups that are created later in this account. Supported destinations are Kinesis Data Streams, Firehose, and Lambda. When log events are sent to the receiving service, they are Base64 encoded and compressed with the GZIP format.</p> <p>The following destinations are supported for subscription filters:</p> <ul> <li> <p>An Kinesis Data Streams data stream in the same account as the subscription policy, for same-account delivery.</p> </li> <li> <p>An Firehose data stream in the same account as the subscription policy, for same-account delivery.</p> </li> <li> <p>A Lambda function in the same account as the subscription policy, for same-account delivery.</p> </li> <li> <p>A logical destination in a different account created with <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutDestination.html\">PutDestination</a>, for cross-account delivery. Kinesis Data Streams and Firehose are supported as logical destinations.</p> </li> </ul> <p>Each account can have one account-level subscription filter policy per Region. If you are updating an existing filter, you must specify the correct name in <code>PolicyName</code>. To perform a <code>PutAccountPolicy</code> subscription filter operation for any destination except a Lambda function, you must also have the <code>iam:PassRole</code> permission.</p> <p> <b>Transformer policy</b> </p> <p>Creates or updates a <i>log transformer policy</i> for your account. You use log transformers to transform log events into a different format, making them easier for you to process and analyze. You can also transform logs from different sources into standardized formats that contain relevant, source-specific information. After you have created a transformer, CloudWatch Logs performs this transformation at the time of log ingestion. You can then refer to the transformed versions of the logs during operations such as querying with CloudWatch Logs Insights or creating metric filters or subscription filters.</p> <p>You can also use a transformer to copy metadata from metadata keys into the log events themselves. This metadata can include log group name, log stream name, account ID and Region.</p> <p>A transformer for a log group is a series of processors, where each processor applies one type of transformation to the log events ingested into this log group. For more information about the available processors to use in a transformer, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-Processors\"> Processors that you can use</a>.</p> <p>Having log events in standardized format enables visibility across your applications for your log analysis, reporting, and alarming needs. CloudWatch Logs provides transformation for common log types with out-of-the-box transformation templates for major Amazon Web Services log sources such as VPC flow logs, Lambda, and Amazon RDS. You can use pre-built transformation templates or create custom transformation policies.</p> <p>You can create transformers only for the log groups in the Standard log class.</p> <p>You can have one account-level transformer policy that applies to all log groups in the account. Or you can create as many as 20 account-level transformer policies that are each scoped to a subset of log groups with the <code>selectionCriteria</code> parameter. If you have multiple account-level transformer policies with selection criteria, no two of them can use the same or overlapping log group name prefixes. For example, if you have one policy filtered to log groups that start with <code>my-log</code>, you can't have another field index policy filtered to <code>my-logpprod</code> or <code>my-logging</code>.</p> <p>You can also set up a transformer at the log-group level. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutTransformer.html\">PutTransformer</a>. If there is both a log-group level transformer created with <code>PutTransformer</code> and an account-level transformer that could apply to the same log group, the log group uses only the log-group level transformer. It ignores the account-level transformer.</p> <p> <b>Field index policy</b> </p> <p>You can use field index policies to create indexes on fields found in log events in the log group. Creating field indexes can help lower the scan volume for CloudWatch Logs Insights queries that reference those fields, because these queries attempt to skip the processing of log events that are known to not match the indexed field. Good fields to index are fields that you often need to query for and fields or values that match only a small fraction of the total log events. Common examples of indexes include request ID, session ID, user IDs, or instance IDs. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatchLogs-Field-Indexing.html\">Create field indexes to improve query performance and reduce costs</a> </p> <p>To find the fields that are in your log group events, use the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_GetLogGroupFields.html\">GetLogGroupFields</a> operation.</p> <p>For example, suppose you have created a field index for <code>requestId</code>. Then, any CloudWatch Logs Insights query on that log group that includes <code>requestId = <i>value</i> </code> or <code>requestId in [<i>value</i>, <i>value</i>, ...]</code> will attempt to process only the log events where the indexed field matches the specified value.</p> <p>Matches of log events to the names of indexed fields are case-sensitive. For example, an indexed field of <code>RequestId</code> won't match a log event containing <code>requestId</code>.</p> <p>You can have one account-level field index policy that applies to all log groups in the account. Or you can create as many as 20 account-level field index policies that are each scoped to a subset of log groups with the <code>selectionCriteria</code> parameter. If you have multiple account-level index policies with selection criteria, no two of them can use the same or overlapping log group name prefixes. For example, if you have one policy filtered to log groups that start with <code>my-log</code>, you can't have another field index policy filtered to <code>my-logpprod</code> or <code>my-logging</code>.</p> <p>If you create an account-level field index policy in a monitoring account in cross-account observability, the policy is applied only to the monitoring account and not to any source accounts.</p> <p>If you want to create a field index policy for a single log group, you can use <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutIndexPolicy.html\">PutIndexPolicy</a> instead of <code>PutAccountPolicy</code>. If you do so, that log group will use only that log-group level policy, and will ignore the account-level policy that you create with <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutAccountPolicy.html\">PutAccountPolicy</a>.</p>"}, "PutDataProtectionPolicy": {"name": "PutDataProtectionPolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutDataProtectionPolicyRequest"}, "output": {"shape": "PutDataProtectionPolicyResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "LimitExceededException"}, {"shape": "OperationAbortedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Creates a data protection policy for the specified log group. A data protection policy can help safeguard sensitive data that's ingested by the log group by auditing and masking the sensitive log data.</p> <important> <p>Sensitive data is detected and masked when it is ingested into the log group. When you set a data protection policy, log events ingested into the log group before that time are not masked.</p> </important> <p>By default, when a user views a log event that includes masked data, the sensitive data is replaced by asterisks. A user who has the <code>logs:Unmask</code> permission can use a <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_GetLogEvents.html\">GetLogEvents</a> or <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_FilterLogEvents.html\">FilterLogEvents</a> operation with the <code>unmask</code> parameter set to <code>true</code> to view the unmasked log events. Users with the <code>logs:Unmask</code> can also view unmasked data in the CloudWatch Logs console by running a CloudWatch Logs Insights query with the <code>unmask</code> query command.</p> <p>For more information, including a list of types of data that can be audited and masked, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/mask-sensitive-log-data.html\">Protect sensitive log data with masking</a>.</p> <p>The <code>PutDataProtectionPolicy</code> operation applies to only the specified log group. You can also use <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutAccountPolicy.html\">PutAccountPolicy</a> to create an account-level data protection policy that applies to all log groups in the account, including both existing log groups and log groups that are created level. If a log group has its own data protection policy and the account also has an account-level data protection policy, then the two policies are cumulative. Any sensitive term specified in either policy is masked.</p>"}, "PutDeliveryDestination": {"name": "PutDeliveryDestination", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutDeliveryDestinationRequest"}, "output": {"shape": "PutDeliveryDestinationResponse"}, "errors": [{"shape": "ServiceUnavailableException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Creates or updates a logical <i>delivery destination</i>. A delivery destination is an Amazon Web Services resource that represents an Amazon Web Services service that logs can be sent to. CloudWatch Logs, Amazon S3, and Firehose are supported as logs delivery destinations.</p> <p>To configure logs delivery between a supported Amazon Web Services service and a destination, you must do the following:</p> <ul> <li> <p>Create a delivery source, which is a logical object that represents the resource that is actually sending the logs. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutDeliverySource.html\">PutDeliverySource</a>.</p> </li> <li> <p>Use <code>PutDeliveryDestination</code> to create a <i>delivery destination</i> in the same account of the actual delivery destination. The delivery destination that you create is a logical object that represents the actual delivery destination. </p> </li> <li> <p>If you are delivering logs cross-account, you must use <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutDeliveryDestinationPolicy.html\">PutDeliveryDestinationPolicy</a> in the destination account to assign an IAM policy to the destination. This policy allows delivery to that destination. </p> </li> <li> <p>Use <code>CreateDelivery</code> to create a <i>delivery</i> by pairing exactly one delivery source and one delivery destination. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_CreateDelivery.html\">CreateDelivery</a>. </p> </li> </ul> <p>You can configure a single delivery source to send logs to multiple destinations by creating multiple deliveries. You can also create multiple deliveries to configure multiple delivery sources to send logs to the same delivery destination.</p> <p>Only some Amazon Web Services services support being configured as a delivery source. These services are listed as <b>Supported [V2 Permissions]</b> in the table at <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/AWS-logs-and-resource-policy.html\">Enabling logging from Amazon Web Services services.</a> </p> <p>If you use this operation to update an existing delivery destination, all the current delivery destination parameters are overwritten with the new parameter values that you specify.</p>"}, "PutDeliveryDestinationPolicy": {"name": "PutDeliveryDestinationPolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutDeliveryDestinationPolicyRequest"}, "output": {"shape": "PutDeliveryDestinationPolicyResponse"}, "errors": [{"shape": "ServiceUnavailableException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}], "documentation": "<p>Creates and assigns an IAM policy that grants permissions to CloudWatch Logs to deliver logs cross-account to a specified destination in this account. To configure the delivery of logs from an Amazon Web Services service in another account to a logs delivery destination in the current account, you must do the following:</p> <ul> <li> <p>Create a delivery source, which is a logical object that represents the resource that is actually sending the logs. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutDeliverySource.html\">PutDeliverySource</a>.</p> </li> <li> <p>Create a <i>delivery destination</i>, which is a logical object that represents the actual delivery destination. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutDeliveryDestination.html\">PutDeliveryDestination</a>.</p> </li> <li> <p>Use this operation in the destination account to assign an IAM policy to the destination. This policy allows delivery to that destination. </p> </li> <li> <p>Create a <i>delivery</i> by pairing exactly one delivery source and one delivery destination. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_CreateDelivery.html\">CreateDelivery</a>.</p> </li> </ul> <p>Only some Amazon Web Services services support being configured as a delivery source. These services are listed as <b>Supported [V2 Permissions]</b> in the table at <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/AWS-logs-and-resource-policy.html\">Enabling logging from Amazon Web Services services.</a> </p> <p>The contents of the policy must include two statements. One statement enables general logs delivery, and the other allows delivery to the chosen destination. See the examples for the needed policies.</p>"}, "PutDeliverySource": {"name": "PutDeliverySource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutDeliverySourceRequest"}, "output": {"shape": "PutDeliverySourceResponse"}, "errors": [{"shape": "ServiceUnavailableException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Creates or updates a logical <i>delivery source</i>. A delivery source represents an Amazon Web Services resource that sends logs to an logs delivery destination. The destination can be CloudWatch Logs, Amazon S3, or Firehose.</p> <p>To configure logs delivery between a delivery destination and an Amazon Web Services service that is supported as a delivery source, you must do the following:</p> <ul> <li> <p>Use <code>PutDeliverySource</code> to create a delivery source, which is a logical object that represents the resource that is actually sending the logs. </p> </li> <li> <p>Use <code>PutDeliveryDestination</code> to create a <i>delivery destination</i>, which is a logical object that represents the actual delivery destination. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutDeliveryDestination.html\">PutDeliveryDestination</a>.</p> </li> <li> <p>If you are delivering logs cross-account, you must use <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutDeliveryDestinationPolicy.html\">PutDeliveryDestinationPolicy</a> in the destination account to assign an IAM policy to the destination. This policy allows delivery to that destination. </p> </li> <li> <p>Use <code>CreateDelivery</code> to create a <i>delivery</i> by pairing exactly one delivery source and one delivery destination. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_CreateDelivery.html\">CreateDelivery</a>. </p> </li> </ul> <p>You can configure a single delivery source to send logs to multiple destinations by creating multiple deliveries. You can also create multiple deliveries to configure multiple delivery sources to send logs to the same delivery destination.</p> <p>Only some Amazon Web Services services support being configured as a delivery source. These services are listed as <b>Supported [V2 Permissions]</b> in the table at <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/AWS-logs-and-resource-policy.html\">Enabling logging from Amazon Web Services services.</a> </p> <p>If you use this operation to update an existing delivery source, all the current delivery source parameters are overwritten with the new parameter values that you specify.</p>"}, "PutDestination": {"name": "PutDestination", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutDestinationRequest"}, "output": {"shape": "PutDestinationResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "OperationAbortedException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Creates or updates a destination. This operation is used only to create destinations for cross-account subscriptions.</p> <p>A destination encapsulates a physical resource (such as an Amazon Kinesis stream). With a destination, you can subscribe to a real-time stream of log events for a different account, ingested using <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutLogEvents.html\">PutLogEvents</a>.</p> <p>Through an access policy, a destination controls what is written to it. By default, <code>PutDestination</code> does not set any access policy with the destination, which means a cross-account user cannot call <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutSubscriptionFilter.html\">PutSubscriptionFilter</a> against this destination. To enable this, the destination owner must call <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutDestinationPolicy.html\">PutDestinationPolicy</a> after <code>PutDestination</code>.</p> <p>To perform a <code>PutDestination</code> operation, you must also have the <code>iam:PassRole</code> permission.</p>"}, "PutDestinationPolicy": {"name": "PutDestinationPolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutDestinationPolicyRequest"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "OperationAbortedException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Creates or updates an access policy associated with an existing destination. An access policy is an <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/policies_overview.html\">IAM policy document</a> that is used to authorize claims to register a subscription filter against a given destination.</p>"}, "PutIndexPolicy": {"name": "PutIndexPolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutIndexPolicyRequest"}, "output": {"shape": "PutIndexPolicyResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}, {"shape": "LimitExceededException"}, {"shape": "OperationAbortedException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Creates or updates a <i>field index policy</i> for the specified log group. Only log groups in the Standard log class support field index policies. For more information about log classes, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch_Logs_Log_Classes.html\">Log classes</a>.</p> <p>You can use field index policies to create <i>field indexes</i> on fields found in log events in the log group. Creating field indexes speeds up and lowers the costs for CloudWatch Logs Insights queries that reference those field indexes, because these queries attempt to skip the processing of log events that are known to not match the indexed field. Good fields to index are fields that you often need to query for and fields or values that match only a small fraction of the total log events. Common examples of indexes include request ID, session ID, userID, and instance IDs. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatchLogs-Field-Indexing.html\">Create field indexes to improve query performance and reduce costs</a>.</p> <p>To find the fields that are in your log group events, use the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_GetLogGroupFields.html\">GetLogGroupFields</a> operation.</p> <p>For example, suppose you have created a field index for <code>requestId</code>. Then, any CloudWatch Logs Insights query on that log group that includes <code>requestId = <i>value</i> </code> or <code>requestId IN [<i>value</i>, <i>value</i>, ...]</code> will process fewer log events to reduce costs, and have improved performance.</p> <p>Each index policy has the following quotas and restrictions:</p> <ul> <li> <p>As many as 20 fields can be included in the policy.</p> </li> <li> <p>Each field name can include as many as 100 characters.</p> </li> </ul> <p>Matches of log events to the names of indexed fields are case-sensitive. For example, a field index of <code>RequestId</code> won't match a log event containing <code>requestId</code>.</p> <p>Log group-level field index policies created with <code>PutIndexPolicy</code> override account-level field index policies created with <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutAccountPolicy.html\">PutAccountPolicy</a>. If you use <code>PutIndexPolicy</code> to create a field index policy for a log group, that log group uses only that policy. The log group ignores any account-wide field index policy that you might have created.</p>"}, "PutIntegration": {"name": "PutIntegration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutIntegrationRequest"}, "output": {"shape": "PutIntegrationResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "LimitExceededException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ValidationException"}], "documentation": "<p>Creates an integration between CloudWatch Logs and another service in this account. Currently, only integrations with OpenSearch Service are supported, and currently you can have only one integration in your account.</p> <p>Integrating with OpenSearch Service makes it possible for you to create curated vended logs dashboards, powered by OpenSearch Service analytics. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatchLogs-OpenSearch-Dashboards.html\">Vended log dashboards powered by Amazon OpenSearch Service</a>.</p> <p>You can use this operation only to create a new integration. You can't modify an existing integration.</p>"}, "PutLogEvents": {"name": "PutLogEvents", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutLogEventsRequest"}, "output": {"shape": "PutLogEventsResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "InvalidSequenceTokenException"}, {"shape": "DataAlreadyAcceptedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}, {"shape": "UnrecognizedClientException"}], "documentation": "<p>Uploads a batch of log events to the specified log stream.</p> <important> <p>The sequence token is now ignored in <code>PutLogEvents</code> actions. <code>PutLogEvents</code> actions are always accepted and never return <code>InvalidSequenceTokenException</code> or <code>DataAlreadyAcceptedException</code> even if the sequence token is not valid. You can use parallel <code>PutLogEvents</code> actions on the same log stream. </p> </important> <p>The batch of events must satisfy the following constraints:</p> <ul> <li> <p>The maximum batch size is 1,048,576 bytes. This size is calculated as the sum of all event messages in UTF-8, plus 26 bytes for each log event.</p> </li> <li> <p>Events more than 2 hours in the future are rejected while processing remaining valid events.</p> </li> <li> <p>Events older than 14 days or preceding the log group's retention period are rejected while processing remaining valid events.</p> </li> <li> <p>The log events in the batch must be in chronological order by their timestamp. The timestamp is the time that the event occurred, expressed as the number of milliseconds after <code>Jan 1, 1970 00:00:00 UTC</code>. (In Amazon Web Services Tools for PowerShell and the Amazon Web Services SDK for .NET, the timestamp is specified in .NET format: <code>yyyy-mm-ddThh:mm:ss</code>. For example, <code>2017-09-15T13:45:30</code>.) </p> </li> <li> <p> A batch of log events in a single request must be in a chronological order. Otherwise, the operation fails.</p> </li> <li> <p>Each log event can be no larger than 1 MB.</p> </li> <li> <p>The maximum number of log events in a batch is 10,000.</p> </li> <li> <p>For valid events (within 14 days in the past to 2 hours in future), the time span in a single batch cannot exceed 24 hours. Otherwise, the operation fails.</p> </li> </ul> <important> <p>The quota of five requests per second per log stream has been removed. Instead, <code>PutLogEvents</code> actions are throttled based on a per-second per-account quota. You can request an increase to the per-second throttling quota by using the Service Quotas service.</p> </important> <p>If a call to <code>PutLogEvents</code> returns \"UnrecognizedClientException\" the most likely cause is a non-valid Amazon Web Services access key ID or secret key. </p>"}, "PutMetricFilter": {"name": "PutMetricFilter", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutMetricFilterRequest"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}, {"shape": "OperationAbortedException"}, {"shape": "LimitExceededException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidOperationException"}], "documentation": "<p>Creates or updates a metric filter and associates it with the specified log group. With metric filters, you can configure rules to extract metric data from log events ingested through <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutLogEvents.html\">PutLogEvents</a>.</p> <p>The maximum number of metric filters that can be associated with a log group is 100.</p> <p>Using regular expressions in filter patterns is supported. For these filters, there is a quota of two regular expression patterns within a single filter pattern. There is also a quota of five regular expression patterns per log group. For more information about using regular expressions in filter patterns, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/FilterAndPatternSyntax.html\"> Filter pattern syntax for metric filters, subscription filters, filter log events, and Live Tail</a>.</p> <p>When you create a metric filter, you can also optionally assign a unit and dimensions to the metric that is created.</p> <important> <p>Metrics extracted from log events are charged as custom metrics. To prevent unexpected high charges, do not specify high-cardinality fields such as <code>IPAddress</code> or <code>requestID</code> as dimensions. Each different value found for a dimension is treated as a separate metric and accrues charges as a separate custom metric. </p> <p>CloudWatch Logs might disable a metric filter if it generates 1,000 different name/value pairs for your specified dimensions within one hour.</p> <p>You can also set up a billing alarm to alert you if your charges are higher than expected. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/monitor_estimated_charges_with_cloudwatch.html\"> Creating a Billing Alarm to Monitor Your Estimated Amazon Web Services Charges</a>. </p> </important>"}, "PutQueryDefinition": {"name": "PutQueryDefinition", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutQueryDefinitionRequest"}, "output": {"shape": "PutQueryDefinitionResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "LimitExceededException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Creates or updates a query definition for CloudWatch Logs Insights. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/AnalyzingLogData.html\">Analyzing Log Data with CloudWatch Logs Insights</a>.</p> <p>To update a query definition, specify its <code>queryDefinitionId</code> in your request. The values of <code>name</code>, <code>queryString</code>, and <code>logGroupNames</code> are changed to the values that you specify in your update operation. No current values are retained from the current query definition. For example, imagine updating a current query definition that includes log groups. If you don't specify the <code>logGroupNames</code> parameter in your update operation, the query definition changes to contain no log groups.</p> <p>You must have the <code>logs:PutQueryDefinition</code> permission to be able to perform this operation.</p>"}, "PutResourcePolicy": {"name": "PutResourcePolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutResourcePolicyRequest"}, "output": {"shape": "PutResourcePolicyResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "LimitExceededException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Creates or updates a resource policy allowing other Amazon Web Services services to put log events to this account, such as Amazon Route 53. An account can have up to 10 resource policies per Amazon Web Services Region.</p>"}, "PutRetentionPolicy": {"name": "PutRetentionPolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutRetentionPolicyRequest"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}, {"shape": "OperationAbortedException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Sets the retention of the specified log group. With a retention policy, you can configure the number of days for which to retain log events in the specified log group.</p> <note> <p>CloudWatch Logs doesn't immediately delete log events when they reach their retention setting. It typically takes up to 72 hours after that before log events are deleted, but in rare situations might take longer.</p> <p>To illustrate, imagine that you change a log group to have a longer retention setting when it contains log events that are past the expiration date, but haven't been deleted. Those log events will take up to 72 hours to be deleted after the new retention date is reached. To make sure that log data is deleted permanently, keep a log group at its lower retention setting until 72 hours after the previous retention period ends. Alternatively, wait to change the retention setting until you confirm that the earlier log events are deleted. </p> <p>When log events reach their retention setting they are marked for deletion. After they are marked for deletion, they do not add to your archival storage costs anymore, even if they are not actually deleted until later. These log events marked for deletion are also not included when you use an API to retrieve the <code>storedBytes</code> value to see how many bytes a log group is storing.</p> </note>"}, "PutSubscriptionFilter": {"name": "PutSubscriptionFilter", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutSubscriptionFilterRequest"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}, {"shape": "OperationAbortedException"}, {"shape": "LimitExceededException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidOperationException"}], "documentation": "<p>Creates or updates a subscription filter and associates it with the specified log group. With subscription filters, you can subscribe to a real-time stream of log events ingested through <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutLogEvents.html\">PutLogEvents</a> and have them delivered to a specific destination. When log events are sent to the receiving service, they are Base64 encoded and compressed with the GZIP format.</p> <p>The following destinations are supported for subscription filters:</p> <ul> <li> <p>An Amazon Kinesis data stream belonging to the same account as the subscription filter, for same-account delivery.</p> </li> <li> <p>A logical destination created with <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutDestination.html\">PutDestination</a> that belongs to a different account, for cross-account delivery. We currently support Kinesis Data Streams and Firehose as logical destinations.</p> </li> <li> <p>An Amazon Kinesis Data Firehose delivery stream that belongs to the same account as the subscription filter, for same-account delivery.</p> </li> <li> <p>An Lambda function that belongs to the same account as the subscription filter, for same-account delivery.</p> </li> </ul> <p>Each log group can have up to two subscription filters associated with it. If you are updating an existing filter, you must specify the correct name in <code>filterName</code>. </p> <p>Using regular expressions in filter patterns is supported. For these filters, there is a quotas of quota of two regular expression patterns within a single filter pattern. There is also a quota of five regular expression patterns per log group. For more information about using regular expressions in filter patterns, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/FilterAndPatternSyntax.html\"> Filter pattern syntax for metric filters, subscription filters, filter log events, and Live Tail</a>.</p> <p>To perform a <code>PutSubscriptionFilter</code> operation for any destination except a Lambda function, you must also have the <code>iam:PassRole</code> permission.</p>"}, "PutTransformer": {"name": "PutTransformer", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutTransformerRequest"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}, {"shape": "LimitExceededException"}, {"shape": "OperationAbortedException"}, {"shape": "InvalidOperationException"}], "documentation": "<p>Creates or updates a <i>log transformer</i> for a single log group. You use log transformers to transform log events into a different format, making them easier for you to process and analyze. You can also transform logs from different sources into standardized formats that contains relevant, source-specific information.</p> <p>After you have created a transformer, CloudWatch Logs performs the transformations at the time of log ingestion. You can then refer to the transformed versions of the logs during operations such as querying with CloudWatch Logs Insights or creating metric filters or subscription filers.</p> <p>You can also use a transformer to copy metadata from metadata keys into the log events themselves. This metadata can include log group name, log stream name, account ID and Region.</p> <p>A transformer for a log group is a series of processors, where each processor applies one type of transformation to the log events ingested into this log group. The processors work one after another, in the order that you list them, like a pipeline. For more information about the available processors to use in a transformer, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-Processors\"> Processors that you can use</a>.</p> <p>Having log events in standardized format enables visibility across your applications for your log analysis, reporting, and alarming needs. CloudWatch Logs provides transformation for common log types with out-of-the-box transformation templates for major Amazon Web Services log sources such as VPC flow logs, Lambda, and Amazon RDS. You can use pre-built transformation templates or create custom transformation policies.</p> <p>You can create transformers only for the log groups in the Standard log class.</p> <p>You can also set up a transformer at the account level. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutAccountPolicy.html\">PutAccountPolicy</a>. If there is both a log-group level transformer created with <code>PutTransformer</code> and an account-level transformer that could apply to the same log group, the log group uses only the log-group level transformer. It ignores the account-level transformer.</p>"}, "StartLiveTail": {"name": "StartLiveTail", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StartLiveTailRequest"}, "output": {"shape": "StartLiveTailResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}, {"shape": "LimitExceededException"}, {"shape": "InvalidOperationException"}], "documentation": "<p>Starts a Live Tail streaming session for one or more log groups. A Live Tail session returns a stream of log events that have been recently ingested in the log groups. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatchLogs_LiveTail.html\">Use Live Tail to view logs in near real time</a>. </p> <p>The response to this operation is a response stream, over which the server sends live log events and the client receives them.</p> <p>The following objects are sent over the stream:</p> <ul> <li> <p>A single <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_LiveTailSessionStart.html\">LiveTailSessionStart</a> object is sent at the start of the session.</p> </li> <li> <p>Every second, a <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_LiveTailSessionUpdate.html\">LiveTailSessionUpdate</a> object is sent. Each of these objects contains an array of the actual log events.</p> <p>If no new log events were ingested in the past second, the <code>LiveTailSessionUpdate</code> object will contain an empty array.</p> <p>The array of log events contained in a <code>LiveTailSessionUpdate</code> can include as many as 500 log events. If the number of log events matching the request exceeds 500 per second, the log events are sampled down to 500 log events to be included in each <code>LiveTailSessionUpdate</code> object.</p> <p>If your client consumes the log events slower than the server produces them, CloudWatch Logs buffers up to 10 <code>LiveTailSessionUpdate</code> events or 5000 log events, after which it starts dropping the oldest events.</p> </li> <li> <p>A <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_StartLiveTailResponseStream.html#CWL-Type-StartLiveTailResponseStream-SessionStreamingException\">SessionStreamingException</a> object is returned if an unknown error occurs on the server side.</p> </li> <li> <p>A <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_StartLiveTailResponseStream.html#CWL-Type-StartLiveTailResponseStream-SessionTimeoutException\">SessionTimeoutException</a> object is returned when the session times out, after it has been kept open for three hours.</p> </li> </ul> <note> <p>The <code>StartLiveTail</code> API routes requests to <code>streaming-logs.<i>Region</i>.amazonaws.com</code> using SDK host prefix injection. VPC endpoint support is not available for this API.</p> </note> <important> <p>You can end a session before it times out by closing the session stream or by closing the client that is receiving the stream. The session also ends if the established connection between the client and the server breaks.</p> </important> <p>For examples of using an SDK to start a Live Tail session, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/example_cloudwatch-logs_StartLiveTail_section.html\"> Start a Live Tail session using an Amazon Web Services SDK</a>.</p>", "endpoint": {"hostPrefix": "streaming-"}}, "StartQuery": {"name": "Start<PERSON>uery", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StartQueryRequest"}, "output": {"shape": "StartQueryResponse"}, "errors": [{"shape": "MalformedQueryException"}, {"shape": "InvalidParameterException"}, {"shape": "LimitExceededException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Starts a query of one or more log groups using CloudWatch Logs Insights. You specify the log groups and time range to query and the query string to use.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CWL_QuerySyntax.html\">CloudWatch Logs Insights Query Syntax</a>.</p> <p>After you run a query using <code>StartQuery</code>, the query results are stored by CloudWatch Logs. You can use <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_GetQueryResults.html\">GetQueryResults</a> to retrieve the results of a query, using the <code>queryId</code> that <code>StartQuery</code> returns. </p> <note> <p>To specify the log groups to query, a <code>StartQuery</code> operation must include one of the following:</p> <ul> <li> <p>Either exactly one of the following parameters: <code>logGroupName</code>, <code>logGroupNames</code>, or <code>logGroupIdentifiers</code> </p> </li> <li> <p>Or the <code>queryString</code> must include a <code>SOURCE</code> command to select log groups for the query. The <code>SOURCE</code> command can select log groups based on log group name prefix, account ID, and log class. </p> <p>For more information about the <code>SOURCE</code> command, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CWL_QuerySyntax-Source.html\">SOURCE</a>.</p> </li> </ul> </note> <p>If you have associated a KMS key with the query results in this account, then <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_StartQuery.html\">StartQuery</a> uses that key to encrypt the results when it stores them. If no key is associated with query results, the query results are encrypted with the default CloudWatch Logs encryption method.</p> <p>Queries time out after 60 minutes of runtime. If your queries are timing out, reduce the time range being searched or partition your query into a number of queries.</p> <p>If you are using CloudWatch cross-account observability, you can use this operation in a monitoring account to start a query in a linked source account. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/CloudWatch-Unified-Cross-Account.html\">CloudWatch cross-account observability</a>. For a cross-account <code>StartQuery</code> operation, the query definition must be defined in the monitoring account.</p> <p>You can have up to 30 concurrent CloudWatch Logs insights queries, including queries that have been added to dashboards. </p>"}, "StopQuery": {"name": "StopQuery", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StopQueryRequest"}, "output": {"shape": "StopQueryResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Stops a CloudWatch Logs Insights query that is in progress. If the query has already ended, the operation returns an error indicating that the specified query is not running.</p>"}, "TagLogGroup": {"name": "TagLogGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "TagLogGroupRequest"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterException"}], "documentation": "<important> <p>The TagLogGroup operation is on the path to deprecation. We recommend that you use <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_TagResource.html\">TagResource</a> instead.</p> </important> <p>Adds or updates the specified tags for the specified log group.</p> <p>To list the tags for a log group, use <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_ListTagsForResource.html\">ListTagsForResource</a>. To remove tags, use <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_UntagResource.html\">UntagResource</a>.</p> <p>For more information about tags, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/Working-with-log-groups-and-streams.html#log-group-tagging\">Tag Log Groups in Amazon CloudWatch Logs</a> in the <i>Amazon CloudWatch Logs User Guide</i>.</p> <p>CloudWatch Logs doesn't support IAM policies that prevent users from assigning specified tags to log groups using the <code>aws:Resource/<i>key-name</i> </code> or <code>aws:TagKeys</code> condition keys. For more information about using tags to control access, see <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/access_tags.html\">Controlling access to Amazon Web Services resources using tags</a>.</p>", "deprecated": true, "deprecatedMessage": "Please use the generic tagging API TagResource"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "TagResourceRequest"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}, {"shape": "TooManyTagsException"}], "documentation": "<p>Assigns one or more tags (key-value pairs) to the specified CloudWatch Logs resource. Currently, the only CloudWatch Logs resources that can be tagged are log groups and destinations. </p> <p>Tags can help you organize and categorize your resources. You can also use them to scope user permissions by granting a user permission to access or change only resources with certain tag values.</p> <p>Tags don't have any semantic meaning to Amazon Web Services and are interpreted strictly as strings of characters.</p> <p>You can use the <code>TagResource</code> action with a resource that already has tags. If you specify a new tag key for the alarm, this tag is appended to the list of tags associated with the alarm. If you specify a tag key that is already associated with the alarm, the new tag value that you specify replaces the previous value for that tag.</p> <p>You can associate as many as 50 tags with a CloudWatch Logs resource.</p>"}, "TestMetricFilter": {"name": "TestMetricFilter", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "TestMetricFilterRequest"}, "output": {"shape": "TestMetricFilterResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Tests the filter pattern of a metric filter against a sample of log event messages. You can use this operation to validate the correctness of a metric filter pattern.</p>"}, "TestTransformer": {"name": "TestTransformer", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "TestTransformerRequest"}, "output": {"shape": "TestTransformerResponse"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ServiceUnavailableException"}, {"shape": "InvalidOperationException"}], "documentation": "<p>Use this operation to test a log transformer. You enter the transformer configuration and a set of log events to test with. The operation responds with an array that includes the original log events and the transformed versions.</p>"}, "UntagLogGroup": {"name": "UntagLogGroup", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UntagLogGroupRequest"}, "errors": [{"shape": "ResourceNotFoundException"}], "documentation": "<important> <p>The UntagLogGroup operation is on the path to deprecation. We recommend that you use <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_UntagResource.html\">UntagResource</a> instead.</p> </important> <p>Removes the specified tags from the specified log group.</p> <p>To list the tags for a log group, use <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_ListTagsForResource.html\">ListTagsForResource</a>. To add tags, use <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_TagResource.html\">TagResource</a>.</p> <p>CloudWatch Logs doesn't support IAM policies that prevent users from assigning specified tags to log groups using the <code>aws:Resource/<i>key-name</i> </code> or <code>aws:TagKeys</code> condition keys. </p>", "deprecated": true, "deprecatedMessage": "Please use the generic tagging API UntagResource"}, "UntagResource": {"name": "UntagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UntagResourceRequest"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Removes one or more tags from the specified resource.</p>"}, "UpdateAnomaly": {"name": "UpdateAnomaly", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateAnomalyRequest"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}, {"shape": "OperationAbortedException"}], "documentation": "<p>Use this operation to <i>suppress</i> anomaly detection for a specified anomaly or pattern. If you suppress an anomaly, CloudWatch Logs won't report new occurrences of that anomaly and won't update that anomaly with new data. If you suppress a pattern, CloudWatch Logs won't report any anomalies related to that pattern.</p> <p>You must specify either <code>anomalyId</code> or <code>patternId</code>, but you can't specify both parameters in the same operation.</p> <p>If you have previously used this operation to suppress detection of a pattern or anomaly, you can use it again to cause CloudWatch Logs to end the suppression. To do this, use this operation and specify the anomaly or pattern to stop suppressing, and omit the <code>suppressionType</code> and <code>suppressionPeriod</code> parameters.</p>"}, "UpdateDeliveryConfiguration": {"name": "UpdateDeliveryConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateDeliveryConfigurationRequest"}, "output": {"shape": "UpdateDeliveryConfigurationResponse"}, "errors": [{"shape": "ServiceUnavailableException"}, {"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Use this operation to update the configuration of a <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_Delivery.html\">delivery</a> to change either the S3 path pattern or the format of the delivered logs. You can't use this operation to change the source or destination of the delivery.</p>"}, "UpdateLogAnomalyDetector": {"name": "UpdateLogAnomalyDetector", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateLogAnomalyDetectorRequest"}, "errors": [{"shape": "InvalidParameterException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceUnavailableException"}, {"shape": "OperationAbortedException"}], "documentation": "<p>Updates an existing log anomaly detector.</p>"}}, "shapes": {"AccessDeniedException": {"type": "structure", "members": {}, "documentation": "<p>You don't have sufficient permissions to perform this action.</p>", "exception": true}, "AccessPolicy": {"type": "string", "min": 1}, "AccountId": {"type": "string", "max": 12, "min": 12, "pattern": "^\\d{12}$"}, "AccountIds": {"type": "list", "member": {"shape": "AccountId"}, "max": 20, "min": 0}, "AccountPolicies": {"type": "list", "member": {"shape": "AccountPolicy"}}, "AccountPolicy": {"type": "structure", "members": {"policyName": {"shape": "PolicyName", "documentation": "<p>The name of the account policy.</p>"}, "policyDocument": {"shape": "AccountPolicyDocument", "documentation": "<p>The policy document for this account policy.</p> <p>The JSON specified in <code>policyDocument</code> can be up to 30,720 characters.</p>"}, "lastUpdatedTime": {"shape": "Timestamp", "documentation": "<p>The date and time that this policy was most recently updated.</p>"}, "policyType": {"shape": "PolicyType", "documentation": "<p>The type of policy for this account policy.</p>"}, "scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>The scope of the account policy.</p>"}, "selectionCriteria": {"shape": "SelectionCriteria", "documentation": "<p>The log group selection criteria that is used for this policy.</p>"}, "accountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID that the policy applies to.</p>"}}, "documentation": "<p>A structure that contains information about one CloudWatch Logs account policy.</p>"}, "AccountPolicyDocument": {"type": "string"}, "AddKeyEntries": {"type": "list", "member": {"shape": "AddKeyEntry"}, "max": 5, "min": 1}, "AddKeyEntry": {"type": "structure", "required": ["key", "value"], "members": {"key": {"shape": "Key", "documentation": "<p>The key of the new entry to be added to the log event</p>"}, "value": {"shape": "AddKeyValue", "documentation": "<p>The value of the new entry to be added to the log event</p>"}, "overwriteIfExists": {"shape": "OverwriteIfExists", "documentation": "<p>Specifies whether to overwrite the value if the key already exists in the log event. If you omit this, the default is <code>false</code>.</p>"}}, "documentation": "<p>This object defines one key that will be added with the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-addKey\"> addKeys</a> processor.</p>"}, "AddKeyValue": {"type": "string", "max": 256, "min": 1}, "AddKeys": {"type": "structure", "required": ["entries"], "members": {"entries": {"shape": "AddKeyEntries", "documentation": "<p>An array of objects, where each object contains the information about one key to add to the log event. </p>"}}, "documentation": "<p>This processor adds new key-value pairs to the log event. </p> <p>For more information about this processor including examples, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-addKeys\"> addKeys</a> in the <i>CloudWatch Logs User Guide</i>.</p>"}, "AllowedActionForAllowVendedLogsDeliveryForResource": {"type": "string"}, "AllowedFieldDelimiters": {"type": "list", "member": {"shape": "FieldDelimiter"}}, "AllowedFields": {"type": "list", "member": {"shape": "RecordField"}}, "AmazonResourceName": {"type": "string", "max": 1011, "min": 1, "pattern": "[\\w+=/:,.@-]*"}, "Anomalies": {"type": "list", "member": {"shape": "Anomaly"}}, "Anomaly": {"type": "structure", "required": ["anomalyId", "patternId", "anomalyDetectorArn", "patternString", "firstSeen", "lastSeen", "description", "active", "state", "histogram", "logSamples", "patternTokens", "logGroupArnList"], "members": {"anomalyId": {"shape": "AnomalyId", "documentation": "<p>The unique ID that CloudWatch Logs assigned to this anomaly.</p>"}, "patternId": {"shape": "<PERSON><PERSON>Id", "documentation": "<p>The ID of the pattern used to help identify this anomaly.</p>"}, "anomalyDetectorArn": {"shape": "AnomalyDetectorArn", "documentation": "<p>The ARN of the anomaly detector that identified this anomaly.</p>"}, "patternString": {"shape": "PatternString", "documentation": "<p>The pattern used to help identify this anomaly, in string format.</p>"}, "patternRegex": {"shape": "PatternRegex", "documentation": "<p>The pattern used to help identify this anomaly, in regular expression format.</p>"}, "priority": {"shape": "Priority", "documentation": "<p>The priority level of this anomaly, as determined by CloudWatch Logs. Priority is computed based on log severity labels such as <code>FATAL</code> and <code>ERROR</code> and the amount of deviation from the baseline. Possible values are <code>HIGH</code>, <code>MEDIUM</code>, and <code>LOW</code>.</p>"}, "firstSeen": {"shape": "EpochMill<PERSON>", "documentation": "<p>The date and time when the anomaly detector first saw this anomaly. It is specified as epoch time, which is the number of seconds since <code>January 1, 1970, 00:00:00 UTC</code>.</p>"}, "lastSeen": {"shape": "EpochMill<PERSON>", "documentation": "<p>The date and time when the anomaly detector most recently saw this anomaly. It is specified as epoch time, which is the number of seconds since <code>January 1, 1970, 00:00:00 UTC</code>.</p>"}, "description": {"shape": "Description", "documentation": "<p>A human-readable description of the anomaly. This description is generated by CloudWatch Logs.</p>"}, "active": {"shape": "Boolean", "documentation": "<p>Specifies whether this anomaly is still ongoing.</p>"}, "state": {"shape": "State", "documentation": "<p>Indicates the current state of this anomaly. If it is still being treated as an anomaly, the value is <code>Active</code>. If you have suppressed this anomaly by using the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_UpdateAnomaly.html\">UpdateAnomaly</a> operation, the value is <code>Suppressed</code>. If this behavior is now considered to be normal, the value is <code>Baseline</code>.</p>"}, "histogram": {"shape": "Histogram", "documentation": "<p>A map showing times when the anomaly detector ran, and the number of occurrences of this anomaly that were detected at each of those runs. The times are specified in epoch time, which is the number of seconds since <code>January 1, 1970, 00:00:00 UTC</code>.</p>"}, "logSamples": {"shape": "LogSamples", "documentation": "<p>An array of sample log event messages that are considered to be part of this anomaly.</p>"}, "patternTokens": {"shape": "PatternTokens", "documentation": "<p>An array of structures where each structure contains information about one token that makes up the pattern.</p>"}, "logGroupArnList": {"shape": "LogGroupArnList", "documentation": "<p>An array of ARNS of the log groups that contained log events considered to be part of this anomaly.</p>"}, "suppressed": {"shape": "Boolean", "documentation": "<p>Indicates whether this anomaly is currently suppressed. To suppress an anomaly, use <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_UpdateAnomaly.html\">UpdateAnomaly</a>.</p>"}, "suppressedDate": {"shape": "EpochMill<PERSON>", "documentation": "<p>If the anomaly is suppressed, this indicates when it was suppressed.</p>"}, "suppressedUntil": {"shape": "EpochMill<PERSON>", "documentation": "<p>If the anomaly is suppressed, this indicates when the suppression will end. If this value is <code>0</code>, the anomaly was suppressed with no expiration, with the <code>INFINITE</code> value.</p>"}, "isPatternLevelSuppression": {"shape": "Boolean", "documentation": "<p>If this anomaly is suppressed, this field is <code>true</code> if the suppression is because the pattern is suppressed. If <code>false</code>, then only this particular anomaly is suppressed.</p>"}}, "documentation": "<p>This structure represents one anomaly that has been found by a logs anomaly detector.</p> <p>For more information about patterns and anomalies, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_CreateLogAnomalyDetector.html\">CreateLogAnomalyDetector</a>. </p>"}, "AnomalyDetector": {"type": "structure", "members": {"anomalyDetectorArn": {"shape": "AnomalyDetectorArn", "documentation": "<p>The ARN of the anomaly detector.</p>"}, "detectorName": {"shape": "DetectorName", "documentation": "<p>The name of the anomaly detector.</p>"}, "logGroupArnList": {"shape": "LogGroupArnList", "documentation": "<p>A list of the ARNs of the log groups that this anomaly detector watches.</p>"}, "evaluationFrequency": {"shape": "EvaluationFrequency", "documentation": "<p>Specifies how often the anomaly detector runs and look for anomalies.</p>"}, "filterPattern": {"shape": "FilterPattern"}, "anomalyDetectorStatus": {"shape": "AnomalyDetectorStatus", "documentation": "<p>Specifies the current status of the anomaly detector. To pause an anomaly detector, use the <code>enabled</code> parameter in the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_UpdateLogAnomalyDetector.html\">UpdateLogAnomalyDetector</a> operation.</p>"}, "kmsKeyId": {"shape": "KmsKeyId", "documentation": "<p>The ARN of the KMS key assigned to this anomaly detector, if any.</p>"}, "creationTimeStamp": {"shape": "EpochMill<PERSON>", "documentation": "<p>The date and time when this anomaly detector was created.</p>"}, "lastModifiedTimeStamp": {"shape": "EpochMill<PERSON>", "documentation": "<p>The date and time when this anomaly detector was most recently modified.</p>"}, "anomalyVisibilityTime": {"shape": "AnomalyVisibilityTime", "documentation": "<p>The number of days used as the life cycle of anomalies. After this time, anomalies are automatically baselined and the anomaly detector model will treat new occurrences of similar event as normal. </p>"}}, "documentation": "<p>Contains information about one anomaly detector in the account.</p>"}, "AnomalyDetectorArn": {"type": "string", "min": 1, "pattern": "[\\w#+=/:,.@-]*"}, "AnomalyDetectorStatus": {"type": "string", "enum": ["INITIALIZING", "TRAINING", "ANALYZING", "FAILED", "DELETED", "PAUSED"]}, "AnomalyDetectors": {"type": "list", "member": {"shape": "AnomalyDetector"}}, "AnomalyId": {"type": "string", "max": 36, "min": 36}, "AnomalyVisibilityTime": {"type": "long", "max": 90, "min": 7}, "ApplyOnTransformedLogs": {"type": "boolean"}, "Arn": {"type": "string"}, "AssociateKmsKeyRequest": {"type": "structure", "required": ["kmsKeyId"], "members": {"logGroupName": {"shape": "LogGroupName", "documentation": "<p>The name of the log group.</p> <p>In your <code>AssociateKmsKey</code> operation, you must specify either the <code>resourceIdentifier</code> parameter or the <code>logGroup</code> parameter, but you can't specify both.</p>"}, "kmsKeyId": {"shape": "KmsKeyId", "documentation": "<p>The Amazon Resource Name (ARN) of the KMS key to use when encrypting log data. This must be a symmetric KMS key. For more information, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html#arn-syntax-kms\">Amazon Resource Names</a> and <a href=\"https://docs.aws.amazon.com/kms/latest/developerguide/symmetric-asymmetric.html\">Using Symmetric and Asymmetric Keys</a>.</p>"}, "resourceIdentifier": {"shape": "ResourceIdentifier", "documentation": "<p>Specifies the target for this operation. You must specify one of the following:</p> <ul> <li> <p>Specify the following ARN to have future <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_GetQueryResults.html\">GetQueryResults</a> operations in this account encrypt the results with the specified KMS key. Replace <i>REGION</i> and <i>ACCOUNT_ID</i> with your Region and account ID.</p> <p> <code>arn:aws:logs:<i>REGION</i>:<i>ACCOUNT_ID</i>:query-result:*</code> </p> </li> <li> <p>Specify the ARN of a log group to have CloudWatch Logs use the KMS key to encrypt log events that are ingested and stored by that log group. The log group ARN must be in the following format. Replace <i>REGION</i> and <i>ACCOUNT_ID</i> with your Region and account ID.</p> <p> <code>arn:aws:logs:<i>REGION</i>:<i>ACCOUNT_ID</i>:log-group:<i>LOG_GROUP_NAME</i> </code> </p> </li> </ul> <p>In your <code>AssociateKmsKey</code> operation, you must specify either the <code>resourceIdentifier</code> parameter or the <code>logGroup</code> parameter, but you can't specify both.</p>"}}}, "Baseline": {"type": "boolean"}, "Boolean": {"type": "boolean"}, "CSV": {"type": "structure", "members": {"quoteCharacter": {"shape": "QuoteCharacter", "documentation": "<p>The character used used as a text qualifier for a single column of data. If you omit this, the double quotation mark <code>\"</code> character is used.</p>"}, "delimiter": {"shape": "Delimiter", "documentation": "<p>The character used to separate each column in the original comma-separated value log event. If you omit this, the processor looks for the comma <code>,</code> character as the delimiter.</p>"}, "columns": {"shape": "Columns", "documentation": "<p>An array of names to use for the columns in the transformed log event.</p> <p>If you omit this, default column names (<code>[column_1, column_2 ...]</code>) are used.</p>"}, "source": {"shape": "Source", "documentation": "<p>The path to the field in the log event that has the comma separated values to be parsed. If you omit this value, the whole log message is processed.</p>"}}, "documentation": "<p>The <code>CSV</code> processor parses comma-separated values (CSV) from the log events into columns.</p> <p>For more information about this processor including examples, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-csv\"> csv</a> in the <i>CloudWatch Logs User Guide</i>.</p>"}, "CancelExportTaskRequest": {"type": "structure", "required": ["taskId"], "members": {"taskId": {"shape": "ExportTaskId", "documentation": "<p>The ID of the export task.</p>"}}}, "ClientToken": {"type": "string", "max": 128, "min": 36, "pattern": "\\S{36,128}"}, "CollectionRetentionDays": {"type": "integer", "max": 30, "min": 1}, "Column": {"type": "string", "max": 128, "min": 1}, "Columns": {"type": "list", "member": {"shape": "Column"}, "max": 100}, "ConfigurationTemplate": {"type": "structure", "members": {"service": {"shape": "Service", "documentation": "<p>A string specifying which service this configuration template applies to. For more information about supported services see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/AWS-logs-and-resource-policy.html\">Enable logging from Amazon Web Services services.</a>.</p>"}, "logType": {"shape": "LogType", "documentation": "<p>A string specifying which log type this configuration template applies to.</p>"}, "resourceType": {"shape": "ResourceType", "documentation": "<p>A string specifying which resource type this configuration template applies to.</p>"}, "deliveryDestinationType": {"shape": "DeliveryDestinationType", "documentation": "<p>A string specifying which destination type this configuration template applies to.</p>"}, "defaultDeliveryConfigValues": {"shape": "ConfigurationTemplateDeliveryConfigValues", "documentation": "<p>A mapping that displays the default value of each property within a delivery's configuration, if it is not specified in the request.</p>"}, "allowedFields": {"shape": "AllowedFields", "documentation": "<p>The allowed fields that a caller can use in the <code>recordFields</code> parameter of a <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_CreateDelivery.html\">CreateDelivery</a> or <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_UpdateDeliveryConfiguration.html\">UpdateDeliveryConfiguration</a> operation.</p>"}, "allowedOutputFormats": {"shape": "OutputFormats", "documentation": "<p>The list of delivery destination output formats that are supported by this log source.</p>"}, "allowedActionForAllowVendedLogsDeliveryForResource": {"shape": "AllowedActionForAllowVendedLogsDeliveryForResource", "documentation": "<p>The action permissions that a caller needs to have to be able to successfully create a delivery source on the desired resource type when calling <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutDeliverySource.html\">PutDeliverySource</a>.</p>"}, "allowedFieldDelimiters": {"shape": "AllowedFieldDelimiters", "documentation": "<p>The valid values that a caller can use as field delimiters when calling <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_CreateDelivery.html\">CreateDelivery</a> or <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_UpdateDeliveryConfiguration.html\">UpdateDeliveryConfiguration</a> on a delivery that delivers in <code>Plain</code>, <code>W3C</code>, or <code>Raw</code> format.</p>"}, "allowedSuffixPathFields": {"shape": "RecordFields", "documentation": "<p>The list of variable fields that can be used in the suffix path of a delivery that delivers to an S3 bucket.</p>"}}, "documentation": "<p>A structure containing information about the deafult settings and available settings that you can use to configure a <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_Delivery.html\">delivery</a> or a <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_DeliveryDestination.html\">delivery destination</a>.</p>"}, "ConfigurationTemplateDeliveryConfigValues": {"type": "structure", "members": {"recordFields": {"shape": "RecordFields", "documentation": "<p>The default record fields that will be delivered when a list of record fields is not provided in a <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_CreateDelivery.html\">CreateDelivery</a> operation.</p>"}, "fieldDelimiter": {"shape": "FieldDelimiter", "documentation": "<p>The default field delimiter that is used in a <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_CreateDelivery.html\">CreateDelivery</a> operation when the field delimiter is not specified in that operation. The field delimiter is used only when the final output delivery is in <code>Plain</code>, <code>W3C</code>, or <code>Raw</code> format.</p>"}, "s3DeliveryConfiguration": {"shape": "S3DeliveryConfiguration", "documentation": "<p>The delivery parameters that are used when you create a delivery to a delivery destination that is an S3 Bucket.</p>"}}, "documentation": "<p>This structure contains the default values that are used for each configuration parameter when you use <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_CreateDelivery.html\">CreateDelivery</a> to create a deliver under the current service type, resource type, and log type.</p>"}, "ConfigurationTemplates": {"type": "list", "member": {"shape": "ConfigurationTemplate"}}, "ConflictException": {"type": "structure", "members": {}, "documentation": "<p>This operation attempted to create a resource that already exists.</p>", "exception": true}, "CopyValue": {"type": "structure", "required": ["entries"], "members": {"entries": {"shape": "CopyValueEntries", "documentation": "<p>An array of <code>CopyValueEntry</code> objects, where each object contains the information about one field value to copy. </p>"}}, "documentation": "<p>This processor copies values within a log event. You can also use this processor to add metadata to log events by copying the values of the following metadata keys into the log events: <code>@logGroupName</code>, <code>@logGroupStream</code>, <code>@accountId</code>, <code>@regionName</code>. </p> <p>For more information about this processor including examples, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-copyValue\"> copyValue</a> in the <i>CloudWatch Logs User Guide</i>.</p>"}, "CopyValueEntries": {"type": "list", "member": {"shape": "CopyValueEntry"}, "max": 5, "min": 1}, "CopyValueEntry": {"type": "structure", "required": ["source", "target"], "members": {"source": {"shape": "Source", "documentation": "<p>The key to copy.</p>"}, "target": {"shape": "Target", "documentation": "<p>The key of the field to copy the value to.</p>"}, "overwriteIfExists": {"shape": "OverwriteIfExists", "documentation": "<p>Specifies whether to overwrite the value if the destination key already exists. If you omit this, the default is <code>false</code>.</p>"}}, "documentation": "<p>This object defines one value to be copied with the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-copoyValue\"> copyValue</a> processor.</p>"}, "Count": {"type": "long"}, "CreateDeliveryRequest": {"type": "structure", "required": ["deliverySourceName", "deliveryDestinationArn"], "members": {"deliverySourceName": {"shape": "DeliverySourceName", "documentation": "<p>The name of the delivery source to use for this delivery.</p>"}, "deliveryDestinationArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the delivery destination to use for this delivery.</p>"}, "recordFields": {"shape": "RecordFields", "documentation": "<p>The list of record fields to be delivered to the destination, in order. If the delivery's log source has mandatory fields, they must be included in this list.</p>"}, "fieldDelimiter": {"shape": "FieldDelimiter", "documentation": "<p>The field delimiter to use between record fields when the final output format of a delivery is in <code>Plain</code>, <code>W3C</code>, or <code>Raw</code> format.</p>"}, "s3DeliveryConfiguration": {"shape": "S3DeliveryConfiguration", "documentation": "<p>This structure contains parameters that are valid only when the delivery's delivery destination is an S3 bucket.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>An optional list of key-value pairs to associate with the resource.</p> <p>For more information about tagging, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws_tagging.html\">Tagging Amazon Web Services resources</a> </p>"}}}, "CreateDeliveryResponse": {"type": "structure", "members": {"delivery": {"shape": "Delivery", "documentation": "<p>A structure that contains information about the delivery that you just created.</p>"}}}, "CreateExportTaskRequest": {"type": "structure", "required": ["logGroupName", "from", "to", "destination"], "members": {"taskName": {"shape": "ExportTaskName", "documentation": "<p>The name of the export task.</p>"}, "logGroupName": {"shape": "LogGroupName", "documentation": "<p>The name of the log group.</p>"}, "logStreamNamePrefix": {"shape": "LogStreamName", "documentation": "<p>Export only log streams that match the provided prefix. If you don't specify a value, no prefix filter is applied.</p>"}, "from": {"shape": "Timestamp", "documentation": "<p>The start time of the range for the request, expressed as the number of milliseconds after <code>Jan 1, 1970 00:00:00 UTC</code>. Events with a timestamp earlier than this time are not exported.</p>"}, "to": {"shape": "Timestamp", "documentation": "<p>The end time of the range for the request, expressed as the number of milliseconds after <code>Jan 1, 1970 00:00:00 UTC</code>. Events with a timestamp later than this time are not exported.</p> <p>You must specify a time that is not earlier than when this log group was created.</p>"}, "destination": {"shape": "ExportDestinationBucket", "documentation": "<p>The name of S3 bucket for the exported log data. The bucket must be in the same Amazon Web Services Region.</p>"}, "destinationPrefix": {"shape": "ExportDestinationPrefix", "documentation": "<p>The prefix used as the start of the key for every object exported. If you don't specify a value, the default is <code>exportedlogs</code>.</p> <p>The length of this parameter must comply with the S3 object key name length limits. The object key name is a sequence of Unicode characters with UTF-8 encoding, and can be up to 1,024 bytes.</p>"}}}, "CreateExportTaskResponse": {"type": "structure", "members": {"taskId": {"shape": "ExportTaskId", "documentation": "<p>The ID of the export task.</p>"}}}, "CreateLogAnomalyDetectorRequest": {"type": "structure", "required": ["logGroupArnList"], "members": {"logGroupArnList": {"shape": "LogGroupArnList", "documentation": "<p>An array containing the ARN of the log group that this anomaly detector will watch. You can specify only one log group ARN.</p>"}, "detectorName": {"shape": "DetectorName", "documentation": "<p>A name for this anomaly detector.</p>"}, "evaluationFrequency": {"shape": "EvaluationFrequency", "documentation": "<p>Specifies how often the anomaly detector is to run and look for anomalies. Set this value according to the frequency that the log group receives new logs. For example, if the log group receives new log events every 10 minutes, then 15 minutes might be a good setting for <code>evaluationFrequency</code> .</p>"}, "filterPattern": {"shape": "FilterPattern", "documentation": "<p>You can use this parameter to limit the anomaly detection model to examine only log events that match the pattern you specify here. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/FilterAndPatternSyntax.html\">Filter and Pattern Syntax</a>.</p>"}, "kmsKeyId": {"shape": "DetectorKmsKeyArn", "documentation": "<p>Optionally assigns a KMS key to secure this anomaly detector and its findings. If a key is assigned, the anomalies found and the model used by this detector are encrypted at rest with the key. If a key is assigned to an anomaly detector, a user must have permissions for both this key and for the anomaly detector to retrieve information about the anomalies that it finds.</p> <p> Make sure the value provided is a valid KMS key ARN. For more information about using a KMS key and to see the required IAM policy, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/LogsAnomalyDetection-KMS.html\">Use a KMS key with an anomaly detector</a>.</p>"}, "anomalyVisibilityTime": {"shape": "AnomalyVisibilityTime", "documentation": "<p>The number of days to have visibility on an anomaly. After this time period has elapsed for an anomaly, it will be automatically baselined and the anomaly detector will treat new occurrences of a similar anomaly as normal. Therefore, if you do not correct the cause of an anomaly during the time period specified in <code>anomalyVisibilityTime</code>, it will be considered normal going forward and will not be detected as an anomaly.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>An optional list of key-value pairs to associate with the resource.</p> <p>For more information about tagging, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws_tagging.html\">Tagging Amazon Web Services resources</a> </p>"}}}, "CreateLogAnomalyDetectorResponse": {"type": "structure", "members": {"anomalyDetectorArn": {"shape": "AnomalyDetectorArn", "documentation": "<p>The ARN of the log anomaly detector that you just created.</p>"}}}, "CreateLogGroupRequest": {"type": "structure", "required": ["logGroupName"], "members": {"logGroupName": {"shape": "LogGroupName", "documentation": "<p>A name for the log group.</p>"}, "kmsKeyId": {"shape": "KmsKeyId", "documentation": "<p>The Amazon Resource Name (ARN) of the KMS key to use when encrypting log data. For more information, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html#arn-syntax-kms\">Amazon Resource Names</a>.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>The key-value pairs to use for the tags.</p> <p>You can grant users access to certain log groups while preventing them from accessing other log groups. To do so, tag your groups and use IAM policies that refer to those tags. To assign tags when you create a log group, you must have either the <code>logs:TagResource</code> or <code>logs:TagLogGroup</code> permission. For more information about tagging, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws_tagging.html\">Tagging Amazon Web Services resources</a>. For more information about using tags to control access, see <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/access_tags.html\">Controlling access to Amazon Web Services resources using tags</a>.</p>"}, "logGroupClass": {"shape": "LogGroupClass", "documentation": "<p>Use this parameter to specify the log group class for this log group. There are three classes:</p> <ul> <li> <p>The <code>Standard</code> log class supports all CloudWatch Logs features.</p> </li> <li> <p>The <code>Infrequent Access</code> log class supports a subset of CloudWatch Logs features and incurs lower costs.</p> </li> <li> <p>Use the <code>Delivery</code> log class only for delivering Lambda logs to store in Amazon S3 or Amazon Data Firehose. Log events in log groups in the Delivery class are kept in CloudWatch Logs for only one day. This log class doesn't offer rich CloudWatch Logs capabilities such as CloudWatch Logs Insights queries.</p> </li> </ul> <p>If you omit this parameter, the default of <code>STANDARD</code> is used.</p> <important> <p>The value of <code>logGroupClass</code> can't be changed after a log group is created.</p> </important> <p>For details about the features supported by each class, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch_Logs_Log_Classes.html\">Log classes</a> </p>"}}}, "CreateLogStreamRequest": {"type": "structure", "required": ["logGroupName", "logStreamName"], "members": {"logGroupName": {"shape": "LogGroupName", "documentation": "<p>The name of the log group.</p>"}, "logStreamName": {"shape": "LogStreamName", "documentation": "<p>The name of the log stream.</p>"}}}, "DashboardViewerPrincipals": {"type": "list", "member": {"shape": "<PERSON><PERSON>"}}, "DataAlreadyAcceptedException": {"type": "structure", "members": {"expectedSequenceToken": {"shape": "SequenceToken"}}, "documentation": "<p>The event was already logged.</p> <important> <p> <code>PutLogEvents</code> actions are now always accepted and never return <code>DataAlreadyAcceptedException</code> regardless of whether a given batch of log events has already been accepted. </p> </important>", "exception": true}, "DataProtectionPolicyDocument": {"type": "string"}, "DataProtectionStatus": {"type": "string", "enum": ["ACTIVATED", "DELETED", "ARCHIVED", "DISABLED"]}, "DateTimeConverter": {"type": "structure", "required": ["source", "target", "matchPatterns"], "members": {"source": {"shape": "Source", "documentation": "<p>The key to apply the date conversion to.</p>"}, "target": {"shape": "Target", "documentation": "<p>The JSON field to store the result in.</p>"}, "targetFormat": {"shape": "TargetFormat", "documentation": "<p>The datetime format to use for the converted data in the target field.</p> <p>If you omit this, the default of <code> yyyy-MM-dd'T'HH:mm:ss.SSS'Z</code> is used.</p>"}, "matchPatterns": {"shape": "MatchPatterns", "documentation": "<p>A list of patterns to match against the <code>source</code> field.</p>"}, "sourceTimezone": {"shape": "SourceTimezone", "documentation": "<p>The time zone of the source field. If you omit this, the default used is the UTC zone.</p>"}, "targetTimezone": {"shape": "TargetTimezone", "documentation": "<p>The time zone of the target field. If you omit this, the default used is the UTC zone.</p>"}, "locale": {"shape": "Locale", "documentation": "<p>The locale of the source field. If you omit this, the default of <code>locale.ROOT</code> is used.</p>"}}, "documentation": "<p>This processor converts a datetime string into a format that you specify. </p> <p>For more information about this processor including examples, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-datetimeConverter\"> datetimeConverter</a> in the <i>CloudWatch Logs User Guide</i>.</p>"}, "Days": {"type": "integer", "documentation": "<p>The number of days to retain the log events in the specified log group. Possible values are: 1, 3, 5, 7, 14, 30, 60, 90, 120, 150, 180, 365, 400, 545, 731, 1096, 1827, 2192, 2557, 2922, 3288, and 3653.</p> <p>To set a log group so that its log events do not expire, use <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_DeleteRetentionPolicy.html\">DeleteRetentionPolicy</a>. </p>"}, "DefaultValue": {"type": "double"}, "DeleteAccountPolicyRequest": {"type": "structure", "required": ["policyName", "policyType"], "members": {"policyName": {"shape": "PolicyName", "documentation": "<p>The name of the policy to delete.</p>"}, "policyType": {"shape": "PolicyType", "documentation": "<p>The type of policy to delete.</p>"}}}, "DeleteDataProtectionPolicyRequest": {"type": "structure", "required": ["logGroupIdentifier"], "members": {"logGroupIdentifier": {"shape": "LogGroupIdentifier", "documentation": "<p>The name or ARN of the log group that you want to delete the data protection policy for.</p>"}}}, "DeleteDeliveryDestinationPolicyRequest": {"type": "structure", "required": ["deliveryDestinationName"], "members": {"deliveryDestinationName": {"shape": "DeliveryDestinationName", "documentation": "<p>The name of the delivery destination that you want to delete the policy for.</p>"}}}, "DeleteDeliveryDestinationRequest": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "DeliveryDestinationName", "documentation": "<p>The name of the delivery destination that you want to delete. You can find a list of delivery destionation names by using the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_DescribeDeliveryDestinations.html\">DescribeDeliveryDestinations</a> operation.</p>"}}}, "DeleteDeliveryRequest": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "DeliveryId", "documentation": "<p>The unique ID of the delivery to delete. You can find the ID of a delivery with the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_DescribeDeliveries.html\">DescribeDeliveries</a> operation.</p>"}}}, "DeleteDeliverySourceRequest": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "DeliverySourceName", "documentation": "<p>The name of the delivery source that you want to delete.</p>"}}}, "DeleteDestinationRequest": {"type": "structure", "required": ["destinationName"], "members": {"destinationName": {"shape": "DestinationName", "documentation": "<p>The name of the destination.</p>"}}}, "DeleteIndexPolicyRequest": {"type": "structure", "required": ["logGroupIdentifier"], "members": {"logGroupIdentifier": {"shape": "LogGroupIdentifier", "documentation": "<p>The log group to delete the index policy for. You can specify either the name or the ARN of the log group.</p>"}}}, "DeleteIndexPolicyResponse": {"type": "structure", "members": {}}, "DeleteIntegrationRequest": {"type": "structure", "required": ["integrationName"], "members": {"integrationName": {"shape": "IntegrationName", "documentation": "<p>The name of the integration to delete. To find the name of your integration, use <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_ListIntegrations.html\">ListIntegrations</a>.</p>"}, "force": {"shape": "Force", "documentation": "<p>Specify <code>true</code> to force the deletion of the integration even if vended logs dashboards currently exist.</p> <p>The default is <code>false</code>.</p>"}}}, "DeleteIntegrationResponse": {"type": "structure", "members": {}}, "DeleteKeys": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON>"], "members": {"withKeys": {"shape": "DeleteWithKeys", "documentation": "<p>The list of keys to delete.</p>"}}, "documentation": "<p>This processor deletes entries from a log event. These entries are key-value pairs. </p> <p>For more information about this processor including examples, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-deleteKeys\"> deleteKeys</a> in the <i>CloudWatch Logs User Guide</i>.</p>"}, "DeleteLogAnomalyDetectorRequest": {"type": "structure", "required": ["anomalyDetectorArn"], "members": {"anomalyDetectorArn": {"shape": "AnomalyDetectorArn", "documentation": "<p>The ARN of the anomaly detector to delete. You can find the ARNs of log anomaly detectors in your account by using the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_ListLogAnomalyDetectors.html\">ListLogAnomalyDetectors</a> operation.</p>"}}}, "DeleteLogGroupRequest": {"type": "structure", "required": ["logGroupName"], "members": {"logGroupName": {"shape": "LogGroupName", "documentation": "<p>The name of the log group.</p>"}}}, "DeleteLogStreamRequest": {"type": "structure", "required": ["logGroupName", "logStreamName"], "members": {"logGroupName": {"shape": "LogGroupName", "documentation": "<p>The name of the log group.</p>"}, "logStreamName": {"shape": "LogStreamName", "documentation": "<p>The name of the log stream.</p>"}}}, "DeleteMetricFilterRequest": {"type": "structure", "required": ["logGroupName", "filterName"], "members": {"logGroupName": {"shape": "LogGroupName", "documentation": "<p>The name of the log group.</p>"}, "filterName": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The name of the metric filter.</p>"}}}, "DeleteQueryDefinitionRequest": {"type": "structure", "required": ["queryDefinitionId"], "members": {"queryDefinitionId": {"shape": "QueryId", "documentation": "<p>The ID of the query definition that you want to delete. You can use <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_DescribeQueryDefinitions.html\">DescribeQueryDefinitions</a> to retrieve the IDs of your saved query definitions.</p>"}}}, "DeleteQueryDefinitionResponse": {"type": "structure", "members": {"success": {"shape": "Success", "documentation": "<p>A value of TRUE indicates that the operation succeeded. FALSE indicates that the operation failed.</p>"}}}, "DeleteResourcePolicyRequest": {"type": "structure", "members": {"policyName": {"shape": "PolicyName", "documentation": "<p>The name of the policy to be revoked. This parameter is required.</p>"}}}, "DeleteRetentionPolicyRequest": {"type": "structure", "required": ["logGroupName"], "members": {"logGroupName": {"shape": "LogGroupName", "documentation": "<p>The name of the log group.</p>"}}}, "DeleteSubscriptionFilterRequest": {"type": "structure", "required": ["logGroupName", "filterName"], "members": {"logGroupName": {"shape": "LogGroupName", "documentation": "<p>The name of the log group.</p>"}, "filterName": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The name of the subscription filter.</p>"}}}, "DeleteTransformerRequest": {"type": "structure", "required": ["logGroupIdentifier"], "members": {"logGroupIdentifier": {"shape": "LogGroupIdentifier", "documentation": "<p>Specify either the name or ARN of the log group to delete the transformer for. If the log group is in a source account and you are using a monitoring account, you must use the log group ARN.</p>"}}}, "DeleteWithKeys": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON>"}, "max": 5, "min": 1}, "Delimiter": {"type": "string", "max": 2, "min": 1}, "Deliveries": {"type": "list", "member": {"shape": "Delivery"}}, "Delivery": {"type": "structure", "members": {"id": {"shape": "DeliveryId", "documentation": "<p>The unique ID that identifies this delivery in your account.</p>"}, "arn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) that uniquely identifies this delivery.</p>"}, "deliverySourceName": {"shape": "DeliverySourceName", "documentation": "<p>The name of the delivery source that is associated with this delivery.</p>"}, "deliveryDestinationArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the delivery destination that is associated with this delivery.</p>"}, "deliveryDestinationType": {"shape": "DeliveryDestinationType", "documentation": "<p>Displays whether the delivery destination associated with this delivery is CloudWatch Logs, Amazon S3, or Firehose.</p>"}, "recordFields": {"shape": "RecordFields", "documentation": "<p>The record fields used in this delivery.</p>"}, "fieldDelimiter": {"shape": "FieldDelimiter", "documentation": "<p>The field delimiter that is used between record fields when the final output format of a delivery is in <code>Plain</code>, <code>W3C</code>, or <code>Raw</code> format.</p>"}, "s3DeliveryConfiguration": {"shape": "S3DeliveryConfiguration", "documentation": "<p>This structure contains delivery configurations that apply only when the delivery destination resource is an S3 bucket.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>The tags that have been assigned to this delivery.</p>"}}, "documentation": "<p>This structure contains information about one <i>delivery</i> in your account. </p> <p>A delivery is a connection between a logical <i>delivery source</i> and a logical <i>delivery destination</i>.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_CreateDelivery.html\">CreateDelivery</a>.</p> <p>To update an existing delivery configuration, use <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_UpdateDeliveryConfiguration.html\">UpdateDeliveryConfiguration</a>.</p>"}, "DeliveryDestination": {"type": "structure", "members": {"name": {"shape": "DeliveryDestinationName", "documentation": "<p>The name of this delivery destination.</p>"}, "arn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) that uniquely identifies this delivery destination.</p>"}, "deliveryDestinationType": {"shape": "DeliveryDestinationType", "documentation": "<p>Displays whether this delivery destination is CloudWatch Logs, Amazon S3, or Firehose.</p>"}, "outputFormat": {"shape": "OutputFormat", "documentation": "<p>The format of the logs that are sent to this delivery destination. </p>"}, "deliveryDestinationConfiguration": {"shape": "DeliveryDestinationConfiguration", "documentation": "<p>A structure that contains the ARN of the Amazon Web Services resource that will receive the logs.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>The tags that have been assigned to this delivery destination.</p>"}}, "documentation": "<p>This structure contains information about one <i>delivery destination</i> in your account. A delivery destination is an Amazon Web Services resource that represents an Amazon Web Services service that logs can be sent to. CloudWatch Logs, Amazon S3, are supported as Firehose delivery destinations.</p> <p>To configure logs delivery between a supported Amazon Web Services service and a destination, you must do the following:</p> <ul> <li> <p>Create a delivery source, which is a logical object that represents the resource that is actually sending the logs. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutDeliverySource.html\">PutDeliverySource</a>.</p> </li> <li> <p>Create a <i>delivery destination</i>, which is a logical object that represents the actual delivery destination. </p> </li> <li> <p>If you are delivering logs cross-account, you must use <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutDeliveryDestinationPolicy.html\">PutDeliveryDestinationPolicy</a> in the destination account to assign an IAM policy to the destination. This policy allows delivery to that destination. </p> </li> <li> <p>Create a <i>delivery</i> by pairing exactly one delivery source and one delivery destination. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_CreateDelivery.html\">CreateDelivery</a>.</p> </li> </ul> <p>You can configure a single delivery source to send logs to multiple destinations by creating multiple deliveries. You can also create multiple deliveries to configure multiple delivery sources to send logs to the same delivery destination.</p>"}, "DeliveryDestinationConfiguration": {"type": "structure", "required": ["destinationResourceArn"], "members": {"destinationResourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the Amazon Web Services destination that this delivery destination represents. That Amazon Web Services destination can be a log group in CloudWatch Logs, an Amazon S3 bucket, or a delivery stream in Firehose.</p>"}}, "documentation": "<p>A structure that contains information about one logs delivery destination.</p>"}, "DeliveryDestinationName": {"type": "string", "max": 60, "min": 1, "pattern": "[\\w-]*"}, "DeliveryDestinationPolicy": {"type": "string", "max": 51200, "min": 1}, "DeliveryDestinationType": {"type": "string", "enum": ["S3", "CWL", "FH"]}, "DeliveryDestinationTypes": {"type": "list", "member": {"shape": "DeliveryDestinationType"}, "max": 3, "min": 1}, "DeliveryDestinations": {"type": "list", "member": {"shape": "DeliveryDestination"}}, "DeliveryId": {"type": "string", "max": 64, "min": 1, "pattern": "^[0-9A-Za-z]+$"}, "DeliverySource": {"type": "structure", "members": {"name": {"shape": "DeliverySourceName", "documentation": "<p>The unique name of the delivery source.</p>"}, "arn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) that uniquely identifies this delivery source.</p>"}, "resourceArns": {"shape": "ResourceArns", "documentation": "<p>This array contains the ARN of the Amazon Web Services resource that sends logs and is represented by this delivery source. Currently, only one ARN can be in the array.</p>"}, "service": {"shape": "Service", "documentation": "<p>The Amazon Web Services service that is sending logs.</p>"}, "logType": {"shape": "LogType", "documentation": "<p>The type of log that the source is sending. For valid values for this parameter, see the documentation for the source service.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>The tags that have been assigned to this delivery source.</p>"}}, "documentation": "<p>This structure contains information about one <i>delivery source</i> in your account. A delivery source is an Amazon Web Services resource that sends logs to an Amazon Web Services destination. The destination can be CloudWatch Logs, Amazon S3, or Firehose.</p> <p>Only some Amazon Web Services services support being configured as a delivery source. These services are listed as <b>Supported [V2 Permissions]</b> in the table at <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/AWS-logs-and-resource-policy.html\">Enabling logging from Amazon Web Services services.</a> </p> <p>To configure logs delivery between a supported Amazon Web Services service and a destination, you must do the following:</p> <ul> <li> <p>Create a delivery source, which is a logical object that represents the resource that is actually sending the logs. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutDeliverySource.html\">PutDeliverySource</a>.</p> </li> <li> <p>Create a <i>delivery destination</i>, which is a logical object that represents the actual delivery destination. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutDeliveryDestination.html\">PutDeliveryDestination</a>.</p> </li> <li> <p>If you are delivering logs cross-account, you must use <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutDeliveryDestinationPolicy.html\">PutDeliveryDestinationPolicy</a> in the destination account to assign an IAM policy to the destination. This policy allows delivery to that destination. </p> </li> <li> <p>Create a <i>delivery</i> by pairing exactly one delivery source and one delivery destination. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_CreateDelivery.html\">CreateDelivery</a>.</p> </li> </ul> <p>You can configure a single delivery source to send logs to multiple destinations by creating multiple deliveries. You can also create multiple deliveries to configure multiple delivery sources to send logs to the same delivery destination.</p>"}, "DeliverySourceName": {"type": "string", "max": 60, "min": 1, "pattern": "[\\w-]*"}, "DeliverySources": {"type": "list", "member": {"shape": "DeliverySource"}}, "DeliverySuffixPath": {"type": "string", "max": 256, "min": 1}, "Descending": {"type": "boolean"}, "DescribeAccountPoliciesRequest": {"type": "structure", "required": ["policyType"], "members": {"policyType": {"shape": "PolicyType", "documentation": "<p>Use this parameter to limit the returned policies to only the policies that match the policy type that you specify.</p>"}, "policyName": {"shape": "PolicyName", "documentation": "<p>Use this parameter to limit the returned policies to only the policy with the name that you specify.</p>"}, "accountIdentifiers": {"shape": "AccountIds", "documentation": "<p>If you are using an account that is set up as a monitoring account for CloudWatch unified cross-account observability, you can use this to specify the account ID of a source account. If you do, the operation returns the account policy for the specified account. Currently, you can specify only one account ID in this parameter.</p> <p>If you omit this parameter, only the policy in the current account is returned.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of items to return. (You received this token from a previous call.)</p>"}}}, "DescribeAccountPoliciesResponse": {"type": "structure", "members": {"accountPolicies": {"shape": "AccountPolicies", "documentation": "<p>An array of structures that contain information about the CloudWatch Logs account policies that match the specified filters.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token to use when requesting the next set of items. The token expires after 24 hours.</p>"}}}, "DescribeConfigurationTemplatesRequest": {"type": "structure", "members": {"service": {"shape": "Service", "documentation": "<p>Use this parameter to filter the response to include only the configuration templates that apply to the Amazon Web Services service that you specify here.</p>"}, "logTypes": {"shape": "LogTypes", "documentation": "<p>Use this parameter to filter the response to include only the configuration templates that apply to the log types that you specify here.</p>"}, "resourceTypes": {"shape": "ResourceTypes", "documentation": "<p>Use this parameter to filter the response to include only the configuration templates that apply to the resource types that you specify here.</p>"}, "deliveryDestinationTypes": {"shape": "DeliveryDestinationTypes", "documentation": "<p>Use this parameter to filter the response to include only the configuration templates that apply to the delivery destination types that you specify here.</p>"}, "nextToken": {"shape": "NextToken"}, "limit": {"shape": "DescribeLimit", "documentation": "<p>Use this parameter to limit the number of configuration templates that are returned in the response.</p>"}}}, "DescribeConfigurationTemplatesResponse": {"type": "structure", "members": {"configurationTemplates": {"shape": "ConfigurationTemplates", "documentation": "<p>An array of objects, where each object describes one configuration template that matches the filters that you specified in the request.</p>"}, "nextToken": {"shape": "NextToken"}}}, "DescribeDeliveriesRequest": {"type": "structure", "members": {"nextToken": {"shape": "NextToken"}, "limit": {"shape": "DescribeLimit", "documentation": "<p>Optionally specify the maximum number of deliveries to return in the response.</p>"}}}, "DescribeDeliveriesResponse": {"type": "structure", "members": {"deliveries": {"shape": "Deliveries", "documentation": "<p>An array of structures. Each structure contains information about one delivery in the account.</p>"}, "nextToken": {"shape": "NextToken"}}}, "DescribeDeliveryDestinationsRequest": {"type": "structure", "members": {"nextToken": {"shape": "NextToken"}, "limit": {"shape": "DescribeLimit", "documentation": "<p>Optionally specify the maximum number of delivery destinations to return in the response.</p>"}}}, "DescribeDeliveryDestinationsResponse": {"type": "structure", "members": {"deliveryDestinations": {"shape": "DeliveryDestinations", "documentation": "<p>An array of structures. Each structure contains information about one delivery destination in the account.</p>"}, "nextToken": {"shape": "NextToken"}}}, "DescribeDeliverySourcesRequest": {"type": "structure", "members": {"nextToken": {"shape": "NextToken"}, "limit": {"shape": "DescribeLimit", "documentation": "<p>Optionally specify the maximum number of delivery sources to return in the response.</p>"}}}, "DescribeDeliverySourcesResponse": {"type": "structure", "members": {"deliverySources": {"shape": "DeliverySources", "documentation": "<p>An array of structures. Each structure contains information about one delivery source in the account.</p>"}, "nextToken": {"shape": "NextToken"}}}, "DescribeDestinationsRequest": {"type": "structure", "members": {"DestinationNamePrefix": {"shape": "DestinationName", "documentation": "<p>The prefix to match. If you don't specify a value, no prefix filter is applied.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of items to return. (You received this token from a previous call.)</p>"}, "limit": {"shape": "DescribeLimit", "documentation": "<p>The maximum number of items returned. If you don't specify a value, the default maximum value of 50 items is used.</p>"}}}, "DescribeDestinationsResponse": {"type": "structure", "members": {"destinations": {"shape": "Destinations", "documentation": "<p>The destinations.</p>"}, "nextToken": {"shape": "NextToken"}}}, "DescribeExportTasksRequest": {"type": "structure", "members": {"taskId": {"shape": "ExportTaskId", "documentation": "<p>The ID of the export task. Specifying a task ID filters the results to one or zero export tasks.</p>"}, "statusCode": {"shape": "ExportTaskStatusCode", "documentation": "<p>The status code of the export task. Specifying a status code filters the results to zero or more export tasks.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of items to return. (You received this token from a previous call.)</p>"}, "limit": {"shape": "DescribeLimit", "documentation": "<p>The maximum number of items returned. If you don't specify a value, the default is up to 50 items.</p>"}}}, "DescribeExportTasksResponse": {"type": "structure", "members": {"exportTasks": {"shape": "ExportTasks", "documentation": "<p>The export tasks.</p>"}, "nextToken": {"shape": "NextToken"}}}, "DescribeFieldIndexesLogGroupIdentifiers": {"type": "list", "member": {"shape": "LogGroupIdentifier"}, "max": 100, "min": 1}, "DescribeFieldIndexesRequest": {"type": "structure", "required": ["logGroupIdentifiers"], "members": {"logGroupIdentifiers": {"shape": "DescribeFieldIndexesLogGroupIdentifiers", "documentation": "<p>An array containing the names or ARNs of the log groups that you want to retrieve field indexes for.</p>"}, "nextToken": {"shape": "NextToken"}}}, "DescribeFieldIndexesResponse": {"type": "structure", "members": {"fieldIndexes": {"shape": "FieldIndexes", "documentation": "<p>An array containing the field index information.</p>"}, "nextToken": {"shape": "NextToken"}}}, "DescribeIndexPoliciesLogGroupIdentifiers": {"type": "list", "member": {"shape": "LogGroupIdentifier"}, "max": 1, "min": 1}, "DescribeIndexPoliciesRequest": {"type": "structure", "required": ["logGroupIdentifiers"], "members": {"logGroupIdentifiers": {"shape": "DescribeIndexPoliciesLogGroupIdentifiers", "documentation": "<p>An array containing the name or ARN of the log group that you want to retrieve field index policies for.</p>"}, "nextToken": {"shape": "NextToken"}}}, "DescribeIndexPoliciesResponse": {"type": "structure", "members": {"indexPolicies": {"shape": "IndexPolicies", "documentation": "<p>An array containing the field index policies.</p>"}, "nextToken": {"shape": "NextToken"}}}, "DescribeLimit": {"type": "integer", "max": 50, "min": 1}, "DescribeLogGroupsLogGroupIdentifiers": {"type": "list", "member": {"shape": "LogGroupIdentifier"}, "max": 50, "min": 1}, "DescribeLogGroupsRequest": {"type": "structure", "members": {"accountIdentifiers": {"shape": "AccountIds", "documentation": "<p>When <code>includeLinkedAccounts</code> is set to <code>true</code>, use this parameter to specify the list of accounts to search. You can specify as many as 20 account IDs in the array. </p>"}, "logGroupNamePrefix": {"shape": "LogGroupName", "documentation": "<p>The prefix to match.</p> <note> <p> <code>logGroupNamePrefix</code> and <code>logGroupNamePattern</code> are mutually exclusive. Only one of these parameters can be passed. </p> </note>"}, "logGroupNamePattern": {"shape": "LogGroupNamePattern", "documentation": "<p>If you specify a string for this parameter, the operation returns only log groups that have names that match the string based on a case-sensitive substring search. For example, if you specify <code>Foo</code>, log groups named <code>FooBar</code>, <code>aws/Foo</code>, and <code>GroupFoo</code> would match, but <code>foo</code>, <code>F/o/o</code> and <code>Froo</code> would not match.</p> <p>If you specify <code>logGroupNamePattern</code> in your request, then only <code>arn</code>, <code>creationTime</code>, and <code>logGroupName</code> are included in the response. </p> <note> <p> <code>logGroupNamePattern</code> and <code>logGroupNamePrefix</code> are mutually exclusive. Only one of these parameters can be passed. </p> </note>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of items to return. (You received this token from a previous call.)</p>"}, "limit": {"shape": "DescribeLimit", "documentation": "<p>The maximum number of items returned. If you don't specify a value, the default is up to 50 items.</p>"}, "includeLinkedAccounts": {"shape": "IncludeLinkedAccounts", "documentation": "<p>If you are using a monitoring account, set this to <code>true</code> to have the operation return log groups in the accounts listed in <code>accountIdentifiers</code>.</p> <p>If this parameter is set to <code>true</code> and <code>accountIdentifiers</code> contains a null value, the operation returns all log groups in the monitoring account and all log groups in all source accounts that are linked to the monitoring account. </p> <p>The default for this parameter is <code>false</code>.</p>"}, "logGroupClass": {"shape": "LogGroupClass", "documentation": "<p>Use this parameter to limit the results to only those log groups in the specified log group class. If you omit this parameter, log groups of all classes can be returned.</p> <p>Specifies the log group class for this log group. There are three classes:</p> <ul> <li> <p>The <code>Standard</code> log class supports all CloudWatch Logs features.</p> </li> <li> <p>The <code>Infrequent Access</code> log class supports a subset of CloudWatch Logs features and incurs lower costs.</p> </li> <li> <p>Use the <code>Delivery</code> log class only for delivering Lambda logs to store in Amazon S3 or Amazon Data Firehose. Log events in log groups in the Delivery class are kept in CloudWatch Logs for only one day. This log class doesn't offer rich CloudWatch Logs capabilities such as CloudWatch Logs Insights queries.</p> </li> </ul> <p>For details about the features supported by each class, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch_Logs_Log_Classes.html\">Log classes</a> </p>"}, "logGroupIdentifiers": {"shape": "DescribeLogGroupsLogGroupIdentifiers", "documentation": "<p>Use this array to filter the list of log groups returned. If you specify this parameter, the only other filter that you can choose to specify is <code>includeLinkedAccounts</code>.</p> <p>If you are using this operation in a monitoring account, you can specify the ARNs of log groups in source accounts and in the monitoring account itself. If you are using this operation in an account that is not a cross-account monitoring account, you can specify only log group names in the same account as the operation.</p>"}}}, "DescribeLogGroupsResponse": {"type": "structure", "members": {"logGroups": {"shape": "LogGroups", "documentation": "<p>An array of structures, where each structure contains the information about one log group.</p>"}, "nextToken": {"shape": "NextToken"}}}, "DescribeLogStreamsRequest": {"type": "structure", "members": {"logGroupName": {"shape": "LogGroupName", "documentation": "<p>The name of the log group.</p> <note> <p> You must include either <code>logGroupIdentifier</code> or <code>logGroupName</code>, but not both. </p> </note>"}, "logGroupIdentifier": {"shape": "LogGroupIdentifier", "documentation": "<p>Specify either the name or ARN of the log group to view. If the log group is in a source account and you are using a monitoring account, you must use the log group ARN.</p> <note> <p> You must include either <code>logGroupIdentifier</code> or <code>logGroupName</code>, but not both. </p> </note>"}, "logStreamNamePrefix": {"shape": "LogStreamName", "documentation": "<p>The prefix to match.</p> <p>If <code>orderBy</code> is <code>LastEventTime</code>, you cannot specify this parameter.</p>"}, "orderBy": {"shape": "OrderBy", "documentation": "<p>If the value is <code>LogStreamName</code>, the results are ordered by log stream name. If the value is <code>LastEventTime</code>, the results are ordered by the event time. The default value is <code>LogStreamName</code>.</p> <p>If you order the results by event time, you cannot specify the <code>logStreamNamePrefix</code> parameter.</p> <p> <code>lastEventTimestamp</code> represents the time of the most recent log event in the log stream in CloudWatch Logs. This number is expressed as the number of milliseconds after <code>Jan 1, 1970 00:00:00 UTC</code>. <code>lastEventTimestamp</code> updates on an eventual consistency basis. It typically updates in less than an hour from ingestion, but in rare situations might take longer.</p>"}, "descending": {"shape": "Descending", "documentation": "<p>If the value is true, results are returned in descending order. If the value is to false, results are returned in ascending order. The default value is false.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of items to return. (You received this token from a previous call.)</p>"}, "limit": {"shape": "DescribeLimit", "documentation": "<p>The maximum number of items returned. If you don't specify a value, the default is up to 50 items.</p>"}}}, "DescribeLogStreamsResponse": {"type": "structure", "members": {"logStreams": {"shape": "LogStreams", "documentation": "<p>The log streams.</p>"}, "nextToken": {"shape": "NextToken"}}}, "DescribeMetricFiltersRequest": {"type": "structure", "members": {"logGroupName": {"shape": "LogGroupName", "documentation": "<p>The name of the log group.</p>"}, "filterNamePrefix": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The prefix to match. CloudWatch Logs uses the value that you set here only if you also include the <code>logGroupName</code> parameter in your request.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of items to return. (You received this token from a previous call.)</p>"}, "limit": {"shape": "DescribeLimit", "documentation": "<p>The maximum number of items returned. If you don't specify a value, the default is up to 50 items.</p>"}, "metricName": {"shape": "MetricName", "documentation": "<p>Filters results to include only those with the specified metric name. If you include this parameter in your request, you must also include the <code>metricNamespace</code> parameter.</p>"}, "metricNamespace": {"shape": "MetricNamespace", "documentation": "<p>Filters results to include only those in the specified namespace. If you include this parameter in your request, you must also include the <code>metricName</code> parameter.</p>"}}}, "DescribeMetricFiltersResponse": {"type": "structure", "members": {"metricFilters": {"shape": "MetricFilters", "documentation": "<p>The metric filters.</p>"}, "nextToken": {"shape": "NextToken"}}}, "DescribeQueriesMaxResults": {"type": "integer", "max": 1000, "min": 1}, "DescribeQueriesRequest": {"type": "structure", "members": {"logGroupName": {"shape": "LogGroupName", "documentation": "<p>Limits the returned queries to only those for the specified log group.</p>"}, "status": {"shape": "QueryStatus", "documentation": "<p>Limits the returned queries to only those that have the specified status. Valid values are <code>Cancelled</code>, <code>Complete</code>, <code>Failed</code>, <code>Running</code>, and <code>Scheduled</code>.</p>"}, "maxResults": {"shape": "DescribeQueriesMaxResults", "documentation": "<p>Limits the number of returned queries to the specified number.</p>"}, "nextToken": {"shape": "NextToken"}, "queryLanguage": {"shape": "QueryLanguage", "documentation": "<p>Limits the returned queries to only the queries that use the specified query language.</p>"}}}, "DescribeQueriesResponse": {"type": "structure", "members": {"queries": {"shape": "QueryInfoList", "documentation": "<p>The list of queries that match the request.</p>"}, "nextToken": {"shape": "NextToken"}}}, "DescribeQueryDefinitionsRequest": {"type": "structure", "members": {"queryLanguage": {"shape": "QueryLanguage", "documentation": "<p>The query language used for this query. For more information about the query languages that CloudWatch Logs supports, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CWL_AnalyzeLogData_Languages.html\">Supported query languages</a>.</p>"}, "queryDefinitionNamePrefix": {"shape": "QueryDefinitionName", "documentation": "<p>Use this parameter to filter your results to only the query definitions that have names that start with the prefix you specify.</p>"}, "maxResults": {"shape": "QueryListMaxResults", "documentation": "<p>Limits the number of returned query definitions to the specified number.</p>"}, "nextToken": {"shape": "NextToken"}}}, "DescribeQueryDefinitionsResponse": {"type": "structure", "members": {"queryDefinitions": {"shape": "QueryDefinitionList", "documentation": "<p>The list of query definitions that match your request.</p>"}, "nextToken": {"shape": "NextToken"}}}, "DescribeResourcePoliciesRequest": {"type": "structure", "members": {"nextToken": {"shape": "NextToken"}, "limit": {"shape": "DescribeLimit", "documentation": "<p>The maximum number of resource policies to be displayed with one call of this API.</p>"}}}, "DescribeResourcePoliciesResponse": {"type": "structure", "members": {"resourcePolicies": {"shape": "ResourcePolicies", "documentation": "<p>The resource policies that exist in this account.</p>"}, "nextToken": {"shape": "NextToken"}}}, "DescribeSubscriptionFiltersRequest": {"type": "structure", "required": ["logGroupName"], "members": {"logGroupName": {"shape": "LogGroupName", "documentation": "<p>The name of the log group.</p>"}, "filterNamePrefix": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The prefix to match. If you don't specify a value, no prefix filter is applied.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of items to return. (You received this token from a previous call.)</p>"}, "limit": {"shape": "DescribeLimit", "documentation": "<p>The maximum number of items returned. If you don't specify a value, the default is up to 50 items.</p>"}}}, "DescribeSubscriptionFiltersResponse": {"type": "structure", "members": {"subscriptionFilters": {"shape": "SubscriptionFilters", "documentation": "<p>The subscription filters.</p>"}, "nextToken": {"shape": "NextToken"}}}, "Description": {"type": "string", "min": 1}, "Destination": {"type": "structure", "members": {"destinationName": {"shape": "DestinationName", "documentation": "<p>The name of the destination.</p>"}, "targetArn": {"shape": "TargetArn", "documentation": "<p>The Amazon Resource Name (ARN) of the physical target where the log events are delivered (for example, a Kinesis stream).</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>A role for impersonation, used when delivering log events to the target.</p>"}, "accessPolicy": {"shape": "AccessPolicy", "documentation": "<p>An IAM policy document that governs which Amazon Web Services accounts can create subscription filters against this destination.</p>"}, "arn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of this destination.</p>"}, "creationTime": {"shape": "Timestamp", "documentation": "<p>The creation time of the destination, expressed as the number of milliseconds after Jan 1, 1970 00:00:00 UTC.</p>"}}, "documentation": "<p>Represents a cross-account destination that receives subscription log events.</p>"}, "DestinationArn": {"type": "string", "min": 1}, "DestinationField": {"type": "string", "max": 128, "min": 1}, "DestinationName": {"type": "string", "max": 512, "min": 1, "pattern": "[^:*]*"}, "Destinations": {"type": "list", "member": {"shape": "Destination"}}, "DetectorKmsKeyArn": {"type": "string", "max": 256, "pattern": "^arn:aws[a-z\\-]*:kms:[-a-z0-9]*:[0-9]*:key/[-a-z0-9]*$"}, "DetectorName": {"type": "string", "min": 1}, "Dimensions": {"type": "map", "key": {"shape": "DimensionsKey"}, "value": {"shape": "DimensionsValue"}}, "DimensionsKey": {"type": "string", "max": 255}, "DimensionsValue": {"type": "string", "max": 255}, "DisassociateKmsKeyRequest": {"type": "structure", "members": {"logGroupName": {"shape": "LogGroupName", "documentation": "<p>The name of the log group.</p> <p>In your <code>DisassociateKmsKey</code> operation, you must specify either the <code>resourceIdentifier</code> parameter or the <code>logGroup</code> parameter, but you can't specify both.</p>"}, "resourceIdentifier": {"shape": "ResourceIdentifier", "documentation": "<p>Specifies the target for this operation. You must specify one of the following:</p> <ul> <li> <p>Specify the ARN of a log group to stop having CloudWatch Logs use the KMS key to encrypt log events that are ingested and stored by that log group. After you run this operation, CloudWatch Logs encrypts ingested log events with the default CloudWatch Logs method. The log group ARN must be in the following format. Replace <i>REGION</i> and <i>ACCOUNT_ID</i> with your Region and account ID.</p> <p> <code>arn:aws:logs:<i>REGION</i>:<i>ACCOUNT_ID</i>:log-group:<i>LOG_GROUP_NAME</i> </code> </p> </li> <li> <p>Specify the following ARN to stop using this key to encrypt the results of future <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_StartQuery.html\">StartQuery</a> operations in this account. Replace <i>REGION</i> and <i>ACCOUNT_ID</i> with your Region and account ID.</p> <p> <code>arn:aws:logs:<i>REGION</i>:<i>ACCOUNT_ID</i>:query-result:*</code> </p> </li> </ul> <p>In your <code>DisssociateKmsKey</code> operation, you must specify either the <code>resourceIdentifier</code> parameter or the <code>logGroup</code> parameter, but you can't specify both.</p>"}}}, "Distribution": {"type": "string", "documentation": "<p>The method used to distribute log data to the destination, which can be either random or grouped by log stream.</p>", "enum": ["Random", "ByLogStream"]}, "DynamicTokenPosition": {"type": "integer"}, "EncryptionKey": {"type": "string", "max": 256}, "Entity": {"type": "structure", "members": {"keyAttributes": {"shape": "EntityKeyAttributes", "documentation": "<p>The attributes of the entity which identify the specific entity, as a list of key-value pairs. Entities with the same <code>keyAttributes</code> are considered to be the same entity.</p> <p>There are five allowed attributes (key names): <code>Type</code>, <code>ResourceType</code>, <code>Identifier</code> <code>Name</code>, and <code>Environment</code>.</p> <p>For details about how to use the key attributes, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/adding-your-own-related-telemetry.html\">How to add related information to telemetry</a> in the <i>CloudWatch User Guide</i>.</p>"}, "attributes": {"shape": "EntityAttributes", "documentation": "<p>Additional attributes of the entity that are not used to specify the identity of the entity. A list of key-value pairs.</p> <p>For details about how to use the attributes, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/adding-your-own-related-telemetry.html\">How to add related information to telemetry</a> in the <i>CloudWatch User Guide</i>.</p>"}}, "documentation": "<p>The entity associated with the log events in a <code>PutLogEvents</code> call.</p>"}, "EntityAttributes": {"type": "map", "key": {"shape": "EntityAttributesKey"}, "value": {"shape": "EntityAttributesValue"}, "max": 10, "min": 0}, "EntityAttributesKey": {"type": "string", "max": 256, "min": 1}, "EntityAttributesValue": {"type": "string", "max": 512, "min": 1}, "EntityKeyAttributes": {"type": "map", "key": {"shape": "EntityKeyAttributesKey"}, "value": {"shape": "EntityKeyAttributesValue"}, "max": 4, "min": 2}, "EntityKeyAttributesKey": {"type": "string", "max": 32, "min": 1}, "EntityKeyAttributesValue": {"type": "string", "max": 512, "min": 1}, "EntityRejectionErrorType": {"type": "string", "enum": ["InvalidEntity", "InvalidTypeValue", "InvalidKeyAttributes", "InvalidAttributes", "EntitySizeTooLarge", "UnsupportedLogGroupType", "Missing<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "Enumerations": {"type": "map", "key": {"shape": "TokenString"}, "value": {"shape": "TokenValue"}}, "EpochMillis": {"type": "long", "min": 0}, "EvaluationFrequency": {"type": "string", "enum": ["ONE_MIN", "FIVE_MIN", "TEN_MIN", "FIFTEEN_MIN", "THIRTY_MIN", "ONE_HOUR"]}, "EventId": {"type": "string"}, "EventMessage": {"type": "string", "min": 1}, "EventNumber": {"type": "long"}, "EventSource": {"type": "string", "enum": ["CloudTrail", "Route53Resolver", "VPCFlow", "EKSAudit", "AWSWAF"]}, "EventsLimit": {"type": "integer", "max": 10000, "min": 1}, "ExportDestinationBucket": {"type": "string", "max": 512, "min": 1}, "ExportDestinationPrefix": {"type": "string"}, "ExportTask": {"type": "structure", "members": {"taskId": {"shape": "ExportTaskId", "documentation": "<p>The ID of the export task.</p>"}, "taskName": {"shape": "ExportTaskName", "documentation": "<p>The name of the export task.</p>"}, "logGroupName": {"shape": "LogGroupName", "documentation": "<p>The name of the log group from which logs data was exported.</p>"}, "from": {"shape": "Timestamp", "documentation": "<p>The start time, expressed as the number of milliseconds after <code>Jan 1, 1970 00:00:00 UTC</code>. Events with a timestamp before this time are not exported.</p>"}, "to": {"shape": "Timestamp", "documentation": "<p>The end time, expressed as the number of milliseconds after <code>Jan 1, 1970 00:00:00 UTC</code>. Events with a timestamp later than this time are not exported.</p>"}, "destination": {"shape": "ExportDestinationBucket", "documentation": "<p>The name of the S3 bucket to which the log data was exported.</p>"}, "destinationPrefix": {"shape": "ExportDestinationPrefix", "documentation": "<p>The prefix that was used as the start of Amazon S3 key for every object exported.</p>"}, "status": {"shape": "ExportTaskStatus", "documentation": "<p>The status of the export task.</p>"}, "executionInfo": {"shape": "ExportTaskExecutionInfo", "documentation": "<p>Execution information about the export task.</p>"}}, "documentation": "<p>Represents an export task.</p>"}, "ExportTaskExecutionInfo": {"type": "structure", "members": {"creationTime": {"shape": "Timestamp", "documentation": "<p>The creation time of the export task, expressed as the number of milliseconds after <code>Jan 1, 1970 00:00:00 UTC</code>.</p>"}, "completionTime": {"shape": "Timestamp", "documentation": "<p>The completion time of the export task, expressed as the number of milliseconds after <code>Jan 1, 1970 00:00:00 UTC</code>.</p>"}}, "documentation": "<p>Represents the status of an export task.</p>"}, "ExportTaskId": {"type": "string", "max": 512, "min": 1}, "ExportTaskName": {"type": "string", "max": 512, "min": 1}, "ExportTaskStatus": {"type": "structure", "members": {"code": {"shape": "ExportTaskStatusCode", "documentation": "<p>The status code of the export task.</p>"}, "message": {"shape": "ExportTaskStatusMessage", "documentation": "<p>The status message related to the status code.</p>"}}, "documentation": "<p>Represents the status of an export task.</p>"}, "ExportTaskStatusCode": {"type": "string", "enum": ["CANCELLED", "COMPLETED", "FAILED", "PENDING", "PENDING_CANCEL", "RUNNING"]}, "ExportTaskStatusMessage": {"type": "string"}, "ExportTasks": {"type": "list", "member": {"shape": "ExportTask"}}, "ExtractedValues": {"type": "map", "key": {"shape": "Token"}, "value": {"shape": "Value"}}, "Field": {"type": "string"}, "FieldDelimiter": {"type": "string", "max": 5, "min": 0}, "FieldHeader": {"type": "string", "max": 64, "min": 1}, "FieldIndex": {"type": "structure", "members": {"logGroupIdentifier": {"shape": "LogGroupIdentifier", "documentation": "<p>If this field index appears in an index policy that applies only to a single log group, the ARN of that log group is displayed here.</p>"}, "fieldIndexName": {"shape": "FieldIndexName", "documentation": "<p>The string that this field index matches.</p>"}, "lastScanTime": {"shape": "Timestamp", "documentation": "<p>The most recent time that CloudWatch Logs scanned ingested log events to search for this field index to improve the speed of future CloudWatch Logs Insights queries that search for this field index.</p>"}, "firstEventTime": {"shape": "Timestamp", "documentation": "<p>The time and date of the earliest log event that matches this field index, after the index policy that contains it was created. </p>"}, "lastEventTime": {"shape": "Timestamp", "documentation": "<p>The time and date of the most recent log event that matches this field index. </p>"}}, "documentation": "<p>This structure describes one log event field that is used as an index in at least one index policy in this account.</p>"}, "FieldIndexName": {"type": "string", "max": 512, "min": 1, "pattern": "[\\.\\-_/#A-Za-z0-9]+"}, "FieldIndexes": {"type": "list", "member": {"shape": "FieldIndex"}}, "FilterCount": {"type": "integer"}, "FilterLogEventsRequest": {"type": "structure", "members": {"logGroupName": {"shape": "LogGroupName", "documentation": "<p>The name of the log group to search.</p> <note> <p> You must include either <code>logGroupIdentifier</code> or <code>logGroupName</code>, but not both. </p> </note>"}, "logGroupIdentifier": {"shape": "LogGroupIdentifier", "documentation": "<p>Specify either the name or ARN of the log group to view log events from. If the log group is in a source account and you are using a monitoring account, you must use the log group ARN.</p> <note> <p> You must include either <code>logGroupIdentifier</code> or <code>logGroupName</code>, but not both. </p> </note>"}, "logStreamNames": {"shape": "InputLogStreamNames", "documentation": "<p>Filters the results to only logs from the log streams in this list.</p> <p>If you specify a value for both <code>logStreamNames</code> and <code>logStreamNamePrefix</code>, the action returns an <code>InvalidParameterException</code> error.</p>"}, "logStreamNamePrefix": {"shape": "LogStreamName", "documentation": "<p>Filters the results to include only events from log streams that have names starting with this prefix.</p> <p>If you specify a value for both <code>logStreamNamePrefix</code> and <code>logStreamNames</code>, the action returns an <code>InvalidParameterException</code> error.</p>"}, "startTime": {"shape": "Timestamp", "documentation": "<p>The start of the time range, expressed as the number of milliseconds after <code>Jan 1, 1970 00:00:00 UTC</code>. Events with a timestamp before this time are not returned.</p>"}, "endTime": {"shape": "Timestamp", "documentation": "<p>The end of the time range, expressed as the number of milliseconds after <code>Jan 1, 1970 00:00:00 UTC</code>. Events with a timestamp later than this time are not returned.</p>"}, "filterPattern": {"shape": "FilterPattern", "documentation": "<p>The filter pattern to use. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/FilterAndPatternSyntax.html\">Filter and Pattern Syntax</a>.</p> <p>If not provided, all the events are matched.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of events to return. (You received this token from a previous call.)</p>"}, "limit": {"shape": "EventsLimit", "documentation": "<p>The maximum number of events to return. The default is 10,000 events.</p>"}, "interleaved": {"shape": "Interleaved", "documentation": "<p>If the value is true, the operation attempts to provide responses that contain events from multiple log streams within the log group, interleaved in a single response. If the value is false, all the matched log events in the first log stream are searched first, then those in the next log stream, and so on.</p> <p> <b>Important</b> As of June 17, 2019, this parameter is ignored and the value is assumed to be true. The response from this operation always interleaves events from multiple log streams within a log group.</p>", "deprecated": true, "deprecatedMessage": "Starting on June 17, 2019, this parameter will be ignored and the value will be assumed to be true. The response from this operation will always interleave events from multiple log streams within a log group."}, "unmask": {"shape": "Unmask", "documentation": "<p>Specify <code>true</code> to display the log event fields with all sensitive data unmasked and visible. The default is <code>false</code>.</p> <p>To use this operation with this parameter, you must be signed into an account with the <code>logs:Unmask</code> permission.</p>"}}}, "FilterLogEventsResponse": {"type": "structure", "members": {"events": {"shape": "FilteredLogEvents", "documentation": "<p>The matched events.</p>"}, "searchedLogStreams": {"shape": "SearchedLogStreams", "documentation": "<p> <b>Important</b> As of May 15, 2020, this parameter is no longer supported. This parameter returns an empty list.</p> <p>Indicates which log streams have been searched and whether each has been searched completely.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token to use when requesting the next set of items. The token expires after 24 hours.</p> <p>If the results don't include a <code>nextToken</code>, then pagination is finished. </p>"}}}, "FilterName": {"type": "string", "max": 512, "min": 1, "pattern": "[^:*]*"}, "FilterPattern": {"type": "string", "documentation": "<p>A symbolic description of how CloudWatch Logs should interpret the data in each log event. For example, a log event can contain timestamps, IP addresses, strings, and so on. You use the filter pattern to specify what to look for in the log event message.</p>", "max": 1024, "min": 0}, "FilteredLogEvent": {"type": "structure", "members": {"logStreamName": {"shape": "LogStreamName", "documentation": "<p>The name of the log stream to which this event belongs.</p>"}, "timestamp": {"shape": "Timestamp", "documentation": "<p>The time the event occurred, expressed as the number of milliseconds after <code>Jan 1, 1970 00:00:00 UTC</code>.</p>"}, "message": {"shape": "EventMessage", "documentation": "<p>The data contained in the log event.</p>"}, "ingestionTime": {"shape": "Timestamp", "documentation": "<p>The time the event was ingested, expressed as the number of milliseconds after <code>Jan 1, 1970 00:00:00 UTC</code>.</p>"}, "eventId": {"shape": "EventId", "documentation": "<p>The ID of the event.</p>"}}, "documentation": "<p>Represents a matched event.</p>"}, "FilteredLogEvents": {"type": "list", "member": {"shape": "FilteredLogEvent"}}, "Flatten": {"type": "boolean"}, "FlattenedElement": {"type": "string", "enum": ["first", "last"]}, "Force": {"type": "boolean"}, "ForceUpdate": {"type": "boolean"}, "FromKey": {"type": "string", "max": 128, "min": 1}, "GetDataProtectionPolicyRequest": {"type": "structure", "required": ["logGroupIdentifier"], "members": {"logGroupIdentifier": {"shape": "LogGroupIdentifier", "documentation": "<p>The name or ARN of the log group that contains the data protection policy that you want to see.</p>"}}}, "GetDataProtectionPolicyResponse": {"type": "structure", "members": {"logGroupIdentifier": {"shape": "LogGroupIdentifier", "documentation": "<p>The log group name or ARN that you specified in your request.</p>"}, "policyDocument": {"shape": "DataProtectionPolicyDocument", "documentation": "<p>The data protection policy document for this log group.</p>"}, "lastUpdatedTime": {"shape": "Timestamp", "documentation": "<p>The date and time that this policy was most recently updated.</p>"}}}, "GetDeliveryDestinationPolicyRequest": {"type": "structure", "required": ["deliveryDestinationName"], "members": {"deliveryDestinationName": {"shape": "DeliveryDestinationName", "documentation": "<p>The name of the delivery destination that you want to retrieve the policy of.</p>"}}}, "GetDeliveryDestinationPolicyResponse": {"type": "structure", "members": {"policy": {"shape": "Policy", "documentation": "<p>The IAM policy for this delivery destination.</p>"}}}, "GetDeliveryDestinationRequest": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "DeliveryDestinationName", "documentation": "<p>The name of the delivery destination that you want to retrieve.</p>"}}}, "GetDeliveryDestinationResponse": {"type": "structure", "members": {"deliveryDestination": {"shape": "DeliveryDestination", "documentation": "<p>A structure containing information about the delivery destination.</p>"}}}, "GetDeliveryRequest": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "DeliveryId", "documentation": "<p>The ID of the delivery that you want to retrieve.</p>"}}}, "GetDeliveryResponse": {"type": "structure", "members": {"delivery": {"shape": "Delivery", "documentation": "<p>A structure that contains information about the delivery.</p>"}}}, "GetDeliverySourceRequest": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "DeliverySourceName", "documentation": "<p>The name of the delivery source that you want to retrieve.</p>"}}}, "GetDeliverySourceResponse": {"type": "structure", "members": {"deliverySource": {"shape": "DeliverySource", "documentation": "<p>A structure containing information about the delivery source.</p>"}}}, "GetIntegrationRequest": {"type": "structure", "required": ["integrationName"], "members": {"integrationName": {"shape": "IntegrationName", "documentation": "<p>The name of the integration that you want to find information about. To find the name of your integration, use <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_ListIntegrations.html\">ListIntegrations</a> </p>"}}}, "GetIntegrationResponse": {"type": "structure", "members": {"integrationName": {"shape": "IntegrationName", "documentation": "<p>The name of the integration.</p>"}, "integrationType": {"shape": "IntegrationType", "documentation": "<p>The type of integration. Integrations with OpenSearch Service have the type <code>OPENSEARCH</code>.</p>"}, "integrationStatus": {"shape": "IntegrationStatus", "documentation": "<p>The current status of this integration.</p>"}, "integrationDetails": {"shape": "IntegrationDetails", "documentation": "<p>A structure that contains information about the integration configuration. For an integration with OpenSearch Service, this includes information about OpenSearch Service resources such as the collection, the workspace, and policies.</p>"}}}, "GetLogAnomalyDetectorRequest": {"type": "structure", "required": ["anomalyDetectorArn"], "members": {"anomalyDetectorArn": {"shape": "AnomalyDetectorArn", "documentation": "<p>The ARN of the anomaly detector to retrieve information about. You can find the ARNs of log anomaly detectors in your account by using the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_ListLogAnomalyDetectors.html\">ListLogAnomalyDetectors</a> operation.</p>"}}}, "GetLogAnomalyDetectorResponse": {"type": "structure", "members": {"detectorName": {"shape": "DetectorName", "documentation": "<p>The name of the log anomaly detector</p>"}, "logGroupArnList": {"shape": "LogGroupArnList", "documentation": "<p>An array of structures, where each structure contains the ARN of a log group associated with this anomaly detector.</p>"}, "evaluationFrequency": {"shape": "EvaluationFrequency", "documentation": "<p>Specifies how often the anomaly detector runs and look for anomalies. Set this value according to the frequency that the log group receives new logs. For example, if the log group receives new log events every 10 minutes, then setting <code>evaluationFrequency</code> to <code>FIFTEEN_MIN</code> might be appropriate.</p>"}, "filterPattern": {"shape": "FilterPattern"}, "anomalyDetectorStatus": {"shape": "AnomalyDetectorStatus", "documentation": "<p>Specifies whether the anomaly detector is currently active. To change its status, use the <code>enabled</code> parameter in the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_UpdateLogAnomalyDetector.html\">UpdateLogAnomalyDetector</a> operation.</p>"}, "kmsKeyId": {"shape": "KmsKeyId", "documentation": "<p>The ARN of the KMS key assigned to this anomaly detector, if any.</p>"}, "creationTimeStamp": {"shape": "EpochMill<PERSON>", "documentation": "<p>The date and time when this anomaly detector was created.</p>"}, "lastModifiedTimeStamp": {"shape": "EpochMill<PERSON>", "documentation": "<p>The date and time when this anomaly detector was most recently modified.</p>"}, "anomalyVisibilityTime": {"shape": "AnomalyVisibilityTime", "documentation": "<p>The number of days used as the life cycle of anomalies. After this time, anomalies are automatically baselined and the anomaly detector model will treat new occurrences of similar event as normal. </p>"}}}, "GetLogEventsRequest": {"type": "structure", "required": ["logStreamName"], "members": {"logGroupName": {"shape": "LogGroupName", "documentation": "<p>The name of the log group.</p> <note> <p> You must include either <code>logGroupIdentifier</code> or <code>logGroupName</code>, but not both. </p> </note>"}, "logGroupIdentifier": {"shape": "LogGroupIdentifier", "documentation": "<p>Specify either the name or ARN of the log group to view events from. If the log group is in a source account and you are using a monitoring account, you must use the log group ARN.</p> <note> <p> You must include either <code>logGroupIdentifier</code> or <code>logGroupName</code>, but not both. </p> </note>"}, "logStreamName": {"shape": "LogStreamName", "documentation": "<p>The name of the log stream.</p>"}, "startTime": {"shape": "Timestamp", "documentation": "<p>The start of the time range, expressed as the number of milliseconds after <code>Jan 1, 1970 00:00:00 UTC</code>. Events with a timestamp equal to this time or later than this time are included. Events with a timestamp earlier than this time are not included.</p>"}, "endTime": {"shape": "Timestamp", "documentation": "<p>The end of the time range, expressed as the number of milliseconds after <code>Jan 1, 1970 00:00:00 UTC</code>. Events with a timestamp equal to or later than this time are not included.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of items to return. (You received this token from a previous call.)</p>"}, "limit": {"shape": "EventsLimit", "documentation": "<p>The maximum number of log events returned. If you don't specify a limit, the default is as many log events as can fit in a response size of 1 MB (up to 10,000 log events).</p>"}, "startFromHead": {"shape": "StartFromHead", "documentation": "<p>If the value is true, the earliest log events are returned first. If the value is false, the latest log events are returned first. The default value is false.</p> <p>If you are using a previous <code>nextForwardToken</code> value as the <code>nextToken</code> in this operation, you must specify <code>true</code> for <code>startFromHead</code>.</p>"}, "unmask": {"shape": "Unmask", "documentation": "<p>Specify <code>true</code> to display the log event fields with all sensitive data unmasked and visible. The default is <code>false</code>.</p> <p>To use this operation with this parameter, you must be signed into an account with the <code>logs:Unmask</code> permission.</p>"}}}, "GetLogEventsResponse": {"type": "structure", "members": {"events": {"shape": "OutputLogEvents", "documentation": "<p>The events.</p>"}, "nextForwardToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of items in the forward direction. The token expires after 24 hours. If you have reached the end of the stream, it returns the same token you passed in.</p>"}, "nextBackwardToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of items in the backward direction. The token expires after 24 hours. This token is not null. If you have reached the end of the stream, it returns the same token you passed in.</p>"}}}, "GetLogGroupFieldsRequest": {"type": "structure", "members": {"logGroupName": {"shape": "LogGroupName", "documentation": "<p>The name of the log group to search.</p> <note> <p> You must include either <code>logGroupIdentifier</code> or <code>logGroupName</code>, but not both. </p> </note>"}, "time": {"shape": "Timestamp", "documentation": "<p>The time to set as the center of the query. If you specify <code>time</code>, the 8 minutes before and 8 minutes after this time are searched. If you omit <code>time</code>, the most recent 15 minutes up to the current time are searched.</p> <p>The <code>time</code> value is specified as epoch time, which is the number of seconds since <code>January 1, 1970, 00:00:00 UTC</code>.</p>"}, "logGroupIdentifier": {"shape": "LogGroupIdentifier", "documentation": "<p>Specify either the name or ARN of the log group to view. If the log group is in a source account and you are using a monitoring account, you must specify the ARN.</p> <note> <p> You must include either <code>logGroupIdentifier</code> or <code>logGroupName</code>, but not both. </p> </note>"}}}, "GetLogGroupFieldsResponse": {"type": "structure", "members": {"logGroupFields": {"shape": "LogGroupFieldList", "documentation": "<p>The array of fields found in the query. Each object in the array contains the name of the field, along with the percentage of time it appeared in the log events that were queried.</p>"}}}, "GetLogRecordRequest": {"type": "structure", "required": ["logRecordPointer"], "members": {"logRecordPointer": {"shape": "LogRecordPointer", "documentation": "<p>The pointer corresponding to the log event record you want to retrieve. You get this from the response of a <code>GetQueryResults</code> operation. In that response, the value of the <code>@ptr</code> field for a log event is the value to use as <code>logRecordPointer</code> to retrieve that complete log event record.</p>"}, "unmask": {"shape": "Unmask", "documentation": "<p>Specify <code>true</code> to display the log event fields with all sensitive data unmasked and visible. The default is <code>false</code>.</p> <p>To use this operation with this parameter, you must be signed into an account with the <code>logs:Unmask</code> permission.</p>"}}}, "GetLogRecordResponse": {"type": "structure", "members": {"logRecord": {"shape": "LogRecord", "documentation": "<p>The requested log event, as a JSON string.</p>"}}}, "GetQueryResultsRequest": {"type": "structure", "required": ["queryId"], "members": {"queryId": {"shape": "QueryId", "documentation": "<p>The ID number of the query.</p>"}}}, "GetQueryResultsResponse": {"type": "structure", "members": {"queryLanguage": {"shape": "QueryLanguage", "documentation": "<p>The query language used for this query. For more information about the query languages that CloudWatch Logs supports, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CWL_AnalyzeLogData_Languages.html\">Supported query languages</a>.</p>"}, "results": {"shape": "QueryResults", "documentation": "<p>The log events that matched the query criteria during the most recent time it ran.</p> <p>The <code>results</code> value is an array of arrays. Each log event is one object in the top-level array. Each of these log event objects is an array of <code>field</code>/<code>value</code> pairs.</p>"}, "statistics": {"shape": "QueryStatistics", "documentation": "<p>Includes the number of log events scanned by the query, the number of log events that matched the query criteria, and the total number of bytes in the scanned log events. These values reflect the full raw results of the query.</p>"}, "status": {"shape": "QueryStatus", "documentation": "<p>The status of the most recent running of the query. Possible values are <code>Cancelled</code>, <code>Complete</code>, <code>Failed</code>, <code>Running</code>, <code>Scheduled</code>, <code>Timeout</code>, and <code>Unknown</code>.</p> <p>Queries time out after 60 minutes of runtime. To avoid having your queries time out, reduce the time range being searched or partition your query into a number of queries.</p>"}, "encryptionKey": {"shape": "EncryptionKey", "documentation": "<p>If you associated an KMS key with the CloudWatch Logs Insights query results in this account, this field displays the ARN of the key that's used to encrypt the query results when <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_StartQuery.html\">StartQuery</a> stores them.</p>"}}}, "GetTransformerRequest": {"type": "structure", "required": ["logGroupIdentifier"], "members": {"logGroupIdentifier": {"shape": "LogGroupIdentifier", "documentation": "<p>Specify either the name or ARN of the log group to return transformer information for. If the log group is in a source account and you are using a monitoring account, you must use the log group ARN.</p>"}}}, "GetTransformerResponse": {"type": "structure", "members": {"logGroupIdentifier": {"shape": "LogGroupIdentifier", "documentation": "<p>The ARN of the log group that you specified in your request.</p>"}, "creationTime": {"shape": "Timestamp", "documentation": "<p>The creation time of the transformer, expressed as the number of milliseconds after Jan 1, 1970 00:00:00 UTC.</p>"}, "lastModifiedTime": {"shape": "Timestamp", "documentation": "<p>The date and time when this transformer was most recently modified, expressed as the number of milliseconds after Jan 1, 1970 00:00:00 UTC.</p>"}, "transformerConfig": {"shape": "Processors", "documentation": "<p>This sructure contains the configuration of the requested transformer.</p>"}}}, "Grok": {"type": "structure", "required": ["match"], "members": {"source": {"shape": "Source", "documentation": "<p>The path to the field in the log event that you want to parse. If you omit this value, the whole log message is parsed.</p>"}, "match": {"shape": "GrokMatch", "documentation": "<p>The grok pattern to match against the log event. For a list of supported grok patterns, see <a href=\"https://docs.aws.amazon.com/mazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation-Processors.html#Grok-Patterns\">Supported grok patterns</a>.</p>"}}, "documentation": "<p>This processor uses pattern matching to parse and structure unstructured data. This processor can also extract fields from log messages.</p> <p>For more information about this processor including examples, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-Grok\"> grok</a> in the <i>CloudWatch Logs User Guide</i>.</p>"}, "GrokMatch": {"type": "string", "max": 512, "min": 1}, "Histogram": {"type": "map", "key": {"shape": "Time"}, "value": {"shape": "Count"}}, "IncludeLinkedAccounts": {"type": "boolean"}, "IndexPolicies": {"type": "list", "member": {"shape": "IndexPolicy"}}, "IndexPolicy": {"type": "structure", "members": {"logGroupIdentifier": {"shape": "LogGroupIdentifier", "documentation": "<p>The ARN of the log group that this index policy applies to.</p>"}, "lastUpdateTime": {"shape": "Timestamp", "documentation": "<p>The date and time that this index policy was most recently updated.</p>"}, "policyDocument": {"shape": "PolicyDocument", "documentation": "<p>The policy document for this index policy, in JSON format.</p>"}, "policyName": {"shape": "PolicyName", "documentation": "<p>The name of this policy. Responses about log group-level field index policies don't have this field, because those policies don't have names.</p>"}, "source": {"shape": "IndexSource", "documentation": "<p>This field indicates whether this is an account-level index policy or an index policy that applies only to a single log group.</p>"}}, "documentation": "<p>This structure contains information about one field index policy in this account.</p>"}, "IndexSource": {"type": "string", "enum": ["ACCOUNT", "LOG_GROUP"]}, "InferredTokenName": {"type": "string", "min": 1}, "InheritedProperties": {"type": "list", "member": {"shape": "InheritedProperty"}}, "InheritedProperty": {"type": "string", "enum": ["ACCOUNT_DATA_PROTECTION"]}, "InputLogEvent": {"type": "structure", "required": ["timestamp", "message"], "members": {"timestamp": {"shape": "Timestamp", "documentation": "<p>The time the event occurred, expressed as the number of milliseconds after <code>Jan 1, 1970 00:00:00 UTC</code>.</p>"}, "message": {"shape": "EventMessage", "documentation": "<p>The raw event message. Each log event can be no larger than 1 MB.</p>"}}, "documentation": "<p>Represents a log event, which is a record of activity that was recorded by the application or resource being monitored.</p>"}, "InputLogEvents": {"type": "list", "member": {"shape": "InputLogEvent"}, "max": 10000, "min": 1}, "InputLogStreamNames": {"type": "list", "member": {"shape": "LogStreamName"}, "max": 100, "min": 1}, "Integer": {"type": "integer"}, "IntegrationDetails": {"type": "structure", "members": {"openSearchIntegrationDetails": {"shape": "OpenSearchIntegrationDetails", "documentation": "<p>This structure contains complete information about one integration between CloudWatch Logs and OpenSearch Service.</p>"}}, "documentation": "<p>This structure contains information about the integration configuration. For an integration with OpenSearch Service, this includes information about OpenSearch Service resources such as the collection, the workspace, and policies.</p> <p>This structure is returned by a <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_GetIntegration.html\">GetIntegration</a> operation.</p>", "union": true}, "IntegrationName": {"type": "string", "max": 50, "min": 1, "pattern": "[\\.\\-_/#A-Za-z0-9]+"}, "IntegrationNamePrefix": {"type": "string", "max": 50, "min": 1, "pattern": "[\\.\\-_/#A-Za-z0-9]+"}, "IntegrationStatus": {"type": "string", "enum": ["PROVISIONING", "ACTIVE", "FAILED"]}, "IntegrationStatusMessage": {"type": "string", "min": 1}, "IntegrationSummaries": {"type": "list", "member": {"shape": "IntegrationSummary"}}, "IntegrationSummary": {"type": "structure", "members": {"integrationName": {"shape": "IntegrationName", "documentation": "<p>The name of this integration.</p>"}, "integrationType": {"shape": "IntegrationType", "documentation": "<p>The type of integration. Integrations with OpenSearch Service have the type <code>OPENSEARCH</code>.</p>"}, "integrationStatus": {"shape": "IntegrationStatus", "documentation": "<p>The current status of this integration.</p>"}}, "documentation": "<p>This structure contains information about one CloudWatch Logs integration. This structure is returned by a <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_ListIntegrations.html\">ListIntegrations</a> operation.</p>"}, "IntegrationType": {"type": "string", "enum": ["OPENSEARCH"]}, "Interleaved": {"type": "boolean"}, "InvalidOperationException": {"type": "structure", "members": {}, "documentation": "<p>The operation is not valid on the specified resource.</p>", "exception": true}, "InvalidParameterException": {"type": "structure", "members": {}, "documentation": "<p>A parameter is specified incorrectly.</p>", "exception": true}, "InvalidSequenceTokenException": {"type": "structure", "members": {"expectedSequenceToken": {"shape": "SequenceToken"}}, "documentation": "<p>The sequence token is not valid. You can get the correct sequence token in the <code>expectedSequenceToken</code> field in the <code>InvalidSequenceTokenException</code> message. </p> <important> <p> <code>PutLogEvents</code> actions are now always accepted and never return <code>InvalidSequenceTokenException</code> regardless of receiving an invalid sequence token. </p> </important>", "exception": true}, "IsSampled": {"type": "boolean"}, "Key": {"type": "string", "max": 128, "min": 1}, "KeyPrefix": {"type": "string", "max": 128, "min": 1}, "KeyValueDelimiter": {"type": "string", "max": 128, "min": 1}, "KmsKeyId": {"type": "string", "max": 256}, "LimitExceededException": {"type": "structure", "members": {}, "documentation": "<p>You have reached the maximum number of resources that can be created.</p>", "exception": true}, "ListAnomaliesLimit": {"type": "integer", "max": 50, "min": 1}, "ListAnomaliesRequest": {"type": "structure", "members": {"anomalyDetectorArn": {"shape": "AnomalyDetectorArn", "documentation": "<p>Use this to optionally limit the results to only the anomalies found by a certain anomaly detector.</p>"}, "suppressionState": {"shape": "SuppressionState", "documentation": "<p>You can specify this parameter if you want to the operation to return only anomalies that are currently either suppressed or unsuppressed.</p>"}, "limit": {"shape": "ListAnomaliesLimit", "documentation": "<p>The maximum number of items to return. If you don't specify a value, the default maximum value of 50 items is used.</p>"}, "nextToken": {"shape": "NextToken"}}}, "ListAnomaliesResponse": {"type": "structure", "members": {"anomalies": {"shape": "Anomalies", "documentation": "<p>An array of structures, where each structure contains information about one anomaly that a log anomaly detector has found.</p>"}, "nextToken": {"shape": "NextToken"}}}, "ListIntegrationsRequest": {"type": "structure", "members": {"integrationNamePrefix": {"shape": "IntegrationNamePrefix", "documentation": "<p>To limit the results to integrations that start with a certain name prefix, specify that name prefix here.</p>"}, "integrationType": {"shape": "IntegrationType", "documentation": "<p>To limit the results to integrations of a certain type, specify that type here.</p>"}, "integrationStatus": {"shape": "IntegrationStatus", "documentation": "<p>To limit the results to integrations with a certain status, specify that status here.</p>"}}}, "ListIntegrationsResponse": {"type": "structure", "members": {"integrationSummaries": {"shape": "IntegrationSummaries", "documentation": "<p>An array, where each object in the array contains information about one CloudWatch Logs integration in this account. </p>"}}}, "ListLimit": {"type": "integer", "max": 1000, "min": 1}, "ListLogAnomalyDetectorsLimit": {"type": "integer", "max": 50, "min": 1}, "ListLogAnomalyDetectorsRequest": {"type": "structure", "members": {"filterLogGroupArn": {"shape": "LogGroupArn", "documentation": "<p>Use this to optionally filter the results to only include anomaly detectors that are associated with the specified log group.</p>"}, "limit": {"shape": "ListLogAnomalyDetectorsLimit", "documentation": "<p>The maximum number of items to return. If you don't specify a value, the default maximum value of 50 items is used.</p>"}, "nextToken": {"shape": "NextToken"}}}, "ListLogAnomalyDetectorsResponse": {"type": "structure", "members": {"anomalyDetectors": {"shape": "AnomalyDetectors", "documentation": "<p>An array of structures, where each structure in the array contains information about one anomaly detector.</p>"}, "nextToken": {"shape": "NextToken"}}}, "ListLogGroupsForQueryMaxResults": {"type": "integer", "max": 500, "min": 50}, "ListLogGroupsForQueryRequest": {"type": "structure", "required": ["queryId"], "members": {"queryId": {"shape": "QueryId", "documentation": "<p>The ID of the query to use. This query ID is from the response to your <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_StartQuery.html\">StartQuery</a> operation.</p>"}, "nextToken": {"shape": "NextToken"}, "maxResults": {"shape": "ListLogGroupsForQueryMaxResults", "documentation": "<p>Limits the number of returned log groups to the specified number.</p>"}}}, "ListLogGroupsForQueryResponse": {"type": "structure", "members": {"logGroupIdentifiers": {"shape": "LogGroupIdentifiers", "documentation": "<p>An array of the names and ARNs of the log groups that were processed in the query.</p>"}, "nextToken": {"shape": "NextToken"}}}, "ListLogGroupsRequest": {"type": "structure", "members": {"logGroupNamePattern": {"shape": "LogGroupNameRegexPattern", "documentation": "<p>Use this parameter to limit the returned log groups to only those with names that match the pattern that you specify. This parameter is a regular expression that can match prefixes and substrings, and supports wildcard matching and matching multiple patterns, as in the following examples. </p> <ul> <li> <p>Use <code>^</code> to match log group names by prefix.</p> </li> <li> <p>For a substring match, specify the string to match. All matches are case sensitive</p> </li> <li> <p>To match multiple patterns, separate them with a <code>|</code> as in the example <code>^/aws/lambda|discovery</code> </p> </li> </ul> <p>You can specify as many as five different regular expression patterns in this field, each of which must be between 3 and 24 characters. You can include the <code>^</code> symbol as many as five times, and include the <code>|</code> symbol as many as four times.</p>"}, "logGroupClass": {"shape": "LogGroupClass", "documentation": "<p>Use this parameter to limit the results to only those log groups in the specified log group class. If you omit this parameter, log groups of all classes can be returned.</p>"}, "includeLinkedAccounts": {"shape": "IncludeLinkedAccounts", "documentation": "<p>If you are using a monitoring account, set this to <code>true</code> to have the operation return log groups in the accounts listed in <code>accountIdentifiers</code>.</p> <p>If this parameter is set to <code>true</code> and <code>accountIdentifiers</code> contains a null value, the operation returns all log groups in the monitoring account and all log groups in all source accounts that are linked to the monitoring account. </p> <p>The default for this parameter is <code>false</code>.</p>"}, "accountIdentifiers": {"shape": "AccountIds", "documentation": "<p>When <code>includeLinkedAccounts</code> is set to <code>true</code>, use this parameter to specify the list of accounts to search. You can specify as many as 20 account IDs in the array.</p>"}, "nextToken": {"shape": "NextToken"}, "limit": {"shape": "ListLimit", "documentation": "<p>The maximum number of log groups to return. If you omit this parameter, the default is up to 50 log groups.</p>"}}}, "ListLogGroupsResponse": {"type": "structure", "members": {"logGroups": {"shape": "LogGroupSummaries", "documentation": "<p>An array of structures, where each structure contains the information about one log group.</p>"}, "nextToken": {"shape": "NextToken"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "AmazonResourceName", "documentation": "<p>The ARN of the resource that you want to view tags for.</p> <p>The ARN format of a log group is <code>arn:aws:logs:<i>Region</i>:<i>account-id</i>:log-group:<i>log-group-name</i> </code> </p> <p>The ARN format of a destination is <code>arn:aws:logs:<i>Region</i>:<i>account-id</i>:destination:<i>destination-name</i> </code> </p> <p>For more information about ARN format, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/iam-access-control-overview-cwl.html\">CloudWatch Logs resources and operations</a>.</p>"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"tags": {"shape": "Tags", "documentation": "<p>The list of tags associated with the requested resource.&gt;</p>"}}}, "ListTagsLogGroupRequest": {"type": "structure", "required": ["logGroupName"], "members": {"logGroupName": {"shape": "LogGroupName", "documentation": "<p>The name of the log group.</p>"}}, "deprecated": true, "deprecatedMessage": "Please use the generic tagging API model ListTagsForResourceRequest and ListTagsForResourceResponse"}, "ListTagsLogGroupResponse": {"type": "structure", "members": {"tags": {"shape": "Tags", "documentation": "<p>The tags for the log group.</p>"}}, "deprecated": true, "deprecatedMessage": "Please use the generic tagging API model ListTagsForResourceRequest and ListTagsForResourceResponse"}, "ListToMap": {"type": "structure", "required": ["source", "key"], "members": {"source": {"shape": "Source", "documentation": "<p>The key in the log event that has a list of objects that will be converted to a map.</p>"}, "key": {"shape": "Key", "documentation": "<p>The key of the field to be extracted as keys in the generated map</p>"}, "valueKey": {"shape": "ValueKey", "documentation": "<p>If this is specified, the values that you specify in this parameter will be extracted from the <code>source</code> objects and put into the values of the generated map. Otherwise, original objects in the source list will be put into the values of the generated map.</p>"}, "target": {"shape": "Target", "documentation": "<p>The key of the field that will hold the generated map </p>"}, "flatten": {"shape": "<PERSON><PERSON>", "documentation": "<p>A Boolean value to indicate whether the list will be flattened into single items. Specify <code>true</code> to flatten the list. The default is <code>false</code> </p>"}, "flattenedElement": {"shape": "FlattenedElement", "documentation": "<p>If you set <code>flatten</code> to <code>true</code>, use <code>flattenedElement</code> to specify which element, <code>first</code> or <code>last</code>, to keep. </p> <p>You must specify this parameter if <code>flatten</code> is <code>true</code> </p>"}}, "documentation": "<p>This processor takes a list of objects that contain key fields, and converts them into a map of target keys.</p> <p>For more information about this processor including examples, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation-Processors.html#CloudWatch-Logs-Transformation-listToMap\"> listToMap</a> in the <i>CloudWatch Logs User Guide</i>.</p>"}, "LiveTailSessionLogEvent": {"type": "structure", "members": {"logStreamName": {"shape": "LogStreamName", "documentation": "<p>The name of the log stream that ingested this log event.</p>"}, "logGroupIdentifier": {"shape": "LogGroupIdentifier", "documentation": "<p>The name or ARN of the log group that ingested this log event.</p>"}, "message": {"shape": "EventMessage", "documentation": "<p>The log event message text.</p>"}, "timestamp": {"shape": "Timestamp", "documentation": "<p>The timestamp specifying when this log event was created.</p>"}, "ingestionTime": {"shape": "Timestamp", "documentation": "<p>The timestamp specifying when this log event was ingested into the log group.</p>"}}, "documentation": "<p>This object contains the information for one log event returned in a Live Tail stream.</p>"}, "LiveTailSessionMetadata": {"type": "structure", "members": {"sampled": {"shape": "IsSampled", "documentation": "<p>If this is <code>true</code>, then more than 500 log events matched the request for this update, and the <code>sessionResults</code> includes a sample of 500 of those events.</p> <p>If this is <code>false</code>, then 500 or fewer log events matched the request for this update, so no sampling was necessary. In this case, the <code>sessionResults</code> array includes all log events that matched your request during this time.</p>"}}, "documentation": "<p>This object contains the metadata for one <code>LiveTailSessionUpdate</code> structure. It indicates whether that update includes only a sample of 500 log events out of a larger number of ingested log events, or if it contains all of the matching log events ingested during that second of time.</p>"}, "LiveTailSessionResults": {"type": "list", "member": {"shape": "LiveTailSessionLogEvent"}}, "LiveTailSessionStart": {"type": "structure", "members": {"requestId": {"shape": "RequestId", "documentation": "<p>The unique ID generated by CloudWatch Logs to identify this Live Tail session request.</p>"}, "sessionId": {"shape": "SessionId", "documentation": "<p>The unique ID generated by CloudWatch Logs to identify this Live Tail session.</p>"}, "logGroupIdentifiers": {"shape": "StartLiveTailLogGroupIdentifiers", "documentation": "<p>An array of the names and ARNs of the log groups included in this Live Tail session.</p>"}, "logStreamNames": {"shape": "InputLogStreamNames", "documentation": "<p>If your StartLiveTail operation request included a <code>logStreamNames</code> parameter that filtered the session to only include certain log streams, these streams are listed here.</p>"}, "logStreamNamePrefixes": {"shape": "InputLogStreamNames", "documentation": "<p>If your StartLiveTail operation request included a <code>logStreamNamePrefixes</code> parameter that filtered the session to only include log streams that have names that start with certain prefixes, these prefixes are listed here.</p>"}, "logEventFilterPattern": {"shape": "FilterPattern", "documentation": "<p>An optional pattern to filter the results to include only log events that match the pattern. For example, a filter pattern of <code>error 404</code> displays only log events that include both <code>error</code> and <code>404</code>.</p> <p>For more information about filter pattern syntax, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/FilterAndPatternSyntax.html\">Filter and Pattern Syntax</a>.</p>"}}, "documentation": "<p>This object contains information about this Live Tail session, including the log groups included and the log stream filters, if any.</p>", "event": true}, "LiveTailSessionUpdate": {"type": "structure", "members": {"sessionMetadata": {"shape": "LiveTailSessionMetadata", "documentation": "<p>This object contains the session metadata for a Live Tail session.</p>"}, "sessionResults": {"shape": "LiveTailSessionResults", "documentation": "<p>An array, where each member of the array includes the information for one log event in the Live Tail session.</p> <p>A <code>sessionResults</code> array can include as many as 500 log events. If the number of log events matching the request exceeds 500 per second, the log events are sampled down to 500 log events to be included in each <code>sessionUpdate</code> structure.</p>"}}, "documentation": "<p>This object contains the log events and metadata for a Live Tail session.</p>", "event": true}, "Locale": {"type": "string", "min": 1}, "LogEvent": {"type": "structure", "members": {"timestamp": {"shape": "Timestamp", "documentation": "<p>The time stamp of the log event.</p>"}, "message": {"shape": "EventMessage", "documentation": "<p>The message content of the log event.</p>"}}, "documentation": "<p>This structure contains the information for one sample log event that is associated with an anomaly found by a log anomaly detector.</p>"}, "LogEventIndex": {"type": "integer"}, "LogGroup": {"type": "structure", "members": {"logGroupName": {"shape": "LogGroupName", "documentation": "<p>The name of the log group.</p>"}, "creationTime": {"shape": "Timestamp", "documentation": "<p>The creation time of the log group, expressed as the number of milliseconds after Jan 1, 1970 00:00:00 UTC.</p>"}, "retentionInDays": {"shape": "Days"}, "metricFilterCount": {"shape": "FilterCount", "documentation": "<p>The number of metric filters.</p>"}, "arn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the log group. This version of the ARN includes a trailing <code>:*</code> after the log group name. </p> <p>Use this version to refer to the ARN in IAM policies when specifying permissions for most API actions. The exception is when specifying permissions for <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_TagResource.html\">TagResource</a>, <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_UntagResource.html\">UntagResource</a>, and <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_ListTagsForResource.html\">ListTagsForResource</a>. The permissions for those three actions require the ARN version that doesn't include a trailing <code>:*</code>.</p>"}, "storedBytes": {"shape": "StoredBytes", "documentation": "<p>The number of bytes stored.</p>"}, "kmsKeyId": {"shape": "KmsKeyId", "documentation": "<p>The Amazon Resource Name (ARN) of the KMS key to use when encrypting log data.</p>"}, "dataProtectionStatus": {"shape": "DataProtectionStatus", "documentation": "<p>Displays whether this log group has a protection policy, or whether it had one in the past. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutDataProtectionPolicy.html\">PutDataProtectionPolicy</a>.</p>"}, "inheritedProperties": {"shape": "InheritedProperties", "documentation": "<p>Displays all the properties that this log group has inherited from account-level settings.</p>"}, "logGroupClass": {"shape": "LogGroupClass", "documentation": "<p>This specifies the log group class for this log group. There are three classes:</p> <ul> <li> <p>The <code>Standard</code> log class supports all CloudWatch Logs features.</p> </li> <li> <p>The <code>Infrequent Access</code> log class supports a subset of CloudWatch Logs features and incurs lower costs.</p> </li> <li> <p>Use the <code>Delivery</code> log class only for delivering Lambda logs to store in Amazon S3 or Amazon Data Firehose. Log events in log groups in the Delivery class are kept in CloudWatch Logs for only one day. This log class doesn't offer rich CloudWatch Logs capabilities such as CloudWatch Logs Insights queries.</p> </li> </ul> <p>For details about the features supported by the Standard and Infrequent Access classes, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch_Logs_Log_Classes.html\">Log classes</a> </p>"}, "logGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the log group. This version of the ARN doesn't include a trailing <code>:*</code> after the log group name. </p> <p>Use this version to refer to the ARN in the following situations:</p> <ul> <li> <p>In the <code>logGroupIdentifier</code> input field in many CloudWatch Logs APIs.</p> </li> <li> <p>In the <code>resourceArn</code> field in tagging APIs</p> </li> <li> <p>In IAM policies, when specifying permissions for <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_TagResource.html\">TagResource</a>, <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_UntagResource.html\">UntagResource</a>, and <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_ListTagsForResource.html\">ListTagsForResource</a>.</p> </li> </ul>"}}, "documentation": "<p>Represents a log group.</p>"}, "LogGroupArn": {"type": "string", "max": 2048, "min": 1, "pattern": "[\\w#+=/:,.@-]*"}, "LogGroupArnList": {"type": "list", "member": {"shape": "LogGroupArn"}}, "LogGroupClass": {"type": "string", "enum": ["STANDARD", "INFREQUENT_ACCESS", "DELIVERY"]}, "LogGroupField": {"type": "structure", "members": {"name": {"shape": "Field", "documentation": "<p>The name of a log field.</p>"}, "percent": {"shape": "Percentage", "documentation": "<p>The percentage of log events queried that contained the field.</p>"}}, "documentation": "<p>The fields contained in log events found by a <code>GetLogGroupFields</code> operation, along with the percentage of queried log events in which each field appears.</p>"}, "LogGroupFieldList": {"type": "list", "member": {"shape": "LogGroupField"}}, "LogGroupIdentifier": {"type": "string", "max": 2048, "min": 1, "pattern": "[\\w#+=/:,.@-]*"}, "LogGroupIdentifiers": {"type": "list", "member": {"shape": "LogGroupIdentifier"}}, "LogGroupName": {"type": "string", "max": 512, "min": 1, "pattern": "[\\.\\-_/#A-Za-z0-9]+"}, "LogGroupNamePattern": {"type": "string", "max": 512, "min": 0, "pattern": "[\\.\\-_/#A-Za-z0-9]*"}, "LogGroupNameRegexPattern": {"type": "string", "max": 129, "min": 3, "pattern": "(\\^?[\\.\\-_\\/#A-Za-z0-9]{3,24})(\\|\\^?[\\.\\-_\\/#A-Za-z0-9]{3,24}){0,4}"}, "LogGroupNames": {"type": "list", "member": {"shape": "LogGroupName"}}, "LogGroupSummaries": {"type": "list", "member": {"shape": "LogGroupSummary"}}, "LogGroupSummary": {"type": "structure", "members": {"logGroupName": {"shape": "LogGroupName", "documentation": "<p>The name of the log group.</p>"}, "logGroupArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the log group.</p>"}, "logGroupClass": {"shape": "LogGroupClass", "documentation": "<p>The log group class for this log group. For details about the features supported by each log group class, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch_Logs_Log_Classes.html\">Log classes</a> </p>"}}, "documentation": "<p>This structure contains information about one log group in your account.</p>"}, "LogGroups": {"type": "list", "member": {"shape": "LogGroup"}}, "LogRecord": {"type": "map", "key": {"shape": "Field"}, "value": {"shape": "Value"}}, "LogRecordPointer": {"type": "string"}, "LogSamples": {"type": "list", "member": {"shape": "LogEvent"}}, "LogStream": {"type": "structure", "members": {"logStreamName": {"shape": "LogStreamName", "documentation": "<p>The name of the log stream.</p>"}, "creationTime": {"shape": "Timestamp", "documentation": "<p>The creation time of the stream, expressed as the number of milliseconds after <code>Jan 1, 1970 00:00:00 UTC</code>.</p>"}, "firstEventTimestamp": {"shape": "Timestamp", "documentation": "<p>The time of the first event, expressed as the number of milliseconds after <code>Jan 1, 1970 00:00:00 UTC</code>.</p>"}, "lastEventTimestamp": {"shape": "Timestamp", "documentation": "<p>The time of the most recent log event in the log stream in CloudWatch Logs. This number is expressed as the number of milliseconds after <code>Jan 1, 1970 00:00:00 UTC</code>. The <code>lastEventTime</code> value updates on an eventual consistency basis. It typically updates in less than an hour from ingestion, but in rare situations might take longer.</p>"}, "lastIngestionTime": {"shape": "Timestamp", "documentation": "<p>The ingestion time, expressed as the number of milliseconds after <code>Jan 1, 1970 00:00:00 UTC</code> The <code>lastIngestionTime</code> value updates on an eventual consistency basis. It typically updates in less than an hour after ingestion, but in rare situations might take longer.</p>"}, "uploadSequenceToken": {"shape": "SequenceToken", "documentation": "<p>The sequence token.</p> <important> <p>The sequence token is now ignored in <code>PutLogEvents</code> actions. <code>PutLogEvents</code> actions are always accepted regardless of receiving an invalid sequence token. You don't need to obtain <code>uploadSequenceToken</code> to use a <code>PutLogEvents</code> action.</p> </important>"}, "arn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the log stream.</p>"}, "storedBytes": {"shape": "StoredBytes", "documentation": "<p>The number of bytes stored.</p> <p> <b>Important:</b> As of June 17, 2019, this parameter is no longer supported for log streams, and is always reported as zero. This change applies only to log streams. The <code>storedBytes</code> parameter for log groups is not affected.</p>", "deprecated": true, "deprecatedMessage": "Starting on June 17, 2019, this parameter will be deprecated for log streams, and will be reported as zero. This change applies only to log streams. The storedBytes parameter for log groups is not affected."}}, "documentation": "<p>Represents a log stream, which is a sequence of log events from a single emitter of logs.</p>"}, "LogStreamName": {"type": "string", "max": 512, "min": 1, "pattern": "[^:*]*"}, "LogStreamSearchedCompletely": {"type": "boolean"}, "LogStreams": {"type": "list", "member": {"shape": "LogStream"}}, "LogType": {"type": "string", "max": 255, "min": 1, "pattern": "[\\w]*"}, "LogTypes": {"type": "list", "member": {"shape": "LogType"}, "max": 10, "min": 1}, "LowerCaseString": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON>"], "members": {"withKeys": {"shape": "LowerCaseStringWithKeys", "documentation": "<p>The array caontaining the keys of the fields to convert to lowercase.</p>"}}, "documentation": "<p>This processor converts a string to lowercase.</p> <p>For more information about this processor including examples, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-lowerCaseString\"> lowerCaseString</a> in the <i>CloudWatch Logs User Guide</i>.</p>"}, "LowerCaseStringWithKeys": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON>"}, "max": 10, "min": 1}, "MalformedQueryException": {"type": "structure", "members": {"queryCompileError": {"shape": "QueryCompileError"}}, "documentation": "<p>The query string is not valid. Details about this error are displayed in a <code>QueryCompileError</code> object. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_QueryCompileError.html\">QueryCompileError</a>.</p> <p>For more information about valid query syntax, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CWL_QuerySyntax.html\">CloudWatch Logs Insights Query Syntax</a>.</p>", "exception": true}, "MatchPattern": {"type": "string", "min": 1}, "MatchPatterns": {"type": "list", "member": {"shape": "MatchPattern"}, "max": 5, "min": 1}, "Message": {"type": "string"}, "MetricFilter": {"type": "structure", "members": {"filterName": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The name of the metric filter.</p>"}, "filterPattern": {"shape": "FilterPattern"}, "metricTransformations": {"shape": "MetricTransformations", "documentation": "<p>The metric transformations.</p>"}, "creationTime": {"shape": "Timestamp", "documentation": "<p>The creation time of the metric filter, expressed as the number of milliseconds after <code>Jan 1, 1970 00:00:00 UTC</code>.</p>"}, "logGroupName": {"shape": "LogGroupName", "documentation": "<p>The name of the log group.</p>"}, "applyOnTransformedLogs": {"shape": "ApplyOnTransformedLogs", "documentation": "<p>This parameter is valid only for log groups that have an active log transformer. For more information about log transformers, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutTransformer.html\">PutTransformer</a>.</p> <p>If this value is <code>true</code>, the metric filter is applied on the transformed version of the log events instead of the original ingested log events.</p>"}}, "documentation": "<p>Metric filters express how CloudWatch Logs would extract metric observations from ingested log events and transform them into metric data in a CloudWatch metric.</p>"}, "MetricFilterMatchRecord": {"type": "structure", "members": {"eventNumber": {"shape": "EventNumber", "documentation": "<p>The event number.</p>"}, "eventMessage": {"shape": "EventMessage", "documentation": "<p>The raw event data.</p>"}, "extractedValues": {"shape": "ExtractedValues", "documentation": "<p>The values extracted from the event data by the filter.</p>"}}, "documentation": "<p>Represents a matched event.</p>"}, "MetricFilterMatches": {"type": "list", "member": {"shape": "MetricFilterMatchRecord"}}, "MetricFilters": {"type": "list", "member": {"shape": "<PERSON>ric<PERSON><PERSON><PERSON>"}}, "MetricName": {"type": "string", "documentation": "<p>The name of the CloudWatch metric to which the monitored log information should be published. For example, you might publish to a metric named ErrorCount.</p>", "max": 255, "pattern": "[^:*$]*"}, "MetricNamespace": {"type": "string", "max": 255, "pattern": "[^:*$]*"}, "MetricTransformation": {"type": "structure", "required": ["metricName", "metricNamespace", "metricValue"], "members": {"metricName": {"shape": "MetricName", "documentation": "<p>The name of the CloudWatch metric.</p>"}, "metricNamespace": {"shape": "MetricNamespace", "documentation": "<p>A custom namespace to contain your metric in CloudWatch. Use namespaces to group together metrics that are similar. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/cloudwatch_concepts.html#Namespace\">Namespaces</a>.</p>"}, "metricValue": {"shape": "MetricValue", "documentation": "<p>The value to publish to the CloudWatch metric when a filter pattern matches a log event.</p>"}, "defaultValue": {"shape": "DefaultValue", "documentation": "<p>(Optional) The value to emit when a filter pattern does not match a log event. This value can be null.</p>"}, "dimensions": {"shape": "Dimensions", "documentation": "<p>The fields to use as dimensions for the metric. One metric filter can include as many as three dimensions.</p> <important> <p>Metrics extracted from log events are charged as custom metrics. To prevent unexpected high charges, do not specify high-cardinality fields such as <code>IPAddress</code> or <code>requestID</code> as dimensions. Each different value found for a dimension is treated as a separate metric and accrues charges as a separate custom metric. </p> <p>CloudWatch Logs disables a metric filter if it generates 1000 different name/value pairs for your specified dimensions within a certain amount of time. This helps to prevent accidental high charges.</p> <p>You can also set up a billing alarm to alert you if your charges are higher than expected. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/monitor_estimated_charges_with_cloudwatch.html\"> Creating a Billing Alarm to Monitor Your Estimated Amazon Web Services Charges</a>. </p> </important>"}, "unit": {"shape": "StandardUnit", "documentation": "<p>The unit to assign to the metric. If you omit this, the unit is set as <code>None</code>.</p>"}}, "documentation": "<p>Indicates how to transform ingested log events to metric data in a CloudWatch metric.</p>"}, "MetricTransformations": {"type": "list", "member": {"shape": "MetricTransformation"}, "max": 1, "min": 1}, "MetricValue": {"type": "string", "documentation": "<p>The value to publish to the CloudWatch metric. For example, if you're counting the occurrences of a term like <code>Error</code>, the value is <code>1</code> for each occurrence. If you're counting the bytes transferred, the value is the value in the log event.</p>", "max": 100}, "MoveKeyEntries": {"type": "list", "member": {"shape": "MoveKeyEntry"}, "max": 5, "min": 1}, "MoveKeyEntry": {"type": "structure", "required": ["source", "target"], "members": {"source": {"shape": "Source", "documentation": "<p>The key to move.</p>"}, "target": {"shape": "Target", "documentation": "<p>The key to move to.</p>"}, "overwriteIfExists": {"shape": "OverwriteIfExists", "documentation": "<p>Specifies whether to overwrite the value if the destination key already exists. If you omit this, the default is <code>false</code>.</p>"}}, "documentation": "<p>This object defines one key that will be moved with the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-moveKey\"> moveKey</a> processor.</p>"}, "MoveKeys": {"type": "structure", "required": ["entries"], "members": {"entries": {"shape": "MoveKeyEntries", "documentation": "<p>An array of objects, where each object contains the information about one key to move. </p>"}}, "documentation": "<p>This processor moves a key from one field to another. The original key is deleted.</p> <p>For more information about this processor including examples, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-moveKeys\"> moveKeys</a> in the <i>CloudWatch Logs User Guide</i>.</p>"}, "NextToken": {"type": "string", "documentation": "<p>The token for the next set of items to return. The token expires after 24 hours.</p>", "min": 1}, "NonMatchValue": {"type": "string", "max": 128, "min": 1}, "OCSFVersion": {"type": "string", "enum": ["V1.1"]}, "OpenSearchApplication": {"type": "structure", "members": {"applicationEndpoint": {"shape": "OpenSearchApplicationEndpoint", "documentation": "<p>The endpoint of the application.</p>"}, "applicationArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the application.</p>"}, "applicationId": {"shape": "OpenSearchApplicationId", "documentation": "<p>The ID of the application.</p>"}, "status": {"shape": "OpenSearchResourceStatus", "documentation": "<p>This structure contains information about the status of this OpenSearch Service resource.</p>"}}, "documentation": "<p>This structure contains information about the OpenSearch Service application used for this integration. An OpenSearch Service application is the web application created by the integration with CloudWatch Logs. It hosts the vended logs dashboards.</p>"}, "OpenSearchApplicationEndpoint": {"type": "string", "max": 1024, "min": 1, "pattern": "^https://[\\.\\-_/#:A-Za-z0-9]+\\.com$"}, "OpenSearchApplicationId": {"type": "string", "max": 256, "min": 1, "pattern": "[\\.\\-_/#A-Za-z0-9]+"}, "OpenSearchCollection": {"type": "structure", "members": {"collectionEndpoint": {"shape": "OpenSearchCollectionEndpoint", "documentation": "<p>The endpoint of the collection.</p>"}, "collectionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the collection.</p>"}, "status": {"shape": "OpenSearchResourceStatus", "documentation": "<p>This structure contains information about the status of this OpenSearch Service resource.</p>"}}, "documentation": "<p>This structure contains information about the OpenSearch Service collection used for this integration. An OpenSearch Service collection is a logical grouping of one or more indexes that represent an analytics workload. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/serverless-collections.html\">Creating and managing OpenSearch Service Serverless collections</a>.</p>"}, "OpenSearchCollectionEndpoint": {"type": "string", "max": 1024, "min": 1, "pattern": "^https://[\\.\\-_/#:A-Za-z0-9]+\\.com$"}, "OpenSearchDataAccessPolicy": {"type": "structure", "members": {"policyName": {"shape": "OpenSearchPolicyName", "documentation": "<p>The name of the data access policy.</p>"}, "status": {"shape": "OpenSearchResourceStatus", "documentation": "<p>This structure contains information about the status of this OpenSearch Service resource.</p>"}}, "documentation": "<p>This structure contains information about the OpenSearch Service data access policy used for this integration. The access policy defines the access controls for the collection. This data access policy was automatically created as part of the integration setup. For more information about OpenSearch Service data access policies, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/serverless-data-access.html\">Data access control for Amazon OpenSearch Serverless</a> in the OpenSearch Service Developer Guide.</p>"}, "OpenSearchDataSource": {"type": "structure", "members": {"dataSourceName": {"shape": "OpenSearchDataSourceName", "documentation": "<p>The name of the OpenSearch Service data source.</p>"}, "status": {"shape": "OpenSearchResourceStatus", "documentation": "<p>This structure contains information about the status of this OpenSearch Service resource.</p>"}}, "documentation": "<p>This structure contains information about the OpenSearch Service data source used for this integration. This data source was created as part of the integration setup. An OpenSearch Service data source defines the source and destination for OpenSearch Service queries. It includes the role required to execute queries and write to collections.</p> <p>For more information about OpenSearch Service data sources , see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/direct-query-s3-creating.html\">Creating OpenSearch Service data source integrations with Amazon S3.</a> </p>"}, "OpenSearchDataSourceName": {"type": "string", "max": 256, "min": 1, "pattern": "[\\.\\-_/#A-Za-z0-9]+"}, "OpenSearchEncryptionPolicy": {"type": "structure", "members": {"policyName": {"shape": "OpenSearchPolicyName", "documentation": "<p>The name of the encryption policy.</p>"}, "status": {"shape": "OpenSearchResourceStatus", "documentation": "<p>This structure contains information about the status of this OpenSearch Service resource.</p>"}}, "documentation": "<p>This structure contains information about the OpenSearch Service encryption policy used for this integration. The encryption policy was created automatically when you created the integration. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/serverless-encryption.html#serverless-encryption-policies\">Encryption policies</a> in the OpenSearch Service Developer Guide. </p>"}, "OpenSearchIntegrationDetails": {"type": "structure", "members": {"dataSource": {"shape": "OpenSearchDataSource", "documentation": "<p>This structure contains information about the OpenSearch Service data source used for this integration. This data source was created as part of the integration setup. An OpenSearch Service data source defines the source and destination for OpenSearch Service queries. It includes the role required to execute queries and write to collections.</p> <p>For more information about OpenSearch Service data sources , see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/direct-query-s3-creating.html\">Creating OpenSearch Service data source integrations with Amazon S3.</a> </p>"}, "application": {"shape": "OpenSearchApplication", "documentation": "<p>This structure contains information about the OpenSearch Service application used for this integration. An OpenSearch Service application is the web application that was created by the integration with CloudWatch Logs. It hosts the vended logs dashboards.</p>"}, "collection": {"shape": "OpenSearchCollection", "documentation": "<p>This structure contains information about the OpenSearch Service collection used for this integration. This collection was created as part of the integration setup. An OpenSearch Service collection is a logical grouping of one or more indexes that represent an analytics workload. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/serverless-collections.html\">Creating and managing OpenSearch Service Serverless collections</a>.</p>"}, "workspace": {"shape": "OpenSearchWorkspace", "documentation": "<p>This structure contains information about the OpenSearch Service workspace used for this integration. An OpenSearch Service workspace is the collection of dashboards along with other OpenSearch Service tools. This workspace was created automatically as part of the integration setup. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/application.html\">Centralized OpenSearch user interface (Dashboards) with OpenSearch Service</a>.</p>"}, "encryptionPolicy": {"shape": "OpenSearchEncryptionPolicy", "documentation": "<p>This structure contains information about the OpenSearch Service encryption policy used for this integration. The encryption policy was created automatically when you created the integration. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/serverless-encryption.html#serverless-encryption-policies\">Encryption policies</a> in the OpenSearch Service Developer Guide. </p>"}, "networkPolicy": {"shape": "OpenSearchNetworkPolicy", "documentation": "<p>This structure contains information about the OpenSearch Service network policy used for this integration. The network policy assigns network access settings to collections. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/serverless-network.html#serverless-network-policies\">Network policies</a> in the OpenSearch Service Developer Guide.</p>"}, "accessPolicy": {"shape": "OpenSearchDataAccessPolicy", "documentation": "<p>This structure contains information about the OpenSearch Service data access policy used for this integration. The access policy defines the access controls for the collection. This data access policy was automatically created as part of the integration setup. For more information about OpenSearch Service data access policies, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/serverless-data-access.html\">Data access control for Amazon OpenSearch Serverless</a> in the OpenSearch Service Developer Guide.</p>"}, "lifecyclePolicy": {"shape": "OpenSearchLifecyclePolicy", "documentation": "<p>This structure contains information about the OpenSearch Service data lifecycle policy used for this integration. The lifecycle policy determines the lifespan of the data in the collection. It was automatically created as part of the integration setup.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/serverless-lifecycle.html\">Using data lifecycle policies with OpenSearch Service Serverless</a> in the OpenSearch Service Developer Guide.</p>"}}, "documentation": "<p>This structure contains complete information about one CloudWatch Logs integration. This structure is returned by a <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_GetIntegration.html\">GetIntegration</a> operation.</p>"}, "OpenSearchLifecyclePolicy": {"type": "structure", "members": {"policyName": {"shape": "OpenSearchPolicyName", "documentation": "<p>The name of the lifecycle policy.</p>"}, "status": {"shape": "OpenSearchResourceStatus", "documentation": "<p>This structure contains information about the status of this OpenSearch Service resource.</p>"}}, "documentation": "<p>This structure contains information about the OpenSearch Service data lifecycle policy used for this integration. The lifecycle policy determines the lifespan of the data in the collection. It was automatically created as part of the integration setup.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/serverless-lifecycle.html\">Using data lifecycle policies with OpenSearch Service Serverless</a> in the OpenSearch Service Developer Guide.</p>"}, "OpenSearchNetworkPolicy": {"type": "structure", "members": {"policyName": {"shape": "OpenSearchPolicyName", "documentation": "<p>The name of the network policy.</p>"}, "status": {"shape": "OpenSearchResourceStatus", "documentation": "<p>This structure contains information about the status of this OpenSearch Service resource.</p>"}}, "documentation": "<p>This structure contains information about the OpenSearch Service network policy used for this integration. The network policy assigns network access settings to collections. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/serverless-network.html#serverless-network-policies\">Network policies</a> in the OpenSearch Service Developer Guide.</p>"}, "OpenSearchPolicyName": {"type": "string", "max": 256, "min": 1, "pattern": "[\\.\\-_/#A-Za-z0-9]+"}, "OpenSearchResourceConfig": {"type": "structure", "required": ["dataSourceRoleArn", "dashboardViewerPrincipals", "retentionDays"], "members": {"kmsKeyArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>To have the vended dashboard data encrypted with KMS instead of the CloudWatch Logs default encryption method, specify the ARN of the KMS key that you want to use.</p>"}, "dataSourceRoleArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Specify the ARN of an IAM role that CloudWatch Logs will use to create the integration. This role must have the permissions necessary to access the OpenSearch Service collection to be able to create the dashboards. For more information about the permissions needed, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/OpenSearch-Dashboards-CreateRole.html\">Permissions that the integration needs</a> in the CloudWatch Logs User Guide.</p>"}, "dashboardViewerPrincipals": {"shape": "DashboardViewerPrincipals", "documentation": "<p>Specify the ARNs of IAM roles and IAM users who you want to grant permission to for viewing the dashboards.</p> <important> <p>In addition to specifying these users here, you must also grant them the <b>CloudWatchOpenSearchDashboardAccess</b> IAM policy. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/OpenSearch-Dashboards-UserRoles.html\">IAM policies for users</a>.</p> </important>"}, "applicationArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>If you want to use an existing OpenSearch Service application for your integration with OpenSearch Service, specify it here. If you omit this, a new application will be created.</p>"}, "retentionDays": {"shape": "CollectionRetentionDays", "documentation": "<p>Specify how many days that you want the data derived by OpenSearch Service to be retained in the index that the dashboard refers to. This also sets the maximum time period that you can choose when viewing data in the dashboard. Choosing a longer time frame will incur additional costs. </p>"}}, "documentation": "<p>This structure contains configuration details about an integration between CloudWatch Logs and OpenSearch Service.</p>"}, "OpenSearchResourceStatus": {"type": "structure", "members": {"status": {"shape": "OpenSearchResourceStatusType", "documentation": "<p>The current status of this resource.</p>"}, "statusMessage": {"shape": "IntegrationStatusMessage", "documentation": "<p>A message with additional information about the status of this resource.</p>"}}, "documentation": "<p>This structure contains information about the status of an OpenSearch Service resource.</p>"}, "OpenSearchResourceStatusType": {"type": "string", "enum": ["ACTIVE", "NOT_FOUND", "ERROR"]}, "OpenSearchWorkspace": {"type": "structure", "members": {"workspaceId": {"shape": "OpenSearchWorkspaceId", "documentation": "<p>The ID of this workspace.</p>"}, "status": {"shape": "OpenSearchResourceStatus", "documentation": "<p>This structure contains information about the status of an OpenSearch Service resource.</p>"}}, "documentation": "<p>This structure contains information about the OpenSearch Service workspace used for this integration. An OpenSearch Service workspace is the collection of dashboards along with other OpenSearch Service tools. This workspace was created automatically as part of the integration setup. For more information, see <a href=\"https://docs.aws.amazon.com/opensearch-service/latest/developerguide/application.html\">Centralized OpenSearch user interface (Dashboards) with OpenSearch Service</a>.</p>"}, "OpenSearchWorkspaceId": {"type": "string", "max": 256, "min": 1, "pattern": "[\\.\\-_/#A-Za-z0-9]+"}, "OperationAbortedException": {"type": "structure", "members": {}, "documentation": "<p>Multiple concurrent requests to update the same resource were in conflict.</p>", "exception": true}, "OrderBy": {"type": "string", "enum": ["LogStreamName", "LastEventTime"]}, "OutputFormat": {"type": "string", "enum": ["json", "plain", "w3c", "raw", "parquet"]}, "OutputFormats": {"type": "list", "member": {"shape": "OutputFormat"}}, "OutputLogEvent": {"type": "structure", "members": {"timestamp": {"shape": "Timestamp", "documentation": "<p>The time the event occurred, expressed as the number of milliseconds after <code>Jan 1, 1970 00:00:00 UTC</code>.</p>"}, "message": {"shape": "EventMessage", "documentation": "<p>The data contained in the log event.</p>"}, "ingestionTime": {"shape": "Timestamp", "documentation": "<p>The time the event was ingested, expressed as the number of milliseconds after <code>Jan 1, 1970 00:00:00 UTC</code>.</p>"}}, "documentation": "<p>Represents a log event.</p>"}, "OutputLogEvents": {"type": "list", "member": {"shape": "OutputLogEvent"}}, "OverwriteIfExists": {"type": "boolean"}, "ParseCloudfront": {"type": "structure", "members": {"source": {"shape": "Source", "documentation": "<p>Omit this parameter and the whole log message will be processed by this processor. No other value than <code>@message</code> is allowed for <code>source</code>.</p>"}}, "documentation": "<p>This processor parses CloudFront vended logs, extract fields, and convert them into JSON format. Encoded field values are decoded. Values that are integers and doubles are treated as such. For more information about this processor including examples, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-parseCloudfront\"> parseCloudfront</a> </p> <p>For more information about CloudFront log format, see <a href=\"https://docs.aws.amazon.com/AmazonCloudFront/latest/DeveloperGuide/AccessLogs.html\"> Configure and use standard logs (access logs)</a>.</p> <p>If you use this processor, it must be the first processor in your transformer.</p>"}, "ParseJSON": {"type": "structure", "members": {"source": {"shape": "Source", "documentation": "<p>Path to the field in the log event that will be parsed. Use dot notation to access child fields. For example, <code>store.book</code> </p>"}, "destination": {"shape": "DestinationField", "documentation": "<p>The location to put the parsed key value pair into. If you omit this parameter, it is placed under the root node.</p>"}}, "documentation": "<p>This processor parses log events that are in JSON format. It can extract JSON key-value pairs and place them under a destination that you specify.</p> <p>Additionally, because you must have at least one parse-type processor in a transformer, you can use <code>ParseJSON</code> as that processor for JSON-format logs, so that you can also apply other processors, such as mutate processors, to these logs.</p> <p>For more information about this processor including examples, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-parseJSON\"> parseJSON</a> in the <i>CloudWatch Logs User Guide</i>.</p>"}, "ParseKeyValue": {"type": "structure", "members": {"source": {"shape": "Source", "documentation": "<p>Path to the field in the log event that will be parsed. Use dot notation to access child fields. For example, <code>store.book</code> </p>"}, "destination": {"shape": "DestinationField", "documentation": "<p>The destination field to put the extracted key-value pairs into</p>"}, "fieldDelimiter": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>Delimite<PERSON>", "documentation": "<p>The field delimiter string that is used between key-value pairs in the original log events. If you omit this, the ampersand <code>&amp;</code> character is used.</p>"}, "keyValueDelimiter": {"shape": "KeyValueDelimiter", "documentation": "<p>The delimiter string to use between the key and value in each pair in the transformed log event.</p> <p> If you omit this, the equal <code>=</code> character is used.</p>"}, "keyPrefix": {"shape": "KeyPrefix", "documentation": "<p>If you want to add a prefix to all transformed keys, specify it here.</p>"}, "nonMatchValue": {"shape": "NonMatchValue", "documentation": "<p>A value to insert into the value field in the result, when a key-value pair is not successfully split.</p>"}, "overwriteIfExists": {"shape": "OverwriteIfExists", "documentation": "<p>Specifies whether to overwrite the value if the destination key already exists. If you omit this, the default is <code>false</code>.</p>"}}, "documentation": "<p>This processor parses a specified field in the original log event into key-value pairs. </p> <p>For more information about this processor including examples, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-parseKeyValue\"> parseKeyValue</a> in the <i>CloudWatch Logs User Guide</i>.</p>"}, "ParsePostgres": {"type": "structure", "members": {"source": {"shape": "Source", "documentation": "<p>Omit this parameter and the whole log message will be processed by this processor. No other value than <code>@message</code> is allowed for <code>source</code>.</p>"}}, "documentation": "<p>Use this processor to parse RDS for PostgreSQL vended logs, extract fields, and and convert them into a JSON format. This processor always processes the entire log event message. For more information about this processor including examples, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-parsePostGres\"> parsePostGres</a>.</p> <p>For more information about RDS for PostgreSQL log format, see <a href=\"https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/USER_LogAccess.Concepts.PostgreSQL.html#USER_LogAccess.Concepts.PostgreSQL.Log_Format.log-line-prefix\"> RDS for PostgreSQL database log filesTCP flag sequence</a>.</p> <important> <p>If you use this processor, it must be the first processor in your transformer.</p> </important>"}, "ParseRoute53": {"type": "structure", "members": {"source": {"shape": "Source", "documentation": "<p>Omit this parameter and the whole log message will be processed by this processor. No other value than <code>@message</code> is allowed for <code>source</code>.</p>"}}, "documentation": "<p>Use this processor to parse Route 53 vended logs, extract fields, and and convert them into a JSON format. This processor always processes the entire log event message. For more information about this processor including examples, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-parseRoute53\"> parseRoute53</a>.</p> <important> <p>If you use this processor, it must be the first processor in your transformer.</p> </important>"}, "ParseToOCSF": {"type": "structure", "required": ["eventSource", "ocsfVersion"], "members": {"source": {"shape": "Source", "documentation": "<p>The path to the field in the log event that you want to parse. If you omit this value, the whole log message is parsed.</p>"}, "eventSource": {"shape": "EventSource", "documentation": "<p>Specify the service or process that produces the log events that will be converted with this processor.</p>"}, "ocsfVersion": {"shape": "OCSFVersion", "documentation": "<p>Specify which version of the OCSF schema to use for the transformed log events.</p>"}}, "documentation": "<p>This processor converts logs into <a href=\"https://ocsf.io\">Open Cybersecurity Schema Framework (OCSF)</a> events.</p> <p>For more information about this processor including examples, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-parseToOCSF\"> parseToOSCF</a> in the <i>CloudWatch Logs User Guide</i>.</p>"}, "ParseVPC": {"type": "structure", "members": {"source": {"shape": "Source", "documentation": "<p>Omit this parameter and the whole log message will be processed by this processor. No other value than <code>@message</code> is allowed for <code>source</code>.</p>"}}, "documentation": "<p>Use this processor to parse Amazon VPC vended logs, extract fields, and and convert them into a JSON format. This processor always processes the entire log event message.</p> <p>This processor doesn't support custom log formats, such as NAT gateway logs. For more information about custom log formats in Amazon VPC, see <a href=\"https://docs.aws.amazon.com/vpc/latest/userguide/flow-logs-records-examples.html#flow-log-example-tcp-flag\"> parseVPC</a> For more information about this processor including examples, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-parseVPC\"> parseVPC</a>.</p> <important> <p>If you use this processor, it must be the first processor in your transformer.</p> </important>"}, "ParseWAF": {"type": "structure", "members": {"source": {"shape": "Source", "documentation": "<p>Omit this parameter and the whole log message will be processed by this processor. No other value than <code>@message</code> is allowed for <code>source</code>.</p>"}}, "documentation": "<p>Use this processor to parse WAF vended logs, extract fields, and and convert them into a JSON format. This processor always processes the entire log event message. For more information about this processor including examples, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-parsePostGres\"> parseWAF</a>.</p> <p>For more information about WAF log format, see <a href=\"https://docs.aws.amazon.com/waf/latest/developerguide/logging-examples.html\"> Log examples for web ACL traffic</a>.</p> <important> <p>If you use this processor, it must be the first processor in your transformer.</p> </important>"}, "ParserFieldDelimiter": {"type": "string", "max": 128, "min": 1}, "PatternId": {"type": "string", "max": 32, "min": 32}, "PatternRegex": {"type": "string", "min": 1}, "PatternString": {"type": "string", "min": 1}, "PatternToken": {"type": "structure", "members": {"dynamicTokenPosition": {"shape": "DynamicTokenPosition", "documentation": "<p>For a dynamic token, this indicates where in the pattern that this token appears, related to other dynamic tokens. The dynamic token that appears first has a value of <code>1</code>, the one that appears second is <code>2</code>, and so on.</p>"}, "isDynamic": {"shape": "Boolean", "documentation": "<p>Specifies whether this is a dynamic token.</p>"}, "tokenString": {"shape": "TokenString", "documentation": "<p>The string represented by this token. If this is a dynamic token, the value will be <code>&lt;*&gt;</code> </p>"}, "enumerations": {"shape": "Enumerations", "documentation": "<p>Contains the values found for a dynamic token, and the number of times each value was found.</p>"}, "inferredTokenName": {"shape": "InferredTokenName", "documentation": "<p>A name that CloudWatch Logs assigned to this dynamic token to make the pattern more readable. The string part of the <code>inferredTokenName</code> gives you a clearer idea of the content of this token. The number part of the <code>inferredTokenName</code> shows where in the pattern this token appears, compared to other dynamic tokens. CloudWatch Logs assigns the string part of the name based on analyzing the content of the log events that contain it.</p> <p>For example, an inferred token name of <code>IPAddress-3</code> means that the token represents an IP address, and this token is the third dynamic token in the pattern.</p>"}}, "documentation": "<p>A structure that contains information about one pattern token related to an anomaly.</p> <p>For more information about patterns and tokens, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_CreateLogAnomalyDetector.html\">CreateLogAnomalyDetector</a>. </p>"}, "PatternTokens": {"type": "list", "member": {"shape": "PatternToken"}}, "Percentage": {"type": "integer", "max": 100, "min": 0}, "Policy": {"type": "structure", "members": {"deliveryDestinationPolicy": {"shape": "DeliveryDestinationPolicy", "documentation": "<p>The contents of the delivery destination policy.</p>"}}, "documentation": "<p>A structure that contains information about one delivery destination policy.</p>"}, "PolicyDocument": {"type": "string", "max": 5120, "min": 1}, "PolicyName": {"type": "string"}, "PolicyType": {"type": "string", "enum": ["DATA_PROTECTION_POLICY", "SUBSCRIPTION_FILTER_POLICY", "FIELD_INDEX_POLICY", "TRANSFORMER_POLICY"]}, "Priority": {"type": "string", "min": 1}, "Processor": {"type": "structure", "members": {"addKeys": {"shape": "<PERSON>d<PERSON><PERSON><PERSON>", "documentation": "<p>Use this parameter to include the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-addKeys\"> addKeys</a> processor in your transformer.</p>"}, "copyValue": {"shape": "CopyValue", "documentation": "<p>Use this parameter to include the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-copyValue\"> copyValue</a> processor in your transformer.</p>"}, "csv": {"shape": "CSV", "documentation": "<p>Use this parameter to include the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-CSV\"> CSV</a> processor in your transformer.</p>"}, "dateTimeConverter": {"shape": "DateTimeConverter", "documentation": "<p>Use this parameter to include the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-datetimeConverter\"> datetimeConverter</a> processor in your transformer.</p>"}, "deleteKeys": {"shape": "DeleteKeys", "documentation": "<p>Use this parameter to include the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-deleteKeys\"> deleteKeys</a> processor in your transformer.</p>"}, "grok": {"shape": "Grok", "documentation": "<p>Use this parameter to include the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-grok\"> grok</a> processor in your transformer.</p>"}, "listToMap": {"shape": "ListToMap", "documentation": "<p>Use this parameter to include the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-listToMap\"> listToMap</a> processor in your transformer.</p>"}, "lowerCaseString": {"shape": "LowerCaseString", "documentation": "<p>Use this parameter to include the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-lowerCaseString\"> lowerCaseString</a> processor in your transformer.</p>"}, "moveKeys": {"shape": "MoveKeys", "documentation": "<p>Use this parameter to include the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-moveKeys\"> moveKeys</a> processor in your transformer.</p>"}, "parseCloudfront": {"shape": "ParseCloudfront", "documentation": "<p>Use this parameter to include the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-parseCloudfront\"> parseCloudfront</a> processor in your transformer.</p> <p>If you use this processor, it must be the first processor in your transformer.</p>"}, "parseJSON": {"shape": "ParseJSON", "documentation": "<p>Use this parameter to include the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-parseJSON\"> parseJSON</a> processor in your transformer.</p>"}, "parseKeyValue": {"shape": "ParseKeyValue", "documentation": "<p>Use this parameter to include the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-parseKeyValue\"> parseKeyValue</a> processor in your transformer.</p>"}, "parseRoute53": {"shape": "ParseRoute53", "documentation": "<p>Use this parameter to include the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-parseRoute53\"> parseRoute53</a> processor in your transformer.</p> <p>If you use this processor, it must be the first processor in your transformer.</p>"}, "parseToOCSF": {"shape": "ParseToOCSF", "documentation": "<p>Use this processor to convert logs into Open Cybersecurity Schema Framework (OCSF) format</p>"}, "parsePostgres": {"shape": "ParsePostgres", "documentation": "<p>Use this parameter to include the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-parsePostGres\"> parsePostGres</a> processor in your transformer.</p> <p>If you use this processor, it must be the first processor in your transformer.</p>"}, "parseVPC": {"shape": "ParseVPC", "documentation": "<p>Use this parameter to include the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-parseVPC\"> parseVPC</a> processor in your transformer.</p> <p>If you use this processor, it must be the first processor in your transformer.</p>"}, "parseWAF": {"shape": "ParseWAF", "documentation": "<p>Use this parameter to include the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-parseWAF\"> parseWAF</a> processor in your transformer.</p> <p>If you use this processor, it must be the first processor in your transformer.</p>"}, "renameKeys": {"shape": "Rename<PERSON><PERSON><PERSON>", "documentation": "<p>Use this parameter to include the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-renameKeys\"> renameKeys</a> processor in your transformer.</p>"}, "splitString": {"shape": "SplitString", "documentation": "<p>Use this parameter to include the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-splitString\"> splitString</a> processor in your transformer.</p>"}, "substituteString": {"shape": "SubstituteString", "documentation": "<p>Use this parameter to include the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-substituteString\"> substituteString</a> processor in your transformer.</p>"}, "trimString": {"shape": "TrimString", "documentation": "<p>Use this parameter to include the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-trimString\"> trimString</a> processor in your transformer.</p>"}, "typeConverter": {"shape": "TypeConverter", "documentation": "<p>Use this parameter to include the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-typeConverter\"> typeConverter</a> processor in your transformer.</p>"}, "upperCaseString": {"shape": "UpperCaseString", "documentation": "<p>Use this parameter to include the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-upperCaseString\"> upperCaseString</a> processor in your transformer.</p>"}}, "documentation": "<p>This structure contains the information about one processor in a log transformer.</p>"}, "Processors": {"type": "list", "member": {"shape": "Processor"}, "max": 20, "min": 1}, "PutAccountPolicyRequest": {"type": "structure", "required": ["policyName", "policyDocument", "policyType"], "members": {"policyName": {"shape": "PolicyName", "documentation": "<p>A name for the policy. This must be unique within the account.</p>"}, "policyDocument": {"shape": "AccountPolicyDocument", "documentation": "<p>Specify the policy, in JSON.</p> <p> <b>Data protection policy</b> </p> <p>A data protection policy must include two JSON blocks:</p> <ul> <li> <p>The first block must include both a <code>DataIdentifer</code> array and an <code>Operation</code> property with an <code>Audit</code> action. The <code>DataIdentifer</code> array lists the types of sensitive data that you want to mask. For more information about the available options, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/mask-sensitive-log-data-types.html\">Types of data that you can mask</a>.</p> <p>The <code>Operation</code> property with an <code>Audit</code> action is required to find the sensitive data terms. This <code>Audit</code> action must contain a <code>FindingsDestination</code> object. You can optionally use that <code>FindingsDestination</code> object to list one or more destinations to send audit findings to. If you specify destinations such as log groups, Firehose streams, and S3 buckets, they must already exist.</p> </li> <li> <p>The second block must include both a <code>DataIdentifer</code> array and an <code>Operation</code> property with an <code>Deidentify</code> action. The <code>DataIdentifer</code> array must exactly match the <code>DataIdentifer</code> array in the first block of the policy.</p> <p>The <code>Operation</code> property with the <code>Deidentify</code> action is what actually masks the data, and it must contain the <code> \"MaskConfig\": {}</code> object. The <code> \"MaskConfig\": {}</code> object must be empty.</p> </li> </ul> <p>For an example data protection policy, see the <b>Examples</b> section on this page.</p> <important> <p>The contents of the two <code>DataIdentifer</code> arrays must match exactly.</p> </important> <p>In addition to the two JSON blocks, the <code>policyDocument</code> can also include <code>Name</code>, <code>Description</code>, and <code>Version</code> fields. The <code>Name</code> is different than the operation's <code>policyName</code> parameter, and is used as a dimension when CloudWatch Logs reports audit findings metrics to CloudWatch.</p> <p>The JSON specified in <code>policyDocument</code> can be up to 30,720 characters long.</p> <p> <b>Subscription filter policy</b> </p> <p>A subscription filter policy can include the following attributes in a JSON block:</p> <ul> <li> <p> <b>DestinationArn</b> The ARN of the destination to deliver log events to. Supported destinations are:</p> <ul> <li> <p>An Kinesis Data Streams data stream in the same account as the subscription policy, for same-account delivery.</p> </li> <li> <p>An Firehose data stream in the same account as the subscription policy, for same-account delivery.</p> </li> <li> <p>A Lambda function in the same account as the subscription policy, for same-account delivery.</p> </li> <li> <p>A logical destination in a different account created with <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutDestination.html\">PutDestination</a>, for cross-account delivery. Kinesis Data Streams and Firehose are supported as logical destinations.</p> </li> </ul> </li> <li> <p> <b>RoleArn</b> The ARN of an IAM role that grants CloudWatch Logs permissions to deliver ingested log events to the destination stream. You don't need to provide the ARN when you are working with a logical destination for cross-account delivery.</p> </li> <li> <p> <b>FilterPattern</b> A filter pattern for subscribing to a filtered stream of log events.</p> </li> <li> <p> <b>Distribution</b> The method used to distribute log data to the destination. By default, log data is grouped by log stream, but the grouping can be set to <code>Random</code> for a more even distribution. This property is only applicable when the destination is an Kinesis Data Streams data stream.</p> </li> </ul> <p> <b>Transformer policy</b> </p> <p>A transformer policy must include one JSON block with the array of processors and their configurations. For more information about available processors, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-Processors\"> Processors that you can use</a>. </p> <p> <b>Field index policy</b> </p> <p>A field index filter policy can include the following attribute in a JSON block:</p> <ul> <li> <p> <b>Fields</b> The array of field indexes to create.</p> </li> </ul> <p>It must contain at least one field index.</p> <p>The following is an example of an index policy document that creates two indexes, <code>RequestId</code> and <code>TransactionId</code>.</p> <p> <code>\"policyDocument\": \"{ \\\"Fields\\\": [ \\\"RequestId\\\", \\\"TransactionId\\\" ] }\"</code> </p>"}, "policyType": {"shape": "PolicyType", "documentation": "<p>The type of policy that you're creating or updating.</p>"}, "scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>Currently the only valid value for this parameter is <code>ALL</code>, which specifies that the data protection policy applies to all log groups in the account. If you omit this parameter, the default of <code>ALL</code> is used.</p>"}, "selectionCriteria": {"shape": "SelectionCriteria", "documentation": "<p>Use this parameter to apply the new policy to a subset of log groups in the account.</p> <p>Specifing <code>selectionCriteria</code> is valid only when you specify <code>SUBSCRIPTION_FILTER_POLICY</code>, <code>FIELD_INDEX_POLICY</code> or <code>TRANSFORMER_POLICY</code>for <code>policyType</code>.</p> <p>If <code>policyType</code> is <code>SUBSCRIPTION_FILTER_POLICY</code>, the only supported <code>selectionCriteria</code> filter is <code>LogGroupName NOT IN []</code> </p> <p>If <code>policyType</code> is <code>FIELD_INDEX_POLICY</code> or <code>TRANSFORMER_POLICY</code>, the only supported <code>selectionCriteria</code> filter is <code>LogGroupNamePrefix</code> </p> <p>The <code>selectionCriteria</code> string can be up to 25KB in length. The length is determined by using its UTF-8 bytes.</p> <p>Using the <code>selectionCriteria</code> parameter with <code>SUBSCRIPTION_FILTER_POLICY</code> is useful to help prevent infinite loops. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/Subscriptions-recursion-prevention.html\">Log recursion prevention</a>.</p>"}}}, "PutAccountPolicyResponse": {"type": "structure", "members": {"accountPolicy": {"shape": "AccountPolicy", "documentation": "<p>The account policy that you created.</p>"}}}, "PutDataProtectionPolicyRequest": {"type": "structure", "required": ["logGroupIdentifier", "policyDocument"], "members": {"logGroupIdentifier": {"shape": "LogGroupIdentifier", "documentation": "<p>Specify either the log group name or log group ARN.</p>"}, "policyDocument": {"shape": "DataProtectionPolicyDocument", "documentation": "<p>Specify the data protection policy, in JSON.</p> <p>This policy must include two JSON blocks:</p> <ul> <li> <p>The first block must include both a <code>DataIdentifer</code> array and an <code>Operation</code> property with an <code>Audit</code> action. The <code>DataIdentifer</code> array lists the types of sensitive data that you want to mask. For more information about the available options, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/mask-sensitive-log-data-types.html\">Types of data that you can mask</a>.</p> <p>The <code>Operation</code> property with an <code>Audit</code> action is required to find the sensitive data terms. This <code>Audit</code> action must contain a <code>FindingsDestination</code> object. You can optionally use that <code>FindingsDestination</code> object to list one or more destinations to send audit findings to. If you specify destinations such as log groups, Firehose streams, and S3 buckets, they must already exist.</p> </li> <li> <p>The second block must include both a <code>DataIdentifer</code> array and an <code>Operation</code> property with an <code>Deidentify</code> action. The <code>DataIdentifer</code> array must exactly match the <code>DataIdentifer</code> array in the first block of the policy.</p> <p>The <code>Operation</code> property with the <code>Deidentify</code> action is what actually masks the data, and it must contain the <code> \"MaskConfig\": {}</code> object. The <code> \"MaskConfig\": {}</code> object must be empty.</p> </li> </ul> <p>For an example data protection policy, see the <b>Examples</b> section on this page.</p> <important> <p>The contents of the two <code>DataIdentifer</code> arrays must match exactly.</p> </important> <p>In addition to the two JSON blocks, the <code>policyDocument</code> can also include <code>Name</code>, <code>Description</code>, and <code>Version</code> fields. The <code>Name</code> is used as a dimension when CloudWatch Logs reports audit findings metrics to CloudWatch.</p> <p>The JSON specified in <code>policyDocument</code> can be up to 30,720 characters.</p>"}}}, "PutDataProtectionPolicyResponse": {"type": "structure", "members": {"logGroupIdentifier": {"shape": "LogGroupIdentifier", "documentation": "<p>The log group name or ARN that you specified in your request.</p>"}, "policyDocument": {"shape": "DataProtectionPolicyDocument", "documentation": "<p>The data protection policy used for this log group.</p>"}, "lastUpdatedTime": {"shape": "Timestamp", "documentation": "<p>The date and time that this policy was most recently updated.</p>"}}}, "PutDeliveryDestinationPolicyRequest": {"type": "structure", "required": ["deliveryDestinationName", "deliveryDestinationPolicy"], "members": {"deliveryDestinationName": {"shape": "DeliveryDestinationName", "documentation": "<p>The name of the delivery destination to assign this policy to.</p>"}, "deliveryDestinationPolicy": {"shape": "DeliveryDestinationPolicy", "documentation": "<p>The contents of the policy.</p>"}}}, "PutDeliveryDestinationPolicyResponse": {"type": "structure", "members": {"policy": {"shape": "Policy", "documentation": "<p>The contents of the policy that you just created.</p>"}}}, "PutDeliveryDestinationRequest": {"type": "structure", "required": ["name", "deliveryDestinationConfiguration"], "members": {"name": {"shape": "DeliveryDestinationName", "documentation": "<p>A name for this delivery destination. This name must be unique for all delivery destinations in your account.</p>"}, "outputFormat": {"shape": "OutputFormat", "documentation": "<p>The format for the logs that this delivery destination will receive.</p>"}, "deliveryDestinationConfiguration": {"shape": "DeliveryDestinationConfiguration", "documentation": "<p>A structure that contains the ARN of the Amazon Web Services resource that will receive the logs.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>An optional list of key-value pairs to associate with the resource.</p> <p>For more information about tagging, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws_tagging.html\">Tagging Amazon Web Services resources</a> </p>"}}}, "PutDeliveryDestinationResponse": {"type": "structure", "members": {"deliveryDestination": {"shape": "DeliveryDestination", "documentation": "<p>A structure containing information about the delivery destination that you just created or updated.</p>"}}}, "PutDeliverySourceRequest": {"type": "structure", "required": ["name", "resourceArn", "logType"], "members": {"name": {"shape": "DeliverySourceName", "documentation": "<p>A name for this delivery source. This name must be unique for all delivery sources in your account.</p>"}, "resourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the Amazon Web Services resource that is generating and sending logs. For example, <code>arn:aws:workmail:us-east-1:************:organization/m-1234EXAMPLEabcd1234abcd1234abcd1234</code> </p>"}, "logType": {"shape": "LogType", "documentation": "<p>Defines the type of log that the source is sending.</p> <ul> <li> <p>For Amazon Bedrock, the valid value is <code>APPLICATION_LOGS</code>.</p> </li> <li> <p>For CloudFront, the valid value is <code>ACCESS_LOGS</code>.</p> </li> <li> <p>For Amazon CodeWhisperer, the valid value is <code>EVENT_LOGS</code>.</p> </li> <li> <p>For Elemental MediaPackage, the valid values are <code>EGRESS_ACCESS_LOGS</code> and <code>INGRESS_ACCESS_LOGS</code>.</p> </li> <li> <p>For Elemental MediaTailor, the valid values are <code>AD_DECISION_SERVER_LOGS</code>, <code>MANIFEST_SERVICE_LOGS</code>, and <code>TRANSCODE_LOGS</code>.</p> </li> <li> <p>For Entity Resolution, the valid value is <code>WORKFLOW_LOGS</code>.</p> </li> <li> <p>For IAM Identity Center, the valid value is <code>ERROR_LOGS</code>.</p> </li> <li> <p>For Amazon Q, the valid value is <code>EVENT_LOGS</code>.</p> </li> <li> <p>For Amazon SES mail manager, the valid values are <code>APPLICATION_LOG</code> and <code>TRAFFIC_POLICY_DEBUG_LOGS</code>.</p> </li> <li> <p>For Amazon WorkMail, the valid values are <code>ACCESS_CONTROL_LOGS</code>, <code>AUTHENTICATION_LOGS</code>, <code>WORKMAIL_AVAILABILITY_PROVIDER_LOGS</code>, <code>WORKMAIL_MAILBOX_ACCESS_LOGS</code>, and <code>WORKMAIL_PERSONAL_ACCESS_TOKEN_LOGS</code>.</p> </li> </ul>"}, "tags": {"shape": "Tags", "documentation": "<p>An optional list of key-value pairs to associate with the resource.</p> <p>For more information about tagging, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws_tagging.html\">Tagging Amazon Web Services resources</a> </p>"}}}, "PutDeliverySourceResponse": {"type": "structure", "members": {"deliverySource": {"shape": "DeliverySource", "documentation": "<p>A structure containing information about the delivery source that was just created or updated.</p>"}}}, "PutDestinationPolicyRequest": {"type": "structure", "required": ["destinationName", "accessPolicy"], "members": {"destinationName": {"shape": "DestinationName", "documentation": "<p>A name for an existing destination.</p>"}, "accessPolicy": {"shape": "AccessPolicy", "documentation": "<p>An IAM policy document that authorizes cross-account users to deliver their log events to the associated destination. This can be up to 5120 bytes.</p>"}, "forceUpdate": {"shape": "ForceUpdate", "documentation": "<p>Specify true if you are updating an existing destination policy to grant permission to an organization ID instead of granting permission to individual Amazon Web Services accounts. Before you update a destination policy this way, you must first update the subscription filters in the accounts that send logs to this destination. If you do not, the subscription filters might stop working. By specifying <code>true</code> for <code>forceUpdate</code>, you are affirming that you have already updated the subscription filters. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/Cross-Account-Log_Subscription-Update.html\"> Updating an existing cross-account subscription</a> </p> <p>If you omit this parameter, the default of <code>false</code> is used.</p>"}}}, "PutDestinationRequest": {"type": "structure", "required": ["destinationName", "targetArn", "roleArn"], "members": {"destinationName": {"shape": "DestinationName", "documentation": "<p>A name for the destination.</p>"}, "targetArn": {"shape": "TargetArn", "documentation": "<p>The ARN of an Amazon Kinesis stream to which to deliver matching log events.</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The ARN of an IAM role that grants CloudWatch Logs permissions to call the Amazon Kinesis <code>PutRecord</code> operation on the destination stream.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>An optional list of key-value pairs to associate with the resource.</p> <p>For more information about tagging, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws_tagging.html\">Tagging Amazon Web Services resources</a> </p>"}}}, "PutDestinationResponse": {"type": "structure", "members": {"destination": {"shape": "Destination", "documentation": "<p>The destination.</p>"}}}, "PutIndexPolicyRequest": {"type": "structure", "required": ["logGroupIdentifier", "policyDocument"], "members": {"logGroupIdentifier": {"shape": "LogGroupIdentifier", "documentation": "<p>Specify either the log group name or log group ARN to apply this field index policy to. If you specify an ARN, use the format arn:aws:logs:<i>region</i>:<i>account-id</i>:log-group:<i>log_group_name</i> Don't include an * at the end.</p>"}, "policyDocument": {"shape": "PolicyDocument", "documentation": "<p>The index policy document, in JSON format. The following is an example of an index policy document that creates two indexes, <code>RequestId</code> and <code>TransactionId</code>.</p> <p> <code>\"policyDocument\": \"{ \"Fields\": [ \"RequestId\", \"TransactionId\" ] }\"</code> </p> <p>The policy document must include at least one field index. For more information about the fields that can be included and other restrictions, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatchLogs-Field-Indexing-Syntax.html\">Field index syntax and quotas</a>.</p>"}}}, "PutIndexPolicyResponse": {"type": "structure", "members": {"indexPolicy": {"shape": "IndexPolicy", "documentation": "<p>The index policy that you just created or updated.</p>"}}}, "PutIntegrationRequest": {"type": "structure", "required": ["integrationName", "resourceConfig", "integrationType"], "members": {"integrationName": {"shape": "IntegrationName", "documentation": "<p>A name for the integration.</p>"}, "resourceConfig": {"shape": "ResourceConfig", "documentation": "<p>A structure that contains configuration information for the integration that you are creating.</p>"}, "integrationType": {"shape": "IntegrationType", "documentation": "<p>The type of integration. Currently, the only supported type is <code>OPENSEARCH</code>.</p>"}}}, "PutIntegrationResponse": {"type": "structure", "members": {"integrationName": {"shape": "IntegrationName", "documentation": "<p>The name of the integration that you just created.</p>"}, "integrationStatus": {"shape": "IntegrationStatus", "documentation": "<p>The status of the integration that you just created.</p> <p>After you create an integration, it takes a few minutes to complete. During this time, you'll see the status as <code>PROVISIONING</code>.</p>"}}}, "PutLogEventsRequest": {"type": "structure", "required": ["logGroupName", "logStreamName", "logEvents"], "members": {"logGroupName": {"shape": "LogGroupName", "documentation": "<p>The name of the log group.</p>"}, "logStreamName": {"shape": "LogStreamName", "documentation": "<p>The name of the log stream.</p>"}, "logEvents": {"shape": "InputLogEvents", "documentation": "<p>The log events.</p>"}, "sequenceToken": {"shape": "SequenceToken", "documentation": "<p>The sequence token obtained from the response of the previous <code>PutLogEvents</code> call.</p> <important> <p>The <code>sequenceToken</code> parameter is now ignored in <code>PutLogEvents</code> actions. <code>PutLogEvents</code> actions are now accepted and never return <code>InvalidSequenceTokenException</code> or <code>DataAlreadyAcceptedException</code> even if the sequence token is not valid.</p> </important>"}, "entity": {"shape": "Entity", "documentation": "<p>The entity associated with the log events.</p>"}}}, "PutLogEventsResponse": {"type": "structure", "members": {"nextSequenceToken": {"shape": "SequenceToken", "documentation": "<p>The next sequence token.</p> <important> <p>This field has been deprecated.</p> <p>The sequence token is now ignored in <code>PutLogEvents</code> actions. <code>PutLogEvents</code> actions are always accepted even if the sequence token is not valid. You can use parallel <code>PutLogEvents</code> actions on the same log stream and you do not need to wait for the response of a previous <code>PutLogEvents</code> action to obtain the <code>nextSequenceToken</code> value.</p> </important>"}, "rejectedLogEventsInfo": {"shape": "RejectedLogEventsInfo", "documentation": "<p>The rejected events.</p>"}, "rejectedEntityInfo": {"shape": "RejectedEntityInfo", "documentation": "<p>Information about why the entity is rejected when calling <code>PutLogEvents</code>. Only returned when the entity is rejected.</p> <note> <p>When the entity is rejected, the events may still be accepted.</p> </note>"}}}, "PutMetricFilterRequest": {"type": "structure", "required": ["logGroupName", "filterName", "filterPattern", "metricTransformations"], "members": {"logGroupName": {"shape": "LogGroupName", "documentation": "<p>The name of the log group.</p>"}, "filterName": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>A name for the metric filter.</p>"}, "filterPattern": {"shape": "FilterPattern", "documentation": "<p>A filter pattern for extracting metric data out of ingested log events.</p>"}, "metricTransformations": {"shape": "MetricTransformations", "documentation": "<p>A collection of information that defines how metric data gets emitted.</p>"}, "applyOnTransformedLogs": {"shape": "ApplyOnTransformedLogs", "documentation": "<p>This parameter is valid only for log groups that have an active log transformer. For more information about log transformers, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutTransformer.html\">PutTransformer</a>.</p> <p>If the log group uses either a log-group level or account-level transformer, and you specify <code>true</code>, the metric filter will be applied on the transformed version of the log events instead of the original ingested log events.</p>"}}}, "PutQueryDefinitionRequest": {"type": "structure", "required": ["name", "queryString"], "members": {"queryLanguage": {"shape": "QueryLanguage", "documentation": "<p>Specify the query language to use for this query. The options are Logs Insights QL, OpenSearch PPL, and OpenSearch SQL. For more information about the query languages that CloudWatch Logs supports, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CWL_AnalyzeLogData_Languages.html\">Supported query languages</a>.</p>"}, "name": {"shape": "QueryDefinitionName", "documentation": "<p>A name for the query definition. If you are saving numerous query definitions, we recommend that you name them. This way, you can find the ones you want by using the first part of the name as a filter in the <code>queryDefinitionNamePrefix</code> parameter of <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_DescribeQueryDefinitions.html\">DescribeQueryDefinitions</a>.</p>"}, "queryDefinitionId": {"shape": "QueryId", "documentation": "<p>If you are updating a query definition, use this parameter to specify the ID of the query definition that you want to update. You can use <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_DescribeQueryDefinitions.html\">DescribeQueryDefinitions</a> to retrieve the IDs of your saved query definitions.</p> <p>If you are creating a query definition, do not specify this parameter. CloudWatch generates a unique ID for the new query definition and include it in the response to this operation.</p>"}, "logGroupNames": {"shape": "LogGroupNames", "documentation": "<p>Use this parameter to include specific log groups as part of your query definition. If your query uses the OpenSearch Service query language, you specify the log group names inside the <code>querystring</code> instead of here.</p> <p>If you are updating an existing query definition for the Logs Insights QL or OpenSearch Service PPL and you omit this parameter, then the updated definition will contain no log groups.</p>"}, "queryString": {"shape": "QueryDefinitionString", "documentation": "<p>The query string to use for this definition. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CWL_QuerySyntax.html\">CloudWatch Logs Insights Query Syntax</a>.</p>"}, "clientToken": {"shape": "ClientToken", "documentation": "<p>Used as an idempotency token, to avoid returning an exception if the service receives the same request twice because of a network error.</p>", "idempotencyToken": true}}}, "PutQueryDefinitionResponse": {"type": "structure", "members": {"queryDefinitionId": {"shape": "QueryId", "documentation": "<p>The ID of the query definition.</p>"}}}, "PutResourcePolicyRequest": {"type": "structure", "members": {"policyName": {"shape": "PolicyName", "documentation": "<p>Name of the new policy. This parameter is required.</p>"}, "policyDocument": {"shape": "PolicyDocument", "documentation": "<p>Details of the new policy, including the identity of the principal that is enabled to put logs to this account. This is formatted as a JSON string. This parameter is required.</p> <p>The following example creates a resource policy enabling the Route 53 service to put DNS query logs in to the specified log group. Replace <code>\"logArn\"</code> with the ARN of your CloudWatch Logs resource, such as a log group or log stream.</p> <p>CloudWatch Logs also supports <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/reference_policies_condition-keys.html#condition-keys-sourcearn\">aws:SourceArn</a> and <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/reference_policies_condition-keys.html#condition-keys-sourceaccount\">aws:SourceAccount</a> condition context keys.</p> <p>In the example resource policy, you would replace the value of <code>SourceArn</code> with the resource making the call from Route 53 to CloudWatch Logs. You would also replace the value of <code>SourceAccount</code> with the Amazon Web Services account ID making that call.</p> <p/> <p> <code>{ \"Version\": \"2012-10-17\", \"Statement\": [ { \"Sid\": \"Route53LogsToCloudWatchLogs\", \"Effect\": \"Allow\", \"Principal\": { \"Service\": [ \"route53.amazonaws.com\" ] }, \"Action\": \"logs:PutLogEvents\", \"Resource\": \"logArn\", \"Condition\": { \"ArnLike\": { \"aws:SourceArn\": \"myRoute53ResourceArn\" }, \"StringEquals\": { \"aws:SourceAccount\": \"myAwsAccountId\" } } } ] }</code> </p>"}}}, "PutResourcePolicyResponse": {"type": "structure", "members": {"resourcePolicy": {"shape": "ResourcePolicy", "documentation": "<p>The new policy.</p>"}}}, "PutRetentionPolicyRequest": {"type": "structure", "required": ["logGroupName", "retentionInDays"], "members": {"logGroupName": {"shape": "LogGroupName", "documentation": "<p>The name of the log group.</p>"}, "retentionInDays": {"shape": "Days"}}}, "PutSubscriptionFilterRequest": {"type": "structure", "required": ["logGroupName", "filterName", "filterPattern", "destinationArn"], "members": {"logGroupName": {"shape": "LogGroupName", "documentation": "<p>The name of the log group.</p>"}, "filterName": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>A name for the subscription filter. If you are updating an existing filter, you must specify the correct name in <code>filterName</code>. To find the name of the filter currently associated with a log group, use <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_DescribeSubscriptionFilters.html\">DescribeSubscriptionFilters</a>.</p>"}, "filterPattern": {"shape": "FilterPattern", "documentation": "<p>A filter pattern for subscribing to a filtered stream of log events.</p>"}, "destinationArn": {"shape": "DestinationArn", "documentation": "<p>The ARN of the destination to deliver matching log events to. Currently, the supported destinations are:</p> <ul> <li> <p>An Amazon Kinesis stream belonging to the same account as the subscription filter, for same-account delivery.</p> </li> <li> <p>A logical destination (specified using an ARN) belonging to a different account, for cross-account delivery.</p> <p>If you're setting up a cross-account subscription, the destination must have an IAM policy associated with it. The IAM policy must allow the sender to send logs to the destination. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutDestinationPolicy.html\">PutDestinationPolicy</a>.</p> </li> <li> <p>A Kinesis Data Firehose delivery stream belonging to the same account as the subscription filter, for same-account delivery.</p> </li> <li> <p>A Lambda function belonging to the same account as the subscription filter, for same-account delivery.</p> </li> </ul>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The ARN of an IAM role that grants CloudWatch Logs permissions to deliver ingested log events to the destination stream. You don't need to provide the ARN when you are working with a logical destination for cross-account delivery.</p>"}, "distribution": {"shape": "Distribution", "documentation": "<p>The method used to distribute log data to the destination. By default, log data is grouped by log stream, but the grouping can be set to random for a more even distribution. This property is only applicable when the destination is an Amazon Kinesis data stream. </p>"}, "applyOnTransformedLogs": {"shape": "ApplyOnTransformedLogs", "documentation": "<p>This parameter is valid only for log groups that have an active log transformer. For more information about log transformers, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutTransformer.html\">PutTransformer</a>.</p> <p>If the log group uses either a log-group level or account-level transformer, and you specify <code>true</code>, the subscription filter will be applied on the transformed version of the log events instead of the original ingested log events.</p>"}}}, "PutTransformerRequest": {"type": "structure", "required": ["logGroupIdentifier", "transformerConfig"], "members": {"logGroupIdentifier": {"shape": "LogGroupIdentifier", "documentation": "<p>Specify either the name or ARN of the log group to create the transformer for. </p>"}, "transformerConfig": {"shape": "Processors", "documentation": "<p>This structure contains the configuration of this log transformer. A log transformer is an array of processors, where each processor applies one type of transformation to the log events that are ingested.</p>"}}}, "QueryCharOffset": {"type": "integer"}, "QueryCompileError": {"type": "structure", "members": {"location": {"shape": "QueryCompileErrorLocation", "documentation": "<p>Reserved.</p>"}, "message": {"shape": "Message", "documentation": "<p>Reserved.</p>"}}, "documentation": "<p>Reserved.</p>"}, "QueryCompileErrorLocation": {"type": "structure", "members": {"startCharOffset": {"shape": "QueryCharOffset", "documentation": "<p>Reserved.</p>"}, "endCharOffset": {"shape": "QueryCharOffset", "documentation": "<p>Reserved.</p>"}}, "documentation": "<p>Reserved.</p>"}, "QueryDefinition": {"type": "structure", "members": {"queryLanguage": {"shape": "QueryLanguage", "documentation": "<p>The query language used for this query. For more information about the query languages that CloudWatch Logs supports, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CWL_AnalyzeLogData_Languages.html\">Supported query languages</a>.</p>"}, "queryDefinitionId": {"shape": "QueryId", "documentation": "<p>The unique ID of the query definition.</p>"}, "name": {"shape": "QueryDefinitionName", "documentation": "<p>The name of the query definition.</p>"}, "queryString": {"shape": "QueryDefinitionString", "documentation": "<p>The query string to use for this definition. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CWL_QuerySyntax.html\">CloudWatch Logs Insights Query Syntax</a>.</p>"}, "lastModified": {"shape": "Timestamp", "documentation": "<p>The date that the query definition was most recently modified.</p>"}, "logGroupNames": {"shape": "LogGroupNames", "documentation": "<p>If this query definition contains a list of log groups that it is limited to, that list appears here.</p>"}}, "documentation": "<p>This structure contains details about a saved CloudWatch Logs Insights query definition.</p>"}, "QueryDefinitionList": {"type": "list", "member": {"shape": "QueryDefinition"}}, "QueryDefinitionName": {"type": "string", "max": 255, "min": 1}, "QueryDefinitionString": {"type": "string", "max": 10000, "min": 1}, "QueryId": {"type": "string", "max": 256, "min": 1}, "QueryInfo": {"type": "structure", "members": {"queryLanguage": {"shape": "QueryLanguage", "documentation": "<p>The query language used for this query. For more information about the query languages that CloudWatch Logs supports, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CWL_AnalyzeLogData_Languages.html\">Supported query languages</a>.</p>"}, "queryId": {"shape": "QueryId", "documentation": "<p>The unique ID number of this query.</p>"}, "queryString": {"shape": "QueryString", "documentation": "<p>The query string used in this query.</p>"}, "status": {"shape": "QueryStatus", "documentation": "<p>The status of this query. Possible values are <code>Cancelled</code>, <code>Complete</code>, <code>Failed</code>, <code>Running</code>, <code>Scheduled</code>, and <code>Unknown</code>.</p>"}, "createTime": {"shape": "Timestamp", "documentation": "<p>The date and time that this query was created.</p>"}, "logGroupName": {"shape": "LogGroupName", "documentation": "<p>The name of the log group scanned by this query.</p>"}}, "documentation": "<p>Information about one CloudWatch Logs Insights query that matches the request in a <code>DescribeQueries</code> operation. </p>"}, "QueryInfoList": {"type": "list", "member": {"shape": "QueryInfo"}}, "QueryLanguage": {"type": "string", "enum": ["CWLI", "SQL", "PPL"]}, "QueryListMaxResults": {"type": "integer", "max": 1000, "min": 1}, "QueryResults": {"type": "list", "member": {"shape": "ResultRows"}}, "QueryStatistics": {"type": "structure", "members": {"recordsMatched": {"shape": "StatsValue", "documentation": "<p>The number of log events that matched the query string.</p>"}, "recordsScanned": {"shape": "StatsValue", "documentation": "<p>The total number of log events scanned during the query.</p>"}, "estimatedRecordsSkipped": {"shape": "StatsValue", "documentation": "<p>An estimate of the number of log events that were skipped when processing this query, because the query contained an indexed field. Skipping these entries lowers query costs and improves the query performance time. For more information about field indexes, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutIndexPolicy.html\">PutIndexPolicy</a>.</p>"}, "bytesScanned": {"shape": "StatsValue", "documentation": "<p>The total number of bytes in the log events scanned during the query.</p>"}, "estimatedBytesSkipped": {"shape": "StatsValue", "documentation": "<p>An estimate of the number of bytes in the log events that were skipped when processing this query, because the query contained an indexed field. Skipping these entries lowers query costs and improves the query performance time. For more information about field indexes, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutIndexPolicy.html\">PutIndexPolicy</a>.</p>"}, "logGroupsScanned": {"shape": "StatsValue", "documentation": "<p>The number of log groups that were scanned by this query.</p>"}}, "documentation": "<p>Contains the number of log events scanned by the query, the number of log events that matched the query criteria, and the total number of bytes in the log events that were scanned.</p> <p>If the query involved log groups that have field index policies, the estimated number of skipped log events and the total bytes of those skipped log events are included. Using field indexes to skip log events in queries reduces scan volume and improves performance. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatchLogs-Field-Indexing.html\">Create field indexes to improve query performance and reduce scan volume</a>.</p>"}, "QueryStatus": {"type": "string", "enum": ["Scheduled", "Running", "Complete", "Failed", "Cancelled", "Timeout", "Unknown"]}, "QueryString": {"type": "string", "max": 10000, "min": 0}, "QuoteCharacter": {"type": "string", "max": 1, "min": 1}, "RecordField": {"type": "structure", "members": {"name": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The name to use when specifying this record field in a <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_CreateDelivery.html\">CreateDelivery</a> or <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_UpdateDeliveryConfiguration.html\">UpdateDeliveryConfiguration</a> operation. </p>"}, "mandatory": {"shape": "Boolean", "documentation": "<p>If this is <code>true</code>, the record field must be present in the <code>recordFields</code> parameter provided to a <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_CreateDelivery.html\">CreateDelivery</a> or <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_UpdateDeliveryConfiguration.html\">UpdateDeliveryConfiguration</a> operation.</p>"}}, "documentation": "<p>A structure that represents a valid record field header and whether it is mandatory.</p>"}, "RecordFields": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON><PERSON>"}, "max": 128, "min": 0}, "RejectedEntityInfo": {"type": "structure", "required": ["errorType"], "members": {"errorType": {"shape": "EntityRejectionErrorType", "documentation": "<p>The type of error that caused the rejection of the entity when calling <code>PutLogEvents</code>.</p>"}}, "documentation": "<p>If an entity is rejected when a <code>PutLogEvents</code> request was made, this includes details about the reason for the rejection.</p>"}, "RejectedLogEventsInfo": {"type": "structure", "members": {"tooNewLogEventStartIndex": {"shape": "LogEventIndex", "documentation": "<p>The index of the first log event that is too new. This field is inclusive.</p>"}, "tooOldLogEventEndIndex": {"shape": "LogEventIndex", "documentation": "<p>The index of the last log event that is too old. This field is exclusive.</p>"}, "expiredLogEventEndIndex": {"shape": "LogEventIndex", "documentation": "<p>The expired log events.</p>"}}, "documentation": "<p>Represents the rejected events.</p>"}, "RenameKeyEntries": {"type": "list", "member": {"shape": "RenameKeyEntry"}, "max": 5, "min": 1}, "RenameKeyEntry": {"type": "structure", "required": ["key", "renameTo"], "members": {"key": {"shape": "Key", "documentation": "<p>The key to rename</p>"}, "renameTo": {"shape": "RenameTo", "documentation": "<p>The string to use for the new key name</p>"}, "overwriteIfExists": {"shape": "OverwriteIfExists", "documentation": "<p>Specifies whether to overwrite the existing value if the destination key already exists. The default is <code>false</code> </p>"}}, "documentation": "<p>This object defines one key that will be renamed with the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-renameKey\"> renameKey</a> processor.</p>"}, "RenameKeys": {"type": "structure", "required": ["entries"], "members": {"entries": {"shape": "RenameKeyEntries", "documentation": "<p>An array of <code>RenameKeyEntry</code> objects, where each object contains the information about a single key to rename. </p>"}}, "documentation": "<p>Use this processor to rename keys in a log event.</p> <p>For more information about this processor including examples, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-renameKeys\"> renameKeys</a> in the <i>CloudWatch Logs User Guide</i>.</p>"}, "RenameTo": {"type": "string", "max": 128, "min": 1}, "RequestId": {"type": "string", "max": 256, "min": 0}, "ResourceAlreadyExistsException": {"type": "structure", "members": {}, "documentation": "<p>The specified resource already exists.</p>", "exception": true}, "ResourceArns": {"type": "list", "member": {"shape": "<PERSON><PERSON>"}}, "ResourceConfig": {"type": "structure", "members": {"openSearchResourceConfig": {"shape": "OpenSearchResourceConfig", "documentation": "<p>This structure contains configuration details about an integration between CloudWatch Logs and OpenSearch Service.</p>"}}, "documentation": "<p>This structure contains configuration details about an integration between CloudWatch Logs and another entity.</p>", "union": true}, "ResourceIdentifier": {"type": "string", "max": 2048, "min": 1, "pattern": "[\\w+=/:,.@\\-\\*]*"}, "ResourceNotFoundException": {"type": "structure", "members": {}, "documentation": "<p>The specified resource does not exist.</p>", "exception": true}, "ResourcePolicies": {"type": "list", "member": {"shape": "ResourcePolicy"}}, "ResourcePolicy": {"type": "structure", "members": {"policyName": {"shape": "PolicyName", "documentation": "<p>The name of the resource policy.</p>"}, "policyDocument": {"shape": "PolicyDocument", "documentation": "<p>The details of the policy.</p>"}, "lastUpdatedTime": {"shape": "Timestamp", "documentation": "<p>Timestamp showing when this policy was last updated, expressed as the number of milliseconds after <code>Jan 1, 1970 00:00:00 UTC</code>.</p>"}}, "documentation": "<p>A policy enabling one or more entities to put logs to a log group in this account.</p>"}, "ResourceType": {"type": "string", "max": 255, "min": 1, "pattern": "[\\w-_]*"}, "ResourceTypes": {"type": "list", "member": {"shape": "ResourceType"}, "max": 10, "min": 1}, "ResultField": {"type": "structure", "members": {"field": {"shape": "Field", "documentation": "<p>The log event field.</p>"}, "value": {"shape": "Value", "documentation": "<p>The value of this field.</p>"}}, "documentation": "<p>Contains one field from one log event returned by a CloudWatch Logs Insights query, along with the value of that field.</p> <p>For more information about the fields that are generated by CloudWatch logs, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CWL_AnalyzeLogData-discoverable-fields.html\">Supported Logs and Discovered Fields</a>.</p>"}, "ResultRows": {"type": "list", "member": {"shape": "ResultField"}}, "RoleArn": {"type": "string", "min": 1}, "S3DeliveryConfiguration": {"type": "structure", "members": {"suffixPath": {"shape": "DeliverySuffixPath", "documentation": "<p>This string allows re-configuring the S3 object prefix to contain either static or variable sections. The valid variables to use in the suffix path will vary by each log source. To find the values supported for the suffix path for each log source, use the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_DescribeConfigurationTemplates.html\">DescribeConfigurationTemplates</a> operation and check the <code>allowedSuffixPathFields</code> field in the response.</p>"}, "enableHiveCompatiblePath": {"shape": "Boolean", "documentation": "<p>This parameter causes the S3 objects that contain delivered logs to use a prefix structure that allows for integration with Apache Hive.</p>", "box": true}}, "documentation": "<p>This structure contains delivery configurations that apply only when the delivery destination resource is an S3 bucket.</p>"}, "Scope": {"type": "string", "enum": ["ALL"]}, "SearchedLogStream": {"type": "structure", "members": {"logStreamName": {"shape": "LogStreamName", "documentation": "<p>The name of the log stream.</p>"}, "searchedCompletely": {"shape": "LogStreamSearchedCompletely", "documentation": "<p>Indicates whether all the events in this log stream were searched.</p>"}}, "documentation": "<p>Represents the search status of a log stream.</p>"}, "SearchedLogStreams": {"type": "list", "member": {"shape": "SearchedLogStream"}}, "SelectionCriteria": {"type": "string"}, "SequenceToken": {"type": "string", "min": 1}, "Service": {"type": "string", "max": 255, "min": 1, "pattern": "[\\w_-]*"}, "ServiceQuotaExceededException": {"type": "structure", "members": {}, "documentation": "<p>This request exceeds a service quota.</p>", "exception": true}, "ServiceUnavailableException": {"type": "structure", "members": {}, "documentation": "<p>The service cannot complete the request.</p>", "exception": true, "fault": true}, "SessionId": {"type": "string", "max": 256, "min": 0}, "SessionStreamingException": {"type": "structure", "members": {"message": {"shape": "Message"}}, "documentation": "<p>This exception is returned if an unknown error occurs during a Live Tail session.</p>", "exception": true}, "SessionTimeoutException": {"type": "structure", "members": {"message": {"shape": "Message"}}, "documentation": "<p>This exception is returned in a Live Tail stream when the Live Tail session times out. Live Tail sessions time out after three hours.</p>", "exception": true}, "Source": {"type": "string", "max": 128, "min": 1}, "SourceTimezone": {"type": "string", "min": 1}, "SplitString": {"type": "structure", "required": ["entries"], "members": {"entries": {"shape": "SplitStringEntries", "documentation": "<p>An array of <code>SplitStringEntry</code> objects, where each object contains the information about one field to split. </p>"}}, "documentation": "<p>Use this processor to split a field into an array of strings using a delimiting character.</p> <p>For more information about this processor including examples, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-splitString\"> splitString</a> in the <i>CloudWatch Logs User Guide</i>.</p>"}, "SplitStringDelimiter": {"type": "string", "max": 128, "min": 1}, "SplitStringEntries": {"type": "list", "member": {"shape": "SplitStringEntry"}, "max": 10, "min": 1}, "SplitStringEntry": {"type": "structure", "required": ["source", "delimiter"], "members": {"source": {"shape": "Source", "documentation": "<p>The key of the field to split.</p>"}, "delimiter": {"shape": "SplitStringDelimiter", "documentation": "<p>The separator characters to split the string entry on.</p>"}}, "documentation": "<p>This object defines one log field that will be split with the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-splitString\"> splitString</a> processor.</p>"}, "StandardUnit": {"type": "string", "enum": ["Seconds", "Microseconds", "Milliseconds", "Bytes", "Kilobytes", "Megabytes", "Gigabytes", "Terabytes", "Bits", "Kilobits", "Megabits", "Gigabits", "Terabits", "Percent", "Count", "Bytes/Second", "Kilobytes/Second", "Megabytes/Second", "Gigabytes/Second", "Terabytes/Second", "Bits/Second", "Kilobits/Second", "Megabits/Second", "Gigabits/Second", "Terabits/Second", "Count/Second", "None"]}, "StartFromHead": {"type": "boolean"}, "StartLiveTailLogGroupIdentifiers": {"type": "list", "member": {"shape": "LogGroupIdentifier"}, "max": 10, "min": 1}, "StartLiveTailRequest": {"type": "structure", "required": ["logGroupIdentifiers"], "members": {"logGroupIdentifiers": {"shape": "StartLiveTailLogGroupIdentifiers", "documentation": "<p>An array where each item in the array is a log group to include in the Live Tail session.</p> <p>Specify each log group by its ARN. </p> <p>If you specify an ARN, the ARN can't end with an asterisk (*).</p> <note> <p> You can include up to 10 log groups.</p> </note>"}, "logStreamNames": {"shape": "InputLogStreamNames", "documentation": "<p>If you specify this parameter, then only log events in the log streams that you specify here are included in the Live Tail session.</p> <p>If you specify this field, you can't also specify the <code>logStreamNamePrefixes</code> field.</p> <note> <p>You can specify this parameter only if you specify only one log group in <code>logGroupIdentifiers</code>.</p> </note>"}, "logStreamNamePrefixes": {"shape": "InputLogStreamNames", "documentation": "<p>If you specify this parameter, then only log events in the log streams that have names that start with the prefixes that you specify here are included in the Live Tail session.</p> <p>If you specify this field, you can't also specify the <code>logStreamNames</code> field.</p> <note> <p>You can specify this parameter only if you specify only one log group in <code>logGroupIdentifiers</code>.</p> </note>"}, "logEventFilterPattern": {"shape": "FilterPattern", "documentation": "<p>An optional pattern to use to filter the results to include only log events that match the pattern. For example, a filter pattern of <code>error 404</code> causes only log events that include both <code>error</code> and <code>404</code> to be included in the Live Tail stream.</p> <p>Regular expression filter patterns are supported.</p> <p>For more information about filter pattern syntax, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/FilterAndPatternSyntax.html\">Filter and Pattern Syntax</a>.</p>"}}}, "StartLiveTailResponse": {"type": "structure", "members": {"responseStream": {"shape": "StartLiveTailResponseStream", "documentation": "<p>An object that includes the stream returned by your request. It can include both log events and exceptions.</p>"}}}, "StartLiveTailResponseStream": {"type": "structure", "members": {"sessionStart": {"shape": "LiveTailSessionStart", "documentation": "<p>This object contains information about this Live Tail session, including the log groups included and the log stream filters, if any.</p>"}, "sessionUpdate": {"shape": "LiveTailSessionUpdate", "documentation": "<p>This object contains the log events and session metadata.</p>"}, "SessionTimeoutException": {"shape": "SessionTimeoutException", "documentation": "<p>This exception is returned in the stream when the Live Tail session times out. Live Tail sessions time out after three hours.</p>"}, "SessionStreamingException": {"shape": "SessionStreamingException", "documentation": "<p>This exception is returned if an unknown error occurs.</p>"}}, "documentation": "<p>This object includes the stream returned by your <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_StartLiveTail.html\">StartLiveTail</a> request.</p>", "eventstream": true}, "StartQueryRequest": {"type": "structure", "required": ["startTime", "endTime", "queryString"], "members": {"queryLanguage": {"shape": "QueryLanguage", "documentation": "<p>Specify the query language to use for this query. The options are Logs Insights QL, OpenSearch PPL, and OpenSearch SQL. For more information about the query languages that CloudWatch Logs supports, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CWL_AnalyzeLogData_Languages.html\">Supported query languages</a>.</p>"}, "logGroupName": {"shape": "LogGroupName", "documentation": "<p>The log group on which to perform the query.</p> <note> <p>A <code>StartQuery</code> operation must include exactly one of the following parameters: <code>logGroupName</code>, <code>logGroupNames</code>, or <code>logGroupIdentifiers</code>. The exception is queries using the OpenSearch Service SQL query language, where you specify the log group names inside the <code>querystring</code> instead of here.</p> </note>"}, "logGroupNames": {"shape": "LogGroupNames", "documentation": "<p>The list of log groups to be queried. You can include up to 50 log groups.</p> <note> <p>A <code>StartQuery</code> operation must include exactly one of the following parameters: <code>logGroupName</code>, <code>logGroupNames</code>, or <code>logGroupIdentifiers</code>. The exception is queries using the OpenSearch Service SQL query language, where you specify the log group names inside the <code>querystring</code> instead of here.</p> </note>"}, "logGroupIdentifiers": {"shape": "LogGroupIdentifiers", "documentation": "<p>The list of log groups to query. You can include up to 50 log groups.</p> <p>You can specify them by the log group name or ARN. If a log group that you're querying is in a source account and you're using a monitoring account, you must specify the ARN of the log group here. The query definition must also be defined in the monitoring account.</p> <p>If you specify an ARN, use the format arn:aws:logs:<i>region</i>:<i>account-id</i>:log-group:<i>log_group_name</i> Don't include an * at the end.</p> <p>A <code>StartQuery</code> operation must include exactly one of the following parameters: <code>logGroupName</code>, <code>logGroupNames</code>, or <code>logGroupIdentifiers</code>. The exception is queries using the OpenSearch Service SQL query language, where you specify the log group names inside the <code>querystring</code> instead of here. </p>"}, "startTime": {"shape": "Timestamp", "documentation": "<p>The beginning of the time range to query. The range is inclusive, so the specified start time is included in the query. Specified as epoch time, the number of seconds since <code>January 1, 1970, 00:00:00 UTC</code>.</p>"}, "endTime": {"shape": "Timestamp", "documentation": "<p>The end of the time range to query. The range is inclusive, so the specified end time is included in the query. Specified as epoch time, the number of seconds since <code>January 1, 1970, 00:00:00 UTC</code>.</p>"}, "queryString": {"shape": "QueryString", "documentation": "<p>The query string to use. For more information, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CWL_QuerySyntax.html\">CloudWatch Logs Insights Query Syntax</a>.</p>"}, "limit": {"shape": "EventsLimit", "documentation": "<p>The maximum number of log events to return in the query. If the query string uses the <code>fields</code> command, only the specified fields and their values are returned. The default is 10,000.</p>"}}}, "StartQueryResponse": {"type": "structure", "members": {"queryId": {"shape": "QueryId", "documentation": "<p>The unique ID of the query. </p>"}}}, "State": {"type": "string", "enum": ["Active", "Suppressed", "Baseline"]}, "StatsValue": {"type": "double"}, "StopQueryRequest": {"type": "structure", "required": ["queryId"], "members": {"queryId": {"shape": "QueryId", "documentation": "<p>The ID number of the query to stop. To find this ID number, use <code>DescribeQueries</code>.</p>"}}}, "StopQueryResponse": {"type": "structure", "members": {"success": {"shape": "Success", "documentation": "<p>This is true if the query was stopped by the <code>StopQuery</code> operation.</p>"}}}, "StoredBytes": {"type": "long", "min": 0}, "SubscriptionFilter": {"type": "structure", "members": {"filterName": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The name of the subscription filter.</p>"}, "logGroupName": {"shape": "LogGroupName", "documentation": "<p>The name of the log group.</p>"}, "filterPattern": {"shape": "FilterPattern"}, "destinationArn": {"shape": "DestinationArn", "documentation": "<p>The Amazon Resource Name (ARN) of the destination.</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p/>"}, "distribution": {"shape": "Distribution"}, "applyOnTransformedLogs": {"shape": "ApplyOnTransformedLogs", "documentation": "<p>This parameter is valid only for log groups that have an active log transformer. For more information about log transformers, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_PutTransformer.html\">PutTransformer</a>.</p> <p>If this value is <code>true</code>, the subscription filter is applied on the transformed version of the log events instead of the original ingested log events.</p>"}, "creationTime": {"shape": "Timestamp", "documentation": "<p>The creation time of the subscription filter, expressed as the number of milliseconds after <code>Jan 1, 1970 00:00:00 UTC</code>.</p>"}}, "documentation": "<p>Represents a subscription filter.</p>"}, "SubscriptionFilters": {"type": "list", "member": {"shape": "SubscriptionFilter"}}, "SubstituteString": {"type": "structure", "required": ["entries"], "members": {"entries": {"shape": "SubstituteStringEntries", "documentation": "<p>An array of objects, where each object contains the information about one key to match and replace. </p>"}}, "documentation": "<p>This processor matches a key’s value against a regular expression and replaces all matches with a replacement string.</p> <p>For more information about this processor including examples, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-substituteString\"> substituteString</a> in the <i>CloudWatch Logs User Guide</i>.</p>"}, "SubstituteStringEntries": {"type": "list", "member": {"shape": "SubstituteStringEntry"}, "max": 10, "min": 1}, "SubstituteStringEntry": {"type": "structure", "required": ["source", "from", "to"], "members": {"source": {"shape": "Source", "documentation": "<p>The key to modify</p>"}, "from": {"shape": "FromKey", "documentation": "<p>The regular expression string to be replaced. Special regex characters such as [ and ] must be escaped using \\\\ when using double quotes and with \\ when using single quotes. For more information, see <a href=\"https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/regex/Pattern.html\"> Class Pattern</a> on the Oracle web site.</p>"}, "to": {"shape": "To<PERSON><PERSON>", "documentation": "<p>The string to be substituted for each match of <code>from</code> </p>"}}, "documentation": "<p>This object defines one log field key that will be replaced using the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-substituteString\"> substituteString</a> processor.</p>"}, "Success": {"type": "boolean"}, "SuppressionPeriod": {"type": "structure", "members": {"value": {"shape": "Integer", "documentation": "<p>Specifies the number of seconds, minutes or hours to suppress this anomaly. There is no maximum.</p>"}, "suppressionUnit": {"shape": "SuppressionUnit", "documentation": "<p>Specifies whether the value of <code>value</code> is in seconds, minutes, or hours.</p>"}}, "documentation": "<p>If you are suppressing an anomaly temporariliy, this structure defines how long the suppression period is to be.</p>"}, "SuppressionState": {"type": "string", "enum": ["SUPPRESSED", "UNSUPPRESSED"]}, "SuppressionType": {"type": "string", "enum": ["LIMITED", "INFINITE"]}, "SuppressionUnit": {"type": "string", "enum": ["SECONDS", "MINUTES", "HOURS"]}, "TagKey": {"type": "string", "max": 128, "min": 1, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]+)$"}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 50, "min": 0}, "TagList": {"type": "list", "member": {"shape": "TagKey"}, "min": 1}, "TagLogGroupRequest": {"type": "structure", "required": ["logGroupName", "tags"], "members": {"logGroupName": {"shape": "LogGroupName", "documentation": "<p>The name of the log group.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>The key-value pairs to use for the tags.</p>"}}, "deprecated": true, "deprecatedMessage": "Please use the generic tagging API model TagResourceRequest"}, "TagResourceRequest": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {"shape": "AmazonResourceName", "documentation": "<p>The ARN of the resource that you're adding tags to.</p> <p>The ARN format of a log group is <code>arn:aws:logs:<i>Region</i>:<i>account-id</i>:log-group:<i>log-group-name</i> </code> </p> <p>The ARN format of a destination is <code>arn:aws:logs:<i>Region</i>:<i>account-id</i>:destination:<i>destination-name</i> </code> </p> <p>For more information about ARN format, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/iam-access-control-overview-cwl.html\">CloudWatch Logs resources and operations</a>.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>The list of key-value pairs to associate with the resource.</p>"}}}, "TagValue": {"type": "string", "max": 256, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "Tags": {"type": "map", "key": {"shape": "TagKey"}, "value": {"shape": "TagValue"}, "max": 50, "min": 1}, "Target": {"type": "string", "max": 128, "min": 1}, "TargetArn": {"type": "string", "min": 1}, "TargetFormat": {"type": "string", "max": 64, "min": 1}, "TargetTimezone": {"type": "string", "min": 1}, "TestEventMessages": {"type": "list", "member": {"shape": "EventMessage"}, "max": 50, "min": 1}, "TestMetricFilterRequest": {"type": "structure", "required": ["filterPattern", "logEventMessages"], "members": {"filterPattern": {"shape": "FilterPattern"}, "logEventMessages": {"shape": "TestEventMessages", "documentation": "<p>The log event messages to test.</p>"}}}, "TestMetricFilterResponse": {"type": "structure", "members": {"matches": {"shape": "MetricFilterMatches", "documentation": "<p>The matched events.</p>"}}}, "TestTransformerRequest": {"type": "structure", "required": ["transformerConfig", "logEventMessages"], "members": {"transformerConfig": {"shape": "Processors", "documentation": "<p>This structure contains the configuration of this log transformer that you want to test. A log transformer is an array of processors, where each processor applies one type of transformation to the log events that are ingested.</p>"}, "logEventMessages": {"shape": "TestEventMessages", "documentation": "<p>An array of the raw log events that you want to use to test this transformer.</p>"}}}, "TestTransformerResponse": {"type": "structure", "members": {"transformedLogs": {"shape": "TransformedLogs", "documentation": "<p>An array where each member of the array includes both the original version and the transformed version of one of the log events that you input.</p>"}}}, "ThrottlingException": {"type": "structure", "members": {}, "documentation": "<p>The request was throttled because of quota limits.</p>", "exception": true}, "Time": {"type": "string", "min": 1}, "Timestamp": {"type": "long", "min": 0}, "ToKey": {"type": "string", "max": 128, "min": 1}, "Token": {"type": "string"}, "TokenString": {"type": "string", "min": 1}, "TokenValue": {"type": "long"}, "TooManyTagsException": {"type": "structure", "members": {"message": {"shape": "Message"}, "resourceName": {"shape": "AmazonResourceName", "documentation": "<p>The name of the resource.</p>"}}, "documentation": "<p>A resource can have no more than 50 tags.</p>", "exception": true}, "TransformedEventMessage": {"type": "string", "min": 1}, "TransformedLogRecord": {"type": "structure", "members": {"eventNumber": {"shape": "EventNumber", "documentation": "<p>The event number.</p>"}, "eventMessage": {"shape": "EventMessage", "documentation": "<p>The original log event message before it was transformed.</p>"}, "transformedEventMessage": {"shape": "TransformedEventMessage", "documentation": "<p>The log event message after being transformed.</p>"}}, "documentation": "<p>This structure contains information for one log event that has been processed by a log transformer.</p>"}, "TransformedLogs": {"type": "list", "member": {"shape": "TransformedLogRecord"}}, "TrimString": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON>"], "members": {"withKeys": {"shape": "TrimStringWithKeys", "documentation": "<p>The array containing the keys of the fields to trim.</p>"}}, "documentation": "<p>Use this processor to remove leading and trailing whitespace.</p> <p>For more information about this processor including examples, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-trimString\"> trimString</a> in the <i>CloudWatch Logs User Guide</i>.</p>"}, "TrimStringWithKeys": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON>"}, "max": 10, "min": 1}, "Type": {"type": "string", "enum": ["boolean", "integer", "double", "string"]}, "TypeConverter": {"type": "structure", "required": ["entries"], "members": {"entries": {"shape": "TypeConverterEntries", "documentation": "<p>An array of <code>TypeConverterEntry</code> objects, where each object contains the information about one field to change the type of. </p>"}}, "documentation": "<p>Use this processor to convert a value type associated with the specified key to the specified type. It's a casting processor that changes the types of the specified fields. Values can be converted into one of the following datatypes: <code>integer</code>, <code>double</code>, <code>string</code> and <code>boolean</code>. </p> <p>For more information about this processor including examples, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-trimString\"> trimString</a> in the <i>CloudWatch Logs User Guide</i>.</p>"}, "TypeConverterEntries": {"type": "list", "member": {"shape": "TypeConverterEntry"}, "max": 5, "min": 1}, "TypeConverterEntry": {"type": "structure", "required": ["key", "type"], "members": {"key": {"shape": "Key", "documentation": "<p>The key with the value that is to be converted to a different type.</p>"}, "type": {"shape": "Type", "documentation": "<p>The type to convert the field value to. Valid values are <code>integer</code>, <code>double</code>, <code>string</code> and <code>boolean</code>.</p>"}}, "documentation": "<p>This object defines one value type that will be converted using the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-typeConverter\"> typeConverter</a> processor.</p>"}, "Unmask": {"type": "boolean"}, "UnrecognizedClientException": {"type": "structure", "members": {}, "documentation": "<p>The most likely cause is an Amazon Web Services access key ID or secret key that's not valid.</p>", "exception": true}, "UntagLogGroupRequest": {"type": "structure", "required": ["logGroupName", "tags"], "members": {"logGroupName": {"shape": "LogGroupName", "documentation": "<p>The name of the log group.</p>"}, "tags": {"shape": "TagList", "documentation": "<p>The tag keys. The corresponding tags are removed from the log group.</p>"}}, "deprecated": true, "deprecatedMessage": "Please use the generic tagging API model UntagResourceRequest"}, "UntagResourceRequest": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {"shape": "AmazonResourceName", "documentation": "<p>The ARN of the CloudWatch Logs resource that you're removing tags from.</p> <p>The ARN format of a log group is <code>arn:aws:logs:<i>Region</i>:<i>account-id</i>:log-group:<i>log-group-name</i> </code> </p> <p>The ARN format of a destination is <code>arn:aws:logs:<i>Region</i>:<i>account-id</i>:destination:<i>destination-name</i> </code> </p> <p>For more information about ARN format, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/iam-access-control-overview-cwl.html\">CloudWatch Logs resources and operations</a>.</p>"}, "tagKeys": {"shape": "TagKeyList", "documentation": "<p>The list of tag keys to remove from the resource.</p>"}}}, "UpdateAnomalyRequest": {"type": "structure", "required": ["anomalyDetectorArn"], "members": {"anomalyId": {"shape": "AnomalyId", "documentation": "<p>If you are suppressing or unsuppressing an anomaly, specify its unique ID here. You can find anomaly IDs by using the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_ListAnomalies.html\">ListAnomalies</a> operation.</p>"}, "patternId": {"shape": "<PERSON><PERSON>Id", "documentation": "<p>If you are suppressing or unsuppressing an pattern, specify its unique ID here. You can find pattern IDs by using the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatchLogs/latest/APIReference/API_ListAnomalies.html\">ListAnomalies</a> operation.</p>"}, "anomalyDetectorArn": {"shape": "AnomalyDetectorArn", "documentation": "<p>The ARN of the anomaly detector that this operation is to act on.</p>"}, "suppressionType": {"shape": "SuppressionType", "documentation": "<p>Use this to specify whether the suppression to be temporary or infinite. If you specify <code>LIMITED</code>, you must also specify a <code>suppressionPeriod</code>. If you specify <code>INFINITE</code>, any value for <code>suppressionPeriod</code> is ignored. </p>"}, "suppressionPeriod": {"shape": "SuppressionPeriod", "documentation": "<p>If you are temporarily suppressing an anomaly or pattern, use this structure to specify how long the suppression is to last.</p>"}, "baseline": {"shape": "Baseline", "documentation": "<p>Set this to <code>true</code> to prevent CloudWatch Logs from displaying this behavior as an anomaly in the future. The behavior is then treated as baseline behavior. However, if similar but more severe occurrences of this behavior occur in the future, those will still be reported as anomalies. </p> <p>The default is <code>false</code> </p>"}}}, "UpdateDeliveryConfigurationRequest": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "DeliveryId", "documentation": "<p>The ID of the delivery to be updated by this request.</p>"}, "recordFields": {"shape": "RecordFields", "documentation": "<p>The list of record fields to be delivered to the destination, in order. If the delivery's log source has mandatory fields, they must be included in this list.</p>"}, "fieldDelimiter": {"shape": "FieldDelimiter", "documentation": "<p>The field delimiter to use between record fields when the final output format of a delivery is in <code>Plain</code>, <code>W3C</code>, or <code>Raw</code> format.</p>"}, "s3DeliveryConfiguration": {"shape": "S3DeliveryConfiguration", "documentation": "<p>This structure contains parameters that are valid only when the delivery's delivery destination is an S3 bucket.</p>"}}}, "UpdateDeliveryConfigurationResponse": {"type": "structure", "members": {}}, "UpdateLogAnomalyDetectorRequest": {"type": "structure", "required": ["anomalyDetectorArn", "enabled"], "members": {"anomalyDetectorArn": {"shape": "AnomalyDetectorArn", "documentation": "<p>The ARN of the anomaly detector that you want to update.</p>"}, "evaluationFrequency": {"shape": "EvaluationFrequency", "documentation": "<p>Specifies how often the anomaly detector runs and look for anomalies. Set this value according to the frequency that the log group receives new logs. For example, if the log group receives new log events every 10 minutes, then setting <code>evaluationFrequency</code> to <code>FIFTEEN_MIN</code> might be appropriate.</p>"}, "filterPattern": {"shape": "FilterPattern"}, "anomalyVisibilityTime": {"shape": "AnomalyVisibilityTime", "documentation": "<p>The number of days to use as the life cycle of anomalies. After this time, anomalies are automatically baselined and the anomaly detector model will treat new occurrences of similar event as normal. Therefore, if you do not correct the cause of an anomaly during this time, it will be considered normal going forward and will not be detected.</p>"}, "enabled": {"shape": "Boolean", "documentation": "<p>Use this parameter to pause or restart the anomaly detector. </p>"}}}, "UpperCaseString": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON>"], "members": {"withKeys": {"shape": "UpperCaseStringWithKeys", "documentation": "<p>The array of containing the keys of the field to convert to uppercase.</p>"}}, "documentation": "<p>This processor converts a string field to uppercase.</p> <p>For more information about this processor including examples, see <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/CloudWatch-Logs-Transformation.html#CloudWatch-Logs-Transformation-upperCaseString\"> upperCaseString</a> in the <i>CloudWatch Logs User Guide</i>.</p>"}, "UpperCaseStringWithKeys": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON>"}, "max": 10, "min": 1}, "ValidationException": {"type": "structure", "members": {}, "documentation": "<p>One of the parameters for the request is not valid.</p>", "exception": true}, "Value": {"type": "string"}, "ValueKey": {"type": "string", "max": 128, "min": 1}, "WithKey": {"type": "string", "min": 1}}, "documentation": "<p>You can use Amazon CloudWatch Logs to monitor, store, and access your log files from EC2 instances, CloudTrail, and other sources. You can then retrieve the associated log data from CloudWatch Logs using the CloudWatch console. Alternatively, you can use CloudWatch Logs commands in the Amazon Web Services CLI, CloudWatch Logs API, or CloudWatch Logs SDK.</p> <p>You can use CloudWatch Logs to:</p> <ul> <li> <p> <b>Monitor logs from EC2 instances in real time</b>: You can use CloudWatch Logs to monitor applications and systems using log data. For example, CloudWatch Logs can track the number of errors that occur in your application logs. Then, it can send you a notification whenever the rate of errors exceeds a threshold that you specify. CloudWatch Logs uses your log data for monitoring so no code changes are required. For example, you can monitor application logs for specific literal terms (such as \"NullReferenceException\"). You can also count the number of occurrences of a literal term at a particular position in log data (such as \"404\" status codes in an Apache access log). When the term you are searching for is found, CloudWatch Logs reports the data to a CloudWatch metric that you specify.</p> </li> <li> <p> <b>Monitor CloudTrail logged events</b>: You can create alarms in CloudWatch and receive notifications of particular API activity as captured by CloudTrail. You can use the notification to perform troubleshooting.</p> </li> <li> <p> <b>Archive log data</b>: You can use CloudWatch Logs to store your log data in highly durable storage. You can change the log retention setting so that any log events earlier than this setting are automatically deleted. The CloudWatch Logs agent helps to quickly send both rotated and non-rotated log data off of a host and into the log service. You can then access the raw log data when you need it.</p> </li> </ul>"}