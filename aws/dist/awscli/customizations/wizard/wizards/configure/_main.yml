version: "0.1"
title: Configure the AWS CLI
description: This wizard will create a new CLI config profile for you
plan:
  decide_profile_type:
    values:
      config_type:
        type: prompt
        description: "What would you like to configure"
        choices:
          # There's also Web Identity and SAML but we're skipping that.
          - display: Static Credentials
            actual_value: static_creds
          - display: Assume Role
            actual_value: assume_role
          - display: Process Provider
            actual_value: proc_provider
          - display: Additional CLI configuration
            actual_value: additional_config
      profile_name:
        type: prompt
        description: Enter the name of the profile
    next_step:
      switch: config_type
      static_creds: get_static_creds
      assume_role: get_assume_role
      proc_provider: get_process_provider
      additional_config: get_extra_cli_config
  get_static_creds:
    values:
      access_key_id:
        type: prompt
        description: Enter your Access Key Id
      secret_key:
        type: prompt
        description: Enter your Secret Access Key
    next_step: DONE
  get_process_provider:
    values:
      creds_process:
        type: prompt
        description: Enter the credential process command
    next_step: DONE
  get_assume_role:
    values:
      existing_profiles:
        type: sharedconfig
        operation: ListProfiles
      existing_roles:
        type: apicall
        operation: iam.ListRoles
        params: {}
        query: "sort_by(Roles[].{display: RoleName, actual_value: Arn}, &display)"
      role_arn:
        type: prompt
        description: Select the role you want to assume
        choices: existing_roles
      credential_source_type:
        type: prompt
        description: Select your credentials source type
        choices:
          - display: Source Profile
            actual_value: source_profile
          - display: Environment Variables
            actual_value: Environment
          - display: Amazon EC2 Instance Metadata
            actual_value: Ec2InstanceMetadata
          - display: Amazon ECS Container Credentials
            actual_value: EcsContainer
    next_step:
      switch: credential_source_type
      source_profile: get_source_profile
      Environment: DONE
      Ec2InstanceMetadata: DONE
      EcsContainer: DONE
  get_source_profile:
    values:
      source_profile:
        type: prompt
        description: Select the source profile
        choices: existing_profiles
    next_step: DONE
  get_extra_cli_config:
    # TODO: Not implemented yet. I think we want a loop for this?
    # Keep prompting for as many key/value pairs as they want.
    values:
      region:
        type: prompt
        description: "Enter the region name"
execute:
  # All the logic needed when the user selects assume role.
  assume_role:
    - type: sharedconfig
      operation: SetValues
      profile: "{profile_name}"
      condition:
        variable: config_type
        equals: assume_role
      params:
        role_arn: "{role_arn}"
    - type: sharedconfig
      operation: SetValues
      condition:
        variable: credential_source_type
        equals: source_profile
      profile: "{profile_name}"
      params:
        source_profile: "{source_profile}"
    - type: sharedconfig
      operation: SetValues
      condition:
        - variable: source_profile
          equals: null
        - variable: config_type
          equals: assume_role
      profile: "{profile_name}"
      params:
        credential_source: "{credential_source_type}"
  static_creds:
    - type: sharedconfig
      operation: SetValues
      profile: "{profile_name}"
      condition:
        variable: config_type
        equals: static_creds
      params:
        aws_access_key_id: "{access_key_id}"
        aws_secret_access_key: "{secret_key}"
  proc_provider:
    - type: sharedconfig
      operation: SetValues
      profile: "{profile_name}"
      condition:
        variable: config_type
        equals: proc_provider
      params:
        credential_process: "{creds_process}"
  additional_config:
    - type: sharedconfig
      operation: SetValues
      profile: "{profile_name}"
      condition:
        variable: config_type
        equals: additional_config
      params:
        region: "{region}"
