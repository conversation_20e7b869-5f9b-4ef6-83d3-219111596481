version: "0.2"
title: Create An Amazon DynamoDB Table
description: This wizard will create a new Amazon DynamoDB Table
plan:
  intro:
    shortname: Intro
    description: Provide table name and key(s)
    values:
      table_name:
        type: prompt
        description: Enter the name of the table
      primary_key_name:
        type: prompt
        description: Enter the name of the primary key
      primary_key_type:
        type: prompt
        description: Primary key type
        choices:
            - actual_value: S
              display: String
            - actual_value: N
              display: Number
            - actual_value: B
              display:  Binary
      wants_sort_key:
        type: prompt
        description: Add a sort key?
        choices:
          - display: Yes
            actual_value: yes
          - display: No
            actual_value: no
      sort_key_name:
        type: prompt
        description: Enter the name of the sort key
        condition:
          variable: wants_sort_key
          equals: yes
      sort_key_type:
        type: prompt
        description: Sort key type
        choices:
            - actual_value: S
              display: String
            - actual_value: N
              display: Number
            - actual_value: B
              display:  Binary
        condition:
          variable: wants_sort_key
          equals: yes
  capacity:
    shortname: Capacity
    description: Set table capacity
    values:
      capacity_mode:
        type: prompt
        description: "Select the read/write capacity mode."
        choices:
          - actual_value: PROVISIONED
            display: Provisioned (free-tier eligible)
          - actual_value: PAY_PER_REQUEST
            display: On-demand
      read_capacity:
        type: prompt
        description: Enter read capacity units
        datatype: int
        default_value: 5
        condition:
          variable: capacity_mode
          equals: PROVISIONED
      write_capacity:
        type: prompt
        description: Enter write capacity units
        datatype: int
        default_value: 5
        condition:
          variable: capacity_mode
          equals: PROVISIONED
  encryption:
    shortname: Encryption
    description: Set table encryption
    values:
      encryption_settings:
        type: prompt
        description: "Encryption settings for your DynamoDB table"
        choices:
          - display: "DEFAULT"
            actual_value: default
          - display: "KMS - AWS managed CMK"
            actual_value: aws_managed_cmk
          - display: "KMS - Customer managed CMK"
            actual_value: customer_managed_cmk
      existing_cmks:
        type: apicall
        operation: kms.ListAliases
        params: {}
        query: "Aliases[?!starts_with(AliasName, `alias/aws`)].{display: AliasName, actual_value: TargetKeyId}"
      kms_key_id:
        type: prompt
        description: Choose a customer managed CMK
        choices: existing_cmks
        condition:
          variable: encryption_settings
          equals: customer_managed_cmk
  preview:
    shortname: Preview
    description: Preview results
    values:
      preview_cli_command_value:
        type: template
        value: |
          aws dynamodb create-table \
            --table-name '{table_name}' \
            --attribute-definitions \
              'AttributeName={primary_key_name},AttributeType={primary_key_type}' \
            {% if {wants_sort_key} == yes %}
              'AttributeName={sort_key_name},AttributeType={sort_key_type}' \
            {% endif %}
            --key-schema \
              'AttributeName={primary_key_name},KeyType=HASH' \
            {% if {wants_sort_key} == yes %}
              'AttributeName={sort_key_name},KeyType=RANGE' \
            {% endif %}
            {% if {encryption_settings} == aws_managed_cmk %}
            --sse-specification 'Enabled=true,SSEType=KMS' \
            {% endif %}
            {% if {encryption_settings} == customer_managed_cmk %}
            --sse-specification \
              'Enabled=true,SSEType=KMS,KMSMasterKeyId={kms_key_id}' \
            {% endif %}
            {% if {capacity_mode} == PROVISIONED %}
            --provisioned-throughput \
               'ReadCapacityUnits={read_capacity},WriteCapacityUnits={write_capacity}'
            {% endif %}
            {% if {capacity_mode} == PAY_PER_REQUEST %}
            --billing-mode PAY_PER_REQUEST
            {% endif %}
      preview_cfn_template_value:
        type: template
        value: |
          Resources:
            {table_name}:
              Type: "AWS::DynamoDB::Table"
              Properties:
                TableName: {table_name}
                AttributeDefinitions:
                  - AttributeName: {primary_key_name}
                    AttributeType: {primary_key_type}
                  {% if {wants_sort_key} == yes %}
                  - AttributeName: {sort_key_name}
                    AttributeType: {sort_key_type}
                  {% endif %}
                KeySchema:
                  - AttributeName: {primary_key_name}
                    KeyType: HASH
                  {% if {wants_sort_key} == yes %}
                  - AttributeName: {sort_key_name}
                    KeyType: RANGE
                  {% endif %}
                {% if {capacity_mode} == PROVISIONED %}
                ProvisionedThroughput:
                  ReadCapacityUnits: {read_capacity}
                  WriteCapacityUnits: {write_capacity}
                {% endif %}
                {% if {capacity_mode} == PAY_PER_REQUEST %}
                BillingMode: PAY_PER_REQUEST
                {% endif %}
                {% if {encryption_settings} == aws_managed_cmk %}
                SSESpecification:
                  SSEEnabled: true
                  SSEType: KMS
                {% endif %}
                {% if {encryption_settings} == customer_managed_cmk %}
                SSESpecification:
                  KMSMasterKeyId: {kms_key_id}
                  SSEEnabled: true
                  SSEType: KMS
                {% endif %}
      preview_value:
        type: template
        value: |
          {%if {preview_type} == preview_cli_command %}
          {preview_cli_command_value}
          {% endif %}
          {%if {preview_type} == preview_cfn_template %}
          {preview_cfn_template_value}
          {% endif %}
      preview_type:
        type: prompt
        description: Select an preview format
        choices:
          - display: None
            actual_value: preview_none
          - display: AWS CLI command
            actual_value: preview_cli_command
          - display: AWS CloudFormation template
            actual_value: preview_cfn_template
        details:
          value: preview_value
          visible: True
          description: "Preview"
  __DONE__:
execute:
  parameter-build:
    - type: define-variable
      varname: create_table_params
      value:
        TableName: "{table_name}"
        KeySchema:
          - AttributeName: "{primary_key_name}"
            KeyType: HASH
        AttributeDefinitions:
          - AttributeName: "{primary_key_name}"
            AttributeType: "{primary_key_type}"
        BillingMode: "{capacity_mode}"
    - type: merge-dict
      output_var: create_table_params
      condition:
        variable: wants_sort_key
        equals: yes
      overlays:
        - "{create_table_params}"
        - KeySchema:
            - AttributeName: "{sort_key_name}"
              KeyType: RANGE
          AttributeDefinitions:
            - AttributeName: "{sort_key_name}"
              AttributeType: "{sort_key_type}"
    - type: merge-dict
      output_var: create_table_params
      condition:
        variable: capacity_mode
        equals: PROVISIONED
      overlays:
        - "{create_table_params}"
        - ProvisionedThroughput:
            ReadCapacityUnits: "{read_capacity}"
            WriteCapacityUnits: "{write_capacity}"
    - type: merge-dict
      output_var: create_table_params
      condition:
        variable: encryption_settings
        equals: aws_managed_cmk
      overlays:
        - "{create_table_params}"
        - SSESpecification:
            Enabled: true
            SSEType: KMS
    - type: merge-dict
      output_var: create_table_params
      condition:
        variable: encryption_settings
        equals: customer_managed_cmk
      overlays:
        - "{create_table_params}"
        - SSESpecification:
            Enabled: true
            SSEType: KMS
            KMSMasterKeyId: "{kms_key_id}"
    - type: apicall
      operation: dynamodb.CreateTable
      params: "{create_table_params}"
__OUTPUT__:
  value: |
    Wizard successfully created DynamoDB Table:
      Table name: {table_name}
      Primary partition key: {primary_key_name} ({primary_key_type})
      {% if {wants_sort_key} == yes %}
      Primary sort key: {sort_key_name} ({sort_key_type})
      {% endif %}

    {% if {preview_type} == preview_cli_command %}
    Steps to create table is equivalent to running the following sample AWS CLI commands:

    {preview_cli_command_value}
    {% endif %}
    {% if {preview_type} == preview_cfn_template %}
    Steps to create table is equivalent to deploying the following sample AWS CloudFormation template:

    {preview_cfn_template_value}
    {% endif %}
