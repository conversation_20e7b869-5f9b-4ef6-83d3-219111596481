# Terraform outputs for TravelAllinOne infrastructure

# VPC Outputs
output "vpc_id" {
  description = "ID of the VPC"
  value       = module.vpc.vpc_id
}

output "vpc_cidr_block" {
  description = "CIDR block of the VPC"
  value       = module.vpc.vpc_cidr_block
}

output "private_subnet_ids" {
  description = "IDs of the private subnets"
  value       = module.vpc.private_subnet_ids
}

output "public_subnet_ids" {
  description = "IDs of the public subnets"
  value       = module.vpc.public_subnet_ids
}

# EKS Outputs
output "cluster_id" {
  description = "EKS cluster ID"
  value       = module.eks.cluster_id
}

output "cluster_arn" {
  description = "EKS cluster ARN"
  value       = module.eks.cluster_arn
}

output "cluster_endpoint" {
  description = "Endpoint for EKS control plane"
  value       = module.eks.cluster_endpoint
}

output "cluster_security_group_id" {
  description = "Security group ID attached to the EKS cluster"
  value       = module.eks.cluster_security_group_id
}

output "cluster_iam_role_name" {
  description = "IAM role name associated with EKS cluster"
  value       = module.eks.cluster_iam_role_name
}

output "cluster_iam_role_arn" {
  description = "IAM role ARN associated with EKS cluster"
  value       = module.eks.cluster_iam_role_arn
}

output "cluster_certificate_authority_data" {
  description = "Base64 encoded certificate data required to communicate with the cluster"
  value       = module.eks.cluster_certificate_authority_data
}

output "cluster_primary_security_group_id" {
  description = "Cluster security group that was created by Amazon EKS for the cluster"
  value       = module.eks.cluster_primary_security_group_id
}

output "node_groups" {
  description = "EKS node groups"
  value       = module.eks.node_groups
}

# RDS Outputs
output "rds_cluster_endpoint" {
  description = "RDS cluster endpoint"
  value       = module.rds.cluster_endpoint
  sensitive   = true
}

output "rds_cluster_reader_endpoint" {
  description = "RDS cluster reader endpoint"
  value       = module.rds.cluster_reader_endpoint
  sensitive   = true
}

output "rds_cluster_database_name" {
  description = "RDS cluster database name"
  value       = module.rds.cluster_database_name
}

output "rds_cluster_port" {
  description = "RDS cluster port"
  value       = module.rds.cluster_port
}

# DocumentDB Outputs
output "documentdb_cluster_endpoint" {
  description = "DocumentDB cluster endpoint"
  value       = module.documentdb.cluster_endpoint
  sensitive   = true
}

output "documentdb_cluster_reader_endpoint" {
  description = "DocumentDB cluster reader endpoint"
  value       = module.documentdb.cluster_reader_endpoint
  sensitive   = true
}

output "documentdb_cluster_port" {
  description = "DocumentDB cluster port"
  value       = module.documentdb.cluster_port
}

# ElastiCache Outputs
output "redis_cluster_address" {
  description = "Redis cluster address"
  value       = module.elasticache.cluster_address
  sensitive   = true
}

output "redis_cluster_port" {
  description = "Redis cluster port"
  value       = module.elasticache.cluster_port
}

# ALB Outputs
output "alb_dns_name" {
  description = "DNS name of the load balancer"
  value       = module.alb.dns_name
}

output "alb_zone_id" {
  description = "Zone ID of the load balancer"
  value       = module.alb.zone_id
}

output "alb_arn" {
  description = "ARN of the load balancer"
  value       = module.alb.arn
}

# Kubernetes Configuration
output "kubectl_config" {
  description = "kubectl config as generated by the module"
  value = {
    cluster_name                     = module.eks.cluster_name
    endpoint                        = module.eks.cluster_endpoint
    cluster_ca_certificate          = module.eks.cluster_certificate_authority_data
    aws_region                      = var.aws_region
  }
}

# Connection Strings (for application configuration)
output "database_connection_strings" {
  description = "Database connection strings for applications"
  value = {
    mysql_url = "mysql://${var.database_username}:${random_password.rds_password.result}@${module.rds.cluster_endpoint}:${module.rds.cluster_port}/${var.database_name}"
    mongodb_url = "mongodb://${var.documentdb_username}:${random_password.documentdb_password.result}@${module.documentdb.cluster_endpoint}:${module.documentdb.cluster_port}/${var.database_name}?ssl=true&replicaSet=rs0&readPreference=secondaryPreferred&retryWrites=false"
    redis_url = "redis://${module.elasticache.cluster_address}:${module.elasticache.cluster_port}"
  }
  sensitive = true
}

# Random passwords for databases
resource "random_password" "rds_password" {
  length  = 16
  special = true
}

resource "random_password" "documentdb_password" {
  length  = 16
  special = false  # DocumentDB doesn't support all special characters
}
