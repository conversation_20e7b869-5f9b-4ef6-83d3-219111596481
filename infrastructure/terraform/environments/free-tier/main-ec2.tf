# Alternative Free Tier configuration using EC2 instead of EKS
# This is more cost-effective for development and small-scale deployments

terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

# Configure AWS Provider
provider "aws" {
  region = var.aws_region
  
  default_tags {
    tags = {
      Project     = "TravelAllinOne"
      Environment = "free-tier"
      ManagedBy   = "Terraform"
    }
  }
}

# Data sources
data "aws_availability_zones" "available" {
  state = "available"
}

data "aws_ami" "ubuntu" {
  most_recent = true
  owners      = ["099720109477"] # Canonical

  filter {
    name   = "name"
    values = ["ubuntu/images/hvm-ssd/ubuntu-jammy-22.04-amd64-server-*"]
  }

  filter {
    name   = "virtualization-type"
    values = ["hvm"]
  }
}

# VPC Module (reuse existing)
module "vpc" {
  source = "../../modules/vpc"
  
  project_name         = "travelallinone"
  environment         = "free-tier"
  vpc_cidr           = "10.0.0.0/16"
  availability_zones = slice(data.aws_availability_zones.available.names, 0, 2)  # Use only 2 AZs
  
  tags = {
    Project     = "TravelAllinOne"
    Environment = "free-tier"
    ManagedBy   = "Terraform"
  }
}

# Security Group for Application Servers
resource "aws_security_group" "app_servers" {
  name_prefix = "travelallinone-app-servers"
  vpc_id      = module.vpc.vpc_id

  # HTTP
  ingress {
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # HTTPS
  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # SSH
  ingress {
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]  # Restrict this in production
  }

  # Application ports
  ingress {
    from_port   = 8000
    to_port     = 8010
    protocol    = "tcp"
    cidr_blocks = [module.vpc.vpc_cidr_block]
  }

  # Frontend
  ingress {
    from_port   = 3000
    to_port     = 3000
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "travelallinone-app-servers-sg"
  }
}

# Key Pair for EC2 instances
resource "aws_key_pair" "app_key" {
  key_name   = "travelallinone-free-tier-key"
  public_key = file("~/.ssh/id_rsa.pub")  # Make sure to generate this key
}

# Application Load Balancer
resource "aws_lb" "main" {
  name               = "travelallinone-alb"
  internal           = false
  load_balancer_type = "application"
  security_groups    = [aws_security_group.app_servers.id]
  subnets           = module.vpc.public_subnet_ids

  enable_deletion_protection = false

  tags = {
    Name = "travelallinone-alb"
  }
}

# Target Group for Frontend
resource "aws_lb_target_group" "frontend" {
  name     = "travelallinone-frontend-tg"
  port     = 3000
  protocol = "HTTP"
  vpc_id   = module.vpc.vpc_id

  health_check {
    enabled             = true
    healthy_threshold   = 2
    interval            = 30
    matcher             = "200"
    path                = "/"
    port                = "traffic-port"
    protocol            = "HTTP"
    timeout             = 5
    unhealthy_threshold = 2
  }
}

# Target Group for API Gateway
resource "aws_lb_target_group" "api" {
  name     = "travelallinone-api-tg"
  port     = 8000
  protocol = "HTTP"
  vpc_id   = module.vpc.vpc_id

  health_check {
    enabled             = true
    healthy_threshold   = 2
    interval            = 30
    matcher             = "200"
    path                = "/health"
    port                = "traffic-port"
    protocol            = "HTTP"
    timeout             = 5
    unhealthy_threshold = 2
  }
}

# ALB Listener
resource "aws_lb_listener" "main" {
  load_balancer_arn = aws_lb.main.arn
  port              = "80"
  protocol          = "HTTP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.frontend.arn
  }
}

# ALB Listener Rule for API
resource "aws_lb_listener_rule" "api" {
  listener_arn = aws_lb_listener.main.arn
  priority     = 100

  action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.api.arn
  }

  condition {
    path_pattern {
      values = ["/api/*"]
    }
  }
}

# Launch Template for Auto Scaling
resource "aws_launch_template" "app" {
  name_prefix   = "travelallinone-app-"
  image_id      = data.aws_ami.ubuntu.id
  instance_type = "t2.micro"  # Free tier eligible
  key_name      = aws_key_pair.app_key.key_name

  vpc_security_group_ids = [aws_security_group.app_servers.id]

  user_data = base64encode(templatefile("${path.module}/user-data.sh", {
    environment = "free-tier"
  }))

  tag_specifications {
    resource_type = "instance"
    tags = {
      Name = "travelallinone-app-server"
    }
  }
}

# Auto Scaling Group
resource "aws_autoscaling_group" "app" {
  name                = "travelallinone-app-asg"
  vpc_zone_identifier = module.vpc.public_subnet_ids
  target_group_arns   = [aws_lb_target_group.frontend.arn, aws_lb_target_group.api.arn]
  health_check_type   = "ELB"

  min_size         = 1
  max_size         = 2  # Keep within free tier limits
  desired_capacity = 1

  launch_template {
    id      = aws_launch_template.app.id
    version = "$Latest"
  }

  tag {
    key                 = "Name"
    value               = "travelallinone-app-server"
    propagate_at_launch = true
  }
}
