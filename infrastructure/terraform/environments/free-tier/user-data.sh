#!/bin/bash

# User data script for TravelAllinOne application servers
# This script sets up <PERSON><PERSON>, Docker Compose, and the application

set -e

# Update system
apt-get update -y
apt-get upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh
usermod -aG docker ubuntu

# Install Docker Compose
curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# Install Node.js (for frontend)
curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
apt-get install -y nodejs

# Install Python and pip (for backend services)
apt-get install -y python3 python3-pip python3-venv

# Install Git
apt-get install -y git

# Install AWS CLI
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
./aws/install

# Install kubectl
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl

# Create application directory
mkdir -p /opt/travelallinone
cd /opt/travelallinone

# Clone the application repository (you'll need to update this with your actual repo)
# git clone https://github.com/yourusername/TravelAllinOne.git .

# Create environment file
cat > .env << EOF
ENVIRONMENT=${environment}
NODE_ENV=production

# Database URLs (will be populated by Terraform outputs)
MYSQL_URL=\${mysql_url}
MONGODB_URL=\${mongodb_url}
REDIS_URL=\${redis_url}

# JWT Secret
JWT_SECRET=your-super-secret-jwt-key-change-in-production

# Payment Gateway (Razorpay)
RAZORPAY_KEY_ID=your_razorpay_key_id
RAZORPAY_KEY_SECRET=your_razorpay_key_secret

# AI Services
PINECONE_API_KEY=your_pinecone_api_key
PINECONE_ENVIRONMENT=your_pinecone_environment
OPENAI_API_KEY=your_openai_api_key
EOF

# Create a simple Docker Compose file for free tier
cat > docker-compose.free-tier.yml << 'EOF'
version: '3.8'

services:
  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=http://localhost:8000
    depends_on:
      - api-gateway
    restart: unless-stopped

  # Simplified API Gateway (single service for free tier)
  api-gateway:
    build: ./backend/api-gateway
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=free-tier
      - AUTH_SERVICE_URL=http://auth-service:8000
      - BUS_SERVICE_URL=http://bus-service:8000
      - CARPOOL_SERVICE_URL=http://carpool-service:8000
      - BIKE_SERVICE_URL=http://bike-service:8000
      - PAYMENT_SERVICE_URL=http://payment-service:8000
      - CHATBOT_SERVICE_URL=http://chatbot-service:8000
    depends_on:
      - auth-service
      - bus-service
    restart: unless-stopped

  # Core services (minimal for free tier)
  auth-service:
    build: ./backend/auth-service
    environment:
      - DATABASE_URL=\${MYSQL_URL}
      - REDIS_URL=\${REDIS_URL}
      - JWT_SECRET=\${JWT_SECRET}
    restart: unless-stopped

  bus-service:
    build: ./backend/bus-service
    environment:
      - DATABASE_URL=\${MYSQL_URL}
      - MONGODB_URL=\${MONGODB_URL}
    restart: unless-stopped

  carpool-service:
    build: ./backend/carpool-service
    environment:
      - DATABASE_URL=\${MYSQL_URL}
      - MONGODB_URL=\${MONGODB_URL}
    restart: unless-stopped

  bike-service:
    build: ./backend/bike-service
    environment:
      - DATABASE_URL=\${MYSQL_URL}
      - MONGODB_URL=\${MONGODB_URL}
    restart: unless-stopped

  payment-service:
    build: ./backend/payment-service
    environment:
      - DATABASE_URL=\${MYSQL_URL}
      - RAZORPAY_KEY_ID=\${RAZORPAY_KEY_ID}
      - RAZORPAY_KEY_SECRET=\${RAZORPAY_KEY_SECRET}
    restart: unless-stopped

  chatbot-service:
    build: ./backend/chatbot-service
    environment:
      - MONGODB_URL=\${MONGODB_URL}
      - PINECONE_API_KEY=\${PINECONE_API_KEY}
      - OPENAI_API_KEY=\${OPENAI_API_KEY}
    restart: unless-stopped

networks:
  default:
    name: travelallinone-network
EOF

# Set up systemd service for the application
cat > /etc/systemd/system/travelallinone.service << EOF
[Unit]
Description=TravelAllinOne Application
Requires=docker.service
After=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=/opt/travelallinone
ExecStart=/usr/local/bin/docker-compose -f docker-compose.free-tier.yml up -d
ExecStop=/usr/local/bin/docker-compose -f docker-compose.free-tier.yml down
TimeoutStartSec=0

[Install]
WantedBy=multi-user.target
EOF

# Enable the service
systemctl enable travelallinone.service

# Create a health check endpoint
cat > /opt/travelallinone/health-check.sh << 'EOF'
#!/bin/bash
curl -f http://localhost:3000/ || exit 1
curl -f http://localhost:8000/health || exit 1
EOF

chmod +x /opt/travelallinone/health-check.sh

# Set up log rotation
cat > /etc/logrotate.d/travelallinone << EOF
/opt/travelallinone/logs/*.log {
    daily
    missingok
    rotate 7
    compress
    delaycompress
    notifempty
    create 644 ubuntu ubuntu
}
EOF

# Create logs directory
mkdir -p /opt/travelallinone/logs
chown ubuntu:ubuntu /opt/travelallinone/logs

# Install monitoring agent (optional, for basic monitoring)
apt-get install -y htop iotop nethogs

echo "TravelAllinOne server setup completed!"
echo "Application will be available at:"
echo "  Frontend: http://$(curl -s http://***************/latest/meta-data/public-ipv4):3000"
echo "  API: http://$(curl -s http://***************/latest/meta-data/public-ipv4):8000"
