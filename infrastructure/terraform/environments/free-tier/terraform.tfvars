# AWS Free Tier Configuration for TravelAllinOne
# This configuration is optimized for AWS Free Tier limits and cost optimization

project_name = "travelallinone"
environment  = "dev"
aws_region   = "ap-south-1"  # Mumbai region for India

# VPC Configuration - Free Tier Optimized
vpc_cidr = "10.0.0.0/16"

# EKS Configuration - Free Tier
kubernetes_version = "1.28"

# Node Groups - Free Tier Eligible Instances
node_groups = {
  general = {
    instance_types = ["t3.micro", "t2.micro"]  # Free tier: 750 hours/month
    scaling_config = {
      desired_size = 1
      max_size     = 2
      min_size     = 1
    }
    capacity_type = "ON_DEMAND"
  }
}

# RDS Configuration - Free Tier
mysql_version         = "8.0"
rds_instance_class    = "db.t3.micro"    # Free tier: 750 hours/month
rds_allocated_storage = 20               # Free tier: 20 GB SSD storage

database_name     = "travelallinone"
database_username = "admin"

# DocumentDB Configuration - Minimal Cost
# Note: DocumentDB is not free tier eligible, using smallest instance
documentdb_cluster_size   = 1
documentdb_instance_class = "db.t4g.medium"  # Smallest available
documentdb_username       = "admin"

# ElastiCache Configuration - Free Tier
redis_node_type  = "cache.t3.micro"  # Free tier eligible
redis_num_nodes  = 1

# Cost Optimization Settings
enable_spot_instances   = true
auto_scaling_enabled    = true
enable_monitoring       = false  # Disable to save costs initially

# Backup Configuration - Minimal retention for cost savings
backup_retention_period = 1      # Minimum retention
backup_window          = "03:00-04:00"
maintenance_window     = "sun:04:00-sun:05:00"

# Security Configuration
allowed_cidr_blocks = ["0.0.0.0/0"]  # Restrict this in production

# Additional tags for cost tracking
additional_tags = {
  CostCenter  = "Development"
  Tier        = "FreeTier"
  AutoShutdown = "true"  # For automated cost control
}
