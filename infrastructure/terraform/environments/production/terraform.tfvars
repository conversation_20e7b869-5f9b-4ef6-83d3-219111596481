# AWS Production Configuration for TravelAllinOne
# This configuration is optimized for production workloads with high availability

project_name = "travelallinone"
environment  = "prod"
aws_region   = "ap-south-1"  # Mumbai region for India

# VPC Configuration - Production
vpc_cidr = "10.0.0.0/16"

# EKS Configuration - Production
kubernetes_version = "1.28"

# Node Groups - Production Grade Instances
node_groups = {
  general = {
    instance_types = ["t3.medium", "t3.large"]
    scaling_config = {
      desired_size = 3
      max_size     = 10
      min_size     = 2
    }
    capacity_type = "ON_DEMAND"
  }
  spot = {
    instance_types = ["t3.medium", "t3.large", "m5.large"]
    scaling_config = {
      desired_size = 2
      max_size     = 8
      min_size     = 0
    }
    capacity_type = "SPOT"
  }
  compute_optimized = {
    instance_types = ["c5.large", "c5.xlarge"]
    scaling_config = {
      desired_size = 1
      max_size     = 5
      min_size     = 0
    }
    capacity_type = "ON_DEMAND"
  }
}

# RDS Configuration - Production with Multi-AZ
mysql_version         = "8.0"
rds_instance_class    = "db.t3.small"    # Production ready
rds_allocated_storage = 100              # Adequate storage for production

database_name     = "travelallinone"
database_username = "admin"

# DocumentDB Configuration - Production Cluster
documentdb_cluster_size   = 3            # Multi-AZ for high availability
documentdb_instance_class = "db.r5.large"  # Production grade
documentdb_username       = "admin"

# ElastiCache Configuration - Production Redis Cluster
redis_node_type  = "cache.r6g.large"    # Production grade with clustering
redis_num_nodes  = 3                     # Cluster mode for high availability

# Performance and Reliability Settings
enable_spot_instances   = true           # Cost optimization with spot instances
auto_scaling_enabled    = true
enable_monitoring       = true           # Full monitoring stack

# Backup Configuration - Production grade retention
backup_retention_period = 30            # 30 days retention
backup_window          = "03:00-04:00"
maintenance_window     = "sun:04:00-sun:06:00"

# Security Configuration - Restricted access
allowed_cidr_blocks = [
  "10.0.0.0/8",      # Private networks
  "172.16.0.0/12",   # Private networks
  "192.168.0.0/16"   # Private networks
]

# Domain and SSL Configuration
domain_name     = "api.travelallinone.com"  # Replace with your domain
certificate_arn = ""                         # Add your ACM certificate ARN

# Additional tags for production
additional_tags = {
  CostCenter     = "Production"
  Tier           = "Production"
  BackupRequired = "true"
  Monitoring     = "enabled"
  Compliance     = "required"
}
