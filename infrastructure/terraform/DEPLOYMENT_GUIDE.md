# TravelAllinOne Infrastructure Deployment Guide

This guide covers deploying TravelAllinOne using both AWS Free Tier and Production configurations.

## Prerequisites

1. **AWS Account** with appropriate permissions
2. **Terraform** >= 1.0 installed
3. **AWS CLI** configured with your credentials
4. **kubectl** installed (for EKS deployments)
5. **SSH Key Pair** generated (`ssh-keygen -t rsa -b 4096`)

## Configuration Options

### 🆓 Free Tier Configuration
- **Cost**: ~$0-50/month (within free tier limits)
- **Suitable for**: Development, testing, small-scale demos
- **Architecture**: EC2-based with minimal resources
- **Limitations**: Single AZ, limited scaling, basic monitoring

### 💰 Production Configuration  
- **Cost**: ~$200-500/month (depending on usage)
- **Suitable for**: Production workloads, high availability
- **Architecture**: EKS-based with auto-scaling
- **Features**: Multi-AZ, advanced monitoring, backup strategies

## Free Tier Deployment

### Step 1: Prepare Backend Storage

```bash
# Create S3 bucket for Terraform state (one-time setup)
aws s3 mb s3://travelallinone-terraform-state-dev --region ap-south-1

# Create DynamoDB table for state locking
aws dynamodb create-table \
    --table-name travelallinone-terraform-locks-dev \
    --attribute-definitions AttributeName=LockID,AttributeType=S \
    --key-schema AttributeName=LockID,KeyType=HASH \
    --provisioned-throughput ReadCapacityUnits=5,WriteCapacityUnits=5 \
    --region ap-south-1
```

### Step 2: Deploy Free Tier Infrastructure

```bash
cd infrastructure/terraform/environments/free-tier

# Initialize Terraform
terraform init

# Plan the deployment
terraform plan -var-file=terraform.tfvars

# Apply the configuration
terraform apply -var-file=terraform.tfvars
```

### Step 3: Deploy Applications

```bash
# SSH into the EC2 instance
ssh -i ~/.ssh/id_rsa ubuntu@<EC2_PUBLIC_IP>

# Navigate to application directory
cd /opt/travelallinone

# Start the application
sudo systemctl start travelallinone
sudo systemctl status travelallinone
```

## Production Deployment

### Step 1: Prepare Backend Storage

```bash
# Create S3 bucket for Terraform state
aws s3 mb s3://travelallinone-terraform-state-prod --region ap-south-1

# Create DynamoDB table for state locking
aws dynamodb create-table \
    --table-name travelallinone-terraform-locks-prod \
    --attribute-definitions AttributeName=LockID,AttributeType=S \
    --key-schema AttributeName=LockID,KeyType=HASH \
    --provisioned-throughput ReadCapacityUnits=5,WriteCapacityUnits=5 \
    --region ap-south-1
```

### Step 2: Deploy Production Infrastructure

```bash
cd infrastructure/terraform/environments/production

# Initialize Terraform
terraform init

# Plan the deployment
terraform plan -var-file=terraform.tfvars

# Apply the configuration
terraform apply -var-file=terraform.tfvars
```

### Step 3: Configure kubectl

```bash
# Update kubeconfig
aws eks update-kubeconfig --region ap-south-1 --name travelallinone-prod-cluster

# Verify connection
kubectl get nodes
```

### Step 4: Deploy Applications to Kubernetes

```bash
# Deploy using Kubernetes manifests
kubectl apply -f ../../kubernetes/manifests/

# Or using Helm charts
helm install travelallinone ../../kubernetes/helm-charts/travelallinone/
```

## Cost Optimization Tips

### Free Tier Optimization
1. **Use t2.micro instances** (750 hours/month free)
2. **Enable auto-shutdown** for non-production hours
3. **Use RDS free tier** (db.t3.micro, 20GB storage)
4. **Minimize data transfer** costs
5. **Use CloudWatch free tier** for basic monitoring

### Production Optimization
1. **Use Spot Instances** for non-critical workloads
2. **Enable auto-scaling** to match demand
3. **Use Reserved Instances** for predictable workloads
4. **Implement proper resource tagging** for cost tracking
5. **Set up billing alerts**

## Monitoring and Maintenance

### Free Tier Monitoring
```bash
# Check application status
curl http://<ALB_DNS_NAME>/health

# View logs
sudo journalctl -u travelallinone -f

# Monitor resource usage
htop
```

### Production Monitoring
```bash
# Check cluster status
kubectl get pods --all-namespaces

# View application logs
kubectl logs -f deployment/frontend

# Access Grafana dashboard
kubectl port-forward svc/grafana 3000:80
```

## Scaling from Free Tier to Production

### Migration Steps
1. **Export data** from free tier databases
2. **Deploy production infrastructure**
3. **Import data** to production databases
4. **Update DNS** to point to production ALB
5. **Decommission** free tier resources

### Zero-Downtime Migration
1. **Set up production environment** in parallel
2. **Configure database replication**
3. **Use blue-green deployment** strategy
4. **Gradually shift traffic** using Route 53 weighted routing

## Troubleshooting

### Common Issues

#### Free Tier
- **Instance limits**: Check EC2 instance limits in your region
- **Storage limits**: Monitor EBS volume usage (30GB free)
- **Network limits**: Be aware of data transfer costs

#### Production
- **EKS node capacity**: Ensure adequate node capacity for pods
- **Database connections**: Monitor RDS connection limits
- **Load balancer limits**: Check ALB target limits

### Support Resources
- AWS Free Tier FAQ: https://aws.amazon.com/free/
- EKS Best Practices: https://aws.github.io/aws-eks-best-practices/
- Terraform AWS Provider: https://registry.terraform.io/providers/hashicorp/aws/

## Security Considerations

### Free Tier Security
- **Restrict SSH access** to specific IP ranges
- **Use IAM roles** instead of access keys
- **Enable CloudTrail** (free tier available)
- **Regular security updates**

### Production Security
- **VPC security groups** with minimal required access
- **WAF protection** for web applications
- **Secrets Manager** for sensitive data
- **Regular security audits**
- **Compliance monitoring**

## Backup and Recovery

### Free Tier
- **RDS automated backups** (7 days retention)
- **EBS snapshots** for critical data
- **Application-level backups**

### Production
- **Multi-region backups**
- **Point-in-time recovery**
- **Disaster recovery procedures**
- **Regular backup testing**
