# Bus Booking Service API for TravelAllinOne (Inspired by AbhiBus)

from fastapi import FastAPI, HTTPException, Depends, Query
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
import sys
import os

# Add shared modules to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'shared'))

from models import (
    BaseResponse, PaginatedResponse, PaginationParams,
    SearchFilters, SearchResult, BookingCreate, BookingResponse,
    VehicleResponse, Route, Location, BookingStatus
)
from utils import (
    get_current_user, get_mysql, get_mongodb, get_redis,
    setup_logging, PricingUtils, LocationUtils, generate_booking_reference
)

# Initialize FastAPI app
app = FastAPI(
    title="TravelAllinOne Bus Service",
    description="Bus Booking Service inspired by AbhiBus",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Setup logging
logger = setup_logging("bus-service")

# Request/Response Models
class BusSearchRequest(BaseModel):
    source_city: str = Field(..., min_length=2, max_length=50)
    destination_city: str = Field(..., min_length=2, max_length=50)
    travel_date: datetime
    passenger_count: int = Field(1, ge=1, le=10)
    bus_type: Optional[str] = Field(None, regex="^(regular|ac|sleeper|luxury)$")
    departure_time_start: Optional[str] = Field(None, regex="^([01]?[0-9]|2[0-3]):[0-5][0-9]$")
    departure_time_end: Optional[str] = Field(None, regex="^([01]?[0-9]|2[0-3]):[0-5][0-9]$")
    max_price: Optional[float] = Field(None, ge=0)
    amenities: Optional[List[str]] = []

class BusDetails(BaseModel):
    id: str
    operator_name: str
    bus_number: str
    bus_type: str
    total_seats: int
    available_seats: int
    amenities: List[str]
    rating: Optional[float] = None
    reviews_count: int = 0

class BusRoute(BaseModel):
    id: str
    source: Location
    destination: Location
    distance_km: float
    estimated_duration_minutes: int
    departure_time: datetime
    arrival_time: datetime
    stops: List[Location] = []

class BusSearchResult(BaseModel):
    bus: BusDetails
    route: BusRoute
    pricing: Dict[str, float]  # seat_type -> price
    available_seat_types: List[str]

class SeatLayout(BaseModel):
    total_seats: int
    seat_configuration: str  # e.g., "2+2", "2+1"
    seats: List[Dict[str, Any]]  # seat details with availability

class BusBookingRequest(BaseModel):
    bus_id: str
    route_id: str
    travel_date: datetime
    passenger_details: List[Dict[str, str]]
    selected_seats: List[str]
    contact_phone: str = Field(..., regex=r"^\+91[6-9]\d{9}$")
    emergency_contact: Optional[str] = None

# Database operations
class BusRepository:
    def __init__(self, mysql_pool, mongo_client):
        self.mysql_pool = mysql_pool
        self.mongo_db = mongo_client.travelallinone
    
    async def search_buses(self, search_params: BusSearchRequest) -> List[BusSearchResult]:
        """Search available buses"""
        async with self.mysql_pool.acquire() as conn:
            async with conn.cursor() as cursor:
                # Build search query
                query = """
                SELECT b.id, b.operator_name, b.bus_number, b.bus_type, 
                       b.total_seats, b.amenities, b.rating, b.reviews_count,
                       r.id as route_id, r.source_city, r.destination_city,
                       r.distance_km, r.estimated_duration_minutes,
                       s.departure_time, s.arrival_time, s.available_seats,
                       s.pricing
                FROM buses b
                JOIN routes r ON b.route_id = r.id
                JOIN schedules s ON b.id = s.bus_id
                WHERE r.source_city = %s 
                  AND r.destination_city = %s
                  AND DATE(s.departure_time) = DATE(%s)
                  AND s.available_seats >= %s
                  AND b.is_active = 1
                """
                
                params = [
                    search_params.source_city,
                    search_params.destination_city,
                    search_params.travel_date,
                    search_params.passenger_count
                ]
                
                # Add optional filters
                if search_params.bus_type:
                    query += " AND b.bus_type = %s"
                    params.append(search_params.bus_type)
                
                if search_params.max_price:
                    query += " AND JSON_EXTRACT(s.pricing, '$.regular') <= %s"
                    params.append(search_params.max_price)
                
                query += " ORDER BY s.departure_time"
                
                await cursor.execute(query, params)
                rows = await cursor.fetchall()
                
                results = []
                for row in rows:
                    # Parse pricing JSON
                    import json
                    pricing = json.loads(row[16]) if row[16] else {"regular": 0}
                    
                    bus_result = BusSearchResult(
                        bus=BusDetails(
                            id=str(row[0]),
                            operator_name=row[1],
                            bus_number=row[2],
                            bus_type=row[3],
                            total_seats=row[4],
                            available_seats=row[14],
                            amenities=row[5].split(',') if row[5] else [],
                            rating=float(row[6]) if row[6] else None,
                            reviews_count=row[7]
                        ),
                        route=BusRoute(
                            id=str(row[8]),
                            source=Location(
                                latitude=0.0, longitude=0.0,  # Would be fetched from cities table
                                address="", city=row[9], state="", pincode=""
                            ),
                            destination=Location(
                                latitude=0.0, longitude=0.0,
                                address="", city=row[10], state="", pincode=""
                            ),
                            distance_km=float(row[11]),
                            estimated_duration_minutes=row[12],
                            departure_time=row[13],
                            arrival_time=row[15]
                        ),
                        pricing=pricing,
                        available_seat_types=list(pricing.keys())
                    )
                    results.append(bus_result)
                
                return results
    
    async def get_seat_layout(self, bus_id: str, travel_date: datetime) -> SeatLayout:
        """Get bus seat layout and availability"""
        async with self.mysql_pool.acquire() as conn:
            async with conn.cursor() as cursor:
                # Get bus details
                await cursor.execute(
                    "SELECT total_seats, seat_configuration FROM buses WHERE id = %s",
                    (bus_id,)
                )
                bus_info = await cursor.fetchone()
                
                if not bus_info:
                    raise HTTPException(status_code=404, detail="Bus not found")
                
                # Get booked seats for the date
                await cursor.execute("""
                    SELECT selected_seats FROM bookings 
                    WHERE bus_id = %s AND DATE(travel_date) = DATE(%s) 
                    AND status IN ('confirmed', 'pending')
                """, (bus_id, travel_date))
                
                booked_seats = []
                for row in await cursor.fetchall():
                    if row[0]:
                        booked_seats.extend(row[0].split(','))
                
                # Generate seat layout
                total_seats = bus_info[0]
                seat_config = bus_info[1]
                
                seats = []
                for i in range(1, total_seats + 1):
                    seat_number = f"S{i:02d}"
                    seats.append({
                        "seat_number": seat_number,
                        "is_available": seat_number not in booked_seats,
                        "seat_type": "regular",  # Could be enhanced with different types
                        "price": 500.0  # Would be calculated based on route and seat type
                    })
                
                return SeatLayout(
                    total_seats=total_seats,
                    seat_configuration=seat_config,
                    seats=seats
                )
    
    async def create_booking(self, user_id: str, booking_data: BusBookingRequest) -> str:
        """Create bus booking"""
        async with self.mysql_pool.acquire() as conn:
            async with conn.cursor() as cursor:
                # Check seat availability
                await cursor.execute("""
                    SELECT available_seats FROM schedules 
                    WHERE bus_id = %s AND DATE(departure_time) = DATE(%s)
                """, (booking_data.bus_id, booking_data.travel_date))
                
                schedule = await cursor.fetchone()
                if not schedule or schedule[0] < len(booking_data.selected_seats):
                    raise HTTPException(status_code=400, detail="Seats not available")
                
                # Calculate total amount
                seat_price = 500.0  # Would be fetched from pricing
                total_amount = seat_price * len(booking_data.selected_seats)
                
                # Create booking
                booking_ref = generate_booking_reference()
                query = """
                INSERT INTO bookings (user_id, bus_id, route_id, booking_reference,
                                    travel_date, passenger_count, selected_seats,
                                    total_amount, status, contact_phone, created_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                
                await cursor.execute(query, (
                    user_id, booking_data.bus_id, booking_data.route_id,
                    booking_ref, booking_data.travel_date,
                    len(booking_data.passenger_details),
                    ','.join(booking_data.selected_seats),
                    total_amount, BookingStatus.PENDING.value,
                    booking_data.contact_phone, datetime.utcnow()
                ))
                
                booking_id = cursor.lastrowid
                
                # Update available seats
                await cursor.execute("""
                    UPDATE schedules SET available_seats = available_seats - %s
                    WHERE bus_id = %s AND DATE(departure_time) = DATE(%s)
                """, (len(booking_data.selected_seats), booking_data.bus_id, booking_data.travel_date))
                
                await conn.commit()
                return str(booking_id)

# API Endpoints
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "bus-service", "timestamp": datetime.utcnow()}

@app.post("/search", response_model=BaseResponse)
async def search_buses(
    search_params: BusSearchRequest,
    mysql_pool = Depends(get_mysql),
    mongo_client = Depends(get_mongodb)
):
    """Search available buses"""
    try:
        bus_repo = BusRepository(mysql_pool, mongo_client)
        results = await bus_repo.search_buses(search_params)
        
        return BaseResponse(
            message=f"Found {len(results)} buses",
            data={"buses": [result.dict() for result in results]}
        )
    
    except Exception as e:
        logger.error(f"Bus search error: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.get("/seat-layout/{bus_id}", response_model=BaseResponse)
async def get_seat_layout(
    bus_id: str,
    travel_date: datetime = Query(...),
    mysql_pool = Depends(get_mysql),
    mongo_client = Depends(get_mongodb)
):
    """Get bus seat layout and availability"""
    try:
        bus_repo = BusRepository(mysql_pool, mongo_client)
        layout = await bus_repo.get_seat_layout(bus_id, travel_date)
        
        return BaseResponse(
            message="Seat layout retrieved successfully",
            data={"seat_layout": layout.dict()}
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Seat layout error: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.post("/book", response_model=BaseResponse)
async def book_bus(
    booking_data: BusBookingRequest,
    current_user: Dict = Depends(get_current_user),
    mysql_pool = Depends(get_mysql),
    mongo_client = Depends(get_mongodb)
):
    """Book bus tickets"""
    try:
        bus_repo = BusRepository(mysql_pool, mongo_client)
        booking_id = await bus_repo.create_booking(current_user['user_id'], booking_data)
        
        logger.info(f"Bus booking created: {booking_id}")
        return BaseResponse(
            message="Booking created successfully",
            data={"booking_id": booking_id}
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Bus booking error: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
