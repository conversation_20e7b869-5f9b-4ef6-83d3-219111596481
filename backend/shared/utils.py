# Shared utilities for TravelAllinOne microservices

import jwt
import bcrypt
import redis
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from fastapi import HTTPException, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import os
from functools import wraps
import asyncio
import aioredis
from motor.motor_asyncio import AsyncIOMotorClient
import aiomysql
from contextlib import asynccontextmanager

# Configuration
JWT_SECRET = os.getenv("JWT_SECRET", "your-super-secret-jwt-key")
JWT_ALGORITHM = "HS256"
JWT_EXPIRATION_HOURS = 24

REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379")
MONGODB_URL = os.getenv("MONGODB_URL", "mongodb://localhost:27017")
MYSQL_URL = os.getenv("DATABASE_URL", "mysql://user:password@localhost:3306/travelallinone")

# Security
security = HTTPBearer()

class AuthUtils:
    @staticmethod
    def hash_password(password: str) -> str:
        """Hash password using bcrypt"""
        salt = bcrypt.gensalt()
        return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')
    
    @staticmethod
    def verify_password(password: str, hashed: str) -> bool:
        """Verify password against hash"""
        return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
    
    @staticmethod
    def create_access_token(data: Dict[str, Any]) -> str:
        """Create JWT access token"""
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(hours=JWT_EXPIRATION_HOURS)
        to_encode.update({"exp": expire})
        return jwt.encode(to_encode, JWT_SECRET, algorithm=JWT_ALGORITHM)
    
    @staticmethod
    def verify_token(token: str) -> Dict[str, Any]:
        """Verify and decode JWT token"""
        try:
            payload = jwt.decode(token, JWT_SECRET, algorithms=[JWT_ALGORITHM])
            return payload
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token has expired"
            )
        except jwt.JWTError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token"
            )

# Database Connections
class DatabaseManager:
    def __init__(self):
        self.redis_client: Optional[aioredis.Redis] = None
        self.mongo_client: Optional[AsyncIOMotorClient] = None
        self.mysql_pool: Optional[aiomysql.Pool] = None
    
    async def connect_redis(self):
        """Connect to Redis"""
        self.redis_client = aioredis.from_url(REDIS_URL)
        
    async def connect_mongodb(self):
        """Connect to MongoDB"""
        self.mongo_client = AsyncIOMotorClient(MONGODB_URL)
        
    async def connect_mysql(self):
        """Connect to MySQL"""
        # Parse MySQL URL
        import urllib.parse as urlparse
        parsed = urlparse.urlparse(MYSQL_URL)
        
        self.mysql_pool = await aiomysql.create_pool(
            host=parsed.hostname,
            port=parsed.port or 3306,
            user=parsed.username,
            password=parsed.password,
            db=parsed.path[1:],  # Remove leading slash
            charset='utf8mb4',
            autocommit=True
        )
    
    async def close_connections(self):
        """Close all database connections"""
        if self.redis_client:
            await self.redis_client.close()
        if self.mongo_client:
            self.mongo_client.close()
        if self.mysql_pool:
            self.mysql_pool.close()
            await self.mysql_pool.wait_closed()

# Global database manager instance
db_manager = DatabaseManager()

# Dependency injection
async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Get current authenticated user"""
    token = credentials.credentials
    payload = AuthUtils.verify_token(token)
    user_id = payload.get("user_id")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token payload"
        )
    return payload

async def get_redis():
    """Get Redis client"""
    if not db_manager.redis_client:
        await db_manager.connect_redis()
    return db_manager.redis_client

async def get_mongodb():
    """Get MongoDB client"""
    if not db_manager.mongo_client:
        await db_manager.connect_mongodb()
    return db_manager.mongo_client

async def get_mysql():
    """Get MySQL connection pool"""
    if not db_manager.mysql_pool:
        await db_manager.connect_mysql()
    return db_manager.mysql_pool

# Caching utilities
class CacheManager:
    def __init__(self, redis_client: aioredis.Redis):
        self.redis = redis_client
    
    async def get(self, key: str) -> Optional[str]:
        """Get value from cache"""
        try:
            return await self.redis.get(key)
        except Exception as e:
            logging.error(f"Cache get error: {e}")
            return None
    
    async def set(self, key: str, value: str, expire: int = 3600):
        """Set value in cache with expiration"""
        try:
            await self.redis.setex(key, expire, value)
        except Exception as e:
            logging.error(f"Cache set error: {e}")
    
    async def delete(self, key: str):
        """Delete key from cache"""
        try:
            await self.redis.delete(key)
        except Exception as e:
            logging.error(f"Cache delete error: {e}")
    
    async def get_or_set(self, key: str, fetch_func, expire: int = 3600):
        """Get from cache or fetch and set"""
        cached = await self.get(key)
        if cached:
            return cached
        
        value = await fetch_func()
        if value:
            await self.set(key, str(value), expire)
        return value

# Validation utilities
class ValidationUtils:
    @staticmethod
    def validate_indian_phone(phone: str) -> bool:
        """Validate Indian phone number"""
        import re
        pattern = r"^\+91[6-9]\d{9}$"
        return bool(re.match(pattern, phone))
    
    @staticmethod
    def validate_pincode(pincode: str) -> bool:
        """Validate Indian pincode"""
        import re
        pattern = r"^\d{6}$"
        return bool(re.match(pattern, pincode))
    
    @staticmethod
    def validate_vehicle_number(vehicle_number: str) -> bool:
        """Validate Indian vehicle number"""
        import re
        pattern = r"^[A-Z]{2}[0-9]{2}[A-Z]{2}[0-9]{4}$"
        return bool(re.match(pattern, vehicle_number))

# Distance calculation utilities
class LocationUtils:
    @staticmethod
    def calculate_distance(lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """Calculate distance between two coordinates using Haversine formula"""
        import math
        
        # Convert latitude and longitude from degrees to radians
        lat1, lon1, lat2, lon2 = map(math.radians, [lat1, lon1, lat2, lon2])
        
        # Haversine formula
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
        c = 2 * math.asin(math.sqrt(a))
        
        # Radius of earth in kilometers
        r = 6371
        return c * r
    
    @staticmethod
    def estimate_travel_time(distance_km: float, vehicle_type: str) -> int:
        """Estimate travel time in minutes based on distance and vehicle type"""
        # Average speeds in km/h
        speeds = {
            "bus": 40,
            "car": 50,
            "bike": 35
        }
        
        speed = speeds.get(vehicle_type, 40)
        return int((distance_km / speed) * 60)

# Pricing utilities
class PricingUtils:
    @staticmethod
    def calculate_bus_fare(distance_km: float, bus_type: str = "regular") -> float:
        """Calculate bus fare based on distance and type"""
        base_rates = {
            "regular": 1.5,      # ₹1.5 per km
            "ac": 2.0,           # ₹2.0 per km
            "sleeper": 2.5,      # ₹2.5 per km
            "luxury": 3.0        # ₹3.0 per km
        }
        
        rate = base_rates.get(bus_type, 1.5)
        base_fare = distance_km * rate
        
        # Minimum fare
        return max(base_fare, 50.0)
    
    @staticmethod
    def calculate_carpool_fare(distance_km: float, total_seats: int) -> float:
        """Calculate carpool fare per seat"""
        # Base rate ₹3 per km, divided by number of seats
        total_cost = distance_km * 3.0
        per_seat_cost = total_cost / total_seats
        
        # Minimum fare per seat
        return max(per_seat_cost, 30.0)
    
    @staticmethod
    def calculate_bike_fare(distance_km: float) -> float:
        """Calculate bike ride fare"""
        # Base fare + distance rate
        base_fare = 20.0
        distance_rate = distance_km * 8.0  # ₹8 per km
        
        return base_fare + distance_rate

# Notification utilities
class NotificationManager:
    def __init__(self, redis_client: aioredis.Redis):
        self.redis = redis_client
    
    async def send_notification(self, user_id: str, title: str, message: str, data: Dict = None):
        """Send notification to user"""
        notification = {
            "title": title,
            "message": message,
            "data": data or {},
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # Store in Redis for real-time notifications
        await self.redis.lpush(f"notifications:{user_id}", str(notification))
        await self.redis.expire(f"notifications:{user_id}", 86400)  # 24 hours
    
    async def get_notifications(self, user_id: str, limit: int = 10) -> List[Dict]:
        """Get user notifications"""
        notifications = await self.redis.lrange(f"notifications:{user_id}", 0, limit - 1)
        return [eval(notif) for notif in notifications]

# Error handling
class TravelAllinOneException(Exception):
    def __init__(self, message: str, status_code: int = 400, details: Dict = None):
        self.message = message
        self.status_code = status_code
        self.details = details or {}
        super().__init__(self.message)

# Logging setup
def setup_logging(service_name: str):
    """Setup logging for microservice"""
    logging.basicConfig(
        level=logging.INFO,
        format=f'%(asctime)s - {service_name} - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(f'/var/log/{service_name}.log'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(service_name)
