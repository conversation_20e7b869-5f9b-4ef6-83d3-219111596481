# Shared models for TravelAllinOne microservices

from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, EmailStr, validator
from enum import Enum
import uuid

# Base Models
class BaseResponse(BaseModel):
    success: bool = True
    message: str = "Operation successful"
    data: Optional[Dict[str, Any]] = None
    errors: Optional[List[str]] = None

class PaginationParams(BaseModel):
    page: int = Field(1, ge=1, description="Page number")
    limit: int = Field(10, ge=1, le=100, description="Items per page")
    
class PaginatedResponse(BaseResponse):
    pagination: Optional[Dict[str, Any]] = None

# Enums
class UserRole(str, Enum):
    PASSENGER = "passenger"
    DRIVER = "driver"
    OPERATOR = "operator"
    ADMIN = "admin"

class BookingStatus(str, Enum):
    PENDING = "pending"
    CONFIRMED = "confirmed"
    CANCELLED = "cancelled"
    COMPLETED = "completed"
    REFUNDED = "refunded"

class PaymentStatus(str, Enum):
    PENDING = "pending"
    SUCCESS = "success"
    FAILED = "failed"
    REFUNDED = "refunded"

class VehicleType(str, Enum):
    BUS = "bus"
    CAR = "car"
    BIKE = "bike"

class TripStatus(str, Enum):
    SCHEDULED = "scheduled"
    ACTIVE = "active"
    COMPLETED = "completed"
    CANCELLED = "cancelled"

# User Models
class UserBase(BaseModel):
    email: EmailStr
    phone: str = Field(..., regex=r"^\+91[6-9]\d{9}$", description="Indian mobile number")
    first_name: str = Field(..., min_length=2, max_length=50)
    last_name: str = Field(..., min_length=2, max_length=50)
    date_of_birth: Optional[datetime] = None
    gender: Optional[str] = Field(None, regex=r"^(male|female|other)$")

class UserCreate(UserBase):
    password: str = Field(..., min_length=8, max_length=128)
    role: UserRole = UserRole.PASSENGER

class UserUpdate(BaseModel):
    first_name: Optional[str] = Field(None, min_length=2, max_length=50)
    last_name: Optional[str] = Field(None, min_length=2, max_length=50)
    phone: Optional[str] = Field(None, regex=r"^\+91[6-9]\d{9}$")
    date_of_birth: Optional[datetime] = None
    gender: Optional[str] = Field(None, regex=r"^(male|female|other)$")

class UserResponse(UserBase):
    id: str
    role: UserRole
    is_active: bool
    is_verified: bool
    created_at: datetime
    updated_at: datetime

# Location Models
class Location(BaseModel):
    latitude: float = Field(..., ge=-90, le=90)
    longitude: float = Field(..., ge=-180, le=180)
    address: str
    city: str
    state: str
    pincode: str = Field(..., regex=r"^\d{6}$")

class Route(BaseModel):
    id: Optional[str] = None
    source: Location
    destination: Location
    distance_km: float = Field(..., gt=0)
    estimated_duration_minutes: int = Field(..., gt=0)

# Vehicle Models
class VehicleBase(BaseModel):
    vehicle_number: str = Field(..., regex=r"^[A-Z]{2}[0-9]{2}[A-Z]{2}[0-9]{4}$")
    vehicle_type: VehicleType
    model: str
    capacity: int = Field(..., gt=0)
    amenities: List[str] = []

class VehicleCreate(VehicleBase):
    operator_id: str

class VehicleResponse(VehicleBase):
    id: str
    operator_id: str
    is_active: bool
    created_at: datetime

# Booking Models
class BookingBase(BaseModel):
    route: Route
    travel_date: datetime
    passenger_count: int = Field(..., gt=0, le=10)
    special_requests: Optional[str] = None

class BookingCreate(BookingBase):
    vehicle_id: str
    selected_seats: Optional[List[str]] = None  # For buses

class BookingResponse(BookingBase):
    id: str
    user_id: str
    vehicle_id: str
    booking_reference: str
    status: BookingStatus
    total_amount: float
    selected_seats: Optional[List[str]] = None
    created_at: datetime
    updated_at: datetime

# Payment Models
class PaymentBase(BaseModel):
    amount: float = Field(..., gt=0)
    currency: str = "INR"
    payment_method: str

class PaymentCreate(PaymentBase):
    booking_id: str

class PaymentResponse(PaymentBase):
    id: str
    booking_id: str
    transaction_id: str
    status: PaymentStatus
    gateway_response: Optional[Dict[str, Any]] = None
    created_at: datetime

# Trip Models (for car pooling and bike sharing)
class TripBase(BaseModel):
    route: Route
    departure_time: datetime
    available_seats: int = Field(..., gt=0)
    price_per_seat: float = Field(..., gt=0)
    vehicle_details: str
    driver_preferences: Optional[str] = None

class TripCreate(TripBase):
    driver_id: str

class TripResponse(TripBase):
    id: str
    driver_id: str
    status: TripStatus
    current_bookings: int = 0
    created_at: datetime

# Chat Models
class ChatMessage(BaseModel):
    id: Optional[str] = None
    user_id: str
    message: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    is_bot: bool = False
    context: Optional[Dict[str, Any]] = None

class ChatSession(BaseModel):
    id: Optional[str] = None
    user_id: str
    messages: List[ChatMessage] = []
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

# Search Models
class SearchFilters(BaseModel):
    source_city: str
    destination_city: str
    travel_date: datetime
    passenger_count: int = Field(1, gt=0, le=10)
    vehicle_type: Optional[VehicleType] = None
    max_price: Optional[float] = None
    departure_time_start: Optional[str] = None  # HH:MM format
    departure_time_end: Optional[str] = None    # HH:MM format
    amenities: Optional[List[str]] = None

class SearchResult(BaseModel):
    vehicle_id: str
    operator_name: str
    vehicle_details: VehicleResponse
    route: Route
    departure_time: datetime
    arrival_time: datetime
    available_seats: int
    price_per_seat: float
    total_price: float
    amenities: List[str]
    rating: Optional[float] = None
    reviews_count: int = 0

# Notification Models
class NotificationBase(BaseModel):
    title: str
    message: str
    type: str = Field(..., regex=r"^(booking|payment|trip|general)$")

class NotificationCreate(NotificationBase):
    user_id: str
    data: Optional[Dict[str, Any]] = None

class NotificationResponse(NotificationBase):
    id: str
    user_id: str
    is_read: bool = False
    data: Optional[Dict[str, Any]] = None
    created_at: datetime

# Rating and Review Models
class ReviewBase(BaseModel):
    rating: int = Field(..., ge=1, le=5)
    comment: Optional[str] = Field(None, max_length=500)

class ReviewCreate(ReviewBase):
    booking_id: str
    driver_id: Optional[str] = None
    vehicle_id: Optional[str] = None

class ReviewResponse(ReviewBase):
    id: str
    user_id: str
    booking_id: str
    driver_id: Optional[str] = None
    vehicle_id: Optional[str] = None
    created_at: datetime

# Utility functions
def generate_booking_reference() -> str:
    """Generate unique booking reference"""
    return f"TRV{uuid.uuid4().hex[:8].upper()}"

def generate_transaction_id() -> str:
    """Generate unique transaction ID"""
    return f"TXN{uuid.uuid4().hex[:12].upper()}"
