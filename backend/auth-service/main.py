# Authentication Service API for TravelAllinOne

from fastapi import <PERSON>AP<PERSON>, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTT<PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, EmailStr
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
import sys
import os

# Add shared modules to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'shared'))

from models import (
    UserCreate, UserResponse, UserUpdate, UserRole, BaseResponse,
    PaginationParams, PaginatedResponse
)
from utils import (
    AuthUtils, get_mysql, get_redis, setup_logging,
    TravelAllinOneException, ValidationUtils
)

# Initialize FastAPI app
app = FastAPI(
    title="TravelAllinOne Auth Service",
    description="Authentication and User Management Service",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Setup logging
logger = setup_logging("auth-service")

# Security
security = HTTPBearer()

# Request/Response Models
class LoginRequest(BaseModel):
    email: EmailStr
    password: str

class LoginResponse(BaseResponse):
    data: Optional[Dict[str, Any]] = None

class TokenRefreshRequest(BaseModel):
    refresh_token: str

class PasswordResetRequest(BaseModel):
    email: EmailStr

class PasswordResetConfirm(BaseModel):
    token: str
    new_password: str

class ChangePasswordRequest(BaseModel):
    current_password: str
    new_password: str

# Database operations
class UserRepository:
    def __init__(self, mysql_pool):
        self.pool = mysql_pool
    
    async def create_user(self, user_data: UserCreate) -> str:
        """Create new user"""
        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                # Check if user already exists
                await cursor.execute(
                    "SELECT id FROM users WHERE email = %s OR phone = %s",
                    (user_data.email, user_data.phone)
                )
                if await cursor.fetchone():
                    raise TravelAllinOneException("User already exists", 409)
                
                # Hash password
                hashed_password = AuthUtils.hash_password(user_data.password)
                
                # Insert user
                query = """
                INSERT INTO users (email, phone, first_name, last_name, 
                                 password_hash, role, date_of_birth, gender, 
                                 created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                now = datetime.utcnow()
                await cursor.execute(query, (
                    user_data.email, user_data.phone, user_data.first_name,
                    user_data.last_name, hashed_password, user_data.role.value,
                    user_data.date_of_birth, user_data.gender, now, now
                ))
                
                user_id = cursor.lastrowid
                await conn.commit()
                return str(user_id)
    
    async def get_user_by_email(self, email: str) -> Optional[Dict]:
        """Get user by email"""
        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                query = """
                SELECT id, email, phone, first_name, last_name, password_hash,
                       role, date_of_birth, gender, is_active, is_verified,
                       created_at, updated_at
                FROM users WHERE email = %s
                """
                await cursor.execute(query, (email,))
                row = await cursor.fetchone()
                
                if row:
                    return {
                        'id': str(row[0]),
                        'email': row[1],
                        'phone': row[2],
                        'first_name': row[3],
                        'last_name': row[4],
                        'password_hash': row[5],
                        'role': row[6],
                        'date_of_birth': row[7],
                        'gender': row[8],
                        'is_active': bool(row[9]),
                        'is_verified': bool(row[10]),
                        'created_at': row[11],
                        'updated_at': row[12]
                    }
                return None
    
    async def get_user_by_id(self, user_id: str) -> Optional[Dict]:
        """Get user by ID"""
        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                query = """
                SELECT id, email, phone, first_name, last_name, role,
                       date_of_birth, gender, is_active, is_verified,
                       created_at, updated_at
                FROM users WHERE id = %s
                """
                await cursor.execute(query, (user_id,))
                row = await cursor.fetchone()
                
                if row:
                    return {
                        'id': str(row[0]),
                        'email': row[1],
                        'phone': row[2],
                        'first_name': row[3],
                        'last_name': row[4],
                        'role': row[5],
                        'date_of_birth': row[6],
                        'gender': row[7],
                        'is_active': bool(row[8]),
                        'is_verified': bool(row[9]),
                        'created_at': row[10],
                        'updated_at': row[11]
                    }
                return None
    
    async def update_user(self, user_id: str, user_data: UserUpdate) -> bool:
        """Update user information"""
        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                # Build dynamic update query
                update_fields = []
                values = []
                
                for field, value in user_data.dict(exclude_unset=True).items():
                    if value is not None:
                        update_fields.append(f"{field} = %s")
                        values.append(value)
                
                if not update_fields:
                    return True
                
                update_fields.append("updated_at = %s")
                values.append(datetime.utcnow())
                values.append(user_id)
                
                query = f"UPDATE users SET {', '.join(update_fields)} WHERE id = %s"
                await cursor.execute(query, values)
                await conn.commit()
                
                return cursor.rowcount > 0

# API Endpoints
@app.on_event("startup")
async def startup_event():
    """Initialize database connections"""
    logger.info("Starting Auth Service...")

@app.on_event("shutdown")
async def shutdown_event():
    """Close database connections"""
    logger.info("Shutting down Auth Service...")

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "auth-service", "timestamp": datetime.utcnow()}

@app.post("/register", response_model=BaseResponse)
async def register_user(user_data: UserCreate, mysql_pool = Depends(get_mysql)):
    """Register new user"""
    try:
        # Validate phone number
        if not ValidationUtils.validate_indian_phone(user_data.phone):
            raise TravelAllinOneException("Invalid Indian phone number format", 400)
        
        user_repo = UserRepository(mysql_pool)
        user_id = await user_repo.create_user(user_data)
        
        logger.info(f"User registered successfully: {user_id}")
        return BaseResponse(
            message="User registered successfully",
            data={"user_id": user_id}
        )
    
    except TravelAllinOneException as e:
        raise HTTPException(status_code=e.status_code, detail=e.message)
    except Exception as e:
        logger.error(f"Registration error: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.post("/login", response_model=LoginResponse)
async def login_user(login_data: LoginRequest, mysql_pool = Depends(get_mysql)):
    """User login"""
    try:
        user_repo = UserRepository(mysql_pool)
        user = await user_repo.get_user_by_email(login_data.email)
        
        if not user or not AuthUtils.verify_password(login_data.password, user['password_hash']):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid email or password"
            )
        
        if not user['is_active']:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Account is deactivated"
            )
        
        # Create access token
        token_data = {
            "user_id": user['id'],
            "email": user['email'],
            "role": user['role']
        }
        access_token = AuthUtils.create_access_token(token_data)
        
        # Remove sensitive data
        user_response = {k: v for k, v in user.items() if k != 'password_hash'}
        
        logger.info(f"User logged in: {user['id']}")
        return LoginResponse(
            message="Login successful",
            data={
                "access_token": access_token,
                "token_type": "bearer",
                "user": user_response
            }
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login error: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.get("/profile", response_model=BaseResponse)
async def get_user_profile(
    current_user: Dict = Depends(AuthUtils.verify_token),
    mysql_pool = Depends(get_mysql)
):
    """Get current user profile"""
    try:
        user_repo = UserRepository(mysql_pool)
        user = await user_repo.get_user_by_id(current_user['user_id'])
        
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        return BaseResponse(
            message="Profile retrieved successfully",
            data={"user": user}
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Profile retrieval error: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.put("/profile", response_model=BaseResponse)
async def update_user_profile(
    user_data: UserUpdate,
    current_user: Dict = Depends(AuthUtils.verify_token),
    mysql_pool = Depends(get_mysql)
):
    """Update user profile"""
    try:
        # Validate phone number if provided
        if user_data.phone and not ValidationUtils.validate_indian_phone(user_data.phone):
            raise TravelAllinOneException("Invalid Indian phone number format", 400)
        
        user_repo = UserRepository(mysql_pool)
        success = await user_repo.update_user(current_user['user_id'], user_data)
        
        if not success:
            raise HTTPException(status_code=404, detail="User not found")
        
        logger.info(f"Profile updated: {current_user['user_id']}")
        return BaseResponse(message="Profile updated successfully")
    
    except TravelAllinOneException as e:
        raise HTTPException(status_code=e.status_code, detail=e.message)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Profile update error: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
