# Chatbot Service API for TravelAllinOne with Pinecone Integration

from fastapi import FastAP<PERSON>, HTTPException, Depends, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from datetime import datetime
from typing import Optional, List, Dict, Any
import sys
import os
import openai
import pinecone
import json
import asyncio

# Add shared modules to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'shared'))

from models import (
    BaseResponse, ChatMessage, ChatSession
)
from utils import (
    get_current_user, get_mongodb, get_redis,
    setup_logging
)

# Initialize FastAPI app
app = FastAPI(
    title="TravelAllinOne Chatbot Service",
    description="AI-Powered Chatbot Service with Pinecone Vector Database",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Setup logging
logger = setup_logging("chatbot-service")

# Configuration
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "your_openai_api_key")
PINECONE_API_KEY = os.getenv("PINECONE_API_KEY", "your_pinecone_api_key")
PINECONE_ENVIRONMENT = os.getenv("PINECONE_ENVIRONMENT", "your_pinecone_environment")

# Initialize OpenAI
openai.api_key = OPENAI_API_KEY

# Initialize Pinecone
pinecone.init(api_key=PINECONE_API_KEY, environment=PINECONE_ENVIRONMENT)

# Create or connect to Pinecone index
INDEX_NAME = "travelallinone-knowledge"
try:
    if INDEX_NAME not in pinecone.list_indexes():
        pinecone.create_index(
            name=INDEX_NAME,
            dimension=1536,  # OpenAI embedding dimension
            metric="cosine"
        )
    pinecone_index = pinecone.Index(INDEX_NAME)
except Exception as e:
    logger.error(f"Pinecone initialization error: {str(e)}")
    pinecone_index = None

# Request/Response Models
class ChatRequest(BaseModel):
    message: str = Field(..., min_length=1, max_length=1000)
    session_id: Optional[str] = None
    context: Optional[Dict[str, Any]] = None

class ChatResponse(BaseModel):
    message: str
    session_id: str
    suggestions: List[str] = []
    context: Optional[Dict[str, Any]] = None

class KnowledgeBaseEntry(BaseModel):
    title: str
    content: str
    category: str = Field(..., regex="^(bus|carpool|bike|payment|general)$")
    tags: List[str] = []
    metadata: Optional[Dict[str, Any]] = None

class FeedbackRequest(BaseModel):
    session_id: str
    message_id: str
    rating: int = Field(..., ge=1, le=5)
    feedback: Optional[str] = None

# WebSocket connection manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
    
    async def connect(self, websocket: WebSocket, user_id: str):
        await websocket.accept()
        self.active_connections[user_id] = websocket
    
    def disconnect(self, user_id: str):
        if user_id in self.active_connections:
            del self.active_connections[user_id]
    
    async def send_message(self, user_id: str, message: dict):
        if user_id in self.active_connections:
            await self.active_connections[user_id].send_text(json.dumps(message))

manager = ConnectionManager()

# Database operations
class ChatbotRepository:
    def __init__(self, mongo_client, redis_client):
        self.mongo_db = mongo_client.travelallinone
        self.redis = redis_client
    
    async def get_or_create_session(self, user_id: str, session_id: Optional[str] = None) -> str:
        """Get existing session or create new one"""
        if session_id:
            # Check if session exists
            session = await self.mongo_db.chat_sessions.find_one({"_id": session_id, "user_id": user_id})
            if session:
                return session_id
        
        # Create new session
        session_data = {
            "user_id": user_id,
            "messages": [],
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
        
        result = await self.mongo_db.chat_sessions.insert_one(session_data)
        return str(result.inserted_id)
    
    async def add_message_to_session(self, session_id: str, message: ChatMessage):
        """Add message to chat session"""
        await self.mongo_db.chat_sessions.update_one(
            {"_id": session_id},
            {
                "$push": {"messages": message.dict()},
                "$set": {"updated_at": datetime.utcnow()}
            }
        )
    
    async def get_session_history(self, session_id: str, limit: int = 10) -> List[ChatMessage]:
        """Get recent messages from session"""
        session = await self.mongo_db.chat_sessions.find_one({"_id": session_id})
        if not session:
            return []
        
        messages = session.get("messages", [])[-limit:]
        return [ChatMessage(**msg) for msg in messages]
    
    async def search_knowledge_base(self, query: str, limit: int = 5) -> List[Dict[str, Any]]:
        """Search knowledge base using vector similarity"""
        try:
            if not pinecone_index:
                return []
            
            # Generate embedding for query
            response = openai.Embedding.create(
                input=query,
                model="text-embedding-ada-002"
            )
            query_embedding = response['data'][0]['embedding']
            
            # Search in Pinecone
            search_results = pinecone_index.query(
                vector=query_embedding,
                top_k=limit,
                include_metadata=True
            )
            
            results = []
            for match in search_results['matches']:
                if match['score'] > 0.7:  # Similarity threshold
                    results.append({
                        "content": match['metadata'].get('content', ''),
                        "title": match['metadata'].get('title', ''),
                        "category": match['metadata'].get('category', ''),
                        "score": match['score']
                    })
            
            return results
        
        except Exception as e:
            logger.error(f"Knowledge base search error: {str(e)}")
            return []
    
    async def add_to_knowledge_base(self, entry: KnowledgeBaseEntry) -> str:
        """Add entry to knowledge base"""
        try:
            if not pinecone_index:
                raise HTTPException(status_code=500, detail="Pinecone not available")
            
            # Generate embedding
            response = openai.Embedding.create(
                input=f"{entry.title} {entry.content}",
                model="text-embedding-ada-002"
            )
            embedding = response['data'][0]['embedding']
            
            # Generate unique ID
            import uuid
            entry_id = str(uuid.uuid4())
            
            # Store in Pinecone
            pinecone_index.upsert([
                (entry_id, embedding, {
                    "title": entry.title,
                    "content": entry.content,
                    "category": entry.category,
                    "tags": entry.tags,
                    "created_at": datetime.utcnow().isoformat()
                })
            ])
            
            # Also store in MongoDB for management
            await self.mongo_db.knowledge_base.insert_one({
                "_id": entry_id,
                **entry.dict(),
                "created_at": datetime.utcnow()
            })
            
            return entry_id
        
        except Exception as e:
            logger.error(f"Knowledge base addition error: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to add to knowledge base")

class ChatbotService:
    def __init__(self, repository: ChatbotRepository):
        self.repository = repository
    
    async def generate_response(self, user_message: str, user_id: str, session_id: str) -> ChatResponse:
        """Generate AI response to user message"""
        try:
            # Get session history for context
            history = await self.repository.get_session_history(session_id, limit=5)
            
            # Search knowledge base for relevant information
            kb_results = await self.repository.search_knowledge_base(user_message)
            
            # Build context for AI
            context_messages = [
                {"role": "system", "content": self._get_system_prompt()},
            ]
            
            # Add knowledge base context
            if kb_results:
                kb_context = "\n".join([f"- {result['content']}" for result in kb_results[:3]])
                context_messages.append({
                    "role": "system", 
                    "content": f"Relevant information from knowledge base:\n{kb_context}"
                })
            
            # Add conversation history
            for msg in history[-4:]:  # Last 4 messages for context
                role = "assistant" if msg.is_bot else "user"
                context_messages.append({"role": role, "content": msg.message})
            
            # Add current user message
            context_messages.append({"role": "user", "content": user_message})
            
            # Generate response using OpenAI
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=context_messages,
                max_tokens=500,
                temperature=0.7
            )
            
            ai_response = response.choices[0].message.content
            
            # Generate suggestions based on context
            suggestions = self._generate_suggestions(user_message, kb_results)
            
            # Save messages to session
            user_msg = ChatMessage(
                user_id=user_id,
                message=user_message,
                is_bot=False
            )
            bot_msg = ChatMessage(
                user_id=user_id,
                message=ai_response,
                is_bot=True
            )
            
            await self.repository.add_message_to_session(session_id, user_msg)
            await self.repository.add_message_to_session(session_id, bot_msg)
            
            return ChatResponse(
                message=ai_response,
                session_id=session_id,
                suggestions=suggestions
            )
        
        except Exception as e:
            logger.error(f"Response generation error: {str(e)}")
            return ChatResponse(
                message="I'm sorry, I'm having trouble processing your request right now. Please try again later.",
                session_id=session_id,
                suggestions=["How can I book a bus ticket?", "What payment methods do you accept?"]
            )
    
    def _get_system_prompt(self) -> str:
        """Get system prompt for AI"""
        return """
        You are a helpful travel assistant for TravelAllinOne, a comprehensive travel booking platform in India.
        You can help users with:
        - Bus ticket bookings (like AbhiBus)
        - Car pooling services (like BlaBlaCar)
        - Bike ride sharing (like Rapido)
        - Payment and booking issues
        - General travel information
        
        Always be helpful, friendly, and provide accurate information about Indian travel.
        Use Indian Rupees (₹) for pricing and mention Indian cities and routes.
        If you don't know something specific, ask the user for more details or suggest contacting customer support.
        """
    
    def _generate_suggestions(self, user_message: str, kb_results: List[Dict]) -> List[str]:
        """Generate contextual suggestions"""
        suggestions = []
        
        # Default suggestions based on message content
        if any(word in user_message.lower() for word in ['bus', 'ticket', 'booking']):
            suggestions.extend([
                "How do I cancel my bus booking?",
                "What are the available bus routes?",
                "How can I select my seat?"
            ])
        elif any(word in user_message.lower() for word in ['carpool', 'share', 'ride']):
            suggestions.extend([
                "How does car pooling work?",
                "How to create a carpool trip?",
                "What are the safety measures?"
            ])
        elif any(word in user_message.lower() for word in ['bike', 'rapido', 'quick']):
            suggestions.extend([
                "How to book a bike ride?",
                "What are the bike ride charges?",
                "How long does it take to find a driver?"
            ])
        elif any(word in user_message.lower() for word in ['payment', 'refund', 'money']):
            suggestions.extend([
                "How to get a refund?",
                "What payment methods are accepted?",
                "How long does refund take?"
            ])
        else:
            suggestions.extend([
                "How can I book a bus ticket?",
                "Tell me about car pooling",
                "How does bike sharing work?",
                "What are your payment options?"
            ])
        
        return suggestions[:3]  # Return top 3 suggestions

# API Endpoints
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "chatbot-service", "timestamp": datetime.utcnow()}

@app.post("/chat", response_model=BaseResponse)
async def chat(
    chat_request: ChatRequest,
    current_user: Dict = Depends(get_current_user),
    mongo_client = Depends(get_mongodb),
    redis_client = Depends(get_redis)
):
    """Process chat message"""
    try:
        chatbot_repo = ChatbotRepository(mongo_client, redis_client)
        chatbot_service = ChatbotService(chatbot_repo)
        
        # Get or create session
        session_id = await chatbot_repo.get_or_create_session(
            current_user['user_id'], chat_request.session_id
        )
        
        # Generate response
        response = await chatbot_service.generate_response(
            chat_request.message, current_user['user_id'], session_id
        )
        
        return BaseResponse(
            message="Response generated successfully",
            data=response.dict()
        )
    
    except Exception as e:
        logger.error(f"Chat error: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.websocket("/ws/{user_id}")
async def websocket_endpoint(websocket: WebSocket, user_id: str):
    """WebSocket endpoint for real-time chat"""
    await manager.connect(websocket, user_id)
    try:
        while True:
            data = await websocket.receive_text()
            message_data = json.loads(data)
            
            # Process message (similar to chat endpoint)
            # This would integrate with the chat processing logic
            
            # Send response back
            response = {
                "type": "message",
                "content": "This is a WebSocket response",
                "timestamp": datetime.utcnow().isoformat()
            }
            await manager.send_message(user_id, response)
            
    except WebSocketDisconnect:
        manager.disconnect(user_id)

@app.post("/knowledge-base", response_model=BaseResponse)
async def add_knowledge_base_entry(
    entry: KnowledgeBaseEntry,
    current_user: Dict = Depends(get_current_user),
    mongo_client = Depends(get_mongodb),
    redis_client = Depends(get_redis)
):
    """Add entry to knowledge base (Admin only)"""
    try:
        if current_user.get('role') != 'admin':
            raise HTTPException(status_code=403, detail="Admin access required")
        
        chatbot_repo = ChatbotRepository(mongo_client, redis_client)
        entry_id = await chatbot_repo.add_to_knowledge_base(entry)
        
        return BaseResponse(
            message="Knowledge base entry added successfully",
            data={"entry_id": entry_id}
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Knowledge base addition error: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
