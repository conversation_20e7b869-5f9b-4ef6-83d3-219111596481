# Payment Service API for TravelAllinOne

from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from datetime import datetime
from typing import Optional, Dict, Any, List
import sys
import os
import razorpay
import hashlib
import hmac

# Add shared modules to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'shared'))

from models import (
    BaseResponse, PaymentCreate, PaymentResponse, PaymentStatus
)
from utils import (
    get_current_user, get_mysql, get_redis,
    setup_logging, generate_transaction_id
)

# Initialize FastAPI app
app = FastAPI(
    title="TravelAllinOne Payment Service",
    description="Payment Processing Service with Razorpay Integration",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Setup logging
logger = setup_logging("payment-service")

# Razorpay configuration
RAZORPAY_KEY_ID = os.getenv("RAZORPAY_KEY_ID", "your_razorpay_key_id")
RAZORPAY_KEY_SECRET = os.getenv("RAZORPAY_KEY_SECRET", "your_razorpay_key_secret")

# Initialize Razorpay client
razorpay_client = razorpay.Client(auth=(RAZORPAY_KEY_ID, RAZORPAY_KEY_SECRET))

# Request/Response Models
class PaymentInitiateRequest(BaseModel):
    booking_id: str
    amount: float = Field(..., gt=0)
    currency: str = "INR"
    description: Optional[str] = None
    customer_email: Optional[str] = None
    customer_phone: Optional[str] = None

class PaymentInitiateResponse(BaseModel):
    order_id: str
    amount: float
    currency: str
    key_id: str
    payment_url: Optional[str] = None

class PaymentVerificationRequest(BaseModel):
    razorpay_order_id: str
    razorpay_payment_id: str
    razorpay_signature: str

class PaymentWebhookRequest(BaseModel):
    event: str
    payload: Dict[str, Any]

class RefundRequest(BaseModel):
    payment_id: str
    amount: Optional[float] = None  # Full refund if not specified
    reason: Optional[str] = None

class PaymentHistoryFilter(BaseModel):
    booking_id: Optional[str] = None
    status: Optional[PaymentStatus] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    limit: int = Field(10, ge=1, le=100)
    offset: int = Field(0, ge=0)

# Database operations
class PaymentRepository:
    def __init__(self, mysql_pool):
        self.pool = mysql_pool
    
    async def create_payment_order(self, user_id: str, payment_data: PaymentInitiateRequest) -> Dict[str, Any]:
        """Create payment order in Razorpay and database"""
        try:
            # Create Razorpay order
            order_data = {
                "amount": int(payment_data.amount * 100),  # Amount in paise
                "currency": payment_data.currency,
                "receipt": f"booking_{payment_data.booking_id}",
                "notes": {
                    "booking_id": payment_data.booking_id,
                    "user_id": user_id
                }
            }
            
            razorpay_order = razorpay_client.order.create(data=order_data)
            
            # Store in database
            async with self.pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    transaction_id = generate_transaction_id()
                    query = """
                    INSERT INTO payments (user_id, booking_id, transaction_id, 
                                        razorpay_order_id, amount, currency, 
                                        status, created_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                    """
                    
                    await cursor.execute(query, (
                        user_id, payment_data.booking_id, transaction_id,
                        razorpay_order["id"], payment_data.amount,
                        payment_data.currency, PaymentStatus.PENDING.value,
                        datetime.utcnow()
                    ))
                    
                    payment_id = cursor.lastrowid
                    await conn.commit()
                    
                    return {
                        "payment_id": str(payment_id),
                        "order_id": razorpay_order["id"],
                        "amount": payment_data.amount,
                        "currency": payment_data.currency
                    }
        
        except Exception as e:
            logger.error(f"Payment order creation error: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to create payment order")
    
    async def verify_payment(self, verification_data: PaymentVerificationRequest) -> bool:
        """Verify Razorpay payment signature"""
        try:
            # Verify signature
            generated_signature = hmac.new(
                RAZORPAY_KEY_SECRET.encode(),
                f"{verification_data.razorpay_order_id}|{verification_data.razorpay_payment_id}".encode(),
                hashlib.sha256
            ).hexdigest()
            
            if generated_signature != verification_data.razorpay_signature:
                return False
            
            # Update payment status in database
            async with self.pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    query = """
                    UPDATE payments 
                    SET razorpay_payment_id = %s, status = %s, 
                        verified_at = %s, updated_at = %s
                    WHERE razorpay_order_id = %s
                    """
                    
                    await cursor.execute(query, (
                        verification_data.razorpay_payment_id,
                        PaymentStatus.SUCCESS.value,
                        datetime.utcnow(),
                        datetime.utcnow(),
                        verification_data.razorpay_order_id
                    ))
                    
                    await conn.commit()
                    return cursor.rowcount > 0
        
        except Exception as e:
            logger.error(f"Payment verification error: {str(e)}")
            return False
    
    async def get_payment_by_order_id(self, order_id: str) -> Optional[Dict[str, Any]]:
        """Get payment details by Razorpay order ID"""
        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                query = """
                SELECT id, user_id, booking_id, transaction_id, razorpay_order_id,
                       razorpay_payment_id, amount, currency, status, created_at,
                       verified_at, updated_at
                FROM payments WHERE razorpay_order_id = %s
                """
                
                await cursor.execute(query, (order_id,))
                row = await cursor.fetchone()
                
                if row:
                    return {
                        "id": str(row[0]),
                        "user_id": str(row[1]),
                        "booking_id": str(row[2]),
                        "transaction_id": row[3],
                        "razorpay_order_id": row[4],
                        "razorpay_payment_id": row[5],
                        "amount": float(row[6]),
                        "currency": row[7],
                        "status": row[8],
                        "created_at": row[9],
                        "verified_at": row[10],
                        "updated_at": row[11]
                    }
                return None
    
    async def get_user_payments(self, user_id: str, filters: PaymentHistoryFilter) -> List[Dict[str, Any]]:
        """Get user payment history"""
        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                query = """
                SELECT p.id, p.booking_id, p.transaction_id, p.amount, p.currency,
                       p.status, p.created_at, p.verified_at,
                       CASE 
                         WHEN bb.id IS NOT NULL THEN 'bus'
                         WHEN cb.id IS NOT NULL THEN 'carpool'
                         WHEN bkb.id IS NOT NULL THEN 'bike'
                         ELSE 'unknown'
                       END as booking_type
                FROM payments p
                LEFT JOIN bus_bookings bb ON p.booking_id = bb.id
                LEFT JOIN carpool_bookings cb ON p.booking_id = cb.id
                LEFT JOIN bike_bookings bkb ON p.booking_id = bkb.id
                WHERE p.user_id = %s
                """
                
                params = [user_id]
                
                # Add filters
                if filters.booking_id:
                    query += " AND p.booking_id = %s"
                    params.append(filters.booking_id)
                
                if filters.status:
                    query += " AND p.status = %s"
                    params.append(filters.status.value)
                
                if filters.start_date:
                    query += " AND p.created_at >= %s"
                    params.append(filters.start_date)
                
                if filters.end_date:
                    query += " AND p.created_at <= %s"
                    params.append(filters.end_date)
                
                query += " ORDER BY p.created_at DESC LIMIT %s OFFSET %s"
                params.extend([filters.limit, filters.offset])
                
                await cursor.execute(query, params)
                rows = await cursor.fetchall()
                
                payments = []
                for row in rows:
                    payment = {
                        "id": str(row[0]),
                        "booking_id": str(row[1]),
                        "transaction_id": row[2],
                        "amount": float(row[3]),
                        "currency": row[4],
                        "status": row[5],
                        "booking_type": row[8],
                        "created_at": row[6],
                        "verified_at": row[7]
                    }
                    payments.append(payment)
                
                return payments
    
    async def process_refund(self, payment_id: str, refund_data: RefundRequest) -> Dict[str, Any]:
        """Process payment refund"""
        try:
            # Get payment details
            async with self.pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(
                        "SELECT razorpay_payment_id, amount FROM payments WHERE id = %s",
                        (payment_id,)
                    )
                    payment = await cursor.fetchone()
                    
                    if not payment:
                        raise HTTPException(status_code=404, detail="Payment not found")
                    
                    # Calculate refund amount
                    refund_amount = refund_data.amount or float(payment[1])
                    
                    # Create refund in Razorpay
                    refund_data_rp = {
                        "amount": int(refund_amount * 100),  # Amount in paise
                        "notes": {
                            "reason": refund_data.reason or "Customer request"
                        }
                    }
                    
                    refund = razorpay_client.payment.refund(
                        payment[0], refund_data_rp
                    )
                    
                    # Update payment status
                    await cursor.execute("""
                        UPDATE payments 
                        SET status = %s, refund_id = %s, refund_amount = %s,
                            refunded_at = %s, updated_at = %s
                        WHERE id = %s
                    """, (
                        PaymentStatus.REFUNDED.value,
                        refund["id"],
                        refund_amount,
                        datetime.utcnow(),
                        datetime.utcnow(),
                        payment_id
                    ))
                    
                    await conn.commit()
                    
                    return {
                        "refund_id": refund["id"],
                        "amount": refund_amount,
                        "status": refund["status"]
                    }
        
        except Exception as e:
            logger.error(f"Refund processing error: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to process refund")

# API Endpoints
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "payment-service", "timestamp": datetime.utcnow()}

@app.post("/initiate", response_model=BaseResponse)
async def initiate_payment(
    payment_data: PaymentInitiateRequest,
    current_user: Dict = Depends(get_current_user),
    mysql_pool = Depends(get_mysql)
):
    """Initiate payment process"""
    try:
        payment_repo = PaymentRepository(mysql_pool)
        order_data = await payment_repo.create_payment_order(
            current_user['user_id'], payment_data
        )
        
        response_data = PaymentInitiateResponse(
            order_id=order_data["order_id"],
            amount=order_data["amount"],
            currency=order_data["currency"],
            key_id=RAZORPAY_KEY_ID
        )
        
        logger.info(f"Payment initiated: {order_data['payment_id']}")
        return BaseResponse(
            message="Payment initiated successfully",
            data=response_data.dict()
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Payment initiation error: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.post("/verify", response_model=BaseResponse)
async def verify_payment(
    verification_data: PaymentVerificationRequest,
    mysql_pool = Depends(get_mysql)
):
    """Verify payment after successful transaction"""
    try:
        payment_repo = PaymentRepository(mysql_pool)
        is_verified = await payment_repo.verify_payment(verification_data)
        
        if not is_verified:
            raise HTTPException(status_code=400, detail="Payment verification failed")
        
        # Get updated payment details
        payment = await payment_repo.get_payment_by_order_id(
            verification_data.razorpay_order_id
        )
        
        logger.info(f"Payment verified: {verification_data.razorpay_payment_id}")
        return BaseResponse(
            message="Payment verified successfully",
            data={"payment": payment}
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Payment verification error: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.get("/history", response_model=BaseResponse)
async def get_payment_history(
    booking_id: Optional[str] = None,
    status: Optional[PaymentStatus] = None,
    limit: int = 10,
    offset: int = 0,
    current_user: Dict = Depends(get_current_user),
    mysql_pool = Depends(get_mysql)
):
    """Get user payment history"""
    try:
        filters = PaymentHistoryFilter(
            booking_id=booking_id,
            status=status,
            limit=limit,
            offset=offset
        )
        
        payment_repo = PaymentRepository(mysql_pool)
        payments = await payment_repo.get_user_payments(
            current_user['user_id'], filters
        )
        
        return BaseResponse(
            message=f"Retrieved {len(payments)} payments",
            data={"payments": payments}
        )
    
    except Exception as e:
        logger.error(f"Payment history error: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.post("/refund/{payment_id}", response_model=BaseResponse)
async def process_refund(
    payment_id: str,
    refund_data: RefundRequest,
    current_user: Dict = Depends(get_current_user),
    mysql_pool = Depends(get_mysql)
):
    """Process payment refund"""
    try:
        payment_repo = PaymentRepository(mysql_pool)
        refund_result = await payment_repo.process_refund(payment_id, refund_data)
        
        logger.info(f"Refund processed: {refund_result['refund_id']}")
        return BaseResponse(
            message="Refund processed successfully",
            data={"refund": refund_result}
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Refund error: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
