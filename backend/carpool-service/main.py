# Car Pooling Service API for TravelAllinOne (Inspired by BlaBlaCar)

from fastapi import FastAPI, HTTPException, Depends, Query
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, <PERSON>
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
import sys
import os

# Add shared modules to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'shared'))

from models import (
    BaseResponse, PaginatedResponse, PaginationParams,
    TripCreate, TripResponse, TripStatus, Location, Route,
    BookingCreate, BookingResponse, BookingStatus
)
from utils import (
    get_current_user, get_mysql, get_mongodb, get_redis,
    setup_logging, PricingUtils, LocationUtils, generate_booking_reference
)

# Initialize FastAPI app
app = FastAPI(
    title="TravelAllinOne CarPool Service",
    description="Car Pooling Service inspired by BlaBlaCar",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Setup logging
logger = setup_logging("carpool-service")

# Request/Response Models
class CarPoolSearchRequest(BaseModel):
    source_city: str = Field(..., min_length=2, max_length=50)
    destination_city: str = Field(..., min_length=2, max_length=50)
    travel_date: datetime
    passenger_count: int = Field(1, ge=1, le=6)
    departure_time_start: Optional[str] = Field(None, regex="^([01]?[0-9]|2[0-3]):[0-5][0-9]$")
    departure_time_end: Optional[str] = Field(None, regex="^([01]?[0-9]|2[0-3]):[0-5][0-9]$")
    max_price_per_seat: Optional[float] = Field(None, ge=0)
    smoking_allowed: Optional[bool] = None
    pets_allowed: Optional[bool] = None

class CarPoolTripCreate(BaseModel):
    source: Location
    destination: Location
    departure_time: datetime
    available_seats: int = Field(..., ge=1, le=6)
    price_per_seat: float = Field(..., gt=0)
    vehicle_make: str = Field(..., min_length=2, max_length=50)
    vehicle_model: str = Field(..., min_length=2, max_length=50)
    vehicle_color: str = Field(..., min_length=2, max_length=30)
    vehicle_number: str = Field(..., regex=r"^[A-Z]{2}[0-9]{2}[A-Z]{2}[0-9]{4}$")
    smoking_allowed: bool = False
    pets_allowed: bool = False
    music_allowed: bool = True
    max_two_in_back: bool = False
    driver_preferences: Optional[str] = Field(None, max_length=500)
    pickup_points: List[Location] = []
    drop_points: List[Location] = []

class CarPoolTrip(BaseModel):
    id: str
    driver_id: str
    driver_name: str
    driver_rating: Optional[float] = None
    driver_reviews_count: int = 0
    route: Route
    departure_time: datetime
    available_seats: int
    price_per_seat: float
    total_price: float
    vehicle_details: Dict[str, str]
    preferences: Dict[str, bool]
    driver_preferences: Optional[str] = None
    pickup_points: List[Location] = []
    drop_points: List[Location] = []
    status: TripStatus

class CarPoolBookingRequest(BaseModel):
    trip_id: str
    passenger_count: int = Field(..., ge=1, le=6)
    pickup_point: Location
    drop_point: Location
    passenger_phone: str = Field(..., regex=r"^\+91[6-9]\d{9}$")
    message_to_driver: Optional[str] = Field(None, max_length=200)

class DriverProfile(BaseModel):
    user_id: str
    license_number: str = Field(..., min_length=10, max_length=20)
    license_expiry: datetime
    driving_experience_years: int = Field(..., ge=0, le=50)
    vehicle_documents: List[str] = []  # Document URLs
    verification_status: str = Field(default="pending", regex="^(pending|verified|rejected)$")
    rating: Optional[float] = None
    total_trips: int = 0
    bio: Optional[str] = Field(None, max_length=500)

# Database operations
class CarPoolRepository:
    def __init__(self, mysql_pool, mongo_client):
        self.mysql_pool = mysql_pool
        self.mongo_db = mongo_client.travelallinone
    
    async def create_trip(self, driver_id: str, trip_data: CarPoolTripCreate) -> str:
        """Create new carpool trip"""
        async with self.mysql_pool.acquire() as conn:
            async with conn.cursor() as cursor:
                # Calculate distance and estimated duration
                distance = LocationUtils.calculate_distance(
                    trip_data.source.latitude, trip_data.source.longitude,
                    trip_data.destination.latitude, trip_data.destination.longitude
                )
                duration = LocationUtils.estimate_travel_time(distance, "car")
                
                # Insert trip
                query = """
                INSERT INTO carpool_trips (driver_id, source_city, destination_city,
                                         source_lat, source_lng, dest_lat, dest_lng,
                                         departure_time, available_seats, price_per_seat,
                                         vehicle_details, preferences, driver_preferences,
                                         distance_km, estimated_duration_minutes, status,
                                         created_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                
                import json
                vehicle_details = {
                    "make": trip_data.vehicle_make,
                    "model": trip_data.vehicle_model,
                    "color": trip_data.vehicle_color,
                    "number": trip_data.vehicle_number
                }
                
                preferences = {
                    "smoking_allowed": trip_data.smoking_allowed,
                    "pets_allowed": trip_data.pets_allowed,
                    "music_allowed": trip_data.music_allowed,
                    "max_two_in_back": trip_data.max_two_in_back
                }
                
                await cursor.execute(query, (
                    driver_id, trip_data.source.city, trip_data.destination.city,
                    trip_data.source.latitude, trip_data.source.longitude,
                    trip_data.destination.latitude, trip_data.destination.longitude,
                    trip_data.departure_time, trip_data.available_seats,
                    trip_data.price_per_seat, json.dumps(vehicle_details),
                    json.dumps(preferences), trip_data.driver_preferences,
                    distance, duration, TripStatus.SCHEDULED.value,
                    datetime.utcnow()
                ))
                
                trip_id = cursor.lastrowid
                await conn.commit()
                return str(trip_id)
    
    async def search_trips(self, search_params: CarPoolSearchRequest) -> List[CarPoolTrip]:
        """Search available carpool trips"""
        async with self.mysql_pool.acquire() as conn:
            async with conn.cursor() as cursor:
                query = """
                SELECT ct.id, ct.driver_id, u.first_name, u.last_name,
                       ct.source_city, ct.destination_city, ct.source_lat, ct.source_lng,
                       ct.dest_lat, ct.dest_lng, ct.departure_time, ct.available_seats,
                       ct.price_per_seat, ct.vehicle_details, ct.preferences,
                       ct.driver_preferences, ct.distance_km, ct.estimated_duration_minutes,
                       ct.status, dp.rating, dp.total_trips
                FROM carpool_trips ct
                JOIN users u ON ct.driver_id = u.id
                LEFT JOIN driver_profiles dp ON ct.driver_id = dp.user_id
                WHERE ct.source_city = %s 
                  AND ct.destination_city = %s
                  AND DATE(ct.departure_time) = DATE(%s)
                  AND ct.available_seats >= %s
                  AND ct.status = 'scheduled'
                """
                
                params = [
                    search_params.source_city,
                    search_params.destination_city,
                    search_params.travel_date,
                    search_params.passenger_count
                ]
                
                # Add optional filters
                if search_params.max_price_per_seat:
                    query += " AND ct.price_per_seat <= %s"
                    params.append(search_params.max_price_per_seat)
                
                query += " ORDER BY ct.departure_time"
                
                await cursor.execute(query, params)
                rows = await cursor.fetchall()
                
                results = []
                for row in rows:
                    import json
                    vehicle_details = json.loads(row[13]) if row[13] else {}
                    preferences = json.loads(row[14]) if row[14] else {}
                    
                    trip = CarPoolTrip(
                        id=str(row[0]),
                        driver_id=str(row[1]),
                        driver_name=f"{row[2]} {row[3]}",
                        driver_rating=float(row[19]) if row[19] else None,
                        driver_reviews_count=row[20] or 0,
                        route=Route(
                            source=Location(
                                latitude=float(row[6]), longitude=float(row[7]),
                                address="", city=row[4], state="", pincode=""
                            ),
                            destination=Location(
                                latitude=float(row[8]), longitude=float(row[9]),
                                address="", city=row[5], state="", pincode=""
                            ),
                            distance_km=float(row[16]),
                            estimated_duration_minutes=row[17]
                        ),
                        departure_time=row[10],
                        available_seats=row[11],
                        price_per_seat=float(row[12]),
                        total_price=float(row[12]) * search_params.passenger_count,
                        vehicle_details=vehicle_details,
                        preferences=preferences,
                        driver_preferences=row[15],
                        status=TripStatus(row[18])
                    )
                    results.append(trip)
                
                return results
    
    async def book_trip(self, user_id: str, booking_data: CarPoolBookingRequest) -> str:
        """Book carpool trip"""
        async with self.mysql_pool.acquire() as conn:
            async with conn.cursor() as cursor:
                # Check trip availability
                await cursor.execute("""
                    SELECT available_seats, price_per_seat FROM carpool_trips 
                    WHERE id = %s AND status = 'scheduled'
                """, (booking_data.trip_id,))
                
                trip = await cursor.fetchone()
                if not trip or trip[0] < booking_data.passenger_count:
                    raise HTTPException(status_code=400, detail="Trip not available")
                
                # Calculate total amount
                total_amount = float(trip[1]) * booking_data.passenger_count
                
                # Create booking
                booking_ref = generate_booking_reference()
                query = """
                INSERT INTO carpool_bookings (user_id, trip_id, booking_reference,
                                            passenger_count, total_amount, status,
                                            pickup_lat, pickup_lng, drop_lat, drop_lng,
                                            passenger_phone, message_to_driver, created_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                
                await cursor.execute(query, (
                    user_id, booking_data.trip_id, booking_ref,
                    booking_data.passenger_count, total_amount,
                    BookingStatus.PENDING.value,
                    booking_data.pickup_point.latitude,
                    booking_data.pickup_point.longitude,
                    booking_data.drop_point.latitude,
                    booking_data.drop_point.longitude,
                    booking_data.passenger_phone,
                    booking_data.message_to_driver,
                    datetime.utcnow()
                ))
                
                booking_id = cursor.lastrowid
                
                # Update available seats
                await cursor.execute("""
                    UPDATE carpool_trips SET available_seats = available_seats - %s
                    WHERE id = %s
                """, (booking_data.passenger_count, booking_data.trip_id))
                
                await conn.commit()
                return str(booking_id)

# API Endpoints
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "carpool-service", "timestamp": datetime.utcnow()}

@app.post("/trips", response_model=BaseResponse)
async def create_trip(
    trip_data: CarPoolTripCreate,
    current_user: Dict = Depends(get_current_user),
    mysql_pool = Depends(get_mysql),
    mongo_client = Depends(get_mongodb)
):
    """Create new carpool trip"""
    try:
        carpool_repo = CarPoolRepository(mysql_pool, mongo_client)
        trip_id = await carpool_repo.create_trip(current_user['user_id'], trip_data)
        
        logger.info(f"Carpool trip created: {trip_id}")
        return BaseResponse(
            message="Trip created successfully",
            data={"trip_id": trip_id}
        )
    
    except Exception as e:
        logger.error(f"Trip creation error: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.post("/search", response_model=BaseResponse)
async def search_trips(
    search_params: CarPoolSearchRequest,
    mysql_pool = Depends(get_mysql),
    mongo_client = Depends(get_mongodb)
):
    """Search available carpool trips"""
    try:
        carpool_repo = CarPoolRepository(mysql_pool, mongo_client)
        results = await carpool_repo.search_trips(search_params)
        
        return BaseResponse(
            message=f"Found {len(results)} trips",
            data={"trips": [trip.dict() for trip in results]}
        )
    
    except Exception as e:
        logger.error(f"Trip search error: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.post("/book", response_model=BaseResponse)
async def book_trip(
    booking_data: CarPoolBookingRequest,
    current_user: Dict = Depends(get_current_user),
    mysql_pool = Depends(get_mysql),
    mongo_client = Depends(get_mongodb)
):
    """Book carpool trip"""
    try:
        carpool_repo = CarPoolRepository(mysql_pool, mongo_client)
        booking_id = await carpool_repo.book_trip(current_user['user_id'], booking_data)
        
        logger.info(f"Carpool booking created: {booking_id}")
        return BaseResponse(
            message="Booking created successfully",
            data={"booking_id": booking_id}
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Carpool booking error: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
