# Bike Ride Sharing Service API for TravelAllinOne (Inspired by <PERSON><PERSON>)

from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
import sys
import os
import asyncio

# Add shared modules to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'shared'))

from models import (
    BaseResponse, Location, BookingStatus
)
from utils import (
    get_current_user, get_mysql, get_mongodb, get_redis,
    setup_logging, PricingUtils, LocationUtils, generate_booking_reference
)

# Initialize FastAPI app
app = FastAPI(
    title="TravelAllinOne Bike Service",
    description="Bike Ride Sharing Service inspired by Rapido",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Setup logging
logger = setup_logging("bike-service")

# Request/Response Models
class BikeRideRequest(BaseModel):
    pickup_location: Location
    drop_location: Location
    ride_type: str = Field(default="regular", regex="^(regular|premium|delivery)$")
    scheduled_time: Optional[datetime] = None  # For scheduled rides
    passenger_phone: str = Field(..., regex=r"^\+91[6-9]\d{9}$")
    special_instructions: Optional[str] = Field(None, max_length=200)

class BikeRideEstimate(BaseModel):
    distance_km: float
    estimated_duration_minutes: int
    estimated_fare: float
    surge_multiplier: float = 1.0
    available_drivers: int

class DriverLocation(BaseModel):
    driver_id: str
    current_location: Location
    is_available: bool
    rating: Optional[float] = None
    vehicle_number: str
    estimated_arrival_minutes: int

class BikeRideBooking(BaseModel):
    id: str
    booking_reference: str
    rider_id: str
    driver_id: Optional[str] = None
    pickup_location: Location
    drop_location: Location
    ride_type: str
    status: str
    estimated_fare: float
    actual_fare: Optional[float] = None
    distance_km: Optional[float] = None
    duration_minutes: Optional[int] = None
    scheduled_time: Optional[datetime] = None
    pickup_time: Optional[datetime] = None
    drop_time: Optional[datetime] = None
    driver_details: Optional[Dict[str, Any]] = None
    created_at: datetime

class DriverProfile(BaseModel):
    user_id: str
    license_number: str = Field(..., min_length=10, max_length=20)
    license_expiry: datetime
    vehicle_registration: str = Field(..., regex=r"^[A-Z]{2}[0-9]{2}[A-Z]{2}[0-9]{4}$")
    vehicle_model: str
    vehicle_color: str
    driving_experience_years: int = Field(..., ge=0, le=50)
    documents: List[str] = []  # Document URLs
    verification_status: str = Field(default="pending", regex="^(pending|verified|rejected)$")
    is_available: bool = True
    current_location: Optional[Location] = None
    rating: Optional[float] = None
    total_rides: int = 0

class RideStatusUpdate(BaseModel):
    booking_id: str
    status: str = Field(..., regex="^(accepted|arrived|started|completed|cancelled)$")
    current_location: Optional[Location] = None
    otp: Optional[str] = None

# Database operations
class BikeRepository:
    def __init__(self, mysql_pool, mongo_client, redis_client):
        self.mysql_pool = mysql_pool
        self.mongo_db = mongo_client.travelallinone
        self.redis = redis_client
    
    async def get_ride_estimate(self, ride_request: BikeRideRequest) -> BikeRideEstimate:
        """Get ride estimate"""
        # Calculate distance
        distance = LocationUtils.calculate_distance(
            ride_request.pickup_location.latitude,
            ride_request.pickup_location.longitude,
            ride_request.drop_location.latitude,
            ride_request.drop_location.longitude
        )
        
        # Calculate estimated duration
        duration = LocationUtils.estimate_travel_time(distance, "bike")
        
        # Calculate fare
        base_fare = PricingUtils.calculate_bike_fare(distance)
        
        # Apply surge pricing if needed
        surge_multiplier = await self._get_surge_multiplier(
            ride_request.pickup_location.city
        )
        estimated_fare = base_fare * surge_multiplier
        
        # Count available drivers nearby
        available_drivers = await self._count_nearby_drivers(
            ride_request.pickup_location.latitude,
            ride_request.pickup_location.longitude
        )
        
        return BikeRideEstimate(
            distance_km=distance,
            estimated_duration_minutes=duration,
            estimated_fare=estimated_fare,
            surge_multiplier=surge_multiplier,
            available_drivers=available_drivers
        )
    
    async def find_nearby_drivers(self, location: Location, radius_km: float = 5) -> List[DriverLocation]:
        """Find nearby available drivers"""
        async with self.mysql_pool.acquire() as conn:
            async with conn.cursor() as cursor:
                # Get drivers within radius using Haversine formula
                query = """
                SELECT dp.user_id, dp.vehicle_registration, dp.vehicle_model, 
                       dp.vehicle_color, dp.rating, dp.current_lat, dp.current_lng,
                       u.first_name, u.last_name
                FROM driver_profiles dp
                JOIN users u ON dp.user_id = u.id
                WHERE dp.verification_status = 'verified'
                  AND dp.is_available = 1
                  AND dp.current_lat IS NOT NULL
                  AND dp.current_lng IS NOT NULL
                  AND (
                    6371 * acos(
                      cos(radians(%s)) * cos(radians(dp.current_lat)) *
                      cos(radians(dp.current_lng) - radians(%s)) +
                      sin(radians(%s)) * sin(radians(dp.current_lat))
                    )
                  ) <= %s
                ORDER BY (
                  6371 * acos(
                    cos(radians(%s)) * cos(radians(dp.current_lat)) *
                    cos(radians(dp.current_lng) - radians(%s)) +
                    sin(radians(%s)) * sin(radians(dp.current_lat))
                  )
                )
                LIMIT 10
                """
                
                lat, lng = location.latitude, location.longitude
                await cursor.execute(query, (lat, lng, lat, radius_km, lat, lng, lat))
                rows = await cursor.fetchall()
                
                drivers = []
                for row in rows:
                    driver_lat, driver_lng = float(row[5]), float(row[6])
                    distance = LocationUtils.calculate_distance(lat, lng, driver_lat, driver_lng)
                    eta = int((distance / 25) * 60)  # Assuming 25 km/h average speed
                    
                    driver = DriverLocation(
                        driver_id=str(row[0]),
                        current_location=Location(
                            latitude=driver_lat,
                            longitude=driver_lng,
                            address="",
                            city=location.city,
                            state="",
                            pincode=""
                        ),
                        is_available=True,
                        rating=float(row[4]) if row[4] else None,
                        vehicle_number=row[1],
                        estimated_arrival_minutes=eta
                    )
                    drivers.append(driver)
                
                return drivers
    
    async def create_booking(self, user_id: str, ride_request: BikeRideRequest) -> str:
        """Create bike ride booking"""
        async with self.mysql_pool.acquire() as conn:
            async with conn.cursor() as cursor:
                # Get ride estimate
                estimate = await self.get_ride_estimate(ride_request)
                
                # Generate OTP for ride verification
                import random
                otp = f"{random.randint(1000, 9999)}"
                
                # Create booking
                booking_ref = generate_booking_reference()
                query = """
                INSERT INTO bike_bookings (user_id, booking_reference, pickup_lat, pickup_lng,
                                         drop_lat, drop_lng, pickup_address, drop_address,
                                         ride_type, estimated_fare, estimated_distance_km,
                                         estimated_duration_minutes, status, passenger_phone,
                                         special_instructions, otp, scheduled_time, created_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                
                await cursor.execute(query, (
                    user_id, booking_ref,
                    ride_request.pickup_location.latitude,
                    ride_request.pickup_location.longitude,
                    ride_request.drop_location.latitude,
                    ride_request.drop_location.longitude,
                    ride_request.pickup_location.address,
                    ride_request.drop_location.address,
                    ride_request.ride_type,
                    estimate.estimated_fare,
                    estimate.distance_km,
                    estimate.estimated_duration_minutes,
                    BookingStatus.PENDING.value,
                    ride_request.passenger_phone,
                    ride_request.special_instructions,
                    otp,
                    ride_request.scheduled_time,
                    datetime.utcnow()
                ))
                
                booking_id = cursor.lastrowid
                await conn.commit()
                
                # Start driver matching process
                asyncio.create_task(self._match_driver(str(booking_id), ride_request))
                
                return str(booking_id)
    
    async def _match_driver(self, booking_id: str, ride_request: BikeRideRequest):
        """Background task to match driver"""
        try:
            # Find nearby drivers
            drivers = await self.find_nearby_drivers(ride_request.pickup_location)
            
            if not drivers:
                # No drivers available, update booking status
                await self._update_booking_status(booking_id, "no_drivers_available")
                return
            
            # Send ride request to drivers (in order of proximity)
            for driver in drivers[:3]:  # Send to top 3 closest drivers
                await self._send_ride_request_to_driver(booking_id, driver.driver_id)
                
                # Wait for 30 seconds for driver response
                await asyncio.sleep(30)
                
                # Check if booking was accepted
                booking_status = await self._get_booking_status(booking_id)
                if booking_status == "accepted":
                    break
            
        except Exception as e:
            logger.error(f"Driver matching error: {str(e)}")
    
    async def _get_surge_multiplier(self, city: str) -> float:
        """Get surge pricing multiplier"""
        # Simple surge logic - can be enhanced with real-time demand data
        surge_key = f"surge:{city}"
        surge = await self.redis.get(surge_key)
        return float(surge) if surge else 1.0
    
    async def _count_nearby_drivers(self, lat: float, lng: float) -> int:
        """Count available drivers nearby"""
        drivers = await self.find_nearby_drivers(
            Location(latitude=lat, longitude=lng, address="", city="", state="", pincode="")
        )
        return len(drivers)
    
    async def _update_booking_status(self, booking_id: str, status: str):
        """Update booking status"""
        async with self.mysql_pool.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "UPDATE bike_bookings SET status = %s WHERE id = %s",
                    (status, booking_id)
                )
                await conn.commit()
    
    async def _get_booking_status(self, booking_id: str) -> str:
        """Get current booking status"""
        async with self.mysql_pool.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "SELECT status FROM bike_bookings WHERE id = %s",
                    (booking_id,)
                )
                result = await cursor.fetchone()
                return result[0] if result else "unknown"
    
    async def _send_ride_request_to_driver(self, booking_id: str, driver_id: str):
        """Send ride request notification to driver"""
        # This would integrate with push notification service
        notification_data = {
            "type": "ride_request",
            "booking_id": booking_id,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # Store in Redis for real-time notifications
        await self.redis.lpush(f"driver_notifications:{driver_id}", str(notification_data))
        await self.redis.expire(f"driver_notifications:{driver_id}", 300)  # 5 minutes

# API Endpoints
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "bike-service", "timestamp": datetime.utcnow()}

@app.post("/estimate", response_model=BaseResponse)
async def get_ride_estimate(
    ride_request: BikeRideRequest,
    mysql_pool = Depends(get_mysql),
    mongo_client = Depends(get_mongodb),
    redis_client = Depends(get_redis)
):
    """Get ride fare estimate"""
    try:
        bike_repo = BikeRepository(mysql_pool, mongo_client, redis_client)
        estimate = await bike_repo.get_ride_estimate(ride_request)
        
        return BaseResponse(
            message="Ride estimate calculated",
            data={"estimate": estimate.dict()}
        )
    
    except Exception as e:
        logger.error(f"Ride estimate error: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.get("/nearby-drivers", response_model=BaseResponse)
async def get_nearby_drivers(
    lat: float = Field(..., ge=-90, le=90),
    lng: float = Field(..., ge=-180, le=180),
    radius: float = Field(5.0, ge=1, le=20),
    mysql_pool = Depends(get_mysql),
    mongo_client = Depends(get_mongodb),
    redis_client = Depends(get_redis)
):
    """Get nearby available drivers"""
    try:
        location = Location(latitude=lat, longitude=lng, address="", city="", state="", pincode="")
        bike_repo = BikeRepository(mysql_pool, mongo_client, redis_client)
        drivers = await bike_repo.find_nearby_drivers(location, radius)
        
        return BaseResponse(
            message=f"Found {len(drivers)} nearby drivers",
            data={"drivers": [driver.dict() for driver in drivers]}
        )
    
    except Exception as e:
        logger.error(f"Nearby drivers error: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.post("/book", response_model=BaseResponse)
async def book_ride(
    ride_request: BikeRideRequest,
    background_tasks: BackgroundTasks,
    current_user: Dict = Depends(get_current_user),
    mysql_pool = Depends(get_mysql),
    mongo_client = Depends(get_mongodb),
    redis_client = Depends(get_redis)
):
    """Book a bike ride"""
    try:
        bike_repo = BikeRepository(mysql_pool, mongo_client, redis_client)
        booking_id = await bike_repo.create_booking(current_user['user_id'], ride_request)
        
        logger.info(f"Bike ride booking created: {booking_id}")
        return BaseResponse(
            message="Ride booked successfully. Finding nearby drivers...",
            data={"booking_id": booking_id}
        )
    
    except Exception as e:
        logger.error(f"Bike booking error: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
