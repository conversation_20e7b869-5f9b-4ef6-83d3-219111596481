{"extends": "@tsconfig/react-native/tsconfig.json", "compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": "./src", "paths": {"@/*": ["*"], "@/components/*": ["components/*"], "@/screens/*": ["screens/*"], "@/navigation/*": ["navigation/*"], "@/services/*": ["services/*"], "@/utils/*": ["utils/*"], "@/types/*": ["types/*"], "@/hooks/*": ["hooks/*"], "@/store/*": ["store/*"], "@/assets/*": ["../assets/*"]}}, "include": ["src/**/*", "index.js", "App.tsx", "metro.config.js", "babel.config.js"], "exclude": ["node_modules", "android", "ios", "**/*.test.ts", "**/*.test.tsx"]}