# Vtravelallinone Mobile CI/CD Pipeline
# Automated build, test, and deployment for Android and iOS

name: Mobile CI/CD

on:
  push:
    branches: [main, develop]
    paths:
      - 'mobile/**'
      - '.github/workflows/mobile-ci-cd.yml'
  pull_request:
    branches: [main]
    paths:
      - 'mobile/**'
  workflow_dispatch:
    inputs:
      platform:
        description: 'Platform to build'
        required: true
        default: 'both'
        type: choice
        options:
        - android
        - ios
        - both
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
        - staging
        - production

env:
  NODE_VERSION: '18'
  JAVA_VERSION: '11'
  RUBY_VERSION: '3.0'

jobs:
  # Code Quality and Security
  code-quality:
    name: Code Quality & Security
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./mobile
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: mobile/package-lock.json

    - name: Install dependencies
      run: npm ci

    - name: Run ESLint
      run: npm run lint

    - name: Run TypeScript check
      run: npm run type-check

    - name: Run tests
      run: npm test -- --coverage --watchAll=false

    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./mobile/coverage/lcov.info
        flags: mobile

    - name: Run security audit
      run: npm audit --audit-level=moderate

  # Android Build and Test
  android-build:
    name: Android Build
    runs-on: ubuntu-latest
    needs: code-quality
    if: github.event.inputs.platform == 'android' || github.event.inputs.platform == 'both' || github.event.inputs.platform == null
    defaults:
      run:
        working-directory: ./mobile
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: mobile/package-lock.json

    - name: Setup Java
      uses: actions/setup-java@v3
      with:
        distribution: 'temurin'
        java-version: ${{ env.JAVA_VERSION }}

    - name: Setup Android SDK
      uses: android-actions/setup-android@v2

    - name: Install dependencies
      run: npm ci

    - name: Cache Gradle dependencies
      uses: actions/cache@v3
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-

    - name: Make gradlew executable
      run: chmod +x android/gradlew

    - name: Build Android Debug APK
      run: |
        cd android
        ./gradlew assembleDebug

    - name: Build Android Release APK
      if: github.ref == 'refs/heads/main'
      run: |
        cd android
        ./gradlew assembleRelease
      env:
        MYAPP_UPLOAD_STORE_FILE: ${{ secrets.ANDROID_KEYSTORE_FILE }}
        MYAPP_UPLOAD_KEY_ALIAS: ${{ secrets.ANDROID_KEY_ALIAS }}
        MYAPP_UPLOAD_STORE_PASSWORD: ${{ secrets.ANDROID_KEYSTORE_PASSWORD }}
        MYAPP_UPLOAD_KEY_PASSWORD: ${{ secrets.ANDROID_KEY_PASSWORD }}

    - name: Upload Android APK
      uses: actions/upload-artifact@v3
      with:
        name: android-apk
        path: mobile/android/app/build/outputs/apk/
        retention-days: 30

    - name: Run Android tests
      run: |
        cd android
        ./gradlew test

  # iOS Build and Test
  ios-build:
    name: iOS Build
    runs-on: macos-latest
    needs: code-quality
    if: github.event.inputs.platform == 'ios' || github.event.inputs.platform == 'both' || github.event.inputs.platform == null
    defaults:
      run:
        working-directory: ./mobile
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: mobile/package-lock.json

    - name: Setup Ruby
      uses: ruby/setup-ruby@v1
      with:
        ruby-version: ${{ env.RUBY_VERSION }}
        bundler-cache: true

    - name: Install dependencies
      run: npm ci

    - name: Cache CocoaPods
      uses: actions/cache@v3
      with:
        path: mobile/ios/Pods
        key: ${{ runner.os }}-pods-${{ hashFiles('**/Podfile.lock') }}
        restore-keys: |
          ${{ runner.os }}-pods-

    - name: Install CocoaPods
      run: |
        cd ios
        pod install

    - name: Build iOS Debug
      run: |
        cd ios
        xcodebuild -workspace VtravelallinoneApp.xcworkspace \
          -scheme VtravelallinoneApp \
          -configuration Debug \
          -destination 'platform=iOS Simulator,name=iPhone 14' \
          build

    - name: Build iOS Release
      if: github.ref == 'refs/heads/main'
      run: |
        cd ios
        xcodebuild -workspace VtravelallinoneApp.xcworkspace \
          -scheme VtravelallinoneApp \
          -configuration Release \
          -destination generic/platform=iOS \
          -archivePath VtravelallinoneApp.xcarchive \
          archive
      env:
        CODE_SIGN_IDENTITY: ${{ secrets.IOS_CODE_SIGN_IDENTITY }}
        PROVISIONING_PROFILE: ${{ secrets.IOS_PROVISIONING_PROFILE }}

    - name: Export iOS IPA
      if: github.ref == 'refs/heads/main'
      run: |
        cd ios
        xcodebuild -exportArchive \
          -archivePath VtravelallinoneApp.xcarchive \
          -exportPath ./build \
          -exportOptionsPlist ExportOptions.plist

    - name: Upload iOS IPA
      if: github.ref == 'refs/heads/main'
      uses: actions/upload-artifact@v3
      with:
        name: ios-ipa
        path: mobile/ios/build/
        retention-days: 30

    - name: Run iOS tests
      run: |
        cd ios
        xcodebuild test \
          -workspace VtravelallinoneApp.xcworkspace \
          -scheme VtravelallinoneApp \
          -destination 'platform=iOS Simulator,name=iPhone 14'

  # Deploy to App Stores
  deploy-android:
    name: Deploy Android to Play Store
    runs-on: ubuntu-latest
    needs: android-build
    if: github.ref == 'refs/heads/main' && (github.event.inputs.platform == 'android' || github.event.inputs.platform == 'both' || github.event.inputs.platform == null)
    environment: production
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Download Android APK
      uses: actions/download-artifact@v3
      with:
        name: android-apk
        path: ./apk

    - name: Deploy to Google Play Store
      uses: r0adkll/upload-google-play@v1
      with:
        serviceAccountJsonPlainText: ${{ secrets.GOOGLE_PLAY_SERVICE_ACCOUNT_JSON }}
        packageName: com.vtravelallinone.mobile
        releaseFiles: ./apk/release/app-release.apk
        track: internal
        status: completed

  deploy-ios:
    name: Deploy iOS to App Store
    runs-on: macos-latest
    needs: ios-build
    if: github.ref == 'refs/heads/main' && (github.event.inputs.platform == 'ios' || github.event.inputs.platform == 'both' || github.event.inputs.platform == null)
    environment: production
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Download iOS IPA
      uses: actions/download-artifact@v3
      with:
        name: ios-ipa
        path: ./ipa

    - name: Deploy to App Store Connect
      uses: apple-actions/upload-testflight-build@v1
      with:
        app-path: ./ipa/VtravelallinoneApp.ipa
        issuer-id: ${{ secrets.APPSTORE_ISSUER_ID }}
        api-key-id: ${{ secrets.APPSTORE_API_KEY_ID }}
        api-private-key: ${{ secrets.APPSTORE_API_PRIVATE_KEY }}

  # Deploy to Firebase App Distribution (Beta Testing)
  deploy-beta:
    name: Deploy Beta Builds
    runs-on: ubuntu-latest
    needs: [android-build, ios-build]
    if: github.ref == 'refs/heads/develop'
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Download Android APK
      uses: actions/download-artifact@v3
      with:
        name: android-apk
        path: ./apk

    - name: Download iOS IPA
      if: runner.os == 'macOS'
      uses: actions/download-artifact@v3
      with:
        name: ios-ipa
        path: ./ipa

    - name: Deploy Android to Firebase App Distribution
      uses: wzieba/Firebase-Distribution-Github-Action@v1
      with:
        appId: ${{ secrets.FIREBASE_ANDROID_APP_ID }}
        serviceCredentialsFileContent: ${{ secrets.FIREBASE_SERVICE_ACCOUNT_JSON }}
        groups: beta-testers
        file: ./apk/debug/app-debug.apk
        releaseNotes: |
          🚀 New beta build available!
          
          Changes in this build:
          ${{ github.event.head_commit.message }}

    - name: Deploy iOS to Firebase App Distribution
      if: runner.os == 'macOS'
      uses: wzieba/Firebase-Distribution-Github-Action@v1
      with:
        appId: ${{ secrets.FIREBASE_IOS_APP_ID }}
        serviceCredentialsFileContent: ${{ secrets.FIREBASE_SERVICE_ACCOUNT_JSON }}
        groups: beta-testers
        file: ./ipa/VtravelallinoneApp.ipa

  # Notify deployment status
  notify:
    name: Notify Deployment Status
    runs-on: ubuntu-latest
    needs: [deploy-android, deploy-ios, deploy-beta]
    if: always()
    steps:
    - name: Notify success
      if: needs.deploy-android.result == 'success' || needs.deploy-ios.result == 'success' || needs.deploy-beta.result == 'success'
      uses: 8398a7/action-slack@v3
      with:
        status: success
        text: '🚀 Vtravelallinone mobile app deployed successfully!'
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

    - name: Notify failure
      if: needs.deploy-android.result == 'failure' || needs.deploy-ios.result == 'failure' || needs.deploy-beta.result == 'failure'
      uses: 8398a7/action-slack@v3
      with:
        status: failure
        text: '❌ Vtravelallinone mobile app deployment failed!'
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
