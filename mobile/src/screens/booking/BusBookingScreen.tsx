/**
 * Bus Booking Screen
 * Search and book bus tickets with beautiful UI
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';
import Animated, { FadeInDown, FadeInUp } from 'react-native-reanimated';
import DatePicker from 'react-native-date-picker';

// Components
import { CustomTextInput } from '@/components/CustomTextInput';
import { CustomButton } from '@/components/CustomButton';
import { BusCard } from '@/components/BusCard';
import { LocationPicker } from '@/components/LocationPicker';
import { FilterModal } from '@/components/FilterModal';
import { LoadingScreen } from '@/components/LoadingScreen';

// Hooks
import { useAppDispatch, useAppSelector } from '@/store';
import { searchBuses, selectBusLoading, selectBusResults } from '@/store/slices/busSlice';

// Utils
import { colors, spacing, typography, shadows } from '@/utils/theme';
import { formatDate, formatTime } from '@/utils/dateUtils';
import { showToast } from '@/utils/toast';

// Types
import { NavigationProp } from '@react-navigation/native';

const { width } = Dimensions.get('window');

interface BusBookingScreenProps {
  navigation: NavigationProp<any>;
}

interface SearchParams {
  from: string;
  to: string;
  date: Date;
  passengers: number;
}

export const BusBookingScreen: React.FC<BusBookingScreenProps> = ({ navigation }) => {
  const dispatch = useAppDispatch();
  const loading = useAppSelector(selectBusLoading);
  const busResults = useAppSelector(selectBusResults);

  const [searchParams, setSearchParams] = useState<SearchParams>({
    from: '',
    to: '',
    date: new Date(),
    passengers: 1,
  });

  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showLocationPicker, setShowLocationPicker] = useState<'from' | 'to' | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);

  const handleSearch = async () => {
    if (!searchParams.from || !searchParams.to) {
      showToast('error', 'Missing Information', 'Please select both departure and destination cities');
      return;
    }

    if (searchParams.from === searchParams.to) {
      showToast('error', 'Invalid Route', 'Departure and destination cannot be the same');
      return;
    }

    try {
      await dispatch(searchBuses({
        from: searchParams.from,
        to: searchParams.to,
        date: searchParams.date.toISOString().split('T')[0],
        passengers: searchParams.passengers,
      })).unwrap();
      
      setHasSearched(true);
    } catch (error: any) {
      showToast('error', 'Search Failed', error.message || 'Please try again');
    }
  };

  const handleSwapLocations = () => {
    setSearchParams(prev => ({
      ...prev,
      from: prev.to,
      to: prev.from,
    }));
  };

  const handleLocationSelect = (location: string) => {
    if (showLocationPicker === 'from') {
      setSearchParams(prev => ({ ...prev, from: location }));
    } else if (showLocationPicker === 'to') {
      setSearchParams(prev => ({ ...prev, to: location }));
    }
    setShowLocationPicker(null);
  };

  const handleBusSelect = (busId: string) => {
    navigation.navigate('BusDetails', { busId });
  };

  if (loading && !hasSearched) {
    return <LoadingScreen />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={colors.busColor} />
      
      {/* Header */}
      <LinearGradient
        colors={['#4285F4', '#1976D2']}
        style={styles.header}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <Animated.View 
          entering={FadeInUp.delay(200)}
          style={styles.headerContent}
        >
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Icon name="arrow-back" size={24} color={colors.white} />
          </TouchableOpacity>
          
          <Text style={styles.headerTitle}>Bus Booking</Text>
          
          <TouchableOpacity
            style={styles.filterButton}
            onPress={() => setShowFilters(true)}
          >
            <Icon name="tune" size={24} color={colors.white} />
          </TouchableOpacity>
        </Animated.View>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Search Form */}
        <Animated.View 
          entering={FadeInDown.delay(300)}
          style={styles.searchCard}
        >
          {/* Location Inputs */}
          <View style={styles.locationContainer}>
            <View style={styles.locationInputs}>
              <TouchableOpacity
                style={styles.locationInput}
                onPress={() => setShowLocationPicker('from')}
              >
                <Icon name="my-location" size={20} color={colors.busColor} />
                <Text style={[styles.locationText, !searchParams.from && styles.placeholderText]}>
                  {searchParams.from || 'From'}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.swapButton}
                onPress={handleSwapLocations}
              >
                <Icon name="swap-vert" size={24} color={colors.busColor} />
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.locationInput}
                onPress={() => setShowLocationPicker('to')}
              >
                <Icon name="location-on" size={20} color={colors.error} />
                <Text style={[styles.locationText, !searchParams.to && styles.placeholderText]}>
                  {searchParams.to || 'To'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Date and Passengers */}
          <View style={styles.detailsRow}>
            <TouchableOpacity
              style={styles.detailInput}
              onPress={() => setShowDatePicker(true)}
            >
              <Icon name="event" size={20} color={colors.busColor} />
              <Text style={styles.detailText}>
                {formatDate(searchParams.date)}
              </Text>
            </TouchableOpacity>

            <View style={styles.passengerContainer}>
              <Icon name="person" size={20} color={colors.busColor} />
              <Text style={styles.detailText}>
                {searchParams.passengers} Passenger{searchParams.passengers > 1 ? 's' : ''}
              </Text>
              <View style={styles.passengerControls}>
                <TouchableOpacity
                  style={styles.passengerButton}
                  onPress={() => setSearchParams(prev => ({ 
                    ...prev, 
                    passengers: Math.max(1, prev.passengers - 1) 
                  }))}
                >
                  <Icon name="remove" size={16} color={colors.busColor} />
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.passengerButton}
                  onPress={() => setSearchParams(prev => ({ 
                    ...prev, 
                    passengers: Math.min(6, prev.passengers + 1) 
                  }))}
                >
                  <Icon name="add" size={16} color={colors.busColor} />
                </TouchableOpacity>
              </View>
            </View>
          </View>

          {/* Search Button */}
          <CustomButton
            title="Search Buses"
            onPress={handleSearch}
            loading={loading}
            style={styles.searchButton}
            variant="primary"
            leftIcon="search"
          />
        </Animated.View>

        {/* Search Results */}
        {hasSearched && (
          <Animated.View 
            entering={FadeInDown.delay(600)}
            style={styles.resultsContainer}
          >
            <View style={styles.resultsHeader}>
              <Text style={styles.resultsTitle}>
                {busResults.length} buses found
              </Text>
              <Text style={styles.resultsSubtitle}>
                {searchParams.from} → {searchParams.to}
              </Text>
            </View>

            {busResults.length > 0 ? (
              busResults.map((bus, index) => (
                <Animated.View
                  key={bus.id}
                  entering={FadeInDown.delay(700 + index * 100)}
                >
                  <BusCard
                    bus={bus}
                    onPress={() => handleBusSelect(bus.id)}
                    style={styles.busCard}
                  />
                </Animated.View>
              ))
            ) : (
              <View style={styles.noBusesContainer}>
                <Icon name="directions-bus" size={64} color={colors.gray400} />
                <Text style={styles.noBusesTitle}>No buses found</Text>
                <Text style={styles.noBusesSubtitle}>
                  Try changing your search criteria
                </Text>
              </View>
            )}
          </Animated.View>
        )}
      </ScrollView>

      {/* Date Picker Modal */}
      <DatePicker
        modal
        open={showDatePicker}
        date={searchParams.date}
        mode="date"
        minimumDate={new Date()}
        onConfirm={(date) => {
          setShowDatePicker(false);
          setSearchParams(prev => ({ ...prev, date }));
        }}
        onCancel={() => setShowDatePicker(false)}
      />

      {/* Location Picker Modal */}
      {showLocationPicker && (
        <LocationPicker
          visible={true}
          onSelect={handleLocationSelect}
          onClose={() => setShowLocationPicker(null)}
          title={`Select ${showLocationPicker === 'from' ? 'Departure' : 'Destination'} City`}
        />
      )}

      {/* Filter Modal */}
      <FilterModal
        visible={showFilters}
        onClose={() => setShowFilters(false)}
        onApply={(filters) => {
          // Apply filters logic
          setShowFilters(false);
        }}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    padding: spacing.sm,
  },
  headerTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    color: colors.white,
  },
  filterButton: {
    padding: spacing.sm,
  },
  content: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  searchCard: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: spacing.lg,
    marginTop: -spacing.lg,
    marginBottom: spacing.lg,
    ...shadows.lg,
  },
  locationContainer: {
    marginBottom: spacing.lg,
  },
  locationInputs: {
    position: 'relative',
  },
  locationInput: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.md,
    backgroundColor: colors.gray100,
    borderRadius: 12,
    marginBottom: spacing.md,
  },
  locationText: {
    fontSize: typography.fontSize.base,
    color: colors.text,
    marginLeft: spacing.sm,
    flex: 1,
  },
  placeholderText: {
    color: colors.gray500,
  },
  swapButton: {
    position: 'absolute',
    right: spacing.md,
    top: '50%',
    transform: [{ translateY: -12 }],
    backgroundColor: colors.white,
    borderRadius: 20,
    padding: spacing.sm,
    ...shadows.sm,
    zIndex: 1,
  },
  detailsRow: {
    flexDirection: 'row',
    gap: spacing.md,
    marginBottom: spacing.lg,
  },
  detailInput: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.md,
    backgroundColor: colors.gray100,
    borderRadius: 12,
  },
  detailText: {
    fontSize: typography.fontSize.sm,
    color: colors.text,
    marginLeft: spacing.sm,
  },
  passengerContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.md,
    backgroundColor: colors.gray100,
    borderRadius: 12,
  },
  passengerControls: {
    flexDirection: 'row',
    marginLeft: 'auto',
    gap: spacing.sm,
  },
  passengerButton: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: colors.white,
    alignItems: 'center',
    justifyContent: 'center',
    ...shadows.sm,
  },
  searchButton: {
    marginTop: spacing.md,
  },
  resultsContainer: {
    marginBottom: spacing.xl,
  },
  resultsHeader: {
    marginBottom: spacing.lg,
  },
  resultsTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text,
  },
  resultsSubtitle: {
    fontSize: typography.fontSize.sm,
    color: colors.gray600,
    marginTop: spacing.xs,
  },
  busCard: {
    marginBottom: spacing.md,
  },
  noBusesContainer: {
    alignItems: 'center',
    paddingVertical: spacing.xl * 2,
  },
  noBusesTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.gray600,
    marginTop: spacing.md,
  },
  noBusesSubtitle: {
    fontSize: typography.fontSize.sm,
    color: colors.gray500,
    marginTop: spacing.xs,
    textAlign: 'center',
  },
});
