/**
 * Home Screen
 * Main dashboard with travel options and quick actions
 */

import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  StatusBar,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';
import Animated, { FadeInDown, FadeInUp } from 'react-native-reanimated';

// Components
import { VLogo } from '@/components/VLogo';
import { ServiceCard } from '@/components/ServiceCard';
import { QuickActionCard } from '@/components/QuickActionCard';
import { RecentBookingCard } from '@/components/RecentBookingCard';

// Hooks
import { useAppSelector, useAppDispatch } from '@/store';
import { selectUser } from '@/store/slices/authSlice';

// Utils
import { colors, spacing, typography, shadows } from '@/utils/theme';

// Types
import { NavigationProp } from '@react-navigation/native';

const { width } = Dimensions.get('window');

interface HomeScreenProps {
  navigation: NavigationProp<any>;
}

export const HomeScreen: React.FC<HomeScreenProps> = ({ navigation }) => {
  const dispatch = useAppDispatch();
  const user = useAppSelector(selectUser);
  const [refreshing, setRefreshing] = useState(false);

  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    // Refresh data here
    setTimeout(() => {
      setRefreshing(false);
    }, 2000);
  }, []);

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good Morning';
    if (hour < 17) return 'Good Afternoon';
    return 'Good Evening';
  };

  const travelServices = [
    {
      id: 'bus',
      title: 'Bus Booking',
      subtitle: 'Comfortable intercity travel',
      icon: 'directions-bus',
      color: colors.busColor,
      gradient: [colors.deepOcean, colors.oceanTeal],
      onPress: () => navigation.navigate('BusBooking'),
    },
    {
      id: 'carpool',
      title: 'Car Pool',
      subtitle: 'Share rides, split costs',
      icon: 'directions-car',
      color: colors.carpoolColor,
      gradient: [colors.freshMint, colors.clearSky],
      onPress: () => navigation.navigate('CarpoolBooking'),
    },
    {
      id: 'bike',
      title: 'Bike Rides',
      subtitle: 'Quick city commutes',
      icon: 'two-wheeler',
      color: colors.bikeColor,
      gradient: [colors.coralSunset, colors.goldenHour],
      onPress: () => navigation.navigate('BikeBooking'),
    },
  ];

  const quickActions = [
    {
      id: 'track',
      title: 'Track Ride',
      icon: 'my-location',
      onPress: () => navigation.navigate('TrackRide'),
    },
    {
      id: 'support',
      title: 'Support',
      icon: 'support-agent',
      onPress: () => navigation.navigate('Support'),
    },
    {
      id: 'offers',
      title: 'Offers',
      icon: 'local-offer',
      onPress: () => navigation.navigate('Offers'),
    },
    {
      id: 'wallet',
      title: 'Wallet',
      icon: 'account-balance-wallet',
      onPress: () => navigation.navigate('Wallet'),
    },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={colors.deepOcean} />
      
      {/* Header */}
      <LinearGradient
        colors={colors.heroGradient}
        style={styles.header}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <Animated.View 
          entering={FadeInUp.delay(200)}
          style={styles.headerContent}
        >
          <View style={styles.headerTop}>
            <View style={styles.logoContainer}>
              <VLogo size="large" animated />
              <Text style={styles.brandName}>Vtravelallinone</Text>
            </View>
            
            <TouchableOpacity
              style={styles.notificationButton}
              onPress={() => navigation.navigate('Notifications')}
            >
              <Icon name="notifications" size={24} color={colors.white} />
              <View style={styles.notificationBadge}>
                <Text style={styles.badgeText}>3</Text>
              </View>
            </TouchableOpacity>
          </View>
          
          <View style={styles.greetingContainer}>
            <Text style={styles.greeting}>
              {getGreeting()}, {user?.firstName || 'Traveler'}!
            </Text>
            <Text style={styles.subtitle}>
              Where would you like to go today?
            </Text>
          </View>
        </Animated.View>
      </LinearGradient>

      {/* Content */}
      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Travel Services */}
        <Animated.View 
          entering={FadeInDown.delay(400)}
          style={styles.section}
        >
          <Text style={styles.sectionTitle}>Choose Your Travel Mode</Text>
          <View style={styles.servicesContainer}>
            {travelServices.map((service, index) => (
              <Animated.View
                key={service.id}
                entering={FadeInDown.delay(500 + index * 100)}
              >
                <ServiceCard
                  title={service.title}
                  subtitle={service.subtitle}
                  icon={service.icon}
                  gradient={service.gradient}
                  onPress={service.onPress}
                  style={styles.serviceCard}
                />
              </Animated.View>
            ))}
          </View>
        </Animated.View>

        {/* Quick Actions */}
        <Animated.View 
          entering={FadeInDown.delay(800)}
          style={styles.section}
        >
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.quickActionsContainer}>
            {quickActions.map((action, index) => (
              <Animated.View
                key={action.id}
                entering={FadeInDown.delay(900 + index * 50)}
                style={styles.quickActionWrapper}
              >
                <QuickActionCard
                  title={action.title}
                  icon={action.icon}
                  onPress={action.onPress}
                />
              </Animated.View>
            ))}
          </View>
        </Animated.View>

        {/* Recent Bookings */}
        <Animated.View 
          entering={FadeInDown.delay(1200)}
          style={styles.section}
        >
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Recent Bookings</Text>
            <TouchableOpacity onPress={() => navigation.navigate('BookingsTab')}>
              <Text style={styles.seeAllText}>See All</Text>
            </TouchableOpacity>
          </View>
          
          <RecentBookingCard
            type="bus"
            from="Mumbai"
            to="Pune"
            date="Today, 2:30 PM"
            status="confirmed"
            onPress={() => navigation.navigate('BookingDetails')}
          />
        </Animated.View>

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.xl,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
  },
  headerContent: {
    paddingTop: spacing.md,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  brandName: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    color: colors.white,
    marginLeft: spacing.sm,
  },
  notificationButton: {
    position: 'relative',
    padding: spacing.sm,
  },
  notificationBadge: {
    position: 'absolute',
    top: 4,
    right: 4,
    backgroundColor: colors.error,
    borderRadius: 10,
    width: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  badgeText: {
    color: colors.white,
    fontSize: 10,
    fontWeight: typography.fontWeight.bold,
  },
  greetingContainer: {
    alignItems: 'flex-start',
  },
  greeting: {
    fontSize: typography.fontSize['2xl'],
    fontWeight: typography.fontWeight.bold,
    color: colors.white,
    marginBottom: spacing.xs,
  },
  subtitle: {
    fontSize: typography.fontSize.base,
    color: colors.white,
    opacity: 0.9,
  },
  content: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  section: {
    marginTop: spacing.xl,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text,
    marginBottom: spacing.md,
  },
  seeAllText: {
    fontSize: typography.fontSize.sm,
    color: colors.oceanTeal,
    fontWeight: typography.fontWeight.medium,
  },
  servicesContainer: {
    gap: spacing.md,
  },
  serviceCard: {
    marginBottom: spacing.md,
  },
  quickActionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickActionWrapper: {
    width: (width - spacing.lg * 2 - spacing.md) / 2,
    marginBottom: spacing.md,
  },
  bottomSpacing: {
    height: spacing.xl,
  },
});
