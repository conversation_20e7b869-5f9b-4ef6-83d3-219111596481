/**
 * Main Tab Navigator
 * Bottom tab navigation for the main app features
 */

import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { View, Text, StyleSheet } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import LinearGradient from 'react-native-linear-gradient';

// Screens
import { HomeScreen } from '@/screens/HomeScreen';
import { BookingsScreen } from '@/screens/BookingsScreen';
import { ProfileScreen } from '@/screens/ProfileScreen';
import { ExploreScreen } from '@/screens/ExploreScreen';

// Stack Navigators
import { HomeStackNavigator } from './HomeStackNavigator';
import { BookingStackNavigator } from './BookingStackNavigator';
import { ProfileStackNavigator } from './ProfileStackNavigator';

// Theme
import { colors, spacing, typography } from '@/utils/theme';

const Tab = createBottomTabNavigator();

// Custom Tab Bar Icon Component
interface TabIconProps {
  name: string;
  focused: boolean;
  color: string;
  size: number;
}

const TabIcon: React.FC<TabIconProps> = ({ name, focused, color, size }) => {
  return (
    <View style={[styles.iconContainer, focused && styles.iconContainerFocused]}>
      {focused && (
        <LinearGradient
          colors={colors.primaryGradient}
          style={styles.iconBackground}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        />
      )}
      <Icon
        name={name}
        size={size}
        color={focused ? colors.white : color}
        style={styles.icon}
      />
    </View>
  );
};

// Custom Tab Bar Label Component
interface TabLabelProps {
  focused: boolean;
  children: string;
}

const TabLabel: React.FC<TabLabelProps> = ({ focused, children }) => {
  return (
    <Text
      style={[
        styles.tabLabel,
        {
          color: focused ? colors.skyBlue : colors.gray600,
          fontWeight: focused ? typography.fontWeight.semibold : typography.fontWeight.normal,
        },
      ]}
    >
      {children}
    </Text>
  );
};

export const MainTabNavigator: React.FC = () => {
  return (
    <Tab.Navigator
      screenOptions={{
        headerShown: false,
        tabBarStyle: styles.tabBar,
        tabBarActiveTintColor: colors.skyBlue,
        tabBarInactiveTintColor: colors.gray600,
        tabBarShowLabel: true,
        tabBarHideOnKeyboard: true,
        tabBarLabelStyle: styles.tabLabelStyle,
      }}
    >
      <Tab.Screen
        name="HomeTab"
        component={HomeStackNavigator}
        options={{
          tabBarLabel: ({ focused }) => (
            <TabLabel focused={focused}>Home</TabLabel>
          ),
          tabBarIcon: ({ focused, color, size }) => (
            <TabIcon
              name="home"
              focused={focused}
              color={color}
              size={size}
            />
          ),
        }}
      />
      
      <Tab.Screen
        name="ExploreTab"
        component={ExploreScreen}
        options={{
          tabBarLabel: ({ focused }) => (
            <TabLabel focused={focused}>Explore</TabLabel>
          ),
          tabBarIcon: ({ focused, color, size }) => (
            <TabIcon
              name="explore"
              focused={focused}
              color={color}
              size={size}
            />
          ),
        }}
      />
      
      <Tab.Screen
        name="BookingsTab"
        component={BookingStackNavigator}
        options={{
          tabBarLabel: ({ focused }) => (
            <TabLabel focused={focused}>Bookings</TabLabel>
          ),
          tabBarIcon: ({ focused, color, size }) => (
            <TabIcon
              name="event-seat"
              focused={focused}
              color={color}
              size={size}
            />
          ),
          tabBarBadge: undefined, // Can be set dynamically for pending bookings
        }}
      />
      
      <Tab.Screen
        name="ProfileTab"
        component={ProfileStackNavigator}
        options={{
          tabBarLabel: ({ focused }) => (
            <TabLabel focused={focused}>Profile</TabLabel>
          ),
          tabBarIcon: ({ focused, color, size }) => (
            <TabIcon
              name="person"
              focused={focused}
              color={color}
              size={size}
            />
          ),
        }}
      />
    </Tab.Navigator>
  );
};

const styles = StyleSheet.create({
  tabBar: {
    backgroundColor: colors.white,
    borderTopWidth: 1,
    borderTopColor: colors.gray200,
    height: 70,
    paddingBottom: spacing.sm,
    paddingTop: spacing.sm,
    elevation: 8,
    shadowColor: colors.shadowColor,
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  iconContainerFocused: {
    transform: [{ scale: 1.1 }],
  },
  iconBackground: {
    position: 'absolute',
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  icon: {
    zIndex: 1,
  },
  tabLabel: {
    fontSize: typography.fontSize.xs,
    marginTop: 2,
    textAlign: 'center',
  },
  tabLabelStyle: {
    fontSize: typography.fontSize.xs,
    marginTop: 2,
  },
});
