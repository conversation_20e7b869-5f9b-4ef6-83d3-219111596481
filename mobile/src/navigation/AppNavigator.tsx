/**
 * Main App Navigator
 * Handles navigation between authenticated and unauthenticated flows
 */

import React, { useEffect } from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { useAppSelector, useAppDispatch } from '@/store';
import { selectIsAuthenticated, selectAuthLoading } from '@/store/slices/authSlice';

// Navigators
import { AuthNavigator } from './AuthNavigator';
import { MainTabNavigator } from './MainTabNavigator';
import { OnboardingNavigator } from './OnboardingNavigator';

// Screens
import { SplashScreen } from '@/screens/SplashScreen';
import { LoadingScreen } from '@/components/LoadingScreen';

// Services
import { checkAuthStatus } from '@/services/authService';

const Stack = createNativeStackNavigator();

export const AppNavigator: React.FC = () => {
  const dispatch = useAppDispatch();
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const authLoading = useAppSelector(selectAuthLoading);
  const isFirstLaunch = useAppSelector(state => state.auth.isFirstLaunch);

  useEffect(() => {
    // Check authentication status on app start
    const initializeAuth = async () => {
      try {
        await checkAuthStatus();
      } catch (error) {
        console.error('Auth initialization error:', error);
      }
    };

    initializeAuth();
  }, [dispatch]);

  // Show loading screen while checking auth status
  if (authLoading) {
    return <LoadingScreen />;
  }

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        animation: 'slide_from_right',
        animationDuration: 300,
      }}
    >
      {isFirstLaunch ? (
        // Show onboarding for first-time users
        <Stack.Screen
          name="Onboarding"
          component={OnboardingNavigator}
          options={{ gestureEnabled: false }}
        />
      ) : isAuthenticated ? (
        // Show main app for authenticated users
        <Stack.Screen
          name="Main"
          component={MainTabNavigator}
          options={{ gestureEnabled: false }}
        />
      ) : (
        // Show auth flow for unauthenticated users
        <Stack.Screen
          name="Auth"
          component={AuthNavigator}
          options={{ gestureEnabled: false }}
        />
      )}
    </Stack.Navigator>
  );
};
