/**
 * Vtravelallinone Mobile App Theme
 * Brand colors: Sky Blue, Light Orange, Black
 */

import { DefaultTheme } from 'react-native-paper';
import { Theme } from '@react-navigation/native';

// Bright Neon Brand Colors - Dark Optimized
export const colors = {
  // Primary Bright Colors - Neon & Electric Theme
  electricCyan: '#00D4FF',     // Electric Cyan Blue
  neonPink: '#FF3366',         // Neon Pink
  laserGreen: '#00FF88',       // Laser Green
  solarYellow: '#FFFF00',      // Solar Yellow
  plasmaPurple: '#AA44FF',     // Plasma Purple
  fireOrange: '#FF6600',       // Fire Orange
  iceBlue: '#44DDFF',          // Ice Blue
  voltLime: '#88FF00',         // Volt Lime

  // Secondary Bright Colors - Vibrant Variations
  cyberBlue: '#0099FF',        // Cyber Blue
  hotMagenta: '#FF0066',       // Hot Magenta
  toxicGreen: '#66FF00',       // Toxic Green
  goldRush: '#FFCC00',         // Gold Rush
  royalViolet: '#6600FF',      // Royal Violet
  sunsetRed: '#FF3300',        // Sunset Red
  aquaBright: '#00FFCC',       // Aqua Bright
  electricLime: '#CCFF00',     // Electric Lime

  // Tertiary Bright Colors - Accent Highlights
  neonBlue: '#0066FF',         // Neon Blue
  cherryPop: '#FF0033',        // Cherry Pop
  mintElectric: '#00FF99',     // Mint Electric
  bananaBright: '#FFFF33',     // Banana Bright
  grapeNeon: '#9933FF',        // Grape Neon
  coralBright: '#FF6633',      // Coral Bright
  turboTeal: '#00CCFF',        // Turbo Teal
  limeShock: '#99FF33',        // Lime Shock

  // Bright Neon Gradient Combinations
  primaryGradient: ['#00D4FF', '#0099FF', '#0066FF'],           // Electric Cyan Gradient
  secondaryGradient: ['#FF3366', '#FF0066', '#FF0033'],         // Neon Pink Gradient
  accentGradient: ['#AA44FF', '#6600FF', '#9933FF'],            // Plasma Purple Gradient
  successGradient: ['#00FF88', '#66FF00', '#00FF99'],           // Laser Green Gradient
  heroGradient: ['#00D4FF', '#FF3366', '#00FF88', '#FFFF00'],   // Rainbow Hero Gradient
  premiumGradient: ['#AA44FF', '#FF3366', '#00FFCC'],           // Premium Neon Gradient
  auroraGradient: ['#00D4FF', '#AA44FF', '#FF3366', '#FFFF00', '#00FF88'], // Bright Aurora Gradient
  fireGradient: ['#FF6600', '#FF3300', '#FF6633'],              // Fire Orange Gradient
  iceGradient: ['#44DDFF', '#00CCFF', '#00D4FF'],               // Ice Blue Gradient
  toxicGradient: ['#66FF00', '#CCFF00', '#99FF33'],             // Toxic Green Gradient

  // Bright Dark-Optimized Semantic Colors
  primary: '#00D4FF',          // Electric Cyan Blue
  secondary: '#FF3366',        // Neon Pink
  accent: '#FFFF00',           // Solar Yellow
  background: '#0A0A0F',       // Dark Background
  surface: '#1A1A2E',          // Dark Surface
  text: '#FFFFFF',             // White Text
  textSecondary: '#A0A0B0',    // Light Gray Text
  textTertiary: '#808090',     // Medium Gray Text
  
  // Status Colors - Modern & Vibrant
  success: '#10B981',          // Emerald Green
  warning: '#F59E0B',          // Amber
  error: '#EF4444',            // Red
  info: '#3B82F6',             // Blue

  // Modern Neutral Palette
  white: '#FFFFFF',
  black: '#000000',
  neutral50: '#FAFBFC',        // Pure White
  neutral100: '#F4F6F8',       // Light Gray
  neutral200: '#E4E7EB',       // Soft Gray
  neutral300: '#D1D5DB',       // Medium Gray
  neutral400: '#9CA3AF',       // Cool Gray
  neutral500: '#6B7280',       // Slate Gray
  neutral600: '#4B5563',       // Dark Gray
  neutral700: '#374151',       // Charcoal
  neutral800: '#1F2937',       // Dark Charcoal
  neutral900: '#111827',       // Almost Black

  // Legacy gray support (for backward compatibility)
  gray100: '#F4F6F8',
  gray200: '#E4E7EB',
  gray300: '#D1D5DB',
  gray400: '#9CA3AF',
  gray500: '#6B7280',
  gray600: '#4B5563',
  gray700: '#374151',
  gray800: '#1F2937',
  gray900: '#111827',
  
  // Transparent Colors - Modern Overlays
  overlay: 'rgba(15, 76, 117, 0.6)',
  overlayLight: 'rgba(15, 76, 117, 0.3)',
  shadowColor: 'rgba(15, 76, 117, 0.2)',

  // Component Specific - Modern Design
  cardBackground: '#FFFFFF',
  cardShadow: 'rgba(15, 76, 117, 0.08)',
  inputBackground: '#F4F6F8',
  inputBorder: '#E4E7EB',
  buttonShadow: 'rgba(15, 76, 117, 0.3)',

  // Bright Travel Service Colors - Neon & Distinct
  busColor: '#00D4FF',         // Electric Cyan Blue
  carpoolColor: '#00FF88',     // Laser Green
  bikeColor: '#FF6600',        // Fire Orange

  // Bright Map Colors - Neon & Clear
  routeColor: '#0099FF',       // Cyber Blue
  markerColor: '#FF3366',      // Neon Pink
  currentLocationColor: '#FFFF00', // Solar Yellow

  // Bright Premium Feature Colors
  premiumFeature: '#AA44FF',   // Plasma Purple
  exclusiveFeature: '#FF0066', // Hot Magenta
  limitedOffer: '#00FFCC',     // Aqua Bright
  vipFeature: '#CCFF00',       // Electric Lime
  specialOffer: '#FF6633',     // Coral Bright
};

// Typography
export const typography = {
  fontFamily: {
    regular: 'Inter-Regular',
    medium: 'Inter-Medium',
    semiBold: 'Inter-SemiBold',
    bold: 'Inter-Bold',
    light: 'Inter-Light',
  },
  fontSize: {
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 30,
    '4xl': 36,
    '5xl': 48,
  },
  lineHeight: {
    tight: 1.25,
    normal: 1.5,
    relaxed: 1.75,
  },
  fontWeight: {
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    extrabold: '800',
  },
};

// Spacing
export const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  '2xl': 48,
  '3xl': 64,
  '4xl': 96,
};

// Border Radius
export const borderRadius = {
  none: 0,
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  '2xl': 24,
  full: 9999,
};

// Shadows
export const shadows = {
  sm: {
    shadowColor: colors.shadowColor,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  md: {
    shadowColor: colors.shadowColor,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 4,
  },
  lg: {
    shadowColor: colors.shadowColor,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  xl: {
    shadowColor: colors.shadowColor,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.35,
    shadowRadius: 16,
    elevation: 16,
  },
};

// React Native Paper Theme - Modern Colors
export const theme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: colors.deepOcean,
    accent: colors.coralSunset,
    background: colors.background,
    surface: colors.surface,
    text: colors.text,
    onSurface: colors.text,
    disabled: colors.neutral400,
    placeholder: colors.neutral500,
    backdrop: colors.overlay,
    notification: colors.coralSunset,
    secondary: colors.oceanTeal,
    tertiary: colors.goldenHour,
  },
  fonts: {
    ...DefaultTheme.fonts,
    regular: {
      fontFamily: typography.fontFamily.regular,
      fontWeight: typography.fontWeight.normal,
    },
    medium: {
      fontFamily: typography.fontFamily.medium,
      fontWeight: typography.fontWeight.medium,
    },
    light: {
      fontFamily: typography.fontFamily.light,
      fontWeight: typography.fontWeight.light,
    },
    thin: {
      fontFamily: typography.fontFamily.light,
      fontWeight: typography.fontWeight.light,
    },
  },
  roundness: borderRadius.md,
};

// React Navigation Theme - Modern Colors
export const navigationTheme: Theme = {
  dark: false,
  colors: {
    primary: colors.deepOcean,
    background: colors.background,
    card: colors.cardBackground,
    text: colors.text,
    border: colors.neutral300,
    notification: colors.coralSunset,
  },
};

// Enhanced Premium Component Styles
export const componentStyles = {
  button: {
    primary: {
      backgroundColor: colors.deepOcean,
      borderRadius: borderRadius['2xl'],
      paddingVertical: spacing.lg,
      paddingHorizontal: spacing.xl,
      ...shadows.xl,
    },
    secondary: {
      backgroundColor: colors.coralSunset,
      borderRadius: borderRadius['2xl'],
      paddingVertical: spacing.lg,
      paddingHorizontal: spacing.xl,
      ...shadows.xl,
    },
    accent: {
      backgroundColor: colors.oceanTeal,
      borderRadius: borderRadius['2xl'],
      paddingVertical: spacing.lg,
      paddingHorizontal: spacing.xl,
      ...shadows.xl,
    },
    success: {
      backgroundColor: colors.emeraldGreen,
      borderRadius: borderRadius['2xl'],
      paddingVertical: spacing.lg,
      paddingHorizontal: spacing.xl,
      ...shadows.xl,
    },
    premium: {
      backgroundColor: colors.royalPurple,
      borderRadius: borderRadius['2xl'],
      paddingVertical: spacing.lg,
      paddingHorizontal: spacing.xl,
      ...shadows.xl,
    },
    outline: {
      backgroundColor: 'transparent',
      borderWidth: 2,
      borderColor: colors.deepOcean,
      borderRadius: borderRadius['2xl'],
      paddingVertical: spacing.lg,
      paddingHorizontal: spacing.xl,
    },
    ghost: {
      backgroundColor: 'transparent',
      borderWidth: 1,
      borderColor: colors.oceanTeal,
      borderRadius: borderRadius['2xl'],
      paddingVertical: spacing.md,
      paddingHorizontal: spacing.lg,
    },
  },
  card: {
    default: {
      backgroundColor: colors.cardBackground,
      borderRadius: borderRadius['2xl'],
      padding: spacing.xl,
      ...shadows.lg,
    },
    elevated: {
      backgroundColor: colors.cardBackground,
      borderRadius: borderRadius['2xl'],
      padding: spacing['2xl'],
      ...shadows.xl,
    },
    premium: {
      backgroundColor: colors.cardBackground,
      borderRadius: borderRadius['2xl'],
      padding: spacing.xl,
      borderWidth: 1,
      borderColor: colors.royalPurple,
      ...shadows.xl,
    },
    gradient: {
      borderRadius: borderRadius['2xl'],
      padding: spacing.xl,
      ...shadows.xl,
    },
    glass: {
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderRadius: borderRadius['2xl'],
      padding: spacing.xl,
      borderWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.3)',
      ...shadows.lg,
    },
  },
  input: {
    default: {
      backgroundColor: colors.inputBackground,
      borderWidth: 1,
      borderColor: colors.inputBorder,
      borderRadius: borderRadius.lg,
      paddingVertical: spacing.md,
      paddingHorizontal: spacing.lg,
      fontSize: typography.fontSize.base,
      color: colors.text,
    },
    focused: {
      borderColor: colors.deepOcean,
      borderWidth: 2,
      backgroundColor: colors.white,
    },
    error: {
      borderColor: colors.error,
      borderWidth: 2,
      backgroundColor: colors.white,
    },
  },
};

// Animation Durations
export const animations = {
  fast: 150,
  normal: 300,
  slow: 500,
  verySlow: 1000,
};

// Screen Dimensions Helper
export const layout = {
  window: {
    width: 0, // Will be set by Dimensions
    height: 0, // Will be set by Dimensions
  },
  isSmallDevice: false, // Will be calculated
  headerHeight: 56,
  tabBarHeight: 60,
  statusBarHeight: 24,
};

export default {
  colors,
  typography,
  spacing,
  borderRadius,
  shadows,
  theme,
  navigationTheme,
  componentStyles,
  animations,
  layout,
};
