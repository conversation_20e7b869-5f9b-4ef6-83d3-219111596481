/**
 * Vtravelallinone Mobile App Theme
 * Brand colors: Sky Blue, Light Orange, Black
 */

import { DefaultTheme } from 'react-native-paper';
import { Theme } from '@react-navigation/native';

// Modern Beautiful Brand Colors
export const colors = {
  // Primary Brand Colors - Ocean & Sunset Theme
  deepOcean: '#0F4C75',        // Deep Ocean Blue
  oceanTeal: '#3282B8',        // Ocean Teal
  coralSunset: '#FF6B6B',      // Coral Sunset
  goldenHour: '#FFE66D',       // Golden Hour
  royalPurple: '#6C5CE7',      // Royal Purple

  // Secondary Colors - Fresh & Vibrant
  freshMint: '#00DFA2',        // Fresh Mint
  softLavender: '#A8E6CF',     // Soft Lavender
  warmPeach: '#FFB4A2',        // Warm Peach
  clearSky: '#74B9FF',         // Clear Sky
  rosePink: '#FD79A8',         // Rose Pink

  // Modern Gradient Combinations
  primaryGradient: ['#0F4C75', '#3282B8'],           // Ocean Gradient
  secondaryGradient: ['#FF6B6B', '#FFE66D'],         // Sunset Gradient
  accentGradient: ['#6C5CE7', '#FD79A8'],            // Royal Gradient
  successGradient: ['#00DFA2', '#74B9FF'],           // Fresh Gradient
  heroGradient: ['#0F4C75', '#3282B8', '#FF6B6B'],   // Hero Gradient

  // Semantic Colors - Modern & Accessible
  primary: '#0F4C75',          // Deep Ocean Blue
  secondary: '#3282B8',        // Ocean Teal
  accent: '#FF6B6B',           // Coral Sunset
  background: '#FAFBFC',       // Pure White
  surface: '#F4F6F8',          // Light Gray
  text: '#1E293B',             // Dark Slate
  textSecondary: '#64748B',    // Slate Gray
  
  // Status Colors - Modern & Vibrant
  success: '#10B981',          // Emerald Green
  warning: '#F59E0B',          // Amber
  error: '#EF4444',            // Red
  info: '#3B82F6',             // Blue

  // Modern Neutral Palette
  white: '#FFFFFF',
  black: '#000000',
  neutral50: '#FAFBFC',        // Pure White
  neutral100: '#F4F6F8',       // Light Gray
  neutral200: '#E4E7EB',       // Soft Gray
  neutral300: '#D1D5DB',       // Medium Gray
  neutral400: '#9CA3AF',       // Cool Gray
  neutral500: '#6B7280',       // Slate Gray
  neutral600: '#4B5563',       // Dark Gray
  neutral700: '#374151',       // Charcoal
  neutral800: '#1F2937',       // Dark Charcoal
  neutral900: '#111827',       // Almost Black

  // Legacy gray support (for backward compatibility)
  gray100: '#F4F6F8',
  gray200: '#E4E7EB',
  gray300: '#D1D5DB',
  gray400: '#9CA3AF',
  gray500: '#6B7280',
  gray600: '#4B5563',
  gray700: '#374151',
  gray800: '#1F2937',
  gray900: '#111827',
  
  // Transparent Colors - Modern Overlays
  overlay: 'rgba(15, 76, 117, 0.6)',
  overlayLight: 'rgba(15, 76, 117, 0.3)',
  shadowColor: 'rgba(15, 76, 117, 0.2)',

  // Component Specific - Modern Design
  cardBackground: '#FFFFFF',
  cardShadow: 'rgba(15, 76, 117, 0.08)',
  inputBackground: '#F4F6F8',
  inputBorder: '#E4E7EB',
  buttonShadow: 'rgba(15, 76, 117, 0.3)',

  // Travel Service Colors - Vibrant & Distinct
  busColor: '#0F4C75',         // Deep Ocean Blue
  carpoolColor: '#00DFA2',     // Fresh Mint
  bikeColor: '#FF6B6B',        // Coral Sunset

  // Map Colors - Modern & Clear
  routeColor: '#3282B8',       // Ocean Teal
  markerColor: '#FF6B6B',      // Coral Sunset
  currentLocationColor: '#FFE66D', // Golden Hour
};

// Typography
export const typography = {
  fontFamily: {
    regular: 'Inter-Regular',
    medium: 'Inter-Medium',
    semiBold: 'Inter-SemiBold',
    bold: 'Inter-Bold',
    light: 'Inter-Light',
  },
  fontSize: {
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 30,
    '4xl': 36,
    '5xl': 48,
  },
  lineHeight: {
    tight: 1.25,
    normal: 1.5,
    relaxed: 1.75,
  },
  fontWeight: {
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    extrabold: '800',
  },
};

// Spacing
export const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  '2xl': 48,
  '3xl': 64,
  '4xl': 96,
};

// Border Radius
export const borderRadius = {
  none: 0,
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  '2xl': 24,
  full: 9999,
};

// Shadows
export const shadows = {
  sm: {
    shadowColor: colors.shadowColor,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  md: {
    shadowColor: colors.shadowColor,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 4,
  },
  lg: {
    shadowColor: colors.shadowColor,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  xl: {
    shadowColor: colors.shadowColor,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.35,
    shadowRadius: 16,
    elevation: 16,
  },
};

// React Native Paper Theme - Modern Colors
export const theme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: colors.deepOcean,
    accent: colors.coralSunset,
    background: colors.background,
    surface: colors.surface,
    text: colors.text,
    onSurface: colors.text,
    disabled: colors.neutral400,
    placeholder: colors.neutral500,
    backdrop: colors.overlay,
    notification: colors.coralSunset,
    secondary: colors.oceanTeal,
    tertiary: colors.goldenHour,
  },
  fonts: {
    ...DefaultTheme.fonts,
    regular: {
      fontFamily: typography.fontFamily.regular,
      fontWeight: typography.fontWeight.normal,
    },
    medium: {
      fontFamily: typography.fontFamily.medium,
      fontWeight: typography.fontWeight.medium,
    },
    light: {
      fontFamily: typography.fontFamily.light,
      fontWeight: typography.fontWeight.light,
    },
    thin: {
      fontFamily: typography.fontFamily.light,
      fontWeight: typography.fontWeight.light,
    },
  },
  roundness: borderRadius.md,
};

// React Navigation Theme - Modern Colors
export const navigationTheme: Theme = {
  dark: false,
  colors: {
    primary: colors.deepOcean,
    background: colors.background,
    card: colors.cardBackground,
    text: colors.text,
    border: colors.neutral300,
    notification: colors.coralSunset,
  },
};

// Modern Component Styles
export const componentStyles = {
  button: {
    primary: {
      backgroundColor: colors.deepOcean,
      borderRadius: borderRadius.xl,
      paddingVertical: spacing.md,
      paddingHorizontal: spacing.lg,
      ...shadows.lg,
    },
    secondary: {
      backgroundColor: colors.coralSunset,
      borderRadius: borderRadius.xl,
      paddingVertical: spacing.md,
      paddingHorizontal: spacing.lg,
      ...shadows.lg,
    },
    accent: {
      backgroundColor: colors.oceanTeal,
      borderRadius: borderRadius.xl,
      paddingVertical: spacing.md,
      paddingHorizontal: spacing.lg,
      ...shadows.lg,
    },
    success: {
      backgroundColor: colors.freshMint,
      borderRadius: borderRadius.xl,
      paddingVertical: spacing.md,
      paddingHorizontal: spacing.lg,
      ...shadows.lg,
    },
    outline: {
      backgroundColor: 'transparent',
      borderWidth: 2,
      borderColor: colors.deepOcean,
      borderRadius: borderRadius.xl,
      paddingVertical: spacing.md,
      paddingHorizontal: spacing.lg,
    },
  },
  card: {
    default: {
      backgroundColor: colors.cardBackground,
      borderRadius: borderRadius.xl,
      padding: spacing.lg,
      ...shadows.md,
    },
    elevated: {
      backgroundColor: colors.cardBackground,
      borderRadius: borderRadius.xl,
      padding: spacing.xl,
      ...shadows.xl,
    },
    gradient: {
      borderRadius: borderRadius.xl,
      padding: spacing.lg,
      ...shadows.lg,
    },
  },
  input: {
    default: {
      backgroundColor: colors.inputBackground,
      borderWidth: 1,
      borderColor: colors.inputBorder,
      borderRadius: borderRadius.lg,
      paddingVertical: spacing.md,
      paddingHorizontal: spacing.lg,
      fontSize: typography.fontSize.base,
      color: colors.text,
    },
    focused: {
      borderColor: colors.deepOcean,
      borderWidth: 2,
      backgroundColor: colors.white,
    },
    error: {
      borderColor: colors.error,
      borderWidth: 2,
      backgroundColor: colors.white,
    },
  },
};

// Animation Durations
export const animations = {
  fast: 150,
  normal: 300,
  slow: 500,
  verySlow: 1000,
};

// Screen Dimensions Helper
export const layout = {
  window: {
    width: 0, // Will be set by Dimensions
    height: 0, // Will be set by Dimensions
  },
  isSmallDevice: false, // Will be calculated
  headerHeight: 56,
  tabBarHeight: 60,
  statusBarHeight: 24,
};

export default {
  colors,
  typography,
  spacing,
  borderRadius,
  shadows,
  theme,
  navigationTheme,
  componentStyles,
  animations,
  layout,
};
