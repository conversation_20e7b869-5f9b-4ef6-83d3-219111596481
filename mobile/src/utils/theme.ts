/**
 * Vtravelallinone Mobile App Theme
 * Brand colors: Sky Blue, Light Orange, Black
 */

import { DefaultTheme } from 'react-native-paper';
import { Theme } from '@react-navigation/native';

// Brand Colors
export const colors = {
  // Primary Brand Colors
  skyBlue: '#87CEEB',
  lightOrange: '#FFD4B3',
  travelBlack: '#1a1a1a',
  
  // Gradient Colors
  primaryGradient: ['#87CEEB', '#FFD4B3'],
  secondaryGradient: ['#FFD4B3', '#87CEEB'],
  darkGradient: ['#1a1a1a', '#2a2a2a'],
  
  // Semantic Colors
  primary: '#87CEEB',
  secondary: '#FFD4B3',
  accent: '#FF6B6B',
  background: '#FFFFFF',
  surface: '#F8F9FA',
  text: '#1a1a1a',
  textSecondary: '#6C757D',
  
  // Status Colors
  success: '#28A745',
  warning: '#FFC107',
  error: '#DC3545',
  info: '#17A2B8',
  
  // Neutral Colors
  white: '#FFFFFF',
  black: '#000000',
  gray100: '#F8F9FA',
  gray200: '#E9ECEF',
  gray300: '#DEE2E6',
  gray400: '#CED4DA',
  gray500: '#ADB5BD',
  gray600: '#6C757D',
  gray700: '#495057',
  gray800: '#343A40',
  gray900: '#212529',
  
  // Transparent Colors
  overlay: 'rgba(0, 0, 0, 0.5)',
  overlayLight: 'rgba(0, 0, 0, 0.3)',
  shadowColor: 'rgba(135, 206, 235, 0.3)',
  
  // Component Specific
  cardBackground: '#FFFFFF',
  cardShadow: 'rgba(0, 0, 0, 0.1)',
  inputBackground: '#F8F9FA',
  inputBorder: '#DEE2E6',
  buttonShadow: 'rgba(135, 206, 235, 0.4)',
  
  // Travel Service Colors
  busColor: '#4285F4',
  carpoolColor: '#34A853',
  bikeColor: '#FF9800',
  
  // Map Colors
  routeColor: '#87CEEB',
  markerColor: '#FFD4B3',
  currentLocationColor: '#FF6B6B',
};

// Typography
export const typography = {
  fontFamily: {
    regular: 'Inter-Regular',
    medium: 'Inter-Medium',
    semiBold: 'Inter-SemiBold',
    bold: 'Inter-Bold',
    light: 'Inter-Light',
  },
  fontSize: {
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 30,
    '4xl': 36,
    '5xl': 48,
  },
  lineHeight: {
    tight: 1.25,
    normal: 1.5,
    relaxed: 1.75,
  },
  fontWeight: {
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    extrabold: '800',
  },
};

// Spacing
export const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  '2xl': 48,
  '3xl': 64,
  '4xl': 96,
};

// Border Radius
export const borderRadius = {
  none: 0,
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  '2xl': 24,
  full: 9999,
};

// Shadows
export const shadows = {
  sm: {
    shadowColor: colors.shadowColor,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  md: {
    shadowColor: colors.shadowColor,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 4,
  },
  lg: {
    shadowColor: colors.shadowColor,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  xl: {
    shadowColor: colors.shadowColor,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.35,
    shadowRadius: 16,
    elevation: 16,
  },
};

// React Native Paper Theme
export const theme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: colors.skyBlue,
    accent: colors.lightOrange,
    background: colors.background,
    surface: colors.surface,
    text: colors.text,
    onSurface: colors.text,
    disabled: colors.gray400,
    placeholder: colors.gray500,
    backdrop: colors.overlay,
    notification: colors.accent,
  },
  fonts: {
    ...DefaultTheme.fonts,
    regular: {
      fontFamily: typography.fontFamily.regular,
      fontWeight: typography.fontWeight.normal,
    },
    medium: {
      fontFamily: typography.fontFamily.medium,
      fontWeight: typography.fontWeight.medium,
    },
    light: {
      fontFamily: typography.fontFamily.light,
      fontWeight: typography.fontWeight.light,
    },
    thin: {
      fontFamily: typography.fontFamily.light,
      fontWeight: typography.fontWeight.light,
    },
  },
  roundness: borderRadius.md,
};

// React Navigation Theme
export const navigationTheme: Theme = {
  dark: false,
  colors: {
    primary: colors.skyBlue,
    background: colors.background,
    card: colors.cardBackground,
    text: colors.text,
    border: colors.gray300,
    notification: colors.accent,
  },
};

// Component Styles
export const componentStyles = {
  button: {
    primary: {
      backgroundColor: colors.skyBlue,
      borderRadius: borderRadius.lg,
      paddingVertical: spacing.md,
      paddingHorizontal: spacing.lg,
      ...shadows.md,
    },
    secondary: {
      backgroundColor: colors.lightOrange,
      borderRadius: borderRadius.lg,
      paddingVertical: spacing.md,
      paddingHorizontal: spacing.lg,
      ...shadows.md,
    },
    outline: {
      backgroundColor: 'transparent',
      borderWidth: 2,
      borderColor: colors.skyBlue,
      borderRadius: borderRadius.lg,
      paddingVertical: spacing.md,
      paddingHorizontal: spacing.lg,
    },
  },
  card: {
    default: {
      backgroundColor: colors.cardBackground,
      borderRadius: borderRadius.lg,
      padding: spacing.md,
      ...shadows.md,
    },
    elevated: {
      backgroundColor: colors.cardBackground,
      borderRadius: borderRadius.lg,
      padding: spacing.lg,
      ...shadows.lg,
    },
  },
  input: {
    default: {
      backgroundColor: colors.inputBackground,
      borderWidth: 1,
      borderColor: colors.inputBorder,
      borderRadius: borderRadius.md,
      paddingVertical: spacing.md,
      paddingHorizontal: spacing.md,
      fontSize: typography.fontSize.base,
      color: colors.text,
    },
    focused: {
      borderColor: colors.skyBlue,
      borderWidth: 2,
    },
    error: {
      borderColor: colors.error,
      borderWidth: 2,
    },
  },
};

// Animation Durations
export const animations = {
  fast: 150,
  normal: 300,
  slow: 500,
  verySlow: 1000,
};

// Screen Dimensions Helper
export const layout = {
  window: {
    width: 0, // Will be set by Dimensions
    height: 0, // Will be set by Dimensions
  },
  isSmallDevice: false, // Will be calculated
  headerHeight: 56,
  tabBarHeight: 60,
  statusBarHeight: 24,
};

export default {
  colors,
  typography,
  spacing,
  borderRadius,
  shadows,
  theme,
  navigationTheme,
  componentStyles,
  animations,
  layout,
};
