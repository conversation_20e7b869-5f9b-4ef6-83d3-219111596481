/**
 * V Logo Component
 * Beautiful animated logo with gradient styling
 */

import React, { useEffect } from 'react';
import { View, Text, StyleSheet, ViewStyle } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withTiming,
  withSequence,
  interpolate,
} from 'react-native-reanimated';

// Utils
import { colors, typography, shadows } from '@/utils/theme';

interface VLogoProps {
  size?: 'small' | 'medium' | 'large' | 'xlarge';
  variant?: 'primary' | 'secondary' | 'minimal' | 'glow';
  animated?: boolean;
  style?: ViewStyle;
}

const sizeConfig = {
  small: {
    container: 32,
    fontSize: 14,
    borderRadius: 8,
  },
  medium: {
    container: 40,
    fontSize: 18,
    borderRadius: 12,
  },
  large: {
    container: 56,
    fontSize: 24,
    borderRadius: 16,
  },
  xlarge: {
    container: 80,
    fontSize: 36,
    borderRadius: 20,
  },
};

const variantConfig = {
  primary: {
    gradient: colors.primaryGradient,
    shadow: shadows.md,
  },
  secondary: {
    gradient: colors.secondaryGradient,
    shadow: shadows.md,
  },
  minimal: {
    gradient: [colors.skyBlue, colors.lightOrange],
    shadow: shadows.sm,
  },
  glow: {
    gradient: colors.primaryGradient,
    shadow: shadows.lg,
  },
};

export const VLogo: React.FC<VLogoProps> = ({
  size = 'medium',
  variant = 'primary',
  animated = false,
  style,
}) => {
  const sizeStyles = sizeConfig[size];
  const variantStyles = variantConfig[variant];
  
  // Animation values
  const scale = useSharedValue(1);
  const rotation = useSharedValue(0);
  const shimmer = useSharedValue(0);
  const glow = useSharedValue(0);

  useEffect(() => {
    if (animated) {
      // Floating animation
      scale.value = withRepeat(
        withSequence(
          withTiming(1.05, { duration: 1500 }),
          withTiming(1, { duration: 1500 })
        ),
        -1,
        true
      );

      // Shimmer effect
      shimmer.value = withRepeat(
        withTiming(1, { duration: 2000 }),
        -1,
        false
      );

      // Glow effect for glow variant
      if (variant === 'glow') {
        glow.value = withRepeat(
          withSequence(
            withTiming(1, { duration: 1000 }),
            withTiming(0.5, { duration: 1000 })
          ),
          -1,
          true
        );
      }
    }
  }, [animated, variant]);

  const animatedContainerStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { scale: scale.value },
        { rotate: `${rotation.value}deg` },
      ],
    };
  });

  const animatedShimmerStyle = useAnimatedStyle(() => {
    const translateX = interpolate(
      shimmer.value,
      [0, 1],
      [-sizeStyles.container, sizeStyles.container * 2]
    );

    return {
      transform: [{ translateX }],
      opacity: interpolate(shimmer.value, [0, 0.5, 1], [0, 0.8, 0]),
    };
  });

  const animatedGlowStyle = useAnimatedStyle(() => {
    if (variant !== 'glow') return {};
    
    return {
      shadowOpacity: interpolate(glow.value, [0, 1], [0.3, 0.8]),
      shadowRadius: interpolate(glow.value, [0, 1], [8, 16]),
    };
  });

  const containerStyle = [
    styles.container,
    {
      width: sizeStyles.container,
      height: sizeStyles.container,
      borderRadius: sizeStyles.borderRadius,
      ...variantStyles.shadow,
    },
    variant === 'glow' && styles.glowContainer,
    style,
  ];

  const letterStyle = [
    styles.letter,
    {
      fontSize: sizeStyles.fontSize,
    },
  ];

  return (
    <Animated.View style={[animatedContainerStyle, animatedGlowStyle]}>
      <View style={containerStyle}>
        <LinearGradient
          colors={variantStyles.gradient}
          style={[
            styles.gradient,
            {
              borderRadius: sizeStyles.borderRadius,
            },
          ]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          {/* Shimmer Effect */}
          {animated && (
            <Animated.View style={[styles.shimmer, animatedShimmerStyle]}>
              <LinearGradient
                colors={['transparent', 'rgba(255, 255, 255, 0.4)', 'transparent']}
                style={styles.shimmerGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
              />
            </Animated.View>
          )}
          
          {/* V Letter */}
          <Text style={letterStyle}>V</Text>
        </LinearGradient>
      </View>
    </Animated.View>
  );
};

// Logo with Text Component
interface VLogoWithTextProps extends VLogoProps {
  showText?: boolean;
  textSize?: 'small' | 'medium' | 'large';
}

export const VLogoWithText: React.FC<VLogoWithTextProps> = ({
  showText = true,
  textSize = 'medium',
  ...logoProps
}) => {
  const textSizeConfig = {
    small: typography.fontSize.sm,
    medium: typography.fontSize.lg,
    large: typography.fontSize.xl,
  };

  return (
    <View style={styles.logoWithTextContainer}>
      <VLogo {...logoProps} />
      {showText && (
        <Text
          style={[
            styles.brandText,
            { fontSize: textSizeConfig[textSize] },
          ]}
        >
          Vtravelallinone
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'hidden',
  },
  gradient: {
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  letter: {
    color: colors.white,
    fontWeight: typography.fontWeight.extrabold,
    fontFamily: typography.fontFamily.bold,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
    zIndex: 2,
  },
  shimmer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1,
  },
  shimmerGradient: {
    flex: 1,
    width: '100%',
  },
  glowContainer: {
    shadowColor: colors.skyBlue,
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.6,
    shadowRadius: 12,
    elevation: 12,
  },
  logoWithTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  brandText: {
    color: colors.white,
    fontWeight: typography.fontWeight.bold,
    fontFamily: typography.fontFamily.bold,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
});

export default VLogo;
