/**
 * Vtravelallinone Mobile App
 * Complete Travel Solution for Android and iOS
 */

import React, { useEffect } from 'react';
import { StatusBar, LogBox } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { Provider as PaperProvider } from 'react-native-paper';
import { Provider as ReduxProvider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import Toast from 'react-native-toast-message';
import SplashScreen from 'react-native-splash-screen';
import { GestureHandlerRootView } from 'react-native-gesture-handler';

import { store, persistor } from '@/store';
import { AppNavigator } from '@/navigation/AppNavigator';
import { theme } from '@/utils/theme';
import { LoadingScreen } from '@/components/LoadingScreen';
import { toastConfig } from '@/utils/toastConfig';
import { initializeApp } from '@/services/appInitializer';

// Ignore specific warnings for development
LogBox.ignoreLogs([
  'Non-serializable values were found in the navigation state',
  'VirtualizedLists should never be nested',
]);

const App: React.FC = () => {
  useEffect(() => {
    const initApp = async () => {
      try {
        // Initialize app services
        await initializeApp();
        
        // Hide splash screen after initialization
        setTimeout(() => {
          SplashScreen.hide();
        }, 1500);
      } catch (error) {
        console.error('App initialization error:', error);
        SplashScreen.hide();
      }
    };

    initApp();
  }, []);

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <ReduxProvider store={store}>
        <PersistGate loading={<LoadingScreen />} persistor={persistor}>
          <PaperProvider theme={theme}>
            <NavigationContainer theme={theme}>
              <StatusBar
                barStyle="light-content"
                backgroundColor={theme.colors.primary}
                translucent={false}
              />
              <AppNavigator />
              <Toast config={toastConfig} />
            </NavigationContainer>
          </PaperProvider>
        </PersistGate>
      </ReduxProvider>
    </GestureHandlerRootView>
  );
};

export default App;
