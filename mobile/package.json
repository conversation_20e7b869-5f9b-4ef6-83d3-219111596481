{"name": "vtravelallinone-mobile", "version": "1.0.0", "description": "Vtravelallinone Mobile App - Complete Travel Solution", "main": "index.js", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "type-check": "tsc --noEmit", "build:android": "cd android && ./gradlew assembleRelease", "build:ios": "cd ios && xcodebuild -workspace VtravelallinoneApp.xcworkspace -scheme VtravelallinoneApp -configuration Release -destination generic/platform=iOS -archivePath VtravelallinoneApp.xcarchive archive", "clean": "react-native clean-project-auto", "pod-install": "cd ios && pod install", "reset-cache": "react-native start --reset-cache"}, "dependencies": {"@react-native-async-storage/async-storage": "^1.19.3", "@react-native-community/geolocation": "^3.2.1", "@react-native-community/netinfo": "^9.4.1", "@react-native-firebase/app": "^18.6.1", "@react-native-firebase/messaging": "^18.6.1", "@react-native-firebase/analytics": "^18.6.1", "@react-native-google-signin/google-signin": "^10.1.0", "@react-native-masked-view/masked-view": "^0.3.0", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "@react-navigation/stack": "^6.3.20", "@reduxjs/toolkit": "^1.9.7", "react": "18.2.0", "react-native": "0.72.6", "react-native-animatable": "^1.3.3", "react-native-bootsplash": "^4.7.5", "react-native-config": "^1.5.1", "react-native-device-info": "^10.11.0", "react-native-fast-image": "^8.6.3", "react-native-gesture-handler": "^2.13.4", "react-native-image-picker": "^7.0.3", "react-native-keychain": "^8.1.3", "react-native-linear-gradient": "^2.8.3", "react-native-maps": "^1.8.0", "react-native-modal": "^13.0.1", "react-native-paper": "^5.11.1", "react-native-permissions": "^3.10.1", "react-native-push-notification": "^8.1.1", "react-native-razorpay": "^2.3.0", "react-native-reanimated": "^3.5.4", "react-native-safe-area-context": "^4.7.4", "react-native-screens": "^3.27.0", "react-native-splash-screen": "^3.3.0", "react-native-svg": "^13.14.0", "react-native-toast-message": "^2.1.6", "react-native-vector-icons": "^10.0.2", "react-redux": "^8.1.3", "redux-persist": "^6.0.0", "axios": "^1.6.0", "date-fns": "^2.30.0", "formik": "^2.4.5", "yup": "^1.3.3"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-native": "^0.72.5", "@types/react-test-renderer": "^18.0.0", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4", "detox": "^20.13.0"}, "engines": {"node": ">=16"}, "keywords": ["react-native", "travel", "booking", "bus", "carpool", "bike", "mobile", "android", "ios"], "author": "Vtravelallinone Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/vtravelallinone-mobile.git"}, "bugs": {"url": "https://github.com/your-org/vtravelallinone-mobile/issues"}, "homepage": "https://vtravelallinone.com"}