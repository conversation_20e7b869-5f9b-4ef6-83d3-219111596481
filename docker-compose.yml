version: '3.8'

services:
  # Databases
  mongodb:
    image: mongo:7.0
    container_name: travelallinone-mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: travelallinone
    volumes:
      - mongodb_data:/data/db
      - ./databases/mongodb/init:/docker-entrypoint-initdb.d
    networks:
      - travel-network

  mysql:
    image: mysql:8.0
    container_name: travelallinone-mysql
    restart: unless-stopped
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword123
      MYSQL_DATABASE: travelallinone
      MYSQL_USER: travel_user
      MYSQL_PASSWORD: travel_password123
    volumes:
      - mysql_data:/var/lib/mysql
      - ./databases/mysql/init:/docker-entrypoint-initdb.d
    networks:
      - travel-network

  # Redis for caching and session management
  redis:
    image: redis:7.2-alpine
    container_name: travelallinone-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - travel-network

  # Backend Services
  auth-service:
    build:
      context: ./backend/auth-service
      dockerfile: Dockerfile
    container_name: travelallinone-auth
    restart: unless-stopped
    ports:
      - "8001:8000"
    environment:
      - DATABASE_URL=mysql://travel_user:travel_password123@mysql:3306/travelallinone
      - MONGODB_URL=*************************************************************************
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your-super-secret-jwt-key-change-in-production
    depends_on:
      - mysql
      - mongodb
      - redis
    networks:
      - travel-network

  bus-service:
    build:
      context: ./backend/bus-service
      dockerfile: Dockerfile
    container_name: travelallinone-bus
    restart: unless-stopped
    ports:
      - "8002:8000"
    environment:
      - DATABASE_URL=mysql://travel_user:travel_password123@mysql:3306/travelallinone
      - MONGODB_URL=*************************************************************************
      - REDIS_URL=redis://redis:6379
    depends_on:
      - mysql
      - mongodb
      - redis
    networks:
      - travel-network

  carpool-service:
    build:
      context: ./backend/carpool-service
      dockerfile: Dockerfile
    container_name: travelallinone-carpool
    restart: unless-stopped
    ports:
      - "8003:8000"
    environment:
      - DATABASE_URL=mysql://travel_user:travel_password123@mysql:3306/travelallinone
      - MONGODB_URL=*************************************************************************
      - REDIS_URL=redis://redis:6379
    depends_on:
      - mysql
      - mongodb
      - redis
    networks:
      - travel-network

  bike-service:
    build:
      context: ./backend/bike-service
      dockerfile: Dockerfile
    container_name: travelallinone-bike
    restart: unless-stopped
    ports:
      - "8004:8000"
    environment:
      - DATABASE_URL=mysql://travel_user:travel_password123@mysql:3306/travelallinone
      - MONGODB_URL=*************************************************************************
      - REDIS_URL=redis://redis:6379
    depends_on:
      - mysql
      - mongodb
      - redis
    networks:
      - travel-network

  payment-service:
    build:
      context: ./backend/payment-service
      dockerfile: Dockerfile
    container_name: travelallinone-payment
    restart: unless-stopped
    ports:
      - "8005:8000"
    environment:
      - DATABASE_URL=mysql://travel_user:travel_password123@mysql:3306/travelallinone
      - MONGODB_URL=*************************************************************************
      - REDIS_URL=redis://redis:6379
      - RAZORPAY_KEY_ID=your_razorpay_key_id
      - RAZORPAY_KEY_SECRET=your_razorpay_key_secret
    depends_on:
      - mysql
      - mongodb
      - redis
    networks:
      - travel-network

  chatbot-service:
    build:
      context: ./backend/chatbot-service
      dockerfile: Dockerfile
    container_name: travelallinone-chatbot
    restart: unless-stopped
    ports:
      - "8006:8000"
    environment:
      - MONGODB_URL=*************************************************************************
      - PINECONE_API_KEY=your_pinecone_api_key
      - PINECONE_ENVIRONMENT=your_pinecone_environment
      - OPENAI_API_KEY=your_openai_api_key
    depends_on:
      - mongodb
    networks:
      - travel-network

  # API Gateway (Nginx)
  api-gateway:
    image: nginx:alpine
    container_name: travelallinone-gateway
    restart: unless-stopped
    ports:
      - "8000:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - auth-service
      - bus-service
      - carpool-service
      - bike-service
      - payment-service
      - chatbot-service
    networks:
      - travel-network

volumes:
  mongodb_data:
  mysql_data:
  redis_data:

networks:
  travel-network:
    driver: bridge
