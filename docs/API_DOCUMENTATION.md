# TravelAllinOne API Documentation

## Overview

TravelAllinOne provides a comprehensive set of REST APIs for travel booking services including bus tickets, car pooling, and bike ride sharing across India.

## Base URLs

### Development
- **API Gateway**: `http://localhost:8000`
- **Auth Service**: `http://localhost:8001`
- **Bus Service**: `http://localhost:8002`
- **CarPool Service**: `http://localhost:8003`
- **Bike Service**: `http://localhost:8004`
- **Payment Service**: `http://localhost:8005`
- **Chatbot Service**: `http://localhost:8006`

### Production
- **API Gateway**: `https://api.travelallinone.com`

## Authentication

All protected endpoints require JWT authentication via Bearer token in the Authorization header:

```
Authorization: Bearer <your_jwt_token>
```

## API Services

### 1. Authentication Service (`/api/auth/`)

#### Register User
```http
POST /api/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "phone": "+************",
  "first_name": "<PERSON>",
  "last_name": "<PERSON><PERSON>",
  "password": "securepassword123",
  "role": "passenger"
}
```

#### Login
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securepassword123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "bearer",
    "user": {
      "id": "123",
      "email": "<EMAIL>",
      "first_name": "John",
      "last_name": "Doe",
      "role": "passenger"
    }
  }
}
```

#### Get Profile
```http
GET /api/auth/profile
Authorization: Bearer <token>
```

#### Update Profile
```http
PUT /api/auth/profile
Authorization: Bearer <token>
Content-Type: application/json

{
  "first_name": "John",
  "last_name": "Smith",
  "phone": "+************"
}
```

### 2. Bus Service (`/api/bus/`)

#### Search Buses
```http
POST /api/bus/search
Content-Type: application/json

{
  "source_city": "Mumbai",
  "destination_city": "Pune",
  "travel_date": "2024-01-15T00:00:00Z",
  "passenger_count": 2,
  "bus_type": "ac",
  "max_price": 800
}
```

**Response:**
```json
{
  "success": true,
  "message": "Found 5 buses",
  "data": {
    "buses": [
      {
        "bus": {
          "id": "bus123",
          "operator_name": "RedBus Travels",
          "bus_number": "MH12AB1234",
          "bus_type": "ac",
          "total_seats": 40,
          "available_seats": 15,
          "amenities": ["wifi", "charging_point", "water_bottle"],
          "rating": 4.2,
          "reviews_count": 150
        },
        "route": {
          "source": {
            "city": "Mumbai",
            "latitude": 19.0760,
            "longitude": 72.8777
          },
          "destination": {
            "city": "Pune", 
            "latitude": 18.5204,
            "longitude": 73.8567
          },
          "distance_km": 150,
          "estimated_duration_minutes": 180,
          "departure_time": "2024-01-15T08:00:00Z",
          "arrival_time": "2024-01-15T11:00:00Z"
        },
        "pricing": {
          "regular": 450,
          "premium": 650
        }
      }
    ]
  }
}
```

#### Get Seat Layout
```http
GET /api/bus/seat-layout/{bus_id}?travel_date=2024-01-15T00:00:00Z
```

#### Book Bus
```http
POST /api/bus/book
Authorization: Bearer <token>
Content-Type: application/json

{
  "bus_id": "bus123",
  "route_id": "route456",
  "travel_date": "2024-01-15T08:00:00Z",
  "passenger_details": [
    {
      "name": "John Doe",
      "age": 30,
      "gender": "male"
    }
  ],
  "selected_seats": ["S01", "S02"],
  "contact_phone": "+************"
}
```

### 3. CarPool Service (`/api/carpool/`)

#### Create Trip
```http
POST /api/carpool/trips
Authorization: Bearer <token>
Content-Type: application/json

{
  "source": {
    "latitude": 19.0760,
    "longitude": 72.8777,
    "address": "Bandra West",
    "city": "Mumbai",
    "state": "Maharashtra",
    "pincode": "400050"
  },
  "destination": {
    "latitude": 18.5204,
    "longitude": 73.8567,
    "address": "Koregaon Park",
    "city": "Pune",
    "state": "Maharashtra", 
    "pincode": "411001"
  },
  "departure_time": "2024-01-15T08:00:00Z",
  "available_seats": 3,
  "price_per_seat": 300,
  "vehicle_make": "Maruti",
  "vehicle_model": "Swift",
  "vehicle_color": "White",
  "vehicle_number": "MH12AB1234",
  "smoking_allowed": false,
  "pets_allowed": false,
  "driver_preferences": "Prefer non-smoking passengers"
}
```

#### Search Trips
```http
POST /api/carpool/search
Content-Type: application/json

{
  "source_city": "Mumbai",
  "destination_city": "Pune", 
  "travel_date": "2024-01-15T00:00:00Z",
  "passenger_count": 2,
  "max_price_per_seat": 400
}
```

#### Book Trip
```http
POST /api/carpool/book
Authorization: Bearer <token>
Content-Type: application/json

{
  "trip_id": "trip123",
  "passenger_count": 2,
  "pickup_point": {
    "latitude": 19.0760,
    "longitude": 72.8777,
    "address": "Bandra Station",
    "city": "Mumbai",
    "state": "Maharashtra",
    "pincode": "400050"
  },
  "drop_point": {
    "latitude": 18.5204,
    "longitude": 73.8567,
    "address": "Pune Station",
    "city": "Pune", 
    "state": "Maharashtra",
    "pincode": "411001"
  },
  "passenger_phone": "+************",
  "message_to_driver": "Will be waiting near platform 1"
}
```

### 4. Bike Service (`/api/bike/`)

#### Get Ride Estimate
```http
POST /api/bike/estimate
Content-Type: application/json

{
  "pickup_location": {
    "latitude": 19.0760,
    "longitude": 72.8777,
    "address": "Bandra West",
    "city": "Mumbai",
    "state": "Maharashtra",
    "pincode": "400050"
  },
  "drop_location": {
    "latitude": 19.0896,
    "longitude": 72.8656,
    "address": "Andheri West",
    "city": "Mumbai",
    "state": "Maharashtra",
    "pincode": "400058"
  },
  "ride_type": "regular",
  "passenger_phone": "+************"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Ride estimate calculated",
  "data": {
    "estimate": {
      "distance_km": 8.5,
      "estimated_duration_minutes": 25,
      "estimated_fare": 88.0,
      "surge_multiplier": 1.0,
      "available_drivers": 12
    }
  }
}
```

#### Get Nearby Drivers
```http
GET /api/bike/nearby-drivers?lat=19.0760&lng=72.8777&radius=5
```

#### Book Ride
```http
POST /api/bike/book
Authorization: Bearer <token>
Content-Type: application/json

{
  "pickup_location": {
    "latitude": 19.0760,
    "longitude": 72.8777,
    "address": "Bandra West",
    "city": "Mumbai",
    "state": "Maharashtra",
    "pincode": "400050"
  },
  "drop_location": {
    "latitude": 19.0896,
    "longitude": 72.8656,
    "address": "Andheri West", 
    "city": "Mumbai",
    "state": "Maharashtra",
    "pincode": "400058"
  },
  "ride_type": "regular",
  "passenger_phone": "+************",
  "special_instructions": "Call when you arrive"
}
```

### 5. Payment Service (`/api/payment/`)

#### Initiate Payment
```http
POST /api/payment/initiate
Authorization: Bearer <token>
Content-Type: application/json

{
  "booking_id": "booking123",
  "amount": 450.0,
  "currency": "INR",
  "description": "Bus ticket payment",
  "customer_email": "<EMAIL>",
  "customer_phone": "+************"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Payment initiated successfully",
  "data": {
    "order_id": "order_123456789",
    "amount": 450.0,
    "currency": "INR",
    "key_id": "rzp_test_1234567890"
  }
}
```

#### Verify Payment
```http
POST /api/payment/verify
Content-Type: application/json

{
  "razorpay_order_id": "order_123456789",
  "razorpay_payment_id": "pay_987654321",
  "razorpay_signature": "signature_hash"
}
```

#### Get Payment History
```http
GET /api/payment/history?limit=10&offset=0
Authorization: Bearer <token>
```

#### Process Refund
```http
POST /api/payment/refund/{payment_id}
Authorization: Bearer <token>
Content-Type: application/json

{
  "amount": 450.0,
  "reason": "Trip cancelled by user"
}
```

### 6. Chatbot Service (`/api/chatbot/`)

#### Send Chat Message
```http
POST /api/chatbot/chat
Authorization: Bearer <token>
Content-Type: application/json

{
  "message": "How can I book a bus ticket from Mumbai to Pune?",
  "session_id": "session123",
  "context": {
    "current_page": "bus_search"
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Response generated successfully",
  "data": {
    "message": "To book a bus ticket from Mumbai to Pune, you can use our bus search feature. Simply enter your travel date, select the number of passengers, and choose from available buses. The fare typically ranges from ₹300-800 depending on the bus type.",
    "session_id": "session123",
    "suggestions": [
      "Show me available buses for tomorrow",
      "What are the different bus types?",
      "How can I select my seat?"
    ]
  }
}
```

#### WebSocket Connection
```javascript
// Connect to WebSocket for real-time chat
const ws = new WebSocket('ws://localhost:8006/ws/user123');

ws.onmessage = function(event) {
  const data = JSON.parse(event.data);
  console.log('Received:', data);
};

ws.send(JSON.stringify({
  message: "Hello, I need help with booking",
  type: "chat"
}));
```

## Error Responses

All APIs return consistent error responses:

```json
{
  "success": false,
  "message": "Error description",
  "errors": ["Detailed error message"],
  "data": null
}
```

### Common HTTP Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `500` - Internal Server Error

## Rate Limiting

- **Authentication endpoints**: 5 requests per minute
- **Search endpoints**: 100 requests per minute
- **Booking endpoints**: 10 requests per minute
- **Payment endpoints**: 20 requests per minute

## Webhooks

### Payment Webhook
```http
POST /api/payment/webhook
Content-Type: application/json

{
  "event": "payment.captured",
  "payload": {
    "payment": {
      "entity": {
        "id": "pay_123456789",
        "amount": 45000,
        "currency": "INR",
        "status": "captured"
      }
    }
  }
}
```

## SDKs and Libraries

### JavaScript/Node.js
```bash
npm install @travelallinone/api-client
```

### Python
```bash
pip install travelallinone-api
```

### cURL Examples

See individual endpoint documentation above for cURL examples.

## Support

For API support, contact:
- Email: <EMAIL>
- Documentation: https://docs.travelallinone.com
- Status Page: https://status.travelallinone.com
