# TravelAllinOne - Comprehensive Travel Booking Platform

A full-stack travel booking application providing bus tickets, car pooling, and bike ride sharing services across India.

## Features

### 🚌 Bus Booking (Inspired by AbhiBus)
- Search buses between cities
- Real-time seat availability
- Multiple bus operators
- Seat selection with layout visualization
- Fare comparison and booking

### 🚗 Car Pooling (Inspired by BlaBlaCar)
- Create and search for rides
- Driver and passenger matching
- Route optimization
- Cost sharing calculations
- Rating and review system

### 🏍️ Bike Ride Sharing (Inspired by Rapido)
- Real-time bike taxi booking
- GPS-based driver matching
- Quick short-distance rides
- Dynamic pricing based on demand

### 💬 AI-Powered Chatbot
- Customer support automation
- Booking assistance
- Travel recommendations
- Powered by Pinecone vector database

## Technology Stack

### Frontend
- **Next.js 14** - React framework with App Router
- **TypeScript** - Type safety
- **Tailwind CSS** - Styling
- **Shadcn/ui** - UI components

### Backend
- **FastAPI** - Python microservices framework
- **Python 3.11+** - Backend language
- **Microservices Architecture** - Scalable service design

### Databases
- **MongoDB** - NoSQL for user data, bookings, and chat logs
- **MySQL** - SQL for transactional data, routes, and vehicles

### AI & ML
- **Pinecone** - Vector database for AI chatbot
- **OpenAI API** - Natural language processing

### Payment
- **Razorpay** - Indian payment gateway
- **Indian Rupees (₹)** - Primary currency

## Project Structure

```
TravelAllinOne/
├── frontend/                 # Next.js application
├── backend/
│   ├── auth-service/        # Authentication microservice
│   ├── bus-service/         # Bus booking service
│   ├── carpool-service/     # Car pooling service
│   ├── bike-service/        # Bike ride sharing service
│   ├── payment-service/     # Payment processing service
│   ├── chatbot-service/     # AI chatbot service
│   └── shared/              # Shared utilities and models
├── databases/
│   ├── mongodb/             # MongoDB schemas and migrations
│   └── mysql/               # MySQL schemas and migrations
├── docker-compose.yml       # Development environment
└── docs/                    # Documentation
```

## Getting Started

### Prerequisites
- Node.js 18+
- Python 3.11+
- Docker and Docker Compose
- MongoDB
- MySQL

### Installation

1. Clone the repository
```bash
git clone <repository-url>
cd TravelAllinOne
```

2. Set up the frontend
```bash
cd frontend
npm install
```

3. Set up the backend services
```bash
cd backend
pip install -r requirements.txt
```

4. Start the development environment
```bash
docker-compose up -d
```

## Development

- Frontend: `http://localhost:3000`
- API Gateway: `http://localhost:8000`
- MongoDB: `localhost:27017`
- MySQL: `localhost:3306`

## Contributing

Please read our contributing guidelines before submitting pull requests.

## License

This project is licensed under the MIT License.
