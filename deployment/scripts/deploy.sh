#!/bin/bash

# Vtravelallinone AWS EKS Deployment Script
# This script deploys the entire application to AWS EKS

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
AWS_REGION=${AWS_REGION:-us-east-1}
CLUSTER_NAME=${CLUSTER_NAME:-vtravelallinone-cluster}
NAMESPACE=${NAMESPACE:-vtravelallinone}
AWS_ACCOUNT_ID=${AWS_ACCOUNT_ID}
IMAGE_TAG=${IMAGE_TAG:-latest}

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
check_prerequisites() {
    print_info "Checking prerequisites..."
    
    if ! command_exists aws; then
        print_error "AWS CLI is required but not installed"
        exit 1
    fi
    
    if ! command_exists kubectl; then
        print_error "kubectl is required but not installed"
        exit 1
    fi
    
    if ! command_exists terraform; then
        print_error "Terraform is required but not installed"
        exit 1
    fi
    
    if [ -z "$AWS_ACCOUNT_ID" ]; then
        print_error "AWS_ACCOUNT_ID environment variable is required"
        exit 1
    fi
    
    print_status "All prerequisites met"
}

# Deploy infrastructure with Terraform
deploy_infrastructure() {
    print_info "Deploying AWS infrastructure with Terraform..."
    
    cd deployment/terraform
    
    # Initialize Terraform
    terraform init
    
    # Plan deployment
    terraform plan -var="aws_region=$AWS_REGION" -var="cluster_name=$CLUSTER_NAME"
    
    # Apply infrastructure
    terraform apply -auto-approve -var="aws_region=$AWS_REGION" -var="cluster_name=$CLUSTER_NAME"
    
    cd ../..
    
    print_status "Infrastructure deployed successfully"
}

# Configure kubectl
configure_kubectl() {
    print_info "Configuring kubectl..."
    
    aws eks update-kubeconfig --region $AWS_REGION --name $CLUSTER_NAME
    
    # Verify connection
    kubectl cluster-info
    
    print_status "kubectl configured successfully"
}

# Install required Kubernetes addons
install_addons() {
    print_info "Installing Kubernetes addons..."
    
    # Install AWS Load Balancer Controller
    kubectl apply -k "github.com/aws/eks-charts/stable/aws-load-balancer-controller//crds?ref=master"
    
    # Install cert-manager for SSL certificates
    kubectl apply -f https://github.com/jetstack/cert-manager/releases/download/v1.13.0/cert-manager.yaml
    
    # Install metrics server
    kubectl apply -f https://github.com/kubernetes-sigs/metrics-server/releases/latest/download/components.yaml
    
    # Wait for addons to be ready
    kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=aws-load-balancer-controller -n kube-system --timeout=300s
    kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=cert-manager -n cert-manager --timeout=300s
    kubectl wait --for=condition=ready pod -l k8s-app=metrics-server -n kube-system --timeout=300s
    
    print_status "Addons installed successfully"
}

# Update Kubernetes manifests with correct values
update_manifests() {
    print_info "Updating Kubernetes manifests..."
    
    # Update AWS account ID in manifests
    find deployment/kubernetes -name "*.yaml" -exec sed -i "s/AWS_ACCOUNT_ID/$AWS_ACCOUNT_ID/g" {} \;
    
    # Update image tags
    find deployment/kubernetes -name "*.yaml" -exec sed -i "s/:latest/:$IMAGE_TAG/g" {} \;
    
    print_status "Manifests updated successfully"
}

# Deploy application to Kubernetes
deploy_application() {
    print_info "Deploying application to Kubernetes..."
    
    # Create namespace if it doesn't exist
    kubectl create namespace $NAMESPACE --dry-run=client -o yaml | kubectl apply -f -
    
    # Apply all Kubernetes manifests
    kubectl apply -f deployment/kubernetes/ -n $NAMESPACE
    
    # Wait for deployments to be ready
    print_info "Waiting for deployments to be ready..."
    
    kubectl rollout status deployment/vtravelallinone-frontend -n $NAMESPACE --timeout=600s
    kubectl rollout status deployment/vtravelallinone-auth-service -n $NAMESPACE --timeout=600s
    kubectl rollout status deployment/vtravelallinone-bus-service -n $NAMESPACE --timeout=600s
    kubectl rollout status deployment/vtravelallinone-carpool-service -n $NAMESPACE --timeout=600s
    kubectl rollout status deployment/vtravelallinone-bike-service -n $NAMESPACE --timeout=600s
    kubectl rollout status deployment/vtravelallinone-payment-service -n $NAMESPACE --timeout=600s
    kubectl rollout status deployment/vtravelallinone-chatbot-service -n $NAMESPACE --timeout=600s
    
    print_status "Application deployed successfully"
}

# Run health checks
run_health_checks() {
    print_info "Running health checks..."
    
    # Wait for all pods to be ready
    kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=vtravelallinone -n $NAMESPACE --timeout=600s
    
    # Get service endpoints
    FRONTEND_URL=$(kubectl get ingress vtravelallinone-frontend-ingress -n $NAMESPACE -o jsonpath='{.status.loadBalancer.ingress[0].hostname}' 2>/dev/null || echo "pending")
    API_URL=$(kubectl get ingress vtravelallinone-api-ingress -n $NAMESPACE -o jsonpath='{.status.loadBalancer.ingress[0].hostname}' 2>/dev/null || echo "pending")
    
    if [ "$FRONTEND_URL" != "pending" ]; then
        print_info "Testing frontend health..."
        curl -f "http://$FRONTEND_URL/health" || print_warning "Frontend health check failed"
    else
        print_warning "Frontend URL not ready yet"
    fi
    
    if [ "$API_URL" != "pending" ]; then
        print_info "Testing API health..."
        curl -f "http://$API_URL/health" || print_warning "API health check failed"
    else
        print_warning "API URL not ready yet"
    fi
    
    print_status "Health checks completed"
}

# Display deployment information
show_deployment_info() {
    print_info "Deployment Information:"
    echo ""
    
    # Get cluster info
    echo "🏗️  Cluster Information:"
    kubectl cluster-info
    echo ""
    
    # Get node information
    echo "🖥️  Node Information:"
    kubectl get nodes -o wide
    echo ""
    
    # Get pod status
    echo "🚀 Pod Status:"
    kubectl get pods -n $NAMESPACE -o wide
    echo ""
    
    # Get service information
    echo "🌐 Service Information:"
    kubectl get services -n $NAMESPACE
    echo ""
    
    # Get ingress information
    echo "🔗 Ingress Information:"
    kubectl get ingress -n $NAMESPACE
    echo ""
    
    # Get URLs
    FRONTEND_URL=$(kubectl get ingress vtravelallinone-frontend-ingress -n $NAMESPACE -o jsonpath='{.status.loadBalancer.ingress[0].hostname}' 2>/dev/null || echo "pending")
    API_URL=$(kubectl get ingress vtravelallinone-api-ingress -n $NAMESPACE -o jsonpath='{.status.loadBalancer.ingress[0].hostname}' 2>/dev/null || echo "pending")
    
    echo "📱 Application URLs:"
    echo "Frontend: http://$FRONTEND_URL"
    echo "API: http://$API_URL"
    echo ""
    
    print_status "Deployment completed successfully! 🎉"
}

# Main deployment function
main() {
    echo "🚀 Starting Vtravelallinone Deployment to AWS EKS"
    echo "=================================================="
    
    check_prerequisites
    deploy_infrastructure
    configure_kubectl
    install_addons
    update_manifests
    deploy_application
    run_health_checks
    show_deployment_info
    
    echo ""
    print_status "🎉 Vtravelallinone has been successfully deployed to AWS EKS!"
    echo ""
    print_info "Next steps:"
    echo "1. Configure your domain DNS to point to the load balancer"
    echo "2. Set up SSL certificates"
    echo "3. Configure monitoring and alerting"
    echo "4. Set up backup procedures"
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "infrastructure")
        check_prerequisites
        deploy_infrastructure
        ;;
    "application")
        check_prerequisites
        configure_kubectl
        update_manifests
        deploy_application
        ;;
    "health")
        configure_kubectl
        run_health_checks
        ;;
    "info")
        configure_kubectl
        show_deployment_info
        ;;
    *)
        echo "Usage: $0 {deploy|infrastructure|application|health|info}"
        echo ""
        echo "Commands:"
        echo "  deploy        - Full deployment (default)"
        echo "  infrastructure - Deploy only AWS infrastructure"
        echo "  application   - Deploy only Kubernetes application"
        echo "  health        - Run health checks"
        echo "  info          - Show deployment information"
        exit 1
        ;;
esac
