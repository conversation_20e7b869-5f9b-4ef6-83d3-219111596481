# 🚀 Vtravelallinone AWS Free Tier Kubernetes Deployment Guide

## 📋 Overview

This guide provides step-by-step instructions to deploy Vtravelallinone on AWS using Kubernetes (EKS) with free tier optimization. The architecture is designed to minimize costs while maintaining production-ready capabilities.

## 🏗️ Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                        AWS Cloud                           │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │   Route 53      │    │   CloudFront    │                │
│  │   (DNS)         │    │   (CDN)         │                │
│  └─────────────────┘    └─────────────────┘                │
│           │                       │                        │
│  ┌─────────────────────────────────────────────────────────┐│
│  │              Application Load Balancer                 ││
│  └─────────────────────────────────────────────────────────┘│
│           │                                                │
│  ┌─────────────────────────────────────────────────────────┐│
│  │                  EKS Cluster                           ││
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     ││
│  │  │   Frontend  │  │  Backend    │  │  Databases  │     ││
│  │  │   (Next.js) │  │  Services   │  │  (MySQL,    │     ││
│  │  │             │  │  (FastAPI)  │  │   Redis,    │     ││
│  │  │             │  │             │  │   MongoDB)  │     ││
│  │  └─────────────┘  └─────────────┘  └─────────────┘     ││
│  └─────────────────────────────────────────────────────────┘│
│                                                            │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │      ECR        │    │   CloudWatch    │                │
│  │  (Container     │    │  (Monitoring)   │                │
│  │   Registry)     │    │                 │                │
│  └─────────────────┘    └─────────────────┘                │
└─────────────────────────────────────────────────────────────┘
```

## 💰 Cost Optimization (AWS Free Tier)

### Free Tier Resources Used:
- **EKS Control Plane**: Free for 12 months
- **EC2 Instances**: t3.small (2 instances) - 750 hours/month free
- **EBS Storage**: 30GB free
- **Load Balancer**: 750 hours/month free
- **CloudWatch**: 10 custom metrics free
- **ECR**: 500MB storage free

### Estimated Monthly Cost: $30-50 USD
(After free tier limits, primarily for additional storage and data transfer)

## 🛠️ Prerequisites

### Required Tools:
```bash
# AWS CLI
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
sudo ./aws/install

# kubectl
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl

# Terraform
wget https://releases.hashicorp.com/terraform/1.6.0/terraform_1.6.0_linux_amd64.zip
unzip terraform_1.6.0_linux_amd64.zip
sudo mv terraform /usr/local/bin/

# Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
```

### AWS Account Setup:
1. Create AWS account (free tier eligible)
2. Create IAM user with required permissions
3. Configure AWS CLI credentials

### Required AWS Permissions:
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "eks:*",
        "ec2:*",
        "iam:*",
        "ecr:*",
        "elasticloadbalancing:*",
        "route53:*",
        "cloudformation:*",
        "cloudwatch:*"
      ],
      "Resource": "*"
    }
  ]
}
```

## 🚀 Quick Start Deployment

### 1. Clone Repository
```bash
git clone https://github.com/your-org/vtravelallinone.git
cd vtravelallinone
```

### 2. Set Environment Variables
```bash
export AWS_ACCOUNT_ID="************"
export AWS_REGION="us-east-1"
export CLUSTER_NAME="vtravelallinone-cluster"
```

### 3. Run Automated Deployment
```bash
# Make script executable
chmod +x deployment/scripts/deploy.sh

# Run full deployment
./deployment/scripts/deploy.sh
```

## 📝 Manual Deployment Steps

### Step 1: Deploy Infrastructure
```bash
cd deployment/terraform

# Initialize Terraform
terraform init

# Plan deployment
terraform plan -var="aws_region=us-east-1"

# Deploy infrastructure
terraform apply -auto-approve
```

### Step 2: Configure kubectl
```bash
aws eks update-kubeconfig --region us-east-1 --name vtravelallinone-cluster
```

### Step 3: Install Kubernetes Addons
```bash
# AWS Load Balancer Controller
kubectl apply -k "github.com/aws/eks-charts/stable/aws-load-balancer-controller//crds?ref=master"

# Cert-manager for SSL
kubectl apply -f https://github.com/jetstack/cert-manager/releases/download/v1.13.0/cert-manager.yaml

# Metrics Server
kubectl apply -f https://github.com/kubernetes-sigs/metrics-server/releases/latest/download/components.yaml
```

### Step 4: Build and Push Images
```bash
# Login to ECR
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.us-east-1.amazonaws.com

# Build and push frontend
cd frontend
docker build -t $AWS_ACCOUNT_ID.dkr.ecr.us-east-1.amazonaws.com/vtravelallinone-frontend:latest .
docker push $AWS_ACCOUNT_ID.dkr.ecr.us-east-1.amazonaws.com/vtravelallinone-frontend:latest

# Build and push backend services
for service in auth-service bus-service carpool-service bike-service payment-service chatbot-service; do
  cd backend/$service
  docker build -t $AWS_ACCOUNT_ID.dkr.ecr.us-east-1.amazonaws.com/vtravelallinone-$service:latest .
  docker push $AWS_ACCOUNT_ID.dkr.ecr.us-east-1.amazonaws.com/vtravelallinone-$service:latest
  cd ../..
done
```

### Step 5: Deploy Application
```bash
# Update manifests with your AWS account ID
find deployment/kubernetes -name "*.yaml" -exec sed -i "s/AWS_ACCOUNT_ID/$AWS_ACCOUNT_ID/g" {} \;

# Apply Kubernetes manifests
kubectl apply -f deployment/kubernetes/
```

### Step 6: Verify Deployment
```bash
# Check pod status
kubectl get pods -n vtravelallinone

# Check services
kubectl get services -n vtravelallinone

# Check ingress
kubectl get ingress -n vtravelallinone
```

## 🔧 Configuration

### Environment Variables
Update the following files with your specific values:

#### `deployment/kubernetes/01-configmap.yaml`
- Database connection strings
- External API keys
- Service URLs

#### `deployment/kubernetes/02-secrets.yaml`
- Database passwords
- JWT secrets
- Payment gateway credentials
- External API keys

### SSL Certificates
```bash
# Install cert-manager issuer
kubectl apply -f - <<EOF
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - http01:
        ingress:
          class: alb
EOF
```

## 📊 Monitoring Setup

### Deploy Prometheus
```bash
kubectl apply -f deployment/monitoring/prometheus.yaml
```

### Access Monitoring
```bash
# Port forward to access Prometheus
kubectl port-forward svc/prometheus 9090:9090 -n vtravelallinone

# Access at http://localhost:9090
```

## 🔄 CI/CD Pipeline

The GitHub Actions workflow automatically:
1. Runs security scans
2. Builds and tests code
3. Builds Docker images
4. Pushes to ECR
5. Deploys to EKS
6. Runs health checks

### Required GitHub Secrets:
```
AWS_ACCESS_KEY_ID
AWS_SECRET_ACCESS_KEY
AWS_ACCOUNT_ID
SLACK_WEBHOOK_URL (optional)
```

## 🛡️ Security Best Practices

### Network Security
- Private subnets for worker nodes
- Security groups with minimal required access
- Network policies for pod-to-pod communication

### Container Security
- Non-root containers
- Read-only root filesystems
- Resource limits and requests
- Security context constraints

### Secrets Management
- Kubernetes secrets for sensitive data
- AWS IAM roles for service accounts
- Encrypted storage for secrets

## 📈 Scaling

### Horizontal Pod Autoscaler
```bash
# Already configured in manifests
kubectl get hpa -n vtravelallinone
```

### Cluster Autoscaler
```bash
# Deploy cluster autoscaler
kubectl apply -f https://raw.githubusercontent.com/kubernetes/autoscaler/master/cluster-autoscaler/cloudprovider/aws/examples/cluster-autoscaler-autodiscover.yaml
```

## 🔍 Troubleshooting

### Common Issues

#### Pods not starting
```bash
# Check pod logs
kubectl logs -f deployment/vtravelallinone-frontend -n vtravelallinone

# Check events
kubectl get events -n vtravelallinone --sort-by='.lastTimestamp'
```

#### Load balancer not accessible
```bash
# Check ingress status
kubectl describe ingress vtravelallinone-frontend-ingress -n vtravelallinone

# Check AWS Load Balancer Controller logs
kubectl logs -f deployment/aws-load-balancer-controller -n kube-system
```

#### Database connection issues
```bash
# Check database pod status
kubectl get pods -l app.kubernetes.io/component=mysql -n vtravelallinone

# Test database connectivity
kubectl exec -it deployment/vtravelallinone-mysql -n vtravelallinone -- mysql -u root -p
```

## 🧹 Cleanup

### Remove Application
```bash
kubectl delete -f deployment/kubernetes/
```

### Remove Infrastructure
```bash
cd deployment/terraform
terraform destroy -auto-approve
```

## 📞 Support

For issues and questions:
- Create GitHub issues
- Check documentation
- Review logs and monitoring

## 🎯 Next Steps

1. **Domain Setup**: Configure your domain to point to the load balancer
2. **SSL Configuration**: Set up proper SSL certificates
3. **Monitoring**: Configure alerts and dashboards
4. **Backup**: Set up database backup procedures
5. **Performance**: Optimize based on usage patterns

---

**🎉 Congratulations! Your Vtravelallinone application is now running on AWS EKS with Kubernetes!**
