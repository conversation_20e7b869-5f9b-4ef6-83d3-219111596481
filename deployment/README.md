# 🚀 Vtravelallinone AWS Free Tier Kubernetes Deployment

## 📋 Complete Deployment Package

This directory contains everything needed to deploy Vtravelallinone on AWS using Kubernetes with free tier optimization.

## 📁 Directory Structure

```
deployment/
├── terraform/              # AWS Infrastructure as Code
│   ├── main.tf             # Main Terraform configuration
│   ├── variables.tf        # Input variables
│   └── outputs.tf          # Output values
├── kubernetes/             # Kubernetes manifests
│   ├── 00-namespace.yaml   # Namespace and resource quotas
│   ├── 01-configmap.yaml   # Application configuration
│   ├── 02-secrets.yaml     # Secrets and credentials
│   ├── 03-frontend-deployment.yaml  # Frontend deployment
│   ├── 04-backend-services.yaml     # Backend microservices
│   ├── 05-databases.yaml   # Database deployments
│   └── 06-ingress.yaml     # Ingress and networking
├── docker/                 # Docker configurations
│   ├── frontend.Dockerfile # Frontend container
│   └── backend-service.Dockerfile  # Backend template
├── scripts/                # Deployment scripts
│   └── deploy.sh          # Automated deployment script
├── monitoring/             # Monitoring setup
│   └── prometheus.yaml    # Prometheus configuration
└── docs/                  # Documentation
    └── AWS_DEPLOYMENT_GUIDE.md  # Comprehensive guide
```

## 🎯 Quick Start

### 1. Prerequisites
- AWS Account (free tier eligible)
- AWS CLI configured
- kubectl installed
- Terraform installed
- Docker installed

### 2. Environment Setup
```bash
export AWS_ACCOUNT_ID="your-aws-account-id"
export AWS_REGION="us-east-1"
export CLUSTER_NAME="vtravelallinone-cluster"
```

### 3. One-Command Deployment
```bash
chmod +x deployment/scripts/deploy.sh
./deployment/scripts/deploy.sh
```

## 🏗️ Architecture Components

### AWS Infrastructure (Terraform)
- **EKS Cluster**: Managed Kubernetes cluster
- **VPC**: Custom VPC with public/private subnets
- **Security Groups**: Network security configuration
- **IAM Roles**: Service permissions
- **ECR Repositories**: Container image storage
- **Load Balancer**: Application Load Balancer

### Kubernetes Applications
- **Frontend**: Next.js application (2 replicas)
- **Backend Services**: 6 FastAPI microservices
  - Auth Service (8001)
  - Bus Service (8002)
  - CarPool Service (8003)
  - Bike Service (8004)
  - Payment Service (8005)
  - Chatbot Service (8006)
- **Databases**: MySQL, Redis, MongoDB
- **Ingress**: AWS Load Balancer Controller
- **Monitoring**: Prometheus metrics

### CI/CD Pipeline (GitHub Actions)
- **Security Scanning**: Trivy vulnerability scanner
- **Code Quality**: Linting and testing
- **Container Building**: Multi-stage Docker builds
- **Automated Deployment**: Zero-downtime deployments
- **Health Checks**: Comprehensive validation

## 💰 Cost Optimization

### Free Tier Resources
- EKS Control Plane: Free for 12 months
- EC2 t3.small instances: 750 hours/month
- EBS Storage: 30GB free
- Load Balancer: 750 hours/month
- CloudWatch: 10 custom metrics

### Estimated Monthly Cost: $30-50 USD
(After free tier limits)

## 🔧 Configuration

### Required Secrets
Update `deployment/kubernetes/02-secrets.yaml` with:
- Database passwords
- JWT secrets
- Payment gateway credentials
- External API keys

### Environment Variables
Configure `deployment/kubernetes/01-configmap.yaml` with:
- Service URLs
- Database connections
- Feature flags
- API endpoints

## 📊 Monitoring & Observability

### Prometheus Metrics
- Application performance metrics
- Infrastructure monitoring
- Custom business metrics
- Alerting rules

### CloudWatch Integration
- EKS cluster logs
- Application logs
- Infrastructure metrics
- Cost monitoring

## 🛡️ Security Features

### Network Security
- Private subnets for worker nodes
- Security groups with minimal access
- Network policies for pod communication
- TLS encryption for all traffic

### Container Security
- Non-root containers
- Read-only root filesystems
- Resource limits and requests
- Security context constraints

### Secrets Management
- Kubernetes secrets for sensitive data
- AWS IAM roles for service accounts
- Encrypted storage

## 📈 Scaling & Performance

### Auto Scaling
- **Horizontal Pod Autoscaler**: Scale pods based on CPU/memory
- **Cluster Autoscaler**: Scale nodes based on demand
- **Load Balancing**: Distribute traffic across replicas

### Performance Optimization
- **Resource Requests/Limits**: Efficient resource allocation
- **Caching**: Redis for session and data caching
- **CDN Ready**: CloudFront integration support
- **Database Optimization**: Connection pooling and indexing

## 🔄 Deployment Options

### Automated Deployment
```bash
./deployment/scripts/deploy.sh
```

### Manual Step-by-Step
```bash
# Infrastructure only
./deployment/scripts/deploy.sh infrastructure

# Application only
./deployment/scripts/deploy.sh application

# Health checks
./deployment/scripts/deploy.sh health

# Deployment info
./deployment/scripts/deploy.sh info
```

### CI/CD Pipeline
- Push to `main` branch for production deployment
- Push to `develop` branch for staging deployment
- Pull requests trigger testing and validation

## 🧪 Testing & Validation

### Health Checks
- Kubernetes liveness/readiness probes
- Application health endpoints
- Database connectivity tests
- External service validation

### Load Testing
```bash
# Install k6 for load testing
kubectl apply -f https://raw.githubusercontent.com/grafana/k6-operator/main/bundle.yaml

# Run load tests
kubectl apply -f - <<EOF
apiVersion: k6.io/v1alpha1
kind: K6
metadata:
  name: vtravelallinone-load-test
spec:
  parallelism: 4
  script:
    configMap:
      name: load-test-script
      file: test.js
EOF
```

## 🔍 Troubleshooting

### Common Commands
```bash
# Check pod status
kubectl get pods -n vtravelallinone

# View logs
kubectl logs -f deployment/vtravelallinone-frontend -n vtravelallinone

# Check events
kubectl get events -n vtravelallinone --sort-by='.lastTimestamp'

# Debug networking
kubectl exec -it pod-name -n vtravelallinone -- /bin/bash
```

### Debug Tools
- Kubernetes dashboard
- Prometheus metrics
- CloudWatch logs
- AWS X-Ray tracing

## 🧹 Cleanup

### Remove Application
```bash
kubectl delete namespace vtravelallinone
```

### Remove Infrastructure
```bash
cd deployment/terraform
terraform destroy -auto-approve
```

## 📚 Documentation

- **[AWS Deployment Guide](docs/AWS_DEPLOYMENT_GUIDE.md)**: Comprehensive deployment instructions
- **[Architecture Overview](docs/ARCHITECTURE.md)**: System design and components
- **[Security Guide](docs/SECURITY.md)**: Security best practices
- **[Monitoring Guide](docs/MONITORING.md)**: Observability setup
- **[Troubleshooting](docs/TROUBLESHOOTING.md)**: Common issues and solutions

## 🎯 Production Readiness

### Before Going Live
1. **Domain Configuration**: Set up DNS records
2. **SSL Certificates**: Configure HTTPS with Let's Encrypt
3. **Monitoring Alerts**: Set up CloudWatch alarms
4. **Backup Strategy**: Configure database backups
5. **Disaster Recovery**: Plan for failure scenarios
6. **Performance Testing**: Load test the application
7. **Security Audit**: Review security configurations

### Maintenance
- Regular security updates
- Database maintenance
- Log rotation
- Cost optimization reviews
- Performance monitoring

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Make changes
4. Test thoroughly
5. Submit pull request

## 📞 Support

- **Issues**: Create GitHub issues for bugs
- **Questions**: Use GitHub discussions
- **Documentation**: Check the docs/ directory
- **Monitoring**: Use Prometheus/Grafana dashboards

---

## 🎉 Success!

Your Vtravelallinone application is now ready for deployment on AWS with:

✅ **Production-ready Kubernetes setup**  
✅ **Cost-optimized for AWS free tier**  
✅ **Automated CI/CD pipeline**  
✅ **Comprehensive monitoring**  
✅ **Security best practices**  
✅ **Scalable architecture**  
✅ **Complete documentation**  

**Happy deploying! 🚀**
