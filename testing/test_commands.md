# TravelAllinOne Testing Commands & Dummy Data

## 🚀 Quick Start Commands

### 1. Start All Services

```bash
# Terminal 1: Start Frontend
cd frontend
npm run dev
# Runs on: http://localhost:3002

# Terminal 2: Start Auth Service
cd backend/auth-service
pip install -r requirements.txt
uvicorn main:app --host 0.0.0.0 --port 8001 --reload

# Terminal 3: Start Bus Service
cd backend/bus-service
pip install -r requirements.txt
uvicorn main:app --host 0.0.0.0 --port 8002 --reload

# Terminal 4: Start CarPool Service
cd backend/carpool-service
pip install -r requirements.txt
uvicorn main:app --host 0.0.0.0 --port 8003 --reload

# Terminal 5: Start Bike Service
cd backend/bike-service
pip install -r requirements.txt
uvicorn main:app --host 0.0.0.0 --port 8004 --reload

# Terminal 6: Start Payment Service
cd backend/payment-service
pip install -r requirements.txt
uvicorn main:app --host 0.0.0.0 --port 8005 --reload

# Terminal 7: Start Chatbot Service
cd backend/chatbot-service
pip install -r requirements.txt
uvicorn main:app --host 0.0.0.0 --port 8006 --reload
```

### 2. Test Service Health

```bash
# Check all services are running
curl http://localhost:8001/health  # Auth Service
curl http://localhost:8002/health  # Bus Service
curl http://localhost:8003/health  # CarPool Service
curl http://localhost:8004/health  # Bike Service
curl http://localhost:8005/health  # Payment Service
curl http://localhost:8006/health  # Chatbot Service
```

## 🧪 API Testing with cURL

### Authentication Service (Port 8001)

#### Register User
```bash
curl -X POST http://localhost:8001/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "phone": "+919876543210",
    "first_name": "John",
    "last_name": "Doe",
    "password": "securepassword123",
    "role": "passenger"
  }'
```

#### Login User
```bash
curl -X POST http://localhost:8001/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "securepassword123"
  }'
```

#### Get Profile (Replace TOKEN with actual token from login)
```bash
curl -X GET http://localhost:8001/profile \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE"
```

### Bus Service (Port 8002)

#### Search Buses
```bash
curl -X POST http://localhost:8002/search \
  -H "Content-Type: application/json" \
  -d '{
    "source_city": "Mumbai",
    "destination_city": "Pune",
    "travel_date": "2024-01-15T00:00:00Z",
    "passenger_count": 2,
    "bus_type": "ac"
  }'
```

#### Get Seat Layout
```bash
curl -X GET "http://localhost:8002/seat-layout/bus123?travel_date=2024-01-15T00:00:00Z"
```

### CarPool Service (Port 8003)

#### Search CarPool Trips
```bash
curl -X POST http://localhost:8003/search \
  -H "Content-Type: application/json" \
  -d '{
    "source_city": "Mumbai",
    "destination_city": "Pune",
    "travel_date": "2024-01-15T00:00:00Z",
    "passenger_count": 2,
    "max_price_per_seat": 400
  }'
```

#### Create Trip (Requires Auth Token)
```bash
curl -X POST http://localhost:8003/trips \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE" \
  -d '{
    "source": {
      "latitude": 19.0760,
      "longitude": 72.8777,
      "address": "Bandra West",
      "city": "Mumbai",
      "state": "Maharashtra",
      "pincode": "400050"
    },
    "destination": {
      "latitude": 18.5204,
      "longitude": 73.8567,
      "address": "Koregaon Park",
      "city": "Pune",
      "state": "Maharashtra",
      "pincode": "411001"
    },
    "departure_time": "2024-01-15T08:00:00Z",
    "available_seats": 3,
    "price_per_seat": 300,
    "vehicle_make": "Maruti",
    "vehicle_model": "Swift",
    "vehicle_color": "White",
    "vehicle_number": "MH12AB1234",
    "smoking_allowed": false,
    "pets_allowed": false
  }'
```

### Bike Service (Port 8004)

#### Get Ride Estimate
```bash
curl -X POST http://localhost:8004/estimate \
  -H "Content-Type: application/json" \
  -d '{
    "pickup_location": {
      "latitude": 19.0760,
      "longitude": 72.8777,
      "address": "Bandra West",
      "city": "Mumbai",
      "state": "Maharashtra",
      "pincode": "400050"
    },
    "drop_location": {
      "latitude": 19.0896,
      "longitude": 72.8656,
      "address": "Andheri West",
      "city": "Mumbai",
      "state": "Maharashtra",
      "pincode": "400058"
    },
    "ride_type": "regular",
    "passenger_phone": "+919876543210"
  }'
```

#### Get Nearby Drivers
```bash
curl -X GET "http://localhost:8004/nearby-drivers?lat=19.0760&lng=72.8777&radius=5"
```

### Payment Service (Port 8005)

#### Initiate Payment (Requires Auth Token)
```bash
curl -X POST http://localhost:8005/initiate \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE" \
  -d '{
    "booking_id": "booking123",
    "amount": 450.0,
    "currency": "INR",
    "description": "Bus ticket payment",
    "customer_email": "<EMAIL>",
    "customer_phone": "+919876543210"
  }'
```

### Chatbot Service (Port 8006)

#### Send Chat Message (Requires Auth Token)
```bash
curl -X POST http://localhost:8006/chat \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE" \
  -d '{
    "message": "How can I book a bus ticket from Mumbai to Pune?",
    "session_id": null,
    "context": {
      "current_page": "bus_search"
    }
  }'
```

## 🌐 Frontend Testing

### 1. Open Frontend
```bash
# Open in browser
open http://localhost:3002
```

### 2. Test User Flows

#### Registration Flow
1. Go to http://localhost:3002/register
2. Fill form with dummy data:
   - Email: <EMAIL>
   - Phone: +919876543210
   - Name: Test User
   - Password: password123

#### Login Flow
1. Go to http://localhost:3002/login
2. Use registered credentials

#### Bus Booking Flow
1. Go to http://localhost:3002/bus
2. Search: Mumbai → Pune, Tomorrow, 2 passengers

#### CarPool Flow
1. Go to http://localhost:3002/carpool
2. Search rides or create new trip

#### Bike Ride Flow
1. Go to http://localhost:3002/bike
2. Enter pickup and drop locations
3. Get estimate and book

## 🗄️ Database Setup (MySQL)

### Create Database Schema
```sql
-- Create database
CREATE DATABASE travelallinone;
USE travelallinone;

-- Users table
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20) UNIQUE NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role ENUM('passenger', 'driver', 'operator', 'admin') DEFAULT 'passenger',
    date_of_birth DATE,
    gender ENUM('male', 'female', 'other'),
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Buses table
CREATE TABLE buses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    operator_name VARCHAR(255) NOT NULL,
    bus_number VARCHAR(50) NOT NULL,
    bus_type ENUM('regular', 'ac', 'sleeper', 'luxury') NOT NULL,
    total_seats INT NOT NULL,
    amenities TEXT,
    rating DECIMAL(3,2),
    reviews_count INT DEFAULT 0,
    route_id INT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Routes table
CREATE TABLE routes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    source_city VARCHAR(100) NOT NULL,
    destination_city VARCHAR(100) NOT NULL,
    distance_km DECIMAL(8,2) NOT NULL,
    estimated_duration_minutes INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Schedules table
CREATE TABLE schedules (
    id INT AUTO_INCREMENT PRIMARY KEY,
    bus_id INT NOT NULL,
    departure_time DATETIME NOT NULL,
    arrival_time DATETIME NOT NULL,
    available_seats INT NOT NULL,
    pricing JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (bus_id) REFERENCES buses(id)
);

-- Bookings table
CREATE TABLE bookings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    bus_id INT,
    route_id INT,
    booking_reference VARCHAR(50) UNIQUE NOT NULL,
    travel_date DATETIME NOT NULL,
    passenger_count INT NOT NULL,
    selected_seats TEXT,
    total_amount DECIMAL(10,2) NOT NULL,
    status ENUM('pending', 'confirmed', 'cancelled', 'completed', 'refunded') DEFAULT 'pending',
    contact_phone VARCHAR(20) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- CarPool trips table
CREATE TABLE carpool_trips (
    id INT AUTO_INCREMENT PRIMARY KEY,
    driver_id INT NOT NULL,
    source_city VARCHAR(100) NOT NULL,
    destination_city VARCHAR(100) NOT NULL,
    source_lat DECIMAL(10,8) NOT NULL,
    source_lng DECIMAL(11,8) NOT NULL,
    dest_lat DECIMAL(10,8) NOT NULL,
    dest_lng DECIMAL(11,8) NOT NULL,
    departure_time DATETIME NOT NULL,
    available_seats INT NOT NULL,
    price_per_seat DECIMAL(8,2) NOT NULL,
    vehicle_details JSON,
    preferences JSON,
    driver_preferences TEXT,
    distance_km DECIMAL(8,2) NOT NULL,
    estimated_duration_minutes INT NOT NULL,
    status ENUM('scheduled', 'active', 'completed', 'cancelled') DEFAULT 'scheduled',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (driver_id) REFERENCES users(id)
);

-- CarPool bookings table
CREATE TABLE carpool_bookings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    trip_id INT NOT NULL,
    booking_reference VARCHAR(50) UNIQUE NOT NULL,
    passenger_count INT NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    status ENUM('pending', 'confirmed', 'cancelled', 'completed') DEFAULT 'pending',
    pickup_lat DECIMAL(10,8) NOT NULL,
    pickup_lng DECIMAL(11,8) NOT NULL,
    drop_lat DECIMAL(10,8) NOT NULL,
    drop_lng DECIMAL(11,8) NOT NULL,
    passenger_phone VARCHAR(20) NOT NULL,
    message_to_driver TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (trip_id) REFERENCES carpool_trips(id)
);

-- Bike bookings table
CREATE TABLE bike_bookings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    booking_reference VARCHAR(50) UNIQUE NOT NULL,
    pickup_lat DECIMAL(10,8) NOT NULL,
    pickup_lng DECIMAL(11,8) NOT NULL,
    drop_lat DECIMAL(10,8) NOT NULL,
    drop_lng DECIMAL(11,8) NOT NULL,
    pickup_address TEXT NOT NULL,
    drop_address TEXT NOT NULL,
    ride_type ENUM('regular', 'premium', 'delivery') DEFAULT 'regular',
    estimated_fare DECIMAL(8,2) NOT NULL,
    estimated_distance_km DECIMAL(8,2) NOT NULL,
    estimated_duration_minutes INT NOT NULL,
    status ENUM('pending', 'accepted', 'arrived', 'started', 'completed', 'cancelled') DEFAULT 'pending',
    passenger_phone VARCHAR(20) NOT NULL,
    special_instructions TEXT,
    otp VARCHAR(10),
    scheduled_time DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Driver profiles table
CREATE TABLE driver_profiles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    license_number VARCHAR(50) NOT NULL,
    license_expiry DATE NOT NULL,
    vehicle_registration VARCHAR(50),
    vehicle_model VARCHAR(100),
    vehicle_color VARCHAR(50),
    driving_experience_years INT NOT NULL,
    documents JSON,
    verification_status ENUM('pending', 'verified', 'rejected') DEFAULT 'pending',
    is_available BOOLEAN DEFAULT TRUE,
    current_lat DECIMAL(10,8),
    current_lng DECIMAL(11,8),
    rating DECIMAL(3,2),
    total_rides INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Payments table
CREATE TABLE payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    booking_id VARCHAR(50) NOT NULL,
    transaction_id VARCHAR(100) UNIQUE NOT NULL,
    razorpay_order_id VARCHAR(100),
    razorpay_payment_id VARCHAR(100),
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(10) DEFAULT 'INR',
    status ENUM('pending', 'success', 'failed', 'refunded') DEFAULT 'pending',
    gateway_response JSON,
    refund_id VARCHAR(100),
    refund_amount DECIMAL(10,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    verified_at TIMESTAMP NULL,
    refunded_at TIMESTAMP NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```
