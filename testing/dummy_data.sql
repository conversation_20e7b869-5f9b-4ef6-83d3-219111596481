-- TravelAllinOne Dummy Data for Testing
-- Run this after creating the database schema

USE travelallinone;

-- Insert dummy users
INSERT INTO users (email, phone, first_name, last_name, password_hash, role, date_of_birth, gender, is_verified) VALUES
('<EMAIL>', '+919876543210', '<PERSON>', 'Doe', '$2b$12$LQv3c1yqBwEHxE5W8fCzaOuizdxA3lPzNyx6YBtaPVtMvlahqhdWW', 'passenger', '1990-05-15', 'male', TRUE),
('<EMAIL>', '+919876543211', 'Jane', 'Smith', '$2b$12$LQv3c1yqBwEHxE5W8fCzaOuizdxA3lPzNyx6YBtaPVtMvlahqhdWW', 'driver', '1988-08-22', 'female', TRUE),
('<EMAIL>', '+919876543212', '<PERSON>', '<PERSON>', '$2b$12$LQv3c1yqBwEHxE5W8fCzaOuizdxA3lPzNyx6YBtaPVtMvlahqhdWW', 'driver', '1985-12-10', 'male', TRUE),
('<EMAIL>', '+919876543213', 'RedBus', 'Travels', '$2b$12$LQv3c1yqBwEHxE5W8fCzaOuizdxA3lPzNyx6YBtaPVtMvlahqhdWW', 'operator', '1980-01-01', 'other', TRUE),
('<EMAIL>', '+919876543214', 'Admin', 'User', '$2b$12$LQv3c1yqBwEHxE5W8fCzaOuizdxA3lPzNyx6YBtaPVtMvlahqhdWW', 'admin', '1975-06-30', 'male', TRUE);

-- Insert dummy routes
INSERT INTO routes (source_city, destination_city, distance_km, estimated_duration_minutes) VALUES
('Mumbai', 'Pune', 150.5, 180),
('Delhi', 'Agra', 230.2, 240),
('Bangalore', 'Chennai', 350.8, 420),
('Hyderabad', 'Vijayawada', 275.3, 300),
('Kolkata', 'Bhubaneswar', 440.7, 480),
('Mumbai', 'Nashik', 165.2, 200),
('Delhi', 'Jaipur', 280.5, 320),
('Chennai', 'Pondicherry', 160.3, 180),
('Pune', 'Goa', 460.8, 540),
('Bangalore', 'Mysore', 145.6, 170);

-- Insert dummy buses
INSERT INTO buses (operator_name, bus_number, bus_type, total_seats, amenities, rating, reviews_count, route_id, is_active) VALUES
('RedBus Travels', 'MH12AB1234', 'ac', 40, 'wifi,charging_point,water_bottle,blanket', 4.2, 150, 1, TRUE),
('VRL Travels', 'KA05CD5678', 'sleeper', 36, 'wifi,charging_point,pillow,blanket', 4.5, 200, 3, TRUE),
('KSRTC', 'KA03EF9012', 'luxury', 32, 'wifi,charging_point,water_bottle,snacks,entertainment', 4.7, 300, 3, TRUE),
('Orange Travels', 'AP09GH3456', 'ac', 45, 'wifi,charging_point,water_bottle', 4.1, 120, 4, TRUE),
('SRS Travels', 'TN07IJ7890', 'regular', 50, 'charging_point', 3.8, 80, 8, TRUE),
('Shivneri Travels', 'MH14KL2345', 'ac', 42, 'wifi,charging_point,water_bottle,blanket', 4.3, 180, 6, TRUE),
('Rajasthan Roadways', 'RJ14MN6789', 'ac', 38, 'wifi,charging_point,water_bottle', 4.0, 100, 7, TRUE),
('Paulo Travels', 'GA08OP1234', 'luxury', 28, 'wifi,charging_point,water_bottle,entertainment,recliner', 4.6, 250, 9, TRUE);

-- Insert dummy schedules for next 7 days
INSERT INTO schedules (bus_id, departure_time, arrival_time, available_seats, pricing) VALUES
-- Mumbai to Pune (Route 1)
(1, '2024-01-15 06:00:00', '2024-01-15 09:00:00', 25, '{"regular": 450, "premium": 650}'),
(1, '2024-01-15 14:00:00', '2024-01-15 17:00:00', 30, '{"regular": 450, "premium": 650}'),
(1, '2024-01-15 22:00:00', '2024-01-16 01:00:00', 35, '{"regular": 450, "premium": 650}'),
-- Bangalore to Chennai (Route 3)
(2, '2024-01-15 20:00:00', '2024-01-16 03:00:00', 20, '{"sleeper_lower": 800, "sleeper_upper": 700}'),
(3, '2024-01-15 21:30:00', '2024-01-16 04:30:00', 15, '{"luxury": 1200}'),
-- Hyderabad to Vijayawada (Route 4)
(4, '2024-01-15 07:30:00', '2024-01-15 12:30:00', 28, '{"regular": 420, "premium": 580}'),
-- Chennai to Pondicherry (Route 8)
(5, '2024-01-15 08:00:00', '2024-01-15 11:00:00', 40, '{"regular": 200}'),
-- Mumbai to Nashik (Route 6)
(6, '2024-01-15 09:00:00', '2024-01-15 12:20:00', 32, '{"regular": 380, "premium": 520}'),
-- Delhi to Jaipur (Route 7)
(7, '2024-01-15 10:00:00', '2024-01-15 15:20:00', 25, '{"regular": 480, "premium": 680}'),
-- Pune to Goa (Route 9)
(8, '2024-01-15 19:00:00', '2024-01-16 04:00:00', 18, '{"luxury": 1500}');

-- Insert driver profiles
INSERT INTO driver_profiles (user_id, license_number, license_expiry, vehicle_registration, vehicle_model, vehicle_color, driving_experience_years, verification_status, is_available, current_lat, current_lng, rating, total_rides) VALUES
(2, 'MH1420110012345', '2026-05-15', 'MH12AB1234', 'Maruti Swift', 'White', 8, 'verified', TRUE, 19.0760, 72.8777, 4.3, 150),
(3, 'KA0520150067890', '2025-12-10', 'KA05CD5678', 'Honda City', 'Silver', 12, 'verified', TRUE, 12.9716, 77.5946, 4.6, 200),
(1, 'DL1320180054321', '2027-08-22', 'DL13EF9012', 'Hyundai Creta', 'Blue', 5, 'verified', FALSE, 28.7041, 77.1025, 4.1, 75);

-- Insert carpool trips
INSERT INTO carpool_trips (driver_id, source_city, destination_city, source_lat, source_lng, dest_lat, dest_lng, departure_time, available_seats, price_per_seat, vehicle_details, preferences, driver_preferences, distance_km, estimated_duration_minutes, status) VALUES
(2, 'Mumbai', 'Pune', 19.0760, 72.8777, 18.5204, 73.8567, '2024-01-15 08:00:00', 3, 300, '{"make": "Maruti", "model": "Swift", "color": "White", "number": "MH12AB1234"}', '{"smoking_allowed": false, "pets_allowed": false, "music_allowed": true, "max_two_in_back": false}', 'Prefer non-smoking passengers. Good music taste appreciated!', 150.5, 180, 'scheduled'),
(3, 'Bangalore', 'Chennai', 12.9716, 77.5946, 13.0827, 80.2707, '2024-01-15 20:00:00', 2, 400, '{"make": "Honda", "model": "City", "color": "Silver", "number": "KA05CD5678"}', '{"smoking_allowed": false, "pets_allowed": true, "music_allowed": true, "max_two_in_back": true}', 'Pet-friendly ride. Small dogs welcome!', 350.8, 420, 'scheduled'),
(1, 'Delhi', 'Agra', 28.7041, 77.1025, 27.1767, 78.0081, '2024-01-15 06:00:00', 4, 250, '{"make": "Hyundai", "model": "Creta", "color": "Blue", "number": "DL13EF9012"}', '{"smoking_allowed": false, "pets_allowed": false, "music_allowed": true, "max_two_in_back": false}', 'Early morning trip to see the Taj Mahal!', 230.2, 240, 'scheduled');

-- Insert sample bookings
INSERT INTO bookings (user_id, bus_id, route_id, booking_reference, travel_date, passenger_count, selected_seats, total_amount, status, contact_phone) VALUES
(1, 1, 1, 'TRV12345678', '2024-01-15 06:00:00', 2, 'S01,S02', 900.00, 'confirmed', '+919876543210'),
(2, 3, 3, 'TRV87654321', '2024-01-15 21:30:00', 1, 'S15', 1200.00, 'pending', '+919876543211');

-- Insert carpool bookings
INSERT INTO carpool_bookings (user_id, trip_id, booking_reference, passenger_count, total_amount, status, pickup_lat, pickup_lng, drop_lat, drop_lng, passenger_phone, message_to_driver) VALUES
(1, 1, 'CPL12345678', 2, 600.00, 'confirmed', 19.0760, 72.8777, 18.5204, 73.8567, '+919876543210', 'Will be waiting near the main gate'),
(4, 2, 'CPL87654321', 1, 400.00, 'pending', 12.9716, 77.5946, 13.0827, 80.2707, '+919876543213', 'Traveling with a small dog');

-- Insert bike bookings
INSERT INTO bike_bookings (user_id, booking_reference, pickup_lat, pickup_lng, drop_lat, drop_lng, pickup_address, drop_address, ride_type, estimated_fare, estimated_distance_km, estimated_duration_minutes, status, passenger_phone, otp) VALUES
(1, 'BKE12345678', 19.0760, 72.8777, 19.0896, 72.8656, 'Bandra West, Mumbai', 'Andheri West, Mumbai', 'regular', 88.00, 8.5, 25, 'pending', '+919876543210', '1234'),
(2, 'BKE87654321', 12.9716, 77.5946, 12.9352, 77.6245, 'Koramangala, Bangalore', 'Electronic City, Bangalore', 'premium', 120.00, 12.3, 35, 'accepted', '+919876543211', '5678');

-- Insert sample payments
INSERT INTO payments (user_id, booking_id, transaction_id, razorpay_order_id, amount, currency, status) VALUES
(1, 'TRV12345678', 'TXN123456789012', 'order_123456789', 900.00, 'INR', 'success'),
(1, 'CPL12345678', 'TXN987654321098', 'order_987654321', 600.00, 'INR', 'success'),
(1, 'BKE12345678', 'TXN456789012345', 'order_456789012', 88.00, 'INR', 'pending');

-- Update user verification status and add more realistic data
UPDATE users SET is_verified = TRUE WHERE role IN ('driver', 'operator');

-- Add indexes for better performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_phone ON users(phone);
CREATE INDEX idx_schedules_departure ON schedules(departure_time);
CREATE INDEX idx_bookings_user ON bookings(user_id);
CREATE INDEX idx_bookings_status ON bookings(status);
CREATE INDEX idx_carpool_trips_departure ON carpool_trips(departure_time);
CREATE INDEX idx_carpool_trips_status ON carpool_trips(status);
CREATE INDEX idx_payments_user ON payments(user_id);
CREATE INDEX idx_payments_status ON payments(status);

-- Show summary of inserted data
SELECT 'Users' as table_name, COUNT(*) as count FROM users
UNION ALL
SELECT 'Routes', COUNT(*) FROM routes
UNION ALL
SELECT 'Buses', COUNT(*) FROM buses
UNION ALL
SELECT 'Schedules', COUNT(*) FROM schedules
UNION ALL
SELECT 'Driver Profiles', COUNT(*) FROM driver_profiles
UNION ALL
SELECT 'CarPool Trips', COUNT(*) FROM carpool_trips
UNION ALL
SELECT 'Bookings', COUNT(*) FROM bookings
UNION ALL
SELECT 'CarPool Bookings', COUNT(*) FROM carpool_bookings
UNION ALL
SELECT 'Bike Bookings', COUNT(*) FROM bike_bookings
UNION ALL
SELECT 'Payments', COUNT(*) FROM payments;
