/**
 * TravelAllinOne Frontend Testing Script
 * Automated browser testing using Playwright
 * 
 * Install dependencies:
 * npm install -g playwright
 * npx playwright install
 */

const { chromium } = require('playwright');

const BASE_URL = 'http://localhost:3002';

const TEST_USER = {
  email: '<EMAIL>',
  phone: '+919876543888',
  firstName: 'Frontend',
  lastName: 'Tester',
  password: 'testpassword123'
};

class FrontendTester {
  constructor() {
    this.browser = null;
    this.page = null;
    this.results = [];
  }

  async init() {
    this.browser = await chromium.launch({ headless: false }); // Set to true for headless
    this.page = await this.browser.newPage();
    
    // Set viewport
    await this.page.setViewportSize({ width: 1280, height: 720 });
    
    // Enable console logging
    this.page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log(`❌ Console Error: ${msg.text()}`);
      }
    });
  }

  async logResult(testName, success, error = null) {
    const result = {
      test: testName,
      success,
      timestamp: new Date().toISOString(),
      error: error ? error.message : null
    };
    this.results.push(result);
    
    const status = success ? "✅ PASS" : "❌ FAIL";
    console.log(`${status} ${testName}`);
    if (error) {
      console.log(`   Error: ${error.message}`);
    }
  }

  async testHomePage() {
    console.log('\n🏠 Testing Home Page...');
    
    try {
      await this.page.goto(BASE_URL);
      await this.page.waitForLoadState('networkidle');
      
      // Check if main elements are present
      const title = await this.page.textContent('h1');
      const hasServiceCards = await this.page.locator('[data-testid="service-card"], .grid .card').count() >= 3;
      
      await this.logResult('Home Page Load', title.includes('Travel') && hasServiceCards);
      
      // Test navigation
      await this.page.click('text=Bus Booking');
      await this.page.waitForURL('**/bus');
      await this.logResult('Navigation to Bus Page', true);
      
      // Go back to home
      await this.page.goto(BASE_URL);
      
    } catch (error) {
      await this.logResult('Home Page Load', false, error);
    }
  }

  async testRegistration() {
    console.log('\n📝 Testing User Registration...');
    
    try {
      await this.page.goto(`${BASE_URL}/register`);
      await this.page.waitForLoadState('networkidle');
      
      // Fill registration form
      await this.page.fill('input[name="first_name"]', TEST_USER.firstName);
      await this.page.fill('input[name="last_name"]', TEST_USER.lastName);
      await this.page.fill('input[name="email"]', TEST_USER.email);
      await this.page.fill('input[name="phone"]', TEST_USER.phone);
      await this.page.fill('input[name="password"]', TEST_USER.password);
      await this.page.fill('input[name="confirmPassword"]', TEST_USER.password);
      
      // Check terms checkbox
      await this.page.check('input[name="terms"]');
      
      // Submit form
      await this.page.click('button[type="submit"]');
      
      // Wait for response (either success redirect or error message)
      await this.page.waitForTimeout(2000);
      
      const currentUrl = this.page.url();
      const hasSuccessMessage = await this.page.locator('text=Registration successful').count() > 0;
      const isOnLoginPage = currentUrl.includes('/login');
      
      await this.logResult('User Registration', hasSuccessMessage || isOnLoginPage);
      
    } catch (error) {
      await this.logResult('User Registration', false, error);
    }
  }

  async testLogin() {
    console.log('\n🔐 Testing User Login...');
    
    try {
      await this.page.goto(`${BASE_URL}/login`);
      await this.page.waitForLoadState('networkidle');
      
      // Fill login form
      await this.page.fill('input[name="email"]', TEST_USER.email);
      await this.page.fill('input[name="password"]', TEST_USER.password);
      
      // Submit form
      await this.page.click('button[type="submit"]');
      
      // Wait for response
      await this.page.waitForTimeout(3000);
      
      // Check if redirected to home or if user menu is visible
      const currentUrl = this.page.url();
      const hasUserMenu = await this.page.locator('[data-testid="user-menu"], button:has-text("' + TEST_USER.firstName + '")').count() > 0;
      const isOnHomePage = currentUrl === BASE_URL + '/' || currentUrl === BASE_URL;
      
      await this.logResult('User Login', hasUserMenu || isOnHomePage);
      
    } catch (error) {
      await this.logResult('User Login', false, error);
    }
  }

  async testBusSearch() {
    console.log('\n🚌 Testing Bus Search...');
    
    try {
      await this.page.goto(`${BASE_URL}/bus`);
      await this.page.waitForLoadState('networkidle');
      
      // Fill search form
      await this.page.fill('input[placeholder*="Departure"]', 'Mumbai');
      await this.page.fill('input[placeholder*="Destination"]', 'Pune');
      
      // Set travel date (tomorrow)
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      const dateString = tomorrow.toISOString().split('T')[0];
      await this.page.fill('input[type="date"]', dateString);
      
      // Click search button
      await this.page.click('button:has-text("Search")');
      
      // Wait for results or no results message
      await this.page.waitForTimeout(3000);
      
      const hasResults = await this.page.locator('.bus-card, [data-testid="bus-result"]').count() > 0;
      const hasNoResultsMessage = await this.page.locator('text=No buses found').count() > 0;
      const hasPopularRoutes = await this.page.locator('text=Popular Routes').count() > 0;
      
      await this.logResult('Bus Search', hasResults || hasNoResultsMessage || hasPopularRoutes);
      
    } catch (error) {
      await this.logResult('Bus Search', false, error);
    }
  }

  async testCarPoolSearch() {
    console.log('\n🚗 Testing CarPool Search...');
    
    try {
      await this.page.goto(`${BASE_URL}/carpool`);
      await this.page.waitForLoadState('networkidle');
      
      // Make sure we're on the search tab
      await this.page.click('text=Find a Ride');
      
      // Fill search form
      await this.page.fill('input[placeholder*="Departure"]', 'Mumbai');
      await this.page.fill('input[placeholder*="Destination"]', 'Pune');
      
      // Set travel date
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      const dateString = tomorrow.toISOString().split('T')[0];
      await this.page.fill('input[type="date"]', dateString);
      
      // Click search button
      await this.page.click('button:has-text("Search")');
      
      // Wait for results
      await this.page.waitForTimeout(3000);
      
      const hasResults = await this.page.locator('.trip-card, [data-testid="trip-result"]').count() > 0;
      const hasNoResultsMessage = await this.page.locator('text=No rides found').count() > 0;
      const hasPopularRoutes = await this.page.locator('text=Popular Routes').count() > 0;
      
      await this.logResult('CarPool Search', hasResults || hasNoResultsMessage || hasPopularRoutes);
      
    } catch (error) {
      await this.logResult('CarPool Search', false, error);
    }
  }

  async testBikeEstimate() {
    console.log('\n🏍️ Testing Bike Ride Estimate...');
    
    try {
      await this.page.goto(`${BASE_URL}/bike`);
      await this.page.waitForLoadState('networkidle');
      
      // Fill pickup and drop locations
      await this.page.fill('input[placeholder*="pickup"]', 'Bandra West, Mumbai');
      await this.page.fill('input[placeholder*="drop"]', 'Andheri West, Mumbai');
      await this.page.fill('input[placeholder*="phone"]', '+919876543777');
      
      // Click get estimate button
      await this.page.click('button:has-text("Get Estimate")');
      
      // Wait for estimate
      await this.page.waitForTimeout(3000);
      
      const hasEstimate = await this.page.locator('text=Ride Estimate, text=Total Fare').count() > 0;
      const hasErrorMessage = await this.page.locator('text=Failed to get estimate').count() > 0;
      
      // Either estimate should work or show appropriate error
      await this.logResult('Bike Ride Estimate', hasEstimate || hasErrorMessage);
      
    } catch (error) {
      await this.logResult('Bike Ride Estimate', false, error);
    }
  }

  async testChatbot() {
    console.log('\n🤖 Testing Chatbot...');
    
    try {
      await this.page.goto(BASE_URL);
      await this.page.waitForLoadState('networkidle');
      
      // Look for chatbot button
      const chatbotButton = this.page.locator('button:has([data-testid="chatbot"]), button:has-text("💬"), [data-testid="chatbot-trigger"]').first();
      
      if (await chatbotButton.count() > 0) {
        await chatbotButton.click();
        await this.page.waitForTimeout(1000);
        
        // Check if chatbot opened
        const chatbotOpen = await this.page.locator('[data-testid="chatbot-window"], .chatbot-container').count() > 0;
        await this.logResult('Chatbot Open', chatbotOpen);
        
        if (chatbotOpen) {
          // Try to send a message
          const messageInput = this.page.locator('input[placeholder*="message"], textarea[placeholder*="message"]').first();
          if (await messageInput.count() > 0) {
            await messageInput.fill('Hello, I need help with booking');
            await this.page.click('button[type="submit"], button:has-text("Send")');
            await this.page.waitForTimeout(2000);
            
            const hasResponse = await this.page.locator('.bot-message, [data-testid="bot-response"]').count() > 0;
            await this.logResult('Chatbot Response', hasResponse);
          }
        }
      } else {
        await this.logResult('Chatbot Open', false, new Error('Chatbot button not found'));
      }
      
    } catch (error) {
      await this.logResult('Chatbot', false, error);
    }
  }

  async testResponsiveDesign() {
    console.log('\n📱 Testing Responsive Design...');
    
    try {
      // Test mobile viewport
      await this.page.setViewportSize({ width: 375, height: 667 });
      await this.page.goto(BASE_URL);
      await this.page.waitForLoadState('networkidle');
      
      // Check if mobile menu exists
      const mobileMenuButton = await this.page.locator('button:has-text("☰"), [data-testid="mobile-menu"], button:has([data-testid="menu-icon"])').count() > 0;
      
      // Test tablet viewport
      await this.page.setViewportSize({ width: 768, height: 1024 });
      await this.page.reload();
      await this.page.waitForLoadState('networkidle');
      
      // Test desktop viewport
      await this.page.setViewportSize({ width: 1280, height: 720 });
      await this.page.reload();
      await this.page.waitForLoadState('networkidle');
      
      const desktopNavigation = await this.page.locator('nav a, header a').count() >= 3;
      
      await this.logResult('Responsive Design', mobileMenuButton && desktopNavigation);
      
    } catch (error) {
      await this.logResult('Responsive Design', false, error);
    }
  }

  async runAllTests() {
    console.log('🚀 Starting TravelAllinOne Frontend Tests...');
    console.log('=' * 50);
    
    const startTime = Date.now();
    
    await this.init();
    
    try {
      await this.testHomePage();
      await this.testRegistration();
      await this.testLogin();
      await this.testBusSearch();
      await this.testCarPoolSearch();
      await this.testBikeEstimate();
      await this.testChatbot();
      await this.testResponsiveDesign();
    } finally {
      await this.browser.close();
    }
    
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    
    // Summary
    const totalTests = this.results.length;
    const passedTests = this.results.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;
    
    console.log('\n' + '='.repeat(50));
    console.log('📊 FRONTEND TEST SUMMARY');
    console.log('='.repeat(50));
    console.log(`Total Tests: ${totalTests}`);
    console.log(`Passed: ${passedTests} ✅`);
    console.log(`Failed: ${failedTests} ❌`);
    console.log(`Success Rate: ${((passedTests/totalTests)*100).toFixed(1)}%`);
    console.log(`Duration: ${duration.toFixed(2)} seconds`);
    
    if (failedTests > 0) {
      console.log('\n❌ Failed Tests:');
      this.results.filter(r => !r.success).forEach(result => {
        console.log(`  - ${result.test}: ${result.error}`);
      });
    }
    
    // Save results
    const fs = require('fs');
    fs.writeFileSync('frontend_test_results.json', JSON.stringify(this.results, null, 2));
    console.log('\n📄 Detailed results saved to frontend_test_results.json');
    
    return passedTests === totalTests;
  }
}

// Run tests if called directly
if (require.main === module) {
  (async () => {
    const tester = new FrontendTester();
    const success = await tester.runAllTests();
    
    if (success) {
      console.log('\n🎉 All frontend tests passed! Your TravelAllinOne UI is working correctly.');
    } else {
      console.log('\n⚠️  Some frontend tests failed. Check the logs above for details.');
      process.exit(1);
    }
  })();
}

module.exports = FrontendTester;
