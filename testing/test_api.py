#!/usr/bin/env python3
"""
TravelAllinOne API Testing Script
Comprehensive testing of all microservices with dummy data
"""

import requests
import json
import time
from datetime import datetime, timedelta

# Configuration
BASE_URLS = {
    'auth': 'http://localhost:8001',
    'bus': 'http://localhost:8002',
    'carpool': 'http://localhost:8003',
    'bike': 'http://localhost:8004',
    'payment': 'http://localhost:8005',
    'chatbot': 'http://localhost:8006'
}

# Test data
TEST_USER = {
    "email": "<EMAIL>",
    "phone": "+919876543999",
    "first_name": "Test",
    "last_name": "User",
    "password": "testpassword123",
    "role": "passenger"
}

LOGIN_DATA = {
    "email": "<EMAIL>",
    "password": "testpassword123"
}

BUS_SEARCH = {
    "source_city": "Mumbai",
    "destination_city": "Pune",
    "travel_date": (datetime.now() + timedelta(days=1)).isoformat(),
    "passenger_count": 2,
    "bus_type": "ac"
}

CARPOOL_SEARCH = {
    "source_city": "Mumbai",
    "destination_city": "Pune",
    "travel_date": (datetime.now() + timedelta(days=1)).isoformat(),
    "passenger_count": 2,
    "max_price_per_seat": 400
}

BIKE_ESTIMATE = {
    "pickup_location": {
        "latitude": 19.0760,
        "longitude": 72.8777,
        "address": "Bandra West",
        "city": "Mumbai",
        "state": "Maharashtra",
        "pincode": "400050"
    },
    "drop_location": {
        "latitude": 19.0896,
        "longitude": 72.8656,
        "address": "Andheri West",
        "city": "Mumbai",
        "state": "Maharashtra",
        "pincode": "400058"
    },
    "ride_type": "regular",
    "passenger_phone": "+919876543999"
}

CHAT_MESSAGE = {
    "message": "How can I book a bus ticket from Mumbai to Pune?",
    "session_id": None,
    "context": {
        "current_page": "bus_search"
    }
}

class APITester:
    def __init__(self):
        self.auth_token = None
        self.session = requests.Session()
        self.results = []

    def log_result(self, test_name, success, response_data=None, error=None):
        """Log test result"""
        result = {
            'test': test_name,
            'success': success,
            'timestamp': datetime.now().isoformat(),
            'response': response_data,
            'error': str(error) if error else None
        }
        self.results.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if error:
            print(f"   Error: {error}")
        if response_data and isinstance(response_data, dict):
            print(f"   Response: {response_data.get('message', 'No message')}")

    def test_service_health(self):
        """Test all service health endpoints"""
        print("\n🏥 Testing Service Health...")
        
        for service, url in BASE_URLS.items():
            try:
                response = self.session.get(f"{url}/health", timeout=5)
                success = response.status_code == 200
                self.log_result(f"{service.title()} Service Health", success, response.json() if success else None)
            except Exception as e:
                self.log_result(f"{service.title()} Service Health", False, error=e)

    def test_auth_service(self):
        """Test authentication service"""
        print("\n🔐 Testing Authentication Service...")
        
        # Test registration
        try:
            response = self.session.post(f"{BASE_URLS['auth']}/register", json=TEST_USER)
            success = response.status_code in [200, 201, 409]  # 409 if user already exists
            self.log_result("User Registration", success, response.json() if success else None)
        except Exception as e:
            self.log_result("User Registration", False, error=e)

        # Test login
        try:
            response = self.session.post(f"{BASE_URLS['auth']}/login", json=LOGIN_DATA)
            success = response.status_code == 200
            if success:
                data = response.json()
                if data.get('success') and 'data' in data and 'access_token' in data['data']:
                    self.auth_token = data['data']['access_token']
                    self.session.headers.update({'Authorization': f'Bearer {self.auth_token}'})
            self.log_result("User Login", success, response.json() if success else None)
        except Exception as e:
            self.log_result("User Login", False, error=e)

        # Test profile retrieval
        if self.auth_token:
            try:
                response = self.session.get(f"{BASE_URLS['auth']}/profile")
                success = response.status_code == 200
                self.log_result("Get User Profile", success, response.json() if success else None)
            except Exception as e:
                self.log_result("Get User Profile", False, error=e)

    def test_bus_service(self):
        """Test bus booking service"""
        print("\n🚌 Testing Bus Service...")
        
        # Test bus search
        try:
            response = self.session.post(f"{BASE_URLS['bus']}/search", json=BUS_SEARCH)
            success = response.status_code == 200
            self.log_result("Bus Search", success, response.json() if success else None)
        except Exception as e:
            self.log_result("Bus Search", False, error=e)

        # Test seat layout (with dummy bus ID)
        try:
            travel_date = (datetime.now() + timedelta(days=1)).isoformat()
            response = self.session.get(f"{BASE_URLS['bus']}/seat-layout/bus123?travel_date={travel_date}")
            success = response.status_code in [200, 404]  # 404 is acceptable for dummy data
            self.log_result("Bus Seat Layout", success, response.json() if success else None)
        except Exception as e:
            self.log_result("Bus Seat Layout", False, error=e)

    def test_carpool_service(self):
        """Test carpool service"""
        print("\n🚗 Testing CarPool Service...")
        
        # Test carpool search
        try:
            response = self.session.post(f"{BASE_URLS['carpool']}/search", json=CARPOOL_SEARCH)
            success = response.status_code == 200
            self.log_result("CarPool Search", success, response.json() if success else None)
        except Exception as e:
            self.log_result("CarPool Search", False, error=e)

    def test_bike_service(self):
        """Test bike ride service"""
        print("\n🏍️ Testing Bike Service...")
        
        # Test ride estimate
        try:
            response = self.session.post(f"{BASE_URLS['bike']}/estimate", json=BIKE_ESTIMATE)
            success = response.status_code == 200
            self.log_result("Bike Ride Estimate", success, response.json() if success else None)
        except Exception as e:
            self.log_result("Bike Ride Estimate", False, error=e)

        # Test nearby drivers
        try:
            response = self.session.get(f"{BASE_URLS['bike']}/nearby-drivers?lat=19.0760&lng=72.8777&radius=5")
            success = response.status_code == 200
            self.log_result("Nearby Drivers", success, response.json() if success else None)
        except Exception as e:
            self.log_result("Nearby Drivers", False, error=e)

    def test_payment_service(self):
        """Test payment service"""
        print("\n💳 Testing Payment Service...")
        
        if not self.auth_token:
            self.log_result("Payment Service", False, error="No auth token available")
            return

        # Test payment initiation
        payment_data = {
            "booking_id": "test_booking_123",
            "amount": 450.0,
            "currency": "INR",
            "description": "Test bus ticket payment",
            "customer_email": "<EMAIL>",
            "customer_phone": "+919876543999"
        }
        
        try:
            response = self.session.post(f"{BASE_URLS['payment']}/initiate", json=payment_data)
            success = response.status_code == 200
            self.log_result("Payment Initiation", success, response.json() if success else None)
        except Exception as e:
            self.log_result("Payment Initiation", False, error=e)

        # Test payment history
        try:
            response = self.session.get(f"{BASE_URLS['payment']}/history?limit=10")
            success = response.status_code == 200
            self.log_result("Payment History", success, response.json() if success else None)
        except Exception as e:
            self.log_result("Payment History", False, error=e)

    def test_chatbot_service(self):
        """Test chatbot service"""
        print("\n🤖 Testing Chatbot Service...")
        
        if not self.auth_token:
            self.log_result("Chatbot Service", False, error="No auth token available")
            return

        # Test chat message
        try:
            response = self.session.post(f"{BASE_URLS['chatbot']}/chat", json=CHAT_MESSAGE)
            success = response.status_code == 200
            self.log_result("Chat Message", success, response.json() if success else None)
        except Exception as e:
            self.log_result("Chat Message", False, error=e)

    def run_all_tests(self):
        """Run all tests"""
        print("🚀 Starting TravelAllinOne API Tests...")
        print("=" * 50)
        
        start_time = time.time()
        
        self.test_service_health()
        self.test_auth_service()
        self.test_bus_service()
        self.test_carpool_service()
        self.test_bike_service()
        self.test_payment_service()
        self.test_chatbot_service()
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Summary
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results if r['success'])
        failed_tests = total_tests - passed_tests
        
        print("\n" + "=" * 50)
        print("📊 TEST SUMMARY")
        print("=" * 50)
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ✅")
        print(f"Failed: {failed_tests} ❌")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        print(f"Duration: {duration:.2f} seconds")
        
        if failed_tests > 0:
            print("\n❌ Failed Tests:")
            for result in self.results:
                if not result['success']:
                    print(f"  - {result['test']}: {result['error']}")
        
        # Save results to file
        with open('test_results.json', 'w') as f:
            json.dump(self.results, f, indent=2, default=str)
        
        print(f"\n📄 Detailed results saved to test_results.json")
        
        return passed_tests == total_tests

if __name__ == "__main__":
    tester = APITester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎉 All tests passed! Your TravelAllinOne API is working correctly.")
    else:
        print("\n⚠️  Some tests failed. Check the logs above for details.")
        exit(1)
