#!/bin/bash

# TravelAllinOne Environment Setup Script
# This script sets up the complete testing environment

set -e  # Exit on any error

echo "🚀 TravelAllinOne Environment Setup"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
echo "🔍 Checking prerequisites..."

# Check Python
if command_exists python3; then
    PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
    print_status "Python $PYTHON_VERSION found"
else
    print_error "Python 3 is required but not installed"
    exit 1
fi

# Check Node.js
if command_exists node; then
    NODE_VERSION=$(node --version)
    print_status "Node.js $NODE_VERSION found"
else
    print_error "Node.js is required but not installed"
    exit 1
fi

# Check npm
if command_exists npm; then
    NPM_VERSION=$(npm --version)
    print_status "npm $NPM_VERSION found"
else
    print_error "npm is required but not installed"
    exit 1
fi

# Check MySQL
if command_exists mysql; then
    print_status "MySQL found"
else
    print_warning "MySQL not found. Please install MySQL 8.0+"
fi

# Check Redis
if command_exists redis-server; then
    print_status "Redis found"
else
    print_warning "Redis not found. Please install Redis 6.0+"
fi

# Check MongoDB
if command_exists mongod; then
    print_status "MongoDB found"
else
    print_warning "MongoDB not found. Please install MongoDB 5.0+"
fi

echo ""
echo "📦 Installing dependencies..."

# Install Python dependencies for all services
echo "Installing Python dependencies..."

SERVICES=("auth-service" "bus-service" "carpool-service" "bike-service" "payment-service" "chatbot-service")

for service in "${SERVICES[@]}"; do
    if [ -d "backend/$service" ]; then
        print_info "Installing dependencies for $service..."
        cd "backend/$service"
        if [ -f "requirements.txt" ]; then
            pip3 install -r requirements.txt --quiet
            print_status "$service dependencies installed"
        else
            print_warning "requirements.txt not found for $service"
        fi
        cd - > /dev/null
    else
        print_warning "Directory backend/$service not found"
    fi
done

# Install shared dependencies
if [ -d "backend/shared" ]; then
    print_info "Installing shared dependencies..."
    cd "backend/shared"
    if [ -f "requirements.txt" ]; then
        pip3 install -r requirements.txt --quiet
        print_status "Shared dependencies installed"
    fi
    cd - > /dev/null
fi

# Install frontend dependencies
if [ -d "frontend" ]; then
    print_info "Installing frontend dependencies..."
    cd frontend
    npm install --silent
    print_status "Frontend dependencies installed"
    cd - > /dev/null
else
    print_error "Frontend directory not found"
fi

# Install testing dependencies
print_info "Installing testing dependencies..."
pip3 install requests --quiet

if command_exists npm; then
    npm install -g playwright --silent
    npx playwright install --quiet
    print_status "Testing dependencies installed"
fi

echo ""
echo "🗄️ Setting up database..."

# Create environment file template
cat > .env.example << EOF
# Database Configuration
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=travelallinone
MYSQL_PASSWORD=password123
MYSQL_DATABASE=travelallinone

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# MongoDB Configuration
MONGODB_HOST=localhost
MONGODB_PORT=27017
MONGODB_DATABASE=travelallinone

# JWT Configuration
JWT_SECRET_KEY=your-super-secret-jwt-key-change-in-production
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# Payment Gateway (Razorpay)
RAZORPAY_KEY_ID=your_razorpay_key_id
RAZORPAY_KEY_SECRET=your_razorpay_key_secret

# AI Services
OPENAI_API_KEY=your_openai_api_key
PINECONE_API_KEY=your_pinecone_api_key
PINECONE_ENVIRONMENT=your_pinecone_environment

# Frontend Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000
EOF

print_status "Environment template created (.env.example)"

# Database setup instructions
echo ""
print_info "Database setup required:"
echo "1. Start MySQL service"
echo "2. Create database and user:"
echo "   mysql -u root -p"
echo "   CREATE DATABASE travelallinone;"
echo "   CREATE USER 'travelallinone'@'localhost' IDENTIFIED BY 'password123';"
echo "   GRANT ALL PRIVILEGES ON travelallinone.* TO 'travelallinone'@'localhost';"
echo "   FLUSH PRIVILEGES;"
echo "   EXIT;"
echo ""
echo "3. Load dummy data:"
echo "   mysql -u travelallinone -p travelallinone < testing/dummy_data.sql"

echo ""
echo "🚀 Creating startup script..."

# Create startup script
cat > start_services.sh << 'EOF'
#!/bin/bash

# TravelAllinOne Services Startup Script

echo "🚀 Starting TravelAllinOne Services..."

# Function to start service in background
start_service() {
    local service_name=$1
    local port=$2
    local directory=$3
    
    echo "Starting $service_name on port $port..."
    cd "$directory"
    uvicorn main:app --host 0.0.0.0 --port $port --reload > "../logs/${service_name}.log" 2>&1 &
    echo $! > "../logs/${service_name}.pid"
    cd - > /dev/null
}

# Create logs directory
mkdir -p logs

# Start backend services
start_service "auth-service" 8001 "backend/auth-service"
start_service "bus-service" 8002 "backend/bus-service"
start_service "carpool-service" 8003 "backend/carpool-service"
start_service "bike-service" 8004 "backend/bike-service"
start_service "payment-service" 8005 "backend/payment-service"
start_service "chatbot-service" 8006 "backend/chatbot-service"

# Start frontend
echo "Starting frontend on port 3002..."
cd frontend
npm run dev > ../logs/frontend.log 2>&1 &
echo $! > ../logs/frontend.pid
cd - > /dev/null

echo ""
echo "✅ All services started!"
echo ""
echo "🌐 Service URLs:"
echo "Frontend:        http://localhost:3002"
echo "Auth Service:    http://localhost:8001"
echo "Bus Service:     http://localhost:8002"
echo "CarPool Service: http://localhost:8003"
echo "Bike Service:    http://localhost:8004"
echo "Payment Service: http://localhost:8005"
echo "Chatbot Service: http://localhost:8006"
echo ""
echo "📊 API Documentation:"
echo "Auth API:        http://localhost:8001/docs"
echo "Bus API:         http://localhost:8002/docs"
echo "CarPool API:     http://localhost:8003/docs"
echo "Bike API:        http://localhost:8004/docs"
echo "Payment API:     http://localhost:8005/docs"
echo "Chatbot API:     http://localhost:8006/docs"
echo ""
echo "📝 Logs are available in the 'logs' directory"
echo "🛑 To stop services, run: ./stop_services.sh"
EOF

chmod +x start_services.sh
print_status "Startup script created (start_services.sh)"

# Create stop script
cat > stop_services.sh << 'EOF'
#!/bin/bash

echo "🛑 Stopping TravelAllinOne Services..."

# Function to stop service
stop_service() {
    local service_name=$1
    local pid_file="logs/${service_name}.pid"
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            kill "$pid"
            echo "✅ Stopped $service_name (PID: $pid)"
        else
            echo "⚠️  $service_name was not running"
        fi
        rm -f "$pid_file"
    else
        echo "⚠️  No PID file found for $service_name"
    fi
}

# Stop all services
stop_service "auth-service"
stop_service "bus-service"
stop_service "carpool-service"
stop_service "bike-service"
stop_service "payment-service"
stop_service "chatbot-service"
stop_service "frontend"

echo "✅ All services stopped!"
EOF

chmod +x stop_services.sh
print_status "Stop script created (stop_services.sh)"

# Create test script
cat > run_tests.sh << 'EOF'
#!/bin/bash

echo "🧪 Running TravelAllinOne Tests..."

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 10

# Run API tests
echo "🔧 Running API tests..."
cd testing
python3 test_api.py

# Run frontend tests (if Playwright is available)
if command -v npx >/dev/null 2>&1; then
    echo "🌐 Running frontend tests..."
    node test_frontend.js
else
    echo "⚠️  Skipping frontend tests (Playwright not available)"
fi

cd ..
echo "✅ Tests completed!"
EOF

chmod +x run_tests.sh
print_status "Test script created (run_tests.sh)"

echo ""
echo "🎉 Setup completed successfully!"
echo ""
print_info "Next steps:"
echo "1. Set up your databases (MySQL, Redis, MongoDB)"
echo "2. Load dummy data: mysql -u travelallinone -p travelallinone < testing/dummy_data.sql"
echo "3. Copy .env.example to .env and update with your configuration"
echo "4. Start services: ./start_services.sh"
echo "5. Run tests: ./run_tests.sh"
echo "6. Open frontend: http://localhost:3002"
echo ""
print_status "Happy testing! 🚀"
