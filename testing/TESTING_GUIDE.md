# 🧪 TravelAllinOne Testing Guide

Complete testing instructions with commands and dummy data for the TravelAllinOne application.

## 📋 Prerequisites

### System Requirements
- Python 3.8+
- Node.js 18+
- MySQL 8.0+
- Redis 6.0+
- MongoDB 5.0+

### Install Dependencies

```bash
# Backend dependencies
cd backend/shared && pip install -r requirements.txt
cd ../auth-service && pip install -r requirements.txt
cd ../bus-service && pip install -r requirements.txt
cd ../carpool-service && pip install -r requirements.txt
cd ../bike-service && pip install -r requirements.txt
cd ../payment-service && pip install -r requirements.txt
cd ../chatbot-service && pip install -r requirements.txt

# Frontend dependencies
cd ../../frontend && npm install

# Testing dependencies
pip install requests
npm install -g playwright
npx playwright install
```

## 🗄️ Database Setup

### 1. MySQL Setup
```bash
# Start MySQL service
sudo systemctl start mysql  # Linux
brew services start mysql   # macOS

# Create database and user
mysql -u root -p
```

```sql
CREATE DATABASE travelallinone;
CREATE USER 'travelallinone'@'localhost' IDENTIFIED BY 'password123';
GRANT ALL PRIVILEGES ON travelallinone.* TO 'travelallinone'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 2. Load Database Schema and Dummy Data
```bash
# Load schema and dummy data
mysql -u travelallinone -p travelallinone < testing/dummy_data.sql
```

### 3. Start Redis and MongoDB
```bash
# Start Redis
redis-server

# Start MongoDB
mongod --dbpath /path/to/your/mongodb/data
```

## 🚀 Start All Services

### Method 1: Manual Start (Recommended for Testing)

```bash
# Terminal 1: Frontend
cd frontend
npm run dev
# ✅ Frontend: http://localhost:3002

# Terminal 2: Auth Service
cd backend/auth-service
uvicorn main:app --host 0.0.0.0 --port 8001 --reload
# ✅ Auth API: http://localhost:8001

# Terminal 3: Bus Service
cd backend/bus-service
uvicorn main:app --host 0.0.0.0 --port 8002 --reload
# ✅ Bus API: http://localhost:8002

# Terminal 4: CarPool Service
cd backend/carpool-service
uvicorn main:app --host 0.0.0.0 --port 8003 --reload
# ✅ CarPool API: http://localhost:8003

# Terminal 5: Bike Service
cd backend/bike-service
uvicorn main:app --host 0.0.0.0 --port 8004 --reload
# ✅ Bike API: http://localhost:8004

# Terminal 6: Payment Service
cd backend/payment-service
uvicorn main:app --host 0.0.0.0 --port 8005 --reload
# ✅ Payment API: http://localhost:8005

# Terminal 7: Chatbot Service
cd backend/chatbot-service
uvicorn main:app --host 0.0.0.0 --port 8006 --reload
# ✅ Chatbot API: http://localhost:8006
```

### Method 2: Using Docker (Optional)
```bash
# Build and start all services
docker-compose up --build
```

## 🧪 Testing Methods

### 1. Quick Health Check
```bash
# Check all services are running
curl http://localhost:8001/health  # Auth Service
curl http://localhost:8002/health  # Bus Service
curl http://localhost:8003/health  # CarPool Service
curl http://localhost:8004/health  # Bike Service
curl http://localhost:8005/health  # Payment Service
curl http://localhost:8006/health  # Chatbot Service

# Check frontend
curl http://localhost:3002
```

### 2. Automated API Testing
```bash
# Run comprehensive API tests
cd testing
python test_api.py
```

### 3. Frontend Testing
```bash
# Run automated frontend tests
cd testing
node test_frontend.js
```

### 4. Manual Browser Testing
Open http://localhost:3002 in your browser and test:

## 🎯 Test Scenarios

### Authentication Flow
1. **Register New User**
   - Go to: http://localhost:3002/register
   - Use dummy data:
     ```
     Email: <EMAIL>
     Phone: +919876543210
     Name: Test User
     Password: password123
     ```

2. **Login**
   - Go to: http://localhost:3002/login
   - Use registered credentials

3. **Profile Management**
   - Go to: http://localhost:3002/profile
   - Update profile information

### Bus Booking Flow
1. **Search Buses**
   - Go to: http://localhost:3002/bus
   - Search: Mumbai → Pune, Tomorrow, 2 passengers
   - Expected: List of available buses or "No buses found"

2. **View Bus Details**
   - Click on any bus result
   - Check seat layout and amenities

### CarPool Flow
1. **Search Rides**
   - Go to: http://localhost:3002/carpool
   - Search: Mumbai → Pune, Tomorrow, 2 passengers
   - Expected: Available carpool trips

2. **Create Trip**
   - Switch to "Offer a Ride" tab
   - Fill trip details

### Bike Ride Flow
1. **Get Estimate**
   - Go to: http://localhost:3002/bike
   - Enter: Bandra West → Andheri West
   - Phone: +919876543210
   - Expected: Fare estimate and nearby drivers

2. **Book Ride**
   - After getting estimate, click "Book Ride"

### Chatbot Testing
1. **Open Chatbot**
   - Click chatbot icon (bottom right)
   - Send message: "How to book a bus ticket?"
   - Expected: AI response with suggestions

## 📊 Test Data

### Pre-loaded Users (Password: `password123`)
```
<EMAIL> - Passenger
<EMAIL> - Driver  
<EMAIL> - Driver
<EMAIL> - Operator
<EMAIL> - Admin
```

### Sample Routes
```
Mumbai → Pune (150 km, 3h)
Delhi → Agra (230 km, 4h)
Bangalore → Chennai (350 km, 7h)
Hyderabad → Vijayawada (275 km, 5h)
```

### Sample Buses
```
RedBus Travels - MH12AB1234 (AC, 40 seats)
VRL Travels - KA05CD5678 (Sleeper, 36 seats)
KSRTC - KA03EF9012 (Luxury, 32 seats)
```

## 🔧 API Testing with cURL

### Authentication
```bash
# Register
curl -X POST http://localhost:8001/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "phone": "+919876543299",
    "first_name": "New",
    "last_name": "User",
    "password": "password123",
    "role": "passenger"
  }'

# Login
curl -X POST http://localhost:8001/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

### Bus Search
```bash
curl -X POST http://localhost:8002/search \
  -H "Content-Type: application/json" \
  -d '{
    "source_city": "Mumbai",
    "destination_city": "Pune",
    "travel_date": "2024-01-15T00:00:00Z",
    "passenger_count": 2
  }'
```

### CarPool Search
```bash
curl -X POST http://localhost:8003/search \
  -H "Content-Type: application/json" \
  -d '{
    "source_city": "Mumbai",
    "destination_city": "Pune",
    "travel_date": "2024-01-15T00:00:00Z",
    "passenger_count": 2
  }'
```

### Bike Estimate
```bash
curl -X POST http://localhost:8004/estimate \
  -H "Content-Type: application/json" \
  -d '{
    "pickup_location": {
      "latitude": 19.0760,
      "longitude": 72.8777,
      "address": "Bandra West",
      "city": "Mumbai",
      "state": "Maharashtra",
      "pincode": "400050"
    },
    "drop_location": {
      "latitude": 19.0896,
      "longitude": 72.8656,
      "address": "Andheri West",
      "city": "Mumbai",
      "state": "Maharashtra",
      "pincode": "400058"
    },
    "ride_type": "regular",
    "passenger_phone": "+919876543210"
  }'
```

## 🐛 Troubleshooting

### Common Issues

1. **Port Already in Use**
   ```bash
   # Kill process on port
   lsof -ti:8001 | xargs kill -9
   ```

2. **Database Connection Error**
   ```bash
   # Check MySQL status
   systemctl status mysql
   
   # Check connection
   mysql -u travelallinone -p -e "SELECT 1"
   ```

3. **Frontend Build Issues**
   ```bash
   # Clear cache and reinstall
   cd frontend
   rm -rf node_modules package-lock.json
   npm install
   ```

4. **Python Dependencies**
   ```bash
   # Create virtual environment
   python -m venv venv
   source venv/bin/activate  # Linux/Mac
   # or
   venv\Scripts\activate     # Windows
   
   pip install -r requirements.txt
   ```

### Service-Specific Debugging

1. **Check Service Logs**
   - Each service shows logs in its terminal
   - Look for error messages and stack traces

2. **API Documentation**
   - Auth Service: http://localhost:8001/docs
   - Bus Service: http://localhost:8002/docs
   - CarPool Service: http://localhost:8003/docs
   - Bike Service: http://localhost:8004/docs
   - Payment Service: http://localhost:8005/docs
   - Chatbot Service: http://localhost:8006/docs

3. **Database Queries**
   ```sql
   -- Check data
   USE travelallinone;
   SELECT COUNT(*) FROM users;
   SELECT COUNT(*) FROM buses;
   SELECT COUNT(*) FROM routes;
   ```

## ✅ Success Criteria

### API Tests Should Pass:
- ✅ All health checks return 200
- ✅ User registration and login work
- ✅ Bus search returns results or appropriate message
- ✅ CarPool search functions correctly
- ✅ Bike estimate calculation works
- ✅ Payment initiation succeeds
- ✅ Chatbot responds to messages

### Frontend Tests Should Pass:
- ✅ Home page loads with all sections
- ✅ Navigation works across all pages
- ✅ Forms submit and validate correctly
- ✅ Search functionality works
- ✅ Responsive design adapts to different screen sizes
- ✅ Chatbot opens and responds

## 📈 Performance Testing

### Load Testing (Optional)
```bash
# Install Apache Bench
sudo apt-get install apache2-utils

# Test API endpoints
ab -n 100 -c 10 http://localhost:8001/health
ab -n 50 -c 5 -p bus_search.json -T application/json http://localhost:8002/search
```

## 🎉 Expected Results

After running all tests, you should see:
- ✅ All services healthy and responsive
- ✅ Database populated with dummy data
- ✅ Frontend fully functional and responsive
- ✅ API endpoints returning appropriate responses
- ✅ User flows working end-to-end

The TravelAllinOne application should be fully functional and ready for production deployment!
